<template>
  <div class="road-interflow-edit" v-loading="loading">
    <el-row :gutter="15">
      <el-form
        ref="elForm"
        :model="queryParams"
        size="mini"
        label-width="100px"
      >
        <el-col :span="8">
          <el-form-item label="附件类型" prop="annexType">
            <DictSelect
                v-model="queryParams.annexType"
                :type="'contract_annex_type'"
                :placeholder="'附件类型'"
                clearable
            ></DictSelect>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="文件类型" prop="fileType">
            <DictSelect
                v-model="queryParams.fileType"
                :type="'contract_file_type'"
                :placeholder="'文件类型'"
                clearable
            ></DictSelect>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-button
            type="success"
            icon="el-icon-search"
            size="mini"
            @click="getList"
          >搜索
          </el-button
          >
          <el-button
            type="primary"
            icon="el-icon-plus"
            size="mini"
            @click="openFlag = true"
          >新增
          </el-button
          >
        </el-col>
        <el-col :span="24">
          <el-table v-adjust-table
            size="mini"
            style="width: 100%"
            v-loading="loading"
            border
            :data="tableData"
            @selection-change="handleSelectionChange"
          >
            <el-table-column label="序号" align="center" type="index"/>
            <el-table-column label="附件类型" align="center" prop="annexType" width="80">
              <template slot-scope="scope">
                <dict-tag :options="dict.type.contract_annex_type" :value="scope.row.annexType"/>
              </template>
            </el-table-column>
            <el-table-column label="文件类型" align="center" prop="fileType" width="80">
              <template slot-scope="scope">
                <dict-tag :options="dict.type.contract_file_type" :value="scope.row.fileType"/>
              </template>
            </el-table-column>
            <el-table-column label="附件路径" align="center" prop="annexPath" width="250">
              <template slot-scope="props">
                <el-link type="primary" :href="props.row.annexPath" target="_blank">{{ props.row.annexPath }}</el-link>
              </template>
            </el-table-column>
            <el-table-column
              label="附件创建时间"
              align="center"
              prop="createTime"
            >
              <template slot-scope="scope">
                <span>{{ moment(scope.row.createTime).format('YYYY-MM-DD HH:mm:ss') }}</span>
              </template>
            </el-table-column>
            <el-table-column
              label="附件上传时间"
              align="center"
              prop="createTime"
            >
              <template slot-scope="scope">
                <span>{{ moment(scope.row.createTime).format('YYYY-MM-DD HH:mm:ss') }}</span>
              </template>
            </el-table-column>
            <el-table-column label="备注" align="center" prop="remark"/>
          </el-table>
        </el-col>
      </el-form>
    </el-row>
    <Dialog title="附件上传" :show.sync="openFlag" width="800px">
      <div class="road-interflow-edit" v-loading="upload.isUploading">
        <el-row :gutter="15">
          <el-form
            ref="elForm"
            :model="fileForm"
            size="mini"
            label-width="100px"
          >
            <el-col :span="12">
              <el-form-item label="附件类型" prop="annexType">
                <DictSelect
                    v-model="fileForm.annexType"
                    :type="'contract_annex_type'"
                    :placeholder="'附件类型'"
                    clearable
                ></DictSelect>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="文件类型" prop="fileType">
                <DictSelect
                    v-model="fileForm.fileType"
                    :type="'contract_file_type'"
                    :placeholder="'文件类型'"
                    clearable
                ></DictSelect>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="文件" prop="field140">
                <el-upload
                  ref="uploadRef"
                  :headers="upload.headers"
                  :action="upload.url"
                  :on-progress="handleFileUploadProgress"
                  :on-success="handleFileSuccess"
                  :on-remove="handleUpdFileList"
                  multiple
                  :file-list="fileList"
                >
                  <el-input
                    v-model="queryParams.field140"
                    placeholder="请上传"
                    clearable
                    size="mini"
                    style="width: 100%"
                  >
                  </el-input>
                </el-upload>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="备注" prop="remark">
                <el-input
                  v-model="fileForm.remark"
                  placeholder="请输入备注"
                  clearable
                  size="mini"
                  :style="{ width: '100%' }"
                >
                </el-input>
              </el-form-item>
            </el-col>
            <div class="text-center">
              <el-button
                type="primary"
                icon="el-icon-search"
                size="mini"
                @click="handleAddFile"
              >新增
              </el-button
              >
              <el-button
                type="danger"
                icon="el-icon-delete"
                size="mini"
                @click="handleDelete"
              >删除
              </el-button
              >
            </div>
            <el-col :span="24" style="margin-top: 20px">
              <el-table v-adjust-table
                size="mini"
                style="width: 100%"
                v-loading="loading"
                border
                :data="tableData"
                @selection-change="handleSelectionChange"
              >
                <el-table-column type="selection" width="50" align="center"/>
                <el-table-column label="序号" align="center" type="index"/>
                <el-table-column label="文件名" align="center" prop="annexName"/>
                <el-table-column
                  label="附件创建时间"
                  align="center"
                  prop="createTime"
                />
                <el-table-column
                  label="附件类型"
                  align="center"
                  prop="annexType"
                >
                  <template slot-scope="scope">
                    <dict-tag :options="dict.type.contract_annex_type" :value="scope.row.annexType"/>
                  </template>
                </el-table-column>
                <el-table-column label="状态" align="center" prop="status"/>
                <el-table-column
                  label="文件类型"
                  align="center"
                  prop="fileType"
                >
                  <template slot-scope="scope">
                    <dict-tag :options="dict.type.contract_file_type" :value="scope.row.fileType"/>
                  </template>
                </el-table-column>
                <el-table-column label="备注" align="center" prop="remark"/>
              </el-table>
            </el-col>
          </el-form>
        </el-row>

      </div>
    </Dialog>
  </div>
</template>
<script>
import {getToken} from "@/utils/auth";
import Dialog from "@/components/Dialog/index.vue";
import {listFiles, addFiles, deleteFiles} from "@/api/contract/info/index.js";
import moment from "moment";

export default {
  components: {Dialog},
  props: {
    conId: {
      type: String,
      default: ''
    }
  },
  dicts: ['contract_annex_type', 'contract_file_type'],
  data() {
    return {
      loading: false,
      openFlag: false,
      tableData: [],
      queryParams: {
        annexType: null,
        fileType: null
      },
      selectIds: [],
      fileForm: {
        annexType: null,
        fileType: null,
        remark: null
      },
      // 用户导入参数
      upload: {
        // 是否禁用上传
        isUploading: false,
        // 设置上传的请求头部
        headers: {Authorization: "Bearer " + getToken()},
        // 上传的地址
        url: process.env.VUE_APP_BASE_API + "/ruoyi-file/upload?platform=ylzx"
      }
    };
  },
  computed: {
    moment() {
      return moment
    }
  },
  watch: {},
  created() {
  },
  mounted() {
    this.getList()
  },
  methods: {
    getList() {
      this.loading = true
      this.queryParams.conId = this.conId
      listFiles(this.queryParams).then(res => {
        this.tableData = res.rows
        this.loading = false
      })
    },
    // 文件上传中处理
    handleFileUploadProgress(event, file, fileList) {
      this.upload.isUploading = true;
    },
    // 文件上传成功处理
    handleFileSuccess(response, file, fileList) {
      this.upload.isUploading = false;
      this.fileList = fileList
      console.log(this.fileList)
    },
    handleUpdFileList(file, fileList) {
      this.fileList = fileList
      console.log(this.fileList)
    },
    // 选中
    handleSelectionChange(e) {
      this.selectIds = e.map(obj => obj.id)
    },
    handleDelete() {
      const that = this
      this.$modal.confirm('是否删除').then(function() {
        deleteFiles(that.selectIds).then(res => {
          that.$modal.msgSuccess('删除成功')
          that.getList()
        })
      })
    },
    async handleAddFile() {
      this.loading = true
      for (let key in this.fileList) {
        const file = this.fileList[key]
        const fileModel = {
          annexName: file.name,
          annexPath: file.response.data.url,
          annexType: this.fileForm.annexType,
          conId: this.conId,
          fileType: this.fileForm.fileType,
          remark: this.fileForm.remark
        }
        await addFiles(fileModel)
      }
      // 清空已上传的文件
      this.$refs.uploadRef.clearFiles()
      this.getList()
    }
  },
};
</script>
<style lang="scss" scoped>
::v-deep .el-upload--text {
  width: 100% !important;
}
::v-deep .el-link {
  width: 200px;
  white-space: nowrap;
  overflow: hidden !important;
  text-overflow: ellipsis;
  display: inline-block;
}
</style>
<style lang="scss" scoped>
@import "@/assets/styles/business.scss";
</style>
