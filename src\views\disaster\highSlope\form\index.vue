<template>
  <div class="formBox">
    <el-form ref="formRef" :model="formParams" :disabled="processIsApprove === '1'" :rules="formRules" label-width="140px" label-position="right">
      <div class="infoBox" style="margin-bottom: 20px;" v-loading="formLoading">
        <div class="infoTitle">
          基础信息
        </div>
        <el-row :gutter="12">
          <el-col :span="12">
            <el-form-item label="管养单位名称" prop="maintenanceUnitId">
              <el-select ref="formMainRef" v-model="formParams.maintenanceUnitId" placeholder="请选择管养单位"
                         style="width: 100%;" @change="formMaintenanceChange('1')">
                <el-option v-for="item in formMaintenanceRenderList" :label="item.label" :value="item.id"
                           :key="item.id" style="display: none;"></el-option>
                <el-tree
                  :data="formMaintenanceList"
                  :props="{ children: 'children', label: 'label', value: 'id' }"
                  :expand-on-click-node="false"
                  highlight-current
                  default-expand-all
                  @node-click="formMainItemClick"
                >
                </el-tree>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item prop="roadSectionId">
                <span slot="label">
                  <el-tooltip content="选择管养单位后带出" placement="top">
                    <i class="el-icon-question"></i>
                  </el-tooltip>
                  路段名称
                </span>
              <el-select v-model="formParams.roadSectionId" placeholder="请选择路段" style="width: 100%;"
                         @change="formMaintenanceChange('2')">
                <el-option v-for="item in formRoadSectionList" :label="item.maintenanceSectionName"
                           :value="item.maintenanceSectionId" :key="item.maintenanceSectionId"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="12">
          <el-col :span="12">
            <el-form-item prop="routerNum">
                <span slot="label">
                  <el-tooltip content="选择路段后带出" placement="top">
                    <i class="el-icon-question"></i>
                  </el-tooltip>
                  路线名称
                </span>
              <el-select v-model="formParams.routerNum" placeholder="请选择路线" style="width: 100%;"
                         @change="formMaintenanceChange('3')">
                <el-option v-for="item in formRouteList" :label="`${item.routeName}（${item.routeCode}）`"
                           :value="item.routeCode" :key="item.routeCode"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item prop="technicalGrade">
                <span slot="label">
                  <el-tooltip content="选择路段后带出" placement="top">
                    <i class="el-icon-question"></i>
                  </el-tooltip>
                  技术等级
                </span>
              <el-select disabled v-model="formParams.technicalGrade" placeholder="请选择技术等级"
                         style="width: 100%;">
                <el-option v-for="dict in dict.type.sys_route_grade" :label="dict.label" :value="dict.value"
                           :key="dict.value"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="12">
          <el-col :span="8">
            <el-form-item prop="">
                <span slot="label">
                  <el-tooltip content="省级区划代码" placement="top">
                    <i class="el-icon-question"></i>
                  </el-tooltip>
                  省级区划
                </span>
              <el-select v-model="divisionsProvincial" disabled style="width: 100%;">
                <el-option v-for="item in divisionsProvincialList" :label="`${item.label}（${item.id}）`"
                           :value="item.id" :key="item.id"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item prop="">
                <span slot="label">
                  <el-tooltip content="市级区划代码" placement="top">
                    <i class="el-icon-question"></i>
                  </el-tooltip>
                  市级区划
                </span>
              <el-select v-model="divisionsMunicipal" clearable style="width: 100%;" @change="formChangeAreaCode">
                <el-option v-for="item in divisionsMunicipalList" :label="`${item.label}（${item.id}）`"
                           :value="item.id" :key="item.id"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item prop="areaCode">
                <span slot="label">
                  <el-tooltip content="县级区划代码，用于生成灾害编码" placement="top">
                    <i class="el-icon-question"></i>
                  </el-tooltip>
                  县级区划
                </span>
              <el-select v-model="formParams.areaCode" clearable style="width: 100%;">
                <el-option v-for="item in areaCodeList" :label="`${item.label}（${item.id}）`" :value="item.id"
                           :key="item.id"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="12">
          <el-col :span="12">
            <el-form-item prop="highSlopeName">
                <span slot="label">
                  <el-tooltip content="按“里程桩号+段高边坡”保存后自动生成（例如“K0+000 至K0+100 段高边坡”）"
                              placement="top">
                    <i class="el-icon-question"></i>
                  </el-tooltip>
                  高边坡名称
                </span>
              <el-input v-model="formParams.highSlopeName" disabled placeholder="请输入高边坡名称"/>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item prop="highSlopeNum">
                <span slot="label">
                  <el-tooltip
                    content="按“路线编号+县级行政区划代码+B+四位编号+L（R）”进行编号（边坡四位编码后加 L 或 R 代表左侧或右侧，例如G75520303B0002L）,保存后自动生成"
                    placement="top">
                    <i class="el-icon-question"></i>
                  </el-tooltip>
                  高边坡编号
                </span>
              <el-input v-model="formParams.highSlopeNum" disabled placeholder="请输入高边坡编号"/>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="12">
          <el-col :span="12">
            <el-form-item prop="pileStartNum">
                <span slot="label">
                  <el-tooltip
                    content="起止点桩号，根据现场调查填写起点桩号和止点桩号（例如起点桩号 K0+100，止点桩号 K0+200）"
                    placement="top">
                    <i class="el-icon-question"></i>
                  </el-tooltip>
                  边坡起点桩号
                </span>
              <div style="width: 100%; display: flex; flex-direction: row; flex-wrap: nowrap;">
                <el-tag
                  type="info"
                  effect="plain"
                  size="medium"
                >
                  K
                </el-tag>
                <el-input v-model="pileStartK" controls-position="right" style="width: 50%;" placeholder="请输入起点桩号"/>
                <el-tag type="info" effect="plain" size="medium">
                  +
                </el-tag>
                <el-input v-model="pileStartAdd" controls-position="right" style="width: 50%;" placeholder="请输入起点桩号"/>
              </div>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item prop="pileEndNum">
                <span slot="label">
                  <el-tooltip
                    content="起止点桩号，根据现场调查填写起点桩号和止点桩号（例如起点桩号 K0+100，止点桩号 K0+200）"
                    placement="top">
                    <i class="el-icon-question"></i>
                  </el-tooltip>
                  边坡终点桩号
                </span>
              <div style="width: 100%; display: flex; flex-direction: row; flex-wrap: nowrap;">
                <el-tag
                  type="info"
                  effect="plain"
                  size="medium"
                >
                  K
                </el-tag>
                <el-input v-model="pileEndK" controls-position="right" style="width: 50%;" placeholder="请输入终点桩号"/>
                <el-tag
                  type="info"
                  effect="plain"
                  size="medium"
                >
                  +
                </el-tag>
                <el-input v-model="pileEndAdd" controls-position="right" style="width: 50%;" placeholder="请输入终点桩号"/>
              </div>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="12">
          <el-col :span="12">
            <el-form-item prop="startLatitudeAndLongitude">
                <span slot="label">
                  <el-tooltip content="起点经纬度利用数据采集系统采集" placement="top">
                    <i class="el-icon-question"></i>
                  </el-tooltip>
                  边坡起点经纬度
                </span>
              <el-input v-model="formParams.startLatitudeAndLongitude" placeholder="请输入起点经纬度，以英文逗号分隔">
                <el-button slot="append" icon="el-icon-location" @click="openCoordinateDialog('start')">坐标拾取
                </el-button>
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item prop="endLatitudeAndLongitude">
                <span slot="label">
                  <el-tooltip content="止点经纬度利用数据采集系统采集" placement="top">
                    <i class="el-icon-question"></i>
                  </el-tooltip>
                  边坡止点经纬度
                </span>
              <el-input v-model="formParams.endLatitudeAndLongitude" placeholder="请输入止点经纬度，以英文逗号分隔">
                <el-button slot="append" icon="el-icon-location" @click="openCoordinateDialog('end')">坐标拾取
                </el-button>
              </el-input>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="12">
          <el-col :span="12">
            <el-form-item label="建造年度" prop="constructionYear">
              <el-date-picker
                style="width: 100%;"
                v-model="formParams.constructionYear"
                type="year"
                value-format="yyyy"
                placeholder="建造年度"
              >
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="改建年度" prop="renovationYear">
              <el-date-picker
                style="width: 100%;"
                v-model="formParams.renovationYear"
                type="year"
                value-format="yyyy"
                placeholder="改建年度"
              >
              </el-date-picker>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="12">
          <el-col :span="12">
            <el-form-item prop="slopeLength">
                <span slot="label">
                  <el-tooltip content="根据起止点桩号，保存后自动计算" placement="top">
                    <i class="el-icon-question"></i>
                  </el-tooltip>
                  边坡长度(米)
                </span>
              <el-input v-model="formParams.slopeLength" disabled style="width: 100%;"
                        placeholder="请输入边坡长度(米)"/>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="车道数量(个)" prop="laneNum">
              <el-input-number v-model="formParams.laneNum" controls-position="right" :min="0" :max="8" :precision="0"
                               style="width: 100%;" placeholder="请输入车道数量(个)"/>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="12">
          <el-col :span="12">
            <el-form-item label="路基宽度(米)" prop="roadbedWidth">
              <el-input-number v-model="formParams.roadbedWidth" controls-position="right" :min="0" :precision="2"
                               style="width: 100%;" placeholder="请输入路基宽度(米)"/>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="路面宽度(米)" prop="roadWidth">
              <el-input-number v-model="formParams.roadWidth" controls-position="right" :min="0" :precision="2"
                               style="width: 100%;" placeholder="请输入路面宽度(米)"/>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="12">
          <!-- <el-col :span="12">
            <el-form-item label="区划代码" prop="areaCode">
              <el-input v-model="formParams.areaCode" type="number" placeholder="请输入区划代码" />
            </el-form-item>
          </el-col> -->
          <el-col :span="12">
            <el-form-item label="相对位置" prop="relativePosition">
              <el-radio-group v-model="formParams.relativePosition" ref="formRadio1">
                <el-radio v-for="dict in dict.type.side_slope_relative_position" :key="dict.value" :label="dict.value">{{
                    dict.label
                  }}
                </el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="12">
          <el-col :span="24">
            <el-form-item label="面层类型" prop="surfaceLayerType">
              <el-radio-group v-model="formParams.surfaceLayerType" ref="formRadio2">
                <el-radio v-for="dict in dict.type.roadbed_surface_type" :label="dict.value">{{
                    dict.label
                  }}
                </el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>

      </div>

      <div class="infoBox" style="margin-bottom: 20px;" v-loading="formLoading">
        <div class="infoTitle">
          边坡类型
        </div>
        <el-row :gutter="12">
          <el-col :span="24">
            <el-form-item label="边坡类型">
              <el-radio-group v-model="slopeType" @change="slopeTypeChange" ref="formRadio8">
                <el-radio label="0">路堤边坡</el-radio>
                <el-radio label="1">路堑边坡</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>
      </div>

      <div class="infoBox" style="margin-bottom: 20px;" v-loading="formLoading" v-show="slopeType === '0'">
        <div class="infoTitle">
          边坡类型（路堤边坡）
        </div>
        <el-row :gutter="12">
          <el-col :span="8">
            <el-form-item label="坡高(米)" prop="embankmentSlopeHeight">
              <el-input-number v-model="formParams.embankmentSlopeHeight" controls-position="right" :min="0"
                               :precision="2" style="width: 100%;" placeholder="请输入坡高(米)"/>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="坡度(度)" prop="embankmentSlope">
              <el-input-number v-model="formParams.embankmentSlope" controls-position="right" :min="0" :precision="2"
                               style="width: 100%;" placeholder="请输入坡度(度)"/>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="分级(级)" prop="embankmentGrade">
              <el-input-number v-model="formParams.embankmentGrade" controls-position="right" :min="0" :precision="2"
                               style="width: 100%;" placeholder="请输入分级(级)"/>
            </el-form-item>
          </el-col>
        </el-row>

        <el-divider></el-divider>

        <el-row :gutter="12">
          <el-col :span="24">
            <el-form-item label="是否临河" prop="embankmentByARiver">
              <el-radio-group v-model="formParams.embankmentByARiver" ref="formRadio3" @change="formRiverChange">
                <el-radio :label="'0'">是</el-radio>
                <el-radio :label="'1'">否</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>

        <el-divider></el-divider>

        <el-row :gutter="12" v-show="formParams.embankmentByARiver === '0'">
          <el-col :span="24">
            <el-form-item label="临河地形" prop="embankmentRiversideTerrain">
              <el-radio-group v-model="formParams.embankmentRiversideTerrain" ref="formRadio4">
                <el-radio v-for="dict in dict.type.side_slope_river_terrain" :label="dict.value">{{
                    dict.label
                  }}
                </el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>

      </div>

      <div class="infoBox" style="margin-bottom: 20px;" v-loading="formLoading" v-show="slopeType === '1'">
        <div class="infoTitle">
          边坡类型（路堑边坡）
        </div>
        <el-row :gutter="12">
          <el-col :span="8">
            <el-form-item label="坡高(米)" prop="cuttingSlopeHeight">
              <el-input-number v-model="formParams.cuttingSlopeHeight" controls-position="right" :min="0"
                               :precision="2" style="width: 100%;" placeholder="请输入坡高(米)"/>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="坡度(度)" prop="cuttingSlope">
              <el-input-number v-model="formParams.cuttingSlope" controls-position="right" :min="0" :precision="2"
                               style="width: 100%;" placeholder="请输入坡度(度)"/>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="分级(级)" prop="cuttingGrade">
              <el-input-number v-model="formParams.cuttingGrade" controls-position="right" :min="0" :precision="0"
                               style="width: 100%;" placeholder="请输入分级(级)"/>
            </el-form-item>
          </el-col>
        </el-row>

        <el-divider></el-divider>

        <el-row :gutter="12">
          <el-col :span="24">
            <el-form-item label="岩性" prop="cuttingLithology">
              <el-radio-group v-model="formParams.cuttingLithology" ref="formRadio5">
                <el-radio v-for="dict in dict.type.side_slope_lithology" :key="dict.value" :label="dict.value">{{
                    dict.label
                  }}
                </el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>

      </div>

      <div class="infoBox" style="margin-bottom: 20px;" v-loading="formLoading">
        <div class="infoTitle">
          排水设施类型
        </div>
        <el-row :gutter="12">
          <el-col :span="24">
            <el-form-item label="地表排水设施" prop="surfaceDrainageFacilities">
              <el-checkbox-group v-model="formParams.surfaceDrainageFacilities">
                <el-checkbox v-for="dict in dict.type.side_slope_surface_drainage_facilities" :label="dict.value">
                  {{ dict.label }}
                </el-checkbox>
              </el-checkbox-group>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="12">
          <el-col :span="24">
            <el-form-item label="" prop="surfaceDrainageFacilitiesRemark">
              <el-input v-model="formParams.surfaceDrainageFacilitiesRemark" placeholder="其它"/>
            </el-form-item>
          </el-col>
        </el-row>

        <el-divider></el-divider>

        <el-row :gutter="12">
          <el-col :span="24">
            <el-form-item label="地下排水设施" prop="undergroundDrainageFacilities">
              <el-checkbox-group v-model="formParams.undergroundDrainageFacilities">
                <el-checkbox v-for="dict in dict.type.side_slope_underground_drainage_facilities" :label="dict.value">
                  {{ dict.label }}
                </el-checkbox>
              </el-checkbox-group>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="12">
          <el-col :span="24">
            <el-form-item label="" prop="undergroundDrainageFacilitiesRemark">
              <el-input v-model="formParams.undergroundDrainageFacilitiesRemark" placeholder="其它"/>
            </el-form-item>
          </el-col>
        </el-row>

      </div>

      <div class="infoBox" style="margin-bottom: 20px;" v-loading="formLoading">
        <div class="infoTitle">
          防护设施类型
        </div>

        <el-row :gutter="12">
          <el-col :span="24">
            <el-form-item label="坡面防护" prop="curDamagedSlopeSurface">
              <el-checkbox-group v-model="formParams.curDamagedSlopeSurface">
                <el-checkbox v-for="dict in dict.type.side_slope_protection" :label="dict.value">{{
                    dict.label
                  }}
                </el-checkbox>
              </el-checkbox-group>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="12">
          <el-col :span="24">
            <el-form-item label="" prop="curDamagedSlopeSurfaceRemark">
              <el-input v-model="formParams.curDamagedSlopeSurfaceRemark" placeholder="其它"/>
            </el-form-item>
          </el-col>
        </el-row>

        <el-divider></el-divider>

        <el-row :gutter="12">
          <el-col :span="24">
            <el-form-item label="沿河防护" prop="curDamagedRiverBank">
              <el-checkbox-group v-model="formParams.curDamagedRiverBank">
                <el-checkbox v-for="dict in dict.type.protection_along_the_river" :label="dict.value">{{
                    dict.label
                  }}
                </el-checkbox>
              </el-checkbox-group>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="12">
          <el-col :span="24">
            <el-form-item label="" prop="curDamagedRiverBankRemark">
              <el-input v-model="formParams.curDamagedRiverBankRemark" placeholder="其它"/>
            </el-form-item>
          </el-col>
        </el-row>

        <el-divider></el-divider>

        <el-row :gutter="12">
          <el-col :span="24">
            <el-form-item label="支挡设施" prop="curDamagedSupportFacility">
              <el-checkbox-group v-model="formParams.curDamagedSupportFacility">
                <el-checkbox v-for="dict in dict.type.side_slope_support_facilities" :label="dict.value">{{
                    dict.label
                  }}
                </el-checkbox>
              </el-checkbox-group>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="12">
          <el-col :span="24">
            <el-form-item label="" prop="curDamagedSupportFacilityRemark">
              <el-input v-model="formParams.curDamagedSupportFacilityRemark" placeholder="其它"/>
            </el-form-item>
          </el-col>
        </el-row>

      </div>

      <div class="infoBox" style="margin-bottom: 20px;" v-loading="formLoading">
        <div class="infoTitle">
          抗震设防等级
        </div>
        <el-row :gutter="12">
          <el-col :span="24">
            <el-form-item label-width="0" prop="seismicFortificationLevel">
              <el-radio-group v-model="formParams.seismicFortificationLevel" ref="formRadio6">
                <el-radio v-for="dict in dict.type.seismic_fortification_level" :key="dict.value" :label="dict.value">{{
                    dict.label
                  }}
                </el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>
      </div>

      <div class="infoBox" style="margin-bottom: 20px;" v-loading="formLoading">
        <div class="infoTitle">
          防洪标准
        </div>
        <el-row :gutter="12">
          <el-col :span="24">
            <el-form-item label-width="0" prop="floodControlStandard">
              <el-radio-group v-model="formParams.floodControlStandard"
                              @input="formRadioChange('floodControlStandard', 'floodControlStandardRemark')"
                              ref="subFormRadio8">
                <el-radio v-for="dict in dict.type.side_slop_flood_control" :key="dict.value" :label="dict.value"
                          style="margin-right: 30px; margin-top: 5px; margin-bottom: 5px;">{{ dict.label }}
                </el-radio>
                <el-radio label="99" style="margin-top: 5px; margin-bottom: 5px;">其它</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="12" v-show="formParams.floodControlStandard === '99'">
          <el-col :span="24">
            <el-form-item label-width="0" prop="floodControlStandardRemark">
              <el-input v-model="formParams.floodControlStandardRemark" placeholder="其它"/>
            </el-form-item>
          </el-col>
        </el-row>
      </div>

      <div class="infoBox" style="margin-bottom: 20px;" v-loading="formLoading">
        <div class="infoTitle">
          其他需要说明的信息
        </div>
        <el-row :gutter="12">
          <el-col :span="24">
            <el-form-item label-width="0" prop="supplementaryInformation">
              <el-input
                :autosize="{ minRows: 4 }"
                type="textarea"
                placeholder="主要填写表格中未明确提出的其他需要上报的信息。"
                v-model="formParams.supplementaryInformation"
                maxlength="500"
                show-word-limit
              ></el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </div>

      <div class="infoBox" style="margin-bottom: 20px;" v-loading="formLoading">
        <div class="infoTitle">
          照片（边坡典型照片）
        </div>
        <el-row :gutter="12">
          <el-col :span="24">
            <el-form-item label-width="0" prop="riskDescription">
              <div class="imgBox" v-if="formImgSrcList.length > 0">
                <el-timeline>
                  <el-timeline-item v-for="item in formImgDateLine" :key="item.time" :timestamp="item.time" placement="top"
                                    color="#5cbb7a">
                    <el-card class="imgBoxCard">
                      <div class="cardMain" v-for="itemC in item.data">
                        <el-button class="imgDeleteBtn" type="danger" icon="el-icon-delete" circle
                                   @click="formDeleteImg(itemC.id)"></el-button>
                        <div class="imgTitle">
                          <el-tooltip class="item" effect="dark" :content="itemC.name" placement="top">
                            <i class="el-icon-info"></i>
                          </el-tooltip>
                          {{ itemC.name }}
                        </div>
                        <el-image
                          fit="cover"
                          class="img"
                          :src="itemC.url"
                          @click="formImgPreview(itemC.imgUrl)"
                          :preview-src-list="formImgUrlList"
                        ></el-image>
                        <div class="footer">
                          {{`由 ${itemC.createBy} 上传于 ${itemC.createTime}` }}
                        </div>
                      </div>
                    </el-card>
                  </el-timeline-item>
                </el-timeline>
              </div>
              <div class="noneBox" v-else>
                暂无内容
              </div>
            </el-form-item>
          </el-col>
        </el-row>
      </div>

      <div class="infoBox" v-loading="formLoading" style="padding: 15px;" v-if="processIsApprove === '0'">
        <div class="infoTitle">
          <el-tooltip content="上传填报高边坡的全貌照片及局部照片（防护设施、排水设施），不少于 3 张" placement="top">
            <i class="el-icon-question"></i>
          </el-tooltip>
          上传照片
        </div>
        <ImageUpload
          v-model="formUploadList"
          :ownerId="formImgOwnerId"
          :needRemark="true"
          :can-sort="true"
          storage-path="/disaster/risk/highSlope/"
          platform="fykj"
        />
      </div>

    </el-form>

    <el-dialog title="坐标拾取" class="coordinateDialog" :visible.sync="coordinateDialog" @close="coordinateClose"
               :close-on-press-escape="false" :close-on-click-modal="false" append-to-body>
      <div class="coordinateMap">
        <div id="coordinateBox" v-if="coordinateDialog"></div>
        <div class="coordinateSearch">
          <el-input v-model="coordinateSearch" clearable placeholder="请输入地名" @keyup.enter.native="coordinateList"/>
          <el-button type="primary" icon="el-icon-search" style="margin-left: 5px;" @click="coordinateList">查询
          </el-button>
        </div>
        <div class="coordinateTip">
          <el-row>
            <el-col :span="24" style="padding: 5px 0px;">
              当前经纬度：
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="24">
              <el-input v-model="coordinatePosition" readonly/>
            </el-col>
          </el-row>
        </div>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="coordinateSave">保 存</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {getToken} from "@/utils/auth";
import {queryHighSlopeForm} from "@/api/disaster/highSlope/highSlope";
import {deptTreeSelect} from "@/api/tmpl";
import {listMaintenanceSectionAll} from "@/api/system/maintenanceSection";
import {listByMaintenanceSectionId} from "@/api/baseData/common/routeLine";
import {getTreeByEntity} from "@/api/system/geography";
import {findFiles} from "@/api/file";
import {removeFile} from "@/api/system/fileUpload";
import AMapLoader from '@amap/amap-jsapi-loader'; // 高德地图
import ImageUpload from '@/views/disaster/ImageUpload.vue'
import CascadeSelection from "@/components/CascadeSelection";
import RangeInput from "@/views/baseData/components/rangeInput";
import {submitTaskHighSlope} from "@/api/process/task/task"

export default {
  name: "HighSlopeForm",
  // 数据字典
  dicts: [
    'side_slope_relative_position', // 相对位置
    'roadbed_surface_type',  // 面层类型
    'side_slope_river_terrain', // 临河地形
    'side_slope_lithology', // 岩性
    'side_slope_surface_drainage_facilities', // 地表排水设施
    'side_slope_underground_drainage_facilities', // 地下排水设施
    'side_slope_protection', // 坡面防护
    'protection_along_the_river', // 沿河防护
    'side_slope_support_facilities', // 支挡设施
    'seismic_fortification_level', // 抗震设防等级
    'side_slop_flood_control', // 防洪标准
    'sys_route_grade', // 路线等级
  ],
  components: {
    // PileInput,
    CascadeSelection,
    RangeInput,
    ImageUpload,
  },
  // -------------------- 变量 --------------------
  data() {
    return {

      /**
       * 表单相关
       */
      formData: {}, // 表单字段备份
      formParams: {}, // 表单字段
      processIsApprove: '0', // 流程是否可审批
      processParmas: { // 表单提交参数
        taskId: '', // 流程ID
        variables: {
          comment: '', // 审批意见
          operatingRecord: '', // 操作记录
          approved: true, // 是否通过
          variablesJson: [], // 附加变量
        },
        formData: {}, // 表单参数
      },
      formTitle: '高边坡信息调查表', // 表单dialog标题
      formDialog: false, // 表单dialog显隐
      formLoading: false, // 表单加载
      formBtnLoading: false, // 表单按钮加载
      formRiskType: '', // 表单灾害类型
      formRiskId: '', // 表单主表ID
      formType: 'add', // 表单类型
      formRules: { // 表单校验规则
        maintenanceUnitId: [
          {required: true, message: "管养单位不能为空", trigger: "blur"}
        ],
        roadSectionId: [
          {required: true, message: "路段信息不能为空", trigger: "blur"}
        ],
        technicalGrade: [
          {required: true, message: "技术等级不能为空", trigger: "blur"}
        ],
        areaCode: [
          {required: true, message: "县级行政区划编码不能为空", trigger: "blur"}
        ],
        routerName: [
          {required: true, message: "路线名称不能为空", trigger: "blur"}
        ],
        // startLatitudeAndLongitude: [
        //   { required: true, message: "起点经纬度不能为空", trigger: "blur" }
        // ],
        // endLatitudeAndLongitude: [
        //   { required: true, message: "终点点经纬度不能为空", trigger: "blur" }
        // ],
        // pileStartNum: [
        //   { required: true, message: "桩号(起)不能为空", trigger: "blur" }
        // ],
        // pileEndNum: [
        //   { required: true, message: "桩号(终)不能为空", trigger: "blur" }
        // ],
        relativePosition: [
          {required: true, message: "相对位置不能为空", trigger: "blur"}
        ],
        surfaceDrainageFacilities: [
          {required: true, message: "地表排水设施不能为空", trigger: "blur"}
        ],
        undergroundDrainageFacilities: [
          {required: true, message: "地下排水设施不能为空", trigger: "blur"}
        ],
        curDamagedSlopeSurface: [
          {required: true, message: "坡面防护不能为空", trigger: "blur"}
        ],
        curDamagedRiverBank: [
          {required: true, message: "沿河防护不能为空", trigger: "blur"}
        ],
        curDamagedSupportFacility: [
          {required: true, message: "支挡设施不能为空", trigger: "blur"}
        ],
      },
      formDataContrast: { // 主表对照中文
        id: '主表ID',
        routerNum: '路线编号',
        routerName: '路线名称',
        highSlopeNum: '高边坡编号',
        highSlopeName: '高边坡名称',
        pileStartNum: '桩号（起）',
        pileEndNum: '桩号（终）',
        roadSectionId: '路段编码',
        roadSectionName: '路段名称',
        startLatitudeAndLongitude: '经纬度（起）',
        endLatitudeAndLongitude: '经纬度（止）',
        constructionYear: '建造年度',
        renovationYear: '改建年度',
        managementOfficeId: '管理处ID',
        managementOfficeName: '管理处名称',
        propertyUnitName: '产权单位名称',
        propertyUnitId: '产权单位ID',
        propertyUnitType: '产权单位类型',
        maintenanceUnitId: '养护分处ID',
        maintenanceUnitName: '养护分处名称',
        areaCode: '区划代码',
        relativePosition: '相对位置',
        technicalGrade: '技术等级',
        slopeLength: '边坡长度（米）',
        laneNum: '车道数量（个）',
        roadbedWidth: '路基宽度（米）',
        roadWidth: '路面宽度（米）',
        surfaceLayerType: '面层类型',
        embankmentSlopeHeight: '路堤边坡（坡高）',
        embankmentSlope: '路堤边坡（坡度）',
        embankmentGrade: '路堤边坡（分级）',
        embankmentByARiver: '是否临河',
        embankmentRiversideTerrain: '临河地形',
        cuttingSlopeHeight: '路堑边坡（坡高）',
        cuttingSlope: '路堑边坡（坡度）',
        cuttingGrade: '路堑边坡（坡度）',
        cuttingLithology: '岩性',
        surfaceDrainageFacilities: '地表排水设施',
        surfaceDrainageFacilitiesRemark: '地表排水设施备注',
        undergroundDrainageFacilities: '地下排水设施',
        undergroundDrainageFacilitiesRemark: '地下排水设施备注',
        curDamagedSlopeSurface: '坡面防护',
        curDamagedSlopeSurfaceRemark: '坡面防护备注',
        curDamagedRiverBank: '沿河防护',
        curDamagedRiverBankRemark: '沿河防护备注',
        curDamagedSupportFacility: '支挡设施',
        curDamagedSupportFacilityRemark: '支挡设施备注',
        seismicFortificationLevel: '抗震设防等级',
        floodControlStandard: '防洪标准',
        floodControlStandardRemark: '防洪标准备注',
        supplementaryInformation: '其他需要说明的信息',
        imgIds: '照片（边坡典型照片）',
        processId: '流程ID',
        status: '状态',
        createName: '采集人昵称',
        createUnit: '采集人单位',
      },

      formMaintenanceList: [], // 管养单位列表
      formMaintenanceRenderList: [], // 管养单位渲染列表
      formRoadSectionList: [], // 路段列表
      formRouteList: [], // 路线列表
      // 图片上传相关
      formImgSrcList: [], // 图片渲染列表
      formImgUrlList: [], // 图片预览列表
      formUploadList: '', // 图片上传列表
      formImgOwnerId: '', // 图片上传ownerId
      formImgNum: [], // 图片数量（图片集用）

      /**
       * 桩号相关
       */
      pileStartK: '', // 起点桩号K
      pileStartAdd: '', // 起点桩号+
      pileEndK: '', // 终点桩号K
      pileEndAdd: '', // 终点桩号+

      /**
       * 边坡相关
       */
      slopeType: '0', // 边坡类型

      /**
       * 区划相关
       */
      divisionsProvincial: '53', // 省级区划
      divisionsProvincialList: [ // 省级区划列表
        {
          disabled: true,
          id: "53",
          label: "云南省",
          level: 1
        }
      ],
      divisionsMunicipal: '', // 市级区划
      divisionsMunicipalList: [], // 市级区划列表
      areaCodeList: [], // 县级区划列表


      /**
       * 导入相关
       */
      // 用户导入参数
      upload: {
        // 是否显示弹出层（高边坡数据导入）
        open: false,
        // 弹出层标题（高边坡数据导入）
        title: "",
        // 是否禁用上传
        isUploading: false,
        // 是否更新已经存在的高边坡数据
        // updateSupport: 0,
        // 崩塌数据类型
        disasterType: '',
        // 设置上传的请求头部
        headers: {Authorization: "Bearer " + getToken()},
        // 上传的地址
        url: process.env.VUE_APP_BASE_API + "/disaster/high-slope/importData"
      },

      /**
       * 坐标相关
       */
      coordinateDialog: false, // 坐标dialog
      coordinateMap: null, // 地图实例
      coordinateMarker: null, // 地图标记
      coordinateType: 'start', // 坐标类型
      coordinatePosition: '', // 坐标经纬度
      coordinateSearch: '', // 坐标查询
    }
  },
  // -------------------- 计算属性 --------------------
  computed: {
    formImgDateLine() {
      let dateLine = []
      if (this.formImgSrcList.length > 0) {
        this.formImgSrcList.forEach(item => {
          let date = item.createTime.split('T')[0]
          if (dateLine.length === 0) {
            dateLine.push({
              time: date,
              data: [item]
            })
          } else {
            let index = dateLine.findIndex(item2 => item2.time === date)
            if (index !== -1) {
              dateLine[index].data.push(item)
            } else {
              dateLine.push({
                time: date,
                data: [item]
              })
            }
          }
        })
        // 时间线排序
        dateLine.sort((a, b) => {
          // 将日期字符串分割并转换为日期对象
          let dateA = new Date(a.time);
          let dateB = new Date(b.time);

          // 比较日期
          return dateA - dateB;
        })
      }
      return dateLine
    }
  },
  mounted() {
    this.initPage()
  },
  // -------------------- 方法 --------------------
  methods: {

    // 初始化页面
    initPage() {
      // 流程提交参数赋值
      this.processParmas.taskId = this.$route.query.taskId
      // 流程是否可审批
      this.processIsApprove = this.$route.query.isApprove
      // 查询流程表单数据
      this.queryList(this.$route.query.businessKey)
      // 获取管养单位数据
      this.queryMaintenanceList()
      // 获取区划数据（市级）
      this.queryDivisionsTree(2)
    },

    // 获取高边坡数据
    async queryList(id) {
      this.formLoading = true
      await queryHighSlopeForm(id).then((res) => {
        if (res.code === 200) {
          this.formData = res.data
          this.formParams = JSON.parse(JSON.stringify(res.data))
          if (res.data.pileStartNum) {
            this.pileStartK = res.data.pileStartNum.split('+')[0].split('K')[1]
            this.pileStartAdd = res.data.pileStartNum.split('+')[1]
          }
          if (res.data.pileEndNum) {
            this.pileEndK = res.data.pileEndNum.split('+')[0].split('K')[1]
            this.pileEndAdd = res.data.pileEndNum.split('+')[1]
          }
          if (res.data.embankmentSlopeHeight) {
            this.slopeType = '0'
          } else {
            this.slopeType = '1'
          }
          this.formParams.surfaceDrainageFacilities = this.formArrayChange(res.data.surfaceDrainageFacilities) // 地表排水设施
          this.formParams.undergroundDrainageFacilities = this.formArrayChange(res.data.undergroundDrainageFacilities) // 地下排水设施
          this.formParams.curDamagedSlopeSurface = this.formArrayChange(res.data.curDamagedSlopeSurface) // 坡面防护
          this.formParams.curDamagedRiverBank = this.formArrayChange(res.data.curDamagedRiverBank) // 沿河防护
          this.formParams.curDamagedSupportFacility = this.formArrayChange(res.data.curDamagedSupportFacility) // 支挡设施
          if (res.data.imgIds) {
            this.queryImg(res.data.imgIds)
            this.formImgOwnerId = res.data.imgIds
          } else {
            this.formImgOwnerId = new Date().getTime().toString()
          }

        }
        this.formLoading = false
      }).catch((err) => {
        this.formLoading = false
        this.$message.error(err)
      })
    },

    // 表单数组转换
    formArrayChange(item) {
      if (item) {
        let check = item.includes(',')
        if (check) {
          return item.split(',')
        } else {
          return [item]
        }
      } else {
        return []
      }
    },

    // 获取管养单位
    queryMaintenanceList() {
      let vo = {
        deptTypeList: [1, 3, 4],
        types: 100
      }
      deptTreeSelect(vo).then((res) => {
        if (res.code === 200) {
          this.formMaintenanceList = res.data
          this.handleMainRender(this.formMaintenanceList)
        }
      }).catch((err) => {
        this.$message.error(err)
        this.formMaintenanceList = []
        this.formMaintenanceRenderList = []
      })
    },

    // 管养单位渲染列表处理
    handleMainRender(data) {
      data.forEach(item => {
        this.formMaintenanceRenderList.push(item)
        if (item.children) {
          this.handleMainRender(item.children)
        }
      })
    },

    // 获取路段信息
    queryMaintenanceSectionList(val) {
      listMaintenanceSectionAll({departmentIdList: val}).then((res) => {
        if (res.code === 200) {
          this.formRoadSectionList = res.data
        }
      }).catch((err) => {
        this.$message.error(err)
        this.formRoadSectionList = []
      })
    },

    // 获取路线信息
    queryRouterList(val) {
      listByMaintenanceSectionId({maintenanceSectionId: val}).then(res => {
        if (res.code == 200) {
          this.formRouteList = res.data
        }
      }).catch((err) => {
        this.$message.error(err)
        this.formRouteList = []
      })
    },

    // 获取区划树
    queryDivisionsTree(level) {
      let vo = {}
      switch (level) {
        case 2:
          vo = {
            supCode: this.divisionsProvincial
          }
          break;

        case 3:
          vo = {
            supCode: this.divisionsMunicipal
          }
          break;
      }

      getTreeByEntity(vo).then((res) => {
        if (res) {
          switch (level) {
            case 2:
              this.divisionsMunicipalList = res.data
              break;

            case 3:
              this.areaCodeList = res.data
              break;
          }
        }
      })
    },

    // 获取图片数据
    async queryImg(id) {
      await findFiles({ownerId: id}).then((res) => {
        if (res.code === 200 && res.data.length > 0) {
          this.formImgSrcList = res.data.map(item => ({
            id: item.ownerId + '-' + item.id,
            name: item.originalFilename,
            url: item.thumbUrl,
            imgUrl: item.url,
            remark: item.remark,
            createTime: item.createTime,
            createBy: item.createBy
          }))
          this.formImgUrlList = res.data.map(item => item.url)
        }
      }).catch(() => {
        this.$message.error('图片查询失败，请重新打开表单尝试')
      })
    },

    /**
     * 表单相关
     */

    // 点击预览图片时
    formImgPreview(url) {
      let index = this.formImgUrlList.findIndex(item => item === url)
      if (index !== -1) {
        let moveUrl = this.formImgUrlList.splice(index, this.formImgUrlList.length - index)
        this.formImgUrlList.unshift(...moveUrl)
      }
    },

    // 点击管养单位时
    formMainItemClick(data) {
      if (data.label.includes('分处') || data.label.includes('红河管理处')) {
        this.formParams.maintenanceUnitId = data.id
        this.$refs.formMainRef.blur()
        this.formMaintenanceChange('1')
      } else {
        this.$message.warning('仅可选择分处！')
      }
    },

    // 管养单位/路段改变时
    formMaintenanceChange(type) {
      switch (type) {
        case '1':
          // 获取管养单位名称
          this.formGetName('1', this.formParams.maintenanceUnitId)
          // 重置路段及路线数据
          this.formParams.roadSectionId = ''
          this.formParams.roadSectionName = ''
          this.formRoadSectionList = []
          this.formParams.routerNum = ''
          this.formParams.routerName = ''
          this.formRouteList = []
          // 重置路线等级
          this.formParams.technicalGrade = ''
          // 重新获取路段数据列表
          this.queryMaintenanceSectionList(this.formParams.maintenanceUnitId)
          break;

        case '2':
          // 获取路段单位名称
          this.formGetName('2', this.formParams.roadSectionId)
          // 重置路线数据
          this.formParams.routerNum = ''
          this.formParams.routerName = ''
          this.formRouteList = []
          // 重置路线等级
          this.formGetTechnicalGrade(this.formParams.roadSectionId)
          // 重新获取路线数据列表
          this.queryRouterList(this.formParams.roadSectionId)
          break;

        case '3':
          // 获取路段单位名称
          this.formGetName('3', this.formParams.routerNum)
          break;
      }
    },

    // 获取管养单位/路段/路线名称
    formGetName(type, val) {
      switch (type) {
        case '1':
          for (let i = 0; i < this.formMaintenanceRenderList.length; i++) {
            if (this.formMaintenanceRenderList[i].id == val) {
              this.formParams.maintenanceUnitName = this.formMaintenanceRenderList[i].label
              break
            }
          }
          break;

        case '2':
          for (let i = 0; i < this.formRoadSectionList.length; i++) {
            if (this.formRoadSectionList[i].maintenanceSectionId == val) {
              this.formParams.roadSectionName = this.formRoadSectionList[i].maintenanceSectionName
              break
            }
          }
          break;

        case '3':
          for (let i = 0; i < this.formRouteList.length; i++) {
            if (this.formRouteList[i].routeCode == val) {
              this.formParams.routerName = this.formRouteList[i].routeName
              break
            }
          }
          break;
      }
    },

    // 获取技术等级
    formGetTechnicalGrade(val) {
      for (let i = 0; i < this.formRoadSectionList.length; i++) {
        if (this.formRoadSectionList[i].maintenanceSectionId == val) {
          this.formParams.technicalGrade = this.formRoadSectionList[i].routeGrade
          break
        }
      }
    },

    // 市级区划改变时
    formChangeAreaCode(val) {
      this.formParams.areaCode = ''
      if (val) {
        let index = this.divisionsMunicipalList.findIndex(item => item.id === val)
        if (index !== -1) {
          this.areaCodeList = this.divisionsMunicipalList[index].children
        }
      } else {
        this.areaCodeList = []
      }
    },

    // 表单是否临河改变时
    formRiverChange() {
      // 清除临河地形
      if (this.formParams.embankmentByARiver === '1') {
        this.formParams.embankmentRiversideTerrain = null
      }
    },

    // 表单其他控制
    formRadioChange(type, remark) {
      if (this.formParams[type] !== '99') {
        this.formParams[remark] = ''
      }
    },

    // 删除图片
    formDeleteImg(id) {
      this.$modal.confirm('是否确认删除该图片？').then(async () => {
        this.$modal.loading('正在删除图片，请稍候...')
        removeFile(id.split('-')[1]).then((res) => {
          if (res.code === 200) {
            this.$message.success('删除图片成功！')
            // 移除预览列表图片
            let index = this.formImgSrcList.findIndex(item => item.id === id)
            if (index !== -1) {
              let imgUrl = this.formImgSrcList[index].imgUrl
              let imgIndex = this.formImgUrlList.indexOf(imgUrl)
              if (imgIndex !== -1) {
                this.formImgUrlList.splice(imgIndex, 1)
              }
            }
            // 移除渲染列表图片
            this.formImgSrcList = this.formImgSrcList.filter(item => item.id !== id)
          }
          this.$modal.closeLoading()
        }).catch(() => {
          this.$message.error('删除图片失败')
          this.$modal.closeLoading()
        })
      })
    },
    /**
     * 边坡相关
     */
    // 当边坡类型改变时
    slopeTypeChange() {
      switch (this.slopeType) {
        case '0':
          // 重置路堑数据
          this.formParams.cuttingSlopeHeight = null
          this.formParams.cuttingSlope = null
          this.formParams.cuttingGrade = null
          this.formParams.cuttingLithology = null
          break;

        case '1':
          // 重置路堤数据
          this.formParams.embankmentSlopeHeight = null
          this.formParams.embankmentSlope = null
          this.formParams.embankmentGrade = null
          this.formParams.embankmentByARiver = null
          this.formParams.embankmentRiversideTerrain = null
          break;
      }
    },
    /**
     * 坐标相关
     */
    // 打开地图dialog
    openCoordinateDialog(type) {
      this.coordinateDialog = true
      this.coordinateType = type
      this.$nextTick(() => {
        this.coordinateInit()
      })
    },

    // 地图初始化
    coordinateInit() {
      const _this = this
      // 重置筛选结果
      this.coordinateSearch = ''

      window._AMapSecurityConfig = {
        securityJsCode: "65e21f3185bc29823f26c3e54bb0a9ab", // 安全密钥
      };
      AMapLoader.load({
        key: "d12697699ae0de43e2cf383e80eaa375", // 申请好的Web端开发者Key，首次调用 load 时必填
        version: "2.0", // 指定要加载的 JSAPI 的版本，缺省时默认为 1.4.15
        // plugins: ["AMap.ToolBar", "AMap.Scale"], //需要使用的的插件列表，如比例尺'AMap.Scale'，支持添加多个如：['...','...']
      })
        .then((AMap) => {
          // 变量初始化
          let positionInit = [102.8207599, 24.8885797] // 默认坐标昆明市
          switch (_this.coordinateType) {
            case 'start':
              if (_this.formParams.startLatitudeAndLongitude && _this.formParams.startLatitudeAndLongitude.includes(",")) {
                let start = _this.formParams.startLatitudeAndLongitude.split(",")
                start.forEach(item => {
                  item = Number(item)
                })
                positionInit = start
              }
              break;

            case 'end':
              if (_this.formParams.endLatitudeAndLongitude && _this.formParams.endLatitudeAndLongitude.includes(",")) {
                let end = _this.formParams.endLatitudeAndLongitude.split(",")
                end.forEach(item => {
                  item = Number(item)
                })
                positionInit = end
              }
              break;
          }
          _this.coordinatePosition = positionInit.join(",")

          console.log('初始化位置的值：', positionInit)

          // 地图初始化
          let satellite = new AMap.TileLayer.Satellite(); //创建卫星图层
          let roadNet = new AMap.TileLayer.RoadNet(); //创建路网图层
          _this.coordinateMap = new AMap.Map("coordinateBox", {
            // 设置地图容器id
            viewMode: "3D", // 是否为3D地图模式
            zoom: 11, // 初始化地图级别
            center: positionInit, // 初始化地图中心点位置
            layers: [satellite, roadNet], // 地图图层的数组
          })
          AMap.plugin('AMap.ToolBar', function () {
            let toolbar = new AMap.ToolBar(); // 缩放工具条实例化
            _this.coordinateMap.addControl(toolbar); //添加控件
          })
          AMap.plugin('AMap.Scale', function () {
            let scale = new AMap.Scale(); // 比例尺工具条实例化
            _this.coordinateMap.addControl(scale); //添加控件
          })

          // 初始化坐标点
          if (_this.coordinateMarker) {
            _this.coordinateMarker = null
          }
          _this.coordinateMarker = new AMap.Marker({
            position: positionInit
          })
          _this.coordinateMap.add(_this.coordinateMarker)

          // 地图绑定点击事件
          _this.coordinateMap.on("click", _this.coordinateClick);
        })
        .catch((e) => {
          console.log(e);
        });
    },

    // 添加地图点击事件
    coordinateClick(e) {
      // 清除原有标记
      if (this.coordinateMarker) {
        this.coordinateMap.remove(this.coordinateMarker)
      }
      this.coordinateMarker = new AMap.Marker({
        position: [e.lnglat.getLng(), e.lnglat.getLat()]
      })
      this.coordinateMap.add(this.coordinateMarker)
      // 坐标点赋值
      this.coordinatePosition = `${e.lnglat.getLng()},${e.lnglat.getLat()}`
    },

    // 添加查询事件
    coordinateList() {
      const _this = this
      AMap.plugin('AMap.PlaceSearch', function () {
        let autoOptions = {
          city: ''
        }
        let placeSearch = new AMap.PlaceSearch(autoOptions);
        placeSearch.search(_this.coordinateSearch, function (status, result) {
          // 搜索成功时，result即是对应的匹配数据
          if (status === 'complete') {
            if (result.info === 'OK' && result.poiList.count > 0) {
              let lng = result.poiList.pois[0].location.lng
              let lat = result.poiList.pois[0].location.lat
              // 重置中心点
              _this.coordinateMap.setCenter([lng, lat])
              // 清除原有标记
              if (_this.coordinateMarker) {
                _this.coordinateMap.remove(_this.coordinateMarker)
              }
              _this.coordinateMarker = new AMap.Marker({
                position: [lng, lat]
              })
              _this.coordinateMap.add(_this.coordinateMarker)
              // 坐标点赋值
              _this.coordinatePosition = `${lng},${lat}`
            } else {
              _this.$message.warning('暂无数据！')
            }
          } else {
            _this.$message.warning('查询失败！')
          }
        })
      })
    },

    // 保存当前坐标
    coordinateSave() {
      if (!this.coordinatePosition) {
        this.$message.warning('请选择经纬度！')
        return
      }
      switch (this.coordinateType) {
        case 'start':
          this.formParams.startLatitudeAndLongitude = this.coordinatePosition
          break;

        case 'end':
          this.formParams.endLatitudeAndLongitude = this.coordinatePosition
          break;
      }
      this.coordinateDialog = false
    },

    // 窗口关闭时销毁地图
    coordinateClose() {
      if (this.coordinateMap) {
        this.coordinateMap.off("click", this.coordinateClick);
        this.coordinateMap.destroy()
        this.coordinateMap = null
      }
    },

    // 表单流程提交
    async formProcessSubmit(parmas) {
      this.$modal.loading("正在提交，请稍候...")
      // 赋予本地参数
      this.processParmas.variables.approved = parmas.approved
      this.processParmas.variables.comment = parmas.comment
      // 获取表单提交数据
      let vo = await this.formAssembly('formRef')
      if (!vo) {
        this.$modal.closeLoading()
        return
      }
      this.processParmas.formData = vo
      // 表单字段修改对比校验
      this.processParmas.variables.operatingRecord = JSON.stringify(this.formCompare(vo))
      submitTaskHighSlope(this.processParmas).then((res) => {
        console.log('提交表单结果：', res)
        if (res.code === 200) {
           window.close()
        }
        this.$modal.closeLoading()
      }).catch(() => {
        this.$modal.closeLoading()
      })
    },

    // 组装表单
    async formAssembly(formName) {
      return new Promise((resolve, reject) => {
        this.$refs[formName].validate((valid) => {
          if (valid) {
            this.formBtnLoading = true
            let vo = this.formParams
            // 桩号验证
            if (!this.pileStartK || !this.pileStartAdd) {
              this.$message.warning('请输入起点桩号！')
              this.formBtnLoading = false
              return
            }
            if (!this.pileEndK || !this.pileEndAdd) {
              this.$message.warning('请输入终点桩号！')
              this.formBtnLoading = false
              return
            }
            if (isNaN(this.pileStartK) || isNaN(this.pileEndK) || isNaN(this.pileStartAdd) || isNaN(this.pileEndAdd)) {
              this.$message.warning('桩号字段必须是数字！')
              this.formBtnLoading = false
              return;
            }

            // 起止点经纬度验证
            if (!vo.startLatitudeAndLongitude) {
              this.$message.warning('起点经纬度未填写/拾取！')
              this.formBtnLoading = false
              return
            }
            if (!vo.endLatitudeAndLongitude) {
              this.$message.warning('止点经纬度未填写/拾取！')
              this.formBtnLoading = false
              return
            }
            // 桩号组装
            vo.pileStartNum = `K${this.pileStartK}+${this.pileStartAdd}`
            vo.pileEndNum = `K${this.pileEndK}+${this.pileEndAdd}`
            // 边坡长度计算
            vo.slopeLength = Math.abs(parseFloat(this.pileEndK + this.pileEndAdd) - parseFloat(this.pileStartK + this.pileStartAdd));
            // 转换数组对象
            vo.surfaceDrainageFacilities = Array.isArray(vo.surfaceDrainageFacilities) ? vo.surfaceDrainageFacilities.join(",") : ''
            vo.undergroundDrainageFacilities = Array.isArray(vo.undergroundDrainageFacilities) ? vo.undergroundDrainageFacilities.join(",") : ''
            vo.curDamagedSlopeSurface = Array.isArray(vo.curDamagedSlopeSurface) ? vo.curDamagedSlopeSurface.join(",") : ''
            vo.curDamagedRiverBank = Array.isArray(vo.curDamagedRiverBank) ? vo.curDamagedRiverBank.join(",") : ''
            vo.curDamagedSupportFacility = Array.isArray(vo.curDamagedSupportFacility) ? vo.curDamagedSupportFacility.join(",") : ''

            if (this.formUploadList) {
              vo.imgIds = this.formImgOwnerId
            }
            resolve(vo)

          } else {
            this.$message.warning('表单必填字段未填写！')
            reject(false)
          }
        })
      })
    },

    // 表单字段对比校验
    formCompare(vo) {
      // 备份数据组装
      let oldVo = this.formData
      // 对比校验数据
      let content = []
      // 循环校对
      for (let key in oldVo) {
        if (oldVo[key] !== vo[key]) {
          content.push({
            name: this.formDataContrast[key],
            oldVal: oldVo[key],
            newVal: vo[key]
          })
        }
      }
      return content
    },
  },
}
</script>

<style lang="scss" scoped>

.infoBox {
  padding: 15px 15px 0 15px;
  box-sizing: border-box;
  border-radius: 6px;
  border: 1px solid #C4C4C4;
  position: relative;

  .infoTitle {
    user-select: none;
    position: absolute;
    top: 0;
    left: 0;
    padding: 0 10px;
    font-size: 14px;
    line-height: 14px;
    font-weight: bold;
    transform: translateX(15px) translateY(-50%);
    background-color: white;
  }

  .imgBox {
    height: auto;
    width: 100%;

    ::v-deep .el-card__body {
      width: 100%;
      display: flex;
      flex-direction: row;
      flex-wrap: wrap;
      align-content: flex-start;
    }

    .imgBoxCard {
      width: 100%;

      .cardMain {
        height: 260px;
        width: 33%;
        box-sizing: border-box;
        padding: 0 10px;
        display: flex;
        flex-direction: column;
        position: relative;

        .imgDeleteBtn {
          position: absolute;
          z-index: 1;
          top: 20%;
          right: 5%;
        }

        .imgTitle {
          height: 28px;
          width: 100%;
          font-size: 16px;
          font-weight: bold;
          white-space: nowrap;
          text-overflow: ellipsis;
          overflow: hidden;
        }

        .img {
          height: calc(100% - (28px + 28px));
          width: 100%;
          padding: 10px 0;
          position: relative;
          z-index: 0;
        }

        .footer {
          height: 28px;
          color: #888888;
          font-size: 14px;
        }
      }
    }
  }

  .noneBox {
    user-select: none;
    height: 200px;
    width: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    color: #888888;
  }
}

.coordinateDialog {

  .coordinateMap {
    height: 600px;
    width: 100%;
    position: relative;

    #coordinateBox {
      height: 100%;
      width: 100%;
      border-radius: 5px;
      position: relative;
      z-index: 0;
    }

    .coordinateSearch {
      position: absolute;
      z-index: 1;
      top: 10px;
      left: 10px;
      width: 50%;
      padding: 10px;
      box-sizing: border-box;
      background-color: #fff;
      border-radius: 5px;
      border: 1px solid #DCDFE6;
      box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.12), 0 0 6px 0 rgba(0, 0, 0, 0.04);
      display: flex;
      flex-direction: row;
    }

    .coordinateTip {
      position: absolute;
      z-index: 1;
      top: 10px;
      right: 10px;
      padding: 10px;
      box-sizing: border-box;
      background-color: #fff;
      border-radius: 5px;
      border: 1px solid #DCDFE6;
      box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.12), 0 0 6px 0 rgba(0, 0, 0, 0.04);
    }
  }
}

</style>
