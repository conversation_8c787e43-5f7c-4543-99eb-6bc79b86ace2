<template>
  <div v-loading="loading" class="route-road">
    <el-select
      filterable
      v-model="selectValue"
      ref="elSelect"
      style="width: 100%"
      clearable
      v-bind="$attrs"
      @change="changeSelect"
      :multiple="multiple"
    >
      <el-option
        v-for="item in routeOptions"
        :key="item.maintenanceSectionId"
        :label="item.maintenanceSectionName"
        :value="item.maintenanceSectionId"
      />
    </el-select>
  </div>
</template>

<script>
import { listMaintenanceSectionAll } from "@/api/system/maintenanceSection";
export default {
  props: {
    value: {
      type: [String,Number],
      default: ''
    },
    deptId: {
      type: [Number, String],
      default: null,
    },
    departmentIdList: {
      type: Array,
      default: null
    },
    multiple: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      routeOptions: [], // 路段数据
      loading: false,
      isFirstLoad: true
    };
  },
  computed: {
    selectValue: {
      get: function() {
        return this.value
      },
      set: function(val) {
        this.$emit('input', val)
      }
    }
  },
  watch: {
    deptId(val) {
      if (!this.isFirstLoad) {
        this.$emit('input', '')
      }
      this.getOptions()
      this.isFirstLoad = false;
    },
    departmentIdList: {
      handler(val) {
        if (!this.isFirstLoad) {
          this.$emit('input', '')
        }
        this.getOptions()
        this.isFirstLoad = false;
      },
      deep: true
    },
  },
  async created() {
    await this.getOptions();
    this.$emit('change', this.value);
  },
  methods: {
    async getOptions() {
      const res = await listMaintenanceSectionAll({ departmentId: this.deptId, departmentIdList: this.departmentIdList })
      this.routeOptions = res.data;
    },
    changeSelect(e) {
      this.$emit('update:value',e)
      this.$emit('change', e);

    },
    getLabel(id) {
      const label = this.routeOptions.find(item => item.maintenanceSectionId === id)?.maintenanceSectionName || ''
      return label
    }
  },
};
</script>

<style lang="scss" scoped></style>
