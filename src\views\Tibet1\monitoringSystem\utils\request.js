
/**
 * 封装的fetch请求
 * @param {string} url - 请求地址
 * @param {Object} options - 请求配置
 * @param {string} options.method - 请求方法，默认为GET
 * @param {Object} options.headers - 请求头
 * @param {Object} options.params - URL参数
 * @param {Object|Array} options.data - 请求体数据
 * @param {number} options.timeout - 超时时间，单位毫秒
 * @returns {Promise} - 返回Promise对象
 */
let baseUrl = "https://jkjc.glyhgl.com:22586"
export const request = async (url, options = {}) => {
  const {
    method = 'GET',
    headers = {},
    params = {},
    data = null,
    timeout = 30000
  } = options;

  // 处理URL参数
  const queryParams = new URLSearchParams();
  Object.keys(params).forEach(key => {
    queryParams.append(key, params[key]);
  });

  // 构建完整URL
  let fullUrl = queryParams.toString()
    ? `${url}${url.includes('?') ? '&' : '?'}${queryParams.toString()}`
    : url;
  fullUrl = baseUrl + fullUrl;
  // 默认请求头
  const defaultHeaders = {
    'Content-Type': 'application/json',
    ...headers
  };

  // 请求配置
  const fetchOptions = {
    method,
    headers: defaultHeaders,
    // credentials: 'include', // 包含cookies
    mode: 'cors', // 'cors', 'no-cors', or'same-origin'
  };

  // 添加请求体
  if (data && (method === 'POST' || method === 'PUT' || method === 'PATCH')) {
    fetchOptions.body = JSON.stringify(data);
  }

  // 创建超时Promise
  const timeoutPromise = new Promise((_, reject) => {
    setTimeout(() => {
      reject(new Error(`请求超时，超过 ${timeout}ms`));
    }, timeout);
  });

  try {
    // 竞争Promise，谁先完成就返回谁的结果
    const response = await Promise.race([
      fetch(fullUrl, fetchOptions),
      timeoutPromise
    ]);

    // 检查响应状态
    if (!response.ok) {
      throw new Error(`请求失败: ${response.status} ${response.statusText}`);
    }

    // 解析响应数据
    const contentType = response.headers.get('content-type');
    if (contentType && contentType.includes('application/json')) {
      return await response.json();
    } else {
      return await response.text();
    }
  } catch (error) {
    console.error('请求出错:', error);
    throw error;
  }
};

/**
 * GET请求
 * @param {string} url - 请求地址
 * @param {Object} options - 请求配置
 * @returns {Promise} - 返回Promise对象
 */
export const get = (url, options = {}) => {
  return request(url, { ...options, method: 'GET' });
};

/**
 * POST请求
 * @param {string} url - 请求地址
 * @param {Object|Array} data - 请求体数据
 * @param {Object} options - 请求配置
 * @returns {Promise} - 返回Promise对象
 */
export const post = (url, data, options = {}) => {
  return request(url, { ...options, method: 'POST', data });
};

/**
 * PUT请求
 * @param {string} url - 请求地址
 * @param {Object|Array} data - 请求体数据
 * @param {Object} options - 请求配置
 * @returns {Promise} - 返回Promise对象
 */
export const put = (url, data, options = {}) => {
  return request(url, { ...options, method: 'PUT', data });
};

/**
 * DELETE请求
 * @param {string} url - 请求地址
 * @param {Object} options - 请求配置
 * @returns {Promise} - 返回Promise对象
 */
export const del = (url, options = {}) => {
  return request(url, { ...options, method: 'DELETE' });
};
