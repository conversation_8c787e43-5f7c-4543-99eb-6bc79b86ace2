<template>
  <div v-loading="loading" class="route-road">
    <el-select
      filterable
      v-model="selectValue"
      style="width: 100%"
      clearable
      v-bind="$attrs"
      @change="changeSelect"
    >
      <el-option
        v-for="item in routeOptions"
        :key="item.routeId"
        :label="item.routeName"
        :value="item.routeId"
      />
    </el-select>
  </div>
</template>

<script>
import { listByMaintenanceSectionId } from "@/api/baseData/common/routeLine";
export default {
  props: {
    value: {
      type: [String, Number],
      default: "",
    },
    routeId: {
      type: [Number, String],
      default: '',
    },
  },
  data() {
    return {
      routeOptions: [], // 路段数据
      loading: false,
    };
  },
  computed: {
    selectValue: {
      get: function () {
        return this.value;
      },
      set: function (val) {
        this.$emit("input", val);
      },
    },
  },
  watch: {
    routeId(val) {
      if (val) {
        this.getOptions();
      }
    },
  },
  created() {
    this.getOptions();
  },
  methods: {
    getOptions() {
      if(!this.routeId) return;
      listByMaintenanceSectionId({ maintenanceSectionId: this.routeId }).then(
        (res) => {
          if (res.code == 200) {
            this.routeOptions = res.data;
          }
        }
      );
    },
    changeSelect(e) {
      this.$emit("update:value", e);
    },
  },
};
</script>

<style lang="scss" scoped></style>
