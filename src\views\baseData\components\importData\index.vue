<template>
  <div>
    <el-dialog
      :visible.sync="dialogVisible"
      :before-close="handleClose"
      width="80%"
      append-to-body
      :close-on-click-modal="false"
      custom-class="custom-dialog"
    >
      <span slot="title">
        <div style="display: flex; justify-content: space-between">
          <span style="font-size: 16px; color: #1d2129"
            >{{ baseTypeText || "桥梁" }}批量{{
              this.importTypeText
            }}数据导入</span
          >
          <!-- <div
              style="margin-right: 30px;cursor: pointer;"
              @click="handleDownload('')"
          >{{ this.importTypeText }}导入模版下载 <i class="el-icon-download"/>
          </div> -->
        </div>
      </span>
      <!--      <div class="showText" v-if="showText">-->
      <!--            更新数据：请先使用数据导出功能导出需要更新的数据进行修改，然后再将修改后的文件上传回系统。(注意不要直接下载新增模板进行修改)-->
      <!--      </div>-->
      <div class="showText">
        {{ this.tipsTextMsg }}
      </div>
      <div
        style="
          width: 100%;
          display: flex;
          justify-content: space-between;
          margin-bottom: 10px;
        "
      >
        <div style="flex: 1;">
          <el-row :gutter="10">
            <el-col :xs="4" :sm="4" :md="4" :lg="3" :xl="3">
              <el-input v-model="queryParams.importFileName" placeholder="请输入文件名称" style="width: 100%"/>
            </el-col>
            <el-col :xs="4" :sm="4" :md="4" :lg="3" :xl="3">
              <el-select
                v-model="queryParams.importState"
                placeholder="请选择导入状态"
                clearable
                style="width: 100%"
              >
                <el-option label="待导入" value="1"></el-option>
                <el-option label="导入中" value="2"></el-option>
                <el-option label="导入成功" value="3"></el-option>
                <el-option label="导入失败" value="4"></el-option>
                <el-option label="部分导入成功" value="5"></el-option>
              </el-select>
            </el-col>
            <el-col :xs="7" :sm="7" :md="7" :lg="7" :xl="6" style="display: flex;align-items: center;">
              <el-button type="primary" @click="getListPage()">搜索 </el-button>
              <el-button v-hasPermi="['baseData:import:del']"  type="danger"
                style="margin-left: 10px" @click="deleteByIds()">
                删除
              </el-button>
              <el-button type="primary" @click="getListPage()">刷新</el-button>
              <el-upload
                :headers="upload.headers"
                :action="upload.url"
                :on-change="onChange"
                :on-remove="handleRemove"
                :on-success="handleSuccess"
                :on-error="handleError"
                :file-list="fileList"
                :auto-upload="false"
                :show-file-list="false"
                ref="upload"
                style="width: 80px;margin-left: 10px;"
              >
                <!-- <div>
                  <el-input
                    v-model="fileName"
                    style="width: 220px;margin-right: 10px;"
                    size="small"
                    placeholder="选取文件"
                    suffix-icon="el-icon-upload"
                    clearable
                    @clear="fileList = []"
                  />
                </div> -->
                <el-button type="primary">上传文件</el-button>
              </el-upload>
            </el-col>
            <el-col :xs="12" :sm="12" :md="12" :lg="12" :xl="11">
              <div style="color: red;font-size: 12px;line-height: 30px;">严禁在本互联网非涉密平台处理、传输国家秘密，请确认扫描、传输的文件资料不涉及国家秘密</div>
            </el-col>
          </el-row>
        </div>
       
        <div style="display: flex;align-items: center;">
          <div style="margin-right: 10px;" v-if="isAdmin && [1,2,3,'1','2','3'].includes(importBaseType)">
            <el-checkbox v-model="checked" @change="onCheckBoxChange">参数校验</el-checkbox>
          </div>
          <el-select
            v-if="assetSubclassList.length > 0"
            style="margin-right: 20px"
            v-model="typeId"
            placeholder="资产子类"
            clearable
          >
            <el-option
              v-for="dict in assetSubclassList"
              :key="dict.id"
              :label="dict.typeName"
              :value="dict.id"
            />
          </el-select>

          <el-button type="success" @click="handleDownload('')"
            >{{ this.importTypeText }}导入模版下载
            <i class="el-icon-download" />
          </el-button>
        </div>
      </div>
      <el-table
        v-adjust-table
        ref="table"
        :key="timeKey"
        height="350"
        border
        :data="tableData"
        @selection-change="handleSelectionChange"
        :row-style="rowStyle"
        @row-click="handleRowClick"
        v-loading="loading"
      >
        <el-table-column type="selection" width="50" align="center" />
        <el-table-column
          fixed
          label="序号"
          type="index"
          width="50"
          align="center"
        />
        <el-table-column
          fixed
          label="导入文件名"
          align="center"
          prop="importFileName"
          min-width="300"
          show-overflow-tooltip
        >
        </el-table-column>
        <el-table-column
          label="导入状态"
          align="center"
          prop="importStateName"
          min-width="140"
          show-overflow-tooltip
        >
          <template #default="{ row }">
            <el-link
              :type="
                {
                  1: '',
                  2: 'primary',
                  3: 'success',
                  4: 'danger',
                  5: 'warning',
                }[row.importState]
              "
              :underline="false"
            >
              {{ row.importStateName }}
            </el-link>
          </template>
        </el-table-column>
        <el-table-column
          label="导入成功条数"
          align="center"
          prop="importSuccessCount"
          min-width="120"
          show-overflow-tooltip
        />
        <el-table-column
          label="导入失败条数"
          align="center"
          prop="importErrorCount"
          min-width="120"
          show-overflow-tooltip
        />
        <el-table-column v-if="isAdmin"
          label="上传文件操作人"
          align="center"
          prop="createBy"
          min-width="120"
          show-overflow-tooltip
        />
        <el-table-column v-if="isAdmin"
          label="导入操作人"
          align="center"
          prop="importUser"
          min-width="120"
          show-overflow-tooltip
        />
        <el-table-column
          label="导入时间"
          align="center"
          prop="importTime"
          min-width="200"
          show-overflow-tooltip
        />
        <el-table-column
          label="导入日志"
          align="center"
          prop="log"
          min-width="300"
          show-overflow-tooltip
        >
          <template #default="{ row }">
            <el-button
              style="font-size: 12px; font-weight: 500"
              type="text"
              @click.stop="logDelt(row)"
              icon="el-icon-document"
              :disabled="row.importState === 1"
              >查看日志
            </el-button>
            <el-button
              style="font-size: 12px; font-weight: 500"
              type="text"
              @click.stop="downloadErrFile(row)"
              icon="el-icon-document"
              :disabled="!row.importErrPath"
              >失败文件下载
            </el-button>
          </template>
        </el-table-column>

        <el-table-column
          label="操作"
          width="200"
          align="center"
          class-name="small-padding fixed-right"
          fixed="right"
        >
          <template #default="{ row }">
            <el-button
              style="font-size: 12px; font-weight: 500"
              icon="el-icon-view"
              type="text"
              @click.stop="handleOfficePreview(row)"
              >预览
            </el-button>
            <el-button
              size="mini"
              type="text"
              icon="el-icon-download"
              @click.stop="handleDownload(row)"
              >下载
            </el-button>
            <el-button
              v-hasPermi="['baseData:import:del']"
              size="mini"
              type="text"
              icon="el-icon-delete"
              @click.stop="deleteByIds(row.id)"
              >删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      <pagination
        v-show="total > 0"
        :total="total"
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
  :pageSizes="[10, 20, 30, 50, 100, 1000]"
        @pagination="getListPage"
      />
      <div slot="footer">
        <el-button :loading="loading" type="primary" @click="handleImport">
          导 入
        </el-button>
        <el-button @click="handleClose">退 出</el-button>
      </div>
    </el-dialog>

    <FilePreview
      :owner-id="ownerId"
      :office-preview-visible="officePreviewVisible"
      @close="officePreviewVisible = false"
    />

    <!-- 导入日志 -->

    <el-dialog
      title="导入日志"
      :visible.sync="viewLogVisible"
      width="65%"
      center
      append-to-bodys
    >
      <div style="height: 60vh; overflow-y: auto; padding: 0 10px 0 5px">
        <pre class="logo-content" v-html="logoText"></pre>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="viewLogVisible = false">关闭</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import {
  getListPage,
  importExecute,
  importExecuteWithCheckFlag,
  add,
  getInfoById,
  del,
} from "@/api/baseData/common/importRecord";
import { getToken } from "@/utils/auth";
import { createIdWorker } from "@/api/baseData/common";
import FilePreview from "@/components/FilePreview";
import { getFile } from "@/api/file";
import store from '@/store';

export default {
  name: "ImportData",
  components: { FilePreview },
  props: {
    dialogVisible: {
      type: Boolean,
      default: false,
    },
    id: {
      type: String || Number,
      default: "",
    },
    isUpdate: {
      type: Boolean,
      default: false,
    },
    importType: {
      type: Number,
    },
    importBaseType: {
      type: String,
      require: true,
      default: "",
    },
    mainTypeId: {
      type: Number || String,
      default: 0,
    },
    typeId: {
      type: String,
      default: "",
    },
    tipsText: {
      type: String,
      default: "",
    },
    assetSubclassList: {
      type: [Array],
      default: ()=> [],
    },
  },
  data() {
    return {
      officePreviewVisible: false,
      dialogUrl: "",
      contentType: "",
      viewLogVisible: false,
      importTypeText:
        this.importType === 1
          ? "更新"
          : this.importType === 2
          ? "新增"
          : "更新与新增",
      logoText: "",
      tipsTextMsg: "",
      form: {},
      fileList: [],
      fileName: "",
      tableData: [],
      ids: [],
      ownerId: "",
      upload: {
        ownerId: "",
        storagePath:
          "base/file/import/" + this.importBaseType
            ? this.importBaseType + "/"
            : "",
        headers: {
          Authorization: "Bearer " + getToken(),
        },
        url: "",
      },
      flag: true,
      timeKey: undefined,
      // 总条数
      total: 0,
      queryParams: {
        pageNum: 1,
        pageSize: 20,
      },
      loading: false, // 表格数据加载状态
      updateNumber: 0, // 用于处理当前上传文件 监听触发上传文件状态改变 事件触发次数
      isAdmin: store.getters.roles.includes('admin'), // 是否是管理员
      checked: true, // 是否跳过参数校验(只有admin才能改成false不开启校验)
      refreshInterval: null,
    };
  },
  computed: {
    baseTypeText() {
      let obj = {
        1: "桥梁",
        2: "隧道",
        3: "涵洞",
        4: "互通",
        5: "路基",
        8: "边坡",
        9: "挡墙",
        11: "平曲线",
        12: "桥梁定期检查病害",
        13: "隧道定期检查病害",
        14: "中央集水沟基础数据",
        15: "隧道定期检查评定记录",
        16: "积雪路段",
        17: "结冰路段",
        18: "隧道定期检查",
        19: "路肩基础数据",
        20: "交通安全设施基础 ",
        21: "附属设施基础数据",
        22: "路面基础数据",
        23: "路面结构数据",
        24: "绿化基础数据",
        25: "一级绿化基础数据",
        26: "二级绿化基础数据",
        27: "桥梁评定记录-部件",
        28: "桥梁评定记录-构件",
        29: "隧道定期检查明细",
        30: "路面评定单元管理",
        31: "隧道机电设备",
        // 33:"桥梁批量新增和更新",
        32: "路面养护处治",
        34: "隧道定期检查及病害数据",
        35: "路面技术状况结果",
        36 :"长下坡路段",
        37 :"急弯路段",
        38 :"坐标转换",
      };
      return obj[this.importBaseType];
    },
  },
  watch: {
    tipsText: {
      immediate: true, // 在组件初始化时立即执行
      handler(newVal) {
        if (!newVal) {
          if (this.isUpdate) {
            this.tipsTextMsg =
              "更新数据：请先使用数据导出功能导出需要更新的数据进行修改，然后再将修改后的文件上传回系统导入。(注意主键列数据勿动，数据里面的下拉字典项、日期格式、管理处名称、路线名称等请严格按照规范填写！)";
          } else {
            this.tipsTextMsg =
              "数据新增：请先下载数据新增导入模版填写好需要新增的数据，然后再将文件上传回系统导入。(注意模版里面的下拉字典项、日期格式、管理处名称、路线名称等请严格按照规范填写！)";
          }
        } else {
          this.tipsTextMsg = newVal;
        }
      },
    },
  },
  created() {
    this.getListPage();
  },
  methods: {
    async setUploadFileUrl() {
      await this.idWorker();
      this.upload.url =
        process.env.VUE_APP_BASE_API +
        `/file/upload?platform=mpkj&ownerId=${this.upload.ownerId}&storagePath=${this.upload.storagePath}`;
    },
    async idWorker() {
      await createIdWorker()
        .then((res) => {
          if (res.code === 200) {
            this.upload.ownerId = res.data;
          } else {
            // 设置一个默认值
            // this.upload.ownerId = uuidv4();
          }
        })
        .catch((error) => {
          // 设置一个默认值
          // this.upload.ownerId = uuidv4();
        });
    },
    async uploadSubmit() {
      // 设置文件上传路径
      await this.setUploadFileUrl();
      // 文件上传
      this.$refs.upload.submit();
    },

    async handleDownload(row) {
      if (this.assetSubclassList.length) {
        if (!this.typeId && !row) {
          this.$modal.msgError("请先选择资产子类");
          return;
        }
      }

      if (row) {
        await this.getFileInfo(row);
        if (!this.dialogUrl) {
          this.$modal.msgError("无文件地址");
          return;
        }
        console.log(this.dialogUrl);
        fetch(this.dialogUrl, { mode: "cors" })
          .then((response) => response.blob())
          .then((res) => {
            if (res) {
              this.$download.saveAs(res, row.importFileName);
            } else {
              this.$modal.msgError("下载失败");
            }
          })
          .catch((error) => {
            console.log(error);
            this.$modal.msgError("请求失败");
          });
        // this.$download.saveAs(this.dialogUrl, row.importFileName);
        return;
      }
      let params = {
        importBaseType: this.importBaseType,
        importType: this.importType === 3 ? 1 : this.importType,
        mainTypeId: this.mainTypeId ? this.mainTypeId : "",
        typeId: this.typeId,
      };
      this.download(
        "/baseData/importRecord/downloadExcelTemplate",
        params,
        row && importFileName
          ? row.importFileName
          : `template_${new Date().getTime()}.xlsx`,
        {
          headers: { "Content-Type": "application/json;" },
          parameterType: "body",
        }
      );
    },
    // 监听上传 状态
    onChange(file, fileList) {
      this.fileName = file.name;
      this.fileList = [file];
      this.updateNumber++;
      if (this.updateNumber % 2 != 0) {
        this.handleAdd();
      }
    },
    // 监听上传成功
    handleSuccess(response, file, fileList) {
      if (response.code != 200) {
        this.$modal.msgError(response.msg || "文件上传失败");
        return;
      }
    },
    //监听文件上传失败
    handleError(err, file, fileList) {
      console.log("上传失败");
      console.log(err);
      console.log(file);
      console.log(fileList);
    },
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id);
      // this.getListPage()
    },
    // 表格点击勾选
    handleRowClick(row) {
      row.isSelected = !row.isSelected;
      this.$refs.table.toggleRowSelection(row);
    },
    // 勾选高亮
    rowStyle({ row, rowIndex }) {
      if (this.ids.includes(row.id)) {
        return { "background-color": "#b7daff" };
      } else {
        return { "background-color": "#fff" };
      }
    },
    async handleAdd() {
      if (!this.fileName) {
        this.$message.warning("请先选择文件！");
        return;
      }
      await this.uploadSubmit();

      this.form.importFileName = this.fileName;
      this.form.importFileId = this.upload.ownerId;
      this.form.importBaseType = this.importBaseType;
      this.form.importType = this.importType;

      await add(this.form).then((data) => {
        if (data && data.code === 200) {
          this.$modal.msgSuccess("添加成功");
        } else {
          this.$modal.msgError(data.msg);
        }
        this.getListPage();

        this.form = {};
      });
    },
    async handleRemove() {
      // let flag = true
      // this.tableData.map(el => {
      //   this.ids.map(e => {
      //     if (el.uid === e) {
      //       if (el.status !== 'ready') {
      //         flag = false
      //         this.$message.warning(`文件名：${el.name}已录入，无法删除！`)
      //         return
      //       }
      //     }
      //   })
      // })
      // if (flag) {
      //   this.tableData = this.tableData.filter(
      //       item => !this.ids.includes(item.uid)
      //   )
      // }

      this.fileList = [];
    },

    async deleteByIds(id) {
      if (id) {
        this.ids.push(id);
      }

      if (this.ids.length === 0) {
        this.$message.warning("请选择至少一条数据进行删除！");
        return;
      }

      await del(this.ids).then((res) => {
        if (res && res.code === 200) {
          this.$modal.msgSuccess("删除成功");
        } else {
          this.$modal.msgError(res.msg);
        }
        this.getListPage();

        this.ids = [];
      });
    },
    async getListPage() {
      this.queryParams.importBaseType = this.importBaseType;

      this.queryParams.importType = this.importType;
      this.loading = true;
      await getListPage(this.queryParams)
        .then((data) => {
          if (data.code === 200) {
            this.tableData = data.rows;
            this.total = data.total;
          } else {
            this.$message.error(data.msg);
          }
        })
        .finally(() => (this.loading = false));
    },
    async handleImport() {
      if (this.ids.length <= 0) {
        this.$message.warning("请选择至少一条数据进行数据导入！");
        return;
      }
      //await importExecute(this.ids)
      clearInterval(this.refreshInterval);
      await importExecuteWithCheckFlag({ recordIds: this.ids, checkFlag: this.checked }).then(async (res) => {
        if (res.code === 200) {
          this.$message.success(res.data || res.msg);
        } else {
          this.$message.error(res.data || res.msg);
        }
        await this.getListPage();
        // let importingIds = this.tableData
        //    .filter(item => item.importState === '2')
        //    .map(item => item.id);
        // importingIds = [...importingIds,...this.ids];
        // importingIds = [...new Set(importingIds)];
        this.loading = false;
        // this.ids = [];
        // 启动定时刷新
        this.refreshInterval = setInterval(async () => {
          let importingIds = this.tableData
           .filter(item => item.importState === '2')
           .map(item => item.id);
          const updatedRecords = [];
          for (const id of importingIds) {
            try {
              const res = await getListPage({ id: id });
              if (res.code === 200) {
                updatedRecords.push(res.rows[0]);
                // if (res.rows[0].importState !== '2') {
                //   importingIds.splice(importingIds.indexOf(id), 1); // 从 importingIds 中移除已完成的 id
                // }
              }
            } catch (error) {
              console.error(`获取导入记录 ${id} 失败`, error);
            }
          }
          // 通过 index 修改 this.tableData
          updatedRecords.forEach(updatedRecord => {
            const index = this.tableData.findIndex(item => item.id === updatedRecord.id);
            if (index !== -1) {
              this.tableData.splice(index, 1, updatedRecord);
            }
          });
          this.$forceUpdate();
          // 检查是否所有选中记录都不再是导入中状态
          const allCompleted = updatedRecords.every(item => item.importState !== '2');
          if (allCompleted) {
            clearInterval(this.refreshInterval); // 停止定时刷新
          }
        }, 3000); // 每1秒刷新一次
      });
    },
    handleClose() {
      this.$modal
        .confirm("确认退出？")
        .then(() => {
          this.tipsText = "";
          this.$emit("close", this.flag);
        })
        .catch(() => {});
    },
    async handleOfficePreview(row) {
      this.officePreviewVisible = true;
      this.ownerId = row.importFileId;
      // this.getFileInfo(row);
    },

    async getFileInfo(row) {
      await getFile({ ownerId: row.importFileId }).then((res) => {
        if (res.code === 200 && res.data) {
          this.dialogUrl = res.data.url;
          this.contentType =
            res.data.contentType ||
            "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet";
        }
      });
    },

    async logDelt(row) {
      this.viewLogVisible = true;
      if (!row.id) {
        return;
      }
      await getInfoById({ id: row.id }).then((res) => {
        if (res.code === 200 && res.data) {
          const msg = res.data.importMsg;
          if (
            msg &&
            typeof msg == "string" &&
            msg.includes("[") &&
            msg.includes("]")
          ) {
            this.logoText = JSON.parse(msg).join(";\n");
          } else {
            this.logoText = msg;
          }
        }
      });
    },
    async downloadErrFile(row) {
      // if (!row.id) {
      //     return;
      // }
      let rows = JSON.parse(JSON.stringify(row));
      rows.importFileId = rows.importErrPath;
      this.handleDownload(rows);
      // this.download(
      //   '/baseData/importRecord/downloadErrFile',
      //   {id: row.id}
    },
    onCheckBoxChange(e) {
      if(this.isAdmin) {
        if(!e) {
          this.$confirm('跳过参数校验可能会导致数据完整性缺失，请谨慎使用!', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }).then(() => { }).catch(() => {
            this.checked = true;
          });
        }
      }else { // 若果不是管理员，只能选择不开启校验
        this.checked = true;
      }
    },
  },
};
</script>

<style lang="scss">
// .custom-dialog {
//   .el-dialog__body {
//     padding: 0 10px;
//   }
// }

.showText {
  width: 100%;
  position: relative;
  bottom: 12px;
  color: red;
}

.logo-content {
  white-space: pre-wrap; /* 保留空白符，自动换行 */
  word-wrap: break-word; /* 长单词或URL内部进行换行 */
}
</style>
<style lang="scss" scoped>
// @import '~@/assets/styles/element-ui-global.scss';
</style>
