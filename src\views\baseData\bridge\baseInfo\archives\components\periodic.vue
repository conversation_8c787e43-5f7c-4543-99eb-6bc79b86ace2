<template>
  <div class="right-box" :class="oneMap ? 'one-map' : ''">
    <div class="right-box-head">
      <h5>定期检查</h5>
    </div>
    <div
      style="height: calc(100% - 53px); width: 100%"
      class="container-view-list"
    >
      <el-table
        ref="table"
        height="100%"
        :header-cell-style="{ height: '36px' }"
        border
        v-loading="loading"
        :data="staticList"
      >
        <el-table-column label="序号" type="index" width="50" align="center">
          <template v-slot="scope">
            {{
              scope.$index +
              (queryParams.pageNum - 1) * queryParams.pageSize +
              1
            }}
          </template>
        </el-table-column>
        <el-table-column
          label="桥梁名称"
          align="center"
          prop="bridgeName"
          min-width="140"
          show-overflow-tooltip
        />
        <el-table-column
          label="桥梁编码"
          align="center"
          prop="bridgeCode"
          min-width="140"
          show-overflow-tooltip
        />

        <el-table-column
          label="桥梁固定编码"
          align="center"
          prop="fixedCode"
          min-width="140"
          show-overflow-tooltip
        />
        <el-table-column
          label="路线名称"
          align="center"
          prop="routeName"
          min-width="140"
          show-overflow-tooltip
        />
        <el-table-column
          label="管理处"
          align="center"
          prop="managementMaintenanceName"
          min-width="140"
          show-overflow-tooltip
        />
        <el-table-column
          label="管养分处"
          align="center"
          prop="managementMaintenanceBranchName"
          min-width="140"
          show-overflow-tooltip
        />
        <el-table-column
          label="桥长(m)"
          align="center"
          prop="totalLength"
          min-width="140"
          show-overflow-tooltip
        />
        <el-table-column
          label="统一里程桩号"
          align="center"
          prop="unifiedMileageStake"
          min-width="140"
          show-overflow-tooltip
        />

        <el-table-column
          label="养护路段"
          align="center"
          prop="maintenanceSectionName"
          min-width="140"
          show-overflow-tooltip
        />

        <el-table-column
          label="年度"
          align="center"
          prop="checkYear"
          min-width="140"
          show-overflow-tooltip
        />
        <el-table-column
          label="检查时间"
          align="center"
          prop="checkDate"
          min-width="140"
          show-overflow-tooltip
        />
        <el-table-column
          label="功能类型"
          align="center"
          prop="functionType"
          min-width="140"
          show-overflow-tooltip
        >
          <template slot-scope="scope">
            <span v-if="scope.row && scope.row.functionType">
              <DictTag
                :value="scope.row.functionType"
                :options="dict.type.bridge_function_type"
              />
            </span>
          </template>
        </el-table-column>
        <el-table-column
          label="定期检查评定等级"
          align="center"
          prop="level"
          min-width="140"
          show-overflow-tooltip
        >
          <template slot-scope="scope">
            <span v-if="scope.row && scope.row.level">
              <DictTag
                :value="scope.row.level"
                :options="dict.type.bridge_tec_condition_level"
              />
            </span>
          </template>
        </el-table-column>
        <el-table-column
          label="结构形式"
          align="center"
          prop="mainSuperstructureType"
          min-width="140"
          show-overflow-tooltip
        >
          <template slot-scope="scope">
            <span v-if="scope.row && scope.row.mainSuperstructureType">
              <DictTag
                :value="scope.row.mainSuperstructureType"
                :options="dict.type.bridge_main_superstructure_type"
              />
            </span>
          </template>
        </el-table-column>

        <el-table-column
          label="定期检查承担单位"
          align="center"
          prop="organization"
          min-width="140"
          show-overflow-tooltip
        />
        <el-table-column
          label="检查报告"
          align="center"
          prop="reportPath"
          min-width="140"
          show-overflow-tooltip
        >
          <template slot-scope="scope">
            <el-button
              v-if="scope.row.reportPath && scope.row.originalFilename"
              type="text"
              icon="el-icon-paperclip"
              @click.stop="handlePreview(scope.row)"
              >{{ scope.row.reportName }}</el-button
            >
            <span v-else></span>
          </template>
        </el-table-column>

        <el-table-column
          label="路线编码"
          align="center"
          prop="routeCode"
          min-width="140"
          show-overflow-tooltip
        />

        <el-table-column
          label="跨径组合"
          align="center"
          prop="spanGroups"
          min-width="140"
          show-overflow-tooltip
        />
      </el-table>
      <FilePreview :owner-id="ownerId" :office-preview-visible="officePreviewVisible" @close="officePreviewVisible=false"/>

      <pagination
        :total="total"
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
        :pageSizes="[10, 20, 30, 50, 100, 1000]"
        @pagination="getList"
      />
    </div>
  </div>
  <!-- <PageContainer
    :ifSearch="false"
    :ifHeader="false"
  >
    <template slot="body">

    </template>

  </PageContainer> -->
</template>

<script>
import { listBridgePeriodicRecords } from "@/api/baseData/bridge/periodic/index";

import { getToken } from "@/utils/auth";
import CascadeSelection from "@/components/CascadeSelection/index.vue";
import { getFile } from "@/api/file";
import FilePreview from "@/components/FilePreview/index.vue";

import ImportData from "@/views/baseData/components/importData/index.vue";

export default {
  name: "periodic-baseInfo",
  components: { ImportData, CascadeSelection ,FilePreview},
  inject: ["oneMap"],
  dicts: [
    "base_data_yes_no",
    "bridge_tec_condition_level",
    "bridge_main_superstructure_type",
    "bridge_function_type",
  ],
  props: {
    id: {
      type: String || Number,
      default: "",
    },
  },
  data() {
    return {
      loading: true,
      ids: [],
      single: true,
      multiple: true,

      showImportAdd: false,
      total: 0,
      staticList: null,
      routeCode: [],
      queryParams: {
        pageNum: 1,
        pageSize: 10,
      },
      dateRange: [],
      officePreviewVisible:false,
      ownerId:''
    };
  },
  watch: {},
  created() {
    this.getList();
  },
  methods: {
    // 获取表格数据
    getList() {
      this.loading = true;

      listBridgePeriodicRecords({
        bridgeId: this.id,
        ...this.queryParams,
      }).then(async (response) => {
        this.staticList = response.rows;
        this.total = response.total;
        this.loading = false;

        let dataList = JSON.parse(JSON.stringify(response.rows));
        for (let index = 0; index < dataList.length; index++) {
          const el = dataList[index];

          if (el.reportPath == parseInt(el.reportPath)) {
            const r = await getFile({ ownerId: el.reportPath });
            if (r.code === 200) {
              el.originalFilename = r.data?.originalFilename;
              el.fileUrl = r.data?.url;
            }
          }
        }
        this.staticList = dataList;
      });
    },
    async handlePreview(row) {
      this.officePreviewVisible=true;
      this.ownerId=row.reportPath;
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id);
      this.single = selection.length != 1;
      this.multiple = !selection.length;
    },
    // 勾选高亮
    rowStyle({ row, rowIndex }) {
      if (this.ids.includes(row.id)) {
        return { "background-color": "#b7daff", color: "#333" };
      } else {
        return { "background-color": "#fff", color: "#333" };
      }
    },
    // 搜索按钮
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    // 重置按钮
    resetQuery() {
      this.routeCode = [];

      this.dateRange = [];
      this.queryParams = {};
      this.handleQuery();
    },

    handleDateChange() {
      this.queryParams.constructionStartDate = this.dateRange[0];
      this.queryParams.constructionEndDate = this.dateRange[1];
    },
    // 表格点击勾选
    handleRowClick(row) {
      row.isSelected = !row.isSelected;
      this.$refs.table.toggleRowSelection(row);
    },
  },
};
</script>

<style lang="scss" scoped>
@import "@/assets/styles/common.scss";
::v-deep {
  .el-table__row:hover td {
    background: #b7daff !important;
  }
}
.container-view-list {
  // padding: 0;
}
.right-box {
  width: 100%;
  height: 100%;
  background-color: #fff;
  border-radius: 10px;
  box-shadow: 0px 0px 10px 0px rgba(0, 0, 0, 0.2);
  position: relative;
  .right-box-head {
    width: 100%;
    height: 52px;
    background: #409eff;
    border-radius: 10px 10px 0 0;
    display: flex;
    justify-content: center;
    align-items: center;
    h5 {
      font-family: Microsoft YaHei, Microsoft YaHei;
      font-weight: 700;
      font-size: 14px;
      color: #ffffff;
    }
  }
}
</style>
