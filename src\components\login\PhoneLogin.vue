<template>
  <div class="phone-login">
    <el-form ref="phoneForm" :model="phoneForm" :rules="phoneRules">
      <el-form-item prop="phone">
        <el-input
          v-model="phoneForm.phone"
          type="text"
          autocomplete="off"
          placeholder="请输入手机号"
          prefix-icon="el-icon-phone"
        >
        </el-input>
      </el-form-item>

      <!-- 添加系统验证码 -->
<!--      <el-form-item prop="captcha">-->
<!--        <el-input-->
<!--          v-model="phoneForm.captcha"-->
<!--          autocomplete="off"-->
<!--          placeholder="请输入验证码"-->
<!--          style="width: 63%"-->
<!--        >-->
<!--          <img slot="prefix" src="../assets/login/code.png" alt="" style="width: 15px;height: 15px;margin-top: 12px;"/>-->
<!--        </el-input>-->
<!--        <div class="login-code">-->
<!--          <img :src="codeUrl" @click="getCode" class="login-code-img"/>-->
<!--        </div>-->
<!--      </el-form-item>-->

      <el-form-item prop="smsCode">
        <el-input
          v-model="phoneForm.smsCode"
          autocomplete="off"
          placeholder="请输入短信验证码"
          style="width: 63%"
          @keyup.enter.native="handlePhoneLogin"
        >
          <img slot="prefix" src="@/assets/login/code.png" alt="" style="width: 15px;height: 15px;margin-top: 12px;"/>
        </el-input>
        <el-button
          class="sms-code-btn"
          :disabled="smsCodeTimer > 0"
          @click="sendSmsCode">
          {{ smsCodeTimer > 0 ? `${smsCodeTimer}秒后重新获取` : '获取验证码' }}
        </el-button>
      </el-form-item>

      <el-form-item style="width:100%;">
        <el-button
          :loading="loading"
          size="medium"
          type="primary"
          style="width:100%;opacity: 0.7;"
          @click.native.prevent="handlePhoneLogin"
        >
          <span v-if="!loading">登 录</span>
          <span v-else>登 录 中...</span>
        </el-button>
      </el-form-item>
    </el-form>

    <!-- 添加账号选择对话框 -->
    <el-dialog
      title="选择登录账号"
      :visible.sync="dialogVisible"
      width="400px"
      :show-close="false"
    >
      <el-radio-group v-model="selectedAccount" class="account-list">
        <el-radio
          v-for="account in accountList"
          :key="account.userName"
          :label="account.userName"
          class="account-item"
        >
          {{ account.userName }} - {{ account.nickName }}
        </el-radio>
      </el-radio-group>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="handleAccountSelect" :disabled="!selectedAccount">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { getCodeImg, sendCode } from '@/api/login'
import {decrypt as AesDecrypt} from '@/utils/aesutil'
import { getHomeUrl } from '@/utils/auth'

export default {
  name: 'PhoneLogin',
  data() {
    const validatePhone = (rule, value, callback) => {
      if (!value) {
        callback(new Error('请输入手机号'));
      } else if (!/^1[3-9]\d{9}$/.test(value)) {
        callback(new Error('请输入正确的手机号'));
      } else {
        callback();
      }
    };

    return {
      loading: false,
      smsCodeTimer: 0,
      codeUrl: "", // 系统验证码图片
      phoneForm: {
        phone: '',
        // captcha: '', // 系统验证码
        smsCode: '', // 短信验证码
        uuid: ''    // 验证码唯一标识
      },
      phoneRules: {
        phone: [
          { required: true, trigger: "blur", validator: validatePhone }
        ],
        // captcha: [
        //   { required: true, trigger: "blur", message: "请输入验证码" }
        // ],
        smsCode: [
          { required: true, trigger: "blur", message: "请输入短信验证码" }
        ]
      },
      dialogVisible: false,
      accountList: [],
      selectedAccount: '',
      timer: null, // 保存定时器引用
    };
  },
  created() {
    // this.getCode();
  },
  methods: {
    // 获取系统验证码
    // getCode() {
    //   getCodeImg().then(res => {
    //     let img = AesDecrypt(res.img);
    //     this.codeUrl = "data:image/gif;base64," + img;
    //     this.phoneForm.uuid = res.uuid;
    //   });
    // },

    // 发送短信验证码
		sendSmsCode() {
			this.$refs.phoneForm.validateField('phone', (valid) => {
				if (!valid) {
					sendCode(this.phoneForm.phone).then(() => {
						this.startSmsCodeTimer();
						this.$message.success('验证码已发送');
					});
				}
			});
		},

    // 手机号登录
    handlePhoneLogin() {
      this.$refs.phoneForm.validate(valid => {
        if (valid) {
          this.loading = true;
          // 调用手机号登录接口
          this.$store.dispatch("LoginByPhone", this.phoneForm).then((res) => {
            if (Array.isArray(res) && res.length > 1) {
              // 返回多个账号，显示选择对话框
              this.accountList = res;
              this.selectedAccount = '';
              this.dialogVisible = true;
              this.loading = false;
            } else {
              // 直接登录成功
              this.$emit('login-success');
            }
          }).catch(() => {
            this.loading = false;
            this.getCode();
          });
        }
      });
    },



    // 处理账号选择
    handleAccountSelect() {
      if (!this.selectedAccount) return;

      this.loading = true;
      this.$store.dispatch("LoginByUser", {
        username: this.selectedAccount
      }).then(() => {
        this.dialogVisible = false;
        this.$emit('login-success');
      }).catch(() => {
        this.loading = false;
      });
    },

    // 开始验证码倒计时
    startSmsCodeTimer() {
      this.smsCodeTimer = 60;
      // 清除可能存在的旧定时器
      if (this.timer) {
        clearInterval(this.timer);
      }
      // 保存定时器引用
      this.timer = setInterval(() => {
        this.smsCodeTimer--;
        if (this.smsCodeTimer <= 0) {
          clearInterval(this.timer);
          this.timer = null;
        }
      }, 1000);
    },
  },
  beforeDestroy() {
    // 组件销毁前清除定时器
    if (this.timer) {
      clearInterval(this.timer);
    }
  }
};
</script>

<style lang="scss" scoped>
.phone-login {
  width: 100%;
  flex: 1;  // 填充剩余空间
  display: flex;  // 使用flex布局

  .el-form {
    width: 100%;  // 表单占满容器宽度
    display: flex;
    flex-direction: column;
  }

  .el-form-item {
    padding: 0 25px;
    margin-bottom: 22px;  // 增加表单项间距，使布局更均匀

    &:last-child {
      margin-top: auto;  // 将登录按钮推到底部
      margin-bottom: 25px;  // 与底部保持一定距离
    }
  }
}

.login-code {
  width: 33%;
  height: 38px;
  float: right;
  img {
    cursor: pointer;
    vertical-align: middle;
    height: 38px;
  }
}

.sms-code-btn {
  width: 35%;
  height: 38px;
  margin-left: 2%;
  padding: 0;
  font-size: 12px;
}

.account-list {
  display: flex;
  flex-direction: column;
  width: 100%;

  .account-item {
    margin: 10px 0;
    padding: 10px;
    border-radius: 4px;

    &:hover {
      background-color: #f5f7fa;
    }
  }
}

.el-dialog__body {
  padding: 20px;
}
</style>
