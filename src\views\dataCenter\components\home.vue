<template>
  <div class="home-container" :style="{ overflowY: isBig ? 'hidden' : 'auto' }" ref="homeRef">
    <div class="home-header" v-if="!isBig">
      <cockpit-header v-on="$listeners"></cockpit-header>
    </div>
    <div class="home-main" :style="mainStyle">
      <div class="page-l">
        <page-l @click="onLClick"></page-l>
      </div>
      <div class="page-r">
        <page-r v-if="!componentUrl"></page-r>
        <component :is="componentUrl" @back="componentUrl = ''"></component>
      </div>
    </div>
  </div>
</template>

<script>
// 组件
import { isBigScreen } from '../util/utils';
import CockpitHeader from './cockpitHeader.vue';
import PageR from './pageR.vue';
import PageL from './pageL.vue';

import RouteDetail from './route/route.vue'; // 路网
import Maintenance from './maintenance/index.vue'; // 日常养护
import SpecialIndex from './maintenance/specialIndex.vue';

export default {
  name: 'Home',
  components: {
    CockpitHeader,
    PageL,
    PageR,
    RouteDetail,
    Maintenance,
    SpecialIndex,
  },
  provide() {
    return {
      sub: this,
    };
  },
  data() {
    return {
      isBig: isBigScreen(),
      height: window.innerHeight || window.screen.height || window.screen.availHeight || 1080,
      componentUrl: null,
    }
  },
  computed: {
    mainStyle() {
      let style = {};
      if (this.isBig) {
        style = {
          // height: 'calc(100vh - 150px)',
          height: 'calc(100vh - 0px)',
          overflow: 'hidden',
          paddingTop: '20px',
        }
      } else {
        style = {
          // height: this.height * 2 - 135 + 'px',
          height: 'calc(200vh - 135px)',
          overflowY: 'auto',
          flexDirection: 'column',
        }
      }
      return style;
    }
  },
  methods: {
    onLClick(obj) {
      if (!obj.componentL && obj.componentR) {
        this.componentUrl = obj.componentR;
        this.$nextTick(() => {
          if (this.$refs.homeRef) {
            this.$refs.homeRef.scrollTo({
              top: this.$refs.homeRef.scrollHeight,
              behavior: 'smooth'
            });
          }
        });
      } else {
        this.$emit('clickL', obj);
      }
    }
  },
  unmounted() { },
}
</script>

<style lang="scss" scoped>
@import "@/assets/styles/utils.scss";

.home-container {
  width: 100%;
  height: 100%;
  overflow-x: hidden;

  .home-header {
    width: 100%;
    height: vwpx(156px);
    z-index: 99;
  }

  .home-main {
    width: 100%;
    display: flex;
    overflow-y: auto;
    background-color: rgba(9, 25, 45, 1);

    .page-l {
      flex: 1;
      background-image: url('~@/assets/cockpit/cockpit-bg.png');
      background-repeat: no-repeat;
      background-size: 100% 100%;
      // background-color: rgba(9, 25, 45, 1);
      padding: 0 vwpx(20px);
    }

    .page-r {
      flex: 1;
      // background-image: url('~@/assets/cockpit/cockpit-bg.png');
      // background-repeat: no-repeat;
      // background-size: 100% 100%;
      background-color: rgba(9, 25, 45, 1);
      padding: 0 vwpx(20px);
    }
  }

}
</style>