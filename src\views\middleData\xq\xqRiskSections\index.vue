<template>
  <div class="app-container">
    <el-row :gutter="20">

      <!--筛选区开始-->
      <el-col :span="24" :xs="24">
        <el-row>
          <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" label-width="68px">
            <el-col :span="24" style="display: flex;">
              <el-form-item label="">
                <CascadeSelection style="min-width: 192px" :form-data="queryParams" v-model="queryParams"
                  @update:fromData="() => { }" types="201" multiple />
              </el-form-item>
              <el-form-item label="" prop="roadNumber">
                <el-input v-model="queryParams.roadNumber" placeholder="请输入公路编号" clearable
                  prefix-icon="el-icon-location" style="width: 240px" @keyup.enter.native="handleQuery" size="small" />
              </el-form-item>
              <el-form-item label="" prop="roadName">
                <el-input v-model="queryParams.roadName" placeholder="请输入公路名称" clearable prefix-icon="el-icon-place"
                  style="width: 240px" @keyup.enter.native="handleQuery" size="small" />
              </el-form-item>
              <el-form-item>
                <el-button type="primary" icon="el-icon-search" size="small" @click="handleQuery">搜索</el-button>
                <el-button icon="el-icon-refresh" size="small" @click="resetQuery">重置</el-button>
                <el-button v-show="!showSearch" @click="showSearch = true" icon="el-icon-arrow-down" circle></el-button>
                <el-button v-show="showSearch" @click="showSearch = false" icon="el-icon-arrow-up" circle></el-button>
              </el-form-item>
            </el-col>
            <el-col :span="24" v-show="showSearch">
              <el-form-item label="巡查时间" prop="checkTime">
                <el-date-picker v-model="dateRange" style="width: 240px" value-format="yyyy-MM-dd" type="daterange"
                  range-separator="-" start-placeholder="开始日期" end-placeholder="结束日期" size="small"></el-date-picker>
              </el-form-item>
              <el-form-item label="" prop="riskLevel">
                <el-select v-model="queryParams.riskLevel" placeholder="风险等级" clearable style="width: 240px"
                  size="small">
                  <el-option v-for="dict in dict.type.geological_points_risk_grade" :key="dict.value"
                    :label="dict.label" :value="parseInt(dict.value)"></el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="" prop="roadType">
                <el-select v-model="queryParams.roadType" placeholder="公路类型" clearable style="width: 240px"
                  size="small">
                  <el-option v-for="dict in roadTypeOptions" :key="dict.dictValue" :label="dict.dictLabel"
                    :value="dict.dictValue" />
                </el-select>
              </el-form-item>
              <el-form-item label="" prop="measuresTaken">
                <el-select v-model="queryParams.measuresTaken" placeholder="是否采取措施" clearable style="width: 240px"
                  size="small">
                  <el-option v-for="dict in measuresOptions" :key="dict.dictValue" :label="dict.dictLabel"
                    :value="dict.dictValue" />
                </el-select>
              </el-form-item>
            </el-col>
          </el-form>
        </el-row>
        <!--筛选区结束-->


        <!--操作按钮区开始-->
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button type="primary" icon="el-icon-plus" size="mini" @click="handleAdd"
              v-hasPermi="['middleData:xqRiskSections:add']">新增</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="success" icon="el-icon-edit" size="mini" :disabled="single" @click="handleUpdate"
              v-hasPermi="['middleData:xqRiskSections:edit']">修改</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="danger" icon="el-icon-delete" size="mini" :disabled="multiple" @click="handleDelete"
              v-hasPermi="['middleData:xqRiskSections:remove']">删除</el-button>
          </el-col>
          <!-- <el-col :span="1.5">
            <el-button
                type="info"
                plain
                icon="el-icon-upload2"
                size="mini"
                @click="handleImport"
                v-hasPermi="['middleData:xqRiskSections:export']"
            >导入</el-button>
          </el-col> -->
          <el-col :span="1.5">
            <el-button type="warning" icon="el-icon-download" size="mini" @click="handleExport"
              v-hasPermi="['middleData:xqRiskSections:export']">导出</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="primary" plain icon="el-icon-check" size="mini" :disabled="multiple"
              @click="handleBatchAudit" v-hasPermi="['middleData:xqRiskSections:audit']">批量审核</el-button>
          </el-col>
          <right-toolbar :showSearch.sync="showSearch" @queryTable="getList" :columns="columns"></right-toolbar>
        </el-row>
        <!--操作按钮区结束-->

        <!--数据表格开始-->
        <div class="tableDiv">
          <el-table v-adjust-table ref="table" size="mini"
            :height="showSearch ? 'calc(100vh - 320px)' : 'calc(100vh - 260px)'" style="width: 100%" v-loading="loading"
            border :data="riskSectionsList" @selection-change="handleSelectionChange" :row-style="rowStyle"
            @row-click="handleRowClick" :header-cell-style="{ 'text-align': 'center' }"
            :cell-style="{ 'text-align': 'center' }">
            <el-table-column type="selection" width="50" align="center" fixed />
            <el-table-column fixed label="序号" type="index" width="50">
              <template v-slot="scope">
                {{ (scope.$index + (queryParams.pageNum - 1) * queryParams.pageSize) + 1 }}
              </template>
            </el-table-column>
            <el-table-column label="省份" align="center" prop="provinceName" width="80" show-overflow-tooltip
              v-if="cmpColumns('省份')" />
            <el-table-column label="市/州" align="center" prop="cityName" width="80" show-overflow-tooltip
              v-if="cmpColumns('市/州')" />
            <el-table-column label="管理处" align="center" prop="managementOffice" width="120" show-overflow-tooltip
              v-if="cmpColumns('管理处')" />
            <el-table-column label="分处" align="center" prop="subOffice" width="120" show-overflow-tooltip
              v-if="cmpColumns('分处')" />
            <el-table-column label="养护路段" align="center" prop="maintenanceSection" width="120" show-overflow-tooltip
              v-if="cmpColumns('养护路段')" />
            <el-table-column label="公路类型" align="center" prop="roadType" width="100" show-overflow-tooltip
              v-if="cmpColumns('公路类型')">
              <template slot-scope="scope">
                <span>{{ formatRoadType(scope.row.roadType) }}</span>
              </template>
            </el-table-column>
            <el-table-column label="公路编号" align="center" prop="roadNumber" width="100" show-overflow-tooltip
              v-if="cmpColumns('公路编号')" />
            <el-table-column label="公路名称" align="center" prop="roadName" width="120" show-overflow-tooltip
              v-if="cmpColumns('公路名称')" />
            <el-table-column label="起点桩号" align="center" prop="startStake" width="100" show-overflow-tooltip
              v-if="cmpColumns('起点桩号')">
              <template slot-scope="scope">
                <span>{{ formatStakeNumber(scope.row.startStake) }}</span>
              </template>
            </el-table-column>
            <el-table-column label="止点桩号" align="center" prop="endStake" width="100" show-overflow-tooltip
              v-if="cmpColumns('止点桩号')">
              <template slot-scope="scope">
                <span>{{ formatStakeNumber(scope.row.endStake) }}</span>
              </template>
            </el-table-column>
            <el-table-column label="长度(km)" align="center" prop="length" width="100" show-overflow-tooltip
              v-if="cmpColumns('长度(km)')" />
            <el-table-column label="风险等级" align="center" prop="riskLevel" width="100" show-overflow-tooltip
              v-if="cmpColumns('风险等级')">
              <template v-slot="scope">
                <dict-tag :options="dict.type.geological_points_risk_grade" :value="scope.row.riskLevel" />
              </template>
            </el-table-column>
            <el-table-column label="路段风险描述" align="center" prop="riskDescription" show-overflow-tooltip width="150"
              v-if="cmpColumns('路段风险描述')" />
            <el-table-column label="是否已采取措施" align="center" prop="measuresTaken" width="120" show-overflow-tooltip
              v-if="cmpColumns('是否已采取措施')">
              <template slot-scope="scope">
                <span>{{ formatMeasures(scope.row.measuresTaken) }}</span>
              </template>
            </el-table-column>
            <el-table-column label="已(拟)采取的措施" align="center" prop="measuresDetail" show-overflow-tooltip width="150"
              v-if="cmpColumns('已(拟)采取的措施')" />
            <el-table-column label="省级责任单位及人员" align="center" prop="provincialResponsible" width="150"
              show-overflow-tooltip v-if="cmpColumns('省级责任单位及人员')" />
            <el-table-column label="复核责任单位及人员" align="center" prop="reviewResponsible" width="150" show-overflow-tooltip
              v-if="cmpColumns('复核责任单位及人员')" />
            <el-table-column label="排查责任单位及人员" align="center" prop="inspectionResponsible" width="150"
              show-overflow-tooltip v-if="cmpColumns('排查责任单位及人员')" />
            <el-table-column label="备注" align="center" prop="remarks" show-overflow-tooltip width="120"
              v-if="cmpColumns('备注')" />
            <!-- <el-table-column label="巡查人" align="center" prop="checkerName" width="100" show-overflow-tooltip
              v-if="cmpColumns('巡查人')" /> -->
            <el-table-column label="审核人" align="center" prop="auditName" width="100" show-overflow-tooltip
              v-if="cmpColumns('审核人')" />
            <el-table-column label="审核状态" align="center" prop="auditStatus" width="100" show-overflow-tooltip
              v-if="cmpColumns('审核状态')">
              <template slot-scope="scope">
                <span>{{ formatAuditStatus(scope.row.auditStatus) }}</span>
              </template>
            </el-table-column>
            <el-table-column label="审核意见" align="center" prop="auditContent" show-overflow-tooltip width="120"
              v-if="cmpColumns('审核意见')" />
            <!-- <el-table-column label="巡查时间(填报时间)" align="center" prop="checkTime" width="150" show-overflow-tooltip
              v-if="cmpColumns('巡查时间(填报时间)')">
              <template v-slot="scope">
                <span>{{ parseTime(scope.row.checkTime, '{y}-{m}-{d}') }}</span>
              </template>
            </el-table-column> -->
            <el-table-column label="审核时间" align="center" prop="auditTime" width="120" show-overflow-tooltip
              v-if="cmpColumns('审核时间')">
              <template v-slot="scope">
                <span>{{ parseTime(scope.row.auditTime, '{y}-{m}-{d}') }}</span>
              </template>
            </el-table-column>
            <el-table-column label="操作" fixed="right" align="center" width="160" class-name="small-padding fixed-width">
              <template slot-scope="scope" v-if="scope.row.userId !== 1">
                <el-button size="mini" type="text" icon="el-icon-edit" @click="handleUpdate(scope.row)"
                  v-hasPermi="['middleData:xqRiskSections:edit']">修改</el-button>
                <el-button size="mini" type="text" icon="el-icon-delete" @click="handleDelete(scope.row)"
                  v-hasPermi="['middleData:xqRiskSections:remove']">删除</el-button>
                <el-button size="mini" type="text" icon="el-icon-check" @click="handleAudit(scope.row)"
                  v-hasPermi="['middleData:xqRiskSections:audit']">审核</el-button>
              </template>
            </el-table-column>
          </el-table>

          <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum"
            :limit.sync="queryParams.pageSize" @pagination="getList" />
        </div>
        <!--数据表格结束-->
      </el-col>
    </el-row>

    <!-- 添加或修改灾害风险路段对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="70%" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="140px">
        <div class="infoBox" style="margin-bottom: 20px;">
          <div class="infoTitle">基本信息</div>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="省份" prop="provinceName">
                <el-input v-model="form.provinceName" placeholder="请输入省份" disabled />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="市/州" prop="cityName">
                <el-select v-model="form.cityName" placeholder="请选择市/州" style="width: 100%;">
                  <el-option v-for="item in cityList" :key="item" :label="item" :value="item">
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="管理处" prop="managementMaintenanceBranchId">
                <el-select v-model="form.managementMaintenanceBranchId" clearable ref="formSelectTree"
                  placeholder="请输入管理处" @clear="formSelectClear" @remove-tag="formRemoveTag" style="width: 100%">
                  <el-option hidden v-for="(item, index) in formatData(deptOptions)"
                    :key="item.label + item.value + '-' + index" :label="item.label" :value="item.value">
                  </el-option>
                  <div @click.stop>
                    <el-tree style="font-weight: 400" :data="deptOptions" :props="defaultProps"
                      @check="formHandleNodeCheck" @node-click="formNodeClick" node-key="id" check-on-click-node
                      ref="formTreeRef" @click.native.stop="handleTreeClick">
                    </el-tree>
                  </div>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="养护路段" prop="maintenanceSectionId">
                <el-select v-model="form.maintenanceSectionId" placeholder="请选择养护路段" clearable filterable
                  @change="formMaintenanceSectionChange" style="width: 100%">
                  <el-option v-for="item in routeOptions" :key="item.maintenanceSectionId"
                    :label="item.maintenanceSectionName" :value="item.maintenanceSectionId">
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="起点桩号" prop="startStake">
                <div style="width: 100%; display: flex; flex-direction: row; flex-wrap: nowrap;">
                  <el-tag type="info" effect="plain" size="medium">K</el-tag>
                  <el-input v-model="startStakeForm.k" controls-position="right" style="width: 50%;" placeholder="千位以上"
                    @input="handleStartStakeInput" @change="handleStartStakeChange" />
                  <el-tag type="info" effect="plain" size="medium">+</el-tag>
                  <el-input v-model="startStakeForm.m" controls-position="right" style="width: 50%;"
                    placeholder="千位以下(三位)" @input="handleStartStakeInput" @change="handleStartStakeChange" />
                </div>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="止点桩号" prop="endStake">
                <div style="width: 100%; display: flex; flex-direction: row; flex-wrap: nowrap;">
                  <el-tag type="info" effect="plain" size="medium">K</el-tag>
                  <el-input v-model="endStakeForm.k" controls-position="right" style="width: 50%;" placeholder="千位以上"
                    @input="handleEndStakeInput" @change="handleEndStakeChange" />
                  <el-tag type="info" effect="plain" size="medium">+</el-tag>
                  <el-input v-model="endStakeForm.m" controls-position="right" style="width: 50%;"
                    placeholder="千位以下(三位)" @input="handleEndStakeInput" @change="handleEndStakeChange" />
                </div>
              </el-form-item>
            </el-col>
          </el-row>
        </div>

        <div class="infoBox" style="margin-bottom: 20px;">
          <div class="infoTitle">风险信息</div>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="风险等级" prop="riskLevel">
                <el-select v-model="form.riskLevel" placeholder="请选择风险等级" style="width: 100%">
                  <el-option v-for="dict in dict.type.geological_points_risk_grade" :key="dict.value"
                    :label="dict.label" :value="parseInt(dict.value)"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="24">
              <el-form-item label="风险描述" prop="riskDescription">
                <el-input v-model="form.riskDescription" type="textarea" :rows="3" placeholder="请输入内容" />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="经度" prop="longitude">
                <el-input v-model="form.longitude" placeholder="请输入经度">
                  <el-button slot="append" icon="el-icon-location"
                    @click="showCoordinatePicker('longitude')">坐标拾取</el-button>
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="纬度" prop="latitude">
                <el-input v-model="form.latitude" placeholder="请输入纬度">
                  <el-button slot="append" icon="el-icon-location"
                    @click="showCoordinatePicker('latitude')">坐标拾取</el-button>
                </el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="12">
              <el-form-item label="是否已采取措施" prop="measuresTaken">
                <el-radio-group v-model="form.measuresTaken">
                  <el-radio v-for="item in measuresOptions" :label="item.dictValue">{{ item.dictLabel
                  }}</el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>

            <el-col :span="12">
              <el-form-item label="已(拟)采取的措施" prop="measuresDetail">
                <el-input v-model="form.measuresDetail" type="textarea" placeholder="请输入内容" />
              </el-form-item>
            </el-col>
          </el-row>
        </div>

        <div class="infoBox" style="margin-bottom: 20px;">
          <div class="infoTitle">隐患描述</div>
          <el-row :gutter="20">
            <el-col :span="24">
              <el-form-item label="隐患描述" prop="hazardDescription">
                <el-input v-model="form.hazardDescription" type="textarea" :rows="3" placeholder="请输入内容" />
              </el-form-item>
            </el-col>
          </el-row>
        </div>

        <!-- 照片上传 -->
        <div class="infoBox" style="margin-bottom: 20px;">
          <div class="infoTitle">照片上传</div>
          <el-row>
            <el-col :span="24">
              <el-form-item label="照片" prop="photo">
                <FileUpload v-model="form.photo" :fileType="['png', 'jpg', 'jpeg', 'PNG', 'JPG', 'JPEG']" :fileSize="10"
                  :limit="5" :isShowTip="true" />
              </el-form-item>
            </el-col>
          </el-row>
        </div>

        <div class="infoBox" style="margin-bottom: 20px;">
          <div class="infoTitle">责任信息</div>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="省级责任单位及人员" prop="provincialResponsible">
                <el-input v-model="form.provincialResponsible" placeholder="请输入省级责任单位及人员" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="复核责任单位及人员" prop="reviewResponsible">
                <el-input v-model="form.reviewResponsible" placeholder="请输入复核责任单位及人员" />
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="排查责任单位及人员" prop="inspectionResponsible">
                <el-input v-model="form.inspectionResponsible" placeholder="请输入排查责任单位及人员" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="备注" prop="remarks">
                <el-input v-model="form.remarks" type="textarea" :rows="3" placeholder="请输入内容" />
              </el-form-item>
            </el-col>
          </el-row>
        </div>

        <div class="infoBox" style="margin-bottom: 20px;">
          <div class="infoTitle">巡查信息</div>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="巡查人" prop="checkerName">
                <el-input v-model="form.checkerName" placeholder="请输入巡查人" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="巡查时间" prop="checkTime">
                <el-date-picker clearable v-model="form.checkTime" type="date" value-format="yyyy-MM-dd"
                  placeholder="请选择巡查时间" style="width: 100%" />
              </el-form-item>
            </el-col>
          </el-row>
        </div>

        <div class="infoBox" style="margin-bottom: 20px;" v-if="form.id">
          <div class="infoTitle">审核信息</div>
          <el-row :gutter="20">
            <el-col :span="24">
              <el-form-item label="审核意见">
                <el-input v-model="form.auditContent" type="textarea" :rows="3" placeholder="请输入内容" />
              </el-form-item>
            </el-col>
          </el-row>
        </div>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 用户导入对话框 -->
    <el-dialog :title="upload.title" :visible.sync="upload.open" width="400px" append-to-body>
      <el-upload ref="upload" :limit="1" accept=".xlsx, .xls" :headers="upload.headers"
        :action="upload.url + '?updateSupport=' + upload.updateSupport" :disabled="upload.isUploading"
        :on-progress="handleFileUploadProgress" :on-success="handleFileSuccess" :auto-upload="false" drag>
        <i class="el-icon-upload"></i>
        <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
        <div class="el-upload__tip text-center" slot="tip">
          <div class="el-upload__tip" slot="tip">
            <el-checkbox v-model="upload.updateSupport" /> 是否更新已经存在的用户数据
          </div>
          <span>仅允许导入xls、xlsx格式文件。</span>
          <el-link type="primary" :underline="false" style="font-size:12px;vertical-align: baseline;"
            @click="importTemplate">下载模板</el-link>
        </div>
      </el-upload>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitFileForm">确 定</el-button>
        <el-button @click="upload.open = false">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 批量审核对话框 -->
    <audit-dialog :visible.sync="auditDialog.open" :title="auditDialog.title" :ids="ids" @submit="submitAudit" />

    <!-- 坐标拾取组件 -->
    <coordinate-picker :visible.sync="coordinatePickerVisible" :initialPosition="coordinatePickerInitPosition"
      @save="handleCoordinateSave" />
  </div>
</template>

<script>
import { listRiskSections, getRiskSections, delData, addRiskSections, updateRiskSections, batchAudit } from "@/api/middleData/xq/riskSections";
import { getToken } from "@/utils/auth";
import AuditDialog from '@/views/middleData/xq/audit/index.vue';
import FileUpload from '@/components/FileUpload';
import Treeselect from "@riophae/vue-treeselect";
import "@riophae/vue-treeselect/dist/vue-treeselect.css";
import { getRouteList } from '@/api/system/dept';
import { getTreeStruct } from '@/api/tmpl';
import { listByMaintenanceSectionId, getMaintenanceSectionListAll } from '@/api/baseData/common/routeLine';
import { listAllRoute } from '@/api/system/route.js';
import { parseTime } from "@/utils/ruoyi";
import { formatStake, parseStake } from "@/utils/common";
import CascadeSelection from '@/components/CascadeSelectionManagementOffice';
import CoordinatePicker from '@/components/CoordinatePicker';

export default {
  name: "RiskSections",
  dicts: ['geological_points_risk_grade'],
  components: { Treeselect, AuditDialog, FileUpload, CascadeSelection, CoordinatePicker },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: false,
      dictType: [],
      // 日期范围
      dateRange: [],
      // 总条数
      total: 0,
      // 灾害风险路段表格数据
      riskSectionsList: null,
      // 弹出层标题
      title: "",
      // 部门树选项
      deptOptions: [],
      // 是否显示弹出层
      open: false,

      // 表单参数
      form: {},
      // 桩号表单
      startStakeForm: {
        k: '',
        m: ''
      },
      endStakeForm: {
        k: '',
        m: ''
      },
      defaultProps: {
        children: 'children',
        label: 'label',
      },
      // 用户导入参数
      upload: {
        // 是否显示弹出层（用户导入）
        open: false,
        // 弹出层标题（用户导入）
        title: "",
        // 是否禁用上传
        isUploading: false,
        // 是否更新已经存在的用户数据
        updateSupport: 0,
        // 设置上传的请求头部
        headers: { Authorization: "Bearer " + getToken() },
        // 上传的地址
        url: process.env.VUE_APP_BASE_API + "/system/user/importData"
      },
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 50,
        provinceName: "云南省",
        cityName: null,
        managementOffice: null,
        subOffice: null,
        maintenanceSection: null,
        roadType: null,
        roadNumber: null,
        roadName: null,
        startStake: null,
        endStake: null,
        length: null,
        riskLevel: null,
        riskDescription: null,
        measuresTaken: null,
        measuresDetail: null,
        provincialResponsible: null,
        reviewResponsible: null,
        inspectionResponsible: null,
        remarks: null,
        checkerId: null,
        checkerName: null,
        auditId: null,
        auditName: null,
        auditStatus: null,
        auditContent: null,
        beginCheckTime: null, // 开始巡查时间
        endCheckTime: null, // 结束巡查时间
        auditTime: null,
        subOfficeId: null,
        managementOfficeId: null,
        maintenanceSectionId: null
      },
      // 列信息
      columns: [
        { key: 1, label: `省份`, visible: true },
        { key: 2, label: `市/州`, visible: true },
        { key: 3, label: `管理处`, visible: true },
        { key: 4, label: `分处`, visible: true },
        { key: 5, label: `养护路段`, visible: true },
        { key: 6, label: `公路类型`, visible: true },
        { key: 7, label: `公路编号`, visible: true },
        { key: 8, label: `公路名称`, visible: true },
        { key: 9, label: `起点桩号`, visible: true },
        { key: 10, label: `止点桩号`, visible: true },
        { key: 11, label: `长度(km)`, visible: true },
        { key: 12, label: `风险等级`, visible: true },
        { key: 13, label: `路段风险描述`, visible: true },
        { key: 14, label: `是否已采取措施`, visible: true },
        { key: 15, label: `已(拟)采取的措施`, visible: true },
        { key: 16, label: `省级责任单位及人员`, visible: true },
        { key: 17, label: `复核责任单位及人员`, visible: true },
        { key: 18, label: `排查责任单位及人员`, visible: true },
        { key: 19, label: `备注`, visible: true },
        // { key: 20, label: `巡查人`, visible: true },
        { key: 21, label: `审核人`, visible: true },
        { key: 22, label: `审核状态`, visible: true },
        { key: 23, label: `审核意见`, visible: true },
        // { key: 24, label: `巡查时间(填报时间)`, visible: true },
        { key: 25, label: `审核时间`, visible: true },
      ],
      // 表单校验
      rules: {
        provinceName: [{ required: true, message: "省份名称不能为空" }],
        cityName: [{ required: true, message: "城市名称不能为空" }],
        roadType: [{ required: true, message: "公路类型（高速公路或普通公路）不能为空" }],
        startStake: [{ required: true, message: "起点桩号（KXXX+XXX格式）不能为空" }],
        endStake: [{ required: true, message: "止点桩号（KXXX+XXX格式）不能为空" }],
        riskLevel: [{ required: true, message: "风险等级不能为空" }],
        measuresTaken: [{ required: true, message: "是否采取措施不能为空" }],
        auditStatus: [{ required: true, message: "审核状态不能为空" }],
        checkerName: [{ required: true, message: "巡查人不能为空" }],
        checkTime: [{ required: true, message: "巡查时间不能为空" }]
      },
      // 是否采取措施字典
      measuresOptions: [
        { dictLabel: '是', dictValue: 1 },
        { dictLabel: '否', dictValue: 0 },
      ],
      // 审核状态字典
      auditStatusOptions: [
        { dictLabel: '未审核', dictValue: 0 },
        { dictLabel: '审核通过', dictValue: 1 },
        { dictLabel: '审核不通过', dictValue: 2 },
      ],
      // 公路类型字典
      roadTypeOptions: [
        { dictLabel: '高速公路', dictValue: 1 },
        { dictLabel: '普通公路', dictValue: 2 },
      ],
      // 批量审核对话框
      auditDialog: {
        open: false,
        title: '批量审核'
      },
      // 添加新的数据结构
      deptOptions: [], // 管养处数据
      routeOptions: [], // 路段选项
      routeList: [], // 路线列表
      coordinatePickerVisible: false, // 坐标拾取对话框可见性
      coordinatePickerInitPosition: "102.8207599,24.8885797", // 初始坐标
      currentCoordinateType: '', // 当前坐标类型，longitude表示经度，latitude表示纬度
      cityList: [
        '昆明市', '曲靖市', '玉溪市', '保山市', '昭通市', '丽江市', '普洱市', '临沧市',
        '楚雄彝族自治州', '红河哈尼族彝族自治州', '文山壮族苗族自治州',
        '西双版纳傣族自治州', '大理白族自治州', '德宏傣族景颇族自治州',
        '怒江傈僳族自治州', '迪庆藏族自治州'
      ],
    };
  },
  watch: {
    // 根据名称筛选部门树
    'form.startStake': {
      immediate: true,
      handler(newVal) {
        if (newVal) {
          const match = newVal.match(/K(\d+)\+(\d{1,3})/);
          if (match) {
            this.startStakeForm.k = match[1];
            this.startStakeForm.m = match[2];
          }
        }
      }
    },
    'form.endStake': {
      immediate: true,
      handler(newVal) {
        if (newVal) {
          const match = newVal.match(/K(\d+)\+(\d{1,3})/);
          if (match) {
            this.endStakeForm.k = match[1];
            this.endStakeForm.m = match[2];
          }
        }
      }
    }
  },
  created() {
    this.getList();
    this.getDepts();
    // 设置表单数据的默认值
    this.reset();
  },
  computed: {
    cmpColumns() {
      return (val) => {
        let el = this.columns.find(item => item.label === val)
        if (el) {
          return el.visible
        } else {
          return true
        }
      }
    }
  },
  methods: {
    // 格式化桩号显示
    formatStakeNumber(value) {
      return formatStake(value);
    },
    /** 查询用户列表 */
    getList() {
      this.loading = true;
      if (this.dateRange && this.dateRange.length > 0) {
        this.queryParams.beginCheckTime = this.dateRange[0];
        this.queryParams.endCheckTime = this.dateRange[1];
      } else {
        this.queryParams.beginCheckTime = null;
        this.queryParams.endCheckTime = null;
      }
      listRiskSections(this.queryParams).then(response => {
        this.riskSectionsList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    /** 表单重置 */
    reset() {
      this.form = {
        id: null,
        auditStatus: 0,
        provinceCode: 51000000000,
        provinceName: "云南省",
        cityName: "",
        cityCode: null,
        managementOffice: null,
        managementOfficeId: null,
        managementMaintenanceBranchId: null,
        subOffice: null,
        subOfficeId: null,
        maintenanceSection: null,
        maintenanceSectionId: null,
        roadType: null,
        roadNumber: null,
        roadName: null,
        startStake: null,
        endStake: null,
        length: null,
        riskLevel: null,
        riskDescription: null,
        measuresTaken: null,
        measuresDetail: null,
        provincialResponsible: null,
        reviewResponsible: null,
        inspectionResponsible: null,
        remarks: null,
        checkerId: this.$store.state.user.id,
        checkerName: this.$store.state.user.nickName,
        auditId: null,
        auditName: null,
        auditContent: null,
        checkTime: this.parseTime(new Date(), '{y}-{m}-{d}'), // 设置当前日期为默认巡查时间
        auditTime: null,
        file: null,
        fileUrl: null,
      };

      this.$nextTick(() => {
        this.startStakeForm = {
          k: '',
          m: ''
        };
        this.endStakeForm = {
          k: '',
          m: ''
        };

        // 处理treeselect组件的值
        if (this.$refs.formTreeRef) {
          this.$refs.formTreeRef.setCheckedKeys([]);
        }

        // 清空路线数据
        this.routeOptions = [];
        this.routeList = [];
      });
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dateRange = [];
      this.resetForm("queryForm");
      this.queryParams.hazardLevelList = []
      this.queryParams.managementMaintenanceBranchId = null
      this.queryParams.managementMaintenanceBranchIds = null
      this.queryParams.managementMaintenanceIds = null
      this.queryParams.routeCodes = null
      this.queryParams.maintenanceSectionId = null
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id);
      this.single = selection.length != 1;
      this.multiple = !selection.length;
    },
    // 表格点击勾选
    handleRowClick(row) {
      row.isSelected = !row.isSelected;
      this.$refs.table.toggleRowSelection(row);
    },
    // 勾选高亮
    rowStyle({ row, rowIndex }) {
      if (this.ids.includes(row.id)) {
        return { 'background-color': '#E1F0FF', color: '#333' }
      } else {
        return { 'background-color': '#fff', color: '#333' }
      }
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加灾害风险路段";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids;
      getRiskSections(id).then(response => {
        this.form = response.data;
        // 确保managementMaintenanceBranchId与managementOfficeId保持一致
        if (this.form.managementOfficeId) {
          this.form.managementMaintenanceBranchId = this.form.managementOfficeId;
        }
        this.open = true;
        this.title = "修改灾害风险路段";

        // 加载路段数据
        if (this.form.managementOfficeId) {
          this.getRouteOptions(this.form.managementOfficeId);
        }
      });
    },
    /** 获取路段选项 */
    getRouteOptions(departmentId) {
      if (!departmentId) return;

      this.routeOptions = [];
      getMaintenanceSectionListAll({ departmentIdList: departmentId }).then((res) => {
        if (res.code === 200) {
          this.routeOptions = res.data || [];

          // 如果已有路段ID，加载该路段下的路线
          if (this.form.maintenanceSectionId) {
            this.getRouteListBySection(this.form.maintenanceSectionId);
          }
        }
      });
    },
    /** 根据路段ID获取路线列表 */
    getRouteListBySection(sectionId) {
      listByMaintenanceSectionId({ maintenanceSectionId: sectionId }).then((res) => {
        if (res.code === 200) {
          res.data.forEach((item) => {
            item.routeName = item.routeName + '(' + item.routeCode + ')';
          });
          this.routeList = res.data || [];
        }
      });
    },
    /** 提交按钮 */
    submitForm: function () {
      // 检查photo是否是数组，如果是则转换为逗号分隔的字符串
      if (this.form.photo && Array.isArray(this.form.photo)) {
        this.form.photo = this.form.photo.join(',');
      }
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.id != null) {
            updateRiskSections(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addRiskSections(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const id = row.id || this.ids;
      if (row.id) { // 单个删除
        this.$modal.confirm('是否确认删除改数据？').then(function () {
          let data = { idList: [row.id] };
          return delData(data);
        }).then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        }).catch(() => { });
      } else { // 批量删除
        this.$modal.confirm(`是否确认删除选中的 ${id.length} 条数据项？`).then(function () {
          let data = { idList: id };
          console.log(data)
          return delData(data);
        }).then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        }).catch(() => { });
      }
    },

    /** 导出按钮操作 */
    handleExport() {
      // 确保导出时也有日期范围参数
      if (this.dateRange && this.dateRange.length > 0) {
        this.queryParams.beginCheckTime = this.dateRange[0];
        this.queryParams.endCheckTime = this.dateRange[1];
      } else {
        this.queryParams.beginCheckTime = null;
        this.queryParams.endCheckTime = null;
      }

      // 先获取数据总数
      let queryParams = {};
      if (this.ids.length === 0) {
        queryParams = {
          ...this.queryParams,
          pageNum: 1,
          pageSize: 1,
        };
      } else {
        queryParams = {
          ids: this.ids,
          pageNum: 1,
          pageSize: 1,
        };
      }

      listRiskSections(queryParams).then((response) => {
        const export_count = response.total;

        const confirmMessage = this.ids.length === 0
          ? `根据搜索条件，本次导出共有 ${export_count} 条数据，是否确认导出？`
          : `根据选中条件，本次导出共有 ${export_count} 条数据，是否确认导出？`;

        this.$confirm(confirmMessage, '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        })
          .then(() => {
            const now = new Date();
            const timeStr = `${now.getFullYear()}年${now.getMonth() + 1}月${now.getDate()}日${now.getHours()}时${now.getMinutes()}分${now.getSeconds()}秒`;
            const fileName = `灾害风险路段_${timeStr}.xlsx`;

            // 导出时使用的参数
            let exportParams = {};
            if (this.ids.length === 0) {
              exportParams = { ...this.queryParams };
            } else {
              exportParams = { ids: this.ids };
            }

            this.download('middleData/xqRiskSections/export', exportParams, fileName, {
              parameterType: 'body', // 设置参数类型为 body
              headers: {
                'Content-Type': 'application/json', // 设置请求头为 JSON
              }
            });
          })
          .catch(() => {
            this.$message({
              type: 'info',
              message: '已取消导出',
            });
          });
      });
    },

    /** 批量审核按钮操作 */
    handleBatchAudit() {
      if (this.ids.length === 0) {
        this.$message.warning('请至少选择一条记录进行审核');
        return;
      }
      this.auditDialog.open = true;
    },

    /** 提交审核 */
    submitAudit(formData) {
      batchAudit(formData).then(response => {
        this.$modal.msgSuccess('审核操作成功');
        this.getList();
      });
    },

    /** 单条记录审核按钮操作 */
    handleAudit(row) {
      this.ids = [row.id]; // 设置当前选中的记录ID
      this.auditDialog.open = true; // 打开审核对话框
      this.auditDialog.title = '审核记录';
    },

    /** 导入按钮操作 */
    handleImport() {
      this.upload.title = "用户导入";
      this.upload.open = true;
    },
    /** 下载模板操作 */
    importTemplate() {
      this.download('system/user/importTemplate', {}, `user_template.xlsx`);
    },
    // 文件上传中处理
    handleFileUploadProgress(event, file, fileList) {
      this.upload.isUploading = true;
    },
    // 文件上传成功处理
    handleFileSuccess(response, file, fileList) {
      this.upload.open = false;
      this.upload.isUploading = false;
      this.$refs.upload.clearFiles();
      this.$alert("<div style='overflow: auto;overflow-x: hidden;max-height: 70vh;padding: 10px 20px 0;'>" + response.msg + "</div>", "导入结果", { dangerouslyUseHTMLString: true });
      this.getList();
    },
    // 提交上传文件
    submitFileForm() {
      this.$refs.upload.submit();
    },

    // 格式化是否采取措施显示
    formatMeasures(status) {
      const dict = this.measuresOptions.find((item) => item.dictValue === status);
      return dict ? dict.dictLabel : status;
    },

    // 格式化审核状态显示
    formatAuditStatus(status) {
      const dict = this.auditStatusOptions.find((item) => item.dictValue === status);
      return dict ? dict.dictLabel : status;
    },

    // 格式化公路类型显示
    formatRoadType(type) {
      const dict = this.roadTypeOptions.find((item) => item.dictValue === type);
      return dict ? dict.dictLabel : type;
    },

    // 桩号处理方法
    handleStartStakeInput() {
      // 移除非数字字符
      this.startStakeForm.k = this.startStakeForm.k.replace(/[^\d]/g, '');
      this.startStakeForm.m = this.startStakeForm.m.replace(/[^\d]/g, '');

      // 限制千位以下为3位数
      if (this.startStakeForm.m.length > 3) {
        this.startStakeForm.m = this.startStakeForm.m.slice(0, 3);
      }
    },
    handleStartStakeChange() {
      const k = parseInt(this.startStakeForm.k || '0');
      const m = parseInt(this.startStakeForm.m || '0');

      // 更新form中的桩号值
      this.form.startStake = `K${k}+${m.toString().padStart(3, '0')}`;

      // 计算长度
      this.calculateLength();
    },
    handleEndStakeInput() {
      // 移除非数字字符
      this.endStakeForm.k = this.endStakeForm.k.replace(/[^\d]/g, '');
      this.endStakeForm.m = this.endStakeForm.m.replace(/[^\d]/g, '');

      // 限制千位以下为3位数
      if (this.endStakeForm.m.length > 3) {
        this.endStakeForm.m = this.endStakeForm.m.slice(0, 3);
      }
    },
    handleEndStakeChange() {
      const k = parseInt(this.endStakeForm.k || '0');
      const m = parseInt(this.endStakeForm.m || '0');

      // 更新form中的桩号值
      this.form.endStake = `K${k}+${m.toString().padStart(3, '0')}`;

      // 计算长度
      this.calculateLength();
    },

    // 计算长度方法
    calculateLength() {
      if (this.form.startStake && this.form.endStake) {
        // 解析起点桩号
        const startMatch = this.form.startStake.match(/K(\d+)\+(\d{1,3})/);
        // 解析终点桩号
        const endMatch = this.form.endStake.match(/K(\d+)\+(\d{1,3})/);

        if (startMatch && endMatch) {
          const startK = parseInt(startMatch[1]);
          const startM = parseInt(startMatch[2]);
          const endK = parseInt(endMatch[1]);
          const endM = parseInt(endMatch[2]);

          // 计算总米数
          const startTotal = startK * 1000 + startM;
          const endTotal = endK * 1000 + endM;

          // 计算长度（公里）并保留两位小数
          if (endTotal >= startTotal) {
            this.form.length = ((endTotal - startTotal) / 1000).toFixed(2);
          } else {
            // 如果终点小于起点，长度设为0
            this.form.length = '0.00';
          }
        }
      }
    },
    /** 获取养护段数据 */
    async getDepts() {
      try {
        const response = await getTreeStruct({ types: '101', deptTypeList: [3, 4], dataRule: true });
        if (response.code === 200) {
          // 处理树数据，添加level和parent信息
          this.deptOptions = this.processTreeData(response.data);
        }
      } catch (error) {
        console.error('获取养护段数据失败：', error);
      }
    },
    /** 处理路线选择 */
    handleRouteSelect(value) {
      if (!value) {
        this.form.roadNumber = null;
        this.form.roadName = '';
        return;
      }
      const selectedRoute = this.routeList.find(route => route.routeId === value);
      if (selectedRoute) {
        this.form.roadNumber = selectedRoute.routeCode;
        this.form.roadName = selectedRoute.routeName;
        if (selectedRoute.routeType !== undefined) {
          this.form.roadType = selectedRoute.routeType;
        }
      }
    },
    /** 处理树数据，添加level和parent信息 */
    processTreeData(data, parentNode = null, level = 0) {
      if (!data || !Array.isArray(data)) {
        return [];
      }

      return data.map(item => {
        // 创建新对象，避免修改原始数据
        const newItem = { ...item };

        // 添加level信息
        newItem.level = level;

        // 添加parent信息（如果有父节点）
        if (parentNode) {
          newItem.parent = parentNode;
        }

        // 递归处理子节点
        if (newItem.children && newItem.children.length > 0) {
          newItem.children = this.processTreeData(newItem.children, newItem, level + 1);
        }

        return newItem;
      });
    },
    /** 表单 - 监听树节点点击 */
    formNodeClick(item) {
      if (!item) {
        console.error("formNodeClick: item 参数为空");
        return;
      }

      const { id, label, level, parent } = item;

      // 使用level属性判断节点层级
      if (level === 0) {
        // 第一层节点（管理处）
        // 设置管理处相关信息
        this.form.managementOfficeId = id;
        this.form.managementOffice = label;
        this.form.managementMaintenanceBranchId = id; // 设置与选择框绑定的值
        this.form.subOfficeId = null;
        this.form.subOffice = null;

        // 收集所有子节点ID
        if (item.children && item.children.length > 0) {
          // 这里如果需要收集子节点ID做其他操作，可以根据实际需求添加
        }

        // 触发部门变更
        this.formDeptChange(id);
      } else if (level === 1) {
        // 第二层节点（分处）
        // 使用parent属性获取父节点信息
        if (parent) {
          this.form.managementOfficeId = parent.id;
          this.form.managementOffice = parent.label;
          this.form.managementMaintenanceBranchId = parent.id; // 设置与选择框绑定的值
        } else {
          // 如果没有parent属性，则回退到原来的查找方式
          let parentNode = null;
          for (let i = 0; i < this.deptOptions.length; i++) {
            const dept = this.deptOptions[i];
            if (dept.children && dept.children.length > 0) {
              for (let j = 0; j < dept.children.length; j++) {
                if (dept.children[j].id === id) {
                  parentNode = dept;
                  break;
                }
              }
            }
            if (parentNode) break;
          }

          if (parentNode) {
            this.form.managementOfficeId = parentNode.id;
            this.form.managementOffice = parentNode.label;
            this.form.managementMaintenanceBranchId = parentNode.id; // 设置与选择框绑定的值
          }
        }

        this.form.subOfficeId = id;
        this.form.subOffice = label;

        // 触发部门变更
        this.formDeptChange(id);
      } else {
        // 如果没有level属性或者level不是0或1，则回退到原来的判断方式
        if (item.children && item.children.length > 0) {
          // 有子节点，则认为是第一层（管理处）
          // 设置管理处相关信息
          this.form.managementOfficeId = id;
          this.form.managementOffice = label;
          this.form.managementMaintenanceBranchId = id; // 设置与选择框绑定的值
          this.form.subOfficeId = null;
          this.form.subOffice = null;

          // 触发部门变更
          this.formDeptChange(id);
        } else {
          // 没有子节点，则认为是第二层（分处）
          // 查找父节点（管理处）
          let parentNode = null;
          // 遍历管理处列表查找父节点
          for (let i = 0; i < this.deptOptions.length; i++) {
            const dept = this.deptOptions[i];
            if (dept.children && dept.children.length > 0) {
              for (let j = 0; j < dept.children.length; j++) {
                if (dept.children[j].id === id) {
                  parentNode = dept;
                  break;
                }
              }
            }
            if (parentNode) break;
          }

          if (parentNode) {
            this.form.managementOfficeId = parentNode.id;
            this.form.managementOffice = parentNode.label;
            this.form.managementMaintenanceBranchId = parentNode.id; // 设置与选择框绑定的值
          }

          this.form.subOfficeId = id;
          this.form.subOffice = label;

          // 触发部门变更
          this.formDeptChange(id);
        }
      }
    },
    /** 表单 - 监听部门变化 */
    formDeptChange(e) {
      if (!e) {
        this.formMaintenanceSectionChange(null);
        return;
      }

      this.routeOptions = [];
      this.form.maintenanceSectionId = '';
      this.form.maintenanceSection = '';
      this.form.roadNumber = '';
      this.form.roadName = '';
      this.form.roadType = null;
      this.routeList = [];

      // 获取养护路段列表
      getMaintenanceSectionListAll({ departmentIdList: e }).then((res) => {
        if (res.code === 200) {
          this.routeOptions = res.data || [];
        }
      });
    },
    /** 表单 - 监听养护路段变化 */
    formMaintenanceSectionChange(e) {
      if (!e) {
        this.routeList = [];
        this.form.roadNumber = '';
        this.form.roadName = '';
        this.form.roadType = null;
        this.form.maintenanceSectionId = '';
        this.form.maintenanceSection = '';

        return;
      }

      // 查找选中的路段数据
      const selectedSection = this.routeOptions.find(item => item.maintenanceSectionId === e);
      if (selectedSection) {
        // 设置路段id和名称
        this.form.maintenanceSectionId = selectedSection.maintenanceSectionId;
        this.form.maintenanceSection = selectedSection.maintenanceSectionName;

        // 如果路段数据中包含routeType和routeCode，则设置对应的roadType和roadNumber
        if (selectedSection.routeType !== undefined) {
          this.form.roadType = selectedSection.routeType;
        }
        if (selectedSection.routeCode !== undefined) {
          this.form.roadNumber = selectedSection.routeCode;
        }
      }

      // 获取该路段下的路线列表
      listByMaintenanceSectionId({ maintenanceSectionId: e }).then((res) => {
        if (res.code === 200) {
          res.data.forEach((item) => {
            item.routeName = item.routeName + '(' + item.routeCode + ')';
          });

          // 如果有数据，选择第一个并赋值
          if (res.data && res.data.length > 0) {
            const firstRoute = res.data[0];
            this.form.roadType = firstRoute.routeType;
            this.form.roadNumber = firstRoute.routeCode;
            this.form.roadName = firstRoute.routeName.split('(')[0];
          }

          this.routeList = res.data || [];
        }
      });
    },
    // treeselect数据格式化
    normalizer(node) {
      if (node.maintenanceSectionId) {
        // 处理路段数据格式
        return {
          id: node.maintenanceSectionId,
          label: node.maintenanceSectionName
        };
      } else {
        // 处理部门数据格式
        return {
          id: node.id,
          label: node.label,
          children: node.children,
        };
      }
    },
    /** 格式化树形数据 */
    formatData(data) {
      let options = [];
      if (!data || !Array.isArray(data)) {
        return options;
      }
      data.forEach((item, key) => {
        options.push({ label: item.label, value: item.id });
        if (item.children) {
          item.children.forEach((items, keys) => {
            options.push({ label: items.label, value: items.id });
            if (items.children) {
              items.children.forEach((itemss, keyss) => {
                options.push({ label: itemss.label, value: itemss.id });
                if (itemss.children) {
                  itemss.children.forEach((itemsss, keysss) => {
                    options.push({ label: itemsss.label, value: itemsss.id });
                  });
                }
              });
            }
          });
        }
      });
      return options;
    },
    /** 处理树的原生点击事件 */
    handleTreeClick(event) {
      // 阻止事件冒泡
      event.stopPropagation();

      // 尝试找到点击的节点元素
      const clickedEl = event.target.closest('.el-tree-node');
      if (clickedEl) {
        const nodeId = clickedEl.getAttribute('data-key');

        if (nodeId && this.$refs.formTreeRef) {
          // 找到节点数据
          const node = this.$refs.formTreeRef.getNode(nodeId);
          if (node && node.data) {
            // 手动调用formNodeClick方法
            this.formNodeClick(node.data);
          }
        }
      }
    },
    // 表单 - 删除标签
    formRemoveTag(tag) {
      if (this.$refs.formTreeRef) {
        let checkedNodes = this.$refs.formTreeRef.getCheckedNodes();
        // 删除节点
        for (let i = 0; i < checkedNodes.length; i++) {
          if (checkedNodes[i].id == tag) {
            checkedNodes.splice(i, 1);
            break;
          }
        }
        // 设置树选中的节点
        this.$refs.formTreeRef.setCheckedNodes(checkedNodes);
        // 更新表单数据
        this.form.managementOfficeId = checkedNodes.map((node) => node.id);

        this.formDeptChange(this.form.managementOfficeId);
      }
    },
    /** 表单下拉框清空事件 */
    formSelectClear() {
      // 清空路线数据
      this.routeOptions = [];
      this.routeList = [];

      if (this.$refs.formTreeRef) {
        // 清空树选中数据
        this.$refs.formTreeRef.setCheckedKeys([]);
      }
    },
    // 表单 - 监听树节点选中
    formHandleNodeCheck(node, nodes) {
      this.form.managementOfficeId = node.id;
      this.form.managementMaintenanceBranchId = node.id; // 设置与选择框绑定的值
      this.formDeptChange(this.form.managementOfficeId);
    },
    /**
     * 坐标相关方法
     */
    // 显示坐标拾取组件
    showCoordinatePicker(type) {
      this.currentCoordinateType = type;

      // 如果已有经纬度，设置初始值
      if (this.form.longitude && this.form.latitude) {
        this.coordinatePickerInitPosition = `${this.form.longitude},${this.form.latitude}`;
      } else {
        // 默认昆明市坐标
        this.coordinatePickerInitPosition = "102.8207599,24.8885797";
      }

      this.coordinatePickerVisible = true;
    },

    // 处理坐标保存
    handleCoordinateSave(position) {
      if (!position) return;

      const coordinates = position.split(',');
      if (coordinates.length === 2) {
        const lng = coordinates[0];
        const lat = coordinates[1];

        // 根据坐标类型设置表单字段
        if (this.currentCoordinateType === 'longitude' || this.currentCoordinateType === 'latitude') {
          this.form.longitude = lng;
          this.form.latitude = lat;
        }
      }
    },
  }
};
</script>
<style lang="scss" scoped>
.hasTagsView .app-main[data-v-078753dd] {
  background: #f5f7fa;
}

.tableDiv {
  background-color: white;
  padding-bottom: 10px;
  overflow-x: auto;
}

/* 确保固定列可见 */
.el-table__fixed-right {
  height: 100% !important;
  z-index: 10;
}

/* 风险路段表单样式 */
.risk-sections-dialog {
  .el-dialog__body {
    padding: 20px 30px;
    max-height: calc(90vh - 150px);
    overflow-y: auto;
  }

  .risk-sections-form {
    .form-section {
      background: #f8f9fa;
      border-radius: 8px;
      padding: 20px;
      margin-bottom: 24px;

      &:last-child {
        margin-bottom: 0;
      }

      .section-title {
        font-size: 16px;
        font-weight: 500;
        color: #1a1a1a;
        margin-bottom: 20px;
        padding-left: 10px;
        border-left: 4px solid #409EFF;
      }
    }

    .el-form-item {
      margin-bottom: 18px;

      &__label {
        font-weight: 500;
        color: #606266;
      }
    }

    .full-width {
      width: 100%;
    }

    .el-textarea__inner {
      font-family: inherit;
    }
  }

  .dialog-footer {
    padding: 20px 30px;
    text-align: right;
    background: #f8f9fa;
    border-top: 1px solid #e4e7ed;
  }
}

/* 自定义选择框样式 */
.custom-select {
  .el-select-dropdown__item {
    padding: 0 0 0 20px;
    height: auto;
    overflow: hidden;
    line-height: 25px;
    margin-bottom: 0;
    text-overflow: ellipsis;

    span {
      margin-left: 5px;
    }
  }
}

.infoBox {
  padding: 15px 15px 0 15px;
  box-sizing: border-box;
  border-radius: 6px;
  border: 1px solid #C4C4C4;
  position: relative;

  .infoTitle {
    user-select: none;
    position: absolute;
    top: 0;
    left: 0;
    padding: 0 10px;
    font-size: 14px;
    line-height: 14px;
    font-weight: bold;
    transform: translateX(15px) translateY(-50%);
    background-color: white;
  }
}

.dialog-footer {
  padding: 20px 30px;
  text-align: right;
  background: #f8f9fa;
  border-top: 1px solid #e4e7ed;
}

.el-form-item {
  margin-bottom: 18px;
}

.el-form-item__label {
  font-weight: 500;
  color: #606266;
}

.el-textarea__inner {
  font-family: inherit;
}
</style>
