<template>
  <div style="height: 100%;">
    <div class="map-view" id="earlyMap" ref="mapRef"></div>
  </div>
</template>
<script>
import 'ol/ol.css'
import { Map, View, Feature } from 'ol'
import TileLayer from 'ol/layer/Tile'
import XYZ from 'ol/source/XYZ'
import { fromLonLat } from 'ol/proj'
import TileWMS from 'ol/source/TileWMS'
import VectorLayer from 'ol/layer/Vector'
import VectorSource from 'ol/source/Vector'
import { defaults as defaultsControl } from 'ol/control'
import { Circle as CircleStyle, RegularShape, Fill, Stroke, Style, Icon, Text } from 'ol/style'
import { Point } from 'ol/geom'
import { getShapeList } from "@/api/oneMap/deptInfo";
import {
  addMapMask,
} from "@/views/map/components/common/mapFun";
let map = null
const key = 'cde0b56cf882626889981701109a7536'
let layer1 = new TileLayer({
  name: '天地图矢量图层',
  source: new XYZ({
    url: `http://t{0-7}.tianditu.gov.cn/DataServer?T=img_w&x={x}&y={y}&l={z}&tk=${key}`,
    attributions: '天地图的属性描述',
    wrapX: false,
    maxZoom: 18,
  }),
  preload: Infinity,
  visible: true,
})
let layer3 = new TileLayer({
  source: new TileWMS({
    ratio: 1,
    url: 'https://gis.glyhgl.com/server/services/yanghu/gis_route_shape4/MapServer/WMSServer',
    params: {
      FORMAT: 'image/png', // 图像格式，如image/png
      LAYERS: '0', // 图层名称
      TILED: true, // 使用切片
      VERSION: '1.3.0', // WMS版本号，默认1.1.1
      STYLES: '',
    },
    serverType: 'geoserver',
    visible: true,
  }),
})
const pointLayer = new VectorLayer({
  source: new VectorSource(), // 空的源，用于动态添加点
})
const blinkingPointLayer = new VectorLayer({
  source: new VectorSource(),
  zIndex: 999 // 确保在最上层
});
let layers = [layer1, layer3, pointLayer, blinkingPointLayer]

export default {
  data() {
    return {
      blinkIntervalList: []
    }
  },
  props: {
    isBig: {
      type: Boolean,
      default: false
    },
  },
  mounted() {
    this.$nextTick(() => {
      this.initMap()
    })
  },
  methods: {
    initMap() {
      if (map) {
        map.setTarget(null); // 移除地图容器
        // 销毁图层和源等资源
        map.getLayers().forEach(layer => layer.getSource().clear());
        map = null; // 清除地图变量引用
      }
      // 创建地图
      map = new Map({
        target: 'earlyMap', // HTML 元素的 ID，地图将在该元素中渲染
        layers,
        view: new View({
          projection: 'EPSG:3857',
          center: fromLonLat([90.06, 30.53]), // 地图中心点坐标
          zoom: 8, // 缩放级别
          wrapX: false,
          crossOrigin: "anonymous",
          maxZoom: 18,
          minZoom: 4,
        }),
        controls: defaultsControl({
          attribution: false,
          zoom: false,
        }).extend([]),
      })
      /* // 添加点击事件监听
      map.on('click', (evt) => {
        const feature = map.forEachFeatureAtPixel(
          evt.pixel,
          (feature) => feature
        );

        if (feature) {
          this.handlePointClick(feature, evt.coordinate);
        }
      }); */
      // this.getRangeShape()
      this.addPoint()
    },
    /* handlePointClick(feature, coordinate) {
      console.log(feature.getProperties())
      this.$emit('point-click', feature.getProperties().id)
    }, */
    focusOnLocation(lonLat, zoomLevel = 18) {
      if (!map) {
        return;
      }
      const center = fromLonLat(lonLat);
      const view = map.getView();
      view.animate({
        center: center,
        zoom: zoomLevel,
        duration: 500
      });
    },
    // 在类中新增方法
    clearAllPoints() {
      const source = pointLayer.getSource();
      source.clear()
      blinkingPointLayer.getSource().clear();
      this.blinkIntervalList.forEach(item => {
        if (item) {
          clearInterval(item);
        }
      })
    },

    addPoint() {
      let data = JSON.parse(localStorage.getItem('mapData'));
      // 创建标注点要素
      const pointFeature = new Feature({
        geometry: new Point(fromLonLat([data.lon, data.lat]))
      });
      // 图标样式
      const iconStyle = new Style({
        image: new Icon({
          src: data.icon,
          scale: 0.6,
          anchor: [0.5, 0.5],
        }),
      })

      // 文本样式
      const textStyle = new Style({
        text: new Text({
          text: data.name,
          // font: 'bold 12px Microsoft YaHei',
          font: '12px Microsoft YaHei',
          fill: new Fill({ color: '#ffffff' }),
          // stroke: new Stroke({ color: '#000000', width: 2 }),
          offsetY: -30,
          textAlign: 'center',
          // backgroundFill: new Fill({
          //   color: 'rgba(0, 111, 255, 0.7)',
          // }),
        }),
      })
      // 设置初始样式
      pointFeature.setStyle([iconStyle, textStyle]);
      pointFeature.setProperties(
        {
          id: data.id
        }
      )
      pointLayer.getSource().addFeature(pointFeature);
      this.focusOnLocation([data.lon, data.lat])
    },

    getRangeShape() {
      getShapeList({ sysDeptIds: [] }).then(res => {
        if (res.code == 200) {
          addMapMask(map, res.data);
        }
      })
    }
  },
  beforeDestroy() {
    this.clearAllPoints()
  }
}
</script>

<style scoped lang="scss">
@import "@/assets/styles/utils.scss";

.map-view {
  width: 100%;
  height: 100%;
}

.line-text {
  z-index: 99999;
}
</style>
