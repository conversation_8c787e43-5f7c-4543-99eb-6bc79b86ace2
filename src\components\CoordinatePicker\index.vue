<template>
  <el-dialog
    title="地图拾取器"
    class="coordinateDialog map-picker-dialog premium-map-picker"
    :visible.sync="dialogVisible"
    @close="handleClose"
    :close-on-press-escape="false"
    :close-on-click-modal="false"
    append-to-body
    width="90%"
    :show-close="false"
    :before-close="handleClose"
    top="3vh">
    <div class="dialog-container">
      <div class="dialog-header">
        <div class="dialog-title-container">
          <font-awesome-icon :icon="['fas', 'map-marked-alt']" class="title-icon" />
          <h3 class="dialog-title">地图拾取器</h3>
        </div>
        <div class="header-actions">
          <el-tooltip content="全屏模式" placement="bottom">
            <button class="action-btn" @click="toggleFullscreen">
              <font-awesome-icon :icon="isFullscreen ? ['fas', 'compress'] : ['fas', 'expand']" />
            </button>
          </el-tooltip>
          <button class="close-btn" @click="dialogVisible = false">
            <font-awesome-icon :icon="['fas', 'times']" />
          </button>
        </div>
      </div>
      <div class="coordinateMap" ref="mapContainer" :class="{'fullscreen': isFullscreen}">
        <div id="coordinateBox" v-if="dialogVisible"></div>
        <div class="coordinateSearch">
          <div class="search-container">
            <font-awesome-icon :icon="['fas', 'search']" class="search-icon" />
            <el-input
              v-model="coordinateSearch"
              clearable
              placeholder="搜索地点..."
              @keyup.enter.native="coordinateList"
              class="search-input"
            >
              <template slot="suffix">
                <div class="suggestion-count" v-if="searchResults.length">
                  {{ searchResults.length }}
                </div>
              </template>
            </el-input>
            <el-button type="primary" class="search-button" @click="coordinateList">
              <span>搜索</span>
              <font-awesome-icon :icon="['fas', 'chevron-right']" class="btn-icon-right" />
            </el-button>
          </div>
          <div class="search-results" v-if="searchResults.length && showSearchResults">
            <div class="results-header">
              <span>搜索结果</span>
              <button class="close-results" @click="showSearchResults = false">
                <font-awesome-icon :icon="['fas', 'times']" />
              </button>
            </div>
            <div class="results-list">
              <div
                v-for="(item, index) in searchResults"
                :key="index"
                class="result-item"
                @click="selectSearchResult(item)">
                <font-awesome-icon :icon="['fas', 'map-marker-alt']" class="result-icon" />
                <div class="result-info">
                  <div class="result-name">{{ item.name }}</div>
                  <div class="result-address">{{ item.address }}</div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div class="mapControls">
          <div class="control-group main-controls">
            <el-tooltip content="卫星视图" placement="left" effect="dark">
              <el-button class="control-btn" :class="{'active': mapStyle === 'satellite'}" @click="switchMapStyle('satellite')">
                <font-awesome-icon :icon="['fas', 'satellite']" />
              </el-button>
            </el-tooltip>
            <el-tooltip content="标准视图" placement="left" effect="dark">
              <el-button class="control-btn" :class="{'active': mapStyle === 'standard'}" @click="switchMapStyle('standard')">
                <font-awesome-icon :icon="['fas', 'map']" />
              </el-button>
            </el-tooltip>
            <el-tooltip content="3D视图" placement="left" effect="dark">
              <el-button class="control-btn" :class="{'active': viewMode === '3D'}" @click="toggleViewMode">
                <font-awesome-icon :icon="['fas', 'cube']" />
              </el-button>
            </el-tooltip>
          </div>
        </div>

        <!-- 悬浮操作按钮 -->
        <div class="floating-actions">
          <el-button @click="dialogVisible = false" class="floating-cancel-btn">
            <font-awesome-icon :icon="['fas', 'times']" />
            <span>取消</span>
          </el-button>
          <el-button type="primary" @click="coordinateSave" class="floating-save-btn">
            <font-awesome-icon :icon="['fas', 'check']" />
            <span>确认选择</span>
          </el-button>
        </div>

        <div class="coordinateTip">
          <div class="tip-container">
            <div class="tip-header">
              <font-awesome-icon :icon="['fas', 'location-arrow']" class="location-icon" />
              <span>位置信息</span>
              <div class="coordinate-actions">
                <el-button type="text" class="refresh-btn" @click="refreshCoordinates">
                  <font-awesome-icon :icon="['fas', 'sync-alt']" />
                </el-button>
              </div>
            </div>
            <div class="coordinate-formats">
              <div class="format-group">
                <div class="format-label">
                  <span>经纬度:</span>
                  <el-tooltip content="经度,纬度格式" placement="top">
                    <font-awesome-icon :icon="['fas', 'info-circle']" class="info-icon" />
                  </el-tooltip>
                </div>
                <el-input v-model="coordinatePosition" readonly class="format-value">
                  <template slot="append">
                    <el-button @click="copyToClipboard(coordinatePosition)" class="copy-btn">
                      <font-awesome-icon :icon="['fas', 'copy']" />
                    </el-button>
                  </template>
                </el-input>
              </div>
              <div class="format-group">
                <div class="format-label">
                  <span>DMS格式:</span>
                  <el-tooltip content="度分秒格式" placement="top">
                    <font-awesome-icon :icon="['fas', 'info-circle']" class="info-icon" />
                  </el-tooltip>
                </div>
                <el-input v-model="dmsFormat" readonly class="format-value">
                  <template slot="append">
                    <el-button @click="copyToClipboard(dmsFormat)" class="copy-btn">
                      <font-awesome-icon :icon="['fas', 'copy']" />
                    </el-button>
                  </template>
                </el-input>
              </div>
              <div class="format-group">
                <div class="format-label">
                  <span>WKT格式:</span>
                  <el-tooltip content="Well-Known Text格式" placement="top">
                    <font-awesome-icon :icon="['fas', 'info-circle']" class="info-icon" />
                  </el-tooltip>
                </div>
                <el-input v-model="wktFormat" readonly class="format-value">
                  <template slot="append">
                    <el-button @click="copyToClipboard(wktFormat)" class="copy-btn">
                      <font-awesome-icon :icon="['fas', 'copy']" />
                    </el-button>
                  </template>
                </el-input>
              </div>
            </div>
          </div>
          <div class="history-section" v-if="coordinateHistory.length > 0">
            <div class="history-header">
              <font-awesome-icon :icon="['fas', 'history']" />
              <span>历史记录</span>
              <el-button type="text" class="clear-history" @click="clearHistory" v-if="coordinateHistory.length > 0">
                <font-awesome-icon :icon="['fas', 'trash-alt']" />
              </el-button>
            </div>
            <div class="history-list">
              <div
                v-for="(item, index) in coordinateHistory.slice(0, 3)"
                :key="index"
                class="history-item"
                @click="selectHistoryPoint(item)">
                <font-awesome-icon :icon="['fas', 'map-marker-alt']" class="history-icon" />
                <span class="history-coords">{{ item }}</span>
              </div>
            </div>
          </div>
        </div>

        <div class="map-crosshair" v-if="showCrosshair">
          <div class="crosshair-inner">
            <div class="crosshair-h"></div>
            <div class="crosshair-v"></div>
          </div>
        </div>

        <!-- 重构加载遮罩层，使其更扁平 -->
        <div class="loading-overlay coordinate-loading-mask" v-if="isLoading || isProgressAnimating">
          <div class="loader map-loader">
            <div class="loader-spinner map-spinner">
              <font-awesome-icon :icon="['fas', 'spinner']" spin class="spinner-icon" />
            </div>
            <span class="loading-text">载入地图中...</span>
            <div class="loading-progress map-progress">
              <div class="progress-bar" :style="{width: loadingProgress + '%'}"></div>
            </div>
            <div class="progress-text map-progress-text">{{ loadingProgress }}%</div>
          </div>
        </div>

        <div class="keyboard-shortcuts" v-if="showKeyboardShortcuts">
          <div class="shortcuts-header">
            <font-awesome-icon :icon="['fas', 'keyboard']" />
            <span>键盘快捷键</span>
            <button class="close-shortcuts" @click="showKeyboardShortcuts = false">
              <font-awesome-icon :icon="['fas', 'times']" />
            </button>
          </div>
          <div class="shortcuts-list">
            <div class="shortcut-item">
              <span class="key">Esc</span>
              <span class="description">关闭对话框</span>
            </div>
            <div class="shortcut-item">
              <span class="key">C</span>
              <span class="description">复制当前坐标</span>
            </div>
            <div class="shortcut-item">
              <span class="key">S</span>
              <span class="description">切换卫星图</span>
            </div>
            <div class="shortcut-item">
              <span class="key">M</span>
              <span class="description">切换地图视图</span>
            </div>
            <div class="shortcut-item">
              <span class="key">3</span>
              <span class="description">切换3D视图</span>
            </div>
            <div class="shortcut-item">
              <span class="key">R</span>
              <span class="description">重置视图</span>
            </div>
            <div class="shortcut-item">
              <span class="key">H</span>
              <span class="description">显示/隐藏快捷键</span>
            </div>
            <div class="shortcut-item">
              <span class="key">F</span>
              <span class="description">切换全屏模式</span>
            </div>
          </div>
        </div>

        <button class="shortcuts-toggle" @click="showKeyboardShortcuts = !showKeyboardShortcuts">
          <font-awesome-icon :icon="['fas', 'keyboard']" />
        </button>
      </div>
    </div>
  </el-dialog>
</template>

<script>
import AMapLoader from '@amap/amap-jsapi-loader'; // 引入高德地图API加载器
import { gsap } from 'gsap'; // 引入GSAP动画库
import { library } from '@fortawesome/fontawesome-svg-core';
import {
  faSearch, faLocationArrow, faCopy, faHistory,
  faMapMarkerAlt, faSpinner, faSave, faTimes,
  faMap, faSatellite, faCube, faInfoCircle,
  faChevronRight, faSyncAlt, faKeyboard, faMapMarkedAlt,
  faTrashAlt, faExpand, faCompress, faCheck, faMagic,
  faLayerGroup, faMountain, faRuler, faStreetView,
  faCompass, faMapPin
} from '@fortawesome/free-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/vue-fontawesome';

// 注册Font Awesome图标
library.add(
  faSearch, faLocationArrow, faCopy, faHistory,
  faMapMarkerAlt, faSpinner, faSave, faTimes,
  faMap, faSatellite, faCube, faInfoCircle,
  faChevronRight, faSyncAlt, faKeyboard, faMapMarkedAlt,
  faTrashAlt, faExpand, faCompress, faCheck, faMagic,
  faLayerGroup, faMountain, faRuler, faStreetView,
  faCompass, faMapPin
);

export default {
  name: "CoordinatePicker",
  components: {
    FontAwesomeIcon
  },
  props: {
    // 控制对话框是否可见
    visible: {
      type: Boolean,
      default: false
    },
    // 初始坐标，格式："经度,纬度"，例如："102.8207599,24.8885797"
    initialPosition: {
      type: String,
      default: "102.8207599,24.8885797" // 默认昆明市坐标
    },
    // 对话框标题
    title: {
      type: String,
      default: "地图拾取器"
    }
  },
  data() {
    return {
      dialogVisible: this.visible,
      coordinateMap: null, // 地图实例
      coordinateMarker: null, // 地图标记
      coordinatePosition: '', // 坐标经纬度
      coordinateSearch: '', // 坐标查询
      coordinateHistory: [], // 历史坐标记录
      dmsFormat: '', // DMS格式坐标 (度分秒)
      wktFormat: '', // WKT格式坐标
      isLoading: true, // 加载状态
      isProgressAnimating: true, // 进度条动画状态
      showCrosshair: false, // 十字准星
      mapStyle: 'satellite', // 地图样式: satellite/standard
      viewMode: '3D', // 视图模式: 3D/2D
      mapLayers: {
        satellite: null,
        roadNet: null,
        standard: null
      },
      searchResults: [], // 搜索结果
      showSearchResults: false, // 是否显示搜索结果
      loadingProgress: 0, // 加载进度
      showKeyboardShortcuts: false, // 是否显示键盘快捷键
      isFullscreen: false, // 是否全屏模式
      animationTimelines: {}, // 存储动画时间线
      markerDropDelay: 0.2 // 标记动画延迟
    };
  },
  watch: {
    visible(val) {
      this.dialogVisible = val;
      if (val) {
        this.isLoading = true;
        this.isProgressAnimating = true;
        this.loadingProgress = 0;

        // 立即开始进度条动画，不等待nextTick
        this.simulateLoadingProgress();

        // 确保加载界面立即可见
        setTimeout(() => {
          const loadingOverlay = document.querySelector('.coordinate-loading-mask');
          if (loadingOverlay) {
            loadingOverlay.style.display = 'flex';
            loadingOverlay.style.opacity = '1';
            console.log('确保加载界面可见');
          }
        }, 0);

        this.$nextTick(() => {
          this.coordinateInit();
        });
      }
    },
    dialogVisible(val) {
      this.$emit('update:visible', val);

      // 退出时清理动画
      if (!val) {
        this.clearAnimations();
      }
    },
    coordinatePosition(val) {
      if (val) {
        this.updateDMSFormat(val);
        this.updateWKTFormat(val);
      }
    },
    isFullscreen(val) {
      // 切换全屏模式时更新地图大小
      if (this.coordinateMap) {
        setTimeout(() => {
          this.coordinateMap.resize();
        }, 300);
      }
    }
  },
  methods: {
    // 清理所有GSAP动画
    clearAnimations() {
      // 清理存储的时间线
      Object.values(this.animationTimelines).forEach(timeline => {
        if (timeline) {
          timeline.kill();
        }
      });

      // 重置时间线存储
      this.animationTimelines = {};
    },

    // DMS格式转换 (度分秒)
    updateDMSFormat(coordString) {
      if (!coordString) return;

      const [lng, lat] = coordString.split(',').map(Number);

      const convertToDMS = (coordinate, isLng) => {
        const absolute = Math.abs(coordinate);
        const degrees = Math.floor(absolute);
        const minutesNotTruncated = (absolute - degrees) * 60;
        const minutes = Math.floor(minutesNotTruncated);
        const seconds = ((minutesNotTruncated - minutes) * 60).toFixed(2);

        let direction = '';
        if (isLng) {
          direction = coordinate >= 0 ? 'E' : 'W';
        } else {
          direction = coordinate >= 0 ? 'N' : 'S';
        }

        return `${degrees}° ${minutes}′ ${seconds}″ ${direction}`;
      };

      const lngDMS = convertToDMS(lng, true);
      const latDMS = convertToDMS(lat, false);

      this.dmsFormat = `${lngDMS}, ${latDMS}`;
    },

    // WKT格式转换
    updateWKTFormat(coordString) {
      if (!coordString) return;

      const [lng, lat] = coordString.split(',').map(Number);
      this.wktFormat = `POINT(${lng} ${lat})`;
    },

    // 复制到剪贴板
    copyToClipboard(text) {
      navigator.clipboard.writeText(text).then(() => {
        // 成功消息动画
        this.$message({
          message: '复制成功',
          type: 'success',
          duration: 1500
        });

        // 添加复制成功的视觉反馈
        if (this.animationTimelines.copy) {
          this.animationTimelines.copy.kill();
        }

        const copyBtns = document.querySelectorAll('.copy-btn');
        copyBtns.forEach(btn => {
          // 创建一个新的时间线
          const timeline = gsap.timeline();
          timeline.to(btn, {
            backgroundColor: 'rgba(40, 167, 69, 0.5)',
            scale: 1.3,
            duration: 0.3,
            ease: "elastic.out(1, 0.5)"
          }).to(btn, {
            backgroundColor: 'rgba(255, 255, 255, 0.15)',
            scale: 1,
            duration: 0.3,
            ease: "power2.out"
          });

          this.animationTimelines.copy = timeline;
        });
      }).catch(() => {
        this.$message.error('复制失败');
      });
    },

    // 选择历史点位
    selectHistoryPoint(coordinates) {
      this.coordinatePosition = coordinates;
      const [lng, lat] = coordinates.split(',').map(Number);

      // 添加历史点选择的动画效果
      const historyItems = document.querySelectorAll('.history-item');
      historyItems.forEach(item => {
        if (item.textContent.includes(coordinates)) {
          gsap.fromTo(item,
            { backgroundColor: 'rgba(24, 103, 192, 0.5)' },
            { backgroundColor: 'transparent', duration: 1.2, ease: "power2.out" }
          );
        }
      });

      // 更新地图和标记
      this.coordinateMap.setCenter([lng, lat]);
      if (this.coordinateMarker) {
        this.coordinateMap.remove(this.coordinateMarker);
      }

      // 缓动到新位置
      this.coordinateMap.setZoomAndCenter(13, [lng, lat], false);

      // 添加带增强动画的标记
      this.createMarkerWithAnimation([lng, lat], true);
    },

    // 切换地图样式
    switchMapStyle(style) {
      if (style === this.mapStyle) return;

      // 添加样式切换动画
      const mapContainer = document.getElementById('coordinateBox');

      if (this.animationTimelines.styleSwitch) {
        this.animationTimelines.styleSwitch.kill();
      }

      // 创建一个新的时间线
      const timeline = gsap.timeline();
      timeline.to(mapContainer, {
        opacity: 0.7,
        scale: 0.98,
        duration: 0.4,
        ease: "power2.out",
        onComplete: () => {
          this.mapStyle = style;

          // 移除所有图层
          this.coordinateMap.remove([this.mapLayers.satellite, this.mapLayers.roadNet, this.mapLayers.standard].filter(Boolean));

          // 添加所选图层
          if (style === 'satellite') {
            this.coordinateMap.add([this.mapLayers.satellite, this.mapLayers.roadNet]);
          } else {
            this.coordinateMap.add(this.mapLayers.standard);
          }
        }
      }).to(mapContainer, {
        opacity: 1,
        scale: 1,
        duration: 0.5,
        delay: 0.1,
        ease: "elastic.out(1, 0.75)"
      });

      this.animationTimelines.styleSwitch = timeline;
    },

    // 切换3D/2D视图
    toggleViewMode() {
      if (this.viewMode === '3D') {
        // 从3D切换到2D
        this.viewMode = '2D';

        if (this.animationTimelines.viewToggle) {
          this.animationTimelines.viewToggle.kill();
        }

        // 创建过渡动画
        const timeline = gsap.timeline();
        timeline.to('#coordinateBox', {
          scale: 0.95,
          opacity: 0.8,
          duration: 0.4,
          ease: "power2.out",
          onComplete: () => {
            this.coordinateMap.setViewMode(this.viewMode);
          }
        }).to('#coordinateBox', {
          scale: 1,
          opacity: 1,
          duration: 0.5,
          ease: "elastic.out(1, 0.5)",
          delay: 0.1
        });

        this.animationTimelines.viewToggle = timeline;
      } else {
        // 从2D切换到3D
        this.viewMode = '3D';

        if (this.animationTimelines.viewToggle) {
          this.animationTimelines.viewToggle.kill();
        }

        // 创建过渡动画
        const timeline = gsap.timeline();
        timeline.to('#coordinateBox', {
          scale: 0.95,
          opacity: 0.8,
          duration: 0.4,
          ease: "power2.out",
          onComplete: () => {
            this.coordinateMap.setViewMode(this.viewMode);
          }
        }).to('#coordinateBox', {
          scale: 1,
          opacity: 1,
          duration: 0.6,
          ease: "elastic.out(1, 0.5)",
          delay: 0.1
        });

        this.animationTimelines.viewToggle = timeline;
      }
    },

    // 创建带动画的标记
    createMarkerWithAnimation(position, isHistorySelection = false) {
      // 先清除之前的标记
      if (this.coordinateMarker) {
        this.coordinateMap.remove(this.coordinateMarker);
        this.coordinateMarker = null;
      }

      // 清除之前的动画
      if (this.animationTimelines.marker) {
        this.animationTimelines.marker.kill();
      }

      // 创建标记
      this.coordinateMarker = new AMap.Marker({
        position: position,
        anchor: 'bottom-center',
        zIndex: 100
      });

      // 创建自定义内容容器
      const markerContent = document.createElement('div');
      markerContent.className = 'custom-marker';

      // 创建各个元素
      const markerPin = document.createElement('div');
      markerPin.className = 'marker-pin';

      const markerPulse = document.createElement('div');
      markerPulse.className = 'marker-pulse';

      const markerShadow = document.createElement('div');
      markerShadow.className = 'marker-shadow';

      const markerRipple = document.createElement('div');
      markerRipple.className = 'marker-ripple';

      const markerGlow = document.createElement('div');
      markerGlow.className = 'marker-glow';

      // 添加所有元素到容器
      markerContent.appendChild(markerPin);
      markerContent.appendChild(markerPulse);
      markerContent.appendChild(markerShadow);
      markerContent.appendChild(markerRipple);
      markerContent.appendChild(markerGlow);

      // 设置内容到标记并添加到地图
      this.coordinateMarker.setContent(markerContent);
      this.coordinateMap.add(this.coordinateMarker);

      // 确保CSS动画能够正常工作
      // 添加特定的动画类，而不是使用style属性
      markerPulse.classList.add('animate-pulse');
      markerRipple.classList.add('animate-ripple');
      markerGlow.classList.add('animate-glow');

      // 添加调试日志
      console.log('动画类已添加到标记元素');

      // 创建一个新的GSAP时间线，只处理不用CSS动画的部分
      const timeline = gsap.timeline();

      // 标记从天而降的动画 - 针对历史选择和新点击的不同效果
      if (isHistorySelection) {
        // 历史点位选择动画 - 缩放弹出效果
        timeline.fromTo(markerPin,
          { scale: 0.2, opacity: 0, y: 0 },
          { scale: 1, opacity: 1, y: 0, duration: 0.8, ease: "elastic.out(1, 0.3)" }
        );
      } else {
        // 新点击动画 - 从天而降效果
        timeline.fromTo(markerPin,
          { y: -120, opacity: 0, scale: 0.5 },
          { y: 0, opacity: 1, scale: 1, duration: 0.9, ease: "bounce.out" }
        );
      }

      // 阴影动画
      timeline.fromTo(markerShadow,
        { opacity: 0, scale: 0.2 },
        { opacity: 0.8, scale: 1, duration: 0.5, ease: "power2.out" },
        "-=0.6" // 与上一个动画重叠执行
      );

      // 存储时间线引用
      this.animationTimelines.marker = timeline;

      // 延迟一点添加标记微小浮动动画，避免和其他动画冲突
      setTimeout(() => {
        // 确保元素还存在
        if (markerPin && document.body.contains(markerPin)) {
          gsap.to(markerPin, {
            y: -4,
            duration: 1.5,
            ease: "sine.inOut",
            repeat: -1,
            yoyo: true
          });
        }
      }, 1000);

      // 添加一个调试日志
      console.log('标记创建完成，位置:', position);
    },

    // 地图初始化
    coordinateInit() {
      const _this = this;
      // 重置筛选结果
      this.coordinateSearch = '';
      // 设置初始坐标
      this.coordinatePosition = this.initialPosition;

      // 加载配置和入口
      window._AMapSecurityConfig = {
        securityJsCode: "8ba5a60100192adc21a2044b9582e26e", // 安全密钥
      };
      AMapLoader.load({
        key: "38ce82094eecafcb00a7dd5b323cc4d0", // 申请好的Web端开发者Key，首次调用 load 时必填
        version: "2.0", // 指定要加载的 JSAPI 的版本，缺省时默认为 1.4.15
        plugins: ['AMap.ToolBar', 'AMap.Scale', 'AMap.PlaceSearch', 'AMap.Geolocation']
      })
        .then((AMap) => {
          // 变量初始化
          let positionInit = this.initialPosition.split(',').map(Number);

          // 初始化图层
          _this.mapLayers.satellite = new AMap.TileLayer.Satellite(); // 创建卫星图层
          _this.mapLayers.roadNet = new AMap.TileLayer.RoadNet(); // 创建路网图层
          _this.mapLayers.standard = new AMap.TileLayer(); // 标准图层

          // 地图初始化
          _this.coordinateMap = new AMap.Map("coordinateBox", {
            // 设置地图容器id
            viewMode: _this.viewMode, // 是否为3D地图模式
            zoom: 11, // 初始化地图级别
            center: positionInit, // 初始化地图中心点位置
            layers: [_this.mapLayers.satellite, _this.mapLayers.roadNet], // 地图图层的数组
            mapStyle: 'amap://styles/dark', // 设置地图的显示样式
            animateEnable: true,
            jogEnable: true,
            pitchEnable: true,
            pitch: 0, // 设置初始仰角
            skyColor: '#1a1a1a' // 设置天空颜色，使3D视图更美观
          });

          let toolbar = new AMap.ToolBar({
            position: 'RB',
            offset: new AMap.Pixel(20, 60)
          });
          _this.coordinateMap.addControl(toolbar);

          let scale = new AMap.Scale({
            position: 'LB'
          });
          _this.coordinateMap.addControl(scale);

          // 地图加载完成事件
          _this.coordinateMap.on('complete', () => {
            // 设置地图容器透明度为0
            const mapDom = document.getElementById('coordinateBox');
            mapDom.style.opacity = '0';

            // 确保加载样式被正确应用
            document.querySelector('.loading-overlay').style.display = 'flex';

            console.log('地图加载完成');

            // 立即将进度条推进到较高值，确保用户看到进度
            if (_this.loadingProgress < 70) {
              _this.loadingProgress = 70;
            }

            // 等待进度条动画结束后再显示地图
            const checkProgress = setInterval(() => {
              if (!_this.isProgressAnimating) {
                clearInterval(checkProgress);

                // 动画显示地图
                gsap.to(mapDom, {
                  opacity: 1,
                  duration: 1.2,
                  ease: 'power2.out',
                  onComplete: () => {
                    // 隐藏加载动画
                    _this.isLoading = false;

                    // 等待一小段时间，确保DOM更新完成后再初始化动画
                    setTimeout(() => {
                      // 地图加载完成后，添加UI元素动画入场
                      _this.animateUIElements();

                      // 创建初始标记前确认DOM已完全加载
                      setTimeout(() => {
                        // 初始化坐标点
                        _this.createMarkerWithAnimation(positionInit);

                        // 更新DMS格式和WKT格式
                        _this.updateDMSFormat(_this.coordinatePosition);
                        _this.updateWKTFormat(_this.coordinatePosition);
                      }, 300);  // 给足够时间让DOM加载
                    }, 200);
                  }
                });
              }
            }, 100);

            // 设置键盘快捷键
            document.addEventListener('keydown', _this.handleKeyboardShortcuts);
          });

          // 地图绑定点击事件
          _this.coordinateMap.on("click", _this.coordinateClick);

          // 启用鼠标悬停显示十字准星
          _this.coordinateMap.on('mousemove', () => {
            if (!_this.showCrosshair) {
              _this.showCrosshair = true;
            }
          });

          _this.coordinateMap.on('mouseout', () => {
            _this.showCrosshair = false;
          });
        })
        .catch((e) => {
          console.log(e);
          _this.isLoading = false;
          _this.$message.error('地图加载失败');
        });
    },

    // UI元素动画入场
    animateUIElements() {
      // 时间错开，创建级联动画效果
      const elements = [
        '.coordinateSearch .search-container',
        '.mapControls .control-group',
        '.coordinateTip',
        '.floating-actions',
        '.shortcuts-toggle'
      ];

      // 创建一个序列动画
      const timeline = gsap.timeline({
        defaults: { duration: 0.5, ease: 'back.out(1.7)' }
      });

      elements.forEach((selector, index) => {
        const element = document.querySelector(selector);
        if (element) {
          // 为每个元素设置初始状态
          gsap.set(element, { opacity: 0, y: 30 });

          // 添加到时间线，稍微错开时间
          timeline.to(element, {
            opacity: 1,
            y: 0,
            delay: index * 0.12
          }, index * 0.17);
        }
      });

      // 存储时间线引用
      this.animationTimelines.uiElements = timeline;
    },

    // 添加地图点击事件
    coordinateClick(e) {
      // 获取点击位置
      const position = [e.lnglat.getLng(), e.lnglat.getLat()];
      console.log('地图点击位置:', position);

      // 创建新标记（此函数内部会处理清除旧标记）
      this.createMarkerWithAnimation(position);

      // 坐标点赋值
      this.coordinatePosition = `${position[0]},${position[1]}`;

      // 添加到历史记录中
      if (!this.coordinateHistory.includes(this.coordinatePosition)) {
        this.coordinateHistory.unshift(this.coordinatePosition);
        // 限制历史记录最多5条
        if (this.coordinateHistory.length > 5) {
          this.coordinateHistory.pop();
        }

        // 历史记录添加动画
        this.$nextTick(() => {
          const firstHistoryItem = document.querySelector('.history-item:first-child');
          if (firstHistoryItem) {
            gsap.fromTo(firstHistoryItem,
              { opacity: 0, x: -20 },
              { opacity: 1, x: 0, duration: 0.5, ease: 'back.out(1.7)' }
            );
          }
        });
      }
    },

    // 添加查询事件
    coordinateList() {
      if (!this.coordinateSearch.trim()) {
        this.$message.warning('请输入搜索内容');
        return;
      }

      const _this = this;
      _this.isLoading = true;

      let placeSearch = new AMap.PlaceSearch({
        city: '',
        citylimit: false,
        showIndoorMap: false,
        pageSize: 10 // 增加结果数量
      });

      placeSearch.search(_this.coordinateSearch, function (status, result) {
        _this.isLoading = false;
        console.log('搜索状态：', status);
        console.log('搜索结果：', result);

        // 搜索成功时，result即是对应的匹配数据
        if (status === 'complete') {
          if (result.info === 'OK' && result.poiList && result.poiList.pois && result.poiList.pois.length > 0) {
            // 保存搜索结果
            _this.searchResults = result.poiList.pois;
            _this.showSearchResults = true;

            // 自动选择第一个结果
            let lng = result.poiList.pois[0].location.lng;
            let lat = result.poiList.pois[0].location.lat;

            // 平滑动画移动到目标位置
            _this.coordinateMap.setZoomAndCenter(13, [lng, lat], false);

            // 清除原有标记
            if (_this.coordinateMarker) {
              _this.coordinateMap.remove(_this.coordinateMarker);
            }

            // 添加带动画的新标记
            _this.createMarkerWithAnimation([lng, lat]);

            // 坐标点赋值
            _this.coordinatePosition = `${lng},${lat}`;

            // 添加到历史记录
            if (!_this.coordinateHistory.includes(_this.coordinatePosition)) {
              _this.coordinateHistory.unshift(_this.coordinatePosition);
              // 限制历史记录最多5条
              if (_this.coordinateHistory.length > 5) {
                _this.coordinateHistory.pop();
              }
            }

            // 搜索结果出现动画
            _this.$nextTick(() => {
              const searchResults = document.querySelector('.search-results');
              if (searchResults) {
                gsap.fromTo(searchResults,
                  { opacity: 0, y: -15, transformOrigin: 'top' },
                  { opacity: 1, y: 0, duration: 0.5, ease: 'back.out(1.7)' }
                );
              }
            });
          } else {
            _this.$message.warning('未找到相关位置信息');
            _this.searchResults = [];
            _this.showSearchResults = false;
          }
        } else {
          _this.$message.warning('搜索失败，请稍后重试');
          _this.searchResults = [];
          _this.showSearchResults = false;
        }
      });
    },

    // 选择搜索结果
    selectSearchResult(item) {
      // 添加选中动画效果
      const resultItems = document.querySelectorAll('.result-item');
      resultItems.forEach(resultItem => {
        if (resultItem.querySelector('.result-name').textContent === item.name) {
          gsap.to(resultItem, {
            backgroundColor: 'rgba(24, 103, 192, 0.5)',
            duration: 0.3,
            onComplete: () => {
              this.coordinatePosition = `${item.location.lng},${item.location.lat}`;
              this.showSearchResults = false;

              // 缓动到选择的位置
              this.coordinateMap.setZoomAndCenter(13, [item.location.lng, item.location.lat], false);

              // 清除原有标记，添加新标记
              if (this.coordinateMarker) {
                this.coordinateMap.remove(this.coordinateMarker);
              }
              this.createMarkerWithAnimation([item.location.lng, item.location.lat], true);

              // 添加到历史记录
              if (!this.coordinateHistory.includes(this.coordinatePosition)) {
                this.coordinateHistory.unshift(this.coordinatePosition);
                // 限制历史记录最多5条
                if (this.coordinateHistory.length > 5) {
                  this.coordinateHistory.pop();
                }
              }
            }
          });
        }
      });
    },

    // 保存当前坐标
    coordinateSave() {
      if (!this.coordinatePosition) {
        this.$message.warning('请选择经纬度！');
        return;
      }

      // 保存前的确认动画
      const saveBtn = document.querySelector('.floating-save-btn');

      if (this.animationTimelines.save) {
        this.animationTimelines.save.kill();
      }

      // 创建一个新的时间线
      const timeline = gsap.timeline();
      timeline.to(saveBtn, {
        scale: 1.15,
        boxShadow: '0 12px 24px rgba(24, 103, 192, 0.6)',
        duration: 0.4,
        ease: "elastic.out(1, 0.5)"
      }).to(saveBtn, {
        scale: 1,
        boxShadow: '0 4px 10px rgba(24, 103, 192, 0.3)',
        duration: 0.3,
        ease: "power1.out",
        onComplete: () => {
          // 成功提示动画
          this.$message({
            message: '坐标选择成功',
            type: 'success',
            duration: 1500
          });

          // 延迟关闭对话框，给用户更好的反馈体验
          setTimeout(() => {
            this.$emit('save', this.coordinatePosition);
            this.dialogVisible = false;
          }, 300);
        }
      });

      this.animationTimelines.save = timeline;
    },

    // 窗口关闭时销毁地图
    handleClose() {
      // 移除键盘快捷键监听
      document.removeEventListener('keydown', this.handleKeyboardShortcuts);

      // 清理所有动画
      this.clearAnimations();

      if (this.coordinateMap) {
        console.log('触发销毁');
        this.coordinateMap.off("click", this.coordinateClick);
        this.coordinateMap.destroy();
        this.coordinateMap = null;
      }
    },

    // 刷新坐标
    refreshCoordinates() {
      // 添加刷新按钮动画
      const refreshBtn = document.querySelector('.refresh-btn');

      if (refreshBtn) {
        gsap.to(refreshBtn, {
          rotation: 360,
          duration: 0.5,
          ease: "power1.inOut",
          onComplete: () => {
            gsap.set(refreshBtn, { rotation: 0 });
            this.coordinateInit();
          }
        });
      } else {
        this.coordinateInit();
      }
    },

    // 模拟加载进度
    simulateLoadingProgress() {
      // 重置进度条和状态
      this.loadingProgress = 0;
      this.isProgressAnimating = true;

      // 移除冗余日志，只保留关键信息
      console.log('开始加载...');

      const interval = setInterval(() => {
        // 优化进度增加逻辑 - 前70%快速加载，后30%稍慢
        let increment;
        
        if (this.loadingProgress < 70) {
          // 前70%快速增加
          increment = Math.floor(Math.random() * 12) + 5;
        } else {
          // 后30%增加较慢
          increment = Math.floor(Math.random() * 5) + 1;
        }
        
        this.loadingProgress += increment;

        if (this.loadingProgress >= 100) {
          this.loadingProgress = 100;
          clearInterval(interval);

          // 减少完成延迟时间
          setTimeout(() => {
            this.isProgressAnimating = false;
          }, 50);
        }
      }, 10); // 缩短更新间隔，使动画更流畅
    },

    // 处理键盘快捷键
    handleKeyboardShortcuts(e) {
      // 只在对话框显示时处理快捷键
      if (!this.dialogVisible) return;

      switch(e.key.toLowerCase()) {
        case 'escape':
          this.dialogVisible = false;
          break;
        case 'c':
          this.copyToClipboard(this.coordinatePosition);
          break;
        case 's':
          this.switchMapStyle('satellite');
          break;
        case 'm':
          this.switchMapStyle('standard');
          break;
        case '3':
          this.toggleViewMode();
          break;
        case 'r':
          this.refreshCoordinates();
          break;
        case 'h':
          this.showKeyboardShortcuts = !this.showKeyboardShortcuts;
          break;
        case 'f':
          this.toggleFullscreen();
          break;
      }
    },

    // 清除历史记录
    clearHistory() {
      // 确认提示动画
      const historyHeader = document.querySelector('.history-header');

      if (this.animationTimelines.clearHistory) {
        this.animationTimelines.clearHistory.kill();
      }

      // 创建一个新的时间线
      const timeline = gsap.timeline();

      // 历史记录项淡出动画
      const historyItems = document.querySelectorAll('.history-item');
      timeline.to(historyItems, {
        opacity: 0,
        x: -20,
        stagger: 0.1,
        duration: 0.3,
        ease: "power2.in"
      }).to(historyHeader, {
        backgroundColor: 'rgba(220, 53, 69, 0.3)',
        duration: 0.3,
        ease: "power1.out",
        onComplete: () => {
          this.coordinateHistory = [];
          this.$message({
            message: '历史记录已清除',
            type: 'success',
            duration: 1500
          });
        }
      }).to(historyHeader, {
        backgroundColor: 'transparent',
        duration: 0.3,
        ease: "power1.in"
      });

      this.animationTimelines.clearHistory = timeline;
    },

    // 切换全屏模式
    toggleFullscreen() {
      this.isFullscreen = !this.isFullscreen;

      // 添加全屏切换动画
      const mapContainer = this.$refs.mapContainer;
      const dialogEl = document.querySelector('.el-dialog');

      if (this.isFullscreen) {
        // 进入全屏模式
        gsap.to(dialogEl, {
          borderRadius: 0,
          duration: 0.3,
          ease: "power2.out"
        });

        gsap.to(mapContainer, {
          height: '95vh',
          borderRadius: 0,
          duration: 0.4,
          ease: "power2.out",
          onComplete: () => {
            // 触发地图重新计算大小
            this.coordinateMap && this.coordinateMap.resize();
          }
        });
      } else {
        // 退出全屏模式
        gsap.to(dialogEl, {
          borderRadius: '16px',
          duration: 0.3,
          ease: "power2.out"
        });

        gsap.to(mapContainer, {
          height: '90vh',
          borderRadius: '0 0 16px 16px',
          duration: 0.4,
          ease: "power2.out",
          onComplete: () => {
            // 触发地图重新计算大小
            this.coordinateMap && this.coordinateMap.resize();
          }
        });
      }
    }
  }
};
</script>

<style lang="scss" scoped>
.coordinateDialog {
  &.map-picker-dialog {
    ::v-deep .el-dialog {
      background: rgba(15, 18, 26, 0.92);
      border-radius: 16px;
      box-shadow: 0 16px 50px rgba(0, 0, 0, 0.5);
      backdrop-filter: blur(12px);
      -webkit-backdrop-filter: blur(12px);
      border: 1px solid rgba(255, 255, 255, 0.1);
      overflow: hidden;
      transition: all 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);

      .el-dialog__header {
        display: none;
      }

      .el-dialog__body {
        padding: 0;
        color: #fff;
        height: calc(95vh - 50px); // 减去一些边距
        overflow: hidden;
      }
    }
  }
}

.dialog-container {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.dialog-header {
  padding: 16px 20px;
  background: rgba(12, 14, 20, 0.95);
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  flex-shrink: 0; // 防止头部压缩

  .dialog-title-container {
    display: flex;
    align-items: center;

    .title-icon {
      color: #1867c0;
      margin-right: 10px;
      font-size: 18px;
      filter: drop-shadow(0 0 3px rgba(24, 103, 192, 0.5));
    }

    .dialog-title {
      color: #fff;
      margin: 0;
      font-weight: 500;
      font-size: 18px;
      letter-spacing: 0.5px;
      text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
    }
  }

  .header-actions {
    display: flex;
    align-items: center;

    .action-btn {
      background: rgba(255, 255, 255, 0.05);
      border: none;
      color: rgba(255, 255, 255, 0.7);
      font-size: 16px;
      cursor: pointer;
      margin-right: 10px;
      padding: 0;
      width: 34px;
      height: 34px;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      transition: all 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);

      &:hover {
        background: rgba(255, 255, 255, 0.15);
        color: #fff;
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
      }

      &:active {
        transform: translateY(0);
      }
    }

    .close-btn {
      background: rgba(255, 255, 255, 0.05);
      border: none;
      color: rgba(255, 255, 255, 0.7);
      font-size: 16px;
      cursor: pointer;
      padding: 0;
      width: 34px;
      height: 34px;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      transition: all 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);

      &:hover {
        background: rgba(255, 0, 0, 0.15);
        color: #ff4d4f;
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
      }

      &:active {
        transform: translateY(0);
      }
    }
  }
}

.coordinateMap {
  flex: 1; // 填充剩余空间
  position: relative;
  overflow: hidden;
  transition: all 0.4s cubic-bezier(0.34, 1.56, 0.64, 1);

  &.fullscreen {
    height: 95vh;
    border-radius: 0;
  }

  #coordinateBox {
    height: 100%;
    width: 100%;
    position: relative;
    z-index: 0;
    transition: all 0.8s cubic-bezier(0.16, 1, 0.3, 1);
  }

  .coordinateSearch {
    position: absolute;
    z-index: 10;
    top: 20px;
    left: 20px;
    width: 380px;
    box-sizing: border-box;

    .search-container {
      display: flex;
      align-items: center;
      background: rgba(15, 18, 26, 0.9);
      border-radius: 12px;
      padding: 10px 14px;
      box-shadow: 0 10px 20px rgba(0, 0, 0, 0.3), 0 0 0 1px rgba(255, 255, 255, 0.1);
      backdrop-filter: blur(12px);
      -webkit-backdrop-filter: blur(12px);
      transform-origin: top left;

      .search-icon {
        color: rgba(255, 255, 255, 0.7);
        margin-right: 10px;
        font-size: 16px;
      }

      .search-input {
        flex: 1;

        .el-input__inner {
          background: transparent;
          border: none;
          color: #fff;
          height: 40px;

          &::placeholder {
            color: rgba(255, 255, 255, 0.5);
          }
        }

        .el-input__suffix {
          .el-input__icon {
            color: rgba(255, 255, 255, 0.7);
          }

          .suggestion-count {
            background: linear-gradient(135deg, #1867c0, #3182ce);
            color: white;
            border-radius: 12px;
            min-width: 22px;
            height: 22px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            padding: 0 6px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
          }
        }
      }

      .search-button {
        margin-left: 8px;
        background: linear-gradient(135deg, #1867c0, #3182ce);
        border: none;
        border-radius: 8px;
        height: 40px;
        padding: 0 16px;
        transition: all 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);

        &:hover {
          transform: translateY(-2px) scale(1.02);
          background: linear-gradient(135deg, #1971d4, #4299e1);
          box-shadow: 0 6px 12px rgba(24, 103, 192, 0.5);
        }

        &:active {
          transform: translateY(0) scale(0.98);
          box-shadow: 0 2px 5px rgba(24, 103, 192, 0.3);
        }

        .btn-icon-right {
          margin-left: 5px;
          font-size: 12px;
          transition: transform 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
        }

        &:hover .btn-icon-right {
          transform: translateX(3px);
        }
      }
    }

    .search-results {
      margin-top: 10px;
      background: rgba(15, 18, 26, 0.95);
      border-radius: 12px;
      overflow: hidden;
      box-shadow: 0 10px 20px rgba(0, 0, 0, 0.3), 0 0 0 1px rgba(255, 255, 255, 0.1);
      backdrop-filter: blur(12px);
      -webkit-backdrop-filter: blur(12px);

      .results-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 12px 16px;
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);

        span {
          font-weight: 500;
          font-size: 14px;
        }

        .close-results {
          background: transparent;
          border: none;
          color: rgba(255, 255, 255, 0.7);
          font-size: 14px;
          cursor: pointer;
          width: 24px;
          height: 24px;
          display: flex;
          align-items: center;
          justify-content: center;
          border-radius: 50%;
          transition: all 0.2s ease;

          &:hover {
            background: rgba(255, 255, 255, 0.1);
            color: #fff;
          }
        }
      }

      .results-list {
        max-height: 300px;
        overflow-y: auto;

        &::-webkit-scrollbar {
          width: 6px;
        }

        &::-webkit-scrollbar-track {
          background: rgba(255, 255, 255, 0.05);
        }

        &::-webkit-scrollbar-thumb {
          background: rgba(255, 255, 255, 0.2);
          border-radius: 3px;
        }

        .result-item {
          display: flex;
          align-items: center;
          padding: 14px 16px;
          cursor: pointer;
          transition: all 0.3s ease;

          &:not(:last-child) {
            border-bottom: 1px solid rgba(255, 255, 255, 0.05);
          }

          &:hover {
            background: rgba(24, 103, 192, 0.2);
            transform: translateX(3px);
          }

          .result-icon {
            color: #1867c0;
            font-size: 18px;
            margin-right: 14px;
            flex-shrink: 0;
          }

          .result-info {
            .result-name {
              font-weight: 500;
              font-size: 14px;
              margin-bottom: 4px;
            }

            .result-address {
              font-size: 12px;
              color: rgba(255, 255, 255, 0.7);
            }
          }
        }
      }
    }

    .mapControls {
      position: absolute;
      z-index: 10;
      right: 20px;
      top: 20px;

      .control-group {
        display: flex;
        flex-direction: column;
        background: rgba(15, 18, 26, 0.9);
        border-radius: 12px;
        overflow: hidden;
        box-shadow: 0 10px 20px rgba(0, 0, 0, 0.3), 0 0 0 1px rgba(255, 255, 255, 0.1);
        backdrop-filter: blur(12px);
        -webkit-backdrop-filter: blur(12px);

        .control-btn {
          background: transparent;
          border: none;
          color: rgba(255, 255, 255, 0.7);
          height: 44px;
          width: 44px;
          padding: 0;
          margin: 0;
          border-radius: 0;
          transition: all 0.3s ease;

          &:not(:last-child) {
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
          }

          &:hover {
            background: rgba(255, 255, 255, 0.1);
            color: #fff;
            box-shadow: inset 0 0 0 1px rgba(255, 255, 255, 0.2);
          }

          &.active {
            background: linear-gradient(135deg, #1867c0, #3182ce);
            color: #fff;
            box-shadow: inset 0 0 10px rgba(0, 0, 0, 0.2);
          }
        }
      }
    }
  }

  .floating-actions {
    position: absolute;
    bottom: 20px;
    left: 50%;
    transform: translateX(-50%);
    z-index: 10;
    display: flex;
    gap: 12px;

    .floating-cancel-btn, .floating-save-btn {
      height: 50px;
      border-radius: 25px;
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 0 24px;
      font-size: 16px;
      font-weight: 500;
      box-shadow: 0 10px 20px rgba(0, 0, 0, 0.3);
      transition: all 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
      border: none;

      svg {
        margin-right: 8px;
        font-size: 16px;
      }
    }

    .floating-cancel-btn {
      background: rgba(15, 18, 26, 0.9);
      border: 1px solid rgba(255, 255, 255, 0.1);
      color: #fff;
      backdrop-filter: blur(12px);
      -webkit-backdrop-filter: blur(12px);

      &:hover {
        background: rgba(30, 34, 44, 0.95);
        transform: translateY(-3px);
        box-shadow: 0 12px 24px rgba(0, 0, 0, 0.4);
      }

      &:active {
        transform: translateY(0);
        box-shadow: 0 5px 10px rgba(0, 0, 0, 0.3);
      }
    }

    .floating-save-btn {
      background: linear-gradient(135deg, #1867c0, #3182ce);
      border: none;
      color: #fff;
      min-width: 160px;

      &:hover {
        background: linear-gradient(135deg, #1971d4, #4299e1);
        transform: translateY(-3px) scale(1.03);
        box-shadow: 0 12px 24px rgba(24, 103, 192, 0.5);
      }

      &:active {
        transform: translateY(0) scale(0.98);
        box-shadow: 0 5px 10px rgba(24, 103, 192, 0.3);
      }
    }
  }

  .coordinateTip {
    position: absolute;
    z-index: 10;
    bottom: 20px;
    right: 20px;
    width: 360px;
    padding: 16px;
    box-sizing: border-box;
    background: rgba(15, 18, 26, 0.9);
    border-radius: 12px;
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.3), 0 0 0 1px rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(12px);
    -webkit-backdrop-filter: blur(12px);
    color: #fff;

    .tip-container {
        .tip-header {
          display: flex;
          align-items: center;
          margin-bottom: 12px;

          .location-icon {
            color: #1867c0;
            margin-right: 8px;
            font-size: 16px;
            filter: drop-shadow(0 0 3px rgba(24, 103, 192, 0.5));
          }

          span {
            font-weight: 500;
            font-size: 16px;
          }

          .coordinate-actions {
            margin-left: auto;

            .el-button {
              background: rgba(255, 255, 255, 0.05);
              border: none;
              color: rgba(255, 255, 255, 0.7);
              font-size: 14px;
              cursor: pointer;
              padding: 0;
              margin-left: 8px;
              width: 30px;
              height: 30px;
              display: flex;
              align-items: center;
              justify-content: center;
              border-radius: 50%;
              transition: all 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);

              &:hover {
                background: rgba(255, 255, 255, 0.15);
                color: #fff;
                transform: translateY(-2px);
                box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
              }

              &:active {
                transform: translateY(0) rotate(180deg);
              }
            }
          }
        }

        .coordinate-formats {
          .format-group {
            margin-bottom: 12px;

            .format-label {
              font-size: 12px;
              color: rgba(255, 255, 255, 0.7);
              margin-bottom: 4px;
              display: flex;
              align-items: center;

              span {
                margin-right: 4px;
              }

              .info-icon {
                font-size: 12px;
                color: rgba(255, 255, 255, 0.5);
                cursor: help;
                transition: color 0.3s ease;

                &:hover {
                  color: rgba(255, 255, 255, 0.9);
                }
              }
            }

            .format-value {
              .el-input__inner {
                background: rgba(255, 255, 255, 0.07);
                border: 1px solid rgba(255, 255, 255, 0.15);
                border-radius: 8px;
                color: #fff;
                height: 36px;
                font-family: 'Roboto Mono', monospace;
                letter-spacing: 0.5px;
                transition: all 0.3s ease;

                &:focus {
                  background: rgba(255, 255, 255, 0.1);
                  border-color: rgba(24, 103, 192, 0.5);
                  box-shadow: 0 0 0 2px rgba(24, 103, 192, 0.2);
                }
              }

              .el-input-group__append {
                background: rgba(255, 255, 255, 0.1);
                border-color: rgba(255, 255, 255, 0.15);
                color: #fff;
                padding: 0 12px;
                transition: all 0.3s ease;

                .el-button {
                  background: transparent;
                  border: none;
                  color: rgba(255, 255, 255, 0.8);
                  padding: 0;
                  font-size: 14px;
                  transition: all 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);

                  &:hover {
                    color: #fff;
                    transform: scale(1.1);
                  }
                }
              }
            }
          }
        }
      }

    .history-section {
      margin-top: 16px;
      padding-top: 12px;
      border-top: 1px solid rgba(255, 255, 255, 0.1);

        .history-header {
          display: flex;
          align-items: center;
          margin-bottom: 8px;
          font-size: 14px;
          font-weight: 500;
          transition: background-color 0.3s ease;
          padding: 4px;
          border-radius: 4px;

          svg {
            margin-right: 6px;
            color: rgba(255, 255, 255, 0.7);
          }

          .clear-history {
            margin-left: auto;
            background: transparent;
            border: none;
            color: rgba(255, 255, 255, 0.6);
            font-size: 12px;
            cursor: pointer;
            padding: 0;
            width: 26px;
            height: 26px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 50%;
            transition: all 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);

            &:hover {
              background: rgba(220, 53, 69, 0.15);
              color: #dc3545;
              transform: translateY(-2px) rotate(5deg);
              box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
            }

            &:active {
              transform: translateY(0) rotate(0);
            }
          }
        }

        .history-list {
          .history-item {
            display: flex;
            align-items: center;
            padding: 8px 10px;
            border-radius: 8px;
            margin-bottom: 4px;
            cursor: pointer;
            transition: all 0.3s cubic-bezier(0.16, 1, 0.3, 1);
            font-size: 12px;

            &:hover {
              background: rgba(24, 103, 192, 0.15);
              transform: translateX(3px);
            }

            .history-icon {
              color: #1867c0;
              margin-right: 8px;
              font-size: 12px;
              filter: drop-shadow(0 0 1px rgba(24, 103, 192, 0.5));
            }

            .history-coords {
              color: rgba(255, 255, 255, 0.9);
              white-space: nowrap;
              overflow: hidden;
              text-overflow: ellipsis;
              font-family: 'Roboto Mono', monospace;
              letter-spacing: 0.5px;
            }
          }
        }
      }
    }
  }

  .floating-actions {
    position: absolute;
    bottom: 20px;
    left: 50%;
    transform: translateX(-50%);
    z-index: 10;
    display: flex;
    gap: 12px;

    .floating-cancel-btn, .floating-save-btn {
        height: 50px;
        border-radius: 25px;
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 0 24px;
        font-size: 16px;
        font-weight: 500;
        box-shadow: 0 10px 20px rgba(0, 0, 0, 0.3);
        transition: all 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
        border: none;

        svg {
          margin-right: 8px;
          font-size: 16px;
        }
      }

      .floating-cancel-btn {
        background: rgba(15, 18, 26, 0.9);
        border: 1px solid rgba(255, 255, 255, 0.1);
        color: #fff;
        backdrop-filter: blur(12px);
        -webkit-backdrop-filter: blur(12px);

        &:hover {
          background: rgba(30, 34, 44, 0.95);
          transform: translateY(-3px);
          box-shadow: 0 12px 24px rgba(0, 0, 0, 0.4);
        }

        &:active {
          transform: translateY(0);
          box-shadow: 0 5px 10px rgba(0, 0, 0, 0.3);
        }
      }

      .floating-save-btn {
        background: linear-gradient(135deg, #1867c0, #3182ce);
        border: none;
        color: #fff;
        min-width: 160px;

        &:hover {
          background: linear-gradient(135deg, #1971d4, #4299e1);
          transform: translateY(-3px) scale(1.03);
          box-shadow: 0 12px 24px rgba(24, 103, 192, 0.5);
        }

        &:active {
          transform: translateY(0) scale(0.98);
          box-shadow: 0 5px 10px rgba(24, 103, 192, 0.3);
        }
      }
    }

    .coordinateTip {
      position: absolute;
      z-index: 10;
      bottom: 20px;
      right: 20px;
      width: 360px;
      padding: 16px;
      box-sizing: border-box;
      background: rgba(15, 18, 26, 0.9);
      border-radius: 12px;
      box-shadow: 0 10px 20px rgba(0, 0, 0, 0.3), 0 0 0 1px rgba(255, 255, 255, 0.1);
      backdrop-filter: blur(12px);
      -webkit-backdrop-filter: blur(12px);
      color: #fff;

    .map-crosshair {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      pointer-events: none;
      z-index: 5;
      display: flex;
      justify-content: center;
      align-items: center;

      .crosshair-inner {
        position: relative;
        width: 30px;
        height: 30px;
        display: flex;
        justify-content: center;
        align-items: center;

        .crosshair-h, .crosshair-v {
          position: absolute;
          background: rgba(255, 255, 255, 0.9);
          box-shadow: 0 0 5px rgba(0, 0, 0, 0.6);
        }

        .crosshair-h {
          width: 20px;
          height: 2px;
          border-radius: 1px;
        }

        .crosshair-v {
          width: 2px;
          height: 20px;
          border-radius: 1px;
        }
      }
    }

    .loading-overlay {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: rgba(10, 12, 18, 0.85);
      display: flex;
      justify-content: center;
      align-items: center;
      z-index: 999;
      backdrop-filter: blur(8px);
      -webkit-backdrop-filter: blur(8px);
      pointer-events: auto;

      .loader {
        display: flex;
        flex-direction: column;
        align-items: center;
        color: #fff;

        .loader-spinner {
          font-size: 42px;
          margin-bottom: 20px;
          color: #1867c0;
          filter: drop-shadow(0 0 12px rgba(24, 103, 192, 0.6));
          animation: spinGlow 2s ease-in-out infinite;
          will-change: transform, filter;
        }

        span {
          margin-bottom: 24px;
          font-size: 18px;
          letter-spacing: 0.5px;
          font-weight: 300;
        }

        .loading-progress {
          width: 260px;
          height: 8px; // 增加高度
          background: rgba(0, 0, 0, 0.4); // 加深背景色
          border-radius: 4px;
          overflow: hidden;
          box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.5), 0 0 5px rgba(24, 103, 192, 0.3); // 增加阴影效果
          position: relative;
          margin-top: 5px; // 增加顶部间隔

          &::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(90deg, rgba(255,255,255,0) 0%, rgba(255,255,255,0.2) 50%, rgba(255,255,255,0) 100%); // 增强光泽效果
            animation: progressGlare 1.5s linear infinite;
          }

          .progress-bar {
            height: 100%;
            background: linear-gradient(90deg, #1867c0, #64b5f6, #1867c0); // 更丰富的渐变色
            background-size: 200% 100%;
            animation: gradientShift 2s ease infinite; // 添加渐变动画
            border-radius: 4px;
            box-shadow: 0 0 15px rgba(24, 103, 192, 0.9); // 增强发光效果
            transition: width 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
            position: relative;
            overflow: hidden;
          }

          .progress-text {
            margin-top: 10px;
            font-size: 16px;
            font-weight: 500;
            color: #fff;
            text-shadow: 0 1px 3px rgba(0, 0, 0, 0.5);
          }
        }
      }
    }

    .keyboard-shortcuts {
      position: absolute;
      bottom: 20px;
      left: 20px;
      padding: 16px;
      background: rgba(15, 18, 26, 0.9);
      border-radius: 12px;
      box-shadow: 0 10px 20px rgba(0, 0, 0, 0.3), 0 0 0 1px rgba(255, 255, 255, 0.1);
      backdrop-filter: blur(12px);
      -webkit-backdrop-filter: blur(12px);
      color: #fff;
      z-index: 10;

      .shortcuts-header {
        display: flex;
        align-items: center;
        margin-bottom: 16px;

        svg {
          color: #1867c0;
          margin-right: 8px;
          filter: drop-shadow(0 0 3px rgba(24, 103, 192, 0.5));
        }

        span {
          font-size: 16px;
          font-weight: 500;
        }

        .close-shortcuts {
          margin-left: auto;
          background: transparent;
          border: none;
          color: rgba(255, 255, 255, 0.7);
          font-size: 14px;
          cursor: pointer;
          padding: 0;
          width: 28px;
          height: 28px;
          display: flex;
          align-items: center;
          justify-content: center;
          border-radius: 50%;
          transition: all 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);

          &:hover {
            background: rgba(255, 255, 255, 0.1);
            color: #fff;
            transform: rotate(90deg);
          }
        }
      }

      .shortcuts-list {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        grid-gap: 12px;

        .shortcut-item {
          display: flex;
          align-items: center;

          .key {
            background: rgba(24, 103, 192, 0.3);
            border: 1px solid rgba(24, 103, 192, 0.5);
            border-radius: 6px;
            padding: 4px 8px;
            margin-right: 8px;
            font-size: 12px;
            font-weight: 500;
            color: #fff;
            min-width: 24px;
            text-align: center;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
            transition: all 0.3s ease;
          }

          .description {
            font-size: 13px;
            color: rgba(255, 255, 255, 0.8);
          }

          &:hover .key {
            background: rgba(24, 103, 192, 0.5);
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
          }
        }
      }
    }

    .shortcuts-toggle {
      position: absolute;
      bottom: 20px;
      left: 20px;
      background: rgba(15, 18, 26, 0.9);
      border: 1px solid rgba(255, 255, 255, 0.1);
      color: rgba(255, 255, 255, 0.8);
      font-size: 16px;
      cursor: pointer;
      padding: 0;
      width: 44px;
      height: 44px;
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: 50%;
      transition: all 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
      box-shadow: 0 6px 12px rgba(0, 0, 0, 0.3);
      z-index: 10;

      &:hover {
        background: rgba(24, 103, 192, 0.4);
        transform: translateY(-3px) rotate(10deg);
        box-shadow: 0 8px 16px rgba(0, 0, 0, 0.4);
      }

      &:active {
        transform: translateY(0) rotate(0);
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
      }
    }
  }

// Animation keyframes
@keyframes fadeInDown {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInRight {
  from {
    opacity: 0;
    transform: translateX(20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.2);
  }
  100% {
    transform: scale(1);
  }
}

@keyframes spinGlow {
  0% {
    transform: rotate(0deg);
    filter: drop-shadow(0 0 5px rgba(24, 103, 192, 0.5));
  }
  50% {
    filter: drop-shadow(0 0 20px rgba(24, 103, 192, 0.8));
  }
  100% {
    transform: rotate(360deg);
    filter: drop-shadow(0 0 5px rgba(24, 103, 192, 0.5));
  }
}

@keyframes gradientShift {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

// 自定义标记样式
.custom-marker {
  position: relative;
  width: 50px;  /* 确保有足够大小容纳所有元素 */
  height: 50px; /* 确保有足够大小容纳所有元素 */
  pointer-events: none; /* 确保不会阻止地图点击 */

  .marker-pin {
    width: 36px;
    height: 36px;
    background: linear-gradient(135deg, #1867c0, #3182ce);
    border-radius: 50% 50% 50% 0;
    transform: rotate(-45deg);
    margin-left: -18px;
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.4);
    border: 2px solid rgba(255, 255, 255, 0.9);
    position: absolute;
    top: 0;
    left: 50%;
    z-index: 10; /* 确保在最上层 */

    &:after {
      content: '';
      width: 16px;
      height: 16px;
      margin: 9px 0 0 9px;
      background: white;
      position: absolute;
      border-radius: 50%;
      box-shadow: inset 0 0 4px rgba(0, 0, 0, 0.3);
    }
  }

  .marker-pulse {
    background: rgba(24, 103, 192, 0.4);
    border-radius: 50%;
    height: 14px;
    width: 14px;
    position: absolute;
    left: 50%;
    top: 50%;
    margin: -18px 0 0 -7px;
    z-index: 5; /* 确保可见性 */
  }

  .marker-shadow {
    background: rgba(0, 0, 0, 0.4);
    border-radius: 50%;
    height: 14px;
    width: 40px;
    position: absolute;
    left: 50%;
    bottom: -7px;
    margin-left: -20px;
    filter: blur(3px);
    transform: rotateX(55deg);
    z-index: 1; /* 确保在底层 */
  }

  .marker-ripple {
    background: rgba(24, 103, 192, 0.15);
    border: 2px solid rgba(24, 103, 192, 0.8);
    border-radius: 50%;
    height: 60px;
    width: 60px;
    position: absolute;
    left: 50%;
    top: 50%;
    margin: -38px 0 0 -30px;
    z-index: 3; /* 确保可见性 */
  }

  .marker-glow {
    background: radial-gradient(ellipse at center, rgba(24, 103, 192, 0.6) 0%, rgba(24, 103, 192, 0) 70%);
    border-radius: 50%;
    height: 80px;
    width: 80px;
    position: absolute;
    left: 50%;
    top: 50%;
    margin: -48px 0 0 -40px;
    filter: blur(5px);
    z-index: 2; /* 确保可见性 */
  }

  /* 添加动画类 */
  .animate-pulse {
    animation: pulse 2s infinite;
    -webkit-animation: pulse 2s infinite;
  }

  .animate-ripple {
    animation: ripple 2.5s infinite;
    -webkit-animation: ripple 2.5s infinite;
  }

  .animate-glow {
    animation: glow 1.5s infinite alternate;
    -webkit-animation: glow 1.5s infinite alternate;
  }
}

/* 重新定义动画关键帧 */
@keyframes pulse {
  0% {
    transform: scale(0.1);
    -webkit-transform: scale(0.1);
    opacity: 0;
  }
  50% {
    opacity: 0.9;
  }
  100% {
    transform: scale(3);
    -webkit-transform: scale(3);
    opacity: 0;
  }
}

@keyframes ripple {
  0% {
    transform: scale(0.1);
    -webkit-transform: scale(0.1);
    opacity: 0.8;
  }
  50% {
    opacity: 0.4;
  }
  100% {
    transform: scale(3);
    -webkit-transform: scale(3);
    opacity: 0;
  }
}

@keyframes glow {
  0% {
    opacity: 0.5;
    filter: blur(3px) brightness(0.8);
    -webkit-filter: blur(3px) brightness(0.8);
  }
  100% {
    opacity: 0.9;
    filter: blur(6px) brightness(1.3);
    -webkit-filter: blur(6px) brightness(1.3);
  }
}
</style>

<!-- 全局样式 - 动画定义和复杂标记样式 -->
<style lang="scss">
// 动画关键帧定义
@keyframes fadeInDown {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInRight {
  from {
    opacity: 0;
    transform: translateX(20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes spinGlow {
  0% {
    transform: rotate(0deg);
    filter: drop-shadow(0 0 5px rgba(24, 103, 192, 0.5));
  }
  50% {
    filter: drop-shadow(0 0 20px rgba(24, 103, 192, 0.8));
  }
  100% {
    transform: rotate(360deg);
    filter: drop-shadow(0 0 5px rgba(24, 103, 192, 0.5));
  }
}

@keyframes progressGlare {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

@keyframes gradientShift {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

/* 补充完整的pulse动画定义 */
@keyframes pulse {
  0% {
    transform: scale(0.1) rotateX(55deg);
    -webkit-transform: scale(0.1) rotateX(55deg);
    opacity: 0;
  }
  50% {
    opacity: 0.9;
  }
  100% {
    transform: scale(3) rotateX(55deg);
    -webkit-transform: scale(3) rotateX(55deg);
    opacity: 0;
  }
}

/* 完整定义ripple动画，同时移除重复定义 */
@keyframes ripple {
  0% {
    transform: scale(0.1);
    -webkit-transform: scale(0.1);
    opacity: 0.8;
  }
  50% {
    opacity: 0.4;
  }
  100% {
    transform: scale(3);
    -webkit-transform: scale(3);
    opacity: 0;
  }
}

/* 补充完整的glow动画定义 */
@keyframes glow {
  0% {
    opacity: 0.5;
    filter: blur(3px) brightness(0.8);
    -webkit-filter: blur(3px) brightness(0.8);
  }
  100% {
    opacity: 0.9;
    filter: blur(6px) brightness(1.3);
    -webkit-filter: blur(6px) brightness(1.3);
  }
}

/* 补充完整的markerPulse动画定义，并修正其可能与pulse冲突的问题 */
@keyframes markerPulse {
  0% {
    transform: scale(0.1) rotateX(55deg);
    -webkit-transform: scale(0.1) rotateX(55deg);
    opacity: 0;
  }
  50% {
    opacity: 0.9;
  }
  100% {
    transform: scale(3) rotateX(55deg);
    -webkit-transform: scale(3) rotateX(55deg);
    opacity: 0;
  }
}

// 全局标记样式 - 包含完整的动画样式确保动态创建的标记能正确显示
.custom-marker {
  position: relative;
  width: 50px;  /* 确保有足够大小容纳所有元素 */
  height: 50px; /* 确保有足够大小容纳所有元素 */
  pointer-events: none; /* 确保不会阻止地图点击 */
  z-index: 10;

  .marker-pin {
    width: 36px;
    height: 36px;
    background: linear-gradient(135deg, #1867c0, #3182ce);
    border-radius: 50% 50% 50% 0;
    transform: rotate(-45deg);
    margin-left: -18px;
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.4);
    border: 2px solid rgba(255, 255, 255, 0.9);
    position: absolute;
    top: 0;
    left: 50%;
    z-index: 10; /* 确保在最上层 */

    &:after {
      content: '';
      width: 16px;
      height: 16px;
      margin: 9px 0 0 9px;
      background: white;
      position: absolute;
      border-radius: 50%;
      box-shadow: inset 0 0 4px rgba(0, 0, 0, 0.3);
    }
  }

  .marker-pulse {
    background: rgba(24, 103, 192, 0.4);
    border-radius: 50%;
    height: 14px;
    width: 14px;
    position: absolute;
    left: 50%;
    top: 50%;
    margin: -18px 0 0 -7px;
    z-index: 5; /* 确保可见性 */
  }

  .marker-shadow {
    background: rgba(0, 0, 0, 0.4);
    border-radius: 50%;
    height: 14px;
    width: 40px;
    position: absolute;
    left: 50%;
    bottom: -7px;
    margin-left: -20px;
    filter: blur(3px);
    transform: rotateX(55deg);
    z-index: 1; /* 确保在底层 */
  }

  .marker-ripple {
    background: rgba(24, 103, 192, 0.15);
    border: 2px solid rgba(24, 103, 192, 0.8);
    border-radius: 50%;
    height: 60px;
    width: 60px;
    position: absolute;
    left: 50%;
    top: 50%;
    margin: -38px 0 0 -30px;
    z-index: 3; /* 确保可见性 */
  }

  .marker-glow {
    background: radial-gradient(ellipse at center, rgba(24, 103, 192, 0.6) 0%, rgba(24, 103, 192, 0) 70%);
    border-radius: 50%;
    height: 80px;
    width: 80px;
    position: absolute;
    left: 50%;
    top: 50%;
    margin: -48px 0 0 -40px;
    filter: blur(5px);
    z-index: 2; /* 确保可见性 */
  }

  /* 动画类定义 */
  .animate-pulse {
    animation: pulse 2s infinite;
    -webkit-animation: pulse 2s infinite;
  }

  .animate-ripple {
    animation: ripple 2.5s infinite;
    -webkit-animation: ripple 2.5s infinite;
  }

  .animate-glow {
    animation: glow 1.5s infinite alternate;
    -webkit-animation: glow 1.5s infinite alternate;
  }
}

// 加载动画元素
.premium-map-picker {
  .loading-overlay {
    z-index: 999;

    .loader-spinner {
      animation: spinGlow 2s ease-in-out infinite;
    }
  }
}

// 进度条样式 - 全局定义确保可见性
.loading-overlay {
  position: absolute !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
  background: rgba(10, 12, 18, 0.85) !important;
  display: flex !important;
  justify-content: center !important;
  align-items: center !important;
  z-index: 999 !important;
  backdrop-filter: blur(8px) !important;
  -webkit-backdrop-filter: blur(8px) !important;
}

.loader {
  display: flex !important;
  flex-direction: column !important;
  align-items: center !important;
  color: white !important;
  z-index: 1000 !important;
}

.loader-spinner {
  font-size: 42px !important;
  margin-bottom: 20px !important;
  color: #1867c0 !important;
  filter: drop-shadow(0 0 12px rgba(24, 103, 192, 0.6)) !important;
}

.loading-progress {
  width: 260px !important;
  height: 10px !important;
  background: rgba(0, 0, 0, 0.6) !important;
  border-radius: 5px !important;
  overflow: hidden !important;
  box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.5), 0 0 5px rgba(24, 103, 192, 0.5) !important;
  position: relative !important;
  margin-top: 15px !important;
}

.loading-progress::after {
  content: '' !important;
  position: absolute !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
  background: linear-gradient(90deg, rgba(255,255,255,0) 0%, rgba(255,255,255,0.3) 50%, rgba(255,255,255,0) 100%) !important;
  animation: progressGlare 1.5s linear infinite !important;
}

.progress-bar {
  height: 100% !important;
  background: linear-gradient(90deg, #1867c0, #64b5f6, #1867c0) !important;
  background-size: 200% 100% !important;
  animation: gradientShift 2s ease infinite !important;
  border-radius: 5px !important;
  box-shadow: 0 0 15px rgba(24, 103, 192, 0.9) !important;
  transition: width 0.3s cubic-bezier(0.34, 1.56, 0.64, 1) !important;
}

.progress-text {
  margin-top: 12px !important;
  font-size: 18px !important;
  font-weight: 500 !important;
  color: white !important;
  text-shadow: 0 1px 3px rgba(0, 0, 0, 0.5) !important;
}

/* 移除调试样式 */
.coordinate-loading-mask {
  /* 额外的选择器增强特异性 */
  background: rgba(0, 0, 0, 0.9) !important; /* 更暗的背景 */
  pointer-events: auto !important; /* 确保可点击 */
}

.map-loader {
  /* 添加更明显的背景 */
  background: rgba(20, 20, 30, 0.8) !important;
  padding: 30px !important;
  border-radius: 15px !important;
  min-width: 300px !important;
  border: 1px solid rgba(24, 103, 192, 0.5) !important;
}

.map-spinner {
  /* 让spinner更大更明显 */
  font-size: 60px !important;
}

.loading-text {
  font-size: 22px !important;
  margin: 15px 0 !important;
  font-weight: bold !important;
}

.map-progress {
  /* 更大更明显的进度条 */
  height: 15px !important;
  border: 2px solid rgba(255, 255, 255, 0.2) !important;
}

/* 确保进度条在不同浏览器中显示 */
.map-progress .progress-bar {
  background: #1867c0 !important; /* 单色以防渐变不显示 */
  min-height: 100% !important;
  display: block !important;
}

.map-progress-text {
  font-size: 22px !important;
  color: #64b5f6 !important;
}
</style>
