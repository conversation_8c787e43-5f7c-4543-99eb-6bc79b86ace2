<template>
  <div class="detailBox">
    <el-form ref="formRef" :model="formParams" :disabled="processIsApprove === '1'" :rules="formRules" label-width="125px" label-position="right">
      <div class="infoBox" style="margin-bottom: 20px;" v-loading="formLoading">
        <div class="infoTitle">
          基础信息
        </div>
        <el-row :gutter="12">
          <el-col :span="12">
            <el-form-item label="管养单位" prop="maintenanceUnitId">
              <el-select ref="formMainRef" v-model="formParams.maintenanceUnitId" placeholder="请选择管养单位"
                         style="width: 100%;" @change="formMaintenanceChange('1')">
                <el-option v-for="item in formMaintenanceRenderList" :label="item.label" :value="item.id"
                           :key="item.id" style="display: none;"></el-option>
                <el-tree
                  :data="formMaintenanceList"
                  :props="{ children: 'children', label: 'label', value: 'id' }"
                  :expand-on-click-node="false"
                  highlight-current
                  default-expand-all
                  @node-click="formMainItemClick"
                >
                </el-tree>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item prop="roadSectionId">
              <span slot="label">
                <el-tooltip content="选择管养单位后带出" placement="top">
                  <i class="el-icon-question"></i>
                </el-tooltip>
                路段名称
              </span>
              <el-select v-model="formParams.roadSectionId" placeholder="请选择路段" style="width: 100%;" @change="formMaintenanceChange('2')">
                <el-option v-for="item in formRoadSectionList" :label="item.maintenanceSectionName" :value="item.maintenanceSectionId" :key="item.maintenanceSectionId"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="12">
          <el-col :span="8">
            <el-form-item prop="routerNum">
              <span slot="label">
                <el-tooltip content="选择路段后带出" placement="top">
                  <i class="el-icon-question"></i>
                </el-tooltip>
                路线名称
              </span>
              <el-select v-model="formParams.routerNum" placeholder="请选择路线" style="width: 100%;" @change="formMaintenanceChange('3')">
                <el-option v-for="item in formRouteList" :label="`${item.routeName}（${item.routeCode}）`" :value="item.routeCode" :key="item.routeCode"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item prop="technicalGrade">
              <span slot="label">
                <el-tooltip content="选择路段后带出" placement="top">
                  <i class="el-icon-question"></i>
                </el-tooltip>
                技术等级
              </span>
              <!-- <el-input readonly v-model="formParams.technicalGrade" placeholder="请输入技术等级" /> -->
              <el-select disabled v-model="formParams.technicalGrade" placeholder="请选择技术等级" style="width: 100%;">
                <el-option v-for="dict in dict.type.sys_route_grade" :label="dict.label" :value="dict.value" :key="dict.value"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item prop="technicalGrade">
              <span slot="label">
                风险等级
              </span>
               <el-input disabled v-model="formParams.riskGrade"/>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="12">
          <el-col :span="8">
            <el-form-item prop="">
              <span slot="label">
                <el-tooltip content="省级区划代码" placement="top">
                  <i class="el-icon-question"></i>
                </el-tooltip>
                省级区划
              </span>
              <el-select v-model="divisionsProvincial" disabled style="width: 100%;">
                <el-option v-for="item in divisionsProvincialList" :label="`${item.label}（${item.id}）`" :value="item.id" :key="item.id"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item prop="">
              <span slot="label">
                <el-tooltip content="市级区划代码" placement="top">
                  <i class="el-icon-question"></i>
                </el-tooltip>
                市级区划
              </span>
              <el-select v-model="divisionsMunicipal" clearable style="width: 100%;" @change="formChangeAreaCode">
                <el-option v-for="item in divisionsMunicipalList" :label="`${item.label}（${item.id}）`" :value="item.id" :key="item.id"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item prop="areaCode">
              <span slot="label">
                <el-tooltip content="县级区划代码，用于生成灾害编码" placement="top">
                  <i class="el-icon-question"></i>
                </el-tooltip>
                县级区划
              </span>
              <el-select v-model="formParams.areaCode" clearable style="width: 100%;">
                <el-option v-for="item in areaCodeList" :label="`${item.label}（${item.id}）`" :value="item.id" :key="item.id"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="12">
          <el-col :span="12">
            <el-form-item prop="disasterName">
              <span slot="label">
                <el-tooltip :content="`灾害名称，依据“中心桩号+${formParams.disasterType.includes('其它') ? '其它' : formParams.disasterType}” 保存后自动生成（例如“K0+100${formParams.disasterType.includes('其它') ? '其它' : formParams.disasterType}”）`" placement="top">
                  <i class="el-icon-question"></i>
                </el-tooltip>
                灾害名称
              </span>
              <el-input v-model="formParams.disasterName" disabled placeholder="请输入灾害名称" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item prop="disasterNum">
              <span slot="label">
                <el-tooltip :content="`灾害编号，按“路线编号+县级行政区划代码+${formRiskTypeShort}+四位数字”编号（例如“S81520303CT0001”）自动生成`" placement="top">
                  <i class="el-icon-question"></i>
                </el-tooltip>
                灾害编号
              </span>
              <el-input v-model="formParams.disasterNum" disabled placeholder="请输入灾害编号" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="12">
          <el-col :span="12">
            <el-form-item prop="startLatitudeAndLongitude">
              <span slot="label">
                <el-tooltip content="起点经纬度利用数据采集系统移动端 APP 采集" placement="top">
                  <i class="el-icon-question"></i>
                </el-tooltip>
                起点经纬度
              </span>
              <el-input v-model="formParams.startLatitudeAndLongitude" placeholder="请输入起点经纬度，以英文逗号分隔" >
                <el-button slot="append" icon="el-icon-location" @click="openCoordinateDialog('start')">坐标拾取</el-button>
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item prop="endLatitudeAndLongitude">
              <span slot="label">
                <el-tooltip content="止点经纬度利用数据采集系统移动端 APP 采集" placement="top">
                  <i class="el-icon-question"></i>
                </el-tooltip>
                止点经纬度
              </span>
              <el-input v-model="formParams.endLatitudeAndLongitude" placeholder="请输入止点经纬度，以英文逗号分隔" >
                <el-button slot="append" icon="el-icon-location" @click="openCoordinateDialog('end')">坐标拾取</el-button>
              </el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="12">
          <el-col :span="12">
            <el-form-item prop="pileStartNum">
              <span slot="label">
                <el-tooltip content="起止点桩号，根据现场调查填写起点桩号和止点桩号（例如起点桩号 K0+100，止点桩号 K0+200）" placement="top">
                  <i class="el-icon-question"></i>
                </el-tooltip>
                起点桩号
              </span>
              <!-- <PileInput v-model="formParams.pileStartNum" placeholder="请输入起点桩号"></PileInput> -->
              <div style="width: 100%; display: flex; flex-direction: row; flex-wrap: nowrap;">
                <el-tag
                  type="info"
                  effect="plain"
                  size="medium"
                >
                  K
                </el-tag>
                <el-input v-model="pileStartK" controls-position="right" style="width: 50%;" placeholder="请输入起点桩号" />
                <el-tag
                  type="info"
                  effect="plain"
                  size="medium"
                >
                  +
                </el-tag>
                <el-input v-model="pileStartAdd" controls-position="right" style="width: 50%;" placeholder="请输入起点桩号" />
              </div>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item prop="pileEndNum">
              <span slot="label">
                <el-tooltip content="起止点桩号，根据现场调查填写起点桩号和止点桩号（例如起点桩号 K0+100，止点桩号 K0+200）" placement="top">
                  <i class="el-icon-question"></i>
                </el-tooltip>
                终点桩号
              </span>
              <!-- <PileInput v-model="formParams.pileEndNum" placeholder="请输入终点桩号"></PileInput> -->
              <div style="width: 100%; display: flex; flex-direction: row; flex-wrap: nowrap;">
                <el-tag
                  type="info"
                  effect="plain"
                  size="medium"
                >
                 K
                </el-tag>
                <el-input v-model="pileEndK" controls-position="right" style="width: 50%;" placeholder="请输入终点桩号" />
                <el-tag
                  type="info"
                  effect="plain"
                  size="medium"
                >
                  +
                </el-tag>
                <el-input v-model="pileEndAdd" controls-position="right" style="width: 50%;" placeholder="请输入终点桩号" />
              </div>
            </el-form-item>
          </el-col>

        </el-row>

      </div>

      <Crumble v-if="formParams.disasterType === '崩塌'" ref="formSubRef"/>

      <LandSlide v-if="formParams.disasterType === '滑坡'" ref="formSubRef"/>

      <DebrisFlow v-if="formParams.disasterType === '泥石流'" ref="formSubRef"/>

      <SubSide v-if="formParams.disasterType === '沉陷与塌陷'" ref="formSubRef"/>

      <WaterDestruction v-if="formParams.disasterType === '水毁'" ref="formSubRef"/>

      <Other v-if="formParams.disasterType.includes('其它')" ref="formSubRef"/>

    </el-form>

    <el-dialog title="坐标拾取" class="coordinateDialog" :visible.sync="coordinateDialog" @close="coordinateClose" :close-on-press-escape="false" :close-on-click-modal="false" append-to-body>
      <div class="coordinateMap">
        <div id="coordinateBox" v-if="coordinateDialog"></div>
        <div class="coordinateSearch">
          <el-input v-model="coordinateSearch" clearable placeholder="请输入地名" @keyup.enter.native="coordinateList" />
          <el-button type="primary" icon="el-icon-search" style="margin-left: 5px;" @click="coordinateList">查询</el-button>
        </div>
        <div class="coordinateTip">
          <el-row>
            <el-col :span="24" style="padding: 5px 0px;">
              当前经纬度：
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="24">
              <el-input v-model="coordinatePosition" readonly />
            </el-col>
          </el-row>
        </div>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="coordinateSave">保 存</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
// -------------------- 引入 --------------------

// 组件
// import PileInput from '@/components/PileInput/index.vue' // 桩号（表单）
import AMapLoader from '@amap/amap-jsapi-loader'; // 高德地图

// 子表
import Crumble from './crumble/index.vue' // 崩塌
import LandSlide from './landslide/index.vue' // 滑坡
import DebrisFlow from './debrisFlow/index.vue' // 泥石流
import SubSide from './subside/index.vue' // 沉陷与塌陷
import WaterDestruction from './waterDestruction/index.vue' // 水毁
import Other from './other/index.vue' // 其它

// API
// import { updateRiskCrumble } from "@/api/disaster/risk/crumble" // 崩塌接口
// import { updateRiskLandslide } from "@/api/disaster/risk/landslide" // 滑坡接口
// import { updateRiskDebrisFlow } from "@/api/disaster/risk/debrisFlow" // 泥石流接口
// import { updateRiskSubside } from "@/api/disaster/risk/subside" // 沉陷与塌陷接口
// import { updateRiskWaterDestruction } from "@/api/disaster/risk/waterDestruction" // 水毁接口
// import { updateRiskOther } from "@/api/disaster/risk/other" // 其它接口
import { queryPageRiskForm } from '@/api/disaster/risk/risk' // 流程数据
import { deptTreeSelect } from '@/api/tmpl' // 管养单位
import { listByMaintenanceSectionId, getMaintenanceSectionListAll } from '@/api/baseData/common/routeLine' // 路段数据
import { getTreeByEntity } from '@/api/system/geography' // 区划接口
import { submitTask } from '@/api/process/task/task'
import {listMaintenanceSectionAll} from "@/api/system/maintenanceSection"; // 流程相关


export default {
  name: "RiskForm",
  // 数据字典
  dicts: [
    'sys_route_grade', // 技术等级
  ],
  // 组件
  components: {
    // PileInput,
    Crumble,
    LandSlide,
    DebrisFlow,
    SubSide,
    WaterDestruction,
    Other,
  },

  // -------------------- 变量 --------------------
  data() {
    return {
      /**
       * 表单详情相关
       */
        formParams: { // 表单字段
        id: '', // 主表ID
        areaCode: '', // 县级区划编码
        disasterNum: '', // 灾害编号
        disasterName: '', // 灾害名称
        disasterType: '', // 灾害类型
        routerNum: '', // 路线编号
        routerName: '', // 路线名称
        startLatitudeAndLongitude: '', // 经纬度（起）
        endLatitudeAndLongitude: '', // 经纬度（止）
        technicalGrade: '', // 技术等级
        pileStartNum: '', // 桩号（起）
        pileEndNum: '', // 桩号（终）
        managementOfficeId: '',
        managementOfficeName: '',
        propertyUnitName: '',
        propertyUnitId: '',
        propertyUnitType: '',
        riskScore: 0,
        maintenanceUnitId: '', // 管养单位ID
        maintenanceUnitName: '', // 管养单位名称
        roadSectionId: '', // 路段ID
        roadSectionName: '', // 路段名称
        status: '', // 流程状态
        // 其它字段
        createBy: '',
        createTime: '',
        delFlag: '',
        dataSources: '',
        processId: '',
        riskGrade: '',
        updateBy: '',
        updateTime: '',
        remark: '',
        createName: '',
        createUnit: '',
      },
      formLoading: false, // 表单加载
      formRules: { // 表单验证规则
        maintenanceUnitId: [
          { required: true, message: "管养单位不能为空", trigger: "blur" }
        ],
        roadSectionId: [
          { required: true, message: "路段信息不能为空", trigger: "blur" }
        ],
        technicalGrade: [
          { required: true, message: "技术等级不能为空", trigger: "blur" }
        ],
        areaCode: [
          { required: true, message: "县级区划代码不能为空", trigger: "blur" }
        ],
        // disasterName: [
        //   { required: true, message: "灾害名称不能为空", trigger: "blur" }
        // ],
        routerName: [
          { required: true, message: "路线名称不能为空", trigger: "blur" }
        ],
        // startLatitudeAndLongitude: [
        //   { required: true, message: "起点经纬度不能为空", trigger: "blur" }
        // ],
        // endLatitudeAndLongitude: [
        //   { required: true, message: "终点点经纬度不能为空", trigger: "blur" }
        // ],
        // pileStartNum: [
        //   { required: true, message: "桩号(起)不能为空", trigger: "blur" }
        // ],
        // pileEndNum: [
        //   { required: true, message: "桩号(终)不能为空", trigger: "blur" }
        // ],
      },
      formRiskTypeShort: '', // 表单灾害类型简称
      formMaintenanceList: [], // 管养单位列表
      formMaintenanceRenderList: [], // 管养单位渲染列表
      formRoadSectionList: [], // 路段列表
      formRouteList: [], // 路线列表

      /**
       * 桩号相关
       */
      pileStartK: '',
      pileStartAdd: '',
      pileEndK: '',
      pileEndAdd: '',

      /**
       * 流程相关
       */
      processParmas: { // 表单提交参数
        taskId: '', // 流程ID
        variables: {
          comment: '', // 审批意见
          operatingRecord: '', // 操作记录
          approved: true, // 是否通过
          variablesJson: [], // 附加变量
        },
        disasterType: '', // 表单类型
        formData: {}, // 表单参数
      },
      processIsApprove: '0', // 流程是否可审批

      // 字段备份
      formData: {}, // 主表字段备份
      formSubData: {}, // 子表字段备份
      formDataContrast: { // 主表对照中文
        id: '主表ID',
        createName: '采集人昵称',
        createUnit: '采集人单位',
        areaCode: '县级区划编码',
        disasterNum: '灾害编号',
        disasterName: '灾害名称',
        disasterType: '灾害类型',
        routerNum: '路线编号',
        routerName: '路线名称',
        startLatitudeAndLongitude: '经纬度（起）',
        endLatitudeAndLongitude: '经纬度（止）',
        technicalGrade: '技术等级',
        pileStartNum: '桩号（起）',
        pileEndNum: '桩号（终）',
        managementOfficeId: '管理处ID',
        managementOfficeName: '管理处名称',
        propertyUnitName: '产权单位名称',
        propertyUnitId: '产权单位ID',
        propertyUnitType: '产权单位类型',
        maintenanceUnitId: '管养单位ID',
        maintenanceUnitName: '管养单位名称',
        roadSectionId: '路段ID',
        roadSectionName: '路段名称',
        status: '流程状态',
        createBy: '创建者',
        createTime: '创建时间',
        delFlag: '删除标志',
        processId: '流程实例ID',
        dataSources: '数据来源',
        riskGrade: '灾害等级',
        updateBy: '更新者',
        updateTime: '更新时间',
        remark: '备注',

        // 子表
        slopeComposition: '坡体结构',
        slopeHeight: '坡体高度',
        slopeGradient: '坡度',
        hisDisasterHappened: '是否发生灾害（0是，1否）',
        hisLastHappenedTime: '灾害最近发生时间',
        hisHappenedFrequency: '灾害发生频率（近十年发生次数）',
        hisRouteStatus: '断通情况',
        hisHazardTarget: '危害对象',
        hisHazardTargetRemark: '危害对象其他',
        hisHazardDegree: '危害程度',
        hisDisasterDisposal: '灾害处治情况',
        curCracked: '是否有裂缝（0是，1否）',
        curCrackLocation: '裂缝位置',
        curCrackLocationRemark: '裂缝位置其他',
        curDamaged: '是否破损（0是，1否）',
        curDamagedDrainage: '排水',
        curDamagedDrainageRemark: '排水其他',
        curDamagedSlopeSurface: '坡面防护',
        curDamagedSlopeSurfaceRemark: '坡面防护其他',
        curDamagedRiverBank: '沿河防护',
        curDamagedRiverBankRemark: '沿河防护其他',
        curDamagedSupportFacility: '支挡设施',
        curDamagedSupportFacilityRemark: '支挡设施其他',
        curDamagedBridge: '桥梁',
        curDamagedTunnel: '隧道',
        curDamagedOther: '其它',
        curDisasterDevelopment: '灾害发育情况',
        riskDescription: '风险点特征描述',
        imgIds: '示意图及图片',
        // 泥石流
        hisDebrisFlowType: '泥石流类型',
        hisScopeLength: '影响范围长度',
        hisScopeWidth: '影响范围宽度',
        hisScopeThickness: '影响范围厚度',
        // 沉陷及塌陷
        hisScopeLength: '规模（长度）',
        hisScopeWidth: '规模（长度）',
        hisTrigger: '触发因素',
        hisTriggerRemark: '触发因素备注',
        // 水毁
        hisScopeLength: '水毁规模（长度）',
        hisScopeWidth: '水毁规模（宽度）',
        hisGeomorphicFeature: '地形特征',
        hisDamageLocation: '水毁部位',
        hisDamageType: '水毁类型',
        hisDamageTypeRemark: '水毁类型备注',
      },

      /**
       * 区划相关
       */
      divisionsProvincial: '53', // 省级区划
      divisionsProvincialList: [ // 省级区划列表
        {
          disabled: true,
          id: "53",
          label: "云南省",
          level: 1
        }
      ],
      divisionsMunicipal: '', // 市级区划
      divisionsMunicipalList: [], // 市级区划列表
      areaCodeList: [], // 县级区划列表

      /**
       * 坐标相关
       */
      coordinateDialog: false, // 坐标dialog
      coordinateMap: null, // 地图实例
      coordinateMarker: null, // 地图标记
      coordinateType: 'start', // 坐标类型
      coordinatePosition: '', // 坐标经纬度
      coordinateSearch: '', // 坐标查询

    }
  },
  // -------------------- 方法 --------------------
  mounted () {
    this.initPage()
  },
  methods: {
    /**
     * 页面相关
     */
    // 初始化页面
    initPage() {
      // 流程提交参数赋值
      this.processParmas.taskId = this.$route.query.taskId
      // 流程类型
      this.processIsApprove = this.$route.query.isApprove
      // 查询流程表单数据
      this.queryList(this.$route.query.businessKey)
      // 获取管养单位数据
      this.queryMaintenanceList()

    },

    /**
     * 查询相关
     */

    // 获取流程数据
    async queryList(id) {
      this.formLoading = true
      await queryPageRiskForm(id).then((res) => {
        if (res.code === 200) {
          // 备份当前表单数据
          this.formData = JSON.parse(JSON.stringify(res.data.disasterRisk))
          this.formSubData = JSON.parse(JSON.stringify(res.data.data))
          // 表单初始化
          this.formInit(res.data.disasterRisk).then(() => {
            this.$refs.formSubRef.formInit('edit', res.data.data, this.processIsApprove)
          })
        }
        this.formLoading = false
      }).catch(() => {
        this.formLoading = false
      })
    },

    // 获取管养单位
    queryMaintenanceList() {
      let vo = {
        deptTypeList: [1, 3, 4],
        types: 100
      }
      deptTreeSelect(vo).then((res) => {
        if (res.code === 200) {
          this.formMaintenanceList = res.data
          this.handleMainRender(this.formMaintenanceList)
        }
      }).catch((err) => {
        this.$message.error(err)
        this.formMaintenanceList = []
        this.formMaintenanceRenderList = []
      })
    },

    // 管养单位渲染列表处理
    handleMainRender(data) {
      data.forEach(item => {
        this.formMaintenanceRenderList.push(item)
        if (item.children) {
          this.handleMainRender(item.children)
        }
      })
    },

    // 获取路段信息
    queryMaintenanceSectionList(val) {
      listMaintenanceSectionAll({departmentIdList: val}).then((res) => {
        if (res.code === 200) {
          this.formRoadSectionList = res.data
        }
      }).catch((err) => {
        this.$message.error(err)
        this.formRoadSectionList = []
      })
    },

    // 获取路线信息
    queryRouterList(val) {
      listByMaintenanceSectionId({ maintenanceSectionId: val }).then(res => {
        if (res.code == 200) {
          this.formRouteList = res.data
        }
      }).catch((err) => {
        this.$message.error(err)
        this.formRouteList = []
      })
    },
    // 获取区划树
    async queryDivisionsTree(level) {
      let vo = {}
      switch (level) {
        case 2:
          vo = {
            supCode: this.divisionsProvincial
          }
          break;

        case 3:
          vo = {
            supCode: this.divisionsMunicipal
          }
          break;
      }

      const res = await getTreeByEntity(vo)
      if (res) {
        switch (level) {
          case 2:
            this.divisionsMunicipalList = res.data
            break;
          case 3:
            this.areaCodeList = res.data
            break;
        }
      }
    },


    /**
     * 表单相关
     */
    // 表单初始化
    formInit(item) {
      return new Promise(async (resolve) => {
        // 风险等级
        this.formRiskGrade = item.riskGrade ? item.riskGrade : '暂无数据'
        // 表单赋值
        this.formParams = {
          id: item.id, // 主表ID
          areaCode: '', // 县级区划编码
          disasterNum: item.disasterNum, // 灾害编号
          disasterName: item.disasterName, // 灾害名称
          disasterType: item.disasterType, // 灾害类型
          routerNum: item.routerNum, // 路线编号
          routerName: item.routerName, // 路线名称
          startLatitudeAndLongitude: item.startLatitudeAndLongitude, // 起点经纬度
          endLatitudeAndLongitude: item.endLatitudeAndLongitude, // 止点经纬度
          technicalGrade: item.technicalGrade, // 技术等级
          pileStartNum: item.pileStartNum, // 桩号（起）
          pileEndNum: item.pileEndNum, // 桩号（终）
          managementOfficeId: item.managementOfficeId,
          managementOfficeName: item.managementOfficeName,
          propertyUnitName: item.propertyUnitName,
          propertyUnitId: item.propertyUnitId,
          propertyUnitType: item.propertyUnitType,
          maintenanceUnitId: item.maintenanceUnitId, // 管养单位ID
          maintenanceUnitName: item.maintenanceUnitName, // 管养单位名称
          roadSectionId: item.roadSectionId, // 路段ID
          roadSectionName: item.roadSectionName, // 路段名称
          status: item.status, // 流程状态
          riskScore: item.riskScore,
          // 其它字段
          createBy: item.createBy,
          createName: item.createName,
          createUnit: item.createUnit,
          createTime: item.createTime,
          delFlag: item.delFlag,
          dataSources: item.dataSources,
          processId: item.processId,
          riskGrade: item.riskGrade,
          updateBy: item.updateBy,
          updateTime: item.updateTime,
          remark: item.remark,
        }
        // 桩号赋值
        if (item.pileStartNum) {
          this.pileStartK = item.pileStartNum.split('+')[0].split('K')[1]
          this.pileStartAdd = item.pileStartNum.split('+')[1]
        }
        if (item.pileEndNum) {
          this.pileEndK = item.pileEndNum.split('+')[0].split('K')[1]
          this.pileEndAdd = item.pileEndNum.split('+')[1]
        }
        // 重置路段/路线下拉
        this.formRoadSectionList = []
        this.formRoadSectionList.push({
          maintenanceSectionName: item.roadSectionName,
          maintenanceSectionId: item.roadSectionId
        })
        this.formRouteList = []
        this.formRouteList.push({routeName: item.routerName, routeCode: item.routerNum})
        // 清除主表表单验证
        this.$refs.formRef.clearValidate()
        // 初始化子表
        this.$nextTick(() => {
          this.$refs.formSubRef.initPage(this.formType, this.formRiskId, item.disasterType)
          this.$refs.formSubRef.$refs.subFormRef.clearValidate()
        })

        // 灾害编号规则英文缩写
        switch (this.formParams.disasterType) {
          case '崩塌':
            this.formRiskTypeShort = 'BT'
            break;

          case '滑坡':
            this.formRiskTypeShort = 'HP'
            break;

          case '泥石流':
            this.formRiskTypeShort = 'NL'
            break;

          case '沉陷与塌陷':
            this.formRiskTypeShort = 'CT'
            break;

          case '水毁':
            this.formRiskTypeShort = 'SH'
            break;

          default:
            this.formRiskTypeShort = 'QT'
            break;
        }

        // 获取县级区划编码
        let area = item.disasterNum.split(this.formRiskTypeShort)[0]
        console.log('area：', area)
        this.formParams.areaCode = area.substr(-6)
        if (this.formParams.areaCode === this.formParams.routerNum) {
          this.formParams.areaCode = ''
        }

        // 获取区划数据（市级）
        await this.queryDivisionsTree(2)
        // 根据县级获取市级编码
        let check = false
        for (let i = 0; i < this.divisionsMunicipalList.length; i++) {
          if (check) break
          for (let j = 0; j < this.divisionsMunicipalList[i].children.length; j++) {
            if (this.divisionsMunicipalList[i].children[j].id === this.formParams.areaCode) {
              this.divisionsMunicipal = this.divisionsMunicipalList[i].id
              this.areaCodeList = this.divisionsMunicipalList[i].children
              check = true
              break
            }
          }
        }
        resolve(true)
      })
    },

    // 提交表单
    async formSubmit(formName, statusNum) {
      return new Promise((resolve, reject) => {
        this.$refs[formName].validate((valid) => {
          this.$refs.formSubRef.$refs.subFormRef.validate(async (validSub) => {
            if(valid && validSub) {
              this.formBtnLoading = true
              // 主表流程状态
              this.formParams.status = statusNum
              // 合并主子表参数
              let vo = Object.assign({}, this.formParams, this.$refs.formSubRef.subFormParams)
              // 判断图片数量
              // if(!vo.imgIds) {
              //   this.formBtnLoading = false
              //   this.$message.warning('请上传示意图及照片（不少于3张）！')
              //   return
              // }
              // if(!vo.imgIds.includes(",")) {
              //   this.formBtnLoading = false
              //   this.$message.warning('请上传示意图及照片（不少于3张）！')
              //   return
              // }
              // if(vo.imgIds.split(",").length < 3) {
              //   this.formBtnLoading = false
              //   this.$message.warning('请上传示意图及照片（不少于3张）！')
              //   return
              // }
              // 桩号验证
              if (!this.pileStartK && !this.pileStartAdd) {
                this.$message.warning('请输入起点桩号！')
                this.formBtnLoading = false
                return
              }
              if (!this.pileEndK && !this.pileEndAdd) {
                this.$message.warning('请输入终点桩号！')
                this.formBtnLoading = false
                return
              }
              let pileStartK = parseInt(this.pileStartK, 10)
              let pileEndK = parseInt(this.pileEndK, 10)
              let pileStartAdd = parseInt(this.pileStartAdd, 10)
              let pileEndAdd = parseInt(this.pileEndAdd, 10)
              // 判断桩号终点和起点的关系
              if (pileStartK > pileEndK || (pileStartK === pileEndK && pileStartAdd > pileEndAdd)) {
                this.$message.warning('桩号终点应大于起点！');
                this.formBtnLoading = false;
                return;
              }
              // 起止点经纬度验证
              if(!vo.startLatitudeAndLongitude) {
                this.$message.warning('起点经纬度未填写/拾取！')
                this.formBtnLoading = false
                return
              }
              if(!vo.endLatitudeAndLongitude) {
                this.$message.warning('止点经纬度未填写/拾取！')
                this.formBtnLoading = false
                return
              }
              // 桩号组装
              vo.pileStartNum = `K${this.pileStartK}+${this.pileStartAdd}`
              vo.pileEndNum = `K${this.pileEndK}+${this.pileEndAdd}`

              // 转换数组对象
              vo.hisHazardTarget = Array.isArray(vo.hisHazardTarget) ?  vo.hisHazardTarget.join(",") : ''
              vo.curCrackLocation = Array.isArray(vo.curCrackLocation) ?  vo.curCrackLocation.join(",") : ''
              vo.curDamagedDrainage = Array.isArray(vo.curDamagedDrainage) ?  vo.curDamagedDrainage.join(",") : ''
              vo.curDamagedSlopeSurface = Array.isArray(vo.curDamagedSlopeSurface) ?  vo.curDamagedSlopeSurface.join(",") : ''
              vo.curDamagedRiverBank = Array.isArray(vo.curDamagedRiverBank) ?  vo.curDamagedRiverBank.join(",") : ''
              vo.curDamagedSupportFacility = Array.isArray(vo.curDamagedSupportFacility) ?  vo.curDamagedSupportFacility.join(",") : ''
              vo.curDamagedBridge = Array.isArray(vo.curDamagedBridge) ?  vo.curDamagedBridge.join(",") : ''
              vo.curDamagedTunnel = Array.isArray(vo.curDamagedTunnel) ?  vo.curDamagedTunnel.join(",") : ''

              // 水毁独立字段
              if (vo.disasterType === '水毁') {
                vo.hisDamageType = vo.hisDamageType ?  vo.hisDamageType.join(",") : ''
                vo.hisHazardWay = vo.hisHazardWay ?  vo.hisHazardWay.join(",") : ''
              }
              // 其它单独处理
              if(vo.disasterType.includes('其它')) {
                vo.disasterType = `其它:${vo.formOtherType}:${vo.formOtherTypeRemark}`
              }
              resolve(vo)
            } else {
              this.$message.warning('表单必填字段未填写！')
              reject(false)
            }
          })
        })
      })
    },

    // 表单流程提交
    async formProcessSubmit(parmas) {
      this.$modal.loading("正在提交，请稍候...")
      // 赋予本地参数
      this.processParmas.variables.approved = parmas.approved
      this.processParmas.variables.comment = parmas.comment
      // 获取表单提交数据
      let vo = await this.formSubmit('formRef', '1')
      if(!vo) {
        this.$modal.closeLoading()
        return
      }
      this.processParmas.formData = vo
      this.processParmas.disasterType = vo.disasterType
      // 表单字段修改对比校验
      this.processParmas.variables.operatingRecord = await JSON.stringify(this.formCompare(vo))
      submitTask(this.processParmas).then((res) => {
        if(res.code === 200) {
          this.$message.info(res.msg)
          window.close()
        }
        this.$modal.closeLoading()
      }).catch(() => {
        this.$modal.closeLoading()
      })
    },

    // 表单字段对比校验
    formCompare(vo) {
      // 备份数据组装
      let oldVo = { ...this.formSubData, ...this.formData };
      // 对比校验数据
      let content = []
      // 循环校对
      for(let key in oldVo) {
        if(oldVo[key] !== vo[key]) {
          content.push({
            name: this.formDataContrast[key],
            oldVal: oldVo[key],
            newVal: vo[key]
          })
        }
      }
      return content
    },

    // 点击管养单位时
    formMainItemClick(data) {
      if (data.label.includes('分处') || data.label.includes('红河管理处')) {
        this.formParams.maintenanceUnitId = data.id
        this.$refs.formMainRef.blur()
        this.formMaintenanceChange('1')
      } else {
        this.$message.warning('仅可选择分处！')
      }
    },

    // 管养单位/路段改变时
    formMaintenanceChange(type) {
      switch (type) {
        case '1':
          // 获取管养单位名称
          this.formGetName('1', this.formParams.maintenanceUnitId)
          // 重置路段及路线数据
          this.formParams.roadSectionId = ''
          this.formParams.roadSectionName = ''
          this.formRoadSectionList = []
          this.formParams.routerNum = ''
          this.formParams.routerName = ''
          this.formRouteList = []
          // 重置路线等级
          this.formParams.technicalGrade = ''
          // 重新获取路段数据列表
          this.queryMaintenanceSectionList(this.formParams.maintenanceUnitId)
          break;

        case '2':
          // 获取路段单位名称
          this.formGetName('2', this.formParams.roadSectionId)
          // 重置路线数据
          this.formParams.routerNum = ''
          this.formParams.routerName = ''
          this.formRouteList = []
          // 重置路线等级
          this.formGetTechnicalGrade(this.formParams.roadSectionId)
          // 重新获取路线数据列表
          this.queryRouterList(this.formParams.roadSectionId)
          break;

        case '3':
          // 获取路段单位名称
          this.formGetName('3', this.formParams.routerNum)
          break;
      }
    },

    // 获取管养单位/路段/路线名称
    formGetName(type, val) {
      switch (type) {
        case '1':
          for(let i=0; i<this.formMaintenanceList.length; i++) {
            if(this.formMaintenanceList[i].id == val) {
              this.formParams.maintenanceUnitName = this.formMaintenanceList[i].label
              break
            }
          }
          break;

        case '2':
          for(let i=0; i<this.formRoadSectionList.length; i++) {
            if(this.formRoadSectionList[i].maintenanceSectionId == val) {
              this.formParams.roadSectionName = this.formRoadSectionList[i].maintenanceSectionName
              break
            }
          }
          break;

        case '3':
          for(let i=0; i<this.formRouteList.length; i++) {
            if(this.formRouteList[i].routeCode == val) {
              this.formParams.routerName = this.formRouteList[i].routeName
              break
            }
          }
          break;
      }
    },

    // 获取技术等级
    formGetTechnicalGrade(val) {
      for(let i=0; i<this.formRoadSectionList.length; i++) {
        if(this.formRoadSectionList[i].maintenanceSectionId == val) {
          this.formParams.technicalGrade = this.formRoadSectionList[i].routeGrade
          break
        }
      }
    },

    // 市级区划改变时
    formChangeAreaCode(val) {
      console.log('市区区划改变：', val)
      this.formParams.areaCode = ''
      if(val) {
        let index = this.divisionsMunicipalList.findIndex(item => item.id === val)
        if(index !== -1) {
          this.areaCodeList = this.divisionsMunicipalList[index].children
        }
      } else {
        this.areaCodeList = []
      }
    },

    /**
     * 坐标相关
     */
    // 打开地图dialog
    openCoordinateDialog(type) {
      this.coordinateDialog = true
      this.coordinateType = type
      this.$nextTick(() => {
        this.coordinateInit()
      })
    },

    // 地图初始化
    coordinateInit() {
      const _this = this
      // 重置筛选结果
      this.coordinateSearch = ''

      window._AMapSecurityConfig = {
        securityJsCode: "65e21f3185bc29823f26c3e54bb0a9ab", // 安全密钥
      };
      AMapLoader.load({
        key: "d12697699ae0de43e2cf383e80eaa375", // 申请好的Web端开发者Key，首次调用 load 时必填
        version: "2.0", // 指定要加载的 JSAPI 的版本，缺省时默认为 1.4.15
        // plugins: ["AMap.ToolBar", "AMap.Scale"], //需要使用的的插件列表，如比例尺'AMap.Scale'，支持添加多个如：['...','...']
      })
        .then((AMap) => {
          // 变量初始化
          let positionInit = [102.8207599,24.8885797] // 默认坐标昆明市
          switch (_this.coordinateType) {
            case 'start':
              if(_this.formParams.startLatitudeAndLongitude && _this.formParams.startLatitudeAndLongitude.includes(",")) {
                let start = _this.formParams.startLatitudeAndLongitude.split(",")
                start.forEach(item => {
                  item = Number(item)
                })
                positionInit = start
              }
              break;

            case 'end':
              if(_this.formParams.endLatitudeAndLongitude && _this.formParams.endLatitudeAndLongitude.includes(",")) {
                let end = _this.formParams.endLatitudeAndLongitude.split(",")
                end.forEach(item => {
                  item = Number(item)
                })
                positionInit = end
              }
              break;
          }
          _this.coordinatePosition = positionInit.join(",")

          console.log('初始化位置的值：', positionInit)

          // 地图初始化
          _this.coordinateMap = new AMap.Map("coordinateBox", {
            // 设置地图容器id
            viewMode: "3D", // 是否为3D地图模式
            zoom: 11, // 初始化地图级别
            center: positionInit, // 初始化地图中心点位置
          })
          AMap.plugin('AMap.ToolBar',function(){
            let toolbar = new AMap.ToolBar(); // 缩放工具条实例化
            _this.coordinateMap.addControl(toolbar); //添加控件
          })
          AMap.plugin('AMap.Scale',function(){
            let scale = new AMap.Scale(); // 比例尺工具条实例化
            _this.coordinateMap.addControl(scale); //添加控件
          })

          // 初始化坐标点
          if(_this.coordinateMarker) {
            _this.coordinateMarker = null
          }
          _this.coordinateMarker = new AMap.Marker({
            position: positionInit
          })
          _this.coordinateMap.add(_this.coordinateMarker)

          // 地图绑定点击事件
          _this.coordinateMap.on("click", _this.coordinateClick);
        })
        .catch((e) => {
          console.log(e);
        });
    },

    // 添加地图点击事件
    coordinateClick(e) {
      // 清除原有标记
      if(this.coordinateMarker) {
        this.coordinateMap.remove(this.coordinateMarker)
      }
      this.coordinateMarker = new AMap.Marker({
        position: [e.lnglat.getLng(), e.lnglat.getLat()]
      })
      this.coordinateMap.add(this.coordinateMarker)
      // 坐标点赋值
      this.coordinatePosition = `${e.lnglat.getLng()},${e.lnglat.getLat()}`
    },

    // 添加查询事件
    coordinateList() {
      const _this = this
      AMap.plugin('AMap.PlaceSearch', function(){
        let autoOptions = {
          city: ''
        }
        let placeSearch = new AMap.PlaceSearch(autoOptions);
        placeSearch.search(_this.coordinateSearch, function(status, result) {
          console.log('搜索状态：', status)
          console.log('搜索结果：', result)
          // 搜索成功时，result即是对应的匹配数据
          if(status === 'complete') {
            if(result.info === 'OK' && result.poiList.count > 0) {
              let lng = result.poiList.pois[0].location.lng
              let lat = result.poiList.pois[0].location.lat
              // 重置中心点
              _this.coordinateMap.setCenter([lng, lat])
              // 清除原有标记
              if(_this.coordinateMarker) {
                _this.coordinateMap.remove(_this.coordinateMarker)
              }
              _this.coordinateMarker = new AMap.Marker({
                position: [lng, lat]
              })
              _this.coordinateMap.add(_this.coordinateMarker)
              // 坐标点赋值
              _this.coordinatePosition = `${lng},${lat}`
            } else {
              _this.$message.warning('暂无数据！')
            }
          } else {
            _this.$message.warning('查询失败！')
          }
        })
      })
    },

    // 保存当前坐标
    coordinateSave() {
      if(!this.coordinatePosition) {
        this.$message.warning('请选择经纬度！')
        return
      }
      switch (this.coordinateType) {
        case 'start':
          this.formParams.startLatitudeAndLongitude = this.coordinatePosition
          break;

        case 'end':
          this.formParams.endLatitudeAndLongitude = this.coordinatePosition
          break;
      }
      this.coordinateDialog = false
    },

    // 窗口关闭时销毁地图
    coordinateClose() {
      if(this.coordinateMap) {
        console.log('触发销毁')
        this.coordinateMap.off("click", this.coordinateClick);
        this.coordinateMap.destroy()
        this.coordinateMap = null
      }
    },
  }
}
</script>

<style lang="scss" scoped>

.detailBox {
  height: 100%;
  width: 100%;
}

.infoBox {
  padding: 15px 15px 0 15px;
  box-sizing: border-box;
  border-radius: 6px;
  border: 1px solid #C4C4C4;
  position: relative;

  .infoTitle {
    user-select: none;
    position: absolute;
    top: 0;
    left: 0;
    padding: 0 10px;
    font-size: 14px;
    line-height: 14px;
    font-weight: bold;
    transform: translateX(15px) translateY(-50%);
    background-color: white;
  }

  .imgBox {
    height: auto;
    width: 100%;
    display: flex;
    flex-wrap: wrap;
    align-content: flex-start;

    .imgItemBox {
      height: 240px;
      width: calc(100% / 3);
      box-sizing: border-box;
      padding: 10px;
      overflow-y: auto;
      display: flex;
      align-items: center;
      justify-content: center;
      position: relative;

      .imgDeleteBtn {
        position: absolute;
        z-index: 1;
        top: 10%;
        right: 10%;
      }
    }
  }
}

.coordinateDialog {

  .coordinateMap {
    height: 600px;
    width: 100%;
    position: relative;

    #coordinateBox {
      height: 100%;
      width: 100%;
      border-radius: 5px;
      position: relative;
      z-index: 0;
    }

    .coordinateSearch {
      position: absolute;
      z-index: 1;
      top: 10px;
      left: 10px;
      width: 50%;
      padding: 10px;
      box-sizing: border-box;
      background-color: #fff;
      border-radius: 5px;
      border: 1px solid #DCDFE6;
      box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.12), 0 0 6px 0 rgba(0, 0, 0, 0.04);
      display: flex;
      flex-direction: row;
    }

    .coordinateTip {
      position: absolute;
      z-index: 1;
      top: 10px;
      right: 10px;
      padding: 10px;
      box-sizing: border-box;
      background-color: #fff;
      border-radius: 5px;
      border: 1px solid #DCDFE6;
      box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.12), 0 0 6px 0 rgba(0, 0, 0, 0.04);
    }
  }
}
</style>
