<template>
  <div class="app-container maindiv">
    <el-row>
      <el-col :span="24" :xs="24">
        <el-row>
          <el-form
            ref="queryForm"
            :model="queryParams"
            size="mini"
            :inline="true"
            label-width="100px"
          >
            <el-form-item label="站点名称">
              <el-input v-model="queryParams.siteName" placeholder="请输入站点名称" />
            </el-form-item>
            <el-form-item label="站点编码">
              <el-input v-model="queryParams.siteCode" placeholder="请输入站点编码" />
            </el-form-item>
            <el-form-item label="所属管理处">
              <selectTree
                :key="'domainId'"
                style="width: 240px"
                v-model="queryParams.domainId"
                :dept-type="101"
                :expand-all="false"
                :dataRule="false"
                placeholder="请选择所属管理处"
                clearable
              />
            </el-form-item>
            <el-form-item>
              <el-button
                type="primary"
                icon="el-icon-search"
                size="mini"
                @click="handleQuery"
              >搜索</el-button>
              <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
            </el-form-item>
          </el-form>
        </el-row>
      </el-col>
    </el-row>

    <!--操作按钮区开始-->
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          icon="el-icon-plus"
          size="mini"
          v-has-menu-permi="['waste:site:add']"
          @click="openDetailDialog(null)"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          icon="el-icon-download"
          size="mini"
          @click="exportList"
          v-has-menu-permi="['waste:site:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar
        :showSearch.sync="showSearch"
        @queryTable="handleQuery"
        :columns="columns"
      ></right-toolbar>
    </el-row>

    <el-row>
      <div class="draggable">
        <el-table
          v-adjust-table
          size="mini"
          style="width: 100%"
          v-loading="loading"
          border
          :data="tableData"
          row-key="id"
          ref="dataTable"
          stripe
          highlight-current-row
          @row-click="handleClickRow"
          :height="showSearch ? 'calc(100vh - 330px)' : 'calc(100vh - 270px)'"
        >
          <el-table-column
            label="序号"
            align="center"
            type="index"
            width="50"
          />
          <template v-for="(column, index) in columns">
            <el-table-column
              :label="column.label"
              v-if="column.visible"
              align="center"
              :prop="column.field"
              :width="column.width"
            >
              <template slot-scope="scope">
                <template v-if="column.field === 'domainId'">
                  {{ scope.row.domainName || scope.row.domainId }}
                </template>
                <span v-else>{{ scope.row[column.field] }}</span>
              </template>
            </el-table-column>
          </template>
          <el-table-column
            label="操作"
            fixed="right"
            align="center"
            width="180"
            class-name="small-padding fixed-width"
          >
            <template slot-scope="scope">
              <el-button
                type="text"
                @click="openDetailDialog(scope.row)"
                v-has-menu-permi="['waste:site:edit']"
                icon="el-icon-edit"
              >编辑</el-button>
              <el-button
                type="text"
                @click="handleDelete(scope.row)"
                v-has-menu-permi="['waste:site:remove']"
                icon="el-icon-delete"
              >删除</el-button>
            </template>
          </el-table-column>
        </el-table>
        <pagination
          v-show="total > 0"
          :total="total"
          :page.sync="queryParams.pageNum"
          :limit.sync="queryParams.pageSize"
          @pagination="handleQuery"
        />
      </div>
    </el-row>

    <!-- 详情对话框 -->
    <el-dialog
      :title="drawerTitle"
      destroy-on-close
      :visible.sync="drawer"
      :close-on-click-modal="false"
      width="50%"
      v-if="drawer"
    >
      <el-form
        ref="elForm"
        :model="formData"
        :rules="rules"
        label-width="120px"
        size="medium"
      >
        <el-row :gutter="15">
          <el-col :span="12">
            <el-form-item label="站点名称" prop="siteName">
              <el-input
                v-model="formData.siteName"
                placeholder="请输入站点名称"
                clearable
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="站点编码" prop="siteCode">
              <el-input
                v-model="formData.siteCode"
                placeholder="请输入站点编码"
                clearable
              ></el-input>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="15">
          <el-col :span="12">
            <el-form-item label="所属管理处" prop="domainId">
              <selectTree
                :key="'domainId'"
                style="width: 100%"
                v-model="formData.domainId"
                :dept-type="101"
                :expand-all="false"
                :dataRule="false"
                placeholder="请选择所属管理处"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="站点位置" prop="siteLocation">
              <el-input
                v-model="formData.siteLocation"
                placeholder="请输入站点位置"
                clearable
              ></el-input>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="15">
          <el-col :span="12">
            <el-form-item label="联系人" prop="contacts">
              <el-input
                v-model="formData.contacts"
                placeholder="请输入联系人"
                clearable
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="联系电话" prop="telPhone">
              <el-input
                v-model="formData.telPhone"
                placeholder="请输入联系电话"
                clearable
              ></el-input>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="15">
          <el-col :span="24">
            <el-form-item label="备注" prop="remark">
              <el-input
                v-model="formData.remark"
                type="textarea"
                placeholder="请输入备注"
                :rows="2"
              ></el-input>
            </el-form-item>
          </el-col>
        </el-row>

        <div style="text-align: right;">
          <el-button type="primary" @click="onSave">保 存</el-button>
          <el-button @click="drawer = false">取 消</el-button>
        </div>
      </el-form>
    </el-dialog>
  </div>
</template>

<script>
import selectTree from "@/components/DeptTmpl/selectTree.vue";
import { addSite, deleteSite, editSite, querySiteListPage } from '@/api/materialWaste/wasteSiteManagement'

export default {
  name: 'WasteSiteManagement',
  components: {
    selectTree
  },
  data() {
    return {
      showSearch: false,
      queryParams: {
        pageNum: 1,
        pageSize: 50,
        siteName: '',
        siteCode: '',
        domainId: null
      },
      total: 0,
      loading: false,
      columns: [
        {
          key: 0,
          field: "siteName",
          label: "站点名称",
          visible: true
        },
        {
          key: 1,
          field: "siteCode",
          label: "站点编码",
          visible: true
        },
        {
          key: 2,
          field: "domainId",
          label: "所属管理处",
          visible: true
        },
        {
          key: 3,
          field: "siteLocation",
          label: "站点位置",
          visible: true
        },
        {
          key: 4,
          field: "contacts",
          label: "联系人",
          visible: true
        },
        {
          key: 5,
          field: "telPhone",
          label: "联系电话",
          visible: true
        }
      ],
      tableData: [],
      rowData: {},
      drawerTitle: "新增站点",
      drawer: false,
      formData: {
        siteName: '',
        siteCode: '',
        domainId: null,
        siteLocation: '',
        contacts: '',
        telPhone: '',
        remark: ''
      },
      rules: {
        siteName: [
          { required: true, message: '站点名称不能为空', trigger: 'blur' }
        ],
        siteCode: [
          { required: true, message: '站点编码不能为空', trigger: 'blur' }
        ],
        domainId: [
          { required: true, message: '所属管理处不能为空', trigger: 'change' }
        ],
        siteLocation: [
          { required: true, message: '站点位置不能为空', trigger: 'blur' }
        ],
        contacts: [
          { required: true, message: '联系人不能为空', trigger: 'blur' }
        ],
        telPhone: [
          { required: true, message: '联系电话不能为空', trigger: 'blur' }
        ]
      }
    };
  },
  created() {
    this.handleQuery();
  },
  methods: {
    handleQuery() {
      this.loading = true;
      querySiteListPage(this.queryParams).then(res => {
        this.loading = false;
        this.tableData = res.rows;
        this.total = res.total;
      }).catch(() => {
        this.loading = false;
      });
    },
    resetQuery() {
      this.queryParams = {
        pageNum: 1,
        pageSize: 50,
        siteName: '',
        siteCode: '',
        domainId: null
      };
      this.handleQuery();
    },
    openDetailDialog(row) {
      if (row) {
        this.drawerTitle = "编辑站点";
        this.formData = JSON.parse(JSON.stringify(row));
      } else {
        this.drawerTitle = "新增站点";
        this.formData = {
          siteName: '',
          siteCode: '',
          domainId: null,
          siteLocation: '',
          contacts: '',
          telPhone: '',
          remark: ''
        };
      }
      this.drawer = true;
    },
    onSave() {
      this.$refs.elForm.validate(valid => {
        if (valid) {
          if (this.formData.id) {
            editSite(this.formData).then(res => {
              this.$modal.msgSuccess("修改成功");
              this.drawer = false;
              this.handleQuery();
            });
          } else {
            addSite(this.formData).then(res => {
              this.$modal.msgSuccess("新增成功");
              this.drawer = false;
              this.handleQuery();
            });
          }
        }
      });
    },
    handleDelete(row) {
      this.$modal.confirm('确认删除该站点吗？').then(() => {
        deleteSite(row.id).then(res => {
          this.$modal.msgSuccess("删除成功");
          this.handleQuery();
        });
      }).catch(() => {});
    },
    handleClickRow(row) {
      this.rowData = row;
    },
    exportList() {
      this.download('manager/waste/site/export', {
        ...this.queryParams
      }, `站点管理数据_${new Date().getTime()}.xlsx`);
    }
  }
};
</script>

<style lang="scss" scoped>
@import "@/assets/styles/business.scss";

::v-deep .dialog-footer {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  width: 100%;
}
</style>
