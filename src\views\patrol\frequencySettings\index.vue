<template>
  <div class="app-container">
    <el-row :gutter="20">

      <!--筛选区开始-->
      <el-col :span="24" :xs="24">
        <el-row>
          <el-col :span="24">
            <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" label-width="68px">
              <el-form-item label="" prop="isSettings">
                <el-select v-model="queryParams.isSettings" placeholder="是否设置" clearable style="width: 240px">
                  <el-option label="已设置" value="1"></el-option>
                  <el-option label="未设置" value="0"></el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="" prop="deptIdArr" >
                <el-select
                  v-model="deptIdArr"
                  filterable
                  placeholder="请选择管养单位"
                  multiple
                  collapse-tags
                  clearable
                  style="width: 240px" @change="deptChange">
                  <el-option
                    v-for="item in deptOptions"
                    :key="item.id"
                    :label="item.label"
                    :value="item.id">
                  </el-option>
                </el-select>
<!--                <treeselect v-model="queryParams.deptIds" :options="deptOptions" :show-count="true"-->
<!--                            placeholder="请选择管养单位"/>-->
              </el-form-item>
              <el-form-item label="" prop="maintenanceSectionId">
                <el-select v-model="queryParams.maintenanceSectionId" filterable placeholder="请选养护路段" style="width: 240px">
                  <el-option
                    v-for="item in maintenanceSectionList"
                    :key="item.maintenanceSectionId"
                    :label="item.maintenanceSectionName"
                    :value="item.maintenanceSectionId">
                  </el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="" prop="assetName">
                <el-input
                  v-model="queryParams.assetName"
                  :placeholder="assetName"
                  clearable
                  style="width: 240px"
                  @keyup.enter.native="handleQuery"
                />
              </el-form-item>
              <el-form-item label="" prop="assetCode">
                <el-input
                  v-model="queryParams.assetCode"
                  :placeholder="assetCode"
                  clearable
                  style="width: 240px"
                  @keyup.enter.native="handleQuery"
                />
              </el-form-item>
              <el-form-item>
                <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
                <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
              </el-form-item>
            </el-form>
          </el-col>
        </el-row>
        <!--筛选区结束-->

        <!--操作按钮区开始-->
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button
              type="success"
              icon="el-icon-edit"
              size="mini"
              :disabled="multiple"
              @click="updateAll"
              v-hasPermi="['patrol:frequencySettings:edit']"
            >批量修改
            </el-button>
          </el-col>
          <right-toolbar @queryTable="getList"></right-toolbar>
        </el-row>
        <!--操作按钮区结束-->

        <!--数据表格开始-->
        <div class="tableDiv">
          <el-tabs type="border-card" v-model="activeName" @tab-click="getList">
            <el-tab-pane label="桥梁" name="桥梁">
              <tables @getUpdate="handleUpdate" v-loading="loading" :data="frequencySettingsList"
                      :activeName="activeName" :selectIds="ids" @getSelect="handleSelectionChange"></tables>
            </el-tab-pane>
            <el-tab-pane label="隧道" name="隧道">
              <tables @getUpdate="handleUpdate" v-loading="loading" :data="frequencySettingsList"
                      :activeName="activeName" :selectIds="ids" @getSelect="handleSelectionChange"></tables>
            </el-tab-pane>
            <el-tab-pane label="涵洞" name="涵洞">
              <tables @getUpdate="handleUpdate" v-loading="loading" :data="frequencySettingsList"
                      :activeName="activeName" :selectIds="ids" @getSelect="handleSelectionChange"></tables>
            </el-tab-pane>
            <el-tab-pane label="隧道机电" name="隧道机电">
              <tables @getUpdate="handleUpdate" v-loading="loading" :data="frequencySettingsList"
                      :activeName="activeName" :selectIds="ids" @getSelect="handleSelectionChange"></tables>
            </el-tab-pane>
          </el-tabs>
          <pagination
            v-show="total>0"
            :total="total"
            :page.sync="queryParams.pageNum"
            :limit.sync="queryParams.pageSize"
            @pagination="getList"
          />
        </div>
        <!--数据表格结束-->
      </el-col>
    </el-row>

    <!-- 添加或修改巡查频率配置对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="600px" append-to-body>
      <el-form ref="form" status-icon :model="form" :rules="rules" label-width="110px">
        <el-form-item label="日常巡查频率" prop="dayFrequency">
          <el-row :gutter="17">
            <el-col :span="11">
              <el-input-number v-model="form.dayFrequency" :min="0" controls-position="right" style="width:100%"></el-input-number>
            </el-col>
            <el-col class="line" :span="6">
              <span>天/次</span>
            </el-col>
          </el-row>
        </el-form-item>
        <el-form-item label="经常检查频率" prop="monthFrequency">
          <el-row :gutter="17">
            <el-col :span="11">
              <el-input-number v-model="form.monthFrequency" :min="0" controls-position="right" style="width:100%"></el-input-number>
            </el-col>
            <el-col class="line" :span="6">
              <span>月/次</span>
            </el-col>
          </el-row>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

  </div>
</template>

<script>
import {
  listFrequencySettings,
  delFrequencySettings,
  updateFrequencySettings,
  updateAll
} from "@/api/patrol/frequencySettings";
import Treeselect from "@riophae/vue-treeselect";
import "@riophae/vue-treeselect/dist/vue-treeselect.css";
import {listMaintenanceSectionAll} from "@/api/system/maintenanceSection";
import {getTreeStruct} from "@/api/tmpl";
import Tables from "./tables.vue";

export default {
  name: "FrequencySettings",
  components: {Treeselect, Tables},
  data() {
    const checkFrequency = (rule, value, callback) => {
      if (!Number.isInteger(parseInt(value)) || value < 0) {
        callback(new Error('必须为正整数！'));
      } else {
        callback();
      }
    };
    return {
      activeName: "桥梁",
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: false,
      dictType: [],
      // 总条数
      total: 0,
      // 巡查频率配置表格数据
      frequencySettingsList: null,
      title: "",
      // 部门树选项
      deptOptions: [],
      maintenanceSectionList: [],
      // 是否显示弹出层
      open: false,
      openAll: false,
      // 表单参数
      form: {},
      defaultProps: {
        children: "children",
        label: "label"
      },
      deptIdArr: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        deptIds: null,
        maintenanceSectionId: null,
        assetName: null,
        assetCode: null,
        isSettings: null,
        type: null
      },
      // 表单校验
      ruleForm: {
        dayFrequency: '',
        monthFrequency: ''
      },
      rules: {
        dayFrequency: [
          {validator: checkFrequency, trigger: 'blur' }
        ],
        monthFrequency: [
          {validator: checkFrequency, trigger: 'blur' }
        ]
      }
    };
  },
  computed: {
    assetName() {
      return "请输入" + this.activeName + "名称";
    },
    assetCode() {
      return "请输入" + this.activeName + "编码";
    },
    getType() {
      if (this.activeName === "桥梁") {return 1}
      else if (this.activeName === "隧道") {return 2}
      else if (this.activeName === "涵洞") {return 3}
      else if (this.activeName === "隧道机电") {return 4}
      else {return 0}
    }
  },
  created() {
    this.getList();
    this.getDeptTree();
    this.getMaintenanceSection();
  },
  methods: {
    /** 查询列表 */
    getList() {
      this.loading = true;
      this.queryParams.type = this.getType;
      this.queryParams.deptIds = this.deptIdArr.join(",");
      listFrequencySettings(this.queryParams).then(response => {
        this.frequencySettingsList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    /** 查询部门管理处下拉树结构 */
    getDeptTree() {
      return getTreeStruct({types:201}).then(response => {
        this.deptOptions = response.data;
      });
    },
    //管理处下拉选点击事件
    deptChange() {
      listMaintenanceSectionAll({departmentIdList: this.deptIdArr.join(",")}).then(res => {
        this.maintenanceSectionList = res.data
      })
    },
    /** 查询养护路段下拉列表 */
    getMaintenanceSection() {
      listMaintenanceSectionAll().then(res => {
        this.maintenanceSectionList = res.data
      })
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.openAll = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        assetId: null,
        frequency: null,
        isSettings: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dateRange = [];
      this.deptIdArr = [];
      this.resetForm("queryForm");
      this.getMaintenanceSection();
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.assetId);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      this.form = {
        assetId:row.assetId,
        dayFrequency:row.dayFrequency,
        monthFrequency:row.monthFrequency
      };
      console.log(this.form,'ddeeeee');

      this.open = true;
      this.title = "修改巡查频率配置";
    },
    /** 修改按钮操作 */
    updateAll() {
      this.reset();
      this.form.ids = this.ids;
      this.open = true;
      this.title = "批量修改巡查频率";
    },
    /** 提交按钮 */
    submitForm: function () {
      this.$refs["form"].validate(valid => {
        if (valid) {
          this.form.type = this.getType;
          updateFrequencySettings(this.form).then(response => {
            this.$modal.msgSuccess("修改成功");
            this.open = false;
            this.getList();
          });
        }
      });
    },

  }
};
</script>
<style scoped>
.tableDiv {
  background-color: white;
  padding-bottom: 10px;
}
</style>
