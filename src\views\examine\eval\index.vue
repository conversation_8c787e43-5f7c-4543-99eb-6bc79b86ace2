<template>
  <div class="app-container">
    <!-- 查询 -->
    <div class="searchBox" :style="{ 'height': queryShow ? '86px' : '48px' }">
      <el-row :gutter="20" style="width: 1000px">
        <el-col :span="5" :offset="0">
          <el-select
            v-model="queryParams.selectedDeptIds"
            multiple
            clearable
            ref="stlectTree"
            class="custom-select"
            placeholder="请选择检查部门"
            @clear="selectClear"
            @remove-tag="removeTag"
            collapse-tags
            style="width: 100%"
          >
            <el-option
              hidden
              v-for="item in deptOptions"
              :key="item.id"
              :label="item.label"
              :value="item.id"
            >
            </el-option>
            <el-tree
              style="font-weight: 400"
              :data="deptOptions"
              :props="defaultProps"
              @check="handleNodeCheck"
              @node-click="nodeClick"
              node-key="id"
              check-on-click-node
              ref="treeRef"
              show-checkbox
            ></el-tree>
          </el-select>
        </el-col>
        <el-col :span="5" :offset="0">
          <el-input v-model="queryParams.taskName" placeholder="请输入检查任务名称" clearable
                    @keyup.enter.native="handleQuery"/>
        </el-col>
        <el-col :span="5" :offset="0">
              <el-select v-model="queryParams.taskStatus" placeholder="请选择任务状态" clearable>
                <el-option label="附件未上传" value="1"/>
                <el-option label="附件已上传" value="2"/>
                <el-option label="已评分" value="3"/>
              </el-select>
        </el-col>
        <el-col :span="4" :offset="0" style="display: flex; align-items: center">
            <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
            <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
            <el-button
              :icon="queryShow ? 'el-icon-arrow-up' : 'el-icon-arrow-down'"
              style="color: #1890ff;border-color: #badeff;background-color: #e8f4ff;"
              circle
              @click="queryShow = !queryShow"
            />
        </el-col>
      </el-row>
      <transition name="search">
        <div class="searchMoreBox" v-if="queryShow">
          <el-row :gutter="20" style="width: 1000px;">
            <el-col :span="10" :offset="0">
              <el-date-picker
                v-model="queryTime"
                @change="() => {queryParams.startTime = queryTime[0] + ' 00:00:00'; queryParams.endTime = queryTime[1] + ' 23:59:59'}"
                type="daterange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                style="width: 100%"
                format="yyyy-MM-dd"
                value-format="yyyy-MM-dd"
              >
              </el-date-picker>
            </el-col>

          </el-row>
        </div>
      </transition>
    </div>

    <!--数据表格开始-->
    <div class="tableDiv" :style="{ 'height': queryShow ? 'calc(100% - 96px)' : 'calc(100% - 58px)',paddingTop:'10px'}">
      <el-table v-adjust-table :data="examineTaskList" :row-key="row => row.id"
                :height="showSearch ? 'calc(100vh - 320px)' : 'calc(100vh - 230px)'" style="width: 100%"
                v-loading="loading" border>
        <el-table-column label="序号" type="index" width="50" align="center"></el-table-column>
        <el-table-column label="年度" align="center" prop="year" width="100"/>
        <el-table-column label="任务名称" align="center" prop="taskName" min-width="140" show-overflow-tooltip/>
        <el-table-column label="管养单位" align="center" prop="maintenanceUnitId" width="140">
          <template v-slot="scope">
            {{ formatDeptName(scope.row) }}
          </template>
        </el-table-column>
        <el-table-column label="下发单位" align="center" prop="createUnit" width="230"/>
        <el-table-column label="下发时间" align="center" prop="createTime" width="140"/>
        <el-table-column label="要求完成时间" align="center" prop="deadline" width="140">
          <template v-slot="scope">
            <span :style="isDeadlinePassed(scope.row)">
              {{ scope.row.deadline | formatDate('YYYY年MM月DD日') }}
            </span>
          </template>
        </el-table-column>
        <el-table-column label="任务状态" align="center" prop="taskStatus" width="120">
          <template v-slot="scope">
            <el-tag :type="scope.row.taskStatus === '3' ? 'success' : 'info'">
              {{ scope.row.taskStatus === '3' ? '已评分' : (scope.row.taskStatus === '2'?'附件已上传':'附件未上传') }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" fixed="right" align="center" prop="taskStatus" width="220">
          <template v-slot="scope">
            <el-button size="mini" type="text" icon="el-icon-view"
                       @click="handleView(scope.row)">查看
            </el-button>
            <el-button size="mini" v-if="scope.row.taskStatus==='2'" type="text" icon="el-icon-edit"
                       @click="handleFill(scope.row)">评分
            </el-button>
<!--            <el-button size="mini" v-if="scope.row.taskStatus=='2'" type="text" icon="el-icon-finished"-->
<!--                       @click="handleSubmit(scope.row)">提交-->
<!--            </el-button>-->
            <el-button size="mini" type="text" icon="el-icon-finished"
                       @click="handleExpAsXls(scope.row)">导出
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <pagination
        v-show="total > 0"
        :total="total"
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
        @pagination="getList"
      />
    </div>
    <!--数据表格结束-->
    <EvaluationDialog :resultData="taskResult" :visible="examineDialogVisible" :isView="isView" :onSaveBtnClick="handleSaveExamineResult" :onDialogClose="onEvaludateDialogClose"/>
  </div>
</template>

<script>
import Treeselect from '@riophae/vue-treeselect';
import selectTreeCheckbox from '@/components/DeptTmpl/selectTreeCheckbox.vue';
import deptTreeCheckbox from '@/components/DeptTmpl/deptTreeCheckbox.vue';
import {findFillDept,} from '@/api/system/user';
import { deptTreeSelect } from '@/api/tmpl';
import {queryExamineTaskList4Eval,getExamineTaskResultByTaskId,evaluate,complete,exportXls} from "@/api/examine/task/examineTask";
import {getMaintenanceSectionListAll} from '@/api/baseData/common/routeLine';
import EvaluationDialog from '../evaluation_dialog.vue';
import moment from 'moment';

export default {
  name: 'ExamineResultEvaluate',
  components:{
    Treeselect,
    selectTreeCheckbox,
    deptTreeCheckbox,
    EvaluationDialog
  },
  data() {
    return {
      queryShow: false, // 隐藏筛选显隐
      //部门的选项
      optionsDept: [],
      deptOptions:[],
      routeOptions:[],
      // 遮罩层
      loading: false,
      // 显示搜索条件
      showSearch: false,
      queryTime: [], // 查询时间
      // 总条数
      total: 0,
      // 填报任务表格数据
      examineTaskList: [],
      taskResult:{task:{},detailList:[]},
      isView:false,
      examineDialogVisible:false,
      defaultProps: {
        children: 'children',
        label: 'label',
      },
      treeValue: {},
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        taskName: '',
        taskStatus: '',
        selectedDeptIds:[],
        selectedSectIds:[],
        startTime: '',
        endTime: '',
      }
    };
  },
  watch: {
    'queryParams.selectedDeptIds': {
      handler(val) {
         this.$nextTick(() => {
             if (!val) {
                this.selectClear();
                this.deptChange([]);
               }else if (val && !val.length) {
                 this.deptChange(val);
               }
        });
      },
      deep: true,
    },
  },
  computed: {
    formatDeptName() {
      return (row) => {
        const dept = this.optionsDept.find(option => option.deptId === parseInt(row.maintenanceUnitId));
        let deptName=dept ? dept.deptName : '';
        row.deptName=deptName;
        return deptName;
      };
    }
  },
  created() {
    this.getDeptList()
    this.getList();
  },
  methods: {
    handleNodeCheck(node, nodes) {
      this.queryParams.selectedDeptIds =
        nodes && nodes.checkedNodes ? nodes.checkedNodes.map((v) => v.id) : [];
      this.deptChange(this.queryParams.selectedDeptIds);
      this.$forceUpdate();
    },
    nodeClick(item) {
      const { id, label } = item;
      this.treeValue.id = id;
      this.treeValue.label = label;
    },
    selectClear() {
      if (this.$refs.treeRef) {
        // 清空树选中数据
        this.$refs.treeRef.setCheckedKeys([]);
        // 清空表单数据
        this.queryParams.selectedDeptIds = [];
        this.queryParams.selectedSectIds = '';
      }
    },
    removeTag(tag) {
      let checkedNodes = this.$refs.treeRef.getCheckedNodes();
      // 删除节点
      for (let i = 0; i < checkedNodes.length; i++) {
        if (checkedNodes[i].id == tag) {
          checkedNodes.splice(i, 1);
          break;
        }
      }
      // 设置 tree 选中的节点
      this.$refs.treeRef.setCheckedNodes(checkedNodes);
      // 更新表单数据并触发搜索
      this.queryParams.selectedDeptIds = checkedNodes.map((node) => node.id);
      this.deptChange(this.queryParams.selectedDeptIds);
    },
    deptChange(e) {
      this.queryParams.selectedSectIds = '';
      getMaintenanceSectionListAll({ departmentIdList: e }).then((res) => {
        if (res.code == 200) {
          this.routeOptions = res.data || [];
        }
      });

    },
    getList() {
      this.loading = true;
      queryExamineTaskList4Eval(this.queryParams).then((response) => {
        this.examineTaskList = response.rows.map(row => {
          // 截取 createTime 字段的年份部分
          const year = row.createTime.split('-')[0];
          return {
            ...row,
            year: year
          };
        });
        this.total = response.total;
        this.loading = false;
      });
    },
    getDeptList() {
      findFillDept().then((res) => {
        if (res.code === 200) {
          this.optionsDept = res.data;
        }
      });
      deptTreeSelect({ types: '201' }).then((response) => {
        this.deptOptions = response.data;
      });
    },
    /** 搜索按钮操作 */
    handleQuery() {
      let qparams=this.queryParams;
      this.queryParams.pageNum = 1;
      this.queryParams.deptIds=(qparams.selectedDeptIds && qparams.selectedDeptIds.length )?qparams.selectedDeptIds.join(","):'';
      this.queryParams.sectIds=(qparams.selectedSectIds && qparams.selectedSectIds.length )?qparams.selectedSectIds.join(","):'';
      this.getList();
    },
    handleFill(task){
      getExamineTaskResultByTaskId(null,task.id).then((res) => {
        let resData=res.data;
        resData.task.deptName=task.deptName;
        this.taskResult = resData
        this.examineDialogVisible=true
        this.isView=false
      });
    },
    handleView(task){
      getExamineTaskResultByTaskId(null,task.id).then((res) => {
        let resData=res.data;
        resData.task.deptName=task.deptName;
        this.taskResult = resData
        this.examineDialogVisible=true
        this.isView=true;
      });
    },
    handleSubmit(task){
      complete(task.id).then((res)=>{
        if(res.code === 200){
          this.$message.success('操作成功！');
          this.examineDialogVisible=false;
          this.getList();
        }else{
          this.$message.error(res.msg);
        }
      });
    },
    handleSaveExamineResult(resultData,submit){
      let params={submit:submit,taskId:resultData.task.id,detailList:[]};
      let detailList=[];
      resultData.schemaList.forEach(function(v,i,a){
        v.detailList.forEach(function(v0,i0,a0){
          detailList.push({
            tplId:v0.tplId,
            detailId:v0.detailId,
            resultDetailId:v0.resultDetailId,
            imgId:v0.imgId,
            deductProject:v0.deductProject,
            deductScore:v0.deductScore
          });
        })
      });
      params.detailList=detailList;
      evaluate(params).then((res)=>{
        if(res.code === 200){
          this.$message.success('操作成功！');
          this.examineDialogVisible=false;
          this.getList();
        }else{
          this.$message.error(res.msg);
        }
      })
    },
    async handleExpAsXls(task) {
      const res = await exportXls(task.id);
      const blob=new Blob([res],{type:'application/vnd.ms-excel'});
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      let fileName = task.taskName + '.xlsx';
      a.download = fileName;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      window.URL.revokeObjectURL(url);
    },
    onEvaludateDialogClose(){
      this.examineDialogVisible=false;
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.queryParams = {
        pageNum: 1,
        pageSize: 10,
        taskName: '',
        taskStatus: '',
        startTime: '',
        endTime: '',
      }
      this.getList()
    },
    isDeadlinePassed(row) {
      if (row.taskStatus !== '3') {
        const now = moment();
        const expiryDate = moment(row.deadline);
        const diffDays = expiryDate.diff(now, 'days');

        if (row.status === 3) {
          return {color: '#67C23A'};
        } else if (diffDays < 0) {
          return {color: '#F56C6C'};
        } else if (diffDays <= 1) {
          return {color: '#E6A23C'};
        } else {
          return {};
        }
      }
    }
  },
};
</script>
<style scoped lang="scss">
.app-container form:first-child .el-select,
.app-container form:nth-child(2) .el-select,
.app-container form:nth-child(2) ::v-deep .el-form-item__content,
.app-container form:first-child ::v-deep .el-form-item__content {
  width: 240px;
}

.app-container form:first-child .el-form-item:last-child ::v-deep .el-form-item__content {
  width: auto;
}

.app-container {
  padding: 10px;
  background-color: #c0c0c0;
  box-sizing: border-box;
}

.searchBox {
  padding: 10px;
  background: #fff;
  border-radius: 10px;
  transition: all .1s linear;
  display: flex;
  flex-direction: column;

  .searchMoreBox {
    min-width: 192px;
    margin-top: 10px;
    display: flex;
    align-items: center;
    flex-direction: row;
  }
}


.dialog-content {
  max-height: 170vh;
  overflow-y: auto;
}

.dialog-footer {
  text-align: right;
}

.tableDiv {
  margin-top: 10px;
  background-color: white;
  padding-bottom: 10px;
  border-radius: 10px;
  transition: all .1s linear;
  display: flex;
  flex-direction: column;

  .btnBox {
    padding: 10px;
  }

  ::v-deep .el-upload-list__item {
    width: 42px;
    height: 42px;
    margin: 0px 5px -6px 0 !important;
  }

  ::v-deep .el-upload--picture-card {
    width: 42px;
    height: 42px;
    margin: 4px 0;
  }

  ::v-deep .el-icon-plus {
    font-size: 1rem;
  }

  ::v-deep .el-upload-list__item-actions {
    font-size: 1rem;
  }

  ::v-deep .el-upload-list__item-actions span + span {
    margin-left: 5px !important;
  }
}

.cascade-selection {
  display: flex;
  align-items: center;

  .tree-select-mini {
    font-size: 13px;
    font-weight: unset;

    ::v-deep .vue-treeselect__control {
      height: 26px;
      line-height: 26px;

      .vue-treeselect__placeholder,
      .vue-treeselect__single-value {
        line-height: 26px;
      }
    }

    ::v-deep .vue-treeselect__menu {
      .vue-treeselect__label {
        font-weight: unset;
        color: #606266;
      }
    }
  }

  .tree-select-small {
    font-size: 13px;

    ::v-deep .vue-treeselect__control {
      height: 30px;
      line-height: 30px;
      font-weight: 200;

      .vue-treeselect__placeholder,
      .vue-treeselect__single-value {
        line-height: 30px;
      }
    }

    ::v-deep .vue-treeselect__menu {
      .vue-treeselect__label {
        font-weight: unset;
        color: #606266;
      }
    }
  }

  /* 输入框超出隐藏，不换行*/
  ::v-deep .el-select__tags {
    flex-wrap: nowrap;
    overflow: auto;
  }

  ::v-deep .el-scrollbar {
    .el-select-dropdown__item {
      padding: 0;
    }
  }

  .custom-select {
    .el-select-dropdown__item {
      padding: 0;
      background-color: #0f0;
      /* 修改背景颜色为绿色 */
    }
  }
}
</style>
