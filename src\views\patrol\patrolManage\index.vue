<template>
  <div class="container">
    <!-- 上半部分 -->
    <div class="top-section">
      <component :is="'Summary'+type" @current-change="handleCurrentChange"></component>
    </div>

    <!-- 下半部分-->
    <div class="bottom-section" v-show="current.ids">
      <el-tabs>
        <el-tab-pane label="路线巡查">
          <InspectionLogs :current="current"></InspectionLogs>
        </el-tab-pane>
        <el-tab-pane label="桥梁日常">
          <BridgeDailyCheck :current="current" type="1"></BridgeDailyCheck>
        </el-tab-pane>
        <el-tab-pane label="隧道日常">
          <TunnelDailyCheck :current="current" type="5"></TunnelDailyCheck>
        </el-tab-pane>
        <el-tab-pane label="病害数量">

          <EventReview :current="current"></EventReview>

        </el-tab-pane>
      </el-tabs>
    </div>
  </div>
</template>

<script>
import Summary from '@/views/patrol/patrolManage/components/Summary.vue'
import Route from '@/views/patrol/patrolManage/components/Route.vue'
import Asset from '@/views/patrol/patrolManage/components/Asset.vue'
import Diseases from '@/views/patrol/patrolManage/components/Diseases.vue'


import Summary1 from '@/views/patrol/patrolManage/components/Summary1.vue'
import Summary2 from '@/views/patrol/patrolManage/components/Summary2.vue'
import Summary3 from '@/views/patrol/patrolManage/components/Summary3.vue'
import InspectionLogs from '@/views/patrol/patrolManage/components/inspectionLogs/index.vue'
import BridgeDailyCheck from '@/views/patrol/patrolManage/components/bridgeDailyCheck/index.vue'
import TunnelDailyCheck from '@/views/patrol/patrolManage/components/tunnelDailyCheck/index.vue'
import EventReview from '@/views/patrol/patrolManage/components/eventReview/index.vue'


export default {
  components: {
    Summary,
    Route,
    Asset,
    Diseases,

    Summary1,
    Summary2,
    Summary3,
    InspectionLogs,
    BridgeDailyCheck,
    TunnelDailyCheck,
    EventReview
  },
  props: {
    type: {
      type: Number,
      default: 3
    }
  },
  data() {
    return {
      current: {}
    }
  },
  methods: {
    handleCurrentChange(data) {
      this.current = data
      console.log(this.current, 'ddd')
    }
  }
}
</script>

<style scoped>
.container {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.top-section {
  height: 50%;
  //border-bottom: 1px solid #eee;
  padding: 15px 20px 5px 20px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  border-radius: 4px;
  border: 1px solid #e6ebf5;
  background-color: #FFFFFF;
  color: #303133;
  overflow: hidden;
}

.bottom-section {
  height: 60%;
  margin-top: 15px;
  padding: 15px 20px 5px 20px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  border-radius: 4px;
  border: 1px solid #e6ebf5;
  background-color: #FFFFFF;
  color: #303133;
  overflow: hidden;
}
</style>
