<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryForm"
      size="small"
      :inline="true"
      label-width="68px"
    >
      <el-form-item label="" prop="name">
        <el-input
          v-model="queryParams.name"
          placeholder="请输入任务名称"
          clearable
          style="width: 240px"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="" prop="userName">
        <el-input
          v-model="queryParams.userName"
          placeholder="请输入任务发布人"
          clearable
          style="width: 240px"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="" prop="deptName">
        <el-input
          v-model="queryParams.deptName"
          placeholder="请输入任务发布部门"
          clearable
          style="width: 240px"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="" prop="status">
        <el-select
          v-model="queryParams.status"
          placeholder="请选择任务状态"
          clearable
          style="width: 240px"
        >
          <el-option label="未下发" value="1" />
          <el-option label="已下发" value="2" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
          >搜索</el-button
        >
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
          >重置</el-button
        >
        <!--        <el-button v-show="!showSearch" @click="showSearch=true" icon="el-icon-arrow-down" circle></el-button>-->
        <!--        <el-button v-show="showSearch" @click="showSearch=false" icon="el-icon-arrow-up" circle></el-button>-->
      </el-form-item>
    </el-form>
    <!--默认折叠-->

    <!--默认折叠 ,此处仅作为示例-->
    <el-form
      :model="queryParams"
      size="small"
      :inline="true"
      v-show="showSearch"
      label-width="68px"
    >
      <el-form-item label="" prop="status">
        <el-select
          v-model="queryParams.status"
          placeholder="示例状态"
          clearable
          style="width: 240px"
        >
          <el-option
            v-for="dict in dictType"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
    </el-form>
    <!--筛选区结束-->

    <!--操作按钮区开始-->
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          >新增
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          >修改
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          icon="el-an-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          >删除
        </el-button>
      </el-col>
      <right-toolbar
        :showSearch.sync="showSearch"
        @queryTable="getList"
        :columns="columns"
      ></right-toolbar>
    </el-row>
    <!--操作按钮区结束-->

    <!--数据表格开始-->
    <div class="tableDiv">
      <el-table
        v-adjust-table
        size="mini"
        :height="showSearch ? 'calc(100vh - 320px)' : 'calc(100vh - 260px)'"
        style="width: 100%"
        v-loading="loading"
        border
        :data="missionList"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="50" align="center" />
        <el-table-column
          fixed
          label="序号"
          type="index"
          width="50"
        ></el-table-column>
        <el-table-column label="任务名称" align="center" prop="name" />
        <el-table-column label="下发时间" align="center" prop="updateTime">
          <template v-slot="scope">
            <span v-if="scope.row.status === '2'">
              {{ scope.row.updateTime | formatDate('YYYY年MM月DD日 hh:mm:ss') }}
            </span>
          </template>
        </el-table-column>
        <el-table-column label="任务发布人" align="center" prop="userName" />
        <el-table-column
          label="任务发布部门"
          width="220"
          align="center"
          prop="deptName"
        />
        <el-table-column label="任务状态" align="center" prop="status">
          <template v-slot="scope">
            <el-tag :type="scope.row.status === '2' ? 'success' : ''"
              >{{ scope.row.status === '1' ? '未下发' : '已下发' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="填报要求文件" align="center" prop="url">
          <template v-slot="scope">
            <FileUpload
              previewWidth="80%"
              for-view
              :value="scope.row.url"
              :download-name="`${scope.row.name}任务填报要求`"
            ></FileUpload>
          </template>
        </el-table-column>
        <el-table-column label="备注" align="center" prop="remark" />
        <el-table-column
          label="操作"
          fixed="right"
          align="center"
          width="300"
          class-name="small-padding fixed-width"
        >
          <template slot-scope="scope">
            <el-button
              v-if="scope.row.status === '1'"
              size="mini"
              type="text"
              icon="el-icon-edit"
              @click="handleUpdate(scope.row)"
              >修改
            </el-button>
            <el-button
              size="mini"
              type="text"
              icon="el-icon-delete"
              @click="handleDelete(scope.row)"
              >删除
            </el-button>
            <el-button
              v-if="scope.row.status === '1'"
              size="mini"
              type="text"
              icon="el-icon-upload2"
              @click="
                showDetail = true;
                currentRow = scope.row;
              "
              >上传表格
            </el-button>
            <el-button
              v-if="scope.row.status === '1'"
              size="mini"
              type="text"
              icon="el-icon-position"
              @click="handleIssued(scope.row)"
              >下发
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      <pagination
        v-show="total > 0"
        :total="total"
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
        @pagination="getList"
      />
    </div>
    <!--数据表格结束-->

    <!-- 添加或修改填报任务对话框 -->
    <el-dialog
      :title="title"
      :visible.sync="open"
      v-if="open"
      width="900px"
      destroy-on-close
      :close-on-click-modal="false"
      append-to-body
      :style="{ 'max-height': '90vh' }"
    >
      <div class="dialog-content">
        <el-form ref="form" :model="form" :rules="rules" label-width="140px">
          <el-form-item label="任务名称" prop="name">
            <el-input
              v-model="form.name"
              placeholder="请输入任务名称"
              :readonly="title.substring(0, 2) === '下发'"
            />
          </el-form-item>
          <el-form-item label="任务发布人" prop="userName">
            <el-input
              v-model="form.userName"
              :readonly="title.substring(0, 2) === '下发'"
              placeholder="请输入任务发布人"
            />
          </el-form-item>
          <el-form-item label="任务发布部门" prop="deptName">
            <el-input
              v-model="form.deptName"
              :readonly="title.substring(0, 2) === '下发'"
              placeholder="请输入任务发布部门"
            />
          </el-form-item>
          <el-form-item label="填报要求文件" prop="url">
            <FileUpload
              previewWidth="80%"
              v-model="form.url"
              platform="system"
              :limit="1"
              :fileType="['doc', 'docx', 'pdf']"
              :showTitle="true"
              :owner-id="Date.now()"
              @input="(value) => (this.form.url = value[0])"
            ></FileUpload>
          </el-form-item>
          <el-form-item label="备注" prop="remark">
            <el-input
              type="textarea"
              :rows="3"
              v-model="form.remark"
              placeholder="请输入备注"
            />
          </el-form-item>
          <el-form-item label="填报人部门" prop="userDeptList">
            <el-select
              style="width: 100%"
              v-model="selectedDepts"
              multiple
              placeholder="请选择填报角色部门"
              @change="handleChange"
            >
              <el-option
                v-for="item in optionsDept"
                :key="item.deptId"
                :label="item.deptName"
                :value="item.deptId"
              >
              </el-option>
            </el-select>
          </el-form-item>

          <el-form-item label="填报人岗位" prop="userPostList">
            <el-select
              style="width: 100%"
              v-model="selectedPosts"
              multiple
              placeholder="请选择填报人岗位"
              @change="handleChange"
            >
              <el-option
                v-for="item in optionsPost"
                :key="item.postId"
                :label="item.postName"
                :value="item.postId"
              >
              </el-option>
            </el-select>
          </el-form-item>

          <el-form-item label="填报人" prop="personList" v-if="open">
            <el-select
              style="width: 100%"
              v-model="selectedUsers"
              multiple
              placeholder="填报人"
            >
              <el-option
                v-for="item in userList"
                :key="item.userId"
                :label="item.nickName"
                :value="item.userId"
              >
              </el-option>
            </el-select>
          </el-form-item>

          <el-form-item label="填报人" prop="personList" v-if="false">
            <el-cascader
              placeholder="请选择填报人"
              ref="userCascade"
              v-model="oprUser"
              :options="deptUserOptions"
              :props="{ multiple: true, value: 'id', emitPath: false }"
              :show-all-levels="false"
              filterable
              clearable
              style="width: 100%"
            />
          </el-form-item>

          <el-form-item
            label="任务状态"
            prop="status"
            v-if="title.substring(0, 2) !== '下发'"
          >
            <el-radio-group v-model="form.status">
              <el-radio label="1">未下发</el-radio>
              <el-radio label="2" :disabled="title.substring(0, 2) === '添加'"
                >已下发</el-radio
              >
            </el-radio-group>
          </el-form-item>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button type="primary" @click="submitForm"
            >{{ title.substring(0, 2) === '下发' ? '下 发' : '确 定' }}
          </el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </div>
    </el-dialog>

    <MissionDetail
      :visible.sync="showDetail"
      :data="currentRow"
      title="填报内容管理"
      :append-to-body="true"
      size="55%"
    />
  </div>
</template>

<script>
import { getToken } from '@/utils/auth';
import {
  findFileUser,
  findFillDept,
  findFillPost,
  getUserProfile,
  listUser,
  findUsersParam,
} from '@/api/system/user';
import MissionDetail from './detail.vue';
import { getTreeStruct } from '@/api/tmpl';
import {
  addRepoteMission,
  delRepoteMission,
  getRepoteMission,
  listRepoteMission,
  updateRepoteMission,
} from '@/api/repote/repoteMission';

export default {
  name: 'Mission',
  components: { MissionDetail },
  data() {
    return {
      temp: null,
      //是否完成的状态
      iScompleteState: false,
      //用户列表
      userList: [],
      //选择框的postList
      selectedPosts: [],
      //选择框的deptList
      selectedDepts: [],
      //input填报人List
      selectedUsers: [],
      //部门的选项
      optionsDept: [],
      //角色的选项
      optionsPost: [],
      // 遮罩层
      loading: false,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: false,
      dictType: [],
      // 总条数
      total: 0,
      // 填报任务表格数据
      missionList: null,
      // 弹出层标题
      title: '',
      // 部门树选项
      deptOptions: undefined,
      // 是否显示弹出层
      open: false,
      showDetail: false,
      currentRow: {},
      // 表单参数
      form: {},
      defaultProps: {
        children: 'children',
        label: 'label',
      },
      deptUserOptions: [],
      // 用户导入参数
      upload: {
        // 是否显示弹出层（用户导入）
        open: false,
        // 弹出层标题（用户导入）
        title: '',
        // 是否禁用上传
        isUploading: false,
        // 是否更新已经存在的用户数据
        updateSupport: 0,
        // 设置上传的请求头部
        headers: { Authorization: 'Bearer ' + getToken() },
        // 上传的地址
        url: process.env.VUE_APP_BASE_API + '/system/user/importData',
      },
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 50,
        status: null,
        name: null,
        userId: null,
        userName: null,
        deptId: null,
        deptName: null,
      },
      // 列信息
      columns: [
        { key: 0, label: `任务名称`, visible: true },
        { key: 1, label: `任务发布人`, visible: true },
        { key: 2, label: `任务发布部门`, visible: true },
        { key: 3, label: `任务状态`, visible: true },
      ],
      // 表单校验
      rules: {
        name: [
          { required: true, message: '填报任务名称不能为空', trigger: 'blue' },
        ],
        personList: [
          { required: true, message: '填报人不能为空', trigger: 'blue' },
        ],
      },
    };
  },
  created() {
    this.getList();
    getTreeStruct({ types: 111 }).then((response) => {
      this.deptUserOptions = response.data;
    });
  },
  watch: {
    selectedUsers() {
      this.form.personList = this.selectedUsers.map((item) => {
        return {
          userId: item,
          // userName: item.label
          userName: this.userList.filter((i) => i.userId == item)[0]?.nickName,
        };
      });
    },
  },
  computed: {
    oprUser: {
      set() {
        let userList = this.$refs.userCascade.getCheckedNodes();
        this.form.personList = userList.map((item) => {
          return {
            userId: item.value,
            userName: item.label,
          };
        });
      },
      get() {
        return this.form.personList?.map((item) => item.userId);
      },
    },
  },
  methods: {
    getList() {
      this.loading = true;
      listRepoteMission(this.queryParams).then((response) => {
        this.missionList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    //打开新增页面就将选择上的userList展示在selectedUsers中
    getUserNickName() {
      // this.selectedUsers = this.userList.map(user => user.userId)
    },
    //监听岗位和部门变化
    handleChange(e) {
      console.log(e, this.selectedPosts, this.selectedDepts);
      findFileUser({
        selectedPosts: this.selectedPosts,
        selectedDepts: this.selectedDepts,
      }).then((res) => {
        if (res.code === 200) {
          this.userList = res.data;
          this.selectedUsers = this.userList.map((user) => user.userId);
        }
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      // this.reset()
    },
    // 表单重置
    reset() {
      this.resetForm('form');
      getUserProfile().then((res) => {
        let user = res.data;
        this.form = {
          personList: null,
          status: '1',
          name: null,
          userId: user.userId,
          userName: user.nickName,
          deptId: user.dept.deptId,
          deptName: user.dept.deptName,
        };
      });
      this.selectedPosts = [];
      this.selectedDepts = [];
      this.selectedUsers = [];
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dateRange = [];
      this.resetForm('queryForm');
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id);
      this.single = selection.length != 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = '添加填报任务';
      findFillPost().then((res) => {
        if (res.code === 200) {
          this.optionsPost = res.data;
        }
      });
      findFillDept().then((res) => {
        if (res.code === 200) {
          this.optionsDept = res.data;
        }
      });
      this.getUserNickName();
    },
    /** 修改按钮操作 */
    async handleUpdate(row) {
      // await  findFillPost().then((res) => {
      //     if (res.code === 200) {
      //       this.optionsPost = res.data;
      //     }
      //   });
      // await findFillDept().then((res) => {
      //     if (res.code === 200) {
      //       this.optionsDept = res.data;
      //     }
      //   });
      this.getUserNickName();

      const id = row.id || this.ids;
      getRepoteMission(id).then((response) => {
        this.form = response.data;
        this.open = true;
        this.title = '修改填报任务';

        // this.selectedPosts = []
        // this.selectedDepts = []
        // this.selectedUsers = []
        // this.userList = this.form.personList.map(item => {
        //   return {
        //     ...item,
        //     nickName: item.userName
        //   }
        // })

        listUser({ pageNum: 1, pageSize: 5000 }).then((res) => {
          let userIds = this.form.personList.map((user) => user.userId);
          this.userList = res.rows.filter((item) =>
            userIds.includes(item.userId)
          );
          this.selectedUsers = this.form.personList.map((user) => user.userId);
        });
      });
    },
    getUserList() {
      let userIds = this.form.personList.map((user) => user.userId);
      findUsersParam({ userIds: userIds }).then((res) => {
        this.userList = res.data;
        this.selectedUsers = this.form.personList.map((user) => user.userId);
      });
    },
    handleIssued(row) {
      const id = row.id || this.ids;
      getRepoteMission(id).then((response) => {
        this.form = response.data;
        this.form.status = '2';
        this.open = true;
        this.title = '下发填报任务';

        // listUser({ pageNum: 1, pageSize: 5000 }).then((res) => {
        //   let userIds = this.form.personList.map((user) => user.userId);
        //   this.userList = res.rows.filter((item) =>
        //     userIds.includes("" + item.userId)
        //   );
        //   this.userList.forEach((item) => (item.userId = item.userId + ""));
        //   this.selectedUsers = this.form.personList.map((user) => user.userId);
        // });
        listUser({ pageNum: 1, pageSize: 5000 }).then((res) => {
          let userIds = this.form.personList.map((user) => user.userId);
          this.userList = res.rows.filter((item) =>
            userIds.includes(item.userId)
          );
          this.selectedUsers = this.form.personList.map((user) => user.userId);
        });
      });
    },
    /** 提交按钮 */
    submitForm: function () {
      this.$refs['form'].validate((valid) => {
        if (valid) {
          if (this.form.id != null) {
            updateRepoteMission(this.form).then((response) => {
              this.$modal.msgSuccess('修改成功');
              this.open = false;
              this.getList();
            });
          } else {
            addRepoteMission(this.form).then((response) => {
              this.$modal.msgSuccess('新增成功');
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      if (row.id) {
        const id = row.id;
        this.$modal
          .confirm('是否确认删除填报任务名称为' + row.name + '的数据项？')
          .then(function () {
            return delRepoteMission(id);
          })
          .then(() => {
            this.getList();
            this.$modal.msgSuccess('删除成功');
          });
      } else {
        if (this.ids.length > 0) {
          this.$modal
            .confirm('是否确认删除选中的' + this.ids.length + '条数据项？')
            .then(() => {
              let delArray = [];
              this.ids.forEach((item) => {
                delArray.push(delRepoteMission(item));
              });
              Promise.all(delArray).then(
                () => this.$modal.msgSuccess('删除成功') || this.getList()
              );
            });
        }
      }
    },
  },
};
</script>
<style scoped lang="scss">
.dialog-content {
  max-height: 70vh;
  overflow-y: auto;
}
.dialog-footer {
  text-align: right;
}
.tableDiv {
  ::v-deep .el-upload-list__item {
    width: 42px;
    height: 42px;
    margin: 0px 5px -6px 0 !important;
  }

  ::v-deep .el-upload--picture-card {
    width: 42px;
    height: 42px;
    margin: 4px 0;
  }
  ::v-deep .el-icon-plus {
    font-size: 1rem;
  }
  ::v-deep .el-upload-list__item-actions {
    font-size: 1rem;
  }

  ::v-deep .el-upload-list__item-actions span + span {
    margin-left: 5px !important;
  }
}
</style>
