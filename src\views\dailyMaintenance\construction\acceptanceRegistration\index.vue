<template>
  <div class="app-container maindiv">
    <registration-list ref="regis" :dis-stage="6" :page-list="pageList">
      <template #btn>
        <el-col :span="1.5">
          <el-button
              icon="el-icon-download"
              size="mini"
              type="warning"
              v-has-menu-permi="['check:construction:export']"
              @click="exportList"
          >导出清单
          </el-button
          >
        </el-col>
      </template>
      <template #operate="scope">
        <el-button
            icon="el-icon-edit"
            size="mini"
            type="text"
            @click="handleRegis (scope.scope.row)"
        >验收登记
        </el-button>
<!--        <el-button-->
<!--            icon="el-icon-check"-->
<!--            size="mini"-->
<!--            type="text"-->
<!--            v-has-menu-permi="['check:construction:review']"-->
<!--            @click="handlePass (scope.scope.row)"-->
<!--        >通过-->
<!--        </el-button>-->
<!--        <el-button-->
<!--            icon="el-icon-close"-->
<!--            size="mini"-->
<!--            type="text"-->
<!--            v-has-menu-permi="['check:construction:reject']"-->
<!--            @click="handleReject(scope.scope.row)"-->
<!--        >驳回-->
<!--        </el-button>-->
      </template>
    </registration-list>
    <el-drawer :title="drawerTitle" :visible.sync="drawer" :wrapperClosable="false" append-to-body destroy-on-close
               modal-append-to-body size="70%">
      <detail :row-data="rowData" @close="handleCloseDetail"></detail>
    </el-drawer>
    <el-dialog v-if="checkModel" :visible.sync="checkModel" append-to-body destroy-on-close modal-append-to-body title="审核意见"
               width="50%">
      <el-form ref="checkForm" :model="formData" :rules="rules" label-width="120px" size="small">
        <el-form-item label="验收人员" prop="visaBy">
          <el-cascader
              ref="deptUser"
              v-model="formData.visaBy"
              :options="deptUserOptions"
              :props="props"
              :show-all-levels="false"
              clearable filterable style="width: 100%"></el-cascader>
        </el-form-item>
        <el-form-item label="审核人" prop="visaCheckBy">
          <el-cascader
              ref="deptUser"
              v-model="formData.visaCheckBy"
              :options="deptUserOptions"
              :props="{
                multiple: false,
                value: 'id',
                emitPath: false
              }"
              :show-all-levels="false"
              clearable filterable style="width: 100%"></el-cascader>
        </el-form-item>
        <el-form-item label="审核意见" prop="remark">
          <el-input v-model="formData.remark" :rows="4" placeholder="请输入审核意见" style="width: 100%"
                    type="textarea"/>
        </el-form-item>
        <div style="text-align: right">
          <el-button type="primary" @click="submitReview">保存</el-button>
        </div>
      </el-form>
    </el-dialog>
  </div>
</template>
<script>
import registrationList from '../component/registrationList.vue'
import {pageList, reject, submitReview} from "@/api/dailyMaintenance/construction/acceptanceRegistration";
import Detail from "./detail.vue";
import {getTreeStruct} from "@/api/tmpl";

export default {
  name: "AcceptanceRegistration",
  components: {Detail, registrationList},
  data() {
    return {
      drawerTitle: '验收审核',
      drawer: false,
      rowData: {},
      formData: {},
      // 部门-用户树选项
      deptUserOptions: [],
      props: {
        multiple: true,//是否多选
        value: "id",
        emitPath: false
      },
      checkModel: false,
      rules: {
        visaBy: [
          {required: true, message: '请选择验收人员', trigger: 'change'}
        ],
        visaCheckBy: [
          {required: true, message: '请选择审核人', trigger: 'change'}
        ],
        remark: [
          {required: true, message: '请输入审核意见', trigger: 'blur'}
        ],
      }
    }
  },
  created() {
    this.getDeptTreeDef()
  },
  methods: {
    pageList,
    /** 查询部门-用户下拉树结构 */
    getDeptTreeDef() {
      getTreeStruct({types: 111}).then(response => {
        this.deptUserOptions = response.data;
      });
    },
    handleRegis(rows) {
      this.rowData = rows
      this.drawer = true
    },
    handlePass(rows) {
      this.checkModel = true
      this.rowData = rows
      // this.$modal.confirm('是否确认通过').then(() => {
      //   submitReview(rows).then(res => {
      //     this.$message.success('通过成功')
      //     this.$refs.regis.handleQuery()
      //   })
      // })
    },
    handleReject(rows) {
      this.$prompt('', '是否确认驳回', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        inputPattern: /^.+$/,
        inputErrorMessage: '请填写审核意见'
      }).then(({value}) => {
        reject(rows).then(res => {
          this.$message.success('驳回成功')
          this.$refs.regis.handleQuery()
        })
      })
    },
    submitReview() {
      this.$refs.checkForm.validate(valid => {
        if (!valid) return
        this.rowData.visaBy = this.formData.visaBy.join(',')
        this.rowData.visaCheckBy = this.formData.visaCheckBy
        this.rowData.checkDesc = this.formData.checkDesc
        submitReview(this.rowData).then(res => {
          this.$message.success('通过成功')
          this.$refs.regis.handleQuery()
        }).finally(() => {
          this.checkModel = false
        })
      })
    },
    handleCloseDetail() {
      this.rowData = {}
      this.drawer = false
      this.$refs.regis.handleQuery()
    },
    // 导出清单按钮
    exportList() {
      this.$refs.regis.queryParams.year = this.$refs.regis.queryParams.yearStr ? parseInt(this.$refs.regis.queryParams.yearStr) : null

      this.download(
          'manager/check/export',
          {...this.$refs.regis.queryParams},
          `check_${new Date().getTime()}.xlsx`,
          {
            headers: {'Content-Type': 'application/json;'},
            parameterType: 'body'
          }
      )
    },
  }
}
</script>
