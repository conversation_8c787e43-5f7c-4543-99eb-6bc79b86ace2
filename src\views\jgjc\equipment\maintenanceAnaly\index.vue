<template>
  <div class='app-container'>
    <el-row :gutter='20'>
      <!--部门数据-->
      <el-col :span='relaNav ? 4 : 0' :xs='24' class='leftDiv'>
        <!--折叠图标-->
        <div class='leftIcon' @click='relaNav = false'>
          <span class='el-icon-caret-left'></span>
        </div>
        <div class='head-container' style='width: 300px'>
          <el-tree
            ref='tree'
            :data='filteredTreeData'
            :default-expanded-keys='[1]'
            :expand-on-click-node='false'
            :props='defaultProps'
            highlight-current
            node-key='code'
            @node-click='handleNodeClick'
          >
          </el-tree>
        </div>
      </el-col>
      <!--角色数据-->
      <el-col :span='relaNav ? 20 : 24' :xs='24'>
        <!--展开图标-->
        <div v-show='!relaNav' class='rightIcon' @click='relaNav = true'>
          <span class='el-icon-caret-right'></span>
        </div>
<!--        <el-row :gutter='10' class='mb8'>-->
<!--          <el-col :span="24">-->
<!--            <el-form ref="queryForm" :inline="true" :model="queryParams" label-width="68px" size="small">-->
<!--              <el-form-item label="" prop="dealStatus">-->
<!--                <el-select v-model="queryParams.dealStatus" placeholder="处理状态">-->
<!--                  <el-option label="待处理" value="待处理"></el-option>-->
<!--                  <el-option label="正在处理" value="正在处理"></el-option>-->
<!--                  <el-option label="已处理" value="已处理"></el-option>-->
<!--                </el-select>-->
<!--              </el-form-item>-->
<!--              <el-form-item>-->
<!--                <el-button icon="el-icon-search" size="mini" type="primary" @click="handleQuery">搜索</el-button>-->
<!--                <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>-->
<!--              </el-form-item>-->
<!--            </el-form>-->
<!--          </el-col>-->
<!--        </el-row>-->
        <!-- 筛选区结束 -->
        <!-- 数据表格开始 -->
        <div class='tableDiv'>
          <el-table v-adjust-table v-loading='loading' :data='dataList'
                    :height="'calc(100vh - 260px)'" border size='mini'
                    @selection-change='handleSelectionChange'
                    style='width: 100%'>
            <el-table-column align='center' fixed='left' type='selection' width='55'></el-table-column>
            <el-table-column align='center' fixed label='序号' type='index' width='100'></el-table-column>
            <template v-for='(column, index) in columns'>
              <el-table-column v-if='column.visible' :key='index' :label='column.label' :prop='column.field'
                               align='center' show-overflow-tooltip>
                <template slot-scope="scope">
                  <dict-tag v-if="column.dict" :options="dict.type[column.dict]" :value="scope.row[column.field]"/>
                  <template v-else-if="column.slots">
                    <RenderDom :index="index" :render="column.render" :row="scope.row"/>
                  </template>
                  <span v-else-if="column.isTime">{{ parseTime(scope.row[column.field], '{y}-{m}-{d}') }}</span>
                  <span v-else>{{ scope.row[column.field] }}</span>
                </template>
              </el-table-column>
            </template>
            <el-table-column align='center' fixed='right' label='操作' width='150'>
              <template slot-scope='scope'>
                <el-button
                  size='mini'
                  icon='el-icon-edit'
                  type='text'
                  @click='handleEdit(scope.row)'
                >故障分析
                </el-button>
              </template>
            </el-table-column>
          </el-table>
          <pagination
            v-show='total > 0'
            :limit.sync='queryParams.pageSize'
            :page.sync='queryParams.pageNum'
            :total='total'
            @pagination='handleQuery'
          />
        </div>
        <!-- 数据表格结束 -->
      </el-col>
    </el-row>
    <el-dialog :title='dialogTitle' :visible.sync='dialogVisible' v-if='dialogVisible' append-to-body destroy-on-close
               width='80%'>
      <el-form ref='elForm' :model='formData' :rules='rules' label-width='120px' size='medium'>
        <el-row :gutter='20'>
          <!-- 设备型号编码 -->
          <el-col :span='12'>
            <el-form-item label='故障原因' prop='faultCause'>
              <el-select  v-model='formData.faultCause' placeholder='请选择故障原因'>
                <el-option label="设备故障" value="设备故障"></el-option>
                <el-option label="软件故障" value="软件故障"></el-option>
                <el-option label="误报" value="误报"></el-option>
              </el-select>
            </el-form-item>
          </el-col>

          <!-- 故障描述 -->
          <el-col :span='24'>
            <el-form-item label='故障描述' prop='analyzeDescription'>
              <el-input
                v-model='formData.analyzeDescription'
                type='textarea'
                :rows='3'
                placeholder='请输入详细的故障描述'
                style='width: 100%'
              />
            </el-form-item>
          </el-col>

          <!-- 故障照片 -->
          <el-col :span='24'>
            <el-form-item label='故障照片' prop='attachments'>
              <file-upload platform='jgjc' ref="fileUpload" v-model="formData.attachments" :limit=1 :ownerId="formData.attachments"/>
            </el-form-item>
          </el-col>
        </el-row>
        <div style="text-align: right; margin-top: 20px">
          <el-button size="mini" type="primary" @click="handleSave">保存</el-button>
          <el-button size="mini" @click="dialogVisible = false">退出</el-button>
        </div>
      </el-form>
    </el-dialog>
  </div>
</template>

<script>
import { getJCDomainTree, getSensorDeviceTreeNode } from '@/api/jgjc/baseInfo/alarmLog'
import {
  getDeviceMaintenanceRecordPage,
  deleteDeviceMaintenanceAnalyze,
  saveDeviceMaintenanceAnalyze,
} from '@/api/jgjc/equipment/maintenance'

export default {
  dicts: ['fault_source'],
  name: 'ContentManagement',
  data() {
    return {
      // 遮罩层
      loading: false,
      // 左侧组织树
      relaNav: true,
      relaOptions: [],
      filteredTreeData: [],
      defaultProps: {
        children: 'children',
        label: 'label',
        isLeaf: (data) => {
          // 通过该字段判断是否是叶子节点（没有子节点）
          // 这里假设接口返回的节点中，有hasChildren字段标识是否有子节点
          return !data.children
        },
      },
      loadedKeys: new Set(), // 记录已加载过的节点key，避免重复加载
      // 总条数
      total: 0,
      dataList: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
      },
      // 列信息
      columns: [
        { key: 0, field: 'dealStatus', label: '处理状态', visible: true },
        { key: 1, field: 'deviceModelCode', label: '设备编码', visible: true },
        { key: 2, field: 'deviceTypeName', label: '设备类型', visible: true },
        { key: 3, field: 'position', label: '安装位置', visible: true },
        { key: 4, field: 'faultSource', label: '故障来源', visible: true, dict: 'fault_source' },
        { key: 5, field: 'faultTime', label: '异常发生时间', visible: true },
        { key: 6, field: 'faultDescription', label: '故障原因', visible: true },
        { key: 7, field: 'reportPerson', label: '报告人', visible: true },
        { key: 8, field: 'reportTime', label: '报告时间', visible: true },
      ],
      ids: [],
      formData: {},
      dialogTitle: '新增',
      dialogVisible: false,
      rules: {
        faultCause: [
          { required: true, message: '请选择故障原因', trigger: 'change' },
        ],
      },
    }
  },
  created() {
    this.getDeptTree()
  },
  methods: {
    // 查询部门下拉树结构
    getDeptTree() {
      getJCDomainTree({}).then(response => {
        this.relaOptions = response.data
        this.filteredTreeData = [...this.relaOptions]
      })
    },
    // 树节点点击事件
    handleNodeClick(nodeData, node) {
      // 如果节点没有子节点且未被加载过
      if (node.level == 4 && !node.childNodes.length && nodeData.structureId && !this.loadedKeys.has(nodeData.structureId)) {
        this.loadChildren(node)
      }
      if (node.level != 5) {
        console.log(node)
        let params = {}
        if (node.level == 1) {
          params.domainName = nodeData.deptName
        } else if (node.level == 2) {
          params.domainName = node.parent.data.deptName
          params.roadName = nodeData.label
        } else if (node.level == 3) {
          params.domainName = node.parent.parent.data.deptName
          params.roadName = node.parent.data.label
          params.structureType = nodeData.structureType
        } else {
          params = nodeData
          // 判断params里面有没有structureId 没有就从父级找 一直找到为止 找到后赋值给params
          let tempNode = node
          while (!tempNode.data.structureId) {
            tempNode = tempNode.parent
          }
          params.structureId = tempNode.data.structureId
        }
        this.queryParams = {
          pageNum: 1,
          pageSize: 10,
          level: node.level,
          ...params,
        }
        this.handleQuery()
      }
    },

    // 加载子节点数据
    async loadChildren(node) {
      const nodeData = node.data
      // 显示加载状态
      node.loading = true
      try {
        const res = await getSensorDeviceTreeNode({ structureId: nodeData.structureId })
        if (res.data && res.data.length > 0) {
          // 给树节点添加子节点（Vue.set确保响应式）
          this.$set(nodeData, 'children', res.data)
          // 展开当前节点（可选）
          this.$nextTick(() => {
            node.expanded = true
          })
        }
        // 标记为已加载
        this.loadedKeys.add(nodeData.structureId)
      } finally {
        node.loading = false
      }
    },
    handleQuery() {
      this.loading = true
      this.queryParams.dealStatus = '待处理'
      getDeviceMaintenanceRecordPage(this.queryParams).then(res => {
        this.dataList = res.rows
        this.total = res.total
        this.loading = false
      }).catch(err => {
        this.loading = false
        console.error(err)
      })
    },
    resetQuery() {
      this.queryParams = {
        pageNum: 1,
        pageSize: 10,
      }
      this.handleQuery()
    },
    handleEdit(row) {
      this.dialogTitle = '故障分析'
      this.formData = {
        recordId: row.id
      }
      this.dialogVisible = true
    },
    handleSave() {
      this.$refs.elForm.validate((valid) => {
        if (!valid) return
        if (this.formData.attachments && Array.isArray(this.formData.attachments)) {
          this.formData.attachments = this.formData.attachments[0]
        }
        saveDeviceMaintenanceAnalyze(this.formData).then((res) => {
          this.$message({
            message: '操作成功',
            type: 'success',
          })
          this.handleQuery()
          this.dialogVisible = false
        })
      })
    },
    handleSelectionChange(e) {
      this.ids = e.map(item => item.id)
    },
    handleDelete() {
      if (this.ids.length <= 0) {
        this.$message.warning('请勾选需要删除的数据')
        return
      }
      this.$confirm('是否确认删除选中的数据？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }).then(() => {
        this.loading = true
        deleteDeviceMaintenanceAnalyze({ ids: this.ids }).then(res => {
          if (res.code === 200) {
            this.$message.success('删除成功')
            this.handleQuery()
          } else {
            this.$message.error(res.msg)
          }
          this.loading = false
        }).catch(err => {
          this.loading = false
          console.error(err)
        })
      })
    },
  },
}
</script>

<style scoped>
.leftDiv {
  border-right: 1px solid #d8dce5;
  min-height: calc(100vh - 100px);
  overflow-y: auto;
  height: calc(80vh - 150px);
  position: relative;
  top: -20px;
  padding-top: 10px;
  background-color: white;
}

.leftIcon {
  border: 1px solid #dcdfe6;
  border-radius: 8px;
  width: 16px;
  height: 50px;
  line-height: 50px;
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
  position: absolute;
  right: 0;
  top: 300px;
  z-index: 2;
}

.leftIcon:hover {
  background-color: #dcdfe6;
}

.rightIcon {
  border: 1px solid #dcdfe6;
  border-radius: 8px;
  width: 16px;
  height: 50px;
  line-height: 50px;
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
  position: absolute;
  left: -10px;
  top: 280px;
  z-index: 10;
  background: white;
}

.rightIcon:hover {
  background-color: #dcdfe6;
}

.tableDiv {
  margin-top: 20px;
}
</style>
<style lang='scss' scoped>
@import "@/assets/styles/business.scss";
</style>
