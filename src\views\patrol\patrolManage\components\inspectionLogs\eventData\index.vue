<template>
  <div class="app-container maindiv">
    <el-row :gutter="20">
      <!--部门数据-->

      <!--角色数据-->
      <el-col :span="relaNav ? 19 : 24" :xs="24">
        <!--展开图标-->
        <div class="rightIcon" @click="relaNav = true" v-show="!relaNav">
          <span class="el-icon-caret-right"></span>
        </div>

        <!--操作按钮区开始-->

        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button type="primary" icon="el-icon-unlock" size="mini" @click="unbind">
              事件解绑
            </el-button>
          </el-col>

          <el-col :span="1.5">
            <el-button
              type="success"
              icon="el-icon-view"
              size="mini"
              @click="openDisInfo"
            >事件信息
            </el-button
            >
          </el-col>
        </el-row>

        <el-row>
          <div class="draggable">
            <el-table
              size="mini"
              style="width: 100%"
              v-loading="loading"
              border
              :data="tableData"
              ref="dataTable"
              stripe
              highlight-current-row
              @row-click="handleClickRow"
              @selection-change="handleSelectionChange"
              :height="
                showSearch ? 'calc(100vh - 330px)' : 'calc(100vh - 270px)'
              "
            >
              <el-table-column type="selection" width="50" align="center"/>
              <el-table-column fixed label="序号" type="index" width="50">
                <template v-slot="scope">
                  {{ (scope.$index + (queryParams.pageNum - 1) * queryParams.pageSize)+1 }}
                </template>
              </el-table-column>
              <template v-for="(column,index) in columns">
                <el-table-column :label="column.label"
                                 v-if="column.visible"
                                 align="center"
                                 :prop="column.field"
                                 :width="column.width">
                  <template slot-scope="scope">
                    <dict-tag v-if="column.dict" :options="dict.type[column.dict]" :value="scope.row[column.field]"/>
                    <template v-else-if="column.slots">
                      <RenderDom :row="scope.row" :index="index" :render="column.render"/>
                    </template>
                    <span v-else-if="column.isTime">{{ parseTime(scope.row[column.field], '{y}-{m}-{d}') }}</span>
                    <span v-else>{{ scope.row[column.field] }}</span>
                  </template>
                </el-table-column>
              </template>
<!--              <el-table-column-->
<!--                label="操作"-->
<!--                fixed="right"-->
<!--                align="center"-->
<!--                width="160"-->
<!--                class-name="small-padding fixed-width"-->
<!--              >-->
<!--                <template slot-scope="scope">-->
<!--                  <el-button-->
<!--                    size="mini"-->
<!--                    type="text"-->
<!--                    icon="el-icon-edit"-->
<!--                    @click="handleUpdate(scope.row)"-->
<!--                  >编辑-->
<!--                  </el-button-->
<!--                  >-->
<!--                  <el-popover-->
<!--                    placement="top"-->
<!--                    width="250"-->
<!--                    trigger="hover">-->
<!--                    <div style="text-align: center; margin: 0">-->
<!--                      <el-button type="text" size="mini" @click="handleSubmit(scope.row, 1)" :disabled="scope.row.disStatus != 1">提交到被损工程</el-button>-->
<!--                      <el-button type="text" size="mini" @click="handleSubmit(scope.row, 2)" :disabled="scope.row.disStatus != 1">提交到被盗工程</el-button>-->
<!--                    </div>-->
<!--                    <template #reference>-->
<!--                      <el-button-->
<!--                        size="mini"-->
<!--                        type="text"-->
<!--                        :disabled="scope.row.disStatus != 1"-->
<!--                        icon="el-icon-edit"-->
<!--                      >操作-->
<!--                      </el-button>-->
<!--                    </template>-->
<!--                  </el-popover>-->

<!--                  <el-button-->
<!--                    size="mini"-->
<!--                    type="text"-->
<!--                    icon="el-icon-delete"-->
<!--                    :disabled="!(scope.row.disStatus == 1 && scope.row.stageName == '新增')"-->
<!--                    @click="handleDelete(scope.row)"-->
<!--                  >删除-->
<!--                  </el-button-->
<!--                  >-->
<!--                </template>-->
<!--              </el-table-column>-->
            </el-table>
<!--            <pagination-->
<!--              v-show="total>0"-->
<!--              :total="total"-->
<!--              :page.sync="queryParams.pageNum"-->
<!--              :limit.sync="queryParams.pageSize"-->
<!--              @pagination="handleQuery"-->
<!--            />-->
          </div>
        </el-row>
      </el-col>
    </el-row>
    <el-drawer :wrapperClosable="false" :title="drawerTitle" destroy-on-close :visible.sync="drawer" size="70%">
      <detail @close="handleCloseDetail" :row-data="rowData" :select-data="queryParams"></detail>
    </el-drawer>
    <el-dialog title="事件信息" destroy-on-close :visible.sync="dialogVisible" width="70%" v-if="dialogVisible">
      <event-detail :dis-info="rowData" :daliy-id="rowData.daliyId"/>
<!--      <event-info :dis-id="rowData.id" :daliy-id="rowData.daliyId"></event-info>-->
    </el-dialog>
    <el-dialog title="附件列表" destroy-on-close :visible.sync="openFile" width="70%">
      <file-upload v-model="disFilePath" :forView="true"></file-upload>
    </el-dialog>
    <el-dialog title="提交到" destroy-on-close :visible.sync="openOperate" width="70%">
    </el-dialog>
  </div>
</template>

<script>
import Detail from "./detail.vue";
import {findUserDeptMaintenanceList} from '@/api/system/maintenanceSection'
import {
  findDiseaseDataList,
  batchDeleteDiseaseData,
  deleteDiseaseData, saveToTheft
} from '@/api/dailyMaintenance/eventManage/eventData.js'
import selectTree from "@/components/DeptTmpl/selectTree.vue";
import RoadSection from "@/views/baseData/components/roadSection/index.vue";
import moment from "moment";
import {listAllDiseases} from "@/api/patrol/diseases";
import EventDetail from "@/views/dailyMaintenance/component/eventDetail.vue";
import {getEventListByRecordId} from "@/api/dailyMaintenance/eventManage/eventReview";
import {formatPile} from "@/utils/ruoyi";

export default {
  components: {
    RoadSection, selectTree,EventDetail,
    Detail,
    RenderDom: {
      functional: true,
      props: {
        row: Object,
        index: Number,
        render: Function
      },
      render(createElement, ctx) {
        const {row, index} = ctx.props
        return ctx.props.render(row, index)
      }
    }
  },
  dicts: ['route_direction', 'lane', 'event_type', 'sys_asset_type'],
  props: ['dataId'],
  data() {
    return {
      leftTotal: 1,
      showSearch: false,
      queryParams: {
        pageNum: 1,
        pageSize: 50
      },
      total: 0,
      loading: false,
      disType: [],
      columns: [
        {key: 0, width: 100, field: 'disStatusName', label: `状态`, visible: true},
        {key: 1, width: 100, field: 'stageName', label: `阶段`, visible: true},
        {key: 22, width: 200, field: 'disCode', label: `事件编码`, visible: true},
        {key: 2, width: 100, field: 'maiSecId', label: `路段名称`, visible: true},
        {key: 3, width: 100, field: 'routeCode', label: `路线编码`, visible: true},
        {key: 4, width: 100, field: 'domainName', label: `管养单位`, visible: true},
        {key: 5, width: 100, field: 'assetMainType', label: `资产类型`, visible: true, dict: 'sys_asset_type'},
        {key: 6, width: 100, field: 'assetName', label: `资产`, visible: true},
        {key: 7, width: 200, field: 'name', label: `施工单`, visible: true},
        {key: 8, width: 200, field: 'code', label: `施工编号`, visible: true},
        {key: 9, width: 100, field: 'beginMileShow', label: `起点桩号`, visible: true},
        {key: 10, width: 100, field: 'endMileShow', label: `终点桩号`, visible: true},
        {key: 11, width: 100, field: 'direction', label: `上下行`, visible: true, dict: 'route_direction'},
        {key: 12, width: 100, field: 'lane', label: `位置`, visible: true, dict: 'lane'},
        {key: 13, width: 100, field: 'disFrom', label: `事件来源`, visible: true},
        // {key: 14, width: 100, field: 'disSort', label: `事件分类`, visible: true, dict: 'event_type'},
        {key: 15, width: 150, field: 'disTypeName', label: `事件类型`, visible: true},
        {
          key: 16,
          width: 100,
          field: 'disFilePath',
          label: `附件`,
          visible: true,
          slots: true,
          render: (row, index) => {
            return (
              <el-button
                size="mini"
                disabled={!row.disFilePath}
                type="text" onClick={e => this.handleOpenFile(e, row)}>查看</el-button>
            )
          }
        },
        {key: 17, width: 100, field: 'disDesc', label: `事件描述`, visible: true,
          slots: true,
          render: (row) => {
            return (
              <el-tooltip className="item" effect="dark" content={row.disDesc} placement="top-start">
                <div style="overflow: hidden;white-space:nowrap;text-overflow:ellipsis">{row.disDesc}</div>
              </el-tooltip>
            )
          }
        },
        {key: 18, width: 100, field: 'updateByName', label: `操作人`, visible: true},
        {key: 19, width: 100, field: 'reportName', label: `上报人`, visible: true},
        {key: 20, width: 100, field: 'reviewName', label: `审核人`, visible: true},
        {key: 21, width: 150, field: 'collectTime', label: `采集时间`, visible: true}
      ],
      tableData: [],
      rowData: {},
      drawerTitle: '新增事件',
      drawer: false,
      openFile: false,
      openOperate: false,
      dialogVisible: false,
      selectIds: [],
      clickRow: {},
      // 左侧组织树
      relaNav: true,
      disFilePath: '',
      keyword: '',
      relaName: '',
      relaOptions: [],
      filteredTreeData: [],

    }
  },
  computed: {},
  watch: {
      dataId(){
        console.log('监听到变化了')
        this.handleQuery()
      }

  },
  created() {
    this.getDeptTree()
    this.handleQuery()
    this.getDisType()
  },
  mounted() {
  },
  methods: {

    /** 解绑操作 */
    unbind(row) {
      // const id = row.id || this.ids;
      const id = row.id || this.selectIds;
      this.$confirm('是否确认解绑该事件？', "系统提示", {confirmButtonText: '确定', cancelButtonText: '取消',
        type: "warning",
        customClass:'margin-left-50'
      }).then(function () {
        return unbindRecord(JSON.stringify(id));
      }).then(() => {
        this.getList();
        this.$message({message: '"删除成功"', customClass: 'margin-left-25'})
      }).catch(() => {
      })
    },
    // 关键词检索
    handleSearch() {
      const keyword = this.keyword.toLowerCase();
      this.filteredTreeData = this.relaOptions.filter(node => this.filterNode(node, keyword));
    },
    // 筛选节点
    filterNode(node, keyword) {
      if (node.label.indexOf(keyword) != -1) {
        return true;
      }
      if (node.children) {
        return node.children.some(childNode => this.filterNode(childNode, keyword));
      }
      return false;
    },
    // 查询部门下拉树结构
    getDeptTree() {
      findUserDeptMaintenanceList().then(response => {
        const treeData = response.data
        treeData.forEach(item => {
          getChild(item)
        })

        function getChild(node) {
          node.label = node.deptName || node.maintenanceSectionName
          node.id = node.deptId || node.maintenanceSectionId
          if (node.children) {
            node.children.forEach(item => {
              getChild(item)
            })
          }
        }

        // 增加一个最顶级
        const tree = [
          {
            label: '云南省交通投资建设集团有限公司',
            id: '1',
            deptId: '1',
            children: [...treeData]
          }
        ]
        this.relaOptions = tree
        this.filteredTreeData = [...this.relaOptions]
      });
    },
    handleNodeClick(e) {
      this.queryParams.domainId = ''
      this.queryParams.domainName = ''
      this.queryParams.maiSecName = ''
      this.queryParams.maiSecId = ''
      this.queryParams.domainIdStr = ''
      if (e.deptId) {
        this.queryParams.domainIdStr = String(e.id)
        this.queryParams.domainId = parseInt(e.id)
        this.queryParams.domainName = e.label
      } else {
        this.queryParams.domainIdStr = String(e.departmentId)
        this.queryParams.domainId = parseInt(e.departmentId)
        this.queryParams.domainName = e.departmentName
        this.queryParams.maiSecName = e.label
        this.queryParams.maiSecId = e.id
      }
      this.handleQuery()
    },
    // 选中
    handleSelectionChange(e) {
      this.selectIds = e.map(obj => obj.id)
    },
    // 查询
    handleQuery() {
      this.loading = true
      // this.queryParams.year = parseInt(this.queryParams.yearStr) || null
      // this.queryParams.domainId = parseInt(this.queryParams.domainIdStr) || null
      // this.queryParams.endMile = parseInt(this.queryParams.endMileStr) || null
      // this.queryParams.beginMile = parseInt(this.queryParams.beginMileStr) || null
      // this.queryParams.assetMainType = parseInt(this.queryParams.assetTypeStr) || null
      // this.queryParams.disStatus = parseInt(this.queryParams.disStatusStr) || null
      // this.queryParams.stage = parseInt(this.queryParams.stageStr) || null
      // if (this.queryParams.domainId == 1) {
      //   this.queryParams.domainId = null
      // }
      // const endTime = this.queryParams.collectTimee ? moment(this.queryParams.collectTimee).endOf("day").format('YYYY-MM-DD HH:mm:ss') : null
      // this.$set(this.queryParams, 'collectTimee', endTime)
      // findDiseaseDataList(this.queryParams).then(res => {
      //   if (res.code == 200) {
      //     this.loading = false
      //     res.rows.forEach(item => {
      //       item.beginMileShow = formatPile(item.beginMile)
      //       item.endMileShow = formatPile(item.endMile)
      //     })
      //     this.tableData = res.rows
      //     this.total = res.total
      //   }
      // })


      getEventListByRecordId(this.dataId).then(response => {

        // this.eventList = response.data;
        this.tableData = response.data
        // this.total = response.total;
        this.loading = false;
      });
    },
    // 修改
    handleUpdate(e) {
      this.drawerTitle = '修改事件'
      this.rowData = e
      this.drawer = true
    },
    // 新增
    handleAdd() {
      this.drawerTitle = '新增事件'
      this.rowData = {}
      this.drawer = true
    },
    // 删除
    handleDelete(e) {
      this.$modal.confirm('是否确认删除').then(() => {
        deleteDiseaseData(e.id).then(res => {
          if (res.code == 200) {
            this.$modal.msgSuccess('删除成功')
            this.handleQuery()
          }
        })
      })
    },
    checkSelectable(row) {
      return row.disStatus == 1
    },
    handleBatchDelete() {
      if (this.selectIds.length > 0)
        this.$modal.confirm('是否确认删除所选的数据').then(() => {
          batchDeleteDiseaseData(this.selectIds).then(res => {
            this.handleQuery();
            this.$modal.msgSuccess("删除成功");
          })
        })
    },
    resetQuery() {
      this.queryParams = {
        pageNum: 1,
        pageSize: 50
      }
    },
    getDisType() {
      listAllDiseases().then(res => {
        this.disType = res.data.map(item => {
          return {
            dictLabel: item.diseaseName,
            dictValue: item.diseaseCode
          }
        })
      })
    },
    handleCloseDetail() {
      this.rowData = {}
      this.drawer = false
      this.handleQuery()
    },
    handleOpenFile(e, row) {
      this.disFilePath = ''
      this.openFile = true
      this.disFilePath = row.disFilePath
    },
    // 导出清单按钮
    exportList() {
      this.download(
        'manager/disease/export',
        {...this.queryParams},
        `disease_${new Date().getTime()}.xlsx`,
        {
          headers: { 'Content-Type': 'application/json;' },
          parameterType: 'body'
        }
      )
    },
    handleClickRow(e) {
      if(e.disStatus == 1) {
          this.clickRow = e
          e.isSelected = !e.isSelected;
          this.$refs.dataTable.toggleRowSelection(e);
      }
      this.rowData = e
    },
    handleSubmit(row, status) {
      if (status === 1) {
        this.$modal.confirm('是否将该事件作为被损工程提交到被损被盗项目中?').then(() => {
          row.mtype = status
          saveToTheft(row).then(res => {
            if (res.code == 200) {
              this.$modal.msgSuccess('提交成功')
              this.handleQuery()
            }
          })
        })
      } else {
        this.$modal.confirm('是否将该事件作为被盗工程提交到被损被盗项目中?').then(() => {
          row.mtype = status
          saveToTheft(row).then(res => {
            if (res.code == 200) {
              this.$modal.msgSuccess('提交成功')
              this.handleQuery()
            }
          })
        })
      }
    },
    openDisInfo(row) {
      if (!this.rowData.id) {
        this.$message.warning('请选择一条事件')
        return
      }
      this.dialogVisible = true
      this.disId = row.id
    }
  }
}
</script>

<style lang="scss" scoped>
.leftDiv {
  border-right: 1px solid #d8dce5;
  min-height: calc(100vh - 100px);
  overflow-y: auto;
  height: calc(80vh - 150px);
  position: relative;
  top: -20px;
  padding-top: 10px;
  background-color: white;
}

.leftIcon {
  border: 1px solid #dcdfe6;
  border-radius: 8px;
  width: 16px;
  height: 50px;
  line-height: 50px;
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
  position: absolute;
  right: 0;
  top: 300px;
  z-index: 2;
}

.leftIcon:hover {
  background-color: #dcdfe6;
}

.rightIcon {
  border: 1px solid #dcdfe6;
  border-radius: 8px;
  width: 16px;
  height: 50px;
  line-height: 50px;
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
  position: absolute;
  left: -10px;
  top: 280px;
  z-index: 10;
  background: white;
}

.rightIcon:hover {
  background-color: #dcdfe6;
}

.left-total {
  font-size: 13px;
  line-height: 28px;
  color: #606266;
  position: absolute;
  bottom: 10px;
  right: 10px;
}
</style>
