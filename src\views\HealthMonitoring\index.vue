<template>
  <div class="sub-page" ref="subPageRef">
    <section class="sub-page-main" :style="mainStyle">
      <div class="sub-page-l">
        <div class="header" ref="hRef">
          <div class="header-main" :class="isBig ? 'h-sub-bg' : 'h-bg'">
            <div class="current-time" :style="{ fontSize: isBig ? '56px' : '14px' }" v-if="!isBig">{{ time }}</div>
            <div class="cockpit-title" :class="isBig ? '' : 'set-family'"
              :style="{ justifyContent: isBig ? '' : 'center', marginLeft: isBig ? '30px' : '', fontSize: isBig ? '2.8vh' : '1.8vw' }">
              <span class="title-n"  :title="title">{{
                title }}</span>
              <slot></slot>
            </div>
          </div>
        </div>
        <div class="page-l">
          <component :is="componentL" :statisticsData="data" @mapClick="onMapClick"></component>
        </div>
      </div>
    </section>


    <TableInfo :show.sync="tableShow" :url="infoUrl" :method="method" :key="tableKey" :data-obj="dataObj"
      :healthMonitoring="healthMonitoring" :ifChart="false" :isBig="isBig" v-if="tableShow" />
    <!-- 详情弹窗 -->
    <div v-if="showDVisible" class="dialog-detail">
      <Dialog :title="title" :show.sync="showDVisible" top="0" :append="false" width="70%" :modal="true">
        <div class="dialog-content">
          <component :is="componentName" :form-data="formData" :id="formData.id" :oneMap="true" />
        </div>
      </Dialog>
    </div>
  </div>
</template>

<script>
import { isBigScreen } from './util/utils';
import HealthIndex from './components/health/index.vue';
import TableInfo from "@/components/table/tableInfo.vue";
import Dialog from "@/components/Dialog/index.vue";
import { mapState, mapMutations } from 'vuex';

import { getMenuSub } from '@/api/oneMap/menuSub';
import { getListPage } from '@/api/oneMap/menuSubField';
import { publicRequest } from '@/api/oneMap/tableInfo'

import Vue from "vue";
const list = require.context("@/views", true, /\.vue$/);

export default {
  name: 'subpage',
  provide() {
    return {
      sub: this,
      oneMap: true,
    };
  },
  components: {
    HealthIndex,
    TableInfo,
    Dialog,
  },
  props: {
    title: {
      type: String,
      default: '自然灾害风险监测预警一张图'
    },
    pageLId: {
      type: String,
      default: 'HealthIndex'
    },
    data: {
      type: [Object, Array],
      default: () => {
        return {}
      }
    },
  },
  data() {
    return {
      timer: null,
      time: '',
      isBig: isBigScreen(),
      height: window.innerHeight || window.screen.height || window.screen.availHeight || 1080,
      componentL: this.pageLId,
      tableShow: false,
      infoUrl: "/oneMap/layerData/getDataInfo",
      method: "post",
      tableKey: new Date().getTime(),
      dataObj: {},
      healthMonitoring: {},
      componentName: null,
      showDVisible: false,
      formData: {},
    }
  },
  computed: {
    ...mapState({
      legendList: (state) => state.map.legendList,
      menuShowType: (state) => state.map.menuShowType,
    }),
    mainStyle() {
      let style = {};
      if (this.isBig) {
        style = {
          height: '100vh',
          overflow: 'hidden',
        }
      } else {
        style = {
          height: '100vh',
          // overflowY: 'auto',
          flexDirection: 'column',
        }
      }
      return style;
    },
  },
  created() {
    // 获取当前是星期几
    const date = new Date();
    const week = ['星期日', '星期一', '星期二', '星期三', '星期四', '星期五', '星期六'];
    const dayOfWeek = week[date.getDay()];
    // 时时获取当前时间 年月日时分秒
    this.timer = setInterval(() => {
      let date = new Date();
      const year = date.getFullYear();
      const month = date.getMonth() + 1;
      const day = date.getDate();

      const hours = date.getHours();
      let minutes = date.getMinutes();
      let seconds = date.getSeconds();
      // 判断分 秒是否大于10
      if (minutes < 10) {
        minutes = '0' + minutes;
      }
      if (seconds < 10) {
        seconds = '0' + seconds;
      }
      this.time = `${year}年${month}月${day}日 ${dayOfWeek} ${hours}:${minutes}:${seconds}`;
    }, 1000);
  },
  watch: {
    showDVisible(val) {
      if (!val) {
        let { path } = this.$route;
        this.$router.push({ path });
      }
    },
  },
  methods: {
    ...mapMutations({
      setTableHeader: "map/setTableHeader",
    }),
    onLoad() {
      console.log('网页加载成功')
      this.$modal.closeLoading();
    },
    onError() {
      console.log('网页加载失败')
    },
    async getMapLayer(id) {
      return new Promise((resolve, reject) => {
        getMenuSub(id).then(res => {
          if (res.code == 200 && res.data) {
            resolve(res.data)
          } else {
            resolve(null)
          }
        }).catch(err => {
          reject(err)
        })
      });
    },
    // 获取显示 头部
    async getTableHeader(params) {
      return new Promise((resolve, reject) => {
        getListPage(params).then(res => {
          if (res.code == 200 && res.rows) {
            let data = res.rows.map(v => {
              v.name = v.columnNameZh || v.name
              v.col = v.columnName || v.col
              v.ifTableShow = 1;
              return v;
            })
            resolve(data)
          } else {
            resolve(null)
          }
        }).catch(err => {
          reject(err)
        });
      });
    },
    scrollToBottom() {
      this.$nextTick(() => {
        if (this.$refs.subPageRef) {
          const container = this.$refs.subPageRef;
          if (container.scrollHeight >= container.clientHeight) {
            container.scrollTo({
              top: container.scrollHeight,
              behavior: 'smooth'
            });
            container.scrollTop = container.scrollHeight;
            setTimeout(() => {
              this.$forceUpdate();
            }, 300);
          }
        }
      });
    },
    // 地图点击
    onMapClick(data) {
      if (!this.isBig) {
        // 移动到页面最下方？
        this.scrollToBottom()
      }
    },
    getInfo() {
      this.$modal.loading();
      return new Promise((resolve, reject) => {
        publicRequest(this.infoUrl, 'post', { ...this.dataObj }).then(res => {
          if (res.code === 200) {
            if (Object.prototype.toString.call(res.data) !== '[object Array]') {
              resolve(res.data || {})
            } else {
              resolve(res.data[0] || {})
            }
          }
        }).finally(() => {
          this.$modal.closeLoading();
        });
      })
    },
  },
  mounted() {
    window.$Bus.$on('aMapClick', async (data) => {
      console.log(data, '地图点击')
      if (data.sys_dept_id) return
      // this.$modal.loading();
      this.componentName = null;
      let componentUrl = data.path || null;
      if (componentUrl) {
        return;
        this.formData = {
          ...data,
        }
        let { path } = this.$route;
        let query = {
          id: data.id,
          bridgeName: data.name,
          whetherHealthMonitorSystemType: 2,
        };
        const array = componentUrl.split("/");
        const [thirdName] = [...array];
        const component = list(componentUrl).default;
        // 把组件加载到components中
        if (component) {
          Vue.component(thirdName, {
            name: thirdName,
            ...component,
          });
        }
        this.$router.push({
          path,
          query,
        });
        // 接收动态组件名称
        this.componentName = thirdName;
        this.showDVisible = true;
        return;
      }
      this.dataObj = {
        dataId: data.id,
        id: data?.layerId || data?.id,
      }
      let res = await this.getMapLayer(data?.layerId);
      let listQueryConfig = null;
      if (res) {
        listQueryConfig = res.listQueryConfig ? JSON.parse(res.listQueryConfig) : null;
      }
      if (listQueryConfig) {
        this.healthMonitoring = listQueryConfig.healthMonitoring;
      }
      let header = await this.getTableHeader({ pageNum: 1, pageSize: 100, menuSubId: data?.layerId })
      if (header) {
        this.setTableHeader({ header });
      }
      // let tableInfo = await this.getInfo()
      let serviceUrl2 = window.location.origin;
      let directUrl2 = serviceUrl2 + `/healthMonitoringRealTime/realTime?code=${data.nodeId}`
      localStorage.setItem('mapData', JSON.stringify(data));
      window.open(`${directUrl2}`, '_blank')
      /* setTimeout(() => {
        this.$modal.closeLoading();
      }, 300); */
      // this.tableShow = true;
    })
    this.$forceUpdate();
  },
  unmounted() {
    if (this.timer) {
      clearInterval(this.timer)
      this.timer = null;
    }
  },
  destroyed() {
    window.$Bus.$off('aMapClick')
  },
}
</script>

<style lang="scss" scoped>
@import "@/assets/styles/utils.scss";

.sub-page {
  width: calc(100% - 0px);
  height: 100%;
  overflow-x: hidden;
  overflow-y: auto;

  .sub-page-header {
    width: 100%;
    height: vwpx(156px);
    z-index: 9;
  }

  .sub-page-main {
    width: 100%;
    display: flex;

    .sub-page-l {
      flex: 1;
      background-image: url('~@/assets/cockpit/cockpit-bg.png');
      background-repeat: no-repeat;
      background-size: 100% 100%;
      // background-image: radial-gradient(ellipse at center, rgba(12,87,177, 1) 0%,rgba(12,87,177, 0.8) 10%, rgba(9, 25, 45, 0.8) 20%, rgba(9, 25, 45, 1) 100%);

      // background-color: rgba(9, 25, 45, 1);
      .page-l {
        width: 100%;
      }
    }

    .sub-page-r {
      flex: 1;
      // background-image: url('~@/assets/cockpit/cockpit-bg.png');
      // background-repeat: no-repeat;
      // background-size: 100% 100%;
      background-color: rgba(9, 25, 45, 1);

      .page-r {
        width: 100%;
      }
    }
  }

  .dialog-detail {
    ::v-deep .el-dialog {
      margin: 0 auto 0;
      background: rgba(4, 17, 48, 0.8);
      box-shadow: inset 0px 0px 10px 0px #3662ec;
      border-radius: 10px 10px 10px 10px;
      border: 1px solid #0687ff;
      color: #ffffff !important;

      .el-dialog__header {
        border-bottom: none;
        padding: 10px 15px !important;

        .el-dialog__title {
          color: #ffffff;
        }

        .el-dialog__headerbtn {
          color: #ffffff;
          top: 10px;
        }
      }

      .el-dialog__body {
        padding: 10px;
        color: #ffffff !important;

        .archives-left-body {
          top: vwpx(100px);
        }
      }
    }

    .dialog-content {
      min-height: 70vh;
      max-height: 80vh;
      overflow-y: auto;
      overflow-x: hidden;

      &::-webkit-scrollbar {
        width: vwpx(16px);
        background: rgba(2, 10, 30, 0.8);
      }

      &::-webkit-scrollbar-thumb {
        background: rgba(35, 134, 255, 0.3);
        border-radius: vwpx(8px);
      }

      &::-webkit-scrollbar-track {
        background: rgba(2, 10, 30, 0.8);
        border-radius: vwpx(8px);
      }
    }
  }
}

@import "@/assets/styles/utils.scss";

@font-face {
  font-family: 'YouSheBiaoTiHei';
  /* 自定义的字体名称 */
  src: url('~@/assets/home/<USER>') format('truetype');
  /* 字体文件路径和格式 */
  /* 可选属性，根据需要设置 */
  font-weight: normal;
  font-style: normal;
}

.header {
  height: vwpx(160px);
  top: 0;
  left: 0;
  right: 0;
  background-image: url('~@/assets/cockpit/cockpit-bg.png');
  background-repeat: no-repeat;
  background-size: cover;
  z-index: 2;
  // position: fixed;

  .h-bg {
    background-image: url('~@/assets/cockpit/header-bg.png');
    background-repeat: no-repeat;
    background-size: 100% 100%;
  }

  .h-sub-bg {
    // background-image: url('~@/assets/cockpit/top-bg.png');
    background-repeat: no-repeat;
    background-size: 100% 100%;
  }

  .year-selector {
    z-index: 99;
    height: 0vh;
    position: relative;
    top: -4vmin;
    float: right;
    right: 1.8518518519vmin;
    display: flex;
    align-items: center;

    ::v-deep .el-input {
      .el-input__inner {
        width: 9vw;
        height: vwpx(70px);
        background-color: rgba(1, 102, 254, 0.2);
        border: 1px solid #0166fe;
        color: #ffffff;
        font-size: vwpx(30px);
      }

      .el-input__inner::placeholder {
        color: #bbbbbb;
      }

      .el-input-group__append {
        background-color: rgba(1, 102, 254, 0.2);
        border: 1px solid #0166fe;
        color: #ffffff;
        border-left: none;
        padding: 0 10px;
        cursor: pointer;
      }
    }

    ::v-deep .select-popper {
      background-color: rgba(1, 102, 254, 0.2);
      border: 1px solid #0166fe;
      color: #ffffff !important;
      font-size: vwpx(30px);
      margin: vwpx(10px) 0;
    }

    ::v-deep .el-select-dropdown__item {
      color: #ffffff !important;
      font-size: vwpx(30px);
      margin: vwpx(15px) 0;
    }

    ::v-deep .el-select-dropdown__item.selected {
      color: #42abff !important;
    }
  }

  .header-main {
    height: 100%;
    width: 100%;
    position: relative;

    .current-time {
      position: absolute;
      left: vwpx(40px);
      top: 40%;
      transform: translateY(-50%);
      color: #fff;
    }

    .set-family {
      font-family: YouSheBiaoTiHei;
      background: linear-gradient(180deg, #FFFFFF 0%, #FFFFFF 60%, #20A9FF 100%);
      background-clip: text;
      -webkit-background-clip: text;
      color: transparent;
    }



    .cockpit-title {
      font-family: YouSheBiaoTiHei;
      font-weight: 400;
      min-width: vwpx(550px);
      letter-spacing: vwpx(8px);
      text-align: center;
      line-height: vwpx(160px);
      color: #fff;

      display: flex;
      align-items: center;
      justify-content: flex-start;

      .title-n {
        display: block;
        text-align: left;
        color: #fff;
        font-size: min(26px, 1.45vw);
        // 超出部分隐藏
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }

    }

    .b-font {
      // font-size: vwpx(96px);
      font-size: 1.3vw;
    }

    .n-font {
      // font-size: vwpx(70px);
      // font-size: clamp(1rem, 0.682rem + 1.59vw, 1.575rem);
      font-size: 1.8vw;
    }

    .btn {
      position: absolute;
      right: vwpx(10px);
      top: 50%;
      transform: translateY(-50%);
      cursor: pointer;

      display: flex;
      align-items: center;
    }
  }

  .ml-1 {
    margin-left: vwpx(10px);
  }

  .mt-1 {
    margin-top: vwpx(10px);
  }
}
</style>