<template>
  <div class="page-r" v-if="!cockpit.componentR">
    <!-- <div class="select-year">
      <el-select v-model="year" placeholder="请选择年份" popper-class="select-popper" @change="onChange"
        :popper-append-to-body="false">
        <el-option v-for="item in years" :key="item" :label="item + '年'" :value="item"></el-option>
      </el-select>

    </div> -->
    <el-row :gutter="isBig ? 30 : 10">
      <el-col :xs="8" :sm="6" :md="6" :lg="6" :xl="6">
        <CockpitCard title="养护工程" :class="isBig ? 'mb-3' : 'mb-2'" w="100%" :h="isBig ? '27.8vh' : '26vh'"
          :isDtl="false">
          <Engineering :year="year"></Engineering>
        </CockpitCard>
        <CockpitCard title="被损被盗" unit="个" :class="isBig ? 'mb-3' : 'mb-2'" w="100%" :h="isBig ? '27.8vh' : '26vh'"
          :isDtl="false">
          <Echarts :key="optionKey" :option="option" v-if="option" :height="isBig ? '26vh' : '25.6vh'" />
        </CockpitCard>
        <CockpitCard title="定期检查" unit="个" w="100%" :h="isBig ? '27.8vh' : '26vh'" :isDtl="false">
          <Echarts :key="dqOptionKey" :option="dqOption" v-if="dqOption" :height="isBig ? '26vh' : '26vh'" />
        </CockpitCard>
      </el-col>

      <el-col :xs="8" :sm="12" :md="12" :lg="12" :xl="12">
        <div style="display: flex;flex-direction: column;">
          <CockpitCard title="管理处年度统计" :class="isBig ? 'mb-3' : 'mb-2'" w="100%"
            :h="isBig ? 'calc(61vh)' : 'calc(57vh - 2px)'" :isDtl="false" hBg="l">
          <div>
            <div class="select-year" :style="isBig ? 'right: 0; top:-72px' : 'right: -1px; top:-30px'">
              <el-select v-model="year" placeholder="请选择年份" popper-class="select-popper" @change="onChange"
                :popper-append-to-body="false">
                <el-option v-for="item in years" :key="item" :label="item + '年'" :value="item"></el-option>
              </el-select>
            </div>
            <YearStatistics></YearStatistics>
          </div>
          </CockpitCard>

          <div style="display: flex;flex-shrink: 0;">
            <CockpitCard title="年度养护经费预算(万元)" w="100%" :h="isBig ? '27.8vh' : '26vh'" :isDtl="false">
              <Echarts :option="yOption" v-if="yOption" :height="isBig ? '27.5vh' : '26vh'" :key="yKey" />
            </CockpitCard>
            <CockpitCard title="日常养护事件" w="100%" :h="isBig ? '27.8vh' : '26vh'" class="ml-2" :isDtl="false">
              <Echarts :option="dOption" v-if="dOption" :height="isBig ? '27.5vh' : '26vh'" :key="dKey" />
            </CockpitCard>
          </div>
        </div>
      </el-col>

      <el-col :xs="8" :sm="6" :md="6" :lg="6" :xl="6">
        <CockpitCard title="今日巡查" :class="isBig ? 'mb-3' : 'mb-2'" w="100%" :h="isBig ? '27.8vh' : '26vh'"
          :isDtl="false">
          <Echarts :option="dpOption" v-if="dpOption" :height="isBig ? '27.2vh' : '26vh'" :key="dpKey" />
        </CockpitCard>
        <CockpitCard title="今日事件" :class="isBig ? 'mb-3' : 'mb-2'" w="100%" :h="isBig ? '27.8vh' : '26vh'"
          :isDtl="false">
          <Tables :columns="dpColumns" :data="dpData"></Tables>
        </CockpitCard>
        <CockpitCard title="今日检查" :class="isBig ? 'mb-3' : 'mb-2'" w="100%" :h="isBig ? '27.8vh' : '26vh'"
          :isDtl="false">
          <Tables :columns="diColumns" :data="diData"></Tables>
        </CockpitCard>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import * as echarts from "echarts";
import { calcFontSize, isBigScreen } from "../util/utils";
import CockpitCard from "./cockpitCard.vue";
import Engineering from "./maintenance/engineering.vue";
import Echarts from "./echarts/echarts.vue";
import Tables from "./tables.vue";
import YearStatistics from "./yearStatistics.vue";
import LonLat from "@/components/mapPosition/lonLat.vue";
import {
  getDomainCountInfo,
  getMonthCountInfo,
  getProjTypeSumFundInfo,
  getTheftCountInfo,
  getCheckCountInfo,
  getInspectionLogsStatistic,
  getPatrolDetail,
} from "@/api/cockpit/index";

export default {
  inject: ['cockpit'],
  components: {
    CockpitCard,
    Engineering,
    Echarts,
    Tables,
    YearStatistics,
    LonLat,
  },
  data() {
    return {
      isBig: isBigScreen(),
      isFullScreen: true,
      year: null,
      years: [],
      option: null,
      yOption: null, // 年度养护经费预算 图表信息
      dOption: null, // 日常养护事件 图表信息
      dpOption: null, // 今日巡查 图表信息
      dqOption: null, // 定期检查 图表信息
      dpColumns: [
        {
          label: "序号",
          prop: "index",
          width: 40,
        },
        {
          label: "管理处",
          prop: "name",
        },
        {
          label: "今日（个）",
          prop: "value",
          width: 60,
        },
        {
          label: "昨日（个）",
          prop: "percent",
          width: 60,
        },
      ], // 今日事件 表头信息
      dpData: [], // 今日事件 表格数据
      diColumns: [
        {
          label: "序号",
          prop: "index",
          width: 40,
        },
        {
          label: "管理处",
          prop: "name",
        },
        {
          label: "桥梁（座）",
          prop: "bridge",
          width: 70,
        },
        {
          label: "隧道（座）",
          prop: "tunnel",
          width: 70,
        },
        {
          label: "路线（条）",
          prop: "route",
          width: 70,
        },
      ], // 今日检查 表头信息
      diData: [], // 今日检查 表格数据
      vals: "", //
      lon: "102.44",
      lat: "23.12",
      optionKey: "optionKey",
      dqOptionKey: "dqOptionKey",
      yKey: "yKey",
      dKey: "dKey",
      dpKey: "dpKey",
    };
  },
  created() {
    this.getYear();
    this.getTheftCount();
    this.getCheckCount();
    this.initYOption();
    this.initDOption();
    this.initDpOption();
    this.initData();
  },
  methods: {
    initData() {
      getDomainCountInfo({ year: this.year }).then((res) => {
        let data = [];
        res.rows.forEach((item, index) => {
          data.push({
            index: index + 1,
            name: item.domainName,
            value: item.totalCount,
            percent: item.yesterdayCount,
          });
        });
        this.dpData = data;
      });
      getPatrolDetail({ year: this.year }).then((res) => {
        let data = [];
        res.data.forEach((item, index) => {
          data.push({
            index: index + 1,
            name: item.managementMaintenanceName,
            bridge: item.bridgeInspectedCount,
            tunnel: item.tunnelInspectedCount,
            route: item.routeInspectedCount,
          });
        });
        this.diData = data;
      });
    },
    initEcharts(
      xData = ["已完成", "进行中", "未开始"],
      data = [175, 124, 30],
      color = null
    ) {
      var iconData = data || [];
      color = color || [
        {
          leftColor: ["rgba(28,255,251,0.1)", "rgba(28,255,251,1)"],
          rightColor: ["rgba(28,255,251,0.06)", "rgba(0,255,251,0.6)"],
          topColor: ["#7DFFFD"],
        },
        {
          leftColor: ["rgba(0,157,255,0.1)", "rgba(0,157,255,1)"],
          rightColor: ["rgba(0,157,255,0.06)", "rgba(0,157,255,0.6)"],
          topColor: ["#4CBAFF"],
        },
        {
          leftColor: ["rgba(255,143,31,0.1)", "rgba(255,143,31,1)"],
          rightColor: ["rgba(255,143,31,0.06)", "rgba(255,204,152,0.6)"],
          topColor: ["#D9B376"],
        },
      ];
      let series = [];
      // for (let i = 0; i < iconData.length; i++) {
      //   series.push({
      //     type: 'bar',
      //     barWidth: 18,
      //     itemStyle: {
      //       normal: {
      //         color: new echarts.graphic.LinearGradient(
      //           0,
      //           0,
      //           0,
      //           1,
      //           [
      //             {
      //               offset: 0,
      //               color: color[i].leftColor[1] || '#00b0ff',
      //             },
      //             {
      //               offset: 0.8,
      //               color: color[i].leftColor[0] || '#7052f4',
      //             },
      //           ],
      //           false
      //         ),
      //       },
      //     },
      //     data: i === 0 ? [iconData[i], 0, 0] : i === 1 ? [0, iconData[i], 0] : [0, 0, iconData[i]],
      //   });
      // }
      let option = {
        backgroundColor: "rgba(0,0,0,0)",
        grid: {
          left: "3%",
          right: "4%",
          bottom: "3%",
          containLabel: true,
        },
        tooltip: {
          show: true,
        },
        xAxis: {
          data: xData,
          axisLine: {
            show: true,
            lineStyle: {
              color: "rgba(110,112,121,0.5)",
            },
          },
          axisTick: {
            show: false,
            length: 9,
            alignWithLabel: true,
            lineStyle: {
              color: "#7DFFFD",
            },
          },
          axisLabel: {
            fontSize: this.isBig ? 28 : calcFontSize(14),
            interval: 0,
            color: "#8B8C8C",
          },
          splitArea: {
            show: false,
          },
        },
        yAxis: {
          show: true,
          type: "value",
          name: "个",
          nameTextStyle: {
            color: "#999999",
            align: "right",
            fontSize: this.isBig ? 24 : calcFontSize(12),
          },
          axisLine: {
            show: false,
            lineStyle: {
              color: "white",
            },
          },
          splitLine: {
            show: true,
            lineStyle: {
              color: "rgba(110,112,121,0.5)",
            },
          },
          splitArea: {
            show: false,
          },
          axisTick: {
            show: false,
          },
          axisLabel: {
            fontSize: this.isBig ? 30 : calcFontSize(14),
            interval: 0,
            color: "#999999",
          },
          boundaryGap: ["20%", "20%"],
          min: 0,
        },
        series: [
          {
            name: "",
            type: "bar",
            barMaxWidth: 26,
            barGap: "10%",
            itemStyle: {
              normal: {
                color: (params) => {
                  let colors = [
                    new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                      {
                        offset: 0,
                        color:
                          color[0].leftColor[1] || "rgba(254, 174, 162, 1)",
                      },
                      {
                        offset: 1,
                        color:
                          color[0].leftColor[0] || "rgba(253, 114, 112, 1)",
                      },
                    ]),
                    new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                      {
                        offset: 0,
                        color:
                          color[1].leftColor[1] || "rgba(123, 200, 255, 1)",
                      },
                      {
                        offset: 1,
                        color: color[1].leftColor[0] || "rgba(53, 157, 245, 1)",
                      },
                    ]),
                    new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                      {
                        offset: 0,
                        color:
                          color[2].leftColor[1] || "rgba(251, 169, 128, 1)",
                      },
                      {
                        offset: 1,
                        color:
                          color[2].leftColor[0] || "rgba(247, 203, 107, 1)",
                      },
                    ]),
                  ];
                  return colors[params.dataIndex];
                },
              },
            },
            label: {
              normal: {
                show: true,
                position: "top",
                formatter: function (data) {
                  return "{a" + data.dataIndex + "|" + data.data + "}";
                },
                rich: {
                  a0: {
                    color: "#8B8C8C",
                    fontSize: this.isBig ? 24 : calcFontSize(16),
                    fontFamily: "DIN",
                  },
                  a1: {
                    color: "#8B8C8C",
                    fontSize: this.isBig ? 24 : calcFontSize(16),
                    fontFamily: "DIN",
                  },
                  a2: {
                    color: "#8B8C8C",
                    fontSize: this.isBig ? 24 : calcFontSize(16),
                    fontFamily: "DIN",
                  },
                },
              },
            },
            data,
          },
        ],
      };
      return option;
    },
    // 初始化 年度养护经费预算 图表信息
    initYOption() {
      var trafficWay = [
        // {
        // name: "被损被盗 6055 万元",
        // value: 6055,
        // },
        // {
        // name: "日常养护费用 51222 万元",
        // value: 51222,
        // },
        // {
        // name: "养护检测费用 4544 万元",
        // value: 4544,
        // },
        // {
        // name: "运营费用 15641 万元",
        // value: 15641,
        // },
        // {
        // name: "专项工程费用 66157 万元",
        // value: 66157,
        // },
      ];
      getProjTypeSumFundInfo({ year: this.year }).then((res) => {
        if (res.code === 200) {
          var total = 0;
          res.rows.forEach((item, index) => {
            total += item.sumFund;
            trafficWay.push({
              // name:
              //   item.type + " " + (item.sumFund / 10000).toFixed(2) + "万元",
              name: item.type,
              value: (item.sumFund / 10000).toFixed(2),
            });
          });
          let sum1 =
            res.rows.find((el) => el.type === "专项工程费用")?.sumFund || 0;
          let sum2 = res.rows.find(
            (el) => el.type === "专项养护实际费用"
          ).sumFund;
          // window.$Bus.$emit("pageL-ZXYH", sum1, sum2);
          total = trafficWay.reduce((sum, item) => sum + Number(item.value), 0);
          total = total * 10000;
          window.$Bus.$emit("pageL-ZXYH", 100885.49 * 10000, 1600.91 * 10000);
          var data = [];
          var color = [
            "#016DFF",
            "#00DFFF",
            "#FF5B00",
            "#FFDD21",
            "#A569FF",
            "#ff5b00",
            "#ff3000",
          ];
          for (var i = 0; i < trafficWay.length; i++) {
            data.push({
              value: trafficWay[i].value,
              name: trafficWay[i].name,
              itemStyle: {
                normal: {
                  borderWidth: 4,
                  shadowBlur: 2,
                  borderColor: color[i],
                  shadowColor: color[i],
                },
              },
            });
          }
          // 位置
          let center = this.isBig ? ["25%", "50%"] : ["25%", "50%"];
          // 是否全屏
          let isFull = this.$route.path?.includes('cockpit');
          var seriesOption = [
            {
              type: "pie",
              zlevel: 0,
              silent: true,
              radius: this.isBig ? ["71%", "74%"] : ["70%", "73%"],
              center,
              hoverAnimation: false,
              color: "rgba(0,62,122,0.9)",
              label: {
                normal: {
                  show: false,
                },
              },
              labelLine: {
                normal: {
                  show: false,
                },
              },
              data: [1],
            },
            {
              name: "",
              type: "pie",
              clockWise: false,
              radius: this.isBig ? ["60%", "65%"] : ["58%", "63%"],
              center,
              hoverAnimation: false,
              startAngle: "90",
              gap: 20,
              itemStyle: {
                normal: {
                  label: {
                    show: false,
                    position: "outside",
                    color: "#ddd",
                    fontSize: this.isBig ? 30 : calcFontSize(12),
                    formatter: function (params) {
                      var percent = 0;
                      if (!total) {
                        percent = 0;
                      } else {
                        percent = (
                          (Number(params.value) / (total / 10000).toFixed(2)) *
                          100
                        ).toFixed(2);
                      }
                      if (params.name !== "") {
                        return percent + "%";
                      } else {
                        return "";
                      }
                    },
                  },
                  labelLine: {
                    length: this.isBig ? 20 : 10,
                    length2: this.isBig ? 50 : 30,
                    show: false,
                    color: "#00ffff",
                  },
                },
              },
              data: data,
              animationType: "scale",
              animationEasing: "elasticOut",
              animationDelay: function (idx) {
                return idx * 50;
              },
            },
          ];
          this.yOption = {
            backgroundColor: "rgba(0,0,0,0)",
            color: color,
            title: {
              text: `{total|${(total / 10000).toFixed(2)}}`,
              subtext: "{a|年度养护经费\n}{b|预算}{c| (万元)}",
              top: this.isBig ? "36%" : "35%",
              textAlign: "center",
              left: this.isBig ? "24.5%" : "24%",
              textStyle: {
                color: "#fff",
                fontSize: this.isBig ? 38 : calcFontSize(18),
                fontWeight: "700",
                rich: {
                  total: {
                    color: "#fff",
                    fontSize: this.isBig ? 54 : calcFontSize(22),
                    fontWeight: "700",
                  },
                },
              },
              subtextStyle: {
                align: "center",
                color: "#fff",
                rich: {
                  a: {
                    color: "#fff",
                    fontSize: this.isBig ? 30 : calcFontSize(14),
                  },
                  b: {
                    color: "#fff",
                    fontSize: this.isBig ? 30 : calcFontSize(14),
                    padding: [10, 0, 0, 0],
                  },
                  c: {
                    color: "rgba(255,255,255,0.8)",
                    fontSize: this.isBig ? 26 : calcFontSize(12),
                    padding: [10, 0, 0, 0],
                  },
                },
              },
            },
            tooltip: {
              show: false,
            },
            legend: {
              icon: "circle",
              orient: "vertical",
              type: 'scroll',
              data: trafficWay.map((v) => v.name),
              right: "1%",
              y: "center",
              itemGap: this.isBig ? 25 : 6,
              itemWidth: this.isBig ? 20 : 10, // 设置宽度
              itemHeight: this.isBig ? 20 : 10, // 设置高度
              textStyle: {
                color: "#fff",
                fontSize: this.isBig ? 25 : 12,
                rich: {
                  title: {
                    color: "#fff",
                    fontSize: this.isBig ? 28 : 12
                  },
                  num: {
                    color: "#fff",
                    fontSize: this.isBig ? 28 : 12,
                  },
                  unit: {
                    color: "#fff",
                    fontSize: this.isBig ? 28 : 12,
                  },
                },
              },
              formatter: (name) => {
                let str = name;
                let arr = data.filter((v) => v.name == name);
                // // 如果name包含 '实际费用'则去除 实际费用
                // if (name.includes("实际费用")) {
                //   name = name.replace("实际费用", "");
                // }
                if (arr.length > 0) {
                  str = `{title|${name}：}{num|${arr[0].value}}`; //  {unit|万元}
                }
                return str;
              },
            },
            toolbox: {
              show: false,
            },
            series: seriesOption,
          };
          this.yKey = new Date().getTime();
        }
      });
    },
    // 初始化 日常养护事件 图表信息
    async initDOption() {
      let arr1 = [];
      const res = await getMonthCountInfo({ year: this.year, type: 1 });
      if (res.code === 200 && res.rows) {
        for (let i = 1; i <= 12; i++) {
          const typeStr = String(i);
          const found = res.rows.find((item) => item.type === typeStr);
          arr1.push({
            name: typeStr,
            value: found ? found.count : 0,
          });
        }
      }
      let arr2 = [];
      const r = await getMonthCountInfo({ year: this.year, type: 2 });
      if (r.code === 200 && r.rows) {
        for (let i = 1; i <= 12; i++) {
          const typeStr = String(i);
          const found = r.rows.find((item) => item.type === typeStr);
          arr2.push({
            name: typeStr,
            value: found ? found.count : 0,
          });
        }
      }
      var fontColor = "#ffffff";
      this.dOption = {
        backgroundColor: "rgba(0,0,0,0)",
        grid: {
          left: "4%",
          right: "5%",
          top: "15%",
          bottom: "3%",
          containLabel: true,
        },
        tooltip: {
          show: true,
          trigger: "axis",
        },
        legend: {
          show: true,
          x: "center",
          top: "2%",
          y: "35",
          icon: "circle",
          itemWidth: this.isBig ? 20 : 10,
          itemHeight: this.isBig ? 20 : 10,
          textStyle: {
            color: "#1bb4f6",
            fontSize: this.isBig ? 26 : calcFontSize(13),
          },
          data: ["下发", "完工"],
        },
        xAxis: [
          {
            type: "category",
            name: "",
            nameTextStyle: {
              color: "#999999",
              algin: "right",
              fontSize: this.isBig ? 30 : calcFontSize(14),
              padding: this.isBig ? [6, 0, 0, 10] : [8, 0, 0, 2],
              verticalAlign: "top",
            },
            boundaryGap: false,
            axisLabel: {
              color: fontColor,
              textStyle: {
                color: "#8B8C8C",
                fontSize: this.isBig ? 30 : calcFontSize(14),
              },
              formatter: function (data) {
                return data;
              },
            },
            axisLine: {
              show: false,
              lineStyle: {
                color: "#ffffff",
              },
            },
            axisTick: {
              show: false,
            },
            splitLine: {
              show: false,
              lineStyle: {
                color: "#ffffff",
              },
            },
            data: arr1.map((el) => el.name),
          },
        ],
        yAxis: [
          {
            name: "个",
            min: 0,
            nameTextStyle: {
              color: "#999999",
              align: "right",
              fontSize: this.isBig ? 30 : calcFontSize(12),
            },
            axisLabel: {
              formatter: "{value}",
              textStyle: {
                color: "#999999",
                fontSize: this.isBig ? 30 : calcFontSize(12),
              },
            },
            axisLine: {
              show: false,
              lineStyle: {
                color: "#999999",
              },
            },
            axisTick: {
              show: false,
            },
            splitLine: {
              show: true,
              lineStyle: {
                color: "rgba(153,153,153,0.3)",
              },
            },
            splitArea: {
              show: false,
            },
          },
        ],
        series: [
          {
            name: "下发",
            type: "line",
            symbol: "emptyCircle",
            symbolSize: 8,
            itemStyle: {
              normal: {
                color: "#1CFFBC",
                lineStyle: {
                  color: "#1CFFBC",
                  width: 1,
                },
                areaStyle: {
                  color: new echarts.graphic.LinearGradient(0, 1, 0, 0, [
                    {
                      offset: 0,
                      color: "rgba(28,255,188,0.3)",
                    },
                    {
                      offset: 1,
                      color: "rgba(28,255,188,0.9)",
                    },
                  ]),
                },
              },
            },
            data: arr1.map((el) => el.value),
          },
          {
            name: "完工",
            type: "line",
            symbol: "emptyCircle",
            symbolSize: 8,
            itemStyle: {
              normal: {
                color: "#0154FB",
                lineStyle: {
                  color: "#0154FB",
                  width: 1,
                },
                areaStyle: {
                  //color: '#94C9EC'
                  color: new echarts.graphic.LinearGradient(0, 1, 0, 0, [
                    {
                      offset: 0,
                      color: "rgba(1,84,251,0.3)",
                    },
                    {
                      offset: 1,
                      color: "rgba(1,84,251,0.9)",
                    },
                  ]),
                },
              },
            },
            data: arr2.map((el) => el.value),
          },
        ],
      };
      this.dKey = new Date().getTime();
    },
    // 初始化 今日巡查 图表信息
    async initDpOption() {
      const res = await getInspectionLogsStatistic({ year: this.year });
      if (res.code === 200) {
        const xData = [
          "保山",
          "大理",
          "昆明东",
          "昆明西",
          "丽江",
          "临沧",
          "普洱",
          "曲靖",
          "文山",
          "昭通",
          "红河",
          "西双版纳",
        ];

        const date = new Date();
        const dateStr = date.toISOString().split("T")[0];
        const todayData = Array(xData.length).fill(0);
        const yesterdayData = Array(xData.length).fill(0);
        res.data.map((el) => {
          el.maintenanceUnitName = el.maintenanceUnitName.replace("管理处", "");
          const index = xData.indexOf(el.maintenanceUnitName);
          if (el.date === dateStr) {
            todayData[index] = el.patrolMileage;
          } else {
            yesterdayData[index] = el.patrolMileage;
          }
        });
        this.dpOption = {
          backgroundColor: "rgba(0,0,0,0)",
          tooltip: {
            trigger: "axis",
            axisPointer: {
              type: "shadow",
            },
          },
          grid: {
            left: "2%",
            right: "2%",
            top: "15%",
            bottom: "5%",
            containLabel: true,
          },
          legend: {
            data: ["今日", "昨日"],
            icon: "rect",
            itemWidth: 10,
            itemHeight: 10,
            borderRadius: 10,
            textStyle: {
              fontSize: this.isBig ? 30 : calcFontSize(14),
              color: "#FFFFFF",
            },
          },
          xAxis: [
            {
              type: "category",
              name: "",
              nameTextStyle: {
                color: "#999999",
                algin: "right",
                fontSize: this.isBig ? 25 : calcFontSize(14),
                padding: [6, 0, 0, 0],
                verticalAlign: "top",
              },
              data: xData,
              axisPointer: {
                type: "shadow",
              },
              axisLine: {
                show: true,
                lineStyle: {
                  color: "rgba(153,153,153,0.3)",
                },
              },
              axisLabel: {
                rotate: 35,
                textStyle: {
                  color: "#8B8C8C", //更改坐标轴文字颜色
                  fontSize: this.isBig ? 26 : calcFontSize(14), //更改坐标轴文字大小
                },
              },
            },
          ],
          yAxis: [
            {
              type: "value",
              name: "(km)",
              min: 0,
              nameTextStyle: {
                color: "#999999",
                align: "right",
                fontSize: this.isBig ? 24 : calcFontSize(12),
              },
              axisLabel: {
                textStyle: {
                  color: "#999999", //更改坐标轴文字颜色
                  fontSize: this.isBig ? 25 : calcFontSize(12), //更改坐标轴文字大小
                },
              },
              axisLine: {
                show: true,
                lineStyle: {
                  color: "rgba(153,153,153,0.3)",
                },
              },
              splitLine: {
                show: true,
                lineStyle: {
                  color: "rgba(153,153,153,0.3)",
                },
              },
              splitArea: {
                show: false,
              },
            },
          ],
          series: [
            {
              name: "今日",
              type: "bar",
              itemStyle: {
                opacity: 1, // 这个是 透明度
                color: new echarts.graphic.LinearGradient(
                  0,
                  1,
                  0,
                  0,
                  [
                    {
                      offset: 0,
                      color: "rgba(33,155,255, 0)", // 0% 处的颜色
                    },
                    {
                      offset: 1,
                      color: "rgba(33,155,255, 1)", // 100% 处的颜色
                    },
                  ],
                  false
                ),
              },
              // 实现数字展示在柱状图
              label: {
                show: false,
                position: "top",
                fontSize: this.isBig ? 24 : calcFontSize(12),
                color: "#F5F5F5",
                offset: [0, -10],
                formatter: "{c}", //添加单位
              },
              data: todayData,
            },
            {
              name: "昨日",
              type: "bar",
              itemStyle: {
                // lenged文本
                opacity: 1, // 这个是 透明度
                color: new echarts.graphic.LinearGradient(
                  0,
                  1,
                  0,
                  0,
                  [
                    {
                      offset: 0,
                      color: "rgba(118,255,49, 0)", // 0% 处的颜色
                    },
                    {
                      offset: 1,
                      color: "rgba(118,255,49, 1)", // 100% 处的颜色
                    },
                  ],
                  false
                ),
              },
              label: {
                show: false,
                position: "top",
                fontSize: this.isBig ? 24 : calcFontSize(12),
                color: "#F5F5F5",
                offset: [0, -10],
                formatter: "{c}", //添加单位
              },
              data: yesterdayData,
            },
          ],
        };
        this.dpKey = new Date().getTime();
      }
    },
    onChange(e) {
      window.$Bus.$emit("onChangeYear", e);
      this.getTheftCount(); // 被损被盗
      this.getCheckCount(); // 定期检查
      this.initYOption(); // 年度养护经费预算
      this.initDOption(); // 日常养护事件
      this.initData(); // 今日事件、今日检查
      this.initDpOption(); // 今日巡查
    },
    getYear() {
      let date = new Date();
      let year = date.getFullYear();
      this.year = year;
      for (let i = year - 10; i < year + 1; i++) {
        this.years.push(i);
      }
    },
    async getTheftCount() {
      const res = await getTheftCountInfo({ year: this.year });
      if (res.code === 200 && res.rows) {
        this.option = this.initEcharts(
          res.rows.map((item) => item.type),
          res.rows.map((item) => item.count)
        );
      }
      this.optionKey = new Date().getTime();
    },
    async getCheckCount() {
      let color = [
        {
          leftColor: ["rgba(255,143,31,0.1)", "rgba(255,143,31,1)"],
          rightColor: ["rgba(255,143,31,0.06)", "rgba(255,204,152,0.6)"],
          topColor: ["#D9B376"],
        },
        {
          leftColor: ["rgba(0,157,255,0.1)", "rgba(0,157,255,1)"],
          rightColor: ["rgba(0,157,255,0.06)", "rgba(0,157,255,0.6)"],
          topColor: ["#4CBAFF"],
        },
        {
          leftColor: ["rgba(28,255,251,0.1)", "rgba(28,255,251,1)"],
          rightColor: ["rgba(28,255,251,0.06)", "rgba(0,255,251,0.6)"],
          topColor: ["#7DFFFD"],
        },
      ];
      const res = await getCheckCountInfo({ year: this.year });
      if (res.code === 200 && res.rows) {
        this.dqOption = this.initEcharts(
          res.rows.map((item) => item.type),
          res.rows.map((item) => item.count),
          color
        );
      }
      this.dqOptionKey = new Date().getTime();
    },
  },
};
</script>

<style lang="scss" scoped>
@import "@/assets/styles/utils.scss";

.page-r {
  width: 100%;
  min-width: vwpx(2000px);
  overflow: hidden;

  .select-year {
    // width: 100%;
    // height: 4vh;
    // margin-bottom: 5px;

    // display: flex;
    // align-items: center;
    // justify-content: flex-end;

    position: absolute;
    right: 0;
    top: -76px;

    ::v-deep .el-input {
      .el-input__inner {
        height: vwpx(300px);
        height: vwpx(60px);
        background-color: rgba(1, 102, 254, 0.2);
        border: 1px solid #0166fe;
        color: #ffffff;
        font-size: vwpx(30px);
      }

      .el-input__inner::placeholder {
        color: #bbbbbb;
      }

      .el-input-group__append {
        background-color: rgba(1, 102, 254, 0.2);
        border: 1px solid #0166fe;
        color: #ffffff;
        border-left: none;
        padding: 0 10px;
        cursor: pointer;
      }
    }

    ::v-deep .select-popper {
      background-color: rgba(1, 102, 254, 0.2);
      border: 1px solid #0166fe;
      color: #ffffff !important;
      font-size: vwpx(30px);
      margin: vwpx(10px) 0;
      // 添加以下样式
      position: absolute !important; // 确保使用绝对定位
      z-index: 2000; // 确保下拉框在其他元素之上
      top: 3vh !important;

      .popper__arrow {
        position: absolute;
        top: -0.7vh;
      }
    }

    ::v-deep .el-select-dropdown__item {
      color: #ffffff !important;
      font-size: vwpx(30px);
      margin: vwpx(15px) 0;

      &:hover {
        background-color: rgba(1, 102, 254, 0.2);
      }

      &.is-focus {
        background-color: rgba(1, 102, 254, 0.2);
      }
    }

    ::v-deep .el-select-dropdown__item.selected {
      color: #42abff !important;
    }
  }

  .mb-2 {
    margin-bottom: vwpx(20px);
  }

  .mb-3 {
    margin-bottom: vwpx(32px);
  }

  .ml-2 {
    margin-left: vwpx(20px);
  }

  .ml-4 {
    margin-left: vwpx(40px);
  }

  .mt-3 {
    margin-top: vwpx(30px);
  }

  .px-2 {
    padding: 0 vwpx(20px);
  }
}
</style>
