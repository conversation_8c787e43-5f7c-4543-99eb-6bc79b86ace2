<template>
  <div class="app-container">
    <el-row :gutter="20">
      <!--筛选区开始-->
      <el-col :span="24" :xs="24">
        <el-row>
          <el-col :span="24">
            <el-form
              ref="queryForm"
              :inline="true"
              :model="queryParams"
              label-width="68px"
              size="small"
            >
              <el-form-item label="" prop="year">
                <el-date-picker
                  v-model="queryParams.year"
                  placeholder="年份"
                  style="width: 240px"
                  type="year"
                  value-format="yyyy"
                >
                </el-date-picker>
              </el-form-item>
              <el-form-item>
                <selectTree
                  :key="'domainId'"
                  v-model="queryParams.domainId"
                  :deptType="100"
                  :deptTypeList="[1, 3, 4]"
                  clearable
                  filterable
                  placeholder="管养单位"
                  style="width: 240px"
                />
              </el-form-item>
              <el-form-item>
                <selectTree
                  :key="'constructionUnit'"
                  v-model="queryParams.checkDomainId" :data-rule="false"
                  clearable
                  filterable
                  :dept-type="100"
                  :filter-keys="['云南省交通投资建设集团有限公司', '云南交投投资有限公司']"
                  :expand-all="false"
                  placeholder="施工单位"
                  style="width: 240px"
                />
              </el-form-item>
              <el-form-item>
                <RoadSection
                  v-model="queryParams.maiSecId"
                  :deptId="queryParams.domainId"
                  placeholder="路段"
                  style="width: 240px"
                />
              </el-form-item>
              <el-form-item>
                <el-input
                  v-model="queryParams.projName"
                  placeholder="项目名称"
                  style="width: 240px"
                ></el-input>
              </el-form-item>
              <el-form-item>
                <el-input
                  v-model="queryParams.projCode"
                  placeholder="项目编码"
                  style="width: 240px"
                ></el-input>
              </el-form-item>
              <el-form-item>
                <el-input
                  v-model="queryParams.name"
                  placeholder="任务单名称"
                  style="width: 240px"
                ></el-input>
              </el-form-item>
              <el-form-item>
                <el-input
                  v-model="queryParams.code"
                  placeholder="任务单编码"
                  style="width: 240px"
                ></el-input>
              </el-form-item>
              <el-form-item>
                <dict-select
                  type="overdue_remind_status"
                  v-model="queryParams.isOverdue"
                  placeholder="超期提醒"
                  style="width: 240px"
                >
                </dict-select>
              </el-form-item>
              <el-form-item>
                <dict-select
                  type="test_task_type"
                  v-model="queryParams.status"
                  placeholder="状态"
                  style="width: 240px"
                >
                </dict-select>
              </el-form-item>
              <el-form-item>
                <el-date-picker
                  v-model="queryParams.issueDate"
                  type="daterange"
                  range-separator="至"
                  start-placeholder="发出起始时间"
                  end-placeholder="发出截止时间"
                  style="width: 240px"
                >
                </el-date-picker>
              </el-form-item>
              <el-form-item label="" prop="operator">
                <el-cascader
                  v-model="queryParams.operator"
                  :options="deptUserOptions"
                  :props="{
                    multiple: false, //是否多选
                    value: 'id',
                    emitPath: false,
                  }"
                  :show-all-levels="false"
                  ref="deptUser"
                  placeholder="通知单拟定人" clearable style="width: 240px"
                  filterable clearable></el-cascader>
              </el-form-item>
              <el-form-item>
                <el-button
                  icon="el-icon-search"
                  size="mini"
                  type="primary"
                  @click="handleQuery"
                  >搜索</el-button
                >
                <el-button
                  icon="el-icon-refresh"
                  size="mini"
                  @click="resetQuery"
                  >重置</el-button
                >
              </el-form-item>
            </el-form>
          </el-col>
        </el-row>
        <!--筛选区结束-->

        <!--操作按钮区开始-->
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button
              icon="el-icon-view"
              size="mini"
              type="warning"
              @click="openOperateDialog"
              >审核意见
            </el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button
              icon="el-icon-zoom-in"
              size="mini"
              type="primary"
              v-has-menu-permi="['check:construction:preview']"
              @click="handlePreview"
              >任务单预览
            </el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button
              type="warning"
              icon="el-icon-view"
              size="mini"
              v-has-permi="['settlement:repository:regenerate']"
              @click="handleRegenerate"
            >重新生成报表
            </el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button
              icon="el-icon-download"
              size="mini"
              type="success"
              v-has-menu-permi="['check:construction:export']"
              @click="exportList"
              >导出
            </el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button
              icon="el-icon-postcard"
              size="mini"
              type="primary"
              @click="openTaskInfoHandle"
              >任务单信息
            </el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button
              icon="el-icon-postcard"
              size="mini"
              type="warning"
              @click="handleExportLedger"
            >施工单台账
            </el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button
              type="danger"
              icon="el-icon-refresh-left"
              size="mini"
              v-has-permi="['check:construction:withdraw']"
              @click="handleWithdraw"
            >撤回
            </el-button>
          </el-col>
          <right-toolbar
            :columns="columns"
            :showSearch.sync="showSearch"
            @queryTable="handleQuery"
          ></right-toolbar>
        </el-row>
        <!--操作按钮区结束-->
        <!--数据表格开始-->
        <div class="tableDiv">
          <el-table v-adjust-table
            ref="dataTable"
            v-loading="loading"
            :data="tableData"
            :height="showSearch ? 'calc(100vh - 330px)' : 'calc(100vh - 270px)'"
            border
            highlight-current-row
            row-key="id"
            size="mini"
            stripe
            style="width: 100%"
            @expand-change="tableExpand"
            @row-click="handleClickRow"
            @selection-change="handleSelectionChange"
          >
            <el-table-column type="selection" width="50" align="center"/>
            <el-table-column type="expand">
              <template slot-scope="props">
                <el-table v-adjust-table
                  :data="props.row.constructionDetailList"
                  style="width: 100%"
                >
                  <el-table-column prop="" align="center" label="">
                  </el-table-column>
                  <el-table-column
                    prop="schemeCode"
                    align="center"
                    label="子目号"
                  >
                  </el-table-column>
                  <el-table-column
                    prop="schemeName"
                    align="center"
                    label="养护方法"
                  >
                  </el-table-column>
                  <el-table-column
                    prop="calcDesc"
                    align="center"
                    label="计算式"
                  >
                  </el-table-column>
                  <el-table-column prop="num" align="center" label="方法数量">
                  </el-table-column>
                  <el-table-column prop="unit" align="center" label="方法单位">
                  </el-table-column>
                  <el-table-column prop="price" align="center" label="单价">
                  </el-table-column>
                  <el-table-column prop="amount" align="center" label="金额">
                  </el-table-column>
                  <el-table-column prop="remark" align="center" label="备注">
                  </el-table-column>
                </el-table>
                <pagination
                  v-show="props.row.totalNum > 0"
                  :limit.sync="props.row.queryData.pageSize"
                  :page.sync="props.row.queryData.pageNum"
                  :total="props.row.totalNum"
                  @pagination="getRowDetailList(props.row)"
                />
              </template>
            </el-table-column>
            <el-table-column
              align="center"
              label="序号"
              type="index"
              width="50"
            />
            <template v-for="(column, index) in columns">
              <el-table-column
                v-if="column.visible"
                :label="column.label"
                :prop="column.field"
                :width="column.width"
                align="center"
                show-overflow-tooltip
              >
                <template slot-scope="scope">
                  <dict-tag
                    v-if="column.dict"
                    :options="dict.type[column.dict]"
                    :value="scope.row[column.field]"
                  />
                  <template v-else-if="column.slots">
                    <RenderDom
                      :index="index"
                      :render="column.render"
                      :row="scope.row"
                    />
                  </template>
                  <span v-else-if="column.isTime">{{
                    parseTime(scope.row[column.field], "{y}-{m}-{d}")
                  }}</span>
                  <span v-else>{{ scope.row[column.field] }}</span>
                </template>
              </el-table-column>
            </template>
            <el-table-column
              align="center"
              class-name="small-padding fixed-width"
              fixed="right"
              label="操作"
              width="250"
            >
              <template slot-scope="scope">
                <el-button
                    icon="el-icon-view"
                    size="mini"
                    type="text"
                    v-has-menu-permi="['check:construction:edit']"
                    @click="handleDetail(scope.row)"
                    >编辑
                </el-button>
                <el-button
                  icon="el-icon-view"
                  size="mini"
                  type="text"
                  @click="openFileModel(scope.row)"
                  >附件查看
                </el-button>
              </template>
            </el-table-column>
          </el-table>
          <pagination
            v-show="total > 0"
            :limit.sync="queryParams.pageSize"
            :page.sync="queryParams.pageNum"
            :total="total"
            @pagination="handleQuery"
          />
        </div>
        <!--数据表格结束-->
      </el-col>
    </el-row>
    <el-dialog
      title="操作意见"
      :visible.sync="openOperateInfo"
      size="50%"
      destroy-on-close
    >
      <operateInfo
        :businessKey="row.id"
        :getNodeInfo="getCurNodeInfo"
      ></operateInfo>
    </el-dialog>
    <el-dialog
      title="任务单信息"
      :visible.sync="openTaskInfo"
      width="80%"
      destroy-on-close
    >
      <task-info :row="row" @openFileModel="openFileModel"></task-info>
    </el-dialog>
    <el-drawer
      :wrapperClosable="false"
      title="编辑任务单"
      :visible.sync="openDetail"
      size="70%"
      destroy-on-close
    >
      <task-detail
        :status="1"
        :read-only="false"
        :row="row"
        @close="closeDetail"
      ></task-detail>
    </el-drawer>
    <IFramePreview ref="iframeRef" :srcdoc="preview.html" :down-url="preview.url" :file-name="preview.fileName"></IFramePreview>

    <el-dialog
      title="附件列表"
      destroy-on-close
      :visible.sync="openFile"
      width="500px"
    >
      <file-upload
        v-if="row.fileId"
        v-model="row.fileId"
        :owner-id="row.fileId"
        for-view
      ></file-upload>
    </el-dialog>
  </div>
</template>

<script>
import Tables from "@/views/patrol/frequencySettings/tables.vue";
import RoadSection from "@/views/baseData/components/roadSection/index.vue";
import selectTree from "@/components/DeptTmpl/selectTree.vue";
import TaskDetail from "@/views/regularTesting/taskManage/component/taskDetail.vue";
import operateInfo from "@/views/dailyMaintenance/component/operateInfo.vue";
import TaskInfo from "@/views/regularTesting/taskManage/component/taskInfo.vue";
import {
  viewConstructionList,
  getConstructionDetailList,
  getConstructionNodeInfo,
  getConstructionFileList,
  getPreviewInfo,
  getConstruction, withdraw
} from "@/api/regularTesting/taskManage/taskList";
import IFramePreview from "@/components/IFramePreview/index.vue";
import {regenerateReport} from "@/api/dailyMaintenance/metering/addPrice";
import {getTreeStruct} from "@/api/tmpl";

export default {
  name: 'InspectionTask',
  components: {
    TaskInfo,
    operateInfo,
    TaskDetail,
    selectTree,
    RoadSection,
    Tables,
    IFramePreview,
  },
  dicts: [
    "test_task_type",
    "task_type",
    "project_type",
    "affiliation_project_type",
  ],
  data() {
    return {
      showSearch: false,
      queryParams: {
        pageNum: 1,
        pageSize: 50,
      },
      total: 0,
      loading: false,
      status: "",
      columns: [
        {
          key: 1,
          width: 100,
          field: "overdueDesc",
          label: `超期提醒`,
          visible: true,
        },
        {
          key: 2,
          width: 100,
          field: "status",
          label: `状态`,
          visible: true,
          dict: "test_task_type",
        },
        {
          key: 3,
          width: 100,
          field: "projName",
          label: `项目名称`,
          visible: true,
        },
        {
          key: 4,
          width: 100,
          field: "projCode",
          label: `项目编码`,
          visible: true,
        },
        {
          key: 6,
          width: 100,
          field: "name",
          label: `任务单名称`,
          visible: true,
        },
        {
          key: 7,
          width: 100,
          field: "code",
          label: `任务单编号`,
          visible: true,
        },
        {
          key: 8,
          width: 100,
          field: "maiSecName",
          label: `路段名称`,
          visible: true,
        },
        {
          key: 9,
          width: 100,
          field: "domainName",
          label: `管养单位`,
          visible: true,
        },
        {
          key: 10,
          width: 100,
          field: "checkDomainName",
          label: `检测单位`,
          visible: true,
        },
        {
          key: 11,
          width: 100,
          field: "checkConName",
          label: `检测合同`,
          visible: true,
        },
        {
          key: 12,
          width: 100,
          field: "issueDate",
          label: `发出时间`,
          visible: true,
        },
        {
          key: 18,
          width: 100,
          field: "content",
          label: `工作内容`,
          visible: true,
        },
        {
          key: 22,
          width: 120,
          field: "beginDate",
          label: `计划开始时间`,
          visible: true,
        },
        {
          key: 23,
          width: 120,
          field: "endDate",
          label: `计划完成时间`,
          visible: true,
        },
        {
          key: 25,
          width: 100,
          field: "receivePerson",
          label: `接收人`,
          visible: true,
        },
      ],
      tableData: [],
      openOperateInfo: false,
      openTaskInfo: false,
      openFile: false,
      row: {},
      preview: {
        html: '',
        url: '',
        fileName: ''
      },
      openDetail: false,
      ids: [],
      deptUserOptions: []
    };
  },
  mounted() {
    this.getDeptTreeDef()
    if (this.$route.query.projName) {
      this.queryParams.projName = this.$route.query.projName;
    }
    this.clearQueryParam()
    this.handleQuery();
  },
  methods: {
    /** 查询部门-用户下拉树结构 */
    getDeptTreeDef() {
      getTreeStruct({types:111}).then(response => {
        this.deptUserOptions = response.data;
      });
    },
    clearQueryParam() {
      const newUrl = window.location.href.replace(/\?.*$/, "");
      window.history.replaceState(null, '', newUrl);
    },
    handleQuery() {
      if (
        this.queryParams.issueDate &&
        this.queryParams.issueDate.length == 2
      ) {
        this.queryParams.issueDate.startIssueDate =
          this.queryParams.issueDate[0];
        this.queryParams.issueDate.endIssueDate = this.queryParams.issueDate[1];
      }
      this.loading = true;
      viewConstructionList(this.queryParams).then((res) => {
        this.tableData = res.rows;
        this.total = res.total;
        this.loading = false;
        this.tableData.forEach((item) => {
          item.queryData = {
            projConId: item.id,
            pageNum: 1,
            pageSize: 10,
          };
          item.totalNum = 0;
        });
      });
    },
    handleClickRow(e) {
      this.row = e;
    },
    getCurNodeInfo(data) {
      return getConstructionNodeInfo(data);
    },
    openOperateDialog() {
      if (!this.row.id) {
        this.$message.warning("请先选择一条记录！");
        return;
      }
      this.openOperateInfo = true;
    },
    async openFileModel(row) {
      this.row = row;
      // 查询附件
      await getConstructionFileList({ inId: row.id }).then(async (res) => {
        if (res.data) {
          this.row.fileId = res.data.fileId;
        }
        this.openFile = true;
      });
    },
    openTaskInfoHandle() {
      if (!this.row.id) {
        this.$message.warning("请先选择一条记录！");
        return;
      }
      getConstruction(this.row.id).then(res=> {
        this.row = res.data
        this.openTaskInfo = true;
      })
    },
    resetQuery() {
      this.queryParams = {
        pageNum: 1,
        pageSize: 50,
      };
      this.handleQuery()
    },
    // 报表预览
    handlePreview() {
      if (!this.row.id) {
        this.$message.warning("请先选择一条记录！");
        return;
      }
      getPreviewInfo({id: this.row.id}).then(res=> {
        if (res.code == 200){
          this.preview.html = res.data.html
          this.preview.url = res.data.downUrl
          this.preview.fileName = res.data.fileName
          this.$refs.iframeRef.visible = true
        }
      })
    },
    handleDetail(row) {
      this.row = row;
      this.openDetail = true;
    },
    closeDetail() {
      this.openDetail = false;
      this.handleQuery();
    },
    // 导出清单按钮
    exportList() {
      if (
        this.queryParams.issueDate &&
        this.queryParams.issueDate.length == 2
      ) {
        this.queryParams.issueDate.startIssueDate =
          this.queryParams.issueDate[0];
        this.queryParams.issueDate.endIssueDate = this.queryParams.issueDate[1];
      }
      this.download(
        "manager/check/construction/export",
        { ...this.queryParams },
        `checksTask_${new Date().getTime()}.xlsx`,
        {
          headers: { "Content-Type": "application/json;" },
          parameterType: "body",
        }
      );
    },
    handleWithdraw() {
      if (!this.row.id) {
        this.$message.warning('请先选择一条记录！')
        return
      }
      this.$confirm('确定撤回吗？', '警告', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        const params = {
          businessKey: this.row.id
        }
        withdraw(params).then(res => {
          this.$message.success('撤回成功')
          this.handleQuery()
        })
      })
    },
    handleExportLedger() {
      if (
        this.queryParams.issueDate &&
        this.queryParams.issueDate.length == 2
      ) {
        this.queryParams.issueDate.startIssueDate =
          this.queryParams.issueDate[0];
        this.queryParams.issueDate.endIssueDate = this.queryParams.issueDate[1];
      }
      this.download(
        "manager/check/construction/exportLedger",
        { ...this.queryParams },
        `checksTask_${new Date().getTime()}.xlsx`,
        {
          headers: { "Content-Type": "application/json;" },
          parameterType: "body",
        }
      );
    },
    tableExpand(row, expandedRows) {
      const isExpand = expandedRows.some((item) => row.id === item.id);
      if (isExpand) {
        this.getRowDetailList(row);
      }
    },
    getRowDetailList(row) {
      getConstructionDetailList(row.queryData).then((res) => {
        this.$set(row, "totalNum", res.total || 0);
        if (res.rows) {
          this.$set(row, "constructionDetailList", res.rows);
        }
      });
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection
    },
    handleRegenerate() {
      if (this.ids.length == 0) {
        this.$modal.msgError("请勾选至少一条数据")
        return
      }
      const params = {
        idList: this.ids.map(item => item.id),
        type: 5
      }
      regenerateReport(params).then(res => {
        this.$modal.msgSuccess("操作成功")
      })
    },
  },
};
</script>

<style lang="scss" scoped></style>
<style lang="scss" scoped>
@import "@/assets/styles/business.scss";
</style>


