<template>
  <div class="left-menu" :style="leftStyle">
    <!-- v-show是为了暂时把地图打印功能去掉 -->
    <div v-for="(item, index) in menuList" :key="index" class="menu-list" @click="handleMenuClick(item, index)"
      v-show="index != 12">
      <img :src="item.icon" alt="" :style="{ border: item.act ? '1px dashed #dddddd' : '' }" />
      <span>{{ item.name }}</span>
    </div>

    <!-- 图层设置 -->
    <section class="layer-setting" :class="isBig ? 'big-setting' : ''" v-if="layerShow">
      <div v-for="(item, index) in layerSetting" :key="'lay' + index">
        <el-checkbox v-model="item.ifShow" :label="item.name || item.layerName"
          @change="handleLayerChange(item, $event)" />
        <el-slider v-model="item.opacity" @change="sliderChange(item, $event)" />
      </div>
    </section>
    <div class="setting-close" v-if="layerShow">
      <i class="el-icon-close close" @click="handleClose"></i>
    </div>
  </div>
</template>

<script>
import { mapState, mapActions, mapMutations } from 'vuex';
import { getShapeList } from "@/api/oneMap/deptInfo"
// openlayer - Api
import 'ol/ol.css';
import { WKT } from 'ol/format';
import { fromLonLat } from 'ol/proj';
import { Point } from 'ol/geom';

import { removeAllLayer, addMapMask, addWidthFeature, removeLayer, onMeasure, getLineConfig, handleRectangle } from '../common/mapFun'
import cache from "@/plugins/cache";
import cursorIcon from '@/assets/map/cursor.png'
import { isBigScreen } from '../common/util';

const defCenter = fromLonLat([102.72378400000002, 25.013740000000027]);
const wktFormat = new WKT();

export default {
  inject: ['instance'],
  data() {
    return {
      menuList: [
        {
          name: '',
          icon: require('@/assets/map/toggle.png'),
          value: 1,
        },
        {
          name: '图层设置',
          icon: require('@/assets/map/layer-set.png'),
          value: 2,
          act: false,
        },
        {
          name: '返回',
          icon: require('@/assets/map/back.png'),
          value: 3,
        },
        {
          name: '测距',
          icon: require('@/assets/map/ranging.png'),
          value: 4,
          act: false,
        },
        {
          name: '测面',
          icon: require('@/assets/map/surface.png'),
          value: 5,
          act: false,
        },
        // {
        //   name: '绘面',
        //   icon: require('@/assets/map/omei.png'),
        //   value: 6,
        //   act: false,
        // },
        {
          name: '完成',
          icon: require('@/assets/map/complete.png'),
          value: 7,
        },
        {
          name: '清空',
          icon: require('@/assets/map/clear.png'),
          value: 8,
        },
        {
          name: '图例',
          icon: require('@/assets/map/legend.png'),
          value: 9,
          act: false,
        },
        {
          name: '全景',
          icon: require('@/assets/map/panorama.png'),
          value: 10,
          act: false,
        },
        {
          name: '点选',
          icon: require('@/assets/map/tap.png'),
          value: 11,
          act: false,
        },
        {
          name: '上一次',
          icon: require('@/assets/map/previous.png'),
          value: 12,
        },
        {
          name: '下一次',
          icon: require('@/assets/map/next.png'),
          value: 13,
        },
        {
          name: '打印',
          icon: require('@/assets/map/print.png'),
          value: 14,
          act: false,
        },
      ],
      layerShow: false,
      loading: false,
      isBig: false,
      sizeChange: false,
      routeType: ''
    }
  },
  computed: {
    ...mapState({
      treeW: state => state.map.treeW,
      menuShowType: state => state.map.menuShowType,
      legendShow: state => state.map.legendShow,
      layerList: state => state.map.layerList,
      deptId: state => state.user.deptId,
      authDeptIds: state => state.map.authDeptIds,
      layerSetting: state => state.map.layerSetting,
      recordList: state => state.map.recordList,
      recordIndex: state => state.map.recordIndex,
      deptShow: state => state.map.deptShow,
      deptW: state => state.map.deptW,
      catalogW: state => state.map.catalogW,
    }),
    leftStyle() {
      let num1 = this.catalogW ? this.deptW + 25 : this.deptW + 20;
      let num2 = this.catalogW + 10;
      let num = num1 + num2;
      if (this.isBig) {
        num = num + 40;
      }
      return { left: num + 'px' }
    },
  },
  mounted() {
    this.isBig = isBigScreen();
    this.routeType = this.$route.query.type || ''
    if (this.$route.query.type == '1') {
      this.menuList = [{
        name: '返回',
        icon: require('@/assets/map/back.png'),
        value: 3,
      },
      ]
    }
    window.$Bus.$on('legendClose', (bool) => {
      // 处理事件逻辑
      let val = 9;
      // 从menuList中查找到 value= 9 的下标
      let index = this.menuList.findIndex(item => item.value === val);
      this.menuList[index].act = bool
    });
    this.layerArr = this.layerList.map(v => {
      return {
        layerName: v.layerName || '天地图影像',
        ifShow: !!v.ifShow,
        opacity: 100,
      }
    })
    window.addEventListener('resize', () => {
      this.sizeChange = window.innerHeight > 1000
    })
    window.$Bus.$on('JieGouWuJianCe', () => {
      this.handleMenuClick({ value: 3 })
    });
  },
  destroyed() {
    // 销毁前取消监听，避免内存泄露
    window.$Bus.$off('legendClose', () => { });
    window.$Bus.$off('JieGouWuJianCe', () => { });
  },
  methods: {
    ...mapActions({
      setShowType: 'map/setShowType',
      getTreeList: 'map/getTreeList',
      legendShowOrHide: 'map/legendShowOrHide',
      getTableShow: 'map/getTableShow',
      changeClickBoolean: 'map/changeClickBoolean',
      getLegend: 'map/getLegend',
      getMapLayer: 'map/getMapLayer',
      getLineClick: 'map/getLineClick',
      setSpread: 'map/setSpread',
    }),
    ...mapMutations({
      setRecordBool: 'map/setRecordBool',
      setRecordIndex: 'map/setRecordIndex',
      setDeptShow: 'map/setDeptShow',
      setDeptW: 'map/setDeptW',
      setCatalogW: 'map/setCatalogW',
    }),
    async handleMenuClick(row, index) {
      let isAdmin = this.$store.getters.roles.includes("admin");
      let lineDeptIds = isAdmin ? [] : [this.deptId]
      switch (row.value) {
        // 切换 目录展示类型 1树形 2数据总览
        case 1:
          this.setShowType(this.menuShowType == 1 ? 2 : 1);
          // 重新设置部门id 为登录用户的部门Id
          await this.$store.dispatch('GetInfo').then(res => {
            if (res.code == 200) {
              this.$store.commit('SET_DEPTID', res.user.deptId)
            }
          })
          let deptIds = (this.authDeptIds && this.authDeptIds.length) ? this.authDeptIds : [];
          if (isAdmin) {
            deptIds = [];
          } else {
            deptIds = [this.$store.getters.deptId]
          }
          this.getTreeList({ deptIds: deptIds, isRefresh: true }).finally(() => { });
          this.getTableShow(false)
          removeAllLayer(window.mapLayer)
          removeLayer(window.mapLayer, 'dataLayer')
          removeLayer(window.mapLayer, 'pbfLayer')
          // 管理处树显示控制
          let deptShow = this.menuShowType == 1 ? false : true
          this.setDeptShow({ bool: deptShow });
          if (!deptShow) {
            let width = this.isBig ? 640 : 320;
            this.setSpread(width)
            this.setDeptW({ deptW: 0 });
            this.setCatalogW({ catalogW: 318 });
          } else {
            this.setSpread(0);
            this.setCatalogW({ catalogW: 0 });
          }
          // 切换清空图列数据
          this.getLegend([]);
          this.getShape(true);
          // removeLayer(window.mapLayer, 'pbfLayer', 'lineLayer')
          removeLayer(window.mapLayer, "路网信息", "name");
          // 注释暂不改变路线切片
          getLineConfig(lineDeptIds);
          // 通知左侧树
          window.$Bus.$emit('reSetDept', true)
          break;
        case 2: // 图层设置
          row.act = !row.act;
          this.layerShow = row.act;
          break;
        case 3: // 返回-行政区
          removeAllLayer(window.mapLayer);
          // 重新设置部门id 为登录用户的部门Id
          await this.$store.dispatch('GetInfo').then(res => {
            if (res.code == 200) {
              this.$store.commit('SET_DEPTID', res.user.deptId)
            }
          })
          this.getShape(true);
          removeLayer(window.mapLayer, "路网信息", "name");
          // 注释暂不改变路线切片
          getLineConfig(lineDeptIds);
          // window.$Bus.$emit('mapClick');
          removeLayer(window.mapLayer, 'dataLayer', 'name');
          // removeLayer(window.mapLayer, 'borderLayer', 'name');
          // 取消列表显示
          this.getTableShow(false);
          // 返回行政区重新获取树数据
          this.getTreeList({ deptIds: (this.authDeptIds && this.authDeptIds.length) ? this.authDeptIds : (deptIds || []) })
          // 清空图例数据
          this.getLegend([])
          // 管理树显示控制
          window.$Bus.$emit('reSet', false)
          break;
        case 4: // 测距
          row.act = !row.act;
          // 修改测面、绘面选中状态
          if (row.act) {
            this.menuList[index + 1].act = false;
            this.menuList[index + 2].act = false;
            this.menuList[12].act = false;
            this.menuList[9].act = false;
            let mapDiv = window.mapLayer.getTargetElement();
            mapDiv.style.cursor = 'pointer'
            onMeasure();
          } else {
            window.drawSource?.clear()
            window.draw.finishDrawing() // 绘制完成
            window.mapLayer.removeInteraction(window.draw)
            window.draw = null;
          }
          break;
        case 5: // 测面
          row.act = !row.act;
          // 修改测距、绘面选中状态
          if (row.act) {
            this.menuList[index - 1].act = false;
            this.menuList[index + 1].act = false;
            this.menuList[12].act = false;
            this.menuList[9].act = false;
            let mapDiv = window.mapLayer.getTargetElement();
            mapDiv.style.cursor = 'pointer'
            onMeasure('Polygon');
          } else {
            window.drawSource?.clear()
            window.draw.finishDrawing() // 绘制完成
            window.mapLayer.removeInteraction(window.draw)
            window.draw = null;
          }
          break;
        case 6: // 绘面
          row.act = !row.act;
          // 修改测距、测面选中状态
          if (row.act) {
            this.menuList[index - 1].act = false;
            this.menuList[index - 2].act = false;
            this.menuList[12].act = false;
            this.menuList[9].act = false;
            let mapDiv = window.mapLayer.getTargetElement();
            mapDiv.style.cursor = 'pointer'
            onMeasure('Polygon', true);
          } else {
            window.drawSource?.clear()
            window.draw.finishDrawing() // 绘制完成
            window.mapLayer.removeInteraction(window.draw)
            window.draw = null;
          }
          break;
        case 7: // 完成
          if (window.draw) {
            window.draw.finishDrawing(); // 绘制完成
          }
          break;
        case 8: // 清空
          this.removeLayer('pbfLayer')
          this.removeLayer('pointLayer1')
          this.removeLayer('pointLayer2')
          this.removeLayer('drawLayer')
          removeAllLayer(window.mapLayer)
          removeLayer(window.mapLayer, 'clickLayer')
          if (window.draw) {
            window.drawSource?.clear()
            window.draw.finishDrawing() // 绘制完成
            window.mapLayer.removeInteraction(window.draw)
            window.draw = null;
          }
          break;
        case 9: // 图例
          row.act = !row.act;
          this.legendShowOrHide(row.act)
          break;

        case 10: // 全景
          row.act = !row.act;
          let mapDivEl = window.mapLayer.getTargetElement(); // 获取地图容器的DOM元素
          if (row.act) {
            mapDivEl.style.cursor = `url(${cursorIcon}),auto`
          } else {
            mapDivEl.style.cursor = 'pointer'
          }
          // 取消点选 选中状态
          this.menuList[index + 1].act = false;
          this.getLineClick(row.act)
          break;
        case 11: // 点选
          row.act = !row.act;
          let mapDiv = window.mapLayer.getTargetElement(); // 获取地图容器的DOM元素
          if (row.act) {
            this.menuList[3].act = false;
            this.menuList[4].act = false;
            this.menuList[5].act = false;
            this.menuList[12].act = false;
            window.mapLayer.removeInteraction(window.draw)
            window.draw = null;
            mapDiv.style.cursor = 'crosshair'
          } else {
            mapDiv.style.cursor = 'pointer'
          }
          // 取消全景 选中状态
          this.menuList[index - 1].act = false;
          this.getLineClick(row.act);
          break;
        case 12: // 上一次地图
          this.setRecordBool({ bool: false });
          // 判断记录列表是否有数据
          if (this.recordList.length > 0) {
            let index = this.recordIndex - 1;
            if (index < 0) return;
            let record = this.recordList[index];
            if (!record) return;
            // window.mapLayer.getView().setCenter(record.center);
            // window.mapLayer.getView().setZoom(record.zoom);
            this.setRecordIndex({ index });
            // 设置动画
            this.mapAnimate(record);
          }
          break;
        case 13: // 下一次地图
          this.setRecordBool({ bool: false });
          // 判断记录列表是否有数据
          if (this.recordList.length > 0) {
            let index = this.recordIndex;
            if (index >= this.recordList.length) return;
            index++;
            let record = this.recordList[index];
            if (!record) return;
            // window.mapLayer.getView().setCenter(record.center);
            // window.mapLayer.getView().setZoom(record.zoom);
            this.setRecordIndex({ index });
            // 设置动画
            this.mapAnimate(record);
          }
          break;
        case 14: // 打印
          row.act = !row.act;
          if (row.act) {
            this.menuList[3].act = false;
            this.menuList[4].act = false;
            this.menuList[5].act = false;
            this.menuList[9].act = false;
            window.mapLayer.removeInteraction(window.draw)
            window.draw = null;
            let mapDiv = window.mapLayer.getTargetElement();
            mapDiv.style.cursor = 'pointer'
            handleRectangle();
          } else {
            window.mapLayer.removeInteraction(window.draw)
            window.draw = null;
          }
          break;
        default:
          break;
      }
      this.$emit('click', row)
    },
    // 地图移动动画
    mapAnimate(record) {
      let view = window.mapLayer.getView()
      let parts = 2;
      let called = false;
      let duration = 2000;//动画的持续时间（以毫秒为单位）
      let zoom = 7
      //动画完成的回调函数
      function callback(complete) {
        --parts;
        if (called) {
          return;
        }
        if (parts === 0 || !complete) {
          called = true;
          // done(complete);
        }
      }
      view.animate({
        center: record.center,
        duration: duration
      }, callback);
      view.animate({
        zoom: record.zoom - 1,
        duration: duration / 2
      }, {
        zoom: record.zoom,
        duration: duration / 2
      }, callback);
    },
    // 图层显示控制
    async handleLayerChange(row, e) {
      let layer = await this.getLayer(row.layerName)
      if (!layer) return;
      if (Object.prototype.toString.call(layer) === '[object Array]') {
        layer.forEach(v => {
          v.setVisible(e)
        })
      } else {
        layer.setVisible(e)
      }
      // 修改底图数据
      let arr = this.layerList.map(v => {
        if (v.layerName == row.layerName) {
          v.ifShow = e
        }
        return v
      })
      this.getMapLayer(arr)
    },
    // 图层显示透明度
    async sliderChange(row, e) {
      let layer = await this.getLayer(row.layerName)
      if (!layer) return;
      if (Object.prototype.toString.call(layer) === '[object Array]') {
        layer.forEach(v => {
          v.setOpacity(e / 100);
        })
      } else {
        layer.setOpacity(e / 100);
      }
    },
    removeLayer(layerName = 'shapeLayer') {
      let allLayer = window.mapLayer?.getLayers()?.getArray();
      if (!allLayer) return;
      let layer = allLayer.find((l) => l.get('name') == layerName);
      if (layer) {
        window.mapLayer.removeLayer(layer);
      }
    },
    async getLayer(layerName = '') {
      if (!layerName) return;
      let allLayer = window.mapLayer?.getLayers()?.getArray();
      if (!allLayer) return;
      let layer = allLayer.find((l) => l.get('name') == layerName || l.get('treeLayerName') == layerName) || [];
      if (this.menuShowType == 1 && (layer && Object.prototype.toString.call(layer) === '[object Array]')) {
        // 通过父组件实例获取父组件下 树子组件选中的数据
        let treeData = this.instance.$refs.treeRef.getTreeCheckedData()
        if (treeData) {
          let currentData = treeData.filter(v => v.id == layerName)
          // 数组扁平化
          let arr = this.flattenArray(currentData)
          arr.forEach(item => {
            let value = item?.columnName ? ((item?.columnName + item?.value) || item.id) : item.id
            let l = allLayer.find((l) => l.get('name') == value || l.get('treeLayerName') == value);
            layer.push(l)
          })
        }
      }
      if (layer) {
        return layer;
      }
    },
    flattenArray(arr) {
      return arr.reduce((acc, val) => acc.concat(Array.isArray(val.child) ? this.flattenArray(val.child) : val), []);
    },
    drawComplete() {
      return '绘制功能未开启'
    },
    // 重新获取行政区划
    getShape(back = false) {
      let rangeData = cache.session.getJSON("rangeData");
      if (rangeData && back) {
        this.setRange(rangeData)
      } else {
        let deptIds = (this.authDeptIds && this.authDeptIds.length) ? this.authDeptIds : ([])
        getShapeList({ sysDeptIds: deptIds }).then(res => {
          if (res.code == 200 && res.data) {
            this.setRange(res.data)
          }
        });
      }
    },
    setRange(data) {
      let wkt = wktFormat.writeGeometry(new Point(defCenter))
      // 先移除 遮罩
      removeLayer(window.mapLayer, 'maskLayer');
      // 计算多个面之间的中心点
      // let calculateArea = this.setCalculate(data);
      let shape = data[0].mgeom;
      let feature;
      let isAdmin = this.$store.getters.roles.includes('admin')
      // 如果是 deptId 为 1 定位到 坐标为 defCenter的位置
      if (isAdmin || (data && data.length > 1)) {
        feature = wktFormat.readFeature(wkt)
        let view = window.mapLayer.getView()

        let parts = 2;
        let called = false;
        let duration = 2000;//动画的持续时间（以毫秒为单位）
        let zoom = this.isBig ? 8.5 : 7
        //动画完成的回调函数
        function callback(complete) {
          --parts;
          if (called) {
            return;
          }
          if (parts === 0 || !complete) {
            called = true;
            // done(complete);
          }
        }
        view.animate({
          center: defCenter,
          duration: duration
        }, callback);
        view.animate({
          zoom: zoom,
          duration: duration / 2
        }, {
          zoom: zoom,
          duration: duration / 2
        }, callback);
      } else {
        feature = new WKT().readFeature(shape, {
          dataProjection: 'EPSG:4326',
          featureProjection: 'EPSG:3857',
        });

        window.mapLayer.getView().fit(feature.getGeometry().getExtent(), {
          size: window.mapLayer.getSize(),
          duration: 1500,
          padding: [50, 50, 50, 50], // 视图边缘和地图边缘之间的距离
          easing: (t) => { // 可选的缓动函数
            return t * (2 - t); // 二次缓动
          },
          minResolution: 5,
        })
      }
      // this.changeClickBoolean(false)


      // 添加遮罩
      addMapMask(window.mapLayer, data)
      // 添加点位信息
      addWidthFeature(window.mapLayer, data, 'dataLayer', null, true, this.$store.state.map.deptMinZoom);
      removeLayer(window.mapLayer, 'dataLayer')
    },
    // 计算多个面的中心点
    setCalculate(data) {
      if (data && data.length > 1) {
        let areaCenter = []
        data.forEach((item, index) => {
          let shape = item.mgeom || item.dgeom
          let feature = new WKT().readFeature(shape, {
            dataProjection: 'EPSG:4326',
            featureProjection: 'EPSG:3857',
          });
          let extent = feature.getGeometry().getExtent()
          // 根据 extent 计算中心点坐标
          const centerX = (extent[0] + extent[2]) / 2;
          const centerY = (extent[1] + extent[3]) / 2;
          let center = [centerX, centerY];
          areaCenter[index] = center;
        })
        // let lineCoordinates = []
      }
    },
    handleClose() {
      this.menuList[1].act = false;
      this.layerShow = false
    },
  }
}
</script>

<style lang="scss" scoped>
@import "@/assets/styles/utils.scss";

.left-menu {
  width: vwpx(120px);
  height: auto;
  max-height: calc(100vh - 200px);
  position: absolute;
  top: vwpx(15px);
  z-index: 9;

  .menu-list {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    margin-bottom: vwpx(8px);
    cursor: pointer;

    img {
      // width: 45px;
      // height: 45px;
      width: vwpx(90px);
      height: vwpx(90px);
      margin-bottom: 2px;
    }

    span {
      font-family: Microsoft YaHei, Microsoft YaHei;
      font-weight: 400;
      font-size: vwpx(26px);
      color: #FFFFFF;
      display: block;
      max-width: vwpx(180px);
      min-width: vwpx(100px);
      text-align: center;
    }

    &:active {
      animation: click 2s ease;
    }
  }

  .layer-setting {
    position: absolute;
    top: vwpx(100px);
    left: vwpx(140px);
    width: vwpx(640px);
    min-height: vwpx(100px);
    max-height: vwpx(700px);
    background: linear-gradient(90deg, rgba(2, 10, 30, 0.8) 0%, rgba(12, 42, 86, 0.2) 100%);
    box-shadow: inset 0px 0px 10px 0px #3662EC;
    border-radius: vwpx(12px);
    border: 1px solid #0687FF;
    padding: vwpx(10px) vwpx(20px);
    overflow-y: auto;
    overflow-x: hidden;

    // 滚动条整体部分
    &::-webkit-scrollbar {
      width: 2px;
      height: 2px;
    }

    // 滚动条轨道
    &::-webkit-scrollbar-track {
      background-color: transparent;
    }

    // 滚动条的滑块部分
    &::-webkit-scrollbar-thumb {
      background-color: #0687FF;
    }

    // 当滑块悬停或活动时的样式
    &::-webkit-scrollbar-thumb:hover {
      background-color: #0687FF;
    }

    .el-checkbox {
      color: #ffffff;
    }

    .el-slider {
      .el-slider__runway {
        margin: 5px 0;
      }
    }
  }

  .big-setting {
    .el-checkbox {
      color: #ffffff;

      ::v-deep .el-checkbox__label {
        font-size: 1.2vh;
      }

      ::v-deep .el-checkbox__inner {
        width: vwpx(30px);
        height: vwpx(30px);
      }
    }

    .el-slider {
      ::v-deep .el-slider__bar {
        height: vwpx(12px);
      }
    }
  }

  .setting-close {
    position: absolute;
    top: vwpx(75px);
    left: vwpx(750px);
    width: vwpx(50px);
    height: vwpx(50px);
    border: 1px solid #0687FF;
    border-radius: 50%;
    cursor: pointer;
    z-index: 99;

    display: flex;
    align-items: center;
    justify-content: center;

    .close {
      color: #ffffff;
      font-size: vwpx(28px);
    }
  }
}

@keyframes click {
  0% {
    transform: scale(1);
  }

  20% {
    transform: scale(0.8);
  }

  40% {
    transform: scale(0.7);
  }

  60% {
    transform: scale(0.8);
  }

  100% {
    transform: scale(1);
  }
}
</style>