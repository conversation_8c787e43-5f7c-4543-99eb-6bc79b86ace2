<template>
    <div class="check-edit-container">
        <div class="check-edit-title">
            基础信息
<!--            <el-button-->
<!--                type="primary"-->
<!--                icon="el-icon-download"-->
<!--                style="float: right; margin: 0 10px"-->
<!--                size="small"-->
<!--                v-tooltip="'下载巡查报告'"-->
<!--                @click="handleDownload"-->
<!--                v-if="checkEntity.id"-->
<!--            >-->
<!--                下载报告-->
<!--            </el-button>-->
        </div>

        <!-- 将审核时间移到这里，单独放置，不受表单disabled属性影响 -->
        <el-form
            v-if="title && title.substring(0, 2) === '审核'"
            :model="checkEntity"
            inline
            label-width="80px"
            size="small"
            style="margin: 10px"
        >
            <el-form-item label="审核时间" prop="auditTime">
                <el-date-picker
                    v-model="checkEntity.auditTime"
                    type="datetime"
                    placeholder="请选择审核时间"
                    value-format="yyyy-MM-dd HH:mm:ss"
                ></el-date-picker>
            </el-form-item>
        </el-form>
        <el-button @click="open = true" type="primary" size="mini" style="margin: 0 10px" v-if="!disabled">
            选择{{ partsType[checkEntity.partsType].substring(0, 2) }}
        </el-button>
        <el-form
            ref="form"
            :model="checkEntity"
            :rules="rules"
            inline
            label-width="80px"
            size="small"
            style="margin: 10px"
            :disabled="disabled"
        >
            <el-form-item
                :label="`${partsType[checkEntity.partsType].substring(0, 2)}${
                    ['3', '4'].includes(checkEntity.partsType) ? '类型' : '名称'
                }`"
                prop="assetName"
            >
                <el-input
                    v-model="checkEntity.assetName"
                    disabled
                    :placeholder="`请输入${partsType[checkEntity.partsType].substring(0, 2)}${
                        ['3', '4'].includes(checkEntity.partsType) ? '类型' : '名称'
                    }`"
                />
            </el-form-item>
            <el-form-item :label="`${partsType[checkEntity.partsType].substring(0, 2)}编码`" prop="assetCode">
                <el-input
                    v-model="checkEntity.assetCode"
                    disabled
                    :placeholder="`请输入${partsType[checkEntity.partsType].substring(0, 2)}编码`"
                />
            </el-form-item>
            <el-form-item :label="`${partsType[checkEntity.partsType].substring(0, 2)}桩号`" prop="centerStake">
                <PileInput
                    v-model="checkEntity.centerStake"
                    disabled
                    format
                    :placeholder="`请输入${partsType[checkEntity.partsType].substring(0, 2)}桩号`"
                />
            </el-form-item>
            <el-form-item label="路线编码" prop="routeCode">
                <el-input v-model="checkEntity.routeCode" placeholder="请输入路线编码" disabled />
            </el-form-item>
            <el-form-item label="养护路段" prop="maintenanceSectionName">
                <el-input v-model="checkEntity.maintenanceSectionName" placeholder="请输入路线名称" disabled />
            </el-form-item>
            <el-form-item label="养护单位" prop="maintainUnitName">
                <el-input v-model="checkEntity.maintainUnitName" placeholder="请输入养护单位" disabled />
            </el-form-item>

            <el-form-item label="记录人" prop="oprUserName">
                <el-cascader
                    ref="userCascade"
                    v-model="oprUser"
                    :options="deptUserOptions"
                    :props="{ multiple: true, value: 'id', emitPath: false }"
                    :show-all-levels="false"
                    filterable
                    clearable
                    style="width: 100%"
                />
            </el-form-item>
            <el-form-item label="检查时间" prop="checkTime">
                <el-tooltip
                    :content="
                        !checkEntity.maintenanceSectionId || !checkEntity.assetId
                            ? `请先选择${partsType[checkEntity.partsType].substring(0, 2)}`
                            : ''
                    "
                    placement="top"
                    :disabled="!!(checkEntity.maintenanceSectionId && checkEntity.assetId)"
                >
                    <el-date-picker
                        clearable
                        v-model="checkEntity.checkTime"
                        type="date"
                        value-format="yyyy-MM-dd"
                        placeholder="请选择检查时间"
                        :picker-options="pickerOptions"
                        @focus="handleDateChange"
                        :disabled="!checkEntity.maintenanceSectionId || !checkEntity.assetId"
                    ></el-date-picker>
                </el-tooltip>
            </el-form-item>
            <el-form-item v-if="['2', '4', '6'].includes(checkEntity.partsType)" label="巡查类别 " prop="category">
                <el-select v-model="checkEntity.category" placeholder="请选择巡查类别">
                    <el-option
                        v-for="dict in dict.type.patrol_inspection_ilk"
                        :key="dict.value"
                        :label="dict.label"
                        :value="dict.value"
                    ></el-option>
                </el-select>
            </el-form-item>
            <el-form-item v-if="['5', '6'].includes(checkEntity.partsType)" label="天气" prop="weather">
                <el-autocomplete
                    v-model="checkEntity.weather"
                    :fetch-suggestions="weatherSuggestions"
                    placeholder="请输入天气"
                ></el-autocomplete>
            </el-form-item>
            <el-form-item label="备注" prop="remark" style="width: 97%">
                <el-input v-model="checkEntity.remark" type="textarea" placeholder="请输入备注" :rows="4" />
            </el-form-item>
        </el-form>

        <!--    检查项详情-->
        <div class="check-edit-title">检查详情</div>

        <el-table v-loading="loading" :data="detailList" size="mini" border>
            <el-table-column label="序号" type="index" width="50" />
            <el-table-column label="检查项" prop="partsTypeName" align="center"></el-table-column>
            <el-table-column align="center" label="检查内容" prop="des"></el-table-column>
            <el-table-column :label="titles[0]" prop="defect" align="center">
                <template slot-scope="scope">
                    <el-autocomplete
                        v-if="!scope.row.ignore"
                        :disabled="disabled"
                        v-model="scope.row.defect"
                        :fetch-suggestions="querySearch(scope.row, 'defect')"
                        :placeholder="`请输入${titles[0]}`"
                    ></el-autocomplete>
                    <div v-else-if="scope.row.ignore">/</div>
                </template>
            </el-table-column>
            <el-table-column :label="titles[1]" prop="advice" align="center">
                <template slot-scope="scope">
                    <el-autocomplete
                        v-if="!scope.row.ignore"
                        :disabled="disabled"
                        v-model="scope.row.advice"
                        :fetch-suggestions="querySearch(scope.row, 'advice')"
                        :placeholder="`请输入${titles[1]}`"
                    ></el-autocomplete>
                    <div v-else-if="scope.row.ignore">/</div>
                </template>
            </el-table-column>
            <el-table-column label="图片" prop="image" align="center">
                <template slot-scope="scope">
                    <ImageUpload
                        v-model="scope.row.image"
                        :is-show-tip="false"
                        :key="scope.row.image"
                        :disabled="disabled"
                        v-if="!scope.row.ignore"
                    ></ImageUpload>
                    <div v-else-if="scope.row.ignore">/</div>
                </template>
            </el-table-column>
        </el-table>
        <!--    资产选择对话框-->
        <el-dialog
            :title="`选择${partsType[checkEntity.partsType].substring(0, 2)}`"
            :visible="open"
            v-dialog-drag
            :modal="true"
            :modal-append-to-body="false"
            :append-to-body="true"
            :close-on-click-modal="false"
            width="85%"
            class="asset-select-dialog"
            @close="handleClose"
        >
            <div class="dialog-content">
                <div class="dialog-scroll-area">
                    <AssetTableDialog
                        ref="assetTableDialog"
                        :type="checkEntity.partsType"
                        v-model="assetList"
                        :visible.sync="open"
                        single
                        @check-time-change="handleCheckTimeChange"
                    />
                </div>
                <div class="dialog-footer">
                    <el-button
                        type="primary"
                        @click="
                            onAssetSelect();
                            open = false;
                        "
                    >
                        确 定
                    </el-button>
                    <el-button @click="handleClose">取 消</el-button>
                </div>
            </div>
        </el-dialog>
    </div>
</template>

<script>
    import { getMergedDays } from '@/api/patrol/assetCheck';
    import { listAllParts } from '@/api/patrol/parts';
    import { getDaysWithData } from '@/api/patrol/inspectionLogs';
    import ImageUpload from '@/views/patrol/diseases/ImageUpload.vue';
    import PileInput from '@/views/system/route/pileinput.vue';
    import AssetTableDialog from '@/views/patrol/assetCheck/assetTableDialog/index.vue';
    import { getTreeStruct } from '@/api/tmpl';
    import { listAllPartsIgnore } from '@/api/patrol/partsIgnore';
    import moment from 'moment';
    /**
     *日常巡查：
     *  桥梁：桥位桩号
     *  隧道：中心桩号，隧道日常巡查和经常检查都多了天气字段，并且子表有里程桩号
     *日常巡查和经常检查：经常检查多了检查类型，日常巡查子表需要检查内容
     *
     *桥涵隧：
     *   涵洞没有名称，名称改为类型
     */

    export default {
        name: 'AssetCheckInsertOrUpdate',
        dicts: ['patrol_inspection_ilk', 'patrol_inspection_category'],
        components: { ImageUpload, PileInput, AssetTableDialog },
        props: {
            checkEntity: {
                type: Object,
                require: true,
                // default: function () {
                //   return {
                //     partsType: '5'
                //   }
                // }
            },
            disabled: {
                type: Boolean,
                default: false,
            },
            title: {
                type: String,
                default: '',
            },
        },
        watch: {
            'checkEntity.details': {
                handler(val, oldVal) {
                    if (!val) {
                        this.getParts();
                    } else {
                        if (val === this.detailList) return;
                        this.loading = true;
                        listAllParts({ partsType: this.checkEntity.partsType })
                            .then((res) => {
                                let partsList = res.data;
                                this.checkEntity.details.forEach((item) => {
                                    let tempArray = partsList.filter((partsItem) => partsItem.id === item.partsTypeId);
                                    if (tempArray.length < 1) return;
                                    item.des = tempArray[0].des;
                                    item.options = JSON.parse(tempArray[0].extend);
                                });
                                this.detailList = this.checkEntity.details;
                            })
                            .finally(() => (this.loading = false));
                    }
                },
                immediate: true,
            },
            'checkEntity.partsType': {
                handler(val) {
                    if (
                        ['2', '4', '6'].includes(val) &&
                        !this.checkEntity.category &&
                        this.dict.type.patrol_inspection_ilk?.length > 0
                    ) {
                        // 当巡查类别为空且字典数据已加载时，默认选择第一个选项
                        this.$nextTick(() => {
                            this.checkEntity.category = this.dict.type.patrol_inspection_ilk[0]?.value || '';
                        });
                    }
                },
                immediate: true,
            },
            'dict.type.patrol_inspection_ilk': {
                handler(val) {
                    if (
                        ['2', '4', '6'].includes(this.checkEntity.partsType) &&
                        !this.checkEntity.category &&
                        val?.length > 0
                    ) {
                        // 当字典数据加载完成且巡查类别为空时，默认选择第一个选项
                        this.checkEntity.category = val[0]?.value || '';
                    }
                },
                immediate: true,
            },
            title: {
                handler(val) {
                    if (val && val.substring(0, 2) === '审核' && !this.checkEntity.auditTime) {
                        // 如果是审核模式且审核时间为空，则设置为当前时间
                        const now = new Date();
                        const year = now.getFullYear();
                        const month = String(now.getMonth() + 1).padStart(2, '0');
                        const day = String(now.getDate()).padStart(2, '0');
                        const hours = String(now.getHours()).padStart(2, '0');
                        const minutes = String(now.getMinutes()).padStart(2, '0');
                        const seconds = String(now.getSeconds()).padStart(2, '0');
                        this.checkEntity.auditTime = `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
                    }
                },
                immediate: true,
            },
        },
        data() {
            return {
                loading: false,
                open: false,
                assetList: null,
                partsIgnoreIds: null,
                // 部门-用户树选项
                deptUserOptions: [],
                rules: {
                    type: [{ required: true, message: '检查类型不能为空', trigger: 'change' }],
                    assetId: [{ required: true, message: '资产id不能为空', trigger: 'blur' }],
                    assetName: [{ required: false, message: '资产名称不能为空', trigger: 'blur' }],
                },
                detailList: [],
                partsType: {
                    1: '桥梁日常巡查',
                    2: '桥梁经常检查',
                    3: '涵洞定期检查',
                    4: '涵洞经常检查',
                    5: '隧道日常巡查',
                    6: '隧道经常检查',
                },
                partsType2Titles: {
                    1: ['状态描述', '保养措施意见'],
                    2: ['缺损类型', '缺损范围及保养意见'],
                    3: ['缺损类型', '缺损范围及保养意见'],
                    4: ['缺损类型', '缺损范围及保养意见'],
                    5: ['状态描述', '保养措施意见'],
                    6: ['缺损类型', '缺损范围及保养意见'],
                },
                asset2bridge: {
                    assetId: 'assetId',
                    assetName: 'assetName',
                    assetCode: 'assetCode',
                    routeId: 'routeId',
                    routeName: 'maintenanceSectionName',
                    routeCode: 'routeCode',
                    propertyUnitId: 'managementMaintenanceBranchId',
                    propertyUnitName: 'managementMaintenanceBranchName',
                    maintainUnitId: 'managementMaintenanceId',
                    maintainUnitName: 'managementMaintenanceName',
                    maintenanceSectionId: 'maintenanceSectionId',
                    maintenanceSectionName: 'maintenanceSectionName',
                    centerStake: 'centerStake',
                },
                asset2culvert: {
                    assetId: 'assetId',
                    assetName: 'assetName',
                    assetCode: 'assetCode',
                    routeId: 'routeId',
                    routeName: 'maintenanceSectionName',
                    routeCode: 'routeCode',
                    propertyUnitId: 'managementMaintenanceBranchId',
                    propertyUnitName: 'managementMaintenanceBranchName',
                    maintainUnitId: 'managementMaintenanceId',
                    maintainUnitName: 'managementMaintenanceName',
                    maintenanceSectionId: 'maintenanceSectionId',
                    maintenanceSectionName: 'maintenanceSectionName',
                    centerStake: 'centerStake',
                },
                asset2tunnel: {
                    assetId: 'assetId',
                    assetName: 'tunnelName',
                    assetCode: 'tunnelCode',
                    routeId: 'routeId',
                    routeName: 'maintenanceSectionName',
                    routeCode: 'routeCode',
                    propertyUnitId: 'managementMaintenanceBranchId',
                    propertyUnitName: 'managementMaintenanceBranchName',
                    maintainUnitId: 'managementMaintenanceId',
                    maintainUnitName: 'managementMaintenanceName',
                    maintenanceSectionId: 'maintenanceSectionId',
                    maintenanceSectionName: 'maintenanceSectionName',
                    centerStake: 'unifiedMileageStake',
                },
                allowedDays: [],
                currentViewMonth: null,
                currentCheckMonth: null,
                mergedDays: [], // 存储API返回的日期数据
                //   pickerOptions: this.customDateStyle(),
            };
        },
        computed: {
            titles() {
                return this.partsType2Titles[this.checkEntity.partsType ?? ''] ?? [];
            },
            oprUser: {
                set() {
                    let userList = this.$refs.userCascade?.getCheckedNodes() || [];
                    this.checkEntity.oprUserId = userList.map((item) => item.value).join(',');
                    this.checkEntity.oprUserName = userList.map((item) => item.label).join(',');
                },
                get() {
                    // 只在新增时设置默认记录人
                    if (!this.disabled && !this.checkEntity.oprUserId) {
                        this.checkEntity.checkTime = moment().format('YYYY-MM-DD');
                        this.checkEntity.oprUserId = this.$store.state.user.id.toString();
                        this.checkEntity.oprUserName = this.$store.state.user.nickName;
                    }
                    // 如果 oprUserId 是数字,转为字符串
                    if (typeof this.checkEntity?.oprUserId === 'number') {
                        return [this.checkEntity.oprUserId.toString()];
                    }
                    // 如果是字符串且包含逗号,则分割
                    if (typeof this.checkEntity?.oprUserId === 'string' && this.checkEntity.oprUserId.includes(',')) {
                        return this.checkEntity.oprUserId.split(',');
                    }
                    // 如果是字符串但不包含逗号,直接返回数组
                    if (typeof this.checkEntity?.oprUserId === 'string') {
                        return [this.checkEntity.oprUserId];
                    }
                    // 默认返回空数组
                    return [];
                },
            },
            pickerOptions() {
                return {
                    cellClassName: (date) => {
                        // 获取年、月、日
                        const year = date.getFullYear();
                        const month = date.getMonth() + 1;
                        const day = date.getDate();
                        if (['1', '3', '5'].includes(this.checkEntity.partsType)) {
                            // 日常巡查：检查具体日期
                            if (
                                year === parseInt(this.currentCheckMonth?.split('-')[0]) &&
                                month === parseInt(this.currentCheckMonth?.split('-')[1])
                            ) {
                                return this.mergedDays.includes(day) ? 'available-date' : 'unavailable-date';
                            }
                        } else {
                            // 经常检查：检查整月
                            if (year === parseInt(this.currentCheckMonth))
                                return this.mergedDays.includes(month) ? 'available-date' : 'unavailable-date';
                        }
                    },
                };
            },
        },
        methods: {
            querySearch(row, type) {
                if (type === 'defect')
                    return function (queryString, cb, options = row.options) {
                        if (!options) return cb([]);
                        let tempArray = Object.keys(options);
                        tempArray = tempArray.map((item) => {
                            return { value: item };
                        });
                        cb(tempArray);
                    };
                return function (queryString, cb, options = row.options) {
                    if (!options || !row.defect) return cb([]);
                    let tempArray = options[row.defect] ?? [];
                    tempArray = tempArray.map((item) => {
                        return { value: item };
                    });
                    cb(tempArray);
                };
            },
            handleCheckTimeChange(date) {
                if (date) {
                    this.checkEntity.checkTime = date;
                }
            },
            getParts() {
                this.loading = true;
                listAllParts({ partsType: this.checkEntity.partsType })
                    .then((res) => {
                        let partsList = res.data;
                        let temp = {
                            checkId: '',
                            partsTypeId: '',
                            partsTypeName: '',
                            defect: '',
                            advice: '',
                            image: '',
                            remark: null,
                        };
                        this.detailList = partsList.map((item) => {
                            return {
                                checkId: '',
                                partsTypeId: item.id,
                                partsTypeName: item.partsName,
                                defect: '',
                                advice: '',
                                image: '',
                                remark: '',
                                des: item.des,
                                ignore: false,
                                options: JSON.parse(item.extend),
                            };
                        });
                        this.checkEntity.details = this.detailList;
                        this.loading = false;
                    })
                    .catch(() => (this.loading = false));
            },
            onAssetSelect() {
                // 先只考虑单选的情况
                if (this.assetList.length < 0) return;
                let asset = this.assetList[0];
                let template;
                switch (this.checkEntity.partsType) {
                    case '1':
                    case '2':
                        template = this.asset2bridge;
                        break;
                    case '3':
                    case '4':
                        template = this.asset2culvert;
                        break;
                    case '5':
                    case '6':
                        template = this.asset2tunnel;
                        break;
                    default:
                        template = {};
                }
                const baseData = asset.baseData;
                for (let key in template) {
                    this.checkEntity[key] = baseData[template[key]];
                }
                this.assetList = null;
                // 桥梁经常检查需要配置不检查项为"/"
                if (this.checkEntity.partsType === '2') {
                    let temp = {
                        partsType: this.checkEntity.partsType,
                        assetId: this.checkEntity.assetId,
                    };
                    listAllPartsIgnore(temp).then((response) => {
                        this.partsIgnoreIds = response.data.map((item) => item.partsId); //检查项类型IDs
                        this.detailList.forEach((item) => {
                            if (this.partsIgnoreIds.includes(item.partsTypeId)) item.ignore = true;
                        });
                    });
                }
            },
            weatherSuggestions(queryString, cb) {
                const suggestions = [{ value: '晴' }, { value: '多云' }, { value: '小雨' }, { value: '中雨' }];
                cb(suggestions);
            },
            // 处理日期变化
            async handleDateChange() {
                await this.$nextTick();
                this.fetchAllowedDays();
                this.fetchMergedDays();
                document
                    .querySelectorAll(
                        "[aria-label='下个月'],[aria-label='上个月'],[aria-label='后一年'],[aria-label='前一年']"
                    )
                    .forEach((item) =>
                        item.addEventListener('click', () => {
                            this.fetchAllowedDays();
                            this.fetchMergedDays();
                        })
                    );
            },
            async fetchMergedDays() {
                let year, month;
                // 获取年月
                year = document.querySelectorAll('.el-date-picker__header-label')[0].innerHTML.slice(0, 4);
                month = document.querySelectorAll('.el-date-picker__header-label')[1].innerHTML.slice(0, -1);
                let monthStr;
                monthStr = `${year}-${month < 10 ? '0' + month : month}`;
                //   if (['1', '3', '5'].includes(this.checkEntity.partsType)) {
                //     monthStr = `${year}-${month < 10 ? '0' + month : month}`;
                //   } else {
                //     monthStr = `${year}`;
                //   }
                if (this.currentCheckMonth === monthStr) return;

                this.currentCheckMonth = monthStr;
                getMergedDays({
                    assetId: this.checkEntity.assetId,
                    yearMonth: monthStr,
                    type: this.checkEntity.partsType,
                })
                    .then((response) => {
                        this.mergedDays = Array.isArray(response.data) ? response.data.map(Number) : [];
                    })
                    .catch((error) => {
                        console.error('获取合并的日期失败', error);
                        this.mergedDays = [];
                    });
            },
            // 获取允许选择的天数
            async fetchAllowedDays() {
                let year, month;
                // 获取年月
                year = document.querySelectorAll('.el-date-picker__header-label')[0].innerHTML.slice(0, 4);
                month = document.querySelectorAll('.el-date-picker__header-label')[1].innerHTML.slice(0, -1);
                const monthStr = `${year}-${month < 10 ? '0' + month : month}`;

                if (this.currentViewMonth === monthStr) return;

                this.currentViewMonth = monthStr;
                // 在请求发送前将 allowedDays 设置为空数组
                this.allowedDays = [];

                getDaysWithData({
                    maintenanceSectionId: this.checkEntity.maintenanceSectionId,
                    yearMonth: monthStr,
                })
                    .then((response) => {
                        // 确保返回的数据是数组
                        this.allowedDays = Array.isArray(response.data) ? response.data : [];
                    })
                    .catch((error) => {
                        console.error('获取允许的天数失败', error);
                        this.allowedDays = []; // 发生错误时设置为空数组
                    });
            },
            handleClose() {
                this.assetList = null;
                this.open = false;
            },
            handleDownload() {
                const now = new Date();
                const timeStr = `${now.getFullYear()}年${now.getMonth() + 1}月${now.getDate()}日${now.getHours()}时${now.getMinutes()}分${now.getSeconds()}秒`;
                const fileName = `${this.partsType[this.checkEntity.partsType]}_${timeStr}.xlsx`;

                this.download(
                    'patrol/assetCheck/exportAssetReportCard',
                    {
                        checkIds: [this.checkEntity.id],
                        type: this.checkEntity.partsType,
                        dataRule: true
                    },
                    fileName,
                    {
                        parameterType: 'body',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                    }
                );
            },
            submitForm: function (data) {
                if (!(data instanceof PointerEvent)) {
                    // 处理状态描述为空的情况
                    if (this.form.details) {
                        this.form.details.forEach((item) => {
                            if (!item.ignore && (!item.defect || item.defect.trim() === '')) {
                                item.defect = '未见异常';
                            }
                            if (!item.ignore && (!item.advice || item.advice.trim() === '')) {
                                item.advice = '正常保养';
                            }
                        });
                    }
                    updateAssetCheck(data).then((response) => {
                        this.$modal.msgSuccess(`${this.title.substring(0, 2)}成功`);
                        this.open = false;
                        this.getList();
                    });
                } else {
                    this.$refs.assetCheckInsertOrUpdate.$refs['form'].validate((valid) => {
                        if (valid) {
                            // 处理状态描述为空的情况
                            if (this.form.details) {
                                this.form.details.forEach((item) => {
                                    //item.defect 去除空格
                                    if (!item.ignore && (!item.defect || item.defect.trim() === '')) {
                                        item.defect = '未见异常';
                                    }
                                    if (!item.ignore && (!item.advice || item.advice.trim() === '')) {
                                        item.advice = '正常保养';
                                    }
                                });
                            }

                            if (this.form.id != null) {
                                updateAssetCheck(this.form).then((response) => {
                                    this.$modal.msgSuccess('修改成功');
                                    this.open = false;
                                    this.getList();
                                });
                            } else {
                                addAssetCheck(this.form).then((response) => {
                                    this.$modal.msgSuccess('新增成功');
                                    this.open = false;
                                    this.getList();
                                });
                            }
                        }
                    });
                }
            },
        },
        created() {
            /** 查询部门-用户下拉树结构 */
            getTreeStruct({ types: 111 }).then((response) => {
                this.deptUserOptions = response.data;
            });
        },
    };
</script>
<style lang="scss">
    td.available.available-date {
        > div {
            > span {
                background-color: #409eff !important;
                color: #fff !important;
                border-radius: 50%;
            }
        }
    }

    td.available.unavailable-date {
        > div {
            > span {
                background-color: #f56c6c !important;
                color: #fff !important;
                border-radius: 50%;
            }
        }
    }
    td.disabled.available-date {
        > div {
            > span {
                background-color: #87c1fa !important;
                color: #fff !important;
                border-radius: 50%;
            }
        }
    }

    td.disabled.unavailable-date {
        > div {
            > span {
                background-color: #f8a5a5 !important;
                color: #fff !important;
                border-radius: 50%;
            }
        }
    }

    .asset-select-dialog {
        display: flex;
        justify-content: center;

        .el-dialog {
            margin: 0 auto !important;
            position: relative;
            top: 50%;
            transform: translateY(-50%);
            height: 90vh;
        }

        .el-dialog__body {
            padding: 0;
            height: calc(90vh - 120px);
        }

        .dialog-content {
            display: flex;
            flex-direction: column;
            height: 100%;
        }

        .dialog-scroll-area {
            flex: 1;
            overflow-y: auto;
            padding: 20px 30px;
        }

        .dialog-footer {
            display: flex;
            justify-content: center;
            align-items: center;
            padding: 10px 0;
            background: #fff;
            border-top: 1px solid #e4e7ed;
        }
    }

    // 确保遮罩层正确显示
    .v-modal {
        opacity: 0.6;
        position: fixed;
    }

    .dialog-footer {
        display: flex;
        justify-content: center;
        align-items: center;
        padding: 10px 0;
    }
</style>
<style lang="scss" scoped>
    .check-edit-container {
        padding-left: 20px;
        background: white;
        /*  padding: 20px;*/
        .el-form--inline .el-form-item {
            width: 31%;
            margin-right: 2%;
        }

        ::v-deep .el-form-item__content {
            width: calc(100% - 80px);
        }

        ::v-deep .el-form-item__label {
            color: #72767b;
        }

        .el-input,
        .el-autocomplete,
        .el-date-editor,
        .el-select {
            width: 100%;
        }

        ::v-deep .el-table {
            width: 96% !important;
            margin: 10px;
        }

        ::v-deep .el-table .el-input__inner {
            border: none !important;
            //padding: 0 !important;
        }

        ::v-deep .el-table td.el-table__cell {
            height: 50px;
            padding: 0;
            background-color: unset !important;
        }

        ::v-deep .el-upload-list__item {
            width: 42px;
            height: 42px;
            margin: 0px 5px -6px 0 !important;
        }

        ::v-deep .el-upload--picture-card {
            width: 42px;
            height: 42px;
            margin: 4px 0;
        }

        ::v-deep .el-icon-plus {
            font-size: 1rem;
        }
    }

    .check-edit-title {
        font-weight: bold;
        font-size: 1.15rem;
        padding: 10px 0;
        /*  color: #333333;*/
        /*  color: #72767b;*/
        &:before {
            content: '';
            display: inline-block;
            width: 5px;
            height: 1.5rem;
            vertical-align: bottom;
            margin-right: 0.8rem;
            background: #3797eb;
        }
    }

    /**
 图片上传居中
 */
    ::v-deep .component-upload-image > div:first-child {
        justify-content: center;
    }

    .report-dialog {
        ::v-deep .el-dialog {
            margin-top: 5vh !important;
            height: 90vh;

            .el-dialog__body {
                padding: 10px;
                height: calc(90vh - 100px); // 减去标题和padding的高度
                overflow-y: auto;
                scrollbar-width: none;
            }
        }
    }
</style>
