<template>
  <div class="upload-file">
    <el-upload
      ref="fileUpload"
      multiple
      drag
      :action="uploadFileUrl"
      :before-upload="handleBeforeUpload"
      :file-list="fileList"
      :limit="limit"
      :on-preview="handlePictureCardPreview"
      :on-error="handleUploadError"
      :on-exceed="handleExceed"
      :on-success="handleUploadSuccess"
      :before-remove="beforeRemove"
      :on-remove="handleRemove"
      class="upload-file-uploader"
      :headers="headers"
      list-type="picture"
    >
      <el-button size="small" type="primary">点击上传</el-button>
      <div slot="file" slot-scope="{ file }">
        <img
          class="el-upload-list__item-thumbnail"
          :src="file.thumbUrl"
          fit="fit"
          alt=""
        />
        <a
          class="el-upload-list__item-name"
          @click="handlePictureCardPreview(file)"
        >
          <i class="el-icon-document"></i>
          {{ file.name }}
        </a>
        <label class="el-upload-list__item-status-label">
          <i class="el-icon-upload-success el-icon-check"></i>
        </label>
        <span
          v-if="!disabled"
          class="el-upload-list__item-delete"
          @click="handleDownload(file)"
        >
          <i class="el-icon-download" />
        </span>
        <i class="el-icon-close" @click="confirmAndRemove(file)"></i>
        <i class="el-icon-close-tip">点击删除</i>
      </div>
      <!-- 上传提示 -->
      <i class="el-icon-upload"></i>
      <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
      <div v-if="showTip" slot="tip" class="el-upload__tip">
        请上传
        <template v-if="fileSize">
          大小不超过 <b style="color: #f56c6c">{{ fileSize }}MB</b>
        </template>
        <template v-if="fileType">
          格式为 <b style="color: #f56c6c">{{ fileType.join("/") }}</b>
        </template>
        的文件
      </div>
    </el-upload>
    <el-dialog
      :visible.sync="dialogVisible"
      custom-class="fixed-dialog"
      append-to-body
    >
      <div style="position: relative">
        <el-tooltip content="下载" placement="top">
          <el-button
            style="
              position: absolute;
              top: 10px;
              right: 10px;
              background-color: #409eff;
              color: white;
              font-size: 16px;
              z-index: 9999;
            "
            size="medium"
            icon="el-icon-download"
            @click="handleDownload(dialogFile)"
          >
          </el-button>
        </el-tooltip>
        <OfficePreview :dialog-url="dialogUrl" :content-type="contentType" />
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getToken } from "@/utils/auth";
import { findFiles } from "@/api/file/index.js";
import { removeFile, addUploadRecord } from "@/api/system/fileUpload.js";
import OfficePreview from "@/components/OfficePreview/index.vue";

export default {
  name: "FileUpload",
  components: {
    OfficePreview,
  },
  props: {
    value: {
      // 绑定值传ownerId，可以是id数组
      type: undefined,
      default: () => [],
    },
    // 数量限制
    limit: {
      type: Number,
      default: null,
    },
    // 大小限制(MB)
    fileSize: {
      type: Number,
      default: null,
    },
    // 文件类型, 例如['png', 'jpg', 'jpeg']
    fileType: {
      type: Array,
      //   default: () => ["doc", "xls", "ppt", "txt", "pdf", "png", "jpg", "jpeg"],
      default: null,
    },
    // 是否显示提示
    isShowTip: {
      type: Boolean,
      default: true,
    },
    fileSubId: {
      type: Number,
      default: 0,
    },
    bizId: {
      type: String,
      default: "",
    },
    ownerId: {
      // 文件唯一标识id
      type: Number,
      default: +new Date(),
    },
    storagePath: {
      // 存储路径用于分类
      type: String,
      default: "",
    },
  },
  data() {
    return {
      number: 0,
      uploadList: [],
      uploadFileUrl:
        process.env.VUE_APP_BASE_API +
        `/file/upload?platform=ylzx&ownerId=${this.ownerId}&storagePath=${this.storagePath}`,
      headers: {
        Authorization: "Bearer " + getToken(),
      },
      fileList: [],
      dialogUrl: "",
      contentType: "",
      dialogVisible: false,
      disabled: false,
      dialogFile: null,
    };
  },
  computed: {
    // 是否显示提示
    showTip() {
      return this.isShowTip && (this.fileType || this.fileSize);
    },
  },
  watch: {
    value: {
      handler(val) {
        if (val) {
          // 首先将值转为数组
          const list = Array.from(
            new Set(Array.isArray(val) ? val : this.value.split(","))
          );
          // 然后将数组转为对象数组
          const tempArr = [];
          list.forEach(async (ownerId) => {
            const { data } = await findFiles({ ownerId });
            tempArr.push(
              ...data.map((file) => {
                return {
                  ...file,
                  name: file.originalFilename,
                  url: file.url,
                  ownerId: file.ownerId,
                  thumbUrl: this.getFileThumbnailUrl(
                    file.contentType,
                    file.thumbUrl
                  ),
                };
              })
            );
          });
          this.fileList = tempArr;
        } else {
          this.fileList = [];
        }
      },
      deep: true,
      immediate: true,
    },
    ownerId: {
      handler(val) {
        if (val) {
          //如果ownerId有变化，这里需要监听，否则url地址的ownerId不会变化
          this.uploadFileUrl =
            process.env.VUE_APP_BASE_API +
            `/file/upload?platform=ylzx&ownerId=${this.ownerId}&storagePath=${this.storagePath}`;
        }
      },
    },
  },
  methods: {
    // 上传前校检格式和大小
    handleBeforeUpload(file) {
      // 校检文件类型
      if (this.fileType) {
        const fileName = file.name.split(".");
        const fileExt = fileName[fileName.length - 1];
        const isTypeOk = this.fileType.indexOf(fileExt) >= 0;
        if (!isTypeOk) {
          this.$modal.msgError(
            `文件格式不正确, 请上传${this.fileType.join("/")}格式文件!`
          );
          return false;
        }
      }
      // 校检文件大小
      if (this.fileSize) {
        const isLt = file.size / 1024 / 1024 < this.fileSize;
        if (!isLt) {
          this.$modal.msgError(`上传文件大小不能超过 ${this.fileSize} MB!`);
          return false;
        }
      }
      this.$modal.loading("正在上传文件，请稍候...");
      this.number++;
      return true;
    },
    // 文件个数超出
    handleExceed() {
      this.$modal.msgError(`上传文件数量不能超过 ${this.limit} 个!`);
    },
    // 上传失败
    handleUploadError(err) {
      this.$modal.msgError("上传文件失败，请重试");
      this.$modal.closeLoading();
    },
    // 上传成功回调
    handleUploadSuccess(res, file) {
      if (res.code === 200) {
        // 根据文件类型设置缩略图
        const thumbUrl = this.getFileThumbnailUrl(
          res.data.contentType,
          res.data.thumbUrl
        );
        const temp = {
          ext: res.data.ext,
          thumbUrl: thumbUrl,
          id: res.data.id,
          remoteUrl: res.data.url,
          contentType: res.data.contentType,
          name: res.data.originalFilename,
          url: res.data.url,
          ownerId: res.data.ownerId,
          uid: file.uid,
        };
        this.uploadList.push(temp);
        addUploadRecord({
          fileSubId: this.fileSubId,
          bizId: this.bizId,
          fileName: res.data.originalFilename,
          fileId: res.data.id,
          ownerId: res.data.ownerId,
        }).then();
        this.uploadedSuccessfully();
      } else {
        this.number--;
        this.$modal.closeLoading();
        this.$modal.msgError(res.msg);
        this.$refs.fileUpload.handleRemove(file);
        this.uploadedSuccessfully();
      }
    },
    // 上传结束处理
    uploadedSuccessfully() {
      if (this.number > 0 && this.uploadList.length === this.number) {
        this.fileList = this.fileList.concat(this.uploadList);
        this.uploadList = [];
        this.number = 0;
        this.$emit("input", this.formatValue(this.fileList));
        this.$modal.closeLoading();
      }
    },
    // 获取文件名称
    getFileName(name) {
      // 如果是url那么取最后的名字 如果不是直接返回
      if (name.lastIndexOf("/") > -1) {
        return name.slice(name.lastIndexOf("/") + 1);
      } else {
        return name;
      }
    },
    // 格式化返回对象
    formatValue(list) {
      return list.filter((item) => item.ownerId).map((el) => el.ownerId);
    },
    async confirmAndRemove(file) {
      try {
        await this.beforeRemove(file);
        this.handleRemove(file);
      } catch (error) {
        // 用户取消了操作
        console.log("用户取消了删除操作");
      }
    },
    beforeRemove(file) {
      return this.$confirm(`确定移除 ${file.name}？`);
    },
    handleRemove(file) {
      if (!file.id) return;
      const findex = this.fileList.map((f) => f.id).indexOf(file.id);
      if (findex > -1) {
        removeFile(file.id).then((res) => {
          if (res.code === 200) {
            this.fileList.splice(findex, 1);
            this.$emit("input", this.formatValue(this.fileList));
            this.$message({
              type: "success",
              message: "删除成功!",
            });
          }
        });
      }
    },
    handlePictureCardPreview(file) {
      this.contentType = file.contentType;
      this.dialogUrl = file.url;
      this.dialogVisible = true;
      this.dialogFile = file;
    },
    handleDownload(file) {
      const url = file.url || (file.response && file.response.url);
      if (url) {
        fetch(url)
          .then((response) => response.blob())
          .then((blob) => {
            const link = document.createElement("a");
            link.href = URL.createObjectURL(blob);
            link.download = this.getFileName(file.name) || "download";
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
          })
          .catch(() => {
            this.$message.error("文件无法下载，未找到文件的URL");
          });
      } else {
        this.$message.error("文件无法下载，未找到文件的URL");
      }
    },
    // 根据文件类型获取缩略图
    getFileThumbnailUrl(contentType, thumbUrl) {
      if (contentType.startsWith("image/")) {
        // 如果是图片类型，直接返回图片 URL
        return thumbUrl;
      } else {
        switch (contentType) {
          case "application/pdf":
            return require("@/assets/images/Ipdf.png");
          case "application/msword":
          case "application/vnd.openxmlformats-officedocument.wordprocessingml.document":
            return require("@/assets/images/Iword.png");
          case "application/vnd.ms-excel":
          case "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet":
            return require("@/assets/images/Iexcel.png");
          case "application/vnd.ms-powerpoint":
          case "application/vnd.openxmlformats-officedocument.presentationml.presentation":
            return require("@/assets/images/Ippt.png");
          case "application/zip":
          case "application/x-zip-compressed":
          case "application/x-rar-compressed":
          case "application/x-7z-compressed":
          case "application/x-tar":
          case "application/gzip":
            return require("@/assets/images/Izip.png");
          default:
            return require("@/assets/images/Iother.png"); // 默认文件图标
        }
      }
    },
  },
};
</script>

<style scoped lang="scss">
.upload-file-uploader {
  margin-bottom: 5px;
}
.fixed-dialog {
  position: fixed !important;
  top: 50% !important;
  left: 50% !important;
  transform: translate(-50%, -50%) !important;
  z-index: 9999 !important; /* 确保弹窗在所有元素之上 */
}
</style>
