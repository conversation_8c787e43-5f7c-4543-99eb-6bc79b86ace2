<template>
  <div :class="wide ? 'container-wide' : 'container'">
    <div :class="wide ? 'title-wide' : 'title'">{{ title }}</div>
    <div :class="wide ? 'body-wide' : 'body'">
      <slot></slot>
    </div>
  </div>
</template>

<script>
export default {
  name: 'ItemCard',
  props: {
    title: {
      type: String,
      default: ''
    },
    wide: {
      type: Boolean,
      default: false
    }
  },
  components: {},
  data() {
    return {}
  },
  created() { },
  methods: {},
  computed: {},
  watch: {},
}
</script>

<style lang="scss" scoped>
.container {
  position: relative;
  width: 100%;
  height: 33%;

  .title {
    position: relative;
    color: #ffffff;
    font-size: 1.8vh;
    font-weight: bold;
    letter-spacing: 0.05vw;
    z-index: 2000;
  }

  .body {
    position: absolute;
    background: url("~@/assets/monitoringSystem/fullBackground.png") no-repeat;
    background-size: 100% 100%;
    width: 100%;
    height: 95%;
    bottom: 0;
    z-index: 1000;
  }
}

.container-wide {
  position: absolute;
  z-index: 999;
  bottom: 0;
  width: 100%;
  height: 33.5%;

  .title-wide {
    color: #fff;
    font-size: 1.8vh;
    font-weight: 700;
    font-family: Microsoft YaHei;
    letter-spacing: 0.05vw;
    position: relative;
    width: 50%;
    height: 10%;
    background: url("~@/assets/monitoringSystem/singleBridgeHeader.png") no-repeat;
    background-size: 100% 50%;
    background-position: 0 110%;
  }

  .body-wide {
    position: absolute;
    background: url("~@/assets/monitoringSystem/singleBridgeLongBody.png") no-repeat;
    background-size: 100% 100%;
    width: 100%;
    height: 88.8%;
    bottom: 0;
    z-index: 1000;
  }
}
</style>