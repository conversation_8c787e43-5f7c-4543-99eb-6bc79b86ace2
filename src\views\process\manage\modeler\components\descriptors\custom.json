{"name": "Custom", "prefix": "custom", "uri": "http://custom", "xml": {"tagAlias": "lowerCase"}, "associations": [], "types": [{"name": "assignee", "properties": [{"name": "activiti:assignee", "isAttr": true, "type": "String"}]}, {"name": "candidateUsers", "properties": [{"name": "activiti:candidateUsers", "isAttr": true, "type": "String"}]}, {"name": "candidateGroups", "properties": [{"name": "activiti:candidateGroups", "isAttr": true, "type": "String"}]}, {"name": "condition expression", "properties": [{"name": "activiti:conditionExpression", "isAttr": true, "type": "String"}]}]}