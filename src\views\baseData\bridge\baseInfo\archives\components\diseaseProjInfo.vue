<template>
  <div class="right-box" :class="oneMap ? 'one-map' : ''">
    <div class="right-box-head">
      <h5>养护工程</h5>
    </div>
    <div
      style="height: calc(100% - 53px); width: 100%"
      class="container-view-list"
    >
      <el-table
        ref="table"
        height="100%"
        :header-cell-style="{ height: '36px' }"
        border
        v-loading="loading"
        :data="staticList"
      >
        <el-table-column label="序号" type="index" width="50" align="center">
          <template v-slot="scope">
            {{
              scope.$index +
              (queryParams.pageNum - 1) * queryParams.pageSize +
              1
            }}
          </template>
        </el-table-column>
        <el-table-column
          label="管养单位名称"
          align="center"
          prop="domainName"
          min-width="140"
          show-overflow-tooltip
        />
        <el-table-column
          label="管养单位ID"
          align="center"
          prop="domainId"
          min-width="140"
          show-overflow-tooltip
        />
        <el-table-column
          label="年度"
          align="center"
          prop="year"
          min-width="140"
          show-overflow-tooltip
        />
        <el-table-column
          label="项目名称"
          align="center"
          prop="name"
          min-width="140"
          show-overflow-tooltip
        />
        <el-table-column
          label="项目编码"
          align="center"
          prop="code"
          min-width="140"
          show-overflow-tooltip
        />
        <el-table-column
          label="路段名称"
          align="center"
          prop="maiSecName"
          min-width="140"
          show-overflow-tooltip
        />
        <el-table-column
          label="桩号范围"
          align="center"
          prop="mileRange"
          min-width="140"
          show-overflow-tooltip
        />
        <el-table-column
          label="工程费用（万元）"
          align="center"
          prop="sumFund"
          min-width="140"
          show-overflow-tooltip
        />
        <el-table-column
          label="工程分类"
          align="center"
          prop="projectType"
          min-width="140"
          show-overflow-tooltip
        >
          <template slot-scope="scope">
            <dict-tag :options="dict.type.project_type" :value="scope.row.projectType" />
          </template>
        </el-table-column>
        
        <el-table-column
          label="所属工程类别"
          align="center"
          prop="mType"
          min-width="140"
          show-overflow-tooltip
          >
          <template slot-scope="scope">
            <dict-tag :options="dict.type.affiliation_project_type" :value="scope.row.mType" />
          </template>
        </el-table-column>
        <el-table-column
          label="构造物名称"
          align="center"
          prop="structureName"
          min-width="140"
          show-overflow-tooltip
        />
        <el-table-column
          label="缺陷责任期（月）"
          align="center"
          prop="defLiaPer"
          min-width="140"
          show-overflow-tooltip
        />
        <el-table-column
          label="预计工期（天）"
          align="center"
          prop="expectDuration"
          min-width="140"
          show-overflow-tooltip
        />
        <el-table-column
          label="预计开始时间"
          align="center"
          prop="beginDate"
          min-width="140"
          show-overflow-tooltip
        />
        <el-table-column
          label="预计结束时间"
          align="center"
          prop="endDate"
          min-width="140"
          show-overflow-tooltip
        />
        <el-table-column
          label="状态"
          align="center"
          prop="status"
          min-width="140"
          show-overflow-tooltip
          >
          <template slot-scope="scope">
            <dict-tag :options="dict.type.maintenance_project_type" :value="scope.row.status" />
            </template>
        </el-table-column>
        <el-table-column
          label="操作人"
          align="center"
          prop="createBy"
          min-width="140"
          show-overflow-tooltip
        />
        <el-table-column
          label="上报人"
          align="center"
          prop="updateBy"
          min-width="140"
          show-overflow-tooltip
        />
      </el-table>

      <pagination
        :total="total"
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
        :pageSizes="[10, 20, 30, 50, 100, 1000]"
        @pagination="getList"
      />
    </div>
  </div>
</template>
  
  <script>
import { getBridgeAssetDiseaseProjInfo } from "@/api/baseData/bridge/baseInfo/index";

import { getFile } from "@/api/file";

import ImportData from "@/views/baseData/components/importData/index.vue";
import { listAllDiseases } from "@/api/patrol/diseases";

export default {
  name: "periodic-baseInfo",
  components: { ImportData },
  inject: ["oneMap"],
  dicts: ["base_data_yes_no", "sys_route_direction", "lane",'project_type','maintenance_project_type','affiliation_project_type'],
  props: {
    id: {
      type: String || Number,
      default: "",
    },
    assetId: {
      type: String || Number,
      default: "",
    },
    assetName: {
      type: String,
      default: "",
    },
  },
  data() {
    return {
      loading: true,
      ids: [],
      single: true,
      multiple: true,

      showImportAdd: false,
      total: 0,
      staticList: null,
      routeCode: [],
      queryParams: {
        pageNum: 1,
        pageSize: 10,
      },
      dateRange: [],
      ownerId: "",
      disType: [],
    };
  },
  watch: {
    assetName: {
      handler: function (val) {
        if(val){
          this.getList();
        }
      },
      immediate: true,
    },
  },
  created() {
   
    this.getDisType();
    
  },
  methods: {
    // 获取表格数据
    getList() {
      this.loading = true;

      getBridgeAssetDiseaseProjInfo({
        assetName: this.assetName,
      }).then(async (response) => {
        this.staticList = response.rows;
        this.total = response.total;
        this.loading = false;

        let dataList = JSON.parse(JSON.stringify(response.rows));

        this.staticList = dataList.map((item) => {
          this.disType.forEach((resItem) => {
            if (resItem.dictValue === item.disType)
              item.disTypeName =resItem.dictLabel;
          });
          return item;
        });
      });
    },
    async handlePreview(row) {
      this.ownerId = row.reportPath;
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id);
      this.single = selection.length != 1;
      this.multiple = !selection.length;
    },
    // 勾选高亮
    rowStyle({ row, rowIndex }) {
      if (this.ids.includes(row.id)) {
        return { "background-color": "#b7daff", color: "#333" };
      } else {
        return { "background-color": "#fff", color: "#333" };
      }
    },
    // 搜索按钮
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    // 重置按钮
    resetQuery() {
      this.routeCode = [];

      this.dateRange = [];
      this.queryParams = {};
      this.handleQuery();
    },

    handleDateChange() {
      this.queryParams.constructionStartDate = this.dateRange[0];
      this.queryParams.constructionEndDate = this.dateRange[1];
    },
    // 表格点击勾选
    handleRowClick(row) {
      row.isSelected = !row.isSelected;
      this.$refs.table.toggleRowSelection(row);
    },
    getDisType() {
      listAllDiseases().then((res) => {
        this.disType = res.data.map((item) => {
          return {
            dictLabel: item.diseaseName,
            dictValue: item.id,
          };
        });
        
      });
    },
  },
};
</script>
  
  <style lang="scss" scoped>
@import "@/assets/styles/common.scss";
::v-deep {
  .el-table__row:hover td {
    background: #b7daff !important;
  }
}
.container-view-list {
  // padding: 0;
}
.right-box {
  width: 100%;
  height: 100%;
  background-color: #fff;
  border-radius: 10px;
  box-shadow: 0px 0px 10px 0px rgba(0, 0, 0, 0.2);
  position: relative;
  .right-box-head {
    width: 100%;
    height: 52px;
    background: #409eff;
    border-radius: 10px 10px 0 0;
    display: flex;
    justify-content: center;
    align-items: center;
    h5 {
      font-family: Microsoft YaHei, Microsoft YaHei;
      font-weight: 700;
      font-size: 14px;
      color: #ffffff;
    }
  }
}
</style>
  