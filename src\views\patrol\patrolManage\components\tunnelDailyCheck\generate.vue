<template>
  <el-dialog
    v-loading="loading"
    element-loading-text="获取数据中..."
    :visible.sync="dialogVisible"
    :title="`批量生成${assetTypeMap[type]}巡查记录`"
    width="85%"
    append-to-body
    :close-on-click-modal="false"
    @before-close="handleClose"
    class="asset-check-generate-dialog"
  >
    <el-steps
      :active="activeStep"
      finish-status="success"
      simple
      style="margin-bottom: 20px"
    >
      <el-step title="选择资产" icon="el-icon-s-operation"></el-step>
      <el-step title="设置时间" icon="el-icon-date"></el-step>
      <el-step title="编辑信息" icon="el-icon-edit-outline"></el-step>
      <el-step title="确认生成" icon="el-icon-document-checked"></el-step>
      <el-step title="完成" icon="el-icon-circle-check"></el-step>
    </el-steps>

    <!-- 步骤1: 选择资产 -->
    <div v-show="activeStep === 0" class="step-content">
      <component
        :is="assetTableComponent"
        :key="componentKey"
        v-bind="$attrs"
        v-on="$listeners"
        v-model="selectedAssets"
        :type="type"
        :generate="true"
        ref="assetTable"
        @selection-change="handleAssetSelect"
      >
      </component>
    </div>

    <!-- 步骤2: 设置时间 -->
    <div v-show="activeStep === 1" style="text-align: center; padding: 20px">
      <!-- 添加选中记录数提示 -->
      <el-alert type="info" :closable="false" style="margin-bottom: 20px">
        <div>
          请选择巡查时间范围（包括起止当天，如只选择一天，点击两次相同日期）
        </div>
        <div style="margin-top: 5px">
          已选择 <strong>{{ generateParams.total }}</strong> 个巡查记录
        </div>
      </el-alert>

      <el-form
        :model="timeForm"
        ref="timeForm"
        :rules="timeRules"
        label-width="100px"
      >
        <el-form-item label="起止时间" prop="dateRange">
          <el-date-picker
            v-model="timeForm.dateRange"
            ref="timePicker"
            type="daterange"
            @change="chooseDate"
            @blur="chooseDate"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            value-format="yyyy-MM-dd"
            popper-class="generate-time-picker-popper"
            style="width: 500px; margin: 0 auto; height: 70px"
          >
          </el-date-picker>
        </el-form-item>
      </el-form>
    </div>

    <!-- 步骤3: 编辑信息 -->
    <div v-show="activeStep === 2">
      <el-alert
        title="请确认并编辑以下巡查信息"
        type="info"
        :closable="false"
        style="margin-bottom: 20px"
      >
        <template slot="title">
          将为选中的
          <strong>{{ generateParams.total }}</strong> 个资产生成巡查记录，
          您可以编辑以下巡查信息
        </template>
      </el-alert>

      <el-table
        :data="inspectionList"
        border
        style="width: 100%"
        max-height="400px"
      >
        <el-table-column type="index" label="序号" width="50"></el-table-column>
        <el-table-column
          label="养护路线名称"
          prop="maintenanceSectionName"
          width="120"
        ></el-table-column>
        <el-table-column
          label="管养单位名称"
          prop="maintenanceUnitName"
          width="120"
        ></el-table-column>
        <el-table-column label="巡查类型" width="150">
          <template slot-scope="scope">
            <el-select
              v-model="scope.row.patrolType"
              placeholder="请选择巡查类型"
            >
              <el-option
                v-for="dict in dict.type.patrol_inspection_type"
                :key="dict.value"
                :label="dict.label"
                :value="parseInt(dict.value)"
              ></el-option>
            </el-select>
          </template>
        </el-table-column>
        <el-table-column label="巡查方向" width="150">
          <template slot-scope="scope">
            <el-select
              v-model="scope.row.direction"
              placeholder="请选择巡查方向"
            >
              <el-option
                v-for="dict in dict.type.patrol_inspection_direction"
                :key="dict.value"
                :label="dict.label"
                :value="parseInt(dict.value)"
              ></el-option>
            </el-select>
          </template>
        </el-table-column>
        <el-table-column label="巡查里程" width="150">
          <template slot-scope="scope">
            <el-input
              v-model="scope.row.patrolMileage"
              placeholder="请输入巡查里程"
            />
          </template>
        </el-table-column>
        <el-table-column label="车牌号" width="150">
          <template slot-scope="scope">
            <el-input v-model="scope.row.carNum" placeholder="请输入车牌号" />
          </template>
        </el-table-column>
        <el-table-column label="采集时间" width="200">
          <template slot-scope="scope">
            <el-date-picker
              v-model="scope.row.collectTime"
              type="date"
              value-format="yyyy-MM-dd"
              placeholder="选择日期"
              style="width: 100%"
              :clearable="false"
              :disabled="scope.row.dateList && scope.row.dateList.length === 1"
              @change="handleCollectTimeChange(scope.row)"
              :picker-options="{
                disabledDate: (time) => {
                  // 使用本地时间格式化日期
                  const date = new Date(time)
                    .toLocaleDateString('zh-CN', {
                      year: 'numeric',
                      month: '2-digit',
                      day: '2-digit',
                    })
                    .split('/')
                    .join('-');
                  // 只有在dateList中的日期才可选
                  return !scope.row.dateList.includes(date);
                },
              }"
            >
            </el-date-picker>
          </template>
        </el-table-column>
        <el-table-column label="巡查开始时间" width="200">
          <template slot-scope="scope">
            <el-date-picker
              v-model="scope.row.startTime"
              type="date"
              value-format="yyyy-MM-dd"
              placeholder="选择日期"
              disabled
              style="width: 100%"
            />
          </template>
        </el-table-column>
        <el-table-column label="巡查结束时间" width="200">
          <template slot-scope="scope">
            <el-date-picker
              v-model="scope.row.endTime"
              type="date"
              value-format="yyyy-MM-dd"
              placeholder="选择日期"
              disabled
              style="width: 100%"
            >
            </el-date-picker>
          </template>
        </el-table-column>
        <el-table-column label="上报日期" width="200">
          <template slot-scope="scope">
            <el-date-picker
              v-model="scope.row.reportedTime"
              type="date"
              value-format="yyyy-MM-dd"
              placeholder="选择日期"
              disabled
              style="width: 100%"
            >
            </el-date-picker>
          </template>
        </el-table-column>
        <el-table-column label="巡查人员" width="200">
          <template slot-scope="scope">
            <el-cascader
              v-model="scope.row.userIds"
              :options="deptUserOptions"
              :props="propSelect"
              :show-all-levels="false"
              filterable
              clearable
              style="width: 100%"
            >
            </el-cascader>
          </template>
        </el-table-column>
        <el-table-column label="天气" width="150">
          <template slot-scope="scope">
            <el-input v-model="scope.row.weather" placeholder="请输入天气" />
          </template>
        </el-table-column>
        <el-table-column label="巡查内容" width="450">
          <template slot-scope="scope">
            <el-input
              v-model="scope.row.content"
              type="textarea"
              :rows="3"
              placeholder="请输入巡查内容"
            >
            </el-input>
          </template>
        </el-table-column>
        <el-table-column label="备注" width="450">
          <template slot-scope="scope">
            <el-input
              v-model="scope.row.remark"
              type="textarea"
              :rows="3"
              placeholder="请输入备注"
            >
            </el-input>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 步骤4: 确认信息 -->
    <div v-show="activeStep === 3">
      <el-alert
        title="请确认以下生成信息"
        type="info"
        :closable="false"
        style="margin-bottom: 20px"
      >
        <template slot="title">
          将为选中的
          <strong>{{ generateParams.total - removedIds.length }}</strong> 个资产
          <template v-if="removedIds.length > 0">
            (总数为{{ generateParams.total }} 移除{{ removedIds.length }})
          </template>
          在
          <strong>{{ timeForm.dateRange[0] }}</strong> 至
          <strong>{{ timeForm.dateRange[1] }}</strong> 期间生巡查记录
        </template>
      </el-alert>

      <el-table
        :data="tableDataList"
        border
        style="width: 100%"
        max-height="400px"
      >
        <el-table-column type="index" label="序号" width="50">
          <template slot-scope="scope">
            <span :class="{ 'row-removed': isRemoved(scope.row) }">
              {{
                scope.$index +
                (generateParams.pageNum - 1) * generateParams.pageSize +
                1
              }}
            </span>
          </template>
        </el-table-column>
        <el-table-column
          prop="baseData.assetName"
          :label="`${assetTypeMap[type]}名称`"
        >
          <template slot-scope="scope">
            <span :class="{ 'row-removed': isRemoved(scope.row) }">
              {{ scope.row.baseData.assetName }}
            </span>
          </template>
        </el-table-column>
        <el-table-column
          prop="baseData.assetCode"
          :label="`${assetTypeMap[type]}编码`"
        >
          <template slot-scope="scope">
            <span :class="{ 'row-removed': isRemoved(scope.row) }">
              {{ scope.row.baseData.assetCode }}
            </span>
          </template>
        </el-table-column>
        <el-table-column
          prop="baseData.maintenanceSectionName"
          label="养护路段"
        >
          <template slot-scope="scope">
            <span :class="{ 'row-removed': isRemoved(scope.row) }">
              {{ scope.row.baseData.maintenanceSectionName }}
            </span>
          </template>
        </el-table-column>
        <el-table-column
          prop="baseData.managementMaintenanceName"
          label="管养单位"
        >
          <template slot-scope="scope">
            <span :class="{ 'row-removed': isRemoved(scope.row) }">
              {{ scope.row.baseData.managementMaintenanceName }}
            </span>
          </template>
        </el-table-column>
        <el-table-column
          prop="baseData.managementMaintenanceBranchName"
          label="管养分处"
        >
          <template slot-scope="scope">
            <span :class="{ 'row-removed': isRemoved(scope.row) }">
              {{ scope.row.baseData.managementMaintenanceBranchName }}
            </span>
          </template>
        </el-table-column>
        <el-table-column prop="baseData.routeName" label="路线名称">
          <template slot-scope="scope">
            <span :class="{ 'row-removed': isRemoved(scope.row) }">
              {{ scope.row.baseData.routeName }}
            </span>
          </template>
        </el-table-column>
        <el-table-column prop="baseData.routeCode" label="路线编码">
          <template slot-scope="scope">
            <span :class="{ 'row-removed': isRemoved(scope.row) }">
              {{ scope.row.baseData.routeCode }}
            </span>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="100" fixed="right">
          <template slot-scope="scope">
            <el-button
              :type="isRemoved(scope.row) ? 'warning' : 'danger'"
              size="mini"
              @click="handleToggleRemove(scope.row)"
            >
              {{ isRemoved(scope.row) ? '取消移除' : '移除' }}
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      <pagination
        :total="total"
        :page.sync="generateParams.pageNum"
        :limit.sync="generateParams.pageSize"
        @pagination="getList"
      />
    </div>

    <!-- 步骤5: 生成进度 -->
    <div v-show="activeStep === 4" style="text-align: center; padding: 20px">
      <!-- 添加总体进度条 -->
      <el-progress
        :text-inside="true"
        :stroke-width="20"
        :percentage="generateProgress"
        :status="progressStatus"
        style="margin-bottom: 20px"
      ></el-progress>

      <div v-if="generating">
        <el-progress
          type="circle"
          :percentage="generateProgress"
          :status="progressStatus"
        ></el-progress>
        <p style="margin-top: 20px">正在生成巡查记录，请稍候...</p>
      </div>
      <div v-else>
        <el-result
          icon="success"
          title="生成完成"
          :sub-title="`成功生成 ${generatedCount} 条巡查记录`"
        >
        </el-result>
      </div>
      <el-table
        :data="generateList"
        border
        style="width: 100%"
        max-height="400px"
      >
        <el-table-column type="index" label="序号" width="50">
          <template slot-scope="scope">
            <template v-if="processedIds.includes(scope.row.baseData.id)">
              <el-icon
                class="el-icon-check"
                style="color: #67c23a; font-weight: bold"
              ></el-icon>
            </template>
            <template v-else>
              <span>{{ scope.$index + 1 }}</span>
            </template>
          </template>
        </el-table-column>
        <el-table-column
          prop="baseData.assetName"
          :label="`${assetTypeMap[type]}名称`"
        >
        </el-table-column>
        <el-table-column
          prop="baseData.assetCode"
          :label="`${assetTypeMap[type]}编码`"
        >
        </el-table-column>
        <el-table-column
          prop="baseData.maintenanceSectionName"
          label="养护路段"
        >
        </el-table-column>
        <el-table-column
          prop="baseData.managementMaintenanceName"
          label="管养单位"
        >
        </el-table-column>
        <el-table-column
          prop="baseData.managementMaintenanceBranchName"
          label="管养分处"
        >
        </el-table-column>
        <el-table-column prop="baseData.routeName" label="路线名称">
        </el-table-column>
        <el-table-column prop="baseData.routeCode" label="路线编码">
        </el-table-column>
      </el-table>
    </div>

    <!-- 底部按钮 -->
    <div slot="footer" class="dialog-footer">
      <el-button @click="handleClose">取 消</el-button>
      <el-button v-if="showPrevButton" @click="handlePrev">上一步</el-button>
      <el-button
        type="primary"
        @click="handleNext"
        :loading="generating"
        :disabled="buttonDisabled"
        >{{ nextButtonText }}</el-button
      >
    </div>
  </el-dialog>
</template>

<script>
import {
  getTotalCount,
  setGenerateLog,
  listByDistance,
  setGenerateCheck,
  getProgress,
} from '@/api/patrol/assetCheck';
import BridgeTableDialog from '@/views/patrol/assetCheck/assetTableDialog/bridge/BridgeTableDialog.vue';
import CulvertTableDialog from '@/views/patrol/assetCheck/assetTableDialog/culvert/CulvertTableDialog.vue';
import TunnelTableDialog from '@/views/patrol/assetCheck/assetTableDialog/tunnel/TunnelTableDialog.vue';
import { generateAssetChecks } from '@/api/patrol/assetCheck';
import { getTreeStruct } from '@/api/tmpl';
import {
  batchAddInspectionLogs,
  batchDeleteInspectionLogs,
} from '@/api/patrol/inspectionLogs';
export default {
  name: 'AssetCheckGenerate',
  dicts: ['patrol_inspection_type', 'patrol_inspection_direction'],
  components: {
    BridgeTableDialog,
    CulvertTableDialog,
    TunnelTableDialog,
  },
  props: {
    type: {
      type: String,
      required: true,
    },
    visible: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      total: 0,
      generateParams: {
        ids: null,
        assetCode: null,
        assetName: null,
        checkTimeHas: null,
        isCheck: null,
        maintenanceSectionId: null,
        managementMaintenanceIds: null,
        routeCodes: null,
        type: null,
        total: null,
        pageNum: 1,
        pageSize: 10,
        dataRule: true,
      },
      tableDataList: [],
      generateList: [],
      dialogVisible: false,
      activeStep: 0,
      selectedAssets: [],
      timeForm: {
        dateRange: [],
      },
      timeRules: {
        dateRange: [
          {
            type: 'array',
            required: true,
            message: '请选择起止时间',
            trigger: 'change',
          },
        ],
      },
      generating: false,
      generateProgress: 0,
      generatedCount: 0,
      assetTypeMap: {
        1: '桥梁',
        2: '桥梁',
        3: '涵洞',
        4: '涵洞',
        5: '隧道',
        6: '隧道',
      },
      loading: false,
      inspectionList: [],
      inspectionRules: {
        patrolType: [
          { required: true, message: '请选择巡查类型', trigger: 'change' },
        ],
        direction: [
          { required: true, message: '请选择巡查方向', trigger: 'change' },
        ],
        carNum: [{ required: true, message: '请输入车牌号', trigger: 'blur' }],
      },
      propSelect: {
        multiple: true, // 是否多选
        value: 'id',
      },
      props: {
        multiple: true,
        value: 'userId',
        label: 'nickName',
        children: 'children',
      },
      deptUserOptions: [],
      removedIds: [], // 存储被移除的id列表
      processId: null,
      progressTimer: null, // 添加定时器变量
      processedIds: [], // 存储已处理的ID
      nowDateList: [], // 存储当前日期列表
      componentKey: 0,
    };
  },
  computed: {
    assetTableComponent() {
      const componentMap = {
        1: 'BridgeTableDialog',
        2: 'BridgeTableDialog',
        3: 'CulvertTableDialog',
        4: 'CulvertTableDialog',
        5: 'TunnelTableDialog',
        6: 'TunnelTableDialog',
      };
      return componentMap[this.type];
    },
    nextButtonText() {
      const textMap = {
        0: '下一步',
        1: '下一步',
        2: '下一步',
        3: '开始生成',
        4: this.generating ? '生成中...' : '完成',
      };
      return textMap[this.activeStep];
    },
    buttonDisabled() {
      //   if (this.activeStep === 0) {
      //     return this.selectedAssets.length === 0;
      //   }
      if (this.activeStep === 1) {
        return !this.timeForm.dateRange || this.timeForm.dateRange.length !== 2;
      }
      return false;
    },
    progressStatus() {
      if (this.generateProgress === 100) {
        return 'success';
      } else if (this.generating) {
        return ''; // 进行中使用默认状态
      } else {
        return 'exception'; // 出错状态
      }
    },
    showPrevButton() {
      // 在第一步(0)和最后完成步骤(nextButtonText为"完成"时)不显示上一步按钮
      return this.activeStep > 0 && this.activeStep < 4;
    },
  },
  watch: {
    visible(val) {
      this.dialogVisible = val;
    },
    dialogVisible(val) {
      this.$emit('update:visible', val);
    },
  },
  created() {
    this.getDeptTreeDef();
  },
  methods: {
    handleAssetSelect(selection) {
      this.selectedAssets = selection;
    },
    handleCollectTimeChange(row) {
      // 当采集时间改变时,同步更新其他三个时间
      row.startTime = row.collectTime;
      row.endTime = row.collectTime;
      row.reportedTime = row.collectTime;
    },
    async handleNext() {
      if (this.activeStep === 0) {
        const currentParams = this.$refs.assetTable.getCurrentQueryParams();
        if (currentParams.ids && currentParams.ids.length === 0) {
          this.$message.warning('请选择要生成巡查记录的资产');
          return;
        }

        this.generateParams = {
          ...this.generateParams,
          ...currentParams,
        };

        // 如 total 为 null，需要获取总数
        if (this.generateParams.total === null) {
          try {
            this.loading = true; // 开启加载状态
            const response = await getTotalCount({
              ...this.generateParams,
              pageNum: null,
              pageSize: null,
            }); // 获取API返回值
            // 使用API返回的total更新
            this.generateParams = {
              ...this.generateParams,
              total: response.data,
            };
          } catch (error) {
            this.$message.error('获取数据数量失败', error);
            return;
          } finally {
            this.loading = false; // 关闭加载状态
          }
        }

        // 检查total是否为0
        if (!this.generateParams.total || this.generateParams.total === 0) {
          this.$message.warning('请选择要生成巡查记录的资产');
          return;
        }

        this.loading = false; // 关闭加载状态
        this.activeStep++;
        this.$nextTick(() => {
          if (this.$refs.timePicker) {
            this.handleDatePickerFocus();
          }
        });
      } else if (this.activeStep === 1) {
        this.$refs.timeForm.validate((valid) => {
          if (valid) {
            // 强制关闭日期选择器弹出框
            this.handleDatePickerBlur();
            this.activeStep++;
          } else {
            return;
          }
        });
        try {
          this.loading = true; // 开启加载状态
          // 根据选择的时间，生成nowDateList，根据起止时间生成连续的日期列表，格式为yyyy-MM-dd
          const startDate = new Date(this.timeForm.dateRange[0]);
          const endDate = new Date(this.timeForm.dateRange[1]);
          const nowDate = startDate;
          this.nowDateList = [];
          while (nowDate <= endDate) {
            this.nowDateList.push(nowDate.toISOString().split('T')[0]);
            nowDate.setDate(nowDate.getDate() + 1);
          }
          const response = await setGenerateLog({
            ...this.generateParams,
            pageNum: null,
            pageSize: null,
            nowDateList: this.nowDateList,
          }); // 获取API返回值
          // 使用API返回的total更新
          response.data.forEach((item) => {
            // 为每条记录创建新的userIds数组
            item.userIds = item.userIds.map((userId) =>
              this.changeCascader(userId, this.deptUserOptions)
            );
          });
          this.inspectionList = response.data;
          if (this.inspectionList.length == 0) {
            this.activeStep++;
            this.getList();
          }
        } catch (error) {
          this.$message.error('生成巡查日志失败', error);
          return;
        } finally {
          this.loading = false; // 关闭加载状态
        }
      } else if (this.activeStep === 2) {
        // 验证表单
        // 验证每条记录的必填项
        // const isValid = this.inspectionList.every((item) => {
        //   return (
        //     item.inspectionDate && item.userIdList && item.userIdList.length
        //   );
        // });

        // if (!isValid) {
        //   this.$message.warning('请完善所有巡查记录的必填信息');
        //   return;
        // }
        if (this.inspectionList.length > 0) {
          try {
            this.loading = true; // 开启加载状态

            this.inspectionList.forEach((item) => {
              // 使用map直接获取每个路径的最后一个节点ID,然后展平成一维数组
              item.userIds = item.userIds.map((path) => path[path.length - 1]);
            });
            const isBatchAdd = await batchAddInspectionLogs(
              this.inspectionList
            );
            if (isBatchAdd) {
              this.activeStep++;
            }
          } catch (error) {
            this.$message.error('生成巡查记录失败', error);
            return;
          } finally {
            this.loading = false; // 关闭加载状态
          }
        } else {
          this.activeStep++;
        }
        this.getList();
      } else if (this.activeStep === 3) {
        // 弹出确认框
        this.$confirm('开始生成后将无法返回上一步，是否继续?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        })
          .then(async () => {
            this.activeStep++;
            await this.generateCheck();
            this.loading = true;
            listByDistance({
              ...this.generateParams,
              excludeIds: this.removedIds,
              isCheck: null,
              pageNum: 1,
              pageSize: 100,
            }).then((response) => {
              this.generateList = response.rows;
              this.loading = false;
            });
            setTimeout(() => {
              listByDistance({
                ...this.generateParams,
                type: this.type,
                isCheck: null,
              }).then((response) => {
                this.tableDataList = response.rows;
                this.total = response.total;
              });
            }, 1000); // 延迟500毫秒
          })
          .catch(() => {
            return;
          });
      } else if (this.activeStep === 4) {
        this.handleClose();
      } else {
        this.activeStep++;
      }
    },
    /** 查询部门-用户下拉树结构 */
    getDeptTreeDef() {
      getTreeStruct({ types: 111 }).then((response) => {
        this.deptUserOptions = response.data;
      });
    },
    getList() {
      this.loading = true;
      listByDistance({
        ...this.generateParams,
        type: this.type,
        isCheck: null,
      }).then((response) => {
        this.tableDataList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    chooseDate() {
      this.$refs.timePicker.focus(); // 选择日期后依旧触发焦点
    },
    handleClose() {
      if (this.generating) {
        this.$confirm('正在生成中，确定要取消吗？')
          .then(() => {
            if (this.progressTimer) {
              clearInterval(this.progressTimer);
              this.progressTimer = null;
            }
            this.dialogVisible = false;
          })
          .catch(() => {});
      } else {
        this.reset(); // 重置所有状态
        this.dialogVisible = false;
        if (this.activeStep === 3) {
          const ids = this.inspectionList.map((item) => item.id);
          if (ids.length > 0) {
            batchDeleteInspectionLogs(ids);
          }
        }
      }
    },
    // 返回的一维数组转成多维数组 key：对比的id ， arrData：原始数组
    changeCascader(key, arrData) {
      let arr = [];
      let returnArr = []; // 存放结果的数组
      let depth = 0; // 定义全局层级
      // 定义递归函数
      function childrenEach(childrenData, depthN) {
        for (var j = 0; j < childrenData.length; j++) {
          depth = depthN; // 将执行的层级赋值 到 全局层级
          arr[depthN] = childrenData[j].id;
          if (childrenData[j].id == key) {
            returnArr = arr.slice(0, depthN + 1); //将目前匹配的数组，截断并保存到结果数组，
            break;
          } else {
            if (childrenData[j].children) {
              depth++;
              childrenEach(childrenData[j].children, depth);
            }
          }
        }
        return returnArr;
      }
      return childrenEach(arrData, depth);
    },
    reset() {
      if (this.progressTimer) {
        clearInterval(this.progressTimer);
        this.progressTimer = null;
      }
      // 重置步骤
      this.activeStep = 0;

      // 重置选中的资产
      this.selectedAssets = [];

      // 重置时间表单
      this.timeForm.dateRange = [];
      if (this.$refs.timeForm) {
        this.$refs.timeForm.resetFields();
        this.resetTimeFormValidation(); // 清除验证状态
      }
      // 强制关闭日期选择器弹出框
      this.handleDatePickerBlur();

      // 重置生成状态
      this.generating = false;
      this.generateProgress = 0;
      this.generatedCount = 0;
      this.nowDateList = 0;

      // 重置加载状态
      this.loading = false;

      // 重置生成参数
      this.generateParams = {
        ids: null,
        assetCode: null,
        assetName: null,
        checkTimeHas: null,
        isCheck: null,
        maintenanceSectionId: null,
        managementMaintenanceIds: null,
        routeCodes: null,
        type: null,
        total: null,
        pageNum: 1,
        pageSize: 10,
        dataRule: true,
      };

      // 重置资产表格组件（如果存在）
      if (
        this.$refs.assetTable &&
        typeof this.$refs.assetTable.reset === 'function'
      ) {
        this.$refs.assetTable.reset();
      }
      this.processedIds = [];

      // 更新componentKey以强制刷新组件
      this.componentKey++;
    },
    // 判断是否已被移除
    isRemoved(row) {
      return this.removedIds.includes(row.baseData.id);
    },

    // 切换移状态
    handleToggleRemove(row) {
      const id = row.baseData.id;
      const index = this.removedIds.indexOf(id);
      if (index > -1) {
        // 如果已经在移除列表中，则取消移除
        this.removedIds.splice(index, 1);
        // this.$message.success('已取消移除');
      } else {
        // 如果不在移除列表中，则添加到移除列表
        this.removedIds.push(id);
        // this.$message.success('已移除');
      }
    },
    async generateCheck() {
      try {
        this.loading = true;
        this.generating = true;
        this.generateProgress = 0;
        const response = await setGenerateCheck({
          ...this.generateParams,
          nowDateList: this.nowDateList,
          excludeIds: this.removedIds,
        });
        this.processId = response.data;

        // 开始轮询进度
        this.startProgressPolling();

        return response;
      } catch (error) {
        this.$message.error('生成失败：' + error.message);
        this.generating = false;
      } finally {
        this.loading = false;
      }
    },
    // 修改轮询进度的方法
    startProgressPolling() {
      if (this.progressTimer) {
        clearInterval(this.progressTimer);
      }

      this.progressTimer = setInterval(async () => {
        try {
          const response = await getProgress({ processId: this.processId });
          const { processedCount, isCompleted, processedIds } = response.data;

          // 计算进度百分比
          const total = this.generateParams.total - this.removedIds.length;
          this.generateProgress = Math.floor((processedCount / total) * 100);

          // 更新已处理的ID列表
          if (processedIds && processedIds.length > 0) {
            this.processedIds = processedIds;
          }

          // 当收到完成状态时，无论进度多少都视为完成
          if (isCompleted) {
            clearInterval(this.progressTimer);
            this.progressTimer = null;
            this.generating = false;
            this.generatedCount = processedCount;

            if (processedCount < total) {
              this.$message.info(
                `生成完成。共处理 ${processedCount} 条记录，${
                  total - processedCount
                } 条记录被跳过。`
              );
            } else {
              this.$message.success(`成功生成 ${processedCount} 条记录。`);
            }
          }
        } catch (error) {
          clearInterval(this.progressTimer);
          this.progressTimer = null;
          this.generating = false;
          this.$message.error('获取进度失败：' + error.message);
        }
      }, 1000);
    },
    // 添加timepicker移除方法
    handleDatePickerBlur() {
      // 强制关闭日期选择器弹出框
      const popperElement = document.querySelector(
        '.generate-time-picker-popper'
      );
      if (popperElement) {
        popperElement.style.display = 'none';
      }
      // 关闭时间组件
      this.$refs.timePicker.blur();
    },
    // 添加focus事件处理方法
    handleDatePickerFocus() {
      const popperElement = document.querySelector(
        '.generate-time-picker-popper'
      );
      if (popperElement) {
        popperElement.style.display = ''; // 清除display样式，恢复默认显示
      }
      // 强制重新计算定位
      this.$refs.timePicker.focus();

      // 使用setTimeout确保在下一个事件循环中更新位置
      setTimeout(() => {
        if (this.$refs.timePicker && this.$refs.timePicker.$refs.reference) {
          // 触发一个resize事件来强制更新popper位置
          window.dispatchEvent(new Event('resize'));
        }
      }, 0);
    },
    // 添加重置表单验证的方法
    resetTimeFormValidation() {
      if (this.$refs.timeForm.validate) {
        // 清除验证状态
        this.$refs.timeForm.clearValidate();
      }
    },
    async handlePrev() {
      if (this.activeStep === 1) {
        // 从第二步返回第一步时,关闭日期选择器
        this.handleDatePickerBlur();
        this.activeStep--;
      } else if (this.activeStep === 2) {
        // 从第三步回第二步时,打开日期选择器
        this.$nextTick(() => {
          this.handleDatePickerFocus();
          this.activeStep--;
        });
      } else if (this.activeStep === 3) {
        // 提取inspectionList中的id并调用删除方法
        const ids = this.inspectionList.map((item) => item.id);
        if (ids.length > 0) {
          try {
            const isBatchDelete = await batchDeleteInspectionLogs(ids);
            if (isBatchDelete) {
              this.activeStep--;
            }
          } catch (error) {
            this.$message.error('删除巡查记录失败', error);
          }
        } else {
          this.activeStep -= 2;
        }
      }
    },
  },
};
</script>
<style lang="scss">
.generate-time-picker-popper {
  width: 1000px;
  transform: translateX(-46.5%) !important;
  left: 50% !important;

  .el-date-range-picker__header {
    .el-icon-arrow-right {
      vertical-align: middle;
    }
  }
}
</style>
<style lang="scss" scoped>
.asset-check-generate-dialog {
  ::v-deep .el-dialog__body {
    padding: 20px;
    height: 75vh;
    overflow-y: auto;
  }

  .step-content {
    height: calc(100% - 100px);
    min-height: 400px;

    :deep(.el-dialog__body) {
      height: 100%;
    }

    > * {
      height: 100%;
    }
  }

  .el-steps {
    margin-bottom: 20px;
  }

  // 调整日期选择器内部文字的垂直居中
  ::v-deep .el-range-editor {
    .el-range-input {
      line-height: 70px;
      vertical-align: middle;
      font-size: 16px;
      font-weight: bold;
    }

    .el-range-separator {
      line-height: 70px;
      height: 70px;
      vertical-align: middle;
      font-size: 16px;
      font-weight: bold;
    }

    // 调整占位符文字
    input::placeholder {
      line-height: 70px;
      vertical-align: middle;
      font-size: 16px;
    }
  }

  ::v-deep .el-form-item__label {
    font-size: 16px;
    font-weight: bold;
  }
}

.row-removed {
  color: #999;
  position: relative;
}

::v-deep .el-table__row {
  // 被移除行的样式
  &:has(.row-removed) {
    position: relative;

    // 添加横线
    &::after {
      content: '';
      position: absolute;
      top: 50%;
      left: 0;
      right: 0;
      border-top: 1px solid #999;
      pointer-events: none; // 确保线不会影响点击事件
      z-index: 1; // 确保线在内容之上
    }

    // 背景
    background-color: #f5f7fa !important;

    // 鼠标悬停时的样式
    &:hover > td {
      background-color: #f5f7fa !important;
    }
  }
}
</style>
