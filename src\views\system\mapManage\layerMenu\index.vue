<template>
  <PageContainer>
    <template slot="search">
      <el-row :gutter="20">
        <el-col :span="3" :offset="0">
          <el-input v-model="queryParams.menuName" placeholder="请输入目录名称" clearable size="mini"
            @keyup.enter.native="handleQuery" />
        </el-col>
        <el-col :span="3" :offset="0">
          <el-select v-model="queryParams.menuType" placeholder="请选择目录类型" clearable size="mini">
            <el-option label="基础数据目录" :value="1" />
            <el-option label="分析图层目录" :value="2" />
          </el-select>
        </el-col>
        <el-col :span="6" :offset="0">
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">
            搜索
          </el-button>
          <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">
            重置
          </el-button>
        </el-col>
      </el-row>
    </template>
    <template slot="header">
      <el-row :gutter="20">
        <el-col :span="1.5" :offset="0" class="mb8">
          <el-button v-hasPermi="['baseData:culvert:add']" type="primary" size="mini" @click="handleAdd">
            新增
          </el-button>
        </el-col>
        <el-col :span="1.5" :offset="0" class="mb8">
          <el-button v-hasPermi="['baseData:culvert:edit']" :disabled="!(tableSelects.length === 1)" type="primary"
            size="mini" @click="handleUpdate">
            编辑
          </el-button>
        </el-col>
        <el-col :span="1.5" :offset="0" class="mb8">
          <el-button v-hasPermi="['baseData:culvert:delete']" :disabled="!(tableSelects.length > 0)" type="primary"
            size="mini" @click="handleDelete">
            删除
          </el-button>
        </el-col>
        <el-col :span="1.5" :offset="0" class="mb8">
          <el-button v-hasPermi="['baseData:culvert:getInfoById']" :disabled="!(tableSelects.length === 1)" type="primary"
            size="mini" @click="onDetail">
            查看
          </el-button>
        </el-col>
      </el-row>
    </template>
    <template slot="body">
      <el-table v-adjust-table :data="layerMenuList" border height="100%" @selection-change="handleSelectionChange"
        @row-click="handleRowClick" ref="tableRef" style="width: 100%" :row-key="(row) => row.id"
        :tree-props="{children: 'child'}">
        <el-table-column type="selection" width="50" align="center" :reserve-selection="true" />
        <el-table-column v-for="col in columns" :prop="col.id" :key="col.id" :label="col.label" :min-width="col.width"
          show-overflow-tooltip>
          <template #default="{ row }">
            <el-link :underline="false" v-if="col.type && col.type == 'select'">{{ col.options[row[col.prop]] }}</el-link>
            <span v-else>{{ row[col.prop] }}</span>
          </template>
        </el-table-column>
      </el-table>
      <!-- <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize"
        @pagination="getList" /> -->
    </template>

    <!-- 新增编辑查看 -->
    <Dialog :title="title" :show.sync="open">
      <AddOrEdit :form="form" @close="handleClose" @refresh="getList" :readonly="readonly" />
    </Dialog>
  </PageContainer>
</template>
<script>
import {
  getListPage,
  getListTree,
  getLayerMenu,
  delLayerMenu,
} from "@/api/oneMap/layerMenu";
import { fields } from "./columns";
import AddOrEdit from "./components/addOrEdit.vue";
import Dialog from "@/components/Dialog/index.vue";

export default {
  name: "LayerMenu",
  components: {
    AddOrEdit,
    Dialog,
  },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 图层目录设置表格数据
      layerMenuList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        parentId: null,
        name: null,
        description: null,
        showIndex: null,
        oneMapShow: null,
        menuType: null,
      },
      // 表单参数
      form: {},
      tableSelects: [],
      columns: fields, // 列表字段
      readonly: false,
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询图层目录设置列表 */
    getList() {
      this.loading = true;
      // getListPage(this.queryParams)
      getListTree().then((res) => {
        this.layerMenuList = res.rows || res || [];
        // this.total = res.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        parentId: null,
        name: null,
        description: null,
        showIndex: null,
        oneMapShow: null,
        menuType: null,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.parentId);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      if(this.tableSelects && this.tableSelects.length==1) {
        this.form.parentId = this.tableSelects[0].id
      }
      this.reset();
      this.open = true;
      this.title = "添加图层目录设置";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      let id = this.tableSelects[0].id;
      getLayerMenu(id).then((response) => {
        this.form = response.data;
        this.open = true;
        this.title = "修改图层目录设置";
      });
    },
    handleClose(e) {
      this.open = e;
      this.readonly = false;
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      let ids = this.tableSelects.map((v) => v.id);
      this.$modal
        .confirm("是否确认删除图层目录数据项？")
        .then(function () {
          return delLayerMenu(ids);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => { });
    },
    /** 详情 */
    onDetail() {
      this.reset();
      let id = this.tableSelects[0].id;
      getLayerMenu(id).then((response) => {
        this.form = response.data;
        this.readonly = true;
        this.open = true;
        this.title = "查看图层目录设置";
      });
    },
    // 多选框选中数据
    handleSelectionChange(data) {
      this.tableSelects = data || [];
    },
    handleRowClick(row) {
      let arr = this.tableSelects.filter((v) => v.id == row.id);
      // 点击行 选中复选框
      if (arr && arr.length > 0) {
        this.$refs.tableRef.toggleRowSelection(row, false);
        this.tableSelects = this.tableSelects.filter((v) => v.id != row.id);
      } else {
        this.tableSelects = [...this.tableSelects, ...[row]];
        this.$refs.tableRef.toggleRowSelection(row, true);
      }
    },
  },
};
</script>
<style lang="scss" scoped></style>
