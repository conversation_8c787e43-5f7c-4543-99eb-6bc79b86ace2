//湿度概率直方图
import { getMax, getMin } from '@/views/jgjc/dataAnalysis/dataView/drawMethods/util'
import * as echarts from "echarts";
import myTheme from './myTheme'
export const CLHZ = {
//车速概率直方统计图
  setCSChart(chartName, data) {
    let myChart = echarts.getInstanceByDom(
      document.getElementById(chartName)
    );
    if (myChart !== undefined) {
      myChart.dispose();
    }

    myChart = echarts.init(document.getElementById(chartName), myTheme.theme);
    let optionName = data.optionName;
    data = data.dataList
    const xLabel = ['<60', '60~70', '70~80', '80~90', '90~100', '100~110', '110~120', '>=120']
    let option = {
      title: {
        left: 'center',
        text: optionName,
      },
      toolbox: {
        right: 0,
        top: 25,
        feature: {
          saveAsImage: {
            title: '保存'
          },
        },
      },
      tooltip: {
        textStyle:{
          align:'left'
        },
        trigger: "axis",
        confine: true
      },
      xAxis: {
        type: 'category',
        nameLocation: "middle",
        nameGap: 22,
        data: xLabel
      },
      yAxis: {
        name: "概率",
        type: 'value',
        nameTextStyle: {
          fontWeight: "bold",
          fontSize: 12
        },
        axisLabel: {
          formatter: function (value, index) {
            return value.toFixed(0) + '%';
          }
        }
      },
      dataZoom: [
        {
          type: "inside",
        },
      ],
      grid: [
        {
          top: "17%",
          left: "2%",
          right: "10%",
          bottom: "12%",
          containLabel: true,
        },
      ],
      series: [],
      legend: {
        type: 'scroll',
        x: 'center',
        y: 'bottom',
        data: [],
      },
    };
    //加入多行数据
    for(let i = 0 ; i < data.length; i++){
      let barData = data[i].distribution.map(item => item.toFixed(1))
      option.legend.data.push(data[i].label);
      option.series.push({
        type: "bar",
        barGap: 0,
        barCategoryGap: 0,
        name: data[i].label,
        data: barData,
      });
    }
    option && myChart.setOption(option);
    window.onresize = function () {
      myChart.resize();
    };
  },
  //日车流量统计图
  setCLLChart(chartName, data) {
    let myChart = echarts.getInstanceByDom(
      document.getElementById(chartName)
    );
    if (myChart !== undefined) {
      myChart.dispose();
    }

    myChart = echarts.init(document.getElementById(chartName), myTheme.theme);
    let optionName = data.optionName;
    data = data.dataList
    // 数据处理 给值增加时间戳
    data.forEach((item,index) =>{
      let tmpDotList0 = []
      for(let i = 0; i < item.result.processedData.values.length; i++) {
        tmpDotList0.push([item.result.processedData.times[i], item.result.processedData.values[i]])
      }
      item.result.processedData.dotList = tmpDotList0
    })
    let option = {
      title: {
        left: 'center',
        text: optionName,
      },
      toolbox: {
        right: 0,
        top: 25,
        feature: {
          saveAsImage: {
            title: '保存'
          },
        },
      },
      tooltip: {
        textStyle:{
          align:'left'
        },
        trigger: "axis",
        confine: true
      },
      xAxis: {
        type: 'category',
        name: "时间",
        nameLocation: "middle",
        nameGap: 22,
        axisLabel: {
          formatter: function (value, index) {
            return value.substring(0, value.length - 12);
          },
          fontSize: 11,
        },
      },
      yAxis: [],
      dataZoom: [
        {
          type: "inside",
        },
      ],
      grid: [
        {
          top: "17%",
          left: "2%",
          right: "10%",
          bottom: "12%",
          containLabel: true,
        },
      ],
      series: [],
      legend: {
        type: 'scroll',
        data: [],
        x: 'center',
        y: 'bottom',
        selected: {},
      },
    };
    //加入多行数据
    let finalMax = 0;
    let finalMin = 0;
    for(let i = 0 ; i < data.length; i++){
      option.legend.data.push(data[i].label);
      option.series.push({
        type: "line",
        name: data[i].label,
        showSymbol: false,
        data: data[i].result.processedData.dotList,
      });

      //计算最大值与最小值
      let minValue = getMin(data[i].result.processedData.values); // 输出最小值
      let maxValue = getMax(data[i].result.processedData.values); // 输出最大值
      // 一些特殊情况处理
      if (maxValue === 0 && minValue === 0) {
        maxValue = 1
        minValue = -1
      } else {
        let delta = maxValue - minValue
        if (delta === 0) {
          delta = 1
          maxValue = maxValue + delta
          minValue = minValue - delta
        }
      }
      // 外层循环最大最小值更新
      if(i === 0){
        finalMax = maxValue
        finalMin = minValue
      }else {
        if(finalMax < maxValue){
          finalMax = maxValue
        }
        if(finalMin > minValue){
          finalMin = minValue
        }
      }
      finalMin = 0
      // 整百整千处理
      if(finalMax !== 0){
        let exponent = Math.floor(Math.log(finalMax) / Math.LN10);
        let exp10 = Math.pow(10, exponent);
        finalMax = Math.ceil(finalMax / exp10) * exp10
      }
      // 一些预警线
      if (data[i].result.limitInfo) {
        Object.keys(data[i].result.limitInfo).forEach((key) => {
          option.series.push({
            type: "line",
            name: data[i].label + key,
            showSymbol: false,
            animation: false,
            markLine: {
              symbol: 'none',
              data: [
                {
                  yAxis: data[i].result.limitInfo[key],
                  lineStyle: {
                    type: 'solid'
                  },
                  label: {
                    show: true,
                    position: 'insideEndTop',
                  },
                }
              ]
            }
          });
          option.legend.data.push(data[i].label + key)
        })
      }
    }
    option.yAxis.push({
      name: '车辆数/辆',
      nameTextStyle: {
        fontWeight: "bold",
        fontSize: 12
      },
      axisLabel: {
        formatter: function (value) {
          return value.toFixed(0); // 2表示小数为2位
        },
      },
      splitNumber: 5,
      min: finalMin,
      max: finalMax,
      interval: (finalMax - finalMin) / 5, // 标轴分割间隔
    })
    option && myChart.setOption(option);
    window.onresize = function () {
      myChart.resize();
    };
  },
  //超重车辆分布直方统计图
  setCZChart(chartName, data) {
    let myChart = echarts.getInstanceByDom(
      document.getElementById(chartName)
    );
    if (myChart !== undefined) {
      myChart.dispose();
    }

    myChart = echarts.init(document.getElementById(chartName), myTheme.theme);
    let optionName = data.optionName;
    const xLabel = data.sensorLabelList
    data = data.dataList
    let option = {
      title: {
        left: 'center',
        text: optionName,
      },
      toolbox: {
        right: 0,
        top: 25,
        feature: {
          saveAsImage: {
            title: '保存'
          },
        },
      },
      tooltip: {
        textStyle:{
          align:'left'
        },
        trigger: "axis",
        confine: true
      },
      xAxis: {
        type: 'category',
        nameLocation: "middle",
        nameGap: 22,
        data: xLabel
      },
      yAxis: {
        name: "概率",
        type: 'value',
        nameTextStyle: {
          fontWeight: "bold",
          fontSize: 12
        },
        axisLabel: {
          formatter: function (value, index) {
            return value.toFixed(0) + '%';
          }
        }
      },
      dataZoom: [
        {
          type: "inside",
        },
      ],
      grid: [
        {
          top: "17%",
          left: "2%",
          right: "10%",
          bottom: "12%",
          containLabel: true,
        },
      ],
      series: [],
      legend: {
        type: 'scroll',
        x: 'center',
        y: 'bottom',
        data: [],
      },
    };
    //加入多行数据
    for(let i = 0 ; i < data.length; i++){
      let barData = data[i].distribution.map(item => item.toFixed(1))
      option.legend.data.push(data[i].label);
      option.series.push({
        type: "bar",
        barGap: 0,
        barCategoryGap: 0,
        name: data[i].label,
        data: barData,
      });
    }
    option && myChart.setOption(option);
    window.onresize = function () {
      myChart.resize();
    };
  },
  //日车流量统计图
  setCLLULTRAChart(chartName, data) {
    let myChart = echarts.getInstanceByDom(
      document.getElementById(chartName)
    );
    if (myChart !== undefined) {
      myChart.dispose();
    }

    myChart = echarts.init(document.getElementById(chartName), myTheme.theme);
    let optionName = data.optionName;
    data = data.dataList
    // 数据处理 给值增加时间戳
    data.forEach((item,index) =>{
      let tmpDotList0 = []
      for(let i = 0; i < item.result.processedData.values.length; i++) {
        tmpDotList0.push([item.result.processedData.times[i], item.result.processedData.values[i]])
      }
      item.result.processedData.dotList = tmpDotList0
    })
    data.sort(function (a, b){
      return b.result.processedData.dotList.length - a.result.processedData.dotList.length
    })
    let option = {
      title: {
        left: 'center',
        text: optionName,
      },
      toolbox: {
        right: 0,
        top: 25,
        feature: {
          saveAsImage: {
            title: '保存'
          },
        },
      },
      tooltip: {
        textStyle:{
          align:'left'
        },
        trigger: "axis",
        confine: true
      },
      xAxis: {
        type: 'category',
        name: "时间",
        nameLocation: "middle",
        nameGap: 22,
        axisLabel: {
          formatter: function (value, index) {
            return value.substring(0, value.length - 12);
          },
          showMinLabel: true,
          showMaxLabel: true,
          fontSize: 11,
        },
      },
      yAxis: [],
      dataZoom: [
        {
          type: "inside",
        },
      ],
      grid: [
        {
          top: "17%",
          left: "2%",
          right: "10%",
          bottom: "12%",
          containLabel: true,
        },
      ],
      series: [],
      legend: {
        type: 'scroll',
        data: [],
        x: 'center',
        y: 'bottom',
        selected: {},
      },
    };
    //加入多行数据
    let finalMax = 0;
    let finalMin = 0;
    for(let i = 0 ; i < data.length; i++){
      option.legend.data.push(data[i].label);
      option.series.push({
        type: "line",
        name: data[i].label,
        showSymbol: false,
        data: data[i].result.processedData.dotList,
      });

      //计算最大值与最小值
      let minValue = getMin(data[i].result.processedData.values); // 输出最小值
      let maxValue = getMax(data[i].result.processedData.values); // 输出最大值
      // 一些特殊情况处理
      if (maxValue === 0 && minValue === 0) {
        maxValue = 1
        minValue = -1
      } else {
        let delta = maxValue - minValue
        if (delta === 0) {
          delta = 1
          maxValue = maxValue + delta
          minValue = minValue - delta
        }
      }
      // 外层循环最大最小值更新
      if(i === 0){
        finalMax = maxValue
        finalMin = minValue
      }else {
        if(finalMax < maxValue){
          finalMax = maxValue
        }
        if(finalMin > minValue){
          finalMin = minValue
        }
      }
      finalMin = 0
      // 整百整千处理
      if(finalMax !== 0){
        let exponent = Math.floor(Math.log(finalMax) / Math.LN10);
        let exp10 = Math.pow(10, exponent);
        finalMax = Math.ceil(finalMax / exp10) * exp10
      }
      // 一些预警线
      if (data[i].result.limitInfo) {
        Object.keys(data[i].result.limitInfo).forEach((key) => {
          option.series.push({
            type: "line",
            name: data[i].label + key,
            showSymbol: false,
            animation: false,
            markLine: {
              symbol: 'none',
              data: [
                {
                  yAxis: data[i].result.limitInfo[key],
                  lineStyle: {
                    type: 'solid'
                  },
                  label: {
                    show: true,
                    position: 'insideEndTop',
                  },
                }
              ]
            }
          });
          option.legend.data.push(data[i].label + key)
        })
      }
    }
    option.yAxis.push({
      name: '车辆数/辆',
      nameTextStyle: {
        fontWeight: "bold",
        fontSize: 12
      },
      axisLabel: {
        formatter: function (value) {
          return value.toFixed(0); // 2表示小数为2位
        },
      },
      splitNumber: 5,
      min: finalMin,
      max: finalMax,
      interval: (finalMax - finalMin) / 5, // 标轴分割间隔
    })
    option && myChart.setOption(option);
    window.onresize = function () {
      myChart.resize();
    };
  },

}
