import Vue from "vue";

import Cookies from "js-cookie";

import Element from "element-ui";
import "./assets/styles/element-variables.scss";

import "@/assets/styles/index.scss"; // global css
import "@/assets/styles/ruoyi.scss"; // ruoyi css
import "@/assets/AppIcons/iconfont.css"; // iconfont
import "@/assets/AppIcons/iconfont.js"; // iconfont
import App from "./App";
import store from "./store";
import router from "./router";
import directive from "./directive"; // directive
import plugins from "./plugins"; // plugins
import { download } from "@/utils/request";

// 日期
import moment from "moment";

import "./assets/icons"; // icon
import "./permission"; // permission control

// 引入过滤器..
import "@/utils/filters/dateFilter";
import "@/utils/filters/fileFilter";

import { getDicts } from "@/api/system/dict/data";
import { getUser } from "@/api/system/user";
import { getConfigKey } from "@/api/system/config";
import {
  parseTime,
  resetForm,
  addDateRange,
  selectDictLabel,
  selectDictLabels,
  handleTree,
  formatPile,
  ceilToTwo,
  isValidMathFormula
} from "@/utils/ruoyi";
// 分页组件
import Pagination from "@/components/Pagination";
// 自定义表格工具组件
import RightToolbar from "@/components/RightToolbar";
// 富文本组件
import Editor from "@/components/Editor";
// 文件上传组件
import FileUpload from "@/components/FileUpload";
// 图片上传组件
import ImageUpload from "@/components/ImageUpload";
// 图片预览组件
import ImagePreview from "@/components/ImagePreview";
// 字典标签组件
import DictTag from "@/components/DictTag";
// 头部标签组件
import VueMeta from "vue-meta";
// 字典数据组件
import DictData from "@/components/DictData";
// 字典数据组件
import DictSelect from "@/components/DictSelect";
// 公共列表页面
import PageContainer from "@/components/PageContainer";
import { create, all } from 'mathjs';

// 全局方法挂载
Vue.prototype.getDicts = getDicts;
Vue.prototype.getConfigKey = getConfigKey;
Vue.prototype.parseTime = parseTime;
Vue.prototype.resetForm = resetForm;
Vue.prototype.addDateRange = addDateRange;
Vue.prototype.selectDictLabel = selectDictLabel;
Vue.prototype.selectDictLabels = selectDictLabels;
Vue.prototype.download = download;
Vue.prototype.handleTree = handleTree;
Vue.prototype.formatPile = formatPile;
Vue.prototype.ceilToTwo = ceilToTwo;
Vue.prototype.isValidMathFormula = isValidMathFormula;
Vue.prototype.$moment = moment;
const math = create(all, {
  number: 'BigNumber', // 使用 Decimal 模式
  precision: 64        // 设置计算精度
});
Vue.prototype.math = math;

// 全局组件挂载
Vue.component("DictTag", DictTag);
Vue.component("Pagination", Pagination);
Vue.component("RightToolbar", RightToolbar);
Vue.component("Editor", Editor);
Vue.component("FileUpload", FileUpload);
Vue.component("ImageUpload", ImageUpload);
Vue.component("ImagePreview", ImagePreview);
Vue.component("DictSelect", DictSelect);
Vue.component("PageContainer", PageContainer);

Vue.use(directive);
Vue.use(plugins);
Vue.use(VueMeta);
DictData.install();

import "video.js/dist/video-js.css"; // 引入video.js的css
import hls from "videojs-contrib-hls"; // 播放hls流需要的插件
Vue.use(hls);

import scroll from "vue-seamless-scroll";
Vue.use(scroll);

import VScaleScreen from 'v-scale-screen'
Vue.use(VScaleScreen)
// 注册全局过滤器

/**
 * If you don't want to use mock-server
 * you want to use MockJs for mock api
 * you can execute: mockXHR()
 *
 * Currently MockJs will be used in the production environment,
 * please remove it before going online! ! !
 */

Vue.use(Element, {
  size: Cookies.get("size") || "mini", // set element-ui default size
});

// 允许分页pagerCount值小于5
Element.Pagination.props.pagerCount.default = 1;
Element.Pagination.props.pagerCount.validator = (value) => true;

Element.Dialog.props.closeOnClickModal.default = false

// 创建全局事件总线
window.$Bus = new Vue();

Vue.config.productionTip = false;

new Vue({
  el: "#app",
  router,
  store,
  render: (h) => h(App),
});
