<template>
  <el-select :disabled='readonly' v-loading="loading" v-model="selectValue" :placeholder="placeholder" @change="$emit('change', $event)" clearable filterable style="width: 100%">
    <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value">
    </el-option>
  </el-select>
</template>

<script>
export default {
  name: 'ASelect',
  props: {
    value: {
      type: [String, Number],
      default: ''
    },
    params: {
      type: Object,
      default: () => {
        return {}
      }
    },
    readonly: {
      type: Boolean,
      default: false
    },
    placeholder: {
      type: String,
      default: '请选择'
    },
    apiMethod: {
      type: Function,
      default: () => {
        return () => {
          return new Promise((resolve, reject) => {
            resolve([])
          })
        }
      }
    },
    defaultProps: {
      type: Object,
      default: () => {
        return {
          label: 'name',
          value: 'id',
        }
      }
    },
  },
  data() {
    return {
      options: [],
      loading: false,
      targetData: []
    }
  },
  computed: {
    selectValue: {
      get: function () {
        return this.value
      },
      set: function (val) {
        this.$emit('input', val)
      }
    }
  },
  mounted() {
    this.getOptions()
  },
  methods: {
    async getOptions() {
      this.loading = true
      this.apiMethod(this.params).then(res => {
        this.targetData = res.rows
        this.options = res.rows.map(item => {
          return {
            label: item[this.defaultProps.label],
            value: item[this.defaultProps.value],
          }
        })
        this.$emit('change', this.getCheckData())
      }).finally(() => {
        this.loading = false
      })
    },
    getCheckData() {
      return this.targetData.filter(item => {
        return item[this.defaultProps.value] === this.value
      })[0]
    }
  },
}

</script>

<style lang="scss" scoped>

</style>
