<template>
  <div class="app-container">
    <el-row :gutter="20">
      <!-- 筛选区开始 -->
      <el-col :span="24" :xs="24">
        <el-row>
          <el-col :span="24">
            <el-form ref="queryForm" :inline="true" :model="queryParams" label-width="68px" size="small">
              <el-form-item label="" prop="name">
                <el-input v-model="queryParams.name" clearable placeholder="请输入名称"/>
              </el-form-item>
              <el-form-item>
                <el-button icon="el-icon-search" size="mini" type="primary" @click="handleQuery">搜索</el-button>
                <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
              </el-form-item>
            </el-form>
          </el-col>
        </el-row>
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button
              icon="el-icon-plus"
              size="mini"
              type="primary"
              @click="handleAdd"
            >新增
            </el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button
              icon="el-icon-delete"
              size="mini"
              type="danger"
              @click="handleDelete"
            >删除
            </el-button>
          </el-col>
        </el-row>
        <!-- 筛选区结束 -->
        <!-- 数据表格开始 -->
        <div class="tableDiv">
          <el-table v-adjust-table v-loading="loading" :data="dataList"
                    :height="'calc(100vh - 260px)'" border size="mini"
                    @selection-change="handleSelectionChange"
                    style="width: 100%">
            <el-table-column align="center" fixed="left" type="selection" width="55"></el-table-column>
            <el-table-column align="center" fixed label="序号" type="index" width="100"></el-table-column>
            <template v-for="(column, index) in columns">
              <el-table-column v-if="column.visible" :key="index" :label="column.label" :prop="column.field"
                               align="center" show-overflow-tooltip>
                <template slot-scope="scope">
                  <span>{{ scope.row[column.field] }}</span>
                </template>
              </el-table-column>
            </template>
            <el-table-column align="center" class-name="small-padding fixed-width" fixed="right" label="操作"
                             width="150">
              <template slot-scope="scope">
                <el-button icon="el-icon-edit" size="mini" type="text"
                           @click="handleEdit(scope.row)">编辑
                </el-button>
              </template>
            </el-table-column>
          </el-table>
          <pagination
            v-show="total > 0"
            :limit.sync="queryParams.pageSize"
            :page.sync="queryParams.pageNum"
            :total="total"
            @pagination="handleQuery"
          />
        </div>
        <!-- 数据表格结束 -->
      </el-col>
    </el-row>
    <el-dialog :title="dialogTitle" :visible.sync="dialogVisible" append-to-body destroy-on-close width="50%">
      <el-form ref="elForm" :model="formData" :rules="rules" label-width="100px" size="medium">
        <el-form-item label="名称" prop="name">
          <el-input v-model="formData.name" placeholder="请输入名称"/>
        </el-form-item>
        <el-form-item label="排序" prop="orders">
          <el-input-number v-model="formData.orders" :min="0" controls-position="right"/>
        </el-form-item>
      </el-form>
      <div style="text-align: right">
        <el-button @click="closeDialog">取消</el-button>
        <el-button type="primary" @click="handleConfirm">确定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {addDeviceType, deleteDeviceType, updateDeviceType, listDeviceTypes} from '@/api/jgjc/equipment/equipmentType';

export default {
  name: "EquipmentType",
  data() {
    return {
      // 遮罩层
      loading: false,
      // 总条数
      total: 0,
      dataList: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        title: ''
      },
      // 列信息
      columns: [
        {key: 0, field: 'name', label: '名称', visible: true},
        {key: 1, field: 'orders', label: '排序', visible: true}
      ],
      // 对话框标题
      dialogTitle: '',
      dialogVisible: false,
      formData: {
        name: '',
        title: '',
        orders: 0
      },
      ids: [],
      rules: {
        name: [{required: true, message: '请输入名称', trigger: 'blur'}],
        // orders: [{required: true, message: '请输入排序', trigger: 'blur'}]
      }
    };
  },
  created() {
    this.handleQuery();
  },
  methods: {
    handleQuery() {
      this.loading = true;
      listDeviceTypes(this.queryParams).then(res => {
        this.dataList = res.rows;
        this.total = res.total;
        this.loading = false;
      }).catch(err => {
        this.loading = false;
        console.error(err);
      });
    },
    resetQuery() {
      this.queryParams = {
        pageNum: 1,
        pageSize: 10,
        title: ''
      };
      this.handleQuery();
    },
    handleAdd() {
      this.dialogTitle = '新增';
      this.formData = {
        name: '',
        title: '',
        orders: 0
      };
      this.dialogVisible = true;
    },
    handleEdit(row) {
      this.dialogTitle = '编辑';
      this.formData = {...row};
      this.dialogVisible = true;
    },
    handleConfirm() {
      this.$refs.elForm.validate(valid => {
        if (valid) {
          this.loading = true;
          if (this.formData.id) {
            updateDeviceType(this.formData).then(res => {
              if (res.code === 200) {
                this.$message.success('编辑成功');
                this.handleQuery();
              } else {
                this.$message.error(res.msg);
              }
              this.loading = false;
              this.closeDialog();
            }).catch(err => {
              this.loading = false;
              console.error(err);
            });
          } else {
            addDeviceType(this.formData).then(res => {
              if (res.code === 200) {
                this.$message.success('新增成功');
                this.handleQuery();
              } else {
                this.$message.error(res.msg);
              }
              this.loading = false;
              this.closeDialog();
            }).catch(err => {
              this.loading = false;
              console.error(err);
            });
          }
        }
      });
    },
    closeDialog() {
      this.dialogVisible = false;
      this.$refs.elForm.resetFields();
    },
    handleSelectionChange(e) {
      this.ids = e.map(item => item.id);
    },
    handleDelete() {
      if (this.ids.length <= 0) {
        this.$message.warning('请勾选需要删除的数据');
        return
      }
      this.$confirm('是否确认删除选中的数据？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.loading = true;
        deleteDeviceType({ids: this.ids}).then(res => {
          if (res.code === 200) {
            this.$message.success('删除成功');
            this.handleQuery();
          } else {
            this.$message.error(res.msg);
          }
          this.loading = false;
        }).catch(err => {
          this.loading = false;
          console.error(err);
        });
      })
    },
  }
};
</script>

<style scoped>
.tableDiv {
  margin-top: 20px;
}
</style>
<style lang="scss" scoped>
@import "@/assets/styles/business.scss";
</style>
