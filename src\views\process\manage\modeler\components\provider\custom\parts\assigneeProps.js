// import entryFactory from 'bpmn-js-properties-panel/lib/factory/EntryFactory';

import {
    is
} from 'bpmn-js/lib/util/ModelUtil';

import entryFactory from '../../../factory/EntryFactory'

let getBusinessObject = require('bpmn-js/lib/util/ModelUtil').getBusinessObject,
    cmdHelper = require('bpmn-js-properties-panel/lib/helper/CmdHelper');

export default function(group, element, translate) {
    console.log('element：', element)
    if (!is(element, 'bpmn:StartEvent') && !is(element, 'bpmn:SequenceFlow')) { // 可以在这里做类型判断
        group.entries.push(entryFactory.textField(translate, {
            id : 'activiti:assignee',
            description : '',
            label : '代理用户',
            modelProperty : 'activiti:assignee',
            disabled: true,
            get: function(element) {
                let bo = getBusinessObject(element)
                console.log('bo', bo)
                return {
                    'activiti:assignee': bo.$attrs['activiti:assignee'] || ''
                };
            },
            set: function(element, values) {
                let bo = getBusinessObject(element)
                let theValues = values['activiti:assignee']
                console.log('values', values)
                return cmdHelper.updateBusinessObject(element, bo, {
                    'activiti:assignee': theValues
                })
            },
        }));
    }
}