<template>
  <div v-loading="loading" class="road-interflow-edit" style="padding: 20px">
    <el-row :gutter="20">
      <el-form
          ref="elForm"
          :model="formData"
          :rules="rules"
          :disabled="readOnly"
          label-width="130px"
          size="medium"
      >
        <el-col :span="24">
          <div class="card_title">1.基本信息</div>
        </el-col>
        <el-col :span="8">
          <el-form-item label="项目" prop="projName">
            <el-input v-model="formData.projName" placeholder="项目" style="width: 100%" :disabled="status != 1"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="名称" prop="name">
            <el-input v-model="formData.name" placeholder="名称" style="width: 100%" :disabled="status != 1"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="编号" prop="code">
            <el-input v-model="formData.code" placeholder="编号" style="width: 100%" :disabled="status != 1"></el-input>
          </el-form-item>
        </el-col>
        <template v-if="type == 1">
          <el-col :span="8">
            <el-form-item label="施工单位" prop="conDomainId">
              <construction-select v-model="formData.conDomainId" :mai-domain-id="formData.domainId" :mai-sec-id="formData.maiSecId" :type="0" placeholder="施工单位" :readOnly="status != 1"></construction-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="施工合同" prop="conConId">
              <contract-section @change="changeCon('conConId')" v-model="formData.conConId" :params="contractFilterMap" placeholder="请选择合同" :disabled="status != 1"></contract-section>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="监理单位" prop="supDomainId">
              <construction-select v-model="formData.supDomainId" :mai-domain-id="formData.domainId" :mai-sec-id="formData.maiSecId" :type="2" placeholder="监理单位" :readOnly="status != 1"></construction-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="监理合同" prop="supConId">
              <contract-section v-model="formData.supConId" :params="contractSupFilterMap" placeholder="请选择合同" :disabled="status != 1"></contract-section>
            </el-form-item>
          </el-col>
          <el-col :span="8" v-if="formData.supDomainId">
            <el-form-item label="监理起止日期" prop="supdateArr">
              <el-date-picker
                v-model="formData.supdateArr"
                type="daterange"
                value-format="yyyy-MM-dd"
                :style="{width: '100%'}"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                :disabled="status != 1">
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="设计单位" prop="designDomainId">
              <construction-select v-model="formData.designDomainId" :mai-domain-id="formData.domainId" :mai-sec-id="formData.maiSecId" :type="1" placeholder="设计单位" :readOnly="status != 1"></construction-select>
            </el-form-item>
          </el-col>
          <el-col :span="8" v-if="formData.designDomainId">
            <el-form-item label="设计起止日期" prop="designdateArr">
              <el-date-picker
                v-model="formData.designdateArr"
                type="daterange"
                value-format="yyyy-MM-dd"
                :style="{width: '100%'}"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                :disabled="status != 1">
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="8" v-if="formData.designDomainId">
            <el-form-item label="设计下发时间" prop="designIssueDate">
              <el-date-picker
                v-model="formData.designIssueDate"
                type="date"
                value-format="yyyy-MM-dd"
                :style="{width: '100%'}"
                placeholder="请选择"
                :disabled="status != 1">
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="缺陷责任期" prop="defLiaPer">
              <el-input-number v-model="formData.defLiaPer" placeholder="缺陷责任期" style="width: 100%" :disabled="status != 1"></el-input-number>
            </el-form-item>
          </el-col>
        </template>
        <template v-if="type == 2">
          <el-col :span="8">
            <el-form-item label="检测单位" prop="checkDomainId">
              <construction-select v-model="formData.checkDomainId" :mai-domain-id="formData.domainId" :mai-sec-id="formData.maiSecId" :type="3" placeholder="检测单位" :readOnly="status != 1"></construction-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="检测合同" prop="checkConId">
              <contract-section @change="changeCon('checkConId')" v-model="formData.checkConId" :params="contractCheckFilterMap" placeholder="请选择合同" :disabled="status != 1"></contract-section>
            </el-form-item>
          </el-col>
        </template>
        <template v-if="type == 3">
          <el-col :span="8">
            <el-form-item label="设计单位" prop="designDomainId">
              <construction-select v-model="formData.designDomainId" :mai-domain-id="formData.domainId" :mai-sec-id="formData.maiSecId" :type="1" placeholder="设计单位" :readOnly="status != 1"></construction-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="设计合同" prop="designConId">
              <contract-section v-model="formData.designConId" :params="{}" placeholder="请选择合同" :disabled="status != 1"></contract-section>
            </el-form-item>
          </el-col>
        </template>
        <el-col :span="8">
          <el-form-item label="起止日期" prop="dateArr">
            <el-date-picker
                v-model="formData.dateArr"
                type="daterange"
                value-format="yyyy-MM-dd"
                :style="{width: '100%'}"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                :disabled="status != 1">
            </el-date-picker>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="工作内容" prop="content">
            <el-input v-model="formData.content" :style="{width: '100%'}" placeholder="工作内容" rows="4"
                      type="textarea" :disabled="status != 1"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="实施要求" prop="exeRequire">
            <el-input v-model="formData.exeRequire" :style="{width: '100%'}" placeholder="实施要求" rows="4"
                      type="textarea" :disabled="status != 1"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="24" v-if="formData.supDomainId">
          <el-form-item label="监理要求" prop="supRequire">
            <el-input v-model="formData.supRequire" :style="{width: '100%'}" placeholder="监理要求" rows="4"
                      type="textarea" :disabled="status != 1"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="24" v-if="formData.designDomainId">
          <el-form-item label="设计要求" prop="designRequire">
            <el-input v-model="formData.designRequire" :style="{width: '100%'}" placeholder="设计要求" rows="4"
                      type="textarea" :disabled="status != 1"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="下发时间" prop="issueDate" v-if="status == 5">
            <el-input v-model="formData.issueDate" :style="{width: '100%'}" placeholder="下发时间"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="24" v-if="!readOnly">
          <el-form-item label="审核意见" prop="comment" v-if="status >= 2">
            <el-input v-model="formData.comment" :style="{width: '100%'}" placeholder="审核意见" rows="4"
                      type="textarea"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <div class="card_title">2.合同清单</div>
        </el-col>
        <el-col :span="24">
          <el-button  v-if="!readOnly" style="margin-bottom:10px;float:right" type="primary" @click="addScheme()" :disabled="status != 1">新增
          </el-button>
          <methods-list :read-only="readOnly" :value.sync="constructionDetailList"></methods-list>
        </el-col>
        <el-col :span="24" class="mt10" v-if="!readOnly">
          <div style="text-align: right">
            <el-button v-if="status == 1" type="primary" @click="handleSave">保存</el-button>
            <el-button v-if="status == 2" type="primary" @click="handleSubmit">提交</el-button>
            <el-button v-if="status == 3" type="primary" @click="handleExamine(true)">通过</el-button>
            <el-button v-if="status == 3" type="danger" @click="handleExamine(false)">驳回</el-button>
            <el-button v-if="status == 5" type="primary" @click="handleExamine(true)">签发</el-button>
            <el-button v-if="status == 4 || status == 5" type="danger" @click="handleExamine(false)">撤回</el-button>
            <el-button v-if="status == 6" type="primary" @click="handleExamine(true)">接收</el-button>
            <el-button v-if="status == 6" type="danger" @click="handleExamine(false)">拒收</el-button>
<!--            <el-button v-if="status == 6" type="danger" @click="handleExamine(false)">撤回</el-button>-->

            <el-button v-if="status == 7" type="primary" @click="handleSupExamine(true)">接收</el-button>
<!--            <el-button v-if="status == 7" type="danger" @click="handleSupExamine(false)">撤回</el-button>-->

            <el-button v-if="status == 8" type="primary" @click="handleDesignExamine(true)">接收</el-button>
<!--            <el-button v-if="status == 8" type="danger" @click="handleDesignExamine(false)">撤回</el-button>-->
            <el-button @click="close">取消</el-button>
          </div>
        </el-col>
      </el-form>
    </el-row>
    <methods-tree scheme-type="专项养护" :con-id="conId" ref="methodsRef" @input="checkLib" :domain-id="formData.domainId"></methods-tree>
  </div>
</template>

<script>
import selectTree from "@/components/DeptTmpl/selectTree.vue";
import RoadSection from "@/views/baseData/components/roadSection/index.vue";
import {getTreeData} from "@/api/contract/quotationSystem";
import ContractSection from "@/components/ContractSection/index.vue";
import ConstructionSelect from "@/components/ConstructionSelect/index.vue";
import {
  addConstruction,
  designProcess,
  editConstruction,
  getProDetail,
  process,
  supProcess
} from "@/api/maintenanceProject/taskList";
import {getCode} from "@/api/system/reportcode";
import MethodsTree from "@/components/MethodsTree/index.vue";
import moment from "moment/moment";
import MethodsList from "@/components/MethodsList/index.vue";
import {v4 as uuidv4} from "uuid";
import { Decimal } from 'decimal.js';

export default {
  components: {MethodsList, MethodsTree, ConstructionSelect, ContractSection, RoadSection, selectTree},
  data() {
    return {
      loading: false,
      formData: {},
      editableTabsValue: '',
      defaultProps: {
        children: 'children',
        label: 'schemeName'
      },
      rules: {
        projName: [
          { required: true, message: '项目不能为空', trigger: 'blur' }
        ],
        name: [
          { required: true, message: '名称不能为空', trigger: 'blur' }
        ],
        code: [
          { required: true, message: '编号不能为空', trigger: 'blur' }
        ],
        conDomainId: [
          { required: true, message: '施工单位不能为空', trigger: 'blur' }
        ],
        conConId: [
          { required: true, message: '施工合同不能为空', trigger: 'blur' }
        ],
        // supDomainId: [
        //   { required: true, message: '监理单位不能为空', trigger: 'blur' }
        // ],
        // supConId: [
        //   { required: true, message: '监理合同不能为空', trigger: 'blur' }
        // ],
        // designDomainId: [
        //   { required: true, message: '设计单位不能为空', trigger: 'blur' }
        // ],
        defLiaPer: [
          { required: true, message: '缺陷责任期不能为空', trigger: 'blur' }
        ],
        checkDomainId: [
          { required: true, message: '检测单位不能为空', trigger: 'blur' }
        ],
        checkConId: [
          { required: true, message: '检测合同不能为空', trigger: 'blur' }
        ],
        dateArr: [
          { required: true, message: '起止日期不能为空', trigger: 'blur' }
        ],
        content: [
          { required: true, message: '工作内容不能为空', trigger: 'blur' }
        ],
        exeRequire: [
          { required: true, message: '实施要求不能为空', trigger: 'blur' }
        ],
        supdateArr: [
          { required: true, message: '监理起止日期不能为空', trigger: 'blur' }
        ],
        designdateArr: [
          { required: true, message: '设计起止日期不能为空', trigger: 'blur' }
        ],
        designIssueDate: [
          { required: true, message: '设计下发时间不能为空', trigger: 'blur' }
        ],
        designRequire: [
          { required: true, message: '设计要求不能为空', trigger: 'blur' }
        ],
        supRequire: [
          { required: true, message: '监理要求不能为空', trigger: 'blur' }
        ],
      },
      conId: '',
      constructionDetailList: [],
    }
  },
  props: {
    type: {
      // 任务单类型 1施工 2检测 3设计
      type: Number,
      default: 1
    },
    status: {
      // 流程状态 1修改 2提交 3审核 4撤回 5签发
      type: Number,
      default: 1
    },
    readOnly: {
      type: Boolean,
      default: false
    },
    project: {
      type: Object,
      default: () => {}
    },
    row: {
      type: Object,
      default: () => {}
    },
    isCopy: {
      type: Boolean,
      default: false
    }
  },
  computed: {
    contractFilterMap: function () {
      return {type: '0', conDomainId: this.formData.conDomainId, sectionName: this.project?.maiSecName || this.row?.maiSecName}
    },
    contractCheckFilterMap: function () {
      return {type: '3', conDomainId: this.formData.checkDomainId, sectionName: this.project?.maiSecName || this.row?.maiSecName}
    },
    contractSupFilterMap: function () {
      return {type: '2', conDomainId: this.formData.supDomainId, sectionName: this.project?.maiSecName || this.row?.maiSecName}
    },
  },
  watch: {
    project: {
      handler(val) {
        if(val) {
          this.formData = {
            type: this.type,
            projId: val.id,
            projName: val.name,
            projCode: val.code,
            projectType: val.projectType,
            mType: val.mType,
            domainId: val.domainId,
            maiSecId: val.maiSecId,
            content: val.enContent
          }

          const params = {
            reportType: 'MP_Z_TASK_CODE',
            domainId: val.domainId,
            sectionName: val.maiSecName,
            year: new Date().getFullYear()
          }
          getCode(params).then(res => {
            if (res.code == 200) {
              this.$set(this.formData, 'code', res.msg)
            }
          })
        }
      },
      immediate: true,
      deep: true
    },
    row: {
      handler(val) {
        if (val) {
          this.formData = JSON.parse(JSON.stringify(val))
          if (this.isCopy) {
            this.formData.id = null
            delete this.formData.status
            if (this.formData.constructionDetailList.length > 0) {
              for (let i = 0; i < this.formData.constructionDetailList.length; i++) {
                this.formData.constructionDetailList[i].id = null
              }
            }
            const params = {
              reportType: 'MP_Z_TASK_CODE',
              domainId: val.domainId,
              sectionName: val.maiSecName,
              year: new Date().getFullYear()
            }
            getCode(params).then(res => {
              if (res.code == 200) {
                this.$set(this.formData, 'code', res.msg)
              }
            })
          }
          this.$set(this.formData, 'dateArr', [this.formData.beginDate, this.formData.endDate])
          if (this.formData.supBeginDate) this.$set(this.formData, 'supdateArr', [this.formData.supBeginDate, this.formData.supEndDate])
          if (this.formData.designBeginDate) this.$set(this.formData, 'designdateArr', [this.formData.designBeginDate, this.formData.designEndDate])
          this.constructionDetailList = this.formData.constructionDetailList || []
        }
        if (this.status == 5 && !this.formData.issueDate && !this.readOnly) {
          this.formData.issueDate = moment().format('YYYY-MM-DD HH:mm:ss')
        }
      },
      immediate: true,
      deep: true
    }
  },
  methods: {
    handleSave() {
      this.$refs.elForm.validate(valid => {
        if (!valid) return
        this.loading = true
        this.formData.constructionDetailList = this.constructionDetailList
        this.formData.beginDate = this.formData.dateArr[0]
        this.formData.endDate = this.formData.dateArr[1]

        if (this.formData.supdateArr?.length > 0) {
          this.formData.supBeginDate = this.formData.supdateArr[0]
          this.formData.supEndDate = this.formData.supdateArr[1]
        }
        if (this.formData.designdateArr?.length > 0) {
          this.formData.designBeginDate = this.formData.designdateArr[0]
          this.formData.designEndDate = this.formData.designdateArr[1]
        }
        if (this.status != 5) delete this.formData.issueDate
        if (this.formData.id) {
          editConstruction(this.formData).then(res => {
            this.loading = false
            this.$message.success('保存成功')
            this.$emit('close')
          }).finally(() => {
            this.loading = false
          })
        } else {
          addConstruction(this.formData).then(res => {
            this.loading = false
            this.$message.success('保存成功')
            this.$emit('close')
          }).finally(() => {
            this.loading = false
          })
        }
      })
    },
    handleSubmit() {
      const params = {
        businessKey: this.formData.id,
        taskId: this.formData.taskId,
        approved: true,
        comment: this.formData.comment
      }
      process(params).then(res => {
        this.$message.success('提交成功！');
        this.$emit('close')
      }).finally(() => {
        this.loading = false
      })
    },
    handleExamine(approved) {
      if (!approved && !this.formData.comment) {
        this.$message.error('请填写审批意见！')
        return
      }
      const params = {
        businessKey: this.formData.id,
        taskId: this.formData.taskId,
        approved,
        comment: this.formData.comment
      }
      if (this.status == 5) {
        params.issueDate = this.formData.issueDate
      }
      process(params).then(res => {
        this.$message.success('操作成功！');
        this.$emit('close')
      }).finally(() => {
        this.loading = false
      })
    },

    handleSupExamine(approved) {
      const params = {
        businessKey: this.formData.id,
        taskId: this.formData.supTaskId,
        approved,
        comment: this.formData.comment
      }
      supProcess(params).then(res => {
        this.$message.success('操作成功！');
        this.$emit('close')
      }).finally(() => {
        this.loading = false
      })
    },
    handleDesignExamine(approved) {
      const params = {
        businessKey: this.formData.id,
        taskId: this.formData.designTaskId,
        approved,
        comment: this.formData.comment
      }
      designProcess(params).then(res => {
        this.$message.success('操作成功！');
        this.$emit('close')
      }).finally(() => {
        this.loading = false
      })
    },
    close() {
      this.$emit('close')
    },
    addScheme() {
      this.conId = ''
      if (this.type == 1) {
        this.conId = this.formData.conConId
      } else if (this.type == 2) {
        this.conId = this.formData.checkConId
      } else if (this.type == 3) {
        this.conId = this.formData.designConId
      }
      if (!this.conId) {
        this.$message.error('请先选择合同')
        return
      }
      this.$refs.methodsRef.openLibModel()
    },
    changeCon(conName) {
      getProDetail({
        projId: this.formData.projId,
        conId: this.formData[conName]
      }).then(res => {
        this.constructionDetailList = res.data
      })
    },
    checkLib(checkDatas) {
      const constructionDetailList = []
      checkDatas = checkDatas.filter(item => {
        return item.nodeType == 2
      })
      checkDatas = checkDatas.filter(item => item.nodeType === 2 && (item.rateFlag === 1 || !constructionDetailList.some(filterItem => filterItem.schemeCode === item.schemeCode)));

      checkDatas.forEach(item => {
        constructionDetailList.push({
          conId: this.conId,
          schemeId: item.id,
          id: uuidv4().replace(/-/g, '').slice(0, 20),
          schemeCode: item.schemeCode,
          schemeName: item.schemeName,
          unit: item.unit,
          price: item.price,
          priceRate: item.priceRate,
          rateFlag: item.rateFlag,
          decimalPlaces: item.decimalPlaces,
          isProduction: item.safetyFeeFlag

        })
      })
      this.constructionDetailList.push(...constructionDetailList)
      this.constructionDetailList = this.constructionDetailList.reduce((acc, curr) => {
        const exists = acc.some(item => item.id === curr.id);
        return exists ? acc : [...acc, curr];
      }, []);
    },
    async changeCalculation(row) {
      if (!this.isValidMathFormula(row.calcDesc)) {
        this.$modal.msgError('计算式错误，请检查')
        return
      }
      let num =  this.math.evaluate(row.calcDesc) || 0
      this.$set(row, 'num', this.ceilToTwo(num, row.decimalPlaces))
      await this.changeSchemeNum(row)
    },
    async changeSchemeNum(row) {
      let money = new Decimal(row.num || 0).times(row.price || 0).toNumber()
      this.$set(row, 'amount', Math.round(money))
      this.total = await this.schemeList.reduce((acc, curr) => Number(acc) + Number(curr.amount), 0)
    },
  }
}
</script>
<style lang="scss" scoped>
.card_title {
  width: 200px;
  text-align: left;
  margin-bottom: 15px;
  font-weight: bold;
}
::v-deep {
  .el-tabs__header {
    padding-left: 20px;
    border: 0;
  }

  .el-tabs__content {
    border: 0;
  }

  .el-form-item__label {
    font-family: Microsoft YaHei, Microsoft YaHei;
    font-weight: 400;
    font-size: 14px;
    color: #333333;
  }

  .el-input.is-disabled .el-input__inner {
    background-color: white;
    border-color: #dfe4ed;
    color: #1d2129;
  }

  .el-textarea.is-disabled .el-textarea__inner {
    background-color: white;
    border-color: #dfe4ed;
    color: #1d2129;
  }
  .el-range-editor.is-disabled {
    background-color: white;
    border-color: #dfe4ed;
    color: #1d2129;
  }
  .el-range-editor.is-disabled input {
    background-color: white;
    border-color: #dfe4ed;
    color: #1d2129;
  }
}
</style>
<style lang="scss" scoped>
@import "@/assets/styles/business.scss";
</style>
