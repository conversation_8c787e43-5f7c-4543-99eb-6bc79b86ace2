<template>
  <div class="app-container">
    <el-form ref="elForm" :rules="formRules" :model="formData" :inline="true" label-width="200px" v-loading="loading">
      <el-row :gutter="20" style="display: flex; flex-wrap: wrap;">
        <el-col :span="24" class="mb20">
          <div class="card_title">项目信息</div>
          <el-descriptions size="mini" :column="3" border :labelStyle="{width: '150px'}" :contentStyle="{width: '300px'}">
            <el-descriptions-item v-for="(item, index) in projectColumns" :key="index" :label="item.name">
              <el-tooltip :content="projectData[item.field]" placement="top">
                <dict-tag v-if="item.dict" :options="dict.type[item.dict]" :value="projectData[item.field]"/>
                <span v-else>{{ projectData[item.field] }}</span>
              </el-tooltip>
            </el-descriptions-item>
          </el-descriptions>
        </el-col>
        <el-col :span="12">
          <el-form-item label="签证单编码" prop="code">
            <el-input v-model="formData.code" :disabled="fromEvent"/>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="是否完结" prop="isEnd">
            <el-radio-group v-model="formData.isEnd" size="medium">
              <el-radio v-for="(item, index) in isEndOptions" :key="index" :label="item.value"
                        :disabled="item.disabled || fromEvent">{{ item.label }}
              </el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="是否计算安全保通费" prop="isGuarantee">
            <el-radio-group v-model="formData.isGuarantee" size="medium" @change="calculate">
              <el-radio v-for="(item, index) in urgentDegreeOptions" :key="index" :label="item.value"
                        :disabled="item.disabled || fromEvent">{{ item.label }}
              </el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="是否计算安全生产费" prop="isProduction">
            <el-radio-group v-model="formData.isProduction" size="medium" @change="calculate">
              <el-radio v-for="(item, index) in urgentDegreeOptions" :key="index" :label="item.value"
                        :disabled="item.disabled || fromEvent">{{ item.label }}
              </el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="修补完成时间" prop="endTime">
            <el-date-picker v-model="formData.endTime" format="yyyy-MM-dd" value-format="yyyy-MM-dd"
                            :style="{width: '100%'}" placeholder="请选择修补完成时间" clearable></el-date-picker>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="24" style="margin-bottom: 18px">
          <div class="card_title">施工前图片</div>
          <el-card class="box-card" shadow="never">
            <el-form-item label="">
              <sort-file-upload v-model="sgqcjzp" can-sort ref="sgqcjzp" :fileType="['png', 'jpg', 'jpeg']"></sort-file-upload>
            </el-form-item>
          </el-card>
        </el-col>
        <el-col :span="24" style="margin-bottom: 18px">
          <div class="card_title">施工后图片</div>
          <el-card class="box-card" shadow="never">
            <el-form-item label="">
              <sort-file-upload v-model="sgfj" can-sort ref="sgfj" :fileType="['png', 'jpg', 'jpeg']"></sort-file-upload>
            </el-form-item>
          </el-card>
        </el-col>
        <el-col :span="12">
          <div class="card_title">施工简图</div>
          <el-card class="box-card" shadow="never">
            <el-form-item label="">
              <sort-file-upload limit="1" v-model="sgjt" can-sort ref="sgjt" :fileType="['png', 'jpg', 'jpeg']"></sort-file-upload>
            </el-form-item>
          </el-card>
        </el-col>
        <el-col :span="12">
          <div class="card_title">签证附件</div>
          <el-card class="box-card" shadow="never">
            <el-form-item label="">
              <sort-file-upload v-model="wgfj" can-sort ref="wgfj"></sort-file-upload>
            </el-form-item>
          </el-card>
        </el-col>
        <el-col :span="24" style="margin-top: 18px">
          <div class="card_title" style="display: flex;width: 100%;justify-content: space-between">
            <div>登记子目</div>
            <div style="font-size: 16px;margin-left: 5px">总金额：{{total}}<el-button icon="el-icon-plus" circle @click="openLibModel" v-if="!fromEvent"></el-button></div>
          </div>
          <el-table v-adjust-table
              :data="methodList"
              border
              height="200px"
              ref="tableRef"
              style="width: 100%">
            <el-table-column
                label="序号"
                align="center"
                type="index"
                width="50"
            />
            <el-table-column
                prop="schemeCode"
                align="center"
                label="子目号"
                width="100">
            </el-table-column>
            <el-table-column
                prop="schemeName"
                align="center"
                label="子目名称"
                width="100">
              <template slot-scope="scope">
                <el-input v-if="scope.row.rateFlag == 1" v-model="scope.row.schemeName">
                </el-input>
                <div v-else>{{scope.row.schemeName}}</div>
              </template>
            </el-table-column>
            <el-table-column
                prop="unit"
                align="center"
                label="单位"
                width="100">
              <template slot-scope="scope">
                <el-input v-if="scope.row.rateFlag == 1" v-model="scope.row.unit">
                </el-input>
                <div v-else>{{scope.row.unit}}</div>
              </template>
            </el-table-column>
            <el-table-column
                prop="price"
                align="center"
                label="单价"
                width="100">
              <template slot-scope="scope">
                <el-input v-if="scope.row.rateFlag == 1" v-model="scope.row.price">
                </el-input>
                <div v-else>{{scope.row.price}}</div>
              </template>
            </el-table-column>
            <el-table-column
                prop="calcDesc"
                align="center"
                label="计算式"
                width="100">
              <template slot-scope="scope">
                <el-input v-model="scope.row.calcDesc" @change="changeCalculation(scope.row)" :disabled="scope.row.schemeName == '安全生产费'||scope.row.schemeName == '安全保通费' || fromEvent">
                </el-input>
              </template>
            </el-table-column>
            <el-table-column
                prop="num"
                align="center"
                label="方法数量"
                width="100">
              <template slot-scope="scope">
                <el-input v-model="scope.row.num" @change="changeSchemeNum(scope.row)" :disabled="scope.row.schemeName == '安全生产费'||scope.row.schemeName == '安全保通费' || fromEvent">
                </el-input>
              </template>
            </el-table-column>
            <el-table-column
                prop="amount"
                align="center"
                label="资金"
                width="100">
            </el-table-column>
            <el-table-column
                prop="remark"
                align="center"
                label="备注"
                width="100">
              <template slot-scope="scope">
                <el-input v-model="scope.row.remark" :disabled="fromEvent">
                </el-input>
              </template>
            </el-table-column>
            <el-table-column
                prop="field101"
                align="center"
                label="移除">
              <template slot-scope="scope">
                <el-button
                    size="mini"
                    type="text"
                    :disabled="scope.row.schemeName == '安全生产费'||scope.row.schemeName == '安全保通费' || fromEvent"
                    @click="handleDelete(scope.row)"
                >移除
                </el-button
                >
              </template>
            </el-table-column>
          </el-table>
        </el-col>
        <el-col :span="24" style="margin-top: 18px">
          <div class="card_title" style="display: flex;width: 100%;justify-content: space-between">
            <div>计算式及说明</div>
            <el-link type="primary" @click="generateInstructions">生成计算式说明</el-link>
          </div>
          <el-input class="calculation_desc" v-model="formData.calculationDesc" type="textarea" :rows="4">
          </el-input>
        </el-col>
        <el-col :span="24" style="margin-top: 18px">
          <div class="card_title">工程内容</div>
          <el-input v-model="formData.enContent" type="textarea" :rows="4" :disabled="fromEvent">
          </el-input>
        </el-col>
        <el-col :span="24" style="margin-top: 18px">
          <div class="card_title">登记备注</div>
          <el-input v-model="formData.remark" type="textarea" :rows="4" :disabled="fromEvent">
          </el-input>
        </el-col>
      </el-row>
      <div style="text-align: right;padding-right: 7.5px;margin-top: 18px">
        <el-button type="primary" @click="onSave">保 存</el-button>
        <el-button @click="onClose">退出</el-button>
      </div>
    </el-form>
    <methods-tree scheme-type="日常养护" :con-id="conId" ref="methodsRef" @input="checkLib" :domain-id="rowData.domainId"></methods-tree>
  </div>
</template>
<script>
import sortFileUpload from "@/components/SortFileUpload/index.vue";
import moment from "moment";
import {
  addFinishedDetail,
  editFinishedDetail,
  getDetailList, getDetailListAll,
  getFinishedInfo,
} from '@/api/theft/construction'
import MethodsTree from "@/components/MethodsTree/index.vue";
import {getSafetyFeeList} from "@/api/contract/quotationSystem";
import {getDetail as getContractInfo} from "@/api/contract/info";
import {v4 as uuidv4} from "uuid";
import { Decimal } from 'decimal.js';
import { getConstructionById } from '@/api/theft/taskList'

export default {
  name: "index",
  components: {MethodsTree, sortFileUpload},
  data() {
    return {
      formData: {
        isEnd: 0,
        isProduction: 1,
        isGuarantee: 1,
        endTime: moment().format('YYYY-MM-DD')
      },
      sgqcjzp: '',
      sgjt: '',
      sgfj: '',
      wgfj: '',
      loading: false,
      methodList: [],
      taskId: '',
      conId: '',
      total: 0,
      libData: [],
      contractInfo: {},
      formRules: {
        code: [
          { required: true, message: '签证单编码不能为空', trigger: 'blur' }
        ],
        isEnd: [
          { required: true, message: '是否完结不能为空', trigger: 'change' }
        ],
        isGuarantee: [
          { required: true, message: '是否计算安全保通费不能为空', trigger: 'change' }
        ],
        isProduction: [
          { required: true, message: '是否计算安全生产费不能为空', trigger: 'change' }
        ],
        endTime: [
          { required: true, message: '修补完成时间不能为空', trigger: 'change' }
        ]
      },
      isEndOptions: [{
        "label": "否",
        "value": 0
      }, {
        "label": "是",
        "value": 1
      }],
      urgentDegreeOptions: [{
        "label": "计算",
        "value": 1
      }, {
        "label": "不计算",
        "value": 2
      }],
      defaultProps: {
        children: 'children',
        label: 'schemeName'
      },
      projectData: {},
      projectColumns: [
        { name: '项目名称', field: 'projName', dict: '' },
        { name: '任务单名称', field: 'name', dict: '' },
        { name: '任务单编码', field: 'code', dict: '' },
        { name: '路段名称', field: 'maiSecName', dict: '' },
        { name: '位置', field: 'mileRang', dict: '' },
        { name: '实施要求', field: 'exeRequire', dict: '' },
        { name: '施工单位', field: 'conDomainName', dict: '' },
        { name: '施工合同', field: 'conConName', dict: '' },
        { name: '监理单位', field: 'supDomainName', dict: '' },
        { name: '监理合同', field: 'supConName', dict: '' },
        { name: '设计单位', field: 'designDomainName', dict: '' },
        { name: '设计合同', field: 'designConName', dict: '' },
        { name: '工作内容', field: 'content', dict: '' },
      ],
    }
  },
  props: {
    rowData: {
      type: Object,
      default: () => {
        return {}
      }
    },
    detailData: {
      type: Object,
      default: () => {
        return {}
      }
    },
    fromEvent: {
      type: Boolean,
      default: () => {
        return false
      }
    }
  },
  watch: {
    rowData: {
      handler(val) {
        if (val.id) {
          getConstructionById(val.id).then(res => {
            this.projectData = res.data
            this.projectData.domainName = row.domainName
            this.projectData.maiSecName = row.maiSecName
          })
          this.$set(this.formData, 'enContent', val.enContent)
          this.formData.conId = val.id
          if (val.type == 1) {
            this.conId = val.conConId
          } else if (val.type == 2) {
            this.conId = val.checkConId
          } else if (val.type == 3) {
            this.conId = val.designConId
          }
        }
      },
      immediate: true
    },
    detailData: {
      handler(val) {
        if (val.id) {
          getFinishedInfo(val.id).then(res => {
            this.formData = res.data
            this.methodList = this.formData.finishedMethodList
            const sgqcjzp = this.formData.fileList.filter(item => item.registerType == 1)
            this.sgqcjzp = sgqcjzp.map(item => item.fileId)

            const sgfj = this.formData.fileList.filter(item => item.registerType == 2)
            this.sgfj = sgfj.map(item => item.fileId)

            const sgjt = this.formData.fileList.filter(item => item.registerType == 3)
            this.sgjt = sgjt.map(item => item.fileId)

            const wgfj = this.formData.fileList.filter(item => item.registerType == 4)
            this.wgfj = wgfj.map(item => item.fileId)
          })
        }
      },
      immediate: true
    }
  },
  created() {
    // 查询合同信息
    this.loading = true
    getContractInfo(this.rowData.conConId).then(res => {
      this.contractInfo = res.rows[0]
      if (!this.detailData.id) this.getDetail(this.rowData.id)
      this.loading = false
      this.calculate()
    })
  },
  methods: {
    getDetail(id) {
      getDetailListAll({projConId: id}).then(res => {
        res.data.forEach(item => {
          item.id = undefined
        })
        this.methodList = res.data
        // 初始化添加安全费保通费
        getSafetyFeeList(this.rowData.conConId).then(res => {
          // 过滤出schemeType为日常养护的，并且对schemeName去重
          const filteredData = res.data.filter(item => item.schemeType === '日常养护').reduce((acc, item) => {
            if (!acc.find(accItem => accItem.schemeName === item.schemeName)) {
              acc.push(item);
            }
            return acc;
          }, []).sort((a, b) => a.schemeName.localeCompare(b.schemeName));
          for (let i = 0; i < filteredData.length; i++) {
            let item = filteredData[i]
            this.methodList.splice(0, 0, {
              schemeId: item.id,
              schemeCode: item.schemeCode,
              schemeName: item.schemeName,
              unit: item.unit,
              price: item.price,
              priceRate: item.priceRate,
              decimalPlaces: item.decimalPlaces,
              isProduction: item.safetyFeeFlag
            })
          }
          this.methodList.forEach(item => {
            if (!item.id) item.id = uuidv4().replace(/-/g, '').slice(0, 20)
          })
          this.calculate()
        })
      })
    },
    onSave() {
      try {
        this.generateParams()
      } catch (e) {
        console.error('Error occurred while generating params:', e)
        return;
      }
      if (!this.formData.finishedMethodList || this.formData.finishedMethodList.length == 0 || !this.formData.calculationDesc) {
        this.$message.warning('计算式、方法不能为空')
        return
      }
      // 判断finishedMethodList 是否除了安全费、保通费没有其他子目
      const isOther = this.formData.finishedMethodList.filter(item => item.schemeName != '安全生产费' && item.schemeName != '安全保通费')
      if (isOther.length == 0) {
        this.$message.warning('未添加除安全费、保通费以外其他子目！')
        return
      }
      this.$refs.elForm.validate(valid => {
        if (!valid) return
        if (this.fromEvent) this.formData.flag = 1
        this.loading = true
        if (this.formData.id) {
          editFinishedDetail(this.formData).then(res => {
            this.loading = false
            this.$message.success('保存成功')
            this.onClose()
          })
        } else {
          addFinishedDetail(this.formData).then(res => {
            this.loading = false
            this.$message.success('保存成功')
            this.onClose()
          })
        }
      })
    },
    // 拼接参数
    generateParams() {
      this.$refs.sgqcjzp.save()
      this.$refs.sgfj.save()
      this.$refs.sgjt.save()
      this.$refs.wgfj.save()
      this.methodList = this.methodList.map(item => ({
        ...item,
        schemeId: item.schemeId ? item.schemeId : item.id
      }))
      // 拼接参数
      this.formData.finishedMethodList = this.methodList
      const fileList = []
      if (this.sgqcjzp) {
        this.sgqcjzp.forEach((item, index) => {
          fileList.push({
            fileId: item,
            indexOrder: index,
            registerType: 1
          })
        })
      }
      if (this.sgfj) {
        this.sgfj.forEach((item, index) => {
          fileList.push({
            fileId: item,
            indexOrder: index,
            registerType: 2
          })
        })
      }
      if (this.sgjt) {
        this.sgjt.forEach((item, index) => {
          fileList.push({
            fileId: item,
            indexOrder: index,
            registerType: 3
          })
        })
      }
      if (this.wgfj) {
        this.wgfj.forEach((item, index) => {
          fileList.push({
            fileId: item,
            indexOrder: index,
            registerType: 4
          })
        })
      }
      this.formData.fileList = fileList
    },
    checkLib(checkDatas) {
      const schemeList = []
      checkDatas = checkDatas.filter(item => item.nodeType === 2 && (item.rateFlag === 1 || !this.methodList.some(filterItem => filterItem.schemeCode === item.schemeCode)));
      checkDatas.forEach(item => {
        schemeList.push({
          schemeId: item.id,
          id: uuidv4().replace(/-/g, '').slice(0, 20),
          schemeCode: item.schemeCode,
          schemeName: item.schemeName,
          unit: item.unit,
          price: item.price,
          priceRate: item.priceRate,
          decimalPlaces: item.decimalPlaces,
          isProduction: item.safetyFeeFlag,
          rateFlag: item.rateFlag
        })
      })
      this.methodList.push(...schemeList)
      this.methodList = this.methodList.reduce((acc, curr) => {
        const exists = acc.some(item => item.id === curr.id);
        return exists ? acc : [...acc, curr];
      }, []);
    },
    openLibModel() {
      this.$refs.methodsRef.openLibModel()
    },
    filterNode(value, data) {
      if (!value) return true;
      return data.schemeName.indexOf(value) !== -1;
    },
    handleDelete(e) {
      this.methodList = this.methodList.filter(item => {
        return item.id != e.id
      })
      this.calculate()
    },
    async changeCalculation(row) {
      if (!this.isValidMathFormula(row.calcDesc)) {
        this.$modal.msgError('计算式错误，请检查')
        return
      }
      let num =  this.math.evaluate(row.calcDesc) || 0
      this.$set(row, 'num', this.ceilToTwo(num, row.decimalPlaces))
      await this.changeSchemeNum(row)
    },
    async changeSchemeNum(row) {
      let money = new Decimal(row.num || 0).times(row.price || 0).toNumber()
      this.$set(row, 'amount', Math.round(money))
      if (row.schemeName != '安全生产费' && row.schemeName != '安全保通费') {
        this.calculate()
      }
    },
    calculate () {
      let lib1 = this.methodList.find(item => item.schemeName == '安全生产费')
      if (lib1) {
        this.$set(lib1, 'amount', 0)
      // 计算安全费和保通费
        if (this.formData.isProduction == '1') {
          let aqf = 0
          for (let i = 0; i < this.methodList.length; i++) {
            let item = this.methodList[i]
            if( item.isProduction == '1' && item.schemeName != '安全生产费' && item.schemeName != '安全保通费') {
              aqf += (lib1.amount || 0) + item.amount
            }
          }
          this.$set(lib1, 'amount', Math.round(aqf * this.contractInfo.dSafeProductionRate))
        }
      }
      let lib2 = this.methodList.find(item => item.schemeName == '安全保通费')
      if (lib2) {
        this.$set(lib2, 'amount', 0)
        if (this.formData.isGuarantee == '1') {
          let aqf = 0
          for (let i = 0; i < this.methodList.length; i++) {
            let item = this.methodList[i]
            if( item.isProduction == '1' && item.schemeName != '安全生产费' && item.schemeName != '安全保通费') {
              aqf += (lib2.amount || 0) + item.amount
            }
          }
          this.$set(lib2, 'amount', Math.round(aqf * this.contractInfo.dSafeGuaranteeRate))
        }
      }
      this.total = this.methodList.reduce((acc, curr) => Number(acc) + Number(curr.amount), 0)

    },
    onClose() {
      this.$emit("close")
    },
    // 生成计算式说明
    generateInstructions() {
      let calculationDesc = ''
      this.methodList.forEach(item => {
        if (item.schemeName != '安全生产费' && item.schemeName != '安全保通费')
        calculationDesc += `${item.schemeName || ''}:(${item.calcDesc || item.num || ''})=${item.num || 0}${item.unit || ''}\n`
      })
      this.$set(this.formData, 'calculationDesc', calculationDesc)
    }
  }
}
</script>
<style scoped lang="scss">
.card_title {
  width: 200px;
  text-align: left;
  margin-bottom: 15px;
  font-weight: bold;
}
</style>
<style lang="scss" scoped>
@import "@/assets/styles/business.scss";
</style>
