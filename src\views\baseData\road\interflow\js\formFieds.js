// 基本信息
const baseInfo = [
    {
        label: '养护路段',
        prop: 'maintenanceSectionId',
        placeholder: '请选择养护路段',
        type: 'select',
        rules: [{required: true, message: '请选择养护路段', trigger: 'change'}],
        options: [],
        api: 'maintenanceSectionList',
        optionLabel: 'maintenanceSectionName',
        optionValue: 'maintenanceSectionId',
        disabledFieds: 'managementMaintenanceBranchId'
    },
    {
        label: '路线编码',
        prop: 'routeCode',
        placeholder: '请选择路线编码',
        type: 'select',
        rules: [{required: true, message: '请选择路线编码', trigger: 'change'}],
        options: [],
        api: 'routeListAll',
        optionLabel: 'routeCode',
        optionValue: 'routeCode',
        disabledFieds: 'maintenanceSectionId'
    },
    {
        label: '路线名称',
        prop: 'routeName',
        placeholder: '请输入路线名称',
        type: 'select',
        api: 'routeListAll',
        optionLabel: 'routeName',
        optionValue: 'routeName',
        disabledFieds: '',
    },
    {
        label: '互通编码',
        prop: 'interFlowCode',
        placeholder: '请输入互通编码',
        type: 'input',
    },
    {
        label: '互通名称',
        prop: 'interFlowName',
        placeholder: '请选择互通名称',
        rules: [{required: true, message: '请选择互通名称', trigger: 'change'}],
        type: 'input',
    },

    {
        label: '互通形式',
        prop: 'interFlowModality',
        placeholder: '请输入互通形式',
        type: 'input',
    },
]


const baseInfo1 = [

    {
        label: '巡查方向',
        prop: 'direction',
        placeholder: '请选择巡查方向',
        type: 'dictSelect',
        dict: 'sys_route_direction'
    },
    {
        label: '位置',
        prop: 'lane',
        placeholder: '请选择',
        type: 'dictSelect',
        dict: 'lane'
    },

    {
        label: '左右',
        prop: 'leftOrRight',
        placeholder: '请选择',
        type: 'dictSelect',
        dict: 'left_right'
    },
    // {
    //     label: '经纬度',
    //     propLon: 'longitude',
    //     propLat: 'latitude',
    //     placeholder: '经纬度',
    //     type: 'coordinate',
    //     prepend: 'lonlat',
    //     // span: 24
    // },
    {
        label: '起点桩号',
        prop: 'startStake',
        placeholder: '请输入起点桩号',
        type: 'pileInput',
        rules: { required: true, message: '请输入起点桩号', trigger: 'change' }
    },

    {
        label: '施工里程桩号',
        prop: 'constructionStake',
        placeholder: '请输入施工里程桩号',
        type: 'pileInput',
    },

    {
        label: '国高网桩号',
        prop: 'nationalNetworkStake',
        placeholder: '请输入国高网桩号',
        type: 'pileInput',
    },

    {
        label: '统一里程桩号',
        prop: 'unifiedMileageStake',
        placeholder: '请输入统一里程终点桩号',
        type: 'pileInput',
    },
    {
        label: '被交叉道',
        prop: 'crossingRoadBy',
        placeholder: '请输入被交叉道',
        type: 'input',
    },
    {
        label: '交叉方式',
        prop: 'crossingRoadWay',
        placeholder: '请输入交叉方式',
        type: 'input',
    },
    {
        label: '交叉点桩号',
        prop: 'overlapPointStake',
        placeholder: '请输入交叉点桩号',
        type: 'input',
    },
    {
        label: '交角(°)',
        prop: 'overlapAngle',
        placeholder: '请输入交角(°)',
        type: 'input',
    },

    {
        label: '建成时间',
        prop: 'buildDate',
        placeholder: '选择建成时间',
        type: 'date',
    },

    {
        label: '移交管理单位',
        prop: 'transferManagementUnit',
        placeholder: '请输入移交管理单位',
        type: 'input',
    },
    {
        label: '备注',
        prop: 'remark',
        placeholder: '请输入备注',
        type: 'inputTextarea',
        span:12
    },

    {
        label: '示例图像',
        prop: 'samplePictureId',
        placeholder: '照片',
        type: 'uploadImg',
        storagePath:"/base/road/interflow"
    },
]

export default {
    baseInfo,
    baseInfo1
}
