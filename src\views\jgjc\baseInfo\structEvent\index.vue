<template>
  <div class='app-container'>
    <el-row :gutter='20'>
      <!--部门数据-->
      <el-col :span='relaNav ? 4 : 0' :xs='24' class='leftDiv'>
        <!--折叠图标-->
        <div class='leftIcon' @click='relaNav = false'>
          <span class='el-icon-caret-left'></span>
        </div>
        <div class='head-container' style='width: 300px'>
          <el-tree
            ref='tree'
            :data='filteredTreeData'
            :default-expanded-keys='[1]'
            :expand-on-click-node='false'
            :props='defaultProps'
            highlight-current
            node-key='code'
            @node-click='handleNodeClick'
          >
          </el-tree>
        </div>
      </el-col>
      <!--角色数据-->
      <el-col :span='relaNav ? 20 : 24' :xs='24'>
        <!--展开图标-->
        <div v-show='!relaNav' class='rightIcon' @click='relaNav = true'>
          <span class='el-icon-caret-right'></span>
        </div>
        <el-row :gutter='10' class='mb8'>
          <el-col :span='1.5'>
            <el-button
              icon='el-icon-plus'
              size='mini'
              type='primary'
              @click='handleAdd'
            >新增
            </el-button>
          </el-col>
          <el-col :span='1.5'>
            <el-button
              icon='el-icon-delete'
              size='mini'
              type='danger'
              @click='handleDelete'
            >删除
            </el-button>
          </el-col>
        </el-row>
        <!-- 筛选区结束 -->
        <!-- 数据表格开始 -->
        <div class='tableDiv'>
          <el-table v-adjust-table v-loading='loading' :data='dataList'
                    :height="'calc(100vh - 260px)'" border size='mini'
                    @selection-change='handleSelectionChange'
                    style='width: 100%'>
            <el-table-column align='center' fixed='left' type='selection' width='55'></el-table-column>
            <el-table-column align='center' fixed label='序号' type='index' width='100'></el-table-column>
            <template v-for='(column, index) in columns'>
              <el-table-column v-if='column.visible' :key='index' :label='column.label' :prop='column.field'
                               align='center' show-overflow-tooltip>
                <template slot-scope="scope">
                  <dict-tag v-if="column.dict" :options="dict.type[column.dict]" :value="scope.row[column.field]"/>
                  <template v-else-if="column.slots">
                    <RenderDom :index="index" :render="column.render" :row="scope.row"/>
                  </template>
                  <span v-else-if="column.isTime">{{ parseTime(scope.row[column.field], '{y}-{m}-{d}') }}</span>
                  <span v-else>{{ scope.row[column.field] }}</span>
                </template>
              </el-table-column>
            </template>
            <el-table-column align='center' fixed='right' label='操作' width='100'>
              <template slot-scope='scope'>
                <el-button
                  size='mini'
                  type='text'
                  icon='el-icon-edit'
                  @click='handleEdit(scope.row)'
                >修改
                </el-button>
              </template>
            </el-table-column>
          </el-table>
          <pagination
            v-show='total > 0'
            :limit.sync='queryParams.pageSize'
            :page.sync='queryParams.pageNum'
            :total='total'
            @pagination='handleQuery'
          />
        </div>
        <!-- 数据表格结束 -->
      </el-col>
    </el-row>
    <el-dialog
      :title='dialogTitle'
      :visible.sync='dialogVisible'
      append-to-body
      destroy-on-close
      width='50%'
    >
      <el-form
        ref='elForm'
        :model='formData'
        :rules='rules'
        label-width='140px'
        size='medium'
      >
        <el-form-item label='事件类型' prop='incidentType'>
          <dict-select
            v-model='formData.incidentType'
            :style="{ width: '100%' }"
            type='incident_type'
          />
        </el-form-item>

        <el-form-item label='事件起始' prop='timeRange'>
          <el-date-picker
            v-model='formData.timeRange'
            type='datetimerange'
            range-separator='至'
            start-placeholder='开始时间'
            end-placeholder='结束时间'
            :style="{ width: '100%' }"
            value-format='yyyy-MM-dd HH:mm:ss'
          />
        </el-form-item>
      </el-form>

      <div style='text-align: right'>
        <el-button @click='closeDialog'>取消</el-button>
        <el-button type='primary' @click='handleConfirm'>确定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getJCDomainTree } from '@/api/jgjc/baseInfo/alarmLog'
import {
  getIncidentRecordPage,
  deleteIncidentRecord,
  updateIncidentRecord,
  saveIncidentRecord,
} from '@/api/jgjc/baseInfo/structEvent'

export default {
  name: 'ContentManagement',
  dicts: ['incident_type'],
  data() {
    return {
      // 遮罩层
      loading: false,
      // 左侧组织树
      relaNav: true,
      relaOptions: [],
      filteredTreeData: [],
      defaultProps: {
        children: 'children',
        label: 'label',
      },
      loadedKeys: new Set(), // 记录已加载过的节点key，避免重复加载
      // 总条数
      total: 0,
      dataList: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
      },
      // 列信息
      columns: [
        { key: 0, field: 'incidentType', label: '事件类型', visible: true, dict: 'incident_type' },
        { key: 1, field: 'startTime', label: '事件开始时间', visible: true },
        { key: 1, field: 'endTime', label: '事件结束时间', visible: true },
        { key: 2, field: 'createTime', label: '创建时间', visible: true },
        { key: 3, field: 'updateTime', label: '更新时间', visible: true },
      ],
      ids: [],
      formData: {},
      dialogTitle: '新增',
      dialogVisible: false,
      rules: {
        incidentType: [
          { required: true, message: '请选择事件类型', trigger: 'change' },
        ],
        timeRange: [
          { required: true, message: '请选择起始时间', trigger: 'change' },
        ],
      },
      structureId: '',
    }
  },
  created() {
    this.getDeptTree()
  },
  methods: {
    // 查询部门下拉树结构
    getDeptTree() {
      getJCDomainTree({}).then(response => {
        this.relaOptions = response.data
        this.filteredTreeData = [...this.relaOptions]
      })
    },
    // 树节点点击事件
    handleNodeClick(nodeData, node) {
      if (node.level == 4) {
        this.structureId = nodeData.structureId
        this.queryParams = {
          pageNum: 1,
          pageSize: 10,
          structureId: nodeData.structureId,
        }
        this.handleQuery()
      }
    },
    handleQuery() {
      this.loading = true
      getIncidentRecordPage(this.queryParams).then(res => {
        this.dataList = res.rows
        this.total = res.total
        this.loading = false
      }).catch(err => {
        this.loading = false
        console.error(err)
      })
    },
    resetQuery() {
      this.queryParams = {
        pageNum: 1,
        pageSize: 10,
        title: '',
      }
      this.handleQuery()
    },
    handleSelectionChange(e) {
      this.ids = e.map(item => item.id)
    },
    handleAdd() {
      if (!this.structureId) return this.$message.error('请选择结构物')
      this.dialogTitle = '新增'
      this.formData = {
        structureId: this.structureId,
      }
      this.dialogVisible = true
    },
    handleEdit(row) {
      this.dialogTitle = '修改'
      this.formData = { ...row }
      if (this.formData.startTime && this.formData.endTime) this.$set(this.formData, 'timeRange', [this.formData.startTime, this.formData.endTime])
      this.dialogVisible = true
    },
    handleConfirm() {
      this.$refs.elForm.validate(valid => {
        if (valid) {
          if (this.formData.timeRange) {
            this.formData.startTime = this.formData.timeRange[0] || null
            this.formData.endTime = this.formData.timeRange[1] || null
          }
          if (this.dialogTitle === '新增') {
            saveIncidentRecord(this.formData).then(res => {
              if (res.code === 200) {
                this.$message.success('新增成功')
                this.handleQuery()
                this.closeDialog()
              } else {
                this.$message.error(res.msg)
              }
            })
          } else {
            updateIncidentRecord(this.formData).then(res => {
              if (res.code === 200) {
                this.$message.success('修改成功')
                this.handleQuery()
                this.closeDialog()
              } else {
                this.$message.error(res.msg)
              }
            })
          }
        }
      })
    },
    closeDialog() {
      this.dialogVisible = false
      this.formData = {}
    },
    handleDelete() {
      if (this.ids.length <= 0) {
        this.$message.warning('请勾选需要删除的数据')
        return
      }
      this.$confirm('是否确认删除选中的数据？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }).then(() => {
        this.loading = true
        deleteIncidentRecord({ ids: this.ids }).then(res => {
          if (res.code === 200) {
            this.$message.success('删除成功')
            this.handleQuery()
          } else {
            this.$message.error(res.msg)
          }
          this.loading = false
        }).catch(err => {
          this.loading = false
          console.error(err)
        })
      })
    },
  },
}
</script>

<style scoped>
.leftDiv {
  border-right: 1px solid #d8dce5;
  min-height: calc(100vh - 100px);
  overflow-y: auto;
  height: calc(80vh - 150px);
  position: relative;
  top: -20px;
  padding-top: 10px;
  background-color: white;
}

.leftIcon {
  border: 1px solid #dcdfe6;
  border-radius: 8px;
  width: 16px;
  height: 50px;
  line-height: 50px;
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
  position: absolute;
  right: 0;
  top: 300px;
  z-index: 2;
}

.leftIcon:hover {
  background-color: #dcdfe6;
}

.rightIcon {
  border: 1px solid #dcdfe6;
  border-radius: 8px;
  width: 16px;
  height: 50px;
  line-height: 50px;
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
  position: absolute;
  left: -10px;
  top: 280px;
  z-index: 10;
  background: white;
}

.rightIcon:hover {
  background-color: #dcdfe6;
}

.tableDiv {
  margin-top: 20px;
}
</style>
<style lang='scss' scoped>
@import "@/assets/styles/business.scss";
</style>
