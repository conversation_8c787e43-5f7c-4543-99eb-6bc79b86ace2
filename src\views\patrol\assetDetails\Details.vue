<template>
	<div class="app-container">
		<!-- 查询 -->
		<div class="searchBox">
			<CascadeSelection style="grid-column: 1 / 3" :form-data="queryForm" v-model="queryForm" types="201" multiple />
			<el-input v-model="queryForm.assetCode" :placeholder="`请输入${partsType[type].substring(0, 2)}编码`" clearable
				prefix-icon="el-icon-user" @keyup.enter.native="handleQuery" />
			<el-input v-model="queryForm.assetName" :placeholder="`请输入${partsType[type].substring(0, 2)}名称`" clearable
				prefix-icon="el-icon-user" @keyup.enter.native="handleQuery" />
			<el-select v-model="queryForm.inspectionType" placeholder="请选择检查类型" @change="handleInspectionTypeChange"
				:clearable="false">
				<template v-if="type !== 3">
					<el-option :label="type === 1 ? '桥梁经常检查' : '隧道经常检查'" :value="type === 1 ? 2 : 6" />
					<el-option :label="type === 1 ? '桥梁日常巡查' : '隧道日常巡查'" :value="type === 1 ? 1 : 5" />
				</template>
				<template v-else>
					<el-option label="涵洞经常检查" value="4" />
				</template>
			</el-select>
			<el-date-picker v-model="queryForm.expiry" :type="datePickerType"
				:placeholder="`请选择${partsType[type].substring(0, 2)}${datePickerPlaceholder}`" :clearable="false"
				prefix-icon="el-icon-date" :value-format="datePickerFormat" @keyup.enter.native="handleQuery" />
			<el-select v-model="queryForm.isInspected" placeholder="请选择检查状态" clearable @change="handleInspectionStatusChange">
				<el-option label="已检查" :value="true" />
				<el-option label="未检查" :value="false" />
			</el-select>
			<DictSelect v-model="queryParams.status" type="patrol_inspection_status" placeholder="请选择状态" clearable
				:disabled="!queryForm.isInspected" />
			<div style="display: flex; gap: 10px">
				<el-button type="primary" icon="el-icon-search" size="mini" @click="queryhandle">
					搜索
				</el-button>
				<el-button icon="el-icon-refresh" size="mini" @click="queryReset">重置</el-button>
			</div>
		</div>
		<!-- 主表数据 -->
		<div class="tableDiv" :style="{ paddingTop: '10px' }">
			<iframe :src="src" v-if="showIframe" id="reportView" frameborder="no" style="width: 100%; height: 100%"
				scrolling="auto" />
		</div>
	</div>
</template>

<script>
import moment from 'moment'
import CascadeSelection from '@/components/CascadeSelection/index.vue'
import { getToken } from '@/utils/auth'

export default {
	name: 'AssetLedger',
	dicts: ['patrol_inspection_status'],
	components: {
		CascadeSelection,
	},
	props: {
		type: {
			type: Number,
			default: 2,
		},
	},
	data() {
		return {
			queryShow: false,
			showIframe: true,
			partsType: {
				1: '桥梁检查',
				2: '隧道检查',
				3: '涵洞检查',
			},
			queryForm: {
				type: this.type,
				assetName: null,
				assetCode: null,
				expiry: moment().format('YYYY-MM'),
				managementMaintenanceIds: null,
				maintenanceSectionId: null,
				routeCodes: null,
				status: null,
				inspectionType: null,
				isInspected: null,
			},
			// 查询参数
			queryParams: {
				type: this.type,
				assetName: null,
				assetCode: null,
				expiry: moment().format('YYYY-MM'),
				managementMaintenanceIds: null,
				maintenanceSectionId: null,
				routeCodes: null,
				status: null,
				inspectionType: null,
				isInspected: null,
			},
			datePickerType: 'year', // 默认年份选择
			datePickerFormat: 'yyyy',
			datePickerPlaceholder: '年份',
		}
	},
	computed: {
		src() {
			var reportId = process.env.VUE_APP_REPORT_VIEWER_REPORT_ID_ASSET_INFO
			var params = `${process.env.VUE_APP_REPORT_VIEWER_URL}${reportId}?token=${getToken()}`
			params += '&type=' + this.queryParams.inspectionType
			params +=
				'&time=' +
				(this.queryParams.expiry.length <= 7
					? moment(this.queryParams.expiry).endOf('month').format('YYYY-MM-DD HH:mm:ss')
					: moment(this.queryParams.expiry).endOf('day').format('YYYY-MM-DD HH:mm:ss'))
			if (this.queryParams.isInspected) {
				params += '&isCheck=' + this.queryParams.isInspected
			}
			if (this.queryParams.assetName) {
				params += '&name=' + this.queryParams.assetName
			}
			if (this.queryParams.assetCode) {
				params += '&code=' + this.queryParams.assetCode
			}
			if (this.queryParams.maintenanceSectionId) {
				params += '&sectionId=' + this.queryParams.maintenanceSectionId
			}
			if (this.queryParams.status) {
				params += '&status=' + this.queryParams.status
			}
			if (this.queryParams.routeCodes && this.queryParams.routeCodes.length) {
				// 确保是数组并且不是空字符串再进行连接
				params +=
					'&routeCodeStr=' +
					(Array.isArray(this.queryParams.routeCodes)
						? this.queryParams.routeCodes.filter((code) => code).join(',')
						: this.queryParams.routeCodes)
			}

			if (
				this.queryParams.managementMaintenanceIds &&
				this.queryParams.managementMaintenanceIds.length
			) {
				// 确保是数组并且不是空字符串再进行连接
				params +=
					'&manageIdStr=' +
					(Array.isArray(this.queryParams.managementMaintenanceIds)
						? this.queryParams.managementMaintenanceIds.filter((id) => id).join(',')
						: this.queryParams.managementMaintenanceIds)
			}
			console.log('积木报表路径为:', params)
			return params
		},
	},
	created() {
		// 获取所有可能的 URL 查询参数
		const {
			type,
			assetName,
			assetCode,
			expiry,
			managementMaintenanceIds,
			maintenanceSectionId,
			routeCodes,
			status,
			inspectionType,
			isInspected,
		} = this.$route.query

		// 根据 inspectionType 确定 type 值
		let derivedType = this.type
		if (inspectionType) {
			const inspectionTypeNum = Number(inspectionType)
			if ([1, 2].includes(inspectionTypeNum)) {
				derivedType = 1 // 桥梁
			} else if ([3, 4].includes(inspectionTypeNum)) {
				derivedType = 3 // 涵洞
			} else if ([5, 6].includes(inspectionTypeNum)) {
				derivedType = 2 // 隧道
			}
		}

		// 创建一个包含 URL 参数的对象
		const urlParams = {
			type: derivedType,
			assetName: assetName || null,
			assetCode: assetCode || null,
			expiry: expiry || moment().format('YYYY-MM'),
			managementMaintenanceIds: managementMaintenanceIds
				? managementMaintenanceIds.split(',')
				: null,
			maintenanceSectionId: maintenanceSectionId || null,
			routeCodes: routeCodes ? routeCodes.split(',') : null,
			status: status || null,
			inspectionType: inspectionType ? Number(inspectionType) : null,
			isInspected: isInspected === undefined ? null : isInspected === 'undefined' ? false : true,
		}

		// 更新 queryForm 和 queryParams
		this.queryForm = { ...this.queryForm, ...urlParams }
		this.queryParams = { ...this.queryParams, ...urlParams }

		// 原有的初始化逻辑
		if (!this.queryForm.inspectionType) {
			this.queryForm.inspectionType = this.type === 3 ? 4 : this.type === 1 ? 2 : 6
			this.queryParams.inspectionType = this.queryForm.inspectionType
		}
		this.handleInspectionTypeChange(this.queryForm.inspectionType)
	},
	methods: {
		queryhandle() {
			this.queryParams = { ...this.queryForm }
		},
		// 重置查询条件
		queryReset() {
			this.queryForm = {
				type: this.type,
				assetName: null,
				assetCode: null,
				expiry: moment().format('YYYY-MM'),
				managementMaintenanceIds: null,
				maintenanceSectionId: null,
				routeCodes: null,
				status: null,
				inspectionType: null,
				isInspected: null,
			}
			this.queryhandle()
		},
		handleInspectionStatusChange(val) {
			this.queryForm.isInspected = val
			// 如果选择未检查或清空，将状态设为 null
			if (!val) {
				this.queryForm.status = null
			}
		},
		handleInspectionTypeChange(val) {
			// 根据检查类型设置日期选择器类型
			if (val === 1 || val === 5) {
				// 日常巡查
				this.datePickerType = 'date'
				this.datePickerFormat = 'yyyy-MM-dd'
				this.datePickerPlaceholder = '日期'
				// 设置为当天
				this.queryForm.expiry = moment().format('YYYY-MM-DD')
			} else {
				// 经常检查
				this.datePickerType = 'month'
				this.datePickerFormat = 'yyyy-MM'
				this.datePickerPlaceholder = '月份'
				// 设置为当月
				this.queryForm.expiry = moment().format('YYYY-MM')
			}
		},
	},
}
</script>

<style lang="scss" scoped>
.app-container form:first-child .el-select,
.app-container form:nth-child(2) .el-select,
.app-container form:nth-child(2) ::v-deep .el-form-item__content,
.app-container form:first-child ::v-deep .el-form-item__content {
	width: 240px;
}

.app-container form:first-child .el-form-item:last-child ::v-deep .el-form-item__content {
	width: auto;
}

.app-container {
	display: flex;
	flex-direction: column;
	padding: 10px;
	background-color: #c0c0c0;
	box-sizing: border-box;
}

.formDialog {
	::v-deep .el-dialog__body {
		height: 600px;
		overflow-y: auto;
	}

	.dialog-footer {
		width: 100%;

		.footerTip {
			color: #888888;
			font-size: 14px;
		}
	}

	.titleBox {
		height: 22px;
		position: relative;
		display: flex;
		flex-direction: row;
		align-items: center;

		.title {
			font-size: 16px;
			color: black;
			margin: 0;
		}

		.subTitle {
			margin-left: 15px;
			font-size: 12px;
			color: #888888;
		}

		.riskLevel {
			user-select: none;
			position: absolute;
			// top: 0;
			right: 5%;
			display: flex;
			align-items: center;
			flex-direction: row;

			.title {
				font-size: 16px;
				font-weight: bold;
			}

			.main {
				font-size: 16px;
				font-weight: bold;
				padding: 5px 10px 5px 10px;
				color: white;
				box-sizing: border-box;
				border-radius: 5px;
			}

			.score {
				color: #888888;
				font-size: 14px;
			}
		}
	}
}

.searchBox {
	padding: 10px;
	background: #fff;
	border-radius: 10px;
	transition: all 0.1s linear;
	display: grid;
	gap: 20px;
	grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));

	.searchMoreBox {
		min-width: 192px;
		margin-top: 10px;
		display: flex;
		align-items: center;
		flex-direction: row;
	}
}

.tableDiv {
	flex: 1;
	margin-top: 10px;
	background-color: white;
	padding-bottom: 10px;
	border-radius: 10px;
	transition: all 0.1s linear;
	display: flex;
	flex-direction: column;

	.btnBox {
		padding: 10px;
	}
}

.infoBox {
	padding: 15px 15px 0 15px;
	box-sizing: border-box;
	border-radius: 6px;
	border: 1px solid #c4c4c4;
	position: relative;

	.infoTitle {
		user-select: none;
		position: absolute;
		top: 0;
		left: 0;
		padding: 0 10px;
		font-size: 14px;
		line-height: 14px;
		font-weight: bold;
		transform: translateX(15px) translateY(-50%);
		background-color: white;
	}

	.imgBox {
		height: auto;
		width: 100%;
		display: flex;
		flex-wrap: wrap;
		align-content: flex-start;

		.imgItemBox {
			height: 240px;
			width: calc(100% / 3);
			box-sizing: border-box;
			padding: 10px;
			overflow-y: auto;
			display: flex;
			align-items: center;
			justify-content: center;
			position: relative;

			.imgDeleteBtn {
				position: absolute;
				z-index: 1;
				top: 10%;
				right: 10%;
			}
		}
	}
}

.coordinateDialog {
	.coordinateMap {
		height: 600px;
		width: 100%;
		position: relative;

		#coordinateBox {
			height: 100%;
			width: 100%;
			border-radius: 5px;
			position: relative;
			z-index: 0;
		}

		.coordinateSearch {
			position: absolute;
			z-index: 1;
			top: 10px;
			left: 10px;
			width: 50%;
			padding: 10px;
			box-sizing: border-box;
			background-color: #fff;
			border-radius: 5px;
			border: 1px solid #dcdfe6;
			box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.12), 0 0 6px 0 rgba(0, 0, 0, 0.04);
			display: flex;
			flex-direction: row;
		}

		.coordinateTip {
			position: absolute;
			z-index: 1;
			top: 10px;
			right: 10px;
			padding: 10px;
			box-sizing: border-box;
			background-color: #fff;
			border-radius: 5px;
			border: 1px solid #dcdfe6;
			box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.12), 0 0 6px 0 rgba(0, 0, 0, 0.04);
		}
	}
}

// v-if过渡动画
// 查询框
.search-enter-active {
	transition: all 0.1s linear;
}

.search-enter {
	opacity: 0;
}

.search-leave-active {
	transition: all 0.1s linear;
}

.search-leave-to {
	opacity: 0;
}

::v-deep .treeselect-main {
	line-height: 28px;
	font-size: 12px;
}

::v-deep .vue-treeselect__placeholder {
	line-height: 28px;
	color: #1d2129;
}

::v-deep .vue-treeselect__input {
	line-height: 28px;
}

::v-deep .vue-treeselect__control {
	height: 28px;
	font-size: 12px;
	font-weight: 400;
}

::v-deep .vue-treeselect__single-value {
	line-height: 28px;
}

::v-deep .vue-treeselect__menu-container {
	font-family: Arial;
	color: #1d2129;
}
</style>
