<template>
  <div class="app-container">
    <el-form ref="elForm" :model="formData" :rules="rules" :inline="true" label-width="200px" v-loading="loading">
      <el-row :gutter="20">
        <el-col :span="24" class="mb20">
          <div class="card_title">项目信息</div>
          <el-descriptions size="mini" :column="3" border :labelStyle="{width: '150px'}" :contentStyle="{width: '300px'}">
            <el-descriptions-item v-for="(item, index) in projectColumns" :key="index" :label="item.name">
              <el-tooltip :content="projectData[item.field]" placement="top">
                <dict-tag v-if="item.dict" :options="dict.type[item.dict]" :value="projectData[item.field]"/>
                <span v-else>{{ projectData[item.field] }}</span>
              </el-tooltip>
            </el-descriptions-item>
          </el-descriptions>
        </el-col>
        <el-col :span="12">
          <el-form-item label="签证人员" prop="visaByArr">
            <el-cascader
                v-model="formData.visaByArr"
                :options="deptUserOptions"
                :props="{
                multiple: true,
                value: 'id',
                emitPath: false
              }"
                :show-all-levels="false"
                ref="visa"
                filterable clearable :disabled="fromEvent" style="width: 380px"></el-cascader>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="签证单审核人" prop="visaCheckBy">
            <el-cascader
                v-model="formData.visaCheckBy"
                :options="deptUserOptions"
                :props="props"
                :show-all-levels="false"
                ref="visaCheck"
                filterable clearable :disabled="fromEvent" style="width: 280px"></el-cascader>
          </el-form-item>
        </el-col>
        <el-col :span="24" style="margin-bottom: 18px">
          <div class="card_title">施工前图片</div>
          <el-card class="box-card" shadow="never">
            <el-form-item label="">
              <sort-file-upload v-model="sgqcjzp" can-sort ref="sgqcjzp" for-view></sort-file-upload>
            </el-form-item>
          </el-card>
        </el-col>
        <el-col :span="24" style="margin-bottom: 18px">
          <div class="card_title">施工后图片</div>
          <el-card class="box-card" shadow="never">
            <el-form-item label="">
              <sort-file-upload v-model="sgfj" can-sort ref="sgfj" for-view></sort-file-upload>
            </el-form-item>
          </el-card>
        </el-col>
        <el-col :span="24">
          <div class="card_title">签证附件</div>
          <el-card class="box-card" shadow="never">
            <el-form-item label="">
              <sort-file-upload v-model="wgfj" can-sort ref="wgfj" for-view></sort-file-upload>
            </el-form-item>
          </el-card>
        </el-col>
        <el-col :span="24">
          <div class="card_title">验收附件</div>
          <el-card class="box-card" shadow="never">
            <el-form-item label="">
              <sort-file-upload v-model="shfj" can-sort ref="shfj"></sort-file-upload>
            </el-form-item>
          </el-card>
        </el-col>
        <el-col :span="24" style="margin-top: 18px">
          <el-tabs v-if="finishedStructureList.length > 0" v-model="editableTabsValue">
            <el-tab-pane v-for="item in finishedStructureList" :label="item.name" :key="item.name" :name="item.name" >
              <div class="card_title" style="display: flex;width: 100%;justify-content: space-between">
                <div>登记子目</div>
              </div>
              <methods-list editSafetyFee :value.sync="item.finishedMethodList":read-only="true"></methods-list>
              <el-col :span="24" style="margin-top: 18px">
                <div class="card_title">养护措施</div>
                <el-input v-model="item.maintainMeasureName" placeholder="请选择养护措施" readonly style="width: 240px">
                </el-input>
              </el-col>
            </el-tab-pane>
          </el-tabs>
          <template v-else>
            <div class="card_title" style="display: flex;width: 100%;justify-content: space-between">
              <div>登记子目</div>
            </div>
            <methods-list editSafetyFee :value.sync="methodList" :read-only="true"></methods-list>
          </template>
        </el-col>
        <el-col :span="24" style="margin-top: 18px">
          <div class="card_title" style="display: flex;width: 100%;justify-content: space-between">
            <div>计算式及说明</div>
          </div>
          <el-input class="calculation_desc" v-model="formData.calculationDesc" type="textarea" :rows="4" readonly>
          </el-input>
        </el-col>
        <el-col :span="24" style="margin-top: 18px">
          <div class="card_title">验收备注</div>
          <el-input v-model="formData.comment" type="textarea" :rows="4" :disabled="fromEvent">
          </el-input>
        </el-col>
        <el-col :span="24" style="margin-top: 18px">
          <div class="card_title">路政反馈</div>
          <el-input v-model="formData.feedback" type="textarea" :rows="4" :disabled="fromEvent">
          </el-input>
        </el-col>
      </el-row>
      <div style="text-align: right;padding-right: 7.5px;margin-top: 18px">
        <el-button v-if="!fromEvent" type="primary" @click="handleExamine(true)">通过</el-button>
        <el-button v-if="!fromEvent" type="danger" @click="handleExamine(false)">驳回</el-button>

        <el-button @click="onClose">退出</el-button>
      </div>
    </el-form>
  </div>
</template>
<script>
import sortFileUpload from "@/components/SortFileUpload/index.vue";
import { finishedProcess, getFinishedInfo } from "@/api/maintenanceProject/construction/completionReg";
import {getTreeStruct} from "@/api/tmpl";
import MethodsList from "@/components/MethodsList/index.vue";
import {getConstructionById} from "@/api/maintenanceProject/taskList";

export default {
  name: "index",
  components: {MethodsList, sortFileUpload},
  data() {
    return {
      formData: {},
      sgqcjzp: '',
      sgfj: '',
      wgfj: '',
      shfj: '',
      loading: false,
      props: {
        multiple: false,//是否多选
        value: "id",
        emitPath: false
      },
      // 部门-用户树选项
      deptUserOptions: [],
      taskId: '',
      rules: {
        visaByArr: [{
          required: true,
          message: '请选择签证人',
          trigger: 'change'
        }],
        visaCheckBy: [{
          required: true,
          message: '请选择签证审核人',
          trigger: 'change'
        }],
      },
      finishedStructureList: [],
      editableTabsValue: '',
      methodList: [],
      projectColumns: [
        { name: '项目名称', field: 'projName', dict: '' },
        { name: '任务单名称', field: 'name', dict: '' },
        { name: '任务单编码', field: 'code', dict: '' },
        { name: '路段名称', field: 'maiSecName', dict: '' },
        { name: '位置', field: 'mileRange', dict: '' },
        { name: '实施要求', field: 'exeRequire', dict: '' },
        { name: '施工单位', field: 'conDomainName', dict: '' },
        { name: '施工合同', field: 'conConName', dict: '' },
        { name: '监理单位', field: 'supDomainName', dict: '' },
        { name: '监理合同', field: 'supConName', dict: '' },
        { name: '设计单位', field: 'designDomainName', dict: '' },
        { name: '设计合同', field: 'designConName', dict: '' },
        { name: '工作内容', field: 'content', dict: '' },
      ],
      projectData: {}
    }
  },
  props: {
    rowData: {
      type: Object,
      default: () => {
        return {}
      }
    },
    fromEvent: {
      type: Boolean,
      default: () => {
        return false
      }
    }
  },
  watch: {
    rowData: {
      handler(val) {
        if (val.id) {
          getConstructionById(val.conId).then(res => {
            this.projectData = res.data
          })
          getFinishedInfo(val.id).then(res => {
            this.formData = res.data
            this.methodList = this.formData.constructionFinishedMethodList || []
            this.finishedStructureList = this.formData.finishedStructureList || []
            if (this.finishedStructureList.length > 0) this.editableTabsValue = this.finishedStructureList[0].name

            const sgqcjzp = this.formData.fileList.filter(item => item.registerType == 1)
            this.sgqcjzp = sgqcjzp.map(item => item.fileId)

            const sgfj = this.formData.fileList.filter(item => item.registerType == 2)
            this.sgfj = sgfj.map(item => item.fileId)

            const wgfj = this.formData.fileList.filter(item => item.registerType == 4)
            this.wgfj = wgfj.map(item => item.fileId)


            const visaByArr = JSON.parse(localStorage.getItem('visaByArr'))
            if (visaByArr) this.$set(this.formData, 'visaByArr', visaByArr)

            const visaCheckBy = JSON.parse(localStorage.getItem('visaCheckBy'))
            if (visaCheckBy) this.$set(this.formData, 'visaCheckBy', visaCheckBy)
          })
        }
      },
      immediate: true
    }
  },
  mounted() {
  },
  created() {
    this.getDeptTreeDef()
  },
  methods: {
    /** 查询部门-用户下拉树结构 */
    getDeptTreeDef() {
      getTreeStruct({types:111}).then(response => {
        this.deptUserOptions = response.data;
      });
    },
    handleExamine(approved) {
      this.$refs.elForm.validate(valid => {
        if (approved && !valid) return
        this.generateParams()
        this.loading = true
        if (this.formData.visaByArr) this.formData.visaBy = this.formData.visaByArr.join(',')
        this.formData.visaName = this.$refs['visa'].checkedNodes.map(item => item.label).join(',')
        this.formData.visaCheckName = this.$refs['visaCheck'].inputValue

        localStorage.setItem('visaByArr', JSON.stringify(this.formData.visaByArr))
        localStorage.setItem('visaCheckBy', JSON.stringify(this.formData.visaCheckBy))
        const params = {
          businessKey: this.rowData.id,
          taskId: this.rowData.taskId,
          approved,
          isSupProcess: 2,
          comment: this.formData.comment,
          visaBy: this.formData.visaBy,
          visaCheckBy: this.formData.visaCheckBy,
          feedback: this.formData.feedback,
          constructionFileList: this.formData.fileList,
          visaName: this.formData.visaName,
          visaCheckName: this.formData.visaCheckName
        }
        finishedProcess(params).then(res => {
          this.loading = false
          this.$modal.msgSuccess("提交成功")
          this.$emit('close')
        })
      })
    },
    // 拼接参数
    generateParams() {
      this.$refs.shfj.save()
      // 拼接参数
      const fileList = []
      if (this.shfj) {
        this.shfj.forEach((item, index) => {
          fileList.push({
            fileId: item,
            indexOrder: index,
            registerType: 9
          })
        })
      }
      this.formData.fileList = fileList
    },
    onClose() {
      this.$emit("close")
    },
  }
}
</script>
<style scoped lang="scss">
.card_title {
  width: 200px;
  text-align: left;
  margin-bottom: 15px;
  font-weight: bold;
}
</style>
<style lang="scss" scoped>
@import "@/assets/styles/business.scss";
</style>
