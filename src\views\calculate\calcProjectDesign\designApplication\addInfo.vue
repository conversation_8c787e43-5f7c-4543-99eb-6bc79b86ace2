<template>
  <div class="road-interflow-edit">
    <el-row :gutter="15">
      <el-col :span="24">
        <el-form
          ref="queryForm"
          :model="queryParams"
          size="mini"
          :inline="true"
          label-width="68px"
        >
          <el-form-item>
            <selectTree
              :key="'domainId'"
              style="width: 240px"
              v-model="queryParams.domainId"
              :deptType="100"
              :deptTypeList="[1, 3, 4]"
              placeholder="管养单位"
              clearable
              filterable
            />
          </el-form-item>
          <el-form-item>
            <construction-select
              v-model="queryParams.calcDomainId"
              :mai-sec-id="maiSecId"
              :mai-domain-id="queryParams.domainId"
              placeholder="施工单位"
            ></construction-select>
          </el-form-item>
          <el-form-item>
            <el-input
              style="width: 190px"
              placeholder="计量单编号"
              v-model="queryParams.code"
            ></el-input>
          </el-form-item>
          <el-form-item>
            <el-date-picker
              style="width: 240px"
              v-model="queryParams.year"
              type="year"
              value-format="yyyy"
              placeholder="年份"
            >
            </el-date-picker>
          </el-form-item>
          <el-form-item>
            <el-input v-model="queryParams.number" placeholder="期数" />
          </el-form-item>
          <el-form-item>
            <el-button
              icon="el-icon-search"
              size="mini"
              type="primary"
              @click="handleQuery"
              >搜索</el-button
            >
            <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
              >重置</el-button
            >
          </el-form-item>
        </el-form>
      </el-col>
      <el-col :span="24">
        <div class="draggable">
          <el-table v-adjust-table
            size="mini"
            style="width: 100%"
            v-loading="loading"
            border
            :data="tableData"
            row-key="id"
            height="600"
            ref="dataTable"
            stripe
            highlight-current-row
            @row-click="handleClickRow"
            @selection-change="handleSelectionChange"
          >
            <el-table-column type="selection" width="50" align="center" />
            <el-table-column
              label="序号"
              align="center"
              type="index"
              width="50"
            />
            <template v-for="(column, index) in columns">
              <el-table-column
                :label="column.label"
                v-if="column.visible"
                align="center"
                :prop="column.field"
              >
                <template slot-scope="scope">
                  <dict-tag
                    v-if="column.dict"
                    :options="dict.type[column.dict]"
                    :value="scope.row[column.field]"
                  />
                  <template v-else-if="column.slots">
                    <RenderDom
                      :row="scope.row"
                      :index="index"
                      :render="column.render"
                    />
                  </template>
                  <span v-else-if="column.isTime">{{
                    parseTime(scope.row[column.field], "{y}-{m}-{d}")
                  }}</span>
                  <span v-else>{{ scope.row[column.field] }}</span>
                </template>
              </el-table-column>
            </template>
          </el-table>
          <pagination
            v-show="total > 0"
            :total="total"
            :page.sync="queryParams.pageNum"
            :limit.sync="queryParams.pageSize"
            @pagination="handleQuery"
          />
        </div>
      </el-col>
      <el-col
        :span="24"
        style="text-align: right; padding-right: 7.5px; margin-top: 18px"
      >
        <el-button type="primary" @click="onSave">保 存</el-button>
        <el-button @click="onClose">退 出</el-button>
      </el-col>
    </el-row>

  </div>
</template>
<script>
import selectTree from "@/components/DeptTmpl/selectTree.vue";
import ConstructionSelect from "@/components/ConstructionSelect/index.vue";
import {listBySid,addMiddle} from "@/api/calculate/calcProjectDesign/designApplication"
export default {
  components: {selectTree, ConstructionSelect},
  dicts: [],
  data() {
    return {
      columns: [
        {
          key: 0,
          width: 120,
          field: "numberName",
          label: `期数`,
          visible: true,
        },
        {
          key: 1,
          width: 120,
          field: "name",
          label: `计量结算单名称`,
          visible: true,
        },
        {
          key: 2,
          width: 120,
          field: "code",
          label: `计量结算单编号`,
          visible: true,
        },
        {
          key: 3,
          field: "domainName",
          label: `管养单位`,
          visible: true,
        },
        {
          key: 4,
          width: 120,
          field: "calcDomainName",
          label: `申请计量单位`,
          visible: true,
        },
        {
          key: 5,
          field: "maiSecName",
          label: `路段名称`,
          visible: true,
        },
        {
          key: 6,
          field: "conName",
          label: `合同名称`,
          visible: true,
        },
        {
          key: 7,
          field: "sumFund",
          label: `施工总费用`,
          visible: true,
        },
        {
          key: 8,
          field: "supFund",
          label: `设计费用`,
          visible: true,
        },
        {
          key: 9,
          field: "designFund",
          label: `设计费用`,
          visible: true,
        },
        {
          key: 10,
          field: "calcDate",
          label: `计量日期`,
          visible: true,
        },
      ],
      queryParams: {
        pageNum: 1,
        pageSize: 50,
      },
      total: 0,
      tableData: [],
      selectIds: [],
    };
  },
  props: {
    designId: {
      type: String,
      default: "",
    },
    maiSecId: {
      type: String,
      default: "",
    }
  },
  mounted() {
    this.handleQuery();
  },
  created() {},
  methods: {
    handleQuery() {
      this.loading = true;
      this.queryParams.maiSecId = this.maiSecId;
      listBySid(this.queryParams).then((res) => {
        this.tableData = res.rows;
        this.total = res.total;
        this.loading = false;
      });
    },
    // 选中
    handleSelectionChange(e) {
      this.selectIds = e;
    },

    handleClickRow(e) {
      e.isSelected = !e.isSelected;
      this.$refs.dataTable.toggleRowSelection(e);
    },
    onSave() {
      const submitData = this.selectIds.map((item) => {
        return {
          settleId: item.id,
          designId: this.designId,
        };
      });
      addMiddle(submitData).then(() => {
        this.$modal.msgSuccess("保存成功");
        this.onClose();
      });
    },
    resetQuery() {
      this.queryParams = {
        pageNum: 1,
        pageSize: 50,
      };
      this.handleQuery()
    },
    onClose() {
      this.$emit("close");
    },
  },
};
</script>
<style scoped lang="scss"></style>
<style lang="scss" scoped>
@import "@/assets/styles/business.scss";
</style>
