<template>
  <div class="app-container maindiv">
    <!-- 查询表单 -->
    <el-form :model="queryParams" ref="queryForm" :inline="true" class="search-form">
      <el-form-item label="设备编码" prop="devCode">
        <el-input
          v-model="queryParams.devCode"
          placeholder="请输入设备编码"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="设备所属单位" prop="devDept">
        <select-tree
          v-model="queryParams.devDept"
          placeholder="请选择管养单位"
          :deptType="100"
          :deptTypeList="[1, 3, 4]"
          clearable
          @node-selected="handleDeptChange"
        />
      </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
        >搜索</el-button>
        <el-button
          icon="el-icon-refresh"
          size="mini"
          @click="resetQuery"
        >重置</el-button>
      </el-form-item>
    </el-form>
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          icon="el-icon-plus"
          size="mini"
          type="primary"
          @click="openDetail"
        >新增
        </el-button>
      </el-col>
      <right-toolbar
        :columns="columns"
        :showSearch.sync="showSearch"
        @queryTable="handleQuery"
      ></right-toolbar>
    </el-row>
    <el-row>
      <div class="draggable">
        <el-table
          ref="dataTable"
          v-loading="loading"
          :data="tableData"
          :height="showSearch ? 'calc(100vh - 330px)' : 'calc(100vh - 270px)'"
          border
          row-key="id"
          size="mini"
          stripe
          style="width: 100%"
        >
          <el-table-column
            align="center"
            label="序号"
            type="index"
            width="50"
          />
          <template v-for="(column,index) in columns">
            <el-table-column
              v-if="column.visible"
              :key="column.key"
              :label="column.label"
              :prop="column.field"
              :width="column.width"
              align="center"
              show-overflow-tooltip
            >
              <template slot-scope="scope">
                <dict-tag
                  v-if="column.dict"
                  :options="dict.type[column.dict]"
                  :value="scope.row[column.field]"
                />
                <template v-else-if="column.slots">
                  <RenderDom :index="index" :render="column.render" :row="scope.row"/>
                </template>
                <span v-else-if="column.isTime">{{ parseTime(scope.row[column.field], '{y}-{m}-{d} {h}:{i}') }}</span>
                <span v-else>{{ scope.row[column.field] }}</span>
              </template>
            </el-table-column>
          </template>
          <el-table-column
            align="center"
            class-name="small-padding fixed-width"
            fixed="right"
            label="操作"
            width="280"
          >
            <template slot-scope="scope">
              <el-button
                icon="el-icon-view"
                size="mini"
                type="text"
                @click="handleViewStructures(scope.row)"
              >查看结构物
              </el-button>
              <el-button
                v-if="scope.row.reportStatus === 1"
                icon="el-icon-time"
                size="mini"
                type="text"
                @click="handleDelay(scope.row)"
              >延迟播报
              </el-button>
              <el-button
                v-if="scope.row.reportStatus === 0"
                icon="el-icon-video-play"
                size="mini"
                type="text"
                @click="handleStartAlarm(scope.row)"
              >开启播报
              </el-button>
              <el-button
                v-else
                icon="el-icon-video-pause"
                size="mini"
                type="text"
                @click="handleEndAlarm(scope.row)"
              >关闭播报
              </el-button>
              <el-button
                icon="el-icon-edit"
                size="mini"
                type="text"
                @click="handleUpdate(scope.row)"
              >修改
              </el-button>
              <el-button
                icon="el-icon-delete"
                size="mini"
                type="text"
                @click="handleDelete(scope.row)"
              >删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>
        <pagination
          v-show="total > 0"
          :limit.sync="queryParams.pageSize"
          :page.sync="queryParams.pageNum"
          :total="total"
          @pagination="handleQuery"
        />
      </div>
    </el-row>

    <!-- 新增/修改对话框 -->
    <el-dialog
      :title="formTitle"
      :visible.sync="dialogVisible"
      :append-to-body="true"
      :destroy-on-close="true"
      width="50%"
      @close="handleClose"
    >
      <div v-loading="loading" class="voice-alarm-edit">
        <el-form
          ref="form"
          :model="formData"
          :rules="rules"
          label-width="120px"
          size="medium"
        >
          <el-form-item label="设备名称" prop="devName">
            <el-input
              v-model="formData.devName"
              placeholder="请输入设备名称"
              clearable
            />
          </el-form-item>
          <el-form-item label="设备编码" prop="devCode">
            <el-input
              v-model="formData.devCode"
              placeholder="请输入设备编码"
              clearable
            />
          </el-form-item>
          <el-form-item label="设备所属单位" prop="devDept">
            <select-tree
              v-model="formData.devDept"
              placeholder="请选择管养单位"
              :deptType="100"
              :deptTypeList="[1, 3, 4]"
              clearable
              @node-selected="handleDeptChange"
            />
          </el-form-item>
          <el-form-item label="关联结构物" prop="devStructures">
            <el-select
              v-model="formData.devStructures"
              multiple
              placeholder="请选择关联结构物"
              style="width: 100%"
              clearable
            >
              <el-option
                v-for="item in structureOptions"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="查看角色" prop="viewRole">
            <el-input
              v-model="formData.viewRole"
              placeholder="请输入查看角色"
              clearable
            />
          </el-form-item>
        </el-form>
        <div style="text-align: right; margin-top: 20px">
          <el-button size="mini" type="primary" @click="handleSave">保存</el-button>
          <el-button size="mini" @click="dialogVisible = false">取消</el-button>
        </div>
      </div>
    </el-dialog>

    <!-- 查看结构物对话框 -->
    <el-dialog
      title="关联结构物"
      :visible.sync="structureDialogVisible"
      width="60%"
    >
      <el-table
        :data="structureData"
        border
        size="mini"
        style="width: 100%"
      >
        <el-table-column
          prop="name"
          label="结构物名称"
          align="center"
        />
        <el-table-column
          prop="code"
          label="编码"
          align="center"
        />
        <el-table-column
          prop="planStatusName"
          label="预案状态"
          align="center" />
        <el-table-column
          prop="statusName"
          label="超限状态"
          align="center" />
      </el-table>
    </el-dialog>

    <!-- 延迟播报对话框 -->
    <el-dialog
      title="延迟播报"
      :visible.sync="delayDialogVisible"
      width="400px"
    >
      <el-form
        ref="delayForm"
        :model="delayForm"
        label-width="100px"
        size="medium"
        :show-message='false'
      >
        <el-form-item label="延迟时间" prop="delayTime" required>
          <el-date-picker
            v-model="delayForm.delayTime"
            type="datetime"
            placeholder="选择延迟时间"
            :picker-options="delayTimeOptions"
            value-format="yyyy-MM-dd HH:mm:ss"
            style="width: 100%"
          />
        </el-form-item>
      </el-form>
      <div style="text-align: right; margin-top: 20px">
        <el-button size="mini" type="primary" @click="handleDelayConfirm">确定</el-button>
        <el-button size="mini" @click="delayDialogVisible = false">取消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listVoiceAlarm, saveVoiceAlarm, updateVoiceAlarm, deleteVoiceAlarm, startVoiceAlarm, endVoiceAlarm, delayVoiceAlarm } from "@/api/jgjc/alarmDevice";
import { listStructure } from "@/api/jgjc/earlyWarning/deviceModel";
import SelectTree from "@/components/DeptTmpl/selectTree.vue";

export default {
  name: 'VoiceAlarm',
  components: {
    SelectTree,
    RenderDom: {
      functional: true,
      props: {
        row: Object,
        index: Number,
        render: Function
      },
      render(createElement, ctx) {
        const {row, index} = ctx.props
        return ctx.props.render(row, index)
      }
    }
  },
  dicts: ['device_online_status', 'device_report_status', 'flash_open_type'],
  data() {
    return {
      showSearch: false,
      loading: false,
      columns: [
        {key: 0, field: 'devName', label: '设备名称', visible: true},
        {key: 1, field: 'devCode', label: '设备编码', visible: true},
        {key: 2, field: 'devDeptName', label: '设备所属单位', visible: true},
        {key: 3, field: 'status', label: '设备在线状态', visible: true, dict: 'device_online_status'},
        {key: 4, field: 'reportStatus', label: '播报状态', visible: true, dict: 'device_report_status'},
        {key: 5, field: 'delayStatus', label: '延时状态', visible: true, dict: 'flash_open_type'},
        {key: 6, field: 'delayTime', label: '延时时间', visible: true},
      ],
      tableData: [],
      dialogVisible: false,
      structureDialogVisible: false,
      delayDialogVisible: false,
      formData: {},
      delayForm: {
        id: '',
        devCode: '',
        delayTime: ''
      },
      structureData: [],
      structureOptions: [],
      total: 0,
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        devCode: '',
        devName: ''
      },
      rules: {
        devName: [{ required: true, message: '请输入设备名称', trigger: 'blur' }],
        devCode: [{ required: true, message: '请输入设备编码', trigger: 'blur' }],
        devDept: [{ required: true, message: '请选择设备所属单位', trigger: 'change' }]
      },
      delayTimeOptions: {
        disabledDate(time) {
          return time.getTime() < Date.now() - 8.64e7; // 禁用今天之前的日期
        }
      },
      timer: null
    }
  },
  computed: {
    formTitle() {
      return this.formData.id ? '修改报警器设备' : '新增报警器设备';
    }
  },
  created() {
    this.handleQuery();
    this.timer = setInterval(() => {
      this.handleQuery();
    }, 15000);
  },
  methods: {
    // 查询
    handleQuery() {
      this.loading = true;
      listVoiceAlarm(this.queryParams).then(res => {
        this.tableData = res.rows;
        this.total = res.total;
        this.loading = false;
      }).catch(() => {
        this.loading = false;
      });
    },

    // 重置查询
    resetQuery() {
      this.$refs.queryForm.resetFields();
      this.handleQuery();
    },

    // 新增
    openDetail() {
      this.formData = {};
      this.dialogVisible = true;
    },

    // 修改
    handleUpdate(row) {
      this.formData = JSON.parse(JSON.stringify(row));
      // 获取关联结构物列表
      if (this.formData.devDeptName) {
        const params = {
          devDept: this.formData.devDeptName
        }
        this.getStructureOptions(params);
      }
      if (this.formData.devStructures) this.formData.devStructures = this.formData.devStructures.split(',');
      this.dialogVisible = true;
    },

    // 删除
    handleDelete(row) {
      this.$modal.confirm('是否确认删除该设备？').then(() => {
        this.loading = true;
        deleteVoiceAlarm({ ids: [row.id] }).then(() => {
          this.$modal.msgSuccess('删除成功');
          this.handleQuery();
        }).finally(() => {
          this.loading = false;
        });
      });
    },

    // 保存
    handleSave() {
      this.$refs.form.validate(valid => {
        if (valid) {
          this.loading = true;
          if (this.formData.devStructures && Array.isArray(this.formData.devStructures) && this.formData.devStructures.length > 0) {
            this.formData.devStructures = this.formData.devStructures.join(',');
          }
          const isUpdate = !!this.formData.id;
          const api = isUpdate ? updateVoiceAlarm(this.formData) : saveVoiceAlarm(this.formData);

          api.then(() => {
            this.$modal.msgSuccess(isUpdate ? '修改成功' : '新增成功');
            this.dialogVisible = false;
            this.handleQuery();
          }).finally(() => {
            this.loading = false;
          });
        }
      });
    },

    // 关闭对话框
    handleClose() {
      this.$refs.form.resetFields();
      this.formData = {};
      this.structureOptions = [];
    },

    // 查看结构物
    handleViewStructures(row) {
      if (!row.devStructures) {
        this.$modal.msgWarning('该设备未关联任何结构物');
        return;
      }

      this.loading = true;
      listStructure({ structureIds: row.devStructures }).then(res => {
        this.structureData = res.data;
        this.structureData.forEach(item => {
          item.statusName = item.status == '0' ? '正常' : item.status == '1' ? '异常' : ''
          item.planStatusName = item.planStatus == '0' ? '未开启' : item.planStatus == '1' ? '开启中' : ''
        })
        this.structureDialogVisible = true;
      }).finally(() => {
        this.loading = false;
      });
    },

    // 开启播报
    handleStartAlarm(row) {
      this.$modal.confirm('确认开启该设备的播报功能？').then(() => {
        this.loading = true;
        startVoiceAlarm({ id: row.id, devCode: row.devCode }).then(() => {
          this.$modal.msgSuccess('开启成功');
          this.handleQuery();
        }).finally(() => {
          this.loading = false;
        });
      });
    },

    // 关闭播报
    handleEndAlarm(row) {
      this.$modal.confirm('确认关闭该设备的播报功能？').then(() => {
        this.loading = true;
        endVoiceAlarm({ id: row.id, devCode: row.devCode }).then(() => {
          this.$modal.msgSuccess('关闭成功');
          this.handleQuery();
        }).finally(() => {
          this.loading = false;
        });
      });
    },

    // 延迟播报
    handleDelay(row) {
      this.delayForm = {
        id: row.id,
        devCode: row.devCode,
        delayTime: ''
      };
      this.delayDialogVisible = true;
    },

    // 确认延迟播报
    handleDelayConfirm() {
      this.$refs.delayForm.validate(valid => {
        if (valid) {
          this.loading = true;
          delayVoiceAlarm(this.delayForm).then(() => {
            this.$modal.msgSuccess('延迟播报设置成功');
            this.delayDialogVisible = false;
            this.handleQuery();
          }).finally(() => {
            this.loading = false;
          });
        }
      });
    },

    // 单位变更时获取结构物列表
    handleDeptChange(node) {
      console.log(node)
      const params = {
        devDept: node.label
      }
      this.formData.devStructures = [];
      this.getStructureOptions(params);
    },

    // 获取结构物选项
    getStructureOptions(params) {
      listStructure(params).then(res => {
        this.structureOptions = res.data;
      });
    }
  },
  beforeDestroy() {
    if (this.timer) {
      clearInterval(this.timer)
      this.timer = null;
    }
  }
}
</script>

<style lang="scss" scoped>
::v-deep .el-table th .gutter {
  display: table-cell !important;
}
</style>
