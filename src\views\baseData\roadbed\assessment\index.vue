<template>
  <PageContainer>
    <template slot="search">
      <div style="display: flex; align-items: center; margin: 0">
        

        <el-input style="width: 200px;margin-right: 15px;" v-model="queryParams.project" clearable placeholder="请输入项目名称" />
      
    
        <el-date-picker
          style="width: 200px;margin-right: 15px;"
          v-model="queryParams.year"
          type="year"
          placeholder="请选择年度"
          clearable
          value-format="yyyy"
        />
         
       
        <div style="min-width:220px;height:32px">
          <el-button
            v-hasPermi="['baseData:assessment:selectPage']"
            type="primary"
            icon="el-icon-search"
            @click="handleQuery"
          >搜索</el-button>
          <el-button
            icon="el-icon-refresh"
            @click="resetQuery"
          >重置</el-button>
        </div>
      </div>
    </template>

    <template slot="header">
      <div class="button-list">
        <el-button
       
          type="primary"
          @click="handleAdd"
        >新增</el-button>
        <el-button
         
          type="primary"
          @click="handleUpdate"
        >编辑</el-button>
        <el-button
        
          type="primary"
          @click="handleDelete"
        >删除</el-button>
      </div>
    </template>

    <template slot="body">
      <el-table
        v-adjust-table
        v-loading="loading"
        ref="table"
        height="100%"
        style="width: 100%"
        border
        :data="tableData"
        :row-style="rowStyle"
        @selection-change="handleSelectionChange"
        @row-click="handleRowClick"
      >
        <el-table-column
          type="selection"
          width="50"
          align="center"
        />
        <el-table-column
          fixed
          label="序号"
          type="index"
          width="50"
          align="center"
          >
          <template v-slot="scope">
            {{
              scope.$index +
              (queryParams.pageNum - 1) * queryParams.pageSize +
              1
            }}
          </template>
        </el-table-column>
        <el-table-column
          prop="project"
          label="项目名称"
          align="center"
        />
        <el-table-column
          prop="year"
          label="年度"
          align="center"
        />
        <el-table-column
          prop="remark"
          label="备注"
          align="center"
        />
      </el-table>
        
        
      <pagination
        :total="total"
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
        :pageSizes="[10, 20, 30, 50, 100, 1000]"
        @pagination="getList"
      />
    </template>
    <Form
      v-if="showAddEdit"
      :forView="forView"
      :formData="formData"
      :title="title"
      :showAddEdit="showAddEdit"
      @close="() => {showAddEdit = false;formData ={}}"
      @refresh="() => {showAddEdit = false;formData ={};getList()}"
    />
   
  </PageContainer>
</template>

<script>
import {
  getListPage,

  deleteByIds
} from '@/api/baseData/roadbed/assessment/index.js'
import Form from './form.vue'
import CascadeSelection from '@/components/CascadeSelection/index.vue'
import rangeInput from '@/views/baseData/components/rangeInput/index.vue'
import ImportData from '@/views/baseData/components/importData/index.vue'


export default {
  name: 'Assessment',
  components: {
    Form,
    CascadeSelection,
    rangeInput,
    ImportData
  },
  dicts: ['sys_route_type', 'sys_operation_state'],
  data() {
    return {
      loading: true,
      showAddEdit: false,
      forView: false,
      clearData: false,
      title: '',
      formData: {},
      ids: [],
      total: 0,
      selection: [],
      tableData: [],
      queryParams: {
        pageNum: 1,
        pageSize: 20
      },
      showDetail: false,
      slopeId: '',
      showImport: false
    }
  },
  watch: {},
  created() {
    this.getList()
  },
  methods: {
    // 获取表格数据
    getList() {
      this.loading = true
      getListPage(this.queryParams)
        .then(response => {
          this.tableData = response.rows
          this.total = response.total
          this.loading = false
          this.clearData = false
        })
        .catch(() => {
          this.loading = false
        })
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.selection = selection
    },
    // 表格点击勾选
    handleRowClick(row) {
      row.isSelected = !row.isSelected
      this.$refs.table.toggleRowSelection(row)
    },
    // 勾选高亮
    rowStyle({ row, rowIndex }) {
      if (this.ids.includes(row.id)) {
        return { 'background-color': '#b7daff', color: '#333' }
      } else {
        return { 'background-color': '#fff', color: '#333' }
      }
    },
    // 搜索按钮
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    // 重置按钮
    resetQuery() {
      this.clearData = true
      this.queryParams = {
        pageNum: 1,
        pageSize: 20
      }
      this.handleQuery()
    },
    // 新增按钮操作
    handleAdd() {
      this.forView = false
      this.showAddEdit = true
      this.formData = {}
      this.title = '新增路面评定项目管理'
    },
    // 编辑按钮
    handleUpdate() {
      if (this.ids.length != 1) {
        this.$message.warning('请选择一条数据进行编辑！')
        return
      } else {

        this.formData = this.selection[0]
        this.forView = false
        this.showAddEdit = true
        this.title = '编辑路面评定项目管理'
          

      }
    },
    // 删除按钮
    handleDelete() {
      if (this.ids.length == 0) {
        this.$message.warning('请选择至少一条数据进行删除！')
        return
      }
      this.$modal
        .confirm('确认删除？')
        .then(() => {
          deleteByIds(this.ids.join()).then(res => {
            if (res && res.code == '200') {
              this.getList()
              this.$modal.msgSuccess('删除成功')
            }
          })
        })
        .catch(() => {})
    },
    // 查看按钮
    handleView() {
      if (this.ids.length != 1) {
        this.$message.warning('请选择一条数据！')
        return
      } else {
        getHorizontalCurve(this.ids[0]).then(res => {
          if (res.code === 200) {
            this.formData = res.data
            this.forView = true
            this.showAddEdit = true
            this.title = '查看路面评定项目管理'
          }
        })
      }
    },
  }
}
</script>

<style lang="scss" scoped>
.button-list {
  border-radius: 4px;
  width: 100%;
  .el-button {
    margin-bottom: 10px;
    margin-right: 10px;
    margin-left: 0;
  }
}
</style>
