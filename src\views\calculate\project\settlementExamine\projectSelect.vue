<template>
  <div class="road-interflow-edit">
    <el-form
      ref="queryForm"
      :inline="true"
      :model="queryParams"
      label-width="68px"
      size="mini"
    >
      <el-form-item>
        <el-input v-model="queryParams.name" placeholder="项目名称"/>
      </el-form-item>
      <el-form-item>
        <el-input v-model="queryParams.code" placeholder="项目编码"/>
      </el-form-item>
      <el-form-item>
        <el-button
          icon="el-icon-search"
          size="mini"
          type="primary"
          @click="handleQuery"
        >搜索
        </el-button
        >
        <el-button
          icon="el-icon-refresh"
          size="mini"
          @click="resetQuery"
        >重置
        </el-button
        >
      </el-form-item>
    </el-form>
    <el-table v-adjust-table v-loading="loading" :data="tableData" style="width: 100%" @row-click="checkProj" height="450px">
      <el-table-column
        align="center"
        label="序号"
        type="index"
        width="50"
      />
      <template v-for="(column, index) in columns">
        <el-table-column
          v-if="column.visible"
          :fixed="column.fixed"
          :label="column.label"
          :prop="column.field"
          :width="column.width"
          show-overflow-tooltip
          align="center"
        >
          <template slot-scope="scope">
            <dict-tag
              v-if="column.dict"
              :options="dict.type[column.dict]"
              :value="scope.row[column.field]"
            />
            <template v-else-if="column.slots">
              <RenderDom
                :index="index"
                :render="column.render"
                :row="scope.row"
              />
            </template>
            <span v-else-if="column.isTime">{{
                parseTime(scope.row[column.field], "{y}-{m}-{d}")
              }}</span>
            <span v-else>{{ scope.row[column.field] }}</span>
          </template>
        </el-table-column>
      </template>
    </el-table>
  </div>
</template>

<script>
import {listProject} from "@/api/calculate/project/settlementApplication";

export default {
  dicts: ['affiliation_project_type', 'project_type', 'project_status_type'],
  props: {
    flag: {
      type: Number,
      default: 1
    },
    settleId: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      loading: false,
      total: 0,
      queryParams: {
        pageNum: 1,
        pageSize: 50,
      },
      tableData: [],
      columns: [
        {key: 0, width: 100, field: 'name', label: `项目名称`, visible: true},
        {key: 1, width: 100, field: 'maiSecName', label: `路段名称`, visible: true},
        {key: 2, width: 100, field: 'mileRange', label: `桩号范围`, visible: true},
        {key: 3, width: 100, field: 'projectType', label: `工程分类`, visible: true, dict: 'project_type'},
        {key: 4, width: 100, field: 'mtype', label: `所属工程类别`, visible: true, dict: 'affiliation_project_type'},
        {key: 5, width: 100, field: 'structureName', label: `构造物名称`, visible: true},
        {key: 6, width: 100, field: 'defLiaPer', label: `缺陷责任期`, visible: true},
        {key: 7, width: 100, field: 'expectDuration', label: `预计工期`, visible: true},
        {key: 8, width: 100, field: 'beginDate', label: `预计开始时间`, visible: true},
        {key: 9, width: 100, field: 'endDate', label: `预计结束时间`, visible: true},
        {key: 10, width: 100, field: 'status', label: `状态`, visible: true, dict: 'project_status_type'},
        {key: 11, width: 100, field: 'remark', label: `备注`, visible: true},
      ]
    }
  },
  mounted() {
    this.handleQuery()
  },
  methods: {
    handleQuery() {
      this.queryParams.settleId = this.settleId
      this.queryParams.flag = this.flag
      listProject(this.queryParams).then(response => {
        if (response.code == 200) {
          this.loading = false
          this.tableData = response.rows
          this.total = response.total
        }
      })
    },
    checkProj(e) {
      const param = {
        projName: e.name,
        projId: e.id,
        projCode: e.code
      }
      this.$emit('checkProj', param)
    },
    resetQuery() {
      this.queryParams = {
        pageNum: 1,
        pageSize: 50,
      }
      this.handleQuery()
    }
  }
}
</script>

<style lang="scss" scoped>

</style>
<style lang="scss" scoped>
@import "@/assets/styles/business.scss";
</style>
