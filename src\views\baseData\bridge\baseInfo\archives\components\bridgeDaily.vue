<template>
  <div class="right-box" :class="oneMap ? 'one-map' : ''">
    <div class="right-box-head">
      <h5>日常检查</h5>
    </div>
    <div
      style="height: calc(100% - 53px); width: 100%"
      class="container-view-list"
    >
    <DailyInspection :type="'1'" :assetId="assetId" :pageSize="10"></DailyInspection>
    </div>
  </div>
</template>

<script>

import DailyInspection from "@/components/Inspect/dailyInspection.vue";

export default {
  name: "bridgeDaily-baseInfo",
  components: { DailyInspection},
  inject: ["oneMap"],
  dicts: [
  ],
  props: {
    assetId: {
      type: String || Number,
      default: "",
    },
  },
  data() {
    return {
     
    };
  },
  watch: {},
  created() {
  },
  methods: {

  },
};
</script>

<style lang="scss" scoped>
@import "@/assets/styles/common.scss";
::v-deep {
  .el-table__row:hover td {
    background: #b7daff !important;
  }
}
.container-view-list {
  // padding: 0;
}
.right-box {
  width: 100%;
  height: 100%;
  background-color: #fff;
  border-radius: 10px;
  box-shadow: 0px 0px 10px 0px rgba(0, 0, 0, 0.2);
  position: relative;
  .right-box-head {
    width: 100%;
    height: 52px;
    background: #409eff;
    border-radius: 10px 10px 0 0;
    display: flex;
    justify-content: center;
    align-items: center;
    h5 {
      font-family: Microsoft YaHei, Microsoft YaHei;
      font-weight: 700;
      font-size: 14px;
      color: #ffffff;
    }
  }
}
</style>
