import Vue from "vue";
import ChooseLonLat from "./chooseLonLat.vue";

export function chooseLonLat({
  type = "lonlat",
  title = "选择位置",
  msg = "请选择",
  callback = () => {},
} = {}) {
  return new Promise((resolve) => {
    const instance = new Vue({
      el: document.createElement("div"),
      data: {
        visible: true,
      },
      methods: {
        close() {
          this.visible = false;
          document.body.removeChild(this.$el);
        },
        cancel() {
          resolve();
          this.close();
        },
        ok(row) {
          if (!msg) {
            this.$message.error(msg);
            return;
          }
          callback(row);
          resolve(row);
          this.close();
        },
      },
      /**
       * 将元素渲染到指定容器中
       * @param {HTMLElement} element - 要渲染的元素
       * @param {HTMLElement} container - 元素将被渲染到的容器
       * @returns {void} - 该函数不返回任何值
       */
      render(h) {
        const { visible, ok } = this;
        return h(ChooseLonLat, {
          props: {
            title,
            visible,
            type,
          },
          on: {
            cancel: this.cancel,
            ok,
          },
        });
      },
    });

    document.body.appendChild(instance.$el);
  });
}
