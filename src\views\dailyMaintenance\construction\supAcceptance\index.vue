<template>
  <div class="app-container maindiv">
    <el-row :gutter="20">
      <!--筛选区开始-->
      <el-col :span="24" :xs="24">
        <el-row>
          <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" label-width="68px">
            <el-form-item label="" prop="year">
              <el-date-picker
                v-model="queryParams.yearStr"
                style="width: 240px"
                type="year"
                value-format="yyyy"
                placeholder="年份">
              </el-date-picker>
            </el-form-item>

            <el-form-item label="" prop="domainId">
              <selectTree
                :key="'field2'"
                style="width: 240px"
                v-model="queryParams.domainIdStr"
                :deptType="100" :deptTypeList="[1, 3, 4]"
                placeholder="管养单位"
                clearable
                filterable
              />
            </el-form-item>
            <el-form-item label="" prop="maiSecId">
              <RoadSection v-model="queryParams.maiSecId" :deptId="queryParams.domainIdStr" placeholder="路段名称"/>
            </el-form-item>
            <el-form-item label="" prop="code">
              <el-input
                v-model="queryParams.code"
                placeholder="施工单编码"
                clearable
                style="width: 240px"
              >
              </el-input>
            </el-form-item>
            <el-form-item label="" prop="conDomainId">
              <selectTree
                :key="'conDomainId'"
                style="width: 240px"
                v-model="queryParams.conDomainId" :data-rule="false"
                :dept-type="100"
                placeholder="施工单位"
                :filter-keys="['云南省交通投资建设集团有限公司', '云南交投投资有限公司']"
                :expand-all="false"
                clearable
                filterable
              />
            </el-form-item>

            <el-form-item>
              <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
              <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
              <el-button v-show="!showSearch" @click="showSearch=true" icon="el-icon-arrow-down" circle></el-button>
              <el-button v-show="showSearch" @click="showSearch=false" icon="el-icon-arrow-up" circle></el-button>
            </el-form-item>
          </el-form>
          <!--默认折叠-->

          <!--默认折叠-->
          <el-col :span="24">
            <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch"
                     label-width="68px">
              <el-form-item label="" prop="beginDate">
                <el-date-picker
                  v-model="queryParams.beginDate"
                  format="yyyy-MM-dd"
                  value-format="yyyy-MM-dd"
                  style="width: 240px"
                  placeholder="开始时间"
                  clearable
                ></el-date-picker>
              </el-form-item>
              <el-form-item label="" prop="endDate">
                <el-date-picker
                  v-model="queryParams.endDate"
                  format="yyyy-MM-dd"
                  value-format="yyyy-MM-dd"
                  style="width: 240px"
                  placeholder="结束时间"
                  clearable
                ></el-date-picker>
              </el-form-item>
              <el-form-item label="" prop="content">
                <el-input
                  v-model="queryParams.content"
                  placeholder="工程内容"
                  clearable
                  style="width: 240px"
                >
                </el-input>
              </el-form-item>
<!--              <el-form-item label="" prop="stage">-->
<!--                <dict-select type="mp_daily_month_plan_status" placeholder="状态" style="width: 240px" clearable v-model="queryParams.stage" />-->
<!--              </el-form-item>-->
              <el-form-item label="" prop="exeRequire">
                <el-input
                  v-model="queryParams.exeRequire"
                  placeholder="实施要求"
                  clearable
                  style="width: 240px"
                >
                </el-input>
              </el-form-item>
            </el-form>
          </el-col>
        </el-row>
        <!--筛选区结束-->
        <!--操作按钮区开始-->
        <el-row :gutter="10" class="mb8">
<!--          <el-col :span="1.5">-->
<!--            <el-button-->
<!--              type="primary"-->
<!--              icon="el-icon-plus"-->
<!--              size="mini"-->
<!--              @click="handleBatchConfirm"-->
<!--            >批量接收-->
<!--            </el-button>-->
<!--          </el-col>-->
          <el-col :span="1.5">
            <el-button
              type="warning"
              icon="el-icon-download"
              size="mini"
              v-has-menu-permi="['checkSupDomainAccept:construction:export']"
              @click="handleExport"
            >导出清单
            </el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button
              type="success"
              icon="el-icon-magic-stick"
              size="mini"
              @click="handleViewOperateInfo"
            >操作意见
            </el-button>
          </el-col>
<!--          <el-col :span="1.5">-->
<!--            <el-button-->
<!--              type="primary"-->
<!--              icon="el-icon-download"-->
<!--              size="mini"-->
<!--            >施工单下载-->
<!--            </el-button>-->
<!--          </el-col>-->
          <el-col :span="1.5">
            <el-button
              type="primary"
              icon="el-icon-view"
              size="mini"
              v-has-menu-permi="['dailyConstruction:construction:preview']"
              @click="handlePreview"
            >监理施工单预览
            </el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button
              type="warning"
              icon="el-icon-view"
              size="mini"
              v-has-permi="['settlement:repository:regenerate']"
              @click="handleRegenerate"
            >重新生成报表
            </el-button>
          </el-col>
          <right-toolbar :showSearch.sync="showSearch" @queryTable="handleQuery" :columns="columns"></right-toolbar>
        </el-row>
        <!--操作按钮区结束-->

        <!--数据表格开始-->
        <div class="tableDiv">
          <el-table v-adjust-table stripe size="mini" :height="showSearch ? 'calc(100vh - 330px)' : 'calc(100vh - 270px)'"
                    style="width: 100%" v-loading="loading" border :data="dataList"
                    ref="dataTable" @row-click="handleClickRow"  @sort-change="sortChange"
                    highlight-current-row
                    @selection-change="handleSelectionChange">
            <el-table-column type="selection" width="50" align="center"/>
            <el-table-column fixed label="序号" type="index" width="50"></el-table-column>
            <template v-for="(column,index) in columns">
              <el-table-column :label="column.label"
                               v-if="column.visible"
                               align="center"
                               :prop="column.field"
                               :sortable="column.sortable"
                               :width="column.width">
                <template slot-scope="scope">
                  <dict-tag v-if="column.dict" :options="dict.type[column.dict]" :value="scope.row[column.field]"/>
                  <template v-else-if="column.slots">
                    <RenderDom :row="scope.row" :index="index" :render="column.render"/>
                  </template>
                  <span v-else-if="column.isTime">{{ parseTime(scope.row[column.field], '{y}-{m}-{d}') }}</span>
                    <span v-else>{{ scope.row[column.field] }}</span>
                </template>
              </el-table-column>
            </template>
<!--            <el-table-column label="附件"-->
<!--                             align="center">-->
<!--              <template slot-scope="scope">-->
<!--                <el-button-->
<!--                  size="mini"-->
<!--                  type="text"-->
<!--                  @click="handleViewFile(scope.row)"-->
<!--                >查看-->
<!--                </el-button>-->
<!--              </template>-->
<!--            </el-table-column>-->
            <el-table-column
              label="操作"
              fixed="right"
              align="center"
              width="250"
              class-name="small-padding fixed-width"
            >
              <template slot-scope="scope">
                <el-button
                  size="mini"
                  type="text"
                  icon="el-icon-view"
                  v-has-menu-permi="['checkSupDomainAccept:construction:review']"
                  @click="handleOpenDetail(scope.row)"
                >接收
                </el-button>
<!--                <el-button-->
<!--                  size="mini"-->
<!--                  type="text"-->
<!--                  icon="el-icon-edit-outline"-->
<!--                  @click="handleGather(scope.row)"-->
<!--                >作业区采集-->
<!--                </el-button>-->
              </template>
            </el-table-column>
          </el-table>

          <pagination
            v-show="total>0"
            :total="total"
            :page.sync="queryParams.pageNum"
            :limit.sync="queryParams.pageSize"
            @pagination="handleQuery"
          />
        </div>
        <!--数据表格结束-->
      </el-col>
    </el-row>
    <!-- 详情信息弹框 -->
    <el-drawer :wrapperClosable="false" :title="detailTitle" :visible.sync="openDetail" size="70%" destroy-on-close>
      <Detail type="7" :rowData="rowData" :read-only="readOnly" @close="handleCloseDetail"></Detail>
    </el-drawer>
    <el-dialog title="事件信息" :visible.sync="openEventInfo" width="80%" destroy-on-close>
      <div class="tableDiv" style="margin-bottom: 10px">
        <el-table v-adjust-table stripe size="mini" height="70vh"
                  style="width: 100%" v-loading="loading" border :data="eventList"
                  @selection-change="handleSelectionChange">
          <el-table-column type="selection" width="50" align="center"/>
          <el-table-column fixed label="序号" type="index" width="50"></el-table-column>
          <template v-for="(column,index) in eventColumns">
            <el-table-column :label="column.label"
                             v-if="column.visible"
                             align="center"
                             :prop="column.field"
                             :width="column.width">
              <template slot-scope="scope">
                <dict-tag v-if="column.dict" :options="dict.type[column.dict]" :value="scope.row[column.field]"/>
                <template v-else-if="column.slots">
                  <RenderDom :row="scope.row" :index="index" :render="column.render"/>
                </template>
                <span v-else-if="column.isTime">{{ parseTime(scope.row[column.field], '{y}-{m}-{d}') }}</span>
                    <span v-else>{{ scope.row[column.field] }}</span>
              </template>
            </el-table-column>
          </template>
        </el-table>
      </div>
    </el-dialog>
    <el-dialog title="操作意见" v-if="openOperateInfo" :visible.sync="openOperateInfo" width="80%" destroy-on-close>
      <operateInfo :get-node-info="nodeInfo" :business-key="checkRow.id" param-name="id"></operateInfo>
    </el-dialog>
    <el-dialog title="现场作业区采集" :visible.sync="openGather" width="80%">
      <gather></gather>
    </el-dialog>
    <el-dialog title="附件列表" v-if="openFileList" :visible.sync="openFileList" width="80%" destroy-on-close>
      <file-info :is-show="true" :in-id="inId"></file-info>
    </el-dialog>
    <el-dialog title="批量接收" :visible.sync="openReview" width="40%" destroy-on-close>
      <el-form ref="form" label-width="80px">
        <el-form-item label="接收意见">
          <el-input v-model="remark" type="textarea" :rows="4" placeholder="请输入内容"></el-input>
        </el-form-item>
      </el-form>
      <div style="text-align: right">
        <el-button type="primary" @click="handleBatchReceive">接收</el-button>
        <el-button type="danger" @click="handleBatchReject">拒收</el-button>
      </div>
    </el-dialog>
    <IFramePreview ref="iframeRef" :srcdoc="preview.html" :down-url="preview.url" :file-name="preview.fileName"></IFramePreview>

  </div>
</template>

<script>

import selectTree from "@/components/DeptTmpl/selectTree.vue";
import Detail from "../../component/constructionInfo.vue";
import eventInfo from "../../component/eventInfo.vue";
import gather from "../../construction/component/gather.vue";
import operateInfo from "../../component/operateInfo.vue";
import {querySupervisionAuditList} from "@/api/dailyMaintenance/construction/supAcceptance";

import {eventInfoByConId, getPreviewUrl, nodeInfo} from "@/api/dailyMaintenance/constructionOrder/noticeDraft"
import RoadSection from "@/views/baseData/components/roadSection/index.vue";
import FileInfo from "@/views/dailyMaintenance/component/fileInfo.vue";
import Luckysheet from "@/components/Luckysheet/index.vue";
import axios from "axios";
import {getToken} from "@/utils/auth";
import IFramePreview from "@/components/IFramePreview/index.vue";
import {regenerateReport} from "@/api/dailyMaintenance/metering/addPrice";

export default {
  name: "index",
  components: {
    IFramePreview,
    Luckysheet,
    FileInfo,
    RoadSection,
    eventInfo,
    operateInfo,
    Detail, selectTree,
    gather,
    RenderDom: {
      functional: true,
      props: {
        row: Object,
        index: Number,
        render: Function
      },
      render(createElement, ctx) {
        const {row, index} = ctx.props
        return ctx.props.render(row, index)
      }
    }
  },
  dicts: ['urgent_degree', 'cost_name', 'mp_daily_month_plan_status', 'route_direction', 'lane', 'disposal_type', 'sys_asset_type', 'dis_stage_name'],
  data() {
    return {
      showSearch: false,
      loading: false,
      queryParams: {
        pageNum: 1,
        pageSize: 50,
      },
      // 列信息
      columns: [
        {key: 0, width: 100, field: 'stageName', label: `状态`, visible: true},
        {key: 1, width: 100, field: 'urgentDegree', label: `类型`, visible: true, dict: 'urgent_degree'},
        {
          key: 2, width: 100, field: 'field3', label: `明细`, visible: true, slots: true, render: (row, index) => {
            return (
              <el-button
                size="mini"
                type="text" onClick={e => this.handleViewEventInfo(e, row)}>查看明细</el-button>
            )
          }
        },
        {key: 3, width: 200, field: 'name', label: `施工单`, visible: true},
        {key: 4, width: 200, field: 'code', label: `施工单编号`, visible: true, sortable: 'custom'},
        {key: 5, width: 100, field: 'domainName', label: `管养单位`, visible: true},
        {key: 6, width: 100, field: 'glcDomainName', label: `管理处`, visible: true},
        {key: 7, width: 100, field: 'maiSecId', label: `路段名称`, visible: true},
        {key: 9, width: 200, field: 'conDomainName', label: `施工单位`, visible: true},
        {key: 10, width: 200, field: 'conConName', label: `施工合同`, visible: true},
        {key: 11, width: 200, field: 'supDomainName', label: `监理单位`, visible: true},
        {key: 12, width: 200, field: 'supConName', label: `监理合同`, visible: true},
        {key: 13, width: 100, field: 'field14', label: `发出时间`, visible: true},
        {key: 14, width: 100, field: 'defLiaPer', label: `缺陷责任期`, visible: true},
        {key: 15, width: 200, field: 'content', label: `工程内容`,
          visible: true,
          slots: true,
          render: (row) => {
            return (
                <el-tooltip className="item" effect="dark" content={row.content} placement="top-start">
                  <div style="overflow: hidden;white-space:nowrap;text-overflow:ellipsis">{row.content}</div>
                </el-tooltip>
            )
          }
        },
        {key: 16, width: 100, field: 'updateByName', label: `操作人`, visible: true},
        //{key: 17, width: 100, field: 'receiveByName', label: `接收人`, visible: true},
        // {key: 18, width: 100, field: 'field19', label: `验收人`, visible: true},
        {key: 19, width: 200, field: 'beginDate', label: `计划开始时间`, visible: true, sortable: 'custom'},
        {key: 20, width: 200, field: 'endDate', label: `计划结束时间`, visible: true, sortable: 'custom'}
      ],
      eventColumns: [
        {key: 21, width: 100, field: 'stage', label: `阶段`, visible: true, dict: 'dis_stage_name'},
        {key: 0, width: 150, field: 'assetMainType', label: `资产类型`, visible: true, dict: 'sys_asset_type'},
        {key: 1, width: 100, field: 'maiSecId', label: `路段`, visible: true},
        {key: 2, width: 100, field: 'routeCode', label: `路线编码`, visible: true},
        {key: 3, width: 100, field: 'direction', label: `上下行`, visible: true, dict: 'route_direction'},
        {key: 4, width: 100, field: 'lane', label: `位置`, visible: true, dict: 'lane'},
        {key: 7, width: 150, field: 'costTypeName', label: `费用类型`, visible: true, dict: 'cost_name'},
        {key: 5, width: 100, field: 'beginMile', label: `起点桩号`, visible: true, slots: true, render: (row, index) => {
            return (
                <span>{this.formatPile(row.beginMile)}</span>
            )
          }},
        {key: 6, width: 100, field: 'endMile', label: `终点桩号`, visible: true, slots: true, render: (row, index) => {
            return (
                <span>{this.formatPile(row.endMile)}</span>
            )
          }},
        {key: 7, width: 100, field: 'disTypeName', label: `事件类型`, visible: true, dict: 'sys_asset_type'},
        {key: 8, width: 100, field: 'dealType', label: `处置类型`, visible: true, dict: 'disposal_type'},
        {key: 9, width: 200, field: 'disDesc', label: `描述`, visible: true},
        {key: 10, width: 100, field: 'remark', label: `备注`, visible: true},
        {key: 11, width: 100, field: 'collectTime', label: `采集时间`, visible: true},
        {key: 12, width: 100, field: 'updateByName', label: `操作人`, visible: true},
      ],
      // 表格数据
      dataList: [],
      eventList: [],
      // 总条数
      total: 0,
      // 选中数组
      ids: [],
      detailTitle: '施工通知单确认',
      openDetail: false,
      openEventInfo: false,
      openOperateInfo: false,
      openGather: false,
      rowData: {},
      checkRow: {},
      readOnly: true,
      openFileList: false,
      inId: '',
      openReview: false,
      preview: {
        html: '',
        url: '',
        fileName: ''
      },
      remark: ''
    };
  },
  watch: {},
  created() {

  },
  mounted() {
    if (this.$route.query.code) {
      this.queryParams.code = this.$route.query.code
    }
    this.clearQueryParam()
    this.handleQuery()
  },
  methods: {
    nodeInfo,
    clearQueryParam() {
      const newUrl = window.location.href.replace(/\?.*$/, "");
      window.history.replaceState(null, '', newUrl);
    },
    // 表格查询
    handleQuery() {
      this.loading = true;
      this.queryParams.year = parseInt(this.queryParams.yearStr) || null
      this.queryParams.domainId = parseInt(this.queryParams.domainIdStr) || null
      this.queryParams.stage = 1
      querySupervisionAuditList(this.queryParams).then(res => {
        this.dataList = res.rows
        this.total = res.total
        this.loading = false;
      })
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection
    },
    // 恢复
    resetQuery() {
      this.queryParams = {
        pageNum: 1,
        pageSize: 50,
      }
      this.handleQuery()
    },
    //导出按钮操作
    handleExport() {
      this.download('manager/constructionConfirm/export', {
        ...this.queryParams
      }, `constructionConfirm_${new Date().getTime()}.xlsx`,
        {
          headers: { 'Content-Type': 'application/json;' },
          parameterType: 'body'
        })
    },
    handleOpenDetail(row) {
      this.rowData = row
      this.openDetail = true
    },
    // 查看
    handleViewEventInfo(e, data) {
      eventInfoByConId({pageSize:9999, conId: data.id}).then(res => {
        this.eventList = res.rows
      })
      this.openEventInfo = true
    },
    handleViewOperateInfo() {
      if (!this.checkRow.id) {
        this.$modal.msgWarning("请选择一条数据")
        return
      }
      this.openOperateInfo = true
    },
    // 关闭详情弹窗
    handleCloseDetail() {
      this.openDetail = false;
      this.handleQuery();
    },
    // 批量接受
    handleBatchConfirm() {
      if (this.ids.length > 0) {
        this.remark = ''
        this.openReview = true
      }
    },
    handleBatchReceive() {
      this.openReview = false
      batchReceiveConfirm(this.ids).then(res => {
        if (res.code == 200) {
          this.$message.success('接收成功')
          this.handleQuery()
        }
      })
    },
    handleBatchReject() {
      if(!this.remake) {
        this.$modal.msgWarning("拒收需要填写拒收原因");
        return
      }
      for (let i = 0; i < this.ids.length; i++) {
        this.ids[i].remark = this.remark
      }
      this.openReview = false
      batchRejectConfirm(this.ids).then(res => {
        if (res.code == 200) {
          this.$message.success('撤回成功')
          this.handleQuery()
        }
      })
    },
    handleGather() {
      this.openGather = true
    },
    handleViewFile(row) {
      this.inId = row.id
      this.openFileList = true
    },
    // 排序
    sortChange(e) {
      if(e.order){
        this.queryParams.sortColumn = this.camelToSnake(e.prop);
        this.queryParams.sortOrder = e.order == 'descending' ? 'desc' : 'asc';
      }else{
        this.queryParams.sortColumn = ''
        this.queryParams.sortOrder = '';
      }
      this.handleQuery();
    },
    handleClickRow(e) {
      e.isSelected = !e.isSelected;
      this.$refs.dataTable.toggleRowSelection(e);
      this.checkRow = e
    },
    camelToSnake(str) {
      return str.replace(/([A-Z])/g, (match) => `_${match.toLowerCase()}`);
    },
    handlePreview() {
      if (!this.checkRow.id) {
        this.$modal.msgWarning("请选择一条数据")
        return
      }
      this.loading = true
      getPreviewUrl(this.checkRow.id, '是').then(res => {
        if (res.code == 200){
          this.preview.html = res.data.html
          this.preview.url = res.data.downUrl
          this.preview.fileName = res.data.fileName
          this.$refs.iframeRef.visible = true
        }
        this.loading = false
      })
    },
    handleRegenerate() {
      if (this.ids.length == 0) {
        this.$modal.msgError("请勾选至少一条数据")
        return
      }
      const params = {
        idList: this.ids.map(item => item.id),
        type: 1
      }
      regenerateReport(params).then(res => {
        this.$modal.msgSuccess("操作成功")
      })
    }
  }
};
</script>
<style>
</style>
<style lang="scss" scoped>
@import "@/assets/styles/business.scss";
</style>
