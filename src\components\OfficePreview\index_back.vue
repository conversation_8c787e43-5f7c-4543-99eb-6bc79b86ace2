<template>
  <div class="file-preview">
    <div v-if="contentType || dialogUrl">
      <img v-if="isImage" width="100%" :src="dialogUrl" alt="" />
      <!-- Office 文件预览 -->
      <iframe
        v-else-if="isOffice"
        :src="officeUrl"
        style="width: 100%; height: 100vh"
        frameborder="0"
      ></iframe>
      <!-- PDF 文件预览 -->
      <vue-office-pdf
        v-else-if="isPDF"
        :src="dialogUrl"
        style="height: 100vh"
      />
      <img v-else :src="thumbnailUrl" alt="文件缩略图" />
    </div>
  </div>
</template>

<script>
import VueOfficePdf from "@vue-office/pdf";
export default {
  name: "OfficePreview",
  components: {
    VueOfficePdf,
  },
  props: {
    dialogUrl: {
      type: String,
      required: true,
    },
    contentType: {
      type: String,
      required: true,
    },
  },
  computed: {
    // 检查文件是否为图像
    isImage() {
      return this.contentType.startsWith("image/");
    },
    // 检查文件是否为Office文档类型（Word, Excel, PowerPoint）
    isOffice() {
      return this.isWord || this.isExcel || this.isPowerPoint;
    },
    // 检查文件是否为Word
    isWord() {
      return (
        "application/msword" === this.contentType ||
        "application/vnd.openxmlformats-officedocument.wordprocessingml.document" ===
          this.contentType
      );
    },
    // 检查文件是否为Excel
    isExcel() {
      return (
        "application/vnd.ms-excel" === this.contentType ||
        "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet" ===
          this.contentType
      );
    },
    // 检查文件是否为PowerPoint
    isPowerPoint() {
      return (
        "application/vnd.ms-powerpoint" === this.contentType ||
        "application/vnd.openxmlformats-officedocument.presentationml.presentation" ===
          this.contentType
      );
    },
    // 检查文件是否为PDF
    isPDF() {
      return "application/pdf" === this.contentType;
    },
    // 根据文件类型计算缩略图 URL
    thumbnailUrl() {
      switch (this.contentType) {
        case "application/vnd.ms-powerpoint":
        case "application/vnd.openxmlformats-officedocument.presentationml.presentation":
          return require("@/assets/images/Ippt.png");
        case "application/zip":
        case "application/x-zip-compressed":
        case "application/x-rar-compressed":
        case "application/x-7z-compressed":
        case "application/x-tar":
        case "application/gzip":
          return require("@/assets/images/Izip.png");
        default:
          return require("@/assets/images/Iother.png");
      }
    },
    // 后端预览的地址
    officeUrl() {
      return `https://view.officeapps.live.com/op/view.aspx?src=${encodeURIComponent(this.dialogUrl)}`;
    },
  },
};
</script>
