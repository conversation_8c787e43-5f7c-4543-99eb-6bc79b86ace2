<template>
  <div>
    <el-dialog
      title="部件评定详情"
      :visible.sync="showDetail"
      width="55%"
      append-to-body
      :before-close="handleClose"
      :close-on-click-modal="true"
    >
    <!-- <el-button
      class="mb8"
        type="primary"
        @click="importUpdate"
      >导入更新</el-button>
      <el-button
      class="mb8"
        type="primary"
        @click="importAdd"
      >导入新增</el-button> -->
      
      <el-table
        v-loading="loading"
        height="250px"
        border
        :data="tableData"
        :header-cell-style="{'background':'#F2F3F5','color': '#212529','height': '36px','font-weight': '700','font-size': '14px'}"
        :cell-style="{'height': '36px'}"
      >
        <el-table-column
          label="序号"
          type="index"
          width="50"
          align="center"
        />
        <el-table-column
          label="部件等级"
          align="center"
          prop="level"
          min-width="120"
          show-overflow-tooltip
        >
          <template slot-scope="scope">
            <span v-if="scope.row && scope.row.level">
              <DictTag
                :value="scope.row.level"
                :options="dict.type.bridge_tec_condition_level"
              />
            </span>
          </template>
        </el-table-column>
        <el-table-column
          label="部件名称"
          align="center"
          prop="name"
          min-width="120"
          show-overflow-tooltip
        />
        <el-table-column
          label="部件得分"
          align="center"
          prop="score"
          min-width="120"
          show-overflow-tooltip
        />
        <el-table-column
          label="部件权重"
          align="center"
          prop="weight"
          min-width="120"
          show-overflow-tooltip
        />
        <el-table-column
            fixed="right"
            label="操作"
            align="center"
            min-width="120"
          >
          <template slot-scope="scope">
            <el-button
              style="font-size: 14px;font-weight: 400;"
              type="text"
              @click.stop="handleView(scope.row)"
            >构件信息</el-button>
          </template>
        </el-table-column>
      </el-table>
      <pagination
        :total="total"
        :page.sync="pageNum"
        :limit.sync="pageSize"
        @pagination="getList"
      />
      <div slot="footer">
        <el-button @click="handleClose">取 消</el-button>
      </div>
    </el-dialog>
    <Component
        v-if="showComDetail"
        :showComDetail="showComDetail"
        :componId="componId"
        @close="() => {showComDetail = false;}"
      />
    <ImportData
      v-if="showImportAdd"
      :is-update="isUpdate"
      :dialog-visible="showImportAdd"
      :import-base-type="'27'"
      :import-type="importType"
      @close="closeImportAdd"
    />
    </div>
</template>

<script>
import {
  evaluatePosition,
} from '@/api/baseData/bridge/evaluate/index'
import DictTag from "@/components/DictTag";
import Component from './component.vue'
import ImportData from '@/views/baseData/components/importData/index.vue'

export default {
  name: 'sideSlope-protections',
  components: { Component,ImportData},
  props: {
    showDetail: { type: Boolean, default: false },
    choseId: { type: undefined, default: '' },
    formData: { type: Object, default: () => {} }
  },
  dicts: ['bridge_tec_condition_level'],
  data() {
    return {
      loading: false,
      pageNum: 1,
      pageSize: 20,
      total: 0,
      tableData: [],
      showComDetail:false,
      componId:null,
      showImportAdd: false,
      importType: 1,
      isUpdate: false


    }
  },
  created() {
    this.getList()
  },
  methods: {
    getList() {
      if (!this.choseId) return
      this.loading = true
      let obj = {
        evaluateId : this.choseId,
        pageNum: this.pageNum,
        pageSize: this.pageSize
      }
      evaluatePosition(obj)
        .then(res => {
          if (res.code === 200) {
            this.tableData = res.rows
            this.total = res.total
          }
          this.loading = false
        })
        .catch(() => {
          this.loading = false
        })
    },
    handleClose() {
      this.$emit('close')
    },
    // 导入更新按钮
    importUpdate() {
      this.isUpdate = true
      this.showImportAdd = true
      this.importType = 1
    },
    // 导入新增按钮
    importAdd() {
      this.isUpdate = false
      this.showImportAdd = true
      this.importType = 2
    },
    handleView(row) {
      this.showComDetail = true
      this.componId = row.id
    },
    closeImportAdd(v) {
      this.showImportAdd = false
      if (v) this.getList()
    },
  },
  watch: {
    // slopeId(newVal, oldVal) {
    //   this.tableData = []
    //   this.getList(newVal)
    // }
  }
}
</script>

<style lang="scss" scoped>
::v-deep .el-form-item {
  margin-bottom: 10px;
  color: #1d2129;
}
</style>