<template>
  <div>
    <el-dialog
      :title="title"
      :visible.sync="showDrawing"
      width="80%"
      append-to-body
      :before-close="handleClose"
      :close-on-click-modal="false"
    >
      <CardBox
        style="height: calc(100vh - 200px); padding: 0px;"
        :typeCode="typeCode"
        :forView="true"
        v-for="(id,index) in treeIds"
        :bizId="id"
        :key="id"
      />
    </el-dialog>
  </div>
</template>

<script>
import CardBox from '@/components/Drawing/cardBox.vue'

export default {
  name: 'view-drawings',
  props: {
    typeCode: {type: String, default: 'BS129'},
    showDrawing: { type: Boolean, default: false },
    id: { type: undefined, default: '' },
    title: { type: String, default: '桥梁图纸文件' }
  },
  components: { CardBox },
  provide() {
    return {
      oneMap: false
    }
  },
  data() {
    return {
      treeIds: []
    }
  },
  created() {
    this.treeIds = [this.id]
  },
  methods: {
    handleClose() {
      this.$emit('close')
    }
  },
  computed: {},
  watch: {}
}
</script>

<style scoped>
</style>