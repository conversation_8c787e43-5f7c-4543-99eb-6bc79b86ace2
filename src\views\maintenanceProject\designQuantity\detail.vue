<template>
  <div class="road-interflow-edit" style="padding: 20px" v-loading="loading">
    <el-row :gutter="20">
      <el-form
          ref="elForm"
          :model="formData"
          :rules="rules"
          :disabled="readOnly"
          size="medium"
          label-width="130px"
      >
        <el-col :span="8">
          <el-form-item label="设计单位" prop="designDomainId">
            <construction-select style="width: 210px" v-model="formData.designDomainId" :mai-sec-id="formData.maiSecId" :type="1" placeholder="设计单位"></construction-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="施工合同" prop="conId">
            <contract-section v-model="formData.conId" valueType="object" :params="contractFilterMap"
                              placeholder="请选择合同" @change="addContract" style="width: 210px"></contract-section>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="设计资金" prop="designFund">
            <el-input readonly v-model="formData.designFund" placeholder="设计资金" style="width: 210px"/>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="管养单位" prop="domainName">
            <el-input readonly v-model="formData.domainName" placeholder="管养单位" style="width: 210px"/>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="项目编码" prop="code">
            <el-input readonly v-model="formData.code" placeholder="项目编码" style="width: 210px"/>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="路段名称" prop="maiSecName">
            <el-input readonly v-model="formData.maiSecName" placeholder="设计单位" style="width: 210px"/>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-tabs v-model="editableTabsValue" closable tab-remove="removeTab">
            <el-tab-pane
                v-for="(item, index) in contractList"
                :key="item.conId"
                :label="item.conName"
                :name="item.conId"
            >
              <el-button type="primary" @click="addScheme(item.conId, index)" style="margin-bottom:10px;float:right">
                新增
              </el-button>
              <el-table v-adjust-table
                  :data="item.schemeList"
                  border
                  height="350px"
                  ref="tableRef"
                  style="width: 100%">
                <el-table-column
                    label="序号"
                    align="center"
                    type="index"
                    width="50"
                />
                <el-table-column
                    prop="schemeCode"
                    align="center"
                    label="子目号">
                </el-table-column>
                <el-table-column
                    prop="schemeName"
                    align="center"
                    label="子目名称">
                </el-table-column>
                <el-table-column
                    prop="unit"
                    align="center"
                    label="单位">
                </el-table-column>
                <el-table-column
                    prop="price"
                    align="center"
                    label="单价">
                </el-table-column>
                <el-table-column
                    prop="calcDesc"
                    align="center"
                    label="计算式"
                    width="200">
                  <template slot-scope="scope">
                    <el-input v-model="scope.row.calcDesc" @change="changeCalculation(scope.row)">
                    </el-input>
                  </template>
                </el-table-column>
                <el-table-column
                    prop="num"
                    align="center"
                    label="设计数量"
                    width="100">
                  <template slot-scope="scope">
                    <el-input v-model="scope.row.num" @change="changeSchemeNum(scope.row)">
                    </el-input>
                  </template>
                </el-table-column>
                <el-table-column
                    prop="amount"
                    align="center"
                    label="金额"
                    width="100">
                </el-table-column>
                <el-table-column
                    prop="remark"
                    align="center"
                    label="备注"
                    width="100">
                  <template slot-scope="scope">
                    <el-input v-model="scope.row.remark">
                    </el-input>
                  </template>
                </el-table-column>
                <el-table-column
                    prop="field101"
                    align="center"
                    label="操作">
                  <template slot-scope="scope">
                    <el-button
                        size="mini"
                        type="text"
                        @click="handleDelete(scope.row)"
                    >移除
                    </el-button
                    >
                  </template>
                </el-table-column>
              </el-table>
            </el-tab-pane>
          </el-tabs>
        </el-col>
        <el-col :span="24" class="mt20">
          <el-form-item label="附件" prop="fileId">
            <file-upload key="fileId" v-model="formData.fileId" :owner-id="formData.fileId"></file-upload>
          </el-form-item>
        </el-col>
        <el-col :span="24" class="mt10">
          <div style="text-align: right">
            <el-button type="primary" @click="handleSave">保存</el-button>
          </div>
        </el-col>
      </el-form>
    </el-row>
    <methods-tree v-if="this.contractList.length > 0" scheme-type="专项养护" :con-id="conId" ref="methodsRef" @input="checkLib" :domain-id="formData.domainId"></methods-tree>
  </div>
</template>

<script>
import ConstructionSelect from "@/components/ConstructionSelect/index.vue";
import ContractSection from "@/components/ContractSection/index.vue";
import MethodsTree from "@/components/MethodsTree/index.vue";
import {add, edit, get} from "@/api/maintenanceProject/designQuantity";
import {v4 as uuidv4} from "uuid";
import { Decimal } from 'decimal.js';

export default {
  components: {MethodsTree, ContractSection, ConstructionSelect},
  props: {
    rowData: {
      type: Object,
      default: () => {
      }
    },
    readOnly: {
      type: Boolean,
      default: false
    },
    type: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      loading: false,
      formData: {},
      rules: {},
      editableTabsValue: '',
      contractList: [],
      treeIndex: 0,
      conId: '',
      contractFilterMap: {},
    }
  },
  created() {
  },
  watch: {
    rowData: {
      handler(newVal) {
        if (this.type != 1) {
          get(newVal.id).then(res => {
            res.data.detailList.forEach(item => {
              if (!this.editableTabsValue || this.editableTabsValue == 0) this.editableTabsValue = item.conId
              this.contractList.push({
                conId: item.conId,
                conName: item.conName,
                schemeList: [...item.children]
              })
            })
          })
        }
        this.formData = JSON.parse(JSON.stringify(newVal))
        if (!this.formData.fileId) this.formData.fileId = uuidv4().replace(/-/g, '').slice(0, 20)
      },
      immediate: true,
      deep: true
    }
  },
  methods: {
    addContract(e) {
      if (e) {
        this.formData.conId = ''
        this.editableTabsValue = e.value
        const exists = this.contractList.some(contract => contract.conId === e.value);
        if (!exists) {
          // 如果不存在，则添加到 contractList
          this.contractList.push({
            conId: e.value,
            conName: e.name,
            schemeList: []
          });
        }
      }
    },
    removeTab(targetName) {
      let tabs = this.contractList;
      let activeName = this.editableTabsValue;
      if (activeName === targetName) {
        tabs.forEach((tab, index) => {
          if (tab.conId === targetName) {
            let nextTab = tabs[index + 1] || tabs[index - 1];
            if (nextTab) {
              activeName = nextTab.conId;
            }
          }
        });
      }
      this.editableTabsValue = activeName;
      this.contractList = tabs.filter(tab => tab.conId !== targetName);
    },
    addScheme(e, index) {
      this.conId = e
      this.treeIndex = index
      this.$refs.methodsRef.openLibModel()
    },
    checkLib(checkDatas) {
      const schemeList = []
      checkDatas = checkDatas.filter(item => {
        return item.nodeType == 2
      })
      checkDatas.forEach(item => {
        schemeList.push({
          conId: this.contractList[this.treeIndex].conId,
          schemeId: item.id,
          schemeCode: item.schemeCode,
          schemeName: item.schemeName,
          unit: item.unit,
          price: item.price,
          priceRate: item.priceRate,
          decimalPlaces: item.decimalPlaces
        })
      })
      this.contractList[this.treeIndex].schemeList.push(...schemeList)
      this.contractList[this.treeIndex].schemeList = this.contractList[this.treeIndex].schemeList.reduce((acc, curr) => {
        const exists = acc.some(item => item.schemeId === curr.schemeId);
        return exists ? acc : [...acc, curr];
      }, []);
    },
    handleDelete(row) {
      console.log(this.contractList[this.treeIndex].schemeList)
      this.contractList[this.treeIndex].schemeList = this.contractList[this.treeIndex].schemeList.filter(item => item.schemeId !== row.schemeId);
    },
    handleSave() {
      this.$refs.elForm.validate(valid => {
        if (!valid) return
        this.formData.detailList = this.contractList.reduce((acc, curr) => {
          return acc.concat(curr.schemeList)
        }, [])
        if (this.formData.fileId && Array.isArray(this.formData.fileId) && this.formData.fileId.length > 0) {
          this.formData.fileId = this.formData.fileId[0]
        } else if (Array.isArray(this.formData.fileId) &&
          this.formData.fileId.length == 0){
          this.formData.fileId = null
        }
        const addParams = {
          projId: this.formData.id,
          designDomainId: this.formData.designDomainId,
          designFund: this.formData.designFund,
          detailList: this.formData.detailList,
          fileId: this.formData.fileId
        }
        this.loading = true
        if (this.type == 1) {
          add(addParams).then(res => {
            this.loading = false
            this.$message.success('保存成功')
            this.$emit('close')
          })
        } else if (this.type == 2) {
          edit(addParams).then(res => {
            this.loading = false
            this.$message.success('保存成功')
            this.$emit('close')
          })
        }
      })
    },
    async changeCalculation(row) {
      if (!this.isValidMathFormula(row.calcDesc)) {
        this.$modal.msgError('计算式错误，请检查')
        return
      }
      let num =  this.math.evaluate(row.calcDesc) || 0
      this.$set(row, 'num', this.ceilToTwo(num, row.decimalPlaces))
      await this.changeSchemeNum(row)
    },
    async changeSchemeNum(row) {
      let money = new Decimal(row.num || 0).times(row.price || 0).toNumber()
      this.$set(row, 'amount', Math.round(money))
      let schemeList = this.contractList.reduce((acc, curr) => {
        return acc.concat(curr.schemeList)
      }, [])
      const total = await schemeList.reduce((acc, curr) => Number(acc) + Number(curr.amount || 0), 0)
      this.$set(this.formData, 'designFund', total)
    },
  }
}
</script>

<style scoped lang="scss">

</style>
<style lang="scss" scoped>
@import "@/assets/styles/business.scss";
</style>
