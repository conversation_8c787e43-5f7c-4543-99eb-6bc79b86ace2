<template>
  <div class="e-charts" :style="{ height: height }"></div>
</template>

<script>
import * as echarts from 'echarts'

export default {
  props: {
    height: {
      type: String,
      default: '260px'
    },
    option: {
      type: Object,
      default: () => { }
    }
  },
  data() {
    return {
      chart: null
    }
  },
  mounted() {
    setTimeout(()=> {
      this.initChart()
    }, 200)
  },
  methods: {
    initChart() {
      this.chart = echarts.init(this.$el);
      this.chart.setOption(this.option)
      // 监听窗口变化，重新渲染图表
      window.addEventListener('resize', () => {
        this.chart.resize();
      });
    }
  },
}
</script>

<style lang="scss" scoped>
.e-charts {
  width: 100%;
}
</style>
