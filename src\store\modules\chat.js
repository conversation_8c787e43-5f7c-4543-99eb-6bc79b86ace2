// store/modules/chat.js
export default {
  namespaced: true, // 添加命名空间
  state: {
    chatLoaded: false,
  },
  mutations: {
    SET_CHAT_LOADED(state, status) {
      state.chatLoaded = status;
    },
  },
  actions: {
    initChat({ commit, rootState, state }) {
      // 修复state引用
      if (!rootState.user.token || state.chatLoaded) return;

      return new Promise((resolve) => {
        const script = document.createElement('script');
        script.src =
          'http://***************:22580/api/application/embed?protocol=http&host=***************:22580&token=4028516c1c74b2f5';
        script.async = true;

        script.onload = () => {
          commit('SET_CHAT_LOADED', true);
          if (window.MaxKB?.init) {
            window.MaxKB.init();
          }
          resolve();
        };

        document.body.appendChild(script);
      });
    },
  },
};
