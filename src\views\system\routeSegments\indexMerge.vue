<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" label-width="86px">
      <el-form-item label="" prop="baseRouteId">
        <el-select v-model="queryParams.baseRouteId" clearable filterable placeholder="请选择路线">
          <el-option
            v-for="item in routeList"
            :key="item.routeId"
            :label="item.routeName"
            :value="item.routeId">
          </el-option>
        </el-select>
      </el-form-item>



      <el-form-item label="" prop="maintenanceSectionId">
        <el-select v-model="queryParams.maintenanceSectionId" clearable filterable placeholder="请选择养护路段">
          <el-option
            v-for="item in maintenanceSectionList"
            :key="item.maintenanceSectionId"
            :label="item.maintenanceSectionName"
            :value="item.maintenanceSectionId">
          </el-option>
        </el-select>
      </el-form-item>



      <el-form-item label="" prop="roadSectionId">
        <el-select v-model="queryParams.roadSectionId" clearable filterable placeholder="请选择公路路段">
          <el-option
            v-for="item in highwaySectionList"
            :key="item.roadSectionId"
            :label="item.roadSectionName"
            :value="item.roadSectionId">
          </el-option>
        </el-select>
      </el-form-item>
<!--      <el-form-item label="" prop="routeSegmentsName">-->
<!--        <el-input-->
<!--          v-model="queryParams.routeSegmentsName"-->
<!--          placeholder="请输入子段名称"-->
<!--          clearable-->
<!--          @keyup.enter.native="handleQuery"-->
<!--        />-->
<!--      </el-form-item>-->





<!--      <el-form-item label="" prop="roadType">-->
<!--        <el-select v-model="queryParams.roadType" placeholder="请选择路段类型" clearable>-->
<!--          <el-option-->
<!--            v-for="dict in dict.type.sys_route_type"-->
<!--            :key="dict.value"-->
<!--            :label="dict.label"-->
<!--            :value="dict.value"-->
<!--          />-->
<!--        </el-select>-->
<!--      </el-form-item>-->

      <el-form-item  label="" prop="maintenanceUnit">
        <treeselect v-model="queryParams.maintenanceUnit" :options="deptOptions" :show-count="true"
                    placeholder="请选择管养单位"/>
      </el-form-item>
      <el-form-item label="" prop="ownerUnit">
        <el-select v-model="queryParams.ownerUnit" placeholder="请选择权属类型" clearable>
          <el-option label="集团公司" value="集团公司"></el-option>
          <el-option label="项目公司" value="项目公司"></el-option>
        </el-select>
      </el-form-item>

      <el-form-item label="" prop="option" v-show="false">
        <el-select v-model="queryParams.option" placeholder="请选择合并维度" clearable>
          <el-option label="养护路段" value="养护路段"></el-option>
          <el-option label="管养单位" value="管养单位"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
      <br>

<!--      <el-form-item v-show="showSearch" label="" prop="roadSurfaceType">-->
<!--        <el-select v-model="queryParams.roadSurfaceType" placeholder="请选择路面类型" clearable>-->
<!--          <el-option-->
<!--            v-for="dict in dict.type.sys_surface_type"-->
<!--            :key="dict.value"-->
<!--            :label="dict.label"-->
<!--            :value="dict.value"-->
<!--          />-->
<!--        </el-select>-->
<!--      </el-form-item>-->

<!--      <el-form-item v-show="showSearch" label="" prop="state">-->
<!--        <el-select v-model="queryParams.state" placeholder="请选择运营状态" clearable>-->
<!--          <el-option-->
<!--            v-for="dict in dict.type.sys_operation_state"-->
<!--            :key="dict.value"-->
<!--            :label="dict.label"-->
<!--            :value="dict.value"-->
<!--          />-->
<!--        </el-select>-->
<!--      </el-form-item>-->

<!--      <el-form-item  v-show="showSearch"  prop="lanesStr">-->
<!--        <el-select v-model="queryParams.lanesStr" placeholder="请选择车道数" clearable multiple>-->
<!--          <el-option-->
<!--            v-for="dict in lanesOptions"-->
<!--            :label="dict"-->
<!--            :value="dict"-->
<!--          />-->
<!--        </el-select>-->
<!--      </el-form-item>-->


<!--      <el-form-item  v-show="showSearch" label="" prop="roadGrade">-->
<!--        <el-select v-model="queryParams.roadGrade" placeholder="请选择路线等级" clearable>-->
<!--          <el-option-->
<!--            v-for="dict in dict.type.sys_route_grade"-->
<!--            :key="dict.value"-->
<!--            :label="dict.label"-->
<!--            :value="dict.value"-->
<!--          />-->
<!--        </el-select>-->
<!--      </el-form-item>-->

<!--      <el-form-item v-show="showSearch" label="" prop="dataRange">-->


<!--        <el-date-picker-->
<!--          v-model="queryParams.dataRange"-->
<!--          type="datetimerange"-->
<!--          value-format="yyyy-MM-dd"-->
<!--          range-separator="至"-->
<!--          start-placeholder="通车时间"-->
<!--          end-placeholder="结束日期">-->
<!--        </el-date-picker>-->
<!--      </el-form-item>-->
    </el-form>

<!--    <el-row :gutter="10" class="mb8">-->
<!--      <el-col :span="1.5">-->
<!--        <el-button-->
<!--          type="primary"-->
<!--          icon="el-icon-plus"-->
<!--          size="mini"-->
<!--          @click="handleAdd"-->
<!--          v-hasPermi="['system:routeSegments:add']"-->
<!--        >新增-->
<!--        </el-button>-->
<!--      </el-col>-->
<!--      <el-col :span="1.5">-->
<!--        <el-button-->
<!--          type="success"-->
<!--          icon="el-icon-edit"-->
<!--          size="mini"-->
<!--          :disabled="single"-->
<!--          @click="handleUpdate"-->
<!--          v-hasPermi="['system:routeSegments:edit']"-->
<!--        >修改-->
<!--        </el-button>-->
<!--      </el-col>-->
<!--      <el-col :span="1.5">-->
<!--        <el-button-->
<!--          type="danger"-->
<!--          icon="el-icon-delete"-->
<!--          size="mini"-->
<!--          :disabled="multiple"-->
<!--          @click="handleDelete"-->
<!--          v-hasPermi="['system:routeSegments:remove']"-->
<!--        >删除-->
<!--        </el-button>-->
<!--      </el-col>-->
<!--      <el-col :span="1.5">-->
<!--        <el-button-->
<!--          type="warning"-->
<!--          icon="el-icon-download"-->
<!--          size="mini"-->
<!--          @click="handleExport"-->
<!--          v-hasPermi="['system:routeSegments:export']"-->
<!--        >导出-->
<!--        </el-button>-->
<!--      </el-col>-->
<!--      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>-->
<!--    </el-row>-->
    <!--数据表格开始-->
    <div class="tableDiv">
      <el-table v-adjust-table size="mini" :height="showSearch ? 'calc(100vh - 320px)' : 'calc(100vh - 260px)'" border
                v-loading="loading" :data="routeSegmentsList" @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" align="center"/>
        <el-table-column label="序号" fixed align="center" type="index" width="50">
          <template v-slot="scope">
            {{ (scope.$index + (queryParams.pageNum - 1) * queryParams.pageSize)+1 }}
          </template>
        </el-table-column>
<!--        <el-table-column label="子段名称" fixed :show-overflow-tooltip="true" align="center" prop="routeSegmentsName" width="240"/>-->
        <el-table-column label="养护路段"  :show-overflow-tooltip="true" align="center"
                         prop="maintenanceSectionName" width="120"/>
        <el-table-column label="路线编码" :show-overflow-tooltip="true" align="center" prop="baseRouteCode" width="120"/>
<!--        <el-table-column label="路线" :show-overflow-tooltip="true" align="center" prop="baseRouteName" width="120"/>-->
        <el-table-column label="管养单位" :show-overflow-tooltip="true" align="center" prop="maintenanceUnitName"
                         width="120"/>
        <el-table-column label="路线等级" width="180" :show-overflow-tooltip="true" align="center" prop="roadGrade">
          <template slot-scope="scope">
            <dict-tag :options="dict.type.sys_route_grade" :value="scope.row.roadGrade"/>
          </template>
        </el-table-column>

        <el-table-column label="路面类型" :show-overflow-tooltip="true" align="center" prop="roadSurfaceType"
                         width="120">
          <template slot-scope="scope">
            <dict-tag :options="dict.type.sys_surface_type" :value="scope.row.roadSurfaceType"/>
          </template>
        </el-table-column>
        <!--        <el-table-column label="匝道口" :show-overflow-tooltip="true" align="center" prop="ramp">-->
        <!--          <template slot-scope="scope">-->
        <!--            <dict-tag :options="dict.type.sys_ramp" :value="scope.row.ramp"/>-->
        <!--          </template>-->
        <!--        </el-table-column>-->
        <el-table-column label="车道数" :show-overflow-tooltip="true" align="center" prop="lanes"/>

        <el-table-column label="起点桩号" :show-overflow-tooltip="true" align="center" prop="pileStart" width="100" :formatter="(...arg)=>formatPile(arg[2])"/>
        <el-table-column label="终点桩号" :show-overflow-tooltip="true" align="center" prop="pileEnd"   width="100"  :formatter="(...arg)=>formatPile(arg[2])"/>
        <el-table-column label="起点名称" :show-overflow-tooltip="true" align="center" prop="placeStartName" width="160"/>
        <el-table-column label="终点名称" :show-overflow-tooltip="true" align="center" prop="placeEndName" width="160"/>
        <el-table-column label="统计里程(km)" :show-overflow-tooltip="true" align="center" prop="roadSectionLength"
                         :formatter="(...arg)=>{if(arg[2]) return (arg[2]/1000).toFixed(3).toLocaleString()}"
                         width="130"/>
        <el-table-column label="养护里程(km)" :show-overflow-tooltip="true" align="center" prop="mainLength"
                         width="120"
                         :formatter="(...arg)=>{if(arg[2]) return (arg[2]/1000).toFixed(3).toLocaleString()}"/>


<!--        <el-table-column label="统一里程起点桩号" :show-overflow-tooltip="true" align="center" width="140"-->
<!--                         prop="unifiedMileagePileStart"-->
<!--                         :formatter="(...arg)=>formatPile(arg[2])"/>-->
<!--        <el-table-column label="统一里程终点桩号" :show-overflow-tooltip="true" align="center" width="140"-->
<!--                         prop="unifiedMileagePileEnd"-->
<!--                         :formatter="(...arg)=>formatPile(arg[2])"/>-->
        <el-table-column label="通车时间" :show-overflow-tooltip="true" align="center" prop="openingTime" width="180">
          <template slot-scope="scope">
            <span>{{ parseTime(scope.row.openingTime, '{y}-{m}-{d}') }}</span>
          </template>
        </el-table-column>
        <el-table-column label="路段类型" :show-overflow-tooltip="true" align="center" prop="roadType">
          <template slot-scope="scope">
            <dict-tag :options="dict.type.sys_route_type" :value="scope.row.roadType"/>
          </template>
        </el-table-column>
        <el-table-column label="运营状态" :show-overflow-tooltip="true" align="center" prop="state" width="120">
          <template slot-scope="scope">
            <dict-tag :options="dict.type.sys_operation_state" :value="scope.row.state"/>
          </template>
        </el-table-column>
<!--        <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="120" fixed="right">-->
<!--          <template slot-scope="scope">-->
<!--            <el-button-->
<!--              size="mini"-->
<!--              type="text"-->
<!--              icon="el-icon-edit"-->
<!--              @click="handleUpdate(scope.row)"-->
<!--              v-hasPermi="['system:routeSegments:edit']"-->
<!--            >修改-->
<!--            </el-button>-->
<!--            <el-button-->
<!--              size="mini"-->
<!--              type="text"-->
<!--              icon="el-icon-delete"-->
<!--              @click="handleDelete(scope.row)"-->
<!--              v-hasPermi="['system:routeSegments:remove']"-->
<!--            >删除-->
<!--            </el-button>-->
<!--          </template>-->
<!--        </el-table-column>-->
      </el-table>

      <pagination
        :totalMileage="(totalMileage/1000).toFixed(3).toLocaleString()"
        v-show="total>0"
        :total="total"
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
        @pagination="getList"
      >

        <span slot="mileage"  style="margin-left: 50px;" v-text="'总统计里程：' + (roadSectionLength/1000).toFixed(3).toLocaleString() + ' km'"></span>


      </pagination>
    </div>
    <!--数据表格结束-->
    <!-- 添加或修改养护子段管理对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="1100px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="140px" inline>
        <el-form-item label="养护路段" prop="maintenanceSectionId">
          <el-select v-model="form.maintenanceSectionId" filterable placeholder="请选择">
            <el-option
              v-for="item in maintenanceSectionList"
              :key="item.maintenanceSectionId"
              :label="item.maintenanceSectionName"
              :value="item.maintenanceSectionId">
            </el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="管养单位" prop="maintenanceUnit">
          <treeselect v-model="form.maintenanceUnit" :options="deptOptions" :show-count="true"
                      placeholder="请选择管养单位"/>
        </el-form-item>


        <el-form-item label="子段名称" prop="routeSegmentsName">
          <el-input v-model="form.routeSegmentsName" placeholder="请输入子段名称"/>
        </el-form-item>

        <el-form-item label="路线" prop="baseRouteId">
          <el-select v-model="form.baseRouteId" filterable placeholder="请选择">
            <el-option
              v-for="item in routeList"
              :key="item.routeId"
              :label="item.routeName"
              :value="item.routeId">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="起点名称" prop="placeStartName">
          <el-input v-model="form.placeStartName" placeholder="请输入起点名称"/>
        </el-form-item>
        <el-form-item label="终点名称" prop="placeEndName">
          <el-input v-model="form.placeEndName" placeholder="请输入终点名称"/>
        </el-form-item>
        <el-form-item label="起点桩号" prop="pileStart">
          <PileInput v-model="form.pileStart" @input="onPileChange(1)" class="pile-input"
                     placeholder="请输入起点桩号"></PileInput>
        </el-form-item>
        <el-form-item label="终点桩号" prop="pileEnd">
          <PileInput v-model="form.pileEnd" @input="onPileChange(1)" class="pile-input"
                     placeholder="请输入终点桩号"></PileInput>
        </el-form-item>
        <el-form-item label="统一里程起点桩号" prop="unifiedMileagePileStart">
          <PileInput v-model="form.unifiedMileagePileStart"  class="pile-input"
                     placeholder="请输入统一里程起点桩号"></PileInput>
        </el-form-item>
        <el-form-item label="统一里程终点桩号" prop="unifiedMileagePileEnd">
          <PileInput v-model="form.unifiedMileagePileEnd" class="pile-input"
                     placeholder="请输入统一里程终点桩号"></PileInput>
        </el-form-item>
        <el-form-item label="养护里程(m)" prop="mainLength">
          <el-input v-model="form.mainLength" placeholder="请输入主线里程(m)"/>
        </el-form-item>
        <el-form-item label="统计里程(m)" prop="roadSectionLength">
          <el-input v-model="form.roadSectionLength" placeholder="请输入子路段长度(m)"/>
        </el-form-item>
        <el-form-item label="车道数" prop="lanes">
          <el-input v-model.number="form.lanes" placeholder="请输入车道数"/>
        </el-form-item>
        <el-form-item label="路线等级" prop="roadGrade">
          <el-select v-model="form.roadGrade" placeholder="请选择路线等级">
            <el-option
              v-for="dict in dict.type.sys_route_grade"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="路段类型" prop="roadType" :key="1">
          <el-select v-model="form.roadType" placeholder="请选择路段类型">
            <el-option
              v-for="dict in dict.type.sys_route_type"
              :key="dict.value"
              :label="dict.label"
              :value="parseInt(dict.value)"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="匝道口" prop="ramp" v-if="form.roadType === 2" :key="2">
          <el-select  v-model="form.ramp" placeholder="请选择路匝道口">
            <el-option
              v-for="dict in dict.type.sys_ramp"
              :key="dict.value"
              :label="dict.label"
              :value="parseInt(dict.value)"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="路面类型" prop="roadSurfaceType">
          <el-select v-model="form.roadSurfaceType" placeholder="请选择路面类型">
            <el-option
              v-for="dict in dict.type.sys_surface_type"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            ></el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="通车时间" prop="openingTime">
          <el-date-picker clearable
                          v-model="form.openingTime"
                          type="date"
                          value-format="yyyy-MM-dd"
                          placeholder="请选择通车时间">
          </el-date-picker>
        </el-form-item>


        <el-form-item label="运营状态" prop="state">
          <el-select v-model="form.state" placeholder="请选择运营状态">
            <el-option
              v-for="dict in dict.type.sys_operation_state"
              :key="dict.value"
              :label="dict.label"
              :value="parseInt(dict.value)"
            ></el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  listRouteSegments,
  getRouteSegments,
  delRouteSegments,
  addRouteSegments,
  updateRouteSegments,
  getTotalMileage
} from "@/api/system/routeSegments";
import Treeselect from "@riophae/vue-treeselect";
import "@riophae/vue-treeselect/dist/vue-treeselect.css";
// import {deptTreeSelect} from "@/api/system/user";


import {listMaintenanceSectionAll} from "@/api/system/maintenanceSection";
import { listAllRoute, routeCacheClear } from "@/api/system/route";
import PileInput from "@/views/system/route/pileinput.vue";
import {deptTreeSelect} from "@/api/tmpl";
import {listAllHighwaySections} from "@/api/system/highwaySections";

export default {
  name: "RouteSegments",
  components: {PileInput, Treeselect},
  dicts: ['sys_route_type', 'sys_route_grade', 'sys_surface_type', 'sys_operation_state', 'sys_ramp'],
  data() {
    return {
      highwaySectionList: [],
      lanesOptions: ['2', '4', '6', '8'],
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: false,
      // 总条数
      total: 0,
      // 总养护里程
      totalMileage: 0,
      // 总统计里程
      roadSectionLength: null,
      // 养护子段管理表格数据
      routeSegmentsList: [],
      maintenanceSectionList: [],
      deptOptions: [],
      routeList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 50,
        roadSectionId: null,
        option: "管养单位",
        state: '2',
        merge: true,
        ownerUnit: null,
        lanesStr: null,
        dataRange: null,
        routeSegmentsName: null,
        maintenanceSectionId: null,
        baseRouteId: null,
        maintenanceUnit: null,
        roadSectionLength: null,
        roadGrade: null,
        mainLength: null,
        roadType: '0',
        roadSurfaceType: null,
        lanes: null,
        openingTime: null,
        pileStart: null,
        pileEnd: null,
        placeStartName: null,
        placeEndName: null,
        unifiedMileagePileStart: null,
        unifiedMileagePileEnd: null
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        routeSegmentsName: [
          {required: true, message: "子段名称不能为空", trigger: "blur"}
        ],
        maintenanceSectionId: [
          {required: true, message: "养护路段不能为空", trigger: "blur"}
        ],
        baseRouteId: [
          {required: true, message: "路线不能为空", trigger: "blur"}
        ],
        roadGrade: [
          {required: true, message: "路线等级不能为空", trigger: "change"}
        ],
        roadSurfaceType: [
          {required: true, message: "路面类型不能为空", trigger: "change"}
        ],
        maintenanceUnit: [
          {required: true, message: "管养单位不能为空", trigger: "change"}
        ],
        roadType: [
          {required: true, message: "管养单位不能为空", trigger: "change"}
        ],
        lanes: [
          {required: true, message: "车道数不能为空", trigger: "blur"},
          {type: 'number', message: '请输入数字', trigger: 'blur'}
        ],
        state: [
          {required: true, message: '运营状态不能为空', trigger: "blur"}
        ],
        mainLength: [
          {required: true, message: '养护里程不能为空', trigger: "blur"}
        ]
      }
    };
  },
  watch: {
    'form.roadType':function (value){
      if (value !== 3){
        // this.form.ramp = 0
      }
    }
  },
  created() {
    this.getList();
    this.getDeptTree()
    this.getRouteList()
    this.getRoadSection()
    this.getMaintenanceSection()
  },
  methods: {
    onPileChange(type) {
      if (type === 1)
        if (!isNaN(this.form.pileEnd) && !isNaN(this.form.pileStart)){
          this.form.roadSectionLength = (this.form.pileEnd - this.form.pileStart).toFixed(2)
          this.form.mainLength = this.form.roadSectionLength
        }

      if (type === 2)
        if (!isNaN(this.form.unifiedMileagePileEnd) && !isNaN(this.form.unifiedMileagePileStart))
          this.form.roadSectionLength = (this.form.unifiedMileagePileEnd - this.form.unifiedMileagePileStart).toFixed(2)
          this.form.mainLength = this.form.roadSectionLength
    },
    /** 查询养护子段管理列表 */
    getList() {
      this.loading = true;

      let tempQueryParams = {...this.queryParams}

      if (this.queryParams.lanesStr){
        tempQueryParams.lanesStr = this.queryParams.lanesStr.join(',')
      }

      if (this.queryParams.dataRange){

        console.log(this.queryParams.dataRange)
        tempQueryParams.startOpeningTime =  this.queryParams.dataRange[0]
        tempQueryParams.endOpeningTime =   this.queryParams.dataRange[1]

      }

      if (this.queryParams.option == "养护路段"){
        tempQueryParams.manageUnitId = tempQueryParams.maintenanceUnit
        tempQueryParams.maintenanceUnit = null
      }

      listRouteSegments(tempQueryParams).then(response => {
        this.routeSegmentsList = response.rows;
        for(let item of this.routeSegmentsList){
          let lanes = new Set()
          let roadGrade  = new Set()
          let roadSurfaceType = new Set()
          if (item.children && item.children.length > 0){
              for (let subItem of item.children){
                lanes.add(subItem.lanes)
                roadGrade.add(subItem.roadGrade)
                roadSurfaceType.add(subItem.roadSurfaceType)
              }
          }
          item.lanes = Array.from(lanes).join(",")
          item.roadGrade = Array.from(roadGrade).join(",")
          item.roadSurfaceType = Array.from(roadSurfaceType).join(",")
        }
        this.total = response.total;
        this.loading = false;
      });
      this.getTotalMileage();
    },
    /** 查询总养护里程 */
    getTotalMileage() {

      let tempQueryParams = {...this.queryParams}

      if (this.queryParams.lanesStr){
        tempQueryParams.lanesStr = this.queryParams.lanesStr.join(',')
      }


      getTotalMileage(tempQueryParams).then(response => {
        // this.totalMileage = response.data;

        this.totalMileage = response.data.mainLength;
        this.roadSectionLength = response.data.roadSectionLength;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        routeSegmentsId: null,
        routeSegmentsName: null,
        maintenanceSectionId: null,
        baseRouteId: null,
        maintenanceUnit: null,
        roadSectionLength: null,
        roadGrade: null,
        roadType: null,
        ramp: null,
        roadSurfaceType: null,
        lanes: null,
        openingTime: null,
        pileStart: null,
        pileEnd: null,
        placeStartName: null,
        placeEndName: null,
        createBy: null,
        createdTime: null,
        updatedBy: null,
        updatedTime: null,
        state: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.routeSegmentsId)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加养护子段管理";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const routeSegmentsId = row.routeSegmentsId || this.ids
      getRouteSegments(routeSegmentsId).then(response => {
        console.log(response.data)
        this.form = response.data;
        this.open = true;
        this.title = "修改养护子段管理";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.routeSegmentsId != null) {
            updateRouteSegments(this.form).then(res => {
              if (res.code === 200) {
                routeCacheClear()
                this.$modal.msgSuccess("修改成功");
                this.open = false;
                this.getList();
              }
            });
          } else {
            addRouteSegments(this.form).then(res => {
              if (res.code === 200) {
                this.$modal.msgSuccess("新增成功");
                this.open = false;
                this.getList();
              }
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const routeSegmentsIds = row.routeSegmentsId || this.ids;
      let str = row.routeSegmentsName?"名称为"+row.routeSegmentsName:"选中"
      this.$modal.confirm('是否确认删除' + str + '的数据？').then(function () {
      // this.$modal.confirm('是否确认删除养护子段管理 名称为"' + row.routeSegmentsName + '"的数据项？').then(function () {
        return delRouteSegments(routeSegmentsIds);
      }).then((res) => {
        if (res.code === 200) {
          routeCacheClear()
          this.getList();
          this.$modal.msgSuccess("删除成功");
        }
      }).catch(() => {
      });
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('system/routeSegments/export', {
        ...this.queryParams
      }, `routeSegments_${new Date().getTime()}.xlsx`)
    },
    /** 查询部门下拉树结构 */
    getDeptTree() {
      return deptTreeSelect({types:101}).then(response => {
        this.deptOptions = response.data;
      });
    },
    getMaintenanceSection() {
      listMaintenanceSectionAll().then(res => {
        this.maintenanceSectionList = res.data
      })
    },
    getRouteList() {
      listAllRoute().then(res => {
        this.routeList = res.data
      })
    },
    getRoadSection() {
      listAllHighwaySections().then(res => {
        let data = res.data
        this.highwaySectionList = data
      })
    }
  }
};
</script>
<style scoped>
.app-container form:first-child .el-select,
.app-container form:nth-child(2) .el-select,
.app-container form:nth-child(2) ::v-deep .el-form-item__content,
.app-container form:first-child ::v-deep .el-form-item__content {
  width: 240px;
}
.app-container form:nth-child(1) ::v-deep .vue-treeselect__control,
.app-container form:nth-child(2) ::v-deep .vue-treeselect__control{
  height: auto;
  line-height: 30px;
}
.app-container  form:first-child  .el-form-item:last-child ::v-deep .el-form-item__content {
  width: auto;
}
.el-dialog .el-input,
.el-dialog .pile-input,
.el-dialog .el-select,
.el-dialog .el-date-editor,
.el-dialog .el-textarea,
.el-dialog .vue-treeselect {
  width: 350px;
}
.tableDiv {
  background-color: white;
  padding-bottom: 10px;
}
::v-deep .vue-treeselect__label{
  font-weight: 500!important;
  color: #606266!important;
}
</style>
