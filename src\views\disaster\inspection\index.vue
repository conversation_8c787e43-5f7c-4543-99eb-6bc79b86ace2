<template>
  <div class="app-container">
    <div class="searchBox" :style="{ 'height': queryShow ? '86px' : '48px' }">
      <el-row :gutter="20">
        <el-col :span="9">
          <CascadeSelection style="min-width: 192px;" :form-data="queryParams" v-model="queryParams"
            @update:fromData="() => { }" types="201" multiple />
        </el-col>
        <el-col :span="3">
          <el-select v-model="queryParams.riskGrade" placeholder="请选择风险等级" clearable>
            <el-option label="一级（重大）" value="一级（重大）"></el-option>
            <el-option label="二级（较大）" value="二级（较大）"></el-option>
            <el-option label="三级（一般）" value="三级（一般）"></el-option>
            <el-option label="四级（低）" value="四级（低）"></el-option>
          </el-select>
        </el-col>
        <el-col :span="3">
          <el-select v-model="queryParams.stage" placeholder="请选择状态" clearable>
            <el-option v-for="dict in states" :key="dict.label" :label="dict.label" :value="dict.value" />
          </el-select>
        </el-col>
        <el-col :span="9">
          <el-button type="primary" icon="el-icon-search" @click="queryhandle">搜索</el-button>
          <el-button icon="el-icon-refresh" @click="queryReset">重置</el-button>
          <el-button :icon="queryShow ? 'el-icon-arrow-up' : 'el-icon-arrow-down'"
            style="color: #1890ff;border-color: #badeff;background-color: #e8f4ff;" circle
            @click="queryShow = !queryShow" />
        </el-col>
      </el-row>
      <transition name="search">
        <div class="searchMoreBox" v-if="queryShow">
          <el-row :gutter="20">
            <el-col :span="3">
              <el-select v-model="queryParams.isAbnormal" placeholder="请选择是否异常" clearable>
                <el-option v-for="dict in abnormal" :key="dict.label" :label="dict.label" :value="dict.value" />
              </el-select>
            </el-col>
            <el-col :span="3">
              <!-- <el-cascader v-model="queryParams.userNameLike" :options="deptUserOptions" :props="props" ref="deptUser"
                filterable clearable size="mini" placeholder="请选择或搜索巡查人员"></el-cascader> -->
              <el-input v-model="queryParams.userNameLike" clearable placeholder="请输入巡查人员"></el-input>
            </el-col>
            <el-col :span="6">
              <el-date-picker v-model="queryTime"
                @change="() => { queryParams.startTime = queryTime[0]; queryParams.endTime = queryTime[1] }"
                style="width: 100%;" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期"
                type="datetimerange" format="yyyy-MM-dd HH:mm:ss" value-format="yyyy-MM-dd HH:mm:ss">
              </el-date-picker>
            </el-col>
          </el-row>
        </div>
      </transition>
    </div>
    <div class="tableDiv" :style="{ 'height': queryShow ? 'calc(100% - 96px)' : 'calc(100% - 58px)' }">
      <div class="btnBox" v-if="isHide">
        <el-row :gutter="10">
          <el-col :span="1.5">
            <el-button type="primary" icon="el-icon-plus" size="mini" v-hasPermi="['disaster:inception:add']"
              @click="formAdd">新增</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="success" icon="el-icon-edit" size="mini" :disabled="forEditDelDisabled"
              v-hasPermi="['disaster:inception:edit']" @click="() => { formEdit('select'); editType = 'click' }">修改
            </el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="danger" icon="el-icon-delete" size="mini" :disabled="forDeleteDelDisabled"
              v-hasPermi="['disaster:inception:remove']" @click="formDelete">删除
            </el-button>
          </el-col>
          <el-col :span="1.5" v-hasPermi="['disaster:inception:export']">
            <el-button type="warning" icon="el-icon-download" size="mini" @click="formExport">
              导出
            </el-button>
          </el-col>
        </el-row>
      </div>
      <!-- 数据表格 -->
      <el-table v-adjust-table height="calc(100% - 98px)" border ref="tableRef" v-loading="tableLoading"
        :data="tableData" @selection-change="tableSelectionChange" @row-click="tableRowClick">
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="序号" fixed align="center" type="index" width="50"></el-table-column>
        <el-table-column label="灾害类型" :show-overflow-tooltip="true" width="100" align="center" prop="disasterType" />
        <el-table-column label="是否异常" :show-overflow-tooltip="true" width="80" align="center" prop="isAbnormal">
          <template slot-scope="scope">
            {{ scope.row.isAbnormal ? '异常' : '正常' }}
          </template>
        </el-table-column>
        <!-- <el-table-column label="删除标志" :show-overflow-tooltip="true" width="80" align="center" prop="delFlag">
          <template slot-scope="scope">
            {{ scope.row.delFlag ? '存在' : '删除' }}
          </template>
        </el-table-column> -->
        <el-table-column label="风险得分" :show-overflow-tooltip="true" width="80" align="center" prop="riskScore" />
        <el-table-column label="风险等级" :show-overflow-tooltip="true" width="120" align="center" prop="riskGrade" />
        <el-table-column label="纬度" :show-overflow-tooltip="true" width="100" align="center" prop="latitude" />
        <el-table-column label="经度" :show-overflow-tooltip="true" width="100" align="center" prop="longitude" />
        <el-table-column label="巡查频率(天/次)" :show-overflow-tooltip="true" width="120" align="center" prop="frequency" />
        <el-table-column label="阶段" :show-overflow-tooltip="true" width="100" align="center" prop="stage">
          <template slot-scope="scope">
            {{ computedStage(scope.row) }}
          </template>
        </el-table-column>
        <!-- <el-table-column label="到期时间" :show-overflow-tooltip="true" width="180" align="center" prop="expiry" /> -->
        <el-table-column label="管理处名称" :show-overflow-tooltip="true" width="180" align="center"
          prop="managementOfficeName" />
        <el-table-column label="产权单位名称" :show-overflow-tooltip="true" width="180" align="center"
          prop="propertyUnitName" />
        <el-table-column label="产权单位类型" :show-overflow-tooltip="true" width="180" align="center"
          prop="propertyUnitType" />
        <el-table-column label="管养单位名称" :show-overflow-tooltip="true" width="180" align="center"
          prop="maintenanceUnitName" />
        <el-table-column label="巡查人员" :show-overflow-tooltip="true" width="180" align="center" prop="inspectionUsers">
          <template slot-scope="scope">
            {{ computedInspectionUsers(scope.row) }}
          </template>
        </el-table-column>
        <el-table-column label="备注" :show-overflow-tooltip="true" width="180" align="center" prop="remark" />
        <el-table-column label="操作" align="center" width="200" class-name="small-padding fixed-width" fixed="right">
          <template slot-scope="scope">
            <template v-if="isHide">
              <el-button v-hasPermi="['disaster:inception:edit']" size="mini" type="text" icon="el-icon-edit"
                @click="() => { formEdit('click', scope.row); clickRow = scope.row; editType = 'click' }">修改
              </el-button>
              <el-button v-hasPermi="['disaster:inception:remove']" size="mini" type="text" icon="el-icon-delete"
                @click="formDelete(scope.row)">删除
              </el-button>
            </template>
            <el-button size="mini" type="text" icon="el-icon-view" @click="formView(scope.row)">查看
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      <pagination :total="queryTotal" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize"
        @pagination="queryList" style="margin-right: 10px;" />
    </div>
    <!-- 新增编辑风险点 -->
    <el-dialog :visible.sync="formDialog" width="70%" max-height="50%" :close-on-press-escape="false"
      :close-on-click-modal="false" append-to-body class="formDialog" @close="closeRiskPointsInspection"
      v-loading="formLoading">
      <template slot="title">
        {{ formTitle }}
      </template>
      <div class="dialog_scroll">
        <el-form ref="formRef" :model="formParams" :rules="formRules" :disabled="this.formType === 'view'"
          label-width="140px" label-position="right">
          <template v-if="isAdd">
            <el-row :gutter="12">
              <el-col :span="24">
                <div style="margin-bottom: 20px;">
                  <template v-if="!formParams.riskId">
                    <el-button @click="openRiskPointsDialog" type="primary" size="mini">
                      选择风险点
                    </el-button>
                  </template>
                  <template v-else>
                    <p style="color: #1890ff;">{{ formParams.riskId }}
                      <el-button type="danger" icon="el-icon-delete" circle @click="clearRiskId"></el-button>
                    </p>
                  </template>
                </div>
              </el-col>
            </el-row>
          </template>
          <div class="infoBox" style="margin-bottom: 20px;">
            <div class="infoTitle">
              基础信息
            </div>
            <el-row :gutter="12">
              <el-col :span="12">
                <el-form-item label="风险点ID" prop="riskId">
                  <el-input v-model="formParams.riskId" disabled></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="巡查状态" prop="stage">
                  <!-- <el-select v-model="formParams.stage" placeholder="请选择状态" clearable style="width: 100%;">
                    <el-option label="未巡查" :value="0" />
                    <el-option label="已巡查" :value="1" />
                  </el-select> -->
                  <el-radio-group v-model="formParams.stage" @input="stageChange" :disabled="!isAdd">
                    <el-radio :label="0">未巡查</el-radio>
                    <el-radio :label="1">已巡查</el-radio>
                  </el-radio-group>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="12" v-show="formParams.stage === 1">
              <el-col :span="12">
                <el-form-item label="是否异常" prop="isAbnormal">
                  <el-select v-model="formParams.isAbnormal" placeholder="请选择是否异常" clearable style="width: 100%;">
                    <el-option v-for="dict in abnormal" :key="dict.label" :label="dict.label" :value="dict.value" />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="巡查时间" prop="inspectionTime">
                  <el-date-picker v-model="formParams.inspectionTime" type="datetime" value-format="yyyy-MM-dd HH:mm:ss"
                    format="yyyy-MM-dd HH:mm:ss" placeholder="选择日期时间" style="width: 100%;">
                  </el-date-picker>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="12" v-show="formParams.stage === 1">
              <el-col :span="24">
                <el-form-item label="备注" prop="remark">
                  <el-input type="textarea" :autosize="{ minRows: 2, maxRows: 10 }" placeholder="请输入内容"
                    v-model="formParams.remark">
                  </el-input>
                </el-form-item>
              </el-col>
            </el-row>
          </div>
          <div v-show="formParams.stage === 1">
            <div class="infoBox" style="margin-bottom: 20px;" v-if="!isAdd">
              <div class="infoTitle">
                照片（风险点照片）
              </div>
              <el-row :gutter="12">
                <el-col :span="24">
                  <el-form-item label-width="0" prop="riskDescription">
                    <div class="imgBox" v-if="formImgSrcList.length > 0">
                      <el-timeline>
                        <el-timeline-item v-for="item in formDataLine" :key="item.time" :timestamp="item.time"
                          placement="top" color="#5cbb7a">
                          <el-card class="imgBoxCard">
                            <div class="cardMain" v-for="itemC in item.data">
                              <el-button class="imgDeleteBtn" type="danger" icon="el-icon-delete" circle
                                @click="formDeleteImg(itemC.id)"></el-button>
                              <div class="imgTitle">
                                <el-tooltip class="item" effect="dark" :content="itemC.name" placement="top">
                                  <i class="el-icon-info"></i>
                                </el-tooltip>
                                {{ itemC.name }}
                              </div>
                              <el-image fit="cover" class="img" :src="itemC.url" @click="formImgPreview(itemC.imgUrl)"
                                :preview-src-list="formImgUrlList"></el-image>
                              <div class="footer">
                                {{ `由 ${itemC.createBy} 上传于 ${itemC.createTime}` }}
                              </div>
                              <div class="footer">
                                {{ itemC.remark ? `图片描述：${itemC.remark}` : '' }}
                              </div>
                            </div>
                          </el-card>
                        </el-timeline-item>
                      </el-timeline>
                    </div>
                    <div class="noneBox" v-else>
                      暂无内容
                    </div>
                  </el-form-item>
                </el-col>
              </el-row>
            </div>
            <div class="infoBox" style="padding: 15px;" v-if="this.formType !== 'view'">
              <div class="infoTitle">
                <!-- <el-tooltip content="上传填报高边坡的全貌照片及局部照片（防护设施、排水设施），不少于 3 张" placement="top">
              <i class="el-icon-question"></i>
            </el-tooltip> -->
                上传照片
              </div>
              <ImageUpload v-if="formDialog" v-model="formUploadList" :ownerId="formImgOwnerId" :needRemark="true"
                :can-sort="true" storage-path="/disaster/inspection/" platform="fykj" @input="queryImg(formImgOwnerId)"
                ref="refImageUpload" />
            </div>
          </div>
        </el-form>
      </div>
      <div class="dialog-footer">
        <el-button type="primary" @click="addRiskPointsInspection">
          确 定
        </el-button>
        <el-button @click="closeRiskPointsInspection">取 消</el-button>
      </div>
    </el-dialog>
    <!-- 选择风险点 -->
    <SelectRiskPoints :openDialog.sync="openRiskPoints" @selectRow="onRiskPointsSelect" ref="refRiskPoints"
      v-bind="$attrs" v-on="$listeners"></SelectRiskPoints>
    <!-- 查看风险点详情 -->
    <el-dialog :visible.sync="detailsDialog" width="70%" :close-on-press-escape="false" :close-on-click-modal="false"
      append-to-body @click="closeDetailsDialog">
      <template slot="title">
        风险点详情
      </template>
      <div class="dialog_scroll">
        <el-form ref="formRef" :model="detailsParams" disabled label-width="140px" label-position="right">
          <div class="infoBox" style="margin-bottom: 20px;">
            <div class="infoTitle">
              基础信息
            </div>
            <el-row :gutter="12">
              <el-col :span="12">
                <el-form-item label="风险点ID" prop="riskId">
                  <el-input v-model="detailsParams.riskId"></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="阶段" prop="stage">
                  <el-select v-model="detailsParams.stage" style="width: 100%;">
                    <el-option label="未巡查" :value="0" />
                    <el-option label="待上报" :value="1" />
                    <el-option label="待审核" :value="2" />
                    <el-option label="退回" :value="3" />
                    <el-option label="审核通过" :value="4" />
                    <el-option label="审核未通过" :value="5" />
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="12">
              <el-col :span="12">
                <el-form-item label="灾害类型" prop="disasterType">
                  <el-input v-model="detailsParams.disasterType"></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="是否异常" prop="isAbnormal">
                  <el-select v-model="detailsParams.isAbnormal" placeholder="请选择是否异常" clearable style="width: 100%;">
                    <el-option v-for="dict in abnormal" :key="dict.label" :label="dict.label" :value="dict.value" />
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="12">
              <el-col :span="12">
                <el-form-item label="风险得分" prop="riskScore">
                  <el-input v-model="detailsParams.riskScore"></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="风险等级" prop="riskGrade">
                  <el-input v-model="detailsParams.riskGrade"></el-input>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="12">
              <el-col :span="12">
                <el-form-item label="纬度" prop="latitude">
                  <el-input v-model="detailsParams.latitude"></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="经度" prop="longitude">
                  <el-input v-model="detailsParams.longitude"></el-input>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="12">
              <el-col :span="12">
                <el-form-item label="巡查频率(天/次)" prop="frequency">
                  <el-input v-model="detailsParams.frequency"></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="巡查时间" prop="inspectionTime">
                  <el-date-picker v-model="detailsParams.inspectionTime" type="datetime"
                    value-format="yyyy-MM-dd HH:mm:ss" format="yyyy-MM-dd HH:mm:ss" placeholder="选择日期时间"
                    style="width: 100%;">
                  </el-date-picker>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="12">
              <el-col :span="12">
                <el-form-item label="到期时间" prop="expiry">
                  <el-input v-model="detailsParams.expiry"></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="管理处名称" prop="managementOfficeName">
                  <el-input v-model="detailsParams.managementOfficeName"></el-input>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="12">
              <el-col :span="12">
                <el-form-item label="产权单位名称" prop="propertyUnitName">
                  <el-input v-model="detailsParams.propertyUnitName"></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="产权单位类型" prop="propertyUnitType">
                  <el-input v-model="detailsParams.propertyUnitType"></el-input>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="12">
              <el-col :span="12">
                <el-form-item label="管养单位名称" prop="maintenanceUnitName">
                  <el-input v-model="detailsParams.maintenanceUnitName"></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="巡查人员" prop="inspectionUsers">
                  <el-input v-model="detailsParams.inspectionUsers">
                  </el-input>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="12">
              <el-col :span="24">
                <el-form-item label="备注" prop="remark">
                  <el-input type="textarea" :autosize="{ minRows: 2, maxRows: 10 }" placeholder="请输入内容"
                    v-model="detailsParams.remark">
                  </el-input>
                </el-form-item>
              </el-col>
            </el-row>
          </div>
          <div class="infoBox" style="margin-bottom: 20px;">
            <div class="infoTitle">
              照片（风险点照片）
            </div>
            <el-row :gutter="12">
              <el-col :span="24">
                <el-form-item label-width="0" prop="riskDescription">
                  <div class="imgBox" v-if="formImgSrcList.length > 0">
                    <el-timeline>
                      <el-timeline-item v-for="item in formDataLine" :key="item.time" :timestamp="item.time"
                        placement="top" color="#5cbb7a">
                        <el-card class="imgBoxCard">
                          <div class="cardMain" v-for="itemC in item.data">
                            <!-- <el-button class="imgDeleteBtn" type="danger" icon="el-icon-delete" circle
                            @click="formDeleteImg(itemC.id)"></el-button> -->
                            <div class="imgTitle">
                              <el-tooltip class="item" effect="dark" :content="itemC.name" placement="top">
                                <i class="el-icon-info"></i>
                              </el-tooltip>
                              {{ itemC.name }}
                            </div>
                            <el-image fit="cover" class="img" :src="itemC.url" @click="formImgPreview(itemC.imgUrl)"
                              :preview-src-list="formImgUrlList"></el-image>
                            <div class="footer">
                              {{ `由 ${itemC.createBy} 上传于 ${itemC.createTime}` }}
                            </div>
                            <div class="footer">
                              {{ itemC.remark ? `图片描述：${itemC.remark}` : '' }}
                            </div>
                          </div>
                        </el-card>
                      </el-timeline-item>
                    </el-timeline>
                  </div>
                  <div class="noneBox" v-else>
                    暂无内容
                  </div>
                </el-form-item>
              </el-col>
            </el-row>
          </div>
        </el-form>
      </div>
      <div class="dialog-footer">
        <el-button @click="closeDetailsDialog">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import inspection from '@/api/disaster/risk/inspection'
import CascadeSelection from '@/components/CascadeSelectionManagementOffice/index.vue' // 管养处/路段/路线
import SelectRiskPoints from '@/views/disaster/inspection/components/selectRiskPoints.vue'
import { removeFile } from '@/api/system/fileUpload.js'
import ImageUpload from '@/views/disaster/ImageUpload.vue' // 图片上传组件
import { getTreeStruct } from '@/api/tmpl'
import { findFiles } from '@/api/file/index.js'
import { isAdd } from '@/api/maintenanceProject/projectManage'
export default {
  name: 'inspection',
  components: {
    CascadeSelection,
    SelectRiskPoints,
    ImageUpload,
  },
  data() {
    return {
      queryParams: {
        pageNum: 1, // 页码
        pageSize: 10, // 每页条数
        managementMaintenanceId: null, //  管理处ID
        maintenanceSectionId: null, // 养护路段id
        routeCodes: null, // 路线编码
        riskGrade: null, // 风险等级
        stage: null, // 状态
        isAbnormal: null, // 是否异常
        startTime: null, // 巡查开始时间
        endTime: null, // 巡查结束时间
        userNameLike: null,// 巡查人员名字模糊查询
        riskId: null, // 风险点id
      },
      queryTime: [], // 查询时间
      queryShow: false, // 隐藏筛选显隐
      tableLoading: false,
      tableData: [],
      tableSelection: [], // 表格选中数据
      queryTotal: 0, // 总条数
      ids: [],
      formTitle: '风险点巡查信息调查表', // 表单dialog标题
      states: Object.freeze([
        {
          value: 0,
          label: '未巡查',
        },
        {
          value: 1,
          label: '待上报',
        },
        {
          value: 2,
          label: '待审核',
        },
        {
          value: 3,
          label: '退回',
        },
        {
          value: 4,
          label: '审核通过',
        },
        {
          value: 5,
          label: '审核未通过',
        },

      ]),
      abnormal: Object.freeze([
        {
          value: true,
          label: '异常',
        },
        {
          value: false,
          label: '正常',
        },
      ]),
      openRiskPoints: false,
      formLoading: false, // 表单加载
      formDialog: false,
      formType: 'add', // 表单类型
      formRules: { // 表单校验规则
        maintenanceUnitId: [
          { required: true, message: "管养单位不能为空", trigger: "blur" }
        ],
        roadSectionId: [
          { required: true, message: "路段信息不能为空", trigger: "blur" }
        ],
        routerNum: [
          { required: true, message: "路段信息不能为空", trigger: "blur" }
        ],
        technicalGrade: [
          { required: true, message: "技术等级不能为空", trigger: "blur" }
        ],
        routerName: [
          { required: true, message: "路线名称不能为空", trigger: "blur" }
        ],
        areaCode: [
          { required: true, message: "区划代码不能为空", trigger: "blur" }
        ],
        relativePosition: [
          { required: true, message: "相对位置不能为空", trigger: "blur" }
        ],
        surfaceDrainageFacilities: [
          { required: true, message: "地表排水设施不能为空", trigger: "blur" }
        ],
        undergroundDrainageFacilities: [
          { required: true, message: "地下排水设施不能为空", trigger: "blur" }
        ],
        curDamagedSlopeSurface: [
          { required: true, message: "坡面防护不能为空", trigger: "blur" }
        ],
        curDamagedRiverBank: [
          { required: true, message: "沿河防护不能为空", trigger: "blur" }
        ],
        curDamagedSupportFacility: [
          { required: true, message: "支挡设施不能为空", trigger: "blur" }
        ],
      },
      formParams: {
        stage: 0,
        riskId: null,
        photos: null,
        isAbnormal: null,
        inspectionTime: null,
        remark: null,
      },
      // 图片上传相关
      formImgSrcList: [], // 图片渲染列表
      formImgUrlList: [], // 图片预览列表
      formUploadList: '', // 图片上传列表
      formImgOwnerId: '', // 图片上传ownerId
      formImgNum: [], // 图片数量（图片集用）
      RiskPointsForm: {},
      // 部门-用户树选项
      deptUserOptions: [],
      props: {
        multiple: true, //是否多选
        value: 'id',
      },
      // 风险点查询返回对象
      RiskPointsFormQuery: {},
      /* 风险点详情 */
      detailsDialog: false,
      detailsParams: {},
      formDataLine: [],
      isAdd: true,
      isHide: true,
      clickRow: null,
      editType: '',
    }
  },
  created() {
    // this.initPage();
    // this.getDeptTreeDef();
  },
  watch: {
    '$route': {
      async handler(newValue, oldValue) {
        let { params: { id } } = newValue
        if (id) {
          this.isHide = false
          this.queryParams.riskId = id
        } else {
          this.isHide = true
        }
        await this.queryList()
      },
      deep: true,
      immediate: true,
    }
  },
  computed: {
    // 计算阶段编号对应名称
    computedStage() {
      return (row) => {
        let name = ''
        switch (row.stage) {
          case 0:
            name = '未巡查';
            break;
          case 1:
            name = '待上报';
            break;
          case 2:
            name = '待审核';
            break;
          case 3:
            name = '退回';
            break;
          case 4:
            name = '审核通过';
            break;
          case 5:
            name = '核未通过';
            break;
        }
        return name
      }
    },
    // 计算修改按钮disable
    forEditDelDisabled() {
      if (this.tableSelection.length !== 1) {
        return true;
      } else {
        return false;
      }
    },
    // 计算批量删除按钮disable
    forDeleteDelDisabled() {
      if (this.tableSelection.length > 0) {
        return false
      } else {
        return true
      }
    },
    // 计算巡查人员姓名
    computedInspectionUsers() {
      return this.inspectionUsers
    },
  },
  methods: {
    initPage() {
      // 获取数据
      this.queryList()
    },
    queryhandle() {
      this.queryList()
    },
    queryReset() {
      this.queryParams = { // 查询参数
        pageNum: 1, // 页码
        pageSize: 10, // 每页条数
        managementMaintenanceId: null, //  管理处ID
        maintenanceSectionId: null, // 养护路段id
        routeCodes: null, // 路线编码
        riskGrade: null, // 风险等级
        stage: null, // 状态
        isAbnormal: null, // 是否异常
        startTime: null, // 巡查开始时间
        endTime: null, // 巡查结束时间
        userNameLike: null,// 巡查人员名字模糊查询
        riskId: null, // 风险点id
      }
      this.queryTime = []
      this.queryList();
    },
    /** 删除按钮操作 */
    formDelete(row) {
      const id = row.id ? [row.id] : this.ids;
      this.$modal.confirm('确认删除？').then(function () {
        return inspection.inspectionBatchRemove(id);
      }).then(() => {
        this.queryList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {
      });
    },
    async formEdit(type, row) {
      this.formDialog = true
      this.isAdd = false
      this.formTitle = '修改风险点巡查'
      if (type === 'click') {
        this.formParams = row
        /*  Object.keys(row).forEach((key) => {
           this.$set(this.formParams, key, row[key]);
         }) */
      } else if (type === 'select') {
        this.formParams = this.tableSelection[0]
        /* Object.keys(this.tableSelection[0]).forEach((key) => {
          this.$set(this.formParams, key, this.tableSelection[0][key]);
        }) */
      }
      /* 获取图片回显 */
      if (this.formParams.photos) {
        this.formImgOwnerId = this.formParams.photos
        await this.queryImg(this.formImgOwnerId)
      } else {
        this.formImgOwnerId = new Date().getTime().toString()
        this.formParams.photos = this.formImgOwnerId
      }
      this.formUploadList = this.formParams.photos
    },
    formAdd() {
      this.formDialog = true
      this.isAdd = true
      this.formTitle = '添加风险点巡查'
      this.formImgOwnerId = new Date().getTime().toString()
    },
    async formView(row) {
      this.tableLoading = true
      await inspection.inspectionQuery(row.id).then(async (res) => {
        if (res.code === 200) {
          this.detailsDialog = true
          this.detailsParams = res.data
          this.detailsParams.inspectionUsers = this.inspectionUsers(this.detailsParams)
          this.tableLoading = false
          if (this.detailsParams.photos) {
            await this.queryImg(this.detailsParams.photos)
          }
        }
      }).catch((err) => {
        this.tableLoading = false
        this.$message.error(err)
      })
    },
    inspectionUsers(row) {
      let name = ''
      if (row.inspectionUsers.length > 0) {
        let nameArr = row.inspectionUsers.map(item => {
          return item.userName
        });
        name = nameArr.join('、')
      }
      return name
    },
    // 获取图片数据
    async queryImg(id) {
      await findFiles({ ownerId: id }).then(res => {
        if (res.code === 200) {
          this.formImgSrcList = res.data.map(item => ({
            id: item.ownerId + '-' + item.id,
            name: item.originalFilename,
            url: item.thumbUrl,
            imgUrl: item.url,
            remark: item.remark,
            createTime: item.createTime,
            createBy: item.createBy
          }))
          this.formImgUrlList = res.data.map(item => item.url)
          this.formDataLine = this.formImgDateLine()
        }
      }).catch(() => {
        this.$message.error('图片查询失败，请重新打开表单尝试')
      })
    },
    formImgDateLine() {
      let dateLine = []
      if (this.formImgSrcList.length > 0) {
        this.formImgSrcList.forEach(item => {
          let date = item.createTime.split('T')[0]
          if (dateLine.length === 0) {
            dateLine.push({
              time: date,
              data: [item]
            })
          } else {
            let index = dateLine.findIndex(item2 => item2.time === date)
            if (index !== -1) {
              dateLine[index].data.push(item)
            } else {
              dateLine.push({
                time: date,
                data: [item]
              })
            }
          }
        })
        // 时间线排序
        dateLine.sort((a, b) => {
          // 将日期字符串分割并转换为日期对象
          let dateA = new Date(a.time);
          let dateB = new Date(b.time);
          // 比较日期
          return dateA - dateB;
        })
      }
      return dateLine
    },
    // 勾选表格项改变时
    tableSelectionChange(val) {
      this.tableSelection = val
      this.ids = this.tableSelection.map(row => row.id);
    },
    // 导出表单
    formExport() {
      // 深拷贝 queryParams
      let vo = { ...this.queryParams };
      delete vo.pageNum
      delete vo.pageSize
      // 检查是否有选中的数据行
      const isAllData = this.tableSelection.length === 0;
      // 根据是否选择数据行来设置提示消息和导出参数
      const message = isAllData
        ? '即将根据查询条件导出所有风险点巡查数据，此过程可能花费时间较长，是否继续？'
        : `已选择${this.tableSelection.length}条风险点巡查数据，确认导出数据？`;
      if (!isAllData) {
        // 仅导出选中项的情况，将选中项的 id 加入参数
        vo.ids = this.ids
      }
      // 显示确认提示，并在确认后调用下载方法
      this.$modal
        .confirm(message)
        .then(() => {
          const filename = `风险点巡查信息_${new Date().getTime()}.xlsx`;
          this.download(
            '/disaster/inspection/export',
            vo,
            filename,
            {
              headers: { "Content-Type": "application/json;" },
              parameterType: "body",
            }
          );
        })
    },
    tableRowClick() { },
    // 获取主表数据
    async queryList() {
      this.tableLoading = true
      await inspection.queryPageInspection(this.queryParams).then((res) => {
        if (res.code === 200) {
          this.tableData = res.rows
          this.queryTotal = res.total
        }
        this.tableLoading = false
      }).catch((err) => {
        this.tableLoading = false
        this.$message.error(err)
      })
    },

    // 删除图片
    formDeleteImg(id) {
      this.$modal.confirm('是否确认删除该图片？').then(async () => {
        this.$modal.loading('正在删除图片，请稍候...')
        removeFile(id.split('-')[1]).then(async (res) => {
          if (res.code === 200) {
            this.$message.success('删除图片成功！')
            /* // 移除预览列表图片
            let index = this.formImgSrcList.findIndex(item => item.id === id)
            if (index !== -1) {
              let imgUrl = this.formImgSrcList[index].imgUrl
              let imgIndex = this.formImgUrlList.indexOf(imgUrl)
              if (imgIndex !== -1) {
                this.formImgUrlList.splice(imgIndex, 1)
              }
            }
            // 移除渲染列表图片
            this.formImgSrcList = this.formImgSrcList.filter(item => item.id !== id) */
            await this.queryImg(id.split('-')[0])
            this.$refs.refImageUpload.getImg(this.formUploadList)
          }
          this.$modal.closeLoading()
        }).catch(() => {
          this.$message.error('删除图片失败')
          this.$modal.closeLoading()
        })
      })
    },
    // 点击预览图片时
    formImgPreview(url) {
      let index = this.formImgUrlList.findIndex(item => item === url)
      if (index !== -1) {
        let moveUrl = this.formImgUrlList.splice(index, this.formImgUrlList.length - index)
        this.formImgUrlList.unshift(...moveUrl)
      }
    },
    // 打开风险点选择弹框
    openRiskPointsDialog() {
      this.openRiskPoints = true
      if (Object.keys(this.RiskPointsForm).length > 0) {
        this.$refs.refRiskPoints.toggleRowSelection(this.RiskPointsForm)
      }
    },
    async onRiskPointsSelect(row, data) {
      this.RiskPointsForm = row // 获取风险点
      this.formParams.riskId = row.id
      this.RiskPointsFormQuery = data
    },
    restImg() {
      // 重置图片
      this.formDataLine = []
      this.formImgSrcList = [] // 图片缩略图列表
      this.formImgUrlList = [] // 图片预览图列表
      this.formUploadList = '' // 图片上传列表
    },
    // 关闭新增窗口
    closeRiskPointsInspection() {
      this.formDialog = false
      this.RiskPointsForm = {}
      this.RiskPointsFormQuery = {}
      this.formParams = {
        stage: 0,
        riskId: null,
        photos: null,
        isAbnormal: null,
        inspectionTime: null,
        remark: null,
      }
      this.restImg()
    },
    // 新增风险点巡查
    async addRiskPointsInspection() {
      if (this.isAdd) { // 新增
        if (Object.keys(this.RiskPointsFormQuery).length) {
          this.formLoading = true
          this.RiskPointsFormQuery.photos = this.formImgOwnerId
          Object.assign(this.RiskPointsFormQuery, this.formParams)
          delete this.RiskPointsFormQuery.id
          await inspection.inspectionAdd(this.RiskPointsFormQuery).then((res) => {
            if (res.code === 200) {
              this.$message.success(res.msg)
              this.closeRiskPointsInspection()
              this.queryList();
            }
            this.formLoading = false
          }).catch((err) => {
            this.formLoading = false
            this.$message.error(err)
          })
        } else {
          this.$message.warning('请选择风险点！')
        }
      } else { // 编辑
        this.formLoading = true
        await inspection.inspectionEdit(this.formParams).then((res) => {
          if (res.code === 200) {
            this.$message.success(res.msg)
            this.closeRiskPointsInspection()
            this.queryList();
          }
          this.formLoading = false
        }).catch((err) => {
          this.formLoading = false
          this.$message.error(err)
        })
      }
    },
    /** 查询部门-用户下拉树结构 */
    getDeptTreeDef() {
      getTreeStruct({ types: 111 }).then((response) => {
        this.deptUserOptions = response.data
      })
    },
    /* 关闭详情弹框 */
    closeDetailsDialog() {
      this.detailsDialog = false
      this.detailsParams = {}
      this.restImg()
    },
    /* 清除已选风险点ID */
    clearRiskId() {
      this.formParams.riskId = null
      this.RiskPointsForm = {}
      this.RiskPointsFormQuery = {}
    },
    async stageChange(val) {
      // 未巡查
      if (val === 0) {
        this.$set(this.formParams, 'isAbnormal', "");
        this.$set(this.formParams, 'inspectionTime', "");
        this.$set(this.formParams, 'remark', "");
        this.formImgOwnerId = ''
        this.restImg()
      } else {// 已巡查
        if (this.isAdd) {
          this.formImgOwnerId = new Date().getTime().toString()
        } else {
          await this.formEdit(this.editType, this.clickRow)
        }
      }
    },
  },
}
</script>

<style lang="scss" scoped>
::v-deep .el-input--mini .el-input__inner {
  height: 28px !important;
}

.app-container {
  padding: 10px;
  background-color: #c0c0c0;
  box-sizing: border-box;

  .searchBox {
    padding: 10px;
    background: #fff;
    border-radius: 10px;
    transition: all .1s linear;
    display: flex;
    flex-direction: column;
  }

  .searchMoreBox {
    margin-top: 10px;
  }

  .tableDiv {
    margin-top: 10px;
    background-color: white;
    padding-bottom: 10px;
    border-radius: 10px;
    transition: all .1s linear;
    display: flex;
    flex-direction: column;

    .btnBox {
      padding: 10px;
    }
  }
}

.asset-select-dialog {
  display: flex;
  justify-content: center;

  ::v-deep .el-dialog {
    margin: 0 auto !important;
    position: relative;
    top: 50%;
    transform: translateY(-50%);
    height: 90vh;
  }

  ::v-deep .el-dialog__body {
    padding: 0;
    height: calc(90vh - 120px);
  }
}

.dialog-footer {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 10px 0;
  background: #fff;
  border-top: 1px solid #e4e7ed;
}

.infoBox {
  padding: 15px 15px 0 15px;
  box-sizing: border-box;
  border-radius: 6px;
  border: 1px solid #C4C4C4;
  position: relative;

  .infoTitle {
    user-select: none;
    position: absolute;
    top: 0;
    left: 0;
    padding: 0 10px;
    font-size: 14px;
    line-height: 14px;
    font-weight: bold;
    transform: translateX(15px) translateY(-50%);
    background-color: white;
  }

  .imgBox {
    height: auto;
    width: 100%;
    // display: flex;
    // flex-wrap: wrap;
    // align-content: flex-start;

    // .imgItemBox {
    //   height: 240px;
    //   width: calc(100% / 3);
    //   box-sizing: border-box;
    //   padding: 10px;
    //   overflow-y: auto;
    //   display: flex;
    //   align-items: center;
    //   justify-content: center;
    //   position: relative;

    //   .imgDeleteBtn {
    //     position: absolute;
    //     z-index: 1;
    //     top: 10%;
    //     right: 10%;
    //   }
    // }

    ::v-deep .el-card__body {
      width: 100%;
      display: flex;
      flex-direction: row;
      flex-wrap: wrap;
      align-content: flex-start;
    }

    .imgBoxCard {
      width: 100%;

      .cardMain {
        height: 260px;
        width: 33%;
        box-sizing: border-box;
        padding: 0 10px;
        display: flex;
        flex-direction: column;
        position: relative;

        .imgDeleteBtn {
          position: absolute;
          z-index: 1;
          top: 20%;
          right: 5%;
        }

        .imgTitle {
          height: 28px;
          width: 100%;
          font-size: 16px;
          font-weight: bold;
          white-space: nowrap;
          text-overflow: ellipsis;
          overflow: hidden;
        }

        .img {
          height: calc(100% - (28px + 28px));
          width: 100%;
          padding: 10px 0;
          position: relative;
          z-index: 0;
        }

        .footer {
          height: 28px;
          color: #888888;
          font-size: 14px;
        }
      }
    }
  }

  .noneBox {
    user-select: none;
    height: 200px;
    width: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    color: #888888;
  }
}

.dialog_scroll {
  padding: 10px;
  height: 70vh;
  overflow-y: auto;
}
</style>
