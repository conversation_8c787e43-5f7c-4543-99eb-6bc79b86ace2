<template>
  <div class="record-main">
    <el-descriptions :column="1" border style="height: 800px;overflow-y: auto;">
      <el-descriptions-item
        :label="item.label"
        v-for="(item, index) in myTableData"
        :key="'myTable' + index"
        >{{ row[item.key] || "" }}</el-descriptions-item
      >
    </el-descriptions>

    <div class="right-steps">
      <el-steps direction="vertical" :active="stepsData.length">
        <el-step v-for="(item, index) in stepsData" :key="'steps' + index">
          <template #icon>{{ stepsData.length - index }}</template>

          <template #title>
            <div :style="{ color: item.direction ? 'none' : 'red' }">
              {{ item.nodeName }}
              <i v-if="item.direction" class="el-icon-circle-check" />
              <i v-else class="el-icon-circle-close" />
              &nbsp&nbsp&nbsp{{ item.endTime }}
              <i v-hasPermi="['comm:node:opinion:edit']" class="el-icon-edit-outline" @click="openEditDialog(item)" style="cursor: pointer;"></i>
              <i v-hasPermi="['comm:node:edit']" v-if="item.nodeName == '完工登记'"  class="el-icon-document" @click="openDrawer(item)" style="cursor: pointer;"></i>
            </div>
          </template>
          <template #description>
            <el-descriptions :column="1" style="width: 100%">
              <el-descriptions-item label="是否计算安全保通费">{{
                item.isGuarantee == 1 ? "是" : "否"
              }}</el-descriptions-item>
              <el-descriptions-item label="是否计算安全生产费">{{
                item.isProduction == 1 ? "是" : "否"
              }}</el-descriptions-item>
              <el-descriptions-item label="操作人">{{
                item.assigneeName
              }}</el-descriptions-item>
              <el-descriptions-item label="备注">{{
                item.comment
              }}</el-descriptions-item>
              <el-descriptions-item label="附件"
                >
                <file-upload style="width: 100%" v-if="item.fileList && item.fileList.length > 0 && item.fileList[0].fileId" v-model="item.fileList[0].fileId" :forView="true"></file-upload>
              </el-descriptions-item>
              <el-descriptions-item contentClassName="must-br">
                <template slot="label">
                  <i
                    v-if="!item.isShowMethod"
                    class="el-icon-arrow-right"
                    @click="changeShowMethod(index)"
                    style="cursor: pointer"
                  ></i>
                  <i
                    v-else
                    class="el-icon-arrow-down"
                    @click="changeShowMethod(index)"
                    style="cursor: pointer"
                  ></i>
                  养护方法
                </template>
                <el-table v-adjust-table :data="item.methodList" v-if="item.isShowMethod">
                  <el-table-column
                    prop="schemeCode"
                    align="center"
                    label="子目号"
                  >
                  </el-table-column>
                  <el-table-column
                    prop="schemeName"
                    align="center"
                    label="子目名称"
                  >
                  </el-table-column>
                  <el-table-column
                    prop="calcDesc"
                    align="center"
                    label="计算式"
                  >
                  </el-table-column>
                  <el-table-column prop="num" align="center" label="数量">
                  </el-table-column>
                  <el-table-column prop="unit" align="center" label="单位">
                  </el-table-column>
                </el-table>
              </el-descriptions-item>
            </el-descriptions>
          </template>
        </el-step>
      </el-steps>
    </div>
    <el-dialog
      title="附件列表"
      destroy-on-close
      :visible.sync="openFile"
      modal-append-to-body
      append-to-body
      width="800px"
    >
      <file-upload
        v-if="fileId"
        v-model="fileId"
        :owner-id="fileId"
        for-view
      ></file-upload>
    </el-dialog>
    <el-dialog
      append-to-body
      :visible.sync="editDialog"
      width="650px"
    >
      <el-form :model="editForm">
        <el-form-item label="操作人" label-width="100px">
          <el-cascader
              v-model="editForm.assignee"
              :options="deptUserOptions"
              :props="assigneeProps"
              :show-all-levels="false"
              ref="assigneeRef"
              filterable
              clearable
              collapse-tags
              @change="assigneeChange"
              style="width: 460px"
            >
          </el-cascader>
        </el-form-item>
        <el-form-item label="操作意见" label-width="100px">
          <el-input v-model="editForm.content" style="width: 460px;"/>
        </el-form-item>
        <el-form-item label="操作时间" label-width="100px">
          <el-date-picker
            v-model="editForm.endTime"
            type="datetime"
            format="yyyy-MM-dd HH:mm:ss"
            value-format="yyyy-MM-dd HH:mm:ss"
            placeholder="结束时间">
          </el-date-picker>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="editDialog = false">取 消</el-button>
        <el-button type="primary" @click="saveEdit">确 定</el-button>
      </span>
    </el-dialog>
    <el-drawer
        append-to-body
        modal-append-to-body
        :wrapperClosable="false"
        :title="drawerTitle"
        destroy-on-close
        :visible.sync="drawer"
        size="70%"
      >
        <RegDetail
          v-if="detailType==='reg'"
          :status="2"
          @close="handleCloseDetail"
          :row="row"
          :from-event="true">
        </RegDetail>
      </el-drawer>
  </div>
</template>
<script>
import { getTaskOperationRecord } from "@/api/regularTesting/taskManage/taskList";
import { getTreeStruct,editNodeInfo } from "@/api/tmpl";
import RegDetail from "@/views/regularTesting/constructionManage/component/regDetail.vue";
export default {
  components: {RegDetail},
  data() {
    return {
      myTableData: [
        {
          label: "项目名称",
          key: "projName",
        },
        {
          label: "任务单名称",
          key: "name",
        },
        {
          label: "任务单编码",
          key: "code",
        },
        {
          label: "路段名称",
          key: "maiSecName",
        },
        {
          label: "位置",
          key: "position",
        },
        {
          label: "施工单位",
          key: "checkDomainName",
        },
        {
          label: "施工合同",
          key: "checkConName",
        },
        {
          label: "验收人员",
          key: "acceptancePerson",
        },
        {
          label: "工作内容",
          key: "content",
        },
        {
          label: "实施要求",
          key: "exeRequire",
        },
      ],
      stepsData: [],
      openFile: false,
      fileId: "",
      editDialog: false,
      editForm: {
        id: '',
        assignee: '',
        content: '',
        startTime: '',
        endTime: '',
        taskId: ''
      },
      deptUserOptions: [],
      assigneeProps: {
        multiple: false, //是否多选
        value: 'id',
        emitPath: false,
      },
      curUser: null,
      drawer: false,
      detailType: '',
      detailData: {},
      drawerTitle: '',
    };
  },
  props: {
    row: {
      type: Object,
      default: {},
    },
  },
  watch: {
    row: {
      handler(val) {
        this.queryList()
      },
      immediate: true,
      deep: true,
    },
  },
  created() {
    this.getDeptTreeDef()
  },
  methods: {
    queryList() {
      if (this.row.id) {
        getTaskOperationRecord({ businessKey: this.row.id }).then((res) => {
          this.stepsData = res.data || [];
          this.stepsData.forEach((item) => {
            this.$set(item, "isShowMethod", false);
          });
        });
      }
    },
    changeShowMethod(index) {
      this.stepsData[index].isShowMethod = !this.stepsData[index].isShowMethod;
    },
    openFileModel(item) {
      if (item.fileList && item.fileList.length > 0) {
        this.fileId = item.fileList[0].fileId;
        this.openFile = true;
      }
    },
    openEditDialog(row) {
      this.editForm.id = row.id
      this.editForm.taskId = row.taskId
      this.editForm.assignee = row.userId
      this.editForm.content = row.comment
      this.editForm.startTime = row.startTime
      this.editForm.endTime = row.endTime
      this.curUser = {
        label: row.assignee,
        value: row.userId
      }
      this.editDialog = true
    },
    assigneeChange(value) {
       this.$nextTick(() => {
        this.curUser = this.$refs.assigneeRef.getCheckedNodes()[0]
      })
      console.log(this.curUser);
    },
    saveEdit() {
      const submitData = {
        ...this.editForm
      }
      submitData.assignee = `${this.curUser.label}@${this.curUser.value}`
      submitData.startTime = submitData.endTime
      editNodeInfo(submitData).then(()=> {
        this.queryList()
        this.editDialog = false
      })
    },
    getDeptTreeDef() {
      getTreeStruct({ types: 111, dataRule: false }).then((response) => {
        this.deptUserOptions = response.data;
      });
    },
    openDrawer(item) {
      if(item.nodeName == '完工登记') {
        this.detailType = 'reg'
        this.drawerTitle = '完工登记'
      }
      this.$nextTick(()=> {
        this.drawer = true
      })
    },
    handleCloseDetail() {
      this.queryList()
      this.drawer = false;
    }
  },
};
</script>
<style lang="scss" scoped>
.record-main {
  display: flex;
  .el-descriptions {
    width: 450px;
    font-size: 14px;
  }
  .right-steps {
    flex: 1;
    padding-left: 20px;
    height: 800px;
    overflow-y: auto;
  }

  ::v-deep .el-descriptions-item__container {
    &:has(.must-br) {
      display: block;
    }
  }
}
</style>
<style lang="scss" scoped>
@import "@/assets/styles/business.scss";
</style>
