<template>
  <div>
    <task-list :proj-code="projCode" :pros-status="1" :task-type="taskType"></task-list>
  </div>
</template>

<script>
import TaskList from "@/views/theft/taskList/component/taskList.vue";

export default {
  name: 'TaskListPrep',
  components: {TaskList},
  data() {
    return {
      projCode: '',
      taskType: ''
    }
  },
  created() {
    this.projCode = this.$route.query.projCode
    this.taskType = this.$route.query.taskType
  }
}
</script>

<style lang="scss" scoped>

</style>
<style lang="scss" scoped>
@import "@/assets/styles/business.scss";
</style>
