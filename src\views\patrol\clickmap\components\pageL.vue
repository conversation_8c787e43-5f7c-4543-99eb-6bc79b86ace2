<template>
  <div class="page-l">
    <!-- <section class="info-l">
      <CockpitCard unit="公里" class="mb-2" :h="'18vh'" @click="onclick(1)">
        <route-index></route-index>
      </CockpitCard>
      <CockpitCard title="桥梁" unit="座" class="mb-2" :h="'18vh'" @click="onclick(2)">
        <bridge-index></bridge-index>
      </CockpitCard>
      <CockpitCard title="隧道" unit="座" class="mb-2" :h="'18vh'" @click="onclick(3)">
        <tunnel-index></tunnel-index>
      </CockpitCard>
      <CockpitCard title="健康监测" class="mb-2" :h="'18vh'" @click="onclick(4)">
        <fitness-index></fitness-index>
      </CockpitCard>
    </section> -->
    <section class="info-c">
      <!-- <div class="map" ref="mapRef"></div> -->
      <MapView />
      <!-- <div class="map-case">
        <div
          v-for="(item, index) in caseList"
          :key="'case' + index"
          class="case-list"
        >
          <div class="case-row" :style="{ background: item.color }"></div>
          <span class="case-text">{{ item.name }}</span>
        </div>
      </div> -->
    </section>
    <!-- <section class="info-r">
      <CockpitCard title="日常养护" class="mb-2" :h="'18vh'" @click="onclick(5)">
        <daily />
      </CockpitCard>
      <CockpitCard title="专项养护" class="mb-2" :h="'41vh'" @click="onclick(6)">
        <div style="width: 100%;height: 200px;">
          <specially></specially>
        </div>
      </CockpitCard>
      <CockpitCard title="巡检查" class="mb-2" :h="'18vh'" @click="onclick(7)">
        <inspection-index></inspection-index>
      </CockpitCard>
    </section> -->
  </div>
</template>

<script>
import { isBigScreen } from '../util/utils';
// 组件
import CockpitCard from './cockpitCard.vue';
import RouteIndex from './route/index.vue';
import BridgeIndex from './bridge/index.vue';
import TunnelIndex from './tunnel/index.vue';
import FitnessIndex from './fitness/index.vue';
import Daily from './maintenance/daily.vue';
import Specially from './maintenance/specially.vue';
import InspectionIndex from './inspection/index.vue';
import MapView from './mapView.vue';

// ol
import { Map, View } from 'ol';
import { defaults } from 'ol/control';
import { fromLonLat } from 'ol/proj';
// api
import { getShapeList } from '@/api/oneMap/deptInfo';
import { getMenuSub } from '@/api/oneMap/menuSub';
import {
  addMapMask,
  addVectorTile,
  addWidthFeature,
  removeLayer,
} from '@/views/map/components/common/mapFun';
import cache from '@/plugins/cache';
import TileLayer from 'ol/layer/Tile';
import { XYZ } from 'ol/source';

var key = 'cde0b56cf882626889981701109a7536';
const lineId = '1824259894327382017';

export default {
  components: {
    CockpitCard,
    RouteIndex,
    BridgeIndex,
    TunnelIndex,
    FitnessIndex,
    Daily,
    Specially,
    InspectionIndex,
    MapView,
  },
  data() {
    return {
      screenBool: isBigScreen(),
      zoom: 7,
      caseList: [
        {
          name: '畅通',
          color: '#00BF2D',
        },
        {
          name: '缓慢',
          color: '#FFE400',
        },
        {
          name: '拥堵',
          color: '#D90000',
        },
        {
          name: '严重拥堵',
          color: '#780000',
        },
      ],
    };
  },
  mounted() {
    // this.initMap();
  },
  unmounted() {
    window.mapLayer = null;
  },
  methods: {
    initMap() {
      window.mapLayer = new Map({
        target: this.$refs.mapRef,
        view: new View({
          projection: 'EPSG:3857',
          //地图初始中心点
          center: fromLonLat([102.75530900000001, 24.95423900000002]),
          //地图初始显示级别
          minZoom: 6,
          zoom: this.screenBool ? this.zoom + 1 : this.zoom,
          maxZoom: 25,
          padding: [50, 0, 50, this.screenBool ? 300 : 150],
          rotation: 0,
          enableRotation: true,
        }),
        layers: [
          // new TileLayer({
          //   name: "天地图矢量图层",
          //   source: new XYZ({
          //     url: `http://t{0-7}.tianditu.gov.cn/DataServer?T=img_w&x={x}&y={y}&l={z}&tk=${key}`,
          //     attributions: "天地图的属性描述",
          //     wrapX: false,
          //     maxZoom: 18,
          //   }),
          //   preload: Infinity,
          //   visible: true,
          // })
        ],
        controls: defaults({
          attribution: false,
          zoom: false,
          rotate: false,
        }),
      });
      window.mapLayer.on('postrender', (e) => {
        this.onPostRender(e);
      });
      // 地图范围
      this.getRangeShape();
      this.addLine();
    },
    // 获取
    getRangeShape(deptId = null, click = false) {
      this.$modal.loading();
      let deptIds;
      let isAdmin = this.$store.getters.roles.includes('admin');
      if (deptId) {
        deptIds = [deptId];
      } else {
        deptIds = isAdmin ? [] : [deptId];
      }
      getShapeList({ sysDeptIds: deptIds })
        .then((res) => {
          if (res.code == 200) {
            // this.rangeData = res.data || [];
            if (!click) {
              cache.session.setJSON('rangeData', res.data);
            }
            removeLayer(window.mapLayer, 'dataLayer');
            addMapMask(window.mapLayer, res.data, 'rgba(8,5,66,0)');
            let [feature] = addWidthFeature(
              window.mapLayer,
              res.data,
              'dataLayer',
              null,
              true
            );

            if (res.data && res.data.length == 1) {
              window.mapLayer.getView().fit(feature.getGeometry().getExtent(), {
                duration: 500,
              });
            }
            // 处理点击后移除不了图层数据问题
            if (click) {
              this.removeLayer('dataLayer');
              // removeLayer(window.mapLayer, 'dataLayer', 'name');
            }
          }
        })
        .finally(() => {
          this.$modal.closeLoading();
        });
    },
    // 添加路线
    async addLine() {
      this.$modal.loading();
      await getMenuSub(lineId)
        .then((res) => {
          let layerName = '路网信息';
          let lineObj = res.data;
          lineObj.borderColour = 'rgba(0,191,45,0.8)';
          addVectorTile(
            window.mapLayer,
            'name',
            layerName,
            JSON.stringify({ id: lineId }),
            lineObj,
            true,
            10,
            6,
            256,
            false
          );
        })
        .finally(() => {
          this.$modal.closeLoading();
        });
    },
    // 实现三维倾斜
    onPostRender(evt) {
      const ctx = evt.context;
      // console.log('地图事件', ctx)
    },
    // 点击事件 index 1-7 分别是 1、路网，2、桥梁，3、隧道，4、健康监测，5、日常养护，6、专项养护，7、巡检查
    onclick(index) {
      let obj = {};
      switch (index) {
        case 1:
          obj = {
            title: '路网养护专题',
            subText: '',
            component: '',
          };
          break;
        case 2:
          obj = {
            title: '桥梁养护专题',
            subText: '龙江特大桥(上行)',
            componentL: 'BridgeSpecial',
            componentR: 'BridgeDetail',
          };
          break;
        case 3:
          obj = {
            title: '隧道养护专题',
            subText: '白塔村隧道左幅',
            componentL: 'TunnelSpecial',
            componentR: 'TunnelDetail',
          };
          break;
        case 4:
          obj = {
            title: '工程集群健康监测',
            subText: '',
            componentL: 'HealthIndex',
            componentR: '',
          };
          break;
        default:
          break;
      }
      this.$emit('click', obj);
    },
  },
};
</script>

<style lang="scss" scoped>
@import '@/assets/styles/utils.scss';

.page-l {
  display: flex;
  align-items: center;
  .info-l {
    flex: 1;
  }

  .info-c {
    flex: 3;
    height: 100%;
    height: calc(100vh - 100px);
    position: relative;

    .map {
      width: 100%;
      cursor: pointer;
    }

    .map-case {
      position: absolute;
      bottom: vwpx(-10px);
      right: vwpx(20px);
      display: flex;
      align-items: center;

      .case-list {
        display: flex;
        flex-direction: column;

        .case-row {
          width: vwpx(140px);
          height: vwpx(8px);
        }

        .case-text {
          font-family: Microsoft YaHei UI, Microsoft YaHei UI;
          font-weight: 400;
          font-size: vwpx(26px);
          color: #ffffff;
          text-align: left;
          font-style: normal;
          text-transform: none;
          margin-top: vwpx(6px);
          margin-left: vwpx(6px);
        }
      }
    }
  }

  .info-r {
    flex: 1;
  }

  .mb-2 {
    margin-bottom: vwpx(20px);
  }
}
</style>
