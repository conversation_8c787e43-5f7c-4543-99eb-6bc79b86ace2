//湿度概率直方图
import { getMax, getMin } from '@/views/jgjc/dataAnalysis/dataView/drawMethods/util'
import * as echarts from "echarts";
import myTheme from './myTheme'
export const WYTJ = {

  //位移温度相关性图
  setZZWYCORChart(chartName, data) {
    let myChart = echarts.getInstanceByDom(
      document.getElementById(chartName)
    );
    if (myChart !== undefined) {
      myChart.dispose();
    }
    myChart = echarts.init(document.getElementById(chartName), myTheme.theme);
    let optionName = data.optionName;
    let xMin = getMin(data.xData.dataList)
    let xMax = getMax(data.xData.dataList)
    let yMax = getMax(data.yData.dataList)
    let yMin = getMin(data.yData.dataList)

    let minPoint = [
      xMin,
      (
        xMin *
        data.k +
        data.b
      ).toFixed(2),
    ];
    let maxPoint = [
      xMax,
      (
        xMax *
        data.k +
        data.b
      ).toFixed(2),
    ];
    // 一些特殊情况处理
    if (xMax === 0 && xMin === 0) {
      xMax = 1
      xMin = -1
    } else {
      let delta = (xMax - xMin) * 0.2
      if (delta === 0) {
        delta = 1
      }
      xMax = xMax + delta
      xMin = xMin - delta
    }
    if (yMax === 0 && yMin === 0) {
      yMax = 1
      yMin = -1
    } else {
      let delta = (yMax - yMin) * 0.2
      if (delta === 0) {
        delta = 1
      }
      yMax = yMax + delta
      yMin = yMin - delta
    }

    let dataset = [];
    for (let i in data.xData.dataList) {
      let tmpLine = [];
      tmpLine.push(data.xData.dataList[i]);
      tmpLine.push(data.yData.dataList[i]);
      dataset.push(tmpLine);
    }
    let markLineOpt = {
      animation: false,
      label: {
        formatter:
          "y = " +
          Number(data.k).toFixed(3) +
          " * x + " +
          Number(data.b).toFixed(3) +
          "  相关系数为" +
          Number(data.corrGust).toFixed(3) +
          "  ",
        // formatter: "y = 0.5 * x + 3",
        align: "right",
        fontSize: 18,
        fontWeight: "bold",
        distance: 90
      },
      lineStyle: {
        width: 2,
        type: "solid",
        color: 'red',
      },
      data: [
        [
          {
            coord: minPoint,
            symbol: "none",
          },
          {
            coord: maxPoint,
            symbol: "none",
          },
        ],
      ],
    };

    let option = {
      title: {
        left: 'center',
        text: optionName,
      },
      tooltip: {
        textStyle:{
          align:'left'
        },
        trigger: "axis",
      },
      grid: {
        left: "3%",
        right: "3%",
        bottom: "5%",
        top: "13%",
        containLabel: true,
      },
      toolbox: {
        feature: {
          saveAsImage: {
            title: "保存",
          },
        },
      },
      xAxis: {
        name: data.xData.name + "/" + data.xData.unit,
        nameLocation: "middle",
        type: "value",
        scale: true,
        min: xMin,
        max: xMax,
        nameGap: 20,
        axisLabel: {
          formatter: function (value) {
            return value.toFixed(data.xData.accuracy >= 0  ? data.xData.accuracy : 4); // 2表示小数为2位
          },
        },
        axisLine:{
          show: true,
          onZero: false,
        },
        axisTick: {
          show: false
        },
      },
      yAxis: {
        name: this.splitBySpace(data.yData.name) + "/" + data.yData.unit,
        nameTextStyle: {
          width: 200,
          overflow: 'break'
        },
        type: "value",
        scale: true,
        min: yMin,
        max: yMax,
        axisLabel: {
          formatter: function (value) {
            return value.toFixed(data.yData.accuracy >= 0  ? data.yData.accuracy : 4); // 2表示小数为2位
          },
        },
        axisLine:{
          show: true,
          onZero: false,
        },
        axisTick: {
          show: false
        },
      },
      dataZoom: [
        {
          type: "inside", //详细配置可见echarts官网
        },
      ],
      series: [
        {
          // name: "",
          type: "scatter",
          data: dataset,
          markLine: markLineOpt,
        },
      ],
    };
    option && myChart.setOption(option);
    //自适应大小
    window.onresize = function () {
      myChart.resize();
    };
  },
  //累计位移时程图
  setLJWYChart(chartName, data) {
    let myChart = echarts.getInstanceByDom(
      document.getElementById(chartName)
    );
    if (myChart !== undefined) {
      myChart.dispose();
    }
    myChart = echarts.init(document.getElementById(chartName), myTheme.theme);
    let optionName = data.optionName;
    data = data.dataList
    // 数据处理 给值增加时间戳
    data.forEach((item,index) =>{
      let tmpDotList0 = []
      for(let i = 0; i < item.result.processedData.values.length; i++) {
        if(i === 0){
          item.result.processedData.times[i] = item.result.processedData.times[i] + '.000'
        }
        tmpDotList0.push([item.result.processedData.times[i], item.result.processedData.values[i].toFixed(item.result.accuracy >= 0  ? item.result.accuracy : 4)])
      }
      item.result.processedData.dotList = tmpDotList0
    })
    let option = {
      title: {
        left: 'center',
        text: optionName,
      },
      toolbox: {
        right: 0,
        top: 25,
        feature: {
          saveAsImage: {
            title: '保存'
          },
        },
      },
      tooltip: {
        textStyle:{
          align:'left'
        },
        trigger: "axis",
        confine: true
      },
      xAxis: {
        type: 'category',
        name: "时间",
        nameLocation: "middle",
        nameGap: 22,
        axisLabel: {
          formatter: function (value, index) {
            return value.substring(0, value.length - 12);
          },
          fontSize: 11,
        },
      },
      yAxis: [],
      dataZoom: [
        {
          type: "inside",
        },
      ],
      grid: [
        {
          top: "17%",
          left: "2%",
          right: "10%",
          bottom: "12%",
          containLabel: true,
        },
      ],
      series: [],
      legend: {
        type: 'scroll',
        data: [],
        x: 'center',
        y: 'bottom',
        selected: {},
      },
    };
    //加入多行数据
    let finalMax = 0;
    let finalMin = 0;
    for(let i = 0 ; i < data.length; i++){
      option.legend.data.push(data[i].label);
      option.series.push({
        type: "line",
        name: data[i].label,
        showSymbol: false,
        data: data[i].result.processedData.dotList,
      });

      //计算最大值与最小值
      let minValue = getMin(data[i].result.processedData.values); // 输出最小值
      let maxValue = getMax(data[i].result.processedData.values); // 输出最大值
      // 一些特殊情况处理
      if (maxValue === 0 && minValue === 0) {
        maxValue = 1
        minValue = -1
      } else {
        let delta = Math.ceil((maxValue - minValue) * 0.2 * (10 ** data[i].result.accuracy))/(10 ** data[i].result.accuracy)
        if(delta === 0){
          delta = 1
        }
        let midValue = Math.ceil((maxValue + minValue) / 2 * (10 ** data[i].result.accuracy))/(10 ** data[i].result.accuracy)
        maxValue = midValue + 3 * delta
        minValue = midValue - 3 * delta
      }
      // 外层循环最大最小值更新
      if(i === 0){
        finalMax = maxValue
        finalMin = minValue
      }else {
        if(finalMax < maxValue){
          finalMax = maxValue
        }
        if(finalMin > minValue){
          finalMin = minValue
        }
      }
      // 一些预警线
      if (data[i].result.limitInfo) {
        Object.keys(data[i].result.limitInfo).forEach((key) => {
          option.series.push({
            type: "line",
            name: data[i].label + key,
            showSymbol: false,
            animation: false,
            markLine: {
              symbol: 'none',
              data: [
                {
                  yAxis: data[i].result.limitInfo[key],
                  lineStyle: {
                    type: 'solid'
                  },
                  label: {
                    show: true,
                    position: 'insideEndTop',
                  },
                }
              ]
            }
          });
          option.legend.data.push(data[i].label + key)
        })
      }
    }
    // 一些特殊情况处理
    if (finalMax === 0 && finalMin === 0) {
      finalMax = 1
      finalMin = -1
    } else {
      let delta = Math.ceil((finalMax - finalMin) * 0.2 * (10 ** data[0].result.accuracy))/(10 ** data[0].result.accuracy)
      if(delta === 0){
        delta = 1
      }
      let midValue = Math.ceil((finalMax + finalMin) / 2 * (10 ** data[0].result.accuracy))/(10 ** data[0].result.accuracy)
      finalMax = midValue + 3 * delta
      finalMin = midValue - 3 * delta
    }
    option.yAxis.push({
      name: '长度/mm',
      nameTextStyle: {
        fontWeight: "bold",
        fontSize: 12
      },
      axisLabel: {
        formatter: function (value) {
          return value.toFixed(data[0].result.accuracy >= 0  ? data[0].result.accuracy : 4); // 2表示小数为2位
        },
      },
      splitNumber: 6,
      min: finalMin,
      max: finalMax,
      interval: (finalMax - finalMin) / 6, // 标轴分割间隔
    })
    option && myChart.setOption(option);
    window.onresize = function () {
      myChart.resize();
    };
  },
  //绘制空表 提示信息
  setEmptyChart(chartName, msg) {
    let myChart = echarts.getInstanceByDom(
      document.getElementById(chartName)
    );
    if (myChart !== undefined) {
      myChart.dispose();
    }
    myChart = echarts.init(document.getElementById(chartName), myTheme.theme);
    let option = {
      title: {
        text: msg,
        left:'center',
      },
    };
    option && myChart.setOption(option);
    //自适应大小
    window.onresize = function () {
      myChart.resize();
    };
  },
  //坐标轴名称过长时添加换行
  splitBySpace(str){
    let n = str.length
    if(n>5){
      for(let i = 0; i < Math.floor(n/5); i++){
        str = str.substring(0, (i+1) * 5) + '\n' + str.substring((i+1) * 5)
      }
    }
    return str
  }
}
