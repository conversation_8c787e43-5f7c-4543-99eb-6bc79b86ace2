<template>
  <div class="roadbedadd">
    <el-dialog
      :title="title"
      :visible.sync="showAddEdit"
      width="60%"
      append-to-body
      :before-close="handleClose"
      :close-on-click-modal="false"
      :class="'roadbedDialog'"
    >
      <div style="height: 45vh; overflow-y: auto; padding: 0 10px 0 5px">
        <div class="check-edit-title">设备信息</div>
        <el-form
          ref="baseInfoData"
          :model="baseInfoForm"
          label-width="150px"
          size="small"
          style="margin: 10px"
          :disabled="title == '查看路面评定单元数据' ? true : false"
        >
          <el-row style="display: flex; flex-wrap: wrap">
            <ManageSelectTree placeholder="请选择" :formObject="baseInfoForm" />
            <el-col
              :span="i.span || 12"
              v-for="(i, idx) in baseInfoData.slice(2)"
              :key="idx"
            >
              <el-form-item :label="i.label" :prop="i.prop" :rules="i.rules">
                <span v-if="i.type === 'input'">
                  <el-input
                    v-model="baseInfoForm[i.prop]"
                    :disabled="i.disabled ? i.disabled : false"
                    :placeholder="i.placeholder"
                  />
                </span>

                <span v-if="i.type === 'pileInput'">
                  <PileInput
                    v-model="baseInfoForm[i.prop]"
                    :placeholder="i.placeholder"
                  />
                </span>
                <span v-else-if="i.type === 'inputNumber'">
                  <el-input-number
                    style="width: 100%"
                    v-model="baseInfoForm[i.prop]"
                    :precision="i.precision"
                    @change="
                      changeInputNumber(i.precision, 'baseInfoForm', i.prop)
                    "
                  ></el-input-number>
                </span>
                <span v-else-if="i.type === 'select'">
                  <div v-if="i.dict">
                    <el-select
                      style="width: 100%"
                      v-model="baseInfoForm[i.prop]"
                      placeholder="请选择"
                      :disabled="i.disabled ? i.disabled : false"
                      @change="changeSelect($event, i)"
                    >
                      <el-option
                        v-for="dict in dict.type[i.dict]"
                        :key="dict.value"
                        :label="dict.label"
                        :value="dict.value"
                      >
                      </el-option>
                    </el-select>
                  </div>
                  <div v-else-if="i.deptType">
                    <SelectTree
                      v-model="baseInfoForm[i.prop]"
                      :dept-type="i.deptType"
                    />
                  </div>
                  <div v-else>
                    <el-select
                      style="width: 100%"
                      v-model="baseInfoForm[i.prop]"
                      :placeholder="i.placeholder"
                      filterable
                      :disabled="!baseInfoForm[i.disabledFieds]"
                      @change="
                        (val) => {
                          handleSelect(val, i);
                        }
                      "
                    >
                      <el-option
                        v-for="(v, index) in i.options"
                        :key="index"
                        :label="v[i.optionLabel]"
                        :value="v[i.optionValue]"
                      />
                    </el-select>
                  </div>
                </span>
                <span v-else-if="i.type === 'coordinate'">
                  <lon-lat
                    :type="i.prepend"
                    :lon.sync="baseInfoForm[i.propLon]"
                    :lat.sync="baseInfoForm[i.propLat]"
                  />
                </span>
                <span v-else-if="i.type === 'date'">
                  <el-date-picker
                    style="width: 100%"
                    v-model="baseInfoForm[i.prop]"
                    type="datetime"
                    :placeholder="i.placeholder"
                    value-format="yyyy-MM-dd  HH:mm:ss"
                  />
                </span>
                <span v-else-if="i.type === 'year'">
                  <el-date-picker
                    style="width: 100%"
                    v-model="baseInfoForm[i.prop]"
                    :type="'year'"
                    :placeholder="i.placeholder"
                    value-format="yyyy"
                  />
                </span>
                <span v-else-if="i.type === 'inputUser'">
                  <el-cascader
                    ref="userCascade"
                    v-model="oprUser"
                    :options="deptUserOptions"
                    :props="{ multiple: true, value: 'id', emitPath: false }"
                    :show-all-levels="false"
                    filterable
                    style="width: 100%"
                  />
                </span>
                <span v-else-if="i.type === 'multiDictSelect'">
                  <MultiDictSelect
                    v-model="baseInfoForm[i.prop]"
                    :disabled="i.disabledFieds"
                    :multiple="i.multiple"
                    :options="dict.type[i.dict]"
                  />
                </span>
                <span v-else-if="i.type === 'updataFile'">
                  <FileUpload
                    :key="ownerId"
                    v-model="form.reportPath"
                    :limit="1"
                    :owner-id="ownerId"
                    storage-path="/base/roadbed/periodic"
                    platform="mpkj"
                  />
                </span>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button
          v-if="title == '查看路面评定单元数据' ? false : true"
          type="primary"
          @click="handleSubmit('submit')"
          >提 交</el-button
        >
        <el-button @click="handleClose">取 消</el-button>
      </div>
    </el-dialog>
    <el-dialog
      title="选择路面"
      :visible.sync="showRoadbedSelect"
      width="80%"
      append-to-body
      :before-close="handleRoadbedClose"
      :close-on-click-modal="false"
    >
      <div style="height: 70vh">
        <div style="display: flex; align-items: center; margin: 0">
          <div style="min-width: 210px; margin: 0 20px">
            <el-input
              v-model="queryParams.routeCode"
              placeholder="请输入路线编码"
              @keyup.enter.native="handleQuery"
            />
          </div>
          <div style="min-width: 210px; margin: 0 20px">
            <el-input
              v-model="queryParams.routeName"
              placeholder="请输入路线名称"
              @keyup.enter.native="handleQuery"
            />
          </div>
          <div style="min-width: 210px; margin: 0 20px">
            <el-select
              v-model="queryParams.routeType"
              placeholder="请选择路线性质"
            >
              <el-option
                v-for="dict in dict.type.sys_route_nature"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </div>
          <div style="min-width: 220px">
            <el-button
              type="primary"
              icon="el-icon-search"
              size="mini"
              @click="handleQuery"
              >搜索</el-button
            >
            <el-button
              style="background: #f2f3f5; border: 1px #f2f3f5 solid"
              icon="el-icon-refresh"
              size="mini"
              @click="resetQuery"
              >重置</el-button
            >
          </div>
        </div>
        <el-divider></el-divider>
        <el-table
          ref="table"
          v-loading="loading"
          height="80%"
          style="width: 100%"
          :data="staticList"
          :header-cell-style="{
            background: '#F2F3F5',
            height: '40px !important',
          }"
          :row-style="rowStyle"
          @row-click="handleRowClick"
        >
          <el-table-column
            fixed
            label="序号"
            type="index"
            width="50"
            align="center"
          />
          <el-table-column
            label="路线编码"
            fixed
            :show-overflow-tooltip="true"
            align="center"
            prop="routeCode"
          />
          <el-table-column
            label="路线名称"
            fixed
            :show-overflow-tooltip="true"
            align="center"
            prop="routeName"
          />
          <el-table-column
            label="起点名称"
            fit
            :show-overflow-tooltip="true"
            align="center"
            prop="placeStartName"
          />
          <el-table-column
            label="终点名称"
            :show-overflow-tooltip="true"
            align="center"
            prop="placeEndName"
          />
          <el-table-column
            label="起点桩号"
            :show-overflow-tooltip="true"
            align="center"
            prop="pileStart"
            :formatter="(...arg) => formatPile(arg[2])"
          />
          <el-table-column
            label="终点桩号"
            :show-overflow-tooltip="true"
            align="center"
            prop="pileEnd"
            :formatter="(...arg) => formatPile(arg[2])"
          />
          <el-table-column
            label="路线性质"
            :show-overflow-tooltip="true"
            align="center"
            prop="routeType"
          >
            <template slot-scope="scope">
              <dict-tag
                :options="dict.type.sys_route_nature"
                :value="scope.row.routeType"
              />
            </template>
          </el-table-column>
          <el-table-column
            label="抗震等级"
            :show-overflow-tooltip="true"
            align="center"
            prop="seismicGrade"
          >
            <template slot-scope="scope">
              <dict-tag
                :options="dict.type.side_slope_seismic_grade"
                :value="scope.row.seismicGrade"
              />
            </template>
          </el-table-column>
          <el-table-column
            label="路线长度(m)"
            :show-overflow-tooltip="true"
            align="center"
            prop="roadSectionLength"
            :formatter="
              (...arg) => {
                if (arg[2]) return arg[2].toLocaleString();
              }
            "
          />
          <el-table-column
            label="创建时间"
            :show-overflow-tooltip="true"
            align="center"
            prop="createdTime"
            width="180"
            v-if="false"
          >
            <template slot-scope="scope">
              <span>{{ parseTime(scope.row.createdTime, "{y}-{m}-{d}") }}</span>
            </template>
          </el-table-column>
        </el-table>
        <pagination
          :total="total"
          :page.sync="queryParams.pageNum"
          :limit.sync="queryParams.pageSize"
          :pageSizes="[10, 20, 30, 50, 100, 1000]"
          @pagination="getList"
        />
      </div>
    </el-dialog>
  </div>
</template>

<script>
import dictionary from "../dataDictionary";
import {
  addRoadbedAssess,
  updateRoadbedAssess,
} from "@/api/baseData/roadbed/assess/index";

import { maintenanceSectionList } from "@/api/baseData/subgrade/baseInfo/getSections";

import { listMaintenanceSectionAll } from "@/api/system/maintenanceSection";
import CascadeSelection from "@/components/CascadeSelection/index.vue";
import { createIdWorker } from "@/api/baseData/common";
import { listAllHighwaySections } from "@/api/system/highwaySections.js";
import SelectTree from "@/components/DeptTmpl/selectTree";
import SectionSelect from "@/components/SectionSelect";
import { listAllRoute } from "@/api/system/route.js";
import "@riophae/vue-treeselect/dist/vue-treeselect.css";
import Treeselect from "@riophae/vue-treeselect";

import { listRoute, getRoute } from "@/api/system/route";
import { getTreeStruct } from "@/api/tmpl";
import PileInput from "@/components/PileInput/index.vue";
import { listByMaintenanceSectionId } from "@/api/baseData/common/routeLine";
import ManageSelectTree from "@/components/manageSelectTree/index.vue";
import MultiDictSelect from "@/views/baseData/components/MultiDictSelect/index.vue";
import lonLat from "@/components/mapPosition/lonLat.vue";

const apis = {
  listAllHighwaySections: listAllHighwaySections,
  listAllRoute: listAllRoute,
  maintenanceSectionList: maintenanceSectionList,
  listMaintenanceSectionAll: listMaintenanceSectionAll,
  listByMaintenanceSectionId: listByMaintenanceSectionId,
};

export default {
  name: "subgrade-baseInfo-addAndEdit",
  components: {
    SelectTree,
    Treeselect,
    SectionSelect,
    PileInput,
    CascadeSelection,
    ManageSelectTree,
    MultiDictSelect,
    lonLat,
  },
  props: {
    formData: {
      default: {},
    },
    showAddEdit: {
      default: false,
    },
    title: {
      default: "添加路面评定单元静态数据",
    },
    forView: {
      default: false,
    },
  },
  dicts: [
    "roadbed_elect_assess_type",
    "base_data_yes_no",
    "sys_route_nature",
    "sys_route_direction",
    "sys_surface_type",
    "sys_route_grade",
  ],
  data() {
    return {
      activeName: "baseInfoData",
      sectionId: "",
      baseInfoData: [],
      specialExamData: [],
      deptUserOptions: [],

      queryParams: {
        pageNum: 1,
        pageSize: 20,
      },
      ids: [],
      baseInfoForm: {
        managementMaintenanceId: "",
        managementMaintenanceBranchId: "",
      },
      showRoadbedSelect: false,
      staticList: [],
      total: 0,
      form: {},
      loading: false,
      types: 101,
      ownerId: undefined,
    };
  },
  created() {
    if (this.formData.baseInfoForm.id) {
      const data = JSON.parse(JSON.stringify(this.formData.baseInfoForm));
      data.routeLevel = data.routeLevel ? data.routeLevel.split(",") : "";
      this.baseInfoForm = data;
    }
    createIdWorker().then((res) => {
      if (res.code === 200) {
        this.ownerId = Number(res.data);
      }
    });

    getTreeStruct({ types: 111, deptTypeList: null }).then((response) => {
      this.deptUserOptions = response.data;
    });
  },
  mounted() {
    this.initFormSlections();
  },
  methods: {
    // 获取表格数据
    getList() {
      this.loading = true;
      listRoute(this.queryParams).then((response) => {
        this.staticList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 搜索按钮
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    // 重置按钮
    resetQuery() {
      this.queryParams = {
        pageNum: 1,
        pageSize: 20,
      };
      this.handleQuery();
    },

    // 获取表单字段以及表单选项列表
    initFormSlections() {
      // 行政识别数据

      this.$nextTick(() => {
        this.baseInfoData = JSON.parse(JSON.stringify(dictionary.baseInfoData));
        this.baseInfoData.map(async (el, i) => {
          // if (el.api&&el.type=='select') {
          //   const r = await apis[el.api](
          //         [this.baseInfoForm.managementMaintenanceId] || ['']
          //       )
          //     // 临时编码，返回的数据不合规范，数组去重
          //     if (r.row||r.data) {
          //       r.list = Array.from(new Set((r.row||r.data).map(JSON.stringify))).map(
          //         JSON.parse
          //       )
          //     }
          //     el.options = r.list
          //   }
        });
      });
      // 结构技术数据
    },

    handleSelect(e, i) {
      switch (i.prop) {
        case "routeCode":
          if (e) {
            this.baseInfoData.forEach((el) => {
              if (el.prop === "routeName") {
                const option = el.options.find((i) => i.routeCode === e);
                if (option) {
                  this.baseInfoForm.routeId = option.routeId;
                  this.baseInfoForm.routeName = option.routeName;
                  // this.form.routeLevel = option.routeType.toString()
                }
              }
            });
          } else {
            this.baseInfoForm.routeId = "";
            this.baseInfoForm.routeName = "";
          }
          break;
        case "routeName":
          if (e) {
            this.baseInfoData.forEach((el) => {
              if (el.prop === "routeCode") {
                const option = el.options.find((i) => i.routeName === e);
                if (option) {
                  this.baseInfoForm.routeId = option.routeId;
                  // this.form.routeCode = option.routeCode
                }
              }
            });
          } else {
            this.baseInfoForm.routeId = "";
            this.baseInfoForm.routeCode = "";
          }
          break;
      }
    },

    changeSelect(e, i) {
      this.$forceUpdate();
      if (i.prop == "sectionType") {
        const item = this.baseInfoData.find(
          (obj) => obj.prop === "sectionType"
        );
        item.disabled = false;
      }
    },
    getRoadbed(e, i) {
      this.showRoadbedSelect = true;
      this.getList();
    },
    async handleSubmit(type) {
      const formNames = ["baseInfoData"];
      switch (type) {
        case "submit":
          this.form.isStaging = false;
          break;
        case "save":
          this.form.isStaging = true;
          break;
      }

      if (!this.form.isStaging) {
        for (let index = 0; index < formNames.length; index++) {
          const element = formNames[index];
          const valid = await new Promise((resolve) => {
            this.$refs[element].validate((valid) => {
              resolve(valid);
            });
          });
          if (!valid) {
            // 如果表单校验不通过，定位到对应的tab
            this.activeName = element;
            return; // 中止提交操作
          }
        }
      }
      let listBase = JSON.parse(JSON.stringify(this.baseInfoForm));

      this.baseInfoData.map((el) => {
        if (
          el.type == "pileInput" &&
          el.precision &&
          this.baseInfoForm[el.prop]
        ) {
          listBase[el.prop] = this.baseInfoForm?.[el.prop]
            ?.toFixed(el.precision)
            .toString();
        }
      });

      if (Array.isArray(this.baseInfoForm.routeLevel)) {
        listBase.routeLevel = this.baseInfoForm.routeLevel.join(",");
      }

      if (this.form.isStaging) {
      } else {
        if (this.baseInfoForm.id != null) {
          updateRoadbedAssess(listBase).then((response) => {
            this.$modal.msgSuccess(
              this.form.isStaging == true ? "暂存成功" : "修改成功"
            );
            this.$emit("refresh");
            this.initFormSlections();
          });
        } else {
          addRoadbedAssess(listBase).then((response) => {
            this.$modal.msgSuccess(
              this.form.isStaging == true ? "暂存成功" : "新增成功"
            );
            this.$emit("refresh");
            this.initFormSlections();
          });
        }
      }
    },

    // 勾选高亮
    rowStyle({ row, rowIndex }) {
      if (this.ids.includes(row.id)) {
        return { "background-color": "#EFF5FF" };
      } else {
        return { "background-color": "#fff" };
      }
    },

    changeInputNumber(precision, info, val) {
      let num = parseFloat(this[info][val]).toFixed(precision);
      this.$nextTick(() => {
        this[info][val] = num;
      });
    },

    // 表格点击勾选
    handleRowClick(row) {
      row.isSelected = !row.isSelected;
      this.$refs.table.toggleRowSelection(row);
      this.ids.push(row.id);

      this.showRoadbedSelect = false;
      //将this.baseInfodata中得prop变成数组
      let propArr = this.baseInfoData.map((el) => el.prop);

      getRoute(row.routeId).then((res) => {
        if (res && res.data) {
          this.$nextTick(() => {
            this.$set(this.baseInfoForm, "routeId", res.data.routeId);
            delete res.data.startStake;
            delete res.data.endStake;
            for (let key in res.data) {
              if (propArr.includes(key)) {
                this.$set(this.baseInfoForm, key, res.data[key]);
              }
            }
          });
        }
      });
    },

    // 监听选中管理处
    deptChange(e) {
      if (!e) return;
      listMaintenanceSectionAll({ departmentId: e }).then((res) => {
        if (res.code == 200) {
          this.baseInfoData.forEach((el) => {
            if (el.prop === "maintenanceSectionId") {
              el.options = res.data;
            }
          });
        }
      });
    },
    maintenanceSectionChange(e) {
      if (!e) return;

      this.baseInfoData.forEach((el) => {
        if (el.prop === "maintenanceSectionId") {
          //el.options中maintenanceSectionName等于e的routeGrade

          const option = el.options.find((i) => i.maintenanceSectionId === e);
          if (option && option.routeGrade) {
            this.baseInfoForm.routeLevel = option.routeGrade.split(",");
          }
        }
      });

      listByMaintenanceSectionId({ maintenanceSectionId: e }).then((res) => {
        if (res.code == 200) {
          this.baseInfoData.forEach((el) => {
            if (el.prop === "routeCode" || el.prop === "routeName") {
              el.options = res.data;
            }
          });
        }
      });
    },

    handleRoadbedClose() {
      this.showRoadbedSelect = false;
    },
    handleClose() {
      if (this.forView) {
        this.form = {};
        this.$emit("close", false);
      } else {
        this.$modal
          .confirm("确认退出？")
          .then(() => {
            this.form = {};
            this.$emit("close", false);
            this.initFormSlections();
          })
          .catch(() => {});
      }
    },
  },
  watch: {
    "baseInfoForm.managementMaintenanceId"(newVal, oldVal) {
      // if (newVal) {
      //   this.deptChange(newVal);
      // }
      // if (oldVal && this.form.maintenanceSectionId) {
      //   this.form.maintenanceSectionId = "";
      //   this.form.routeCode = "";
      // }
    },
    "baseInfoForm.managementMaintenanceBranchId"(newVal, oldVal) {
      if (newVal) {
        this.deptChange(newVal);
      }
      if (oldVal) {
        if (this.baseInfoForm.maintenanceSectionId)
          this.baseInfoForm.maintenanceSectionId = "";
        if (this.baseInfoForm.routeCode) this.baseInfoForm.routeCode = "";
      }
    },
    "baseInfoForm.maintenanceSectionId"(newVal, oldVal) {
      if (newVal) {
        this.maintenanceSectionChange(newVal);
      }
      if (!newVal && this.baseInfoForm.routeName) {
        this.baseInfoForm.routeLevel = [];
        this.baseInfoForm.routeName = "";
      }

      if (oldVal && this.baseInfoForm.routeCode) {
        this.baseInfoForm.routeCode = "";
        this.baseInfoForm.routeName = "";
      }
    },
    "baseInfoForm.startStake"() {
      if (this.baseInfoForm.startStake && this.baseInfoForm.endStake) {
        if (
          !isNaN(Number(this.baseInfoForm.startStake)) &&
          !isNaN(Number(this.baseInfoForm.endStake))
        ) {
          this.baseInfoForm.ratingLength = Math.abs(
            this.baseInfoForm.startStake - this.baseInfoForm.endStake
          );
        }
      }
    },
    "baseInfoForm.endStake"() {
      if (this.baseInfoForm.startStake && this.baseInfoForm.endStake) {
        if (
          !isNaN(Number(this.baseInfoForm.startStake)) &&
          !isNaN(Number(this.baseInfoForm.endStake))
        ) {
          this.baseInfoForm.ratingLength = Math.abs(
            this.baseInfoForm.startStake - this.baseInfoForm.endStake
          );
        }
      }
    },
  },
  computed: {
    oprUser: {
      set() {
        if (this.$refs.userCascade) {
          let userList = this.$refs.userCascade[0].getCheckedNodes();
          this.baseInfoForm.recorder = userList
            ?.map((item) => item.value)
            .join(",");
          this.baseInfoForm.recorderName = userList
            ?.map((item) => item.label)
            .join(",");
        }
      },
      get() {
        // let idListList = this.checkEntity.oprUserId.split(',')
        return this.baseInfoForm?.recorder?.split(",") ?? null;
      },
    },
  },
};
</script>

<style  scoped>
.roadbedDialog ::v-deep .el-input.is-disabled .el-input__inner {
  background-color: white;
  border-color: #dfe4ed;
  color: black;
}

.check-edit-title {
  font-weight: bold;
  font-size: 1.15rem;
  margin-left: 20px;
  padding: 10px 0;
  /*  color: #333333;*/
  /*  color: #72767b;*/
  &:before {
    content: "";
    display: inline-block;
    width: 5px;
    height: 1.5rem;
    vertical-align: bottom;
    margin-right: 0.8rem;
    background: #3797eb;
  }
}
</style>
