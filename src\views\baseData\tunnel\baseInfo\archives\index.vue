<template>
  <div class="archives" :class="oneMap?'one-map':''">
    <div class="archives-left"   :class="isBig?'isBig-left':''" :style="{ backgroundColor: oneMap ? 'rgba(1, 102, 254, 0.2)' : '' }">
      <div class="archives-left-head" :class="isBig?'isBig-head':''">
        <h5>{{ title }}</h5>
      </div>
      <div class="archives-left-body" :class="isBig?'isBig-left-body':''">
        <el-button
          v-for="item in buttonList"
          :key="item.key"
          :disabled="item.disabled"
          :class="currentButton == item.key ? 'is-active' : ''"
          @click="buttonClick(item)"
          :style="oneMap ? 'background-color:unset;color:white;' : ''"
        >
          {{item.label}}
        </el-button>
      </div>
    </div>
    <div class="archives-right" :style="{ backgroundColor: oneMap ? 'rgba(1, 102, 254, 0.1)' : '' }">
      <component
        :is="currentButton"
        :id="id"
        :tunnelCode="tunnelCode"
        :assetId="assetId"
        :assetName="assetName"
        @getBaseInfo="getBaseInfo"
      />

    </div>
  </div>
</template>

<script>
import BaseInfo from './components/baseInfo.vue'
import Periodic from './components/periodic.vue'
import Drawing from './components/drawing.vue'
import TunnelOften from './components/tunnelOften.vue'
import TunnelDaily from './components/tunnelDaily.vue'
import DailyMaintenance from './components/dailyMaintenance.vue'
import DiseaseProjInfo from './components/diseaseProjInfo.vue'
import { isBigScreen } from "@/views/map/components/common/util";

export default {
  name: 'tunnel-baseInfo-archives',
  inject: {
    oneMap: {
      default: false,
    },
  },
  props: {},
  components: { BaseInfo, Periodic, Drawing ,TunnelOften,TunnelDaily,DailyMaintenance,DiseaseProjInfo},
  data() {
    return {
      currentButton: 'BaseInfo',
      buttonList: [
        { key: 'BaseInfo', label: '基本信息', disabled: false },
        { key: 'Drawing', label: '文件图纸', disabled: false },
        { key: 'TunnelOften', label: '经常检查', disabled: false },
        { key: 'TunnelDaily', label: '日常巡查', disabled: false },
        { key: 'Periodic', label: '定期检查', disabled: false },
        { key: 'DailyMaintenance', label: '日常养护', disabled: false },
        { key: 'DiseaseProjInfo', label: '养护工程', disabled: false },
      ],
      id: '',
      title: '',
      tunnelCode: '',
      assetName:'',
      assetId:'',
      formData: {},
      isBig: isBigScreen(),
    }
  },
  created() {
    this.title = this.$route.query.tunnelName? this.$route.query.tunnelName:''
    this.id = this.$route.query.id
    this.tunnelCode = this.$route.query.tunnelCode
    this.assetId = this.$route.query.assetId||''
    this.assetName = this.$route.query.assetName||''
  },
  methods: {
    buttonClick(item) {
      this.currentButton = item.key
    },
    // 当baseInfo组件获取到数据后会调用这个方法
    getBaseInfo(data) {
      // 路由里不一定会有tunnelName，所以需要从基本信息里取
      this.title = this.title ? this.title : data.tunnelName
      this.tunnelCode = this.tunnelCode ? this.tunnelCode : data.tunnelCode
      this.assetId = this.assetId ? this.assetId : data.assetId
    },
    tabClick() {}
  },
  computed: {},
  watch: {}
}
</script>

<style lang="scss" scoped>
@import "@/assets/styles/common.scss";
.archives {
  width: 100%;
  height: 100%;
  padding: 10px;
  display: flex;
  .archives-left {
    width: 220px;
    height: 100%;
    background-color: white;
    border-radius: 10px;
    position: relative;
    font-size: 14px;
    box-shadow: 0px 0px 10px 0px rgba(0, 0, 0, 0.2);
    &.isBig-left{
        width:vwpx(440px);
     }
    .archives-left-head {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 52px;
      background: #409eff;
      border-radius: 10px 10px 0 0;
      display: flex;
      justify-content: center;
      align-items: center;
      h5 {
        font-family: Microsoft YaHei, Microsoft YaHei;
        font-weight: 700;
        color: #ffffff;
      }
      &.isBig-head{
        height: vwpx(104px);
        font-size: vwpx(28px);
      }
    }
    .archives-left-body {
      position: absolute;
      bottom: 0;
      left: 0;
      width: 100%;
      height: calc(100% - 52px);
      padding: 15px 10px;
      display: flex;
      flex-direction: column;
      &.isBig-left-body{
       
       height: calc(100% -  4.8148148148vmin );
       padding: vwpx(30px) vwpx(20px);

       ::v-deep  .el-button--mini{
         font-size: vwpx(28px);
       }
     }
      .el-button {
        width: 100%;
        margin-left: 0;
        margin-bottom: 8px;
        background: #f7f8fa;
        // border: 0;
        font-family: Microsoft YaHei, Microsoft YaHei;
        font-weight: 400;
        color: #333;
      }
      .el-button.is-disabled {
        color: #c0c4cc;
        cursor: not-allowed;
      }
      .is-active {
        background: #eff5ff;
        color: #409eff;
        border-color: #409eff;
      }
    }
  }
  .archives-right {
    width: calc(100% - 220px);
    height: 100%;
    padding-left: 10px;
    border-radius: 10px;
  }
}
</style>
<style lang="scss">
</style>