<template>
  <div class="app-container">
    <el-row :gutter="20">

      <!--筛选区开始-->
      <el-col :span="24" :xs="24">
        <el-row>
          <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" label-width="68px">
            <el-col :span="24">
              <el-form-item label="" prop="managementOfficeId">
                <CascadeSelection
                  ref="CascadeSelection"
                  style="min-width: 192px"
                  :form-data.sync="queryParams"
                  types="201"
                />
              </el-form-item>
              <!--            <el-form-item label="" prop="managementOfficeId">-->
              <!--              <el-input-->
              <!--                v-model="queryParams.managementOfficeId"-->
              <!--                placeholder="请输入管理处id"-->
              <!--                clearable-->
              <!--                prefix-icon="el-icon-user"-->
              <!--                style="width: 240px"-->
              <!--                @keyup.enter.native="handleQuery"-->
              <!--              />-->
              <!--            </el-form-item>-->
              <!--            <el-form-item label="" prop="maintenanceSectionId">-->
              <!--              <el-input-->
              <!--                v-model="queryParams.maintenanceSectionId"-->
              <!--                placeholder="请输入路段id"-->
              <!--                clearable-->
              <!--                prefix-icon="el-icon-user"-->
              <!--                style="width: 240px"-->
              <!--                @keyup.enter.native="handleQuery"-->
              <!--              />-->
              <!--            </el-form-item>-->

              <!--            <el-form-item label="" prop="roadNumber">-->
              <!--              <el-input-->
              <!--                v-model="queryParams.roadNumber"-->
              <!--                placeholder="请输入公路编号"-->
              <!--                clearable-->
              <!--                prefix-icon="el-icon-user"-->
              <!--                style="width: 240px"-->
              <!--                @keyup.enter.native="handleQuery"-->
              <!--              />-->
              <!--            </el-form-item>-->


              <el-form-item label="" prop="disasterType">
                <el-input
                  v-model="queryParams.disasterType"
                  placeholder="请输入灾害类型"
                  clearable
                  prefix-icon="el-icon-user"
                  style="width: 240px"
                  @keyup.enter.native="handleQuery"
                />
              </el-form-item>



              <el-form-item>
                <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
                <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
                <el-button v-show="!showSearch" @click="showSearch=true" icon="el-icon-arrow-down" circle></el-button>
                <el-button v-show="showSearch" @click="showSearch=false" icon="el-icon-arrow-up" circle></el-button>
              </el-form-item>

            </el-col>
            <!--默认折叠 ,此处仅作为示例-->
            <el-form-item v-show="showSearch" label="" prop="riskName">
              <el-input
                v-model="queryParams.riskName"
                placeholder="请输入风险名称"
                clearable
                prefix-icon="el-icon-user"
                style="width: 240px"
                @keyup.enter.native="handleQuery"
              />
            </el-form-item>

            <el-form-item v-show="showSearch" label="" prop="inspectionTime">
              <el-date-picker clearable
                              v-model="queryParams.inspectionTime"
                              type="date"
                              style="width: 240px"
                              value-format="yyyy-MM-dd"
                              placeholder="请选择巡查时间">
              </el-date-picker>
            </el-form-item>
            <el-form-item v-show="showSearch" label="" prop="stage">

              <el-select v-model="queryParams.stage" placeholder="阶段" clearable collapse-tags
                         style="width: 240px">
                <el-option v-for="dict in auditStatusOptions" :key="dict.dictValue" :label="dict.dictLabel"
                           :value="dict.dictValue"/>
              </el-select>
              <!--                <el-input-->
              <!--                  v-model="queryParams.stage"-->
              <!--                  placeholder="请选择阶段"-->
              <!--                  clearable-->
              <!--                  prefix-icon="el-icon-user"-->
              <!--                  style="width: 240px"-->
              <!--                  @keyup.enter.native="handleQuery"-->
              <!--                />-->
            </el-form-item>

            <el-form-item v-show="showSearch" label="" prop="hazardLevel">

              <el-select v-model="queryParams.hazardLevel" placeholder="隐患等级" clearable collapse-tags
                         style="width: 240px">
                <el-option v-for="dict in hazardLevelOptions" :key="dict.dictValue" :label="dict.dictLabel"
                           :value="dict.dictValue"/>
              </el-select>
              <!--                <el-input-->
              <!--                  v-model="queryParams.hazardLevel"-->
              <!--                  placeholder="请选择隐患等级"-->
              <!--                  clearable-->
              <!--                  prefix-icon="el-icon-user"-->
              <!--                  style="width: 240px"-->
              <!--                  @keyup.enter.native="handleQuery"-->
              <!--                />-->
            </el-form-item>


            <el-form-item label="" prop="sourceType"  v-show="showSearch">
              <el-select
                v-model="queryParams.sourceType"
                placeholder="是否一张图清单"
                clearable
                style="width: 200px"
              >
                <el-option label="是" value="是"/>
                <el-option label="否" value="否"/>
              </el-select>
            </el-form-item>

          </el-form>
        </el-row>
        <!--筛选区结束-->


        <!--操作按钮区开始-->
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button
              type="primary"
              icon="el-icon-plus"
              size="mini"
              @click="handleAdd"
              v-hasPermi="['middleData:xqinspection:add']"
            >新增
            </el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button
              type="success"
              icon="el-icon-edit"
              size="mini"
              :disabled="single"
              @click="handleUpdate"
              v-hasPermi="['middleData:xqinspection:edit']"
            >修改
            </el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button
              type="danger"
              icon="el-icon-delete"
              size="mini"
              :disabled="multiple"
              @click="handleDelete"
              v-hasPermi="['middleData:xqinspection:remove']"
            >删除
            </el-button>
          </el-col>
          <!--          <el-col :span="1.5">-->
          <!--            <el-button-->
          <!--              type="info"-->
          <!--              plain-->
          <!--              icon="el-icon-upload2"-->
          <!--              size="mini"-->
          <!--              @click="handleImport"-->
          <!--              v-hasPermi="['middleData:xqinspection:export']"-->
          <!--            >导入-->
          <!--            </el-button>-->
          <!--          </el-col>-->
          <el-col :span="1.5">
            <el-button
              type="warning"
              icon="el-icon-download"
              size="mini"
              @click="handleExport"
              v-hasPermi="['middleData:xqinspection:export']"
            >导出清单
            </el-button>
          </el-col>
          <right-toolbar :showSearch.sync="showSearch" @queryTable="getList" :columns="columns"></right-toolbar>
        </el-row>
        <!--操作按钮区结束-->

        <!--数据表格开始-->
        <div class="tableDiv">
          <el-table
            ref="table"
            size="mini"
            :height="showSearch ? 'calc(100vh - 320px)' : 'calc(100vh - 260px)'"
            style="width: 100%"
            v-loading="loading"
            border
            :data="xqinspectionList"
            @selection-change="handleSelectionChange"
            :row-style="rowStyle"
            @row-click="handleRowClick"
          >
            <el-table-column type="selection" width="50" align="center"/>
            <el-table-column  label="序号" type="index" width="50">
              <template v-slot="scope">
                {{ (scope.$index + (queryParams.pageNum - 1) * queryParams.pageSize) + 1 }}
              </template>
            </el-table-column>
            <el-table-column label="灾害类型" align="center" prop="disasterType"/>
            <!--            <el-table-column label="外键" align="center" prop="riskId"/>-->
            <el-table-column label="风险名称" min-width="140" align="center" prop="riskName"/>
            <!--                <el-table-column label="巡查经度" align="center" prop="longitude" />-->
            <!--                <el-table-column label="巡查纬度" align="center" prop="latitude" />-->
            <el-table-column label="管理处名称" align="center" prop="managementOffice"/>
            <el-table-column label="养护路段" align="center" prop="maintenanceSection"/>
            <el-table-column label="公路编号" align="center" prop="roadNumber"/>
            <el-table-column label="隐患等级" align="center" prop="hazardLevel">
              <template slot-scope="scope">
                <el-tag size="mini" type="success">
                  {{hazardLevelOptions.filter(i => i.dictValue == scope.row.hazardLevel)[0].dictLabel}}
<!--                  {{ getTempDictLable(hazardLevelOptions, scope.row.hazardLevel) }}-->
                </el-tag>

              </template>

            </el-table-column>
            <el-table-column label="是否一张图清单" align="center" prop="sourceType"/>

            <el-table-column label="巡查时间" align="center" prop="inspectionTime" width="180">
              <template v-slot="scope">
                <span>{{ parseTime(scope.row.inspectionTime, '{y}-{m}-{d}') }}</span>
              </template>
            </el-table-column>
            <!--            <el-table-column label="巡查照片" align="center" prop="photos"/>-->
            <el-table-column label="巡查备注" show-overflow-tooltip align="center" prop="remark"/>
            <el-table-column label="阶段" align="center" prop="stage">
              <template slot-scope="scope">

                <el-tag size="mini"> {{ getTempDictLable(auditStatusOptions, scope.row.stage) }}</el-tag>

              </template>

            </el-table-column>
            <el-table-column
              label="操作"
              fixed="right"
              align="center"
              width="220"
              class-name="small-padding fixed-width"
            >
              <template slot-scope="scope" v-if="scope.row.userId !== 1">

<!--                <el-button-->
<!--                  size="mini"-->
<!--                  type="text"-->
<!--                  icon="el-icon-download"-->
<!--                  @click="handleReport(scope.row)"-->
<!--                >导出-->
<!--                </el-button>-->
                <el-button
                  size="mini"
                  type="text"
                  icon="el-icon-view"
                  @click="handleView(scope.row)"
                >查看
                </el-button>

<!--                <el-button-->
<!--                  size="mini"-->
<!--                  type="text"-->
<!--                  icon="el-icon-view"-->
<!--                  @click="handleView(scope.row)"-->
<!--                  v-hasPermi="['middleData:xqinspection:view']"-->
<!--                >查看-->
<!--                </el-button>-->

                <el-button
                  size="mini"
                  type="text"
                  icon="el-icon-edit"
                  @click="handleUpdate(scope.row)"
                  v-hasPermi="['middleData:xqinspection:edit']"
                >修改
                </el-button>
                <el-button
                  size="mini"
                  type="text"
                  icon="el-icon-delete"
                  @click="handleDelete(scope.row)"
                  v-hasPermi="['middleData:xqinspection:remove']"
                >删除
                </el-button>

                <el-button
                  v-if="scope.row.stage == 2"
                  size="mini"
                  type="text"
                  icon="el-icon-document"
                  @click="handleAudit(scope.row)"
                  v-hasPermi="['middleData:xqinspection:audit']"
                >审核
                </el-button>
              </template>
            </el-table-column>
          </el-table>

          <pagination
            v-show="total>0"
            :total="total"
            :page.sync="queryParams.pageNum"
            :limit.sync="queryParams.pageSize"
            @pagination="getList"
          />
        </div>
        <!--数据表格结束-->
      </el-col>
    </el-row>


    <!-- 添加或修改汛期巡查记录对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="75%" append-to-body class="asset-check-edit-dialog">
      <div :onlyRead="mode === 'view'">
        <div class="check-edit-title">隐患信息</div>
        <el-form ref="form" :model="form" :rules="rules" label-width="100px">


          <!--        <el-col :span="24">-->

          <!--          <el-button @click="$refs.select.show()" type="primary" size="mini" style="margin: 0 10px">-->
          <!--            选择隐患-->
          <!--          </el-button>-->
          <!--        </el-col>-->

          <el-col :span="12">
            <el-form-item label="隐患名称" prop="riskName" :onlyRead="mode === 'edit'">
              <el-input v-model="form.riskName" placeholder="点击选择隐患" readonly
                        @click.native="$refs.select.show()"/>
            </el-form-item>
          </el-col>

          <el-col :span="12">
            <el-form-item label="管理处名称" prop="managementOffice">
              <el-input v-model="form.managementOffice" placeholder="请输入管理处名称" readonly/>
            </el-form-item>
          </el-col>


          <el-col :span="12">
            <el-form-item label="养护路段" prop="maintenanceSection">
              <el-input v-model="form.maintenanceSection" placeholder="请输入养护路段" readonly/>
            </el-form-item>
          </el-col>


          <el-col :span="12">
            <el-form-item label="公路编号" prop="roadNumber">
              <el-input v-model="form.roadNumber" placeholder="请输入公路编号" readonly/>
            </el-form-item>
          </el-col>


          <el-col :span="12">
            <el-form-item label="隐患等级" prop="hazardLevel">
              <!--            <el-input v-model="form.hazardLevel"  : placeholder="请输入隐患等级" readonly/>-->
              <el-input :value="getTempDictLable(hazardLevelOptions, form.hazardLevel)" placeholder="请输入隐患等级"
                        readonly/>
            </el-form-item>
          </el-col>

          <el-col :span="12">
            <el-form-item label="灾害类型" prop="disasterType">
              <el-input v-model="form.disasterType" placeholder="请输入灾害类型" readonly/>
            </el-form-item>
          </el-col>


          <el-col :span="24" v-if="mode === 'add'" >
            <el-form-item label="隐患描述" prop="hazardDescription">
              <el-input v-model="form.hazardDescription" type="textarea" placeholder="隐患描述"/>
            </el-form-item>
          </el-col>
          <!--        <el-col :span="12">-->
          <!--          <el-form-item label="阶段" prop="stage">-->
          <!--            <el-input v-model="form.stage"-->
          <!--                      placeholder="请输入阶段"/>-->
          <!--          </el-form-item>-->
          <!--        </el-col>-->
        </el-form>
      </div>
        <div class="check-edit-title" style="margin-top: 20px">巡查信息</div>

        <el-form ref="form" :model="form" :rules="rules" label-width="100px">
          <el-col :span="12" :onlyRead="mode === 'view'">
            <el-form-item label="巡查经度" prop="longitude">

              <el-input v-model="form.longitude" placeholder="请输入巡查经度">
                <el-button slot="append" icon="el-icon-location" @click="showCoordinatePicker('longitude')">坐标拾取
                </el-button>
              </el-input>
            </el-form-item>
          </el-col>


          <el-col :span="12" :onlyRead="mode === 'view'">
            <el-form-item label="巡查纬度" prop="latitude">

              <el-input v-model="form.latitude" placeholder="请输入巡查纬度">
                <el-button slot="append" icon="el-icon-location" @click="showCoordinatePicker('latitude')">坐标拾取
                </el-button>
              </el-input>
            </el-form-item>
          </el-col>


          <el-col :span="24" :onlyRead="mode === 'view'">
            <el-form-item label="巡查时间" prop="inspectionTime">
              <el-date-picker clearable
                              style="width: 100%"
                              v-model="form.inspectionTime"
                              type="date"
                              value-format="yyyy-MM-dd"
                              placeholder="请选择巡查时间">
              </el-date-picker>
            </el-form-item>
          </el-col>

          <el-col :span="24" :onlyRead="mode === 'view'">
            <el-form-item label="巡查人员" prop="inspectionTime">

              <el-cascader
                ref="userCascade"
                v-model="oprUser"
                :options="deptUserOptions"
                :props="{ multiple: true, value: 'id', emitPath: false }"
                :show-all-levels="false"
                filterable
                clearable
                style="width: 100%"
              />


            </el-form-item>
          </el-col>


          <el-col :span="24" :onlyRead="mode === 'view'">
            <el-form-item label="巡查备注" prop="remark">
              <el-input v-model="form.remark" type="textarea" placeholder="请输入内容"/>
            </el-form-item>
          </el-col>


          <el-col :span="24">
            <el-form-item label="巡查照片" prop="photos">

              <image-upload v-model="form.photos"  :disabled="mode === 'view'" />
              <!--            <el-input v-model="form.photos" type="textarea" placeholder="请输入内容"/>-->
            </el-form-item>
          </el-col>
        </el-form>

        <div :onlyRead="mode === 'view'">
        <br>
        <div class="check-edit-title" style="margin-bottom: 20px">巡查详情</div>
        <el-empty v-if="Object.keys(itemObject).length === 0" description="暂无巡查项"></el-empty>
        <template v-for="(value, key, index) in itemObject">
          <div class="infoBox">
            <div class="infoTitle">{{ key }}</div>
            <el-form label-width="180px">
              <el-form-item v-for="i of itemObject[key]" :label="i.name">
                <el-input
                  v-model="i.value"
                  :placeholder="`请输入${i.name}`"
                  clearable
                />
              </el-form-item>
            </el-form>
          </div>
        </template>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button v-if="mode != 'view'" type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>

    </el-dialog>



    <!-- 审核对话框 -->
    <el-dialog :title="title" :visible.sync="open1" width="75%" append-to-body class="asset-check-edit-dialog1" v-dialog-drag   >
      <div :onlyRead="mode === 'view'">
        <div class="check-edit-title">隐患信息</div>
        <el-form ref="form" :model="form" :rules="rules" label-width="100px">
          <el-col :span="12">
            <el-form-item label="隐患名称" prop="riskName" :onlyRead="mode === 'edit'">
              <el-input v-model="form.riskName" placeholder="点击选择隐患" readonly
                        @click.native="$refs.select.show()"/>
            </el-form-item>
          </el-col>

          <el-col :span="12">
            <el-form-item label="管理处名称" prop="managementOffice">
              <el-input v-model="form.managementOffice" placeholder="请输入管理处名称" readonly/>
            </el-form-item>
          </el-col>


          <el-col :span="12">
            <el-form-item label="养护路段" prop="maintenanceSection">
              <el-input v-model="form.maintenanceSection" placeholder="请输入养护路段" readonly/>
            </el-form-item>
          </el-col>


          <el-col :span="12">
            <el-form-item label="公路编号" prop="roadNumber">
              <el-input v-model="form.roadNumber" placeholder="请输入公路编号" readonly/>
            </el-form-item>
          </el-col>


          <el-col :span="12">
            <el-form-item label="隐患等级" prop="hazardLevel">
              <!--            <el-input v-model="form.hazardLevel"  : placeholder="请输入隐患等级" readonly/>-->
              <el-input :value="getTempDictLable(hazardLevelOptions, form.hazardLevel)" placeholder="请输入隐患等级"
                        readonly/>
            </el-form-item>
          </el-col>

          <el-col :span="12">
            <el-form-item label="灾害类型" prop="disasterType">
              <el-input v-model="form.disasterType" placeholder="请输入灾害类型" readonly/>
            </el-form-item>
          </el-col>

          <!--        <el-col :span="12">-->
          <!--          <el-form-item label="阶段" prop="stage">-->
          <!--            <el-input v-model="form.stage"-->
          <!--                      placeholder="请输入阶段"/>-->
          <!--          </el-form-item>-->
          <!--        </el-col>-->
        </el-form>

        <div class="check-edit-title" style="margin-top: 20px">巡查信息</div>

        <el-form ref="form" :model="form" :rules="rules" label-width="100px">
          <el-col :span="12">
            <el-form-item label="巡查经度" prop="longitude">

              <el-input v-model="form.longitude" placeholder="请输入巡查经度">
                <el-button slot="append" icon="el-icon-location" @click="showCoordinatePicker('longitude')">坐标拾取
                </el-button>
              </el-input>
            </el-form-item>
          </el-col>


          <el-col :span="12">
            <el-form-item label="巡查纬度" prop="latitude">

              <el-input v-model="form.latitude" placeholder="请输入巡查纬度">
                <el-button slot="append" icon="el-icon-location" @click="showCoordinatePicker('latitude')">坐标拾取
                </el-button>
              </el-input>
            </el-form-item>
          </el-col>


          <el-col :span="24">
            <el-form-item label="巡查时间" prop="inspectionTime">
              <el-date-picker clearable
                              style="width: 100%"
                              v-model="form.inspectionTime"
                              type="date"
                              value-format="yyyy-MM-dd"
                              placeholder="请选择巡查时间">
              </el-date-picker>
            </el-form-item>
          </el-col>

          <el-col :span="24">
            <el-form-item label="巡查人员" prop="inspectionTime">

              <el-cascader
                ref="userCascade"
                v-model="oprUser"
                :options="deptUserOptions"
                :props="{ multiple: true, value: 'id', emitPath: false }"
                :show-all-levels="false"
                filterable
                clearable
                style="width: 100%"
              />


            </el-form-item>
          </el-col>


          <el-col :span="24">
            <el-form-item label="巡查备注" prop="remark">
              <el-input v-model="form.remark" type="textarea" placeholder="请输入内容"/>
            </el-form-item>
          </el-col>


          <el-col :span="24">
            <el-form-item label="巡查照片" prop="photos">

              <image-upload v-model="form.photos" :limit="1"/>
              <!--            <el-input v-model="form.photos" type="textarea" placeholder="请输入内容"/>-->
            </el-form-item>
          </el-col>
        </el-form>
        <br>
        <div class="check-edit-title" style="margin-bottom: 20px">巡查详情</div>
        <el-empty v-if="Object.keys(itemObject).length === 0" description="暂无巡查项"></el-empty>
        <template v-for="(value, key, index) in itemObject">
          <div class="infoBox">
            <div class="infoTitle">{{ key }}</div>
            <el-form label-width="180px">
              <el-form-item v-for="i of itemObject[key]" :label="i.name">
                <el-input
                  v-model="i.value"
                  :placeholder="`请输入${i.name}`"
                  clearable
                />
              </el-form-item>
            </el-form>
          </div>
        </template>


      </div>
<!--      <div slot="footer" class="dialog-footer" style="padding: 56px; border-radius: 10px; margin-top: 30px; background: #ccc;">-->
      <div slot="footer" class="dialog-footer">

        <el-form ref="form" :model="form" label-width="80px" style="width: 25vw">
          <el-form-item label="审核结果">
            <el-radio-group v-model="form.stage">
              <el-radio :label="4">通过</el-radio>
              <el-radio :label="5">不通过</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="审核意见">
            <el-input type="textarea" :rows="6" v-model="form.auditContent"></el-input>
          </el-form-item>
<!--          <el-form-item>-->
<!--            <el-button v-if="mode != 'view'" type="primary" @click="submitForm">确 定</el-button>-->
<!--            <el-button @click="cancel">取 消</el-button>-->
<!--          </el-form-item>-->
        </el-form>
        <el-button type="primary" @click="auditSubmit">确 定</el-button>
        <el-button @click="open1 = false">取 消</el-button>
      </div>
    </el-dialog>


    <!-- 用户导入对话框 -->
    <el-dialog :title="upload.title" :visible.sync="upload.open" width="400px" append-to-body>
      <el-upload
        ref="upload"
        :limit="1"
        accept=".xlsx, .xls"
        :headers="upload.headers"
        :action="upload.url + '?updateSupport=' + upload.updateSupport"
        :disabled="upload.isUploading"
        :on-progress="handleFileUploadProgress"
        :on-success="handleFileSuccess"
        :auto-upload="false"
        drag
      >
        <i class="el-icon-upload"></i>
        <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
        <div class="el-upload__tip text-center" slot="tip">
          <div class="el-upload__tip" slot="tip">
            <el-checkbox v-model="upload.updateSupport"/>
            是否更新已经存在的用户数据
          </div>
          <span>仅允许导入xls、xlsx格式文件。</span>
          <el-link type="primary" :underline="false" style="font-size:12px;vertical-align: baseline;"
                   @click="importTemplate">下载模板
          </el-link>
        </div>
      </el-upload>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitFileForm">确定审核</el-button>
        <el-button @click="upload.open = false">取 消</el-button>
      </div>
    </el-dialog>
    <!-- 坐标拾取组件 -->
    <coordinate-picker
      :visible.sync="coordinatePickerVisible"
      :initialPosition="coordinatePickerInitPosition"
      @save="handleCoordinateSave"
    />

    <HazardsTableDialog ref="select" @change="onChange"></HazardsTableDialog>
  </div>
</template>

<script>
import {
  listXqinspection,
  getXqinspection,
  delXqinspection,
  addXqinspection,
  updateXqinspection
} from "@/api/middleData/xqinspection";
import {getToken} from "@/utils/auth";

import Treeselect from "@riophae/vue-treeselect";
import "@riophae/vue-treeselect/dist/vue-treeselect.css";

import HazardsTableDialog from "@/views/middleData/xqinspection/hazardsTableDialog.vue";
import {delXqItemsetting, listAllXqItemsetting} from "@/api/middleData/xqItemsetting";

import _ from 'lodash';
import CoordinatePicker from "@/components/CoordinatePicker/index.vue";
import {getTreeStruct} from "@/api/tmpl";
import moment from "moment/moment";
import ImageUpload from "@/views/patrol/diseases/ImageUpload.vue";
// import ImagePreview from "./TempImagePreview.vue";
import ImagePreview from "@/views/patrol/diseases/ImagePreview.vue";
import Vue from "vue";
import CascadeSelection from "@/views/middleData/xqinspection/CascadeSelectionManagementOffice.vue";

export default {
  name: "Xqinspection",
  components: {CascadeSelection, CoordinatePicker, HazardsTableDialog, ImageUpload, ImagePreview},
  data() {
    return {
      riskData: {},
      userList: [],
      itemList: [],
      itemObject: {},
      // 部门-用户树选项
      deptUserOptions: [],
      mode: null,
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: false,
      dictType: [],
      // 日期范围
      dateRange: [],
      // 总条数
      total: 0,
      // 汛期巡查记录表格数据
      xqinspectionList: null,
      // 弹出层标题
      title: "",
      // 部门树选项
      deptOptions: undefined,
      // 是否显示弹出层
      open: false,
      open1: false,
      hazardLevelOptions: [
        {dictLabel: '重大隐患', dictValue: 1},
        {dictLabel: '较大隐患', dictValue: 2},
        {dictLabel: '一般隐患', dictValue: 3},
        {dictLabel: '无', dictValue: 0},
      ],
      // 审核状态字典
      auditStatusOptions: [
        {dictLabel: '待审核', dictValue: 2},
        {dictLabel: '审核通过', dictValue: 4},
        {dictLabel: '审核不通过', dictValue: 5},
      ],
      // 表单参数
      form: {},
      defaultProps: {
        children: "children",
        label: "label"
      },
      coordinatePickerVisible: false, // 坐标拾取对话框可见性
      coordinatePickerInitPosition: "102.8207599,24.8885797", // 初始坐标
      currentCoordinateType: '', // 当前坐标类型，longitude表示经度，latitude表示纬度
      // 用户导入参数
      upload: {
        // 是否显示弹出层（用户导入）
        open: false,
        // 弹出层标题（用户导入）
        title: "",
        // 是否禁用上传
        isUploading: false,
        // 是否更新已经存在的用户数据
        updateSupport: 0,
        // 设置上传的请求头部
        headers: {Authorization: "Bearer " + getToken()},
        // 上传的地址
        url: process.env.VUE_APP_BASE_API + "/system/user/importData"
      },
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 50,
        disasterType: null,
        riskName: null,
        inspectionTime: null,
        stage: null,
        roadNumber: null,
        hazardLevel: null,
        managementOfficeId: null,
        maintenanceSectionId: null,
        sourceType: null
      },
      // 列信息
      columns: [
        {key: 0, label: `灾害类型（桥、隧、边坡）`, visible: true},
        {key: 1, label: `关联的风险点ID，外键`, visible: true},
        {key: 2, label: `风险名称（桥梁、隧道）`, visible: true},
        {key: 3, label: `巡查经度`, visible: true},
        {key: 4, label: `巡查纬度`, visible: true},
        {key: 5, label: `巡查照片`, visible: true},
        {key: 6, label: `巡查备注`, visible: true},
        {key: 7, label: `巡查时间`, visible: true},
        {key: 8, label: `巡查频率（天）`, visible: true},
        {key: 9, label: `阶段(0:未巡查;1:待上报;2:待审核;3:退回;4:审核通过;5:审核未通过)`, visible: true},
        {key: 10, label: `到期时间`, visible: true},
        {key: 11, label: `删除标识(TRUE: 删除,FALSE: 未删除)`, visible: true},
        {key: 12, label: `管理处名称（公共字段）`, visible: true},
        {key: 13, label: `养护路段（公共字段）`, visible: true},
        {key: 14, label: `公路编号（公共字段）`, visible: true},
        {key: 15, label: `隐患等级(1.重大隐患 2.较大隐患 3.一般隐患)（公共字段）`, visible: true},
        {key: 16, label: `管理处id（公共字段）`, visible: true},
        {key: 17, label: `路段id（公共字段）`, visible: true}
      ],
      // 表单校验
      rules: {
        riskId: [
          {required: true, message: "关联的风险点ID，外键不能为空", trigger: "blur"}
        ],


      }
    };
  },
  watch: {
    itemList: {
      deep: true,
      immediate: true,
      handler() {

        console.log('dddd')

      }
    }

  },
  computed: {
    oprUser: {
      set() {
        let userList = this.$refs.userCascade?.getCheckedNodes() || [];
        // this.checkEntity.oprUserId = userList.map((item) => item.value).join(',');
        // this.checkEntity.oprUserName = userList.map((item) => item.label).join(',');

        this.userList = userList.map(item => ({userId: item.value, userName: item.label}))

        console.log(this.userList)

      },
      get() {
        // 只在新增时设置默认记录人
        // if (!this.disabled && !this.checkEntity.oprUserId) {
        //   this.checkEntity.checkTime = moment().format('YYYY-MM-DD');
        //   this.checkEntity.oprUserId = this.$store.state.user.id.toString();
        //   this.checkEntity.oprUserName = this.$store.state.user.nickName;
        // }
        // // 如果 oprUserId 是数字,转为字符串
        // if (typeof this.checkEntity?.oprUserId === 'number') {
        //   return [this.checkEntity.oprUserId.toString()];
        // }
        // // 如果是字符串且包含逗号,则分割
        // if (typeof this.checkEntity?.oprUserId === 'string' && this.checkEntity.oprUserId.includes(',')) {
        //   return this.checkEntity.oprUserId.split(',');
        // }
        // // 如果是字符串但不包含逗号,直接返回数组
        // if (typeof this.checkEntity?.oprUserId === 'string') {
        //   return [this.checkEntity.oprUserId];
        // }
        if (this.userList) {
          return this.userList.map(item => item.userId)
        }
        // 默认返回空数组
        return [];
      },
    },
  },
  created() {
    this.getList();
    // this.getDeptTree();
    // this.getConfigKey("sys.user.initPassword").then(response => {
    //   this.initPassword = response.msg;
    // });

    /** 查询部门-用户下拉树结构 */
    getTreeStruct({types: 111}).then((response) => {
      this.deptUserOptions = response.data;
    });
  },
  methods: {
    /** 查询用户列表 */
    getList() {
      this.loading = true;
      // this.queryParams.createTimee = this.dateRange[0]
      // this.queryParams.createTimes = this.dateRange[1]
      listXqinspection(this.queryParams).then(response => {
        this.xqinspectionList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        disasterType: null,
        riskId: null,
        riskName: null,
        longitude: null,
        latitude: null,
        photos: null,
        remark: null,
        inspectionTime: null,
        frequency: null,
        stage: null,
        expiry: null,
        delFlag: null,
        managementOffice: null,
        maintenanceSection: null,
        roadNumber: null,
        hazardLevel: null,
        managementOfficeId: null,
        maintenanceSectionId: null,
        hazardDescription: null,
        sourceType: null

      };
      this.resetForm("form");

      this.riskData = {}
      this.userList = []
      this.itemList = []
      this.itemObject = {}
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dateRange = [];
      this.resetForm("queryForm");
      this.queryParams.maintenanceSectionId = null
      this.queryParams.roadNumber = null
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id);
      this.single = selection.length != 1;
      this.multiple = !selection.length;
    },
    // 表格点击勾选
    handleRowClick(row) {
      row.isSelected = !row.isSelected;
      this.$refs.table.toggleRowSelection(row);
    },
    // 勾选高亮
    rowStyle({row, rowIndex}) {
      if (this.ids.includes(row.id)) {
        return {'background-color': '#E1F0FF', color: '#333'}
      } else {
        return {'background-color': '#fff', color: '#333'}
      }
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加汛期巡查记录";
      this.mode = "add"
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids;
      getXqinspection(id).then(response => {
        this.form = response.data;
        this.userList = response.data.disasterXqInspectionUserList
        this.itemList = response.data.disasterXqInspectionItemList
        this.itemObject = _.groupBy(this.itemList, 'patrolType')
        this.open = true;
        this.title = "修改汛期巡查记录";
      });
      this.mode = "edit"
    },

    handleView(row) {
      this.mode = "view"
      this.reset();
      const id = row.id || this.ids;
      getXqinspection(id).then(response => {
        this.form = response.data;
        this.userList = response.data.disasterXqInspectionUserList
        this.itemList = response.data.disasterXqInspectionItemList
        this.itemObject = _.groupBy(this.itemList, 'patrolType')
        this.open = true;
        this.title = "查看汛期巡查记录";
      });

    },

    handleReport(row) {
      this.download('middleData/xqinspection/report', {
          ...this.queryParams,
          ids: this.ids
        }, `汛期巡查记录${new Date().getTime()}.xlsx`,
        {
          parameterType: 'body', // 设置参数类型为 body
          headers: {
            'Content-Type': 'application/json', // 设置请求头为 JSON
          },
        }

      )

    },

    handleAudit(row) {
      this.reset();
      const id = row.id || this.ids;
      getXqinspection(id).then(response => {
        this.form = response.data;
        this.userList = response.data.disasterXqInspectionUserList
        this.itemList = response.data.disasterXqInspectionItemList
        this.itemObject = _.groupBy(this.itemList, 'patrolType')


        this.form.stage = 4

        this.form.auditId =  this.$store.state.user.id.toString();
        this.form.auditName = this.$store.state.user.nickName;
        this.form.auditTime = moment().format('YYYY-MM-DD');
        this.open1 = true;
        this.title = "审核汛期巡查记录";
      });
      this.mode = "view"
    },

    auditSubmit(){
      /** 提交按钮 */
        // this.form.disasterXqInspectionUserList = this.userList
        // this.form.disasterXqInspectionItemList = this.itemList

      delete this.form.disasterXqInspectionUserList
      delete this.form.disasterXqInspectionItemList
      updateXqinspection(this.form).then(response => {
        this.$modal.msgSuccess("审核成功");
        this.open1 = false;
        this.getList();
      });
    },
    /** 提交按钮 */
    submitForm: function () {

      console.log(this.form)
      console.log(this.userList)
      console.log(this.itemList)

      this.form.disasterXqInspectionUserList = this.userList
      this.form.disasterXqInspectionItemList = this.itemList

      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.id != null) {
            updateXqinspection(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addXqinspection(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const id = row.id || this.ids;
      this.$modal.confirm('是否确认删除汛期巡查记录编号为"' + id + '"的数据项？').then(() => {
        let array = []
        if (this.ids.length > 0){
          this.ids.forEach(item => {
            array.push(delXqinspection(item))
          })
        }
        return Promise.all(array)

      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {
      });
    },



    /** 导出按钮操作 */
    handleExport() {

      this.download('middleData/xqinspection/export', {
        ...this.queryParams,
        ids: this.ids
      }, `汛期巡查记录${new Date().getTime()}.xlsx`,
        {
          parameterType: 'body', // 设置参数类型为 body
          headers: {
            'Content-Type': 'application/json', // 设置请求头为 JSON
          },
        }

      )

    },
    /** 导入按钮操作 */
    handleImport() {
      this.upload.title = "用户导入";
      this.upload.open = true;
    },
    /** 下载模板操作 */
    importTemplate() {
      this.download('system/user/importTemplate', {}, `user_template.xlsx`)
    },
    // 文件上传中处理
    handleFileUploadProgress(event, file, fileList) {
      this.upload.isUploading = true;
    },
    // 文件上传成功处理
    handleFileSuccess(response, file, fileList) {
      this.upload.open = false;
      this.upload.isUploading = false;
      this.$refs.upload.clearFiles();
      this.$alert("<div style='overflow: auto;overflow-x: hidden;max-height: 70vh;padding: 10px 20px 0;'>" + response.msg + "</div>", "导入结果", {dangerouslyUseHTMLString: true});
      this.getList();
    },
    // 提交上传文件
    submitFileForm() {
      this.$refs.upload.submit();
    },
    onChange(data) {
      this.riskData = data
      // this.form = {...this.riskData}
      // this.form.riskId = this.form.id
      // delete  this.form.id

      this.form.disasterType = this.riskData.hazardType
      this.form.hazardCategory = this.riskData.hazardCategory
      this.form.riskId = this.riskData.id
      this.form.riskName = this.riskData.tempName
      this.form.longitude = undefined
      this.form.latitude = undefined
      this.form.photos = undefined
      this.form.stake = this.riskData.centerStake ? this.riskData.centerStake : this.riskData.startStake
      this.form.hazardDescription = this.riskData.hazardDescription
      this.form.sourceType = this.riskData.sourceType

      const now = new Date();
      const formatted = `${now.getFullYear()}-${
        (now.getMonth() + 1).toString().padStart(2, '0') // 补零至两位数
      }-${now.getDate().toString().padStart(2, '0')}`;
      console.log(formatted);

      this.form.inspectionTime = formatted
      this.form.stage = 2
      this.form.managementOffice = this.riskData.managementOffice
      this.form.maintenanceSection = this.riskData.maintenanceSection
      this.form.roadNumber = this.riskData.roadNumber
      this.form.hazardLevel = this.riskData.hazardLevel
      this.form.managementOfficeId = this.riskData.managementOfficeId
      this.form.maintenanceSectionId = this.riskData.maintenanceSectionId


      this.getItem()
    },
    getItem() {
      listAllXqItemsetting({riskType: this.riskData.hazardType}).then(res => {
        this.itemList = res.data.map(item => {
          item.itemId = item.id
          item.value = undefined
          delete item.createBy
          delete item.createTime
          delete item.updateBy
          delete item.updateTime
          delete item.delFlag
          delete item.id
          return item
        })
        this.itemObject = _.groupBy(this.itemList, 'patrolType')
      })

    },
    getTempDictLable(data, value) {
      if (data && value) {
        return data.filter(i => i.dictValue == value)[0].dictLabel
      }
      return null
    },

    /**
     * 坐标相关方法
     */
    // 显示坐标拾取组件
    showCoordinatePicker(type) {
      this.currentCoordinateType = type;

      // 如果已有经纬度，设置初始值
      if (this.form.longitude && this.form.latitude) {
        this.coordinatePickerInitPosition = `${this.form.longitude},${this.form.latitude}`;
      } else {
        // 默认昆明市坐标
        this.coordinatePickerInitPosition = "102.8207599,24.8885797";
      }

      this.coordinatePickerVisible = true;
    },

    // 处理坐标保存
    handleCoordinateSave(position) {
      if (!position) return;

      console.log(position)
      const coordinates = position.split(',');
      if (coordinates.length === 2) {
        const lng = coordinates[0];
        const lat = coordinates[1];

        // 根据坐标类型设置表单字段
        if (this.currentCoordinateType === 'longitude' || this.currentCoordinateType === 'latitude') {
          this.form.longitude = lng;
          this.form.latitude = lat;
        }
      }
    },

  }
};
</script>
<style scoped>
.hasTagsView .app-main[data-v-078753dd] {
  background: #f5f7fa;
}

.tableDiv {
  background-color: white;
  padding-bottom: 10px;
}

.infoBox {
  padding: 15px 15px 0 15px;
  box-sizing: border-box;
  border-radius: 6px;
  border: 1px solid #C4C4C4;
  position: relative;
  margin: 0px 20px 30px;

  .infoTitle {
    user-select: none;
    position: absolute;
    top: 0;
    left: 0;
    padding: 0 10px;
    font-size: 14px;
    line-height: 14px;
    font-weight: bold;
    transform: translateX(15px) translateY(-50%);
    background-color: white;
  }

  .imgBox {
    height: auto;
    width: 100%;
    display: flex;
    flex-wrap: wrap;
    align-content: flex-start;

    .imgItemBox {
      height: 240px;
      width: calc(100% / 3);
      box-sizing: border-box;
      padding: 10px;
      overflow-y: auto;
      display: flex;
      align-items: center;
      justify-content: center;
      position: relative;

      .imgDeleteBtn {
        position: absolute;
        z-index: 1;
        top: 10%;
        right: 10%;
      }
    }
  }
}

.check-edit-title {
//font-weight: bold; //font-size: 1.15rem; font-size: 1.1rem; padding: 10px 0;
  /* color: #333333;*/
  /* color: #72767b;*/
  padding: 0px 0 15px 0;

  &:before {
    content: '';
    display: inline-block;
    width: 5px;
    height: 1.5rem;
    vertical-align: bottom;
    margin-right: 0.8rem;
    background: #3797EB;
  }
}

.asset-check-edit-dialog ::v-deep .el-dialog__body {
  padding: 20px 20px 10px;
  height: 76vh;
  overflow-y: auto;
  scrollbar-width: none;
}

.asset-check-edit-dialog1 ::v-deep .el-dialog__body {
  padding: 20px 20px 10px;
  height: 46vh;
  overflow-y: auto;
  scrollbar-width: none;
}


.asset-check-edit-dialog1 ::v-deep .el-dialog__footer {
  padding-top: 56px;
  border-radius: 10px;
  //margin: 30px 100px 30px 100px;
  margin: 30px 20%;
  background: rgb(240,242,245);
}




[onlyRead] {
  pointer-events: none;
}

</style>
