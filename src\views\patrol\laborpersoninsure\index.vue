<template>
  <div class="app-container">
    <el-row :gutter="20">

      <!--部门数据-->
      <el-col :span="deptNav ? 5:0 " :xs="24" class="leftDiv">
        <!--折叠图标-->
        <div class="leftIcon"  @click="deptNav=false">
          <span class="el-icon-caret-left"></span>
        </div>
        <div class="head-container">
          <el-input
            v-model="deptName"
            placeholder="单位名称"
            clearable
            size="small"
            prefix-icon="el-icon-search"
            style="margin-bottom: 20px"
          />
        </div>
        <div class="head-container" style="width: 300px">
          <el-tree
              :data="deptOptions"
              :props="defaultProps"
              :expand-on-click-node="false"
              :filter-node-method="filterNode"
              ref="tree"
              :render-after-expand="true"
              node-key="id"
              highlight-current
              @node-click="handleNodeClick"
            >
              <template #default="{ node, data }">
                  <div class="custom-tree-node" >
                      <div :class="{ 'activeNode': node.data.domainType === 999 }"  :title="data.label"
                        style="width:270px;white-space:nowrap;overflow:hidden;text-overflow:ellipsis;">
                        {{ node.label }}
                      </div>
                  </div>
              </template>
            </el-tree>
        </div>
      </el-col>


      <!--筛选区开始-->
      <el-col :span="deptNav ? 19:24" :xs="24">
        <!--展开图标-->
        <div class="rightIcon" @click="deptNav=true" v-show="!deptNav">
          <span class="el-icon-caret-right"></span>
        </div>
        <!-- 暂时屏蔽，后期需要就放开 -->
        <el-row v-if="false">
          <el-col :span="24" >
            <el-form :model="queryParams" ref="queryForm" size="small" :inline="true"  label-width="68px">
                <el-form-item >
                  <el-button type="primary"  @click="deptNav=!deptNav"   :icon="deptNavIcon" size="mini">
                    <span v-show="deptNav">折叠</span>
                    <span v-show="!deptNav">展开</span>
                  </el-button>
                </el-form-item>
            </el-form>
            <!--默认折叠-->
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="24" >
            <el-row>
                <el-col style="height:100px">
                    <div class="txt_height">参保情况</div>
                    <el-row>
                        <span class="mx-1" type="primary">购买个人险</span>
                        <span class="mx-1" type="danger">{{ statisticsData.personalCount }}</span>
                        <span class="mx-1" type="primary">人</span>
                        <span class="mx-1" type="danger">{{ statisticsData.personalShare }}</span>
                        <span class="mx-1" type="primary">份，购买团体险</span>
                        <span class="mx-1" type="danger">{{ statisticsData.groupCount }}</span>
                        <span class="mx-1" type="primary">份。</span>
                    </el-row>
                    <el-row>
                        <span class="mx-1" type="primary">未参保人数：</span>
                        <span class="mx-1" type="danger">{{ statisticsData.notCount }}</span>
                        <span class="mx-1" type="primary">人，请尽快购买。</span>
                    </el-row>
                    <el-row>
                        <span class="mx-1" type="primary">保险过期</span>
                        <span class="mx-1" type="danger">{{ statisticsData.expireCount }}</span>
                        <span class="mx-1" type="primary">份，请尽快续保。</span>
                    </el-row>
                </el-col>
            </el-row>
          </el-col>
        </el-row>
        
        <el-col >
            <el-tabs type="border-card" v-model="activeTab">
              <el-tab-pane label="个人险购买情况" name="personal">
                  <InsuranceTabData ref="personalInsur" v-if="activeTab === 'personal'" :labisureType="0" :domainId="domainId" :grade="grade" 
                    @getStatistics="handleStatistics" :domainIdStr="domainIdStr"></InsuranceTabData>
              </el-tab-pane>
              <el-tab-pane label="团体险购买情况" name="team">
                  <InsuranceTabData ref="teamInsur" v-if="activeTab === 'team'" :labisureType="1" :domainId="domainId" :grade="grade" 
                    @getStatistics="handleStatistics" :domainIdStr="domainIdStr"></InsuranceTabData>
              </el-tab-pane>
            </el-tabs>

        </el-col>

      </el-col>
    </el-row>
    


  </div>
</template>

<script>
  import { getStatistics,listLaborpersoninsure } from "@/api/patrol/laborpersoninsure";
  import { getLaborUnitTree } from "@/api/patrol/laborunit";
  import { getToken } from "@/utils/auth";
  import InsuranceTabData from "@/views/patrol/laborpersoninsure/insuranceTabData.vue"
  import Treeselect from "@riophae/vue-treeselect";
  import "@riophae/vue-treeselect/dist/vue-treeselect.css";

  export default {
    name: "Laborpersoninsure",
    components: { InsuranceTabData },
    data() {
      return {
        // 遮罩层
        loading: true,
        // 选中数组
        ids: [],
        // 非单个禁用
        single: true,
        // 非多个禁用
        multiple: true,
        // 显示搜索条件
        showSearch: false,
        dictType:[],
        // 总条数
        total: 0,
        // 劳务人员参保情况表格数据
        laborpersoninsureList: null,
        // 弹出层标题
        title: "",
        // 左侧组织树
        deptNav: true,
        // 部门树选项
        deptOptions: undefined,
        // 部门名称
        deptName: undefined,
        //当前点击的树节点
        currentOptionData:undefined,
        // 是否显示弹出层
        open: false,
        activeTab: "personal",
        // 表单参数
        form: {},
        defaultProps: {
          children: "children",
          label: "label"
        },
        domainId: "-1",
        grade: -1,
        domainIdStr: "",
        //统计数据
        statisticsData:{
          expireCount: 0,
          groupCount: 0,
          notCount: 0,
          personalCount: 0,
          personalShare: 0
        },
        // 用户导入参数
        upload: {
          // 是否显示弹出层（用户导入）
          open: false,
          // 弹出层标题（用户导入）
          title: "",
          // 是否禁用上传
          isUploading: false,
          // 是否更新已经存在的用户数据
          updateSupport: 0,
          // 设置上传的请求头部
          headers: { Authorization: "Bearer " + getToken() },
          // 上传的地址
          url: process.env.VUE_APP_BASE_API + "/system/user/importData"
        },
        // 查询参数
        queryParams: {
          pageNum: 1,
          pageSize: 50,
            userId: null,
            labisureType: null,
            domainId: null,
            insureType: null,
            insureStartDate: null,
            insureEndDate: null,
            insureCode: null,
            attFile: null,
            status: null
        },
        // 列信息
        columns: [
        { key: 0, label: `用户id，关联劳务人员表ID字段`, visible: true },
        { key: 1, label: `类型(0:个人保险，1：团体保险)`, visible: true },
        { key: 2, label: `所属单位id`, visible: true },
        { key: 3, label: `保险类型`, visible: true },
        { key: 4, label: `保险起始时间`, visible: true },
        { key: 5, label: `保险结束时间`, visible: true },
        { key: 6, label: `保险单号`, visible: true },
        { key: 7, label: `附件`, visible: true },
        { key: 8, label: `备注`, visible: true },
        { key: 9, label: `删除标志（0代表存在，1代表删除）`, visible: true },
        { key: 10, label: `状态（0-正常，1-停用）`, visible: true }
        ],
        // 表单校验
        rules: {
          userId: [
              { required: true, message: "用户id，关联劳务人员表ID字段不能为空", trigger: "blur" }
          ]
        }
      };
    },
    watch: {
      // 根据名称筛选部门树
      deptName(val) {
        this.$refs.tree.filter(val);
      }
    },
    created() {
      this.getList();
      this.getDeptTree();
      this.handleStatistics();
    },
    computed: {
      deptNavIcon() {
          return this.deptNav ? 'el-icon-arrow-left' : 'el-icon-arrow-right';
      },
    },
    methods: {
      /** 查询用户列表 */
      getList() {
        this.loading = true;
        listLaborpersoninsure(this.queryParams).then(response => {
          this.laborpersoninsureList = response.rows;
          this.total = response.total;
          this.loading = false;
        });
      },
      /** 查询部门下拉树结构 */
      getDeptTree() {
        getLaborUnitTree().then(response => {
          this.deptOptions = response.data;
        });
      },
      // 筛选节点
      filterNode(value, data) {
        if (!value) return true;
        return data.label.indexOf(value) !== -1;
      },
      // 节点单击事件
      handleNodeClick(val) {
        if (val.domainType === 999) {
            this.domainId = val.parentId
            this.grade = 3
            this.domainIdStr = val.id
            console.log("handleNodeClick11,", val, this.domainIdStr);
        } else {
            let labDomainIdList = this.getChilds(val)
            this.domainId = val.id
            this.grade = 1
            this.domainIdStr = labDomainIdList.length > 0 ? labDomainIdList.join(",") : ''
            console.log("handleNodeClick222,", this.domainId, this.domainIdStr);
        }
        this.handleStatistics();
        this.$nextTick(() => {
          if (this.activeTab === 'personal'){
            this.$refs.personalInsur.handleQuery();
          }else {
            this.$refs.teamInsur.handleQuery();
          }
        })
      },
      getChilds(node){
        let result = []
        if (node.domainType === 999) {
            result.push(node.id)
        }
        if (node.children && node.children.length > 0) {
            for (let i = 0; i < node.children.length; i++) {
                result = result.concat(this.getChilds(node.children[i]));
            }
        }
        return result
      },
      // 取消按钮
      cancel() {
        this.open = false;
        this.reset();
      },
      // 表单重置
      reset() {
        this.form = {
            userId: null,
            labisureType: null,
            domainId: null,
            insureType: null,
            insureStartDate: null,
            insureEndDate: null,
            insureCode: null,
            attFile: null,
            remark: null,
            delFlag: null,
            status: null
        };
        this.resetForm("form");
      },
      //获取统计数据
      handleStatistics(){
        let domainIds = [];
        if(this.domainIdStr != null && this.domainIdStr != ''){
          domainIds = this.domainIdStr.split(',');
        }
        getStatistics({domainIds: domainIds}).then((res)=>{
          this.statisticsData = res.data;
        })
        // getStatistics(this.domainId).then((res)=>{
        //   this.statisticsData = res.data;
        // })
      }
  }
}
</script>
<style scoped>
  .hasTagsView .app-main[data-v-078753dd]{
    background: #f5f7fa;
  }

  .tableDiv{
    background-color: white;
    padding-bottom: 10px;
  }

  .custom-tree-node{
      display: flex;
      align-items: center;
      width: 100%;
      justify-content: space-between;
      font-size:14px;
  }

  .leftDiv{
    border-right: 1px solid #d8dce5;
    min-height: calc(100vh - 110px);
    overflow-y: auto;
    height: calc(80vh - 110px);
    position: relative;
    top: -20px;
    padding-top: 10px;
    background-color: white;
  }

  .leftIcon{
    border: 1px solid #DCDFE6;
    border-radius: 8px;
    width: 16px;
    height: 50px;
    line-height: 50px;
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
    position: absolute;
    right: 0;
    top: 300px;
    z-index: 2;
  }
  .leftIcon:hover{
    background-color: #DCDFE6;
  }

  .rightIcon{
    border: 1px solid #DCDFE6;
    border-radius: 8px;
    width: 16px;
    height: 50px;
    line-height: 50px;
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
    position: absolute;
    left: -10px;
    top: 280px;
    z-index: 10;
    background: white;
  }
  .rightIcon:hover{
    background-color: #DCDFE6;
  }

  .txt_height{
    height:30px
  }

</style>
