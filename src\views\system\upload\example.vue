<template>
	<cardBox
		style="height: 100%"
		:typeCode="typeCode"
		:bizId="bizId"
		:forView="forView"
		:boxHeight="'40vh'"
	/>
</template>
<script>
import cardBox from '@/components/Drawing/cardBox.vue'
export default {
	name: 'example',
	components: {
		cardBox,
	},
	provide() {
		return {
			oneMap: false,
		}
	},
	data() {
		return {
			typeCode: 'BS123', // 模块类型编码
			bizId: '0', // 业务ID(资产id等)
			forView: false, // 是否只用于查看
		}
	},
}
</script>
