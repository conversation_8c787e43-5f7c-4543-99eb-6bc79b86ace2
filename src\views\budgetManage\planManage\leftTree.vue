<template>
  <div class="left-tree">
    <div class="head-container">
      <el-input
          v-model="keyword"
          placeholder="输入关键词检索"
          clearable
          size="small"
          prefix-icon="el-icon-search"
          style="margin-bottom: 20px"
      />
    </div>
    <div class="head-container" style="width: 300px;overflow-x: auto;">
      <el-tree
          :data="realOptions"
          :expand-on-click-node="false"
          :filter-node-method="filterNode"
          ref="tree"
          node-key="id"
          highlight-current
          @node-click="handleNodeClick"
      >
      </el-tree>
    </div>
  </div>
</template>
<script>
import { findUserDeptMaintenanceList2 } from "@/api/system/maintenanceSection";

export default {
  data() {
    return {
      keyword: '',
      realOptions: [],
    }
  },
  created() {
    this.getDeptTree()
  },
  watch: {
    keyword(val) {
        this.$refs.tree.filter(val);
      }
  },
  methods: {
    // 筛选节点
    filterNode(value, data) {
      if (!value) return true;
      return data.label.indexOf(value) !== -1;
    },
    // 查询部门下拉树结构
    getDeptTree() {
      findUserDeptMaintenanceList2().then(response => {
        const treeData = response.data
        treeData.forEach(item => {
          getChild(item)
        })
        function getChild(node) {
          node.label = node.deptName || node.maintenanceSectionName
          node.id = node.deptId || node.maintenanceSectionId
          if (node.children) {
            node.children.forEach(item => {
              getChild(item)
            })
          }
        }
        // 增加一个最顶级
        const tree = [
          {
            label: "云南省交通投资建设集团有限公司",
            id: "1",
            children: [...treeData],
          },
        ];
        this.realOptions = tree
      });
    },
    handleNodeClick(e) {
      if (e.deptId || e.departmentId) {
        if (e.deptId) {
          this.$emit('rowClick',{ domainId: String(e.id) })
        } else {
          this.$emit('rowClick',{ domainId: String(e.departmentId),maiSecId: e.id })
        }
      } else {
        this.$emit('rowClick', {})
      }
    },
  }
}
</script>
<style scoped lang="scss">
.left-tree{
  height: 100%;
  overflow: auto;
}
</style>
<style lang="scss" scoped>
@import "@/assets/styles/business.scss";
</style>
