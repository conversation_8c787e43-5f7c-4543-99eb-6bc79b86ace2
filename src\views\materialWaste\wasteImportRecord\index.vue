<template>
  <div class="app-container maindiv">
    <el-row>
      <el-col :span="24" :xs="24">
        <el-row>
          <el-form
            ref="queryForm"
            :model="queryParams"
            size="mini"
            :inline="true"
            label-width="100px"
          >
            <el-form-item label="管养单位">
              <selectTree
                :key="'domainId'"
                style="width: 240px"
                v-model="queryParams.domainId"
                :dept-type="101"
                :expand-all="false"
                :dataRule="false"
                placeholder="请选择管养单位"
                clearable
              />
            </el-form-item>
            <el-form-item label="站点名称">
              <el-input v-model="queryParams.siteName" placeholder="请输入站点名称" />
            </el-form-item>
            <el-form-item label="物资名称">
              <el-input v-model="queryParams.wname" placeholder="请输入物资名称" />
            </el-form-item>
            <el-form-item label="起始处理时间">
              <el-date-picker
                v-model="queryParams.startHandleTime"
                type="date"
                placeholder="选择日期"
                value-format="yyyy-MM-dd"
              />
            </el-form-item>
            <el-form-item label="截止处理时间">
              <el-date-picker
                v-model="queryParams.endHandleTime"
                type="date"
                placeholder="选择日期"
                value-format="yyyy-MM-dd"
              />
            </el-form-item>
            <el-form-item>
              <el-button
                type="primary"
                icon="el-icon-search"
                size="mini"
                @click="handleQuery"
              >搜索</el-button>
              <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
            </el-form-item>
          </el-form>
        </el-row>
      </el-col>
    </el-row>

    <!--操作按钮区开始-->
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="success"
          icon="el-icon-upload"
          size="mini"
          @click="handleImport"
          v-has-menu-permi="['waste:record:store:upload']"
        >导入</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          icon="el-icon-download"
          size="mini"
          @click="exportList"
          v-has-menu-permi="['waste:detail:export']"
        >导出</el-button>
      </el-col>
<!--      <el-col :span="1.5">-->
<!--        <el-button-->
<!--          type="info"-->
<!--          icon="el-icon-download"-->
<!--          size="mini"-->
<!--          @click="downloadTemplate"-->
<!--          v-has-menu-permi="['waste:detail:export']"-->
<!--        >下载模板</el-button>-->
<!--      </el-col>-->
      <right-toolbar
        :showSearch.sync="showSearch"
        @queryTable="handleQuery"
        :columns="columns"
      ></right-toolbar>
    </el-row>

    <el-row>
      <div class="draggable">
        <el-table
          v-adjust-table
          size="mini"
          style="width: 100%"
          v-loading="loading"
          border
          :data="tableData"
          row-key="id"
          ref="dataTable"
          stripe
          highlight-current-row
          @row-click="handleClickRow"
          :height="showSearch ? 'calc(100vh - 330px)' : 'calc(100vh - 270px)'"
        >
          <el-table-column
            label="序号"
            align="center"
            type="index"
            width="50"
          />
          <template v-for="(column, index) in columns">
            <el-table-column
              :label="column.label"
              v-if="column.visible"
              align="center"
              :prop="column.field"
              :width="column.width"
            >
              <template slot-scope="scope">
                <template v-if="column.field === 'domainId'">
                  {{ scope.row.domainName || scope.row.domainId }}
                </template>
                <template v-else-if="column.field === 'handleTime'">
                  {{ scope.row.handleTime | dateFormat }}
                </template>
                <span v-else>{{ scope.row[column.field] }}</span>
              </template>
            </el-table-column>
          </template>
          <el-table-column label="操作" fixed="right" align="center" width="150">
            <template slot-scope="scope">
              <el-button
                type="text"
                icon="el-icon-edit"
                size="mini"
                v-has-menu-permi="['waste:detail:edit']"
                @click="openDetailDialog(scope.row)"
              >编辑</el-button>
            </template>
          </el-table-column>
        </el-table>
        <pagination
          v-show="total > 0"
          :total="total"
          :page.sync="queryParams.pageNum"
          :limit.sync="queryParams.pageSize"
          @pagination="handleQuery"
        />
      </div>
    </el-row>

    <!-- 详情对话框 -->
    <el-dialog
      :title="drawerTitle"
      destroy-on-close
      :visible.sync="drawer"
      :close-on-click-modal="false"
      width="50%"
      v-if="drawer"
    >
      <el-form
        ref="elForm"
        :model="formData"
        :rules="rules"
        label-width="120px"
        size="medium"
      >
        <el-row :gutter="15">
          <el-col :span="12">
            <el-form-item label="站点名称">
              <el-input
                v-model="formData.siteName"
                placeholder="站点名称"
                :disabled="true"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="物资类型">
              <el-input
                v-model="formData.wtype"
                placeholder="物资类型"
                :disabled="true"
              ></el-input>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="15">
          <el-col :span="12">
            <el-form-item label="物资名称" prop="wname">
              <el-input
                v-model="formData.wname"
                placeholder="请输入物资名称"
                clearable
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="处理时间">
              <el-date-picker
                v-model="formData.handleTime"
                type="date"
                placeholder="处理时间"
                value-format="yyyy-MM-dd"
                style="width: 100%"
                :disabled="true"
              ></el-date-picker>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="15">
          <el-col :span="12">
            <el-form-item label="产权单位">
              <el-input
                v-model="formData.ownerUnit"
                placeholder="产权单位"
                :disabled="true"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="规格型号">
              <el-input
                v-model="formData.specs"
                placeholder="规格型号"
                :disabled="true"
              ></el-input>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="15">
          <el-col :span="12">
            <el-form-item label="单位">
              <el-input
                v-model="formData.unit"
                placeholder="单位"
                :disabled="true"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="物资数量" prop="quantity">
              <el-input
                v-model="formData.quantity"
                placeholder="请输入物资数量"
                clearable
                type="number"
              ></el-input>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="15">
          <el-col :span="12">
            <el-form-item label="管理处负责人">
              <el-input
                v-model="formData.glcPerson"
                placeholder="管理处负责人"
                :disabled="true"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="负责人电话">
              <el-input
                v-model="formData.glcTelphone"
                placeholder="负责人电话"
                :disabled="true"
              ></el-input>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="15">
          <el-col :span="12">
            <el-form-item label="现场管理人员">
              <el-input
                v-model="formData.xcPerson"
                placeholder="现场管理人员"
                :disabled="true"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="管理人员电话">
              <el-input
                v-model="formData.xcTelphone"
                placeholder="管理人员电话"
                :disabled="true"
              ></el-input>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="15">
<!--          <el-col :span="12">-->
<!--            <el-form-item label="处理金额">-->
<!--              <el-input-->
<!--                v-model="formData.handleAmount"-->
<!--                placeholder="处理金额"-->
<!--                :disabled="true"-->
<!--              ></el-input>-->
<!--            </el-form-item>-->
<!--          </el-col>-->
          <el-col :span="12">
            <el-form-item label="所属管理处">
              <selectTree
                :key="'domainId'"
                style="width: 100%"
                v-model="formData.domainId"
                :dept-type="101"
                :expand-all="false"
                :dataRule="false"
                placeholder="所属管理处"
                :disabled="true"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="15">
          <el-col :span="24">
            <el-form-item label="备注">
              <el-input
                v-model="formData.remark"
                type="textarea"
                placeholder="备注"
                :rows="2"
                :disabled="true"
              ></el-input>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="15">
          <el-col :span="24">
            <el-form-item label='全局照'>
              <file-upload v-model="formData.globalPhoto" for-view></file-upload>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="15">
          <el-col :span="24">
            <el-form-item label='近景照'>
              <file-upload v-model="formData.closeUpPhoto" for-view></file-upload>
            </el-form-item>
          </el-col>
        </el-row>

        <div style="text-align: right;">
          <el-button type="primary" @click="onSave">保 存</el-button>
          <el-button @click="drawer = false">取 消</el-button>
        </div>
      </el-form>
    </el-dialog>

    <!-- 导入对话框 -->
    <el-dialog
      title="导入废料入库记录"
      :visible.sync="importDialogVisible"
      width="30%"
      :close-on-click-modal="false"
    >
      <el-upload
        class="upload-demo"
        ref="upload"
        :action="importUrl"
        :headers="headers"
        :on-success="handleImportSuccess"
        :on-error="handleImportError"
        :before-upload="beforeUpload"
        :file-list="fileList"
        :auto-upload="false"
        accept=".xlsx,.xls"
      >
        <el-button slot="trigger" size="small" type="primary">选取文件</el-button>
        <div slot="tip" class="el-upload__tip">只能上传xlsx/xls文件，且不超过10MB<el-link type="primary" @click="downloadTemplate" v-has-menu-permi="['waste:detail:export']">下载导入模板</el-link></div>
      </el-upload>
      <span slot="footer" class="dialog-footer">
        <el-button @click="importDialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="submitUpload">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import selectTree from "@/components/DeptTmpl/selectTree.vue";
import { getToken } from "@/utils/auth";
import {
  listWasteImportDetail,
  editWasteImportDetail,
} from '@/api/materialWaste/wasteimportRecord'

export default {
  name: 'WasteImportRecord',
  components: {
    selectTree
  },
  filters: {
    dateFormat(value) {
      if (!value) return '';
      return value.split(' ')[0];
    }
  },
  data() {
    return {
      showSearch: false,
      queryParams: {
        pageNum: 1,
        pageSize: 50,
        domainId: null,
        siteName: '',
        wname: '',
        startTime: '',
        endTime: '',
        handleType: 1 // 固定为入库类型
      },
      total: 0,
      loading: false,
      columns: [
        {
          key: 0,
          field: "siteName",
          label: "站点名称",
          visible: true
        },
        {
          key: 1,
          field: "wtype",
          label: "物资类型",
          visible: true
        },
        {
          key: 2,
          field: "wname",
          label: "物资名称",
          visible: true
        },
        {
          key: 3,
          field: "handleTime",
          label: "处理时间",
          visible: true
        },
        {
          key: 4,
          field: "ownerUnit",
          label: "产权单位",
          visible: true
        },
        {
          key: 5,
          field: "specs",
          label: "规格型号",
          visible: true
        },
        {
          key: 6,
          field: "unit",
          label: "单位",
          visible: true
        },
        {
          key: 7,
          field: "quantity",
          label: "物资数量",
          visible: true
        },
        {
          key: 8,
          field: "glcPerson",
          label: "管理处负责人",
          visible: true
        },
        {
          key: 9,
          field: "xcPerson",
          label: "现场管理人员",
          visible: true
        },
        // {
        //   key: 10,
        //   field: "handleAmount",
        //   label: "处理金额",
        //   visible: true
        // }
      ],
      tableData: [],
      rowData: {},
      drawerTitle: "编辑入库记录",
      drawer: false,
      formData: {
        id: '',
        siteName: '',
        wtype: '',
        wname: '',
        handleTime: '',
        ownerUnit: '',
        specs: '',
        unit: '',
        quantity: 0,
        glcPerson: '',
        glcTelphone: '',
        xcPerson: '',
        xcTelphone: '',
        handleAmount: 0,
        domainId: null,
        remark: ''
      },
      rules: {
        siteName: [
          { required: true, message: '站点名称不能为空', trigger: 'blur' }
        ],
        wtype: [
          { required: true, message: '物资类型不能为空', trigger: 'blur' }
        ],
        wname: [
          { required: true, message: '物资名称不能为空', trigger: 'blur' }
        ],
        handleTime: [
          { required: true, message: '处理时间不能为空', trigger: 'change' }
        ],
        quantity: [
          { required: true, message: '物资数量不能为空', trigger: 'blur' }
        ]
      },
      importDialogVisible: false,
      importUrl: process.env.VUE_APP_BASE_API + "/manager/waste/import/detail/store/upload",
      headers: {
        Authorization: "Bearer " + getToken()
      },
      fileList: []
    };
  },
  created() {
    this.handleQuery();
  },
  methods: {
    handleQuery() {
      this.loading = true;
      listWasteImportDetail(this.queryParams).then(res => {
        this.loading = false;
        this.tableData = res.rows;
        this.total = res.total;
      }).catch(() => {
        this.loading = false;
      });
    },
    resetQuery() {
      this.queryParams = {
        pageNum: 1,
        pageSize: 50,
        domainId: null,
        siteName: '',
        wname: '',
        startTime: '',
        endTime: '',
        handleType: 1
      };
      this.handleQuery();
    },
    openDetailDialog(row) {
      if (!row) {
        this.$modal.msgWarning("请先选择一条记录");
        return;
      }

      this.drawerTitle = "编辑入库记录";
      this.formData = JSON.parse(JSON.stringify(row));
      this.drawer = true;
    },
    onSave() {
      this.$refs.elForm.validate(valid => {
        if (valid) {
          editWasteImportDetail(this.formData).then(res => {
            this.$modal.msgSuccess("修改成功");
            this.drawer = false;
            this.handleQuery();
          })
        }
      });
    },
    handleClickRow(row) {
      this.rowData = row;
    },
    exportList() {
      this.download('manager/waste/import/detail/export', {
        ...this.queryParams
      }, `废料入库记录_${new Date().getTime()}.xlsx`);
    },
    handleImport() {
      this.importDialogVisible = true;
    },
    submitUpload() {
      this.$refs.upload.submit();
    },
    beforeUpload(file) {
      const isLt10M = file.size / 1024 / 1024 < 10;
      if (!isLt10M) {
        this.$message.error('上传文件大小不能超过10MB!');
        return false;
      }
      return true;
    },
    handleImportSuccess(response, file, fileList) {
      if (response.code === 200) {
        this.$modal.msgSuccess(response.msg);
        this.importDialogVisible = false;
        this.handleQuery();
      } else {
        this.$modal.msgError(response.msg);
      }
      this.fileList = [];
    },
    handleImportError(err, file, fileList) {
      this.$modal.msgError('导入失败');
      this.fileList = [];
    },
    downloadTemplate() {
      this.download('/manager/waste/import/detail/download?type=1', {}, '物资废料入库模板.xlsx');
    }
  }
};
</script>

<style lang="scss" scoped>
@import "@/assets/styles/business.scss";

::v-deep .dialog-footer {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  width: 100%;
}

.upload-demo {
  text-align: center;
}
</style>
