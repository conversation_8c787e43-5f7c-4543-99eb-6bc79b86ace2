// 行政识别数据
const administrative = [
  {
    label: '固定编码',
    prop: 'fixedCode',
    placeholder: '请输入固定编码',
    type: 'input'
  },
  {
    label: '管理处',
    prop: 'managementMaintenanceId',
    placeholder: '请选择管理处',
    type: 'selectTree',
    deptType: 201,
    rules: [{ required: true, message: '请选择管养分处', trigger: 'change' }]
  },
  {
    label: '管养分处',
    prop: 'managementMaintenanceBranchId',
    placeholder: '请输入管养分处',
    type: 'selectTree',
    deptType: 202,
    rules: [{ required: true, message: '请选择管养分处', trigger: 'change' }]
  },
  {
    label: '养护路段',
    prop: 'maintenanceSectionId',
    placeholder: '请选择养护路段',
    type: 'select',
    rules: [{ required: true, message: '请选择养护路段', trigger: 'change' }],
    options: [],
    api: 'maintenanceSectionList',
    optionLabel: 'maintenanceSectionName',
    optionValue: 'maintenanceSectionId',
    disabledFieds: 'managementMaintenanceId'
  },
  {
    label: '路段类型',
    prop: 'sectionType',
    placeholder: '请选择路段类型',
    type: 'roadType',
    options: []
  },
  {
    label: '路线编码',
    prop: 'routeCode',
    placeholder: '请选择路线编码',
    type: 'select',
    rules: [{ required: true, message: '请选择路线编码', trigger: 'change' }],
    options: [],
    api: 'routeListAll',
    optionLabel: 'routeCode',
    optionValue: 'routeCode',
    disabledFieds: 'maintenanceSectionId'
  },
  {
    label: '路线名称',
    prop: 'routeName',
    placeholder: '请输入路线名称',
    type: 'select',
    api: 'routeListAll',
    optionLabel: 'routeName',
    optionValue: 'routeName',
    disabledFieds: 'maintenanceSectionId'
  },
  {
    label: '路线等级',
    prop: 'routeLevel',
    placeholder: '请选择路线等级',
    type: 'dictSelect',
    dict: 'sys_route_grade'
  },
  {
    label: '所在行政区编码',
    prop: 'areaCode',
    placeholder: '请选择所在行政区编码',
    type: 'CascaderRegion',
    options: [],
    rules: [{ required: true, message: '请选择所在行政区编码', trigger: 'blur' }],
  },
  {
    label: '桥梁编码',
    prop: 'bridgeCode',
    placeholder: '请输入桥梁编码',
    type: 'input',
    rules: [{ required: true, message: '请输入桥梁编码', trigger: 'blur' }]
  },
  {
    label: '桥梁名称',
    prop: 'bridgeName',
    placeholder: '请输入桥梁名称',
    type: 'input',
    rules: [{ required: true, message: '请输入桥梁名称', trigger: 'blur' }]
  },
  {
    label: '桥位桩号',
    prop: 'centerStake',
    placeholder: '请输入桥位桩号',
    type: 'pileInput',
    precision: 3,
    rules: [{ required: true, message: '请输入桥位桩号', trigger: 'blur' }]
  },
  {
    label: '行车方向',
    prop: 'direction',
    placeholder: '请选择行车方向',
    type: 'dictSelect',
    dict: 'sys_route_direction'
  },
  {
    label: '跨径分类',
    prop: 'spanClassifyType',
    placeholder: '请选择跨径分类',
    type: 'dictSelect',
    dict: 'bridge_span_classify',
    rules: [{ required: true, message: '请选择跨径分类', trigger: 'change' }]
  },
  {
    label: '功能类型',
    prop: 'functionType',
    placeholder: '请选择功能类型',
    type: 'dictSelect',
    dict: 'bridge_function_type'
  },
  {
    label: '下穿通道名',
    prop: 'underpassChannelName',
    placeholder: '请输入下穿通道名',
    type: 'input'
  },
  {
    label: '下穿通道桩号',
    prop: 'underpassChannelunderStake',
    placeholder: '请输入下穿通道桩号',
    type: 'pileInput',
    precision: 3
  },
  {
    label: '设计荷载',
    prop: 'designLoadType',
    placeholder: '请选择设计荷载',
    type: 'dictSelect',
    dict: 'bridge_design_load'
  },
  {
    label: '通行载重',
    prop: 'trafficLoadType',
    placeholder: '请选择通行载重',
    type: 'dictSelect',
    dict: 'bridge_traffic_load'
  },
  {
    label: '桥梁坡度',
    prop: 'bridgeSlope',
    placeholder: '请输入桥梁坡度',
    type: 'inputNumber',
    precision: 4
  },
  {
    label: '桥梁平曲线半径',
    prop: 'planeCurveRadius',
    placeholder: '请输入桥梁平曲线半径',
    type: 'inputNumber',
    precision: 3
  },
  {
    label: '建成年限/时间',
    prop: 'buildDate',
    placeholder: '请选择建成年限/时间',
    type: 'date'
  },
  {
    label: '建设单位',
    prop: 'buildUnit',
    placeholder: '请输入建设单位',
    type: 'input'
  },
  {
    label: '设计单位',
    prop: 'designUnit',
    placeholder: '请输入设计单位',
    type: 'input'
  },
  {
    label: '施工单位',
    prop: 'constructionUnit',
    placeholder: '请输入施工单位',
    type: 'input'
  },
  {
    label: '监理单位',
    prop: 'supervisionUnit',
    placeholder: '请输入监理单位',
    type: 'input'
  },
  {
    label: '通航等级',
    prop: 'navigationGradeType',
    placeholder: '请选择通航等级',
    type: 'dictSelect',
    dict: 'bridge_navigation_grade'
  },
  {
    label: '墩台防撞设施类型',
    prop: 'pierCollisionAvoidanceType',
    placeholder: '请选择墩台防撞设施类型',
    type: 'dictSelect',
    dict: 'bridge_pier_collision_avoidance'
  },
  {
    label: '是否互通立交',
    prop: 'whetherInterchangeType',
    placeholder: '请选择是否互通立交',
    type: 'dictSelect',
    dict: 'base_data_yes_no'
  },
  {
    label: '是否宽路窄桥',
    prop: 'whetherNarrowBridgeType',
    placeholder: '请选择是否宽路窄桥',
    type: 'dictSelect',
    dict: 'base_data_yes_no'
  },
  {
    label: '是否在长大桥梁目录',
    prop: 'whetherInLongSpanCatalogType',
    placeholder: '请选择是否在长大桥梁目录',
    type: 'dictSelect',
    dict: 'base_data_yes_no'
  },
  {
    label: '抗震等级',
    prop: 'seismicGradeType',
    placeholder: '请选择抗震等级',
    type: 'dictSelect',
    dict: 'bridge_seismic_grade'
  },
  {
    label: '收费性质',
    prop: 'chargePropertyType',
    placeholder: '请选择收费性质',
    type: 'dictSelect',
    dict: 'bridge_charge_property'
  },
  {
    label: '是否有健康监测系统',
    prop: 'whetherHealthMonitorSystemType',
    placeholder: '请选择是否有健康监测系统',
    type: 'dictSelect',
    dict: 'base_data_yes_no'
  },
  {
    label: '是否独柱墩',
    prop: 'whetherSingleColumnPierType',
    placeholder: '请选择是否独柱墩',
    type: 'dictSelect',
    dict: 'base_data_yes_no'
  },
  {
    label: '地理位置信息数据',
    prop: 'geolocationData',
    placeholder: '请输入地理位置信息数据',
    type: 'coordinate'
  },
]
// 结构技术数据
const technology = [
  {
    label: '桥长(m)',
    prop: 'totalLength',
    placeholder: '请输入桥长',
    type: 'inputNumber',
    precision: 2
  },
  {
    label: '桥面总宽(m)',
    prop: 'deckTotalWidth',
    placeholder: '请输入桥面总宽',
    type: 'inputNumber',
    precision: 2
  },
  {
    label: '车行道宽(m)',
    prop: 'laneWidth',
    placeholder: '请输入车行道宽',
    type: 'inputNumber',
    precision: 2
  },
  {
    label: '桥面标高(m)',
    prop: 'deckCenterHeight',
    placeholder: '请输入桥面标高',
    type: 'inputNumber',
    precision: 2
  },
  {
    label: '桥下净高(m)',
    prop: 'underVerticalHeight',
    placeholder: '请输入桥下净高',
    type: 'inputNumber',
    precision: 2
  },
  {
    label: '桥上净高(m)',
    prop: 'clearHeightOnBridge',
    placeholder: '请输入桥上净高',
    type: 'inputNumber',
    precision: 2
  },
  {
    label: '引道总宽(m)',
    prop: 'totalChannelWidth',
    placeholder: '请输入引道总宽',
    type: 'inputNumber',
    precision: 2
  },
  {
    label: '引道路面宽(m)',
    prop: 'channelDeckWidth',
    placeholder: '请输入引道路面宽',
    type: 'inputNumber',
    precision: 2
  },
  {
    label: '引道线形起点岸',
    prop: 'channelAlignmentStartType',
    placeholder: '请选择引道线形起点岸',
    type: 'dictSelect',
    dict: 'bridge_approach_type'
  },
  {
    label: '引道线形止点岸',
    prop: 'channelAlignmentEndType',
    placeholder: '请选择引道线形止点岸',
    type: 'dictSelect',
    dict: 'bridge_approach_type'
  },
  {
    label: '引道曲线半径(m)',
    prop: 'channelCurveRadius',
    placeholder: '请选择引道曲线半径',
    type: 'inputNumber',
    precision: 2
  },
  {
    label: '人行道宽度(m)',
    prop: 'sidewalkWidth',
    placeholder: '请输入人行道宽度',
    type: 'inputNumber',
    precision: 2
  },
  {
    label: '中护栏等级',
    prop: 'mediumBarrierGradeType',
    placeholder: '请选择中护栏等级',
    type: 'dictSelect',
    dict: 'bridge_barrier_grade'
  },
  {
    label: '边护栏等级',
    prop: 'gradeOfSideRailType',
    placeholder: '请选择边护栏等级',
    type: 'dictSelect',
    dict: 'bridge_barrier_grade'
  },
  {
    label: '护栏或防撞栏高度(m)',
    prop: 'guardrailHeight',
    placeholder: '请输入护栏或防撞栏高度',
    type: 'inputNumber',
    precision: 2
  },
  {
    label: '中央分隔带宽度(m)',
    prop: 'centralSeparatorWidth',
    placeholder: '请输入中央分隔带宽度',
    type: 'inputNumber',
    precision: 2
  },
  {
    label: '桥面标准净空(m)',
    prop: 'deckStandardClearance',
    placeholder: '请输入桥面标准净空',
    type: 'inputNumber',
    precision: 2
  },
  {
    label: '桥面实际净空(m)',
    prop: 'deckActualClearance',
    placeholder: '请输入桥面实际净空',
    type: 'inputNumber',
    precision: 2
  },
  {
    label: '桥下实际净空(m)',
    prop: 'underActualClearance',
    placeholder: '请输入桥下实际净空',
    type: 'inputNumber',
    precision: 2
  },
  {
    label: '设计洪水频率',
    prop: 'designFloodFrequencyType',
    placeholder: '请选择设计洪水频率',
    type: 'dictSelect',
    dict: 'bridge_design_flood_frequency'
  },
  {
    label: '设计洪水位',
    prop: 'designFloodLevelType',
    placeholder: '请输入设计洪水位',
    type: 'input'
  },
  {
    label: '历史洪水位',
    prop: 'historicalFloodLevel',
    placeholder: '请输入历史洪水位',
    type: 'input'
  },
  {
    label: '地震动峰值加速度系数',
    prop: 'earthquakeAccelerationType',
    placeholder: '请选择地震动峰值加速度系数',
    type: 'dictSelect',
    dict: 'bridge_earthquake_acceleration'
  },
  {
    label: '是否涉水桥梁',
    prop: 'whetherWaterBridge',
    placeholder: '请选择是否涉水桥梁',
    type: 'dictSelect',
    dict: 'base_data_yes_no'
  },
  {
    label: '墩柱是否在行洪范围内',
    prop: 'whetherPiersInFlood',
    placeholder: '请选择是否涉水桥梁',
    type: 'dictSelect',
    dict: 'base_data_yes_no'
  },
  {
    label: '桥梁墩柱是否在水中',
    prop: 'whetherPiersInWater',
    placeholder: '请选择是否涉水桥梁',
    type: 'dictSelect',
    dict: 'base_data_yes_no'
  },
]
// 结构信息数据
const information = [
  {
    label: '跨径组合',
    prop: 'spanGroups',
    placeholder: '请输入跨径组合',
    type: 'input'
  },
  {
    label: '主桥上部构造结构形式',
    prop: 'mainSuperstructureType',
    placeholder: '请选择主桥上部构造结构形式',
    type: 'dictSelect',
    dict: 'bridge_main_superstructure_type'
  },
  {
    label: '主桥上部构造材料类型',
    prop: 'mainSuperstructureMaterialType',
    placeholder: '请输入主桥上部构造材料类型',
    type: 'dictSelect',
    dict: 'bridge_main_superstructure_material_type'
  },
  {
    label: '桥墩形式',
    prop: 'pierType',
    placeholder: '请选择桥墩形式',
    type: 'dictSelect',
    dict: 'bridge_pier_types',
    multiple: true
  },
  {
    label: '桥墩材料',
    prop: 'pierMaterialsType',
    placeholder: '请选择桥墩材料',
    type: 'dictSelect',
    dict: 'bridge_pier_materials'
  },
  {
    label: '桥台形式',
    prop: 'abutmentType',
    placeholder: '请选择桥台形式',
    type: 'dictSelect',
    dict: 'bridge_abutment_types',
    multiple: true
  },
  {
    label: '桥台材料',
    prop: 'abutmentMaterialsType',
    placeholder: '请选择桥台材料',
    type: 'dictSelect',
    dict: 'bridge_abutment_materials'
  },
  {
    label: '基础形式',
    prop: 'baseType',
    placeholder: '请选择基础形式',
    type: 'dictSelect',
    dict: 'bridge_base_types'
  },
  {
    label: '基础材料',
    prop: 'baseMaterialsType',
    placeholder: '请选择基础材料',
    type: 'dictSelect',
    dict: 'bridge_base_materials'
  },
  {
    label: '支座形式',
    prop: 'bearingType',
    placeholder: '请选择支座形式',
    type: 'dictSelect',
    dict: 'bridge_bearing_types',
    multiple: true
  },
  {
    label: '伸缩缝类型',
    prop: 'expansionJointType',
    placeholder: '请选择伸缩缝类型',
    type: 'dictSelect',
    dict: 'bridge_expansion_joint_types',
    multiple: true
  },
  {
    label: '桥面铺装类型',
    prop: 'deckPavementType',
    placeholder: '请选择桥面铺装类型',
    type: 'dictSelect',
    dict: 'bridge_deck_pavement_types'
  }
]
// 桥梁照片
const images = [
  {
    label: '桥梁正面照',
    prop: 'frontPhotoId',
    placeholder: '请选择文件'
  },
  {
    label: '桥梁立面照',
    prop: 'facadePhotoId',
    placeholder: '请选择文件'
  },
  {
    label: '桥梁典型照片',
    prop: 'typicalPhotoId',
    placeholder: '请选择文件'
  }
]
// 档案资料
const archives = [
  {
    label: '设计图纸',
    prop: 'designPapersId',
    placeholder: '请选择文件'
  },
  {
    label: '设计文件',
    prop: 'designFilesId',
    placeholder: '请选择文件'
  },
  {
    label: '竣工图纸',
    prop: 'finishPapersId',
    placeholder: '请选择文件'
  },
  {
    label: '施工文件(含施工缺陷处理)',
    prop: 'constructFilesId',
    placeholder: '请选择文件'
  },
  {
    label: '验收文件',
    prop: 'acceptanceFilesId',
    placeholder: '请选择文件'
  },
  {
    label: '行政审批文件',
    prop: 'administrationFilesId',
    placeholder: '请选择文件'
  }
]
// 其他数据
const other = [
  {
    label: '养护检查等级',
    prop: 'maintenanceCheckGrade',
    placeholder: '请选择养护检查等级',
    type: 'dictSelect',
    dict: 'bridge_maintenance_check_grade'
  },
  {
    label: '是否跨国界',
    prop: 'isCrossBorderType',
    placeholder: '请选择是否跨国界',
    type: 'dictSelect',
    dict: 'base_data_yes_no'
  },
  {
    label: '是否危桥',
    prop: 'isDangerType',
    placeholder: '请选择是否危桥',
    type: 'dictSelect',
    dict: 'base_data_yes_no'
  },
  {
    label: '是否跨省桥梁',
    prop: 'isCrossProvinceType',
    placeholder: '请选择是否跨省桥梁',
    type: 'dictSelect',
    dict: 'base_data_yes_no'
  },
  {
    label: '路线类型',
    prop: 'routeType',
    placeholder: '请选择路线类型',
    type: 'dictSelect',
    dict: 'bridge_route_type',
    rules: [{ required: true, message: '请选择路线类型', trigger: 'change' }]
  },
  {
    label: '跨越地物类型',
    prop: 'acrossType',
    placeholder: '请选择跨越地物类型',
    type: 'dictSelect',
    dict: 'bridge_across_type',
    rules: [{ required: true, message: '请选择跨越地物类型', trigger: 'change' }]
  },
  {
    label: '跨越地物名称',
    prop: 'acrossName',
    placeholder: '请输入跨越地物名称',
    type: 'input',
    rules: [{ required: true, message: '请输入跨越地物名称', trigger: 'blur' }]
  },
  {
    label: '主桥上部构造材料',
    prop: 'mainMaterialType',
    placeholder: '请选择主桥上部构造材料',
    type: 'dictSelect',
    dict: 'bridge_main_material'
  },
  {
    label: '桥面净宽',
    prop: 'deckWidth',
    placeholder: '请输入桥面净宽',
    type: 'inputNumber',
    precision: 2,
    min: 0,
    max: 999.99
  },
  {
    label: '交通管制措施',
    prop: 'trafficMeasureType',
    placeholder: '请选择交通管制措施',
    type: 'dictSelect',
    dict: 'bridge_traffic_measure',
    rules: [{ required: true, message: '请选择交通管制措施', trigger: 'change' }]
  },
  {
    label: '单孔最大跨径',
    prop: 'maxSpan',
    placeholder: '请输入单孔最大跨径',
    type: 'inputNumber',
    precision: 3,
    min: 0,
    max: 9999.999,
    rules: [{ required: true, message: '请输入单孔最大跨径', trigger: 'blur' }]
  },
  {
    label: '跨径总长',
    prop: 'spanTotalLength',
    placeholder: '请输入跨径总长',
    type: 'inputNumber',
    precision: 3,
    min: 0,
    max: 9999.999,
    rules: [{ required: true, message: '请输入跨径总长', trigger: 'blur' }]
  },
  {
    label: '路线属性',
    prop: 'routeClassifyType',
    placeholder: '请选择路线属性',
    type: 'dictSelect',
    dict: 'bridge_route_classify',
    rules: [{ required: true, message: '请选择路线属性', trigger: 'change' }]
  },
  {
    label: '运营状态',
    prop: 'sysOperationState',
    placeholder: '请选择运营状态',
    type: 'dictSelect',
    dict: 'sys_operation_state'
  },
  {
    label: '桥梁身份码',
    prop: 'bridgeIdentityCode',
    placeholder: '请输入桥梁身份码',
    type: 'inputNumber',
    rules: [{ required: true, message: '请输入桥梁身份码', trigger: 'blur' }]
  },
  {
    label: '施工桩号',
    prop: 'constructionStake',
    placeholder: '请输入施工桩号',
    type: 'pileInput',
    precision: 3
  },
  {
    label: '统一里程桩号',
    prop: 'unifiedMileageStake',
    placeholder: '请输入养护桩号',
    type: 'pileInput',
    precision: 3
  },
  {
    label: '国高网桩号',
    prop: 'nationalNetworkStake',
    placeholder: '请输入国高网桩号',
    type: 'pileInput',
    precision: 3
  },
  {
    label: '桥梁技术状况等级评定',
    prop: 'bridgeTechAssessType',
    placeholder: '请选择桥梁技术状况等级评定',
    type: 'dictSelect',
    dict: 'bridge_tec_condition_level'
  },
  {
    label: '桥梁技术状况等级评定日期',
    prop: 'bridgeTechAssessDate',
    placeholder: '请选择桥梁技术状况等级评定日期',
    type: 'date'
  },
  {
    label: '桥梁技术状况等级评定单位',
    prop: 'bridgeTechAssessUnit',
    placeholder: '请输入桥梁技术状况等级评定单位',
    type: 'input'
  },
  {
    label: '桥梁养护工程师',
    prop: 'bridgeMaintenanceEngineer',
    placeholder: '请输入桥梁养护工程师',
    type: 'input'
  },
  {
    label: '建成通车日期',
    prop: 'buildOpenedDate',
    placeholder: '请选择建成年限/时间',
    type: 'date'
  },
  {
    label: '使用年限',
    prop: 'bridgeYearUsed',
    placeholder: '请选择使用年限',
    type: 'dictSelect',
    dict: 'bridge_year_used'
  },
  {
    label: '监管单位',
    prop: 'superUnitName',
    placeholder: '请输入监管单位',
    type: 'input'
  },
  {
    label: '桥下通航等级及标准净空(m)',
    prop: 'underStandardClearance',
    placeholder: '请输入桥下通航等级及标准净空',
    type: 'inputNumber',
    precision: 2
  },
  {
    label: '移交管理单位',
    prop: 'transferManagementUnit',
    placeholder: '请输入移交管理单位',

    type: 'input',
  },

  {
    label: '备注',
    prop: 'remark',
    placeholder: '请输入备注',
    type: 'inputTextarea',
    span:24
  },
]

export default {
  administrative,
  technology,
  information,
  images,
  archives,
  other
}
