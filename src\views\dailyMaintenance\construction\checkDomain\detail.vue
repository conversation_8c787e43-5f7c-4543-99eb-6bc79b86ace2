<template>
  <div class="app-container">
    <el-form ref="elForm" :model="formData" :inline="true" label-width="200px" v-loading="loading">
      <el-col :span="24" style="margin-bottom: 18px">
        <div class="card_title">事件信息</div>
        <el-card class="box-card" shadow="never">
          <el-descriptions :column="4" size="mini" border>
            <el-descriptions-item label="事件编码">
              {{disData.disCode}}
            </el-descriptions-item>
            <el-descriptions-item label="采集时间">
              {{disData.collectTime}}
            </el-descriptions-item>
            <el-descriptions-item label="起点桩号">
              {{formatPile(disData.beginMile)}}
            </el-descriptions-item>
            <el-descriptions-item label="终点桩号">
              {{formatPile(disData.endMile)}}
            </el-descriptions-item>
            <el-descriptions-item label="事件描述">
              {{disData.disDesc}}
            </el-descriptions-item>
          </el-descriptions>
        </el-card>
      </el-col>
      <el-row :gutter="20">
        <el-col :span="24" style="margin-bottom: 18px">
          <div class="card_title">施工前采集照片</div>
          <el-card class="box-card" shadow="never">
            <el-form-item label="">
              <sort-file-upload v-model="sgqcjzp" :forView="true"></sort-file-upload>
            </el-form-item>
          </el-card>
        </el-col>
        <el-col :span="24" style="margin-bottom: 18px">
          <div class="card_title">完工时照片</div>
          <el-card class="box-card" shadow="never">
            <el-form-item label="">
              <sort-file-upload v-model="wgsfj" can-sort :forView="true" ref="wgsfj"></sort-file-upload>
            </el-form-item>
          </el-card>
        </el-col>
        <el-col :span="24">
          <div class="card_title">施工简图</div>
          <el-card class="box-card" shadow="never">
            <el-form-item label="">
              <sort-file-upload v-model="sgjt" can-sort :forView="true" ref="sgjt"></sort-file-upload>
            </el-form-item>
          </el-card>
        </el-col>
        <el-col :span="24" style="margin-bottom: 18px">
          <div class="card_title">施工附件</div>
          <el-card class="box-card" shadow="never">
            <el-form-item label="">
              <sort-file-upload v-model="sgfj" :forView="true"></sort-file-upload>
            </el-form-item>
          </el-card>
        </el-col>
        <el-col :span="24">
          <div class="card_title">完工附件</div>
          <el-card class="box-card" shadow="never">
            <el-form-item label="">
              <sort-file-upload v-model="wgfj" can-sort :forView="true" ref="wgfj"></sort-file-upload>
            </el-form-item>
          </el-card>
        </el-col>
        <el-col :span="24" style="margin-bottom: 18px">
          <div class="card_title">施工单位审核附件</div>
          <el-card class="box-card" shadow="never">
            <el-form-item label="">
              <sort-file-upload v-model="sgdwshfj" can-sort ref="sgdwshfj"></sort-file-upload>
            </el-form-item>
          </el-card>
        </el-col>
        <el-col :span="24" style="margin-top: 18px">
          <div class="card_title" style="display: flex;width: 100%;justify-content: space-between">
            <div>事件方法数量</div>
            <div style="font-size: 16px">总金额：{{total}}</div>
          </div>
          <el-table v-adjust-table
              :data="methodList"
              border
              height="200px"
              ref="tableRef"
              style="width: 100%">
            <el-table-column
                label="序号"
                align="center"
                type="index"
                width="50"
            />
            <el-table-column
                prop="schemeCode"
                align="center"
                label="子目号">
            </el-table-column>
            <el-table-column
                prop="schemeName"
                align="center"
                label="子目名称">
            </el-table-column>
            <el-table-column
                prop="unit"
                align="center"
                label="单位">
            </el-table-column>
            <el-table-column
                prop="price"
                align="center"
                label="单价">
            </el-table-column>
            <el-table-column
                prop="calcDesc"
                align="center"
                label="计算式">
            </el-table-column>
            <el-table-column
                prop="num"
                align="center"
                label="方法数量">
            </el-table-column>
            <el-table-column
                prop="amount"
                align="center"
                label="资金">
            </el-table-column>
            <el-table-column
                prop="remark"
                align="center"
                label="备注">
            </el-table-column>
          </el-table>
        </el-col>
        <el-col :span="24" style="margin-top: 18px">
          <div class="card_title" style="display: flex;width: 100%;justify-content: space-between">
            <div>计算式及说明</div>
            <el-link type="primary" @click="generateInstructions">生成计算式说明</el-link>
          </div>
          <el-input class="calculation_desc" v-model="formData.calculationDesc" readonly type="textarea" :rows="4">
          </el-input>
        </el-col>
        <el-col :span="24" style="margin-top: 18px">
          <div class="card_title">审核意见</div>
          <el-input v-model="formData.remark" type="textarea" :rows="4">
          </el-input>
        </el-col>
      </el-row>
      <div style="text-align: right;padding-right: 7.5px;margin-top: 18px">
        <el-button v-has-menu-permi="['checkDomain:construction:review']" type="primary" @click="onReview">通 过</el-button>
        <el-button v-has-menu-permi="['checkDomain:construction:reject']" type="danger" @click="onReject">驳 回</el-button>
        <el-button @click="onClose">退出</el-button>
      </div>
    </el-form>
  </div>
</template>
<script>
import {
  getConstructionAuditById,
  submitConstructionAudit,
  rejectConstructionAudit
} from "@/api/dailyMaintenance/construction/checkDomain"
import sortFileUpload from "@/components/SortFileUpload/index.vue";
import {getDiseaseDataById} from "@/api/dailyMaintenance/eventManage/eventData";
import {formatPile} from "../../../../utils/ruoyi";

export default {
  name: "index",
  components: {sortFileUpload},
  data() {
    return {
      formData: {
        calculationDesc: ''
      },
      sgqcjzp: '',
      sgjt: '',
      sgfj: '',
      wgfj: '',
      wgsfj: '',
      sgdwshfj: '',
      total: 0,
      taskId: '',
      loading: false,
      methodList: [],
      urgentDegreeOptions: [{
        "label": "计算",
        "value": 1
      }, {
        "label": "不计算",
        "value": 2
      }],
      defaultProps: {
        children: 'children',
        label: 'schemeName'
      },
      disData: {}
    }
  },
  props: {
    rowData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  watch: {
    rowData: {
      handler(val) {
        if (val.id) {
          this.taskId = val.taskId
          this.getDetail(val.id)
        }
      },
      immediate: true
    },
    filterText(val) {
      this.$refs.tree.filter(val);
    }
  },
  mounted() {
  },
  methods: {
    formatPile,
    getDetail(id) {
      this.loading = true
      getConstructionAuditById(id).then(res => {
        const detail = res.data
        this.formData = JSON.parse(JSON.stringify(detail))
        this.formData.taskId = this.taskId
        this.formData.detailId = this.rowData.detailId
        this.methodList = detail.methodList
        this.methodList.forEach(item => {
          if ((item.amount == null || item.amount == undefined) && typeof item.price === 'number' && typeof item.num === 'number') item.amount = Math.round(item.price * item.num)
        })
        getDiseaseDataById(detail.disId).then(res => {
          this.disData = res.data
        })
        this.total = this.methodList.reduce((acc, curr) => Number(acc) + Number(curr.amount || 0), 0)

        const sgqcjzp = detail.registerAttList.filter(item => item.registerType == 0)
        this.sgqcjzp = sgqcjzp.map(item => item.fileId)

        const sgjt = detail.attList.filter(item => item.registerType == 1)
        this.sgjt = sgjt.map(item => item.fileId)

        const sgfj = detail.attList.filter(item => item.registerType == 2)
        this.sgfj = sgfj.map(item => item.fileId)

        const wgfj = detail.attList.filter(item => item.registerType == 5)
        this.wgfj = wgfj.map(item => item.fileId)

        const wgsfj = detail.attList.filter(item => item.registerType == 6)
        this.wgsfj = wgsfj.map(item => item.fileId)

        const sgdwshfj = detail.attList.filter(item => item.registerType == 8)
        this.sgdwshfj = sgdwshfj.map(item => item.fileId)
      }).finally(() => {
        this.loading = false
      })
    },
    onReview() {
      try {
        this.generateParams()
      } catch (e) {
        console.error('Error occurred while generating params:', e)
        return;
      }
      // if (!this.wgsfj || this.wgsfj.length == 0) {
      //   this.$message.warning('请上传完工时照片')
      //   return
      // }
      // if (!this.formData.methodList || this.formData.methodList.length == 0 || !this.formData.calculationDesc) {
      //   this.$message.warning('计算式、方法不能为空')
      //   return
      // }
      if (!this.formData.remark) {
        this.$message.warning('请填写审核意见')
        return
      }
      this.loading = true
      submitConstructionAudit(this.formData).then(res => {
        this.$message.success('保存成功')
        this.onClose()
      }).finally(() => {
        this.loading = false
      })
    },
    onReject() {
      try {
        this.generateParams()
      } catch (e) {
        console.error('Error occurred while generating params:', e)
        return;
      }
      // if (!this.wgsfj || this.wgsfj.length == 0) {
      //   this.$message.warning('请上传完工时照片')
      //   return
      // }
      // if (!this.formData.methodList || this.formData.methodList.length == 0 || !this.formData.calculationDesc) {
      //   this.$message.warning('计算式、方法不能为空')
      //   return
      // }
      if (!this.formData.remark) {
        this.$message.warning('请填写审核意见')
        return
      }
      this.loading = true
      rejectConstructionAudit(this.formData).then(res => {
        this.$message.success('保存成功')
        this.onClose()
      }).finally(() => {
        this.loading = false
      })
    },
    // 拼接参数
    generateParams() {
      this.$refs.wgsfj.save()
      this.$refs.sgjt.save()
      this.$refs.wgfj.save()
      this.$refs.sgdwshfj.save()
      this.methodList = this.methodList.map(item => ({
        ...item,
        schemeId: item.schemeId ? item.schemeId : item.id
      }))
      // 拼接参数
      this.formData.methodList = this.methodList
      const finishedAttList = []
      const registerAttList = []
      const checkAttList = []
      if (this.sgqcjzp) {
        this.sgqcjzp.forEach((item, index) => {
          registerAttList.push({
            fileId: item,
            indexOrder: index,
            registerType: 0
          })
        })
      }
      if (this.sgjt) {
        this.sgjt.forEach((item, index) => {
          finishedAttList.push({
            fileId: item,
            indexOrder: index,
            registerType: 1
          })
        })
      }
      if (this.sgfj) {
        this.sgfj.forEach((item, index) => {
          registerAttList.push({
            fileId: item,
            indexOrder: index,
            registerType: 2
          })
        })
      }
      if (this.wgfj) {
        this.wgfj.forEach((item, index) => {
          finishedAttList.push({
            fileId: item,
            indexOrder: index,
            registerType: 5
          })
        })
      }
      if (this.wgsfj) {
        this.wgsfj.forEach((item, index) => {
          finishedAttList.push({
            fileId: item,
            indexOrder: index,
            registerType: 6
          })
        })
      }
      if (this.sgdwshfj) {
        this.sgdwshfj.forEach((item, index) => {
          checkAttList.push({
            fileId: item,
            indexOrder: index,
            registerType: 8,
            remark: '施工单位审核'
          })
        })
      }
      this.formData.registerAttList = registerAttList
      this.formData.finishedAttList = finishedAttList
      this.formData.checkAttList = checkAttList
    },
    checkLib() {
      let checkDatas = this.$refs.tree.getCheckedNodes()
      checkDatas = checkDatas.filter(item => {
        return item.nodeType == 2 && !this.methodList.some(filterItem => filterItem.id === item.id)
      })
      this.methodList.push(...checkDatas)

      this.libModel = false
    },
    onClose() {
      this.$emit("close")
    },
    // 生成计算式说明
    generateInstructions() {
      let calculationDesc = ''
      this.methodList.forEach(item => {
        if (item.schemeName != '安全生产费' && item.schemeName != '安全保通费')
        calculationDesc += `${item.schemeName || ''}:(${item.calcDesc || item.num || ''})=${item.num || 0}${item.unit || ''}\n`
      })
      this.$set(this.formData, 'calculationDesc', calculationDesc)
    },
    generateUUID() {
      return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {
        var r = crypto.getRandomValues(new Uint8Array(1))[0] % 16 | 0;
        var v = c === 'x' ? r : (r & 0x3 | 0x8);
        return v.toString(16);
      }).slice(0, 20); // 截取前 20 位
    }
  }
}
</script>
<style scoped lang="scss">
.card_title {
  width: 200px;
  text-align: left;
  margin-bottom: 15px;
  font-weight: bold;
}
</style>
<style lang="scss" scoped>
@import "@/assets/styles/business.scss";
</style>
