<template>
  <div ref="fullRef">
    <Home @clickL="onLclick" v-show="!componentL" @fullClick="onFull" />
    <!-- <Subpage :title="title" :subTitle="subTile" :pageLId="componentL" :pageRId="componentR" v-if="componentL"
      @back="onBack" /> -->
  </div>
</template>

<script>
import screenfull from 'screenfull';
import Home from './components/home.vue';
import Subpage from './components/subpage.vue';
import { Loading } from 'element-ui';

export default {
  name: 'index',
  components: {
    Home,
    Subpage,
  },
  data() {
    return {
      componentL: '',
      componentR: '',
      title: '',
      subTile: '',
      loading: false,
      isFull: false,
    };
  },
  methods: {
    onLclick(obj) {
      this.$modal.loading();
      this.componentL = obj.componentL;
      this.componentR = obj.componentR;
      this.title = obj.title;
      this.subTile = obj.subText;
      this.$modal.closeLoading();
    },
    onBack() {
      this.componentL = '';
      this.componentR = '';
      this.title = '';
      this.subTile = '';
    },
    onFull(e) {
      this.isFull = e;
      let fullDom = this.$refs.fullRef;
      if (fullDom) screenfull.toggle(fullDom);

      if (!document.fullscreenElement) {
        screenfull.request();
      } else {
        screenfull.exit();
      }
    },
    handleKeyDown(event) {
      console.log(event);
      if (event.keyCode === 27) {
        screenfull.exit();
      }
    },
    change() {
      // this.isFullscreen = screenfull.isFullscreen;
    },
  },
  mounted() {
    screenfull.on('change', this.change);
    document.addEventListener('keydown', this.handleKeyDown);
    this.$nextTick(() => {
      let parentEl = this.$refs.fullRef.parentNode;
      parentEl.style.overflow = 'auto';
      parentEl.style.overflowX = 'hidden';
    });
  },
  unmounted() {
    screenfull.off('change', this.change);
    document.removeEventListener('keydown', this.handleKeyDown);
    let parentEl = this.$refs.fullRef.parentNode;
    parentEl.style.overflow = 'hidden';
  },
};
</script>

<style lang="scss" scoped></style>
