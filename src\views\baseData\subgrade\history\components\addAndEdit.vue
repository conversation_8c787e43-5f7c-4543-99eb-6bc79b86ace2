<template>
  <div class="roadbedadd">
    <el-dialog
      :title="title"
      :visible.sync="showAddEdit"
      width="60%"
      append-to-body
      :before-close="handleClose"
      :close-on-click-modal="false"
      :class="title=='查看路基数据'?'roadbedDialog':''"
    >

      <div style="height: 60vh;overflow-y: auto;padding: 0 10px 0 5px;">
        <el-form
          ref="baseInfoData"
          :model="baseInfoForm"
          label-width="190px"
          :disabled="title=='查看路基数据'?true:false"
          :style="{pointerEvents: forView ? 'none' : ''}"
        >
          <el-row :gutter="10">
            <el-col
              :span="12"
              v-for="(i,idx) in baseInfoData"
              :key="idx"
            >
              <el-form-item
                :label="i.label"
                :prop="i.prop"
                :rules="i.rules"
              >
                <span v-if="i.type === 'input'">
                  <el-input
                    v-model="baseInfoForm[i.prop]"
                    :placeholder="i.placeholder"
                    clearable
                  />
                </span>
                <span v-if="i.type === 'pileInput'">
                  <PileInput
                    v-model="baseInfoForm[i.prop]"
                    :placeholder="i.placeholder"
                  />
                </span>
                <span v-else-if="i.type === 'inputNumber'">
                  <el-input-number
                    style="width: 100%"
                    v-model="baseInfoForm[i.prop]"
                    :precision="i.precision"
                    @change="changeInputNumber(i.precision,'baseInfoForm',i.prop)"
                    clearable
                  ></el-input-number>
                </span>
                <span v-else-if="i.type === 'select'">
                  <div v-if="i.dict">
                    <el-select
                      style="width: 100%"
                      v-model="baseInfoForm[i.prop]"
                      placeholder="请选择"
                      :disabled="i.disabled"
                      clearable
                      @change="changeSelect($event, i)"
                    >
                      <el-option
                        v-for="dict in dict.type[i.dict]"
                        :key="dict.value"
                        :label="dict.label"
                        :value="dict.value"
                      >
                      </el-option>
                    </el-select>

                  </div>
                  <div v-else-if="i.deptType">
                    <SelectTree
                      v-model="baseInfoForm[i.prop]"
                      :dept-type="i.deptType"
                      clearable
                      @input="deptChange($event, i)"
                    />

                  </div>

                  <div v-else>
                    <el-select
                      style="width:100%"
                      v-model="baseInfoForm[i.prop]"
                      :placeholder="i.placeholder"
                      clearable
                      filterable
                      :disabled="baseInfoForm.id?false:i.disabled"
                    >
                      <el-option
                        v-for="(v,index) in i.options"
                        :key="index"
                        :label="v[i.optionLabel]"
                        :value="v[i.optionValue]"
                      />
                    </el-select>
                  </div>
                </span>
                <div v-else-if="i.type =='uploadImg'">
                  <ImageUpload
                    :key="i.ownerId"
                    v-model="baseInfoForm[i.prop]"
                    :limit="1"
                    :owner-id="i.ownerId"
                    storage-path="/base/subgrade/baseInfo"
                  />

                </div>
                <span v-else-if="i.type === 'date'">
                  <el-date-picker
                    style="width:100%"
                    v-model="baseInfoForm[i.prop]"
                    type="date"
                    :placeholder="i.placeholder"
                    clearable
                    value-format="yyyy-MM-dd"
                  />
                </span>
                <span v-else-if="i.type === 'tree'">
                  <select-tree
                    v-model="baseInfoForm[i.prop]"
                    clearable
                  ></select-tree>
                </span>
                <span v-else-if="i.type === 'sectionSelect'">
                  <section-select
                    v-model="baseInfoForm[i.prop]"
                    :formObject="baseInfoForm"
                    :sectionId="sectionId?sectionId:baseInfoForm.maintenanceSectionId"
                    clearable
                    :disabled="baseInfoForm.id?false:i.disabled"
                  ></section-select>
                </span>
                <span v-else-if="i.type === 'startcoordinate'">
                  <div
                    class="flex-between"
                    style="display: inline-flex;"
                  >
                    <el-input
                      v-model="baseInfoForm.startLongitude"
                      type="number"
                      placeholder="经度"
                      class="longitude-latitude"
                    >
                      <template slot="prepend">Lon</template>
                    </el-input>
                    <el-input
                      v-model="baseInfoForm.startLatitude"
                      type="number"
                      placeholder="纬度"
                      class="longitude-latitude"
                    >
                      <template slot="prepend">Lat</template>
                    </el-input>
                  </div>
                </span>
                <span v-else-if="i.type === 'endcoordinate'">
                  <div
                    class="flex-between"
                    style="display: inline-flex;"
                  >
                    <el-input
                      v-model="baseInfoForm.endLongitude"
                      type="number"
                      placeholder="经度"
                      class="longitude-latitude"
                    >
                      <template slot="prepend">Lon</template>
                    </el-input>
                    <el-input
                      v-model="baseInfoForm.endLatitude"
                      type="number"
                      placeholder="纬度"
                      class="longitude-latitude"
                    >
                      <template slot="prepend">Lat</template>
                    </el-input>
                  </div>
                </span>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>
      <div
        slot="footer"
        class="dialog-footer"
      >
        <el-button
          v-if="!forView"
          type="primary"
          @click="handleSubmit('submit')"
        >提 交</el-button>
        <el-button
          v-if="!forView"
          type="primary"
          @click="handleSubmit('save')"
        >暂 存</el-button>
        <el-button @click="handleClose">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import dictionary from '../dataDictionary'
import {
  subgradeAdd,
  addStatic,
  subgradeEdit,
  subgradeTempAdd
} from '@/api/baseData/subgrade/baseInfo/index'

import { getListPage } from '@/api/baseData/subgrade/baseInfo/manageList.js'
import { maintenanceSectionList } from '@/api/baseData/subgrade/baseInfo/getSections'

import { listMaintenanceSectionAll } from '@/api/system/maintenanceSection'

import { createIdWorker } from '@/api/baseData/common'
import { listAllHighwaySections } from '@/api/system/highwaySections.js'
import SelectTree from '@/components/DeptTmpl/selectTree'
import SectionSelect from '@/components/SectionSelect'
import { listAllRoute } from '@/api/system/route.js'
import '@riophae/vue-treeselect/dist/vue-treeselect.css'
import Treeselect from '@riophae/vue-treeselect'
import manageList from './manageList.vue'
import { deptTreeSelect } from '@/api/tmpl'
import PileInput from '@/components/PileInput/index.vue'
import { listByMaintenanceSectionId } from '@/api/baseData/common/routeLine'

const apis = {
  listAllHighwaySections: listAllHighwaySections,
  listAllRoute: listAllRoute,
  maintenanceSectionList: maintenanceSectionList,
  listMaintenanceSectionAll: listMaintenanceSectionAll,
  listByMaintenanceSectionId: listByMaintenanceSectionId
}

export default {
  name: 'subgrade-baseInfo-addAndEdit',
  components: { SelectTree, Treeselect, SectionSelect, manageList, PileInput },
  props: {
    formData: {
      default: {}
    },
    showAddEdit: {
      default: false
    },
    title: {
      default: '添加路基静态数据'
    },
    forView: {
      default: false
    }
  },
  dicts: [
    'roadbed_maintenance_grade',
    'sys_route_direction',
    'roadbed_length_classification',
    'roadbed_assessment_grade',
    'base_data_yes_no',
    'sys_operation_state',
    'roadbed_lining_type',
    'sys_route_type',
    'roadbed_hole_type_code',
    'roadbed_hole_style',
    'roadbed_civil_grade',
    'roadbed_other_grade',
    'roadbed_electrical_grade',
    'roadbed_other_grade',
    'roadbed_maintenance_unit_category',
    'left_right',
    'lane',
    'bridge_route_level',
    'subgrade_suface_type',
    'sys_route_grade',
    'roadbed_section_tech_level',
    'roadbed_surface_type',
    'sys_surface_type'
  ],
  data() {
    return {
      activeName: 'baseInfoData',
      sectionId: '',
      baseInfoData: [],

      baseInfoForm: {},

      form: {},
      types: 101
    }
  },
  created() {
    this.baseInfoForm = this.formData.baseInfoForm
      ? this.formData.baseInfoForm
      : {}
  },
  mounted() {
    this.initFormSlections()
  },
  methods: {
    deptChange(e, i) {
      if (i.prop == 'managementMaintenanceId') {
      }
    },

    // 获取表单字段以及表单选项列表
    initFormSlections() {
      // 行政识别数据

      this.$nextTick(() => {
        this.baseInfoData = JSON.parse(JSON.stringify(dictionary.baseInfoData))

        this.baseInfoData.map(async (el, i) => {
          if (el.type == 'uploadImg') {
            // 获取图片ownerId

            createIdWorker().then(res => {
              if (res.code === 200) {
                this.$nextTick(() => {
                  el.ownerId = Number(res.data)
                })
              }
            })
          }

          if (el.api && el.type == 'select') {
            const r = await apis[el.api](
              [this.baseInfoForm.managementMaintenanceId] || ['']
            )
            // 临时编码，返回的数据不合规范，数组去重
            if (r.row || r.data) {
              r.list = Array.from(
                new Set((r.row || r.data).map(JSON.stringify))
              ).map(JSON.parse)
            }
            el.options = r.list
          }
        })
      })
      // 结构技术数据
    },

    changeSelect(e, i) {
      this.$forceUpdate()
      if (i.prop == 'sectionType') {
        const item = this.baseInfoData.find(obj => obj.prop === 'sectionType')
        item.disabled = false
      }
    },

    handleClick(tab) {},
    getManageListPage() {
      getListPage({
        roadbedId: this.baseInfoForm.id ? this.baseInfoForm.id : ''
      }).then(response => {
        this.manageListData = response.rows
      })
    },
    async handleSubmit(type) {
      const formNames = ['baseInfoData']
      switch (type) {
        case 'submit':
          this.form.isStaging = false
          break
        case 'save':
          this.form.isStaging = true
          break
      }

      if (!this.form.isStaging) {
        for (let index = 0; index < formNames.length; index++) {
          const element = formNames[index]
          const valid = await new Promise(resolve => {
            this.$refs[element].validate(valid => {
              resolve(valid)
            })
          })
          if (!valid) {
            // 如果表单校验不通过，定位到对应的tab
            this.activeName = element
            return // 中止提交操作
          }
        }
      }

      let listBase = JSON.parse(JSON.stringify(this.baseInfoForm))

      this.baseInfoData.map(el => {
        if (
          el.type == 'pileInput' &&
          el.precision &&
          this.baseInfoForm[el.prop]
        ) {
          listBase[el.prop] = this.baseInfoForm?.[el.prop]
            ?.toFixed(el.precision)
            .toString()
        }

        if (el.type == 'uploadImg') {
          listBase[el.prop] = this.baseInfoForm[el.prop]
            ? this.baseInfoForm[el.prop][0]
            : ''
        }

        if (el.prop == 'routeId' && this.baseInfoForm[el.prop]) {
          this.baseInfoData
            .find(e => e.prop == 'routeId')
            .options.map(li => {
              if (li.roadSectionId == this.baseInfoForm[el.prop]) {
                listBase.routeName = li.roadSectionName
              }
            })
        }

        if (el.prop == 'sectionType' && this.baseInfoForm[el.prop]) {
          // listBase.sectionType=this.baseInfoForm[el.prop][0]
          // if(this.baseInfoForm[el.prop][0]=='2'&&this.baseInfoForm[el.prop].length>1){
          //   listBase.belongConnectingLineId=this.baseInfoForm[el.prop][1]
          //   listBase.belongRampId=''
          // }else if(this.baseInfoForm[el.prop][0]=='3'&&this.baseInfoForm[el.prop].length>1){
          //   listBase.belongRampId=this.baseInfoForm[el.prop][1]
          //   listBase.belongConnectingLineId=''
          // }
        }
      })

      if (this.form.isStaging) {
        subgradeTempAdd(listBase).then(response => {
          this.$modal.msgSuccess(
            this.form.isStaging == true ? '暂存成功' : '新增成功'
          )
          this.$emit('refresh')
          this.initFormSlections()
        })
      } else {
        if (this.baseInfoForm.id != null) {
          this.$modal.loading()
          subgradeEdit(listBase)
            .then(response => {
              this.$modal.msgSuccess(
                this.form.isStaging == true ? '暂存成功' : '新增成功'
              )
              this.$emit('refresh')
              this.initFormSlections()
            })
            .finally(() => {
              this.$modal.closeLoading()
            })
        } else {
          subgradeAdd(listBase).then(response => {
            this.$modal.msgSuccess(
              this.form.isStaging == true ? '暂存成功' : '新增成功'
            )
            this.$emit('refresh')
            this.initFormSlections()
          })
        }
      }
    },

    changeInputNumber(precision, info, val) {
      let num = parseFloat(this[info][val]).toFixed(precision)
      this.$nextTick(() => {
        this[info][val] = num
      })
    },
    handleClose() {
      if (this.forView) {
        this.form = {}
        this.$emit('close', false)
      } else {
        this.$modal
          .confirm('确认退出？')
          .then(() => {
            this.form = {}
            this.$emit('close', false)
            this.initFormSlections()
          })
          .catch(() => {})
      }
    }
  },
  watch: {
    'baseInfoForm.managementMaintenanceId'(newVal, oldVal) {
      const item = this.baseInfoData.find(
        obj => obj.prop === 'maintenanceSectionId'
      )
      if (newVal && item) {
        item.disabled = false
        listMaintenanceSectionAll({ departmentId: newVal }).then(res => {
          if (res.code == 200) {
            item.options = res.data
          }
        })
      }
    },
    'baseInfoForm.maintenanceSectionId'(newVal, oldVal) {
      const item = this.baseInfoData.find(obj => obj.prop === 'sectionType')
      const routeCodeItem = this.baseInfoData.find(
        obj => obj.prop === 'routeCode'
      )
      if (newVal && item) {
        item.disabled = false
        this.sectionId = newVal
      }

      if (routeCodeItem && newVal) {
        routeCodeItem.disabled = false
        listByMaintenanceSectionId({ maintenanceSectionId: newVal }).then(
          res => {
            if (res.code == 200) {
              routeCodeItem.options = res.data || []
            }
          }
        )
      }
    }
  }
}
</script>

<style  scoped>
.roadbedDialog ::v-deep .el-input.is-disabled .el-input__inner {
  background-color: white;
  border-color: #dfe4ed;
  color: black;
}

.longitude-latitude {
  ::v-deep {
    .el-input-group__prepend {
      padding: 0 12px;
    }
    .el-input__inner {
      padding: 0 5px;
    }
  }
}
</style>
