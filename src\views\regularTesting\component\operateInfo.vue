<template>
  <div class="app-container maindiv">
    <el-row :gutter="20">
      <el-col :span="24" :xs="24">
        <!--数据表格开始-->
        <div class="tableDiv">
          <el-table v-adjust-table
            stripe
            size="mini"
            height="500px"
            style="width: 100%"
            v-loading="loading"
            border
            :data="dataList"
          >
            <el-table-column
              fixed
              label="序号"
              type="index"
              width="50"
            ></el-table-column>
            <template v-for="(column, index) in columns">
              <el-table-column
                v-if="column.visible"
                :label="column.label"
                :prop="column.field"
                :width="column.width"
                align="center"
              >
                <template slot-scope="scope">
                  <dict-tag
                    v-if="column.dict"
                    :options="dict.type[column.dict]"
                    :value="scope.row[column.field]"
                  />
                  <template v-else-if="column.slots">
                    <RenderDom
                      :index="index"
                      :render="column.render"
                      :row="scope.row"
                    />
                  </template>
                  <span v-else-if="column.isTime">{{
                    parseTime(scope.row[column.field], "{y}-{m}-{d}")
                  }}</span>
                  <span v-else>{{ scope.row[column.field] }}</span>
                </template>
              </el-table-column>
            </template>
          </el-table>
        </div>
        <!--数据表格结束-->
      </el-col>
    </el-row>
  </div>
</template>

<script>
export default {
  name: "operateInfo",
  components: {
    RenderDom: {
      functional: true,
      props: {
        row: Object,
        index: Number,
        render: Function,
      },
      render(createElement, ctx) {
        const { row, index } = ctx.props;
        return ctx.props.render(row, index);
      },
    },
  },
  props: {
    businessKey: {
      type: String,
      default: "",
    },
    getNodeInfo: {
      type: Function,
    },
    paramName: {
      type: String,
      default: "businessKey",
    },
  },
  data() {
    return {
      loading: false,
      queryParams: {
        pageNum: 1,
        pageSize: 3,
      },
      // 列信息
      columns: [
        { key: 0, field: "nodeName", label: `操作节点`, visible: true },
        {
          key: 1,
          field: "nodeName",
          label: `操作类型`,
          visible: true,
          slots: true,
          render: (row, index) => {
            return row.direction ? <span>通过</span> : <span>驳回</span>;
          },
        },
        { key: 2, field: "assigneeName", label: `操作人`, visible: true },
        { key: 3, field: "endTime", label: `操作时间`, visible: true },
        { key: 4, field: "comment", label: `操作意见`, visible: true },
        {
          key: 5,
          field: "direction",
          label: `方向`,
          visible: true,
          slots: true,
          render: (row, index) => {
            return row.direction ? <span style="color: green">通过</span> : <span style="color: red">驳回</span>;
          },
        },
      ],
      // 表格数据
      dataList: [],
    };
  },
  watch: {},
  mounted() {
    if (this.businessKey) {
      this.loading = true;
      const params = {};
      params[this.paramName] = this.businessKey;
      this.getNodeInfo(params).then((res) => {
        this.dataList = res.data;
        this.loading = false;
      });
    }
  },
};
</script>
<style></style>
<style lang="scss" scoped>
@import "@/assets/styles/business.scss";
</style>
