<template>
  <el-col :span="realNav ? 5 : 0" :xs="24" class="leftDiv">
        <!--折叠图标-->
        <div class="leftIcon" @click="closeNav" v-show="realNav">
          <span class="el-icon-caret-left"></span>
        </div>

        <div class="left-tree">
          <div class="head-container">
            <el-input
                v-model="keyword"
                placeholder="输入关键词检索"
                clearable
                size="small"
                prefix-icon="el-icon-search"
                style="margin-bottom: 20px"
            />
          </div>
          <div class="head-container" style="width: 300px;overflow-x: auto;">
            <el-tree
                :data="realOptions"
                :expand-on-click-node="false"
                :filter-node-method="filterNode"
                ref="tree"
                node-key="id"
                highlight-current
                @node-click="handleNodeClick"
            >
            </el-tree>
          </div>
        </div>
      </el-col>
</template>
<script>
import { findUserDeptMaintenanceList2 } from "@/api/system/maintenanceSection";

export default {
  data() {
    return {
      keyword: '',
      realOptions: [],

    }
  },
  created() {
    this.getDeptTree()
  },
  props: {
    realNav: {
      type: Boolean,
      default: true
    }
  },
  watch: {
    keyword(val) {
        this.$refs.tree.filter(val);
      }
  },
  methods: {
    // 筛选节点
    filterNode(value, data) {
      if (!value) return true;
      return data.label.indexOf(value) !== -1;
    },
    // 查询部门下拉树结构
    getDeptTree() {
      findUserDeptMaintenanceList2().then(response => {
        const treeData = response.data
        treeData.forEach(item => {
          getChild(item)
        })
        function getChild(node) {
          node.label = node.deptName || node.maintenanceSectionName
          node.id = node.deptId || node.maintenanceSectionId
          if (node.children) {
            node.children.forEach(item => {
              getChild(item)
            })
          }
        }
        // 增加一个最顶级
        const tree = [
          {
            label: "云南省交通投资建设集团有限公司",
            id: "1",
            children: [...treeData],
          },
        ];
        this.realOptions = tree
      });
    },
    handleNodeClick(e) {
      if (e.deptId || e.departmentId) {
        if (e.deptId) {
          this.$emit('rowClick',{ domainId: String(e.id) })
        } else if (e.departmentId) {
          this.$emit('rowClick',{ domainId: String(e.departmentId),maiSecId: e.id })
        }
      } else {
        this.$emit('rowClick', {})
      }
    },
    closeNav() {
      this.$emit('closeNav')
    }
  }
}
</script>
<style scoped lang="scss">
.leftDiv {
  float: none;
  display: flex;
  position: relative;
  padding-top: 10px;
  background-color: white;
  border-right: 1px solid #d8dce5;
}

.left-tree {
  width: 100%;
  height: 100%;
  overflow: auto;
}

.leftIcon {
  border: 1px solid #dcdfe6;
  border-radius: 8px;
  width: 16px;
  height: 50px;
  line-height: 50px;
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
  position: absolute;
  right: 0;
  top: 300px;
  z-index: 2;
}

.leftIcon:hover {
  background-color: #dcdfe6;
}
</style>
