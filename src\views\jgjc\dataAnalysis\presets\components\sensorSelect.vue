<template>
  <div style='display: flex'>
    <el-select
      size='small'
      class='monitor-content'
      v-model='internalSensorList'
      :placeholder="'请选择'"
      style='width: 380px'
      no-data-text='无数据'
      multiple
      filterable
      collapse-tags
      @change='handleChange'
    >
      <el-option
        v-for='item in internalSensorList'
        :key='item.dataCode'
        :label='item.label'
        :value='item'
      >
        <span style='float: left'>{{ item.dataCode }}</span>
        <span style='float: right; color: #8492a6; font-size: 13px'>{{ item.label }}</span>
      </el-option>
    </el-select>
    <el-button
      icon='el-icon-plus'
      size='small'
      style='margin-left: 10px;'
      type='primary'
      @click='addNewSensor'
    >新增
    </el-button>
    <el-dialog :visible.sync='sensorSelectDialogVisible' width='550px' center @close='closeDialog' append-to-body>
      <span slot='title' class='title'>新增传感器</span>
      <div>
        <el-form
          :model='addingNode'
          :inline='true'
          label-position='right'
          label-width='140px'
          label-suffix='：'
          :rules='formRules'
        >
          <el-form-item label='传感器名称' prop='addSensorNodeInfoLabel' :required='true' error='传感器名称不可为空'>
            <el-input
              size='small'
              class='monitor-content'
              v-model='addingNode.addSensorNodeInfoLabel'
              style='width: 240px'
              placeholder='请输入内容'
              disabled
            ></el-input>
            <el-button
              icon='el-icon-plus'
              size='small'
              class='btnAddBg'
              style='margin-left: 10px;'
              @click='selectFromTree'
            >选择
            </el-button>
          </el-form-item>
          <el-form-item label='显示图例' prop='sensorLabel' :required='true' error='显示图例不可为空'>
            <el-input
              size='small'
              class='monitor-content'
              v-model='addingNode.sensorLabel'
              style='width: 240px'
              placeholder='请输入内容'
            ></el-input>
          </el-form-item>
        </el-form>
      </div>
      <span slot='footer' class='dialog-footer'>
        <el-button size='mini' @click='sensorSelectDialogVisible = false'>取 消</el-button>
        <el-button size='mini' type='primary' @click='addSensorNode'>确 定</el-button>
    </span>
      <data-composition-tree ref='dataTree' @selectNewSensorCallBack='selectNewSensorCallBack' />
    </el-dialog>
  </div>
</template>

<script>
import DataCompositionTree from '@/views/jgjc/dataAnalysis/presets/components/dataCompositionTree.vue'

export default {
  name: 'SensorSelect',
  components: { DataCompositionTree },
  props: {
    value: {  // 添加 value prop 用于 v-model
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      internalSensorList: [],  // 内部使用的数据
      sensorSelectDialogVisible: false,
      addingNode: {},
      formRules: [],
    }
  },
  watch: {
    value: {
      immediate: true,
      handler(newVal) {
        this.internalSensorList = [...newVal]  // 初始化内部数据
      },
    },
  },
  methods: {
    addNewSensor() {
      this.sensorSelectDialogVisible = true
    },
    addSensorNode() {
      // 添加新传感器到内部列表
      this.internalSensorList = [...this.internalSensorList, this.addingNode.sensorInfo]
      this.emitChange()  // 触发更新
      this.sensorSelectDialogVisible = false
    },
    selectFromTree() {
      this.$refs.dataTree.open()
    },
    selectNewSensorCallBack(newSensor) {
      this.$set(this.addingNode, 'sensorInfo', newSensor)
      this.$set(this.addingNode, 'addSensorNodeInfoLabel', newSensor.dataCode)
      this.$set(this.addingNode, 'sensorLabel', newSensor.label)
    },
    closeDialog() {
      this.sensorSelectDialogVisible = false
    },
    handleChange() {
      this.emitChange()  // 选择器变化时触发更新
    },
    emitChange() {
      this.$emit('input', [...this.internalSensorList])  // 触发 input 事件
    },
  },
}
</script>

<style scoped lang='scss'>

</style>
