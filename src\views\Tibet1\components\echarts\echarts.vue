<template>
  <div class="e-charts" :style="{ height: height }"></div>
</template>

<script>
import * as echarts from "echarts";
require("echarts/theme/macarons"); // echarts theme

export default {
  props: {
    height: {
      type: String,
      default: "260px",
    },
    option: {
      type: Object,
      default: () => { },
    },
  },
  data() {
    return {
      chart: null,
    };
  },
  mounted() {
    this.initChart();
  },
  watch: {
    option: {
      handler(newVal, oldVal) {
        this.refreshChart();
      },
      deep: true,
    },
  },
  methods: {
    initChart() {
      this.chart = echarts.init(this.$el, "macarons", { renderer: "svg" });
      this.chart.setOption(this.option);
      // 监听窗口变化，重新渲染图表
      this.chart.resize();
      window.addEventListener("reload", () => {
        this.chart.setOption(this.option);
      })

      window.addEventListener("resize", () => {
        this.chart.resize();
        this.$forceUpdate();
      });
      this.chart.on('click', (params) => {
        this.$emit('click', params)
      })
    },
    refreshChart() {
      this.chart.setOption(this.option);
      this.chart.resize();
    }
  },
};
</script>

<style lang="scss" scoped>
.e-charts {
  width: 100%;
}
</style>
