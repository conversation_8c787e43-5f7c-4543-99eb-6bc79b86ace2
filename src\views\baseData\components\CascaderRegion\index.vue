<template>
  <div >
    <el-cascader
      ref="regin"
      v-model="value"
      :options="regionOptions"
      :props="defaultProps"
      @change="handleChange"
      clearable
      placeholder=请选择县级行政区
      :show-all-levels="false"
      style="width: 100%;"
    >
<!--      <template slot-scope="{ node, data }">-->
<!--        <span>{{ data.label }}</span>-->
<!--        <span v-if="node.isLeaf">({{ data.id }})</span>-->
<!--      </template>-->
    </el-cascader>
  </div>
</template>

<script>
import { getTreeByEntity } from "@/api/system/geography";
export default {
  props: {
    deep: {
      type: [String,Number],
      default: ''
    },
    value: {
      type: String,
      default: ''
    },
  },
  data() {
    return {
      regionOptions: [], // 路段数据
      loading: false,
      isFirstLoad: true,
      // value: [],
      defaultProps: {
        expandTrigger: 'hover',
        value: 'id',
        disabled: 'disabled11',
        emitPath: false
      },
    };
  },
  created() {
    this.getOptions();
    // this.regionOptions=require('./data.json').data
  },
  methods: {
    getOptions() {
      getTreeByEntity({ deep: this.deep,supCode:'53' }).then((res) => {
        if (res.code == 200) {
          this.regionOptions =this.formatOptions(res.data);
        }
      });
    },

    formatOptions  (data) {
      return data.map((item) => {
        const { children, ...rest } = item;
        if (children) {
          rest.children = this.formatOptions(children);
        } else {
          rest.label = `${item.label}(${item.id})`;
        }
        return rest;
      });
    },
    handleChange(value) {
      // let nodeData= this.$refs.regin.getCheckedNodes()[0].data;
      // this.value=nodeData.label+"("+nodeData.value+")"
      this.$emit("input", value);
    }
  },
};
</script>
