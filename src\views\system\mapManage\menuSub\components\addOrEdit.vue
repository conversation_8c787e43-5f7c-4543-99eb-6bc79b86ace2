<template>
  <el-form
    ref="form"
    :model="ruleForm"
    :rules="rules"
    label-width="150px"
    :disabled="readonly"
    :class="readonly ? 'forView':''"
  >
    <el-row :gutter="10">
      <el-col :span="11">
        <el-form-item
          label="数据获取方式"
          prop="dataQueryType"
          label-width="110px"
        >
          <el-select
            v-model="ruleForm.dataQueryType"
            placeholder="请选择"
            style="width: 100%;"
            @change="changeDataQueryType"
          >
            <el-option
              v-for="item in dataQueryOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
      </el-col>
    </el-row>
    <el-tabs
      type="card"
      v-model="activeName"
      @tab-click="handleClick"
    >
      <el-tab-pane
        label="基础信息"
        name="1"
      >
        <div style="display: flex; flex-wrap: wrap; height: 60vh; overflow-y: auto; padding-right:10px">
          <el-col
            :span="12"
            v-for="(item, index) in base"
            :key="index"
          >
            <el-form-item
              :label="item.label"
              :prop="item.prop"
            >
              <template v-if="item.type == 'input'">
                <el-input
                  v-model="ruleForm[item.prop]"
                  :placeholder="'请输入' + item.label"
                  @change="handleChange(item.prop)"
                />
              </template>
              <template v-else-if="item.type == 'select'">
                <el-select
                  v-model="ruleForm[item.prop]"
                  :placeholder="'请选择' + item.label"
                  clearable
                  filterable
                  :allow-create="item.prop === 'statUnit' ? true : false"
                  style="width: 100%;"
                  @change="handleSelectChange($event, item.options)"
                >
                  <template v-if="item.options && item.options == 'tableOptions'">
                    <el-option
                      v-for="item in tableOptions"
                      :key="item.value"
                      :label="(item.label ? (item.label + '----') : '') + item.value"
                      :value="item.value"
                    />
                  </template>
                  <template v-if="item.options && item.options == 'columnOptions'">
                    <el-option
                      v-for="item in columnOptions"
                      :key="item.value"
                      :label="(item.label ? (item.label + '----') : '') + item.value"
                      :value="item.value"
                    />
                  </template>
                  <template v-else-if="item.options && item.options == 'statOptions'">
                    <el-option
                      v-for="item in statOptions"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                    />
                  </template>
                  <template v-else-if="item.options && item.options == 'unitOptions'">
                    <el-option
                      v-for="item in unitOptions"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                    />
                  </template>
                  <template v-else-if="item.options && item.options == 'coordinateOptions'">
                    <el-option
                      v-for="item in coordinateOptions"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                    />
                  </template>
                  <template v-else-if="item.options && item.options == 'baseLayerOptions'">
                    <el-option
                      v-for="item in baseLayerOptions"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                    />
                  </template>
                  <template v-else-if="item.options && item.options == 'menuShowTypeOptions'">
                    <el-option
                      v-for="item in menuShowTypeOptions"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                    />
                  </template>
                  <template v-else-if="item.options && item.options == 'parentOptions'">
                    <el-option
                      v-for="item in parentOptions"
                      :key="item.id"
                      :label="item.name"
                      :value="item.id"
                    />
                  </template>
                  <template v-else>
                    <el-option
                      v-for="item in menuSubClassifyList"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                    />
                  </template>
                </el-select>
              </template>
              <template v-else-if="item.type == 'inputNumber'">
                <el-input-number
                  v-model="ruleForm[item.prop]"
                  :step="1"
                  :min="0"
                  :placeholder="'请输入' + item.label"
                  style="width: 100%;"
                />
              </template>
              <template v-else-if="item.type == 'radio'">
                <el-radio-group
                  v-model="ruleForm[item.prop]"
                  style="width: 100%;"
                >
                  <el-radio
                    :label="item.value"
                    v-for="(item,index) in item.options"
                    :key="index"
                  >{{item.label}}</el-radio>
                </el-radio-group>
              </template>
              <template v-else-if="item.type == 'cascader'">
                <el-cascader
                  v-model="ruleForm[item.prop]"
                  :options="layerMenuList"
                  :props="{ checkStrictly: true, label: 'name', value: 'id', children: 'child' }"
                  clearable
                  style="width: 100%;"
                  @change="handleCascaderChange"
                />
              </template>
              <template v-else-if="item.type == 'textarea'">
                <el-input
                  type="textarea"
                  :rows="3"
                  :placeholder="'请输入' + item.label"
                  v-model="ruleForm[item.prop]"
                />
              </template>
              <template v-else-if="item.type == 'dict'">
                <el-select
                  v-model="ruleForm[item.prop]"
                  clearable
                  filterable
                  style="width: 100%;"
                >
                  <el-option
                    v-for="dict in dict.type[item.dict]"
                    :key="dict.value"
                    :label="dict.label"
                    :value="dict.value"
                  >
                  </el-option>
                </el-select>
              </template>
              <template v-else-if="item.type == 'color'">
                <el-color-picker
                  v-model="ruleForm[item.prop]"
                  show-alpha
                ></el-color-picker>
              </template>
            </el-form-item>
          </el-col>
        </div>
      </el-tab-pane>
      <el-tab-pane
        label="配置信息"
        name="2"
      >
        <div style="height: 60vh">
          <div
            style="margin:0 0 10px 10px"
            v-if="config.length"
          >
            <el-button
              @click="changeEditJson(1)"
              :class="activeBtn === 1 ? 'active-btn' : ''"
            >基础配置模版</el-button>
            <el-button
              @click="changeEditJson(2)"
              :class="activeBtn === 2 ? 'active-btn' : ''"
            >使用JSON编辑器</el-button>
          </div>
          <div
            v-if="activeBtn === 1"
            style="max-height: calc(100% - 38px); display: flex; flex-wrap: wrap; overflow-y: auto; padding-right:10px"
          >
            <el-col
              :span="item.type === 'obj' ? 24 : 12"
              v-for="(item, index) in config"
              :key="index"
            >
              <el-form-item
                v-if="item.type !== 'obj'"
                :label="item.label"
                :prop="item.prop"
              >
                <template v-if="item.type === 'input'">
                  <el-input v-model="listQueryConfig[item.prop]" />
                </template>
                <template v-if="item.type === 'select'">
                  <el-select
                    v-model="listQueryConfig[item.prop]"
                    clearable
                    filterable
                    style="width: 100%;"
                  >
                    <el-option
                      v-for="dict in [{value: 'get', label: 'get'},{value: 'post', label: 'post'}]"
                      :key="dict.value"
                      :label="dict.label"
                      :value="dict.value"
                    >
                    </el-option>
                  </el-select>
                </template>
              </el-form-item>
              <div
                v-else
                style="border: 1px solid #c0d2e2;border-radius: 10px; margin-left: 10px;padding:10px;display: flex;flex-direction: column;"
              >
                <span style="color: #333; font-size: 14px;margin: 0 0 10px 0">{{item.label}}</span>
                <div style="display: flex; flex-wrap: wrap;">
                  <el-col
                    :span="i.prop === 'params' ? 24 : 12"
                    v-for="(i, idx) in item.child"
                    :key="idx"
                  >
                    <el-form-item
                      :label="i.label"
                      :prop="i.prop"
                      label-width="120px"
                    >
                      <template v-if="i.type === 'input'">
                        <el-input v-model="listQueryConfig[item.prop][i.prop]" />
                      </template>
                      <template v-if="i.type === 'select'">
                        <el-select
                          v-model="listQueryConfig[item.prop][i.prop]"
                          clearable
                          filterable
                          style="width: 100%;"
                        >
                          <el-option
                            v-for="dict in [{value: '1', label: '组件'},{value: '2', label: '路由'},{value: '3', label: '接口访问'}]"
                            :key="dict.value"
                            :label="dict.label"
                            :value="dict.value"
                          >
                          </el-option>
                        </el-select>
                      </template>
                      <template v-if="i.type === 'arr'">
                        <el-button
                          type="primary"
                          @click="addParams"
                        >新增</el-button>
                        <div
                          v-for="(e, i) in listQueryConfig.detlCondf.params"
                          :key="i"
                          class="params-card"
                        >
                          <div style="width: calc(100% - 40px)">
                            <el-col :span="12">
                              <el-form-item
                                label="传入参数字段"
                                prop="paramColName"
                                label-width="120px"
                              >
                                <el-input v-model="e.paramColName" />
                              </el-form-item>
                            </el-col>
                            <el-col :span="12">
                              <el-form-item
                                label="获取参数字段"
                                prop="dataColName"
                                label-width="120px"
                              >
                                <el-input v-model="e.dataColName" />
                              </el-form-item>
                            </el-col>
                          </div>
                          <i
                            class="el-icon-remove"
                            style="color:red; cursor:pointer"
                            @click="removeParams(e,i)"
                          ></i>
                        </div>
                      </template>
                    </el-form-item>
                  </el-col>
                </div>
              </div>
            </el-col>
          </div>
          <div v-else>
            <vue-json-editor
              v-model="listQueryConfig"
              :mode="'code'"
            />
          </div>
        </div>
      </el-tab-pane>
      <el-tab-pane
        label="图片信息"
        name="3"
      >
        <div style="display: flex; flex-wrap: wrap; height: 60vh; overflow-y: auto; padding-right:10px">
          <el-col
            :span="12"
            v-for="(item, index) in picture"
            :key="index"
          >
            <el-form-item
              :label="item.label"
              :prop="item.prop"
              label-width="110px"
            >
              <ImageUpload
                v-model="ruleForm[item.prop]"
                :limit="1"
                :owner-id="enterOwnerId"
              />
            </el-form-item>
          </el-col>
        </div>
      </el-tab-pane>
    </el-tabs>
    <div style="display: flex;justify-content: center;margin: 20px 10px 0 10px">
      <el-button
        type="primary"
        @click="onSubmit"
        v-loading="loading"
        v-if="!readonly"
      >确定</el-button>
      <el-button @click="onCancel">取消</el-button>
    </div>
  </el-form>
</template>

<script>
import { getListTree } from '@/api/oneMap/layerMenu'
import { getListPage } from '@/api/oneMap/layer'
import { addMenuSub, updateMenuSub, getParentList } from '@/api/oneMap/menuSub'
import { getTableList, getTableColumnList } from '@/api/oneMap/tableInfo'
import { createIdWorker } from '@/api/baseData/common'
import vueJsonEditor from 'vue-json-editor'
import CryptoJS from 'crypto-js';

export default {
  name: 'menu-sub-form',
  dicts: ['shape_type'],
  components: { vueJsonEditor },
  props: {
    form: {
      type: Object,
      default: {}
    },
    id: {
      type: [String, Number],
      default: ''
    },
    readonly: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      ruleForm: {
        figureId: '',
      },
      rules: {
        classifyName: [
          { required: true, message: '请输入分类名称', trigger: 'blur' }
        ],
        menuType: [
          {
            required: true,
            message: '请选择目录类型',
            trigger: ['change', 'blur']
          }
        ]
      },
      loading: false,
      statOptions: [
        {
          label: 'count',
          value: 1
        },
        {
          label: 'sum',
          value: 2
        }
      ],
      tableOptions: [], // 表数据
      columnOptions: [], // 表字段
      parentOptions: [], // 父级图层
      base: [],
      config: [],
      picture: [],
      listQueryConfig: {
        listActionUrl: '',
        listActionMethod: '',
        exportActionUrl: '',
        exportActionMethod: '',
        detlCondf: { params: [] }
      },
      fields1: {
        base: [
          {
            label: '图层目录',
            prop: 'layerMenuId',
            type: 'cascader'
          },
          {
            label: '父级图层名称',
            prop: 'parentId',
            type: 'select',
            options: 'parentOptions'
          },
          {
            label: '图层名称',
            prop: 'name',
            type: 'input'
          },
          {
            label: '查询数据源',
            prop: 'datasourceName',
            type: 'input'
          },
          {
            label: '图层数据表名',
            prop: 'tableInfo',
            type: 'select',
            options: 'tableOptions'
          },
          {
            label: '图层表字段',
            prop: 'tableColumnInfo',
            type: 'select',
            options: 'columnOptions'
          },
          {
            label: '空间坐标字段',
            prop: 'geomColumnInfo',
            type: 'select',
            options: 'columnOptions'
          },
          {
            label: '数据统计类型',
            prop: 'dataType',
            type: 'select',
            options: 'statOptions'
          },
          {
            label: '统计单位',
            prop: 'statUnit',
            type: 'select',
            options: 'unitOptions'
          },
          {
            label: 'id列名',
            prop: 'idColumnInfo',
            type: 'select',
            options: 'columnOptions'
          },
          {
            label: '注记显示名称列',
            prop: 'textShowNameColumnInfo',
            type: 'select',
            options: 'columnOptions'
          },
          {
            label: '注记显示层级',
            prop: 'textShowLevel',
            type: 'inputNumber'
          },
          {
            label: '开始显示层级',
            prop: 'showLevel',
            type: 'inputNumber'
          },
          {
            label: '排序',
            prop: 'showIndex',
            type: 'inputNumber'
          },
          {
            label: '边框粗细',
            prop: 'borderWidth',
            type: 'inputNumber'
          },
          {
            label: '一张图',
            prop: 'oneMapShow',
            type: 'radio',
            options: [
              {
                label: '显示',
                value: 1
              },
              {
                label: '不显示',
                value: 0
              }
            ]
          },
          {
            label: '是否符号化',
            prop: 'ifPersonalize',
            type: 'radio',
            options: [
              {
                label: '是',
                value: 1
              },
              {
                label: '否',
                value: 0
              }
            ]
          },
          {
            label: '填充颜色',
            prop: 'interiorColour',
            type: 'color'
          },
          {
            label: '边框颜色',
            prop: 'borderColour',
            type: 'color'
          },
          // {
          //   label: '图层url',
          //   prop: 'baseLayerId',
          //   type: 'select',
          //   options: 'baseLayerOptions'
          // },
          {
            label: '图层坐标系',
            prop: 'coordinateSystem',
            type: 'select',
            options: 'coordinateOptions'
          },
          {
            label: '是否切片缓存',
            prop: 'ifTileLocalCache',
            type: 'radio',
            options: [
              {
                label: '是',
                value: 1
              },
              {
                label: '否',
                value: 0
              }
            ]
          },
          {
            label: '控制数据权限字段',
            prop: 'ruleDataColumnInfo',
            type: 'input'
          },
          {
            label: '目录展示类型',
            prop: 'menuShowType',
            type: 'select',
            options: 'menuShowTypeOptions'
          },
          {
            label: '图层描述信息',
            prop: 'description',
            type: 'textarea'
          }
        ],
        config: [
          {
            label: '列表查询接口',
            prop: 'listActionUrl',
            type: 'input'
          },
          {
            label: '接口请求方式',
            prop: 'listActionMethod',
            type: 'select'
          },
          {
            label: '列表数据导出接口',
            prop: 'exportActionUrl',
            type: 'input'
          },
          {
            label: '导出请求方式',
            prop: 'exportActionMethod',
            type: 'select'
          },
          {
            label: '详情查看配置',
            prop: 'detlCondf',
            type: 'obj',
            child: [
              {
                label: '详情查看类型',
                prop: 'type',
                type: 'select'
              },
              {
                label: '地址路径',
                prop: 'path',
                type: 'input'
              },
              {
                label: '详情查看参数',
                prop: 'params',
                type: 'arr',
                child: []
              }
            ]
          }
        ],
        picture: [
          {
            label: '图标',
            prop: 'iconId',
            type: 'upImg'
          },
          {
            label: '图层大图',
            prop: 'bigIconId',
            type: 'upImg'
          },
          {
            label: '图例',
            prop: 'figureId',
            type: 'upImg'
          }
        ]
      },
      fields2: {
        base: [
          {
            label: '图层目录',
            prop: 'layerMenuId',
            type: 'cascader'
          },
          {
            label: '图层名称',
            prop: 'name',
            type: 'input'
          },
          {
            label: '空间坐标字段',
            prop: 'personalizeColumn',
            type: 'input'
          },
          {
            label: '数据统计类型',
            prop: 'dataType',
            type: 'select',
            options: 'statOptions'
          },
          {
            label: '统计单位',
            prop: 'statUnit',
            type: 'select',
            options: 'unitOptions'
          },
          {
            label: 'id列名',
            prop: 'idColumnInfo',
            type: 'select',
            options: 'columnOptions'
          },
          {
            label: '注记显示名称列',
            prop: 'textShowNameColumnInfo',
            type: 'input'
          },
          {
            label: '注记显示层级',
            prop: 'textShowLevel',
            type: 'inputNumber'
          },
          {
            label: '开始显示层级',
            prop: 'showLevel',
            type: 'inputNumber'
          },
          {
            label: '排序',
            prop: 'showIndex',
            type: 'inputNumber'
          },
          {
            label: '边框粗细',
            prop: 'borderWidth',
            type: 'inputNumber'
          },
          {
            label: '一张图',
            prop: 'oneMapShow',
            type: 'radio',
            options: [
              {
                label: '显示',
                value: 1
              },
              {
                label: '不显示',
                value: 0
              }
            ]
          },
          {
            label: '是否符号化',
            prop: 'ifPersonalize',
            type: 'radio',
            options: [
              {
                label: '是',
                value: 1
              },
              {
                label: '否',
                value: 0
              }
            ]
          },
          {
            label: '填充颜色',
            prop: 'interiorColour',
            type: 'color'
          },
          {
            label: '边框颜色',
            prop: 'borderColour',
            type: 'color'
          },
          // {
          //   label: '图层url',
          //   prop: 'baseLayerId',
          //   type: 'select',
          //   options: 'baseLayerOptions'
          // },
          {
            label: '图层坐标系',
            prop: 'coordinateSystem',
            type: 'select',
            options: 'coordinateOptions'
          },
          {
            label: '是否切片缓存',
            prop: 'ifTileLocalCache',
            type: 'radio',
            options: [
              {
                label: '是',
                value: 1
              },
              {
                label: '否',
                value: 0
              }
            ]
          },
          {
            label: '控制数据权限字段',
            prop: 'ruleDataColumnInfo',
            type: 'input'
          },
          {
            label: '查询数据源',
            prop: 'datasourceName',
            type: 'input'
          },
          {
            label: '目录展示类型',
            prop: 'menuShowType',
            type: 'select',
            options: 'menuShowTypeOptions'
          },
          {
            label: '图层显示查询sql',
            prop: 'layerShowSql',
            type: 'textarea'
          },
          {
            label: '图层统计显示查询sql',
            prop: 'layerStatShowSql',
            type: 'textarea'
          },
          {
            label: '图层目录总数查询sql',
            prop: 'layerTotalSql',
            type: 'textarea'
          },
          // {
          //   label: '列表搜索查询配置',
          //   prop: 'listQueryConfig',
          //   type: 'textarea'
          // },
          {
            label: '图层描述信息',
            prop: 'description',
            type: 'textarea'
          }
        ],
        config: [
          {
            label: '列表查询接口',
            prop: 'listActionUrl',
            type: 'input'
          },
          {
            label: '接口请求方式',
            prop: 'listActionMethod',
            type: 'select'
          },
          {
            label: '列表数据导出接口',
            prop: 'exportActionUrl',
            type: 'input'
          },
          {
            label: '导出请求方式',
            prop: 'exportActionMethod',
            type: 'select'
          },
          {
            label: '详情查看配置',
            prop: 'detlCondf',
            type: 'obj',
            child: [
              {
                label: '详情查看类型',
                prop: 'type',
                type: 'select'
              },
              {
                label: '地址路径',
                prop: 'path',
                type: 'input'
              },
              {
                label: '详情查看参数',
                prop: 'params',
                type: 'arr',
                child: []
              }
            ]
          }
        ],
        picture: [
          {
            label: '图标',
            prop: 'iconId',
            type: 'upImg'
          },
          {
            label: '图层大图',
            prop: 'bigIconId',
            type: 'upImg'
          },
          {
            label: '图例',
            prop: 'figureId',
            type: 'upImg'
          }
        ]
      },
      fields3: {},
      fields4: {
        base: [
          {
            label: '图层url',
            prop: 'baseLayerId',
            type: 'select',
            options: 'baseLayerOptions'
          },
          {
            label: '目录展示类型',
            prop: 'menuShowType',
            type: 'select',
            options: 'menuShowTypeOptions'
          }
        ]
      },
      menuSubClassifyList: [],
      layerMenuList: [],
      enterOwnerId: '', // 图标Id
      dataQueryOptions: [
        { value: 1, label: '单表' },
        { value: 2, label: '多表sql' },
        { value: 3, label: '接口获取' },
        { value: 4, label: '地址访问' }
      ],
      unitOptions: [
        { value: '座', label: '座' },
        { value: '条', label: '条' }
      ],
      coordinateOptions: [
        { value: 'WGS84', label: 'WGS84' },
        { value: 'CGCS2000', label: 'CGCS2000' },
        { value: 'GCJ_02', label: 'GCJ_02' }
      ],
      baseLayerOptions: [],
      activeName: '1',
      menuShowTypeOptions: [
        { value: 0, label: '都显示' },
        { value: 1, label: '树形显示' },
        { value: 2, label: '数据总览' }
      ],
      activeBtn: 1
    }
  },
  created() {
    this.ruleForm = { ...this.form }
    if (!this.form.id) {
      this.ruleForm = {
        ...this.ruleForm,
        dataQueryType: 1,
        borderWidth: 0.5,
        textShowLevel: 8,
        layerShowLevel: 8,
        ifPersonalize: 1,
        oneMapShow: 1,
        showIndex: 1,
        showLevel: 8,
        interiorColour: 'rgba(0, 191, 255, 1)',
        borderColour: 'rgba(0, 191, 255, 1)',
        ifTileLocalCache: 1
      }
    } else {
      if (this.ruleForm.listQueryConfig) {
        this.listQueryConfig = JSON.parse(this.ruleForm.listQueryConfig)
        if (!this.listQueryConfig?.detlCondf?.params) {
          this.listQueryConfig = {
            ...this.listQueryConfig,
            detlCondf: {
              params: []
            }
          }
        }
      }
    }
    this.changeDataQueryType()
    this.getList()
    this.getTable()
    this.getLayerUrl()
    createIdWorker().then(res => {
      if (res.code === 200) this.enterOwnerId = res.data
    })
    this.$forceUpdate()
  },
  watch: {
    'ruleForm.tableInfo': {
      handler(val) {
        if (val) {
          this.handleSelectChange(val, 'tableOptions')
        }
      }
    }
  },
  methods: {
    // 获取所有列表数据
    getList() {
      getListTree({ pageNum: 1, pageSize: 9999 }).then(res => {
        this.layerMenuList = res || []
        if (res.code == 200) {
          this.layerMenuList = res.rows || []
        }
      })
    },
    // 提交
    onSubmit() {
      this.$refs.form.validate(vali => {
        if (!vali) return
        if (this.ruleForm.layerShowSql) {
          this.ruleForm.layerShowSql = this.encryptData(this.ruleForm.layerShowSql);
        }
        if (this.ruleForm.layerTotalSql) {
          this.ruleForm.layerTotalSql = this.encryptData(this.ruleForm.layerTotalSql);
        }
        if (this.ruleForm.layerStatShowSql) {
          this.ruleForm.layerStatShowSql = this.encryptData(this.ruleForm.layerStatShowSql);
        }
        this.ruleForm.iconId =
          Object.prototype.toString.call(this.ruleForm.iconId) ===
          '[object Array]'
            ? this.ruleForm.iconId[0]
            : this.ruleForm.iconId || ''
        this.ruleForm.bigIconId =
          Object.prototype.toString.call(this.ruleForm.bigIconId) ===
          '[object Array]'
            ? this.ruleForm.bigIconId[0]
            : this.ruleForm.bigIconId || ''
        this.ruleForm.figureId =
          Object.prototype.toString.call(this.ruleForm.figureId) ===
          '[object Array]'
            ? this.ruleForm.figureId[0]
            : this.ruleForm.figureId || ''
        this.ruleForm.layerMenuId =
          Object.prototype.toString.call(this.ruleForm.layerMenuId) ===
          '[object Array]'
            ? this.ruleForm.layerMenuId[0]
            : this.ruleForm.layerMenuId
        this.ruleForm.listQueryConfig = JSON.stringify(this.listQueryConfig)
        let request = this.ruleForm.id
          ? updateMenuSub(this.ruleForm)
          : addMenuSub(this.ruleForm)
        let msg = this.ruleForm.id ? '编辑成功' : '新增成功'
        this.loading = true
        request
          .then(res => {
            if (res.code == 200) {
              this.$modal.msgSuccess(msg)
              this.$emit('refresh')
              this.onCancel()
            }
          })
          .finally(() => {
            this.loading = false
          })
      })
    },
    // 取消关闭
    onCancel() {
      this.$emit('close', false)
    },
    // 级联选择器监听变化
    handleCascaderChange(e) {
      if (e) {
        this.ruleForm.layerMenuId = e[e.length - 1]
        getParentList({layerMenuId: this.ruleForm.layerMenuId}).then(res => {
          if (res.code === 200) { 
            this.parentOptions = res.data || []
          }
        })
      }
    },
    handleChange(prop) {
      switch (prop) {
        case 'datasourceName':
          this.ruleForm.tableInfo = ''
          this.getTable()
          break
      }
    },
    getTable() {
      getTableList({
        pageNum: 1,
        pageSize: 999,
        datasourceName: this.ruleForm.datasourceName
      }).then(res => {
        if (res.code == 200) {
          this.tableOptions =
            res.rows.map(v => {
              return {
                label: v.tableComment,
                value: v.tableName
              }
            }) || []
        }
      })
    },
    getLayerUrl() {
      getListPage({ pageNum: 1, pageSize: 9999, ifMenuLayer: 1 }).then(res => {
        if (res.code === 200) {
          this.baseLayerOptions =
            res.rows.map(v => {
              return {
                label: v.layerName,
                value: v.id
              }
            }) || []
        }
      })
    },
    // 监听选择框变化
    handleSelectChange(e, options) {
      switch (options) {
        case 'tableOptions':
          getTableColumnList({
            tableName: e,
            pageNum: 1,
            pageSize: 99,
            datasourceName: this.ruleForm.datasourceName
          }).then(res => {
            if (res.code == 200) {
              this.columnOptions =
                res.rows.map(v => {
                  return {
                    label: v.columnComment,
                    value: v.columnName
                  }
                }) || []
            }
          })
          break
      }
    },
    changeDataQueryType() {
      switch (this.ruleForm.dataQueryType) {
        case 1:
          this.base = this.fields1.base
          this.config = this.fields1.config || []
          this.picture = this.fields1.picture
          break
        case 2:
          this.base = this.fields2.base
          this.config = this.fields2.config || []
          this.picture = this.fields2.picture
          break
        case 3:
          this.base = this.fields3.base
          this.config = this.fields3.config || []
          this.picture = this.fields3.picture
          break
        case 4:
          this.base = this.fields4.base
          this.config = this.fields4.config || []
          this.picture = this.fields4.picture
          break
      }
    },
    handleClick() {},
    changeEditJson(v) {
      this.activeBtn = v
    },
    addParams() {
      this.listQueryConfig.detlCondf.params.push({})
    },
    removeParams(item, index) {
      if (item.paramColName || item.dataColName) {
        this.$modal.confirm('确认删除？').then(() => {
          this.listQueryConfig.detlCondf.params = this.listQueryConfig.detlCondf.params.filter(
            (el, idx) => idx !== index
          )
        })
      } else {
        this.listQueryConfig.detlCondf.params = this.listQueryConfig.detlCondf.params.filter(
          (el, idx) => idx !== index
        )
      }
    },
    //Base64解密
    decodeBase64UsingTextDecoder(text) {
      const binaryString = window.atob(text);
      const bytes = new Uint8Array(binaryString.length);
      for (let i = 0; i < binaryString.length; i++) {
        bytes[i] = binaryString.charCodeAt(i);
      }
      const decoder = new TextDecoder();
      return decoder.decode(bytes);
    },
    // 加密函数
    encryptData(plainText) {
      let str = 'OWUzMjEwYzlhZTM0MWRlMWFiMTZjY2UwZjFhN2I5MDk='
      let key = CryptoJS.enc.Utf8.parse(this.decodeBase64UsingTextDecoder(str));
      return CryptoJS.AES.encrypt(plainText, key,{mode: CryptoJS.mode.ECB, padding: CryptoJS.pad.Pkcs7}).toString();
      // const encrypted = CryptoJS.AES.encrypt(plainText, key);
      // return encrypted.toString();
    }
  }
}
</script>

<style lang="scss" scoped>
@import '~@/assets/styles/el-tabs.scss';
::v-deep .el-color-picker--mini .el-color-picker__trigger {
  width: 70px;
}
.forView ::v-deep .el-input.is-disabled .el-input__inner {
  background-color: white;
  border-color: #dfe4ed;
  color: black;
}
::v-deep .params-card {
  width: 100%;
  border: 1px solid #c0d2e2;
  border-radius: 10px;
  margin-top: 10px;
  padding: 0 10px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  .el-form-item {
    margin: 18px 0;
  }
}
.active-btn {
  border-color: #1890ff;
  color: #1890ff;
}
::v-deep .jsoneditor {
  width: calc(100% - 20px);
  height: 55vh !important;
  margin: 0 10px;
  border-radius: 10px;
}
</style>
