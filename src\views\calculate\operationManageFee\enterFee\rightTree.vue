<template>
  <div>
    <div class="head-container" style="width: 300px">
      <el-tree
        ref="tree"
        :data="filteredTreeData"
        :expand-on-click-node="false"
        :filter-node-method="filterNode"
        highlight-current
        node-key="id"
        @node-click="handleNodeClick"
      >
        <span slot-scope="{ node, data }">
          <i v-if="data.status == 1" class="el-icon-circle-check" style="color: #7fc784"></i>
          <i v-if="data.status == 0" class="el-icon-circle-close" style="color: red"></i>
          <el-tooltip :content="data.label" placement="top">
            <span style="font-size: 14px">{{ data.label }}</span>
          </el-tooltip>
        </span>
      </el-tree>
    </div>
  </div>
</template>
<script>
import {findByYear} from "@/api/budgetManage/planManage";
import {listMeteringTree} from "@/api/calculate/operationManageFee/enterFee";

export default {
  data() {
    return {
      keyword: '',
      relaOptions: [],
      filteredTreeData: [],
    }
  },
  created() {
  },
  methods: {
    // 关键词检索
    handleSearch() {
      const keyword = this.keyword.toLowerCase();
      this.filteredTreeData = this.relaOptions.filter(node => this.filterNode(node, keyword));
    },
    // 筛选节点
    filterNode(node, keyword) {
      if (String(node.label).indexOf(keyword) != -1) {
        return true;
      }
      if (node.children) {
        return node.children.some(childNode => this.filterNode(childNode, keyword));
      }
      return false;
    },
    // 查询部门下拉树结构
    getDeptTree(params) {
      listMeteringTree(params).then(res => {
        const treeData = res.data
        treeData.forEach(item => {
          getChild(item)
        })

        function getChild(node, status) {
          if (status || status == 0) {
            node.status = status
          }
          node.label = node.numberName ? `${node.name}(${node.numberName})` : node.name,
          node.value = node.name
          if (node.children) {
            node.children.forEach(item => {
              getChild(item, node.status)
            })
          }
        }

        this.relaOptions = treeData
        this.filteredTreeData = [...this.relaOptions]
      })
    },
    handleNodeClick(e) {
      // 获取当前选中节点的最顶级
      this.$emit('query', e)
    },
  }
}
</script>
<style lang="scss" scoped>
.left-total {
  font-size: 13px;
  line-height: 28px;
  color: #606266;
  position: absolute;
  bottom: 10px;
  right: 10px;
}
</style>
<style lang="scss" scoped>
@import "@/assets/styles/business.scss";
</style>
