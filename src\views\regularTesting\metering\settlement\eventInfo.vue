<template>
  <div class="record-main">
    <el-descriptions :column="1" border>
      <el-descriptions-item
        :label="item.label"
        v-for="(item, index) in myTableData"
        :key="'myTable' + index"
        >{{ tableInfo[item.key] || "" }}</el-descriptions-item
      >
    </el-descriptions>

    <div class="right-steps">
      <el-steps direction="vertical" :active="stepsData.length">
        <el-step v-for="(item, index) in stepsData" :key="'steps' + index">
          <template #icon>{{ stepsData.length - index }}</template>

          <template #title>
            <div :style="{ color: item.direction ? 'none' : 'red' }">
              {{ item.nodeName }}
              <i v-if="item.direction" class="el-icon-circle-check" />
              <i v-else class="el-icon-circle-close" />
              &nbsp&nbsp&nbsp{{ item.endTime }}
            </div>
          </template>
          <template #description>
            <el-descriptions :column="1">
              <el-descriptions-item label="是否计算安全保通费">{{
                item.isGuarantee == 1 ? "是" : "否"
              }}</el-descriptions-item>
              <el-descriptions-item label="是否计算安全生产费">{{
                item.isProduction == 1 ? "是" : "否"
              }}</el-descriptions-item>
              <el-descriptions-item label="操作人">{{
                item.assigneeName
              }}</el-descriptions-item>
              <el-descriptions-item label="备注">{{
                item.comment
              }}</el-descriptions-item>
              <el-descriptions-item label="附件"
                ><el-link
                  icon="el-icon-link"
                  type="primary"
                  @click="openFileModel(item)"
                  >{{item.fileList && item.fileList.length > 0 ? '查看' : '空'}}</el-link
                  ></el-descriptions-item
                  >
              <el-descriptions-item contentClassName="must-br">
                <template slot="label">
                  <i
                    v-if="!item.isShowMethod"
                    class="el-icon-arrow-right"
                    @click="changeShowMethod(index)"
                    style="cursor: pointer"
                  ></i>
                  <i
                    v-else
                    class="el-icon-arrow-down"
                    @click="changeShowMethod(index)"
                    style="cursor: pointer"
                  ></i>
                  养护方法
                </template>
                <el-table v-adjust-table :data="item.methodList" v-if="item.isShowMethod">
                  <el-table-column
                    prop="schemeCode"
                    align="center"
                    label="子目号"
                  >
                  </el-table-column>
                  <el-table-column
                    prop="schemeName"
                    align="center"
                    label="子目名称"
                  >
                  </el-table-column>
                  <el-table-column
                    prop="calcDesc"
                    align="center"
                    label="计算式"
                  >
                  </el-table-column>
                  <el-table-column prop="num" align="center" label="数量">
                  </el-table-column>
                  <el-table-column prop="unit" align="center" label="单位">
                  </el-table-column>
                </el-table>
              </el-descriptions-item>
            </el-descriptions>
          </template>
        </el-step>
      </el-steps>
    </div>
    <el-dialog
      title="附件列表"
      destroy-on-close
      :visible.sync="openFile"
      modal-append-to-body
      append-to-body
      width="500px"
    >
      <file-upload
        v-if="fileId"
        v-model="fileId"
        :owner-id="fileId"
        for-view
      ></file-upload>
    </el-dialog>
  </div>
</template>
<script>
import { getTaskOperationRecord, getConstruction } from "@/api/regularTesting/taskManage/taskList";
export default {
  data() {
    return {
      myTableData: [
        {
          label: "项目名称",
          key: "projName",
        },
        {
          label: "任务单名称",
          key: "name",
        },
        {
          label: "任务单编码",
          key: "code",
        },
        {
          label: "路段名称",
          key: "maiSecName",
        },
        {
          label: "位置",
          key: "position",
        },
        {
          label: "施工单位",
          key: "checkDomainName",
        },
        {
          label: "施工合同",
          key: "checkConName",
        },
        {
          label: "验收人员",
          key: "acceptancePerson",
        },
        {
          label: "工作内容",
          key: "content",
        },
        {
          label: "实施要求",
          key: "exeRequire",
        },
      ],
      tableInfo: {},
      stepsData: [],
      openFile: false,
      fileId: "",
    };
  },
  props: {
    row: {
      type: Object,
      default: {},
    },
  },
  watch: {
    row: {
      handler(val) {
        if (val.constructionId) {
          getConstruction(val.constructionId).then(res=> {
            this.tableInfo = res.data || {};
          })
          getTaskOperationRecord({ businessKey: val.constructionId }).then((res) => {
            this.stepsData = res.data || [];
            this.stepsData.forEach((item) => {
              this.$set(item, "isShowMethod", false);
            });
          });
        }
      },
      immediate: true,
      deep: true,
    },
  },
  methods: {
    changeShowMethod(index) {
      this.stepsData[index].isShowMethod = !this.stepsData[index].isShowMethod;
    },
    openFileModel(item) {
      if (item.fileList && item.fileList.length > 0) {
        this.fileId = item.fileList[0].fileId;
        this.openFile = true;
      }
    }
  },
};
</script>
<style lang="scss" scoped>
.record-main {
  display: flex;
  .el-descriptions {
    width: 300px;
    font-size: 14px;
  }
  .right-steps {
    flex: 1;
    padding-left: 20px;
    height: 500px;
    overflow-y: auto;
  }

  ::v-deep .el-descriptions-item__container {
    &:has(.must-br) {
      display: block;
    }
  }
}
</style>
<style lang="scss" scoped>
@import "@/assets/styles/business.scss";
</style>
