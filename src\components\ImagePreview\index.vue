<template>
  <div>
    <el-image :src="`${item || realSrc}`" fit="cover" :style="`width:${realWidth}; height:${realHeight};`"
      :preview-src-list="srcList" v-for="(item,index) in srcList" :key="'image-preview' + index" class="mr5">
      <div slot="error" class="image-slot">
        <i class="el-icon-picture-outline"></i>
      </div>
    </el-image>
  </div>
</template>

<script>
import { findFiles } from '@/api/file/index.js'
export default {
  name: "ImagePreview",
  props: {
    ownerId: {
      type: String,
      default: "",
      require: true
    },
    width: {
      type: [Number, String],
      default: "20px"
    },
    height: {
      type: [Number, String],
      default: "20px"
    }
  },
  data() {
    return {
      srcList: []
    }
  },
  watch: {
    ownerId: {
      handler(val) {
        if (!this.ownerId) return
        findFiles({ ownerId: val }).then(res => {
          if (res.code === 200 && res.data) {
            this.srcList = res.data.map(file => file.url);
          }
        })
      },
      immediate: true
    }
  },
  computed: {
    realSrc() {
      if (!this.srcList.length === 0) return
      let real_src = this.srcList[0];
      return real_src;
    },
    realWidth() {
      return typeof this.width == "string" ? this.width : `${this.width}px`;
    },
    realHeight() {
      return typeof this.height == "string" ? this.height : `${this.height}px`;
    }
  },
};
</script>

<style lang="scss" scoped>
.el-image {
  border-radius: 5px;
  background-color: #ebeef5;
  box-shadow: 0 0 5px 1px #ccc;

  ::v-deep .el-image__inner {
    transition: all 0.3s;
    cursor: pointer;

    &:hover {
      transform: scale(1.2);
    }
  }

  ::v-deep .image-slot {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;
    height: 100%;
    color: #909399;
    font-size: 30px;
  }
}
</style>
