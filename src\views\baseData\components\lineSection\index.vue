<template>
  <div v-loading="loading">
    <el-cascader
      :options="options"
      :placeholder="placeholder"
      ref="cascaderRef"
      style="width: 100%"
      clearable
      v-if="show"
      v-bind="$attrs"
      v-model="selectValue"
    ></el-cascader>
  </div>
</template>

<script>
import { segmentsList } from "@/api/baseData/tunnel/baseInfo/getSections";
import { getDicts as getDicts } from "@/api/system/dict/data";
export default {
  props: {
    value: {
      type: "",
    },
    formObj: {
      type: Object,
      default: {},
    },
    maintenanceSectionId: {
      type: [String, Number],
      default: "",
    },
    placeholder: {
      type: String,
      default: "",
    },
    isEdit: {
      type: Boolean,
      default: true,
    },
  },
  data() {
    return {
      show: false,
      options: [],
      selectObj: {
        belongConnectingLineName: "",
        belongConnectingLineId: "",
        sectionType: "",
        sectionTypeName: "",
      },
      loading: false,
    };
  },
  computed: {
    selectValue: {
      get() {
        let arr;
        if(this.formObj.belongConnectingLineId) {
          arr = [this.formObj.sectionType,this.formObj.belongConnectingLineId]
        }else {
          arr = [this.formObj.sectionType]
        }
        return arr
      },
      set(e) {
        // 获取选中的nodeList
        let nodeList = this.$refs.cascaderRef.getCheckedNodes();
        let { label, value, parent } =
          nodeList && nodeList.length ? nodeList[0] : {};
        if (parent) {
          this.selectObj.belongConnectingLineName = label;
          this.selectObj.belongConnectingLineId = value;
          this.selectObj.sectionType = parent.value;
          this.selectObj.sectionTypeName = parent.label;
        } else {
          this.selectObj.belongConnectingLineName = null;
          this.selectObj.belongConnectingLineId = null;
          this.selectObj.sectionType = value;
          this.selectObj.sectionTypeName = label;
        }
        let obj = {
          ...this.formObj,
          ...this.selectObj,
        };
        this.$emit("update:formObj", obj);
      },
    },
  },
  watch: {
    formObj:{
      handler(val){
        this.$emit("update:formObj", val);
      },
      deep: true,
      immediate: true,
    }
  },
  created() {
    this.getOptions();
  },
  methods: {
    getOptions() {
      this.loading = true;
      getDicts("sys_route_type").then((res) => {
        if (res.code == 200) {
          this.options =
            res.data.map((v) => {
              return {
                value: v.dictValue,
                label: v.dictLabel,
                leaf: v.dictLabel == "主线",
              };
            }) || [];

          if (this.isEdit) {
            this.options.forEach((item, index) => {
              if (!item.leaf) {
                segmentsList({
                  maintenanceSectionId: this.maintenanceSectionId,
                  roadType: item.value,
                }).then((res) => {
                  if (res.code == 200) {
                    item.children =
                      res.data.map((v) => {
                        return {
                          value: v.routeSegmentsId,
                          label: v.routeSegmentsName,
                          leaf: true,
                        };
                      }) || [];
                    this.$forceUpdate();
                    if (index + 1 >= this.options.length) {
                      this.loading = false;
                      setTimeout(() => {
                        this.show = true;
                      }, 500);
                    }
                  }
                });
              }
            });
          }
        }
      });
    },
  },
};
</script>

<style lang="scss" scoped></style>
