<template>
  <el-dialog :visible="openDialog" :title="`选择风险点`" v-dialog-drag :modal="true" :modal-append-to-body="false"
    :append-to-body="true" :close-on-click-modal="false" width="85%" class="asset-select-dialog" @close="handleClose">
    <div class="dialog-content">
      <div class="dialog-scroll-area">
        <div class="container">
          <div class="searchBox" :style="{ 'height': queryShow ? '86px' : '48px' }">
            <el-row :gutter="12">
              <el-col :span="24" style="display: flex; align-items: center">
                <CascadeSelection style="min-width: 192px;" :form-data="queryParams" v-model="queryParams"
                  @update:fromData="() => { }" types="201" multiple />
                <RangeInput @startValue="(v) => { queryParams.pileStartNum = v }"
                  @endValue="(v) => { queryParams.pileEndNum = v }" />
                <el-button type="primary" icon="el-icon-search" size="mini" @click="queryhandle">搜索</el-button>
                <el-button icon="el-icon-refresh" size="mini" @click="queryReset">重置</el-button>
                <el-button :icon="queryShow ? 'el-icon-arrow-up' : 'el-icon-arrow-down'"
                  style="color: #1890ff;border-color: #badeff;background-color: #e8f4ff;" circle
                  @click="queryShow = !queryShow" />
              </el-col>
            </el-row>
            <transition name="search">
              <div class="searchMoreBox" v-if="queryShow">
                <el-row :gutter="20">
                  <el-col :span="4.8" :offset="0">
                    <el-select v-model="queryParams.disasterType" placeholder="请选择灾害类型" clearable>
                      <el-option v-for="dict in dict.type.disaster_risk_type" :key="dict.value" :label="dict.label"
                        :value="dict.label" />
                    </el-select>
                  </el-col>
                  <el-col :span="4.8" :offset="0">
                    <el-input v-model="queryParams.disasterNum" placeholder="请输入灾害编号" clearable
                      @keyup.enter.native="queryhandle" />
                  </el-col>
                  <el-col :span="4.8" :offset="0">
                    <el-input v-model="queryParams.disasterName" placeholder="请输入灾害名称" clearable
                      @keyup.enter.native="queryhandle" />
                  </el-col>
                  <el-col :span="4.8" :offset="0">
                    <el-date-picker v-model="queryParams.expiryTime" format="yyyy-MM-dd" value-format="yyyy-MM-dd"
                      type="date" placeholder="选择日期" @change="(val) => { queryParams.expiryTime = val + ' 00:00:00' }">
                    </el-date-picker>
                  </el-col>
                  <el-col :span="4.8" :offset="0">
                    <el-select v-model="queryParams.checked" placeholder="请选择检查状态" clearable>
                      <el-option label="未检查" :value="0"></el-option>
                      <el-option label="已检查" :value="1"></el-option>
                    </el-select>
                  </el-col>
                </el-row>
                <!-- <el-row :gutter="20" style=" margin-left: 10px">
                  <el-col :span="4" :offset="0">
                    <el-date-picker v-model="queryParams.expiryTime" format="yyyy-MM-dd" value-format="yyyy-MM-dd"
                      type="date" placeholder="选择日期" @change="(val) => { queryParams.expiryTime = val + ' 00:00:00' }">
                    </el-date-picker>
                  </el-col>
                </el-row>
                <el-row :gutter="20" style="width: 426px; margin-left: 10px">
                  <el-col :span="12" :offset="0">
                    <el-select v-model="queryParams.checked" placeholder="请选择检查状态" clearable>
                      <el-option label="未检查" :value="0"></el-option>
                      <el-option label="已检查" :value="1"></el-option>
                    </el-select>
                  </el-col>
                </el-row> -->
              </div>
            </transition>
          </div>
          <!-- 数据表格 -->
          <el-table size="mini" :height="queryShow ? 'calc(100% - 118px)' : 'calc(100% - 98px)'" border ref="tableRef"
            v-loading="tableLoading" :data="tableData" @selection-change="tableSelectionChange" @select="handleSelect"
            @select-all="handleSelectAll" @row-click="handleRowClick" :row-key="getRowKey">
            <el-table-column type="selection" width="55" align="center" reserve-selection />
            <el-table-column label="序号" fixed align="center" type="index" width="50"></el-table-column>
            <el-table-column label="灾害类型" fixed :show-overflow-tooltip="true" width="120" align="center"
              prop="disasterType">
              <template slot-scope="scope">
                <p v-if="!scope.row.disasterType.includes('其它')">{{ scope.row.disasterType }}</p>
                <dict-tag v-if="scope.row.disasterType.includes('其它') && scope.row.disasterType.split(':')[1] !== '99'"
                  :options="dict.type.disaster_risk_other_type" :value="scope.row.disasterType.split(':')[1]" />
                <p v-if="scope.row.disasterType.includes('其它') && scope.row.disasterType.split(':')[1] === '99'">
                  {{ scope.row.disasterType.split(':')[2] }}</p>
              </template>
            </el-table-column>
            <el-table-column label="灾害编号" :show-overflow-tooltip="true" width="180" align="center" prop="disasterNum" />
            <el-table-column label="灾害名称" :show-overflow-tooltip="true" width="180" align="center"
              prop="disasterName" />
            <el-table-column label="管养单位" :show-overflow-tooltip="true" width="120" align="center"
              prop="maintenanceUnitName" />
            <el-table-column label="路段名称" :show-overflow-tooltip="true" width="120" align="center"
              prop="roadSectionName" />
            <el-table-column label="路线名称" :show-overflow-tooltip="true" width="150" align="center" prop="routerName" />
            <el-table-column label="技术等级" :show-overflow-tooltip="true" width="120" align="center"
              prop="technicalGrade">
              <template slot-scope="scope">
                <dict-tag :options="dict.type.sys_route_grade" :value="scope.row.technicalGrade" />
              </template>
            </el-table-column>
            <el-table-column label="风险等级" :show-overflow-tooltip="true" width="120" align="center" prop="riskGrade" />
            <el-table-column label="采集人" :show-overflow-tooltip="true" width="120" align="center" prop="createName" />
            <el-table-column label="采集单位" :show-overflow-tooltip="true" width="250" align="center" prop="createUnit" />
            <el-table-column label="采集时间" :show-overflow-tooltip="true" width="180" align="center" prop="createTime" />
            <el-table-column label="修改时间" :show-overflow-tooltip="true" width="180" align="center" prop="updateTime" />
            <el-table-column label="状态" :show-overflow-tooltip="true" width="120" align="center" prop="status">
              <template slot-scope="scope">
                <el-tag v-if="scope.row.status === '0'" type="info">待提交</el-tag>
                <el-tag v-if="scope.row.status === '1'" type="warning">审核中</el-tag>
                <el-tag v-if="scope.row.status === '2'" type="success">已审核</el-tag>
                <el-tag v-if="scope.row.status === '3'">已撤销</el-tag>
                <el-tag v-if="scope.row.status === '4'" type="danger">已驳回</el-tag>
              </template>
            </el-table-column>
          </el-table>
          <pagination :total="queryTotal" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize"
            @pagination="handleCurrentChange" />
        </div>
      </div>
      <div class="dialog-footer">
        <el-button type="primary" @click="onRiskPointsSelect">
          确 定
        </el-button>
        <el-button @click="handleClose">取 消</el-button>
      </div>
    </div>
  </el-dialog>
</template>

<script>
// import { queryPageRisk } from "@/api/disaster/risk/risk" // 灾害相关接口
import inspection from '@/api/disaster/risk/inspection'
import CascadeSelection from '@/components/CascadeSelection/index.vue' // 管养处/路段/路线
import RangeInput from '@/views/baseData/components/rangeInput/index.vue' // 桩号（查询）
export default {
  name: 'selectRiskPoints',
  // 数据字典
  dicts: [
    'disaster_risk_type',  // 灾害类型
    'disaster_risk_other_type', // 灾害类型（其它）
    'sys_route_grade', // 路线等级
  ],
  components: {
    CascadeSelection,
    RangeInput,
  },
  props: {
    openDialog: {
      type: Boolean,
      default: false
    },
  },
  data() {
    return {
      tableLoading: false, // 表格加载
      queryShow: false, // 隐藏筛选显隐
      queryParams: { // 查询参数
        pageNum: 1, // 页码
        pageSize: 10, // 每页条数
        managementMaintenanceIds: [], // 管养处
        maintenanceSectionId: '', // 路段编号
        routeCodes: [], // 路线编号
        disasterNum: '', // 灾害编号
        disasterName: '', // 灾害名称
        disasterType: '', // 灾害类型
        pileStartNum: '', // 起始桩号
        pileEndNum: '', // 结束桩号
        expiryTime: '', // 结束时间
        disasterIds: [], //灾害ID数组
        checked: '' // 状态
      },
      queryTime: [], // 查询时间
      queryTotal: 0, // 总条数
      formProcessType: [],
      /**
       * 表格相关
       */
      tableData: [], // 表格数据
      currentRow: null,
      tableSelection: [],
    }
  },
  watch: {
    openDialog: {
      handler(newValue) {
        if (newValue) {
          this.initPage()
        }
      }
    }
  },
  methods: {
    // 点击查询按钮
    queryhandle() {
      this.queryList()
      // 查询字典类型数据
      this.queryType()
    },
    queryType() {
      this.getDicts("process_type").then((res) => {
        if (res.code === 200) {
          this.formProcessType = res.data
        }
      })
    },
    // 重置查询条件
    queryReset() {
      this.queryParams = { // 查询参数
        pageNum: 1, // 页码
        pageSize: 10, // 每页条数
        managementMaintenanceIds: [], // 管养处
        maintenanceSectionId: '', // 路段编号
        routeCodes: [], // 路线编号
        disasterNum: '', // 灾害编号
        disasterName: '', // 灾害名称
        disasterType: '', // 灾害类型
        pileStartNum: '', // 起始桩号
        pileEndNum: '', // 结束桩号
        expiryTime: '', // 结束时间
        disasterIds: [] //灾害ID数组
      }
      this.queryhandle();
    },
    // 勾选表格项改变时
    tableSelectionChange(val) {
      // this.tableSelection = val
    },
    handleSelect(selection, row) {
      this.clearSelection()
      this.$nextTick(() => {
        if (selection.includes(row)) {
          this.toggleRowSelection(row)
          this.currentRow = row
        } else {
          this.currentRow = {}
        }
      })
    },
    // 表格点击勾选
    handleRowClick(row) {
      this.clearSelection()
      this.$refs.tableRef.toggleRowSelection(row);
      this.currentRow = row
    },
    handleSelectAll(selection) {
      this.clearSelection()
      this.currentRow && this.toggleRowSelection(this.currentRow)
    },
    clearSelection() {
      this.$refs.tableRef.clearSelection()
    },
    toggleRowSelection(row) {
      this.$refs.tableRef.toggleRowSelection(row)
    },
    clearCurrentRow() {
      this.currentRow = {}
    },
    toCurrentRow() {
      if (Object.keys(this.currentRow).length > 0) {
        this.toggleRowSelection(this.currentRow)
      }
    },
    getRowKey(row) {
      // 根据你的数据结构，返回一个唯一的key，例如ID
      return row.id;
    },
    // 获取主表数据
    async queryList() {
      this.tableLoading = true
      await inspection.listByDisasterRisk(this.queryParams).then((res) => {
        if (res.code === 200) {
          this.tableData = res.rows
          this.queryTotal = res.total
        }
        this.tableLoading = false
      }).catch((err) => {
        this.tableLoading = false
        this.$message.error(err)
      })
    },
    initPage() {
      // 获取数据
      this.queryList()
    },
    handleCurrentChange() {
      // 获取数据
      this.queryList()
      this.handleSelectAll(); // 分页改变时恢复选中状态
    },
    // 关闭风险点选择弹框
    handleClose() {
      this.$emit('update:openDialog', false)
      this.clearSelection()
      this.clearCurrentRow()
    },
    async onRiskPointsSelect() {
      /*
      /inspection/getToDoInspection
      {
      riskId: 必填
      longitude: 必填
      latitude: 必填
      expiryTime: 不填默认当前
      }
       */
      if (this.currentRow?.id) {
        await inspection.inspectionGetToDoInspection({ riskId: this.currentRow.id }).then((res) => {
          if (res.code === 200) {
            this.$emit('selectRow', this.currentRow, res.data)
            this.handleClose()
          }
        }).catch((err) => {
          this.$message.error(err)
        })
      } else {
        this.$message.warning('请选择风险点！')
      }
    },
  },
}
</script>

<style lang="scss" scoped>
.dialog-content {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.dialog-scroll-area {
  flex: 1;
  overflow-y: auto;
  padding: 20px 30px;
}

.container {
  height: 100%;
  box-sizing: border-box;
}

.searchBox {
  padding: 10px 0;
  background: #fff;
  transition: all .1s linear;
  display: flex;
  flex-direction: column;

  .searchMoreBox {
    min-width: 192px;
    margin-top: 10px;
    display: flex;
    align-items: center;
    flex-direction: row;
  }
}

.dialog-footer {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 10px 0;
  background: #fff;
  border-top: 1px solid #e4e7ed;
}
</style>