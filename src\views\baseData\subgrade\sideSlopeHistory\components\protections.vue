<template>
  <el-dialog
    title="防护形式详情"
    :visible.sync="showDetail"
    width="55%"
    append-to-body
    :before-close="handleClose"
    :close-on-click-modal="true"
  >
    <el-form :model="formData">
      <el-col :span="6">
        <el-form-item label="边坡名称:">{{formData.slopeName}}</el-form-item>
      </el-col>
      <el-col :span="6">
        <el-form-item label="边坡编码:">{{formData.slopeCode}}</el-form-item>
      </el-col>
      <el-col :span="6">
        <el-form-item label="防护形式总数:">{{formData.protectionFormNumber}}</el-form-item>
      </el-col>
    </el-form>
    <el-table
      v-loading="loading"
      height="250px"
      border
      :data="tableData"
      :header-cell-style="{'background':'#F2F3F5','color': '#212529','height': '36px','font-weight': '700','font-size': '14px'}"
      :cell-style="{'height': '36px'}"
    >
      <el-table-column
        label="序号"
        type="index"
        width="50"
        align="center"
      />
      <el-table-column
        label="台高(m)"
        align="center"
        prop="platformHeight"
        min-width="120"
        show-overflow-tooltip
      />
      <el-table-column
        label="防护形式"
        align="center"
        prop="protectionForm"
        min-width="120"
        show-overflow-tooltip
      />
      <el-table-column
        label="坡比"
        align="center"
        prop="slopeRatio"
        min-width="120"
        show-overflow-tooltip
      />
    </el-table>
    <pagination
      :total="total"
      :page.sync="pageNum"
      :limit.sync="pageSize"
      @pagination="getList"
    />
    <div slot="footer">
      <el-button @click="handleClose">取 消</el-button>
    </div>
  </el-dialog>
</template>

<script>
import { getProtection } from '@/api/baseData/subgrade/sideSlopeHistory/index.js'

export default {
  name: 'sideSlope-protections',
  props: {
    showDetail: { type: Boolean, default: false },
    slopeId: { type: undefined, default: '' },
    formData: { type: Object, default: () => {} }
  },
  data() {
    return {
      loading: false,
      pageNum: 1,
      pageSize: 20,
      total: 0,
      tableData: []
    }
  },
  created() {
    this.getList()
  },
  methods: {
    getList() {
      if (!this.slopeId) return
      this.loading = true
      let obj = {
        slopeHisId: this.slopeId,
        pageNum: this.pageNum,
        pageSize: this.pageSize
      }
      getProtection(obj)
        .then(res => {
          if (res.code === 200) {
            this.tableData = res.rows
            this.total = res.total
          }
          this.loading = false
        })
        .catch(() => {
          this.loading = false
        })
    },
    handleClose() {
      this.$emit('close')
    }
  },
  watch: {
    // slopeId(newVal, oldVal) {
    //   this.tableData = []
    //   this.getList(newVal)
    // }
  }
}
</script>

<style lang="scss" scoped>
::v-deep .el-form-item {
  margin-bottom: 10px;
  color: #1d2129;
}
</style>