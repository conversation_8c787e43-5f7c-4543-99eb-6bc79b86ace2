<template>
	<div class="roadbedadd">
		<el-dialog
			:title="title"
			:visible.sync="showAddEdit"
			width="60%"
			append-to-body
			:before-close="handleClose"
			:close-on-click-modal="false"
			:class="forView ? 'forView' : ''"
		>
			<div v-loading="loading" style="height: 60vh; overflow-y: auto; padding: 0 10px 0 5px">
				<el-form
					ref="form"
					:model="form"
					:rules="rules"
					label-width="150px"
					:disabled="forView ? true : false"
				>
					<div style="display: flex; flex-wrap: wrap; margin-bottom: 20px">
						<el-divider content-position="left">基础数据</el-divider>
						<!-- <el-col :span="12">
              <el-form-item
                label="管理处"
                prop="managementMaintenanceId"
              >
                <SelectTree
                  v-model="form.managementMaintenanceId"
                  :dept-type="201"
                  placeholder="请选择"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item
                label="管养分处"
                prop="managementMaintenanceBranchId"
              >
                <SelectTree
                  v-model="form.managementMaintenanceBranchId"
                  :dept-type="202"
                  placeholder="请选择"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item
                label="养护路段"
                prop="maintenanceSectionId"
              >
                <el-select
                  v-model="form.maintenanceSectionId"
                  style="width: 100%;"
                  placeholder="请选择"
                  clearable
                  :disabled="!form.managementMaintenanceId"
                >
                  <el-option
                    v-for="item in routeOptions"
                    :key="item.maintenanceSectionId"
                    :label="item.maintenanceSectionName"
                    :value="item.maintenanceSectionId"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item
                label="路段类型"
                prop="sectionType"
              >
                <SectionSelect
                  v-model="form.sectionType"
                  :style="'width: 100%;' + (forView ? 'pointer-events: none' : '')"
                  :formObject="form"
                  :sectionId="form.maintenanceSectionId"
                  :disabled="!form.maintenanceSectionId"
                  clearable
                />
              </el-form-item>
            </el-col> -->
						<el-col :span="12">
							<el-form-item label="公路路段" prop="roadSectionId">
								<el-select
									v-model="form.roadSectionId"
									placeholder="请选择公路路段"
									clearable
									style="width: 100%"
								>
									<el-option
										v-for="item in routeList"
										:key="item.roadSectionId"
										:label="item.roadSectionName"
										:value="item.roadSectionId"
									></el-option>
								</el-select>
							</el-form-item>
						</el-col>

						<el-col :span="12">
							<el-form-item label="长度(m)" prop="routeLength">
								<el-input-number
									v-model="form.routeLength"
									style="width: 100%"
									:precision="3"
									:step="0.001"
									controls-position="right"
								/>
							</el-form-item>
						</el-col>
						<el-col :span="12">
							<el-form-item label="增长累计" prop="brokenLength">
								<el-input-number
									:disabled="true"
									v-model="form.brokenLength"
									style="width: 100%"
									:precision="3"
									:step="0.001"
									controls-position="right"
								/>
							</el-form-item>
						</el-col>
						<el-col :span="12">
							<el-form-item label="减短累计" prop="brokenShortLength">
								<el-input-number
									:disabled="true"
									v-model="form.brokenShortLength"
									style="width: 100%"
									:precision="3"
									:step="0.001"
									controls-position="right"
								/>
							</el-form-item>
						</el-col>
						<el-col :span="12">
							<el-form-item label="换算里程" prop="convertMileage">
								<el-input-number
									:disabled="true"
									v-model="form.convertMileage"
									style="width: 100%"
									:precision="3"
									:step="0.001"
									controls-position="right"
								/>
							</el-form-item>
						</el-col>

						<el-divider content-position="left">中心统一里程</el-divider>
						<el-col :span="12">
							<el-form-item label="起点桩号" prop="unifiedMileageStartStake">
								<PileInput v-model="form.unifiedMileageStartStake" />
							</el-form-item>
						</el-col>
						<el-col :span="12">
							<el-form-item label="终点桩号" prop="unifiedMileageEndStake">
								<PileInput v-model="form.unifiedMileageEndStake" />
							</el-form-item>
						</el-col>
						<el-col :span="12">
							<el-form-item label="统一里程桩号" prop="unifiedMileageStake">
								<PileInput v-model="form.unifiedMileageStake" />
							</el-form-item>
						</el-col>
						<el-col :span="12">
							<el-form-item label="管养里程(km)" prop="maintenanceMileage">
								<el-input-number
									v-model="form.maintenanceMileage"
									style="width: 100%"
									:precision="3"
									clearable
								/>
							</el-form-item>
						</el-col>
						<el-divider content-position="left">中心施工里程</el-divider>
						<el-col :span="12">
							<el-form-item label="起点桩号" prop="constructionMileageStartStake">
								<PileInput v-model="form.constructionMileageStartStake" />
							</el-form-item>
						</el-col>
						<el-col :span="12">
							<el-form-item label="终点桩号" prop="constructionMileageEndStake">
								<PileInput v-model="form.constructionMileageEndStake" />
							</el-form-item>
						</el-col>
						<el-col :span="12">
							<el-form-item label="施工里程(km)" prop="constructionMileage">
								<el-input-number
									v-model="form.constructionMileage"
									style="width: 100%"
									:precision="3"
									clearable
								/>
							</el-form-item>
						</el-col>
						<el-col :span="12">
							<el-form-item label="施工桩号" prop="constructionStake">
								<PileInput v-model="form.constructionStake" />
							</el-form-item>
						</el-col>
						<el-divider content-position="left">中心国高网里程</el-divider>
						<el-col :span="12">
							<el-form-item label="起点桩号" prop="nationalNetworkStartStake">
								<PileInput v-model="form.nationalNetworkStartStake" />
							</el-form-item>
						</el-col>
						<el-col :span="12">
							<el-form-item label="终点桩号" prop="nationalNetworkEndStake">
								<PileInput v-model="form.nationalNetworkEndStake" />
							</el-form-item>
						</el-col>
						<el-col :span="12">
							<el-form-item label="国高网里程(km)" prop="nationalNetworkMileage">
								<el-input-number
									v-model="form.nationalNetworkMileage"
									style="width: 100%"
									:precision="3"
									clearable
								/>
							</el-form-item>
						</el-col>
						<el-col :span="12">
							<el-form-item label="国高网桩号" prop="nationalNetworkStake">
								<PileInput v-model="form.nationalNetworkStake" />
							</el-form-item>
						</el-col>
					</div>

					<el-divider content-position="left">其他</el-divider>
					<el-col :span="12">
						<el-form-item label="移交管理单位" prop="transferManagementUnit">
							<el-input
								v-model="form.transferManagementUnit"
								style="width: 100%"
								controls-position="right"
							/>
						</el-form-item>
					</el-col>
					<el-col :span="12">
						<el-form-item label="备注" prop="remark">
							<el-input autosize v-model="form.remark" type="textarea" />
						</el-form-item>
					</el-col>
					<el-button type="primary" @click="handleAddFormList()">添加子表单</el-button>

					<div v-for="(itemL, index) in brokenChainDetailList" :key="index">
						<el-card shadow="never" style="margin: 10px 0">
							<div style="display: flex; justify-content: space-around; align-items: center">
								<div>
									<el-col v-for="(item, i) in itemL" :key="i" :span="item.span || 12">
										<el-form-item
											:label="item.label"
											:prop="item.prop"
											:rules="item.rules"
											label-width="100px"
										>
											<span v-if="item.type === 'input'">
												<el-input
													v-model="brokenChainDetailData[index][item.prop]"
													:placeholder="item.placeholder"
													:disabled="item.disabled"
													clearable
												/>
											</span>
											<span v-else-if="item.type === 'pileInput'">
												<PileInput
													v-model="brokenChainDetailData[index][item.prop]"
                          @input="changValue(brokenChainDetailData[index])"
												/>
											</span>
											<span v-else-if="item.type === 'inputNumber'">
												<el-input-number
													v-model="brokenChainDetailData[index][item.prop]"
													style="width: 100%"
													:precision="item.precision"
													:disabled="item.disabled"
													clearable
                          controls-position="right"
												/>
											</span>
											<span v-else-if="item.type === 'dictSelect'">
												<el-select
													v-model="brokenChainDetailData[index][item.prop]"
													style="width: 100%"
													placeholder="请选择"
													clearable
												>
													<el-option
														v-for="i in dict.type[item.dict]"
														:key="i.value"
														:label="i.label"
														:value="i.value"
													/>
												</el-select>
											</span>
										</el-form-item>
									</el-col>
								</div>
								<i
									@click="handleDeleteFormList(index)"
									class="el-icon-remove"
									style="color: red; cursor: pointer; margin-left: 20px"
								></i>
							</div>
						</el-card>
						<!-- <el-col :span="22" >
              <el-divider content-position="left"></el-divider>
            </el-col>
            <el-col :span="2" >
              <div class="closeStyle" @click="handleDeleteFormList">X</div>
            </el-col> -->
					</div>
				</el-form>
			</div>
			<div slot="footer">
				<el-button
					v-if="!forView"
					type="primary"
					:loading="loading"
					@click="handleSubmit('submit')"
				>
					提 交
				</el-button>
				<el-button
					v-if="!forView && (!formData.id || formData.status == 1)"
					type="primary"
					:loading="loading"
					@click="handleSubmit('save')"
				>
					暂 存
				</el-button>
				<el-button @click="handleClose">取 消</el-button>
			</div>
		</el-dialog>
	</div>
</template>

<script>
import PileInput from '@/components/PileInput/index.vue'
import SelectTree from '@/components/DeptTmpl/selectTree'
import SectionSelect from '@/components/SectionSelect'
import { listAllHighwaySections } from '@/api/system/highwaySections'
import { listMaintenanceSectionAll } from '@/api/system/maintenanceSection'
import { listByMaintenanceSectionId } from '@/api/baseData/common/routeLine'
import {
	addBrokenChain,
	tempBrokenChain,
	updateBrokenChain,
} from '@/api/baseData/road/brokenChain/index.js'

export default {
	name: 'brokenChain-form',
	components: { PileInput, SelectTree, SectionSelect },
	props: {
		formData: {
			default: {},
		},
		showAddEdit: {
			default: false,
		},
		title: {
			default: '添加统一里程数据',
		},
		forView: {
			default: false,
		},
	},
	dicts: ['route_left_right_margin'],
	data() {
		return {
			loading: false,
			form: {},
			brokenChainDetailData: [],
			brokenChainDetailList: [],
			rules: {
				roadSectionId: [{ required: true, message: '请选择公路', trigger: 'change' }],
				constructionMileageStartStake: [
					{ required: true, message: '请输入施工桩号起点', trigger: 'blur' },
				],
				constructionMileageEndStake: [
					{ required: true, message: '请输入施工桩号终点', trigger: 'blur' },
				],
			},
			routeOptions: [],
			routeList: [],
		}
	},
	created() {
		this.init()
		this.maintenanceSectionChange()
	},
	mounted() {},
	methods: {
		init() {
			if (this.formData.id) {
				this.form = JSON.parse(JSON.stringify(this.formData))
				this.form.brokenChainDetailList.map(() => {
					this.brokenChainDetailList.push([
						{
							label: '断链',
							prop: 'tendersName',
							placeholder: '请输入断链',
							type: 'input',
							span: 24,
						},
						{
							label: '原测桩号',
							prop: 'originalStake',
							placeholder: '请输入原测桩号',
							type: 'pileInput',
							precision: 3,
						},
						{
							label: '断链桩号',
							prop: 'brokenStake',
							placeholder: '请输入断链桩号',
							type: 'pileInput',
							precision: 3,
						},
						{
							label: '统一里程桩号',
							prop: 'unifiedMileageStake',
							placeholder: '请输入统一里程桩号',
							type: 'pileInput',
							precision: 3,
						},
						{
							label: '长链',
							prop: 'brokenLength',
							placeholder: '请输入断链',
							type: 'inputNumber',
							precision: 3,
							disabled: true,
						},
						{
							label: '短链',
							prop: 'brokenShortLength',
							placeholder: '请输入短链',
							type: 'inputNumber',
							precision: 3,
							disabled: true,
						},
						{
							label: '左右幅',
							prop: 'leftRightMargin',
							placeholder: '请选择左右幅',
							type: 'dictSelect',
							dict: 'route_left_right_margin',
						},

						{
							label: '备注',
							prop: 'remark',
							placeholder: '请输入备注',
							type: 'input',
						},
					])
				})
				this.brokenChainDetailData = this.form.brokenChainDetailList
			}
		},
		handleAddFormList() {
			this.brokenChainDetailData.push({
				bridgeName: '',
				originalStake: '',
				brokenStake: '',
				brokenLength: '',
				leftRightMargin: '',
				remark: '',
				brokenShortLength: '',
				unifiedMileageStake: '',
			})
			this.brokenChainDetailList.push([
				{
					label: '断链',
					prop: 'tendersName',
					placeholder: '请输入断链',
					type: 'input',
					span: 24,
				},
				{
					label: '原测桩号',
					prop: 'originalStake',
					placeholder: '请输入原测桩号',
					type: 'pileInput',
					precision: 3,
				},
				{
					label: '断链桩号',
					prop: 'brokenStake',
					placeholder: '请输入断链桩号',
					type: 'pileInput',
					precision: 3,
				},
				{
					label: '统一里程桩号',
					prop: 'unifiedMileageStake',
					placeholder: '请输入统一里程桩号',
					type: 'pileInput',
					precision: 3,
				},
				{
					label: '长链',
					prop: 'brokenLength',
					placeholder: '请输入断链',
					type: 'inputNumber',
					precision: 3,
					disabled: true,
				},
				{
					label: '短链',
					prop: 'brokenShortLength',
					placeholder: '请输入短链',
					type: 'inputNumber',
					precision: 3,
					disabled: true,
				},
				{
					label: '左右幅',
					prop: 'leftRightMargin',
					placeholder: '请选择左右幅',
					type: 'dictSelect',
					dict: 'route_left_right_margin',
				},

				{
					label: '备注',
					prop: 'remark',
					placeholder: '请输入备注',
					type: 'input',
				},
			])
		},
		handleDeleteFormList(index) {
      this.brokenChainDetailData.splice(index, 1)
      this.brokenChainDetailList.splice(index, 1)

      this.form.brokenLength = this.brokenChainDetailData
				.reduce((total, item) => {
					return total + Number(item.brokenLength)
				}, 0)
				.toFixed(3)

			this.form.brokenShortLength = this.brokenChainDetailData
				.reduce((total, item) => {
					return total + Number(item.brokenShortLength)
				}, 0)
				.toFixed(3)

			this.form.convertMileage = this.form.brokenLength - this.form.brokenShortLength
		},
		handleSubmit(type) {
			let pass = true
			switch (type) {
				case 'submit':
					this.form.status = 2
					break
				case 'save':
					this.form.status = 1
					break
			}
			if (this.form.status == 2) {
				this.$refs.form.validate((valid) => {
					if (valid) {
						pass = true
					} else {
						pass = false
						return false
					}
				})
			}
			if (!pass) return

			this.form.brokenChainDetailList = this.brokenChainDetailData

			let arr = this.routeList.filter((el) => {
				return el.roadSectionId === this.form.roadSectionId
			})
			this.form.roadSectionName = arr[0]?.roadSectionName

			this.loading = true
			if (this.form.id != null) {
				const api = this.form.status === 1 ? tempBrokenChain : updateBrokenChain

				api(this.form)
					.then((response) => {
						this.$modal.msgSuccess('修改成功')
						this.$emit('refresh')
					})
					.catch(() => {
						this.loading = false
					})
			} else {
				const api = this.form.status === 1 ? tempBrokenChain : addBrokenChain

				api(this.form)
					.then((response) => {
						this.$modal.msgSuccess(this.form.status === 1 ? '暂存成功' : '新增成功')
						this.$emit('refresh')
					})
					.catch(() => {
						this.loading = false
					})
			}
		},
		deptChange(e) {
			if (!e) return
			listMaintenanceSectionAll({ departmentId: e }).then((res) => {
				if (res.code == 200) {
					this.routeOptions = res.data
				}
			})
		},
		maintenanceSectionChange(e) {
			listAllHighwaySections({}).then((res) => {
				if (res.code == 200) {
					this.routeList = res.data || []
					// this.form.routeCode = ''
					this.$forceUpdate()
				}
			})
		},
		handleClose() {
			if (this.forView) {
				this.form = {}
				this.$emit('close', false)
			} else {
				this.$modal
					.confirm('确认退出？')
					.then(() => {
						this.form = {}
						this.$emit('close', false)
					})
					.catch(() => {})
			}
		},
		changValue(itemData) {
			//itemData.originalStake减去itemData.brokenStake,如果是正数，减去的值就是itemData.brokenLength乘1000，如果是负数，减去的值就是itemData.brokenShortLength乘1000

			let brokenLength = (Number(itemData.originalStake) - Number(itemData.brokenStake))
			if (brokenLength > 0) {
				this.$set(itemData, 'brokenLength', brokenLength.toFixed(3))
				this.$set(itemData, 'brokenShortLength', '0.000')
			} else {
				this.$set(itemData, 'brokenShortLength', Math.abs(brokenLength).toFixed(3))
				this.$set(itemData, 'brokenLength', '0.000')
			}

			this.form.brokenLength = this.brokenChainDetailData
				.reduce((total, item) => {
					return total + Number(item.brokenLength)
				}, 0)
				.toFixed(3)

			this.form.brokenShortLength = this.brokenChainDetailData
				.reduce((total, item) => {
					return total + Number(item.brokenShortLength)
				}, 0)
				.toFixed(3)

			this.form.convertMileage = this.form.brokenLength - this.form.brokenShortLength
		},
	},

	watch: {
		'form.managementMaintenanceId'(newVal, oldVal) {
			if (newVal) {
				this.deptChange(newVal)
			}
			if (!newVal && this.form.routeCode) {
				this.form.routeCode = ''
			}
		},
		'form.maintenanceSectionId'(newVal, oldVal) {
			if (newVal) {
				this.maintenanceSectionChange(newVal)
			}
			if (!newVal && this.form.routeCode) {
				this.form.routeCode = ''
			}
		},
	},
}
</script>

<style lang="scss" scoped>
.forView ::v-deep .el-input.is-disabled .el-input__inner,
.forView ::v-deep .el-textarea.is-disabled .el-textarea__inner {
	background-color: white;
	border-color: #dfe4ed;
	color: black;
}
::v-deep .el-dialog__header {
	border-bottom: 1px #dfe4ed solid;
	padding: 20px 30px !important;
}
::v-deep .el-divider--horizontal {
	margin: 20px 0 !important;
}
.brokenChain-protection-card {
	width: 100%;
	border: #dfe4ed 1px solid;
	border-radius: 10px;
	padding: 20px 40px 0 0;
	position: relative;
	margin-bottom: 10px;
	i {
		cursor: pointer;
		position: absolute;
		right: 5px;
		top: 45%;
		color: #f56c6c;
	}
}
::v-deep .inputNumber {
	.el-input-number__decrease,
	.el-input-number__increase {
		display: block;
		height: 30px;
		margin-top: 1px;
		display: flex;
		justify-content: center;
		align-items: center;
	}
}

.closeStyle {
	width: 30px;
	height: 30px;
	border-radius: 50%;
	font-weight: 800;
	font-size: 20px;
	border: 1px solid;
	text-align: center;
	position: relative;
	top: 5px;
	left: 15px;
}
</style>
<style lang="scss">
.longitude-latitude {
	.el-input-group__prepend {
		padding: 0 10px;
	}
	.el-input__inner {
		padding: 0 5px;
	}
}
</style>
