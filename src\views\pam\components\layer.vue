<template>
  <div class="layer">
    <section class="layer-list" v-if="showLayer">
      <div v-for="(item, index) in layerList" :key="index" class="list-item" @click="handleClick(item, index)"
        :class="item.ifShow ? 'click-act' : ''">
        <span>{{ item.layerName }}</span>
      </div>
    </section>
    <img src="@/assets/map/spread.png" alt="" class="spread-img" v-if="showLayer" />
    <img src="@/assets/map/layer.png" class="layer-img" @click="toShow" />
  </div>
</template>

<script>
import { mapState, mapActions, mapMutations } from 'vuex';
import { getLayersByName } from '@/views/map/components/common/mapFun'

export default {
  data() {
    return {
      showLayer: false,
      clickIndex: 0,
    }
  },
  computed: {
    ...mapState({
      layerList: state => state.map.layerList,
      layerSetting: state => state.map.layerSetting,
    })
  },
  methods: {
    ...mapActions({}),
    ...mapMutations({
      setLayer: 'map/setLayer',
    }),
    handleClick(row, index) {
      this.clickIndex = index;
      row.ifShow = !row.ifShow;
      let layer = getLayersByName('name', row.layerName)
      if (!layer) return;
      layer.setVisible(row.ifShow)
      // 修改图层设置数据
      let arr = this.layerSetting.map(v => {
        if (v.layerName == row.layerName) {
          v.ifShow = row.ifShow
        }
        return v
      })
      this.setLayer({ layer: arr })
    },
    // 展示隐藏
    toShow() {
      this.showLayer = !this.showLayer;
    }
  }
}
</script>

<style lang="scss" scoped>
@import "@/assets/styles/utils.scss";

.layer {
  position: absolute;
  min-width: vwpx(200px);
  max-width: vwpx(300px);
  right: vwpx(20px);
  bottom: vwpx(60px);
  cursor: pointer;
  z-index: 2;

  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;

  .layer-list {
    min-width: vwpx(200px);
    min-height: vwpx(140px);
    max-height: vwpx(520px);
    overflow-y: auto;
    border-radius: 4px;
    border: 1px dashed #0687FF;
    box-shadow: inset 0 0 10px rgba(54, 98, 236, 0.6);
    padding: vwpx(20px) 0;

    .list-item {
      display: flex;
      align-items: center;
      justify-content: center;
      height: vwpx(60px);
      color: #ffffff;
    }

    .click-act {
      background: rgba(6, 135, 255, 0.2);
      font-family: Microsoft YaHei, Microsoft YaHei;
      font-weight: 400;
      color: #00FDFD;
      height: vwpx(60px);
      margin: 0 5px;
    }

  }

  .spread-img {
    width: 20%;
    height: 20%;
    margin-bottom: 5px;
  }

  .layer-img {
    width: vwpx(100px);
    height: vwpx(100px);
  }
}
</style>