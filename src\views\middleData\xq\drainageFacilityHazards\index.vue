<template>
  <div class="app-container">
    <el-row :gutter="20">

      <!--筛选区开始-->
      <el-col :span="24" :xs="24">
        <el-row>
          <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" label-width="68px">
            <el-col :span="24" style="display: flex;">
              <el-form-item label="">
                <CascadeSelection style="min-width: 192px" :form-data="queryParams" v-model="queryParams"
                  @update:fromData="() => { }" types="201" multiple />
              </el-form-item>
              <el-form-item label="" prop="hazardLevelList">
                <el-select v-model="queryParams.hazardLevelList" placeholder="隐患等级" clearable multiple collapse-tags
                  style="width: 240px" size="small">
                  <el-option v-for="dict in hazardLevelOptions" :key="dict.dictValue" :label="dict.dictLabel"
                    :value="dict.dictValue" />
                </el-select>
              </el-form-item>
              <el-form-item label="" prop="participant">
                <el-input v-model="queryParams.participant" placeholder="请输入参与人" clearable prefix-icon="el-icon-user"
                  style="width: 240px" @keyup.enter.native="handleQuery" />
              </el-form-item>
              <el-form-item>
                <el-button type="primary" icon="el-icon-search" size="small" @click="handleQuery">搜索</el-button>
                <el-button icon="el-icon-refresh" size="small" @click="resetQuery">重置</el-button>
                <el-button v-show="!showSearch" @click="showSearch = true" icon="el-icon-arrow-down" circle></el-button>
                <el-button v-show="showSearch" @click="showSearch = false" icon="el-icon-arrow-up" circle></el-button>
              </el-form-item>
            </el-col>
            <el-col :span="24" v-show="showSearch">
              <!--默认折叠 ,此处仅作为示例-->
              <el-form-item label="巡查时间" prop="createTime">
                <el-date-picker v-model="dateRange" style="width: 240px" value-format="yyyy-MM-dd" type="daterange"
                  range-separator="-" start-placeholder="开始日期" end-placeholder="结束日期" size="small"></el-date-picker>
              </el-form-item>

              <el-form-item label="" prop="roadType">
                <el-select v-model="queryParams.roadType" placeholder="公路类型" clearable style="width: 240px"
                  size="small">
                  <el-option v-for="dict in roadTypeOptions" :key="dict.dictValue" :label="dict.dictLabel"
                    :value="dict.dictValue" />
                </el-select>
              </el-form-item>
              <el-form-item label="" prop="rectificationCompleted">
                <el-select v-model="queryParams.rectificationCompleted" placeholder="是否完成整治" clearable
                  style="width: 240px" size="small">
                  <el-option v-for="dict in measuresOptions" :key="dict.dictValue" :label="dict.dictLabel"
                    :value="dict.dictValue" />
                </el-select>
              </el-form-item>
            </el-col>
          </el-form>
        </el-row>
        <!--筛选区结束-->


        <!--操作按钮区开始-->
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button type="primary" icon="el-icon-plus" size="mini" @click="handleAdd"
              v-hasPermi="['middleData:drainageFacilityHazards:add']">新增</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="success" icon="el-icon-edit" size="mini" :disabled="single" @click="handleUpdate"
              v-hasPermi="['middleData:drainageFacilityHazards:edit']">修改</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="danger" icon="el-icon-delete" size="mini" :disabled="multiple" @click="handleDelete"
              v-hasPermi="['middleData:drainageFacilityHazards:remove']">删除</el-button>
          </el-col>
          <!-- <el-col :span="1.5">
            <el-button
                type="info"
                plain
                icon="el-icon-upload2"
                size="mini"
                @click="handleImport"
                v-hasPermi="['middleData:drainageFacilityHazards:export']"
            >导入</el-button>
          </el-col> -->
          <el-col :span="1.5">
            <el-button type="warning" icon="el-icon-download" size="mini" @click="handleExport"
              v-hasPermi="['middleData:drainageFacilityHazards:export']">导出</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="primary" plain icon="el-icon-check" size="mini" :disabled="multiple"
              @click="handleBatchAudit" v-hasPermi="['middleData:drainageFacilityHazards:audit']">批量审核</el-button>
          </el-col>
          <right-toolbar :showSearch.sync="showSearch" @queryTable="getList" :columns="columns"></right-toolbar>
        </el-row>
        <!--操作按钮区结束-->

        <!--数据表格开始-->
        <div class="tableDiv">
          <el-table v-adjust-table ref="table" size="mini"
            :height="showSearch ? 'calc(100vh - 320px)' : 'calc(100vh - 260px)'" style="width: 100%" v-loading="loading"
            border :data="drainageFacilityHazardsList" @selection-change="handleSelectionChange" :row-style="rowStyle"
            @row-click="handleRowClick" :header-cell-style="{ 'text-align': 'center' }"
            :cell-style="{ 'text-align': 'center' }">
            <el-table-column type="selection" width="50" align="center" fixed />
            <el-table-column fixed label="序号" type="index" width="50">
              <template v-slot="scope">
                {{ (scope.$index + (queryParams.pageNum - 1) * queryParams.pageSize) + 1 }}
              </template>
            </el-table-column>
            <el-table-column label="省份" align="center" prop="province" width="80" show-overflow-tooltip
              v-if="cmpColumns('省份')" />
            <el-table-column label="市/州" align="center" prop="city" width="80" show-overflow-tooltip
              v-if="cmpColumns('市/州')" />
            <el-table-column label="管理处" align="center" prop="managementOffice" width="120" show-overflow-tooltip
              v-if="cmpColumns('管理处')" />
            <el-table-column label="分处" align="center" prop="subOffice" width="120" show-overflow-tooltip
              v-if="cmpColumns('分处')" />
            <el-table-column label="养护路段" align="center" prop="maintenanceSection" width="120" show-overflow-tooltip
              v-if="cmpColumns('养护路段')" />
            <el-table-column label="公路类型" align="center" prop="roadType" width="100" show-overflow-tooltip
              v-if="cmpColumns('公路类型')">
              <template slot-scope="scope">
                <span>{{ formatRoadType(scope.row.roadType) }}</span>
              </template>
            </el-table-column>
            <el-table-column label="公路编号" align="center" prop="roadNumber" width="100" show-overflow-tooltip
              v-if="cmpColumns('公路编号')" />
            <el-table-column label="起点桩号" align="center" prop="startStake" width="100" show-overflow-tooltip
              v-if="cmpColumns('起点桩号')">
              <template slot-scope="scope">
                <span>{{ formatStakeNumber(scope.row.startStake) }}</span>
              </template>
            </el-table-column>
            <el-table-column label="止点桩号" align="center" prop="endStake" width="100" show-overflow-tooltip
              v-if="cmpColumns('止点桩号')">
              <template slot-scope="scope">
                <span>{{ formatStakeNumber(scope.row.endStake) }}</span>
              </template>
            </el-table-column>
            <!-- <el-table-column label="灾害风险隐患类型" align="center" prop="hazardType" width="140" show-overflow-tooltip v-if="cmpColumns('灾害风险隐患类型')"/> -->
            <el-table-column label="隐患等级" align="center" prop="hazardLevel" width="100" show-overflow-tooltip
              v-if="cmpColumns('隐患等级')">
              <template slot-scope="scope">
                <span>{{ formatHazardLevel(scope.row.hazardLevel) }}</span>
              </template>
            </el-table-column>
            <el-table-column label="隐患描述" align="center" prop="hazardDescription" show-overflow-tooltip width="120"
              v-if="cmpColumns('隐患描述')" />
            <el-table-column label="照片" align="center" prop="photo" width="100" v-if="cmpColumns('照片')">
              <template slot-scope="scope">
                <el-button v-if="scope.row.photo" type="primary" icon="el-icon-picture-outline" size="mini" circle
                  @click.stop="previewImage(scope.row.photo)"></el-button>
                <span v-else>无图片</span>
              </template>
            </el-table-column>
            <el-table-column label="是否完成整治" align="center" prop="rectificationCompleted" width="110"
              show-overflow-tooltip v-if="cmpColumns('是否完成整治')">
              <template slot-scope="scope">
                <span>{{ formatMeasures(scope.row.rectificationCompleted) }}</span>
              </template>
            </el-table-column>
            <el-table-column label="已(拟)整治措施" align="center" prop="rectificationMeasures" show-overflow-tooltip
              width="120" v-if="cmpColumns('已(拟)整治措施')" />
            <el-table-column label="整治完成时限" align="center" prop="completionDeadline" width="120"
              v-if="cmpColumns('整治完成时限')">
              <template v-slot="scope">
                <span>{{ parseTime(scope.row.completionDeadline, '{y}-{m}-{d}') }}</span>
              </template>
            </el-table-column>
            <el-table-column label="省级责任单位及人员" align="center" prop="provincialResponsible" width="150"
              show-overflow-tooltip v-if="cmpColumns('省级责任单位及人员')" />
            <el-table-column label="复核责任单位及人员" align="center" prop="reviewResponsible" width="150" show-overflow-tooltip
              v-if="cmpColumns('复核责任单位及人员')" />
            <el-table-column label="排查责任单位及人员" align="center" prop="inspectionResponsible" width="150"
              show-overflow-tooltip v-if="cmpColumns('排查责任单位及人员')" />
            <el-table-column label="备注" align="center" prop="remarks" show-overflow-tooltip width="120"
              v-if="cmpColumns('备注')" />
            <el-table-column label="巡查人" align="center" prop="checkerName" width="100" show-overflow-tooltip
              v-if="cmpColumns('巡查人')" />
            <el-table-column label="审核人" align="center" prop="auditName" width="100" show-overflow-tooltip
              v-if="cmpColumns('审核人')" />
            <el-table-column label="审核状态" align="center" prop="auditStatus" width="100" show-overflow-tooltip
              v-if="cmpColumns('审核状态')">
              <template slot-scope="scope">
                <span>{{ formatAuditStatus(scope.row.auditStatus) }}</span>
              </template>
            </el-table-column>
            <el-table-column label="审核意见" align="center" prop="auditContent" show-overflow-tooltip width="120"
              v-if="cmpColumns('审核意见')" />
            <el-table-column label="巡查时间(填报时间)" align="center" prop="checkTime" width="150" show-overflow-tooltip
              v-if="cmpColumns('巡查时间(填报时间)')">
              <template v-slot="scope">
                <span>{{ parseTime(scope.row.checkTime, '{y}-{m}-{d}') }}</span>
              </template>
            </el-table-column>
            <el-table-column label="审核时间" align="center" prop="auditTime" width="120" show-overflow-tooltip
              v-if="cmpColumns('审核时间')">
              <template v-slot="scope">
                <span>{{ parseTime(scope.row.auditTime, '{y}-{m}-{d}') }}</span>
              </template>
            </el-table-column>
            <el-table-column label="操作" fixed="right" align="center" width="160" class-name="small-padding fixed-width">
              <template slot-scope="scope">
                <el-button size="mini" type="text" icon="el-icon-edit" @click="handleUpdate(scope.row)"
                  v-hasPermi="['middleData:drainageFacilityHazards:edit']">修改</el-button>
                <el-button size="mini" type="text" icon="el-icon-delete" @click="handleDelete(scope.row)"
                  v-hasPermi="['middleData:drainageFacilityHazards:remove']">删除</el-button>
                <el-button size="mini" type="text" icon="el-icon-check" @click="handleAudit(scope.row)"
                  v-hasPermi="['middleData:drainageFacilityHazards:audit']">审核</el-button>
              </template>
            </el-table-column>
          </el-table>

          <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum"
            :limit.sync="queryParams.pageSize" @pagination="getList" />
        </div>
        <!--数据表格结束-->
      </el-col>
    </el-row>
    <!-- 添加或修改涉灾隐患点—防洪排水设施对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="70%" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="140px">
        <div class="infoBox" style="margin-bottom: 20px;">
          <div class="infoTitle">基本信息</div>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="省份" prop="province">
                <el-input v-model="form.province" placeholder="请输入省份" disabled />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="市/州" prop="city">
                <el-select v-model="form.city" placeholder="请选择市/州" style="width: 100%;">
                  <el-option v-for="item in cityList" :key="item" :label="item" :value="item">
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="12">
              <!-- <el-form-item label="管理处" prop="managementMaintenanceBranchId">
                <el-select v-model="form.managementMaintenanceBranchId" clearable ref="formSelectTree"
                  placeholder="请输入管理处" @clear="formSelectClear" @remove-tag="formRemoveTag" style="width: 100%">
                  <el-option hidden v-for="(item, index) in formatData(deptOptions)"
                    :key="item.label + item.value + '-' + index" :label="item.label" :value="item.value">
                  </el-option>
                  <div @click.stop>
                    <el-tree style="font-weight: 400" :data="deptOptions" :props="defaultProps"
                      @check="formHandleNodeCheck" @node-click="formNodeClick" node-key="id" check-on-click-node
                      ref="formTreeRef" @click.native.stop="handleTreeClick">
                    </el-tree>
                  </div>
                </el-select>
              </el-form-item> -->
              <el-form-item label="管理处" prop="subOfficeId">
                <selectTree :key="'field2'" v-model="form.subOfficeId" :deptType="100" :deptTypeList="[1, 3, 4]"
                  placeholder="请选择管理处" clearable filterable />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <!-- <el-form-item label="养护路段" prop="maintenanceSectionId">
                <el-select v-model="form.maintenanceSectionId" placeholder="请选择养护路段" clearable filterable
                  @change="formMaintenanceSectionChange" style="width: 100%">
                  <el-option v-for="item in routeOptions" :key="item.maintenanceSectionId"
                    :label="item.maintenanceSectionName" :value="item.maintenanceSectionId">
                  </el-option>
                </el-select>
              </el-form-item> -->
              <el-form-item label="养护路段" prop="maintenanceSectionId">
                <RoadSection v-model="form.maintenanceSectionId" :deptId="form.subOfficeId"
                  @change="maintenanceSectionIdChange" placeholder="请选择养护路段" style="width: 100%;" />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="起点桩号" prop="startStake">
                <div style="width: 100%; display: flex; flex-direction: row; flex-wrap: nowrap;">
                  <el-tag type="info" effect="plain" size="medium">K</el-tag>
                  <el-input v-model="form.beginMile1" controls-position="right" style="width: 50%;" placeholder="千位以上"
                    @input="handleStartStakeInput" @change="handleStartStakeChange" />
                  <el-tag type="info" effect="plain" size="medium">+</el-tag>
                  <el-input v-model="form.beginMile2" controls-position="right" style="width: 50%;"
                    placeholder="千位以下(三位)" @input="handleStartStakeInput" @change="handleStartStakeChange" />
                </div>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="止点桩号" prop="endStake">
                <div style="width: 100%; display: flex; flex-direction: row; flex-wrap: nowrap;">
                  <el-tag type="info" effect="plain" size="medium">K</el-tag>
                  <el-input v-model="form.endMile1" controls-position="right" style="width: 50%;" placeholder="千位以上"
                    @input="handleEndStakeInput" @change="handleEndStakeChange" />
                  <el-tag type="info" effect="plain" size="medium">+</el-tag>
                  <el-input v-model="form.endMile2" controls-position="right" style="width: 50%;" placeholder="千位以下(三位)"
                    @input="handleEndStakeInput" @change="handleEndStakeChange" />
                </div>
              </el-form-item>
            </el-col>
          </el-row>
        </div>

        <div class="infoBox" style="margin-bottom: 20px;">
          <div class="infoTitle">隐患信息</div>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="隐患等级" prop="hazardLevel">
                <el-select v-model="form.hazardLevel" placeholder="请选择隐患等级" style="width: 100%">
                  <el-option v-for="dict in hazardLevelOptions" :key="dict.dictValue" :label="dict.dictLabel"
                    :value="dict.dictValue" />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="24">
              <el-form-item label="隐患描述" prop="hazardDescription">
                <el-input v-model="form.hazardDescription" type="textarea" :rows="3" placeholder="请输入内容" />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="经度" prop="longitude">
                <el-input v-model="form.longitude" placeholder="请输入经度">
                  <el-button slot="append" icon="el-icon-location"
                    @click="showCoordinatePicker('longitude')">坐标拾取</el-button>
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="纬度" prop="latitude">
                <el-input v-model="form.latitude" placeholder="请输入纬度">
                  <el-button slot="append" icon="el-icon-location"
                    @click="showCoordinatePicker('latitude')">坐标拾取</el-button>
                </el-input>
              </el-form-item>
            </el-col>
          </el-row>
        </div>

        <!-- 照片上传 -->
        <div class="infoBox" style="margin-bottom: 20px;">
          <div class="infoTitle">照片上传</div>
          <el-row>
            <el-col :span="24">
              <el-form-item label="照片" prop="photo">
                <FileUpload v-model="form.photo" :fileType="['png', 'jpg', 'jpeg', 'PNG', 'JPG', 'JPEG']" :fileSize="10"
                  :limit="5" :isShowTip="true" />
              </el-form-item>
            </el-col>
          </el-row>
        </div>

        <div class="infoBox" style="margin-bottom: 20px;">
          <div class="infoTitle">整治信息</div>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="是否完成整治" prop="rectificationCompleted">
                <el-select v-model="form.rectificationCompleted" placeholder="请选择是否完成整治" style="width: 100%">
                  <el-option v-for="dict in measuresOptions" :key="dict.dictValue" :label="dict.dictLabel"
                    :value="dict.dictValue" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="整治完成时限" prop="completionDeadline">
                <el-date-picker clearable v-model="form.completionDeadline" type="date" value-format="yyyy-MM-dd"
                  placeholder="请选择整治完成时限" style="width: 100%" />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="24">
              <el-form-item label="整治措施" prop="rectificationMeasures">
                <el-input v-model="form.rectificationMeasures" type="textarea" :rows="3" placeholder="请输入内容" />
              </el-form-item>
            </el-col>
          </el-row>
        </div>

        <div class="infoBox" style="margin-bottom: 20px;">
          <div class="infoTitle">责任信息</div>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="省级责任单位及人员" prop="provincialResponsible">
                <el-input v-model="form.provincialResponsible" placeholder="请输入省级责任单位及人员" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="复核责任单位及人员" prop="reviewResponsible">
                <el-input v-model="form.reviewResponsible" placeholder="请输入复核责任单位及人员" />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="排查责任单位及人员" prop="inspectionResponsible">
                <el-input v-model="form.inspectionResponsible" placeholder="请输入排查责任单位及人员" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="备注" prop="remarks">
                <el-input v-model="form.remarks" type="textarea" :rows="3" placeholder="请输入内容" />
              </el-form-item>
            </el-col>
          </el-row>
        </div>

        <div class="infoBox" style="margin-bottom: 20px;">
          <div class="infoTitle">巡查信息</div>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="巡查人" prop="checkerName">
                <el-input v-model="form.checkerName" placeholder="请输入巡查人" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="巡查时间" prop="checkTime">
                <el-date-picker clearable v-model="form.checkTime" type="date" value-format="yyyy-MM-dd"
                  placeholder="请选择巡查时间" style="width: 100%" />
              </el-form-item>
            </el-col>
          </el-row>
        </div>

        <div class="infoBox" style="margin-bottom: 20px;" v-if="form.id">
          <div class="infoTitle">审核信息</div>
          <el-row :gutter="20">
            <el-col :span="24">
              <el-form-item label="审核意见">
                <el-input v-model="form.auditContent" type="textarea" :rows="3" placeholder="请输入内容" />
              </el-form-item>
            </el-col>
          </el-row>
        </div>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 用户导入对话框 -->
    <el-dialog :title="upload.title" :visible.sync="upload.open" width="400px" append-to-body>
      <el-upload ref="upload" :limit="1" accept=".xlsx, .xls" :headers="upload.headers"
        :action="upload.url + '?updateSupport=' + upload.updateSupport" :disabled="upload.isUploading"
        :on-progress="handleFileUploadProgress" :on-success="handleFileSuccess" :auto-upload="false" drag>
        <i class="el-icon-upload"></i>
        <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
        <div class="el-upload__tip text-center" slot="tip">
          <div class="el-upload__tip" slot="tip">
            <el-checkbox v-model="upload.updateSupport" /> 是否更新已经存在的用户数据
          </div>
          <span>仅允许导入xls、xlsx格式文件。</span>
          <el-link type="primary" :underline="false" style="font-size:12px;vertical-align: baseline;"
            @click="importTemplate">下载模板</el-link>
        </div>
      </el-upload>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitFileForm">确 定</el-button>
        <el-button @click="upload.open = false">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 批量审核对话框 -->
    <audit-dialog :visible.sync="auditDialog.open" :title="auditDialog.title" :ids="ids" @submit="submitAudit" />

    <!-- 添加图片预览对话框 -->
    <el-dialog title="照片预览" :visible.sync="imagePreview.visible" width="800px" append-to-body center
      :close-on-click-modal="true" class="image-preview-dialog">
      <div class="image-preview-container">
        <FileUpload v-if="imagePreview.url" v-model="imagePreview.url" :key="imagePreview.url" for-view></FileUpload>
      </div>
    </el-dialog>

    <!-- 坐标拾取组件 -->
    <coordinate-picker :visible.sync="coordinatePickerVisible" :initialPosition="coordinatePickerInitPosition"
      @save="handleCoordinateSave" />
  </div>
</template>

<script>
import { listDrainageFacilityHazards, getDrainageFacilityHazards, delData, addDrainageFacilityHazards, updateDrainageFacilityHazards, batchAudit } from "@/api/middleData/xq/drainageFacilityHazards";
import { getToken } from "@/utils/auth";
import Treeselect from "@riophae/vue-treeselect";
import "@riophae/vue-treeselect/dist/vue-treeselect.css";
import AuditDialog from '@/views/middleData/xq/audit/index.vue';
import { getTreeStruct } from '@/api/tmpl';
import { listByMaintenanceSectionId, getMaintenanceSectionListAll } from '@/api/baseData/common/routeLine';
import { listAllRoute } from '@/api/system/route.js';
import FileUpload from '@/components/FileUpload';
import { parseTime } from "@/utils/ruoyi";
import { formatStake, parseStake } from "@/utils/common";
import CascadeSelection from '@/components/CascadeSelectionManagementOffice/index.vue'; // 管养处/路段/路线
import CoordinatePicker from '@/components/CoordinatePicker'; // 引入坐标拾取组件
import selectTree from "@/components/DeptTmpl/selectTree.vue";
import RoadSection from "../floodControlSignHazards/components/index.vue"
export default {
  name: "DrainageFacilityHazards",
  components: { Treeselect, AuditDialog, FileUpload, CascadeSelection, CoordinatePicker, selectTree, RoadSection },
  data() {
    const validateStartStake = (rule, value, callback) => {
      if ((this.form.beginMile1 == 0 || this.form.beginMile1) && (this.form.beginMile2 == 0 || this.form.beginMile2)) {
        callback();
      } else {
        callback(new Error('起点桩号(KXXX+XXX格式)不能为空'));
      }
    };
    const validateEndStake = (rule, value, callback) => {
      if ((this.form.endMile1 == 0 || this.form.endMile1) && (this.form.endMile2 == 0 || this.form.endMile2)) {
        callback();
      } else {
        callback(new Error('终点桩号(KXXX+XXX格式)不能为空'));
      }
    };
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 级联选择相关
      deptOptions: [], // 管养处数据
      routeOptions: [], // 路段选项
      routeList: [], // 路线列表
      cascadeObj: {}, // 级联选择返回数据
      defaultProps: {
        children: 'children',
        label: 'label',
      },
      treeValue: {},
      // 显示搜索条件
      showSearch: false,
      dictType: [],
      // 日期范围
      dateRange: [],
      // 总条数
      total: 0,
      // 涉灾隐患点—防洪排水设施表格数据
      drainageFacilityHazardsList: null,
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 图片预览数据
      imagePreview: {
        visible: false,
        url: null
      },
      // 表单参数
      form: {
        endMile1: '',
        endMile2: '',
        startMile1: '',
        startMile2: '',
      },
      // 桩号表单
      startStakeForm: {
        k: '',
        m: ''
      },
      endStakeForm: {
        k: '',
        m: ''
      },
      defaultProps: {
        children: "children",
        label: "label"
      },
      // 用户导入参数
      upload: {
        // 是否显示弹出层（用户导入）
        open: false,
        // 弹出层标题（用户导入）
        title: "",
        // 是否禁用上传
        isUploading: false,
        // 是否更新已经存在的用户数据
        updateSupport: 0,
        // 设置上传的请求头部
        headers: { Authorization: "Bearer " + getToken() },
        // 上传的地址
        url: process.env.VUE_APP_BASE_API + "/system/user/importData"
      },
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 50,
        serialNumber: null,
        province: "云南省",
        city: null,
        managementOffice: null,
        subOffice: null,
        maintenanceSection: null,
        roadType: null,
        roadNumber: null,
        startStake: null,
        endStake: null,
        hazardType: null,
        hazardLevel: null,
        hazardLevelList: [1, 2, 3],
        hazardDescription: null,
        photo: null,
        rectificationCompleted: null,
        rectificationMeasures: null,
        completionDeadline: null,
        provincialResponsible: null,
        reviewResponsible: null,
        inspectionResponsible: null,
        remarks: null,
        checkerId: null,
        checkerName: null,
        participant: null,
        auditId: null,
        auditName: null,
        auditStatus: null,
        auditContent: null,
        beginCheckTime: null,
        endCheckTime: null,
        auditTime: null,
        subOfficeId: null,
        managementOfficeId: null,
        maintenanceSectionId: null
      },
      // 列信息
      columns: [
        { key: 1, label: `省份`, visible: true },
        { key: 2, label: `市/州`, visible: true },
        { key: 3, label: `管理处`, visible: true },
        { key: 4, label: `分处`, visible: true },
        { key: 5, label: `养护路段`, visible: true },
        { key: 6, label: `公路类型`, visible: true },
        { key: 7, label: `公路编号`, visible: true },
        { key: 8, label: `起点桩号`, visible: true },
        { key: 9, label: `止点桩号`, visible: true },
        // { key: 10, label: `灾害风险隐患类型`, visible: true },
        { key: 11, label: `隐患等级`, visible: true },
        { key: 12, label: `隐患描述`, visible: true },
        { key: 13, label: `照片`, visible: true },
        { key: 14, label: `是否完成整治`, visible: true },
        { key: 15, label: `已(拟)整治措施`, visible: true },
        { key: 16, label: `整治完成时限`, visible: true },
        { key: 17, label: `省级责任单位及人员`, visible: true },
        { key: 18, label: `复核责任单位及人员`, visible: true },
        { key: 19, label: `排查责任单位及人员`, visible: true },
        { key: 20, label: `备注`, visible: true },
        { key: 21, label: `巡查人`, visible: true },
        { key: 22, label: `审核人`, visible: true },
        { key: 23, label: `审核状态`, visible: true },
        { key: 24, label: `审核意见`, visible: true },
        { key: 25, label: `巡查时间(填报时间)`, visible: true },
        { key: 26, label: `审核时间`, visible: true }
      ],
      // 表单校验
      rules: {
        province: [
          { required: true, message: "省份不能为空", trigger: "blur" }
        ],
        city: [
          { required: true, message: "市/州不能为空", trigger: "blur" }
        ],
        managementMaintenanceBranchId: [
          { required: true, message: "管理处不能为空", trigger: "blur" }
        ],
        maintenanceSectionId: [
          { required: true, message: "养护路段不能为空", trigger: "blur" }
        ],
        startStake: [
          { required: true, validator: validateStartStake, trigger: 'blur' }
        ],
        endStake: [
          { required: true, validator: validateEndStake, trigger: 'blur' }
        ],
        hazardType: [
          { required: true, message: "灾害风险隐患类型不能为空", trigger: "change" }
        ],
        hazardLevel: [
          { required: true, message: "隐患等级(重大隐患、较大隐患、一般隐患)不能为空", trigger: "blur" }
        ]
      },
      // 字典数据
      hazardLevelOptions: [
        { dictLabel: '一般隐患', dictValue: 3 },
        { dictLabel: '较大隐患', dictValue: 2 },
        { dictLabel: '重大隐患', dictValue: 1 },
        { dictLabel: '无', dictValue: 0 },
      ],
      riskLevelOptions: [
        { dictLabel: '无', dictValue: 5 },
        { dictLabel: '低风险', dictValue: 4 },
        { dictLabel: '中风险', dictValue: 3 },
        { dictLabel: '较高风险', dictValue: 2 },
        { dictLabel: '高风险', dictValue: 1 },
      ],
      measuresOptions: [
        { dictLabel: '是', dictValue: 1 },
        { dictLabel: '否', dictValue: 0 },
      ],
      // 审核状态字典
      auditStatusOptions: [
        { dictLabel: '未审核', dictValue: 0 },
        { dictLabel: '审核通过', dictValue: 1 },
        { dictLabel: '审核不通过', dictValue: 2 },
      ],
      // 公路类型字典
      roadTypeOptions: [
        { dictLabel: '高速公路', dictValue: 1 },
        { dictLabel: '普通公路', dictValue: 2 },
      ],
      // 批量审核对话框
      auditDialog: {
        open: false,
        title: '批量审核'
      },
      coordinatePickerVisible: false, // 坐标拾取对话框可见性
      coordinatePickerInitPosition: "102.8207599,24.8885797", // 初始坐标
      currentCoordinateType: '', // 当前坐标类型，longitude表示经度，latitude表示纬度
      cityList: [
        '昆明市', '曲靖市', '玉溪市', '保山市', '昭通市', '丽江市', '普洱市', '临沧市',
        '楚雄彝族自治州', '红河哈尼族彝族自治州', '文山壮族苗族自治州',
        '西双版纳傣族自治州', '大理白族自治州', '德宏傣族景颇族自治州',
        '怒江傈僳族自治州', '迪庆藏族自治州'
      ],
    };
  },
  watch: {
    'form.startStake': {
      immediate: true,
      handler(newVal) {
        if (newVal) {
          const formatted = formatStake(newVal);
          const match = formatted.match(/K(\d+)\+(\d{1,3})/);
          if (match) {
            this.form.beginMile1 = match[1];
            this.form.beginMile2 = match[2];
          }
        }
      }
    },
    'form.endStake': {
      immediate: true,
      handler(newVal) {
        if (newVal) {
          const formatted = formatStake(newVal);
          const match = formatted.match(/K(\d+)\+(\d{1,3})/);
          if (match) {
            this.form.endMile1 = match[1];
            this.form.endMile2 = match[2];
          }
        }
      }
    }
  },
  created() {
    this.getList();
    this.getDeptList(); // 获取级联选择数据
    // this.getDeptTree();
    // this.getConfigKey("sys.user.initPassword").then(response => {
    //   this.initPassword = response.msg;
    // });
  },
  computed: {
    cmpColumns() {
      return (val) => {
        let el = this.columns.find(item => item.label === val)
        if (el) {
          return el.visible
        } else {
          return true
        }
      }
    }
  },
  methods: {
    // 处理第一层节点点击（管理处）
    handleFirstLevelNodeClick(id, label, item) {
      this.treeValue.id = id;
      this.treeValue.label = label;
      this.queryParams.managementMaintenanceBranchId = id;
      this.queryParams.managementMaintenanceBranchIds = [];
      this.queryParams.managementMaintenanceBranchIds.push(id);
      if (item.children && item.children.length > 0) {
        item.children.forEach((child) => {
          this.queryParams.managementMaintenanceBranchIds.push(child.id);
        });
      }
    },

    // 处理第二层节点点击（分处）
    handleSecondLevelNodeClick(id, label) {
      this.treeValue.id = id;
      this.treeValue.label = label;
      this.queryParams.managementMaintenanceBranchId = id;
      this.queryParams.managementMaintenanceBranchIds = [];
      this.queryParams.managementMaintenanceBranchIds.push(id);
    },

    // 表单使用 - 处理第一层节点点击（管理处）
    handleFormFirstLevelNodeClick(id, label, item) {
      this.form.managementOfficeId = id;
      this.form.managementOffice = label;
      this.form.subOfficeId = null;
      this.form.subOffice = null;

      this.treeValue.id = id;
      this.treeValue.label = label;

      this.form.managementMaintenanceBranchIds = [];
      this.form.managementMaintenanceBranchIds.push(id);
      if (item.children && item.children.length > 0) {
        item.children.forEach((child) => {
          this.form.managementMaintenanceBranchIds.push(child.id);
        });
      }
    },

    // 表单使用 - 处理第二层节点点击（分处）
    handleFormSecondLevelNodeClick(id, label, item) {
      // 查找父节点（管理处）
      let parentNode = null;
      for (let i = 0; i < this.deptOptions.length; i++) {
        const dept = this.deptOptions[i];
        if (dept.children && dept.children.length > 0) {
          for (let j = 0; j < dept.children.length; j++) {
            if (dept.children[j].id === id) {
              parentNode = dept;
              break;
            }
          }
        }
        if (parentNode) break;
      }

      if (parentNode) {
        this.form.managementOfficeId = parentNode.id;
        this.form.managementOffice = parentNode.label;
        this.form.subOfficeId = id;
        this.form.subOffice = label;
      } else {
        // 如果找不到父节点，直接设置分处信息
        this.form.subOfficeId = id;
        this.form.subOffice = label;
      }

      this.treeValue.id = id;
      this.treeValue.label = label;

      this.form.managementMaintenanceBranchIds = [];
      this.form.managementMaintenanceBranchIds.push(id);
    },
    // 获取级联部门树数据
    getDeptList() {
      getTreeStruct({ types: '101', deptTypeList: [3, 4], dataRule: true }).then((response) => {
        // 处理树数据，添加level和parent信息
        const processedData = this.processTreeData(response.data);
        this.deptOptions = processedData;
      });
    },

    // 处理树数据，添加level和parent信息
    processTreeData(data, parentNode = null, level = 0) {
      if (!data || !Array.isArray(data)) {
        return [];
      }

      return data.map(item => {
        // 创建新对象，避免修改原始数据
        const newItem = { ...item };

        // 添加level信息
        newItem.level = level;

        // 添加parent信息（如果有父节点）
        if (parentNode) {
          newItem.parent = parentNode;
        }

        // 递归处理子节点
        if (newItem.children && newItem.children.length > 0) {
          newItem.children = this.processTreeData(newItem.children, newItem, level + 1);
        }

        return newItem;
      });
    },

    // 格式化树形数据
    formatData(data) {
      let options = [];
      if (!data || !Array.isArray(data)) {
        return options;
      }
      data.forEach((item, key) => {
        options.push({ label: item.label, value: item.id });
        if (item.children) {
          item.children.forEach((items, keys) => {
            options.push({ label: items.label, value: items.id });
            if (items.children) {
              items.children.forEach((itemss, keyss) => {
                options.push({ label: itemss.label, value: itemss.id });
                if (itemss.children) {
                  itemss.children.forEach((itemsss, keysss) => {
                    options.push({ label: itemsss.label, value: itemsss.id });
                  });
                }
              });
            }
          });
        }
      });
      return options;
    },

    // 查询表单 - 监听树节点选中
    handleNodeCheck(node, nodes) {
      this.queryParams.managementMaintenanceBranchId = node.id;
      this.deptChange(this.queryParams.managementMaintenanceBranchId);
    },

    // 查询表单 - 监听树节点点击
    nodeClick(item) {
      console.log("nodeClick被调用", item);
      if (!item) {
        console.error("nodeClick: item 参数为空");
        return;
      }

      const { id, label, level, parent } = item;
      console.log(`nodeClick: id=${id}, label=${label}, level=${level}`);

      // 使用level属性判断节点层级
      if (level === 0) {
        // 第一层节点（管理处）
        console.log("nodeClick: 第一层节点（管理处）");

        // 设置管理处相关信息 - queryParams
        this.queryParams.managementOfficeId = id;
        this.queryParams.managementOffice = label;
        this.queryParams.subOfficeId = null;
        this.queryParams.subOffice = null;
        this.queryParams.managementMaintenanceBranchId = id;

        // 同时设置 form 对象的相关字段，以便新增操作使用
        this.form.managementOfficeId = id;
        this.form.managementOffice = label;
        this.form.subOfficeId = null;
        this.form.subOffice = null;
        this.form.managementMaintenanceBranchId = id;

        // 收集所有子节点ID
        this.queryParams.managementMaintenanceBranchIds = [id];
        this.form.managementMaintenanceBranchIds = [id];
        if (item.children && item.children.length > 0) {
          item.children.forEach((child) => {
            this.queryParams.managementMaintenanceBranchIds.push(child.id);
            this.form.managementMaintenanceBranchIds.push(child.id);
          });
        }

        // 设置树值
        this.treeValue.id = id;
        this.treeValue.label = label;

        // 触发部门变更
        this.deptChange(this.queryParams.managementMaintenanceBranchId);
      } else if (level === 1) {
        // 第二层节点（分处）

        // 使用parent属性获取父节点信息
        if (parent) {
          // 设置 queryParams 的管理处信息
          this.queryParams.managementOfficeId = parent.id;
          this.queryParams.managementOffice = parent.label;

          // 同时设置 form 对象的管理处信息
          this.form.managementOfficeId = parent.id;
          this.form.managementOffice = parent.label;
        } else {
          // 如果没有parent属性，则回退到原来的查找方式
          let parentNode = null;
          for (let i = 0; i < this.deptOptions.length; i++) {
            const dept = this.deptOptions[i];
            if (dept.children && dept.children.length > 0) {
              for (let j = 0; j < dept.children.length; j++) {
                if (dept.children[j].id === id) {
                  parentNode = dept;
                  break;
                }
              }
            }
            if (parentNode) break;
          }

          if (parentNode) {
            // 设置 queryParams 的管理处信息
            this.queryParams.managementOfficeId = parentNode.id;
            this.queryParams.managementOffice = parentNode.label;

            // 同时设置 form 对象的管理处信息
            this.form.managementOfficeId = parentNode.id;
            this.form.managementOffice = parentNode.label;
          }
        }

        // 设置 queryParams 的分处信息
        this.queryParams.subOfficeId = id;
        this.queryParams.subOffice = label;
        this.queryParams.managementMaintenanceBranchId = id;
        this.queryParams.managementMaintenanceBranchIds = [id];

        // 同时设置 form 对象的分处信息
        this.form.subOfficeId = id;
        this.form.subOffice = label;
        this.form.managementMaintenanceBranchId = id;
        this.form.managementMaintenanceBranchIds = [id];

        console.log("nodeClick: 收集所有子节点ID", this.form);

        // 设置树值
        this.treeValue.id = id;
        this.treeValue.label = label;

        // 触发部门变更
        this.deptChange(this.queryParams.managementMaintenanceBranchId);
      } else {
        // 如果没有level属性或者level不是0或1，则回退到原来的判断方式
        if (item.children && item.children.length > 0) {
          // 有子节点，则认为是第一层（管理处）
          console.log("nodeClick: 第一层节点（管理处）（基于子节点判断）");
          this.handleFirstLevelNodeClick(id, label, item);

          // 同时设置 form 对象的管理处信息
          this.form.managementOfficeId = id;
          this.form.managementOffice = label;
          this.form.subOfficeId = null;
          this.form.subOffice = null;
          this.form.managementMaintenanceBranchId = id;
          this.form.managementMaintenanceBranchIds = [id];
          if (item.children && item.children.length > 0) {
            item.children.forEach((child) => {
              this.form.managementMaintenanceBranchIds.push(child.id);
            });
          }
        } else {
          // 没有子节点，则认为是第二层（分处）
          this.handleSecondLevelNodeClick(id, label);

          // 查找父节点（管理处）并设置 form 对象的相关信息
          let parentNode = null;
          for (let i = 0; i < this.deptOptions.length; i++) {
            const dept = this.deptOptions[i];
            if (dept.children && dept.children.length > 0) {
              for (let j = 0; j < dept.children.length; j++) {
                if (dept.children[j].id === id) {
                  parentNode = dept;
                  break;
                }
              }
            }
            if (parentNode) break;
          }

          if (parentNode) {
            this.form.managementOfficeId = parentNode.id;
            this.form.managementOffice = parentNode.label;
          }

          this.form.subOfficeId = id;
          this.form.subOffice = label;
          this.form.managementMaintenanceBranchId = id;
          this.form.managementMaintenanceBranchIds = [id];
        }
      }
      console.log("form", this.form);
    },

    // 查询表单 - 监听选择变化
    deptChange(e) {
      if (e && e.length == 0) {
        this.maintenanceSectionChange(null);
      }

      this.routeOptions = [];
      this.queryParams.maintenanceSectionId = '';
      this.queryParams.routeCodes = [];
      this.routeList = [];
      getMaintenanceSectionListAll({ departmentIdList: e }).then((res) => {
        if (res.code == 200) {
          this.routeOptions = res.data || [];
        }
      });
      this.cascadeObj = {
        managementMaintenanceBranchId: e,
      };
    },

    // 查询表单 - 路线编码变化
    routeCodesChange(e) {
      this.cascadeObj = {
        ...this.cascadeObj,
        routeCodes: e,
        routeCode: e && e.length === 1 ? e[0] : null,
      };
    },

    // 表单 - 路线编码变化
    formRouteCodesChange(e) {
      this.handleCascadeUpdate({
        ...this.cascadeObj,
        routeCodes: e,
        routeCode: e && e.length === 1 ? e[0] : null,
      });
    },

    // 查询表单 - 监听路段变化
    maintenanceSectionChange(e) {
      if (!e) {
        this.routeList = [];
        this.queryParams.routeCodes = [];
        this.queryParams.maintenanceSectionId = '';

        // 更新对象
        this.cascadeObj = {
          ...this.cascadeObj,
          maintenanceSectionId: '',
          routeCodes: [],
        };

        // 监听路段为空则加载所有线路数据
        listAllRoute().then((res) => {
          if (res.code == 200) {
            this.routeList = res.data || [];
          }
        });
        return;
      }

      listByMaintenanceSectionId({ maintenanceSectionId: e }).then((res) => {
        if (res.code == 200) {
          res.data.forEach((item) => {
            item.routeName = item.routeName + '(' + item.routeCode + ')';
          });
          this.routeList = res.data || [];
        }
      });

      this.cascadeObj = {
        ...this.cascadeObj,
        maintenanceSectionId: e,
      };
    },
    async maintenanceSectionIdChange(val) {
      if (this.form.maintenanceSectionId) {
        await this.managementChange(this.form.maintenanceSectionId)
      } else {
        this.listAllRoute()
      }
      this.form.maintenanceSection = val?.maintenanceSectionName
    },
    listAllRoute() {
      listAllRoute().then((res) => {
        if (res.code == 200) {
          this.routeList = res.data || [];
        }
      })
    },
    // 查询表单 - 删除标签
    removeTag(tag) {
      if (this.$refs.treeRef) {
        let checkedNodes = this.$refs.treeRef.getCheckedNodes();
        // 删除节点
        for (let i = 0; i < checkedNodes.length; i++) {
          if (checkedNodes[i].id == tag) {
            checkedNodes.splice(i, 1);
            break;
          }
        }
        // 设置树选中的节点
        this.$refs.treeRef.setCheckedNodes(checkedNodes);
        // 更新表单数据并触发搜索
        this.queryParams.managementMaintenanceBranchId = checkedNodes.map((node) => node.id);

        this.deptChange(this.queryParams.managementMaintenanceBranchId);
      }
    },

    // 查询表单 - 清空选择
    selectClear() {
      if (this.$refs.treeRef) {
        // 清空树选中数据
        this.$refs.treeRef.setCheckedKeys([]);

        // 清空表单数据
        this.queryParams.managementMaintenanceBranchId = '';
        this.queryParams.maintenanceSectionId = '';
        this.queryParams.routeCodes = [];
        this.queryParams.managementMaintenanceBranchIds = [];

        // 更新对象数据
        this.cascadeObj = {
          managementMaintenanceBranchId: [],
          maintenanceSectionId: '',
          routeCodes: [],
        };
      }
    },

    // 表单 - 监听树节点选中
    formHandleNodeCheck(node, nodes) {
      this.form.managementMaintenanceBranchId = node.id;
      this.formDeptChange(this.form.managementMaintenanceBranchId);
    },

    // 表单 - 监听树节点点击
    // 此方法参照nodeClick方法结构重写，确保与查询表单的处理逻辑一致
    formNodeClick(item) {
      if (!item) {
        console.error("formNodeClick: item 参数为空");
        return;
      }

      const { id, label, level, parent } = item;

      // 使用level属性判断节点层级
      if (level === 0) {
        // 第一层节点（管理处）

        // 设置管理处相关信息
        this.form.managementOfficeId = id;
        this.form.managementOffice = label;
        this.form.subOfficeId = null;
        this.form.subOffice = null;

        this.form.managementMaintenanceBranchId = id;

        // 收集所有子节点ID
        this.form.managementMaintenanceBranchIds = [id];
        if (item.children && item.children.length > 0) {
          item.children.forEach((child) => {
            this.form.managementMaintenanceBranchIds.push(child.id);
          });
        }

        // 设置树值
        this.treeValue.id = id;
        this.treeValue.label = label;

        // 触发部门变更
        this.formDeptChange(this.form.managementMaintenanceBranchId);
      } else if (level === 1) {
        // 第二层节点（分处）
        // 使用parent属性获取父节点信息
        if (parent) {
          this.form.managementOfficeId = parent.id;
          this.form.managementOffice = parent.label;
        } else {
          // 如果没有parent属性，则回退到原来的查找方式
          let parentNode = null;
          for (let i = 0; i < this.deptOptions.length; i++) {
            const dept = this.deptOptions[i];
            if (dept.children && dept.children.length > 0) {
              for (let j = 0; j < dept.children.length; j++) {
                if (dept.children[j].id === id) {
                  parentNode = dept;
                  break;
                }
              }
            }
            if (parentNode) break;
          }

          if (parentNode) {
            this.form.managementOfficeId = parentNode.id;
            this.form.managementOffice = parentNode.label;
          }
        }

        this.form.subOfficeId = id;
        this.form.subOffice = label;
        this.form.managementMaintenanceBranchId = id;
        this.form.managementMaintenanceBranchIds = [id];

        // 设置树值
        this.treeValue.id = id;
        this.treeValue.label = label;

        console.log("form1", this.form);

        // 触发部门变更
        this.formDeptChange(this.form.managementMaintenanceBranchId);
      } else {
        // 如果没有level属性或者level不是0或1，则回退到原来的判断方式
        if (item.children && item.children.length > 0) {
          // 有子节点，则认为是第一层（管理处）

          // 设置管理处相关信息
          this.form.managementOfficeId = id;
          this.form.managementOffice = label;
          this.form.subOfficeId = null;
          this.form.subOffice = null;

          this.form.managementMaintenanceBranchId = id;

          // 收集所有子节点ID
          this.form.managementMaintenanceBranchIds = [id];
          if (item.children && item.children.length > 0) {
            item.children.forEach((child) => {
              this.form.managementMaintenanceBranchIds.push(child.id);
            });
          }

          // 设置树值
          this.treeValue.id = id;
          this.treeValue.label = label;

          // 触发部门变更
          this.formDeptChange(this.form.managementMaintenanceBranchId);
        } else {
          // 没有子节点，则认为是第二层（分处）

          // 查找父节点（管理处）
          let parentNode = null;
          // 遍历管理处列表查找父节点
          for (let i = 0; i < this.deptOptions.length; i++) {
            const dept = this.deptOptions[i];
            if (dept.children && dept.children.length > 0) {
              for (let j = 0; j < dept.children.length; j++) {
                if (dept.children[j].id === id) {
                  parentNode = dept;
                  break;
                }
              }
            }
            if (parentNode) break;
          }

          if (parentNode) {
            this.form.managementOfficeId = parentNode.id;
            this.form.managementOffice = parentNode.label;
          }

          this.form.subOfficeId = id;
          this.form.subOffice = label;
          this.form.managementMaintenanceBranchId = id;
          this.form.managementMaintenanceBranchIds = [id];

          // 设置树值
          this.treeValue.id = id;
          this.treeValue.label = label;

          // 触发部门变更
          this.formDeptChange(this.form.managementMaintenanceBranchId);
        }
      }
      console.log("form2", this.form);
      // 不再需要使用treeNode对象，因为我们现在直接使用item的level和parent属性
    },

    // 表单 - 监听选择变化
    formDeptChange(e) {
      if (e && e.length == 0) {
        this.formMaintenanceSectionChange(null);
      }

      this.routeOptions = [];
      this.form.maintenanceSectionId = '';
      this.form.routeCodes = [];
      this.routeList = [];
      getMaintenanceSectionListAll({ departmentIdList: e }).then((res) => {
        if (res.code == 200) {
          this.routeOptions = res.data || [];
        }
      });

      // 处理级联数据更新，类似原来的handleCascadeUpdate
      this.handleCascadeUpdate({
        managementMaintenanceBranchId: e,
      });
    },

    // 表单 - 监听路段变化
    formMaintenanceSectionChange(e) {
      if (!e) {
        this.routeList = [];
        this.form.routeCodes = [];
        this.form.maintenanceSectionId = '';

        // 更新对象
        const updateData = {
          ...this.cascadeObj,
          maintenanceSectionId: '',
          routeCodes: [],
        };

        // 处理级联数据更新
        this.handleCascadeUpdate(updateData);

        // 监听路段为空则加载所有线路数据
        listAllRoute().then((res) => {
          if (res.code == 200) {
            this.routeList = res.data || [];
          }
        });
        return;
      }

      // 查找选中的路段数据
      const selectedSection = this.routeOptions.find(item => item.maintenanceSectionId === e);
      if (selectedSection) {
        // 设置路段id和名称
        this.form.maintenanceSectionId = selectedSection.maintenanceSectionId;
        this.form.maintenanceSection = selectedSection.maintenanceSectionName;

        // 如果路段数据中包含routeType和routeCode，则设置对应的roadType和roadNumber
        if (selectedSection.routeType !== undefined) {
          this.form.roadType = selectedSection.routeType;
        }
        if (selectedSection.routeCode !== undefined) {
          this.form.roadNumber = selectedSection.routeCode;
        }
      }

      listByMaintenanceSectionId({ maintenanceSectionId: e }).then((res) => {
        if (res.code == 200) {
          res.data.forEach((item) => {
            item.routeName = item.routeName + '(' + item.routeCode + ')';
          });
          // 如果有数据，选择第一个并赋值
          if (res.data && res.data.length > 0) {
            const firstRoute = res.data[0];
            this.form.roadType = firstRoute.routeType;
            this.form.roadNumber = firstRoute.routeCode;
          }
          this.routeList = res.data || [];
        }
      });

      const updateData = {
        ...this.cascadeObj,
        maintenanceSectionId: e,
      };

      // 处理级联数据更新
      this.handleCascadeUpdate(updateData);
    },

    // 表单 - 删除标签
    formRemoveTag(tag) {
      if (this.$refs.formTreeRef) {
        let checkedNodes = this.$refs.formTreeRef.getCheckedNodes();
        // 删除节点
        for (let i = 0; i < checkedNodes.length; i++) {
          if (checkedNodes[i].id == tag) {
            checkedNodes.splice(i, 1);
            break;
          }
        }
        // 设置树选中的节点
        this.$refs.formTreeRef.setCheckedNodes(checkedNodes);
        // 更新表单数据
        this.form.managementMaintenanceBranchId = checkedNodes.map((node) => node.id);

        this.formDeptChange(this.form.managementMaintenanceBranchId);
      }
    },

    /** 处理树的原生点击事件 */
    handleTreeClick(event) {
      console.log('树组件原生点击事件触发', event);
      // 阻止事件冒泡
      event.stopPropagation();

      // 尝试找到点击的节点元素
      const clickedEl = event.target.closest('.el-tree-node');
      if (clickedEl) {
        const nodeId = clickedEl.getAttribute('data-key');
        console.log('点击了树节点，ID:', nodeId);

        if (nodeId && this.$refs.formTreeRef) {
          // 找到节点数据
          const node = this.$refs.formTreeRef.getNode(nodeId);
          if (node && node.data) {
            console.log('找到节点数据:', node.data);
            // 手动调用formNodeClick方法
            this.formNodeClick(node.data);
          }
        }
      }
    },

    // 表单 - 清空选择
    formSelectClear() {
      if (this.$refs.formTreeRef) {
        // 清空树选中数据
        this.$refs.formTreeRef.setCheckedKeys([]);

        // 清空表单数据
        this.form.managementMaintenanceBranchId = '';
        this.form.maintenanceSectionId = '';
        this.form.routeCodes = [];
        this.form.managementMaintenanceBranchIds = [];

        // 处理级联数据更新
        this.handleCascadeUpdate({
          managementMaintenanceBranchId: [],
          maintenanceSectionId: '',
          routeCodes: [],
        });
      }
    },
    /** 查询用户列表 */
    getList() {
      this.loading = true;
      if (this.dateRange && this.dateRange.length > 0) {
        this.queryParams.beginCheckTime = this.dateRange[0];
        this.queryParams.endCheckTime = this.dateRange[1];
      } else {
        this.queryParams.beginCheckTime = null;
        this.queryParams.endCheckTime = null;
      }
      listDrainageFacilityHazards(this.queryParams).then(response => {
        this.drainageFacilityHazardsList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },

    /** 处理级联选择数据更新 */
    handleCascadeUpdate(data) {
      // 更新表单数据，但只更新data中存在的字段，保留其他字段的现有值
      const updatedForm = { ...this.form };

      // 只有当data中有对应字段时才更新
      if (data.managementMaintenanceName !== undefined) {
        updatedForm.managementOffice = data.managementMaintenanceName;
      }
      if (data.managementMaintenanceId !== undefined) {
        updatedForm.managementOfficeId = data.managementMaintenanceId;
      }

      // 分处相关
      if (data.managementMaintenanceBranchName !== undefined) {
        updatedForm.subOffice = data.managementMaintenanceBranchName;
      }
      if (data.managementMaintenanceBranchId !== undefined) {
        updatedForm.subOfficeId = data.managementMaintenanceBranchId;
      }

      // 养护路段相关
      if (data.maintenanceSectionName !== undefined) {
        updatedForm.maintenanceSection = data.maintenanceSectionName;
      }
      if (data.maintenanceSectionId !== undefined) {
        updatedForm.maintenanceSectionId = data.maintenanceSectionId;
      }

      // 路线相关
      if (data.routeCode !== undefined) {
        updatedForm.routeCode = data.routeCode;
      }
      if (data.routeCodes !== undefined) {
        updatedForm.routeCodes = data.routeCodes;
      }
      if (data.roadType !== undefined) {
        updatedForm.roadType = data.roadType;
      }

      // 更新表单
      this.form = updatedForm;
      console.log("handleCascadeUpdate后的表单数据:", this.form);
    },

    // 表单重置
    reset() {
      this.form = {
        id: null,
        province: "云南省",
        city: "",
        managementOffice: null,
        managementOfficeId: null,
        subOffice: null,
        subOfficeId: null,
        maintenanceSection: null,
        maintenanceSectionId: null,
        roadType: null,
        roadNumber: null,
        stake: null,
        facilityType: null,
        facilityName: null,
        hazardLocation: null,
        hazardLevel: null,
        hazardDescription: null,
        photo: null,
        rectificationCompleted: null,
        rectificationMeasures: null,
        completionDeadline: null,
        provincialResponsible: null,
        reviewResponsible: null,
        inspectionResponsible: null,
        isNaturalDisaster: null,
        riskDatabaseId: null,
        remarks: null,
        checkerId: this.$store.state.user.id,
        checkerName: this.$store.state.user.nickName,
        auditId: null,
        auditName: null,
        auditStatus: 0,
        auditContent: null,
        checkTime: this.parseTime(new Date(), '{y}-{m}-{d}'),
        auditTime: null,
      };
      this.resetForm("form");
      this.$refs.upload?.clearFiles();
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dateRange = [];
      this.resetForm("queryForm");
      this.queryParams.hazardLevelList = []
      this.queryParams.managementMaintenanceBranchId = null
      this.queryParams.managementMaintenanceBranchIds = null
      this.queryParams.managementMaintenanceIds = null
      this.queryParams.routeCodes = null
      this.queryParams.maintenanceSectionId = null
      this.queryParams.hazardLevelList = []
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id);
      this.single = selection.length != 1;
      this.multiple = !selection.length;
    },
    // 表格点击勾选
    handleRowClick(row) {
      row.isSelected = !row.isSelected;
      this.$refs.table.toggleRowSelection(row);
    },
    // 勾选高亮
    rowStyle({ row, rowIndex }) {
      if (this.ids.includes(row.id)) {
        return { 'background-color': '#E1F0FF', color: '#333' }
      } else {
        return { 'background-color': '#fff', color: '#333' }
      }
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加涉灾隐患点—防洪排水设施";

      // 确保树组件已初始化
      this.$nextTick(() => {
        console.log('表单对话框已打开，检查树组件', this.$refs.formTreeRef);
        if (this.$refs.formTreeRef) {
          // 为树组件添加点击事件监听器
          const treeEl = this.$refs.formTreeRef.$el;
          console.log('找到树组件元素', treeEl);
        }
      });
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids;
      getDrainageFacilityHazards(id).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改涉灾隐患点—防洪排水设施";
      });
    },
    /** 提交按钮 */
    submitForm: function () {
      // 检查photo是否是数组，如果是则转换为逗号分隔的字符串
      if (this.form.photo && Array.isArray(this.form.photo)) {
        this.form.photo = this.form.photo.join(',');
      }
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.id != null) {
            updateDrainageFacilityHazards(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addDrainageFacilityHazards(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const id = row.id || this.ids;
      if (row.id) { // 单个删除
        this.$modal.confirm('是否确认删除改数据？').then(function () {
          let data = { idList: [row.id] };
          return delData(data);
        }).then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        }).catch(() => { });
      } else { // 批量删除
        this.$modal.confirm(`是否确认删除选中的 ${id.length} 条数据项？`).then(function () {
          let data = { idList: id };
          console.log(data)
          return delData(data);
        }).then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        }).catch(() => { });
      }
    },

    /** 导出按钮操作 */
    handleExport() {
      if (this.dateRange && this.dateRange.length > 0) {
        this.queryParams.beginCheckTime = this.dateRange[0];
        this.queryParams.endCheckTime = this.dateRange[1];
      } else {
        this.queryParams.beginCheckTime = null;
        this.queryParams.endCheckTime = null;
      }

      // 先获取数据总数
      let queryParams = {};
      if (this.ids.length === 0) {
        queryParams = {
          ...this.queryParams,
          pageNum: 1,
          pageSize: 1,
        };
      } else {
        queryParams = {
          ids: this.ids,
          pageNum: 1,
          pageSize: 1,
        };
      }

      listDrainageFacilityHazards(queryParams).then((response) => {
        const export_count = response.total;

        const confirmMessage = this.ids.length === 0
          ? `根据搜索条件，本次导出共有 ${export_count} 条数据，是否确认导出？`
          : `根据选中条件，本次导出共有 ${export_count} 条数据，是否确认导出？`;

        this.$confirm(confirmMessage, '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        })
          .then(() => {
            const now = new Date();
            const timeStr = `${now.getFullYear()}年${now.getMonth() + 1}月${now.getDate()}日${now.getHours()}时${now.getMinutes()}分${now.getSeconds()}秒`;
            const fileName = `防洪排水设施隐患_${timeStr}.xlsx`;

            // 导出时使用的参数
            let exportParams = {};
            if (this.ids.length === 0) {
              exportParams = { ...this.queryParams };
            } else {
              exportParams = { ids: this.ids };
            }

            this.download('middleData/drainageFacilityHazards/export', exportParams, fileName, {
              parameterType: 'body', // 设置参数类型为 body
              headers: {
                'Content-Type': 'application/json', // 设置请求头为 JSON
              }
            });
          })
          .catch(() => {
            this.$message({
              type: 'info',
              message: '已取消导出',
            });
          });
      });
    },

    /** 批量审核按钮操作 */
    handleBatchAudit() {
      if (this.ids.length === 0) {
        this.$message.warning('请至少选择一条记录进行审核');
        return;
      }
      this.auditDialog.open = true;
    },

    /** 提交审核 */
    submitAudit(formData) {
      batchAudit(formData).then(response => {
        this.$modal.msgSuccess('审核操作成功');
        this.getList();
      });
    },

    /** 导入按钮操作 */
    handleImport() {
      this.upload.title = "用户导入";
      this.upload.open = true;
    },
    /** 下载模板操作 */
    importTemplate() {
      this.download('system/user/importTemplate', {
      }, `user_template.xlsx`)
    },
    // 文件上传中处理
    handleFileUploadProgress(event, file, fileList) {
      this.upload.isUploading = true;
    },
    // 文件上传成功处理
    handleFileSuccess(response, file, fileList) {
      this.upload.open = false;
      this.upload.isUploading = false;
      this.$refs.upload.clearFiles();
      this.$alert("<div style='overflow: auto;overflow-x: hidden;max-height: 70vh;padding: 10px 20px 0;'>" + response.msg + "</div>", "导入结果", { dangerouslyUseHTMLString: true });
      this.getList();
    },
    // 提交上传文件
    submitFileForm() {
      this.$refs.upload.submit();
    },

    // 格式化隐患等级显示
    formatHazardLevel(hazardLevel) {
      const dict = this.hazardLevelOptions.find((item) => item.dictValue === hazardLevel);
      return dict ? dict.dictLabel : hazardLevel;
    },

    // 格式化风险等级显示
    formatRiskLevel(riskLevel) {
      const dict = this.riskLevelOptions.find((item) => item.dictValue === riskLevel);
      return dict ? dict.dictLabel : riskLevel;
    },

    // 格式化是否采取措施显示
    formatMeasures(status) {
      const dict = this.measuresOptions.find((item) => item.dictValue === status);
      return dict ? dict.dictLabel : status;
    },

    // 格式化审核状态显示
    formatAuditStatus(status) {
      const dict = this.auditStatusOptions.find((item) => item.dictValue === status);
      return dict ? dict.dictLabel : status;
    },

    // 格式化公路类型显示
    formatRoadType(type) {
      const dict = this.roadTypeOptions.find((item) => item.dictValue === type);
      return dict ? dict.dictLabel : type;
    },

    /** 单条记录审核按钮操作 */
    handleAudit(row) {
      this.ids = [row.id];
      this.auditDialog.open = true;
      this.auditDialog.title = '审核记录';
    },

    /** 图片预览 */
    previewImage(url) {
      this.imagePreview.url = url;
      this.imagePreview.visible = true;
    },

    // 格式化桩号显示
    formatStakeNumber(value) {
      return formatStake(value);
    },

    // 处理起点桩号输入
    handleStartStakeInput() {
      // 移除非数字字符
      if (this.form.beginMile1) {
        this.form.beginMile1 = this.form.beginMile1.replace(/[^\d]/g, '');
      }
      if (this.form.beginMile2) {
        this.form.beginMile2 = this.form.beginMile2.replace(/[^\d]/g, '');
      }

      // 限制千位以下为3位数
      if (this.form.beginMile2?.length > 3) {
        this.form.beginMile2 = this.form.beginMile2.slice(0, 3);
      }
    },

    // 处理起点桩号变更
    handleStartStakeChange() {
      const k = parseInt(this.form.beginMile1 || '0');
      const m = parseInt(this.form.beginMile2 || '0');

      // 更新form中的桩号值，存储为数值类型
      this.form.startStake = k * 1000 + m;
    },

    // 处理终点桩号输入
    handleEndStakeInput() {
      // 移除非数字字符
      if (this.form.endMile1) {
        this.form.endMile1 = this.form.endMile1.replace(/[^\d]/g, '');
      }
      if (this.form.endMile2) {
        this.form.endMile2 = this.form.endMile2.replace(/[^\d]/g, '');
      }

      // 限制千位以下为3位数
      if (this.form.endMile2?.length > 3) {
        this.form.endMile2 = this.form.endMile2.slice(0, 3);
      }
    },

    // 处理终点桩号变更
    handleEndStakeChange() {
      const k = parseInt(this.form.endMile1 || '0');
      const m = parseInt(this.form.endMile2 || '0');

      // 更新form中的桩号值，存储为数值类型
      this.form.endStake = k * 1000 + m;
    },
    /** 取消按钮 */
    cancel() {
      this.open = false;
      this.reset();
    },
    /**
     * 坐标相关方法
     */
    // 显示坐标拾取组件
    showCoordinatePicker(type) {
      this.currentCoordinateType = type;

      // 如果已有经纬度，设置初始值
      if (this.form.longitude && this.form.latitude) {
        this.coordinatePickerInitPosition = `${this.form.longitude},${this.form.latitude}`;
      } else {
        // 默认昆明市坐标
        this.coordinatePickerInitPosition = "102.8207599,24.8885797";
      }

      this.coordinatePickerVisible = true;
    },

    // 处理坐标保存
    handleCoordinateSave(position) {
      if (!position) return;

      const coordinates = position.split(',');
      if (coordinates.length === 2) {
        const lng = coordinates[0];
        const lat = coordinates[1];

        // 根据坐标类型设置表单字段
        if (this.currentCoordinateType === 'longitude' || this.currentCoordinateType === 'latitude') {
          this.form.longitude = lng;
          this.form.latitude = lat;
        }
      }
    },
  }
};
</script>
<style lang="scss" scoped>
.hasTagsView .app-main[data-v-078753dd] {
  background: #f5f7fa;
}

/* 下拉选择器样式 */
.custom-select {
  width: 100%;
}

/* 输入框超出隐藏，不换行*/
::v-deep .el-select__tags {
  flex-wrap: nowrap;
  overflow: auto;
}

::v-deep .el-scrollbar {
  .el-select-dropdown__item {
    padding: 0;
  }
}

.tableDiv {
  background-color: white;
  padding-bottom: 10px;
  overflow-x: auto;
}

/* 确保固定列可见 */
.el-table__fixed-right {
  height: 100% !important;
  z-index: 10;
}

/* 图片预览容器样式 */
.image-preview-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 300px;
}

/* 图片预览对话框样式 */
.image-preview-dialog .el-dialog__body {
  padding: 20px;
}

/* 排水设施隐患表单样式 */
.drainage-hazard-dialog {
  .el-dialog__body {
    padding: 20px 30px;
    max-height: calc(90vh - 150px);
    overflow-y: auto;
  }

  .drainage-hazard-form {
    .form-section {
      background: #f8f9fa;
      border-radius: 8px;
      padding: 20px;
      margin-bottom: 24px;

      &:last-child {
        margin-bottom: 0;
      }

      .section-title {
        font-size: 16px;
        font-weight: 500;
        color: #1a1a1a;
        margin-bottom: 20px;
        padding-left: 10px;
        border-left: 4px solid #409EFF;
      }
    }

    .el-form-item {
      margin-bottom: 18px;

      &__label {
        font-weight: 500;
        color: #606266;
      }
    }

    .full-width {
      width: 100%;
    }

    .el-textarea__inner {
      font-family: inherit;
    }
  }

  .dialog-footer {
    padding: 20px 30px;
    text-align: right;
    background: #f8f9fa;
    border-top: 1px solid #e4e7ed;
  }
}

.infoBox {
  padding: 15px 15px 0 15px;
  box-sizing: border-box;
  border-radius: 6px;
  border: 1px solid #C4C4C4;
  position: relative;

  .infoTitle {
    user-select: none;
    position: absolute;
    top: 0;
    left: 0;
    padding: 0 10px;
    font-size: 14px;
    line-height: 14px;
    font-weight: bold;
    transform: translateX(15px) translateY(-50%);
    background-color: white;
  }
}

.dialog-footer {
  padding: 20px 30px;
  text-align: right;
  background: #f8f9fa;
  border-top: 1px solid #e4e7ed;
}

.el-form-item {
  margin-bottom: 18px;
}

.el-form-item__label {
  font-weight: 500;
  color: #606266;
}

.el-textarea__inner {
  font-family: inherit;
}
</style>
