<template>
  <div class="drawing">
    <div class="drawing-right drawing-card">
      <div class="drawing-card-head">
        定期检测项目资料
      </div>
      <cardBox
        v-if="bizId"
        style="height: calc(100vh - 160px); padding: 10px;"
        typeCode="BS139"
        :bizId="bizId"
        :key="bizId"
        :showFloatingTitle="false"
        :boxHeight="'40vh'"
      />

      <el-empty
        v-else
        style="height:calc(100% - 51px); background-color: #fff; border-radius: 0 0 10px 10px;"
      ></el-empty>
    </div>
  </div>
</template>

<script>
import cardBox from '@/components/Drawing/cardBox.vue'

export default {
  name: 'drawing-baseInfo',
  components: { cardBox },
  dicts: [],
  data() {
    return {
      bizId: ''
    }
  },
  props: {
    row: {
      type: Object,
      default: () => {}
    }
  },
  watch: {
    row: {
      handler(val) {
        if (val.id) {
          this.bizId = val.id
        }
      },
      immediate: true,
      deep: true
    }
  },
  created() {
  },
}
</script>

<style lang="scss" scoped>
.drawing {
  width: 100%;
  height: 100%;
  padding: 10px;
  display: flex;
  .drawing-card {
    min-width: 250px;
    height: 100%;
    background-color: white;
    position: relative;
    border-radius: 10px;
    box-shadow: 0px 0px 10px 0px rgba(0, 0, 0, 0.2);
    .drawing-card-head {
      width: 100%;
      height: 51px;
      background: #409eff;
      border-radius: 10px 10px 0 0;
      display: flex;
      justify-content: center;
      align-items: center;
      font-size: 16px;
      font-weight: 600;
      color: #fff;
    }
    .drawing-card-body {
      position: absolute;
      bottom: 0;
      left: 0;
      width: 100%;
      height: calc(100% - 51px);
      padding: 15px 10px;
      display: flex;
      flex-direction: column;
      overflow-y: auto;

    }
  }
  .drawing-right {
    width: 100%;
    height: 100%;
    margin-left: 10px;
    border-radius: 10px;
    background-color: #f0f0f0;
    box-shadow: 0px 0px 10px 0px rgba(0, 0, 0, 0.2);
  }
}
::v-deep .el-tree-node__content {
  &:hover {
    background-color: #b7daff;
  }
}
::v-deep
  .el-tree--highlight-current
  .el-tree-node.is-current
  > .el-tree-node__content {
  background-color: #b7daff;
}
::v-deep .el-radio__label {
  padding-left: 5px !important;
}

::v-deep .card-item {
  height: 300px; /* 固定高度，可以根据需要调整 */
  overflow-y: auto; /* 超出高度时添加垂直滚动条 */
  background: white; /* 确保背景色 */
  padding: 10px; /* 内边距，可根据需要调整 */
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1); /* 添加阴影效果 */
  border-radius: 4px; /* 圆角 */
}

.card-item:hover {
  transform: scale(1.05); /* 鼠标悬停时放大 */
}

::v-deep .card-item  {
  .el-card {
    overflow-y: auto;
  }

  .collapse-container {
    height: auto;
  }
}
</style>
<style lang="scss" scoped>
@import "@/assets/styles/business.scss";
</style>
