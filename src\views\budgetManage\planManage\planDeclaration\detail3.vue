<template>
  <div class="maindiv">
    <el-row>
      <el-col :span="8">
        <el-form :model="formData" ref="queryForm" :inline="true" :rules="rules" label-width="80px">
          <el-form-item label="项目名称" prop="projectName">
            <el-input v-model="formData.projectName" size="medium" placeholder="请输入项目名称" style="width:220px" clearable :disabled="optionModel=='read'">
            </el-input>
          </el-form-item>
          <el-form-item label="合同"  prop="conIds">
              <el-select
                  v-model="formData.conIds" filterable
                  placeholder="请选择合同"
                  clearable
                  size="medium"
                  :disabled="optionModel=='read'"
                  :style="{width: '100%'}">
                <el-option
                    v-for="item in contractList"
                    :key="item.id"
                    :label="item.name"
                    :value="item.id"
                >
                </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="立项理由" prop="projectReason">
            <el-input type="textarea" v-model="formData.projectReason" style="width:220px" placeholder="请输入立项理由" clearable :disabled="optionModel=='read'">
            </el-input>
          </el-form-item>

          <el-form-item label="备注" prop="remark">
            <el-input type="textarea" v-model="formData.remark" style="width:220px" placeholder="请输入备注" clearable :disabled="optionModel=='read'">
            </el-input>
          </el-form-item>
        </el-form>
      </el-col>
      <el-col :span="16">
        <el-form :rules="rules" size="medium" label-width="70px">
          <el-col :span="6" v-if="optionModel != 'read'">
            <el-form-item label="养护方法">
              <el-button icon="el-icon-plus" circle @click="openLibModel"></el-button>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="总费用">
              <el-input
                  disabled
                  :value="total"
                  :style="{width: '100%'}">
              </el-input>
            </el-form-item>
          </el-col>
        </el-form>
        <el-table v-adjust-table
            :data="schemeList"
            border
            height="600px"
            ref="tableRef"
            style="width: 100%">
          <el-table-column
              label="序号"
              align="center"
              type="index"
              width="50"
          />
          <el-table-column
              prop="schemeCode"
              align="center"
              label="子目号"
              width="100">
          </el-table-column>
          <el-table-column
              prop="schemeName"
              align="center"
              label="子目名称"
              width="100">
            <template slot-scope="scope">
              <el-input
                v-if="scope.row.schemeCode.indexOf('10000') != -1"
                v-model="scope.row.schemeName"
              >
              </el-input>
              <span v-else>{{scope.row.schemeName}}</span>
            </template>
          </el-table-column>
          <el-table-column
              prop="unit"
              align="center"
              label="单位"
              width="100">
            <template slot-scope="scope">
              <el-input
                v-if="scope.row.schemeCode.indexOf('10000') != -1"
                v-model="scope.row.unit"
              >
              </el-input>
              <span v-else>{{scope.row.schemeName}}</span>
            </template>
          </el-table-column>
          <el-table-column
              prop="price"
              align="center"
              label="单价"
              width="100">
            <template slot-scope="scope">
              <el-input
                v-if="scope.row.schemeCode.indexOf('10000') != -1"
                v-model="scope.row.price"
                @change="changePrice(scope.row)"
              >
              </el-input>
              <span v-else>{{scope.row.price}}</span>
            </template>
          </el-table-column>
          <el-table-column
              prop="calcDesc"
              align="center"
              label="计算式"
              width="100">
            <template slot-scope="scope">
              <el-input v-model="scope.row.calcDesc" @change="changeCalculation(scope.row)" :disabled="optionModel=='read'">
              </el-input>
            </template>
          </el-table-column>
          <el-table-column
              prop="num"
              align="center"
              label="方法数量"
              width="100">
            <template slot-scope="scope">
              <el-input v-model="scope.row.num"  @change="changeSchemeNum(scope.row)" :disabled="optionModel=='read'">
              </el-input>
            </template>
          </el-table-column>
          <el-table-column
              prop="amount"
              align="center"
              label="资金"
              width="100">
          </el-table-column>
          <el-table-column
              prop="remark"
              align="center"
              label="备注"
              width="100">
            <template slot-scope="scope">
              <el-input v-model="scope.row.remark" :disabled="optionModel=='read'">
              </el-input>
            </template>
          </el-table-column>
          <el-table-column
              prop="field101"
              align="center"
              label="移除"
              fixed="right"
              v-if="optionModel != 'read'"
          >
              <template slot-scope="scope">
                  <el-button
                          size="mini"
                          type="text"
                          @click="handleDelete(scope.row)"
                  >移除
                  </el-button
                  >
              </template>
          </el-table-column>
        </el-table>
      </el-col>
    </el-row>
    <span slot="footer" class="dialog-footer" style="display: flex;justify-content: flex-end;margin-top: 10px;">
      <el-button type="primary" @click="onSubmit" v-if="optionModel!='read'">确定</el-button>
      <el-button @click="closeDialog()">取消</el-button>
    </span>
    <methods-tree ref="methods" :con-id="formData.conIds" v-model="methods" :loading="submitLoading" :domain-id="domainId"></methods-tree>
  </div>

</template>

<script>
import { AddPlanDetail, EditPlanDetail, AddCondetail, GetCondetail, EditCondetail } from "@/api/budgetManage/planManage";
import {getListAll} from "@/api/contract/info/index"
import MethodsTree from "@/components/MethodsTree/index.vue";
import { Decimal } from 'decimal.js';

export default {
  components: {MethodsTree},
  props: {
    planId: {
      type: String,
      default: "",
    },
    typeId: {
      type: [String, Number],
      default: "",
    },
    companyType: {
      type: Number,
      default: 0,
    },
    currentRow: {
      type: Object,
      default: null,
    },
    maiSecId: {
      type: String,
      default: "",
    },
    optionModel: {
      type: String,
      default: "add",
    },
    domainId: {
      type: [String, Number],
      default: "",
    },
  },
  watch: {
    methods: {
      async handler(val) {
        const cloneData = JSON.parse(JSON.stringify(val))
        cloneData.forEach(item=> {
          item.conId = this.formData.conIds
          item.schemeId = item.id
          delete item.id
        })
        const tempList = []
        for (let index = 0; index < cloneData.length; index++) {
          const item = cloneData[index];
          const hasFlag = this.schemeList.some((value) => {
              return value.schemeId === item.schemeId;
          });
          if (hasFlag) {
            try {
                await this.$confirm(
                  `存在与子目号${item.schemeCode}相同的数据，是否确认加入?`,
                  "确认",
                  {
                    confirmButtonText: "确定",
                    cancelButtonText: "取消",
                    type: "warning",
                    distinguishCancelAndClose: true,
                  }
                );
                tempList.push(item);
              } catch (error) {
                continue;
              }
          } else {
            tempList.push(item);
          }
        }
        this.schemeList = [...this.schemeList, ...tempList]
      },
      immediate: true,
      deep: true
    },
  },
  data() {
    return {
      formData: {
        projectName:"",
        projectReason:"",
        conIds:"",
        remark:"",
      },
      rules: {
        projectName: [
          { required: true, message: '请输入项目名称', trigger: 'blur' },
        ],
        projectReason: [
          { required: true, message: '请输入立项理由', trigger: 'blur' },
        ],
        conIds: [
          { required: true, message: '请选择合同', trigger: 'change' },
        ]
      },
      contractList: [],
      libData:[],
      methods: [],
      schemeList: [],
      total: 0,
      submitLoading: false
    }
  },
  methods:{
    onSubmit() {
      console.log("this.planId",this.planId)
      console.log("this.typeId",this.typeId)
      this.$refs["queryForm"].validate(async (valid) => {
          if (valid) {
            if (this.schemeList.length <= 0) {
                this.$message.warning('请先选择养护方法')
                return
              }
            this.submitLoading = true
            // 提交接口
            if(this.currentRow){ // 修改
              const params = {
                companyType:this.companyType,
                planId:this.planId,
                typeId:this.typeId,
                ...this.formData,
                sumFund:this.total,
                id:this.currentRow.id
              }
              // 先改表单
              const res = await EditPlanDetail(params)
              this.submitLoading = false
              if(res.code==200){
                this.schemeList.forEach((element,index) => {
                  element.detailId = this.currentRow.id
                  element.orderIndex = index
                });
                // 再改合同明细
                const result = await AddCondetail([...this.schemeList])
                if(result.code==200){
                  this.$modal.msgSuccess('保存成功')
                  this.closeDialog()
                }
              }
            }else{ // 新增
              const params = {
                companyType:this.companyType,
                planId:this.planId,
                typeId:this.typeId,
                ...this.formData,
                sumFund:this.total,
              }
              const res = await AddPlanDetail(params)
              this.submitLoading = false
              if(res.code==200){
                // 先传传左边的表单数据得到id再传右边的表格数组
                this.schemeList.forEach((element,index) => {
                  element.detailId = res.msg
                  element.orderIndex = index
                });

                const result = await AddCondetail([...this.schemeList])
                if(result.code==200){
                  this.$modal.msgSuccess('保存成功')
                  this.closeDialog()
                }
              }
            }
          }
        });
    },
    closeDialog(){
      this.$refs["queryForm"].resetFields();
      this.$emit('closeDialog')
    },
    // 合同列表
    async getContractList(){
      const res = await getListAll({ sectionName: this.maiSecId, type: '3' })
      this.contractList = res.rows || []
    },
    // handleCheckCon(e) {
    //   // 获取报价体系
    //   getTreeData({
    //     conId: e,
    //     schemeType: '日常养护'
    //   }).then(res => {
    //     this.libData = res.rows || []
    //     // 过滤libData中children为空的
    //     this.libData = this.libData.filter(item => {
    //       return item.children && item.children.length > 0
    //     })
    //   })
    // },
    openLibModel() {
      this.$refs.methods.openLibModel()
    },
    changePrice(row) {
      this.$set(row, "amount", Math.round((row.num || 0) * (row.price || 0)));
      this.total = this.schemeList.reduce((acc, curr) => acc + curr.amount, 0);
      this.formData.sumFund = this.total;
    },
    changeCalculation(row) {
      if (!this.isValidMathFormula(row.calcDesc)) {
        this.$modal.msgError('计算式错误，请检查')
        return
      }
      this.$set(row, 'num', Math.round(this.math.evaluate(row.calcDesc),3))
      this.$set(row, 'amount', Math.round(new Decimal(row.num || 0).times(row.price || 0).toNumber()))
      this.total = this.schemeList.reduce((acc, curr) => acc + curr.amount, 0)
    },
    changeSchemeNum(row) {
      this.$set(row, 'amount', Math.round(new Decimal(row.num || 0).times(row.price || 0).toNumber()))
      this.total = this.schemeList.reduce((acc, curr) => acc + curr.amount, 0)
    },
    handleDelete(row) {
      this.schemeList = this.schemeList.filter(item => item.id !== row.id);
      this.total = this.schemeList.reduce((acc, curr) => acc + curr.amount, 0)
    },
  },
  async created(){
    // 初始化合同列表
    this.getContractList()
     // 编辑 查看 状态回显
    if(this.currentRow){
      this.typeId = this.currentRow.typeId
      this.companyType = this.currentRow.companyType
      this.formData.projectName = this.currentRow.projectName
      this.formData.projectReason = this.currentRow.projectReason
      this.formData.conIds = this.currentRow.conIds
      this.formData.remark = this.currentRow.remark
      this.total = this.currentRow.sumFund
      if(this.currentRow.conIds){
        const res = await GetCondetail({detailId:this.currentRow.id,pageSize: 999})
        this.schemeList = res.rows
        console.log("res",res)
        console.log(" this.schemeList", this.schemeList)
      }
    }
  }
}

</script>


<style scoped lang="scss">
.maindiv{
  .el-form-item{
    width: 100%;
    text-align: center;
  }
}

</style>
<style lang="scss" scoped>
@import "@/assets/styles/business.scss";
</style>
