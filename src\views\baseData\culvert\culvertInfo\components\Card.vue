<template>
  <div>
    <el-dialog
      v-loading="loading"
      :title="title"
      :visible.sync="dialogVisible"
      :before-close="handleClose"
      :close-on-click-modal="false"
      width="80%"
    >
      <div style="overflow-y: auto;height: 65vh;">

        <el-descriptions
          :label-style="{'display': 'none'}"
          :content-style="{'height': '41px'}"
          :column="1"
          border
        >
          <el-descriptions-item>1.行政识别数据</el-descriptions-item>
        </el-descriptions>
        <el-descriptions
          :label-style="labelStyle"
          :content-style="contentStyle"
          :column="3"
          border
        >
          <el-descriptions-item
            v-for="(item,index) in fields1"
            :key="'fields1' + index"
          >
            <el-col
              :span="12"
              style="text-align: center; border-right: 1px solid #e6ebf5;height: 100%;line-height: 41px;"
            >
              {{ item.label }}
            </el-col>
            <el-col
              :span="12"
              style="text-align: center;height: 100%;line-height: 41px;"
            >
              <span v-if="(item.type === 'input' && !forView) || (item.type === 'stake' && !forView)">
                <el-input v-model="form[item.prop]"></el-input>
              </span>
              <span v-else-if="item.type === 'date' && !forView">
                <el-date-picker
                  v-model="form[item.prop]"
                  class="bridge-card-input"
                  style="width:100%"
                  type="date"
                  value-format="yyyy-MM-dd"
                />
              </span>
              <span v-else-if="item.type === 'dict'">
                <el-select
                  v-model="form[item.prop]"
                  style="width: 100%;"
                  placeholder=""
                  :disabled="forView"
                >
                  <el-option
                    v-for="i in dict.type[item.dict]"
                    :key="'dict1'+i.value"
                    :label="i.label"
                    :value="i.value"
                  />
                </el-select>
              </span>
              <span v-else-if="item.type === 'stake' && forView">
                {{ formatPile(form[item.prop]) }}
              </span>
              <span v-else-if="item.type === 'date' && forView">
                {{ parseTime(form[item.prop]) }}
              </span>
              <span v-else>{{ form[item.prop] }}</span>
            </el-col>
          </el-descriptions-item>
        </el-descriptions>
        <el-descriptions
          :label-style="{'display': 'none'}"
          :content-style="{'height': '41px', 'border-top': '0'}"
          :column="1"
          border
        >
          <el-descriptions-item>2.结构数据</el-descriptions-item>
        </el-descriptions>
        <el-descriptions
          :label-style="labelStyle"
          :content-style="contentStyle"
          :column="3"
          border
        >
          <el-descriptions-item
            v-for="(item,index) in fields2"
            :key="'fields2' + index"
          >
            <el-col
              :span="12"
              style="text-align: center; border-right: 1px solid #e6ebf5;height: 100%;line-height: 41px;"
            >
              {{ item.label }}
            </el-col>
            <el-col
              :span="12"
              style="text-align: center;height: 100%;line-height: 41px;"
            >
              <span v-if="item.type === 'input' && !forView">
                <el-input v-model="form[item.prop]"></el-input>
              </span>
              <span v-else>{{ form[item.prop] }}</span>
            </el-col>
          </el-descriptions-item>
        </el-descriptions>
        <el-descriptions
          :label-style="{'display': 'none'}"
          :content-style="{'height': '41px', 'border-top': '0'}"
          :column="1"
          border
        >
          <el-descriptions-item>3.档案资料(全、不全或无)</el-descriptions-item>
        </el-descriptions>
        <el-descriptions
          :label-style="labelStyle"
          :content-style="contentStyle"
          :column="4"
          border
        >
          <el-descriptions-item
            v-for="(item,index) in fields3"
            :key="'fields3'+index"
          >
            <el-col
              :span="12"
              style="text-align: center; border-right: 1px solid #e6ebf5;height: 100%;line-height: 41px;"
            >
              {{ item.label }}
            </el-col>
            <el-col
              :span="12"
              style="text-align: center;height: 100%;line-height: 41px;"
            >
              <span v-if="forView">
                <dict-tag
                  :options="dict.type[item.dict]"
                  :value="form[item.prop]"
                  v-if="item.type == 'dict'"
                /> 
                <span v-else>{{ form[item.prop] }}</span>
              </span>
              <span v-else-if="item.type == 'dict'">
                <el-select
                  v-model="form[item.prop]"
                  style="width: 100%;"
                  placeholder=""
                  :disabled="forView"
                >
                  <el-option
                    v-for="i in dict.type[item.dict]"
                    :key="'dict2'+i.value"
                    :label="i.label"
                    :value="i.value"
                  />
                </el-select>
              </span>
              <span v-else>{{ form[item.prop] }}</span>
            </el-col>
          </el-descriptions-item>
        </el-descriptions>
        <el-descriptions
          :label-style="{'display': 'none'}"
          :content-style="{'height': '41px', 'border-top': '0', 'border-bottom': '0'}"
          :column="1"
          border
        >
          <el-descriptions-item>4.最近技术状况评定</el-descriptions-item>
        </el-descriptions>
        <el-table
          height="30%"
          border
          :data="tableData1"
          :header-cell-style="{'height': '36px'}"
        >
          <el-table-column
            label="评定时间"
            align="center"
            prop="date"
            min-width="80"
          />
          <el-table-column
              label="检测类别"
              align="center"
              prop="bridgeName"
              min-width="100"
              show-overflow-tooltip
          />
          <el-table-column
              label="涵洞部件技术状况统计结果/特殊检查结论"
              align="center"
              prop="bridgeName"
              min-width="200"
              show-overflow-tooltip
          />

<!--          <el-table-column-->
<!--            label="全涵评定等级"-->
<!--            align="center"-->
<!--            prop="bridgeName"-->
<!--            min-width="120"-->
<!--            show-overflow-tooltip-->
<!--          />-->
<!--          <el-table-column-->
<!--            label="进水口"-->
<!--            align="center"-->
<!--            prop="bridgeName"-->
<!--            min-width="120"-->
<!--            show-overflow-tooltip-->
<!--          />-->
<!--          <el-table-column-->
<!--            label="出水口"-->
<!--            align="center"-->
<!--            prop="bridgeName"-->
<!--            min-width="120"-->
<!--            show-overflow-tooltip-->
<!--          />-->
<!--          <el-table-column-->
<!--            label="涵身两侧"-->
<!--            align="center"-->
<!--            prop="bridgeName"-->
<!--            min-width="120"-->
<!--            show-overflow-tooltip-->
<!--          />-->
<!--          <el-table-column-->
<!--            label="涵身顶部"-->
<!--            align="center"-->
<!--            prop="bridgeName"-->
<!--            min-width="120"-->
<!--            show-overflow-tooltip-->
<!--          />-->
<!--          <el-table-column-->
<!--            label="涵底铺砌"-->
<!--            align="center"-->
<!--            prop="bridgeName"-->
<!--            min-width="120"-->
<!--            show-overflow-tooltip-->
<!--          />-->
<!--          <el-table-column-->
<!--            label="涵附近填土"-->
<!--            align="center"-->
<!--            prop="bridgeName"-->
<!--            min-width="120"-->
<!--            show-overflow-tooltip-->
<!--          />-->
<!--          <el-table-column-->
<!--            label="经常保养小修"-->
<!--            align="center"-->
<!--            prop="bridgeName"-->
<!--            min-width="120"-->
<!--            show-overflow-tooltip-->
<!--          />-->
          <el-table-column
            label="处治对策"
            align="center"
            prop="bridgeName"
            min-width="120"
            show-overflow-tooltip
          />
          <el-table-column
            label="下次检查时间"
            align="center"
            prop="bridgeName"
            min-width="120"
            show-overflow-tooltip
          />
        </el-table>
        <el-descriptions
          :label-style="{'display': 'none'}"
          :content-style="{'height': '41px', 'border-top': '0', 'border-bottom': '0'}"
          :column="1"
          border
        >
          <el-descriptions-item>5.建设及维修记录</el-descriptions-item>
        </el-descriptions>
        <el-table
          height="30%"
          border
          :data="tableData2"
          :header-cell-style="{'height': '36px'}"
        >
          <el-table-column
            label="施工日期"
            align="center"
            prop="bridgeName"
            min-width="120"
            show-overflow-tooltip
          >
            <el-table-column
              label="开工"
              align="center"
              prop="bridgeName"
              min-width="120"
              show-overflow-tooltip
            />
            <el-table-column
              label="竣工"
              align="center"
              prop="bridgeName"
              min-width="120"
              show-overflow-tooltip
            />
          </el-table-column>
          <el-table-column
            label="修建类别"
            align="center"
            prop="bridgeName"
            min-width="120"
            show-overflow-tooltip
          />
          <el-table-column
            label="修建原因"
            align="center"
            prop="bridgeName"
            min-width="120"
            show-overflow-tooltip
          />
          <el-table-column
            label="工程范围"
            align="center"
            prop="bridgeName"
            min-width="120"
            show-overflow-tooltip
          />
          <el-table-column
            label="工程费用(万元)"
            align="center"
            prop="bridgeName"
            min-width="120"
            show-overflow-tooltip
          />
          <el-table-column
            label="经费来源"
            align="center"
            prop="bridgeName"
            min-width="120"
            show-overflow-tooltip
          />
          <el-table-column
            label="质量评定"
            align="center"
            prop="bridgeName"
            min-width="120"
            show-overflow-tooltip
          />
          <el-table-column
            label="建设单位"
            align="center"
            prop="bridgeName"
            min-width="120"
            show-overflow-tooltip
          />
          <el-table-column
            label="设计单位"
            align="center"
            prop="bridgeName"
            min-width="120"
            show-overflow-tooltip
          />
          <el-table-column
            label="监理单位"
            align="center"
            prop="bridgeName"
            min-width="120"
            show-overflow-tooltip
          />
          <el-table-column
            label="施工单位"
            align="center"
            prop="bridgeName"
            min-width="120"
            show-overflow-tooltip
          />
        </el-table>
        <el-descriptions
          :label-style="{'display': 'none'}"
          :content-style="{'height': '41px', 'border-top': '0'}"
          :column="1"
          border
        >
          <el-descriptions-item>6.涵洞照片</el-descriptions-item>
        </el-descriptions>
        <el-descriptions
          :label-style="labelStyle"
          :content-style="contentStyle"
          :column="2"
          border
        >
          <el-descriptions-item>
            <el-col
              :span="12"
              style="border-right: 1px solid #e6ebf5; display: flex; justify-content: center;align-items: center;height: 100%"
            >
              进洞口图片
            </el-col>
            <el-col
              :span="12"
              style="text-align: center;height: 100%;"
            >
              <span v-if="!forView">
                <ImageUpload
                  style="margin:10px"
                  v-model="form.entranceHoleImageId"
                  :limit="1"
                />
              </span>
              <span v-else>
                <span v-if="form.entranceHoleImageId">
                  <ImagePreview
                    width="148px"
                    height="148px"
                    style="margin:10px"
                    :owner-id="form.entranceHoleImageId"
                  />
                </span>
                <span v-else>
                  <div style="width: 148px; height: 148px">
                  </div>
                </span>
              </span>
            </el-col>
          </el-descriptions-item>
          <el-descriptions-item>
            <el-col
              :span="12"
              style="border-right: 1px solid #e6ebf5; display: flex; justify-content: center;align-items: center;height: 100%"
            >
              出洞口图片
            </el-col>
            <el-col
              :span="12"
              style="text-align: center;height: 100%;"
            >
              <span v-if="!forView">
                <ImageUpload
                  style="margin:10px"
                  v-model="form.exitHoleImageId"
                  :limit="1"
                />
              </span>
              <span v-else>
                <span v-if="form.exitHoleImageId">
                  <ImagePreview
                    width="148px"
                    height="148px"
                    style="margin:10px"
                    :owner-id="form.exitHoleImageId"
                  />
                </span>
                <span v-else>
                  <div style="width: 148px; height: 148px">
                  </div>
                </span>
              </span>
            </el-col>
          </el-descriptions-item>
        </el-descriptions>
        <el-descriptions
          :label-style="labelStyle"
          :content-style="contentStyle"
          :column="3"
          border
        >
          <el-descriptions-item
            v-for="(item,index) in fields6"
            :key="'fields6' + index"
          >
            <el-col
              :span="12"
              style="text-align: center; border-right: 1px solid #e6ebf5;height: 100%;line-height: 41px;"
            >
              {{ item.label }}
            </el-col>
            <el-col
              :span="12"
              style="text-align: center;height: 100%;line-height: 41px;"
            >
              <span v-if="item.type === 'input' && !forView">
                <el-input v-model="form[item.prop]"></el-input>
              </span>
              <span v-else-if="item.type === 'date' && !forView">
                <el-date-picker
                  v-model="form[item.prop]"
                  class="bridge-card-input"
                  style="width:100%"
                  type="date"
                  value-format="yyyy-MM-dd"
                />
              </span>
              <span v-else>{{ form[item.prop] }}</span>
            </el-col>
          </el-descriptions-item>
        </el-descriptions>
      </div>
      <div slot="footer">
        <el-button
          v-if="!forView"
          type="primary"
          @click="handleSubmit"
        >保 存</el-button>
        <el-button @click="handleClose">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import dictionary from '../js/cardFields'
import { parseTime } from '@/utils/ruoyi'
import { getInfoById, getCard } from '@/api/baseData/culvert/culvertInfo/index.js'

export default {
  name: 'culvert-card',
  props: {
    title: {
      type: String,
      default: '查看涵洞卡片'
    },
    dialogVisible: {
      type: Boolean,
      default: false
    },
    forView: {
      type: Boolean,
      default: false
    },
    cardId: {
      type: undefined,
      default: ''
    }
  },
  dicts: [
    'bridge_route_level',
    'bridge_function_type',
    'bridge_design_load',
    'base_archives_type',
    'bridge_main_superstructure_type',
    'sys_route_grade',
    'base_archival_data',
    'sys_design_load'
  ],
  components: {},
  data() {
    return {
      labelStyle: {
        display: 'none'
      },
      contentStyle: { 'border-top': '0', padding: '0', height: '41px' },
      loading: false,
      form: {},
      fields1: [],
      fields2: [],
      fields3: [],
      tableData1: [],
      tableData2: [],
      fields6: []
    }
  },
  created() {
    this.fields1 = dictionary.fields1
    this.fields2 = dictionary.fields2
    this.fields3 = dictionary.fields3
    this.fields6 = dictionary.fields6
  },
  methods: {
    getForm() {
      this.loading = true
      getCard({ids:[this.cardId]})
        .then(res => {
          if (res.length>0) {
            this.form = res[0].culvert
          }
        })
        .finally(() => {
          this.loading = false
        })
    },
    handleSubmit() {},
    handleClose() {
      if (this.forView) {
        this.$emit('close', false)
      } else {
        this.$modal
          .confirm('确认退出？')
          .then(() => {
            this.form = {}
            this.$emit('close', false)
          })
          .catch(() => {})
      }
    }
  },
  computed: {},
  watch: {
    cardId: {
      handler(val) {
        if (val) {
          this.getForm()
        }
      },
      deep: true
    }
  }
}
</script>

<style lang="scss" scoped>
::v-deep {
  .el-input__inner {
    border: 0;
  }
  .el-input.is-disabled .el-input__inner {
    background-color: white;
    border-color: #dfe4ed;
    color: #1d2129;
  }
}
</style>
