<template>
  <Echarts ref="chart" v-if="option && !showTable"  :option="option" height="100%" :key="optionKey" />
  <LineTesterTabel  :source-data="tableData"  v-else-if="showTable"></LineTesterTabel>
  <view v-else></view>
</template>

<script>
import Echarts from '../../components/echarts.vue';
import { fetchGet } from '../api.js'
import { isBigScreen } from '../../utils/utils.js';
import * as echarts from "echarts";
import { vehicleTypeList } from "@/assets/vehiclePic/vehicleType";
import RealTimeWebSocket from "./realTimeWebSocket.js";
import LineTesterTabel from "./LineTesterTabel.vue"

// const mainUrl ="https://jkjc.yciccloud.com:8000"; //养护系统测试使用
// const mainUrl ="http://**********"; //西藏发布正式使用
const mainUrl ="http://**********"; //西藏发布正式使用




export default {
  name: 'ChartOneAndTwo',
  props: {
    chartBoxHeight: {
      type: String,
      default: '260px',
    },
    theme: {
      type: String,
      default: 'dark',
    },
    activeName: {
      type: String,
      default: '1',
    },
	showTable:{
		type: Boolean,
		default: false,
	}
  },
  components: { Echarts,LineTesterTabel },
  data() {
    return {
		tableData:[],
      isBig: isBigScreen(),
      option: null,
      optionKey: 'option1',
      sgWindowLength: 11,
      sgPolyOrder: 1,
      analysisType: { index: 2, label: '均值' },
      searchForm: {
        sensorId: '',
        specificMonitorContentIds: [],
        isPreprocess: false,
        isShowLimit: false,
        granularityNum: 1,
        granularityType: { index: 5, label: '天' },
        formatter: "{yyyy}-{MM}-{dd}",
        formatterNum: 12,
        granularityTypeName: '日',
      },
      tabNum: '',
      realTimeWebSocket: null,
      realTimePushData: {},
      myChart: null,
    }
  },
  created() {
  },
  methods: {
    reLoad() {
      if (this.$refs.chart) {
        this.$refs.chart.reload()
      }
    },

    //获取实时数据后绘制图表
    async drawRealTimeData(searchForm, selectedSensor) {
      this.tabNum = 1
      this.searchForm = searchForm
      let tmpType = await this.monitorTypeJudge(selectedSensor)
      console.log('tmpType', tmpType)
      if (!searchForm.sensorId) {
        this.$message.warning("查询数据前请注意查询条件的完整性")
        return;
      }
      this.option = null
      if (searchForm.isPreprocess) {
        // 预处理数据
        const url = mainUrl+"/xboot/displayScreen/default/preprocess"
        const params = {
          nodeCode: selectedSensor.code,
          sensorId: selectedSensor.sensorId,
          structureNodeCode: this.$route.query.code,
          granularityNum: 1,
          granularityType: 0,
          analysisType: 2,
          sgWindowLength: this.sgWindowLength,
          sgPolyOrder: this.sgPolyOrder,
          dataType: 0,
          specificMonitorContentIds: searchForm.specificMonitorContentIds,
        }
        try {
          const res = await fetchGet(url, params)
          if (res.code === 200) {
            if (res.result?.length && res.result[0].processedData?.errMsg) {
              this.$message.error(res.result[0].processedData?.errMsg || "获取历史数据失败")
              this.setEmptyChart('无实时数据')
              return
            }
            switch (tmpType) {
              case 0:
                this.option = this.setNormalChart(res.result)
                this.optionKey = new Date().getTime();
                break;
              case 1:
                this.option = this.setWindChart2(res.result)
                this.optionKey = new Date().getTime();
                break;
              case 2:
                this.option = this.setVehicleLoadChart(res.result)
                this.optionKey = new Date().getTime();
                break;
            }
            this.$message.success(res.message || "数据获取成功")
            return true
          }
        } catch (err) {
          this.$message.error(err || '数据获取失败')
          this.setEmptyChart('无实时数据')
        }
      } else {
        // 非预处理数据
        const url = mainUrl+"/xboot/displayScreen/default/getCalibratedRealTimeDataYH"
        const params = {
          nodeCode: selectedSensor.code,
          sensorId: selectedSensor.sensorId,
          structureNodeCode: this.$route.query.code,
          granularityNum: 1,
          granularityType: 0,
          analysisType: 2,
          specificMonitorContentIds: searchForm.specificMonitorContentIds,
        }
        try {
          const res = await fetchGet(url, params)
          if (res.code === 200) {
            if (res.result?.length && res.result[0].values?.length === 0) {
              this.setEmptyChart('无实时数据')
              return
            }
			this.tableData = res.result;
            switch (tmpType) {
              case 0:
                this.option = this.setRealChart(res.result);
                this.optionKey = new Date().getTime();
                break;
              case 1:
                this.option = this.setWindChart(res.result)
                this.optionKey = new Date().getTime();
                break;
              case 2:
                this.option = this.setVehicleLoadChart(res.result)
                this.optionKey = new Date().getTime();
                break;
            }
            this.$message.success(res.message || "数据获取成功")
            return true
          }
        } catch (err) {
          this.$message.error('数据获取失败')
          this.setEmptyChart('无实时数据')
        }
      }
    },

    //获取历史数据后绘制图表
    async drawHistoryData(searchForm, selectedSensor) {
      this.searchForm = searchForm
      let tmpType = await this.monitorTypeJudge(selectedSensor)
      if (
        searchForm.sensorId === "" ||
        searchForm.timeRange.length === 0 ||
        searchForm.timeRange[0] === ""
      ) {
        this.$message.warning("查询数据前请注意查询条件的完整性")
        return;
      }
      this.option = null
      if (searchForm.isPreprocess) {
        // 取预处理数据
        const url = mainUrl+"/xboot/displayScreen/default/preprocess"
        const params = {
          nodeCode: selectedSensor.code,
          sensorId: selectedSensor.sensorId,
          structureNodeCode: this.$route.query.code,
          startTime: searchForm.timeRange[0],
          endTime: searchForm.timeRange[1],
          granularityNum: searchForm.granularityNum,
          granularityType: searchForm.granularityType?.index,
          analysisType: this.analysisType?.index,
          sgWindowLength: this.sgWindowLength,
          sgPolyOrder: this.sgPolyOrder,
          specificMonitorContentIds: searchForm.specificMonitorContentIds
        }
        try {
          const res = await fetchGet(url, params)
          if (res.code === 200) {
            if (res.result?.length && res.result[0].processedData?.errMsg) {
              this.$message.error(res.result[0].processedData?.errMsg || "获取历史数据失败")
              this.setEmptyChart('无历史数据')
              return
            }
            switch (tmpType) {
              case 0:
                this.option = this.setNormalChart(res.result)
                this.optionKey = new Date().getTime();
                break;
              case 1:
                this.option = this.setWindChart2(res.result)
                this.optionKey = new Date().getTime();
                break;
              case 2:
                this.option = this.setVehicleLoadChart(res.result)
                this.optionKey = new Date().getTime();
                break;
            }
            this.$message.success(res.message || "获取历史数据成功")
            return true
          }
        } catch (error) {
          this.$message.error('获取历史数据失败')
          this.setEmptyChart('无历史数据')
        }
      } else {
        // 不做预处理直接取历史数据
        const url = mainUrl+"/xboot/displayScreen/default/getCalibratedHistoryData"
        const params = {
          nodeCode: selectedSensor.code,
          sensorId: selectedSensor.sensorId,
          structureNodeCode: this.$route.query.code,
          startTime: searchForm.timeRange[0],
          endTime: searchForm.timeRange[1],
          granularityNum: searchForm.granularityNum,
          granularityType: searchForm.granularityType?.index,
          analysisType: this.analysisType?.index,
          specificMonitorContentIds: searchForm.specificMonitorContentIds,
          timeOutMsg: "请求时间数据范围太大,请调整采样周期或周期单位"
        }
        try {
          const res = await fetchGet(url, params)
          if (res.code === 200) {
            if (res.result?.length && res.result[0].values?.length === 0) {
              this.setEmptyChart('无历史数据')
              return
            }
            this.changeGranularityTypeRe(res.result[0].granularityNum, res.result[0].granularityType)
            switch (tmpType) {
              case 0:
                this.option = this.setRealChart(res.result);
                this.optionKey = new Date().getTime();
                break;
              case 1:
                this.option = this.setWindChart(res.result)
                this.optionKey = new Date().getTime();
                break;
              case 2:
                this.option = this.setVehicleLoadChart(res.result)
                this.optionKey = new Date().getTime();
                break;
            }
            this.$message.success(res.message || "数据获取成功")
            return true
          } else {
            this.$message.error(res.message || '数据获取失败')
            this.setEmptyChart('无历史数据')
          }
        } catch (error) {
          this.$message.error('数据获取失败')
          this.setEmptyChart('无历史数据')
        }
      }
    },

    //反向转换粒度
    changeGranularityTypeRe(granularityNum, granularityType) {
      if (granularityType === 0) {
        this.searchForm.granularityNum = 1
        this.searchForm.granularityType = { index: 0, label: '原始' }
        this.searchForm.granularityTypeName = '原始'
      } else if (granularityNum === 1 && granularityType === 3) {
        this.searchForm.granularityNum = 1
        this.searchForm.granularityType = { index: 3, label: '分' }
        this.searchForm.granularityTypeName = '分'
      } else if (granularityNum === 10 && granularityType === 3) {
        this.searchForm.granularityNum = 10
        this.searchForm.granularityType = { index: 3, label: '分' }
        this.searchForm.granularityTypeName = '10分'
      } else if (granularityType === 4) {
        this.searchForm.granularityNum = 1
        this.searchForm.granularityType = { index: 4, label: '时' }
        this.searchForm.granularityTypeName = '小时'
      } else if (granularityType === 5) {
        this.searchForm.granularityNum = 1
        this.searchForm.granularityType = { index: 5, label: '天' }
        this.searchForm.granularityTypeName = '日'
      } else {
        this.searchForm.granularityNum = 1
        this.searchForm.granularityType = { index: 0, label: '原始' }
        this.searchForm.granularityTypeName = '原始'
      }
      this.$emit('changeGranularity', this.searchForm)
    },
    // 绘制数据平滑滤噪后的历史数据
    setNormalChart(data) {
      // 更改数据格式
      data.forEach((datai) => {
        let tmpOriginDotList = [];
        let tmpProcessDotList = [];
        datai.originData.times.forEach((value, index) => {
          datai.originData.values[index] = parseFloat(datai.originData.values[index].toFixed(datai.accuracy >= 0 ? datai.accuracy : 4))
          tmpOriginDotList.push(
            [datai.originData.times[index], datai.originData.values[index]]
          );
          datai.processedData.values[index] = parseFloat(datai.processedData.values[index].toFixed(datai.accuracy >= 0 ? datai.accuracy : 4))
          tmpProcessDotList.push(
            [datai.processedData.times[index], datai.processedData.values[index]]
          )
        })
        datai.originData.dot = tmpOriginDotList
        datai.processedData.dot = tmpProcessDotList
      })
      let formatterNum = this.tabNum === 1 ? 4 : this.searchForm.formatterNum
      // echarts配置项
      let option = {
        toolbox: {
          right: 0,
          top: 0,
          itemSize: this.isBig ? 30 : 12,
          feature: {
            saveAsImage: {
              title: '保存',
              type: 'png',
              backgroundColor: this.theme === 'dark' ? 'rgba(4, 37, 72, 0.8)' : '#fff',
            },
          },
        },
        tooltip: {
          textStyle: {
            align: 'left',
            fontSize: this.isBig ? 24 : 14,
          },
          trigger: "axis",
        },
        xAxis: {
          type: 'category',
          name: "时间",
          nameLocation: "middle",
          nameGap: this.isBig ? 64 : 32,
          nameTextStyle: {
            fontSize: this.isBig ? 24 : 12,
            color: this.theme === 'dark' ? '#fff' : '#000',
            lineHeight: this.isBig ? 120 : 20,
          },
          axisLabel: {
            showMinLabel: true,
            showMaxLabel: true,
            fontSize: this.isBig ? 24 : 11,
            color: this.theme === 'dark' ? '#fff' : '#000',
            formatter: function (value, index) {
              return value.substring(0, value.length - formatterNum);
            }
          },
        },
        yAxis: [],
        dataZoom: [
          {
            type: "inside",
          },
          {
            type: 'slider',
            xAxisIndex: 0,
            minSpan: this.isBig ? 20 : 5,
            height: this.isBig ? 80 : 20,
            bottom: this.isBig ? 120 : 5,
          }
        ],
        grid: [
          {
            top: this.isBig ? "20%" : "15%",
            left: this.isBig ? "3%" : "2%",
            right: 5 * (data.length > 2 ? data.length - 2 : 1) + "%",
            bottom: this.isBig ? "20%" : "15%",
            containLabel: true,
          },
        ],
        series: [],
        legend: {
          type: 'scroll',
          top: this.isBig ? "10%" : '0',
          itemGap: this.isBig ? 40 : 10,
          data: [],
          selected: {},
          textStyle: {
            color: this.theme === 'dark' ? '#fff' : '#000',
            fontSize: this.isBig ? 25 : 14,
          },
        },
      };
      //加入多行数据
      for (let i = 0; i < data.length; i++) {
        option.legend.data.push(data[i].name);
        option.legend.data.push(data[i].name + "预处理结果");
        option.series.push({
          type: "line",
          name: data[i].name,
          showSymbol: false,
          data: data[i].originData.dot,
          yAxisIndex: i,
        });
        // push处理后的值
        option.series.push({
          type: "line",
          name: data[i].name + "预处理结果",
          showSymbol: false,
          data: data[i].processedData.dot,
          yAxisIndex: i,
        });
        // 画预警线
        let limitInfoList = [];
        let markLineDataList = []
        if (data[i].limitInfo && this.searchForm.isShowLimit) {
          option.legend.data.push("预警线")
          option.series.push({
            type: "line",
            name: "预警线",
            showSymbol: false,
            yAxisIndex: i,
            animation: false,
            markLine: {
              symbol: 'none',
              data: markLineDataList,
              lineStyle: {
                color: "red"
              }
            },
            itemStyle: {
              color: "red"
            }
          });
          Object.keys(data[i].limitInfo).forEach((key) => {
            markLineDataList.push({
              yAxis: data[i].limitInfo[key],
              lineStyle: {
                type: 'solid'
              },
              emphasis: {
                label: {
                  position: 'outside',
                  formatter: data[i].name + key + data[i].limitInfo[key],
                  show: true,
                },
              },
              label: {
                show: false,
              },
            })
            limitInfoList.push(data[i].limitInfo[key])
          })
        }
        //计算最大值与最小值
        let minValue = this.getMin(data[i].originData.values); // 输出最小值
        let maxValue = this.getMax(data[i].originData.values); // 输出最大值
        limitInfoList.push(minValue)
        limitInfoList.push(maxValue)
        minValue = this.getMin(limitInfoList)
        maxValue = this.getMax(limitInfoList)
        // 一些特殊情况处理
        if (maxValue === 0 && minValue === 0) {
          maxValue = 1
          minValue = -1
        } else {
          let delta = Math.ceil((maxValue - minValue) * 0.2 * (10 ** data[i].accuracy)) / (10 ** data[i].accuracy)
          if (delta === 0) {
            delta = 1
          }
          let midValue = Math.ceil((maxValue + minValue) / 2 * (10 ** data[i].accuracy)) / (10 ** data[i].accuracy)
          maxValue = midValue + 3 * delta
          minValue = midValue - 3 * delta
        }
        // 湿度坐标轴范围特殊处理
        if (data[i].name.indexOf('湿度') >= 0) {
          if (minValue < 0) {
            minValue = 0;
          }
          if (maxValue > 100) {
            maxValue = 100;
          }
        }
        // 索力坐标轴范围特殊处理
        // if (data[i].name.indexOf('索力') >= 0) {
        //   if (minValue < 0) {
        //     minValue = 0;
        //   }
        // }
        // 如果超过3列数据，则多余的y轴需要偏移
        if (i > 1) {
          option.yAxis.push({
            name: data[i].name + "/" + data[i].unit,
            nameTextStyle: {
              fontWeight: "bold",
              fontSize: this.isBig ? 24 : 12,
              color: this.theme === 'dark' ? '#fff' : '#000',
              borderWidth: 12,
            },
            axisLine: {
              show: true,
              lineStyle: {
                width: '1'//坐标线的宽度
              },
              onZero: false,
            },
            axisLabel: {
              fontSize: this.isBig ? 22 : 11,
              color: this.theme === 'dark' ? '#fff' : '#000',
              formatter: function (value) {
                return value.toFixed(data[i].accuracy >= 0 ? data[i].accuracy : 4); // 2表示小数为2位
              },
            },
            splitNumber: 6,
            min: minValue,
            max: maxValue,
            interval: (maxValue - minValue) / 6, // 标轴分割间隔
            offset: 120 * (i - 1),
            splitLine: {
              show: true,
              lineStyle: {
                color: this.theme === 'dark' ? 'rgba(110, 112, 121, 0.50)' : '#eee',
              }
            },
            splitArea: {
              show: false,
            },
          });
        } else {
          option.yAxis.push({
            name: data[i].name + "/" + data[i].unit,
            nameTextStyle: {
              fontWeight: "bold",
              fontSize: this.isBig ? 24 : 12,
              color: this.theme === 'dark' ? '#fff' : '#000',
            },
            axisLabel: {
              fontSize: this.isBig ? 22 : 11,
              color: this.theme === 'dark' ? '#fff' : '#000',
              formatter: function (value) {
                return value.toFixed(data[i].accuracy >= 0 ? data[i].accuracy : 4); // 2表示小数为2位
              },
            },
            splitNumber: 6,
            min: minValue,
            max: maxValue,
            interval: (maxValue - minValue) / 6, // 标轴分割间隔
            splitLine: {
              show: true,
              lineStyle: {
                color: this.theme === 'dark' ? 'rgba(110, 112, 121, 0.50)' : '#eee',
              }
            },
            splitArea: {
              show: false,
            },
          })
        }
      }
      return option
    },
    //绘制实时数据 带预警值线
    setRealChart(data) {
      // 修改数据格式
      data.forEach((datai) => {
        let tmpDotList = [];
        datai.times.forEach((value, index) => {
          if (datai.values[index] === null) {

          } else {
            datai.values[index] = parseFloat(datai.values[index].toFixed(datai.accuracy >= 0 ? datai.accuracy : 4))
          }
          tmpDotList.push(
            [datai.times[index], datai.values[index]]
          )
        })
        datai.dot = tmpDotList
      })
      let formatterNum = this.tabNum === 1 ? 4 : this.searchForm.formatterNum
      let option = {
        toolbox: {
          right: 0,
          top: 0,
          itemSize: this.isBig ? 30 : 12,
          feature: {
            saveAsImage: {
              title: '保存',
              type: 'png',
              backgroundColor: this.theme === 'dark' ? 'rgba(4, 37, 72, 0.8)' : '#fff',
            },
          },
        },
        tooltip: {
          textStyle: {
            align: 'left',
            fontSize: this.isBig ? 24 : 14,
          },
          trigger: "axis",
          confine: true
        },
        xAxis: {
          type: 'category',
          name: "时间",
          nameLocation: "middle",
          nameTextStyle: {
            fontSize: this.isBig ? 24 : 12,
            color: this.theme === 'dark' ? '#fff' : '#000',
          },
          nameGap: this.isBig ? 40 : 20,
          axisLabel: {
            showMinLabel: true,
            showMaxLabel: true,
            fontSize: this.isBig ? 24 : 11,
            color: this.theme === 'dark' ? '#fff' : '#000',
            formatter: function (value, index) {
              return value.substring(0, value.length - formatterNum);
            }
          },
        },
        yAxis: [],
        dataZoom: [
          {
            type: "inside",
          },
          {
            type: 'slider',
            xAxisIndex: 0,
            minSpan: this.isBig ? 20 : 5,
            height: this.isBig ? 80 : 20,
            bottom: this.isBig ? 120 : 5,
            // textStyle: {
            //   color: '#333', // 文本颜色
            //   fontSize: 12, // 文本字体大小
            // },
          }
        ],
        grid: [
          {
            top: this.isBig ? "20%" : "15%",
            left: this.isBig ? "3%" : "2%",
            right: 5 * (data.length > 2 ? data.length - 2 : 1) + "%",
            bottom: this.isBig ? "20%" : "15%",
            containLabel: true,
          },
        ],
        series: [],
        legend: {
          type: 'scroll',
          top: this.isBig ? "10%" : '0',
          itemGap: this.isBig ? 40 : 10,
          data: [],
          selected: {},
          textStyle: {
            color: this.theme === 'dark' ? '#fff' : '#000',
            fontSize: this.isBig ? 25 : 14,
          },
        },
      };
      // 不单独加legend图例也可以，此处单独给其data添加是为了控制换行
      //加入多行数据
      for (let i = 0; i < data.length; i++) {
        option.legend.data.push(data[i].name);
        option.series.push({
          type: "line",
          name: data[i].name,
          showSymbol: false,
          data: data[i].dot,
          yAxisIndex: i,
        });
        let limitInfoList = [];
        let markLineDataList = []
        if (data[i].limitInfo && this.searchForm.isShowLimit) {
          option.legend.data.push("预警线")
          option.series.push({
            type: "line",
            name: "预警线",
            showSymbol: false,
            yAxisIndex: i,
            animation: false,
            markLine: {
              symbol: 'none',
              data: markLineDataList,
              lineStyle: {
                color: "red"
              }
            },
            itemStyle: {
              color: "red"
            }
          });
          Object.keys(data[i].limitInfo).forEach((key) => {
            markLineDataList.push({
              yAxis: data[i].limitInfo[key],
              emphasis: {
                label: {
                  position: 'outside',
                  formatter: data[i].name + key + data[i].limitInfo[key],
                  show: true,
                },
              },
              label: {
                show: false,
              },
              axisLabel: {
                formatter: function (value) {
                  return value.toFixed(data[i].accuracy >= 0 ? data[i].accuracy : 4);
                },
                fontSize: this.isBig ? 22 : 11,
                color: this.theme === 'dark' ? '#fff' : '#000',
              },
            })
            limitInfoList.push(data[i].limitInfo[key])
          })
        }
        // 设置图例换行
        // option.legend.data.push("")
        //计算最大值与最小值
        let minValue = this.getMin(data[i].values); // 输出最小值
        let maxValue = this.getMax(data[i].values); // 输出最大值
        limitInfoList.push(minValue)
        limitInfoList.push(maxValue)
        minValue = this.getMin(limitInfoList)
        maxValue = this.getMax(limitInfoList)
        // 一些特殊情况处理
        if (maxValue === 0 && minValue === 0) {
          maxValue = 1
          minValue = -1
        } else {
          let delta = Math.ceil((maxValue - minValue) * 0.2 * (10 ** data[i].accuracy)) / (10 ** data[i].accuracy)
          if (delta === 0) {
            delta = 1
          }
          let midValue = Math.ceil((maxValue + minValue) / 2 * (10 ** data[i].accuracy)) / (10 ** data[i].accuracy)
          maxValue = midValue + 3 * delta
          minValue = midValue - 3 * delta
        }
        // 湿度坐标轴范围特殊处理
        if (data[i].name.indexOf('湿度') >= 0) {
          if (minValue < 0) {
            minValue = 0;
          }
          if (maxValue > 100) {
            maxValue = 100;
          }
        }
        // 索力坐标轴范围特殊处理
        // if (data[i].name.indexOf('索力') >= 0) {
        //   if (minValue < 0) {
        //     minValue = 0;
        //   }
        // }
        // 如果超过3列数据，则多余的y轴需要偏移
        if (i > 1) {
          option.yAxis.push({
            name: data[i].name + "/" + data[i].unit,
            nameTextStyle: {
              fontWeight: "bold",
              fontSize: this.isBig ? 24 : 12,
              color: this.theme === 'dark' ? '#fff' : '#000',
            },
            axisLabel: {
              fontSize: this.isBig ? 22 : 11,
              color: this.theme === 'dark' ? '#fff' : '#000',
              formatter: function (value) {
                return value.toFixed(data[i].accuracy >= 0 ? data[i].accuracy : 4);
              },
            },
            splitNumber: 6,
            min: minValue,
            max: maxValue,
            interval: (maxValue - minValue) / 6, // 标轴分割间隔
            offset: 120 * (i - 1),
            splitLine: {
              show: true,
              lineStyle: {
                color: this.theme === 'dark' ? 'rgba(110, 112, 121, 0.50)' : '#eee',
              }
            },
            splitArea: {
              show: false,
            },
          });
        } else {
          option.yAxis.push({
            name: data[i].name + "/" + data[i].unit,
            nameTextStyle: {
              fontWeight: "bold",
              fontSize: this.isBig ? 24 : 12,
              color: this.theme === 'dark' ? '#fff' : '#000',
            },
            axisLabel: {
              fontSize: this.isBig ? 22 : 11,
              color: this.theme === 'dark' ? '#fff' : '#000',
              formatter: function (value) {
                return value.toFixed(data[i].accuracy >= 0 ? data[i].accuracy : 4); // 2表示小数为2位
              },
            },
            splitNumber: 6,
            min: minValue,
            max: maxValue,
            interval: (maxValue - minValue) / 6, // 标轴分割间隔
            splitLine: {
              show: true,
              lineStyle: {
                color: this.theme === 'dark' ? 'rgba(110, 112, 121, 0.50)' : '#eee',
              }
            },
            splitArea: {
              show: false,
            },
          })
        }
      }

      return option
    },
    // 绘制风速风向时程图
    setWindChart(data) {
      // 调整风速风向数据位置，风速为第一列，风向为第二列
      let tmpData = []
      for (let i = 0; i < data.length; i++) {
        if (data[i].name === '风速') {
          tmpData.push(data[i])
        }
      }
      for (let i = 0; i < data.length; i++) {
        if (data[i].name === '风向') {
          tmpData.push(data[i])
        }
      }
      if (tmpData.length !== 2) {
        return this.setRealChart(data)
      }
      // 若风速数据缺失，则补全为0
      if (tmpData[0].times.length === 0) {
        tmpData[1].times.forEach(function (name, index) {
          tmpData[0].times.push(tmpData[1].times[index])
          tmpData[0].values.push(0)
        });
      }
      // 数据格式标准化
      let windData = []
      tmpData[0].values.forEach(function (name, index) {
        if (tmpData[0].values[index] === null) {

        } else {
          tmpData[0].values[index] = parseFloat(tmpData[0].values[index].toFixed(tmpData[0].accuracy >= 0 ? tmpData[0].accuracy : 4))
        }
        windData[index] = [tmpData[0].times[index], tmpData[0].values[index],
        tmpData[1].values[index]]
      });
      const dims = {
        time: 0,
        windSpeed: 1,
        R: 2,
      };
      const arrowSize = this.isBig ? 36 : 18;
      const renderArrow = function (param, api) {
        const point = api.coord([
          api.value(dims.time),
          api.value(dims.windSpeed)
        ]);
        return {
          type: 'path',
          shape: {
            pathData: 'M31 16l-15-15v9h-26v12h26v9z',
            x: -arrowSize / 2,
            y: -arrowSize / 2,
            width: arrowSize,
            height: arrowSize
          },
          rotation: -(api.value(dims.R) / 360 * 2 * Math.PI) + (Math.PI / 2),
          position: point,
          style: api.style({
            stroke: '#555',
            lineWidth: 1
          })
        };
      };
      let formatterNum = this.tabNum === 1 ? 4 : this.searchForm.formatterNum
      let option = {
        toolbox: {
          right: 0,
          top: 0,
          itemSize: this.isBig ? 30 : 12,
          feature: {
            saveAsImage: {
              title: '保存',
              type: 'png',
              backgroundColor: this.theme === 'dark' ? 'rgba(4, 37, 72, 0.8)' : '#fff',
            },
          },
        },
        tooltip: {
          textStyle: {
            align: 'left',
            fontSize: this.isBig ? 24 : 14,
          },
          trigger: "axis",
          formatter: function (params) {
            return [
              echarts.format.formatTime(
                'yyyy-MM-dd',
                params[0].value[dims.time]
              ) +
              ' ' +
              echarts.format.formatTime('hh:mm', params[0].value[dims.time]),
              '风速：' + params[0].value[dims.windSpeed],
              '风向：' + (params[0].value[dims.R] ? params[0].value[dims.R] : '空'),
            ].join('<br>');
          }
        },
        xAxis: {
          type: 'category',
          name: "时间",
          nameLocation: "middle",
          nameTextStyle: {
            fontSize: this.isBig ? 24 : 12,
            color: this.theme === 'dark' ? '#fff' : '#000',
          },
          nameGap: this.isBig ? 40 : 20,
          axisLabel: {
            showMinLabel: true,
            showMaxLabel: true,
            fontSize: this.isBig ? 24 : 11,
            color: this.theme === 'dark' ? '#fff' : '#000',
            formatter: function (value, index) {
              return value.substring(0, value.length - formatterNum);
            }
          },
        },
        yAxis: [],
        dataZoom: [
          {
            type: "inside",
          },
        ],
        grid: [
          {
            top: "25%",
            left: "5%",
            right: "5%",
            bottom: this.isBig ? "20%" : "5%",
            containLabel: true,
          },
        ],
        series: [
          {
            type: 'custom',
            renderItem: renderArrow,
            encode: {
              x: dims.time,
              y: dims.windSpeed
            },
            data: windData,
            z: 10
          },
          {
            type: 'line',
            symbol: 'none',
            encode: {
              x: dims.time,
              y: dims.windSpeed
            },
            lineStyle: {
              color: '#aaa',
              type: 'dotted',
            },
            data: windData,
            z: 1
          },
        ],
        visualMap: {
          type: 'piecewise',
          // show: false,
          orient: 'horizontal',
          left: 'center',
          top: this.isBig ? 200 : 10,
          textStyle: {
            color: this.theme === 'dark' ? '#fff' : '#000', // 根据主题设置文本颜色
            fontSize: this.isBig ? 28 : 12 // 根据屏幕大小设置字体大小
          },
          itemWidth: this.isBig ? 40 : 20,
          itemHeight: this.isBig ? 30 : 14,
          itemGap: this.isBig ? 40 : 10,
          pieces: [
            {
              gte: 17,
              color: '#D33C3E',
              label: '大风（>=17）'
            },
            {
              gte: 11,
              lt: 17,
              color: '#f4e9a3',
              label: '中风（11 ~ 17）'
            },
            {
              lt: 11,
              color: '#18BF12',
              label: '微风（<11）'
            }
          ],
          seriesIndex: 0,
          dimension: 1
        },
      };
      // 使得曲线的最大值与最小值顶格
      let minValue = this.getMin(tmpData[0].values); // 输出最小值
      let maxValue = this.getMax(tmpData[0].values); // 输出最大值
      // 一些特殊情况处理
      if (maxValue === 0 && minValue === 0) {
        maxValue = 1
        minValue = -1
      } else {
        let delta = (maxValue - minValue) * 0.2
        if (delta === 0) {
          delta = 1
        }
        maxValue = maxValue + delta
        minValue = minValue - delta
      }
      if (minValue < 0) {
        minValue = 0
      }
      option.yAxis.push({
        name: "风速/" + tmpData[0].unit,
        nameTextStyle: {
          fontWeight: "bold",
          fontSize: this.isBig ? 24 : 12,
          color: this.theme === 'dark' ? '#fff' : '#000',
        },
        axisLabel: {
          fontSize: this.isBig ? 22 : 11,
          color: this.theme === 'dark' ? '#fff' : '#000',
          formatter: function (value) {
            return value.toFixed(tmpData[0].accuracy >= 0 ? tmpData[0].accuracy : 4); // 2表示小数为2位
          },
        },
        splitNumber: 6,
        min: minValue,
        max: maxValue,
        interval: (maxValue - minValue) / 6, // 标轴分割间隔
        splitLine: {
          show: true,
          lineStyle: {
            color: this.theme === 'dark' ? 'rgba(110, 112, 121, 0.50)' : '#eee',
          }
        },
        splitArea: {
          show: false,
        },
      })
      // 若风向数据缺失，则省去箭头
      if (tmpData[1].times.length === 0) {
        option.series[0] = {
          type: 'line',
        }
      }

      return option
    },
    // 绘制风速风向时程图-预处理
    setWindChart2(data) {
      let tmpData = []
      for (let i = 0; i < data.length; i++) {
        if (data[i].name === '风速') {
          tmpData.push(data[i])
        }
      }
      for (let i = 0; i < data.length; i++) {
        if (data[i].name === '风向') {
          tmpData.push(data[i])
        }
      }
      if (tmpData.length !== 2) {
        this.setNormalChart(data)
        return
      }
      // 若风速数据缺失，则补全为0
      if (tmpData[0].originData === null) {
        tmpData[0].originData = {
          times: [],
          values: []
        }
        tmpData[0].processedData = {
          times: [],
          values: []
        }
        tmpData[1].originData.times.forEach(function (name, index) {
          tmpData[0].originData.times.push(tmpData[1].times[index])
          tmpData[0].originData.values.push(0)
          tmpData[0].processedData.times.push(tmpData[1].times[index])
          tmpData[0].processedData.values.push(0)
        });
      }
      // 数据格式标准化
      let windData = []
      tmpData[0].originData.values.forEach(function (name, index) {
        tmpData[0].originData.values[index] = parseFloat(tmpData[0].originData.values[index].toFixed(tmpData[0].accuracy >= 0 ? tmpData[0].accuracy : 4))
        windData[index] = [tmpData[0].originData.times[index], tmpData[0].originData.values[index]
          , tmpData[1].originData?.values[index]]
      });
      let windData2 = []
      tmpData[0].processedData.values.forEach(function (name, index) {
        tmpData[0].processedData.values[index] = parseFloat(tmpData[0].processedData.values[index].toFixed(tmpData[0].accuracy >= 0 ? tmpData[0].accuracy : 4))
        windData2[index] = [tmpData[0].processedData.times[index], tmpData[0].processedData.values[index]
          , tmpData[1].processedData?.values[index]]
      });
      const dims = {
        time: 0,
        windSpeed: 1,
        R: 2,
      };
      const arrowSize = this.isBig ? 36 : 18;
      const renderArrow = function (param, api) {
        const point = api.coord([
          api.value(dims.time),
          api.value(dims.windSpeed)
        ]);
        return {
          type: 'path',
          shape: {
            pathData: 'M31 16l-15-15v9h-26v12h26v9z',
            x: -arrowSize / 2,
            y: -arrowSize / 2,
            width: arrowSize,
            height: arrowSize
          },
          rotation: -(api.value(dims.R) / 360 * 2 * Math.PI) + (Math.PI / 2),
          position: point,
          style: api.style({
            stroke: '#555',
            lineWidth: 1
          })
        };
      };
      let formatterNum = this.tabNum === 1 ? 4 : this.searchForm.formatterNum
      let option = {
        toolbox: {
          right: 0,
          top: 0,
          itemSize: this.isBig ? 30 : 12,
          feature: {
            saveAsImage: {
              title: '保存',
              type: 'png',
              backgroundColor: this.theme === 'dark' ? 'rgba(4, 37, 72, 0.8)' : '#fff',
            },
          },
        },
        tooltip: {
          textStyle: {
            align: 'left',
            fontSize: this.isBig ? 24 : 14,
          },
          trigger: "axis",
          formatter: function (params) {
            return [
              echarts.format.formatTime(
                'yyyy-MM-dd',
                params[0].value[dims.time]
              ) +
              ' ' +
              echarts.format.formatTime('hh:mm', params[0].value[dims.time]),
              '风速：' + params[0].value[dims.windSpeed],
              '风向：' + (params[0].value[dims.R] ? params[0].value[dims.R] : '空'),
            ].join('<br>');
          }
        },
        xAxis: {
          type: 'category',
          name: "时间",
          nameLocation: "middle",
          nameTextStyle: {
            fontSize: this.isBig ? 24 : 12,
            color: this.theme === 'dark' ? '#fff' : '#000',
          },
          nameGap: this.isBig ? 40 : 20,
          axisLabel: {
            showMinLabel: true,
            showMaxLabel: true,
            fontSize: this.isBig ? 24 : 11,
            color: this.theme === 'dark' ? '#fff' : '#000',
            formatter: function (value, index) {
              return value.substring(0, value.length - formatterNum);
            }
          },
        },
        yAxis: [],
        dataZoom: [
          {
            type: "inside",
          },
        ],
        grid: [
          {
            top: "30%",
            left: "5%",
            right: "5%",
            bottom: "5%",
            bottom: this.isBig ? "20%" : "5%",
            containLabel: true,
          },
        ],
        legend: {},
        series: [
          {
            name: '风向',
            type: 'custom',
            renderItem: renderArrow,
            encode: {
              x: dims.time,
              y: dims.windSpeed
            },
            data: windData,
            z: 10
          },
          {
            name: '风速',
            type: 'line',
            symbol: 'none',
            encode: {
              x: dims.time,
              y: dims.windSpeed
            },
            lineStyle: {
              color: '#aaa',
              type: 'dotted'
            },
            data: windData,
            z: 1
          },
          {
            name: '风向预处理结果',
            type: 'custom',
            renderItem: renderArrow,
            encode: {
              x: dims.time,
              y: dims.windSpeed
            },
            data: windData2,
            z: 10
          },
          {
            name: '风速预处理结果',
            type: 'line',
            symbol: 'none',
            encode: {
              x: dims.time,
              y: dims.windSpeed
            },
            lineStyle: {
              color: 'red',
              type: 'dotted'
            },
            data: windData2,
            z: 1
          },
        ],
        visualMap: [
          {
            type: 'piecewise',
            // show: false,
            orient: 'horizontal',
            left: 'center',
            top: this.isBig ? 160 : 23,
            textStyle: {
              color: this.theme === 'dark' ? '#fff' : '#000', // 根据主题设置文本颜色
              fontSize: this.isBig ? 28 : 12 // 根据屏幕大小设置字体大小
            },
            itemWidth: this.isBig ? 40 : 20,
            itemHeight: this.isBig ? 30 : 14,
            itemGap: this.isBig ? 40 : 10,
            pieces: [
              {
                gte: 17,
                color: '#D33C3E',
                label: '大风（>=17）'
              },
              {
                gte: 11,
                lt: 17,
                color: '#f4e9a3',
                label: '中风（11 ~ 17）'
              },
              {
                lt: 11,
                color: '#18BF12',
                label: '微风（<11）'
              }
            ],
            seriesIndex: 0,
            dimension: 1
          },
          {
            type: 'piecewise',
            // show: false,
            orient: 'horizontal',
            left: 'center',
            top: this.isBig ? 200 : 43,
            textStyle: {
              color: this.theme === 'dark' ? '#fff' : '#000', // 根据主题设置文本颜色
              fontSize: this.isBig ? 28 : 12 // 根据屏幕大小设置字体大小
            },
            itemWidth: this.isBig ? 40 : 20,
            itemHeight: this.isBig ? 30 : 14,
            itemGap: this.isBig ? 40 : 10,
            pieces: [
              {
                gte: 17,
                color: '#D33C3E',
                label: '大风（>=17）预处理'
              },
              {
                gte: 11,
                lt: 17,
                color: '#f4e9a3',
                label: '中风（11 ~ 17）预处理'
              },
              {
                lt: 11,
                color: '#18BF12',
                label: '微风（<11）预处理'
              }
            ],
            seriesIndex: 2,
            dimension: 1
          }
        ],
      };
      // 使得曲线的最大值与最小值顶格
      let minValue = this.getMin(tmpData[0].originData.values); // 输出最小值
      let maxValue = this.getMax(tmpData[0].originData.values); // 输出最大值
      // 一些特殊情况处理
      if (maxValue === 0 && minValue === 0) {
        maxValue = 1
        minValue = -1
      } else {
        let delta = (maxValue - minValue) * 0.2
        if (delta === 0) {
          delta = 1
        }
        maxValue = maxValue + delta
        minValue = minValue - delta
      }
      if (minValue < 0) {
        minValue = 0
      }
      option.yAxis.push({
        name: "风速/" + tmpData[0].unit,
        nameTextStyle: {
          fontWeight: "bold",
          fontSize: this.isBig ? 24 : 12,
          color: this.theme === 'dark' ? '#fff' : '#000',
        },
        axisLabel: {
          fontSize: this.isBig ? 22 : 11,
          color: this.theme === 'dark' ? '#fff' : '#000',
          formatter: function (value) {
            return value.toFixed(tmpData[0].accuracy >= 0 ? tmpData[0].accuracy : 4); // 2表示小数为2位
          },
        },
        splitNumber: 6,
        min: minValue,
        max: maxValue,
        interval: (maxValue - minValue) / 6, // 标轴分割间隔
        splitLine: {
          show: true,
          lineStyle: {
            color: this.theme === 'dark' ? 'rgba(110, 112, 121, 0.50)' : '#eee',
          }
        },
        splitArea: {
          show: false,
        },
      })
      // 若风向数据缺失，则省去箭头
      if (tmpData[1].originData === null) {
        option.series[0] = {
          type: 'line',
        }
        option.series[2] = {
          type: 'line',
        }
      }
      return option
    },
    // 绘制车辆荷载图
    setVehicleLoadChart(data) {
      let axles = {}
      let axlesIndex = -1
      let carWeight = {}
      let carWeightIndex = -1
      for (let i = 0; i < data.length; i++) {
        if (data[i].name === '车型') {
          axles = data[i]
          axlesIndex = i
        } else if (data[i].name === '总重') {
          carWeight = data[i]
          carWeightIndex = i
        }
      }
      if (axlesIndex < 0 || carWeightIndex < 0) {
        // this.setEmptyChart("未选择车型或总重数据")
        return
      }
      let drawData = []
      for (let i = 0; i < 23; i++) {
        drawData.push([])
      }
      carWeight.values.forEach((item, index) => {
        let tmpACar = [carWeight.times[index]]
        for (let i = 0; i < data.length; i++) {
          tmpACar.push(data[i].values[index])
        }
        axles.values[index] = parseInt(axles.values[index])
        if (axles.values[index] < 0 || axles.values[index] > 22) {
          axles.values[index] = 0
        }
        drawData[axles.values[index]].push(tmpACar)
      })
      let dataZoomMinSpan = this.isBig ? 2 : 1;
      let dataZoomMaxSpan = this.isBig ? 20 : 10;

      const renderImg = (param, api) => {
        const point = api.coord([
          api.value(0),
          api.value(carWeightIndex + 1)
        ]);
        let tmpAxles = api.value(axlesIndex + 1)
        return {
          type: 'image',
          style: {
            image: require('@/assets/vehiclePic/' + tmpAxles + '.png'),
            x: -30,
            y: -7.5,
            width: this.isBig ? 120 : 60,
            height: this.isBig ? 30 : 15,
            opacity: 0.8
          },
          position: point,
        };
      };

      let option = {
        toolbox: {
          right: 0,
          top: 0,
          itemSize: this.isBig ? 30 : 12,
          feature: {
            saveAsImage: {
              title: '保存',
              type: 'png',
              backgroundColor: this.theme === 'dark' ? 'rgba(4, 37, 72, 0.8)' : '#fff',
            },
          },
        },
        tooltip: {
          textStyle: {
            align: 'left',
            fontSize: this.isBig ? 24 : 14,
          },
          trigger: "axis",
          confine: true,
          formatter: (params) => {
            params = params[0].data
            let tmpResult = []
            tmpResult.push('时间：' + params[0])
            for (let i = 0; i < data.length; i++) {
              if (i === axlesIndex) {
                tmpResult.push(data[i].name + "：" + vehicleTypeList[params[i + 1]].name + (data[i].unit ? data[i].unit : ''))
              } else {
                tmpResult.push(data[i].name + "：" + params[i + 1] + (data[i].unit ? data[i].unit : ''))
              }
            }
            return tmpResult.join('<br>');
          }
        },
        xAxis: {
          type: 'time',
          name: "时间",
          nameLocation: "middle",
          nameTextStyle: {
            fontSize: this.isBig ? 24 : 12,
            color: this.theme === 'dark' ? '#fff' : '#000',
          },
          nameGap: this.isBig ? 40 : 20,
          axisLabel: {
            formatter: "{yyyy}-{MM}-{dd}\n{hh}:{mm}:{ss}",
            showMinLabel: true,
            showMaxLabel: true,
            fontSize: 11,
            fontSize: this.isBig ? 24 : 11,
            color: this.theme === 'dark' ? '#fff' : '#000',
          },
        },
        yAxis: [],
        dataZoom: [
          {
            type: "inside",
            minSpan: dataZoomMinSpan,
            maxSpan: dataZoomMaxSpan
          },
          {
            type: 'slider',
            xAxisIndex: 0,
            height: this.isBig ? 40 : 20,
            bottom: this.isBig ? 120 : 5,
            minSpan: dataZoomMinSpan,
            maxSpan: dataZoomMaxSpan
          }
        ],
        grid: [
          {
            top: "20%",
            left: "2%",
            right: "5%",
            bottom: this.isBig ? "20%" : "15%",
            containLabel: true,
          },
        ],
        series: [],
        legend: {
          type: 'scroll',
          top: this.isBig ? "10%" : '0',
          itemGap: this.isBig ? 40 : 10,
          data: [],
          itemWidth: this.isBig ? 120 : 60,
          itemHeight: this.isBig ? 30 : 15,
          itemStyle: {
            opacity: 0.8,
          },
          textStyle: {
            color: this.theme === 'dark' ? '#fff' : '#000',
            fontSize: this.isBig ? 25 : 14,
          },
        },
      };
      let tmpDrawData = [];
      for (let i = 0; i < 23; i++) {
        if (drawData[i].length !== 0) {
          tmpDrawData.push({
            type: i,
            value: drawData[i]
          })
        }
      }
      drawData = tmpDrawData;
      //加入多行数据
      for (let i = 0; i < drawData.length; i++) {
        option.series.push({
          type: "custom",
          name: vehicleTypeList[drawData[i].type].name,
          renderItem: renderImg,
          data: drawData[i].value,
          encode: {
            x: 0,
            y: carWeightIndex + 1
          },
        });
        option.legend.data.push({
          name: vehicleTypeList[drawData[i].type].name,
          icon: 'image://' + require('@/assets/vehiclePic/' + drawData[i].type + '.png'),
        })
      }
      option.yAxis = {
        name: carWeight.name + "/" + carWeight.unit,
        nameTextStyle: {
          fontWeight: "bold",
          fontSize: 12,
          fontSize: this.isBig ? 24 : 12,
          color: this.theme === 'dark' ? '#fff' : '#000',
        },
        axisLabel: {
          fontSize: this.isBig ? 22 : 11,
          color: this.theme === 'dark' ? '#fff' : '#000',
          formatter: function (value) {
            return value.toFixed(carWeight.accuracy >= 0 ? carWeight.accuracy : 4); // 2表示小数为2位
          },
        },
        splitLine: {
          show: true,
          lineStyle: {
            color: this.theme === 'dark' ? 'rgba(110, 112, 121, 0.50)' : '#eee',
          }
        },
        splitArea: {
          show: false,
        },
        // splitNumber: 6,
        // min: minValue,
        // max: maxValue,
        // interval: (maxValue - minValue) / 6, // 标轴分割间隔
      }
      return option
    },
    // 判别监测类型 车辆荷载2  风速风向1  其他0
    async monitorTypeJudge(selectedSensor) {
      for (let i = 0; i < selectedSensor.specificMonitorDataType.length; i++) {
        if (selectedSensor.specificMonitorDataType[i] === 2) {
          this.searchForm.granularityNum = 1
          this.searchForm.granularityType = { index: 0, label: '原始' }
          this.searchForm.formatter = "{yyyy}-{MM}-{dd}\n{hh}:{mm}:{ss}";
          this.searchForm.formatterNum = 4;
          this.searchForm.granularityTypeName = '原始'
          this.$emit('changeGranularity', this.searchForm)
          this.$emit('setDisabled', true)
          return 2;
        }
      }
      for (let i = 0; i < selectedSensor.specificMonitorTypeName.length; i++) {
        if (selectedSensor.specificMonitorTypeName[i].indexOf('风') >= 0) {
          return 1;
        }
      }
      return 0;
    },
    //绘制空表 提示信息
    setEmptyChart(msg) {
      let option = {
        title: {
          text: msg,
          left: 'center',
          top: 'center',
          textStyle: {
            fontSize: this.isBig ? 32 : 14,
            color: '#606266',
            fontWeight: 700
          },
        },
      };
      this.option = option
      this.optionKey = new Date().getTime();
    },
    // 公共方法-获取最小值
    getMin(arr) {
      if (arr.length === 0) {
        return 0;
      }
      let tmpValue = arr[0];
      arr.forEach((value, index) => {
        if (tmpValue > value) {
          tmpValue = value
        }
      })
      return tmpValue
    },
    // 公共方法-获取最大值
    getMax(arr) {
      if (arr.length === 0) {
        return 0;
      }
      let tmpValue = arr[0];
      arr.forEach((value, index) => {
        if (tmpValue < value) {
          tmpValue = value
        }
      })
      return tmpValue
    }
  },
  computed: {},
  watch: {
  },
}
</script>

<style lang="scss" scoped>
@import "@/assets/styles/utils.scss";

.empty-box {
  height: 100%;
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: vwpx(40px);
  color: #666
}

.chart {
  width: 100%;
  height: 100%;
}
</style>