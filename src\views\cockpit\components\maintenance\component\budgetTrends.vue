<template>
  <div class="budget-trends">
    <Echarts :option="option" v-if="option" height="100%" key="trendsKey" />
  </div>
</template>

<script>
import * as echarts from "echarts";
import Echarts from '../../echarts/echarts.vue';
import { isBigScreen } from "@/views/cockpit/util/utils";
import { getProjectBudgetStatistics } from "@/api/cockpit/maintain";

export default {
  name: 'budgetTrends',
  components: {
    Echarts
  },
  props: {
    year: {
      type: [String, Number],
      default: new Date().getFullYear(),
    },
  },
  data() {
    return {
      isBig: isBigScreen(),
      option: null,
    }
  },
  mounted() {},
  watch: {
    year:{
      async handler(val) {
        let data = await this.getData(val);
        let xData = data.map(v => v.year)
        let seriesData = data.map(v => v.budgetFund ? v.budgetFund / 10000 : 0)
        seriesData = seriesData.map(value => value.toFixed(2))
        this.option = this.initCharts(xData, seriesData);
      },
      immediate: true,
      deep: true
    }
  },
  methods: {
    async getData(val) {
      return new Promise((resolve, reject) => {
        let year = val || new Date().getFullYear();
        getProjectBudgetStatistics(year).then(res => {
          if (res.code == 200 && res.data) {
            resolve(res.data)
          } else {
            resolve([])
          }
        }).catch(err => {
          reject(err)
        })
      })
    },
    initCharts(xData, seriesData) {
      let option = {
        backgroundColor: 'rgba(255,255,255,0)',
        grid: {
          left: '1%',
          right: '4%',
          top: '10%',
          bottom: '2%',
          containLabel: true
        },
        tooltip: {
          show: true,
          trigger: 'item'
        },
        legend: {
          show: false,
        },
        xAxis: [
          {
            type: 'category',
            boundaryGap: false,
            axisLabel: {
              color: '#fff',
              fontSize: this.isBig ? 24 : '12'
            },
            axisLine: {
              show: true,
              lineStyle: {
                color: 'transparent'
              }
            },
            axisTick: {
              show: false,
            },
            splitLine: {
              show: false,
            },
            data: xData || ['2016', '2017', '2018', '2019', '2020', '2021', '2022', '2023', '2024', '2025']
          }
        ],
        yAxis: [
          {
            type: 'value',
            name: '万元',
            nameTextStyle: {
              fontSize: this.isBig ? 24 : '12',
              color: '#999999',
              padding: [0, 40, this.isBig ? 20 : 0, 0]
            },
            axisLabel: {
              formatter: '{value}',
              fontSize: this.isBig ? 24 : '12',
              textStyle: {
                color: '#999999'
              }
            },
            axisLine: {
              lineStyle: {
                color: '#27b4c2'
              }
            },
            axisTick: {
              show: false,
            },
            splitLine: {
              show: true,
              lineStyle: {
                color: 'rgba(110,112,121,0.5)'
              }
            },
            splitArea: false
          },
        ],
        series: [
          {
            name: '',
            type: 'line',
            stack: '总量',
            symbol: 'circle',
            symbolSize: 8,
            smooth: true, // 平滑曲线
            barWidth: this.isBig ? 16 : 8,
            itemStyle: {
              normal: {
                color: '#1CFFBC',
                lineStyle: {
                  color: "#1CFFBC",
                  width: 1
                },
                areaStyle: {
                  color: new echarts.graphic.LinearGradient(0, 1, 0, 0, [{
                    offset: 0,
                    color: 'rgba(28,255,188,0.1)'
                  }, {
                    offset: 1,
                    color: 'rgba(28,255,188,0.6)'
                  }]),
                }
              }
            },
            markPoint: {
              itemStyle: {
                normal: {
                  color: 'red'
                }
              }
            },
            data: seriesData || [14, 19, 16, 23, 21, 22, 16, 17, 18, 17]
          },
        ]
      };
      return option;
    }
  }
}
</script>

<style lang="scss" scoped>
.budget-trends {
  width: 100%;
  height: 100%;
  padding: 5px;
}
</style>