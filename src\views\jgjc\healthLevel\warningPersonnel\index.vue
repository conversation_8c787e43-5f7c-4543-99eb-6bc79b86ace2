<template>
  <div class='app-container'>
    <el-row :gutter='20'>
      <!--部门数据-->
      <el-col :span='relaNav ? 4 : 0' :xs='24' class='leftDiv'>
        <!--折叠图标-->
        <div class='leftIcon' @click='relaNav = false'>
          <span class='el-icon-caret-left'></span>
        </div>
        <div class='head-container' style='width: 300px'>
          <el-tree
            ref='tree'
            :data='filteredTreeData'
            :default-expanded-keys='[1]'
            :expand-on-click-node='false'
            :props='defaultProps'
            highlight-current
            node-key='code'
            @node-click='handleNodeClick'
          >
          </el-tree>
        </div>
      </el-col>
      <!--角色数据-->
      <el-col :span='relaNav ? 20 : 24' :xs='24'>
        <!--展开图标-->
        <div v-show='!relaNav' class='rightIcon' @click='relaNav = true'>
          <span class='el-icon-caret-right'></span>
        </div>
        <el-row :gutter='10' class='mb8'>
          <el-col :span='1.5'>
            <el-button
              icon='el-icon-plus'
              size='mini'
              type='primary'
              @click='handleAdd'
            >新增
            </el-button>
          </el-col>
          <el-col :span='1.5'>
            <el-button
              icon='el-icon-delete'
              size='mini'
              type='danger'
              @click='handleDelete'
            >删除
            </el-button>
          </el-col>
        </el-row>
        <!-- 筛选区结束 -->
        <!-- 数据表格开始 -->
        <div class='tableDiv'>
          <el-table v-adjust-table v-loading='loading' :data='dataList'
                    :height="'calc(100vh - 260px)'" border size='mini'
                    @selection-change='handleSelectionChange'
                    style='width: 100%'>
            <el-table-column align='center' fixed='left' type='selection' width='55'></el-table-column>
            <el-table-column align='center' fixed label='序号' type='index' width='100'></el-table-column>
            <el-table-column align='center' label='用户名' prop='userName'></el-table-column>
            <el-table-column align='center' label='联系电话' prop='userTelephone'></el-table-column>
            <el-table-column align='center' label='发送预警' prop='openFlag'>
              <template slot-scope='scope'>
                <el-switch
                  v-model='scope.row.openFlag'
                  :active-value='true'
                  :inactive-value='false'
                  @change='handleSwitchChange(scope.row)'
                />
              </template>
            </el-table-column>
            <el-table-column align='center' label='处理前发送通知' prop='sendBeforeDealFlag'>
              <template slot-scope='scope'>
                <el-switch
                  v-model='scope.row.sendBeforeDealFlag'
                  :active-value='true'
                  :inactive-value='false'
                  @change='handleSwitchChange(scope.row)'
                />
              </template>
            </el-table-column>
            <el-table-column align='center' label='处理后发送通知' prop='sendAfterDealFlag'>
              <template slot-scope='scope'>
                <el-switch
                  v-model='scope.row.sendAfterDealFlag'
                  :active-value='true'
                  :inactive-value='false'
                  @change='handleSwitchChange(scope.row)'
                />
              </template>
            </el-table-column>
            <el-table-column align='center' label='是否发送一级预警' prop='oneFlag'>
              <template slot-scope='scope'>
                <el-switch
                  v-model='scope.row.oneFlag'
                  :active-value='true'
                  :inactive-value='false'
                  @change='handleSwitchChange(scope.row)'
                />
              </template>
            </el-table-column>
            <el-table-column align='center' label='是否发送二级预警' prop='twoFlag'>
              <template slot-scope='scope'>
                <el-switch
                  v-model='scope.row.twoFlag'
                  :active-value='true'
                  :inactive-value='false'
                  @change='handleSwitchChange(scope.row)'
                />
              </template>
            </el-table-column>
            <el-table-column align='center' label='是否发送三级预警' prop='threeFlag'>
              <template slot-scope='scope'>
                <el-switch
                  v-model='scope.row.threeFlag'
                  :active-value='true'
                  :inactive-value='false'
                  @change='handleSwitchChange(scope.row)'
                />
              </template>
            </el-table-column>
          </el-table>
          <pagination
            v-show='total > 0'
            :limit.sync='queryParams.pageSize'
            :page.sync='queryParams.pageNum'
            :total='total'
            @pagination='handleQuery'
          />
        </div>
        <!-- 数据表格结束 -->
      </el-col>
    </el-row>
    <el-dialog
      :title='dialogTitle'
      :visible.sync='dialogVisible'
      append-to-body
      destroy-on-close
      width='80%'
    >
      <el-row :gutter='20'>
        <!--部门数据-->
        <el-col :span='5' :xs='24' class='leftDiv' style='height: 600px;min-height: auto;'>
          <div class='head-container' style='width: fit-content'>
            <el-tree
              :data='deptOptions'
              :props='defaultProps'
              :expand-on-click-node='false'
              ref='tree'
              :default-expanded-keys='[1]'
              node-key='id'
              highlight-current
              @node-click='handleUserNodeClick'
            />
          </div>
        </el-col>
        <!--用户数据-->
        <el-col :span='19'>
          <el-form ref='userQueryParams' :model='userQueryParams' :inline='true' label-width='100px' size='mini'>
            <el-form-item label='用户名称'>
              <el-input v-model='userQueryParams.userName' placeholder='请输入用户名称' clearable />
            </el-form-item>
            <el-form-item>
              <el-button type='primary' @click='getList'>查询</el-button>
            </el-form-item>
          </el-form>
          <div class='tableDiv'>
            <el-table v-adjust-table size='mini' height='500px' style='width: 100%' v-loading='loading' border
                      :data='userList' @selection-change='handleUserSelectionChange'
                      ref='table'>
              <el-table-column type='selection' width='50' align='center' />
              <el-table-column fixed label='序号' type='index' width='50'>
                <template v-slot='scope'>
                  {{ (scope.$index + (userQueryParams.pageNum - 1) * userQueryParams.pageSize) + 1 }}
                </template>
              </el-table-column>
              <el-table-column fixed label='用户名称' align='center' key='userName' prop='userName'
                               :show-overflow-tooltip='true' />
              <el-table-column label='用户昵称' align='center' key='nickName' prop='nickName'
                               :show-overflow-tooltip='true' />
              <el-table-column label='手机号码' align='center' key='phonenumber' prop='phonenumber' />
            </el-table>

            <pagination
              v-show='userTotal>0'
              :total='userTotal'
              :page.sync='userQueryParams.pageNum'
              :limit.sync='userQueryParams.pageSize'
              @pagination='getList'
            />
          </div>
        </el-col>
      </el-row>
      <div style='text-align: right'>
        <el-button @click='closeDialog'>取消</el-button>
        <el-button type='primary' @click='handleConfirm'>确定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getJCDomainTree } from '@/api/jgjc/baseInfo/alarmLog'
import {
  getWarningUserPage,
  deleteWarningUser,
  updateWarningUser,
  saveWarningUser,
} from '@/api/jgjc/healthLevel/warningPersonnel'
import { deptTreeSelect, listUser } from '@/api/system/user'

export default {
  name: 'ContentManagement',
  data() {
    return {
      // 遮罩层
      loading: false,
      // 左侧组织树
      relaNav: true,
      relaOptions: [],
      filteredTreeData: [],
      defaultProps: {
        children: 'children',
        label: 'label',
      },
      loadedKeys: new Set(), // 记录已加载过的节点key，避免重复加载
      // 总条数
      total: 0,
      dataList: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
      },
      ids: [],
      formData: {},
      dialogTitle: '新增',
      dialogVisible: false,
      rules: {
        number: [
          { required: true, message: '请输入流量卡号', trigger: 'blur' },
        ],
        installation: [
          { required: true, message: '请输入安装位置', trigger: 'blur' },
        ],
      },
      structureId: '',
      deptOptions: [],
      userQueryParams: {
        pageNum: 1,
        pageSize: 10,
      },
      userList: [],
      userTotal: 0,
      checkUserList: [],
    }
  },
  created() {
    this.getDeptTree()
    this.getJCDeptTree()
  },
  methods: {
    /** 查询部门下拉树结构 */
    getDeptTree() {
      deptTreeSelect().then(response => {
        this.deptOptions = response.data
      })
    },
    // 节点单击事件
    handleUserNodeClick(data) {
      this.userQueryParams.deptId = data.id
      this.getList()
    },
    getList() {
      this.loading = true
      listUser(this.userQueryParams).then(response => {
          this.userList = response.rows
          this.userTotal = response.total
          this.loading = false
        },
      )
    },
    // 查询部门下拉树结构
    getJCDeptTree() {
      getJCDomainTree({}).then(response => {
        this.relaOptions = response.data
        this.filteredTreeData = [...this.relaOptions]
      })
    },
    // 树节点点击事件
    handleNodeClick(nodeData, node) {
      if (node.level == 4) {
        this.structureId = nodeData.structureId
        this.queryParams = {
          pageNum: 1,
          pageSize: 10,
          sensorOrStructureId: nodeData.structureId,
        }
        this.handleQuery()
      }
    },
    handleQuery() {
      this.loading = true
      getWarningUserPage(this.queryParams).then(res => {
        this.dataList = res.rows
        this.total = res.total
        this.loading = false
      }).catch(err => {
        this.loading = false
        console.error(err)
      })
    },
    resetQuery() {
      this.queryParams = {
        pageNum: 1,
        pageSize: 10,
        title: '',
      }
      this.handleQuery()
    },
    handleSelectionChange(e) {
      this.ids = e.map(item => item.id)
    },
    handleUserSelectionChange(e) {
      this.checkUserList = e
    },
    assigneeChange(value) {
      console.log(this.$refs.assigneeRef.getCheckedNodes()[0])
    },
    handleAdd() {
      if (!this.structureId) return this.$message.error('请选择结构物')
      this.dialogTitle = '新增'
      this.formData = {
        sensorOrStructureId: this.structureId,
      }
      this.checkUserList = []
      this.dialogVisible = true
      this.getList()
    },
    handleSwitchChange(row) {
      this.dialogTitle = '修改'
      this.loading = true
      updateWarningUser(row).then(res => {
        if (res.code === 200) {
          this.handleQuery()
          this.closeDialog()
        } else {
          this.$message.error(res.msg)
        }
      }).finally(() => {
        this.loading = false
      })
    },
    async handleConfirm() {
      this.loading = true
      for (let i = 0; i < this.checkUserList.length; i++) {
        const item = this.checkUserList[i]
        const formData = {
          ...this.formData,
          openFlag: false,
          oneFlag: false,
          twoFlag: false,
          threeFlag: false,
          sendBeforeDealFlag: false,
          sendAfterDealFlag: false,
          userId: item.id,
          userName: item.nickName,
          userTelephone: item.phonenumber || ''
        }
        const res = await saveWarningUser(formData)
        if (res.code != 200) {
          this.$message.error(res.msg)
          return
        }
      }
      this.loading = false
      this.$message.success('保存成功')
      this.handleQuery()
      this.closeDialog()
    },
    closeDialog() {
      this.dialogVisible = false
      this.formData = {}
    },
    handleDelete() {
      if (this.ids.length <= 0) {
        this.$message.warning('请勾选需要删除的数据')
        return
      }
      this.$confirm('是否确认删除选中的数据？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }).then(() => {
        this.loading = true
        deleteWarningUser({ ids: this.ids }).then(res => {
          if (res.code === 200) {
            this.$message.success('删除成功')
            this.handleQuery()
          } else {
            this.$message.error(res.msg)
          }
          this.loading = false
        }).catch(err => {
          this.loading = false
          console.error(err)
        })
      })
    },
  },
}
</script>

<style scoped>
.leftDiv {
  border-right: 1px solid #d8dce5;
  min-height: calc(100vh - 100px);
  overflow-y: auto;
  height: calc(80vh - 150px);
  position: relative;
  top: -20px;
  padding-top: 10px;
  background-color: white;
}

.leftIcon {
  border: 1px solid #dcdfe6;
  border-radius: 8px;
  width: 16px;
  height: 50px;
  line-height: 50px;
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
  position: absolute;
  right: 0;
  top: 300px;
  z-index: 2;
}

.leftIcon:hover {
  background-color: #dcdfe6;
}

.rightIcon {
  border: 1px solid #dcdfe6;
  border-radius: 8px;
  width: 16px;
  height: 50px;
  line-height: 50px;
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
  position: absolute;
  left: -10px;
  top: 280px;
  z-index: 10;
  background: white;
}

.rightIcon:hover {
  background-color: #dcdfe6;
}

.tableDiv {
  margin-top: 20px;
}
</style>
<style lang='scss' scoped>
@import "@/assets/styles/business.scss";
</style>
