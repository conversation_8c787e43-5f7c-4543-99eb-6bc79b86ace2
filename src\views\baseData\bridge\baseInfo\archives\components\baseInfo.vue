<template>
  <div
    v-loading="loading"
    style="border-radius: 10px; box-shadow: 0px 0px 10px 0px rgba(0, 0, 0, 0.2)"
    :class="oneMap ? 'one-map' : 'default'"
  >
    <el-tabs v-model="activeName" type="card">
      <el-tab-pane label="行政识别数据" name="administrative">
        <div
          style="
            height: calc(100vh - 167px);
            overflow-y: auto;
            padding: 0 10px 0 5px;
          "
        >
          <el-form
            ref="administrative"
            :model="form"
            label-width="200px"
            disabled
          >
            <div style="display: flex; flex-wrap: wrap">
              <ManageSelectTree placeholder="请选择" :formObject="form" />
              <el-col
                v-for="(item, index) in administrativeFields"
                :key="index"
                :span="item.span ? item.span : 12"
              >
                <el-form-item
                  :label="item.label"
                  :prop="item.prop"
                  :rules="item.rules"
                >
                  <span v-if="item.type === 'input'">
                    <el-input
                      v-model="form[item.prop]"
                      :placeholder="item.placeholder"
                    />
                  </span>
                  <span v-else-if="item.type === 'pileInput'">
                    <PileInput v-model="form[item.prop]" />
                  </span>
                  <span v-else-if="item.type === 'inputNumber'">
                    <el-input-number
                      v-model="form[item.prop]"
                      style="width: 100%"
                      :precision="item.precision"
                    />
                  </span>
                  <span v-else-if="item.type === 'select'">
                    <el-select
                      v-model="form[item.prop]"
                      style="width: 100%"
                      :placeholder="item.placeholder"
                    >
                      <el-option
                        v-for="v in item.options"
                        :key="v[item.optionValue]"
                        :label="v[item.optionLabel]"
                        :value="v[item.optionValue]"
                      />
                    </el-select>
                  </span>
                  <span v-else-if="item.type === 'selectTree'">
                    <SelectTree
                      v-model="form[item.prop]"
                      :dept-type="item.deptType"
                      placeholder="请选择"
                    />
                  </span>
                  <span v-else-if="item.type === 'coordinate'">
                    <lon-lat
                      :type="item.prepend"
                      :lon.sync="form[item.propLon]"
                      :lat.sync="form[item.propLat]"
                    />
                  </span>
                  <span v-else-if="item.type === 'dictSelect'">
                    <el-select
                      v-model="form[item.prop]"
                      style="width: 100%"
                      placeholder="请选择"
                    >
                      <el-option
                        v-for="i in dict.type[item.dict]"
                        :key="i.value"
                        :label="i.label"
                        :value="i.value"
                      />
                    </el-select>
                  </span>

                  <span v-else-if="item.type === 'multiDictSelect'">
                      <MultiDictSelect
                        v-model="form[item.prop]"
                        :disabled="item.disabledFieds"
                        :multiple="item.multiple"
                        :options="dict.type[item.dict]"/>
                    </span>

                  <span v-else-if="item.type === 'CascaderRegion'">
                    <CascaderRegion
                      v-model="form[item.prop]"
                      :deep="1"
                      @input="
                        (value) => {
                          form[item.prop] = value;
                        }
                      "
                    />
                  </span>
                  <span
                    v-else-if="item.type === 'date' || item.type === 'year'"
                  >
                    <el-date-picker
                      v-model="form[item.prop]"
                      style="width: 100%"
                      :type="item.type"
                      :placeholder="item.placeholder"
                      clearable
                      :value-format="
                        item.type === 'year' ? 'yyyy' : 'yyyy-MM-dd'
                      "
                    />
                  </span>
                  <span v-else-if="item.type === 'tree'">
                    <select-tree
                      style="width: 100%"
                      v-model="form[item.prop]"
                    />
                  </span>
                  <span v-else-if="item.type === 'coordinate'">
                    <el-input
                      v-model="form[item.prop]"
                      type="number"
                      :placeholder="item.placeholder"
                    >
                      <template slot="prepend">{{ item.prepend }}</template>
                    </el-input>
                  </span>
                  <span v-else-if="item.type === 'roadType'">
                    <SectionSelect
                      style="pointer-events: none"
                      v-model="form[item.prop]"
                      :formObject="form"
                      :sectionId="form.maintenanceSectionId"
                      :disabled="!form.maintenanceSectionId"
                    />
                  </span>
                </el-form-item>
              </el-col>
            </div>
          </el-form>
        </div>
      </el-tab-pane>
      <el-tab-pane label="结构技术数据" name="technology">
        <div
          style="
            height: calc(100vh - 167px);
            overflow-y: auto;
            padding: 0 10px 0 5px;
          "
        >
          <el-form ref="technology" :model="form" label-width="200px" disabled>
            <div style="display: flex; flex-wrap: wrap">
              <el-col
                v-for="(item, index) in technologyFields"
                :key="index"
                :span="12"
              >
                <el-form-item
                  :label="item.label"
                  :prop="item.prop"
                  :rules="item.rules"
                >
                  <span v-if="item.type === 'input'">
                    <el-input
                      v-model="form[item.prop]"
                      :placeholder="item.placeholder"
                    />
                  </span>
                  <span v-else-if="item.type === 'inputNumber'">
                    <el-input-number
                      v-model="form[item.prop]"
                      style="width: 100%"
                      :precision="item.precision"
                    />
                  </span>
                  <span v-else-if="item.type === 'dictSelect'">
                    <el-select
                      v-model="form[item.prop]"
                      style="width: 100%"
                      placeholder="请选择"
                    >
                      <el-option
                        v-for="i in dict.type[item.dict]"
                        :key="i.value"
                        :label="i.label"
                        :value="i.value"
                      />
                    </el-select>
                  </span>
                </el-form-item>
              </el-col>
            </div>
          </el-form>
        </div>
      </el-tab-pane>
      <el-tab-pane label="结构信息数据" name="information">
        <div
          style="
            height: calc(100vh - 167px);
            overflow-y: auto;
            padding: 0 10px 0 5px;
          "
        >
          <el-form ref="information" :model="form" label-width="200px" disabled>
            <div style="display: flex; flex-wrap: wrap">
              <el-col
                v-for="(item, index) in informationFields"
                :key="index"
                :span="12"
              >
                <el-form-item
                  :label="item.label"
                  :prop="item.prop"
                  :rules="item.rules"
                >
                  <span v-if="item.type === 'input'">
                    <el-input
                      v-model="form[item.prop]"
                      :placeholder="item.placeholder"
                    />
                  </span>
                  <span v-else-if="item.type === 'inputNumber'">
                    <el-input-number
                      v-model="form[item.prop]"
                      style="width: 100%"
                      :precision="item.precision"
                    />
                  </span>
                  <span v-else-if="item.type === 'dictSelect'">
                    <el-select
                      v-model="form[item.prop]"
                      style="width: 100%"
                      placeholder="请选择"
                      :multiple="item.multiple"
                    >
                      <el-option
                        v-for="i in dict.type[item.dict]"
                        :key="i.value"
                        :label="i.label"
                        :value="i.value"
                      />
                    </el-select>
                  </span>
                </el-form-item>
              </el-col>
            </div>
          </el-form>
        </div>
      </el-tab-pane>
      <el-tab-pane label="桥梁照片" name="images">
        <div
          style="
            height: calc(100vh - 167px);
            overflow-y: auto;
            padding: 0 10px 0 5px;
          "
        >
          <el-form ref="images" :model="form" label-width="100px">
            <el-col
              v-for="(item, index) in imagesFields"
              :key="index"
              :span="8"
            >
              <el-form-item :label="item.label" :prop="item.prop">
                <!-- <ImagePreview
                  v-if="form[item.prop]"
                  :owner-id="form[item.prop]"
                  width="146px"
                  height="146px"
                /> -->
                <ImageUpload
                     v-if="form[item.prop]"
                    :key="item.ownerId"
                    v-model="form[item.prop]"
                    :limit="1"
                    :disabled="true"
                    :owner-id="item.ownerId"
                    storage-path="/base/bridge/baseInfo"
                  />
                <span v-else> 无 </span>
              </el-form-item>
            </el-col>
          </el-form>
        </div>
      </el-tab-pane>
      <el-tab-pane label="档案资料" name="archives">
        <!--        <div style="height: calc(100vh - 167px);overflow-y: auto;padding: 0 10px 0 5px;">-->
        <!--          <el-form-->
        <!--            ref="archives"-->
        <!--            :model="form"-->
        <!--            label-width="220px"-->
        <!--          >-->
        <!--            <el-col-->
        <!--              v-for="(item,index) in archivesFields"-->
        <!--              :key="index"-->
        <!--              :span="12"-->
        <!--            >-->
        <!--              <el-form-item-->
        <!--                :label="item.label"-->
        <!--                :prop="item.prop"-->
        <!--              >-->
        <!--                <FileUpload-->
<!--        platform="mpkj"-->
        <!--                  v-if="form[item.prop]"-->
        <!--                  v-model="form[item.prop]"-->
        <!--                  :forView="true"-->
        <!--                />-->
        <!--                <span v-else>-->
        <!--                  无-->
        <!--                </span>-->
        <!--              </el-form-item>-->
        <!--            </el-col>-->
        <!--          </el-form>-->
        <!--        </div>-->

        <div
          style="
            height: calc(100vh - 167px);
            overflow-y: auto;
            padding: 0 10px 0 5px;
          "
        >
          <el-form ref="archives" :model="form" label-width="190px" disabled>
            <div style="display: flex; flex-wrap: wrap">
              <el-col
                v-for="(item, index) in archivesFields"
                :key="index"
                :span="12"
              >
                <el-form-item
                  :label="item.label"
                  :prop="item.prop"
                  :rules="item.rules"
                >
                  <span v-if="item.type === 'input'">
                    <el-input
                      v-model="form[item.prop]"
                      :placeholder="item.placeholder"
                      clearable
                    />
                  </span>

                  <span v-else-if="item.type === 'dictSelect'">
                    <el-select
                      v-model="form[item.prop]"
                      style="width: 100%"
                      placeholder="请选择"
                      clearable
                    >
                      <el-option
                        v-for="i in dict.type[item.dict]"
                        :key="i.value"
                        :label="i.label"
                        :value="i.value"
                      />
                    </el-select>
                  </span>
                  <span
                    v-else-if="item.type === 'date' || item.type === 'year'"
                  >
                    <el-date-picker
                      v-model="form[item.prop]"
                      style="width: 100%"
                      :type="item.type"
                      :placeholder="item.placeholder"
                      clearable
                      :value-format="
                        item.type === 'year' ? 'yyyy' : 'yyyy-MM-dd'
                      "
                    />
                  </span>
                </el-form-item>
              </el-col>
            </div>
          </el-form>
        </div>
      </el-tab-pane>
      <el-tab-pane label="涉水信息" name="wade">
          <div style="height: calc(100vh - 167px); overflow-y: auto; padding: 0 10px 0 5px">
            <el-form
              ref="wade"
              :model="form"
              label-width="225px"
              disabled
            >
              <div style="display: flex; flex-wrap: wrap">
                <el-col
                  v-for="(item, index) in wadeFields"
                  :key="index"
                  :span="12"
                >
                  <el-form-item
                    :label="item.label"
                    :prop="item.prop"
                    :rules="item.rules"
                  >
                    <span v-if="item.type === 'input'">
                      <el-input
                        v-model="form[item.prop]"
                        :placeholder="item.placeholder"
                        clearable
                      />
                    </span>
                    <span v-else-if="item.type === 'dictSelect'">
                      <el-select
                        v-model="form[item.prop]"
                        style="width: 100%"
                        placeholder="请选择"
                        clearable
                      >
                        <el-option
                          v-for="i in dict.type[item.dict]"
                          :key="i.value"
                          :label="i.label"
                          :value="i.value"
                        />
                      </el-select>
                    </span>
                    <span v-else-if="item.type === 'date' || item.type === 'year'" >
                      <el-date-picker
                        v-model="form[item.prop]"
                        style="width: 100%"
                        :type="item.type"
                        :placeholder="item.placeholder"
                        :value-format="item.type === 'year' ? 'yyyy' : 'yyyy-MM-dd'"
                      />
                    </span>
                  </el-form-item>
                </el-col>
              </div>
            </el-form>
          </div>
        </el-tab-pane>
      <el-tab-pane label="其他数据" name="other">
        <div
          style="
            height: calc(100vh - 167px);
            overflow-y: auto;
            padding: 0 10px 0 5px;
          "
        >
          <el-form
            ref="administrative"
            :model="form"
            label-width="190px"
            disabled
          >
            <div style="display: flex; flex-wrap: wrap">
              <el-col
                v-for="(item, index) in otherFields"
                :key="index"
                :span="item.span ? item.span : 12"
              >
                <el-form-item
                  :label="item.label"
                  :prop="item.prop"
                  :rules="item.rules"
                >
                  <span v-if="item.type === 'input'">
                    <el-input
                      v-model="form[item.prop]"
                      :placeholder="item.placeholder"
                      clearable
                    />
                  </span>
                  <span v-else-if="item.type === 'inputTextarea'">
                    <el-input
                      v-model="form[item.prop]"
                      autosize
                      :placeholder="item.placeholder"
                      type="textarea"
                      clearable
                    />
                  </span>
                  <span v-else-if="item.type === 'pileInput'">
                    <PileInput v-model="form[item.prop]" />
                  </span>
                  <span v-else-if="item.type === 'inputNumber'">
                    <el-input-number
                      v-model="form[item.prop]"
                      style="width: 100%"
                      :precision="item.precision"
                      clearable
                    />
                  </span>
                  <span v-else-if="item.type === 'dictSelect'">
                    <el-select
                      v-model="form[item.prop]"
                      style="width: 100%"
                      placeholder="请选择"
                      clearable
                    >
                      <el-option
                        v-for="i in dict.type[item.dict]"
                        :key="i.value"
                        :label="i.label"
                        :value="i.value"
                      />
                    </el-select>
                  </span>
                  <span
                    v-else-if="item.type === 'date' || item.type === 'year'"
                  >
                    <el-date-picker
                      v-model="form[item.prop]"
                      style="width: 100%"
                      :type="item.type"
                      :placeholder="item.placeholder"
                      clearable
                      :value-format="
                        item.type === 'year' ? 'yyyy' : 'yyyy-MM-dd'
                      "
                    />
                  </span>
                </el-form-item>
              </el-col>
            </div>
          </el-form>
        </div>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
import formFields from "../../js/formFields";
import dicts from "../../js/dicts.js";
import { getStatic } from "@/api/baseData/bridge/baseInfo/index";
import { getHisStatic } from "@/api/baseData/bridge/history/index";
import ManageSelectTree from "@/components/manageSelectTree/index.vue";
import { listMaintenanceSectionAll } from "@/api/system/maintenanceSection";
import { listByMaintenanceSectionId } from "@/api/baseData/common/routeLine";
import PileInput from "@/components/PileInput/index.vue";
import SelectTree from "@/components/DeptTmpl/selectTree";
import SectionSelect from "@/components/SectionSelect";
import CascaderRegion from "@/views/baseData/components/CascaderRegion/index.vue";
import lonLat from "@/components/mapPosition/lonLat.vue";
import MultiDictSelect from "@/views/baseData/components/MultiDictSelect/index.vue";

export default {
  name: "ArchivesBaseInfo",
  components: {
    MultiDictSelect,
    lonLat,
    CascaderRegion,
    PileInput,
    SelectTree,
    SectionSelect,
    ManageSelectTree,
  },
  inject: ["oneMap"],
  dicts: dicts,
  props: {
    id: {
      type: String || Number,
      default: "",
    },
  },
  data() {
    return {
      loading: false,
      activeName: "administrative",
      administrativeFields: [],
      technologyFields: [],
      informationFields: [],
      imagesFields: [],
      archivesFields: [],
      otherFields: [],
      wadeFields: [],
      form: {
        longitude: "",
        latitude: "",
      },
      routeOptions: [],
    };
  },
  watch: {
    "form.managementMaintenanceId"(newVal, oldVal) {
      if (newVal) {
        this.deptChange(newVal);
      }
    },
    "form.maintenanceSectionId"(newVal, oldVal) {
      if (newVal) {
        this.maintenanceSectionChange(newVal);
      }
      if (!newVal && this.form.routeCode) {
        this.form.routeCode = "";
        this.form.routeName = "";
      }
    },
  },
  created() {
    this.init();
  },
  methods: {
    async init() {
      this.loading = true;
      await this.getFormData();
      await this.initFormSlections();
      this.loading = false;
    },
    // 获取表单字段以及表单选项列表
    async initFormSlections() {
      this.administrativeFields = JSON.parse(
        JSON.stringify(formFields.administrative)
      );
      this.technologyFields = JSON.parse(JSON.stringify(formFields.technology));
      this.informationFields = JSON.parse(
        JSON.stringify(formFields.information)
      );
      this.imagesFields = JSON.parse(JSON.stringify(formFields.images));
      this.archivesFields = JSON.parse(JSON.stringify(formFields.archives));
      this.otherFields = JSON.parse(JSON.stringify(formFields.other));
      this.wadeFields = JSON.parse(JSON.stringify(formFields.wade));
    },
    // 获取表单数据
    async getFormData() {
      let api = this.$route.query.type == "history" ? getHisStatic : getStatic;

      const res = await api(this.id);
      if (res.code === 200) {
        res.data.pierType = res.data.pierType
          ? res.data.pierType.split(",")
          : [];
        res.data.abutmentType = res.data.abutmentType
          ? res.data.abutmentType.split(",")
          : "";
        res.data.bearingType = res.data.bearingType
          ? res.data.bearingType.split(",")
          : "";
        res.data.expansionJointType = res.data.expansionJointType
          ? res.data.expansionJointType.split(",")
          : "";
        // 让值为0或者null的字段都统一变成undefined，这样回显el-number-input就不会自动变成0了
        for (let key in res.data) {
          if (res.data[key] === null) {
            res.data[key] = undefined;
          }
        }
        this.form = res.data;
        this.$emit('getBaseInfo', this.form)
      }
    },
    // 监听选中管理处
    deptChange(e) {
      if (!e) return;
      listMaintenanceSectionAll({ departmentId: e }).then((res) => {
        if (res.code == 200) {
          this.administrativeFields.forEach((el) => {
            if (el.prop === "maintenanceSectionId") {
              el.options = res.data;
            }
          });
        }
      });
    },
    // 监听选中养护路段
    maintenanceSectionChange(e) {
      if (!e) return;
      listByMaintenanceSectionId({ maintenanceSectionId: e }).then((res) => {
        if (res.code == 200) {
          this.administrativeFields.forEach((el) => {
            if (el.prop === "routeCode" || el.prop === "routeName") {
              el.options = res.data;
            }
          });
        }
      });
    },
  },
};
</script>

<style lang="scss" scoped>
@import "@/assets/styles/common.scss";
.default {
  @import "~@/assets/styles/el-tabs.scss";
  ::v-deep {
    .el-tabs__header {
      padding-left: 10px;
      border: 0;
    }
    .el-tabs__content {
      border: 0;
    }
    .el-form-item__label {
      font-family: Microsoft YaHei, Microsoft YaHei;
      font-weight: 400;
      font-size: 14px;
      color: #333333;
    }
    .el-input.is-disabled .el-input__inner {
      background-color: white;
      border-color: #dfe4ed;
      color: #1d2129;
    }
    .el-textarea.is-disabled .el-textarea__inner {
      background-color: white;
      border-color: #dfe4ed;
      color: #1d2129;
    }
  }
}
</style>
