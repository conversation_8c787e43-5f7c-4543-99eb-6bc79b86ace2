<template>
    <div class="route-line" v-loading="loading">
        <el-select filterable :disabled="disabled" style="width: 100%;" :size="size" clearable v-model="selectValue" :placeholder="placeholder" :multiple="multiple">
            <el-option v-for="item in routeOptions" :key="item.routeId" :label="item.routeName"
                :value="item.routeId"></el-option>
        </el-select>
    </div>
</template>

<script>
import { listAllRoute } from '@/api/system/route.js'
export default { // 路线选择器
    name: 'RouteLine',
    components: {},
    props: {
        value: {
            type: [String, Array,Number],
            default: ''
        },
        placeholder: {
            type: String,
            default: '请选择路段'
        },
        size: {
            type: String,
            default: 'medium'
        },
        disabled: {
            type: Boolean,
            default: false
        },
        multiple: {
            type: Boolean,
            default: false
        }
    },
    data() {
        return {
            loading: false,
            routeOptions: []
        }
    },
    watch: {},
    computed: {
        selectValue: {
            get: function () {
                return this.value
            },
            set: function(val) {
                this.$emit('input', val)
            }
        }
    },
    created() {
        this.getOptions()
    },
    mounted() { },
    methods: {
        getOptions() {
            this.loading = true
            listAllRoute().then(res => {
                if (res.code === 200) {
                    this.routeOptions = res.data
                }
            }).finally(() => {
                this.loading = false
            })
        },
    },
}
</script>

<style lang="scss" scoped>
.route-line {}
</style>