<template>
  <div class="app-container">
    <!-- 查询 -->
    <div class="searchBox" :style="{ 'height': queryShow ? '86px' : '48px' }">
      <el-row :gutter="20" style="width: 600px;">
        <el-col :span="8" :offset="0">
          <el-select
            v-model="propertyUnitType"
            clearable
            ref="stlectTree"
            class="custom-select"
            placeholder="产权权属单位类型"
            collapse-tags
          >
            <el-option
              value="集团公司"
              label="集团公司"
            >
            </el-option>
            <el-option
              value="项目公司"
              label="项目公司"
            >
            </el-option>
          </el-select>
        </el-col>
        <el-col :span="8" :offset="0">
          <el-select
            v-model="managementMaintenanceIds"
            multiple
            clearable
            ref="stlectTree"
            class="custom-select"
            placeholder="管理处"
            @clear="selectClear"
            @remove-tag="removeTag"
            collapse-tags
          >
            <el-option
              hidden
              v-for="item in deptOptions"
              :key="item.id"
              :value="item.id"
              :label="item.label"
            >
            </el-option>
            <el-tree
              style="font-weight:400;"
              :data="deptOptions"
              :props="defaultProps"
              @check="handleNodeCheck"
              @node-click="nodeClick"
              node-key="id"
              check-on-click-node
              ref="treeRef"
              show-checkbox
            ></el-tree>
          </el-select>
        </el-col>
        <el-col :span="8" :offset="0">
          <el-button type="primary" icon="el-icon-search" size="mini" @click="queryhandle">搜索</el-button>
          <el-button icon="el-icon-refresh" size="mini" @click="queryReset">重置</el-button>
        </el-col>
      </el-row>
    </div>
    <!-- 主表数据 -->
    <div class="tableDiv" :style="{ 'height': queryShow ? 'calc(100% - 96px)' : 'calc(100% - 58px)',paddingTop:'10px' }">
      <iframe :src="src" v-if="showIframe" id="reportView" frameborder="no" style="width: 100%;height: 100%" scrolling="auto" />
    </div>
  </div>
</template>
<script>
// 导入相关
import {getToken} from "@/utils/auth";

import '@riophae/vue-treeselect/dist/vue-treeselect.css'
// API
import {deptTreeSelect} from '@/api/tmpl' // 管养单位

export default {
  name: "RiskLedgerSummary",
  // dicts: [
  //   'disaster_report_id_conf',  // 报表配置
  // ],
  computed: {
    src() {
      var token=this.getTokenForReport();
      var params="&propertyUnitType="+this.queryParams.propertyUnitType;
      params+="&managementOfficeIdStr="+this.queryParams.managementMaintenanceIds.join(",");
      return this.reportViewerUrl+this.reportId+"?token="+token+"&tenantId=1"+params;
    }
  },
  data() {
    return {
      loading: true,
      reportViewerUrl: process.env.VUE_APP_REPORT_VIEWER_URL,
      reportKey:'risk-ledger-summary',
      reportId: "",
      queryShow:false,
      showIframe:false,
      defaultProps: {
        children: 'children',
        label: 'label'
      },
      deptOptions:[],
      propertyUnitType:'',
      managementMaintenanceIds:[],
      queryParams: { // 查询参数
        propertyUnitType:'',
        managementMaintenanceIds: [], // 管养处
      },
      treeValue: {}
    }
  },
  created(){
    this.getDeptList();
  },
  mounted() {
    this.findReportId();
  },
  methods: {
    getDeptList() {
      deptTreeSelect({ types: 201 }).then(response => {
        this.deptOptions = response.data
      })
    },
    findReportId(){
      var that=this;
      this.getDicts("disaster_report_id_conf").then((res) => {
        if(res.code === 200) {
          var report_id_conf=res.data;
          var reportIds=report_id_conf.filter(function(dict){
            if(dict.dictLabel==that.reportKey){
              return dict;
            }
          });
          that.reportId=reportIds[0].dictValue;
          that.queryhandle();
          that.showIframe=true;
        }
      });
    },
    queryhandle() {
      this.queryParams.propertyUnitType=this.propertyUnitType;
      this.queryParams.managementMaintenanceIds=this.managementMaintenanceIds;

    },
    // 重置查询条件
    queryReset() {
      this.propertyUnitType='';
      this.managementMaintenanceIds=[];
      this.queryParams = { // 查询参数
        managementMaintenanceIds: [], // 管养处
        propertyUnitType: '', // 产权权属单位类型
      }
      this.queryhandle();
    },

    handleNodeCheck(node, nodes) {
      this.managementMaintenanceIds =
        nodes && nodes.checkedNodes ? nodes.checkedNodes.map(v => v.id) : []
      this.$forceUpdate()
    },
    nodeClick(item) {
      const { id, label } = item
      this.treeValue.id = id
      this.treeValue.label = label
    },
    removeTag(tag) {
      let checkedNodes = this.$refs.treeRef.getCheckedNodes()
      // 删除节点
      for (let i = 0; i < checkedNodes.length; i++) {
        if (checkedNodes[i].id == tag) {
          checkedNodes.splice(i, 1)
          break
        }
      }
      // 设置 tree 选中的节点
      this.$refs.treeRef.setCheckedNodes(checkedNodes)
    },
    // 监听清空事件
    selectClear() {
      // 清空树选中数据
      this.$refs.treeRef.setCheckedKeys([])
    },
    // 管养单位渲染列表处理
    getTokenForReport(){
        return getToken();
        // return "eyJhbGciOiJIUzUxMiJ9.eyJ1c2VyX2lkIjoxLCJ1c2VyX2tleSI6ImM4MTlkYjBhLTU2ZGQtNGFlYS1hZjJjLTE2MGM2ZTEzNjk3ZiIsInVzZXJuYW1lIjoiYWRtaW4ifQ.zv7anhpmR1mqZQEdk7-hIyEWMiI59PKiwNtIsnuDmY0kOuVON5vTx6h6vhbzlmdxq-zUTZSJhhwRThTvB8gpKg";
    },
  },
}

</script>

<style lang="scss" scoped>
.app-container form:first-child .el-select,
.app-container form:nth-child(2) .el-select,
.app-container form:nth-child(2) ::v-deep .el-form-item__content,
.app-container form:first-child ::v-deep .el-form-item__content {
  width: 240px;
}

.app-container form:first-child .el-form-item:last-child ::v-deep .el-form-item__content {
  width: auto;
}

.app-container {
  padding: 10px;
  background-color: #c0c0c0;
  box-sizing: border-box;
}

.formDialog {
  ::v-deep .el-dialog__body {
    height: 600px;
    overflow-y: auto;
  }

  .dialog-footer {
    width: 100%;

    .footerTip {
      color: #888888;
      font-size: 14px;
    }
  }

  .titleBox {
    height: 22px;
    position: relative;
    display: flex;
    flex-direction: row;
    align-items: center;

    .title {
      font-size: 16px;
      color: black;
      margin: 0;
    }

    .subTitle {
      margin-left: 15px;
      font-size: 12px;
      color: #888888;
    }

    .riskLevel {
      user-select: none;
      position: absolute;
      // top: 0;
      right: 5%;
      display: flex;
      align-items: center;
      flex-direction: row;

      .title {
        font-size: 16px;
        font-weight: bold;
      }

      .main {
        font-size: 16px;
        font-weight: bold;
        padding: 5px 10px 5px 10px;
        color: white;
        box-sizing: border-box;
        border-radius: 5px;
      }

      .score {
        color: #888888;
        font-size: 14px;
      }

    }
  }
}

.searchBox {
  padding: 10px;
  background: #fff;
  border-radius: 10px;
  transition: all .1s linear;
  display: flex;
  flex-direction: column;

  .searchMoreBox {
    min-width: 192px;
    margin-top: 10px;
    display: flex;
    align-items: center;
    flex-direction: row;
  }
}

.tableDiv {
  margin-top: 10px;
  background-color: white;
  padding-bottom: 10px;
  border-radius: 10px;
  transition: all .1s linear;
  display: flex;
  flex-direction: column;

  .btnBox {
    padding: 10px;
  }
}

.infoBox {
  padding: 15px 15px 0 15px;
  box-sizing: border-box;
  border-radius: 6px;
  border: 1px solid #C4C4C4;
  position: relative;

  .infoTitle {
    user-select: none;
    position: absolute;
    top: 0;
    left: 0;
    padding: 0 10px;
    font-size: 14px;
    line-height: 14px;
    font-weight: bold;
    transform: translateX(15px) translateY(-50%);
    background-color: white;
  }

  .imgBox {
    height: auto;
    width: 100%;
    display: flex;
    flex-wrap: wrap;
    align-content: flex-start;

    .imgItemBox {
      height: 240px;
      width: calc(100% / 3);
      box-sizing: border-box;
      padding: 10px;
      overflow-y: auto;
      display: flex;
      align-items: center;
      justify-content: center;
      position: relative;

      .imgDeleteBtn {
        position: absolute;
        z-index: 1;
        top: 10%;
        right: 10%;
      }
    }
  }
}

.coordinateDialog {

  .coordinateMap {
    height: 600px;
    width: 100%;
    position: relative;

    #coordinateBox {
      height: 100%;
      width: 100%;
      border-radius: 5px;
      position: relative;
      z-index: 0;
    }

    .coordinateSearch {
      position: absolute;
      z-index: 1;
      top: 10px;
      left: 10px;
      width: 50%;
      padding: 10px;
      box-sizing: border-box;
      background-color: #fff;
      border-radius: 5px;
      border: 1px solid #DCDFE6;
      box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.12), 0 0 6px 0 rgba(0, 0, 0, 0.04);
      display: flex;
      flex-direction: row;
    }

    .coordinateTip {
      position: absolute;
      z-index: 1;
      top: 10px;
      right: 10px;
      padding: 10px;
      box-sizing: border-box;
      background-color: #fff;
      border-radius: 5px;
      border: 1px solid #DCDFE6;
      box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.12), 0 0 6px 0 rgba(0, 0, 0, 0.04);
    }
  }
}

// v-if过渡动画
// 查询框
.search-enter-active {
  transition: all .1s linear;
}

.search-enter {
  opacity: 0;
}

.search-leave-active {
  transition: all .1s linear;
}

.search-leave-to {
  opacity: 0;
}
</style>
