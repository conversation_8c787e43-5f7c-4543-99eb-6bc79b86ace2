<template>
  <div>
    <el-dialog
      :title="title"
      :visible.sync="showAddEdit"
      width="60%"
      append-to-body
      :before-close="handleClose"
      :close-on-click-modal="false"
      :class="forView ? 'forView' : ''"
    >
      <div
        v-loading="loading"
        style="height: 60vh; overflow-y: auto; padding: 0 10px 0 5px"
      >
        <el-form
          ref="form"
          :model="form"
          :rules="rules"
          label-width="120px"
          :disabled="forView ? true : false"
        >
          <div style="display: flex; flex-wrap: wrap; margin-bottom: 20px">
            <el-divider content-position="left">基础数据</el-divider>
            <!-- <el-col :span="12">
              <el-form-item
                label="管理处"
                prop="managementMaintenanceId"
              >
                <SelectTree
                  v-model="form.managementMaintenanceId"
                  :dept-type="201"
                  placeholder="请选择"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item
                label="管养分处"
                prop="managementMaintenanceBranchId"
              >
                <SelectTree
                  v-model="form.managementMaintenanceBranchId"
                  :dept-type="202"
                  placeholder="请选择"
                />
              </el-form-item>
            </el-col> -->
            <ManageSelectTree placeholder="请选择" :formObject="form" />

            <el-col :span="12">
              <el-form-item label="养护路段" prop="maintenanceSectionId">
                <el-select
                  v-model="form.maintenanceSectionId"
                  style="width: 100%"
                  placeholder="请选择"
                  clearable
                  :disabled="!form.managementMaintenanceBranchId"
                >
                  <el-option
                    v-for="item in routeOptions"
                    :key="item.maintenanceSectionId"
                    :label="item.maintenanceSectionName"
                    :value="item.maintenanceSectionId"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="路段类型" prop="sectionType">
                <SectionSelect
                  v-model="form.sectionType"
                  :style="
                    'width: 100%;' + (forView ? 'pointer-events: none' : '')
                  "
                  :formObject="form"
                  :sectionId="form.maintenanceSectionId"
                  :disabled="!form.maintenanceSectionId"
                  clearable
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="路线编码" prop="routeCode">
                <el-select
                  v-model="form.routeCode"
                  placeholder="请选择"
                  :disabled="!form.maintenanceSectionId"
                  clearable
                  style="width: 100%"
                  @change="
                    (val) => {
                      handleSelect(val);
                    }
                  "
                >
                  <el-option
                    v-for="(item, index) in routeList"
                    :key="index"
                    :label="item.routeCode"
                    :value="item.routeCode"
                  >
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="路线名称" prop="routeName">
                <el-select
                  v-model="form.routeName"
                  style="width: 100%"
                  placeholder="请选择"
                  :disabled="true"
                  clearable
                >
                  <el-option
                    v-for="item in []"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="路线技术等级" prop="routeLevel">
                <MultiDictSelect
                  v-model="form.routeLevel"
                  :disabled="''"
                  :multiple="true"
                  :options="dict.type['sys_route_grade']"
                />
              </el-form-item>
            </el-col>

            <el-col :span="12">
              <el-form-item label="中央集水沟编码" prop="roadbedCode">
                <el-input
                  v-model="form.roadbedCode"
                  style="width: 100%"
                  clearable
                  placeholder="请输入"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="面层类型" prop="surfaceType">
                <el-select
                  v-model="form.pavementType"
                  style="width: 100%"
                  placeholder="请选择"
                  clearable
                >
                  <el-option
                    v-for="item in dict.type.sys_surface_type"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="车道数" prop="laneNumber">
                <el-input-number
                  v-model="form.laneNumber"
                  style="width: 100%"
                  :precision="0"
                  clearable
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="方向" prop="direction">
                <el-select
                  v-model="form.direction"
                  style="width: 100%"
                  placeholder="请选择"
                  clearable
                >
                  <el-option
                    v-for="i in dict.type.sys_route_direction"
                    :key="i.value"
                    :label="i.label"
                    :value="i.value"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="左右" prop="leftOrRight">
                <el-select
                  v-model="form.leftOrRight"
                  style="width: 100%"
                  placeholder="请选择"
                  clearable
                >
                  <el-option
                    v-for="i in dict.type.left_right"
                    :key="i.value"
                    :label="i.label"
                    :value="i.value"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="位置" prop="lane">
                <el-select
                  v-model="form.lane"
                  style="width: 100%"
                  placeholder="请选择"
                  clearable
                >
                  <el-option
                    v-for="i in dict.type.lane"
                    :key="i.value"
                    :label="i.label"
                    :value="i.value"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="起点桩号" prop="startStake">
                <PileInput v-model="form.startStake" placeholder="请输入" />
              </el-form-item>
            </el-col>

            <el-col :span="12">
              <el-form-item label="经纬度" prop="longitude">
                <lon-lat
                  type="lonlat"
                  :lon.sync="form.longitude"
                  :lat.sync="form.latitude"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="终点桩号" prop="endStake">
                <PileInput v-model="form.endStake" placeholder="请输入" />
              </el-form-item>
            </el-col>

            <el-col :span="12">
              <el-form-item label="建成时间" prop="buildDate">
                <el-date-picker
                  style="width: 100%"
                  v-model="form.buildDate"
                  type="date"
                  placeholder="请选择建成时间"
                  :picker-options="pickerOptions"
                  clearable
                  value-format="yyyy-MM-dd"
                />
              </el-form-item>
            </el-col>

            <el-col :span="12">
              <el-form-item label="长度(m)" prop="length">
                <el-input-number
                  v-model="form.length"
                  style="width: 100%"
                  clearable
                />
              </el-form-item>
            </el-col>

            <el-col :span="12">
              <el-form-item label="类型" prop="type">
                <el-input v-model="form.type" style="width: 100%" clearable />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="规格" prop="specifica">
                <el-input
                  v-model="form.specifica"
                  style="width: 100%"
                  clearable
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="集水井位置" prop="position">
                <el-input
                  v-model="form.position"
                  style="width: 100%"
                  clearable
                />
              </el-form-item>
            </el-col>

            <el-col :span="12">
              <el-form-item label="集水井数量" prop="number">
                <el-input-number
                  v-model="form.number"
                  style="width: 100%"
                  clearable
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="坡率(%)" prop="slopeRatio">
                <el-input-number
                  v-model="form.slopeRatio"
                  style="width: 100%"
                  :precision="3"
                  clearable
                />
              </el-form-item>
            </el-col>

            <el-divider content-position="left">统一里程</el-divider>
            <el-col :span="12">
              <el-form-item label="起点桩号" prop="unifiedMileageStartStake">
                <PileInput v-model="form.unifiedMileageStartStake" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="终点桩号" prop="unifiedMileageEndStake">
                <PileInput v-model="form.unifiedMileageEndStake" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="统一里程桩号" prop="unifiedMileageStake">
                <PileInput v-model="form.unifiedMileageStake" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="管养里程(km)" prop="maintenanceMileage">
                <el-input-number
                  v-model="form.maintenanceMileage"
                  style="width: 100%"
                  :precision="3"
                  clearable
                />
              </el-form-item>
            </el-col>
            <el-divider content-position="left">施工里程</el-divider>
            <el-col :span="12">
              <el-form-item label="起点桩号" prop="constructionStartStake">
                <PileInput v-model="form.constructionStartStake" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="终点桩号" prop="constructionEndStake">
                <PileInput v-model="form.constructionEndStake" />
              </el-form-item>
            </el-col>

            <el-col :span="12">
              <el-form-item label="施工里程(km)" prop="constructionMileage">
                <PileInput v-model="form.constructionMileage" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="施工桩号" prop="constructionStake">
                <PileInput v-model="form.constructionStake" />
              </el-form-item>
            </el-col>
            <el-divider content-position="left">国高网里程</el-divider>
            <el-col :span="12">
              <el-form-item label="起点桩号" prop="nationalNetworkStartStake">
                <PileInput v-model="form.nationalNetworkStartStake" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="终点桩号" prop="nationalNetworkEndStake">
                <PileInput v-model="form.nationalNetworkEndStake" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item
                label="国高网里程(km)"
                prop="nationalNetworkMileage"
              >
                <el-input-number
                  v-model="form.nationalNetworkMileage"
                  style="width: 100%"
                  :precision="3"
                  clearable
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="国高网桩号" prop="nationalNetworkStake">
                <PileInput v-model="form.nationalNetworkStake" />
              </el-form-item>
            </el-col>

            <el-divider content-position="left">其他</el-divider>
            <el-col :span="12">
              <el-form-item label="移交管理单位" prop="transferManagementUnit">
                <el-input
                  v-model="form.transferManagementUnit"
                  style="width: 100%"
                  clearable
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="备注" prop="remark">
                <el-input
                  v-model="form.remark"
                  type="textarea"
                  autosize
                  clearable
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="图片" prop="samplePictureId">
                <ImageUpload
                  :key="ownerId"
                  v-model="form.samplePictureId"
                  :limit="1"
                  :owner-id="ownerId"
                  storage-path="/base/subgrade/collectionDitch"
                />
              </el-form-item>
            </el-col>
          </div>
        </el-form>
      </div>
      <div slot="footer">
        <el-button
          v-if="!forView"
          type="primary"
          :loading="loading"
          @click="handleSubmit('submit')"
          >提 交</el-button
        >
        <el-button
          v-if="!forView && (!formData.id || formData.status == 1)"
          v-hasPermi="['baseData:collectionDitch:tempAdd']"
          type="primary"
          :loading="loading"
          @click="handleSubmit('save')"
          >暂 存</el-button
        >
        <el-button @click="handleClose">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import PileInput from "@/components/PileInput/index.vue";
import SelectTree from "@/components/DeptTmpl/selectTree";
import SectionSelect from "@/components/SectionSelect";
import { createIdWorker } from "@/api/baseData/common";
import { listMaintenanceSectionAll } from "@/api/system/maintenanceSection";
import { listByMaintenanceSectionId } from "@/api/baseData/common/routeLine";
import ManageSelectTree from "@/components/manageSelectTree/index.vue";
import MultiDictSelect from "@/views/baseData/components/MultiDictSelect/index.vue";
import lonLat from "@/components/mapPosition/lonLat.vue";

import {
  addCollectionDitch,
  updateCollectionDitch,
  tempaddCollectionDitch,
} from "@/api/baseData/subgrade/collectionDitch/index";
import { isArray } from "../../../../utils/validate";
import { containsXY } from "ol/extent";

export default {
  name: "wallInfo",
  props: {
    formData: {
      default: {},
    },
    showAddEdit: {
      default: false,
    },
    title: {
      default: "添加边坡静态数据",
    },
    forView: {
      default: false,
    },
  },
  dicts: [
    "lane",
    "sys_route_grade",
    "subgrade_suface_type",
    "bridge_business_state",
    "sys_surface_type",
    "retaining_wall_position",
    "sys_route_direction",
    "left_right",
  ],
  components: {
    PileInput,
    SelectTree,
    SectionSelect,
    ManageSelectTree,
    MultiDictSelect,
    lonLat,
  },
  data() {
    return {
      loading: false,
      form: {
        managementMaintenanceId: "",
        managementMaintenanceBranchId: "",
      },
      pickerOptions: {
        disabledDate(v) {
          return v.getTime() > new Date().getTime() 
        }
      },
      rules: {
        managementMaintenanceId: [
          { required: true, message: "请选择管理处", trigger: "change" },
        ],
        managementMaintenanceBranchId: [
          { required: true, message: "请选择管养分处", trigger: "change" },
        ],
        maintenanceSectionId: [
          { required: true, message: "请选择养护路段", trigger: "change" },
        ],
        routeCode: [
          { required: true, message: "请选择路线编码", trigger: "change" },
        ],
        startStake: [
          { required: true, message: "请输入起点桩号", trigger: "blur" },
        ],
        endStake: [
          { required: true, message: "请输入终点桩号", trigger: "blur" },
        ],
      },
      routeOptions: [],
      routeList: [],
      ownerId: "",
    };
  },
  created() {
    this.init();
  },
  methods: {
    init() {
      if (this.formData.id) {
        this.form = JSON.parse(JSON.stringify(this.formData));
      }
      if (!this.forView) {
        createIdWorker().then((res) => {
          if (res.code === 200) {
            this.ownerId = Number(res.data);
          }
        });
      }
    },
    handleSelect(e) {
      const option = this.routeList.find((i) => i.routeCode === e);
      if (option) {
        this.form.routeId = option.routeId;
        this.form.routeName = option.routeName;
      }
    },
    handleSubmit(type) {
      let pass = true;
      switch (type) {
        case "submit":
          this.form.isStaging = 2;
          break;
        case "save":
          this.form.isStaging = 1;
          break;
      }
      if (this.form.isStaging == 2) {
        this.$refs.form.validate((valid) => {
          if (valid) {
            pass = true;
          } else {
            pass = false;
            return false;
          }
        });
      }
      if (!pass) return;
      if (isArray(this.form?.samplePictureId)) {
        this.form.samplePictureId =
          this.form?.samplePictureId?.length > 0
            ? this.form?.samplePictureId[0]
            : "";
      }

      if (this.form.routeLevel && isArray(this.form.routeLevel)) {
        this.form.routeLevel = this.form.routeLevel.join(",");
      }

      let arr = this.routeList.filter(
        (el) => el.routeCode == this.form.routeCode
      );
      this.form.routeId = arr[0]?.routeId;
      this.form.routeName = arr[0]?.routeName;
      this.loading = true;

      if (this.form.id != null) {
        const api =
          this.form.isStaging === 1
            ? tempaddCollectionDitch
            : updateCollectionDitch;

        api(this.form)
          .then((response) => {
            this.$modal.msgSuccess("修改成功");
            this.$emit("refresh");
          })
          .catch(() => {
            this.loading = false;
          });
      } else {
        const api =
          this.form.isStaging === 1
            ? tempaddCollectionDitch
            : addCollectionDitch;
        api(this.form)
          .then((response) => {
            this.$modal.msgSuccess(
              this.form.isStaging === 1 ? "暂存成功" : "新增成功"
            );
            this.$emit("refresh");
          })
          .catch(() => {
            this.loading = false;
          });
      }
    },
    handleClose() {
      if (this.forView) {
        this.form = {};
        this.$emit("close", false);
      } else {
        this.$modal
          .confirm("确认退出？")
          .then(() => {
            this.form = {};
            this.$emit("close", false);
          })
          .catch(() => {});
      }
    },
    deptChange(e) {
      if (!e) return;
      listMaintenanceSectionAll({ departmentId: e }).then((res) => {
        if (res.code == 200) {
          this.routeOptions = res.data;
        }
      });
    },
    maintenanceSectionChange(e) {
      if (!e) return;

      const option = this.routeOptions.find(
        (i) => i.maintenanceSectionId === e
      );
      if (option && option.routeGrade) {
        this.form["routeLevel"] = option.routeGrade.split(",");
      }

      listByMaintenanceSectionId({ maintenanceSectionId: e }).then((res) => {
        if (res.code == 200) {
          this.routeList = res.data || [];
          // this.form.routeCode = ''
          this.$forceUpdate();
        }
      });
    },
  },
  computed: {},
  watch: {
    "form.managementMaintenanceId"(newVal, oldVal) {
      // if (newVal) {
      //   this.deptChange(newVal);
      // }
      // if (oldVal) {
      //   if (this.form.maintenanceSectionId) {
      //     this.form.maintenanceSectionId = "";
      //   } else if (this.form.routeCode) {
      //     this.form.routeCode = "";
      //   }
      // }
    },
    "form.managementMaintenanceBranchId"(newVal, oldVal) {
      if (newVal) {
        this.deptChange(newVal);
      }
      if (oldVal && this.form.maintenanceSectionId) {
        if(this.form.maintenanceSectionId)this.form.maintenanceSectionId=''
         if(this.form.routeCode)this.form.routeCode=''
      }
    },
    "form.maintenanceSectionId"(newVal, oldVal) {
      if (newVal) {
        this.maintenanceSectionChange(newVal);
      }

      if (oldVal) {
        if (this.form.routeCode) {
          this.form.routeCode = "";
        }
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.forView ::v-deep .el-input.is-disabled .el-input__inner {
  background-color: white;
  border-color: #dfe4ed;
  color: black;
}
::v-deep .el-dialog__header {
  border-bottom: 1px #dfe4ed solid;
  padding: 20px 30px !important;
}
::v-deep .el-divider--horizontal {
  margin: 20px 0 !important;
}
</style>
