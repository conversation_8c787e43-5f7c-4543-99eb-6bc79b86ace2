<template>
  <div class="app-container">


    <div class="searchBox" :style="{ 'height': queryShow ? '86px' : '48px' }">

      <el-form :model="queryParams" ref="queryForm" size="mini" :inline="true"  label-width="68px">
<!--        <el-form-item label="" prop="reportingUnitName">-->
<!--          <el-input-->
<!--            v-model="queryParams.reportingUnitName"-->
<!--            placeholder="请输入报送单位"-->
<!--            clearable-->
<!--            prefix-icon="el-icon-user"-->
<!--            style="width: 240px"-->
<!--            @keyup.enter.native="handleQuery"-->
<!--          />-->
<!--        </el-form-item>-->

        <el-form-item label="" prop="roadSectionName">
          <el-input
            v-model="queryParams.roadSectionName"
            placeholder="请输入路段名称"
            clearable
            prefix-icon="el-icon-user"
            style="width: 240px"
            @keyup.enter.native="handleQuery"
          />
        </el-form-item>
        <el-form-item label="" prop="routeCode">
          <el-input
            v-model="queryParams.routeCode"
            placeholder="请输入所属路线编号"
            clearable
            prefix-icon="el-icon-user"
            style="width: 240px"
            @keyup.enter.native="handleQuery"
          />
        </el-form-item>
        <el-form-item label="" prop="projectName">
          <el-input
            v-model="queryParams.projectName"
            placeholder="请输入项目名称"
            clearable
            prefix-icon="el-icon-user"
            style="width: 240px"
            @keyup.enter.native="handleQuery"
          />
        </el-form-item>

        <el-form-item label="" prop="workPointType">
<!--          <DictSelect  clearable style="width: 240px" type="backtrack_work_point_type" placeholder="请选择工点类型" v-model="queryParams.workPointType" ></DictSelect>-->

          <el-input
            v-model="queryParams.workPointType"
            placeholder="请输入工点类型"
            clearable
            prefix-icon="el-icon-user"
            style="width: 240px"
            @keyup.enter.native="handleQuery"
          />
        </el-form-item>

        <el-form-item label="" prop="riskHiddenDangerSituation">

          <el-input
            v-model="queryParams.riskHiddenDangerSituation"
            placeholder="风险隐患情况(等级)"
            clearable
            prefix-icon="el-icon-user"
            style="width: 240px"
            @keyup.enter.native="handleQuery"
          />
        </el-form-item>

        <el-form-item>
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
          <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
<!--          <el-button-->
<!--            :icon="queryShow ? 'el-icon-arrow-up' : 'el-icon-arrow-down'"-->
<!--            style="color: #1890ff;border-color: #badeff;background-color: #e8f4ff;"-->
<!--            circle-->
<!--            @click="queryShow = !queryShow"-->
<!--          />-->
        </el-form-item>


      </el-form>


      <transition name="search">
        <div class="searchMoreBox" v-if="queryShow">
          <el-form :model="queryParams" ref="queryForm2" size="mini" :inline="true"  label-width="68px">

<!--            <el-form-item label="" prop="constructUnit">-->
<!--              <el-input-->
<!--                v-model="queryParams.constructUnit"-->
<!--                placeholder="请输入建设单位"-->
<!--                clearable-->
<!--                prefix-icon="el-icon-user"-->
<!--                style="width: 240px"-->
<!--                @keyup.enter.native="handleQuery"-->
<!--              />-->
<!--            </el-form-item>-->
<!--            <el-form-item label="" prop="operatingUnit">-->
<!--              <el-input-->
<!--                v-model="queryParams.operatingUnit"-->
<!--                placeholder="请输入运营单位"-->
<!--                clearable-->
<!--                prefix-icon="el-icon-user"-->
<!--                style="width: 240px"-->
<!--                @keyup.enter.native="handleQuery"-->
<!--              />-->
<!--            </el-form-item>-->

<!--            <el-form-item label="" prop="designUnit">-->
<!--              <el-input-->
<!--                v-model="queryParams.designUnit"-->
<!--                placeholder="请输入设计单位"-->
<!--                clearable-->
<!--                prefix-icon="el-icon-user"-->
<!--                style="width: 240px"-->
<!--                @keyup.enter.native="handleQuery"-->
<!--              />-->
<!--            </el-form-item>-->
<!--            <el-form-item label="" prop="designReviewUnit">-->
<!--              <el-input-->
<!--                v-model="queryParams.designReviewUnit"-->
<!--                placeholder="请输入设计回溯单位"-->
<!--                clearable-->
<!--                prefix-icon="el-icon-user"-->
<!--                style="width: 240px"-->
<!--                @keyup.enter.native="handleQuery"-->
<!--              />-->
<!--            </el-form-item>-->
<!--            <el-form-item label="" prop="workPointStakeNumber">-->
<!--              <el-input-->
<!--                v-model="queryParams.workPointStakeNumber"-->
<!--                placeholder="请输入工点桩号"-->
<!--                clearable-->
<!--                prefix-icon="el-icon-user"-->
<!--                style="width: 240px"-->
<!--                @keyup.enter.native="handleQuery"-->
<!--              />-->
<!--            </el-form-item>-->
<!--            <el-form-item label="" prop="point">-->
<!--              <el-input-->
<!--                v-model="queryParams.point"-->
<!--                placeholder="请输入坐标"-->
<!--                clearable-->
<!--                prefix-icon="el-icon-user"-->
<!--                style="width: 240px"-->
<!--                @keyup.enter.native="handleQuery"-->
<!--              />-->
<!--            </el-form-item>-->


<!--            <el-form-item label="" prop="threatTarget">-->
<!--              <el-input-->
<!--                v-model="queryParams.threatTarget"-->
<!--                placeholder="请输入威胁对象"-->
<!--                clearable-->
<!--                prefix-icon="el-icon-user"-->
<!--                style="width: 240px"-->
<!--                @keyup.enter.native="handleQuery"-->
<!--              />-->
<!--            </el-form-item>-->

<!--            <el-form-item label="" prop="workPointSource">-->
<!--              <el-input-->
<!--                v-model="queryParams.workPointSource"-->
<!--                placeholder="请输入工点来源"-->
<!--                clearable-->
<!--                prefix-icon="el-icon-user"-->
<!--                style="width: 240px"-->
<!--                @keyup.enter.native="handleQuery"-->
<!--              />-->
<!--            </el-form-item>-->

<!--            <el-form-item label="" prop="projectType">-->
<!--              <el-input-->
<!--                v-model="queryParams.projectType"-->
<!--                placeholder="请输入类型"-->
<!--                clearable-->
<!--                prefix-icon="el-icon-user"-->
<!--                style="width: 240px"-->
<!--                @keyup.enter.native="handleQuery"-->
<!--              />-->
<!--            </el-form-item>-->



            <el-form-item label="" prop="costOwnership">
              <DictSelect clearable style="width: 240px" type="backtrack_cost_ownership" placeholder="请选择费用归口" v-model="queryParams.costOwnership" ></DictSelect>

              <!--              <el-input-->
<!--                v-model="queryParams.costOwnership"-->
<!--                placeholder="请输入费用归口"-->
<!--                clearable-->
<!--                prefix-icon="el-icon-user"-->
<!--                style="width: 240px"-->
<!--                @keyup.enter.native="handleQuery"-->
<!--              />-->
            </el-form-item>
            <el-form-item label="" prop="ownership">
              <DictSelect clearable style="width: 240px" type="backtrack_ownership" placeholder="请选择权属" v-model="queryParams.ownership"></DictSelect>
<!--              <el-input-->
<!--                v-model="queryParams.ownership"-->
<!--                placeholder="请输入权属"-->
<!--                clearable-->
<!--                prefix-icon="el-icon-user"-->
<!--                style="width: 240px"-->
<!--                @keyup.enter.native="handleQuery"-->
<!--              />-->
            </el-form-item>

          </el-form>

        </div>

      </transition>
    </div>

    <!-- 主表数据 -->
    <div class="tableDiv" :style="{ 'height': queryShow ? 'calc(100% - 96px)' : 'calc(100% - 58px)' }">
      <!-- 功能按钮 -->
      <div class="btnBox">
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button
              type="primary"
              icon="el-icon-plus"
              size="mini"
              @click="handleAdd"
              v-hasPermi="['backtrack:backtrack:add']"
            >新增
            </el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button
              type="success"
              icon="el-icon-edit"
              size="mini"
              :disabled="single"
              @click="handleUpdate"
              v-hasPermi="['backtrack:backtrack:edit']"
            >修改
            </el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button
              type="danger"
              icon="el-icon-delete"
              size="mini"
              :disabled="multiple"
              @click="handleDelete"
              v-hasPermi="['backtrack:backtrack:remove']"
            >删除
            </el-button>
          </el-col>
<!--          <el-col :span="1.5">-->
<!--            <el-button-->
<!--              type="info"-->
<!--              plain-->
<!--              icon="el-icon-upload2"-->
<!--              size="mini"-->
<!--              @click="handleImport"-->
<!--              v-hasPermi="['backtrack:backtrack:export']"-->
<!--            >导入-->
<!--            </el-button>-->
<!--          </el-col>-->
          <el-col :span="1.5">
            <el-button
              type="warning"
              icon="el-icon-download"
              size="mini"
              @click="handleExport"
              v-hasPermi="['system:user:export']"
            >导出
            </el-button>
          </el-col>
<!--          <right-toolbar :showSearch.sync="showSearch" @queryTable="getList" :columns="columns"></right-toolbar>-->
        </el-row>

      </div>

      <!-- 数据表格 -->
      <el-table v-adjust-table size="mini" height="calc(100% - 98px)" border v-loading="loading" :data="backtrackList"
                @selection-change="handleSelectionChange" ref="tableRef">
        <el-table-column type="selection" width="50" align="center"/>
        <el-table-column fixed align="center" label="序号" type="index" width="50">
        </el-table-column>

<!--        <el-table-column label="路段id" align="center" prop="roadSectionId"/>-->
        <el-table-column label="路段名称" show-overflow-tooltip align="center" prop="roadSectionName"/>
        <el-table-column label="所属路线编号" min-width="120"  align="center" prop="routeCode"/>
        <el-table-column label="运营单位" show-overflow-tooltip min-width="220" align="center" prop="operatingUnit"/>
        <!--        <el-table-column label="运营单位id" align="center" prop="operatingUnitId"/>-->
        <!--        <el-table-column label="报送单位id" align="center" prop="reportingUnitId"/>-->
       >
        <el-table-column label="项目名称" show-overflow-tooltip min-width="220" align="center" prop="projectName"/>
        <el-table-column label="建设单位" show-overflow-tooltip min-width="220" align="center" prop="constructUnit"/>
        <el-table-column label="报送单位" show-overflow-tooltip align="center" prop="reportingUnitName"/
        <el-table-column label="原设计单位" show-overflow-tooltip min-width="220" align="center" prop="designUnit"/>
        <el-table-column label="设计回溯单位" show-overflow-tooltip min-width="260" align="center" prop="designReviewUnit"/>
<!--        <el-table-column label="工点桩号" align="center" prop="workPointStakeNumber" :formatter="(...arg)=>formatPile(arg[2])"/>-->
        <el-table-column label="工点桩号" min-width="180" show-overflow-tooltip align="center" prop="workPointStakeNumber" />
        <el-table-column label="工点概述" show-overflow-tooltip min-width="320" align="center" prop="workPointSummary"/>
        <el-table-column label="坐标" min-width="120" show-overflow-tooltip align="center" prop="point"/>
        <el-table-column label="风险隐患情况" show-overflow-tooltip  min-width="120" align="center" prop="riskHiddenDangerSituation"></el-table-column>
<!--        <el-table-column label="风险隐患情况" show-overflow-tooltip  min-width="320" align="center" prop="riskHiddenDangerSituation">-->
<!--          <template slot-scope="scope">-->
<!--            <dict-tag :options="dict.type.backtrack_risk_level"  :value="scope.row.riskHiddenDangerSituation"  />-->
<!--          </template>-->
<!--        </el-table-column>-->
        <el-table-column label="工点类型" align="center" prop="workPointType"></el-table-column>

<!--        <el-table-column label="工点类型" align="center" prop="workPointType">-->
<!--          <template slot-scope="scope">-->
<!--            <dict-tag :options="dict.type.backtrack_work_point_type"  :value="scope.row.workPointType"  />-->
<!--          </template>-->
<!--        </el-table-column>-->
        <el-table-column label="威胁对象" align="center" prop="threatTarget"/>
        <el-table-column label="应对措施及处治措施" show-overflow-tooltip min-width="320" align="center" prop="countermeasuresAndTreatmentMeasures"/>
        <el-table-column label="工作计划" show-overflow-tooltip min-width="320" align="center" prop="workPlan"/>
        <el-table-column label="工点来源"  min-width="200" align="center" prop="workPointSource"/>
        <el-table-column label="重合情况" min-width="200" show-overflow-tooltip align="center" prop="overlappingSituation"/>
        <el-table-column label="序号对应关系" min-width="220" align="center" prop="indexRelationship"/>
<!--        <el-table-column label="方案类型" align="center" prop="projectType">-->
<!--          <template slot-scope="scope">-->
<!--            <dict-tag :options="dict.type.backtrack_project_type"  :value="scope.row.projectType"  />-->
<!--          </template>-->
<!--        </el-table-column>-->
        <el-table-column label="具体实施方案-工程处治" show-overflow-tooltip min-width="220" align="center" prop="engineeringTreatment"/>
        <el-table-column label="具体实施方案-监测"   show-overflow-tooltip   min-width="220" align="center" prop="monitoring"/>
        <el-table-column label="具体实施方案-预警"   show-overflow-tooltip   min-width="220" align="center" prop="warning"/>
<!--        <el-table-column label="工程处治 列预算情况" show-overflow-tooltip min-width="220" align="center" prop="engineeringTreatmentBudgetStatus"/>-->
<!--        <el-table-column label="工程处治 金额" min-width="220" align="center" prop="engineeringTreatmentAmount" :formatter="(...arg)=>arg[2] ? arg[2]+'（万元）':''" />-->
<!--        <el-table-column label="监测预警 列预算情况" show-overflow-tooltip min-width="220" align="center" prop="monitoringBudgetStatus"/>-->
<!--        <el-table-column label="监测预警 金额" min-width="220" align="center" prop="monitoringAmount" :formatter="(...arg)=>arg[2]? arg[2]+'（万元）':''"/>-->
                <el-table-column label="列预算情况-具体情况" show-overflow-tooltip min-width="220" align="center" prop="engineeringTreatmentBudgetStatus"/>
                <el-table-column label="估算费用-工程处治" min-width="220" align="center" prop="engineeringTreatmentAmount" :formatter="(...arg)=>arg[2] ? arg[2]+'（万元）':''" />
                <el-table-column label="列预算情况-金额（万元）" show-overflow-tooltip min-width="220" align="center" prop="monitoringBudgetStatus"/>
                <el-table-column label="估算费用-监测预警" min-width="220" align="center" prop="monitoringAmount" :formatter="(...arg)=>arg[2]? arg[2]+'（万元）':''"/>
        <el-table-column label="费用归口" width="200" align="center" prop="costOwnership">

        </el-table-column>
<!--        <el-table-column label="费用归口" width="200" align="center" prop="costOwnership">-->
<!--          <template slot-scope="scope">-->
<!--            <dict-tag :options="dict.type.backtrack_cost_ownership"  :value="scope.row.costOwnership"  />-->
<!--          </template>-->
<!--        </el-table-column>-->
        <el-table-column label="权属" align="center" prop="ownership">


        </el-table-column>

<!--        <el-table-column label="权属" align="center" prop="ownership">-->
<!--          <template slot-scope="scope">-->
<!--            <dict-tag :options="dict.type.backtrack_ownership" :value="scope.row.ownership" ></dict-tag>-->

<!--          </template>-->

<!--        </el-table-column>-->
        <el-table-column label="公路竣工验收情况" show-overflow-tooltip min-width="280" align="center" prop="highwayAcceptanceStatus"/>
        <el-table-column label="处治情况" align="center" prop="treatmentStatus"/>
        <el-table-column label="备注" show-overflow-tooltip  min-width="320" align="center" prop="remarks"/>
        <el-table-column
          label="操作"
          fixed="right"
          align="center"
          width="160"
          class-name="small-padding fixed-width"
        >
          <template slot-scope="scope" v-if="scope.row.userId !== 1">
            <el-button
              size="mini"
              type="text"
              icon="el-icon-edit"
              @click="handleUpdate(scope.row)"
              v-hasPermi="['backtrack:backtrack:edit']"
            >修改
            </el-button>
            <el-button
              size="mini"
              type="text"
              icon="el-icon-delete"
              @click="handleDelete(scope.row)"
              v-hasPermi="['backtrack:backtrack:remove']"
            >删除
            </el-button>
          </template>
        </el-table-column>

      </el-table>


      <pagination
        v-show="total>0"
        :total="total"
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
        @pagination="getList"
      />
    </div>


    <!-- 添加或修改设计回溯对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="70%" max-height="50%" class="formDialog" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="180px">

        <div class="infoBox" >
          <div class="infoTitle">
            基础信息
          </div>
          <!--          <el-form-item label="报送单位id" prop="reportingUnitId">-->
          <!--            <el-input v-model="form.reportingUnitId" placeholder="请输入报送单位id"/>-->
          <!--          </el-form-item>-->




          <el-form-item label="运营单位" prop="operatingUnitId" class="inline-form-item">
<!--            <el-input v-model="form.operatingUnit" placeholder="请输入运营单位"/>-->
            <el-select ref="formMainRef" v-model="form.operatingUnitId" placeholder="请选择管养单位" @change="formMaintenanceChange('1')"
                       style="width: 100%;"  >
              <el-option v-for="item in formMaintenanceRenderList" :label="item.label" :value="item.id"
                         :key="item.id" ></el-option>

            </el-select>

          </el-form-item>

          <!--          <el-form-item label="路段id" prop="roadSectionId">-->
          <!--            <el-input v-model="form.roadSectionId" placeholder="请输入路段id"/>-->
          <!--          </el-form-item>-->


          <el-form-item label="路段名称" prop="roadSectionId" class="inline-form-item">
<!--            <el-input v-model="form.roadSectionName" placeholder="请输入路段名称"/>-->
            <span slot="label">
                  <el-tooltip content="选择运营单位后带出" placement="top">
                    <i class="el-icon-question"></i>
                  </el-tooltip>
                  路段名称
                </span>

            <el-select v-model="form.roadSectionId" placeholder="请选择路段" style="width: 100%;" @change="formMaintenanceChange('2')">
              <el-option v-for="item in formRoadSectionList" :label="item.maintenanceSectionName"
                         :value="item.maintenanceSectionId" :key="item.maintenanceSectionId"></el-option>
            </el-select>
          </el-form-item>


          <el-form-item label="所属路线编号" prop="routeCode" class="inline-form-item">
            <span slot="label">
                  <el-tooltip content="选择路段后带出" placement="top">
                    <i class="el-icon-question"></i>
                  </el-tooltip>
                  所属路线编号
                </span>
            <el-input v-model="form.routeCode" placeholder="请输入所属路线编号"/>
          </el-form-item>

<!--          <el-form-item label="报送单位" prop="reportingUnitName" class="inline-form-item">-->
<!--            <el-input v-model="form.reportingUnitName" placeholder="请输入报送单位"/>-->
<!--          </el-form-item>-->

          <el-form-item label="项目名称" prop="projectName" class="inline-form-item">
            <el-input v-model="form.projectName" placeholder="请输入项目名称"/>
          </el-form-item>


          <el-form-item label="建设单位" prop="constructUnit" class="inline-form-item">
            <el-input v-model="form.constructUnit" placeholder="请输入建设单位"/>
          </el-form-item>





          <!--          <el-form-item label="运营单位id" prop="operatingUnitId">-->
          <!--            <el-input v-model="form.operatingUnitId" placeholder="请输入运营单位id"/>-->
          <!--          </el-form-item>-->


          <el-form-item label="原设计单位" prop="designUnit" class="inline-form-item">
            <el-input v-model="form.designUnit" placeholder="请输入设计单位"/>
          </el-form-item>


          <el-form-item label="设计回溯单位" prop="designReviewUnit" class="inline-form-item">
            <el-input v-model="form.designReviewUnit" placeholder="请输入设计回溯单位"/>
          </el-form-item>


          <el-form-item label="工点桩号" prop="workPointStakeNumber" class="inline-form-item">
            <el-input v-model="form.workPointStakeNumber" placeholder="请输入工点桩号"/>
<!--            <PileInput v-model="form.workPointStakeNumber" placeholder="请输入工点桩号"/>-->
          </el-form-item>





          <el-form-item label="坐标" prop="point" class="inline-form-item" >
<!--            <el-input v-model="form.point" placeholder="请输入坐标"/>-->

            <lon-lat
              type="lonlat"
              :lon.sync="point.longitude"
              :lat.sync="point.latitude"
            />
          </el-form-item>


          <el-form-item label="风险隐患情况" prop="riskHiddenDangerSituation" class="inline-form-item">
            <el-input v-model="form.riskHiddenDangerSituation" placeholder="请输入风险隐患情况"/>
<!--            <DictSelect type="backtrack_risk_level" v-model="form.riskHiddenDangerSituation"></DictSelect>-->
          </el-form-item>


          <el-form-item label="工点类型" prop="workPointType" class="inline-form-item">
            <el-input v-model="form.workPointType" placeholder="请输入工点类型"/>
<!--            <DictSelect type="backtrack_work_point_type"  v-model="form.workPointType"></DictSelect>-->
          </el-form-item>


          <el-form-item label="威胁对象" prop="threatTarget" class="inline-form-item">
            <el-input v-model="form.threatTarget" placeholder="请输入威胁对象"/>
          </el-form-item>

          <el-form-item label="工点来源" prop="workPointSource" class="inline-form-item">
            <el-input v-model="form.workPointSource" placeholder="请输入工点来源"/>
          </el-form-item>

          <el-form-item label="序号对应关系" prop="indexRelationship" class="inline-form-item">
            <el-input v-model="form.indexRelationship" placeholder="请输入序号对应关系"/>
          </el-form-item>



          <el-form-item label="工作计划" prop="workPlan">
            <el-input v-model="form.workPlan" placeholder="请输入工作计划"/>
          </el-form-item>





          <el-form-item label="重合情况" prop="overlappingSituation">
            <el-input v-model="form.overlappingSituation" placeholder="请输入重合情况"/>
          </el-form-item>

          <el-form-item label="工点概述" prop="workPointSummary">
            <el-input v-model="form.workPointSummary" type="textarea" :rows="3" placeholder="请输入内容"/>
          </el-form-item>
          <el-form-item label="应对措施及处治措施" prop="countermeasuresAndTreatmentMeasures">
            <el-input v-model="form.countermeasuresAndTreatmentMeasures" :rows="3" type="textarea" placeholder="请输入内容"/>
          </el-form-item>

        </div>
        <div class="infoBox" >
          <div class="infoTitle">
            具体实施方案
          </div>


<!--          <el-form-item label="方案类型" prop="projectType">-->
<!--            <el-input v-model="form.projectType" placeholder="请输入类型"/>-->

<!--            <DictSelect type="backtrack_project_type" v-model="form.projectType" multiple></DictSelect>-->
<!--          </el-form-item>-->

          <el-form-item label="工程处治" prop="engineeringTreatment">
            <el-input v-model="form.engineeringTreatment" type="textarea" placeholder="请输入内容"/>
          </el-form-item>


          <el-form-item label="监测" prop="monitoring">
            <el-input v-model="form.monitoring" type="textarea" placeholder="请输入内容"/>
          </el-form-item>


          <el-form-item label="预警" prop="warning">
            <el-input v-model="form.warning" type="textarea" placeholder="请输入内容"/>
          </el-form-item>



        </div>
        <div class="infoBox"  >
          <div class="infoTitle">
            估算费用
          </div>

          <el-form-item label="工程处置金额（万元）" prop="engineeringTreatmentAmount">
            <el-input v-model="form.engineeringTreatmentAmount"  v-number-only pattern="^\d*\.?\d*$" placeholder="请输入工程处治 金额"/>
          </el-form-item>
          <el-form-item label="监测预警金额（万元）" prop="monitoringAmount">
            <el-input v-model="form.monitoringAmount" v-number-only pattern="^\d*\.?\d*$" placeholder="请输入监测预警 金额"/>
          </el-form-item>

        </div>
        <div class="infoBox"  >
          <div class="infoTitle">
            列预算情况
          </div>
          <el-form-item label="列预算情况-具体情况" prop="engineeringTreatmentBudgetStatus">
            <el-input v-model="form.engineeringTreatmentBudgetStatus" placeholder="请输入列预算情况-具体情况"/>
          </el-form-item>
          <el-form-item label="列预算情况-金额（万元）" prop="monitoringBudgetStatus">
            <el-input  v-number-only v-model="form.monitoringBudgetStatus" placeholder="请输入列预算情况-金额（万元）"/>
          </el-form-item>



        </div>

        <div class="infoBox"  >
          <div class="infoTitle">
            其他
          </div>
          <el-form-item label="费用归口" prop="costOwnership">
            <el-input v-model="form.costOwnership" placeholder="请输入费用归口"/>
<!--            <DictSelect type="backtrack_cost_ownership" v-model="form.costOwnership" multiple></DictSelect>-->
          </el-form-item>


          <el-form-item label="权属" prop="ownership">
            <el-input v-model="form.ownership" placeholder="请输入权属"/>
<!--            <DictSelect type="backtrack_ownership" v-model="form.ownership"></DictSelect>-->
          </el-form-item>


          <el-form-item label="公路竣工验收情况" prop="highwayAcceptanceStatus">
<!--            <el-input v-model="form.highwayAcceptanceStatus" placeholder="请输入公路竣工验收情况"/>-->
            <el-select v-model="form.highwayAcceptanceStatus" style="width: 100%">
              <el-option value="已竣工验收">已竣工验收</el-option>
              <el-option value="未竣工验收">未竣工验收</el-option>
            </el-select>
          </el-form-item>


          <el-form-item label="处治情况" prop="treatmentStatus">
            <el-input v-model="form.treatmentStatus" placeholder="请输入处治情况"/>
          </el-form-item>


          <el-form-item label="备注" prop="remarks">
            <span slot="label">
                  <el-tooltip
                    content="备注"
                    placement="top">
                    <i class="el-icon-question"></i>
                  </el-tooltip>
                  备注
                </span>
            <el-input v-model="form.remarks" type="textarea" :rows="4" placeholder="请输入内容"/>
          </el-form-item>


        </div>
      </el-form>
      <div slot="footer" class="dialog-footer" style="display: flex; justify-content: center;">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>


<script>
import {listBacktrack, getBacktrack, delBacktrack, addBacktrack, updateBacktrack} from "@/api/backtrack/backtrack";
import {getToken} from "@/utils/auth";
import Treeselect from "@riophae/vue-treeselect";
import "@riophae/vue-treeselect/dist/vue-treeselect.css";
import PileInput from "@/views/system/route/pileinput.vue";
import {deptTreeSelect} from "@/api/tmpl";
import {listMaintenanceSectionAll} from "@/api/system/maintenanceSection";
import {listByMaintenanceSectionId} from "@/api/baseData/common/routeLine";
import {formatPile} from "@/utils/ruoyi";
import lonLat from "@/components/mapPosition/lonLat.vue";

export default {
  name: "Backtrack",
  components: {lonLat, PileInput},
  dicts: ['backtrack_project_type', 'backtrack_cost_ownership', 'backtrack_ownership', 'backtrack_project_category', 'backtrack_work_point_type', 'backtrack_risk_level'],
  data() {
    return {
      point: {},
      formMaintenanceList: [], // 管养单位列表
      formMaintenanceRenderList: [], // 管养单位渲染列表
      formRoadSectionList: [], // 路段列表
      formRouteList: [], // 路线列表
      queryShow: false, // 隐藏筛选显隐
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      dictType: [],
      // 总条数
      total: 0,
      // 设计回溯表格数据
      backtrackList: null,
      // 弹出层标题
      title: "",
      // 部门树选项
      deptOptions: undefined,
      // 是否显示弹出层
      open: false,

      // 表单参数
      form: {},
      defaultProps: {
        children: "children",
        label: "label"
      },
      // 用户导入参数
      upload: {
        // 是否显示弹出层（用户导入）
        open: false,
        // 弹出层标题（用户导入）
        title: "",
        // 是否禁用上传
        isUploading: false,
        // 是否更新已经存在的用户数据
        updateSupport: 0,
        // 设置上传的请求头部
        headers: {Authorization: "Bearer " + getToken()},
        // 上传的地址
        url: process.env.VUE_APP_BASE_API + "/system/user/importData"
      },
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 50,
        reportingUnitId: null,
        reportingUnitName: null,
        roadSectionId: null,
        roadSectionName: null,
        routeCode: null,
        projectName: null,
        constructUnit: null,
        operatingUnit: null,
        operatingUnitId: null,
        designUnit: null,
        designReviewUnit: null,
        workPointStakeNumber: null,
        workPointSummary: null,
        point: null,
        riskHiddenDangerSituation: null,
        workPointType: null,
        threatTarget: null,
        countermeasuresAndTreatmentMeasures: null,
        workPlan: null,
        workPointSource: null,
        overlappingSituation: null,
        indexRelationship: null,
        engineeringTreatment: null,
        monitoring: null,
        warning: null,
        projectType: [],
        engineeringTreatmentBudgetStatus: null,
        engineeringTreatmentAmount: null,
        monitoringBudgetStatus: null,
        monitoringAmount: null,
        costOwnership: null,
        ownership: null,
        highwayAcceptanceStatus: null,
        treatmentStatus: null,
        remarks: null
      },
      // 列信息
      columns: [
        {key: 0, label: `报送单位id`, visible: true},
        {key: 1, label: `报送单位`, visible: true},
        {key: 2, label: `路段id`, visible: true},
        {key: 3, label: `路段名称`, visible: true},
        {key: 4, label: `所属路线编号`, visible: true},
        {key: 5, label: `项目名称`, visible: true},
        {key: 6, label: `建设单位`, visible: true},
        {key: 7, label: `运营单位`, visible: true},
        {key: 8, label: `运营单位id`, visible: true},
        {key: 9, label: `设计单位`, visible: true},
        {key: 10, label: `设计回溯单位`, visible: true},
        {key: 11, label: `工点桩号`, visible: true},
        {key: 12, label: `工点概述`, visible: true},
        {key: 13, label: `坐标`, visible: true},
        {key: 14, label: `风险隐患情况`, visible: true},
        {key: 15, label: `工点类型`, visible: true},
        {key: 16, label: `威胁对象`, visible: true},
        {key: 17, label: `应对措施及处治措施`, visible: true},
        {key: 18, label: `工作计划`, visible: true},
        {key: 19, label: `工点来源`, visible: true},
        {key: 20, label: `重合情况`, visible: true},
        {key: 21, label: `序号对应关系`, visible: true},
        {key: 22, label: `工程处治`, visible: true},
        {key: 23, label: `监测`, visible: true},
        {key: 24, label: `预警`, visible: true},
        {key: 25, label: `类型`, visible: true},
        {key: 26, label: `工程处治 列预算情况`, visible: true},
        {key: 27, label: `工程处治 金额（万元）`, visible: true},
        {key: 28, label: `监测预警 列预算情况`, visible: true},
        {key: 29, label: `监测预警 金额（万元）`, visible: true},
        {key: 30, label: `费用归口`, visible: true},
        {key: 31, label: `权属`, visible: true},
        {key: 32, label: `公路竣工验收情况`, visible: true},
        {key: 33, label: `处治情况`, visible: true},
        {key: 34, label: `备注`, visible: true}
      ],
      // 表单校验
      rules: {
        operatingUnitId: [
          { required: true, message: '请选择运营单位', trigger: 'change' }
        ],
        roadSectionId: [
          { required: true, message: '请选择路段名称', trigger: 'change' }
        ],
        routeCode: [
          { required: true, message: '请输入所属路线编号', trigger: 'change' }
        ],
        projectName: [
          { required: true, message: '请输入项目名称', trigger: 'blur' }
        ],
        constructUnit: [
          { required: true, message: '请输入建设单位', trigger: 'blur' }
        ],
        designUnit: [
          { required: true, message: '请输入原设计单位', trigger: 'blur' }
        ],
        designReviewUnit: [
          { required: true, message: '请输入设计回溯单位', trigger: 'blur' }
        ],
        workPointStakeNumber: [
          { required: true, message: '请输入工点桩号', trigger: 'blur' }
        ],
        point: [
          { required: false, message: '请输入坐标', trigger: 'change' }
        ],
        riskHiddenDangerSituation: [
          { required: true, message: '请选择风险隐患情况', trigger: 'change' }
        ],
        workPointType: [
          { required: true, message: '请选择工点类型', trigger: 'change' }
        ],
        threatTarget: [
          { required: true, message: '请输入威胁对象', trigger: 'blur' }
        ],
        workPointSource: [
          { required: true, message: '请输入工点来源', trigger: 'blur' }
        ],
        indexRelationship: [
          { required: true, message: '请输入序号对应关系', trigger: 'blur' }
        ],
        workPlan: [
          { required: true, message: '请输入工作计划', trigger: 'blur' }
        ],
        overlappingSituation: [
          { required: true, message: '请输入重合情况', trigger: 'blur' }
        ],
        workPointSummary: [
          { required: true, message: '请输入工点概述', trigger: 'blur' }
        ],
        countermeasuresAndTreatmentMeasures: [
          { required: true, message: '请输入应对措施及处治措施', trigger: 'blur' }
        ],
        projectType: [
          { required: true, message: '请选择方案类型', trigger: 'change' }
        ],
        engineeringTreatment: [
          // { required: true, message: '请输入工程处治内容', trigger: 'blur' }
        ],
        monitoring: [
          // { required: true, message: '请输入监测内容', trigger: 'blur' }
        ],
        warning: [
          // { required: true, message: '请输入预警内容', trigger: 'blur' }
        ],
        engineeringTreatmentAmount: [
          // { required: true, message: '请输入工程处置金额', trigger: 'blur' }
        ],
        monitoringAmount: [
          // { required: true, message: '请输入监测预警金额', trigger: 'blur' }
        ],
        engineeringTreatmentBudgetStatus: [
          // { required: true, message: '请输入列预算情况-具体情况', trigger: 'blur' }
        ],
        monitoringBudgetStatus: [
          // { required: true, message: '请输入列预算情况-金额', trigger: 'blur' }
        ],
        costOwnership: [
          { required: true, message: '请选择费用归口', trigger: 'change' }
        ],
        ownership: [
          { required: true, message: '请选择权属', trigger: 'change' }
        ],
        highwayAcceptanceStatus: [
          { required: true, message: '请选择公路竣工验收情况', trigger: 'change' }
        ],
        treatmentStatus: [
          { required: true, message: '请输入处治情况', trigger: 'blur' }
        ],
        remarks: [
          // { required: true, message: '请输入备注', trigger: 'blur' }
        ]
      }
    };
  },
  watch: {
    // 根据名称筛选部门树
  },
  created() {
    this.getList();

    this.queryMaintenanceList()
  },
  directives: {
    'number-only': {
      bind: function (el) {
        const input = el.querySelector('input');
        input.addEventListener('input', function () {
          const pattern = /^\d*\.?\d*$/;
          if (!pattern.test(this.value)) {
            this.value = this.value.slice(0, -1);
          }
        });
      }
    }
  },
  methods: {
    formatPile,
    queryMaintenanceList() {
      deptTreeSelect({ types: 201 }).then((res) => {
        if (res.code === 200) {
          this.formMaintenanceRenderList = res.data
        }
      })
    },
    // 管养单位/路段改变时
    formMaintenanceChange(type) {
      switch (type) {
        case '1':
          // 获取管养单位名称
          this.formGetName('1', this.form.operatingUnitId)
          // 重置路段及路线数据
          this.form.roadSectionId = ''
          this.form.roadSectionName = ''
          this.formRoadSectionList = []
          this.form.routeCode = ''
          this.formRouteList = []
          // 重新获取路段数据列表
          this.queryMaintenanceSectionList(this.form.operatingUnitId)
          break;

        case '2':
          // 获取路段单位名称
          this.formGetName('2', this.form.roadSectionId)
          // 重置路线数据
          this.form.routeCode = ''
          this.formRouteList = []
          // 重新获取路线数据列表
          this.queryRouterList( this.form.roadSectionId)
          break;

        case '3':
          // 获取路段单位名称
          this.formGetName('3', this.formParams.routerNum)
          break;
      }
    },
    // 获取管养单位/路段/路线名称
    formGetName(type, val) {
      switch (type) {
        case '1':
          for (let i = 0; i < this.formMaintenanceRenderList.length; i++) {
            if (this.formMaintenanceRenderList[i].id == val) {
              this.form.operatingUnit= this.formMaintenanceRenderList[i].label
              break
            }
          }
          break;

        case '2':
          for (let i = 0; i < this.formRoadSectionList.length; i++) {
            if (this.formRoadSectionList[i].maintenanceSectionId == val) {
              this.form.roadSectionName = this.formRoadSectionList[i].maintenanceSectionName
              break
            }
          }
          break;

        case '3':
          for (let i = 0; i < this.formRouteList.length; i++) {
            if (this.formRouteList[i].routeCode == val) {
              this.formParams.routerName = this.formRouteList[i].routeName
              break
            }
          }
          break;
      }
    },
    // 获取路段信息
    queryMaintenanceSectionList(val) {
      listMaintenanceSectionAll({departmentIdList: val}).then((res) => {
        if (res.code === 200) {
          this.formRoadSectionList = res.data
        }
      })
    },

    // 获取路线信息
    queryRouterList(val) {
      listByMaintenanceSectionId({maintenanceSectionId: val}).then(res => {
        if (res.code == 200) {
          this.formRouteList = res.data
          if (this.formRouteList.length ===1){
           this.form.routeCode = this.formRouteList[0].routeCode
          }
        }
      })
    },
    /** 查询用户列表 */
    getList() {
      this.loading = true;
      listBacktrack(this.queryParams).then(response => {
        this.backtrackList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        reportingUnitId: null,
        reportingUnitName: null,
        roadSectionId: null,
        roadSectionName: null,
        routeCode: null,
        projectName: null,
        constructUnit: null,
        operatingUnit: null,
        operatingUnitId: null,
        designUnit: null,
        designReviewUnit: null,
        workPointStakeNumber: null,
        workPointSummary: null,
        point: null,
        riskHiddenDangerSituation: null,
        workPointType: null,
        threatTarget: null,
        countermeasuresAndTreatmentMeasures: null,
        workPlan: null,
        workPointSource: null,
        overlappingSituation: null,
        indexRelationship: null,
        engineeringTreatment: null,
        monitoring: null,
        warning: null,
        projectType: null,
        engineeringTreatmentBudgetStatus: null,
        engineeringTreatmentAmount: null,
        monitoringBudgetStatus: null,
        monitoringAmount: null,
        costOwnership: null,
        ownership: null,
        highwayAcceptanceStatus: null,
        treatmentStatus: null,
        remarks: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dateRange = [];
      this.resetForm("queryForm");
      this.resetForm("queryForm2");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id);
      this.single = selection.length != 1;
      this.multiple = !selection.length;
    },
    // 表格点击勾选
    handleRowClick(row) {
      row.isSelected = !row.isSelected;
      this.$refs.table.toggleRowSelection(row);
    },
    // 勾选高亮
    rowStyle({row, rowIndex}) {
      if (this.ids.includes(row.id)) {
        return {'background-color': '#E1F0FF', color: '#333'}
      } else {
        return {'background-color': '#fff', color: '#333'}
      }
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加设计回溯";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids;
      getBacktrack(id).then(response => {
        this.form = response.data;
        if (this.form.projectType )
          this.form.projectType = this.form.projectType === ''? []: this.form.projectType.split(",")
        if (this.form.costOwnership )
          this.form.costOwnership = this.form.costOwnership === '' ? []: this.form.costOwnership.split(",")
        if(this.form.point){
          this.point.longitude = this.form.point.split(',')[0]
            this.point.latitude = this.form.point.split(',')[1]
        }
        this.open = true;
        this.title = "修改设计回溯";
      });

    },
    /** 提交按钮 */
    submitForm: function () {
      // this.$refs["form"].validate(valid => {
      //   if (valid) {
          this.form.projectType && ( this.form.projectType instanceof Array) && (this.form.projectType = this.form.projectType.join(","))
          this.form.costOwnership && ( this.form.costOwnership instanceof Array)  && (this.form.costOwnership = this.form.costOwnership.join(","))
          if (this.point.longitude)
          this.form.point = this.point.longitude + ',' + this.point.latitude
          if (this.form.id != null) {
            updateBacktrack(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addBacktrack(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        // }
      // });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const id = row.id || this.ids;

      if(row.id){
        this.$modal.confirm('是否确认删除设计回溯编号为"' + id + '"的数据项？').then(function () {
          return delBacktrack(id);
        }).then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        }).catch(() => {
        });
      }else {

        if (this.ids.length > 0) {
          this.$modal.confirm('是否确认批量删除选中数据？').then( () => {
            let delArray = []
            this.ids.forEach(item => {
              delArray.push(delBacktrack(item))
            })
            Promise.all(delArray).then(() => {this.$modal.msgSuccess("删除成功");this.getList()})
          })
        }


      }


    },

    /** 导出按钮操作 */
    handleExport() {

      this.download('disaster/backtrack/export', {
        ...this.queryParams,
        ids: this.ids
      }, `设计回溯_${new Date().getTime()}.xlsx`)

    },
    /** 导入按钮操作 */
    handleImport() {
      this.upload.title = "用户导入";
      this.upload.open = true;
    },
    /** 下载模板操作 */
    importTemplate() {
      this.download('system/user/importTemplate', {}, `user_template.xlsx`)
    },
    // 文件上传中处理
    handleFileUploadProgress(event, file, fileList) {
      this.upload.isUploading = true;
    },
    // 文件上传成功处理
    handleFileSuccess(response, file, fileList) {
      this.upload.open = false;
      this.upload.isUploading = false;
      this.$refs.upload.clearFiles();
      this.$alert("<div style='overflow: auto;overflow-x: hidden;max-height: 70vh;padding: 10px 20px 0;'>" + response.msg + "</div>", "导入结果", {dangerouslyUseHTMLString: true});
      this.getList();
    },
    // 提交上传文件
    submitFileForm() {
      this.$refs.upload.submit();
    }
  }
};
</script>
<style scoped lang="scss">
.app-container {
  padding: 10px;
  background-color: #c0c0c0;
  box-sizing: border-box;
}

.searchBox {
  padding: 10px;
  background: #fff;
  border-radius: 10px;
  transition: all .1s linear;
  display: flex;
  flex-direction: column;

  .searchMoreBox {
    min-width: 192px;
    margin-top: 10px;
    display: flex;
    align-items: center;
    flex-direction: row;
  }

  ::v-deep .el-form-item {
    margin-bottom: 0px;
  }

}

.tableDiv {
  margin-top: 10px;
  background-color: white;
  padding-bottom: 10px;
  border-radius: 10px;
  transition: all .1s linear;
  display: flex;
  flex-direction: column;

  .btnBox {
    padding: 10px;
  }
}

// v-if过渡动画
// 查询框
.search-enter-active {
  transition: all .1s linear;
}

.search-enter {
  opacity: 0;
}

.search-leave-active {
  transition: all .1s linear;
}

.search-leave-to {
  opacity: 0;
}

.infoBox {
  padding: 25px 15px 0 15px;
  box-sizing: border-box;
  border-radius: 6px;
  border: 1px solid #C4C4C4;
  position: relative;
  margin-bottom: 40px !important;

  .infoTitle {
    user-select: none;
    position: absolute;
    top: 0;
    left: 0;
    padding: 0 10px;
    font-size: 14px;
    line-height: 14px;
    font-weight: bold;
    transform: translateX(15px) translateY(-50%);
    background-color: white;
  }
}

.formDialog {
  ::v-deep .el-dialog__body {
    height: 680px;
    overflow-y: auto;
  }

  .dialog-footer {
    width: 100%;

    .footerTip {
      color: #888888;
      font-size: 14px;
    }
  }

  .titleBox {
    height: 22px;
    display: flex;
    flex-direction: row;
    align-items: center;

    .title {
      font-size: 16px;
      color: black;
      margin: 0;
    }

    .subTitle {
      margin-left: 15px;
      font-size: 12px;
      color: #888888;
    }
  }
}

.inline-form-item {
  display: inline-block;
  box-sizing: border-box;
  width: 48%;
  margin-right: 2%; /* 设置项之间的间距 */
}

</style>
