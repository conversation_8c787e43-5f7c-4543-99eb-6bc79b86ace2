<template>
  <div>
    <div class="reportTree">
      <el-tree
          ref="reportTree"
          :data="data"
          node-key="code"
          :default-expanded-keys="expandArray"
          :empty-text="emptyText"
          :highlight-current="true"
          @node-click="handleNodeClick"
          v-loading="treeLoadAll"
          draggable
          :allow-drag="true"
          :allow-drop="allowDrop"
      >
              <span
                  class="custom-tree-node"
                  @mouseover="showButtonIcon(data)"
                  @mouseleave="hideButtonIcon(data)"
                  slot-scope="{ node, data }"
                  :class="[addIcon(data.type)+'_ctrl ']+[(data.nodeLevel === 8 && data.algorithmName > 4 &&
                   data.usageMode === 0 && data.children.length < 2) ? 'highLight ' : ' '] +
                   [(data.nodeLevel === 9 && node.parent.data.usageMode === 0) ? 'sensorLineForDraw' : ' '] +
                  [(data.nodeLevel === 9 && node.parent.data.usageMode === 1) ? 'sensorLineForTable' : ' ']"
                  >
                <span class="label" :class="[showBigFont(addIcon(data.type))?'font16':'smallFont']">
                  <span class="label flexList" :class="[showBigFont(addIcon(data.type))?'font16':'smallFont']">
                  <span class="el-icon-circle-plus icons openIcon" v-if="addIconShow(addIcon(data.type))" ></span>
                  <span class="el-icon-remove icons closeIcon" v-if="addIconShow(addIcon(data.type))" ></span>

                  <span v-if="data.nodeLevel === 5">
                      <span  :class="structureType(data.type)" ></span>
                  </span>
                    <span :class="[treeiconBox[data.content]||'icon-cube']" class="iconfont proIcon" v-if="data.type === '监测类型'" ></span>
                    <span :class="['iconStyle', 'iconfont']"></span>
                    <span v-if="data.nodeLevel === 8">
                      {{data.content}}
                      <span v-if="data.usageMode === 0">{{"-"+ (algorithmList[data.algorithmName].type)}}</span>
                      <span v-if="data.usageMode === 0 && data.granularityType !== 0">{{"-"+ (data.granularityNum) + (granularityTypeListForDisplay[data.granularityType].label)}}</span>
                      <span v-if="data.usageMode === 0 && data.granularityType === 0">{{"-"+ (granularityTypeListForDisplay[data.granularityType].label)}}</span>
                    </span>
                    <span v-if="data.nodeLevel < 8">{{data.content}}</span>
                    <el-tag v-if="data.usageMode === 0" size="mini">绘图</el-tag>
                    <el-tag v-if="data.usageMode === 1" type="success" size="mini">表格</el-tag>
                    <span v-if="node.parent.data.usageMode === 0 && data.nodeLevel === 9">
                      <!-- 要求两列数据的绘图类型时 添加的tag提示-->
                      <span v-if="node.parent.data.algorithmName === 5 || node.parent.data.algorithmName === 8">
                        <span v-if="node.parent.data.children.length === 1">
                          <el-tag v-if="node.parent.data.children[0].id === data.id" size="mini" effect="dark">风速</el-tag>
                        </span>
                         <span v-if="node.parent.data.children.length >= 2">
                          <el-tag v-if="node.parent.data.children[0].id === data.id" size="mini" effect="dark">风速</el-tag>
                          <el-tag v-if="node.parent.data.children[1].id === data.id"  type="success" size="mini" effect="dark">风向</el-tag>
                        </span>
                      </span>
                      <span v-if="node.parent.data.algorithmName === 6">
                        <span v-if="node.parent.data.children.length === 1">
                          <el-tag v-if="node.parent.data.children[0].id === data.id" size="mini" effect="dark">X轴</el-tag>
                        </span>
                         <span v-if="node.parent.data.children.length >= 2">
                          <el-tag v-if="node.parent.data.children[0].id === data.id" size="mini" effect="dark">X轴</el-tag>
                          <el-tag v-if="node.parent.data.children[1].id === data.id"  type="success" size="mini" effect="dark">Y轴</el-tag>
                        </span>
                      </span>
                      <span v-if="node.parent.data.algorithmName === 7">
                        <span v-if="node.parent.data.children.length === 1">
                          <el-tag v-if="node.parent.data.children[0].id === data.id" size="mini" effect="dark">X轴</el-tag>
                        </span>
                         <span v-if="node.parent.data.children.length >= 2">
                          <el-tag v-if="node.parent.data.children[0].id === data.id" size="mini" effect="dark">X轴</el-tag>
                          <el-tag v-if="node.parent.data.children[1].id === data.id"  type="success" size="mini" effect="dark">Y轴</el-tag>
                        </span>
                      </span>
                      <!-- 层级9即传感器层级时，通常显示名称-->
                      <span style="font-weight:bolder">{{data.specificMonitorTypeName + data.sensorInstallCode}}</span>
                      <span>
                      {{"\n"}}
                      <span class="sensorLineItem">
                        {{"采样方式：" + (data.eigenvalueType.name) }}
                      </span>
                      <span class="sensorLineItem">
                        {{"特征值种类："}}<span v-for="item in data.eigenvalueTypeList">{{item.name}}
                      </span>
                      </span>
                      <span class="sensorLineItem">
                        {{"指定y轴：" +  (yAxisTypeList[data.yAxisType].type)}}
                      </span>
                      <span class="sensorLineItem">
                        {{"曲线颜色：" +  (findColorName(data.color))}}
                        <span :style="{'float': 'inline-end', 'background':data.color, 'height': '24px', 'width':'24px'}"></span>
                      </span>
                      {{"\n"}}
                      <span class="sensorLineItem">
                        {{"传感器名称：" + (data.content)}}
                      </span>
                      {{"\n"}}
                      <span class="sensorLineItem">
                        {{"启用安装时刻："}}
                        <el-tag v-if="data.useInstallTime === 1"  type="success" size="mini">启用</el-tag>
                        <el-tag v-if="data.useInstallTime !== 1"  type="warning" size="mini">未启用</el-tag>
                        <span v-if="data.useInstallTime === 1" class="sensorLineItem">
                          {{"安装时刻：" + data.installTime}}
                        </span>
                      </span>
                      {{"\n"}}
                      <span class="sensorLineItem">
                        {{"异常值剔除："}}
                        <el-tag v-if="data.use3sigma === 1"  type="success" size="mini">启用</el-tag>
                        <el-tag v-if="data.use3sigma !== 1"  type="warning" size="mini">未启用</el-tag>
                        <span v-if="data.use3sigma === 1" class="sensorLineItem">
                          {{"窗口大小：" + data.windowLength}}
                        </span>
                        <span v-if="data.use3sigma === 1" class="sensorLineItem">
                          {{"异常值插值："}}
                          <el-tag v-if="data.sigmaInterpolate === 1"  type="success" size="mini">启用</el-tag>
                          <el-tag v-if="data.sigmaInterpolate !== 1"  type="warning" size="mini">未启用</el-tag>
                        </span>
                      </span>
                      {{"\n"}}
                      <span class="sensorLineItem">
                        {{"启用上下限："}}
                        <el-tag v-if="data.useUpDownLimit === 1"  type="success" size="mini">启用</el-tag>
                        <el-tag v-if="data.useUpDownLimit !== 1"  type="warning" size="mini">未启用</el-tag>
                        <span v-if="data.useUpDownLimit === 1" class="sensorLineItem">
                          {{"上限：" + data.upLimit}}
                        </span>
                        <span v-if="data.useUpDownLimit === 1" class="sensorLineItem">
                          {{"下限：" + data.downLimit}}
                        </span>
                        <span v-if="data.useUpDownLimit === 1" class="sensorLineItem">
                          {{"上下限插值："}}
                          <el-tag v-if="data.upDownInterpolate === 1"  type="success" size="mini">启用</el-tag>
                          <el-tag v-if="data.upDownInterpolate !== 1"  type="warning" size="mini">未启用</el-tag>
                        </span>
                      </span>
                      </span>
                    </span>
                    <span v-if="node.parent.data.usageMode === 1 && data.nodeLevel === 9" class="sensor-Label">
                      <span style="font-weight:bolder">{{data.specificMonitorTypeName + data.sensorInstallCode}}</span>
                      <span class="sensorLineItem">
                        {{"特征值种类："}}<span v-for="item in data.eigenvalueTypeList">{{item.name}}
                      </span>
                      </span>
                    </span>
                  </span>
                </span>
                <span v-show="!(data.useFlag === null ? true : data.useFlag)" class="icon-buttom">
                  <!--编辑-->
                  <span
                      slot="reference"
                      class="iconfont el-icon-edit-outline"
                      @click.stop="showEditDialog(data, node)"
                      style="font-weight:bold;margin-right: 5px;"
                      title="编辑"
                  >编辑</span>
                  <!--复制-->
                  <span
                      slot="reference"
                      class="iconfont"
                      @click.stop="copyNode(data, node)"
                      style="font-weight:bold;margin-right: 5px;color:dodgerblue;"
                      title="复制"
                  >复制</span>
                  <span
                      slot="reference"
                      class="iconfont icon-zengjia icon-buttom-item btnAddColor"
                      @click.stop="showAddDialog(data, node)"
                      v-show="data.nodeLevel !== 9"
                      style="font-weight:bold;margin-right: 5px;"
                      title="新增"
                  ></span>
                  <el-popconfirm
                      confirm-button-text="确定"
                      cancel-button-text="取消"
                      confirm-button-type="text"
                      icon="el-icon-info"
                      icon-color="red"
                      :title="'确定要删除该节点吗?'"
                      @confirm="removeNode(node, data)"
                  >
                    <span
                        slot="reference"
                        class="el-icon-delete btnDelColor"
                        @click.stop
                        title="删除"
                        style="font-weight:bold;font-size:14px;">
                    </span>
                  </el-popconfirm>
                </span>
              </span>
      </el-tree>
    </div>

    <!--    普通层级编辑对话框-->
    <el-dialog :visible.sync="editDialogVisible" width="500px" center append-to-body>
      <span slot="title" class="title">
        编辑内容
      </span>
        <div>
          <span class="label">节点内容</span>
          <el-input
              size="mini"
              class="monitor-content"
              v-model="editingNode.content"
              placeholder="请输入内容"
          ></el-input>
        </div>
        <span slot="footer" class="dialog-footer">
        <el-button size="mini" @click="editDialogVisible = false"
        >取 消</el-button
        >
        <el-button size="mini" type="primary" @click="editNode()"
        >确 定</el-button
        >
      </span>
    </el-dialog>

    <!--    监测内容层级编辑对话框-->
    <el-dialog :visible.sync="editDialogVisibleForContent" width="500px" center append-to-body>
      <span slot="title" class="title">
        编辑内容
      </span>
      <div>
        <el-form
            :model="editingNode"
            :inline="true"
            label-position="right"
            label-width="120px"
            ref="editRuleForm"
            label-suffix="："
        >
          <el-form-item label="节点内容" prop="content">
            <el-input
                size="small"
                placeholder="请输入内容"
                v-model="editingNode.content"
                clearable
            ></el-input>
          </el-form-item>
          <el-form-item label="备注" prop="description">
            <el-input
                type="textarea"
                autosize
                placeholder="请输入内容"
                v-model="editingNode.description"
                clearable
            ></el-input>
          </el-form-item>
        </el-form>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button size="mini" @click="editDialogVisibleForContent = false"
        >取 消</el-button
        >
        <el-button size="mini" type="primary" @click="editNodeForContent()"
        >确 定</el-button
        >
      </span>
    </el-dialog>

    <!--    "监测位置"层级编辑对话框-->
    <el-dialog :visible.sync="editDialogVisibleForLoc" width="580px" center append-to-body>
      <span slot="title" class="title">
        编辑内容
      </span>
      <div>
        <el-form
            :model="editingNode"
            :inline="true"
            :rules="edit_set_rule"
            label-position="right"
            label-width="120px"
            ref="editRuleForm"
            label-suffix="："
        >
          <el-form-item label="节点内容" prop="content">
            <el-input
                size="small"
                placeholder="请输入内容"
                v-model="editingNode.content"
                clearable
            ></el-input>
          </el-form-item>
          <el-form-item label="使用方式" prop="usageMode">
            <el-select
                v-model="editingNode.usageMode"
                no-data-text="无数据"
                placeholder="请选择"
                size="small"
                value-key="index"
            >
              <el-option
                  v-for="item in usageModeList"
                  :label="item.type"
                  :key="item.index"
                  :value="item.index"
              >
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="绘图种类" prop="algorithmName" v-show="editingNode.usageMode === 0">
            <el-select
                v-model="editingNode.algorithmName"
                no-data-text="无数据"
                placeholder="请选择"
                size="small"
                value-key="index"
                @change="changeAlgorithm"
            >
              <el-option
                  v-for="item in algorithmList"
                  :label="item.type"
                  :key="item.index"
                  :value="item.index"
              >
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="采样周期" prop="granularityNum" v-show="editingNode.usageMode === 0">
            <el-input
                v-model="editingNode.granularityNum"
                type="number"
                size="small"
                min="1"
                @input="editingNode.granularityNum=editingNode.granularityNum.replace(/^(0+)|[^\d]+/g,'')"
                style="width: 100px"></el-input>
          </el-form-item>
          <el-form-item label="周期单位" prop="granularityType" v-show="editingNode.usageMode === 0">
            <el-select
                v-model="editingNode.granularityType"
                value-key="index"
                style="width: 100px"
                placeholder="请选择"
                size="small"
            >
              <el-option
                  v-for="item in granularityTypeList"
                  :label="item.label"
                  :key="item.index"
                  :value="item.index"
              >
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="标题名称" prop="title" v-show="editingNode.usageMode === 0">
            <el-input
                size="small"
                style="width: 400px;"
                v-model="editingNode.title"
                placeholder="请输入内容"
            ></el-input>
          </el-form-item>
          <el-form-item label="左侧y轴名称" prop="leftAxisName" v-show="editingNode.usageMode === 0">
            <el-input
                size="small"
                style="width: 400px;"
                v-model="editingNode.leftAxisName"
                placeholder="请输入内容"
            ></el-input>
          </el-form-item>
          <el-form-item label="右侧y轴名称" prop="rightAxisName" v-show="editingNode.usageMode === 0">
            <el-input
                size="small"
                style="width: 400px;"
                v-model="editingNode.rightAxisName"
                placeholder="请输入内容"
            ></el-input>
          </el-form-item>
          <el-form-item label="表格页面方向" prop="tableDirection" v-show="editingNode.usageMode === 1">
            <el-select
                v-model="editingNode.tableDirection"
                no-data-text="无数据"
                placeholder="请选择"
                size="small"
                value-key="index"
            >
              <el-option
                  v-for="item in tableDirectionList"
                  :label="item.type"
                  :key="item.index"
                  :value="item.index"
              >
              </el-option>
            </el-select>
          </el-form-item>
        </el-form>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button size="mini" @click="editDialogVisibleForLoc = false"
        >取 消</el-button
        >
        <el-button size="mini" type="primary" @click="editNodeForLoc('editRuleForm')"
        >确 定</el-button
        >
      </span>
    </el-dialog>

    <!--    "传感器"层级编辑对话框-->
    <el-dialog :visible.sync="editDialogVisibleForSensor" width="500px" center append-to-body>
      <span slot="title" class="title">
        编辑传感器内容
      </span>
      <div>
        <el-form
            :model="editingNode"
            :inline="true"
            label-position="right"
            label-width="140px"
            label-suffix="："
            :rules="editSensor_set_rule"
            ref="editSensorRuleForm"
        >
          <el-form-item label="当前传感器" prop="currentSensor">
            <span style="margin-right: 10px;">{{editingNode.specificMonitorTypeName + editingNode.sensorInstallCode}}</span>
            <el-button size="mini" type="primary" @click="changeSensor()">更 换</el-button>
          </el-form-item>
          <el-form-item label="采样方式" prop="eigenvalueType">
            <el-select
                v-model="editingNode.eigenvalueType"
                no-data-text="无数据"
                placeholder="请选择"
                size="small"
                value-key="type"
            >
              <el-option
                  v-for="item in eigenvalueTypeList"
                  :label="item.name"
                  :key="item.type"
                  :value="item"
              >
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="特征值种类" prop="eigenvalueTypeList">
            <el-select
                v-model="editingNode.eigenvalueTypeList"
                no-data-text="无数据"
                placeholder="请选择"
                size="small"
                value-key="type"
                multiple
            >
              <el-option
                  v-for="item in eigenvalueTypeList1"
                  :label="item.name"
                  :key="item.type"
                  :value="item"
              >
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="指定y轴" prop="yAxisType" v-show="currentEditNodeUsageMode === 0">
            <el-select
                v-model="editingNode.yAxisType"
                no-data-text="无数据"
                placeholder="请选择"
                size="small"
                value-key="index"
            >
              <el-option
                  v-for="item in yAxisTypeList"
                  :label="item.type"
                  :key="item.index"
                  :value="item.index"
              >
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="曲线颜色" prop="color">
            <el-select
                v-model="editingNode.color"
                no-data-text="无数据"
                placeholder="请选择"
                size="small"
                value-key="ENName"
            >
              <el-option
                  v-for="item in colorList"
                  :label="item.CNName"
                  :key="item.ENName"
                  :value="item.ENName"
              >
                <span style="float: left">{{ item.CNName }}</span>
                <span :style="{'float': 'right', 'background':item.ENName, 'margin':'5px 0', 'height': '24px', 'width':'24px'}"></span>
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="名称选择" prop="labelType">
            <el-select
                v-model="editingNode.labelType"
                no-data-text="无数据"
                placeholder="请选择"
                size="small"
                value-key="index"
                @change="changeSensorContent"
            >
              <el-option
                  v-for="item in labelTypeList"
                  :label="item.type"
                  :key="item.index"
                  :value="item.index"
              >
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="传感器名称" prop="content">
            <el-input
                size="small"
                style="width: 250px;"
                v-model="editingNode.content"
                placeholder="请输入内容"
            ></el-input>
          </el-form-item>
          <el-form-item label="启用安装时刻" prop="useInstallTime">
            <el-switch
                v-model="editingNode.useInstallTime"
                :active-value="1"
                :inactive-value="0">
            </el-switch>
          </el-form-item>
          <el-form-item label="安装时刻" prop="installTime" v-show="editingNode.useInstallTime === 1">
            <DatePicker
                type="date"
                placeholder="选择日期"
                :value="editingNode.installTime"
                format="yyyy-MM-dd"
                @on-change="statusDateChange"
            ></DatePicker>
          </el-form-item>
          <el-form-item label="异常值剔除" prop="use3sigma">
            <el-switch
                v-model="editingNode.use3sigma"
                :active-value="1"
                :inactive-value="0">
            </el-switch>
          </el-form-item>
          <el-form-item label="窗口大小" prop="windowLength" v-show="editingNode.use3sigma === 1">
            <el-input
                v-model="editingNode.windowLength"
                type="number"
                size="small"
                min="1"
                @input="editingNode.windowLength=editingNode.windowLength.replace(/^(0+)|[^\d]+/g,'')"
                style="width: 100px"></el-input>
          </el-form-item>
          <el-form-item label="异常值插值" prop="sigmaInterpolate" v-show="editingNode.use3sigma === 1">
            <el-switch
                v-model="editingNode.sigmaInterpolate"
                :active-value="1"
                :inactive-value="0">
            </el-switch>
          </el-form-item>
          <el-form-item label="启用上下限" prop="useUpDownLimit">
            <el-switch
                v-model="editingNode.useUpDownLimit"
                :active-value="1"
                :inactive-value="0">
            </el-switch>
          </el-form-item>
          <el-form-item label="上限" prop="upLimit" v-show="editingNode.useUpDownLimit === 1">
            <el-input
                v-model="editingNode.upLimit"
                type="number"
                size="small"
                style="width: 100px"></el-input>
          </el-form-item>
          <el-form-item label="下限" prop="downLimit" v-show="editingNode.useUpDownLimit === 1">
            <el-input
                v-model="editingNode.downLimit"
                type="number"
                size="small"
                style="width: 100px"></el-input>
          </el-form-item>
          <el-form-item label="上下限插值" prop="upDownInterpolate" v-show="editingNode.useUpDownLimit === 1">
            <el-switch
                v-model="editingNode.upDownInterpolate"
                :active-value="1"
                :inactive-value="0">
            </el-switch>
          </el-form-item>
        </el-form>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button size="mini" @click="editDialogVisibleForSensor = false"
        >取 消</el-button
        >
        <el-button size="mini" type="primary" @click="editNodeForSensor('editSensorRuleForm')"
        >确 定</el-button
        >
      </span>
    </el-dialog>

    <!--    普通层级新增对话框-->
    <el-dialog :visible.sync="addDialogVisible" width="500px" center append-to-body>
      <span slot="title" class="title">
        新增子节点
      </span>
      <div>
        <span class="label">子节点内容：</span>
        <el-input
            size="mini"
            class="monitor-content"
            v-model="addNodeContent"
            placeholder="请输入内容"
        ></el-input>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button size="mini" @click="addDialogVisible = false">取 消</el-button>
        <el-button size="mini" type="primary" @click="addNode()">确 定</el-button>
      </span>
    </el-dialog>

    <!--    "传感器"层级新增对话框-->
    <SensorSelectTree :monitorData="monitorData" :selectNewSensorCallBack="addSensorNode" :expand-array="expandArrForSelect"></SensorSelectTree>

  </div>

</template>

<script>
import { cloneDeep } from "lodash";
import { edit_set_rule, addSensor_set_rule, editSensor_set_rule} from "./config";
import {
  addContentProp,
  addLocationProp,
  addSensorProp,
  copyProp, copySensorProp
} from "./utils";
import SensorSelectTree from "./SensorSelectTree.vue";
import {colorList} from "./utils";

export default {
  name: "DataCompositionTree",
  components: {SensorSelectTree},
  props: ['data', 'treeLoadAll', 'sensorInfoList', 'expandArray', "currentStructure", "expandArrForSelect", "monitorData"],
  data() {
    return {
      // dialog是否显示
      editDialogVisible: false,
      addDialogVisible: false,
      editDialogVisibleForContent: false,
      editDialogVisibleForLoc: false,
      editDialogVisibleForSensor: false,
      edit_set_rule: edit_set_rule,
      addSensor_set_rule: addSensor_set_rule,
      editSensor_set_rule: editSensor_set_rule,
      isChangingSensor: false,

      // 无数据时显示文字
      emptyText: "暂无数据",

      currentEditData: {},
      currentEditNode: {},
      currentEditNodeUsageMode: '',
      editingNode: {},
      addingNode: {},
      addNodeContent: '',
      currentAddData: {},
      currentAddNode: {},
      addSensorNodeInfoLabel: '',
      addSensorNodeInfo: {},
      tmpCode: 0,
      eigenvalueTypeList: [{type: 'MEAN', name: "平均值", value: 0}, {type: "MAX", name: "最大值", value: 0}, {type: "MIN", name: "最小值", value: 0},
        {type: "MEDIAN", name: "中值", value: 0}, {type: "MEAN_SQUARE", name: "均方差", value: 0}, {type: "ROOT_MEAN_SQUARE", name: "均方根", value: 0},
        {type: "FIRST", name: "第一条", value: 0}, {type: "LAST", name: "最后一条", value: 0}, {type: "MAX_DELTA", name: "最大差", value: 0},
        {type: "ABSOLUTE_MAX", name: "绝对最大值", value: 0}, {type: "ABSOLUTE_CUMULATE", name: "绝对值累积量", value: 0}],
      eigenvalueTypeList1: [{type: 'MEAN', name: "平均值", value: 0}, {type: "MAX", name: "最大值", value: 0}, {type: "MIN", name: "最小值", value: 0},
        {type: "MEDIAN", name: "中值", value: 0}, {type: "MEAN_SQUARE", name: "均方差", value: 0}, {type: "ROOT_MEAN_SQUARE", name: "均方根", value: 0},
        {type: "FIRST", name: "第一条", value: 0}, {type: "LAST", name: "最后一条", value: 0}, {type: "MAX_DELTA", name: "最大差", value: 0},
        {type: "ABSOLUTE_MAX", name: "绝对最大值", value: 0}, {type: "CDW", name: "累计下挠量", value: 0}, {type: "ABSOLUTE_CUMULATE", name: "绝对值累积量", value: 0}],
      algorithmList: [{index: 0, type: "时程图"}, {index: 1, type: "湿度概率直方图"}, {index: 2, type: "车速概率直方图"},
                      {index: 3, type: "超重车辆分布图"}, {index: 4, type: "风级概率分布图"}, {index: 5, type: "风玫瑰图"},
                      {index: 6, type: "相关性分析图"}, {index: 7, type: "均值散点图"}, {index: 8, type: "风速时程图"}],
      labelTypeList: [{index: 0, type: "sensorId"}, {index: 1, type: "特征值名称"}, {index: 2, type: "传感器位置"}, {index: 3, type: "监测类型名称"}
                      , {index: 4, type: "报告展示名称"}],
      colorList: colorList,
      tableDirectionList: [{index: 0, type: "纵向"}, {index: 1, type: "横向"}],
      granularityTypeList: [{index: 0, label: '原始'}, {index: 3, label: '分'},
        {index: 4, label: '时'}, {index: 5, label: '天'}, {index: 6, label: '周'}, {index: 7, label: '月'}, {index: 8, label: '年'}],
      granularityTypeListForDisplay: [{index: 0, label: '原始'}, {index: 1, label: '分'}, {index: 2, label: '分'}, {index: 3, label: '分'},
        {index: 4, label: '时'}, {index: 5, label: '天'}, {index: 6, label: '周'}, {index: 7, label: '月'}, {index: 8, label: '年'}],
      analysisTypeList: [{index: 0, label: '最大值'}, {index: 1, label:'最小值'}, {index: 2, label: '均值'}],
      usageModeList: [{index: 0, type: '绘图'}, {index: 1, type:'表格'}],
      yAxisTypeList: [{index: 0, type: '左侧'}, {index: 1, type:'右侧'}],
    }
  },
  methods: {
    // 增加Icon
    addIcon(type) {
      return addIcon(type);
    },

    // 点击展示树结构
    handleNodeClick(data, node) {
      // console.log("data:", data);
    },

    //显示操作按钮
    showButtonIcon(data) {
      data.useFlag = false;
    },
    //隐藏操作按钮
    hideButtonIcon(data) {
      data.useFlag = true;
      data.isShowButtom = false;
      // this.addNodeVisible = false
    },

    // 删除节点
    removeNode(node, data) {
      const parent = node.parent;
      const children = parent.data.children || parent.data;
      let index = '';
      if(data.nodeLevel === 9){
        // nodeLevel=9为传感器节点，code有重复，用id来做区分
        index = children.findIndex((d) => d.id === data.id);
      }else {
        index = children.findIndex((d) => d.code === data.code);
      }
      children.splice(index, 1);
      this.$message({
        message: "删除该节点成功!",
        type: "success",
      });
    },

    // 显示编辑信息的弹框
    showEditDialog(data, node) {
      this.currentEditNodeUsageMode = node.parent.data.usageMode
      this.currentEditData = data
      this.editingNode = cloneDeep(this.currentEditData)
      if(data.nodeLevel === 7){
        this.editDialogVisibleForContent = true
      }else if(data.nodeLevel === 8){
        this.editDialogVisibleForLoc = true
      }else if(data.nodeLevel === 9){
        this.editDialogVisibleForSensor = true
      } else {
        this.editDialogVisible = true
      }
    },

    // 按下普通层级编辑对话框的确认按钮
    editNode() {
      if(!this.editingNode.content || this.editingNode.content === ''){
        this.$message({
          message: "名称不能为空！",
          type: "warning",
        });
        return;
      }
      this.currentEditData.content = this.editingNode.content
      this.editDialogVisibleFor = false
      this.$message({
        message: "编辑" + this.currentEditData.content + "成功!",
        type: "success",
      });
    },

    // 按下"监测位置"层级编辑对话框的确认按钮
    editNodeForContent() {
      if(!this.editingNode.content || this.editingNode.content === ''){
        this.$message({
          message: "名称不能为空！",
          type: "warning",
        });
        return;
      }else {
        copyProp(this.editingNode, this.currentEditData)
        this.editDialogVisibleForContent = false
        this.$message({
          message: "编辑" + this.currentEditData.content + "成功!",
          type: "success",
        });
      }
    },

    // 按下"监测位置"层级编辑对话框的确认按钮
    editNodeForLoc(formName) {
      this.$refs[formName].validate(async (valid) => {
        if (valid) {
          if(this.editingNode.granularityNum === ''){
            this.$message({
              message: "采样周期不能为空！",
              type: "warning",
            });
            return;
          }else {
            copyProp(this.editingNode, this.currentEditData)
            this.editDialogVisibleForLoc = false
            this.$message({
              message: "编辑" + this.currentEditData.content + "成功!",
              type: "success",
            });
          }
        } else {
          return false;
        }
      });
    },

    // 按下"传感器"层级编辑对话框确认按钮
    editNodeForSensor(formName) {
      this.$refs[formName].validate(async (valid) => {
        if (valid) {
          copyProp(this.editingNode, this.currentEditData)
          this.editDialogVisibleForSensor = false
          this.$message({
            message: "编辑" + this.currentEditData.specificMonitorTypeName + this.currentEditData.sensorInstallCode + "成功!",
            type: "success",
          });
        } else {
          return false;
        }
      });

    },

    // 新增子节点
    showAddDialog(data, node) {
      this.currentAddData = data
      this.currentAddNode = node
      if(data.nodeLevel === 8){
        this.isChangingSensor = false;
        window.$Bus.$emit("showSensorTree");
      }else {
        this.addDialogVisible = true
      }
    },

    // 按下新增对话框的确认按钮
    addNode() {
      if(!this.addNodeContent || this.addNodeContent.length === 0){
          this.$message({
              message: "子节点内容不能为空",
              type: "warning",
          });
          return;
      }
      const tmpCode = new Date().getTime().toString()
      let newChild = {}
      if(this.currentAddData.nodeLevel === 7){
        // 新增了一个"监测位置"
        newChild = {
          content: this.addNodeContent,
          code: tmpCode,
          type: "用户自行添加",
          useFlag: null,
          iconStyle: addIcon("用户自行添加"),
          visible: false,
          children: [],
          nodeLevel: this.currentAddData.nodeLevel + 1,
          parentCode: this.currentAddData.code,
          title: this.currentStructure.label + this.addNodeContent + this.currentAddNode.parent.data.content + "变化曲线图",
        };
        addLocationProp(newChild, this)
      }else if(this.currentAddData.nodeLevel === 6){
        // 新增了一个"监测内容"
        newChild = {
          content: this.addNodeContent,
          code: tmpCode,
          type: "用户自行添加",
          useFlag: null,
          iconStyle: addIcon("用户自行添加"),
          visible: false,
          children: [],
          nodeLevel: this.currentAddData.nodeLevel + 1,
          parentCode: this.currentAddData.code,
        };
        addContentProp(newChild, this)
      }else{
        // 新增一个普通节点
        newChild = {
          content: this.addNodeContent,
          code: tmpCode,
          type: "用户自行添加",
          useFlag: null,
          iconStyle: addIcon("用户自行添加"),
          visible: false,
          children: [],
          nodeLevel: this.currentAddData.nodeLevel + 1,
          parentCode: this.currentAddData.code,
        };
      }
      if (!this.currentAddData.children) {
        this.$set(this.currentAddData, 'children', []);
      }
      this.currentAddData.children.push(newChild);
      this.addDialogVisible = false
      this.$message({
        message: "新增" + this.addNodeContent + "成功!",
        type: "success",
      });
    },

    // 按下新增传感器对话框的确认按钮
    addSensorNode(addingNode) {
      if(this.isChangingSensor){
        const newSensor = cloneDeep(addingNode);
        addSensorProp(newSensor, this);
        copySensorProp(newSensor, this.editingNode);
        copyProp(newSensor, this.editingNode)
        this.changeSensorContent(this.editingNode.labelType)
      }else {
        const tmpCode = new Date().getTime().toString()
        const newChild = cloneDeep(addingNode);
        newChild.id = tmpCode;
        this.$set(newChild, 'parentCode', this.currentAddData.code);
        addSensorProp(newChild, this)
        newChild.color = this.seekNoUseColor() // 分配一个不同的颜色
        newChild.content = newChild.specificMonitorTypeName + newChild.sensorInstallCode
        if (!this.currentAddData.children) {
          this.$set(this.currentAddData, 'children', []);
        }
        this.currentAddData.children.push(newChild);
        this.addDialogVisible = false
        this.$message({
          message: "新增" + newChild.specificMonitorTypeName + newChild.sensorInstallCode + "成功!",
          type: "success",
        });
      }
    },
    // 复制节点
    copyNode(data, node) {
      const parent = node.parent;
      const children = parent.data.children || parent.data;
      let index = 0;
      let newData = cloneDeep(data)
      const tmpCode = new Date().getTime().toString()
      if(data.nodeLevel === 9){
        // nodeLevel=9为传感器节点，code有重复，用id来做区分
        index = children.findIndex((d) => d.id === data.id);
        newData.id = tmpCode
      }else {
        index = children.findIndex((d) => d.code === data.code);
        newData.code = tmpCode
      }
      children.splice(index+1, 0, newData);
      this.$message({
        message: "复制该节点成功!",
        type: "success",
      });
    },
    // 传感器选择
    changeSensor() {
      this.isChangingSensor = true;
      window.$Bus.$emit("showSensorTree");
    },
    // 更改传感器名称
    changeSensorContent(e) {
      if(e === 0){
        this.editingNode.content = this.editingNode.specificMonitorTypeName + this.editingNode.sensorInstallCode
      }else if(e === 1){
        this.editingNode.content = this.editingNode.eigenvalueType.name
      }else if(e === 2){
        this.editingNode.content = this.editingNode.installLocation
      }else if(e === 3){
        this.editingNode.content = this.editingNode.specificMonitorTypeName
      }else if(e === 4){
        this.editingNode.content = this.editingNode.reportDisplayName
      }
    },
    // 更改绘图算法
    changeAlgorithm(e) {
      // 因为绘图种类一般牵扯数据粒度，所以在这里自动选上对应的通用数据粒度
      if(e === 0){

      }else if(e === 1){
        this.editingNode.granularityNum = 1;
        this.editingNode.granularityType = 5;
      }else if(e === 2){
        this.editingNode.granularityNum = 1;
        this.editingNode.granularityType = 0;
      }else if(e === 3){
        this.editingNode.granularityNum = 1;
        this.editingNode.granularityType = 0;
      }else if(e === 4){
        this.editingNode.granularityNum = 1;
        this.editingNode.granularityType = 5;
      }else if(e === 5){
        if(this.currentEditData.children.length < 2){
          this.$message({
            message: "绘图种类为"+this.algorithmList[e].type+"时，该位置下传感器数量必须为2！",
            type: "warning",
          });
        }
        this.editingNode.granularityNum = 10;
        this.editingNode.granularityType = 3;
      }else if(e === 6){
        if(this.currentEditData.children.length < 2){
          this.$message({
            message: "绘图种类为"+this.algorithmList[e].type+"时，该位置下传感器数量必须为2！",
            type: "warning",
          });
        }
        this.editingNode.granularityNum = 1;
        this.editingNode.granularityType = 4;
      }else if(e === 7){
        if(this.currentEditData.children.length < 2){
          this.$message({
            message: "绘图种类为"+this.algorithmList[e].type+"时，该位置下传感器数量必须为2！",
            type: "warning",
          });
        }
        this.editingNode.granularityNum = 1;
        this.editingNode.granularityType = 4;
      }
    },
    // 更改安装日期
    statusDateChange(e){
      this.editingNode.installTime = e;
    },
    // 反向找颜色
    findColorName(ENName){
      for (let i = 0; i < colorList.length; i++) {
        if(colorList[i].ENName === ENName){
          return colorList[i].CNName
        }
      }
    },
    // 是否允许拖拽后放置
    allowDrop(draggingNode, dropNode, type) {
        // 处于同一等级才能移动
        let dragLevel = draggingNode.data.nodeLevel;
        let dropLevel = dropNode.data.nodeLevel;
        if (dragLevel === dropLevel && type !== 'inner') {
            return true;
        } else return dragLevel === 9 && dropLevel === 8 && type === 'inner';
    },
    // 寻找没有用过的颜色来分配
    seekNoUseColor() {
      for(let i = 0; i < colorList.length; i++){
        let tmpFlag = 0;
        for(let j = 0; j < this.currentAddData.children.length; j++){
          if(colorList[i].ENName === this.currentAddData.children[j].color){
            tmpFlag = 1;
            break;
          }
        }
        if(tmpFlag === 0){
          return colorList[i].ENName;
        }
      }
      return colorList[0].ENName;
    }
  }
}
</script>

<style scoped>
.el-form-item {
  margin: 5px 0;
  display: inline-block;
  width: 550px !important;
}
/deep/.el-form-item__label {
  font-weight: bold !important;
}

.reportTree{
  margin: 5px 0 0 15px;
  border-width: 1px;
  border-left:1px solid #D0D0D0;
}

.label {
  float: left;
  margin-left: 4px;
  margin-right: 4px;
  line-height: 28px;
  font-size: 14px;
  font-family: "Courier New", Courier, monospace;
}

.monitor-content {
  float: left;
  width: 300px;
  margin: 0 10px;
}
.title {
  font-family: "Courier New", Courier, monospace;
  font-size: 16px;
}
.sensor-label {
  max-width: 400px;
  overflow: hidden;
  text-overflow: ellipsis;
}
.highLight {
  background-color: rgba(254,240,240,1);
}
.sensorLineForDraw {
  height: 180px;
  border: solid 1px gray;
  border-radius: 5px;
  margin: 5px;
  white-space: pre-wrap;
}
.sensorLineForTable {
  height: 40px;
  border: solid 1px gray;
  border-radius: 5px;
  margin: 5px;
  white-space: pre-wrap;
}
.sensorLineItem {
  border-left: solid 1px grey ;
  margin-right: 20px;
}
</style>
