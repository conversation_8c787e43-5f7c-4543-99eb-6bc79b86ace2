<template>
	<div class="app-container maindiv">
		<el-row :gutter="10" class="mb8">
			<el-col :span="1.5">
				<el-button icon="el-icon-plus" size="mini" type="primary" @click="openDetail">
					新增
				</el-button>
			</el-col>
			<right-toolbar
				:columns="columns"
				:showSearch.sync="showSearch"
				@queryTable="handleQuery"
			></right-toolbar>
		</el-row>
		<el-row>
			<div class="draggable">
				<el-table
					ref="dataTable"
					v-loading="loading"
					:data="tableData"
					:height="showSearch ? 'calc(100vh - 330px)' : 'calc(100vh - 270px)'"
					border
					row-key="id"
					size="mini"
					stripe
					style="width: 100%"
				>
					<el-table-column align="center" label="序号" type="index" width="50" />
					<template v-for="(column, index) in columns">
						<el-table-column
							v-if="column.visible"
							:label="column.label"
							:prop="column.field"
							:width="column.width"
							align="center"
							show-overflow-tooltip
						>
							<template slot-scope="scope">
								<dict-tag
									v-if="column.dict"
									:options="dict.type[column.dict]"
									:value="scope.row[column.field]"
								/>
								<template v-else-if="column.slots">
									<RenderDom :index="index" :render="column.render" :row="scope.row" />
								</template>
								<span v-else-if="column.isTime">
									{{ parseTime(scope.row[column.field], '{y}-{m}-{d}') }}
								</span>
								<span v-else>{{ scope.row[column.field] }}</span>
							</template>
						</el-table-column>
					</template>
					<el-table-column
						align="center"
						class-name="small-padding fixed-width"
						fixed="right"
						label="操作"
						width="220"
					>
						<template slot-scope="scope">
							<el-button
								icon="el-icon-edit"
								size="mini"
								type="text"
								@click="handleUpdate(scope.row)"
							>
								修改
							</el-button>
							<el-button
								icon="el-icon-delete"
								size="mini"
								type="text"
								@click="handleDelete(scope.row)"
							>
								删除
							</el-button>
						</template>
					</el-table-column>
				</el-table>
				<pagination
					v-show="total > 0"
					:limit.sync="queryParams.pageSize"
					:page.sync="queryParams.pageNum"
					:total="total"
					@pagination="handleQuery"
				/>
			</div>
		</el-row>
		<el-dialog
			:append-to-body="true"
			:destroy-on-close="true"
			:visible.sync="relaFlag"
			title="视频信息"
			width="70%"
			@close="handleClose"
		>
			<div v-loading="loading" class="road-interflow-edit">
				<el-row :gutter="15">
					<el-form ref="elForm" :model="formData" :rules="rules" label-width="120px" size="medium">
						<el-row :gutter="15">
							<el-col :span="12">
								<el-form-item label="视频名称" prop="name">
									<el-input
										v-model="formData.name"
										clearable
										placeholder="请输入视频名称"
									></el-input>
								</el-form-item>
							</el-col>
							<el-col :span="12">
								<el-form-item label="视频地址" prop="videoUrl">
									<el-input
										v-model="formData.videoUrl"
										:style="{ width: '100%' }"
										clearable
										placeholder="请输入视频地址"
									></el-input>
								</el-form-item>
							</el-col>
						</el-row>
						<el-row :gutter="15">
							<el-col :span="12">
								<el-form-item label="位置" prop="position">
									<el-input
										v-model="formData.position"
										clearable
										placeholder="请输入视频位置"
									></el-input>
								</el-form-item>
							</el-col>
							<el-col :span="12">
								<el-form-item label="是否启用" prop="enabled">
									<el-select v-model="formData.enabled" :style="{ width: '100%' }">
										<el-option label="是" :value="true"></el-option>
										<el-option label="否" :value="false"></el-option>
									</el-select>
								</el-form-item>
							</el-col>
							
							
							<el-col :span="12">
								<el-form-item label="摄像头类型" prop="type">
									<el-select v-model="formData.type" :style="{ width: '100%' }">
										<el-option label="枪机" :value="1"></el-option>
										<el-option label="球机" :value="2"></el-option>
									</el-select>
								</el-form-item>
							</el-col>
							
							<el-col :span="12">
								<el-form-item label="经度" prop="lon">
									<el-input
										v-model="formData.lon"
										clearable
										placeholder="请输入视频位置"
									></el-input>
								</el-form-item>
							</el-col>
							<el-col :span="12">
								<el-form-item label="纬度" prop="lat">
									<el-input
										v-model="formData.lat"
										clearable
										placeholder="请输入视频位置"
									></el-input>
								</el-form-item>
							</el-col>
							
						</el-row>
						<el-row :gutter="15">
							<el-col :span="24">
								<el-form-item label="说明" prop="description">
									<el-input
										v-model="formData.description"
										:style="{ width: '100%' }"
										type="textarea"
										clearable
										placeholder="请输入说明"
									></el-input>
								</el-form-item>
							</el-col>
						</el-row>
					</el-form>
				</el-row>
				<div style="text-align: right; margin-top: 20px">
					<el-button size="mini" type="primary" @click="handleSave">保存</el-button>
					<el-button size="mini" @click="relaFlag = false">退出</el-button>
				</div>
			</div>
		</el-dialog>
	</div>
</template>

<script>
import selectTree from '@/components/DeptTmpl/selectTree'
import { addVideo, delVideo, updateVideo, getVideoList } from '@/api/jgjc/structure/structure'

export default {
	name: 'videoInfo',
	components: {
		selectTree,
		RenderDom: {
			functional: true,
			props: {
				row: Object,
				index: Number,
				render: Function,
			},
			render(createElement, ctx) {
				const { row, index } = ctx.props
				return ctx.props.render(row, index)
			},
		},
	},
	props: [],
	data() {
		return {
			leftTotal: 1,
			showSearch: false,
			loading: false,
			columns: [
				{ key: 2, field: 'name', label: '名称', visible: true },
				{
					key: 3,
					field: 'videoUrl',
					label: '视频地址',
					visible: true,
					slots: true,
					render: (row, index)=> {
						return (
							<ElLink href={row.videoUrl} type="primary" target="_blank">
								{row.videoUrl}
							</ElLink>
						)
					},
				},
				{ key: 4, field: 'position', label: '设备位置', visible: true },
				{
					key: 5,
					field: 'enabled',
					label: '是否启用',
					visible: true,
					slots: true,
					render: (row, index) => {
						return <span>{row.enabled ? '启用' : '未启用'}</span>
					},
				},
				{ key: 6, field: 'description', label: '说明', visible: true },
			],
			tableData: [],
			relaFlag: false,
			formData: {},
			total: 0,
			queryParams: {
				pageNum: 1,
				pageSize: 10,
			},
			// 养护路段
			rules: {
				name: [{ required: true, message: '请输入视频名称', trigger: 'blur' }],
				videoUrl: [{ required: true, message: '请输入视频地址', trigger: 'blur' }],
				position: [{ required: true, message: '请输入位置信息', trigger: 'blur' }],
				enabled: [{ required: true, message: '请选择视频状态', trigger: 'change' }],
			},
		}
	},
	computed: {},
	watch: {},
	mounted() {
		this.queryParams.structureId = this.$route.query.structureId
		this.handleQuery()
	},
	methods: {
		// 查询
		handleQuery() {
			this.loading = true
			getVideoList(this.queryParams).then((res) => {
				this.tableData = res?.rows || []
				this.loading = false
				this.total = res.total
				this.$nextTick(() => {
					this.$refs.dataTable.doLayout()
				})
			})
		},
		// 修改
		handleUpdate(e) {
			this.formData = JSON.parse(JSON.stringify(e))
			this.$set(this.formData, 'structureType', this.$route.query.structureType)
			this.relaFlag = true
		},
		// 删除
		handleDelete(e) {
			this.$modal.confirm('是否确认删除').then(() => {
				this.loading = true
				const ids = [e.id]
				delVideo({ ids: ids, structureId: this.$route.query.structureId }).then((res) => {
					this.$modal.msgSuccess('删除成功')
					this.handleQuery()
				})
			})
		},
		// 新增
		openDetail() {
			this.formData = {}
			this.$set(this.formData, 'structureType', this.$route.query.structureType)
			this.relaFlag = true
		},
		// 保存
		handleSave() {
			this.$refs['elForm'].validate((valid) => {
				if (!valid) return
				this.formData.structureId = this.$route.query.structureId
				if (this.formData.id) {
					updateVideo(this.formData).then((res) => {
						this.$modal.msgSuccess('保存成功')
						this.handleQuery()
					})
				} else {
					addVideo(this.formData).then((res) => {
						this.$modal.msgSuccess('保存成功')
						this.handleQuery()
					})
				}
				this.relaFlag = false
			})
		},
		handleClose() {
			this.formData = {}
		},
	},
}
</script>

<style lang="scss" scoped>
::v-deep .el-table th .gutter {
	display: table-cell !important;
}
</style>
