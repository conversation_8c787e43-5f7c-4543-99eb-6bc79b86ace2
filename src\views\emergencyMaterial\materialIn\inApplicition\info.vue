<template>
  <div class="road-interflow-edit">
    <el-row>
      <el-row>
        <el-col :span="24">
          <el-form
            ref="queryForm"
            :inline="true"
            :model="queryParams"
            size="mini"
          >
            <el-form-item label="物资类型">
              <el-select v-model="queryParams.typeList" multiple style="width: 100px">
                <el-option v-for="item in typeList" :label="item.name" :value="item.id" :key="item.id"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="物资名称">
              <el-input
                style="width: 100px"
                v-model="queryParams.assetsName"
              ></el-input>
            </el-form-item>
            <el-form-item>
              <el-button
                icon="el-icon-search"
                size="mini"
                type="primary"
                @click="handleQuery"
                >搜索</el-button
              >
              <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
                >重置</el-button
              >
            </el-form-item>
          </el-form>
        </el-col>
      </el-row>
      <el-row :gutter="10" class="mb8">
        <el-col :span="1.5">
          <el-button
            icon="el-icon-download"
            size="mini"
            type="warning"
            @click="exportList"
            >导出
          </el-button>
        </el-col>
      </el-row>
      <el-col :span="24">
        <div class="draggable">
          <el-table v-adjust-table
            ref="dataTable"
            v-loading="loading"
            :data="tableData"
            border
            height="300"
            highlight-current-row
            row-key="id"
            size="mini"
            stripe
            style="width: 100%"
          >
            <el-table-column
              type="index"
              label="序号"
              width="68"
              align="center"
            ></el-table-column>
            <template v-for="(column, index) in columns">
              <el-table-column
                v-if="column.visible"
                :label="column.label"
                :prop="column.field"
                :width="column.width"
                align="center"
              >
                <template slot-scope="scope">
                  <dict-tag
                    v-if="column.dict"
                    :options="dict.type[column.dict]"
                    :value="scope.row[column.field]"
                  />
                  <template v-else-if="column.slots">
                    <RenderDom
                      :index="index"
                      :render="column.render"
                      :row="scope.row"
                    />
                  </template>
                  <span v-else-if="column.isTime">{{
                    parseTime(scope.row[column.field], "{y}-{m}-{d}")
                  }}</span>
                  <span v-else>{{ scope.row[column.field] }}</span>
                </template>
              </el-table-column>
            </template>
          </el-table>
          <pagination
            v-show="total > 0"
            :limit.sync="queryParams.pageSize"
            :page.sync="queryParams.pageNum"
            :total="total"
            @pagination="handleQuery"
          />
        </div>
      </el-col>
    </el-row>
  </div>
</template>
<script>
import { getMaterialTypeTree } from "@/api/emergencyMaterial/materialType";
import { getInDetailList } from "@/api/emergencyMaterial/materialIn";
export default {
  components: {
    RenderDom: {
      functional: true,
      props: {
        row: Object,
        index: Number,
        render: Function,
      },
      render(createElement, ctx) {
        const { row, index } = ctx.props;
        return ctx.props.render(row, index);
      },
    },
  },
  dicts: [],
  data() {
    return {
      queryParams: {
        pageNum: 1,
        pageSize: 10,
      },
      loading: false,
      tableData: [],
      total: 0,
      columns: [
        {
          key: 1,
          field: "typeName",
          label: `物资类型`,
          visible: true,
        },
        {
          key: 2,
          field: "assetsName",
          label: `物资名称`,
          visible: true,
        },
        {
          key: 3,
          field: "assetsName",
          label: `物资名称`,
          visible: true,
        },
        {
          key: 4,
          field: "model",
          label: `规格型号`,
          visible: true,
        },
        {
          key: 5,
          field: "unit",
          label: `单位`,
          visible: true,
        },
        {
          key: 6,
          field: "bpQuantity",
          label: `标配数量`,
          visible: true,
        },
        {
          key: 7,
          field: "quantity",
          label: `入库数量`,
          visible: true,
        },
        {
          key: 8,
          field: "qsQuantity",
          label: `缺失数量`,
          visible: true,
        },
        {
          key: 9,
          field: "remark",
          label: `备注`,
          visible: true,
        },
      ],
      detailDialog: false,
      typeList: [],
    };
  },
  props: {
    rowData: {
      type: Object,
      default: () => {},
    },
  },
  created() {
    this.handleQuery();
    this.getTypeList()
  },
  methods: {
    getTypeList() {
      getMaterialTypeTree().then((res) => {
        this.typeList = res.data?.children || [];
      });
    },
    handleQuery() {
      this.loading = true;
      this.queryParams.inId = this.rowData.id;
      this.queryParams.materialId = this.rowData.materialId;

      getInDetailList(this.queryParams).then((res) => {
        this.tableData = res.rows
        this.total = res.total;
        this.loading = false;
      });
    },
    exportList() {
      this.download(
        "manager/emergency/material/inbound/detail/other/export",
        { ...this.queryParams },
        `入库单详情_${new Date().getTime()}.xlsx`,
        {
          headers: { "Content-Type": "application/json;" },
          parameterType: "body",
        }
      );
    },
    resetQuery() {
      this.queryParams = {
        pageNum: 1,
        pageSize: 10,
        isStandard: ''
      };
    },
  },
};
</script>
<style lang="scss" scoped></style>
<style lang="scss" scoped>
@import "@/assets/styles/business.scss";
</style>
