<template>
  <div class="road-network">
    <section class="main-m">
      <span>主线养护里程：</span>
      <span>{{ countObj.total || 10000 }} <sub>km</sub></span>
      <!-- <span>km</span> -->
    </section>
    <div class="divider"></div>
    <section class="mileage mb-3">
      <div class="item-l">
        <span class="name">权属集团养护里程</span>
        <div class="mt10">
          <span class="mileage-number">{{ countObj.group || 3271.948 }}</span>
          <span class="unit">km</span>
        </div>
      </div>
      <div class="divider-col"></div>
      <div class="item-r">
        <span class="name">权属项目公司养护里程</span>
        <div class="mt10">
          <span class="mileage-number">{{ countObj.project || 2750.146 }}</span>
          <span class="unit">km</span>
        </div>
      </div>
    </section>

    <section class="mileage">
      <div class="item-l">
        <span class="name">养护路段</span>
        <div class="mt10">
          <span class="section-number">{{ countObj.section || 96 }}</span>
          <span class="unit">条</span>
        </div>
      </div>
      <div class="divider-col"></div>
      <div class="item-r">
        <span class="name">公路路段</span>
        <div class="mt10">
          <span class="section-number">{{ countObj.road || 98 }}</span>
          <span class="unit">条</span>
        </div>
      </div>
    </section>
  </div>
</template>

<script>
// api
import { getRouteCount } from "@/api/cockpit/route";

export default {
  data() {
    return {
      countObj: {
        total: 0, // 主线养护里程
        group: 0, // 权属集团养护里程
        project: 0, // 权属项目公司养护里程
        section: 0, // 养护路段
        road: 0, // 公路路段
      },
    }
  },
  created() {
    this.getCount();
  },
  methods: {
    getCount() {
      getRouteCount().then((res) => {
        if (res.code === 200 && res.data) {
          if (Object.prototype.toString.call(res.data) === '[object Array]') {
            res.data.forEach(item => {
              this.countObj.total += item.mainLength;
              if (item.deptType == 1) {
                // 权属集团
                this.countObj.group = item.maintenanceSection;
                // 养护路段
                this.countObj.section = item.highwaySection;
              } else if (item.deptType == 10) {
                // 权属项目公司养护里程
                this.countObj.project = item.maintenanceSection;
                // 公路路段
                this.countObj.road = item.highwaySection;
              }
            })
            this.countObj.total = 6013.358 || this.countObj.total.toFixed(3);
          }
        }
      });
    },
  }
}
</script>

<style lang="scss" scoped>
@import "@/assets/styles/utils.scss";

.road-network {
  width: 100%;
  height: 100%;
  padding: vwpx(20px);

  .main-m {
    width: 100%;
    display: flex;
    align-items: center;

    span:first-child {
      font-family: Microsoft YaHei, Microsoft YaHei;
      font-weight: 400;
      font-size: vwpx(32px);
      color: #FFFFFF;
    }

    span:nth-child(2) {
      font-family: Microsoft YaHei, Microsoft YaHei;
      font-weight: 700;
      font-size: vwpx(48px);
      color: #FFBA00;

      sub {
        font-family: Microsoft YaHei, Microsoft YaHei;
        font-weight: 400;
        font-size: vwpx(24px);
        color: rgba(255, 255, 255, 0.8);
        text-shadow: 0px 0px 10px rgba(27, 126, 242, 0.8);
        margin-left: vwpx(10px);
      }
    }

    // span:last-child {
    //   font-family: Microsoft YaHei, Microsoft YaHei;
    //   font-weight: 400;
    //   font-size: calc(12px * 2);
    //   color: rgba(255, 255, 255, 0.8);
    //   text-shadow: 0px 0px 10px rgba(27, 126, 242, 0.8);
    //   margin-left: 5px;

    //   display: flex;
    //   align-items: flex-end;
    // }
  }

  .divider {
    border-bottom: vwpx(2px) dotted rgba(156, 189, 255, 0.5);
    margin: vwpx(30px) 0;
  }

  .mileage {
    display: flex;

    .item-l {
      // flex: 1;
      width: 45%;

      .name {
        font-family: Microsoft YaHei, Microsoft YaHei;
        font-weight: 400;
        font-size: vwpx(28px);
        color: rgba(255, 255, 255, 0.8);
        margin-bottom: vwpx(42px);
      }
    }

    .divider-col {
      width: 0px;
      height: vwpx(100px);
      border-right: vwpx(3px) dotted rgba(156, 189, 255, 0.5);
      margin-right: 10%;
    }

    .item-r {
      // flex: 1;
      width: 45%;

      .name {
        font-family: Microsoft YaHei, Microsoft YaHei;
        font-weight: 400;
        font-size: vwpx(28px);
        color: rgba(255, 255, 255, 0.8);
      }
    }


  }

  .mb-3 {
    margin-bottom: vwpx(60px);
  }

  .mt3 {
    margin-top: vwpx(12px);
  }

  .mileage-number {
    font-family: Microsoft YaHei, Microsoft YaHei;
    font-weight: 700;
    font-size: vwpx(36px);
    color: #2DDB44;
  }

  .section-number {
    font-family: Microsoft YaHei, Microsoft YaHei;
    font-weight: 700;
    font-size: vwpx(36px);
    color: #017BFD;
  }

  .unit {
    font-family: Microsoft YaHei, Microsoft YaHei;
    font-weight: 400;
    font-size: vwpx(24px);
    color: rgba(255, 255, 255, 0.8);
    text-shadow: 0px 0px 10px rgba(27, 126, 242, 0.8);
    margin-left: vwpx(20px);
  }
}
</style>