// import { getFileSubListByEntity, save } from "@/api/system/moduleFileSub.js";
// const state = {
//     list: [], // 当前list数据
//     originalList: [], // 原始list数据
//     editingStatus: [], // 使用数组来管理每个条目的编辑状态
// }
// const mutations = {
//     SET_LIST(state, newList) {
//         state.list = JSON.parse(JSON.stringify(newList));
//         mutations.UPDATE_EDITING_STATUS(state);
//     },
//     SET_ORIGINAL_LIST(state, newList) {
//         state.originalList = JSON.parse(JSON.stringify(newList));
//     },
//     RESET_LIST(state) {
//         state.list = JSON.parse(JSON.stringify(state.originalList));
//         mutations.UPDATE_EDITING_STATUS(state);
//     },
//     ADD_TO_LIST(state, newItem) {
//         state.list.push(newItem); // 添加新数据到列表
//         state.editingStatus.push(true); // 添加新条目时，默认状态为 false
//     },
//     CHANGE_LIST(state) {
//         state.list = JSON.parse(JSON.stringify(state.originalList));
//         const listLength = state.list.length;
//         state.editingStatus = Array(listLength).fill(false);
//     },
//     SET_EDITING_STATUS(state, { index, status }) {
//         // 确保在设置之前该索引已经存在于数组中
//         if (index in state.editingStatus) {
//             state.editingStatus[index] = status;
//             // Vue.set(state.editingStatus, index, status);
//         }
//     },
//     UPDATE_EDITING_STATUS(state) {
//         const lengthDifference = state.list.length - state.editingStatus.length;
//         if (lengthDifference > 0) {
//             state.editingStatus = state.editingStatus.concat(Array(lengthDifference).fill(false));
//         } else {
//             state.editingStatus = state.editingStatus.slice(0, state.list.length);
//         }
//     },
// }
// const actions = {
//     fetchList({ commit }, entity) {
//         // 调用 API 来获取 list， entity 是调用 API 时需要的参数
//         getFileSubListByEntity(entity).then(response => {
//             commit('SET_LIST', response.data);
//             commit('SET_ORIGINAL_LIST', response.data);
//         }).catch(error => {
//             console.error('获取列表失败', error);
//         });
//     },
//     addToList({ commit }, newItem) {
//         commit('ADD_TO_LIST', newItem); // 调用 mutation
//     },
//     setEditingStatus({ commit }, payload) {
//         commit('SET_EDITING_STATUS', payload);
//     },
//     // 向数据库保存数据
//     saveData({ state, commit, dispatch }, entity) {
//         // 确保 entity 包含了所有必要的信息
//         if (!entity || Object.keys(entity).length === 0) {
//             console.error('保存数据失败，因为没有提供有效的 entity');
//             return;
//         }

//         // 调用 API 来保存数据
//         save(entity).then(response => {
//             // 保存成功后，重新从数据库获取最新的列表
//             // 确保在调用 fetchList 时传递正确的 entity
//             dispatch('fetchList', { moduleId: entity.moduleId });

//             console.log('数据保存成功', response.data);
//         }).catch(error => {
//             // 处理保存失败的情况
//             console.error('保存数据失败', error);
//         });
//     },

// }
// export default {
//     namespaced: true,
//     state,
//     mutations,
//     actions
// }

import { getFileSubListByEntity, saveModule } from "@/api/system/moduleFileSub.js";
import Vue from 'vue';

const state = {
    cards: {}, // 使用对象来存储每个 card 的状态
};

const mutations = {
    INIT_CARD_STATE(state, { cardId }) {
        if (!state.cards[cardId]) {
            Vue.set(state.cards, cardId, {
                list: [],
                originalList: [],
                editingStatus: [],
                showComponent: false,
                fileMap: {},
                fileSet: {}
            });
        }
    },
    // 重置列表
    RESET_LIST(state) {
        state.cards = {};
    },
    // 重置指定卡片的列表
    RESET_CARD(state, { cardId }) {
        if (state.cards[cardId]) {
            state.cards[cardId].list = [];
            state.cards[cardId].originalList = [];
            state.cards[cardId].editingStatus = [];
            state.cards[cardId].showComponent = false;
            state.cards[cardId].fileMap = {};
            state.cards[cardId].fileSet = {};
        }
    },
    // 删除指定列表
    DELETE_LIST(state, { cardId }) {
        if (state.cards[cardId]) {
            Vue.delete(state.cards, cardId);
        }
    },
    SET_LIST(state, { cardId, newList }) {
        if (state.cards[cardId]) {
            state.cards[cardId].list = JSON.parse(JSON.stringify(newList));
            mutations.UPDATE_EDITING_STATUS(state, { cardId });
        }
    },
    SET_ORIGINAL_LIST(state, { cardId, newList }) {
        if (state.cards[cardId]) {
            state.cards[cardId].originalList = JSON.parse(JSON.stringify(newList));
        }
    },
    ADD_TO_LIST(state, { cardId, newItem }) {
        if (state.cards[cardId]) {
            state.cards[cardId].list.push(newItem);
            state.cards[cardId].editingStatus.push(true);
            console.log(cardId, state.cards[cardId].editingStatus)
        }
    },
    SET_EDITING_STATUS(state, { cardId, index, status }) {
        if (state.cards[cardId] && index in state.cards[cardId].editingStatus) {
            state.cards[cardId].editingStatus[index] = status;
        }
    },
    SET_SHOW_COMPONENT(state, { cardId, status }) {
        if (state.cards[cardId]) {
            state.cards[cardId].showComponent = status;
        }
    },
    CHANGE_LIST(state, { cardId }) {
        if (state.cards[cardId]) {
            state.cards[cardId].list = JSON.parse(JSON.stringify(state.cards[cardId].originalList));
            const listLength = state.cards[cardId].list.length;
            state.cards[cardId].editingStatus = Array(listLength).fill(false);
        }
    },
    UPDATE_EDITING_STATUS(state, { cardId }) {
        if (state.cards[cardId]) {
            const lengthDifference = state.cards[cardId].list.length - state.cards[cardId].editingStatus.length;
            if (lengthDifference > 0) {
                state.cards[cardId].editingStatus = state.cards[cardId].editingStatus.concat(Array(lengthDifference).fill(false));
            } else {
                state.cards[cardId].editingStatus = state.cards[cardId].editingStatus.slice(0, state.cards[cardId].list.length);
            }
        }
    },
    // 设置文件信息
    SET_FILE_MAP(state, { cardId, fileSubId, file }) {
        if (!state.cards[cardId]) {
            state.cards[cardId] = { fileMap: {}, fileSet: {} };
        }
        if (!state.cards[cardId].fileMap[fileSubId]) {
            state.cards[cardId].fileMap[fileSubId] = [];
            state.cards[cardId].fileSet[fileSubId] = new Set();
        }

        const fileList = state.cards[cardId].fileMap[fileSubId];
        const fileSet = state.cards[cardId].fileSet[fileSubId];

        // 生成唯一标识符
        const fileIdentifier = `${file.name}_${file.ownerId}`;

        // 检查文件是否已经存在
        if (!fileSet.has(fileIdentifier)) {
            fileList.push(file);
            fileSet.add(fileIdentifier);
        }
    },
    // 清除文件信息
    CLEAR_FILE_MAP(state, { cardId, fileSubId }) {
        if (state.cards[cardId]) {
            state.cards[cardId].fileMap[fileSubId] = [];
            state.cards[cardId].fileSet[fileSubId] = new Set();
        }
    },

};

const actions = {
    initCardState({ commit }, cardId) {
        commit('INIT_CARD_STATE', { cardId });
    },
    fetchList({ commit }, { cardId, entity }) {
        getFileSubListByEntity(entity).then(response => {
            commit('SET_LIST', { cardId, newList: response.data });
            commit('SET_ORIGINAL_LIST', { cardId, newList: response.data });
            commit('SET_SHOW_COMPONENT', { cardId: cardId, status: true });
        }).catch(error => {
            console.error('获取列表失败', error);
        });
    },
    addToList({ commit }, { cardId, newItem }) {
        commit('ADD_TO_LIST', { cardId, newItem });
    },
    setEditingStatus({ commit }, { cardId, index, status }) {
        commit('SET_EDITING_STATUS', { cardId, index, status });
    },
    deleteList({ commit }, { cardId }) {
        commit('SET_SHOW_COMPONENT', { cardId: cardId, status: false });
        commit('DELETE_LIST', { cardId });
    },
    setFileMap({ commit }, { cardId, fileSubId, name, size, ownerId, fileId }) {
        const file = {
            name: name,
            size: size,
            ownerId: ownerId,
            fileId: fileId
        };
        commit('SET_FILE_MAP', { cardId, fileSubId, file });
    },
    clearFileMap({ commit }, { cardId, fileSubId }) {
        commit('CLEAR_FILE_MAP', { cardId, fileSubId });
    },
    // saveData({ state, commit, dispatch }, { cardId, entity }) {
    //     if (!entity || Object.keys(entity).length === 0) {
    //         console.error('保存数据失败，因为没有提供有效的 entity');
    //         return;
    //     }
    //     saveModule(entity).then(response => {
    //         dispatch('fetchList', { cardId, entity: { moduleId: entity.moduleId } });
    //         console.log('数据保存成功', response.data);
    //     }).catch(error => {
    //         console.error('保存数据失败', error);
    //     });
    // },
};

const getters = {
    getTotalFileSize: (state) => (cardId = null, fileSubId = null) => {
        if (cardId) {
            const card = state.cards[cardId];
            if (!card) return 0;

            if (fileSubId) {
                const fileList = card.fileMap[fileSubId] || [];
                return fileList.reduce((acc, file) => acc + file.size, 0);
            }

            return Object.values(card.fileMap).reduce((total, fileList) => {
                return total + fileList.reduce((acc, file) => acc + file.size, 0);
            }, 0);
        }

        return Object.values(state.cards).reduce((total, card) => {
            return total + Object.values(card.fileMap).reduce((cardTotal, fileList) => {
                return cardTotal + fileList.reduce((acc, file) => acc + file.size, 0);
            }, 0);
        }, 0);
    },
    getFileIdList: (state) => (cardId = null, fileSubId = null) => {
        const ownerIds = new Set();

        if (cardId) {
            const card = state.cards[cardId];
            if (!card) return [];
            if (fileSubId) {
                const fileList = card.fileMap[fileSubId] || [];
                fileList.forEach(file => ownerIds.add(file.ownerId));
            } else {
                Object.values(card.fileMap).forEach(fileList => {
                    fileList.forEach(file => ownerIds.add(file.ownerId));
                });
            }
        } else {
            Object.values(state.cards).forEach(card => {
                Object.values(card.fileMap).forEach(fileList => {
                    fileList.forEach(file => ownerIds.add(file.ownerId));
                });
            });
        }

        return Array.from(ownerIds);
    },
    getFileNum: (state) => (cardId = null, fileSubId = null) => {
        if (cardId) {
            const card = state.cards[cardId];
            if (!card) return 0;

            if (fileSubId) {
                const fileList = card.fileMap[fileSubId] || [];
                return fileList.length;
            }

            return Object.values(card.fileMap).reduce((total, fileList) => {
                return total + fileList.length;
            }, 0);
        }

        return Object.values(state.cards).reduce((total, card) => {
            return total + Object.values(card.fileMap).reduce((cardTotal, fileList) => {
                return cardTotal + fileList.length;
            }, 0);
        }, 0);
    },
    getFileMapList: (state) => (cardId, fileSubId) => {
        if (state.cards[cardId]) {
            return state.cards[cardId].fileMap[fileSubId];
        }
        return {};
    },
    getFileIdsMap: (state) => (cardId) => {
        if (state.cards[cardId]) {
            return state.cards[cardId].fileMap;
        }
        return {};
    },
    getListLength: (state) => (cardId = null) => {
        if (cardId) {
            const card = state.cards[cardId];
            return card && card.list ? card.list.length : 0;
        } else {
            return Object.values(state.cards).reduce((total, card) => {
                return total + (card.list ? card.list.length : 0);
            }, 0);
        }
    },
    getFileMapLength: (state) => (cardId = null) => {
        if (cardId) {
            const card = state.cards[cardId];
            return card && card.fileMap ? Object.keys(card.fileMap).length : 0;
        } else {
            return Object.values(state.cards).reduce((total, card) => {
                return total + (card.fileMap ? Object.keys(card.fileMap).length : 0);
            }, 0);
        }
    },
};

export default {
    namespaced: true,
    state,
    mutations,
    actions,
    getters
};
