<template>
  <div class="sub-page">
    <section class="sub-page-main" :style="mainStyle">
      <div class="sub-page-l">
        <Header title="健康监测大屏" :fixed="isBig ? 'static' : 'fixed'" theme="dark" :isSub="true" :showT="true">
          <template #btn>
            <img v-if="isAnalysis && !isBig" src="@/assets/monitoringSystem/analysis.png" @click="handleAnalysis"
              :style="{ width: isBig ? '1.91vw' : '4.45vw', height: isBig ? '64px' : '28px', marginRight: isBig ? '0.4vw' : '0.6vw' }" />
            <img v-if="isAnalysis && !isBig && params.code === '1593262192235515904'" src="@/assets/monitoringSystem/SZLS.png"
              @click="handleAnalysis2"
              :style="{ width: isBig ? '1.91vw' : '4.45vw', height: isBig ? '64px' : '28px', marginRight: isBig ? '0.4vw' : '0.6vw' }"></img>
            <img src="@/assets/cockpit/back.png" @click="handleBack"
              :style="{ width: isBig ? '140px' : '60px', height: isBig ? '64px' : '28px' }" />
          </template>
        </Header>
        <div class="page-l">
          <pageOne v-if="structureData" v-loading="loading" element-loading-text="加载中"
            element-loading-spinner="el-icon-loading" element-loading-background="rgba(0, 0, 0, 0.3)" />
        </div>
      </div>
      <div v-if="!isBig" class="sub-page-r" v-show="showAnalysis && !isBig"
        :class="showAnalysis ? 'sub-page-analysis' : ''">
        <img v-show="showAnalysis" src="@/assets/cockpit/back.png" @click="handleBack2"
          :style="{ width: isBig ? '140px' : '60px', height: isBig ? '64px' : '28px' }"
          class="sub-page-analysis-back" />
        <div class="page-r" v-show="isTable">
          <pageTwo :key="pageTwoKey" v-if="structureData" v-loading="loading" @setAnalysis="setAnalysis"
            element-loading-text="加载中" element-loading-spinner="el-icon-loading"
            element-loading-background="rgba(0, 0, 0, 0.3)" />
        </div>
        <iframe v-show="!isTable" src="https://jkjc.glyhgl.com:22582/video.html" width="100%" height="100%"
          frameborder="0" @load="onLoad"></iframe>
      </div>
      <div v-else class="sub-page-r">
        <div class="header" v-if="params.code === '1593262192235515904'">
          <el-button @click="onChange(1)" :class="activeBtn === 1 ? 'active' : ''">更多分析</el-button>
          <el-button @click="onChange(2)" :class="activeBtn === 2 ? 'active' : ''">数字孪生</el-button>
        </div>
        <div class="page-r" v-show="isTable">
          <pageTwo v-if="sensorList && structureData" v-loading="loading" element-loading-text="加载中"
            element-loading-spinner="el-icon-loading" element-loading-background="rgba(0, 0, 0, 0.3)" />
        </div>
        <iframe v-show="!isTable" src="https://jkjc.glyhgl.com:22582/video.html" width="100%" height="100%"
          frameborder="0" @load="onLoad"></iframe>
        <!-- <div v-show="!isTable" style="width:100%; height: 92vh; position: absolute; bottom: 0;">
          <iframe v-show="!isTable" src="https://jkjc.glyhgl.com:22582/video.html" width="100%" height="100%"
            frameborder="0" @load="onLoad"></iframe>
        </div> -->
      </div>
    </section>
  </div>
</template>

<script>
import { isBigScreen, getUrlParam } from '../utils/utils.js';
import Header from '../components/Header.vue';
import pageOne from "./pageOne/index.vue"
import pageTwo from "./pageTwo/index.vue"
import { fetchGet } from "../utils/api.js"

export default {
  name: 'BigScreen',
  provide() {
    return {
      iThis: this,
    }
  },
  props: {},
  components: { Header, pageOne, pageTwo },
  data() {
    return {
      loading: false,
      isBig: isBigScreen(),
      params: {},
      sensorList: [],
      structureData: undefined,
      isAnalysis: false,
      showAnalysis: false,
      pageTwoKey: 'pageTwoKey',
      isTable: true,
      activeBtn: 1,
    }
  },
  async created() {
    this.params = getUrlParam();
    this.loading = true;
    // 获取传感器列表
    let url = 'https://jkjc.yciccloud.com:8000/xboot/displayScreen/default/getSensorManageByCode'
    const res = await fetchGet(url, { nodeCode: this.params.code });
    if (res.code == 200) {
      this.sensorList = res.result || [];
    }

    // 获取结构物数据
    const url2 = 'https://jkjc.yciccloud.com:8000/xboot/structureNormalDataManage/getByCode'
    const r = await fetchGet(url2, { code: this.params.code })
    if (r.code === 200) {
      this.structureData = r.data?.result || r.result;
    }

    this.loading = false;
  },
  methods: {
    handleBack() {
      if (this.params && this.params.openType && this.params.openType === 'window') {
        // 关闭当前页面
        window.close();
      } else {
        // 返回上一页
        this.$router.go(-1);
      }
    },
    handleAnalysis() {
      this.isTable = true;
      this.showAnalysis = !this.showAnalysis;
      if (this.pageTwoKey === 'pageTwoKey1') return;
      this.pageTwoKey = 'pageTwoKey' + 1;
    },
    handleAnalysis2() {
      this.isTable = false;
      this.showAnalysis = !this.showAnalysis;
    },
    setAnalysis(flag) {
      this.isAnalysis = flag;
    },
    handleBack2() {
      this.showAnalysis = false;
    },
    onChange(type) {
      switch (type) {
        case 1:
          this.isTable = true;
          this.activeBtn = 1
          break;
        case 2:
          this.isTable = false;
          this.activeBtn = 2
          // this.$modal.loading();
          // setTimeout(() => {
          //   this.$modal.closeLoading();
          // }, 200)
          break;

      }
    },
    onLoad() {
      console.log('网页加载成功')
      this.$modal.closeLoading();
    },
  },
  computed: {
    mainStyle() {
      let style = {};
      if (this.isBig) {
        style = {
          height: '100vh',
          overflow: 'hidden',
        }
      } else {
        style = {
          height: '100vh',
          overflow: 'hidden',
          flexDirection: 'column',
        }
        // style = {
        //   height: '200vh',
        //   overflowY: 'auto',
        //   overflowX: 'hidden',
        //   flexDirection: 'column',
        // }
      }
      return style;
    },
  },
  watch: {},
}
</script>

<style lang="scss" scoped>
@import "@/assets/styles/utils.scss";

.header-btn {
  border: 1px solid #6694e4;
  color: #c1ddf6;
  background: #0e204e;
  box-shadow: inset 0px 0px 10px 0px #0687FF;
  padding: vwpx(14px) vwpx(20px);
  display: grid;
  place-items: center;
}

.sub-page {
  font-family: Arial, sans-serif !important;
  background-color: rgba(8, 5, 66, 0.65);
  touch-action: none;
  user-select: none;

  .sub-page-main {
    width: 100%;
    display: flex;
    overflow: hidden;

    .sub-page-l {
      flex: 1;
      background-image: url('~@/assets/cockpit/cockpit-bg.png');
      background-repeat: no-repeat;
      background-size: 100% 100%;

      .page-l {
        width: 100%;
      }
    }

    .sub-page-r {
      flex: 1;
      background-color: rgba(9, 25, 45, 1);

      .page-r {
        width: 100%;
      }

      .header {
        height: vwpx(160px);
        display: flex;
        align-items: center;
        position: absolute;
        top: 0;

        .el-button {
          height: vwpx(90px);
          font-size: vwpx(32px);
          background: rgba(1, 102, 254, 0.2);
          border-radius: vwpx(14px);
          border: 1px solid #1D77FF;
          padding: 0 vwpx(40px);
          color: #ffffff;
          margin-left: vwpx(40px);
        }

        .active {
          background: #1D77FF;
          border: 1px solid #1D77FF;
        }
      }
    }

    .sub-page-analysis {
      width: 100%;
      height: 100%;
      position: absolute;
      top: 0;
      left: 0;
      z-index: 999;

      .sub-page-analysis-back {
        cursor: pointer;
        position: absolute;
        top: vwpx(62px);
        right: vwpx(30px);

      }
    }
  }
}
</style>