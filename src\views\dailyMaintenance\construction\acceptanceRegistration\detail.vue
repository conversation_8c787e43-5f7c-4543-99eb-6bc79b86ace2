<template>
  <div class="app-container">
    <el-form ref="elForm" :rules="rules" :model="formData" :inline="true" label-width="200px" v-loading="loading">
      <el-row>
<!--        <el-col :span="12">-->
<!--          <el-form-item label="是否计算安全保通费" prop="guaranteeFund">-->
<!--            <el-radio-group v-model="formData.isGuarantee" size="medium" :disabled="readOnly">-->
<!--              <el-radio v-for="(item, index) in urgentDegreeOptions" :key="index" :label="item.value"-->
<!--                        :disabled="item.disabled">{{ item.label }}-->
<!--              </el-radio>-->
<!--            </el-radio-group>-->
<!--          </el-form-item>-->
<!--        </el-col>-->
<!--        <el-col :span="12">-->
<!--          <el-form-item label="是否计算安全生产费" prop="productionFund">-->
<!--            <el-radio-group v-model="formData.isProduction" size="medium" :disabled="readOnly">-->
<!--              <el-radio v-for="(item, index) in urgentDegreeOptions" :key="index" :label="item.value"-->
<!--                        :disabled="item.disabled">{{ item.label }}-->
<!--              </el-radio>-->
<!--            </el-radio-group>-->
<!--          </el-form-item>-->
<!--        </el-col>-->
<!--        <el-col :span="12">-->
<!--          <el-form-item label="有无新增单价" prop="productionFund">-->
<!--            <el-radio-group v-model="formData.addUnitPrice" size="medium" :disabled="readOnly">-->
<!--              <el-radio v-for="(item, index) in addPriceOptions" :key="index" :label="item.value"-->
<!--                        :disabled="item.disabled">{{ item.label }}-->
<!--              </el-radio>-->
<!--            </el-radio-group>-->
<!--          </el-form-item>-->
<!--        </el-col>-->
<!--        <el-col :span="12">-->
<!--          <el-form-item label="修补完成时间" prop="repairTime">-->
<!--            <el-date-picker v-model="formData.repairTime" format="yyyy-MM-dd" value-format="yyyy-MM-dd"-->
<!--                            :style="{width: '100%'}" placeholder="请选择修补完成时间" clearable></el-date-picker>-->
<!--          </el-form-item>-->
<!--        </el-col>-->
        <el-col :span="24" style="margin-bottom: 18px">
          <div class="card_title">事件信息</div>
          <el-card class="box-card" shadow="never">
            <el-descriptions :column="4" size="mini" border>
              <el-descriptions-item label="事件编码">
                {{disData.disCode}}
              </el-descriptions-item>
              <el-descriptions-item label="采集时间">
                {{disData.collectTime}}
              </el-descriptions-item>
              <el-descriptions-item label="起点桩号">
                {{formatPile(disData.beginMile)}}
              </el-descriptions-item>
              <el-descriptions-item label="终点桩号">
                {{formatPile(disData.endMile)}}
              </el-descriptions-item>
              <el-descriptions-item label="事件描述">
                {{disData.disDesc}}
              </el-descriptions-item>
            </el-descriptions>
          </el-card>
        </el-col>
        <el-col :span="12">
          <el-form-item label="签证人员" prop="visaByArr">
            <el-cascader
                v-model="formData.visaByArr"
                :options="deptUserOptions"
                :props="{
                  multiple: true,
                  value: 'id',
                  emitPath: false
                }"
                :show-all-levels="false"
                ref="visa"
                filterable clearable style="width: 380px"></el-cascader>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="签证单审核人" prop="visaCheckBy">
            <el-cascader
                v-model="formData.visaCheckBy"
                :options="deptUserOptions"
                :props="props"
                :show-all-levels="false"
                ref="visaCheck"
                filterable clearable style="width: 280px"></el-cascader>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="24" style="margin-bottom: 18px">
          <div class="card_title">施工前采集照片</div>
          <el-card class="box-card" shadow="never">
            <el-form-item label="">
              <sort-file-upload v-model="sgqcjzp" :forView="true"></sort-file-upload>
            </el-form-item>
          </el-card>
        </el-col>
        <el-col :span="24" style="margin-bottom: 18px">
          <div class="card_title">完工时照片</div>
          <el-card class="box-card" shadow="never">
            <el-form-item label="">
              <sort-file-upload v-model="wgsfj" can-sort ref="wgsfj" :forView="true"></sort-file-upload>
            </el-form-item>
          </el-card>
        </el-col>
        <el-col :span="24">
          <div class="card_title">施工简图</div>
          <el-card class="box-card" shadow="never">
            <el-form-item label="">
              <sort-file-upload v-model="sgjt" can-sort ref="sgjt" :forView="true" :fileType="['png', 'jpg', 'jpeg']"></sort-file-upload>
            </el-form-item>
          </el-card>
        </el-col>
        <el-col :span="24" style="margin-bottom: 18px">
          <div class="card_title">施工附件</div>
          <el-card class="box-card" shadow="never">
            <el-form-item label="">
              <sort-file-upload v-model="sgfj" :forView="true"></sort-file-upload>
            </el-form-item>
          </el-card>
        </el-col>
        <el-col :span="24" style="margin-bottom: 18px">
          <div class="card_title">完工附件</div>
          <el-card class="box-card" shadow="never">
            <el-form-item label="">
              <sort-file-upload v-model="wgfj" :forView="true"></sort-file-upload>
            </el-form-item>
          </el-card>
        </el-col>
        <el-col :span="24">
          <div class="card_title">验收附件</div>
          <el-card class="box-card" shadow="never">
            <el-form-item label="">
              <sort-file-upload v-model="ysfj" can-sort ref="ysfj"></sort-file-upload>
            </el-form-item>
          </el-card>
        </el-col>
        <el-col :span="24" style="margin-top: 18px">
          <div class="card_title" style="display: flex;width: 100%;justify-content: space-between">
            <div>事件方法数量</div>
            <div style="font-size: 16px">总金额：{{total}}</div>
          </div>
          <el-table v-adjust-table
              :data="methodList"
              row-key="id"
              border
              height="200px"
              ref="tableRef"
              style="width: 100%">
            <el-table-column
                label="序号"
                align="center"
                type="index"
                width="50"
            />
            <el-table-column
                prop="schemeCode"
                align="center"
                label="子目号">
            </el-table-column>
            <el-table-column
                prop="schemeName"
                align="center"
                label="子目名称">
            </el-table-column>
            <el-table-column
                prop="unit"
                align="center"
                label="单位">
            </el-table-column>
            <el-table-column
                prop="price"
                align="center"
                label="单价">
            </el-table-column>
            <el-table-column
                prop="calcDesc"
                align="center"
                label="计算式">
            </el-table-column>
            <el-table-column
                prop="num"
                align="center"
                label="方法数量">
            </el-table-column>
            <el-table-column
                prop="amount"
                align="center"
                label="资金">
            </el-table-column>
            <el-table-column
                prop="remark"
                align="center"
                label="备注">
            </el-table-column>
          </el-table>
        </el-col>
        <el-col :span="24" style="margin-top: 18px">
          <div class="card_title" style="display: flex;width: 100%;justify-content: space-between">
            <div>计算式及说明</div>
          </div>
          <el-input readonly class="calculation_desc" v-model="formData.calculationDesc" type="textarea" :rows="4">
          </el-input>
        </el-col>
        <el-col :span="24" style="margin-top: 18px">
          <div class="card_title">登记备注</div>
          <el-input v-model="formData.remark" type="textarea" :rows="4">
          </el-input>
        </el-col>
      </el-row>
      <div style="text-align: right;padding-right: 7.5px;margin-top: 18px">
        <el-button v-has-menu-permi="['check:construction:review']" type="primary" @click="onSubmit">通 过</el-button>
        <el-button v-has-menu-permi="['check:construction:reject']" type="danger" @click="onReject">驳 回</el-button>
        <el-button @click="onClose">退出</el-button>
      </div>
    </el-form>
    <methods-tree :key="rowData.conConId" scheme-type="日常养护" :con-id="rowData.conConId" ref="methodsRef"
                  @input="checkLib" :domain-id="rowData.domainId"></methods-tree>

  </div>
</template>
<script>
import {getTreeData} from "@/api/contract/quotationSystem"
import {reject, submitReview, getDetil} from "@/api/dailyMaintenance/construction/acceptanceRegistration"
import sortFileUpload from "@/components/SortFileUpload/index.vue";
import moment from "moment/moment";
import {getTreeStruct} from "@/api/tmpl";
import MethodsTree from "@/components/MethodsTree/index.vue";
import {v4 as uuidv4} from "uuid";
import {getDiseaseDataById} from "@/api/dailyMaintenance/eventManage/eventData";
import {formatPile} from "../../../../utils/ruoyi";
import { Decimal } from 'decimal.js';

export default {
  name: "index",
  components: {MethodsTree, sortFileUpload},
  data() {
    return {
      formData: {
        calculationDesc: ''
      },
      sgqcjzp: '',
      sgjt: '',
      sgfj: '',
      wgfj: '',
      ysfj: '',
      wgsfj: '',
      taskId: '',
      loading: false,
      total: 0,
      methodList: [],
      // 部门-用户树选项
      deptUserOptions: [],
      props: {
        multiple: false,//是否多选
        value: "id",
        emitPath: false
      },
      urgentDegreeOptions: [{
        "label": "计算",
        "value": 1
      }, {
        "label": "不计算",
        "value": 2
      }],
      addPriceOptions: [{
        "label": "无",
        "value": 0
      }, {
        "label": "有",
        "value": 1
      }],
      rules: {
        visaByArr: [
          {required: true, message: '请选择签证人员', trigger: 'change'}
        ],
        visaCheckBy: [
          {required: true, message: '请选择签证单审核人', trigger: 'change'}
        ],
        remark: [
          {required: true, message: '请填写审核意见', trigger: 'blur'}
        ]
      },
      disData: {}
    }
  },
  props: {
    rowData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  watch: {
    rowData: {
      handler(val) {
        if (val.id) {
          this.taskId = val.taskId
          this.getDetail(val.id)
        }
      },
      immediate: true
    },
    filterText(val) {
      this.$refs.tree.filter(val);
    }
  },
  mounted() {},
  created() {
    this.getDeptTreeDef()
  },
  methods: {
    formatPile,
    /** 查询部门-用户下拉树结构 */
    getDeptTreeDef() {
      getTreeStruct({types: 111}).then(response => {
        this.deptUserOptions = response.data;
      });
    },
    getDetail(id) {
      this.loading = true
      getDetil(id).then(res => {
        const detail = res.data
        this.formData = JSON.parse(JSON.stringify(detail))
        this.formData.taskId = this.taskId
        this.formData.detailId = this.rowData.detailId
        this.methodList = detail.methodList
        this.methodList.forEach(item => {
          if ((item.amount == null || item.amount == undefined) && typeof item.price === 'number' && typeof item.num === 'number') item.amount = Math.round(item.price * item.num)
        })
        getDiseaseDataById(detail.disId).then(res => {
          this.disData = res.data
        })
        this.total = this.methodList.reduce((acc, curr) => Number(acc) + Number(curr.amount || 0), 0)

        const sgqcjzp = detail.registerAttList.filter(item => item.registerType == 0)
        this.sgqcjzp = sgqcjzp.map(item => item.fileId)

        const sgjt = detail.attList.filter(item => item.registerType == 1)
        this.sgjt = sgjt.map(item => item.fileId)

        const sgfj = detail.attList.filter(item => item.registerType == 2)
        this.sgfj = sgfj.map(item => item.fileId)

        const wgfj = detail.attList.filter(item => item.registerType == 5)
        this.wgfj = wgfj.map(item => item.fileId)

        const ysfj = detail.attList.filter(item => item.registerType == 7)
        this.ysfj = ysfj.map(item => item.fileId)

        const wgsfj = detail.attList.filter(item => item.registerType == 6)
        this.wgsfj = wgsfj.map(item => item.fileId)

        const visaByArr = JSON.parse(localStorage.getItem('visaByArr'))
        if (visaByArr) this.$set(this.formData, 'visaByArr', visaByArr)

        const visaCheckBy = JSON.parse(localStorage.getItem('visaCheckBy'))
        if (visaCheckBy) this.$set(this.formData, 'visaCheckBy', visaCheckBy)
      }).finally(() => {
        this.loading = false
      })
    },
    onReject() {
      if (!this.formData.remark) {
        this.$message.warning('请填写审核意见')
        return
      }
      this.loading = true
      try {
        this.generateParams()
      } catch (e) {
        console.error('Error occurred while generating params:', e)
        return;
      }
      // this.formData.daliyId = this.formData.conId
      this.formData.repairTime = this.formData.repairTime ? moment(this.formData.repairTime).endOf("day").format('YYYY-MM-DD HH:mm:ss') : null
      if (this.formData.visaByArr) {
        this.formData.visaBy = this.formData.visaByArr.join(',')
        this.formData.visaName = this.$refs['visa'].checkedNodes.map(item => item.label).join(',')
        this.formData.visaCheckName = this.$refs['visaCheck'].inputValue
      }
      reject(this.formData).then(res => {
        this.$message.success('驳回成功')
        this.onClose()
      }).finally(() => {
        this.loading = false
      })
    },
    onSubmit() {
      this.$refs.elForm.validate(valid => {
        if (!valid) return
        if (!this.formData.remark) {
          this.$message.warning('请填写审核意见')
          return
        }
        this.loading = true
        try {
          this.generateParams()
        } catch (e) {
          console.error('Error occurred while generating params:', e)
          return;
        }
        // this.formData.daliyId = this.formData.conId
        this.formData.repairTime = this.formData.repairTime ? moment(this.formData.repairTime).endOf("day").format('YYYY-MM-DD HH:mm:ss') : null
        this.formData.visaBy = this.formData.visaByArr.join(',')
        this.formData.visaName = this.$refs['visa'].checkedNodes.map(item => item.label).join(',')
        this.formData.visaCheckName = this.$refs['visaCheck'].inputValue
        localStorage.setItem('visaByArr', JSON.stringify(this.formData.visaByArr))
        localStorage.setItem('visaCheckBy', JSON.stringify(this.formData.visaCheckBy))
        submitReview(this.formData).then(res => {
          this.$message.success('提交成功')
          this.onClose()
        }).finally(() => {
          this.loading = false
        })
      })
    },
    // 拼接参数
    generateParams() {
      this.$refs.wgsfj.save()
      this.$refs.sgjt.save()
      this.$refs.ysfj.save()
      // this.methodList = this.methodList.map(item => ({
      //   ...item,
      //   schemeId: item.id
      // }))
      // 拼接参数
      this.formData.methodList = this.methodList
      const attList = []
      const checkAttList = this.formData.checkAttList || []
      if (this.sgqcjzp) {
        this.sgqcjzp.forEach((item, index) => {
          attList.push({
            fileId: item,
            indexOrder: index,
            registerType: 0
          })
        })
      }
      if (this.sgjt) {
        this.sgjt.forEach((item, index) => {
          attList.push({
            fileId: item,
            indexOrder: index,
            registerType: 1
          })
        })
      }
      if (this.sgfj) {
        this.sgfj.forEach((item, index) => {
          attList.push({
            fileId: item,
            indexOrder: index,
            registerType: 2
          })
        })
      }
      if (this.ysfj) {
        this.ysfj.forEach((item, index) => {
          checkAttList.push({
            fileId: item,
            indexOrder: index,
            registerType: 7,
            remark: '验收登记'
          })
        })
      }
      if (this.wgsfj) {
        this.wgsfj.forEach((item, index) => {
          attList.push({
            fileId: item,
            indexOrder: index,
            registerType: 6
          })
        })
      }
      this.formData.checkAttList = checkAttList
      this.formData.attList = attList
    },
    checkLib(checkDatas) {
      const filteredCheckDatas = checkDatas.filter(item => item.nodeType === 2 && (item.rateFlag === 1 || !this.methodList.some(filterItem => filterItem.id === item.id)));
      const newCheckDatas = filteredCheckDatas.map(item => ({
        ...item,
        id: uuidv4().replace(/-/g, '').slice(0, 20)
      }));
      this.methodList.push(...newCheckDatas);
    },
    openLibModel() {
      this.$refs.methodsRef.openLibModel()
    },
    handleDelete(e) {
      this.methodList = this.methodList.filter(item => {
        return item.id != e.id
      })
    },
    async changeCalculation(row) {
      if (!this.isValidMathFormula(row.calcDesc)) {
        this.$modal.msgError('计算式错误，请检查')
        return
      }
      let num = this.math.evaluate(row.calcDesc) || 0
      this.$set(row, 'num', this.ceilToTwo(num, row.decimalPlaces))
      await this.changeSchemeNum(row)
    },
    async changeSchemeNum(row) {
      let money = new Decimal(row.num || 0).times(row.price || 0).toNumber()
      this.$set(row, 'amount', Math.round(money))
      this.total = await this.schemeList.reduce((acc, curr) => Number(acc) + Number(curr.amount), 0)
    },
    onClose() {
      this.$emit("close")
    },
    // 生成计算式说明
    generateInstructions() {
      let calculationDesc = ''
      this.methodList.forEach(item => {
        if (item.schemeName != '安全生产费' && item.schemeName != '安全保通费')
        calculationDesc += `${item.schemeName || ''}:(${item.calcDesc || item.num || ''})=${item.num || 0}${item.unit || ''}\n`
      })
      this.$set(this.formData, 'calculationDesc', calculationDesc)
    },
    generateUUID() {
      return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {
        var r = crypto.getRandomValues(new Uint8Array(1))[0] % 16 | 0;
        var v = c === 'x' ? r : (r & 0x3 | 0x8);
        return v.toString(16);
      }).slice(0, 20); // 截取前 20 位
    }
  }
}
</script>
<style scoped lang="scss">
.card_title {
  width: 200px;
  text-align: left;
  margin-bottom: 15px;
  font-weight: bold;
}
</style>
<style lang="scss" scoped>
@import "@/assets/styles/business.scss";
</style>
