<template>
  <div>
    <el-row :gutter="20">
      <el-col :span="24" :xs="24">
        <el-row>
          <el-col :span="24" :xs="24">
            <el-row>
              <el-form
                ref="queryForm"
                :inline="true"
                :model="queryParams"
                label-width="68px"
                size="mini"
              >
                <el-form-item
                  label=""
                  prop="constructionDate"
                >
                  <el-date-picker
                    v-model="queryParams.yearStr"
                    clearable
                    placeholder="请选择年度"
                    style="width: 240px"
                    type="year"
                    value-format="yyyy"
                  />
                </el-form-item>

                <el-form-item>
                  <selectTree
                    :key="'domainId'"
                    v-model="queryParams.domainId"
                    :deptType="100"
                    :deptTypeList="[1, 3, 4]" clearable
                    filterable
                    placeholder="管养单位"
                    style="width: 240px"
                  />
                </el-form-item>
                <el-form-item>
                  <RoadSection v-model="queryParams.maiSecId" :deptId="queryParams.domainId" placeholder="路段"
                               style="width: 240px"/>
                </el-form-item>
                <el-form-item>
                  <el-input v-model="queryParams.name" placeholder="施工通知单名称" style="width: 240px"></el-input>
                </el-form-item>
                <el-form-item>
                  <el-input v-model="queryParams.code" placeholder="施工通知单编码" style="width: 240px"></el-input>
                </el-form-item>
                <el-form-item>
                  <el-button icon="el-icon-search" size="mini" type="primary" @click="handleQuery">搜索</el-button>
                  <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
                  <el-button v-show="!showSearch" circle icon="el-icon-arrow-down" @click="showSearch=true"></el-button>
                  <el-button v-show="showSearch" circle icon="el-icon-arrow-up" @click="showSearch=false"></el-button>
                </el-form-item>
                <div v-show="showSearch">
                  <el-form-item>
                    <selectTree
                      :key="'constructionUnit'"
                      v-model="queryParams.conDomainId" :data-rule="false"
                      :dept-type="100"
                      :expand-all="false"
                      :filter-keys="['云南省交通投资建设集团有限公司', '云南交投投资有限公司']"
                      clearable
                      filterable
                      placeholder="施工单位"
                      style="width: 240px"
                    />
                  </el-form-item>
                  <el-form-item label="" prop="disDesc">
                    <el-input
                      v-model="queryParams.disDesc"
                      clearable
                      placeholder="事件描述"
                      style="width: 240px"
                    >
                    </el-input>
                  </el-form-item>
                  <el-form-item v-if="disStage == -2" label="" prop="stage">
                    <dict-select v-model="queryParams.stage" clearable placeholder="阶段" style="width: 240px"
                                 type="dis_stage_name"></dict-select>
                  </el-form-item>
                  <el-form-item label="" prop="beginMile">
                    <el-input
                      v-model="queryParams.beginMileStr"
                      clearable
                      placeholder="起点桩号"
                      style="width: 115px"
                    >
                    </el-input>
                    <span style="width: 10px;display: inline-block;text-align: center">~</span>
                    <el-input
                      v-model="queryParams.endMileStr"
                      clearable
                      placeholder="终点桩号"
                      style="width: 115px"
                    >
                    </el-input>
                  </el-form-item>
                  <el-form-item label="" prop="beginDate">
                    <el-date-picker
                      v-model="queryParams.beginDate"
                      clearable
                      format="yyyy-MM-dd"
                      placeholder="开始时间"
                      style="width: 240px"
                      value-format="yyyy-MM-dd HH:mm:ss"
                    ></el-date-picker>
                  </el-form-item>
                  <el-form-item label="" prop="endDate">
                    <el-date-picker
                      v-model="queryParams.endDate"
                      clearable
                      format="yyyy-MM-dd"
                      placeholder="结束时间"
                      style="width: 240px"
                      value-format="yyyy-MM-dd HH:mm:ss"
                    ></el-date-picker>
                  </el-form-item>
                  <el-form-item prop="disCode">
                    <el-input v-model="queryParams.disCode" placeholder="事件编码" style="width: 240px"/>
                  </el-form-item>
<!--                  <el-form-item prop="timeOutMsg">-->
<!--                    <el-select v-model="queryParams.timeOutMsg" placeholder="是否超期" style="width: 240px">-->
<!--                    </el-select>-->
<!--                  </el-form-item>-->
                  <el-form-item prop="costType">
                    <cost-select v-model=queryParams.costType :type="9" placeholder="请选择费用类型"
                                 style="width: 240px"></cost-select>
                  </el-form-item>
                  <el-form-item label="" prop="assetMainType">
                    <dict-select v-model="queryParams.assetMainType" :level="1" clearable
                                 only-tail-node
                                 placeholder="请选择资产类型" style="width: 240px" type="sys_asset_type"></dict-select>
                  </el-form-item>
                  <el-form-item v-if="disStage == -2" label="" prop="operator">
                    <el-cascader
                      ref="deptUser"
                      v-model="queryParams.operator"
                      :options="deptUserOptions"
                      :props="{
                        multiple: false, //是否多选
                        value: 'id',
                        emitPath: false,
                      }"
                      :show-all-levels="false"
                      clearable clearable filterable
                      placeholder="通知单拟定人" style="width: 240px"></el-cascader>
                  </el-form-item>
                </div>
              </el-form>
            </el-row>
          </el-col>
        </el-row>
        <!--操作按钮区开始-->
        <el-row :gutter="10" class="mb8">
          <!--          <el-button-->
          <!--              icon="el-icon-view"-->
          <!--              size="mini"-->
          <!--              type="primary"-->
          <!--              @click="handleViewOperateInfo"-->
          <!--          >操作记录-->
          <!--          </el-button>-->
          <el-col v-if="disStage > 2 || disStage == -2" :span="1.5">
            <el-button
              v-has-menu-permi="['checkApply:construction:visapreview', 'checkApply:construction:visapreview', 'checkApply:construction:visapreview', 'checkApply:construction:visapreview', 'checkApply:construction:visapreview']"
              icon="el-icon-download"
              size="mini"
              type="primary"
              @click="visaPreview"
            >签证单预览
            </el-button
            >
          </el-col>
          <el-col v-if="disStage > 2 || disStage == -2" :span="1.5">
            <el-button
              v-has-menu-permi="['checkApply:construction:maintainpreview', 'checkApply:construction:maintainpreview', 'checkApply:construction:maintainpreview', 'checkApply:construction:maintainpreview', 'checkApply:construction:maintainpreview']"
              icon="el-icon-download"
              size="mini"
              type="success"
              @click="maintenanceFilePreview"
            >维修档案预览
            </el-button
            >
          </el-col>
          <el-col v-if="disStage > 2 || disStage == -2" :span="1.5">
            <el-button
              v-has-menu-permi="['checkApply:construction:filedownload', 'checkApply:construction:filedownload', 'checkApply:construction:filedownload', 'checkApply:construction:filedownload', 'checkApply:construction:filedownload']"
              icon="el-icon-download"
              size="mini"
              type="primary"
              @click="archivesDownload"
            >施工档案下载
            </el-button
            >
          </el-col>
          <el-col v-if="disStage > 2 || disStage == -2" :span="1.5">
            <el-button
              v-has-permi="['settlement:repository:regenerate']"
              icon="el-icon-view"
              size="mini"
              type="warning"
              @click="handleRegenerate"
            >重新生成报表
            </el-button>
          </el-col>
          <slot name="btn"></slot>
          <right-toolbar
            :columns="columns"
            :showSearch.sync="showSearch"
            @queryTable="handleQuery"
          ></right-toolbar>
        </el-row>
        <el-row>
          <div class="draggable">
            <el-table ref="dataTable"
                      v-adjust-table
                      v-loading="loading"
                      :data="tableData"
                      :height="
                showSearch ? 'calc(100vh - 330px)' : 'calc(100vh - 270px)'
              "
                      border
                      highlight-current-row
                      row-key="id"
                      size="mini"
                      stripe
                      style="width: 100%"
                      @expand-change="loadData"
                      @row-click="handleClickRow"
                      @selection-change="handleSelectionChange"
            >
              <el-table-column align="center" type="selection" width="50"/>
              <el-table-column type="expand">
                <template slot-scope="props">
                  <el-table v-adjust-table v-loading="methodLoading" :data="props.row.methodList" style="width: 100%">
                    <el-table-column
                      align="center"
                      label="子目号"
                      prop="schemeCode">
                    </el-table-column>
                    <el-table-column
                      align="center"
                      label="养护方法"
                      prop="schemeName">
                    </el-table-column>
                    <el-table-column
                      align="center"
                      label="计算式"
                      prop="calcDesc">
                    </el-table-column>
                    <el-table-column
                      align="center"
                      label="方法数量"
                      prop="num">
                    </el-table-column>
                    <el-table-column
                      align="center"
                      label="方法单位"
                      prop="unit">
                    </el-table-column>
                    <el-table-column
                      align="center"
                      label="单价"
                      prop="price">
                    </el-table-column>
                    <el-table-column
                      align="center"
                      label="金额"
                      prop="amount">
                      <template slot-scope="scope">
                        <div>{{ Math.round(scope.row.amount) }}</div>
                      </template>
                    </el-table-column>
                  </el-table>
                </template>
              </el-table-column>
              <el-table-column
                :index="currentIndex"
                align="center"
                fixed="left"
                label="序号"
                type="index"
                width="50"
              />
              <template v-for="(column,index) in columns">
                <el-table-column v-if="column.visible"
                                 :fixed="column.fixed"
                                 :label="column.label"
                                 :prop="column.field"
                                 :width="column.width"
                                 align="center"
                                 show-overflow-tooltip>
                  <template slot-scope="scope">
                    <dict-tag v-if="column.dict" :options="dict.type[column.dict]" :value="scope.row[column.field]"/>
                    <template v-else-if="column.slots">
                      <RenderDom :index="index" :render="column.render" :row="scope.row"/>
                    </template>
                    <span v-else>{{ scope.row[column.field] }}</span>
                  </template>
                </el-table-column>
              </template>
              <el-table-column
                align="center"
                class-name="small-padding fixed-width"
                fixed="right"
                label="操作"
                width="300"
              >
                <template v-slot="scope">
                  <el-button icon="el-icon-tickets" type="text" @click="openEventInfo(scope.row)">事件信息</el-button>
                  <slot :scope="scope" name="operate"></slot>
                </template>
              </el-table-column>
            </el-table>
            <pagination
              v-show="total>0"
              :limit.sync="queryParams.pageSize"
              :page.sync="queryParams.pageNum"
              :total="total"
              @pagination="handleQuery"
            />
          </div>
        </el-row>
      </el-col>
    </el-row>
    <el-dialog
      v-if="eventInfoVisible"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :destroy-on-close="true"
      :visible.sync="eventInfoVisible"
      title="事件信息"
      width="90%">
      <event-detail :daliy-id="disInfo.daliyId" :dis-id="disInfo.disId" :dis-info="disInfo" @close="closeEventInfo"/>
    </el-dialog>
    <el-drawer v-if="openDetail" :visible.sync="openDetail" :wrapperClosable="false" destroy-on-close size="70%"
               title="施工单详情">
      <Detail :con-id="conId" read-only @close="handleCloseDetail"></Detail>
    </el-drawer>
    <el-dialog v-if="openOperateInfo" :visible.sync="openOperateInfo" destroy-on-close title="操作记录" width="80%">
      <operateInfo :business-key="clickRow.daliyId" :get-node-info="nodeInfo" param-name="id"></operateInfo>
    </el-dialog>
    <IFramePreview ref="iframeRef" :down-url="preview.url" :file-name="preview.fileName"
                   :srcdoc="preview.html"></IFramePreview>

  </div>
</template>

<script>
import RouteCodeSection from "@/components/RouteCodeSection/index.vue";
import RoadSection from "@/views/baseData/components/roadSection/index.vue";
import selectTree from "@/components/DeptTmpl/selectTree.vue";
import EventDetail from "@/views/dailyMaintenance/component/eventDetail.vue";
import Detail from "@/views/dailyMaintenance/component/constructionInfo.vue";
import {nodeInfo} from "@/api/dailyMaintenance/constructionOrder/noticeDraft";
import operateInfo from "@/views/dailyMaintenance/component/operateInfo.vue";
import {formatDateNoTime} from '@/utils/index'
import {getMethodList} from "@/api/dailyMaintenance/construction/complete";
import {
  archivesDownload,
  maintenanceFilePreview,
  visaPreview
} from "@/api/dailyMaintenance/construction/acceptanceApplication";
import Luckysheet from "@/components/Luckysheet/index.vue";
import IFramePreview from "@/components/IFramePreview/index.vue";
import axios from "axios";
import CostSelect from "@/components/CostSelect/index.vue";
import {regenerateReport} from "@/api/dailyMaintenance/metering/addPrice";
import {getTreeStruct} from "@/api/tmpl";

export default {
  components: {
    CostSelect,
    IFramePreview,
    Luckysheet,
    operateInfo,
    Detail,
    EventDetail,
    RoadSection,
    selectTree,
    RouteCodeSection,
    RenderDom: {
      functional: true,
      props: {
        row: Object,
        index: Number,
        render: Function
      },
      render(createElement, ctx) {
        const {row, index} = ctx.props
        return ctx.props.render(row, index)
      }
    }
  },
  dicts: ['sys_asset_type', 'route_direction', 'lane', 'cost_name'],
  props: {
    pageList: {
      type: Function,
      required: true
    },
    disStage: {
      type: Number,
      default: 2
    },
    stage: {
      type: Number,
      default: undefined
    }
  },
  data() {
    return {
      showSearch: false,
      queryParams: {
        pageNum: 1,
        pageSize: 50,
        domainId: null
      },
      total: 0,
      loading: false,
      methodLoading: false,
      columns: [
        {
          key: 0,
          width: 200,
          field: 'code',
          fixed: 'left',
          label: `施工通知单编码`,
          visible: true,
          slots: true,
          render: (row) => {
            return (
              <el-button type="text" onClick={e => this.handleOpenDetail(e, row.daliyId)}>{row.code}</el-button>
            )
          }
        },
        {key: 1, width: 180, field: 'name', label: `施工通知单名称`, visible: true},
        {
          key: 2,
          width: 100,
          field: 'timeOutMsg',
          label: `超期情况`,
          visible: true,
          slots: true,
          render: (row, index) => {
            if (!row.timeOutMsg) {
              return ('')
            } else if (row.timeOutMsg == '超期完成') {
              return (
                <el-tag type='warning' effect='dark' style="width: 65px;margin: 5px !important;color: #fff !important">
                  {row.timeOutMsg}
                </el-tag>
              )
            } else if (row.timeOutMsg == '按期完成') {
              return (
                <el-tag type='success' effect='dark' style="width: 65px;margin: 5px !important;color: #fff !important">
                  {row.timeOutMsg}
                </el-tag>
              )
            } else if (row.timeOutMsg == '已超期') {
              return (
                <el-tag type='danger' effect='dark' style="width: 65px;margin: 5px !important;color: #fff !important">
                  {row.timeOutMsg}
                </el-tag>
              )
            } else {
              return (
                <el-tag effect='dark'
                        style="min-width: 65px;margin: 5px !important;color: #fff !important;background-color:#1890ff!important">
                  {row.timeOutMsg}
                </el-tag>
              )
            }
          }
        },
        {
          key: 3, width: 100, field: 'endDate', label: `截止时间`, visible: true, slots: true, render: (row, index) => {
            return (
              <span>{formatDateNoTime(row.endDate)}</span>
            )
          }
        },
        {key: 4, width: 120, field: 'domainName', label: `管养单位`, visible: true},
        {key: 5, width: 120, field: 'maiSecId', label: `路段`, visible: true},
        // {key: 6, width: 100, field: 'routeCode', label: `路段编码`, visible: true},
        {key: 12, width: 100, field: 'direction', label: `方向`, visible: true, dict: 'route_direction'},
        {
          key: 7,
          width: 100,
          field: 'beginMile',
          label: `起点桩号`,
          visible: true,
          slots: true,
          render: (row, index) => {
            return (
              <span>{this.formatPile(row.beginMile)}</span>
            )
          }
        },
        {
          key: 8, width: 100, field: 'endMile', label: `终点桩号`, visible: true, slots: true, render: (row, index) => {
            return (
              <span>{this.formatPile(row.endMile)}</span>
            )
          }
        },
        {key: 9, width: 150, field: 'assetMainTypeName', label: `资产类型`, visible: true},
        {key: 10, width: 100, field: 'disTypeName', label: `事件类型`, visible: true},
        {
          key: 11, width: 200, field: 'disDesc', label: `事件描述`, visible: true
        },
        {key: 13, width: 100, field: 'lane', label: `位置`, visible: true, dict: 'lane'},
        {key: 14, width: 150, field: 'costTypeName', label: `费用类型`, visible: true, dict: 'cost_name'},
        {
          key: 15,
          width: 150,
          field: 'beginDate',
          label: `施工通知单开始时间`,
          visible: true,
          slots: true,
          render: (row, index) => {
            return (
              <span>{formatDateNoTime(row.beginDate)}</span>
            )
          }
        },
        {
          key: 16,
          width: 200,
          field: 'content',
          label: `工程内容`,
          visible: true
        },
        {
          key: 17, width: 200, field: 'exeRequire', label: `实施要求`, visible: true
        },
        {
          key: 18, width: 200, field: 'conDomainName', label: `施工单位`, visible: true
        },
        {
          key: 19, width: 200, field: 'conConName', label: `施工合同`, visible: true
        },
        {
          key: 20, width: 200, field: 'supDomainName', label: `监理单位`, visible: true
        },
        {
          key: 21, width: 200, field: 'supConName', label: `监理合同`, visible: true
        },

      ],
      tableData: [],
      clickRow: {},
      disFilePath: '',
      eventInfoVisible: false,
      disInfo: {},
      openDetail: false,
      openOperateInfo: false,
      conId: '',
      preview: {
        html: '',
        url: '',
        fileName: ''
      },
      selectIds: [],
      deptUserOptions: []
    }
  },
  computed: {},
  watch: {},
  created() {
    if (this.disStage == -2) {
      this.getDeptTreeDef()
      this.columns.splice(2, 0, {key: 22, width: 100, field: 'stageName', label: `阶段`, visible: true})
    }
  },
  mounted() {
    if (this.$route.query.code) {
      this.queryParams.code = this.$route.query.code
    }
    this.clearQueryParam()
    this.handleQuery()
  },
  methods: {
    /** 查询部门-用户下拉树结构 */
    getDeptTreeDef() {
      getTreeStruct({types: 111}).then(response => {
        this.deptUserOptions = response.data;
      });
    },
    nodeInfo,
    clearQueryParam() {
      const newUrl = window.location.href.replace(/\?.*$/, "");
      window.history.replaceState(null, '', newUrl);
    },
    handleQuery() {
      this.loading = true
      this.queryParams.year = this.queryParams.yearStr ? parseInt(this.queryParams.yearStr) : null
      if (this.stage) {
        this.queryParams.stage = this.stage
      } else {
        this.queryParams.disStage = this.disStage
      }
      this.queryParams.beginMile = Number(this.queryParams.beginMileStr)
      this.queryParams.endMile = Number(this.queryParams.endMileStr)
      this.pageList(this.queryParams).then(res => {
        this.tableData = res.rows
        this.total = res.total
      }).finally(() => {
        this.loading = false
      })
    },
    // 选中
    handleSelectionChange(e) {
      this.selectIds = e.map(obj => obj.disId)
    },
    resetQuery() {
      this.queryParams = {
        pageNum: 1,
        pageSize: 50,
        domainId: null
      }
      this.handleQuery()
    },
    handleCloseDetail() {
      this.drawer = false
      this.handleQuery()
    },
    handleViewOperateInfo() {
      if (!this.clickRow.id) {
        this.$modal.msgWarning("请选择一条数据")
        return
      }
      this.openOperateInfo = true
    },
    handleOpenFile(e, row) {
      this.disFilePath = ''
      this.openFile = true
      this.disFilePath = row.disease.disFilePath
    },
    handleClickRow(e) {
      this.clickRow = e
      e.isSelected = !e.isSelected;
      this.$refs.dataTable.toggleRowSelection(e);
    },
    openEventInfo(row) {
      this.eventInfoVisible = true
      this.disInfo = row
    },
    closeEventInfo() {
      this.eventInfoVisible = false
    },
    handleOpenDetail(e, conId) {
      this.conId = conId
      this.openDetail = true
    },
    loadData(row) {
      this.methodLoading = true
      getMethodList({
        id: row.id,
        daliyId: row.daliyId,
        disId: row.disId,
        detailId: row.detailId || row.id
      }).then(res => {
        this.$set(row, 'methodList', res.rows)
        this.methodLoading = false
      })
    },
    visaPreview() {
      if (!this.clickRow.id) {
        this.$message.error('请选择一条数据')
        return
      }
      if (this.clickRow.stage < 3) {
        this.$message.error('该事件尚未完工!')
        return;
      }
      this.loading = true
      visaPreview(this.clickRow.disId).then(res => {
        if (res.code == 200) {
          this.preview.html = res.data.html
          this.preview.url = res.data.downUrl
          this.preview.fileName = res.data.fileName
          this.$refs.iframeRef.visible = true
        }
        this.loading = false
      })
    },
    maintenanceFilePreview() {
      if (!this.clickRow.id) {
        this.$message.error('请选择一条数据')
        return
      }
      if (this.clickRow.stage < 3) {
        this.$message.error('该事件尚未完工!')
        return;
      }
      this.loading = true
      maintenanceFilePreview(this.clickRow.disId).then(res => {
        if (res.code == 200) {
          this.preview.html = res.data.html
          this.preview.url = res.data.downUrl
          this.preview.fileName = res.data.fileName
          this.$refs.iframeRef.visible = true
        }
        this.loading = false
      })
    },
    archivesDownload() {
      if (!this.clickRow.id) {
        this.$message.error('请选择一条数据')
        return
      }
      this.loading = true
      archivesDownload(this.clickRow.disId).then(res => {
        if (res.code == 200) {
          if (res.data.fileName.endsWith('.zip')) {
            let link = document.createElement('a')
            link.download = res.data.fileName
            link.style.display = 'none'
            link.href = res.data.downUrl
            document.body.appendChild(link)
            link.click()
            document.body.removeChild(link)
            this.loading = false
          } else {
            axios({
              method: "get",
              responseType: 'arraybuffer',
              url: res.data.downUrl,
              headers: {}
            }).then((res) => {
              const arrayBuffer = res.data;
              // 创建一个Blob对象
              const blob = new Blob([arrayBuffer], {type: 'application/zip'}); // 对于.xls文件
              saveAs(blob, res.data.fileName)
            }).finally(() => {
              this.loading = false
            })
          }
        }
      })
    },
    currentIndex(index) {
      if (this.disStage != -2) return index + 1
      return (this.queryParams.pageNum - 1) * this.queryParams.pageSize + index + 1
    },
    handleRegenerate() {
      if (this.selectIds.length == 0) {
        this.$modal.msgError("请勾选至少一条数据")
        return
      }
      const params = {
        idList: this.selectIds,
        type: 1
      }
      regenerateReport(params).then(res => {
        this.$modal.msgSuccess("操作成功")
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.leftDiv {
  border-right: 1px solid #d8dce5;
  min-height: calc(100vh - 100px);
  overflow-y: auto;
  height: calc(80vh - 150px);
  position: relative;
  top: -20px;
  padding-top: 10px;
  background-color: white;
}

.leftIcon {
  border: 1px solid #dcdfe6;
  border-radius: 8px;
  width: 16px;
  height: 50px;
  line-height: 50px;
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
  position: absolute;
  right: 0;
  top: 300px;
  z-index: 2;
}

.leftIcon:hover {
  background-color: #dcdfe6;
}

.rightIcon {
  border: 1px solid #dcdfe6;
  border-radius: 8px;
  width: 16px;
  height: 50px;
  line-height: 50px;
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
  position: absolute;
  left: -10px;
  top: 280px;
  z-index: 10;
  background: white;
}

.rightIcon:hover {
  background-color: #dcdfe6;
}

.left-total {
  font-size: 13px;
  line-height: 28px;
  color: #606266;
  position: absolute;
  bottom: 10px;
  right: 10px;
}
</style>
<style lang="scss" scoped>
@import "@/assets/styles/business.scss";
</style>
