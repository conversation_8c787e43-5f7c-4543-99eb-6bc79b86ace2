<template>
  <div class="app-container maindiv">
    <el-row :gutter="20" style="display: flex">
      <!--部门数据-->
      <maintenance-tree
        @rowClick="handleNodeClick"
        :realNav="realNav"
        @closeNav="realNav = false"
      ></maintenance-tree>
      <!--角色数据-->
      <el-col :span="realNav ? 19 : 24" :xs="24">
        <!--展开图标-->
        <div class="rightIcon" @click="realNav = true" v-show="!realNav">
          <span class="el-icon-caret-right"></span>
        </div>
        <el-row>
          <el-col :span="24" :xs="24">
            <el-row>
              <el-form
                ref="queryForm"
                :model="queryParams"
                size="mini"
                :inline="true"
                label-width="68px"
              >
                <el-form-item label="">
                  <el-date-picker
                    style="width: 240px"
                    v-model="queryParams.year"
                    type="year"
                    @change='getNumbers'
                    value-format="yyyy"
                    placeholder="年份"
                  >
                  </el-date-picker>
                </el-form-item>
                <el-form-item>
                  <el-select
                    v-model="queryParams.number"
                    placeholder="期数"
                    clearable
                    style="width: 100%"
                  >
                    <el-option
                      v-for="item in numbers"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                    >
                    </el-option>
                  </el-select>
                </el-form-item>
                <el-form-item>
                  <selectTree
                    :key="'domainId'"
                    style="width: 240px"
                    v-model="queryParams.domainId"
                    :deptType="100"
                    :deptTypeList="[1, 3, 4]"
                    placeholder="管养单位"
                    clearable
                    filterable
                  />
                </el-form-item>
                <el-form-item>
                  <RoadSection
                    v-model="queryParams.maiSecId"
                    :deptId="queryParams.domainId"
                    placeholder="养护路段"
                  />
                </el-form-item>
                <el-form-item>
                  <el-button
                    type="primary"
                    icon="el-icon-search"
                    size="mini"
                    @click="handleQuery"
                    >搜索</el-button
                  >
                  <el-button
                    icon="el-icon-refresh"
                    size="mini"
                    @click="resetQuery"
                    >重置</el-button
                  >
                  <el-button
                    v-show="!showSearch"
                    @click="showSearch = true"
                    icon="el-icon-arrow-down"
                    circle
                  ></el-button>
                  <el-button
                    v-show="showSearch"
                    @click="showSearch = false"
                    icon="el-icon-arrow-up"
                    circle
                  ></el-button>
                </el-form-item>
              </el-form>
              <!--默认折叠-->
              <el-col :span="24">
                <el-form
                  :model="queryParams"
                  ref="queryForm"
                  size="small"
                  :inline="true"
                  v-show="showSearch"
                  label-width="68px"
                >
                  <el-form-item v-if="type == 'view'">
                    <dict-select style="width: 240px" type="supervision_measurement_state" v-model="queryParams.status" placeholder="状态"></dict-select>
                  </el-form-item>
                  <el-form-item>
                    <el-input
                      style="width: 240px"
                      placeholder="监理计量单名称"
                      v-model="queryParams.name"
                    ></el-input>
                  </el-form-item>
                  <el-form-item>
                    <el-input
                      style="width: 240px"
                      placeholder="监理计量单编码"
                      v-model="queryParams.code"
                    ></el-input>
                  </el-form-item>
                  <el-form-item>
                    <el-input
                      style="width: 240px"
                      placeholder="施工计量单编码"
                      v-model="queryParams.settleCode"
                    ></el-input>
                  </el-form-item>
                </el-form>
              </el-col>
            </el-row>
          </el-col>
        </el-row>
        <!--操作按钮区开始-->
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button
              type="success"
              icon="el-icon-view"
              size="mini"
              @click="handleOpenOperate"
              >操作意见
            </el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button
              type="primary"
              @click="handlePreview"
              v-has-menu-permi="['calctheftsup:supercalc:preview']"
              icon="el-icon-view"
            >报表预览</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button
              type="warning"
              icon="el-icon-download"
              size="mini"
              v-has-menu-permi="['calctheftsup:supercalc:pendingexport', 'calctheftsup:supercalc:viewexport']"
              @click="exportList"
              >导出清单
            </el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button
              type="warning"
              icon="el-icon-view"
              size="mini"
              v-has-permi="['settlement:repository:regenerate']"
              @click="handleRegenerate"
            >重新生成报表
            </el-button>
          </el-col>
          <el-col :span="1.5" class="mb10">
            <el-button v-if="type == 'view'" type="primary" v-has-menu-permi="['calctheftsup:supercalc:updatevisacheck']" icon="el-icon-edit" size="mini" @click="openEditUserInfoDialog"
            >人员信息修改
            </el-button>
          </el-col>
          <right-toolbar
            :showSearch.sync="showSearch"
            @queryTable="handleQuery"
            :columns="columns"
          ></right-toolbar>
        </el-row>
        <el-row>
          <div class="draggable">
            <el-table v-adjust-table
              size="mini"
              style="width: 100%"
              v-loading="loading"
              border
              :data="tableData"
              row-key="id"
              ref="dataTable"
              stripe
              highlight-current-row
              @selection-change="handleSelectionChange"
              @row-click="handleClickRow"
              :height="
                showSearch ? 'calc(100vh - 330px)' : 'calc(100vh - 270px)'
              "
            >
              <el-table-column type="selection" width="50" align="center"/>
              <el-table-column
                label="序号"
                align="center"
                type="index"
                width="50"
              />
              <template v-for="(column, index) in columns">
                <el-table-column
                  :label="column.label"
                  v-if="column.visible"
                  align="center"
                  :prop="column.field"
                  :width="column.width"
                >
                  <template slot-scope="scope">
                    <dict-tag
                      v-if="column.dict"
                      :options="dict.type[column.dict]"
                      :value="scope.row[column.field]"
                    />
                    <template v-else-if="column.slots">
                      <RenderDom
                        :row="scope.row"
                        :index="index"
                        :render="column.render"
                      />
                    </template>
                    <span v-else-if="column.isTime">{{
                      parseTime(scope.row[column.field], "{y}-{m}-{d}")
                    }}</span>
                    <span v-else>{{ scope.row[column.field] }}</span>
                  </template>
                </el-table-column>
              </template>
              <el-table-column
                label="操作"
                fixed="right"
                align="center"
                width="250"
                class-name="small-padding fixed-width"
              >
                <template slot-scope="scope">
                  <el-button
                    type="text"
                    @click="showInfo(scope.row)"
                    icon="el-icon-view"
                    >查看明细</el-button
                  >
                  <el-button
                    type="text"
                    @click="updateFund(scope.row)"
                    v-has-menu-permi="['calctheftsup:supercalc:updateFund']"
                    icon="el-icon-refresh-left"
                    v-if="type == 'view'"
                  >更新费用</el-button
                  >
                  <el-button
                    size="mini"
                    type="text"
                    icon="el-icon-edit"
                    v-if="type == 'view'"
                    v-has-menu-permi="['calctheftsup:supercalc:edit']"
                    @click="handleEdit(scope.row)"
                  >修改
                  </el-button>
                  <el-button
                    type="text"
                    v-has-menu-permi="['calctheftsup:supercalc:process']"
                    @click="handleOpenSubmit(scope.row)"
                    icon="el-icon-check"
                    v-if="type == 'edit'"
                    >{{scope.row.status == 11 ? '结算支付' : '审核'}}</el-button
                  >
<!--                  <el-button-->
<!--                    type="text"-->
<!--                    icon="el-icon-refresh-left"-->
<!--                    :disabled="scope.row.status != 0"-->
<!--                  >更新费用</el-button-->
<!--                  >-->
                </template>
              </el-table-column>
            </el-table>
            <pagination
              v-show="total > 0"
              :total="total"
              :page.sync="queryParams.pageNum"
              :limit.sync="queryParams.pageSize"
              @pagination="handleQuery"
            />
          </div>
        </el-row>
      </el-col>
    </el-row>
    <el-dialog
      title="操作意见"
      :visible.sync="openOperateInfo"
      width="90%"
      destroy-on-close
      v-if="openOperateInfo"
    >
      <operateInfo
        @close="modelClose"
        :businessKey="row.id"
        :getNodeInfo="getNodeInfo"
      ></operateInfo>
    </el-dialog>
    <el-dialog
      title="明细查看"
      append-to-body
      modal-append-to-body
      :visible.sync="infoDialog"
      width="90%"
      v-if="infoDialog"
    >
      <info @close="modelClose" :maiSecId="maiSecId" :rowData="rowData"></info>
    </el-dialog>
    <el-dialog
      :title="drawerTitle"
      destroy-on-close
      :visible.sync="drawer"
      :close-on-click-modal="false"
      size="50%"
    >
      <detail @close="handleCloseDetail" :row-data="rowData"></detail>
    </el-dialog>
    <el-dialog
      title="附件列表"
      :visible.sync="openFile"
      width="500px"
      v-if="openFile"
    >
      <file-upload
        @close="modelClose"
        v-model="disFilePath"
        :forView="true"
      ></file-upload>
    </el-dialog>
    <el-dialog
      title="确认"
      :visible.sync="confirmDialog"
      width="30%"
      v-if="confirmDialog"
    >
      <el-form
        ref="checkFormRef"
        :model="formData"
        size="small"
        :inline="true"
        label-width="120px"
        :rules="confirmRules"
      >
      <template v-if="formData.status == 2">
          <el-form-item label="验收人" prop="acceptPerson">
            <el-cascader
              v-model="formData.acceptPerson"
              :options="deptUserOptions"
              :props="visaProps"
              :show-all-levels="false"
              ref="visa"
              filterable
              clearable
              style="width: 100%"
            ></el-cascader>
          </el-form-item>
          <el-form-item label="审核人" prop="checkPerson">
            <el-cascader
              v-model="formData.checkPerson"
              :options="deptUserOptions"
              :props="visaCheckProps"
              :show-all-levels="false"
              ref="visaCheck"
              filterable
              clearable
              style="width: 100%"
            ></el-cascader>
          </el-form-item>
        </template>
        <el-form-item label="操作意见">
          <el-input
            v-model="formData.comment"
            type="textarea"
            style="width: 230px"
          />
        </el-form-item>
      </el-form>
      <div style="text-align: right">
        <el-button size="mini" type="danger" @click="handleSubmit(false,2)">驳回</el-button>
        <el-button size="mini" type="danger" @click="handleSubmit(false,1)">驳回到申请</el-button>
        <el-button size="mini" type="primary" @click="handleSubmit(true)"
        >确 定</el-button
        >
        <el-button size="mini" @click="confirmDialog = false">取 消</el-button>
      </div>
    </el-dialog>
    <IFramePreview ref="iframeRef" :srcdoc="preview.html" :down-url="preview.url" :file-name="preview.fileName"></IFramePreview>
    <el-dialog
      title="编辑人员信息"
      destroy-on-close
      :visible.sync="userInfoDialog"
      :close-on-click-modal="false"
      size="50%"
    >
      <el-form  ref="userInfoRef" :model="userInfo" :rules="userInfoRules">
        <el-form-item label="验收人员" prop="visaBy" label-width="100px">
          <el-cascader
            v-model="userInfo.visaBy"
            :options="deptUserOptions"
            :props="visaProps"
            :show-all-levels="false"
            ref="visaRef"
            filterable
            clearable
            style="width: 100%"
          ></el-cascader>
        </el-form-item>
        <el-form-item label="审核人" prop="visaCheckBy" label-width="100px">
          <el-cascader
            ref="visaCheckRef"
            v-model="userInfo.visaCheckBy"
            :options="deptUserOptions"
            :props="visaCheckProps"
            :show-all-levels="false"
            filterable
            clearable
            style="width: 100%"
          ></el-cascader>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button size="small" @click="userInfoDialog = false">取 消</el-button>
        <el-button size="small" type="primary" @click="saveUserInfo">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import Detail from "../supApplication/detail.vue";
import selectTree from "@/components/DeptTmpl/selectTree.vue";
import MaintenanceTree from "@/components/MaintenanceTree/index.vue";
import operateInfo from "@/views/dailyMaintenance/component/operateInfo.vue";
import info from "./info.vue";
import {
  getNodeInfo, previewReport,
  processSettle, updateFund, getVisaCheck, updateVisaCheck
} from "@/api/calculate/calctheftsup/supApplication";
import {listSettlecalc, viewSettlecalc} from "@/api/calculate/calctheftsup/supExamine";
import RoadSection from "@/views/baseData/components/roadSection/index.vue";
import Luckysheet from "@/components/Luckysheet/index.vue";
import { getNumbers, getNumbersByYear } from '@/api/dailyMaintenance/metering/settlementApplication'
import { getTreeStruct } from "@/api/tmpl";
import IFramePreview from "@/components/IFramePreview/index.vue";
import {regenerateReport} from "@/api/dailyMaintenance/metering/addPrice";
export default {
  name: 'SupView',
  components: {
    IFramePreview,
    MaintenanceTree,
    Luckysheet,
    RoadSection,
    operateInfo,
    selectTree,
    Detail,
    info,
    RenderDom: {
      functional: true,
      props: {
        row: Object,
        index: Number,
        render: Function,
      },
      render(createElement, ctx) {
        const { row, index } = ctx.props;
        return ctx.props.render(row, index);
      },
    },
  },
  props: [],
  dicts: ["supervision_measurement_state"],
  data() {
    return {
      leftTotal: 1,
      showSearch: false,
      queryParams: {
        pageNum: 1,
        pageSize: 50,
      },
      total: 0,
      loading: false,
      type: 'edit',
      superId: "",
      maiSecId: "",
      preview: {
        html: '',
        url: '',
        fileName: ''
      },
      columns: [
        {
          key: 0,
          width: 150,
          field: "status",
          label: `状态`,
          visible: true,
          dict: "supervision_measurement_state",
        },
        {
          key: 1,
          width: 120,
          field: "numberName",
          label: `期数`,
          visible: true,
        },
        {
          key: 2,
          width: 200,
          field: "name",
          label: `监理计量单名称`,
          visible: true,
        },
        {
          key: 3,
          width: 200,
          field: "code",
          label: `监理计量单编号`,
          visible: true,
        },
        {
          key: 4,
          width: 200,
          field: "preCalcName",
          label: `上期名称`,
          visible: true,
        },
        {
          key: 5,
          width: 200,
          field: "preCalcCode",
          label: `上期编号`,
          visible: true,
        },
        {
          key: 6,
          width: 100,
          field: "domainName",
          label: `管养单位`,
          visible: true,
        },
        {
          key: 7,
          field: "maiSecName",
          label: `养护路段`,
          visible: true,
        },
        {
          key: 8,
          width: 200,
          field: "calcDomainName",
          label: `申请计量单位`,
          visible: true,
        },
        {
          key: 9,
          width: 200,
          field: "conName",
          label: `监理合同`,
          visible: true,
        },
        {
          key: 10,
          field: "supFund",
          label: `监理费`,
          visible: true,
        },
        {
          key: 11,
          width: 100,
          field: "deductionFund",
          label: `扣款金额`,
          visible: true,
        },
        {
          key: 12,
          width: 100,
          field: "calcDate",
          label: `计量日期`,
          visible: true,
        },
        {
          key: 13,
          field: "updateTime",
          label: `操作日期`,
          visible: true,
        },
        {
          key: 14,
          field: "updateuser",
          label: `操作人`,
          visible: true,
        },
        {
          key: 15,
          width: 100,
          field: "fileId",
          label: `附件`,
          visible: true,
          slots: true,
          render: (row, index) => {
            return (
              <el-button
                size="mini"
                disabled={!row.fileId}
                type="text"
                onClick={(e) => this.handleOpenFile(e, row)}
              >
                查看
              </el-button>
            );
          },
        },
        {
          key: 16,
          width: 200,
          field: "acceptPersonName",
          label: `验收人`,
          visible: true,
        },
        {
          key: 17,
          width: 100,
          field: "checkPersonName",
          label: `审核人`,
          visible: true,
        },
      ],
      tableData: [],
      rowData: {},
      drawerTitle: "监理计量申请",
      drawer: false,
      openFile: false,
      openOperateInfo: false,
      infoDialog: false,
      disFilePath: "",
      // 左侧组织树
      realNav: true,
      confirmDialog: false,
      formData: {},
      row: {},
      numbers: [],
      deptUserOptions: [],
      visaProps: {
        multiple: true, //是否多选
        value: "id",
        emitPath: false,
      },
      visaCheckProps: {
        multiple: false, //是否多选
        value: "id",
        emitPath: false,
      },
      confirmRules: {
        acceptPerson: [
          {required: true, type: 'array', message: '请选择验收人', trigger: 'change'}
        ],
        checkPerson: [{required: true, message:'请选择审核人', trigger: 'change'}]
      },
      ids: [],
      userInfoDialog: false,
      userInfo: {
        id: '',
        visaBy: '',
        visaCheckBy: '',
        visaCheckName: '',
        visaName: ''
      },
      userInfoRules: {
        visaBy: [
          {required: true, type: 'array', message: '请选择验收人员'}
        ],
        visaCheckBy: [
          {required: true, message: '请选择审核人'}
        ]
      },
    };
  },
  computed: {},
  watch: {},
  created() {
    const type = this.$route.query.type
    if (type) this.type = type
    this.getNumbers();
    this.handleQuery();
    this.getDeptTreeDef();
  },
  mounted() {},
  methods: {
    getNodeInfo,
    getDeptTreeDef() {
      getTreeStruct({ types: 111 }).then((response) => {
        this.deptUserOptions = response.data;
      });
    },
    handleNodeClick(e) {
      this.queryParams.domainId = e.domainId || "";
      this.queryParams.maiSecId = e.maiSecId || "";
      this.handleQuery();
    },
    handleQuery() {
      this.loading = true;
      if (this.type == 'edit') {
        listSettlecalc(this.queryParams).then((res) => {
          this.loading = false;
          this.tableData = res.rows;
          this.total = res.total;
        });
      } else {
        viewSettlecalc(this.queryParams).then((res) => {
          this.loading = false;
          this.tableData = res.rows;
          this.total = res.total;
        });
      }
    },
    getNumbers() {
      const parmas = {
        year: this.queryParams.year || null,
        pageNum: 1,
        pageSize: 1000,
      };
      this.numbers = []
      getNumbersByYear(parmas).then((res) => {
        res.rows.forEach((item) => {
          this.numbers.push({
            label: item.name,
            value: item.id,
          });
        });
      });
    },
    resetQuery() {
      this.queryParams = {
        pageNum: 1,
        pageSize: 50,
      };
      this.handleQuery()
    },
    handleCloseDetail() {
      this.rowData = {};
      this.drawer = false;
      this.handleQuery();
    },
    handleClickRow(row) {
      this.row = row;
    },
    handleOpenSubmit(row) {
      this.formData = {
        businessKey: row.id,
        taskId: row.taskId,
        status: row.status
      };
      this.confirmDialog = true;
    },
    // 提交
    handleSubmit(approved,reject) {
      if (!approved)  {
        this.formData.reject = reject
      }
      this.formData.approved = approved
      if (this.formData.acceptPerson) this.formData.acceptPersonName = this.$refs['visa'].checkedNodes.map(item => item.label).join(',')
      if (this.formData.checkPerson) this.formData.checkPersonName = this.$refs['visaCheck'].inputValue
      if (!approved) {
        this.submitCallback()
      } else {
        this.$refs.checkFormRef.validate((valid)=> {
          if (valid) {
            this.submitCallback()
          }
        })
      }
    },
    updateFund(row) {
      this.$modal.confirm('是否确定更新费用').then(() => {
        this.loading = true
        updateFund({id: row.id}).then(res => {
          this.handleQuery()
        })
      })
    },
    submitCallback() {
      if (this.formData.acceptPerson && this.formData.acceptPerson.length) {
        this.formData.acceptPerson = this.formData.acceptPerson.join(',')
      }
      processSettle(this.formData).then((res) => {
        this.$modal.msgSuccess("提交成功");
        this.confirmDialog = false;
        this.handleQuery();
      });
    },
    handleOpenOperate() {
      if (!this.row.id) {
        this.$modal.msgError("请选择一条数据");
        return;
      }
      this.openOperateInfo = true;
    },
    handleOpenFile(e, row) {
      this.disFilePath = "";
      this.disFilePath = row.fileId;
      this.openFile = true;
    },
    handleAdd() {
      this.drawer = true;
    },
    handleEdit(row) {
      this.rowData = row;
      this.drawer = true;
    },
    showInfo(row) {
      this.rowData = row;
      this.maiSecId = row.maiSecId;
      this.infoDialog = true;
    },
    // 报表预览
    handlePreview() {
      if (!this.row.id) {
        this.$modal.msgWarning("请选择一条数据");
        return;
      }
      this.loading = true
      previewReport(this.row.id).then(res => {
        if (res.code == 200){
          this.preview.html = res.data.html
          this.preview.url = res.data.downUrl
          this.preview.fileName = res.data.fileName
          this.$refs.iframeRef.visible = true
        }
        this.loading = false
      })
    },
    // 报表下载
    handleDownload(row) {
      const params = {
        id: row.id,
      };
      this.download(
        "manager/settlecalc/report/download",
        { ...params },
        `intermediate_${new Date().getTime()}.xlsx`,
        {
          headers: { "Content-Type": "application/json;" },
          parameterType: "body",
        }
      );
    },
    // 导出清单按钮
    exportList() {
      if (this.type == 'edit') {
        this.download(
            "manager/theft/supercalc/pending/export",
            {...this.queryParams},
            `supExamine_${new Date().getTime()}.xlsx`,
            {
              headers: {"Content-Type": "application/json;"},
              parameterType: "body",
            }
        );
      } else {
        this.download(
            "manager/theft/supercalc/view/export",
            {...this.queryParams},
            `supExamine_${new Date().getTime()}.xlsx`,
            {
              headers: {"Content-Type": "application/json;"},
              parameterType: "body",
            }
        );
      }
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection
    },
    handleRegenerate() {
      if (this.ids.length == 0) {
        this.$modal.msgError("请勾选至少一条数据")
        return
      }
      const params = {
        idList: this.ids.map(item => item.id),
        type: 8
      }
      regenerateReport(params).then(res => {
        this.$modal.msgSuccess("操作成功")
      })
    },
    modelClose() {
      this.drawer = false;
      this.openFile = false;
      this.openOperateInfo = false;
      this.infoDialog = false;
    },
    openEditUserInfoDialog() {
      if (!this.row.id) {
        this.$message.warning("请先选择一条数据！");
        return;
      }
      getVisaCheck(this.row.id).then(res=> {
        this.userInfo = res.data
        this.userInfo.visaBy =  this.userInfo.visaBy.split(',')
        this.userInfoDialog = true
      })
    },
    saveUserInfo() {
      this.$refs.userInfoRef.validate((valid)=> {
        if (valid) {
          const formData = JSON.parse(JSON.stringify(this.userInfo))
          const visaNode = this.$refs.visaRef.getCheckedNodes()
          const visaCheckNode = this.$refs.visaCheckRef.getCheckedNodes()
          formData.visaName = visaNode.map(item=> {
            return item.label
          }).join()
          formData.visaBy = formData.visaBy.join()
          formData.visaCheckName = visaCheckNode[0].label
          updateVisaCheck(formData).then(res=> {
            this.$message.success("修改成功！");
            this.userInfoDialog = false
            this.handleQuery()
          })
        }
      })
    },
  },
};
</script>

<style lang="scss" scoped>
.rightIcon {
  border: 1px solid #dcdfe6;
  border-radius: 8px;
  width: 16px;
  height: 50px;
  line-height: 50px;
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
  position: absolute;
  left: -10px;
  top: 280px;
  z-index: 10;
  background: white;
}

.rightIcon:hover {
  background-color: #dcdfe6;
}

.left-total {
  font-size: 13px;
  line-height: 28px;
  color: #606266;
  position: absolute;
  bottom: 10px;
  right: 10px;
}
</style>
<style lang="scss" scoped>
@import "@/assets/styles/business.scss";
</style>
