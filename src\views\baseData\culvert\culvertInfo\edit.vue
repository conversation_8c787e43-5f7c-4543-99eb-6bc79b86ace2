<template>
  <div v-loading="loading" class="culvert-info-edit">
    <el-tabs v-model="activeName" type="card">
      <el-tab-pane label="基本信息" name="0" />
      <el-tab-pane label="结构技术" name="1" />
      <el-tab-pane label="档案资料" name="2" />
    </el-tabs>

    <div style="height: 60vh;overflow-y: auto;padding: 0 10px 0 5px;">
      <el-form
          ref="ruleFormEl"
          :model="ruleForm"

          label-width="160px"
      >
        <template v-if="activeName === '0'">
          <MepuColumn :is-manger-unit="true" :fields="baseInfoFields" :from="ruleForm"/>
        </template>

        <template v-if="activeName === '1'">
          <MepuColumn :fields="structureInfoFields" :from="ruleForm"/>

        </template>

        <template v-if="activeName === '2'">
          <MepuColumn :fields="archivesInfoFields" :from="ruleForm"/>

        </template>
      </el-form>

    </div>

    <div class="text-center">
      <el-button type="primary" @click="doSubmit('submit')">提 交</el-button>
      <el-button type="primary" @click="doSubmit('save')" v-if="(formData.id ? formData.status == 1 : true)">暂 存</el-button>
      <el-button @click="onClose">取 消</el-button>
    </div>
  </div>
</template>

<script>
import {
  culvertAdd,
  culvertEdit,
  tempAdd,
  getInfoById,
} from "@/api/baseData/culvert/culvertInfo/index.js";
import MepuColumn from "@/views/baseData/components/MepuColumn/index.vue";
import formFields from './js/formFields'
export default {
  name: "CulvertInfoEdit",
  components: {MepuColumn },
  props: {
    formData: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      loading: false,
      activeName: "0",
      ruleForm: {
        managementMaintenanceId: '',
        managementMaintenanceBranchId: '',
        routeLevel:[]
      },
      baseInfoFields: [],
      structureInfoFields: [],
      archivesInfoFields: [],
    };
  },
  computed: {},

  created() {
    this.init();
  },
  mounted() {},
  methods: {
    init() {
      this.baseInfoFields = JSON.parse(
          JSON.stringify(formFields.baseInfo)
      )
      this.structureInfoFields = JSON.parse(JSON.stringify(formFields.structureInfo))
      this.archivesInfoFields = JSON.parse(
          JSON.stringify(formFields.archivesInfo
          )
      )
      if (Object.keys(this.formData).length > 0) {
        // this.ruleForm = JSON.parse(JSON.stringify(this.formData))
        this.getInfo();
      }
    },
    getInfo() {
      if (this.formData.id) {
        this.$modal.loading();
        getInfoById(this.formData.id).then((res) => {
          if(res.code == 200) {
            
            this.ruleForm = JSON.parse(JSON.stringify(res.data)) || {};
            this.ruleForm.entranceHoleImage = [res.data.entranceHoleImageId]
            this.ruleForm.exitHoleImage = [res.data.exitHoleImageId]
            
          }
        }).finally(()=>{
          this.$modal.closeLoading();
        });
      }
    },

    doSubmit(type) {
      switch (type) {
        case "submit":
          this.ruleForm.isStaging = false;
          break;
        case "save":
          this.ruleForm.isStaging = true;
          break;
      }
      if(this.ruleForm.isStaging){
        this.tempSubmit();
      }else {
        this.onSubmit();
      }
    },

    onSubmit() {
      // 如果 被锁定 则不能编辑
      if(this.ruleForm && this.ruleForm.isLocked) {
        this.$modal.msgWarning("数据已被锁定，不能修改");
        return
      }
      // const baseFlag = this.$refs.ruleFormEl.validateCheck();
      let flag = false
      this.$refs['ruleFormEl'].validate((valid) => {
        flag = valid
      })
      // const structureFlag = this.$refs.structureEl.validateCheck();
      // const archivesFlag = this.$refs.archivesEl.validateCheck();
      this.ruleForm.entranceHoleImageId = 
      (Array.isArray(this.ruleForm.entranceHoleImageId)&&this.ruleForm.entranceHoleImageId.length>0) ? 
      this.ruleForm.entranceHoleImageId[0] :this.ruleForm.entranceHoleImageId

      this.ruleForm.exitHoleImageId =
      (Array.isArray(this.ruleForm.exitHoleImageId)&&this.ruleForm.exitHoleImageId.length>0)?
      this.ruleForm.exitHoleImageId[0] :this.ruleForm.exitHoleImageId

      this.ruleForm.culvertSpan = this.ruleForm.culvertSpan?this.ruleForm.culvertSpan:undefined
      for (const key in this.ruleForm) {
        if (Array.isArray(this.ruleForm[key])) {
          this.ruleForm[key] = this.ruleForm[key].join(',')
        }
      }
      // if (!baseFlag || !structureFlag || !archivesFlag) return;
      if (!flag ) return;
      this.loading = true;
      const api = this.formData?.id ? culvertEdit : culvertAdd;
      api(this.ruleForm)
        .then((res) => {
          if (res.code === 200) {
            this.$message.success("提交成功！");
            this.onClose();
          }
        })
        .finally(() => {
          this.loading = false;
        });
    },
    // 暂存
    tempSubmit() {
      this.$confirm("确定缓存数据?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
        center: true,
      })
        .then(() => {

          for (const key in this.ruleForm) {
            if (Array.isArray(this.ruleForm[key])) {
              this.ruleForm[key] = this.ruleForm[key].join(',')
            }
          }

          tempAdd(this.ruleForm)
            .then((res) => {
              if (res.code == 200) {
                this.$modal.msgSuccess("缓存成功");
                this.onClose();
              }
            })
            .catch((err) => {
              // this.$modal.msgError("请求失败");
            })
            .finally(() => {
              this.loading = false;
          });
        })
        .catch(() => {});
    },
    onClose() {

      this.$emit("close");
    },
  },
};
</script>

<style lang="scss" scoped>
.culvert-info-edit {
  /* max-height: 75vh;
    padding: 10px;
    overflow: auto; */
}
</style>
