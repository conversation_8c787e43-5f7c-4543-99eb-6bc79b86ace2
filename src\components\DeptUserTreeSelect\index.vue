<template>
  <el-select :disabled="disabled" class="main-select-tree" ref="selectTree" v-model="value"  style="width: 100%;" :size="size" :placeholder="placeholder" :clearable="clearable" filterable :filter-method="filterMethod" @visible-change="handleVisible">
    <el-option v-for="(item,index) in formatData(deptUserOptions)" :key="index" :label="item.label" :value="item.value" style="display: none;" />
    <el-tree class="main-select-el-tree" ref="selecteltree"
             :data="deptUserOptions"
             node-key="id"
             highlight-current
             :props="defaultProps"
             @node-click="handleNodeClick"
             :filter-node-method="filterNode"
             :current-node-key="value"
             :expand-on-click-node="expandOnClickNode"
             :default-expand-all="expandAll"
             :render-after-expand="false"
    />
  </el-select>
</template>

<script>
import {getTreeStruct} from "@/api/tmpl";

export default {
  props: {
    value: {
      type: String,
      default: ''
    },
    deptType: { // 部门类型types
      type: Number,
      default: '',
      require: true
    },
    deptTypeList: {
      type: Array,
      default: null
    },
    size: {
      type: String,
      default: ''
    },
    placeholder: {
      type: String,
      default: ''
    },
    disabled: {
      type: Boolean,
      default: false
    },
    clearable: {
      type: Boolean,
      default: false
    },
    expandAll: {
      type: Boolean,
      default: false
    },
    onlySelectChild: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      deptUserOptions: [],
      expandOnClickNode: true,
      options:[],
      defaultProps: {
        children: 'children',
        label: 'label'
      }
    }
  },
  created() {
    this.getDeptTreeDef();
  },
  methods: {
    /** 查询部门-用户下拉树结构 */
    getDeptTreeDef() {
      getTreeStruct({types:111}).then(response => {
        this.deptUserOptions = response.data;
        console.log(this.deptUserOptions)
      });
    },
    // 四级菜单
    formatData(data){
      let options = [];
      data.forEach((item,key) => {
        options.push({label:item.label,value:item.id});
        if(item.children){
          item.children.forEach((items,keys) => {
            options.push({label:items.label,value:items.id});
            if(items.children){
              items.children.forEach((itemss,keyss) => {
                options.push({label:itemss.label,value:itemss.id});
                if(itemss.children){
                  itemss.children.forEach((itemsss,keysss) => {
                    options.push({label:itemsss.label,value:itemsss.id});
                  });
                }
              });
            }
          });
        }
      });
      return options;
    },
    handleVisible(visible) {
      if (visible) {
        this.$refs.selecteltree.filter('');
      }
    },
    filterMethod(e) {
      this.$refs.selecteltree.filter(e);
      return true
    },
    filterNode(value, data) {
      if (!value) return true;
      return data.label.indexOf(value) !== -1;
    },
    handleNodeClick(node){
      if (this.onlySelectChild) {
        if (!node.children ||node.children.length == 0) {
          this.$emit('input', node.id)
          this.$refs.selectTree.blur();
        }
      } else {
        this.$emit('input', node.id)
        this.$refs.selectTree.blur();
      }
    }
  }
}
</script>

<style scoped lang="scss">

</style>
