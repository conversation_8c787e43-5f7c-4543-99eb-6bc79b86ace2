<template>
  <PageContainer>
    <template slot="search">
      <div style="display: flex; align-items: center; margin: 0">
        <el-select
            v-model="queryParams.roadSectionIds"
            placeholder="请选择公路路段"
            multiple
            clearable
            collapse-tags
            style="width: 200px;margin-right: 20px;"
          >
              <el-option
                v-for="item in routeList"
                :key="item.roadSectionId"
                :label="item.roadSectionName"
                :value="item.roadSectionId"
              >
              </el-option>
            </el-select>

        <el-select
          style="margin-right: 20px"
          v-model="queryParams.status"
          placeholder="数据状态"
          clearable
        >
          <el-option
            v-for="dict in dict.type.base_data_state"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>

        <div style="min-width:220px;height:32px">
          <el-button
            v-hasPermi="['baseData:brokenChain:getListPage']"
            type="primary"
            icon="el-icon-search"
            @click="handleQuery"
            >搜索</el-button
          >
          <el-button icon="el-icon-refresh" @click="resetQuery">重置</el-button>
        </div>
      </div>
    </template>

    <template slot="header">
      <div class="button-list">
        <el-button
          v-hasPermi="['baseData:brokenChain:add']"
          type="primary"
          @click="handleAdd"
          >新增</el-button
        >
        <el-button
          v-hasPermi="['baseData:brokenChain:edit']"
          type="primary"
          @click="handleUpdate"
          >编辑</el-button
        >
        <el-button
          v-hasPermi="['baseData:brokenChain:delete']"
          type="primary"
          @click="handleDelete"
          >删除</el-button
        >
        <el-button
          v-hasPermi="['baseData:brokenChain:getInfoById']"
          type="primary"
          @click="handleView"
          >查看</el-button
        >
        <!-- <el-button
          type="primary"
          @click="handleImport"
        >导入</el-button> -->
        <el-button
          v-hasPermi="['baseData:brokenChain:export']"
          type="primary"
          @click="exportList"
        >导出清单</el-button>

      </div>
    </template>

    <template slot="body">
      <el-table
        v-adjust-table
        v-loading="loading"
        ref="table"
        height="100%"
        style="width: 100%"
        border
        :data="tableData"
        :row-style="rowStyle"
        @selection-change="handleSelectionChange"
        @row-click="handleRowClick"
      >
        <el-table-column type="selection" width="50" align="center" />
        <el-table-column
          fixed
          label="序号"
          type="index"
          width="50"
          align="center"
        >
          <template v-slot="scope">
            {{
              scope.$index +
              (queryParams.pageNum - 1) * queryParams.pageSize +
              1
            }}
          </template>
        </el-table-column>
        <el-table-column
          label="养护路段"
          prop="roadSectionName"
          width="150"
          align="center"
        />

        <el-table-column
          label="施工桩号起点"
          prop="constructionMileageStartStake"
          width="150"
          align="center"
        >
          <template slot-scope="scope">
            <span>
              {{ formatPile(scope.row.constructionMileageStartStake) }}
            </span>
          </template>
        </el-table-column>
        <el-table-column
          label="施工桩号终点"
          prop="constructionMileageEndStake"
          width="150"
          align="center"
        >
          <template slot-scope="scope">
            <span>
              {{ formatPile(scope.row.constructionMileageEndStake) }}
            </span>
          </template>
        </el-table-column>
        <el-table-column
          label="长链长度"
          prop="brokenLength"
          width="120"
          align="center"
        />
        <el-table-column
          label="短链长度"
          prop="brokenShortLength"
          width="120"
          align="center"
        />
        <el-table-column
          label="换算里程"
          prop="convertMileage"
          width="120"
          align="center"
        />
        <el-table-column
          label="国高桩号起点"
          prop="nationalNetworkStartStake"
          width="150"
          align="center"
        >
          <template slot-scope="scope">
            <span>
              {{ formatPile(scope.row.nationalNetworkStartStake) }}
            </span>
          </template>
        </el-table-column>
        <el-table-column
          label="国高桩号终点"
          prop="nationalNetworkEndStake"
          width="150"
          align="center"
        >
          <template slot-scope="scope">
            <span>
              {{ formatPile(scope.row.nationalNetworkEndStake) }}
            </span>
          </template>
        </el-table-column>
        <el-table-column
          label="数据状态"
          align="center"
          prop="status"
          width="150"
        >

          <template slot-scope="scope">
            <el-link
              :type="{ 1: 'info', 2: 'success' }[scope.row.status]"
              :underline="false"
            >
              <DictTag
                :value="scope.row.status"
                :options="dict.type.base_data_state"
              />
            </el-link>
          </template>
        </el-table-column>
        <el-table-column
          label="创建时间"
          prop="createTime"
          width="180"
          align="center"
        >
          <template slot-scope="scope">
            <span>{{ scope.row.createTime.replace("T", " ") }}</span>
          </template>
        </el-table-column>
        <el-table-column
          label="更新时间"
          prop="updateTime"
          width="180"
          align="center"
        >
          <template slot-scope="scope">
            <span>{{ scope.row.updateTime.replace("T", " ") }}</span>
          </template>
        </el-table-column>
        <el-table-column
          label="创建用户"
          prop="createBy"
          width="120"
          align="center"
        />
        <el-table-column
          label="更新用户"
          prop="updateBy"
          width="120"
          align="center"
        />
        <el-table-column
          label="备注"
          prop="remark"
          width="150"
          align="center"
        />
      </el-table>
      <pagination
        :total="total"
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
        :pageSizes="[10, 20, 30, 50, 100, 1000]"
        @pagination="getList"
      />
    </template>
    <Form
      v-if="showAddEdit"
      :forView="forView"
      :formData="formData"
      :title="title"
      :showAddEdit="showAddEdit"
      @close="
        () => {
          showAddEdit = false;
          formData = {};
        }
      "
      @refresh="
        () => {
          showAddEdit = false;
          formData = {};
          getList();
        }
      "
    />
    <Protections
      v-if="showDetail"
      :showDetail="showDetail"
      :brokenChainId="brokenChainId"
      :formData="formData"
      @close="
        () => {
          showDetail = false;
          formData = {};
        }
      "
    />
    <ImportData
      v-if="showImport"
      :is-update="false"
      :dialog-visible="showImport"
      :import-base-type="'11'"
      :import-type="2"
      @close="closeImport"
    />
  </PageContainer>
</template>

<script>
import {
  getListPage,
  getBrokenChain,
  delBrokenChain,
} from "@/api/baseData/road/brokenChain/index.js";
import Form from "./form.vue";
import CascadeSelection from "@/components/CascadeSelection/index.vue";
import rangeInput from "@/views/baseData/components/rangeInput/index.vue";
import ImportData from "@/views/baseData/components/importData/index.vue";
import { statusDialog } from "@/views/baseData/components/statusDialog/index.js";
import { statusListDialog } from "@/views/baseData/components/statusDialog/list.js";

import { getMaintenanceSectionListAll } from "@/api/baseData/common/routeLine";
import { listAllHighwaySections } from "@/api/system/highwaySections";
export default {
  name: "BrokenChain",
  components: {
    Form,
    CascadeSelection,
    rangeInput,
    ImportData,
  },
  dicts: ['sys_route_type', 'sys_operation_state','base_data_state'],
  data() {
    return {
      loading: true,
      showAddEdit: false,
      forView: false,
      title: '',
      routeOptions: [],
      formData: {},
      ids: [],
      total: 0,
      tableData: [],
      queryParams: {
        pageNum: 1,
        pageSize: 20,
      },
      showDetail: false,
      brokenChainId: "",
      showImport: false,
      routeList: [],
    };
  },
  watch: {},
  created() {
    getMaintenanceSectionListAll().then((res) => {
      if (res.code == 200) {
        this.routeOptions = res.data || [];
      }
    });
    listAllHighwaySections({}).then((res) => {
      if (res.code == 200) {
        this.routeList = res.data || [];
      }
    });
    this.getList();
  },
  methods: {
    // 获取表格数据
    getList() {
      this.loading = true;
      getListPage(this.queryParams)
        .then((response) => {
          this.tableData = response.rows;
          this.total = response.total;
          this.loading = false;
        })
        .catch(() => {
          this.loading = false;
        });
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id);
    },
    // 表格点击勾选
    handleRowClick(row) {
      row.isSelected = !row.isSelected;
      this.$refs.table.toggleRowSelection(row);
    },
    // 勾选高亮
    rowStyle({ row, rowIndex }) {
      if (this.ids.includes(row.id)) {
        return { "background-color": "#b7daff", color: "#333" };
      } else {
        return { "background-color": "#fff", color: "#333" };
      }
    },
    // 搜索按钮
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    // 重置按钮
    resetQuery() {
      this.queryParams = {
        pageNum: 1,
        pageSize: 20,
      };
      this.handleQuery();
    },
    // 新增按钮操作
    handleAdd() {
      this.forView = false;
      this.showAddEdit = true;
      this.formData = {};
      this.title = "新增统一里程数据";
    },
    // 编辑按钮
    handleUpdate() {
      if (this.ids.length != 1) {
        this.$message.warning("请选择一条数据进行编辑！");
        return;
      } else {
        getBrokenChain(this.ids[0]).then((res) => {
          if (res.code === 200) {
            this.formData = res.data;
            this.forView = false;
            this.showAddEdit = true;
            this.title = "编辑统一里程数据";
          }
        });
      }
    },
    // 删除按钮
    handleDelete() {
      if (this.ids.length == 0) {
        this.$message.warning("请选择至少一条数据进行删除！");
        return;
      }
      this.$modal
        .confirm("确认删除？")
        .then(() => {
          delBrokenChain(this.ids.join()).then((res) => {
            if (res && res.code == "200") {
              this.getList();
              this.$modal.msgSuccess("删除成功");
            }
          });
        })
        .catch(() => {});
    },
    // 查看按钮
    handleView() {
      if (this.ids.length != 1) {
        this.$message.warning("请选择一条数据！");
        return;
      } else {
        getBrokenChain(this.ids[0]).then((res) => {
          if (res.code === 200) {
            this.formData = res.data;
            this.forView = true;
            this.showAddEdit = true;
            this.title = "查看统一里程数据";
          }
        });
      }
    },
    // 导出按钮
    exportList() {
      if (this.ids.length === 0) {
        this.$modal
          .confirm("即将导出所有表格数据，此过程可能花费时间较长，是否继续？")
          .then(() => {
            this.download(
              "/baseData/brokenChain/export",
              this.queryParams,
              `brokenChain_${new Date().getTime()}.xlsx`,
              {
                headers: { "Content-Type": "application/json;" },
                parameterType: "body",
              }
            );
          })
          .catch(() => {});
      } else {
        this.$modal
          .confirm(`已选择${this.ids.length}条统一里程数据，确认导出？`)
          .then(() => {
            this.download(
              "/baseData/brokenChain/export",
              { ids: this.ids },
              `brokenChain__${new Date().getTime()}.xlsx`,
              {
                headers: { "Content-Type": "application/json;" },
                parameterType: "body",
              }
            );
          })
          .catch(() => {});
      }
    },
    // 导入按钮
    handleImport() {
      this.showImport = true;
    },
    closeImport(v) {
      this.showImport = false;
      if (v) this.getList();
    },
    // 二维码下载
    // downloadQrcode() {
    //   if (this.ids.length === 0) {
    //     this.$modal
    //       .confirm(
    //         '即将下载所有表格的二维码数据，此过程可能花费时间较长，是否继续？'
    //       )
    //       .then(() => {
    //         this.download(
    //           '/baseData/sidebrokenChain/basic/genQrCode',
    //           this.queryParams,
    //           `QrCode_${new Date().getTime()}`,
    //           {
    //             headers: { 'Content-Type': 'application/json;' },
    //             parameterType: 'body'
    //           }
    //         )
    //       })
    //       .catch(() => {})
    //   } else {
    //     this.$modal
    //       .confirm(`已选择${this.ids.length}条统一里程数据，是否下载二维码？`)
    //       .then(() => {
    //         let data = {
    //           ...this.queryParams,
    //           ids: this.ids
    //         }
    //         this.download(
    //           '/baseData/sidebrokenChain/basic/genQrCode',
    //           data,
    //           `QrCode_${new Date().getTime()}`,
    //           {
    //             headers: { 'Content-Type': 'application/json;' },
    //             parameterType: 'body'
    //           }
    //         )
    //       })
    //       .catch(() => {})
    //   }
    // },
    // 运营状态变更按钮
    changeStatus() {
      if (this.ids.length !== 1) {
        this.$message.warning("请选择一条数据！");
        return;
      } else {
        // baseDataType 基础数据类型 ？统一里程
        statusDialog({ dataId: this.ids[0], baseDataType: 11 }).then(() => {
          this.getList();
        });
      }
    },
    // 表格操作-运营状态
    handleOperational(event, row) {
      event.stopPropagation();
      statusListDialog({ dataId: row.id, baseDataType: 11 });
    },
    // 防护形式详情
    handleDetail(row) {
      this.showDetail = true;
      this.brokenChainId = row.id;
      this.formData = row;
    },
  },
};
</script>

<style lang="scss" scoped>
.button-list {
  border-radius: 4px;
  width: 100%;
  .el-button {
    margin-bottom: 10px;
    margin-right: 10px;
    margin-left: 0;
  }
}
</style>
