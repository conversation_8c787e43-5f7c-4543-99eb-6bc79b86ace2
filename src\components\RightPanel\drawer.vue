<template>
  <el-drawer :title="title" :visible.sync="drawer" :direction="direction" :before-close="handleClose" :size="size">
    <slot></slot>
  </el-drawer>
</template>

<script>
export default {
  props: {
    title: {
      type: String,
      default: '标题'
    },
    show: {
      type: Boolean,
      default: false
    },
    direction: {
      type: String,
      default: 'rtl'
    },
    size: {
      type: String,
      default: '50%'
    }
  },
  computed: {
    drawer: {
      get() {
        return this.show
      },
      set(val) {
        this.$emit('update:show', val)
      }
    }
  },
  methods: {
    handleClose() {
      // this.$emit('update:show', false)
      this.$emit('close')
    }
  }
}
</script>

<style lang="scss" scoped></style>