<template>
  <div class="app-container">
    <el-row :gutter="20">
      <!--筛选区开始-->
      <el-col :span="24" :xs="24">
        <el-row>
          <el-col :span="24" >
            <el-form :model="queryParams" ref="queryForm" size="small" :inline="true"  label-width="100px">
              <el-form-item label="名称：" prop="name">
                <el-input
                  v-model="queryParams.name"
                  placeholder="请输入名称"
                  clearable
                  prefix-icon="el-icon-edit"
                  style="width: 240px"
                  maxlength="200" show-word-limit
                  @keyup.enter.native="handleQuery"
                />
              </el-form-item>
              <el-form-item label="联系人姓名：" prop="contactName">
                <el-input
                  v-model="queryParams.contactName"
                  placeholder="请输入联系人姓名"
                  clearable
                  prefix-icon="el-icon-edit"
                  style="width: 240px"
                  maxlength="200" show-word-limit
                  @keyup.enter.native="handleQuery"
                />
              </el-form-item>
              <el-form-item label="联系人电话：" prop="contactNumber">
                <el-input
                  v-model="queryParams.contactNumber"
                  placeholder="请输入联系人电话"
                  clearable
                  prefix-icon="el-icon-edit"
                  style="width: 240px"
                  maxlength="200" show-word-limit
                  @keyup.enter.native="handleQuery"
                />
              </el-form-item>
              <el-form-item label="邮箱：" prop="email">
                <el-input
                  v-model="queryParams.email"
                  placeholder="请输入邮箱"
                  clearable
                  prefix-icon="el-icon-edit"
                  style="width: 240px"
                  maxlength="200" show-word-limit
                  @keyup.enter.native="handleQuery"
                />
              </el-form-item>
              <el-form-item>
                <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
                <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
              </el-form-item>
            </el-form>
            <!--默认折叠-->
          </el-col>
        </el-row>
        <!--筛选区结束-->
        <!--操作按钮区开始-->
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button
              type="primary"
              icon="el-icon-plus"
              size="mini"
              @click="handleAdd"
              v-hasPermi="['system:manufacturer:add']"
            >新增</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button
              type="success"
              icon="el-icon-edit"
              size="mini"
              :disabled="single"
              @click="handleUpdate"
              v-hasPermi="['system:manufacturer:edit']"
            >修改</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button
              type="danger"
              icon="el-icon-delete"
              size="mini"
              :disabled="multiple"
              @click="handleDelete"
              v-hasPermi="['system:manufacturer:remove']"
            >删除</el-button>
          </el-col>
          <right-toolbar :showSearch.sync="showSearch" @queryTable="getList" :columns="columns"></right-toolbar>
        </el-row>
        <!--操作按钮区结束-->
        <!--数据表格开始-->
        <div class="tableDiv">
          <el-table
            ref="table"
            size="mini"
            :height="showSearch ? 'calc(100vh - 320px)' : 'calc(100vh - 260px)'"
            v-loading="loading"
            border
            stripe
            :fit="false"
            :highlight-current-row="true"
            highlight-selection-row="true"
            :data="manufacturerList"
            @selection-change="handleSelectionChange"
            :row-style="rowStyle"
            @row-click="handleRowClick"
          >
            <el-table-column type="selection" width="50" align="center" />
            <el-table-column fixed type="index" align="right" width="50">
            </el-table-column>
            <el-table-column label="名称" fixed="left" align="left" header-align="center" prop="name" width="250" :show-overflow-tooltip="true"/>
            <el-table-column label="联系人姓名" align="left" header-align="center" prop="contactName" width="100" :show-overflow-tooltip="true"/>
            <el-table-column label="联系人手机号" align="left" header-align="center" prop="contactNumber" width="120" :show-overflow-tooltip="true" />
            <el-table-column label="邮箱" align="left" header-align="center" prop="email" width="150" :show-overflow-tooltip="true"/>
            <el-table-column label="网址" align="left" header-align="center" width="180" :show-overflow-tooltip="true">
              <template slot-scope="scope">
                <el-button type="text" @click="openUrl(scope.row.webSite)">{{ scope.row.webSite }}</el-button>
              </template>
            </el-table-column>
            <el-table-column label="地址" align="left" header-align="center" prop="address" width="150" :show-overflow-tooltip="true"/>
            <el-table-column label="说明" align="left" header-align="center" prop="description" width="400" :show-overflow-tooltip="true"/>
            <el-table-column label="序号" align="right" header-align="center" prop="orders" width="50"/>
            <el-table-column
              label="操作"
              fixed="right"
              align="center"
              width="120"
              class-name="small-padding fixed-width"
            >
              <template slot-scope="scope" v-if="scope.row.userId !== 1">
                <el-button
                  size="mini"
                  type="text"
                  icon="el-icon-edit"
                  @click="handleUpdate(scope.row)"
                  v-hasPermi="['system:manufacturer:edit']"
                >修改</el-button>
                <el-button
                  size="mini"
                  type="text"
                  icon="el-icon-delete"
                  @click="handleDelete(scope.row)"
                  v-hasPermi="['system:manufacturer:remove']"
                >删除</el-button>
              </template>
            </el-table-column>
          </el-table>
          <pagination
            v-show="total>0"
            :total="total"
            :page.sync="queryParams.pageNum"
            :limit.sync="queryParams.pageSize"
            @pagination="getList"
          />
        </div>
        <!--数据表格结束-->
      </el-col>
    </el-row>
    <!-- 添加或修改厂家对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="700px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="100px">
        <el-col :span="24">
          <el-form-item label="名称" prop="name">
            <el-input v-model="form.name" placeholder="请输入名称" maxlength="200" show-word-limit/>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="联系人姓名" prop="contactName">
            <el-input v-model="form.contactName" placeholder="请输入联系人姓名" maxlength="200" show-word-limit/>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="联系人手机号" prop="contactNumber">
            <el-input v-model="form.contactNumber" placeholder="请输入联系人手机号" maxlength="200" show-word-limit/>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="邮箱" prop="email">
            <el-input v-model="form.email" placeholder="请输入邮箱" maxlength="200" show-word-limit/>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="网址" prop="webSite">
            <el-input v-model="form.webSite" type="text" placeholder="请输入内容" maxlength="200" show-word-limit/>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="地址" prop="address">
            <el-input v-model="form.address" type="text" placeholder="请输入地址" maxlength="200" show-word-limit/>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="序号" prop="orders">
            <el-input-number v-model="form.orders" placeholder="请输入序号" style="width:100%;" controls-position="right" :min="1" :step="1" :max="1000000"></el-input-number>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="说明" prop="description">
            <el-input v-model="form.description" type="textarea" :rows="5" placeholder="请输入内容" maxlength="10000" show-word-limit/>
          </el-form-item>
        </el-col>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listManufacturer, getManufacturer, delManufacturer, addManufacturer, updateManufacturer,delManufacturerBatch } from "@/api/jgjc/jc/manufacturer";
import { getToken } from "@/utils/auth";
import Treeselect from "@riophae/vue-treeselect";
import "@riophae/vue-treeselect/dist/vue-treeselect.css";
import { delType } from '@/api/jgjc/jc/monitoringType'

export default {
  name: "Manufacturer",
  components: {  },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      delText:[],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: false,
      dictType:[],
      // 总条数
      total: 0,
      // 厂家表格数据
      manufacturerList: null,
      // 弹出层标题
      title: "",
      // 部门树选项
      deptOptions: undefined,
      // 是否显示弹出层
      open: false,
      // 表单参数
      form: {},
      defaultProps: {
        children: "children",
        label: "label"
      },
      // 用户导入参数
      upload: {
        // 是否显示弹出层（用户导入）
        open: false,
        // 弹出层标题（用户导入）
        title: "",
        // 是否禁用上传
        isUploading: false,
        // 是否更新已经存在的用户数据
        updateSupport: 0,
        // 设置上传的请求头部
        headers: { Authorization: "Bearer " + getToken() },
        // 上传的地址
        url: process.env.VUE_APP_BASE_API + "/system/user/importData"
      },
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 50,
        name: null,
        contactName: null,
        contactNumber: null,
        email: null,
        description: null,
        orderDirection: 'ASC'
      },
      // 列信息
      columns: [
        { key: 0, label: `名称`, visible: true },
        { key: 1, label: `联系人姓名`, visible: true },
        { key: 2, label: `联系人手机号`, visible: true },
        { key: 3, label: `邮箱`, visible: true },
        { key: 4, label: `网址`, visible: true },
        { key: 6, label: `说明`, visible: true },
        { key: 5, label: `序号`, visible: true }
      ],
      // 表单校验
      rules: {
        name: [
          { required: true, message: "名称不能为空", trigger: "blur" }
        ],
        orders: [
          { required: true, message: "序号不能为空", trigger: "blur" }
        ]
      }
    };
  },
  watch: {
    // 根据名称筛选部门树
  },
  created() {
    this.getList();
    // this.getDeptTree();
    // this.getConfigKey("sys.user.initPassword").then(response => {
    //   this.initPassword = response.msg;
    // });
  },
  methods: {
    openUrl(url) {
      if(url) {
        window.open(url)
      }
    },
    /** 查询用户列表 */
    getList() {
      this.loading = true;
      listManufacturer(this.queryParams).then(response => {
        this.manufacturerList = response.data;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        name: '',
        contactName: '',
        contactNumber: '',
        email: '',
        webSite: '',
        address:'',
        orders: 1,
        description: ''
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dateRange = [];
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id);
      this.delText = selection.map(item => item.name);
      this.single = selection.length != 1;
      this.multiple = !selection.length;
    },
    // 表格点击勾选
    handleRowClick(row) {
      row.isSelected = !row.isSelected;
      this.$refs.table.toggleRowSelection(row);
    },
    // 勾选高亮
    rowStyle({ row, rowIndex }) {
      if (this.ids.includes(row.id)) {
        return { 'background-color': '#E1F0FF', color: '#333' }
      } else {
        return { 'background-color': '#fff', color: '#333' }
      }
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加厂家";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids;
      getManufacturer(id).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改厂家";
      });
    },
    /** 提交按钮 */
    submitForm: function() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.id != null) {
            updateManufacturer(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addManufacturer(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      if(row.id) {
        this.$modal.confirm('是否确认删除厂家名称为"' + row.name + '"的数据项？').then(function() {
          return delManufacturer(row.id);
        }).then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        }).catch(() => {
        });
      }else{
        var ids = this.ids
        this.$modal.confirm('是否确认删除厂家名称为"' + this.delText + '"的数据项？').then(function() {
          return delManufacturerBatch(ids);
        }).then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        }).catch(() => {
        });
      }
    }
  }
};
</script>
<style>
.hasTagsView .app-main[data-v-078753dd]{
  background: #f5f7fa;
}

.tableDiv{
  background-color: white;
  padding-bottom: 10px;
}
</style>
