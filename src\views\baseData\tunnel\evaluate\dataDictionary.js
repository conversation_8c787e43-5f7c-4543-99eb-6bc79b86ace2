// 行政识别数据
const baseInfoData = [
  {
    type: 'select',
    deptType: 201,
    label:'管理处',
    prop: 'managementMaintenanceId',
    rules: [{ required: true, message: '请选择隧道', trigger: 'change' }],
    span:12,
    disabled: true,
  },

  {
    label: '路线编码',
    prop: 'routeCode',
    type: 'input',
    disabled: true,
    rules: [{ required: true, message: '请选择隧道', trigger: 'change' }],
    span:12,
  },

  {
    label:'养护路段',
    prop: 'maintenanceSectionId',
    rules: [{ required: true, message: '请选择隧道', trigger: 'change' }],
    type: 'input',
    span:12,
    disabled: true,
  },



 
  {
    label:'隧道编码',
    prop: 'tunnelCode',
    type: 'input',
    rules: [{ required: true, message: '请选择隧道', trigger: 'change' }],
    span:12,
    disabled: true,
  },
  {
    label:'隧道名称',
    prop: 'tunnelName',
    rules: [{ required: true, message: '请选择隧道', trigger: 'change' }],
    type: 'input',
    span:12,
    disabled: true,
  },
  
  {
    label:'年平均日交通量',
    prop: 'annualAverageDailyTraffic',
    type: 'inputNumber',
    span:12,
    rules: [{ required: true, message: '请选择年平均日交通量', trigger: 'change' }],
  },
  {
    label:'年度',
    prop: 'checkYear',
    type: 'date',
    span:12,
    rules: [{ required: true, message: '请选择年度', trigger: 'change' }],
  },
  {
    label:'备注',
    prop: 'remark',
    type: 'inputTextarea',
    span:12,
  },
]





export default {
  baseInfoData,
  
}
