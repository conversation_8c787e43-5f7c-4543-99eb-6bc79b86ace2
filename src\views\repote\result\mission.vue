<template>
  <div>
    <el-form
      :model="queryParams"
      ref="queryForm"
      size="small"
      :inline="true"
      label-width="68px"
      class="flex-form"

    >
      <el-form-item label="" prop="name" class="flex-item">
        <el-input
          style="width: 100%"
          v-model="queryParams.name"
          placeholder="请输入任务名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="" prop="userName" class="flex-item">
        <el-input
          style="width: 100%"
          v-model="queryParams.userName"
          placeholder="请输入任务发布人"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="" prop="startTime" class="flex-item">
        <el-date-picker
          style="width: 100%"
          v-model="queryParams.startTime"
          type="datetime"
          value-format="yyyy-MM-dd HH:mm:ss"
          placeholder="选择开始时间"
          clearable
          class="date_picker"
          :picker-options="startPickerOptions"
        />
      </el-form-item>
      <el-form-item label="" prop="endTime" class="flex-item">
        <el-date-picker
          style="width: 100%"
          v-model="queryParams.endTime"
          type="datetime"
          value-format="yyyy-MM-dd HH:mm:ss"
          placeholder="选择结束时间"
          clearable
          class="date_picker"
          :picker-options="endPickerOptions"
        />
      </el-form-item>
      <el-form-item class="flex-item button-container" style="margin-left: 65%">
        <el-button
          type="primary"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
          >搜索</el-button
        >
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
          >重置</el-button
        >
      </el-form-item>
    </el-form>
    <!--默认折叠-->

    <!--数据表格开始-->
    <div class="tableDiv">
      <el-table
        v-adjust-table
        size="mini"
        :height="showSearch ? 'calc(100vh - 320px)' : 'calc(100vh - 260px)'"
        style="width: 100%"
        v-loading="loading"
        border
        :data="missionList"
        @row-click="handleRowClick"
        ref="tableDiv"
      >
        <el-table-column
          fixed
          label="序号"
          align="center"
          type="index"
          width="50"
        ></el-table-column>
        <el-table-column label="任务名称" align="center" prop="name" />
        <el-table-column label="任务发布人" align="center" prop="userName" />
        <el-table-column label="下发时间" align="center" prop="updateTime">
          <template v-slot="scope">
            <span v-if="scope.row.status === '2'">
              {{ scope.row.updateTime | formatDate("YYYY年MM月DD日 hh:mm:ss") }}
            </span>
          </template>
        </el-table-column>
        <el-table-column label="任务发布部门" align="center" prop="deptName" />
        <el-table-column label="任务完成情况" align="center">
          <template slot-scope="scope">
            {{ scope.row.finishNum }}/{{ scope.row.totalNum }}
          </template>
        </el-table-column>
      </el-table>
      <pagination
        v-show="total > 0"
        :total="total"
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
        @pagination="getList"
      />
    </div>
    <!--数据表格结束-->
  </div>
</template>

<script>
import { EventBus } from "@/utils/eventBus";
import {
  listMission,
  getMission,
  delMission,
  addMission,
  updateMission,
  addPerson,
} from "@/api/repote/mission";
import { getToken } from "@/utils/auth";
import {
  findFileUser,
  findFillDept,
  findFillPost,
  getUserProfile,
} from "@/api/system/user";
import { getTreeStruct } from "@/api/tmpl";
import {
  addRepoteMission,
  delRepoteMission,
  getRepoteMission,
  listRepoteMission,
  updateRepoteMission,
} from "@/api/repote/repoteMission";
import { delParts } from "@/api/patrol/parts";

export default {
  name: "Mission",
  components: {},
  mounted() {
    this.updateFormHeight();
    window.addEventListener("resize", this.updateFormHeight);
  },
  beforeDestroy() {
    window.removeEventListener("resize", this.updateFormHeight);
  },
  data() {
    return {
      temp: null,
      //是否完成的状态
      iScompleteState: false,
      //用户列表
      userList: [],
      //选择框的postList
      selectedPosts: [],
      //选择框的deptList
      selectedDepts: [],
      //input填报人List
      selectedUsers: [],
      //部门的选项
      optionsDept: [],
      //角色的选项
      optionsPost: [],
      // 遮罩层
      loading: false,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: false,
      dictType: [],
      // 总条数
      total: 0,
      // 填报任务表格数据
      missionList: null,
      // 弹出层标题
      title: "",
      // 部门树选项
      deptOptions: undefined,
      // 是否显示弹出层
      open: false,
      showDetail: false,
      currentRow: {},
      // 表单参数
      form: {},
      defaultProps: {
        children: "children",
        label: "label",
      },
      deptUserOptions: [],
      // 用户导入参数
      upload: {
        // 是否显示弹出层（用户导入）
        open: false,
        // 弹出层标题（用户导入）
        title: "",
        // 是否禁用上传
        isUploading: false,
        // 是否更新已经存在的用户数据
        updateSupport: 0,
        // 设置上传的请求头部
        headers: { Authorization: "Bearer " + getToken() },
        // 上传的地址
        url: process.env.VUE_APP_BASE_API + "/system/user/importData",
      },
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 50,
        status: "2",
        startTime: null,
        endTime: null,
        name: null,
        userId: null,
        userName: null,
        deptId: null,
        deptName: null,
      },
      // 列信息
      columns: [
        { key: 0, label: `任务名称`, visible: true },
        { key: 1, label: `任务发布人`, visible: true },
        { key: 2, label: `任务发布部门`, visible: true },
        { key: 3, label: `任务状态`, visible: true },
      ],
      // 表单校验
      rules: {
        name: [
          { required: true, message: "填报任务名称不能为空", trigger: "blue" },
        ],
        personList: [
          { required: true, message: "填报人不能为空", trigger: "blue" },
        ],
      },
    };
  },
  created() {
    this.getList();
    getTreeStruct({ types: 111 }).then((response) => {
      this.deptUserOptions = response.data;
    });
    // 等待几秒后执行
    this.$nextTick(() => {
      setTimeout(() => {
        this.currentRow = this.missionList ? this.missionList[0] : {};
        EventBus.$emit("updateMissionData", this.currentRow);
      }, 1000);
    });
  },
  computed: {
    oprUser: {
      set() {
        let userList = this.$refs.userCascade.getCheckedNodes();
        console.log(userList);

        this.form.personList = userList.map((item) => {
          return {
            userId: item.value,
            userName: item.label,
          };
        });
      },
      get() {
        return this.form.personList?.map((item) => item.userId);
      },
    },
    startPickerOptions() {
      return {
        disabledDate: (time) => {
          if (this.queryParams.endTime) {
            return (
              time.getTime() > new Date(this.queryParams.endTime).getTime()
            );
          }
          return false;
        },
      };
    },
    endPickerOptions() {
      return {
        disabledDate: (time) => {
          if (this.queryParams.startTime) {
            return (
              time.getTime() < new Date(this.queryParams.startTime).getTime()
            );
          }
          return false;
        },
      };
    },
  },
  methods: {
    getList() {
      this.loading = true;
      listRepoteMission(this.queryParams).then((response) => {
        this.missionList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    updateFormHeight() {
      this.$nextTick(() => {
        const formHeight = this.$refs.queryForm.$el.offsetHeight;
        EventBus.$emit("formHeightUpdated", formHeight);
        const tableHeight = this.$refs.tableDiv.$el.offsetHeight;
        EventBus.$emit("tableHeightUpdated", tableHeight);
      });
    },
    //打开新增页面就将选择上的userList展示在selectedUsers中
    getUserNickName() {
      this.selectedUsers = this.userList.map((user) => user.userId);
    },
    //监听岗位和部门变化
    handleChange(e) {
      console.log(e, this.selectedPosts, this.selectedDepts);
      findFileUser({
        selectedPosts: this.selectedPosts,
        selectedDepts: this.selectedDepts,
      }).then((res) => {
        if (res.code === 200) {
          this.userList = res.data;
          this.selectedUsers = this.userList.map((user) => user.userId);
        }
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      // this.reset()
    },
    // 表单重置
    reset() {
      this.resetForm("form");
      getUserProfile().then((res) => {
        let user = res.data;
        this.form = {
          personList: null,
          status: "1",
          name: null,
          userId: user.userId,
          userName: user.userName,
          deptId: user.dept.deptId,
          deptName: user.dept.deptName,
        };
      });
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
      this.currentRow = this.missionList ? this.missionList[0] : {};
      EventBus.$emit("updateMissionData", this.currentRow);
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dateRange = [];
      this.resetForm("queryForm");
      this.handleQuery();
    },
    handleRowClick(row) {
      this.currentRow = row;
      EventBus.$emit("updateMissionData", row);
    },
  },
};
</script>
<style scoped>
.flex-form {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
}

.flex-item {
  width: 40%;
}

.el-form-item {
  margin-bottom: 0; /* 去掉默认的底部间距 */
}

.el-input {
  width: 100%; /* 使输入框占满 flex-item */
}

/* 响应式设计：屏幕宽度小于600px时 */
@media (max-width: 600px) {
  .flex-form {
    flex-wrap: wrap; /* 允许换行 */
  }

  .flex-item {
    flex: 1 1 100%; /* 每个项占满一行 */
    margin-bottom: 10px; /* 增加底部间距 */
  }

  .el-form-item {
    width: 100%; /* 确保表单项占满一行 */
  }

  .button-container {
    display: flex;
    flex-direction: column; /* 垂直排列按钮 */
    gap: 5px; /* 按钮之间的间距 */
  }

  .el-button {
    width: 100%; /* 按钮占满一行 */
  }
  .el-picker-panel__footer .el-button--text.el-picker-panel__link-btn {
    display: none;
  }
}
</style>
