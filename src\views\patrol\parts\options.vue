<template>
  <el-drawer v-bind="$attrs" v-on="$listeners">
    <div class="parts-options-container">
      <el-descriptions size="mini">
        <el-descriptions-item label="类型">
          {{ selectDictLabel(dict.type.patrol_inspection_category, data.partsType) }}
        </el-descriptions-item>
        <el-descriptions-item label="名称">
          {{ data.partsName }}
        </el-descriptions-item>
      </el-descriptions>
      <!--内容区域-->
      <div style="height: 75vh;overflow: auto;margin-top: 20px;display: flex;justify-content: space-around;">
        <!---------------->
        <el-card style="width: 48%" shadow="hover">
          <div slot="header">
            <span>{{ title[0] }}</span>
            <el-button style="float: right; padding: 3px 0" type="text" v-if="false">新增</el-button>
          </div>

          <div style="height: 48px">
            <el-button v-show="!showAdd" type="primary" size="mini"
                       @click="params.name= ''; showAdd=!showAdd; showUpdate=!showAdd">添加
            </el-button>
            <div class="parts-options-list" v-show="showAdd">
              <div>
                <el-input v-model="params.name" size="mini" :placeholder="`请输入${title[0]}`"
                          style="width: 100%"></el-input>
              </div>
              <div>
                <i class="el-icon-circle-close" title="取消" @click="params.name= '';showAdd = false;"></i>
                <i class="el-icon-circle-check" title="提交" @click="onAdd();params.name= '';showAdd=false;"></i>
              </div>
            </div>
          </div>
          <div class="parts-options-list" v-for="item in Object.keys(mateEntity)" :key="item"
               @click="currentSelect = item"
               :class="{'parts-options-current-select': currentSelect === item}"
               :style="{background: current === item ? 'rgb(245, 247, 250)':''}">
            <div>
              <el-input v-if="showUpdate && current === item" v-model="params.name" size="mini"/>
              <span v-else :style="{cursor: 'pointer'}">{{ item }}</span>
            </div>

            <div style="visibility: hidden" @click.stop>
                      <span v-if="showUpdate && current === item">
                        <i class="el-icon-circle-close" title="取消"
                           @click="params.name= '';showUpdate=false; current = null"></i>
                        <i class="el-icon-circle-check" title="提交"
                           @click="onUpdate(); params.name= ''; showUpdate=false; current = null"></i>
                      </span>
              <span v-else>
                        <i class="el-icon-edit" title="修改"
                           @click=" current = params.name = item; showAdd = false; showUpdate = true"></i>
                        <i class="el-icon-delete" title="删除" @click="current = item; onDelete();"></i>
                      </span>

            </div>
          </div>

        </el-card>
        <!---------------->
        <el-button style="height: 50px" type="primary" disabled icon="el-icon-arrow-right" v-if="false"></el-button>

        <el-card style="width: 48%" shadow="hover">
          <div slot="header">
            <span>{{ title[1] }}<span style="font-size: 12px">（{{currentSelect}}）</span></span>
            <el-button style="float: right; padding: 3px 0" type="text" v-if="false">新增</el-button>
          </div>

          <div style="height: 48px">
            <el-button v-show="!showAdd2" type="primary" size="mini"
                       @click="params.value= ''; showAdd2=!showAdd2; showUpdate2=!showAdd2">添加
            </el-button>
            <div class="parts-options-list" v-show="showAdd2">
              <div>
                <el-input v-model="params.value" size="mini" :placeholder="`请输入${title[1]}`"
                          style="width: 100%"></el-input>
              </div>
              <div>
                <i class="el-icon-circle-close" title="取消" @click="params.value= ''; showAdd2=false; "></i>
                <i class="el-icon-circle-check" title="提交" @click="onAdd(); params.value= ''; showAdd2=false; "></i>
              </div>
            </div>
          </div>

          <div class="parts-options-list" v-for="item in dataList" :key="item"
               :style="{background: current2 === item ? 'rgb(245, 247, 250)':''}">
            <div>
              <el-input v-if="showUpdate2 && current2 === item" v-model="params.value" size="mini"/>
              <span v-else>{{ item }}</span>
            </div>

            <div style="visibility: hidden">
                      <span v-if="showUpdate2 && current2 === item">
                        <i class="el-icon-circle-close" title="取消"
                           @click="params.value= ''; showUpdate2=false; current2 = null"></i>
                        <i class="el-icon-circle-check" title="提交"
                           @click="onUpdate();params.value= ''; showUpdate2=false;current2 = null"></i>
                      </span>
              <span v-else>
                        <i class="el-icon-edit" title="修改"
                           @click="current2 = params.value = item; showUpdate2 = true; showAdd2 = false"></i>
                        <i class="el-icon-delete" title="删除" @click="current2  = item; onDelete();"></i>
                      </span>

            </div>
          </div>


        </el-card>


      </div>
      <el-button type="primary" size="small" style="position: absolute;bottom: 10px;right: 20px" @click="onSubmit"
                 v-if="false">
        <i v-if="loading" class="el-icon-loading el-icon--left"/>完成
      </el-button>
    </div>
  </el-drawer>
</template>

<script>
import {getParts, updateParts} from "@/api/patrol/parts";

export default {
  name: "PartsOptions",
  dicts: ['patrol_inspection_category'],
  props: {
    data: {
      type: Object,
      default: {}
    }
  },
  data() {
    return {
      loading: false,
      showAdd: false,
      showUpdate: false,
      showAdd2: false,
      showUpdate2: false,
      current: null,
      current2: null,
      params: {
        name: '',
        value: ''
      },
      entity: {
        name: '',
        level: 1,
        children: {}
      },
      tempEntity: {
        "未见异常": ["11", "22"]
      },
      requestEntity: {},
      mateEntity: {},
      // dataList: [],
      currentSelect: null,
      partsType2Title: {
        '1': ['状态描述', '保养措施意见'],
        '2': ['缺损类型', '缺损范围及保养意见'],
        '3': ['缺损类型', '缺损范围及保养意见'],
        '4': ['缺损类型', '缺损范围及保养意见'],
        '5': ['状态描述', '保养措施意见'],
        '6': ['缺损类型', '缺损范围及保养意见'],
      },
    };
  },
  computed: {
    title() {
      return this.partsType2Title[this.data.partsType ?? ''] ?? {}
    },
    dataList() {
      if (this.currentSelect) {
        return this.mateEntity[this.currentSelect]
      } else {
        let returnArray = []
        for (let temp in this.mateEntity) {
          returnArray = [...returnArray, ...this.mateEntity[temp]]
        }
        return returnArray
      }
    }
  },
  methods: {
    onSubmit() {
    },
    getList() {
      getParts(this.data.id).then(res => {
        // this.requestEntity = this.mateEntity = JSON.parse(res.data.extend)
        this.mateEntity = JSON.parse(res.data.extend)
        this.requestEntity = JSON.parse(res.data.extend)
        let flag = Object.keys(this.mateEntity).filter(item => item === this.currentSelect).length > 0
        if (!flag)
          this.currentSelect = Object.keys(this.mateEntity)[0]
      })
    },
    onAdd() {
      let tempArray = [...Object.keys(this.mateEntity).filter(item => item === this.params.name), ...this.mateEntity[this.currentSelect].filter(item => item === this.params.value)]
      if (tempArray.length > 0) {
        this.$modal.msgWarning("已存在相同记录");
        return
      }
      let request = {id: this.data.id, extend: JSON.stringify(this.handler())}
      updateParts(request).then(res => {
        this.$modal.msgSuccess("新增成功");
        this.getList()
      })
    },
    onUpdate() {
      let tempArray = [...Object.keys(this.mateEntity).filter(item => item === this.params.name), ...this.mateEntity[this.currentSelect].filter(item => item === this.params.value)]
      if (tempArray.length > 0) {
        this.$modal.msgWarning("已存在相同记录");
        return
      }
      let request = {id: this.data.id, extend: JSON.stringify(this.handler())}
      updateParts(request).then(res => {
        this.$modal.msgSuccess("修改成功");
        this.getList()
      })
    },
    onDelete() {
      let request = {id: this.data.id, extend: JSON.stringify(this.handler())}
      updateParts(request).then(res => {
        this.$modal.msgSuccess("删除成功");
        this.getList()
      })
      this.current = this.current2 = null
    },
    handler() {
      if (this.showAdd || this.showAdd2) {
        if (this.params.name) {
          this.requestEntity[this.params.name] = []
        }
        if (this.params.value) {
          this.requestEntity[this.currentSelect] = [...this.requestEntity[this.currentSelect], this.params.value]
        }
      } else if (this.showUpdate || this.showUpdate2) {
        if (this.params.name) {
          let tempData = this.requestEntity[this.current]
          delete this.requestEntity[this.current]
          this.requestEntity[this.params.name] = tempData
        }
        if (this.params.value) {
          this.requestEntity[this.currentSelect] = this.requestEntity[this.currentSelect].toString().replace(this.current2, this.params.value).split(',')
        }
      } else {
        if (this.current) {
          delete this.requestEntity[this.current]
        }
        if (this.current2) {
          this.requestEntity[this.currentSelect] = this.requestEntity[this.currentSelect].filter(item => item !== this.current2)
        }
      }
      return this.requestEntity
    }
  },
  watch: {
    data(value) {
      if (value.partsType) {
        // this.requestEntity = this.mateEntity =  JSON.parse(this.data.extend)
        this.mateEntity = JSON.parse(this.data.extend)
        this.requestEntity = JSON.parse(this.data.extend)
        this.currentSelect = Object.keys(this.mateEntity)[0]
      }
      // this.getList()
    },
  },
};
</script>
<style>
.parts-options-container {
  margin-top: -20px;
  padding: 20px;
}

.parts-options-list {
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: relative;
  user-select: none;
  height: 37.33px;
  padding: 10px;
  margin-bottom: 5px;
  font-size: 14px;
  color: rgb(114, 118, 123);
  border-radius: 4px;
  background: rgb(245, 247, 250);
}

.parts-options-list:hover > div {
  visibility: unset !important;
}

.parts-options-list:hover {
  background: rgb(245, 247, 250);
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  border-radius: 4px;
  font-size: 15px;
}

.parts-options-list > :first-child {
  flex-grow: 1;
}

.parts-options-container .el-icon-circle-close,
.parts-options-container .el-icon-circle-check,
.parts-options-container .el-icon-delete,
.parts-options-container .el-icon-edit {
  font-size: 16px;
  padding: 0px 5px;
  cursor: pointer;
}

/*按钮图标颜色*/
.parts-options-container .el-icon-edit {
  color: #1C83E4;
}

.parts-options-container .el-icon-circle-close {
  color: #D0021B;
}

.parts-options-container .el-icon-circle-check {
  color: #2FD351;
}

.parts-options-container .el-icon-delete {
  /*  color: #1C83E4;*/
  /*  color: #1171CD*/
}

.parts-options-current-select {
  background: rgb(245, 247, 250) !important;
  color: #1171CD;
 /* color: black;*/
  font-weight: 500;
}

.parts-options-current-select:before {
  content: '';
  display: inline-block;
  position: absolute;
  left: -2px;
  width: 5px;
  height: 1.25rem;
  vertical-align: bottom;
  background: #3797EB;
  border-radius: 2px;
}

/*.parts-options-title {
  color: #333333;
  margin: 10px 0;
}
.parts-options-title:before {
  content: '';
  display: inline-block;
  width: 5px;
  height: 1.25rem;
  vertical-align: bottom;
  margin-right: 0.8rem;
  background: #3797EB;
}*/
</style>
