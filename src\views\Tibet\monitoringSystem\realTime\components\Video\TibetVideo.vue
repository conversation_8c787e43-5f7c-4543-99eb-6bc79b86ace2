<template>
    <div class="hello-ezuikit-js">
        <el-select
            style="margin: 30px 0px"
            v-model="videoSelect"
            :teleported="false"
            popper-class="popperClass"
            placeholder="请选择"
            size="mini"
            @change="changeVideo"
        >
            <el-option v-for="(item, index) in videos" :key="item.index" :label="item.name" :value="item.id"> </el-option>
        </el-select>
        <div class="video-box" ref="videoContainer">
            <div id="video-container"></div>
        </div>
        <div>
            <button v-on:click="init">初始化视频</button>
            <button v-on:click="stop">停止视频</button>
            <button v-on:click="play">开始播放</button>
			s
        </div>
    </div>
</template>
 
<script>
import EZUIKit from 'ezuikit-js';
var player = null;
 
export default {
    name: 'HelloWorld',
    props: {
        msg: String
    },
    data() {
        return {
            videoSelect: 1,
            videos: [
                {
                    id: 1,
                    accessToken: 'at.bzmch15u8shbyqzf0kq74ka28x5kbf3d-3goop61n29-0ib0p1u-uf7zagxgi',
                    name: '视频11',
                    address: 'ezopen://<EMAIL>/GA9932307/1.cloud.rec'
                },
                {
                    id: 2,
                    name: '回放',
                    accessToken: 'at.bzmch15u8shbyqzf0kq74ka28x5kbf3d-3goop61n29-0ib0p1u-uf7zagxgi',
                    address: 'ezopen://<EMAIL>/GA9932307/1.rec'
                }
            ]
        };
    },
    mounted: () => {
        console.group('mounted 组件挂载完毕状态===============》');
    },
    methods: {
        init() {
            if (player) {
                this.destroy();
            }
            const findItms = this.videos.find((item) => item.id === this.videoSelect);
            const container = this.$refs.videoContainer;
            console.log(container.clientWidth, container.clientHeight, '最大值和最小值');
            player = new EZUIKit.EZUIKitPlayer({
                id: 'video-container', // 视频容器ID
                accessToken: findItms.accessToken,
                url: findItms.address,
                // simple: 极简版; pcLive: pc直播; pcRec: pc回放; mobileLive: 移动端直播; mobileRec: 移动端回放;security: 安防版; voice: 语音版;
                template: 'pcLive',
                // plugin: ["talk"], // 加载插件，talk-对讲
                width: container.clientWidth,
                height: container.clientHeight,
                handleError: (error) => {
                    console.error('handleError', error);
                },
                // language: "en", // zh | en
                // staticPath: "/ezuikit_static", // 如果想使用本地静态资源，请复制根目录下ezuikit_static 到当前目录下， 然后设置该值
                env: {
                    // https://open.ys7.com/help/1772?h=domain
                    // domain默认是 https://open.ys7.com, 如果是私有化部署或海外的环境，请配置对应的domain
                    // The default domain is https://open.ys7.com If it is a private deployment or overseas (outside of China) environment, please configure the corresponding domain
                    domain: 'https://open.ys7.com'
                }
            });
            window.player = player;
        },
        play() {
            var playPromise = player.play();
            playPromise.then((data) => {
                console.log('promise 获取 数据', data);
            });
        },
        stop() {
            var stopPromise = player.stop();
            stopPromise.then((data) => {
                console.log('promise 获取 数据', data);
            });
        },
        changeVideo(val) {
            console.log(val, '-----');
            let options = this.videos.find((item) => item.id == val);
            player
                .changePlayUrl({
                    // minHeight: 100, // 视频最小高度，单位为px
                    accessToken: options.accessToken, //accessToken 的值为你在莹石云平台监控地址的token
                    url: options.address
                })
                .then(() => {
                    console.log('切换成功');
                });
        },
        destroy() {
            var destroyPromise = player.destroy();
            destroyPromise.then((data) => {
                console.log('promise 获取 数据', data);
            });
            player = null;
        }
    }
};
</script>
<style lang="scss" scoped>
.hello-ezuikit-js {
    height: 1400px;
    width: 100%;
}
.video-box {
    width: 60vw;
    height: 60vh;
}
</style>