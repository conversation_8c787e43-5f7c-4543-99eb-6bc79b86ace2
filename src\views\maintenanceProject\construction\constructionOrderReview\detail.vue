<template>
  <div class="app-container">
    <el-form ref="elForm" :model="formData" :inline="true" label-width="200px" v-loading="loading">
      <el-row :gutter="20">
        <el-col :span="24" class="mb20">
          <div class="card_title">项目信息</div>
          <el-descriptions size="mini" :column="3" border :labelStyle="{width: '150px'}" :contentStyle="{width: '300px'}">
            <el-descriptions-item v-for="(item, index) in projectColumns" :key="index" :label="item.name">
              <el-tooltip :content="projectData[item.field]" placement="top">
                <dict-tag v-if="item.dict" :options="dict.type[item.dict]" :value="projectData[item.field]"/>
                <span v-else>{{ projectData[item.field] }}</span>
              </el-tooltip>
            </el-descriptions-item>
          </el-descriptions>
        </el-col>
        <el-col :span="24" style="margin-bottom: 18px">
          <div class="card_title">施工前图片</div>
          <el-card class="box-card" shadow="never">
            <el-form-item label="">
              <sort-file-upload v-model="sgqcjzp" can-sort ref="sgqcjzp" for-view></sort-file-upload>
            </el-form-item>
          </el-card>
        </el-col>
        <el-col :span="24" style="margin-bottom: 18px">
          <div class="card_title">施工后图片</div>
          <el-card class="box-card" shadow="never">
            <el-form-item label="">
              <sort-file-upload v-model="sgfj" can-sort ref="sgfj" for-view></sort-file-upload>
            </el-form-item>
          </el-card>
        </el-col>
        <el-col :span="24">
          <div class="card_title">签证附件</div>
          <el-card class="box-card" shadow="never">
            <el-form-item label="">
              <sort-file-upload v-model="wgfj" can-sort ref="wgfj" for-view></sort-file-upload>
            </el-form-item>
          </el-card>
        </el-col>
        <el-col :span="24">
          <div class="card_title">审核照片</div>
          <el-card class="box-card" shadow="never">
            <el-form-item label="">
              <sort-file-upload v-model="shzp" can-sort ref="shzp" :file-type="['png', 'jpg', 'jpeg']"></sort-file-upload>
            </el-form-item>
          </el-card>
        </el-col>
        <el-col :span="24">
          <div class="card_title">审核附件</div>
          <el-card class="box-card" shadow="never">
            <el-form-item label="">
              <sort-file-upload v-model="shfj" can-sort ref="shfj"></sort-file-upload>
            </el-form-item>
          </el-card>
        </el-col>
        <el-col :span="24" style="margin-top: 18px">
          <el-tabs v-if="finishedStructureList.length > 0" v-model="editableTabsValue">
            <el-tab-pane v-for="item in finishedStructureList" :label="item.name" :key="item.name" :name="item.name" >
              <div class="card_title" style="display: flex;width: 100%;justify-content: space-between">
                <div>登记子目</div>
              </div>
              <methods-list editSafetyFee :value.sync="item.finishedMethodList":read-only="true"></methods-list>
              <el-col :span="24" style="margin-top: 18px">
                <div class="card_title">养护措施</div>
                <el-input v-model="item.maintainMeasureName" placeholder="请选择养护措施" readonly style="width: 240px">
                </el-input>
              </el-col>
            </el-tab-pane>
          </el-tabs>
          <template v-else>
            <div class="card_title" style="display: flex;width: 100%;justify-content: space-between">
              <div>登记子目</div>
            </div>
            <methods-list editSafetyFee :value.sync="methodList" :read-only="true"></methods-list>
          </template>
        </el-col>
        <el-col :span="24" style="margin-top: 18px">
          <div class="card_title" style="display: flex;width: 100%;justify-content: space-between">
            <div>计算式及说明</div>
          </div>
          <el-input class="calculation_desc" v-model="formData.calculationDesc" type="textarea" :rows="4" readonly>
          </el-input>
        </el-col>
        <el-col :span="24" style="margin-top: 18px">
          <div class="card_title">审核意见</div>
          <el-input v-model="formData.comment" type="textarea" :rows="4">
          </el-input>
        </el-col>
      </el-row>
      <div style="text-align: right;padding-right: 7.5px;margin-top: 18px">
        <el-button type="primary" @click="handleExamine(true)">通过</el-button>
        <el-button type="danger" @click="handleExamine(false)">驳回</el-button>

        <el-button @click="onClose">退出</el-button>
      </div>
    </el-form>
  </div>
</template>
<script>
import sortFileUpload from "@/components/SortFileUpload/index.vue";
import moment from "moment";
import {
  addFinishedDetail,
  editFinishedDetail, finishedProcess, getFinishedInfo,
} from "@/api/maintenanceProject/construction/completionReg";
import MethodsTree from "@/components/MethodsTree/index.vue";
import MethodsList from "@/components/MethodsList/index.vue";
import {getConstructionById} from "@/api/maintenanceProject/taskList";

export default {
  name: "index",
  components: {MethodsList, MethodsTree, sortFileUpload},
  data() {
    return {
      formData: {},
      sgqcjzp: '',
      sgfj: '',
      wgfj: '',
      shzp: '',
      shfj: '',
      loading: false,
      taskId: '',
      finishedStructureList: [],
      editableTabsValue: '',
      methodList: [],
      projectColumns: [
        { name: '项目名称', field: 'projName', dict: '' },
        { name: '任务单名称', field: 'name', dict: '' },
        { name: '任务单编码', field: 'code', dict: '' },
        { name: '路段名称', field: 'maiSecName', dict: '' },
        { name: '位置', field: 'mileRange', dict: '' },
        { name: '实施要求', field: 'exeRequire', dict: '' },
        { name: '施工单位', field: 'conDomainName', dict: '' },
        { name: '施工合同', field: 'conConName', dict: '' },
        { name: '监理单位', field: 'supDomainName', dict: '' },
        { name: '监理合同', field: 'supConName', dict: '' },
        { name: '设计单位', field: 'designDomainName', dict: '' },
        { name: '设计合同', field: 'designConName', dict: '' },
        { name: '工作内容', field: 'content', dict: '' },
      ],
      projectData: {}
    }
  },
  props: {
    rowData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  watch: {
    rowData: {
      handler(val) {
        if (val.id) {
          getConstructionById(val.conId).then(res => {
            this.projectData = res.data
          })
          getFinishedInfo(val.id).then(res => {
            this.formData = res.data
            this.methodList = this.formData.constructionFinishedMethodList || []
            this.finishedStructureList = this.formData.finishedStructureList || []
            if (this.finishedStructureList.length > 0) this.editableTabsValue = this.finishedStructureList[0].name

            const sgqcjzp = this.formData.fileList.filter(item => item.registerType == 1)
            this.sgqcjzp = sgqcjzp.map(item => item.fileId)

            const sgfj = this.formData.fileList.filter(item => item.registerType == 2)
            this.sgfj = sgfj.map(item => item.fileId)

            const wgfj = this.formData.fileList.filter(item => item.registerType == 4)
            this.wgfj = wgfj.map(item => item.fileId)
          })
        }
      },
      immediate: true
    }
  },
  mounted() {
  },
  methods: {
    handleExamine(approved) {
      this.generateParams()
      this.loading = true
      const params = {
        businessKey: this.rowData.id,
        taskId: this.rowData.taskId,
        approved,
        comment: this.formData.comment,
        isSupProcess: 2,
        constructionFileList: this.formData.fileList
      }
      finishedProcess(params).then(res => {
        this.loading = false
        this.$modal.msgSuccess("提交成功")
        this.$emit('close')
      })
    },
    // 拼接参数
    generateParams() {
      this.$refs.shfj.save()
      this.$refs.shzp.save()
      // 拼接参数
      const fileList = []
      if (this.shfj) {
        this.shfj.forEach((item, index) => {
          fileList.push({
            fileId: item,
            indexOrder: index,
            registerType: 6
          })
        })
      }
      if (this.shzp) {
        this.shzp.forEach((item, index) => {
          fileList.push({
            fileId: item,
            indexOrder: index,
            registerType: 5
          })
        })
      }
      this.formData.fileList = fileList
    },
    onClose() {
      this.$emit("close")
    },
  }
}
</script>
<style scoped lang="scss">
.card_title {
  width: 200px;
  text-align: left;
  margin-bottom: 15px;
  font-weight: bold;
}
</style>
<style lang="scss" scoped>
@import "@/assets/styles/business.scss";
</style>
