<template>
  <div class="app-container">
    <el-row :gutter="20">
      <!--筛选区开始-->
      <el-col :span="24" :xs="24">
        <el-row>
          <el-col :span="24">
            <el-form ref="queryForm" :inline="true" :model="queryParams" label-width="68px" size="small">
              <el-form-item label="" prop="disType">
                <el-date-picker
                    v-model="queryParams.year"
                    placeholder="年份"
                    style="width: 240px"
                    type="year"
                    value-format="yyyy"
                >
                </el-date-picker>
              </el-form-item>
              <el-form-item>
                <selectTree
                    :key="'domainId'"
                    v-model="queryParams.domainId"
                    :deptType="100"
                    :deptTypeList="[1, 3, 4]" clearable
                    filterable
                    placeholder="管养单位"
                    style="width: 240px"
                />
              </el-form-item>
              <el-form-item>
                <selectTree
                    :key="'constructionUnit'"
                    v-model="queryParams.calcDomainId" :data-rule="false"
                    :dept-type="100"
                    placeholder="施工单位"
                    :filter-keys="['云南省交通投资建设集团有限公司', '云南交投投资有限公司']"
                    :expand-all="false"
                    clearable
                    filterable
                    style="width: 240px"
                />
              </el-form-item>
              <el-form-item>
                <RoadSection v-model="queryParams.maiSecId" :deptId="queryParams.domainId" placeholder="路段"
                             style="width: 240px"/>
              </el-form-item>
              <el-form-item>
                <el-input v-model="queryParams.name" placeholder="项目名称" style="width: 240px"></el-input>
              </el-form-item>
              <el-form-item>
                <el-input v-model="queryParams.code" placeholder="项目编码" style="width: 240px"></el-input>
              </el-form-item>
              <el-form-item v-if="!mtype">
                <cost-select :type="35"
                    v-model="queryParams.mtype"
                    placeholder="工程分类"
                    style="width: 240px"
                />
              </el-form-item>
              <el-form-item>
                <dict-select
                    v-model="queryParams.projectType"
                    type="theft_project_type"
                    placeholder="所属工程类别"
                    style="width: 240px"
                />
              </el-form-item>
              <el-form-item>
                <el-input v-model="queryParams.constructionCode" placeholder="任务单编码" style="width: 240px"></el-input>
              </el-form-item>
              <el-form-item>
                <dict-select
                    v-model="queryParams.status"
                    type="project_status_type"
                    placeholder="项目状态"
                    style="width: 240px"
                />
              </el-form-item>
              <el-form-item>
                <el-input v-model="queryParams.createuser" placeholder="操作人" style="width: 240px"></el-input>
              </el-form-item>
              <el-form-item>
                <el-button icon="el-icon-search" size="mini" type="primary" @click="handleQuery">搜索</el-button>
                <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
              </el-form-item>
            </el-form>
          </el-col>
        </el-row>
        <!--筛选区结束-->

        <!--操作按钮区开始-->
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button
                icon="el-icon-edit"
                size="mini"
                type="primary"
                v-has-menu-permi="['theft:project:add']"
                @click="handleAdd"
            >新增
            </el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button
                icon="el-icon-download"
                size="mini"
                type="success"
                v-has-menu-permi="['theft:project:export']"
                @click="exportList"
            >导出
            </el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button
                size="mini"
                type="warning"
                v-has-menu-permi="['theft:project:updateStatus']"
                @click="updProject(1)"
            >项目开始
            </el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button
                size="mini"
                type="danger"
                v-has-menu-permi="['theft:project:updateStatus']"
                @click="updProject(2)"
            >项目结束
            </el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button
                size="mini"
                type="primary"
                v-has-menu-permi="['theft:project:updateStatus']"
                @click="updProject(0)"
            >恢复到未开始
            </el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button
              size="mini"
              type="success"
              v-has-menu-permi="['theft:project:updateStatus']"
              @click="updProject(1)"
            >恢复到进行中
            </el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button
                size="mini"
                type="success"
                v-has-menu-permi="['theft:construction:add']"
                @click="addTask('2')"
            >生成检测任务单
            </el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button
                size="mini"
                type="warning"
                v-has-menu-permi="['theft:construction:add']"
                @click="addTask('1')"
            >生成施工任务单
            </el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button
              size="mini"
              type="primary"
              @click="addClaimRecord"
            >生成索赔记录
            </el-button>
          </el-col>
<!--          <el-col :span="1.5">-->
<!--            <el-button-->
<!--                size="mini"-->
<!--                type="primary"-->
<!--                @click="addTask('3')"-->
<!--            >生成设计任务单-->
<!--            </el-button>-->
<!--          </el-col>-->
          <right-toolbar :columns="columns" :showSearch.sync="showSearch" @queryTable="handleQuery"></right-toolbar>
        </el-row>
        <!--操作按钮区结束-->
        <!--数据表格开始-->
        <div class="tableDiv">
          <el-table v-adjust-table
              ref="dataTable"
              v-loading="loading"
              :data="tableData"
              :height="'calc(100vh - 330px)'"
              border
              highlight-current-row
              row-key="id"
              size="mini"
              @row-click="handleClickRow"
              stripe
              style="width: 100%"
          >
            <el-table-column
                align="center"
                label="序号"
                type="index"
                width="50"
            />
            <template v-for="(column,index) in columns">
              <el-table-column v-if="column.visible" show-overflow-tooltip
                               :label="column.label"
                               :prop="column.field"
                               :width="column.width"
                               align="center">
                <template slot-scope="scope">
                  <dict-tag v-if="column.dict" :options="dict.type[column.dict]" :value="scope.row[column.field]"/>
                  <template v-else-if="column.slots">
                    <RenderDom :index="index" :render="column.render" :row="scope.row"/>
                  </template>
                  <span v-else-if="column.isTime">{{ parseTime(scope.row[column.field], '{y}-{m}-{d}') }}</span>
                  <span v-else>{{ scope.row[column.field] }}</span>
                </template>
              </el-table-column>
            </template>
            <el-table-column
                align="center"
                class-name="small-padding fixed-width"
                fixed="right"
                label="操作"
                width="250"
            >
              <template slot-scope="scope">
                <el-button
                    icon="el-icon-delete"
                    size="mini"
                    type="text"
                    v-has-menu-permi="['theft:project:remove']"
                    :disabled="scope.row.status != 0"
                    @click="handleDelete(scope.row)"
                >删除
                </el-button>
                <el-button
                    icon="el-icon-edit"
                    size="mini"
                    type="text"
                    v-has-menu-permi="['theft:project:edit']"
                    @click="handleEdit (scope.row)"
                >修改
                </el-button>
                <el-button
                    icon="el-icon-view"
                    size="mini"
                    type="text"
                    @click="handleView (scope.row)"
                >查看
                </el-button>
                <el-button
                    icon="el-icon-tickets"
                    size="mini"
                    type="text"
                    @click="handleEdit (scope.row)"
                >资料管理
                </el-button>
              </template>
            </el-table-column>
          </el-table>
          <pagination
              v-show="total>0"
              :limit.sync="queryParams.pageSize"
              :page.sync="queryParams.pageNum"
              :total="total"
              @pagination="handleQuery"
          />
        </div>
        <!--数据表格结束-->
      </el-col>
    </el-row>
    <el-drawer :wrapperClosable="false" :title="detailTitle" :visible.sync="openDetail" size="75%" destroy-on-close v-if="openDetail">
      <detail :row-data="row" @close="handleClose" :readonly="readonly"></detail>
    </el-drawer>
    <el-drawer :wrapperClosable="false" title="新增任务单" :visible.sync="openTask" size="70%" destroy-on-close v-if="openTask">
      <task-detail :type="taskType" @close="handleClose" :project="checkRow"></task-detail>
    </el-drawer>
    <el-dialog title="附件列表" destroy-on-close :visible.sync="openFile" width="80%">
      <file-upload v-model="filePath" :forView="true"></file-upload>
    </el-dialog>
    <el-dialog title="新增索赔记录" :visible.sync="dialogVisible" width="50%">
      <el-form ref="dataForm" :rules="rules" :model="formData" label-width="120px">
        <el-form-item label="项目名称" prop="projName">
          <el-input v-model="formData.projName" placeholder="项目名称" readonly></el-input>
        </el-form-item>
        <el-form-item label="赔偿协议编号" prop="code">
          <el-input v-model="formData.code" placeholder="赔偿协议编号"></el-input>
        </el-form-item>
        <el-form-item label="索赔人" prop="claimBy">
          <el-input v-model="formData.claimBy" placeholder="索赔人"></el-input>
        </el-form-item>
        <el-form-item label="索赔金额" prop="claimFund">
          <el-input v-model="formData.claimFund" placeholder="索赔金额"></el-input>
        </el-form-item>
        <el-form-item label="是否赔付完成" prop="isClaim">
          <dict-select v-model="formData.isClaim" type="is_claim" placeholder="是否赔付完成"></dict-select>
        </el-form-item>
        <el-form-item label="赔付说明" prop="claimDesc">
          <el-input type="textarea" :rows="4" v-model="formData.claimDesc" placeholder="赔付说明"></el-input>
        </el-form-item>
        <el-form-item label="附件" prop="fileId">
          <file-upload key="fileId" v-model="formData.fileId" :owner-id="formData.fileId"></file-upload>
        </el-form-item>
      </el-form>
      <div style="text-align: right">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleSave">保存</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import Tables from "@/views/patrol/frequencySettings/tables.vue";
import RoadSection from "@/views/baseData/components/roadSection/index.vue";
import selectTree from "@/components/DeptTmpl/selectTree.vue";
import Detail from "./projectDetail.vue";
import TaskDetail from "@/views/theft/taskList/component/taskDetail.vue";
import {deleteProject, isAdd, queryList, updateProjectStatus} from "@/api/theft/projectManage";
import CostSelect from "@/components/CostSelect/index.vue";
import {addClaim, editClaim} from "@/api/theft/claimRecord";
export default {
  dicts: ['theft_m_type', 'theft_project_type', 'route_direction', 'lane', 'project_status_type', 'is_claim'],
  components: {
    CostSelect, Detail, selectTree, RoadSection, Tables, TaskDetail,
    RenderDom: {
      functional: true,
      props: {
        row: Object,
        index: Number,
        render: Function
      },
      render(createElement, ctx) {
        const {row, index} = ctx.props
        return ctx.props.render(row, index)
      }
    }
  },
  props: {
    mtype: {
      type: String,
      default: null
    }
  },
  data() {
    return {
      showSearch: false,
      queryParams: {
        pageNum: 1,
        pageSize: 50
      },
      total: 0,
      loading: false,
      columns: [
        { key: 0, width: 100, field: 'status', label: `状态`, visible: true, dict: 'project_status_type' },
        { key: 1, width: 100, field: 'domainName', label: `管养单位`, visible: true },
        { key: 2, width: 100, field: 'mtype', label: `工程类型`, visible: true,dict: 'theft_m_type' },
        { key: 3, width: 100, field: 'projectType', label: `项目类别`, visible: true,dict: 'theft_project_type' },
        { key: 4, width: 200, field: 'name', label: `工程名称`, visible: true },
        { key: 5, width: 200, field: 'code', label: `工程编码`, visible: true },
        { key: 6, width: 100, field: 'maiSecName', label: `路段名称`, visible: true },
        { key: 7, width: 100, field: 'claimFund', label: `赔偿金额`, visible: true },
        { key: 8, width: 100, field: 'claimCode', label: `赔偿协议`, visible: true },
        { key: 9, width: 100, field: 'isClaim', label: `是否赔付完成`, visible: true, dict: 'is_claim' },
        {
          key: 10,
          width: 100,
          field: 'constructionTaskList',
          label: `施工任务单`,
          visible: true,
          slots: true,
          render: (row) => {
            return (
                <el-link disabled={!row.constructionTaskList} type="primary" underline
                         onClick={e => this.handleOpenDetail(row.code, 1)}>{row.constructionTaskList}</el-link>
            )
          }
        },
        {
          key: 11, width: 100, field: 'checkTaskList', label: `检测任务单`, visible: true,
          slots: true,
          render: (row) => {
            return (
                <el-link disabled={!row.checkTaskList} type="primary" underline
                         onClick={e => this.handleOpenDetail(row.code, 2)}>{row.checkTaskList}</el-link>
            )
          }
        },
        {
          key: 12, width: 100, field: 'designTaskList', label: `设计任务单`, visible: true,
          slots: true,
          render: (row) => {
            return (
                <el-link disabled={!row.designTaskList} type="primary" underline
                         onClick={e => this.handleOpenDetail(row.code, 3)}>{row.designTaskList}</el-link>
            )
          }
        },
        { key: 13, width: 100, field: 'routeCode', label: `路线编码`, visible: true },
        { key: 14, width: 100, field: 'beginMileStr', label: `起点桩号`, visible: true },
        { key: 15, width: 100, field: 'endMileStr', label: `终点桩号`, visible: true },
        { key: 16, width: 100, field: 'direction', label: `上下行`, visible: true,dict: 'route_direction' },
        { key: 17, width: 100, field: 'lane', label: `位置`, visible: true, dict: 'lane'},
        { key: 18, width: 200, field: 'enContent', label: `工程内容`, visible: true },
        { key: 19, width: 100, field: 'fileId', label: `图片`, visible: true,slots: true,
          render: (row, index) => {
            return (
              <el-button
                size="mini"
                disabled={!row.fileId}
                type="text" onClick={e => this.handleOpenFile(e, row)}>查看</el-button>
            )
          }},
        { key: 20, width: 100, field: 'updateuser', label: `操作人`, visible: true },
        { key: 21, width: 100, field: 'createuser', label: `上报人`, visible: true },
        { key: 22, width: 100, field: 'causeTime', label: `肇事日期`, visible: true }
      ],
      tableData: [],
      detailTitle: '新增项目信息',
      openDetail: false,
      row: {},
      checkRow: null,
      openTask: false,
      taskType: '1',
      readonly: false,
      openFile: false,
      filePath: '',
      dialogVisible: false,
      rules: {
        projName: [{ required: true, message: '项目名称不能为空', trigger: 'blur' }],
        code: [{ required: true, message: '赔偿协议编号不能为空', trigger: 'blur' }],
        claimBy: [{ required: true, message: '索赔人不能为空', trigger: 'blur' }],
        claimFund: [{ required: true, message: '索赔金额不能为空', trigger: 'blur' }],
        isClaim: [{ required: true, message: '请选择是否赔付完成', trigger: 'blur' }],
      },
      formData: {}
    }
  },
  mounted() {
    this.handleQuery()
  },
  methods: {
    handleQuery() {
      this.loading = true
      if (this.mtype) this.$set(this.queryParams, 'mtype', this.mtype)
      queryList(this.queryParams).then(res => {
        this.tableData = res.rows
        this.total = res.total
        this.loading = false
      })
    },
    resetQuery() {
      this.queryParams = {
        pageNum: 1,
        pageSize: 50
      }
      this.handleQuery()
    },
    handleAdd() {
      this.row = {}
      this.detailTitle = '新增项目信息'
      this.readonly = false
      this.openDetail = true
    },
    handleEdit(row) {
      this.row = row
      this.detailTitle = '修改项目信息'
      this.readonly = false
      this.openDetail = true
    },
    handleDelete(row) {
      this.$modal.confirm("是否确认删除").then(() => {
        this.loading = true
        deleteProject(row.id).then(res => {
          this.$modal.msgSuccess("删除成功")
          this.handleQuery()
        })
      });
    },
    handleOpenFile(e, row) {
      this.filePath = ''
      this.openFile = true
      this.filePath = row.fileId
    },
    handleView(row) {
      this.row = row
      this.detailTitle = '项目信息'
      this.readonly = true
      this.openDetail = true
    },
    handleClose() {
      this.openDetail = false
      this.openTask = false
      this.handleQuery()
    },
    // 导出清单按钮
    exportList() {
      this.queryParams.fileName = '养护工程项目管理'
      this.download(
          'manager/theft/project/export',
          {...this.queryParams},
          `theft_${new Date().getTime()}.xlsx`,
          {
            headers: { 'Content-Type': 'application/json;' },
            parameterType: 'body'
          }
      )
    },
    handleClickRow(row) {
      this.checkRow = row
    },
    updProject(status) {
      if (!this.checkRow) {
        this.$modal.msgError("请选择一条数据")
        return
      } else {
        if (status == 0 && (this.checkRow.constructionTaskList || this.checkRow.checkTaskList|| this.checkRow.designTaskList)) {
          this.$modal.msgError("当前项目存在未完成的任务单，无法更新为未开始")
          return
        }
        if (status == 1 && (this.checkRow.status != 0 && this.checkRow.status != 2)) {
          this.$modal.msgError("当前项目已开始，无法再次更新为已开始")
          return;
        }
        if (status == 2 && this.checkRow.status != 1) {
          this.$modal.msgError("当前项目未开始，无法更新为已结束")
          return
        }
        this.$modal.confirm("是否确认更新？").then(() => {
          this.loading = true
          updateProjectStatus({id: this.checkRow.id, status: status}).then(res => {
            this.$modal.msgSuccess("更新成功")
            this.handleQuery()
          }).finally(() => {
            this.loading = false
          })
        })
      }
    },
    handleOpenDetail(code, type) {
      this.$router.push(`/theft/taskList/taskListPrep?projCode=${code}&taskType=${type}`)
    },
    addTask(type) {
      if (!this.checkRow) {
        this.$modal.msgError("请选择一条数据")
        return
      }
      isAdd(this.checkRow.id).then(res => {
        if (res.code == 200) {
          this.taskType = type
          this.openTask = true
        }
      })
    },
    addClaimRecord() {
      if (!this.checkRow) {
        this.$modal.msgError("请选择一条数据")
        return
      }
      this.formData.projName = this.checkRow.name
      this.formData.projCode = this.checkRow.id
      this.formData.projId = this.checkRow.id
      this.dialogVisible = true
    },
    handleSave() {
      this.$refs.dataForm.validate(valid => {
        if (valid) {
          if (this.formData.fileId && Array.isArray(this.formData.fileId) && this.formData.fileId.length > 0) {
            this.formData.fileId = this.formData.fileId[0]
          } else if (Array.isArray(this.formData.fileId) &&
            this.formData.fileId.length == 0) {
            this.formData.fileId = null
          }
          addClaim(this.formData).then(response => {
            this.$message.success('新增成功')
            this.dialogVisible = false
            this.handleQuery()
          })
        }
      })
    },
  }
}
</script>

<style lang="scss" scoped>

</style>
<style lang="scss" scoped>
@import "@/assets/styles/business.scss";
</style>
