<template>
  <div class="page-two-box">
    <div class="search-box">
      <div class="search-item">
        <span class="search-item-span">选择范围</span>
        <el-date-picker v-model="form.timerange" type="datetimerange" :picker-options="pickerOptions"
          range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期" align="right" class="custom-date-picker"
          value-format="yyyy-MM-dd HH:mm:ss" />
      </div>
      <el-button :loading="loading" class="search-item" type="primary" @click="draw">绘制</el-button>
    </div>
    <div v-if="!isEmpty" class="chart-box" v-loading="loading" element-loading-text="加载中"
      element-loading-spinner="el-icon-loading" element-loading-background="rgba(0, 0, 0, 0.01)">
      <Cards w="100%" h="41vh" :isDtl="false" :isHeader="false" v-for="(item, index) in dataList" :key="index">
        <div :id="item.chartName" class="chart"></div>
      </Cards>
    </div>
    <div v-else class="empty-box">没有数据</div>
  </div>
</template>

<script>
import Cards from "../../components/cards.vue"
import { fetchGet } from "../../../utils/api.js"
import * as echarts from "echarts";
import { getMin, getMax, url } from "./utils/utils.js"
import { isBigScreen } from '../../../utils/utils.js';
import theme from './utils/walden.project.json'

export default {
  name: 'CLHZ',
  props: {
    currentStructure: {
      type: Object,
      default: () => ({})
    }
  },
  components: { Cards },
  data() {
    return {
      isBig: isBigScreen(),
      loading: false,
      dataList: [],
      analysisType: "CLHZ",
      form: {
        timerange: []
      },
      isEmpty: false,
      pickerOptions: {
        shortcuts: [{
          text: '最近一周',
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
            picker.$emit('pick', [start, end]);
          }
        }, {
          text: '最近一个月',
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
            picker.$emit('pick', [start, end]);
          }
        }, {
          text: '最近三个月',
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
            picker.$emit('pick', [start, end]);
          }
        }]
      },
    }
  },
  created() {
    // 填入当前时间
    let endTime = new Date();
    let startTime = new Date(endTime.getTime() - 86400000 * 30)
    this.form.timerange.push(startTime.getFullYear() + '-' + (startTime.getMonth() + 1) + '-' + startTime.getDate() + ' ' +
      startTime.getHours() + ':' + startTime.getMinutes() + ':' + startTime.getSeconds())
    this.form.timerange.push(endTime.getFullYear() + '-' + (endTime.getMonth() + 1) + '-' + endTime.getDate() + ' ' +
      endTime.getHours() + ':' + endTime.getMinutes() + ':' + endTime.getSeconds())
  },
  methods: {
    // “绘制”按钮的方法
    async draw() {
      if (!this.currentStructure?.code) {
        this.$message({
          type: 'warning',
          message: '结构物未选择'
        });
        return;
      }
      this.dataList.splice(0)
      this.isEmpty = false;
      this.$emit('changeDraw', true)
      this.loading = true
      const obj = {
        structureCode: this.currentStructure.code,
        analysisType: this.analysisType,
        startTime: this.form.timerange[0],
        endTime: this.form.timerange[1],
      }
      try {
        const res = await fetchGet(url, obj)
        if (res.code === 200) {
          if (!res.data.result) {
            this.isEmpty = true;
            return;
          }
          for (let i = 0; i < res.data.result.length; i++) {
            let drawTypeObject = res.data.result[i]
            for (let j = 0; j < drawTypeObject.sortedResponseList.length; j++) {
              let chartObject = drawTypeObject.sortedResponseList[j]
              if (chartObject.status === 0) {
                let chartName = drawTypeObject.drawType + j
                this.dataList.push({
                  chartName: chartName
                })
                this.$nextTick(() => {
                  this.setEmptyChart(chartName, chartObject)
                })
              } else {
                let chartName = drawTypeObject.drawType + j
                this.dataList.push({
                  chartName: chartName
                })
                this.$nextTick(() => {
                  let funcName = 'set' + drawTypeObject.drawType + 'Chart';
                  this.callModelFun(funcName, chartName, chartObject)
                })//此时已经获取到数据，调用画图方法;
              }
            }
          }
        } else {
          this.isEmpty = true
        }
      } catch (error) {
        this.$emit('changeDraw', false)
        this.isEmpty = true
        this.loading = false
      }
      finally {
        this.$emit('changeDraw', false)
        this.loading = false
      }
    },
    // 画图中转函数
    callModelFun(funcName, chartName, data) {
      let methods = this.$options.methods;
      methods.isBig = this.isBig;
      methods[funcName](chartName, data);
    },
    //车速概率直方统计图
    setCSChart(chartName, data) {
      let myChart = echarts.getInstanceByDom(
        document.getElementById(chartName)
      );
      if (myChart !== undefined) {
        myChart.dispose();
      }
      echarts.registerTheme('walden', theme.theme)
      myChart = echarts.init(document.getElementById(chartName), "walden");
      let optionName = data.optionName;
      data = data.dataList
      const xLabel = ['<60', '60~70', '70~80', '80~90', '90~100', '100~110', '110~120', '>=120']
      let option = {
        title: {
          left: 'center',
          text: optionName,
          textStyle: {
            color: '#FFFFFF',
            fontSize: this.isBig ? 36 : 18
          }
        },
        toolbox: {
          right: 0,
          top: this.isBig ? 50 : 25,
          itemSize: this.isBig ? 34 : 15,
          feature: {
            saveAsImage: {
              title: '保存',
              type: 'png',
              backgroundColor: 'rgba(4, 37, 72, 0.8)',
            },
          },
        },
        tooltip: {
          textStyle: {
            align: 'left',
            fontSize: this.isBig ? 24 : 14,
          },
          trigger: "axis",
          confine: true
        },
        xAxis: {
          type: 'category',
          nameLocation: "middle",
          nameGap: this.isBig ? 44 : 22,
          data: xLabel,
          axisLabel: {
            color: "#ffffff",
            fontSize: this.isBig ? 24 : 11,
          },
          axisLine: {
            show: true,
            lineStyle: {
              color: 'rgba(110, 112, 121, 0.70)'
            }
          },
          axisTick: {
            alignWithLabel: true, // 刻度线与标签对齐
            inside: true // 刻度线朝上
          }
        },
        yAxis: {
          name: "概率",
          type: 'value',
          nameTextStyle: {
            fontWeight: "bold",
            fontSize: this.isBig ? 24 : 12
          },
          axisLabel: {
            formatter: function (value, index) {
              return value.toFixed(0) + '%';
            },
            textStyle: {
              // 设置文本样式
              color: "rgba(153, 153, 153, 1)",
              fontSize: this.isBig ? 24 : 10,
            },
          },
          splitArea: {
            show: false,
          },
          splitLine: {
            lineStyle: {
              color: 'rgba(110, 112, 121, 0.70)' // 修改分割线颜色
            }
          },
        },
        dataZoom: [
          {
            type: "inside",
          },
        ],
        grid: [
          {
            top: "17%",
            left: "2%",
            right: "10%",
            bottom: "12%",
            containLabel: true,
          },
        ],
        series: [],
        legend: {
          type: 'scroll',
          x: 'center',
          y: 'bottom',
          data: [],
          textStyle: {
            color: "#ffffff",
            fontSize: this.isBig ? 24 : 12,
          },
        },
      };
      //加入多行数据
      for (let i = 0; i < data.length; i++) {
        let barData = data[i].distribution.map(item => item.toFixed(1))
        option.legend.data.push(data[i].label);
        option.series.push({
          type: "bar",
          barGap: 0,
          barCategoryGap: 0,
          name: data[i].label,
          data: barData,
        });
      }
      option && myChart.setOption(option);
      window.onresize = function () {
        myChart.resize();
      };
    },
    //日车流量统计图
    setCLLChart(chartName, data) {
      let myChart = echarts.getInstanceByDom(
        document.getElementById(chartName)
      );
      if (myChart !== undefined) {
        myChart.dispose();
      }
      echarts.registerTheme('walden', theme.theme)
      myChart = echarts.init(document.getElementById(chartName), "walden");
      let optionName = data.optionName;
      data = data.dataList
      // 数据处理 给值增加时间戳
      data.forEach((item, index) => {
        let tmpDotList0 = []
        for (let i = 0; i < item.result.processedData.values.length; i++) {
          tmpDotList0.push([item.result.processedData.times[i], item.result.processedData.values[i]])
        }
        item.result.processedData.dotList = tmpDotList0
      })
      let option = {
        title: {
          left: 'center',
          text: optionName,
          textStyle: {
            color: '#FFFFFF',
            fontSize: this.isBig ? 36 : 18
          }
        },
        toolbox: {
          right: 0,
          top: this.isBig ? 50 : 25,
          itemSize: this.isBig ? 34 : 15,
          feature: {
            saveAsImage: {
              title: '保存',
              type: 'png',
              backgroundColor: 'rgba(4, 37, 72, 0.8)',
            },
          },
        },
        tooltip: {
          textStyle: {
            align: 'left',
            fontSize: this.isBig ? 24 : 14,
          },
          trigger: "axis",
          confine: true
        },
        xAxis: {
          type: 'category',
          name: "时间",
          nameLocation: "middle",
          nameGap: this.isBig ? 44 : 22,
          axisLabel: {
            formatter: function (value, index) {
              return value.substring(0, value.length - 12);
            },
            color: "#ffffff",
            fontSize: this.isBig ? 24 : 11,
          },
          axisLine: {
            show: true,
            lineStyle: {
              color: 'rgba(110, 112, 121, 0.70)'
            }
          },
          axisTick: {
            alignWithLabel: true, // 刻度线与标签对齐
            inside: true // 刻度线朝上
          }
        },
        yAxis: [],
        dataZoom: [
          {
            type: "inside",
          },
        ],
        grid: [
          {
            top: "17%",
            left: "2%",
            right: "10%",
            bottom: "12%",
            containLabel: true,
          },
        ],
        series: [],
        legend: {
          type: 'scroll',
          data: [],
          x: 'center',
          y: 'bottom',
          selected: {},
          textStyle: {
            color: "#ffffff",
            fontSize: this.isBig ? 24 : 12,
          },
        },
      };
      //加入多行数据
      let finalMax = 0;
      let finalMin = 0;
      for (let i = 0; i < data.length; i++) {
        option.legend.data.push(data[i].label);
        option.series.push({
          type: "line",
          name: data[i].label,
          showSymbol: false,
          data: data[i].result.processedData.dotList,
        });

        //计算最大值与最小值
        let minValue = getMin(data[i].result.processedData.values); // 输出最小值
        let maxValue = getMax(data[i].result.processedData.values); // 输出最大值
        // 一些特殊情况处理
        if (maxValue === 0 && minValue === 0) {
          maxValue = 1
          minValue = -1
        } else {
          let delta = maxValue - minValue
          if (delta === 0) {
            delta = 1
            maxValue = maxValue + delta
            minValue = minValue - delta
          }
        }
        // 外层循环最大最小值更新
        if (i === 0) {
          finalMax = maxValue
          finalMin = minValue
        } else {
          if (finalMax < maxValue) {
            finalMax = maxValue
          }
          if (finalMin > minValue) {
            finalMin = minValue
          }
        }
        finalMin = 0
        // 整百整千处理
        if (finalMax !== 0) {
          let exponent = Math.floor(Math.log(finalMax) / Math.LN10);
          let exp10 = Math.pow(10, exponent);
          finalMax = Math.ceil(finalMax / exp10) * exp10
        }
        // 一些预警线
        if (data[i].result.limitInfo) {
          Object.keys(data[i].result.limitInfo).forEach((key) => {
            option.series.push({
              type: "line",
              name: data[i].label + key,
              showSymbol: false,
              animation: false,
              markLine: {
                symbol: 'none',
                data: [
                  {
                    yAxis: data[i].result.limitInfo[key],
                    lineStyle: {
                      type: 'solid'
                    },
                    label: {
                      show: true,
                      position: 'insideEndTop',
                    },
                  }
                ]
              }
            });
            option.legend.data.push(data[i].label + key)
          })
        }
      }
      option.yAxis.push({
        name: '车辆数/辆',
        nameTextStyle: {
          fontWeight: "bold",
          fontSize: this.isBig ? 24 : 12,
        },
        axisLabel: {
          formatter: function (value) {
            return value.toFixed(0); // 2表示小数为2位
          },
          textStyle: {
            // 设置文本样式
            color: "rgba(153, 153, 153, 1)",
            fontSize: this.isBig ? 24 : 10,
          },
        },
        splitArea: {
          show: false,
        },
        splitLine: {
          lineStyle: {
            color: 'rgba(110, 112, 121, 0.70)' // 修改分割线颜色
          }
        },
        splitNumber: 5,
        min: finalMin,
        max: finalMax,
        interval: (finalMax - finalMin) / 5, // 标轴分割间隔
      })
      option && myChart.setOption(option);
      window.onresize = function () {
        myChart.resize();
      };
    },
    //超重车辆分布直方统计图
    setCZChart(chartName, data) {
      let myChart = echarts.getInstanceByDom(
        document.getElementById(chartName)
      );
      if (myChart !== undefined) {
        myChart.dispose();
      }
      echarts.registerTheme('walden', theme.theme)
      myChart = echarts.init(document.getElementById(chartName), "walden");
      let optionName = data.optionName;
      const xLabel = data.sensorLabelList
      data = data.dataList
      let option = {
        title: {
          left: 'center',
          text: optionName,
          textStyle: {
            color: '#FFFFFF',
            fontSize: this.isBig ? 36 : 18
          }
        },
        toolbox: {
          right: 0,
          top: this.isBig ? 50 : 25,
          itemSize: this.isBig ? 34 : 15,
          feature: {
            saveAsImage: {
              title: '保存',
              type: 'png',
              backgroundColor: 'rgba(4, 37, 72, 0.8)',
            },
          },
        },
        tooltip: {
          textStyle: {
            align: 'left',
            fontSize: this.isBig ? 24 : 14,
          },
          trigger: "axis",
          confine: true
        },
        xAxis: {
          type: 'category',
          nameLocation: "middle",
          nameGap: this.isBig ? 44 : 22,
          data: xLabel,
          axisLabel: {
            // 添加或修改 axisLabel 配置项
            textStyle: {
              // 设置文本样式
              color: "#ffffff",
              fontSize: this.isBig ? 24 : 10,
            },
          },
          axisLine: {
            show: true,
            lineStyle: {
              color: 'rgba(110, 112, 121, 0.70)'
            }
          },
          axisTick: {
            alignWithLabel: true, // 刻度线与标签对齐
            inside: true // 刻度线朝上
          }
        },
        yAxis: {
          name: "概率",
          type: 'value',
          nameTextStyle: {
            fontWeight: "bold",
            fontSize: this.isBig ? 24 : 12
          },
          axisLabel: {
            formatter: function (value, index) {
              return value.toFixed(0) + '%';
            },
            textStyle: {
              // 设置文本样式
              color: "rgba(153, 153, 153, 1)",
              fontSize: this.isBig ? 24 : 10,
            },
          },
          splitArea: {
            show: false,
          },
          splitLine: {
            lineStyle: {
              color: 'rgba(110, 112, 121, 0.70)' // 修改分割线颜色
            }
          },
        },
        dataZoom: [
          {
            type: "inside",
          },
        ],
        grid: [
          {
            top: "17%",
            left: "2%",
            right: "10%",
            bottom: "12%",
            containLabel: true,
          },
        ],
        series: [],
        legend: {
          type: 'scroll',
          x: 'center',
          y: 'bottom',
          data: [],
          textStyle: {
            color: "#ffffff",
            fontSize: this.isBig ? 24 : 12,
          },
        },
      };
      //加入多行数据
      for (let i = 0; i < data.length; i++) {
        let barData = data[i].distribution.map(item => item.toFixed(1))
        option.legend.data.push(data[i].label);
        option.series.push({
          type: "bar",
          barGap: 0,
          barCategoryGap: 0,
          name: data[i].label,
          data: barData,
        });
      }
      option && myChart.setOption(option);
      window.onresize = function () {
        myChart.resize();
      };
    },
    //日车流量统计图
    setCLLULTRAChart(chartName, data) {
      let myChart = echarts.getInstanceByDom(
        document.getElementById(chartName)
      );
      if (myChart !== undefined) {
        myChart.dispose();
      }
      echarts.registerTheme('walden', theme.theme)
      myChart = echarts.init(document.getElementById(chartName), "walden");
      let optionName = data.optionName;
      data = data.dataList
      // 数据处理 给值增加时间戳
      data.forEach((item, index) => {
        let tmpDotList0 = []
        for (let i = 0; i < item.result.processedData.values.length; i++) {
          tmpDotList0.push([item.result.processedData.times[i], item.result.processedData.values[i]])
        }
        item.result.processedData.dotList = tmpDotList0
      })
      data.sort(function (a, b) {
        return b.result.processedData.dotList.length - a.result.processedData.dotList.length
      })
      let option = {
        title: {
          left: 'center',
          text: optionName,
          textStyle: {
            color: '#FFFFFF',
            fontSize: this.isBig ? 36 : 18
          }
        },
        toolbox: {
          right: 0,
          top: this.isBig ? 50 : 25,
          itemSize: this.isBig ? 34 : 15,
          feature: {
            saveAsImage: {
              title: '保存',
              type: 'png',
              backgroundColor: 'rgba(4, 37, 72, 0.8)',
            },
          },
        },
        tooltip: {
          textStyle: {
            align: 'left',
            fontSize: this.isBig ? 24 : 14,
          },
          trigger: "axis",
          confine: true
        },
        xAxis: {
          type: 'category',
          name: "时间",
          nameLocation: "middle",
          nameGap: this.isBig ? 44 : 22,
          axisLabel: {
            formatter: function (value, index) {
              return value.substring(0, value.length - 12);
            },
            showMinLabel: true,
            showMaxLabel: true,
            color: "#ffffff",
            fontSize: this.isBig ? 24 : 10,
          },
          axisLine: {
            show: true,
            lineStyle: {
              color: 'rgba(110, 112, 121, 0.70)'
            }
          },
          axisTick: {
            alignWithLabel: true, // 刻度线与标签对齐
            inside: true // 刻度线朝上
          }
        },
        yAxis: [],
        dataZoom: [
          {
            type: "inside",
          },
        ],
        grid: [
          {
            top: "17%",
            left: "2%",
            right: "10%",
            bottom: "12%",
            containLabel: true,
          },
        ],
        series: [],
        legend: {
          type: 'scroll',
          data: [],
          x: 'center',
          y: 'bottom',
          selected: {},
          textStyle: {
            color: "#ffffff",
            fontSize: this.isBig ? 24 : 12,
          },
        },
      };
      //加入多行数据
      let finalMax = 0;
      let finalMin = 0;
      for (let i = 0; i < data.length; i++) {
        option.legend.data.push(data[i].label);
        option.series.push({
          type: "line",
          name: data[i].label,
          showSymbol: false,
          data: data[i].result.processedData.dotList,
        });

        //计算最大值与最小值
        let minValue = getMin(data[i].result.processedData.values); // 输出最小值
        let maxValue = getMax(data[i].result.processedData.values); // 输出最大值
        // 一些特殊情况处理
        if (maxValue === 0 && minValue === 0) {
          maxValue = 1
          minValue = -1
        } else {
          let delta = maxValue - minValue
          if (delta === 0) {
            delta = 1
            maxValue = maxValue + delta
            minValue = minValue - delta
          }
        }
        // 外层循环最大最小值更新
        if (i === 0) {
          finalMax = maxValue
          finalMin = minValue
        } else {
          if (finalMax < maxValue) {
            finalMax = maxValue
          }
          if (finalMin > minValue) {
            finalMin = minValue
          }
        }
        finalMin = 0
        // 整百整千处理
        if (finalMax !== 0) {
          let exponent = Math.floor(Math.log(finalMax) / Math.LN10);
          let exp10 = Math.pow(10, exponent);
          finalMax = Math.ceil(finalMax / exp10) * exp10
        }
        // 一些预警线
        if (data[i].result.limitInfo) {
          Object.keys(data[i].result.limitInfo).forEach((key) => {
            option.series.push({
              type: "line",
              name: data[i].label + key,
              showSymbol: false,
              animation: false,
              markLine: {
                symbol: 'none',
                data: [
                  {
                    yAxis: data[i].result.limitInfo[key],
                    lineStyle: {
                      type: 'solid'
                    },
                    label: {
                      show: true,
                      position: 'insideEndTop',
                    },
                  }
                ]
              }
            });
            option.legend.data.push(data[i].label + key)
          })
        }
      }
      option.yAxis.push({
        name: '车辆数/辆',
        nameTextStyle: {
          fontWeight: "bold",
          fontSize: this.isBig ? 24 : 12,
        },
        axisLabel: {
          formatter: function (value) {
            return value.toFixed(0); // 2表示小数为2位
          },
          textStyle: {
            // 设置文本样式
            color: "rgba(153, 153, 153, 1)",
            fontSize: this.isBig ? 24 : 10,
          },
        },
        splitArea: {
          show: false,
        },
        splitLine: {
          lineStyle: {
            color: 'rgba(110, 112, 121, 0.70)' // 修改分割线颜色
          }
        },
        splitNumber: 5,
        min: finalMin,
        max: finalMax,
        interval: (finalMax - finalMin) / 5, // 标轴分割间隔
      })
      option && myChart.setOption(option);
      window.onresize = function () {
        myChart.resize();
      };
    },
    //绘制空表 提示信息
    setEmptyChart(chartName, obj) {
      let myChart = echarts.getInstanceByDom(
        document.getElementById(chartName)
      );
      if (myChart !== undefined) {
        myChart.dispose();
      }
      myChart = echarts.init(document.getElementById(chartName));
      let option = {
        title: [
          {
            text: obj.optionName,
            left: 'center',
            top: 0,
            textStyle: {
              fontSize: this.isBig ? 36 : 18,
              color: '#FFFFFF'
            }
          },
          {
            text: obj.errorMsg || '没有数据',
            left: 'center',
            top: 'center',
            textStyle: {
              fontSize: this.isBig ? 28 : 14
            }
          },
        ]
      };
      option && myChart.setOption(option);
      //自适应大小
      window.onresize = function () {
        myChart.resize();
      };
    },
    //时间范围下拉框的change事件
    statusDateChange(e) {
      this.form.timerange = e;
    },
    //清除当前绘图
    clearChart() {
      this.dataList.forEach((item, index) => {
        let myChart = echarts.getInstanceByDom(
          document.getElementById(item.chartName)
        );
        if (myChart !== undefined) {
          myChart.dispose();
        }
      })
      this.dataList.splice(0)
    },
    //坐标轴名称过长时添加换行
    splitBySpace(str) {
      let n = str.length
      if (n > 5) {
        for (let i = 0; i < Math.floor(n / 5); i++) {
          str = str.substring(0, (i + 1) * 5) + '\n' + str.substring((i + 1) * 5)
        }
      }
      return str
    }
  },
  computed: {},
  watch: {},
}
</script>

<style lang="scss" scoped>
@import "@/assets/styles/utils.scss";

// 日期选择器样式调整
::v-deep {

  // 输入框
  .el-range-editor.el-input__inner {
    width: vwpx(800px) !important;
    height: vwpx(70px);
    padding: vwpx(6px) vwpx(20px);
    background-color: rgba(0, 25, 64, 0.5);
    border: 1px solid #0166fe;
    color: #ffffff;
  }

  // 输入框内文本的样式
  .el-range-input {
    font-size: vwpx(30px);
    background: transparent !important;
    color: #fff !important;
  }

  // 左右的图标
  .el-range__icon {
    font-size: vwpx(26px);
    height: 100%;
    width: vwpx(40px);
    margin: 0;
  }

  .el-icon-circle-close:before {
    font-size: vwpx(26px);
    line-height: vwpx(61px);
    margin-left: vwpx(10px);
  }

  // 文字颜色
  .el-range-input {
    color: #000;
  }
}

::v-deep {
  .el-button {
    width: vwpx(200px);
    height: vwpx(68px);
    border-radius: vwpx(12px);
    font-size: vwpx(32px);
    padding: vwpx(14px) vwpx(20px);
  }
}
</style>