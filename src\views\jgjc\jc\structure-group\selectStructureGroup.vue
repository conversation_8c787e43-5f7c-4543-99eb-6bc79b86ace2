<template>
  <el-dialog title="选择结构物分组" :visible.sync="visible" width="800px" top="5vh" append-to-body>
    <el-row>
      <el-tree node-key="id" :data="list" @current-change="tree_current_change" :expand-on-click-node="false" highlight-current default-expand-all></el-tree>
    </el-row>
    <div slot="footer" class="dialog-footer">
      <el-button type="primary" @click="handleSelect">确 定</el-button>
      <el-button @click="visible = false">取 消</el-button>
    </div>
  </el-dialog>
</template>
<script>
import { getStructureGroupFindTree } from "@/api/jgjc/jc/structureGroup";
export default {
  data() {
    return {
      // 遮罩层
      visible: false,
      selectData:null,
      list: [],
    };
  },
  methods: {
    // 显示弹框
    show() {
      this.getList();
      this.visible = true;
    },
    tree_current_change(data,node){
      this.selectData = data
    },
    // 查询表数据
    getList() {
      getStructureGroupFindTree().then(res => {
        this.list = res.data;
      });
    },
    /** 提交选择隧道操作 */
    handleSelect() {
      if(this.selectData!=null){
        this.$emit('select',this.selectData)
        this.visible = false;
        return
      }
      this.$modal.msgWarning("请选择一条结构物分组记录");
    }
  }
};
</script>
