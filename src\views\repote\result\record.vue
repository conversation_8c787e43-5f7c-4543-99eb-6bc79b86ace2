<template>
  <el-dialog
    :visible.sync="open"
    :title="title"
    append-to-body
    width="80%"
    destroy-on-close
    :close-on-click-modal="false"
    v-if="open"
  >
    <el-form
      :model="queryParams"
      ref="queryForm"
      size="small"
      :inline="true"
      label-width="68px"
    >
      <el-form-item label="" prop="userNameLike">
        <el-input
          v-model="queryParams.userNameLike"
          placeholder="请输入填报人名字"
          clearable
          style="width: 240px"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>

      <el-form-item label="" prop="status">
        <el-select
          v-model="queryParams.status"
          placeholder="请选择填报状态"
          clearable
          style="width: 240px"
        >
          <el-option label="待填写" :value="1" />
          <el-option label="待提交" :value="2" />
          <el-option label="已提交" :value="3" />
        </el-select>
      </el-form-item>

      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
          >搜索</el-button
        >
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
          >重置</el-button
        >
      </el-form-item>
    </el-form>
    <!--默认折叠-->

    <!--默认折叠 ,此处仅作为示例-->
    <el-form
      :model="queryParams"
      size="small"
      :inline="true"
      v-show="showSearch"
      label-width="68px"
    >
    </el-form>
    <!--筛选区结束-->

    <!--数据表格开始-->
    <div class="tableDiv">
      <el-table
        v-adjust-table
        size="mini"
        :height="showSearch ? 'calc(100vh - 320px)' : 'calc(100vh - 260px)'"
        style="width: 100%"
        v-loading="loading"
        border
        :data="repoteRecordList"
      >
        <el-table-column label="序号" type="index" width="50" />
        <el-table-column
          label="填报人"
          width="70"
          align="center"
          prop="userName"
        />
        <el-table-column
          label="填报单位"
          width="400"
          align="center"
          :show-overflow-tooltip="true"
        >
          <template slot-scope="scope">
            <span>
              <template v-if="deptFullData[scope.row.userId]">
                {{ deptFullData[scope.row.userId] }}
              </template>
              <template v-else> Loading... </template>
            </span>
          </template>
        </el-table-column>
        <el-table-column
          label="提交时间"
          width="220"
          align="center"
          prop="createTime"
        >
          <template slot-scope="scope">
            <span v-if="scope.row.status === 3">
              {{ scope.row.updateTime | formatDate('YYYY年MM月DD日 hh:mm:ss') }}
              <el-button
                size="mini"
                type="text"
                icon="el-icon-s-help"
                @click.stop="handleBack(scope.row)"
                >回退报表
              </el-button>
            </span>
          </template>
        </el-table-column>
        <el-table-column
          label="备注"
          align="center"
          prop="remake"
          :show-overflow-tooltip="true"
        />
        <el-table-column label="填报状态" align="center" prop="status">
          <template v-slot="scope">
            <el-tag
              :type="
                scope.row.status === 1
                  ? 'success'
                  : scope.row.status === 2
                  ? 'info'
                  : ''
              "
            >
              {{
                scope.row.status === 1
                  ? '待填写'
                  : scope.row.status === 2
                  ? '待提交'
                  : '已提交'
              }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="填报文件地址" align="center" width="200">
          <template slot-scope="scope">
            <FileUpload
              for-view
              :value="scope.row.url"
              :key="scope.row.id"
              :download-name="`${title}_${scope.row.userName}_${scope.row.createTime}`"
            />
          </template>
        </el-table-column>
      </el-table>

      <pagination
        v-show="total > 0"
        :total="total"
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
        @pagination="getList"
      />
    </div>
    <!--数据表格结束-->
  </el-dialog>
</template>

<script>
import { EventBus } from '@/utils/eventBus';
import {
  listRepoteRecord,
  getRepoteRecord,
  delRepoteRecord,
  addRepoteRecord,
  updateRepoteRecord,
} from '@/api/repote/repoteRecord';
import { getUserProfile, getUser } from '@/api/system/user';
import { getToken } from '@/utils/auth';
import Treeselect from '@riophae/vue-treeselect';
import '@riophae/vue-treeselect/dist/vue-treeselect.css';

export default {
  name: 'RepoteRecord',
  components: {},
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: false,
      dictType: [],
      // 总条数
      total: 0,
      // 填报记录表格数据
      repoteRecordList: null,
      // 弹出层标题
      title: '',
      // 部门树选项
      deptOptions: undefined,
      // 是否显示弹出层
      open: false,
      // 部门名称
      deptData: {},
      // 部门完整名称
      deptFullData: {},

      // 表单参数
      form: {},
      defaultProps: {
        children: 'children',
        label: 'label',
      },
      // 用户导入参数
      upload: {
        // 是否显示弹出层（用户导入）
        open: false,
        // 弹出层标题（用户导入）
        title: '',
        // 是否禁用上传
        isUploading: false,
        // 是否更新已经存在的用户数据
        updateSupport: 0,
        // 设置上传的请求头部
        headers: { Authorization: 'Bearer ' + getToken() },
        // 上传的地址
        url: process.env.VUE_APP_BASE_API + '/system/user/importData',
      },
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 50,
        formId: null,
        userId: null,
        missionName: null,
        formName: null,
        userName: null,
        status: null,
      },
      // 列信息
      columns: [
        { key: 0, label: `填报表格ID`, visible: true },
        { key: 1, label: `填报人ID`, visible: true },
        { key: 2, label: `填报人名字`, visible: true },
        { key: 3, label: `填报后表格地址`, visible: true },
        { key: 4, label: `备注`, visible: true },
        { key: 5, label: `填报状态`, visible: true },
      ],
      // 表单校验
      rules: {
        userName: [{ require: true, message: '请输入填报人', trigger: 'blur' }],
        url: [{ require: true, message: '请上传表格', trigger: 'blur' }],
      },
    };
  },
  created() {
    EventBus.$on('openRecordDialog', (data) => {
      this.queryParams.missionName = data.missionName;
      this.queryParams.formName = data.name;
      this.title = data.name;
      this.open = true;
      this.getList();
    });
  },
  methods: {
    //Promise
    async fetchDeptData(repoteRecordList) {
      const userIds = repoteRecordList.map((item) => item.userId);
      const promises = userIds.map((userId) => getUser(userId));
      const results = await Promise.all(promises);
      results.forEach((data, index) => {
        this.$set(this.deptData, userIds[index], data.data.dept.deptName);
        this.$set(this.deptFullData, userIds[index], data.deptIds);
      });
    },
    /** 查询用户列表 */
    getList() {
      this.loading = true;
      listRepoteRecord(this.queryParams).then((response) => {
        this.repoteRecordList = response.rows;
        this.total = response.total;
        this.loading = false;
        this.fetchDeptData(response.rows);
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      // this.reset();
    },
    // 表单重置
    reset() {
      this.resetForm('form');
      getUserProfile().then((res) => {
        let user = res.data;
        this.form = {
          formId: null,
          userId: user.userId,
          userName: user.nickName,
          url: null,
          annexUrls: null,
          remake: null,
          missionName: null,
          formName: null,
          status: 1,
        };
      });
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dateRange = [];
      this.resetForm('queryForm');
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id);
      this.single = selection.length != 1;
      this.multiple = !selection.length;
    },
    /** 回退按钮 */
    async handleBack(row) {
      try {
        // 打开遮罩层
        this.$modal.loading('正在回退，请稍后...');

        const id = row.id || this.ids;
        const response = await getRepoteRecord(id);

        this.form = response.data;
        this.form.status = 4;

        if (this.form.id != null) {
          await updateRepoteRecord(this.form);
          this.$modal.msgSuccess('回退成功');
          this.getList();
        }
      } catch (error) {
        // 处理错误
        this.$modal.msgError('请求失败');
      } finally {
        // 关闭遮罩层
        this.$modal.closeLoading();
      }
    },

    /** 删除按钮操作 */
    handleDelete(row) {
      const id = row.id || this.ids;
      this.$modal
        .confirm('是否确认删除填报记录编号为"' + id + '"的数据项？')
        .then(function () {
          return delRepoteRecord(id);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess('删除成功');
        })
        .catch(() => {});
    },

    /** 导出按钮操作 */
    handleExport() {
      this.download(
        'repote/repoteRecord/export',
        {
          ...this.queryParams,
        },
        `repoteRecord_${new Date().getTime()}.xlsx`
      );
    },
    /** 导入按钮操作 */
    handleImport() {
      this.upload.title = '用户导入';
      this.upload.open = true;
    },
    /** 下载模板操作 */
    importTemplate() {
      this.download('system/user/importTemplate', {}, `user_template.xlsx`);
    },
    // 文件上传中处理
    handleFileUploadProgress(event, file, fileList) {
      this.upload.isUploading = true;
    },
    // 文件上传成功处理
    handleFileSuccess(response, file, fileList) {
      this.upload.open = false;
      this.upload.isUploading = false;
      this.$refs.upload.clearFiles();
      this.$alert(
        "<div style='overflow: auto;overflow-x: hidden;max-height: 70vh;padding: 10px 20px 0;'>" +
          response.msg +
          '</div>',
        '导入结果',
        { dangerouslyUseHTMLString: true }
      );
      this.getList();
    },
    // 提交上传文件
    submitFileForm() {
      this.$refs.upload.submit();
    },
  },
};
</script>
<style scoped lang="scss">
.hasTagsView .app-main[data-v-078753dd] {
  background: #f5f7fa;
}

.tableDiv {
  background-color: white;
  padding-bottom: 10px;
}

::v-deep .el-list-enter-active,
::v-deep .el-list-leave-active {
  transition: all 0s;
}

::v-deep .el-list-enter,
.el-list-leave-active {
  opacity: 0;
  transform: translateY(0);
}

.tableDiv {
  ::v-deep .el-upload-list__item {
    width: 42px;
    height: 42px;
    margin: 0px 5px -6px 0 !important;
  }

  ::v-deep .el-upload--picture-card {
    width: 42px;
    height: 42px;
    margin: 4px 0;
  }

  ::v-deep .el-icon-plus {
    font-size: 1rem;
  }

  ::v-deep .el-upload-list__item-actions {
    font-size: 1rem;
  }

  ::v-deep .el-upload-list__item-actions span + span {
    margin-left: 5px !important;
  }
}
</style>
