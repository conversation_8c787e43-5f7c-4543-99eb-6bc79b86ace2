<template>
  <!-- 授权用户 -->
  <el-dialog title="选择隧道" :visible.sync="visible" width="800px" top="5vh" append-to-body>
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true">
      <el-form-item label="隧道名称" prop="tunnelName">
        <el-input
          v-model="queryParams.tunnelName"
          placeholder="请输入隧道名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>
    <el-row>
      <el-table @row-click="clickRow" @select="selectChange" ref="table" :data="tunnelList"
                @select-all="handleSelectionChange" height="260px">
        <el-table-column type="selection" width="55"></el-table-column>
        <el-table-column label="隧道编码" align="center" prop="tunnelCode"/>
        <el-table-column label="隧道名称" align="center" prop="tunnelName"/>
      </el-table>
      <pagination
        v-show="total>0"
        :total="total"
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
        @pagination="getList"
      />
    </el-row>
    <div slot="footer" class="dialog-footer">
      <el-button type="primary" @click="handleSelectUser">确 定</el-button>
      <el-button @click="visible = false">取 消</el-button>
    </div>
  </el-dialog>
</template>

<script>
import {listStatic} from "@/api/baseData/tunnel/baseInfo";
import {getDefaultSelected, addAllDeptTunnel} from "@/api/system/deptTunnel";
export default {
  dicts: ['sys_normal_disable'],
  props: {
    deptData: {
      type: Object,
      require: true,
    },
  },
  data() {
    return {
      // 遮罩层
      visible: false,
      // 选中数组值
      tunnelIds: [],
      // 总条数
      total: 0,
      // 隧道数据
      tunnelList: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        maintenanceSectionId: undefined,
        tunnelName: undefined,
      },
      //提交参数
      params: {}
    };
  },
  methods: {
    // 显示弹框
    show() {
      this.queryParams.deptId = this.deptData.deptId;
      this.queryParams.maintenanceSectionId = this.deptData.maintenanceSectionId;
      this.getDefault();
      this.getList();
      this.visible = true;
    },
    clickRow(row) {
      this.$refs.table.toggleRowSelection(row);
      this.setChecked(row);
    },
    selectChange(arr, row) {
      //退选和选中处理
      this.setChecked(row);
    },
    //退选和选中处理
    setChecked(row){
      if (this.tunnelIds.includes(row.assetId)) {
        this.tunnelIds = this.tunnelIds.filter(i => i !== row.assetId);
      } else {
        this.tunnelIds.push(row.assetId)
      }
    },

    // 多选框选中数据
    handleSelectionChange(selection) {
      if (selection.length > 0) {
        selection.forEach(item => {
          // this.setChecked(item);
          if (!this.tunnelIds || !this.tunnelIds.includes(item.assetId)) {
            this.tunnelIds.push(item.assetId)
          }
        })
      } else {
        this.tunnelList.forEach(item => {
          this.tunnelIds = this.tunnelIds.filter(i => i !== item.assetId);
        })
      }
    },
    // 查询默认选中数据
    getDefault() {
      // this.queryParams.maintenanceSectionId = null;
      getDefaultSelected(this.queryParams).then(data => {
        if (data) {
          this.tunnelIds = data;
        }
      });
    },
    // 查询表数据
    getList() {
      listStatic(this.queryParams).then(res => {
        this.tunnelList = res.rows;
        this.total = res.total;
        this.setSelected(this.tunnelList);
      });
    },
    //设置默认选中
    setSelected(rows) {
      this.$nextTick(()=> {//渲染后执行
        rows.forEach(row => {
          if (this.tunnelIds.includes(row.assetId)) {
            this.$refs.table.toggleRowSelection(row,true);
          }
        });
      })
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    /** 提交选择隧道操作 */
    handleSelectUser() {
      const tunnelIds = this.tunnelIds.join(",");
      // if (tunnelIds == "") {
      //   this.$modal.msgError("请选择要分配的隧道");
      //   return;
      // }
      const deptId = this.deptData.deptId;
      this.params.deptId = deptId;
      this.params.tunnelIds = tunnelIds;
      this.params.maintenanceSectionId = this.deptData.maintenanceSectionId;
      addAllDeptTunnel(this.params).then(response => {
        this.$modal.msgSuccess("操作成功");
        this.$parent.handleDeptAsset(deptId);
        this.getList();
        this.visible=false;
      });
    }
  }
};
</script>
