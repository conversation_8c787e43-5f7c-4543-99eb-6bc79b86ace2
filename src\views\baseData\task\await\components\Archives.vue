<template>
  <div v-loading="loading" class="culvert-info-edit-structure">
    <el-form ref="ruleFormEl" :model="dataForm" :rules="rules" label-width="100px">
      <el-row :gutter="20">
        <el-col :span="8">
          <el-form-item label="设计图纸" prop="designDrawingComplete">
            <!-- <DictSelect v-model="dataForm.designDrawingComplete" placeholder="请选择设计图纸" type="base_archives_type" /> -->
            <el-select v-model="ruleForm.designDrawingComplete" clearable filterable placeholder="请选择设计图纸">
              <el-option v-for="dict in dict.type.base_archives_type"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="竣工文件" prop="completionDocumentComplete">
            <!-- <DictSelect v-model="dataForm.completionDocumentComplete" placeholder="请选择竣工文件" type="base_archives_type" /> -->
            <el-select v-model="ruleForm.completionDocumentComplete" clearable filterable placeholder="请选择竣工文件">
              <el-option v-for="dict in dict.type.base_archives_type"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="特别检查报告" prop="particularlyReportComplete">
            <!-- <DictSelect v-model="dataForm.particularlyReportComplete" placeholder="请选择特别检查报告" type="base_archives_type" /> -->
            <el-select v-model="ruleForm.particularlyReportComplete" clearable filterable placeholder="请选择特别检查报告">
              <el-option v-for="dict in dict.type.base_archives_type"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="档案号" prop="fileNumberComplete">
            <!-- <DictSelect v-model="dataForm.fileNumberComplete" placeholder="请选择档案号" type="base_archives_type" /> -->
            <el-select v-model="ruleForm.fileNumberComplete" clearable filterable placeholder="请选择档案号">
              <el-option v-for="dict in dict.type.base_archives_type"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value">
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="设计文件" prop="designDocumentComplete">
            <!-- <DictSelect v-model="dataForm.designDocumentComplete" placeholder="请选择设计文件" type="base_archives_type" /> -->
            <el-select v-model="ruleForm.designDocumentComplete" clearable filterable placeholder="请选择设计文件">
              <el-option v-for="dict in dict.type.base_archives_type"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="验收文件" prop="acceptanceDocumentComplete">
            <!-- <DictSelect v-model="dataForm.acceptanceDocumentComplete" placeholder="" type="base_archives_type" /> -->
            <el-select v-model="ruleForm.acceptanceDocumentComplete" clearable filterable placeholder="请选择验收文件">
              <el-option v-for="dict in dict.type.base_archives_type"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="专项检查报告" prop="specialInspectionReportComplete">
            <!-- <DictSelect v-model="dataForm.specialInspectionReportComplete" placeholder="请选择专项检查报告" type="base_archives_type" /> -->
            <el-select v-model="ruleForm.specialInspectionReportComplete" clearable filterable placeholder="请选择专项检查报告">
              <el-option v-for="dict in dict.type.base_archives_type"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="存档案" prop="saveArchivesComplete">
            <!-- <DictSelect v-model="dataForm.saveArchivesComplete" placeholder="请选择存档案" type="base_archives_type" /> -->
            <el-select v-model="ruleForm.saveArchivesComplete" clearable filterable placeholder="请选择存档案">
              <el-option v-for="dict in dict.type.base_archives_type"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value">
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="施工文件" prop="constructionDocumentsComplete">
            <!-- <DictSelect v-model="dataForm.constructionDocumentsComplete" placeholder="请选择施工文件" type="base_archives_type" /> -->
            <el-select v-model="ruleForm.constructionDocumentsComplete" clearable filterable placeholder="请选择施工文件">
              <el-option v-for="dict in dict.type.base_archives_type"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="定期检查报告" prop="periodicInspectionReportComplete">
            <!-- <DictSelect v-model="dataForm.periodicInspectionReportComplete" placeholder="请选择定期检查报告" type="base_archives_type" /> -->
            <el-select v-model="ruleForm.periodicInspectionReportComplete" clearable filterable placeholder="请选择定期检查报告">
              <el-option v-for="dict in dict.type.base_archives_type"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="历次维修资料" prop="previousMaintenanceComplete">
            <!-- <DictSelect v-model="dataForm.previousMaintenanceComplete" placeholder="请选择历次维修资料" type="base_archives_type" /> -->
            <el-select v-model="ruleForm.previousMaintenanceComplete" clearable filterable placeholder="请选择历次维修资料">
              <el-option v-for="dict in dict.type.base_archives_type"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="建档时间" prop="filingTimeComplete">
            <!-- <DictSelect v-model="dataForm.filingTimeComplete" placeholder="请选择建档时间" type="base_archives_type" /> -->
            <el-select v-model="ruleForm.filingTimeComplete" clearable filterable placeholder="请选择建档时间">
              <el-option v-for="dict in dict.type.base_archives_type"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value">
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </div>
</template>

<script>
export default {
  name: 'CulvertInfoEditStructure',
  dicts: ['base_archives_type', 'sys_route_type','sys_culvert_type','bridge_design_load'],
  components: {},
  props: {
    ruleForm: {
      type: Object,
      default: () => {}
    },
    rules: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      loading: false,
      dataForm: {}
    }
  },
  computed: {},
  watch: {},
  created() {
    // 使用浅拷贝，方便收集数据
    this.dataForm = this.ruleForm
  },
  mounted() {},
  methods: {
    validateCheck() {
      let flag = false
      this.$refs['ruleFormEl'].validate((valid) => {
        flag = valid
      })
      return flag
    }
  }
}
</script>

<style lang="scss" scoped>
.culvert-info-edit-structure {}
</style>
