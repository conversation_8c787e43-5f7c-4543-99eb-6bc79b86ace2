<template>
  <div class="stake-mark">
    <el-form :model="form" ref="form" :rules="rules" :label-width="isBig ? '160px' : '80px'" :inline="false">
      <el-form-item label="管理处">
        <el-select v-model="form.managementMaintenanceIds" @change="handleDeptChange" @clear="handleDeptClear"
          placeholder="请选择" clearable filterable style="width: 100%;" multiple :popper-append-to-body="true">
          <el-option v-for="item in deptOptions" :key="item.id" :label="item.label" :value="item.id" />
        </el-select>
      </el-form-item>
      <el-form-item label="养护路段">
        <el-select v-model="form.maintenanceSectionIds" placeholder="请选择" clearable filterable style="width: 100%;"
          :popper-append-to-body="true" multiple>
          <el-option v-for="item in routeOptions" :key="item.maintenanceSectionId" :label="item.maintenanceSectionName"
            :value="item.maintenanceSectionId" />
        </el-select>
      </el-form-item>
      <el-form-item label="总重(吨)">
        <!-- <el-input v-model="totalWeight" placeholder="小于等于" clearable type="number" min="0" /> -->
        <el-input-number v-model="totalWeight" controls-position="right" :min="0" style="width:100%" />
      </el-form-item>
      <el-form-item label="轴重(吨)">
        <!-- <el-input v-model="axleLoad" placeholder="小于等于" clearable type="number" :min="0" /> -->
        <el-input-number v-model="axleLoad" controls-position="right" :min="0" style="width:100%" />
      </el-form-item>
      <el-form-item label="轴距(米)">
        <!-- <el-input v-model="wheelbase" placeholder="大于等于" clearable type="number" :min="0" /> -->
        <el-input-number v-model="wheelbase" controls-position="right" :min="0" style="width:100%" />
      </el-form-item>
    </el-form>
    <div class="btn-list">
      <el-button type="primary" @click="onView" size="mini">查看</el-button>
      <el-button type="primary" @click="onQuit" size="mini" class="hollow-out">退出</el-button>
    </div>
  </div>
</template>

<script>
// api
import { deptTreeSelect } from '@/api/tmpl'
import { getMaintenanceSectionListAll } from '@/api/baseData/common/routeLine'
import { getListPage } from '@/api/baseData/facility/baseInfo/index'
import { listStatic } from "@/api/baseData/bridge/baseInfo/index";
import { listStatic as listStaticTunnel } from "@/api/baseData/tunnel/baseInfo/index";

import { mapState } from 'vuex';
import { addWidthFeature, removeLayer, addCluster } from '@/views/map/components/common/mapFun';

import TollStationIcon from '@/assets/map/toll-station-icon.png'
import TollStationWIcon from '@/assets/map/toll-station-icon-w.png'
import bridgeWIcon from '@/assets/map/bridge-w.png'
import tunnelWIcon from '@/assets/map/tunnel-w.png'
import { isBigScreen } from '../common/util';

export default {
  data() {
    return {
      isBig: isBigScreen(),
      form: {
        managementMaintenanceIds: [],
        maintenanceSectionId: '',
        pageNum: 1,
        pageSize: 10000,
        mainTypeId: 8,
        typeId: 26,
        paramsDTO: {
          precisionParams: {
            customParams: [],
          }
        },
      },
      totalWeight: null, // 总重
      wheelbase: null, // 轴距
      axleLoad: null, //轴重
      rules: {},
      deptOptions: [],
      routeOptions: [],
    }
  },
  created() {
    this.getDept();
    this.getRoutes();
    window.$Bus.$on('treeClick',(val)=>{
      if(val && val.deptIds) {
        this.form.managementMaintenanceIds = val.deptIds;
        this.getRoutes(val.deptIds);
      }
    });
  },
  computed: {
    ...mapState({
      deptId: (state) => state.map.deptId,
    })
  },
  watch: {
    deptId(newVal, oldVal) {
      this.onView();
    },
  },
  beforeDestroy() {
    window.$Bus.$off('treeClick');
  },
  methods: {
    // 获取管理处
    getDept() {
      deptTreeSelect({ types: 201 }).then(res => {
        if (res.code === 200) {
          this.deptOptions = res.data || [];
        }
      })
    },
    // 获取养护路段数据
    getRoutes(e) {
      getMaintenanceSectionListAll({ departmentIdList: e }).then(res => {
        if (res.code == 200) {
          this.routeOptions = res.data || []
        }
      })
    },
    // 管养出监听选择变化
    handleDeptChange(e) {
      if (e) {
        this.getRoutes(e);
      }
    },
    // 清空管养出
    handleDeptClear(e) {
      this.form.maintenanceSectionId = '';
      this.getRoutes();
    },
    // 查看
    async onView() {
      // 移除上次
      removeLayer(window.mapLayer, 'tollStationLayer', 'name')
      removeLayer(window.mapLayer, 'tollStationLayer')
      let totalWeight = this.totalWeight - 0
      let wheelbase = this.wheelbase - 0
      let axleLoad = this.axleLoad - 0
      // 组装请求条件
      let params = {
        ...this.form,
        paramsDTO: {
          precisionParams: {
            customParams: [
              {
                "col": "base.total_weight",
                "compareSymbol": "&gt;=",
                "colValue": totalWeight ? totalWeight : null,
              },
              {
                "col": "base.wheelbase",
                "compareSymbol": "&gt;=",
                "colValue": wheelbase ? wheelbase : null,
              },
              {
                "col": "base.axle_load",
                "compareSymbol": "&gt;=",
                "colValue": axleLoad ? axleLoad : null,
              }
            ],
          }
        },
      }
      let ids = this.form.managementMaintenanceIds || [];
      this.form.managementMaintenanceIds = ids && ids.length ? ids : this.deptId ? [this.deptId] : [];
      // 先查询所有点
      let allData = await this.onSearchAll();
      let queryData = [];
      if (totalWeight || wheelbase || axleLoad) {
        queryData = await this.onSearch(params);
      }
      // 将查询结果从allData中移除
      if (queryData.length) {
        queryData.forEach(item => {
          let index = allData.findIndex(el => el.id === item.id);
          if (index !== -1) {
            allData.splice(index, 1);
          }
        });
      }

      // 将点聚合
      // 先移除
      removeLayer(window.mapLayer, 'tollStationLayer');
      removeLayer(window.mapLayer, 'tollStationLayer', 'name');
      // 再添加
      // 加载查询结果
      if (queryData.length) {
        queryData.forEach(row => {
          row.typeName = row.name || row.typeName || "收费站";
          row.listQueryConfig = {
            detlCondf: {
              path: './baseData/ancillary/baseInfo/commonForm.vue',
              type: 1,
            },
          }
          row.mainTypeId = 8
          row.items = {}
        });
        if (queryData.length > 20) {
          addCluster(queryData, TollStationIcon, (e) => {
            // console.log('回调函数', e);
          }, 'name', 'tollStationLayer');
        } else {
          addWidthFeature(window.mapLayer, queryData, 'tollStationLayer', TollStationIcon);
        }

        addCluster(allData, TollStationWIcon, (e) => {
          // console.log('回调函数', e);
        }, 'name', 'tollStationLayer');
      } else {
        addCluster(allData, TollStationWIcon, (e) => {
          // console.log('回调函数', e);
        }, 'name', 'tollStationLayer');
      }
      
      // 如果选中了 养护路段
      if (this.form.maintenanceSectionIds.length) {
        let bridgeData = await this.onSearchBridge();
        bridgeData = bridgeData.filter(v => v.shape).map(item => {
          item.typeName = item.bridgeName || item.assetName || item.typeName || "桥梁";
          item.typeStr = 'bridge';
          item.listQueryConfig = {
            detlCondf: {
              path: './baseData/bridge/baseInfo/archives/index.vue',
              type: 2, // 1：组件 2：路由 3：接口
            },
          }
          return item;
        });
        if (bridgeData.length > 20) {
          addCluster(bridgeData, bridgeWIcon, (e) => { }, 'name', 'tollStationLayer');
        } else {
          addWidthFeature(window.mapLayer, bridgeData, 'tollStationLayer', bridgeWIcon);
        }
        let tunnelData = await this.onSearchTunnel();
        tunnelData = tunnelData.filter(v => v.shape).map(item => {
          item.typeName = item.tunnelName || item.typeName || "隧道";
          item.typeStr = 'tunnel';
          item.listQueryConfig = {
            detlCondf: {
              path: './baseData/tunnel/baseInfo/archives/index.vue',
              type: 2, // 1：组件 2：路由 3：接口
            },
          }
          return item;
        });
        if (tunnelData.length > 20) {
          addCluster(tunnelData, tunnelWIcon, (e) => { }, 'name', 'tollStationLayer');
        } else {
          addWidthFeature(window.mapLayer, tunnelData, 'tollStationLayer', tunnelWIcon);
        }
      }
      return;
      this.$modal.loading();
      getListPage(params).then(res => {
        if (res.code === 200 && res.rows) {
          let data = res.rows.map(row => {
            row.typeName = row.name || row.typeName || "收费站";
            row.listQueryConfig = {
              detlCondf: {
                path: './baseData/ancillary/baseInfo/commonForm.vue',
                type: 1,
              },
            }
            row.mainTypeId = 8
            row.items = {}
            if (row.latitude && row.longitude) {
              let lonLat = [row.latitude - 0, row.longitude - 0];
            }
            return row;
          })
          addWidthFeature(window.mapLayer, data, 'tollStationLayer', TollStationIcon);
        }
      }).finally(() => {
        this.$modal.closeLoading();
      });
    },
    // 查询所有点
    onSearchAll() {
      // 组装请求条件
      let params = {
        ...this.form,
        paramsDTO: {
          precisionParams: {
            customParams: [
              {
                "col": "base.total_weight",
                "compareSymbol": "&gt;=",
                "colValue": null,
              },
              {
                "col": "base.wheelbase",
                "compareSymbol": "&gt;=",
                "colValue": null,
              },
              {
                "col": "base.axle_load",
                "compareSymbol": "&gt;=",
                "colValue": null,
              }
            ],
          }
        },
      }
      return new Promise((resolve, reject) => {
        this.$modal.loading();
        getListPage(params).then(res => {
          if (res.code === 200 && res.rows) {
            resolve(res.rows);
          } else {
            resolve([]);
          }
        }).catch(err => {
          reject(err);
        }).finally(() => {
          this.$modal.closeLoading();
        });
      });
    },
    // 查询符合条件的数据
    onSearch(params) {
      return new Promise((resolve, reject) => {
        this.$modal.loading();
        getListPage(params).then(res => {
          if (res.code === 200 && res.rows) {
            resolve(res.rows);
          } else {
            resolve([]);
          }
        }).catch(err => {
          reject(err);
        }).finally(() => {
          this.$modal.closeLoading();
        });
      });
    },
    // 桥梁数据
    onSearchBridge() {
      return new Promise((resolve, reject) => {
        let params = {
          pageNum: 1,
          pageSize: 10000,
          operationState: 2,
          managementMaintenanceIds: this.form.managementMaintenanceIds,
          maintenanceSectionIds: this.form.maintenanceSectionIds,
          bridgeTechAssessType: ['3', '4', '5'], // 类型 3类以上
        }
        this.$modal.loading();
        listStatic(params).then(res => {
          if (res.code === 200 && res.rows) {
            resolve(res.rows);
          } else {
            resolve([]);
          }
        }).catch(err => {
          reject(err);
        }).finally(() => {
          this.$modal.closeLoading();
        });
      });
    },
    // 隧道数据
    onSearchTunnel() {
      return new Promise((resolve, reject) => {
        let params = {
          pageNum: 1,
          pageSize: 10000,
          operationState: 2,
          managementMaintenanceIds: this.form.managementMaintenanceIds,
          maintenanceSectionIds: this.form.maintenanceSectionIds,
          assessmentGrades: ['3', '4', '5'], // 类型 3类以上
        }
        this.$modal.loading();
        listStaticTunnel(params).then(res => {
          if (res.code === 200 && res.rows) {
            resolve(res.rows);
          } else {
            resolve([]);
          }
        }).catch(err => {
          reject(err);
        }).finally(() => {
          this.$modal.closeLoading();
        });
      });
    },
    // 退出
    onQuit() {
      this.$emit('quit');
      removeLayer(window.mapLayer, 'tollStationLayer');
      removeLayer(window.mapLayer, 'tollStationLayer', 'name');
    },
  }
}
</script>

<style lang="scss" scoped>
@import "@/assets/styles/utils.scss";

.stake-mark {
  z-index: 9999;
  margin: 10px 0;
  color: #ffffff;

  ::v-deep .el-form {
    .el-form-item__label {
      color: #ffffff;
      font-size: vwpx(30px);
    }

    .el-input--mini .el-input__inner {
      height: vwpx(68px) !important;
      min-height: vwpx(66px) !important;
    }
  }

  ::v-deep .el-input {
    .el-input__inner {
      background-color: rgba(1, 102, 254, 0.2);
      border: 1px solid #0166FE;
      color: #ffffff;
      font-size: vwpx(28px);
    }

    .el-input__inner::placeholder {
      color: #BBBBBB;
    }

    .el-input-group__append {
      background-color: rgba(1, 102, 254, 0.2);
      border: 1px solid #0166FE;
      color: #ffffff;
      border-left: none;
      padding: 0 vwpx(20px);
      cursor: pointer;
    }
  }

  ::v-deep.el-input-number {

    .el-input-number__increase,
    .el-input-number__decrease {
      background-color: rgba(1, 102, 254, 0.2);
      border: 1px solid #0166FE;
      color: #ffffff;
      font-size: vwpx(28px);
      cursor: pointer;
    }
  }

  .btn-list {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    margin-top: vwpx(60px);

    button {
      height: vwpx(72px);
      min-height: vwpx(66px);
      width: vwpx(150px);
      height: 3vh;
      font-size: 1.4vh;
    }

    .hollow-out {
      background-color: rgba(1, 102, 254, 0.2);
      color: #ffffff;
    }
  }

  ::v-deep .el-select__tags {
    flex-wrap: nowrap;
    overflow: auto;
  }

  ::v-deep .el-select-dropdown__item {
    font-size: vwpx(30px);
    margin: vwpx(15px) 0;
  }
}

input[type="number"] {
  background-color: rgba(1, 102, 254, 0.2);
}
</style>
