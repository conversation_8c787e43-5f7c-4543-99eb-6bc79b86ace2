<template>
  <div class="level-statistics">
    <Echarts :option="option" v-if="option" height="100%" key="levelKey" />
  </div>
</template>

<script>
import * as echarts from "echarts";
import Echarts from '../../echarts/echarts.vue';
// api
import { getRouteGradeCount } from "@/api/cockpit/route";
import { isBigScreen } from "../../../util/utils";

export default {
  name: 'LevelStatistics',
  components: {
    Echarts
  },
  data() {
    return {
      isBig: isBigScreen(),
      option: null,
      loading: false,
      // 路线技术等级统计数据
      gradeCount: []
    }
  },
  async created() {
    this.loading = true;
    this.gradeCount = await this.getGradeCount();
    this.loading = false;
    this.$nextTick(() => {
      this.option = this.initCharts();
    })
  },
  mounted() {},
  methods: {
    // 获取 路线技术等级统计 数据
    getGradeCount() {
      return new Promise((resolve, reject) => {
        getRouteGradeCount().then((res) => {
          if (res.code === 200 && res.data) {
            resolve(res.data)
          } else {
            reject(res)
          }
        }).catch((err) => {
          reject(err)
        })
      })
    },
    // 初始化图表
    initCharts() {
      var salvProName = ["高速公路", "一级公路", "二级公路", "其他"]; // 默认
      var salvProValue = [1239, 1081, 851, 1044]; // 默认
      if (this.gradeCount && this.gradeCount.length) {
        salvProName = this.gradeCount.map(v => {
          return v.routeGrade || ''
        })
        salvProValue = this.gradeCount.map(v => {
          return v.length ? v.length.toFixed(2) - 0 : v.length - 0 || 0
        })
      }
      var salvProMax = [];//背景按最大值
      // 最大值为 所有数据和
      let max = salvProValue.reduce((acc, curr) => acc + curr, 0);
      for (let i = 0; i < salvProValue.length; i++) {
        salvProMax.push(max)
      }
      let option = {
        backgroundColor: "rgba(255,255,255,0)",
        grid: {
          left: '2%',
          right: '2%',
          bottom: '0%',
          top: '10%',
          containLabel: true
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'none'
          },
          formatter: (params) => {
            return params[0].name + ' : ' + params[0].value;
          }
        },
        xAxis: {
          show: false,
          type: 'value'
        },
        yAxis: [{
          type: 'category',
          inverse: true,
          axisLabel: {
            show: true,
            textStyle: {
              color: '#fff',
              fontSize: this.isBig ? 26 : 13,
              padding: [0, 10, 0, 0]
            },
          },
          splitLine: {
            show: false
          },
          axisTick: {
            show: false
          },
          axisLine: {
            show: false
          },
          data: salvProName
        }, {
          type: 'category',
          inverse: true,
          axisTick: 'none',
          axisLine: 'none',
          show: true,
          axisLabel: {
            textStyle: {
              color: '#ffffff',
              fontSize: this.isBig ? 26 : 13,
            },
          },
          data: salvProValue
        }],
        series: [{
          name: '值',
          type: 'bar',
          zlevel: 1,
          itemStyle: {
            normal: {
              barBorderRadius: this.isBig ? 30 : 15,
              color: new echarts.graphic.LinearGradient(0, 0, 1, 0, [{
                offset: 0,
                color: 'rgb(43,76,206,1)'
              }, {
                offset: 1,
                color: 'rgb(0,253,253,1)'
              }]),
            },
          },
          barWidth: this.isBig ? 16 : 8,
          data: salvProValue
        },
        {
          name: '背景',
          type: 'bar',
          barWidth: this.isBig ? 16 : 8,
          barGap: '-100%',
          data: salvProMax,
          itemStyle: {
            normal: {
              color: 'rgba(0,0,0,0.5)',
              barBorderRadius:this.isBig ? 30 : 15,
            }
          },
        },
        ]
      };
      return option;
    }
  }
}
</script>

<style lang="scss" scoped>
@import "@/assets/styles/utils.scss";

.level-statistics {
  width: 100%;
  height: 100%;
  padding: vwpx(10px) vwpx(20px);
}
</style>