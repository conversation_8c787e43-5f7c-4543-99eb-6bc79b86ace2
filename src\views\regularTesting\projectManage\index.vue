<template>
  <div class="app-container">
    <el-row :gutter="20">
      <!--筛选区开始-->
      <el-col :span="24" :xs="24">
        <el-row>
          <el-col :span="24">
            <el-form
              ref="queryForm"
              :inline="true"
              :model="queryParams"
              label-width="68px"
              size="small"
            >
              <el-form-item label="" prop="disType">
                <el-date-picker
                  v-model="queryParams.year"
                  placeholder="年份"
                  style="width: 200px"
                  type="year"
                  value-format="yyyy"
                >
                </el-date-picker>
              </el-form-item>
              <el-form-item>
                <selectTree
                  :key="'domainId'"
                  v-model="queryParams.domainId"
                  :deptType="100"
                  :deptTypeList="[1, 3, 4]"
                  clearable
                  filterable
                  placeholder="管养单位"
                  style="width: 200px"
                />
              </el-form-item>
              <el-form-item>
                <RoadSection
                  v-model="queryParams.maiSecId"
                  :deptId="queryParams.domainId"
                  placeholder="路段"
                  style="width: 200px"
                />
              </el-form-item>
              <el-form-item>
                <el-input
                  v-model="queryParams.name"
                  placeholder="项目名称"
                  style="width: 200px"
                ></el-input>
              </el-form-item>
              <el-form-item>
                <el-input
                  v-model="queryParams.code"
                  placeholder="项目编码"
                  style="width: 200px"
                ></el-input>
              </el-form-item>
              <el-form-item>
                <cost-select :type="6"
                  v-model="queryParams.projectType"
                  placeholder="工程类型"
                  style="width: 200px"
                  clearable
                />
              </el-form-item>
              <el-form-item>
                <selectTree
                  :key="'constructionUnit'"
                  v-model="queryParams.checkDomainId"
                  :data-rule="false"
                  clearable
                  filterable
                  :dept-type="100"
                  :filter-keys="['云南省交通投资建设集团有限公司', '云南交投投资有限公司']"
                  :expand-all="false"
                  placeholder="施工单位"
                  style="width: 200px"
                />
              </el-form-item>
              <el-form-item>
                <el-input
                  v-model="queryParams.constructionCode"
                  placeholder="任务单编码"
                  style="width: 200px"
                ></el-input>
              </el-form-item>
              <el-form-item>
                <dict-select
                  v-model="queryParams.status"
                  type="project_status_type"
                  placeholder="项目状态"
                  style="width: 200px"
                  clearable
                />
              </el-form-item>
              <el-form-item>
                <el-input
                  v-model="queryParams.createuser"
                  placeholder="操作人"
                  style="width: 200px"
                ></el-input>
              </el-form-item>
              <el-form-item>
                <el-button
                  icon="el-icon-search"
                  size="mini"
                  type="primary"
                  @click="handleQuery"
                  >搜索</el-button
                >
                <el-button
                  icon="el-icon-refresh"
                  size="mini"
                  @click="resetQuery"
                  >重置</el-button
                >
              </el-form-item>
            </el-form>
          </el-col>
        </el-row>
        <!--筛选区结束-->

        <!--操作按钮区开始-->
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button
              icon="el-icon-edit"
              size="mini"
              type="primary"
              v-has-menu-permi="['check:project:add']"
              @click="handleAdd"
              >新增
            </el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button
              icon="el-icon-download"
              size="mini"
              type="success"
              v-has-menu-permi="['check:project:export']"
              @click="exportList"
              >导出
            </el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button v-has-menu-permi="['check:project:editStatus']" size="mini" type="warning" @click="updProject(1)"
              >项目开始
            </el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button v-has-menu-permi="['check:project:editStatus']" size="mini" type="danger" @click="updProject(2)"
              >项目结束
            </el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button v-has-menu-permi="['check:project:editStatus']" size="mini" type="primary" @click="updProject(0)"
              >恢复到未开始
            </el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button v-has-menu-permi="['check:construction:add']" size="mini" type="success" @click="addTask('2')"
              >生成检测任务单
            </el-button>
          </el-col>
          <right-toolbar
            :columns="columns"
            :showSearch.sync="showSearch"
            @queryTable="handleQuery"
          ></right-toolbar>
        </el-row>
        <!--操作按钮区结束-->
        <!--数据表格开始-->
        <div class="tableDiv">
          <el-table v-adjust-table
            ref="dataTable"
            v-loading="loading"
            :data="tableData"
            :height="showSearch ? 'calc(100vh - 330px)' : 'calc(100vh - 270px)'"
            border
            highlight-current-row
            row-key="id"
            size="mini"
            @row-click="handleClickRow"
            stripe
            style="width: 100%"
          >
            <el-table-column
              align="center"
              label="序号"
              type="index"
              width="50"
            />
            <template v-for="(column, index) in columns">
              <el-table-column
                v-if="column.visible"
                :label="column.label"
                :prop="column.field"
                :width="column.width"
                align="center"
                show-overflow-tooltip
              >
                <template slot-scope="scope">
                  <dict-tag
                    v-if="column.dict"
                    :options="dict.type[column.dict]"
                    :value="scope.row[column.field]"
                  />
                  <template v-else-if="column.slots">
                    <RenderDom
                      :index="index"
                      :render="column.render"
                      :row="scope.row"
                    />
                  </template>
                  <span v-else-if="column.isTime">{{
                    parseTime(scope.row[column.field], "{y}-{m}-{d}")
                  }}</span>
                  <span v-else>{{ scope.row[column.field] }}</span>
                </template>
              </el-table-column>
            </template>
            <el-table-column
              align="center"
              class-name="small-padding fixed-width"
              fixed="right"
              label="操作"
              width="250"
            >
              <template slot-scope="scope">
                <el-button
                  icon="el-icon-delete"
                  size="mini"
                  type="text"
                  v-has-menu-permi="['check:project:remove']"
                  @click="handleDelete(scope.row)"
                  >删除
                </el-button>
                <el-button
                  icon="el-icon-edit"
                  size="mini"
                  type="text"
                  v-has-menu-permi="['check:project:edit']"
                  @click="handleEdit(scope.row)"
                  >修改
                </el-button>
                <el-button
                  icon="el-icon-view"
                  size="mini"
                  type="text"
                  @click="handleView(scope.row)"
                  >查看
                </el-button>
                <el-button
                  icon="el-icon-view"
                  size="mini"
                  type="text"
                  @click="handleFileManage(scope.row)"
                  >资料管理
                </el-button>
              </template>
            </el-table-column>
          </el-table>
          <pagination
            v-show="total > 0"
            :limit.sync="queryParams.pageSize"
            :page.sync="queryParams.pageNum"
            :total="total"
            @pagination="handleQuery"
          />
        </div>
        <!--数据表格结束-->
      </el-col>
    </el-row>
    <el-drawer
      :wrapperClosable="false"
      :title="detailTitle"
      :visible.sync="openDetail"
      size="70%"
      destroy-on-close
      v-if="openDetail"
    >
      <detail
        :row-data="row"
        @close="handleClose"
        :readonly="readonly"
      ></detail>
    </el-drawer>
    <el-drawer
      :wrapperClosable="false"
      title="新增任务单"
      :visible.sync="openTask"
      size="70%"
      destroy-on-close
      v-if="openTask"
    >
      <task-detail @close="openTask = false" :project="checkRow"></task-detail>
    </el-drawer>
    <el-drawer
      :wrapperClosable="false"
      title="资料管理"
      :visible.sync="openData"
      size="70%"
      destroy-on-close
      v-if="openData"
    >
      <data-manage @close="openData = false" :row="row"></data-manage>
    </el-drawer>
  </div>
</template>

<script>
import Tables from "@/views/patrol/frequencySettings/tables.vue";
import RoadSection from "@/views/baseData/components/roadSection/index.vue";
import selectTree from "@/components/DeptTmpl/selectTree.vue";
import Detail from "./detail.vue";
import TaskDetail from "@/views/regularTesting/taskManage/component/taskDetail.vue";
import DataManage from "@/views/regularTesting/projectManage/component/dataManage.vue";
import {
  queryList,
  updateProjectStatus,
  deleteProject,
} from "@/api/regularTesting/projectManage/projectInfo";
import CostSelect from "@/components/CostSelect/index.vue";
export default {
  name: 'projectManage',
  dicts: [
    "affiliation_project_type",
    "project_type",
    "project_status_type",
    "test_project_type",
  ],
  components: {
    CostSelect,
    Detail,
    selectTree,
    RoadSection,
    Tables,
    TaskDetail,
    DataManage,
    RenderDom: {
      functional: true,
      props: {
        row: Object,
        index: Number,
        render: Function,
      },
      render(createElement, ctx) {
        const { row, index } = ctx.props;
        return ctx.props.render(row, index);
      },
    },
  },
  data() {
    return {
      showSearch: false,
      queryParams: {
        pageNum: 1,
        pageSize: 50,
      },
      total: 0,
      loading: false,
      columns: [
        {
          key: 0,
          width: 100,
          field: "status",
          label: `状态`,
          visible: true,
          dict: "project_status_type",
        },
        {
          key: 1,
          width: 100,
          field: "domainName",
          label: `管养单位`,
          visible: true,
        },
        { key: 2, width: 100, field: "year", label: `年度`, visible: true },
        {
          key: 3,
          width: 100,
          field: "projectType",
          label: `工程类型`,
          visible: true,
          dict: "test_project_type",
        },
        { key: 4, width: 100, field: "name", label: `项目名称`, visible: true },
        { key: 5, width: 100, field: "code", label: `项目编码`, visible: true },
        {
          key: 6,
          width: 100,
          field: "maiSecName",
          label: `路段名称`,
          visible: true,
        },
        {
          key: 7,
          width: 100,
          field: "sumFund",
          label: `计划资金`,
          visible: true,
        },
        {
          key: 8,
          width: 100,
          field: "constructionCount",
          label: `任务单`,
          visible: true,
          slots: true,
          render: (row) => {
            if (row.constructionCount > 0) {
              return (
                <el-link type="primary" onClick={() => {this.toTaskPage(row)}}>{row.constructionCount}</el-link>
              )
            } else {
              return <span>0</span>
            }
          },
        },
        {
          key: 9,
          width: 200,
          field: "mileRange",
          label: `桩号范围`,
          visible: true,
          slots: true,
          render: (row) => {
            return (
              <el-tooltip
                className="item"
                effect="dark"
                content={row.mileRange}
                placement="top-start"
              >
                <div style="overflow: hidden;white-space:nowrap;text-overflow:ellipsis">
                  {row.mileRange}
                </div>
              </el-tooltip>
            );
          },
        },
        {
          key: 10,
          width: 200,
          field: "structureName",
          label: `构造物名称`,
          visible: true,
          slots: true,
          render: (row) => {
            return (
              <el-tooltip
                className="item"
                effect="dark"
                content={row.structureName}
                placement="top-start"
              >
                <div style="overflow: hidden;white-space:nowrap;text-overflow:ellipsis">
                  {row.structureName}
                </div>
              </el-tooltip>
            );
          },
        },
        {
          key: 11,
          width: 100,
          field: "reason",
          label: `立项理由`,
          visible: true,
        },
        {
          key: 12,
          width: 100,
          field: "updateuser",
          label: `操作人`,
          visible: true,
        },
        {
          key: 13,
          width: 100,
          field: "createuser",
          label: `上报人`,
          visible: true,
        },
        { key: 14, width: 100, field: "remark", label: `备注`, visible: true },
      ],
      tableData: [],
      detailTitle: "新增检测项目",
      openDetail: false,
      row: {},
      checkRow: null,
      openTask: false,
      openData: false,
      readonly: false,
    };
  },
  mounted() {
    this.handleQuery();
  },
  methods: {
    handleQuery() {
      if (this.queryParams.projectType === '') {
        delete this.queryParams.projectType;
      }
      this.loading = true;
      queryList(this.queryParams)
        .then((res) => {
          this.tableData = res.rows;
          this.total = res.total;
          this.loading = false;
        })
        .finally(() => {
          this.loading = false;
        });
    },
    resetQuery() {
      this.queryParams = {
        pageNum: 1,
        pageSize: 50,
      };
      this.handleQuery()
    },
    handleAdd() {
      this.row = {};
      this.detailTitle = "新增检测项目";
      this.readonly = false;
      this.openDetail = true;
    },
    handleEdit(row) {
      this.row = row;
      this.detailTitle = "修改项目信息";
      this.readonly = false;
      this.openDetail = true;
    },
    handleDelete(row) {
      this.$modal.confirm("是否确认删除").then(() => {
        this.loading = true;
        deleteProject(row.id).then((res) => {
          this.$modal.msgSuccess("删除成功");
          this.handleQuery();
        });
      });
    },
    handleView(row) {
      this.row = row;
      this.detailTitle = "项目信息";
      this.readonly = true;
      this.openDetail = true;
    },
    handleFileManage(row) {
      this.row = row;
      this.openData = true;
    },
    handleClose() {
      this.openDetail = false;
      this.handleQuery();
    },
    // 导出清单按钮
    exportList() {
      this.queryParams.fileName = "定期检测项目信息";
      this.download(
        "manager/check/project/export",
        { ...this.queryParams },
        `project_${new Date().getTime()}.xlsx`,
        {
          headers: { "Content-Type": "application/json;" },
          parameterType: "body",
        }
      );
    },
    handleClickRow(row) {
      this.checkRow = row;
    },
    updProject(status) {
      if (!this.checkRow) {
        this.$modal.msgError("请选择一条数据");
        return;
      } else {
        if (
          status == 0 &&
          (this.checkRow.constructionTaskList != 0 ||
            this.checkRow.checkTaskList != 0 ||
            this.checkRow.designTaskList != 0)
        ) {
          this.$modal.msgError("当前项目存在未完成的任务单，无法更新为未开始");
          return;
        }
        if (status == 1 && this.checkRow.status != 0) {
          this.$modal.msgError("当前项目已开始或结束，无法再次更新为开始");
          return;
        }
        if (status == 2 && this.checkRow.status != 1) {
          this.$modal.msgError("当前项目未开始，无法更新为已结束");
          return;
        }
        this.$modal.confirm("是否确认更新？").then(() => {
          this.loading = true;
          updateProjectStatus({ id: this.checkRow.id, status: status }).then(
            (res) => {
              this.$modal.msgSuccess("更新成功");
              this.handleQuery();
            }
          );
        });
      }
    },
    addTask(type) {
      if (!this.checkRow) {
        this.$modal.msgError("请选择一条数据");
        return;
      }
      this.openTask = true;
    },
    toTaskPage(row) {
      this.$router.push({
        path: "/regularTesting/taskManage/inspectionTask",
        query: { projName: row.name},
      })
    }
  },
};
</script>

<style lang="scss" scoped></style>
<style lang="scss" scoped>
@import "@/assets/styles/business.scss";
</style>
