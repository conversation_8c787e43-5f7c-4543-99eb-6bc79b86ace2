<template>
  <div v-loading="loading" class="road-interflow-edit" style="padding: 20px">
    <el-row :gutter="20">
      <el-form
        ref="elForm"
        :model="formData"
        :rules="rules"
        :disabled="readOnly"
        label-width="130px"
        size="medium"
      >
        <el-col :span="24">
          <div class="card_title">1.基本信息</div>
        </el-col>
        <el-col :span="8">
          <el-form-item label="项目" prop="projName">
            <el-input
              v-model="formData.projName"
              placeholder="项目"
              style="width: 100%"
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="名称" prop="name">
            <el-input
              v-model="formData.name"
              placeholder="名称"
              style="width: 100%"
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="编号" prop="code">
            <el-input
              v-model="formData.code"
              placeholder="编号"
              style="width: 100%"
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="检测单位" prop="checkDomainId">
            <construction-select
              v-model="formData.checkDomainId"
              :mai-sec-id="formData.maiSecId"
              :type="3"
              placeholder="检测单位"
              @change="changeCheckDomain"
            ></construction-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="检测合同" prop="checkConId">
            <contract-section
              v-model="formData.checkConId"
              :params="contractFilterMap"
              placeholder="请选择合同"
              @change="changeContract"
              ref="contractSectionRef"
            ></contract-section>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="起止日期" prop="dateArr">
            <el-date-picker
              v-model="formData.dateArr"
              type="daterange"
              :style="{ width: '100%' }"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              value-format="yyyy-MM-dd"
            >
            </el-date-picker>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="工作内容" prop="content">
            <el-input
              v-model="formData.content"
              :style="{ width: '100%' }"
              placeholder="工作内容"
              rows="2"
              type="textarea"
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="实施要求" prop="exeRequire">
            <el-input
              v-model="formData.exeRequire"
              :style="{ width: '100%' }"
              placeholder="实施要求"
              rows="2"
              type="textarea"
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="24" v-if="status !== 1">
          <el-form-item label="审核意见">
            <el-input
              v-model="formData.comment"
              :style="{ width: '100%' }"
              placeholder="审核意见"
              rows="2"
              type="textarea"
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="24" v-if="status == 4">
          <el-form-item label="下发时间" prop="issueDate">
            <el-input
              v-model="formData.issueDate"
              :style="{ width: '100%' }"
              placeholder="下发时间"
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <div class="card_title">2.合同清单</div>
        </el-col>
        <el-col :span="24">
          <el-button
            style="margin-bottom: 10px; float: right"
            type="primary"
            @click="addScheme"
            >新增
          </el-button>
          <methods-list :value.sync="constructionDetailList"></methods-list>
        </el-col>
        <el-col :span="24" class="mt10">
          <div style="text-align: right">
            <el-button v-if="status == 1" type="primary" @click="handleSave"
              >保存</el-button
            >
            <el-button v-if="status == 2" type="primary" @click="handleSave"
              >提交</el-button
            >
            <template v-if="status == 3">
              <el-button type="primary" @click="handleSave">通过</el-button>
              <el-button type="danger" @click="overrule">驳回</el-button>
            </template>
            <template v-if="status == 4">
              <el-button v-if="type === 1" type="primary" @click="handleSave"
                >签发</el-button
              >
              <el-button type="danger" @click="overrule">撤回</el-button>
            </template>
            <template v-if="status == 5">
              <el-button type="primary" @click="handleSave">接收</el-button>
              <el-button type="danger" @click="overrule">拒收</el-button>
            </template>
            <el-button @click="close">取消</el-button>
          </div>
        </el-col>
      </el-form>
    </el-row>
    <methods-tree
      ref="methodsTree"
      :con-id="formData.checkConId"
      :domain-id="methodsTreeDomainId"
      v-model="methodsTreeSelectData"
      :loading="loading"
    ></methods-tree>
  </div>
</template>

<script>
import selectTree from "@/components/DeptTmpl/selectTree.vue";
import RoadSection from "@/views/baseData/components/roadSection/index.vue";
import ContractSection from "@/components/ContractSection/index.vue";
import ConstructionSelect from "@/components/ConstructionSelect/index.vue";
import MethodsList from "@/components/MethodsList/index.vue";
import MethodsTree from "@/components/MethodsTree/index.vue";
import {
  addConstruction,
  editConstruction,
  changeConstructionStatus,
  getConstructionDetailListAll,
  getConListByProIdAndConId
} from "@/api/regularTesting/taskManage/taskList";
import { getCode } from "@/api/system/reportcode";
import moment from "moment/moment";
export default {
  components: {
    ConstructionSelect,
    ContractSection,
    RoadSection,
    selectTree,
    MethodsList,
    MethodsTree,
  },
  data() {
    return {
      loading: false,
      formData: {},
      editableTabsValue: "",
      defaultProps: {
        children: "children",
        label: "schemeName",
      },
      rules: {
        projName: [
          { required: true, message: "项目不能为空", trigger: "blur" },
        ],
        name: [{ required: true, message: "名称不能为空", trigger: "blur" }],
        code: [{ required: true, message: "编号不能为空", trigger: "blur" }],
        checkDomainId: [
          { required: true, message: "检测单位不能为空", trigger: "blur" },
        ],
        checkConId: [
          { required: true, message: "检测合同不能为空", trigger: "blur" },
        ],
        dateArr: [
          { required: true, message: "起止日期不能为空", trigger: "blur" },
        ],
        content: [
          { required: true, message: "工作内容不能为空", trigger: "blur" },
        ],
        exeRequire: [
          { required: true, message: "实施要求不能为空", trigger: "blur" },
        ],
      },
      constructionDetailList: [],
      contractFilterMap: {},
      methodsTreeSelectData: [],
      methodsTreeDomainId: ''
    };
  },
  props: {
    status: {
      // 流程状态 1修改 2提交 3审核 4签发 5接收
      type: Number,
      default: 1,
    },
    readOnly: {
      type: Boolean,
      default: false,
    },
    project: {
      type: Object,
      default: () => {},
    },
    row: {
      type: Object,
      default: () => {},
    },
    type: {
      type: Number,
      default: 1,
    },
  },
  watch: {
    project: {
      handler(val) {
        if (val) {
          this.methodsTreeDomainId = val.domainId
          this.formData = {
            projId: val.id,
            projName: val.name,
            projCode: val.code,
            projectType: val.projectType,
            domainId: val.domainId,
            maiSecId: val.maiSecId,
          };
          this.contractFilterMap = {
            sectionName: val.maiSecName,
            type: "3",
          };
          const params = {
            reportType: "MP_DJ_TASK_CODE",
            domainId: val.domainId,
            sectionName: val.maiSecName,
            year: new Date().getFullYear(),
          };
          getCode(params).then((res) => {
            if (res.code == 200) {
              this.$set(this.formData, "code", res.msg);
            }
          });
        }
      },
      immediate: true,
      deep: true,
    },
    row: {
      handler(val) {
        if (val) {
          this.methodsTreeDomainId = val.domainId
          this.contractFilterMap = {
            sectionName: val.maiSecName,
            type: "3",
          };
          this.formData = JSON.parse(JSON.stringify(val));
          this.formData.dateArr = [
            this.formData.beginDate,
            this.formData.endDate,
          ];
          if (this.status == 4) {
            this.formData.issueDate = moment().format("YYYY-MM-DD HH:mm:ss");
          }
          if (val.id) {
            getConstructionDetailListAll({ projConId: val.id }).then((res) => {
              if (res.data) {
                this.constructionDetailList = res.data || [];
              }
            });
          }
        }
      },
      immediate: true,
      deep: true,
    },
    methodsTreeSelectData: {
      async handler(val) {
        if (val.length > 0) {
          const constructionDetailList = [];
          const conId = this.formData.checkConId;
          for (let index = 0; index < val.length; index++) {
            const item = val[index];
            const hasFlag = this.constructionDetailList.some((value) => {
              return value.schemeId === item.id;
            });
            if (hasFlag) {
              try {
                await this.$confirm(
                  `存在与子目号${item.schemeCode}相同的数据，是否确认加入?`,
                  "确认",
                  {
                    confirmButtonText: "确定",
                    cancelButtonText: "取消",
                    type: "warning",
                    distinguishCancelAndClose: true,
                  }
                );
                constructionDetailList.push({
                  conId,
                  schemeId: item.id,
                  schemeCode: item.schemeCode,
                  schemeName: item.schemeName,
                  unit: item.unit,
                  price: item.price,
                  priceRate: item.priceRate,
                  isProduction: item.safetyFeeFlag,
                  order: this.constructionDetailList.length + constructionDetailList.length + 1,
                });
              } catch (error) {
                continue;
              }
            } else {
              constructionDetailList.push({
                conId,
                schemeId: item.id,
                schemeCode: item.schemeCode,
                schemeName: item.schemeName,
                unit: item.unit,
                price: item.price,
                priceRate: item.priceRate,
                isProduction: item.safetyFeeFlag,
                order: this.constructionDetailList.length + constructionDetailList.length + 1,
              });
            }
          }

          this.constructionDetailList = [
            ...this.constructionDetailList,
            ...constructionDetailList,
          ];
        }
      },
      immediate: true,
      deep: true,
    },
  },
  methods: {
    changeCheckDomain() {
      this.$refs.contractSectionRef.selectValue = "";
      this.$set(
        this.contractFilterMap,
        "conDomainId",
        this.formData.checkDomainId
      );
    },
    handleSave() {
      this.$refs.elForm.validate((valid) => {
        if (!valid) return;
        this.loading = true;
        this.formData.constructionDetailList = this.constructionDetailList;
        this.formData.beginDate = this.formData.dateArr[0];
        this.formData.endDate = this.formData.dateArr[1];
        if (this.status === 1) {
          if (this.formData.id) {
            editConstruction(this.formData).then((res) => {
              this.loading = false;
              this.$message.success("保存成功");
              this.close();
            });
          } else {
            addConstruction(this.formData).then((res) => {
              this.loading = false;
              this.$message.success("保存成功");
              this.close();
            });
          }
        } else {
          let changeStatusData = this.formData;
          changeStatusData = {
            approved: true,
            businessKey: this.formData.id,
            comment: this.formData.comment,
            taskId: this.formData.taskId,
          };
          if (this.status == 4) {
            changeStatusData.issueDate = this.formData.issueDate;
          }
          this.changeStatus(changeStatusData);
        }
      });
    },
    changeStatus(data) {
      changeConstructionStatus(data).then((res) => {
        this.loading = false;
        this.$message.success("操作成功");
        this.close();
      });
    },
    overrule() {
      if (!this.formData.comment) {
        this.$message.warning("请填写审核意见");
        return;
      }
      this.changeStatus({
        approved: false,
        businessKey: this.formData.id,
        comment: this.formData.comment,
        taskId: this.formData.taskId,
      });
    },
    close() {
      this.$emit("close");
    },
    changeContract() {
      this.constructionDetailList = [];
      if (this.project && this.project.id) {
        getConListByProIdAndConId({
          projId: this.project.id,
          conId: this.formData.checkConId,
        }).then(res=> {
          const data = res.data || []
          this.constructionDetailList = data.map((item, index)=> {
            return {
              conId: this.formData.checkConId,
              schemeId: item.schemeId,
              schemeCode: item.schemeCode,
              schemeName: item.schemeName,
              unit: item.unit,
              price: item.price,
              priceRate: item.priceRate,
              isProduction: item.safetyFeeFlag,
              calcDesc: item.calcDesc,
              amount: item.amount,
              num: item.num,
              order: this.constructionDetailList.length + index + 1,
            }
          })
        })
      }
    },
    addScheme() {
      if (!this.formData.checkConId) {
        this.$message.error("请先选择合同");
        return;
      }
      this.$refs.methodsTree.openLibModel();
    },
  },
};
</script>
<style lang="scss" scoped>
.card_title {
  width: 200px;
  text-align: left;
  margin-bottom: 15px;
  font-weight: bold;
}
</style>
<style lang="scss" scoped>
@import "@/assets/styles/business.scss";
</style>
