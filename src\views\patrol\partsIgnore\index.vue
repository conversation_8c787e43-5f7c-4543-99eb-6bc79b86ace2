<template>
  <div class="app-container">
    <el-card shadow="hover">
      <!--      <div slot="header" class="clearfix">-->
      <div slot="header">
        <span style="color: #389BEE;">桥梁经常检查</span>
        <!--        <span  style="float: right; font-size: 0.8rem; vertical-align: bottom">{{currentRow.bridgeName ? `当前桥梁：${currentRow.bridgeName}` : '' }}</span>-->
      </div>
      <!-- card-body-->
      <div style="display: flex; flex-direction: column; align-items: flex-end; height: 100%">
        <el-form ref="queryForm" :model="queryParams" size="small" :inline="true">
          <el-form-item prop="managementMaintenanceId">
            <tree-select v-model="queryParams.managementMaintenanceId" :options="deptOptions" :show-count="true"
                         placeholder="请选择管养单位"/>
          </el-form-item>
          <el-form-item prop="maintenanceSectionId">
            <el-select v-model="queryParams.maintenanceSectionId" placeholder="养护路段" clearable>
              <el-option
                v-for="v in maintenanceSectionList" :key="v.maintenanceSectionId"
                :label="v.maintenanceSectionName"
                :value="v.maintenanceSectionId"
              />
            </el-select>
          </el-form-item>
          <el-form-item prop="bridgeCode">
            <el-input
              v-model="queryParams.bridgeCode"
              style="width:100%"
              placeholder="桥梁编号"
              clearable
            />
          </el-form-item>
          <el-form-item prop="bridgeName">
            <el-input
              v-model="queryParams.bridgeName"
              style="width:100%;"
              placeholder="桥梁名称"
              clearable
            />
          </el-form-item>
          <el-form-item style="width:220px">
            <div>
              <el-button
                type="primary"
                icon="el-icon-search"
                size="mini"
                @click="handleQuery"
              >搜索
              </el-button>
              <el-button
                icon="el-icon-refresh"
                size="mini"
                @click="resetQuery"
              >重置
              </el-button>
              <el-button
                v-show="!showSearch"
                icon="el-icon-arrow-down"
                circle
                @click="showSearch=true"
              />
              <el-button
                v-show="showSearch"
                icon="el-icon-arrow-up"
                circle
                @click="showSearch=false"
              />
            </div>
          </el-form-item>
          <el-form-item v-show="showSearch" prop="routeCode">
            <el-select v-model="queryParams.routeCode" filterable clearable placeholder="请选择路线编码">
              <el-option
                v-for="item in routeList"
                :key="item.routeId"
                :label="item.routeName"
                :value="item.routeCode">
              </el-option>
            </el-select>
            <!--  <el-select v-model="queryParams.baseRouteId" filterable placeholder="请选择路线">
                          <el-option
                            v-for="item in routeList"
                            :key="item.routeId"
                            :label="item.routeName"
                            :value="item.routeId">
                          </el-option>
            </el-select>-->
          </el-form-item>

          <el-form-item v-show="showSearch" prop="mainSuperstructureType">
            <DictSelect
              multiple
              v-model="queryParams.mainSuperstructureType"
              :type="'bridge_main_superstructure_type'"
              :placeholder="'结构形式'"
              clearable
            />
          </el-form-item>

        </el-form>

        <right-toolbar :showSearch.sync="showSearch" @queryTable="getList" :columns="columns"
                       style="margin: 10px  0"></right-toolbar>
        <!-- 表格-->
        <el-table
          v-adjust-table
          v-loading="loading"
          size="mini"
          height="100%"
          border
          highlight-current-row
          :data="tableDataList"
          @row-click="(row)=>currentRow = row"
        >
          <!--        <el-table-column type="selection" width="50" align="center"/>-->
          <el-table-column fixed width="50" align="center">
            <template scope="scope">

              <el-radio v-model="radioCurrentRow" :label="scope.row.id"></el-radio>
            </template>
          </el-table-column>
          <el-table-column fixed label="序号" type="index" width="50" align="center">
            <template v-slot="scope">
              {{ (scope.$index + (queryParams.pageNum - 1) * queryParams.pageSize)+1 }}
            </template>
          </el-table-column>
          <el-table-column fixed label="桥梁名称" align="center" prop="bridgeName" min-width="140"
                           show-overflow-tooltip/>
          <el-table-column label="桥梁编号" align="center" prop="bridgeCode" min-width="140" show-overflow-tooltip/>
          <el-table-column label="养护路段" align="center" prop="maintenanceSectionName" min-width="140"
                           show-overflow-tooltip/>
          <el-table-column label="路线编号" align="center" prop="routeCode" min-width="140" show-overflow-tooltip/>

          <el-table-column
            label="管养处"
            align="center"
            prop="managementMaintenanceName"
            min-width="140"
            show-overflow-tooltip
          />
          <el-table-column
            v-if="columns[0].visible"
            label="管养分处"
            align="center"
            prop="managementMaintenanceBranchName"
            min-width="140"
            show-overflow-tooltip
          />
          <el-table-column
            v-if="columns[1].visible"
            label="结构形式"
            align="center"
            prop="mainSuperstructureTypeName"
            min-width="140"
            show-overflow-tooltip
          />
          <el-table-column
            v-if="columns[2].visible"
            label="桥位桩号"
            align="center"
            prop="centerStake"
            min-width="140"
            :formatter="(...arg)=>formatPile(arg[2])"
            show-overflow-tooltip
          />
          <el-table-column
            v-if="columns[3].visible"
            label="桥长"
            align="center"
            prop="totalLength"
            min-width="140"
            show-overflow-tooltip
          />
          <el-table-column
            v-if="columns[4].visible"
            label="桥梁评定等级"
            align="center"
            prop="bridgeTechAssessTypeName"
            min-width="140"
            show-overflow-tooltip
          />
          <el-table-column
            v-if="columns[5].visible"
            label="跨径分类"
            align="center"
            prop="spanClassifyTypeName"
            min-width="140"
            show-overflow-tooltip
          />
          <el-table-column
            v-if="columns[6].visible"
            label="跨径组合"
            align="center"
            prop="spanGroups"
            min-width="140"
            show-overflow-tooltip
          />
          <el-table-column
            v-if="columns[7].visible"
            label="固定编码"
            align="center"
            prop="fixedCode"
            min-width="140"
            show-overflow-tooltip
          />
          <el-table-column
            v-if="columns[8].visible"
            label="是否是独柱墩"
            align="center"
            prop="whetherSingleColumnPierTypeName"
            min-width="140"
            show-overflow-tooltip
          />
          <el-table-column
            v-if="columns[9].visible"
            label="建成时间"
            align="center"
            prop="buildDate"
            min-width="140"
            show-overflow-tooltip
          />

        </el-table>
        <pagination
          :total="total"
          :page.sync="queryParams.pageNum"
          :limit.sync="queryParams.pageSize"
          @pagination="getList"
        />
      </div>
    </el-card>

    <el-card shadow="hover">
      <div slot="header">
        <span>不检查项配置</span>
        <el-button style="float: right;margin-top: -5px " size="mini" type="primary" @click="onSubmit"
                   :disabled="!currentRow.hasOwnProperty('id')">完成
        </el-button>
      </div>

      <!--<el-scrollbar style="height:100%">-->
      <div style="height: 100%; overflow: auto">
        <el-tree :data="partsTree" :props="defaultProps" node-key="id" default-expand-all
                 show-checkbox
                 v-loading="loading || partsLoading "
                 element-loading-spinner="none"
                 element-loading-background="rgba(255, 255, 255, 0.8)"
                 check-on-click-node :expand-on-click-node="false" ref="tree"
                 :default-checked-keys="partsIgnoreIds"></el-tree>
      </div>
      <!-- </el-scrollbar>-->
    </el-card>
  </div>
</template>

<script>


import TreeSelect from "@riophae/vue-treeselect";
import "@riophae/vue-treeselect/dist/vue-treeselect.css";
import {deptTreeSelect} from "@/api/system/user";
import {listMaintenanceSectionAll} from "@/api/system/maintenanceSection";
import {listAllRoute} from "@/api/system/route";
import {listStatic} from "@/api/baseData/bridge/baseInfo";
import {listAllParts} from "@/api/patrol/parts";
import {addBatchPartsIgnore, delPartsIgnoreByCondition, listAllPartsIgnore} from "@/api/patrol/partsIgnore";

export default {
  name: "BridgePartsIgnore",
  props: {
    partsType: {
      default: '2'
      // default: 'BRIDGE_REGULAR_INSPECTION'
    }
  },
  components: {TreeSelect},
  data() {
    return {
      partsTree: [],
      defaultProps: {
        children: 'children',
        label: 'partsName'
      },
      currentRow: {},
      radioCurrentRow: null,
      partsIgnoreIds: [],
      deptOptions: [],
      maintenanceSectionList: null,
      routeList: null,
      tableDataList: null,
      total: 0,
      loading: false,
      partsLoading: false,
      queryParams: {
        pageNum: 1,
        pageSize: 50,
        managementMaintenanceId: null,
        maintenanceSectionId: null,
        bridgeCode: null,
        bridgeName: null,
        routeCode: null,
        mainSuperstructureType: null
      },
      showSearch: false,
      columns: [
        {key: 0, label: `管养分处`, visible: true},
        {key: 1, label: `结构形式`, visible: false},
        {key: 2, label: `桥位桩号`, visible: false},
        {key: 3, label: `桥长`, visible: false},
        {key: 4, label: `桥梁评定等级`, visible: false},
        {key: 5, label: `跨径分类`, visible: false},
        {key: 6, label: `跨径组合`, visible: false},
        {key: 7, label: `固定编码`, visible: false},
        {key: 8, label: `是否是独柱墩`, visible: false},
        {key: 9, label: `建成时间`, visible: false}
      ],
    };
  },
  watch: {
    currentRow() {
      this.radioCurrentRow = this.currentRow.id
      this.getPartsIgnore()
    }
  },
  created() {
    this.getParts()
    this.getList()
    this.getDeptTree()
    this.getRouteList()
    this.getMaintenanceSection()
  },
  methods: {
    async onSubmit() {
      this.partsLoading = true
      !(this.partsIgnoreIds == false) && await delPartsIgnoreByCondition({assetId: this.currentRow.assetId})
      let partsIds = this.$refs.tree.getCheckedKeys()
      if (partsIds.length < 1) {
        this.partsLoading = false
        this.$modal.msgSuccess("提交成功");
        return
      }
      let formArray = partsIds.map(item => {
        return {
          partsType: this.partsType,
          partsId: item,
          assetId: this.currentRow.assetId
        }
      })
      addBatchPartsIgnore(formArray).then(res => {
        this.$modal.msgSuccess("提交成功");
        // this.getPartsIgnore()
        this.partsIgnoreIds = partsIds
      }).finally(() => {
        this.partsLoading = false
      })
    },
    getPartsIgnore() {
      this.$refs.tree.setCheckedKeys([])
      let temp = {
        partsId: null,
        partsType: this.partsType,
        assetId: this.currentRow.assetId
      }
      this.partsLoading = true
      listAllPartsIgnore(temp).then(response => {
        this.partsIgnoreIds = response.data.map(item => item.partsId)
        this.partsLoading = false
      });
    },
    getParts() {
      this.partsLoading = true
      listAllParts({partsType: this.partsType}).then(response => {
        this.partsTree = response.data
        this.partsLoading = false
      });
    },
    wrapperQueryParams(){
      let temp = {
        maintenanceSectionIds: '',
        managementMaintenanceIds: '',
        routeCodes: '',

      }
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    /** 获取表格数据*/
    getList() {
      this.loading = true

      // if (this.queryParams.mainSuperstructureType)
      // this.queryParams.mainSuperstructureType = [this.queryParams.mainSuperstructureType]

      listStatic(this.queryParams).then(response => {
        this.tableDataList = response.rows
        this.total = response.total
        this.loading = false
      })
    },
    /** 查询部门下拉树结构 */
    getDeptTree() {
      return deptTreeSelect().then(response => {
        this.deptOptions = response.data;
      });
    },
    /** 查询养护路段下拉列表 */
    getMaintenanceSection() {
      listMaintenanceSectionAll().then(res => {
        this.maintenanceSectionList = res.data
      })
    },
    /** 查询路线列表 */
    getRouteList() {
      listAllRoute().then(res => {
        this.routeList = res.data
      })
    }
  }
};
</script>
<style scoped>
.app-container {
  padding: 10px;
  display: flex;
}
.el-card:first-child {
  width: 80%;
  margin-right: 10px;
  box-sizing: border-box;
}
.el-card:nth-child(2) {
  width: 20%;
}
::v-deep .el-card__body {
  height: 94%;
  /*  padding: 15px 20px 5px 20px;*/
}
.el-form--inline.el-form {
  background: rgb(246, 246, 247);
  border-radius: 6px;
  padding: 10px;
}
.el-form--inline .el-form-item {
  width: 18.5%;
  margin: 10px 0 10px 10px;
}
::v-deep .vue-treeselect__control {
  height: auto;
}
::v-deep .el-radio__label{
  display: none;
}
::v-deep .el-table__body tr.current-row > td.el-table__cell {
  background: #F3DFDF ;
}
/*::v-deep .el-scrollbar__wrap {
  overflow-x: hidden;
}
::v-deep .el-scrollbar__bar.is-vertical {
  opacity: 1;
}*/
</style>
