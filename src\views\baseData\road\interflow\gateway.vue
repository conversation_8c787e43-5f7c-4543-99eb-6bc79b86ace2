<template>
  <div class="gateway">
    <div style="margin-bottom: 10px">
      <el-row>
        <!-- <el-col :span="5">
          <RouteLine v-model="searchForm.wayId" placeholder="路线编码" size="mini" @keyup.enter.native="onSelect" />
        </el-col> -->
        <el-form
          :model="queryParams"
          ref="queryForm"
          size="small"
          :inline="true"
          label-width="86px"
        >
          <el-form-item label="" prop="baseRouteId">
            <el-select
              v-model="queryParams.baseRouteId"
              clearable
              filterable
              placeholder="请选择路线"
            >
              <el-option
                v-for="item in routeList"
                :key="item.routeId"
                :label="item.routeName"
                :value="item.routeId"
              >
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="" prop="maintenanceSectionId">
            <el-select
              v-model="queryParams.maintenanceSectionId"
              clearable
              filterable
              placeholder="请选择养护路段"
            >
              <el-option
                v-for="item in maintenanceSectionList"
                :key="item.maintenanceSectionId"
                :label="item.maintenanceSectionName"
                :value="item.maintenanceSectionId"
              >
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="" prop="routeSegmentsName">
            <el-input
              v-model="queryParams.routeSegmentsName"
              placeholder="请输入子段名称"
              clearable
              @keyup.enter.native="handleQuery"
            />
          </el-form-item>
          <el-form-item label="" prop="roadGrade">
            <el-select
              v-model="queryParams.roadGrade"
              placeholder="请选择路线等级"
              clearable
            >
              <el-option
                v-for="dict in dict.type.sys_route_grade"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button
              type="primary"
              icon="el-icon-search"
              size="mini"
              @click="handleQuery"
              >搜索</el-button
            >
            <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
              >重置</el-button
            >
            <el-button
              type="primary"
              icon="el-icon-search"
              size="mini"
              @click="onSelect"
              >确定</el-button
            >
          </el-form-item>
        </el-form>
      </el-row>
    </div>

    <el-table
      ref="tableEl"
      v-loading="loading"
      height="400px"
      :data="tableData"
      :row-key="getRowKey"
      @selection-change="onSelectTable"
      @select="selectClick"
      @select-all="selectAllClick"
    >
      <el-table-column
        fixed="left"
        :reserve-selection="true"
        type="selection"
        width="55"
        align="center"
      />
      <el-table-column
        label="序号"
        fixed="left"
        type="index"
        width="55"
        align="center"
      />
      <el-table-column
        label="管理处"
        align="center"
        prop="maintenanceUnitName"
        show-overflow-tooltip
      />
      <el-table-column
        label="路段名称"
        align="center"
        prop="routeSegmentsName"
      />
      <el-table-column label="路线" align="center" prop="baseRouteName" />
      <el-table-column
        label="养护路段"
        align="center"
        prop="maintenanceSectionName"
      />
      <el-table-column
        label="路线等级"
        :show-overflow-tooltip="true"
        align="center"
        prop="roadGrade"
      >
        <template slot-scope="scope">
          <dict-tag
            :options="dict.type.sys_route_grade"
            :value="scope.row.roadGrade"
          />
        </template>
      </el-table-column>
      <el-table-column
        label="路段类型"
        :show-overflow-tooltip="true"
        align="center"
        prop="roadType"
      >
        <template slot-scope="scope">
          <dict-tag
            :options="dict.type.sys_route_type"
            :value="scope.row.roadType"
          />
        </template>
      </el-table-column>
      <el-table-column
        label="路面类型"
        :show-overflow-tooltip="true"
        align="center"
        prop="roadSurfaceType"
        width="120"
      >
        <template slot-scope="scope">
          <dict-tag
            :options="dict.type.sys_surface_type"
            :value="scope.row.roadSurfaceType"
          />
        </template>
      </el-table-column>
      <el-table-column
        label="匝道口"
        :show-overflow-tooltip="true"
        align="center"
        prop="ramp"
      >
        <template slot-scope="scope">
          <dict-tag :options="dict.type.sys_ramp" :value="scope.row.ramp" />
        </template>
      </el-table-column>
      <el-table-column
        label="车道数"
        :show-overflow-tooltip="true"
        align="center"
        prop="lanes"
      />
    </el-table>

    <pagination
      v-show="pages.total > 0"
      :total="pages.total"
      :page.sync="pages.pageNum"
      :limit.sync="pages.pageSize"
      @pagination="getList"
    />
  </div>
</template>

<script>
import { listRouteSegments } from "@/api/system/routeSegments.js";
import { listMaintenanceSectionAll } from "@/api/system/maintenanceSection";
import { listAllRoute } from "@/api/system/route";

// import RouteLine from '@/components/RouteLine'
export default {
  name: "Gateway",
  dicts: [
    "sys_route_type",
    "sys_route_grade",
    "sys_surface_type",
    "sys_operation_state",
    "sys_ramp",
  ],
  components: {
    /* RouteLine */
  },

  props: {
    params: {
      type: Object,
      default: () => {},
    },
    list: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      loading: false,
      tableData: [],
      tableSelects: [], // table选中数组
      pages: {
        pageNum: 1,
        pageSize: 20,
        total: 0,
      },
      isReverse: true,
      // 查询参数
      queryParams: {
        pageNum: 1,
        routeSegmentsName: null,
        maintenanceSectionId: null,
        baseRouteId: this.routeId,
        maintenanceUnit: null,
        roadSectionLength: null,
        roadGrade: null,
        mainLength: null,
        roadType: 2,
        roadSurfaceType: null,
        lanes: null,
        openingTime: null,
        pileStart: null,
        pileEnd: null,
        placeStartName: null,
        placeEndName: null,
        unifiedMileagePileStart: null,
        unifiedMileagePileEnd: null,
        baseRouteCode: null,
      },
      routeList: [],
      maintenanceSectionList: [],
    };
  },
  created() {
    //管养分处
    this.queryParams.maintenanceUnit =
      this.params.managementMaintenanceBranchId;

    this.queryParams.maintenanceSectionId = this.params.maintenanceSectionId;

    this.queryParams.baseRouteCode = this.params.routeCode;

    // 路线编码为空routeCode
    this.init();
    this.getRouteList();
    this.getMaintenanceSection();
  },
  methods: {
    init() {
      this.tableSelects = JSON.parse(JSON.stringify(this.list));
      this.getList();
    },
    /** 查询列表 */
    getList() {
      this.loading = true;
      this.queryParams.pageSize = this.pages.pageSize;
      this.queryParams.pageNum = this.pages.pageNum;

      listRouteSegments(this.queryParams)
        .then((res) => {
          if (res.code === 0) {
            this.tableData = res.rows;
            this.pages.total = res.total;
            if (this.isReverse) {
              this.$nextTick(() => {
                this.tableData
                  .filter((el) => {
                    return this.list.some(
                      (ele) => el.routeSegmentsId === ele.rampId
                    );
                  })
                  .forEach((el) => {
                    this.$refs.tableEl.toggleRowSelection(el, true);
                  });
              });
            }
          }
        })
        .finally(() => {
          this.loading = false;
        });
    },
    /** 搜索按钮操作 */
    onSelect() {
      this.$emit("change", this.tableSelects);
    },
    onSelectTable(list) {
      this.tableSelects = list.map((el) => {
        el.rampId = el.routeSegmentsId;
        return el;
      });
    },
    getRowKey(row) {
      return row.routeSegmentsId;
    },
    onRowClick(row) {
      this.isReverse = false;
      this.$refs.tableEl.toggleRowSelection(row);
    },
    selectClick() {
      this.isReverse = false;
    },
    selectAllClick() {
      this.isReverse = false;
    },
    getRouteList() {
      listAllRoute().then((res) => {
        this.routeList = res.data;
      });
    },
    getMaintenanceSection() {
      listMaintenanceSectionAll().then((res) => {
        this.maintenanceSectionList = res.data;
      });
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
  },
};
</script>

<style lang="scss" scoped>
.gateway {
}
</style>
