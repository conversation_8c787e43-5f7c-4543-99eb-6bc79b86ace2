<template>
  <div class="check-edit-container">
    <!--    基础信息-->
    <div class="check-edit-title">基础信息</div>
    <el-form ref="form" :model="checkEntity" :rules="rules" inline label-width="80px" size="small">
      <el-form-item label="桥梁名称" prop="assetName">
        <el-input v-model="checkEntity.assetName" readonly @click.native="selectBridge" placeholder="点击选择..."/>
      </el-form-item>
      <el-form-item label="桥梁编码" prop="assetCode">
        <el-input v-model="checkEntity.assetCode" readonly placeholder="选择桥梁自动填充"/>
      </el-form-item>
      <el-form-item label="路线编码" prop="routeCode">
        <el-input v-model="checkEntity.routeCode" readonly placeholder="选择桥梁自动填充"/>
      </el-form-item>
      <el-form-item label="养护路段" prop="maintenanceSectionName">
        <el-input v-model="checkEntity.maintenanceSectionName" readonly placeholder="选择桥梁自动填充"/>
      </el-form-item>
      <el-form-item label="桥位桩号" prop="centerStake">
        <el-input v-model="checkEntity.centerStake" readonly placeholder="选择桥梁自动填充"/>
      </el-form-item>
      <el-form-item label="养护单位" prop="maintainUnitName">
        <el-input v-model="checkEntity.maintainUnitName" readonly placeholder="选择桥梁自动填充"/>
      </el-form-item>
      <el-form-item label="负责人" prop="kahuna">
        <el-input v-model="checkEntity.kahuna" placeholder="请输入负责人"/>
      </el-form-item>
      <el-form-item label="记录人" prop="oprUser">
        <el-input v-model="checkEntity.oprUser" placeholder="请输入记录人"/>
      </el-form-item>
      <el-form-item label="检查时间" prop="checkTime">
        <el-date-picker clearable
                        v-model="checkEntity.checkTime"
                        type="date"
                        value-format="yyyy-MM-dd"
                        placeholder="请选择检查时间">
        </el-date-picker>
      </el-form-item>
      <el-form-item label="备注" prop="remark" style="width: 94%">
        <el-input v-model="checkEntity.remark" type="textarea" placeholder="请输入备注" :rows="4"/>
      </el-form-item>

      <!--      <el-form-item label="图像" prop="image">
              <image-upload v-model="form.image"/>
            </el-form-item>-->
    </el-form>

    <!--    检查项详情-->
    <div class="check-edit-title">检查详情</div>
    <el-table v-loading="loading" :data="detailList" size="mini" border :cell-style="{padding: '0'}">
      <el-table-column fixed label="序号" type="index" width="50"/>
      <el-table-column label="项目名称" align="center" prop="partsTypeName"></el-table-column>
      <el-table-column :label="titles[0]" align="center" prop="defect">
        <template slot-scope="scope">
          <el-autocomplete
            v-model="scope.row.defect"
            :fetch-suggestions="querySearch(scope.row, 'defect')"
            :placeholder="`请输入${titles[0]}`"
          ></el-autocomplete>
        </template>
      </el-table-column>
      <el-table-column :label="titles[1]" align="center" prop="advice">
        <template slot-scope="scope">
          <el-autocomplete
            v-model="scope.row.advice"
            :fetch-suggestions="querySearch(scope.row, 'advice')"
            :placeholder="`请输入${titles[1]}`"
          ></el-autocomplete>
        </template>
      </el-table-column>
      <el-table-column label="图片" align="center" prop="image">
        <template slot-scope="scope">
          <ImageUpload v-model="scope.row.image" :is-show-tip="false" :key="scope.row.partsTypeId"></ImageUpload>
        </template>
      </el-table-column>
    </el-table>


    <!-- 选择桥梁 -->
    <select-bridge ref="select" @update:checkEntity="handleCheckEntityUpdate"/>

  </div>
</template>

<script>

import {listAllParts} from "@/api/patrol/parts";
import ImageUpload from "@/views/patrol/diseases/ImageUpload.vue";
import SelectBridge from "./selectBridge.vue";

export default {
  name: "AssetCheckInsertOrUpdate",
  dicts: ['patrol_inspection_type', 'patrol_inspection_category'],
  components: {ImageUpload, SelectBridge},
  props: {
    checkEntity: {
      type: Object,
      require: true, // 调试时注释
      default: function () {
        return {
          partsType: '1'
        }
      }
    },
  },
  watch: {
    'checkEntity.patrolCheckDetailList': function (val, oldVal){
      if (!val){
        this.$nextTick(()=>{
          this.getParts()
        })
        // this.$nextTick(()=>{
        //   let temp = oldVal.map(item => {
        //     console.log(item)
        //     return {
        //       checkId: '',
        //       partsTypeId: item.partsTypeId,
        //       partsTypeName: item.partsTypeName,
        //       defect: '',
        //       advice: '',
        //       image: '',
        //       options: item.options
        //     }
        //   })
        //   this.detailList = temp
        // })
      }
    }
  },
  data() {
    return {
      loading: false,
      bridgeOpen: false,
      rules: {
        type: [
          {required: true, message: "检查类型不能为空", trigger: "change"}
        ],
        assetId: [
          {required: true, message: "桥梁id不能为空", trigger: "blur"}
        ],
      },
      detailList: [],
      partsType: {
        '1': '桥梁日常巡查',
        '2': '桥梁经常检查',
        '3': '涵洞定期检查',
        '4': '涵洞经常检查',
        '5': '隧道日常巡查',
        '6': '隧道经常检查',
      },
      partsType2AssetName: {
        '1': '31',
        '2': '31',
        '3': '33',
        '4': '33',
        '5': '32',
        '6': '32',
      },
      partsType2ParentName: {
        '1': '桥梁病害',
        '2': '桥梁病害',
        '3': '涵洞病害',
        '4': '涵洞病害',
        '5': '隧道病害',
        '6': '隧道病害',
      },
       partsType2Titles: {
        '1': ['状态描述', '保养措施意见'],
        '2': ['缺损类型', '缺损范围及保养意见'],
        '3': ['缺损类型', '缺损范围及保养意见'],
        '4': ['缺损类型', '缺损范围及保养意见'],
        '5': ['状态描述', '保养措施意见'],
        '6': ['缺损类型', '缺损范围及保养意见'],
      },
    };
  },
  computed: {
    titles() {
      return this.partsType2Titles[this.checkEntity.partsType ?? ''] ?? []
    },
  },
  created() {
    this.getParts()
  },
  methods: {
    /** 选择桥梁 */
    selectBridge() {
      this.$refs.select.show();
    },
    handleCheckEntityUpdate(newValue) {
      // 在这里更新父组件中的值
      this.checkEntity.assetId = newValue.id;
      this.checkEntity.assetName = newValue.assetName;
      this.checkEntity.assetCode = newValue.assetCode;
      this.checkEntity.routeCode = newValue.routeCode;
      this.checkEntity.maintenanceSectionName = newValue.maintenanceSectionName;
      this.checkEntity.centerStake = newValue.centerStake;
      this.checkEntity.maintainUnitName = newValue.managementMaintenanceName;
    },
    querySearch(row, type) {
      if (type === 'defect')
        return function (queryString, cb, options = row.options) {
          if (!options)
            return cb([])
          let tempArray = Object.keys(options)
          tempArray = tempArray.map(item => {
            return {value: item}
          })
          cb(tempArray);
        }
      return function (queryString, cb, options = row.options) {
        if (!options || !row.defect)
          return cb([])
        let tempArray = options[row.defect] ?? []
        tempArray = tempArray.map(item => {
          return {value: item}
        })
        cb(tempArray);
      }
    },
    getParts() {
      this.loading = true
      listAllParts({partsType: this.checkEntity.partsType}).then(res => {
        let partsList = res.data
        let temp = {
          checkId: '',
          partsTypeId: '',
          partsTypeName: '',
          defect: '',
          advice: '',
          image: ''
        }
        this.detailList = partsList.map(item => {
          return {
            checkId: '',
            partsTypeId: item.id,
            partsTypeName: item.partsName,
            defect: '',
            advice: '',
            image: '',
            options: JSON.parse(item.extend)
          }
        })
        this.checkEntity.patrolCheckDetailList = this.detailList
        this.loading = false
      }).catch(() => this.loading = false)
    },
  }
}
</script>
<style lang="scss" scoped>
.check-edit-container {
  padding-left: 20px;
  background: white;
  /* padding: 20px;*/
  .el-form--inline .el-form-item {
    width: 31%;
    margin-right: 2%;
  }

  ::v-deep .el-form-item__content {
    width: calc(100% - 80px);
  }

  ::v-deep .el-form-item__label {
    color: #72767b;
  }

  .el-input,
  .el-autocomplete,
  .el-date-editor,
  .el-select {
    width: 100%;
  }

  ::v-deep .el-table {
    width: 96% !important;
    margin: 10px;
  }

  ::v-deep .el-table .el-input__inner {
    border: none !important;
    padding: 0 !important;
  }

  ::v-deep .el-table td.el-table__cell {
    padding: 0;
    background-color: unset !important;
  }

  ::v-deep .el-upload-list__item {
    width: 42px;
    height: 42px;
    margin: 0px 5px -6px 0 !important
  }

  ::v-deep .el-upload--picture-card {
    width: 42px;
    height: 42px;
    margin: 4px 0;
  }

  ::v-deep .el-icon-plus {
    font-size: 1rem;
  }
}

.check-edit-title {
    font-weight: bold;
    font-size: 1.15rem;
    padding: 10px 0;
  /* color: #333333;*/
  /* color: #72767b;*/
   &:before {
     content: '';
     display: inline-block;
     width: 5px;
     height: 1.5rem;
     vertical-align: bottom;
     margin-right: 0.8rem;
     background: #3797EB;
   }
}

/**
图片上传居中
*/
/*::v-deep .component-upload-image > div:first-child {
justify-content: center;
}*/

/**
input placeholder居中
*/
/*::v-deep table .el-input__inner::-webkit-input-placeholder {
text-align: center;
}
::v-deep table .el-input__inner::-moz-placeholder {
text-align: center;
}
::v-deep table .el-input__inner:-ms-input-placeholder {
text-align: center;
}*/

</style>
