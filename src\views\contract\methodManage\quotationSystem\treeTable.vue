<template>
  <div class="custom-tree-container">
    <div class="block">
      <div class="custom-tree-title">
        <span class="cloumn">子目号</span>
        <span class="cloumn">子目名称</span>
        <span class="cloumn">单位</span>
        <span class="cloumn">单价</span>
        <span class="cloumn">小数位数</span>
        <span class="cloumn">备注</span>
      </div>
      <el-tree
          :data="data"
          node-key="id"
          default-expand-all
          draggable
          :expand-on-click-node="false">
      <span class="custom-tree-node" slot-scope="{ node, data }">
        <span class="cloumn">{{ data.schemeCode }}</span>
        <span class="cloumn">{{ data.schemeName }}</span>
        <span class="cloumn">{{ data.unit }}</span>
        <span class="cloumn">{{ data.price }}</span>
        <span class="cloumn">{{ data.decimalPlaces }}</span>
        <span class="cloumn">{{ data.remark }}</span>
      </span>
      </el-tree>
    </div>
  </div>
</template>

<script>
let id = 1000;

export default {
  props: {
    data: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
    }
  },

  methods: {
  }
};
</script>

<style lang="scss" scoped>
::v-deep .el-tree-node__content {
  height: 35px !important;
  padding-left: 0px!important;
  border-bottom: 1px solid #dfe6ec;
}
.custom-tree-title {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 14px;
  height: 35px;
  font-weight: bold;
  background-color: #f2f3f5;
  padding-left: 24px;
}
.custom-tree-node {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 14px;
  height: 35px;
  padding-right: 8px;
}
.cloumn {
  min-width: 216px;
  height: 35px;
  line-height: 35px;
  padding: 0;
  color: #333333;
  text-align: center;
  border-right: 1px solid #dfe6ec;
}
</style>
<style lang="scss" scoped>
@import "@/assets/styles/business.scss";
</style>
