<template>
  <!-- 授权用户 -->
  <el-dialog title="选择养护子段" :visible.sync="visible" width="800px" top="5vh" append-to-body>
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true">
      <el-form-item label="养护子段名称" prop="routeSegmentsName">
        <el-input
          v-model="queryParams.routeSegmentsName"
          placeholder="请输入养护子段名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>
    <el-row>
      <el-table @row-click="clickRow" @select="selectChange" ref="table" :data="routeSegmentsList"
                @select-all="handleSelectionChange" height="260px">
        <el-table-column type="selection" width="55"></el-table-column>
        <el-table-column label="养护子段ID" align="center" width="120" prop="routeSegmentsId"/>
        <el-table-column label="养护子段名称" align="center" prop="routeSegmentsName"/>
      </el-table>
      <pagination
        v-show="total>0"
        :total="total"
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
        @pagination="getList"
      />
    </el-row>
    <div slot="footer" class="dialog-footer">
      <el-button type="primary" @click="handleSelectUser">确 定</el-button>
      <el-button @click="visible = false">取 消</el-button>
    </div>
  </el-dialog>
</template>

<script>
import {findByDeptSegmentsList, getDefaultSelected} from "@/api/system/routeSegments";
import {addDeptSegments} from "@/api/system/segments";
export default {
  dicts: ['sys_normal_disable'],
  props: {
    deptData: {
      type: Object,
      require: true,
      // default: function () {
      //   return {
      //     partsType: '5'
      //   }
      // }
    },
  },
  data() {
    return {
      // 遮罩层
      visible: false,
      // 选中数组值
      routeSegmentsIds: [],
      // 总条数
      total: 0,
      // 养护子段数据
      routeSegmentsList: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        maintenanceSectionId: undefined,
        routeSegmentsName: undefined,
      },
      //提交参数
      params: {}
    };
  },
  methods: {
    // 显示弹框
    show() {
      this.queryParams.deptId = this.deptData.deptId;
      this.queryParams.maintenanceSectionId = this.deptData.maintenanceSectionId;
      this.getDefault();
      this.getList();
      this.visible = true;
    },
    clickRow(row) {
      this.$refs.table.toggleRowSelection(row);
      this.setChecked(row);
    },
    selectChange(arr, row) {
      //退选和选中处理
      this.setChecked(row);
    },
    //退选和选中处理
    setChecked(row){
      if (this.routeSegmentsIds.includes(row.routeSegmentsId)) {
        this.routeSegmentsIds = this.routeSegmentsIds.filter(i => i !== row.routeSegmentsId);
      } else {
        this.routeSegmentsIds.push(row.routeSegmentsId)
      }
    },

    // 多选框选中数据
    handleSelectionChange(selection) {
      if (selection.length > 0) {
        selection.forEach(item => {
          // this.setChecked(item);
          if (!this.routeSegmentsIds || !this.routeSegmentsIds.includes(item.routeSegmentsId)) {
            this.routeSegmentsIds.push(item.routeSegmentsId)
          }
        })
      } else {
        this.routeSegmentsList.forEach(item => {
          this.routeSegmentsIds = this.routeSegmentsIds.filter(i => i !== item.routeSegmentsId);
        })
      }
    },
    // 查询默认选中数据
    getDefault() {
      getDefaultSelected(this.queryParams).then(data => {
        this.routeSegmentsIds = data;
      });
    },
    // 查询表数据
    getList() {
      // this.queryParams.maintenanceSectionId = null;
      findByDeptSegmentsList(this.queryParams).then(res => {
        this.routeSegmentsList = res.rows;
        this.total = res.total;
        this.setSelected(this.routeSegmentsList);
      });
    },
    //设置默认选中
    setSelected(rows) {
      this.$nextTick(()=> {//渲染后执行
        rows.forEach(row => {
          if (this.routeSegmentsIds.includes(row.routeSegmentsId)) {
            this.$refs.table.toggleRowSelection(row,true);
          }
        });
      })
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    /** 提交选择养护子段操作 */
    handleSelectUser() {
      const routeSegmentsIds = this.routeSegmentsIds.join(",");
      // if (routeSegmentsIds == "") {
      //   this.$modal.msgError("请选择要分配的养护子段");
      //   return;
      // }
      const deptId = this.deptData.deptId;
      this.params.deptId = deptId;
      this.params.routeSegmentsIds = routeSegmentsIds;
      this.params.maintenanceSectionId = this.deptData.maintenanceSectionId;
      addDeptSegments(this.params).then(response => {
        this.$modal.msgSuccess("操作成功");
        this.$parent.handleDeptAsset(deptId);
        this.getList();
        this.visible=false;
      });
    }
  }
};
</script>
