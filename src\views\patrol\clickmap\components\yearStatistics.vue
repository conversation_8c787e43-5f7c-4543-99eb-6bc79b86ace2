<template>
  <div class="yaer-statistics">
    <el-row :gutter="10">
      <el-col :span="10" :offset="0">
        <section class="stati-l">
          <div class="title">集团公司事件统计情况</div>
          <div class="number">
            <span v-for="(item, index) in numArr" :key="index" class="number-item">{{ item }}</span>
            <span>件</span>
          </div>
          <Echarts height="35vh" :option="option" v-if="option" />
        </section>
      </el-col>
      <el-col :span="14" :offset="0">
        <section class="stati-r">
          <!-- 统计 -->
          <div class="inspection-plan">
            <div v-for="(item, index) in staData" :key="'sta' + index" class="list"
              :style="{ borderRight: staData.length < 3 ? '1px dotted rgba(23,116,255,0.8)' : '' }">
              <div class="name">
                <span :style="{ background: item.color }"></span>
                <span>{{ item.name }}</span>
              </div>
              <div class="number">
                <span>{{ item.value }}</span>
                <small>件</small>
              </div>
              <div class="ratio">
                <span>占比</span>
                <span>{{ item.ratio }} <small>%</small></span>
              </div>
            </div>
          </div>
          <div class="statistics-list">
            <div v-for="(item, index) in list" :key="'list' + index" class="list">
              <div class="name">
                <span :style="{ background: item.color }"></span>
                <span>{{ item.name }}</span>
              </div>
              <div class="number">
                <span>{{ item.value }}</span>
                <small>件</small>
              </div>
              <div class="ratio">
                <span>占比</span>
                <span>{{ item.ratio }} <small>%</small></span>
              </div>
              <div class="divider-coumns" v-if="index !== 2 && index !== 5"></div>
              <div class="divider-bottom" v-if="index != list.length - 1"></div>
            </div>
          </div>
        </section>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import { isBigScreen } from "../util/utils";
import Echarts from "./echarts/echarts.vue";

export default {
  components: {
    Echarts,
  },
  data() {
    return {
      option: null,
      bigBool: isBigScreen(),
      num: 37417,
      numArr: [],
      staData: [
        {
          name: '巡查发现',
          value: 22602,
          color: '#3D8FFF',
          ratio: 59.75
        },
        {
          name: '计划上报',
          value: 15224,
          color: '#91C6FF',
          ratio: 40.25
        },
      ],
      list: [
        {
          name: '附属设施',
          value: 1513,
          color: '#0166FE',
          ratio: 4.00
        },
        {
          name: '交通安全设施',
          value: 9714,
          color: '#52FFFF',
          ratio: 25.68
        },
        {
          name: '路基',
          value: 5056,
          color: '#1CFFBC',
          ratio: 13.37
        },
        {
          name: '路面',
          value: 5826,
          color: '#76FF31',
          ratio: 15.40
        },
        {
          name: '绿化',
          value: 2501,
          color: '#FFDD21',
          ratio: 6.61
        },
        {
          name: '桥涵隧',
          value: 8675,
          color: '#FFAE7C',
          ratio: 22.93
        },
        {
          name: '其他',
          value: 4541,
          color: '#FF5B00',
          ratio: 12.00
        },
      ],
    };
  },
  mounted() {
    this.numArr = this.num.toString().split('');
    console.log(this.numArr);
    this.initChart();
  },
  methods: {
    // 初始化图表
    initChart() {
      let colorList = ['#0166FE', '#52FFFF', '#1CFFBC', '#76FF31', '#FFDD21', '#FFAE7C', '#FF5B00'];
      let colorList1 = ['#3D8FFF', '#91C6FF', '#1CFFBC', '#76FF31', '#FFDD21', '#FFAE7C', '#FF5B00'];
      let dataArr = this.list;
      let data1 = this.staData;
      this.option = {
        tooltip: {
          trigger: 'item',
        },
        legend: {
          show: false,
        },
        grid: {
          top: "15%",
          bottom: "10%",
          right: "2%",
          left: "10%",
        },
        series: [
          {
            type: 'pie',
            center: ['50%', '50%'],
            radius: ['35%', '40%'],
            clockwise: true,
            avoidLabelOverlap: true,
            hoverOffset: 15,
            itemStyle: {
              normal: {
                color: function (params) {
                  return colorList1[params.dataIndex];
                },
              },
            },
            label: {
              show: false,
            },
            data: data1,
          },
          {
            type: 'pie',
            center: ['50%', '50%'],
            radius: ['70%', '80%'],
            clockwise: true,
            avoidLabelOverlap: true,
            hoverOffset: 15,
            itemStyle: {
              normal: {
                color: function (params) {
                  return colorList[params.dataIndex];
                },
              },
            },
            label: {
              show: false,
            },
            data: dataArr,
          },
        ],
      };
    },
  }
}
</script>

<style lang="scss" scoped>
@import "@/assets/styles/utils.scss";

.yaer-statistics {
  padding: vwpx(20px);
  height: 100%;

  .stati-l {
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    margin-top: vwpx(100px);

    .title {
      font-family: Source Han Sans, Source Han Sans;
      font-weight: 500;
      font-size: vwpx(44px);
      color: #FFFFFF;
      text-align: left;
      font-style: normal;
      text-transform: none;
    }

    .number {
      width: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
      flex-direction: row;
      margin-top: vwpx(40px);

      span:last-child {
        font-family: Source Han Sans, Source Han Sans;
        font-weight: 400;
        font-size: vwpx(36px);
        color: rgba(255, 255, 255, 0.8);
        text-align: left;
        font-style: normal;
        text-transform: none;
      }

      .number-item {
        margin: 0 vwpx(10px);
        display: block;
        width: vwpx(90px);
        height: vwpx(116px);
        line-height: vwpx(116px);
        background: rgba(4, 17, 48, 0.4);
        box-shadow: inset 0px 0px 6px 0px #0154FB;
        border-radius: 0px 0px 0px 0px;
        border: 1px solid #009DFF;

        font-family: HarmonyOS Sans SC, HarmonyOS Sans SC;
        font-weight: 500;
        font-size: vwpx(64px);
        color: #00FDFD;
        text-shadow: 0px 0px 10px rgba(27, 126, 242, 0.8);
        text-align: center;
        font-style: normal;
        text-transform: none;
      }
    }
  }

  .stati-r {
    .inspection-plan {
      width: 100%;
      height: vwpx(260px);
      padding: vwpx(30px) 0;
      background: rgba(0, 0, 0, 0.1);
      box-shadow: inset 0px 0px 10px 0px #0065FF;
      border-radius: 6px 6px 6px 6px;
      border: 1px solid #20A9FF;
      margin-bottom: vwpx(30px);

      display: flex;
      align-items: center;
    }

    .statistics-list {
      width: 100%;
      height: vwpx(820px);
      padding: 0 vwpx(10px) 0 vwpx(15px);
      background: rgba(0, 0, 0, 0.1);
      box-shadow: inset 0px 0px 10px 0px #0065FF;
      border-radius: 6px 6px 6px 6px;
      border: 1px solid #20A9FF;
      padding-top: vwpx(30px);

      display: flex;
      align-items: center;
      flex-wrap: wrap;
    }

    .list {
      width: 33%;
      position: relative;

      display: flex;
      align-items: center;
      justify-content: center;
      flex-direction: column;

      .name {
        display: flex;
        align-items: center;

        span:first-child {
          display: block;
          width: vwpx(24px);
          height: vwpx(24px);
        }

        span:last-child {
          font-family: Source Han Sans, Source Han Sans;
          font-weight: 500;
          font-size: vwpx(30px);
          color: #FFFFFF;
          line-height: 20px;
          text-align: center;
          font-style: normal;
          text-transform: none;
          margin-left: vwpx(20px);
        }
      }

      .number {
        font-family: HarmonyOS Sans SC, HarmonyOS Sans SC;
        font-weight: 500;
        font-size: vwpx(44px);
        color: #42ABFF;
        line-height: vwpx(80px);
        text-align: center;
        font-style: normal;
        text-transform: none;

        small {
          font-family: Source Han Sans, Source Han Sans;
          font-weight: 400;
          font-size: vwpx(26px);
          color: rgba(255, 255, 255, 0.8);
          text-shadow: 0px 0px 10px rgba(27, 126, 242, 0.8);
          text-align: center;
          font-style: normal;
          text-transform: none;
          margin-left: vwpx(10px);
        }
      }

      .ratio {
        span:first-child {
          font-weight: 400;
          font-size: vwpx(24px);
          color: rgba(255, 255, 255, 0.8);
          text-shadow: 0px 0px 10px rgba(27, 126, 242, 0.8);
        }

        span:last-child {
          font-weight: 500;
          font-size: vwpx(36px);
          color: #FFFFFF;
          text-shadow: 0px 0px 10px rgba(27, 126, 242, 0.8);
          margin-left: vwpx(10px);

          small {
            font-weight: 400;
            font-size: vwpx(26px);
            color: rgba(255, 255, 255, 0.8);
            text-shadow: 0px 0px 10px rgba(27, 126, 242, 0.8);
          }
        }
      }

      .divider-coumns {
        height: vwpx(180px);
        border-right: 1px dotted rgba(23, 116, 255, 0.8);
        position: absolute;
        right: 0;
        top: vwpx(5px);
      }

      .divider-bottom {
        width: 100%;
        height: 1px;
        background-color: rgba(156, 189, 255, 0.5);
        margin-top: vwpx(45px);
      }
    }
  }
}
</style>