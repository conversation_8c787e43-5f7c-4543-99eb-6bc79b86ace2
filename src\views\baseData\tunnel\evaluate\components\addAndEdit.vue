<template>
  <div class="roadbedadd">
    <el-dialog
      :title="title"
      :visible.sync="showAddEdit"
      width="60%"
      append-to-body
      :before-close="handleClose"
      :close-on-click-modal="false"
      :class="'roadbedDialog'"
    >
          <div style="height: 60vh;overflow-y: auto;padding: 0 10px 0 5px;">
            <div class="check-edit-title">基础信息</div>
            <el-button @click="getTunnel" type="primary" size="mini" style="margin: 0 35px" v-if="title=='查看隧道评定数据'?false:true">
              选择隧道
            </el-button>
            <el-form
              ref="baseInfoData"
              :model="baseInfoForm"
               label-width="150px" size="small" style="margin: 10px"

              :disabled="title=='查看隧道评定数据'?true:false"
            >
              <div style="display: flex; flex-wrap: wrap">
                <el-col
                  :span="i.span || 12"
                  v-for="(i,idx) in baseInfoData"
                  :key="idx"
                >
                  <el-form-item
                    :label="i.label"
                    :prop="i.prop"
                    :rules="i.rules"
                  >
                    <span v-if="i.type === 'input'" >
                      <el-input
                        v-model="baseInfoForm[i.prop]"
                        :disabled="i.disabled?i.disabled:false"
                        :placeholder="i.placeholder"
                        clearable

                      />
                    </span>

                    <span v-if="i.type === 'pileInput'">
                      <PileInput
                        v-model="baseInfoForm[i.prop]"
                        :placeholder="i.placeholder"
                      />
                    </span>
                    <span v-else-if="i.type === 'inputNumber'">
                      <el-input-number
                        style="width: 100%"
                        v-model="baseInfoForm[i.prop]"

                        :precision="i.precision"
                        @change="changeInputNumber(i.precision,'baseInfoForm',i.prop)"
                        clearable
                      ></el-input-number>
                    </span>
                    <span v-else-if="i.type === 'select'">
                      <div v-if="i.dict">
                        <el-select
                          style="width: 100%"
                          v-model="baseInfoForm[i.prop]"
                          placeholder="请选择"
                          :disabled="i.disabled?i.disabled:false"
                          clearable
                          @change="changeSelect($event, i)"
                        >
                          <el-option
                            v-for="dict in dict.type[i.dict]"
                            :key="dict.value"
                            :label="dict.label"
                            :value="dict.value"
                          >
                          </el-option>
                        </el-select>

                      </div>
                      <div v-else-if="i.deptType">
                        <SelectTree  v-model="baseInfoForm[i.prop]" :dept-type="i.deptType"  :disabled="i.disabled?i.disabled:false"  clearable />
                      </div>
                      <div v-else>
                        <el-select
                          style="width:100%"
                          v-model="baseInfoForm[i.prop]"
                          :placeholder="i.placeholder"
                          clearable
                          filterable
                          :disabled="(baseInfoForm&&baseInfoForm.id)?false:i.disabled"

                        >
                          <el-option
                            v-for="(v,index) in i.options"
                            :key="index"
                            :label="v[i.optionLabel]"
                            :value="v[i.optionValue]"
                          />
                        </el-select>
                      </div>
                    </span>
                    <span v-else-if="i.type === 'date'">
                      <el-date-picker
                        style="width:100%"
                        v-model="baseInfoForm[i.prop]"
                        type="year"
                        :placeholder="i.placeholder"
                        clearable
                        value-format="yyyy"
                      />
                    </span>
                    <span v-else-if="i.type === 'inputTextarea'">
                     <el-input
                       v-model="baseInfoForm[i.prop]"
                       autosize
                        :placeholder="i.placeholder"
                       type="textarea"
                       clearable/>
                    </span>
                    <span v-else-if="i.type === 'year'">
                      <el-date-picker
                        style="width:100%"
                        v-model="baseInfoForm[i.prop]"
                        :type="'year'"
                        :placeholder="i.placeholder"
                        clearable
                        value-format="yyyy"
                      />
                    </span>
                    <span v-else-if="i.type === 'inputUser'">
                      <el-cascader
                        ref="userCascade"
                        v-model="oprUser"
                        :options="deptUserOptions"
                        :props="{ multiple: true, value: 'id', emitPath: false}"
                        :show-all-levels="false"
                        filterable clearable
                        style="width: 100%"/>
                    </span>
                    <span v-else-if="i.type === 'updataFile'">
                      <FileUpload
                        :key="ownerId"
                        v-model="form.reportPath"
                        :limit="1"
                        :owner-id="ownerId"
                        storage-path="/base/tunnel/periodic/"
                        platform="mpkj"
                      />
                    </span>
                  </el-form-item>
                </el-col>
              </div>
            </el-form>
          </div>
      <div
        slot="footer"
        class="dialog-footer"
      >
        <el-button
          v-if="title=='查看隧道评定结果数据'?false:true"
          type="primary"
          @click="handleSubmit('submit')"
        >提 交</el-button>
        <el-button @click="handleClose">取 消</el-button>
      </div>
    </el-dialog>
    <el-dialog
      title="选择隧道"
      :visible.sync="showTunnelSelect"
      width="80%"
      class="chose-brige"
      append-to-body
      :before-close="handleTunnelClose"
      :close-on-click-modal="false"
    >
    <div style="height: 70vh;">
    <div style="display: flex; align-items: center; margin: 0">
        <el-col :span="12">
          <CascadeSelection
            style="min-width:192px;"
            :form-data="queryParams"
            v-model="queryParams"
            types="201"
            size="mini"
            multiple
          />
        </el-col>
        <div style="min-width:210px; margin-right:20px">
          <el-input
            v-model="queryParams.tunnelName"
            placeholder="隧道名称"
            clearable
            size="mini"
          />
        </div>
        <div style="min-width:220px">
          <el-button
            type="primary"
            icon="el-icon-search"
            size="mini"
            @click="handleQuery"
          >搜索</el-button>
          <el-button
            style="background:#F2F3F5;border:1px #F2F3F5 solid"
            icon="el-icon-refresh"
            size="mini"
            @click="resetQuery"
          >重置</el-button>
        </div>

      </div>
      <el-divider></el-divider>
      <el-table
        ref="table"
        v-loading="loading"
        height="80%"
        style="width: 100%"
        :data="staticList"
        :header-cell-style="{'height': '36px'}"
        :row-style="rowStyle"
        @row-click="handleRowClick"
      >

        <el-table-column
          fixed
          label="序号"
          type="index"
          width="50"
          align="center"
        />
        <el-table-column
          fixed
          label="隧道名称"
          align="center"
          prop="tunnelName"
          min-width="130"
          show-overflow-tooltip
        />
        <el-table-column
          label="隧道编码"
          align="center"
          prop="tunnelCode"
          min-width="130"
          show-overflow-tooltip
        />
        <el-table-column
          label="养护路段"
          align="center"
          prop="maintenanceSectionName"
          min-width="130"
          show-overflow-tooltip
        />
        <el-table-column
          label="路线编码"
          align="center"
          prop="routeCode"
          min-width="130"
          show-overflow-tooltip
        />
        <el-table-column
          label="暂存状态"
          align="center"
          prop="status"
          min-width="130"
        >
          <template slot-scope="scope">
            {{ scope.row.status == 1 ? '暂存' : '正常' }}
          </template>
        </el-table-column>
        <el-table-column
          label="管理处"
          align="center"
          prop="managementMaintenanceName"
          min-width="130"
          show-overflow-tooltip
        />
        <el-table-column
          label="管养分处"
          align="center"
          prop="managementMaintenanceBranchName"
          min-width="130"
          show-overflow-tooltip
        />
        <el-table-column
          label="结构形式"
          align="center"
          prop="mainSuperstructureTypeName"
          min-width="130"
          show-overflow-tooltip
        />
        <el-table-column
          label="桥位桩号"
          align="center"
          prop="centerStake"
          min-width="130"
          show-overflow-tooltip
        >
          <template slot-scope="scope">
            {{ formatPile(scope.row.centerStake) }}
          </template>
        </el-table-column>
        <el-table-column
          label="桥长"
          align="center"
          prop="totalLength"
          min-width="130"
          show-overflow-tooltip
        />
        <el-table-column
          label="隧道评定等级"
          align="center"
          prop="tunnelTechAssessTypeName"
          min-width="130"
          show-overflow-tooltip
        />
        <el-table-column
          label="跨径分类"
          align="center"
          prop="spanClassifyTypeName"
          min-width="130"
          show-overflow-tooltip
        />
        <el-table-column
          label="跨径组合"
          align="center"
          prop="spanGroups"
          min-width="130"
          show-overflow-tooltip
        />
        <el-table-column
          label="固定编码"
          align="center"
          prop="fixedCode"
          min-width="130"
          show-overflow-tooltip
        />
        <el-table-column
          label="是否是独柱墩"
          align="center"
          prop="whetherSingleColumnPierTypeName"
          min-width="130"
          show-overflow-tooltip
        />

        <el-table-column
          label="是否锁定"
          align="center"
          prop="isLocked"
          min-width="130"
          show-overflow-tooltip
        >
          <template slot-scope="scope">
            {{ scope.row.isLocked ? '是' : '否' }}
          </template>
        </el-table-column>
        <el-table-column
          label="是否长大隧道目录"
          align="center"
          prop="whetherInLongSpanCatalogTypeName"
          min-width="150"
          show-overflow-tooltip
        />
        <el-table-column
          label="建成时间"
          align="center"
          prop="buildDate"
          min-width="130"
          show-overflow-tooltip
        />

      </el-table>
      <pagination
        :total="total"
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
        :pageSizes="[10, 20, 30, 50, 100, 1000]"
        @pagination="getList"
      />
    </div>
    </el-dialog>
  </div>
</template>

<script>
import dictionary from '../dataDictionary'
import { addTunnelEvaluateBridge,updateTunnelEvaluateBridge} from '@/api/baseData/tunnel/evaluate/index'


import {
  maintenanceSectionList,
} from '@/api/baseData/subgrade/baseInfo/getSections'

import { listMaintenanceSectionAll } from "@/api/system/maintenanceSection";
import CascadeSelection from '@/components/CascadeSelection/index.vue'
import { createIdWorker } from '@/api/baseData/common'
import { listAllHighwaySections } from '@/api/system/highwaySections.js'
import SelectTree from '@/components/DeptTmpl/selectTree'
import SectionSelect from '@/components/SectionSelect'
import { listAllRoute } from '@/api/system/route.js'
import "@riophae/vue-treeselect/dist/vue-treeselect.css";
import Treeselect from "@riophae/vue-treeselect";

import {
  listStatic,
  getStatic
} from '@/api/baseData/tunnel/baseInfo/index'
import {getTreeStruct} from "@/api/tmpl";
import PileInput from '@/components/PileInput/index.vue'
import { listByMaintenanceSectionId } from "@/api/baseData/common/routeLine";

const apis = {
  listAllHighwaySections: listAllHighwaySections,
  listAllRoute: listAllRoute,
  maintenanceSectionList: maintenanceSectionList,
  listMaintenanceSectionAll:listMaintenanceSectionAll,
  listByMaintenanceSectionId:listByMaintenanceSectionId
}

export default {
  name: 'subgrade-baseInfo-addAndEdit',
  components: {SelectTree,Treeselect,SectionSelect,PileInput,CascadeSelection},
  props: {
    formData: {
      default: {}
    },
    showAddEdit: {
      default: false
    },
    title: {
      default: '添加隧道评定结果静态数据'
    },
    forView: {
      default: false
    }
  },
  dicts: [

  ],
  data() {
    return {
      activeName: 'baseInfoData',
      sectionId:'',
      baseInfoData: [],
      specialExamData:[],
      deptUserOptions: [],

      queryParams: {
        pageNum: 1,
        pageSize: 20
      },
      ids: [],
      baseInfoForm: {},
      showTunnelSelect:false,
      staticList: [],
      total: 0,
      form: {},
      loading: false,
      types: 101,
      ownerId: undefined
    }
  },
  created() {
    this.baseInfoForm = this.formData.baseInfoForm?JSON.parse(JSON.stringify(this.formData.baseInfoForm)):{}


    createIdWorker().then(res => {
        if (res.code === 200) {
          this.ownerId = Number(res.data)
        }
      })

    getTreeStruct({types: 111,deptTypeList:null}).then(response => {
      this.deptUserOptions = response.data;
    });
  },
  mounted() {
    this.initFormSlections()
  },
  methods: {


    // 获取表格数据
    getList() {
      this.loading = true
      listStatic(this.queryParams).then(response => {
        this.staticList = response.rows
        this.total = response.total
        this.loading = false
      })
    },
    // 搜索按钮
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    // 重置按钮
    resetQuery() {
      this.queryParams = {
        pageNum: 1,
        pageSize: 20
      }
      this.handleQuery()
    },

    // 获取表单字段以及表单选项列表
    initFormSlections() {
      // 行政识别数据

      this.$nextTick(()=>{
        this.baseInfoData = JSON.parse(JSON.stringify(dictionary.baseInfoData));
        this.baseInfoData.map(async (el,i) => {
        if (el.api&&el.type=='select') {
          const r = await apis[el.api](
                [this.baseInfoForm.managementMaintenanceId] || ['']
              )
            // 临时编码，返回的数据不合规范，数组去重
            if (r.row||r.data) {
              r.list = Array.from(new Set((r.row||r.data).map(JSON.stringify))).map(
                JSON.parse
              )
            }
            el.options = r.list

          }
      })

      })
      // 结构技术数据
    },

    changeSelect(e, i) {
      this.$forceUpdate()
      if(i.prop=='sectionType'){
        const item = this.baseInfoData.find(obj => obj.prop === 'sectionType');
        item.disabled=false;
      }
    },
    getTunnel(e, i) {


      this.showTunnelSelect=true
      this.getList()

    },
    async handleSubmit(type) {
      const formNames = [
        'baseInfoData',
      ]
      switch (type) {
        case 'submit':
          this.form.isStaging = false
          break
        case 'save':
          this.form.isStaging = true
          break
      }

      if (!this.form.isStaging) {
        for (let index = 0; index < formNames.length; index++) {
          const element = formNames[index]
          const valid = await new Promise(resolve => {
            this.$refs[element].validate(valid => {
              resolve(valid)
            })
          })
          if (!valid) {
            // 如果表单校验不通过，定位到对应的tab
            this.activeName = element
            return // 中止提交操作
          }
        }
      }
      let listBase=JSON.parse(JSON.stringify(this.baseInfoForm))


      this.baseInfoData.map(el => {
        if(el.type=='pileInput'&&el.precision&&this.baseInfoForm[el.prop]){

          listBase[el.prop]=(this.baseInfoForm?.[el.prop]?.toFixed(el.precision)).toString()
        }

      })
      listBase.items=this.setItemList

      if(this.form.isStaging){

      }else{
        if (this.baseInfoForm.id != null) {
          updateTunnelEvaluateBridge(listBase).then(response => {
            this.$modal.msgSuccess(
              this.form.isStaging == true ? '暂存成功' : '新增成功'
            )
            this.$emit('refresh')
            this.initFormSlections()
          })
        } else {
          addTunnelEvaluateBridge(listBase).then(response => {
            this.$modal.msgSuccess(
              this.form.isStaging == true ? '暂存成功' : '新增成功'
            )
            this.$emit('refresh')
            this.initFormSlections()
          })
        }
      }
    },

    // 勾选高亮
    rowStyle({ row, rowIndex }) {
      if (this.ids.includes(row.id)) {
        return { 'background-color': '#EFF5FF' }
      } else {
        return { 'background-color': '#fff' }
      }
    },

    changeInputNumber(precision,info,val) {
      let num=parseFloat(this[info][val]).toFixed(precision)
      this.$nextTick(() => {
        this[info][val] = num
      })
    },

    // 表格点击勾选
    handleRowClick(row) {
      row.isSelected = !row.isSelected
      this.$refs.table.toggleRowSelection(row)
      this.ids.push(row.id)

      this.showTunnelSelect=false
      //将this.baseInfodata中得prop变成数组
      let propArr=this.baseInfoData.map(el=>el.prop)
      getStatic(row.id).then(res => {
            if (res && res.data) {
              this.$nextTick(() => {
                this.$set(this.baseInfoForm, 'tunnelId',res.data.id)

                for (let key in res.data) {
                if (propArr.includes(key)) {
                  if(key === 'remark' && res.data[key]){
                    this.$set(this.baseInfoForm, key, res.data[key]);
                  }else if(key !== 'remark') {
                    this.$set(this.baseInfoForm, key, res.data[key]);
                  }
                }
              }
            })
            }
          })
    },

    handleTunnelClose() {
      this.showTunnelSelect=false
    },
    handleClose() {
      if (this.forView) {
        this.form = {}
        this.$emit('close', false)
      } else {
        this.$modal
          .confirm('确认退出？')
          .then(() => {
            this.form = {}
            this.$emit('close', false)
            this.initFormSlections()
          })
          .catch(() => {})
      }
    },

  },
  watch: {

  },
  computed: {
    oprUser: {
      set() {
        if(this.$refs.userCascade){
          let userList = this.$refs.userCascade[0].getCheckedNodes()
          this.baseInfoForm.recorder = userList?.map(item => item.value).join(',')
          this.baseInfoForm.recorderName = userList?.map(item => item.label).join(',')
        }

      },
      get() {
        // let idListList = this.checkEntity.oprUserId.split(',')
        return this.baseInfoForm?.recorder?.split(',') ?? null
      }
    }
  }
}
</script>

<style  scoped>
.roadbedDialog ::v-deep .el-input.is-disabled .el-input__inner{
  background-color: white;
  border-color: #dfe4ed;
  color: black;
}

.check-edit-title {
  font-weight: bold;
  font-size: 1.15rem;
  margin-left: 20px;
  padding: 10px 0;
  /*  color: #333333;*/
  /*  color: #72767b;*/
  &:before {
    content: '';
    display: inline-block;
    width: 5px;
    height: 1.5rem;
    vertical-align: bottom;
    margin-right: 0.8rem;
    background: #3797EB;
  }
}

</style>
