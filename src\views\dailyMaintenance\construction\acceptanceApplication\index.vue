<template>
  <div class="app-container maindiv">
    <registration-list ref="regis" :page-list="pageList" :dis-stage="3">
      <template #btn>
        <el-col :span="1.5">
          <el-button
              icon="el-icon-download"
              size="mini"
              type="warning"
              v-has-menu-permi="['checkApply:construction:export']"
              @click="exportList"
          >导出清单
          </el-button
          >
        </el-col>
      </template>
      <template #operate="scope" >
        <el-button
          size="mini"
          type="text"
          icon="el-icon-edit"
          v-has-menu-permi="['checkApply:construction:edit']"
          @click="handleRegis (scope.scope.row)"
        >修改
        </el-button>
        <el-button
          size="mini"
          type="text"
          icon="el-icon-check"
          v-has-menu-permi="['checkApply:construction:review']"
          @click="handlePass (scope.scope.row)"
        >申请
        </el-button>
        <el-button
          size="mini"
          type="text"
          icon="el-icon-close"
          v-has-menu-permi="['checkApply:construction:reject']"
          @click="handleReject(scope.scope.row)"
        >撤回
        </el-button>
      </template>
    </registration-list>
    <el-drawer append-to-body modal-append-to-body :wrapperClosable="false" :title="drawerTitle" destroy-on-close :visible.sync="drawer" size="70%">
      <detail @close="handleCloseDetail" :row-data="rowData"></detail>
    </el-drawer>
  </div>
</template>
<script>
import registrationList from '../component/registrationList.vue'
import {pageList, submitReview, rejectToRegister} from "@/api/dailyMaintenance/construction/acceptanceApplication";
import Detail from "./detail.vue";
export default {
  name: "AcceptanceApplication",
  components: {Detail, registrationList},
  data() {
    return {
      drawerTitle: '验收申请',
      drawer: false,
      rowData: {}
    }
  },
  methods: {
    pageList,
    handleRegis(rows) {
      this.rowData = rows
      this.drawer = true
    },
    // 暂不处理
    handlePass(rows) {
      this.$prompt('', '是否确认通过', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        inputPattern: /^.+$/,
        inputErrorMessage: '请填写审核意见'
      }).then(({value}) => {
        rows.remark = value
        submitReview(rows).then(res => {
          this.$message.success('通过成功')
          this.$refs.regis.handleQuery()
        })
      })
    },
    handleReject(rows) {
      this.$prompt('', '是否确认驳回', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        inputPattern: /^.+$/,
        inputErrorMessage: '请填写审核意见'
      }).then(({value}) => {
        rows.remark = value
        rejectToRegister(rows).then(res => {
          this.$message.success('驳回成功')
          this.$refs.regis.handleQuery()
        })
      })
    },
    handleCloseDetail() {
      this.rowData = {}
      this.drawer = false
      this.$refs.regis.handleQuery()
    },
    // 导出清单按钮
    exportList() {
      this.$refs.regis.queryParams.year = this.$refs.regis.queryParams.yearStr ? parseInt(this.$refs.regis.queryParams.yearStr) : null

      this.download(
          'manager/checkApply/export',
          {...this.$refs.regis.queryParams},
          `checkApply_${new Date().getTime()}.xlsx`,
          {
            headers: {'Content-Type': 'application/json;'},
            parameterType: 'body'
          }
      )
    },
  }
}
</script>
