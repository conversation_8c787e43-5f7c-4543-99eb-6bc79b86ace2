<template>
	<el-form style="padding: 15px" ref="form" :model="form" :rules="rules" label-width="120px">
		<!-- :class="readonly ? 'forView' : ''" -->
		<div v-if="activeName === 'first'">
			<el-form-item label="预警级别" prop="level">
				<el-select
					:disabled="readonly"
					v-model="form.level"
					placeholder="请选择预警级别"
					style="width: 100%"
				>
					<el-option
						v-for="item in dict.type.map_weather_warning_Level"
						:key="item.value"
						:label="item.label"
						:value="item.value"
					></el-option>
				</el-select>
			</el-form-item>
			<el-form-item label="气象类型" prop="type">
				<el-select
					:disabled="readonly"
					v-model="form.type"
					placeholder="请选择气象类型"
					style="width: 100%"
				>
					<el-option
						v-for="item in dict.type.map_weather_warning_type"
						:key="item.value"
						:label="item.label"
						:value="item.value"
					></el-option>
				</el-select>
			</el-form-item>
			<el-form-item label="短信模版类型" prop="templateId	">
				<el-select
					v-model="form.templateId"
					placeholder="请选择短信模版类型"
					v-loading="optionLoading"
					style="width: 100%"
				>
					<el-option
						v-for="item in templateOptions"
						:key="item.id"
						:label="item.name"
						:value="item.id"
					></el-option>
				</el-select>
			</el-form-item>
			<el-form-item label="状态" prop="status">
				<el-radio v-for="item in statusOption" v-model="form.status" :label="item.value">
					{{ item.label }}
				</el-radio>
			</el-form-item>
			<el-form-item label="角色" prop="roleIds">
				<el-select
					v-model="form.roleIds"
					multiple
					placeholder="请选择角色"
					filterable
					style="width: 100%"
				>
					<el-option
						v-for="item in roleOptions"
						:key="item.roleId"
						:label="item.roleName"
						:value="item.roleId"
						:disabled="item.status == 1"
					></el-option>
				</el-select>
			</el-form-item>
			<el-form-item label="备注" prop="remark">
				<el-input type="textarea" v-model="form.remark" placeholder="请输入备注" />
			</el-form-item>
		</div>
		<div v-else>
			<el-form-item label="短信模版类型" prop="name">
				<el-input :disabled="readonly" v-model="form.name" placeholder="请输入短信模版类型" />
			</el-form-item>
			<!-- <el-form-item label="状态" prop="status">
				<el-radio
					:disabled="readonly"
					v-for="item in statusOption"
					v-model="form.status"
					:label="item.value"
				>
					{{ item.label }}
				</el-radio>
			</el-form-item> -->
			<el-form-item label="短信内容" prop="content">
				<el-input
					:disabled="readonly"
					type="textarea"
					:rows="6"
					v-model="form.content"
					placeholder="请输入短信内容"
				/>
			</el-form-item>
		</div>
		<div style="display: flex; justify-content: center; margin: 20px 10px 0 10px">
			<el-button
				v-if="!(activeName === 'second' && readonly)"
				type="primary"
				@click="onSubmit"
				:loading="loading"
			>
				确定
			</el-button>
			<el-button @click="onCancel">取消</el-button>
		</div>
	</el-form>
</template>

<script>
import { add, edit, getMsgList, addMsg, editMsg } from '@/api/system/weatherSetting'

export default {
	name: 'WeatherSettingForm',
	dicts: ['map_weather_warning_type', 'map_weather_warning_Level'],
	components: {},
	props: {
		data: {
			type: Object,
			default: () => {},
		},
		readonly: {
			type: Boolean,
			default: false,
		},
		activeName: {
			type: String,
			default: '',
		},
		statusOption: {
			type: Array,
			default: () => [],
		},
		roleOptions: {
			type: Array,
			default: () => [],
		},
	},
	data() {
		return {
			form: {},
			loading: false,
			optionLoading: false,
			rules: {},
			templateOptions: [],
		}
	},
	created() {
		if (this.data && this.data.id) {
			this.form = JSON.parse(JSON.stringify(this.data))
			if (this.activeName === 'first') this.form.roleIds = this.form.roleIds?.map(Number)
		}
		if (this.activeName === 'first') {
			this.getTemplateOptions()
			this.rules.level = [{ required: true, message: '请输入预警级别', trigger: 'change' }]
			this.rules.type = [{ required: true, message: '请选择气象类型', trigger: 'change' }]
			this.rules.status = [{ required: true, message: '请选择状态', trigger: 'change' }]
			this.rules.templateId = [{ required: true, message: '请选择短信模版类型', trigger: 'change' }]
		} else {
			this.rules.name = [{ required: true, message: '请输入短信模版类型', trigger: 'blur' }]
			this.rules.content = [{ required: true, message: '请输入短信内容', trigger: 'blur' }]
		}
	},
	watch: {},
	methods: {
		getTemplateOptions() {
			this.optionLoading = true
			getMsgList({})
				.then((res) => {
					if (res.code === 200) {
						this.templateOptions = res.data || []
					}
				})
				.finally(() => {
					this.optionLoading = false
				})
		},
		onSubmit() {
			let api = this.form.id
				? this.activeName === 'first'
					? edit
					: editMsg
				: this.activeName === 'first'
				? add
				: addMsg
			this.loading = true
			this.$refs.form.validate((valid) => {
				if (!valid) return
				api(this.form)
					.then((response) => {
						if (response.code === 200) {
							this.$message.success(this.form.id ? '修改成功' : '新增成功')
							this.$emit('onCancel', true)
						}
					})
					.finally(() => {
						this.loading = false
					})
			})
		},
		onCancel() {
			this.$emit('onCancel', false)
		},
	},
}
</script>

<style lang="scss" scoped>
.forView ::v-deep .el-input.is-disabled .el-input__inner,
.forView ::v-deep .el-textarea.is-disabled .el-textarea__inner {
	background-color: white;
	border-color: #dfe4ed;
	color: black;
}

::v-deep .el-radio__original {
	display: none !important; /* 隐藏原生 radio 输入，但仍然允许交互 */
}

::v-deep.el-radio:focus:not(.is-focus):not(:active):not(.is-disabled) .el-radio__inner {
	box-shadow: none !important;
}
</style>
