<template>
  <el-dialog
    title="选择项目"
    width="80%"
    append-to-body
    :visible.sync="showProjectSelect"
    :before-close="handleClose"
    :close-on-click-modal="true"
  >
    <div style="height: 70vh">
      <template slot="search">
        <div style="display: flex; align-items: center; margin: 0">
          <el-input
            style="width: 200px; margin-right: 15px"
            v-model="queryParams.project"
            clearable
            placeholder="请输入项目名称"
          />
          <el-date-picker
            style="width: 200px; margin-right: 15px"
            v-model="queryParams.year"
            type="year"
            placeholder="请选择年度"
            clearable
            value-format="yyyy"
          />

          <div style="min-width: 220px; height: 32px">
            <el-button type="primary" icon="el-icon-search" @click="handleQuery"
              >搜索</el-button
            >
            <el-button icon="el-icon-refresh" @click="resetQuery"
              >重置</el-button
            >
          </div>
        </div>
      </template>
      <el-table
        ref="table"
        v-loading="loading"
        height="80%"
        border
        :data="tableData"
        :cell-style="{ height: '36px' }"
        @row-click="handleRowClick"
      >
        <el-table-column
          label="序号"
          type="index"
          width="50"
          align="center"
        />
        <el-table-column prop="project" label="项目名称" align="center" />
        <el-table-column prop="year" label="年度" align="center" />
        <el-table-column prop="remark" label="备注" align="center" />
      </el-table>
      <pagination
        :total="total"
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
        :pageSizes="[10, 20, 30, 50, 100, 1000]"
        @pagination="getList"
      />
    </div>
  </el-dialog>
</template>
  
  <script>
import { getListPage } from "@/api/baseData/roadbed/assessment/index.js";
export default {
  name: "project-select",
  props: {
    showProjectSelect: { type: Boolean, default: false },
  },
  data() {
    return {
      queryParams: {
        pageNum: 1,
        pageSize: 20,
      },
      total: 0,
      selection: [],
      tableData: [],
      clearData: false,
      loading: true,
    };
  },
  created() {},
  mounted() {
    this.getList();
  },
  methods: {
    // 获取表格数据
    getList() {
      this.loading = true;
      getListPage(this.queryParams)
        .then((response) => {
          this.tableData = response.rows;
          this.total = response.total;
          this.loading = false;
          this.clearData = false;
        })
        .catch(() => {
          this.loading = false;
        });
    },
    // 搜索按钮
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    // 重置按钮
    resetQuery() {
      this.clearData = true;
      this.queryParams = {
        pageNum: 1,
        pageSize: 20,
      };
      this.handleQuery();
    },
    // 表格点击勾选
    handleRowClick(row) {
      this.$emit("projectClick", row);
    },
    handleClose() {
      this.$emit("close");
    },
  },
};
</script>
  
 