<template>
  <div class="pie-charts" v-loading="loading" element-loading-background="rgba(0, 0, 0, 0.4)">
    <Echarts :option="option" v-if="option" height="100%" :key="'chart' + type + year + eKey" />
  </div>
</template>

<script>
import Echarts from '../../echarts/echarts.vue';
import { isBigScreen } from "@/views/cockpit/util/utils";
// api
import { getDiseaseEventDealType, getDiseaseEventTypeRatio, getDiseaseEventCost, getMaintenanceProjectType,getProjectTypeFundStatistics } from '@/api/cockpit/maintain';

export default {
  name: 'PieECharts',
  components: {
    Echarts,
  },
  props: {
    title: {
      type: String,
      default: ''
    },
    data: {
      type: Array,
      default: () => [{
        name: '-',
        value: 0,
        unit: '-',
      }, {
        name: '-',
        value: 0,
        unit: '-',
      }]
    },
    color: {
      type: Array,
      default: () => ['#01FBEF', '#FFA800', '#FFE000', '#8B34FF', '#3C59FD']
    },
    year: {
      type: [String, Number],
      default: ''
    },
    type: {
      type: [String, Number],
      default: 1, // 1、日常养护事件（处置类型），2、事件类型占比，3、修复费用统计,4、工程类别，5、工程类型
    }
  },
  data() {
    return {
      isBig: isBigScreen(),
      option: null,
      loading: false,
      eKey: new Date().getTime(),
    }
  },
  created() { },
  watch: {
    year: {
      async handler(newVal) {
        if (newVal) {
          this.loading = true;
          this.option = null;
          let d = await this.getData();
          let arr = [];
          arr = d.map(v => {
            return {
              name: v.name,
              value: this.type == 3 ? (v.sumFund ? v.sumFund / 10000 : 0) : v.count || 0,
              unit: v.unit || (this.type == 3 ? '万元' : '件'),
            }
          })
          this.loading = false;
          let data = arr && arr.length ? arr : this.data;
          // 更新dom
          this.eKey = new Date().getTime();
          this.option = await this.initCharts(data, this.color);
        }
      },
      deep: true,
      immediate: false
    }
  },
  methods: {
    // 获取数据
    getData() {
      return new Promise((resolve, reject) => {
        let obj = {
          1: getDiseaseEventDealType(this.year),
          2: getDiseaseEventTypeRatio(this.year),
          3: getDiseaseEventCost(this.year),
          4: getMaintenanceProjectType(this.year),
          5: getProjectTypeFundStatistics(this.year),
          6: null,
          7: null,
          8: null,
          9: null,
        };
        // let responseUrl = this.type == 1 ? getDiseaseEventDealType(this.year) : this.type == 2 ? getDiseaseEventTypeRatio(this.year) : this.type == 3 ? getDiseaseEventCost(this.year) : this.type == 4 ? null : null;
        let responseUrl = obj[this.type]
        if (!responseUrl) {
          resolve([])
          return;
        }
        responseUrl.then((res) => {
          let data = res.rows ||  res.data || []
          if(this.type == 4 && Object.prototype.toString.call(data) === '[object Object]') {
            data = [
              {
                name: '桥梁工程',
                count: data.qlProjCount || 0,
                unit: '件'
              },
              {
                name: '路基工程',
                count: data.ljProjCount || 0,
                unit: '件'
              },
              {
                name: '路面工程',
                count: data.lmProjCount || 0,
                unit: '件'
              },
              {
                name: '隧道工程',
                count: data.sdProjCount || 0,
                unit: '件'
              },
              {
                name: '机电工程',
                count: data.jdProjCount || 0,
                unit: '件'
              },
              {
                name: '交安工程',
                count: data.jaProjCount || 0,
                unit: '件'
              },
              {
                name: '绿化工程',
                count: data.lhProjCount || 0,
                unit: '件'
              },
              {
                name: '房建工程',
                count: data.fjProjCount || 0,
                unit: '件'
              }
            ];
          }
          if(this.type == 5 && Object.prototype.toString.call(data) === '[object Array]') {
            data = data.map(v=>{
              return {
                name: v.typeName,
                count: v.actualCompleteFund || 0,
                unit: '万元'
              }
            })
          }
          if (res.code === 200 && data) {
            resolve(data)
          } else {
            resolve('')
          }
        }).catch(err => {
          reject(err)
        })
      })
    },
    // 初始化图表
    initCharts(dataArr = [], colorArr = []) {
      var trafficWay = dataArr || [];
      var data = [];
      let sum = dataArr.reduce((acc, curr) => acc + curr.value, 0);
      let num = dataArr.length > 5 ? 5 : 10;
      let avg = sum / (dataArr.length * num);
      let total = avg;

      var color = colorArr || ['#00ffff', '#00cfff', '#006ced', '#ffe000', '#ffa800', '#ff5b00', '#ff3000']
      for (var i = 0; i < trafficWay.length; i++) {
        data.push({
          value: trafficWay[i].value,
          name: trafficWay[i].name,
          itemStyle: {
            normal: {
              borderWidth: this.isBig ? 12 : 6,
              shadowBlur: 2,
              borderColor: color[i],
              shadowColor: color[i]
            }
          }
        }, {
          value: total || 3,
          name: '',
          itemStyle: {
            normal: {
              label: {
                show: false
              },
              labelLine: {
                show: false
              },
              color: 'rgba(0, 0, 0, 0)',
              borderColor: 'rgba(0, 0, 0, 0)',
              borderWidth: 0
            }
          }
        });
      }
      let center = ['25%', '50%']
      var seriesOption = [
        {
          type: "pie",
          zlevel: 0,
          silent: true,
          radius: ["80%", "84%"],
          center,
          hoverAnimation: false,
          color: "rgba(0,62,122,1)",
          label: {
            normal: {
              show: false,
            },
          },
          labelLine: {
            normal: {
              show: false,
            },
          },
          data: [1],
        },
        {
          name: '',
          type: 'pie',
          clockWise: false,
          radius: ['70%', '70%'],
          center,
          hoverAnimation: false,
          label: {
            normal: {
              show: false,
            },
          },
          labelLine: {
            normal: {
              show: false,
            },
          },
          data: data
        }];
      let option = {
        backgroundColor: 'rgba(0,0,0,0)',
        color: color,
        title: {
          text: this.title,
          x: 'center',
          y: 'center',
          left: '24%',
          textAlign: "center",
          textStyle: {
            color: "#fff",
            fontSize: this.isBig ? 32 : 16,
            fontWeight: "700",
            shadowColor: "rgba(27,126,242,0.8)",
            shadowBlur: 10,
            shadowOffsetX: 5,
            shadowOffsetY: 5,
          },
        },
        tooltip: {
          show: false
        },
        toolbox: {
          show: false
        },
        series: seriesOption,
        legend: {
          show: true,
          y: 'center',
          left: this.isBig ? '58%' : '52%',
          icon: 'circle',
          itemWidth: this.isBig ? 30 : 10, // 设置宽度
          itemHeight: this.isBig ? 30 : 10, // 设置高度
          itemGap: this.isBig ? 20 : 10,
          textStyle: {
            color: '#fff',
            rich: {
              title: {
                color: 'rgba(255,255,255,0.8)',
                fontSize: this.isBig ? 24 : 12,
                padding: [3, 0],
              },
              value: {
                color: '#409DFF',
                fontSize: this.isBig ? 28 : 14,
                fontWeight: 'bold',
              },
              unit: {
                color: 'rgba(182,182,182,0.8)',
                fontSize: this.isBig ? 24 : 12,
              }
            }
          },
          formatter: (name) => {
            let d = dataArr.filter(v => v.name == name);
            let value = d ? d[0].value : null;
            let units = d ? d[0].unit : null;
            return `{title|${name}：}` + `{value|${value}} ` + `{unit|${units}} `;
          },
        },
      }
      return option;
    },
  }
}
</script>

<style lang="scss" scoped>
.pie-charts {
  width: 100%;
  height: 100%;
  padding: 5px;
}
</style>