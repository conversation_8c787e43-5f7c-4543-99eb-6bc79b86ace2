<template>
  <div class="app-container">
    <el-row :gutter="20">
      <!--筛选区开始-->
      <el-col :span="24" :xs="24">
        <el-row>
          <el-col :span="24">
            <el-form
              ref="queryForm"
              :inline="true"
              :model="queryParams"
              label-width="68px"
              size="small"
            >
              <el-form-item label="" prop="year">
                <el-date-picker
                  v-model="queryParams.year"
                  placeholder="年份"
                  style="width: 240px"
                  type="year"
                  value-format="yyyy"
                >
                </el-date-picker>
              </el-form-item>
              <el-form-item>
                <selectTree
                  :key="'domainId'"
                  v-model="queryParams.domainId"
                  :deptType="100"
                  :deptTypeList="[1, 3, 4]"
                  clearable
                  filterable
                  placeholder="管养单位"
                  style="width: 240px"
                />
              </el-form-item>
              <el-form-item>
                <selectTree
                  :key="'constructionUnit'"
                  v-model="queryParams.checkDomainId" :data-rule="false"
                  clearable
                  filterable
                  :dept-type="100"
                  :filter-keys="['云南省交通投资建设集团有限公司', '云南交投投资有限公司']"
                  :expand-all="false"
                  placeholder="施工单位"
                  style="width: 240px"
                />
              </el-form-item>
              <el-form-item>
                <RoadSection
                  v-model="queryParams.maiSecId"
                  :deptId="queryParams.domainId"
                  placeholder="路段"
                  style="width: 240px"
                />
              </el-form-item>
              <el-form-item>
                <el-input
                  v-model="queryParams.projName"
                  placeholder="项目名称"
                  style="width: 240px"
                ></el-input>
              </el-form-item>
              <el-form-item>
                <el-input
                  v-model="queryParams.projCode"
                  placeholder="项目编码"
                  style="width: 240px"
                ></el-input>
              </el-form-item>
              <el-form-item>
                <el-input
                  v-model="queryParams.name"
                  placeholder="任务单名称"
                  style="width: 240px"
                ></el-input>
              </el-form-item>
              <el-form-item>
                <el-input
                  v-model="queryParams.code"
                  placeholder="任务单编码"
                  style="width: 240px"
                ></el-input>
              </el-form-item>
              <el-form-item v-if="prosStatus === 1 || prosStatus === 2">
                <dict-select
                  type="overdue_remind_status"
                  v-model="queryParams.isOverdue"
                  placeholder="超期提醒"
                  style="width: 240px"
                >
                </dict-select>
              </el-form-item>
              <el-form-item v-if="prosStatus === 6">
                <dict-select
                  type="test_task_type"
                  v-model="queryParams.status"
                  placeholder="状态"
                  style="width: 240px"
                >
                </dict-select>
              </el-form-item>
              <el-form-item>
                <el-date-picker
                  v-model="issueDate"
                  type="daterange"
                  range-separator="至"
                  start-placeholder="发出起始时间"
                  end-placeholder="发出截止时间"
                  value-format="yyyy-MM-dd"
                  style="width: 240px"
                >
                </el-date-picker>
              </el-form-item>
              <el-form-item label="" prop="operator" v-if="prosStatus == 6">
                <el-cascader
                  v-model="queryParams.operator"
                  :options="deptUserOptions"
                  :props="{
                    multiple: false, //是否多选
                    value: 'id',
                    emitPath: false,
                  }"
                  :show-all-levels="false"
                  ref="deptUser"
                  placeholder="通知单拟定人" clearable style="width: 240px"
                  filterable clearable></el-cascader>
              </el-form-item>
              <el-form-item>
                <el-button
                  icon="el-icon-search"
                  size="mini"
                  type="primary"
                  @click="handleQuery"
                  >搜索</el-button
                >
                <el-button
                  icon="el-icon-refresh"
                  size="mini"
                  @click="resetQuery"
                  >重置</el-button
                >
              </el-form-item>
            </el-form>
          </el-col>
        </el-row>
        <!--筛选区结束-->
        <!--操作按钮区开始-->
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button
              v-if="prosStatus === 3"
              icon="el-icon-view"
              size="mini"
              type="warning"
              @click="openBatchDialog"
              >批量提交
            </el-button>
            <template v-if="prosStatus === 6">
              <el-button
                icon="el-icon-download"
                size="mini"
                type="success"
                v-has-menu-permi="['check:construction:export']"
                @click="exportList"
                >导出清单</el-button
              >
              <el-button icon="el-icon-view" size="mini" type="warning" v-has-menu-permi="['check:construction:visapreview']"
                @click="getPreviewInfo(1)"
                >签证单预览</el-button
              >
              <el-button icon="el-icon-view" size="mini" type="primary" v-has-menu-permi="['check:construction:maintainpreview']"
              @click="getPreviewInfo(2)"
                >维修档案预览</el-button
              >
              <el-button icon="el-icon-view" size="mini" type="success" v-has-menu-permi="['check:construction:repairpreview']"
              @click="getPreviewInfo(3)"
                >修复反馈预览</el-button
              >
              <el-button icon="el-icon-download" size="mini" type="warning" v-has-menu-permi="['check:construction:filedownload']"
              @click="getPreviewInfo(4)"
                >施工档案下载</el-button
              >
                <el-button
                  type="warning"
                  icon="el-icon-view"
                  size="mini"
                  v-has-permi="['settlement:repository:regenerate']"
                  @click="handleRegenerate"
                >重新生成报表
                </el-button>
            </template>
          </el-col>
          <right-toolbar
            :columns="columns"
            :showSearch.sync="showSearch"
            @queryTable="handleQuery"
          ></right-toolbar>
        </el-row>
        <!--操作按钮区结束-->
        <!--数据表格开始-->
        <div class="tableDiv">
          <el-table v-adjust-table
            ref="dataTable"
            v-loading="loading"
            :data="tableData"
            :height="showSearch ? 'calc(100vh - 330px)' : 'calc(100vh - 270px)'"
            border
            highlight-current-row
            row-key="id"
            size="mini"
            stripe
            style="width: 100%"
            @expand-change="tableExpand"
            @selection-change="selectionChange"
            @row-click="handleClickRow"
          >
            <el-table-column
              type="selection"
              width="55"
            ></el-table-column>
            <el-table-column type="expand">
              <template slot-scope="props">
                <el-table v-adjust-table
                  :data="props.row.constructionDetailList"
                  style="width: 100%"
                >
                  <el-table-column prop="" align="center" label="">
                  </el-table-column>
                  <el-table-column
                    prop="schemeCode"
                    align="center"
                    label="子目号"
                  >
                  </el-table-column>
                  <el-table-column
                    prop="schemeName"
                    align="center"
                    label="养护方法"
                  >
                  </el-table-column>
                  <el-table-column
                    prop="calcDesc"
                    align="center"
                    label="计算式"
                  >
                  </el-table-column>
                  <el-table-column prop="num" align="center" label="方法数量">
                  </el-table-column>
                  <el-table-column prop="unit" align="center" label="方法单位">
                  </el-table-column>
                  <el-table-column prop="price" align="center" label="单价">
                  </el-table-column>
                  <el-table-column prop="amount" align="center" label="金额">
                  </el-table-column>
                  <el-table-column prop="remark" align="center" label="备注">
                  </el-table-column>
                </el-table>
                <pagination
                  v-show="props.row.totalNum > 0"
                  :limit.sync="props.row.queryData.pageSize"
                  :page.sync="props.row.queryData.pageNum"
                  :total="props.row.totalNum"
                  @pagination="getRowDetailList(props.row)"
                />
              </template>
            </el-table-column>
            <el-table-column
              align="center"
              label="序号"
              type="index"
              width="50"
            />
            <template v-for="(column, index) in columns">
              <el-table-column
                v-if="column.visible"
                :label="column.label"
                :prop="column.field"
                :width="column.width"
                align="center"
                show-overflow-tooltip
              >
                <template slot-scope="scope">
                  <dict-tag
                    v-if="column.dict"
                    :options="dict.type[column.dict]"
                    :value="scope.row[column.field]"
                  />
                  <template v-else-if="column.slots">
                    <RenderDom
                      :index="index"
                      :render="column.render"
                      :row="scope.row"
                    />
                  </template>
                  <span v-else-if="column.isTime">{{
                    parseTime(scope.row[column.field], "{y}-{m}-{d}")
                  }}</span>
                  <span v-else>{{ scope.row[column.field] }}</span>
                </template>
              </el-table-column>
            </template>
            <el-table-column
              align="center"
              class-name="small-padding fixed-width"
              fixed="right"
              label="操作"
              width="200"
            >
              <template slot-scope="scope">
                <template v-if="prosStatus === 1">
                  <el-button
                    icon="el-icon-view"
                    size="mini"
                    type="text"
                    v-has-menu-permi="['check:construction:process']"
                    @click="handleDetail(scope.row, 1, '开工登记')"
                    >开工登记</el-button
                  >
                  <el-button
                      icon="el-icon-view"
                      size="mini"
                      type="text"
                      @click="openRecordDialog(scope.row)"
                      >操作记录</el-button
                    >
                </template>
                <template v-if="prosStatus === 2">
                  <el-button
                    icon="el-icon-view"
                    size="mini"
                    type="text"
                    v-has-menu-permi="['check:construction:process']"
                    @click="handleDetail(scope.row, 2, '完工登记')"
                    >完工登记</el-button
                  >
                  <el-button
                    icon="el-icon-view"
                    size="mini"
                    type="text"
                    @click="openRecordDialog(scope.row)"
                    >操作记录</el-button
                  >
                </template>
                <template v-if="prosStatus === 3">
                  <el-button
                    icon="el-icon-view"
                    size="mini"
                    type="text"
                    v-has-menu-permi="['check:construction:process']"
                    @click="handleDetail(scope.row, 3, '提交验收')"
                    >提交验收</el-button
                  >
                  <el-button
                    icon="el-icon-view"
                    size="mini"
                    type="text"
                    @click="openOperate(scope.row)"
                    >审核意见</el-button
                  >
                  <el-button
                    icon="el-icon-view"
                    size="mini"
                    type="text"
                    @click="openRecordDialog(scope.row)"
                    >操作记录</el-button
                  >
                  <el-button
                    icon="el-icon-view"
                    size="mini"
                    type="text"
                    v-has-menu-permi="['check:finished:edit']"
                    @click="handleDetail(scope.row, 2, '修改完工登记')"
                    >修改完工登记信息</el-button
                  >
                </template>
                <template v-if="prosStatus === 4">
                  <el-button
                    icon="el-icon-view"
                    size="mini"
                    type="text"
                    v-has-menu-permi="['check:construction:process']"
                    @click="handleDetail(scope.row, 4, '施工单位审核')"
                    >施工单位审核</el-button
                  >
                  <el-button
                    icon="el-icon-view"
                    size="mini"
                    type="text"
                    @click="openRecordDialog(scope.row)"
                    >操作记录</el-button
                  >
                </template>
                <template v-if="prosStatus === 5">
                  <el-button
                    icon="el-icon-view"
                    size="mini"
                    type="text"
                    v-has-menu-permi="['check:construction:process']"
                    @click="handleDetail(scope.row, 5, '验收登记')"
                    >验收登记</el-button
                  >
                  <el-button
                    icon="el-icon-view"
                    size="mini"
                    type="text"
                    @click="openOperate(scope.row)"
                    >审核意见</el-button
                  >
                  <el-button
                    icon="el-icon-view"
                    size="mini"
                    type="text"
                    @click="openRecordDialog(scope.row)"
                    >操作记录</el-button
                  >
                </template>
                <template v-if="prosStatus === 6">
                  <el-button
                    icon="el-icon-view"
                    size="mini"
                    type="text"
                    @click="openRecordDialog(scope.row)"
                    >施工进度</el-button
                  >
                  <el-button
                    icon="el-icon-view"
                    size="mini"
                    type="text"
                    @click="openOperate(scope.row)"
                    >审核意见</el-button
                  >
                </template>
              </template>
            </el-table-column>
          </el-table>
          <pagination
            v-show="total > 0"
            :limit.sync="queryParams.pageSize"
            :page.sync="queryParams.pageNum"
            :total="total"
            @pagination="handleQuery"
          />
        </div>
        <!--数据表格结束-->
      </el-col>
    </el-row>
    <el-drawer
      :wrapperClosable="false"
      :title="detailTitle"
      :visible.sync="openDetail"
      size="70%"
      destroy-on-close
    >
      <reg-detail
        :status="status"
        :type="type"
        :read-only="readonly"
        :row="row"
        @close="closeDetail"
      ></reg-detail>
    </el-drawer>
    <el-dialog
      title="施工进度"
      :visible.sync="recordDialog"
      width="80%"
      destroy-on-close
    >
      <RegRecord :row="row" :prosStatus="prosStatus"></RegRecord>
    </el-dialog>
    <el-dialog
      title="操作意见"
      :visible.sync="openOperateInfo"
      width="80%"
      destroy-on-close
    >
      <operateInfo
        :businessKey="row.id"
        :getNodeInfo="getCurNodeInfo"
      ></operateInfo>
    </el-dialog>
    <el-dialog title="批量提交验收" :visible.sync="batchDialog" size="50%">
      <el-input v-model="reviewComment" type="textarea" />
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitBatchExamine">提 交</el-button>
        <el-button @click="batchDialog = false">取 消</el-button>
      </span>
    </el-dialog>
    <IFramePreview ref="iframeRef" :srcdoc="preview.html" :down-url="preview.url" :file-name="preview.fileName"></IFramePreview>
  </div>
</template>

<script>
import RoadSection from "@/views/baseData/components/roadSection/index.vue";
import selectTree from "@/components/DeptTmpl/selectTree.vue";
import RegDetail from "./regDetail.vue";
import RegRecord from "./regRecord.vue";
import operateInfo from "@/views/dailyMaintenance/component/operateInfo.vue";
import {
  getConstructionList,
  getConstructionNodeInfo,
  viewConstructionList,
  getConstructionDetailList,
  getConstruction
} from "@/api/regularTesting/taskManage/taskList";

import {
  getRegisterDetailList,
  getFinishedDetailList,
  getVisaPreviewInfo,
  getMaintainPreviewInfo,
  getRepairPreviewInfo,
  downloadMaintainZip
} from "@/api/regularTesting/constructionManage/constructionManage";
import IFramePreview from "@/components/IFramePreview/index.vue";
import axios from 'axios'
import { saveAs } from 'file-saver'
import {regenerateReport} from "@/api/dailyMaintenance/metering/addPrice";
import {debounce} from 'lodash'
import {getTreeStruct} from "@/api/tmpl";
export default {
  components: { selectTree, RoadSection, RegDetail, RegRecord, operateInfo, IFramePreview },
  dicts: ["test_task_type"],
  data() {
    return {
      showSearch: false,
      queryParams: {
        pageNum: 1,
        pageSize: 50,
      },
      total: 0,
      loading: false,
      status: "",
      columns: [
        {
          key: 1,
          width: 100,
          field: "overdueDesc",
          label: `超期提醒`,
          visible: true,
        },
        {
          key: 2,
          width: 100,
          field: "status",
          label: `状态`,
          visible: true,
          dict: "test_task_type",
        },
        {
          key: 3,
          width: 100,
          field: "projName",
          label: `项目名称`,
          visible: true,
        },
        {
          key: 4,
          width: 100,
          field: "projCode",
          label: `项目编码`,
          visible: true,
        },
        {
          key: 6,
          width: 100,
          field: "name",
          label: `任务单名称`,
          visible: true,
        },
        {
          key: 7,
          width: 100,
          field: "code",
          label: `任务单编号`,
          visible: true,
        },
        {
          key: 8,
          width: 100,
          field: "maiSecName",
          label: `路段名称`,
          visible: true,
        },
        { key: 8, width: 100, field: "sumFund", label: `金额`, visible: true },
        {
          key: 9,
          width: 100,
          field: "domainName",
          label: `管养单位`,
          visible: true,
        },
        {
          key: 10,
          width: 100,
          field: "checkDomainName",
          label: `检测单位`,
          visible: true,
        },
        {
          key: 11,
          width: 100,
          field: "checkConName",
          label: `检测合同`,
          visible: true,
        },
        {
          key: 12,
          width: 100,
          field: "issueDate",
          label: `发出时间`,
          visible: true,
        },
        {
          key: 18,
          width: 100,
          field: "content",
          label: `工作内容`,
          visible: true,
        },
        {
          key: 22,
          width: 120,
          field: "beginDate",
          label: `计划开始时间`,
          visible: true,
        },
        {
          key: 23,
          width: 120,
          field: "endDate",
          label: `计划完成时间`,
          visible: true,
        },
      ],
      tableData: [],
      row: {},
      status: 1,
      readonly: true,
      detailTitle: "",
      openDetail: false,
      type: 1,
      recordDialog: false,
      openOperateInfo: false,
      selection: [],
      batchDialog: false,
      reviewComment: "",
      issueDate: [],
      deptUserOptions: [],
      preview: {
        html: '',
        url: '',
        fileName: ''
      },
    };
  },
  props: {
    // 1: 开工登记;2:完工登记;3: 提交验收;4: 施工单位审核;5:验收登记;6:施工进度
    prosStatus: {
      type: Number,
      default: 1,
    },
  },
  watch: {
    prosStatus: {
      handler(newVal) {
        switch (newVal) {
          case 1:
            this.queryParams.status = 4;
            this.columns = this.columns.filter(
              (item) => item.field !== "price"
            );
            break;
          case 2:
            this.queryParams.status = 5;
            this.columns = this.columns.filter(
              (item) => item.field !== "price"
            );
            break;
          case 3:
            this.queryParams.status = 6;
            this.columns = this.columns.filter(
              (item) => item.field !== "overdueDesc"
            );
            break;
          case 4:
            this.queryParams.status = 7;
            this.columns = this.columns.filter(
              (item) => item.field !== "overdueDesc"
            );
            break;
          case 5:
            this.queryParams.status = 8;
            this.columns = this.columns.filter(
              (item) => item.field !== "price"
            );
            this.columns = this.columns.filter(
              (item) => item.field !== "overdueDesc"
            );
            break;
          case 6:
            this.queryParams.status = "";
            this.columns = this.columns.filter(
              (item) => item.field !== "price"
            );
            this.columns = this.columns.filter(
              (item) => item.field !== "overdueDesc"
            );
            break;
          default:
            break;
        }
      },
      immediate: true,
    },
  },
  mounted() {
    this.getDeptTreeDef()
    this.handleQuery();
  },
  methods: {
    /** 查询部门-用户下拉树结构 */
    getDeptTreeDef() {
      getTreeStruct({types:111}).then(response => {
        this.deptUserOptions = response.data;
      });
    },
    handleQuery() {
      if (
        this.issueDate &&
        this.issueDate.length == 2
      ) {
        this.queryParams.issueStartDate =
          this.issueDate[0];
        this.queryParams.issueEndDate = this.issueDate[1];
      }
      this.loading = true;
      if (this.prosStatus === 6) {
        viewConstructionList(this.queryParams).then((res) => {
          this.tableData = res.rows;
          this.total = res.total;
          this.loading = false;
          this.tableData.forEach((item) => {
            item.queryData = {
              conId: item.id,
              pageNum: 1,
              pageSize: 10,
            };
            item.totalNum = 0;
          });
        });
      } else {
        getConstructionList(this.queryParams).then((res) => {
          this.tableData = res.rows;
          this.total = res.total;
          this.loading = false;
          this.tableData.forEach((item) => {
            item.queryData = {
              conId: item.id,
              projConId: item.id,
              pageNum: 1,
              pageSize: 10,
            };
            item.totalNum = 0;
          });
        });
      }
    },
    resetQuery() {
      const status = this.queryParams.status
      this.queryParams = {
        pageNum: 1,
        pageSize: 50,
        status
      };
      this.handleQuery()
    },
    tableExpand(row, expandedRows) {
      const isExpand = expandedRows.some((item) => row.id === item.id);
      if (isExpand) {
        this.getRowDetailList(row);
      }
    },
    getRowDetailList(row) {
      if (this.prosStatus === 1) {
        getConstructionDetailList(row.queryData).then((res) => {
          this.$set(row, "totalNum", res.total || 0);
          if (res.rows) {
            this.$set(row, "constructionDetailList", res.rows);
          }
        });
      } else if (this.prosStatus === 2) {
        getRegisterDetailList(row.queryData).then((res) => {
          this.$set(row, "totalNum", res.total || 0);
          if (res.rows) {
            this.$set(row, "constructionDetailList", res.rows);
          }
        });
      } else {
        getFinishedDetailList(row.queryData).then((res) => {
          this.$set(row, "totalNum", res.total || 0);
          if (res.rows) {
            this.$set(row, "constructionDetailList", res.rows);
          }
        });
      }
    },
    handleClickRow(e) {
      this.row = e;
    },
    openOperate(row) {
      this.row = row;
      this.openOperateInfo = true;
    },
    openRecordDialog: debounce(function(e) {
      getConstruction(e.id).then(res=> {
          this.row = res.data
          this.recordDialog = true;
        })
    }, 500),
    selectionChange(selection) {
      this.selection = selection;
    },
    openBatchDialog() {
      if (this.selection.length <= 0) {
        this.$message.warning("请先选择一条记录！");
        return;
      }
      this.reviewComment = "";
      this.batchDialog = true;
      console.log(this.selection);
    },
    getPreviewInfo(newVal) {
      if (!this.row.id) {
        this.$message.warning("请先选择一条数据！");
        return;
      }
      const params = {id: this.row.id}
      switch (newVal) {
        case 1:
          getVisaPreviewInfo(params).then(res=> {
            if (res.code == 200){
              this.handlePreviewData(res.data)
            }
          })
          break;
        case 2:
          getMaintainPreviewInfo(params).then(res=> {
            if (res.code == 200){
              this.handlePreviewData(res.data)
            }
          })
          break;
        case 3:
          getRepairPreviewInfo(params).then(res=> {
            if (res.code == 200){
              this.handlePreviewData(res.data)
            }
          })
          break;
        case 4:
          this.loading = true
          downloadMaintainZip(params).then(res=> {
            if (res.code == 200){
              if (res.data.fileName.endsWith('.zip')) {
                let link = document.createElement('a')
                link.download = res.data.fileName
                link.style.display = 'none'
                link.href = res.data.downUrl
                document.body.appendChild(link)
                link.click()
                document.body.removeChild(link)
                this.loading = false
              } else {
                axios({
                  method: "get",
                  responseType: 'arraybuffer',
                  url: res.data.downUrl,
                  headers: {}
                }).then((res) => {
                  const arrayBuffer = res.data;
                  // 创建一个Blob对象
                  const blob = new Blob([arrayBuffer], {type: 'application/zip'}); // 对于.xls文件
                  saveAs(blob, res.data.fileName)
                }).finally(() => {
                  this.loading = false
                })
              }
            }
          })
          break;
        default:
          break;
      }
    },
    handlePreviewData (data) {
      this.preview.html = data.html
      this.preview.url = data.downUrl
      this.preview.fileName = data.fileName
      this.$refs.iframeRef.visible = true
    },
    submitBatchExamine() {
      if (!this.reviewComment) {
        this.$message.warning("请填写审核意见！");
        return;
      }
      const ids = this.selection.map((item) => item.id);
      const submitData = {
        ids,
        comment: this.reviewComment,
      };
      console.log(submitData);
      this.batchDialog = false;
    },
    // 导出清单按钮
    exportList() {
      if (
        this.issueDate &&
        this.issueDate.length == 2
      ) {
        this.queryParams.issueStartDate =
          this.issueDate[0];
        this.queryParams.issueEndDate = this.issueDate[1];
      }
      this.download(
        `manager/check/construction${
          this.prosStatus === 6 ? "" : "/pending"
        }/export`,
        { ...this.queryParams },
        `checksTask_${new Date().getTime()}.xlsx`,
        {
          headers: { "Content-Type": "application/json;" },
          parameterType: "body",
        }
      );
    },
    getCurNodeInfo(data) {
      return getConstructionNodeInfo(data);
    },
    handleDetail(row, status, title) {
      this.status = status;
      this.readonly = false;
      this.detailTitle = title;
      this.row = row;
      if (status > 2) {
        this.readonly = true;
      }
      this.openDetail = true;
    },
    handleRevoke(row, status, title) {
      this.type = 2;
      this.handleDetail(row, status, title);
    },
    handleRegenerate() {
      if (this.selection.length == 0) {
        this.$modal.msgError("请勾选至少一条数据")
        return
      }
      const params = {
        idList: this.selection.map(item => item.id),
        type: 5
      }
      regenerateReport(params).then(res => {
        this.$modal.msgSuccess("操作成功")
      })
    },
    closeDetail() {
      this.type = 1;
      this.openDetail = false;
      this.handleQuery();
    },
  },
};
</script>

<style lang="scss" scoped></style>
<style lang="scss" scoped>
@import "@/assets/styles/business.scss";
</style>
