<template>
  <div ref="fullRef" class="cockpit">
    <Home @clickL="onLClick" v-show="!componentL && !singlePage" @fullClick="onFull" ref="home" />
    <Subpage :title="title" :subTitle="subTile" :pageLId="componentL" :pageRId="componentR" :data="data"
      :singlePage="singlePage" v-if="componentL || componentR" @back="onBack" ref="subRef" />
  </div>
</template>

<script>
import screenfull from "screenfull";
import Home from "./components/home.vue";
import Subpage from "./components/subpage.vue";
import { Loading } from "element-ui";
import { isBigScreen } from "./util/utils";

export default {
  name: "index",
  components: {
    Home,
    Subpage,
  },
  provide() {
    return {
      cockpit: this,
    };
  },
  data() {
    return {
      componentL: "",
      componentR: "",
      title: "",
      subTile: "",
      loading: false,
      isFull: false,
      data: {},
      isBig: isBigScreen(),
      singlePage: false,
    };
  },
  methods: {
    onLClick(obj) {
      this.$modal.loading();
      this.componentL = obj.componentL;
      this.componentR = obj.componentR;
      this.title = obj.title;
      this.subTile = obj.subText;
      this.data = obj.data;
      this.singlePage = obj.singlePage;
      this.$modal.closeLoading();
    },
    onBack() {
      if (this.$route.query.isFromHome) {
        this.$router.go(-1)
      } else {
        this.componentL = "";
        this.componentR = "";
        this.title = "";
        this.subTile = "";
        this.singlePage = false;
        this.data = {};
      }
    },
    onFull(e) {
      this.isFull = e;
      let fullDom = null;
      if (this.$refs.subRef) {
        fullDom = this.$refs.subRef;
      } else {
        fullDom = this.$refs.fullRef;
      }
      if (fullDom) screenfull.toggle(fullDom);

      if (!document.fullscreenElement) {
        screenfull.request();
      } else {
        screenfull.exit();
      }
    },
    handleKeyDown(event) {
      if (event.keyCode === 27) {
        screenfull.exit();
      }
    },
    change() {
      this.isFullscreen = screenfull.isFullscreen;
    },
  },
  created() {
    let { page } = this.$route.query;
    if(page) {
      if(page == 'patrolCheck') {
        this.singlePage = false;
        this.componentL = 'PatrolCheck';
        this.componentR = 'PatrolDetail';
        this.title = '巡检查专题';
        this.subTile = '巡检查专题';
      }
    }
  },
  mounted() {
    // 如果是路由访问传参
    if (this.$route.query && this.$route.query.componentR) {
      this.componentR = this.$route.query.componentR;
      this.componentL = this.isBig ? "BridgeSpecial" : this.$route.query.componentR;
    }
    // 监听全屏
    screenfull.on("change", this.change);
    document.addEventListener("keydown", this.handleKeyDown);
    this.$nextTick(() => {
      let parentEl = this.$refs.fullRef?.parentNode;
      parentEl.style.overflow = "auto";
      parentEl.style.overflowX = "hidden";
    });
  },
  watch: {
    // 如果是从home页的巡检查管理点击跳转过来，执行一下代码
    '$route.query.isFromHome': {
      handler(val) {
        if (val) {
          this.$nextTick(() => {
            this.$refs.home.$refs.PageL.onclick(7)
          });
        }
      },
      immediate: true // 立即触发一次
    }
  },
  destroyed() {
    screenfull.off("change", this.change);
    document.removeEventListener("keydown", this.handleKeyDown);
    let parentEl = this.$refs.fullRef?.parentNode;
    if (parentEl) {
      parentEl.style.overflow = "hidden";
    }
  },
};
</script>

<style lang="scss">
html,
body,
* {
  font-family: "Arial", sans-serif;
  /* 使用清晰字体 */
  -webkit-font-smoothing: antialiased;
  /* 优化文字渲染 */
}

.cockpit {
  width: 100%;
  height: 100%;
  overflow-y: auto;
  touch-action: none;
  /* 禁用触摸操作 */
  user-select: none;
  /* 防止文本被选中 */
}
</style>
