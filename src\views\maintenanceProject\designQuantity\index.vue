<template>
  <div class="app-container">
    <el-row :gutter="20">
      <!--筛选区开始-->
      <el-col :span="24" :xs="24">
        <el-row>
          <el-col :span="24">
            <el-form ref="queryForm" :inline="true" :model="queryParams" label-width="68px" size="small">
              <el-form-item>
                <selectTree
                    :key="'domainId'"
                    v-model="queryParams.domainId"
                    :deptType="100"
                    :deptTypeList="[1, 3, 4]" clearable
                    filterable
                    placeholder="管养单位"
                    style="width: 240px"
                />
              </el-form-item>
              <el-form-item>
                <selectTree
                    :key="'constructionUnit'"
                    style="width: 240px"
                    v-model="queryParams.conDomainId" :data-rule="false"
                    :dept-type="100"
                    placeholder="施工单位"
                    :filter-keys="['云南省交通投资建设集团有限公司', '云南交投投资有限公司']"
                    :expand-all="false"
                    clearable
                    filterable
                />
              </el-form-item>
              <el-form-item>
                <RoadSection style="width: 240px" v-model="queryParams.maiSecId" :deptId="queryParams.domainId" placeholder="路段"/>
              </el-form-item>
              <el-form-item>
                <el-input style="width: 240px" placeholder="项目名称" v-model="queryParams.name"></el-input>
              </el-form-item>
              <el-form-item>
                <el-input style="width: 240px" placeholder="项目编码" v-model="queryParams.code"></el-input>
              </el-form-item>
<!--              <el-form-item>-->
<!--                <el-input style="width: 240px" placeholder="状态" v-model="queryParams.calcEnterStatus"></el-input>-->
<!--              </el-form-item>-->
              <el-form-item>
                <el-button icon="el-icon-search" size="mini" type="primary" @click="handleQuery">搜索</el-button>
                <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
              </el-form-item>
            </el-form>
          </el-col>
        </el-row>
        <!--操作按钮区开始-->
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button
                icon="el-icon-view"
                size="mini"
                type="success"
                @click="handleOpenOperate"
            >审核意见
            </el-button>
          </el-col>
          <right-toolbar :columns="columns" :showSearch.sync="showSearch" @queryTable="handleQuery"></right-toolbar>
        </el-row>
        <!--数据表格开始-->
        <div class="tableDiv">
          <el-table v-adjust-table
              ref="dataTable"
              v-loading="loading"
              :data="tableData"
              :height="
                showSearch ? 'calc(100vh - 330px)' : 'calc(100vh - 270px)'
              "
              border
              highlight-current-row
              row-key="id"
              size="mini"
              stripe
              style="width: 100%"
              @row-click="tableRowClick"
          >
            <el-table-column
                align="center"
                label="序号"
                type="index"
                width="50"
            />
            <template v-for="(column,index) in columns">
              <el-table-column v-if="column.visible" show-overflow-tooltip
                               :label="column.label"
                               :prop="column.field"
                               :width="column.width"
                               align="center">
                <template slot-scope="scope">
                  <dict-tag v-if="column.dict" :options="dict.type[column.dict]" :value="scope.row[column.field]"/>
                  <template v-else-if="column.slots">
                    <RenderDom :index="index" :render="column.render" :row="scope.row"/>
                  </template>
                  <span v-else-if="column.isTime">{{ parseTime(scope.row[column.field], '{y}-{m}-{d}') }}</span>
                  <span v-else>{{ scope.row[column.field] }}</span>
                </template>
              </el-table-column>
            </template>
            <el-table-column
                align="center"
                class-name="small-padding fixed-width"
                fixed="right"
                label="操作"
                width="250"
            >
              <template slot-scope="scope">
                <template>
                  <el-button
                      icon="el-icon-edit"
                      size="mini"
                      type="text"
                      v-if="type == 1"
                      @click="handleOpenAdd(scope.row)"
                  >新增
                  </el-button>
                  <el-button
                      icon="el-icon-edit"
                      size="mini"
                      type="text"
                      v-if="type == 2"
                      @click="handleEdit(scope.row)"
                  >编辑
                  </el-button>
                  <el-button
                      icon="el-icon-delete"
                      size="mini"
                      type="text"
                      v-if="type == 2"
                      @click="handleDelete(scope.row)"
                  >删除
                  </el-button>
                  <el-button
                      icon="el-icon-check"
                      size="mini"
                      type="text"
                      v-if="type == 2"
                      @click="handleSubmit(scope.row)"
                  >提交
                  </el-button>
                  <el-button
                      icon="el-icon-check"
                      size="mini"
                      type="text"
                      v-if="type == 3"
                      @click="handleReceive(scope.row)"
                  >审核
                  </el-button>
                  <el-button
                      icon="el-icon-view"
                      size="mini"
                      type="text"
                      @click="handleView(scope.row)"
                  >查看
                  </el-button>
                </template>
              </template>
            </el-table-column>
          </el-table>
          <pagination
              v-show="total>0"
              :limit.sync="queryParams.pageSize"
              :page.sync="queryParams.pageNum"
              :total="total"
              @pagination="handleQuery"
          />
        </div>
        <!--数据表格结束-->
      </el-col>
    </el-row>
    <el-drawer v-if="openDetail" :title="detailTitle" :visible.sync="openDetail" :wrapperClosable="false"
               destroy-on-close
               size="75%">
      <detail :type="type" :read-only="readonly" :row-data="row" @close="close"></detail>
    </el-drawer>
    <el-dialog :visible.sync="openFile" v-if="openFile" destroy-on-close title="附件列表" width="80%">
      <file-upload v-model="fileId" :forView="true"></file-upload>
    </el-dialog>
    <el-dialog title="审核意见" :visible.sync="openOperateInfo" v-if="openOperateInfo" width="80%" destroy-on-close>
      <operateInfo type="proj"  :business-key="row.id" :get-node-info="getNodeInfo"></operateInfo>
    </el-dialog>
  </div>
</template>

<script>
import selectTree from "@/components/DeptTmpl/selectTree.vue";
import RoadSection from "@/views/baseData/components/roadSection/index.vue";
import {listProject, pendingList, process, remove, view, getNodeInfo} from "@/api/maintenanceProject/designQuantity";
import Detail from "./detail.vue";
import operateInfo from "@/views/dailyMaintenance/component/operateInfo.vue";
import {v4 as uuidv4} from "uuid";

export default {
  name: 'DesignQuantity',
  components: {Detail, RoadSection, selectTree,operateInfo,
    RenderDom: {
      functional: true,
      props: {
        row: Object,
        index: Number,
        render: Function
      },
      render(createElement, ctx) {
        const {row, index} = ctx.props
        return ctx.props.render(row, index)
      }
    }},
  dicts: ['calc_enter_status', 'affiliation_project_type', 'project_type'],
  data() {
    return {
      showSearch: false,
      queryParams: {
        pageNum: 1,
        pageSize: 50
      },
      total: 0,
      loading: false,
      columns: [
        {key: 0, width: 100, field: 'calcEnterStatus', label: `状态`, visible: true, dict: 'calc_enter_status'},
        {key: 1, width: 100, field: 'domainName', label: `管养单位`, visible: true},
        {key: 2, width: 100, field: 'year', label: `年度`, visible: true},
        {key: 3, width: 100, field: 'name', label: `项目名称`, visible: true},
        {key: 4, width: 100, field: 'code', label: `项目编码`, visible: true},
        {key: 5, width: 100, field: 'maiSecName', label: `路段名称`, visible: true},
        {key: 6, width: 100, field: 'sumFund', label: `计划资金`, visible: true},
        {key: 7, width: 100, field: 'projectType', label: `工程分类`, visible: true, dict: 'project_type'},
        {key: 8, width: 100, field: 'mtype', label: `所属工程类别`, visible: true, dict: 'affiliation_project_type'},
        {key: 9, width: 100, field: 'designDomainName', label: `设计单位`, visible: true},
        {key: 10, width: 100, field: 'designFund', label: `设计资金`, visible: true},
        {key: 11, field: 'fileId', label: `附件`, visible: true, slots: true, render: (row, index) => {
            return (
                <el-button
                    size="mini"
                    disabled={!row.fileId}
                    type="text" onClick={e => this.handleOpenFile(row)}>查看</el-button>
            )
          }
        },
      ],
      tableData: [],
      openDetail: false,
      detailTitle: '新增',
      type: 1,
      readonly: false,
      row: {},
      openFile: false,
      fileId: '',
      openOperateInfo: false
    }
  },
  created() {
    this.type = this.$route.query.type
    this.handleQuery()
  },
  methods: {
    getNodeInfo,
    handleQuery() {
      this.loading = true
      if (this.type == 1) {
        listProject(this.queryParams).then(response => {
          if (response.code == 200) {
            this.loading = false
            this.tableData = response.rows
            this.total = response.total
          }
        })
      } else if (this.type == 0) {
        view(this.queryParams).then(response => {
          if (response.code == 200) {
            this.loading = false
            this.tableData = response.rows
            this.total = response.total
          }
        })
      } else {
        this.type == 2 ? this.queryParams.calcEnterStatus = 1 : this.queryParams.calcEnterStatus = 2
        pendingList(this.queryParams).then(response => {
          if (response.code == 200) {
            this.loading = false
            this.tableData = response.rows
            this.total = response.total
          }
        })
      }
    },
    handleView(row) {
      this.row = row
      this.detailTitle = '新增'
      this.readonly = true
      this.openDetail = true
    },
    handleOpenAdd(row) {
      this.row = row
      this.detailTitle = '新增'
      this.readonly = false
      this.openDetail = true
    },
    handleEdit(row) {
      this.row = row
      this.detailTitle = '编辑'
      this.readonly = false
      this.openDetail = true
    },
    handleDelete(row ) {
      this.$modal.confirm("是否确认删除").then(() => {
        this.loading = true
        remove(row.id).then(res => {
          this.$modal.msgSuccess("删除成功")
          this.handleQuery()
        })
      });
    },
    handleReceive(row) {
      this.$prompt('', '是否确认审核', {
        confirmButtonText: '审核',
        cancelButtonText: '取消',
        inputPlaceholder: '请输入',
        inputType: 'textarea'
      }).then(({ value }) => {
        this.loading = true
        const params = {
          businessKey: row.id,
          taskId: row.taskId,
          approved: true,
          comment: value
        }
        process(params).then(res => {
          this.handleQuery()
          this.$modal.msgSuccess("提交成功")
        }).catch(() => {
          this.loading = false
        })
      });
    },
    handleSubmit(row) {
      this.$prompt('', '是否确认提交', {
        confirmButtonText: '提交',
        cancelButtonText: '取消',
        inputPlaceholder: '请输入',
        inputType: 'textarea'
      }).then(({ value }) => {
        this.loading = true
        const params = {
          businessKey: row.id,
          taskId: row.taskId,
          approved: true,
          comment: value
        }
        process(params).then(res => {
          this.handleQuery()
          this.$modal.msgSuccess("提交成功")
        }).catch(() => {
          this.loading = false
        })
      });
    },
    tableRowClick(row) {
      this.row = row
    },
    handleOpenOperate() {
      if (!this.row.id) {
        this.$message.warning('请先选择一条数据');
        return
      }
      this.openOperateInfo = true
    },
    handleOpenFile(row) {
      this.fileId = row.fileId
      this.openFile = true
    },
    close() {
      this.openDetail = false
      this.handleQuery()
    },
    resetQuery() {
      this.queryParams = { pageNum: 1, pageSize: 50 }
      this.handleQuery()
    }
  }
}
</script>

<style scoped lang="scss">

</style>
<style lang="scss" scoped>
@import "@/assets/styles/business.scss";
</style>
