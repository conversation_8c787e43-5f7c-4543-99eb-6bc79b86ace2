<template>
  <div class="app-container maindiv" style="height: 500px">
    <el-steps v-if="nodeList.diseaseNodeNameList && nodeList.diseaseNodeNameList.length > 0"
              :active="nodeList.diseaseNodeNameList.length" direction="vertical">
      <el-step v-for="(item,index) in nodeList.diseaseNodeNameList">
        <template #icon>{{ nodeList.diseaseNodeNameList.length - index }}</template>
        <template #title>
          <div :style="{color: item.direction ? 'none' : 'red'}">{{ item.nodeName }}
            <i v-if="item.direction" class="el-icon-circle-check"/>
            <i v-else class="el-icon-circle-close"/>
            &nbsp&nbsp&nbsp{{ item.endTime }}
          </div>
        </template>
        <template #description>
          <el-descriptions :column="1">
            <el-descriptions-item label="操作人">{{ item.assigneeName }}</el-descriptions-item>
            <el-descriptions-item label="意见">{{ item.comment }}</el-descriptions-item>
          </el-descriptions>
        </template>
      </el-step>
    </el-steps>
    <el-empty v-else></el-empty>
  </div>
</template>

<script>
import {disNodeInfo} from "@/api/dailyMaintenance/construction/registration";

export default {
  name: 'eventInfo',
  props: {
    disId: {
      type: String,
      default: ''
    },
    daliyId: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      nodeList: []
    }
  },
  mounted() {
    this.getNodeInfo()
  },
  methods: {
    getNodeInfo() {
      disNodeInfo({
        disId: this.disId,
        daliyId: this.daliyId
      }).then(res => {
        if (res.data.diseaseNodeNameList) {
          res.data.diseaseNodeNameList.slice().reverse()
        }
        if (res.data.nodeNameList) {
          res.data.nodeNameList.slice().reverse()
        }
        this.nodeList = res.data
      })
    }
  }
}
</script>

<style scoped>

</style>
<style lang="scss" scoped>
@import "@/assets/styles/business.scss";
</style>
