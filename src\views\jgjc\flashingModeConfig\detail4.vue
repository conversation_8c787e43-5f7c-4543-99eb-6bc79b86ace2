<template>
  <div>
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button icon="el-icon-plus" size="mini" type="primary" @click="handleAdd">
          新增
        </el-button>
        <el-button icon="el-icon-delete" size="mini" type="danger" @click="handleDelete">
          删除
        </el-button>
      </el-col>
    </el-row>
    <!-- 数据表格开始 -->
    <div class="tableDiv">
      <el-table
        v-adjust-table
        v-loading="loading"
        :data="dataList"
        :height="'calc(100vh - 260px)'"
        border
        size="mini"
        style="width: 100%"
        @selection-change="handleSelectionChange"
      >
        <el-table-column align="center" fixed="left" type="selection" width="55"></el-table-column>
        <el-table-column
          align="center"
          fixed
          label="序号"
          type="index"
          width="100"
        ></el-table-column>
        <template v-for="(column, index) in columns">
          <el-table-column
            v-if="column.visible"
            :key="index"
            :label="column.label"
            :prop="column.field"
            align="center"
            show-overflow-tooltip
          >
            <template slot-scope="scope">
              <dict-tag
                v-if="column.dict"
                :options="dict.type[column.dict]"
                :value="scope.row[column.field]"
              />
              <span v-else-if="column.isSwitch">
                {{ scope.row[column.field] === 1 ? '开' : '关' }}
              </span>
              <span v-else-if="column.isColor">
                {{ getColorText(scope.row[column.field]) }}
              </span>
              <span v-else-if="column.isFrequency">
                {{ getFrequencyText(scope.row[column.field]) }}
              </span>
              <span v-else>{{ scope.row[column.field] }}</span>
            </template>
          </el-table-column>
        </template>
        <el-table-column align="center" fixed="right" label="操作" width="150">
          <template slot-scope="scope">
            <el-button icon="el-icon-edit" size="mini" type="text" @click="handleUpdate(scope.row)">修改</el-button>
          </template>
        </el-table-column>
      </el-table>
      <pagination
        v-show="total > 0"
        :limit.sync="queryParams.pageSize"
        :page.sync="queryParams.pageNum"
        :total="total"
        @pagination="handleQuery"
      />
    </div>
    <!-- 数据表格结束 -->

    <!-- 新增/修改对话框 -->
    <el-dialog
      :title="dialogTitle"
      :visible.sync="dialogVisible"
      append-to-body
      destroy-on-close
      width="50%"
    >
      <el-form ref="form" :model="formData" :rules="rules" label-width="120px" size="medium">
        <el-row>
          <el-col :span="12">
            <el-form-item label="模式名称" prop="modeName">
              <el-input
                v-model="formData.modeName"
                placeholder="请输入模式名称"
                clearable
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
<!--          <el-col :span="12">-->
<!--            <el-form-item label="模式编码" prop="modeCode">-->
<!--              <el-input-->
<!--                v-model="formData.modeCode"-->
<!--                placeholder="请输入模式编码"-->
<!--                clearable-->
<!--                style="width: 100%"-->
<!--              />-->
<!--            </el-form-item>-->
<!--          </el-col>-->
        </el-row>

        <el-row>
          <el-col :span="12">
            <el-form-item label="爆闪开关" prop="flashSw">
              <el-radio-group v-model="formData.flashSw">
                <el-radio :label="1">开启</el-radio>
                <el-radio :label="0">关闭</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="爆闪灯亮度" prop="flashBright">
              <el-slider
                v-model="formData.flashBright"
                :min="0"
                :max="10"
                show-input
                :disabled="formData.flashSw === 0"
              ></el-slider>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="语音播报开关" prop="voiceSw">
              <el-radio-group v-model="formData.voiceSw">
                <el-radio :label="1">开启</el-radio>
                <el-radio :label="0">关闭</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="语音播报内容" prop="voiceContent">
              <el-input
                v-model="formData.voiceContent"
                placeholder="请输入语音播报内容"
                clearable
                :disabled="formData.voiceSw === 0"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="播报次数" prop="voiceCount">
              <el-input-number
                v-model="formData.voiceCount"
                :min="0"
                placeholder="0表示循环播报"
                :disabled="formData.voiceSw === 0"
                controls-position="right"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="播报音量" prop="voiceVolume">
              <el-slider
                v-model="formData.voiceVolume"
                :min="0"
                :max="10"
                show-input
                :disabled="formData.voiceSw === 0"
              ></el-slider>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="播报间隔(秒)" prop="voiceInterval">
              <el-input-number
                v-model="formData.voiceInterval"
                :min="1"
                :max="60"
                placeholder="1-60秒"
                :disabled="formData.voiceSw === 0"
                controls-position="right"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="播报语速" prop="voiceSpeed">
              <el-input-number
                v-model="formData.voiceSpeed"
                :min="1"
                :max="4"
                placeholder="1-4级"
                :disabled="formData.voiceSw === 0"
                controls-position="right"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="屏幕开关" prop="textSw">
              <el-radio-group v-model="formData.textSw">
                <el-radio :label="1">开启</el-radio>
                <el-radio :label="0">关闭</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="屏幕亮度" prop="textBright">
              <el-slider
                v-model="formData.textBright"
                :min="0"
                :max="10"
                show-input
                :disabled="formData.textSw === 0"
              ></el-slider>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="显示文本" prop="textContent">
              <el-input
                v-model="formData.textContent"
                placeholder="请输入屏幕显示文本"
                clearable
                :maxlength="2"
                show-word-limit
                :disabled="formData.textSw === 0"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="显示频率" prop="textFrequency">
              <el-select
                v-model="formData.textFrequency"
                placeholder="请选择显示频率"
                clearable
                :disabled="formData.textSw === 0"
                style="width: 100%"
              >
                <el-option label="常亮" :value="0" />
                <el-option label="慢速" :value="30" />
                <el-option label="中速" :value="60" />
                <el-option label="快速" :value="120" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="文本颜色" prop="textColor">
              <el-select
                v-model="formData.textColor"
                placeholder="请选择文本颜色"
                clearable
                style="width: 100%"
              >
                <el-option label="无" value="" />
                <el-option label="红色" value="红色" />
                <el-option label="黄色" value="黄色" />
                <el-option label="绿色" value="绿色" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <div style="text-align: right; margin-top: 20px">
          <el-button size="mini" type="primary" @click="handleSave">保存</el-button>
          <el-button size="mini" @click="dialogVisible = false">取消</el-button>
        </div>
      </el-form>
    </el-dialog>
  </div>
</template>

<script>
import { getCommonConfigPage, saveCommonConfig, updateCommonConfig, deleteCommonConfig } from '@/api/jgjc/flashingModeConfig'

export default {
  dicts: ['sys_yes_no'],
  data() {
    return {
      // 遮罩层
      loading: false,
      dataList: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
      },
      // 列信息
      columns: [
        { key: 0, field: 'modeName', label: '模式名称', visible: true },
        { key: 1, field: 'modeCode', label: '模式编码', visible: true },
        { key: 2, field: 'flashSw', label: '爆闪开关', visible: true, isSwitch: true },
        { key: 3, field: 'flashBright', label: '爆闪亮度', visible: true },
        { key: 4, field: 'voiceSw', label: '语音开关', visible: true, isSwitch: true },
        { key: 5, field: 'voiceContent', label: '语音内容', visible: true },
        { key: 6, field: 'voiceCount', label: '播报次数', visible: true },
        { key: 7, field: 'textSw', label: '屏幕开关', visible: true, isSwitch: true },
        { key: 8, field: 'textContent', label: '显示文本', visible: true },
        { key: 9, field: 'textFrequency', label: '显示频率', visible: true, isFrequency: true },
        { key: 10, field: 'textColor', label: '文本颜色', visible: true, isColor: true }
      ],
      total: 0,
      ids: [],
      dialogVisible: false,
      dialogTitle: '新增',
      formData: this.getDefaultFormData(),
      rules: {
        modeName: [
          { required: true, message: '请输入模式名称', trigger: 'blur' },
          { min: 1, max: 50, message: '长度在1到50个字符', trigger: 'blur' }
        ],
        modeCode: [
          { required: true, message: '请输入模式编码', trigger: 'blur' }
        ],
        flashBright: [
          { required: true, message: '请设置爆闪亮度', trigger: 'blur' }
        ],
        // voiceContent: [
        //   { required: true, message: '请输入语音内容', trigger: 'blur', validator: this.validateVoiceContent }
        // ],
        voiceCount: [
          { required: true, message: '请设置播报次数', trigger: 'blur' }
        ],
        voiceVolume: [
          { required: true, message: '请设置播报音量', trigger: 'blur' }
        ],
        voiceInterval: [
          { required: true, message: '请设置播报间隔', trigger: 'blur' }
        ],
        voiceSpeed: [
          { required: true, message: '请设置播报语速', trigger: 'blur' }
        ],
        // textContent: [
        //   { required: true, message: '请输入显示文本', trigger: 'blur', validator: this.validateTextContent }
        // ],
        textBright: [
          { required: true, message: '请设置屏幕亮度', trigger: 'blur' }
        ],
        textFrequency: [
          { required: true, message: '请选择显示频率', trigger: 'change' }
        ],
        textColor: [
          { required: true, message: '请选择文本颜色', trigger: 'change' }
        ]
      }
    }
  },
  created() {
    this.handleQuery()
  },
  methods: {
    // 获取默认表单数据
    getDefaultFormData() {
      return {
        modeName: '',
        flashSw: 1,
        flashBright: 0,
        voiceSw: 1,
        voiceContent: '',
        voiceCount: 1,
        voiceVolume: 0,
        voiceInterval: 1,
        voiceSpeed: 1,
        textSw: 1,
        textBright: 0,
        textContent: '',
        textFrequency: 0,
        textColor: ''
      }
    },
    // 查询列表
    handleQuery() {
      this.loading = true
      getCommonConfigPage(this.queryParams).then(response => {
        this.dataList = response.rows
        this.total = response.total
        this.loading = false
      }).catch(() => {
        this.loading = false
      })
    },
    // 选择行变化
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
    },
    // 新增按钮
    handleAdd() {
      this.dialogTitle = '新增'
      this.formData = this.getDefaultFormData()
      this.dialogVisible = true
      this.$nextTick(() => {
        this.$refs.form.clearValidate()
      })
    },
    // 修改按钮
    handleUpdate(row) {
      this.dialogTitle = '修改'
      this.formData = Object.assign({}, row)
      this.dialogVisible = true
      this.$nextTick(() => {
        this.$refs.form.clearValidate()
      })
    },
    // 删除按钮
    handleDelete() {
      if (this.ids.length === 0) {
        this.$message.warning('请选择要删除的数据')
        return
      }
      this.$confirm('确认删除选中的数据吗?', '警告', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        deleteCommonConfig({ ids: this.ids }).then(() => {
          this.$message.success('删除成功')
          this.handleQuery()
        })
      })
    },
    // 保存按钮
    handleSave() {
      this.$refs.form.validate(valid => {
        if (valid) {
          const saveData = Object.assign({}, this.formData)
          const promise = this.dialogTitle === '新增' ? saveCommonConfig(saveData) : updateCommonConfig(saveData)
          promise.then(() => {
            this.$message.success('保存成功')
            this.dialogVisible = false
            this.handleQuery()
          })
        }
      })
    },
    // 验证语音内容
    validateVoiceContent(rule, value, callback) {
      if (this.formData.voiceSw === 1 && !value) {
        callback(new Error('请输入语音内容'))
      } else {
        callback()
      }
    },
    // 验证显示文本
    validateTextContent(rule, value, callback) {
      if (this.formData.textSw === 1 && !value) {
        callback(new Error('请输入显示文本'))
      } else {
        callback()
      }
    },
    // 获取颜色文本
    getColorText(color) {
      const map = {
        '': '无',
        'red': '红色',
        'yellow': '黄色',
        'green': '绿色'
      }
      return map[color] || color
    },
    // 获取频率文本
    getFrequencyText(frequency) {
      const map = {
        0: '常亮',
        30: '慢速',
        60: '中速',
        120: '快速'
      }
      return map[frequency] || frequency
    }
  }
}
</script>

<style lang="scss" scoped>
.el-divider--horizontal {
  margin: 10px 0;
}
</style>
