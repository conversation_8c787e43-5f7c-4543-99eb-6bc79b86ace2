const fields1 = [
  {
    label: '路线编码',
    prop: 'routeCode',
    type: 'description',
  },
  {
    label: '路线名称',
    prop: 'routeName',
    type: 'description',
  },
  {
    label: '路线等级',
    prop: 'routeLevel',
    type: 'dict',
    dict: 'sys_route_grade',
  },
  {
    label: '隧道编码',
    prop: 'tunnelCode',
    type: 'input',
  },
  {
    label: '隧道名称',
    prop: 'tunnelName',
    type: 'input',
  },
  {
    label: '中心桩号',
    prop: 'stake',
    type: 'stake',
  },
  {
    label: '管养分处',
    prop: 'managementMaintenanceBranchName',
    type: 'description',
  },
  {
    label: '管养单位',
    prop: 'managementMaintenanceName',
    type: 'description',
  },
  {
    label: '建成时间',
    prop: 'operationDate',
    type: 'date',
  },
]
const fields2 = [
  {
    label: '隧道长度(m)',
    prop: 'tunnelLength',
    type: 'input',
  },
  {
    label: '隧道净宽(m)',
    prop: 'tunnelWidth',
    type: 'input',
  },
  {
    label: '路面宽度(米)',
    prop: 'pavementWidth',
    type: 'input',
  },
  {
    label: '隧道净高(m)',
    prop: 'tunnelHeight',
    type: 'input',
  },
  {
    label: '岩层地质',
    prop: 'rockGeology',
    type: 'input',
  },
  {
    label: '围岩分类',
    prop: 'rockClassification',
    type: 'input',
  },
  {
    label: '衬砌类型',
    prop: 'liningType',
    type: 'dict',
    dict: 'tunnel_lining_type'
  },
  {
    label: '衬砌厚度(厘米)',
    prop: 'liningThickness',
    type: 'input',
  },
  {
    label: '洞口形式',
    prop: 'holeStyle',
    type: 'dict',
    dict: 'tunnel_hole_style'
  },
  {
    label: '路面类型',
    prop: 'pavementType',
    type: 'input',
  },
  {
    label: '照明设施',
    prop: 'lightingFacility',
    type: 'dict',
    dict: 'tunnel_lighting_facility'
  },
  {
    label: '通风设施',
    prop: 'ventilationFacility',
    type: 'dict',
    dict: 'tunnel_ventilation_facility'
  },
  {
    label: '消防设施',
    prop: 'fireFightingDevice',
    type: 'dict',
    dict: 'base_data_yes_no'
  },
  {
    label: '监控设施',
    prop: 'monitoringFacility',
    type: 'input',
  },
  {
    label: '供配电设施',
    prop: 'distributionFacility',
    type: 'input',
  },
  {
    label: '洞内纵坡',
    prop: 'holeLongitudinalSlope',
    type: 'input',
  },
  {
    label: '施工工法',
    prop: 'buildMethod',
    type: 'dict',
    dict: 'tunnel_build_method'
  },
  {
    label: '进洞门类型',
    prop: 'tunnelDoorTypeIn',
    type: 'dict',
    dict: 'tunnel_door_type'
  },
  {
    label: '出洞门类型',
    prop: 'tunnelDoorTypeOut',
    type: 'dict',
    dict: 'tunnel_door_type'
  },
  {
    label: '是否危隧',
    prop: 'isDangerous',
    type: 'dict',
    dict: 'base_data_yes_no'
  },
  {
    label: '单车道宽度(m)',
    prop: 'singleLaneWidth',
    type: 'input',
  },
  {
    label: '车道总宽度(m)',
    prop: 'totalLaneWidth',
    type: 'input',
  },
  {
    label: '检修道',
    prop: 'accessRoad',
    type: 'input',
  },
  {
    label: '检修道宽度(m)',
    prop: 'accessRoadWidth',
    type: 'input',
  },
  {
    label: '最大纵坡(%)',
    prop: 'maxLongitudinalSlope',
    type: 'input',
  },
  {
    label: '衬砌材料',
    prop: 'liningMaterial',
    type: 'dict',
    dict: 'tunnel_lining_material'
  },
  {
    label: '断面形式',
    prop: 'sectionForm',
    type: 'dict',
    dict: 'tunnel_section_form'
  },
  {
    label: '人行横洞数量',
    prop: 'personGalleryNum',
    type: 'input',
  },
  {
    label: '车行横洞数量',
    prop: 'carGalleryNum',
    type: 'input',
  },

  {
    label: '紧急停车带数量',
    prop: 'emergencyParkingStripNum',
    type: 'input',
  },
  {
    label: '安全通道数量',
    prop: 'escapeRoute',
    type: 'input',
  
  },
  {
    label: '配置等级',
    prop: 'allocationGrade',
    type: 'input',
  },
  {
    label:'机电设施类别',
    prop:'facilityType',
    type:'input'
  },
  {
    label:'共用机电设施的隧道编码',
    prop:'facilityCode',
    type:'input'
  },
  {
    label:'通风控制方式',
    prop:'ventilationWay',
    type:'input'
  },
  {
    label:'照明设施控制方式',
    prop:'lightingWay',
    type:'input'
  },
  {
    label: '进洞口防护和过渡',
    prop: 'protectionTransitionIn',
    type: 'input',
  },
  {
    label: '出洞口防护和过渡',
    prop: 'protectionTransitionOut',
    type: 'input',
  },
]
const fields3 = [
  {
    label: '设计图纸',
    prop: 'designDrawing',
    type: 'dict',
    dict: 'base_archival_data'
  },
  {
    label: '设计文件',
    prop: 'designDocument',
    type: 'dict',
    dict: 'base_archival_data'
  },
  {
    label: '施工文件',
    prop: 'constructionDocument',
    type: 'dict',
    dict: 'base_archival_data'
  },
  {
    label: '竣工图纸',
    prop: 'completedDrawing',
    type: 'dict',
    dict: 'base_archival_data'
  },
  {
    label: '验收文件',
    prop: 'acceptanceDocument',
    type: 'dict',
    dict: 'base_archival_data'
  },
  {
    label: '定期检查报告',
    prop: 'periodicInspectionReportComplete',
    type: 'dict',
    dict: 'base_archival_data'
  },
  {
    label: '特别检查报告',
    prop: 'particularlyReportComplete',
    type: 'dict',
    dict: 'base_archival_data'
  },
  {
    label: '专项检查报告',
    prop: 'specialInspectionReportComplete',
    type: 'dict',
    dict: 'base_archival_data'
  },
  {
    label: '历次维修资料',
    prop: 'previousMaintenanceComplete',
    ttype: 'dict',
    dict: 'base_archival_data'
  },
  {
    label: '档案号',
    prop: 'fileNumberComplete',
    type: 'description',
  },
  {
    label: '存档案',
    prop: 'saveArchivesComplete',
    type: 'description',
  },
  {
    label: '建档时间',
    prop: 'filingTimeComplete',
    type: 'description',
  },
]
const fields6 = [
  //   {
  //     label: '进洞照片',
  //     prop: 'inHoleImage',
  //     type: 'image',
  //   },
  //   {
  //     label: '出洞照片',
  //     prop: 'outHoleImage',
  //     type: 'image',
  //   },
  {
    label: '主管负责人',
    prop: 'mainCharger',
    type: 'input',
  },
  {
    label: '填卡人',
    prop: 'reportWriter',
    type: 'input',
  },
  {
    label: '填卡日期',
    prop: 'reportWriteDate',
    type: 'date',
  },

]
export default {
  fields1,
  fields2,
  fields3,
  fields6
}
