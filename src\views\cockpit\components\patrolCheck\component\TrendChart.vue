<template>
  <div class="trend-chart" v-loading="loading" element-loading-background="rgba(0, 0, 0, 0.3)">
    <Echarts :option="option" v-if="option" height="100%" :key="'trendKey' + type" />
  </div>
</template>

<script>
import * as echarts from "echarts";
import Echarts from '../../echarts/echarts.vue';
// api
import { getTrendData } from '@/api/cockpit/inspection';

export default {
  components: {
    Echarts
  },
  props: {
    type: {
      type: Number,
      default: 1
    }
  },
  data() {
    return {
      option: null,
      loading: true,
      xAxisData: [],
      seriesData: []
    }
  },
  mounted() { },
  watch: {
    type: {
      async handler(val) {
        if (val) {
          this.loading = true;
          this.xAxisData = [];
          this.seriesData = [];
          this.option = null;
          let data = await this.getData();
          this.loading = false;
          if (this.type == 1) {
            this.xAxisData = data.map(item => item.year);
          } else {
            data.map(item => {
              this.xAxisData.push(item.year + '.' + item.month);
            });
          }
          this.seriesData = data.map(item => item.count);
          this.initCharts();
        }
      },
      deep: true,
      immediate: true,
    }
  },
  methods: {
    getData() {
      return new Promise((resolve, reject) => {
        getTrendData(this.type).then(res => {
          if (res.code === 200 && res.rows) {
            resolve(res.rows);
          } else {
            reject(new Error('获取数据失败'));
          }
        }).catch(err => {
          reject(err);
        })
      });
    },
    initCharts() {
      var fontColor = '#FFFFFF';
      this.option = {
        backgroundColor: 'rgba(0,0,0,0)',
        grid: {
          left: '1%',
          right: '3%',
          top: '15%',
          bottom: '3%',
          containLabel: true
        },
        tooltip: {
          show: true,
        },
        legend: {
          show: false,
        },
        xAxis: [
          {
            type: 'category',
            boundaryGap: false,
            axisLabel: {
              color: fontColor,
              interval: 0,
            },
            axisLine: {
              show: true,
              lineStyle: {
                color: 'transparent'
              }
            },
            axisTick: {
              show: false,
            },
            splitLine: {
              show: false,
            },
            data: this.xAxisData || ['2016', '2017', '2018', '2019', '2020', '2021', '2022', '2023', '2025', '2025']
          }
        ],
        yAxis: [
          {
            type: 'value',
            name: '个',
            min: 0,
            nameTextStyle: {
              color: '#999999',
              padding: [0, 40, 0, 0]
            },
            axisLabel: {
              formatter: '{value}',
              textStyle: {
                color: '#999999'
              }
            },
            axisLine: {
              show: false,
              lineStyle: {
                color: '#6E7079'
              }
            },
            axisTick: {
              show: false,
            },
            splitLine: {
              show: true,
              lineStyle: {
                color: 'rgba(110,112,121,0.3)'
              },
            },
            splitArea: false,
          },
        ],
        series: [
          {
            name: '',
            type: 'line',
            stack: '总量',
            symbol: 'circle',
            symbolSize: 8,
            itemStyle: {
              normal: {
                color: '#ffffff',
                borderColor: '#00FFB7',
                lineStyle: {
                  color: "#1CFFBC",
                  width: 1
                },
                areaStyle: {
                  color: new echarts.graphic.LinearGradient(0, 1, 0, 0, [{
                    offset: 0,
                    color: 'rgba(28,255,188,0.1)'
                  }, {
                    offset: 1,
                    color: 'rgba(28,255,188,0.6)'
                  }]),
                }
              }
            },
            data: this.seriesData || [9000, 10000, 8000, 9300, 7600, 8100, 5700, 6000, 7000, 7000]
          },
        ]
      };
    }
  },
}
</script>

<style lang="scss" scoped>
.trend-chart {
  width: 100%;
  height: 100%;
}
</style>