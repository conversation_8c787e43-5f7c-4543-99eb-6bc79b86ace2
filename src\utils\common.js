/**
 * 格式化桩号显示
 * @param {number|string} value 桩号数值
 * @returns {string} 格式化后的桩号字符串 (KXXX+XXX)
 */
export function formatStake(value) {
  if (!value) return '';
  // 如果已经是格式化的形式，直接返回
  if (typeof value === 'string' && (value.includes('k') || value.includes('K'))) {
    return value.toUpperCase(); // 统一使用大写K
  }
  
  // 将输入转换为数字
  const num = parseFloat(value);
  if (isNaN(num)) return '';
  
  // 计算千位以上的部分
  const kPart = Math.floor(num / 1000);
  // 计算千位以下的部分，并确保是3位数
  const remainder = Math.round((num % 1000) * 1000) / 1000;
  const mPart = remainder.toString().padStart(3, '0');
  
  return `K${kPart}+${mPart}`;
}

/**
 * 解析格式化的桩号为数字
 * @param {string} value 格式化的桩号字符串 (KXXX+XXX)
 * @returns {number} 桩号数值
 */
export function parseStake(value) {
  if (!value) return null;
  
  // 如果已经是数字，直接返回
  if (typeof value === 'number') return value;
  
  // 如果是字符串但不包含K和+，尝试直接转换为数字
  if (typeof value === 'string' && !value.includes('K') && !value.includes('k') && !value.includes('+')) {
    const num = parseFloat(value);
    return isNaN(num) ? null : num;
  }
  
  // 处理格式化的桩号字符串
  const match = value.toUpperCase().match(/K(\d+)\+(\d{1,3})/);
  if (!match) return null;
  
  const kPart = parseInt(match[1]);
  const mPart = parseInt(match[2]);
  
  return kPart * 1000 + mPart;
} 