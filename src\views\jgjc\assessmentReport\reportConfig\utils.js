export function addSensorProp(sensor, vueComponent){
    if(!sensor.hasOwnProperty('eigenvalueType')){
        vueComponent.$set(sensor, 'eigenvalueType', {type: 'MEAN', name: "平均值", value: 0}) // 特征值类型， 绘图使用
    }
    if(!sensor.hasOwnProperty('eigenvalueTypeList')){
        vueComponent.$set(sensor, 'eigenvalueTypeList', [{type: 'MEAN', name: "平均值", value: 0}]) // 特征值类型列表，生成表格用
    }
    if(!sensor.hasOwnProperty('yAxisType')){
        vueComponent.$set(sensor, 'yAxisType', 0) // 0-左侧y轴, 1-右侧y轴
    }
    if(!sensor.hasOwnProperty('labelType')){
        vueComponent.$set(sensor, 'labelType', 0) // sensorContent类型
    }
    if(!sensor.hasOwnProperty('color')){
        vueComponent.$set(sensor, 'color', "red") // sensorContent类型
    }
    if(!sensor.hasOwnProperty('useInstallTime')){
        vueComponent.$set(sensor, 'useInstallTime', 0) // 是否启用安装时间作为开始时间
    }
    if(!sensor.hasOwnProperty('installTime')){
        vueComponent.$set(sensor, 'installTime', "2018-01-01") // 安装时刻
    }
    if(!sensor.hasOwnProperty('use3sigma')){
        vueComponent.$set(sensor, 'use3sigma', 0) // 是否启用异常值剔除
    }
    if(!sensor.hasOwnProperty('windowLength')){
        vueComponent.$set(sensor, 'windowLength', 100) // 异常值剔除窗口大小
    }
    if(!sensor.hasOwnProperty('sigmaInterpolate')){
        vueComponent.$set(sensor, 'sigmaInterpolate', 0) // 异常值剔除是否插值
    }
    if(!sensor.hasOwnProperty('useUpDownLimit')){
        vueComponent.$set(sensor, 'useUpDownLimit', 0) // 是否启用上下限
    }
    if(!sensor.hasOwnProperty('upLimit')){
        vueComponent.$set(sensor, 'upLimit', 1) // 上限大小
    }
    if(!sensor.hasOwnProperty('downLimit')){
        vueComponent.$set(sensor, 'downLimit', -1) // 下限大小
    }
    if(!sensor.hasOwnProperty('upDownInterpolate')){
        vueComponent.$set(sensor, 'upDownInterpolate', 0) // 上下限剔除是否插值
    }
}

export function addContentProp(content, vueComponent){
    if(!content.hasOwnProperty('description')){
        vueComponent.$set(content, 'description', '') // 监测内容层级描述字段
    }
}

export function addLocationProp(location, vueComponent){
    if(!location.hasOwnProperty('usageMode')){
        vueComponent.$set(location, 'usageMode', 0) // 0-绘图, 1-表格
    }
    if(!location.hasOwnProperty('granularityNum')){
        vueComponent.$set(location, 'granularityNum', 10)
    }
    if(!location.hasOwnProperty('granularityType')){
        vueComponent.$set(location, 'granularityType', 3)
    }
    if(!location.hasOwnProperty('tableDirection')){
        vueComponent.$set(location, 'tableDirection', 0) // 表格页面方向
    }
    if(!location.hasOwnProperty('algorithmName')){
        vueComponent.$set(location, 'algorithmName', 0) // 绘图算法类型
    }
    if(!location.hasOwnProperty('leftAxisName')){
        vueComponent.$set(location, 'leftAxisName', '') // 左侧y轴名称
    }
    if(!location.hasOwnProperty('rightAxisName')){
        vueComponent.$set(location, 'rightAxisName', '') // 右侧y轴名称
    }
}

/**
 * obj2对象拷贝obj1对象上同名属性的值
 * @param obj1
 * @param obj2
 */
export function copyProp(obj1, obj2){
    for (let key in obj1) {
        if (obj2.hasOwnProperty(key)) {
            obj2[key] = obj1[key];
        }
    }
}

/**
 * 按需拷贝
 * @param sensor1
 * @param sensor2
 */
export function copySensorProp(sensor1, sensor2) {
    sensor1.id = sensor2.id;

    sensor1.eigenvalueType = sensor2.eigenvalueType;
    sensor1.eigenvalueTypeList = sensor2.eigenvalueTypeList;
    sensor1.yAxisType = sensor2.yAxisType;
    sensor1.labelType = sensor2.labelType;
    sensor1.color = sensor2.color;
    sensor1.useInstallTime = sensor2.useInstallTime;
    sensor1.installTime = sensor2.installTime;
    sensor1.use3sigma = sensor2.use3sigma;
    sensor1.windowLength = sensor2.windowLength;
    sensor1.sigmaInterpolate = sensor2.sigmaInterpolate;
    sensor1.useUpDownLimit = sensor2.useUpDownLimit;
    sensor1.upLimit = sensor2.upLimit;
    sensor1.downLimit = sensor2.downLimit;
    sensor1.upDownInterpolate = sensor2.upDownInterpolate;
}

export const colorList = [{CNName: "红色", ENName: "red"}, {CNName: "橙色", ENName: "orange"}, {CNName: "绿色", ENName: "green"},
    {CNName: "青色", ENName: "cyan"}, {CNName: "蓝色", ENName: "blue"}, {CNName: "紫罗兰", ENName: "violet"}, {CNName: "粉色", ENName: "pink"},
    {CNName: "暗紫色", ENName: "darkviolet"}, {CNName: "暗绿色", ENName: "darkgreen"}, {CNName: "金色", ENName: "gold"}, {CNName: "灰色", ENName: "grey"},
    {CNName: "紫色", ENName: "purple"}, {CNName: "深蓝色", ENName: "navy"}, {CNName: "巧克力色", ENName: "chocolate"}, {CNName: "黄色", ENName: "yellow"}
]
