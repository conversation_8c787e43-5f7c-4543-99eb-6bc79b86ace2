<template>
  <div class='app-container'>
    <el-row :gutter='20'>
      <!--部门数据-->
      <el-col :span='relaNav ? 4 : 0' :xs='24' class='leftDiv'>
        <!--折叠图标-->
        <div class='leftIcon' @click='relaNav = false'>
          <span class='el-icon-caret-left'></span>
        </div>
        <div class='head-container' style='width: 300px'>
          <el-tree
            ref='tree'
            :data='filteredTreeData'
            :default-expanded-keys='[1]'
            :expand-on-click-node='false'
            :props='defaultProps'
            highlight-current
            node-key='code'
            @node-click='handleNodeClick'
          >
          </el-tree>
        </div>
      </el-col>
      <!--角色数据-->
      <el-col :span='relaNav ? 20 : 24' :xs='24'>
        <!--展开图标-->
        <div v-show='!relaNav' class='rightIcon' @click='relaNav = true'>
          <span class='el-icon-caret-right'></span>
        </div>
        <el-row :gutter='10'>
          <el-form ref='queryForm' :inline='true' :model='queryParams' label-width='68px' size='small'>
            <el-form-item label='' prop='dataCode'>
              <el-input v-model='queryParams.dataCode' clearable placeholder='请输入数据编码' />
            </el-form-item>
            <el-form-item label='' prop='code'>
              <el-input v-model='queryParams.code' clearable placeholder='请输入编码' />
            </el-form-item>
            <el-form-item>
              <el-button icon='el-icon-search' size='mini' type='primary' @click='handleQuery'>搜索</el-button>
              <el-button icon='el-icon-refresh' size='mini' @click='resetQuery'>重置</el-button>
            </el-form-item>
          </el-form>
        </el-row>
        <el-row :gutter='10' class='mb8'>
<!--          <el-col :span='1.5'>-->
<!--            <el-button-->
<!--              icon='el-icon-plus'-->
<!--              size='mini'-->
<!--              type='primary'-->
<!--              @click='handleAdd'-->
<!--            >新增-->
<!--            </el-button>-->
<!--          </el-col>-->
          <el-col :span='1.5'>
            <el-button
              icon='el-icon-delete'
              size='mini'
              type='danger'
              @click='handleDelete'
            >删除
            </el-button>
          </el-col>
        </el-row>
        <!-- 筛选区结束 -->
        <!-- 数据表格开始 -->
        <div class='tableDiv'>
          <el-table v-adjust-table v-loading='loading' :data='dataList'
                    :height="'calc(100vh - 260px)'" border size='mini'
                    @selection-change='handleSelectionChange'>
            <el-table-column align='center' fixed='left' type='selection' width='55'></el-table-column>
            <el-table-column align='center' fixed label='序号' type='index' width='100'></el-table-column>
            <template v-for='(column, index) in columns'>
              <el-table-column v-if='column.visible' :key='index' :label='column.label' :prop='column.field' :width='column.width'
                               align='center' show-overflow-tooltip>
                <template slot-scope='scope'>
                  <span>{{ scope.row[column.field] }}</span>
                </template>
              </el-table-column>
            </template>
            <el-table-column align='center' fixed='right' label='操作' width='150'>
              <template slot-scope='scope'>
                <el-button
                  icon='el-icon-edit'
                  size='mini'
                  type='text'
                  @click='handleEdit(scope.row)'
                >编辑
                </el-button>
              </template>
            </el-table-column>
          </el-table>
          <pagination
            v-show='total > 0'
            :limit.sync='queryParams.pageSize'
            :page.sync='queryParams.pageNum'
            :total='total'
            @pagination='handleQuery'
          />
        </div>
        <!-- 数据表格结束 -->
      </el-col>
    </el-row>
    <el-dialog :dialogTitle='dialogTitle' v-if='dialogVisible' :visible.sync='dialogVisible' width='800px' append-to-body>
      <el-form ref="elForm" :model="formData" :rules="rules" label-width="100px" size="medium">
        <el-row :gutter="20">
          <!-- 第一行 -->
          <el-col :span="12">
            <el-form-item label="数据编码" prop="dataCode">
              <el-input v-model="formData.dataCode" placeholder="请输入数据编码" clearable />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="编码" prop="code">
              <el-input v-model="formData.code" placeholder="请输入编码" clearable />
            </el-form-item>
          </el-col>

          <!-- 第二行 -->
          <el-col :span="12">
            <el-form-item label="位置" prop="position">
              <el-input v-model="formData.position" placeholder="请输入位置信息" clearable />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="启用状态" prop="enabled">
              <el-switch
                v-model="formData.enabled"
                :active-value="true"
                :inactive-value="false"
                active-text="启用"
                inactive-text="禁用"
              />
            </el-form-item>
          </el-col>

          <!-- 第三行 -->
          <el-col :span="12">
            <el-form-item label="隐藏状态" prop="hideSensor">
              <el-switch
                v-model="formData.hideSensor"
                :active-value="true"
                :inactive-value="false"
                active-text="隐藏"
                inactive-text="显示"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="在线检查" prop="checkOnline">
              <el-switch
                v-model="formData.checkOnline"
                :active-value="true"
                :inactive-value="false"
                active-text="开启"
                inactive-text="关闭"
              />
            </el-form-item>
          </el-col>

          <!-- 第四行 -->
          <el-col :span="12">
            <el-form-item label="排序号" prop="orders">
              <el-input-number
                v-model="formData.orders"
                :min="0"
                controls-position="right"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>

          <!-- 第五行 - 说明（单独一行） -->
          <el-col :span="24">
            <el-form-item label="说明" prop="description">
              <el-input
                v-model="formData.description"
                type="textarea"
                :rows="3"
                placeholder="请输入详细说明"
                clearable
              />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div style="text-align: right">
        <el-button type='primary' @click='handleConfirm'>确 定</el-button>
        <el-button @click='cancel'>取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getSensorTreeNode, getSensorPage, updateSensor, saveSensor, deleteSensor } from '@/api/jgjc/baseInfo/sensorManagement'
import { getJCDomainTree } from '@/api/jgjc/baseInfo/alarmLog'

export default {
  name: 'ContentManagement',
  data() {
    return {
      // 遮罩层
      loading: false,
      // 左侧组织树
      relaNav: true,
      relaOptions: [],
      filteredTreeData: [],
      defaultProps: {
        children: 'children',
        label: 'label',
        isLeaf: (data) => {
          // 通过该字段判断是否是叶子节点（没有子节点）
          // 这里假设接口返回的节点中，有hasChildren字段标识是否有子节点
          return !data.children
        },
      },
      loadedKeys: new Set(), // 记录已加载过的节点key，避免重复加载
      // 总条数
      total: 0,
      dataList: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
      },
      // 列信息
      columns: [
        { key: 0, width: '150', field: 'dataCode', label: '数据编码', visible: true },
        { key: 1, width: '150', field: 'code', label: '编码', visible: true },
        { key: 2, width: '200', field: 'position', label: '位置', visible: true },
        {
          key: 3,
          width: '100',
          field: 'enabled',
          label: '启用状态',
          visible: true,
          formatter: (row) => row.enabled ? '已启用' : '已禁用'
        },
        {
          key: 4,
          width: '100',
          field: 'hideSensor',
          label: '隐藏状态',
          visible: true,
          formatter: (row) => row.hideSensor ? '是' : '否'
        },
        {
          key: 5,
          width: '120',
          field: 'checkOnline',
          label: '在线检查',
          visible: true,
          formatter: (row) => row.checkOnline ? '开启' : '关闭'
        },
        { key: 6, width: '80', field: 'orders', label: '序号', visible: true },
        { key: 7, width: '200', field: 'description', label: '说明', visible: true }
      ],
      ids: [],
      structureId: '',
      monitoringContentId: '',
      dialogTitle: '', // 对话框标题
      dialogVisible: false, // 对话框显示状态
      formData: {},
      deviceNameList: [],
      rules: {
        dataCode: [
          { required: true, message: '数据编码不能为空', trigger: 'blur' }
        ],
        code: [
          { required: true, message: '编码不能为空', trigger: 'blur' }
        ],
        position: [
          { required: true, message: '位置信息不能为空', trigger: 'blur' }
        ],
        description: [
          { max: 500, message: '说明长度不能超过500个字符', trigger: 'blur' }
        ]
      }
    }
  },
  created() {
    this.getDeptTree()
  },
  methods: {
    // 查询部门下拉树结构
    getDeptTree() {
      getJCDomainTree({}).then(response => {
        this.relaOptions = response.data
        this.filteredTreeData = [...this.relaOptions]
      })
    },
    // 树节点点击事件
    handleNodeClick(nodeData, node) {
      // 如果节点没有子节点且未被加载过
      if (node.level == 4 && node.level == 4 && !node.childNodes.length && nodeData.structureId && !this.loadedKeys.has(nodeData.structureId)) {
        this.loadChildren(node)
      }
      let params = {}
      if (node.level == 1) {
        params.domainName = nodeData.deptName
      } else if (node.level == 2) {
        params.domainName = node.parent.data.deptName
        params.roadName = nodeData.label
      } else if (node.level == 3) {
        params.domainName = node.parent.parent.data.deptName
        params.roadName = node.parent.data.label
        params.structureType = nodeData.structureType
      } else {
        params = nodeData
        // 判断params里面有没有structureId 没有就从父级找 一直找到为止 找到后赋值给params
        let tempNode = node
        while (!tempNode.data.structureId) {
          tempNode = tempNode.parent
        }
        params.structureId = tempNode.data.structureId
        this.structureId = params.structureId
      }
      this.queryParams = {
        pageNum: 1,
        pageSize: 10,
        level: node.level,
        ...params
      }
      this.handleQuery()
    },

    // 加载子节点数据
    async loadChildren(node) {
      const nodeData = node.data
      // 显示加载状态
      node.loading = true
      try {
        const res = await getSensorTreeNode({ structureId: nodeData.structureId, pageNum: 1, pageSize: 100 })
        if (res.data && res.data.length > 0) {
          // 给树节点添加子节点（Vue.set确保响应式）
          this.$set(nodeData, 'children', res.data)
          // 展开当前节点（可选）
          this.$nextTick(() => {
            node.expanded = true
          })
        }
        // 标记为已加载
        this.loadedKeys.add(nodeData.id)
      } finally {
        node.loading = false
      }
    },
    handleQuery() {
      this.loading = true
      getSensorPage(this.queryParams).then(res => {
        this.dataList = res.rows
        this.total = res.total
        this.loading = false
      }).catch(err => {
        this.loading = false
        console.error(err)
      })
    },
    resetQuery() {
      this.queryParams = {
        pageNum: 1,
        pageSize: 10,
      }
      this.handleQuery()
    },
    handleSelectionChange(e) {
      this.ids = e.map(item => item.id)
    },
    handleDelete() {
      if (this.ids.length <= 0) {
        this.$message.warning('请勾选需要删除的数据')
        return
      }
      this.$confirm('是否确认删除选中的数据？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }).then(() => {
        this.loading = true
        deleteSensor({ ids: this.ids }).then(res => {
          if (res.code === 200) {
            this.$message.success('删除成功')
            this.handleQuery()
          } else {
            this.$message.error(res.msg)
          }
          this.loading = false
        }).catch(err => {
          this.loading = false
          console.error(err)
        })
      })
    },
    handleAdd() {
      if (!this.monitoringContentId) {
        this.$message.warning('请选择监测内容')
        return
      }
      this.formData = {}
      this.formData.structureId = this.structureId
      this.formData.monitoringContentId = this.monitoringContentId
      this.dialogTitle = '新增'
      this.dialogVisible = true
    },
    handleEdit(row) {
      this.dialogTitle = '修改'
      this.formData = { ...row }
      this.dialogVisible = true
    },
    handleConfirm() {
      this.$refs['elForm'].validate(valid => {
        if (valid) {
          if (this.formData.id) {
            updateSensor(this.formData).then(res => {
              this.$modal.msgSuccess('修改成功')
              this.dialogVisible = false
              this.handleQuery()
            })
          } else {
            saveSensor(this.formData).then(res => {
              this.$modal.msgSuccess('新增成功')
              this.dialogVisible = false
              this.handleQuery()
            })
          }
        }
      })
    },
    cancel() {
      this.dialogVisible = false
      this.formData = {}
    },
  },
}
</script>

<style scoped>
.leftDiv {
  border-right: 1px solid #d8dce5;
  min-height: calc(100vh - 100px);
  overflow-y: auto;
  height: calc(80vh - 150px);
  position: relative;
  top: -20px;
  padding-top: 10px;
  background-color: white;
}

.leftIcon {
  border: 1px solid #dcdfe6;
  border-radius: 8px;
  width: 16px;
  height: 50px;
  line-height: 50px;
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
  position: absolute;
  right: 0;
  top: 300px;
  z-index: 2;
}

.leftIcon:hover {
  background-color: #dcdfe6;
}

.rightIcon {
  border: 1px solid #dcdfe6;
  border-radius: 8px;
  width: 16px;
  height: 50px;
  line-height: 50px;
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
  position: absolute;
  left: -10px;
  top: 280px;
  z-index: 10;
  background: white;
}

.rightIcon:hover {
  background-color: #dcdfe6;
}

.tableDiv {
  margin-top: 20px;
}
</style>
<style lang='scss' scoped>
@import "@/assets/styles/business.scss";
</style>
