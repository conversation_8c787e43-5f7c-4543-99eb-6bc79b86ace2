<template>
  <div class="el-table-body">
    <el-table :data="data" height="100%" ref="rolltable" @mouseenter.native="stopScroll"
      @mouseleave.native="startScroll">
      <el-table-column v-for="(col, index) in columns" :prop="col.prop" :key="col.id" :label="col.label"
        :min-width="col.width" :align="index == 0 ? '' : 'center'" show-overflow-tooltip>
        <template slot-scope="{row, $index}">
          <div v-if="col.prop === 'index'" :style="indexStyle($index)">
            {{ $index + 1 }}
          </div>
          <span v-else>{{ row[col.prop] }}</span>
        </template>
      </el-table-column>
    </el-table>

  </div>
</template>

<script>
import { isBigScreen } from '../util/utils';

export default {
  name: 'tables',
  props: {
    columns: {
      type: Array,
      default: () => []
    },
    data: {
      type: Array,
      default: () => []
    },
    isScroll: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      isBig: isBigScreen(),
      rolltimer: null,
    }
  },
  mounted() {
    this.$nextTick(() => {
      this.tableScroll(false)
    })
  },
  destroyed() {
    this.tableScroll(true);
  },
  methods: {
    tableScroll(stop) {
      if (this.data && this.data.length <= 6) return;
      if (!this.isScroll) return;
      if (stop) {
        clearInterval(this.rolltimer)
        return
      }
      const table = this.$refs.rolltable
      const divData = table.bodyWrapper
      this.rolltimer = setInterval(() => {
        divData.scrollTop += 1
        if (divData.clientHeight + divData.scrollTop + 1 > divData.scrollHeight) {
          if (table.tableData.length > 5) {
            divData.scrollTop = 0
          }
        }
      }, 35)
    },
    startScroll() {
      this.tableScroll(false);
    },

    stopScroll() {
      this.tableScroll(true);
    },
    indexStyle(index) {
      let bs = '';
      let border = '';
      if (index === 0) {
        bs = 'inset 0px 0px 6px 0px #F53F3F'
        border = '1px solid #F53F3F'
      } else if (index === 1) {
        bs = 'inset 0px 0px 6px 0px #FF8F1F'
        border = '1px solid #FF8000'
      } else if (index === 2) {
        bs = 'inset 0px 0px 6px 0px #FDAF34'
        border = '1px solid #FDAF34'
      } else {
        bs = 'inset 0px 0px 6px 0px #0154FB'
        border = '1px solid #009DFF'
      }

      let obj = {
        textAlign: 'center',
        width: this.isBig ? '40px' : '22px',
        height: this.isBig ? '40px' : '22px',
        lineHeight: this.isBig ? '40px' : '22px',
        background: 'rgba(4,17,48,0.4)',
        boxShadow: bs,
        border,
      }
      return obj
    }
  }
}
</script>

<style lang="scss" scoped>
@import "@/assets/styles/utils.scss";

.el-table-body {
  width: 100%;
  height: 100%;
  padding: 10px;
  overflow-x: hidden;

  ::v-deep .el-table {
    background-color: unset;
    border: unset;
    overflow-x: hidden;

    &::before {
      background-color: unset;
    }

    tr {
      background-color: unset;
    }

    td {
      color: #ffffff;
    }

    th.is-leaf {
      border-bottom: none;
    }

    thead {
      background-color: unset;
      color: #00FDFD;
    }

    .el-table__header-wrapper tr th {
      background-color: rgba(2, 43, 91, 1);
      color: #00FDFD !important;
      font-size: vwpx(26px);
    }

    tbody {
      background-color: unset;
      border: none;

      .el-table__row {
        border-bottom: none !important;
        cursor: pointer;

        .el-table__cell {
          border-bottom: 1px solid rgba(2, 57, 128, 1);
          padding: 0;
          cursor: pointer;
        }
      }
    }

    .el-table__body tr:hover>td {
      background-color: unset;
    }

    .el-table__body tr.current-row>td.el-table__cell {
      background-color: rgba(0, 115, 232, 0);
    }

    .el-table__inner-wrapper::before {
      background-color: unset;
    }

    th.el-table__cell {
      background-color: unset;
      color: #ffffff;
    }

    .el-table__row {
      height: vwpx(65px) !important;
    }


    .el-table__body-wrapper {
      &::-webkit-scrollbar {
        // 整个滚动条
        width: 0; // 纵向滚动条的宽度
        background: rgba(213, 215, 220, 0.3);
        border: none;
      }

      &::-webkit-scrollbar-track {
        // 滚动条轨道
        border: none;
      }
    }
  }
}
</style>