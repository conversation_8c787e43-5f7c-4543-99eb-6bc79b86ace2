export const requestIntercept = {
  methods: {
    /**
     * 请求处理：为axios请求添加返回拦截，成功返回result，失败弹窗提醒（提醒内容为后端返回message）
     * @param func axios请求函数
     * @param params 请求参数
     * @param isShowSuccess 是否要展示"成功"信息
     * @param successMessage "成功"信息（默认为处理成功后，后端返回的message）
     */
    procRequest(func, params, isShowSuccess, successMessage) {
      return new Promise( (resolve, reject) => {
        func(params).then(res => {
          const { success, message, result } = res
          if (success) {
            if (isShowSuccess) {
              const successInfo = successMessage ?? message
              this.$message({
                type: 'success',
                message: successInfo
              })
            }
            resolve(result)
          } else {
            this.$message({
              type: 'error',
              message
            })
            reject(message)
          }
        }).catch(err => {
          reject(err)
        })
      })
    },

    /**
     * 数据处理请求封装：拦截数据处理结果，仅返回处理正确结果
     * @param func 数据处理函数
     * @param params 请求参数
     * @returns {Promise<unknown>}
     */
    dataProcessRequest(func, params) {
      return new Promise(async (resolve, reject) => {
        this.procRequest(func, params).then(resList => {
          let delay = 0
          const successResList = resList.filter(res => {
            const errMsg = res.processedData?.errMsg
            if (res.processedData?.errMsg) {
              setTimeout(() => {
                this.$message.error(res.name ? `[${res.name}]: ${errMsg}` : errMsg);
              }, delay);
              delay += 500
              return false
            } else {
              return true
            }
          })
          if (successResList.length > 0) {
            resolve(successResList)
          } else {
            reject("算法调用失败")
          }
        }).catch(err => {
          reject(err)
        })
      })
    },

  }
}
