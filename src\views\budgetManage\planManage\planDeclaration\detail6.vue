<template>
  <div class="maindiv">
    <el-form :model="formData"  ref="queryForm" :inline="true" class="demo-form-inline" :rules="rules" label-width="150px">
      <el-form-item label="里程桩号" prop="mileageStr">
        <el-input v-model="formData.mileageStr" placeholder="请输入里程桩号" style="width:220px" clearable :disabled="optionModel=='read'">
        </el-input>
      </el-form-item>
      <el-form-item label="隧道里程(单洞)(km)" prop="tunnlMileage">
        <el-input v-model="formData.tunnlMileage" type="number" @input="handleInput" placeholder="请输入隧道里程" style="width:220px" clearable :disabled="optionModel=='read'">
        </el-input>
      </el-form-item>
      <el-form-item label="公路等级" prop="hgrade">
        <dict-select type="sys_budget_route_grade" onlyTailNode @change="routeGradeChange" clearable v-model="formData.hgrade" placeholder="请选择公路等级" style="width: 220px" :disabled="optionModel=='read'"></dict-select>
      </el-form-item>
      <el-form-item label="缺陷责任期后年数" prop="dutyYear">
        <el-input v-model="formData.dutyYear" type="number" @input="calcBaseFund" placeholder="请输入缺陷责任期后年数" style="width:220px" clearable :disabled="optionModel=='read'">
        </el-input>
      </el-form-item>
      <el-form-item label="费用标准" prop="fundStandard">
        <el-input v-model="formData.fundStandard" type="number" placeholder="请输入费用标准" style="width:220px" disabled>
        </el-input>
      </el-form-item>
      <el-form-item label="调整系数" prop="coefficient">
        <el-input v-model="formData.coefficient" type="number" @input="handleInput" placeholder="请输入调整系数" style="width:220px" clearable :disabled="optionModel=='read'">
        </el-input>
      </el-form-item>
      <el-form-item label="总费用" prop="sumFund">
        <el-input v-model="formData.sumFund" type="number" placeholder="请输入总费用" style="width:220px" clearable :disabled="optionModel=='read'">
        </el-input>
      </el-form-item>
      <el-form-item label="备注" prop="remark">
        <el-input v-model="formData.remark" type="textarea" placeholder="请输入备注" style="width:600px" clearable :disabled="optionModel=='read'">
        </el-input>
      </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer" style="display: flex;justify-content: flex-end;margin-top: 10px;">
      <el-button type="primary" @click="onSubmit" v-if="optionModel != 'read'" :loading="submitLoading">确定</el-button>
      <el-button @click="closeDialog()">取消</el-button>
    </span>
  </div>

</template>

<script>
import { AddPlanDetail, EditPlanDetail } from "@/api/budgetManage/planManage";
export default {
  props: {
    planId: {
      type: String,
      default: "",
    },
    typeId: {
      type: [String, Number],
      default: "",
    },
    companyType: {
      type: Number,
      default: 0,
    },
    currentRow: {
      type: Object,
      default: null,
    },
    optionModel: {
      type: String,
      default: "add",
    },
  },
  watch:{
    currentRow(val){
      if(val){
        this.typeId = val.typeId
        this.companyType = val.companyType
        this.formData = { ...val }
      }
    }
  },
  data() {
    return {
      formData: {
        mileageStr:"",
        isDuty:"",
        mianMileage:null,
        tunnlMileage:null,
        bridgeMileage:null,
        hgrade:null,
        lane:null,
        fundStandard:null,
        coefficient:1,
        dutyYear:0,
        sumFund:null,
        remark:"",
      },
      rules: {
        mileageStr: [
          { required: true, message: '请输入里程桩号', trigger: 'blur' },
        ],
        tunnlMileage:[
          { required: true, message: '请输入隧道里程', trigger: 'blur' },
        ],
        hgrade:[
          { required: true, message: '请选择公路等级', trigger: 'blur' },
        ],
        dutyYear:[
          { required: true, message: '请输入缺陷责任期后年数', trigger: 'blur' },
        ],
        coefficient:[
          { required: true, message: '请输入调整系数', trigger: 'blur' },
        ],
        sumFund:[
          { required: true, message: '请输入总费用', trigger: 'blur' },
        ]
      },
      projectNames:{
        19:"隧道机电维修维护费"
      },
      submitLoading: false
    }
  },
  methods:{
    handleInput(){
    let tunnelLength = parseFloat(this.formData.tunnlMileage) || 0,
      tollRatio = parseFloat(this.formData.coefficient) || 0,
      basefund = parseFloat(this.formData.fundStandard) || 0;
      this.formData.sumFund = Math.round(tunnelLength * basefund * tollRatio);
    },
    routeGradeChange(){
      this.calcBaseFund()
    },
    laneChange(){
      this.calcBaseFund()
    },
    calcBaseFund(){
      if(this.formData.hgrade==1){ // 高速公路
        switch(this.formData.dutyYear)
        {
          case '0':
            this.formData.fundStandard = 21600
            break;
          case '1':
            this.formData.fundStandard = 54000
            break;
          case '2':
            this.formData.fundStandard = 55200
            break;
          case '3':
            this.formData.fundStandard = 56400
            break;
          case '4':
            this.formData.fundStandard = 57600
            break;
          case '5':
            this.formData.fundStandard = 58800
            break;
          default:
            this.formData.fundStandard = 60000
            break;
        }
      }
      else{
        switch(this.formData.dutyYear)
        {
          case '0':
            this.formData.fundStandard = 14400
            break;
          case '1':
            this.formData.fundStandard = 30000
            break;
          case '2':
            this.formData.fundStandard = 31200
            break;
          case '3':
            this.formData.fundStandard = 32400
            break;
          case '4':
            this.formData.fundStandard = 33600
            break;
          case '5':
            this.formData.fundStandard = 34800
            break;
          default:
            this.formData.fundStandard = 36000
            break;
        }
      }

      this.handleInput();
    },
    onSubmit() {
      console.log("this.planId",this.planId)
      console.log("this.typeId",this.typeId)
      this.$refs["queryForm"].validate(async (valid) => {
          if (valid) {
            this.submitLoading = true
            // 提交接口
            if(this.currentRow){ // 修改
              this.formData.planId = this.planId
              const res = await EditPlanDetail(this.formData)
              this.submitLoading = false
              if(res.code==200){
                this.$modal.msgSuccess('保存成功')
                this.closeDialog()
              }
            }else{ // 新增
              const params = {
                projectName:this.projectNames[this.typeId],
                companyType:this.companyType,
                planId:this.planId,
                typeId:this.typeId,
                ...this.formData,
              }
              const res = await AddPlanDetail(params)
              this.submitLoading = false
              if(res.code==200){
                this.$modal.msgSuccess('保存成功')
                this.closeDialog()
              }
            }
          }
        });
    },
    closeDialog(){
      this.$refs["queryForm"].resetFields();
      this.$emit('closeDialog')
    }
  },
  created(){
    if(this.currentRow){ // 编辑 查看 状态回显
      this.typeId = this.currentRow.typeId
      this.companyType = this.currentRow.companyType
      this.formData = { ...this.currentRow }
    }
  },
}

</script>


<style scoped lang="scss">
.maindiv{

}

</style>
<style lang="scss" scoped>
@import "@/assets/styles/business.scss";
</style>
