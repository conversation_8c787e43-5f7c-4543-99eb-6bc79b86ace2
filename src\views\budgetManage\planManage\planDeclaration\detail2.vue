<template>
  <div class="maindiv">
    <el-form :model="formData" ref="queryForm" :inline="true" :rules="rules" label-width="80px">
      <el-form-item label="里程桩号" prop="mileageStr">
        <el-input v-model="formData.mileageStr" placeholder="请输入里程桩号" style="width:220px" clearable :disabled="optionModel=='read'">
        </el-input>
      </el-form-item>
      <el-form-item label="车辆数" prop="carNum">
        <el-input v-model="formData.carNum" @input="handleInput" placeholder="请输入车辆数" style="width:220px" clearable :disabled="optionModel=='read'">
        </el-input>
      </el-form-item>
      <el-form-item label="费用标准" prop="fundStandard">
        <el-input v-model="formData.fundStandard" @input="handleInput" placeholder="请输入费用标准" style="width:220px" clearable :disabled="optionModel=='read'">
        </el-input>
      </el-form-item>
      <el-form-item label="总费" prop="sumFund">
        <el-input v-model="formData.sumFund" placeholder="请输入总费" style="width:220px" clearable :disabled="optionModel=='read'">
        </el-input>
      </el-form-item>
      <el-form-item label="备注" prop="remark">
        <el-input type="textarea" v-model="formData.remark" style="width:220px" placeholder="请输入备注" clearable :disabled="optionModel=='read'">
        </el-input>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="onSubmit" v-if="optionModel!='read'" :loading="submitLoading">确定</el-button>
        <el-button @click="closeDialog()">取消</el-button>
      </el-form-item>
    </el-form>
  </div>

</template>

<script>
import { AddPlanDetail, EditPlanDetail } from "@/api/budgetManage/planManage";
export default {
  props: {
    planId: {
      type: String,
      default: "",
    },
    typeId: {
      type: [String, Number],
      default: "",
    },
    companyType: {
      type: Number,
      default: 0,
    },
    currentRow: {
      type: Object,
      default: null,
    },
    optionModel: {
      type: String,
      default: "add",
    },
  },
  data() {
    return {
      formData: {},
      rules: {
        mileageStr: [
          { required: true, message: '请输入里程桩号', trigger: 'blur' },
        ],
        carNum: [
          { required: true, message: '请输入车辆数', trigger: 'blur' },
        ],
        fundStandard: [
          { required: true, message: '请输入费用标准', trigger: 'blur' },
        ],
        sumFund: [
          { required: true, message: '请输入总费用', trigger: 'blur' },
        ],
      },
      projectNames:{
        5:"消防、救援车辆使用费"
      },
      submitLoading:false
    }
  },
  watch: {
    currentRow(val){
      if(val){
        this.typeId = val.typeId
        this.companyType = val.companyType
        this.formData = { ...val }
      }
    }
  },
  methods:{
    handleInput(){
      let fundStandard = this.formData.fundStandard || 0
      let carNum = this.formData.carNum || 0
      this.formData.sumFund = carNum*fundStandard;
    },
    onSubmit() {
      console.log("this.planId",this.planId)
      console.log("this.typeId",this.typeId)
      this.$refs["queryForm"].validate(async (valid) => {
          if (valid) {
            this.submitLoading = true
            // 提交接口
            if(this.currentRow){ // 修改
              const res = await EditPlanDetail(this.formData)
              this.submitLoading = false
              if(res.code==200){
                this.$modal.msgSuccess('保存成功')
                this.closeDialog()
              }
            }else{ // 新增
              const params = {
                projectName:this.projectNames[this.typeId],
                companyType:this.companyType,
                planId:this.planId,
                typeId:this.typeId,
                ...this.formData,
              }
              const res = await AddPlanDetail(params)
              this.submitLoading = false
              if(res.code==200){
                this.$modal.msgSuccess('保存成功')
                this.closeDialog()
              }
            }
          }
        });
    },
    closeDialog(){
      this.$refs["queryForm"].resetFields();
      this.$emit('closeDialog')
    }
  },
  created(){
    if(this.currentRow){ // 编辑 查看 状态回显
      this.typeId = this.currentRow.typeId
      this.companyType = this.currentRow.companyType
      this.formData = { ...this.currentRow }
    }
  },
}

</script>


<style scoped lang="scss">
.maindiv{
  .el-form-item{
    width: 100%;
    text-align: center;
  }
}

</style>
<style lang="scss" scoped>
@import "@/assets/styles/business.scss";
</style>
