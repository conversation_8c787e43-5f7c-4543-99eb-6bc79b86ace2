<template>
  <div class="daily-maintenance">
    <div
      v-for="(item, index) in list"
      :key="index"
      class="list"
      :style="{ height: isBig ? '8.5vh' : '7vh' }"
    >
      <span class="top-left"></span>
      <span class="top-right"></span>
      <span class="bottom-left"></span>
      <span class="bottom-right"></span>
      <div class="name">{{ item.name }}</div>
      <div class="divider"></div>
      <div class="value" :style="{ color: item.color }">
        {{ item.value }} <small>{{ item.unit }}</small>
      </div>
    </div>
  </div>
</template>

<script>
import { isBigScreen } from "../../util/utils";
import { getDiseaseCountInfo } from "@/api/cockpit/index";

export default {
  name: "DailyMaintenance",
  data() {
    return {
      isBig: isBigScreen(),
      list: [],
      list1: [
        // 备用list 当数据请求失败时用
        {
          name: "待下发病害",
          value: "-",
          unit: "个",
        },
        {
          name: "待施工病害",
          value: "-",
          unit: "个",
        },
        {
          name: "施工中病害",
          value: "-",
          unit: "个",
        },
        {
          name: "完工病害",
          value: "-",
          unit: "个",
        },
        {
          name: "病害事件",
          value: "-",
          unit: "个",
        },
        {
          name: "完工数量占比",
          value: "-",
          unit: "%",
        },
      ],
      year: new Date().getFullYear(),
      colorArr: ["#8148FE", "#04E0FF", "#FF8400", "#2DD944", "#0096FE", "#00ABFB"],
    };
  },
  methods: {
    getData() {
      this.list = this.list1;
      getDiseaseCountInfo({ year: this.year })
        .then((res) => {
          if (res.code === 200 && res.rows) {
            res.rows.map((item) => {
              item.name = item.type;
              if (item.ratio) {
                item.unit = "%";
                item.value =
                  item.ratio && item.ratio != "NaN"
                    ? (item.ratio * 100).toFixed(2)
                    : 0;
              } else {
                item.unit = "个";
                item.value = item.count;
              }
            });
            this.list = res.rows || this.list1;
          } else {
            this.list = this.list1;
          }
          this.list = this.list.map((item, index) => {
            item.color = this.colorArr[index];
            return item;
          });
        })
        .catch(() => {
          this.list = this.list1;
        });
    },
  },
  components: {},
  created() {
    window.$Bus.$on("onChangeYear", (y) => {
      this.year = y;
      this.getData();
    });
  },
  mounted() {
    this.getData();
  },
  beforeDestroy() {
    window.$Bus.$off("onChangeYear");
  },
};
</script>

<style lang="scss" scoped>
@import "@/assets/styles/utils.scss";

.daily-maintenance {
  padding: vwpx(16px);
  color: #ffffff;

  display: flex;
  flex-wrap: wrap;
  align-items: center;
  justify-content: center;

  .list {
    padding: 0 vwpx(22px);
    width: 31%;
    height: vwpx(140px);
    background: rgba(0, 0, 0, 0.1);
    box-shadow: inset 0px 0px 10px 0px rgba(0, 101, 255, 0.8);
    margin: 2% 1.1%;
    position: relative;

    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;

    .name {
      font-size: 1.4vh;
      font-family: Microsoft YaHei UI, Microsoft YaHei UI;
      font-weight: 700;
      color: #ffffff;
      text-align: center;
      font-style: normal;
      text-transform: none;
    }

    .divider {
      // width: 80%;
      // margin: 5px 10%;
      // border: 1px dotted rgba(23,116,255,0.3);
      width: 100%;
      height: vwpx(2px);
      background: repeating-linear-gradient(
        to right,
        rgba(23, 116, 255, 0.3),
        rgba(23, 116, 255, 0.3) vwpx(2px),
        transparent vwpx(3px),
        transparent vwpx(8px)
      );
      margin-top: vwpx(7px);
    }

    .value {
      font-family: Microsoft YaHei UI, Microsoft YaHei UI;
      font-weight: 700;
      font-size: vwpx(40px);
      color: #42abff;
      text-align: left;
      font-style: normal;
      text-transform: none;

      small {
        font-family: Microsoft YaHei UI, Microsoft YaHei UI;
        font-weight: 400;
        font-size: vwpx(24px);
        color: rgba(255, 255, 255, 0.8);
        text-shadow: 0px 0px 10px rgba(27, 126, 242, 0.8);
        text-align: center;
        font-style: normal;
        text-transform: none;
      }
    }

    .top-left {
      position: absolute;
      top: 0;
      left: 0;
      display: block;
      width: vwpx(20px);
      height: vwpx(20px);
      border-top: 1px solid #105ed9;
      border-left: 1px solid #105ed9;
    }

    .top-right {
      position: absolute;
      top: 0;
      right: 0;
      display: block;
      width: vwpx(20px);
      height: vwpx(20px);
      border-top: 1px solid #105ed9;
      border-right: 1px solid #105ed9;
    }

    .bottom-left {
      position: absolute;
      bottom: 0;
      left: 0;
      display: block;
      width: vwpx(20px);
      height: vwpx(20px);
      border-bottom: 1px solid #105ed9;
      border-left: 1px solid #105ed9;
    }

    .bottom-right {
      position: absolute;
      bottom: 0;
      right: 0;
      display: block;
      width: vwpx(20px);
      height: vwpx(20px);
      border-bottom: 1px solid #105ed9;
      border-right: 1px solid #105ed9;
    }
  }
}
</style>
