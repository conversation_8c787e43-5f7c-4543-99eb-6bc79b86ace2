<template>
  <PageContainer>
    <template slot="search">
      <div style="display: flex; align-items: center; margin: 0">
        <CascadeSelection
          :form-data="queryParams"
          v-model="queryParams"
          types="201"
          multiple
        />
        <rangeInput
            :clearData="clearData"
          :startPlaceholder="'起始桩号'"
          :endPlaceholder="'终点桩号'"
          @startValue="
            (v) => {
              queryParams.startStake = v;
            }
          "
          @endValue="
            (v) => {
              queryParams.endStake = v;
            }
          "
        />
        <el-select
          style="margin-right: 20px"
          v-model="queryParams.typeId"
          placeholder="资产子类"
          clearable
          @change="getDynamicSelect"
        >
          <el-option
            v-for="dict in assetSubclassList"
            :key="dict.id"
            :label="dict.typeName"
            :value="dict.id"
          />
        </el-select>

        <div style="min-width: 220px; height: 32px">
          <el-button type="primary" icon="el-icon-search" @click="handleQuery"
            >搜索</el-button
          >
          <el-button icon="el-icon-refresh" @click="resetQuery">重置</el-button>
          <el-button
            v-show="!showSearch"
            icon="el-icon-arrow-down"
            circle
            @click="showSearch = true"
          />
          <el-button
            v-show="showSearch"
            icon="el-icon-arrow-up"
            style="
              color: #1890ff;
              border-color: #badeff;
              background-color: #e8f4ff;
            "
            circle
            @click="showSearch = false"
          />
        </div>
      </div>
      <div v-if="showSearch" style="margin-top: 5px">
        <el-select
          v-model="queryParams.operationState"
          placeholder="运营状态"
          clearable
          collapse-tags
          style="width: 170px"
        >
          <el-option
            v-for="dict in dict.type.sys_operation_state"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
        <el-select
          style="margin-left: 20px; width: 170px"
          v-model="queryParams.status"
          placeholder="数据状态"
          clearable
        >
          <el-option
            v-for="dict in dict.type.base_data_state"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
        <el-input
          style="margin-left: 20px; width: 170px"
          clearable
          v-model="queryParams.assetCode"
          placeholder="资产编码"
        ></el-input>
      </div>
    </template>

    <template slot="header">
      <div class="button-list">
        <el-button
          v-hasPermi="['baseData:facility:add']"
          type="primary"
          @click="handleAdd"
          >新增</el-button
        >
        <el-button
          v-hasPermi="['baseData:facility:edit']"
          type="primary"
          @click="handleUpdate"
          >编辑</el-button
        >
        <el-button
          v-hasPermi="['baseData:facility:remove']"
          type="primary"
          @click="handleDelete"
          >删除</el-button
        >
        <el-button
          v-hasPermi="['baseData:facility:query']"
          type="primary"
          @click="handleView"
          >查看</el-button
        >
        <el-button v-hasPermi="['baseData:import:execute']" type="primary" @click="importUpdate">导入更新</el-button>
        <el-button v-hasPermi="['baseData:import:execute']" type="primary" @click="importAdd">导入新增</el-button>
        <el-button
          v-hasPermi="['baseData:facility:export']"
          type="primary"
          @click="exportList"
          >导出数据</el-button
        >
        <el-button v-hasPermi="['baseData:businessStatusRecord:add']" type="primary" @click="changeStatus">运营状态变更</el-button>
        <el-button type="primary" @click="downloadQrcode">二维码下载</el-button>
      </div>
    </template>

    <template slot="body">
      <el-table
        v-adjust-table
        v-loading="loading"
        ref="table"
        height="100%"
        style="width: 100%"
        border
        :data="tableData"
        :row-style="rowStyle"
        @selection-change="handleSelectionChange"
        @row-click="handleRowClick"
      >
        <el-table-column type="selection" width="50" align="center" />
        <el-table-column label="序号" type="index" width="50" align="center">
          <template v-slot="scope">
            {{
              scope.$index +
              (queryParams.pageNum - 1) * queryParams.pageSize +
              1
            }}
          </template>
        </el-table-column>
        <el-table-column fixed label="操作" align="center" width="50">
          <template slot-scope="scope">
            <el-link
              type="primary"
              :disabled="!scope.row.shape"
              @click.stop="handleLocation(scope.row)"
              >定位</el-link
            >
          </template>
        </el-table-column>
        <el-table-column
          label="资产子类"
          align="center"
          prop="typeName"
          min-width="140"
          show-overflow-tooltip
        >
        </el-table-column>
        <el-table-column
          label="资产编码"
          align="center"
          prop="assetCode"
          min-width="140"
          show-overflow-tooltip
        />
        <el-table-column
          label="资产名称"
          align="center"
          prop="assetName"
          min-width="140"
          show-overflow-tooltip
        />
        <el-table-column
          label="管理处"
          align="center"
          prop="managementMaintenanceName"
          min-width="140"
          show-overflow-tooltip
        />
        <el-table-column
          label="管养分处"
          align="center"
          prop="managementMaintenanceBranchName"
          min-width="140"
          show-overflow-tooltip
        />
        <el-table-column
          label="养护路段"
          align="center"
          prop="maintenanceSectionName"
          min-width="140"
          show-overflow-tooltip
        />
        <el-table-column
          label="路线编码"
          align="center"
          prop="routeCode"
          min-width="140"
          show-overflow-tooltip
        />
        <el-table-column
          label="桩号范围"
          align="center"
          min-width="200"
          show-overflow-tooltip
        >
          <template slot-scope="scope">
            {{ formatPile(scope.row.startStake) }} ~
            {{ formatPile(scope.row.endStake) }}
          </template>
        </el-table-column>
        <el-table-column
          label="运营状态"
          align="center"
          prop="operationStateName"
          min-width="130"
          show-overflow-tooltip
        >
          <template slot-scope="{ row }">
            <el-link
              :underline="false"
              :type="
                { 1: 'info', 2: 'success', 3: 'danger', 4: 'primary' }[
                  row.operationState
                ]
              "
              @click="handleOperational($event, row)"
            >
              <DictTag
                :value="row.operationState"
                :options="dict.type.sys_operation_state"
              />
            </el-link>
          </template>
        </el-table-column>
        <el-table-column label="数据状态" align="center" min-width="140">
          <template #default="{ row }">
            <el-link
              :type="{ 1: 'info', 2: 'success' }[row.status]"
              :underline="false"
            >
              <DictTag
                :value="row.status"
                :options="dict.type.base_data_state"
              />
            </el-link>
          </template>
        </el-table-column>
        <el-table-column
          label="方向"
          align="center"
          prop="direction"
          min-width="140"
          show-overflow-tooltip
        >
          <template slot-scope="scope">
            <dict-tag
              :options="dict.type.sys_route_direction"
              :value="scope.row.direction"
            />
          </template>
        </el-table-column>
        <el-table-column
          label="位置"
          align="center"
          prop="lane"
          min-width="140"
          show-overflow-tooltip
        >
          <template slot-scope="scope">
            <dict-tag :options="dict.type.lane" :value="scope.row.lane" />
          </template>
        </el-table-column>
        <el-table-column
          label="经度"
          align="center"
          min-width="140"
          show-overflow-tooltip
          prop="longitude"
        >
        </el-table-column>
        <el-table-column
          label="纬度"
          align="center"
          min-width="140"
          show-overflow-tooltip
          prop="latitude"
        >
        </el-table-column>
        <el-table-column
          label="施工里程桩号"
          align="center"
          prop="constructionStake"
          min-width="140"
          show-overflow-tooltip
        >
          <template slot-scope="scope">
            {{ formatPile(scope.row.constructionStake) }}
          </template>
        </el-table-column>
        <el-table-column
          label="统一里程桩号"
          align="center"
          prop="unifiedMileageStake"
          min-width="140"
          show-overflow-tooltip
        >
          <template slot-scope="scope">
            {{ formatPile(scope.row.unifiedMileageStake) }}
          </template>
        </el-table-column>
        <template v-if="showTableHead">
          <el-table-column
            v-for="item in tableHead"
            :key="item.id"
            :label="item.alias"
            align="center"
            :prop="item.columnName"
            min-width="140"
            show-overflow-tooltip
          />
        </template>
        <el-table-column
          label="备注"
          align="center"
          prop="remark"
          min-width="140"
          show-overflow-tooltip
        />
        <el-table-column label="图片" align="center">
          <template #default="{ row }">
            <el-link
              :underline="false"
              type="primary"
              :disabled="row.picPath ? false : true"
              @click.stop="previewImg(row)"
              >查看</el-link
            >
          </template>
        </el-table-column>
      </el-table>
      <pagination
        :total="total"
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
        :pageSizes="[10, 20, 30, 50, 100, 1000]"
        @pagination="getList"
      />
      <Dialog title="查看图片" width="500px" :show.sync="imgShow">
        <ImagePreview :owner-id="imageUrl" width="100%" height="100%" />
      </Dialog>
    </template>
    <Form
      v-if="showAddEdit"
      :forView="forView"
      :formData="formData"
      :title="title"
      :showAddEdit="showAddEdit"
      @close="
        () => {
          showAddEdit = false;
          formData = {};
        }
      "
      @refresh="
        () => {
          showAddEdit = false;
          formData = {};
          getList();
        }
      "
    />
    <MapPosition
      v-if="showMapPosition"
      :dialogVisible="showMapPosition"
      :data="mapPositionData"
      @close="showMapPosition = false"
    />
    <ImportData
      v-if="showImportAdd"
      :is-update="isUpdate"
      :dialog-visible="showImportAdd"
      :import-base-type="importBaseType"
      :import-type="importType"
      @close="closeImportAdd"
      :mainTypeId="6"
      :assetSubclassList="assetSubclassList"
    />
  </PageContainer>
</template>

<script>
import {
  getListPage,
  getFacility,
  delFacility,
  getAssetSubclass,
  getDynamicData,
} from "@/api/baseData/facility/baseInfo/index";
import Form from "./form.vue";
import CascadeSelection from "@/components/CascadeSelection/index.vue";
import rangeInput from "@/views/baseData/components/rangeInput/index.vue";
import Dialog from "@/components/Dialog/index.vue";
import ImportData from "@/views/baseData/components/importData/index.vue";
import { statusDialog } from "@/views/baseData/components/statusDialog/index.js";
import { statusListDialog } from "@/views/baseData/components/statusDialog/list.js";
import MapPosition from "@/components/mapPosition/index.vue";

export default {
  name: "BaseInfo",
  components: {
    Form,
    CascadeSelection,
    rangeInput,
    ImportData,
    Dialog,
    MapPosition,
  },
  dicts: [
    "sys_route_type",
    "sys_operation_state",
    "sys_route_direction",
    "lane",
    "base_data_state",
  ],
  data() {
    return {
      loading: true,
      showAddEdit: false,
      forView: false,
      title: "",
      clearData: false,
      formData: {},
      assetSubclassList: [],
      tableHead: [],
      ids: [],
      total: 0,
      tableData: [],
      queryParams: {
        pageNum: 1,
        pageSize: 20,
        mainTypeId: 6,
        operationState: "2",
      },
      showImport: false,
      showImportAdd: false,
      isUpdate: false,
      importType: 1,
      importBaseType: "20",
      imgShow: false,
      imageUrl: "",
      assetIds: [],
      showSearch: false,
      showMapPosition: false,
      mapPositionData: undefined,
      showTableHead: false,
    };
  },
  watch: {},
  created() {
    this.getList();
    this.getAssetSubclassList();
  },
  methods: {
    // 获取表格数据
    getList() {
      this.loading = true;
      //  let data=this.assetSubclassList.find(item=>item.id==this.queryParams.asset)
      //  this.queryParams.mainTypeId=data.mainType
      getListPage(this.queryParams)
        .then((response) => {
          this.tableData = response.rows;
          this.total = response.total;
          this.loading = false;
        })
        .catch(() => {
          this.loading = false;
        });
    },
    //查询字长子类
    getAssetSubclassList() {
      getAssetSubclass({ mainTypeId: 6 }).then((res) => {
        this.assetSubclassList = res;
      });
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id);
      this.assetIds = selection.map((item) => item.assetId);
    },
    // 表格点击勾选
    handleRowClick(row) {
      row.isSelected = !row.isSelected;
      this.$refs.table.toggleRowSelection(row);
    },
    // 勾选高亮
    rowStyle({ row, rowIndex }) {
      if (this.ids.includes(row.id)) {
        return { "background-color": "#b7daff", color: "#333" };
      } else {
        return { "background-color": "#fff", color: "#333" };
      }
    },

    // 搜索按钮
    handleQuery() {
      if (this.queryParams.typeId) {
        this.showTableHead = true;
      } else {
        this.showTableHead = false;
      }
      this.queryParams.pageNum = 1;
      this.getList();
      this.getDynamicList();
    },

    getDynamicList() {
      if (this.queryParams.typeId) {
        getDynamicData({
          mainTypeId: this.queryParams.mainTypeId,
          typeId: this.queryParams.typeId,
        }).then((res) => {
          if (res.code == 200) {
            this.tableHead = res.data;
            this.$nextTick(() => {
              this.$refs.table.doLayout();
            });
          }
        });
      }
    },
    // 重置按钮
    resetQuery() {
      this.clearData=true;
      this.queryParams = {
        pageNum: 1,
        pageSize: 20,
        mainTypeId: 6,
        operationState: "2"
      };
      this.handleQuery();
    },
    importUpdate() {
      this.isUpdate = true;
      this.showImportAdd = true;
      this.importType = 1;
    },
    importAdd() {
      this.isUpdate = false;
      this.showImportAdd = true;
      this.importType = 2;
    },
    // 查看图片
    previewImg(row) {
      this.imgShow = true;
      this.imageUrl = row.picPath;
    },
    // 新增按钮操作
    handleAdd() {
      this.forView = false;
      this.showAddEdit = true;
      this.formData = {};
      this.title = "新增交安数据";
    },
    getDynamicSelect(val) {
    },
    // 编辑按钮
    handleUpdate() {
      if (this.ids.length != 1) {
        this.$message.warning("请选择一条数据进行编辑！");
        return;
      } else {
        getFacility(this.ids[0]).then((res) => {
          if (res.code === 200) {
            this.formData = res.data;
            this.forView = false;
            this.showAddEdit = true;
            this.title = "编辑交安数据";
          }
        });
      }
    },
    // 表格操作-定位
    handleLocation(row) {
      this.mapPositionData = row;
      this.showMapPosition = true;
    },
    // 删除按钮
    handleDelete() {
      if (this.ids.length == 0) {
        this.$message.warning("请选择至少一条数据进行删除！");
        return;
      }
      this.$modal
        .confirm("确认删除？")
        .then(() => {
          delFacility(this.ids.join()).then((res) => {
            if (res && res.code == "200") {
              this.getList();
              this.$modal.msgSuccess("删除成功");
            }
          });
        })
        .catch(() => {});
    },
    // 查看按钮
    handleView() {
      if (this.ids.length != 1) {
        this.$message.warning("请选择一条数据！");
        return;
      } else {
        getFacility(this.ids[0]).then((res) => {
          if (res.code === 200) {
            this.formData = res.data;
            this.forView = true;
            this.showAddEdit = true;
            this.title = "查看交安数据";
          }
        });
      }
    },
    // 导出按钮
    exportList() {
      if (this.ids.length === 0) {
        this.$modal
          .confirm("即将导出所有表格数据，此过程可能花费时间较长，是否继续？")
          .then(() => {
            this.download(
              "/baseData/facility/export",
              this.queryParams,
              `facility_${new Date().getTime()}.xlsx`,
              {
                headers: { "Content-Type": "application/json;" },
                parameterType: "body",
              }
            );
          })
          .catch(() => {});
      } else {
        this.$modal
          .confirm(`已选择${this.ids.length}条交安数据，确认导出？`)
          .then(() => {
            this.download(
              "/baseData/facility/export",
              { ids: this.ids, mainTypeId: 6 },
              `facility__${new Date().getTime()}.xlsx`,
              {
                headers: { "Content-Type": "application/json;" },
                parameterType: "body",
              }
            );
          })
          .catch(() => {});
      }
    },
    // 导入按钮
    handleImport() {
      this.showImport = true;
    },
    closeImportAdd(v) {
      this.showImportAdd = false;
      if (v) this.getList();
    },
    closeImport(v) {
      this.showImport = false;
      if (v) this.getList();
    },
    // 二维码下载
    downloadQrcode() {
      if (this.ids.length === 0) {
        this.$modal
          .confirm(
            "即将下载所有表格的二维码数据，此过程可能花费时间较长，是否继续？"
          )
          .then(() => {
            this.download(
              "/baseData/facility/genQrCode",
              this.queryParams,
              `QrCode_${new Date().getTime()}`,
              {
                headers: { "Content-Type": "application/json;" },
                parameterType: "body",
              }
            );
          })
          .catch(() => {});
      } else {
        this.$modal
          .confirm(`已选择${this.ids.length}条交安数据，是否下载二维码？`)
          .then(() => {
            let data = {
              ...this.queryParams,
              ids: this.ids,
            };
            this.download(
              "/baseData/facility/genQrCode",
              data,
              `QrCode_${new Date().getTime()}`,
              {
                headers: { "Content-Type": "application/json;" },
                parameterType: "body",
              }
            );
          })
          .catch(() => {});
      }
    },
    // 运营状态变更按钮
    changeStatus() {
      if (this.ids.length !== 1) {
        this.$message.warning("请选择一条数据！");
        return;
      } else {
        // baseDataType 基础数据类型 ？交安
        statusDialog({ dataId: this.ids[0], baseDataType: 20 }).then(() => {
          this.getList();
        });
      }
    },
    // 表格操作-运营状态
    handleOperational(event, row) {
      event.stopPropagation();
      statusListDialog({ dataId: row.id, baseDataType: 20 });
    },
  },
};
</script>

<style lang="scss" scoped>
.button-list {
  border-radius: 4px;
  width: 100%;
  .el-button {
    margin-bottom: 10px;
    margin-right: 10px;
    margin-left: 0;
  }
}
</style>
