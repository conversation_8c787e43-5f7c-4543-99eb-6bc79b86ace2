<template>
  <div class="road-interflow-edit">
    <el-row :gutter="15">
      <el-row>
        <el-col :span="24">
          <el-form
              ref="queryForm"
              :inline="true"
              :model="queryParams"
              label-width="68px"
              size="mini"
          >
            <el-form-item prop="disType">
              <dict-select v-model="queryParams.disType" clearable placeholder="请选择事件类型" style="width: 190px"
                           type="sys_asset_type"></dict-select>
            </el-form-item>
            <el-form-item prop="maiSecName">
              <RoadSection v-model="queryParams.maiSecName" placeholder="路段名称" style="width: 190px"/>
            </el-form-item>
            <el-form-item>
              <RouteCodeSection v-model="queryParams.routeCode" :maintenanceSectionId="queryParams.maiSecName"
                                placeholder="路线编码" style="width: 190px;"/>
            </el-form-item>
            <el-form-item>
              <el-input
                  v-model="queryParams.beginMile"
                  clearable
                  placeholder="起止桩号"
                  style="width: 110px"
              >
              </el-input>
              <div style="width: 20px;display: inline-block;text-align: center">~</div>
              <el-input
                  v-model="queryParams.endMile"
                  clearable
                  placeholder="起止桩号"
                  style="width: 110px"
              >
              </el-input>
            </el-form-item>
            <el-form-item>
              <el-input v-model="queryParams.schemeCode" placeholder="子目号" style="width: 190px"></el-input>
            </el-form-item>
            <el-form-item>
              <el-input v-model="queryParams.schemeName" placeholder="方法名" style="width: 190px"></el-input>
            </el-form-item>
            <el-form-item>
              <el-input v-model="queryParams.code" placeholder="施工单编号" style="width: 190px"></el-input>
            </el-form-item>
            <el-form-item>
              <el-button icon="el-icon-search" size="mini" type="primary" @click="handleQuery">搜索</el-button>
              <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
            </el-form-item>
          </el-form>
        </el-col>
      </el-row>
      <el-row :gutter="10" class="mb8">
        <el-col :span="1.5">
          <el-button
              icon="el-icon-view"
              size="mini"
              type="primary"
              @click="dialogVisible = true"
          >事件信息
          </el-button
          >
        </el-col>
      </el-row>
      <el-row>
        <div class="draggable">
          <el-table v-adjust-table
              ref="dataTable"
              v-loading="loading"
              :data="tableData"
              border
              height="600"
              highlight-current-row
              row-key="id"
              size="mini"
              stripe
              style="width: 100%"
              @row-click="handleClickRow"
              @selection-change="handleSelectionChange"
          >
            <el-table-column align="center" type="selection" width="50"/>
            <el-table-column
                align="center"
                label="序号"
                type="index"
                width="50"
            />
            <template v-for="(column,index) in columns">
              <el-table-column v-if="column.visible"
                               :label="column.label"
                               :prop="column.field"
                               :width="column.width"
                               align="center">
                <template slot-scope="scope">
                  <dict-tag v-if="column.dict" :options="dict.type[column.dict]" :value="scope.row[column.field]"/>
                  <template v-else-if="column.slots">
                    <RenderDom :index="index" :render="column.render" :row="scope.row"/>
                  </template>
                  <span v-else-if="column.isTime">{{ parseTime(scope.row[column.field], '{y}-{m}-{d}') }}</span>
                    <span v-else>{{ scope.row[column.field] }}</span>
                </template>
              </el-table-column>
            </template>
          </el-table>
          <pagination
              v-show="total>0"
              :limit.sync="queryParams.pageSize"
              :page.sync="queryParams.pageNum"
              :total="total"
              @pagination="handleQuery"
          />
        </div>
      </el-row>
      <el-col :span="24" style="text-align: right;padding-right: 7.5px;margin-top: 18px">
        <el-button type="primary" @click="onSave">保 存</el-button>
        <el-button @click="onClose">退 出</el-button>
      </el-col>
    </el-row>
    <el-dialog :visible.sync="dialogVisible" append-to-body destroy-on-close modal-append-to-body title="事件信息"
               width="80%">
      <event-tree-info></event-tree-info>
    </el-dialog>
  </div>
</template>
<script>
import {addDetail, listSettle} from "@/api/dailyMaintenance/metering/middleApplication"
import EventTreeInfo from "@/views/dailyMaintenance/component/eventTreeInfo.vue";
import RoadSection from "@/views/baseData/components/roadSection/index.vue";
import RouteCodeSection from "@/components/RouteCodeSection/index.vue";

export default {
  components: {RoadSection, EventTreeInfo, RouteCodeSection},
  data() {
    return {
      columns: [
        {key: 0, width: 100, field: 'name', label: `施工单`, visible: true},
        {key: 1, width: 100, field: 'code', label: `施工单编号`, visible: true},
        {key: 2, width: 100, field: 'domainName', label: `管养单位`, visible: true},
        {key: 3, width: 100, field: 'assetType', label: `资产类型`, visible: true},
        {key: 4, width: 100, field: 'maiSecId', label: `路段名称`, visible: true},
        {key: 5, width: 100, field: 'routeCode', label: `路线编码`, visible: true},
        {key: 6, width: 100, field: 'direction', label: `上下行`, visible: true},
        {key: 7, width: 100, field: 'lane', label: `位置`, visible: true},
        {key: 8, width: 100, field: 'beginMile', label: `起点桩号`, visible: true},
        {key: 9, width: 100, field: 'endMile', label: `终点桩号`, visible: true},
        {key: 10, width: 100, field: 'disTypeName', label: `事件类型`, visible: true},
        {key: 11, width: 100, field: 'disDesc', label: `事件内容`, visible: true},
        {key: 12, width: 100, field: 'realEDate', label: `完工时间`, visible: true},
        {key: 13, width: 100, field: 'sumFund', label: `总金额`, visible: true},
        {key: 14, width: 100, field: 'unsettledAmount', label: `未结算金额`, visible: true},
        {key: 15, width: 100, field: 'settlementStatus', label: `结算状态`, visible: true},
        {key: 16, width: 100, field: 'unitPrice', label: `有无新增单价`, visible: true},
      ],
      queryParams: {
        pageNum: 1,
        pageSize: 50,
      },
      tableData: [],
      selectIds: [],
      total: 0,
      dialogVisible: false,
      loading: true
    }
  },
  props: {
    maiSecId: {
      type: String,
      default: ''
    },
    calcId: {
      type: String,
      default: ''
    }
  },
  watch: {
    calcId: {
      handler(val) {
        if (val) {
          this.handleQuery()
        }
      }
    }
  },
  created() {
    this.handleQuery()
  },
  methods: {
    handleQuery() {
      this.loading = true
      this.queryParams.maiSecId = '28'
      listSettle(this.queryParams).then(res => {
        this.tableData = res.rows
        this.total = res.total
        this.loading = false
      })
    },
    resetQuery() {
      this.queryParams = {
        pageNum: 1,
        pageSize: 50,
      }
    },
    // 选中
    handleSelectionChange(e) {
      this.selectIds = e
    },

    handleClickRow(e) {
      e.isSelected = !e.isSelected;
      this.$refs.dataTable.toggleRowSelection(e);
    },

    onSave() {
      const params = []
      for (let i = 0; i < this.selectIds.length; i++) {
        params.push({
          calcId: this.calcId,
          settleId: this.selectIds[i].settleId
        })
      }
      addDetail(params).then(res => {
        this.onClose()
      })
    },
    onClose() {

    }
  }
}
</script>
<style lang="scss" scoped>

</style>
<style lang="scss" scoped>
@import "@/assets/styles/business.scss";
</style>
