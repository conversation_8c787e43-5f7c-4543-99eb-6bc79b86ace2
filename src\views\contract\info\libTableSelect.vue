<template>
  <div class="app-container">
    <el-row :gutter="20">

      <!--筛选区开始-->
      <el-col :span="24" :xs="24">
        <el-row>
          <el-col :span="24">
            <el-form ref="queryForm" :inline="true" :model="queryParams" label-width="68px" size="small">

              <el-form-item label="" prop="libName">
                <el-input
                  v-model="queryParams.libName"
                  clearable
                  placeholder="请输入名称"
                  prefix-icon="el-icon-user"
                  style="width: 240px"
                  @keyup.enter.native="handleQuery"
                />
              </el-form-item>
              <el-form-item>
                <el-button icon="el-icon-search" size="mini" type="primary" @click="handleQuery">搜索</el-button>
                <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
              </el-form-item>
            </el-form>
            <!--默认折叠-->
          </el-col>
        </el-row>
        <!--筛选区结束-->
        <!--数据表格开始-->
        <div class="tableDiv">
          <el-table v-adjust-table v-loading="loading" :data="libList"
                    border size="mini" style="width: 100%" highlight-current-row @current-change="handleClick">
            <el-table-column fixed label="序号" type="index" width="50">
            </el-table-column>
            <el-table-column align="center" label="名称" prop="libName"/>
            <el-table-column align="center" label="描述" prop="libDesc"/>
            <el-table-column align="center" label="起始时间" prop="beginTime" width="180">
              <template slot-scope="scope">
                <span>{{ parseTime(scope.row.beginTime, '{y}-{m}-{d}') }}</span>
              </template>
            </el-table-column>
            <el-table-column align="center" label="终止时间" prop="endTime" width="180">
              <template slot-scope="scope">
                <span>{{ parseTime(scope.row.endTime, '{y}-{m}-{d}') }}</span>
              </template>
            </el-table-column>
            <el-table-column align="center" label="备注" prop="remark"/>
            <el-table-column align="center" label="创建时间" prop="createTime"/>
          </el-table>

          <pagination
            v-show="total>0"
            :limit.sync="queryParams.pageSize"
            :page.sync="queryParams.pageNum"
            :total="total"
            @pagination="getList"
          />
        </div>
        <!--数据表格结束-->
      </el-col>
    </el-row>
  </div>
</template>

<script>

import {listByParam} from '@/api/contract/quotationSystem'

export default {
  name: "Lib",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 显示搜索条件
      showSearch: false,
      dictType: [],
      lib: {},
      // 总条数
      total: 0,
      // 报价体系基本信息表格数据
      libList: null,
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 50,
        libName: null
      },
    };
  },
  watch: {
    // 根据名称筛选部门树
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询用户列表 */
    getList() {
      this.loading = true;
      this.queryParams.domainId = parseInt(this.queryParams.domainIdStr)
      listByParam(this.queryParams).then(response => {
        this.libList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 表单重置
    reset() {
      this.form = {
        libName: null,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dateRange = [];
      this.resetForm("queryForm");
      this.handleQuery();
    },
    handleClick(e) {
      const checkData = {
        id: e.id,
        libName: e.libName
      }
      this.$emit('checkLib', checkData)
    }
  }
};
</script>
<style>
.hasTagsView .app-main[data-v-078753dd] {
  background: #f5f7fa;
}

.tableDiv {
  background-color: white;
  padding-bottom: 10px;
}
</style>
<style lang="scss" scoped>
@import "@/assets/styles/business.scss";
</style>
