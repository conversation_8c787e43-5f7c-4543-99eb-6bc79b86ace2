<template>
  <div class="tableDiv">
    <el-table v-adjust-table v-loading="loading" :data="dataList" height="100%" size="mini" style="width: 100%">
      <el-table-column align="center" fixed label="序号" type="index" width="100"></el-table-column>
      <template v-for="(column, index) in columns">
        <el-table-column v-if="column.visible" :key="index" :label="column.label" :prop="column.field" align="center"
          show-overflow-tooltip>
          <template slot-scope="scope">
            <dict-tag v-if="column.dict" :options="dict.type[column.dict]" :value="scope.row[column.field]" />
            <template v-else-if="column.slots">
              <RenderDom :index="index" :render="column.render" :row="scope.row" />
            </template>
            <span v-else-if="column.isTime">
              {{ parseTime(scope.row[column.field], '{y}-{m}-{d}') }}
            </span>
            <span v-else>{{ scope.row[column.field] }}</span>
          </template>
        </el-table-column>
      </template>
    </el-table>
  </div>
</template>

<script>
import {
  getZJGTStructDeviceStatus,
  getSansiStructDeviceStatus,
  getWdmStructDeviceStatus
} from "@/api/jgjc/earlyWarning/deviceModel";
export default {
  name: 'equipmentonlineStatus',
  components: {
    RenderDom: {
      functional: true,
      props: {
        row: Object,
        index: Number,
        render: Function,
      },
      render(createElement, ctx) {
        const { row, index } = ctx.props;
        return ctx.props.render(row, index);
      },
    }
  },
  props: {
    checkData: {
      type: Object,
      default: () => { }
    }
  },
  data() {
    return {
      loading: false,
      dataList: [],
      columns: [
        { key: 0, width: 100, field: 'roadId', label: '控制ID', visible: true },
        { key: 1, width: 100, field: 'name', label: '名称', visible: true },
        { key: 2, width: 100, field: 'province', label: '省份', visible: true },
        { key: 3, width: 100, field: 'city', label: '城市', visible: true },
        { key: 4, width: 100, field: 'systemStatus', label: '设备状态', visible: true }
      ],
    }
  },
  mounted() {
    let data = JSON.parse(localStorage.getItem('mapData'));
    this.getEquipmentonlineStatus(data)
  },
  methods: {
    getEquipmentonlineStatus(data) {
      // this.loading = true
      // 获取设备在线状态
      if (data.flashType == '中交国通') {
        this.columns = [
          { key: 0, width: 100, field: 'roadId', label: '控制ID', visible: true },
          { key: 1, width: 100, field: 'name', label: '名称', visible: true },
          { key: 2, width: 100, field: 'province', label: '省份', visible: true },
          { key: 3, width: 100, field: 'city', label: '城市', visible: true },
          {
            key: 4, width: 100, field: 'systemStatus', label: '设备状态', visible: true, slots: true, render: (row, index) => {
              if (row.systemStatus === '在线') {
                return <div style='color: #32da49'>{row.systemStatus}</div>
              } else {
                return <div style='color: #fb4545'>{row.systemStatus}</div>
              }
            }
          }
        ]
        getZJGTStructDeviceStatus({ structId: data.id }).then(res => {
          this.dataList = res.data
          this.dataList.forEach(item => {
            item.systemStatus = item.systemStatus == '0' ? '离线' : '在线'
          })
        })
      }
      if (data.flashType == '三思') {
        this.columns = [
          { key: 0, width: 100, field: 'deviceCode', label: '设备编码', visible: true },
          { key: 1, width: 100, field: 'deviceName', label: '名称', visible: true },
          {
            key: 4, width: 100, field: 'status', label: '设备状态', visible: true, slots: true, render: (row, index) => {
              if (row.status === '在线') {
                return <div style='color: #32da49'>{row.status}</div>
              } else {
                return <div style='color: #fb4545'>{row.status}</div>
              }
            }
          }
        ]
        getSansiStructDeviceStatus({ structId: data.id }).then(res => {
          this.dataList = res.data
        })
      }
      if (data.flashType == '维的美') {
        this.columns = [
          { key: 0, width: 100, field: 'deviceName', label: '名称', visible: true },
          { key: 1, width: 100, field: 'deviceCode', label: '设备编码', visible: true },
          {
            key: 4, width: 100, field: 'status', label: '设备状态', visible: true, slots: true, render: (row, index) => {
              if (row.status === '在线') {
                return <div style='color: #32da49'>{row.status}</div>
              } else {
                return <div style='color: #fb4545'>{row.status}</div>
              }
            }
          },
          { key: 3, width: 100, field: 'lastLoginTime', label: '最后一次登录时间', visible: true },
          { key: 4, width: 100, field: 'lastLogoutTime', label: '最后一次退出时间', visible: true },
          { key: 5, width: 100, field: 'lastLedOnTime', label: '最后一次开启时间', visible: true },
          { key: 6, width: 100, field: 'lastLedOffTime', label: '最后一次关闭时间', visible: true }
        ]
        getWdmStructDeviceStatus({ structId: data.id }).then(res => {
          this.dataList = res.data
        })
      }
    }

  }
}
</script>

<style lang="scss" scoped>
@import "./index.scss";
</style>