<template>
	<el-row class="mainContent route-load">
		<el-col :span="16">
			<el-row>
				<el-col :span="24">
					<el-card class="task-center">
						<template slot="header">
							<div class="card-title">
								<span>任务中心</span>
								<el-button type="text" @click="routeChange('task/NextIndex')">查看更多</el-button>
							</div>
						</template>
						<el-table
							:data="taskList"
							header-cell-class-name="table-header"
							v-loading="loadingList.taskLoading"
              height="100%"
							border
						>
							<el-table-column
								align="center"
								prop="name"
								label="流程类型"
								show-overflow-tooltip
							></el-table-column>
							<el-table-column
								align="center"
								prop="category"
								label="审批节点"
								show-overflow-tooltip
							>
								<template slot-scope="row">
									<el-link :underline="false" @click="handlerClick(row.row)">
										<i class="el-icon-user-solid"></i>
										{{ row.row.taskname }}
									</el-link>
								</template>
							</el-table-column>
							<el-table-column
								align="center"
								prop="taskName"
								label="待办数量"
								show-overflow-tooltip
							>
								<template slot-scope="row">
									<el-link :underline="false" @click="handlerClick(row.row)" type="primary">
										{{ row.row.total }}
									</el-link>
								</template>
							</el-table-column>
							<el-table-column align="center" prop="createTime" label="操作" show-overflow-tooltip>
								<template slot-scope="scope">
									<el-button
										size="mini"
										type="text"
										icon="el-icon-view"
										@click="onHandler(scope.row)"
									>
										办理
									</el-button>
								</template>
							</el-table-column>
						</el-table>
					</el-card>
				</el-col>
				<!-- <el-col :span="12">
          <el-card class="executive-condition">
            <template slot="header">
              <div class="card-title">
                <span>施工单事件执行情况</span>
              </div>
            </template>
            <div
              class="condition-item"
              v-for="(item, index) in constructionEvent"
              :key="'construction' + index"
            >
              <div class="condition-item-top">
                <div class="icon-box">
                  <i class="el-icon-s-order"></i>
                </div>
                {{ item.category }}
              </div>
              <el-carousel height="70px" arrow="always" :autoplay="false">
                <el-carousel-item>
                  <router-link :to="item.url">
                    <el-row style="text-align: center">
                      <el-col :span="6">
                        <div class="numTitle">已下发</div>
                        <div class="num">{{ item.issueCount || 0 }}</div>
                      </el-col>
                      <el-col :span="6">
                        <div class="numTitle">待施工</div>
                        <div class="num">{{ item.registerCount || 0 }}</div>
                      </el-col>
                      <el-col :span="6">
                        <div class="numTitle">施工中</div>
                        <div class="num">{{ item.registeringCount || 0 }}</div>
                      </el-col>
                      <el-col :span="6">
                        <div class="numTitle">已完工</div>
                        <div class="num">{{ item.finishedCount || 0 }}</div>
                      </el-col>
                    </el-row>
                  </router-link>
                </el-carousel-item>
              </el-carousel>
            </div>
          </el-card>
        </el-col> -->
			</el-row>
			<el-row>
				<el-col :span="12">
					<el-card class="jurisdiction-incident">
						<template slot="header">
							<div class="card-title">
								<span>辖区事件</span>
								<el-button
									type="text"
									@click="routeChange('dailyMaintenance/eventManage/eventData')"
								>
									查看更多
								</el-button>
							</div>
						</template>
						<div class="incident-item" v-for="item in eventData">
							<div class="listIcon">
								<i class="el-icon-s-order"></i>
							</div>
							<div class="listContent">
								<el-descriptions :column="2" :title="item.disCode">
                  <template #title>
                    <el-tooltip effect="dark" :content="`${ item.disCode }(${item.reportName}, ${item.collectTime})`" placement="top-start">
                      <p class="dis-title ellipsis">
                        {{ item.disCode }}
                        ({{ item.reportName }}, {{ item.collectTime }})
                      </p>
                    </el-tooltip>
                  </template>
									<el-descriptions-item label="管养单位">
                     {{ item.domainName }}
									</el-descriptions-item>
									<el-descriptions-item label="状态">
										<el-tag size="small" type="warning">{{ item.disStatusName }}</el-tag>
									</el-descriptions-item>
									<el-descriptions-item label="事件类型">
                    {{ item.disName }}
                  </el-descriptions-item>
                  <el-descriptions-item label="描述" contentStyle="white-space: nowrap; overflow: hidden; cursor: pointer;">
                    <el-tooltip effect="dark" :content="item.disDesc" placement="top-start">
                      <span>{{ item.disDesc }}</span>
                    </el-tooltip>
									</el-descriptions-item>
								</el-descriptions>
							</div>
						</div>
					</el-card>
				</el-col>
				<el-col :span="12">
					<el-card class="budget-card">
						<template slot="header">
							<div class="card-title">
								<span>预算执行情况</span>
								<!-- <el-button type="text">查看更多</el-button> -->
							</div>
						</template>
						<Echarts :option="option" v-if="option" height="100%"></Echarts>
					</el-card>
				</el-col>
			</el-row>
			<el-row>
				<el-col :span="12">
					<el-card class="line-info">
						<template slot="header">
							<div class="card-title">
								<span>路线基础信息</span>
								<el-button type="text" @click="routeChange('base/road/statistics')">
									查看更多
								</el-button>
							</div>
						</template>
						<el-table
							:data="mainSecList"
							header-cell-class-name="table-header"
              height="100%"
							v-loading="loadingList.mainSecLoading"
							border
						>
							<el-table-column
								align="center"
								prop="managementMaintenanceName"
								label="管理处"
								show-overflow-tooltip
							></el-table-column>
							<el-table-column
								align="center"
								prop="maintenanceSectionName"
								label="路段名称"
								show-overflow-tooltip
							></el-table-column>
							<el-table-column
								align="center"
								prop="maintenanceMileage"
								label="里程(km)"
								show-overflow-tooltip
							></el-table-column>
						</el-table>
					</el-card>
				</el-col>
				<el-col :span="12">
					<el-card class="bridge-info">
						<template slot="header">
							<div class="card-title">
								<span>桥梁基础信息</span>
								<el-button type="text" @click="routeChange('base/bridge/statistics')">
									查看更多
								</el-button>
							</div>
						</template>
						<el-table
							:data="bridgeList"
							header-cell-class-name="table-header"
							v-loading="loadingList.bridgeLoading"
              height="100%"
							border
						>
              <el-table-column
								align="center"
								prop="managementMaintenanceName"
								label="管理处"
								show-overflow-tooltip
							></el-table-column>
							<el-table-column
								align="center"
								prop="maintenanceSectionName"
								label="养护路段名称"
								show-overflow-tooltip
							></el-table-column>
							<el-table-column
								align="center"
								prop="superMajorBridge"
								label="特大桥"
								width="80"
							></el-table-column>
							<el-table-column
								align="center"
								prop="greatBridge"
								label="大桥"
								width="80"
							></el-table-column>
							<el-table-column
								align="center"
								prop="mediumBridge"
								label="中桥"
								width="80"
							></el-table-column>
							<el-table-column
								align="center"
								prop="smallBridge"
								label="小桥"
								width="60"
							></el-table-column>
						</el-table>
					</el-card>
				</el-col>
			</el-row>
		</el-col>
		<!--    右-->
		<el-col :span="8">
			<el-row>
				<el-card class="box-card leftTop1">
					<div class="textItem">
						您好，{{ nickName }}
						<div class="textItemSpan">
							欢迎使用高速公路智慧养护管理平台
							<br />
							时间：{{ times }}
						</div>
					</div>
				</el-card>
			</el-row>
			<!-- 填报任务 -->
			<el-row>
				<el-card class="task-write">
					<template slot="header">
						<div class="card-title">
							<span>填报任务</span>
							<el-button type="text" @click="routeChange('repote/repote/record')">
								查看更多
							</el-button>
						</div>
					</template>
					<el-table
						:data="writeTaskList"
						header-cell-class-name="table-header"
						v-loading="loadingList.writeTaskLoading"
            height="100%"
						border
					>
						<el-table-column
							align="center"
							prop="missionName"
							label="任务标题"
							show-overflow-tooltip
						></el-table-column>
						<el-table-column
							align="center"
							prop="createTime"
							label="下发时间"
							show-overflow-tooltip
						></el-table-column>
						<el-table-column align="center" prop="status" label="填报状态" show-overflow-tooltip>
							<template v-slot="scope">
								{{
									scope.row.status === 1 ? '待填写' : scope.row.status === 2 ? '待提交' : '已提交'
								}}
							</template>
						</el-table-column>
					</el-table>
				</el-card>
			</el-row>
			<!-- 巡检查-->
			<el-row>
				<el-card class="month-patrol">
					<template slot="header">
						<div class="card-title">
							<span>巡查、检查完成情况</span>
							<el-button type="text" @click="routeChange('patrol/inspectionLogs')">
								查看更多
							</el-button>
						</div>
					</template>
					<el-row style="padding: 20px 20px 10px;">
						<el-col :span="12">
							<div class="patrol-overview-item">
								<img class="listIcon" src="@/assets/home/<USER>/patrol-icon1.svg" alt="">
								<div class="listContent">
									<div class="numTitle">
										路面计划巡查里程(本日)
                    <el-tooltip class="item" effect="dark" content="路面计划巡查里程(本日)" placement="top-start">
                      <img class="listIcon" src="@/assets/home/<USER>/patrol-icon2.svg" alt="" style="width: 15px;height: 15px;">
                    </el-tooltip>
									</div>
									<div class="nums" style="margin-top: 5px;">
										{{ patrolData ? patrolData.totalMileage : 0 }}
										<span class="num-unit">Km</span>
									</div>
								</div>
							</div>
						</el-col>
						<el-col :span="12" style="padding-left: 5px">
							<div class="patrol-overview-item">
								<div class="listIcon"><img class="listIcon" src="@/assets/home/<USER>/patrol-icon1.svg" alt=""></div>
								<div class="listContent">
									<div class="numTitle">
										路面已巡查里程(本日)
                    <el-tooltip class="item" effect="dark" content="路面已巡查里程(本日)" placement="top-start">
                      <img class="listIcon" src="@/assets/home/<USER>/patrol-icon2.svg" alt=""  style="width: 15px;height: 15px;">
                    </el-tooltip>

									</div>
									<div class="nums" style="margin-top: 10px;">
										{{ patrolData ? patrolData.inspectedMileage : 0 }}
										<span class="num-unit">Km</span>
									</div>
								</div>
							</div>
						</el-col>
					</el-row>
					<el-row style="display: flex; flex-direction: column; padding: 0 50px;">
						<el-col>
							<div class="patrol-item">
								<div class="patrol-item-top">
									<img class="patrol-item-icon" src="@/assets/home/<USER>/patrol-icon3.svg" alt="">
									<div class="numTitle1">桥梁经常巡查(本月)</div>
								</div>
								<div class="patrol-item-bottom">
									<div class="patrol-data">
										<span class="patrol-text">计划</span>
										<span class="patrol-num">
                      {{ patrolData ? patrolData.bridgeTotalCount : 0 }}
                      <span>座</span>
										</span>

									</div>
									<div class="patrol-data">
										<span class="patrol-text">已巡查</span>
										<span :class="{'patrol-num': true, 'patrol-num1': true}">
											{{ patrolData ? patrolData.bridgeInspectedCount : 0 }}
                      <span>座</span>
										</span>
									</div>
								</div>
							</div>
						</el-col>
						<el-col>
							<div class="patrol-item">
								<div class="patrol-item-top">
									<img class="patrol-item-icon" src="@/assets/home/<USER>/patrol-icon4.svg" alt="">
									<div class="numTitle1">隧道经常巡查(本月)</div>
								</div>
								<div class="patrol-item-bottom">
									<div class="patrol-data">
										<span class="patrol-text">土建计划</span>
										<span :class="{'patrol-num': true}">
											{{ patrolData ? patrolData.tunnelTotalCount : 0 }}
                      <span>座</span>
										</span>
									</div>
									<div class="patrol-data">
										<span class="patrol-text">土建已巡查</span>
										<span :class="{'patrol-num': true, 'patrol-num1': true}">
											{{ patrolData ? patrolData.tunnelInspectedCount : 0 }}
                      <span>座</span>
										</span>
									</div>
                  <div class="patrol-data">
										<span class="patrol-text">机电计划</span>
										<span :class="{'patrol-num': true}">
											{{ patrolData ? patrolData.tunnelDeviceTotalCount : 0 }}
                      <span>座</span>
										</span>
									</div>
                  <div class="patrol-data">
										<span class="patrol-text">机电已巡查</span>
										<span :class="{'patrol-num': true, 'patrol-num1': true}">
											{{ patrolData ? patrolData.tunnelDeviceInspectedCount : 0 }}
                      <span>座</span>
										</span>
									</div>
								</div>
							</div>
						</el-col>
						<el-col>
							<div class="patrol-item" style="margin-bottom: 0;">
								<div class="patrol-item-top">
									<img class="patrol-item-icon" src="@/assets/home/<USER>/patrol-icon5.svg" alt="">
									<div class="numTitle1">涵洞经常巡查(本月)</div>
								</div>
								<div class="patrol-item-bottom">
									<div class="patrol-data">
										<span class="patrol-text">计划</span>
										<span :class="{'patrol-num': true}">
											{{ patrolData ? patrolData.culvertTotalCount : 0 }}
                      <span>座</span>
										</span>
									</div>
									<div class="patrol-data">
										<span class="patrol-text">已巡查</span>
										<span :class="{'patrol-num': true, 'patrol-num1': true}">
											{{ patrolData ? patrolData.culvertInspectedCount : 0 }}
                      <span>座</span>
										</span>
									</div>
								</div>
							</div>
						</el-col>
					</el-row>
				</el-card>
			</el-row>
			<!-- 隧道基础信息-->
			<el-row>
				<el-card class="tunnel-info">
					<template slot="header">
						<div class="card-title">
							<span>隧道基础信息</span>
							<el-button type="text" @click="routeChange('base/tunnel/statistics')">
								查看更多
							</el-button>
						</div>
					</template>
					<el-table
						:data="tunnelList"
						header-cell-class-name="table-header"
						v-loading="loadingList.tunnelLoading"
            height="100%"
						border
					>
            <el-table-column
								align="center"
								prop="managementMaintenanceName"
								label="管理处"
								show-overflow-tooltip
						></el-table-column>
						<el-table-column
							align="center"
							prop="maintenanceSectionName"
							label="养护路段名称"
							show-overflow-tooltip
						></el-table-column>
						<el-table-column
							align="center"
							prop="superMajorTunnel"
							label="特长隧道"
							width="80"
						></el-table-column>
						<el-table-column
							align="center"
							prop="greatTunnel"
							label="长隧道"
							width="80"
						></el-table-column>
						<el-table-column
							align="center"
							prop="mediumTunnel"
							label="中隧道"
							width="70"
						></el-table-column>
						<el-table-column
							align="center"
							prop="smallTunnel"
							label="短隧道"
							width="70"
						></el-table-column>
					</el-table>
				</el-card>
			</el-row>
		</el-col>
	</el-row>
</template>

<script>
import { mapGetters } from 'vuex'
import { queryPageTodoTaskGroup } from '@/api/process/task/task' // 流程任务模块
import { getStatistics } from '@/api/baseData/road/statistics/index'
import { getStatistics as bridgeListApi } from '@/api/baseData/bridge/statistics/index'
import { getStatistics as tunnelListApi } from '@/api/baseData/tunnel/statistics/index'
import { listRepoteRecord } from '@/api/repote/repoteRecord'
import { findDiseaseDataList } from '@/api/dailyMaintenance/eventManage/eventData.js'
import request from '@/utils/request'
import Echarts from './echarts.vue'
import moment from 'moment/moment'
import { divide, round } from 'lodash'
export default {
	components: {
		Echarts,
	},
	data() {
		return {
			taskList: [],
			writeTaskList: [],
			mainSecList: [],
			bridgeList: [],
			tunnelList: [],
			eventData: [],
			title: '增长人数',
			times: '',
			chart: null,
			option: null,
			loadingList: {
				taskLoading: false,
				mainSecLoading: false,
				bridgeLoading: false,
				tunnelLoading: false,
				writeTaskLoading: false,
				eventLoading: false,
				constructionLoading: false,
			},
			constructionEvent: [],
			constructionUrlList: {
				日常养护: '/dailyMaintenance/construction/constructionSituation',
				养护工程: '/maintenanceProject/construction/constructionProgress',
				定期检测: '/regularTesting/constructionManage/constructionProgress',
				被损被盗: '/theft/construction/constructionProgress',
			},
			patrolData: null,
			fundObj: {
				日常养护: {
					fund1: 0, // 预算
					fund2: 0, // 实际
				},
				养护工程: {
					fund1: 0,
					fund2: 0,
				},
				被损被盗: {
					fund1: 0,
					fund2: 0,
				},
				定期检测: {
					fund1: 0,
					fund2: 0,
				},
				运营维护费: {
					fund1: 0,
					fund2: 0,
				},
			},
      taskParams: {
        applyUserName: '',
				businessKey: '',
				category: '',
				endTime: '',
				maintenanceSectionId: '',
				managementMaintenanceIds: [],
				pageNum: 1,
				pageSize: 20,
				startTime: '',
				title: '',
      }
		}
	},
	computed: {
		...mapGetters(['nickName']),
	},
	created() {
		this.times = moment().format('YYYY年MM月DD日')
		this.init()
	},
	mounted() {
		this.$nextTick(() => {
			const Element = document.querySelector('.leftTop1');
			let logoInfo = JSON.parse(localStorage.getItem('logoInfo'));
			if(logoInfo?.backgroundImage){
				Element.style.backgroundImage = `url("${logoInfo.backgroundImage}")`;
			}
		});
	},
	methods: {
		init() {
			this.getTaskList()
			this.getMainSecList()
			this.getBridgeList()
			this.getTunnelList()
			this.getWriteTask()
			this.getEventData()
			// this.getAgencyEvent();
			this.getPatrolData()
			this.getSumFundInfo()
		},
		// 获取任务列表
		getTaskList() {
			this.loadingList.taskLoading = true
			queryPageTodoTaskGroup(this.taskParams).then((res) => {
				this.taskList = res?.data?.records || []
				this.loadingList.taskLoading = false
			})
		},
		// 任务单列表点击
		handlerClick(row) {
			this.$router.push({ path: '/task/next', query: { ...row, ...this.taskParams } })
		},
		onHandler(row) {
			if (row.url) {
				this.$router.push({ path: row.url })
			} else {
				this.$modal.msgError('当前节点未配置地址')
			}
		},
		// 获取施工单执行情况
		getAgencyEvent() {
			this.loadingList.constructionLoading = true
			request({
				url: '/manager/construction/getAgencyEvent',
				method: 'get',
			}).then((res) => {
				this.constructionEvent = res.rows
				this.constructionEvent.forEach((item) => {
					item.url = this.constructionUrlList[item.category]
				})
				this.loadingList.constructionLoading = false
			})
		},
		// 获取事件数据
		getEventData() {
			const params = {}
			this.loadingList.eventLoading = true
			findDiseaseDataList(params).then((res) => {
				this.eventData = res.rows
				this.loadingList.eventLoading = false
			})
		},
		getSumFundInfo() {
			request({
				url: '/manager/disease/getProjTypeSumFundInfo',
				method: 'post',
				data: {
					year: new Date().getFullYear(),
				},
			}).then((res) => {
				const resultData = res.rows
				resultData.forEach((item) => {
					if (item.type === '日常养护费用') {
						this.fundObj['日常养护'].fund1 = item.sumFund
					}
					if (item.type === '日常养护实际费用') {
						this.fundObj['日常养护'].fund2 = item.sumFund
					}
					if (item.type === '专项工程费用') {
						this.fundObj['养护工程'].fund1 = item.sumFund
					}
					if (item.type === '专项养护实际费用') {
						this.fundObj['养护工程'].fund2 = item.sumFund
					}
					if (item.type === '被损被盗') {
						this.fundObj['被损被盗'].fund1 = item.sumFund
					}
					if (item.type === '被损被盗实际费用') {
						this.fundObj['被损被盗'].fund2 = item.sumFund
					}
					if (item.type === '养护检测费用') {
						this.fundObj['定期检测'].fund1 = item.sumFund
					}
					if (item.type === '养护检测实际费用') {
						this.fundObj['定期检测'].fund2 = item.sumFund
					}
					if (item.type === '运营费用') {
						this.fundObj['运营维护费'].fund1 = item.sumFund
					}
					if (item.type === '运营费用实际费用') {
						this.fundObj['运营维护费'].fund2 = item.sumFund
					}
				})
				this.setOption()
			})
		},
		setOption() {
			const data1 = []
			const data2 = []
			const xAxisData = []
			for (let key in this.fundObj) {
				xAxisData.push(key)
				const item = this.fundObj[key]
				data1.push(round(divide(item.fund1, 10000), 2))
				data2.push(round(divide(item.fund2, 10000), 2))
			}
			this.option = {
				tooltip: {
					show: true,
					trigger: 'axis',
					axisPointer: {
						type: 'shadow',
					},
					valueFormatter: (value) => {
						return `${value}万元`
					},
				},
				grid: {
					left: '3%',
					right: '4%',
					bottom: '50',
					containLabel: true,
				},
				legend: {
					data: ['预算', '实际完成'],
					bottom: '0',
				},
				xAxis: {
					type: 'category',
					data: xAxisData,
					axisLabel: {
						width: 70,
						interval: 0,
						overflow: 'truncate',
						ellipsis: '...',
					},
				},
				yAxis: {
					name: '万元',
					type: 'value',
				},
				series: [
					{
						name: '预算',
						data: data1,
						type: 'bar',
					},
					{
						name: '实际完成',
						data: data2,
						type: 'bar',
					},
				],
			}
		},
		// 获取路线基础信息
		getMainSecList() {
			const params = {
				pageNum: 10,
				pageSize: 1,
				managementMaintenanceIds: [],
				routeCodes: [],
				maintenanceSectionId: '',
			}
			this.loadingList.mainSecLoading = true
			getStatistics(params).then((res) => {
				this.mainSecList = res
				this.loadingList.mainSecLoading = false
			})
		},
		// 获取桥梁基础信息
		getBridgeList() {
			const params = {
				operationState: '2',
				managementMaintenanceIds: [],
				routeCodes: [],
				maintenanceSectionId: '',
			}
			this.loadingList.bridgeLoading = true
			bridgeListApi(params).then((res) => {
				this.bridgeList = res
				this.loadingList.bridgeLoading = false
			})
		},
		// 获取填报任务
		getWriteTask() {
			this.loadingList.writeTaskLoading = true
			listRepoteRecord().then((res) => {
				this.writeTaskList = res.rows
				this.loadingList.writeTaskLoading = false
			})
		},
		// 获取巡检查数据
		getPatrolData() {
			request({
				url: '/patrol/inspectionLogs/patrolMonthlyDetail',
				method: 'get',
			}).then((res) => {
				this.patrolData = res.data
			})
		},
		// 获取隧道基础信息
		getTunnelList() {
			const params = {
				operationState: '',
				checkYear: '2024',
				managementMaintenanceIds: [],
				routeCodes: [],
				maintenanceSectionId: '',
			}
			this.loadingList.tunnelLoading = true
			tunnelListApi(params).then((res) => {
				this.tunnelList = res
				this.loadingList.tunnelLoading = false
			})
		},
		routeChange(path) {
			this.$router.push(path)
		},
	},
}
</script>
<style lang="scss" scoped>
.route-load {
	padding: 10px;
	height: 100%;
	overflow-y: auto;

	::v-deep .table-header {
		background-color: #fff;
		font-size: 12px;
	}

	.el-card {
		border-radius: 10px;
		margin: 10px;

		.card-title {
			display: flex;
			justify-content: space-between;
			align-items: center;
			height: 28px;
			font-weight: 700;
		}

		::v-deep .el-card__body {
			height: 350px;
			overflow-y: auto;
		}
	}

	.task-center {
		height: 400px;
	}

	.executive-condition {
		height: 400px;

		.condition-item-top {
			display: flex;
			align-items: center;
			margin-bottom: 10px;

			.icon-box {
				width: 25px;
				height: 25px;
				margin-right: 5px;
				font-size: 14px;
				color: #ffffff;
				background-color: #ff9800;
				text-align: center;
				line-height: 25px;
				border-radius: 50%;
			}
		}

		::v-deep .el-carousel__button {
			width: 10px;
			background-color: #2196f3;
		}
		::v-deep .el-carousel__arrow {
			width: 20px;
			height: 20px;
		}
		::v-deep .el-carousel--horizontal {
			overflow: hidden;
		}

		::v-deep .el-carousel__indicators--horizontal {
			bottom: -10px;
		}
		::v-deep .el-carousel__arrow--left {
			left: 0;
		}
		::v-deep .el-carousel__arrow--right {
			right: 0;
		}

		.numTitle {
			font-size: 14px;
			color: #82848a;
			margin-bottom: 5px;
		}
		.num {
			font-size: 26px;
			font-weight: bolder;
		}
		.nums1 {
			font-size: 22px;
			font-weight: bolder;
		}
	}

	.jurisdiction-incident {
		height: 400px;
		.incident-item {
			display: flex;
			margin-bottom: 5px;

			.listIcon {
				flex-shrink: 0;
				color: #2196f3;
				background-color: #d8ebf3;
				text-align: center;
				width: 35px;
				height: 35px;
				line-height: 35px;
				border-radius: 50%;
				font-size: 24px;
				margin-top: 15px;
				margin-right: 10px;
			}

      .listContent {
        width: 100%;
        overflow: hidden;
      }
		}

    .ellipsis {
      width: 100%;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }

    .dis-title {
      cursor: pointer;
    }

    ::v-deep .el-descriptions__title {
      width: 100%;
    }
	}

	.budget-card {
		height: 400px;

		.e-chart-box {
			width: 100%;
			height: 100%;
		}
	}

	.line-info {
		height: 400px;
	}

	.bridge-info {
		height: 400px;
	}

	.leftTop1 {
		color: #fff;
		/* 原始背景图片 */
		//background-image: url('../../assets/other/1.png');
		// background-image: url('../../assets/other/2.jpg');
		/* 确保背景图片覆盖整个div */
		background-size: cover;
		background-position: center;
		background-repeat: no-repeat;
		/* 背景不重复 */

		/* 其他样式 */
		position: relative;
		width: 100%;
		height: 150px;

		.textItem {
			z-index: 2;
			position: absolute;
			font-size: 16px;
			font-weight: 500;
			line-height: 2;
			width: 100%;
		}
	}

	.leftTop1::before {
		content: '';
		position: absolute;
		top: 0;
		right: 0;
		bottom: 0;
		left: 0;
		background-image: -webkit-gradient(
			linear,
			left top,
			right top,
			from(rgba(255, 0, 0, 0)),
			to(rgba(255, 0, 0, 1))
		);
		background-image: linear-gradient(to right, rgb(4 144 68), rgb(5 145 68 / 20%));
		background-position: top left;
		background-size: 100% 100%;
		opacity: 0.9;
		border-radius: 10px;
	}

	.task-write {
		height: 230px;
	}

	.month-patrol {
		height: 400px;

    ::v-deep .el-card__body {
      padding: 0;
    }

		.patrol-overview-item {
			display: flex;
			align-items: center;

			.listIcon {
				width: 40px;
				height: 40px;
        margin-right: 5px;
			}

			.listContent {
        color: #303133;
				font-size: 15px;
        .nums {
          font-size: 28px;
          font-weight: bold;
          color: #1890ff;
          .num-unit {
            font-size: 15px;
            color: #b3b3b3;
          }
        }
			}
		}

		.patrol-item {
			display: flex;
			flex-direction: column;
			justify-content: center;
			width: 100%;
      margin-bottom: 10px;
      color: #303133;

			.patrol-item-top {
        position: relative;
				display: flex;
				align-items: center;
        padding-bottom: 4px;
				margin-bottom: 4px;
				font-size: 15px;
        border-bottom: solid 1px #f2f5fa;

        .patrol-item-icon {
          position: absolute;
          top: 50%;
          left: -20px;
          width: 15px;
          height: 15px;
          transform: translateY(calc(-50% - 4px));
        }
			}

			.patrol-item-bottom {
				width: 100%;
				display: flex;
				align-items: center;
        padding-left: 20px;

				.patrol-data {
          display: flex;
					flex-direction: column;
					// align-items: center;
					// justify-content: center;
          // margin-right: 20px;
          width: 50%;

          &:last-child {
            margin-right: 0;
          }
				}

				.patrol-text {
          font-size: 14px;
					margin-bottom: 5px;
          font-weight: 500;
				}

				.patrol-num {
					font-size: 22px;
					font-weight: bold;
          span {
            font-size: 15px;
            color: #b3b3b3;
            font-weight: normal;
          }
				}

        .patrol-num1 {
          color: #30ac82;
        }
			}
		}
	}

	.tunnel-info {
		height: 400px;
	}

	.listIcon2 {
		color: #ffffff;
		background-color: #ff9800;
		text-align: center;
		width: 30px;
		height: 30px;
		line-height: 30px;
		border-radius: 50%;
		font-size: 16px;
		margin-right: 8px;
	}
	.listIcon3 {
		color: #ffffff;
		background-color: #c73adf;
		text-align: center;
		width: 30px;
		height: 30px;
		line-height: 30px;
		border-radius: 50%;
		font-size: 16px;
		margin-right: 8px;
	}
}
</style>
