<template>
	<div class="drawing-upload">
		<div class="success-box" v-for="(item, index) in fileList" :key="index">
			<el-image :src="item.thumbUrl" alt="" :preview-src-list="[item.url]" ref="imageRefs" />
			<div class="name">{{ item.originalFilename || item.name }}</div>
			<div class="tool-box">
				<el-button type="primary" @click="previewFile(item, index)">预览</el-button>
				<el-button
					v-hasPermi="['baseData:drawing:download']"
					type="primary"
					@click="handleDownload(item)"
				>
					下载
				</el-button>
				<el-button
					v-hasPermi="['baseData:drawing:remove']"
					type="danger"
					@click="handleRemove(item)"
				>
					删除
				</el-button>
			</div>
		</div>
		<el-upload
			ref="fileUpload"
			multiple
			:action="uploadFileUrl"
			:before-upload="handleBeforeUpload"
			:http-request="customUpload"
			:file-list="fileList"
			:limit="limit"
			:accept="computedAccept"
			:on-error="handleUploadError"
			:on-exceed="handleExceed"
			:on-success="handleUploadSuccess"
			:headers="headers"
			:drag="true"
			:class="oneMap ? 'dark' : ''"
		>
			<i class="el-icon-upload"></i>
			<div class="el-upload__text">
				将文件拖到此处，或
				<em>点击上传</em>
			</div>
		</el-upload>
		<div class="tip">
			严禁在本互联网非涉密平台处理、传输国家秘密，请确认扫描、传输的文件资料不涉及国家秘密
		</div>
		<div v-if="isPreview && !wordUrl" class="mask"></div>
		<div v-if="isPreview" class="preview-box">
			<i class="el-icon-close body-close" @click="closePreview"></i>
			<div ref="container" class="box-container" v-if="pdfUrl">
				<iframe :src="pdfUrl" style="width: 100%; height: 100%; border-radius: 6px" />
				<!-- <VueOfficePdf :src="pdfUrl" style="width: 100%; height: calc(100% - 30px); border-radius: 6px;" /> -->
			</div>
			<div ref="container" class="box-container" v-if="excelUrl">
				<Luckysheet :file-url="excelUrl" style="width: 100%; height: 100%; border-radius: 6px" />
			</div>
			<div ref="container" class="box-container" v-if="wordUrl">
				<vue-office-docx :src="wordUrl" style="width: 100%; height: 100%; border-radius: 6px" />
			</div>
		</div>
	</div>
</template>

<script>
import { getToken } from '@/utils/auth'
import axios from 'axios'
import pdfIcon from '@/assets/images/Ipdf.png'
import wordIcon from '@/assets/images/Iword.png'
import excelIcon from '@/assets/images/Iexcel.png'
import pptIcon from '@/assets/images/Ippt.png'
import zipIcon from '@/assets/images/Izip.png'
import otherIcon from '@/assets/images/Iother.png'
import { removeFile, addUploadRecord, removeUploadRecord } from '@/api/system/fileUpload.js'
import { findFiles, updateFileSort, updateFileRemark } from '@/api/file/index.js'
import { getModuleFileCount, isExistFileDraw } from '@/api/system/moduleFile.js'
import Luckysheet from '@/components/Luckysheet'
import VueOfficeDocx from '@vue-office/docx'
import VueOfficePdf from '@vue-office/pdf'
// import "@vue-office/docx/lib/index.css";
import { mapGetters } from 'vuex'

export default {
	name: 'drawingUpload',
	props: {
		upLoadBind: {
			type: Object,
			default: () => {},
		},
	},
  inject: {
    oneMap: {
      default: false,
    },
  },
	components: { Luckysheet, VueOfficeDocx, VueOfficePdf },
	data() {
		return {
			number: 0,
			fileList: [],
			uploadList: [],
			value: [],
			limit: 0,
			fileSize: 0,
			fileType: [],
			fileSubId: '',
			bizId: '',
			ownerIdList: [],
			assetType: null,
			typeCode: '',
			storagePath: '/ylzx',
			isStatic: false,
			uploadFileUrl: `${process.env.VUE_APP_BASE_API}/file/upload`,
			headers: {
				Authorization: 'Bearer ' + getToken(),
			},
			platform: 'ylzx',
			isPreview: false,
			pdfUrl: '',
			excelUrl: '',
			wordUrl: '',
		}
	},
	created() {
		// 改组件只适用“图纸”功能，暂未对其他功能做适配
		this.fileSubId = this.upLoadBind.fileSubId
		this.bizId = this.upLoadBind.bizId
		this.ownerIdList = this.upLoadBind.ownerIdList
		this.assetType = this.upLoadBind.assetType
		this.storagePath = this.upLoadBind.storagePath
		this.limit = this.upLoadBind.limit
		this.fileSize = this.upLoadBind.fileSize
		this.fileType = this.upLoadBind.fileType
		;(this.value = this.upLoadBind.value), (this.typeCode = this.upLoadBind.typeCode)
	},
	methods: {
		handleBeforeUpload(file) {
			// 校检文件类型
			if (this.fileType) {
				const fileName = file.name.split('.')
				const fileExt = fileName[fileName.length - 1]
				const isTypeOk = this.fileType.indexOf(fileExt) >= 0
				if (!isTypeOk) {
					this.$modal.msgError(`文件格式不正确, 请上传${this.fileType.join('/')}格式文件!`)
					return false
				}
			}
			// 校检文件大小
			if (this.fileSize) {
				const isLt = file.size / 1024 / 1024 < this.fileSize
				if (!isLt) {
					this.$modal.msgError(`上传文件大小不能超过 ${this.fileSize} MB!`)
					return false
				}
			}
			// 更新 sort 值
			this.currentSort = this.getMaxSort() + this.number
			this.$modal.loading('正在上传文件，请稍候...')
			this.number++
			return true
		},
		customUpload({ file }) {
			const formData = new FormData()
			formData.append('file', file)

			const url = `${this.uploadFileUrl}?platform=${this.platform}&storagePath=${this.storagePath}&isStatic=${this.isStatic}&sort=${this.currentSort}`

			axios
				.post(url, formData, {
					headers: {
						'Content-Type': 'multipart/form-data',
						...this.headers,
					},
				})
				.then((response) => {
					this.handleUploadSuccess(response.data, file)
				})
		},
		handleUploadError() {
			this.$modal.msgError('上传文件失败，请重试')
			this.$modal.closeLoading()
		},
		handleExceed() {
			this.$modal.msgError(`上传文件数量不能超过 ${this.limit} 个!`)
		},
		handleUploadSuccess(res, file) {
			if (res.code === 200) {
				// 根据文件类型设置缩略图
				const thumbUrl = this.getFileThumbnailUrl(res.data)
				const temp = {
					ext: res.data.ext,
					thumbUrl: thumbUrl,
					id: res.data.id,
					remoteUrl: res.data.url,
					contentType: res.data.contentType,
					name: res.data.originalFilename,
					url: res.data.url,
					uid: file.uid,
					ownerId: res.data.ownerId,
					sort: res.data.sort,
				}
				this.uploadList.push(temp)
				if (!(this.bizId === '' && this.fileSubId === 0)) {
					addUploadRecord({
						fileSubId: this.fileSubId,
						bizId: this.bizId,
						fileName: res.data.originalFilename,
						fileId: res.data.id,
						fileSize: res.data.fileSize,
						ownerId: res.data.ownerId,
						assetType: this.assetType,
					}).then((res) => {
						this.changeStatus()
					})
				}
				this.uploadedSuccessfully()
				if (this.successHook) {
					this.successHook(res.data)
				}
			} else {
				this.number--
				this.$modal.msgError(res.msg)
				this.fileList.splice(findex, 1)
				this.uploadedSuccessfully()
			}
			this.$modal.closeLoading()
		},
		uploadedSuccessfully() {
			if (this.number > 0 && this.uploadList.length === this.number) {
				this.fileList = this.fileList.concat(this.uploadList)
				// 按照fileList的sort排序
				this.fileList.sort((a, b) => a.sort - b.sort)
				this.uploadList = []
				this.number = 0
				this.$modal.closeLoading()
			}
		},
		// 删除
		handleRemove(file) {
			this.$confirm('确定删除该文件吗？', '提示', {
				confirmButtonText: '确定',
				cancelButtonText: '取消',
				type: 'warning',
			}).then(() => {
				if (!file.id) return
				const findex = this.fileList.map((f) => f.id).indexOf(file.id)
				if (findex > -1) {
					if (this.bizId === '' && this.fileSubId === 0) {
						removeFile(file.id).then((res) => {
							if (res.code === 200) {
								this.fileList.splice(findex, 1)
								this.$message({
									type: 'success',
									message: '删除成功!',
								})
								this.changeStatus()
							}
						})
					} else {
						removeUploadRecord({
							fileId: file.id,
							assetType: this.assetType,
							fileSubId: this.fileSubId,
						}).then(() => {
							removeFile(file.id).then((res) => {
								if (res.code === 200) {
									this.fileList.splice(findex, 1)
									this.$message({
										type: 'success',
										message: '删除成功!',
									})
									this.changeStatus()
								}
							})
						})
					}
				}
			})
		},
		// 预览
		previewFile(file, index) {
			const contentType = file.contentType
			// 获取文件扩展名
			let fileExt = ''
			if (file.ext) {
				fileExt = file.ext.toLowerCase()
			} else if (file.name) {
				fileExt = file.name.split('.').pop().toLowerCase()
			}

			if (contentType) {
				if (contentType.startsWith('image/')) {
					this.$refs.imageRefs[index].showViewer = true
				} else {
					switch (contentType) {
						case 'application/pdf':
							const pdfjsURL = '/pdfjs/web/viewer.html?file='
							// 确保 file 参数是完整的绝对 URL
							const pdfFileUrl = encodeURIComponent(file.url)
							this.pdfUrl = pdfjsURL + pdfFileUrl
							this.isPreview = true
							this.$nextTick(() => {
								this.addWatermark(this.nickName, this.$refs.container)
							})
							break
						case 'application/vnd.ms-excel':
						case 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet':
							this.excelUrl = file.url
							this.isPreview = true
							this.$nextTick(() => {
								this.addWatermark(this.nickName, this.$refs.container)
							})
							break
						case 'application/msword':
						case 'application/vnd.openxmlformats-officedocument.wordprocessingml.document':
							this.wordUrl = file.url
							this.isPreview = true
							this.$nextTick(() => {
								this.addWatermark(this.nickName, this.$refs.container)
							})
							break
						case 'application/zip':
						case 'application/x-zip-compressed':
						case 'application/x-rar-compressed':
						case 'application/x-7z-compressed':
						case 'application/x-tar':
						case 'application/gzip':
						case 'application/x-gzip':
						case 'application/vnd.openxmlformats-officedocument.presentationml.presentation':
							this.$modal.msgError('此格式暂不支持预览，请自行下载')
							break
						// case '':
						//   this.$modal.msgError('该文件已损坏，无法预览，请自行下载')
						//   break;
					}
				}
				return
			}

			if (fileExt) {
				switch (fileExt) {
					case 'pdf':
						const pdfjsURL = '/pdfjs/web/viewer.html?file='
						// 确保 file 参数是完整的绝对 URL
						const pdfFileUrl = encodeURIComponent(file.url)
						this.pdfUrl = pdfjsURL + pdfFileUrl
						this.isPreview = true
						this.$nextTick(() => {
							this.addWatermark(this.nickName, this.$refs.container)
						})
						break
					case 'doc':
					case 'docx':
						this.wordUrl = file.url
						this.isPreview = true
						this.$nextTick(() => {
							this.addWatermark(this.nickName, this.$refs.container)
						})
						break
					case 'xls':
					case 'xlsx':
						this.excelUrl = file.url
						this.isPreview = true
						this.$nextTick(() => {
							this.addWatermark(this.nickName, this.$refs.container)
						})
						break
					case 'zip':
					case 'rar':
					case '7z':
					case 'tar':
					case 'gz':
					case 'ppt':
					case 'pptx':
						this.$modal.msgError('此格式暂不支持预览，请自行下载')
						break
					case 'jpg':
					case 'jpeg':
					case 'png':
					case 'gif':
					case 'bmp':
					case 'webp':
						this.$refs.imageRefs[index].showViewer = true
						break
				}
				return
			}
		},
		// 关闭预览
		closePreview() {
			this.isPreview = false
			this.pdfUrl = ''
			this.excelUrl = ''
			this.wordUrl = ''
		},
		// 下载
		async handleDownload(file) {
			const url = file.url || (file.response && file.response.url)
			if (!url) {
				this.$message.error('文件无法下载，未找到文件的URL')
				return
			}
			this.$modal.loading('正在下载文件...')
			try {
				// 创建一个不包含 Authorization 的请求头
				const downloadHeaders = {
					...this.headers,
					'Content-Disposition': 'attachment', // 添加强制下载头
					'Content-Type': 'application/octet-stream', // 使用通用的二进制流类型
				}
				delete downloadHeaders.Authorization

				// 使用 axios 进行下载，因为它可以自动处理跨域认证
				const response = await axios({
					url: url,
					method: 'GET',
					responseType: 'blob',
					headers: downloadHeaders,
				})

				// 从响应头中获取文件类型
				const contentType = response.headers['content-type']
				const blob = new Blob([response.data], { type: contentType || 'application/octet-stream' })

				// 创建一个隐藏的 iframe 来处理下载
				const iframe = document.createElement('iframe')
				iframe.style.display = 'none'
				document.body.appendChild(iframe)

				const originalFileName = this.getFileName(file.name) || 'download'
				const fileExtension = originalFileName.split('.').pop()
				let fileName = this.downloadName || originalFileName

				// 如果 fileName 已经有后缀，去掉后缀
				if (fileName.includes('.')) {
					fileName = fileName.substring(0, fileName.lastIndexOf('.'))
				}

				const timestamp = this.needTime ? `_${this.$moment().format('YYYY-MM-DDTHH_mm_ss')}` : ''
				const finalFileName = `${fileName}${timestamp}.${fileExtension}`

				// 在 iframe 中创建 blob URL 并触发下载
				const iframeWindow = iframe.contentWindow
				const blobUrl = iframeWindow.URL.createObjectURL(blob)
				const link = iframeWindow.document.createElement('a')
				link.href = blobUrl
				link.download = finalFileName
				link.click()

				// 清理资源
				setTimeout(() => {
					iframeWindow.URL.revokeObjectURL(blobUrl)
					document.body.removeChild(iframe)
				}, 1000)
				this.$modal.closeLoading()
			} catch (error) {
				console.error('下载文件时发生错误:', error)
				this.$message.error('下载文件失败，请重试')
				this.$modal.closeLoading()
			}
		},

		// --------------------------------------

		getMaxSort() {
			const fileListMax = Math.max(...this.fileList.map((file) => file.sort || 0), 0)
			const uploadListMax = Math.max(...this.uploadList.map((file) => file.sort || 0), 0)
			return (
				Math.max(
					fileListMax,
					uploadListMax,
					(this.fileList.length + this.uploadList.length - 1) * 1000
				) + 1000
			)
		},
		// 格式化返回对象
		formatValue(list) {
			return Array.from(new Set(list.filter((item) => item.ownerId).map((el) => el.ownerId)))
		},
		// 根据文件类型获取缩略图
		getFileThumbnailUrl(file) {
			// 获取文件类型和缩略图URL
			const contentType = file.contentType
			const thumbUrl = file.thumbUrl

			// 如果有contentType且是图片类型，直接返回缩略图
			if (contentType && contentType.startsWith('image/')) {
				return thumbUrl
			}

			// 获取文件扩展名
			let fileExt = ''
			if (file.ext) {
				fileExt = file.ext.toLowerCase()
			} else if (file.name) {
				fileExt = file.name.split('.').pop().toLowerCase()
			}

			// 根据contentType或文件扩展名判断文件类型
			if (contentType) {
				switch (contentType) {
					case 'application/pdf':
						return pdfIcon
					case 'application/msword':
					case 'application/vnd.openxmlformats-officedocument.wordprocessingml.document':
						return wordIcon
					case 'application/vnd.ms-excel':
					case 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet':
						return excelIcon
					case 'application/vnd.ms-powerpoint':
					case 'application/vnd.openxmlformats-officedocument.presentationml.presentation':
						return pptIcon
					case 'application/zip':
					case 'application/x-zip-compressed':
					case 'application/x-rar-compressed':
					case 'application/x-7z-compressed':
					case 'application/x-tar':
					case 'application/gzip':
						return zipIcon
				}
			}

			// 如果没有contentType或未匹配到，根据文件扩展名判断
			if (fileExt) {
				switch (fileExt) {
					case 'pdf':
						return pdfIcon
					case 'doc':
					case 'docx':
						return wordIcon
					case 'xls':
					case 'xlsx':
						return excelIcon
					case 'ppt':
					case 'pptx':
						return pptIcon
					case 'zip':
					case 'rar':
					case '7z':
					case 'tar':
					case 'gz':
						return zipIcon
					case 'jpg':
					case 'jpeg':
					case 'png':
					case 'gif':
					case 'bmp':
					case 'webp':
						return thumbUrl
				}
			}

			// 默认返回其他文件图标
			return otherIcon
		},
		// 获取文件名称
		getFileName(name) {
			// 如果是url那么取最后的名字 如果不是直接返回
			if (name.lastIndexOf('/') > -1) {
				return name.slice(name.lastIndexOf('/') + 1)
			} else {
				return name
			}
		},
		// 修改图纸状态
		async changeStatus() {
			let hasFiles = false
			const res = await getModuleFileCount({ bizId: this.bizId })
			res.data?.find((el) => el.count > 0) ? (hasFiles = true) : (hasFiles = false)
			isExistFileDraw({
				assetType: this.assetType,
				bizId: this.bizId,
				isExistFileDraw: hasFiles ? '1' : '2',
			})
		},
		// Canvas 绘制水印
		addWatermark(text, container = document.body) {
			const canvas = document.createElement('canvas')
			canvas.width = 200
			canvas.height = 150
			canvas.style.display = 'none'

			const ctx = canvas.getContext('2d')
			ctx.font = '16px Arial'
			ctx.fillStyle = 'rgba(200, 200, 200, 0.3)'
			ctx.rotate((-20 * Math.PI) / 180)
			ctx.fillText(text, 10, 100)

			const watermarkDiv = document.createElement('div')
			watermarkDiv.style.position = 'fixed'
			watermarkDiv.style.top = '0'
			watermarkDiv.style.left = '0'
			watermarkDiv.style.width = '100%'
			watermarkDiv.style.height = '100%'
			watermarkDiv.style.pointerEvents = 'none'
			watermarkDiv.style.backgroundImage = `url(${canvas.toDataURL()})`
			watermarkDiv.style.zIndex = '9999'

			container.appendChild(watermarkDiv)
		},
	},
	computed: {
		// 生成 accept 属性值
		computedAccept() {
			// 如果 accept 传入了（不是 null），直接使用传入的值
			if (this.accept !== null) {
				return this.accept
			}
			// 如果 fileType 不为空，则生成 accept 字符串
			if (this.fileType && this.fileType.length > 0) {
				return this.fileType?.map((type) => `.${type}`).join(',')
			}
			// 否则返回空字符串
			return ''
		},
		...mapGetters(['nickName']),
	},
	watch: {
		value: {
			handler(val) {
				if (val) {
					const tempArr = []
					val.forEach(async (ownerId) => {
						const { data } = await findFiles({ ownerId })
						tempArr.push(
							...data.map((file) => {
								return {
									...file,
									name: file.originalFilename,
									url: file.url,
									ownerId: file.ownerId,
									sort: file.sort,
									thumbUrl: this.getFileThumbnailUrl(file),
								}
							})
						)
					})
					this.fileList = tempArr
				} else {
					this.fileList = []
				}
			},
			deep: true,
			immediate: true,
		},
	},
}
</script>

<style lang="scss" scoped>
.drawing-upload {
	width: 100%;
	max-height: 60vh;
	display: flex;
	flex-wrap: wrap;
	position: relative;
	overflow-y: auto;

	.success-box {
		cursor: pointer;
		width: 360px;
		height: 180px;
		border: 1px solid #d9d9d9;
		border-radius: 6px;
		margin-right: 10px;
		margin-bottom: 10px;
		position: relative;
		display: grid;
		place-items: center;

		&:hover {
			.tool-box {
				display: flex;
				align-items: center;
				justify-content: space-evenly;
				padding: 0 12px;
			}
		}

		.el-image {
			width: 358px;
			height: 178px;
			border-radius: 6px;
		}

		.name {
			position: absolute;
			bottom: 0;
			left: 0;
			width: 100%;
			height: 36px;
			line-height: 36px;
			padding: 0 12px;
			background: rgba(0, 0, 0, 0.3);
			color: #fff;
			font-size: 12px;
			white-space: nowrap;
			overflow: hidden;
			text-overflow: ellipsis;
			border-radius: 0 0 6px 6px;
		}

		.tool-box {
			display: none;
			background-color: rgba(0, 0, 0, 0.3);
			width: 358px;
			height: 178px;
			border-radius: 6px;
			position: absolute;
			top: 0;
			left: 0;
		}
	}

	.tip {
		display: block;
		width: 100%;
		height: 48px;
		line-height: 48px;
		color: #f56c6c;
	}

	.mask {
		position: fixed;
		top: 0;
		left: 0;
		width: 100%;
		height: 100%;
		background-color: rgba(0, 0, 0, 0.5);
		z-index: 999;
	}

	.preview-box {
		width: 80vw;
		height: 80vh;
		position: fixed;
		top: 50%;
		left: 50%;
		// 使用 transform 向上和向左移动自身宽度和高度的一半
		transform: translate(-50%, -50%);
		z-index: 1000;
		border-radius: 6px;

		.body-close {
			position: absolute;
			top: 5px;
			right: 5px;
			height: 23px;
			width: 23px;
			z-index: 9999;
			font-size: 21px;
			color: #fff;
			margin: 5px;
			border: 1px solid #909399;
			border-radius: 4px;
			background: #909399;
			cursor: pointer;
		}

		.box-container {
			position: relative;
			width: 100%;
			height: 100%;
			overflow: hidden;
		}
	}
}

::v-deep .el-upload-list {
	display: none;
}

.dark {
	::v-deep .el-upload-dragger {
		background: #0166fe33;
		border: 1px solid #0166fe;
	}

  ::v-deep .el-upload-dragger .el-upload__text {
    color: #fff;
  }
}
</style>
