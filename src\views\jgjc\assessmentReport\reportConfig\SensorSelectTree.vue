<template>
  <el-dialog :visible.sync="sensorTreeDialogVisible" width="500px" center append-to-body>
    <span slot="title" class="title">
      选择传感器
    </span>
    <el-form
        :model="form"
        :inline="true"
        label-position="left"
        label-width="140px"
        ref="ruleForm"
        label-suffix="："
        :ref="drawType+'sensorTree'"
    >
      <el-form-item label="当前选择传感器" prop="currentSelectSensorLabel">
        <span style="float: left">{{form.currentSelectSensorLabel}}</span>
      </el-form-item>
    </el-form>
    <div class="reportTree">
      <el-tree
          ref="reportTree"
          :data="monitorData"
          node-key="code"
          :default-expanded-keys="expandArray"
          :empty-text="emptyText"
          :highlight-current="true"
          @node-click="handleNodeClick"
      >
              <span
                  class="custom-tree-node"
                  slot-scope="{ node, data }"
                  :class="[addIcon(data.type)+'_ctrl']">
                <span class="label" :class="[showBigFont(addIcon(data.type))?'font16':'smallFont']">
                  <span class="label flexList" :class="[showBigFont(addIcon(data.type))?'font16':'smallFont']">
                  <span class="el-icon-circle-plus icons openIcon" v-if="addIconShow(addIcon(data.type))" ></span>
                  <span class="el-icon-remove icons closeIcon" v-if="addIconShow(addIcon(data.type))" ></span>

                  <span v-if="data.nodeLevel === 5">
                      <span  :class="structureType(data.type)" ></span>
                  </span>

                  <span :class="[treeiconBox[data.content]||'icon-cube']" class="iconfont proIcon" v-if="data.type === '监测类型'" ></span>
                  <span :class="['iconStyle', 'iconfont']"></span>
                    <span v-if="data.nodeLevel <= 8">{{data.content}}</span>
                    <span v-if="data.nodeLevel === 9">
                      {{data.specificMonitorTypeName + data.sensorInstallCode}}</span>
                  </span>
                </span>
              </span>
      </el-tree>
    </div>
    <span slot="footer" class="dialog-footer">
      <el-button size="mini" @click="sensorTreeDialogVisible = false">取 消</el-button>
      <el-button size="mini" type="primary" @click="confirmSelect">确 定</el-button>
    </span>
  </el-dialog>
</template>

<script>
import { cloneDeep } from "lodash";
export default {
  name: "SensorSelectTree",
  props: ['monitorData', "selectNewSensorCallBack", 'expandArray'],
  data() {
    return {
      // dialog是否显示
      sensorTreeDialogVisible: false,
      // 无数据时显示文字
      emptyText: "暂无数据",
      form: {
        currentSelectSensor: {},
        currentSelectSensorLabel: ''
      },
      formRules: {
        currentSelectSensorLabel: [
          {required: true, message: '选择传感器不可为空', trigger: 'blur'}
        ]
      },
    }
  },
  mounted() {
    // 监听编辑数据
    window.$Bus.$on("showSensorTree", (data) => {
      this.sensorTreeDialogVisible = true;
    });
  },
  destroyed() {
    window.$Bus.$off("showSensorTree");
  },
  methods: {
    // 增加Icon
    addIcon(type) {
      return addIcon(type);
    },

    // 点击节点
    handleNodeClick(data, node) {
      if(data.nodeLevel === 9){
        this.form.currentSelectSensor = cloneDeep(data)
        this.form.currentSelectSensorLabel = data.specificMonitorTypeName + data.sensorInstallCode
      }
    },

    // 确认选择
    confirmSelect(){
      if(this.form.currentSelectSensorLabel === ''){
        this.$message({type: 'warning', message: "选择传感器不可为空", duration:1000});
        return
      }
      this.selectNewSensorCallBack(this.form.currentSelectSensor)
      this.sensorTreeDialogVisible = false
    }
  }
}
</script>

<style scoped>
.reportTree{
  margin: 5px 0 0 15px;
  border-width: 1px;
  border-left:1px solid #D0D0D0;
}
.custom-tree-node {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 14px;
  padding: 2px;
}
.label {
  line-height: 28px;
  font-size: 14px;
  margin-left: 4px;
  margin-right: 4px;
  font-family: inherit;
  color: black;
  font-weight: inherit;
}
.title {
  font-family: "Courier New", Courier, monospace;
  font-size: 16px;
  font-weight: bolder;
}
/deep/.el-tree-node__content{
  height: 30px !important;
}
/deep/.el-tree-node__expand-icon{
  display: none;
}
.el-form-item {
  margin: 5px 15px;
  display: inline-block;
  width: 550px !important;
}
</style>
