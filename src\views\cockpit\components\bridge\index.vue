<template>
  <div class="bridge-index" v-loading="loading" element-loading-background="rgba(0, 0, 0, 0.4)">
    <!-- <div class="type">
      <el-select v-model="bridgeType" value-key="" placeholder="请选择" clearable style="width: 120px;"
        :popper-append-to-body="false">
        <el-option v-for="item in bridgeTypeOptions" :key="item.value" :label="item.label" :value="item.value" />
      </el-select>
    </div> -->
    <!-- <pie-echarts :height="'17vh'"></pie-echarts> -->
    <Echarts :option="option" v-if="option" :height="`100%`" key="bridgeKey" />
  </div>
</template>

<script>
import { calcFontSize, isBigScreen } from "../../util/utils";
import PieEcharts from "../echarts/pieEcharts.vue";
import Echarts from "../echarts/echarts.vue";
export default {
  components: {
    PieEcharts,
    Echarts,
  },
  props: {
    statisticsData: { default: () => { } },
  },
  data() {
    return {
      bridgeType: "",
      option: null,
      // bridgeTypeOptions: [
      //   {
      //     value: 1,
      //     label: "特大桥",
      //   },
      //   {
      //     value: 2,
      //     label: "大桥",
      //   },
      // ],
      isBig: isBigScreen(),
      loading: false,
    };
  },
  mounted() {
    this.loading = true;
    var outr = this.isBig ? "83%" : "79%";
    var inr = "74%";
    var numberdata = this.statisticsData.spanClassifyTypes.map(
      (item) => item.typeCount);

    // var titledata = ["大桥", "特大桥", "中桥", "小桥"];
    // let color = [
    //   "#00ffff",
    //   "#00cfff",
    //   "#006ced",
    //   "#518BFF",
    //   "#ffa800",
    //   "#ff5b00",
    //   "#ff3000",
    // ];
    const colorObj = {
      大桥: "#00ffff",
      特大桥: "#00cfff",
      中桥: "#006ced",
      小桥: "#518BFF",
    };
    this.option = this.initPieCharts(outr, inr, numberdata, colorObj);
    this.$nextTick(() => {
      this.loading = false;
    });
  },
  methods: {
    initPieCharts(outr = 135, inr = 125, numberdata = [], colorObj = []) {
      var total = 0;
      //计算总和
      for (var i = 0; i < numberdata.length; i++) {
        total += Number(numberdata[i]);
      }
      let placeHolderStyle = {
        normal: {
          label: {
            show: false,
          },
          labelLine: {
            show: false,
          },
          color: "rgba(0, 0, 0, 0)",
          borderColor: "rgba(0, 0, 0, 0)",
          borderWidth: 0,
        },
      };

      var data = [];
      let center = ["23%", "49%"] //此处调整圆环的位置
      let left = "22%"
      let size = 13
      for (var i = 0; i < this.statisticsData.spanClassifyTypes.length; i++) {
        let el = this.statisticsData.spanClassifyTypes[i];
        data.push(
          {
            value: el.typeCount,
            name: el.typeLabel,
            itemStyle: {
              normal: {
                borderWidth: 5,
                shadowBlur: 0,
                borderColor: colorObj[el.typeLabel],
                shadowColor: colorObj[el.typeLabel],
                color: colorObj[el.typeLabel],
                borderRadius: this.isBig ? 10 : 0,
              },
            },
          },
          {
            value: total / 60,
            name: "",
            itemStyle: placeHolderStyle,
          }
        );
      }

      var seriesObj = [
        {
          type: "pie",
          zlevel: 0,
          silent: true,
          radius: ["90%", "94%"],
          center,
          hoverAnimation: false,
          color: "rgba(0,62,122,1)",
          label: {
            normal: {
              show: false,
            },
          },
          labelLine: {
            normal: {
              show: false,
            },
          },
          data: [1],
        },
        {
          name: "",
          type: "pie",
          clockWise: false,
          startAngle: "90",
          center, //此处调整圆环的位置
          radius: [outr, inr], //此处可以调整圆环的大小
          hoverAnimation: false,
          itemStyle: {
            normal: {
              label: {
                show: false,
                position: "outside",
                color: "#fff",
                lineHeight: this.isBig ? 32 : 16,
                formatter: function (params) {
                  let percent = 0;
                  let total = 1;
                  for (let i = 0; i < data.length; i++) {
                    total += data[i].value;
                  }
                  percent = ((params.value / total) * 100).toFixed(2);
                  if (params.name !== "") {
                    return (
                      params.name +
                      `：${params.value}` +
                      "\n" +
                      "占比：" +
                      percent +
                      "%"
                    );
                  } else {
                    return "";
                  }
                },
                fontSize: this.isBig ? 30 : calcFontSize(12),
              },
              labelLine: {
                length: this.isBig ? 30 : 15,
                length2: this.isBig ? 60 : 30,
                show: false,
                color: "#00ffff",
              },
            },
          },
          data: data,
          animationType: "scale",
          animationEasing: "elasticOut",
          animationDelay: function (idx) {
            return idx * 50;
          },
        },
      ];

      let option = {
        backgroundColor: "rgba(0,0,0,0)",
        tooltip: {
          show: true,
          formatter: (params) => {
            if (params.name == "") {
              return "";
            }
            return `${params.name}: ${params.value}`;
          },
        },
        title: {
          text: `${total}`,
          subtext: "桥梁",
          top: "32%",
          textAlign: "center",
          left: left || "20%",
          textStyle: {
            color: "#fff",
            fontSize: this.isBig ? 54 : calcFontSize(22),
            fontWeight: "700",
            shadowColor: "rgba(27,126,242,0.8)",
            shadowBlur: 10,
            shadowOffsetX: 5,
            shadowOffsetY: 5,
          },
          subtextStyle: {
            fontSize: this.isBig ? 46 : calcFontSize(16),
            align: "center",
            color: "#fff",
          },
        },
        toolbox: {
          show: false,
        },
        series: seriesObj,
        legend: {
          show: true,
          // right: this.isBig ? '10%' : '5%',
          left: '45%',
          y: "center",
          icon: "circle",
          itemWidth: this.isBig ? 30 : 10, // 设置宽度
          itemHeight: this.isBig ? 30 : 10, // 设置高度
          itemGap: this.isBig ? 35 : 16,
          formatter: (name) => {
            let d = data.filter(v => v.name == name);
            let num = d[0].value;
            let percent = 0;
            percent = ((num / total) * 100).toFixed(2);
            return "{title|" + name + "：}" + "{num|" + num + "} " + "{unit|座}" + " {title|占比：}" + "{num|" + percent + "}" + "{unit|%}";
          },
          textStyle: {
            color: '#fff',
            rich: {
              title: {
                color: 'rgba(255,255,255,0.8)',
                fontSize: this.isBig ? size * 2 : size - 1,
                padding: [3, 0],
              },
              num: {
                color: 'rgba(255,255,255,1)',
                fontSize: this.isBig ? size * 2 : size,
              },
              unit: {
                color: 'rgba(255,255,255,0.8)',
                fontSize: this.isBig ? size * 1.8 : size - 1,
              },
              percent: {
                color: 'rgba(255,255,255,0.8)',
                fontSize: this.isBig ? size * 1.8 : size - 1,
              },
            }
          },
        },
      };
      return option;
    },
  },
};
</script>

<style lang="scss" scoped>
@import "@/assets/styles/utils.scss";

.bridge-index {
  padding: vwpx(20px) 0;
  color: white;
  position: relative;
  width: 100%;
  height: 100%;

  // .type {
  //   position: absolute;
  //   top: 10px;
  //   right: 10px;
  //   z-index: 99;
  // }

  // ::v-deep .el-input {
  //   .el-input__inner {
  //     background-color: rgba(1, 102, 254, 0.2);
  //     border: 1px solid #0166fe;
  //     color: #ffffff;
  //     // border-right: none;
  //   }

  //   .el-input__inner::placeholder {
  //     color: #bbbbbb;
  //   }

  //   .el-input-group__append {
  //     background-color: rgba(1, 102, 254, 0.2);
  //     border: 1px solid #0166fe;
  //     color: #ffffff;
  //     border-left: none;
  //     padding: 0 10px;
  //     cursor: pointer;
  //   }
  // }
}
</style>
