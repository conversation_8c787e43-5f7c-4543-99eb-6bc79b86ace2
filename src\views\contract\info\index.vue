<template>
  <div class="app-container maindiv">
    <el-row :gutter="20">
      <el-col :span="24" :xs="24">
        <el-row>
          <el-form
            ref="queryForm"
            :model="queryParams"
            size="mini"
            :inline="true"
            label-width="68px"
          >
            <!--            <el-form-item label="" prop="id">-->
            <!--              <el-input-->
            <!--                v-model="queryParams.id"-->
            <!--                placeholder="请输入合同编号"-->
            <!--                clearable-->
            <!--                style="width: 240px"-->
            <!--              >-->
            <!--              </el-input>-->
            <!--            </el-form-item>-->
            <el-form-item label="" prop="name">
              <el-input
                v-model="queryParams.name"
                placeholder="请输入合同名称"
                clearable
                style="width: 240px"
              >
              </el-input>
            </el-form-item>

            <el-form-item label="" prop="field103">
              <el-select v-model="queryParams.sectionName" filterable placeholder="请选择养护路段" clearable>
                <el-option
                  v-for="item in maintenanceSectionList"
                  :key="item.maintenanceSectionId"
                  :label="item.maintenanceSectionName"
                  :value="item.maintenanceSectionId"
                >
                </el-option>
              </el-select>
            </el-form-item>

            <el-form-item label="" prop="status">
              <DictSelect
                v-model="queryParams.isEnable"
                :type="'contract_status'"
                :placeholder="'状态'"
                clearable
              ></DictSelect>
            </el-form-item>

            <el-form-item>
              <el-button
                type="primary"
                icon="el-icon-search"
                size="mini"
                @click="handleQuery"
              >搜索
              </el-button
              >
              <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
              >重置
              </el-button
              >
            </el-form-item>
          </el-form>
        </el-row>
        <!--操作按钮区开始-->
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button
              type="primary"
              icon="el-icon-plus"
              size="mini"
              v-has-menu-permi="['contract:contract:add']"
              @click="handleDetail"
            >新增
            </el-button
            >
          </el-col>
          <el-col :span="1.5">
            <el-button
              type="danger"
              icon="el-icon-delete"
              v-has-menu-permi="['contract:contract:removeBatchByIds']"
              size="mini"
              @click="handleDelete"
            >批量删除
            </el-button
            >
          </el-col>
          <el-col :span="1.5">
            <el-button
              type="warning"
              icon="el-icon-download"
              v-has-menu-permi="['contract:contract:export']"
              size="mini"
              @click="exportList"
            >导出清单
            </el-button
            >
          </el-col>
          <!--          <el-col :span="1.5">-->
          <!--            <el-button-->
          <!--              type="success"-->
          <!--              icon="el-icon-refresh"-->
          <!--              size="mini"-->
          <!--              @click="openUpdate = true"-->
          <!--            >路段合同更新-->
          <!--            </el-button-->
          <!--            >-->
          <!--          </el-col>-->
          <right-toolbar
            :showSearch.sync="showSearch"
            @queryTable="handleQuery"
            :columns="columns"
          ></right-toolbar>
        </el-row>
        <!--操作按钮区结束-->
        <!--数据表格开始-->
        <el-row>
          <div class="tableDiv">
            <el-table v-adjust-table
              size="mini"
              style="width: 100%"
              v-loading="loading"
              border
              stripe
              ref="dataTable"
              @row-click="handleClickRow"
              :height="
                showSearch ? 'calc(100vh - 330px)' : 'calc(100vh - 270px)'
              "
              :data="contractList"
              @selection-change="handleSelectionChange"
            >
              <el-table-column type="selection" width="100" align="center"/>
              <el-table-column label="序号" type="index" width="100">
              </el-table-column>
              <!--              <el-table-column-->
              <!--                label="合同编码"-->
              <!--                v-if="columns[0].visible"-->
              <!--                align="center"-->
              <!--                prop="field1"-->
              <!--                width="80"-->
              <!--              />-->
              <el-table-column
                label="合同名称"
                v-if="columns[1].visible"
                align="center"
                prop="name"
                width="200"
              />
              <el-table-column
                label="合同类型"
                v-if="columns[2].visible"
                align="center"
                prop="type"
                width="80"
              >
                <template slot-scope="scope">
                  <dict-tag :options="dict.type.contract_type" :value="scope.row.type"/>
                </template>
              </el-table-column>
              <el-table-column
                label="业主单位"
                v-if="columns[3].visible"
                align="center"
                prop="owrUnit"
                width="200"
              />
              <el-table-column
                label="承包商"
                v-if="columns[4].visible"
                align="center"
                prop="contractor"
                width="200"
              />
              <el-table-column
                label="养护路段"
                v-if="columns[5].visible"
                align="center"
                prop="sectionName"
                width="300"
              >
                <template slot-scope="scope">
                    <el-tooltip className="item" effect="dark" :content="scope.row.sectionName" placement="top-start">
                        <div slot="content" class="text_content">
                            {{ scope.row.sectionName }}
                        </div>
                        <div class="text_els">
                            {{ scope.row.sectionName }}
                        </div>
                    </el-tooltip>
                </template>
              </el-table-column>
              <el-table-column
                label="金额"
                v-if="columns[6].visible"
                align="center"
                prop="amount"
                width="100"
              />
              <el-table-column
                label="报价体系"
                v-if="columns[7].visible"
                align="center"
                prop="libName"
                width="200"
              />
              <el-table-column
                label="监理费率"
                v-if="columns[8].visible"
                align="center"
                prop="superviseRate"
                width="100"
              />
              <el-table-column
                label="设计费率"
                v-if="columns[9].visible"
                align="center"
                prop="designRate"
                width="100"
              />
              <el-table-column
                label="日养安全费率"
                v-if="columns[10].visible"
                align="center"
                prop="dSafeProductionRate"
                width="130"
              />
              <el-table-column
                label="日养保通费率"
                v-if="columns[11].visible"
                align="center"
                prop="dSafeGuaranteeRate"
                width="130"
              />
              <el-table-column
                label="专项安全费率"
                v-if="columns[12].visible"
                align="center"
                prop="pSafeProductionRate"
                width="130"
              />
              <el-table-column
                label="专项保通费率"
                v-if="columns[13].visible"
                align="center"
                prop="pSafeGuaranteeRate"
                width="130"
              />
              <el-table-column
                label="开始时间"
                v-if="columns[14].visible"
                align="center"
                prop="beginDate"
                width="80"
              />
              <el-table-column
                label="结束时间"
                v-if="columns[15].visible"
                align="center"
                prop="endDate"
                width="80"
              />
              <el-table-column
                label="锁定状态"
                align="center"
                prop="isLock"
                width="80"
              >
                <template slot-scope="props">
                  <el-link type="primary" v-if="props.row.isLock === 0" @click="handleLock(props.row, 1)">锁定</el-link>
                  <el-link type="primary" v-else @click="handleLock(props.row, 0)">解锁</el-link>
                </template>
              </el-table-column>
              <el-table-column label="启用状态" align="center" width="80" prop="isEnable">
                <template slot-scope="props">
                  <el-link type="primary" v-if="props.row.isEnable === 0" @click="handleEnbel(props.row, 1)">启用
                  </el-link>
                  <el-link type="primary" v-else @click="handleEnbel(props.row, 0)">停用</el-link>
                </template>
              </el-table-column>
              <el-table-column label="附件" align="center" width="80">
                <template slot-scope="props">
                  <el-link type="primary" @click="handleFileInfo(props.row.id)">查看</el-link>
                </template>
              </el-table-column>

              <el-table-column
                label="操作"
                fixed="right"
                align="center"
                width="160"
                class-name="small-padding fixed-width"
              >
                <template slot-scope="scope">
                  <el-button
                    size="mini"
                    type="text"
                    icon="el-icon-edit"
                    :disabled="scope.row.isLock != 0"
                    v-has-menu-permi="['contract:contract:edit']"
                    @click="handleUpdate(scope.row)"
                  >修改
                  </el-button
                  >
                  <el-button
                    size="mini"
                    type="text"
                    icon="el-icon-delete"
                    :disabled="scope.row.isLock != 0"
                    v-has-menu-permi="['contract:contract:delete']"
                    @click="handleDelete(scope.row)"
                  >删除
                  </el-button
                  >
                </template>
              </el-table-column>
            </el-table>

            <pagination
              v-show="total > 0"
              :total="total"
              :page.sync="queryParams.pageNum"
              :limit.sync="queryParams.pageSize"
              @pagination="handleQuery"
            />
          </div>
        </el-row>
        <!--数据表格结束-->
      </el-col>
    </el-row>
    <!-- 详情信息弹框 -->
    <el-dialog :title="detailTitle" v-if="openDetail" destroy-on-close :visible.sync="openDetail" width="80%">
      <Detail @close="handleCloseDetail" :contract="contract"></Detail>
    </el-dialog>
    <!-- 详情信息弹框结束-->

    <!-- 附件弹框 -->
    <el-dialog title="附件信息查询" v-if="openFileInfo" destroy-on-close :visible.sync="openFileInfo" width="80%">
      <FileInfo @close="handleCloseFileInfo" :conId="conId"></FileInfo>
    </el-dialog>
    <!-- 附件弹框结束-->

    <!-- 合同更新弹窗-->
<!--    <el-dialog title="养护路段合同更新" destroy-on-close :visible.sync="openUpdate" width="400px">-->
<!--      <div class="road-interflow-edit">-->
<!--        <el-row :gutter="15">-->
<!--          <el-form-->
<!--            ref="updateForm"-->
<!--            :model="updateParams"-->
<!--            size="mini"-->
<!--            :inline="true"-->
<!--          >-->
<!--            <el-col :span="24">-->
<!--              <el-form-item label="养护路段" prop="field140" required>-->
<!--                <el-input-->
<!--                  v-model="updateParams.field140"-->
<!--                  placeholder="输入搜索..."-->
<!--                  clearable-->
<!--                  :style="{ width: '100%' }"-->
<!--                >-->
<!--                </el-input>-->
<!--              </el-form-item>-->
<!--            </el-col>-->
<!--            <el-col :span="24">-->
<!--              <el-form-item label="原合同" prop="field141" required>-->
<!--                <el-input-->
<!--                  v-model="updateParams.field141"-->
<!--                  placeholder="输入搜索..."-->
<!--                  clearable-->
<!--                  :style="{ width: '100%' }"-->
<!--                >-->
<!--                </el-input>-->
<!--              </el-form-item>-->
<!--            </el-col>-->
<!--            <el-col :span="24">-->
<!--              <el-form-item label="新合同" prop="field142" required>-->
<!--                <el-input-->
<!--                  v-model="updateParams.field142"-->
<!--                  placeholder="输入搜索..."-->
<!--                  clearable-->
<!--                  :style="{ width: '100%' }"-->
<!--                >-->
<!--                </el-input>-->
<!--              </el-form-item>-->
<!--            </el-col>-->
<!--            <el-col :span="24">-->
<!--              <el-form-item label="是否验证老合同" prop="field143" required>-->
<!--                <el-radio-group v-model="updateParams.field143">-->
<!--                  <el-radio :label="1">是</el-radio>-->
<!--                  <el-radio :label="0">否</el-radio>-->
<!--                </el-radio-group>-->
<!--              </el-form-item>-->
<!--            </el-col>-->
<!--            <el-col :span="24">-->
<!--              <el-form-item label="更新模块" prop="field144" required>-->
<!--                <el-checkbox-group v-model="updateParams.field144">-->
<!--                  <el-checkbox-->
<!--                    style="display: block"-->
<!--                    label="日常养护"-->
<!--                  ></el-checkbox>-->
<!--                  <el-checkbox-->
<!--                    style="display: block"-->
<!--                    label="被损被盗"-->
<!--                  ></el-checkbox>-->
<!--                  <el-checkbox-->
<!--                    style="display: block"-->
<!--                    label="专项养护"-->
<!--                  ></el-checkbox>-->
<!--                  <el-checkbox-->
<!--                    style="display: block"-->
<!--                    label="定期检查"-->
<!--                  ></el-checkbox>-->
<!--                </el-checkbox-group>-->
<!--              </el-form-item>-->
<!--            </el-col>-->
<!--          </el-form>-->
<!--          <div style="text-align: right">-->
<!--            <el-button type="primary" size="mini" @click="onSearch"-->
<!--            >保存-->
<!--            </el-button-->
<!--            >-->
<!--            <el-button size="mini" @click="openUpdate = false">退出</el-button>-->
<!--          </div>-->
<!--        </el-row>-->
<!--      </div>-->
<!--    </el-dialog>-->
    <!-- 合同更新弹窗结束-->
  </div>
</template>
<script>
import Detail from './detail.vue'
import FileInfo from './fileInfo.vue'
import {getList, deleteContract, saveContract} from '@/api/contract/info/index.js'
import {listMaintenanceSectionAll} from '@/api/system/maintenanceSection'

export default {
  name: 'ContractInfo',
  components: {Detail, FileInfo},
  props: [],
  dicts: ['contract_type'],
  data() {
    return {
      loading: false,
      showSearch: false,
      contract: {},
      // 查询参数
      queryParams: {
        name: undefined,
        sectionName: undefined,
        status: undefined,
        pageNum: 1,
        pageSize: 10
      },
      // 养护路段
      maintenanceSectionList: [],
      // 列信息
      columns: [
        {key: 0, label: `合同编码`, visible: true},
        {key: 1, label: `合同名称`, visible: true},
        {key: 2, label: `合同类型`, visible: true},
        {key: 3, label: `业主单位`, visible: true},
        {key: 4, label: `承包商`, visible: true},
        {key: 5, label: `养护路段`, visible: true},
        {key: 6, label: `金额`, visible: true},
        {key: 7, label: `报价体系`, visible: true},
        {key: 8, label: `监理费率`, visible: true},
        {key: 9, label: `设计费率`, visible: true},
        {key: 10, label: `日养安全费率`, visible: true},
        {key: 11, label: `日养保通费率`, visible: true},
        {key: 12, label: `专项安全费率`, visible: true},
        {key: 13, label: `专项保通费率`, visible: true},
        {key: 14, label: `开始时间`, visible: true},
        {key: 15, label: `结束时间`, visible: true}
      ],
      // 合同列表
      contractList: [],
      total: 0,
      // 窗口打开标志
      openDetail: false,
      detailTitle: '新增合同',
      openFileInfo: false,
      openUpdate: false,
      selectIds: [],
      updateParams: {
        field140: undefined,
        field141: undefined,
        field142: undefined,
        field143: undefined,
        field144: []
      },
      conId: ''
    }
  },
  computed: {},
  watch: {},
  created() {
    this.handleQuery()
    this.getMaintenanceSection()

  },
  mounted() {
  },
  methods: {
    // 表单查询
    handleQuery() {
      this.loading = true
      getList(this.queryParams).then((res) => {
        this.contractList = res.rows
        this.total = res.total
        this.loading = false
      })
    },
    getMaintenanceSection() {
      listMaintenanceSectionAll().then(res => {
        this.maintenanceSectionList = res.data
      })
    },
    // 表单重置
    resetQuery() {
      this.queryParams = {
        name: undefined,
        sectionName: undefined,
        status: undefined,
        pageNum: 1,
        pageSize: 10
      }
      this.handleQuery()
    },
    handleUpdate(e) {
      this.contract = e
      this.detailTitle = '修改合同'
      this.openDetail = true
    },
    // 打开详情弹窗
    handleDetail() {
      this.contract = {}
      this.detailTitle = '新增合同'
      this.openDetail = true
    },
    // 选中
    handleSelectionChange(e) {
      this.selectIds = e.map(obj => obj.id)
    },
    handleDelete(e) {
      let ids = []
      if (e.id) {
        ids.push(e.id)
      } else {
        ids.push(...this.selectIds)
      }
      let that = this
      this.$modal.confirm('是否删除').then(function () {
        deleteContract(ids).then(res => {
          that.$modal.msgSuccess('删除成功')
          that.handleQuery()
        })
      })
    },
    // 导出清单按钮
    exportList() {
      this.download(
        'manager/contract/export',
        {...this.queryParams},
        `contract_${new Date().getTime()}.xlsx`,
        {
          headers: { 'Content-Type': 'application/json;' },
          parameterType: 'body'
        }
      )
    },
    // 锁定
    handleLock(data, isLock) {
      data.isLock = isLock
      saveContract(data).then(res => {
        this.$modal.msgSuccess('保存成功')
        this.handleQuery()
      })
    },
    // 启用
    handleEnbel(data, isEnable) {
      data.isEnable = isEnable
      saveContract(data).then(res => {
        this.$modal.msgSuccess('保存成功')
        this.handleQuery()
      })
    },
    // 关闭详情弹窗
    handleCloseDetail() {
      this.openDetail = false
      this.handleQuery()
    },
    // 打开详情弹窗
    handleFileInfo(id) {
      this.conId = id
      this.openFileInfo = true
    },
    handleClickRow(e) {
      e.isSelected = !e.isSelected;
      this.$refs.dataTable.toggleRowSelection(e);
    },
    // 关闭详情弹窗
    handleCloseFileInfo() {
      this.openFileInfo = false
    }
  }
}
</script>
<style lang="scss" scoped>
.text_content {
  display: block;
  max-width: 700px;
  white-space: wrap;
  font-size: 14px;
}
.text_els {
  width: 100%;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
</style>
<style lang="scss" scoped>
@import "@/assets/styles/business.scss";
</style>
