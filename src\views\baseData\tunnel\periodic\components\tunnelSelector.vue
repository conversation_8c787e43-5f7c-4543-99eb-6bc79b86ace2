<template>
  <div>
    <el-dialog
      title="隧道信息"
      :visible.sync="show"
      width="60%"
      append-to-body
      :before-close="handleClose"
    >
      <div
        style="height: 60vh;"
        class="container-view-list"
      >
        <el-form
          :model="queryParams"
          :inline="true"
        >
          <el-form-item>
            <CascadeSelection
              :form-data="queryParams"
              v-model="queryParams"
              types="201"
              :gutter="10"
              multiple
            />
          </el-form-item>
          <el-form-item>
            <el-select
              v-model="queryParams.lengthClassification"
              placeholder="隧道种类"
              clearable
            >
              <el-option
                v-for="item in dict.type.tunnel_length_classification"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-input
              v-model="queryParams.tunnelName"
              placeholder="隧道名称"
              clearable
            ></el-input>
          </el-form-item>
          <el-form-item>
            <el-button
              type="primary"
              @click="getList"
            >搜索</el-button>
          </el-form-item>
        </el-form>
        <el-table
          v-loading="loading"
          ref="table"
          height="100%"
          border
          :header-cell-style="{'height': '36px'}"
          :data="tableData"
          :row-style="rowStyle"
          @row-click="handleRowClick"
        >
          <el-table-column
            label="序号"
            type="index"
            width="50"
            align="center"
          />
          <el-table-column
            label="隧道编码"
            align="center"
            prop="tunnelCode"
            min-width="120"
            show-overflow-tooltip
          />
          <el-table-column
            label="隧道名称"
            align="center"
            prop="tunnelName"
            min-width="120"
            show-overflow-tooltip
          />
          <el-table-column
            label="隧道种类"
            align="center"
            prop="lengthClassification"
            min-width="80"
            show-overflow-tooltip
          >
            <template slot-scope="scope">
              <dict-tag
                :options="dict.type.tunnel_length_classification"
                :value="scope.row.lengthClassification"
              />
            </template>
          </el-table-column>
          <el-table-column
            label="路线编码"
            align="center"
            prop="routeCode"
            min-width="80"
            show-overflow-tooltip
          />
          <el-table-column
            label="养护路段"
            align="center"
            prop="maintenanceSectionName"
            min-width="80"
            show-overflow-tooltip
          />
          <el-table-column
            label="管理处"
            align="center"
            prop="managementMaintenanceName"
            min-width="120"
            show-overflow-tooltip
          />
        </el-table>
        <pagination
          :total="total"
          :page.sync="queryParams.pageNum"
          :limit.sync="queryParams.pageSize"
          :pageSizes="[10, 20, 30, 50, 100, 1000]"
          @pagination="getList"
        />
      </div>
      <div slot="footer">
        <el-button
          type="primary"
          :loading="loading"
          @click="handleSubmit"
        >确 定</el-button>
        <el-button @click="handleClose">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listStatic } from '@/api/baseData/tunnel/baseInfo/index'
import CascadeSelection from '@/components/CascadeSelection/index.vue'

export default {
  name: '',
  props: {
    show: { type: Boolean, default: false }
  },
  components: { CascadeSelection },
  dicts: ['tunnel_length_classification'],
  data() {
    return {
      loading: false,
      tableData: [],
      queryParams: {
        pageNum: 1,
        pageSize: 10
      },
      total: 0,
      currentRow: null,
      routeOptions: [],
      routeList: []
    }
  },
  created() {
    this.getList()
  },
  methods: {
    getList() {
      this.loading = true
      listStatic(this.queryParams)
        .then(res => {
          if (res.code === 200) {
            this.tableData = res.rows
            this.total = res.total
          }
        })
        .finally(() => {
          this.loading = false
        })
    },
    handleSubmit() {
      if (!this.currentRow) {
        this.$message.warning('请选择一条数据！')
        return
      }
      const obj = {
        tunnelName: this.currentRow.tunnelName,
        id: this.currentRow.id,
        managementMaintenanceId: this.currentRow.managementMaintenanceId,
        assessmentGrade: this.currentRow.assessmentGrade
      }
      this.$emit('close', obj)
    },
    handleClose() {
      this.$emit('close', false)
    },
    // 表格点击勾选
    handleRowClick(row) {
      if (this.currentRow === row) {
        this.currentRow = null
      } else {
        this.currentRow = row
      }
      row.isSelected = !row.isSelected
    },
    // 勾选高亮
    rowStyle({ row, rowIndex }) {
      if (this.currentRow == row) {
        return { 'background-color': '#b7daff', color: '#333' }
      } else {
        return { 'background-color': '#fff', color: '#333' }
      }
    }
  },
  computed: {},
  watch: {}
}
</script>

<style lang="scss" scoped>
.container-view-list {
  padding: 0 !important;
  v-deep .el-table {
    flex: 1;
  }
}
::v-deep {
  .el-table__row {
    cursor: pointer;
  }
  .el-table__row:hover td {
    background: #b7daff !important;
  }
}
</style>
