<template>
  <PageContainer>
    <template slot="search">
      <div style="display: flex; align-items: center; margin: 0">
        <!-- <CascadeSelection
          style="min-width:192px;"
          :form-data="queryParams"
          v-model="queryParams"
          types="201"
          multiple
        />
        <div style="margin-left: 20px">
          <el-date-picker
            style="width: 100%"
            v-model="queryParams.checkYear"
            type="year"
            placeholder="年份"
          />
        </div>
        <div style="margin-left: 20px">
          <el-input
            v-model="queryParams.tunnelName"
            style="width:100%;"
            placeholder="隧道名称"
            clearable
          />
        </div>
        <div style="margin-left: 20px">
          <el-input
            v-model="queryParams.tunnelCode"
            style="width:100%"
            placeholder="隧道编码"
            clearable
          />
        </div>
        <div style="margin-left: 20px">
          <el-select
            v-model="queryParams.isChecked"
            style="width:100%"
            placeholder="是否检测"
            clearable
          >
            <el-option
              v-for="dict in dict.type.base_data_yes_no"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </div>
        <div style="margin: 0 20px">
          <el-select
            v-model="queryParams.assessmentGrade"
            style="width:100%"
            placeholder="技术状况等级"
            clearable
          >
            <el-option
              v-for="dict in dict.type.tunnel_assess_grade"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </div> -->
        <CascadeSelection
          :form-data="queryParams"
          v-model="queryParams"
          types="201"
          multiple
        />
        <div style="margin-left: 20px">
          <el-input
            v-model="queryParams.tunnelName"
            style="width:100%;"
            placeholder="隧道名称"
            clearable
          />
        </div>
        <div style="margin-left: 20px">
          <el-input
            v-model="queryParams.tunnelCode"
            style="width:100%"
            placeholder="隧道编码"
            clearable
          />
        </div>
        <div style="margin:0 20px">
          <el-date-picker
            style="width: 100%"
            v-model="queryParams.checkYear"
            type="year"
            placeholder="年份"
            value-format="yyyy"
          />
        </div>

        <div style="min-width:240px;">
          <el-button
            v-hasPermi="['baseData:evaluate:listPage']"
            type="primary"
            icon="el-icon-search"
            @click="handleQuery"
          >搜索</el-button>
          <el-button
            icon="el-icon-refresh"
            @click="resetQuery"
          >重置</el-button>
        </div>
      </div>
    </template>
    <template slot="header">

      <div class="button-list">
        <el-button
          v-hasPermi="['baseData:evaluate:add']"
          type="primary"
          @click="handleAdd"
        >新增</el-button>
        <el-button
          v-hasPermi="['baseData:evaluate:edit']"
          type="primary"
          @click="handleUpdate"
        >编辑</el-button>
        <el-button
          v-hasPermi="['baseData:evaluate:remove']"
          type="primary"
          @click="handleRemove"
        >删除</el-button>
        <el-button
          v-hasPermi="['baseData:import:execute']"
          type="primary"
          @click="importUpdate"
        >导入更新</el-button>
        <el-button
          v-hasPermi="['baseData:import:execute']"
          type="primary"
          @click="importAdd"
        >导入新增</el-button>

        <el-button
          v-hasPermi="['baseData:evaluate:export']"
          type="primary"
          class="mb8"
          @click="exportList"
        >数据导出</el-button>
      </div>
    </template>
    <template slot="body">
      <el-table
        v-adjust-table
        ref="table"
        v-loading="loading"
        height="99%"
        border
        :data="tableData"
        :header-cell-style="{'height': '36px'}"
        :row-style="rowStyle"
        @selection-change="handleSelectionChange"
        @row-click="handleRowClick"
      >
        <el-table-column
          fixed
          type="selection"
          width="50"
          align="center"
        />
        <el-table-column
          label="序号"
          type="index"
          width="50"
          align="center"
        >
          <template v-slot="scope">
            {{ (scope.$index + (queryParams.pageNum - 1) * queryParams.pageSize)+1 }}
          </template>
        </el-table-column>
        <el-table-column
          label="年度"
          align="center"
          prop="checkYear"
          min-width="120"
          show-overflow-tooltip
        />
        <el-table-column
          label="隧道名称"
          align="center"
          prop="tunnelName"
          min-width="120"
          show-overflow-tooltip
        />
        <el-table-column
          label="隧道编码"
          align="center"
          prop="tunnelCode"
          min-width="120"
          show-overflow-tooltip
        />
        <el-table-column
          label="管理处"
          align="center"
          prop="managementMaintenanceName"
          min-width="120"
          show-overflow-tooltip
        />
        <el-table-column
          label="管理分处"
          align="center"
          prop="managementMaintenanceBranchName"
          min-width="120"
          show-overflow-tooltip
        />
        <el-table-column
          label="养护路段"
          align="center"
          prop="maintenanceSectionName"
          min-width="120"
          show-overflow-tooltip
        />
        <el-table-column
          label="路线编码"
          align="center"
          prop="routeCode"
          min-width="120"
          show-overflow-tooltip
        />
        <el-table-column
          label="公路等级"
          align="center"
          prop="routeGrade"
          min-width="150"
          show-overflow-tooltip
        >
          <template slot-scope="scope">
            <dict-tag
              :options="dict.type.sys_route_grade"
              :value="scope.row.routeGrade"
            />
          </template>
        </el-table-column>
        <el-table-column
          label="隧道长度分类"
          align="center"
          prop="lengthClassification"
          min-width="150"
          show-overflow-tooltip
        >
          <template slot-scope="scope">
            <dict-tag
              :options="dict.type.tunnel_length_classification"
              :value="scope.row.lengthClassification"
            />
          </template>
        </el-table-column>
        <el-table-column
          label="年平均日交通量[pcu/(d.ln)]"
          align="center"
          prop="annualAverageDailyTraffic"
          min-width="200"
          show-overflow-tooltip
        />
        <el-table-column
          label="隧道养护等级"
          align="center"
          prop="maintenanceGrade"
          min-width="120"
          show-overflow-tooltip
        >
          <template slot-scope="scope">
            <dict-tag
              :options="dict.type.tunnel_maintenance_grade"
              :value="scope.row.maintenanceGrade"
            />
          </template>
        </el-table-column>
        <el-table-column
          label="备注"
          align="center"
          prop="remark"
          min-width="120"
          show-overflow-tooltip
        />
      </el-table>
      <pagination
        :total="total"
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
        :pageSizes="[10, 20, 30, 50, 100, 1000]"
        @pagination="getList"
      />
    </template>
    <ImportData
      v-if="showImport"
      :is-update="false"
      :dialog-visible="showImport"
      :import-base-type="'15'"
      :import-type="importType"
      @close="closeImport"
    />
    <add-and-edit
      v-if="showAddEdit"
      :forView="forView"
      :formData="formData"
      :title="title"
      :showAddEdit="showAddEdit"
      @close="() => {showAddEdit = false;formData ={}}"
      @refresh="() => {showAddEdit = false;getList()}"
    />
  </PageContainer>
</template>

<script>
import CascadeSelection from '@/components/CascadeSelection/index.vue'
import ImportData from '@/views/baseData/components/importData/index.vue'
import { getListPage, deleteByIds } from '@/api/baseData/tunnel/evaluate/index'
import { getFile } from '@/api/file'
import AddAndEdit from './components/addAndEdit.vue'
export default {
  name: 'Evaluate',
  props: {},
  components: { CascadeSelection, ImportData,AddAndEdit },
  dicts: [
    'base_data_yes_no',
    'tunnel_length_classification',
    'tunnel_assess_grade',
    'tunnel_maintenance_grade',
    'sys_route_grade'
  ],
  data() {
    return {
      loading: false,
      queryParams: { pageNum: 1, pageSize: 20 ,checkYear: new Date().getFullYear() + '' },
      total: 0,
      tableData: [],
      showImport: false,
      importType: 1,
      isUpdate: false,
      ids: [],
      title: '',
      showAddEdit: false,
      forView: false,
      formData: {
        baseInfoForm: {}
      },
      selectItem: []

    }
  },
  created() {
    this.getList()
  },
  methods: {
    getList() {
      this.loading = true
      getListPage(this.queryParams)
        .then(async res => {
          if (res.code === 200) {
            this.tableData = res.rows
            this.total = res.total
          }
        })
        .finally(() => {
          this.loading = false
        })
    },
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    // 新增按钮操作
    handleAdd() {
      this.forView = false
      this.showAddEdit = true
      this.title = '新增隧道评定数据'
      this.formData.baseInfoForm = {}
    },
    handleUpdate() {
      if (this.ids.length != 1) {
        this.$message.warning('请选择一条数据进行编辑！')
        return
      } else {
        this.formData.baseInfoForm = this.selectItem[0];
        this.title = '编辑隧道评定数据'
        this.showAddEdit = true
      }
    },
    resetQuery() {
      this.queryParams = {
        pageNum: 1,
        pageSize: 20,
        checkYear: new Date().getFullYear() + ''
      }
      this.getList()
    },
    handleRemove() {
      if (this.ids.length === 0) {
        this.$message.warning('请选择至少一条数据进行删除！')
        return
      }
      this.$modal
        .confirm('确认删除？')
        .then(() => {
          deleteByIds(this.ids).then(res => {
            if (res.code === 200) {
              this.getList()
              this.$modal.msgSuccess('删除成功')
            }
          })
        })
        .catch(() => {})
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.selectItem = selection
    },
    // 表格点击勾选
    handleRowClick(row) {
      row.isSelected = !row.isSelected
      this.$refs.table.toggleRowSelection(row)
    },
    // 勾选高亮
    rowStyle({ row, rowIndex }) {
      if (this.ids.includes(row.id)) {
        return { 'background-color': '#b7daff', color: '#333' }
      } else {
        return { 'background-color': '#fff', color: '#333' }
      }
    },
    closeImport(v) {
      this.showImport = false
      if (v) this.getList()
    },
    // 导入更新按钮
    importUpdate() {
      this.isUpdate = true
      this.showImport = true
      this.importType = 1
    },
    // 导入新增按钮
    importAdd() {
      this.isUpdate = false
      this.showImport = true
      this.importType = 2
    },
    // 导出清单按钮
    exportList() {
      this.download(
        '/baseData/tunnel/evaluate/export',
        { ids: this.ids, ...this.queryParams },
        ``,
        {
          headers: { 'Content-Type': 'application/json;' },
          parameterType: 'body'
        }
      )
    }
  },
  computed: {},
  watch: {}
}
</script>

<style lang="scss" scoped>
.button-list {
  border-radius: 4px;
  width: 100%;
  .el-button {
    margin-bottom: 10px;
    margin-right: 10px;
    margin-left: 0;
  }
}
</style>
