<template>
  <div class="left-cnt">
    <div class="btn">
      <el-row :gutter='10' style='width: 100%'>
        <el-col :span="8">
          <el-select
            v-model="dept"
            :loading="loading"
            :remote-method="remoteMethod"
            filterable
            placeholder="请输入管理处或路段名称"
            :popper-append-to-body="false"
            popper-class="type-popper"
            remote
            clearable
            reserve-keyword
            style="min-width: 100%"
            @change="filterData">
            <el-option
              v-for="item in options"
              :key="item.content"
              :label="item.content"
              :value="item.content">
            </el-option>
          </el-select>
        </el-col>
        <el-col v-if="!isSjy" :span="8">
          <el-select v-model="implementUnit" :popper-append-to-body="false" popper-class="type-popper" style="width: 100%" placeholder="请选择实施单位" clearable
                     @change="filterData">
            <el-option v-for='item in implementUnitList' :key='item.dictValue' :label='item.dictLabel' :value='item.dictValue'></el-option>
          </el-select>
        </el-col>
        <el-col :span="8">
          <el-select v-model="structureType" :popper-append-to-body="false" popper-class="type-popper" style="min-width: 100%"
                     @change="filterData">
            <el-option label="全部" value="全部"></el-option>
            <el-option label="边坡" value="边坡"></el-option>
            <el-option label="桥梁" value="桥梁"></el-option>
            <el-option label="隧道" value="隧道"></el-option>
          </el-select>
        </el-col>
      </el-row>
    </div>
    <div class="card-body">
      <template>
        <span class="upper-left"></span>
        <span class="upper-right"></span>
        <span class="lower-left"></span>
        <span class="lower-right"></span>
      </template>
      <div class="content">
        <div class="title-box title-box-b" :class="filterType == -1 ? 'checked' : null" @click="filterStutas(-1)">
          <span>总数</span>
          <span style="font-weight: bold">{{ statisticsData.all > 137 ? 137 : statisticsData.all }}</span>
        </div>
        <div class="title-box title-box-r" :class="filterType == -2 ? 'checked' : null" @click="filterStutas(-2)">
          <span>正在预警</span>
          <span style="font-weight: bold">{{ statisticsData.abnormal }}</span>
        </div>
        <div class="title-box title-box-o" :class="filterType == 1 ? 'checked' : null" @click="filterStutas(1)">
          <audio class="warning-audio" ref="warningAudio" src="@/assets/other/warning.mp3" preload="auto" loop></audio>
          <span>超限警告</span>
          <span style="font-weight: bold">{{ statisticsData.warning }}</span>
        </div>
        <div class="title-box title-box-y" :class="filterType == 0 ? 'checked' : null" @click="filterStutas(0)">
          <span>爆闪掉线</span>
          <span style="font-weight: bold">
            {{ statisticsData.disconnect }}
          </span>
        </div>
        <left-box v-for="(item,index) in boxData" :key="item.id + index" :box-data="item" class="left-box"
                  @click="onBoxClick"></left-box>
        <el-pagination
          :current-page.sync="pagination.pageNum"
          :page-size="pagination.pageSize"
          :pager-count="11"
          :total="total"
          background
          layout="prev, pager, next"
          style="width: 100%; text-align: center"
          @current-change="getPageData">
        </el-pagination>
      </div>
    </div>
    <div v-if="detailVisible" class="model-box" ref="dialog1" @mousedown="bringToFront('dialog1')">
      <div class="title" @mousedown="startDrag($event, 'dialog1')">{{ checkData.name }}  {{checkData.flashType}}
        <i class="el-icon-close" @click="detailVisible = false"></i>
      </div>
      <div class="content">
        <div class="top-btns">
          <div class="btn" :class="showType == '1'? 'checked' : null" @click="showType = '1'">图片</div>
          <div class="btn" v-if="checkData.isHasVideo == '1'" :class="showType == '2'? 'checked' : null"
               @click="showType = '2'">视频
          </div>
          <div class="btn" :class="showType == '3'? 'checked' : null" @click="showType = '3'">详情</div>
          <div class="btn" v-if="checkData.flashType != '默认'" :class="showType == '4'? 'checked' : null" @click="showType = '4'">设备在线状态</div>
        </div>
        <template v-if="showType == '1'">
          <el-image :preview-src-list="[checkData.imgUrl]" :src="checkData.imgUrl || ''" class="img"></el-image>
        </template>
        <template v-if="showType == '2'">
          <el-select class="select" v-model="videoUrl" :popper-append-to-body="false" popper-class="type-popper">
            <el-option v-for="item in videoList" :key="item.id" :label="item.name" :value="item.videoUrl">
            </el-option>
          </el-select>
          <Video :key="videoUrl" width="100%" height="100%" :url="videoUrl"></Video>
        </template>
        <template v-if="showType == '3'">
          <div class="textwrap" v-html="checkData.description"></div>
        </template>
        <template v-if="showType == '4'">
          <div class="tableDiv">
            <el-table
              v-adjust-table
              v-loading="loading"
              :data="dataList"
              height="100%"
              size="mini"
              style="width: 100%"
            >
              <el-table-column
                align="center"
                fixed
                label="序号"
                type="index"
                width="100"
              ></el-table-column>
              <template v-for="(column, index) in columns">
                <el-table-column
                  v-if="column.visible"
                  :key="index"
                  :label="column.label"
                  :prop="column.field"
                  align="center"
                  show-overflow-tooltip
                >
                  <template slot-scope="scope">
                    <dict-tag
                      v-if="column.dict"
                      :options="dict.type[column.dict]"
                      :value="scope.row[column.field]"
                    />
                    <template v-else-if="column.slots">
                      <RenderDom :index="index" :render="column.render" :row="scope.row"/>
                    </template>
                    <span v-else-if="column.isTime">
          								{{ parseTime(scope.row[column.field], '{y}-{m}-{d}') }}
          							</span>
                    <span v-else>{{ scope.row[column.field] }}</span>
                  </template>
                </el-table-column>
              </template>
            </el-table>
          </div>
        </template>
        <!--        <div class="steering-wheel"></div>-->
        <!--        <div class="operate">-->
        <!--          <div>-->
        <!--            <img src="@/assets/earlyWarning/qp.png">-->
        <!--            全屏-->
        <!--          </div>-->
        <!--          <div>-->
        <!--            <img src="@/assets/earlyWarning/hf.png">-->
        <!--            回放-->
        <!--          </div>-->
        <!--          <div>-->
        <!--            <img src="@/assets/earlyWarning/jt.png">-->
        <!--            截屏-->
        <!--          </div>-->
        <!--        </div>-->
        <div class="btns">
          <div class="btn" @click="openDetail" v-if="checkData.isHasSensor == '1'">查看数据</div>
          <div class="btn"  v-if="checkData.isHasSensor == '1'" @click="openWarningRecord" style="color: red;">超限警告记录</div>
          <div class="btn"  v-if="checkData.flashType != '默认'" @click="openWarningExecute">预警执行记录</div>
          <div class="btn" @click="handleEnd" v-if="checkData.flashType != '默认' && checkData.planStatus == 1" v-has-menu-permi="['jgjc:earlyWarning:close']">结束预案</div>
          <div class="btn" @click="handleBegin"
               v-has-menu-permi="['jgjc:earlyWarning:start']"
               v-if="checkData.flashType != '默认' && checkData.planStatus == 0 && checkData.isHasOffFlashDev == '0'">启动预案
          </div>
        </div>
      </div>
    </div>
    <div class="model-box" v-if="modelVisible" style='z-index: 3000;width: 60vw;height: 70vh' title="启动预案" ref="dialog2" @mousedown="bringToFront('dialog2')">
      <div class="title" @mousedown="startDrag($event, 'dialog2')">启动预案
        <i class="el-icon-close" @click="modelVisible = false"></i>
      </div>
      <div class="content" style="color: white;">
        <el-form ref="elForm" :model="formData" :rules="rules" :label-width="isBig ? '280px' : '160px'">
          <el-row>
            <el-col :span="16">
<!--              <el-form-item label="是否自定义" prop="isCustom">-->
<!--                <el-select v-model="formData.isCustom" @change="changeIsCustom" popper-class="type-popper"-->
<!--                           :popper-append-to-body="false">-->
<!--                  <el-option label="否" value="0"></el-option>-->
<!--                  <el-option label="是" value="1"></el-option>-->
<!--                </el-select>-->
<!--              </el-form-item>-->
              <el-form-item label="预案模式" prop="flashMode">
                <el-select v-model="formData.flashMode" @change="changeFlashMode" popper-class="type-popper"
                           :popper-append-to-body="false">
                  <el-option v-for="item in flashModes" :key="item.value" :label="item.label"
                             :value="item.value"></el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="爆闪">
                {{ flashData.strobe ? '开' : '关' }}
              </el-form-item>
              <el-form-item label="显示">
                {{ flashData.displayName }}
              </el-form-item>
              <el-form-item label="闪烁">
                {{ flashData.flicker ? '常规' : '常亮' }}
              </el-form-item>
              <el-form-item label="音频">
                {{ flashData.sound }}
              </el-form-item>
            </el-col>
            <el-col :span="6" :offset="2" class="tips">
              <img v-if="flashData.strobe" src="@/assets/earlyWarning/ssfwd.gif">
              <img v-else src="@/assets/earlyWarning/ssfwd.png">
              <div v-if="flashData.displayName != 'ball'" class="mode-dispaly" :style="{'color': flashData.display}"
                   :class="flashData.flicker ? 'blink' : ''">{{ flashData.displayName }}
              </div>
              <div v-else class="display-ball" :style="{'background-color': flashData.display}"
                   :class="flashData.flicker ? 'blink' : ''">
              </div>
            </el-col>
          </el-row>
          <div class='submit-btn'>
            <el-button class="model-btn" type="primary" @click="handleSubmit">发布</el-button>
          </div>
        </el-form>
      </div>
    </div>
    <div style="position:fixed; z-index: 9999; width: 90vw;height: 90vh;left: 5%;top: 5%" class="model-box"
         v-if="iframeVisible">
      <div class="title" style="z-index: 999">{{ checkData.name }}
        <i class="el-icon-close" @click="iframeVisible = false"></i>
      </div>
      <div class="content" style="padding: 0">
        <iframe :src="iframeSrc" style="width: 100%;height: 100%" frameborder="0"></iframe>
      </div>
    </div>
    <div style="position:fixed; z-index: 3000; width: 90vw;height: 90vh;left: 5%;top: 5%" class="model-box"
         v-if="recordVisible">
      <div class="title" style="z-index: 999">预警记录
        <i class="el-icon-close" @click="recordVisible = false"></i>
      </div>
      <div class="content" style="padding: 0">
        <warning-record :check-data='checkData' style="width: 100%;height: 100%" frameborder="0" @query='$emit("query")'></warning-record>
      </div>
    </div>
    <div style="position:fixed; z-index: 3000; width: 90vw;height: 90vh;left: 5%;top: 5%" class="model-box"
         v-if="executeVisible">
      <div class="title" style="z-index: 999">预警执行记录
        <i class="el-icon-close" @click="executeVisible = false"></i>
      </div>
      <div class="content" style="padding: 0">
        <warning-execute :check-data='checkData' style="width: 100%;height: 100%" frameborder="0"></warning-execute>
      </div>
    </div>
  </div>
</template>

<script>
import LeftBox from "@/views/jgjc/earlyWarning/leftBox.vue";
import {
  endPlan,
  getVideoList,
  startPlan,
  getZJGTStructDeviceStatus,
  getSansiStructDeviceStatus,
  getWdmStructDeviceStatus, getThirdPartyDeviceStatus,
} from '@/api/jgjc/earlyWarning/deviceModel'
import {getDomainTree} from "@/api/jgjc/structure/structure";
import Video from "@/views/jgjc/earlyWarning/video.vue";
import {zjgtModes, wdmModes, sansiModes} from "./defaultModes.js"

import {
  getZjgtConfigPage,
  getSsConfigPage,
  getWdmConfigPage,
  getCommonConfigPage,
} from '@/api/jgjc/flashingModeConfig'
import WarningRecord from '@/views/jgjc/earlyWarning/warningRecord.vue'
import WarningExecute from '@/views/jgjc/earlyWarning/warningExecute.vue'
import { listDictData } from '@/api/jgjc/dataDict/commDict'
import axios from 'axios'
import { getDicts } from '@/api/system/dict/data'

export default {
  components: { WarningRecord, WarningExecute, Video, LeftBox,
    RenderDom: {
      functional: true,
      props: {
        row: Object,
        index: Number,
        render: Function,
      },
      render(createElement, ctx) {
        const { row, index } = ctx.props;
        return ctx.props.render(row, index);
      },
    }},
  data() {
    return {
      dept: '',
      structureType: '全部',
      implementUnit: null,
      modelVisible: false,
      detailVisible: false,
      formData: {},
      boxData: [],
      filterList: [],
      filterType: -1,
      sourceData: [],
      total: 0,
      loading: false,
      deptOptions: [],
      options: [],
      pagination: {
        pageNum: 1,
        pageSize: 9
      },
      statisticsData: {
        all: 0,
        normal: 0,
        abnormal: 0,
        warning: 0,
        disconnect: 0
      },
      checkData: {
        flsshType: ''
      },
      showType: '1',
      videoList: [],
      videoUrl: '', //https://zhyhpt.yciccloud.com:9000/webApp/video/video.m3u8
      flashModes: zjgtModes,
      flashData: {
        strobe: false, display: '', displayName: '', flicker: false
      },
      rules: {
        flashMode: [
          {required: true, message: '请选择预案模式', trigger: 'change'}
        ],
      },
      iframeVisible: false,
      iframeSrc: '',
      // 拖动状态记录
      dragState: {
        isDragging: false,
        currentDialog: null,
        startX: 0,
        startY: 0
      },
      // 对话框z-index基础值
      zIndexBase: 2000,
      columns: [
        {key: 0, width: 100, field: 'roadId', label: '控制ID', visible: true},
        {key: 1, width: 100, field: 'name', label: '名称', visible: true},
        {key: 2, width: 100, field: 'province', label: '省份', visible: true},
        {key: 3, width: 100, field: 'city', label: '城市', visible: true},
        {key: 4, width: 100, field: 'systemStatus', label: '设备状态', visible: true}],
      dataList: [],
      recordVisible: false,
      executeVisible: false,
      implementUnitList: [],
      isSjy: this.$store.getters.roles.includes("SJYJCYJCK118"),
    }
  },
  dicts: ['voice_track', 'flash_text_color'],
  props: {
    structureList: {
      type: Array,
      default: () => []
    },
    isBig: {
      type: Boolean,
      default: false
    },
  },
  watch: {
    structureList: {
      handler(val) {
        this.sourceData = val
        this.filterList = val
        this.total = this.sourceData.length
        this.statistics()
        this.getPageData(1)
        this.deptOptions = this.converList(val)
        this.filterStutas(this.filterType)
        this.filterData()
        // 如果checkData不为空，根据id刷新checkData
        if (this.checkData.id) {
          this.checkData = val.find(item => item.id === this.checkData.id)
        }
      },
      deep: true
    },
    'statisticsData.warning': {
      handler(val) {
        this.tryPlayAudio()
      }
    }
  },
  created() {
    // this.getDeptTree()
  },
  mounted() {
    getDicts('structure_implement_unit').then(res => {
      this.implementUnitList = res.data
    })
    // 初始化所有对话框位置
    Object.keys(this.$refs).forEach(ref => {
      if (ref.startsWith('dialog')) {
        this.initDialogPosition(ref);
      }
    });
    this.tryPlayAudio()
  },
  methods: {
    tryPlayAudio() {
      const audio = this.$refs.warningAudio;
      if (!audio) return;
      audio.load();
      if(this.statisticsData.warning > 0) {
          audio.play()
      } else {
          audio.pause()
      }
    },
    // 开始拖动
    startDrag(e, dialogRef) {
      if (e.button !== 0) return;

      const dialog = this.$refs[dialogRef];
      this.dragState = {
        isDragging: true,
        currentDialog: dialog,
        startX: e.clientX - dialog.offsetLeft,
        startY: e.clientY - dialog.offsetTop
      };

      document.addEventListener('mousemove', this.handleDrag);
      document.addEventListener('mouseup', this.stopDrag);
      e.preventDefault();
    },

    // 处理拖动
    handleDrag(e) {
      if (!this.dragState.isDragging) return;

      const {currentDialog, startX, startY} = this.dragState;
      let left = e.clientX - startX;
      let top = e.clientY - startY;

      // 限制在可视区域内
      const maxLeft = window.innerWidth - currentDialog.offsetWidth;
      const maxTop = window.innerHeight - currentDialog.offsetHeight;

      currentDialog.style.left = `${Math.max(0, Math.min(left, maxLeft))}px`;
      currentDialog.style.top = `${Math.max(0, Math.min(top, maxTop))}px`;
    },

    // 停止拖动
    stopDrag() {
      this.dragState.isDragging = false;
      document.removeEventListener('mousemove', this.handleDrag);
      document.removeEventListener('mouseup', this.stopDrag);
    },

    // 点击时置顶
    bringToFront(dialogRef) {
      const dialog = this.$refs[dialogRef];
      const currentZIndex = parseInt(dialog.style.zIndex || this.zIndexBase);
      const maxZIndex = Math.max(
        ...Array.from(document.querySelectorAll('.model-box'))
          .map(el => parseInt(el.style.zIndex || 0))
      );

      if (currentZIndex <= maxZIndex) {
        dialog.style.zIndex = maxZIndex + 1;
      }
    },

    // 初始化对话框位置（居中）
    initDialogPosition(dialogRef) {
      this.$nextTick(() => {
        const dialog = this.$refs[dialogRef];
        if (dialog) {
          dialog.style.left = `${(window.innerWidth - dialog.offsetWidth) / 2}px`;
          dialog.style.top = `${(window.innerHeight - dialog.offsetHeight) / 2}px`;
        }
      });
    },
    remoteMethod(query) {
      if (query !== '') {
        this.options = this.deptOptions.filter(item => {
          return item.content.toLowerCase()
            .indexOf(query.toLowerCase()) > -1;
        });
      } else {
        this.options = [];
      }
    },
    // 查询部门下拉树结构
    getDeptTree() {
      getDomainTree({}).then(response => {
        const relaOptions = response.data.result;
        this.deptOptions = this.convertTreeToList(relaOptions)
      });
    },
    converList(data) {
      // 使用 Set 来去重
      const uniqueContents = new Set();
      data.forEach(item => {
        uniqueContents.add(item.domainName);
        uniqueContents.add(item.roadName);
        uniqueContents.add(item.name);
      });

      const output = Array.from(uniqueContents).map(content => ({content}));
      return output
    },
    convertTreeToList(data) {
      const result = [];

      function traverse(node) {
        // 提取当前节点的所需字段
        const {content, code, type, parentCode} = node;
        if (node.type === '管理处' || node.type === '路段') {
          result.push({content, code, type, parentCode});
        }
        // 递归处理子节点
        if (node.children && node.children.length > 0) {
          node.children.forEach(child => traverse(child));
        }
      }

      // 遍历初始数据
      data.forEach(node => traverse(node));
      return result;
    },
    onBoxClick(data) {
      this.showType = 1
      this.filterType = -1
      this.detailVisible = true
      this.checkData = data
      this.videoUrl = ''
      this.videoList = []
      this.dataList = []
      this.initDialogPosition('dialog1')
      // 获取视频
      getVideoList({structureId: this.checkData.id}).then(res => {
        if (res.code == 200) {
          this.videoList = res.data
          if (this.videoList.length > 0) {
            this.videoUrl = this.videoList[0].videoUrl
          }
        }
      })
      // 获取设备在线状态
      if (data.flashType == '中交国通') {
        this.columns = [
          {key: 0, width: 100, field: 'roadId', label: '控制ID', visible: true},
          {key: 1, width: 100, field: 'name', label: '名称', visible: true},
          {key: 2, width: 100, field: 'province', label: '省份', visible: true},
          {key: 3, width: 100, field: 'city', label: '城市', visible: true},
          {key: 4, width: 100, field: 'systemStatus', label: '设备状态', visible: true, slots: true, render: (row, index) => {
              if (row.systemStatus === '在线') {
                return <div style='color: #32da49'>{row.systemStatus}</div>
              } else {
                return <div style='color: #fb4545'>{row.systemStatus}</div>
              }
            }}
        ]
        getZJGTStructDeviceStatus({structId: this.checkData.id}).then(res => {
          this.dataList = res.data
          this.dataList.forEach(item => {
            item.systemStatus = item.systemStatus == '0' ? '离线' : '在线'
          })
        })
      }
      if (data.flashType == '三思') {
        this.columns = [
          {key: 0, width: 100, field: 'deviceCode', label: '设备编码', visible: true},
          {key: 1, width: 100, field: 'deviceName', label: '名称', visible: true},
          {key: 4, width: 100, field: 'status', label: '设备状态', visible: true, slots: true, render: (row, index) => {
              if (row.status === '在线') {
                return <div style='color: #32da49'>{row.status}</div>
              } else {
                return <div style='color: #fb4545'>{row.status}</div>
              }
            }}
        ]
        getSansiStructDeviceStatus({structId: this.checkData.id}).then(res => {
          this.dataList = res.data
        })
      }
      if (data.flashType == '维的美') {
        this.columns = [
          {key: 0, width: 100, field: 'deviceName', label: '名称', visible: true},
          {key: 1, width: 100, field: 'deviceCode', label: '设备编码', visible: true},
          {key: 4, width: 100, field: 'status', label: '设备状态', visible: true, slots: true, render: (row, index) => {
              if (row.status === '在线') {
                return <div style='color: #32da49'>{row.status}</div>
              } else {
                return <div style='color: #fb4545'>{row.status}</div>
              }
            }},
          {key: 3, width: 100, field: 'lastLoginTime', label: '最后一次登录时间', visible: true},
          {key: 4, width: 100, field: 'lastLogoutTime', label: '最后一次退出时间', visible: true},
          {key: 5, width: 100, field: 'lastLedOnTime', label: '最后一次开启时间', visible: true},
          {key: 6, width: 100, field: 'lastLedOffTime', label: '最后一次关闭时间', visible: true}
        ]
        getWdmStructDeviceStatus({structId: this.checkData.id}).then(res => {
          this.dataList = res.data
        })
      }
      if (data.flashType == '第三方') {
        this.columns = [
          {key: 0, width: 100, field: 'structureCode', label: '结构物编码', visible: true},
          {key: 1, width: 100, field: 'deviceName', label: '设备名称', visible: true},
          {key: 2, width: 100, field: 'deviceCode', label: '设备编码', visible: true},
          {
            key: 3,
            width: 100,
            field: 'deviceStatus',
            label: '设备状态',
            visible: true,
            slots: true,
            render: (row, index) => {
              if (row.deviceStatus === '在线') {
                return <div style='color: #32da49'>{row.deviceStatus}</div>
              } else {
                return <div style='color: #fb4545'>{row.deviceStatus}</div>
              }
            }
          },
          {key: 4, width: 100, field: 'province', label: '省份', visible: true},
          {key: 5, width: 100, field: 'city', label: '城市', visible: true},
          {key: 6, width: 150, field: 'lastLoginTime', label: '上一次设备登录时间', visible: true},
          {key: 7, width: 150, field: 'lastLogoutTime', label: '上一次设备退出时间', visible: true},
          {key: 8, width: 150, field: 'lastLedOnTime', label: '上一次设备灯光开启时间', visible: true},
          {key: 9, width: 150, field: 'lastLedOffTime', label: '上一次设备灯光关闭时间', visible: true}
        ]
        getThirdPartyDeviceStatus({structId: this.checkData.id, structureCode: this.checkData.code, pageNum: 1, pageSize: 999}).then(res => {
          this.dataList = res.rows
        })
      }
      this.$emit('box-click', data)
    },
    handleBegin() {
      this.formData = {
        isCustom: '0'
      }
      this.modelVisible = true
      this.initDialogPosition('dialog2')
      this.flashModes = []
      if (this.checkData.flashType == '中交国通') {
        getZjgtConfigPage({
          pageNum: 1,
          pageSize: 100,
        }).then(res => {
          for (let i = 0; i < res.rows.length; i++) {
            const item = res.rows[i]
            const temp = {}
            temp.zjgtConfigEntity = item
            temp.isCustom = item.isCustom
            temp.label = item.modeName
            temp.value = item.id
            temp.strobe = item.rbSw == 1
            temp.flicker = false
            if (item.textSw == 1) {
              temp.display = this.getColor(item.textColorA)
            } else {
              temp.display = this.getColor(item.textColorB)
            }
            if (item.textSw == 1) {
              temp.displayName = item.textA
            } else if (item.textSw == 2) {
              temp.displayName = item.textB
            } else if (item.textSw == 3) {
              temp.displayName = item.textA + item.textB
            }
            if (item.voiceTrack == 0) {
              temp.sound = item.voiceContent
            } else {
              temp.sound = this.dict.translate('voice_track', item.voiceTrack)
            }
            this.flashModes.push(temp)
          }
          console.log(this.flashModes)
        })

      }
      if (this.checkData.flashType == '三思') {
        // this.flashModes = sansiModes
        getSsConfigPage({
          pageNum: 1,
          pageSize: 100,
        }).then(res => {
          for (let i = 0; i < res.rows.length; i++) {
            const item = res.rows[i]
            const temp = {}
            temp.sansiConfigEntity = item
            temp.isCustom = item.isCustom
            temp.label = item.name
            temp.value = item.id
            temp.strobe = item.lampSwitch == 1
            temp.flicker = item.frequency != 0
            temp.display = item.textColor == 0 ? 'yellow' : 'red'
            temp.displayName = item.screenWord
            temp.sound = item.soundContent
            this.flashModes.push(temp)
          }
        })
      }
      if (this.checkData.flashType == '维的美') {
        getWdmConfigPage({
          pageNum: 1,
          pageSize: 100,
        }).then(res => {
          for (let i = 0; i < res.rows.length; i++) {
            const item = res.rows[i]
            const temp = {}
            temp.wdmConfigEntity = item
            temp.isCustom = item.isCustom
            temp.label = item.modeName
            temp.value = item.id
            temp.strobe = item.flashSw == 1
            temp.flicker = false
            temp.display = item.textColor == 0 ? 'red' : item.textColor == 1 ? 'green' : 'yellow'
            temp.displayName = item.textContent
            temp.sound = item.voiceContent
            this.flashModes.push(temp)
          }
        })
        // this.flashModes = wdmModes
      }
      if (this.checkData.flashType == '第三方') {
        getCommonConfigPage({
          pageNum: 1,
          pageSize: 100,
        }).then(res => {
          for (let i = 0; i < res.rows.length; i++) {
            const item = res.rows[i]
            const temp = {}
            temp.flashCommonModeConfig = item
            temp.isCustom = item.isCustom
            temp.label = item.modeName
            temp.value = item.id
            temp.strobe = item.flashSw == 1
            temp.flicker = false
            temp.display = item.textColor == '红色' ? 'red' : item.textColor == '绿色' ? 'green' : 'yellow'
            temp.displayName = item.textContent
            temp.sound = item.voiceContent
            this.flashModes.push(temp)
          }
        })
        // this.flashModes = wdmModes
      }
    },
    handleEnd() {
      this.$modal.confirm("是否结束预案?").then(() => {
        const params = {
          structId: this.checkData.id,
          flashType: this.checkData.flashType,
          structureCode: this.checkData.code
        }
        endPlan(params).then(res => {
          if (res.code == 200) {
            this.$message.success("操作成功")
            this.modelVisible = false
            this.$emit('query')
          }
        })
      })
    },
    handleSubmit() {
      this.$refs.elForm.validate(valid => {
        if (!valid) return
        this.formData.structId = this.checkData.id
        this.formData.structureCode = this.checkData.code
        this.formData.flashType = this.checkData.flashType
        this.formData.planName = '一级预警'
        this.formData.lampSwitch = ''
        this.formData.screenWord = ''
        this.formData.flicker = ''
        this.formData.soundContent = ''
        this.formData.isCustom = this.flashData.isCustom || '0'
        this.formData.zjgtConfigEntity = this.flashData.zjgtConfigEntity || {}
        this.formData.sansiConfigEntity = this.flashData.sansiConfigEntity || {}
        this.formData.wdmConfigEntity = this.flashData.wdmConfigEntity || {}
        this.formData.flashCommonModeConfig = this.flashData.flashCommonModeConfig || {}
        console.log(this.formData)
        startPlan(this.formData).then(res => {
          if (res.code == 200) {
            this.$message.success("操作成功")
            this.modelVisible = false
            this.$emit('query')
          }
        })
      })
    },
    changeFlashMode() {
      this.flashData = this.flashModes.find((item) => item.value == this.formData.flashMode)
    },
    openDetail() {
      const structureCode = this.checkData.code
      axios.get('https://jkjc.yciccloud.com:8000/xboot/displayScreen/default/getStructureNodeCode', {
        params: { structureCode }
      }).then(res => {
        if (res.status === 200) {
          const code = res.request.response
          this.iframeVisible = true
          this.iframeSrc = '/monitoringSystem/realTime?code=' + code
        }
      })
    },
    openWarningRecord() {
      this.recordVisible = true
    },
    openWarningExecute() {
      this.executeVisible = true
    },
    getPageData(val) {
      this.pagination.pageNum = val
      this.boxData = []
      this.boxData = this.filterList.slice((this.pagination.pageNum - 1) * this.pagination.pageSize, this.pagination.pageNum * this.pagination.pageSize)
    },
    filterStutas(type) {
      this.filterType = type
      if (type == -1) {
        this.filterList = this.sourceData
      } else if (type == -2) {
        this.filterList = this.sourceData.filter((item) => {
          return item.planStatus == 1
        })
      } else if (type == 0) {
        this.filterList = this.sourceData.filter((item) => {
          return item.isHasOffFlashDev != 0
        })
      } else {
        this.filterList = this.sourceData.filter((item) => {
          return item.status == type
        })
      }
      this.total = this.filterList.length
      this.getPageData(1)
      this.$emit('fileterData', this.filterList)
    },
    filterData() {
      this.sourceData = this.structureList.filter((item) => {
        return (this.structureType == '全部' ? true : item.structureType == this.structureType) && (item.domainName.includes(this.dept) || item.roadName.includes(this.dept) || item.name.includes(this.dept)) && (this.implementUnit ? item.implementUnit == this.implementUnit: true)
      })
      this.filterList = [...this.sourceData]
      this.total = this.filterList.length
      this.statistics()
      this.getPageData(1)
    },

    statistics() {
      this.statisticsData = {
        all: 0,
        normal: 0,
        abnormal: 0,
        warning: 0,
        disconnect: 0
      }
      this.sourceData.forEach((item) => {
        if (item.planStatus == 0 && item.status == 0) {
          this.statisticsData.normal++
        }
        if (item.planStatus == 1) {
          this.statisticsData.abnormal++
        }
        if (item.status == 1) {
          this.statisticsData.warning++
        }
        if (item.isHasOffFlashDev != 0) {
          this.statisticsData.disconnect++
        }
        this.statisticsData.all++
      })

    },
    getColor(value) {
      if (value == 1) {
        return 'red'
      } else if (value == 2) {
        return 'green'
      } else if (value == 3) {
        return 'none'
      } else if (value == 4) {
        return 'yellow'
      } else {
        return 'none'
      }
    },
  }
}
</script>
<style lang="scss" scoped>
@import "@/assets/styles/utils.scss";
@import "./index.scss";
.left-cnt {
  padding: vwpx(40px); // 20 * 2
  .btn {
    width: 100%;
    display: flex;
    justify-content: space-between;
    margin: vwpx(40px) 0; // 20 * 2
    padding: 0;
    flex: 1;
    ::v-deep .el-input {
      .el-input__inner {
        height: vwpx(97px);
        background-color: rgba(1, 102, 254, 0.2);
        border: 1px solid #0166fe;
        color: #ffffff;
        font-size: vwpx(32px);
      }

      .el-input__inner::placeholder {
        color: #bbbbbb;
      }

      .el-input-group__append {
        background-color: rgba(1, 102, 254, 0.2);
        border: 1px solid #0166fe;
        color: #ffffff;
        border-left: none;
        padding: 0 vwpx(20px); // 10 * 2
        cursor: pointer;
      }

      ::v-deep .el-select-dropdown__item {
        color: #ffffff !important;
        font-size: vwpx(32px);
        margin: vwpx(30px) 0;
      }

      ::v-deep .el-select-dropdown__item.selected {
        color: #42abff !important;
      }

      ::v-deep .el-input--mini .el-input__inner {
        height: vwpx(67px);
        line-height: vwpx(97px);
      }
    }


    ::v-deep .select-popper {
      background-color: rgba(1, 102, 254, 0.2);
      border: 1px solid #0166fe;
      color: #ffffff !important;
      font-size: vwpx(32px);
      margin: vwpx(20px) 0;
    }

    ::v-deep .el-pager li {
      background-color: #42abff !important; /* 设置背景色 */
      color: white !important;
    }

    ::v-deep .el-pager li.active {
      background-color: #0166fe !important; /* 设置活动按钮的背景色 */
      color: #fff !important; /* 活动按钮文字颜色 */
    }

    ::v-deep .el-select-dropdown {
      background: rgba(7, 40, 87, 0.8) !important;
      border-color: #0166FE;
    }
  }

  .card-body {
    padding: vwpx(40px); // 20 * 2
    .content {
      display: flex;
      flex-wrap: wrap;
      justify-content: space-between;
      min-height: vwpx(1320px);

      .title-box {
        width: 25%;
        height: vwpx(120px); // 60 * 2
        color: white;
        font-size: 2vmin; // 30 * 2
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 0 vwpx(60px); // 30 * 2
        background-size: 100% 100%;
        cursor: pointer;
      }

      .checked {
        transform: scale(1.1);
      }

      .left-box {
        width: 32%;
        height: vwpx(400px); // 180 * 2
        margin: vwpx(40px) 0; // 10 * 2
      }

      .title-box-b {
        background-image: url("~@/assets/earlyWarning/title-box-b.png");
      }

      .title-box-g {
        background-image: url("~@/assets/earlyWarning/title-box-r.png");
      }

      .title-box-r {
        background-image: url("~@/assets/earlyWarning/title-box-r.png");
      }
      .title-box-y {
        background-image: url("~@/assets/earlyWarning/title-box-y.svg");
      }
      .title-box-o {
        background-image: url("~@/assets/earlyWarning/title-box-o.svg");
      }
    }
  }
}

.card-body {
  width: 100%;
  background-color: rgba(4, 37, 72, 0.3);

  position: relative;
  border: 1px solid rgba(0, 166, 255, 0.5);

  .upper-left {
    position: absolute;
    top: vwpx(-2px);
    left: vwpx(-2px);
    width: vwpx(30px);
    height: vwpx(30px);
    background-image: url('~@/assets/cockpit/card-box.png');
    background-repeat: no-repeat;
    background-size: 100% 100%;
    transform: rotate(180deg);
  }

  .upper-right {
    position: absolute;
    top: vwpx(-2px);
    right: vwpx(-2px);
    width: vwpx(30px);
    height: vwpx(30px);
    background-image: url('~@/assets/cockpit/card-box.png');
    background-repeat: no-repeat;
    background-size: 100% 100%;
    transform: rotate(270deg);
  }

  .lower-left {
    position: absolute;
    bottom: vwpx(-2px);
    left: vwpx(-2px);
    width: vwpx(30px);
    height: vwpx(30px);
    background-image: url('~@/assets/cockpit/card-box.png');
    background-repeat: no-repeat;
    background-size: 100% 100%;
    transform: rotate(90deg);
  }

  .lower-right {
    position: absolute;
    bottom: vwpx(-2px);
    right: vwpx(-2px);
    width: vwpx(30px);
    height: vwpx(30px);
    background-image: url('~@/assets/cockpit/card-box.png');
    background-repeat: no-repeat;
    background-size: 100% 100%;
    transform: rotate(0deg);
  }
}

.model-box {
  width: 133.722222vmin;
  height: 84.944444vmin;
  background: #0b1c3a;
  border: 1px solid #1467ad;
  position: fixed;
  //left: 15%;
  //top: 10%;
  z-index: 9;
  cursor: default; /* 默认光标 */
  .title {
    cursor: move; /* 标题拖动光标 */
    user-select: none; /* 防止文本选中 */
    height: vwpx(70px);
    background: #003d5a;
    font-size: vwpx(40px);
    color: white;
    display: flex;
    align-items: center;
    padding-left: vwpx(40px);
    padding-right: vwpx(40px);
    justify-content: space-between;
    margin: 0px;

    i {
      cursor: pointer;
    }
  }

  .content {
    padding: 2%;
    width: 100%;
    height: 94.5%;
    position: relative;

    .img {
      width: 100%;
      height: 100%;
    }

    .textwrap {
      margin-top: vwpx(130px);
      height: 76%;
      background-color: #003d5a;
      padding: 1%;
      color: white;
      overflow-y: auto;
    }

    .select {
      position: absolute;
      right: 5%;
      top: 5%;
      width: vwpx(300px);
      z-index: 999;

      ::v-deep .select-popper {
        background-color: rgba(1, 102, 254, 0.2);
        border: 1px solid #0166fe;
        color: #ffffff !important;
        font-size: vwpx(32px);
        margin: vwpx(20px) 0;
      }

      ::v-deep .el-pager li {
        background-color: #42abff !important; /* 设置背景色 */
        color: white !important;
      }

      ::v-deep .el-pager li.active {
        background-color: #0166fe !important; /* 设置活动按钮的背景色 */
        color: #fff !important; /* 活动按钮文字颜色 */
      }

      ::v-deep .el-select-dropdown {
        background: rgba(7, 40, 87, 0.8) !important;
        border-color: #0166FE;
      }
    }

    .steering-wheel {
      position: absolute;
      left: 5%;
      bottom: 5%;
      width: vwpx(250px);
      height: vwpx(250px);
      background: url("~@/assets/earlyWarning/steering-wheel.png") no-repeat;
      background-size: 100% 100%;
    }

    .operate {
      position: absolute;
      right: 5%;
      top: 5%;
      width: 10%;
      color: white;

      div {
        display: flex;
        justify-content: space-between;
        margin-bottom: vwpx(20px);
      }

      img {
        cursor: pointer;
      }
    }

    .top-btns {
      display: flex;
      flex-wrap: nowrap;
      width: 54vmin;
      justify-content: flex-start;
      position: absolute;
      top: 2%;
      left: 2%;
      z-index: 9;

      .checked {
        background: #009a9a !important;
      }

      .btn {
        width: 11.25vmin;
        height: vwpx(68px);
        margin: 0px vwpx(20px);
        background: #005f9a;
        color: white;
        font-size: vwpx(32px);
        display: flex;
        justify-content: center;
        align-items: center;
      }
    }

    .btns {
      display: flex;
      flex-wrap: nowrap;
      width: 50vmin;
      justify-content: flex-end;
      position: absolute;
      bottom: 4%;
      right: 2%;

      .btn {
        width: 11.25vmin;
        height: vwpx(68px);
        margin: 0px vwpx(20px);
        background: #005f9a;
        color: white;
        font-size: vwpx(32px);
        display: flex;
        justify-content: center;
        align-items: center;
      }
    }
  }
}

.tips {
  position: relative;

  img {
    width: 100%;
  }

  .mode-dispaly {
    font-size: 11vmin;
    line-height: 100%;
    position: absolute;
    width: 100%;
    height: 21%;
    position: absolute;
    margin-top: -64%;
    text-align: center
  }

  .display-ball {
    width: 11vmin;
    height: 11vmin;
    border-radius: 50%;
    position: absolute;
    margin-top: -63%;
    margin-left: calc(50% - 5.5vmin);
  };

  /* 定义关键帧动画 */
  @keyframes blink {
    0%, 100% {
      opacity: 1;
    }
    50% {
      opacity: 0;
    }
  }
  /* 应用动画 */
  .blink {
    animation: blink 0.5s infinite; /* 持续时间1秒，无限次播放 */
  }
}
.submit-btn {
  text-align: right;
  position: absolute;
  bottom: 10px;
  right: 15px;
}
</style>
