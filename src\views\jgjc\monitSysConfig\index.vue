<template>
  <div class="app-container maindiv">
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          icon="el-icon-plus"
          size="mini"
          type="primary"
          @click="openDetail"
        >新增
        </el-button
        >
      </el-col>
      <right-toolbar
        :columns="columns"
        :showSearch.sync="showSearch"
        @queryTable="handleQuery"
      ></right-toolbar>
    </el-row>
    <el-row>
      <div class="draggable">
        <el-table
          ref="dataTable"
          v-loading="loading"
          :data="tableData"
          :height="
                showSearch ? 'calc(100vh - 330px)' : 'calc(100vh - 270px)'
              "
          border
          row-key="id"
          size="mini"
          stripe
          style="width: 100%"
        >
          <el-table-column
            align="center"
            label="序号"
            type="index"
            width="50"
          />
          <template v-for="(column,index) in columns">
            <el-table-column v-if="column.visible"
                             :label="column.label"
                             :prop="column.field"
                             :width="column.width"
                             align="center"
                             show-overflow-tooltip>
              <template slot-scope="scope">
                <dict-tag v-if="column.dict" :options="dict.type[column.dict]" :value="scope.row[column.field]"/>
                <template v-else-if="column.slots">
                  <RenderDom :index="index" :render="column.render" :row="scope.row"/>
                </template>
                <span v-else-if="column.isTime">{{ parseTime(scope.row[column.field], '{y}-{m}-{d}') }}</span>
                <span v-else>{{ scope.row[column.field] }}</span>
              </template>
            </el-table-column>
          </template>
          <el-table-column
            align="center"
            class-name="small-padding fixed-width"
            fixed="right"
            label="操作"
            width="220"
          >
            <template slot-scope="scope">
              <el-button
                icon="el-icon-edit"
                size="mini"
                type="text"
                @click="handleUpdate(scope.row)"
              >修改
              </el-button
              >
              <el-button
                icon="el-icon-delete"
                size="mini"
                type="text"
                @click="handleDelete(scope.row)"
              >删除
              </el-button
              >
            </template>
          </el-table-column>
        </el-table>
        <pagination
          v-show="total > 0"
          :limit.sync="queryParams.pageSize"
          :page.sync="queryParams.pageNum"
          :total="total"
          @pagination="handleQuery"
        />
      </div>
    </el-row>
    <el-dialog :append-to-body="true" :destroy-on-close="true" :visible.sync="relaFlag" title="爆闪设备"
               width="70%" @close="handleClose">
      <div v-loading="loading" class="road-interflow-edit">
        <el-row :gutter="15">
          <el-form
            ref="elForm"
            :model="formData"
            :rules="rules"
            label-width="120px"
            size="medium"
          >
            <el-col :span="24">
              <el-form-item label="参数名称" prop="paramName">
                <el-input
                  v-model="formData.paramName"
                  :style="{ width: '100%' }"
                  clearable
                  placeholder="请输入参数名称"
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="参数键名" prop="paramKey">
                <el-input
                  v-model="formData.paramKey"
                  :style="{ width: '100%' }"
                  clearable
                  placeholder="请输入参数键名"
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="参数键值" prop="paramValue">
                <el-input
                  v-model="formData.paramValue"
                  :style="{ width: '100%' }"
                  clearable
                  placeholder="请输入参数键值"
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="备注" prop="remark">
                <el-input
                  v-model="formData.remark"
                  :style="{ width: '100%' }"
                  clearable
                  placeholder="请输入备注"
                ></el-input>
              </el-form-item>
            </el-col>
          </el-form>
        </el-row>
        <div style="text-align: right; margin-top: 20px">
          <el-button size="mini" type="primary" @click="handleSave"
          >保存
          </el-button
          >
          <el-button size="mini" @click="relaFlag = false">退出</el-button>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {addConfig, delConfig, editConfig, listConfig} from "@/api/jgjc/monitSysConfig/monitSysConfig";

export default {
  name: 'MonitSysConfig',
  components: {
    RenderDom: {
      functional: true,
      props: {
        row: Object,
        index: Number,
        render: Function
      },
      render(createElement, ctx) {
        const {row, index} = ctx.props
        return ctx.props.render(row, index)
      }
    }
  },
  props: [],
  data() {
    return {
      showSearch: false,
      loading: false,
      columns: [
        {key: 0, field: 'paramName', label: '参数名称', visible: true},
        {key: 1, field: 'paramKey', label: '参数键名', visible: true},
        {key: 2, field: 'paramValue', label: '参数键值', visible: true},
        {key: 3, field: 'remark', label: '备注', visible: true},

      ],
      tableData: [],
      relaFlag: false,
      formData: {},
      total: 0,
      queryParams: {
        pageNum: 1,
        pageSize: 10
      },
      // 养护路段
      rules: {
        paramName: [{ required: true, message: '请输入参数名称', trigger: 'blur' }],
        paramKey: [{ required: true, message: '请输入参数键名', trigger: 'blur' }],
        paramValue: [{ required: true, message: '请输入参数键值', trigger: 'blur' }],
      },
    }
  },
  computed: {},
  watch: {},
  mounted() {
    this.handleQuery()
  },
  methods: {
    // 查询
    handleQuery() {
      this.loading = true
      listConfig(this.queryParams).then(res => {
        this.tableData = res.rows
        this.loading = false
        this.total = res.total
        this.$nextTick(() => {
          this.$refs.dataTable.doLayout();
        });
      })
    },
    // 修改
    handleUpdate(e) {
      this.formData = JSON.parse(JSON.stringify(e))
      this.relaFlag = true
    },
    // 删除
    handleDelete(e) {
      this.$modal.confirm('是否确认删除').then(() => {
        this.loading = true
        const ids = [e.id]
        delConfig({ids: ids }).then(res => {
          this.$modal.msgSuccess('删除成功');
          this.handleQuery()
        })
      })
    },
    // 新增
    openDetail() {
      this.formData = {}
      this.relaFlag = true
    },
    // 保存
    handleSave() {
      this.$refs["elForm"].validate((valid) => {
        if (!valid) return;
        if (this.formData.id) {
          editConfig(this.formData).then(res => {
            this.$modal.msgSuccess('保存成功')
            this.handleQuery()
          })
        } else {
          addConfig(this.formData).then(res => {
            this.$modal.msgSuccess('保存成功')
            this.handleQuery()
          })
        }
        this.relaFlag = false
      });
    },
    handleClose() {
      this.formData = {}
    },
  }
}
</script>

<style lang="scss" scoped>
::v-deep .el-table th .gutter {
  display: table-cell !important;
}
</style>
