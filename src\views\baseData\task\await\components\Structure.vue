<template>
  <div v-loading="loading" class="culvert-info-edit-structure">
    <el-form ref="ruleFormEl" :model="ruleForm" :rules="rules" label-width="95px">
      <el-row :gutter="20">
        <el-col :span="8">
          <el-form-item label="涵身长度(米)" prop="culvertLength">
            <el-input-number v-model="ruleForm.culvertLength" style="width: 100%;" controls-position="right" placeholder="请输入涵身长度" />
          </el-form-item>
          <el-form-item label="进口形式" prop="inletPortForm">
            <el-input v-model="ruleForm.inletPortForm" placeholder="请输入进口形式" />
          </el-form-item>
          <el-form-item label="涵底纵坡" prop="culvertBottomLongitudinalRamp">
            <el-input v-model="ruleForm.culvertBottomLongitudinalRamp" placeholder="请输入涵底纵坡" />
          </el-form-item>
          <el-form-item label="路面宽度(米)" prop="pavementWidth">
            <el-input-number v-model="ruleForm.pavementWidth" style="width: 100%;" controls-position="right" placeholder="请输入路面宽度" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="孔径(米)" prop="aperture">
            <el-input-number v-model="ruleForm.aperture" style="width: 100%;" controls-position="right" placeholder="请输入涵身长度" />
          </el-form-item>
          <el-form-item label="出口形式" prop="exitForm">
            <el-input v-model="ruleForm.exitForm" placeholder="请输入出口形式" />
          </el-form-item>
          <el-form-item label="涵底铺砌" prop="culvertBottomPave">
            <el-input v-model="ruleForm.culvertBottomPave" placeholder="请输入涵底铺砌" />
          </el-form-item>
          <el-form-item label="路基宽度(米)" prop="subgradeWidth">
            <el-input-number v-model="ruleForm.subgradeWidth" style="width: 100%;" controls-position="right" placeholder="请输入路基宽度" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="净高(米)" prop="culvertHeight">
            <el-input-number v-model="ruleForm.culvertHeight" style="width: 100%;" controls-position="right" placeholder="请输入净高" />
          </el-form-item>
          <el-form-item label="基础形式" prop="basicsForm">
            <el-input v-model="ruleForm.basicsForm" placeholder="请输入基础形式" />
          </el-form-item>
          <el-form-item label="填土高度(米)" prop="fillHeight">
            <el-input-number v-model="ruleForm.fillHeight" style="width: 100%;" controls-position="right" placeholder="请输入填土高度" />
          </el-form-item>
          <el-form-item label="路面类型" prop="pavementType">
            <el-input v-model="ruleForm.pavementType" placeholder="请输入路面类型" />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </div>
</template>

<script>
export default {
  name: 'CulvertInfoEditStructure',
  components: {},
  props: {
    ruleForm: {
      type: Object,
      default: () => {}
    },
    rules: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      loading: false,
      dataForm: {}
    }
  },
  computed: {},
  watch: {},
  created() {
    // 使用浅拷贝，方便收集数据
    this.dataForm = this.ruleForm
  },
  mounted() { },
  methods: {
    validateCheck() {
      let flag = false
      this.$refs['ruleFormEl'].validate((valid) => {
        flag = valid
      })
      return flag
    }
  }
}
</script>

<style lang="scss" scoped>
.culvert-info-edit-structure {}
</style>
