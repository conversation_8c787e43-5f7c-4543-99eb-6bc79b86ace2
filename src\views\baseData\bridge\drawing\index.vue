<template>
  <div class="drawing">
    <div class="drawing-card">
      <div class="drawing-card-head">
        组织结构
      </div>
      <div class="drawing-card-body">
        <el-input v-model="deptFilterText" placeholder="输入关键词检索" suffix-icon="el-icon-search" clearable
          style="margin-bottom: 10px" />
        <el-tree class="first-tree" v-loading="deptLoading" ref="deptTree" highlight-current :data="deptTreeData"
          :filter-node-method="deptFilterNode" :expand-on-click-node="false" @node-click="deptNodeClick">
          <span class="custom-tree-node" slot-scope="{ node, data }">
            <span>{{ node.label }}</span>
            <span>{{ data.count ? data.count : ''  }}</span>
          </span>
        </el-tree>
      </div>
    </div>
    <div class="drawing-card" style="margin-left: 10px">
      <div class="drawing-card-head">桥梁</div>
      <div class="drawing-card-body">
        <el-input v-model="queryParams.bridgeName" style="width:100%;" placeholder="桥梁名称" suffix-icon="el-icon-search"
          clearable />
        <div v-if="lastClickedNodeId" class="drawing-card-body-selectBox">
          <span style="margin-right: 10px">是否有图纸</span>
          <el-radio-group v-model="queryParams.isExistFileDraw" @change="getTreeData">
            <el-radio-button :label="undefined">全部</el-radio-button>
            <el-radio-button :label="1">是</el-radio-button>
            <el-radio-button :label="2">否</el-radio-button>
          </el-radio-group>
        </div>
        <div v-if="lastClickedNodeId" class="drawing-card-body-selectBox">
          <span style="margin-right: 10px">方向</span>
          <el-radio-group v-model="queryParams.direction" @change="getTreeData">
            <el-radio-button :label="undefined">全部</el-radio-button>
            <el-radio-button :label="1">上行</el-radio-button>
            <el-radio-button :label="2">下行</el-radio-button>
          </el-radio-group>
        </div>
        <div v-loading="treeLoading" v-if="treeData && treeData.length > 0" class="tree-content">
          <div class="label">{{ '共' + total }}</div>
          <div class="main">
            <el-tree ref="tree" highlight-current :data="treeData" :filter-node-method="treeFilterNode"
              :expand-on-click-node="false" @node-click="treeNodeClick" />
          </div>
          <pagination :pager-count="3" layout="prev, pager, next" :total="total" :page.sync="queryParams.pageNum"
            :limit.sync="queryParams.pageSize" :pageSizes="[10, 20, 30, 50, 100, 1000]" @pagination="getTreeData" />
        </div>
        <el-empty v-loading="treeLoading" v-else :image="emptyImage" :image-size="160"></el-empty>
      </div>
    </div>
    <div class="drawing-right drawing-card">
      <div class="drawing-card-head">
        {{ title }}
      </div>
      <div v-if="treeIds.length > 0">
        <cardBox style="min-height: 200px;max-height: calc(100vh - 160px);" typeCode="BS129"
          v-for="(id, index) in treeIds" :bizId="id" :key="id" :assetType="8" />
      </div>
      <el-empty v-else :image="emptyImage" :image-size="320"></el-empty>
    </div>
  </div>
</template>

<script>
import { listStatic } from '@/api/baseData/bridge/baseInfo/index'
import { getTreeStruct } from '@/api/tmpl.js'
import { getMaintenanceSectionListAll } from '@/api/baseData/common/routeLine'
import { getModuleFileCount } from '@/api/system/moduleFile.js'
import cardBox from '@/components/Drawing/cardBox.vue'

export default {
  name: 'Drawing',
  components: { cardBox },
  provide() {
    return {
      oneMap: false
    };
  },
  data() {
    return {
      deptLoading: false,
      deptTreeData: [],
      deptFilterText: '',
      lastClickedNodeId: '',
      treeLoading: false,
      treeData: [],
      treeIds: [],
      queryParams: {
        bridgeName: '',
        pageNum: 1,
        pageSize: 20
      },
      title: '桥梁图纸文件',
      emptyImage: require('@/assets/drawing/empty.png'),
      total: 0
    }
  },
  watch: {
    deptFilterText(val) {
      this.$refs.deptTree.filter(val)
    },
    'queryParams.bridgeName'(val) {
      this.$refs.tree.filter(val)
    }
  },
  created() {
    this.getDeptTree()
  },
  methods: {
    async getDeptTree() {
      this.deptLoading = true
      try {
        // 获取部门数据
        const deptRes = await getTreeStruct({ types: 101 })
        let deptArr = JSON.parse(JSON.stringify(deptRes.data))
        // 获取养护路段数据
        const maintenanceRes = await getMaintenanceSectionListAll()
        // 获取图纸文件数量
        const fileCountRes = await getModuleFileCount({typeCode: 'BS129'})
        let newList = maintenanceRes.data.flatMap(item1 => {
          const matchedItem = fileCountRes.data.find(item2 => item2.maintenanceSectionId === item1.maintenanceSectionId)
          return [{
            count: matchedItem ? matchedItem.count : 0,
            maintenanceSectionName: item1.maintenanceSectionName,
            maintenanceSectionId: item1.maintenanceSectionId,
            departmentId: item1.departmentId
          }]
        })
        deptArr.forEach(item => {
          item.count = newList
          .filter(resItem => item.id === resItem.departmentId)
          .reduce((acc, resItem) => acc + resItem.count, 0)
          item.children = newList
            .filter(resItem => item.id === resItem.departmentId)
            .map(resItem => ({
              parentId: item.id,
              id: resItem.maintenanceSectionId,
              label: resItem.maintenanceSectionName,
              count: resItem.count,
            }))
        })
        this.deptTreeData = deptArr
      } catch (error) {
      } finally {
        this.deptLoading = false
      }
    },
    deptFilterNode(value, data) {
      if (!value) return true
      return data.label.indexOf(value) !== -1
    },
    deptNodeClick(e) {
      if (e.id === this.lastClickedNodeId) {
        return // 如果点击了同一个节点，则直接返回，不执行下面的逻辑
      }
      if (e.label.includes('管理处')) {
        this.queryParams.managementMaintenanceIds = [e.id]
        this.queryParams.maintenanceSectionIds = undefined
      } else {
        this.queryParams.maintenanceSectionIds = [e.id]
        this.queryParams.managementMaintenanceIds = undefined
      }
      this.lastClickedNodeId = e.id
      this.getTreeData()
    },
    getTreeData() {
      this.treeLoading = true
      this.treeIds = []
      this.title = '桥梁图纸文件'
      listStatic(this.queryParams)
        .then(res => {
          if (res.code === 200) {
            res.rows.forEach(el => {
              el.label = el.bridgeName
            })
            this.treeData = res.rows
            this.total = res.total
          }
        })
        .finally(() => {
          this.treeLoading = false
        })
    },
    treeFilterNode(value, data) {
      if (!value) return true
      return data.label.indexOf(value) !== -1
    },
    treeNodeClick(e) {
      this.title = e.bridgeName
      this.treeIds = [e.assetId]
    }
  }
}
</script>

<style lang="scss" scoped>
.drawing {
  width: 100%;
  height: 100%;
  padding: 10px;
  display: flex;

  .drawing-card {
    min-width: 272px;
    height: 100%;
    background-color: white;
    position: relative;
    border-radius: 10px;
    box-shadow: 0px 0px 10px 0px rgba(0, 0, 0, 0.2);

    .drawing-card-head {
      font-family: Microsoft YaHei-Regular;
      width: calc(100% - 30px);
      height: 51px;
      line-height: 51px;
      border-radius: 10px 10px 0 0;
      font-size: 16px;
      font-weight: 400;
      color: #1D2129;
      border-bottom: 1px solid #E5E6EB;
      margin: 0 15px;
      background-color: white;
    }

    .drawing-card-body {
      position: absolute;
      bottom: 0;
      left: 0;
      width: 100%;
      height: calc(100% - 51px);
      padding: 15px 15px;
      display: flex;
      flex-direction: column;
      overflow-y: auto;

      .custom-tree-node {
        flex: 1;
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding-right: 8px;
        font-size: 14px;
      }

      .drawing-card-body-selectBox {
        margin-top: 10px;
        display: flex;
        align-items: center;
        justify-content: end;
        height: 40px;
        width: 100%;

        span {
          text-align: right;
          color: #333;
          font-size: 14px;
        }
      }

      .tree-content {
        width: 100%;
        height: 100%;
        overflow: hidden;

        .label {
          font-size: 14px;
          color: #86909C;
          text-align: right;
          height: 24px;
          line-height: 24px;
        }

        .main {
          border-radius: 6px 6px 6px 6px;
          border: 1px solid #C0D2E2;
          height: calc(100% - 65px) !important;
          overflow: auto;
        }
      }
    }
  }

  .drawing-right {
    width: 100% !important;
    margin-left: 10px;
  }
}

::v-deep .el-tree-node__content {
  margin: 2px 0;

  &:hover {
    background-color: #b7daff;
  }
}

::v-deep .el-tree--highlight-current .el-tree-node.is-current>.el-tree-node__content {
  background-color: #b7daff;
}

::v-deep .el-radio__label {
  padding-left: 5px !important;
}

::v-deep {
  .el-radio-button__inner {
    padding: 7px 15px;
    font-size: 12px;
    width: 54px;
    height: 28px;
  }

  .el-radio-button__orig-radio:checked+.el-radio-button__inner {
    background: #E0EFFF;
    color: #1691FF;
  }
}

.first-tree {
  ::v-deep {
    .el-tree-node__content {
      height: 32px;
      margin: 2px 0;
      background-color: #F7F8FA;
      border-radius: 5px;
    }
  }
}
</style>
