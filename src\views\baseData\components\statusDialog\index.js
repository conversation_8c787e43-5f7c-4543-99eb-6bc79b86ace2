import Vue from "vue";
import Dialog from "./index.vue";

export function statusDialog({
  dataId = "", // 数据id
  baseDataType = null, // 基础数据类型 1桥梁 2隧道 3涵洞 4互通
  title = "运营状态变更",
  msg = "数据Id不能为空",
} = {}) {
  return new Promise((resolve) => {
    const instance = new Vue({
      el: document.createElement("div"),
      data: {
        visible: true,
        dataId,
      },
      methods: {
        close() {
          this.visible = false;
          document.body.removeChild(this.$el);
        },
        cancel() {
          resolve();
          this.close();
        },
        ok() {
          const { dataId } = this;
          if (msg) {
            if (!dataId) {
              this.$message.error(msg);
              return;
            }
          }
          resolve({ dataId });
          this.close();
        },
      },
      render(h) {
        const { visible, dataId } = this;
        return h(Dialog, {
          props: {
            title,
            visible,
            dataId,
            baseDataType,
          },
          on: {
            "update:dataId": (v) => (this.dataId = v),
            cancel: this.cancel,
            ok: this.ok,
          },
        });
      },
    });

    document.body.appendChild(instance.$el);
  });
}
