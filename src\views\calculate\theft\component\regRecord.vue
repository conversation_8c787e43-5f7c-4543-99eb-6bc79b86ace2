<template>
  <div class="record-main">
    <el-descriptions :column="1" border>
      <el-descriptions-item
        :label="item.label"
        v-for="(item, index) in myTableData"
        :key="'myTable' + index"
        >{{ taskData[item.key] || "" }}</el-descriptions-item
      >
    </el-descriptions>

    <div class="right-steps">
      <el-steps v-if="stepsData && stepsData.length > 0" direction="vertical" :active="stepsData.length">
        <el-step v-for="(item, index) in stepsData" :key="'steps' + index">
          <template #icon>{{ stepsData.length - index }}</template>
          <template #title>
            <div :style="{color: item.direction ? 'none' : 'red'}">{{ item.nodeName }}
              <i v-if="item.direction" class="el-icon-circle-check"/>
              <i v-else class="el-icon-circle-close"/>
              &nbsp&nbsp&nbsp{{ item.endTime }}
<!--              <i v-hasPermi="['comm:node:opinion:edit']" class="el-icon-edit-outline" @click="openEditDialog(item)" style="cursor: pointer;"></i>-->
<!--              <i v-hasPermi="['comm:node:edit']" v-if="item.nodeName == '完工登记' || item.nodeName == '编制签证'"  class="el-icon-document" @click="openDrawer(item)" style="cursor: pointer;"></i>-->
            </div>
          </template>
          <template #description>
            <el-descriptions :column="1" :colon="false">
              <el-descriptions-item v-if="item.isProduction || item.isProduction  == 0" label="是否计算安全生产费">{{ item.isProduction == 0 ? '否' : '是'}}</el-descriptions-item>
              <el-descriptions-item v-if="item.isGuarantee || item.isGuarantee  == 0" label="是否计算安全保通费">{{ item.isGuarantee == 0 ? '否' : '是'}}</el-descriptions-item>
              <el-descriptions-item label="操作人">{{ item.assigneeName }}</el-descriptions-item>
              <el-descriptions-item label="备注">{{ item.comment }}</el-descriptions-item>
              <template v-if="item.annex && item.annex.length > 0">
                <el-descriptions-item label="附件">
                </el-descriptions-item>
                <el-descriptions-item>
                  <file-upload v-for="file in item.annex" v-model="file.fileId" :forView="true"></file-upload>
                </el-descriptions-item>
              </template>
              <template v-if="item.djList && item.djList.length > 0">
                <el-descriptions-item label="审核照片">
                </el-descriptions-item>
                <el-descriptions-item>
                  <file-upload v-for="file in item.djList" v-model="file.fileId" :forView="true"></file-upload>
                </el-descriptions-item>
              </template>
              <template v-if="item.sgqList && item.sgqList.length > 0">
                <el-descriptions-item label="施工前照片">
                </el-descriptions-item>
                <el-descriptions-item>
                  <file-upload v-for="file in item.sgqList" v-model="file.fileId" :forView="true"></file-upload>
                </el-descriptions-item>
              </template>
              <template v-if="item.sghList && item.sghList.length > 0">
                <el-descriptions-item label="施工后照片">
                </el-descriptions-item>
                <el-descriptions-item>
                  <file-upload v-for="file in item.sghList" v-model="file.fileId" :forView="true"></file-upload>
                </el-descriptions-item>
              </template>
              <template v-if="item.djzp && item.djzp.length > 0">
                <el-descriptions-item label="登记照片">
                </el-descriptions-item>
                <el-descriptions-item>
                  <file-upload v-for="file in item.djzp" v-model="file.fileId" :forView="true"></file-upload>
                </el-descriptions-item>
              </template>
              <el-descriptions-item v-if="item.methodList && item.methodList.length > 0" label="方法清单">
              </el-descriptions-item>
              <el-descriptions-item v-if="item.methodList && item.methodList.length > 0">
                <el-table v-adjust-table :data="item.methodList" border size="mini">
                  <el-table-column prop="schemeCode" label="子目号" align="center"/>
                  <el-table-column prop="schemeName" label="方法名" align="center"/>
                  <el-table-column prop="calcDesc" label="计算式" align="center"/>
                  <el-table-column prop="num" label="数量" align="center"/>
                  <el-table-column prop="remark" label="备注" align="center"/>
                </el-table>
              </el-descriptions-item>
            </el-descriptions>
          </template>
        </el-step>
      </el-steps>
      <el-empty style="width: 500px" v-else></el-empty>
    </div>

    <el-dialog
      append-to-body
      :visible.sync="editDialog"
      width="650px"
    >
      <el-form :model="editForm">
        <el-form-item label="操作人" label-width="100px">
          <el-cascader
              v-model="editForm.assignee"
              :options="deptUserOptions"
              :props="assigneeProps"
              :show-all-levels="false"
              ref="assigneeRef"
              filterable
              clearable
              @change="assigneeChange"
              style="width: 460px"
            >
          </el-cascader>
        </el-form-item>
        <el-form-item label="操作意见" label-width="100px">
          <el-input v-model="editForm.content" style="width: 460px;"/>
        </el-form-item>
        <el-form-item label="操作时间" label-width="100px">
          <el-date-picker
            v-model="editForm.endTime"
            type="datetime"
            format="yyyy-MM-dd HH:mm:ss"
            value-format="yyyy-MM-dd HH:mm:ss"
            placeholder="结束时间">
          </el-date-picker>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="editDialog = false">取 消</el-button>
        <el-button type="primary" @click="saveEdit">确 定</el-button>
      </span>
    </el-dialog>
    <el-drawer
        append-to-body
        modal-append-to-body
        :wrapperClosable="false"
        :title="drawerTitle"
        destroy-on-close
        :visible.sync="drawer"
        size="70%"
      >
        <!-- 验收登记详情 -->
        <RegDetail v-if="detailType==='reg'" @close="handleCloseDetail" :row-data="row" :from-event="true"></RegDetail>
        <!-- 完工登记详情 -->
        <ComDetail v-if="detailType==='com'" @close="handleCloseDetail" :row-data="rowData" :detail-data="row" :from-event="true"></ComDetail>
      </el-drawer>
  </div>
</template>
<script>
import { EventBus } from '@/utils/eventBus';
import {getRecordById, updateAuthorizedNode} from "@/api/theft/construction";
import { getTreeStruct,editNodeInfo } from "@/api/tmpl";
import ComDetail from "@/views/theft/construction/completionReg/detail.vue";
import RegDetail from "@/views/theft/construction/inspectionReg/detail.vue";

export default {
  components: {ComDetail, RegDetail},
  data() {
    return {
      myTableData: [
        {
          label: "项目名称",
          key: "projectName",
        },
        {
          label: "项目编码",
          key: "projectCode",
        },
        {
          label: "任务单名称",
          key: "constructionName",
        },
        {
          label: "任务单编码",
          key: "constructionCode",
        },
        {
          label: "签证单编码",
          key: "code",
        },
        {
          label: "签证单金额",
          key: "sumFund",
        },
        {
          label: "管养单位",
          key: "domainName",
        },
        {
          label: "路段名称",
          key: "maiSecName",
        },
        {
          label: "施工单位",
          key: "conDomainName",
        },
        {
          label: "施工合同",
          key: "conConName",
        },
        {
          label: "监理单位",
          key: "supDomainName",
        },
        {
          label: "监理合同",
          key: "supConName",
        },
        {
          label: "验收人员",
          key: "visaName",
        },
        {
          label: "是否计算安全生产费",
          key: "isProduction",
        },
        {
          label: "是否计算安全保通费",
          key: "isGuarantee",
        },
      ],
      stepsData: [],
      taskData: {},
      editDialog: false,
      editForm: {
        id: '',
        assignee: '',
        content: '',
        startTime: '',
        endTime: '',
        taskId: ''
      },
      deptUserOptions: [],
      assigneeProps: {
        multiple: false, //是否多选
        value: 'id',
        emitPath: false,
      },
      curUser: null,
      isAuthorizedNode: false,
      drawer: false,
      detailType: '',
      detailData: {},
      drawerTitle: '',
    };
  },
  props: {
    id: {
      type: String,
      default: '',
    }
  },
  created() {
    this.getDeptTreeDef()
  },
  beforeDestroy() {
    EventBus.$off('task-data');
  },
  mounted() {
    this.queryList()
  },
  methods: {
    queryList() {
      if (this.id) {
        getRecordById(this.id).then((res) => {
          this.stepsData = res.data.nodeInfoVOList || [];
          this.stepsData.forEach(item => {
            item.annex = item.fileList?.filter(item => item.isAnnex == 1) || []
            item.sgqList = item.fileList?.filter(item => item.registerType == 1) || []
            item.sghList = item.fileList?.filter(item => item.registerType == 2) || []
            item.djList = item.fileList?.filter(item => item.registerType == 5) || []
            item.djzp = item.fileList?.filter(item => item.registerType == 0) || []
          })
          this.taskData = res.data
          if (this.taskData?.isProduction == 1) this.taskData.isProduction = '计算'
          else this.taskData.isProduction = '不计算'

          if (this.taskData?.isGuarantee == 1) this.taskData.isGuarantee = '计算'
          else this.taskData.isGuarantee = '不计算'
        });
      }
    },
    openEditDialog(row) {
      this.editForm.id = row.id
      this.editForm.taskId = row.taskId
      this.editForm.assignee = row.userId
      this.editForm.content = row.comment
      this.editForm.startTime = row.startTime
      this.editForm.endTime = row.endTime
      this.curUser = {
        label: row.assignee,
        value: row.userId
      }
      if (row.nodeName == '编制签证') {
        this.isAuthorizedNode = true
      } else {
        this.isAuthorizedNode = false
      }
      this.editDialog = true
    },
    assigneeChange(value) {
       this.$nextTick(() => {
        this.curUser = this.$refs.assigneeRef.getCheckedNodes()[0]
      })
    },
    saveEdit() {
      const submitData = {
        ...this.editForm
      }
      submitData.assignee = `${this.curUser.label}@${this.curUser.value}`
      submitData.startTime = submitData.endTime
      if (this.isAuthorizedNode) {
        updateAuthorizedNode(submitData).then(()=> {
          this.queryList()
          this.editDialog = false
        })
      } else {
        editNodeInfo(submitData).then(()=> {
          this.queryList()
          this.editDialog = false
        })
      }
    },
    getDeptTreeDef() {
      getTreeStruct({ types: 111, dataRule: false }).then((response) => {
        this.deptUserOptions = response.data;
      });
    },
    handleCloseDetail() {
      this.drawer = false
      this.queryList()
    },
    openDrawer(item) {
      if(item.nodeName == '验收登记') {
        this.detailType = 'reg'
        this.drawerTitle = '验收登记'
      }
      if(item.nodeName == '完工登记') {
        this.detailType = 'com'
        this.drawerTitle = '完工登记'
      }
      if(item.nodeName == '编制签证') {
        this.detailType = 'com'
        this.drawerTitle = '编制签证'
      }
      this.$nextTick(()=> {
        this.drawer = true
      })
    }
  },
};
</script>
<style lang="scss" scoped>
.record-main {
  display: flex;
  .el-descriptions {
    width: 300px;
    margin-right: 20px;
  }
}
::v-deep .el-descriptions-item__container {
  width: 38vw;
  overflow-x: auto;
}
</style>
<style lang="scss" scoped>
@import "@/assets/styles/business.scss";
</style>
