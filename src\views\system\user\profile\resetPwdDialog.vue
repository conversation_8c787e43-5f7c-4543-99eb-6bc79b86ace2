<template>
  <el-dialog title="修改密码"
             :show-close="false"
             :visible.sync="dialogVisible"
             width="30%">
    <div style="margin-bottom: 20px">
      <el-alert
        title="默认密码不可用，请修改密码后继续"
        type="error"
        :closable="false"
        show-icon>
      </el-alert>
    </div>
    <el-form ref="form" :model="user" :rules="rules" label-width="80px">
      <el-form-item label="旧密码" prop="oldPassword">
        <el-input v-model="user.oldPassword" placeholder="请输入旧密码" type="password" show-password/>
      </el-form-item>
      <el-form-item label="新密码" prop="newPassword">
        <el-input v-model="user.newPassword" placeholder="请输入新密码" type="password" show-password/>
      </el-form-item>
      <el-form-item label="确认密码" prop="confirmPassword">
        <el-input v-model="user.confirmPassword" placeholder="请确认新密码" type="password" show-password/>
      </el-form-item>
    </el-form>

    <template slot="footer">
<!--      <el-button @click="dialogVisible = false">取 消</el-button>-->
      <el-button type="primary" size="mini" @click="submit">保存</el-button>
    </template>
  </el-dialog>
</template>

<script>
import {updateUserPwd} from "@/api/system/user";
import {isDefaultPassword} from "@/api/login";

export default {
  data() {
    const equalToPassword = (rule, value, callback) => {
      if (this.user.newPassword !== value) {
        callback(new Error("两次输入的密码不一致"));
      } else {
        callback();
      }
    };
    return {
      dialogVisible: false,
      user: {
        oldPassword: undefined,
        newPassword: undefined,
        confirmPassword: undefined
      },
      clearTimeout: [],
      // 表单校验
      rules: {
        oldPassword: [
          {required: true, message: "旧密码不能为空", trigger: "blur"}
        ],
        newPassword: [
          {required: true, message: "新密码不能为空", trigger: "blur"},
          {min: 6, max: 20, message: "长度在 6 到 20 个字符", trigger: "blur"},
          {
            // 必须同时包含大小写字母和特殊符号，且不包含非法字符
            pattern: /^(?=.*[A-Z])(?=.*[a-z])(?=.*[!@#$%^&*(),.?":{}|<>])(?!.*[<>"'|\\])[A-Za-z\d!@#$%^&*(),.?":{}|<>]+$/,
            message: "必须同时包含大小写字母和特殊符号（如!@#$%），且不能包含非法字符：< > \" ' \\\ |",
            trigger: "blur"
          }
        ],
        confirmPassword: [
          {required: true, message: "确认密码不能为空", trigger: "blur"},
          {required: true, validator: equalToPassword, trigger: "blur"}
        ]
      }
    };
  },
  watch: {
    // 监听token变化
    '$store.state.user.token': {
      handler(newToken) {
        let isJumpLogin =  this.$store.state.user.isJumpLogin;
        console.log("newToken: ",newToken)
        console.log("isJumpLogin: ",isJumpLogin)
        if (newToken && !isJumpLogin) {
          this.clearTimeout.push(
            setInterval(() => {
              isDefaultPassword().then(res => {
                if (res.data === true) {
                  this.dialogVisible = true
                }else {
                  this.clearTimeout.forEach(item =>{
                    clearInterval(item)
                  })
                }
              })
            }, 10000)
          )
        }
      },
      immediate: true
    }
  },
  methods: {
    submit() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          updateUserPwd(this.user.oldPassword, this.user.newPassword).then(response => {
            this.$modal.msgSuccess("修改成功");
            this.dialogVisible = false
          });
        }
      });
    },
  },
  // created() {
  //   setInterval(() => {
  //     isDefaultPassword().then(res => {
  //       if (res.data === true) {
  //         this.dialogVisible = true
  //       }
  //     })
  //   }, 5000)
  // }
};
</script>
<style scoped>
.v-modal {
  opacity: 0.8
}
</style>
