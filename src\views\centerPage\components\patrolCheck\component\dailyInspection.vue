<template>
  <div class="daily-inspection" v-loading="loading" element-loading-background="rgba(0, 0, 0, 0.4)">
    <section class="inspection-number">
      <h4>当日巡查病害上报个数：</h4>
      <span class="number" v-for="(item, index) in diseaseNum" :key="'num' + index">{{ item }}</span>
      <span class="unit">个</span>
    </section>
    <div class="divider"></div>
    <section class="inspection-list">
      <div class="list-item" :class="index < daily.length - 1 ? 'list-item-border' : ''" v-for="(item, index) in daily"
        :key="index">
        <span class="item-name">{{ item.name }}</span>
        <span class="item-number">{{ item.number }}</span>
      </div>
    </section>
  </div>
</template>

<script>
// api
import { getDailyPatrol } from '@/api/cockpit/inspection';

export default {
  name: "dailyInspection",
  data() {
    return {
      diseaseNum: [],
      daily: [
        {
          name: '巡查里程',
          number: 0
        },
        {
          name: '巡查次数',
          number: 0
        },
        // {
        //   name: '巡查异常情况',
        //   number: 3
        // },
        // {
        //   name: '有效巡查率',
        //   number: '0%'
        // }
      ],
      loading: false,
    }
  },
  created() {
    this.getData();
  },
  methods: {
    getData() {
      this.loading = true;
      getDailyPatrol().then(res => {
        if (res.code === 200 && res.data) {
          let diseaseNum = res.data.diseaseNum + '';
          this.diseaseNum = diseaseNum ? diseaseNum.split('') : [0];
          this.daily[0].number = res.data.patrolMileage ? (res.data.patrolMileage / 1000).toFixed(2) : 0;
          this.daily[1].number = res.data.patrolNum || 0;
          // this.daily[2].number = res.data.effPatrolRate || '0%';
        }
      }).finally(() => {
        this.loading = false;
      })
    }
  }
}
</script>

<style lang="scss" scoped>
@import "@/assets/styles/utils.scss";

.daily-inspection {
  width: 100%;
  height: 100%;
  padding: vwpx(30px);
  overflow-y: auto;

  .inspection-number {
    width: 100%;
    display: flex;
    align-items: center;
    color: #ffffff;

    h4 {
      font-family: Microsoft YaHei, Microsoft YaHei;
      font-weight: 700;
      font-size: vwpx(32px);
    }

    .number {
      width: vwpx(80px);
      height: vwpx(96px);
      background: rgba(4, 17, 48, 0.4);
      box-shadow: inset 0px 0px 6px 0px #0154FB;
      border: vwpx(2px) solid #009DFF;

      display: flex;
      justify-content: center;
      align-items: center;
      margin: 0 vwpx(6px);

      font-family: Microsoft YaHei UI, Microsoft YaHei UI;
      font-weight: 700;
      font-size: vwpx(48px);
      color: #FFBA00;
      text-shadow: 0px 0px 10px rgba(27, 126, 242, 0.8);
    }

    .unit {
      font-family: Microsoft YaHei UI, Microsoft YaHei UI;
      font-weight: 400;
      font-size: vwpx(32px);
      color: #FFFFFF;
      margin-left: vwpx(10px);
    }
  }

  .divider {
    width: 100%;
    border-bottom: 1px dotted rgba(156, 189, 255, 0.5);
    margin: vwpx(40px) 0;
  }

  .inspection-list {
    width: 100%;
    height: vwpx(240px);
    background: rgba(0, 0, 0, 0.1);
    box-shadow: inset 0px 0px 10px 0px #0065FF;
    border-radius: 6px;
    border: 1px solid #20A9FF;

    display: flex;
    align-items: center;

    .list-item {
      flex: 1;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;

      .item-name {
        font-family: Source Han Sans, Source Han Sans;
        font-weight: 500;
        font-size: vwpx(28px);
        color: #FFFFFF;
      }

      .item-number {
        font-family: HarmonyOS Sans SC, HarmonyOS Sans SC;
        font-weight: 500;
        font-size: vwpx(48px);
        color: #F2AF4A;
        margin-top: vwpx(12px);
      }
    }

    .list-item-border {
      border-right: 1px dotted rgba(156, 189, 255, 0.5);
    }
  }
}
</style>