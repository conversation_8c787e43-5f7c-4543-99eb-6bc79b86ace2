<template>
  <div class="road-interflow-edit">
    <el-row :gutter="15">
      <el-form
        ref="elForm"
        :inline="true"
        :model="formData"
        :rules="rules"
        label-width="120px"
      >
        <el-col :span="24">
          <el-form-item label="需调差的结算单" prop="name">
            <el-input
              v-model="formData.settleName"
              @focus="settleOpenFlag = true"
              readonly
              style="width: 280px"
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="费用类型" prop="dataType">
            <cost-select :type="6"
              v-model="formData.dataType"
              style="width: 280px"
            />
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="调差子目" prop="methodId">
            <el-select
              v-model="formData.methodId"
              placeholder="调差子目"
              clearable
              style="width: 280px"
              @change="handleCheckMethod"
            >
              <el-option
                v-for="item in schemaList"
                :key="item.id"
                :label="item.label"
                :value="item.id"
              >
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="调差项目" prop="projId">
            <el-select
              v-model="formData.projId"
              clearable
              @change="projectChange"
              style="width: 280px"
            >
              <el-option
                v-for="item in materialList"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="调差月份" prop="month">
            <el-select v-model="formData.month" @change="monthChange">
              <el-option
                v-for="item in adjustDetailList"
                :value="item.month"
                :label="item.month"
                :key="item.id"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="单价" prop="price">
            <el-input v-model="formData.price" style="width: 280px" />
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="单位" prop="unit">
            <el-input v-model="formData.unit" style="width: 280px" />
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="计算式" prop="calcDesc">
            <el-input v-model="formData.calcDesc" @change="changeCalculation" style="width: 280px" />
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="数量" prop="num">
            <el-input v-model="formData.num" style="width: 280px" />
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="金额" prop="amount">
            <el-input v-model="formData.amount" style="width: 280px" readonly/>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="备注" prop="remark">
            <el-input
              v-model="formData.remark"
              type="textarea"
              :rows="3"
              style="width: 280px"
            />
          </el-form-item>
        </el-col>
        <el-col
          :span="24"
          style="text-align: right; padding-right: 7.5px; margin-top: 18px"
        >
          <el-button type="primary" @click="onSave">保 存</el-button>
          <el-button @click="onClose">退 出</el-button>
        </el-col>
      </el-form>
    </el-row>
    <el-dialog :visible.sync="settleOpenFlag" v-if="settleOpenFlag" title="项目选择" width="80%" append-to-body modal-append-to-body >
      <settle-select @checkSettle="checkSettle" />
    </el-dialog>
  </div>
</template>

<script>
import {
  addMaterialAdjust,
  editMaterialAdjust,
  listAllScheme,
} from "@/api/regularTesting/metering/settlementApplication";
import {
  queryMaterialAdjustListAll,
  getMaterialAdjustById,
} from "@/api/dailyMaintenance/metering/materialAdjustment";
import {multiply, round} from "lodash"
import CostSelect from "@/components/CostSelect/index.vue";
import SettleSelect from "../settlementApplication/settleSelect.vue";
export default {
  components: {SettleSelect, CostSelect},
  data() {
    return {
      formData: {
        name: '',
        dataType: '',
        methodId: '',
        projId: '',
        month: '',
        price: '',
        unit: '',
        calcDesc: '',
        num: '',
        amount: '',
        remark: ''
      },
      rules: {
        name: [
          { required: true, message: "请输入需调差的结算单", trigger: "blur" },
        ],
        dataType: [
          { required: true, message: "请选择费用类型", trigger: "blur" },
        ],
        methodId: [
          { required: true, message: "请选择调差子目", trigger: "blur" },
        ],
        projId: [
          { required: true, message: "请输入调差项目", trigger: "blur" },
        ],
        month: [{ required: true, message: "请选择调差月份", trigger: "blur" }],
        price: [
          { required: true, message: "请输入单价", trigger: "blur" },
          {
            pattern: /^\d+(\.\d{1,2})?$/,
            message: "单价格式不正确",
            trigger: "blur",
          },
        ],
        unit: [{ required: true, message: "请输入单位", trigger: "blur" }],
        num: [
          { required: true, message: "请输入数量", trigger: "blur" },
          { pattern: /^\d+$/, message: "数量必须为整数", trigger: "blur" },
        ],
        amount: [
          { required: true, message: "请输入金额", trigger: "blur" },
          {
            pattern: /^\d+(\.\d{1,2})?$/,
            message: "金额格式不正确",
            trigger: "blur",
          },
        ],
      },
      urgentDegreeOptions: [
        {
          label: "是",
          value: 1,
        },
        {
          label: "否",
          value: 0,
        },
      ],
      schemaList: [],
      materialList: [],
      adjustDetailList: [],
      settleOpenFlag: false,
    };
  },
  props: {
    prntData: {
      type: Object,
      default: () => {
        return {};
      },
    },
    rowData: {
      type: Object,
      default: () => {
        return {};
      },
    },
  },
  watch: {
    rowData: {
      handler(val) {
        this.getSchema();
        if (val.id) {
          this.formData = JSON.parse(JSON.stringify(val));
          getMaterialAdjustById(val.projId).then((res) => {
            this.adjustDetailList = res?.data?.adjustDetailList || [];
          });
        }
        this.formData.name = this.prntData.name;
      },
      immediate: true,
      deep: true,
    },
    prntData: {
      handler(val) {
        if (val.id) {
          this.getMaterialList();
        }
      },
      immediate: true,
      deep: true,
    },
    formPrice: {
      handler(val) {
        this.formData.amount = Number(val) || 0
      },
      immediate: true,
      deep: true,
    }
  },
  computed: {
    formPrice() {
      return round(multiply(Number(this.formData.num) || 0, Number(this.formData.price) || 0),2)
    }
  },
  methods: {
    onSave() {
      this.$refs.elForm.validate((valid) => {
        if (!valid) return;
        this.formData.settleId = this.prntData.id;
        if (this.formData.id) {
          editMaterialAdjust(this.formData).then(() => {
            this.$modal.msgSuccess("保存成功");
            this.onClose();
          });
        } else {
          addMaterialAdjust(this.formData).then(() => {
            this.$modal.msgSuccess("保存成功");
            this.onClose();
          });
        }
      });
    },
    getSchema() {
      listAllScheme({ settleCalcId: this.formData.adSettleId }).then((res) => {
        this.schemaList = res.data;
        this.schemaList.forEach((item) => {
          item.label = `${item.schemeCode}${item.schemeName}`;
        });
      });
    },
    handleCheckMethod(e) {
      const schema = this.schemaList.find((item) => item.id === e);
      if (schema) {
        this.formData.num = schema.num;
      } else {
        this.formData.num = "";
      }
    },
    projectChange(val) {
      this.adjustDetailList = [];
      this.formData.price = "";
      this.formData.month = "";
      this.formData.unit = "";
      if (val) {
        const curProject = this.materialList.find((item) => item.id === val);
        this.formData.unit = curProject.unit;
        getMaterialAdjustById(val).then((res) => {
          this.adjustDetailList = res?.data?.adjustDetailList || [];
        });
      }
    },
    monthChange(month) {
      const curAdjustDetail = this.adjustDetailList.find(
        (item) => item.month === month
      );
      this.formData.price = curAdjustDetail?.changeValue|| 0;
    },
    changeCalculation() {
      if (!this.isValidMathFormula(this.formData.calcDesc)) {
        this.$modal.msgError('计算式错误，请检查')
        return
      }
      let num =  this.math.evaluate(this.formData.calcDesc) || 0
      this.formData.num = num
    },
    getMaterialList() {
      let year = this.prntData.years

      queryMaterialAdjustListAll({ year }).then((res) => {
        this.materialList = res.data;
      });
    },
    checkSettle(e) {
      this.formData.adSettleId = e.settleId
      this.$set(this.formData, 'settleName', e.settleName)
      this.settleOpenFlag = false
      this.getSchema()
    },
    onClose() {
      this.$emit("close", "6");
    },
  },
};
</script>

<style lang="scss" scoped></style>
<style lang="scss" scoped>
@import "@/assets/styles/business.scss";
</style>
