<template>
  <div>
    <el-dialog
      title="运营状态变更"
      :visible.sync="dialogVisible"
      :before-close="handelClose"
      width="550px"
      append-to-body
    >
      <el-form
        ref="form"
        :model="form"
        label-width="100px"
      >
        <el-form-item
          label="运营状态"
          prop="businessState"
          :rules="[{ required: true, message: '请选择运营状态', trigger: 'blur' }]"
        >
          <DictSelect
            v-model="form.businessState"
            :type="'bridge_business_state'"
            clearable
          ></DictSelect>
        </el-form-item>
        <el-form-item
          label="原因"
          prop="reason"
          :rules="[{ required: true, message: '请输入原因', trigger: 'blur' }]"
        >
          <el-input
            v-model="form.reason"
            placeholder="请输入"
          ></el-input>
        </el-form-item>
        <el-form-item
          label="备注"
          prop="remark"
        >
          <el-input
            type="textarea"
            v-model="form.remark"
            placeholder="请输入"
          ></el-input>
        </el-form-item>
        <el-form-item label="文件" prop="files">
          <FileUpload v-model="form.files"
                      platform="mpkj"/>
        </el-form-item>
      </el-form>
      <div slot="footer">
        <el-button @click="handelClose">取 消</el-button>
        <el-button
          type="primary"
          @click="handelCheck"
        >确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: '',
  props: {
    dialogVisible: {
      default: false
    },
    id: {
      default: ''
    }
  },
  components: {},
  data() {
    return {
      form: {}
    }
  },
  created() {
  },
  methods: {
    handelCheck() {
      this.$refs['form'].validate(valid => {
        if (valid) {
          console.log('校验通过！')
        } else {
          console.log('error submit!!')
          return false
        }
      })
    },
    handelClose() {
      this.$emit('close')
    }
  },
  computed: {},
  watch: {}
}
</script>

<style scoped>
</style>
