<template>
  <Cards :title="title" w="100%" h="28vh" :isDtl="false" hBg="l" style="margin-top: 25px;">
    <!-- <Echarts v-if="option" :option="option" :height="'100%'" key="option" /> -->
    <div v-if="isVibChart" class="chart" id="vibrationAccelerationChart"></div>
    <div v-if="isBreakChart" class="chart" id="breakChart"></div>
    <div v-if="isEmpty" class="empty">没有数据</div>
    <template slot="more">
      <el-cascader v-model="defaultValue" :options="sensorOptions" @change="handleChangeSensor" :props="props"
        v-if="isVibChart"></el-cascader>
    </template>
  </Cards>
</template>

<script>
import * as echarts from "echarts";
import { isBigScreen } from '../../../utils/utils.js';
import Cards from "../../components/cards.vue"
import { fetchGet } from "../../../utils/api.js";
const getSensorGroupBySensorIdByCode = (code) => {
  return fetchGet(
    'https://jkjc.yciccloud.com:8000/xboot/sensorManage/getSensorGroupBySensorIdByCode',
    { nodeCode: code }
  )
}
const getCalibratedRealTimeDataNoToken = (p) => {
  return fetchGet(
    'https://jkjc.yciccloud.com:8000/xboot/displayScreen/default/getCalibratedRealTimeData',
    p
  )
}


export default {
  name: 'Bottom',
  props: {
    // title: {
    //   type: String,
    //   default: '主梁振动加速器'
    // }
  },
  inject: ['iThis'],
  components: { Cards },
  data() {
    return {
      props: {
        value: "code",
        label: "content",
        multiple: false,
      },
      title: '主梁振动加速器',
      isBig: isBigScreen(),
      option: null,
      options: [
        {
          value: "1",
          label: "主梁竖向振动加速器",
        }
      ],
      type: null,
      allSensorList: [],
      isBreakChart: false,
      isVibChart: false,
      sensorOptions: [],
      defaultValue: [],
      chartTime: [],
      chartData: [],
      backupTime: [],
      backupData: [],
      sensorList: {},
      myInterval: null,
      isEmpty: false
    }
  },
  created() {
    this.title = this.iThis.params.type == '桥梁' ? '主梁振动加速器' : '裂缝变化值';
    this.init()
  },
  methods: {
    init() {
      const url = 'https://jkjc.yciccloud.com:8000/xboot/projectStructureManage/recursionNodeUnderStructure'
      const obj = { structureNodeCode: this.iThis.params.code }
      fetchGet(url, obj).then(res => {
        if (res.code == 200) {
          this.allSensorList = res.result;
          this.allSensorList.forEach((item) => {
            if (item.content == "振动") {
              this.isBreakChart = false;
              this.title = "主梁振动加速度";
              this.sensorOptions = item.children;
              // 初始化图像
              this.defaultValue = [
                this.sensorOptions[0].code,
                this.sensorOptions[0].children[0].code,
              ];
              this.handleChangeSensor(this.defaultValue);
              this.getTreeData(this.sensorOptions);
              this.isVibChart = true;
            }
          });
          // 判断有没有振动传
          if (this.sensorOptions.length == 0) {
            this.allSensorList.forEach((item) => {
              if (item.content == "裂缝") {
                this.isVibChart = false;
                this.title = "裂缝变化值";
                getSensorGroupBySensorIdByCode(item.children[0].children[0].code).then((res) => {
                  if (res.code == 200) {
                    this.sensorList = res.result[0];
                    this.getChartData(this.sensorList.code, this.sensorList.sensorId)
                    this.isBreakChart = true;
                  } else {
                    console.log(
                      "getSensorGroupBySensorIdByCode返回数据失败",
                      res
                    );
                  }
                });
              }
            });
            if (!this.allSensorList.find(item => item.content == "裂缝")) {
              let result = this.iThis.sensorList;
              let arr = result.filter(item => item.specificMonitorTypeName.includes('裂缝'))
              if (arr?.length > 0) {
                this.title = "裂缝变化值";
                this.getChartData(arr[0].code, arr[0].sensorId)
                this.isBreakChart = true;
              } else {
                this.isVibChart = false;
                this.isBreakChart = false;
                this.isEmpty = true
              }

            }
          }
        } else {
          console.log("getRecursionNodeUnderStructure返回数据失败");
        }
      })
    },
    async handleChangeSensor(code) {
      clearInterval(this.myInterval);
      this.myInterval = null;
      this.chartData = [];
      this.chartTime = [];
      this.backupTime = [];
      this.backupData = [];
      this.isVibChart = false;
      await getSensorGroupBySensorIdByCode(code[1]).then((res) => {
        if (res.code == 200) {
          this.sensorList = res.result[0];
          this.getChartData(this.sensorList.code, this.sensorList.sensorId)
          this.isVibChart = true;
        } else {
          console.log("getSensorGroupBySensorIdByCode返回数据失败", res);
        }
      });
    },
    getTreeData(list) {
      list.forEach((item) => {
        if (item.nodeLevel == 8) {
          delete item.children;
        } else {
          this.getTreeData(item.children);
        }
      });
    },
    // 获取数据
    async getChartData(nodeCode, sensorId) {
      let params = {
        nodeCode: nodeCode,
        sensorId: sensorId,
        structureNodeCode: this.iThis.params.code,
      };
      await getCalibratedRealTimeDataNoToken(params).then((res) => {
        if (res.code == 200) {
          if (res.result[0].times.length == 0 || res.result[0].values.length == 0) {
            let domId = this.isVibChart ? 'vibrationAccelerationChart' : 'breakChart';
            var chartDom = document.getElementById(domId);
            var myChart = echarts.init(chartDom);
            var option = {
              title: {
                text: '没有数据',
                left: 'center',
                top: 'center',
                textStyle: {
                  fontSize: this.isBig ? 32 : 14,
                  color: '#606266',
                  fontWeight: 700
                },
              },
            }
            myChart.setOption(option);
          } else {
            if (this.isVibChart) {
              // 先用前200条数据对图标进行初始化
              this.chartTime = res.result[0].times.slice(0, 200);
              this.chartData = res.result[0].values.slice(0, 200);
              // 再把后300条存起来 通过绘图方法里的定时器 不断往横纵轴里push数据
              this.backupTime = res.result[0].times.slice(199, -1);
              this.backupData = res.result[0].values.slice(199, -1);
              this.initChart();
            } else {
              this.initChart2(res.result)
            }
          }
          this.loading = false;
        } else {
          console.log("getCalibratedHistoryData返回数据失败");
        }
      });
    },
    // 绘制图像
    initChart() {
      var chartDom = document.getElementById("vibrationAccelerationChart");
      var myChart = echarts.init(chartDom);
      var option;
      //获取时间
      const time = this.chartTime;

      // 数据
      const dataOne = this.chartData;
      option = {
        tooltip: {
          trigger: "axis",
        },
        grid: {
          left: "1%",
          right: "3%",
          top: "10%",
          bottom: "5%",
          containLabel: true,
        },
        xAxis: {
          type: "category",
          axisLabel: {
            // 添加或修改 axisLabel 配置项
            textStyle: {
              // 设置文本样式
              color: "#ffffff",
              fontSize: this.isBig ? 24 : 10,
            },
          },
          axisLine: {
            show: true,
            lineStyle: {
              color: 'rgba(110, 112, 121, 0.70)'
            }
          },
          axisTick: {
            alignWithLabel: true, // 刻度线与标签对齐
            inside: true // 刻度线朝上
          },
          data: time,
        },
        yAxis: {
          type: "value",
          axisLabel: {
            // 添加或修改 axisLabel 配置项
            textStyle: {
              // 设置文本样式
              color: "rgba(153, 153, 153, 1)",
              fontSize: this.isBig ? 24 : 10,
            },
          },
          splitArea: {
            show: false,
          },
          splitLine: {
            lineStyle: {
              color: 'rgba(110, 112, 121, 0.70)' // 修改分割线颜色
            }
          },
        },
        series: [
          {
            data: dataOne,
            type: "line",
            lineStyle: {
              color: "#00BFFF",
            },
          },
        ],
      };

      var that = this;
      let index = 0;
      // 300条数据 100ms插入一条 30s插入完
      function insertData() {
        if (index < that.backupData.length) {
          // console.log("index", index);
          setTimeout(function () {
            time.shift();
            time.push(that.backupTime[index]);
            dataOne.shift();
            dataOne.push(that.backupData[index]);
            index++;
            myChart.setOption({
              xAxis: [
                {
                  data: time,
                },
              ],
              series: [
                {
                  data: dataOne,
                },
              ],
            });
            insertData();
          }, 100);
        }
      }

      // 执行方法
      insertData();

      // 36s后 从接口重新拿数据
      setTimeout(async () => {
        // 获取最新实时数据
        await getCalibratedRealTimeDataNoToken({
          nodeCode: that.sensorList.code,
          sensorId: that.sensorList.sensorId,
          structureNodeCode: that.iThis.params.code,
        }).then((res) => {
          if (res.code == 200) {
            // console.log("获取实时数据1", res);
            that.backupTime = res.result[0].times;
            that.backupData = res.result[0].values;
            // 重新循环插入数据
            index = 0;
            insertData();
          } else {
            console.log("getCalibratedHistoryData返回数据失败");
          }
        });
        // 前面先执行一遍 再开启 54s 一个的定时器循环获取数据
        that.myInterval = setInterval(async function () {
          // 获取最新实时数据
          await getCalibratedRealTimeDataNoToken({
            nodeCode: that.sensorList.code,
            sensorId: that.sensorList.sensorId,
            structureNodeCode: that.iThis.params.code,
          }).then((res) => {
            if (res.code == 200) {
              // console.log("获取实时数据2", res);
              that.backupTime = res.result[0].times;
              that.backupData = res.result[0].values;
              // 重新循环插入数据
              index = 0;
              insertData();
            } else {
              console.log("getCalibratedHistoryData返回数据失败");
            }
          });
        }, 54000);
      }, 36000);

      option && myChart.setOption(option);
      window.addEventListener("resize", function () {
        myChart.resize();
      });
    },
    initChart2(data) {
      var chartDom = document.getElementById("breakChart");
      var myChart = echarts.init(chartDom);
      var option;

      let myYData = [];
      let mylegend = [];
      let myXdata = [];
      data.forEach((item) => {
        myYData = item.values;
        mylegend.push(item.name);
        myXdata = item.times;
      });
      option = {
        tooltip: {
          trigger: "axis",
        },
        legend: {
          data: mylegend,
          top: "6%",
          textStyle: {
            color: "#ffffff",
            fontSize: this.isBig ? 24 : 12,
          },
        },
        grid: {
          left: "2%",
          right: "4%",
          bottom: "5%",
          top: "18%",
          containLabel: true,
        },
        xAxis: {
          type: "category",
          boundaryGap: false,
          data: myXdata,
          axisLabel: {
            // 添加或修改 axisLabel 配置项
            textStyle: {
              // 设置文本样式
              color: "#ffffff",
              fontSize: this.isBig ? 24 : 10,
            },
          },
          axisLine: {
            show: true,
            lineStyle: {
              color: 'rgba(110, 112, 121, 0.70)'
            },
          },
          axisTick: {
            alignWithLabel: true, // 刻度线与标签对齐
            inside: true // 刻度线朝上
          },
        },
        yAxis: {
          type: "value",
          axisLabel: {
            // 添加或修改 axisLabel 配置项
            textStyle: {
              // 设置文本样式
              color: "rgba(153, 153, 153, 1)",
              fontSize: this.isBig ? 24 : 10,
            },
          },
          splitArea: {
            show: false,
          },
          splitLine: {
            lineStyle: {
              color: 'rgba(110, 112, 121, 0.70)' // 修改分割线颜色
            }
          },
        },
        series: [
          {
            name: data[0].name,
            type: "line",
            data: myYData,
          },
        ],
      };
      option && myChart.setOption(option, true);
    },
  },
  computed: {},
  watch: {},
  beforeDestroy() {
    clearInterval(this.myInterval);
  },
}
</script>

<style lang="scss" scoped>
@import "@/assets/styles/utils.scss";

.chart {
  width: 100%;
  height: 100%;
}

.empty {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: vwpx(32px);
  color: #606266;
  font-weight: 700;
}


::v-deep {
  .el-input__inner {
    width: vwpx(400px) !important;
    height: vwpx(60px) !important;
    font-size: vwpx(24px);
    color: rgb(255, 255, 255);
    padding: 0 vwpx(60px) 0 vwpx(30px);
    background-color: rgba(4, 17, 48, 0.4);
    border: 1px solid rgba(6, 135, 255, 1);
  }

  .el-input__inner::placeholder {
    color: #bbbbbb;
  }

  .el-input-group__append {
    background-color: rgba(1, 102, 254, 0.2);
    border: 1px solid #0166fe;
    color: #ffffff;
    border-left: none;
    padding: 0 10px;
    cursor: pointer;
  }
}
</style>