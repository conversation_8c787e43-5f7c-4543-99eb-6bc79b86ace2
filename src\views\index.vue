<template>
  <div class="app-container home">
    <!--    <deptTree  :deptType="4"  @node-click="handleNodeClick" />-->
    <deptTree :deptType="101" />
    <deptTreeCheckbox :deptType="101" />

    <!--    <selectTree :deptType='101'/>-->
    <!--    <selectTreeCheckbox :deptType='101'/>-->
  </div>
</template>

<script>
import deptTree from "@/components/DeptTmpl/deptTree.vue";
import deptTreeCheckbox from "@/components/DeptTmpl/deptTreeCheckbox.vue";
import selectTree from "@/components/DeptTmpl/selectTree.vue";
import selectTreeCheckbox from "@/components/DeptTmpl/selectTreeCheckbox.vue";
import card from "@/components/ModuleFile/card.vue";
import index from "@/components/ModuleFile/index.vue";
import cardBox from "@/components/ModuleFile/cardBox.vue";
import moduleFile from "@/components/ModuleFile/moduleFile.vue";
import moduleFileTest from "@/components/ModuleFile/moduleFileTest.vue";
export default {
  name: "Index",
  components: {
    deptTree,
    deptTreeCheckbox,
    selectTree,
    selectTreeCheckbox,
    card,
    cardBox,
    moduleFile,
    moduleFileTest,
    index,
  },
  data() {
    return {
      // 版本号
      version: "3.6.4",
    };
  },
  methods: {},
};
</script>



