<template>
  <div class="route-index ">
    <el-row :gutter="10" class="mt-1">
      <el-col :xs="6" :sm="6" :md="6" :lg="6" :xl="6">
        <CockpitCard title="路网统计" :isDtl="false" w="100%" h="calc(20vh)">
          <RoadNetwork />
        </CockpitCard>
        <CockpitCard title="路线性质统计" :isDtl="false" w="100%" h="calc(20vh)" class="mt-1">
          <NatureOfRoute />
        </CockpitCard>
        <CockpitCard title="路线技术等级统计" :isDtl="false" w="100%" h="calc(20vh)" class="mt-1">
          <LevelStatistics />
        </CockpitCard>
        <CockpitCard title="通车里程趋势" :isDtl="false" w="100%" h="calc(20vh)" class="mt-1">
          <MileageTrend />
        </CockpitCard>
      </el-col>
      <el-col :xs="6" :sm="6" :md="6" :lg="6" :xl="6">
        <CockpitCard title="优等路率" :isDtl="false" w="100%" h="calc(24vh)">
          <ExcellentRoadRate />
        </CockpitCard>
        <CockpitCard title="路线统计" :isDtl="false" w="100%" h="calc(65vh)" class="mt-1">
          <RouteStatistics />
        </CockpitCard>
      </el-col>
      <el-col :xs="12" :sm="12" :md="12" :lg="12" :xl="12">
        <el-row :gutter="10">
          <el-col :xs="12" :sm="12" :md="12" :lg="12" :xl="12">
            <CockpitCard title="路面养护经费投入情况" :isDtl="false" w="100%" h="calc(41vh)">
              <RoadMaintenanceExpenditure />
            </CockpitCard>
          </el-col>
          <el-col :xs="12" :sm="12" :md="12" :lg="12" :xl="12">
            <CockpitCard title="路面养护工程实施情况" :isDtl="false" w="100%" h="calc(41vh)">
              <RoadMaintenanceImplementation />
              <template slot="more">
                <img src="@/assets/cockpit/back.png" @click="handleBack" style="width:118px;height:54px" />
              </template>
            </CockpitCard>
          </el-col>
          <el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24">
            <CockpitCard title="管理处路段统计" :isDtl="false" w="100%" h="calc(48vh - 2px)" class="mt-1">
              <ManagementOfficeStatistics />
            </CockpitCard>
          </el-col>
        </el-row>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import CockpitCard from '../cockpitCard.vue';
import RoadNetwork from './component/roadNetwork.vue';
import NatureOfRoute from './component/natureOfRoute.vue';
import LevelStatistics from './component/levelStatistics.vue';
import MileageTrend from './component/mileageTrend.vue';
import ExcellentRoadRate from './component/excellentRoadRate.vue';
import RouteStatistics from './component/routeStatistics.vue';
import RoadMaintenanceExpenditure from './component/roadMaintenanceExpenditure.vue';
import RoadMaintenanceImplementation from './component/roadMaintenanceImplementation.vue';
import ManagementOfficeStatistics from './component/managementOfficeStatistics.vue';

export default {
  name: 'RouteIndex',
  components: {
    CockpitCard,
    RoadNetwork,
    NatureOfRoute,
    LevelStatistics,
    MileageTrend,
    ExcellentRoadRate,
    RouteStatistics,
    RoadMaintenanceExpenditure,
    RoadMaintenanceImplementation,
    ManagementOfficeStatistics,
  },
  data() {
    return {}
  },
  methods: {
    handleBack() {
      this.$emit('back')
    },
  }
}
</script>

<style lang="scss" scoped>
.route-index {
  width: 100%;

  .mt-1 {
    margin-top: 0.6rem;
  }
}
</style>