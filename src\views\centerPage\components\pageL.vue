<template>
  <div class="page-l">
    <section class="info-l">
      <template v-if="!cockpit.componentR">
        <CockpitCard unit="公里" class="mb-2" :h="isBig ? '19.8vh' : '18.5vh'" @click="onclick(1)" :isDtl="false">
          <route-index></route-index>
        </CockpitCard>
        <CockpitCard title="桥梁" unit="座" class="mb-2" :h="(ifShow && isBig) ? '19vh' : isBig ? '19.9vh' : '18.5vh'"
          @click="onclick(2)">
          <bridge-index v-if="data.bridgeStatistics" :statisticsData="data.bridgeStatistics" v-loading="loading"
            style="height: 100%;"></bridge-index>
        </CockpitCard>
        <CockpitCard title="隧道" unit="座" class="mb-2" :h="(ifShow && isBig) ? '19vh' : isBig ? '19.9vh' : '18.5vh'"
          @click="onclick(3)">
          <tunnel-index v-if="data.tunnelStatistics" :statisticsData="data.tunnelStatistics"
            v-loading="loading"></tunnel-index>
        </CockpitCard>
        <CockpitCard title="健康监测" :h="(ifShow && isBig) ? '15vh' : isBig ? '19.8vh' : '18.5vh'" @click="onclick(4)">
          <fitness-index type="2" top="0vh" />
        </CockpitCard>
      </template>
    </section>
    <section class="info-c">
      <!-- <AMap /> -->
      <!-- <div class="map-case" :style="{ left: isBig ? '66%' : '70%' }">
        <div v-for="(item, index) in caseList" :key="'case' + index" class="case-list">
          <div class="case-row" :style="{ background: item.color }"></div>
          <span class="case-text">{{ item.name }}</span>
        </div>
      </div> -->
    </section>
    <section class="info-r">
      <template>
        <CockpitCard title="日常养护" class="mb-2" :h="(ifShow && isBig) ? '21vh' : isBig ? '21.5vh' : '19vh'" @click="onclick(5)">
          <daily />
        </CockpitCard>
        <CockpitCard title="养护工程" class="mb-2" :h="(ifShow && isBig) ? '35vh' : isBig ? '40.5vh' : '41vh'" @click="onclick(6)">
          <div style="width: 100%;">
            <specially :component="cockpit.componentL" v-if="!cockpit.componentR"/>
          </div>
        </CockpitCard>
        <CockpitCard title="年度巡查检查情况" :h="(ifShow && isBig) ? '21.7vh' : isBig ? '22vh' : '19vh'" @click="onclick(7)">
          <inspection-index />
        </CockpitCard>
      </template>
    </section>
  </div>
</template>

<script>
import { isBigScreen, showH } from '../util/utils';
// 组件
import CockpitCard from './cockpitCard.vue';
import RouteIndex from './route/index.vue';
import BridgeIndex from './bridge/index.vue';
import TunnelIndex from './tunnel/index.vue';
import FitnessIndex from './fitness/index.vue';
import Daily from './maintenance/daily.vue';
import Specially from './maintenance/specially.vue';
import InspectionIndex from './inspection/index.vue';
import MapView from './mapView.vue'
import AMap from '@/components/Map/aMap.vue'

// ol
import { Map, View } from 'ol';
import { defaults } from 'ol/control';
import { fromLonLat } from 'ol/proj';
import TileLayer from 'ol/layer/Tile';
import { XYZ } from 'ol/source';

// api
import { getShapeList } from "@/api/oneMap/deptInfo";
import { getMenuSub } from "@/api/oneMap/menuSub";
import { getStatistics } from "@/api/cockpit/index";

import { addMapMask, addVectorTile, addWidthFeature, removeLayer } from '@/views/map/components/common/mapFun';
import cache from '@/plugins/cache';


var key = "cde0b56cf882626889981701109a7536";
const lineId = "1824259894327382017";

export default {
  name: 'PageL',
  inject: ['cockpit'],
  components: {
    CockpitCard,
    RouteIndex,
    BridgeIndex,
    TunnelIndex,
    FitnessIndex,
    Daily,
    Specially,
    InspectionIndex,
    MapView,
    AMap,
  },
  data() {
    return {
      isBig: isBigScreen(),
      ifShow: showH,
      zoom: 7,
      caseList: [
        {
          name: '畅通',
          color: '#00BF2D'
        },
        {
          name: '缓慢',
          color: '#FFE400'
        },
        {
          name: '拥堵',
          color: '#D90000'
        },
        {
          name: '严重拥堵',
          color: '#780000'
        }
      ],
      data: {},
      isJump: false,
      loading: false,
    }
  },
  created() {
    if (this.$route?.query?.jumpType && this.$route?.query?.jumpType == '1') {
      this.isJump = true
    }
  },
  mounted() {
    this.getStatisticsData()
  },
  methods: {
    getStatisticsData() {
      this.loading = true;
      getStatistics()
        .then(res => {
          if (res) {
            this.data = res;
            let obj = {
              1: 259, // 特大桥
              2: 8030, // 大桥
              3: 4550, // 中桥
              4: 1271, // 小桥
            };
            // return;
            // 临时修改 - 开始 2025-1-4
            // 桥梁 - bridgeStatistics
            let spanClassifyTypes = this.data.bridgeStatistics?.spanClassifyTypes.map(v => {
              if (v.typeValue == 3 || v.typeLabel == '中桥') {
                v.typeCount = 4550;
              }
              // v.typeCount = obj[v.typeValue]
              return v;
            })
            this.data.bridgeStatistics.spanClassifyTypes = spanClassifyTypes;
            // -- 主桥结构形式
            let mainSuperstructureTypes = this.data.bridgeStatistics?.mainSuperstructureTypes.map(v => {
              if (v.typeValue == 13 || v.typeLabel == 'T梁') {
                v.typeCount = 4849;
              }
              return v;
            });
            this.data.bridgeStatistics.mainSuperstructureTypes = mainSuperstructureTypes;
            // -- 技术状况等级
            let techAssessTypes = this.data.bridgeStatistics?.techAssessTypes.map(v => {
              if (v.typeValue == 2 || v.typeLabel == '2类') {
                v.typeCount = 7654;
              }
              if (v.typeValue == 3 || v.typeLabel == '3类') {
                v.typeCount = 28;
              }
              return v;
            });
            this.data.bridgeStatistics.techAssessTypes = techAssessTypes;
            // 隧道 - tunnelStatistics
            let lengthClassifications = this.data.tunnelStatistics?.lengthClassifications.map(v => {
              if (v.typeValue == 3 || v.typeLabel == '中隧道') {
                v.typeCount = 335;
              }
              return v;
            })
            this.data.bridgeStatistics.lengthClassifications = lengthClassifications;
            // 临时修改 - 结束 2025-1-4
          }
        })
        .finally(() => {
          if (this.isJump) {
            this.onclick(4)
          }
          this.loading = false;
        })
    },
    // 点击事件 index 1-7 分别是 1、路网，2、桥梁，3、隧道，4、健康监测，5、日常养护，6、专项养护，7、巡检查
    onclick(index) {
      let obj = {};
      switch (index) {
        case 1:
          obj = {
            title: "路网养护专题",
            subText: "",
            componentL: '',
            componentR: 'RouteDetail',
            singlePage: this.isBig ? false : true,
          }
          break;
        case 2:
          obj = {
            id: '1821371445660356609',
            title: "桥梁养护专题",
            subText: "龙江特大桥(上行)",
            componentL: 'BridgeSpecial',
            componentR: 'BridgeDetail',
            data: this.data.bridgeStatistics,
            singlePage: false,
          }
          break;
        case 3:
          obj = {
            id: '',
            title: "隧道养护专题",
            subText: "白塔村隧道左幅",
            componentL: 'TunnelSpecial',
            componentR: 'TunnelDetail',
            data: this.data.tunnelStatistics,
            singlePage: false,
          }
          break;
        case 4:
          obj = {
            title: "工程集群健康监测",
            subText: "",
            componentL: 'HealthIndex',
            componentR: '',
            singlePage: false,
          }
          break;
        case 5:
          obj = {
            title: "日常养护专题",
            subText: "",
            componentL: '',
            componentR: 'Maintenance',
            singlePage: this.isBig ? false : true,
          }
          break;
        case 6:
          obj = {
            title: "专项养护专题",
            subText: "",
            componentL: '',
            componentR: 'SpecialIndex',
            singlePage: this.isBig ? false : true,
          }
          break;
        case 7:
          obj = {
            title: "巡检查专题",
            subText: "巡检查专题",
            componentL: 'PatrolCheck',
            componentR: 'PatrolDetail',
            data: {},
            singlePage: false,
          }
          break;
        default:
          break;
      }
      this.$emit('click', obj);
    },
  },
}
</script>

<style lang="scss" scoped>
@import "@/assets/styles/utils.scss";

.page-l {
  width: 100%;
  height: 100%;
  position: relative;

  .info-l {
    height: 100%;
    position: absolute;
    left: 0;
    top: 0;
    z-index: 1;
  }

  .info-c {
    width: 100%;
    height: 100%;
    position: relative;
    z-index: 0;

    .map-case {
      position: absolute;
      bottom: vwpx(50px);
      transform: translateX(-50%);
      display: flex;
      align-items: center;

      .case-list {
        display: flex;
        flex-direction: column;

        .case-row {
          width: vwpx(140px);
          height: vwpx(8px);
        }

        .case-text {
          font-family: Microsoft YaHei UI, Microsoft YaHei UI;
          font-weight: 400;
          font-size: vwpx(26px);
          color: #FFFFFF;
          text-align: left;
          // font-style: normal;
          // text-transform: none;
          margin-top: vwpx(6px);
          margin-left: vwpx(6px);
        }
      }
    }
  }

  .info-r {
    height: 100%;
    position: absolute;
    right: 0;
    top: 0;
    z-index: 1;
  }

  .mb-2 {
    margin-bottom: vwpx(20px);
  }
}
</style>