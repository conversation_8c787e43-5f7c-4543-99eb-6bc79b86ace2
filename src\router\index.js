import Vue from 'vue'
import Router from 'vue-router'

Vue.use(Router)

/* Layout */
import Layout from '@/layout'

/**
 * Note: 路由配置项
 *
 * hidden: true                     // 当设置 true 的时候该路由不会再侧边栏出现 如401，login等页面，或者如一些编辑页面/edit/1
 * alwaysShow: true                 // 当你一个路由下面的 children 声明的路由大于1个时，自动会变成嵌套的模式--如组件页面
 *                                  // 只有一个时，会将那个子路由当做根路由显示在侧边栏--如引导页面
 *                                  // 若你想不管路由下面的 children 声明的个数都显示你的根路由
 *                                  // 你可以设置 alwaysShow: true，这样它就会忽略之前定义的规则，一直显示根路由
 * redirect: noRedirect             // 当设置 noRedirect 的时候该路由在面包屑导航中不可被点击
 * name:'router-name'               // 设定路由的名字，一定要填写不然使用<keep-alive>时会出现各种问题
 * query: '{"id": 1, "name": "ry"}' // 访问路由的默认传递参数
 * roles: ['admin', 'common']       // 访问路由的角色权限
 * permissions: ['a:a:a', 'b:b:b']  // 访问路由的菜单权限
 * meta : {
    noCache: true                   // 如果设置为true，则不会被 <keep-alive> 缓存(默认 false)
    title: 'title'                  // 设置该路由在侧边栏和面包屑中展示的名字
    icon: 'svg-name'                // 设置该路由的图标，对应路径src/assets/icons/svg
    breadcrumb: false               // 如果设置为false，则不会在breadcrumb面包屑中显示
    activeMenu: '/system/user'      // 当路由设置了该属性，则会高亮相对应的侧边栏。
  }
 */

// 公共路由
export const constantRoutes = [
  {
    path: '/redirect',
    component: Layout,
    hidden: true,
    children: [
      {
        path: '/redirect/:path(.*)',
        component: () => import('@/views/redirect')
      }
    ]
  },
  {
    path: '/login',
    component: () => import('@/views/login'),
    hidden: true
  },
  {
    path: '/register',
    component: () => import('@/views/register'),
    hidden: true
  },
  {
    path: '/404',
    component: () => import('@/views/error/404'),
    hidden: true
  },
  {
    path: '/401',
    component: () => import('@/views/error/401'),
    hidden: true
  },
  {
    path: '/index1',
    component: () => import('@/views/homenew/TibetHome'),
    hidden: true
  },
  // 西藏预警监测大屏
  {
    path: '/TibetWarning',
    component: () => import('@/views/Tibet/index'),
    hidden: true
  },
  {
    path: '/TibetWarningNew',
    component: () => import('@/views/Tibet1/index'),
    hidden: true
  },
  {
    path: '',
    component: Layout,
    redirect: 'index',
    children: [
      {
        path: 'index',
        // component: () => import('@/views/index'),
        // component: () => import('@/views/home/<USER>'),
        // component: () => import('@/views/homenew/index'),
        component: () => {
          let logoInfo = JSON.parse(localStorage.getItem('logoInfo'))
          if (logoInfo?.homePath === '/xizangindex') {
            return import('@/views/homenew/TibetHome')
          } else {
            return import('@/views/homenew/index')
          }
        },
        name: 'Index',
        meta: { title: '首页', icon: 'dashboard', affix: true }
      },
    ]
  },
  // 正式：home
  {
    path: '/home',
    component: () => import('@/views/index/index1.vue'), // 普通正常页面
    hidden: true
  },
  
  {
    path: '/TibetVideo',
    component: () => import('@/views/Tibet/monitoringSystem/realTime/components/Video/TibetVideo.vue'), // 临时视频页面
    hidden: true
  },
  // 正式：监测一张图
  {
    path: '/HealthMonitoring',
    component: () => import('@/views/HealthMonitoring/index.vue'), // 普通正常页面
    hidden: true
  },
  // 汇报：homeHB
  {
    path: '/homeHB',
    component: () => import('@/views/index/index4k.vue'), // 大屏展示
    hidden: true
  },
  {
    path: '/onemap',
    name: 'oneMap',
    component: () => import('@/views/map/index.vue'),
    hidden: true,
    meta: { noCache: false }
  },
  {
    path: '/cockpit',
    name: 'cockpit',
    component: () => import('@/views/cockpit/index.vue'),
    hidden: true
  },
  {
    path: '/dataCenter', // 大数据中心地址
    name: 'dataCenter',
    component: () => import('@/views/dataCenter/index.vue'),
    hidden: true
  },
  {
    path: '/dataCenterMap', // 大数据中心一张图地址（该需求调整仅⽤于核对和修改静态版本数据，适⽤于2025年交通部4⽉15⽇汇报演示使⽤）
    name: 'dataCenterMap',
    component: () => import('@/views/dataCenterMap/index.vue'),
    hidden: true
  },
  {
    path: '/dataCenterMap/heatMap', // 大数据中心热力图地址（该需求调整仅⽤于核对和修改静态版本数据，适⽤于2025年交通部4⽉15⽇汇报演示使⽤）
    name: 'heatMap',
    component: () => import('@/views/dataCenterMap/heatMap.vue'),
    hidden: true
  },
  {
    path: '/centerPage', // 大数据中心 驾驶舱（该需求调整仅⽤于核对和修改静态版本数据，适⽤于2025年交通部4⽉15⽇汇报演示使⽤）
    name: 'CenterPage',
    component: () => import('@/views/centerPage/index.vue'),
    hidden: true
  },
  {
    path: '/disaster/exp_card',
    name: 'openjimu4highslope',
    component: Layout,
    hidden: true,
    children: [
      {
        path: 'highslope',
        component: () => import('@/views/disaster/JimuAdapter4HighSlope.vue'),
        name: 'JimuAdapter4HighSlope',
        meta: { title: '公路高边坡信息调查表导出' },
      },
    ]
  },
  {
    path: '/disaster/exp_card',
    name: 'openjimu4riskcrumble',
    component: Layout,
    hidden: true,
    children: [
      {
        path: 'risk_crumble',
        component: () => import('@/views/disaster/JimuAdapter4RiskCrumble.vue'),
        name: 'JimuAdapter4RiskCrumble',
        meta: { title: '崩塌信息调查表导出' },
      },
    ]
  },
  {
    path: '/disaster/exp_card',
    name: 'openjimu4risklandslide',
    component: Layout,
    hidden: true,
    children: [
      {
        path: 'risk_landslide',
        component: () => import('@/views/disaster/JimuAdapter4RiskLandSlide.vue'),
        name: 'JimuAdapter4RiskLandSlide',
        meta: { title: '滑坡信息调查表导出' },
      },
    ]
  },
  {
    path: '/disaster/exp_card',
    name: 'openjimu4risksubside',
    component: Layout,
    hidden: true,
    children: [
      {
        path: 'risk_subside',
        component: () => import('@/views/disaster/JimuAdapter4RiskSubside.vue'),
        name: 'JimuAdapter4RiskSubside',
        meta: { title: '沉陷与塌陷信息调查表导出' },
      },
    ]
  },
  {
    path: '/disaster/exp_card',
    name: 'openjimu4riskdebrisflow',
    component: Layout,
    hidden: true,
    children: [
      {
        path: 'risk_debris_flow',
        component: () => import('@/views/disaster/JimuAdapter4RiskDebrisFlow.vue'),
        name: 'JimuAdapter4RiskDebrisFlow',
        meta: { title: '泥石流信息调查表导出' },
      },
    ]
  },
  {
    path: '/disaster/exp_card',
    name: 'openjimu4riskwaterdest',
    component: Layout,
    hidden: true,
    children: [
      {
        path: 'risk_water_dest',
        component: () => import('@/views/disaster/JimuAdapter4RiskWaterDest.vue'),
        name: 'JimuAdapter4RiskWaterDest',
        meta: { title: '水毁信息调查表导出' },
      },
    ]
  },
  {
    path: '/disaster/exp_card',
    name: 'openjimu4riskother',
    component: Layout,
    hidden: true,
    children: [
      {
        path: 'risk_other',
        component: () => import('@/views/disaster/JimuAdapter4RiskOther.vue'),
        name: 'JimuAdapter4RiskOther',
        meta: { title: '其它信息调查表导出' },
      },
    ]
  },
  {
    path: '/disaster/exp_card',
    name: 'openjimu4disastersubmitted',
    component: Layout,
    hidden: true,
    children: [
      {
        path: 'disaster_submitted',
        component: () => import('@/views/disaster/JimuAdapter4DisasterSubmitted.vue'),
        name: 'JimuAdapter4DisasterSubmitted',
        meta: { title: '灾毁信息导出' },
      },
    ]
  },
  // {
  //   path: '',
  //   component: Layout,
  //   redirect: 'test',
  //   children: [
  //     {
  //       path: 'test',
  //       component: () => import('@/views/stdTmplPage'),
  //       name: 'Index',
  //       meta: { title: '模板页', icon: 'dashboard', affix: true }
  //     }
  //   ]
  // },

  {
    path: '/user',
    component: Layout,
    hidden: true,
    redirect: 'noredirect',
    children: [
      {
        path: 'profile',
        component: () => import('@/views/system/user/profile/index'),
        name: 'Profile',
        meta: { title: '个人中心', icon: 'user' }
      }
    ]
  },

  {
    path: '/task',
    component: Layout,
    hidden: true,
    redirect: 'noredirect',
    children: [
      {
        hidden: true,
        path: 'next',
        component: () => import('@/views/process/task/next/await.vue'),
        name: 'Index',
        meta: { title: '任务详情', icon: 'dashboard', }
      }
    ]
  },
  {
    path: '/mergeRoadSection',
    component: Layout,
    hidden: true,
    redirect: 'noredirect',
    children: [
      {
        hidden: true,
        path: 'info',
        component: () => import('@/views/system/routeSegments/indexMerge.vue'),
        name: 'Index',
        meta: { title: '养护路段信息', icon: 'dashboard', }
      }
    ]
  },
  // 流程审批表单
  {
    name: 'ProcessForm',
    path: '/processForm',
    component: () => import('@/views/process/form/index'),
    hidden: true,
    children: [
      // 灾害表单
      {
        name: 'RiskForm',
        path: '/processForm/riskForm',
        component: () => import('@/views/disaster/risk/form/index'),
      },
      // 高边坡表单
      {
        name: 'HighSlopeForm',
        path: '/processForm/highSlopeForm',
        component: () => import('@/views/disaster/highSlope/form/index'),
      },
      //灾毁上报
      {
        name: 'DisasterSubmittedForm',
        path: '/processForm/DisasterSubmittedForm',
        component: () => import('@/views/disaster/damage/form/index'),
      }
    ]
  },
  {
    name: 'MonitoringSystemBigScreen',
    path: '/monitoringSystem/bigScreen',
    component: () => import('@/views/monitoringSystem/bigScreen/index'),
    hidden: true,
  },
  {
    name: 'MonitoringSystemRealTime',
    path: '/monitoringSystem/realTime',
    component: () => import('@/views/monitoringSystem/realTime/index'),
    hidden: true,
  },
  {
    name: 'MonitoringSystemRealTimeTibet',
    path: '/monitoringSystemTibet/realTime',
    component: () => import('@/views/Tibet1/monitoringSystem/realTime/index'),
    hidden: true,
  },
  {
    name: 'healthMonitoringRealTime',
    path: '/healthMonitoringRealTime/realTime',
    component: () => import('@/views/HealthMonitoring/monitoringSystem/realTime/index'),
    hidden: true,
  },
  // 养护小助手
  {
    path: '/AI',
    component: Layout,
    hidden: true,
    redirect: 'noredirect',
    children: [
      {
        path: 'aiAssistant',
        component: () => import('@/views/ai/index.vue'),
        name: 'aiAssistant',
        meta: { title: '养护小助手' }
      }
    ]
  },
  // 科学决策大屏展示
  {
    path: '/scientificDecision',
    component: () => import('@/views/index/scientificDecision.vue'),
    hidden: true
  },
]

// 动态路由，基于用户权限动态去加载
export const dynamicRoutes = [
  {
    path: '/system/user-auth',
    component: Layout,
    hidden: true,
    permissions: ['system:user:edit'],
    children: [
      {
        path: 'role/:userId(\\d+)',
        component: () => import('@/views/system/user/authRole'),
        name: 'AuthRole',
        meta: { title: '分配角色', activeMenu: '/system/user' }
      }
    ]
  },
  {
    path: '/system/role-auth',
    component: Layout,
    hidden: true,
    permissions: ['system:role:edit'],
    children: [
      {
        path: 'user/:roleId(\\d+)',
        component: () => import('@/views/system/role/authUser'),
        name: 'AuthUser',
        meta: { title: '分配用户', activeMenu: '/system/role' }
      }
    ]
  },
  {
    path: '/system/dict-data',
    component: Layout,
    hidden: true,
    permissions: ['system:dict:list'],
    children: [
      {
        path: 'index/:dictId(\\d+)',
        component: () => import('@/views/system/dict/data'),
        name: 'Data',
        meta: { title: '字典数据', activeMenu: '/system/dict' }
      }
    ]
  },
  {
    path: '/monitor/job-log',
    component: Layout,
    hidden: true,
    permissions: ['monitor:job:list'],
    children: [
      {
        path: 'index/:jobId(\\d+)',
        component: () => import('@/views/monitor/job/log'),
        name: 'JobLog',
        meta: { title: '调度日志', activeMenu: '/monitor/job' }
      }
    ]
  },
  {
    path: '/tool/gen-edit',
    component: Layout,
    hidden: true,
    permissions: ['tool:gen:edit'],
    children: [
      {
        path: 'index/:tableId(\\d+)',
        component: () => import('@/views/tool/gen/editTable'),
        name: 'GenEdit',
        meta: { title: '修改生成配置', activeMenu: '/tool/gen' }
      }
    ]
  }
]

// 防止连续点击多次路由报错
let routerPush = Router.prototype.push;
let routerReplace = Router.prototype.replace;
// push
Router.prototype.push = function push(location) {
  return routerPush.call(this, location).catch(err => err)
}
// replace
Router.prototype.replace = function push(location) {
  return routerReplace.call(this, location).catch(err => err)
}

export default new Router({
  mode: 'history', // 去掉url中的#
  scrollBehavior: () => ({ y: 0 }),
  routes: constantRoutes
})
