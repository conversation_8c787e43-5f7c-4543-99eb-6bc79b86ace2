<template>
  <div class="app-container maindiv">
    <el-row :gutter="20">
      <!--筛选区开始-->
      <el-col :span="24" :xs="24">
        <el-row>
          <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" label-width="68px">
            <el-form-item label="" prop="disType">
              <el-select v-model="queryParams.disType" style="width: 240px" clearable filterable placeholder="事件类型" >
                <el-option v-for="dict in disType" :key="dict.dictValue"
                           :label="dict.dictLabel" :value="dict.dictValue">
                </el-option>
              </el-select>
            </el-form-item>

            <el-form-item label="" prop="collectTimes">
              <el-date-picker
                  v-model="queryParams.collectTimes"
                  format="yyyy-MM-dd"
                  value-format="yyyy-MM-dd"
                  style="width: 240px"
                  placeholder="采集开始时间"
                  clearable
              ></el-date-picker>
            </el-form-item>
            <el-form-item label="" prop="collectTimee">
              <el-date-picker
                  v-model="queryParams.collectTimee"
                  format="yyyy-MM-dd"
                  value-format="yyyy-MM-dd"
                  style="width: 240px"
                  placeholder="采集结束时间"
                  clearable
              ></el-date-picker>
            </el-form-item>
            <el-form-item label="" prop="updateBy">
              <el-cascader
                  v-model="queryParams.updateBy"
                  :options="deptUserOptions"
                  :props="props"
                  :show-all-levels="false"
                  ref="deptUser"
                  placeholder="操作人" clearable style="width: 240px"
                  filterable clearable></el-cascader>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
              <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
            </el-form-item>
          </el-form>
        </el-row>
        <!--筛选区结束-->
        <el-row class="mb8">
          <el-button icon="el-icon-tickets" type="success" @click="openEventInfo()">事件信息</el-button>
        </el-row>
        <!--数据表格开始-->
        <div class="tableDiv">
          <el-table v-adjust-table stripe size="mini" height="400px"
                    style="width: 100%" v-loading="loading" border :data="dataList"
                    ref="dataTable" @row-click="handleClickRow"
                    @selection-change="handleSelectionChange" highlight-current-row
                    @current-change="handleCurrentChange">
            <el-table-column type="selection" width="50" align="center"/>
            <el-table-column fixed label="序号" type="index" width="50"></el-table-column>
            <template v-for="column in columns">
              <el-table-column :label="column.label" show-overflow-tooltip
                               v-if="column.visible"
                               align="center"
                               :prop="column.field"
                               :width="column.width">
                <template slot-scope="scope">
                  <dict-tag v-if="column.dict" :options="dict.type[column.dict]" :value="scope.row[column.field]"/>
                  <span v-else-if="column.isTime">{{ parseTime(scope.row[column.field], '{y}-{m}-{d}') }}</span>
                    <span v-else>{{ scope.row[column.field] }}</span>
                </template>
              </el-table-column>
            </template>
          </el-table>
          <pagination
              v-show="total>0"
              :total="total"
              :page.sync="queryParams.pageNum"
              :limit.sync="queryParams.pageSize"
              @pagination="handleQuery"
          />
        </div>
        <!--数据表格结束-->
        <div style="text-align: right">
          <el-button type="primary" @click="handleSave">保存</el-button>
        </div>
      </el-col>
    </el-row>
    <el-dialog
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :destroy-on-close="true"
      append-to-body modal-append-to-body
      :visible.sync="eventInfoVisible"
      v-if="eventInfoVisible"
      title="事件信息"
      width="80%">
      <event-detail @close="closeEventInfo" :dis-info="disInfo" :daliy-id="daliyId"/>
    </el-dialog>
  </div>
</template>

<script>

import selectTree from "@/components/DeptTmpl/selectTree.vue";
import {findDiseaseDataList} from '@/api/dailyMaintenance/eventManage/eventData.js'
import EventDetail from "@/views/dailyMaintenance/component/eventDetail.vue";
import {listAllDiseases} from "@/api/patrol/diseases";
import DeptUserTreeSelect from "@/components/DeptUserTreeSelect/index.vue"
import {getTreeStruct} from "@/api/tmpl";
import {formatPile} from "@/utils/ruoyi";

export default {
  name: "eventInfo",
  components: {EventDetail, selectTree, DeptUserTreeSelect},
  dicts: ['route_direction', 'lane', 'disposal_type', 'sys_asset_type'],
  data() {
    return {
      showSearch: false,
      loading: false,
      total: 0,
      queryParams: {
        pageNum: 1,
        pageSize: 50,
        disStatus: 0,
        updateBy: ''
      },
      props: {
        multiple: false,//是否多选
        value: "id",
        emitPath: false
      },
      // 部门-用户树选项
      deptUserOptions: [],
      // 列信息
      columns: [
        {key: 20, width: 100, field: 'disCode', label: `事件编码`, visible: true},
        {key: 0, width: 100, field: 'assetMainType', label: `资产类型`, visible: true, dict: 'sys_asset_type'},
        {key: 1, width: 100, field: 'maiSecId', label: `路段`, visible: true},
        {key: 2, width: 100, field: 'routeCode', label: `路线编码`, visible: true},
        {key: 3, width: 100, field: 'direction', label: `上下行`, visible: true, dict: 'route_direction'},
        {key: 4, width: 100, field: 'lane', label: `位置`, visible: true, dict: 'lane'},
        {key: 5, width: 100, field: 'beginMileShow', label: `起点桩号`, visible: true},
        {key: 6, width: 100, field: 'endMileShow', label: `终点桩号`, visible: true},
        {key: 7, width: 100, field: 'disTypeName', label: `事件类型`, visible: true, dict: 'sys_asset_type'},
        {key: 8, width: 100, field: 'dealType', label: `处置类型`, visible: true, dict: 'disposal_type'},
        {key: 9, width: 100, field: 'disDesc', label: `描述`, visible: true,slots: true,
          render: (row) => {
            return (
              <el-tooltip className="item" effect="dark" content={row.disDesc} placement="top-start">
                <div style="overflow: hidden;white-space:nowrap;text-overflow:ellipsis">{row.disDesc}</div>
              </el-tooltip>
            )
          }},
        {key: 10, width: 100, field: 'remark', label: `备注`, visible: true},
        {key: 11, width: 100, field: 'collectTime', label: `采集时间`, visible: true},
        {key: 12, width: 100, field: 'updateByName', label: `操作人`, visible: true},
      ],
      // 表格数据
      dataList: [],
      // 选中数组
      checkData: [],
      eventInfoVisible: false,
      disInfo: {},
      disType: []
    };
  },
  props: {
    maiSecId: {
      type: String,
      default: ""
    },
    filterData: {
      type: Array,
      default: () => []
    },
    daliyId: {
      type: String,
      default: ''
    },
    conId: {
      type: String,
      default: ''
    }
  },
  watch: {},
  mounted() {
    this.getDeptTreeDef()
    this.handleQuery()
    this.getDisType()
  },
  methods: {
    /** 查询部门-用户下拉树结构 */
    getDeptTreeDef() {
      getTreeStruct({types:111}).then(response => {
        this.deptUserOptions = response.data;
      });
    },
    // 查询
    handleQuery() {
      this.loading = true
      if (this.maiSecId) this.queryParams.maiSecId = this.maiSecId
      this.queryParams.conConId = this.conId
      this.queryParams.dealTypeList = [0,3]
      findDiseaseDataList(this.queryParams).then(res => {
        if (res.code == 200) {
          this.loading = false
          res.rows.forEach(item => {
            item.beginMileShow = formatPile(item.beginMile)
            item.endMileShow = formatPile(item.endMile)
          })
          this.dataList = res.rows.filter(item => {
            return !this.filterData.some(filterItem => filterItem.id === item.id);
          })
          this.total = res.total
        }
      })
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.checkData = selection;
    },
    // 恢复
    resetQuery() {
      this.queryParams = {
        pageNum: 1,
        pageSize: 50,
        disStatus: 0
      };
    },
    // 保存
    handleSave() {
      this.$emit('saveEvent', this.checkData)
    },
    handleClickRow(e) {
      e.isSelected = !e.isSelected;
      this.$refs.dataTable.toggleRowSelection(e);
    },
    handleCurrentChange(row) {
      this.disInfo = row
    },
    openEventInfo() {
      if (!this.disInfo.id) {
        this.$message.warning('请先选择事件')
        return
      }
      this.eventInfoVisible = true
    },
    getDisType() {
      listAllDiseases().then(res => {
        this.disType = res.data.map(item => {
          return {
            dictLabel: item.diseaseName,
            dictValue: item.id
          }
        })
      })
    },
    closeEventInfo() {
      this.eventInfoVisible = false
    }
  }
};
</script>
<style>
</style>
<style lang="scss" scoped>
@import "@/assets/styles/business.scss";
</style>
