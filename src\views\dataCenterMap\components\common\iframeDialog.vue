<template>
  <div class="iframe-dialog" v-if="visibleSync">
    <iframe :src="iframeUrl" frameborder="0" width="100%" height="100%" v-if="iframeUrl" @load="onLoad"
      @error="onError" />
    <template v-else>
      <component :is="componentName" :type="params.jumpType || 2" :pageLId="pageLId" :pageRId="pageRId" :subTitle="title" :id="params.id"></component>
    </template>

    <template v-if="isBack">
      <div class="close" v-if="(iframeUrl && !loadding) || (componentName && !loadding)">
      <img src="@/assets/cockpit/back.png" @click="handelClose" :style="{
        width: isBig ? '140px' : '60px',
        height: isBig ? '64px' : '29px',
      }" />
    </div>
    </template>
  </div>
</template>

<script>
import { isBigScreen } from './util';
import subpage from '@/views/cockpit/components/subpage.vue';

export default {
  name: 'iframeDialog',
  provide() {
    return {
      that: this,
    };
  },
  components: {
    subpage,
  },
  props: {
    iframeUrl: {
      type: String,
      default: '',
    },
    visible: {
      default: false,
    },
    componentName: {
      type: String,
      default: '',
    },
    params: {
      type: [Array, Object, String],
      default: () => {
        return {};
      },
    },
    isBack: {
      type: Boolean,
      default: true,
    },
  },
  data() {
    return {
      isBig: isBigScreen(),
      loadding: true,
      title: '',
      pageRId: '',
      pageLId: '',
    };
  },
  computed: {
    visibleSync: {
      get() {
        if (this.visible) {
          this.$modal.loading();
          this.loadding = true;

          setTimeout(() => {
            this.$modal.closeLoading();
            this.loadding = false;
          }, 1000);
        }
        return this.visible;
      },
      set(v) {
        this.$emit('update:visible', v);
      },
    },
  },
  created() {
    if (this.params) {
      let obj = this.convertParamsToObject();
      this.title = obj.bridge_name || obj.route_name || obj.typeName || obj.name;
      this.pageRId = this.params.componentR;
      this.pageLId = this.params.componentL;
    }
  },
  methods: {
    convertParamsToObject() {
      if (typeof this.params === 'string') {
        const paramsArray = this.params.split('&');
        const paramsObject = paramsArray.reduce((acc, current) => {
          const [key, value] = current.split('=');
          acc[key] = value;
          return acc;
        }, {});
        return paramsObject;
      }
      return this.params;
    },
    handelClose() {
      this.$emit('cancel');
      this.loadding = false;
    },
    onLoad() {
      this.loadding = false;
      this.$modal.closeLoading();
    },
    onError() {
      this.loadding = false;
      this.$emit('cancel');
      this.$modal.closeLoading();
    },
  },
};
</script>

<style lang="scss" scoped>
@import '@/assets/styles/utils.scss';

.iframe-dialog {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;

  width: 100%;
  height: 100%;

  z-index: 99998;

  .close {
    position: fixed;
    top: vwpx(40px);
    right: vwpx(40px);
    cursor: pointer;
    z-index: 99999;
  }
}
</style>