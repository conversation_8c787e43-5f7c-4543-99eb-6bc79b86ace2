<template>
  <div class="app-container">
    <el-row :gutter="20">
      <!--设备类型数据-->
      <el-col :span="5" :xs="24" class="leftDiv">
        <span>设备类型</span>
        <div class="head-container" style="width: 300px">
          <el-tree
            :data="deviceTypeOptions"
            :props="defaultProps"
            :expand-on-click-node="false"
            ref="tree"
            default-expand-all
            node-key="id"
            highlight-current
            show-border
            @node-click="handleNodeClick">
            <span class="custom-tree-node" slot-scope="{ node, data }" 
                style="width: 100%;display: flex;justify-content: space-between;align-items: center;">
              <span class="treeLable">{{ node.label }}</span>
              <span v-if="data.id != '0'">
                  <el-button size="mini" type="text" icon="el-icon-edit" @click="handleUpdate(data)" 
                    v-hasPermi="['patrol:labordevicetype:edit']"
                  >修改</el-button>
                  <el-button size="mini" type="text" icon="el-icon-delete" @click="handleDelete(data)"
                      v-hasPermi="['patrol:labordevicetype:remove']"
                  >删除</el-button>
              </span>
            </span>
          </el-tree>
        </div>
      </el-col>
      <!--筛选区开始-->
      <el-col :span="5" :xs="24">
        <!--操作按钮区开始-->
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button
                type="primary"
                icon="el-icon-plus"
                size="mini"
                @click="handleAdd"
                v-hasPermi="['patrol:labordevicetype:add']"
            >新增</el-button>
          </el-col>
        </el-row>
      </el-col>
    </el-row>

    <!-- 添加或修改设备类型对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="600px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="110px">
          <el-col :span="12">
            <el-form-item label="父级类型" prop="parentId">
              <treeselect v-model="form.parentId" :options="deviceTypeRootOptions" :show-count="false"
                  placeholder="请输入父级类型"/>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="设备类型名称" prop="deviceType" :required="true">
              <el-input v-model="form.deviceType" placeholder="请输入设备类型名称" />
            </el-form-item>
          </el-col>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
  import { getLabordevicetypeTree, getLabordevicetype, delLabordevicetype, addLabordevicetype, updateLabordevicetype } from "@/api/patrol/labordevicetype";
  import { getToken } from "@/utils/auth";
  import Treeselect from "@riophae/vue-treeselect";
  import "@riophae/vue-treeselect/dist/vue-treeselect.css";

  export default {
    name: "Labordevicetype",
    components: { Treeselect },
    data() {
      return {
        // 遮罩层
        loading: true,
        // 选中数组
        ids: [],
        // 非单个禁用
        single: true,
        // 非多个禁用
        multiple: true,
        // 显示搜索条件
        showSearch: false,
        dictType:[],
        // 总条数
        total: 0,
        // 设备类型表格数据
        labordevicetypeList: null,
        // 弹出层标题
        title: "",
        // 部门树选项
        deviceTypeOptions: undefined,
        //根节点树
        deviceTypeRootOptions: [],
        //当前选中节点
        curentNodeType: undefined,
        // 是否显示弹出层
        open: false,

        // 表单参数
        form: {},
        defaultProps: {
          children: "children",
          label: "label"
        },
        // 用户导入参数
        upload: {
          // 是否显示弹出层（用户导入）
          open: false,
          // 弹出层标题（用户导入）
          title: "",
          // 是否禁用上传
          isUploading: false,
          // 是否更新已经存在的用户数据
          updateSupport: 0,
          // 设置上传的请求头部
          headers: { Authorization: "Bearer " + getToken() },
          // 上传的地址
          url: process.env.VUE_APP_BASE_API + "/system/user/importData"
        },
        // 查询参数
        queryParams: {
          pageNum: 1,
          pageSize: 50,
            parentId: null,
            deviceType: null,
            status: null,
        },
        // 列信息
        columns: [
        { key: 0, label: `设备父级id`, visible: true },
        { key: 1, label: `设备类型`, visible: true },
        { key: 2, label: `状态（0-正常，1-停用）`, visible: true },
        { key: 3, label: `删除标志（0代表存在，1代表删除）`, visible: true }
        ],
        // 表单校验
        rules: {
          // parentId: [
          //     { required: true, message: "设备父级id不能为空", trigger: "blur" }
          // ],
        }
      };
    },
    watch: {
      // 根据名称筛选部门树
                      },
    created() {
      this.getList();
      // this.getDeptTree();
      // this.getConfigKey("sys.user.initPassword").then(response => {
      //   this.initPassword = response.msg;
      // });
    },
    methods: {
      /** 查询用户列表 */
      getList() {
        this.loading = true;
        getLabordevicetypeTree().then(response => {
          this.deviceTypeOptions = response.data;
          if (this.deviceTypeOptions == null || this.deviceTypeOptions.length <= 0) {
            this.deviceTypeOptions.push({ id:"0", label:"无"});
          }
          this.deviceTypeRootOptions = [];
          this.deviceTypeOptions.forEach(element => {
            this.deviceTypeRootOptions.push({ id: element.id, label:element.label });  
          });
          this.loading = false;
          //初始化
          this.curentNodeType = undefined;
        });
      },
      // 节点单击事件
      handleNodeClick(data) {
        this.curentNodeType = data;
      },
      // 取消按钮
      cancel() {
        this.open = false;
        this.reset();
      },
      // 表单重置
      reset() {
        this.form = {
            parentId: null,
            deviceType: null,
            status: null,
            delFlag: null
        };
        this.resetForm("form");
      },
      /** 搜索按钮操作 */
      handleQuery() {
        this.queryParams.pageNum = 1;
        this.getList();
      },
      /** 重置按钮操作 */
      resetQuery() {
        this.dateRange = [];
        this.resetForm("queryForm");
        this.handleQuery();
      },
      // 多选框选中数据
      handleSelectionChange(selection) {
        this.ids = selection.map(item => item.id);
        this.single = selection.length != 1;
        this.multiple = !selection.length;
      },
      /** 新增按钮操作 */
      handleAdd() {
        //新增时，只能选择顶级,不是父级
        if(this.curentNodeType && this.curentNodeType.children.length == 0 && this.curentNodeType.parentId != '0'){
          this.$modal.msgSuccess("类型最多2级");
          return
        }
        
        this.reset();
        this.open = true;
        //给默认父节点
        this.form.parentId = this.curentNodeType?.id;
        this.title = "添加设备类型";
      },
      /** 修改按钮操作 */
      handleUpdate(row) {
        this.reset();
        const id = row.id || this.ids;
        getLabordevicetype(id).then(response => {
          this.form = response.data;
          this.open = true;
          this.title = "修改设备类型";
        });

      },
      /** 提交按钮 */
      submitForm: function() {
        this.$refs["form"].validate(valid => {
          if (valid) {
            if (this.form.id != null) {
              updateLabordevicetype(this.form).then(response => {
                this.$modal.msgSuccess("修改成功");
                this.open = false;
                this.getList();
              });
            } else {
              if (this.form.parentId == null || this.form.parentId == "") {
                this.form.parentId = "0";
              }
              addLabordevicetype(this.form).then(response => {
                this.$modal.msgSuccess("新增成功");
                this.open = false;
                this.getList();
              });
            }
          }
        });
      },
      /** 删除按钮操作 */
      handleDelete(row) {
        const id = row.id || this.ids;
        this.$modal.confirm('是否确认删除设备类型编号为"' + id + '"的数据项？').then(function() {
          return delLabordevicetype(id);
        }).then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        }).catch(() => {});
      },

      /** 导出按钮操作 */
      handleExport() {
        this.download('manager/labordevicetype/export', {
          ...this.queryParams
        }, `labordevicetype_${new Date().getTime()}.xlsx`)

      },
      /** 导入按钮操作 */
      handleImport() {
        this.upload.title = "用户导入";
        this.upload.open = true;
      },
      /** 下载模板操作 */
      importTemplate() {
        this.download('system/user/importTemplate', {
        }, `user_template.xlsx`)
      },
      // 文件上传中处理
      handleFileUploadProgress(event, file, fileList) {
        this.upload.isUploading = true;
      },
      // 文件上传成功处理
      handleFileSuccess(response, file, fileList) {
        this.upload.open = false;
        this.upload.isUploading = false;
        this.$refs.upload.clearFiles();
        this.$alert("<div style='overflow: auto;overflow-x: hidden;max-height: 70vh;padding: 10px 20px 0;'>" + response.msg + "</div>", "导入结果", { dangerouslyUseHTMLString: true });
        this.getList();
      },
      // 提交上传文件
      submitFileForm() {
        this.$refs.upload.submit();
      }
  }
  };
</script>
<style>
  .hasTagsView .app-main[data-v-078753dd]{
    background: #f5f7fa;
  }

  .tableDiv{
    background-color: white;
    padding-bottom: 10px;
  }
  .treeLable{
    font-size: 14px;
  }

  .leftDiv{
    border-right: 1px solid #d8dce5;
    min-height: calc(100vh - 130px);
    overflow-y: auto;
    height: calc(80vh - 130px);
    position: relative;
    top: -20px;
    padding-top: 10px;
    background-color: white;
  }

</style>
