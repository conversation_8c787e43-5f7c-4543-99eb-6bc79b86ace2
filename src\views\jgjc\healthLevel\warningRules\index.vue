<template>
  <div class='app-container'>
    <el-row :gutter='20'>
      <!-- 筛选区开始 -->
      <el-col :span='24' :xs='24'>
        <el-form :model='queryParams' ref='queryForm' :inline='true' label-width='80px'>
          <el-form-item label='名称' prop='ruleName'>
            <el-input v-model='queryParams.ruleName' placeholder='请输入名称' />
          </el-form-item>
          <el-form-item>
            <el-button
              icon='el-icon-search'
              size='mini'
              type='primary'
              @click='handleQuery'
            >查询
            </el-button>
            <el-button
              icon='el-icon-refresh'
              size='mini'
              @click='resetQuery'
            >重置
            </el-button>
          </el-form-item>
        </el-form>
        <el-row :gutter='10' class='mb8'>
          <el-col :span='1.5'>
            <el-button
              icon='el-icon-plus'
              size='mini'
              type='primary'
              @click='handleAdd'
            >新增
            </el-button>
          </el-col>
          <el-col :span='1.5'>
            <el-button
              icon='el-icon-delete'
              size='mini'
              type='danger'
              @click='handleDelete'
            >删除
            </el-button>
          </el-col>
        </el-row>
        <!-- 筛选区结束 -->
        <!-- 数据表格开始 -->
        <div class='tableDiv'>
          <el-table v-adjust-table v-loading='loading' :data='dataList'
                    :height="'calc(100vh - 260px)'" border size='mini'
                    @selection-change='handleSelectionChange'
                    style='width: 100%'>
            <el-table-column align='center' fixed='left' type='selection' width='50'></el-table-column>
            <el-table-column align='center' fixed label='序号' type='index' width='50'></el-table-column>
            <template v-for='(column, index) in columns'>
              <el-table-column v-if='column.visible' :key='index' :label='column.label' :prop='column.field'
                               :width='column.width'
                               align='center' show-overflow-tooltip>
                <template slot-scope='scope'>
                  <span>{{ scope.row[column.field] }}</span>
                </template>
              </el-table-column>
            </template>
            <el-table-column align='center' class-name='small-padding fixed-width' fixed='right' label='操作'
                             width='200'>
              <template slot-scope='scope'>
                <el-button icon='el-icon-edit' size='mini' type='text'
                           @click='handleEdit(scope.row)'>编辑
                </el-button>
              </template>
            </el-table-column>
          </el-table>
          <pagination
            v-show='total > 0'
            :limit.sync='queryParams.pageSize'
            :page.sync='queryParams.pageNum'
            :total='total'
            @pagination='handleQuery'
          />
        </div>
        <!-- 数据表格结束 -->
      </el-col>
    </el-row>
    <el-dialog :title='dialogTitle' :visible.sync='dialogVisible' v-if='dialogVisible' append-to-body destroy-on-close
               width='80%'>
      <el-form ref='elForm' :model='formData' :rules='rules' label-width='140px' size='medium'>
        <el-form-item label='预警规则名称' prop='ruleName'>
          <el-input v-model='formData.ruleName' placeholder='请输入名称' />
        </el-form-item>
        <el-form-item label='规则描述' prop='ruleDescription'>
          <el-input type='textarea' :rows='3' v-model='formData.ruleDescription' />
        </el-form-item>
      </el-form>
      <div class='title'>预警判断式设置</div>
      <div class='mb10 mt10'>判断式格式：</div>
      <div style='color: #999999'>
        $1 < x < $2,x >= $3
        &lt;x&gt;代表获取到的数据，<$1>代表第一个参数，判断式权重高者优先判断，逗号分隔的判断式满足其一即判断为该级预警。
      </div>
      <el-table v-adjust-table :data='formData.alertRuleChainList' style='width: 100%' class='mt10 mb10' height='300px'>
        <el-table-column align='center' label='预警级别' prop='alertLevel'>
          <template slot-scope='scope'>
            <el-select v-model='scope.row.alertLevel' placeholder='请选择预警级别' clearable style='width: 100%'>
              <el-option v-for='(item, index) in levelList' :key='index' :label='item.name' :value='item.value' />
            </el-select>
          </template>
        </el-table-column>
        <el-table-column align='center' label='详细预警名称' prop='detailRuleName'>
          <template slot-scope='scope'>
            <el-input v-model='scope.row.detailRuleName' placeholder='请输入详细名称' style='width: 100%' />
          </template>
        </el-table-column>
        <el-table-column align='center' label='预警判断式' prop='ruleExpression'>
          <template slot-scope='scope'>
            <el-input v-model='scope.row.ruleExpression' placeholder='请输入预警判断式' style='width: 100%' />
          </template>
        </el-table-column>
        <el-table-column align='center' label='判断式参数描述' prop='alertParameterDescription'>
          <template slot-scope='scope'>
            <el-input v-model='scope.row.alertParameterDescription' placeholder='请输入判断式参数描述'
                      style='width: 100%' />
          </template>
        </el-table-column>
        <el-table-column align='center' label='预警判断式描述' prop='alertRuleChainDescription'>
          <template slot-scope='scope'>
            <el-input v-model='scope.row.alertRuleChainDescription' placeholder='请输入预警判断式描述'
                      style='width: 100%' />
          </template>
        </el-table-column>
        <el-table-column align='center' label='判断方式' prop='judgeMethod'>
          <template slot-scope='scope'>
            <el-select v-model='scope.row.judgeMethod' placeholder='请选择判断方式' clearable style='width: 100%'>
              <el-option v-for='(item, index) in judgeMethodList' :key='index' :label='item.name' :value='item.value' />
            </el-select>
          </template>
        </el-table-column>
        <el-table-column align='center' label='采样方式' prop='analysisType'>
          <template slot-scope='scope'>
            <a-select :api-method='listSampleTypes' :params='selectParams' v-model='scope.row.analysisType' :default-props='{label: "title",value: "name",}'></a-select>
          </template>
        </el-table-column>
        <el-table-column align='center' label='采样周期' prop='granularityNum'>
          <template slot-scope='scope'>
            <el-input v-model='scope.row.granularityNum' placeholder='请输入采样周期' style='width: 100%' />
          </template>
        </el-table-column>
        <el-table-column align='center' label='周期单位' prop='granularityType'>
          <template slot-scope='scope'>
            <a-select :api-method='listSampleIntervals' :params='selectParams' v-model='scope.row.granularityType' :default-props='{label: "title",value: "name",}'></a-select>
          </template>
        </el-table-column>
        <el-table-column align='center' label='操作' prop='option'>
          <template slot-scope='scope'>
            <el-button type='text' @click='handleRemoveAlert(scope.row)'>删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      <div style='text-align: right'>
        <el-button @click='addAlertRule'>添加判断式</el-button>
        <el-button @click='closeDialog'>取消</el-button>
        <el-button type='primary' @click='handleConfirm'>确定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { saveAlertRule, deleteAlertRule, updateAlertRule, getAlertRulePage } from '@/api/jgjc/healthLevel/warningRules'
import ASelect from '@/views/jgjc/component/aSelect/index.vue'
import { listSampleTypes } from '@/api/jgjc/baseInfo/samplingType'
import { listSampleIntervals } from '@/api/jgjc/baseInfo/samplingInterval'

export default {
  name: 'ContentManagement',
  components: { ASelect },
  data() {
    return {
      // 遮罩层
      loading: false,
      // 总条数
      total: 0,
      dataList: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
      },
      // 列信息
      columns: [
        { key: 0, width: 300, field: 'ruleName', label: '预警规则名称', visible: true },
        { key: 1, field: 'ruleDescription', label: '预警规则描述', visible: true },
        { key: 1, width: 100, field: 'number', label: '判断式个数', visible: true },
      ],
      // 对话框标题
      dialogTitle: '',
      dialogVisible: false,
      typeData: {},
      formData: {},
      ids: [],
      rules: {
        ruleName: [{ required: true, message: '请输入名称', trigger: 'blur' }],
        // orders: [{required: true, message: '请输入排序', trigger: 'blur'}]
      },
      levelList: [
        { name: '蓝色预警', value: '一级' },
        { name: '黄色预警', value: '二级' },
        { name: '橙色预警', value: '三级' },
        { name: '红色预警', value: '四级' },
      ],
      judgeMethodList: [
        { name: '表达式', value: 'DEFAULT' },
        { name: '超限次数', value: 'FREQUENCY' },
      ],
      selectParams: {
        pageNum: 1,
        pageSize: 100,
      },
    }
  },
  created() {
    this.handleQuery()
  },
  methods: {
    listSampleTypes,
    listSampleIntervals,
    handleQuery() {
      this.loading = true
      getAlertRulePage(this.queryParams).then(res => {
        this.dataList = res.rows
        this.dataList.forEach(item => {
          item.number = item.alertRuleChainList.length
        })
        this.total = res.total
        this.loading = false
      }).catch(err => {
        this.loading = false
        console.error(err)
      })
    },
    resetQuery() {
      this.queryParams = {
        pageNum: 1,
        pageSize: 10,
        title: '',
      }
      this.handleQuery()
    },
    handleAdd() {
      this.dialogTitle = '新增'
      this.formData = {
        alertRuleChainList: [],
      }
      this.formData.alertRuleChainList.push({
        alertLevel: '一级',
        judgeMethod: 'DEFAULT'
      })

      this.dialogVisible = true
    },
    handleEdit(row) {
      this.dialogTitle = '编辑'
      this.formData = Object.assign({}, row)
      this.dialogVisible = true
    },
    handleConfirm() {
      this.$refs.elForm.validate(valid => {
        if (valid) {
          if (!this.formData.alertRuleChainList || this.formData.alertRuleChainList.length === 0) {
            this.$message.error('请添加判断式')
            return
          }
          this.loading = true
          this.formData.alertRuleChainList.forEach(item => {
            item.ruleExpression = btoa(unescape(encodeURIComponent(item.ruleExpression)));
          })
          if (this.formData.id) {
            updateAlertRule(this.formData).then(res => {
              if (res.code === 200) {
                this.$message.success('编辑成功')
                this.handleQuery()
              } else {
                this.$message.error(res.msg)
              }
              this.loading = false
              this.closeDialog()
            }).catch(err => {
              this.loading = false
              console.error(err)
            })
          } else {
            saveAlertRule(this.formData).then(res => {
              if (res.code === 200) {
                this.$message.success('新增成功')
                this.handleQuery()
              } else {
                this.$message.error(res.msg)
              }
              this.loading = false
              this.closeDialog()
            }).catch(err => {
              this.loading = false
              console.error(err)
            })
          }
        }
      })
    },
    addAlertRule() {
      this.formData.alertRuleChainList.push({
        alertLevel: '一级',
        judgeMethod: 'DEFAULT'
      })
    },
    handleRemoveAlert(row) {
      this.formData.alertRuleChainList.splice(this.formData.alertRuleChainList.indexOf(row), 1)
    },
    closeDialog() {
      this.dialogVisible = false
      this.$refs.elForm.resetFields()
    },
    handleSelectionChange(e) {
      this.ids = e.map(item => item.id)
    },
    handleDelete() {
      if (this.ids.length <= 0) {
        this.$message.warning('请勾选需要删除的数据')
        return
      }
      this.$confirm('是否确认删除选中的数据？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }).then(() => {
        this.loading = true
        deleteAlertRule({ ids: this.ids }).then(res => {
          if (res.code === 200) {
            this.$message.success('删除成功')
            this.handleQuery()
          } else {
            this.$message.error(res.msg)
          }
          this.loading = false
        }).catch(err => {
          this.loading = false
          console.error(err)
        })
      })
    },
  },
}
</script>

<style scoped>
.tableDiv {
  margin-top: 20px;
}

.title {
  font-size: 18px;
  font-weight: bold;
}
</style>
<style lang='scss' scoped>
@import "@/assets/styles/business.scss";
</style>
