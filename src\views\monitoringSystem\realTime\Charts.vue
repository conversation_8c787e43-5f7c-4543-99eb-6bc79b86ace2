<template>
  <div class="charts" :class="theme" ref="chartsRef" v-loading="loading"
    element-loading-background="rgba(0, 0, 0, 0.2)">
    <!-- 搜索框 -->
    <div class="search-box">
      <div class="search-item">
        <span>设备安装代码</span>
        <el-select v-model="searchForm.sensorId" placeholder="请选择" @change="changeSensor">
          <el-option v-for="(item, index) in sensorOptions" :key="index" :label="item.sensorInstallCode"
            :value="item.sensorId">
          </el-option>
        </el-select>
      </div>
      <div class="search-item" v-show="activeName != '3'">
        <span>选择Y轴值类型和顺序</span>
        <el-select v-model="searchForm.specificMonitorContentIds" placeholder="请选择" multiple :disabled="searchDisable"
          @change="handleDraw">
          <el-option v-for="(item, index) in specificMonitorTypeList" :key="index" :label="item.specificMonitorTypeName"
            :value="item.specificMonitorTypeId">
          </el-option>
        </el-select>
      </div>
      <div class="search-item" v-show="activeName == '2'">
        <span>数据采样</span>
        <div class="select-box">
          <div v-for="(item, index) in ['原始', '分', '10分', '小时', '日']" :key="index"
            @click="searchDisable ? null : handleSelectBox(item)"
            :class="{ 'select-item-active': searchForm.granularityTypeName === item, 'select-item-disabled': searchDisable }">
            {{ item }}
          </div>
        </div>
      </div>
      <div class="check-box search-item" v-show="activeName != '3'">
        <el-checkbox v-model="searchForm.isPreprocess" :disabled="searchDisable" @change="handleDraw">预处理</el-checkbox>
      </div>
      <div class="check-box search-item" v-show="activeName != '3'">
        <el-checkbox v-model="searchForm.isShowLimit" :disabled="searchDisable" @change="handleDraw">预警线</el-checkbox>
      </div>
      <div class="search-item" v-show="activeName == '2'">
        <span>时间范围</span>
        <el-date-picker v-model="timeRange" type="datetimerange" range-separator="-" start-placeholder="开始日期"
          end-placeholder="结束日期" value-format="yyyy-MM-dd HH:mm:ss">
        </el-date-picker>
      </div>
      <el-button v-show="activeName != '3'" class="search-item" type="primary" @click="handleDraw"
        :disabled="!sensorData">绘制</el-button>
      <el-button v-show="activeName == '3'" class="search-item" type="primary"
        :disabled="!sensorData || noDataForChart3" @click="handleDraw">{{ stopFlag ? '停止' : '开始' }}</el-button>
      <!-- <el-button v-show="activeName == '2'" class="search-item" type="primary" :disabled="!sensorData"
        @click="downloadData" :loading="btnLoading">数据下载</el-button> -->
    </div>

    <div class="main-tabs" ref="mainTabs">
      <el-tabs v-model="activeName" tab-position="top" @tab-click="tabClick" type="border-card">
        <el-tab-pane label="实时数据" name="1">
        </el-tab-pane>
        <el-tab-pane label="历史数据" name="2">
        </el-tab-pane>
        <el-tab-pane label="实时数据(推送)" name="3">
        </el-tab-pane>
        <el-tab-pane label="结构物信息" name="4">
        </el-tab-pane>
      </el-tabs>
      <div class="main-tabs-charts" v-show="activeName != '4'">
        <RealtimeCharts v-show="activeName == '1'" :activeName="activeName" ref="chart1" :theme="theme"
          key="RealtimeCharts" @setDisabled="searchDisable = true" />
        <RealtimeCharts v-show="activeName == '2'" :activeName="activeName" ref="chart2" :theme="theme"
          key="HistoryCharts" @changeGranularity="changeGranularity" @setDisabled="searchDisable = true" />
        <ChartThree v-if="sensorData" v-show="activeName == '3'" :activeName="activeName" ref="chart3" :theme="theme"
          @noData="noData" />
        <div class="empty-msg" v-if="!sensorData">请先选择传感器</div>
      </div>
      <div class="main-tabs-text" :class="centerClass" v-show="activeName == '4'" v-html="displayHtmlText"></div>

    </div>

  </div>
</template>

<script>
import RealtimeCharts from './components/ChartOneAndTwo.vue'
import ChartThree from './components/ChartThree.vue'
import { fetchGet } from './api.js'

export default {
  name: 'Charts',
  props: {
    // 主题
    theme: {
      type: String,
      default: 'dark'
    },
    // 选中tree的传感器列表
    sensorData: {
      default: null,
    },
    structureDisplayHtmlText: {
      default: null,
    },
    dataForSKWY: {
			type: Array,
			default: () => {
				return []
			}
		},
  },
  components: { RealtimeCharts, ChartThree },
  data() {
    return {
      searchForm: {
        sensorId: '',
        specificMonitorContentIds: [],
        isPreprocess: false,
        isShowLimit: false,
        granularityNum: 1,
        granularityType: { index: 5, label: '天' },
        formatter: "{yyyy}-{MM}-{dd}",
        formatterNum: 12,
        granularityTypeName: '日',
      },
      activeName: '1',
      timeRange: [],
      chartBoxHeight: '100%',
      sensorOptions: [],
      specificMonitorTypeList: [],
      loading: false,
      chartFlag1: false,
      chartFlag2: false,
      chartFlag3: false,
      historySearchStr: '',
      searchDisable: false,
      stopFlag: false,
      noDataForChart3: false,
      centerClass: '',
      analysisType: { index: 2, label: '均值' },
      btnLoading: false,
    }
  },
  mounted() {
    this.init();
  },
  methods: {
    init() {
      // 初始化获取高度
      this.$nextTick(() => {
        this.setMainTabsHeight();
      });
      // 初始化时间范围
      let endTime = new Date();
      let startTime = new Date(endTime.getTime() - 86400000 * 7)
      this.timeRange.push(startTime.getFullYear() + '-' + (startTime.getMonth() + 1) + '-' + startTime.getDate() + ' ' +
        startTime.getHours() + ':' + startTime.getMinutes() + ':' + startTime.getSeconds())
      this.timeRange.push(endTime.getFullYear() + '-' + (endTime.getMonth() + 1) + '-' + endTime.getDate() + ' ' +
        endTime.getHours() + ':' + endTime.getMinutes() + ':' + endTime.getSeconds())

      // 监听窗口变化，重新渲染图表
      window.addEventListener('resize', () => {
        this.$nextTick(() => {
          this.setMainTabsHeight();
        });
      });

      // 监听 charts 元素的尺寸变化
      if (this.$refs.chartsRef) {
        new ResizeObserver(() => {
          this.$nextTick(() => {
            this.setMainTabsHeight();
            this.$refs['chart' + this.activeName]?.reLoad();
          });
        }).observe(this.$refs.chartsRef);
      }
    },
    // 动态设置 main-tabs 的高度
    setMainTabsHeight() {
      const searchBox = this.$el?.querySelector('.search-box');
      if (!searchBox) return;
      // 获取 search-box 的高度
      const searchBoxHeight = searchBox.offsetHeight;
      // 获取 search-box 的计算样式
      const searchBoxComputedStyle = window.getComputedStyle(searchBox);
      // 获取 search-box 的 margin-bottom 的值
      const searchBoxMarginBottom = searchBoxComputedStyle.getPropertyValue('margin-bottom');
      // 将 search-box 的 margin-bottom 的值转换为数字
      const searchBoxMarginBottomValue = parseFloat(searchBoxMarginBottom.replace('px', ''));

      // 设置 main-tabs 的高度
      if (this.$refs.mainTabs) {
        this.$refs.mainTabs.style.height = `calc(100% - ${searchBoxHeight + searchBoxMarginBottomValue}px)`;
      }
    },
    // 数据采样点击事件
    handleSelectBox(e) {
      this.searchForm.granularityTypeName = this.searchForm.granularityTypeName === e ? null : e;
      switch (e) {
        case '原始':
          this.searchForm.granularityNum = 1
          this.searchForm.granularityType = { index: 0, label: '原始' }
          this.searchForm.formatter = "{yyyy}-{MM}-{dd}\n{hh}:{mm}:{ss}";
          this.searchForm.formatterNum = 4;
          break;
        case '分':
          this.searchForm.granularityNum = 1
          this.searchForm.granularityType = { index: 3, label: '分' }
          this.searchForm.formatter = "{yyyy}-{MM}-{dd}\n{hh}:{mm}"
          this.searchForm.formatterNum = 7;
          break;
        case '10分':
          this.searchForm.granularityNum = 10
          this.searchForm.granularityType = { index: 3, label: '分' }
          this.searchForm.formatter = "{yyyy}-{MM}-{dd}\n{hh}:{mm}"
          this.searchForm.formatterNum = 7;
          break;
        case '小时':
          this.searchForm.granularityNum = 1
          this.searchForm.granularityType = { index: 4, label: '时' }
          this.searchForm.formatter = "{yyyy}-{MM}-{dd}\n{hh}:{mm}"
          this.searchForm.formatterNum = 7;
          break;
        case '日':
          this.searchForm.granularityNum = 1
          this.searchForm.granularityType = { index: 5, label: '天' }
          this.searchForm.formatter = "{yyyy}-{MM}-{dd}"
          this.searchForm.formatterNum = 12;
          break;
      }
      this.handleDraw()
    },
    changeGranularity(obj) {
      this.searchForm.formatter = obj?.formatter || this.searchForm.formatter
      this.searchForm.formatterNum = obj?.formatterNum || this.searchForm.formatterNum
      this.searchForm.granularityNum = obj?.granularityNum || this.searchForm.granularityNum
      this.searchForm.granularityType = obj?.granularityType || this.searchForm.granularityType
      this.searchForm.granularityTypeName = obj?.granularityTypeName || this.searchForm.granularityTypeName
      this.$forceUpdate()
    },
    // tab-click 事件
    tabClick() {
      // 确保能够实时更新高度
      this.$nextTick(() => {
        this.setMainTabsHeight();
        this.$refs['chart' + this.activeName]?.reLoad();
      });
    },
    async changeSensor() {
      window.$Bus.$emit('changeTreeNode', this.sensorOptions.find(item => item.sensorId === this.searchForm.sensorId))
      if (this.activeName == '3') {
        this.noDataForChart3 = false
        this.stopFlag = false
        this.chartFlag3 = false
        await this.$refs.chart3.stopDrawRealTimePushData()
        await this.$refs.chart3.disConnectRealTime()
      }
      setTimeout(async () => {
        this.handleDraw()
      }, 300)
    },
    async handleDraw() {
      if (!this.sensorData) return;
      if (this.activeName != '4') {
        this.loading = true
        // 告诉Tree组件，我在走接口的时候不允许你点击！
        window.$Bus.$emit('treeClick', false)
        const obj = this.sensorOptions.find(item => item.sensorId === this.searchForm.sensorId)
        switch (this.activeName) {
          case '1':
            try {
              let dataForSKWY = this.dataForSKWY.length > 0 ? this.dataForSKWY : null
							let vehicleArr = this.sensorOptions[0].isVehicle ? this.sensorOptions : null
							this.chartFlag1 = await this.$refs.chart1.drawRealTimeData(this.searchForm, obj, vehicleArr, dataForSKWY);
            } catch (error) {
              this.$message.error('数据获取失败')
            }
            this.loading = false
            window.$Bus.$emit('treeClick', true)
            break;
          case '2':
            try {
              let dataForSKWY = this.dataForSKWY.length > 0 ? this.dataForSKWY : null
							let vehicleArr = this.sensorOptions[0].isVehicle ? this.sensorOptions : null
							this.searchForm.timeRange = this.timeRange
							this.chartFlag2 = await this.$refs.chart2.drawHistoryData(this.searchForm, obj, vehicleArr, dataForSKWY);
            } catch (error) {
              this.$message.error('数据获取失败')
            }
            this.loading = false
            window.$Bus.$emit('treeClick', true)
            break;
          case '3':
            if (this.stopFlag) {
              await this.$refs.chart3.stopDrawRealTimePushData()
              this.stopFlag = false
            } else {
              if (this.chartFlag3) {
                await this.$refs.chart3.connectRealTime()
                await this.$refs.chart3.startSendData()
              } else {
                this.chartFlag3 = await this.$refs.chart3.initChart3(obj);
              }
              this.stopFlag = true
            }
            this.loading = false
            window.$Bus.$emit('treeClick', true)
            break;
        }
      }
    },
    async downloadData() {
      if (
        !this.searchForm.sensorId ||
        this.timeRange.length === 0
      ) {
        this.$message({
          type: "warning",
          message: "下载数据前请注意查询条件的完整性",
        });
        return;
      }
      this.btnLoading = true
      const obj = this.sensorOptions.find(item => item.sensorId === this.searchForm.sensorId)
      //这里放参数
      const params = {
        nodeCode: obj.code,
        sensorId: obj.sensorId,
        structureNodeCode: this.$route.query.code,
        startTime: this.timeRange[0],
        endTime: this.timeRange[1],
        granularityNum: this.searchForm.granularityNum,
        granularityType: this.searchForm.granularityType?.index,
        analysisType: this.analysisType.index,
        specificMonitorContentIds: this.searchForm.specificMonitorContentIds,
        dataType: 1,
        rawOrProcess: this.isPreprocess ? 1 : 0
      };
      const url = "https://jkjc.yciccloud.com:8000/xboot/data/download"
      const res = await fetchGet(url, params)
      if (res.code === 200) {
        this.$message({ type: 'success', message: res.message, duration: 2000 });
      }
      this.btnLoading = false
    },
    noData() {
      this.noDataForChart3 = true
      setTimeout(() => {
        this.stopFlag = false
      }, 300)
    }
  },
  computed: {
    displayHtmlText() {
      if (!this.structureDisplayHtmlText || this.structureDisplayHtmlText === "<p><br></p>") {
        this.centerClass = 'centerClass'
        const fontSize = this.isBig ? '28px' : '14px';
        return `<div style="text-align: center; font-size: ${fontSize};">没有结构物信息</div>`;
      } else {
        this.centerClass = ''
      }
      return this.structureDisplayHtmlText;
    }
  },
  watch: {
    // 监听tree节点点击返回的传感器数据
    'sensorData': {
      handler() {
        this.searchDisable = false
        this.chartFlag1 = false
        this.chartFlag2 = false
        this.chartFlag3 = false
        this.stopFlag = false
        this.noDataForChart3 = false
        this.searchForm.sensorId = ''
        this.searchForm.specificMonitorContentIds = []

        // “设备安装代码”option
        this.sensorOptions = this.sensorData
        if (this.sensorData[0]) {
          // “设备安装代码”默认第一个
          this.searchForm.sensorId = this.sensorData[0].sensorId;
        }
        setTimeout(async () => {
          if (this.activeName == '3') {
            await this.$refs.chart3.stopDrawRealTimePushData()
            await this.$refs.chart3.disConnectRealTime()
          }
          this.handleDraw()
        }, 300)
      },
      deep: true,
    },
    // 监听“设备安装代码”，改变“选择Y轴值类型和顺序”的option
    'searchForm.sensorId'(val) {
      if (!val) return;
      this.specificMonitorTypeList = []
      this.searchForm.specificMonitorContentIds = []
      this.sensorOptions?.forEach((item) => {
        if (item.sensorId === val) {
          const { specificMonitorTypeName, specificMonitorTypeId } = item;
          this.specificMonitorTypeList = specificMonitorTypeId.map((id, index) => ({
            specificMonitorTypeName: specificMonitorTypeName[index],
            specificMonitorTypeId: id,
          }))
        }
      })
    },
    theme() {
      this.handleDraw()
    },
    async activeName(e) {
      // chartFlag 为true时，说明已经绘制过了，再次点击tab时，不重新绘制
      if (!this['chartFlag' + e]) {
        this.handleDraw()
      }
      if (e != '3') {
        if (!this.chartFlag3 || this.noDataForChart3) return;
        await this.$refs.chart3.stopDrawRealTimePushData()
        this.stopFlag = false
      } else {
        if (!this.chartFlag3 || this.noDataForChart3) return;
        await this.$refs.chart3.connectRealTime()
        await this.$refs.chart3.startSendData()
        this.stopFlag = true
      }
    }
  },
  beforeDestroy() {
    window.removeEventListener('resize', this.setMainTabsHeight);
    const chartsRef = this.$refs.chartsRef;
    if (chartsRef) {
      const resizeObserver = new ResizeObserver(() => { });
      resizeObserver.unobserve(chartsRef);
    }
  },
}
</script>

<style lang="scss" scoped>
@import "@/assets/styles/utils.scss";

// 默认主题-浅色
.charts {
  width: 100%;
  height: 100%;
  transition: all 0.3s;

  // 搜索框
  .search-box {
    width: 100%;
    min-height: vwpx(120px);
    background: inherit;
    color: inherit;
    font-size: vwpx(30px);
    border-radius: vwpx(24px);
    margin-bottom: vwpx(20px);
    padding: 0 vwpx(40px);
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    background: #EFF5FF;
    transition: all 0.3s;

    span {
      margin-right: vwpx(40px);
    }

    // 公共样式
    .search-item {
      margin: vwpx(10px) vwpx(60px) vwpx(10px) 0;
      display: flex;
      justify-content: center;
      align-items: center;
    }

    // 数据采样
    .select-box {
      display: flex;
      justify-content: center;
      align-items: center;
      height: vwpx(70px);
      border: 1px solid #DCDFE6;
      background-color: #fff;
      border-radius: vwpx(12px);

      .select-item-active {
        background: #1890ff;
      }

      .select-item-disabled {
        color: #ccc;
        cursor: not-allowed;
      }

      div:first-child {
        border-left: 0 !important;
        border-radius: vwpx(12px) 0 0 vwpx(12px);
      }

      div:last-child {
        border-radius: 0 vwpx(12px) vwpx(12px) 0;
      }


      div {
        width: vwpx(130px);
        height: vwpx(70px);
        display: flex;
        justify-content: center;
        align-items: center;
        cursor: pointer;
        border-left: 1px solid #DCDFE6;
      }
    }

    // 复选框
    .check-box {
      width: vwpx(220px);
      height: vwpx(70px);
      border: 1px solid #DCDFE6;
      border-radius: vwpx(12px);
      display: flex;
      align-items: center;
      justify-content: center;
      background-color: #fff;
    }

    // 日期选择器样式调整
    ::v-deep {

      // 输入框
      .el-range-editor.el-input__inner {
        width: vwpx(800px) !important;
        height: vwpx(70px);
        padding: vwpx(6px) vwpx(20px);
      }

      // 输入框内文本的样式
      .el-range-input {
        font-size: vwpx(30px);
      }

      // 左右的图标
      .el-range__icon {
        font-size: vwpx(26px);
        height: 100%;
        width: vwpx(40px);
        margin: 0;
      }

      .el-icon-circle-close:before {
        font-size: vwpx(26px);
        line-height: vwpx(61px);
        margin-left: vwpx(10px);
      }

      // 文字颜色
      .el-range-input {
        color: #000;
      }
    }

    // 下拉选择器样式调整
    ::v-deep {

      // 输入框
      .el-input__inner {
        width: vwpx(400px);
        height: vwpx(70px);
        font-size: vwpx(30px);
        padding-right: vwpx(70px);
        padding-left: vwpx(30px);
      }

      // 下拉框
      .el-input__suffix {
        right: vwpx(5px);
      }

      // 下拉框的箭头
      .el-select .el-input .el-select__caret {
        font-size: vwpx(30px);
        line-height: vwpx(70px);
      }
    }

    // 按钮样式调整, 复选框样式调整
    ::v-deep {

      // 按钮
      .el-button {
        width: vwpx(200px);
        height: vwpx(68px);
        border-radius: vwpx(12px);
        font-size: vwpx(30px);
        padding: vwpx(14px) vwpx(20px);
      }

      // 复选框
      .el-checkbox__inner {
        width: vwpx(30px);
        height: vwpx(30px);
      }

      .el-checkbox {

        // 第一个span
        span:first-child {
          height: vwpx(30px) !important;
        }
      }

      // 自适应“勾勾”样式
      .el-checkbox__input.is-checked .el-checkbox__inner::after {
        width: vwpx(6px);
        height: vwpx(14px);
        top: vwpx(2px);
        left: vwpx(9px);
        border: solid white;
        border-width: 0 vwpx(3px) vwpx(3px) 0;
        transform: rotate(45deg);
      }

      .el-checkbox__label {
        font-size: vwpx(30px);
        padding-left: vwpx(12px);
        // padding-bottom: vwpx(4px);
      }

      .el-checkbox {
        height: vwpx(70px);
        display: flex;
        justify-content: center;
        align-items: center;
      }
    }
  }

  .main-tabs {
    width: 100%;
    
    .main-tabs-charts {
      overflow-y: auto;
    }

    .main-tabs-charts,
    .main-tabs-text {
      width: 100%;
      height: calc(100% - #{vwpx(80px)});
      border: 1px solid #dcdfe6;
      border-top: none !important;
      padding: vwpx(10px);
    }

    .main-tabs-text {
      overflow-y: auto;
      font-size: vwpx(33px);
      color: #606266;
      font-weight: 700;
    }

    .centerClass {
      display: flex;
      justify-content: center;
      align-items: center;
    }

    .empty-msg {
      width: 100%;
      height: 100%;
      display: flex;
      justify-content: center;
      align-items: center;
      font-size: vwpx(33px);
      color: #606266;
      font-weight: 700;
    }

    // tabs样式调整
    ::v-deep {
      .el-tabs--border-card {
        box-shadow: none !important;
        border-bottom: none !important;
      }

      .el-tabs__item {
        width: vwpx(270px);
        height: vwpx(80px);
        padding: 0 !important;
        display: flex;
        justify-content: center;
        align-items: center;
        font-size: vwpx(30px);
        color: #303133;
        transition: 0.3s;
        flex-shrink: 0;
      }

      .el-tabs__item.is-active {
        color: #1890ff !important;
      }

      .el-tabs__nav {
        display: flex;
        flex-wrap: nowrap;
      }

      .el-tabs__nav-wrap::after {
        display: none;
      }

      .el-tabs__active-bar {
        display: none;
      }

      // 这个继承 main-tabs 的高度
      .el-tabs__content {
        height: 0 !important;
        padding: 0;
      }

      .el-tabs__header {
        background: #EFF5FF;
      }

    }

    .charts {
      width: 100%;
      height: 100%;
    }
  }
}

::-webkit-scrollbar {
  width: vwpx(24px);
  height: vwpx(24px);
}

// 深色主题
.dark {
  ::-webkit-scrollbar-thumb {
    background-color: rgba(1, 102, 254, 0.8);
  }

  ::-webkit-scrollbar-track {
    background-color: rgba(0, 35, 94, .6);
  }

  .search-box {
    background: rgba(1, 102, 254, 0.3) !important;
    border: 0;
  }

  .select-box {
    border: 1px solid #0166FE !important;
    background-color: rgba(0, 25, 64, 0.5) !important;

    .select-item-active {
      background: #0166FE !important;
    }

    div {
      border-left: 1px solid #0166FE !important;
      color: #fff;
    }
  }

  .check-box {
    border: 1px solid #0166fe !important;
    background-color: rgba(0, 25, 64, 0.5) !important;
  }

  // .main-tabs-charts {
  //   background: rgba(0, 35, 94, 0.5) !important;
  // }

  .main-tabs-charts,
  .main-tabs-text {
    border: 1px solid #0166FE !important;
    border-top: none !important;
  }

  ::v-deep {
    .el-checkbox__inner {
      border: 1px solid #0687FF;
      background-color: rgba(0, 25, 64, 0.5);
    }

    .el-checkbox__input.is-checked .el-checkbox__inner {
      background-color: #1890ff !important;
    }

    .el-checkbox__label {
      color: #fff;
    }

    .el-checkbox__input.is-checked+.el-checkbox__label {
      color: #1890ff
    }

    .el-input__inner {
      background-color: rgba(0, 25, 64, 0.5);
      border: 1px solid #0166fe;
      color: #ffffff;
    }

    .el-tabs__item {
      color: #fff !important;
    }

    .el-tabs__item.is-active {
      color: #0166FE !important;
      background: rgba(0, 35, 94, 0.6);
      border-right-color: transparent;
      border-left-color: transparent;
      box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.3);
    }

    .el-tabs--border-card {
      background: rgba(1, 102, 254, 0.15) !important;
      border: 1px solid #0166fe;
      border-bottom: none !important;
    }

    .el-tabs__header {
      background: rgba(1, 102, 254, 0.15) !important;
      border: none !important;
    }

    .el-range-input {
      background: transparent !important;
      color: #fff !important;
    }
  }
}
</style>