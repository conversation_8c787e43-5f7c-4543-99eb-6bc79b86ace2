<template>
  <div style="height: 100%">
    <!-- 定义DOM元素，用于在该DOM元素中显示模型或图纸 -->
    <div class="domId" ref="domId" style="height: 100%"></div>
    <!-- <Space wrap>
      <Modal v-model="showModal" draggable scrollable :mask="false" title="实时数据" footer-hide>
        <div id="realTimeChart" style="height: 30vh; width: 28vw"></div>
      </Modal>
    </Space> -->
  </div>
</template>

<script>
import { fetchGet } from '../../utils/api.js';
import { isBigScreen } from '../../utils/utils.js';
// const getInfoByCode = (a) => {
//   return fetchGet('https://jkjc.yciccloud.com:8000/xboot/structureNormalDataManage/getByCode', a);
// };
const getCalibratedRealTimeData = () => { };
const getviewtoken = (a) => {
  return fetchGet('https://jkjc.yciccloud.com:8000/xboot/bridge/bim/getViewToken', a);
}
// const getComponentId = (a) => {
//   return fetchGet('https://jkjc.yciccloud.com:8000/xboot/sensorManage/getComponentIdBySensorId', a)
// }
const getComponentIdList = (a) => {

};
const getsensorIdbyStructidComponentid = (a) => {
  return fetchGet('https://jkjc.glyhgl.com:22586/bigScreen/structure/getByComponentIdAndStructureNode', a)
}
import {
  BimfaceSDKLoader,
  BimfaceSDKLoaderConfig,
  Glodon,
} from "bimfacesdkloader";
export default {
  name: "Bim",
  props: ["bridge", "bimToRealTime"],
  data() {
    return {
      showModal: false,
      chartData: [],
      structureCode: "",
      viewToken: "", // 这里替换成自己的
      viewer3D: "",
      app: "",
      drawableContainer: "",
      fileId: "",
      drawableList: [],
      leadLabelList: [],
      // 需要把曝光设置为0.4的桥梁 由于是甲方要求 这里只能写成静态数据
      structureIdList: [
        "1593261718891532288",
        "1593261714705616896",
        "1593261560137125888",
        "1593262505038319616",
        "1593262510889373696",
        "1593261702848319488",
        "1593261698842759168",
        "1593262445600837632",
        "1593262442039873536",
        "1593261834851454977",
        "1593261817197629440",
        "1593261822377594880",
        "1593262192235515904",
      ],
      // 是否需要拉曝光
      isExposure: false,
    };
  },
  activated() {
    if (this.$route.meta.refreshBim) {
      // 清除缓存
      console.log("BIM清除缓存");
      // 渲染数据
      console.log("BIM渲染数据");
    }
    // 恢复成默认的false
    this.$route.meta.refreshBim = false;
  },
  async mounted() {
    this.structureCode = this.bridge.code;
    // console.log("structureCode",this.structureCode)
    // console.log("bridge", this.bridge);
    if (this.structureIdList.includes(this.structureCode)) {
      this.isExposure = true;
    } else {
      this.isExposure = false;
    }
    this.fileId = this.bridge.fileId;
    // 清空list
    this.drawableList = [];
    // 获取viewtoken
    if (this.fileId) {
      await getviewtoken({
        fileId: this.fileId,
      }).then((res) => {
        if (res.code == 200) {
          this.viewToken = res.result;
          // this.viewToken = "3c473f079546424faa91ad52d0bf02f3";
          console.log("viewtoken", this.viewToken);
        } else {
          console.log("失败");
        }
      });
      // ↓↓↓↓ 初始化显示组件
      let options = new BimfaceSDKLoaderConfig();
      options.viewToken = this.viewToken;
      BimfaceSDKLoader.load(
        options,
        this.successCallback,
        this.failureCallback
      );
      // ↑↑↑↑ 初始化显示组件

      window.$Bus.$on("singleBridgeBimShowLable", (infoList) => {
        if (infoList.length != 0) {
          // 先清除所有选项
          // if (this.drawableList.length != 0) {
          //   this.clearAllLable();
          // }
          let sensorIdParams = "";
          let nodeCodeList = [];
          infoList.forEach((element) => {
            sensorIdParams += String(element[3]) + ",";
            nodeCodeList.push(element[2]);
          });
          // 删掉末尾的逗号
          sensorIdParams = sensorIdParams.slice(0, -1);
          getComponentIdList({
            sensorId: sensorIdParams,
          }).then((res) => {
            if (res.code == 200) {
              // console.log("res", res);
              let componentIdList = res.result;
              // 用that代替this 因为在function traverseArrayWithDelay中的this不再是VC
              let that = this;
              // 定义一个异步函数来遍历数组
              async function traverseArrayWithDelay(array, delay = 500) {
                for (let index = 0; index < array.length; index++) {
                  // 等待一段时间
                  await new Promise((resolve) => setTimeout(resolve, delay));
                  // 这里添加对数组元素的处理逻辑
                  // console.log(array[index]);
                  that.drawLeadLabel(
                    componentIdList[index].componentId,
                    array[index].sensorInstallCode,
                    array[index].sensorId,
                    nodeCodeList[index]
                  );
                }
              }
              // 调用函数
              traverseArrayWithDelay(componentIdList);
            } else {
              this.clearAllLable();
            }
          });
          // infoList.forEach((item) => {
          //   // console.log("singleBridgeBimShowLable返回数据", data, nodeCode);
          //   let sensorId = item[3];
          //   let nodeCode = item[2];
          //   if (this.drawable) {
          //     this.drawable.clear();
          //   }
          //   if (sensorId) {
          //     //获取componentId
          //     var componentId;
          //     getComponentId({
          //       sensorId: sensorId,
          //     }).then((res) => {
          //       if (res.code == 200) {
          //         console.log("res",res)
          //         componentId = res.result.componentId;
          //         // 添加标签
          //         // this.creatLable(componentId, res.result.sensorInstallCode);
          //         this.drawLeadLabel(
          //           componentId,
          //           res.result.sensorInstallCode,
          //           sensorId,
          //           nodeCode
          //         );
          //       }
          //     });
          //   }
          // });
        } else {
          this.clearAllLable();
        }
      });
    } else {
      // console.log("没有BIM")
      // this.$bus.emit("hasBim", false);
    }
  },
  methods: {
    async getRealData(sensorId, nodeCode) {
      const { success, result } = await getCalibratedRealTimeData({
        nodeCode: nodeCode,
        sensorId: sensorId,
        structureNodeCode: this.bridge.code,
      });
      if (success) {
        this.chartData = result;
        this.initChart(this.chartData, this.chartData[0].times);
      }
    },
    initChart(data, xAxisData) {
      // console.log("data", data);
      var chartDom = document.getElementById("realTimeChart");
      var myChart = echarts.init(chartDom);
      // 生成 legend 和 series
      let legendData = [];
      let seriesData = [];
      // 计算上下限
      let allValues = [];
      data.forEach((item) => {
        legendData.push(item.name);
        seriesData.push({
          name: item.name,
          type: "line",
          data: item.values,
        });
        allValues = allValues.concat(item.values);
      });

      // 计算所有values的最小值和最大值
      let minValue = Math.min(...allValues);
      let maxValue = Math.max(...allValues);

      // 设置y轴的上下限，使数据处于中间位置
      let yAxisMin = Math.floor(minValue - (maxValue - minValue) * 0.2); // 下限减少20%
      let yAxisMax = Math.ceil(maxValue + (maxValue - minValue) * 0.2); // 上限增加20%

      var option = {
        tooltip: {
          trigger: "axis",
        },
        legend: {
          data: legendData,
        },
        grid: {
          top: "8%",
          left: "0%",
          right: "4%",
          bottom: "0%",
          containLabel: true,
        },
        xAxis: {
          type: "category",
          boundaryGap: false,
          data: xAxisData,
        },
        yAxis: {
          type: "value",
          min: yAxisMin,
          max: yAxisMax,
        },
        series: seriesData,
      };

      //notMerge参数默认为false，设置成true，就不会合并options 造成二次数据残留
      myChart.setOption(option, true);

      window.addEventListener("resize", function () {
        myChart.resize();
      });
    },
    // 清除自定义标签
    clearLable() {
      // console.log("清除构建", this.drawable);
      this.drawable.clear();
      // this.drawableContainer.clear();
    },
    // 清除所有标签
    clearAllLable() {
      this.drawableList.forEach((item) => {
        // console.log("item", item);
        item.clear();
      });
    },
    // 绘制引线标签
    drawLeadLabel(componentId, data, sensorId, nodeCode) {
      let that = this;
      this.drawableConfig =
        new Glodon.Bimface.Plugins.Drawable.DrawableContainerConfig();
      this.drawableConfig.viewer = this.viewer3D;
      this.drawableConfig.maxNum = 10;
      this.drawable = new Glodon.Bimface.Plugins.Drawable.DrawableContainer(
        this.drawableConfig
      );
      // 把每一个标签存入drawableList
      this.drawableList.push(this.drawable);

      this.viewer3D
        .getModel()
        .getComponentProperty(componentId, function (obj) {
          let o = that.viewer3D.getBoundingBoxById(componentId);
          //引线标签的配置类
          let config = new Glodon.Bimface.Plugins.Drawable.LeadLabelConfig();

          // config.height = 50;
          config.width = 160;
          let text = data;

          // console.log("data:", data);

          config.text = text;
          //引线标签关联的构件
          config.objectId = componentId;

          //引线标签的世界坐标

          let position = {
            x: (o.min.x + o.max.x) / 2,
            y: (o.min.y + o.max.y) / 2,
            z: (o.min.z + o.max.z) / 2,
          };
          config.worldPosition = position;

          //引线标签是否可拖拽
          config.draggable = false;
          //引线标签的视图
          config.viewer = that.viewer3D;

          let label = new Glodon.Bimface.Plugins.Drawable.LeadLabel(config);

          label.onClick(function (item) {
            if (that.nodeCode != "" && that.sensorId != "") {
              that.showModal = true;
              that.getRealData(sensorId, nodeCode);
            }
          });
          that.drawable.addItem(label);
        });
    },
    // 添加自定义标签
    creatLable(componentId, data) {
      let that = this;
      let position = new Object();
      that.viewer3D
        .getModel()
        .getComponentProperty(componentId, function (objectdata) {
          console.log("objectdata", objectdata);
          if (that.viewer3D.getBoundingBoxById(componentId).min)
            position = that.viewer3D.getBoundingBoxById(componentId).min;
          else position = objectdata.boundingBox.min;
          // 初始化DrawableContainer
          let drawableConfig =
            new Glodon.Bimface.Plugins.Drawable.DrawableContainerConfig();
          drawableConfig.viewer = that.viewer3D;
          that.drawableContainer =
            new Glodon.Bimface.Plugins.Drawable.DrawableContainer(
              drawableConfig
            );
          // 创建自定义元素，可以是一个dom element，也可以是个字符串
          let config = new Glodon.Bimface.Plugins.Drawable.CustomItemConfig();
          let content = document.createElement("div");
          content.innerHTML =
            '<div class="leadTips"><div><img src="https://static.bimface.com/attach/24ce9654e88a4218908f46279e5c4b04_line.png" height="35" width="49"/></div><div class="leadText" id="canvasDiv" >' +
            data +
            "</div></div>";
          config.content = content;
          config.viewer = that.viewer3D;
          config.worldPosition = position;

          //生成customItem实例
          let customItem = new Glodon.Bimface.Plugins.Drawable.CustomItem(
            config
          );
          // 添加自定义标签
          that.drawableContainer.addItem(customItem);
        });
    },
    // 加载成功回调函数
    successCallback(viewMetaData) {
      var that = this;
      var dom4Show = this.$refs.domId;
      // 设置WebApplication3D的配置项
      var webAppConfig =
        new Glodon.Bimface.Application.WebApplication3DConfig();
      webAppConfig.domElement = dom4Show;
      // 设置viewhouse不可见
      webAppConfig.enableViewHouse = false;
      webAppConfig.enableLogarithmicDepthBuffer = true;
      // 设置全局单位
      webAppConfig.globalUnit =
        Glodon.Bimface.Common.Units.LengthUnits.Millimeter;
      // 隐藏菜单栏
      webAppConfig.Toolbars = ["ModelTree"];

      // 创建WebApplication3D，用以显示模型
      var app = new Glodon.Bimface.Application.WebApplication3D(webAppConfig);
      app.addView(viewMetaData.viewToken);
      var viewer3D = app.getViewer();

      // 甲方要求 有些桥需要设置默认曝光
      if (this.isExposure) {
        viewer3D.setExposureShift(0.4);
      }

      // 设置背景颜色
      let monochrome = new Glodon.Web.Graphics.Color(0, 0, 0, 0);
      viewer3D.setBackgroundColor(monochrome);
      // 增加加载完成监听事件
      viewer3D.addEventListener(
        Glodon.Bimface.Viewer.Viewer3DEvent.ViewAdded,
        function () {
          that.viewAdded = true;
          //自适应屏幕大小
          window.onresize = function () {
            viewer3D.resize(
              document.documentElement.clientWidth,
              document.documentElement.clientHeight - 40
            );
          };
          // 渲染场景
          viewer3D.addEventListener(
            Glodon.Bimface.Viewer.Viewer3DEvent.MouseClicked,
            function (objectdata) {
              // console.log(objectdata);
              // that.componentclick(objectdata.elementId);
            }
          );

          viewer3D.render();
          setTimeout(() => {
            viewer3D.setBackgroundColor(
              new Glodon.Web.Graphics.Color(0, 0, 0, 0)
            );
          }, 1000);
          that.viewer3D = viewer3D;
        }
      );
    },
    // 加载失败回调函数
    failureCallback(error) {
      console.log(error);
    },
    //构件点击事件
    componentclick(elementId) {
      if (!elementId) {
        // alert("请选择传感器")
        return;
      }
      getsensorIdbyStructidComponentid({
        StructureNode: this.structureCode,
        ComponentId: elementId,
      }).then((res) => {
        if (res.code === 200) {
          if (res.result) {
            this.drawLeadLabel(elementId, res?.result[0]?.sensorInstallCode);
          }
          // this.bimToRealTime(res.result);
        }
      });
    },
  },
  beforeDestroy() {
    window.$Bus.$off("singleBridgeBimShowLable")
  }
};
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style scoped>
.domId {
  position: relative;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 10;
}
</style>

<style>
.leadTips {
  display: flex;
  justify-content: flex-start;
  width: 230px;
  transform: translateY(-40px);
}

.leadTips img {
  display: inline-block;
  width: 49px;
  height: 40px;
}

.leadText {
  width: auto;
  display: inline-block;
  background: #4a90e2;
  color: #fff;
  padding: 10px;
}
</style>