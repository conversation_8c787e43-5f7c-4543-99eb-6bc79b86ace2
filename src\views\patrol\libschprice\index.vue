<template>
  <div class="app-container">
    <el-row :gutter="20">

      <!--筛选区开始-->
      <el-col :span="24" :xs="24">
        <el-row>
          <el-col :span="24" >
            <el-form :model="queryParams" ref="queryForm" size="small" :inline="true"  label-width="68px">

                          <el-form-item label="" prop="rootId">
                            <el-input
                                v-model="queryParams.rootId"
                                placeholder="请输入当前节点id"
                                clearable
                                prefix-icon="el-icon-user"
                                style="width: 240px"
                                @keyup.enter.native="handleQuery"
                            />
                          </el-form-item>
                          <el-form-item label="" prop="schemeCode">
                            <el-input
                                v-model="queryParams.schemeCode"
                                placeholder="请输入子目号"
                                clearable
                                prefix-icon="el-icon-user"
                                style="width: 240px"
                                @keyup.enter.native="handleQuery"
                            />
                          </el-form-item>
                          <el-form-item label="" prop="unit">
                            <el-input
                                v-model="queryParams.unit"
                                placeholder="请输入单位"
                                clearable
                                prefix-icon="el-icon-user"
                                style="width: 240px"
                                @keyup.enter.native="handleQuery"
                            />
                          </el-form-item>
                          <el-form-item label="" prop="price">
                            <el-input
                                v-model="queryParams.price"
                                placeholder="请输入单价"
                                clearable
                                prefix-icon="el-icon-user"
                                style="width: 240px"
                                @keyup.enter.native="handleQuery"
                            />
                          </el-form-item>
                          <el-form-item label="" prop="guaranteePeriod">
                            <el-input
                                v-model="queryParams.guaranteePeriod"
                                placeholder="请输入质保期"
                                clearable
                                prefix-icon="el-icon-user"
                                style="width: 240px"
                                @keyup.enter.native="handleQuery"
                            />
                          </el-form-item>
                          <el-form-item label="" prop="decimalPlaces">
                            <el-input
                                v-model="queryParams.decimalPlaces"
                                placeholder="请输入保留位数"
                                clearable
                                prefix-icon="el-icon-user"
                                style="width: 240px"
                                @keyup.enter.native="handleQuery"
                            />
                          </el-form-item>
                          <el-form-item label="" prop="safetyFeeFlag">
                            <el-input
                                v-model="queryParams.safetyFeeFlag"
                                placeholder="请输入是否计算安全费"
                                clearable
                                prefix-icon="el-icon-user"
                                style="width: 240px"
                                @keyup.enter.native="handleQuery"
                            />
                          </el-form-item>
                          <el-form-item label="" prop="rateFlag">
                            <el-input
                                v-model="queryParams.rateFlag"
                                placeholder="请输入是否是费率章节"
                                clearable
                                prefix-icon="el-icon-user"
                                style="width: 240px"
                                @keyup.enter.native="handleQuery"
                            />
                          </el-form-item>
              <el-form-item>
                <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
                <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
                <el-button v-show="!showSearch" @click="showSearch=true" icon="el-icon-arrow-down" circle></el-button>
                <el-button  v-show="showSearch" @click="showSearch=false"  icon="el-icon-arrow-up" circle></el-button>
              </el-form-item>
            </el-form>
            <!--默认折叠-->
          </el-col>

          <!--默认折叠 ,此处仅作为示例-->
          <el-col :span="24" >
            <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">

              <el-form-item label="" prop="status">
                <el-select
                    v-model="queryParams.status"
                    placeholder="示例状态"
                    clearable
                    style="width: 240px"
                >
                  <el-option
                      v-for="dict in dictType"
                      :key="dict.value"
                      :label="dict.label"
                      :value="dict.value"
                  />
                </el-select>
              </el-form-item>
              <el-form-item label="" prop="status">
                <el-select
                    v-model="queryParams.status"
                    placeholder="示例状态"
                    clearable
                    style="width: 240px"
                >
                  <el-option
                      v-for="dict in dictType"
                      :key="dict.value"
                      :label="dict.label"
                      :value="dict.value"
                  />
                </el-select>
              </el-form-item>

            </el-form>
          </el-col>
        </el-row>
        <!--筛选区结束-->


        <!--操作按钮区开始-->
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button
                type="primary"
                icon="el-icon-plus"
                size="mini"
                @click="handleAdd"
                v-hasPermi="['patrol:libschprice:add']"
            >新增</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button
                type="success"
                icon="el-icon-edit"
                size="mini"
                :disabled="single"
                @click="handleUpdate"
                v-hasPermi="['patrol:libschprice:edit']"
            >修改</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button
                type="danger"
                icon="el-icon-delete"
                size="mini"
                :disabled="multiple"
                @click="handleDelete"
                v-hasPermi="['patrol:libschprice:remove']"
            >删除</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button
                type="info"
                plain
                icon="el-icon-upload2"
                size="mini"
                @click="handleImport"
                v-hasPermi="['patrol:libschprice:export']"
            >导入</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button
                type="warning"
                icon="el-icon-download"
                size="mini"
                @click="handleExport"
                v-hasPermi="['system:user:export']"
            >导出</el-button>
          </el-col>
          <right-toolbar :showSearch.sync="showSearch" @queryTable="getList" :columns="columns"></right-toolbar>
        </el-row>
        <!--操作按钮区结束-->

        <!--数据表格开始-->
        <div class="tableDiv">
          <el-table  size="mini" :height="showSearch ? 'calc(100vh - 320px)' : 'calc(100vh - 260px)'"   style="width: 100%" v-loading="loading" border :data="libschpriceList" @selection-change="handleSelectionChange">
            <el-table-column type="selection" width="50" align="center" />
            <el-table-column fixed label="序号" type="index" width="50">
              <template v-slot="scope">
                {{ (scope.$index + (queryParams.pageNum - 1) * queryParams.pageSize)+1 }}
              </template>
            </el-table-column>
                    <el-table-column label="当前节点id" align="center" prop="rootId" />
                    <el-table-column label="子目号" align="center" prop="schemeCode" />
                    <el-table-column label="子目名称" align="center" prop="schemeName" />
                    <el-table-column label="单位" align="center" prop="unit" />
                    <el-table-column label="单价" align="center" prop="price" />
                    <el-table-column label="质保期" align="center" prop="guaranteePeriod" />
                    <el-table-column label="方案描述" align="center" prop="describe" />
                    <el-table-column label="保留位数" align="center" prop="decimalPlaces" />
                    <el-table-column label="是否计算安全费" align="center" prop="safetyFeeFlag" />
                    <el-table-column label="是否是费率章节" align="center" prop="rateFlag" />
                    <el-table-column label="备注" align="center" prop="remark" />
                    <el-table-column label="状态" align="center" prop="status" />
            <el-table-column
                label="操作"
                fixed="right"
                align="center"
                width="160"
                class-name="small-padding fixed-width"
            >
              <template slot-scope="scope" v-if="scope.row.userId !== 1">
                <el-button
                    size="mini"
                    type="text"
                    icon="el-icon-edit"
                    @click="handleUpdate(scope.row)"
                    v-hasPermi="['patrol:libschprice:edit']"
                >修改</el-button>
                <el-button
                    size="mini"
                    type="text"
                    icon="el-icon-delete"
                    @click="handleDelete(scope.row)"
                    v-hasPermi="['patrol:libschprice:remove']"
                >删除</el-button>
              </template>
            </el-table-column>
          </el-table>

          <pagination
              v-show="total>0"
              :total="total"
              :page.sync="queryParams.pageNum"
              :limit.sync="queryParams.pageSize"
              @pagination="getList"
          />
        </div>
        <!--数据表格结束-->
      </el-col>
    </el-row>

    <!-- 添加或修改报价体系子目数据对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="600px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">

                      <el-col :span="12">
                        <el-form-item label="当前节点id" prop="rootId">
                          <el-input v-model="form.rootId" placeholder="请输入当前节点id" />
                        </el-form-item>
                      </el-col>


                      <el-col :span="12">
                        <el-form-item label="子目号" prop="schemeCode">
                          <el-input v-model="form.schemeCode" placeholder="请输入子目号" />
                        </el-form-item>
                      </el-col>


                      <el-col :span="12">
                        <el-form-item label="子目名称" prop="schemeName">
                          <el-input v-model="form.schemeName" type="textarea" placeholder="请输入内容" />
                        </el-form-item>
                      </el-col>


                      <el-col :span="12">
                        <el-form-item label="单位" prop="unit">
                          <el-input v-model="form.unit" placeholder="请输入单位" />
                        </el-form-item>
                      </el-col>


                      <el-col :span="12">
                        <el-form-item label="单价" prop="price">
                          <el-input v-model="form.price" placeholder="请输入单价" />
                        </el-form-item>
                      </el-col>


                      <el-col :span="12">
                        <el-form-item label="质保期" prop="guaranteePeriod">
                          <el-input v-model="form.guaranteePeriod" placeholder="请输入质保期" />
                        </el-form-item>
                      </el-col>


                      <el-col :span="12">
                        <el-form-item label="方案描述" prop="describe">
                          <el-input v-model="form.describe" type="textarea" placeholder="请输入内容" />
                        </el-form-item>
                      </el-col>


                      <el-col :span="12">
                        <el-form-item label="保留位数" prop="decimalPlaces">
                          <el-input v-model="form.decimalPlaces" placeholder="请输入保留位数" />
                        </el-form-item>
                      </el-col>


                      <el-col :span="12">
                        <el-form-item label="是否计算安全费" prop="safetyFeeFlag">
                          <el-input v-model="form.safetyFeeFlag" placeholder="请输入是否计算安全费" />
                        </el-form-item>
                      </el-col>


                      <el-col :span="12">
                        <el-form-item label="是否是费率章节" prop="rateFlag">
                          <el-input v-model="form.rateFlag" placeholder="请输入是否是费率章节" />
                        </el-form-item>
                      </el-col>


                      <el-col :span="12">
                        <el-form-item label="备注" prop="remark">
                          <el-input v-model="form.remark" type="textarea" placeholder="请输入内容" />
                        </el-form-item>
                      </el-col>




                      <el-col :span="12">
                        <el-form-item label="删除标志" prop="delFlag">
                          <el-input v-model="form.delFlag" placeholder="请输入删除标志" />
                        </el-form-item>
                      </el-col>



      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 用户导入对话框 -->
    <el-dialog :title="upload.title" :visible.sync="upload.open" width="400px" append-to-body>
      <el-upload
          ref="upload"
          :limit="1"
          accept=".xlsx, .xls"
          :headers="upload.headers"
          :action="upload.url + '?updateSupport=' + upload.updateSupport"
          :disabled="upload.isUploading"
          :on-progress="handleFileUploadProgress"
          :on-success="handleFileSuccess"
          :auto-upload="false"
          drag
      >
        <i class="el-icon-upload"></i>
        <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
        <div class="el-upload__tip text-center" slot="tip">
          <div class="el-upload__tip" slot="tip">
            <el-checkbox v-model="upload.updateSupport" /> 是否更新已经存在的用户数据
          </div>
          <span>仅允许导入xls、xlsx格式文件。</span>
          <el-link type="primary" :underline="false" style="font-size:12px;vertical-align: baseline;" @click="importTemplate">下载模板</el-link>
        </div>
      </el-upload>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitFileForm">确 定</el-button>
        <el-button @click="upload.open = false">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
  import { listLibschprice, getLibschprice, delLibschprice, addLibschprice, updateLibschprice } from "@/api/patrol/libschprice";
  import { getToken } from "@/utils/auth";
  import Treeselect from "@riophae/vue-treeselect";
  import "@riophae/vue-treeselect/dist/vue-treeselect.css";

  export default {
    name: "Libschprice",
    components: {  },
    data() {
      return {
        // 遮罩层
        loading: true,
        // 选中数组
        ids: [],
        // 非单个禁用
        single: true,
        // 非多个禁用
        multiple: true,
        // 显示搜索条件
        showSearch: false,
        dictType:[],
        // 总条数
        total: 0,
        // 报价体系子目数据表格数据
        libschpriceList: null,
        // 弹出层标题
        title: "",
        // 部门树选项
        deptOptions: undefined,
        // 是否显示弹出层
        open: false,

        // 表单参数
        form: {},
        defaultProps: {
          children: "children",
          label: "label"
        },
        // 用户导入参数
        upload: {
          // 是否显示弹出层（用户导入）
          open: false,
          // 弹出层标题（用户导入）
          title: "",
          // 是否禁用上传
          isUploading: false,
          // 是否更新已经存在的用户数据
          updateSupport: 0,
          // 设置上传的请求头部
          headers: { Authorization: "Bearer " + getToken() },
          // 上传的地址
          url: process.env.VUE_APP_BASE_API + "/system/user/importData"
        },
        // 查询参数
        queryParams: {
          pageNum: 1,
          pageSize: 50,
            rootId: null,
            schemeCode: null,
            schemeName: null,
            unit: null,
            price: null,
            guaranteePeriod: null,
            describe: null,
            decimalPlaces: null,
            safetyFeeFlag: null,
            rateFlag: null,
            status: null,
        },
        // 列信息
        columns: [
        { key: 0, label: `当前节点id`, visible: true },
        { key: 1, label: `子目号`, visible: true },
        { key: 2, label: `子目名称`, visible: true },
        { key: 3, label: `单位`, visible: true },
        { key: 4, label: `单价`, visible: true },
        { key: 5, label: `质保期`, visible: true },
        { key: 6, label: `方案描述`, visible: true },
        { key: 7, label: `保留位数`, visible: true },
        { key: 8, label: `是否计算安全费`, visible: true },
        { key: 9, label: `是否是费率章节`, visible: true },
        { key: 10, label: `备注`, visible: true },
        { key: 11, label: `状态（0-正常，1-停用）`, visible: true },
        { key: 12, label: `删除标志（0代表存在，1代表删除）`, visible: true }
        ],
        // 表单校验
        rules: {
    rootId: [
        { required: true, message: "当前节点id不能为空", trigger: "blur" }
    ],
    schemeCode: [
        { required: true, message: "子目号不能为空", trigger: "blur" }
    ],
    unit: [
        { required: true, message: "单位不能为空", trigger: "blur" }
    ],
    price: [
        { required: true, message: "单价不能为空", trigger: "blur" }
    ],
    guaranteePeriod: [
        { required: true, message: "质保期不能为空", trigger: "blur" }
    ],
    decimalPlaces: [
        { required: true, message: "保留位数不能为空", trigger: "blur" }
    ],
    safetyFeeFlag: [
        { required: true, message: "是否计算安全费不能为空", trigger: "blur" }
    ],
    rateFlag: [
        { required: true, message: "是否是费率章节不能为空", trigger: "blur" }
    ],


        }
      };
    },
    watch: {
      // 根据名称筛选部门树
                      },
    created() {
      this.getList();
      // this.getDeptTree();
      // this.getConfigKey("sys.user.initPassword").then(response => {
      //   this.initPassword = response.msg;
      // });
    },
    methods: {
      /** 查询用户列表 */
      getList() {
        this.loading = true;
        listLibschprice(this.queryParams).then(response => {
          this.libschpriceList = response.rows;
          this.total = response.total;
          this.loading = false;
        });
      },
      // 取消按钮
      cancel() {
        this.open = false;
        this.reset();
      },
      // 表单重置
      reset() {
        this.form = {
            rootId: null,
            schemeCode: null,
            schemeName: null,
            unit: null,
            price: null,
            guaranteePeriod: null,
            describe: null,
            decimalPlaces: null,
            safetyFeeFlag: null,
            rateFlag: null,
            remark: null,
            status: null,
            delFlag: null
        };
        this.resetForm("form");
      },
      /** 搜索按钮操作 */
      handleQuery() {
        this.queryParams.pageNum = 1;
        this.getList();
      },
      /** 重置按钮操作 */
      resetQuery() {
        this.dateRange = [];
        this.resetForm("queryForm");
        this.handleQuery();
      },
      // 多选框选中数据
      handleSelectionChange(selection) {
        this.ids = selection.map(item => item.id);
        this.single = selection.length != 1;
        this.multiple = !selection.length;
      },
      /** 新增按钮操作 */
      handleAdd() {
        this.reset();
        this.open = true;
        this.title = "添加报价体系子目数据";
      },
      /** 修改按钮操作 */
      handleUpdate(row) {
        this.reset();
        const id = row.id || this.ids;
        getLibschprice(id).then(response => {
          this.form = response.data;
          this.open = true;
          this.title = "修改报价体系子目数据";
        });

      },
      /** 提交按钮 */
      submitForm: function() {
        this.$refs["form"].validate(valid => {
          if (valid) {
            if (this.form.id != null) {
              updateLibschprice(this.form).then(response => {
                this.$modal.msgSuccess("修改成功");
                this.open = false;
                this.getList();
              });
            } else {
              addLibschprice(this.form).then(response => {
                this.$modal.msgSuccess("新增成功");
                this.open = false;
                this.getList();
              });
            }
          }
        });
      },
      /** 删除按钮操作 */
      handleDelete(row) {
        const id = row.id || this.ids;
        this.$modal.confirm('是否确认删除报价体系子目数据编号为"' + id + '"的数据项？').then(function() {
          return delLibschprice(id);
        }).then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        }).catch(() => {});
      },

      /** 导出按钮操作 */
      handleExport() {

    this.download('patrol/libschprice/export', {
      ...this.queryParams
    }, `libschprice_${new Date().getTime()}.xlsx`)

      },
      /** 导入按钮操作 */
      handleImport() {
        this.upload.title = "用户导入";
        this.upload.open = true;
      },
      /** 下载模板操作 */
      importTemplate() {
        this.download('system/user/importTemplate', {
        }, `user_template.xlsx`)
      },
      // 文件上传中处理
      handleFileUploadProgress(event, file, fileList) {
        this.upload.isUploading = true;
      },
      // 文件上传成功处理
      handleFileSuccess(response, file, fileList) {
        this.upload.open = false;
        this.upload.isUploading = false;
        this.$refs.upload.clearFiles();
        this.$alert("<div style='overflow: auto;overflow-x: hidden;max-height: 70vh;padding: 10px 20px 0;'>" + response.msg + "</div>", "导入结果", { dangerouslyUseHTMLString: true });
        this.getList();
      },
      // 提交上传文件
      submitFileForm() {
        this.$refs.upload.submit();
      }
  }
  };
</script>
<style>
  .hasTagsView .app-main[data-v-078753dd]{
    background: #f5f7fa;
  }

  .tableDiv{
    background-color: white;
    padding-bottom: 10px;
  }
</style>
