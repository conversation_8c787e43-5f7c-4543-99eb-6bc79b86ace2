<template>
  <div class="construction-progress">
    <section class="status-list">
      <div class="list-box">
        <span>任务单</span>
        <div class="box-item">
          <div v-for="(item, index) in list.slice(0, 2)" :key="'f-' + index" class="list-item" :class="index ===0 ? 'border-r':''">
            <div class="list-item-name">{{ item.name }}</div>
            <div class="list-item-value">{{ item.value }}</div>
          </div>
        </div>
      </div>
      <div class="divider"></div>
      <div class="list-box">
        <span>签证单</span>
        <div class="box-item">
          <div v-for="(item, index) in list.slice(2, 4)" :key="'l-' + index" class="list-item" :class="index ===0 ? 'border-r':''">
            <div class="list-item-name">{{ item.name }}</div>
            <div class="list-item-value">{{ item.value }}</div>
          </div>
        </div>
      </div>
    </section>

    <section class="construction-order" v-loading="loading" element-loading-background="rgba(0, 0, 0, 0.4)"
      :style="{height: isBig ? 'calc(48vh)' : 'calc(50vh - 150px)'}">
      <Echarts :option="option" v-if="option" height="100%" />
    </section>
  </div>
</template>

<script>
import Echarts from '../../echarts/echarts.vue';
import { isBigScreen } from "@/views/cockpit/util/utils";
import { getConstructionStatistics } from "@/api/cockpit/maintain";

export default {
  components: {
    Echarts,
  },
  props: {
    year: {
      type: [String, Number],
      default: ''
    },
  },
  data() {
    return {
      isBig: isBigScreen(),
      list: [
        {
          name: '施工中',
          value: 9,
        },
        {
          name: '已完工',
          value: 60,
        },
        {
          name: '待验收',
          value: 5,
        },
        {
          name: '已结算',
          value: 8,
        },
      ],
      option: null,
      loading: false,
    }
  },
  created() {},
  watch: {
    year: {
      handler(val) {
        this.getData(val);
      },
      immediate: true,
      deep: true
    }
  },
  methods: {
    async getData(val) {
      this.loading = true;
      let year = val || this.year || new Date().getFullYear();
      getConstructionStatistics(year).then(res=>{
        if(res.code === 200) {
          let data = res.data;
          // 施工中
          this.list[0].value = data.beUnderConstructionCount;
          // 已完工
          this.list[1].value = data.completedCount;
          // 待验收
          this.list[2].value = data.toBeAccepted;
          // 已结算
          this.list[3].value = data.alreadySettledCount;
          this.option = this.initCharts(data);
        }
      }).finally(() => {
        this.loading = false;
      });
    },
    initCharts(data) {
      const chartPieColors = [
        [
          {
            offset: 0,
            color: "#128CFF" // 0% 处的颜色
          },
          {
            offset: 1,
            color: "#128CFF" // 100% 处的颜色
          }
        ],
        [
          {
            offset: 0,
            color: "#01EAFF" // 0% 处的颜色
          },
          {
            offset: 1,
            color: "#01EAFF" // 100% 处的颜色
          }
        ],
        [
          {
            offset: 0,
            color: "#EE9433" // 0% 处的颜色
          },
          {
            offset: 1,
            color: "#EE9433" // 100% 处的颜色
          }
        ],
        [
          {
            offset: 0,
            color: "#FEEA00" // 0% 处的颜色
          },
          {
            offset: 1,
            color: "#FEEA00" // 100% 处的颜色
          }
        ],
      ]
      let dataList = [
        {
          name: "施工任务单",
          value: data ? data.conCount : 22602
        },
        {
          name: "检测任务单",
          value: data ? data.checkCount : 15224
        },
        {
          name: "设计任务单",
          value: data ? data.designCount : 22602
        },
        {
          name: "监理任务单",
          value: data ? data.supCount : 15224
        }
      ];
      let seriesData = dataList.map((item, index) => {
        return {
          value: item.value,
          name: item.name,
          itemStyle: {
            borderWidth: 8,
            borderColor: "transparent",
            color: {
              type: "linear",
              x: 0,
              y: 0,
              x2: 0,
              y2: 1,
              colorStops: chartPieColors[index > 8 ? Math.floor(Math.random() * 8 + 1) : index],
              global: false // 缺省为 false
            }
          }
        };
      });
      const totalValue = dataList.reduce((sum, item) => sum + item.value, 0);
      let total = totalValue + totalValue / 3;
      // 添加一个透明项，确保它位于饼图的最下方
      // 由于饼图是按照数据顺序绘制的，将透明项添加到数组开头

      seriesData[seriesData.length] = {
        value: total / 3,
        name: '',
        itemStyle: {
          color: "rgba(255, 255, 255, 0)",
          borderWidth: 0  // 移除边框
        },
        label: {
          show: false
        },
        labelLine: {
          show: false
        }
      }
      let valPadding = this.isBig ? [120, 0, 0, -180] : [60, 0, 0, -100]
      let totalCount = data ? data.totalCount : 37826
      let option = {
        backgroundColor: 'rgba(0, 0, 0, 0)',
        title: [
          {
            text: `{total|${totalCount}}{unit|个}`,
            textAlign: 'center',
            left: this.isBig ? '30%' : '25%',
            top: this.isBig ? '36%' : '26%',
            padding: [24, 0],
            textStyle: {
              color: '#fff',
              fontSize: this.isBig ? 48 : 18,
              align: 'center',
              rich: {
                total: {
                  color: '#fff',
                  fontSize: this.isBig ? 82 : 36,
                  align: 'center',
                  fontWeight: '700',
                },
                unit: {
                  color: 'rgba(255,255,255,0.8)',
                  fontSize: this.isBig ? 48 : 18,
                  align: 'center',
                  padding: [10, 0, 0, 10],
                }
              }
            }
          },
          {
            text: '施工单',
            textAlign: 'center',
            left: this.isBig ? '30%' : '25%',
            top: this.isBig ? '52%' : '42%',
            padding: [24, 0],
            textStyle: {
              color: '#fff',
              fontSize: this.isBig ? 46 : 18,
              align: 'center' 
            }
          }
        ],
        legend: {
          top: this.isBig ? '35%' : '25%',
          align: 'left',
          right: '3%',
          itemWidth: this.isBig ? 30 : 15,
          itemHeight: this.isBig ? 30 : 15,
          width: '45%',
          textStyle: {
            fontSize: this.isBig ? 28 : 14,
            rich: {
              title: {
                color: '#FFFFFF',
                fontSize: this.isBig ? 32 : 16,
                fontWeight: 500,
                fontFamily: 'Source Han Sans, Source Han Sans',
                padding: [0, 10, 0, 10]
              },
              value0: {
                color: '#128CFF',
                fontSize: this.isBig ? 52 : 26,
                fontWeight: 700,
                fontFamily: 'Source Han Sans, Source Han Sans',
                padding: valPadding
              },
              value1: {
                color: '#01EAFF',
                fontSize: this.isBig ? 52 : 26,
                fontWeight: 700,
                fontFamily: 'Source Han Sans, Source Han Sans',
                padding: valPadding
              },
              value2: {
                color: '#EE9433',
                fontSize: this.isBig ? 52 : 26,
                fontWeight: 700,
                fontFamily: 'Source Han Sans, Source Han Sans',
                padding: valPadding
              },
              value3: {
                color: '#FEEA00',
                fontSize: this.isBig ? 52 : 26,
                fontWeight: 700,
                fontFamily: 'Source Han Sans, Source Han Sans',
                padding: valPadding
              }
            }
          },
          // 设置为2行2列排列
          orient: 'horizontal',
          itemGap: this.isBig ? 60 : 30,
          data: ['施工任务单', '检测任务单', '设计任务单', '监理任务单'],
          formatter: (name) => {
            // 获取图例对应的value值
            let index = seriesData.findIndex(item => item.name === name);
            if (index !== -1) {
              // 格式化图例
              return `{title|${name}}` + ` {value${index}|${seriesData[index].value}}`;
            }
            return `{title|${name}}`;
          },
        },
        series: [
          {
            name: '施工单',
            type: 'pie',
            radius: ['80%', '90%'],
            center: this.isBig ? ['30%', '50%'] : ['25%', '50%'],
            startAngle: 214,
            endAngle: 300,
            avoidLabelOverlap: false,
            label: {
              show: false
            },
            data: seriesData
          }
        ]
      };
      return option;
    }
  }
}
</script>

<style lang="scss" scoped>
@import "@/assets/styles/utils.scss";

.construction-progress {
  width: 100%;
  height: 100%;
  padding: 30px vwpx(80px);

  .status-list {
    width: 100%;
    height: vwpx(230px);
    background: rgba(0, 0, 0, 0.1);
    box-shadow: inset 0px 0px 10px 0px #0065FF;
    border-radius: 10px;
    border: 1px solid #20A9FF;

    display: flex;
    align-items: center;
    justify-content: space-between;

    .divider {
      width: vwpx(2px);
      height: 100%;
      background: rgba(32, 169, 255, 0.6);
    }

    .list-box {
      width: 100%;
      height: 100%;
      display: flex;
      align-items: center;
      flex-direction: column;
      flex: 1;

      span {
        font-family: Source Han Sans, Source Han Sans;
        font-weight: 600;
        font-size: vwpx(38px);
        color: #FFFFFF;
        padding-top: vwpx(20px);
      }

      .box-item {
        width: 100%;
        flex: 1;
        display: flex;
        align-items: center;

        .border-r {
          border-right: vwpx(2px) dotted rgba(156, 189, 255, 0.5);
        }

        .list-item {
          flex: 1;
          display: flex;
          align-items: center;
          justify-content: center;
          flex-direction: column;

          .list-item-name {
            font-family: Source Han Sans, Source Han Sans;
            font-weight: 500;
            font-size: vwpx(36px);
            color: #FFFFFF;
          }

          .list-item-value {
            font-family: HarmonyOS Sans SC, HarmonyOS Sans SC;
            font-weight: 500;
            font-size: vwpx(56px);
            color: #F2AF4A;
            margin-top: vwpx(10px);
          }
        }
      }
    }
  }

  .construction-order {
    width: 100%;
    margin-top: vwpx(100px);
  }

}
</style>