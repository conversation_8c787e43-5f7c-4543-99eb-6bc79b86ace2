<template>
  <div class="summary-container">
    <!-- 查询条件区域 -->
    <div class="query-container">

      <el-form ref="queryForm" size="small" :inline="true" label-width="68px">

        <el-form-item label="">
          <selectTree
            :key="'domainId'"
            v-model="queryForm.maintenanceUnitId"
            :deptType="101"
            :deptTypeList="[3]"
            clearable
            @change="deptChange(queryForm.maintenanceUnitId)"
            filterable
            placeholder="请选择管理处"
            style="width: 240px"
          />

        </el-form-item>

        <el-form-item label="">
          <el-select
            clearable
            v-model="queryForm.maintenanceSectionId"
            filterable
            placeholder="请选养护路段"
            style="width: 240px"
          >
            <el-option
              v-for="item in maintenanceSectionList"
              :key="item.maintenanceSectionId"
              :label="item.maintenanceSectionName"
              :value="item.maintenanceSectionId"
            ></el-option>
          </el-select>
        </el-form-item>


        <el-form-item label="">
          <el-date-picker
            value-format="yyyy-MM-dd"
            v-model="queryForm.patrolTime"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            :picker-options="pickerOptions"
          />
        </el-form-item>

        <el-form-item>
          <el-button icon="el-icon-search" type="primary" @click="handleSearch">搜索</el-button>
          <el-button icon="el-icon-refresh" @click="resetQuery">重置</el-button>
        </el-form-item>

      </el-form>
    </div>
    <el-row :gutter="10" style="margin-bottom: 8px" >
      <el-col :span="1.5">
        <el-button type="primary" icon="el-icon-download" size="mini" @click="handleExport"> 导出清单 </el-button>
      </el-col>
    </el-row>
    <!-- 表格区域 -->
    <div class="table-container">
      <el-table v-adjust-table :data="tableData" style="width: 100%" height="calc(100% - 50px)" v-loading="loading"
                highlight-current-row
                ref="table"
                @selection-change="handleSelectionChange"
                @row-click="handleRowClick"

      >
        <el-table-column type="selection" width="50" align="center"/>
        <el-table-column fixed label="序号" type="index" width="50">
          <template v-slot="scope">
            {{ (scope.$index + (queryForm.pageNum - 1) * queryForm.pageSize) + 1 }}
          </template>
        </el-table-column>
        <el-table-column min-width="120" prop="maintenanceSectionName" label="养护路段"/>
        <el-table-column min-width="120" prop="maintenanceUnitName" label="管理处"/>
        <el-table-column min-width="120" prop="startTime" label="巡查日期"/>
        <el-table-column min-width="120" prop="count" label="巡查次数"/>
        <el-table-column min-width="120" prop="patrolMileage" label="巡查总里程(公里)"
                         :formatter="(...arg)=>{if(arg[2]) return (arg[2]/1000).toFixed(3).toLocaleString()}"/>
        <el-table-column min-width="180" prop="bridgeRecordNum" label="桥梁巡查记录(异常/总数)"/>
        <el-table-column min-width="180" prop="tunnelRecordNum" label="隧道巡查记录(异常/总数)"/>
        <el-table-column min-width="120" prop="diseaseNum" label="巡查病害数量"/>
<!--        <el-table-column min-width="220" prop="" label="报表">-->
<!--          <template slot-scope="scope">-->
<!--            <el-button type="primary" @click.stop="handleReport(scope.row, 0)">综合</el-button>-->
<!--            <el-button type="primary" @click.stop="handleReport(scope.row, 1)">桥梁</el-button>-->
<!--            <el-button type="primary" @click.stop="handleReport(scope.row, 2)">隧道</el-button>-->
<!--          </template>-->
<!--        </el-table-column>-->
      </el-table>
      <pagination
        v-show="total > 0"
        :total="total"
        :page.sync="queryForm.pageNum"
        :limit.sync="queryForm.pageSize"
        @pagination="getList"
      />
    </div>
  </div>
</template>

<script>
import CascadeSelection from '@/components/CascadeSelection/index.vue'
import selectTree from "@/components/DeptTmpl/selectTree.vue";
import {listMaintenanceSectionAll} from "@/api/system/maintenanceSection";
import {getTreeStruct} from "@/api/tmpl";
import {countByMaintenanceUnit, countByPatrolUnit} from "@/api/patrol/inspectionLogs";

export default {
  components: {
    selectTree,
    CascadeSelection,
  },
  data() {
    return {
      maintenanceSectionList: [],
      queryForm: {
        pageNum: 1,
        pageSize: 10,
        maintenanceSectionId: null,
        maintenanceUnitId: null,
        routeCodes: null,
        patrolTime: null,
      },
      loading: false,
      tableData: [],
      total: 0,
      current: null,
      pickerOptions: {
        shortcuts: [
          {
            text: '最近一周',
            onClick(picker) {
              const end = new Date()
              const start = new Date()
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 7)
              picker.$emit('pick', [start, end])
            },
          },
          {
            text: '最近一个月',
            onClick(picker) {
              const end = new Date()
              const start = new Date()
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 30)
              picker.$emit('pick', [start, end])
            },
          },
          {
            text: '最近三个月',
            onClick(picker) {
              const end = new Date()
              const start = new Date()
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 90)
              picker.$emit('pick', [start, end])
            },
          },
        ],
      },
    }
  },
  created() {
    this.getList()
    this.getMaintenanceSection()

  },
  methods: {
    handleSearch() {
      this.getList()
    },
    async handleExport() {

      if (this.current) {
        this.$confirm(`是否确认导出当前选中数据？`, '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        })
          .then(() => {
            const now = new Date();
            const timeStr = `${now.getFullYear()}年${
              now.getMonth() + 1
            }月${now.getDate()}日${now.getHours()}时${now.getMinutes()}分${now.getSeconds()}秒`;
            const fileName = `管理处巡维统计_${timeStr}.xlsx`;
            this.download(
              'patrol/inspectionLogs/exportCount?type=1',
              {
                maintenanceSectionName: this.current.maintenanceSectionName,
                // maintenanceUnitName: this.current.maintenanceSubUnitName,
                patrolUnitName: this.current.patrolUnitName,
                startTime: this.current.startTime + " 00:00:00",
                endTime: this.current.startTime + " 23:59:59",
                idList: this.current.ids.split(','),
                patrolTime: null},
              fileName,
              {
                parameterType: 'body', // 设置参数类型为 body
                headers: {
                  'Content-Type': 'application/json', // 设置请求头为 JSON
                },
              }
            );
          })
          .catch(() => {
            this.$message({
              type: 'info',
              message: '已取消导出',
            });
          });
        return;
      }

      let result = await countByMaintenanceUnit({...this.queryForm, patrolTime: null, pageNum: 1, pageSize: 1000000})
      this.$confirm(`是否确认导出当前${result.total}数据？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(() => {
          const now = new Date();
          const timeStr = `${now.getFullYear()}年${
            now.getMonth() + 1
          }月${now.getDate()}日${now.getHours()}时${now.getMinutes()}分${now.getSeconds()}秒`;
          const fileName = `管理处巡维统计_${timeStr}.xlsx`;

          this.download(
            'patrol/inspectionLogs/exportCount?type=1',
            {...this.queryForm, patrolTime: null},
            fileName,
            {
              parameterType: 'body', // 设置参数类型为 body
              headers: {
                'Content-Type': 'application/json', // 设置请求头为 JSON
              },
            }
          );
        })
        .catch(() => {
          this.$message({
            type: 'info',
            message: '已取消导出',
          });
        });
      return;
    },
    handleReport(row, type) {
      console.log('报表', row, type)

      const now = new Date(); // 添加 now 的定义
      const timeStr = `${now.getFullYear()}年${
        now.getMonth() + 1
      }月${now.getDate()}日${now.getHours()}时${now.getMinutes()}分${now.getSeconds()}秒`;
      const fileName = `巡维统计_${timeStr}.xlsx`;
      this.download(
        'patrol/inspectionLogs/exportCountCard' + "?type=" + type,
        {
          ...row,
        },
        fileName,
        {
          parameterType: 'body', // 设置参数类型为 body
          headers: {
            'Content-Type': 'application/json', // 设置请求头为 JSON
          },
        }
      );
    },

    /** 重置按钮操作 */
    resetQuery() {
      this.queryForm = {
        pageNum: 1,
        pageSize: 10,
        maintenanceSectionId: null,
        maintenanceUnitId: null,
        routeCodes: null,
        patrolTime: null,
      }
      this.getList();
    },
    getList() {

      if (this.queryForm.patrolTime) {
        this.queryForm.startTime = this.queryForm.patrolTime[0];
        this.queryForm.endTime = this.queryForm.patrolTime[1];
      }


      this.loading = true
      countByMaintenanceUnit({...this.queryForm, patrolTime: null}).then(res => {
        this.tableData = res.rows
        this.total = res.total
        this.loading = false
      })
      console.log('获取列表', this.queryForm)
    },
    /** 查询养护路段下拉列表 */
    getMaintenanceSection() {
      listMaintenanceSectionAll().then((res) => {
        this.maintenanceSectionList = res.data;
      });
    },
    //管理处下拉选点击事件
    deptChange(departmentId) {
      listMaintenanceSectionAll({departmentId: departmentId}).then((res) => {
        this.maintenanceSectionList = res.data;
      });
    },

    // 多选框选中数据
    handleSelectionChange(selection) {
      if (selection.length == 1)
      {
        this.current = selection[0];
        this.$emit("current-change", {...this.current, queryForm: this.queryForm})
      }

      if (selection.length > 1) {
        this.$refs.table.clearSelection()

        this.$refs.table.toggleRowSelection(selection[1]);
      }
      // this.$refs.table.toggleRowSelection(this.current[0]);
      // this.single = selection.length != 1;
      // this.multiple = !selection.length;
    },
    // 表格点击勾选
    handleRowClick(row) {
      this.$refs.table.toggleRowSelection(row);
    },
  },
}
</script>

<style lang="scss" scoped>
.summary-container {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.table-container {
  flex: 1;
  overflow: hidden;

  .el-table {
    height: calc(100% - 50px);
  }
}

 .el-table__body tr.current-row > ::v-deep td.el-table__cell {
  background: #F3DFDF ;
}
 .el-table__body tr.current-row > ::v-deep td.el-table__cell,
 .el-table__body tr.selection-row > ::v-deep  td.el-table__cell {
  background-color: #F3DFDF;
}
::v-deep .el-table__body tr.current-row > td.el-table__cell {
  background: #b7daff;
}
</style>
