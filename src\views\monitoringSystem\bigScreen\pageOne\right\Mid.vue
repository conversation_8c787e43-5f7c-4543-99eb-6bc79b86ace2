<template>
  <Cards :title="title" w="100%" h="27vh" :isDtl="false">
    <Echarts v-if="option" :option="option" :height="'100%'" :key="optionKey" />
  </Cards>
</template>

<script>
import ItemCard from '../../components/itemCard.vue'
import Echarts from "../../components/echarts.vue"
import Cards from "../../components/cards.vue"
import { get } from '../../../utils/request.js'
import { isBigScreen } from '../../../utils/utils.js';
import { fetchGet } from '../../../utils/api.js'

export default {
  name: 'Mid',
  props: {
    title: {
      type: String,
      default: '设备综合指标'
    }
  },
  inject: ['iThis'],
  components: { ItemCard, Echarts, Cards },
  data() {
    return {
      isBig: isBigScreen(),
      isScroll: true,
      rolltimer: null,
      dataList: [],
      option: null,
      optionKey: 'optionKey'
    }
  },
  created() { },
  async mounted() {
    this.dataList = await this.__init()
    if (!this.dataList || this.dataList.length == 0) {
      this.option = {
        title: {
          text: '没有数据',
          left: 'center',
          top: 'center',
          textStyle: {
            fontSize: this.isBig ? 32 : 14,
            color: '#606266',
            fontWeight: 700
          },
        },
      };
      this.optionKey = new Date().getTime();
      return
    }
    this.getOption()
    // 添加窗口大小变化的监听器
    window.addEventListener('resize', this.handleWindowResize);
  },
  destroyed() { },
  methods: {
    async __init() {
      const url = 'https://jkjc.yciccloud.com:8000/xboot/data/getDataInfo';
      try {
        const res = await fetchGet(url, { nodeCode: this.iThis.params.code })
        if (res.code === 200) {
          return res.result
        } else return []
      } catch (error) {
        return []
      }
      // return new Promise((resolve, reject) => {
      //   get(`/bigScreen/structure/getDataInfo`, { params: { nodeCode: this.iThis.params.code } }).then(res => {
      //     if (res.code === 200 && res.data) {
      //       resolve(res.data.result)
      //     } else {
      //       reject(res.msg)
      //     }
      //   }).catch((err) => {
      //     reject(err)
      //   })
      // })
    },
    getOption() {
      let color = ['#01FBEF', '#F0AE4B', '#0154FB', '#009DFF'];
      let chartData = [
        {
          name: "设备在线率",
          value: 0,
          unit: ''
        },
        {
          name: "数据完整率",
          value: 0,
          unit: ''
        },
        {
          name: "传感器报警",
          value: 0,
          unit: ''
        },
      ];
      // 4-15汇报版本，龙江大桥数据写死
      if (this.iThis.params.code === '1593262192235515904') {
        console.log(123123)
        this.dataList.map(el => {
          if (el.disPlayName === '设备在线率') {
            el.disPlayNumber = 1
          }
          if (el.disPlayName === '数据完整率') {
            el.disPlayNumber = 1
          }
          if (el.disPlayName === '传感器报警') {
            el.disPlayNumber = 0
          }
        })
      }
      chartData = this.dataList.map(v => {

        return {
          name: v.name || v.disPlayName,
          value: v.value || v.disPlayNumber || 0,
          unit: v.unit || '',
        }
      })
      let arrName = [];
      let arrValue = [];
      let sum = 0;
      let pieSeries = [], lineYAxis = [];
      // 数据处理
      chartData.forEach((v, i) => {
        arrName.push(v.name);
        arrValue.push(v.value);
        sum = sum + v.value;
      })
      if (sum <= 0) {
        sum = 1;
      }
      let center = ["60%", "50%"]
      // 图表option整理
      chartData.forEach((v, i) => {
        let value = v.value * 0.75
        pieSeries.push({
          name: '设备综合指标',
          type: 'pie',
          clockWise: false,
          hoverAnimation: false,
          radius: [75 - i * 15 + '%', 67 - i * 15 + '%'],
          center,
          label: {
            show: false
          },
          data: [{
            value: value,
            name: v.name,
            itemStyle: {
              borderWidth: 30, //圆柱图宽度
              borderRadius: 30, //光环宽度
              borderDashOffset: 20,
            }
          }, {
            value: 1 - value,
            name: '',
            itemStyle: {
              color: "rgba(0,0,0,0)",
            }
          }],
        });
        pieSeries.push({
          name: '',
          type: 'pie',
          silent: true,
          z: 1,
          clockWise: false, //顺时加载
          hoverAnimation: false, //鼠标移入变大
          radius: [75 - i * 15 + '%', 67 - i * 15 + '%'],
          center,
          label: {
            show: false
          },
          data: [{
            value: 7.5,
            itemStyle: {
              color: "#083165",
              borderWidth: 30, //圆柱图宽度
              borderRadius: 30, //光环宽度
              borderDashOffset: 20,
            }
          }, {
            value: 2.5,
            name: '',
            itemStyle: {
              color: "rgba(0,0,0,0)",
              borderWidth: 30, //圆柱图宽度
              borderRadius: 30, //光环宽度
              borderDashOffset: 20,
            }
          }]
        });
        // v.percent = (v.value / sum * 100).toFixed(0) + "%";
        v.percent = (v.value * 100).toFixed(1);
        if (parseInt(v.percent) < 10) {
          v.percent = '  ' + v.percent;
        }
        v.percent += "%";
        lineYAxis.push({
          value: i,
          textStyle: {
            rich: {
              circle: {
                color: color[i],
                padding: [0, 5]
              }
            }
          }
        });
      })
      let lineWidth = window.innerWidth * 0.035;
      let percentSize = 18
      if (window.innerWidth <= 1366) {
        percentSize = 14
      } else if (window.innerWidth <= 1600) {
        percentSize = 16
      } else if (window.innerWidth <= 1920) {
        percentSize = 18
      }

      this.option = {
        backgroundColor: 'rgba(255, 255, 255,0)',
        color: color,
        grid: {
          top: '9%',
          bottom: '66%',
          left: "42%",
          containLabel: false
        },
        yAxis: [{
          position: 'right',
          offset: 0,
          type: 'category',
          inverse: true,
          axisLine: {
            show: false
          },
          axisTick: {
            show: false
          },
          axisLabel: {
            formatter: (params) => {
              let item = chartData[params];
              return '{line|}{percent|' + item.percent + '}'
            },
            interval: 0,
            inside: true,
            textStyle: {
              rich: {
                line: {
                  width: this.isBig ? 180 : lineWidth,
                  height: 1,
                  backgroundColor: "rgba(156, 189, 255, 0.30)",
                },
                percent: {
                  color: '#fff',
                  fontSize: this.isBig ? 36 : percentSize,
                  fontWeight: 700,
                  // padding: this.isBig ? [0, 0, 0, 20] : [0, 0, 0, 10],
                },
              }
            },
            show: true
          },
          data: lineYAxis
        }],
        xAxis: [{
          show: false
        }],
        series: pieSeries,
        legend: {
          show: true,
          top: "6%",
          left: "5%",
          itemGap: this.isBig ? 10 : 5,
          itemWidth: this.isBig ? 26 : 13,
          itemHeight: this.isBig ? 26 : 13,
          orient: 'horizontal',
          textStyle: {
            color: "#fff",
            fontSize: this.isBig ? 28 : 14,
            fontWeight: 400,
            padding: [0, 0, 0, window.innerHeight * 0.002]
          },
        }
      };
    },
    handleWindowResize() {
      // 当窗口大小变化时，重新生成图表配置
      this.getOption();
      // 更新 optionKey 以触发 Echarts 组件重新渲染
      this.optionKey = new Date().getTime();
    }
  },
  computed: {},
  watch: {},
  destroyed() {
    // 移除窗口大小变化的监听器，防止内存泄漏
    window.removeEventListener('resize', this.handleWindowResize);
  },
}
</script>

<style lang="scss" scoped>
@import "@/assets/styles/utils.scss";

.box {
  padding-top: 2.6vh;
  height: 100%;
  width: 100%;
  font-size: vwpx(32px);
  color: #eeeeee;
  line-height: 2vh;
}

.alertcontainer {
  padding-top: 1vh;
  position: absolute;
  top: 8%;
  left: 4%;
  height: 88%;
  width: 93%;
  /* 允许垂直滚动 */
  overflow-y: auto;
  /* Firefox 不显示滚动条 */
  scrollbar-width: none;
  /* Internet Explorer and Edge 不显示滚动条 */
  -ms-overflow-style: none;

  .alertItem {
    height: 20%;
    width: 100%;
    display: flex;
    margin-bottom: 0.55vh;
  }

  .alertIcon {
    width: 5%;
    height: 100%;
    font-size: vwpx(36px);
    text-shadow: #000 1px 1px 1px;
    color: red;
    padding-left: 1%;
  }

  .alertText {
    width: 95%;
    height: 100%;
    font-size: small;
    color: #eee;
    line-height: 1.7vh;
    font-size: vwpx(32px);
  }

}
</style>