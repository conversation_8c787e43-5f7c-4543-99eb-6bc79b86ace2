<template>
  <div class="box">
    <Cards w="100%" h="57vh" :isDtl="false" :isHeader="false">
      <transition name="el-zoom-in-top">
        <div class="category" ref="category" v-show="!isSpread">
          <div class="list" v-for="(item, index) in categoryList" :key="'cate' + index">
            <img :src="item.icon" alt="" />
            <div class="text">{{ item.name }}</div>
            <div class="number">{{ item.number }}</div>
          </div>
          <div class="spread-packup" @click="handleSpread">
            <img src="@/assets/map/spread.png" class="animated img" @dragstart.prevent @contextmenu.prevent />
          </div>
        </div>
      </transition>
      <div class="spread-packup2" @click="handleSpread" v-show="isSpread">
        <img src="@/assets/map/spread.png" class="animated spread-img" @dragstart.prevent @contextmenu.prevent />
      </div>
      <section class="bim-box" v-show="structureData.fileId && activeBreadcrumb === 'BIM模型'">
        <bim :bridge="structureData" />
      </section>
      <section v-show="activeBreadcrumb === '全景'">
        <div style="width: 100%; height:57vh">
          <iframe :src="structureData.panoramic720view" width="100%" height="100%" frameborder="0" allowfullscreen>
          </iframe>
        </div>
      </section>
      <div style="width: 100%; height:100%;" v-show="activeBreadcrumb === '视频' && videoInfo.length > 0">
        <el-tabs class="original" tab-position="top" v-model="videoName" @tab-click="tabClick">
          <el-tab-pane v-for="(item, index) in videoInfo" :key="index" :label="item.label"
            :name="item.label"></el-tab-pane>
        </el-tabs>
        <div class="video-box" v-if="videoUrl">
          <newVideo width="100%" height="100%" :url="videoUrl"></newVideo>
        </div>
      </div>
      <section v-show="activeBreadcrumb === '图片'">
        <div style="width: 100%; height:57vh; display: flex; justify-content: center; align-items: center;">
          <el-carousel trigger="click" style="width: 100%; height: 100%;">
            <el-carousel-item v-for="(item, index) in structureImages" :key="index" style="height: 57vh;">
              <el-image :src="item" :preview-src-list="srcList" style="width: 100%; height: 100%;"
                fit="fill"></el-image>
            </el-carousel-item>
          </el-carousel>
        </div>
      </section>
      <section class="breadcrumb">
        <el-breadcrumb separator-class="el-icon-arrow-right">
          <el-breadcrumb-item v-if="structureData.fileId">
            <span class="breadcrumb-text" :style="{ fontWeight: activeBreadcrumb === 'BIM模型' ? '900' : 'normal' }"
              @click="setActiveBreadcrumb('BIM模型')">
              BIM模型
            </span>
          </el-breadcrumb-item>
          <el-breadcrumb-item v-if="structureData.panoramic720view">
            <span class="breadcrumb-text" :style="{ fontWeight: activeBreadcrumb === '全景' ? '900' : 'normal' }"
              @click="setActiveBreadcrumb('全景')">
              全景
            </span>
          </el-breadcrumb-item>
          <el-breadcrumb-item v-if="videoInfo.length > 0">
            <span class="breadcrumb-text" :style="{ fontWeight: activeBreadcrumb === '视频' ? '900' : 'normal' }"
              @click="setActiveBreadcrumb('视频')">
              视频
            </span>
          </el-breadcrumb-item>
          <el-breadcrumb-item>
            <span class="breadcrumb-text" :style="{ fontWeight: activeBreadcrumb === '图片' ? '900' : 'normal' }"
              @click="setActiveBreadcrumb('图片')">
              图片
            </span>
          </el-breadcrumb-item>
        </el-breadcrumb>
      </section>
    </Cards>
  </div>
</template>

<script>
import Cards from "../../components/cards.vue"
import { fetchGet } from '../../../utils/api.js'
import newVideo from '../../../realTime/components/Video/newVideo.vue'
import bim from '../../components/bim.vue'

export default {
  name: 'Top',
  inject: ['iThis'],
  props: {},
  components: { Cards, newVideo, bim },
  data() {
    return {
      categoryList: [
        {
          icon: require('@/assets/monitoringSystem/displacement-icon.png'),
          name: '位移',
          number: 30,
        },
        {
          icon: require('@/assets/monitoringSystem/displacement-icon.png'),
          name: '振动',
          number: 29,
        },
        {
          icon: require('@/assets/monitoringSystem/displacement-icon.png'),
          name: '温湿度',
          number: 32,
        },
        {
          icon: require('@/assets/monitoringSystem/displacement-icon.png'),
          name: '应变',
          number: 68,
        }
      ],
      structureData: {},
      activeBreadcrumb: 'BIM模型',
      structureImages: ["https://jkjc.yciccloud.com:8002/ynjt-structure-file/7c28243f509445fb86fafaa6a0d0fccd-治租河.jpeg"],
      srcList: ["https://jkjc.yciccloud.com:8002/ynjt-structure-file/7c28243f509445fb86fafaa6a0d0fccd-治租河.jpeg",],
      videoInfo: [],
      videoName: "",
      videoUrl: "",
      listName: [],
      timer: null,
      isSpread: false
    }
  },
  async created() {
    this.structureData = this.iThis.structureData
    this.structureImages = [this.iThis.structureData.structureImage]
    this.srcList = [this.iThis.structureData.structureImage]
    const info = await this.getVideoList()
    if (info) {
      this.videoInfo = info.children
      this.videoName = info.children[0].label
      this.tabClick()
    }
    if (this.structureData.code === "1593262524063682560" || this.structureData.code === "1593262527993745408") {
      this.structureData.panoramic720view = "https://www.720yun.com/vr/4dbj5Oyyuf7"
    }
    this.activeBreadcrumb = this.structureData.fileId ? 'BIM模型' : this.structureData.panoramic720view ? '全景' : info ? '视频' : '图片'
    if (this.structureData.fileId) this.listName.push('BIM模型');
    if (this.structureData.panoramic720view) this.listName.push('全景');
    if (info) this.listName.push('视频');
    if (this.structureImages.length > 0) this.listName.push('图片');
    this.getCategoryList()
    // this.autoPlay()
  },
  methods: {
    getCategoryList() {
      const url = 'https://jkjc.yciccloud.com:8000/xboot/displayScreen/default/getSensorCountKindByMonitorType'
      const params = {
        nodeCode: this.iThis.params.code
      }
      fetchGet(url, params).then(res => {
        // 转换数据格式
        this.categoryList = Object.entries(res.result).map(([name, number]) => ({
          icon: require('@/assets/monitoringSystem/displacement-icon.png'),
          name,
          number
        }))
      })
    },
    async getVideoList() {
      const url = "https://jkjc.yciccloud.com:8000/xboot/structurevideo/getByStructureCode"
      const params = { nodeCode: this.iThis.params.code, currentPage: -1, pageSize: -1 };
      const res = await fetchGet(url, params);
      let b = res.result.records;
      for (let i = 0; i < b.length; i++) {
        b[i].type = "video";
        b[i].label = b[i].videoDisplayName || b[i].videoIp;
      }
      let a = undefined;
      if (res.code == '200' && b.length > 0) {
        a = {
          id: "1X",
          label: "视频",
          type: "videoIcon",
          children: b
        }
      }
      return a;
    },
    setActiveBreadcrumb(item) {
      this.activeBreadcrumb = item
      switch (item) {
        case '视频':
          this.tabClick()
          break;
      }
    },
    tabClick() {
      this.videoUrl = ""
      const url = "https://jkjc.yciccloud.com:8000/xboot/structurevideo/getByStructureCode"
      const data = this.videoInfo.find(item => item.label === this.videoName)
      fetchGet(url, data).then(res => {
        if (res.code === 200) {
          let urlList = res.result.records;
          urlList.forEach((item) => {
            if (item.id == data.id) {
              this.videoUrl = String('https://jkjc.glyhgl.com:22585' + item.videoUrl);
              // this.videoUrl = String(item.videoIp + item.videoUrl);
            }
          });
        }
      })
    },
    handleSpread() {
      this.isSpread = !this.isSpread
      // if (this.isSpread) {
      //   this.$refs.category.style.display = "flex"
      //   this.isSpread = false
      // } else {
      //   this.$refs.category.style.display = "none"
      //   this.isSpread = true
      // }
    },
    autoPlay() {
      let currentIndex = 0;
      this.timer = setInterval(() => {
        // 获取当前要显示的面包屑项
        this.activeBreadcrumb = this.listName[currentIndex];
        // 处理视频切换
        if (this.activeBreadcrumb === '视频') {
          this.tabClick();
        }
        // 增加索引
        currentIndex = (currentIndex + 1) % this.listName.length;
      }, 3000); // 每3秒切换一次
    },
    // 停止自动播放
    stopAutoPlay() {
      if (this.timer) {
        clearInterval(this.timer);
      }
    }
  },
  // 在组件销毁时停止定时器
  beforeDestroy() {
    this.stopAutoPlay();
  },
  computed: {},
  watch: {},
}
</script>

<style lang="scss" scoped>
@import "@/assets/styles/utils.scss";

@keyframes moveUpDown {
  0% {
    transform: translateY(0) rotate(90deg);
  }

  100% {
    transform: translateX(-7px) rotate(90deg); // 向上移动 20px，可以根据需要调整
  }
}

.box {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
  position: relative;

  .spread-packup2 {
    z-index: 99;
    top: vwpx(4px);
    left: calc(50% - #{vwpx(30px)});
    position: absolute;
    cursor: pointer;
    transform: rotate(270deg);
    width: vwpx(60px);
    height: vwpx(30px);

    &:hover {
      .spread-img {
        animation: moveUpDown 0.7s infinite alternate; // 0.7s 动画时长，无限循环，交替方向
      }
    }

    .spread-img {
      transform: rotate(90deg);
      width: 100%;
      height: 100%;
    }
  }

  .bim-box {
    width: 100%;
    height: 100%;
  }

  .category {
    z-index: 99;
    width: 100%;
    position: absolute;
    top: vwpx(10px);
    left: 0%;
    right: 0%;
    padding: 0 vwpx(10px) vwpx(10px) 0;

    display: flex;
    align-items: center;
    flex-wrap: wrap;
    justify-content: center;

    &:hover {
      .spread-packup {
        display: block;
      }
    }

    .list {
      display: flex;
      justify-content: center;
      align-items: center;

      width: vwpx(260px);
      height: vwpx(88px);
      background: rgba(14, 25, 48, 0.5);
      box-shadow: inset 0px 0px 10px 0px #0687FF;
      border-radius: vwpx(12px);
      border: 1px solid #0687FF;
      margin-right: vwpx(20px);
      margin-top: vwpx(20px);

      img {
        width: vwpx(40px);
        height: vwpx(40px);
      }

      .text {
        font-weight: 400;
        font-size: vwpx(28px);
        color: #FFFFFF;
        margin: 0 vwpx(20px);
      }

      .number {
        font-weight: bold;
        font-size: vwpx(28px);
        color: #F2AF4A;
      }
    }

    .spread-packup {
      display: none;
      bottom: 0;
      left: calc(50% - #{vwpx(30px)});
      position: absolute;
      cursor: pointer;
      transform: rotate(90deg);
      width: vwpx(60px);
      height: vwpx(30px);

      // 添加动画效果
      .animated {
        display: block;
        animation: moveUpDown 0.7s infinite alternate; // 0.7s 动画时长，无限循环，交替方向
      }

      .img {
        transform: rotate(90deg);
        width: 100%;
        height: 100%;
      }
    }
  }

  .video-box {
    height: calc(100% - #{vwpx(180px)});
    width: 100%;
  }

  .breadcrumb {
    z-index: 99;
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    padding: vwpx(20px) vwpx(10px);
    color: #00FDFD;
    height: vwpx(80px);
    background: rgba(0, 36, 73, 0.8);

    display: flex;
    justify-content: center;
    align-items: center;
  }

  ::v-deep {
    .el-breadcrumb__separator {
      margin: 0 vwpx(12px);
      font-weight: normal;
      font-size: vwpx(30px);
    }
  }

  .breadcrumb-text {
    font-size: vwpx(34px);
  }


  /* 不被选中时的颜色 */
  .el-breadcrumb ::v-deep .el-breadcrumb__inner {
    color: #00FDFD !important;
    font-weight: 400 !important;
    cursor: pointer;
  }

  /* 被选中时的颜色 */
  .el-breadcrumb__item:last-child ::v-deep .el-breadcrumb__inner {
    color: #00FDFD !important;
    font-weight: 400 !important;
    cursor: pointer;
  }

}

::v-deep {
  .original {
    .el-tabs__item {
      width: vwpx(260px);
      height: vwpx(60px);
      line-height: vwpx(60px);
      padding: 0;
      font-size: vwpx(33px);
      color: #303133;
      transition: 0.3s;
    }
  }

  .el-tabs__nav {
    display: flex;
    /* 防止子元素换行 */
    flex-wrap: nowrap;
  }

  .el-tabs__nav-wrap::after {
    display: none;
  }

  .el-tabs__active-bar {
    display: none;
  }

  .el-tabs__header {
    margin: vwpx(20px);
  }

  .el-tabs__item {
    color: #fff !important;
  }

  .el-tabs__content {
    background: rgba(0, 35, 94, 0.5) !important;
  }

  .original {
    .el-tabs__item.is-active {
      color: #1D77FF !important;
    }
  }
}
</style>