<template>
  <div v-loading="loading" class="culvert-info-edit-base" :style="{height: formH + 'px'}">
    <el-form ref="ruleFormEl" :model="dataForm" :rules="rules" label-width="90px">
      <el-row :gutter="20">
        <el-col :span="8" :offset="0">
          <el-form-item label="管理处" prop="managementMaintenanceId">
            <SelectTree v-model="dataForm.managementMaintenanceId" :dept-type="201" @input="deptChange" placeholder="请选择管理处" />
            <!-- <treeselect v-model="dataForm.managementMaintenanceId" :options="deptOptions" :show-count="true"
              placeholder="请选择管理处" @input="deptChange" clearable/> -->
          </el-form-item>
        </el-col>
        <el-col :span="8" :offset="0">
          <el-form-item label="养护路段" prop="maintenanceSectionId">
            <!-- <RouteRoad v-model="dataForm.maintenanceSectionId" :disabled="!dataForm.managementMaintenanceId" @change="$set(dataForm, 'routeCode', null)" /> -->
            <el-select v-model="dataForm.maintenanceSectionId"  placeholder="养护路段" clearable :disabled="!dataForm.managementMaintenanceId"  style="width: 100%;">
              <el-option v-for="item in routeOptions"
                :key="item.maintenanceSectionId"
                :label="item.maintenanceSectionName"
                :value="item.maintenanceSectionId">
              </el-option>
            </el-select>

          </el-form-item>
        </el-col>
        <el-col :span="8" :offset="0">
          <el-form-item label="路线编码" prop="routeCode">
            <!-- <RouteLine v-model="dataForm.routeCode" :disabled="!dataForm.maintenanceSectionId" placeholder="请选择路线编码" /> -->
            <el-select v-model="dataForm.routeCode"  placeholder="请选择路线编码" :disabled="!dataForm.maintenanceSectionId" clearable style="width: 100%;">
              <el-option v-for="item in routeList"
                :key="item.routeId"
                :label="item.routeName"
                :value="item.routeId">
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="8">
          <el-form-item label="路线等级" prop="routeLevel">
            <el-select v-model="dataForm.routeLevel" clearable filterable style="width: 100%;">
              <el-option v-for="dict in dict.type.bridge_route_level"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="涵洞编码" prop="culvertCode">
            <el-input v-model="dataForm.culvertCode" placeholder="请输入涵洞编码" />
          </el-form-item>
          <el-form-item label="匝道编码" prop="rampId">
            <el-input v-model="dataForm.rampId" placeholder="请输入匝道编码" />
          </el-form-item>
          <el-form-item label="中心桩号">
            <PileInput v-model="dataForm.centerStake" placeholder="请输入中心桩号" />
          </el-form-item>
          <el-form-item label="进洞口图片">
            <ImageUpload
              v-model="dataForm.entranceHoleImage"
              :limit="1"
              storage-path="/base/culvert/culvertInfo"
              :owner-id="enterOwnerId"
            />
          </el-form-item>
        </el-col>
        <el-col :span="8">

          <el-form-item label="路线类型" prop="sectionType">
            <!-- <el-select v-model="dataForm.sectionType" clearable filterable style="width: 100%;">
              <el-option v-for="dict in dict.type.sys_route_type"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value">
              </el-option>
            </el-select> -->
            <SectionSelect
              :sectionId="dataForm.maintenanceSectionId"
              :formObject="dataForm"
              :disabled="!dataForm.maintenanceSectionId"
              clearable
              v-model="dataForm.sectionType"
            />
            <!-- <LineSection v-model="dataForm.sectionType" :maintenanceSectionId="dataForm.maintenanceSectionId"
            :disabled="!dataForm.maintenanceSectionId" :formObj.sync="dataForm" clearable/> -->
          </el-form-item>
          <el-form-item label="涵洞类型" prop="culvertType">
            <el-select v-model="dataForm.culvertType" clearable filterable style="width: 100%;">
              <el-option v-for="dict in dict.type.sys_culvert_type"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="设计荷载" prop="designLoad">
            <!-- <DictSelect v-model="dataForm.designLoad" type="bridge_design_load" /> -->
            <el-select v-model="dataForm.designLoad" clearable filterable style="width: 100%;">
              <el-option v-for="dict in dict.type.bridge_design_load"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="施工桩号">
            <PileInput v-model="dataForm.constructionStake" placeholder="请输入施工桩号" />
          </el-form-item>
          <el-form-item label="出洞口图片">
            <ImageUpload v-model="dataForm.exitHoleImage" :limit="1" storage-path="/base/culvert/culvertInfo" :owner-id="leaveOwnerId" />
          </el-form-item>
        </el-col>
        <el-col :span="8">

          <el-form-item label="管养分处" prop="managementMaintenanceBranchId">
            <!-- <treeselect v-model="dataForm.managementMaintenanceBranchId" :options="branchDeptOptions" :show-count="true"
                    placeholder="请选择管养分处"/> -->
            <SelectTree v-model="dataForm.managementMaintenanceBranchId" :dept-type="202" placeholder="请选择管养分处"/>
          </el-form-item>
          <el-form-item label="建成时间">
            <el-date-picker
              v-model="dataForm.buildDate"
              type="date"
              placeholder="选择建成时间"
              value-format="yyyy-MM-dd"
              style="width: 100%;"
            />
          </el-form-item>
          <el-form-item label="涵洞跨径(m)" prop="culvertSpan">
            <el-input-number v-model="dataForm.culvertSpan" style="width: 100%;" controls-position="right" placeholder="请输入涵洞跨径" />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </div>
</template>

<script>
import { createIdWorker } from '@/api/baseData/common'
import PileInput from '@/components/PileInput/index.vue'
import SelectTree from '@/components/DeptTmpl/selectTree'
import "@riophae/vue-treeselect/dist/vue-treeselect.css";
import Treeselect from "@riophae/vue-treeselect";
import RouteLine from '@/components/RouteLine'
import RouteRoad from '@/components/RouteRoad'
import SectionSelect from '@/components/SectionSelect'
import LineSection from '@/views/baseData/components/lineSection'
import { deptTreeSelect } from "@/api/tmpl";
import {listMaintenanceSectionAll} from "@/api/system/maintenanceSection";
import {listByMaintenanceSectionId} from "@/api/baseData/common/routeLine";

export default {
  name: 'CulvertInfoEditBase',
  dicts: ['bridge_route_level', 'sys_route_type','sys_culvert_type','bridge_design_load'],
  components: { PileInput, SelectTree, RouteLine, RouteRoad,Treeselect,SectionSelect ,LineSection},
  props: {
    ruleForm: {
      type: Object,
      default: () => {}
    },
    rules: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      loading: false,
      dataForm: {
        culvertSpan: undefined,
      },
      enterOwnerId: '', // 进洞口图片id
      leaveOwnerId: '', // 出洞口图片id
      formH: 600, // form 表单最大高度
      deptOptions: [], // 部门树数据
      branchDeptOptions: [], //
      types: 201, // 编码规划
      routeOptions: [], // 路段数据
      routeList: [], // 路线编码数据
    }
  },
  computed: {},
  watch: {
    ruleForm: {
      handler(val) {
        if(val && Object.keys(val).length > 0) {
          this.dataForm = val
          if (val.maintenanceSectionId) {
            this.maintenanceSectionChange(val.maintenanceSectionId)
          }
        }
      },
      deep: true,
      immediate: true,
    },
  },
  created() {
    // this.getDeptTree();
    this.init()
  },
  mounted() {
    this.formH = window.innerHeight - 360
    window.addEventListener("resize", ()=>{
      this.formH = window.innerHeight - 360
    })
  },
  methods: {
    init() {
      // 使用浅拷贝，方便收集数据
      this.dataForm = this.ruleForm
      this.getCreateId()
    },
    getCreateId() {
      // 生成进洞口图片id
      createIdWorker().then(res => {
        if (res.code === 200) this.enterOwnerId = res.data
      })
      // 生成出洞口图片id
      createIdWorker().then(res => {
        if (res.code === 200) this.leaveOwnerId = res.data
      })
    },
    validateCheck() {
      let flag = false
      this.$refs['ruleFormEl'].validate((valid) => {
        flag = valid
      })
      return flag
    },
    // 监听选中管理处
    deptChange(e){
      if(!e) return;
      listMaintenanceSectionAll({departmentId:e}).then(res=>{
        if(res.code ==200) {
          this.routeOptions = res.data
        }
      })
    },
    // 监听养护路段数据变化
    maintenanceSectionChange(e){
      if(!e) return;
      listByMaintenanceSectionId({maintenanceSectionId:e}).then(res=>{
        if(res.code ==200) {
          this.routeList = res.data || []
        }
      })
    },
    /** 查询部门下拉树结构 */
    getDeptTree() {
      deptTreeSelect({types: this.types}).then(response => {
        this.deptOptions = response.data;
      });
      deptTreeSelect({types: 202}).then(response => {
        this.branchDeptOptions = response.data;
      });

    },
  }
}
</script>

<style lang="scss" scoped>
.culvert-info-edit-base {
  overflow-y: auto;
  overflow-x: hidden;
}
</style>
