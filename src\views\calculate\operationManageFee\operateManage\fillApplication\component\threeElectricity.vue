<template>
  <div class="app-container">
    <el-row :gutter="20">
      <!-- 筛选区开始 -->
      <el-col :span="24" :xs="24">
        <el-row>
          <el-col :span="24">
            <el-form ref="queryForm" :inline="true" :model="queryParams" label-width="68px" size="small">
              <el-form-item label="" prop="maintenanceRoad">
                <RoadSection :deptId="this.domainId" ref="roadSection" v-model="queryParams.maiSecId" placeholder="路段"
                             style="width: 100%"/>
              </el-form-item>
              <el-form-item label="" prop="houseName">
                <el-input v-model="queryParams.houseName" placeholder="请输入户名"/>
              </el-form-item>
              <el-form-item label="" prop="houseNum">
                <el-input v-model="queryParams.houseNum" placeholder="请输入户号"/>
              </el-form-item>
              <el-form-item>
                <el-button icon="el-icon-search" size="mini" type="primary" @click="handleQuery">搜索</el-button>
                <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
              </el-form-item>
            </el-form>
          </el-col>
        </el-row>
        <!-- 筛选区结束 -->
        <!-- 数据表格开始 -->
        <div class="tableDiv">
          <el-table v-adjust-table v-loading="loading" :data="dataList"
                    :height="'calc(70vh - 260px)'" border size="mini"
                    style="width: 100%" @selection-change="handleSelectionChange">>
            <el-table-column
              type="selection"
              width="50">
            </el-table-column>
            <el-table-column align="center" fixed label="序号" type="index" width="100"></el-table-column>
            <template v-for="(column, index) in columns">
              <el-table-column
                v-if="column.visible"
                :label="column.label"
                :prop="column.field"
                align="center"
              >
                <template slot-scope="scope">
                  <dict-tag
                    v-if="column.dict"
                    :options="dict.type[column.dict]"
                    :value="scope.row[column.field]"
                  />
                  <template v-else-if="column.slots">
                    <RenderDom
                      :index="index"
                      :render="column.render"
                      :row="scope.row"
                    />
                  </template>
                  <span v-else-if="column.isTime">{{
                      parseTime(scope.row[column.field], "{y}-{m}-{d}")
                    }}</span>
                  <span v-else>{{ scope.row[column.field] }}</span>
                </template>
              </el-table-column>
            </template>
          </el-table>
          <pagination
            v-show="total > 0"
            :limit.sync="queryParams.pageSize"
            :page.sync="queryParams.pageNum"
            :total="total"
            @pagination="handleQuery"
          />
        </div>
        <!-- 数据表格结束 -->
      </el-col>
    </el-row>
    <div class="mt20" style="text-align: right">
      <el-button type="primary" @click="handleCheckData">保存</el-button>
    </div>
  </div>
</template>

<script>
import {
  listElectricityPage
} from '@/api/calculate/operationManageFee/threeElectricity';
import RoadSection from "@/views/baseData/components/roadSection/index.vue";
import RouteCodeSection from "@/components/RouteCodeSection/index.vue";
import SelectTree from "@/components/DeptTmpl/selectTree.vue";

export default {
  name: "YourComponentName",
  components: {SelectTree, RouteCodeSection, RoadSection},
  dicts: ['company_type'],
  props: {
    domainId: {
      type: String,
      default: '',
    }
  },
  data() {
    return {
      // 遮罩层
      loading: false,
      // 显示搜索条件
      showSearch: false,
      // 总条数
      total: 0,
      dataList: [],
      selectDatas: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        maintenanceRoad: '',
        username: '',
        accountNumber: ''
      },
      // 表格列配置
      columns: [
        {key: 0, field: 'domainName', label: '管养单位', visible: true},
        {key: 1, field: 'routeCode', label: '路线编码', visible: true},
        {key: 2, field: 'maiSecName', label: '养护路段', visible: true},
        {key: 3, field: 'openTime', label: '通车时间', visible: true},
        {key: 4, field: 'companyType', label: '产权单位', visible: true, dict: 'company_type'},
        {key: 5, field: 'houseNum', label: '用电户号', visible: true},
        {key: 6, field: 'houseName', label: '用电户名', visible: true},
        {key: 7, field: 'measureNum', label: '计量点编号', visible: true},
        {key: 8, field: 'kva', label: '变压器容量(KVA)', visible: true},
        {key: 9, field: 'kv', label: '电压等级(KV)', visible: true},
        {key: 10, field: 'electricalProperties', label: '用电性质', visible: true},
        {key: 11, field: 'electricityAddr', label: '用电地址', visible: true},
        {key: 12, field: 'powerUnit', label: '供电单位', visible: true},
        {key: 13, field: 'attribute', label: '市场化属性分类', visible: true},
        {key: 14, field: 'powerRange', label: '用电范围', visible: true},
        {key: 15, field: 'payUnit', label: '电费缴纳单位', visible: true}
      ],
    };
  },
  created() {
    this.handleQuery()
  },
  methods: {
    handleQuery() {
      this.loading = true;
      this.queryParams.domainId = this.domainId
      listElectricityPage(this.queryParams).then(response => {
        this.dataList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 选中
    handleSelectionChange(e) {
      this.selectDatas = e
    },
    handleCheckData() {
      this.$emit('check', this.selectDatas)
    },
  }
}
</script>
<style scoped>
.tableDiv {
  margin-top: 20px;
}
</style>
<style lang="scss" scoped>
@import "@/assets/styles/business.scss";
</style>
