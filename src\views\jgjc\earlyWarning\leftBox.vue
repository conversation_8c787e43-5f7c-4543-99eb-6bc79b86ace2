<template>
  <div ref="body" class="box-body">
    <span ref="span1" class="box-span upper-left"></span>
    <span ref="span2" class="box-span upper-right"></span>
    <span ref="span3" class="box-span lower-left"></span>
    <span ref="span4" class="box-span lower-right"></span>
    <div class="content" @click="handleClick">
      <el-image class="img" :src="boxData.thumbUrl || ''">
        <div slot="placeholder" class="image-slot">
          <i class="el-icon-picture-outline"></i>
          加载中...
        </div>
      </el-image>
      <span class="box-name">
        {{boxData.roadName}}<p/>
        {{boxData.name}}</span>
      <img v-if="boxData.isHasVideo == 1" class="icon type-icon" src="@/assets/earlyWarning/video.png" />
      <img class="icon early-icon" :src="boxData.planStatus == 1 ? EarlyWarningR: EarlyWarningG " />
      <img class="icon wifi-icon" :src="boxData.isHasOffFlashDev == 0 ? WifiBlue: WifiRed" />
    </div>
  </div>
</template>

<script>
import EarlyWarningG from '@/assets/earlyWarning/early-warning-g.png'
import EarlyWarningR from '@/assets/earlyWarning/early-warning-r.png'
import WifiBlue from '@/assets/earlyWarning/wifi_blue.png'
import WifiRed from '@/assets/earlyWarning/wifi_red.png'

export default {
  props: {
    boxData: {
      type: Object,
      default: () => {
        return {
          timers: []
        }
      }
    }
  },
  watch: {
    boxData: {
      handler(val) {
        this.$nextTick(() => {
          const boxBody = this.$refs.body
          const span1 = this.$refs.span1
          const span2 = this.$refs.span2
          const span3 = this.$refs.span3
          const span4 = this.$refs.span4
          boxBody.style.borderColor = '#42ABFF'
          span1.style.borderColor = '#42ABFF'
          span2.style.borderColor = '#42ABFF'
          span3.style.borderColor = '#42ABFF'
          span4.style.borderColor = '#42ABFF'
          this.timers.forEach(timerId => clearInterval(timerId));
          this.timers = [];
          if (val.planStatus == 1) {
            boxBody.style.borderColor = '#fe4646'
            span1.style.borderColor = '#fe4646'
            span2.style.borderColor = '#fe4646'
            span3.style.borderColor = '#fe4646'
            span4.style.borderColor = '#fe4646'
          } else if (val.status == 1) {
            const colors = ['#fe4646', '#42ABFF', '#32da49'];
            this.changeBorderColor(boxBody, colors);
            this.changeBorderColor(span1, colors);
            this.changeBorderColor(span2, colors);
            this.changeBorderColor(span3, colors);
            this.changeBorderColor(span4, colors);
          }
        })
      },
      immediate: true
    }
  },
  data() {
    return {
      timers: [],
      EarlyWarningG,
      EarlyWarningR,
      WifiBlue,
      WifiRed
    }
  },
  methods: {
    changeBorderColor(element, colors, speed = 100) {
      let index = 0;
      const timerId = setInterval(() => {
        element.style.borderColor = colors[index];
        index = (index + 1) % colors.length; // 循环颜色数组
      }, speed);
      this.timers.push(timerId)
    },
    handleClick() {
      this.$emit('click', this.boxData)
    }
  },
  beforeDestroy() {
    this.timers.forEach(timerId => clearInterval(timerId));
    this.timers = [];
  }
}
</script>

<style lang="scss" scoped>
@import "@/assets/styles/utils.scss";

.box-body {
  width: 100%;
  height: 100%;
  border: 1px solid #42ABFF;
  position: relative;

  .box-span {
    position: absolute;
    padding: vwpx(16px);
    border-style: solid;
    border-color: #42ABFF;
  }

  .upper-left {
    border-width: vwpx(6px) 0 0 vwpx(6px);
    top: vwpx(-6px);
    left: vwpx(-6px);
  }

  .upper-right {
    border-width: vwpx(6px) vwpx(6px) 0 0;
    top: vwpx(-6px);
    right: vwpx(-6px);
  }

  .lower-right {
    border-width: 0 0 vwpx(6px) vwpx(6px);
    bottom: vwpx(-6px);
    left: vwpx(-6px);
  }

  .lower-left {
    border-width: 0 vwpx(6px) vwpx(6px) 0;
    bottom: vwpx(-6px);
    right: vwpx(-6px);
  }
  .content {
    width: 100%;
    height: 100%;
    padding: vwpx(20px);
    position: relative;
    cursor: pointer;
    .img {
      width: 100%;
      height: 100%;
      .image-slot {
        width: 100%;
        height: 100%;
        display: flex;
        justify-content: center;
        align-items: center;
        background-color: #f5f7fa;
        font-size: 14px;
        color: #C0C4CC;
      }
    }
    .icon {
      position: absolute;
    }
    .type-icon {
      right: 5%;
      top: 7%;
    }
    .early-icon {
      right: 5%;
      bottom: 7%;
    }
    .wifi-icon {
      left: 8%;
      top: 7%;
      height: 3vmin;
      width: 3vmin;
    }
    .box-name {
      display: inline-block;
      position: absolute;
      bottom: 5%;
      width: 96.5%;
      left: 2%;
      padding: 0px 10px;
      color: white;
      font-size: vwpx(32px);
      text-shadow: 2px 2px 6px rgba(66, 66, 66, 1);
      background-color: rgba(9, 25, 45, 0.7); /* 白色半透明背景 */
      font-weight: bolder;
      p {
        margin: 0;
      }
    }
  }
}

</style>
