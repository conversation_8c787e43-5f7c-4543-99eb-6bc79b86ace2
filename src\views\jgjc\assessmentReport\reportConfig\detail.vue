<template>
  <el-dialog :visible.sync="centerDialogVisible" width="1000px" center @close="closeDialog">
    <span slot="title" class="title">
      {{ title }}
    </span>
    <div>
      <el-form
          :model="form"
          :inline="true"
          label-position="left"
          label-width="95px"
          :rules="report_set_rule"
          ref="ruleForm"
          label-suffix="："
      >
        <el-form-item label="当前桥梁" prop="currentStructure">
          <span style="float: left">{{currentStructure.label ? currentStructure.label : "未选择"}}</span>
        </el-form-item>
        <el-form-item label="配置名称" prop="structureName">
          <el-input
              placeholder="请输入内容"
              v-model="form.structureName"
              size="small"
              class="searchInput"
              clearable>
          </el-input>
        </el-form-item>
        <el-form-item label="开始章节" prop="startChapter">
          <el-input v-model.number="form.startChapter" type="number" size="small" style="width: 50px"></el-input>
        </el-form-item>
      </el-form>
      <el-collapse v-model="collapseActiveNames" @change="handleCollapseChange">
        <el-collapse-item name="dataCompositionTree">
          <template slot="title">
            <span>报告数据部分结构</span>
            <el-button size='mini' type="primary" @click.stop="resetReportStructureTree" class="resetButton">重置</el-button>
          </template>
          <DataCompositionTree :currentStructure="currentStructure" :data="data" :tree-load-all="treeLoadAll" :sensor-info-list="sensorInfoList"
                               :expand-array="expandArr" :monitorData="monitorData" :expandArrForSelect="expandArrForSelect"/>
        </el-collapse-item>
      </el-collapse>
    </div>
    <span slot="footer" class="dialog-footer">
      <el-button size="mini" @click="centerDialogVisible = false">取 消</el-button>
      <el-button size="mini" type="primary" @click="submitForm('ruleForm')" v-if="!isEdit">保 存</el-button>
      <el-button size="mini" type="primary" @click="saveEdit('ruleForm')" v-if="isEdit">保 存</el-button>
      <el-button size="mini" type="success" @click="submitForm('ruleForm')" v-if="isEdit">另存为</el-button>
    </span>
  </el-dialog>
</template>

<script>
import DataCompositionTree from "./DataCompositionTree.vue";
import { getTreeStructureUnderNodeCode, getSensorListUnderNodeCode, saveReportStructure, getReportStructureById, updateReportStructure } from "@/api/jgjc/assessmentReport/reportConfig"
import {
  addContentProp,
  addLocationProp,
  addSensorProp
} from "./utils";
import { cloneDeep } from "lodash";
import {colorList} from "./utils";

export default {
  name: "report-config",
  components: {DataCompositionTree},
  props: ["currentStructure"],
  data() {
    return {
      data: [],
      treeLoadAll: false,
      sensorInfoList: [],

      title: "配置结构信息",
      // dialog是否显示
      centerDialogVisible: false,

      // 提交的表单信息
      form: {
        currentStructure: {},
        structureName: '',
        startChapter: 6,
      },
      report_set_rule: {
        structureName: [
          {required: true, message: '配置名称不可为空', trigger: 'blur'}
        ],
        startChapter: [
          {required: true, message: '开始章节不可为空', trigger: 'blur'}
        ],
      },

      // 报告配置dialog中collapse
      collapseActiveNames: ['dataCompositionTree'],
      isLong: false,
      isLessThan2: false,
      numLimit: 20,
      isEdit: false,
      id: '',
      expandArrForSelect: [],
      monitorData: [],
      colorList: colorList,
    };
  },
  mounted() {
    // 监听编辑数据
    window.$Bus.$on("showConfig", async (data) => {
      this.isEdit = false;
      this.title = "配置结构信息"
      this.form.structureName = ''
      this.form.startChapter = 6
      this.centerDialogVisible = true;
      // 读取临时生成的报告结构树
      await this.getTrees()
    });
    window.$Bus.$on("showEditConfig", async (id) => {
      this.id = id;
      this.isEdit = true;
      this.title = "编辑配置信息"
      this.centerDialogVisible = true;
      // 读取历史的报告结构树
      await this.getTreesById(id)
    });
  },
  destroyed() {
    window.$Bus.$off("showConfig");
    window.$Bus.$off("showEditConfig");
  },
  created() {
    this.initialOptions();
  },

  methods: {
    // 报告配置dialog中Collapse改变方法
    handleCollapseChange(val) {
      // console.log(`Collapse changed: ${val}`)
    },

    // 关闭弹框
    closeDialog() {
    },

    // 初始化选项框
    initialOptions() {
      // 清空form数据
      this.form.currentStructure = "";
    },

    // 递归得到树结构 并初始化传感器列表
    async getTrees() {
      this.treeLoadAll = true;
      if (!this.currentStructure?.structureId) {
        this.treeLoadAll = false;
        return [];
      }
      const treeD = await getTreeStructureUnderNodeCode({ structureId: this.currentStructure.structureId });
      // 获取传感器信息列表
      this.sensorInfoList = await getSensorListUnderNodeCode({ structureId: this.currentStructure.structureId });
      this.treeLoadAll = false;
      this.expandArrForSelect.splice(0)
      this.monitorData = this.treeChildrenExpandForSelect(cloneDeep(treeD));
      this.data = this.treeChildrenExapend1(this.addProp(treeD));
    },
    // 读取历史树结构物
    async getTreesById(id) {
      this.treeLoadAll = true;
      const reportStructure = await getReportStructureById(id)
      const treeD = await getTreeStructureUnderNodeCode({ structureId: this.currentStructure.structureId });
      this.expandArrForSelect.splice(0)
      this.monitorData = this.treeChildrenExpandForSelect(treeD);
      this.sensorInfoList = await getSensorListUnderNodeCode({ structureId: this.currentStructure.structureId });
      this.form.structureName = reportStructure.name;
      this.form.startChapter = Number(JSON.parse(reportStructure.dataModel).startChapter);
      let originArr = JSON.parse(reportStructure.reportStructure);
      let arr = JSON.parse(reportStructure.reportStructure);
      let deleteSensorCount = 0;
      // 更新最新的传感器节点
      // 监测类型
      for(let i = 0; i < originArr.length; i++){
        let tmpType = originArr[i];
        // 监测内容
        for(let j = 0; j < originArr[i].children.length; j++){
          let tmpContent = originArr[i].children[j];
          // 监测位置
          for(let k = 0; k < originArr[i].children[j].children.length; k++){
            let tmpLocation = originArr[i].children[j].children[k];
            let tmpLocationArr = arr[i].children[j].children[k];
            let tmpLocationChildren = [];
            // 传感器
            for(let l = 0; l < originArr[i].children[j].children[k].children.length; l++){
              let tmpSensor = tmpLocation.children[l];
              // 用具体监测类型Id和sensorId一起确定，因为record的Id字段可能为树新生成
              const index = this.sensorInfoList.findIndex((d) => (
                d.specificMonitorTypeId === tmpSensor.specificMonitorTypeId &&
                d.sensorId === tmpSensor.sensorId
              ));
              if(index < 0){
                // 该传感器在系统中已经被删除，故在此也删除
                deleteSensorCount++;
              }else {
                // 该传感器未被删除，则更新该传感器的字段
                let sensorContent = tmpSensor.content
                Object.keys(this.sensorInfoList[index]).forEach(key => {
                  tmpSensor[key] = this.sensorInfoList[index][key]
                })
                tmpSensor.content = sensorContent
                tmpLocationChildren.push(tmpSensor)
              }
              // 兼容性代码 有些属性没有给他分配上
              addSensorProp(tmpSensor, this)
              tmpSensor.useInstallTime = parseInt(tmpSensor.useInstallTime)
              tmpSensor.use3sigma = parseInt(tmpSensor.use3sigma)
              tmpSensor.sigmaInterpolate = parseInt(tmpSensor.sigmaInterpolate)
              tmpSensor.useUpDownLimit = parseInt(tmpSensor.useUpDownLimit)
              tmpSensor.upDownInterpolate = parseInt(tmpSensor.upDownInterpolate)
              // 下面这段代码是用来临时更新旧配置的颜色属性
              // tmpSensor.color = colorList[l % colorList.length].ENName // 自动分配不同颜色
              // 若原始值为空值 做一次填充
              if(!tmpSensor.content || tmpSensor.content === ''){
                if(tmpSensor.labelType === 0){
                  tmpSensor.content = tmpSensor.specificMonitorTypeName + tmpSensor.sensorInstallCode
                }else if(tmpSensor.labelType === 1){
                  tmpSensor.content = tmpSensor.eigenvalueType.name
                }else if(tmpSensor.labelType === 2){
                  tmpSensor.content = tmpSensor.installLocation
                }else if(tmpSensor.labelType === 3){
                  tmpSensor.content = tmpSensor.specificMonitorTypeName
                }else if(tmpSensor.labelType === 4){
                  tmpSensor.content = tmpSensor.reportDisplayName
                }
              }
            }
            tmpLocationArr.children = tmpLocationChildren
            // 兼容性代码 有些属性没有给他分配上
            addLocationProp(tmpLocation, this)
            if(!tmpLocation?.title){
              this.$set(tmpLocation, 'title', this.currentStructure.label + tmpLocation.content + tmpType.content + "变化曲线图") // 初始化绘图标题名称
            }
          }
          addContentProp(tmpContent, this)
        }
      }
      if(deleteSensorCount > 0){
        await this.$confirm('该配置中有'+deleteSensorCount+'个传感器可能已经在系统中被删除，是否需要在配置中也同步删除？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(async () => {
          originArr = arr;
        }).catch(() => {
        });
      }
      this.data = this.treeChildrenExapend1(originArr);
      // 将更新后的结果保存一次
      // 组合数据模型
      const dataModel = {
        startChapter: this.form.startChapter
      }
      const result = await updateReportStructure({
        id: id,
        structureCode: this.currentStructure.structureId,
        name: this.form.structureName,
        reportStructure: JSON.stringify(this.data),
        dataModel: JSON.stringify(dataModel)
      })
      if(result){
        this.$message({
          type: 'success',
          message: '自动更新传感器完成并保存'
        });
        window.$Bus.$emit("refreshStructureTable", this.currentStructure.structureId);
      }else if(result === false){
        this.$message({
          type: 'error',
          message: '更新配置失败!'
        });
      }
      this.treeLoadAll = false;
    },

    // 提交表单
    submitForm(formName) {
      if(!this.currentStructure?.structureId){
        this.$message({
          type: 'warning',
          message: '桥梁未选择'
        });
        return;
      }
      if(this.checkName(this.form.structureName)){
        this.$message({
          type: 'warning',
          message: '配置名称含有非法字符'
        });
        return;
      }
      this.isLongJudge(this.data)
      if(this.isLong){
        this.$message({
          type: 'warning',
          message: '一个监测位置下最多只能有'+this.numLimit+'个传感器，请重新组织结构后保存'
        });
        return;
      }
      if(this.isLessThan2){
        this.$message({
          type: 'warning',
          message: '结构中有绘图种类需要传感器数目为2，但不满足条件，请根据红色高亮提示调整后保存'
        });
        return;
      }
      this.$refs[formName].validate(async (valid) => {
        if (valid) {
          this.treeLoadAll = true;
          // 组合数据模型
          const dataModel = {
            startChapter: this.form.startChapter
          }
          const result = await saveReportStructure({
            structureCode: this.currentStructure.structureId,
            name: this.form.structureName,
            reportStructure: JSON.stringify(this.data),
            dataModel: JSON.stringify(dataModel)
          })
          if(result){
            this.centerDialogVisible = false
            this.$message({
              type: 'success',
              message: '新增配置成功!'
            });
            window.$Bus.$emit("refreshStructureTable", this.currentStructure.structureId);
          }else if(result === false){
            this.$message({
              type: 'error',
              message: '新增配置失败!'
            });
          }
          this.treeLoadAll = false;
        } else {
          return false;
        }
      });
    },

    // 保存编辑
    saveEdit(formName){
      if(!this.currentStructure?.structureId){
        this.$message({
          type: 'warning',
          message: '桥梁未选择'
        });
        return;
      }
      if(this.checkName(this.form.structureName)){
        this.$message({
          type: 'warning',
          message: '配置名称含有非法字符'
        });
        return;
      }
      this.isLongJudge(this.data)
      if(this.isLong){
        this.$message({
          type: 'warning',
          message: '一个监测位置下最多只能有'+this.numLimit+'个传感器，请重新组织结构后保存'
        });
        return;
      }
      if(this.isLessThan2){
        this.$message({
          type: 'warning',
          message: '结构中有绘图种类需要传感器数目为2，但不满足条件，请根据红色高亮提示调整后保存'
        });
        return;
      }
      this.$refs[formName].validate(async (valid) => {
        if (valid) {
          this.treeLoadAll = true;
          // 组合数据模型
          const dataModel = {
            startChapter: this.form.startChapter
          }
          const result = await updateReportStructure({
            id: this.id,
            structureCode: this.currentStructure.structureId,
            name: this.form.structureName,
            reportStructure: JSON.stringify(this.data),
            dataModel: JSON.stringify(dataModel)
          })
          if(result){
            this.centerDialogVisible = false
            this.$message({
              type: 'success',
              message: '更新配置成功!'
            });
            window.$Bus.$emit("refreshStructureTable", this.currentStructure.structureId);
          }else if(result === false){
            this.$message({
              type: 'error',
              message: '更新配置失败!'
            });
          }
          this.treeLoadAll = false;
        } else {
          return false;
        }
      });
    },

    // 增加对应等级的配置属性 并赋予默认值 树结构初始化
    addProp(arr=[]){
      let tmpCode = new Date().getTime().toString()
      let tmpCode1 = new Date().getTime().toString() + 'A'
      let attachmentsTable = {
        structureId: tmpCode,
        content: "附件",
        type: "监测类型",
        nodeLevel: 6,
        useFlag: true,
        havePermission: false,
        children: [
          {
            structureId: tmpCode1,
            content: "附件",
            type: "监测内容",
            nodeLevel: 7,
            useFlag: true,
            havePermission: false,
            children: [],
            sort: 0,
            parentCode: tmpCode
          }
        ],
        sort: arr.length + 1
      }
      // 自动化表格
      let attachmentsTableKid = attachmentsTable.children[0].children
      // 监测类型
      for(let i = 0; i < arr.length; i++){
        let tmpType = arr[i];
        this.$set(tmpType, 'originType', tmpType.content) // 保留一个原生的type名称
        // 监测内容
        for(let j = 0; j < arr[i].children.length; j++){
          let tmpContent = arr[i].children[j];
          addContentProp(tmpContent, this)
          // 监测位置
          for(let k = 0; k < arr[i].children[j].children.length; k++){
            // 传感器
            for(let l = 0; l < arr[i].children[j].children[k].children.length; l++){
              let tmpSensor = arr[i].children[j].children[k].children[l];
              addSensorProp(tmpSensor, this) // 检查属性包含情况
              tmpSensor.color = colorList[l % colorList.length].ENName // 自动分配不同颜色
              tmpSensor.content = tmpSensor.specificMonitorTypeName+tmpSensor.sensorInstallCode
            }
            let tmpLocation = arr[i].children[j].children[k]
            addLocationProp(tmpLocation, this) // 检查属性包含情况
            this.$set(tmpLocation, 'title', this.currentStructure.label + tmpLocation.content + tmpType.content + "变化曲线图") // 初始化绘图标题名称
            // 新增默认附件表格
            let tmpLocationAttachments = cloneDeep(tmpLocation)
            tmpLocationAttachments.usageMode = 1
            attachmentsTableKid.push(tmpLocationAttachments)
          }
        }
      }
      arr.push(attachmentsTable)
      return arr
    },

    // 判定单个绘图下是否有超过12个传感器
    isLongJudge(arr=[]){
      this.isLong = false;
      this.isLessThan2 = false;
      for(let i = 0; i < arr.length; i++){
        for(let j = 0; j < arr[i].children.length; j++){
          for(let k = 0; k < arr[i].children[j].children.length; k++){
            const tmpLocation = arr[i].children[j].children[k]
            // 如果有一个位置下传感器超过12个，则提示
            if(tmpLocation.children.length > this.numLimit){
              this.isLong = true;
            }
            // 结构中有绘图种类需要传感器数目为2，但不满足条件，需要根据红色高亮提示调整后保存
            if(tmpLocation.children.length < 2 && tmpLocation.algorithmName > 4
            && tmpLocation.usageMode === 0){
              this.isLessThan2 = true;
            }
          }
        }
      }
    },
    // 校验报告名是否含有非法字符
    checkName(s) {
      const containSpecial = RegExp(/[ \[\]\\.\/]/);
      return (containSpecial.test(s));
    },

    // 重置报告结构树
    resetReportStructureTree() {
      this.$confirm('确定重置结构树?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        await this.getTrees()
      }).catch(() => {
      });
    },

    // 展开这三级
    treeChildrenExpandForSelect(arr=[]){
      let expandBox = ["监测类型","监测内容","监测位置"]
      for(let i of arr){
        if(expandBox.indexOf(i.type)>-1){
          this.expandArrForSelect.push(i.structureId)
          i.children = this.treeChildrenExapend(i.children)
        }
      }
      return arr
    },
  },


};
</script>

<style scoped>
.title {
  font-family: "Courier New", Courier, monospace;
  font-size: 16px;
  font-weight: bolder;
}
.el-form-item {
  margin: 5px 0;
  display: inline-block;
  width: 550px !important;
}
/deep/.el-form-item__label {
  font-weight: bold !important;
}
/deep/.el-input__inner {
  padding: 0 0 0 10px !important;
}
/deep/.ivu-input {
  font-size: 13px !important;
}
/deep/.el-collapse-item__header{
  font-weight: bold !important;
  color: #606266;
  font-size: 14px;
}
.resetButton {
  margin-left: 750px;
}
/deep/.el-tag--mini {
  margin-left: 5px;
}
</style>
