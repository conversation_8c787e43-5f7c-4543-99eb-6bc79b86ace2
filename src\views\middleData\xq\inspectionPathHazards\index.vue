<template>
    <div class="app-container">
        <el-row :gutter="20">
            <!--筛选区开始-->
            <el-col :span="24" :xs="24">
                <el-row>
                    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" label-width="68px">
                        <el-col :span="24" style="display: flex;">
                            <el-form-item label="">
                                <CascadeSelection style="min-width: 192px;margin-right: 10px;" :form-data="queryParams"
                                    v-model="queryParams" @update:fromData="() => { }" types="201" multiple />
                            </el-form-item>

                            <el-form-item label="" prop="hazardLevelList">
                                <el-select v-model="queryParams.hazardLevelList" placeholder="隐患等级" clearable multiple
                                    collapse-tags style="width: 240px">
                                    <el-option v-for="dict in hazardLevelOptions" :key="dict.dictValue"
                                        :label="dict.dictLabel" :value="dict.dictValue" />
                                </el-select>
                            </el-form-item>
                            <el-form-item label="" prop="participant">
                                <el-input v-model="queryParams.participant" placeholder="请输入参与人" clearable
                                    prefix-icon="el-icon-user" style="width: 240px" @keyup.enter.native="handleQuery" />
                            </el-form-item>
                            <el-form-item>
                                <el-button type="primary" icon="el-icon-search" size="small"
                                    @click="handleQuery">搜索</el-button>
                                <el-button icon="el-icon-refresh" size="small" @click="resetQuery">重置</el-button>
                                <el-button v-show="!showSearch" @click="showSearch = true" icon="el-icon-arrow-down"
                                    circle></el-button>
                                <el-button v-show="showSearch" @click="showSearch = false" icon="el-icon-arrow-up"
                                    circle></el-button>
                            </el-form-item>
                        </el-col>
                        <el-col :span="24" v-show="showSearch">
                            <!--默认折叠 ,此处仅作为示例-->
                            <el-form-item label="巡查时间" prop="createTime">
                                <el-date-picker v-model="dateRange" style="width: 240px" value-format="yyyy-MM-dd"
                                    type="daterange" range-separator="-" start-placeholder="开始日期" end-placeholder="结束日期"
                                    size="small"></el-date-picker>
                            </el-form-item>

                            <el-form-item label="" prop="roadType">
                                <el-select v-model="queryParams.roadType" placeholder="公路类型" clearable
                                    style="width: 240px" size="small">
                                    <el-option v-for="dict in roadTypeOptions" :key="dict.dictValue"
                                        :label="dict.dictLabel" :value="dict.dictValue" />
                                </el-select>
                            </el-form-item>
                            <el-form-item label="" prop="rectificationCompleted">
                                <el-select v-model="queryParams.rectificationCompleted" placeholder="是否完成整治" clearable
                                    style="width: 240px" size="small">
                                    <el-option v-for="dict in measuresOptions" :key="dict.dictValue"
                                        :label="dict.dictLabel" :value="dict.dictValue" />
                                </el-select>
                            </el-form-item>
                        </el-col>
                    </el-form>
                </el-row>
                <!--筛选区结束-->

                <!--操作按钮区开始-->
                <el-row :gutter="10" class="mb8">
                    <el-col :span="1.5">
                        <el-button type="primary" icon="el-icon-plus" size="mini" @click="handleAdd"
                            v-hasPermi="['middleData:inspectionPathHazards:add']">新增</el-button>
                    </el-col>
                    <el-col :span="1.5">
                        <el-button type="success" icon="el-icon-edit" size="mini" :disabled="single"
                            @click="handleUpdate" v-hasPermi="['middleData:inspectionPathHazards:edit']">修改</el-button>
                    </el-col>
                    <el-col :span="1.5">
                        <el-button type="danger" icon="el-icon-delete" size="mini" :disabled="multiple"
                            @click="handleDelete"
                            v-hasPermi="['middleData:inspectionPathHazards:remove']">删除</el-button>
                    </el-col>
                    <!-- <el-col :span="1.5">
                        <el-button
                            type="info"
                            plain
                            icon="el-icon-upload2"
                            size="mini"
                            @click="handleImport"
                            v-hasPermi="['middleData:inspectionPathHazards:export']"
                            >导入</el-button
                        >
                    </el-col> -->
                    <el-col :span="1.5">
                        <el-button type="warning" icon="el-icon-download" size="mini" @click="handleExport"
                            v-hasPermi="['middleData:inspectionPathHazards:export']">导出</el-button>
                    </el-col>
                    <el-col :span="1.5">
                        <el-button type="primary" plain icon="el-icon-check" size="mini" :disabled="multiple"
                            @click="handleBatchAudit"
                            v-hasPermi="['middleData:inspectionPathHazards:audit']">批量审核</el-button>
                    </el-col>
                    <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"
                        :columns="columns"></right-toolbar>
                </el-row>
                <!--操作按钮区结束-->

                <!--数据表格开始-->
                <div class="tableDiv">
                    <el-table v-adjust-table ref="table" size="mini"
                        :height="showSearch ? 'calc(100vh - 320px)' : 'calc(100vh - 260px)'" style="width: 100%"
                        v-loading="loading" border :data="inspectionPathHazardsList"
                        @selection-change="handleSelectionChange" :row-style="rowStyle" @row-click="handleRowClick"
                        :header-cell-style="{ 'text-align': 'center' }" :cell-style="{ 'text-align': 'center' }">
                        <el-table-column type="selection" width="50" align="center" fixed />
                        <el-table-column fixed label="序号" type="index" width="50">
                            <template v-slot="scope">
                                {{ scope.$index + (queryParams.pageNum - 1) * queryParams.pageSize + 1 }}
                            </template>
                        </el-table-column>
                        <el-table-column label="省份" align="center" prop="province" width="80" show-overflow-tooltip
                            v-if="cmpColumns('省份')" />
                        <el-table-column label="市/州" align="center" prop="city" width="80" show-overflow-tooltip
                            v-if="cmpColumns('市/州')" />
                        <el-table-column label="管理处" align="center" prop="managementOffice" width="120"
                            show-overflow-tooltip v-if="cmpColumns('管理处')" />
                        <el-table-column label="分处" align="center" prop="subOffice" width="120" show-overflow-tooltip
                            v-if="cmpColumns('分处')" />
                        <el-table-column label="养护路段" align="center" prop="maintenanceSection" width="120"
                            show-overflow-tooltip v-if="cmpColumns('养护路段')" />
                        <el-table-column label="公路类型" align="center" prop="roadType" width="100" show-overflow-tooltip
                            v-if="cmpColumns('公路类型')">
                            <template slot-scope="scope">
                                <span>{{ formatRoadType(scope.row.roadType) }}</span>
                            </template>
                        </el-table-column>
                        <el-table-column label="路线编码" align="center" prop="roadNumber" width="100" show-overflow-tooltip
                            v-if="cmpColumns('路线编码')" />
                        <el-table-column label="桩号" align="center" prop="stakeNumber" width="100" show-overflow-tooltip
                            v-if="cmpColumns('桩号')">
                            <template slot-scope="scope">
                                <span>{{ formatStakeNumber(scope.row.stakeNumber) }}</span>
                            </template>
                        </el-table-column>
                        <el-table-column label="位置" align="center" prop="location" width="120" show-overflow-tooltip
                            v-if="cmpColumns('位置')" />
                        <el-table-column label="隐患等级" align="center" prop="hazardLevel" width="100"
                            show-overflow-tooltip v-if="cmpColumns('隐患等级')">
                            <template slot-scope="scope">
                                <span>{{ formatHazardLevel(scope.row.hazardLevel) }}</span>
                            </template>
                        </el-table-column>
                        <el-table-column label="隐患描述" align="center" prop="hazardDescription" show-overflow-tooltip
                            width="120" v-if="cmpColumns('隐患描述')" />
                        <el-table-column label="照片" align="center" prop="photo" width="100" v-if="cmpColumns('照片')">
                            <template slot-scope="scope">
                                <el-button v-if="scope.row.photo" type="primary" icon="el-icon-picture-outline"
                                    size="mini" circle @click.stop="previewImage(scope.row.photo)"></el-button>
                                <span v-else>无图片</span>
                            </template>
                        </el-table-column>
                        <el-table-column label="是否完成整治" align="center" prop="rectificationCompleted" width="110"
                            show-overflow-tooltip v-if="cmpColumns('是否完成整治')">
                            <template slot-scope="scope">
                                <span>{{ formatMeasures(scope.row.rectificationCompleted) }}</span>
                            </template>
                        </el-table-column>
                        <el-table-column label="已(拟)整治措施" align="center" prop="rectificationMeasures"
                            show-overflow-tooltip width="120" v-if="cmpColumns('已(拟)整治措施')" />
                        <el-table-column label="整治完成时限" align="center" prop="completionDeadline" width="120"
                            v-if="cmpColumns('整治完成时限')">
                            <template v-slot="scope">
                                <span>{{ parseTime(scope.row.completionDeadline, '{y}-{m}-{d} {h}:{m}:{s}') }}</span>
                            </template>
                        </el-table-column>
                        <el-table-column label="省级责任单位及人员" align="center" prop="provincialResponsible" width="150"
                            show-overflow-tooltip v-if="cmpColumns('省级责任单位及人员')" />
                        <el-table-column label="复核责任单位及人员" align="center" prop="reviewResponsible" width="150"
                            show-overflow-tooltip v-if="cmpColumns('复核责任单位及人员')" />
                        <el-table-column label="排查责任单位及人员" align="center" prop="inspectionResponsible" width="150"
                            show-overflow-tooltip v-if="cmpColumns('排查责任单位及人员')" />
                        <el-table-column label="备注" align="center" prop="remarks" show-overflow-tooltip width="120"
                            v-if="cmpColumns('备注')" />
                        <el-table-column label="巡查人" align="center" prop="checkerName" width="100" show-overflow-tooltip
                            v-if="cmpColumns('巡查人')" />
                        <el-table-column label="审核人" align="center" prop="auditName" width="100" show-overflow-tooltip
                            v-if="cmpColumns('审核人')" />
                        <el-table-column label="审核状态" align="center" prop="auditStatus" width="100"
                            show-overflow-tooltip v-if="cmpColumns('审核状态')">
                            <template slot-scope="scope">
                                <span>{{ formatAuditStatus(scope.row.auditStatus) }}</span>
                            </template>
                        </el-table-column>
                        <el-table-column label="审核意见" align="center" prop="auditContent" show-overflow-tooltip
                            width="120" v-if="cmpColumns('审核意见')" />
                        <el-table-column label="巡查时间(填报时间)" align="center" prop="checkTime" width="150"
                            show-overflow-tooltip v-if="cmpColumns('巡查时间(填报时间)')">
                            <template v-slot="scope">
                                <span>{{ parseTime(scope.row.checkTime, '{y}-{m}-{d} {h}:{m}:{s}') }}</span>
                            </template>
                        </el-table-column>
                        <el-table-column label="审核时间" align="center" prop="auditTime" width="120" show-overflow-tooltip
                            v-if="cmpColumns('审核时间')">
                            <template v-slot="scope">
                                <span>{{ parseTime(scope.row.auditTime, '{y}-{m}-{d}') }}</span>
                            </template>
                        </el-table-column>
                        <el-table-column label="操作" fixed="right" align="center" width="160"
                            class-name="small-padding fixed-width">
                            <template slot-scope="scope">
                                <el-button size="mini" type="text" icon="el-icon-edit" @click="handleUpdate(scope.row)"
                                    v-hasPermi="['middleData:inspectionPathHazards:edit']">修改</el-button>
                                <el-button size="mini" type="text" icon="el-icon-delete"
                                    @click="handleDelete(scope.row)"
                                    v-hasPermi="['middleData:inspectionPathHazards:remove']">删除</el-button>
                                <el-button size="mini" type="text" icon="el-icon-check" @click="handleAudit(scope.row)"
                                    v-hasPermi="['middleData:inspectionPathHazards:audit']">审核</el-button>
                            </template>
                        </el-table-column>
                    </el-table>

                    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum"
                        :limit.sync="queryParams.pageSize" @pagination="getList" />
                </div>
                <!--数据表格结束-->
            </el-col>
        </el-row>

        <!-- 添加或修改涉灾隐患点—检查步道路对话框 -->
        <el-dialog :title="title" :visible.sync="open" width="70%" append-to-body>
            <el-form ref="form" :model="form" :rules="rules" label-width="160px">
                <div class="infoBox" style="margin-bottom: 20px;">
                    <div class="infoTitle">
                        基础信息
                    </div>
                    <el-row>
                        <el-col :span="12">
                            <el-form-item label="省份" prop="province">
                                <el-input v-model="form.province" placeholder="请输入省份" />
                            </el-form-item>
                        </el-col>

                        <el-col :span="12">
                            <el-form-item label="市/州" prop="city">
                                <el-select v-model="form.city" placeholder="请选择市/州" style="width: 100%;">
                                    <el-option v-for="item in cityList" :key="item" :label="item" :value="item">
                                    </el-option>
                                </el-select>
                            </el-form-item>
                        </el-col>
                    </el-row>

                    <el-row>
                        <el-col :span="12">
                            <el-form-item label="管理处" prop="subOfficeId">
                                <selectTree :key="'field2'" v-model="form.subOfficeId" :deptType="100"
                                    :deptTypeList="[1, 3, 4]" placeholder="请选择管理处" clearable filterable />
                            </el-form-item>
                        </el-col>

                        <el-col :span="12">
                            <el-form-item label="养护路段" prop="maintenanceSectionId">
                                <RoadSection v-model="form.maintenanceSectionId" :deptId="form.subOfficeId"
                                    @change="maintenanceSectionIdChange" placeholder="请选择养护路段" style="width: 100%;" />
                            </el-form-item>
                        </el-col>
                    </el-row>

                    <el-row>
                        <el-col :span="12">
                            <el-form-item label="路线编码" prop="roadNumber">
                                <el-select v-model="form.roadNumber" placeholder="请选择路线编码" clearable filterable
                                    style="width: 100%" collapse-tags @change="routeCodeChange">
                                    <el-option v-for="item in routeList" :key="item.routeId" :label="item.routeName"
                                        :value="item.routeCode">
                                    </el-option>
                                </el-select>
                            </el-form-item>
                        </el-col>
                        <el-col :span="12">
                            <el-form-item label="公路类型" prop="roadType">
                                <el-radio-group v-model="form.roadType">
                                    <el-radio v-for="item in roadTypeOptions" :label="item.dictValue">{{
                                        item.dictLabel }}</el-radio>
                                </el-radio-group>
                            </el-form-item>
                        </el-col>
                    </el-row>
                </div>


                <div class="infoBox" style="margin-bottom: 20px;">
                    <div class="infoTitle">
                        风险评估
                    </div>
                    <el-row>
                        <el-col :span="12">
                            <el-tooltip effect="dark" content="桩号(KXXX+XXX格式)" placement="top">
                                <el-form-item label="桩号" prop="stakeNumber">
                                    <div class="input-row">
                                        <span class="dwClass">K</span>
                                        <el-input type="number" :min="0" v-model="form.beginMile1" />
                                        <span class="dwClass">-</span>
                                        <el-input type="number" :min="0" v-model="form.beginMile2" />
                                    </div>
                                </el-form-item>
                            </el-tooltip>
                        </el-col>

                        <el-col :span="12">
                            <el-form-item label="位置" prop="location">
                                <el-input v-model="form.location" placeholder="请输入位置">
                                    <el-button slot="append" icon="el-icon-location"
                                        @click="showCoordinatePicker('longlat')">坐标拾取</el-button>
                                </el-input>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row>
                        <el-col :span="12">
                            <el-form-item label="隐患等级" prop="hazardLevel">
                                <el-select v-model="form.hazardLevel" placeholder="请选择隐患等级" style="width: 100%;">
                                    <el-option v-for="item in hazardLevelOptions" :key="item.dictValue"
                                        :label="item.dictLabel" :value="item.dictValue">
                                    </el-option>
                                </el-select>
                            </el-form-item>
                        </el-col>

                        <el-col :span="12">
                            <el-form-item label="隐患描述" prop="hazardDescription">
                                <el-input v-model="form.hazardDescription" type="textarea" placeholder="请输入内容" />
                            </el-form-item>
                        </el-col>
                    </el-row>

                    <el-row>
                        <el-col :span="12">
                            <el-form-item label="是否完成整治" prop="rectificationCompleted">
                                <el-radio-group v-model="form.rectificationCompleted"
                                    @input="val => { if (val == 0) { form.completionDeadline = null } }">
                                    <el-radio v-for="item in measuresOptions" :label="item.dictValue">{{ item.dictLabel
                                        }}</el-radio>
                                </el-radio-group>
                            </el-form-item>
                        </el-col>

                        <el-col :span="12">
                            <el-form-item label="已(拟)整治措施" prop="rectificationMeasures">
                                <el-input v-model="form.rectificationMeasures" type="textarea" placeholder="请输入内容" />
                            </el-form-item>
                        </el-col>
                    </el-row>

                    <el-row>
                        <el-col :span="12" v-if="form.rectificationCompleted == 1">
                            <el-form-item label="整治完成时限" prop="completionDeadline">
                                <el-date-picker clearable v-model="form.completionDeadline" type="datetime"
                                    format="yyyy-MM-dd HH:mm:ss" placeholder="请选择整治完成时限" style="width: 100%;">
                                </el-date-picker>
                            </el-form-item>
                        </el-col>

                        <el-col :span="12">
                            <el-form-item label="排查负责人" prop="investigationManager">
                                <el-input v-model="form.investigationManager" placeholder="请输入排查负责人" />
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row>
                        <el-col :span="12">
                            <el-form-item label="排查参与人" prop="investigationNames">
                                <el-input v-model="form.investigationNames" placeholder="请输入排查参与人" />
                            </el-form-item>
                        </el-col>

                        <el-col :span="12">
                            <el-form-item label="经度" prop="longitude">
                                <el-input v-model="form.longitude" placeholder="请输入经度"
                                    @input="(v) => (form.longitude = v.replace(/[^\d.]/g, ''))">
                                    <el-button slot="append" icon="el-icon-location"
                                        @click="showCoordinatePicker('longitude')">坐标拾取</el-button>
                                </el-input>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row>
                        <el-col :span="12">
                            <el-form-item label="纬度" prop="latitude">
                                <el-input v-model="form.latitude" placeholder="请输入纬度"
                                    @input="(v) => (form.latitude = v.replace(/[^\d.]/g, ''))">
                                    <el-button slot="append" icon="el-icon-location"
                                        @click="showCoordinatePicker('latitude')">坐标拾取</el-button>
                                </el-input>
                            </el-form-item>
                        </el-col>

                        <el-col :span="12">
                            <el-form-item label="巡查时间(填报时间)" prop="checkTime">
                                <el-date-picker clearable v-model="form.checkTime" type="datetime"
                                    format="yyyy-MM-dd HH:mm:ss" placeholder="请选择巡查时间(填报时间)" style="width: 100%;">
                                </el-date-picker>
                            </el-form-item>
                        </el-col>
                    </el-row>

                    <el-row>
                        <el-col :span="12">
                            <el-form-item label="省级责任单位及人员" prop="provincialResponsible">
                                <el-input v-model="form.provincialResponsible" placeholder="请输入省级责任单位及人员" />
                            </el-form-item>
                        </el-col>

                        <el-col :span="12">
                            <el-form-item label="复核责任单位及人员" prop="reviewResponsible">
                                <el-input v-model="form.reviewResponsible" placeholder="请输入复核责任单位及人员" />
                            </el-form-item>
                        </el-col>
                    </el-row>

                    <el-row>
                        <el-col :span="12">
                            <el-form-item label="排查责任单位及人员" prop="inspectionResponsible">
                                <el-input v-model="form.inspectionResponsible" placeholder="请输入排查责任单位及人员" />
                            </el-form-item>
                        </el-col>

                        <el-col :span="12">
                            <el-form-item label="备注" prop="remarks">
                                <el-input v-model="form.remarks" type="textarea" placeholder="请输入内容" />
                            </el-form-item>
                        </el-col>
                    </el-row>
                </div>

                <div class="infoBox" style="margin-bottom: 20px;" v-if="this.form.id">
                    <div class="infoTitle">
                        照片预览
                    </div>
                    <el-row :gutter="12">
                        <el-col :span="24">
                            <el-form-item label-width="0" prop="riskDescription">
                                <div class="imgBox" v-if="formImgSrcList.length > 0">
                                    <el-timeline>
                                        <el-timeline-item v-for="item in formDataLine" :key="item.time"
                                            :timestamp="item.time" placement="top" color="#5cbb7a">
                                            <el-card class="imgBoxCard">
                                                <div class="cardMain" v-for="itemC in item.data">
                                                    <el-button class="imgDeleteBtn" type="danger" icon="el-icon-delete"
                                                        circle @click="formDeleteImg(itemC.id)"></el-button>
                                                    <div class="imgTitle">
                                                        <el-tooltip class="item" effect="dark" :content="itemC.name"
                                                            placement="top">
                                                            <i class="el-icon-info"></i>
                                                        </el-tooltip>
                                                        {{ itemC.name }}
                                                    </div>
                                                    <el-image fit="cover" class="img" :src="itemC.url"
                                                        @click="formImgPreview(itemC.imgUrl)"
                                                        :preview-src-list="formImgUrlList"></el-image>
                                                    <div class="footer">
                                                        {{ `由 ${itemC.createBy} 上传于 ${itemC.createTime}` }}
                                                    </div>
                                                    <div class="footer">
                                                        {{ itemC.remark ? `图片描述：${itemC.remark}` : '' }}
                                                    </div>
                                                </div>
                                            </el-card>
                                        </el-timeline-item>
                                    </el-timeline>
                                </div>
                                <div class="noneBox" v-else>
                                    暂无内容
                                </div>
                            </el-form-item>
                        </el-col>
                    </el-row>
                </div>
                <div class="infoBox" style="padding: 15px;">
                    <div class="infoTitle">
                        <!-- <el-tooltip content="上传填报高边坡的全貌照片及局部照片（防护设施、排水设施），不少于 3 张" placement="top">
              <i class="el-icon-question"></i>
            </el-tooltip> -->
                        上传照片
                    </div>
                    <ImageUpload v-model="formUploadList" :ownerId="formImgOwnerId" :needRemark="true" :can-sort="true"
                        storage-path="/xq/inspectionPathHazards/" platform="fykj" @input="queryImg(formImgOwnerId)"
                        ref="refImageUpload" :fileType="['png', 'jpg', 'jpeg', 'PNG', 'JPG', 'JPEG']" />
                </div>

                <!-- <el-col :span="12">
                    <el-form-item label="照片" prop="photo">
                        <el-input v-model="form.photo" placeholder="请输入照片" />
                    </el-form-item>
                </el-col>
                <el-col :span="12">
                    <el-form-item label="整治完成时限" prop="completionDeadline">
                        <el-date-picker clearable v-model="form.completionDeadline" type="date"
                            value-format="yyyy-MM-dd" placeholder="请选择整治完成时限">
                        </el-date-picker>
                    </el-form-item>
                </el-col>

                <el-col :span="12">
                    <el-form-item label="省级责任单位及人员" prop="provincialResponsible">
                        <el-input v-model="form.provincialResponsible" placeholder="请输入省级责任单位及人员" />
                    </el-form-item>
                </el-col>

                <el-col :span="12">
                    <el-form-item label="复核责任单位及人员" prop="reviewResponsible">
                        <el-input v-model="form.reviewResponsible" placeholder="请输入复核责任单位及人员" />
                    </el-form-item>
                </el-col>

                <el-col :span="12">
                    <el-form-item label="排查责任单位及人员" prop="inspectionResponsible">
                        <el-input v-model="form.inspectionResponsible" placeholder="请输入排查责任单位及人员" />
                    </el-form-item>
                </el-col>

                <el-col :span="12">
                    <el-form-item label="备注" prop="remarks">
                        <el-input v-model="form.remarks" type="textarea" placeholder="请输入内容" />
                    </el-form-item>
                </el-col>

                <el-col :span="12">
                    <el-form-item label="巡查人" prop="checkerName">
                        <el-input v-model="form.checkerName" placeholder="请输入巡查人" />
                    </el-form-item>
                </el-col>

                <el-col :span="12">
                    <el-form-item label="审核人" prop="auditName">
                        <el-input v-model="form.auditName" placeholder="请输入审核人" />
                    </el-form-item>
                </el-col>

                <el-col :span="12">
                    <el-form-item label="审核意见">
                        <editor v-model="form.auditContent" :min-height="192" />
                    </el-form-item>
                </el-col>

                <el-col :span="12">
                    <el-form-item label="巡查时间(填报时间)" prop="checkTime">
                        <el-date-picker clearable v-model="form.checkTime" type="date" value-format="yyyy-MM-dd"
                            placeholder="请选择巡查时间(填报时间)">
                        </el-date-picker>
                    </el-form-item>
                </el-col>

                <el-col :span="12">
                    <el-form-item label="审核时间" prop="auditTime">
                        <el-date-picker clearable v-model="form.auditTime" type="date" value-format="yyyy-MM-dd"
                            placeholder="请选择审核时间">
                        </el-date-picker>
                    </el-form-item>
                </el-col>-->
            </el-form>
            <div slot="footer" class="dialog-footer">
                <el-button type="primary" @click="submitForm">确 定</el-button>
                <el-button @click="cancel">取 消</el-button>
            </div>
        </el-dialog>

        <!-- 用户导入对话框 -->
        <el-dialog :title="upload.title" :visible.sync="upload.open" width="400px" append-to-body>
            <el-upload ref="upload" :limit="1" accept=".xlsx, .xls" :headers="upload.headers"
                :action="upload.url + '?updateSupport=' + upload.updateSupport" :disabled="upload.isUploading"
                :on-progress="handleFileUploadProgress" :on-success="handleFileSuccess" :auto-upload="false" drag>
                <i class="el-icon-upload"></i>
                <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
                <div class="el-upload__tip text-center" slot="tip">
                    <div class="el-upload__tip" slot="tip">
                        <el-checkbox v-model="upload.updateSupport" /> 是否更新已经存在的用户数据
                    </div>
                    <span>仅允许导入xls、xlsx格式文件。</span>
                    <el-link type="primary" :underline="false" style="font-size: 12px; vertical-align: baseline"
                        @click="importTemplate">下载模板</el-link>
                </div>
            </el-upload>
            <div slot="footer" class="dialog-footer">
                <el-button type="primary" @click="submitFileForm">确 定</el-button>
                <el-button @click="upload.open = false">取 消</el-button>
            </div>
        </el-dialog>

        <!-- 批量审核对话框 -->
        <audit-dialog :visible.sync="auditDialog.open" :title="auditDialog.title" :ids="ids" @submit="submitAudit" />

        <!-- 添加图片预览对话框 -->
        <el-dialog title="照片预览" :visible.sync="imagePreview.visible" width="800px" append-to-body center
            :close-on-click-modal="true" class="image-preview-dialog">
            <div class="image-preview-container">
                <FileUpload v-if="imagePreview.url" v-model="imagePreview.url" :key="imagePreview.url" for-view>
                </FileUpload>
            </div>
        </el-dialog>
        <!-- 坐标拾取组件 -->
        <coordinate-picker :visible.sync="coordinatePickerVisible" :initialPosition="coordinatePickerInitPosition"
            @save="handleCoordinateSave" />
    </div>
</template>

<script>
import {
    listInspectionPathHazards,
    getInspectionPathHazards,
    delData,
    addInspectionPathHazards,
    updateInspectionPathHazards,
    batchAudit,
} from '@/api/middleData/xq/inspectionPathHazards';
import { getToken } from '@/utils/auth';
import Treeselect from '@riophae/vue-treeselect';
import '@riophae/vue-treeselect/dist/vue-treeselect.css';
import CascadeSelection from '@/components/CascadeSelectionManagementOffice/index.vue'; // 管养处/路段/路线
import AuditDialog from '@/views/middleData/xq/audit/index.vue'; // 引入审核组件
import FileUpload from '@/components/FileUpload'; // 引入图片上传组件
import selectTree from "@/components/DeptTmpl/selectTree.vue";
import RoadSection from "../floodControlSignHazards/components/index.vue"

import { removeFile } from '@/api/system/fileUpload.js'
import { findFiles } from '@/api/file/index.js'
import ImageUpload from '@/views/disaster/ImageUpload.vue' // 图片上传组件

import { listByMaintenanceSectionId } from '@/api/baseData/common/routeLine'
import { listAllRoute } from '@/api/system/route'
import CoordinatePicker from '@/components/CoordinatePicker'; // 引入坐标拾取组件
import { formatStake } from "@/utils/common";
export default {
    name: 'InspectionPathHazards',
    components: {
        CascadeSelection,
        AuditDialog,
        FileUpload,
        selectTree,
        RoadSection,
        ImageUpload,
        CoordinatePicker
    },
    data() {
        const validateStartStake = (rule, value, callback) => {
            if ((this.form.beginMile1 == 0 || this.form.beginMile1) && (this.form.beginMile2 == 0 || this.form.beginMile2)) {
                callback();
            } else {
                callback(new Error('桩号(KXXX+XXX格式)不能为空'));
            }
        };
        return {
            // 遮罩层
            loading: true,
            // 选中数组
            ids: [],
            // 非单个禁用
            single: true,
            // 非多个禁用
            multiple: true,
            // 显示搜索条件
            showSearch: false,
            dictType: [],
            // 日期范围
            dateRange: [],
            // 总条数
            total: 0,
            // 涉灾隐患点—检查步道路表格数据
            inspectionPathHazardsList: null,
            // 弹出层标题
            title: '',
            // 部门树选项
            deptOptions: undefined,
            // 是否显示弹出层
            open: false,

            // 表单参数
            form: {},
            defaultProps: {
                children: 'children',
                label: 'label',
            },
            // 用户导入参数
            upload: {
                // 是否显示弹出层（用户导入）
                open: false,
                // 弹出层标题（用户导入）
                title: '',
                // 是否禁用上传
                isUploading: false,
                // 是否更新已经存在的用户数据
                updateSupport: 0,
                // 设置上传的请求头部
                headers: { Authorization: 'Bearer ' + getToken() },
                // 上传的地址
                url: process.env.VUE_APP_BASE_API + '/system/user/importData',
            },
            // 查询参数
            queryParams: {
                pageNum: 1,
                pageSize: 50,
                serialNumber: null,
                province: null,
                city: null,
                managementOffice: null,
                subOffice: null,
                maintenanceSection: null,
                roadType: null,
                roadNumber: null,
                stakeNumber: null,
                location: null,
                hazardLevel: null,
                hazardLevelList: [1, 2, 3],
                hazardDescription: null,
                photo: null,
                rectificationCompleted: null,
                rectificationMeasures: null,
                completionDeadline: null,
                provincialResponsible: null,
                reviewResponsible: null,
                inspectionResponsible: null,
                remarks: null,
                checkerName: null,
                participant: null,
                auditId: null,
                auditName: null,
                auditStatus: null,
                auditContent: null,
                beginCheckTime: null, // 开始巡查时间
                endCheckTime: null, // 结束巡查时间
                auditTime: null,
                riskLevel: null,
            },
            // 列信息
            columns: [
                { key: 1, label: `省份`, visible: true },
                { key: 2, label: `市/州`, visible: true },
                { key: 3, label: `管理处`, visible: true },
                { key: 4, label: `分处`, visible: true },
                { key: 5, label: `养护路段`, visible: true },
                { key: 6, label: `公路类型`, visible: true },
                { key: 7, label: `路线编码`, visible: true },
                { key: 8, label: `桩号`, visible: true },
                { key: 9, label: `位置`, visible: true },
                { key: 10, label: `隐患等级`, visible: true },
                { key: 11, label: `隐患描述`, visible: true },
                { key: 12, label: `照片`, visible: true },
                { key: 13, label: `是否完成整治`, visible: true },
                { key: 14, label: `已(拟)整治措施`, visible: true },
                { key: 15, label: `整治完成时限`, visible: true },
                { key: 16, label: `省级责任单位及人员`, visible: true },
                { key: 17, label: `复核责任单位及人员`, visible: true },
                { key: 18, label: `排查责任单位及人员`, visible: true },
                { key: 19, label: `备注`, visible: true },
                { key: 20, label: `巡查人`, visible: true },
                { key: 21, label: `审核人`, visible: true },
                { key: 22, label: `审核状态`, visible: true },
                { key: 23, label: `审核意见`, visible: true },
                { key: 24, label: `巡查时间(填报时间)`, visible: true },
                { key: 25, label: `审核时间`, visible: true },
            ],
            // 表单校验
            rules: {
                province: [{ required: true, message: '省份不能为空', trigger: 'blur' }],
                city: [{ required: true, message: "州/市不能为空", trigger: "blur" }],
                subOfficeId: [{ required: true, message: "管理处不能为空", trigger: "blur" }],
                maintenanceSectionId: [{ required: true, message: "养护路段不能为空", trigger: "blur" }],
                roadType: [{ required: true, message: '公路类型不能为空', trigger: 'change' }],
                roadNumber: [{ required: true, message: '路线编码不能为空', trigger: 'blur' }],
                stakeNumber: [{ required: true, validator: validateStartStake, trigger: 'blur' }],
                location: [{ required: true, message: '位置不能为空', trigger: 'blur' }],
                hazardLevel: [{ required: true, message: '隐患等级不能为空', trigger: 'blur' }],
                hazardDescription: [{ required: true, message: '隐患描述不能为空', trigger: 'blur' }],
                rectificationCompleted: [{ required: true, message: '是否完成整治不能为空', trigger: 'blur' }],
                /*  provincialResponsible: [{ required: true, message: '省级责任单位及人员不能为空', trigger: 'blur' }],
                 reviewResponsible: [{ required: true, message: '复核责任单位及人员不能为空', trigger: 'blur' }],
                 inspectionResponsible: [{ required: true, message: '排查责任单位及人员不能为空', trigger: 'blur' }], */
                auditStatus: [{ required: true, message: '审核状态不能为空', trigger: 'change' }],
            },
            // 字典数据
            hazardLevelOptions: [
                { dictLabel: '一般隐患', dictValue: 3 },
                { dictLabel: '较大隐患', dictValue: 2 },
                { dictLabel: '重大隐患', dictValue: 1 },
                { dictLabel: '无', dictValue: 0 },
            ],
            riskLevelOptions: [
                { dictLabel: '无', dictValue: 5 },
                { dictLabel: '低风险', dictValue: 4 },
                { dictLabel: '中风险', dictValue: 3 },
                { dictLabel: '较高风险', dictValue: 2 },
                { dictLabel: '高风险', dictValue: 1 },
                { dictLabel: '无', dictValue: 0 },
            ],
            measuresOptions: [
                { dictLabel: '是', dictValue: 1 },
                { dictLabel: '否', dictValue: 0 },
            ],
            // 审核状态字典
            auditStatusOptions: [
                { dictLabel: '未审核', dictValue: 0 },
                { dictLabel: '审核通过', dictValue: 1 },
                { dictLabel: '审核不通过', dictValue: 2 },
            ],
            // 公路类型字典
            roadTypeOptions: [
                { dictLabel: '高速公路', dictValue: 1 },
                { dictLabel: '普通公路', dictValue: 2 },
            ],
            // 批量审核对话框
            auditDialog: {
                open: false,
                title: '批量审核'
            },
            // 图片预览数据
            imagePreview: {
                visible: false,
                url: null
            },
            cityList: [
                '昆明市', '曲靖市', '玉溪市', '保山市', '昭通市', '丽江市', '普洱市', '临沧市',
                '楚雄彝族自治州', '红河哈尼族彝族自治州', '文山壮族苗族自治州',
                '西双版纳傣族自治州', '大理白族自治州', '德宏傣族景颇族自治州',
                '怒江傈僳族自治州', '迪庆藏族自治州'
            ],
            // 图片上传相关
            formImgSrcList: [], // 图片渲染列表
            formImgUrlList: [], // 图片预览列表
            formUploadList: '', // 图片上传列表
            formImgOwnerId: '', // 图片上传ownerId
            formImgNum: [], // 图片数量（图片集用）
            routeList: [],
            // 坐标相关
            coordinatePickerVisible: false, // 坐标拾取对话框可见性
            coordinatePickerInitPosition: "102.8207599,24.8885797", // 初始坐标
            currentCoordinateType: '', // 当前坐标类型，longitude表示经度，latitude表示纬度
        };
    },
    watch: {
        // 根据名称筛选部门树
    },
    created() {
        this.getList();
        this.listAllRoute();
        // this.getDeptTree();
        // this.getConfigKey("sys.user.initPassword").then(response => {
        //   this.initPassword = response.msg;
        // });
    },
    computed: {
        cmpColumns() {
            return (val) => {
                let el = this.columns.find(item => item.label === val)
                if (el) {
                    return el.visible
                } else {
                    return true
                }
            }
        }
    },
    methods: {
        // 格式化桩号显示
        formatStakeNumber(value) {
            return formatStake(value);
        },
        /** 查询用户列表 */
        getList() {
            this.loading = true;
            if (this.dateRange && this.dateRange.length > 0) {
                this.queryParams.beginCheckTime = this.dateRange[0];
                this.queryParams.endCheckTime = this.dateRange[1];
            } else {
                this.queryParams.beginCheckTime = null;
                this.queryParams.endCheckTime = null;
            }
            listInspectionPathHazards(this.queryParams).then((response) => {
                this.inspectionPathHazardsList = response.rows;
                this.total = response.total;
                this.loading = false;
            });
        },
        // 取消按钮
        cancel() {
            this.open = false;
            this.reset();
        },
        restImg() {
            // 重置图片
            this.formDataLine = []
            this.formImgSrcList = [] // 图片缩略图列表
            this.formImgUrlList = [] // 图片预览图列表
            this.formUploadList = '' // 图片上传列表
        },
        // 表单重置
        reset() {
            this.form = {
                serialNumber: null,
                province: '云南省',
                city: null,
                managementOffice: null,
                subOffice: null,
                maintenanceSection: null,
                roadType: null,
                roadNumber: null,
                stakeNumber: null,
                location: null,
                hazardLevel: null,
                hazardDescription: null,
                photo: null,
                rectificationCompleted: null,
                rectificationMeasures: null,
                completionDeadline: null,
                provincialResponsible: null,
                reviewResponsible: null,
                inspectionResponsible: null,
                remarks: null,
                checkerName: null,
                checkerName: null,
                auditId: null,
                checkerName: null,
                auditId: null,
                auditName: null,
                auditStatus: null,
                auditContent: null,
                checkTime: null,
                auditTime: null,
            };
            this.resetForm('form');
            this.restImg();
        },
        /** 搜索按钮操作 */
        handleQuery() {
            this.queryParams.pageNum = 1;
            this.getList();
        },
        /** 重置按钮操作 */
        resetQuery() {
            this.dateRange = [];
            this.resetForm('queryForm');
            this.queryParams.hazardLevelList = []
            this.queryParams.managementMaintenanceIds = null //  管理处ID
            this.queryParams.maintenanceSectionId = null // 养护路段id
            this.queryParams.routeCodes = null // 路线编码
            this.handleQuery();
        },
        // 多选框选中数据
        handleSelectionChange(selection) {
            this.ids = selection.map((item) => item.id);
            this.single = selection.length != 1;
            this.multiple = !selection.length;
        },
        // 表格点击勾选
        handleRowClick(row) {
            row.isSelected = !row.isSelected;
            this.$refs.table.toggleRowSelection(row);
        },
        // 勾选高亮
        rowStyle({ row, rowIndex }) {
            if (this.ids.includes(row.id)) {
                return { 'background-color': '#E1F0FF', color: '#333' };
            } else {
                return { 'background-color': '#fff', color: '#333' };
            }
        },
        /** 新增按钮操作 */
        handleAdd() {
            this.reset();
            this.open = true;
            this.title = '添加涉灾隐患点—检查步道路';
            this.formImgOwnerId = new Date().getTime().toString()
            this.form.photo = this.formImgOwnerId
        },
        /** 修改按钮操作 */
        handleUpdate(row) {
            this.reset();
            const id = row.id || this.ids;
            getInspectionPathHazards(id).then(async (response) => {
                /* 路段编码回显相关 先查询下拉列表不然会出现回显不了的情况*/
                await this.managementChange(response.data.maintenanceSectionId)

                this.form = response.data;
                this.open = true;
                this.title = '修改涉灾隐患点—检查步道路';

                /* 桩号回显相关 */
                const beginMile1 = Math.floor(Number(this.form.stakeNumber) / 1000);
                const beginMile2 = Number(this.form.stakeNumber) % 1000;
                this.$set(this.form, 'beginMile1', beginMile1);
                this.$set(this.form, 'beginMile2', beginMile2);

                /* 获取图片回显 */
                if (this.form.photo) {
                    this.formImgOwnerId = this.form.photo
                    await this.queryImg(this.formImgOwnerId)
                } else {
                    this.formImgOwnerId = new Date().getTime().toString()
                    this.form.photo = this.formImgOwnerId
                }
                this.formUploadList = this.form.photo
            });
        },
        /** 提交按钮 */
        submitForm: function () {
            /* 桩号上传相关 */
            this.form.stakeNumber = Number(this.form.beginMile1) * 1000 + Number(this.form.beginMile2) *
                1; // 起始里程
            this.$refs['form'].validate((valid) => {
                if (valid) {
                    if (this.form.id != null) {
                        updateInspectionPathHazards(this.form).then((response) => {
                            this.$modal.msgSuccess('修改成功');
                            this.open = false;
                            this.getList();
                        });
                    } else {
                        addInspectionPathHazards(this.form).then((response) => {
                            this.$modal.msgSuccess('新增成功');
                            this.open = false;
                            this.getList();
                        });
                    }
                }
            });
        },
        /** 删除按钮操作 */
        handleDelete(row) {
            const id = row.id || this.ids;
            if (row.id) { // 单个删除
                this.$modal.confirm('是否确认删除改数据？').then(function () {
                    let data = { idList: [row.id] };
                    return delData(data);
                }).then(() => {
                    this.getList();
                    this.$modal.msgSuccess("删除成功");
                }).catch(() => { });
            } else { // 批量删除
                this.$modal.confirm(`是否确认删除选中的 ${id.length} 条数据项？`).then(function () {
                    let data = { idList: id };
                    console.log(data)
                    return delData(data);
                }).then(() => {
                    this.getList();
                    this.$modal.msgSuccess("删除成功");
                }).catch(() => { });
            }
        },

        /** 导出按钮操作 */
        handleExport() {
            // 确保导出时也有日期范围参数
            if (this.dateRange && this.dateRange.length > 0) {
                this.queryParams.beginCheckTime = this.dateRange[0];
                this.queryParams.endCheckTime = this.dateRange[1];
            } else {
                this.queryParams.beginCheckTime = null;
                this.queryParams.endCheckTime = null;
            }

            // 先获取数据总数
            let queryParams = {};
            if (this.ids.length === 0) {
                queryParams = {
                    ...this.queryParams,
                    pageNum: 1,
                    pageSize: 1,
                };
            } else {
                queryParams = {
                    ids: this.ids,
                    pageNum: 1,
                    pageSize: 1,
                };
            }

            listInspectionPathHazards(queryParams).then((response) => {
                const export_count = response.total;

                const confirmMessage = this.ids.length === 0
                    ? `根据搜索条件，本次导出共有 ${export_count} 条数据，是否确认导出？`
                    : `根据选中条件，本次导出共有 ${export_count} 条数据，是否确认导出？`;

                this.$confirm(confirmMessage, '提示', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning',
                })
                    .then(() => {
                        const now = new Date();
                        const timeStr = `${now.getFullYear()}年${now.getMonth() + 1}月${now.getDate()}日${now.getHours()}时${now.getMinutes()}分${now.getSeconds()}秒`;
                        const fileName = `巡查道路隐患_${timeStr}.xlsx`;

                        // 导出时使用的参数
                        let exportParams = {};
                        if (this.ids.length === 0) {
                            exportParams = { ...this.queryParams };
                        } else {
                            exportParams = { ids: this.ids };
                        }

                        this.download('middleData/inspectionPathHazards/export', exportParams, fileName, {
                            parameterType: 'body', // 设置参数类型为 body
                            headers: {
                                'Content-Type': 'application/json', // 设置请求头为 JSON
                            }
                        });
                    })
                    .catch(() => {
                        this.$message({
                            type: 'info',
                            message: '已取消导出',
                        });
                    });
            });
        },

        /** 批量审核按钮操作 */
        handleBatchAudit() {
            if (this.ids.length === 0) {
                this.$message.warning('请至少选择一条记录进行审核');
                return;
            }
            this.auditDialog.open = true;
        },

        /** 提交审核 */
        submitAudit(formData) {
            batchAudit(formData).then(response => {
                this.$modal.msgSuccess('审核操作成功');
                this.getList();
            });
        },

        /** 导入按钮操作 */
        handleImport() {
            this.upload.title = '用户导入';
            this.upload.open = true;
        },
        /** 下载模板操作 */
        importTemplate() {
            this.download('system/user/importTemplate', {}, `user_template.xlsx`);
        },
        // 文件上传中处理
        handleFileUploadProgress(event, file, fileList) {
            this.upload.isUploading = true;
        },
        // 文件上传成功处理
        handleFileSuccess(response, file, fileList) {
            this.upload.open = false;
            this.upload.isUploading = false;
            this.$refs.upload.clearFiles();
            this.$alert(
                "<div style='overflow: auto;overflow-x: hidden;max-height: 70vh;padding: 10px 20px 0;'>" +
                response.msg +
                '</div>',
                '导入结果',
                { dangerouslyUseHTMLString: true }
            );
            this.getList();
        },
        // 提交上传文件
        submitFileForm() {
            this.$refs.upload.submit();
        },
        // 格式化隐患等级显示
        formatHazardLevel(hazardLevel) {
            const dict = this.hazardLevelOptions.find((item) => item.dictValue === hazardLevel);
            return dict ? dict.dictLabel : hazardLevel;
        },

        // 格式化风险等级显示
        formatRiskLevel(riskLevel) {
            const dict = this.riskLevelOptions.find((item) => item.dictValue === riskLevel);
            return dict ? dict.dictLabel : riskLevel;
        },

        // 格式化是否采取措施显示
        formatMeasures(status) {
            const dict = this.measuresOptions.find((item) => item.dictValue === status);
            return dict ? dict.dictLabel : status;
        },

        // 格式化审核状态显示
        formatAuditStatus(status) {
            const dict = this.auditStatusOptions.find((item) => item.dictValue === status);
            return dict ? dict.dictLabel : status;
        },

        // 格式化公路类型显示
        formatRoadType(type) {
            const dict = this.roadTypeOptions.find((item) => item.dictValue === type);
            return dict ? dict.dictLabel : type;
        },

        /** 单条记录审核按钮操作 */
        handleAudit(row) {
            this.ids = [row.id]; // 设置当前选中的记录ID
            this.auditDialog.open = true; // 打开审核对话框
            this.auditDialog.title = '审核记录';
        },

        /** 图片预览 */
        previewImage(url) {
            this.imagePreview.url = url;
            this.imagePreview.visible = true;
        },

        // 获取图片数据
        async queryImg(id) {
            await findFiles({ ownerId: id }).then(res => {
                if (res.code === 200) {
                    this.formImgSrcList = res.data.map(item => ({
                        id: item.ownerId + '-' + item.id,
                        name: item.originalFilename,
                        url: item.thumbUrl,
                        imgUrl: item.url,
                        remark: item.remark,
                        createTime: item.createTime,
                        createBy: item.createBy
                    }))
                    this.formImgUrlList = res.data.map(item => item.url)
                    this.formDataLine = this.formImgDateLine()
                }
            }).catch(() => {
                this.$message.error('图片查询失败，请重新打开表单尝试')
            })
        },
        formImgDateLine() {
            let dateLine = []
            if (this.formImgSrcList.length > 0) {
                this.formImgSrcList.forEach(item => {
                    let date = item.createTime.split('T')[0]
                    if (dateLine.length === 0) {
                        dateLine.push({
                            time: date,
                            data: [item]
                        })
                    } else {
                        let index = dateLine.findIndex(item2 => item2.time === date)
                        if (index !== -1) {
                            dateLine[index].data.push(item)
                        } else {
                            dateLine.push({
                                time: date,
                                data: [item]
                            })
                        }
                    }
                })
                // 时间线排序
                dateLine.sort((a, b) => {
                    // 将日期字符串分割并转换为日期对象
                    let dateA = new Date(a.time);
                    let dateB = new Date(b.time);
                    // 比较日期
                    return dateA - dateB;
                })
            }
            return dateLine
        },
        // 删除图片
        formDeleteImg(id) {
            this.$modal.confirm('是否确认删除该图片？').then(async () => {
                this.$modal.loading('正在删除图片，请稍候...')
                removeFile(id.split('-')[1]).then(async (res) => {
                    if (res.code === 200) {
                        this.$message.success('删除图片成功！')
                        /* // 移除预览列表图片
                        let index = this.formImgSrcList.findIndex(item => item.id === id)
                        if (index !== -1) {
                          let imgUrl = this.formImgSrcList[index].imgUrl
                          let imgIndex = this.formImgUrlList.indexOf(imgUrl)
                          if (imgIndex !== -1) {
                            this.formImgUrlList.splice(imgIndex, 1)
                          }
                        }
                        // 移除渲染列表图片
                        this.formImgSrcList = this.formImgSrcList.filter(item => item.id !== id) */
                        await this.queryImg(id.split('-')[0])
                        this.$refs.refImageUpload.getImg(this.formUploadList)
                    }
                    this.$modal.closeLoading()
                }).catch(() => {
                    this.$message.error('删除图片失败')
                    this.$modal.closeLoading()
                })
            })
        },
        // 点击预览图片时
        formImgPreview(url) {
            let index = this.formImgUrlList.findIndex(item => item === url)
            if (index !== -1) {
                let moveUrl = this.formImgUrlList.splice(index, this.formImgUrlList.length - index)
                this.formImgUrlList.unshift(...moveUrl)
            }
        },
        async maintenanceSectionIdChange(val) {
            if (this.form.maintenanceSectionId) {
                await this.managementChange(this.form.maintenanceSectionId)
            } else {
                this.listAllRoute()
            }
            this.form.maintenanceSection = val?.maintenanceSectionName
        },
        listAllRoute() {
            listAllRoute().then((res) => {
                if (res.code == 200) {
                    this.routeList = res.data || [];
                }
            })
        },
        //路段下拉选改变事件
        managementChange(routeSectionId) {
            return new Promise((resolve, reject) => {
                this.queryParams.roadNumber = null;
                this.form.roadNumber = null;
                listByMaintenanceSectionId({ maintenanceSectionId: routeSectionId }).then((res) => {
                    if (res.code == 200) {
                        res.data.forEach((item) => {
                            item.routeName = item.routeName + '(' + item.routeCode + ')';
                        });
                        this.routeList = res.data || [];
                        resolve(this.routeList)
                    } else {
                        reject(res)
                    }
                });
            })
        },
        routeCodeChange(val) {
            if (val) {
                let el = this.routeList.find(item => val == item.routeCode)
                this.form.routeName = el?.routeName
            } else {
                this.form.routeName = null
            }
        },
        /**
       * 坐标相关方法
       */
        // 显示坐标拾取组件
        showCoordinatePicker(type) {
            this.currentCoordinateType = type;
            if (type === 'longlat') {
                // 如果已有经纬度，设置初始值
                if (this.form.location) {
                    this.coordinatePickerInitPosition = this.form.location;
                } else {
                    // 默认昆明市坐标
                    this.coordinatePickerInitPosition = "102.8207599,24.8885797";
                }
            } else {
                // 如果已有经纬度，设置初始值
                if (this.form.longitude && this.form.latitude) {
                    this.coordinatePickerInitPosition = `${this.form.longitude},${this.form.latitude}`;
                } else {
                    // 默认昆明市坐标
                    this.coordinatePickerInitPosition = "102.8207599,24.8885797";
                }
            }
            this.coordinatePickerVisible = true;
        },
        // 处理坐标保存
        handleCoordinateSave(position) {
            if (!position) return;
            const coordinates = position.split(',');
            if (this.currentCoordinateType === 'longlat') {
                this.form.location = position
            } else {
                if (coordinates.length === 2) {
                    const lng = coordinates[0];
                    const lat = coordinates[1];
                    // 根据坐标类型设置表单字段
                    if (this.currentCoordinateType === 'longitude' || this.currentCoordinateType === 'latitude') {
                        this.form.longitude = lng;
                        this.form.latitude = lat;
                    }
                }
            }
        },
    },
};
</script>
<style lang="scss" scoped>
.hasTagsView .app-main[data-v-078753dd] {
    background: #f5f7fa;
}

.tableDiv {
    background-color: white;
    padding-bottom: 10px;
    overflow-x: auto;
}

/* 确保固定列可见 */
.el-table__fixed-right {
    height: 100% !important;
    z-index: 10;
}

/* 图片预览容器样式 */
.image-preview-container {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 300px;
}

/* 图片预览对话框样式 */
.image-preview-dialog .el-dialog__body {
    padding: 20px;
}

.infoBox {
    padding: 15px 15px 0 15px;
    box-sizing: border-box;
    border-radius: 6px;
    border: 1px solid #C4C4C4;
    position: relative;

    .infoTitle {
        user-select: none;
        position: absolute;
        top: 0;
        left: 0;
        padding: 0 10px;
        font-size: 14px;
        line-height: 14px;
        font-weight: bold;
        transform: translateX(15px) translateY(-50%);
        background-color: white;
    }

    .imgBox {
        height: auto;
        width: 100%;

        ::v-deep .el-card__body {
            width: 100%;
            display: flex;
            flex-direction: row;
            flex-wrap: wrap;
            align-content: flex-start;
        }

        .imgBoxCard {
            width: 100%;

            .cardMain {
                height: 260px;
                width: 33%;
                box-sizing: border-box;
                padding: 0 10px;
                display: flex;
                flex-direction: column;
                position: relative;

                .imgDeleteBtn {
                    position: absolute;
                    z-index: 1;
                    top: 20%;
                    right: 5%;
                }

                .imgTitle {
                    height: 28px;
                    width: 100%;
                    font-size: 16px;
                    font-weight: bold;
                    white-space: nowrap;
                    text-overflow: ellipsis;
                    overflow: hidden;
                }

                .img {
                    height: calc(100% - (28px + 28px));
                    width: 100%;
                    padding: 10px 0;
                    position: relative;
                    z-index: 0;
                }

                .footer {
                    height: 28px;
                    color: #888888;
                    font-size: 14px;
                }
            }
        }
    }

    .noneBox {
        user-select: none;
        height: 200px;
        width: 100%;
        display: flex;
        justify-content: center;
        align-items: center;
        color: #888888;
    }
}

.dwClass {
    font-size: 12px;
    line-height: 3;
    color: #007aff;
    margin-left: 5px;
}

.input-row {
    display: flex;
    align-items: center;
}
</style>
