<template>
  <div>
    <div class="bridge-detail">
      <el-row :gutter="isBig ? 30 : 10" style="width: 105%">
        <el-col :xs="8" :sm="6" :md="6" :lg="6" :xl="6">
          <CockpitCard title="桥梁基础信息" w="100%" :h="isBig ? 'calc(57vh - 15px)' : 'calc(57vh)'"
            :class="isBig ? 'mb-2' : 'mb-3'" :isDtl="false">
            <div class="bridge-info" v-if="info.bridgeCode">
              <div v-for="(item, index) in infoList" :key="index" class="info-list">
                <span class="name">{{ item.name }}</span>
                <span v-if="item.type === 'dict'">
                  <span class="value" v-if="info[item.value]">
                    {{dict.type[item.dict].find((i) => i.value == info[item.value]).label}}
                  </span>
                </span>
                <span v-else-if="item.type === 'stake'" class="value">{{ formatPile(info[item.value]) }}</span>
                <span v-else class="value">
                  {{ (info[item.value] ? info[item.value] : item.unit ? '0 ' : '') + (info[item.value] && item.unit ? item.unit : "")}}
                </span>
              </div>
            </div>
          </CockpitCard>
          <CockpitCard title="桥梁正面照" w="100%" :h="isBig ? 'calc(25vh + 20px)' : 'calc(25vh)'"
            :class="isBig ? 'mb-2' : 'mb-3'" :isDtl="false">
            <div v-if="info.frontPhotoId" style="
              width: 100%;
              height: 100%;
              display: flex;
              align-items: center;
              justify-content: center;
            ">
              <!-- <ImagePreview :owner-id="info.frontPhotoId" width="95%" height="92%"></ImagePreview> -->
              <ImgView :owner-id="info.frontPhotoId" width="100%" height="100%" />
            </div>
            <div v-else class="img-empty">暂无照片</div>
          </CockpitCard>
        </el-col>
        <el-col :xs="8" :sm="6" :md="6" :lg="6" :xl="6">
          <CockpitCard title="巡查情况" w="100%" :h="isBig ? 'calc(57vh - 15px)' : 'calc(57vh)'"
            :class="isBig ? 'mb-2' : 'mb-3'" :isDtl="false">
            <Tables :columns="pColumns" :data="pData"></Tables>
          </CockpitCard>
          <div class="flex">
            <CockpitCard title="桥梁立面照" w="50%" :isDtl="false" :h="isBig ? 'calc(25vh + 20px)' : 'calc(25vh)'"
              :class="isBig ? 'mb-2' : 'mb-3'">
              <div v-if="info.facadePhotoId" style="
                width: 100%;
                height: 100%;
                display: flex;
                align-items: center;
                justify-content: center;
              ">
                <!-- <ImagePreview :owner-id="info.facadePhotoId" width="90%" height="92%"></ImagePreview> -->
                <ImgView :owner-id="info.facadePhotoId" width="100%" height="100%" />
              </div>
              <div v-else class="img-empty">暂无照片</div>
            </CockpitCard>
            <CockpitCard title="桥梁典型照" w="50%" :isDtl="false" :h="isBig ? 'calc(25vh + 20px)' : 'calc(25vh)'"
              :class="isBig ? 'mb-2' : 'mb-3'" class="ml-3">
              <div v-if="info.typicalPhotoId" style="
                width: 100%;
                height: 100%;
                display: flex;
                align-items: center;
                justify-content: center;
              ">
                <!-- <ImagePreview :owner-id="info.typicalPhotoId" width="90%" height="92%"></ImagePreview> -->
                <ImgView :owner-id="info.typicalPhotoId" width="100%" height="100%" />
              </div>
              <div v-else class="img-empty">暂无照片</div>
            </CockpitCard>
          </div>
        </el-col>
        <el-col :xs="8" :sm="6" :md="6" :lg="6" :xl="6">
          <CockpitCard title="桥梁技术状况评定" w="100%" :h="isBig ? 'calc(41vh)' : '41vh'" class="mb-3" :isDtl="false">
            <Echarts :option="pdOption" v-if="pdOption" height="40vh" :key="pdKey" />
          </CockpitCard>
          <CockpitCard title="桥梁经常检查情况" w="100%" :h="isBig ? 'calc(41vh - 6px)' : '41vh'" :isDtl="false" style="">
            <div class="inspect-info">
              <vue-seamless-scroll :data="inspectList" :class-option="optionHover" class="seamless-warp">
                <div v-for="(item, index) in inspectList" :key="'in' + index" class="list">
                  <span class="name">{{ item.name }}</span>
                  <div :class="item.value == 1 ? 'normal' : item.value == '无' ? 'success' : 'normal'">
                    <span></span>
                    未见异常
                    <!-- {{ item.value =='无' ? '未见异常' : item.value }} -->
                  </div>
                </div>
              </vue-seamless-scroll>
            </div>
          </CockpitCard>
        </el-col>
        <el-col :xs="8" :sm="6" :md="6" :lg="6" :xl="6">
          <CockpitCard title="最新技术评定结果" w="100%" :h="isBig ? 'calc(87vh)' : 'calc(87vh + 3px)'" :isDtl="false">
            <Echarts :option="option" v-if="option" height="25vh" :key="dpKey" />
            <div class="pr-table">
              <Tables :columns="prColumns" :data="info.positions"></Tables>
            </div>
            <div class="mt-advice">
              <div class="advice-name">
                <img src="@/assets/cockpit/mt-advice.png" />
                <span>养护意见</span>
              </div>
              <div class="advice-cont">
                {{ info.suggestion || "桥梁整体状况较为良好，应进行“日常养护、预防养护”针对较为严重的病害现象建议立即进行处置。" }}
              </div>
            </div>
          </CockpitCard>
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<script>
import * as echarts from "echarts";
import { isBigScreen } from "../../util/utils";
import CockpitCard from "../cockpitCard.vue";
import Tables from "../tables.vue";
import Echarts from "../echarts/echarts.vue";
import { getBridgeInfo, getBridgeBaseInfo, selectWithResult, getByCondition } from "@/api/cockpit/index";
import ImgView from "@/components/ImagePreview/imgView.vue";

export default {
  inject: ['sub'],
  components: {
    CockpitCard,
    Tables,
    Echarts,
    ImgView
  },
  dicts: [
    "bridge_main_superstructure_type",
    "bridge_span_classify",
    "bridge_periodic_detection_level",
    "sys_route_direction",
  ],

  data() {
    return {
      isBig: isBigScreen(),
      info: {},
      bridgeId: "1869222701873262594",
      bridgeAssetId: '',
      year: new Date().getFullYear(),
      infoList: [
        {
          name: "管养单位：",
          value: "managementMaintenanceName",
        },
        {
          name: "路线编码：",
          value: "routeCode",
        },
        {
          name: "路段名称：",
          value: "maintenanceSectionName",
        },
        {
          name: "桥梁名称：",
          value: "bridgeName",
        },
        {
          name: "桥梁编码：",
          value: "bridgeCode",
        },
        {
          name: "桥位桩号：",
          value: "centerStake",
          type: "stake",
        },
        {
          name: "桥梁长度：",
          value: "totalLength",
          unit: "米",
        },
        {
          name: "结构形式：",
          value: "mainSuperstructureType",
          type: "dict",
          dict: "bridge_main_superstructure_type",
        },

        {
          name: "跨径组合：",
          value: "spanGroups",
        },
        {
          name: "跨径类型：",
          value: "spanClassifyType",
          type: "dict",
          dict: "bridge_span_classify",
        },
        {
          name: "桥梁位置：",
          value: "direction",
          type: "dict",
          dict: "sys_route_direction",
        },
        {
          name: "下一次评定日期：",
          value: "",
        },
        {
          name: "养护工程处置次数：",
          value: "",
        },
        {
          name: "日常养护次数：",
          value: "",
        },
        {
          name: "本月日常巡查次数：",
          value: "dailyCount",
          unit: "次",
        },
        {
          name: "本月经常检查次数：",
          value: "oftenCount",
          unit: "次",
        },
      ],
      pColumns: [
        {
          label: "巡查时间",
          prop: "checkTime",
        },
        {
          label: "巡查人",
          prop: "oprUserName",
        },
        {
          label: "天气状况",
          prop: "weather",
        },
        {
          label: "是否异常",
          prop: "isException",
        },
      ],
      pData: [

      ],
      pdOption: null,
      inspectList: [],
      option: null,
      prColumns: [
        {
          label: "部件名称",
          prop: "name",
        },
        {
          label: "部件得分",
          prop: "score",
        },
        {
          label: "部件等级",
          prop: "level",
        },
      ],
      dpKey: "dpKey",
      pdKey: "pdKey"
    };
  },
  computed: {
    // 滚动设置
    optionHover() {
      return {
        hoverStop: true,
        step: 0.2, // 数值越大速度滚动越快
        limitMoveNum: 2, // 开始无缝滚动的数据量 this.dataList.length
        direction: 1, // 0向下 1向上 2向左 3向右
        openWatch: true, // 开启数据实时监控刷新dom
        singleHeight: 0, // 单步运动停止的高度(默认值0是无缝不停止的滚动) direction => 0/1
        singleWidth: 0, // 单步运动停止的宽度(默认值0是无缝不停止的滚动) direction => 2/3
        waitTime: 1000, // 单步运动停止的时间(默认值1000ms)
      };
    },
  },
  methods: {
    async init() {
      this.initSelectWithResult()
      await this.initBaseInfo();

    },

    async initGetByCondition() {
      const { data } = await getByCondition({ bridgeId: this.bridgeAssetId, year: this.year, type: 2 });
      if (data) {
        let list = [];
        data.forEach(element => {
          list.push({
            name: element.partsTypeName,
            value: element.defect
          })
        });
        this.inspectList = list;
      }
    },

    async initBaseInfo() {
      this.pdOption = null
      this.option = null
      const { data } = await getBridgeInfo({ id: this.bridgeId, years: this.year });
      if (data) {
        this.bridgeAssetId = data.assetId;
        this.initGetByCondition()
        data.checkDate = new Date(data.checkDate)
          .toLocaleDateString("zh-CN", {
            year: "numeric",
            month: "long",
          })
          .replace("年", "年")
          .replace("月", "月");
        data.levelName =
          this.dict.type.bridge_periodic_detection_level.find(
            (i) => i.value == data.level
          )?.label || "";
        data.positions?.map((item) => {
          item.level = item.level + "类";
        });
        this.info = { ...data }
        this.$emit('update:dTitle', this.info.bridgeName)
        // const data1 = await getBridgeBaseInfo({assetId:this.bridgeId});
        // if(data1 && data1.data){
        //   this.info = {...data,...data1.data};
        // }
        // this.initPdEcharts();
        await getBridgeBaseInfo({ assetId: this.bridgeId }).then(res => {
          if (res && res.data) {
            delete res?.data?.managementMaintenanceName;
            this.info = { ...data, ...res.data };
          }
        }).finally(() => {
          // 评定等级
          this.initPdEcharts();
          // 总得分
          this.initEcharts();
        })
      }
    },

    initSelectWithResult() {
      selectWithResult({ bridgeId: this.bridgeId, year: this.year, type: 1 }).then(res => {
        if (res.code == 200) {
          this.pData = res.data?.map((item) => {
            return {
              checkTime: item.checkTime,
              oprUserName: item.oprUserName,
              weather: item.weather,
              isException: item.isException ? "是" : "否",
            };
          });
        }
      });
    },
    initPdEcharts() {
      // this.info.evaluateList = Object.values(this.info.evaluateList).flat();
      if (this.info.evaluateList && Array.isArray(this.info.evaluateList) && this.info.evaluateList.length == 0) return
      this.info.evaluateList = this.info.evaluateList.sort(
        (a, b) => a.year - b.year
      );
      const data = this.info.evaluateList.map((item) =>
        Number(item.totalScore)
      );
      const sideData = data?.map((item) => item + 1) || [];
      let checkDate = this.info.checkDate || '';
      let levelName = this.info.levelName || '';
      let score = this.info.score || '';
      this.pdOption = {
        backgroundColor: "rgba(0,0,0,0)",
        title: [
          {
            text: `${checkDate}评定结果：${levelName}(${score}分)`,
            left: "center",
            top: "2%",
            textStyle: {
              color: "#42ABFF",
              fontSize: this.isBig ? 32 : 16,
            },
          },
        ],
        grid: {
          top: "20%",
          left: "3%",
          right: "5%",
          bottom: "2%",
          containLabel: true,
        },
        xAxis: {
          type: 'category',
          name: '',
          nameTextStyle: {
            color: "#999999",
            algin: 'right',
            fontSize: this.isBig ? 25 : 14,
            padding: [6, 0, 0, -10],
            verticalAlign: 'top',
          },
          data: this.info.evaluateList?.map((item) => item.year),
          //坐标轴
          axisLine: {
            show: true,
            lineStyle: {
              color: "rgba(26,139,255,0.5)",
              type: 'dashed'
            },
          },
          //坐标值标注
          axisLabel: {
            show: true,
            textStyle: {
              color: "#fff",
              fontSize: this.isBig ? 25 : 14,
            },
          },
        },
        yAxis: [
          {
            type: "value",
            name: "技术状况评分",
            nameTextStyle: {
              color: "#999999",
              algin: 'left',
              fontSize: this.isBig ? 25 : 14,
              padding: [0, 0, 10, 0],
            },
            //坐标轴
            axisLine: {
              show: true,
              lineStyle: {
                color: 'rgba(110,112,121,0.5)',
                width: 1,
              },
            },
            //坐标值标注
            axisLabel: {
              show: true,
              textStyle: {
                color: "#999999",
                fontSize: this.isBig ? 25 : 14,
              },
            },
            //分格线
            splitLine: {
              show: false,
              lineStyle: {
                color: "rgba(110,112,121,0.5)",
              },
            },
            splitArea: {
              show: false,
            },
            interval: 10,
          },
          {
            type: "value",
            name: '技术状况等级',
            nameTextStyle: {
              color: "#1A8BFF",
              algin: 'right',
              fontSize: this.isBig ? 25 : 14,
              padding: [0, 60, 10, 0],
            },
            position: 'right',
            //分格线
            splitLine: {
              show: true,
              lineStyle: {
                color: "#1A8BFF",
                type: 'dashed',
                align: 'center',
              },
            },
          }
        ],
        series: [
          {
            name: "a",
            tooltip: {
              show: false,
            },
            label: {
              normal: {
                show: true,
                position: "top",
                fontSize: this.isBig ? 25 : 14,
                color: "#fff",
                offset: [0, -10],
              },
            },
            type: "bar",
            barWidth: 24.5,
            itemStyle: {
              normal: {
                color: new echarts.graphic.LinearGradient(
                  0,
                  1,
                  0,
                  0,
                  [
                    {
                      offset: 0,
                      color: "rgba(0,255,251,0.1)", // 0% 处的颜色
                    },
                    {
                      offset: 0.6,
                      color: "rgba(0,255,251,0.6)", // 60% 处的颜色
                    },
                    {
                      offset: 1,
                      color: "rgba(0,255,251,1)", // 100% 处的颜色
                    },
                  ],
                  false
                ),
              },
            },
            data: data,
            barGap: 0,
            markLine: {
              symbol: "none",
              silent: true,
              label: {
                position: 'middle',
                formatter: '{b}',
                color: '#1A8BFF',
                fontSize: this.isBig ? 25 : 13,
              },
              data: [
                {
                  name: '5类',
                  yAxis: 40,
                  lineStyle: {
                    color: 'rgba(26,139,255,0.5)',
                    width: 1,
                    type: 'dashed'
                  },
                  label: {
                    position: 'end',
                    formatter: '\n{b}',
                    offset: [-20, 50] // 右侧 -20，上方 40
                  },
                },
                {
                  name: '4类',
                  yAxis: 60,
                  lineStyle: {
                    color: 'rgba(26,139,255,0.5)',
                    width: 1,
                    type: 'dashed'
                  },
                  label: {
                    position: 'end',
                    formatter: '\n{b}',
                    offset: [-20, 25] // 右侧 -20，上方 40
                  },
                },
                {
                  name: '3类',
                  yAxis: 80,
                  lineStyle: {
                    color: 'rgba(26,139,255,0.5)',
                    width: 1,
                    type: 'dashed'
                  },
                  label: {
                    position: 'end',
                    formatter: '\n{b}',
                    offset: [-20, 25] // 右侧 -20，上方 40
                  },
                },
                {
                  name: '2类',
                  yAxis: 90,
                  lineStyle: {
                    color: 'rgba(26,139,255,0.5)',
                    width: 1,
                    type: 'dashed'
                  },
                  label: {
                    position: 'end',
                    formatter: '\n{b}',
                    offset: [-20, 10] // 右侧 -20，上方 40
                  },
                },
                {
                  name: '1类',
                  yAxis: 100,
                  lineStyle: {
                    color: 'rgba(26,139,255,0.5)',
                    width: 1,
                    type: 'dashed'
                  },
                  label: {
                    position: 'end',
                    formatter: '\n{b}',
                    offset: [-20, 10] // 右侧 -20，上方 40
                  },
                },
              ],
            }
          },
          {
            type: "bar",
            barWidth: 8,
            itemStyle: {
              normal: {
                color: new echarts.graphic.LinearGradient(
                  0,
                  1,
                  0,
                  0,
                  [
                    {
                      offset: 0,
                      color: "rgba(0,255,251,0.1)", // 0% 处的颜色
                    },
                    {
                      offset: 0.6,
                      color: "rgba(0,255,251,0.4)", // 60% 处的颜色
                    },
                    {
                      offset: 1,
                      color: "rgba(0,255,251,0.6)", // 100% 处的颜色
                    },
                  ],
                  false
                ),
              },
            },
            barGap: 0,
            data: sideData,
          },
          {
            name: "b",
            tooltip: {
              show: false,
            },
            type: "pictorialBar",
            itemStyle: {
              borderWidth: 1,
              borderColor: "rgba(125,255,253,0.7)",
              color: "#7DFFFD",
            },

            symbol: "path://M 0,0 l 120,0 l -30,60 l -120,0 z",
            symbolSize: ["30", "12"],
            symbolOffset: ["0", "-8"],
            symbolRotate: -15,
            symbolPosition: "end",
            data: data,
            z: 3,
          },
        ],
      };
      this.pdKey = 'pdKey' + new Date().getTime();
    },
    initEcharts() {
      let angle = 0; //角度，用来做简单的动画效果的
      let num = Number(this.info.score) || "-";
      let unit = "分";
      let title = "总体得分";
      this.option = {
        tooltip: {
          formatter: "{a} <br/>{b} : {c}%",
        },
        title: [
          {
            // text: num + "%",
            text: "{a|" + num + "}{b| " + unit + "}\n{c|" + title + "}",
            x: "center",
            y: "center",
            textStyle: {
              fontSize: "30",
              color: "#fff",
              fontFamily: "Lato",
              foontWeight: "600",
              rich: {
                a: {
                  fontSize: this.nowSize(20),
                  color: "#fff",
                  fontWeight: "700",
                },
                b: {
                  fontSize: this.nowSize(8),
                  color: "#B6B6B6",
                  padding: [0, 0, 0, 3],
                },
                c: {
                  fontSize: this.nowSize(12),
                  color: "#1CFFBC",
                  padding: [5, 0],
                  fontWeight: "400",
                },
              },
            },
          },
        ],
        grid: {
          top: "10%",
          left: "10%",
          right: "10%",
          bottom: "10%",
          containLabel: true,
        },
        series: [
          //内圆
          {
            type: "pie",
            radius: "55%",
            center: ["50%", "50%"],
            animation: false,
            z: 3,
            itemStyle: {
              normal: {
                color: "rgba(28,255,188,0.1)",
                label: {
                  show: false,
                },
                labelLine: {
                  show: false,
                },
                borderColor: "#1CFFBC",
                borderWidth: 1,
              },
            },
            hoverAnimation: false,
            label: {
              show: false,
            },
            tooltip: {
              show: false,
            },
            data: [100],
          },
          // 进度光环
          {
            name: "外部刻度",
            type: "gauge",
            z: 2,
            center: ["50%", "50%"],
            radius: "80%",
            min: 0, //最小刻度
            max: 100, //最大刻度
            splitNumber: 10, //刻度数量
            startAngle: 225,
            endAngle: -45,
            axisLine: {
              show: true,
              roundCap: true,
              lineStyle: {
                width: 10,
                color: [[num / 100, "#1CFFBC"]],
              },
            }, // 仪表盘轴线
            axisLabel: {
              show: false,
            }, //刻度标签。
            axisTick: {
              show: false,
            }, //刻度样式
            splitLine: {
              show: false,
            }, //分隔线样式
            detail: {
              show: false,
            }, //仪表盘详情，用于显示数据
            pointer: {
              show: false,
            }, //仪表盘指针。
          },
          // 进度光环背景
          {
            name: "外部刻度",
            type: "gauge",
            z: 1,
            center: ["50%", "50%"],
            radius: "80%",
            min: 0, //最小刻度
            max: 100, //最大刻度
            splitNumber: 10, //刻度数量
            startAngle: 225,
            endAngle: -45,
            animation: true,
            axisLine: {
              show: true,
              roundCap: true, // 设置为圆角
              lineStyle: {
                width: 10,
                color: [[1, "rgba(23,116,255,0.2)"]],
                borderRadius: 25,
                borderWidth: 10,
              },
            }, // 仪表盘轴线
            axisLabel: {
              show: false,
            }, //刻度标签。
            axisTick: {
              show: false,
            }, //刻度样式
            splitLine: {
              show: false,
            }, //分隔线样式
            detail: {
              show: false,
            }, //仪表盘详情，用于显示数据
            pointer: {
              show: false,
            }, //仪表盘指针。
            itemStyle: {
              normal: {
                borderRadius: 25,
              },
            },
          },
        ],
      };
      this.dpKey = 'dpKey' + new Date().getTime();
    },
    nowSize(val) {
      let w =
        window.innerWidth ||
        window.screen.width ||
        window.screen.availWidth ||
        1920;
      let initW = this.isBig ? w * 3 : 1920;
      let size = (val * initW) / w;
      return size;
    },
    async getBridgeId() {
      return new Promise((resolve, reject) => {
        this.getConfigKey('default_cockpit_bridge_id').then(res => {
          if (res && res.code == 200) {
            this.bridgeId = res.data || res.msg;
            resolve(res.data || res.msg);
          } else {
            reject(res.msg || '获取桥梁ID失败');
          }
        })
      })
    }
  },
  async created() {
    window.$Bus.$on('aMapClick', async (data) => {
      if (data.sys_dept_id) return
      this.pdOption = null;
      this.option = null;
      this.bridgeId = data.id
      await this.init()
      window.$Bus.$off('aMapClick', () => { })
    })

    window.$Bus.$on('pageSelectYear', async (data) => {
      this.year = data;
      this.init();
    })
  },
  async mounted() {
    let query = this.$route?.query
    if (query && query.id) {
      this.bridgeId = query.id
    } else if (this.sub && this.sub.id) {
      this.bridgeId = this.sub.id
    } else {
      await this.getBridgeId()
    }
    this.init();
  },
  destroyed() {
    window.$Bus.$off('aMapClick')
    window.$Bus.$off('pageSelectYear')
  },
};
</script>

<style lang="scss" scoped>
@import "@/assets/styles/utils.scss";



.bridge-detail {
  width: 100%;
  display: flex;
  padding: 0 vwpx(30px);
  margin-top: vwpx(15px);

  .bridge-info {
    padding: vwpx(20px);

    .info-list {
      width: 100%;
      height: vwpx(70px);
      background-color: rgba(23, 116, 255, 0.1);
      margin: vwpx(4px) 0;
      padding: 0 vwpx(10px);

      display: flex;
      align-items: center;

      .name {
        font-family: Microsoft YaHei, Microsoft YaHei;
        font-weight: 400;
        font-size: 1.25vh;
        color: #42abff;
      }

      .value {
        font-family: Microsoft YaHei, Microsoft YaHei;
        font-weight: 400;
        font-size: 1.2vh;
        color: #ffffff;
        margin-left: vwpx(20px);
      }
    }
  }

  .inspect-info {
    padding: vwpx(20px);
    height: 100%;
    overflow-x: hidden;
    overflow-y: auto;

    .list {
      height: vwpx(64px);
      background-color: rgba(23, 116, 255, 0.1);

      display: flex;
      align-items: center;
      justify-content: space-between;
      margin: vwpx(6px) 0;
      padding: 0 vwpx(10px);

      .name {
        font-family: Microsoft YaHei, Microsoft YaHei;
        font-weight: 400;
        font-size: vwpx(26px);
        color: #00cbff;
      }

      .normal {
        font-family: Microsoft YaHei, Microsoft YaHei;
        font-weight: 400;
        font-size: vwpx(26px);
        color: #1cffbc;
        display: flex;
        align-items: center;

        span {
          width: vwpx(15px);
          height: vwpx(15px);
          display: block;
          border-radius: 50%;
          background-color: #1cffbc;
          margin-right: vwpx(10px);
        }
      }

      .error {
        font-family: Microsoft YaHei, Microsoft YaHei;
        font-weight: 400;
        font-size: vwpx(26px);
        color: #f53f3f;
        display: flex;
        align-items: center;

        span {
          width: vwpx(15px);
          height: vwpx(15px);
          display: block;
          border-radius: 50%;
          background-color: #f53f3f;
          margin-right: vwpx(10px);
        }
      }

      .success {
        font-family: Microsoft YaHei, Microsoft YaHei;
        font-weight: 400;
        font-size: vwpx(26px);
        color: #1cffbc;
        display: flex;
        align-items: center;

        span {
          width: vwpx(15px);
          height: vwpx(15px);
          display: block;
          border-radius: 50%;
          background-color: #1cffbc;
          margin-right: vwpx(10px);
        }
      }
    }
  }

  .year-selector {
    z-index: 99;
    height: 4vh;
    position: absolute;
    top: vwpx(20px);
    right: vwpx(40px);

    ::v-deep .el-input {
      .el-input__inner {
        width: vwpx(220px);
        height: vwpx(70px);
        background-color: rgba(1, 102, 254, 0.2);
        border: 1px solid #0166fe;
        color: #ffffff;
        font-size: vwpx(30px);
      }

      .el-input__inner::placeholder {
        color: #bbbbbb;
      }

      .el-input-group__append {
        background-color: rgba(1, 102, 254, 0.2);
        border: 1px solid #0166fe;
        color: #ffffff;
        border-left: none;
        padding: 0 10px;
        cursor: pointer;
      }
    }

    ::v-deep .select-popper {
      background-color: rgba(1, 102, 254, 0.2);
      border: 1px solid #0166fe;
      color: #ffffff !important;
      font-size: vwpx(30px);
      margin: vwpx(10px) 0;
    }

    ::v-deep .el-select-dropdown__item {
      color: #ffffff !important;
      font-size: vwpx(30px);
      margin: vwpx(15px) 0;
    }

    ::v-deep .el-select-dropdown__item.selected {
      color: #42abff !important;
    }
  }

  .pr-table {
    border: 1px solid #2cb5ff;
    margin: 0 vwpx(20px);
    height: 44vh;
  }

  .mt-advice {
    border: 1px solid #2cb5ff;
    height: vwpx(320px);
    background-color: rgba(23, 116, 255, 0.1);
    margin: vwpx(40px) vwpx(20px);

    display: flex;
    align-items: center;

    .advice-name {
      display: flex;
      align-items: center;
      justify-content: center;
      flex-direction: column;
      margin: 0 vwpx(20px);

      img {
        width: vwpx(130px);
        height: vwpx(130px);
      }

      span {
        font-family: Source Han Sans, Source Han Sans;
        font-weight: 500;
        font-size: 1.35vh;
        color: #42abff;
        margin-top: vwpx(20px);
      }
    }

    .advice-cont {
      flex: 1;
      height: 100%;
      background: rgba(23, 116, 255, 0.1);
      font-family: Microsoft YaHei UI, Microsoft YaHei UI;
      font-weight: 400;
      font-size: 1.3vh;
      color: #ffffff;

      display: flex;
      align-items: center;
      padding: vwpx(30px);
    }
  }

  .flex {
    display: flex;
  }

  .flex-1 {
    flex: 1;
  }

  .mb-2 {
    margin-bottom: vwpx(20px);
  }

  .mb-3 {
    margin-bottom: vwpx(30px);
  }

  .ml-3 {
    margin-left: vwpx(30px);
  }

  .img {
    width: 100%;
    height: 100%;
    padding: 5px;
    cursor: pointer;
    object-fit: fill;
  }

  .img-empty {
    height: calc(100% - 15px);
    margin: vwpx(20px);
    background: #01315d;
    font-family: Microsoft YaHei, Microsoft YaHei;
    font-weight: 400;
    font-size: vwpx(24px);
    color: rgba(255, 255, 255, 0.6);
    border-radius: vwpx(5px);

    display: flex;
    align-items: center;
    justify-content: center;
  }
}

.seamless-warp {
  height: 100%;
  overflow: hidden;
}
</style>
