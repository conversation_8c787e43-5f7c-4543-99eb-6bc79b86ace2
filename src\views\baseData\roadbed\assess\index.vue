<template>
  <PageContainer>
    <template slot="search">
      <div style="display: flex; align-items: center; margin: 0">
       
        <CascadeSelection
          :form-data="queryParams"
          v-model="queryParams"
          types="201"
          multiple
        />
        <el-select
          style="margin-left: 15px;"
          v-model="queryParams.direction"
          placeholder="调查方向"
          clearable
          
          collapse-tags
        >
          <el-option
            v-for="dict in dict.type.sys_route_direction"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
   
        <RangeInput
          :clearData="clearData"
          @startValue="
            (v) => {
              queryParams.startStake = v;
            }
          "
          @endValue="
            (v) => {
              queryParams.endStake = v;
            }
          "
        />
        <el-select
          style="margin-left: 15px;"
          v-model="queryParams.hasRecords"
          placeholder="是否有养护记录"
          clearable
          
          collapse-tags
        >
          <el-option
            v-for="dict in dict.type.base_data_yes_no"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>

        <div style="min-width:240px;margin-left: 15px;">
          <el-button
            v-hasPermi="['baseData:pavementAssess:listPage']"
            type="primary"
            icon="el-icon-search"
            @click="handleQuery"
          >搜索</el-button>
          <el-button
            icon="el-icon-refresh"
            @click="resetQuery"
          >重置</el-button>
        </div>
      </div>
    </template>
    <template slot="header">

      <div class="button-list">
        <el-button
          v-hasPermi="['baseData:pavementAssess:add']"
          type="primary"
          @click="handleAdd"
        >新增</el-button>
        <el-button
          v-hasPermi="['baseData:pavementAssess:edit']"
          type="primary"
          @click="handleUpdate"
        >编辑</el-button>
        <el-button
          v-hasPermi="['baseData:pavementAssess:query']"
          type="primary"
          @click="handleView"
        >查看</el-button>
        <el-button
          v-hasPermi="['baseData:pavementAssess:remove']"
          type="primary"
          @click="handleRemove"
        >删除</el-button>
        <el-button
          type="primary" 
          v-hasPermi="['baseData:import:execute']"
          @click="importUpdate"
        >导入更新</el-button>
        <el-button
          v-hasPermi="['baseData:import:execute']"
          type="primary"
          @click="importAdd"
        >导入新增</el-button>
        <!-- <el-button
            type="primary"
            @click="importMaintain"
          >导入养护记录</el-button> -->
        <el-button
          v-hasPermi="['baseData:pavementAssess:export']"
          type="primary"
          class="mb8"
          @click="exportList"
        >数据导出</el-button>
      </div>
    </template>
    <template slot="body">
      <el-table
        v-adjust-table
        ref="table"
        v-loading="loading"
        height="99%"
        border
        :data="tableData"
        :header-cell-style="{'height': '36px'}"
        :row-style="rowStyle"
        @selection-change="handleSelectionChange"
        @row-click="handleRowClick"
      >
        <el-table-column
          fixed
          type="selection"
          width="50"
          align="center"
        />
        <el-table-column
          label="序号"
          type="index"
          width="50"
          align="center"
          fixed
        >
          <template v-slot="scope">
            {{ (scope.$index + (queryParams.pageNum - 1) * queryParams.pageSize)+1 }}
          </template>
        </el-table-column>

        <el-table-column
          label="管理处"
          align="center"
          prop="managementMaintenanceName"
          min-width="120"
          fixed
          show-overflow-tooltip
        />
        <el-table-column
          label="管养分处"
          align="center"
          prop="managementMaintenanceBranchName"
          min-width="120"
          fixed
          show-overflow-tooltip
        ></el-table-column>
        <el-table-column
          label="路线编码"
          align="center"
          prop="routeCode"
          min-width="120"
          show-overflow-tooltip
        ></el-table-column>
        <el-table-column
          label="路线名称"
          align="center"
          prop="routeName"
          min-width="120"
          show-overflow-tooltip
        ></el-table-column>
        <el-table-column
          label="路线技术等级"
          align="center"
          prop="routeLevel"
          min-width="120"
          show-overflow-tooltip
        >
          <template slot-scope="scope">
            <dict-tag
              :options="dict.type.sys_route_grade"
              :value="scope.row.routeLevel"
            />
          </template>
        </el-table-column>
        <el-table-column
          label="养护路段"
          align="center"
          prop="maintenanceSectionName"
          min-width="120"
          show-overflow-tooltip
        ></el-table-column>
        <el-table-column
          label="调查方向"
          align="center"
          prop="direction"
          min-width="120"
          show-overflow-tooltip
        >
          <template slot-scope="scope">
            <dict-tag
              :options="dict.type.sys_route_direction"
              :value="scope.row.direction"
            />
          </template>
        </el-table-column>
        <el-table-column
          label="具体方向"
          align="center"
          prop="specificDirection"
          min-width="120"
          show-overflow-tooltip
        ></el-table-column>
        
        <el-table-column
          label="路面类型"
          align="center"
          prop="pavType"
          min-width="120"
          show-overflow-tooltip
        >
          <template slot-scope="scope">
            <dict-tag
              :options="dict.type.sys_surface_type"
              :value="scope.row.pavType"
            />
          </template>
        </el-table-column>
        <el-table-column
          label="起点桩号"
          align="center"
          prop="startStake"
          min-width="120"
          show-overflow-tooltip
        >
          <template slot-scope="scope">
            <span v-if="scope.row && scope.row.startStake">
              {{ formatPile(scope.row.startStake) }}
            </span>
          </template>
        </el-table-column>
        <el-table-column
          label="终点桩号"
          align="center"
          prop="endStake"
          min-width="120"
          show-overflow-tooltip
        >
          <template slot-scope="scope">
            <span v-if="scope.row && scope.row.endStake">
              {{ formatPile(scope.row.endStake) }}
            </span>
          </template>
        </el-table-column>
        <el-table-column
          label="评定长度(m)"
          align="center"
          prop="ratingLength"
          min-width="120"
          show-overflow-tooltip
        ></el-table-column>
        <el-table-column label="养护处置记录" fixed="right" width="160" align="center" class-name="small-padding fixed-width">
            <template slot-scope="scope">
              <el-button
                size="mini"
                type="text"
                icon="el-icon-edit"
                @click.stop="checkDetail(scope.row)"
                v-if="scope.row.hasRecords"
              >查看</el-button>
            </template>
          </el-table-column>

        
      </el-table>
      <pagination
        :total="total"
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
        :pageSizes="[10, 20, 30, 50, 100, 1000]"
        @pagination="getList"
      />
    </template>
    <ImportData
      v-if="showImport"
      :is-update="isUpdate"
      :dialog-visible="showImport"
      :import-base-type="importBaseType"
      :import-type="importType"
      @close="closeImport"
    />
    <add-and-edit
      v-if="showAddEdit"
      :forView="forView"
      :formData="formData"
      :title="title"
      :showAddEdit="showAddEdit"
      @close="() => {showAddEdit = false;formData ={}}"
      @refresh="() => {showAddEdit = false;getList()}"
    />
    <Detail
      :showDetail="showManageTable"
      :choseId="choseId"
      @close="() => {showManageTable = false;}"
    />
  </PageContainer>
</template>

<script>
import CascadeSelection from '@/components/CascadeSelection/index.vue'
import ImportData from '@/views/baseData/components/importData/index.vue'
import PileInput from '@/components/PileInput/index.vue'
import { getListPage, deleteByIds } from '@/api/baseData/roadbed/assess/index'
import { getFile } from '@/api/file'
import RangeInput from "@/views/baseData/components/rangeInput/index.vue";

import AddAndEdit from './components/addAndEdit.vue'
import Detail from './components/detail.vue'
export default {
  name: 'Assess',
  props: {},
  components: { CascadeSelection, ImportData,AddAndEdit,Detail,PileInput,RangeInput },
  dicts: [
    'base_data_yes_no',
    'sys_surface_type',
    'sys_route_grade',
    'sys_route_direction',
    
  ],
  data() {
    return {
      loading: false,
      queryParams: { pageNum: 1, pageSize: 20 },
      total: 0,
      clearData: false,
      tableData: [],
      showImport: false,
      importType: 1,
      importBaseType:'30',
      isUpdate: false,
      ids: [],
      title: '',
      showAddEdit: false,
      forView: false,
      choseId: '',
      formData: {
        baseInfoForm: {}
      },
      selectItem: [],
      showManageTable:false
    }
  },
  created() {
    this.getList()
  },
  methods: {
    getList() {
      this.loading = true
      getListPage(this.queryParams)
        .then(async res => {
          if (res.code === 200) {
            this.tableData = res.rows
            this.total = res.total
            this.clearData = false;

          }
        })
        .finally(() => {
          this.loading = false
        })
    },
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    // 新增按钮操作
    handleAdd() {
      this.forView = false
      this.showAddEdit = true
      this.title = '新增路面评定单元数据'
      this.formData.baseInfoForm = {}
    },
    handleUpdate() {
      if (this.ids.length != 1) {
        this.$message.warning('请选择一条数据进行编辑！')
        return
      } else {
        this.formData.baseInfoForm = this.selectItem[0];
        this.title = '编辑路面评定单元数据'
        this.showAddEdit = true
      }
    },
    handleView(){
      if (this.ids.length != 1) {
        this.$message.warning('请选择一条数据进行查看！')
        return
      } else {
        this.formData.baseInfoForm = this.selectItem[0];
        this.title = '查看路面评定单元数据'
        this.showAddEdit = true
        this.forView=true
      }
    },
    resetQuery() {
      this.clearData = true;

      this.queryParams = {
        pageNum: 1,
        pageSize: 20
      }
      this.getList()
    },
    handleRemove() {
      if (this.ids.length === 0) {
        this.$message.warning('请选择至少一条数据进行删除！')
        return
      }
      this.$modal
        .confirm('确认删除？')
        .then(() => {
          deleteByIds(this.ids).then(res => {
            if (res.code === 200) {
              this.getList()
              this.$modal.msgSuccess('删除成功')
            }
          })
        })
        .catch(() => {})
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.selectItem = selection
    },
    // 表格点击勾选
    handleRowClick(row) {
      row.isSelected = !row.isSelected
      this.$refs.table.toggleRowSelection(row)
      // this.showManageTable=true
      // this.choseId = row.id
    },

    // 查看详情
    checkDetail(row) {
      this.showManageTable=true
      this.choseId = row.id
    },

    // 勾选高亮
    rowStyle({ row, rowIndex }) {
      if (this.ids.includes(row.id)) {
        return { 'background-color': '#b7daff', color: '#333' }
      } else {
        return { 'background-color': '#fff', color: '#333' }
      }
    },
    closeImport(v) {
      this.showImport = false
      if (v) this.getList()
    },
    // 导入更新按钮
    importUpdate() {
      this.isUpdate = true
      this.showImport = true
      this.importBaseType='30'
      this.importType = 1
    },
    // 导入新增按钮
    importAdd() {
      this.isUpdate = false
      this.showImport = true
      this.importBaseType='30'

      this.importType = 2
    },
    importMaintain(){
      this.isUpdate = false
      this.showImport = true
      this.importBaseType='32'
      this.importType = 2
    },
    // 导出清单按钮
    exportList() {
      if (this.tableData.length === 0) return;
      if (this.ids.length === 0) {
        this.$modal
          .confirm("即将导出所有表格数据，此过程可能花费时间较长，是否继续？")
          .then(() => {
            this.download(
              '/baseData/pavement/assess/export',
              this.queryParams,
              ``,
              {
                headers: { "Content-Type": "application/json;" },
                parameterType: "body",
              }
            );
          })
          .catch(() => {});
      } else {
        this.$modal
          .confirm(`已选择${this.ids.length}条路面评定单元数据，确认导出数据？`)
          .then(() => {
            this.download(
              '/baseData/pavement/assess/export',
              { ids: this.ids, ...this.queryParams },
              ``,
              {
                headers: { "Content-Type": "application/json;" },
                parameterType: "body",
              }
            );
          })
          .catch(() => {});
      }
    }
  },
  computed: {},
  watch: {}
}
</script>

<style lang="scss" scoped>
.button-list {
  border-radius: 4px;
  width: 100%;
  .el-button {
    margin-bottom: 10px;
    margin-right: 10px;
    margin-left: 0;
  }
}
</style>
