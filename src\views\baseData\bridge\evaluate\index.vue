<template>
  <PageContainer>
    <template slot="search">
      <div style="display: flex; align-items: center; margin: 0">
        <CascadeSelection
          :form-data="queryParams"
          v-model="queryParams"
          types="201"
          multiple
        />

        <div style="margin-left: 20px">
          <el-input
            v-model="queryParams.bridgeName"
            style="width:100%;"
            placeholder="桥梁名称"
            clearable
          />
        </div>
        <div style="margin-left: 20px">
          <el-input
            v-model="queryParams.bridgeCode"
            style="width:100%"
            placeholder="桥梁编码"
            clearable
          />
        </div>
        <div style="margin: 0 20px">
          <el-date-picker
            style="width: 100%"
            v-model="queryParams.checkYear"
            type="year"
            placeholder="年份"
            value-format="yyyy"
          />
        </div>
        <div style="min-width:240px; height:32px">
          <el-button
            v-hasPermi="['baseData:evaluate:listPage']"
            type="primary"
            icon="el-icon-search"
            class="mb8"
            @click="handleQuery"
          >搜索</el-button>
          <el-button
            icon="el-icon-refresh"
            class="mb8"
            @click="resetQuery"
          >重置</el-button>
        </div>
      </div>
    </template>
    <template slot="header">
      <el-row
        :gutter="10"
        class="button-list"
      >
        <el-col :span="1.5">
          <el-button
            v-hasPermi="['baseData:evaluate:export']"
            class="mb8"
            type="primary"
            @click="evaluateExport"
          >数据导出</el-button>
        </el-col>
        <el-button
          v-hasPermi="['baseData:import:execute']"
          class="mb8"
          type="primary"
          @click="importUpdate"
        >导入更新</el-button>
        <el-button
          v-hasPermi="['baseData:import:execute']"
          class="mb8"
          type="primary"
          @click="importAdd"
        >导入新增</el-button>

      </el-row>
    </template>
    <template slot="body">
      <el-table
        v-adjust-table
        ref="table"
        height="100%"
        style="width: 100%"
        :header-cell-style="{'background':'#F2F3F5','color': '#212529','font-weight': '700','font-size': '14px'}"
        :cell-style="{'height': '36px'}"
        v-loading="loading"
        border
        :data="staticList"
        @selection-change="handleSelectionChange"
        @row-click="handleRowClick"
      >
        <el-table-column
          type="selection"
          width="50"
          align="center"
        />
        <el-table-column
          fixed
          label="序号"
          type="index"
          width="50"
          align="center"
        >
          <template v-slot="scope">
            {{ (scope.$index + (queryParams.pageNum - 1) * queryParams.pageSize)+1 }}
          </template>
        </el-table-column>
        <el-table-column
          fixed
          label="桥梁名称"
          align="center"
          prop="bridgeName"
          min-width="140"
          show-overflow-tooltip
        />
        <el-table-column
          label="桥梁编码"
          align="center"
          prop="bridgeCode"
          min-width="140"
          show-overflow-tooltip
        />
        <el-table-column
          label="年度"
          align="center"
          prop="checkYear"
          min-width="140"
          show-overflow-tooltip
        />

        <el-table-column
          label="管养处名称"
          align="center"
          prop="managementMaintenanceName"
          min-width="140"
          show-overflow-tooltip
        />
        <el-table-column
          label="管养分处名称"
          align="center"
          prop="managementMaintenanceBranchName"
          min-width="140"
          show-overflow-tooltip
        />
        <el-table-column
          label="养护路段名称"
          align="center"
          prop="maintenanceSectionName"
          min-width="140"
          show-overflow-tooltip
        />
        <el-table-column
          label="路线编码"
          align="center"
          prop="routeCode"
          min-width="140"
          show-overflow-tooltip
        />
        <el-table-column
          label="路线名称"
          align="center"
          prop="routeName"
          min-width="140"
          show-overflow-tooltip
        />

        <el-table-column
          label="结构形式"
          align="center"
          prop="mainSuperstructureType"
          min-width="140"
          show-overflow-tooltip
        >
          <template slot-scope="scope">
            <span v-if="scope.row && scope.row.mainSuperstructureType">
              <DictTag
                :value="scope.row.mainSuperstructureType"
                :options="dict.type.bridge_main_superstructure_type"
              />
            </span>
          </template>
        </el-table-column>

        <!-- <el-table-column
        label="桥梁数据id"
        align="center"
        prop="bridgeId"
        min-width="140"
        show-overflow-tooltip
      /> -->
        <el-table-column
          label="检查日期"
          align="center"
          prop="checkDate"
          min-width="140"
          show-overflow-tooltip
        >
        </el-table-column>
        <el-table-column
          label="上部结构得分"
          align="center"
          prop="topSideScore"
          min-width="140"
          show-overflow-tooltip
        />
        <el-table-column
          label="上部结构等级	"
          align="center"
          prop="topSideLevel"
          min-width="140"
          show-overflow-tooltip
        >
          <template slot-scope="scope">
            <span v-if="scope.row && scope.row.topSideLevel">
              <DictTag
                :value="scope.row.topSideLevel"
                :options="dict.type.bridge_tec_condition_level"
              />
            </span>
          </template>
        </el-table-column>
        <el-table-column
          label="下部结构得分"
          align="center"
          prop="bottomSideScore"
          min-width="140"
          show-overflow-tooltip
        />
        <el-table-column
          label="下部结构等级	"
          align="center"
          prop="bottomSideLevel"
          min-width="140"
          show-overflow-tooltip
        >
          <template slot-scope="scope">
            <span v-if="scope.row && scope.row.bottomSideLevel">
              <DictTag
                :value="scope.row.bottomSideLevel"
                :options="dict.type.bridge_tec_condition_level"
              />
            </span>
          </template>
        </el-table-column>
        <el-table-column
          label="桥面系得分"
          align="center"
          prop="deckScore"
          min-width="140"
          show-overflow-tooltip
        />
        <el-table-column
          label="桥面系等级"
          align="center"
          prop="deckLevel"
          min-width="140"
          show-overflow-tooltip
        >
          <template slot-scope="scope">
            <span v-if="scope.row && scope.row.deckLevel">
              <DictTag
                :value="scope.row.deckLevel"
                :options="dict.type.bridge_tec_condition_level"
              />
            </span>
          </template>
        </el-table-column>

        <el-table-column
          label="总体得分"
          align="center"
          prop="totalScore"
          min-width="140"
          show-overflow-tooltip
        />
        <el-table-column
          label="总体等级"
          align="center"
          prop="totalLevel"
          min-width="140"
          show-overflow-tooltip
        >
          <template slot-scope="scope">
            <span v-if="scope.row && scope.row.totalLevel">
              <DictTag
                :value="scope.row.totalLevel"
                :options="dict.type.bridge_tec_condition_level"
              />
            </span>
          </template>
        </el-table-column>

        <el-table-column
          label="是否存在5类桥梁技术状况单项指标控制"
          align="center"
          prop="controlStatus"
          min-width="160"
          show-overflow-tooltip
        >
          <template slot-scope="scope">
            <span v-if="scope.row && scope.row.controlStatus">
              <DictTag
                :value="scope.row.controlStatus"
                :options="dict.type.base_data_yes_no"
              />
            </span>
          </template>
        </el-table-column>

        <el-table-column
          fixed="right"
          label=操作
          align="center"
          min-width="120"
        >
          <template slot-scope="scope">
            <el-button
              v-hasPermi="['baseData:evaluatePosition:listPage']"
              style="font-size: 14px;font-weight: 400;"
              type="text"
              @click.stop="handleView(scope.row)"
            >部件信息</el-button>
          </template>
        </el-table-column>

      </el-table>
      <pagination
        :total="total"
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
        :pageSizes="[10, 20, 30, 50, 100, 1000]"
        @pagination="getList"
      />
    </template>
    <ImportData
      v-if="showImportAdd"
      :is-update="isUpdate"
      :dialog-visible="showImportAdd"
      :import-base-type="importBaseType"
      :import-type="importType"
      @close="closeImportAdd"
    />
    <Protections
      v-if="showDetail"
      :showDetail="showDetail"
      :choseId="choseId"
      @close="() => {showDetail = false;}"
    />

  </PageContainer>
</template>

<script>
import { listBridgeEvaluateRecords } from '@/api/baseData/bridge/evaluate/index'

import { getToken } from '@/utils/auth'
import CascadeSelection from '@/components/CascadeSelection/index.vue'
import Protections from './components/protections.vue'

import ImportData from '@/views/baseData/components/importData/index.vue'

export default {
  name: 'Evaluate',
  components: { ImportData, CascadeSelection, Protections },
  dicts: [
    'base_data_yes_no',
    'bridge_tec_condition_level',
    'bridge_tec_condition_level',
    'bridge_main_superstructure_type'
  ],
  data() {
    return {
      loading: true,
      showAddEdit: false,
      forView: false,
      title: '',
      formData: {},
      showUpload: false,
      importBaseType: '6',
      isUpdate: false,
      importType: 0,
      ids: [],
      single: true,
      multiple: true,
      showDetail: false,
      choseId: null,
      showImportAdd: false,
      total: 0,
      staticList: null,
      routeCode: [],
      queryParams: {
        pageNum: 1,
        pageSize: 20,
        checkYear:new Date().getFullYear()+''
      },
      dateRange: [],

      routeCodeOptions: [],

      classification: [],
      operation: [],
      sysyes: [],
      assessment: [],
      lining: []
    }
  },
  watch: {},
  created() {
    this.getList()
    this.getOptions()
  },
  methods: {
    // 获取搜索栏相关字典数据
    getOptions() {},
    // 获取表格数据
    getList() {
      this.loading = true

      listBridgeEvaluateRecords(this.queryParams).then(response => {

        response.rows.map(item => {
          if(item.checkDate){
            let dateString = item.checkDate;
            const year = dateString.substring(0, 4);
            const month = dateString.substring(4, 6);
            const day = dateString.substring(6, 8);
            item.checkDate=year+'-'+month+'-'+day
          }
        })

        
        this.staticList = response.rows
        this.total = response.total
        this.loading = false
      })
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length != 1
      this.multiple = !selection.length
    },

    // 搜索按钮
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    // 重置按钮
    resetQuery() {
      this.routeCode = []

      this.dateRange = []
      this.queryParams = {
        pageNum: 1,
        pageSize: 20,
        checkYear:new Date().getFullYear()+''
      }
      this.handleQuery()
    },
    // 表单重置
    reset() {
      this.resetForm('form')
    },
    // 新增按钮操作
    handleAdd() {
      this.showAddEdit = true
      this.title = '新增桥梁评定数据数据'
    },
    closeImportAdd(v) {
      this.showImportAdd = false
      if (v) this.getList()
    },
    evaluateExport() {
      this.download(
        '/baseData/bridge/evaluate/export',
        { ids: this.ids, ...this.queryParams },
        ``,
        {
          headers: { 'Content-Type': 'application/json;' },
          parameterType: 'body'
        }
      )
    },

    handleDateChange() {
      this.queryParams.constructionStartDate = this.dateRange[0]
      this.queryParams.constructionEndDate = this.dateRange[1]
    },
    // 表格点击勾选
    handleRowClick(row) {
      row.isSelected = !row.isSelected
      this.$refs.table.toggleRowSelection(row)
    },

    // 导入更新按钮
    importUpdate() {
      this.isUpdate = true
      this.showImportAdd = true
      this.importType = 1
    },
    // 导入新增按钮
    importAdd() {
      this.isUpdate = false
      this.showImportAdd = true
      this.importType = 2
    },
    handleView(row) {
      this.showDetail = true
      this.choseId = row.id
    }
  }
}
</script>

<style lang="scss" scoped>


::v-deep .el-table .el-table__fixed-body-wrapper{
    top: 48px !important;
}

::v-deep .el-table {
  .tr-fixed {
    display: table-row;
    position: sticky;
    bottom: 0;
    width: 100%;
    td {
      border-top: 1px solid #f3f5fa;
      background: #fff;
    }
  }

  .fixed-row {
    bottom: 0;
  }
}

</style>
