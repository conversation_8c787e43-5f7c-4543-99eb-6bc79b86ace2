<template>
  <div>
    <el-form ref="formRef" :model="formParams" :disabled="processIsApprove === '1'" :rules="formRules"
             label-width="140px" label-position="right">
      <div class="infoBox" style="margin-bottom: 20px;" v-loading="formLoading">
        <div class="infoTitle">
          基础信息
        </div>

        <el-row :gutter="12">
          <el-col :span="12">
            <el-form-item label="灾毁类型" prop="disasterType">
              <el-input v-model="formParams.disasterType" disabled placeholder="点击右侧查询数据进行关联"/>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="关联风险点" prop="disasterRiskId">
              <div style="width: 100%; display: flex; flex-direction: row; flex-wrap: nowrap;">
                <el-input v-model="formParams.disasterRiskId" disabled
                          placeholder="点击右侧查询数据进行关联"></el-input>
                <el-button type="primary" style="margin-left: 10px;" @click="openRiskSearchDialog">查询</el-button>
              </div>
            </el-form-item>
          </el-col>
        </el-row>

      </div>

      <div class="infoBox" style="margin-bottom: 20px;" v-loading="riskFormLoading">
        <div class="infoTitle">
          关联风险点基础信息
        </div>

        <el-row :gutter="12">
          <el-col :span="12">
            <el-form-item label="灾害名称" prop="disasterName">
              <el-input v-model="riskFormParams.disasterName" disabled placeholder="关联风险点后显示"/>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="灾害编号" prop="disasterNum">
              <el-input v-model="riskFormParams.disasterNum" disabled placeholder="关联风险点后显示"/>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="12">
          <el-col :span="12">
            <el-form-item label="管养单位名称" prop="maintenanceUnitId">
              <el-input v-model="riskFormParams.maintenanceUnitName" disabled placeholder="关联风险点后显示"/>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="技术等级" prop="technicalGrade">
              <el-select disabled v-model="riskFormParams.technicalGrade" disabled placeholder="关联风险点后显示"
                         style="width: 100%;">
                <el-option v-for="dict in dict.type.sys_route_grade" :label="dict.label" :value="dict.value"
                           :key="dict.value"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="12">
          <el-col :span="12">
            <el-form-item label="路线名称" prop="routerName">
              <el-input v-model="riskFormParams.routerName" disabled placeholder="关联风险点后显示"/>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="路线编号" prop="routerNum">
              <el-input v-model="riskFormParams.routerNum" disabled placeholder="关联风险点后显示"/>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="12">
          <el-col :span="12">
            <el-form-item label="起点经纬度" prop="startLatitudeAndLongitude">
              <div style="width: 100%; display: flex; flex-direction: row; flex-wrap: nowrap;">
                <el-input v-model="riskFormParams.startLatitudeAndLongitude" disabled
                          placeholder="请输入起点经纬度，以英文逗号分隔"></el-input>
                <el-button slot="append" icon="el-icon-location" disabled>查看坐标
                </el-button>
              </div>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="止点经纬度" prop="endLatitudeAndLongitude">
              <div style="width: 100%; display: flex; flex-direction: row; flex-wrap: nowrap;">
                <el-input v-model="riskFormParams.endLatitudeAndLongitude" disabled
                          placeholder="请输入止点经纬度，以英文逗号分隔"></el-input>
                <el-button icon="el-icon-location" disabled>查看坐标</el-button>
              </div>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="12">
          <el-col :span="12">
            <el-form-item label="起点桩号" prop="pileStartNum">
              <el-input v-model="riskFormParams.pileStartNum" disabled placeholder="关联风险点后显示"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="终点桩号" prop="pileEndNum">
              <el-input v-model="riskFormParams.pileEndNum" disabled placeholder="关联风险点后显示"></el-input>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="12">
          <el-col :span="12">
            <el-form-item label="风险评分" prop="riskScore">
              <el-input v-model="riskFormParams.riskScore" disabled placeholder="关联风险点后显示"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="风险等级" prop="riskGrade">
              <el-input v-model="riskFormParams.riskGrade" disabled placeholder="关联风险点后显示"></el-input>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="12">
          <el-col :span="12">
            <el-form-item label="采集人" prop="createName">
              <el-input v-model="riskFormParams.createName" disabled placeholder="关联风险点后显示"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="采集时间" prop="createTime">
              <el-input v-model="riskFormParams.createTime" disabled placeholder="关联风险点后显示"></el-input>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="12">
          <el-col :span="24">
            <el-form-item label="">
              <el-button type="primary" @click="queryRiskDetail">查看关联风险点完整信息</el-button>
            </el-form-item>
          </el-col>
        </el-row>

      </div>

      <div class="infoBox" style="margin-bottom: 20px;" v-loading="formLoading">
        <div class="infoTitle">
          灾毁基础信息
        </div>

        <el-row :gutter="12">
          <el-col :span="12">
            <el-form-item prop="disasterSectionId">
                <span slot="label">
                  <el-tooltip :content="`灾毁段编号，按XX自动生成`" placement="top">
                    <i class="el-icon-question"></i>
                  </el-tooltip>
                  灾毁段编号
                </span>
              <el-input v-model="formParams.disasterSectionId" placeholder="保存后自动生成"/>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="12">
          <el-col :span="12">
            <el-form-item label="起点经纬度" prop="startLatitudeAndLongitude">
              <el-input v-model="formParams.startLatitudeAndLongitude" disabled placeholder="关联风险点后自动生成"/>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="止点经纬度" prop="endLatitudeAndLongitude">
              <el-input v-model="formParams.endLatitudeAndLongitude" disabled placeholder="关联风险点后自动生成"/>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="12">
          <el-col :span="12">
            <el-form-item label="起点桩号" prop="pileStartNum">
              <el-input v-model="formParams.pileStartNum" disabled placeholder="关联风险点后自动生成"/>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="止点桩号" prop="pileEndNum">
              <el-input v-model="formParams.pileEndNum" disabled placeholder="关联风险点后自动生成"/>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="12">
          <el-col :span="12">
            <el-form-item label="路面宽度" prop="roadbedWidth">
              <div style="width: 100%; display: flex; align-items: center; flex-direction: row; flex-wrap: nowrap;">
                <el-input-number v-model="formParams.roadbedWidth" controls-position="right" :min="0" :precision="2"
                                 style="width: 100%;" placeholder="请输入路面宽度"/>
                <el-tag size="small" type="info" effect="light">（米）</el-tag>
              </div>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="路面类型" prop="roadbedType">
              <el-select v-model="formParams.roadbedType" placeholder="请选择路面类型" style="width: 100%;">
                <el-option v-for="dict in dict.type.damage_surface_type" :label="dict.label" :value="dict.value"
                           :key="dict.value"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

      </div>

      <div class="infoBox" style="margin-bottom: 20px;" v-loading="formLoading">
        <div class="infoTitle">
          灾毁损失情况
        </div>

        <el-row :gutter="12">
          <el-col :span="12">
            <el-form-item label="是否中断" prop="interrupt">
              <el-radio-group v-model="formParams.interrupt" ref="subFormRadio1">
                <el-radio :label="'0'">是</el-radio>
                <el-radio :label="'1'">否</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="灾毁发生时间" prop="disasterStartTime">
              <el-date-picker v-model="formParams.disasterStartTime" style="width:100%" type="datetime"
                              placeholder="灾毁发生时间" clearable value-format="yyyy-MM-dd HH:mm"
                              format="yyyy-MM-dd HH:mm"/>
            </el-form-item>
          </el-col>
        </el-row>

        <div class="infoBox" style="margin-bottom: 20px;" v-loading="formLoading">
          <div class="infoTitle">
            灾毁设施类型
          </div>

          <el-row :gutter="12">
            <el-col :span="24">
              <el-form-item label="灾毁设施类型" prop="disasterFacilitiesType">
                <el-checkbox-group v-model="formParams.disasterFacilitiesType" @change="formFacilitiesChange">
                  <el-checkbox v-for="dict in dict.type.damage_facility_type" :label="dict.value" :key="dict.value">
                    {{ dict.label }}
                  </el-checkbox>
                </el-checkbox-group>
              </el-form-item>
            </el-col>
          </el-row>

          <div class="infoBox" style="margin-bottom: 20px;" v-loading="formLoading"
               v-show="formParams.disasterFacilitiesType.includes('1')">
            <div class="infoTitle">
              路基
            </div>

            <el-row :gutter="12">
              <el-col :span="12">
                <el-form-item label="损失路基方量" prop="damagedRoadbedVolume">
                  <div style="width: 100%; display: flex; align-items: center; flex-direction: row; flex-wrap: nowrap;">
                    <el-input-number v-model="formParams.damagedRoadbedVolume" controls-position="right" :min="0"
                                     :precision="0" style="width: 100%;" placeholder="请输入损失路基方量"/>
                    <el-tag size="small" type="info" effect="light">（立方米）</el-tag>
                  </div>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="损毁路基长度" prop="damagedRoadbedLength">
                  <div style="width: 100%; display: flex; align-items: center; flex-direction: row; flex-wrap: nowrap;">
                    <el-input-number v-model="formParams.damagedRoadbedLength" controls-position="right" :min="0"
                                     :precision="0" style="width: 100%;" placeholder="请输入损毁路基长度"/>
                    <el-tag size="small" type="info" effect="light">（米）</el-tag>
                  </div>
                </el-form-item>
              </el-col>
            </el-row>

            <el-row :gutter="12">
              <el-col :span="12">
                <el-form-item label="损失全额" prop="damagedFullAmount">
                  <div style="width: 100%; display: flex; align-items: center; flex-direction: row; flex-wrap: nowrap;">
                    <el-input-number v-model="formParams.damagedFullAmount" controls-position="right"
                                     @change="formtotalCompute" :min="0" :precision="2" style="width: 100%;"
                                     placeholder="请输入损失路基方量"/>
                    <el-tag size="small" type="info" effect="light">（万元）</el-tag>
                  </div>
                </el-form-item>
              </el-col>
            </el-row>

          </div>

          <div class="infoBox" style="margin-bottom: 20px;" v-loading="formLoading"
               v-show="formParams.disasterFacilitiesType.includes('2')">
            <div class="infoTitle">
              路面
            </div>

            <el-row :gutter="12">
              <el-col :span="24">
                <el-form-item label-width="0">
                  <el-checkbox v-model="asphaltSelect" style="pointer-events: none;">沥青路面</el-checkbox>
                  <el-checkbox v-model="cementSelect" style="pointer-events: none;">水泥路面</el-checkbox>
                  <el-checkbox v-model="dinasSelect" style="pointer-events: none;">砂石路面</el-checkbox>
                </el-form-item>
              </el-col>
            </el-row>

            <el-divider></el-divider>

            <el-row :gutter="12">
              <el-col :span="2">
                <el-form-item label-width="0">
                  <p style="font-weight: bold; margin: 0;">沥青路面 ></p>
                </el-form-item>
              </el-col>
              <el-col :span="7">
                <el-form-item label="损毁路面面积" prop="asphaltDamagedRoadbedArea">
                  <div style="width: 100%; display: flex; align-items: center; flex-direction: row; flex-wrap: nowrap;">
                    <el-input-number v-model="formParams.asphaltDamagedRoadbedArea" controls-position="right" :min="0"
                                     :precision="0" style="width: 100%;" placeholder="请输入损毁路面面积"/>
                    <el-tag size="small" type="info" effect="light">（平方米）</el-tag>
                  </div>
                </el-form-item>
              </el-col>
              <el-col :span="7">
                <el-form-item label="损毁路面长度" prop="asphaltDamagedRoadbedLength">
                  <div style="width: 100%; display: flex; align-items: center; flex-direction: row; flex-wrap: nowrap;">
                    <el-input-number v-model="formParams.asphaltDamagedRoadbedLength" controls-position="right" :min="0"
                                     :precision="0" style="width: 100%;" placeholder="请输入损毁路面长度"/>
                    <el-tag size="small" type="info" effect="light">（米）</el-tag>
                  </div>
                </el-form-item>
              </el-col>
              <el-col :span="7">
                <el-form-item label="损失全额" prop="asphaltDamagedFullAmount">
                  <div style="width: 100%; display: flex; align-items: center; flex-direction: row; flex-wrap: nowrap;">
                    <el-input-number v-model="formParams.asphaltDamagedFullAmount" controls-position="right"
                                     @change="formtotalCompute" :min="0" :precision="2" style="width: 100%;"
                                     placeholder="请输入损失全额"/>
                    <el-tag size="small" type="info" effect="light">（万元）</el-tag>
                  </div>
                </el-form-item>
              </el-col>
            </el-row>

            <el-divider></el-divider>

            <el-row :gutter="12">
              <el-col :span="2">
                <el-form-item label-width="0">
                  <p style="font-weight: bold; margin: 0;">水泥路面 ></p>
                </el-form-item>
              </el-col>
              <el-col :span="7">
                <el-form-item label="损毁路面面积" prop="cementDamagedRoadbedArea">
                  <div style="width: 100%; display: flex; align-items: center; flex-direction: row; flex-wrap: nowrap;">
                    <el-input-number v-model="formParams.cementDamagedRoadbedArea" controls-position="right" :min="0"
                                     :precision="0" style="width: 100%;" placeholder="请输入损毁路面面积"/>
                    <el-tag size="small" type="info" effect="light">（平方米）</el-tag>
                  </div>
                </el-form-item>
              </el-col>
              <el-col :span="7">
                <el-form-item label="损毁路面长度" prop="cementDamagedRoadbedLength">
                  <div style="width: 100%; display: flex; align-items: center; flex-direction: row; flex-wrap: nowrap;">
                    <el-input-number v-model="formParams.cementDamagedRoadbedLength" controls-position="right" :min="0"
                                     :precision="0" style="width: 100%;" placeholder="请输入损毁路面长度"/>
                    <el-tag size="small" type="info" effect="light">（米）</el-tag>
                  </div>
                </el-form-item>
              </el-col>
              <el-col :span="7">
                <el-form-item label="损失全额" prop="cementDamagedFullAmount">
                  <div style="width: 100%; display: flex; align-items: center; flex-direction: row; flex-wrap: nowrap;">
                    <el-input-number v-model="formParams.cementDamagedFullAmount" controls-position="right"
                                     @change="formtotalCompute" :min="0" :precision="2" style="width: 100%;"
                                     placeholder="请输入损失全额"/>
                    <el-tag size="small" type="info" effect="light">（万元）</el-tag>
                  </div>
                </el-form-item>
              </el-col>
            </el-row>

            <el-divider></el-divider>

            <el-row :gutter="12">
              <el-col :span="2">
                <el-form-item label-width="0">
                  <p style="font-weight: bold; margin: 0;">砂石路面 ></p>
                </el-form-item>
              </el-col>
              <el-col :span="7">
                <el-form-item label="损毁路面面积" prop="dinasDamagedRoadbedArea">
                  <div style="width: 100%; display: flex; align-items: center; flex-direction: row; flex-wrap: nowrap;">
                    <el-input-number v-model="formParams.dinasDamagedRoadbedArea" controls-position="right" :min="0"
                                     :precision="0" style="width: 100%;" placeholder="请输入损毁路面面积"/>
                    <el-tag size="small" type="info" effect="light">（平方米）</el-tag>
                  </div>
                </el-form-item>
              </el-col>
              <el-col :span="7">
                <el-form-item label="损毁路面长度" prop="dinasDamagedRoadbedLength">
                  <div style="width: 100%; display: flex; align-items: center; flex-direction: row; flex-wrap: nowrap;">
                    <el-input-number v-model="formParams.dinasDamagedRoadbedLength" controls-position="right" :min="0"
                                     :precision="0" style="width: 100%;" placeholder="请输入损毁路面长度"/>
                    <el-tag size="small" type="info" effect="light">（米）</el-tag>
                  </div>
                </el-form-item>
              </el-col>
              <el-col :span="7">
                <el-form-item label="损失全额" prop="dinasDamagedFullAmount">
                  <div style="width: 100%; display: flex; align-items: center; flex-direction: row; flex-wrap: nowrap;">
                    <el-input-number v-model="formParams.dinasDamagedFullAmount" controls-position="right"
                                     @change="formtotalCompute" :min="0" :precision="2" style="width: 100%;"
                                     placeholder="请输入损失全额"/>
                    <el-tag size="small" type="info" effect="light">（万元）</el-tag>
                  </div>
                </el-form-item>
              </el-col>
            </el-row>

          </div>

          <div class="infoBox" style="margin-bottom: 20px;" v-loading="formLoading"
               v-show="formParams.disasterFacilitiesType.includes('3')">
            <div class="infoTitle">
              桥梁
            </div>

            <el-row :gutter="12">
              <el-col :span="24">
                <el-form-item label="毁坏类型" prop="bridgeDamagedType">
                  <el-radio-group v-model="formParams.bridgeDamagedType" ref="subFormRadio2">
                    <el-radio v-for="dict in dict.type.bridge_damage_type" :label="dict.value" :key="dict.value">
                      {{ dict.label }}
                    </el-radio>
                  </el-radio-group>
                </el-form-item>
              </el-col>
            </el-row>

            <el-divider v-if="formParams.bridgeDamagedType"></el-divider>

            <el-row :gutter="12" v-show="formParams.bridgeDamagedType">
              <el-col :span="8">
                <el-form-item label="损毁长度" prop="bridgeDamagedLength">
                  <div style="width: 100%; display: flex; align-items: center; flex-direction: row; flex-wrap: nowrap;">
                    <el-input-number v-model="formParams.bridgeDamagedLength" controls-position="right" :min="0"
                                     :precision="2" style="width: 100%;" placeholder="请输入损毁长度"/>
                    <el-tag size="small" type="info" effect="light">（延米）</el-tag>
                  </div>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="损毁数量" prop="bridgeDamagedNumber">
                  <div style="width: 100%; display: flex; align-items: center; flex-direction: row; flex-wrap: nowrap;">
                    <el-input-number v-model="formParams.bridgeDamagedNumber" controls-position="right" :min="0"
                                     :precision="0" style="width: 100%;" placeholder="请输入损毁数量"/>
                    <el-tag size="small" type="info" effect="light">（座）</el-tag>
                  </div>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="损毁全额" prop="bridgeDamagedFullAmount">
                  <div style="width: 100%; display: flex; align-items: center; flex-direction: row; flex-wrap: nowrap;">
                    <el-input-number v-model="formParams.bridgeDamagedFullAmount" controls-position="right"
                                     @change="formtotalCompute" :min="0" :precision="2" style="width: 100%;"
                                     placeholder="请输入损毁全额"/>
                    <el-tag size="small" type="info" effect="light">（万元）</el-tag>
                  </div>
                </el-form-item>
              </el-col>
            </el-row>

          </div>

          <div class="infoBox" style="margin-bottom: 20px;" v-loading="formLoading"
               v-show="formParams.disasterFacilitiesType.includes('4')">
            <div class="infoTitle">
              隧道
            </div>

            <el-row :gutter="12">
              <el-col :span="8">
                <el-form-item label="损毁长度" prop="tunnelDamagedLength">
                  <div style="width: 100%; display: flex; align-items: center; flex-direction: row; flex-wrap: nowrap;">
                    <el-input-number v-model="formParams.tunnelDamagedLength" controls-position="right" :min="0"
                                     :precision="0" style="width: 100%;" placeholder="请输入损毁长度"/>
                    <el-tag size="small" type="info" effect="light">（延米）</el-tag>
                  </div>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="损毁数量" prop="tunnelDamagedNumber">
                  <div style="width: 100%; display: flex; align-items: center; flex-direction: row; flex-wrap: nowrap;">
                    <el-input-number v-model="formParams.tunnelDamagedNumber" controls-position="right" :min="0"
                                     :precision="0" style="width: 100%;" placeholder="请输入损毁数量"/>
                    <el-tag size="small" type="info" effect="light">（道）</el-tag>
                  </div>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="损毁全额" prop="tunnelDamagedFullAmount">
                  <div style="width: 100%; display: flex; align-items: center; flex-direction: row; flex-wrap: nowrap;">
                    <el-input-number v-model="formParams.tunnelDamagedFullAmount" controls-position="right" :min="0"
                                     @change="formtotalCompute" :precision="2" style="width: 100%;"
                                     placeholder="请输入损毁全额"/>
                    <el-tag size="small" type="info" effect="light">（万元）</el-tag>
                  </div>
                </el-form-item>
              </el-col>
            </el-row>
          </div>

          <div class="infoBox" style="margin-bottom: 20px;" v-loading="formLoading"
               v-show="formParams.disasterFacilitiesType.includes('5')">
            <div class="infoTitle">
              涵洞
            </div>

            <el-row :gutter="12">
              <el-col :span="12">
                <el-form-item label="损毁数量" prop="culvertDamagedNumber">
                  <div style="width: 100%; display: flex; align-items: center; flex-direction: row; flex-wrap: nowrap;">
                    <el-input-number v-model="formParams.culvertDamagedNumber" controls-position="right" :min="0"
                                     :precision="0" style="width: 100%;" placeholder="请输入损毁数量"/>
                    <el-tag size="small" type="info" effect="light">（道）</el-tag>
                  </div>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="损毁全额" prop="culvertDamagedFullAmount">
                  <div style="width: 100%; display: flex; align-items: center; flex-direction: row; flex-wrap: nowrap;">
                    <el-input-number v-model="formParams.culvertDamagedFullAmount" controls-position="right"
                                     @change="formtotalCompute" :min="0" :precision="2" style="width: 100%;"
                                     placeholder="请输入损毁全额"/>
                    <el-tag size="small" type="info" effect="light">（万元）</el-tag>
                  </div>
                </el-form-item>
              </el-col>
            </el-row>
          </div>

          <div class="infoBox" style="margin-bottom: 20px;" v-loading="formLoading"
               v-show="formParams.disasterFacilitiesType.includes('6')">
            <div class="infoTitle">
              防护工程
            </div>

            <el-row :gutter="12">
              <el-col :span="24">
                <el-form-item label-width="0">
                  <el-checkbox v-model="revetmentSelect" style="pointer-events: none;">护坡</el-checkbox>
                  <el-checkbox v-model="barricadeSelect" style="pointer-events: none;">驳岸、挡墙</el-checkbox>
                </el-form-item>
              </el-col>
            </el-row>

            <el-divider></el-divider>

            <el-row :gutter="12">
              <el-col :span="2">
                <el-form-item label-width="0">
                  <p style="font-weight: bold; margin: 0;">护坡 ></p>
                </el-form-item>
              </el-col>
              <el-col :span="7">
                <el-form-item label="损毁方量" prop="revetmentDamagedVolume">
                  <div style="width: 100%; display: flex; align-items: center; flex-direction: row; flex-wrap: nowrap;">
                    <el-input-number v-model="formParams.revetmentDamagedVolume" controls-position="right" :min="0"
                                     :precision="0" style="width: 100%;" placeholder="请输入损毁方量"/>
                    <el-tag size="small" type="info" effect="light">（立方米）</el-tag>
                  </div>
                </el-form-item>
              </el-col>
              <el-col :span="7">
                <el-form-item label="损毁数量" prop="revetmentDamagedNumber">
                  <div style="width: 100%; display: flex; align-items: center; flex-direction: row; flex-wrap: nowrap;">
                    <el-input-number v-model="formParams.revetmentDamagedNumber" controls-position="right" :min="0"
                                     :precision="0" style="width: 100%;" placeholder="请输入损毁数量"/>
                    <el-tag size="small" type="info" effect="light">（处）</el-tag>
                  </div>
                </el-form-item>
              </el-col>
              <el-col :span="7">
                <el-form-item label="损毁全额" prop="revetmentDamagedFullAmount">
                  <div style="width: 100%; display: flex; align-items: center; flex-direction: row; flex-wrap: nowrap;">
                    <el-input-number v-model="formParams.revetmentDamagedFullAmount" controls-position="right"
                                     @change="formtotalCompute" :min="0" :precision="2" style="width: 100%;"
                                     placeholder="请输入损毁全额"/>
                    <el-tag size="small" type="info" effect="light">（万元）</el-tag>
                  </div>
                </el-form-item>
              </el-col>
            </el-row>

            <el-divider></el-divider>

            <el-row :gutter="12">
              <el-col :span="2">
                <el-form-item label-width="0">
                  <p style="font-weight: bold; margin: 0;">驳岸、挡墙 ></p>
                </el-form-item>
              </el-col>
              <el-col :span="7">
                <el-form-item label="损毁方量" prop="barricadeDamagedVolume">
                  <div style="width: 100%; display: flex; align-items: center; flex-direction: row; flex-wrap: nowrap;">
                    <el-input-number v-model="formParams.barricadeDamagedVolume" controls-position="right" :min="0"
                                     :precision="0" style="width: 100%;" placeholder="请输入损毁方量"/>
                    <el-tag size="small" type="info" effect="light">（立方米）</el-tag>
                  </div>
                </el-form-item>
              </el-col>
              <el-col :span="7">
                <el-form-item label="损毁数量" prop="barricadeDamagedNumber">
                  <div style="width: 100%; display: flex; align-items: center; flex-direction: row; flex-wrap: nowrap;">
                    <el-input-number v-model="formParams.barricadeDamagedNumber" controls-position="right" :min="0"
                                     :precision="0" style="width: 100%;" placeholder="请输入损毁数量"/>
                    <el-tag size="small" type="info" effect="light">（处）</el-tag>
                  </div>
                </el-form-item>
              </el-col>
              <el-col :span="7">
                <el-form-item label="损毁全额" prop="barricadeDamagedFullAmount">
                  <div style="width: 100%; display: flex; align-items: center; flex-direction: row; flex-wrap: nowrap;">
                    <el-input-number v-model="formParams.barricadeDamagedFullAmount" controls-position="right"
                                     @change="formtotalCompute" :min="0" :precision="2" style="width: 100%;"
                                     placeholder="请输入损毁全额"/>
                    <el-tag size="small" type="info" effect="light">（万元）</el-tag>
                  </div>
                </el-form-item>
              </el-col>
            </el-row>

          </div>

          <div class="infoBox" style="margin-bottom: 20px;" v-loading="formLoading"
               v-show="formParams.disasterFacilitiesType.includes('7')">
            <div class="infoTitle">
              坍塌方
            </div>

            <el-row :gutter="12">
              <el-col :span="8">
                <el-form-item label="损毁方量" prop="collapseDamagedVolume">
                  <div style="width: 100%; display: flex; align-items: center; flex-direction: row; flex-wrap: nowrap;">
                    <el-input-number v-model="formParams.collapseDamagedVolume" controls-position="right" :min="0"
                                     :precision="0" style="width: 100%;" placeholder="请输入损毁方量"/>
                    <el-tag size="small" type="info" effect="light">（立方米）</el-tag>
                  </div>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="损毁数量" prop="collapseDamagedNumber">
                  <div style="width: 100%; display: flex; align-items: center; flex-direction: row; flex-wrap: nowrap;">
                    <el-input-number v-model="formParams.collapseDamagedNumber" controls-position="right" :min="0"
                                     :precision="0" style="width: 100%;" placeholder="请输入损毁数量"/>
                    <el-tag size="small" type="info" effect="light">（处）</el-tag>
                  </div>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="损毁全额" prop="collapseDamagedFullAmount">
                  <div style="width: 100%; display: flex; align-items: center; flex-direction: row; flex-wrap: nowrap;">
                    <el-input-number v-model="formParams.collapseDamagedFullAmount" controls-position="right"
                                     @change="formtotalCompute" :min="0" :precision="2" style="width: 100%;"
                                     placeholder="请输入损毁全额"/>
                    <el-tag size="small" type="info" effect="light">（万元）</el-tag>
                  </div>
                </el-form-item>
              </el-col>
            </el-row>
          </div>

          <div class="infoBox" style="margin-bottom: 20px;" v-loading="formLoading"
               v-show="formParams.disasterFacilitiesType.includes('99')">
            <div class="infoTitle">
              其它
            </div>

            <el-row :gutter="12">
              <el-col :span="24">
                <el-form-item label="灾毁设施名称" prop="otherRemarks">
                  <el-input v-model="formParams.otherRemarks" clearable placeholder="请输入灾毁设施名称"/>
                </el-form-item>
              </el-col>
            </el-row>

            <el-row :gutter="12">
              <el-col :span="12">
                <el-form-item label="损失全额" prop="otherDamagedAmount">
                  <div style="width: 100%; display: flex; align-items: center; flex-direction: row; flex-wrap: nowrap;">
                    <el-input-number v-model="formParams.otherDamagedAmount" controls-position="right"
                                     @change="formtotalCompute" :min="0" :precision="2" style="width: 100%;"
                                     placeholder="请输入损失全额"/>
                    <el-tag size="small" type="info" effect="light">（万元）</el-tag>
                  </div>
                </el-form-item>
              </el-col>
            </el-row>
          </div>

        </div>

        <div class="infoBox" style="margin-bottom: 20px;" v-loading="formLoading">
          <div class="infoTitle">
            损失合计
          </div>

          <el-row :gutter="12">
            <el-col :span="12">
              <el-form-item label-width="0" prop="totalLoss">
                <div style="width: 100%; display: flex; align-items: center; flex-direction: row; flex-wrap: nowrap;">
                  <el-input-number v-model="formParams.totalLoss" controls-position="right" disabled :min="0"
                                   :precision="2" style="width: 100%;" placeholder="自动计算损失合计"/>
                  <el-tag size="small" type="info" effect="light">（万元）</el-tag>
                </div>
              </el-form-item>
            </el-col>
          </el-row>
        </div>

      </div>

      <div class="infoBox" style="margin-bottom: 20px;" v-loading="formLoading">
        <div class="infoTitle">
          灾毁照片
        </div>
        <el-row :gutter="12">
          <el-col :span="24">
            <el-form-item label-width="0" prop="riskDescription">
              <div class="imgBox" v-if="formImgSrcList1.length > 0">
                <el-timeline>
                  <el-timeline-item v-for="item in formImgDateLine1" :key="item.time" :timestamp="item.time"
                                    placement="top" color="#5cbb7a">
                    <el-card class="imgBoxCard">
                      <div class="cardMain" v-for="itemC in item.data">
                        <el-button class="imgDeleteBtn" type="danger" icon="el-icon-delete" circle
                                   @click="formDeleteImg(itemC.id, 1)"></el-button>
                        <div class="imgTitle">
                          <el-tooltip class="item" effect="dark" :content="itemC.name" placement="top">
                            <i class="el-icon-info"></i>
                          </el-tooltip>
                          {{ itemC.name }}
                        </div>
                        <el-image
                          fit="cover"
                          class="img"
                          @click="formImgPreview(itemC.imgUrl, 1)"
                          :src="itemC.url"
                          :preview-src-list="formImgUrlList1"
                        ></el-image>
                        <div class="footer">
                          {{`由 ${itemC.createBy} 上传于 ${itemC.createTime}` }}
                        </div>
                      </div>
                    </el-card>
                  </el-timeline-item>
                </el-timeline>
              </div>
              <div class="noneBox" v-else>
                暂无内容
              </div>
            </el-form-item>
          </el-col>
        </el-row>
      </div>

      <div class="infoBox" v-loading="formLoading" style="padding: 15px; margin-bottom: 20px;">
        <div class="infoTitle">
          <el-tooltip content="上传提示" placement="top">
            <i class="el-icon-question"></i>
          </el-tooltip>
          上传灾毁照片
        </div>
        <ImageUpload
          v-model="formUploadList1"
          :ownerId="formImgOwnerId1"
          storage-path="/disaster/risk/damage/"
          platform="fykj"
        />
      </div>

      <div class="infoBox" style="margin-bottom: 20px;" v-loading="formLoading">
        <div class="infoTitle">
          应急抢通信息
        </div>

        <el-row :gutter="12">
          <el-col :span="12">
            <el-form-item label="是否已抢通" prop="alreadySnatchedThrough">
              <el-radio-group v-model="formParams.alreadySnatchedThrough" ref="subFormRadio3">
                <el-radio :label="'0'">是</el-radio>
                <el-radio :label="'1'">否</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="预计抢通时间" prop="actualRushTimeExpected">
              <el-date-picker v-model="formParams.actualRushTimeExpected" style="width:100%" type="datetime"
                              placeholder="请选择预计抢通时间" clearable value-format="yyyy-MM-dd HH:mm"
                              format="yyyy-MM-dd HH:mm"/>
            </el-form-item>
          </el-col>
        </el-row>

        <el-divider></el-divider>

        <el-row :gutter="12">
          <el-col :span="24">
            <el-form-item label="灾毁等级" prop="disasterLevel">
              <el-radio-group v-model="formParams.disasterLevel" ref="subFormRadio4">
                <el-radio v-for="dict in dict.type.hazard_degree" :label="dict.value" :key="dict.value">{{
                    dict.label
                  }}
                </el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>

        <el-divider></el-divider>

        <el-row :gutter="12">
          <el-col :span="24">
            <el-form-item label="备注" prop="remarks">
              <el-input v-model="formParams.remarks" clearable
                        placeholder="例：造成上行行车道2和行车道3断交，现场行车道1通行(仅一个车道通行)。"/>
            </el-form-item>
          </el-col>
        </el-row>

      </div>

      <div class="infoBox" style="margin-bottom: 20px;" v-loading="formLoading">
        <div class="infoTitle">
          应急抢通情况
        </div>

        <el-row :gutter="12">
          <el-col :span="12">
            <el-form-item label="实际抢通时间" prop="actualRushTime">
              <el-date-picker v-model="formParams.actualRushTime" style="width:100%" type="datetime"
                              placeholder="请选择实际抢通时间" clearable value-format="yyyy-MM-dd HH:mm"
                              format="yyyy-MM-dd HH:mm"/>
            </el-form-item>
          </el-col>
        </el-row>

        <el-divider></el-divider>

        <el-row :gutter="12">
          <el-col :span="12">
            <el-form-item label="是否已处置" prop="handled">
              <el-radio-group v-model="formParams.handled" ref="subFormRadio5">
                <el-radio :label="'0'">是</el-radio>
                <el-radio :label="'1'">否</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="（预计）处置时间" prop="disposalTimeExpected">
              <el-date-picker v-model="formParams.disposalTimeExpected" style="width:100%" type="datetime"
                              placeholder="请选择（预计）处置时间" clearable value-format="yyyy-MM-dd HH:mm"
                              format="yyyy-MM-dd HH:mm"/>
            </el-form-item>
          </el-col>
        </el-row>

        <el-divider></el-divider>

        <el-row :gutter="12">
          <el-col :span="8">
            <el-form-item label="（预计）投入人工" prop="inputLaborExpected">
              <div style="width: 100%; display: flex; align-items: center; flex-direction: row; flex-wrap: nowrap;">
                <el-input-number v-model="formParams.inputLaborExpected" controls-position="right" :min="0"
                                 :precision="2" style="width: 100%;" placeholder="请输入（预计）投入人工"/>
                <el-tag size="small" type="info" effect="light">（万元）</el-tag>
              </div>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="（预计）投入机械" prop="inputMachineryExpected">
              <div style="width: 100%; display: flex; align-items: center; flex-direction: row; flex-wrap: nowrap;">
                <el-input-number v-model="formParams.inputMachineryExpected" controls-position="right" :min="0"
                                 :precision="2" style="width: 100%;" placeholder="请输入（预计）投入机械"/>
                <el-tag size="small" type="info" effect="light">（万元）</el-tag>
              </div>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="（预计）投入资金" prop="inputFundExpected">
              <div style="width: 100%; display: flex; align-items: center; flex-direction: row; flex-wrap: nowrap;">
                <el-input-number v-model="formParams.inputFundExpected" controls-position="right" :min="0"
                                 :precision="2" style="width: 100%;" placeholder="请输入（预计）投入资金"/>
                <el-tag size="small" type="info" effect="light">（万元）</el-tag>
              </div>
            </el-form-item>
          </el-col>
        </el-row>

        <el-divider></el-divider>

        <div class="infoBox" style="margin-bottom: 20px;" v-loading="formLoading">
          <div class="infoTitle">
            抢通后照片
          </div>
          <el-row :gutter="12">
            <el-col :span="24">
              <el-form-item label-width="0" prop="riskDescription">
                <div class="imgBox" v-if="formImgSrcList2.length > 0">
                  <el-timeline>
                    <el-timeline-item v-for="item in formImgDateLine2" :key="item.time" :timestamp="item.time"
                                      placement="top" color="#5cbb7a">
                      <el-card class="imgBoxCard">
                        <div class="cardMain" v-for="itemC in item.data">
                          <el-button class="imgDeleteBtn" type="danger" icon="el-icon-delete" circle
                                     @click="formDeleteImg(itemC.id, 2)"></el-button>
                          <div class="imgTitle">
                            <el-tooltip class="item" effect="dark" :content="itemC.name" placement="top">
                              <i class="el-icon-info"></i>
                            </el-tooltip>
                            {{ itemC.name }}
                          </div>
                          <el-image
                            fit="cover"
                            class="img"
                            @click="formImgPreview(itemC.imgUrl, 2)"
                            :src="itemC.url"
                            :preview-src-list="formImgUrlList2"
                          ></el-image>
                          <div class="footer">
                            {{`由 ${itemC.createBy} 上传于 ${itemC.createTime}` }}
                          </div>
                        </div>
                      </el-card>
                    </el-timeline-item>
                  </el-timeline>
                </div>
                <div class="noneBox" v-else>
                  暂无内容
                </div>
              </el-form-item>
            </el-col>
          </el-row>
        </div>

        <div class="infoBox" v-loading="formLoading" style="padding: 15px; margin-bottom: 20px;">
          <div class="infoTitle">
            <el-tooltip content="请补充上传灾毁抢通后现场照片" placement="top">
              <i class="el-icon-question"></i>
            </el-tooltip>
            上传抢通后照片
          </div>
          <ImageUpload
            v-model="formUploadList2"
            :ownerId="formImgOwnerId2"
            storage-path="/disaster/risk/damageScoopOut/"
            platform="fykj"
          />
        </div>
      </div>

      <div class="infoBox" style="margin-bottom: 20px;" v-loading="formLoading">
        <div class="infoTitle">
          公函
        </div>
        <el-row :gutter="12">
          <el-col :span="24">
            <el-form-item label-width="0" prop="riskDescription">
              <div class="fileBox" v-if="formFileSrcList.length > 0" v-for="item in formFileSrcList">
                <div class="fileItemBox">
                  <div class="fileItem">
                    <div class="boxLeft">
                      <i class="el-icon-document"></i>
                    </div>
                    <div class="boxmiddle">
                      {{ item.name }}
                    </div>
                    <div class="boxRight">
                      <el-button type="primary" icon="el-icon-download" @click="formDownloadFile(item.url, item.name)">
                        下载
                      </el-button>
                      <el-button type="danger" icon="el-icon-delete" @click="formDeleteFile(item.id)">删除</el-button>
                    </div>
                  </div>
                </div>
              </div>
              <div class="noneBox" v-if="formFileSrcList.length === 0">暂无内容</div>
            </el-form-item>
          </el-col>
        </el-row>
      </div>

      <div class="infoBox" v-loading="formLoading" style="padding: 15px; margin-bottom: 20px;">
        <div class="infoTitle">
          <el-tooltip content="灾毁损失全额超过1000万元，需要上传公函" placement="top">
            <i class="el-icon-question"></i>
          </el-tooltip>
          上传公函
        </div>
        <FileUpload
          v-model="formUploadList3"
          :ownerId="formFileOwnerId"
          storage-path="/disaster/risk/officialLetter/"
        />
      </div>

    </el-form>

    <!-- 风险点详情dialog -->
    <el-dialog :visible.sync="riskFormDialog" width="70%" max-height="50%" :close-on-press-escape="false"
               :close-on-click-modal="false" append-to-body class="formDialog">
      <template slot="title">
        <div class="titleBox">
          <div class="title">风险点详情表单</div>
          <div class="subTitle" v-if="riskFormParams.id">ID：{{ riskFormParams.id }}</div>
          <div class="riskLevel" v-if="!riskFormParams.disasterType.includes('其它')">
            <p class="title">风险等级：</p>
            <p class="main" v-if="riskFormParams.riskGrade" :style="{ 'background':
              riskFormParams.riskGrade.includes('一') ? 'rgb(254, 0, 0)' :
              riskFormParams.riskGrade.includes('二') ? 'rgb(255, 192, 1)' :
              riskFormParams.riskGrade.includes('三') ? 'rgb(255, 255, 3)' : 'rgb(1, 176, 241)' }"
            >{{ riskFormParams.riskGrade }}</p>
            <p class="main" v-else style="background: rgb(1, 176, 241)">
              暂无
            </p>
            <el-tag
              type="info"
              effect="plain"
              style="margin-left: 5px;"
            >{{ `${riskFormParams.riskScore ? riskFormParams.riskScore : '0'}分` }}
            </el-tag>
          </div>
        </div>
      </template>
      <el-form ref="formRef" :model="riskFormParams" label-width="125px" label-position="right">
        <div class="infoBox" style="margin-bottom: 20px;" v-loading="riskFormDetailLoading">
          <div class="infoTitle">
            基础信息
          </div>
          <el-row :gutter="12">
            <el-col :span="12">
              <el-form-item label="管养单位名称">
                <el-input v-model="riskFormParams.maintenanceUnitName" disabled placeholder="请选择管养单位"
                          style="width: 100%;"/>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="路段名称">
                <el-input v-model="riskFormParams.roadSectionName" disabled placeholder="请选择路段"
                          style="width: 100%;"/>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="12">
            <el-col :span="12">
              <el-form-item label="路线名称">
                <el-input v-model="riskFormParams.routerName" disabled placeholder="请选择路线" style="width: 100%;"/>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="技术等级" prop="technicalGrade">
                <el-select v-model="riskFormParams.technicalGrade" disabled placeholder="请选择技术等级"
                           style="width: 100%;">
                  <el-option v-for="dict in dict.type.sys_route_grade" :label="dict.label" :value="dict.value"
                             :key="dict.value"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="12">
            <el-col :span="12">
              <el-form-item label="灾害名称" prop="disasterName">
                <el-input v-model="riskFormParams.disasterName" disabled placeholder="请输入灾害名称"/>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="灾害编号" prop="disasterNum">
                <el-input v-model="riskFormParams.disasterNum" disabled placeholder="请输入灾害编号"/>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="12">
            <el-col :span="12">
              <el-form-item label="起点经纬度" prop="startLatitudeAndLongitude">
                <div style="width: 100%; display: flex; flex-direction: row; flex-wrap: nowrap;">
                  <el-input v-model="riskFormParams.startLatitudeAndLongitude" disabled
                            placeholder="请输入起点经纬度，以英文逗号分隔"></el-input>
                  <el-button slot="append" icon="el-icon-location" @click="openCoordinateDialog('start')">查看坐标
                  </el-button>
                </div>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="止点经纬度" prop="endLatitudeAndLongitude">
                <div style="width: 100%; display: flex; flex-direction: row; flex-wrap: nowrap;">
                  <el-input v-model="riskFormParams.endLatitudeAndLongitude" disabled
                            placeholder="请输入止点经纬度，以英文逗号分隔"></el-input>
                  <el-button icon="el-icon-location" @click="openCoordinateDialog('end')">查看坐标</el-button>
                </div>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="12">
            <el-col :span="12">
              <el-form-item label="起点桩号" prop="pileStartNum">
                <el-input v-model="riskFormParams.pileStartNum" disabled placeholder="请输入起点桩号"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="终点桩号" prop="pileEndNum">
                <el-input v-model="riskFormParams.pileEndNum" disabled placeholder="请输入终点桩号"></el-input>
              </el-form-item>
            </el-col>

          </el-row>

        </div>

        <Crumble v-if="riskFormParams.disasterType === '崩塌'" ref="formSubRef"/>

        <LandSlide v-if="riskFormParams.disasterType === '滑坡'" ref="formSubRef"/>

        <DebrisFlow v-if="riskFormParams.disasterType === '泥石流'" ref="formSubRef"/>

        <SubSide v-if="riskFormParams.disasterType === '沉陷与塌陷'" ref="formSubRef"/>

        <WaterDestruction v-if="riskFormParams.disasterType === '水毁'" ref="formSubRef"/>

        <Other v-if="riskFormParams.disasterType.includes('其它')" ref="formSubRef"/>

      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-row class="footerTip" v-if="riskFormParams.createName">
          <el-col :span="12" style="display: flex; justify-content: flex-start;">
            采集人：{{ riskFormParams.createName }}
          </el-col>
          <el-col :span="12" style="display: flex; justify-content: flex-end;">
            采集时间：{{ riskFormParams.createTime }}
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24" style="display: flex; justify-content: center;">
            <!-- <el-button type="primary" @click="riskFormDialog = false" :loading="formBtnLoading">关 闭</el-button> -->
          </el-col>
        </el-row>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {submitTaskDamage} from "@/api/process/task/task";
import {queryDamageById, queryPageRisk, queryPageRiskById} from "@/api/disaster/damage/damage";
import {deptTreeSelect} from "@/api/tmpl";
import {getTreeByEntity} from "@/api/system/geography";
import CascadeSelection from "@/components/CascadeSelection";
import RangeInput from "@/views/baseData/components/rangeInput";
import ImageUpload from "@/views/disaster/ImageUpload";
import FileUpload from "@/views/disaster/FileUpload";
import Crumble from "@/views/disaster/damage/crumble";
import LandSlide from "@/views/disaster/damage/landslide";
import DebrisFlow from "@/views/disaster/damage/debrisFlow";
import SubSide from "@/views/disaster/damage/subside";
import WaterDestruction from "@/views/disaster/damage/waterDestruction";
import Other from "@/views/disaster/damage/other";
import {findFiles} from "@/api/file";
import {removeFile} from "@/api/system/fileUpload";

export default {
  name: "DisasterSubmittedForm",
  dicts: [
    'disaster_risk_type',  // 灾害类型
    'disaster_risk_other_type', // 灾害类型（其它）
    'sys_route_grade', // 技术等级
    'damage_surface_type', // 灾毁路面类型
    'damage_facility_type', // 灾毁设施类型
    'bridge_damage_type', // 桥梁损毁类型
    'hazard_degree', // 危害程度/灾毁等级
  ],
  components: {
    // PileInput,
    CascadeSelection,
    RangeInput,
    ImageUpload,
    FileUpload,
    Crumble,
    LandSlide,
    DebrisFlow,
    SubSide,
    WaterDestruction,
    Other,
  },
  data() {
    return {
      formParams: {
        id: '', // 主表ID
        disasterType: '', // 灾毁类型
        disasterRiskId: '', // 风险点ID
        disasterSectionId: '', // 灾毁段编号
        startLatitudeAndLongitude: '', // 经纬度（起）
        endLatitudeAndLongitude: '', // 经纬度（止）
        pileStartNum: '', // 桩号（起）
        pileEndNum: '', // 桩号（终）
        roadbedWidth: '', // 路面宽度
        roadbedType: '', // 路面类型
        disasterStartTime: '', // 灾毁发生时间
        interrupt: '', // 是否中断
        disasterFacilitiesType: [], // 灾毁设施类型
        // 路基
        damagedRoadbedVolume: 0, // 损毁路基方量
        damagedRoadbedLength: 0, // 损毁路基长度
        damagedFullAmount: 0, // 损失全额
        // 路面
        asphaltDamagedRoadbedArea: 0, // 损毁路面面积-沥青路面
        asphaltDamagedRoadbedLength: 0, // 损毁路面长度-沥青路面
        asphaltDamagedFullAmount: 0, // 损失全额-沥青路面

        cementDamagedRoadbedArea: 0, // 损毁路面面积-水泥路面
        cementDamagedRoadbedLength: 0, // 损毁路面长度-水泥路面
        cementDamagedFullAmount: 0, // 损失全额-水泥路面

        dinasDamagedRoadbedArea: 0, // 损毁路面面积-砂石路面
        dinasDamagedRoadbedLength: 0, // 损毁路面长度-砂石路面
        dinasDamagedFullAmount: 0, // 损失全额-砂石路面
        // 桥梁
        bridgeDamagedType: '', // 桥梁毁坏类型
        bridgeDamagedLength: 0, // 损毁长度-桥梁
        bridgeDamagedNumber: 0, // 损毁数量-桥梁
        bridgeDamagedFullAmount: 0, // 损毁全额-桥梁
        // 隧道
        tunnelDamagedLength: 0, // 损毁长度-隧道
        tunnelDamagedNumber: 0, // 损毁数量-隧道
        tunnelDamagedFullAmount: 0, // 损毁全额-隧道
        // 涵洞
        culvertDamagedNumber: 0, // 损毁数量-涵洞
        culvertDamagedFullAmount: 0, // 损毁全额-涵洞
        // 防护工程
        revetmentDamagedVolume: 0, // 损毁方量-坡护
        revetmentDamagedNumber: 0, // 损毁数量-坡护
        revetmentDamagedFullAmount: 0, // 损毁全额-坡护

        barricadeDamagedVolume: 0, // 损毁方量-驳岸、挡墙
        barricadeDamagedNumber: 0, // 损毁数量-驳岸、挡墙
        barricadeDamagedFullAmount: 0, // 损毁全额-驳岸、挡墙
        // 坍塌方
        collapseDamagedVolume: 0, // 损毁方量-坍塌方
        collapseDamagedNumber: 0, // 损毁数量-坍塌方
        collapseDamagedFullAmount: 0, // 损毁全额-坍塌方
        // 其它
        otherRemarks: '', // 其它备注
        otherDamagedAmount: 0, // 莫他灾毁损失
        // 合计
        totalLoss: 0, // 损失合计
        disasterImgIds: '', // 灾毁照片
        alreadySnatchedThrough: '', // 是否已抢通
        actualRushTimeExpected: '', // 预计抢通时间
        disasterLevel: '', // 灾毁等级
        remarks: '', // 备注
        // 应急抢通情况
        actualRushTime: '', // 实际抢通时间
        handled: '', // 是否已处置
        disposalTimeExpected: '', // 处置时间
        inputLaborExpected: 0, // 投入人工
        inputMachineryExpected: 0, // 投入机械
        inputFundExpected: 0, // 投入资金
        snatchedThroughImgIds: '', // 抢通后照片
        // 公函
        officialLetter: '', // 公函
        // 其它字段
        processId: '', // 流程ID
        status: '', // 状态
        createName: '', // 填报人名称
        createUnit: '', // 填报人单位
        createTime: '', // 填报时间
      },
      formLoading: false, // 表单加载
      processIsApprove: '0', // 流程是否可审批
      formRules: { // 表单校验规则
        disasterType: [
          {required: true, message: "灾毁类型不能为空", trigger: "blur"}
        ],
        disasterRiskId: [
          {required: true, message: "需要关联风险点信息", trigger: "blur"}
        ],
      },
      riskSearchDialog: false, // 关联风险点查询dialog
      riskTableLoading: false, // 关联风险点数据加载
      riskFormLoading: false, // 表单加载（主表基础信息）
      riskFormDialog: false, // 关联风险点表单dialog
      riskFormDetailLoading: false, // 表单加载（风险点详细信息）
      riskFormParams: { // 关联风险点表单（主表）
        id: '', // 主表ID
        areaCode: '', // 县级区划编码
        disasterNum: '', // 灾害编号
        disasterName: '', // 灾害名称
        disasterType: '', // 灾害类型
        routerNum: '', // 路线编号
        routerName: '', // 路线名称
        startLatitudeAndLongitude: '', // 经纬度（起）
        endLatitudeAndLongitude: '', // 经纬度（止）
        technicalGrade: '', // 技术等级
        pileStartNum: '', // 桩号（起）
        pileEndNum: '', // 桩号（终）
        maintenanceUnitId: '', // 管养单位ID
        maintenanceUnitName: '', // 管养单位名称
        roadSectionId: '', // 路段ID
        roadSectionName: '', // 路段名称
        status: '', // 流程状态
        createName: '', // 采集人
        createTime: '', // 采集时间
        updateBy: '', // 更新人
        updateTime: '', // 更新时间
      },
      formMaintenanceList: [], // 管养单位列表
      processParams: { // 表单提交参数
        taskId: '', // 流程ID
        variables: {
          comment: '', // 审批意见
          operatingRecord: '', // 操作记录
          approved: true, // 是否通过
          variablesJson: [], // 附加变量
        },
        disasterType: '', // 表单类型
        formData: {}, // 表单参数
      },
      formData: {}, // 主表字段备份
      formDataContrast: { // 主表对照中文
        id: '主表ID',
        disasterType: '灾害类型',
        disasterRiskId: '风险点采集ID',
        disasterSectionId: '灾毁段编号',
        startLatitudeAndLongitude: '经纬度（起）',
        endLatitudeAndLongitude: '经纬度（止）',
        pileStartNum: '桩号（起）',
        pileEndNum: '桩号（终）',
        roadbedWidth: '路面宽度',
        roadbedType: '路面类型',
        disasterStartTime: '灾毁发生时间',
        interrupt: '是否中断',
        disasterFacilitiesType: '灾毁设施类型',
        damagedRoadbedVolume: '损毁路基方量',
        damagedRoadbedLength: '损毁路基长度',
        damagedFullAmount: '损失全额',
        asphaltDamagedRoadbedArea: '损毁路面面积-沥青路面',
        asphaltDamagedRoadbedLength: '损毁路面长度-沥青路面',
        asphaltDamagedFullAmount: '损失全额-沥青路面',
        cementDamagedRoadbedArea: '损毁路面面积-水泥路面',
        cementDamagedRoadbedLength: '损毁路面长度-水泥路面',
        cementDamagedFullAmount: '损失全额-水泥路面',
        dinasDamagedRoadbedArea: '损毁路面面积-砂石路面',
        dinasDamagedRoadbedLength: '损毁路面长度-砂石路面',
        dinasDamagedFullAmount: '损失全额-砂石路面',
        bridgeDamagedType: '桥梁毁坏类型',
        bridgeDamagedLength: '损毁长度-桥梁',
        bridgeDamagedNumber: '损毁数量-桥梁',
        bridgeDamagedFullAmount: '损毁全额-桥梁',
        tunnelDamagedLength: '损毁长度-隧道',
        tunnelDamagedNumber: '损毁数量-隧道',
        tunnelDamagedFullAmount: '损毁全额-隧道',
        culvertDamagedNumber: '损毁数量-涵洞',
        culvertDamagedFullAmount: '损毁全额-涵洞',
        revetmentDamagedVolume: '损毁方量-坡护',
        revetmentDamagedNumber: '损毁数量-坡护',
        revetmentDamagedFullAmount: '损毁全额-坡护',
        barricadeDamagedVolume: '损毁方量-驳岸、挡墙',
        barricadeDamagedNumber: '损毁数量-驳岸、挡墙',
        barricadeDamagedFullAmount: '损毁全额-驳岸、挡墙',
        collapseDamagedVolume: '损毁方量-坍塌方',
        collapseDamagedNumber: '损毁数量-坍塌方',
        collapseDamagedFullAmount: '损毁全额-坍塌方',
        otherRemarks: '其他备注',
        otherDamagedAmount: '莫他灾毁损失',
        totalLoss: '损失合计',
        disasterImgIds: '灾毁照片',
        alreadySnatchedThrough: '是否已抢通',
        actualRushTimeExpected: '预计抢通时间',
        disasterLevel: '灾毁等级',
        remarks: '备注',
        actualRushTime: '实际抢通时间',
        handled: '是否已处置',
        disposalTimeExpected: '处置时间（预计）',
        inputLaborExpected: '投入人工（预计）',
        inputMachineryExpected: '投入机械（预计）',
        inputFundExpected: '投入资金（预计）',
        snatchedThroughImgIds: '抢通后照片',
        officialLetter: '公函',
        processId: '流程ID',
        dataSources: '数据来源',
        status: '状态（0正常 1停用）',
        delFlag: '删除标志（0代表存在 2代表删除）',
        createName: '创建人名称',
        createUnit: '创建人单位'
      },
      disasterFacilitiesData: [], // 灾毁设施类型字典数据
      formDialog: false, // 表单dialog显隐
      // 灾毁照片
      formImgSrcList1: [], // 图片渲染列表
      formImgUrlList1: [], // 图片预览列表
      formUploadList1: '', // 图片上传列表
      formImgOwnerId1: '', // 图片上传ownerId1
      // 抢通后照片
      formImgSrcList2: [], // 图片渲染列表
      formImgUrlList2: [], // 图片预览列表
      formUploadList2: '', // 图片上传列表
      formImgOwnerId2: '', // 图片上传ownerId2
      // 公函
      formFileSrcList: [], // 图片渲染列表
      formUploadList3: '', // 文件上传列表
      formFileOwnerId: '', // 文件上传ownerId

      formImgNum: [], // 图片数量（图片集用）
    }
  },
  computed: {
    // 沥青路面勾选状态
    asphaltSelect() {
      if (this.formParams.asphaltDamagedRoadbedArea > 0) {
        return true
      }
      if (this.formParams.asphaltDamagedRoadbedLength > 0) {
        return true
      }
      if (this.formParams.asphaltDamagedFullAmount > 0) {
        return true
      }
      return false
    },
    // 水泥路面勾选状态
    cementSelect() {
      if (this.formParams.cementDamagedRoadbedArea > 0) {
        return true
      }
      if (this.formParams.cementDamagedRoadbedLength > 0) {
        return true
      }
      if (this.formParams.cementDamagedFullAmount > 0) {
        return true
      }
      return false
    },
    // 砂石路面勾选状态
    dinasSelect() {
      if (this.formParams.dinasDamagedRoadbedArea > 0) {
        return true
      }
      if (this.formParams.dinasDamagedRoadbedLength > 0) {
        return true
      }
      if (this.formParams.dinasDamagedFullAmount > 0) {
        return true
      }
      return false
    },
    // 护坡勾选状态
    revetmentSelect() {
      if (this.formParams.revetmentDamagedVolume > 0) {
        return true
      }
      if (this.formParams.revetmentDamagedNumber > 0) {
        return true
      }
      if (this.formParams.revetmentDamagedFullAmount > 0) {
        return true
      }
      return false
    },
    // 驳岸、挡墙勾选状态
    barricadeSelect() {
      if (this.formParams.barricadeDamagedVolume > 0) {
        return true
      }
      if (this.formParams.barricadeDamagedNumber > 0) {
        return true
      }
      if (this.formParams.barricadeDamagedFullAmount > 0) {
        return true
      }
      return false
    },
    // 灾毁图片时间线
    formImgDateLine1() {
      let dateLine = []
      if (this.formImgSrcList1.length > 0) {
        this.formImgSrcList1.forEach(item => {
          let date = item.createTime.split('T')[0]
          if (dateLine.length === 0) {
            dateLine.push({
              time: date,
              data: [item]
            })
          } else {
            let index = dateLine.findIndex(item2 => item2.time === date)
            if (index !== -1) {
              dateLine[index].data.push(item)
            } else {
              dateLine.push({
                time: date,
                data: [item]
              })
            }
          }
        })
        // 时间线排序
        dateLine.sort((a, b) => {
          // 将日期字符串分割并转换为日期对象
          let dateA = new Date(a.time);
          let dateB = new Date(b.time);

          // 比较日期
          return dateA - dateB;
        })
      }

      return dateLine
    },
    // 抢通后图片时间线
    formImgDateLine2() {
      let dateLine = []
      if (this.formImgSrcList2.length > 0) {
        this.formImgSrcList2.forEach(item => {
          let date = item.createTime.split('T')[0]
          if (dateLine.length === 0) {
            dateLine.push({
              time: date,
              data: [item]
            })
          } else {
            let index = dateLine.findIndex(item2 => item2.time === date)
            if (index !== -1) {
              dateLine[index].data.push(item)
            } else {
              dateLine.push({
                time: date,
                data: [item]
              })
            }
          }
        })
        // 时间线排序
        dateLine.sort((a, b) => {
          // 将日期字符串分割并转换为日期对象
          let dateA = new Date(a.time);
          let dateB = new Date(b.time);

          // 比较日期
          return dateA - dateB;
        })
      }

      return dateLine
    },
    //控制表格头部修改按钮
    tableEditDisabled() {
      if (this.tableSelection.length === 1) {
        if (this.tableSelection[0].status !== '0') {
          return true
        } else {
          return false
        }
      }
      return true
    },
    //控制表头部删除按钮
    tableDelDisabled() {
      let reviewIndex = this.tableSelection.findIndex((item) => item.status === '1');
      let finishIndex = this.tableSelection.findIndex((item) => item.status === '2');
      if (this.tableSelection.length > 0 && reviewIndex === -1 && finishIndex === -1) {
        return false
      }
      return true
    }
  },
  mounted() {
    this.initPage()
  },
  methods: {
    // 初始化页面
    initPage() {
      // 流程提交参数赋值
      this.processParams.taskId = this.$route.query.taskId
      // 流程是否可审批
      this.processIsApprove = this.$route.query.isApprove
      // 查询流程表单数据
      this.queryList(this.$route.query.businessKey)
      // 获取管养单位数据
      this.queryMaintenanceList()
      // 获取区划数据（市级）
      this.queryDivisionsTree(2)
    },
    // 获取高边坡数据
    async queryList(id) {
      this.formLoading = true
      await queryDamageById(id).then((res) => {
        if (res.code === 200) {
          this.riskFormParams = res.data.disasterRiskDto.disasterRisk
          this.formData = JSON.parse(JSON.stringify(res.data.disasterSubmitted));
          this.formParams = res.data.disasterSubmitted
          if (this.formParams.disasterFacilitiesType) {
            this.formParams.disasterFacilitiesType = this.formArrayChange(this.formParams.disasterFacilitiesType)
          }
          //灾毁照片
          if (this.formParams.disasterImgIds) {
            this.queryImg(this.formParams.disasterImgIds, 1)
            this.formImgOwnerId1 = this.formParams.disasterImgIds
          } else {
            this.formImgOwnerId1 = 'ZH' + new Date().getTime().toString()
          }
          //抢通后图片
          if (this.formParams.snatchedThroughImgIds) {
            this.queryImg(this.formParams.snatchedThroughImgIds, 2)
            this.formImgOwnerId2 = this.formParams.snatchedThroughImgIds
          } else {
            this.formImgOwnerId2 = 'QT' + new Date().getTime().toString()
          }
          //公函
          if (this.formParams.officialLetter) {
            this.queryImg(this.formParams.officialLetter, 3)
            this.formFileOwnerId = this.formParams.officialLetter
          } else {
            this.formFileOwnerId = 'GH' + new Date().getTime().toString()
          }
        }
        this.formLoading = false
      }).catch((err) => {
        this.formLoading = false
        this.$message.error(err)
      })
    },
    riskList() {
      this.riskTableLoading = true
      queryPageRisk(this.riskQueuryParams).then((res) => {
        if (res.code === 200) {
          this.riskTableData = res.rows
          this.riskTableTotal = res.total
        }
        this.riskTableLoading = false
      }).catch(() => {
        this.riskTableLoading = false
      })
    },
    // 获取管养单位
    queryMaintenanceList() {
      let vo = {
        deptTypeList: [1, 3, 4],
        types: 100
      }
      deptTreeSelect(vo).then((res) => {
        console.log('养护路段数据：', res)
        if (res.code === 200) {
          this.handleMainRender(res.data)
        }
      }).catch((err) => {
        this.$message.error(err)
        this.formMaintenanceList = []
      })
    },
    // 管养单位渲染列表处理
    handleMainRender(data) {
      data.forEach(item => {
        this.formMaintenanceList.push(item)
        if (item.children) {
          this.handleMainRender(item.children)
        }
      })
    },
    // 获取区划树
    queryDivisionsTree(level) {
      let vo = {}
      switch (level) {
        case 2:
          vo = {
            supCode: this.divisionsProvincial
          }
          break;

        case 3:
          vo = {
            supCode: this.divisionsMunicipal
          }
          break;
      }

      getTreeByEntity(vo).then((res) => {
        console.log('区划数据：', res)
        if (res) {
          switch (level) {
            case 2:
              this.divisionsMunicipalList = res.data
              break;

            case 3:
              this.areaCodeList = res.data
              break;
          }
        }
      })
    },
    // 获取关联风险点数据
    queryRiskData() {
      this.riskFormLoading = true
      this.riskFormDetailLoading = true
      console.log('关联风险点ID：', this.formParams.disasterRiskId)
      queryPageRiskById(this.formParams.disasterRiskId).then((res) => {
        console.log('关联风险点数据：', res)
        if (res.code == 200) {
          this.riskFormParams = res.data.disasterRisk
          this.riskFormSubParams = res.data.data
        }
        this.riskFormLoading = false
        this.riskFormDetailLoading = false
      }).catch(() => {
        this.riskFormLoading = false
        this.riskFormDetailLoading = false
      })
    },
    // 获取风险点详情并渲染
    queryRiskDetail() {
      if (!this.formParams.disasterRiskId) {
        this.$message.warning('请先选择关联风险点')
        return
      }
      this.queryRiskData()
      this.riskFormDialog = true
      this.$nextTick(() => {
        this.$refs.formSubRef.initPage(this.riskFormSubParams, this.riskFormParams.disasterType)
      })
    },
    // 选择灾毁类型默认在新增时显示选中数据
    disasterTypeSelectHandle(val) {
      this.riskQueuryParams.disasterType = val
    },
    // 当灾毁设施类型改变时
    formFacilitiesChange() {
      if (this.disasterFacilitiesData === 0) {
        this.queryDamageFacilityDict().then(() => {
          this.formWwitchFacilities()
        }).catch(() => {
          return
        })
      } else {
        this.formWwitchFacilities()
      }
    },
    // 获取图片数据
    async queryImg(id, index) {
      try {
        const res = await findFiles({ownerId: id});
        if (res.code === 200 && res.data.length > 0) {
          const dataList = res.data.map((item) => ({
            id: `${item.ownerId}-${item.id}`,
            name: item.originalFilename,
            url: item.thumbUrl,
            imgUrl: item.url,
            createTime: item.createTime,
            createBy: item.createBy
          }));

          // 根据索引将图片或文件添加到对应列表
          switch (index) {
            case 1: // 灾毁照片
              this.formImgSrcList1.push(...dataList);
              this.formImgUrlList1.push(...res.data.map(item => item.url));
              break;
            case 2: // 抢通后图片
              this.formImgSrcList2.push(...dataList);
              this.formImgUrlList2.push(...res.data.map(item => item.url));
              break;
            case 3: // 公函
              this.formFileSrcList.push(...dataList);
              break;
          }
        } else if (res.code === 200 && res.data.length === 0) {
          switch (index) {
            case 1: // 灾毁照片
              this.formParams.disasterImgIds = ''
              break;
            case 2: // 抢通后图片
              this.formParams.snatchedThroughImgIds = ''
              break;
            case 3: // 公函
              this.formParams.officialLetter = ''
              break;
          }
        }
      } catch (error) {
        this.$message.error('图片查询失败，请重新打开表单尝试');
      }
    },

    // 点击预览图片时
    formImgPreview(url, listIndex) {
      let targetList;

      // 根据传入的 listIndex 确定目标列表
      if (listIndex === 1) {
        targetList = this.formImgUrlList1;
      } else if (listIndex === 2) {
        targetList = this.formImgUrlList2;
      }

      // 查找图片的索引
      let index = targetList.findIndex(item => item === url);
      if (index !== -1) {
        // 移动该图片到列表的开头
        let moveUrl = targetList.splice(index, targetList.length - index);
        targetList.unshift(...moveUrl);
      }
    },

    // 删除图片通用方法
    async formDeleteImg(id, type) {
      const isDisasterImg = type === 1; // 判断是灾毁照片还是抢通后照片
      const modalText = isDisasterImg ? '灾毁照片' : '抢通后照片';
      const imgListPrefix = isDisasterImg ? 'disasterImg' : 'snatchedThroughImg';
      const formImgSrcList = isDisasterImg ? this.formImgSrcList1 : this.formImgSrcList2;
      const formImgUrlList = isDisasterImg ? this.formImgUrlList1 : this.formImgUrlList2;
      const formImgOwnerId = isDisasterImg ? this.formImgOwnerId1 : this.formImgOwnerId2;

      this.$modal.confirm(`是否确认删除${modalText}？`).then(async () => {
        this.$modal.loading('正在删除图片，请稍候...');
        try {
          const res = await removeFile(id.split('-')[1]);
          if (res.code === 200) {
            this.$message.success('删除图片成功！');

            // 移除预览列表图片
            let index = formImgSrcList.findIndex(item => item.id === id);
            if (index !== -1) {
              let imgUrl = formImgSrcList[index].imgUrl;
              let imgIndex = formImgUrlList.indexOf(imgUrl);
              if (imgIndex !== -1) {
                formImgUrlList.splice(imgIndex, 1);
              }
            }

            // 移除渲染列表图片
            formImgSrcList.splice(index, 1);

            // 移除表单图片字段
            let localList = this.formParams[`${imgListPrefix}Ids`].split(',');
            let check = true;
            if (localList.length > 0) {
              // 判断是单图片还是图片集
              if (id.includes(formImgOwnerId)) {
                check = false; // 图片集
              }
            }

            // 单图片和图片集分类处理
            switch (check) {
              case true: // 单图片
                let index2 = localList.indexOf(id);
                if (index2 !== -1) {
                  localList.splice(index2, 1);
                  this.formParams[`${imgListPrefix}Ids`] = localList.join(',');
                }
                break;

              case false: // 图片集
                let imgIndex = this.formImgNum.findIndex(item => item.id === id.split('-')[0]);
                if (imgIndex !== -1) {
                  if (this.formImgNum[imgIndex].num === 1) {
                    // 如果图片集只剩一张图片，删除整个图片集
                    this.formImgNum.splice(imgIndex, 1);
                    let index3 = localList.indexOf(id.split('-')[0]);
                    if (index3 !== -1) {
                      localList.splice(index3, 1);
                      this.formParams[`${imgListPrefix}Ids`] = localList.join(',');
                    }
                  } else {
                    // 如果图片集还有多张，减少图片集数量
                    this.formImgNum[imgIndex].num--;
                  }
                }
                break;
            }
          } else {
            this.$message.error('删除图片失败');
          }
        } catch (error) {
          this.$message.error('删除图片失败');
        }
        this.$modal.closeLoading();
      });
    },

    // 文件下载
    async formDownloadFile(url, fileName) {
      try {
        // 使用fetch请求文件
        const response = await fetch(url);
        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }
        const blob = await response.blob(); // 将响应体转换为Blob对象

        // 创建下载链接
        const link = document.createElement('a');
        link.href = window.URL.createObjectURL(blob);
        link.setAttribute('download', fileName); // 设置下载的文件名
        link.style.display = 'none';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
      } catch (error) {
        this.$message.error('下载文件错误');
      }
    },

    // 删除文件（公函）
    formDeleteFile(id) {
      this.$modal.confirm('是否确认删除？').then(async () => {
        this.$modal.loading('正在删除文件，请稍候...')
        removeFile(id.split('-')[1]).then((res) => {
          if (res.code === 200) {
            this.$message.success('删除文件成功！')
            // 移除渲染列表文件
            this.formFileSrcList = this.formFileSrcList.filter(item => item.id !== id)
            // 移除表单公函字段
            let localList = this.formParams.officialLetter.split(',')
            // 判断是单文件还是文件集
            let check = true
            if (localList.length > 0) {
              // 图片集
              if (id.includes(this.formFileOwnerId)) {
                check = false
              }
            }
            // 单图片和图片集分类处理
            switch (check) {
              // 单图片
              case true:
                console.log('单文件')
                let index2 = localList.indexOf(id)
                if (index2 !== -1) {
                  localList.splice(index2, 1)
                  this.formParams.officialLetter = localList.join(',')
                }
                break;

              // 图片集
              case false:
                console.log('文件集')
                let imgIndex = this.formImgNum.findIndex(item => item.id === id.split('-')[0])
                if (imgIndex !== -1) {
                  console.log('对应的文件集：', this.formImgNum[imgIndex])
                  // 如果对应的图片集只有一张图片，则删除图片集
                  if (this.formImgNum[imgIndex].num === 1) {
                    this.formImgNum.splice(imgIndex, 1)
                    let index3 = localList.findIndex(item => item === id.split('-')[0])
                    if (index3 !== -1) {
                      localList.splice(index3, 1)
                      this.formParams.officialLetter = localList.join(',')
                    }
                  } else {
                    this.formImgNum[imgIndex].num--
                  }
                }
                break;
            }
          } else {
            this.$message.error('删除文件失败')
          }
          this.$modal.closeLoading()
        }).catch(() => {
          this.$message.error('删除文件失败')
          this.$modal.closeLoading()
        })
      })
    },

    // 表单损失合计计算
    formtotalCompute() {
      let total = 0
      total = this.formParams.damagedFullAmount + this.formParams.asphaltDamagedFullAmount +
        this.formParams.cementDamagedFullAmount + this.formParams.dinasDamagedFullAmount +
        this.formParams.bridgeDamagedFullAmount + this.formParams.tunnelDamagedFullAmount +
        this.formParams.culvertDamagedFullAmount + this.formParams.revetmentDamagedFullAmount +
        this.formParams.barricadeDamagedFullAmount + this.formParams.collapseDamagedFullAmount +
        this.formParams.otherDamagedAmount
      this.formParams.totalLoss = total
    },
    // 打开查询风险点dialog
    openRiskSearchDialog() {
      this.riskList()
      this.riskSearchDialog = true
    },
    // 提交表单
    async formAssembly(formName) {
      return new Promise((resolve, reject) => {
        this.$refs[formName].validate((valid) => {
          if (valid) {
            this.formBtnLoading = true
            let vo = this.formParams

            vo.disasterFacilitiesType = Array.isArray(vo.disasterFacilitiesType) ? vo.disasterFacilitiesType.join(",") : ''

            if (this.formUploadList1) {
              vo.disasterImgIds = this.formImgOwnerId1
            }
            if (this.formUploadList2) {
              vo.snatchedThroughImgIds = this.formImgOwnerId2
            }
            if (this.formUploadList3) {
              vo.officialLetter = this.formFileOwnerId
            }
            resolve(vo)

          } else {
            this.$message.warning('表单必填字段未填写！')
            reject(false)
          }
        })
      })
    },
    // 表单流程提交
    async formProcessSubmit(param) {
      this.$modal.loading("正在提交，请稍候...")
      // 赋予本地参数
      this.processParams.variables.approved = param.approved
      this.processParams.variables.comment = param.comment
      // 获取表单提交数据
      let vo = await this.formAssembly('formRef')
      if (!vo) {
        this.$modal.closeLoading()
        return
      }
      this.processParams.formData = vo
      this.processParams.disasterType = vo.disasterType
      // 表单字段修改对比校验
      this.processParams.variables.operatingRecord = JSON.stringify(this.formCompare(vo))
      submitTaskDamage(this.processParams).then((res) => {
        if (res.code === 200) {
             window.close()
        }
        this.$modal.closeLoading()
      }).catch(() => {
        this.$modal.closeLoading()
      })
    },
    // 表单字段对比校验
    formCompare(vo) {
      // 备份数据
      let oldVo = this.formData
      // 对比校验数据
      let content = []
      // 循环校对
      for (let key in oldVo) {
        if (oldVo[key] !== vo[key]) {
          content.push({
            name: this.formDataContrast[key],
            oldVal: oldVo[key],
            newVal: vo[key]
          })
        }
      }
      return content
    },
    // 表单数组转换
    formArrayChange(item) {
      if (item) {
        let check = item.includes(',')
        if (check) {
          return item.split(',')
        } else {
          return [item]
        }
      } else {
        return []
      }
    },
  }
}
</script>

<style lang="scss" scoped>
.infoBox {
  padding: 15px 15px 0 15px;
  box-sizing: border-box;
  border-radius: 6px;
  border: 1px solid #C4C4C4;
  position: relative;

  .infoTitle {
    user-select: none;
    position: absolute;
    top: 0;
    left: 0;
    padding: 0 10px;
    font-size: 14px;
    line-height: 14px;
    font-weight: bold;
    transform: translateX(15px) translateY(-50%);
    background-color: white;
  }

  .imgBox {
    height: auto;
    width: 100%;

    .imgItemBox {
      height: 240px;
      width: calc(100% / 3);
      box-sizing: border-box;
      padding: 10px;
      overflow-y: auto;
      display: flex;
      align-items: center;
      justify-content: center;
      position: relative;

      .imgDeleteBtn {
        position: absolute;
        z-index: 1;
        top: 10%;
        right: 10%;
      }

    }

    ::v-deep .el-card__body {
      width: 100%;
      display: flex;
      flex-direction: row;
      flex-wrap: wrap;
      align-content: flex-start;
    }

    .imgBoxCard {
      width: 100%;

      .cardMain {
        height: 260px;
        width: 33%;
        box-sizing: border-box;
        padding: 0 10px;
        display: flex;
        flex-direction: column;
        position: relative;

        .imgDeleteBtn {
          position: absolute;
          z-index: 1;
          top: 20%;
          right: 5%;
        }

        .imgTitle {
          height: 28px;
          width: 100%;
          font-size: 16px;
          font-weight: bold;
          white-space: nowrap;
          text-overflow: ellipsis;
          overflow: hidden;
        }

        .img {
          height: calc(100% - (28px + 28px));
          width: 100%;
          padding: 10px 0;
          position: relative;
          z-index: 0;
        }

        .footer {
          height: 28px;
          color: #888888;
          font-size: 14px;
        }

      }
    }
  }

  .fileBox {
    width: 100%;
    display: flex;
    flex-direction: column;

    .fileItemBox {
      width: 100%;
      padding: 10px 0;

      .fileItem {
        width: 100%;
        padding: 10px;
        border: 1px solid #DCDFE6;
        display: flex;
        flex-direction: row;

        .boxLeft {
          height: auto;
          width: 48px;
          font-size: 24px;
          display: flex;
          justify-content: center;
          align-items: center;
        }

        .boxmiddle {
          height: auto;
          width: calc(100% - (48px + 200px));
          color: #888888;
          font-size: 14px;
          display: flex;
          align-items: center;
        }

        .boxRight {
          height: auto;
          width: 200px;
          display: flex;
          justify-content: center;
          align-items: center;
        }

      }


    }
  }

  .noneBox {
    user-select: none;
    height: 200px;
    width: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    color: #888888;
  }

}

.formDialog {
  ::v-deep .el-dialog__body {
    height: 600px;
    overflow-y: auto;
  }

  .dialog-footer {
    width: 100%;

    .footerTip {
      color: #888888;
      font-size: 14px;
    }
  }

  .titleBox {
    height: 22px;
    display: flex;
    flex-direction: row;
    align-items: center;

    .title {
      font-size: 16px;
      color: black;
      margin: 0;
    }

    .subTitle {
      margin-left: 15px;
      font-size: 12px;
      color: #888888;
    }

    .riskLevel {
      user-select: none;
      position: absolute;
      // top: 0;
      right: 5%;
      display: flex;
      align-items: center;
      flex-direction: row;

      .title {
        font-size: 16px;
        font-weight: bold;
      }

      .main {
        font-size: 16px;
        font-weight: bold;
        padding: 5px 10px 5px 10px;
        color: white;
        box-sizing: border-box;
        border-radius: 5px;
      }

      .score {
        color: #888888;
        font-size: 14px;
      }
    }
  }
}
</style>
