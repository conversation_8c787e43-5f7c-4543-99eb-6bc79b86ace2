<template>
	<div class="charts" :class="theme" ref="chartsRef" v-loading="loading"
		element-loading-background="rgba(0, 0, 0, 0.2)">
		<!-- 搜索框 -->
		<div class="search-box">
			<div class="search-item">
				<span>设备安装代码</span>
				<el-select v-model="searchForm.sensorId" placeholder="请选择" @change="changeSensor">
					<el-option v-for="(item, index) in sensorOptions" :key="index" :label="item.sensorInstallCode"
						:value="item.sensorId">
					</el-option>
				</el-select>
			</div>
			<div class="search-item" v-show="activeName != '3'">
				<span>选择Y轴值类型和顺序</span>
				<el-select v-model="searchForm.specificMonitorContentIds" placeholder="请选择" multiple :disabled="searchDisable"
					@change="handleDraw">
					<el-option v-for="(item, index) in specificMonitorTypeList" :key="index" :label="item.specificMonitorTypeName"
						:value="item.specificMonitorTypeId">
					</el-option>
				</el-select>
			</div>
			<div class="search-item" v-show="activeName == '2'">
				<span>数据采样</span>
				<div class="select-box">
					<div v-for="(item, index) in ['原始', '分', '10分', '小时', '日']" :key="index"
						@click="searchDisable ? null : handleSelectBox(item)"
						:class="{ 'select-item-active': searchForm.granularityTypeName === item, 'select-item-disabled': searchDisable }">
						{{ item }}
					</div>
				</div>
			</div>
			<div class="check-box search-item" v-show="activeName != '3'">
				<el-checkbox v-model="searchForm.isPreprocess" :disabled="searchDisable" @change="handleDraw">预处理</el-checkbox>
			</div>
			<div class="check-box search-item" v-show="activeName != '3'">
				<el-checkbox v-model="searchForm.isShowLimit" :disabled="searchDisable" @change="handleDraw">预警线</el-checkbox>
			</div>
			<div class="search-item" v-show="activeName == '2'">
				<span>时间范围</span>
				<el-date-picker v-model="timeRange" type="datetimerange" range-separator="-" start-placeholder="开始日期"
					end-placeholder="结束日期" value-format="yyyy-MM-dd HH:mm:ss">
				</el-date-picker>
			</div>
			<el-button v-show="activeName != '3'" class="search-item" type="primary" @click="handleDraw"
				:disabled="!sensorData" :loading="drawLoading">绘制</el-button>

			<div class="switch-container">
				<el-switch v-show="hasGXDLZT" v-model="showTable" active-text="显示断线仪表格" inactive-text="">
				</el-switch>
			</div>



			<el-button v-show="activeName == '3'" class="search-item" type="primary"
				:disabled="!sensorData || noDataForChart3" @click="handleDraw" :loading="realtimeLoading">{{ stopFlag ? '停止' :
					'开始' }}</el-button>
		</div>

		<div class="main-tabs" ref="mainTabs">
			<el-tabs v-model="activeName" tab-position="top" @tab-click="tabClick" type="border-card">
				<el-tab-pane label="地图" name="0">
				</el-tab-pane>
				<el-tab-pane label="实时数据" name="1">
				</el-tab-pane>
				<el-tab-pane label="历史数据" name="2">
				</el-tab-pane>
				<el-tab-pane label="实时数据(推送)" name="3">
				</el-tab-pane>
				<el-tab-pane label="结构物信息" name="4">
				</el-tab-pane>
			</el-tabs>
			<div class="main-tabs-charts" v-show="activeName != '4'">
				<RealtimeCharts v-show="activeName == '1'" :activeName="activeName" :showTable="showTable" ref="chart1"
					:theme="theme" key="RealtimeCharts" @setDisabled="searchDisable = true" />
				<RealtimeCharts v-show="activeName == '2'" :activeName="activeName" ref="chart2" :theme="theme"
					key="HistoryCharts" @changeGranularity="changeGranularity" @setDisabled="searchDisable = true" />
				<ChartThree v-if="sensorData" v-show="activeName == '3'" :activeName="activeName" ref="chart3" :theme="theme"
					@noData="noData" />
				<div class="empty-msg" v-if="activeName != '0' && !sensorData">请先选择传感器</div>
				<div style="height: 100%; width: 100%; position: relative" v-show="activeName == '0'">
					<div class="topBottom">
						<!-- <template v-if="!cmpUpStart || !cmpDownStart">
							<el-button type="primary" @click="startWarnPlan('')" :loading="startAllLoading">
								启动全部</el-button>
						</template> -->
						<el-button type="primary" @click="startWarnPlan('')" :loading="startAllLoading">
							启动全部</el-button>
						<template v-if="cmpUpStart">
							<el-button class="blinking" type="primary">
								上行已启动</el-button>
						</template>
						<template v-else>
							<el-button type="primary" @click="startWarnPlan('上行')" :loading="startUpLoading">
								启动上行</el-button>
						</template>

						<template v-if="cmpDownStart">
							<el-button class="blinking" type="primary">
								下行已启动</el-button>
						</template>
						<template v-else>
							<el-button type="primary" @click="startWarnPlan('下行')" :loading="startDownLoading">
								启动下行</el-button>
						</template>
						<el-button type="primary" @click="endWarnPlan" :loading="endLoading">
							一键结束</el-button>
						<!-- <template v-if="cmpUpStart || cmpDownStart">
							<el-button type="primary" @click="endWarnPlan" :loading="endLoading">
								一键结束</el-button>
						</template> -->
					</div>
					<map-view />
				</div>
			</div>
			<div class="main-tabs-text" :class="centerClass" v-show="activeName == '4'" v-html="displayHtmlText"></div>
		</div>

	</div>
</template>

<script>
import RealtimeCharts from './components/ChartOneAndTwo.vue'
import LineTesterTabel from './components/LineTesterTabel.vue'
import ChartThree from './components/ChartThree.vue'
// const mapView = () => import('./components/mapView.vue')
const mapView = () => import('./components/map.vue')
import {
	startWarnPlan,//启动上行，下行，全部
	endWarnPlan,//结束预案
	getWdmStructFlashDeviceStatus,//读取结构物在线状态信息
	offSiteFlashWarning,//解除现场预警
} from '@/api/Tibet/index'

import {
	fetchGet
} from './api.js'
export default {
	name: 'Charts',
	props: {
		// 主题
		theme: {
			type: String,
			default: 'dark'
		},
		// 选中tree的传感器列表
		sensorData: {
			default: null,
		},
		structureDisplayHtmlText: {
			default: null,
		}
	},
	components: {
		RealtimeCharts,
		ChartThree,
		mapView,
		LineTesterTabel
	},
	data() {
		return {
			showTable: false,
			searchForm: {
				sensorId: '',
				specificMonitorContentIds: [],
				isPreprocess: false,
				isShowLimit: false,
				granularityNum: 1,
				granularityType: {
					index: 5,
					label: '天'
				},
				formatter: "{yyyy}-{MM}-{dd}",
				formatterNum: 12,
				granularityTypeName: '日',
			},
			activeName: '0',
			timeRange: [],
			chartBoxHeight: '100%',
			sensorOptions: [],
			specificMonitorTypeList: [],
			loading: false,
			chartFlag1: false,
			chartFlag2: false,
			chartFlag3: false,
			historySearchStr: '',
			searchDisable: false,
			stopFlag: false,
			noDataForChart3: false,
			centerClass: '',
			analysisType: {
				index: 2,
				label: '均值'
			},
			btnLoading: false,
			warnData: {
				structId: "",
				direction: ""
			},
			resultUpData: null,
			resultDownData: null,
			// 新增加载状态
			drawLoading: false,
			realtimeLoading: false,
			startAllLoading: false,
			startUpLoading: false,
			startDownLoading: false,
			endLoading: false,
			structure: {},
		}
	},
	mounted() {
		this.init();
		this.getWdmStructFlashDeviceStatus();
	},
	methods: {
		async startWarnPlan(direction) {
			try {
				let loadingKey = 'startAllLoading';
				if (direction === '上行') {
					loadingKey = 'startUpLoading';
				} else if (direction === '下行') {
					loadingKey = 'startDownLoading';
				}

				this[loadingKey] = true;
				let data = JSON.parse(localStorage.getItem('mapData'));
				this.warnData.structId = data.id;
				this.warnData.direction = direction;
				// 调用API
				const response = await startWarnPlan(this.warnData);

				// 处理成功响应
				this.$message.success('预警计划已启动');
				console.log('响应数据:', response.data);
				this.getWdmStructFlashDeviceStatus();
				// 更新页面状态
				this.refreshData();
			} catch (error) {
				// 处理错误
				this.$message.error('启动预警计划失败');
				console.error('错误信息:', error);
			} finally {
				// 隐藏加载状态
				this.startAllLoading = false;
				this.startUpLoading = false;
				this.startDownLoading = false;
			}
		},
		async endWarnPlan() {
			try {
				this.endLoading = true;
				let data = JSON.parse(localStorage.getItem('mapData'));
				let postdate = { structId: data.id };
				// 调用API
				const response = await endWarnPlan(postdate);

				// 处理成功响应
				this.$message.success('预警计划已停止');
				console.log('响应数据:', response.data);
				this.getWdmStructFlashDeviceStatus();
				// 更新页面状态
				this.refreshData();
			} catch (error) {
				// 处理错误
				this.$message.error('停止预警计划失败');
				console.error('错误信息:', error);
			} finally {
				// 隐藏加载状态
				this.endLoading = false;
			}
		},
		async getWdmStructFlashDeviceStatus() {
			try {
				this.loading = true;
				let data = JSON.parse(localStorage.getItem('mapData'));
				let postdate = { structId: data.id };
				// 调用API
				const res = await getWdmStructFlashDeviceStatus(postdate);
				if (res?.data) {
					this.resultUpData = res.data.find((item) => {
						if (item.direction === "上行") {
							return item;
						}
					});
					this.resultDownData = res.data.find((item) => {
						if (item.direction === "下行") {
							return item;
						}
					});
				}
				// 更新页面状态
				this.refreshData();
			} catch (error) {
				// 处理错误
				this.$message.error('获取预警状态失败');
				console.error('错误信息:', error);
			} finally {
				// 隐藏加载状态
				this.loading = false;
			}
		},
		showUnInstall() {
			this.$message({
				type: "warning",
				message: "暂未接入设备,无法开启爆闪",
			});
		},
		init() {
			// 初始化获取高度
			this.$nextTick(() => {
				this.setMainTabsHeight();
			});
			// 初始化时间范围
			let endTime = new Date();
			let startTime = new Date(endTime.getTime() - 86400000 * 7)
			this.timeRange.push(startTime.getFullYear() + '-' + (startTime.getMonth() + 1) + '-' + startTime
				.getDate() + ' ' +
				startTime.getHours() + ':' + startTime.getMinutes() + ':' + startTime.getSeconds())
			this.timeRange.push(endTime.getFullYear() + '-' + (endTime.getMonth() + 1) + '-' + endTime.getDate() +
				' ' +
				endTime.getHours() + ':' + endTime.getMinutes() + ':' + endTime.getSeconds())

			// 监听窗口变化，重新渲染图表
			window.addEventListener('resize', () => {
				this.$nextTick(() => {
					this.setMainTabsHeight();
				});
			});

			// 监听 charts 元素的尺寸变化
			if (this.$refs.chartsRef) {
				new ResizeObserver(() => {
					this.$nextTick(() => {
						this.setMainTabsHeight();
						this.$refs['chart' + this.activeName]?.reLoad();
					});
				}).observe(this.$refs.chartsRef);
			}
		},
		// 动态设置 main-tabs 的高度
		setMainTabsHeight() {
			const searchBox = this.$el?.querySelector('.search-box');
			if (!searchBox) return;
			// 获取 search-box 的高度
			const searchBoxHeight = searchBox.offsetHeight;
			// 获取 search-box 的计算样式
			const searchBoxComputedStyle = window.getComputedStyle(searchBox);
			// 获取 search-box 的 margin-bottom 的值
			const searchBoxMarginBottom = searchBoxComputedStyle.getPropertyValue('margin-bottom');
			// 将 search-box 的 margin-bottom 的值转换为数字
			const searchBoxMarginBottomValue = parseFloat(searchBoxMarginBottom.replace('px', ''));

			// 设置 main-tabs 的高度
			if (this.$refs.mainTabs) {
				this.$refs.mainTabs.style.height = `calc(100% - ${searchBoxHeight + searchBoxMarginBottomValue}px)`;
			}
		},
		// 数据采样点击事件
		handleSelectBox(e) {
			this.searchForm.granularityTypeName = this.searchForm.granularityTypeName === e ? null : e;
			switch (e) {
				case '原始':
					this.searchForm.granularityNum = 1
					this.searchForm.granularityType = {
						index: 0,
						label: '原始'
					}
					this.searchForm.formatter = "{yyyy}-{MM}-{dd}\n{hh}:{mm}:{ss}";
					this.searchForm.formatterNum = 4;
					break;
				case '分':
					this.searchForm.granularityNum = 1
					this.searchForm.granularityType = {
						index: 3,
						label: '分'
					}
					this.searchForm.formatter = "{yyyy}-{MM}-{dd}\n{hh}:{mm}"
					this.searchForm.formatterNum = 7;
					break;
				case '10分':
					this.searchForm.granularityNum = 10
					this.searchForm.granularityType = {
						index: 3,
						label: '分'
					}
					this.searchForm.formatter = "{yyyy}-{MM}-{dd}\n{hh}:{mm}"
					this.searchForm.formatterNum = 7;
					break;
				case '小时':
					this.searchForm.granularityNum = 1
					this.searchForm.granularityType = {
						index: 4,
						label: '时'
					}
					this.searchForm.formatter = "{yyyy}-{MM}-{dd}\n{hh}:{mm}"
					this.searchForm.formatterNum = 7;
					break;
				case '日':
					this.searchForm.granularityNum = 1
					this.searchForm.granularityType = {
						index: 5,
						label: '天'
					}
					this.searchForm.formatter = "{yyyy}-{MM}-{dd}"
					this.searchForm.formatterNum = 12;
					break;
			}
			this.handleDraw()
		},
		changeGranularity(obj) {
			this.searchForm.formatter = obj?.formatter || this.searchForm.formatter
			this.searchForm.formatterNum = obj?.formatterNum || this.searchForm.formatterNum
			this.searchForm.granularityNum = obj?.granularityNum || this.searchForm.granularityNum
			this.searchForm.granularityType = obj?.granularityType || this.searchForm.granularityType
			this.searchForm.granularityTypeName = obj?.granularityTypeName || this.searchForm.granularityTypeName
			this.$forceUpdate()
		},
		// tab-click 事件
		tabClick() {
			// 确保能够实时更新高度
			this.$nextTick(() => {
				this.setMainTabsHeight();
				this.$refs['chart' + this.activeName]?.reLoad();
			});
		},
		async changeSensor() {
			window.$Bus.$emit('changeTreeNode', this.sensorOptions.find(item => item.sensorId === this.searchForm
				.sensorId))
			if (this.activeName == '3') {
				this.noDataForChart3 = false
				this.stopFlag = false
				this.chartFlag3 = false
				await this.$refs.chart3.stopDrawRealTimePushData()
				await this.$refs.chart3.disConnectRealTime()
			}
			setTimeout(async () => {
				this.handleDraw()
			}, 300)
		},
		async handleDraw() {
			if (!this.sensorData) return;

			if (!['0', '4'].includes(this.activeName)) {
				this.loading = true
				// 根据不同的tab设置不同的loading状态
				if (this.activeName === '3') {
					this.realtimeLoading = true;
				} else {
					this.drawLoading = true;
				}

				// 告诉Tree组件，我在走接口的时候不允许你点击！
				window.$Bus.$emit('treeClick', false)
				const obj = this.sensorOptions.find(item => item.sensorId === this.searchForm.sensorId)
				switch (this.activeName) {
					case '1':
						try {
							this.chartFlag1 = await this.$refs.chart1.drawRealTimeData(this.searchForm, obj);
						} catch (error) {
							this.$message.error('数据获取失败')
						}
						break;
					case '2':
						try {
							this.searchForm.timeRange = this.timeRange
							this.chartFlag2 = await this.$refs.chart2.drawHistoryData(this.searchForm, obj);
						} catch (error) {
							this.$message.error('数据获取失败')
						}
						break;
					case '3':
						if (this.stopFlag) {
							await this.$refs.chart3.stopDrawRealTimePushData()
							this.stopFlag = false
						} else {
							if (this.chartFlag3) {
								await this.$refs.chart3.connectRealTime()
								await this.$refs.chart3.startSendData()
							} else {
								this.chartFlag3 = await this.$refs.chart3.initChart3(obj);
							}
							this.stopFlag = true
						}
						break;
				}

				this.loading = false;
				this.drawLoading = false;
				this.realtimeLoading = false;
				window.$Bus.$emit('treeClick', true)
			}
		},
		async downloadData() {
			if (
				!this.searchForm.sensorId ||
				this.timeRange.length === 0
			) {
				this.$message({
					type: "warning",
					message: "下载数据前请注意查询条件的完整性",
				});
				return;
			}
			this.btnLoading = true
			const obj = this.sensorOptions.find(item => item.sensorId === this.searchForm.sensorId)
			//这里放参数
			const params = {
				nodeCode: obj.code,
				sensorId: obj.sensorId,
				structureNodeCode: this.$route.query.code,
				startTime: this.timeRange[0],
				endTime: this.timeRange[1],
				granularityNum: this.searchForm.granularityNum,
				granularityType: this.searchForm.granularityType?.index,
				analysisType: this.analysisType.index,
				specificMonitorContentIds: this.searchForm.specificMonitorContentIds,
				dataType: 1,
				rawOrProcess: this.isPreprocess ? 1 : 0
			};
			const url = "https://jkjc.yciccloud.com:8000/xboot/data/download"
			const res = await fetchGet(url, params)
			if (res.code === 200) {
				this.$message({
					type: 'success',
					message: res.message,
					duration: 2000
				});
			}
			this.btnLoading = false
		},
		noData() {
			this.noDataForChart3 = true
			setTimeout(() => {
				this.stopFlag = false
				this.realtimeLoading = false;
			}, 300)
		},
		refreshData() {
			// 刷新数据的逻辑
		}
	},
	computed: {
		displayHtmlText() {
			if (!this.structureDisplayHtmlText || this.structureDisplayHtmlText === "<p><br></p>") {
				this.centerClass = 'centerClass'
				const fontSize = this.isBig ? '28px' : '14px';
				return `<div style="text-align: center; font-size: ${fontSize};">没有结构物信息</div>`;
			} else {
				this.centerClass = ''
			}
			return this.structureDisplayHtmlText;
		},
		// 计算属性：判断是否包含 GXDLZT
		hasGXDLZT() {
			if (!this.searchForm?.specificMonitorContentIds) {
				return false;
			}
			// 解码 URL 编码（如 %2C 转成逗号）
			const decodedIds = decodeURIComponent(this.searchForm.specificMonitorContentIds);
			// 检查是否包含 GXDLZT
			return decodedIds.includes("GXDLZT");
		},
		cmpUpStart() {
			if (this.resultUpData?.warnStatus?.warnState?.vflash) {
				return this.resultUpData.warnStatus.warnState.vflash !== '关闭'
			} else {
				return false
			}
		},
		cmpDownStart() {
			if (this.resultDownData?.warnStatus?.warnState?.vflash) {
				return this.resultDownData.warnStatus.warnState.vflash !== '关闭'
			} else {
				return false
			}
		},
	},
	watch: {
		// 监听tree节点点击返回的传感器数据
		'sensorData': {
			handler() {
				this.searchDisable = false
				this.chartFlag1 = false
				this.chartFlag2 = false
				this.chartFlag3 = false
				this.stopFlag = false
				this.noDataForChart3 = false
				this.searchForm.sensorId = ''
				this.searchForm.specificMonitorContentIds = []

				// "设备安装代码"option
				this.sensorOptions = this.sensorData
				if (this.sensorData[0]) {
					// "设备安装代码"默认第一个
					this.searchForm.sensorId = this.sensorData[0].sensorId;
				}
				setTimeout(async () => {
					if (this.activeName == '3') {
						await this.$refs.chart3.stopDrawRealTimePushData()
						await this.$refs.chart3.disConnectRealTime()
					}
					this.handleDraw()
				}, 300)
			},
			deep: true,
		},
		// 监听"设备安装代码"，改变"选择Y轴值类型和顺序"的option
		'searchForm.sensorId'(val) {
			if (!val) return;
			this.specificMonitorTypeList = []
			this.searchForm.specificMonitorContentIds = []
			this.sensorOptions?.forEach((item) => {
				if (item.sensorId === val) {
					const {
						specificMonitorTypeName,
						specificMonitorTypeId
					} = item;
					this.specificMonitorTypeList = specificMonitorTypeId.map((id, index) => ({
						specificMonitorTypeName: specificMonitorTypeName[index],
						specificMonitorTypeId: id,
					}))
				}
			})
		},
		theme() {
			this.handleDraw()
		},
		async activeName(e) {
			// chartFlag 为true时，说明已经绘制过了，再次点击tab时，不重新绘制
			if (!this['chartFlag' + e]) {
				this.handleDraw()
			}
			if (e != '3') {
				if (!this.chartFlag3 || this.noDataForChart3) return;
				await this.$refs.chart3.stopDrawRealTimePushData()
				this.stopFlag = false
				this.realtimeLoading = false;
			} else {
				if (!this.chartFlag3 || this.noDataForChart3) return;
				await this.$refs.chart3.connectRealTime()
				await this.$refs.chart3.startSendData()
				this.stopFlag = true
			}
		}
	},
	beforeDestroy() {
		window.removeEventListener('resize', this.setMainTabsHeight);
		const chartsRef = this.$refs.chartsRef;
		if (chartsRef) {
			const resizeObserver = new ResizeObserver(() => { });
			resizeObserver.unobserve(chartsRef);
		}
	},
}
</script>

<style lang="scss" scoped>
@import "@/assets/styles/utils.scss";

// 默认主题-浅色
.charts {
	width: 100%;
	height: 100%;
	transition: all 0.3s;

	// 搜索框
	.search-box {
		width: 100%;
		min-height: vwpx(120px);
		background: inherit;
		color: inherit;
		font-size: vwpx(30px);
		border-radius: vwpx(24px);
		margin-bottom: vwpx(20px);
		padding: 0 vwpx(40px);
		display: flex;
		align-items: center;
		flex-wrap: wrap;
		background: #EFF5FF;
		transition: all 0.3s;

		span {
			margin-right: vwpx(40px);
		}

		// 数据采样
		.select-box {
			display: flex;
			justify-content: center;
			align-items: center;
			height: vwpx(70px);
			border: 1px solid #DCDFE6;
			background-color: #fff;
			border-radius: vwpx(12px);

			.select-item-active {
				background: #1890ff;
			}

			.select-item-disabled {
				color: #ccc;
				cursor: not-allowed;
			}

			div:first-child {
				border-left: 0 !important;
				border-radius: vwpx(12px) 0 0 vwpx(12px);
			}

			div:last-child {
				border-radius: 0 vwpx(12px) vwpx(12px) 0;
			}


			div {
				width: vwpx(130px);
				height: vwpx(70px);
				display: flex;
				justify-content: center;
				align-items: center;
				cursor: pointer;
				border-left: 1px solid #DCDFE6;
			}
		}

		// 复选框
		.check-box {
			width: vwpx(220px);
			height: vwpx(70px);
			border: 1px solid #DCDFE6;
			border-radius: vwpx(12px);
			display: flex;
			align-items: center;
			justify-content: center;
			background-color: #fff;
		}

		// 日期选择器样式调整
		::v-deep {

			// 输入框
			.el-range-editor.el-input__inner {
				width: vwpx(800px) !important;
				height: vwpx(70px);
				padding: vwpx(6px) vwpx(20px);
			}

			// 输入框内文本的样式
			.el-range-input {
				font-size: vwpx(30px);
			}

			// 左右的图标
			.el-range__icon {
				font-size: vwpx(26px);
				height: 100%;
				width: vwpx(40px);
				margin: 0;
			}

			.el-icon-circle-close:before {
				font-size: vwpx(26px);
				line-height: vwpx(61px);
				margin-left: vwpx(10px);
			}

			// 文字颜色
			.el-range-input {
				color: #000;
			}
		}

		// 下拉选择器样式调整
		::v-deep {

			// 输入框
			.el-input__inner {
				width: vwpx(400px);
				height: vwpx(70px);
				font-size: vwpx(30px);
				padding-right: vwpx(70px);
				padding-left: vwpx(30px);
			}

			// 下拉框
			.el-input__suffix {
				right: vwpx(5px);
			}

			// 下拉框的箭头
			.el-select .el-input .el-select__caret {
				font-size: vwpx(30px);
				line-height: vwpx(70px);
			}
		}

		// 按钮样式调整, 复选框样式调整
		::v-deep {

			// 按钮
			.el-button {
				width: vwpx(200px);
				height: vwpx(68px);
				border-radius: vwpx(12px);
				font-size: vwpx(30px);
				padding: vwpx(14px) vwpx(20px);
			}

			// 复选框
			.el-checkbox__inner {
				width: vwpx(30px);
				height: vwpx(30px);
			}

			.el-checkbox {

				// 第一个span
				span:first-child {
					height: vwpx(30px) !important;
				}
			}

			// 自适应"勾勾"样式
			.el-checkbox__input.is-checked .el-checkbox__inner::after {
				width: vwpx(6px);
				height: vwpx(14px);
				top: vwpx(2px);
				left: vwpx(9px);
				border: solid white;
				border-width: 0 vwpx(3px) vwpx(3px) 0;
				transform: rotate(45deg);
			}

			.el-checkbox__label {
				font-size: vwpx(30px);
				padding-left: vwpx(12px);
			}

			.el-checkbox {
				height: vwpx(70px);
				display: flex;
				justify-content: center;
				align-items: center;
			}
		}
	}

	.main-tabs {
		width: 100%;

		.main-tabs-charts,
		.main-tabs-text {
			width: 100%;
			height: calc(100% - #{vwpx(80px)});
			border: 1px solid #dcdfe6;
			border-top: none !important;
			padding: vwpx(10px);
		}

		.main-tabs-text {
			overflow-y: auto;
			font-size: vwpx(33px);
			color: #606266;
			font-weight: 700;
		}

		.centerClass {
			display: flex;
			justify-content: center;
			align-items: center;
		}

		.empty-msg {
			width: 100%;
			height: 100%;
			display: flex;
			justify-content: center;
			align-items: center;
			font-size: vwpx(33px);
			color: #606266;
			font-weight: 700;
		}

		// tabs样式调整
		::v-deep {
			.el-tabs--border-card {
				box-shadow: none !important;
				border-bottom: none !important;
			}

			.el-tabs__item {
				width: vwpx(270px);
				height: vwpx(80px);
				padding: 0 !important;
				display: flex;
				justify-content: center;
				align-items: center;
				font-size: vwpx(30px);
				color: #303133;
				transition: 0.3s;
				flex-shrink: 0;
			}

			.el-tabs__item.is-active {
				color: #1890ff !important;
			}

			.el-tabs__nav {
				display: flex;
				flex-wrap: nowrap;
			}

			.el-tabs__nav-wrap::after {
				display: none;
			}

			.el-tabs__active-bar {
				display: none;
			}

			// 这个继承 main-tabs 的高度
			.el-tabs__content {
				height: 0 !important;
				padding: 0;
			}

			.el-tabs__header {
				background: #EFF5FF;
			}

		}

		.charts {
			width: 100%;
			height: 100%;
		}
	}
}

::-webkit-scrollbar {
	width: vwpx(14px);
	height: vwpx(14px);
}

// 深色主题
.dark {
	::-webkit-scrollbar-thumb {
		background-color: rgba(1, 102, 254, 0.4);
	}

	::-webkit-scrollbar-track {
		background-color: rgba(0, 35, 94, .6);
	}

	.search-box {
		background: rgba(1, 102, 254, 0.3) !important;
		border: 0;
	}

	.select-box {
		border: 1px solid #0166FE !important;
		background-color: rgba(0, 25, 64, 0.5) !important;

		.select-item-active {
			background: #0166FE !important;
		}

		div {
			border-left: 1px solid #0166FE !important;
			color: #fff;
		}
	}

	.check-box {
		border: 1px solid #0166fe !important;
		background-color: rgba(0, 25, 64, 0.5) !important;
	}

	.main-tabs-charts,
	.main-tabs-text {
		border: 1px solid #0166FE !important;
		border-top: none !important;
	}

	::v-deep {
		.el-checkbox__inner {
			border: 1px solid #0687FF;
			background-color: rgba(0, 25, 64, 0.5);
		}

		.el-checkbox__input.is-checked .el-checkbox__inner {
			background-color: #1890ff !important;
		}

		.el-checkbox__label {
			color: #fff;
		}

		.el-checkbox__input.is-checked+.el-checkbox__label {
			color: #1890ff
		}

		.el-input__inner {
			background-color: rgba(0, 25, 64, 0.5);
			border: 1px solid #0166fe;
			color: #ffffff;
		}

		.el-tabs__item {
			color: #fff !important;
		}

		.el-tabs__item.is-active {
			color: #0166FE !important;
			background: rgba(0, 35, 94, 0.6);
			border-right-color: transparent;
			border-left-color: transparent;
			box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.3);
		}

		.el-tabs--border-card {
			background: rgba(1, 102, 254, 0.15) !important;
			border: 1px solid #0166fe;
			border-bottom: none !important;
		}

		.el-tabs__header {
			background: rgba(1, 102, 254, 0.15) !important;
			border: none !important;
		}

		.el-range-input {
			background: transparent !important;
			color: #fff !important;
		}
	}
}

.topBottom {
	position: absolute;
	top: 95%;
	left: 10px;
	z-index: 999;
}

.switch-container ::v-deep .el-switch__label {
	color: white !important;
}

/* 闪烁动画效果 */
@keyframes blink {

	/* 开始状态：红色背景，白色文字，完全不透明 */
	0% {
		opacity: 1;
		background-color: #ffc107;
		color: white;
	}

	/* 中间状态：半透明 */
	50% {
		opacity: 0.5;
		background-color: #ff0000;
		color: white;
	}

	/* 结束状态：恢复完全不透明 */
	100% {
		opacity: 1;
		background-color: #ff0000;
		color: white;
	}
}

.blinking {
	animation: blink 1s linear infinite;
}
</style>