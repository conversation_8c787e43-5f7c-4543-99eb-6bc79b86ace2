<template>
  <div class="regular-inspection">
    <div class="chart-pie">
      <Echarts :option="option" v-if="option" height="100%" key="regularKey" />
    </div>
    <div class="chart-legend">
      <div v-for="(item, index) in data" :key="index" class="legend-list"
        :class="index < data.length - 1 ? 'border-b' : ''">
        <span>{{ item.name }}</span>
        <span>{{ item.value }}</span>
        <span>{{ item.unit }}</span>
      </div>
    </div>
  </div>
</template>

<script>
import * as echarts from "echarts";
import Echarts from '../../echarts/echarts.vue';

export default {
  name: 'regularInspection',
  components: {
    Echarts,
  },
  props: {
    progress: {
      type: [String, Number],
      default: () => 0,
    },
    color: {
      type: String,
      default: () => '#38a700',
    },
    data: {
      type: Array,
      default: () => [
        {
          name: '累计检查次数',
          value: 1627,
          unit: '次'
        },
        {
          name: '待检查桥梁',
          value: 1627,
          unit: '座'
        },
        {
          name: '已检查桥梁',
          value: 230,
          unit: '座'
        },
      ],
    }
  },
  data() {
    return {
      option: null,
    }
  },
  mounted() {
    this.initChart();
  },
  methods: {
    initChart() {
      let center = ['50%', '50%'];
      this.option = {
        backgroundColor: 'rgba(0,0,0,0)',
        title: [{
          text: '已检占比',
          left: '35%',
          top: '55%',
          textStyle: {
            color: '#ffffff',
            fontSize: 26,
            fontWeight: '400',
          }
        }, {
          text: this.progress + '%',
          left: '35%',
          top: '40%',
          textStyle: {
            fontSize: 44,
            color: '#ffffff',
            fontFamily: 'Lato',
            fontWeight: '700',
          },
        }],
        polar: {
          radius: ['78%', '86%'],
          center,
        },
        angleAxis: {
          max: 100,
          show: false,
        },
        radiusAxis: {
          type: 'category',
          show: true,
          axisLabel: {
            show: false,
          },
          axisLine: {
            show: false,

          },
          axisTick: {
            show: false
          },
        },
        series: [
          {
            type: "pie",
            zlevel: 0,
            silent: true,
            radius: ["92%", "96%"],
            center,
            hoverAnimation: false,
            color: "rgba(0,62,122,1)",
            label: {
              normal: {
                show: false,
              },
            },
            labelLine: {
              normal: {
                show: false,
              },
            },
            data: [1],
          },
          {
            name: '',
            type: 'bar',
            roundCap: true,
            barWidth: 20,
            showBackground: true,
            backgroundStyle: {
              color: 'rgba(66, 66, 66, .3)',
            },
            data: [this.progress],
            coordinateSystem: 'polar',
            itemStyle: {
              normal: {
                color: new echarts.graphic.LinearGradient(0, 1, 0, 0, [{
                  offset: 0,
                  color: this.color,
                }, {
                  offset: 1,
                  color: this.color,
                }]),
              }
            }
          },
        ]
      };
    }
  },
}
</script>


<style lang="scss" scoped>
.regular-inspection {
  width: 100%;
  height: 100%;
  padding: 20px 10px;
  display: flex;
  align-items: center;

  .chart-pie {
    flex: 1;
    width: 100%;
    height: 100%;
  }

  .chart-legend {
    flex: 1;
    margin-right: 40px;
    margin-left: 20px;

    .legend-list {
      display: flex;
      align-items: center;
      height: 100px;
      line-height: 100px;

      span:first-child {
        font-family: Microsoft YaHei UI, Microsoft YaHei UI;
        font-weight: 400;
        font-size: 28px;
        color: #FFFFFF;
        margin-left: 20px;
      }

      span:nth-child(2) {
        font-family: Microsoft YaHei UI, Microsoft YaHei UI;
        font-weight: 700;
        font-size: 36px;
        color: #409DFF;
        margin: 0 20px;
      }

      span:last-child {
        font-family: Microsoft YaHei UI, Microsoft YaHei UI;
        font-weight: 400;
        font-size: 24px;
        color: #B6B6B6;
      }
    }

    .border-b {
      border-bottom: 2px dotted rgba(156, 189, 255, 0.5);
    }
  }
}
</style>