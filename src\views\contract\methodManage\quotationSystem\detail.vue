<template>
  <div class="app-container maindiv">
    <el-row>
      <!--角色数据-->
      <el-col :xs="24">
        <!--展开图标-->
        <el-row>
          <el-col :span="24" :xs="24">
            <el-row>
              <el-form
                  ref="queryForm"
                  :model="queryParams"
                  size="mini"
                  :inline="true"
                  label-width="68px"
              >
                <el-form-item label="" prop="schemeCode">
                  <el-input
                      v-model="queryParams.schemeCode"
                      placeholder="请输入子目号编码"
                      clearable
                      style="width: 240px"
                  >
                  </el-input>
                </el-form-item>
                <el-form-item label="" prop="schemeName">
                  <el-input
                      v-model="queryParams.schemeName"
                      placeholder="请输入子目名称"
                      clearable
                      style="width: 240px"
                  >
                  </el-input>
                </el-form-item>
                <el-form-item label="" prop="safetyFeeFlag">
                  <el-select
                      v-model="queryParams.safetyFeeFlag"
                      placeholder="是否计算安全费"
                      clearable
                      style="width: 240px"
                  >
                    <el-option
                        v-for="(item, index) in schemeCode05Options"
                        :key="index"
                        :label="item.label"
                        :value="item.value"
                        :disabled="item.disabled"
                    ></el-option>
                  </el-select>
                </el-form-item>
                <el-form-item label="" prop="rateFlag">
                  <el-select
                      v-model="queryParams.rateFlag"
                      placeholder="是否为费率章节"
                      clearable
                      style="width: 240px"
                  >
                    <el-option
                        v-for="(item, index) in schemeCode05Options"
                        :key="index"
                        :label="item.label"
                        :value="item.value"
                    ></el-option>
                  </el-select>
                </el-form-item>
                <el-form-item label="" prop="nodeType">
                  <el-select
                      v-model="queryParams.nodeType"
                      placeholder="请选择节点类型"
                      clearable
                      style="width: 240px"
                  >
                    <el-option
                        v-for="(item, index) in typeOptions"
                        :key="index"
                        :label="item.label"
                        :value="item.value"
                    ></el-option>
                  </el-select>
                </el-form-item>
                <el-form-item>
                  <el-button
                      type="primary"
                      icon="el-icon-search"
                      size="mini"
                      @click="handleQuery"
                  >搜索
                  </el-button
                  >
                  <el-button
                      icon="el-icon-refresh"
                      size="mini"
                      @click="resetQuery"
                  >重置
                  </el-button
                  >
                </el-form-item>
              </el-form>
            </el-row>
          </el-col>
        </el-row>
        <!--操作按钮区开始-->
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button
                type="success"
                icon="el-icon-upload2"
                size="mini"
                v-has-menu-permi="['contract:libSchPriceTree:import']"
                @click="handleImport"
            >导入
            </el-button
            >
          </el-col>
          <el-col :span="1.5">
            <el-button
                plain
                type="success"
                icon="el-icon-upload2"
                size="mini"
                v-has-menu-permi="['contract:libschpricetree:export']"
                @click="handleExport"
            >导出
            </el-button
            >
          </el-col>
          <el-col :span="1.5">
            <el-button
                type="primary"
                icon="el-icon-plus"
                size="mini"
                v-has-menu-permi="['contract:libSchPriceTree:add']"
                @click="handleAddDetail"
            >新增
            </el-button
            >
          </el-col>
          <el-col :span="1.5">
            <el-button
                type="warning"
                icon="el-icon-edit"
                size="mini"
                @click="modifyMode = !modifyMode"
            >{{ modifyMode ? '关闭修改模式' : '修改模式' }}
            </el-button
            >
          </el-col>
          <right-toolbar
              :showSearch.sync="showSearch"
              @queryTable="handleQuery"
              :columns="columns"
          ></right-toolbar>
        </el-row>
        <div>
          <div class="draggable">
            <div class="custom-tree-title">
              <span class="column">子目号</span>
              <span class="column">子目名称</span>
              <span class="column">单位</span>
              <span class="column">单价</span>
              <span class="column">小数位数</span>
              <span class="column">备注</span>
              <span class="column" v-if="modifyMode">操作</span>
            </div>
            <el-tree
                :data="tableData"
                node-key="id"
                :draggable="modifyMode"
                highlight-current
                check-on-click-node
                v-loading="loading"
                @node-drop="onNodeDrop"
                @node-click="handleRowClick"
                ref="dataTree"
                :expand-on-click-node="false">
              <span class="custom-tree-node" slot-scope="{ node, data }">
                <span class="column">{{ data.schemeCode }}</span>
                <span class="column">
                  <el-tooltip className="item" effect="dark" :content="data.schemeName" placement="top-start">
                    <div style="overflow: hidden;white-space:nowrap;text-overflow:ellipsis">{{ data.schemeName }}</div>
                  </el-tooltip>
                </span>
                <span class="column">{{ data.unit }}</span>
                <span class="column">{{ data.price }}</span>
                <span class="column">{{ data.decimalPlaces }}</span>
                <span class="column">
                   <el-tooltip :content="data.remark" placement="top">
                     <span class="column ellipsis">{{ data.remark }}</span>
                   </el-tooltip>
                </span>
                <span class="column" v-if="modifyMode">
                  <el-button
                      size="mini"
                      type="text"
                      icon="el-icon-edit"
                      v-if="modifyMode"
                      v-has-menu-permi="['contract:libSchPriceTree:edit']"
                      @click.stop="handleUpdate(data)"
                  >修改
                  </el-button
                  >
                  <el-button
                      size="mini"
                      type="text"
                      icon="el-icon-delete"
                      v-if="modifyMode"
                      v-has-menu-permi="['contract:libSchPriceTree:delete']"
                      @click="handleDelete(data)"
                  >删除
                  </el-button
                  >
                </span>
              </span>
            </el-tree>
          </div>
        </div>
      </el-col>
    </el-row>
    <Dialog :title="quotationTitle" :show.sync="quotationFlag" width="800px">
      <div class="road-interflow-edit" v-loading="loading">
        <el-row :gutter="15">
          <el-form
              ref="quotationRef"
              :model="quotationData"
              :rules="quotationRules"
              size="medium"
              label-width="100px"
          >
            <el-form-item label="名称" prop="name">
              <el-input
                  v-model="quotationData.name"
                  placeholder="请输入名称"
                  clearable
                  :style="{ width: '100%' }"
              ></el-input>
            </el-form-item>
            <el-form-item label="描述" prop="description">
              <el-input
                  v-model="quotationData.description"
                  placeholder="请输入描述"
                  clearable
                  :style="{ width: '100%' }"
              >
              </el-input>
            </el-form-item>
            <el-form-item label="备注" prop="memo">
              <el-input
                  v-model="quotationData.memo"
                  placeholder="请输入备注"
                  clearable
                  :style="{ width: '100%' }"
              ></el-input>
            </el-form-item>
          </el-form>
        </el-row>
        <div style="text-align: right; margin-top: 20px">
          <el-button type="primary" size="mini" @click="onSearch"
          >保存
          </el-button
          >
          <el-button size="mini" @click="quotationFlag = false">退出</el-button>
        </div>
      </div>
    </Dialog>
    <Dialog :title="detailTitle" :show.sync="openDetail" width="1000px">
      <AddDetail @close="handleCloseDetail" :check="check" :formdata="check" :checkPNode="checkPNode"
                 :editType="editType" :lib="lib"></AddDetail>
    </Dialog>
    <el-dialog :title="upload.title" :visible.sync="upload.open" width="400px" append-to-body>
      <el-upload
          ref="upload"
          :limit="1"
          accept=".xlsx, .xls"
          :headers="upload.headers"
          :action="upload.url + '?libId=' +lib.id + '&updateSupport=' + upload.updateSupport"
          :disabled="upload.isUploading"
          :on-progress="handleFileUploadProgress"
          :on-success="handleFileSuccess"
          :auto-upload="false"
          drag
      >
        <i class="el-icon-upload"></i>
        <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
        <div class="el-upload__tip text-center" slot="tip">
          <div class="el-upload__tip" slot="tip">
            <el-checkbox v-model="upload.updateSupport"/>
            是否更新已经存在的用户数据
          </div>
          <span>仅允许导入xls、xlsx格式文件。</span>
          <el-link type="primary" :underline="false" style="font-size:12px;vertical-align: baseline;"
                   @click="importTemplate">下载模板
          </el-link>
        </div>
      </el-upload>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitFileForm">确 定</el-button>
        <el-button @click="upload.open = false">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import Sortable from 'sortablejs'
import Dialog from '@/components/Dialog/index.vue'
import AddDetail from './addDetail.vue'
import {getTreeData, delLibschpricetree, updateLibschpricetree} from "@/api/contract/quotationSystem.js";
import {getToken} from "@/utils/auth";

export default {
  components: {Dialog, AddDetail},
  props: {
    lib: {
      type: Object,
      default: () => {
      }
    }
  },
  data() {
    return {
      loading: false,
      showSearch: false,
      // 修改模式
      modifyMode: false,
      // 当前选中行
      check: {},
      // 当前选中行的顶层
      checkPNode: '',
      // 报价体系弹窗标志
      quotationFlag: false,
      quotationTitle: '新增报价体系',
      quotationData: {
        name: undefined,
        description: undefined,
        memo: undefined
      },
      editType: 'add',
      // 详情弹窗打开标志
      openDetail: false,
      detailTitle: '新增',
      defaultProps: {
        children: 'children',
        label: 'label'
      },
      quotationRules: {
        name: [
          {
            required: true,
            message: '请输入名称',
            trigger: 'blur'
          }
        ]
      },
      upload: {
        // 是否显示弹出层（用户导入）
        open: false,
        // 弹出层标题（用户导入）
        title: "",
        // 是否禁用上传
        isUploading: false,
        // 是否更新已经存在的用户数据
        updateSupport: 0,
        // 设置上传的请求头部
        headers: {Authorization: "Bearer " + getToken()},
        // 上传的地址
        url: process.env.VUE_APP_BASE_API + "/manager/libschpricetree/import"
      },
      // 查询参数
      queryParams: {
      },
      schemeCode05Options: [
        {
          "label": "是",
          "value": "1"
        },
        {
          "label": "否",
          "value": "2"
        }
      ],
      typeOptions: [
        {
          label: "方法类型",
          value: 1,
        },
        {
          label: "方法",
          value: 2,
        },
      ],
      columns: [
        {key: 0, label: `子目号`, visible: true},
        {key: 1, label: `子目名称`, visible: true},
        {key: 2, label: `单位`, visible: true},
        {key: 3, label: `单价`, visible: true},
        {key: 4, label: `小数位数`, visible: true},
        {key: 5, label: `备注`, visible: true}

      ],
      // 查询结果
      tableData: [
        {
          id: '1',
          parentId: '0',
          schemeCode: '日常养护',
          children: []
        }
      ],
      //树形结构数据转的数组
      tableDataArray: []
    }
  },
  computed: {},
  watch: {
    modifyMode: function (val, oldVal) {
      const columns = document.getElementsByClassName('column')
      for (const columnsKey in columns) {
        if (val) {
          columns[columnsKey].style.width = '9vw'
        } else {
          columns[columnsKey].style.width = '10.5vw'
        }
      }
    },
    immediate: true
  },
  created() {
  },
  mounted() {
    //行拖拽
    this.handleQuery()
  },
  methods: {
    // 筛选节点
    filterNode(value, data) {
      if (!value) return true
      return data.label.indexOf(value) !== -1
    },
    // 选中表格行
    handleRowClick(e) {
      this.checkPNode = e.schemeType
      if (this.check.id && e.id === this.check.id) {
        this.check = {}
        this.checkPNode = ''
      } else {
        this.check = e
      }
    },
    // 表单查询
    handleQuery() {
      this.$refs['queryForm'].validate((valid) => {
        if (!valid) return
        this.loading = true
        this.queryParams.libId = this.lib.id
        this.queryParams.isConstruction =  '否'
        this.queryParams.domain = ''
        if (this.queryParams.rateFlag) this.queryParams.rateFlag = Number(this.queryParams.rateFlag)
        getTreeData(this.queryParams).then(res => {
          this.tableData = res.rows
          this.loading = false
        })
      })
    },
    // 表单重置
    resetQuery() {
      this.$refs['queryForm'].resetFields()
      this.handleQuery()
    },
    // 新增
    handleAddDetail() {
      if (!this.check.id) {
        this.$modal.msgError("请先选择一条记录！");
        return
      }
      if (this.check.nodeType == 2) {
        return;
      }
      this.editType = 'add'
      this.detailTitle = '新增'
      this.openDetail = true
    },
    handleUpdate(e) {
      this.editType = 'update'
      this.check = e
      this.checkPNode = e.schemeType
      this.detailTitle = '修改'
      this.openDetail = true
    },

    // 关闭详情弹窗
    handleCloseDetail() {
      this.openDetail = false
      this.handleQuery()
    },
    handleDelete(e) {
      let that = this
      this.$modal.confirm('是否删除').then(function () {
        delLibschpricetree(e.id).then(() => {
          that.handleQuery()
        })
      })
    },
    /** 导出按钮操作 */
    handleExport() {
      this.queryParams.libName = '报价方法导出清单'
      this.download('manager/libschpricetree/export', {
        ...this.queryParams
      }, `libschpricetree_${new Date().getTime()}.xlsx`,
        {
          headers: { 'Content-Type': 'application/json;' },
          parameterType: 'body'
        })

    },
    /** 导入按钮操作 */
    handleImport() {
      this.upload.title = "用户导入";
      this.upload.open = true;
    },
    /** 下载模板操作 */
    importTemplate() {
      this.download('manager/libschpricetree/importTemplate', {}, `导入报价方法.xlsx`)
    },
    // 文件上传中处理
    handleFileUploadProgress(event, file, fileList) {
      this.upload.isUploading = true;
    },
    // 文件上传成功处理
    handleFileSuccess(response, file, fileList) {
      this.upload.open = false;
      this.upload.isUploading = false;
      this.$refs.upload.clearFiles();
      this.$alert("<div style='overflow: auto;overflow-x: hidden;max-height: 70vh;padding: 10px 20px 0;'>" + response.msg + "</div>", "导入结果", {dangerouslyUseHTMLString: true});
      this.handleQuery();
      this.$refs.dataTree.doLayout()
    },
    // 提交上传文件
    submitFileForm() {
      this.$refs.upload.submit();
    },
    // 更新节点的位置并同步接口更新
    onNodeDrop(before, after) {
      this.loading = true
      before.data.parentId = after.data.parentId
      updateLibschpricetree(before.data).then(res => {
        this.loading = false
      })
    },
  }
}
</script>

<style lang="scss" scoped>
.leftDiv {
  border-right: 1px solid #d8dce5;
  min-height: calc(100vh - 100px);
  overflow-y: auto;
  height: calc(80vh - 150px);
  position: relative;
  top: -20px;
  padding-top: 10px;
  background-color: white;
}

.leftIcon {
  border: 1px solid #dcdfe6;
  border-radius: 8px;
  width: 16px;
  height: 50px;
  line-height: 50px;
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
  position: absolute;
  right: 0;
  top: 300px;
  z-index: 2;
}

.leftIcon:hover {
  background-color: #dcdfe6;
}

.rightIcon {
  border: 1px solid #dcdfe6;
  border-radius: 8px;
  width: 16px;
  height: 50px;
  line-height: 50px;
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
  position: absolute;
  left: -10px;
  top: 280px;
  z-index: 10;
  background: white;
}

.rightIcon:hover {
  background-color: #dcdfe6;
}

.treeIcon {
  float: right;
  padding-left: 30px;

  a {
    padding-right: 5px;
  }
}

.left-total {
  font-size: 13px;
  line-height: 28px;
  color: #606266;
  position: absolute;
  bottom: 10px;
  right: 10px;
}

.draggable {
  width: 100%;
  height: 600px;
  overflow-y: auto;
}
::v-deep .el-tree-node__content {
  height: 35px !important;
  padding-left: 0px !important;
  border-bottom: 1px solid #dfe6ec;
}

.custom-tree-title {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: left;
  font-size: 14px;
  height: 35px;
  font-weight: bold;
  background-color: #f2f3f5;
  padding-left: 24px;
}

.custom-tree-node {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: left;
  font-size: 14px;
  height: 35px;
  padding-right: 8px;
}

.column {
  width: 10.5vw;
  height: 35px;
  line-height: 35px;
  padding: 0;
  color: #333333;
  text-align: center;
  border-right: 1px solid #dfe6ec;
}
.ellipsis {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  display: inline-block;
}
</style>
<style lang="scss" scoped>
@import "@/assets/styles/business.scss";
</style>
