<template>
  <PageContainer>
    <template slot="search">
      <div style="display: flex; align-items: center; margin: 0">
        <CascadeSelection
          style="min-width:192px;"
          :form-data="queryParams"
          v-model="queryParams"
          types="201"
          multiple
        />
        <el-select
          style="margin: 0 20px"
          v-model="queryParams.operationState"
          placeholder="运营状态"
          clearable
        >
          <el-option
            v-for="dict in dict.type.sys_operation_state"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
        <el-date-picker
          v-model="queryParams.years"
          type="year"
          value-format="yyyy"
          placeholder="年份"
          style="margin-right: 20px;"
        />
        <el-button
          v-hasPermi="['baseData:bridgeStatistics:getStatistics']"
          type="primary"
          icon="el-icon-search"
          @click="handleQuery"
        >搜索</el-button>
        <el-button
            icon="el-icon-refresh"
            @click="resetQuery"
          >重置</el-button>
      </div>
    </template>
    <template slot="header">
      <el-button
        v-hasPermi="['baseData:bridgeStatistics:export']"
        style="margin-bottom: 10px;"
        type="primary"
        @click="exportList"
      >导出报表</el-button>
    </template>
    <template slot="body">
      <!-- <div class="body">
      </div> -->
      <el-table
        v-adjust-table
        ref="table"
        v-loading="loading"
        height="100%"
        border
        :span-method="objectSpanMethod"
        :data="tableData"
        :header-cell-style="{'height': '36px'}"
        :row-class-name="tableRowClassName"
      >
        <el-table-column
          fixed
          label="序号"
          type="index"
          width="50"
          align="center"
        />
        <el-table-column
          fixed="left"
          label="单位名称"
          align="center"
          prop="managementMaintenanceName"
          min-width="120"
          show-overflow-tooltip
        />
        <el-table-column
          fixed="left"
          label="路段名称"
          align="center"
          prop="maintenanceSectionName"
          min-width="120"
          show-overflow-tooltip
        />
        <el-table-column
          label="跨径分类"
          align="center"
        >
          <el-table-column
            label="特大桥"
            align="center"
            prop="superMajorBridge"
            min-width="120"
          >
            <template slot-scope="{ row }">
              <el-link
                v-hasPermi="['baseData:bridge:selectPage']"
                type="primary"
                :underline="false"
                @click="handleSpanClassify({...row, clickName: '特大桥'}, ['1'], row.superMajorBridge)"
              > {{row.superMajorBridge}} </el-link>
            </template>
          </el-table-column>
          <el-table-column
            label="大桥"
            align="center"
            prop="greatBridge"
            min-width="120"
          >
            <template slot-scope="{ row }">
              <el-link
                v-hasPermi="['baseData:bridge:selectPage']"
                type="primary"
                :underline="false"
                @click="handleSpanClassify({...row, clickName: '大桥'}, ['2'], row.greatBridge)"
              > {{row.greatBridge}} </el-link>
            </template>
          </el-table-column>
          <el-table-column
            label="中桥"
            align="center"
            prop="mediumBridge"
            min-width="120"
          >
            <template slot-scope="{ row }">
              <el-link
                v-hasPermi="['baseData:bridge:selectPage']"
                type="primary"
                :underline="false"
                @click="handleSpanClassify({...row, clickName: '中桥'}, ['3'], row.mediumBridge)"
              > {{row.mediumBridge}} </el-link>
            </template>
          </el-table-column>
          <el-table-column
            label="小桥"
            align="center"
            prop="smallBridge"
            min-width="120"
          >
            <template slot-scope="{ row }">
              <el-link
                v-hasPermi="['baseData:bridge:selectPage']"
                type="primary"
                :underline="false"
                @click="handleSpanClassify({...row, clickName: '小桥'}, ['4'], row.smallBridge)"
              > {{row.smallBridge}} </el-link>
            </template>
          </el-table-column>
          <el-table-column
            label="总数"
            align="center"
            prop="bridgeTotalCount"
            min-width="120"
          >
            <template slot-scope="{ row }">
              <el-link
                v-hasPermi="['baseData:bridge:selectPage']"
                type="primary"
                :underline="false"
                @click="handleSpanClassify({...row, clickName: '跨径分类总数'}, ['1','2','3','4'], row.bridgeTotalCount)"
              > {{row.bridgeTotalCount}} </el-link>
            </template>
          </el-table-column>
        </el-table-column>
        <el-table-column
          label="桥梁技术评定等级"
          align="center"
        >
          <el-table-column
            label="一类"
            align="center"
            prop="oneType"
            min-width="120"
          >
            <template slot-scope="{ row }">
              <el-link
                v-hasPermi="['baseData:bridge:selectPage']"
                type="primary"
                :underline="false"
                @click="handleConditionLevel({...row, clickName: '一类'}, ['1'], row.oneType)"
              > {{row.oneType}} </el-link>
            </template>
          </el-table-column>
          <el-table-column
            label="二类"
            align="center"
            prop="twoTypes"
            min-width="120"
          >
            <template slot-scope="{ row }">
              <el-link
                v-hasPermi="['baseData:bridge:selectPage']"
                type="primary"
                :underline="false"
                @click="handleConditionLevel({...row, clickName: '二类'}, ['2'], row.twoTypes)"
              > {{row.twoTypes}} </el-link>
            </template>
          </el-table-column>
          <el-table-column
            label="三类"
            align="center"
            prop="threeTypes"
            min-width="120"
          >
            <template slot-scope="{ row }">
              <el-link
                v-hasPermi="['baseData:bridge:selectPage']"
                type="primary"
                :underline="false"
                @click="handleConditionLevel({...row, clickName: '三类'}, ['3'], row.threeTypes)"
              > {{row.threeTypes}} </el-link>
            </template>
          </el-table-column>
          <el-table-column
            label="四类"
            align="center"
            prop="fourTypes"
            min-width="120"
          >
            <template slot-scope="{ row }">
              <el-link
                v-hasPermi="['baseData:bridge:selectPage']"
                type="primary"
                :underline="false"
                @click="handleConditionLevel({...row, clickName: '四类'}, ['4'], row.fourTypes)"
              > {{row.fourTypes}} </el-link>
            </template>
          </el-table-column>
          <el-table-column
            label="五类"
            align="center"
            prop="fiveTypes"
            min-width="120"
          >
            <template slot-scope="{ row }">
              <el-link
                v-hasPermi="['baseData:bridge:selectPage']"
                type="primary"
                :underline="false"
                @click="handleConditionLevel({...row, clickName: '五类'}, ['5'], row.fiveTypes)"
              > {{row.fiveTypes}} </el-link>
            </template>
          </el-table-column>
          <el-table-column
            label="未评定"
            align="center"
            prop="notEvaluated"
            min-width="120"
          >
            <template slot-scope="{ row }">
              <el-link
                v-hasPermi="['baseData:bridge:selectPage']"
                type="primary"
                :underline="false"
                @click="handleConditionLevel({...row, clickName: '未评定'}, ['9'], row.notEvaluated)"
              > {{row.notEvaluated}} </el-link>
            </template>
          </el-table-column>
          <el-table-column
            label="评定总数"
            align="center"
            prop="evaluatedTotalCount"
            min-width="120"
          >
            <template slot-scope="{ row }">
              <el-link
                v-hasPermi="['baseData:bridge:selectPage']"
                type="primary"
                :underline="false"
                @click="handleConditionLevel({...row, clickName: '评定总数'}, ['1','2','3','4','5'], row.evaluatedTotalCount)"
              > {{row.evaluatedTotalCount}} </el-link>
            </template>
          </el-table-column>
        </el-table-column>
        <el-table-column
          label="特大桥长度(m)"
          align="center"
          prop="superMajorBridgeLength"
          min-width="120"
        />
        <el-table-column
          label="大桥长度(m)"
          align="center"
          prop="greatBridgeLength"
          min-width="120"
        />
        <el-table-column
          label="中桥长度(m)"
          align="center"
          prop="mediumBridgeLength"
          min-width="120"
        />
        <el-table-column
          label="小桥长度(m)"
          align="center"
          prop="smallBridgeLength"
          min-width="120"
        />
        <el-table-column
          label="总长度(m)"
          align="center"
          prop="bridgeTotalLength"
          min-width="120"
        />
      </el-table>
      <!-- <pagination
        :total="total"
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
        :pageSizes="[10, 20, 30, 50, 100, 1000]"
        @pagination="getList"
      /> -->
    </template>
    <DetailTable
      v-if="showTable"
      :showTable="showTable"
      :row="row"
      :params="params"
      @close="closeTable"
    />
  </PageContainer>
</template>

<script>
import CascadeSelection from '@/components/CascadeSelection/index.vue'
import { getStatistics } from '@/api/baseData/bridge/statistics/index'
import DetailTable from './components/DetailTable.vue'

export default {
  name: 'Statistics',
  props: {},
  dicts: ['sys_operation_state'],
  components: { CascadeSelection, DetailTable },
  data() {
    return {
      queryParams: {
        operationState: '2'
      },
      tableData: [],
      loading: false,
      cellList: [],
      count: null,
      showTable: false,
      row: {},
      params: {}
    }
  },
  created() {
    this.getList()
  },
  methods: {
    handleQuery() {
      this.getList()
    },
     // 重置按钮
     resetQuery() {
      this.queryParams = {
        operationState: "2"
      }
      this.handleQuery()
    },
    getList() {
      this.loading = true
      this.tableData = []
      this.cellList = []
      this.count = null
      getStatistics(this.queryParams)
        .then(res => {
          if (res) {
            this.tableData = res
            this.computeCell(this.tableData)
          }
        })
        .finally(() => {
          this.loading = false
        })
    },
    computeCell(table) {
      for (let index = 0; index < table.length; index++) {
        if (index == 0) {
          this.cellList.push(1)
          this.count = 0
        } else {
          if (
            table[index].managementMaintenanceName ==
            table[index - 1].managementMaintenanceName
          ) {
            this.cellList[this.count] += 1
            this.cellList.push(0)
          } else {
            this.cellList.push(1)
            this.count = index
          }
        }
      }
    },
    objectSpanMethod({ row, column, rowIndex, columnIndex }) {
      if (columnIndex === 1) {
        const rowCell = this.cellList[rowIndex]
        if (rowCell > 0) {
          const colCell = 1
          return {
            rowspan: rowCell,
            colspan: colCell
          }
        } else {
          return {
            rowspan: 0,
            colspan: 0
          }
        }
      }
    },
    exportList() {
      this.$modal
        .confirm('导出所有表格数据，是否继续？')
        .then(() => {
          this.download(
            '/baseData/bridge/statistics/export',
            this.queryParams,
            `桥梁统计_${new Date().getTime()}.xlsx`,
            {
              headers: { 'Content-Type': 'application/json;' },
              parameterType: 'body'
            }
          )
        })
        .catch(() => {})
    },
    handleSpanClassify(row, list, Num) {
      if (Num == 0) return
      this.showTable = true
      this.row = row
      this.params = { spanClassifyType: list, types: '1',...this.queryParams }
    },
    handleConditionLevel(row, list, Num) {
      if (Num == 0) return
      this.showTable = true
      this.row = row
      this.params = {
        bridgeTechAssessType: list,
        types: list[0] == '9' ? '2' : '1',
        ...this.queryParams
      }
    },
    closeTable() {
      this.showTable = false
      this.row = {}
    },
    tableRowClassName(row_params) {
      let { row } = row_params
      if (row.maintenanceSectionName == '总计') {
        return `tr-fixed fixed-row`
      } else {
        return ``
      }
    }
  },
  computed: {},
  watch: {}
}
</script>

<style lang="scss" scoped>
.body {
  height: calc(100vh - 230px);
  overflow-y: auto;
}
::v-deep .el-table .el-table__fixed-body-wrapper{
    top: 72px !important;
}
::v-deep .el-table {
  .tr-fixed {
    display: table-row;
    position: sticky;
    bottom: 0;
    width: 100%;
    td {
      border-top: 1px solid #f3f5fa;
      background: #fff;
    }
  }
  .fixed-row {
    bottom: 0;
  }
}
</style>