<template>
  <div class="app-container">
    <el-row :gutter="20">
      <!--筛选区开始-->
      <el-col :span="24" :xs="24">
        <el-row>
          <el-col :span="24">
            <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" label-width="68px">
              <el-form-item label="" prop="disType">
                <el-date-picker
                    style="width: 240px"
                    v-model="queryParams.year"
                    type="year"
                    value-format="yyyy"
                    placeholder="年份"
                >
                </el-date-picker>
              </el-form-item>
              <el-form-item>
                <selectTree
                    :key="'domainId'"
                    style="width: 240px"
                    v-model="queryParams.domainId"
                    :deptType="100" :deptTypeList="[1, 3, 4]"
                    placeholder="管养单位"
                    clearable
                    filterable
                />
              </el-form-item>
              <el-form-item>
                <selectTree
                    :key="'constructionUnit'"
                    style="width: 240px"
                    v-model="queryParams.conDomainId" :data-rule="false"
                    :dept-type="100"
                    placeholder="施工单位"
                    :filter-keys="['云南省交通投资建设集团有限公司', '云南交投投资有限公司']"
                    :expand-all="false"
                    clearable
                    filterable
                />
              </el-form-item>
              <el-form-item>
                <RoadSection style="width: 240px" v-model="queryParams.maiSecId" :deptId="queryParams.domainId" placeholder="路段"/>
              </el-form-item>
              <el-form-item>
                <el-input style="width: 240px" placeholder="项目名称" v-model="queryParams.projName"></el-input>
              </el-form-item>
              <el-form-item>
                <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
                <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
                <el-button v-show="!showSearch" @click="showSearch=true" icon="el-icon-arrow-down" circle></el-button>
                <el-button v-show="showSearch" @click="showSearch=false" icon="el-icon-arrow-up" circle></el-button>
              </el-form-item>
            </el-form>
            <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch"
                     label-width="68px">
              <el-form-item>
                <el-input style="width: 240px" placeholder="项目编码" v-model="queryParams.projCode"></el-input>
              </el-form-item>
              <el-form-item>
                <dict-select type="task_type" v-model="queryParams.type" placeholder="任务单类型"
                             style="width: 240px"></dict-select>
              </el-form-item>
              <el-form-item>
                <el-input style="width: 240px" placeholder="任务单名称" v-model="queryParams.name"></el-input>
              </el-form-item>
              <el-form-item>
                <el-input style="width: 240px" placeholder="任务单编码" v-model="queryParams.code"></el-input>
              </el-form-item>
              <el-form-item>
                <dict-select type="overdue_status" v-model="queryParams.overdueStatus" placeholder="超期提醒"
                             style="width: 240px"></dict-select>
              </el-form-item>
              <el-form-item>
                <el-date-picker
                    v-model="queryParams.issueDate"
                    style="width: 240px"
                    type="daterange"
                    value-format="yyyy-MM-dd"
                    range-separator="至"
                    start-placeholder="发出日期起"
                    end-placeholder="发出日期止">
                </el-date-picker>
              </el-form-item>
            </el-form>
          </el-col>
        </el-row>
        <!--筛选区结束-->

        <!--操作按钮区开始-->
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button
                type="success"
                icon="el-icon-download"
                size="mini"
                v-has-menu-permi="['projConstruction:construction:pendingexport']"
                @click="exportList"
            >导出清单
            </el-button>
          </el-col>
          <right-toolbar :columns="columns" :showSearch.sync="showSearch" @queryTable="handleQuery"></right-toolbar>
        </el-row>
        <!--操作按钮区结束-->
        <!--数据表格开始-->
        <div class="tableDiv">
          <el-table v-adjust-table
              size="mini"
              style="width: 100%"
              v-loading="loading"
              border
              :data="tableData"
              row-key="id"
              ref="dataTable"
              stripe
              highlight-current-row
              :height="
                showSearch ? 'calc(100vh - 330px)' : 'calc(100vh - 270px)'
              "
          >
              <el-table-column type="expand">
                  <template slot-scope="props">
                      <el-table v-adjust-table :data="props.row.constructionDetailList" style="width: 100%">
                          <el-table-column
                                  prop=""
                                  align="center"
                                  label="">
                          </el-table-column>
                          <el-table-column
                                  prop="schemeCode"
                                  align="center"
                                  label="子目号">
                          </el-table-column>
                          <el-table-column
                                  prop="schemeName"
                                  align="center"
                                  label="养护方法">
                          </el-table-column>
                          <el-table-column
                                  prop="calcDesc"
                                  align="center"
                                  label="计算式">
                          </el-table-column>
                          <el-table-column
                                  prop="num"
                                  align="center"
                                  label="方法数量">
                          </el-table-column>
                          <el-table-column
                                  prop="unit"
                                  align="center"
                                  label="方法单位">
                          </el-table-column>
                          <el-table-column
                                  prop="price"
                                  align="center"
                                  label="单价">
                          </el-table-column>
                          <el-table-column
                                  prop="amount"
                                  align="center"
                                  label="金额">
                          </el-table-column>
                          <el-table-column
                                  prop="remark"
                                  align="center"
                                  label="备注">
                          </el-table-column>
                      </el-table>
                  </template>
              </el-table-column>
            <el-table-column
                label="序号"
                align="center"
                type="index"
                width="50"
            />
            <template v-for="(column,index) in columns">
              <el-table-column :label="column.label"
                               v-if="column.visible" show-overflow-tooltip
                               align="center"
                               :prop="column.field"
                               :width="column.width">
                <template slot-scope="scope">
                  <dict-tag v-if="column.dict" :options="dict.type[column.dict]" :value="scope.row[column.field]"/>
                  <template v-else-if="column.slots">
                    <RenderDom :row="scope.row" :index="index" :render="column.render"/>
                  </template>
                  <span v-else-if="column.isTime">{{ parseTime(scope.row[column.field], '{y}-{m}-{d}') }}</span>
                  <span v-else>{{ scope.row[column.field] }}</span>
                </template>
              </el-table-column>
            </template>
            <el-table-column
                label="操作"
                fixed="right"
                align="center"
                width="150"
                class-name="small-padding fixed-width"
            >
              <template slot-scope="scope">
                <el-button
                    size="mini"
                    type="text"
                    icon="el-icon-edit"
                    v-has-menu-permi="['projConstruction:construction:process']"
                    @click="handleReg(scope.row)"
                >开工登记
                </el-button>
              </template>
            </el-table-column>
          </el-table>
          <pagination
              v-show="total>0"
              :total="total"
              :page.sync="queryParams.pageNum"
              :limit.sync="queryParams.pageSize"
              @pagination="handleQuery"
          />
        </div>
        <!--数据表格结束-->
      </el-col>
    </el-row>
    <el-dialog
        title="开工登记"
        v-if="dialogVisible"
        :visible.sync="dialogVisible"
        width="80%"
    >
      <el-descriptions size="mini" :column="3" border :labelStyle="{width: '150px'}" :contentStyle="{width: '300px'}">
        <el-descriptions-item v-for="(item, index) in projectColumns" :key="index" :label="item.name">
          <el-tooltip :content="projectData[item.field]" placement="top">
            <dict-tag v-if="item.dict" :options="dict.type[item.dict]" :value="projectData[item.field]"/>
            <span v-else>{{ projectData[item.field] }}</span>
          </el-tooltip>
        </el-descriptions-item>
      </el-descriptions>
      <el-form class="mt20" ref="elForm" :model="formData" label-width="130px" size="medium">
        <el-form-item label="附件">
          <file-upload v-model="projectData.fileId" :forView="true" />
        </el-form-item>
        <el-form-item label="开工时间" prop="startTime">
          <el-input v-model="formData.startTime" readonly/>
        </el-form-item>
        <el-form-item label="开工意见" prop="comment">
          <el-input type="textarea" :rows="4" v-model="formData.comment"/>
        </el-form-item>
        <el-form-item>
          <div style="text-align: right">
            <el-button type="primary" @click="handleReceive">登记</el-button>
            <el-button @click="dialogVisible = false">取消</el-button>
          </div>
        </el-form-item>
      </el-form>
    </el-dialog>
  </div>
</template>

<script>
import RoadSection from "@/views/baseData/components/roadSection/index.vue";
import selectTree from "@/components/DeptTmpl/selectTree.vue";
import {
  addRegister, getConstructionById,
  getPendingConstructionList,
  process,
  viewConstructionList
} from "@/api/maintenanceProject/taskList";
import {getProject} from "@/api/maintenanceProject/projectManage";
import moment from "moment/moment";

export default {
  name: 'ConstructionReg',
  components: {selectTree, RoadSection,
    RenderDom: {
      functional: true,
      props: {
        row: Object,
        index: Number,
        render: Function
      },
      render(createElement, ctx) {
        const {row, index} = ctx.props
        return ctx.props.render(row, index)
      }
    }
  },
    dicts: ['maintenance_project_type', 'task_type', 'project_type', 'affiliation_project_type'],
    data() {
    return {
      showSearch: false,
      queryParams: {
        pageNum: 1,
        pageSize: 50
      },
      total: 0,
      loading: false,
      dialogVisible: false,
      projectData: {},
      formData: {},
      projectColumns: [
        { name: '项目名称', field: 'projName', dict: '' },
        { name: '任务单名称', field: 'name', dict: '' },
        { name: '任务单编码', field: 'code', dict: '' },
        { name: '路段名称', field: 'maiSecName', dict: '' },
        { name: '位置', field: 'mileRange', dict: '' },
        { name: '实施要求', field: 'exeRequire', dict: '' },
        { name: '施工单位', field: 'conDomainName', dict: '' },
        { name: '施工合同', field: 'conConName', dict: '' },
        { name: '监理单位', field: 'supDomainName', dict: '' },
        { name: '监理合同', field: 'supConName', dict: '' },
        { name: '设计单位', field: 'designDomainName', dict: '' },
        { name: '设计合同', field: 'designConName', dict: '' },
        { name: '工作内容', field: 'content', dict: '' },
      ],
      columns: [
          {key: 0, width: 100, field: 'timeOutMsg', label: `超期提醒`, visible: true},
          {key: 3, width: 100, field: 'status', label: `状态`, visible: true, dict: 'maintenance_project_type'},
          {key: 4, width: 200, field: 'projName', label: `项目名称`, visible: true},
          {key: 5, width: 200, field: 'projCode', label: `项目编码`, visible: true},
          {key: 6, width: 100, field: 'type', label: `任务单类型`, visible: true, dict: 'task_type'},
          {key: 7, width: 200, field: 'name', label: `任务单名称`, visible: true},
          {key: 8, width: 200, field: 'code', label: `任务单编号`, visible: true},
          {key: 9, width: 100, field: 'maiSecName', label: `路段名称`, visible: true},
          {key: 10, width: 100, field: 'domainName', label: `管养单位`, visible: true},
          {key: 11, width: 200, field: 'conDomainName', label: `施工单位`, visible: true},
          {key: 12, width: 200, field: 'conConName', label: `施工合同`, visible: true},
          {key: 13, width: 200, field: 'supDomainName', label: `监理单位`, visible: true},
          {key: 14, width: 200, field: 'supConName', label: `监理合同`, visible: true},
          {key: 15, width: 200, field: 'designDomainName', label: `设计单位`, visible: true},
          {key: 16, width: 100, field: 'issueDate', label: `发出时间`, visible: true},
          {key: 17, width: 100, field: 'defLiaPer', label: `缺陷责任期(月)`, visible: true},
          {key: 18, width: 200, field: 'content', label: `工作内容`, visible: true},
          {key: 19, width: 200, field: 'exeRequire', label: `实施要求`, visible: true},
          // {key: 20, width: 100, field: 'projectType', label: `工程分类`, visible: true, dict: 'project_type'},
          // {key: 21, width: 100, field: 'mtype', label: `所属工程类别`, visible: true, dict: 'affiliation_project_type'},
          {key: 22, width: 100, field: 'beginDate', label: `计划开始时间`, visible: true},
          {key: 23, width: 100, field: 'endDate', label: `计划完成时间`, visible: true},
          {key: 24, width: 100, field: 'field24', label: `验收人员`, visible: true},
      ],
      tableData: [],
    }
  },
  mounted() {
    this.handleQuery()
  },
  methods:{
    handleQuery() {
      if (this.queryParams.issueDate && this.queryParams.issueDate.length == 2) {
          this.queryParams.issueDate.startIssueDate = this.queryParams.issueDate[0]
          this.queryParams.issueDate.endIssueDate = this.queryParams.issueDate[1]
      }
      this.loading = true
      this.queryParams.status = 4
      getPendingConstructionList(this.queryParams).then(res => {
          this.tableData = res.rows
          this.total = res.total
          this.loading = false
      })
    },
    resetQuery() {
      this.queryParams = {
        pageNum: 1,
        pageSize: 50
      }
      this.handleQuery()
    },
    handleReg(row) {
      getConstructionById(row.id).then(res => {
        this.projectData = res.data
        this.projectData.domainName = row.domainName
        this.projectData.maiSecName = row.maiSecName
        this.formData.businessKey = row.id
        this.formData.taskId = row.taskId
        this.formData.approved = true
        this.formData.startTime = moment().format('YYYY-MM-DD')
        this.dialogVisible = true
      })
    },
    // 接收
    handleReceive() {
      // if (!this.formData.comment) {
      //   this.$message.warning('请填写审批意见！')
      //   return
      // }
      process(this.formData).then(res => {
        this.$message.success('操作成功！');
        this.dialogVisible = false
        this.handleQuery()
      })
    },
    // 导出清单按钮
    exportList() {
        if (this.queryParams.issueDate && this.queryParams.issueDate.length == 2) {
            this.queryParams.issueDate.startIssueDate = this.queryParams.issueDate[0]
            this.queryParams.issueDate.endIssueDate = this.queryParams.issueDate[1]
        }
        this.download(
            'manager/proj/construction/pending/export',
            {...this.queryParams},
            `buildTask_${new Date().getTime()}.xlsx`,
            {
                headers: {'Content-Type': 'application/json;'},
                parameterType: 'body'
            }
        )
    },
  }
}
</script>

<style scoped lang="scss">

</style>
<style lang="scss" scoped>
@import "@/assets/styles/business.scss";
</style>
