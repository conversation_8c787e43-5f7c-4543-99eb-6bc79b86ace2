<template>
  <PageContainer :ifHeader="false">
    <template slot="search">
      <div style="display: flex; align-items: center; margin: 0">
        <CascadeSelection
          style="min-width: 192px"
          :form-data="queryParams"
          v-model="queryParams"
          types="201"
          multiple
        />
        <RangeInput
          @startValue="
            (v) => {
              queryParams.centerStakeStart = v;
            }
          "
          @endValue="
            (v) => {
              queryParams.centerStakeEnd = v;
            }
          "
          :clearData="clearData"
        />
        <div style="min-width: 240px">
          <el-button
            v-hasPermi="['baseData:bridgeHis:selectPage']"
            type="primary"
            icon="el-icon-search"
            @click="handleQuery"
            >搜索</el-button
          >
          <el-button icon="el-icon-refresh" @click="resetQuery">重置</el-button>
          <el-button
            v-show="!showSearch"
            icon="el-icon-arrow-down"
            circle
            @click="showSearch = true"
          />
          <el-button
            v-show="showSearch"
            icon="el-icon-arrow-up"
            style="
              color: #1890ff;
              border-color: #badeff;
              background-color: #e8f4ff;
            "
            circle
            @click="showSearch = false"
          />
        </div>
      </div>
      <el-form
        v-show="showSearch"
        ref="queryForm"
        :model="queryParams"
        :inline="true"
      >
        <div class="first-divider" />
        <el-form-item style="margin: 5px 10px 0 0">
          <el-select
            v-model="queryParams.mainSuperstructureType"
            placeholder="结构形式"
            clearable
            multiple
            collapse-tags
          >
            <el-option
              v-for="dict in dict.type.bridge_main_superstructure_type"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item style="margin: 5px 10px 0 0">
          <el-input
            v-model="queryParams.bridgeCode"
            style="width: 100%"
            placeholder="桥梁编码"
            clearable
          />
        </el-form-item>
        <el-form-item style="margin: 5px 10px 0 0">
          <el-input
            v-model="queryParams.bridgeName"
            style="width: 100%"
            placeholder="桥梁名称"
            clearable
          />
        </el-form-item>
        <el-form-item style="margin: 5px 10px 0 0">
          <el-select
            v-model="queryParams.operationState"
            placeholder="运营状态"
            clearable
            collapse-tags
          >
            <el-option
              v-for="dict in dict.type.sys_operation_state"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item style="margin: 5px 10px 0 0">
          <el-select
            v-model="queryParams.whetherSingleColumnPierType"
            placeholder="是否是独柱墩"
            clearable
          >
            <el-option
              v-for="dict in dict.type.base_data_yes_no"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item style="margin: 5px 10px 0 0">
          <el-select
            v-model="queryParams.spanClassifyType"
            placeholder="跨径分类"
            clearable
            multiple
            collapse-tags
          >
            <el-option
              v-for="dict in dict.type.bridge_span_classify"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item style="margin: 5px 10px 0 0">
          <el-select
            v-model="queryParams.bridgeTechAssessType"
            placeholder="评定等级"
            clearable
            multiple
            collapse-tags
          >
            <el-option
              v-for="dict in dict.type.bridge_tec_condition_level"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item style="margin: 5px 10px 0 0">
          <el-select
            v-model="queryParams.whetherInLongSpanCatalogType"
            placeholder="是否长大桥目录"
            clearable
          >
            <el-option
              v-for="dict in dict.type.base_data_yes_no"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item style="margin: 5px 10px 0 0">
          <el-select
            v-model="queryParams.status"
            placeholder="暂存状态"
            clearable
          >
            <el-option
              v-for="dict in [
                { value: 1, label: '暂存' },
                { value: 2, label: '正常' },
              ]"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item style="margin: 5px 10px 0 0">
          <el-select
            v-model="queryParams.whetherHealthMonitorSystemType"
            placeholder="是否有健康监测系统"
            clearable
          >
            <el-option
              v-for="dict in dict.type.base_data_yes_no"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>

        <el-form-item style="margin: 5px 10px 0 0">
          <el-select
            v-model="queryParams.ifAdditionalOverpassBridge"
            placeholder="是否为新增上垮桥"
            clearable
          >
            <el-option
              v-for="dict in dict.type.base_data_yes_no"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item style="margin: 5px 10px 0 0">
          <el-select
            v-model="queryParams.ifRebuildAfterDemolition"
            placeholder="是否为拆除还建桥"
            clearable
          >
            <el-option
              v-for="dict in dict.type.base_data_yes_no"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item style="margin: 5px 10px 0 0">
          <el-select
            v-model="queryParams.overpassBridgeType"
            placeholder="跨线桥类型"
            clearable
          >
            <el-option
              v-for="dict in dict.type.overpass_bridge_type"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="修建年度" style="margin: 5px 10px 0 0">
          <el-date-picker
            v-model="queryParams.constructionYear"
            type="year"
            value-format="yyyy"
            placeholder="选择年份"
          />
        </el-form-item>

        <!-- <el-form-item label="建成时间" style="margin: 5px 10px 0 0">
          <el-date-picker
            v-model="dateRange"
            type="daterange"
            range-separator="-"
            start-placeholder="年-月-日"
            end-placeholder="年-月-日"
            value-format="yyyy-MM-dd"
          />
        </el-form-item> -->
      </el-form>
    </template>
    <!-- <template
      slot="header"

    >

    </template> -->
    <template slot="body">
      <el-table
        v-adjust-table
        ref="table"
        v-loading="loading"
        height="99%"
        border
        :data="staticList"
        :header-cell-style="{ height: '36px' }"
        :row-style="rowStyle"
        @selection-change="handleSelectionChange"
        @row-click="handleRowClick"
      >
        <el-table-column type="selection" width="50" align="center" />
        <el-table-column
          fixed
          label="序号"
          type="index"
          width="50"
          align="center"
        >
          <template v-slot="scope">
            {{
              scope.$index +
              (queryParams.pageNum - 1) * queryParams.pageSize +
              1
            }}
          </template>
        </el-table-column>
        <el-table-column fixed label="操作" align="center" width="80">
          <template slot-scope="scope">
            <el-link
              :disabled="!scope.row.shape"
              type="primary"
              :underline="false"
              @click.stop="handleLocation(scope.row)"
              >定位</el-link
            >
          </template>
        </el-table-column>
        <el-table-column
          fixed
          label="桥梁名称"
          align="center"
          prop="bridgeName"
          min-width="130"
          show-overflow-tooltip
        />
        <el-table-column
          label="桥梁编码"
          align="center"
          prop="bridgeCode"
          min-width="130"
          show-overflow-tooltip
        />
        <el-table-column
          label="养护路段"
          align="center"
          prop="maintenanceSectionName"
          min-width="130"
          show-overflow-tooltip
        />
        <el-table-column
          label="路线编码"
          align="center"
          prop="routeCode"
          min-width="130"
          show-overflow-tooltip
        />
        <el-table-column
          label="暂存状态"
          align="center"
          prop="status"
          min-width="130"
        >
          <template slot-scope="scope">
            {{ scope.row.status == 1 ? "暂存" : "正常" }}
          </template>
        </el-table-column>
        <el-table-column
          label="管理处"
          align="center"
          prop="managementMaintenanceName"
          min-width="130"
          show-overflow-tooltip
        />
        <el-table-column
          label="管养分处"
          align="center"
          prop="managementMaintenanceBranchName"
          min-width="130"
          show-overflow-tooltip
        />
        <el-table-column
          label="结构形式"
          align="center"
          prop="mainSuperstructureTypeName"
          min-width="130"
          show-overflow-tooltip
        />
        <el-table-column
          label="桥位桩号"
          align="center"
          prop="centerStake"
          min-width="130"
          show-overflow-tooltip
        >
          <template slot-scope="scope">
            {{ formatPile(scope.row.centerStake) }}
          </template>
        </el-table-column>
        <el-table-column
          label="桥长"
          align="center"
          prop="totalLength"
          min-width="130"
          show-overflow-tooltip
        />
        <el-table-column
          label="桥梁评定等级"
          align="center"
          prop="bridgeTechAssessTypeName"
          min-width="130"
          show-overflow-tooltip
        />
        <el-table-column
          label="跨径分类"
          align="center"
          prop="spanClassifyTypeName"
          min-width="130"
          show-overflow-tooltip
        />
        <el-table-column
          label="跨径组合"
          align="center"
          prop="spanGroups"
          min-width="130"
          show-overflow-tooltip
        />
        <el-table-column
          label="固定编码"
          align="center"
          prop="fixedCode"
          min-width="130"
          show-overflow-tooltip
        />
        <el-table-column
          label="是否是独柱墩"
          align="center"
          prop="whetherSingleColumnPierTypeName"
          min-width="130"
          show-overflow-tooltip
        >
          <template slot-scope="scope">
            <dict-tag
              :options="dict.type.base_data_yes_no"
              :value="scope.row.whetherSingleColumnPierType"
            />
          </template>
        </el-table-column>
        <el-table-column
          label="运营状态"
          align="center"
          prop="operationStateName"
          min-width="130"
          show-overflow-tooltip
        >
          <template slot-scope="{ row }">
            <el-link
              :underline="false"
              :type="
                { 1: 'info', 2: 'success', 3: 'danger', 4: 'primary' }[
                  row.operationState
                ]
              "
            >
              <DictTag
                :value="row.operationState"
                :options="dict.type.sys_operation_state"
              />
            </el-link>
          </template>
        </el-table-column>
        <el-table-column
          label="是否锁定"
          align="center"
          prop="isLocked"
          min-width="130"
          show-overflow-tooltip
        >
          <template slot-scope="{ row }">
            <el-link
              :underline="false"
              :type="row.isLocked ? 'danger' : 'info'"
              @click.stop="handleLocked(row)"
            >
              {{ row.isLocked ? "是" : "否" }}
            </el-link>
          </template>
        </el-table-column>
        <el-table-column
          label="是否长大桥梁目录"
          align="center"
          prop="whetherInLongSpanCatalogTypeName"
          min-width="140"
          show-overflow-tooltip
        >
          <template slot-scope="scope">
            <dict-tag
              :options="dict.type.base_data_yes_no"
              :value="scope.row.whetherInLongSpanCatalogType"
            />
          </template>
        </el-table-column>
        <el-table-column
          label="建成时间"
          align="center"
          prop="buildDate"
          min-width="130"
          show-overflow-tooltip
        />

        <el-table-column
          label="变更人"
          align="center"
          prop="operateBy"
          min-width="130"
          show-overflow-tooltip
        />
        <el-table-column
          label="变更时间"
          align="center"
          prop="operateTime"
          min-width="130"
          show-overflow-tooltip
        />
        <el-table-column label="图片" align="center" width="220">
          <template slot-scope="scope">
            <div style="display: flex; justify-content: space-around">
              <el-button
                :disabled="!scope.row.frontPhotoId"
                type="text"
                @click="handlePhotos(scope.row, 1)"
                >正面照</el-button
              >
              <el-button
                :disabled="!scope.row.facadePhotoId"
                type="text"
                @click="handlePhotos(scope.row, 2)"
                >立面照</el-button
              >
              <el-button
                :disabled="!scope.row.typicalPhotoId"
                type="text"
                @click="handlePhotos(scope.row, 3)"
                >典型照</el-button
              >
            </div>
          </template>
        </el-table-column>
        <el-table-column fixed="right" label="档案" align="center" width="80">
          <template slot-scope="scope">
            <el-button
              v-hasPermi="['baseData:bridgeHis:info']"
              type="text"
              @click="goArchivesPage(scope.row)"
              >查看</el-button
            >
          </template>
        </el-table-column>
      </el-table>
      <pagination
        :total="total"
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
        :pageSizes="[10, 20, 30, 50, 100, 1000]"
        @pagination="getList"
      />
    </template>

    <el-dialog
      :title="imageTitle"
      :visible.sync="showImage"
      width="500px"
      append-to-body
    >
      <ImagePreview :owner-id="imageUrl" width="100%" height="100%" />
    </el-dialog>
    <Drawings
      v-if="showDrawing"
      :showDrawing="showDrawing"
      :typeCode="'BS129'"
      :id="ids[0]"
      @close="showDrawing = false"
    />

    <MapPosition
      v-if="showMapPosition"
      :dialogVisible="showMapPosition"
      :data="mapPositionData"
      @close="showMapPosition = false"
    />
    <drawer-panel
      :show.sync="panelShow"
      :title="panelTitle"
      size="87%"
      @close="onPanelClose"
    >
      <ArchiveIndex :key="archiveKey" />
    </drawer-panel>
  </PageContainer>
</template>

<script>
import { listStatic } from "@/api/baseData/bridge/history/index";
import { statusListDialog } from "@/views/baseData/components/statusDialog/list.js";
import SelectTreeCheckbox from "@/components/DeptTmpl/selectTreeCheckbox";
import CascadeSelection from "@/components/CascadeSelection/index.vue";
import RangeInput from "@/views/baseData/components/rangeInput/index.vue";
import Drawings from "@/views/baseData/components/drawings/index.vue";
import MapPosition from "@/components/mapPosition/index.vue";
import DrawerPanel from "@/components/RightPanel/drawer.vue";
import ArchiveIndex from "@/views/baseData/bridge/baseInfo/archives/index.vue";

export default {
  name: "History",
  components: {
    CascadeSelection,
    SelectTreeCheckbox,
    RangeInput,
    Drawings,
    MapPosition,
    DrawerPanel,
    ArchiveIndex,
  },
  dicts: [
    "bridge_tec_condition_level",
    "bridge_main_superstructure_type",
    "base_data_yes_no",
    "bridge_span_classify",
    "sys_operation_state",
    "overpass_bridge_type",
  ],
  data() {
    return {
      loading: true,
      showAddEdit: false,
      forView: false,
      clearData: false,
      title: "",
      importBaseType: "1",
      importType: null,
      formData: "",
      ids: [],
      showSearch: false,
      total: 0,
      staticList: null,
      queryParams: {
        pageNum: 1,
        pageSize: 20,
        operationState: "2",
      },
      dateRange: [],
      maintenanceOptions: [], // 养护路段options
      routeCodeOptions: [], // 路线编码options
      imageUrl: "",
      imageTitle: "",
      showImage: false,
      showImportAdd: false,
      showCard: false,
      isUpdate: false,
      selectdTables: [],
      showDrawing: false,
      showMapPosition: false,
      mapPositionData: undefined,
      panelShow: false,
      panelTitle: "桥梁档案",
      archiveKey: new Date().getTime(),
    };
  },
  watch: {},
  created() {
    this.getList();
  },
  methods: {
    // 获取表格数据
    getList() {
      this.loading = true;
      if (this.dateRange?.length > 0) {
        this.queryParams.buildDateStart = this.dateRange[0];
        this.queryParams.buildDateEnd = this.dateRange[1];
      } else {
        this.queryParams.buildDateStart = "";
        this.queryParams.buildDateEnd = "";
      }
      listStatic(this.queryParams)
        .then((response) => {
          this.staticList = response.rows;
          this.total = response.total;
          this.clearData = false;
          this.loading = false;
        })
        .catch(() => {
          this.loading = false;
        });
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.selectdTables = selection;
      this.ids = selection.map((item) => item.id);
    },
    // 表格操作-运营状态
    handleOperational(event, row) {
      event.stopPropagation();
      statusListDialog({ dataId: row.id, baseDataType: 1 });
    },
    // 表格点击勾选
    handleRowClick(row) {
      row.isSelected = !row.isSelected;
      this.$refs.table.toggleRowSelection(row);
    },
    // 勾选高亮
    rowStyle({ row, rowIndex }) {
      if (this.ids.includes(row.id)) {
        return { "background-color": "#b7daff", color: "#333" };
      } else {
        return { "background-color": "#fff", color: "#333" };
      }
    },
    // 搜索按钮
    handleQuery() {
      this.queryParams.pageNum = 1;

      this.getList();
    },
    // 重置按钮
    resetQuery() {
      this.dateRange = [];
      this.clearData = true;
      this.queryParams = {
        pageNum: 1,
        pageSize: 20,
        operationState: "2",
      };
      this.handleQuery();
    },

    closeImportAdd(v) {
      this.showImportAdd = false;
      if (v) this.getList();
    },

    // 表格操作-正面照、立面照、典型照
    handlePhotos(row, type) {
      this.showImage = true;
      switch (type) {
        case 1:
          this.imageUrl = row.frontPhotoId;
          this.imageTitle = "桥梁正面照";
          break;
        case 2:
          this.imageUrl = row.facadePhotoId;
          this.imageTitle = "桥梁立面照";
          break;
        case 3:
          this.imageUrl = row.typicalPhotoId;
          this.imageTitle = "桥梁典型照片";
          break;
      }
    },
    // 表格操作-档案查看
    goArchivesPage(row) {
      let { path } = this.$route;
      let query = {
        bridgeName: row.bridgeName,
        id: row.id,
        whetherHealthMonitorSystemType: row.whetherHealthMonitorSystemType,
        type: "history",
      };
      this.$router.push({
        path,
        query,
      });
      // 刷新组件
      this.archiveKey = new Date().getTime();
      this.panelShow = true;
      return;
      this.$router.push(
        `baseInfo/archives?bridgeName=${row.bridgeName}&id=${row.id}&whetherHealthMonitorSystemType=${row.whetherHealthMonitorSystemType}&type=history`
      );
    },
    // 定位
    handleLocation(row) {
      this.mapPositionData = row;
      this.showMapPosition = true;
    },
    onPanelClose() {
      this.panelShow = false;
      let { path } = this.$route;
      this.$router.push({
        path,
        query: {},
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.first-divider {
  width: 100%;
  height: 1px;
  border-bottom: 1px solid #dcdfe6;
  margin: 10px 0 5px 0 !important;
}
</style>
