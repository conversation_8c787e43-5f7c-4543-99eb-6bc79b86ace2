<template>
  <div class="app-container">
    <el-row :gutter="20">

      <!--筛选区开始-->
      <el-col :span="24" :xs="24">
        <!--操作按钮区开始-->
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button
              type="primary"
              icon="el-icon-plus"
              size="mini"
              @click="handleAdd"
              v-hasPermi="['system:category:add']"
            >新增</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button
              type="success"
              icon="el-icon-edit"
              size="mini"
              :disabled="single"
              @click="handleUpdate"
              v-hasPermi="['system:category:edit']"
            >修改</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button
              icon="el-icon-sort"
              size="mini"
              @click="modifySort"
              v-hasPermi="['system:category:edit']"
            >排序</el-button>
          </el-col>
          <el-col :span=1.5>
            <el-button type="primary" icon="el-icon-refresh" size="mini" @click="handleQuery">刷新</el-button>
          </el-col>
          <right-toolbar :showSearch.sync="showSearch" @queryTable="getList" :columns="columns"></right-toolbar>
        </el-row>
        <!--操作按钮区结束-->

        <!--数据表格开始-->
        <div class="tableDiv">
          <el-table
            ref="table"
            size="mini"
            row-key="id"
            :height="showSearch ? 'calc(100vh - 320px)' : 'calc(100vh - 260px)'"
            style="width: 100%"
            v-loading="loading"
            border
            :data="categoryList"
            @current-change="handleCurrentChange"
            :row-style="rowStyle"
            default-expand-all
            :tree-props="{children: 'children', hasChildren: 'hasChildren'}"
            @row-click="handleRowClick"
          >
            <el-table-column label="名称" align="left" header-align="center" prop="label" :show-overflow-tooltip="true"/>
            <el-table-column label="说明" align="left" header-align="center" prop="data.description" :show-overflow-tooltip="true" />
            <el-table-column label="序号" align="right" header-align="center" prop="orders" width="50" />
            <el-table-column
              label="操作"
              fixed="right"
              align="left"
              header-align="center"
              width="160"
              class-name="small-padding fixed-width"
            >
              <template slot-scope="scope" v-if="scope.row.userId !== 1">
                <el-button
                  size="mini"
                  type="text"
                  icon="el-icon-plus"
                  @click="handleAddChildren(scope.row)"
                  v-hasPermi="['system:category:add']"
                >新增</el-button>
                <el-button
                  size="mini"
                  type="text"
                  icon="el-icon-edit"
                  @click="handleUpdate(scope.row)"
                  v-hasPermi="['system:category:edit']"
                >修改</el-button>
                <el-button
                  size="mini"
                  type="text"
                  icon="el-icon-delete"
                  v-if="scope.row.children==null"
                  @click="handleDelete(scope.row)"
                  v-hasPermi="['system:category:remove']"
                >删除</el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
        <!--数据表格结束-->
      </el-col>
    </el-row>

    <!-- 添加或修改结构分组对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="600px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-col :span="24">
          <el-form-item label="名称" prop="name">
            <el-input v-model="form.name" placeholder="请输入名称"  maxlength="200" show-word-limit/>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="序号" prop="orders">
            <el-input-number v-model="form.orders" placeholder="请输入序号" style="width:100%;" controls-position="right" :min="1" :step="1" :max="1000000"></el-input-number>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="说明" prop="description">
            <el-input v-model="form.description" type="textarea" placeholder="请输入内容" :rows="5"  maxlength="10000" show-word-limit/>
          </el-form-item>
        </el-col>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
    <el-dialog title="排序结构分组" :visible.sync="sortOpen" width="600px" append-to-body>
      <el-tree :data="sortData" node-key="id" default-expand-all draggable>
      </el-tree>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="sortSave">保存排序</el-button>
        <el-button @click="sortCancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getStructureGroupFindTree, getStructureGroup, delStructureGroup, addStructureGroup, updateStructureGroup, updateStructureGroupModifySort } from "@/api/jgjc/jc/structureGroup";
import { getToken } from "@/utils/auth";
import Treeselect from "@riophae/vue-treeselect";
import "@riophae/vue-treeselect/dist/vue-treeselect.css";

export default {
  name: "JcStructureGroup",
  components: {  },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: false,
      dictType:[],
      // 总条数
      total: 0,
      // 结构分组表格数据
      categoryList: null,
      // 弹出层标题
      title: "",
      // 部门树选项
      deptOptions: undefined,
      // 是否显示弹出层
      open: false,
      currentRow:null,
      // 表单参数
      form: {},
      defaultProps: {
        children: "children",
        label: "label"
      },
      sortOpen:false,
      sortData:[],

      // 用户导入参数
      upload: {
        // 是否显示弹出层（用户导入）
        open: false,
        // 弹出层标题（用户导入）
        title: "",
        // 是否禁用上传
        isUploading: false,
        // 是否更新已经存在的用户数据
        updateSupport: 0,
        // 设置上传的请求头部
        headers: { Authorization: "Bearer " + getToken() },
        // 上传的地址
        url: process.env.VUE_APP_BASE_API + "/system/user/importData"
      },
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 50,
        parentId: null,
        name: null,
        orders: null,
        description: null
      },
      // 列信息
      columns: [
        { key: 1, label: `名称`, visible: true },
        { key: 3, label: `说明`, visible: true },
        { key: 2, label: `序号`, visible: true }
      ],
      // 表单校验
      rules: {
        name: [
          { required: true, message: "名称不能为空", trigger: "blur" }
        ],
        orders: [
          { required: true, message: "序号不能为空", trigger: "blur" }
        ]
      }
    };
  },
  watch: {
    // 根据名称筛选部门树
  },
  created() {
    this.getList();
    // this.getDeptTree();
    // this.getConfigKey("sys.user.initPassword").then(response => {
    //   this.initPassword = response.msg;
    // });
  },
  methods: {
    /** 查询用户列表 */
    getList() {
      this.loading = true;
      getStructureGroupFindTree().then(response => {
        this.categoryList = response.data;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        parentId: null,
        name: null,
        orders: 1,
        description: ''
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dateRange = [];
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id);
      this.single = selection.length != 1;
      this.multiple = !selection.length;
    },
    handleCurrentChange(val){
      this.currentRow = val;
      this.ids=[val.id]
      this.single=false
      this.multiple=true
    },
    // 表格点击勾选
    handleRowClick(row) {
      //row.isSelected = !row.isSelected;
      // this.$refs.table.toggleRowSelection(row);
      this.$refs.table.setCurrentRow(row);
    },
    // 勾选高亮
    rowStyle({ row, rowIndex }) {
      if (this.ids.includes(row.id)) {
        return { 'background-color': '#E1F0FF', color: '#333' }
      } else {
        return { 'background-color': '#fff', color: '#333' }
      }
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.form.parentId = null;
      this.open = true;
      this.title = "添加结构分组";
    },
    handleAddChildren(row){
      this.reset();
      this.form.parentId = row.id;
      this.open = true;
      this.title = "添加结构分组";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids;
      getStructureGroup(id).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改结构分组";
      });
    },
    /** 提交按钮 */
    submitForm: function() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.id != null) {
            updateStructureGroup(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addStructureGroup(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const id = row.id || this.ids;
      this.$modal.confirm('是否确认删除结构分组为"' + row.label + '"的数据项？').then(function() {
        return delStructureGroup(id);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    modifySort(){
      this.loading = true;
      getStructureGroupFindTree().then(response => {
        this.sortData = response.data;
        this.sortOpen = true;
        this.loading = false;
      });
    },
    sortSave(){
      var cmd = {
        organizationId: null,
        data: new Array()
      }
      var len = this.sortData.length
      for (let j = 0; j < len; j++) {
        var root = this.sortData[j]
        var rootCmd = {
          id: root.id,
          children: new Array()
        }
        if (root.children !== null) {
          rootCmd.children = new Array()
          this.childrenNode(rootCmd, root.children)
        }
        cmd.data.push(rootCmd)
      }
      updateStructureGroupModifySort(cmd).then(response => {
        if(response.success) {
          this.$modal.msgSuccess("保存排序成功");
          this.sortOpen = false;
          this.sortData=[]
          this.getList()
        }else{
          this.$modal.msgSuccess("保存排序失败");
        }
      });
    },
    childrenNode(parentCmd, children) {
      for (let i = 0; i < children.length; i++) {
        var node = children[i]
        var childCmd = {
          id: node.id,
          children: new Array()
        }
        if (node.children !== null) {
          this.childrenNode(childCmd, node.children)
        }
        parentCmd.children.push(childCmd)
      }
    },
    sortCancel(){
      this.sortOpen = false;
      this.sortData=[]
    }
  }
};
</script>
<style>
.hasTagsView .app-main[data-v-078753dd]{
  background: #f5f7fa;
}

.tableDiv{
  background-color: white;
  padding-bottom: 10px;
}
</style>
