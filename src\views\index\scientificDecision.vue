<template>
  <div class="home-container">
    <v-scale-screen width="7680" height="2160">
      <img src="@/assets/home/<USER>/big/kxjc.png" alt="" />
      <div class="btn">
        <img src="@/assets/cockpit/back.png" @click="back" style="width:118px;height:54px" />
      </div>
    </v-scale-screen>
  </div>
</template>

<script>
export default {
  data() {
    return {}
  },
  methods: {
    back() {
      this.$router.go(-1)
    }
  }
}

</script>

<style lang="scss" scoped>
img {
  height: 100%;
}

.home-container {
  position: relative;
}

.btn {
  position: absolute;
  top: 45px;
  right: 50px;
}
</style>
