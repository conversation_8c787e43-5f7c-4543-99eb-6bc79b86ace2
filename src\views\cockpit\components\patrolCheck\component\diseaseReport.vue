<template>
  <div class="disease-report" v-loading="loading" element-loading-background="rgba(0, 0, 0, 0.4)">
    <div class="chars-pie">
      <Echarts :option="option" v-if="option" height="100%" key="reportKey" />
    </div>
    <div class="sta-data">
      <div class="data-list" v-for="(item, index) in rData" :key="'list' + index"
        :class="index < rData.length - 1 ? 'border-b' : ''">
        <div class="list-name">
          <span class="legend-color" :style="{ backgroundColor: rColor[index] }"></span>
          <span>{{ item.name }}</span>
        </div>
        <div class="list-value">
          <div class="account-for">
            <span class="value">{{ item.value }} <span>个</span></span>
            <span class="account">（{{ item.accountFor }}）</span>
          </div>
          <div class="on-year">
            <span class="name">同比</span>
            <span class="value" :style="{ color: item.onYear > 0 ? '#EA0000' : '#95F204' }">
              {{ item.onYear }}
              <span v-if="item.onYear > 0">↑</span>
              <span v-else>↓</span>
            </span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import Echarts from '../../echarts/echarts.vue';
// api
import { getDiseaseReportCount } from '@/api/cockpit/inspection';
import { isBigScreen } from '../../../util/utils';

export default {
  name: "diseaseReport",
  components: {
    Echarts,
  },
  data() {
    return {
      isBig: isBigScreen(),
      loading: false,
      option: null,
      rData: [{
        name: '普通事件',
        value: 0,
        accountFor: '0%',
        onYear: 0,
      }, {
        name: '缺陷责任期',
        value: 0,
        accountFor: '0%',
        onYear: 0,
      }, {
        name: '及时事件',
        value: 0,
        accountFor: '0%',
        onYear: 0,
      }],
      rColor: ['#01FBEF', '#3851E6', '#C1A133', '#E333B7', '#00CFFF', '#FFE000', '#FFA800'],
      total: 0,
      grow: 0,
    }
  },
  mounted() {
    this.init();
  },
  methods: {
    init() {
      let data = this.rData;
      const colorArr = this.rColor;
      this.loading = true;
      getDiseaseReportCount().then(res => {
        if (res.code === 200 && res.rows) {
          data = res.rows.filter(v => v.name !== '总数').map(v => {
            return {
              name: v.name,
              value: v.count,
              accountFor: (v.ratio ? v.ratio.toFixed(2) : 0) + '%',
              onYear: v.grow ? v.grow.toFixed(2) : 0,
            }
          });
          this.total = res.rows.find(v => v.name === '总数').count;
          this.grow = res.rows.find(v => v.name === '总数').grow;
          this.grow = this.grow.toFixed(2);
          this.rData = data;
          this.option = this.initPieCharts(data, colorArr);
        }
      }).finally(() => {
        this.loading = false;
      })
    },
    initPieCharts(dataArr = [], colorArr = []) {
      var trafficWay = dataArr || [];
      var data = [];

      let sum = dataArr.reduce((acc, curr) => acc + curr.value, 0);
      let avg = sum / (dataArr.length * 10);
      let total = avg;

      var color = colorArr || ['#00ffff', '#00cfff', '#006ced', '#ffe000', '#ffa800', '#ff5b00', '#ff3000']
      for (var i = 0; i < trafficWay.length; i++) {
        data.push({
          value: trafficWay[i].value,
          name: trafficWay[i].name,
          itemStyle: {
            normal: {
              borderWidth: this.isBig ? 16 : 8,
              shadowBlur: 2,
              borderColor: color[i],
              shadowColor: color[i]
            }
          }
        }, {
          value: total || 3,
          name: '',
          itemStyle: {
            normal: {
              label: {
                show: false
              },
              labelLine: {
                show: false
              },
              color: 'rgba(0, 0, 0, 0)',
              borderColor: 'rgba(0, 0, 0, 0)',
              borderWidth: 0
            }
          }
        });
      }
      var seriesOption = [
        {
          type: "pie",
          zlevel: 0,
          silent: true,
          radius: ["92%", "96%"],
          center: ['50%', '50%'],
          hoverAnimation: false,
          color: "rgba(0,62,122,1)",
          label: {
            normal: {
              show: false,
            },
          },
          labelLine: {
            normal: {
              show: false,
            },
          },
          data: [1],
        },
        {
          name: '',
          type: 'pie',
          clockWise: false,
          radius: ['80%', '80%'],
          hoverAnimation: false,
          label: {
            normal: {
              show: false,
            },
          },
          labelLine: {
            normal: {
              show: false,
            },
          },
          data: data
        }];
      let subStr = `{text|本月上报病害数}\n{text|同比} {number|${this.grow}↑}`
      if (this.grow <= 0) {
        subStr = `{text|本月上报病害数}\n{text|同比} {number1|${this.grow}↓}`
      }
      let option = {
        backgroundColor: 'rgba(0,0,0,0)',
        color: color,
        title: {
          text: `{total|${this.total}} {unit|个}`,
          subtext: subStr,
          top: this.isBig ? '38%' : '32%',
          textAlign: "center",
          left: this.isBig ? "50%" : "48%",
          textStyle: {
            color: '#fff',
            fontSize: this.isBig ? 44 : 22,
            fontWeight: '400',
            rich: {
              total: {
                fontSize: this.isBig ? 52 : 26,
                fontWeight: 'bold'
              },
              unit: {
                fontSize: this.isBig ? 40 : 20,
              }
            }
          },
          subtextStyle: {
            color: '#fff',
            fontSize: this.isBig ? 32 : 16,
            fontWeight: '400',
            rich: {
              text: {
                fontSize: this.isBig ? 28 : 14,
                fontWeight: 400
              },
              number: {
                fontSize: this.isBig ?28 : 14,
                fontWeight: 400,
                color: '#EA0000',
                padding: [10, 5]
              },
              number1: {
                fontSize: this.isBig ?28 : 14,
                fontWeight: 400,
                color: '#95F204',
                padding: [10, 5]
              }
            }
          }
        },
        tooltip: {
          show: false
        },
        toolbox: {
          show: false
        },
        series: seriesOption
      }
      return option;
    },
  }
}
</script>

<style lang="scss" scoped>
@import "@/assets/styles/utils.scss";

.disease-report {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  padding: vwpx(20px) vwpx(10px);

  .chars-pie {
    flex: 2;
    height: 100%;
  }

  .sta-data {
    flex: 3;
    display: flex;
    flex-direction: column;
    margin-top: vwpx(30px);

    .data-list {
      display: flex;
      justify-content: space-between;
      margin: vwpx(20px);
      color: #ffffff;
      padding-bottom: vwpx(20px);
      font-size: vwpx(26px);

      .list-name {

        .legend-color {
          display: inline-block;
          width: vwpx(20px);
          height: vwpx(20px);
          border-radius: 50%;
          margin: 0 vwpx(10px);
        }
      }

      .list-value {
        margin-right: vwpx(40px);

        .account-for {
          .value {
            span {
              color: #B6B6B6;
            }
          }
        }

        .on-year {
          margin-top: vwpx(12px);

          .name {
            margin-right: vwpx(20px);
          }

          .value {
            // display: flex;
            // align-items: center;

            span {
              margin-left: vwpx(2px);
            }
          }
        }
      }
    }

    .border-b {
      border-bottom: 1px dotted rgba(156, 189, 255, 0.5);
    }
  }
}
</style>