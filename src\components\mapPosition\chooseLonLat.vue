<template>
  <div class="choose-lonlat">
    <el-dialog :title="title" :visible.sync="visibleSync" :width="width" @close="handelClose">
      <div style="position: relative;">
        <div class="map" ref="mapRef" v-loading="loading"></div>
        <div class="input">
          <div style="display: flex;align-items: center;">
            <el-input v-model="keyWord" placeholder="请输入地址搜索" clearable></el-input>
            <el-button type="primary" @click="onSearch" icon="el-icon-location" class="ml-1">查询</el-button>
          </div>
          <div class="address-list">
            <template v-if="list.length && resultType == 1">
              <div v-for="(item, index) in list" :key="index" class="list" @click="onClick(item)">
                <span>{{ item.name }}</span>
              </div>
            </template>
            <template v-else-if="list.length && resultType == 2">
              <div v-for="(item, index) in list" :key="index" class="list"
                @click="onSearch(item.adminName, item.adminCode)">
                <span>{{ item.adminName }} ({{ item.count }})</span>
              </div>
            </template>
            <template v-else-if="keyWord && !list.length && resultType">
              <div>无数据</div>
            </template>
          </div>
        </div>
        <div class="current-location">
          <div>当前经纬度：</div>
          <el-input v-model="location" placeholder="请输入或选择当前位置"></el-input>
        </div>
      </div>
      <span slot="footer">
        <el-button @click="handelClose">取消</el-button>
        <el-button type="primary" @click="handelSub">确定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import "ol/ol.css";
import { Map, View } from 'ol';
import TileLayer from 'ol/layer/Tile';
import { XYZ } from 'ol/source';
import { fromLonLat } from 'ol/proj';
import { defaults } from 'ol/control';

import { Loading } from 'element-ui';
import { WKT } from 'ol/format';

import AddressIcon from '@/assets/map/address.png'
import { Fill, Icon, Style, Text } from 'ol/style';
import VectorLayer from 'ol/layer/Vector';
import VectorSource from 'ol/source/Vector';
import { wgs84togcj02 } from "./formate";

var key = "cde0b56cf882626889981701109a7536";

export default {
  name: 'chooseLonLat',
  props: {
    title: {
      type: String,
      default: '选择经纬度'
    },
    visible: {
      type: Boolean,
      default: false
    },
    width: {
      type: String,
      default: '60%'
    },
    type: {
      type: String,
      default: 'lonlat'
    }
  },
  data() {
    return {
      lonlat: '',
      mapLayer: null,
      defaultCenter: [102.75530900000001, 24.95423900000002],
      loading: false,
      location: '',
      keyWord: '',
      list: [],
      resultType: null,
      name: '',
    }
  },
  computed: {
    visibleSync: {
      get() {
        return this.visible;
      },
      set(v) {
        this.$emit("update:visible", v);
      },
    },
  },
  async mounted() {
    // 获取当前经纬度
    this.loading = true;
    await this.getLonLat();
    this.location = this.defaultCenter.join(',')
    this.$nextTick(() => {
      this.initMap();
      this.loading = false;
      this.lonlat = this.location;
      this.loadToMap(this.defaultCenter);
    })
  },
  watch: {
    keyWord(val) {
      if (!val) {
        this.list = [];
      }
    }
  },
  destroyed() {
    this.mapLayer = null
  },
  methods: {
    initMap() {
      this.mapLayer = new Map({
        target: this.$refs.mapRef,
        layers: [
          new TileLayer({
            source: new XYZ({
              url: `http://t{0-7}.tianditu.gov.cn/DataServer?T=img_w&x={x}&y={y}&l={z}&tk=${key}`,
              attributions: "天地图影像",
              wrapX: false,
              maxZoom: 18,
            })
          }),
          new TileLayer({
            name: "天地图矢量图层注记",
            source: new XYZ({
              url: `http://t{0-7}.tianditu.gov.cn/DataServer?T=cva_w&x={x}&y={y}&l={z}&tk=${key}`,
              attributions: "天地图的属性描述",
              wrapX: false,
            }),
            preload: Infinity,
          }),
          new TileLayer({
            name: "高德影像地图数据图层",
            source: new XYZ({
              url: `https://webst02.is.autonavi.com/appmaptile?lang=zh_cn&size=10&scale=10&style=8&x={x}&y={y}&z={z}`,
              attributions: "高德影像地图数据图层，自带注记",
              wrapX: false,
              maxZoom: 18,
            }),
            preload: Infinity,
          })
        ],
        view: new View({
          projection: "EPSG:4326",
          center: this.defaultCenter,
          zoom: 10,
          maxZoom: 20,
        }),
        controls: defaults({
          attribution: false,
          zoom: false,
          rotate: false,
        })
      });
      // 点击地图事件
      this.mapLayer.on('singleclick', async (evt) => {
        let lonlat = evt.coordinate;
        await this.getAddress(lonlat);
        this.lonlat = lonlat.join(',');
        this.location = lonlat.join(',');
        this.loadToMap(lonlat);
      })
    },
    // 通过经纬度获取 位置名称
    getAddress(lonlat) {
      return new Promise((resolve, reject) => {
        fetch(
          `https://api.tianditu.gov.cn/geocoder?postStr={'lon':${lonlat[0]},'lat':${lonlat[1]},'ver':1}&type=geocode&tk=${key}`,
          {
            method: 'GET',
            mode: 'cors', // 跨域请求需要设置 mode: 'cors'
          }
        )
          .then((response) => response.json()) // 将响应解析为 JSON
          .then((res) => {
            if (res.status == 0) {
              let result = res.result
              this.name = result.addressComponent.address || result.formatted_address
            }
            resolve(res);
          }).catch((err) => {
            reject(err);
          });
      })
    },

    // 异步获取当前经纬度
    getLonLat() {
      return new Promise((resolve, reject) => {
        if (navigator.geolocation) {
          // 定位
          navigator.geolocation.getCurrentPosition((position) => {
            this.defaultCenter = [position.coords.longitude - 0, position.coords.latitude - 0]
            resolve(position.coords);
          }, (err) => {
            let errorType = ['您拒绝共享位置信息', '获取不到位置信息', '获取位置信息超时']
            resolve(err);
          }, {
            enableHighAccuracy: true, //布尔值，表示系统是否使用最高精度来表示结果，注意，这会导致较慢的响应时间或者增加电量消耗（比如对于支持gps的移动设备来说）。如果值为false ，设备会通过更快响应以及/或者使用更少的电量等方法来尽可能的节约资源。默认值fasle
            timeout: 600, //它表明的是设备必须在多长时间（单位毫秒）内返回一个位置。默认直到获取到位置才会返回值。
            maximumAge: 0 //表明可以返回多长时间（即最长年龄，单位毫秒）内的可获取的缓存位置。如果设置为 0, 说明设备不能使用一个缓存位置，而且必须去获取一个真实的当前位置。默认0
          });
        } else {
          console.log("浏览器不支持地理定位");
          resolve();
        }
      })
    },
    onSearch(name = null, code = null) {
      if (!this.keyWord) {
        this.$message.warning('请输入地址')
        return;
      }
      let specify = code;
      if (name) {
        //   this.keyWord = name;
      }
      let postStr = specify
        ? `{"yingjiType":0,"sourceType":0,"keyWord":"${this.keyWord}","level":7,"mapBound":"97.5167,21.1333,106.1833,29.25","queryType":7,"start":0,"count":100,"queryTerminal":10000,specify:${specify}}`
        : `{"yingjiType":0,"sourceType":0,"keyWord":"${this.keyWord}","level":7,"mapBound":"97.5167,21.1333,106.1833,29.25","queryType":7,"start":0,"count":100,"queryTerminal":10000}`;
      let loading = Loading.service({ fullscreen: false });
      fetch(
        `https://api.tianditu.gov.cn/v2/search?postStr=${postStr}&type=query&tk=${key}`,
        {
          method: 'GET',
          mode: 'cors', // 跨域请求需要设置 mode: 'cors'
        }
      )
        .then((response) => response.json()) // 将响应解析为 JSON
        .then((res) => {
          if (res.status && res.status.infocode === 1000) {
            this.resultType = res.resultType;
            if (res.resultType == 1) {
              this.list = res.pois || [];
            } else if (res.resultType == 2) {
              this.list = res.statistics?.allAdmins || [];
            }
          } else {
            console.log('获取位置失败')
          }
        }).finally(() => {
          loading.close();
        })
    },
    // 加载到地图
    loadToMap(lonlat) {
      this.mapLayer.getView().setCenter(lonlat);
      // this.mapLayer.getView().setZoom(15);
      this.removePoint();
      // 将坐标带你转为wkt
      let wkt = `POINT(${lonlat[0]} ${lonlat[1]})`;
      // 将wkt 转为Feature
      let feature = new WKT().readFeature(wkt);
      //将Feature加载到地图
      let style = new Style({
        image: new Icon({
          src: AddressIcon,
          anchor: [0.5, 0.5],
        }),
        // text: new Text({
        //   text: this.name,
        //   font: '14px 微软雅黑',
        //   fill: new Fill({
        //     color: '#ffffff',
        //   }),
        //   offsetY: -35,
        // })
      })
      feature.setStyle(style);
      let layer = new VectorLayer({
        source: new VectorSource({
          features: [feature],
        }),
      })
      layer.set('name', 'pointLayer')
      layer.setZIndex(1000)
      this.mapLayer.addLayer(layer);
    },
    // 移除地图上的点
    removePoint() {
      let allLayer = this.mapLayer?.getLayers()?.getArray();
      if (!allLayer) return;
      let layer = allLayer.find((l) => l.get('name') == 'pointLayer');
      if (layer) {
        this.mapLayer.removeLayer(layer);
      }
    },
    handelClose() {
      this.$emit("cancel");
    },
    handelSub() {
      if (!this.lonlat) {
        this.$message.warning('请选择位置')
        return;
      }

      // 若果存在中文逗号则替换为英文逗号
      if (this.lonlat.includes('，')) {
        this.lonlat = this.lonlat.replace('，', ',')
      }
      // 将wgcs84 转为 gcj02
      let arr = this.lonlat.split(',');
      let res = wgs84togcj02(arr[0] - 0, arr[1] - 0);
      this.lonlat = `${res[0]},${res[1]}`;
      this.$emit("ok", this.lonlat);
    },
    // 获取点击数据
    onClick(row) {
      this.lonlat = row.lonlat;
      this.location = row.lonlat;
      this.name = row.name;
      let arr = this.location.split(',');
      this.loadToMap([arr[0] - 0, arr[1] - 0])
    }
  }
}
</script>

<style lang="scss">
.choose-lonlat {
  position: relative;

  .map {
    width: 100%;
    height: 600px;
  }

  .input {
    position: absolute;
    left: 10px;
    top: 10px;
    width: 260px;
    background-color: #ffffff;
    border-radius: 5px;
    padding: 5px;

    .address-list {
      max-height: 400px;
      overflow-y: auto;

      .list {
        cursor: pointer;
        margin: 5px 0;
      }
    }
  }

  .current-location {
    position: absolute;
    width: 240px;
    height: 60px;
    background-color: #ffffff;
    border-radius: 5px;
    right: 10px;
    top: 10px;
    padding: 5px 10px;
  }

  .el-dialog {
    padding: 0;

    .el-dialog__body {
      padding: 0;
    }

    .el-dialog__header {
      padding: 5px !important;
    }

    .el-dialog__footer {
      padding: 5px;
    }

    .el-dialog__headerbtn {
      top: 10px;
    }
  }

  .ml-1 {
    margin-left: 10px;
  }
}
</style>