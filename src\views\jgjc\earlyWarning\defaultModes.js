// 中交国通 (ZJGT)
export const zjgtModes = [
  { label: '正常', value: '0', strobe: false, display: 'none', displayName: '', flicker: false, sound: '无' },
  { label: '道路轮廓强化', value: '1', strobe: false, display: 'yellow', displayName: '慢', flicker: true, sound: '减速慢行' },
  { label: '主动诱导', value: '2', strobe: false, display: 'yellow', displayName: '慢', flicker: false, sound: '减速慢行' },
  { label: '减速慢行', value: '3', strobe: false, display: 'yellow', displayName: '慢', flicker: false, sound: '减速慢行' },
  { label: '封路', value: '4', strobe: true, display: 'red', displayName: '停', flicker: false, sound: '前方封路停止前进' },
  { label: '交通事故', value: '5', strobe: true, display: 'yellow', displayName: '慢', flicker: false, sound: '前方发生交通事故' },
  { label: '特重大交通事故', value: '6', strobe: true, display: 'red', displayName: '停', flicker: false, sound: '前方发生交通事故' },
  { label: '桥梁坍塌', value: '7', strobe: true, display: 'red', displayName: '停', flicker: false, sound: '前方塌陷,请减速' },
  { label: '边坡坍塌', value: '8', strobe: true, display: 'red', displayName: '停', flicker: false, sound: '前方塌陷,请减速' }
];

// 维的美 (WDM)
export const wdmModes = [
  { label: '正常', value: '0', strobe: false, display: 'none', displayName: '', flicker: false, sound: '无' },
  { label: '道路轮廓强化', value: '1', strobe: false, display: 'yellow', displayName: '慢', flicker: false, sound: '请减速慢行' },
  { label: '主动诱导', value: '2', strobe: false, display: 'yellow', displayName: '慢', flicker: false, sound: '请减速慢行' },
  { label: '减速慢行', value: '3', strobe: false, display: 'yellow', displayName: '慢', flicker: false, sound: '请减速慢行' },
  { label: '封路', value: '4', strobe: true, display: 'red', displayName: '停', flicker: false, sound: '前方封路,禁止通行' },
  { label: '交通事故', value: '5', strobe: true, display: 'yellow', displayName: '慢', flicker: false, sound: '前方交通事故,请减速慢行' },
  { label: '特重大交通事故', value: '6', strobe: true, display: 'red', displayName: '停', flicker: false, sound: '前方特重大交通事故，禁止通行' },
  { label: '桥梁坍塌', value: '7', strobe: true, display: 'red', displayName: '停', flicker: false, sound: '前方桥梁塌陷，禁止通行' },
  { label: '边坡坍塌', value: '8', strobe: true, display: 'red', displayName: '停', flicker: false, sound: '前方边坡塌陷，禁止通行' }
];

// 三思 (Sansi)
export const sansiModes = [
  { label: '正常', value: '0', strobe: false, display: 'none', displayName: '', flicker: false, sound: '无' },
  { label: '道路轮廓强化', value: '1', strobe: false, display: 'yellow', displayName: 'ball', flicker: false, sound: '请谨慎驾驶' },
  { label: '主动诱导', value: '2', strobe: false, display: 'yellow', displayName: 'ball', flicker: true, sound: '请减速慢行' },
  { label: '减速慢行', value: '3', strobe: false, display: 'red', displayName: 'ball', flicker: true, sound: '请减速慢行' },
  { label: '封路', value: '4', strobe: true, display: 'red', displayName: '停', flicker: false, sound: '前方封路，请停止前进' },
  { label: '交通事故', value: '5', strobe: true, display: 'red', displayName: '慢', flicker: true, sound: '前方发生交通事故，请减速' },
  { label: '特重大交通事故', value: '6', strobe: true, display: 'red', displayName: '停', flicker: false, sound: '前方发生特重大事故，请减速' },
  { label: '桥梁坍塌', value: '7', strobe: true, display: 'red', displayName: '停', flicker: true, sound: '前方坍塌，请停止前进' },
  { label: '边坡坍塌', value: '8', strobe: true, display: 'red', displayName: '停', flicker: true, sound: '前方边坡坍塌，停止前进' }
];

