<template>
  <PageContainer>
    <template slot="search">
      <el-row :gutter="20">
        <el-col :span="3" :offset="0">
          <el-input v-model="queryParams.name" placeholder="分类名称" clearable size="mini"
            @keyup.enter.native="handleQuery" />
        </el-col>
        <el-col :span="3" :offset="0">
          <el-select v-model="queryParams.classifyStatType" placeholder="分类统计类型" clearable size="mini">
            <el-option label="count" :value="1" />
            <el-option label="sum" :value="2" />
          </el-select>
        </el-col>
        <el-col :span="6" :offset="0">
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">
            搜索
          </el-button>
          <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">
            重置
          </el-button>
        </el-col>
      </el-row>
    </template>
    <template slot="header">
      <el-row :gutter="20">
        <el-col :span="1.5" :offset="0" class="mb8">
          <el-button v-hasPermi="['baseData:culvert:add']" type="primary" size="mini" @click="handleAdd">
            新增
          </el-button>
        </el-col>
        <el-col :span="1.5" :offset="0" class="mb8">
          <el-button v-hasPermi="['baseData:culvert:edit']" :disabled="!(tableSelects.length === 1)" type="primary"
            size="mini" @click="handleUpdate">
            编辑
          </el-button>
        </el-col>
        <el-col :span="1.5" :offset="0" class="mb8">
          <el-button v-hasPermi="['baseData:culvert:delete']" :disabled="!(tableSelects.length > 0)" type="primary"
            size="mini" @click="handleDelete">
            删除
          </el-button>
        </el-col>
        <el-col :span="1.5" :offset="0" class="mb8">
          <el-button v-hasPermi="['baseData:culvert:getInfoById']" :disabled="!(tableSelects.length === 1)" type="primary"
            size="mini" @click="onDetail">
            查看
          </el-button>
        </el-col>
      </el-row>
    </template>
    <template slot="body">
      <el-table v-loading="loading" :data="menuSubClassifyList" @selection-change="handleSelectionChange"
        @row-click="handleRowClick" border height="100%" :row-key="(row) => row.id" ref="tableRef"
        :tree-props="{children: 'child'}">
        <el-table-column type="selection" width="55" align="center" :reserve-selection="true" />
        <el-table-column label="分类名称" min-width="100" prop="name" show-overflow-tooltip/>
        <el-table-column label="主键id" min-width="150" prop="id" show-overflow-tooltip/>
        <el-table-column label="分类统计类型" min-width="120" align="center" prop="statType">
          <template slot-scope="{row}">
            {{ row.statType == 1 ? 'count' : 'sum' }}
          </template>
        </el-table-column>
        <el-table-column label="分类统计单位" min-width="120" align="center" prop="statUnit">
          <template slot-scope="{ row }">
            {{ row.statUnit }}
          </template>
        </el-table-column>
        <el-table-column label="图层显示层级" min-width="120" align="center" prop="showLevel">
          <template slot-scope="{ row }">
            {{ row.showLevel }}
          </template>
        </el-table-column>
        <el-table-column label="一张图是否显示" min-width="120" align="center" prop="oneMapShow">
          <template slot-scope="{ row }">

          </template>
        </el-table-column>
<!--        <el-table-column label="图层描述信息" min-width="120" align="center" prop="description" show-overflow-tooltip />-->
        <el-table-column label="图标" align="center" prop="iconId">
          <template slot-scope="{ row }">
            <span v-if="row.iconId" @click.stop="">
              <ImagePreview :owner-id="row.iconId" />
            </span>
            <span v-else>无</span>
          </template>
        </el-table-column>
        <el-table-column label="数据库表字段" min-width="120" align="center" prop="tableColumnInfo" show-overflow-tooltip />
        <el-table-column label="查询数据源" min-width="120" align="center" prop="datasourceName" show-overflow-tooltip />
        <el-table-column label="目录展示类型" align="center" prop="iconId">
          <template slot-scope="{ row }">
            {{ row.menuShowType == 0 ? '都显示' : row.menuShowType == 1 ? '树形显示' : '数据总览' }}
          </template>
        </el-table-column>
        <el-table-column label="排序" align="center" prop="showIndex" />
      </el-table>

      <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize"
        @pagination="getList" />
    </template>

    <!-- 新增编辑查看 -->
    <Dialog :title="title" :show.sync="open">
      <AddOrEdit :form="form" @close="handleClose" @refresh="getList" :readonly="readonly" />
    </Dialog>
  </PageContainer>
</template>

<script>
import { getListPage, getMenuSubClassify, delMenuSubClassify,getMenuSubClassifyTree } from '@/api/oneMap/subClassify'
import AddOrEdit from "./components/addOrEdit.vue";
import Dialog from "@/components/Dialog/index.vue";

export default {
  name: "MenuSubClassify",
  components: {
    AddOrEdit,
    Dialog,
  },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 图层目录数据配置子分类表格数据
      menuSubClassifyList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        layerMenuSubId: null,
        parentId: null,
        subName: null,
        subTableName: null,
        subTableColumn: null,
        description: null,
        subStatType: null,
        subStatUnit: null,
        layerShowLevel: null,
        showIndex: null,
        oneMapShow: null,
        iconId: null
      },
      // 表单参数
      form: {},
      // 选中列表数据
      tableSelects: [],
      readonly: false, // 查看
    };
  },
  created() {
    this.queryParams.layerMenuSubId = this.$route.query.id
    this.getList();
  },
  methods: {
    /** 查询图层目录数据配置子分类列表 */
    getList() {
      this.loading = true;
      // getListPage(this.queryParams).then(response => {
      //   this.menuSubClassifyList = response.rows;
      //   this.total = response.total;
      //   this.loading = false;
      // });
      getMenuSubClassifyTree(this.queryParams).then(res=>{
        if(res) {
          this.menuSubClassifyList = res || [];
        }
      }).finally(()=>{
        this.loading = false;
      })
    },
    handleClose(e) {
      this.open = e;
      this.readonly = false;
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        layerMenuSubId: null,
        parentId: null,
        subName: null,
        subTableName: null,
        subTableColumn: null,
        description: null,
        subStatType: null,
        subStatUnit: null,
        layerShowLevel: null,
        showIndex: null,
        oneMapShow: null,
        iconId: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.tableSelects = selection;
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.form.layerMenuSubId = this.$route.query.id
      this.open = true;
      this.title = "添加图层目录数据配置子分类";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      let id = this.tableSelects[0].id
      getMenuSubClassify(id).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改图层目录数据配置子分类";
      });
    },
    /** 查看详情 */
    onDetail() {
      this.reset();
      let id = this.tableSelects[0].id
      getMenuSubClassify(id).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "查看图层目录数据配置子分类";
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = this.tableSelects.map(v => v.id)
      this.$modal.confirm('是否确认删除图层目录数据配置子分类的数据项？').then(function () {
        return delMenuSubClassify(ids);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => { });
    },
    handleRowClick(row) {
      let arr = this.tableSelects.filter((v) => v.id == row.id);
      // 点击行 选中复选框
      if (arr && arr.length > 0) {
        this.$refs.tableRef.toggleRowSelection(row, false);
        this.tableSelects = this.tableSelects.filter((v) => v.id != row.id);
      } else {
        this.tableSelects = [...this.tableSelects, ...[row]];
        this.$refs.tableRef.toggleRowSelection(row, true);
      }
    },
  }
};
</script>

<style lang="scss" scoped></style>
