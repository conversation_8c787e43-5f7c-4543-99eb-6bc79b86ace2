<template>
  <el-drawer
    title="我是标题"
    size="50%"
    :visible.sync="localDrawerShow"
    :direction="direction"
    :append-to-body="true"
    @open="init"
    @close="handleClose"
  >
    <div class="parts-options-container">
      <el-descriptions size="mini">
        <el-descriptions-item label="类型编号">
          {{ moduleType.code }}
        </el-descriptions-item>
        <el-descriptions-item label="类型名称">
          {{ moduleType.name }}
        </el-descriptions-item>
      </el-descriptions>
      <div
        style="
          height: 75vh;
          overflow: auto;
          margin-top: 20px;
          display: flex;
          justify-content: space-around;
        "
      >
        <CardComponent
          :parent-name="moduleType && moduleType.name ? moduleType.name : ''"
          :dialogName="[moduleType && moduleType.name ? moduleType.name : '']"
          field-name="moduleName"
          child-field-name="categoryName"
          :items="moduleFileList"
          :defaultEntity="defaultEntity"
          :form-struct="formStructFile"
          :form-rule="formRuleFile"
          :currentSelect="currentSelect"
          :onSave="saveModuleFile"
          :onDelete="deleteModuleFile"
          :clickHandler="handleParentClick"
        />
        <CardComponent
          :parent-name="
            currentSelect && currentSelect.moduleName
              ? currentSelect.moduleName
              : ''
          "
          :dialogName="[
            moduleType && moduleType.name ? moduleType.name : '',
            currentSelect && currentSelect.moduleName
              ? currentSelect.moduleName
              : '',
          ]"
          field-name="categoryName"
          :items="selectedSubList"
          :defaultEntity="defaultSubEntity"
          :form-struct="formStructFileSub"
          :form-rule="formRuleFileSub"
          :currentSelect="CurrentSelectSub"
          :onSave="saveModuleFileSub"
          :onDelete="deleteModuleFileSub"
        />
      </div>
    </div>
  </el-drawer>
</template>


<script>
import {
  saveModuleFile,
  deleteModule,
  selectVoByTypeId,
} from "@/api/system/moduleFile.js";
import { saveModule, deleteFileSubModule } from "@/api/system/moduleFileSub.js";
import CardComponent from "@/views/system/moduleFile/CardComponent.vue";
export default {
  name: "Drawer",
  components: { CardComponent },
  props: {
    moduleType: {
      type: Object,
      default: {},
    },
    drawerShow: {
      type: Boolean,
      default: false,
    },
    direction: {
      type: String,
      default: "rtl",
    },
  },
  data() {
    return {
      localDrawerShow: this.drawerShow,
      moduleFileList: [],
      defaultEntity: {},
      defaultSubEntity: {},
      currentSelect: {},
      CurrentSelectSub: {},
      selectedSubList: [],
      formStructFile: [
        {
          label: "名称",
          prop: "moduleName",
          type: "el-input",
          attrs: { placeholder: "请输入名称" },
        },
        {
          label: "描述",
          prop: "remark",
          type: "el-input",
          attrs: { placeholder: "请输入描述" },
        },
      ],
      formRuleFile: {
        moduleName: [
          { required: true, message: "请输入名称", trigger: "blur" },
        ],
        remark: [{ required: false, message: "请输入描述", trigger: "blur" }],
      },
      formStructFileSub: [
        {
          label: "类型名称",
          prop: "categoryName",
          type: "el-input",
          attrs: { placeholder: "请输入名称" },
        },
        {
          label: "文件类型",
          prop: "fileTypeArray",
          type: "el-select",
          attrs: {
            multiple: true,
            filterable: true,
            allowCreate: true,
            defaultFirstOption: true,
            placeholder: "不填写则类型无限制",
            options: [
              { value: "jpg", label: "jpg" },
              { value: "png", label: "png" },
              { value: "jpeg", label: "jpeg" },
              { value: "doc", label: "doc" },
              { value: "docx", label: "docx" },
              { value: "xls", label: "xls" },
              { value: "xlsx", label: "xlsx" },
              { value: "pdf", label: "pdf" },
              { value: "ppt", label: "ppt" },
            ],
          },
        },
        {
          label: "文件数量",
          prop: "fileNum",
          type: "el-input-number",
          attrs: {
            controlsPosition: "right",
            placeholder: "不填写则数量无限制",
            min: 0,
          },
        },
        {
          label: "文件大小(MB)",
          prop: "fileSize",
          type: "el-input-number",
          attrs: {
            controlsPosition: "right",
            placeholder: "不填写则大小无限制",
            min: 0,
          },
        },
        {
          label: "排序",
          prop: "sort",
          type: "el-input-number",
          attrs: {
            controlsPosition: "right",
            placeholder: "请输入排序",
            min: 1,
            max: 10,
          },
        },
      ],
      formRuleFileSub: {
        categoryName: [
          { required: true, message: "请输入名称", trigger: "blur" },
        ],
        fileTypeArray: [
          { required: false, message: "请输入允许文件后缀", trigger: "blur" },
        ],
        fileNum: [
          {
            required: false,
            message: "请输入最大允许文件数量",
            trigger: "blur",
          },
        ],
        fileSize: [
          {
            required: false,
            message: "请输入最大允许文件大小",
            trigger: "blur",
          },
        ],
        sort: [{ required: false, message: "请输入排序", trigger: "blur" }],
      },
    };
  },
  methods: {
    fetchList(id = this.moduleType?.id) {
      if (id != null) {
        selectVoByTypeId(id)
          .then((res) => {
            this.moduleFileList = res.data;
            // this.$set(this, "moduleFileList", res.data);
            this.init();
          })
          .catch((error) => {
            console.error("返回错误:", error);
            // 处理错误情况
            this.moduleFileList = [];
            this.currentSelect = {};
            this.selectedSubList = [];
          });
      } else {
        // 处理 id 为 null 的情况
        this.moduleFileList = [];
        this.currentSelect = {};
        this.selectedSubList = [];
      }
    },
    init() {
      // 更新 currentSelect 为新列表中的项或新列表的第一项
      this.currentSelect =
        this.moduleFileList.find(
          (item) => item.id === this.currentSelect?.id
        ) ||
        this.moduleFileList[0] ||
        {};

      this.handleParentClick(this.currentSelect);
    },
    saveModuleFile(data) {
      const modulesName = data.moduleName;
      saveModuleFile(data).then(() => {
        this.$message.success(modulesName + "添加成功");
        this.fetchList(this.moduleType.id);
      });
    },
    deleteModuleFile(data) {
      console.log(data);
      deleteModule(data.id).then(() => {
        this.$message.success(data.moduleName + "删除成功");
        this.fetchList(this.moduleType.id);
      });
    },
    saveModuleFileSub(data) {
      const categoryName = data.categoryName;
      saveModule(data).then(() => {
        this.$message.success(categoryName + "添加成功");
        this.fetchList(this.moduleType.id);
      });
    },
    deleteModuleFileSub(data) {
      deleteFileSubModule(data.id).then(() => {
        this.$message.success(data.categoryName + "删除成功");
        this.fetchList(this.moduleType.id);
      });
    },

    handleParentClick(item) {
      this.defaultSubEntity = { moduleId: item?.id ?? null };
      this.currentSelect = item ?? null;
      this.selectedSubList = item?.sysModuleFileSubList ?? [];
    },

    handleClose() {
      //   this.currentSelect = {};
      //   this.moduleFileList = [];
      this.selectedSubList = [];
      this.$emit("update:drawerShow", false);
      this.$emit("update:currentRow", {});
    },
  },
  watch: {
    moduleType(value) {
      this.fetchList(value.id);
      this.defaultEntity = { typeId: value.id };
    },
    drawerShow(newVal) {
      this.localDrawerShow = newVal;
    },
    localDrawerShow(newVal) {
      this.$emit("update:drawerShow", newVal);
    },
  },
};
</script>

<style scoped>
.parts-options-container {
  margin-top: -20px;
  padding: 20px;
}

.parts-options-container .el-icon-circle-close,
.parts-options-container .el-icon-circle-check,
.parts-options-container .el-icon-delete,
.parts-options-container .el-icon-edit {
  font-size: 16px;
  padding: 0px 5px;
  cursor: pointer;
}

/*按钮图标颜色*/
.parts-options-container .el-icon-edit {
  color: #1c83e4;
}

.parts-options-container .el-icon-circle-close {
  color: #d0021b;
}

.parts-options-container .el-icon-circle-check {
  color: #2fd351;
}

.parts-options-container .el-icon-delete {
  /*  color: #1C83E4;*/
  /*  color: #1171CD*/
}
</style>