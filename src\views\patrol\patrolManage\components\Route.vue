<template>
	<div class="component-container">
		<!-- 查询条件区域 -->
		<div class="query-container">
			<el-row :gutter="48" style="width: 80%">
				<!-- 寻线员 -->
				<el-col :span="6" :offset="0">
					<el-input v-model="queryForm.personName" placeholder="寻线员" />
				</el-col>
				<!--巡查类型，1:日间巡查 2:夜间巡查-->
				<el-col :span="6" :offset="0">
					<el-select v-model="queryForm.patrolType" placeholder="请选择巡查类型">
						<el-option label="日间巡查" value="1" />
						<el-option label="夜间巡查" value="2" />
					</el-select>
				</el-col>
				<!--有无事件-->
				<el-col :span="6" :offset="0">
					<el-select v-model="queryForm.hasEvent" placeholder="请选择有无事件">
						<el-option label="有事件" value="1" />
						<el-option label="无事件" value="0" />
					</el-select>
				</el-col>
				<!--搜索按钮 导出清单 导出卡片-->
				<el-col :span="6" :offset="0">
					<el-button type="primary" @click="handleSearch">搜索</el-button>
					<el-button type="primary" @click="handleExport">导出清单</el-button>
					<el-button type="primary" @click="handleExport">导出卡片</el-button>
				</el-col>
			</el-row>
		</div>
		<!-- 表格区域 -->
		<div class="table-container">
			<el-table v-adjust-table :data="tableData" style="width: 100%" height="calc(100% - 50px)">
				<el-table-column prop="number" label="序号" />
				<el-table-column prop="patrolType" label="巡查类型" />
				<el-table-column prop="patrolSection" label="巡查路段" />
				<el-table-column prop="direction" label="方向" />
				<el-table-column prop="patrolPerson" label="巡查人员" />
				<el-table-column prop="vehicle" label="车辆" />
				<el-table-column prop="weather" label="天气" />
				<el-table-column prop="patrolContent" label="巡查内容" />
				<el-table-column prop="maintenanceUnit" label="管养单位" />
				<el-table-column prop="managementOffice" label="管理处" />
				<el-table-column prop="startTime" label="开始时间" />
				<el-table-column prop="endTime" label="结束时间" />
				<el-table-column prop="reportTime" label="上报时间" />
				<el-table-column prop="collectTime" label="采集时间" />
				<el-table-column prop="patrolMileage" label="巡查里程(公里)" />
				<el-table-column prop="diseaseCount" label="巡查病害数" />
			</el-table>
			<pagination
				v-show="total > 0"
				:total="total"
				:page.sync="queryForm.pageNum"
				:limit.sync="queryForm.pageSize"
				@pagination="getList"
			/>
		</div>
	</div>
</template>

<script>
export default {
	data() {
		return {
			queryForm: {
				pageNum: 1,
				pageSize: 10,
			},
			tableData: [],
			total: 0,
		}
	},
	methods: {
		handleSearch() {
			this.getList()
		},
		handleExport() {
			console.log('导出')
		},
		handleExportCard() {
			console.log('导出卡片')
		},
		getList() {
			console.log('获取列表')
		},
	},
}
</script>

<style scoped>
.component-container {
	height: 100%;
	display: flex;
	flex-direction: column;
}

.query-container {
	padding-bottom: 20px;
}

.table-container {
	flex: 1;
	overflow: hidden;
}

.el-input,
.el-select {
	width: 100%;
}

.el-button {
	margin-right: 10px;
}

.el-button:last-child {
	margin-right: 0;
}
</style>
