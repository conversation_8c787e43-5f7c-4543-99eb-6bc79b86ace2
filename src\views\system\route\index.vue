<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" label-width="68px">
      <el-form-item label="" prop="routeCode">
        <el-input
          v-model="queryParams.routeCode"
          placeholder="请输入路线编码"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="" prop="routeName">
        <el-input
          v-model="queryParams.routeName"
          placeholder="请输入路线名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="" prop="routeType">
        <el-select v-model="queryParams.routeType" placeholder="请选择路线性质" clearable>
          <el-option
            v-for="dict in dict.type.sys_route_nature"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <!--默认折叠 ,此处仅作为示例-->
    <el-form :model="queryParams" ref="queryForm1" size="small" :inline="true" v-show="showSearch" label-width="68px"></el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['system:route:add']"
        >新增
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['system:route:edit']"
        >修改
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['system:route:remove']"
        >删除
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['system:route:export']"
        >导出
        </el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <!--数据表格开始-->
    <div class="tableDiv">
      <el-table v-adjust-table size="mini" :height="showSearch ? 'calc(100vh - 320px)' : 'calc(100vh - 260px)'" border
                v-loading="loading" :data="routeList" @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" align="center"/>
        <el-table-column label="序号"     fixed  align="center" type="index" width="50">
          <template v-slot="scope">
            {{ (scope.$index + (queryParams.pageNum - 1) * queryParams.pageSize)+1 }}
          </template>
        </el-table-column>
        <el-table-column label="路线编码"  fixed :show-overflow-tooltip="true" align="center" prop="routeCode"/>
        <el-table-column label="路线名称"  fixed :show-overflow-tooltip="true" align="center" prop="routeName"/>
        <el-table-column label="起点名称" fit :show-overflow-tooltip="true" align="center" prop="placeStartName"/>
        <el-table-column label="终点名称" :show-overflow-tooltip="true" align="center" prop="placeEndName"/>
        <el-table-column label="起点桩号" :show-overflow-tooltip="true" align="center" prop="pileStart" :formatter="(...arg)=>formatPile(arg[2])"/>
        <el-table-column label="终点桩号" :show-overflow-tooltip="true" align="center" prop="pileEnd" :formatter="(...arg)=>formatPile(arg[2])"/>
        <el-table-column label="路线性质" :show-overflow-tooltip="true" align="center" prop="routeType">
          <template slot-scope="scope">
            <dict-tag :options="dict.type.sys_route_nature" :value="scope.row.routeType"/>
          </template>
        </el-table-column>
<!--        <el-table-column label="抗震等级" :show-overflow-tooltip="true" align="center" prop="seismicGrade">-->
<!--          <template slot-scope="scope">-->
<!--            <dict-tag :options="dict.type.side_slope_seismic_grade" :value="scope.row.seismicGrade"/>-->
<!--          </template>-->
<!--        </el-table-column>-->
        <el-table-column label="路线长度(m)" :show-overflow-tooltip="true" align="center" prop="roadSectionLength"
                         :formatter="(...arg)=>{if(arg[2]) return arg[2].toLocaleString()}"/>
        <el-table-column label="创建时间"  :show-overflow-tooltip="true" align="center" prop="createdTime" width="180" v-if="false">
          <template slot-scope="scope">
            <span>{{ parseTime(scope.row.createdTime, '{y}-{m}-{d}') }}</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width" fixed="right" width="120" >
          <template slot-scope="scope">
            <el-button
              size="mini"
              type="text"
              icon="el-icon-edit"
              @click="handleUpdate(scope.row)"
              v-hasPermi="['system:route:edit']"
            >修改
            </el-button>
            <el-button
              size="mini"
              type="text"
              icon="el-icon-delete"
              @click="handleDelete(scope.row)"
              v-hasPermi="['system:route:remove']"
            >删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <pagination
        v-show="total>0"
        :total="total"
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
        @pagination="getList"
      />

    </div>
    <!--数据表格结束-->
    <!-- 添加或修改路线管理对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="40%" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="96px">
        <el-form-item label="路线编码" prop="routeCode">
          <el-input v-model="form.routeCode" placeholder="请输入路线编码"/>
        </el-form-item>
        <el-form-item label="路线名称" prop="routeName">
          <el-input v-model="form.routeName" placeholder="请输入路线名称"/>
        </el-form-item>
        <el-form-item label="起点名称" prop="placeStartName">
          <el-input v-model="form.placeStartName" placeholder="请输入起点名称"/>
        </el-form-item>
        <el-form-item label="终点名称" prop="placeEndName">
          <el-input v-model="form.placeEndName" placeholder="请输入终点名称"/>
        </el-form-item>
        <el-form-item label="起点桩号" prop="pileStart">
          <PileInput v-model="form.pileStart" @input="onPileChange" placeholder="请输入起点桩号"></PileInput>
        </el-form-item>
        <el-form-item label="终点桩号" prop="pileEnd">
          <PileInput v-model="form.pileEnd" @input="onPileChange" placeholder="请输入终点桩号"></PileInput>
        </el-form-item>
        <el-form-item label="路线长度(m)" prop="roadSectionLength">
          <el-input v-model="form.roadSectionLength" placeholder="请输入路线长度(m)"/>
        </el-form-item>
        <el-form-item label="路线性质" prop="routeType">
          <el-select v-model="form.routeType" placeholder="请选择路线性质" style="width: 100%">
            <el-option
              v-for="dict in dict.type.sys_route_nature"
              :key="dict.value"
              :label="dict.label"
              :value="parseInt(dict.value)"
            ></el-option>
          </el-select>
        </el-form-item>
<!--        <el-form-item label="抗震等级" prop="seismicGrade">-->
<!--          <dict-select type="side_slope_seismic_grade" v-model="form.seismicGrade"></dict-select>-->
<!--        </el-form-item>-->
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {listRoute, getRoute, delRoute, addRoute, updateRoute, routeCacheClear} from "@/api/system/route";
import PileInput from './pileinput.vue'

export default {
  name: "Road",
  dicts: ['sys_route_nature', 'side_slope_seismic_grade'],
  components: {PileInput},
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: false,
      // 总条数
      total: 0,
      // 路线管理表格数据
      routeList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 50,
        routeCode: null,
        routeName: null,
        placeStartName: null,
        placeEndName: null,
        pileStart: null,
        pileEnd: null,
        routeType: null,
        roadSectionLength: null,
        createdTime: null,
        updatedBy: null,
        updatedTime: null
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        routeCode: [
          {required: true, message: "路线编码不能为空", trigger: "blur"}
        ],
        routeName: [
          {required: true, message: "路线名称不能为空", trigger: "blur"}
        ],
        placeStartName: [
          {required: true, message: "起点名称不能为空", trigger: "blur"}
        ],
        placeEndName: [
          {required: true, message: "终点名称不能为空", trigger: "blur"}
        ],
        routeType: [
          {required: true, message: "路线性质不能为空", trigger: "change"}
        ],
        roadSectionLength1: [
          {type: 'number', message: '请输入数字', trigger: 'blur'}
        ]
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    onPileChange() {
      if (!isNaN(this.form.pileEnd) && !isNaN(this.form.pileStart))
        this.form.roadSectionLength = (this.form.pileEnd - this.form.pileStart).toFixed(2)
        this.form.roadSectionLength = Math.abs(this.form.roadSectionLength)
    },
    /** 查询路线管理列表 */
    getList() {
      this.loading = true;
      listRoute(this.queryParams).then(response => {
        this.routeList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        routeId: null,
        routeCode: null,
        routeName: null,
        placeStartName: null,
        placeEndName: null,
        pileStart: null,
        pileEnd: null,
        seismicGrade: null,
        routeType: null,
        roadSectionLength: null,
        createBy: null,
        createdTime: null,
        updatedBy: null,
        updatedTime: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.routeId)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加路线管理";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const routeId = row.routeId || this.ids
      getRoute(routeId).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改路线管理";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          if (this.form.routeId != null) {
            updateRoute(this.form).then(res => {
              if (res.code === 200) {
                this.$modal.msgSuccess("修改成功");
                this.open = false;
                this.getList();
                routeCacheClear()
              }
            });
          } else {
            addRoute(this.form).then(res => {
              if (res.code === 200) {
                this.$modal.msgSuccess("新增成功");
                this.open = false;
                this.getList();
              }
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const routeIds = row.routeId || this.ids;
      let str = row.routeCode?"编号为"+row.routeCode:"选中"
      this.$modal.confirm('是否确认删除' + str + '的数据？').then(function () {
      // this.$modal.confirm('是否确认删除路线管理 编码为:"' + row.routeCode + '"的数据项？').then(function () {
        return delRoute(routeIds);
      }).then((res) => {
        if (res.code === 200) {
          routeCacheClear()
          this.getList();
          this.$modal.msgSuccess("删除成功");
        }
      }).catch(() => {
      });
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('system/route/export', {
        ...this.queryParams
      }, `路线信息_${new Date().getTime()}.xlsx`)
    }
  }
};
</script>
<style scoped>
.app-container form:first-child .el-select,
.app-container form:nth-child(2) .el-select,
.app-container form:nth-child(2) ::v-deep .el-form-item__content,
.app-container form:first-child ::v-deep .el-form-item__content {
  width: 240px;
}
.app-container  form:first-child .el-form-item:last-child ::v-deep .el-form-item__content {
  width: auto;
}
.tableDiv {
  background-color: white;
  padding-bottom: 10px;
}

</style>
