<template>
  <div class="app-container">
    <el-row :gutter="20">

      <!--筛选区开始-->
      <el-col :span="24" :xs="24">
        <el-row>
          <el-col :span="24" >
            <el-form :model="queryParams" ref="queryForm" size="small" :inline="true"  label-width="68px">
                          <el-form-item label="" prop="departmentIdList">
                            <el-select v-model="queryParams.departmentIdList" filterable multiple collapse-tags placeholder="请选择管养单位"
                                       style="width: 240px" @change="deptChange">
                              <el-option
                                v-for="item in deptOptions"
                                :key="item.id"
                                :label="item.label"
                                :value="item.id">
                              </el-option>
                            </el-select>
                          </el-form-item>

                          <el-form-item label="" prop="rodaSectionsArr">
                            <el-select multiple  style="width: 100%;" clearable collapse-tags v-model="queryParams.rodaSectionsArr" >
                              <el-option v-for="item in rodaSectionList"
                                         :key="item.maintenanceSectionId"
                                         :label="item.maintenanceSectionName"
                                         :value="item.maintenanceSectionId"></el-option>
                            </el-select>
                          </el-form-item>

<!--                          <el-form-item label="来文时间" prop="submissionTime">-->
<!--                            <el-date-picker-->
<!--                              clearable-->
<!--                              v-model="queryParams.submissionTime"-->
<!--                              type="daterange"-->
<!--                              range-separator="至"-->
<!--                              start-placeholder="开始日期"-->
<!--                              end-placeholder="结束日期"-->
<!--                              value-format="yyyy-MM-dd"-->
<!--                              placeholder="请选择来文时间">-->
<!--                            </el-date-picker>-->
<!--                          </el-form-item>-->
                          <el-form-item label="检查时间" prop="oprTime">
                            <el-date-picker
                              v-model="dateRange"
                              style="width: 240px"
                              value-format="yyyy-MM-dd"
                              type="daterange"
                              range-separator="-"
                              start-placeholder="开始日期"
                              end-placeholder="结束日期"
                            ></el-date-picker>
                          </el-form-item>


                          <el-form-item label="" prop="totalWeight">
                            <el-input
                                v-model="queryParams.totalWeight"
                                placeholder="请输入总重"
                                clearable
                                prefix-icon="el-icon-user"
                                style="width: 240px"
                                @keyup.enter.native="handleQuery"
                            />
                          </el-form-item>
                          <el-form-item label="" prop="axleLoad">
                            <el-input
                                v-model="queryParams.axleLoad"
                                placeholder="请输入轴重"
                                clearable
                                prefix-icon="el-icon-user"
                                style="width: 240px"
                                @keyup.enter.native="handleQuery"
                            />
                          </el-form-item>
                          <el-form-item label="" prop="wheelBase">
                            <el-input
                                v-model="queryParams.wheelBase"
                                placeholder="请输入轴距"
                                clearable
                                prefix-icon="el-icon-user"
                                style="width: 240px"
                                @keyup.enter.native="handleQuery"
                            />
                          </el-form-item>
                          <el-form-item label="" prop="isPass">
<!--                            <el-input-->
<!--                                v-model="queryParams.isPass"-->
<!--                                placeholder="请输入是否同意通行"-->
<!--                                clearable-->
<!--                                prefix-icon="el-icon-user"-->
<!--                                style="width: 240px"-->
<!--                                @keyup.enter.native="handleQuery"-->
<!--                            />-->
                            <el-select
                              v-model="queryParams.isPass"
                              placeholder="请选择是否通行">
                              <el-option
                                v-for="dict in isPass"
                                :key="dict.dictValue"
                                :label="dict.dictLabel"
                                :value="dict.dictLabel"
                              ></el-option>
                            </el-select>
                          </el-form-item>
              <el-form-item>
                <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
                <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
                <el-button v-show="!showSearch" @click="showSearch=true" icon="el-icon-arrow-down" circle></el-button>
                <el-button  v-show="showSearch" @click="showSearch=false"  icon="el-icon-arrow-up" circle></el-button>
              </el-form-item>
            </el-form>
            <!--默认折叠-->
          </el-col>
        </el-row>
        <!--筛选区结束-->


        <!--操作按钮区开始-->
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button
                type="primary"
                icon="el-icon-plus"
                size="mini"
                @click="handleAdd"
                v-hasPermi="['other:transportation:add']"
            >新增</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button
                type="success"
                icon="el-icon-edit"
                size="mini"
                :disabled="single"
                @click="handleUpdate"
                v-hasPermi="['other:transportation:edit']"
            >修改</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button
                type="danger"
                icon="el-icon-delete"
                size="mini"
                :disabled="multiple"
                @click="handleDelete"
                v-hasPermi="['other:transportation:remove']"
            >删除</el-button>
          </el-col>
<!--          <el-col :span="1.5">-->
<!--            <el-button-->
<!--                type="info"-->
<!--                plain-->
<!--                icon="el-icon-upload2"-->
<!--                size="mini"-->
<!--                @click="handleImport"-->
<!--                v-hasPermi="['other:transportation:export']"-->
<!--            >导入</el-button>-->
<!--          </el-col>-->
<!--          <el-col :span="1.5">-->
<!--            <el-button-->
<!--                type="warning"-->
<!--                icon="el-icon-download"-->
<!--                size="mini"-->
<!--                @click="handleExport"-->
<!--                v-hasPermi="['system:user:export']"-->
<!--            >导出</el-button>-->
<!--          </el-col>-->

        </el-row>
        <!--操作按钮区结束-->

        <!--数据表格开始-->
        <div class="tableDiv">
          <el-table v-adjust-table  size="mini" :height="showSearch ? 'calc(100vh - 320px)' : 'calc(100vh - 260px)'"   style="width: 100%" v-loading="loading" border :data="transportationList" @selection-change="handleSelectionChange" >
            <el-table-column type="selection" width="50" align="center" />
            <el-table-column fixed label="序号" type="index" width="50" :index="indexMethod"></el-table-column>
                    <el-table-column label="项目名称" align="center" prop="projectName" width="200"/>
                    <el-table-column label="车辆货物信息" align="center" prop="vehicleCargoInfo" />
                    <el-table-column label="来文时间" align="center" prop="submissionTime" width="180">
                      <template slot-scope="scope">
                        <span>{{ parseTime(scope.row.submissionTime, '{y}-{m}-{d}') }}</span>
                      </template>
                    </el-table-column>
                    <el-table-column label="运输起点" align="center" prop="startingPoint" />
                    <el-table-column label="运输路段" align="center" prop="roadName" width="180" />
                    <el-table-column label="运输终点" align="center" prop="endingPoint" />
                    <el-table-column label="管理处" align="center" prop="deptName" width="180" />
                    <el-table-column label="总重" align="center" prop="totalWeight" />
                    <el-table-column label="轴重" align="center" prop="axleLoad" />
                    <el-table-column label="轴距" align="center" prop="wheelBase" />
                    <el-table-column
                      label="是否同意通行"
                      align="center"
                      prop="isPass">
                      <template slot-scope="scope">
                        <span>{{ isPassDict[scope.row.isPass] || '未知' }}</span>
                      </template>
                    </el-table-column>
                    <el-table-column label="回函文号" align="center" prop="replyNumber" width="180"/>
                    <el-table-column label="备注" align="center" prop="remarks" width="200"/>
            <el-table-column
                label="操作"
                fixed="right"
                align="center"
                width="230"
                class-name="small-padding fixed-width"
            >
              <template slot-scope="scope" v-if="scope.row.userId !== 1">
                <el-button
                    size="mini"
                    type="text"
                    icon="el-icon-edit"
                    @click="handleUpdate(scope.row)"
                    v-hasPermi="['other:transportation:edit']"
                >修改</el-button>
                <el-button
                    size="mini"
                    type="text"
                    icon="el-icon-delete"
                    @click="handleDelete(scope.row)"
                    v-hasPermi="['other:transportation:remove']"
                >删除</el-button>
                <el-button
                  size="mini"
                  type="text"
                  icon="el-icon-upload2"
                  @click="handleUploadClick(scope.row)">
                  上传附件
                </el-button>
              </template>
            </el-table-column>
          </el-table>

          <pagination
              v-show="total>0"
              :total="total"
              :page.sync="queryParams.pageNum"
              :limit.sync="queryParams.pageSize"
              @pagination="findUserDeptList"
          />
        </div>
        <!--数据表格结束-->
      </el-col>
    </el-row>

    <!-- 添加或修改大件运输对话框 -->
    <el-dialog :title="title" :visible.sync="open" :width="dialogWidth"   append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="150px">

                      <el-col :span="12">
                        <el-form-item label="项目名称" prop="projectName">
                          <el-input v-model="form.projectName" placeholder="请输入项目名称" />
                        </el-form-item>
                      </el-col>


                      <el-col :span="12">
                        <el-form-item label="车辆货物信息" prop="vehicleCargoInfo">
                          <el-input v-model="form.vehicleCargoInfo" placeholder="请输入车辆货物信息" />
                        </el-form-item>
                      </el-col>


                      <el-col :span="12">
                        <el-form-item label="来文时间" prop="submissionTime" >
                          <el-date-picker clearable
                                          v-model="form.submissionTime"
                                          type="date"
                                          width="100%"
                                          value-format="yyyy-MM-dd"
                                          style="width: 100%"
                                          placeholder="请选择来文时间">
                          </el-date-picker>
                        </el-form-item>
                      </el-col>


                      <el-col :span="12">
                        <el-form-item label="运输起点" prop="startingPoint">
                          <el-input v-model="form.startingPoint" placeholder="请输入运输起点" />
                        </el-form-item>
                      </el-col>

                      <el-col :span="12">
                        <el-form-item label="运输终点" prop="endingPoint">
                          <el-input v-model="form.endingPoint" placeholder="请输入运输终点" />
                        </el-form-item>
                      </el-col>

                      <el-col :span="12">
                        <el-form-item label="管理处" prop="deptIds">
                          <el-select v-model="form.deptIds" style="width: 100%;" filterable placeholder="请选择管养单位"
                                      @change="deptChange" clearable multiple>
                            <el-option
                              v-for="item in deptOptions"
                              :key="item.id"
                              :label="item.label"
                              :value="item.id">
                            </el-option>
                          </el-select>
                        </el-form-item>
                      </el-col>

                      <el-col :span="12">
                        <el-form-item label="运输路段" prop="maintenanceSectionIds">
                          <el-select multiple  style="width: 100%;" clearable  filterable   v-model="form.maintenanceSectionIds">
                            <el-option v-for="item in rodaSectionList"
                                       :key="item.maintenanceSectionId"
                                       :label="item.maintenanceSectionName"
                                       :value="item.maintenanceSectionId">
                            </el-option>
                          </el-select>
                        </el-form-item>
                      </el-col>



                      <el-col :span="12">
                        <el-form-item label="总重（吨）" prop="totalWeight">
                          <el-input v-model="form.totalWeight"
                                    type="number"
                                    :min="0"
                                    :step="0.01"
                                    placeholder="请输入总重" />
                        </el-form-item>
                      </el-col>


                      <el-col :span="12">
                        <el-form-item label="轴重（吨）" prop="axleLoad">
                          <el-input v-model="form.axleLoad"
                                    type="number"
                                    :min="0"
                                    :step="0.01"
                                    @input="validateAxleLoad"
                                    placeholder="请输入轴重" />
                        </el-form-item>
                      </el-col>


                      <el-col :span="12">
                        <el-form-item label="轴距（米）" prop="wheelBase">
                          <el-input v-model="form.wheelBase"
                                    type="number"
                                    :min="0"
                                    :step="0.01"
                                    placeholder="请输入轴距" />
                        </el-form-item>
                      </el-col>


                      <el-col :span="12">
                        <el-form-item label="是否同意通行" prop="isPass">
                          <el-select
                            v-model="form.isPass"
                            style="width: 100%"
                            @change="handleSelectChange"
                            placeholder="请选择是否通行">
                            <el-option
                              v-for="dict in isPass"
                              :key="dict.dictValue"
                              :label="dict.dictLabel"
                              :value="dict.dictValue"
                            ></el-option>
                          </el-select>
                        </el-form-item>
                      </el-col>


                      <el-col :span="12">
                        <el-form-item label="回函文号" prop="replyNumber">
                          <el-input v-model="form.replyNumber" placeholder="请输入回函文号" />
                        </el-form-item>
                      </el-col>


                      <el-col :span="12">
                        <el-form-item label="备注" prop="remarks">
                          <el-input v-model="form.remarks" placeholder="请输入备注" />
                        </el-form-item>
                      </el-col>



      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 用户导入对话框 -->
    <el-dialog :title="upload.title" :visible.sync="upload.open" width="400px" append-to-body>
      <el-upload
          ref="upload"
          :limit="1"
          accept=".xlsx, .xls"
          :headers="upload.headers"
          :action="upload.url + '?updateSupport=' + upload.updateSupport"
          :disabled="upload.isUploading"
          :on-progress="handleFileUploadProgress"
          :on-success="handleFileSuccess"
          :auto-upload="false"
          drag
      >
        <i class="el-icon-upload"></i>
        <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
        <div class="el-upload__tip text-center" slot="tip">
          <div class="el-upload__tip" slot="tip">
            <el-checkbox v-model="upload.updateSupport" /> 是否更新已经存在的用户数据
          </div>
          <span>仅允许导入xls、xlsx格式文件。</span>
          <el-link type="primary" :underline="false" style="font-size:12px;vertical-align: baseline;" @click="importTemplate">下载模板</el-link>
        </div>
      </el-upload>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitFileForm">确 定</el-button>
        <el-button @click="upload.open = false">取 消</el-button>
      </div>
    </el-dialog>

    <MissionDetail
      :visible.sync="showDetail"
      :data="currentRow"
      title="上传附件管理"
      :append-to-body="true"
      size="55%"
    />

  </div>

</template>

<script>
import {
  listTransportation,
  getTransportation,
  delTransportation,
  addTransportation,
  updateTransportation,
  addTransportationDept,
  addTransportationRoad,
  listTransportationAll,
  getTransportationRoad,
  getTransportationDept,
  deleteTransportationDeptAll,
  deleteTransportationRoadAll,
  findUserDept,
  findTransportationByDeptIDs,
  findUserTransportation
} from "@/api/other/transportation";
  import { getToken } from "@/utils/auth";
  import "@riophae/vue-treeselect/dist/vue-treeselect.css";
  import selectTreeCheckbox from '@/components/DeptTmpl/selectTreeCheckbox.vue'
  import deptTree from '@/components/DeptTmpl/deptTree.vue'
  import RouteCuring from "@/components/RouteCuring/index.vue";
  import TransportationSelection from "@/components/TransportationSelection/index.vue";
  import {getTreeStruct} from "@/api/tmpl";
  import {listMaintenanceSectionAll} from "@/api/system/maintenanceSection";
  import MissionDetail from "@/views/other/transportation/detail.vue";
  import RangeInput from "@/views/baseData/components/rangeInput/index.vue";
  import CascadeSelection from "@/components/CascadeSelection/index.vue";
  import "@riophae/vue-treeselect/dist/vue-treeselect.css";
  export default {
    name: "Transportation",
    components: {
      CascadeSelection,
      RangeInput,
      MissionDetail,
      deptTree,
      selectTreeCheckbox,
      RouteCuring,
      TransportationSelection},
    data() {
      return {
        //新增框宽度
        dialogWidth:"900px",
        //是否展示弹出
        showDetail: false,
        currentRow: {},
        //路段名称
        searchForm: {},
        // 遮罩层
        loading: true,
        // 选中数组
        ids: [],
        // 非单个禁用
        single: true,
        // 非多个禁用
        multiple: true,
        // 显示搜索条件
        showSearch: false,
        dictType:[],
        // 日期范围
        dateRange: [],
        // 总条数
        total: 0,
        //用户权限部门
        userDeptList: [],
        userDept:[],
        userTranslationIds: [],
        // 大件运输表格数据
        transportationList: null,
        isPassDict: {
          0: '是',
          1: '否'
        },
        //是否通过
        isPass:[
          { dictValue: '1', dictLabel: '是' },
          { dictValue: '0', dictLabel: '否' }
        ],
        //部门选择树
        deptOptions:[],
        //路段列表
        rodaSectionList:[],
        // 弹出层标题
        title: "",
        //管理处对象
        dept:[],
        //运输路段对象
        rodaSections:[],
        // 是否显示弹出层
        open: false,

        // 表单参数
        form: {},
        defaultProps: {
          children: "children",
          label: "label"
        },

        // 用户导入参数
        upload: {
          // 是否显示弹出层（用户导入）
          open: false,
          // 弹出层标题（用户导入）
          title: "",
          // 是否禁用上传
          isUploading: false,
          // 是否更新已经存在的用户数据
          updateSupport: 0,
          // 设置上传的请求头部
          headers: { Authorization: "Bearer " + getToken() },
          // 上传的地址
          url: process.env.VUE_APP_BASE_API + "/system/user/importData"
        },
        // 查询参数
        queryParams: {
          pageNum: 1,
          pageSize: 20,
          submissionTime: null,
          deptIds: null,
          totalWeight: null,
          axleLoad: null,
          userTranslationIds:[],
          wheelBase: null,
          isPass: null,
          rodaSections: null
        },
        rodaSectionsArr: [],
        // 列信息
        columns: [
        { key: 0, label: `项目名称`, visible: true },
        { key: 1, label: `车辆货物信息`, visible: true },
        { key: 2, label: `来文时间`, visible: true },
        { key: 3, label: `运输起点`, visible: true },
        { key: 4, label: `运输路段`, visible: true },
        { key: 5, label: `运输终点`, visible: true },
        { key: 6, label: `管理处`, visible: true },
        { key: 7, label: `总重`, visible: true },
        { key: 8, label: `轴重`, visible: true },
        { key: 9, label: `轴距`, visible: true },
        { key: 10, label: `是否同意通行`, visible: true },
        { key: 11, label: `回函文号`, visible: true },
        { key: 12, label: `备注`, visible: true },
        { key: 13, label: `状态`, visible: true },
        { key: 14, label: `删除标志`, visible: true }
        ],
        // 表单校验
        rules: {
    startingPoint: [
        { required: true, message: "运输起点不能为空", trigger: "blur" }
    ],
    rodaSection: [
        { required: true, message: "运输路段不能为空", trigger: "blur" }
    ],
    endingPoint: [
        { required: true, message: "运输终点不能为空", trigger: "blur" }
    ],
    deptIds: [
        { required: true, message: "管理处不能为空", trigger: "blur" }
    ],
        }
      };
    },
    watch: {
      // data: {
      //    handler(newData){
      //      console.log('MissionDetail received new data:', newData);
      //    }
      // }
    },
    mounted() {
      this.setDialogWidth();
      window.addEventListener('resize', this.setDialogWidth);
    },
    beforeDestroy() {
      window.removeEventListener('resize', this.setDialogWidth);
    },
    created() {
      this.findUserDeptList();
      // this.getList();
      this.getDeptTree();
    },
    methods: {
      indexMethod(index) {
        return (this.queryParams.pageNum - 1) * this.queryParams.pageSize + index + 1;
      },
      handleSelectChange(value) {
        // 通过 value (整数值) 来找到对应的字典项
        const selectedOption = this.isPass.find(dict => dict.dictValue === value);
        // 更新 form.isPass 为整数值
        this.form.isPass = selectedOption ? selectedOption.dictValue : null;
      },
      //上传附件
      handleUploadClick(row) {
        this.showDetail = true;
        this.currentRow = row;
        console.log('当前行数据:', this.currentRow);
      },
      //数字校验
      validateAxleLoad(value) {
        // 只允许输入有效数字
        const reg = /^[0-9]+(\.[0-9]{0,2})?$/; // 限制为最多两位小数
        if (!reg.test(value)) {
          this.form.axleLoad = value.slice(0, -1); // 删除最后一个非数字字符
        }
      },
      /** 查询用户列表 */
      getList() {
        this.loading = true;
        this.queryParams.submissionTimes = this.dateRange[0];
        this.queryParams.submissionTimee = this.dateRange[1];
        this.queryParams.userTranslationIds = this.userTranslationIds;
        if (this.queryParams.departmentIdList) {
          this.queryParams.deptIds = this.queryParams.departmentIdList.join(",");
        }
        this.queryParams.rodaSections =  this.rodaSectionsArr.map(item => `"${item}"`).join(",");
        console.log(this.queryParams)
        listTransportationAll(this.queryParams).then(response => {
          this.transportationList = response.rows;
          console.log(response)
          this.total = response.total;
          this.loading = false;
        });
      },
      getDeptTree() {
        return getTreeStruct({types:201}).then(response => {
          this.deptOptions = response.data;
        });
      },
      //获取用户权限
      findUserDeptList() {
        this.loading = true;
        findUserDept().then(data => {
          this.userDeptList = data.data;
          console.log(this.userDeptList)
          return findUserTransportation(this.userDeptList);
        }).then(response => {
          // 获取到跟当前用户有的translateID
          this.userTranslationIds = response.data;

          // 将 userTranslationIds 赋值给 queryParams.deptIds
          this.queryParams.userTranslationIds = this.userTranslationIds// 设置 deptIds

          console.log("queryParams: ", JSON.stringify(this.queryParams.userTranslationIds));
          // 确保在获取到 userTranslationIds 后再进行下一步
          return findTransportationByDeptIDs(this.queryParams);

        }).then(response => {
          this.transportationList = response.rows;
          this.total = response.total;
          this.loading = false;
        }).catch(error => {
          console.error("Error occurred: ", error);
        });
      },
      // 取消按钮
      cancel() {
        this.open = false;
        this.reset();
      },
      // 表单重置
      reset() {
        this.form = {
            projectName: null,
            vehicleCargoInfo: null,
            submissionTime: null,
            startingPoint: null,
            endingPoint: null,
            totalWeight: null,
            axleLoad: null,
            wheelBase: null,
            isPass: null,
            replyNumber: null,
            remarks: null,
            delFlag: null,
            maintenanceSectionIds:null,
            deptIds:null

        };
        this.dept = [];
        this.rodaSections = [];
        this.queryParams.departmentIdList = null;
        this.resetForm("form");
      },
      /** 搜索按钮操作 */
      handleQuery() {
        this.queryParams.pageNum = 1;
        // this.getList();
        this.findUserDeptList();
      },
      /** 重置按钮操作 */
      resetQuery() {
        this.dateRange = [];
        this.rodaSectionsArr = [],
        this.resetForm("queryForm");
        this.handleQuery();
      },
      // 多选框选中数据
      handleSelectionChange(selection) {
        this.ids = selection.map(item => item.id);
        this.single = selection.length != 1;
        this.multiple = !selection.length;
      },
      /** 新增按钮操作 */
      handleAdd() {
        this.reset();
        this.open = true;
        this.title = "添加大件运输";
      },
      // /** 修改按钮操作 */
      // handleUpdate(row) {
      //   this.reset();
      //   const id = row.id || this.ids;
      //   this.form.maintenanceSectionIds = [];
      //   this.form.deptIds = [];
      //   getTransportation(id).then(response => {
      //     this.form = response.data;
      //     getTransportationDept(id).then(response => {
      //       this.form.deptIds = response.data;
      //     })
      //     getTransportationRoad(id).then(response => {
      //       this.form.maintenanceSectionIds = response.data;
      //     })
      //     // this.getList();
      //     this.open = true;
      //     this.title = "修改大件运输";
      //   });
      //
      // },
      //
      handleUpdate(row) {
        this.reset();
        const id = row.id || this.ids;
        this.form.maintenanceSectionIds = [];
        this.form.deptIds = [];

        // 使用Promise.all并行处理所有API请求，并等待它们全部完成
        Promise.all([
          getTransportation(id),
          getTransportationDept(id),
          getTransportationRoad(id)
        ]).then(([transportationResponse, deptResponse, roadResponse]) => {
          // 按顺序更新表单数据
          this.form = transportationResponse.data;
          this.form.deptIds = deptResponse.data;
          this.form.maintenanceSectionIds = roadResponse.data;

          // 只有当所有数据都更新后才打开表单
          this.open = true;
          this.title = "修改大件运输";
        }).catch(error => {
          // 错误处理
          console.error("加载数据失败:", error);
          // 可以添加错误提示
        });
      },
      /** 提交按钮 */
      submitForm: function() {
        this.dept = [];
        this.rodaSections = [];

        // 填充 dept 数组
        this.form.deptIds.forEach(d => {
          this.deptOptions.forEach(i => {
            if (i.id === d) {
              this.dept.push({
                deptId: i.id,
                deptName: i.label
              });
            }
          });
        });

        // 填充 rodaSections 数组
        this.form.maintenanceSectionIds.forEach(d => {
          this.rodaSectionList.forEach(i => {
            if (i.maintenanceSectionId === d) {
              this.rodaSections.push({
                maintenanceSectionId: i.maintenanceSectionId,
                maintenanceSectionName: i.maintenanceSectionName
              });
            }
          });
        });

        this.form.dept = this.dept;
        this.form.rodaSections = this.rodaSections;
        this.transportationId = null;

        this.$refs["form"].validate(valid => {
          if (valid) {
            if (this.form.id != null) {
              updateTransportation(this.form).then(response => {
                this.$modal.msgSuccess("修改成功");

                // 获取更新后的 transportation 信息
                return getTransportation(this.form.id);
              }).then(response => {
                this.form = response.data;

                // 并行获取管理处和路段信息
                return Promise.all([
                  getTransportationDept(this.form.id), // 获取当前管理处信息
                  getTransportationRoad(this.form.id)  // 获取当前路段信息
                ]);
              }).then(([deptResponse, roadResponse]) => {
                // 填充管理处和路段信息
                this.form.deptIds = deptResponse.data;
                this.form.maintenanceSectionIds = roadResponse.data;

                // 删除所有相关的管理处记录

                return Promise.all([
                  deleteTransportationDeptAll(this.form.id).catch(() => {}),
                  deleteTransportationRoadAll(this.form.id).catch(() => {})
                ]);
              }).then(() => {
                // 删除完成后，进行新增操作
                let addDeptPromises = this.dept.map(dept => {
                  return addTransportationDept({
                    transportationId: this.form.id,
                    deptId: dept.deptId,
                    deptName: dept.deptName,
                  });
                });

                let addRoadPromises = this.rodaSections.map(road => {
                  return addTransportationRoad({
                    transportationId: this.form.id,
                    roadId: road.maintenanceSectionId,
                    roadName: road.maintenanceSectionName,
                  });
                });

                // 等待所有新增请求完成
                return Promise.all([addDeptPromises,addRoadPromises]);
              }).then(() => {
                this.open = false;
                this.reset();
                this.findUserDeptList();
              }).catch(error => {
                this.$modal.msgError("操作失败: " + error.message);
              });

            } else {
              addTransportation(this.form).then(response => {
                this.$modal.msgSuccess("新增成功");
                this.transportationId = response.data;

                // 循环处理每个 rodaSection 并调用 addTransportationRoad
                let roadPromises = this.rodaSections.map(road => {
                  return addTransportationRoad({
                    transportationId: this.transportationId,
                    roadId: road.maintenanceSectionId,
                    roadName: road.maintenanceSectionName,
                  });
                });

                // 等待所有 addTransportationRoad 请求完成
                return Promise.all(roadPromises);
              }).then(response => {
                this.$modal.msgSuccess("新增路段部门成功");

                // 循环处理每个 dept 并调用 addTransportationDept
                let deptPromises = this.dept.map(dept => {
                  return addTransportationDept({
                    transportationId: this.transportationId,
                    deptId: dept.deptId,
                    deptName: dept.deptName,
                  });
                });

                // 等待所有 addTransportationDept 请求完成
                return Promise.all(deptPromises);
              }).then(response => {
                this.$modal.msgSuccess("新增部门成功");
                this.open = false;
                this.reset();
                this.getList();
              }).catch(error => {
                console.error("操作失败: ", error);
                this.$modal.msgError("提交失败，请重试");
              });
            }
          }
        });
      },

      /** 删除按钮操作 */
      handleDelete(row) {
        const id = row.id || this.ids;
        this.$modal.confirm('是否确认删除大件运输编号为"' + id + '"的数据项？').then(function() {
          return delTransportation(id);
        }).then(() => {
          // this.getList();
          this.$modal.msgSuccess("删除成功");
        }).catch(() => {});
      },
      //部门改变
      deptChange() {
        let departmentIdList = this.queryParams.departmentIdList??this.form.deptIds
        console.log("departmentIdList:" ,departmentIdList)
        listMaintenanceSectionAll({departmentIdList: departmentIdList}).then(res => {
          this.rodaSectionList = res.data
        })
      },
      /** 导出按钮操作 */
      handleExport() {

    this.download('other/transportation/export', {
      ...this.queryParams
    }, `transportation_${new Date().getTime()}.xlsx`)

      },
      /** 导入按钮操作 */
      handleImport() {
        this.upload.title = "用户导入";
        this.upload.open = true;
      },
      /** 下载模板操作 */
      importTemplate() {
        this.download('system/user/importTemplate', {
        }, `user_template.xlsx`)
      },
      // 文件上传中处理
      handleFileUploadProgress(event, file, fileList) {
        this.upload.isUploading = true;
      },
      // 文件上传成功处理
      handleFileSuccess(response, file, fileList) {
        this.upload.open = false;
        this.upload.isUploading = false;
        this.$refs.upload.clearFiles();
        this.$alert("<div style='overflow: auto;overflow-x: hidden;max-height: 70vh;padding: 10px 20px 0;'>" + response.msg + "</div>", "导入结果", { dangerouslyUseHTMLString: true });
        // this.getList();
      },
      // 提交上传文件
      submitFileForm() {
        this.$refs.upload.submit();
      },
      //动态调整新增框宽度
      setDialogWidth() {
        const width = window.innerWidth;
        if (width < 768) {
          this.dialogWidth = "90vw"; // 小屏幕设备
        } else if (width < 1024) {
          this.dialogWidth = "80vw"; // 中等屏幕设备
        } else {
          this.dialogWidth = "70vw"; // 大屏幕设备
        }
      },
      // 表格点击勾选
      handleRowClick(row) {
        row.isSelected = !row.isSelected
        this.$refs.table.toggleRowSelection(row)
      },
      // 勾选高亮
      rowStyle({ row, rowIndex }) {
        if (this.ids.includes(row.id)) {
          return { 'background-color': '#b7daff', color: '#333' }
        } else {
          return { 'background-color': '#fff', color: '#333' }
        }
      }
  }
  };
</script>
<style>
  .hasTagsView .app-main[data-v-078753dd]{
    background: #f5f7fa;
  }

  .tableDiv{
    background-color: white;
    padding-bottom: 10px;
  }

  .uniform-width {
    width: 100%;
  }
</style>
