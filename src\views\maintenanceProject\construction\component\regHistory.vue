<template>
  <div id="chart" style="width: 100%;height: 700px">
  </div>
</template>

<script>
import * as echarts from "echarts";
import {getNodeInfoNew} from "@/api/maintenanceProject/taskList";

export default {
  props: {
    businessKey: {
      type: String,
      default: ''
    },
  },
  data() {
    return {
      chart: null,
      option: {
        tooltip: {
          trigger: 'item',
          triggerOn: 'mousemove',
          formatter: function (params) {
            const data = params.data;
            return `
        <div>
          <div>操作人：${data.assigneeName || '无'}</div>
          <div>操作时间：${data.endTime || '无'}</div>
          <div>操作意见：${data.comment || '无'}</div>
        </div>
      `;
          }
        },
        series: [
          {
            type: 'tree',
            data: [
              {
                name: '任务单编制',
                children: [
                  {
                    name: '任务单审核',
                    children: [
                      {
                        name: '任务单签发',
                        children: [
                          {
                            name: '编制签证',
                            children: [
                              {
                                name: '提交验收',
                                children: []
                              },
                              {
                                name: 'ws-001',
                                id: 'xxxx',
                                children: []
                              }
                            ]
                          },
                          {
                            name: 'ws-002',
                            children: []
                          }
                        ]
                      }
                    ]
                  }
                ]
              }
            ],
            top: '1%',
            left: '7%',
            bottom: '1%',
            right: '20%',
            symbolSize: 7,
            expandAndCollapse: false,
            initialTreeDepth: -1,
            label: {
              position: 'left',
              verticalAlign: 'middle',
              align: 'right',
              fontSize: 9
            },
            leaves: {
              label: {
                position: 'right',
                verticalAlign: 'middle',
                align: 'left'
              }
            },
            emphasis: {
              focus: 'descendant'
            },
            animationDuration: 550,
            animationDurationUpdate: 750
          }
        ]
      }
    }
  },
  mounted() {
    this.initChart()
  },
  methods: {
    initChart() {
      getNodeInfoNew({businessKey: this.businessKey}).then(async res => {
        if (res.code == 200 && res.data) {
          this.option.series[0].data = [await this.transformToTree(res.data)]
          console.log(this.option)
          this.chart = echarts.init(document.getElementById('chart'))
          this.chart.setOption(this.option)
          window.onresize = () => {
            this.chart.resize()
          }
        }
      })
    },
    async transformToTree(data) {
      // 处理 baseInfo 主流程
      const baseTree = this.buildTree(data.baseInfo);

      // 处理 childrenInfo 分支流程
      if (data.childrenInfo && data.childrenInfo.length > 0) {
        // 找到 baseInfo 的最后一个节点
        let lastNode = baseTree;
        while (lastNode.children && lastNode.children.length > 0) {
          lastNode = lastNode.children[0];
        }

        // 为最后一个节点添加 childrenInfo 分支
        lastNode.children = lastNode.children || [];
        data.childrenInfo.forEach(branch => {
          if (branch && branch.length > 0) {
            const branchTree = this.buildTree(branch);
            lastNode.children.push(branchTree);
          }
        });
      }

      return baseTree;
    },

// 辅助函数：将线性数组转换为树形结构
    buildTree(nodes) {
      if (!nodes || nodes.length === 0) return null;

      // 创建根节点
      const root = this.createNode(nodes[0]);
      let current = root;

      // 依次添加子节点
      for (let i = 1; i < nodes.length; i++) {
        const newNode = this.createNode(nodes[i]);
        current.children = [newNode];
        current = newNode;
      }

      return root;
    },

// 辅助函数：创建节点对象并保留所有原始字段
    createNode(source) {
      return {
        name: source.nodeName,
        id: source.id,
        nodeName: source.nodeName,
        approved: source.approved,
        assigneeName: source.assigneeName,
        assignee: source.assignee,
        startTime: source.startTime,
        endTime: source.endTime,
        comment: source.comment,
        taskId: source.taskId,
        direction: source.direction,
        userId: source.userId,
        fileList: source.fileList,
        methodList: source.methodList,
        isProduction: source.isProduction,
        isGuarantee: source.isGuarantee,
        content: source.content,
        children: [],
        itemStyle: {color: source.approved ? '#47cbfc' : '#f00'}
      };
    }
  }

}

</script>

<style lang="scss" scoped>

</style>
