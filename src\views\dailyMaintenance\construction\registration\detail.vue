<template>
  <div class="app-container">
    <el-form ref="elForm" :model="formData" :inline="true" label-width="180px" v-loading="loading">
      <el-row :gutter="20">
        <el-col :span="24" style="margin-bottom: 18px">
          <div class="card_title">事件信息</div>
          <el-card class="box-card" shadow="never">
            <el-descriptions :column="4" size="mini" border>
            <el-descriptions-item label="事件编码">
              {{disData.disCode}}
            </el-descriptions-item>
            <el-descriptions-item label="采集时间">
              {{disData.collectTime}}
            </el-descriptions-item>
            <el-descriptions-item label="起点桩号">
              {{formatPile(disData.beginMile)}}
            </el-descriptions-item>
            <el-descriptions-item label="终点桩号">
              {{formatPile(disData.endMile)}}
            </el-descriptions-item>
            <el-descriptions-item label="事件描述">
              {{disData.disDesc}}
            </el-descriptions-item>
            </el-descriptions>
          </el-card>
        </el-col>
        <el-col :span="24" style="margin-bottom: 18px">
          <div class="card_title">施工前采集照片</div>
          <el-card class="box-card" shadow="never">
            <el-form-item label="">
              <sort-file-upload v-model="sgqcjzp" can-sort ref="sgqcjzpRef" :fileType="['png', 'jpg', 'jpeg']"></sort-file-upload>
            </el-form-item>
          </el-card>
        </el-col>
        <el-col :span="12">
          <div class="card_title">施工简图</div>
          <el-card class="box-card" shadow="never">
            <el-form-item label="">
              <sort-file-upload v-model="sgjt" can-sort ref="sgjtRef" :fileType="['png', 'jpg', 'jpeg']"></sort-file-upload>
            </el-form-item>
          </el-card>
        </el-col>
        <el-col :span="12">
          <div class="card_title">施工附件</div>
          <el-card class="box-card" shadow="never">
            <el-form-item label="">
              <sort-file-upload v-model="sgfj" can-sort ref="sgfjRef"></sort-file-upload>
            </el-form-item>
          </el-card>
        </el-col>
        <el-col :span="24" style="margin-top: 18px">
          <div class="card_title" style="display: flex;width: 100%;justify-content: space-between">
            <div>事件方法数量</div>
            <div style="font-size: 16px">总金额：{{total}}<el-button v-if="!fromEvent" class="ml10" icon="el-icon-plus" circle @click="openLibModel"></el-button></div>
          </div>
          <el-table v-adjust-table
              :data="methodList"
              border
              row-key="id"
              height="200px"
              ref="tableRef"
              style="width: 100%">
            <el-table-column
                label="序号"
                align="center"
                type="index"
                width="50"
            />
            <el-table-column
                prop="schemeCode"
                align="center"
                label="子目号"
                width="100">
            </el-table-column>
            <el-table-column
                prop="schemeName"
                align="center"
                label="子目名称"
                width="100">
              <template slot-scope="scope">
                <el-input v-if="scope.row.rateFlag == 1" v-model="scope.row.schemeName">
                </el-input>
                <div v-else>{{scope.row.schemeName}}</div>
              </template>
            </el-table-column>
            <el-table-column
                prop="unit"
                align="center"
                label="单位"
                width="100">
              <template slot-scope="scope">
                <el-input v-if="scope.row.rateFlag == 1" v-model="scope.row.unit">
                </el-input>
                <div v-else>{{scope.row.unit}}</div>
              </template>
            </el-table-column>
            <el-table-column
                prop="price"
                align="center"
                label="单价"
                width="100">
              <template slot-scope="scope">
                <el-input v-if="scope.row.rateFlag == 1" v-model="scope.row.price">
                </el-input>
                <div v-else>{{scope.row.price}}</div>
              </template>
            </el-table-column>
            <el-table-column
                prop="calcDesc"
                align="center"
                label="计算式"
                width="100">
              <template slot-scope="scope">
                <el-input v-model="scope.row.calcDesc" @change="changeCalculation(scope.row)" :disabled="scope.row.schemeName == '安全生产费'||scope.row.schemeName == '安全保通费' || fromEvent">
                </el-input>
              </template>
            </el-table-column>
            <el-table-column
                prop="num"
                align="center"
                label="方法数量"
                width="100">
              <template slot-scope="scope">
                <el-input v-model="scope.row.num" @change="changeSchemeNum(scope.row)" :disabled="scope.row.schemeName == '安全生产费'||scope.row.schemeName == '安全保通费' || fromEvent">
                </el-input>
              </template>
            </el-table-column>
            <el-table-column
                prop="amount"
                align="center"
                label="资金"
                width="100">
            </el-table-column>
            <el-table-column
                prop="remark"
                align="center"
                label="备注"
                width="100">
              <template slot-scope="scope">
                <el-input v-model="scope.row.remark" :disabled="fromEvent">
                </el-input>
              </template>
            </el-table-column>
            <el-table-column
                prop="field101"
                align="center"
                label="移除">
              <template slot-scope="scope">
                <el-button
                    size="mini"
                    type="text"
                    :disabled="scope.row.schemeName == '安全生产费'||scope.row.schemeName == '安全保通费' || fromEvent"
                    @click="handleDelete(scope.row)"
                >移除
                </el-button
                >
              </template>
            </el-table-column>
          </el-table>
        </el-col>
        <el-col :span="24" style="margin-top: 18px">
          <div class="card_title" style="display: flex;width: 100%;justify-content: space-between"><div>计算式及说明</div><el-link type="primary" @click="generateInstructions">生成计算式说明</el-link></div>
          <el-input class="calculation_desc" v-model="formData.calculationDesc" type="textarea" :rows="4" :disabled="fromEvent">
          </el-input>
        </el-col>
        <el-col :span="24" style="margin-top: 18px">
          <div class="card_title">登记备注</div>
          <el-input v-model="formData.remark" type="textarea" :rows="4" :disabled="fromEvent">
          </el-input>
        </el-col>
      </el-row>
      <div style="text-align: right;padding-right: 7.5px;margin-top: 18px">
        <el-button type="primary" v-has-permi="['register:construction:save']" @click="onSave">保 存</el-button>
        <el-button type="success" v-has-permi="['register:construction:review']" @click="onSubmit" v-if="!fromEvent">提 交</el-button>
        <el-button @click="onClose">退出</el-button>
      </div>
    </el-form>
    <methods-tree :key="rowData.conConId" scheme-type="日常养护" :con-id="rowData.conConId" ref="methodsRef" @input="checkLib" :domain-id="rowData.domainId"></methods-tree>
  </div>
</template>
<script>
import {getTreeData} from "@/api/contract/quotationSystem"
import {getDiseaseDataById} from "@/api/dailyMaintenance/eventManage/eventData"
import {getRegisterDetail, save, review} from "@/api/dailyMaintenance/construction/registration"
import sortFileUpload from "@/components/SortFileUpload/index.vue";
import MethodsTree from "@/components/MethodsTree/index.vue";
import { v4 as uuidv4 } from 'uuid';
import {getSafetyFeeList} from "@/api/contract/quotationSystem";
import {getDetail as getContractInfo} from "@/api/contract/info";
import {formatPile} from "../../../../utils/ruoyi";
import { Decimal } from 'decimal.js';

export default {
  name: "index",
  components: {MethodsTree, sortFileUpload},
  data() {
    return {
      formData: {
        calculationDesc: ''
      },
      sgqcjzp: '',
      sgjt: '',
      sgfj: '',
      total: 0,
      loading: false,
      methodList: [],
      contractInfo: {},
      disData: {}
    }
  },
  props: {
    rowData: {
      type: Object,
      default: () => {
        return {}
      }
    },
    fromEvent: {
      type: Boolean,
      default: false
    }
  },
  watch: {
    rowData: {
      handler(val) {
        if (val.id) {
          this.getDetail(val.id)
        }
      },
      immediate: true
    },
    filterText(val) {
      this.$refs.tree.filter(val);
    }
  },
  mounted() {
    // 查询合同信息
    // this.loading = true
    // getContractInfo(this.rowData.conConId).then(res => {
    //   this.contractInfo = res.rows[0]
    //   this.loading = false
    // })
  },
  methods: {
    formatPile,
    getDetail(id) {
      getRegisterDetail(id).then(res => {
        const detail = res.rows[0]
        this.formData = JSON.parse(JSON.stringify(detail))
        this.formData.taskId = this.rowData.taskId
        this.formData.detailId = this.rowData.detailId
        this.methodList = detail.methodList
        this.methodList.forEach(item => {
          if ((item.amount == null || item.amount == undefined) && typeof item.price === 'number' && typeof item.num === 'number') item.amount = Math.round(item.price * item.num)
        })
        getContractInfo(detail.conConId).then(res => {
          this.contractInfo = res.rows[0]
          this.loading = false
          this.calculate()
        })
        getDiseaseDataById(detail.disId).then(res => {
          this.disData = res.data
        })
        const sgqcjzp = detail.attList.filter(item => item.registerType == 0)
        this.sgqcjzp = sgqcjzp.map(item => item.fileId)

        const sgjt = detail.attList.filter(item => item.registerType == 1)
        this.sgjt = sgjt.map(item => item.fileId)

        const sgfj = detail.attList.filter(item => item.registerType == 2)
        this.sgfj = sgfj.map(item => item.fileId)
        if (this.methodList.some(item => item.schemeName == '安全生产费' || item.schemeName == '安全保通费')) {
          this.calculate()
          return
        }
        // 初始化添加安全费保通费
        getSafetyFeeList(detail.conConId).then(res => {
          // 过滤出schemeType为日常养护的，并且对schemeName去重
          const filteredData = res.data.filter(item => item.schemeType === '日常养护').reduce((acc, item) => {
            if (!acc.find(accItem => accItem.schemeName === item.schemeName)) {
              acc.push(item);
            }
            return acc;
          }, []).sort((a, b) => a.schemeName.localeCompare(b.schemeName));
          for (let i = 0; i < filteredData.length; i++) {
            let item = filteredData[i]
            this.methodList.splice(0, 0, {
              id: item.id,
              schemeId: item.id,
              schemeCode: item.schemeCode,
              schemeName: item.schemeName,
              unit: item.unit,
              price: item.price,
              priceRate: item.priceRate,
              decimalPlaces: item.decimalPlaces,
              safetyFeeFlag: item.safetyFeeFlag
            })
          }
          this.calculate()
        })
      })
    },
    onSave() {
      try {
        this.generateParams()
      } catch (e) {
        console.error('Error occurred while generating params:', e)
        return;
      }
      if (!this.sgqcjzp || this.sgqcjzp.length == 0) {
        this.$message.warning('请上传施工前采集照片')
        return
      }
      this.loading = true
      save(this.formData).then(res => {
        this.$message.success('保存成功')
        this.onClose()
      }).finally(() => {
        this.loading = false
      })
    },
    onSubmit() {
      try {
        this.generateParams()
      } catch (e) {
        console.error('Error occurred while generating params:', e)
        return;
      }
      if (!this.sgqcjzp || this.sgqcjzp.length == 0) {
        this.$message.warning('请上传施工前检查照片')
        return
      }
      this.loading = true
      // this.formData.daliyId = this.formData.conId
      review(this.formData).then(res => {
        this.$message.success('提交成功')
        this.onClose()
      }).finally(() => {
        this.loading = false
      })
    },
    // 拼接参数
    generateParams() {
      this.$refs.sgqcjzpRef.save()
      this.$refs.sgjtRef.save()
      this.$refs.sgfjRef.save()
      // this.methodList = this.methodList.map(item => ({
      //   ... item,
      //   schemeId: item.id
      // }))
      // 拼接参数
      this.formData.methodList = this.methodList
      const attList = []
      if (this.sgqcjzp) {
        this.sgqcjzp.forEach((item,index) => {
          attList.push({
            fileId: item,
            indexOrder: index,
            registerType: 0,
            remark: '施工登记'
          })
        })
      }
      if (this.sgjt) {
        this.sgjt.forEach((item,index) => {
          attList.push({
            fileId: item,
            indexOrder: index,
            registerType: 1,
            remark: '施工登记'
          })
        })
      }
      if (this.sgfj) {
        this.sgfj.forEach((item,index) => {
          attList.push({
            fileId: item,
            indexOrder: index,
            registerType: 2,
            remark: '施工登记'
          })
        })
      }
      this.formData.attList = attList
    },
    checkLib(checkDatas) {
      const filteredCheckDatas = checkDatas.filter(item => item.nodeType === 2 && (item.rateFlag === 1 || !this.methodList.some(filterItem => filterItem.schemeCode === item.schemeCode)));
      const newCheckDatas = filteredCheckDatas.map(item => ({
        ...item,
        schemeId: item.id,
        id: uuidv4().replace(/-/g, '').slice(0, 20),
      }));
      this.methodList.push(...newCheckDatas);
    },
    openLibModel() {
      this.$refs.methodsRef.openLibModel()
    },
    filterNode(value, data) {
      if (!value) return true;
      return data.schemeName.indexOf(value) !== -1;
    },
    handleDelete(e) {
      this.methodList = this.methodList.filter(item => {
        return item.id != e.id
      })
      this.calculate()
    },
    async changeCalculation(row) {
      if (!this.isValidMathFormula(row.calcDesc)) {
        this.$modal.msgError('计算式错误，请检查')
        return
      }
      let num =  this.math.evaluate(row.calcDesc) || 0
      this.$set(row, 'num', this.ceilToTwo(num, row.decimalPlaces))
      await this.changeSchemeNum(row)
    },
    async changeSchemeNum(row) {
      let money = new Decimal(row.num || 0).times(row.price || 0).toNumber()
      this.$set(row, 'amount', Math.round(money))
      if (row.schemeName != '安全生产费' && row.schemeName != '安全保通费') {
        this.calculate()
      }
      this.total = await this.methodList.reduce((acc, curr) => Number(acc) + Number(curr.amount || 0), 0)
    },
    onClose() {
      this.$emit("close")
    },
    // 生成计算式说明
    generateInstructions() {
      let calculationDesc = ''
      this.methodList.forEach(item => {
        if (item.schemeName != '安全生产费' && item.schemeName != '安全保通费')
        calculationDesc += `${item.schemeName || ''}:(${item.calcDesc || item.num || ''})=${item.num || 0}${item.unit || ''}\n`
      })
      this.$set(this.formData, 'calculationDesc', calculationDesc)
    },
    calculate () {
      let lib1 = this.methodList.find(item => item.schemeName == '安全生产费')
      if (lib1) {
        this.$set(lib1, 'amount', 0)
        // 计算安全费和保通费
        let aqf = 0
        for (let i = 0; i < this.methodList.length; i++) {
          let item = this.methodList[i]
          if( item.safetyFeeFlag == '1' && item.schemeName != '安全生产费' && item.schemeName != '安全保通费') {
            aqf += (lib1.amount || 0) + (item.amount || 0)
          }
        }
        if(this.contractInfo.dSafeProductionRate) {
          this.$set(lib1, 'amount', 0)
          this.$set(lib1, 'amount', Math.round(aqf * this.contractInfo.dSafeProductionRate))
        }
      }

      let lib2 = this.methodList.find(item => item.schemeName == '安全保通费')
      if (lib2) {
        this.$set(lib2, 'amount', 0)
        let aqf = 0
        for (let i = 0; i < this.methodList.length; i++) {
          let item = this.methodList[i]
          if( item.safetyFeeFlag == '1' && item.schemeName != '安全生产费' && item.schemeName != '安全保通费') {
            aqf += (lib2.amount || 0) + (item.amount || 0)
          }
        }
        if(this.contractInfo.dSafeGuaranteeRate) {
          this.$set(lib2, 'amount', 0)
          this.$set(lib2, 'amount', Math.round(aqf * this.contractInfo.dSafeGuaranteeRate))
        }
      }
      this.total = this.methodList.reduce((acc, curr) => Number(acc) + Number(curr.amount || 0), 0)
    },
    generateUUID() {
      return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
        var r = crypto.getRandomValues(new Uint8Array(1))[0] % 16 | 0;
        var v = c === 'x' ? r : (r & 0x3 | 0x8);
        return v.toString(16);
      }).slice(0, 20); // 截取前 20 位
    }
  }
}
</script>
<style scoped lang="scss">
.card_title {
  width: 200px;
  text-align: left;
  margin-bottom: 15px;
  font-weight: bold;
}
</style>
<style lang="scss" scoped>
@import "@/assets/styles/business.scss";
</style>
