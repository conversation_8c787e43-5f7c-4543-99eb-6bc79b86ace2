<template>
  <div class="app-container maindiv">
    <el-row :gutter="20">
      <!--筛选区开始-->
      <el-col :span="24" :xs="24">
        <el-row>
          <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" label-width="68px">
            <el-form-item label="" prop="field4">
              <el-input
                  v-model="queryParams.field4"
                  placeholder="物资类型"
                  clearable
                  style="width: 240px"
              >
              </el-input>
            </el-form-item>
            <el-form-item label="" prop="field4">
              <el-input
                  v-model="queryParams.field4"
                  placeholder="物资名称"
                  clearable
                  style="width: 240px"
              >
              </el-input>
            </el-form-item>
            <el-form-item label="" prop="field4">
              <el-input
                  v-model="queryParams.电话"
                  placeholder="物资编码"
                  clearable
                  style="width: 240px"
              >
              </el-input>
            </el-form-item>
            <el-form-item label="" prop="field4">
              <el-input
                  v-model="queryParams.field4"
                  placeholder="物资状态"
                  clearable
                  style="width: 240px"
              >
              </el-input>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
              <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
            </el-form-item>
          </el-form>
        </el-row>
        <!--筛选区结束-->
        <!--数据表格开始-->
        <div class="tableDiv">
          <el-table v-adjust-table stripe size="mini" height="200px"
                    style="width: 100%" v-loading="loading" border :data="dataList"
                    @selection-change="handleSelectionChange">
            <el-table-column type="selection" width="50" align="center"/>
            <el-table-column fixed label="序号" type="index" width="50"></el-table-column>
            <template v-for="column in columns">
              <el-table-column :label="column.label"
                               v-if="column.visible"
                               align="center"
                               :prop="column.field"
                               :width="column.width">
                <template slot-scope="scope">
                  <dict-tag v-if="column.dict" :options="dict.type[column.dict]" :value="scope.row.type"/>
                  <span v-else-if="column.isTime">{{ parseTime(scope.row[column.field], '{y}-{m}-{d}') }}</span>
                    <span v-else>{{ scope.row[column.field] }}</span>
                </template>
              </el-table-column>
            </template>
          </el-table>
          <pagination
              v-show="total>0"
              :total="total"
              :page.sync="queryParams.pageNum"
              :limit.sync="queryParams.pageSize"
              @pagination="getList"
          />
        </div>
        <!--数据表格结束-->
      </el-col>
    </el-row>
  </div>
</template>

<script>

export default {
  name: "materialInfo",
  components: {},
  data() {
    return {
      showSearch: false,
      loading: false,
      queryParams: {
        field1: undefined,
        field2: undefined,
        field3: undefined,
        field4: undefined,
        field5: undefined,
        field6: undefined,
        field7: undefined,
        field8: undefined,
        field9: undefined,
        pageNum: 1,
        pageSize: 3,
      },
      // 列信息
      columns: [
        {key: 0, width: 100, field: 'field1', label: `所属单位`, visible: true},
        {key: 1, width: 100, field: 'field2', label: `物资类型`, visible: true},
        {key: 2, width: 100, field: 'field3', label: `物资名称`, visible: true},
        {key: 3, width: 100, field: 'field4', label: `物资编码`, visible: true},
        {key: 4, width: 100, field: 'field5', label: `物资状态`, visible: true},
        {key: 5, width: 100, field: 'field6', label: `规格型号`, visible: true},
        {key: 6, width: 100, field: 'field7', label: `单位`, visible: true},
        {key: 7, width: 100, field: 'field8', label: `库存数量`, visible: true},
        {key: 8, width: 100, field: 'field9', label: `存放地点`, visible: true},
        {key: 9, width: 100, field: 'field10', label: `负责人`, visible: true},
        {key: 10, width: 100, field: 'field9', label: `负责人电话`, visible: true},
        {key: 11, width: 100, field: 'field10', label: `备注`, visible: true},
      ],
      // 表格数据
      dataList: [
      ],
      // 总条数
      total: 0,
      // 选中数组
      ids: [],
      detailTitle: '查看上报事件',
      openDetail: false,

    };
  },
  watch: {},
  created() {

  },
  methods: {
    // 多选框选中数据
    handleSelectionChange() {
      this.ids = selection.map(item => item.field1);
    },
    // 恢复
    handleRecover() {
    }
  }
};
</script>
<style>
</style>
<style lang="scss" scoped>
@import "@/assets/styles/business.scss";
</style>
