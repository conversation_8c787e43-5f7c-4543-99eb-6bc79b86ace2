<template>
  <div>
    <div class="title">风险点位置</div>
      <div class="card-body">
        <template>
          <span class="upper-left"></span>
          <span class="upper-right"></span>
          <span class="lower-left"></span>
          <span class="lower-right"></span>
        </template>
      <div class="map-view" id="earlyMap" ref="mapRef" ></div>
    </div>
  </div>
</template>
<script>
import 'ol/ol.css'
import { Map, View, Feature } from 'ol'
import TileLayer from 'ol/layer/Tile'
import XYZ from 'ol/source/XYZ'
import { fromLonLat } from 'ol/proj'
import TileWMS from 'ol/source/TileWMS'
import VectorLayer from 'ol/layer/Vector'
import VectorSource from 'ol/source/Vector'
import { defaults as defaultsControl } from 'ol/control'
import { Circle as CircleStyle, RegularShape, Fill, Stroke, Style, Icon, Text } from 'ol/style'
import { Point } from 'ol/geom'
import { getShapeList } from "@/api/oneMap/deptInfo";
import {
  addMapMask,
} from "@/views/map/components/common/mapFun";
let map = null
const key = 'cde0b56cf882626889981701109a7536'
let layer1 = new TileLayer({
	name: '天地图矢量图层',
	source: new XYZ({
		url: `http://t{0-7}.tianditu.gov.cn/DataServer?T=img_w&x={x}&y={y}&l={z}&tk=${key}`,
		attributions: '天地图的属性描述',
		wrapX: false,
		maxZoom: 18,
	}),
	preload: Infinity,
	visible: true,
})
let layer3 = new TileLayer({
	source: new TileWMS({
		ratio: 1,
		url: 'https://gis.glyhgl.com/server/services/yanghu/gis_route_shape4/MapServer/WMSServer',
		params: {
			FORMAT: 'image/png', // 图像格式，如image/png
			LAYERS: '0', // 图层名称
			TILED: true, // 使用切片
			VERSION: '1.3.0', // WMS版本号，默认1.1.1
			STYLES: '',
		},
		serverType: 'geoserver',
		visible: true,
	}),
})
const pointLayer = new VectorLayer({
	source: new VectorSource(), // 空的源，用于动态添加点
})
const blinkingPointLayer = new VectorLayer({
  source: new VectorSource(),
  zIndex: 999 // 确保在最上层
});
let layers = [layer1, layer3, pointLayer, blinkingPointLayer]

export default {
	data() {
		return {
      blinkIntervalList: []
    }
	},
  props: {
    isBig: {
      type: Boolean,
      default: false
    },
  },
	mounted() {
		this.initMap()
	},
	methods: {
		initMap() {
      if (map) {
        map.setTarget(null); // 移除地图容器
        // 销毁图层和源等资源
        map.getLayers().forEach(layer => layer.getSource().clear());
        map = null; // 清除地图变量引用
      }
			// 创建地图
			map = new Map({
				target: 'earlyMap', // HTML 元素的 ID，地图将在该元素中渲染
				layers,
				view: new View({
					projection: 'EPSG:3857',
					center: fromLonLat([102.0007599, 23.7085797]), // 地图中心点坐标
					zoom: this.isBig ? 8 : 8, // 缩放级别
          wrapX: false,
          crossOrigin: "anonymous",
					maxZoom: 18,
				}),
				controls: defaultsControl({
					attribution: false,
					zoom: false,
				}).extend([]),
			})
      // 添加点击事件监听
      map.on('click', (evt) => {
        const feature = map.forEachFeatureAtPixel(
          evt.pixel,
          (feature) => feature
        );

        if (feature) {
          this.handlePointClick(feature, evt.coordinate);
        }
      });
      this.getRangeShape()
		},
    handlePointClick(feature, coordinate) {
      console.log(feature.getProperties())
      this.$emit('point-click', feature.getProperties().id)
    },
    focusOnLocation(lonLat, zoomLevel = 18) {
      if (!map) {
        return;
      }
      const center = fromLonLat(lonLat);
      const view = map.getView();
      view.animate({
        center: center,
        zoom: zoomLevel,
        duration: 500
      });
    },
    // 在类中新增方法
    clearAllPoints() {
      const source = pointLayer.getSource();
      source.clear()
      blinkingPointLayer.getSource().clear();
      this.blinkIntervalList.forEach(item => {
        if (item) {
          clearInterval(item);
        }
      })
    },

    addPoint(type, lonLat, textContent = '', id) {
      // 创建标注点要素
      const pointFeature = new Feature({
        geometry: new Point(lonLat),
      });
      // 颜色配置参数
      const colors = ['#fe4646', '#42ABFF', '#32da49']; // 红/绿/蓝三色
      let colorIndex = type;
      const blinkSpeed = 100; // 闪烁速度（毫秒）
      const _this = this
      function createHaloStyle() {
        let radius = _this.isBig ? 26 :6
        let haloRadius = _this.isBig ? 48 : 14
        return [
          // 光晕层
          new Style({
            image: new CircleStyle({
              radius: haloRadius,
              fill: new Fill({
                color: colors[colorIndex] + '66',
              }),
            }),
          }),
          // 核心闪烁层
          new Style({
            image: new CircleStyle({
              radius: radius,
              fill: new Fill({
                color: colors[colorIndex] + 'CC', // 添加透明度（CC=80%）
              }),
              stroke: new Stroke({
                color: colors[colorIndex],
                width: 5,
              }),
            }),
            text: new Text({
              text: textContent,
              font: _this.isBig ? '48px sans-serif' : '16px sans-serif',
              fill: new Fill({
                color: '#ffffff' // 文字颜色
              }),
              stroke: new Stroke({
                color: '#000000', // 文字描边颜色
                width: 2
              }),
              offsetY: _this.isBig ? 80 : 20, // 文字在Y轴上的偏移量（向下移动）
              className: 'line-text' // 添加自定义class
            })
          })
        ];
      }

      // 设置初始样式
      pointFeature.setStyle(createHaloStyle());
      pointFeature.setProperties(
        {
          id: id
        }
      )

      if (type == 2) {
        // 启动颜色切换定时器
        const blinkInterval = setInterval(() => {
          colorIndex = (colorIndex + 1) % colors.length;
          pointFeature.setStyle(createHaloStyle());
        }, blinkSpeed);
        this.blinkIntervalList.push(blinkInterval)
        // 销毁时清除定时器（重要！）
        pointFeature.on('change', function(e) {
          if (e.target.getGeometry() === null) {
            clearInterval(blinkInterval);
          }
        });
      }
      // 将新点添加到图层
      if (type === 2) {
        blinkingPointLayer.getSource().addFeature(pointFeature);
        // ...（保持原有闪烁逻辑）...
      } else {
        pointLayer.getSource().addFeature(pointFeature);
      }
    },

    getRangeShape() {
      getShapeList({ sysDeptIds: [] }).then(res=> {
        if (res.code == 200) {
          addMapMask(map, res.data);
        }
      })
    }
	},
  beforeDestroy() {
    this.clearAllPoints()
  }
}
</script>

<style scoped lang="scss">
@import "@/assets/styles/utils.scss";

.map-view {
	width: 100%;
	height: 100%;
}
.title {
  height: vwpx(97px);
  line-height: vwpx(97px);
  margin: vwpx(40px) 0; // 20 * 2
  padding-left: vwpx(80px);
  background-image: url("~@/assets/earlyWarning/title-bg-r.png");
  background-size: 100% 100%;
  background-position: center;
  color: #fff;
  font-size: vwpx(60px); // 30 * 2
}
.line-text {
  z-index: 99999;
}
.card-body {
  width: 100%;
  background-color: rgba(4, 37, 72, 0.3);
  position: relative;
  border: 1px solid rgba(0, 166, 255, 0.5);
  height: 89.5%;
  .upper-left {
    position: absolute;
    top: vwpx(-2px);
    left: vwpx(-2px);
    width: vwpx(30px);
    height: vwpx(30px);
    background-image: url('~@/assets/cockpit/card-box.png');
    background-repeat: no-repeat;
    background-size: 100% 100%;
    transform: rotate(180deg);
  }

  .upper-right {
    position: absolute;
    top: vwpx(-2px);
    right: vwpx(-2px);
    width: vwpx(30px);
    height: vwpx(30px);
    background-image: url('~@/assets/cockpit/card-box.png');
    background-repeat: no-repeat;
    background-size: 100% 100%;
    transform: rotate(270deg);
  }

  .lower-left {
    position: absolute;
    bottom: vwpx(-2px);
    left: vwpx(-2px);
    width: vwpx(30px);
    height: vwpx(30px);
    background-image: url('~@/assets/cockpit/card-box.png');
    background-repeat: no-repeat;
    background-size: 100% 100%;
    transform: rotate(90deg);
  }

  .lower-right {
    position: absolute;
    bottom: vwpx(-2px);
    right: vwpx(-2px);
    width: vwpx(30px);
    height: vwpx(30px);
    background-image: url('~@/assets/cockpit/card-box.png');
    background-repeat: no-repeat;
    background-size: 100% 100%;
    transform: rotate(0deg);
  }
}
</style>
