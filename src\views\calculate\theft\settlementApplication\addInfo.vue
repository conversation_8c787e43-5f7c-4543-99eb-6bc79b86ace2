<template>
  <div class="road-interflow-edit">
    <el-row :gutter="15">
      <el-col :span="24">
        <el-form
          ref="queryForm"
          :model="queryParams"
          size="mini"
          :inline="true"
          label-width="68px"
        >
          <el-form-item>
            <el-input
              style="width: 190px"
              placeholder="中间计量名称"
              v-model="queryParams.name"
            ></el-input>
          </el-form-item>
          <el-form-item>
            <el-input
              style="width: 190px"
              placeholder="中间计量编号"
              v-model="queryParams.code"
            ></el-input>
          </el-form-item>
          <el-form-item>
            <el-input
              style="width: 190px"
              placeholder="施工单编号"
              v-model="queryParams.constructionCode"
            ></el-input>
          </el-form-item>
          <el-form-item>
            <el-button
              icon="el-icon-search"
              size="mini"
              type="primary"
              @click="handleQuery"
              >搜索</el-button
            >
            <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
              >重置</el-button
            >
          </el-form-item>
        </el-form>
      </el-col>
      <el-col :span="24">
        <div class="draggable">
          <el-table v-adjust-table
            size="mini"
            style="width: 100%"
            v-loading="loading"
            border
            :data="tableData"
            row-key="id"
            height="600"
            ref="dataTable"
            stripe
            highlight-current-row
            @row-click="handleClickRow"
            @selection-change="handleSelectionChange"
            @expand-change="expandChange"
          >
            <el-table-column type="selection" width="50" align="center" />
            <el-table-column
              label="序号"
              align="center"
              type="index"
              width="50"
            />
            <template v-for="(column, index) in columns">
              <el-table-column
                :label="column.label"
                v-if="column.visible"
                align="center"
                :prop="column.field"
              >
                <template slot-scope="scope">
                  <dict-tag
                    v-if="column.dict"
                    :options="dict.type[column.dict]"
                    :value="scope.row[column.field]"
                  />
                  <template v-else-if="column.slots">
                    <RenderDom
                      :row="scope.row"
                      :index="index"
                      :render="column.render"
                    />
                  </template>
                  <span v-else-if="column.isTime">{{
                    parseTime(scope.row[column.field], "{y}-{m}-{d}")
                  }}</span>
                  <span v-else>{{ scope.row[column.field] }}</span>
                </template>
              </el-table-column>
            </template>
            <el-table-column
              align="center"
              class-name="small-padding fixed-width"
              fixed="right"
              label="操作"
              width="200"
            >
              <template slot-scope="scope">
                <el-button
                  icon="el-icon-view"
                  size="mini"
                  type="text"
                  @click="handleOpenTaskDialog(scope.row)"
                  >任务清单
                </el-button>
                <el-button
                  icon="el-icon-view"
                  size="mini"
                  type="text"
                  @click="handleOpenMethodDialog(scope.row)"
                  >报价清单
                </el-button>
              </template>
            </el-table-column>
          </el-table>
          <pagination
            v-show="total > 0"
            :total="total"
            :page.sync="queryParams.pageNum"
            :limit.sync="queryParams.pageSize"
            @pagination="handleQuery"
          />
        </div>
      </el-col>
      <el-col
        :span="24"
        style="text-align: right; padding-right: 7.5px; margin-top: 18px"
      >
        <el-button type="primary" @click="onSave">保 存</el-button>
        <el-button @click="onClose">退 出</el-button>
      </el-col>
    </el-row>
    <el-dialog
      v-if="taskListDialog"
      :visible.sync="taskListDialog"
      append-to-body
      modal-append-to-body
      title="任务清单"
      width="90%"
    >
      <el-table v-adjust-table :data="taskList">
        <template v-for="(column, index) in taskListColumns">
          <el-table-column
            :label="column.label"
            v-if="column.visible"
            align="center"
            :prop="column.field"
            :width="column.width"
          >
            <template slot-scope="scope">
              <dict-tag
                v-if="column.dict"
                :options="dict.type[column.dict]"
                :value="scope.row[column.field]"
              />
              <template v-else-if="column.slots">
                <RenderDom
                  :row="scope.row"
                  :index="index"
                  :render="column.render"
                />
              </template>
              <span v-else-if="column.isTime">{{
                parseTime(scope.row[column.field], "{y}-{m}-{d}")
              }}</span>
              <span v-else>{{ scope.row[column.field] }}</span>
            </template>
          </el-table-column>
        </template>
      </el-table>
      <pagination
        v-show="taskListTotal > 0"
        :total="taskListTotal"
        :page.sync="taskListDialogParams.pageNum"
        :limit.sync="taskListDialogParams.pageSize"
        @pagination="getTaskList"
      />
    </el-dialog>
    <el-dialog
      v-if="methodDialog"
      :visible.sync="methodDialog"
      append-to-body
      modal-append-to-body
      title="报价清单"
      width="90%"
    >
      <el-table v-adjust-table :data="methodList">
        <template v-for="(column, index) in methodColumns">
          <el-table-column
            :label="column.label"
            v-if="column.visible"
            align="center"
            :prop="column.field"
            :width="column.width"
          >
            <template slot-scope="scope">
              <dict-tag
                v-if="column.dict"
                :options="dict.type[column.dict]"
                :value="scope.row[column.field]"
              />
              <template v-else-if="column.slots">
                <RenderDom
                  :row="scope.row"
                  :index="index"
                  :render="column.render"
                />
              </template>
              <span v-else-if="column.isTime">{{
                parseTime(scope.row[column.field], "{y}-{m}-{d}")
              }}</span>
              <span v-else>{{ scope.row[column.field] }}</span>
            </template>
          </el-table-column>
        </template>
      </el-table>
      <pagination
        v-show="methodTotal > 0"
        :total="methodTotal"
        :page.sync="methodDialogParams.pageNum"
        :limit.sync="methodDialogParams.pageSize"
        @pagination="getMethodList"
      />
    </el-dialog>
  </div>
</template>
<script>
import {
  listEvent,
  listMethod,
} from "@/api/calculate/theft/middleApplication";
import {
  listBySid,
  addMiddle,
} from "@/api/calculate/theft/settlementApplication";
export default {
  dicts: ["bridge_simple_bool","project_clac_middle_status","testing_calc_status"],
  data() {
    return {
      columns: [
        {
          key: 0,
          width: 100,
          field: "name",
          label: `中间计量名称`,
          visible: true,
        },
        {
          key: 1,
          width: 100,
          field: "code",
          label: `中间计量编号`,
          visible: true,
        },
        {
          key: 2,
          width: 100,
          field: "domainName",
          label: `管养单位`,
          visible: true,
        },
        {
          key: 3,
          width: 100,
          field: "calcDomainName",
          label: `申请计量单位`,
          visible: true,
        },
        {
          key: 4,
          width: 100,
          field: "maiSecId",
          label: `路段名称`,
          visible: true,
        },
        {
          key: 5,
          width: 100,
          field: "conName",
          label: `合同名称`,
          visible: true,
        },
        {
          key: 6,
          width: 100,
          field: "calcFund",
          label: `核定计量金额`,
          visible: true,
        },
        {
          key: 8,
          width: 100,
          field: "sumFund",
          label: `基本费用`,
          visible: true,
        },
        {
          key: 9,
          width: 100,
          field: "productionFund",
          label: `安全生产费`,
          visible: true,
        },
        {
          key: 10,
          width: 100,
          field: "guaranteeFund",
          label: `安全保通费`,
          visible: true,
        },
        {
          key: 11,
          width: 100,
          field: "supFund",
          label: `监理费`,
          visible: true,
        },

      ],
      queryParams: {
        pageNum: 1,
        pageSize: 50,
      },
      total: 0,
      selectIds: [],
      taskListDialog: false,
      taskListDialogParams: {
        pageNum: 1,
        pageSize: 10,
      },
      taskList: [],
      taskListColumns: [
        {
          key: 0,
          width: 100,
          field: "constructionCode",
          label: `任务单编号`,
          visible: true,
        },
        {
          key: 1,
          field: "constructionName",
          label: `任务单名称`,
          visible: true,
        },
        {
          key: 0,
          width: 100,
          field: "status",
          label: `状态`,
          visible: true,
          dict: "project_clac_middle_status",
        },
        {
          key: 1,
          width: 100,
          field: "calcStatus",
          label: `计量情况`,
          visible: true,
          dict: "testing_calc_status",
        },
        {
          key: 4,
          width: 100,
          field: "productionFund",
          label: `金额`,
          visible: true,
        },
        {
          key: 5,
          width: 100,
          field: "residueFund",
          label: `剩余金额`,
          visible: true,
        },
        {
          key: 2,
          field: "projectName",
          label: `项目名称`,
          visible: true,
        },
        {
          key: 3,
          field: "maiSecName",
          label: `路段名称`,
          visible: true,
        },
        {
          key: 4,
          field: "domainName",
          label: `管养单位`,
          visible: true,
        },
        {
          key: 9,
          width: 100,
          field: "productionFund",
          label: `安全生产费`,
          visible: true,
        },
        {
          key: 10,
          width: 100,
          field: "guaranteeFund",
          label: `安全保通费`,
          visible: true,
        },
        {
          key: 11,
          width: 100,
          field: "supFund",
          label: `监理费`,
          visible: true,
        },

        {
          key: 10,
          width: 100,
          field: "isGuarantee",
          label: `是否计算安全保通费`,
          visible: true,
          dict: 'bridge_simple_bool'
        },
        {
          key: 11,
          width: 100,
          field: "isProduction",
          label: `是否计算安全生产费`,
          visible: true,
          dict: 'bridge_simple_bool'
        },
        {
          key: 12,
          width: 100,
          field: "conDomainName",
          label: `施工单位`,
          visible: true,
        },
        {
          key: 12,
          width: 100,
          field: "supDomainName",
          label: `监理单位`,
          visible: true,
        }
      ],
      taskListTotal: 0,
      methodDialog: false,
      methodDialogParams: {
        pageNum: 1,
        pageSize: 10,
      },
      methodList: [],
      methodColumns: [
        { key: 0, field: "schemeCode", label: `子目号`, visible: true },
        { key: 1, field: "schemeName", label: `子目名称`, visible: true },
        { key: 2, field: "price", label: `单价`, visible: true },
        { key: 3, field: "calcDesc", label: `计算式`, visible: true },
        { key: 4, field: "num", label: `数量`, visible: true },
        { key: 5, field: "amount", label: `金额`, visible: true },
        { key: 5, field: "remark", label: `备注`, visible: true },
      ],
      methodTotal: 0,
    };
  },
  props: {
    settleId: {
      type: String,
      default: "",
    },
  },
  mounted() {
    this.handleQuery();
  },
  created() {},
  methods: {
    handleQuery() {
      this.loading = true;
      this.queryParams.settleId = this.settleId;
      listBySid(this.queryParams).then((res) => {
        this.tableData = res.rows;
        this.total = res.total;
        this.loading = false;
      });
    },
    // 选中
    handleSelectionChange(e) {
      this.selectIds = e;
    },
    expandChange(row, expanded) {
      console.log(row, expanded);
    },
    handleClickRow(e) {
      e.isSelected = !e.isSelected;
      this.$refs.dataTable.toggleRowSelection(e);
    },
    handleOpenTaskDialog(row) {
      this.taskListDialogParams.calcId = row.id;
      this.getTaskList();
    },
    getTaskList() {
      listEvent(this.taskListDialogParams).then((res) => {
        this.taskList = res.rows;
        this.taskListTotal = res.total;
        this.taskListDialog = true;
      });
    },
    handleOpenMethodDialog(row) {
      this.methodDialogParams.calcId = row.id;
      this.getMethodList();
    },
    getMethodList() {
      listMethod(this.methodDialogParams).then((res) => {
        this.methodList = res.rows;
        this.methodTotal = res.total;
        this.methodDialog = true;
      });
    },
    onSave() {
      // for (let i = 0; i < this.selectIds.length; i++) {
      //   this.selectIds[i].calcId = this.selectIds[i].id;
      //   this.selectIds[i].settleId = this.settleId;
      // }
      const submitData = this.selectIds.map((item) => {
        return {
          calcId: item.id,
          settleId: this.settleId,
        };
      });
      addMiddle(submitData).then(() => {
        this.$modal.msgSuccess("保存成功");
        this.onClose();
      });
    },
    resetQuery() {
      this.queryParams = {
        pageNum: 1,
        pageSize: 50,
      };
      this.handleQuery()
    },
    onClose() {
      this.$emit("close");
    },
  },
};
</script>
<style scoped lang="scss"></style>
<style lang="scss" scoped>
@import "@/assets/styles/business.scss";
</style>
