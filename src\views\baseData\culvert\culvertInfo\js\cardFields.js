const fields1 = [
  {
    label: '路线编码',
    prop: 'routeCode',
    type: 'description',
  },
  {
    label: '路线名称',
    prop: 'routeName',
    type: 'description',
  },
  {
    label: '路线等级',
    prop: 'routeLevel',
    type: 'dict',
    dict: 'sys_route_grade'
  },
  {
    label: '中心桩号',
    prop: 'stake',
    type: 'stake',
  },
  {
    label: '功能类型',
    prop: 'functionType',
    type: 'input',
    // dict: 'bridge_function_type'
  },
  {
    label: '结构形式',
    prop: 'structType',
    type: 'input',
    // dict: 'bridge_main_superstructure_type'
  },
  {
    label: '设计荷载',
    prop: 'designLoad',
    type: 'dict',
    dict: 'sys_design_load'
  },
  {
    label: '管养单位',
    prop: 'managementMaintenanceName',
    type: 'description',
  },
  {
    label: '建成时间',
    prop: 'buildDate',
    type: 'date',
  },
]
const fields2 = [
  {
    label: '涵身长度(米)',
    prop: 'culvertLength',
    type: 'input',
  },
  {
    label: '孔径(米)',
    prop: 'aperture',
    type: 'input',
  },
  {
    label: '净高(米)',
    prop: 'culvertHeight',
    type: 'input',
  },
  {
    label: '进口形式',
    prop: 'inletPortForm',
    type: 'input',
  },
  {
    label: '出口形式',
    prop: 'exitForm',
    type: 'input',
  },
  {
    label: '基础形式',
    prop: 'basicsForm',
    type: 'input',
  },
  {
    label: '涵底纵坡',
    prop: 'culvertBottomLongitudinalRamp',
    type: 'input',
  },
  {
    label: '涵底铺砌',
    prop: 'culvertBottomPave',
    type: 'input',
  },
  {
    label: '填土高度(米)',
    prop: 'fillHeight',
    type: 'input',
  },
  {
    label: '路面宽度(米)',
    prop: 'pavementWidth',
    type: 'input',
  },
  {
    label: '路基宽度(米)',
    prop: 'subgradeWidth',
    type: 'input',
  },
  {
    label: '路面类型',
    prop: 'pavementType',
    type: 'input',
  },
]
const fields3 = [
  {
    label: '设计图纸',
    prop: 'designDrawingComplete',
    type: 'dict',
    dict: 'base_archival_data'
  },
  {
    label: '设计文件',
    prop: 'designDocumentComplete',
    type: 'dict',
    dict: 'base_archival_data'
  },
  {
    label: '施工文件',
    prop: 'constructionDocumentsComplete',
    type: 'dict',
    dict: 'base_archival_data'
  },
  {
    label: '竣工图纸',
    prop: 'completionDocumentComplete',
    type: 'dict',
    dict: 'base_archival_data'
  },
  {
    label: '验收文件',
    prop: 'acceptanceDocumentComplete',
    type: 'dict',
    dict: 'base_archival_data'
  },
  {
    label: '定期检查报告',
    prop: 'periodicInspectionReportComplete',
    type: 'dict',
    dict: 'base_archival_data'
  },
  {
    label: '特别检查报告',
    prop: 'particularlyReportComplete',
    type: 'dict',
    dict: 'base_archival_data'
  },
  {
    label: '专项检查报告',
    prop: 'specialInspectionReportComplete',
    type: 'dict',
    dict: 'base_archival_data'
  },
  {
    label: '历次维修资料',
    prop: 'previousMaintenanceComplete',
    type: 'dict',
    dict: 'base_archival_data'
  },
  {
    label: '档案号',
    prop: 'fileNumberComplete',
    type: 'input',
  },
  {
    label: '存档案',
    prop: 'saveArchivesComplete',
    type: 'dict',
    dict: 'base_archival_data'
  },
  {
    label: '建档时间',
    prop: 'filingTimeComplete',
    type: 'date',
  },
]
const fields6 = [
    // {
    //   label: '进洞照片',
    //   prop: 'entranceHoleImageId',
    //   type: 'image',
    // },
    // {
    //   label: '出洞照片',
    //   prop: 'exitHoleImageId',
    //   type: 'image',
    // },
  {
    label: '涵洞工程师',
    prop: 'designr',
    type: 'input',
  },
  {
    label: '填卡人',
    prop: 'reportWriter',
    type: 'input',
  },
  {
    label: '填卡日期',
    prop: 'reportWriteDate',
    type: 'date',
  },

]
export default {
  fields1,
  fields2,
  fields3,
  fields6
}
