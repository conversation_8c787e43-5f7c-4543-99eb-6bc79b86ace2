<template>
  <div class="app-container maindiv">
    <el-row :gutter="20">
      <!--部门数据-->
      <el-col :span="relaNav ? 5 : 0" :xs="24" class="leftDiv">
        <!--折叠图标-->
        <div class="leftIcon" @click="relaNav = false">
          <span class="el-icon-caret-left"></span>
        </div>
        <div class="head-container">
          <el-input
              v-model="keyword"
              placeholder="输入关键词检索"
              @change="handleSearch"
              clearable
              size="small"
              prefix-icon="el-icon-search"
              style="margin-bottom: 20px"
          />
        </div>
        <div class="left-total">共 {{ leftTotal }} 条</div>
        <div class="head-container" style="width: 300px">
          <el-tree
              :data="filteredTreeData"
              :expand-on-click-node="false"
              :filter-node-method="filterNode"
              :default-expanded-keys="[1]"
              ref="tree"
              node-key="id"
              highlight-current
              @node-click="handleNodeClick"
          >
          </el-tree>
        </div>
      </el-col>
      <!--角色数据-->
      <el-col :span="relaNav ? 19 : 24" :xs="24">
        <!--展开图标-->
        <div class="rightIcon" @click="relaNav = true" v-show="!relaNav">
          <span class="el-icon-caret-right"></span>
        </div>
        <el-row>
          <el-col :span="24" :xs="24">
            <el-row>
              <el-form
                  ref="queryForm"
                  :model="queryParams"
                  size="mini"
                  :inline="true"
                  label-width="68px"
              >

                <el-form-item>
                  <selectTree
                      :key="'domainId'"
                      style="width: 240px"
                      v-model="queryParams.domainIdStr"
                      :deptType="100" :deptTypeList="[1, 3, 4]"
                      placeholder="管养单位"
                      clearable
                      filterable
                  />
                </el-form-item>
                <el-form-item>
                  <selectTree
                      :key="'constructionUnit'"
                      style="width: 240px"
                      v-model="queryParams.constructionUnit"
                      :deptType="100" :deptTypeList="[1, 3, 4]"
                      placeholder="己方单位"
                      clearable
                      filterable
                  />
                </el-form-item>
                <el-form-item>
                  <el-input style="width: 240px" placeholder="路段" v-model="queryParams.maiSecId"></el-input>
                </el-form-item>
                <el-form-item>
                  <el-input style="width: 240px" placeholder="附件上传状态" v-model="queryParams.routeCode"></el-input>
                </el-form-item>
                <el-form-item>
                  <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
                  <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
                </el-form-item>
              </el-form>
              <!--默认折叠-->
            </el-row>
          </el-col>
        </el-row>
        <!--操作按钮区开始-->
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button
                type="warning"
                icon="el-icon-upload"
                size="mini"
                @click="exportList"
            >上传
            </el-button
            >
          </el-col>
          <el-col :span="1.5">
            <el-button
                type="primary"
                icon="el-icon-plus"
                size="mini"
            >提交
            </el-button
            >
          </el-col>
          <el-col :span="1.5">
            <el-button
                type="danger"
                icon="el-icon-delete"
                size="mini"
            >撤回
            </el-button
            >
          </el-col>
          <el-col :span="1.5">
            <el-button
                type="warning"
                icon="el-icon-view"
                size="mini"
            >预览
            </el-button
            >
          </el-col>
          <right-toolbar
              :showSearch.sync="showSearch"
              @queryTable="handleQuery"
              :columns="columns"
          ></right-toolbar>
        </el-row>
        <el-row>
          <div class="draggable">
            <el-table v-adjust-table
                size="mini"
                style="width: 100%"
                v-loading="loading"
                border
                :data="tableData"
                row-key="id"
                ref="dataTable"
                stripe
                highlight-current-row
                :height="
                showSearch ? 'calc(100vh - 330px)' : 'calc(100vh - 270px)'
              "
            >
              <el-table-column type="selection" width="50" align="center"/>
              <el-table-column
                  label="序号"
                  align="center"
                  type="index"
                  width="50"
              />
              <template v-for="(column,index) in columns">
                <el-table-column :label="column.label"
                                 v-if="column.visible"
                                 align="center"
                                 :prop="column.field"
                                 :width="column.width">
                  <template slot-scope="scope">
                    <dict-tag v-if="column.dict" :options="dict.type[column.dict]" :value="scope.row[column.field]"/>
                    <template v-else-if="column.slots">
                      <RenderDom :row="scope.row" :index="index" :render="column.render"/>
                    </template>
                    <span v-else-if="column.isTime">{{ parseTime(scope.row[column.field], '{y}-{m}-{d}') }}</span>
                    <span v-else>{{ scope.row[column.field] }}</span>
                  </template>
                </el-table-column>
              </template>
            </el-table>
            <pagination
                v-show="total>0"
                :total="total"
                :page.sync="queryParams.pageNum"
                :limit.sync="queryParams.pageSize"
                @pagination="handleQuery"
            />
          </div>
        </el-row>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import EventInfo from "../../component/eventTreeInfo.vue";
import selectTree from "@/components/DeptTmpl/selectTree.vue";
export default {
  name: 'PushFee',
  components: {
    selectTree,
    EventInfo,
    RenderDom: {
      functional: true,
      props: {
        row: Object,
        index: Number,
        render: Function
      },
      render(createElement, ctx) {
        const {row, index} = ctx.props
        return ctx.props.render(row, index)
      }
    }
  },
  props: [],
  data() {
    return {
      leftTotal: 1,
      showSearch: false,
      queryParams: {},
      total: 0,
      loading: false,
      columns: [
        {key: 0, width: 100, field: 'field0', label: `状态`, visible: true},
        {key: 1, width: 100, field: 'field1', label: `附件`, visible: true},
        {key: 2, width: 100, field: 'field2', label: `期数`, visible: true},
        {key: 3, width: 100, field: 'field3', label: `结算计量名称`, visible: true},
        {key: 4, width: 100, field: 'field4', label: `结算计量编号`, visible: true},
        {key: 5, width: 100, field: 'field5', label: `管养单位`, visible: true},
        {key: 6, width: 100, field: 'field6', label: `申请计量单位`, visible: true},
        {key: 7, width: 100, field: 'field7', label: `路段名称`, visible: true},
        {key: 8, width: 100, field: 'field8', label: `合同编号`, visible: true},
        {key: 9, width: 100, field: 'field9', label: `核定计量金额`, visible: true},
        {key: 10, width: 100, field: 'field10', label: `基本费用`, visible: true},
        {key: 11, width: 100, field: 'field11', label: `安全生产费`, visible: true},
        {key: 12, width: 100, field: 'field12', label: `安全保通费`, visible: true},
        {key: 13, width: 100, field: 'field13', label: `调整费用(元)`, visible: true},
        {key: 14, width: 100, field: 'field14', label: `监理费`, visible: true},
        {key: 15, width: 100, field: 'field15', label: `材料调差费用`, visible: true},
        {key: 16, width: 100, field: 'field16', label: `扣款金额`, visible: true},
        {key: 17, width: 100, field: 'field17', label: `上一期名称`, visible: true},
        {key: 18, width: 100, field: 'field18', label: `上一期编号`, visible: true},
        {key: 19, width: 100, field: 'field19', label: `计量日期`, visible: true},
        {key: 20, width: 100, field: 'field20', label: `抽取状态`, visible: true},
      ],
      tableData: [
        {dataType: '1'}
      ],
      rowData: {},
      // 左侧组织树
      relaNav: true,
      keyword: '',
      relaOptions: [],
      filteredTreeData: [],
    }
  },
  computed: {},
  watch: {},
  created() {
    this.getDeptTree()
  },
  mounted() { },
  methods: {
    // 关键词检索
    handleSearch() {
      const keyword = this.keyword.toLowerCase();
      this.filteredTreeData = this.relaOptions.filter(node => this.filterNode(node, keyword));
    },
    // 筛选节点
    filterNode(node, keyword) {
      if (node.label.indexOf(keyword) != -1) {
        return true;
      }
      if (node.children) {
        return node.children.some(childNode => this.filterNode(childNode, keyword));
      }
      return false;
    },
    // 查询部门下拉树结构
    getDeptTree() {
      // getUserTreeStructure({}).then(response => {
      //   this.relaOptions = response.rows
      //   this.filteredTreeData = [...this.relaOptions]
      // });
    },
    handleNodeClick(e) {
      this.handleQuery()
    },
    handleQuery() {
      // this.loading = true
    },
    resetQuery() {
      this.queryParams = {
        pageNum: 1,
        pageSize: 50
      }
      this.handleQuery()
    },
    handleCloseDetail() {
      this.rowData = {}
      this.drawer = false
      this.handleQuery()
    },
    handleEdit(row) {
      this.rowData = row
      this.drawer = true
    },
    handleUpdate(row) {
      this.rowData = row
      this.updateDialog = true
    },
    // 导出清单按钮
    exportList() {
      this.download(
          'manager/disease/export',
          {...this.queryParams},
          `disease_${new Date().getTime()}.xlsx`,
        {
          headers: { 'Content-Type': 'application/json;' },
          parameterType: 'body'
        }
      )
    },
  }
}
</script>

<style lang="scss" scoped>
.leftDiv {
  border-right: 1px solid #d8dce5;
  min-height: calc(100vh - 100px);
  overflow-y: auto;
  height: calc(80vh - 150px);
  position: relative;
  top: -20px;
  padding-top: 10px;
  background-color: white;
}

.leftIcon {
  border: 1px solid #dcdfe6;
  border-radius: 8px;
  width: 16px;
  height: 50px;
  line-height: 50px;
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
  position: absolute;
  right: 0;
  top: 300px;
  z-index: 2;
}

.leftIcon:hover {
  background-color: #dcdfe6;
}

.rightIcon {
  border: 1px solid #dcdfe6;
  border-radius: 8px;
  width: 16px;
  height: 50px;
  line-height: 50px;
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
  position: absolute;
  left: -10px;
  top: 280px;
  z-index: 10;
  background: white;
}

.rightIcon:hover {
  background-color: #dcdfe6;
}

.left-total {
  font-size: 13px;
  line-height: 28px;
  color: #606266;
  position: absolute;
  bottom: 10px;
  right: 10px;
}
</style>
<style lang="scss" scoped>
@import "@/assets/styles/business.scss";
</style>
