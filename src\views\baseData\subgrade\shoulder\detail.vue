<template>
  <div v-loading="loading" :class="oneMap ? 'one-map' : ''">
    <el-form
      ref="form"
      :model="form"
      :rules="rules"
      label-width="120px"
      :disabled="true"
    >
      <div style="display: flex; flex-wrap: wrap; margin-bottom: 20px">
        <el-divider content-position="left">基础数据</el-divider>
        <ManageSelectTree placeholder="请选择" :formObject="form" />

        <el-col :span="12">
          <el-form-item label="养护路段" prop="maintenanceSectionId">
            <el-select
              v-model="form.maintenanceSectionId"
              style="width: 100%"
              placeholder="请选择"
              clearable
              :disabled="!form.managementMaintenanceBranchId"
            >
              <el-option
                v-for="item in routeOptions"
                :key="item.maintenanceSectionId"
                :label="item.maintenanceSectionName"
                :value="item.maintenanceSectionId"
              />
            </el-select>
          </el-form-item>
        </el-col>

        <el-col :span="12">
          <el-form-item label="路段类型" prop="sectionType">
            <SectionSelect
              v-model="form.sectionType"
              :style="'width: 100%;' + (forView ? 'pointer-events: none' : '')"
              :formObject="form"
              :sectionId="form.maintenanceSectionId"
              :disabled="!form.maintenanceSectionId"
              clearable
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="路线编码" prop="routeCode">
            <el-select
              v-model="form.routeCode"
              placeholder="请选择"
              :disabled="!form.maintenanceSectionId"
              clearable
              style="width: 100%"
              @change="
                (val) => {
                  handleSelect(val);
                }
              "
            >
              <el-option
                v-for="(item, index) in routeList"
                :key="index"
                :label="item.routeCode"
                :value="item.routeCode"
              >
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="路线名称" prop="routeName">
            <el-select
              v-model="form.routeName"
              style="width: 100%"
              placeholder="请选择"
              :disabled="true"
              clearable
            >
              <el-option
                v-for="item in []"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="路线技术等级" prop="routeLevel">
            <MultiDictSelect
              v-model="form.routeLevel"
              :disabled="''"
              :multiple="true"
              :options="dict.type['sys_route_grade']"
            />
          </el-form-item>
        </el-col>

        <el-col :span="12">
          <el-form-item label="路肩编码" prop="roadbedCode">
            <el-input
              v-model="form.roadbedCode"
              style="width: 100%"
              placeholder="请选择"
              clearable
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="方向" prop="direction">
            <el-select
              v-model="form.direction"
              style="width: 100%"
              placeholder="请选择"
              clearable
            >
              <el-option
                v-for="i in dict.type.sys_route_direction"
                :key="i.value"
                :label="i.label"
                :value="i.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="左右" prop="leftOrRight">
            <el-select
              v-model="form.leftOrRight"
              style="width: 100%"
              placeholder="请选择"
              clearable
            >
              <el-option
                v-for="i in dict.type.left_right"
                :key="i.value"
                :label="i.label"
                :value="i.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="位置" prop="lane">
            <el-select
              v-model="form.lane"
              style="width: 100%"
              placeholder="请选择"
              clearable
            >
              <el-option
                v-for="i in dict.type.lane"
                :key="i.value"
                :label="i.label"
                :value="i.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="经纬度" prop="longitude">
            <lon-lat
              type="lonlat"
              :lon.sync="form.longitude"
              :lat.sync="form.latitude"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="起点桩号" prop="startStake">
            <PileInput v-model="form.startStake" placeholder="请输入" />
          </el-form-item>
        </el-col>

        <el-col :span="12">
          <el-form-item label="终点桩号" prop="endStake">
            <PileInput v-model="form.endStake" placeholder="请输入" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="建成时间" prop="buildDate">
            <el-date-picker
              style="width: 100%"
              v-model="form.buildDate"
              type="date"
              placeholder="请选择建成时间"
              :picker-options="pickerOptions"
              clearable
              value-format="yyyy-MM-dd"
            />
          </el-form-item>
        </el-col>

        <el-col :span="12">
          <el-form-item label="坡长(m)" prop="slopeLength">
            <el-input-number
              v-model="form.slopeLength"
              style="width: 100%"
              clearable
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="路肩类型" prop="shoulderType">
            <el-input
              style="width: 100%"
              v-model="form.shoulderType"
              placeholder="请输入路肩类型"
              clearable
            />
            <!-- 
                  <el-select
                    v-model="form.shoulderType"
                    style="width: 100%;"
                    placeholder="请选择"
                    clearable
                  >
                    <el-option
                      v-for="i in dict.type.shoulder_type"
                      :key="i.value"
                      :label="i.label"
                      :value="i.value"
                    />
                  </el-select> -->
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="土质" prop="soil">
            <el-input v-model="form.soil" style="width: 100%" clearable />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="高度(m)" prop="height">
            <el-input-number
              v-model="form.height"
              style="width: 100%"
              clearable
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="平台数量" prop="numPlatform">
            <el-input-number
              v-model="form.numPlatform"
              style="width: 100%"
              clearable
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="检查梯" prop="ladder">
            <el-input v-model="form.ladder" style="width: 100%" clearable />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="防护方式" prop="protectType">
            <el-input
              v-model="form.protectType"
              style="width: 100%"
              clearable
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="坡脚长度(m)" prop="footLength">
            <el-input-number
              v-model="form.footLength"
              style="width: 100%"
              clearable
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="落台宽度(m)" prop="fallingWidth">
            <el-input-number
              v-model="form.fallingWidth"
              style="width: 100%"
              clearable
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="面层类型" prop="surfaceType">
            <el-select
              v-model="form.pavementType"
              style="width: 100%"
              placeholder="请选择"
              clearable
            >
              <el-option
                v-for="item in dict.type.sys_surface_type"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
        </el-col>

        <el-divider content-position="left">统一里程</el-divider>
        <el-col :span="12">
          <el-form-item label="起点桩号" prop="unifiedMileageStartStake">
            <PileInput v-model="form.unifiedMileageStartStake" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="终点桩号" prop="unifiedMileageEndStake">
            <PileInput v-model="form.unifiedMileageEndStake" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="统一里程桩号" prop="unifiedMileageStake">
            <PileInput v-model="form.unifiedMileageStake" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="管养里程(km)" prop="maintenanceMileage">
            <el-input-number
              v-model="form.maintenanceMileage"
              style="width: 100%"
              :precision="3"
              clearable
            />
          </el-form-item>
        </el-col>
        <el-divider content-position="left">施工里程</el-divider>
        <el-col :span="12">
          <el-form-item label="起点桩号" prop="constructionStartStake">
            <PileInput v-model="form.constructionStartStake" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="终点桩号" prop="constructionEndStake">
            <PileInput v-model="form.constructionEndStake" />
          </el-form-item>
        </el-col>

        <el-col :span="12">
          <el-form-item label="施工里程(km)" prop="constructionMileage">
            <PileInput v-model="form.constructionMileage" />
          </el-form-item>
        </el-col>

        <el-col :span="12">
          <el-form-item label="施工桩号" prop="constructionStake">
            <PileInput v-model="form.constructionStake" />
          </el-form-item>
        </el-col>
        <el-divider content-position="left">国高网里程</el-divider>
        <el-col :span="12">
          <el-form-item label="起点桩号" prop="nationalNetworkStartStake">
            <PileInput v-model="form.nationalNetworkStartStake" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="终点桩号" prop="nationalNetworkEndStake">
            <PileInput v-model="form.nationalNetworkEndStake" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="国高网里程(km)" prop="nationalNetworkMileage">
            <el-input-number
              v-model="form.nationalNetworkMileage"
              style="width: 100%"
              :precision="3"
              clearable
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="国高网桩号" prop="nationalNetworkStake">
            <PileInput v-model="form.nationalNetworkStake" />
          </el-form-item>
        </el-col>
        <el-divider content-position="left">其他</el-divider>

        <el-col :span="12">
          <el-form-item label="移交管理单位" prop="transferManagementUnit">
            <el-input
              v-model="form.transferManagementUnit"
              style="width: 100%"
              clearable
            />
          </el-form-item>
        </el-col>

        <el-col :span="12">
          <el-form-item label="备注" prop="remark">
            <el-input
              v-model="form.remark"
              type="textarea"
              autosize
              clearable
            />
          </el-form-item>
        </el-col>

        <el-col :span="12">
          <el-form-item label="图片" prop="samplePictureId">
            <ImageUpload
              :key="ownerId"
              v-model="form.samplePictureId"
              :limit="1"
              :owner-id="ownerId"
              storage-path="/base/subgrade/shoulder"
            />
          </el-form-item>
        </el-col>
      </div>
    </el-form>
  </div>
</template>
  
  <script>
import PileInput from "@/components/PileInput/index.vue";
import SelectTree from "@/components/DeptTmpl/selectTree";
import SectionSelect from "@/components/SectionSelect";
import {
    assetInfo
} from "@/api/baseData/subgrade/sideSlope/index.js";

import ManageSelectTree from "@/components/manageSelectTree/index.vue";
import MultiDictSelect from "@/views/baseData/components/MultiDictSelect/index.vue";
import lonLat from "@/components/mapPosition/lonLat.vue";
import { listMaintenanceSectionAll } from "@/api/system/maintenanceSection";

export default {
  name: "wallInfo",
  props: {
    formData: {
      default: {},
    },
    id: {
      type: [String, Number],
      default: "",
    },
    forView: {
      default: true,
    },
  },
  inject: ["oneMap"],
  dicts: [
    "lane",
    "sys_route_grade",
    "subgrade_suface_type",
    "bridge_business_state",
    "sys_surface_type",
    "retaining_wall_position",
    "sys_route_direction",
    "left_right",
    "shoulder_type",
  ],
  components: {
    PileInput,
    SelectTree,
    SectionSelect,
    ManageSelectTree,
    lonLat,
    MultiDictSelect,
  },
  data() {
    return {
      loading: false,
      form: {
        managementMaintenanceId: "",
        managementMaintenanceBranchId: "",
      },
      pickerOptions: {
        disabledDate(v) {
          return v.getTime() > new Date().getTime();
        },
      },
      rules: {
        managementMaintenanceId: [
          { required: true, message: "请选择管理处", trigger: "change" },
        ],
        managementMaintenanceBranchId: [
          { required: true, message: "请选择管养分处", trigger: "change" },
        ],
        maintenanceSectionId: [
          { required: true, message: "请选择养护路段", trigger: "change" },
        ],
        routeCode: [
          { required: true, message: "请选择路线编码", trigger: "change" },
        ],
        startStake: [
          { required: true, message: "请输入起点桩号", trigger: "blur" },
        ],
        endStake: [
          { required: true, message: "请输入终点桩号", trigger: "blur" },
        ],
      },
      routeOptions: [],
      routeList: [],
      ownerId: "",
    };
  },
  created() {
    this.init();
  },
  methods: {
    init() {
      assetInfo(this.id).then((res) => {
        if (res.code === 200 && res.data) {
          this.form = res.data;
          listMaintenanceSectionAll({ departmentId: this.form.managementMaintenanceBranchId }).then((res) => {
              if (res.code == 200) {
                this.routeOptions = res.data;
              }
            });
        }
      });
    },
    handleSelect(e) {
      const option = this.routeList.find((i) => i.routeCode === e);
      if (option) {
        this.form.routeId = option.routeId;
        this.form.routeName = option.routeName;
      }
    },
  },
};
</script>
  
<style lang="scss" scoped>
@import "@/assets/styles/common.scss";
::v-deep .el-textarea.is-disabled .el-textarea__inner {
  background-color: rgba(1, 102, 254, 0.2);
  border-color: #0166fe;
}

.el-divider {
    background-color: #0166fe;
  }

.el-divider__text {
    background: rgba(4, 17, 48, 0.8);
    background-image: initial;
    background-position-x: initial;
    background-position-y: initial;
    background-size: initial;
    background-repeat: initial;
    background-attachment: initial;
    background-origin: initial;
    background-clip: initial;
    color: #fff;
    border: 1.5px solid #0166fe;
    padding: 10px 20px;
  }
  
  .el-divider--horizontal {
    margin: 24px 0 !important;
  }
</style>
  