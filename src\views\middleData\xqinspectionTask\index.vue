<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" >
      <el-form-item label="" prop="managementOfficeId">
        <CascadeSelection
          ref="CascadeSelection"
          style="min-width: 192px"
          :form-data.sync="queryParams"
          types="201"
        />
      </el-form-item>

      <el-form-item label="" prop="hazardLevel">
        <el-select
          v-model="queryParams.hazardLevel"
          placeholder="隐患等级"
          clearable
          collapse-tags
          style="width: 240px"
        >
          <el-option
            v-for="dict in hazardLevelOptions"
            :key="dict.dictValue"
            :label="dict.dictLabel"
            :value="dict.dictValue"
          />
        </el-select>
      </el-form-item>

      <el-form-item label="" prop="isCheck">
        <el-select
          v-model="queryParams.isCheck"
          placeholder="巡查状态"
          clearable
          style="width: 240px"
        >
          <el-option label="待巡查" value="0"/>
          <el-option label="已巡查" value="1"/>

        </el-select>
      </el-form-item>

      <el-form-item label="" prop="patrolTime">
        <el-date-picker clearable
                        v-model="queryParams.patrolTime"
                        type="date"
                        style="width: 240px"
                        value-format="yyyy-MM-dd"
                        placeholder="请选择日期">
        </el-date-picker>
      </el-form-item>

      <el-form-item style="width: 200px;">
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
        <el-button v-show="!showSearch" @click="showSearch=true" icon="el-icon-arrow-down" circle></el-button>
        <el-button v-show="showSearch" @click="showSearch=false" icon="el-icon-arrow-up" circle></el-button>
      </el-form-item>


      <el-row>

        <!--默认折叠 ,此处仅作为示例-->
        <el-form-item v-show="showSearch" label="" prop="riskName">
          <el-input
            v-model="queryParams.riskName"
            placeholder="请输入风险名称"
            clearable
            prefix-icon="el-icon-user"
            style="width: 240px"
            @keyup.enter.native="handleQuery"
          />
        </el-form-item>

<!--        <el-form-item v-show="showSearch" label="" prop="inspectionTime">-->
<!--          <el-date-picker clearable-->
<!--                          v-model="queryParams.inspectionTime"-->
<!--                          type="date"-->
<!--                          style="width: 240px"-->
<!--                          value-format="yyyy-MM-dd"-->
<!--                          placeholder="请选择巡查时间">-->
<!--          </el-date-picker>-->
<!--        </el-form-item>-->

      </el-row>


    </el-form>



    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          icon="el-icon-plus"
          size="mini"

        >新增
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          icon="el-icon-edit"
          size="mini"

          v-hasPermi="['middleData:xqinspection:edit']"
        >修改
        </el-button>
      </el-col>

    </el-row>


    <el-row>
      <el-table size="mini" style="width: 100%" v-loading="loading" border
                highlight-current-row
                @row-click="clickRow" @select="selectChange"
                @select-all="handleSelectionChange"
                ref="table"
                :data="tableDataList"
                :height="showSearch ? 'calc(100vh - 320px)' : 'calc(100vh - 260px)'"
      >
        <el-table-column fixed label="序号" type="index" width="50" align="center">
          <template v-slot="scope">
            {{ (scope.$index + (queryParams.pageNum - 1) * queryParams.pageSize)+1 }}
          </template>
        </el-table-column>

        <el-table-column label="隐患类型" align="center" prop="hazardType" width="80" show-overflow-tooltip />

        <el-table-column label="巡查状态" align="center" prop="isCheck" width="80" show-overflow-tooltip >
          <template slot-scope="scope">
            <!--            <span>{{ scope.row.isCheck === "0" ? '待巡查':'已巡查' }}</span>-->
            <el-tag v-if="scope.row.isCheck === '0'" type="plain"  >待巡查</el-tag>
            <el-tag v-else type="success"  >已巡查</el-tag>
          </template>
        </el-table-column>
<!--        <el-table-column label="省份" align="center" prop="provinceName" width="80" show-overflow-tooltip />-->
<!--        <el-table-column label="市/州" align="center" prop="cityName" width="90" show-overflow-tooltip />-->
        <el-table-column label="管理处" align="center" prop="managementOffice" width="120" show-overflow-tooltip />
        <el-table-column label="分处" align="center" prop="subOffice" width="120" show-overflow-tooltip />
        <el-table-column label="养护路段" align="center" prop="maintenanceSection" width="120" show-overflow-tooltip />
        <el-table-column label="公路类型" align="center" prop="roadType" width="100" show-overflow-tooltip >
          <template slot-scope="scope">
            <span>{{ formatRoadType(scope.row.roadType) }}</span>
          </template>
        </el-table-column>
        <el-table-column label="公路编号" align="center" prop="roadNumber" width="100" show-overflow-tooltip />
        <el-table-column label="名称" align="center" prop="tempName" width="140" show-overflow-tooltip />
        <el-table-column label="隐患等级" align="center" prop="hazardLevel" width="100" show-overflow-tooltip >
          <template slot-scope="scope">
            <span>{{ formatHazardLevel(scope.row.hazardLevel) }}</span>
          </template>
        </el-table-column>
        <el-table-column label="中心桩号" align="center" prop="centerStake" width="100" show-overflow-tooltip >
          <template slot-scope="scope">
            <span>{{ formatStakeNumber(scope.row.centerStake) }}</span>
          </template>
        </el-table-column>
        <!--        <el-table-column label="桥梁名称" align="center" prop="bridgeName" width="120" show-overflow-tooltip />-->
        <!--        <el-table-column label="桥梁编码" align="center" prop="assetCode" width="100" show-overflow-tooltip />-->
        <!--        <el-table-column label="灾害风险隐患部位" align="center" prop="hazardLocation" width="140" show-overflow-tooltip />-->
        <!--        <el-table-column label="灾害风险隐患类型" align="center" prop="hazardCategory" width="140" show-overflow-tooltip />-->

        <el-table-column label="灾害风险隐患描述" align="center" prop="hazardDescription" width="140" show-overflow-tooltip />
        <el-table-column label="照片" align="center" prop="photo" width="100" >
          <template slot-scope="scope">
            <el-button
              v-if="scope.row.photo"
              type="primary"
              icon="el-icon-picture-outline"
              size="mini"
              circle
              @click.stop="previewImage(scope.row.photo)"
            ></el-button>
            <span v-else>无图片</span>
          </template>
        </el-table-column>
        <el-table-column label="是否完成整治" align="center" prop="rectificationCompleted" width="110" show-overflow-tooltip >
          <template slot-scope="scope">
            <span>{{ formatMeasures(scope.row.rectificationCompleted) }}</span>
          </template>
        </el-table-column>
        <el-table-column label="已(拟)采取的措施" align="center" prop="measuresDetail" width="130" show-overflow-tooltip />
        <el-table-column label="整治完成时限" align="center" prop="completionDeadline" width="120">
          <template v-slot="scope">
            <span>{{ parseTime(scope.row.completionDeadline, '{y}-{m}-{d}') }}</span>
          </template>
        </el-table-column>
        <!--        <el-table-column label="省级责任单位及人员" align="center" prop="provincialResponsible" width="150" show-overflow-tooltip />-->
        <!--        <el-table-column label="复核责任单位及人员" align="center" prop="reviewResponsible" width="150" show-overflow-tooltip />-->
        <!--        <el-table-column label="排查责任单位及人员" align="center" prop="inspectionResponsible" width="150" show-overflow-tooltip />-->
        <el-table-column label="是否属于5类自然灾害" align="center" prop="isNaturalDisaster" width="160" show-overflow-tooltip >
          <template slot-scope="scope">
            <span>{{ formatMeasures(scope.row.isNaturalDisaster) }}</span>
          </template>
        </el-table-column>



      </el-table>
      <pagination
        v-show="total>0"
        :total="total"
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
        @pagination="getList"
      />
    </el-row>

    <!-- 添加图片预览对话框 -->
    <el-dialog
      title="照片预览"
      :visible.sync="imagePreview.visible"
      width="800px"
      append-to-body
      center
      :close-on-click-modal="true"
      class="image-preview-dialog"
    >
      <div class="image-preview-container">
        <FileUpload
          v-if="imagePreview.url"
          v-model="imagePreview.url"
          :key="imagePreview.url"
          for-view
        />
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {listAllRoute} from "@/api/system/route";
import CascadeSelection from "./CascadeSelectionManagementOffice.vue";
import {listXqHazard} from "@/api/middleData/xqinspection";
import { formatStake, parseStake } from "@/utils/common";
export default {
  name: "xqtask",
  components: {CascadeSelection},
  props: {
    maintenanceSectionId: {
      type: String,
      default: '',
    },
  },
  data() {
    return {
      // 显示搜索条件
      showSearch: false,
      // 遮罩层
      visible: false,
      loading: false,
      // 选中数组值
      assetIds: [],
      // 总条数
      total: 0,
      // 表数据
      tableDataList: [],
      routeList: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 20,
        tunnelName: null,
        tunnelCode: null,
        routeCode: null,
        managementOfficeId: null,
        maintenanceSectionId: null,
        hazardLevel: null,
        roadNumber: null,
        isCheck: '0',
        patrolTime: this.getDate()
      },
      //提交参数
      params: {},
      maintenanceSectionList: [],
      // 部门树选项
      deptOptions: [],
      currentRow: null,
      // 字典数据
      hazardLevelOptions: [
        { dictLabel: '重大隐患', dictValue: 1 },
        { dictLabel: '较大隐患', dictValue: 2 },
        { dictLabel: '一般隐患', dictValue: 3 },
        { dictLabel: '无', dictValue: 0 },
      ],
      // 审核状态字典
      auditStatusOptions: [
        { dictLabel: '待审核', dictValue: 1 },
        { dictLabel: '审核通过', dictValue: 2 },
        { dictLabel: '审核不通过', dictValue: 3 },
      ],  // 公路类型字典
      roadTypeOptions: [
        { dictLabel: '高速公路', dictValue: 1 },
        { dictLabel: '普通公路', dictValue: 2 },
      ],
      measuresOptions: [
        { dictLabel: '是', dictValue: 1 },
        { dictLabel: '否', dictValue: 0 },
      ],
      // 图片预览数据
      imagePreview: {
        visible: false,
        url: null
      },
    };
  },
  created() {

    this.handleQuery()
  },
  methods: {
    // 显示弹框
    show() {
      this.getList();
      this.visible = true;
    },
    clickRow(row) {
      this.$refs.table.toggleRowSelection(row);
      this.setChecked(row);
    },
    selectChange(arr, row) {
      //退选和选中处理
      this.setChecked(row);
    },
    //退选和选中处理
    setChecked(row){
      if (this.assetIds.includes(row.assetId)) {
        this.assetIds = this.assetIds.filter(i => i !== row.assetId);
      } else {
        this.assetIds.push(row.assetId)
      }
      if (!this.isAddAll) this.currentRow = row;
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      if (selection.length > 0) {
        selection.forEach(item => {
          // this.setChecked(item);
          if (!this.assetIds || !this.assetIds.includes(item.assetId)) {
            this.assetIds.push(item.assetId)
          }
        })
      } else {
        this.tableDataList.forEach(item => {
          this.assetIds = this.assetIds.filter(i => i !== item.assetId);
        })
      }
    },
    /** 查询路线列表 */
    getRouteList() {
      listAllRoute().then(res => {
        this.routeList = res.data
      })
    },
    // 查询表数据
    getList() {
      this.loading = true;

      const tempParams = {
        ...this.queryParams,
        tempName: this.queryParams.riskName
      }
      if (this.queryParams.patrolTime){
        tempParams.patrolTime += " 23:59:59"
      }
      this.queryParams.ruleIdsFlag = true;
      listXqHazard(tempParams).then(response => {
        this.tableDataList = response.rows
        this.total = response.total
        this.loading = false
      })
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.queryParams.maintenanceSectionId = null
      this.queryParams.roadNumber = null

      this.handleQuery();

    },
    /** 提交选择养护路段操作 */
    handleSelectBridge() {
      let data;
      if (this.isAddAll) {
        if (!this.assetIds) {
          this.$modal.msgError("请选择隧道");
          return;
        }
        data = this.assetIds;
      } else {
        if (!this.currentRow) {
          this.$modal.msgError("请选择隐患");
          return;
        }
        data = this.currentRow;
      }
      this.$emit('update:checkEntity', data);
      this.$emit('change', data);
      this.assetIds = [];
      this.visible=false;
    },
    // 格式化隐患等级显示
    formatHazardLevel(hazardLevel) {
      const dict = this.hazardLevelOptions.find((item) => item.dictValue === hazardLevel);
      return dict ? dict.dictLabel : hazardLevel;
    },

    // 格式化风险等级显示
    formatRiskLevel(riskLevel) {
      const dict = this.riskLevelOptions.find((item) => item.dictValue === riskLevel);
      return dict ? dict.dictLabel : riskLevel;
    },

    // 格式化是否采取措施显示
    formatMeasures(status) {
      const dict = this.measuresOptions.find((item) => item.dictValue === status);
      return dict ? dict.dictLabel : status;
    },

    // 格式化审核状态显示
    formatAuditStatus(status) {
      const dict = this.auditStatusOptions.find((item) => item.dictValue === status);
      return dict ? dict.dictLabel : status;
    },

    // 格式化公路类型显示
    formatRoadType(type) {
      const dict = this.roadTypeOptions.find((item) => item.dictValue === type);
      return dict ? dict.dictLabel : type;
    },
    /** 格式化桩号显示 */
    formatStakeNumber(value) {
      return formatStake(value);
    },
    /** 图片预览 */
    previewImage(url) {
      this.imagePreview.url = url;
      this.imagePreview.visible = true;
    },
    getDate(){
      const now = new Date();
      const formatted = `${now.getFullYear()}-${
        (now.getMonth() + 1).toString().padStart(2, '0') // 补零至两位数
      }-${now.getDate().toString().padStart(2, '0')}`;
      console.log(formatted);
      return formatted
    }
  }
};
</script>
<style >
.el-table__body tr.current-row>td.el-table__cell {
  background: #b7daff;
}

</style>
