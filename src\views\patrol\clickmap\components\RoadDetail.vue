<template>
  <div class="road-detail">
    <!-- 在这里添加路线详情的内容 -->
    <div>路线ID: {{ lineId }}</div>
    <!-- 其他路线详情内容 -->
  </div>
</template>

<script>
export default {
  name: 'RoadDetail',
  props: {
    lineId: {
      type: [String, Number],
      required: true,
    },
  },
  data() {
    return {
      // 路线详情数据
    };
  },
  methods: {},
};
</script>

<style lang="scss" scoped>
.road-detail {
  padding: 20px;
}
</style>
