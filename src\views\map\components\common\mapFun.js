import { createStringXY } from "ol/coordinate";
import { fromLonLat } from "ol/proj"; // 导入方法
import { WKT, MVT, GeoJSON, JSONFeature } from "ol/format";
import VectorLayer from "ol/layer/Vector";
import { Heatmap as HeatmapLayer } from 'ol/layer';
import VectorSource from "ol/source/Vector";
// import Cluster from "ol/source/Cluster";
import {
  Circle as CircleStyle,
  Fill,
  Icon,
  Style,
  Text,
  Stroke,
  RegularShape,
} from "ol/style";
import {
  MultiPolygon as GeomMultiPolygon,
  LineString as GeomLineString,
} from "ol/geom";
import { fromExtent } from "ol/geom/Polygon";
import { Point } from "ol/geom";
import {
  Vector as sourceVector,
  VectorTile as VectorTileSource,
  XYZ,
  Cluster,
  TileWMS,
  TileImage,
} from "ol/source";
import { Feature } from "ol";
import { Draw, Modify, Select, DoubleClickZoom } from "ol/interaction";
import { createBox } from 'ol/interaction/Draw.js';
import { Loading } from 'element-ui';
import VectorTileLayer from "ol/layer/VectorTile";
import { getArea, getLength } from "ol/sphere";
import { toFeature } from "ol/render/Feature";
import { createXYZ } from "ol/tilegrid";
import { get as getProj } from "ol/proj";
import TileLayer from "ol/layer/Tile";
import Overlay from "ol/Overlay";

import { getToken } from "@/utils/auth";
import { findFiles } from "@/api/file/index.js";
import store from "@/store";
import { getShapeList } from "@/api/oneMap/deptInfo";
import cache from "@/plugins/cache";
import { getMenuSub } from "@/api/oneMap/menuSub";

import spotImg from "@/assets/map/spot.png";
import pointImg from "@/assets/map/point.png";
import { isBigScreen } from "./util";

export const lineId = "1824259894327382017";
const key = "cde0b56cf882626889981701109a7536";

// 从vuex 中获取 当前 地图相关数据
let mapObj = store.state.map || {};
// 是否是超级管理员
let isAdmin = store.getters.roles.includes("admin");

// 创建一个解析 WKT 的格式对象
const format = new WKT();
const isBig = isBigScreen();

// 定义一些常量
var pi = 3.1415926535897932384626;
var a = 6378245.0;
var ee = 0.00669342162296594323;
const defCenter = fromLonLat([102.72378400000002, 25.013740000000027]);
var timer = null;
export const baseStyle = new Style({
  fill: new Fill({
    color: "rgba(255, 255, 255, 0.8)",
  }),
  stroke: new Stroke({
    color: "#33cc33",
    width: 2,
  }),
  image: new CircleStyle({
    anchor: [0.5, 0],
    radius: 7,
    stroke: new Stroke({
      color: "rgba(255,255,255,0.8)",
      width: 1,
    }),
    fill: new Fill({
      color: "rgb(64,158,255)",
    }),
  }),
  text: new Text({
    font: isBig ? "0.3vw Microsoft YaHei" : "13px Microsoft YaHei",
    fill: new Fill({
      color: "#000",
    }),
    stroke: new Stroke({
      color: "#fff",
      width: 0,
    }),
    text: "",
    offsetY: isBig ? -70 : -35,
    padding: [5, 5, 0, 10], // 文本与背景之间的间距
    overflow: true,
    declutter: true,
  }),
  // zIndex: 9999,
});

// 添加数据到地图
export function addWidthFeature(
  mapLayer,
  data,
  layerName = "dataLayer",
  icon = null,
  flashing = false,
  minZoom = 1,
) {
  if (!data || !mapLayer) return;
  let features = [];
  let mFeatures = [];
  let isM = false;
  let icons = icon;
  data.forEach((item) => {
    let shape = item.dgeom || item.geom || item.shape || null;
    if (!icon) {
      if (item.sys_dept_id == 1 || item.dept_id == 1) {
        icons = pointImg;
      } else {
        icons = spotImg;
      }
    }
    if (!shape) return;
    let feature = new WKT().readFeature(shape, {
      dataProjection: "EPSG:4326",
      featureProjection: "EPSG:3857",
    });
    if (item.mgeom) {
      isM = true; // 是否添加行政区面
      let mFeature = format.readFeature(item.mgeom, {
        dataProjection: "EPSG:4326",
        featureProjection: "EPSG:3857",
      });
      // 添加面判断
      let obj = JSON.parse(JSON.stringify(item));
      obj.type = "mgeom";
      mFeature.set("data", obj);
      mFeatures = [...mFeatures, ...[mFeature]];
    }
    // 定义要素样式
    feature.setStyle(
      new Style({
        text: new Text({
          text:
            typeof icon == "boolean"
              ? ""
              : item.typeName || item.typename || item.name || "",
          fill: new Fill({
            color: "#fff",
          }),
          stroke: new Stroke({
            color: "#333",
            width: 2,
          }),
          // 设置文本的偏移量，以便它出现在指定的位置
          offsetX: 0,
          offsetY: isBig ? -130 : -50,
          // 设置文本的位置
          textAlign: "center",
          textBaseline: "middle",
          // 设置文本的大小
          font: isBig ? "0.5vw Arial" : "12px Arial",
          backgroundFill: new Fill({
            color: "rgba(13,27,43,0.6)",
          }),
          padding: [2, 5, 2, 5],
        }),
        image: new Icon({
          anchor: [0.5, 1],
          // anchorXUnits: "fraction",
          // anchorYUnits: "pixels",
          src: icons
            ? typeof icons == "boolean"
              ? require("@/assets/map/piont.png")
              : icons
            : spotImg, // 图标图片路径
          scale: isBig ? 2 : 0.8,
        }),
      })
    );

    feature.set("data", item);
    features = [...features, ...[feature]];
  });
  // 将要素添加到要素集中
  let vectorsLayer = new VectorLayer({
    source: new VectorSource({
      features: features,
    }),
  });
  vectorsLayer.setMinZoom(minZoom);
  vectorsLayer.setZIndex(9002);
  vectorsLayer.set("name", layerName);

  let flag = false;
  if (flashing) {
    // let styleFunction = vectorsLayer.getStyleFunction()
    const fun = () => {
      if (!flag) {
        // vectorsLayer.setOpacity(0.8)
        // 遍历图层中的所有要素并获取它们的样式
        vectorsLayer.getSource().forEachFeature(function (feature) {
          // var styles = styleFunction(feature, 0);
          // 修改图标样式，例如更改颜色或大小
          var style = feature.getStyle();
          let textStyle = style.getText();
          let feaObj = feature.getProperties().data || {};
          // 输出样式对象或者做其他处理
          let newStyle = new Style({
            text: textStyle,
            image: new Icon({
              anchor: [0.5, 1],
              // anchorXUnits: "fraction",
              // anchorYUnits: "pixels",
              src:
                feaObj.dept_id == 1 || feaObj.sys_dept_id == 1
                  ? pointImg
                  : spotImg, // 图标图片路径 ? require("@/assets/map/point.gif") : require("@/assets/map/spot.gif"), // 图标图片路径
              scale: isBig ? 2 : 0.8,
              opacity: 0.8,
            }),
          });
          feature.setStyle(newStyle);
        });
        flag = true;
      } else {
        // vectorsLayer.setOpacity(1)
        // 遍历图层中的所有要素并获取它们的样式
        vectorsLayer.getSource().forEachFeature(function (feature) {
          // var styles = styleFunction(feature, 0);
          // 修改图标样式，例如更改颜色或大小
          var style = feature.getStyle();
          let textStyle = style.getText();
          let feaObj = feature.getProperties().data || {};
          // 输出样式对象或者做其他处理
          let newStyle = new Style({
            text: textStyle,
            image: new Icon({
              anchor: [0.5, 1],
              // anchorXUnits: "fraction",
              // anchorYUnits: "pixels",
              src:
                feaObj.dept_id == 1 || feaObj.sys_dept_id == 1
                  ? pointImg
                  : spotImg, // 图标图片路径
              scale: isBig ? 2 : 0.8,
              opacity: 1,
            }),
          });
          feature.setStyle(newStyle);
        });
        flag = false;
      }
    };
    timer = setInterval(fun, 1000);
  }
  mapLayer.addLayer(vectorsLayer);

  // 添加行政区面
  if (isM) {
    let mLayer = new VectorLayer({
      source: new VectorSource({
        features: mFeatures,
      }),
    });
    const style = new Style({
      fill: new Fill({
        color: "rgba(255, 0, 0,0)",
      }),
      stroke: new Stroke({
        color: "rgba(255, 0, 0,0)",
        width: 3,
      }),
    });
    mLayer.setStyle(style);
    mLayer.setZIndex(9000);
    mLayer.set("name", layerName);
    mapLayer.addLayer(mLayer);
    return {
      vectorsLayer,
      mLayer,
    };
  }
  let feat = [...mFeatures, ...features];
  // 返回Feature 数据
  return feat;
}

/**
 * 点击把数据加载到地图图层
 * @param {*} feature
 * @param {*} data 需要加载到Feature的数据
 * @param {*} icon 图标、图例
 * @param {*} clear 请否清除上次图层
 * @param {*} layerName 图层名称
 */
export function addClickFeature(
  feature,
  data,
  icon = null,
  flashing = true,
  clear = true,
  layerName = "clickLayer",
  type = 2,
  name = ""
) {
  if (timer) {
    clearInterval(timer);
    timer = null;
  }
  let map = window.mapLayer;
  if (clear) {
    removeLayer(map, "clickLayer");
  }
  if (Object.prototype.toString.call(feature) == "[object Array]") {
  } else {
    let dataObj = {
      listQueryConfig: { detlCondf: mapObj.detlCondf },
      ...data,
    };
    feature.set("data", dataObj);
  }

  let styleA = "";
  let styleB = "";
  if (type == 2) {
    let legendIcon =
      mapObj.legendList && mapObj.legendList.length
        ? mapObj.legendList[0].icon
        : require("@/assets/map/bridge.png");
    icon = icon ? icon : legendIcon;
  }
  // 判断Feature 是点线面
  let featureType = feature.getGeometry().getType();
  if (icon && featureType && featureType == "Point") {
    styleA = new Style({
      image: new Icon({
        anchor: [0.5, 1], // 图标的锚点，相对于图标图片的比例
        // anchorOrigin: "top-right", // 锚点的原点，可以是'top-left', 'top-right', 'bottom-left' 或 'bottom-right'
        // offsetOrigin: "top-right", // 偏移的原点，与anchorOrigin相同可以取消默认偏移
        src: icon || "",
        scale: isBig ? 2.0 : 0.8,
      }),
      text: new Text({
        text: name,
        font: isBig ? "28px Calibri,sans-serif" : "14px Calibri,sans-serif",
        fill: new Fill({ color: "#333333" }),
        stroke: new Stroke({ color: "#fff", width: 2 }),
        offsetY: isBig ? -70 : -30,
      }),
    });
    styleB = new Style({
      image: new Icon({
        anchor: [0.5, 1], // 图标的锚点，相对于图标图片的比例
        anchorOrigin: "top-right", // 锚点的原点，可以是'top-left', 'top-right', 'bottom-left' 或 'bottom-right'
        offsetOrigin: "top-right", // 偏移的原点，与anchorOrigin相同可以取消默认偏移
        src: icon || "",
        scale: isBig ? 2.05 : 0.65,
      }),
      text: new Text({
        text: name,
        font: isBig ? "28px Calibri,sans-serif" : "14px Calibri,sans-serif",
        fill: new Fill({ color: "#333333" }),
        stroke: new Stroke({ color: "#fff", width: 2 }),
        offsetY: isBig ? -70 : -30,
      }),
    });
  } else {
    let interiorColour =
      mapObj.legendList && mapObj.legendList.length
        ? mapObj.legendList[0]?.interiorColour
        : "rgba(255, 0, 0, 1)";
    let borderColour =
      mapObj.legendList && mapObj.legendList.length
        ? mapObj.legendList[0]?.borderColour
        : "rgba(255, 0, 0, 1)";
    styleA = new Style({
      fill: new Fill({
        color: interiorColour,
      }),
      stroke: new Stroke({
        color: data?.style?.borderColour || borderColour,
        width: isBig
          ? data?.style?.borderWidth * 2 || 15
          : data?.style?.borderWidth || 5,
      }),
      image: new CircleStyle({
        radius: 10,
        fill: new Fill({
          color: interiorColour,
        }),
      }),
      text: new Text({
        text: name,
        font: isBig ? "0.4vw Calibri,sans-serif" : "14px Calibri,sans-serif",
        fill: new Fill({ color: "#333333" }),
        stroke: new Stroke({ color: "#fff", width: 2 }),
        offsetY: isBig ? - 70 : -30,
      }),
    });
    styleB = new Style({
      fill: new Fill({
        color: "rgba(255, 0, 0, 0.3)",
      }),
      stroke: new Stroke({
        color: "rgba(255, 0, 0, 0.3)",
        width: isBig ? 15 : 5,
      }),
      image: new CircleStyle({
        radius: 10,
        fill: new Fill({
          color: "rgba(255, 0, 0, 0.3)",
        }),
      }),
      text: new Text({
        text: name,
        font: isBig ? "0.4vw Calibri,sans-serif" : "14px Calibri,sans-serif",
        fill: new Fill({ color: "#333333" }),
        stroke: new Stroke({ color: "#fff", width: 2 }),
        offsetY: isBig ? -60 : -30,
      }),
    });
  }
  let features = Array.isArray(feature) ? feature : [feature];
  let cLayer = new VectorLayer({
    source: new VectorSource({
      features: features,
    }),
    style: styleA,
  });
  cLayer.set("name", layerName);
  cLayer.set("layerId", data ? data.id : "");
  cLayer.setZIndex(9006);

  let flag = false;
  if (flashing) {
    const fun = () => {
      if (!flag) {
        cLayer.setStyle(styleA);
        flag = true;
      } else {
        cLayer.setStyle(styleB);
        flag = false;
      }
    };
    timer = setInterval(fun, 500);
  }
  map.addLayer(cLayer);
}

// 添加多条路线到地图
export function addFeatureToMap(features = [], layerName = "dataLayer") {
  let legendList = mapObj.legendList;
  let obj =
    legendList && legendList.length == 1
      ? legendList[0]
      : {
        borderColour: "rgba(0, 234, 255, 1)",
        borderWidth: 5,
        interiorColour: "rgba(0, 191, 255, 1)",
      };
  if (features.length === 0) return;
  let feature = [];
  let styleA = baseStyle.clone();
  styleA.getStroke().setColor(obj?.borderColour || "rgba(255, 0, 0, 0.6)");
  styleA.getStroke().setWidth(obj?.borderWidth || 5);
  styleA.getFill().setColor(obj?.interiorColour || "rgba(255,0,0,0.3)");
  let styleB = baseStyle.clone();
  styleB.getStroke().setColor("rgba(255, 0, 0, 0.6)");

  features.forEach((f) => {
    // f.setStyle(styleA);
    feature.push(f);
  });
  let vectorsLayer = new VectorLayer({
    source: new VectorSource({
      features: feature,
    }),
    style: styleA,
  });
  vectorsLayer.setZIndex(9002);
  vectorsLayer.set("name", layerName);

  if (layerName !== 'highwayLayer') {
    let flag = false;
    if (true) {
      const fun = () => {
        if (!flag) {
          vectorsLayer.setStyle(styleB);
          flag = true;
        } else {
          vectorsLayer.setStyle(styleA);
          flag = false;
        }
      };
      timer = setInterval(fun, 1000);
    }
  } else {
    let s = baseStyle.clone()
    s.getStroke().setColor("rgba(0, 234, 255, 1)");
    s.getStroke().setWidth(5);
    s.getFill().setColor("rgba(0, 191, 255, 1)");
    vectorsLayer.setStyle(s);
  }
  window.mapLayer.addLayer(vectorsLayer);
}

export function removeOverlay() {
  const overlays = mapLayer.getOverlays();
  if (overlays) {
    overlays.clear();
  }
}

// 移除地图图层
export function removeLayer(
  mapLayer,
  layerName = "shapeLayer",
  layerKey = "name"
) {
  let allLayer = mapLayer?.getLayers()?.getArray();
  if (!allLayer) return;
  let layer = allLayer.find((l) => l.get(layerKey) == layerName);
  if (layer) {
    mapLayer.removeLayer(layer);
  }
  if (timer) {
    clearInterval(timer);
    timer = null;
  }
}

// 移除所有图层数据
export function removeAllLayer(mapLayer, layerName = "name") {
  let allLayer = mapLayer?.getLayers()?.getArray();
  if (!allLayer) return;
  let layerNameArr = [
    "tableLayer",
    "pbfLayer",
    "clickLayer",
    "tollStationLayer",
  ];
  layerNameArr.forEach((item) => {
    let layer = allLayer.find((l) => l.get(layerName) == item);
    if (layer) {
      mapLayer.removeLayer(layer);
    }
  });
  if (timer) {
    clearInterval(timer);
    timer = null;
  }
}

export function removeHeatLayer() {
  let allLayer = window.mapLayer?.getLayers()?.getArray();
  if (!allLayer) return;
  let layer = allLayer.find((l) => l.get('name').includes('heatLayer'));
  if (layer) {
    window.mapLayer.removeLayer(layer);
  }
}

/**
 * 根据图层名称和图层键获取图层对象
 * @param {string} layerName - 图层名称
 * @param {string} layerKey - 图层键
 * @returns {Object|null} 返回找到的图层对象，如果未找到则返回null
 */
export function getLayersByName(layerName, layerKey) {
  // 获取地图所有图层的数组
  let allLayer = window.mapLayer?.getLayers()?.getArray();
  // 在所有图层中查找指定名称的图层
  let layer = allLayer.find((l) => l.get(layerName) == layerKey);
  // 如果找到了图层，则返回该图层对象，否则返回null
  if (layer) {
    return layer;
  }
  return null;
}

/**
 * 获取地图上的所有图层
 * @returns {Array} 返回一个包含所有图层的数组，如果没有图层则返回空数组
 */
export function getAllLayers() {
  // 获取地图对象的所有图层，并将其转换为数组
  let allLayer = window.mapLayer?.getLayers()?.getArray();
  // 如果没有图层，则返回空数组
  if (!allLayer) return [];
  // 返回所有图层的数组
  return allLayer;
}

// 从 wkt中获取经纬
export function getLonLat(wkt) {
  const geometry = format.readGeometry(wkt);
  let center = geometry?.getCoordinates();
  if (!center) return { lon: "", lat: "" };
  let [lon, lat] = center;
  return { lon, lat };
}
// 将wkt转为 Feature
export function wktToFeature(shape) {
  return format.readFeature(shape, {
    dataProjection: "EPSG:4326", //数据坐标系
    featureProjection: "EPSG:3857", //要素坐标系
  });
}

// 将Feature转为 wkt
export function featureToWkt(featrue) {
  return format.writeFeature(featrue);
}
// 验证wkt是否合法
export function isValidWKT(wkt) {
  try {
    // 尝试解析WKT，如果无法解析，会抛出异常
    format.readGeometry(wkt);
    return true;
  } catch (e) {
    // 解析失败，WKT不合法
    return false;
  }
}

/**
 * 添加适量切片
 * @param {*} mapLayer 地图对象
 * @param {*} layerKey 图层名称
 * @param {*} layerValue 图层名称值
 * @param {*} dataDto 切片请求参数
 * @param {*} row 点击的数据
 * @param {*} lineBase 是否是地图路线
 * @param {*} minZoom 最小矢量图层加载层级
 * @param {*} tileSize 图层加载大小
 * @returns
 */
export function addVectorTile(
  mapLayer,
  layerKey,
  layerValue,
  dataDto,
  row = null,
  lineBase = false,
  zIndex = null,
  minZoom = 6,
  tileSize = 256,
  showText = true
) {
  let url = `${process.env.VUE_APP_BASE_API}/oneMap/layerData/3857/${tileSize}/{z}/{x}/{y}.pbf`;
  let index = 0;
  const style = baseStyle.clone();
  let s2 = baseStyle.clone();
  const config = row ? JSON.parse(row?.listQueryConfig) : null;
  let vectorTileSource = new VectorTileSource({
    tileSize,
    format: new MVT(),
    url: url,
    maxZoom: 18,
    tileGrid: createXYZ({
      extent: getProj("EPSG:3857").getExtent(),
      maxZoom: 18,
    }),
    wrapX: false,
    tilePixelRatio: 2,
    projection: "EPSG:3857",
    tileLoadFunction: function (tile, url) {
      tile.setLoader(function (extent, resolution, projection) {
        fetch(url, {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            Authorization: "Bearer " + getToken(),
          },
          body: dataDto,
        }).then(function (response) {
          response.arrayBuffer().then(function (data) {
            const format = tile.getFormat(); // ol/format/MVT configured as source format
            try {
              const features = format.readFeatures(data, {
                extent: extent,
                featureProjection: projection,
              });
              tile.setFeatures(features);
            } catch (e) {
              // console.error('解析切片失败，跳过该切片:', e);
              // 创建空特征集避免崩溃
              tile.setFeatures([]);
            }
            // const features = format.readFeatures(data, {
            //   extent: extent,
            //   featureProjection: projection,
            // });
            // if (!lineBase) {
            // }
            // tile.setFeatures(features);
          });
        });
      });
    },
    tileUrlFunction: function (tileCoord) {
      console.log(tileCoord);
    },
  });
  minZoom =
    mapObj.legendList && mapObj.legendList.length
      ? mapObj.showLevel
      : row && row.showLevel
        ? row.showLevel
        : minZoom;
  //如果是 路线图层
  if (lineBase) {
    minZoom = 1;
  }
  let vLayer = new VectorTileLayer({
    maxZoom: 25,
    minZoom,
    source: vectorTileSource,
    declutter: true,
    style: (feature, resolution) => {
      const zoom = Math.floor(
        mapLayer.getView().getZoomForResolution(resolution)
      );
      let featureData = feature.getProperties();
      const geometry = feature.getGeometry();
      const featureType = geometry.getType();
      style.getText().setBackgroundFill(
        new Fill({
          color: "rgba(0,0,0,0)",
        })
      );
      // 设置虚线
      if (config?.lineDash?.isLineDash) {
        style.getStroke().setLineDash(config?.lineDash.dash);
      }
      // 如果是面或者是线
      if (
        featureData &&
        (featureData.shapeType == 2 ||
          featureData.shapeType == 3 ||
          (featureType != "Point" && featureType != "MultiPoint"))
      ) {
        let legendList = mapObj.legendList || [];
        let legendData = legendList.find(
          (item) =>
            item.id == featureData.layer || item.layerName == feature.layer
        );
        let fillColor =
          row?.interiorColour ||
          legendData?.interiorColour ||
          "rgba(255, 0, 0, 1)";
        let strokeColor =
          row?.borderColour || legendData?.borderColour || "rgba(255, 0, 0, 1)";
        // 边框颜色
        style.getStroke().setColor(strokeColor);
        // 填充颜色
        style.getFill().setColor(fillColor);

        // 设置边框粗细
        let borderW = isBig ? row?.borderWidth * 2 || 4 : row?.borderWidth || 2;
        style.getStroke().setWidth(borderW);
        let isRandom = false;

        let a = JSON.parse(row.listQueryConfig)
        if (a.mutipleLine) {
          // let a = JSON.parse(row.listQueryConfig)
          style.getStroke().setColor(a.mutipleLine[featureData.type])
          style.getFill().setColor(a.mutipleLine[featureData.type])
          style.getStroke().setWidth(isBig ? 8 : 4);
          featureData.listQueryConfig = a
          featureData.listQueryConfig.tableHeader = JSON.parse(featureData.tableHeader)
          featureData.treeId = featureData.layer
          delete featureData.layer
        }
        // 路线
        if (
          (featureData.shapeType == 2 ||
            (featureType != "Point" && featureType != "MultiPoint")) &&
          lineBase
        ) {
          style.getText().setFill(
            new Fill({
              color: "rgba(255,255,255, 1)", // 文本颜色
            })
          );
          style.getText().setStroke(
            new Stroke({
              color: "rgba(0,135,46, 1)", // 文本颜色
              width: isBig ? 6 * 2 : 6,
            })
          );
          style.getText().setPlacement("line");
          style.getText().setOffsetY(0);
          style.getText().setRotateWithView(true);
          style.setZIndex(0);
          isRandom = true;

          s2.getText().setFill(
            new Fill({
              color: "rgb(255, 255, 255, 1)", // 文本颜色
            })
          );
          s2.getText().setPadding([0, 2, 0, 2]); // 内边距
          s2.getText().setPlacement("point");
          s2.getText().setOffsetY(0);
          s2.getText().setRotation(0)
          s2.getText().setRotateWithView(true);
          s2.getText().setBackgroundFill(
            new Fill({
              color: "rgba(241,185,53, 1)",
            })
          )
          s2.getText().setFont(isBig ? "20px Microsoft YaHei" : "10px Microsoft YaHei");
          s2.setZIndex(0);
          if (zoom >= 7 && zoom < 9) {
            let result = [];
            for (let i = 1; i <= 1070; i += 36) {
              result.push(i);
            }
            if (result.includes(Number(featureData.id))) {
              s2.getText().setText(featureData.code);
            } else {
              s2.getText().setText('');
            }
          }
          if (zoom >= 9 && zoom < 11) {
            let result = [];
            for (let i = 1; i <= 1070; i += 36) {
              result.push(i);
            }
            for (let i = 1; i <= 1070; i += 12) {
              result.push(i);
            }
            if (result.includes(Number(featureData.id))) {
              s2.getText().setText(featureData.code);
            } else {
              s2.getText().setText('');
            }
          }
          if (zoom >= 11) {
            s2.getText().setText(featureData.code);
          }
        }
        let typeName = featureData.typeName || featureData.typename || "";

        let textShowLevel = row?.textShowLevel || 6;
        const text = zoom >= textShowLevel ? `${typeName}` : "";

        if (isRandom && zoom < 10 && zoom >= 7) {
          style.getText().setOverflow(true);
          if (zoom < 9) {
            style.getText().setPlacement("point");
          }
          let randomArr = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12];
          for (let index = 0; index < 150; index++) {
            randomArr[index] = index + 1;
          }
          const randomIndex =
            randomArr[Math.floor(Math.random() * randomArr.length)]; // 生成一个随机索引
          if (randomIndex == 10 || randomIndex == 60 || randomIndex == 100) {
            if (showText) {
              style.getText().setText(text);
            }
          } else {
            style.getText().setText("");
          }
        } else {
          style.getText().setOverflow(false);
          style.getText().setPlacement("line");
          index = 1 - index; // 切换 0 和 1
          style.getText().setOffsetX(index == 0 ? 0 : 1000);
          // style.getText().setTextAlign(index == 0 ? 'start' : 'end');
          style.getText().setOffsetY(index == 0 ? -10 : 10);
          if (showText) {
            style.getText().setText(text);
          }
        }
      } else {
        let scale = 1;
        if (zoom > mapObj.showLevel + 1) scale = 0.7;
        else scale = 0.65;
        let typeName = featureData.typeName || featureData.typename || "";
        if (typeName) {
          let textShowLevel = row?.textShowLevel || 6;
          typeName = zoom >= textShowLevel ? `${typeName}` : "";
          style.getText().setText(typeName);
        } else {
          style.getText().setText("");
        }

        let icon = "";
        if (row && row.icon) {
          icon = row.icon;
        } else if (mapObj.legendList) {
          icon = mapObj.legendList[0]?.icon;
        }
        // 单独处理结构监测图标
        if (featureData.layer === '1821371445660356609') {
          if (featureData.type === '边坡') {
            icon = require("@/assets/map/slope.png");
          }
          if (featureData.type === '隧道') {
            icon = require("@/assets/map/tunnel.png");
          }
          if (featureData.type === '桥梁') {
            icon = require("@/assets/map/bridge-n.png");
          }
        }
        // 单独处理监测预警图层
        if (row.name === "监测预警图层" && featureData.type == 1) {
          icon = require("@/assets/map/slope-red.png");
        }
        if (icon && featureType === "Point") {
          // 聚合样式
          if (!isNaN(Number(typeName)) && featureData.ifAgg) {
            style.getText().setOffsetY(0);
            style.getText().setFill(
              new Fill({
                color: "rgba(255,255,255, 1)", // 文本颜色
              })
            );
            style.setImage(
              new CircleStyle({
                anchor: [0.5, 0],
                radius: typeName - 0 > 99 ? 18 : 14,
                stroke: new Stroke({
                  color: "rgba(255,255,255,0.4)",
                  width: 1,
                }),
                fill: new Fill({
                  color: "rgb(64,158,255)",
                }),
              })
            );
          } else {
            style.getText().setOffsetY(-25);
            style.getText().setFill(
              new Fill({
                color: "rgba(255,255,255, 0.8)", // 文本颜色
              })
            );
            style.getText().setStroke(
              new Stroke({
                color: "rgba(255,255,255, 0)", // 描边
              })
            );
            // style.getText().setBackgroundFill(
            //   new Fill({
            //     color: "rgba(13,27,43,0.6)",
            //   })
            // );
            style.getText().setText("");
            style.getText().setOffsetY(-40);
            let index = 8;
            let newStr = typeName;
            if (typeName.length > index) {
              let num = isBig ? -110 : -50;
              style.getText().setOffsetY(num);
              newStr = typeName.slice(0, index) + "\n" + typeName.slice(index);
            }
            let newIndex = 20;
            if (newStr.length > newIndex) {
              let num = isBig ? -130 : -60;
              style.getText().setOffsetY(num);
              newStr =
                newStr.slice(0, newIndex) + "\n" + newStr.slice(newIndex);
            }
            style.getText().setText(newStr);
            style
              .getText()
              .setFont(isBig ? "28px Microsoft YaHei" : "13px Microsoft YaHei");
            //统一50*54图标 或自定义
            style.setImage(
              new Icon({
                anchor: [0.5, 1], //中心点
                src: icon || "",
                size: [50, 54], //实际尺寸 50*54
                scale: isBig ? 2.0 : scale, // scale
                crossOrigin: "anonymous",
              })
            );
          }
        } else {
          style.setImage(
            new CircleStyle({
              anchor: [0.5, 0],
              radius: 7,
              stroke: new Stroke({
                color: row?.borderColour,
                width: isBig ? row?.borderWidth * 2 || 2 : row?.borderWidth || 1,
              }),
              fill: new Fill({
                color: row?.interiorColour,
                width: isBig ? row?.borderWidth * 2 : row?.borderWidth,
              }),
            })
          );
        }
      }
      if (zIndex) {
        style.setZIndex(9);
      } else {
        style.setZIndex(999);
      }
      // 单独处理：路线要返回多个样式
      if ((featureData.shapeType == 2 || (featureType != "Point" && featureType != "MultiPoint")) && lineBase) {
        return [style, s2];
      }
      return style;
    },
  });
  vLayer.set("name", layerValue);
  vLayer.set(layerKey, layerValue);
  if (zIndex) {
    vLayer.setZIndex(zIndex);
  } else {
    vLayer.setZIndex(9003);
  }

  mapLayer.addLayer(vLayer);
  return vLayer;
}

// 添加遮罩
export function addMapMask(mapLayer, data, bgFill = "rgba(8,5,66,0.65)") {
  if (data) {
    // 先移除
    removeLayer(window.mapLayer, 'borderLayer');
    removeLayer(window.mapLayer, 'borderLayer', 'name');
    //数组
    let vectorSource = new VectorSource();
    let features = [];
    for (let i = 0; i < data.length; i++) {
      let mgeom = data[i].mgeom;
      const feature = new WKT().readFeature(mgeom, {
        dataProjection: "EPSG:4326",
        featureProjection: "EPSG:3857",
      });
      vectorSource.addFeature(feature);

      const feature4326 = new WKT().readFeature(mgeom);
      features.push(feature4326);
    }
    let initLayer = new VectorLayer({
      source: vectorSource,
      style: new Style({
        stroke: new Stroke({
          color: "rgba(1,179,246,0.2)",
          width: isBig ? 4 : 2.5,
          lineDash: [15, 15],
          lineCap: "round",
          lineJoin: "round",
        }),
      }),
    });
    initLayer.setZIndex(8888);
    initLayer.set("name", "borderLayer");
    mapLayer.addLayer(initLayer);
    // 添加反转
    const converGeom = erase(features);
    const convertFt = new Feature({
      geometry: converGeom.transform("EPSG:4326", "EPSG:3857"),
    });
    convertFt.setStyle(
      new Style({
        fill: new Fill({
          color: bgFill,
        }),
        stroke: new Stroke({
          color: "rgba(8,5,66,0)", //"#000000",
          width: 2,
        }),
      })
    );
    let maskLayer = new VectorLayer({
      source: new VectorSource({
        features: [convertFt],
      }),
    });
    maskLayer.setZIndex(9000);
    maskLayer.set("name", "maskLayer");
    maskLayer.setOpacity(0);
    mapLayer.addLayer(maskLayer);

    // 遮罩渐变淡入
    let opacity = 0;
    const animate = setInterval(() => {
      opacity += 0.1;
      if (opacity >= 1.0) {
        clearInterval(animate);
      }
      maskLayer.setOpacity(opacity);
    }, 100);

    return mapLayer;
  }
}
// 添加反转
function erase(fes) {
  const polygonRing = fromExtent([-180, -90, 180, 90]);
  // 擦除操作
  for (let i = 0, len = fes.length; i < len; i++) {
    let geom = fes[i].getGeometry();
    let muilt = false;
    if (geom instanceof GeomMultiPolygon) {
      muilt = true;
    }
    const coords = geom.getCoordinates();
    coords.forEach((coord) => {
      let arr = coord[0];
      if (!muilt) {
        arr = coord; //单面
      }
      const linearRing = new GeomLineString(arr);
      polygonRing.appendLinearRing(linearRing);
    });
  }
  return polygonRing;
}

// 返回操作-公共
export async function toBack(dept_ids = null, m_id = []) {
  let deptId = dept_ids ? dept_ids[0] : null;
  if (dept_ids) {
    store.commit("SET_DEPTID", deptId);
  } else {
    // 重新设置部门id 为登录用户的部门Id
    await store.dispatch("GetInfo").then((res) => {
      if (res.code == 200) {
        store.commit("SET_DEPTID", res.user.deptId);
        deptId = res.user.deptId;
      }
    });
  }
  removeAllLayer(window.mapLayer);
  removeLayer(window.mapLayer, "dataLayer");
  removeLayer(window.mapLayer, "maskLayer", "name");
  // 优化如果有存储测 不重新请求
  let rangeData = cache.session.getJSON("rangeData");
  if (dept_ids && dept_ids.length) {
    rangeData = rangeData.filter((v) => v.sys_dept_id == dept_ids[0]);
  }
  if (rangeData) {
    addWidthFeature(window.mapLayer, rangeData, "dataLayer", null, true, store.state.map.deptMinZoom);
    addMapMask(window.mapLayer, rangeData);
    let shape = rangeData[0]?.mgeom;
    let feature;
    // 如果是 deptId 为 1 定位到 坐标为 defCenter的位置
    if (isAdmin && rangeData.length > 1) {
      let wkt = format.writeGeometry(new Point(defCenter));
      feature = format.readFeature(wkt);
      let view = window.mapLayer.getView();
      view.setCenter(defCenter);
      view.setZoom(isBig ? 8.5 : 7);
    } else {
      feature = format.readFeature(shape, {
        dataProjection: "EPSG:4326",
        featureProjection: "EPSG:3857",
      });
      window.mapLayer.getView().fit(feature.getGeometry().getExtent(), {
        duration: 500,
      });
      let view = window.mapLayer.getView();
      view.setZoom(isBig ? 8.5 : 7);
    }
    store.dispatch("map/changeClickBoolean", false);
    let dept_id = isAdmin || !mapObj.deptId ? [] : [mapObj.deptId];
    let deptArr = [];
    if (dept_ids) {
      deptArr = dept_ids;
    } else {
      deptArr =
        mapObj.authDeptIds && mapObj.authDeptIds.length
          ? mapObj.authDeptIds
          : dept_id;
    }
    // store.dispatch("map/getTreeList", {
    //   deptIds: deptArr,
    //   maintenanceSectionIds: m_id || [],
    // });
    return;
  }
  // 获取行政规划
  return new Promise((resolve, reject) => {
    getShapeList({ sysDeptId: deptId }).then((res) => {
      if (res.code == 200 && res.data) {
        addWidthFeature(window.mapLayer, res.data, "dataLayer", null, true, store.state.map.deptMinZoom);
        addMapMask(window.mapLayer, res.data);
        let shape = res.data[0].mgeom;
        let feature;
        // 如果是 deptId 为 1 定位到 坐标为 defCenter的位置
        if (isAdmin) {
          let wkt = format.writeGeometry(new Point(defCenter));
          feature = format.readFeature(wkt);

          let view = window.mapLayer.getView();
          view.setCenter(defCenter);
          view.setZoom(7);
        } else {
          feature = format.readFeature(shape, {
            dataProjection: "EPSG:4326",
            featureProjection: "EPSG:3857",
          });
          window.mapLayer.getView().fit(feature.getGeometry().getExtent(), {
            duration: 500,
          });
        }
        store.dispatch("map/changeClickBoolean", false);
        let dept_id = isAdmin || !mapObj.deptId ? [] : [mapObj.deptId];
        let deptArr = [];
        if (dept_ids) {
          deptArr = dept_ids;
        } else {
          deptArr =
            mapObj.authDeptIds && mapObj.authDeptIds.length
              ? mapObj.authDeptIds
              : dept_id;
        }
        store.dispatch("map/getTreeList", {
          deptIds: deptArr,
          maintenanceSectionIds: m_id || [],
        });
        resolve(res.data);
      } else {
        reject("");
      }
    });
  });
}

// 将 GCJ-02 转换为 WGS-84 的函数
export function gcj02towgs84(lng, lat) {
  if (out_of_china(lng, lat)) {
    return [lng, lat];
  } else {
    var dlat = transformlat(lng - 105.0, lat - 35.0);
    var dlng = transformlng(lng - 105.0, lat - 35.0);
    var radlat = (lat / 180.0) * pi;
    var magic = Math.sin(radlat);
    magic = 1 - ee * magic * magic;
    var sqrtmagic = Math.sqrt(magic);
    dlat = (dlat * 180.0) / (((a * (1 - ee)) / (magic * sqrtmagic)) * pi);
    dlng = (dlng * 180.0) / ((a / sqrtmagic) * Math.cos(radlat) * pi);
    return [lng * 2 - (lng + dlng), lat * 2 - (lat + dlat)];
  }
}
/**
 * 将 WGS-84 坐标系中的经纬度坐标转换为 GCJ-02 坐标系中的经纬度坐标
 * @param {number} lng - WGS-84 坐标系中的经度
 * @param {number} lat - WGS-84 坐标系中的纬度
 * @returns {Array<number>} - 转换后的 GCJ-02 坐标系中的经纬度坐标
 */
export function wgs84togcj02(lng, lat) {
  // var x_PI = 3.14159265358979324 * 3000.0 / 180.0;
  var PI = 3.14159265358979324;
  var a = 6378245.0;
  var ee = 0.00669342162296594323;
  if (out_of_china(lng, lat)) {
    return [lng, lat];
  } else {
    var dlat = transformlat(lng - 105.0, lat - 35.0);
    var dlng = transformlng(lng - 105.0, lat - 35.0);
    var radlat = (lat / 180.0) * PI;
    var magic = Math.sin(radlat);
    magic = 1 - ee * magic * magic;
    var sqrtmagic = Math.sqrt(magic);
    dlat = (dlat * 180.0) / (((a * (1 - ee)) / (magic * sqrtmagic)) * PI);
    dlng = (dlng * 180.0) / ((a / sqrtmagic) * Math.cos(radlat) * PI);
    var mglat = lat + dlat;
    var mglng = lng + dlng;
    // return [lng * 2 - mglng, lat * 2 - mglat]
    return [mglng, mglat];
  }
}

// 判断是否在国内
function out_of_china(lng, lat) {
  return (
    lng < 72.004 || lng > 137.8347 || lat < 0.8293 || lat > 55.8271 || false
  );
}

// 转换纬度的函数
function transformlat(lng, lat) {
  var ret =
    -100.0 +
    2.0 * lng +
    3.0 * lat +
    0.2 * lat * lat +
    0.1 * lng * lat +
    0.2 * Math.sqrt(Math.abs(lng));
  ret +=
    ((20.0 * Math.sin(6.0 * lng * pi) + 20.0 * Math.sin(2.0 * lng * pi)) *
      2.0) /
    3.0;
  ret +=
    ((20.0 * Math.sin(lat * pi) + 40.0 * Math.sin((lat / 3.0) * pi)) * 2.0) /
    3.0;
  ret +=
    ((160.0 * Math.sin((lat / 12.0) * pi) + 320 * Math.sin((lat * pi) / 30.0)) *
      2.0) /
    3.0;
  return ret;
}

// 转换经度的函数
function transformlng(lng, lat) {
  var ret =
    300.0 +
    lng +
    2.0 * lat +
    0.1 * lng * lng +
    0.1 * lng * lat +
    0.1 * Math.sqrt(Math.abs(lng));
  ret +=
    ((20.0 * Math.sin(6.0 * lng * pi) + 20.0 * Math.sin(2.0 * lng * pi)) *
      2.0) /
    3.0;
  ret +=
    ((20.0 * Math.sin(lng * pi) + 40.0 * Math.sin((lng / 3.0) * pi)) * 2.0) /
    3.0;
  ret +=
    ((150.0 * Math.sin((lng / 12.0) * pi) +
      300.0 * Math.sin((lng / 30.0) * pi)) *
      2.0) /
    3.0;
  return ret;
}

// 获取图列图表
export async function getImageById(id) {
  return new Promise((resolve, reject) => {
    findFiles({ ownerId: id })
      .then((res) => {
        if (res.code === 200 && res.data) {
          let imgUrlList = res.data.map((file) => file.url);
          resolve(imgUrlList);
        } else {
          reject("");
        }
      })
      .catch(() => {
        reject("");
      });
  });
}

// 获取路线切片
export async function getLineConfig(managementMaintenanceIds = null) {
  let lineObj = cache.session.getJSON("lineObj");
  if (!lineObj) {
    await getMenuSub(lineId).then((res) => {
      if (res.code == 200 && res.data) {
        // 将路线配置信息存储到session
        cache.session.setJSON("lineObj", res.data);
        lineObj = res.data;
      }
    });
  }

  if (isAdmin && !managementMaintenanceIds) {
    managementMaintenanceIds.length = 0;
  }
  let layerName = "路网信息";
  addVectorTile(
    window.mapLayer,
    "name",
    layerName,
    JSON.stringify({ id: lineId, managementMaintenanceIds }),
    lineObj,
    true,
    9001,
    1
  );
  let arr = [
    {
      layerName,
      ifShow: true,
      opacity: 100,
      name: layerName,
    },
  ];
  store.commit("map/setLayer", { layer: arr });
}

/**
 * 测距、测面
 * @param {*} type
 */
export function onMeasure(type = "LineString", bool = false, callback) {
  let style = new Style({
    fill: new Fill({
      color: bool ? "rgba(252,55,2, 0.3)" : "rgba(255, 255, 255, 0.2)",
    }),
    stroke: new Stroke({
      color: "#FC3702",
      width: isBig ? 5 : 3,
      lineDash: bool ? [0, 0] : [10, 10],
    }),
    text: new Text({
      text: "",
      fill: new Fill({ color: "#333333" }),
      stroke: new Stroke({ color: "#fff", width: 2 }),
      offsetY: 20,
    }),
  });
  let labelStyle = new Style({
    text: new Text({
      font: isBigScreen()
        ? "26px Calibri,sans-serif"
        : "12px Calibri,sans-serif",
      fill: new Fill({
        color: "rgba(255, 255, 255, 1)",
      }),
      backgroundFill: new Fill({
        color: "rgba(0, 0, 0, 0.4)",
      }),
      padding: [3, 3, 3, 3],
      textBaseline: "bottom",
      offsetY: -5,
    }),
    image: new RegularShape({
      radius: 6,
      points: 3,
      angle: Math.PI,
      displacement: [0, 10],
      fill: new Fill({
        color: "rgba(0, 0, 0, 0.7)",
      }),
    }),
  });
  let tipStyle = new Style({
    text: new Text({
      font: isBig ? "26px Calibri,sans-serif" : "12px Calibri,sans-serif",
      fill: new Fill({
        color: "rgba(255, 255, 255, 1)",
      }),
      backgroundFill: new Fill({
        color: "rgba(0, 0, 0, 0.4)",
      }),
      padding: [2, 2, 2, 2],
      textAlign: "left",
      offsetX: 15,
    }),
  });
  //加载测量的绘制矢量层
  window.drawSource = new VectorSource(); //图层数据源

  let modify = new Modify({
    source: window.drawSource,
  });
  window.mapLayer.addInteraction(modify);

  const layer = new VectorLayer({
    source: window.drawSource,
    style: (feature) => {
      let styles = [style];
      const geometry = feature.getGeometry();
      const drawType = geometry.getType();
      if (drawType == "LineString") {
        const length = geometry.getLength();
        let lengthText = `${length.toFixed(2)}m`;
        // 转化
        if (length >= 1000) {
          lengthText = Math.round((length / 1000) * 100) / 100 + " " + "km";
        } else {
          lengthText = Math.round(length * 100) / 100 + " " + "m";
        }
        // 更新文本样式以显示长度
        labelStyle.getText().setText("距离：" + lengthText);
      } else if (drawType == "Polygon") {
        const area = getArea(geometry);
        let output;
        if (area > 1000000) {
          output = Math.round((area / 1000000) * 100) / 100 + " km\xB2";
        } else {
          output = Math.round(area * 100) / 100 + " m\xB2";
        }
        labelStyle.getText().setText("面积：" + output);
      }
      if (!bool) {
        styles.push(labelStyle);
      }
      return styles;
    },
  });
  layer.set("name", "drawLayer");
  layer.setZIndex(9002);
  window.mapLayer.addLayer(layer);
  if (window.draw) {
    // 绘制
    window.draw.finishDrawing(); // 绘制完成
    window.mapLayer.removeInteraction(window.draw);
    window.draw = null;
  }
  const activeTip = "点击继续测量 " + (type === "Polygon" ? "面积" : "距离") + "，双击结束。";
  const idleTip = bool ? "点击开始绘制" : "点击开始测量";
  let tip = idleTip;
  window.draw = new Draw({
    source: window.drawSource,
    type,
    style: (feature) => {
      let styles = [style];
      tipStyle.getText().setText(tip);
      styles.push(tipStyle);
      return styles;
    },
  });

  window.draw.on("drawstart", (evt) => {
    window.drawSource.clear();
    if (!bool) {
      tip = activeTip;
    }
  });
  window.draw.on("drawend", (evt) => {
    tip = idleTip;
    // window.mapLayer.removeInteraction(window.draw);
    if (callback) {
      const feature = evt.feature; // 获取绘制的Feature
      callback(feature);
    }
  });

  window.mapLayer.addInteraction(window.draw);
}

// 匹配本地存储的 行政区数据
export function onMatching(deptId = null) {
  let rangeData = cache.session.getJSON("rangeData");
  if (!rangeData) return null;
  if (!deptId) {
    return rangeData;
  } else {
    let arr = rangeData.filter(
      (v) => v.sys_dept_id == deptId || v.dept_id == deptId
    );
    return arr;
  }
}

/**
 * 添加热力图到地图上
 * 该函数用于在地图上添加热力图图层，显示数据的分布密度。
 * 具体实现细节需要根据地图库和数据格式进行调整。
 *
 * @returns {void} 该函数不返回任何值，直接在地图上添加热力图图层。
 */
export function addHeatmap(dataDto, layerKey, layerValue) {
  let features = new GeoJSON().readFeatures(dataDto, {
    featureProjection: 'EPSG:3857',
  });
  let vectorSource = new sourceVector({
    features: features,
  })
  // 热力图图层
  const heatmapLayers = new HeatmapLayer({
    source: vectorSource,
    minZoom: 6,
    // maxZoom: 15,
    // 热点半径
    radius: parseInt(10, 8),
    // 模糊尺寸
    blur: parseInt(20, 15),
    opacity: 1, // 透明度，默认1
    // 权重
    weight: function (feature) {
      return feature.get("value");
    },
    // 颜色 渐变
    gradient: ["rgba(0,0,255,0.8)", "rgba(0,255,0,0.8)", "rgba(255, 153, 0,1)", "rgba(255, 0, 0,0.6)"], // 热图的颜色梯度，指定为一个CSS颜色字符串数组。
  });
  heatmapLayers.set("name", layerKey);
  heatmapLayers.set(layerKey, layerValue);
  heatmapLayers.setZIndex(9001);
  window.mapLayer.addLayer(heatmapLayers);
}

// 地图打印、下载功能
export function exportToPNG(startDownPx, endDownPx) {
  let map = window.mapLayer
  let size = []
  size[0] = endDownPx[0] - startDownPx[0]
  size[1] = endDownPx[1] - startDownPx[1]
  map.once('rendercomplete', function () {
    const mapCanvas = document.createElement('canvas');
    mapCanvas.width = size[0];
    mapCanvas.height = size[1];
    const mapContext = mapCanvas.getContext('2d');
    Array.prototype.forEach.call(map.getViewport().querySelectorAll('.ol-layer canvas, canvas.ol-layer'), function (canvas) {
      if (canvas.width > 0) {
        const opacity = canvas.parentNode.style.opacity || canvas.style.opacity;
        mapContext.globalAlpha = opacity === '' ? 1 : Number(opacity);

        const backgroundColor = canvas.parentNode.style.backgroundColor;
        if (backgroundColor) {
          mapContext.fillStyle = backgroundColor;
          mapContext.fillRect(startDownPx[0], startDownPx[1], canvas.width, canvas.height);
        }
        let matrix;
        const transform = canvas.style.transform;
        if (transform) {
          // Get the transform parameters from the style's transform matrix
          matrix = transform
            // eslint-disable-next-line no-useless-escape
            .match(/^matrix\(([^\(]*)\)$/)[1]
            .split(',')
            .map(Number);
        } else {
          matrix = [parseFloat(canvas.style.width) / canvas.width, 0, 0, parseFloat(canvas.style.height) / canvas.height, 0, 0];
        }
        // Apply the transform to the export map context
        CanvasRenderingContext2D.prototype.setTransform.apply(mapContext, matrix);
        mapContext.drawImage(canvas, -startDownPx[0], -startDownPx[1]);
      }
    });
    if (navigator.msSaveBlob) {
      // link download attribute does not work on MS browsers
      navigator.msSaveBlob(mapCanvas.msToBlob(), 'map.png');
    } else {
      const base64 = mapCanvas.toDataURL();
      const fileName = 'map.png';
      let link = document.createElement('a');
      link.href = base64;
      link.style.display = 'none';
      link.download = fileName;
      document.body.appendChild(link);
      link.click();
      link.parentNode.removeChild(link);
      downloadFile(base64, fileName);
    }
    // window.mapLayer.removeInteraction(window.draw)
    // window.draw = null;
  });
  map.renderSync();
}
// 下载文件（为地图打印功能所用）
const downloadFile = (base64, fileName) => {
  var link = document.createElement('a');
  link.download = fileName;
  link.href = window.URL.createObjectURL(blobToFile(baseToBlob(base64), fileName));
  link.click();
}
// base64 转 blob（为地图打印功能所用）
const baseToBlob = (baseData) => {
  var arr = baseData.split(',');
  var mime = arr[0].match(/:(.*?);/)[1];
  var bstr = atob(arr[1]);
  var n = bstr.length;
  var u8arr = new Uint8Array(n);
  while (n--) {
    u8arr[n] = bstr.charCodeAt(n);
  }
  return new Blob([u8arr], { type: mime });
}
// blob转file（为地图打印功能所用）
const blobToFile = (theBlob, fileName) => {
  let files = new File([theBlob], fileName, { type: 'image/png' });
  theBlob.lastModifiedDate = new Date();
  theBlob.name = fileName;
  return files;
}
// 绘制矩形-需要打印的矩形范围（为地图打印功能所用）
export function handleRectangle() {
  let map = window.mapLayer;
  let startDownPx = []
  let endDownPx = []
  const source = new VectorSource({ wrapX: false });
  const vector = new VectorLayer({
    source: source,
  });
  vector.set("name", "rectangle");
  vector.setZIndex(9002);
  map.addLayer(vector)
  window.draw = new Draw({
    source: source,
    type: 'Circle',
    geometryFunction: createBox(),
  });
  map.addInteraction(window.draw);
  window.draw.on("drawstart", (evt) => {
    startDownPx = evt.target.downPx_
  })
  window.draw.on("drawend", (evt) => {
    endDownPx = evt.target.downPx_
    let loadingInstance = Loading.service({
      text: '下载中...',
      background: 'rgba(0, 0, 0, 0.7)',
    });
    setTimeout(() => {
      removeLayer(map, "rectangle");
    }, 500)
    setTimeout(() => {
      removeLayer(map, "rectangle");
      exportToPNG(startDownPx, endDownPx)
      loadingInstance.close()
    }, 1000)
  });
}

// 聚合 - 点
export function addCluster(data = [], icons = null, callback = null, layerKey = 'name', layerValue = 'clusterLayer') {
  if (!data.length) return;
  let features = [];
  data.forEach((item, index) => {
    let shape = item.shape || null;
    let feature = null;
    item.typeName = item.name || item.typeName || "收费站";
    item.listQueryConfig = {
      detlCondf: {
        path: item.typeStr === 'bridge' ? './baseData/bridge/baseInfo/archives/index.vue' : item.typeStr === 'tunnel' ? './baseData/tunnel/baseInfo/archives/index.vue' : './baseData/ancillary/baseInfo/commonForm.vue',
        type: item.typeStr === 'bridge' ? 2 : 1, // 1：组件 2：路由 3：接口
      },
    }
    item.mainTypeId = item.mainTypeId || 8;
    item.items = {};
    if (shape) {
      feature = format.readFeature(shape, {
        dataProjection: "EPSG:4326",
        featureProjection: "EPSG:3857",
      });
    } else {
      // 如果 没有 longitude 和 latitude 字段，跳出本次循环进入下一次循环
      if (!item.longitude || !item.latitude) {
        return;
      }
      // 将经纬度转化为 ol.geometry.Point 对象
      let lonLat = [item.longitude - 0, item.latitude - 0];
      let geometry = new Point(lonLat);
      // 将经纬度转化为 wkt 格式
      let wkt = new WKT().writeGeometry(geometry);
      feature = format.readFeature(wkt, {
        dataProjection: "EPSG:4326",
        featureProjection: "EPSG:3857",
      });
    }
    feature.set("data", item);
    features = [...features, ...[feature]];
  });
  let source = new VectorSource({
    features: features,
  });
  // 聚合
  let clusterSource = new Cluster({
    distance: 40,
    minDistance: parseInt(20, 10),
    source: source,
  });
  // 缓存不同架合数量对应的样式
  let styles = [];
  const clusterLayer = new VectorLayer({
    source: clusterSource,
    style: (fea) => {
      let size = fea.get('features').length;
      let style = styles[size];
      let item = fea.get('features')[0].get('data');
      const color = size > 15 ? '#ff0000' : size > 10 ? '#ff9900' : '#00ff00'; // 颜色分级
      if (!style) {
        if (size > 3) {
          style = new Style({
            image: new CircleStyle({
              radius: 8 + Math.log2(size) * 2,
              stroke: new Stroke({
                color: 'rgba(255,255,255,0.6)',
              }),
              fill: new Fill({
                color: color,
              }),
            }),
            text: new Text({
              text: size.toString(),
              fill: new Fill({
                color: '#fff',
              }),
            }),
          });
          styles[size] = style;
        } else {
          style = new Style({
            text: new Text({
              text: item.typeName || item.typename || item.name || "",
              fill: new Fill({
                color: "#fff",
              }),
              stroke: new Stroke({
                color: "#333",
                width: 2,
              }),
              // 设置文本的偏移量，以便它出现在指定的位置
              offsetX: 0,
              offsetY: isBig ? -130 : -50,
              // 设置文本的位置
              textAlign: "center",
              textBaseline: "middle",
              // 设置文本的大小
              font: isBig ? "0.5vw Arial" : "12px Arial",
              backgroundFill: new Fill({
                color: "rgba(13,27,43,0.6)",
              }),
              padding: [2, 5, 2, 5],
            }),
            image: new Icon({
              anchor: [0.5, 1],
              src: icons || require("@/assets/map/piont.png"), // 图标图片路径
              scale: isBig ? 2 : 0.8,
            }),
          })
        }
      }
      return style;
    }
  });
  clusterLayer.set(layerKey, layerValue);
  clusterLayer.setZIndex(9005);
  window.mapLayer.addLayer(clusterLayer);

  window.mapLayer.on('click', (evt) => {
    if (callback) {
      let feature = window.mapLayer.forEachFeatureAtPixel(
        evt.pixel,
        (feature) => feature,
        {
          layerFilter: (e) => {
            let layerName = ["maskLayer", "dataLayer", "clickLayer", 'pbfLayer', '路网信息'];
            return e.get("name") && !layerName.includes(e.get("name"));
          },
          hitTolerance: isBig ? 40 : 20, //像素容差率 默认0
        }
      );
      if (!feature) return;
      let features = feature.get("features");
      if (!features && feature instanceof Feature) {
        features = [feature];
      } else if (!features) {
        let fea = toFeature(feature, feature.getType());
        features = [fea];
      }
      callback(features[0]);
    }
  });
}

// 构建 不同类型的图层
export function buildLayer(data = []) {
  let layerArr = [];
  if (data && data.length) {
    data.forEach((item, index) => {
      if (item.serviceType === 'XYZ') {
        layerArr[index] = new TileLayer({
          name: item.layerName,
          zIndex: item.showIndex || 8000,
          source: new XYZ({
            url:
              item.layerUrl ||
              `http://t{0-7}.tianditu.gov.cn/DataServer?T=img_w&x={x}&y={y}&l={z}&tk=${key}`,
            attributions: item.layerName,
            wrapX: false, // 是否水平包裹
            crossOrigin: "anonymous", // 常见的设置为 'anonymous' 或 'use-credentials'
            maxZoom: 18,
          }),
          preload: Infinity,
          visible: item.layerUrl ? !!item.ifShow : true,
        });
      } else if (item.serviceType === 'WMS') {
        layerArr[index] = new TileLayer({
          name: item.layerName,
          zIndex: 9002,
          source: new TileWMS({
            url: item.layerUrl,
            params: {
              FORMAT: "image/png", // 图像格式，如image/png
              LAYERS: "0", // 图层名称
              TILED: true, // 使用切片
              VERSION: "1.3.0", // WMS版本号，默认1.1.1
              STYLES: "", // 图层样式
            },
            attributions: item.layerName,
            transition: 0,
            serviceType: 'geoserver',
          }),
          visible: item.layerUrl ? !!item.ifShow : true,
        });
      } else if (item.serviceType === 'TMS') {
        layerArr[index] = new TileLayer({
          name: item.layerName,
          zIndex: 9002,
          source: new TileImage({
            url: item.layerUrl,
            attributions: item.layerName,
            crossOrigin: "anonymous",
            tileLoadFunction: (image, src) => {
              image.getImage().src = src;
            },
          }),
          visible: item.layerUrl ? !!item.ifShow : true,
        });
      }
    });
  }
  return layerArr;
}

// 添加 gif 图层
export function addGifLayer(data = [], layerName = 'gifLayer', zIndex = 9002) {
  let features = [];
  if (data && data.length) {
    let mapEl = document.getElementById('map')
    const zoom = Math.round(window.mapLayer.getView().getZoom());
    data.forEach((item, index) => {
      let { name, center, gifUrl } = item;
      // const gif = gifler(gifUrl);
      if (center) {
        let feature = wktToFeature(center);
        feature.set('data', item)
        // 检查页面是否已存在 id 为 "gif-" + index 的 DOM 元素，若存在则移除
        const existingGifElement = document.getElementById("gif-" + index);
        if (existingGifElement && existingGifElement.parentNode) {
          existingGifElement.parentNode.removeChild(existingGifElement);
        }
        // gif.frames(
        //   document.createElement('canvas'),
        //   function (ctx, frame) {
        //     let width = frame.width;
        //     let height = frame.height;
        //     let x = frame.x;
        //     let y = frame.y;
        //     ctx.canvas.width = width;
        //     ctx.canvas.height = height;
        //     feature.setStyle(new Style({
        //       image: new Icon({
        //         anchor: [0.5, 1],
        //         img: ctx.canvas,
        //         imgSize: [width, height],
        //         opacity: 1.0,
        //         scale: isBig ? 1.0 : 0.1, // scale
        //       }),
        //     }));
        //     ctx.clearRect(0, 0, width, height);
        //     ctx.drawImage(frame.buffer, x, y);
        //   }
        // );
        let oDiv = document.createElement("span");
        // 给 oDiv 添加属性和样式
        oDiv.id = "gif-" + index;
        oDiv.style.display = zoom >= 10 ? 'block' : 'none';
        oDiv.style.width = '35px'
        oDiv.style.height = '35px'
        oDiv.style.background = 'url(' + gifUrl + ') no-repeat'
        oDiv.style.backgroundSize = "100% 100%"

        mapEl.appendChild(oDiv)
        let { lon, lat } = getLonLat(center)
        let markerPoint = new Overlay({
          position: fromLonLat([lon, lat]),
          positioning: "center-center",
          element: document.getElementById("gif-" + index),
          stopEvent: false
        });

        markerPoint.set("name", layerName)
        markerPoint.set("layerId", layerName);
        window.mapLayer.addOverlay(markerPoint);

        features.push(feature);
      }
    });

    let gifLayer = new VectorLayer({
      source: new VectorSource({
        features: features,
      }),
      // style: '',
      minZoom: 8,
    });
    gifLayer.set("name", layerName);
    gifLayer.set("layerId", layerName);
    gifLayer.setZIndex(zIndex);
    // window.mapLayer.addLayer(gifLayer);
    // 监听 地图层级 修改 图标显示
    // window.mapLayer.getView().on("change:resolution", () => {
    //   const zoomLevel = Math.round(window.mapLayer.getView().getZoom());

    // });
  }
  return features;
}

// 添加 天气 图层数据
export function addWeatherLayer(data = [], layerName = 'weatherLayer', zIndex = 9001) {
  let features = [];
  if (data && data.length) {
    data.forEach((item, index) => {
      let { geometry, color } = item;
      let feature = wktToFeature(geometry);
      if (feature) {
        item.layerName = layerName;
        feature.set('data', item);
      }
      let style = new Style({
        fill: new Fill({
          color: color,
        }),
        stroke: new Stroke({
          color: color,
          width: 1,
        }),
        image: new CircleStyle({
          radius: 10,
          fill: new Fill({
            color: color,
          }),
        }),
        text: new Text({
          text: '',
          font: isBig ? "0.4vw Calibri,sans-serif" : "14px Calibri,sans-serif",
          fill: new Fill({ color: "#333333" }),
          stroke: new Stroke({ color: "#fff", width: 2 }),
          offsetY: isBig ? - 70 : -30,
        }),
      });
      feature.setStyle(style);
      features.push(feature);
    });

    let cLayer = new VectorLayer({
      source: new VectorSource({
        features: features,
      }),
      // style: '',
    });
    cLayer.set("name", layerName);
    cLayer.set("layerId", layerName);
    cLayer.setZIndex(zIndex);
    window.mapLayer.addLayer(cLayer);
  }
  return features;
}