<template>
  <div v-if="!dictLoading">{{ dictLabel }}</div>
</template>

<script>
import { getDictData } from '@/api/oneMap/layerData'
export default {
  props: {
    dictType: {
      type: [String, Number],
      default: ''
    },
    dictValue: {
      type: [String, Number],
      default: ''
    },
  },
  data() {
    return {
      dictLabel: '',
      dictLoading: true,
    }
  },
  watch: {
    dictType: {
      handler(val) {
        if (val) {
          this.getDict();
        }
      },
      deep: true,
      immediate: true,
    },
  },
  methods: {
    // 根据类型获取字典数据
    getDict() {
      this.dictLoading = true;
      getDictData({ dictType: this.dictType }).then(res => {
        if (res.code == 200 && res.data) {
          let arr = res.data.filter(v => v.dictValue == this.dictValue)
          if (arr && arr.length > 0) {
            this.dictLabel = arr[0].dictLabel
            return arr[0].dictLabel
          } else {
            return ''
          }
        } else {
          return ''
        }
      }).finally(() => {
        this.dictLoading = false;
      })
    },
  },
}
</script>

<style lang="scss" scoped></style>