<template>
  <div style="height: 100%; position: relative;">
    <div class="map-view" id="earlyMap" ref="mapRef"></div>
    <!-- 点击弹窗 -->
    <!-- <div ref="infoWindow" class="ol-popup">
      <h3>{{ clickedPoint.name }}</h3>
      <p><span>ID:</span> {{ clickedPoint.id }}</p>
      <p><span>坐标:</span> {{ clickedPoint.lon }}, {{ clickedPoint.lat }}</p>
    </div> -->
  </div>
</template>
<script>
import 'ol/ol.css';
import { Map, View, Feature } from 'ol';
import TileLayer from 'ol/layer/Tile';
import XYZ from 'ol/source/XYZ';
import { fromLonLat } from 'ol/proj';
import TileWMS from 'ol/source/TileWMS';
import VectorLayer from 'ol/layer/Vector';
import Overlay from "ol/Overlay";
import VectorSource from 'ol/source/Vector';
import { defaults as defaultsControl } from 'ol/control';
import { Circle as CircleStyle, Fill, Stroke, Style, Icon, Text } from 'ol/style';
import { Point } from 'ol/geom';
import { getShapeList } from "@/api/oneMap/deptInfo";
import { addMapMask } from "@/views/map/components/common/mapFun";

import pointIcon from '@/assets/map/point.png';
import highlightIcon from '@/assets/map/denger.gif'; // 高亮图标（需准备）
import { recursionNodeUnderStructure } from '@/api/HealthMonitoring/index'
import { fetchGet } from '../api.js'
import { warningRecordPage } from '@/api/jgjc/earlyWarning/deviceModel.js'
import testdata from './data.json';
let map = null;
const key = 'cde0b56cf882626889981701109a7536';
let layer1 = new TileLayer({
  name: '天地图矢量图层',
  source: new XYZ({
    url: `http://t{0-7}.tianditu.gov.cn/DataServer?T=img_w&x={x}&y={y}&l={z}&tk=${key}`,
    attributions: '天地图的属性描述',
    wrapX: false,
    maxZoom: 18,
  }),
  preload: Infinity,
  visible: true,
});
let layer3 = new TileLayer({
  source: new TileWMS({
    ratio: 1,
    url: 'https://gis.glyhgl.com/server/services/yanghu/gis_route_shape4/MapServer/WMSServer',
    params: {
      FORMAT: 'image/png',
      LAYERS: '0',
      TILED: true,
      VERSION: '1.3.0',
      STYLES: '',
    },
    serverType: 'geoserver',
    visible: true,
  }),
});
const pointLayer = new VectorLayer({
  source: new VectorSource(),
  zIndex: 999,
});
const blinkingPointLayer = new VectorLayer({
  source: new VectorSource(),
  // zIndex: 999,
});
const radarLayer = new VectorLayer({
  source: new VectorSource(),
  zIndex: 998,
});
let layers = [layer1, layer3, radarLayer, pointLayer, blinkingPointLayer];
let overlay = null
export default {
  data() {
    return {
      radarAnimations: [],
      blinkIntervalList: [],
      localRadarPoints: [{
        id: 1,
        lon: 103.645834,
        lat: 22.771567,
        name: '雷达监测点',
        icon: pointIcon,
        radar: {
          enable: true,         // 是否启用雷达扩散效果
          color: '#ff5722',     // 扩散圆环的颜色（橙红色）
          startRadius: 15,       // 扩散起始半径（0表示从中心点开始）
          maxRadius: 20,        // 扩散最大半径（像素单位）
          speed: 4,             // 扩散速度（每帧增加的像素值）
          alpha: 0.8,           // 扩散圆环的最大透明度（0-1之间）
          cycle: 1000           // 扩散动画的完整周期（毫秒）
        }
      }],
      showInfoWindow: false,       // 控制弹窗显示
      clickedPoint: {},          // 点击的点位数据
      infoWindowX: 0,              // 弹窗X坐标
      infoWindowY: 0,              // 弹窗Y坐标
      highlightedFeature: null,    // 高亮的要素
      cacheStructure: null,
    };
  },
  props: {
    isBig: {
      type: Boolean,
      default: false
    },
  },
  mounted() {
    this.$nextTick(() => {
      this.initMap();
    });
  },
  methods: {
    initMap() {
      if (map) {
        map.setTarget(null);
        map.getLayers().forEach(layer => layer.getSource().clear());
        map = null;
      }
      /* overlay = new Overlay({
        element: this.$refs.infoWindow,
        autoPan: {
          animation: {
            duration: 250,
          },
        },
      }); */
      map = new Map({
        target: 'earlyMap',
        layers,
        // overlays: [overlay],
        view: new View({
          projection: 'EPSG:3857',
          center: fromLonLat([102.0007599, 23.7085797]),
          zoom: 8,
          wrapX: false,
          crossOrigin: "anonymous",
          // maxZoom: 18,
          minZoom: 4,
        }),
        controls: defaultsControl({
          attribution: false,
          zoom: false,
        }).extend([]),
      });

      this.radarAnimations.forEach(clearInterval);
      this.radarAnimations = [];
      this.loadAllPoints();

      // **添加地图点击事件监听**
      map.on('click', this.handleMapClick);
    },

    async loadAllPoints() {
      pointLayer.getSource().clear();
      radarLayer.getSource().clear();
      const cachePoints1 = this.getCachePoints1();
      const cachePoints = await this.getCachePoints();
      /* const allPoints = [...this.localRadarPoints, ...cachePoints].filter(
        (item, index, arr) => arr.findIndex(p => p.id === item.id) === index
      );
      allPoints.forEach(point => {
        if (this.localRadarPoints.some(p => p.id === point.id)) {
          this.addRadarPoint(point);
        } else {
          // this.addNormalPoint(point);
        }
      }); */

      cachePoints.forEach((item) => {
        this.addPoint(item.type, item, item.name, item.id)
      })
      cachePoints1.forEach((item) => {
        this.addNormalPoint(item);
      })
      if (cachePoints1.length > 0) {
        const firstPoint = cachePoints1[0];
        this.focusOnLocation([firstPoint.lon, firstPoint.lat]);
      }
    },
    async getwarningRecord() {
      const params = {
        pageNumber: 1,
        pageSize: 50,
        structureCode: this.cacheStructure.code,
        structId: this.cacheStructure.id,
      };
      try {
        const res = await warningRecordPage(params);
        return res?.code === 200 ? res.rows : [];
      } catch (error) {
        console.log(error, '获取预警记录失败');
        return testdata;
      }
    },
    getCachePoints1() {
      try {
        const data = localStorage.getItem('mapData');
        if (!data) return [];
        const parsedData = JSON.parse(data);
        this.cacheStructure = Array.isArray(parsedData) ? parsedData[0] : parsedData
        return Array.isArray(parsedData) ? parsedData : [parsedData];
      } catch (error) {
        console.error('读取缓存点位失败', error);
        return [];
      }
    },
    async getCachePoints() {
      try {
        /* const data = localStorage.getItem('mapData');
        if (!data) return [];

        const parsedData = JSON.parse(data);
        let parsedData1 = []
        // const structureNodeCode = this.$route.query.code;
        const structureNodeCode = '1703594205844078593';
        const res = await recursionNodeUnderStructure({ structureNodeCode })
        parsedData1 = res.data.map((item) => {
          item.lon = Number(item.longitude)
          item.lat = Number(item.latitude)
          // item.name = `${item.installLocation} ${item.structureName}`
          item.name = item.installLocation
          item.icon = item.highlightIcon
          return item
        })
        return Array.isArray(parsedData) ? [...parsedData, ...parsedData1] : [parsedData, ...parsedData1]; */
        let parsedData1 = []
        const structureNodeCode = this.$route.query.code;
        const res = await recursionNodeUnderStructure({ structureNodeCode })
        const res1 = await this.getwarningRecord()
        parsedData1 = res.data.map((item) => {
          item.lon = Number(item.longitude)
          item.lat = Number(item.latitude)
          item.name = item.installLocation
          res1.forEach((citem) => {
            if (item.installLocation == citem.installLocation) {
              item.type = 2
            } else {
              item.type = 1
            }
          })
          return item
        })
        return parsedData1;
      } catch (error) {
        console.error('读取缓存点位失败1', error);
        let parsedData1 = []
        const structureNodeCode = this.$route.query.code;
        const res = await recursionNodeUnderStructure({ structureNodeCode })
        parsedData1 = res.data.map((item) => {
          item.lon = Number(item.longitude)
          item.lat = Number(item.latitude)
          item.name = item.installLocation
          item.type = 1
          return item
        })
        return parsedData1;
      }
    },

    addRadarPoint(pointData) {
      if (!pointData.radar || !pointData.radar.enable) return;

      const pointFeature = new Feature({
        geometry: new Point(fromLonLat([pointData.lon, pointData.lat])),
        pointData: pointData  // 将点位数据存储到要素中
      });

      const { color, maxRadius, alpha } = pointData.radar;
      const pulseStyle = new Style({
        image: new Icon({
          src: pointData.icon,
          scale: 0.6,
          anchor: [0.5, 0.5],
          className: 'pulse-animation'
        }),
        overlay: new CircleStyle({
          radius: maxRadius,
          stroke: new Stroke({ color, width: 1 }),
          fill: new Fill({ color: `rgba(255, 87, 34, ${alpha * 0.5})` })
        })
      });

      pointFeature.setStyle(pulseStyle);
      pointFeature.setProperties({ id: pointData.id });
      pointLayer.getSource().addFeature(pointFeature);

      this.createRadarAnimation(pointData);
    },

    addNormalPoint(pointData) {
      const pointFeature = new Feature({
        geometry: new Point(fromLonLat([pointData.lon, pointData.lat])),
        pointData: pointData  // 将点位数据存储到要素中
      });

      const iconStyle = new Style({
        image: new Icon({
          src: pointData.icon || 'https://picsum.photos/40/40?random=default',
          scale: 0.6,
          anchor: [0.5, 0.5],
        }),
      });

      const textStyle = new Style({
        text: new Text({
          text: pointData.name,
          font: '12px Microsoft YaHei',
          fill: new Fill({ color: '#ffffff' }),
          offsetY: -30,
          textAlign: 'center',
        }),
      });

      pointFeature.setStyle([iconStyle, textStyle]);
      pointFeature.setProperties({ id: pointData.id });
      pointLayer.getSource().addFeature(pointFeature);
    },
    addPoint(type, pointData, textContent = '', id) {
      // 创建标注点要素
      const pointFeature = new Feature({
        geometry: new Point(fromLonLat([pointData.lon, pointData.lat])),
        pointData: pointData  // 将点位数据存储到要素中
      });
      // 颜色配置参数
      const colors = ['#fe4646', '#42ABFF', '#32da49']; // 红/绿/蓝三色
      let colorIndex = type;
      const blinkSpeed = 100; // 闪烁速度（毫秒）
      const _this = this
      function createHaloStyle() {
        let radius = _this.isBig ? 26 : 6
        let haloRadius = _this.isBig ? 48 : 14
        return [
          // 光晕层
          new Style({
            image: new CircleStyle({
              radius: haloRadius,
              fill: new Fill({
                color: colors[colorIndex] + '66',
              }),
            }),
          }),
          // 核心闪烁层
          new Style({
            image: new CircleStyle({
              radius: radius,
              fill: new Fill({
                color: colors[colorIndex] + 'CC', // 添加透明度（CC=80%）
              }),
              stroke: new Stroke({
                color: colors[colorIndex],
                width: 5,
              }),
            }),
            text: new Text({
              text: textContent,
              font: _this.isBig ? '48px sans-serif' : '16px sans-serif',
              fill: new Fill({
                color: '#ffffff' // 文字颜色
              }),
              stroke: new Stroke({
                color: '#000000', // 文字描边颜色
                width: 2
              }),
              offsetY: -30,
              textAlign: 'center',
              className: 'line-text' // 添加自定义class
            })
          })
        ];
      }

      // 设置初始样式
      pointFeature.setStyle(createHaloStyle());
      pointFeature.setProperties(
        {
          id: id
        }
      )

      if (type == 2) {
        // 启动颜色切换定时器
        const blinkInterval = setInterval(() => {
          colorIndex = (colorIndex + 1) % colors.length;
          pointFeature.setStyle(createHaloStyle());
        }, blinkSpeed);
        this.blinkIntervalList.push(blinkInterval)
        // 销毁时清除定时器（重要！）
        pointFeature.on('change', function (e) {
          if (e.target.getGeometry() === null) {
            clearInterval(blinkInterval);
          }
        });
      }
      // 将新点添加到图层
      if (type === 2) {
        blinkingPointLayer.getSource().addFeature(pointFeature);
        // ...（保持原有闪烁逻辑）...
      } else {
        pointLayer.getSource().addFeature(pointFeature);
      }
    },
    createRadarAnimation(pointData) {
      const { lon, lat, radar } = pointData;
      const { color, startRadius, maxRadius, speed, alpha, cycle } = radar;
      const totalFrames = Math.ceil(maxRadius / speed);
      let frame = 0;

      const animation = setInterval(() => {
        radarLayer.getSource().clear();
        frame = (frame + 1) % totalFrames;
        const currentRadius = startRadius + frame * speed;
        const currentAlpha = alpha * (1 - frame / totalFrames);

        if (currentRadius > 0) {
          const radarFeature = new Feature({
            geometry: new Point(fromLonLat([lon, lat]))
          });
          const radarStyle = new Style({
            image: new CircleStyle({
              radius: currentRadius,
              stroke: new Stroke({ color, width: 2 }),
              fill: new Fill({ color: `rgba(255, 87, 34, ${currentAlpha})` })
            })
          });
          radarFeature.setStyle(radarStyle);
          radarLayer.getSource().addFeature(radarFeature);
        }
      }, cycle / totalFrames);

      this.radarAnimations.push(animation);
    },

    focusOnLocation(lonLat, zoomLevel = 18) {
      if (!map) return;
      const center = fromLonLat(lonLat);
      const view = map.getView();
      view.animate({
        center,
        zoom: zoomLevel,
        duration: 500
      });
    },

    clearAllPoints() {
      pointLayer.getSource().clear();
      blinkingPointLayer.getSource().clear();
      radarLayer.getSource().clear();
      this.blinkIntervalList.forEach(item => item && clearInterval(item));
      this.radarAnimations.forEach(item => item && clearInterval(item));
      this.showInfoWindow = false;
      this.highlightedFeature = null;
    },

    getRangeShape() {
      getShapeList({ sysDeptIds: [] }).then(res => {
        if (res.code === 200) {
          addMapMask(map, res.data);
        }
      });
    },

    // **点击事件处理函数**
    handleMapClick(evt) {
      // 清除之前的高亮
      // this.clearHighlight();
      // overlay.setPosition(undefined);
      // 获取点击位置的要素
      const feature = map.forEachFeatureAtPixel(evt.pixel, (feature) => feature);
      if (!feature) return;

      // 获取点位数据
      const pointData = feature.get('pointData');
      if (!pointData) return;
      if (pointData.type) {
        if (pointData.type === 2) {
          this.$emit('mapClick', pointData);
        }
        window.$Bus.$emit('mapClickEvent', pointData);
      }

      // 设置高亮效果
      // this.highlightFeature(feature);

      // 显示弹窗
      // this.clickedPoint = pointData;
      // overlay.setPosition(fromLonLat([pointData.lon, pointData.lat]));
      /* this.showInfoWindow = true;
      
      // 计算弹窗位置（偏移点位图标）
      const pixel = map.getPixelFromCoordinate(feature.getGeometry().getCoordinates());
      this.infoWindowX = pixel[0] + 10;
      this.infoWindowY = pixel[1] - 40; */

    },

    // 高亮要素
    highlightFeature(feature) {
      this.highlightedFeature = feature;

      // 创建高亮样式（替换为高亮图标）
      const pointData = feature.get('pointData');
      const highlightStyle = new Style({
        image: new Icon({
          src: highlightIcon || pointData.icon, // 使用高亮图标或原图标
          scale: 0.7, // 略大于原图标
          anchor: [0.5, 0.5],
        }),
        // 高亮边框
        overlay: new CircleStyle({
          radius: pointData.radar ? 20 : 15,
          stroke: new Stroke({ color: '#ff0', width: 2 }),
          fill: new Fill({ color: 'rgba(255, 255, 0, 0.2)' })
        })
      });

      feature.setStyle(highlightStyle);
    },

    // 清除高亮
    clearHighlight() {
      if (this.highlightedFeature) {
        // 恢复原样式
        const pointData = this.highlightedFeature.get('pointData');
        if (pointData.radar && pointData.radar.enable) {
          const { color, maxRadius, alpha } = pointData.radar;
          const pulseStyle = new Style({
            image: new Icon({
              src: pointData.icon,
              scale: 0.6,
              anchor: [0.5, 0.5],
              className: 'pulse-animation'
            }),
            overlay: new CircleStyle({
              radius: maxRadius,
              stroke: new Stroke({ color, width: 1 }),
              fill: new Fill({ color: `rgba(255, 87, 34, ${alpha * 0.5})` })
            })
          });
          this.highlightedFeature.setStyle(pulseStyle);
        } else {
          const iconStyle = new Style({
            image: new Icon({
              src: pointData.icon,
              scale: 0.6,
              anchor: [0.5, 0.5],
            }),
          });
          const textStyle = new Style({
            text: new Text({
              text: pointData.name,
              font: '12px Microsoft YaHei',
              fill: new Fill({ color: '#ffffff' }),
              offsetY: -30,
              textAlign: 'center',
            }),
          });
          this.highlightedFeature.setStyle([iconStyle, textStyle]);
        }
        this.highlightedFeature = null;
      }
    }
  },
  beforeDestroy() {
    this.clearAllPoints();
    if (map) {
      map.un('click', this.handleMapClick); // 移除事件监听
    }
  }
};
</script>

<style scoped lang="scss">
@import "@/assets/styles/utils.scss";

.map-view {
  width: 100%;
  height: 100%;
  position: relative;
}

.line-text {
  z-index: 99999;
}

.pulse-animation {
  animation: radarPulse 2s infinite;
}

@keyframes radarPulse {
  0% {
    box-shadow: 0 0 0 0 rgba(255, 87, 34, 0.4);
  }

  70% {
    box-shadow: 0 0 0 15px rgba(255, 87, 34, 0.6);
  }

  100% {
    box-shadow: 0 0 0 0 rgba(255, 87, 34, 0.8);
  }
}

.ol-popup {
  position: absolute;
  background-color: white;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.2);
  padding: 15px;
  border-radius: 10px;
  border: 1px solid #cccccc;
  bottom: 12px;
  left: -50px;
  min-width: 280px;
}

.ol-popup:after,
.ol-popup:before {
  top: 100%;
  border: solid transparent;
  content: " ";
  height: 0;
  width: 0;
  position: absolute;
  pointer-events: none;
}

.ol-popup:after {
  border-top-color: white;
  border-width: 10px;
  left: 48px;
  margin-left: -10px;
}

.ol-popup:before {
  border-top-color: #cccccc;
  border-width: 11px;
  left: 48px;
  margin-left: -11px;
}
</style>