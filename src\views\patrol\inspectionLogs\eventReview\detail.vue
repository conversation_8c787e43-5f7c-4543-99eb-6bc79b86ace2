<template>
  <div class="road-interflow-edit" v-loading="loading">
    <el-row :gutter="15">
      <el-form ref="elForm" :model="formData" :rules="rules" label-width="100px" size="medium">
        <el-col :span="12">
          <el-form-item label="管养单位" prop="domainId">
            <selectTree
                v-model="formData.domainId"
                :deptType="100"
                :deptTypeList="[1, 3, 4]" :disabled="type == 'view'"
                :style="{width: '100%'}"
                :onlySelectChild="true"
                clearable
                filterable
                placeholder="管养单位"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="养护路段" prop="maiSecId">
            <el-select v-model="formData.maiSecId" :disabled="type == 'view'" clearable placeholder="养护路段"
                       style="width: 100%;"
                       @change="handleChangeRoute">
              <el-option v-for="item in routeOptions"
                         :key="item.maintenanceSectionId"
                         :label="item.maintenanceSectionName"
                         :value="item.maintenanceSectionId">
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="路线编码" prop="routeCode">
            <route-code-section v-model="formData.routeCode" @change="getCode" placeholder="路线编码"
                                style="width: 100%" :route-type="formData.routeType" ref="routeRef"
                                :maintenanceSectionId="formData.maiSecId"></route-code-section>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="事件编号" prop="disCode">
            <el-input v-model="formData.disCode" placeholder="请输入事件编号" readonly :style="{width: '100%'}">
            </el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="起点桩号" prop="beginMile">
            <el-input v-model="formData.beginMile1" :disabled="type == 'view'" :style="{width: '45%'}">
              <template slot="prepend">K</template>
            </el-input>
            <div style="width: 10%;text-align: center;display: inline-block;">+</div>
            <el-input maxlength="3" v-model="formData.beginMile2" :disabled="type == 'view'"
                      :style="{width: '45%'}">
            </el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="终点桩号" prop="endMile">
            <el-input v-model="formData.endMile1" :disabled="type == 'view'" :style="{width: '45%'}">
              <template slot="prepend">K</template>
            </el-input>
            <div style="width: 10%;text-align: center;display: inline-block;">+</div>
            <el-input maxlength="3" v-model="formData.endMile2" :disabled="type == 'view'"
                      :style="{width: '45%'}">
            </el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="路线方向" prop="direction">
            <dict-select v-model="formData.direction" :disabled="type == 'view'"
                         clearable placeholder="请选择路线方向"
                         style="width: 100%"
                         type="route_direction"></dict-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="路线车道" prop="laneArr">
            <dict-select v-model="formData.laneArr" :disabled="type == 'view'" clearable
                         multiple placeholder="请选择路线车道"
                         style="width: 100%"
                         type="lane"></dict-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="事件分类" prop="createReason">
            <dict-select v-model="formData.createReason" :disabled="type == 'view'"
                         clearable placeholder="请选择事件分类"
                         style="width: 100%"
                         type="event_type"></dict-select>
          </el-form-item>
        </el-col>
        <el-col :span="12" v-if="formData.createReason == 4">
          <el-form-item label="工程分类" prop="mtype">
            <dict-select v-model="formData.mtype" :disabled="type == 'view'"
                         clearable placeholder="请选择工程分类"
                         style="width: 100%"
                         type="disease_mtype"></dict-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="处置类型" prop="dealType">
            <dict-select v-model="formData.dealType" :disabled="type == 'view'"
                         clearable placeholder="请选择处置类型"
                         style="width: 100%"
                         type="disposal_type"></dict-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="资产类型" prop="assetMainType">
            <el-select v-model="formData.assetMainType" :disabled="type == 'view'" style="width: 100%"
                       @change="$set(formData, 'assetType', '');$set(formData, 'diseaseType', '')">
              <el-option v-for="dict in assetMainType" :key="dict.dictValue"
                         :label="dict.dictLabel" :value="dict.dictValue">
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="资产名称" prop="assetType">
            <el-select v-model="formData.assetType" :disabled="type == 'view'" style="width: 100%"
                       @change="$set(formData, 'diseaseType', '')">
              <el-option v-for="dict in assetTypeNameList" :key="dict.dictValue"
                         :label="dict.dictLabel" :value="dict.dictValue">
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="事件类型" prop="diseaseType">
            <el-select v-model="formData.diseaseType" :disabled="type == 'view'" style="width: 100%">
              <el-option v-for="dict in diseaseTypeList" :key="dict.dictValue"
                         :label="dict.dictLabel" :value="dict.dictValue">
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="是否风险事件" prop="whetherRiskEvent">
            <el-select v-model="formData.whetherRiskEvent" style="width: 100%">
              <el-option label="是" value="是"></el-option>
              <el-option label="否" value="否"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12" v-if="formData.assetMainType == '4'">
          <el-form-item label="资产" prop="assetName">
            <el-input v-model="formData.assetName" readonly @focus="handleOpenAsset"/>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="尺寸" prop="disSize">
            <el-input v-model="formData.disSize" :disabled="type == 'view'" :style="{width: '100%'}"
                      @blur="addDise"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="数量" prop="disQuantity">
            <el-input v-model="formData.disQuantity" :disabled="type == 'view'" :style="{width: '100%'}"
                      @blur="addDise"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="事件描述" prop="diseaseDesc">
            <el-input v-model="formData.diseaseDesc" :autosize="{minRows: 4, maxRows: 4}" :disabled="type == 'view'"
                      :style="{width: '100%'}" type="textarea"
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col v-if="type === 'examine'" :span="24">
          <el-form-item label="审核意见" prop="receiveComment">
            <el-input v-model="formData.receiveComment" :autosize="{minRows: 4, maxRows: 4}"
                      :style="{width: '100%'}" type="textarea"
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="附件" prop="disPicPath">
            <file-upload v-model="formData.disPicPath" :forView="type == 'view'"
                         :owner-id="formData.disPicPath"></file-upload>
          </el-form-item>
        </el-col>
        <div style="text-align: right;padding-right: 7.5px">
          <template v-if="type === 'add'">
            <el-button type="primary" @click="onSubmit">保 存</el-button>
          </template>
          <template v-if="type === 'examine'">
            <el-button type="primary" @click="onReview(1)">有 效</el-button>
            <el-button @click="onReview(0)">无效</el-button>
          </template>
          <el-button @click="onClose">取 消</el-button>
        </div>
      </el-form>
    </el-row>
    <el-dialog title="选择资产" destroy-on-close :visible.sync="openAsset" width="65%" append-to-body v-if="openAsset">
      <asset-select @checkAsset="checkAsset"
                    :asset-type="formData.assetType == 107 ? 31 : formData.assetType == 145 ? 32 : 33"
                    :mai-sec-id="formData.maiSecId"></asset-select>
    </el-dialog>
  </div>
</template>
<script>
import fileUpload from "@/components/FileUpload/index.vue";
import {listMaintenanceSectionAll} from "@/api/system/maintenanceSection";
import {listByMaintenanceSectionId} from '@/api/baseData/common/routeLine'
import {
  addAuditData,
  editAuditData,
  reviewAuditData,
  reviewInvalidAuditData
} from "@/api/dailyMaintenance/eventManage/eventReview"
import selectTree from "@/components/DeptTmpl/selectTree.vue";
import {listAllData} from "@/api/system/dict/data";
import RouteCodeSection from "@/components/RouteCodeSection/index.vue";
import {listAllDiseases} from "@/api/patrol/diseases";
import {getCode} from "@/api/system/reportcode";
import assetSelect from "@/components/AssetSelect/index.vue";

export default {
  inheritAttrs: false,
  components: {assetSelect, RouteCodeSection, selectTree, fileUpload},
  props: {
    type: {
      type: String,
      default: ''
    },
    rowData: {
      type: Object,
      default: () => {
        return {}
      }
    },
    selectData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  data() {
    return {
      formData: {},
      rules: {
        domainId: [{required: true, message: '请选择管养单位', trigger: 'change'}],
        maiSecId: [{required: true, message: '请选择养护路段', trigger: 'blue'}],
        routeCode: [{required: true, message: '请选择路线编码', trigger: 'change'}],
        disCode: [{required: true, message: '请输入事件编号', trigger: 'blue'}],
        beginMile: [{
          required: true, validator: (rule, value, callback) => {
            if (this.formData.beginMile1 != null && this.formData.beginMile2 != null) {
              if (isNaN(Number(this.formData.beginMile1)) || isNaN(Number(this.formData.beginMile2))) {
                callback(new Error('请输入合法的桩号'))
              }
              const route = this.$refs.routeRef.getRouteInfo()
              if (route) {
                if ((this.formData.beginMile1 >= parseInt(route.pileEnd / 1000) || 0 ) || (this.formData.beginMile1 < parseInt(route.pileStart / 1000) || 0 )) {
                  callback(new Error('起点桩号不在桩号范围内'))
                }
              }
              callback()
            } else {
              callback(new Error('请完整填写起点桩号'))
            }
          }, trigger: 'blur'
        }],
        endMile: [{
          required: true, validator: (rule, value, callback) => {
            if (this.formData.endMile1 != null && this.formData.endMile2 != null) {
              if (isNaN(Number(this.formData.endMile1)) || isNaN(Number(this.formData.endMile2))) {
                callback(new Error('请输入合法的桩号'))
              }
              const route = this.$refs.routeRef.getRouteInfo()
              if (route) {
                if ((this.formData.endMile1 >= parseInt(route.pileEnd / 1000) || 0 ) || (this.formData.endMile1 < parseInt(route.pileStart / 1000) || 0 )) {
                  callback(new Error('起点桩号不在桩号范围内'))
                }
              }
              callback()
            } else {
              callback(new Error('请完整填写终点桩号'))
            }
          }, trigger: 'blur'
        }],
        direction: [{required: true, message: '请选择路线方向', trigger: 'change'}],
        laneArr: [{required: true, type: 'array', message: '请选择路线车道', trigger: 'change'}],
        createReason: [{required: true, message: '请选择事件分类', trigger: 'change'}],
        whetherRiskEvent: [{required: true, message: '请选择是否风险事件', trigger: 'change'}],
        mtype: [{required: true, message: '请选择工程分类', trigger: 'change'}],
        dealType: [{required: true, message: '请选择处置类型', trigger: 'change'}],
        assetMainType: [{required: true, message: '请选择资产类型', trigger: 'change'}],
        assetType: [{required: true, message: '请选择资产名称', trigger: 'change'}],
        diseaseType: [{required: true, message: '请选择事件类型', trigger: 'change'}]
      },
      routeOptions: [], // 路段数据
      routeList: [], // 路线编码数据
      assetMainType: [],
      diseaseTypeList: [],
      openAsset: false,
      loading: false
    }
  },
  computed: {
    assetTypeNameList: function () {
      const nameList = this.assetMainType.find(item => item.dictValue === this.formData.assetMainType)
      return nameList ? nameList.children : []
    }
  },
  watch: {
    rowData: {
      handler: function (val, oldVal) {
        if (val) {
          if (val.id) this.loading = true
          this.formData = JSON.parse(JSON.stringify(val))
          this.$set(this.formData, 'beginMile1', parseInt(this.formData.beginMile / 1000) || 0)
          this.$set(this.formData, 'beginMile2', this.formData.beginMile % 1000 || 0)
          this.$set(this.formData, 'endMile1', parseInt(this.formData.endMile / 1000) || 0)
          this.$set(this.formData, 'endMile2', this.formData.endMile % 1000 || 0)
          if (this.formData.lane) {
            this.$set(this.formData, 'laneArr', this.formData.lane.split(','))
          }
          if (this.formData.assetMainType) {
            this.formData.assetMainType = String(this.formData.assetMainType)
          }
          if (this.formData.assetType) {
            this.formData.assetType = String(this.formData.assetType)
          }
          if (this.formData.diseaseType) {
            this.formData.diseaseType = String(this.formData.diseaseType)
          }
          if (this.formData.domainId) {
            this.formData.domainId = String(this.formData.domainId)
          }
          if (this.formData.maiSecId) {
            this.handleChangeRoute(this.formData.maiSecId)
          }
          this.formData.disPicPath = this.formData.disPicPath ? this.formData.disPicPath : this.generateUUID()
        }
      },
      immediate: true
    },
    "formData.domainId": {
      handler: function (val) {
        if (val) {
          if (!this.formData.id) {
            this.$set(this.formData, 'maiSecId', '')
            this.$set(this.formData, 'routeCode', '')
          }
          listMaintenanceSectionAll({departmentId: val}).then(res => {
            if (res.code == 200) {
              this.routeOptions = res.data
            }
          })
        }
      },
      immediate: true
    },
    "formData.assetType": {
      handler: function (val) {
        this.diseaseTypeList = []
        if (val) {
          listAllDiseases({assetName: val}).then(res => {
            this.diseaseTypeList = res.data.map(item => {
              return {
                dictLabel: item.diseaseName,
                dictValue: item.id
              }
            })
          })
        }
      },
      immediate: true
    },
    "formData.beginMile1": {
      handler(val) {
        this.$set(this.formData, 'endMile1', val)
      }
    },
    "formData.beginMile2": {
      handler(val) {
        this.$set(this.formData, 'endMile2', val)
      }
    }
  },
  created() {
    this.getAssetType()
  },
  mounted() {
  },
  methods: {
    // 新增保存
    onSubmit() {
      // 校验表单，调用保存接口
      this.$refs['elForm'].validate(valid => {
        if (!valid) return
        this.formData.beginMile = this.formData.beginMile1 * 1000 + parseInt(this.formData.beginMile2)
        this.formData.endMile = this.formData.endMile1 * 1000 + parseInt(this.formData.endMile2)
        if (this.formData.disPicPath && Array.isArray(this.formData.disPicPath) && this.formData.disPicPath.length > 0) {
          this.formData.disPicPath = this.formData.disPicPath[0]
        }
        this.formData.handleStatus = 1
        this.formData.lane = this.formData.laneArr.join(',');
        if (this.formData.id) {
          editAuditData(this.formData).then(res => {
            if (res.code == 200) {
              this.$modal.msgSuccess('保存成功')
              this.$emit("close");
            }
          })
        } else {
          addAuditData(this.formData).then(res => {
            if (res.code == 200) {
              this.$modal.msgSuccess('保存成功')
              this.$emit("close");
            }
          })
        }
      })
    },
    getCode() {
      this.$set(this.formData, 'disCode', '')
      const params = {
        reportType: 'MP_DISEASE_CODE',
        domainId: this.formData.domainId,
        routeCode: this.formData.routeCode,
        sectionName: this.formData.maiSecId,
        year: new Date().getFullYear()
      }
      getCode(params).then(res => {
        if (res.code == 200) {
          this.$set(this.formData, 'disCode', res.msg)
        }
      })
    },
    onReview(status) {
      // 根据状态调用审核接口
      this.$refs['elForm'].validate(valid => {
        if (!valid) return
        this.formData.beginMile = this.formData.beginMile1 * 1000 + parseInt(this.formData.beginMile2)
        this.formData.endMile = this.formData.endMile1 * 1000 + parseInt(this.formData.endMile2)
        if (this.formData.disPicPath && Array.isArray(this.formData.disPicPath) && this.formData.disPicPath.length > 0) {
          this.formData.disPicPath = this.formData.disPicPath[0]
        }
        this.formData.lane = this.formData.laneArr.join(',');
        const routeInfo = this.$refs.routeRef.getRouteInfo()
        this.formData.routeType = routeInfo.roadType
        if (status == 1) {
          this.formData.handleStatus = 2
          reviewAuditData(this.formData).then(res => {
            if (res.code == 200) {
              this.$modal.msgSuccess('操作成功')
              this.$emit("close");
            }
          })
        } else {
          this.formData.handleStatus = 3
          reviewInvalidAuditData(this.formData).then(res => {
            if (res.code == 200) {
              this.$modal.msgSuccess('操作成功')
              this.$emit("close");
            }
          })
        }
      })
    },
    // 选中养护路段
    handleChangeRoute(e) {
      if (!this.formData.id) {
        this.$set(this.formData, 'routeCode', '')
      }
      //  获取路线数据
      listByMaintenanceSectionId({maintenanceSectionId: e}).then((res) => {
        if (res.code == 200) {
          let routeList = res.data || [];
          routeList = routeList.filter(item => item != null)
          this.routeList = routeList
        }
      });
    },
    onOpen() {
    },
    onClose() {
      this.$emit("close");
    },
    close() {
      this.$emit('update:visible', false)
    },
    getAssetType() {
      listAllData({dictType: 'sys_asset_type'}).then(res => {
        this.assetMainType = this.handleTree(res.data, "dictCode", "dictParentCode");
      }).finally(() => {
        this.loading = false
      })
    },
    addDise() {
      let diseaseDesc = ''
      if (this.formData.disSize) {
        diseaseDesc += '尺寸：' + this.formData.disSize + ';'
      }
      if (this.formData.disQuantity) {
        diseaseDesc += '数量：' + this.formData.disQuantity + ';'
      }
      this.$set(this.formData, 'diseaseDesc', diseaseDesc)
    },
    handleOpenAsset() {
      if (!this.formData.assetType) {
        this.$modal.msgWarning('请选择资产子类型')
        return
      }
      this.openAsset = true
    },
    checkAsset(asset) {
      this.formData.assetName = asset.name
      this.formData.assetId = asset.id
      this.openAsset = false
    },
    generateUUID() {
      return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {
        var r = crypto.getRandomValues(new Uint8Array(1))[0] % 16 | 0;
        var v = c === 'x' ? r : (r & 0x3 | 0x8);
        return v.toString(16);
      }).slice(0, 20); // 截取前 20 位
    }
  }
}

</script>
<style lang="scss" scoped>
::v-deep {
  .el-tabs__header {
    padding-left: 20px;
    border: 0;
  }

  .el-tabs__content {
    border: 0;
  }

  .el-form-item__label {
    font-family: Microsoft YaHei, Microsoft YaHei;
    font-weight: 400;
    font-size: 14px;
    color: #333333;
  }

  .el-input.is-disabled .el-input__inner {
    background-color: white;
    border-color: #dfe4ed;
    color: #1d2129;
  }

  .el-textarea.is-disabled .el-textarea__inner {
    background-color: white;
    border-color: #dfe4ed;
    color: #1d2129;
  }
}
</style>
