<template>
  <PageContainer>
    <template slot="search">
      <div style="display: flex; align-items: center; margin: 0">

        <div>
          <el-select
            style="margin-right:20px"
            v-model="queryParams.mainTypeId"
            placeholder="资产名称"
            clearable
          >
            <el-option
              v-for="dict in typeList"
              :key="dict.id"
              :label="dict.mainTypeName"
              :value="dict.id"
            />
          </el-select>
        </div>
        <div style="margin-right: 15px;">
          <el-input
            v-model="queryParams.typeName "
            style="width: 100%;"
            placeholder="资产名称"
            clearable
          />
        </div>

        <div style="min-width:220px;">
          <el-button
            v-hasPermi="['baseData:asset:listPage']"
            type="primary"
            @click="handleQuery"
          >搜索</el-button>
          <el-button @click="resetQuery">重置</el-button>
        </div>
      </div>
    </template>
    <template slot="header">
      <div class="button-list">

        <el-button
          type="primary"
          v-hasPermi="['baseData:asset:edit']"
          @click="handleUpdate"
        >编辑</el-button>
        <el-button
          type="primary"

          @click="handleRegister"

        >注册</el-button>
        <el-button
          type="primary"
          :disabled="true"

        >注册历史</el-button>


      </div>
    </template>
    <template slot="body">

      <el-table
        v-adjust-table
        ref="table"
        height="100%"
        style="width: 100%"
        :header-cell-style="{'height': '36px'}"
        :row-style="rowStyle"
        v-loading="loading"
        border
        :data="staticList"
        @selection-change="handleSelectionChange"
        @row-click="handleRowClick"
      >
        <el-table-column
          type="selection"
          width="50"
          align="center"
        />
        <el-table-column
          fixed
          label="序号"
          type="index"
          width="50"
          align="center"
        >
          <template v-slot="scope">
            {{ (scope.$index + (queryParams.pageNum - 1) * queryParams.pageSize)+1 }}
          </template>
        </el-table-column>

        <el-table-column

          label="类别代码"
          align="center"
          prop="typeCode"

        />
        <el-table-column

          label="类别名称"
          align="center"
          prop="typeName"

        />
        <el-table-column

          label="编码前缀"
          align="center"
          prop="codePrefix"

        />
        <el-table-column

          label="当前流水号"
          align="center"
          prop="curNum"

        />
        <el-table-column
          label="资产表名"
          align="center"
          prop="tableName"

        />

      </el-table>
      <pagination
        :total="total"
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
        @pagination="getList"
      />
    </template>
    <Form
      v-if="showAddEdit"
      :forView="forView"
      :formData="formData"
      :choseId="ids[0]"
      :minId="selectdTables[0].mainType"
      :title="title"
      :showAddEdit="showAddEdit"
      @close="() => {showAddEdit = false;formData ={}}"
      @refresh="() => {showAddEdit = false;formData ={};getList()}"
    />

  </PageContainer>
</template>

<script>
import {
  typeGetList,
  typeGetListPage,
  fieldsRegister
} from '@/api/system/asset'
import Form from './form.vue'
import DictTag from '@/components/DictTag'
export default {
  name: 'asset-baseInfo',
  components: { Form, DictTag },
  dicts: [],
  data() {
    return {
      loading: true,
      showAddEdit: false,
      forView: false,
      title: '',
      formData: {},
      typeList: [],
      ids: [],
      showImportAdd: false,
      total: 0,
      staticList: [],
      queryParams: {
        pageNum: 1,
        pageSize: 50
      },
      selectdTables: []
    }
  },
  watch: {},
  created() {
    this.getList()
    this.getAssetSubclassList()
  },
  methods: {
    getList() {
      this.loading = true
      typeGetListPage(this.queryParams)
        .then(res => {
          if (res.code === 200) {
            this.staticList = res.rows
            this.total = res.total
          }
        })
        .finally(() => {
          this.loading = false
        })
    },
    handleSelectionChange(selection) {
      this.selectdTables = selection
      this.ids = selection.map(item => item.id)
    },
    rowStyle({ row, rowIndex }) {
      if (this.ids.includes(row.id)) {
        return { 'background-color': '#b7daff', color: '#333' }
      } else {
        return { 'background-color': '#fff', color: '#333' }
      }
    },
    //查询字长子类
    getAssetSubclassList() {
      typeGetList().then(res => {
        if(res.code === 200){
          this.typeList = res.data
        }

      })
    },
    // 编辑按钮
    handleUpdate() {
      if (this.ids.length != 1) {
        this.$message.warning('请选择一条数据进行编辑！')
        return
      } else {
        this.showAddEdit = true
      }
    },
    // 表格点击勾选
    handleRowClick(row) {
      row.isSelected = !row.isSelected
      this.$refs.table.toggleRowSelection(row)
    },
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    resetQuery() {
      this.queryParams = { pageNum: 1, pageSize: 50 }
      this.handleQuery()
    },

    handleRegister(){
      if (this.ids.length != 1) {
        this.$message.warning('请选择一条数据进行注册！')
        return
      } else {
        this.$modal
          .confirm('确认注册？')
          .then(() => {
            fieldsRegister({typeId:this.ids[0],mainTypeId:this.selectdTables[0].mainType}).then(res => {
              if (res && res.code == '200') {
                this.showAddEdit = true
              }
            })
          })
          .catch(() => {})
      }
    }

  }
}
</script>

<style lang="scss" scoped>
.button-list {
  border-radius: 4px;
  width: 100%;
  overflow-x: auto;
  white-space: nowrap;
  .el-button {
    margin-bottom: 10px;
    margin-right: 10px;
    margin-left: 0;
  }
}
</style>
