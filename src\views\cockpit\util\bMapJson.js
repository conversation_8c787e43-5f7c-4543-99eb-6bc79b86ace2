const bMap<PERSON>son = [
  // 隐藏地形
  {
    "featureType": "land",
    "elementType": "geometry",
    "stylers": {
      "color": "#242f3eff"
    }
  },
  // 隐藏人造物
  {
    "featureType": "manmade",
    "elementType": "geometry", 
    "stylers": {
      "color": "#242f3eff"
    }
  },
  // 隐藏水体
  {
    "featureType": "water",
    "elementType": "geometry",
    "stylers": {
      "color": "#17263cff"
    }
  },
  // 隐藏天空
  {
    "featureType": "sky",
    "elementType": "all",
    "stylers": {
      "visibility": "off"
    }
  },
  // 隐藏背景
  {
    "featureType": "background",
    "elementType": "all",
    "stylers": {
      "visibility": "off"
    }
  },
  // 设置背景透明
  {
    "featureType": "background",
    "elementType": "geometry",
    "stylers": {
      "color": "#00000000"
    }
  },
  // 隐藏道路
  {
    "featureType": "road",
    "elementType": "all",
    "stylers": {
      "visibility": "off"
    }
  },
  // 隐藏建筑物
  {
    "featureType": "building",
    "elementType": "all",
    "stylers": {
      "visibility": "off"
    }
  },
  // 隐藏地名
  {
    "featureType": "poilabel",
    "elementType": "all", 
    "stylers": {
      "visibility": "off"
    }
  },
  // 隐藏行政区划
  {
    "featureType": "districtlabel",
    "elementType": "all",
    "stylers": {
      "visibility": "off" 
    }
  },
  // 隐藏边界
  {
    "featureType": "boundary",
    "elementType": "all",
    "stylers": {
      "visibility": "off"
    }
  },
  // 隐藏国界
  {
    "featureType": "country",
    "elementType": "all",
    "stylers": {
      "visibility": "off"
    }
  },
  // 隐藏中国地图
  {
    "featureType": "land",
    "elementType": "all",
    "stylers": {
      "visibility": "off"
    }
  },
];
export default bMapJson;
