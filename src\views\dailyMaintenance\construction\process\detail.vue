<template>
  <div class="app-container">
    <el-form ref="elForm" :model="formData" :inline="true" label-width="180px" v-loading="loading">
      <el-row :gutter="20">
        <el-col v-if="type == 'rc'" :span="24" style="margin-bottom: 18px">
          <div class="card_title">入场登记</div>
          <el-card class="box-card" shadow="never">
            <el-form-item label="">
              <file-upload v-model="rcdjzp" :owner-id="rcdjzp"></file-upload>
            </el-form-item>
          </el-card>
        </el-col>
        <el-col v-else :span="24" style="margin-bottom: 18px">
          <div class="card_title">出场登记</div>
          <el-card class="box-card" shadow="never">
            <el-form-item label="">
              <file-upload v-model="ccdjzp" :owner-id="ccdjzp"></file-upload>
            </el-form-item>
          </el-card>
        </el-col>
        <el-col :span="12">
          <div class="card_title">施工简图</div>
          <el-card class="box-card" shadow="never">
            <el-form-item label="">
              <file-upload v-model="sgjt" :owner-id="sgjt"></file-upload>
            </el-form-item>
          </el-card>
        </el-col>
        <el-col :span="12">
          <div class="card_title">施工附件</div>
          <el-card class="box-card" shadow="never">
            <el-form-item label="">
              <file-upload v-model="sgfj" :owner-id="sgfj"></file-upload>
            </el-form-item>
          </el-card>
        </el-col>
        <el-col :span="24" style="margin-top: 18px">
          <div class="card_title" style="display: flex;width: 100%;justify-content: space-between"><div>事件方法数量</div><el-button icon="el-icon-plus" circle @click="openLibModel"></el-button></div>
          <el-table v-adjust-table
              :data="methodList"
              border
              height="200px"
              ref="tableRef"
              style="width: 100%">
            <el-table-column
                label="序号"
                align="center"
                type="index"
                width="50"
            />
            <el-table-column
                prop="schemeCode"
                align="center"
                label="子目号"
                width="100">
            </el-table-column>
            <el-table-column
                prop="schemeName"
                align="center"
                label="子目名称"
                width="100">
            </el-table-column>
            <el-table-column
                prop="unit"
                align="center"
                label="单位"
                width="100">
            </el-table-column>
            <el-table-column
                prop="price"
                align="center"
                label="单价"
                width="100">
            </el-table-column>
            <el-table-column
                prop="calcDesc"
                align="center"
                label="计算式"
                width="100">
              <template slot-scope="scope">
                <el-input v-model="scope.row.calcDesc" @change="changeCalculation(scope.row)">
                </el-input>
              </template>
            </el-table-column>
            <el-table-column
                prop="num"
                align="center"
                label="方法数量"
                width="100">
              <template slot-scope="scope">
                <el-input v-model="scope.row.num"  @change="changeSchemeNum(scope.row)">
                </el-input>
              </template>
            </el-table-column>
            <el-table-column
                prop="amount"
                align="center"
                label="资金"
                width="100">
            </el-table-column>
            <el-table-column
                prop="remark"
                align="center"
                label="备注"
                width="100">
              <template slot-scope="scope">
                <el-input v-model="scope.row.remark">
                </el-input>
              </template>
            </el-table-column>
            <el-table-column
                prop="field101"
                align="center"
                label="移除">
              <template slot-scope="scope">
                <el-button
                    size="mini"
                    type="text"
                    @click="handleDelete(scope.row)"
                >移除
                </el-button
                >
              </template>
            </el-table-column>
          </el-table>
        </el-col>
        <el-col :span="24" style="margin-top: 18px">
          <div class="card_title" style="display: flex;width: 100%;justify-content: space-between"><div>计算式及说明</div><el-link type="primary" @click="generateInstructions">生成计算式说明</el-link></div>
          <el-input class="calculation_desc" v-model="formData.calculationDesc" type="textarea" :rows="4">
          </el-input>
        </el-col>
        <el-col :span="24" style="margin-top: 18px">
          <div class="card_title">登记备注</div>
          <el-input v-model="formData.remark" type="textarea" :rows="4">
          </el-input>
        </el-col>
      </el-row>
      <div style="text-align: right;padding-right: 7.5px;margin-top: 18px">
        <el-button type="primary" @click="onSave">保 存</el-button>
        <el-button type="success" @click="onSubmit">提 交</el-button>
        <el-button @click="onClose">退出</el-button>
      </div>
    </el-form>
    <el-dialog title="选择报价" destroy-on-close :visible.sync="libModel" width="80%" append-to-body>
      <el-input
          placeholder="输入关键字进行过滤"
          v-model="filterText">
      </el-input>

      <el-tree
          class="filter-tree"
          :data="libData"
          style="height: 300px;overflow-y: scroll"
          show-checkbox
          :props="defaultProps"
          default-expand-all
          :filter-node-method="filterNode"
          ref="tree">
      </el-tree>
      <div style="text-align: right;padding-right: 7.5px">
        <el-button type="primary" @click="checkLib">保 存</el-button>
        <el-button @click="libModel = false">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import {getTreeData} from "@/api/contract/quotationSystem"
import {getRegisterDetail, save, review} from "@/api/dailyMaintenance/construction/registration"
export default {
  name: "index",
  data() {
    return {
      formData: {
        calculationDesc: ''
      },
      rcdjzp: '',
      ccdjzp: '',
      sgjt: '',
      sgfj: '',
      loading: false,
      methodList: [],
      libModel: false,
      filterText: '',
      libData: [],
      defaultProps: {
        children: 'children',
        label: 'schemeName'
      }
    }
  },
  props: {
    rowData: {
      type: Object,
      default: () => {
        return {}
      }
    },
    type: {
      type: String,
      default: 'rc'
    }
  },
  watch: {
    rowData: {
      handler(val) {
        if (val.id) {
          this.formData = val
          this.getDetail(val.id)
        }
      },
      immediate: true
    },
    filterText(val) {
      this.$refs.tree.filter(val);
    }
  },
  mounted() {
    this.getLibData()
  },
  methods: {
    getDetail(id) {
      getRegisterDetail(id).then(res => {
        const detail = res.rows[0]
        this.methodList = detail.methodList
        const rcdjzp = detail.attList.find(item => item.registerType == 3)
        this.rcdjzp = rcdjzp ? rcdjzp.fileId : this.generateUUID()
        const ccdjzp = detail.attList.find(item => item.registerType == 4)
        this.ccdjzp = ccdjzp ? ccdjzp.fileId : this.generateUUID()
        const sgjt = detail.attList.find(item => item.registerType == 1)
        this.sgjt = sgjt ? sgjt.fileId : this.generateUUID()
        const sgfj = detail.attList.find(item => item.registerType == 2)
        this.sgfj = sgfj ? sgfj.fileId : this.generateUUID()
      })
    },
    onSave() {
      this.loading = true
      this.generateParams()
      save(this.formData).then(res => {
        this.loading = false
        this.$message.success('保存成功')
        this.onClose()
      })
    },
    onSubmit() {
      this.loading = true
      this.generateParams()
      this.formData.daliyId = this.formData.conId
      review(this.formData).then(res => {
        this.loading = false
        this.$message.success('提交成功')
        this.onClose()
      })
    },
    // 拼接参数
    generateParams() {
      this.methodList = this.methodList.map(item => ({
        ... item,
        schemeId: item.id
      }))
      // 拼接参数
      this.formData.methodList = this.methodList
      const attList = []
      if (this.rcdjzp) {
        attList.push({
          fileId: Array.isArray(this.rcdjzp) ? this.rcdjzp[0] : this.rcdjzp,
          registerType:3
        })
      }
      if (this.ccdjzp) {
        attList.push({
          fileId: Array.isArray(this.ccdjzp) ? this.ccdjzp[0] : this.ccdjzp,
          registerType:4
        })
      }
      if (this.sgjt) {
        attList.push({
          fileId: Array.isArray(this.sgjt) ? this.sgjt[0] : this.sgjt,
          registerType:1
        })
      }
      if (this.sgfj) {
        attList.push({
          fileId: Array.isArray(this.sgfj) ? this.sgfj[0] : this.sgfj,
          registerType:2
        })
      }
      this.formData.attList = attList
    },
    checkLib() {
      let checkDatas = this.$refs.tree.getCheckedNodes()
      checkDatas = checkDatas.filter(item => {
        return item.nodeType == 2 && !this.methodList.some(filterItem => filterItem.id === item.id)
      })
      this.methodList.push(...checkDatas)

      this.libModel = false
    },
    getLibData() {
      // 获取报价体系
      getTreeData({
        conId: this.rowData.conConId
      }).then(res => {
        this.libData = res.rows || []
        // 过滤libData中children为空的
        this.libData = this.libData.filter(item => {
          return item.children && item.children.length > 0
        })
      })
    },
    openLibModel() {
      this.libModel = true
    },
    filterNode(value, data) {
      if (!value) return true;
      return data.schemeName.indexOf(value) !== -1;
    },
    handleDelete(e) {
      this.methodList = this.methodList.filter(item => {
        return item.id != e.id
      })
    },
    changeCalculation(row) {
      if (!this.isValidMathFormula(row.calcDesc)) {
        this.$modal.msgError('计算式错误，请检查')
        return
      }
      this.$set(row, 'num', this.math.evaluate(row.calcDesc))
      this.$set(row, 'amount', row.num * row.price)
      this.total = this.methodList.reduce((acc, curr) => acc + curr.amount, 0)
    },
    changeSchemeNum(row) {
      this.$set(row, 'amount', row.num * row.price)
      this.total = this.methodList.reduce((acc, curr) => acc + curr.amount, 0)
    },
    onClose() {
      this.$emit("close")
    },
    // 生成计算式说明
    generateInstructions() {
      let calculationDesc = ''
      this.methodList.forEach(item => {
        if (item.schemeName != '安全生产费' && item.schemeName != '安全保通费')
        calculationDesc += `${item.schemeName || ''}:(${item.calcDesc || item.num || ''})=${item.num || 0}${item.unit || ''}\n`
      })
      this.$set(this.formData, 'calculationDesc', calculationDesc)
    },
    generateUUID() {
      return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
        var r = crypto.getRandomValues(new Uint8Array(1))[0] % 16 | 0;
        var v = c === 'x' ? r : (r & 0x3 | 0x8);
        return v.toString(16);
      }).slice(0, 20); // 截取前 20 位
    }
  }
}
</script>
<style scoped lang="scss">
.card_title {
  width: 200px;
  text-align: left;
  margin-bottom: 15px;
  font-weight: bold;
}
</style>
<style lang="scss" scoped>
@import "@/assets/styles/business.scss";
</style>
