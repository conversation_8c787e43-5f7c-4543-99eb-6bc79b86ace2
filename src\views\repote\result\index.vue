<template>
  <div class="app-container">
    <div class="layout">
      <div class="left">
        <Mission></Mission>
      </div>
      <div class="spacer"></div>
      <div class="right">
        <MissionDetail></MissionDetail>
      </div>
    </div>
    <RepoteRecord></RepoteRecord>
  </div>
</template>

<script>
import Mission from "@/views/repote/result/mission.vue";
import MissionDetail from "@/views/repote/result/detail.vue";
import RepoteRecord from "@/views/repote/result/record.vue";

export default {
  name: "result",
  components: { Mission, MissionDetail, RepoteRecord },
  data() {
    return {};
  },
  created() {},
  computed: {},
  methods: {},
};
</script>
<style scoped>
.layout {
  display: flex;
  /* width: 100%; */
  gap: 10px; /* 中间的间距 */
}

.left {
  flex: 0 0 40%; /* 左边占30% */
}

.spacer {
  flex: 0 0 10px; /* 中间的间距 */
}

.right {
  flex: 1; /* 右边占剩余空间 */
}
</style>
