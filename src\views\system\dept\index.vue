<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch">
      <el-form-item label="部门名称" prop="deptName">
        <el-input
          v-model="queryParams.deptName"
          placeholder="请输入部门名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-select v-model="queryParams.status" placeholder="部门状态" clearable>
          <el-option
            v-for="dict in dict.type.sys_normal_disable"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['system:dept:add']"
        >新增
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="info"
          plain
          icon="el-icon-sort"
          size="mini"
          @click="toggleExpandAll"
        >展开/折叠
        </el-button>
      </el-col>

      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          :disabled="(!curDeptId) || syncIng"
          :icon="syncIng ? '':'el-icon-refresh'"
          size="mini"
          @click="handleSync"
          v-hasPermi="['system:dept:add']"
        >
          <i v-if="syncIng" class="el-icon-loading "/>
          资产同步
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          :disabled="(!curDeptId) || syncTypeIng"
          :icon="syncTypeIng ? '':'el-icon-refresh'"
          size="mini"
          @click="handleSyncType"
          v-hasPermi="['system:dept:add']"
        >
          <i v-if="syncTypeIng" class="el-icon-loading "/>
          单位类型同步
        </el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table

      @row-click="row=>curDeptId=row.deptId"
      highlight-current-row
      v-if="refreshTable"
      v-loading="loading"
      :data="deptList"
      row-key="deptId"
      :default-expand-all="isExpandAll"
      :tree-props="{children: 'children', hasChildren: 'hasChildren'}"
    >
      <el-table-column prop="deptName" label="部门名称" min-width="240"></el-table-column>
      <el-table-column prop="deptCode" label="部门代码" min-width="100"></el-table-column>
      <el-table-column prop="deptType" label="单位类型" min-width="150">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.sys_dept_types" :value="scope.row.deptType"/>
        </template>
      </el-table-column>
      <el-table-column prop="orderNum" label="排序" min-width="80"></el-table-column>
      <el-table-column prop="status" label="状态">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.sys_normal_disable" :value="scope.row.status"/>
        </template>
      </el-table-column>
      <el-table-column label="创建时间" align="center" prop="createTime" min-width="200">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.createTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" width="340" fixed="right" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['system:dept:edit']"
          >修改
          </el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-plus"
            @click="handleAdd(scope.row)"
            v-hasPermi="['system:dept:add']"
          >新增
          </el-button>
          <el-button
            v-if="scope.row.parentId != 0"
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['system:dept:remove']"
          >删除
          </el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleDeptSub(scope.row)"
            v-hasPermi="['system:dept:deptSubTree']"
          >数据权限
          </el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleDeptAsset(scope.row.deptId)"
            v-hasPermi="['system:dept:asset']"
          >资产权限
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 添加或修改部门对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="600px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-row>
          <el-col :span="24" v-if="form.parentId !== 0">
            <el-form-item label="上级部门" prop="parentId">
              <treeselect v-model="form.parentId" :options="deptOptions" :normalizer="normalizer"
                          placeholder="选择上级部门"/>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="部门名称" prop="deptName">
              <el-input v-model="form.deptName" placeholder="请输入部门名称"/>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="显示排序" prop="orderNum">
              <el-input-number v-model="form.orderNum" controls-position="right" :min="0"/>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="负责人" prop="leader">
              <el-input v-model="form.leader" placeholder="请输入负责人" maxlength="20"/>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="联系电话" prop="phone">
              <el-input v-model="form.phone" placeholder="请输入联系电话" maxlength="11"/>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="邮箱" prop="email">
              <el-input v-model="form.email" placeholder="请输入邮箱" maxlength="50"/>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="部门状态">
              <el-radio-group v-model="form.status">
                <el-radio
                  v-for="dict in dict.type.sys_normal_disable"
                  :key="dict.value"
                  :label="dict.value"
                >{{ dict.label }}
                </el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <!--          <el-col :span="12">-->
          <!--            <el-form-item label="部门代码" prop="deptCode">-->
          <!--              <el-input v-model="form.deptCode" placeholder="请输入部门代码" disabled maxlength="30" />-->
          <!--            </el-form-item>-->
          <!--          </el-col>-->
          <el-col :span="12">
            <el-form-item label="单位类型" prop="deptType">
              <el-select v-model="form.deptType" filterable placeholder="请选择单位类型">
                <el-option
                  v-for="dict in dict.type.sys_dept_types"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 管理数据权限
    :check-strictly = true  //父子级不关联   true 不关联
    :check-on-click-node = true  //是否在点击节点时选中  true 选中
    :expand-on-click-node = false  //是否在点击节点时展开或收缩  false 关闭
    -->
    <el-drawer title="数据权限管理" :visible.sync="drawer1" size="20%">
      <div class="demo-drawer__content">
        <el-tree
          :data="deptSubOptions"
          show-checkbox
          node-key="id"
          auto-expand-parent
          ref="deptSub"
          @check.self="inChecked"
          @node-click="nodeClick"
          :check-strictly=true
          :check-on-click-node=false
          :expand-on-click-node=false
          :default-expanded-keys="dataCheckedArr"
          :default-checked-keys="dataCheckedArr"
          :props="defaultProps">
        </el-tree>
      </div>
      <div class="drawer-footer">
        <el-button type="primary" @click="handleClose()" :loading="loading">{{
            loading ? '提交中 ...' : '确 定'
          }}
        </el-button>
      </div>
    </el-drawer>

    <!-- 管理资产权限 -->
    <el-drawer title="资产权限管理" :visible.sync="drawer2" size="20%">
      <div class="head-container" style="width: 300px">
        <div style="width: 270px; margin-bottom:10px; display: flex">
          <el-input
            maxlength="220"
            v-model="maintenanceName"
            placeholder="养护路段名称"
            clearable
            size="small"
            prefix-icon="el-icon-search"/>
          <div style="margin-left: 5px;">
            <el-button size="small" icon="el-icon-plus" @click="selectMaintenance"></el-button>
          </div>
          <div style="margin-left: 5px;">
            <el-button
              type="info"
              plain
              icon="el-icon-upload2"
              size="small"
              @click="handleImport"
              v-hasPermi="['system:dept:asset']"
            >导入</el-button>
          </div>
        </div>
        <el-tree
          :data="deptAssetOptions"
          :filter-node-method="filterNode"
          :check-strictly="true"
          node-key="id"
          auto-expand-parent
          ref="deptAsset"
          :render-content="renderContent"
          :default-expanded-keys="deptAssetExpandedArr"
          :props="defaultProps">
        </el-tree>
        <div class="drawer-footer">
          <el-button type="primary" @click="handleAssetClose()" :loading="loading">{{
              loading ? '提交中 ...' : '确 定'
            }}
          </el-button>
        </div>
      </div>
    </el-drawer>

    <!-- 资产导入对话框 -->
    <el-dialog :title="upload.title" :visible.sync="upload.open" width="400px" append-to-body>
      <el-upload
        ref="upload"
        :limit="1"
        accept=".xlsx, .xls"
        :headers="upload.headers"
        :action="upload.url + '?updateSupport=' + upload.updateSupport + '&deptId=' + deptData.deptId"
        :disabled="upload.isUploading"
        :on-progress="handleFileUploadProgress"
        :on-success="handleFileSuccess"
        :auto-upload="false"
        drag
      >
        <i class="el-icon-upload"></i>
        <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
        <div class="el-upload__tip text-center" slot="tip">
          <div class="el-upload__tip" slot="tip">
            <el-checkbox v-model="upload.updateSupport" /> 是否覆盖已经存在的资产权限
          </div>
          <span>仅允许导入xls、xlsx格式文件。</span>
          <el-link type="primary" :underline="false" style="font-size:12px;vertical-align: baseline;" @click="importTemplate">下载模板</el-link>
        </div>
      </el-upload>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitFileForm">确 定</el-button>
        <el-button @click="upload.open = false">取 消</el-button>
      </div>
    </el-dialog>

    <select-maintenance ref="selectMaintenance" :deptId="deptData.deptId"/>
    <select-segments ref="selectSegments" :dept-data="deptData"/>
    <select-bridge ref="selectBridge" :dept-data="deptData"/>
    <select-culvert ref="selectCulvert" :dept-data="deptData"/>
    <select-tunnel ref="selectTunnel" :dept-data="deptData"/>
  </div>
</template>

<script>
import {
  addDept,
  addDeptSub,
  syncDeptAsset,
  delDept,
  deptTreeSelect,
  findCheckedArr,
  getDept,
  listDept,
  listDeptExcludeChild,
  updateDept,
  addDeptAssetData,
  syncDeptType,
  deptCacheClear
} from "@/api/system/dept";
import { getToken } from "@/utils/auth";
import {delteDeptMaintenance, findTreeList} from "@/api/system/maintenance";
import Treeselect from "@riophae/vue-treeselect";
import "@riophae/vue-treeselect/dist/vue-treeselect.css";
import SelectMaintenance from "@/views/system/dept/selectMaintenance";
import SelectBridge from "@/views/system/dept/selectBridge";
import SelectCulvert from "@/views/system/dept/selectCulvert";
import SelectSegments from "@/views/system/dept/selectSegments";
import SelectTunnel from "@/views/system/dept/selectTunnel";

export default {
  name: "Dept",
  dicts: ['sys_normal_disable', 'sys_dept_types'],
  components: {SelectMaintenance, Treeselect, SelectBridge, SelectCulvert, SelectSegments, SelectTunnel},
  data() {
    return {
      // 遮罩层
      loading: true,
      // 显示搜索条件
      showSearch: true,
      // 表格树数据
      deptList: [],
      // 部门树选项
      deptOptions: [],
      //抽屉
      drawer1: false,
      drawer2: false,
      // 管理部门树选项
      deptSubOptions: [],
      // 资产树
      deptAssetOptions: [],
      dataCheckedArr: [],
      // 资产权限默认展开数组
      deptAssetExpandedArr: [],
      //养护路段名称
      maintenanceName: undefined,
      defaultProps: {
        children: "children",
        label: "label"
      },
      curDeptId: null,
      syncIng: false,
      syncTypeIng: false,
      //提交参数
      params: {},
      //父级参数
      deptData: {},
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 是否展开，默认全部展开
      isExpandAll: false,
      // 重新渲染表格状态
      refreshTable: true,
      // 查询参数
      queryParams: {
        deptName: undefined,
        status: undefined
      },
      // 资产导入参数
      upload: {
        // 是否显示弹出层（资产导入）
        open: false,
        // 弹出层标题（资产导入）
        title: "",
        // 是否禁用上传
        isUploading: false,
        // 是否更新已经存在的用户数据
        updateSupport: 0,
        // 设置上传的请求头部
        headers: { Authorization: "Bearer " + getToken() },
        // 上传的地址
        url: process.env.VUE_APP_BASE_API + "/system/dept/importData"
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        // parentId: [
        //   {required: true, message: "上级部门不能为空", trigger: "blur"}
        // ],
        deptName: [
          {required: true, message: "部门名称不能为空", trigger: "blur"}
        ],
        orderNum: [
          {required: true, message: "显示排序不能为空", trigger: "blur"}
        ],
        deptType: [
          {required: true, message: "单位类型不能为空", trigger: ["blur", "change"]}
        ],
        email: [
          {
            type: "email",
            message: "请输入正确的邮箱地址",
            trigger: ["blur", "change"]
          }
        ],
        phone: [
          {
            pattern: /^1[3|4|5|6|7|8|9][0-9]\d{8}$/,
            message: "请输入正确的手机号码",
            trigger: "blur"
          }
        ]
      }
    };
  },
  created() {
    this.getList();
  },
  watch: {
    // 根据名称筛选部门树
    maintenanceName(val) {
      this.$refs.deptAsset.filter(val);
    }
  },
  methods: {

    handleSync() {
      this.syncIng = true
      syncDeptAsset({deptId: this.curDeptId}).then(res => {
        console.log(res)
        this.$modal.msgSuccess("资产权限同步成功");
        this.syncIng = false
      }).finally(() => this.syncIng = false)
    },

    handleSyncType() {
      this.syncTypeIng = true
      syncDeptType({deptId: this.curDeptId}).then(res => {
        console.log(res)
        this.$modal.msgSuccess("单位类型同步成功");
        this.getList()
      }).finally(() => this.syncTypeIng = false)
    },
    /** 查询部门列表 */
    getList() {
      this.loading = true;
      listDept(this.queryParams).then(response => {
        this.deptList = this.handleTree(response.data, "deptId");
        this.loading = false;
      });
    },
    /** 转换部门数据结构 */
    normalizer(node) {
      if (node.children && !node.children.length) {
        delete node.children;
      }
      return {
        id: node.deptId,
        label: node.deptName,
        children: node.children
      };
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        deptId: undefined,
        deptName: undefined,
        orderNum: undefined,
        leader: undefined,
        phone: undefined,
        email: undefined,
        status: "0"
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    /** 新增按钮操作 */
    handleAdd(row) {
      this.reset();
      if (row != undefined) {
        this.form.parentId = row.deptId;
      }
      this.open = true;
      this.title = "添加部门";
      listDept().then(response => {
        this.deptOptions = this.handleTree(response.data, "deptId");
      });
    },
    // --------------------------------------------------数据权限----------------------------------------------------

    // 处理复选框选中事件，这里必须关闭父子级联动
    inChecked(data) {
      const isKeyChecked = this.$refs.deptSub.getCheckedKeys().includes(data.id);
      console.log("isKeyChecked:", isKeyChecked)
      console.log("data:", data)
      let children = [];
      //保持之前选中的部分
      children.push(...this.$refs.deptSub.getCheckedNodes());
      if (isKeyChecked) { //当前节点是否选中
        children.push(...this.getAllChildren(data));
      } else {
        this.getAllChildren(data).forEach(item => {
          children.splice(children.indexOf(item), 1);
        })
      }
      this.$refs.deptSub.setCheckedNodes(children);
    },
    //node-click 仅点击文字会触发
    //click 点击文字和复选框都会触发
    nodeClick(data, node) {
      this.$refs.deptSub.setChecked(data, !node.checked);
    },
    getAllChildren(node) {
      let children = [];
      if (node.children && node.children.length > 0) {
        node.children.forEach(child => {
          children.push(child);
          // 递归获取子节点的子节点
          const subChildren = this.getAllChildren(child);
          children = children.concat(subChildren);
        });
      }
      return children;
    },
    /** 数据权限按钮操作 */
    handleDeptSub(row) {
      this.reset();
      deptTreeSelect().then(response => {
        this.deptSubOptions = response.data;
      });
      findCheckedArr(row.deptId).then(response => {
        this.dataCheckedArr = response.data;
      });
      this.drawer1 = true;
      this.params.deptId = row.deptId;
    },
    /** 提交数据权限 */
    handleClose() {
      if (this.loading) {
        return;
      }
      let idArr = [];//选中的节点,需包含半选节点
      this.$refs.deptSub.getCheckedNodes(false, true).forEach(item => {
        idArr.push(item.id);
      })
      this.params.deptIds = idArr.join(",");
      // this.params.deptIds = this.$refs.deptSub.getCheckedKeys().join(",");
      console.log(this.params.deptIds)
      addDeptSub(this.params).then(response => {
        this.$modal.msgSuccess("修改成功");
        // this.getList();
        this.drawer1 = false;
        this.params = {};
        this.dataCheckedArr = [];
      });
    },
    // --------------------------------------------------资产权限----------------------------------------------------
    /** 资产权限按钮操作 */
    handleDeptAsset(deptId) {
      this.reset();
      findTreeList(deptId).then(response => {
        this.deptAssetOptions = response.data;
      });
      if (this.deptData.maintenanceSectionId) {
        this.deptAssetExpandedArr.push(this.deptData.maintenanceSectionId);
      }
      this.drawer2 = true;
      this.deptData.deptId = deptId;
    },
    // 筛选节点
    filterNode(value, data) {
      if (!value) return true;
      return data.label.indexOf(value) !== -1;
    },
    //删除节点
    remove(node, data) {
      const parent = node.parent;
      const children = parent.data.children || parent.data;
      const index = children.findIndex(d => d.id === data.id);
      children.splice(index, 1);
      this.deptData.maintenanceSectionId = data.id;
      delteDeptMaintenance(this.deptData).then(response => {
        // this.$modal.msgSuccess("修改成功");
      });
    },
    //函数渲染
    renderContent(h, {node, data, store}) {
      const {label, checked, level} = node;
      return (
        <span class="custom-tree-node">
          <span on-click={() => this.selectSegments(node)}>{node.label}</span>
          <span>
            <el-button v-show={data.level === 1} type="text"
                       on-click={() => this.remove(node, data)}>
                <i class="el-icon-delete" style="color: #a79b9b; margin-left: 10px;"></i>
            </el-button>
          </span>
        </span>);
    },
    /** 选择养护路段 */
    selectMaintenance() {
      this.$refs.selectMaintenance.show();
    },
    /** 选择子集 */
    selectSegments(node) {
      const id = node.data.id;
      this.deptData.maintenanceSectionId = node.parent.data.id;
      if (id === "A-1") {//子段
        this.$refs.selectSegments.show();
      } else if (id === "A-2") {//桥梁
        this.$refs.selectBridge.show();
      } else if (id === "A-3") {//隧道
        this.$refs.selectTunnel.show();
      } else if (id === "A-4") {//涵洞
        this.$refs.selectCulvert.show();
      }
    },
    /** 提交资产权限 */
    handleAssetClose() {
      console.log("-------");
      if (this.loading) {
        return;
      }
      //被选中的子段ID
      // this.params.routeSegmentsIds = this.$refs.deptAsset.getCheckedKeys().join(",");
      // addDeptAssetData(this.params).then(response => {
      //   this.$modal.msgSuccess("修改成功");
      //关闭抽屉
      this.drawer2 = false;
      this.deptData = {};
      // });
    },
    /** 导入按钮操作 */
    handleImport() {
      this.upload.title = "资产权限导入";
      this.upload.open = true;
    },
    /** 下载模板操作 */
    importTemplate() {
      this.download('system/dept/importTemplate', {
      }, `资产权限导入_${new Date().getTime()}.xlsx`)
    },
    // 文件上传中处理
    handleFileUploadProgress(event, file, fileList) {
      this.upload.isUploading = true;
    },
    // 文件上传成功处理
    handleFileSuccess(response, file, fileList) {
      this.upload.open = false;
      this.upload.isUploading = false;
      this.$refs.upload.clearFiles();
      this.$alert("<div style='overflow: auto;overflow-x: hidden;max-height: 70vh;padding: 10px 20px 0;'>" + response.msg + "</div>", "导入结果", { dangerouslyUseHTMLString: true });
      this.handleDeptAsset(this.deptData.deptId);
    },
    // 提交上传文件
    submitFileForm() {
      this.$refs.upload.submit();
    },

    //-----------------------------------------------------部门操作-------------------------------------------------------------
    /** 展开/折叠操作 */
    toggleExpandAll() {
      this.refreshTable = false;
      this.isExpandAll = !this.isExpandAll;
      this.$nextTick(() => {
        this.refreshTable = true;
      });
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      getDept(row.deptId).then(response => {
        response.data.deptType = response.data.deptType.toString();
        this.form = response.data;
        this.open = true;
        this.title = "修改部门";
      });
      listDeptExcludeChild(row.deptId).then(response => {
        this.deptOptions = this.handleTree(response.data, "deptId");
      });
    },
    /** 提交按钮 */
    submitForm: function () {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          if (this.form.deptId != undefined) {
            updateDept(this.form).then(res => {
              if (res.code === 200) {
                this.$modal.msgSuccess("修改成功");
                this.open = false;
                this.getList();
                deptCacheClear()
              }
            });
          } else {
            addDept(this.form).then(res => {
              if (res.code === 200) {
                this.$modal.msgSuccess("新增成功");
                this.open = false;
                this.getList();
              }
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      this.$modal.confirm('是否确认删除名称为"' + row.deptName + '"的数据项？').then(function () {
        return delDept(row.deptId);
      }).then((res) => {
        if (res.code === 200) {
          deptCacheClear()
          this.getList();
          this.$modal.msgSuccess("删除成功");
        }
      }).catch(() => {
      });
    }
  }
};
</script>
<style>
.drawer-footer {
  position: absolute;
  bottom: 18px;
  right: 15px;
}

</style>
