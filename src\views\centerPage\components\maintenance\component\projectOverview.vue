<template>
  <div class="project-overview">
    <section class="total-number">
      <span>专项养护总数：</span>
      <span>{{ totalCount || 1680450}}</span>
      <small>个</small>
    </section>
    <section class="list status-list">
      <div v-for="(item, index) in statusList" :key="'status' + index" class="list-item">
        <div class="item" :class="index < statusList.length - 1 ? 'border-r' : ''">
          <div class="name">{{ item.name }}</div>
          <div class="number">{{ item.number }}</div>
        </div>
      </div>
    </section>
    <section class="list type-list">
      <div v-for="(item, index) in typeList" :key="'type' + index" class="list-item">
        <div class="item" :class="index < typeList.length - 1 ? 'border-r' : ''">
          <div class="name">{{ item.name }}</div>
          <div class="number">{{ item.number }}</div>
        </div>
      </div>
    </section>
  </div>
</template>

<script>
import { getMaintenanceProject } from '@/api/cockpit/maintain';

export default {
  name: 'ProjectOverview',
  props: {
    year: {
      type: [Number,String],
      default: ''
    }
  },
  data() {
    return {
      totalCount: 0,
      statusList: [
        {
          name: '未开始',
          number: 3,
        },
        {
          name: '进行中',
          number: 3,
        },
        {
          name: '已完成',
          number: 3,
        },
        {
          name: '',
          number: null,
        }
      ],
      typeList: [
        {
          name: '预防养护',
          number: 3,
        },
        {
          name: '修复养护',
          number: 3,
        },
        {
          name: '专项养护',
          number: 3,
        },
        {
          name: '应急养护',
          number: 0,
        }
      ]
    }
  },
  watch: {
    year: {
      handler(val) {
        this.getStatisticsData();
      },
      immediate: true,
      deep: true
    }
  },
  methods: {
    async getStatisticsData() {
      let year = this.year || new Date().getFullYear();
      const res = await getMaintenanceProject(year);
      if (res.code === 200 && res.data) {
        let data = res.data || {};
        this.totalCount = data.totalCount || 0;
        // 未开始
        this.statusList[0].number = data.notStartedCount || 0;
        // 进行中
        this.statusList[1].number = data.inProgressCount || 0;
        // 已完成
        this.statusList[2].number = data.completedCount || 0;

        // 预防养护
        this.typeList[0].number = data.preventMaintainCount || 0;
        // 修复养护
        this.typeList[1].number = data.repairMaintainCount || 0;
        // 专项养护
        this.typeList[2].number = data.specialMaintainCount || 0;
        // 应急养护
        this.typeList[3].number = data.emergencyMaintainCount || 0;
      }
    }
  },
}
</script>

<style lang="scss" scoped>
@import "@/assets/styles/utils.scss";

.project-overview {
  width: 100%;
  height: 100%;
  padding: 5px 10px;
  overflow-y: auto;

  .total-number {
    width: 100%;

    span:nth-child(1) {
      font-family: Microsoft YaHei, Microsoft YaHei;
      font-weight: 400;
      font-size: vwpx(32px);
      color: #FFFFFF;
    }

    span:nth-child(2) {
      font-family: Microsoft YaHei, Microsoft YaHei;
      font-weight: 700;
      font-size: vwpx(48px);
      color: #FFBA00;
    }

    small {
      font-family: Microsoft YaHei, Microsoft YaHei;
      font-weight: 400;
      font-size: vwpx(24px);
      color: rgba(255, 255, 255, 0.8);
      line-height: 16px;
      text-shadow: 0px 0px 10px rgba(27, 126, 242, 0.8);
      margin-left: 5px;
    }
  }

  .list {
    width: 100%;
    height: vwpx(180px);
    background: rgba(0, 0, 0, 0.1);
    box-shadow: inset 0px 0px 10px 0px #0065FF;
    border-radius: 6px;
    border: 1px solid #20A9FF;

    display: flex;
    align-items: center;
    justify-content: space-around;

    .list-item {
      flex: 1;
      display: flex;
      align-items: center;
      justify-content: center;

      .item {
        width: 100%;
        height: 100%;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;

        .name {
          font-family: Source Han Sans, Source Han Sans;
          font-weight: 500;
          font-size: vwpx(28px);
          color: #FFFFFF;
        }

        .number {
          font-family: HarmonyOS Sans SC, HarmonyOS Sans SC;
          font-weight: 500;
          font-size: vwpx(48px);
          color: #F2AF4A;
          margin-top: 3px;
        }
      }

      .border-r {
        border-right: 1px dotted rgba(156,189,255,0.5);
      }
    }
  }

  .status-list {
    margin: vwpx(20px) 0;
  }

  .type-list {}
}
</style>