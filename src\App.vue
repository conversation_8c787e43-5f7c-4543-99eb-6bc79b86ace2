<template>
  <div id="app">
    <ResetPwdDialog></ResetPwdDialog>
    <router-view/>
    <theme-picker/>
  </div>
</template>

<script>
import ThemePicker from "@/components/ThemePicker";
import ResetPwdDialog from "@/views/system/user/profile/resetPwdDialog.vue";

export default {
  name: "App",
  components: {ThemePicker, ResetPwdDialog},
  mounted() {
    // 初始化时判断token
    if (this.$store.state.user.token) {
      document.body.classList.add('has-token')
    } else {
      document.body.classList.remove('has-token')
    }
  },
  watch: {
    // 监听token变化
    '$store.state.user.token': {
      handler(newToken) {
        if (newToken) {
          document.body.classList.add('has-token')
        } else {
          document.body.classList.remove('has-token')
        }
      },
      immediate: true
    }
  },
  metaInfo() {
    return {
      title: this.$store.state.settings.dynamicTitle && this.$store.state.settings.title,
      titleTemplate: title => {
        return title ? `${title} - ${process.env.VUE_APP_TITLE}` : process.env.VUE_APP_TITLE
      }
    }
  }
};
</script>
<style scoped>
#app .theme-picker {
  display: none;
}

</style>
<style>
#maxkb {
  display: none !important;
}

.has-token #maxkb {
  display: block !important;
}
</style>
