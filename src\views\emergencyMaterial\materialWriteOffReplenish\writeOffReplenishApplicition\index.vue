<template>
  <div class="app-container maindiv">
    <el-row>
      <el-col :span="24" :xs="24">
        <el-row>
          <el-form
            ref="queryForm"
            :model="queryParams"
            size="mini"
            :inline="true"
            label-width="68px"
          >
            <el-form-item>
              <el-input v-model="queryParams.code" placeholder="编号"  />
            </el-form-item>
            <el-form-item>
              <el-input v-model="queryParams.materialName" placeholder="物资库名称" />
            </el-form-item>
            <el-form-item>
              <selectTree
                :key="'domainId'"
                style="width: 240px"
                v-model="queryParams.domainId"
                :dept-type="100"
                :expand-all="false"
                placeholder="补充单位"
                clearable
              />
            </el-form-item>
            <el-form-item>
              <el-date-picker
                  v-model="inTime"
                  type="daterange"
                  range-separator="至"
                  start-placeholder="补充起始时间"
                  end-placeholder="补充截止时间"
                  style="width: 240px"
                  value-format="yyyy-MM-dd"
                >
                </el-date-picker>
            </el-form-item>
            <el-form-item>
              <el-button
                type="primary"
                icon="el-icon-search"
                size="mini"
                @click="handleQuery"
                >搜索</el-button
              >
              <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
                >重置</el-button
              >
            </el-form-item>
          </el-form>
        </el-row>
      </el-col>
    </el-row>
    <!--操作按钮区开始-->
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          icon="el-icon-plus"
          size="mini"
          @click="openDetailDialog(null)"
          >申请
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          icon="el-icon-view"
          size="mini"
          @click="handleOpenOperate"
          >审核意见
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          icon="el-icon-download"
          size="mini"
          @click="exportList"
          >导出
        </el-button>
      </el-col>
      <right-toolbar
        :showSearch.sync="showSearch"
        @queryTable="handleQuery"
        :columns="columns"
      ></right-toolbar>
    </el-row>
    <el-row>
      <div class="draggable">
        <el-table v-adjust-table
          size="mini"
          style="width: 100%"
          v-loading="loading"
          border
          :data="tableData"
          row-key="id"
          ref="dataTable"
          stripe
          highlight-current-row
          @row-click="handleClickRow"
          :height="showSearch ? 'calc(100vh - 330px)' : 'calc(100vh - 270px)'"
        >
          <el-table-column
            label="序号"
            align="center"
            type="index"
            width="50"
          />
          <template v-for="(column, index) in columns">
            <el-table-column
              :label="column.label"
              v-if="column.visible"
              align="center"
              :prop="column.field"
              :width="column.width"
            >
              <template slot-scope="scope">
                <dict-tag
                  v-if="column.dict"
                  :options="dict.type[column.dict]"
                  :value="scope.row[column.field]"
                />
                <template v-else-if="column.slots">
                  <RenderDom
                    :row="scope.row"
                    :index="index"
                    :render="column.render"
                  />
                </template>
                <span v-else-if="column.isTime">{{
                  parseTime(scope.row[column.field], "{y}-{m}-{d}")
                }}</span>
                <span v-else>{{ scope.row[column.field] }}</span>
              </template>
            </el-table-column>
          </template>
          <el-table-column label="核销信息">
            <template slot-scope="scope">
              <el-button type="text" @click="openOutDetailDialog(scope.row)">{{ scope.row.outCode }}</el-button>
            </template>
          </el-table-column>
          <el-table-column
            label="操作"
            fixed="right"
            align="center"
            width="250"
            class-name="small-padding fixed-width"
          >
            <template slot-scope="scope">
              <el-button
                type="text"
                icon="el-icon-view"
                @click="openSubmitDialog(scope.row)"
                >提交</el-button
              >
              <el-button
                type="text"
                @click="showInfo(scope.row)"
                icon="el-icon-view"
                >查看</el-button
              >
              <el-button
                type="text"
                @click="openDetailDialog(scope.row)"
                icon="el-icon-edit"
                >编辑</el-button
              >
              <el-button
                type="text"
                @click="handleDelete(scope.row)"
                icon="el-icon-delete"
                >删除</el-button
              >
            </template>
          </el-table-column>
        </el-table>
        <pagination
          v-show="total > 0"
          :total="total"
          :page.sync="queryParams.pageNum"
          :limit.sync="queryParams.pageSize"
          @pagination="handleQuery"
        />
      </div>
    </el-row>
    <!-- 操作记录 -->
    <el-dialog
      title="操作记录"
      :visible.sync="openOperateInfo"
      width="70%"
      destroy-on-close
      v-if="openOperateInfo"
    >
      <operateInfo
        @close="modelClose"
        :businessKey="rowData.id"
        :getNodeInfo="getNodeInfo"
      ></operateInfo>
    </el-dialog>
    <!-- 查看 -->
    <el-dialog
      title="物资补充清单"
      append-to-body
      modal-append-to-body
      :visible.sync="infoDialog"
      width="70%"
      v-if="infoDialog"
    >
      <info @close="modelClose" :rowData="rowData"></info>
    </el-dialog>
    <!-- 详情 -->
    <el-dialog
      :title="drawerTitle"
      destroy-on-close
      :visible.sync="drawer"
      :close-on-click-modal="false"
      width="70%"
      v-if="drawer"
    >
      <detail @close="handleCloseDetail" :row-data="rowData"></detail>
    </el-dialog>
    <!-- 提交 -->
     <el-dialog
      title="提交"
      append-to-body
      modal-append-to-body
      :visible.sync="submitDialog"
      width="400px"
      v-if="submitDialog"
     >
      <el-input type="textarea" v-model="submitRemark"/>

      <span slot="footer" class="dialog-footer">
          <el-button size="mini" type="primary" @click="confirmSubmit"
            >确 定</el-button
          >
          <el-button size="mini" @click="modelClose">退 出</el-button>
        </span>
     </el-dialog>
     <!-- 出库信息 -->
    <el-dialog
      title="核销信息"
      append-to-body
      modal-append-to-body
      :visible.sync="outDetailDialog"
      width="80%"
      v-if="outDetailDialog"
    >
      <OutDetail :rowData="outDetail" :readonly="true"></OutDetail>
    </el-dialog>
  </div>
</template>

<script>
import Detail from "./detail.vue";
import selectTree from "@/components/DeptTmpl/selectTree.vue";
import operateInfo from "@/views/dailyMaintenance/component/operateInfo.vue";
import info from "../component/info.vue";
import OutDetail from "@/views/emergencyMaterial/materialWriteOff/writeOffApplicition/detail.vue"
import { getNodeInfo, getWriteOffReplenishList,delWriteOffReplenish, submitWriteOffReplenish } from "@/api/emergencyMaterial/materialWriteOffReplenish";
export default {
  components: {
    operateInfo,
    OutDetail,
    selectTree,
    Detail,
    info,
    RenderDom: {
      functional: true,
      props: {
        row: Object,
        index: Number,
        render: Function,
      },
      render(createElement, ctx) {
        const { row, index } = ctx.props;
        return ctx.props.render(row, index);
      },
    },
  },
  props: [],
  dicts: [],
  data() {
    return {
      showSearch: false,
      queryParams: {
        pageNum: 1,
        pageSize: 10,
      },
      total: 0,
      loading: false,
      columns: [
        {
          key: 0,
          width: 100,
          field: "code",
          label: `编号`,
          visible: true,
        },
        {
          key: 1,
          width: 100,
          field: "materialCode",
          label: `物资库编码`,
          visible: true,
        },
        {
          key: 2,
          width: 100,
          field: "materialName",
          label: `物资库名称`,
          visible: true,
        },
        {
          key: 3,
          field: "manager",
          label: `管理人员`,
          visible: true,
        },
        {
          key: 4,
          field: "domainName",
          label: `补充单位`,
          visible: true,
        },

        {
          key: 5,
          width: 100,
          field: "person",
          label: `联系人`,
          visible: true,
        },
        {
          key: 6,
          field: "personTel",
          label: `联系方式`,
          visible: true,
        },
        {
          key: 7,
          width: 100,
          field: "inTime",
          label: `补充日期`,
          visible: true,
        },
      ],
      tableData: [],
      rowData: {},
      drawerTitle: "新增物资补充",
      drawer: false,
      infoDialog: false,
      openOperateInfo: false,
      submitDialog: false,
      submitRemark: '',
      inTime: [],
      outDetail: null,
      outDetailDialog: false
    };
  },
  computed: {},
  watch: {},
  created() {
    this.handleQuery();
  },
  mounted() {},
  methods: {
    getNodeInfo,
    handleQuery() {
      this.loading = true;
      if (this.inTime && this.inTime.length > 1) {
        this.queryParams.startInTime = this.inTime[0]
        this.queryParams.endInTime = this.inTime[1]
      } else {
        this.queryParams.startInTime = ''
        this.queryParams.endInTime = ''
      }
      getWriteOffReplenishList(this.queryParams).then((res) => {
        this.loading = false;
        this.tableData = res.rows;
        this.total = res.total;
      });
    },
    resetQuery() {
      this.queryParams = {
        pageNum: 1,
        pageSize: 50,
      };
      this.inTime = []
      this.handleQuery()
    },
    openDetailDialog(row) {
      if (row) {
        this.drawerTitle = "编辑物资补充"
      } else {
        this.drawerTitle = "新增物资补充"
      }
      this.rowData = row || {}
      this.drawer = true
    },
    handleCloseDetail() {
      this.drawer = false;
      this.handleQuery();
    },
    handleDelete(row) {
      this.$modal.confirm("是否删除").then(() => {
        delWriteOffReplenish(row.id).then(()=> {
          this.$message.success('删除成功')
          this.handleQuery();
        })
      });
    },
    handleClickRow(row) {
      this.rowData = row;
    },
    handleOpenOperate() {
      if (!this.rowData.id) {
        this.$modal.msgError("请选择一条数据");
        return;
      }
      this.openOperateInfo = true;
    },
    showInfo(row) {
      this.rowData = row;
      this.infoDialog = true;
    },
    // 导出清单按钮
    exportList() {
      this.download(
        "manager/emergency/material/verification/supplement/export",
        { ...this.queryParams },
        `核销补充申请列表_${new Date().getTime()}.xlsx`,
        {
          headers: { "Content-Type": "application/json;" },
          parameterType: "body",
        }
      );
    },
    openSubmitDialog(row) {
      this.rowData = row;
      this.submitDialog = true
    },
    confirmSubmit() {
      if(!this.submitRemark) {
        this.$message.warning('请输入审核意见')
        return
      }
      const data = {
        businessKey: this.rowData.id,
        approved: true,
        comment: this.submitRemark,
        taskId: this.rowData.taskId
      }
      submitWriteOffReplenish(data).then(()=> {
        this.$message.success('提交成功')
        this.modelClose()
        this.handleQuery();
      })
    },
    openOutDetailDialog(row) {
      this.outDetail = {id: row.outId}
      this.outDetailDialog = true
    },
    modelClose() {
      this.submitDialog = false
      this.openOperateInfo = false;
      this.infoDialog = false;
    },
  },
};
</script>

<style lang="scss" scoped>
::v-deep .dialog-footer {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  width: 100%;
}
</style>
<style lang="scss" scoped>
@import "@/assets/styles/business.scss";
</style>
