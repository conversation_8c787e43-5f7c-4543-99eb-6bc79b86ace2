<template>
  <div class="sub-page">
    <!-- <div class="sub-page-header">
      <cockpit-header :title="title">
        <img src="@/assets/cockpit/back.png" @click="handleBack"
          :style="{ width: isBig ? '140px' : '60px', height: isBig ? '64px' : '28px' }" />
      </cockpit-header>
    </div> -->
    <section class="sub-page-main" :style="mainStyle">
      <div class="sub-page-l">
        <cockpit-header :title="title" fixed="static">
          <template #btn>
            <img src="@/assets/cockpit/back.png" @click="handleBack"
              :style="{ width: isBig ? '140px' : '60px', height: isBig ? '64px' : '28px' }" />
          </template>
        </cockpit-header>
        <div class="page-l">
          <component :is="componentL"></component>
        </div>
      </div>
      <div class="sub-page-r">
        <cockpit-header :title="subTitle" fixed="static" v-if="componentR">
          <static-info></static-info>
          <template #btn>
            <span></span>
          </template>
        </cockpit-header>
        <div class="page-r">
          <component :is="componentR" v-if="componentR"></component>
          <template v-else>
            <iframe :src="thirdparty" width="100%" height="100%" frameborder="0" @load="onLoad" @error="onError"></iframe>
            <!-- <object type="text/html" :data="thirdparty" width="100%" height="100%" @load="onLoad" @error="onError">
              <p>您的浏览器不支持此对象嵌入.</p>
            </object> -->
          </template>
        </div>
      </div>
    </section>
  </div>
</template>

<script>
import { isBigScreen } from '../util/utils';
import CockpitHeader from './cockpitHeader.vue';
import StaticInfo from './common/static.vue';
import BridgeSpecial from './bridge/special.vue';
import BridgeDetail from './bridge/detail.vue';
import TunnelSpecial from './tunnel/special.vue';
import TunnelDetail from './tunnel/detail.vue';
import HealthIndex from './health/index.vue';

export default {
  name: 'subpage',
  components: {
    CockpitHeader,
    StaticInfo,
    BridgeSpecial,
    BridgeDetail,
    TunnelSpecial,
    TunnelDetail,
    HealthIndex,
  },
  props: {
    title: {
      type: String,
      default: ''
    },
    subTitle: {
      type: String,
      default: ''
    },
    pageLId: {
      type: String,
      default: ''
    },
    pageRId: {
      type: String,
      default: ''
    },
  },
  data() {
    return {
      isBig: isBigScreen(),
      height: window.innerHeight || window.screen.height || window.screen.availHeight || 1080,
      componentL: this.pageLId,
      componentR: this.pageRId,
      thirdparty: 'http://mp.f3.ttvt.cc/?HoveringMouse=true',
    }
  },
  computed: {
    mainStyle() {
      let style = {};
      if (this.isBig) {
        style = {
          height: '100vh',
          overflow: 'hidden',
        }
      } else {
        style = {
          // height: this.height * 2 + 'px',
          height: '200vh',
          overflowY: 'auto',
          flexDirection: 'column',
        }
      }
      return style;
    },
  },
  methods: {
    handleBack() {
      this.$emit('back');
    },
    onLoad() {
      console.log('网页加载成功')
      this.$modal.closeLoading();
    },
    onError() {
      console.log('网页加载失败')
    },
  },
  unmounted() { },
}
</script>

<style lang="scss" scoped>
@import "@/assets/styles/utils.scss";

.sub-page {
  width: calc(100% - 0px);
  min-height: 100vh;
  overflow: hidden;

  .sub-page-header {
    width: 100%;
    height: vwpx(156px);
    z-index: 9;
  }

  .sub-page-main {
    width: 100%;
    display: flex;
    overflow: hidden;

    .sub-page-l {
      flex: 1;
      background-image: url('~@/assets/cockpit/cockpit-bg.png');
      background-repeat: no-repeat;
      background-size: 100% 100%;

      .page-l {
        width: 100%;
      }
    }

    .sub-page-r {
      flex: 1;
      background-image: url('~@/assets/cockpit/cockpit-bg.png');
      background-repeat: no-repeat;
      background-size: 100% 100%;

      .page-r {
        width: 100%;
        height: calc(100% - 100px);
      }
    }
  }
}
</style>