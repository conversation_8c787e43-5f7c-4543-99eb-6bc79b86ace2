<template>
  <el-container>
    <el-header>
      <div class="data2">
        <el-form
          :model="form2"
          :inline="true"
          label-position="right"
          label-suffix="："
          ref="tigaForm2"
          label-width="85px">
          <el-form-item label="监测类型" prop="monitorType">
            <el-select
              v-model="form2.monitorType"
              placeholder="请选择监测类型"
              @change="changeMonitorType2"
              no-data-text="无数据"
              value-key="content"
              size="small"
            >
              <el-option
                v-for="item in monitorTypeList"
                :key="item.code"
                :label="item.content"
                :value="item">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="监测内容" prop="monitorContent">
            <el-select
              v-model="form2.monitorContent"
              default-first-option
              placeholder="请选择监测内容"
              @change="changeMonitorContent2"
              no-data-text="无数据"
              value-key="content"
              size="small"
            >
              <el-option
                v-for="item in monitorContentList2"
                :key="item.code"
                :label="item.content"
                :value="item">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="监测位置" prop="monitorLocation">
            <el-select
              v-model="form2.monitorLocation"
              default-first-option
              placeholder="请选择监测位置"
              @change="changeMonitorLocation2"
              no-data-text="无数据"
              value-key="content"
              size="small"

            >
              <el-option
                v-for="item in monitorLocationList2"
                :key="item.code"
                :label="item.content"
                :value="item">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="传感器" prop="sensorId">
            <el-select
              v-model="form2.sensorId"
              placeholder="请选择传感器"
              @change="changeSensor2"
              no-data-text="无数据"
              value-key="sensorId"
              size="small"

            >
              <el-option
                v-for="item in sensorInfoList2"
                :key="item.code"
                :label="item.sensorInstallCode"
                :value="item">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="具体类型" prop="specificMonitorType">
            <el-select
              v-model="form2.specificMonitorType"
              filterable
              collapse-tags
              :placeholder="tips1"
              no-data-text="无数据"
              size="small"
              value-key="specificMonitorTypeId"
              :disabled="isDraw"
            >
              <el-option
                v-for="item in specificMonitorTypeList2"
                :label="item.specificMonitorTypeName"
                :key="item.specificMonitorTypeId"
                :value="item.specificMonitorTypeId"
              >
              </el-option>
            </el-select>
          </el-form-item>
        </el-form>
      </div>
      <div class="data1">
        <el-form
          :model="form"
          :inline="true"
          label-position="right"
          label-suffix="："
          ref="tigaForm"
          label-width="85px">
          <el-form-item label="监测类型" prop="monitorType">
            <el-select
              v-model="form.monitorType"
              placeholder="请选择监测类型"
              @change="changeMonitorType"
              no-data-text="无数据"
              value-key="content"
              size="small"
            >
              <el-option
                v-for="item in monitorTypeList"
                :key="item.code"
                :label="item.content"
                :value="item">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="监测内容" prop="monitorContent">
            <el-select
              v-model="form.monitorContent"
              default-first-option
              placeholder="请选择监测内容"
              @change="changeMonitorContent"
              no-data-text="无数据"
              value-key="content"
              size="small"

            >
              <el-option
                v-for="item in monitorContentList"
                :key="item.code"
                :label="item.content"
                :value="item">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="监测位置" prop="monitorLocation">
            <el-select
              v-model="form.monitorLocation"
              default-first-option
              placeholder="请选择监测位置"
              @change="changeMonitorLocation"
              no-data-text="无数据"
              value-key="content"
              size="small"
            >
              <el-option
                v-for="item in monitorLocationList"
                :key="item.code"
                :label="item.content"
                :value="item">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="传感器" prop="sensorId">
            <el-select
              v-model="form.sensorId"
              placeholder="请选择传感器"
              @change="changeSensor"
              no-data-text="无数据"
              value-key="sensorId"
              size="small"
            >
              <el-option
                v-for="item in sensorInfoList"
                :key="item.code"
                :label="item.sensorInstallCode"
                :value="item">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="具体类型" prop="specificMonitorType">
            <el-select
              v-model="form.specificMonitorType"
              filterable
              collapse-tags
              :placeholder="tips2"
              no-data-text="无数据"
              size="small"
              value-key="specificMonitorTypeId"
              :disabled="isDraw"
            >
              <el-option
                v-for="item in specificMonitorTypeList"
                :label="item.specificMonitorTypeName"
                :key="item.specificMonitorTypeId"
                :value="item.specificMonitorTypeId"
              >
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="时间范围" prop="timerange">
            <el-date-picker
              size="medium"
              style="width:200px"
              type="datetimerange"
              v-model="form.timerange"
              value-format="yyyy-MM-dd HH:mm:ss"
              format="yyyy-MM-dd HH:mm:ss"
              @on-change="statusDateChange"
              :transfer="true"
            ></el-date-picker>
          </el-form-item>
          <el-form-item>
            <el-button
              type="primary"
              @click="draw"
              size="small"
              :loading="loading">
              <span>绘制</span>
            </el-button>
          </el-form-item>
          <el-form-item>
            当前选择结构物为：{{ currentStructure.label }}
          </el-form-item>
        </el-form>
      </div>
    </el-header>
    <el-main>
    </el-main>
    <div id="myChart2" v-loading="loading" style="width: 100%; height: 400px; overflow:hidden"></div>
  </el-container>
</template>

<script>
import {
  getSensorByDataCode, getCorrelationAnalysis, getMax, getMin
} from "@/api/jgjc/dataAnalysis/customize/index.js";
import * as echarts from "echarts";

export default {
  name: "Correlation",
  props: ['monitorTypeList', 'currentStructure', 'isDraw'],
  emits: ['changeDraw'],
  data() {
    return {
      isZD: false,
      tips1: "请选择具体监测类型",
      tips2: "请选择具体监测类型",
      processMethod: "数据序列相关性分析",
      monitorContentList: [],
      monitorContentList2: [],
      monitorLocationList: [],
      monitorLocationList2: [],
      sensorInfoList: [],
      sensorInfoList2: [],
      specificMonitorTypeList: [],
      specificMonitorTypeList2: [],
      currentStructure: {},
      form: {
        monitorType: '',
        monitorContent: '',
        monitorLocation: '',
        sensorId: '',
        specificMonitorType: '',
        timerange: ''
      },
      form2: {
        monitorType: '',
        monitorContent: '',
        monitorLocation: '',
        sensorId: '',
        specificMonitorType: '',
      },
      sensorInfo: {},
      sensorInfo2: {},
      loading: false
    };
  },
  methods: {
    // 判断是否为长时查询
    async isLongCheck(isZD, isPreprocess) {
      // 计算时间差值
      let startTime = new Date(this.form.timerange[0]);
      let endTime = new Date(this.form.timerange[1]);
      let deltaTime = endTime.getTime() - startTime.getTime()
      let flag = false

      if (isZD) {
        // 振动数据 1天+原始值 或 任意时间+预处理
        if (isPreprocess) {
          flag = true
        } else {
          if (deltaTime > 86400000) {
            flag = true
          }
        }
      } else {
        // 普通数据 6月+原始值 或 2月+预处理
        if (isPreprocess) {
          if (deltaTime > 86400000 * 60) {
            flag = true
          }
        } else {
          if (deltaTime > 86400000 * 180) {
            flag = true
          }
        }
      }

      if (flag) {
        const res = await this.$confirm('当前条件可能造成查询时间过长, 是否继续?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).catch(err => err)
        return res === 'confirm';
      }
      return true
    },
    // “绘制”按钮的方法
    async draw() {
      if (this.form.monitorType === '' ||
        this.form.monitorContent === '' ||
        this.form.monitorLocation === '' ||
        this.form.sensorId === '' ||
        this.form.specificMonitorType === '' ||
        this.form2.monitorType === '' ||
        this.form2.monitorContent === '' ||
        this.form2.monitorLocation === '' ||
        this.form2.sensorId === '' ||
        this.form2.specificMonitorType === '' ||
        this.form.timerange.length === 0 ||
        this.form.timerange[0] === ''
      ) {
        this.$message({
          type: 'warning',
          message: "查询数据前请注意查询条件的完整性"
        });
        return;
      }
      if (await this.isLongCheck(this.isZD, true)) {
        this.loading = true
        const {code, msg, data} = await getCorrelationAnalysis({
          dataCodeX: this.form.sensorId,
          sensorCodeX: this.form.specificMonitorType,
          dataCodeY: this.form2.sensorId,
          sensorCodeY: this.form2.specificMonitorType,
          structureCode: this.currentStructure.structureCode,
          startTime: this.form.timerange[0],
          endTime: this.form.timerange[1],
        })
        if (code) {
          if (data.processedData?.errMsg) {
            this.$message.error(data.processedData?.errMsg);
          } else {
            this.setCorrelationChart("myChart2", "序列相关性分析", data)
          }
        } else {
          this.$message.error("算法调用失败");
          console.log(msg)
        }
        this.loading = false
      } else {
        // 用户取消了本次查询
      }
    },
    // 绘制相关性分析图表
    setCorrelationChart(chartName, title, data) {
      let myChart = echarts.getInstanceByDom(document.getElementById(chartName))
      if (myChart !== undefined) {
        myChart.dispose()
      }
      myChart = echarts.init(document.getElementById(chartName))
      let minPoint = [getMin(data.processedData.xdata.dataList), (getMin(data.processedData.xdata.dataList) * data.processedData.k
        + data.processedData.b).toFixed(2)]
      let maxPoint = [getMax(data.processedData.xdata.dataList), (getMax(data.processedData.xdata.dataList) * data.processedData.k
        + data.processedData.b).toFixed(2)]
      let minV = minPoint[1]
      let maxV = maxPoint[1]
      if (minPoint[1] > maxPoint[1]) {
        minV = maxPoint[1]
        maxV = minPoint[1]
      }
      let dataset = [];
      dataset.push(minPoint);
      dataset.push(maxPoint);
      for (let i in data.processedData.xdata.dataList) {
        let tmpLine = []
        tmpLine.push(data.processedData.xdata.dataList[i])
        tmpLine.push(data.processedData.ydata.dataList[i])
        dataset.push(tmpLine)
      }
      let markLineOpt = {
        animation: false,
        textAlign: 'center',
        // label: {
        //   formatter: "y = "+ data.processedData.k +" * x + " + data.processedData.b + "  相关系数为" + data.processedData.corrGust + "  ",
        //   // formatter: "y = 0.5 * x + 3",
        //   align: "right",
        //   fontSize: 18,
        //   fontWeight: 'bold'
        // },
        lineStyle: {
          type: "solid",
          color: "#ff1010"
        },
        data: [
          [
            {
              coord: minPoint,
              symbol: "none",
            },
            {
              coord: maxPoint,
              symbol: "none",
            },
          ],
        ],
      };

      let option = {
        title: {
          text: "y = " + data.processedData.k + " * x + " + data.processedData.b + "  相关系数为" + data.processedData.corrGust + "  ",
          textStyle: {
            fontSize: 18,
            fontWeight: 'bold'
          }
        },
        tooltip: {
          textStyle: {
            align: 'left'
          },
          trigger: "axis",
        },
        grid: {
          left: "3%",
          right: "3",
          bottom: "3%",
          containLabel: true,
        },
        toolbox: {
          feature: {
            saveAsImage: {
              title: '保存'
            },
          },
        },
        xAxis: {
          name: data.processedData.xdata.name + "/" + data.processedData.xdata.unit,
          nameLocation: 'middle',
          type: "value",
          scale: true,
          nameGap: 20,
          axisLabel: {
            formatter: function (value) {
              return value.toFixed(2); // 2表示小数为2位
            },
          },
        },
        yAxis: {
          name: data.processedData.ydata.name + "/" + data.processedData.ydata.unit,
          type: "value",
          scale: true,
          axisLabel: {
            formatter: function (value) {
              return value.toFixed(2); // 2表示小数为2位
            },
          },
        },
        dataZoom: [
          {
            type: "inside", //详细配置可见echarts官网
          },
        ],
        series: [
          {
            // name: "",
            type: "scatter",
            data: dataset,
            markLine: markLineOpt
          }
        ],
      };
      option && myChart.setOption(option);
      //自适应大小
      window.onresize = function () {
        myChart.resize();
      };
    },
    // 下面是一系列下拉框的change方法
    async changeMonitorType(e) {
      this.form.monitorType = e.content
      this.monitorContentList.splice(0)
      this.monitorLocationList.splice(0)
      this.sensorInfoList.splice(0)
      this.specificMonitorTypeList.splice(0)
      this.form.monitorContent = '';
      this.form.monitorLocation = '';
      this.form.sensorId = '';
      this.form.specificMonitorType = '';
      const monitorContentList = e.children;
      for (const monitorContent of monitorContentList) {
        this.monitorContentList.push(monitorContent)
      }
    },

    async changeMonitorContent(e) {
      this.form.monitorContent = e.content
      this.monitorLocationList.splice(0)
      this.sensorInfoList.splice(0)
      this.specificMonitorTypeList.splice(0)
      this.form.monitorLocation = '';
      this.form.sensorId = '';
      this.form.specificMonitorType = '';
      const monitorLocationList = e.children;
      for (const monitorLocation of monitorLocationList) {
        this.monitorLocationList.push(monitorLocation)
      }
    },

    async changeMonitorLocation(e) {
      this.form.monitorLocation = e.content
      this.sensorInfoList.splice(0)
      this.specificMonitorTypeList.splice(0)
      this.form.sensorId = '';
      this.form.specificMonitorType = '';
      const ref = await getSensorByDataCode({dataCode: e.code})
      const sensorInfoList = ref.data
      for (const sensorInfo of sensorInfoList) {
        this.sensorInfoList.push({
          code: sensorInfo.code,
          sensorId: sensorInfo.sensorId,
          sensorInstallCode: sensorInfo.dataCode,
          specificMonitorTypeId: sensorInfo.children.map(item => item.sensorCode),
          specificMonitorTypeName: sensorInfo.children.map(item => item.typeName),
        })
      }
    },

    async changeSensor(e) {
      this.specificMonitorTypeList.splice(0)
      this.form.specificMonitorType = ''
      this.form.sensorId = e.sensorInstallCode
      this.sensorInfo = e
      e.specificMonitorTypeId.forEach((value, index) => {
        this.specificMonitorTypeList.push({
          specificMonitorTypeName: e.specificMonitorTypeName[index],
          specificMonitorTypeId: e.specificMonitorTypeId[index],
        })
      })
    },

    async changeMonitorType2(e) {
      this.form2.monitorType = e.content
      this.monitorContentList2.splice(0)
      this.monitorLocationList2.splice(0)
      this.sensorInfoList2.splice(0)
      this.specificMonitorTypeList2.splice(0)
      this.form2.monitorContent = '';
      this.form2.monitorLocation = '';
      this.form2.sensorId = '';
      this.form2.specificMonitorType = '';
      const monitorContentList = e.children;
      for (const monitorContent of monitorContentList) {
        this.monitorContentList2.push(monitorContent)
      }
    },

    async changeMonitorContent2(e) {
      this.form2.monitorContent = e.content
      this.monitorLocationList2.splice(0)
      this.sensorInfoList2.splice(0)
      this.specificMonitorTypeList2.splice(0)
      this.form2.monitorLocation = '';
      this.form2.sensorId = '';
      this.form2.specificMonitorType = '';
      const monitorLocationList = e.children;
      for (const monitorLocation of monitorLocationList) {
        this.monitorLocationList2.push(monitorLocation)
      }
    },

    async changeMonitorLocation2(e) {
      this.form2.monitorLocation = e.content
      this.sensorInfoList2.splice(0)
      this.specificMonitorTypeList2.splice(0)
      this.form2.sensorId = '';
      this.form2.specificMonitorType = '';
      const ref = await getSensorByDataCode({dataCode: e.code})
      const sensorInfoList = ref.data
      for (const sensorInfo of sensorInfoList) {
        this.sensorInfoList2.push({
          code: sensorInfo.code,
          sensorId: sensorInfo.sensorId,
          sensorInstallCode: sensorInfo.dataCode,
          specificMonitorTypeId: sensorInfo.children.map(item => item.sensorCode),
          specificMonitorTypeName: sensorInfo.children.map(item => item.typeName),
        })
      }
    },

    async changeSensor2(e) {
      this.specificMonitorTypeList2.splice(0)
      this.form2.specificMonitorType = '';
      this.form2.sensorId = e.sensorInstallCode
      this.sensorInfo2 = e
      e.specificMonitorTypeId.forEach((value, index) => {
        this.specificMonitorTypeList2.push({
          specificMonitorTypeName: e.specificMonitorTypeName[index],
          specificMonitorTypeId: e.specificMonitorTypeId[index],
        })
      })
    },

    // 时间范围下拉框的change事件
    statusDateChange(e) {
      this.form.timerange = e;
    },

    // 清空表单
    resetThisForm() {
      this.monitorContentList2.splice(0)
      this.monitorLocationList2.splice(0)
      this.sensorInfoList2.splice(0)
      this.specificMonitorTypeList2.splice(0)
      this.form2.monitorContent = '';
      this.form2.monitorLocation = '';
      this.form2.sensorId = '';
      this.form2.specificMonitorType = '';
      this.form2.monitorType = '';
      this.monitorContentList.splice(0)
      this.monitorLocationList.splice(0)
      this.sensorInfoList.splice(0)
      this.specificMonitorTypeList.splice(0)
      this.form.monitorContent = '';
      this.form.monitorLocation = '';
      this.form.sensorId = '';
      this.form.specificMonitorType = '';
      this.form.monitorType = '';
    }
  }
}

</script>

<style scoped>
.el-header {
  height: auto !important;
  padding: 0;
  color: #333;
  border-bottom: 1px solid #eee;
}

.el-form-item {
  display: inline-block;
  height: 40px;
  margin: 5px 0 5px 5px;
}

.el-form {
  text-align: left;
}

.el-button {
  margin-left: 5px;
}

/deep/ .ivu-input {
  font-size: 14px !important;
}

/deep/ .el-input__inner {
  padding: 0 0 0 10px !important;
}

.el-main {
  padding: 0;
  text-align: center;
}

.data1 {
  width: 100%;
  font-family: "Microsoft YaHei";
  display: flex;
  justify-content: left;
}

.data2 {
  width: 100%;
  font-family: "Microsoft YaHei";
  display: flex;
  justify-content: left;
  border-bottom: 1px solid #eee;
}
</style>
