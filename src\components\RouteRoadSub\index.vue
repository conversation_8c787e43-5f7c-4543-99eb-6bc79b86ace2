<template>
  <div v-loading="loading" class="route-road">
    <el-select :multiple="multiple" :disabled="disabled" filterable v-model="selectValue" style="width: 100%;" :size="size" clearable :placeholder="placeholder" @change="changeSelect">
      <el-option
        v-for="item in routeOptions"
        :key="item.routeSegmentsId"
        :label="item.routeSegmentsName"
        :value="item.routeSegmentsId"
      />
    </el-select>
  </div>
</template>

<script>
import { listAllRouteSegments } from '@/api/system/routeSegments.js'
export default { // 路线选择器
  name: 'RouteRoadSub',
  components: {},
  props: {
    value: {
      type: String,
      default: ''
    },
    routeId: { // 路段id，通过路段id来查询路线信息
      type: String,
      default: '',
      require: true
    },
    placeholder: {
      type: String,
      default: '请选择路线'
    },
    size: {
      type: String,
      default: 'medium'
    },
    disabled: {
      type: Boolean,
      default: false
    },
    multiple: {
      type: <PERSON><PERSON><PERSON>,
      default: false
    }
  },
  data() {
    return {
      loading: false,
      routeOptions: []
    }
  },
  computed: {
    selectValue: {
      get: function() {
        return this.value
      },
      set: function(val) {
        this.$emit('input', val)
      }
    }
  },
  watch: {
    routeId: {
      handler(v) {
        this.getOptions()
      },
      immediate: true
    }
  },
  created() {},
  mounted() {},
  methods: {
    getOptions() {
      if (!this.routeId) return
      this.loading = true
      listAllRouteSegments({ baseRouteId: this.routeId }).then(res => {
        if (res.code === 200) {
          this.routeOptions = res.data
        }
      }).finally(() => {
        this.loading = false
      })
    },
    changeSelect(val) {
      this.$emit('change', val)
    }
  }
}
</script>

<style lang="scss" scoped>
.route-road {}
</style>
