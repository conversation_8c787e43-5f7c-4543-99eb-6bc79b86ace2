// 基础信息
const baseInfo = [
  {
    label: '管养单位性质',
    prop: 'maintenanceUnitCategory',
    placeholder: '请选择管养单位性质',
    type: 'dictSelect',
    dict: 'tunnel_maintenance_unit_category',
    rules: [{ required: true, message: '请选择管养单位性质', trigger: 'change' }]
  },
  // {
  //   label: '管理处',
  //   prop: 'managementMaintenanceId',
  //   placeholder: '请选择按管理处',
  //   type: 'selectTree',
  //   rules: [{ required: true, message: '请选择管理处', trigger: 'change' }],
  //   deptType: 201
  // },
  // {
  //   label: '管养分处',
  //   prop: 'managementMaintenanceBranchId',
  //   placeholder: '请选择管养分处',
  //   type: 'selectTree',
  //   rules: [{ required: true, message: '请选择管养分处', trigger: 'change' }],
  //   deptType: 202
  // },
  {
    label: '养护路段',
    prop: 'maintenanceSectionId',
    placeholder: '请选择养护路段',
    type: 'select',
    rules: [{ required: true, message: '请选择养护路段', trigger: 'change' }],
    options: [],
    disabled: true,
    api: 'listMaintenanceSectionAll',
    optionLabel: 'maintenanceSectionName',
    optionValue: 'maintenanceSectionId',
    disabledFieds: 'managementMaintenanceBranchId'
  },
  {
    label: '路线编码',
    prop: 'routeCode',
    placeholder: '请选择路线编码',
    type: 'select',
    rules: [{ required: true, message: '请选择路线编码', trigger: 'change' }],
    options: [],
    disabled: true,
    api: 'routeListAll',
    optionLabel: 'routeCode',
    optionValue: 'routeCode',
    disabledFieds: 'maintenanceSectionId'
  },
  {
    label: '路线名称',
    prop: 'routeName',
    placeholder: '请输入路线名称',
    type: 'select',
    api: 'routeListAll',
    optionLabel: 'routeName',
    optionValue: 'routeName',
    disabledFieds: '',
    rules: [{ required: true, message: '路线名称不能为空，选择路线编码自动带出。', trigger: 'change' }],
  },
  {
    label: '路线技术等级',
    prop: 'routeLevel',
    placeholder: '请选择路线技术等级',
    // type: 'dictSelect',
    multiple: true,
    dict: 'sys_route_grade',
    disabledFieds: '',
    type: 'multiDictSelect',
    rules: [{ required: true, message: '路线技术等级不能为空，选择养护路段自动带出。', trigger: 'change' }],
  },
  {
    label: '路段类型',
    prop: 'sectionType',
    placeholder: '请选择路段类型',
    type: 'roadType',
  
  },
  {
    label: '隧道名称',
    prop: 'tunnelName',
    placeholder: '请输入隧道名称',
    rules: [{ required: true, message: '请输入隧道名称', trigger: 'blur' }],
    type: 'input',
  },

  {
    label: '隧道竣工名称',
    prop: 'tunnelCompletedName',
    placeholder: '请输入隧道竣工名称',
    type: 'input',
  },
  {
    label: '隧道编码',
    prop: 'tunnelCode',
    placeholder: '请输入隧道编码',
    rules: [{ required: true, message: '请输入隧道编码', trigger: 'blur' }],
    type: 'input',
  },
  {
    label: '按隧道长度分类',
    prop: 'lengthClassification',
    placeholder: '请选择按隧道长度分类',
    type: 'dictSelect',
    rules: [{ required: true, message: '请选择按隧道长度分类', trigger: 'change' }],
    dict: 'tunnel_length_classification',
  },


  {
    label: '隧道所在位置',
    prop: 'direction',
    placeholder: '请选择隧道所在位置',
    type: 'dictSelect',
    dict: 'sys_route_direction',
    rules: [{ required: true, message: '请选择隧道所在位置', trigger: 'change' }]
  },

  {
    label: '入口位置经纬度',
    propLon: 'longitude',
    propLat: 'latitude',
    placeholder: '请输入口位置经纬度',
    type: 'coordinate',
    prepend: 'lonlat',
    // span:24
  },

  {
    label: '地理位置高程',
    prop: 'elevation',
    placeholder: '请输入地理位置高程',
    type: 'input',
  },
  {
    label: '所在行政区编码',
    prop: 'areaCode',
    placeholder: '请选择所在行政区编码',
    type: 'CascaderRegion',
    options: [],
    rules: [{ required: true, message: '请选择所在行政区编码', trigger: 'blur' }],
  },
  {
    label: '总体评定等级',
    prop: 'assessmentGrade',
    placeholder: '请选择总体评定等级',
    type: 'dictSelect',
    dict: 'tunnel_assess_grade',
    forView:true
  },
  {
    label: '评定单位',
    prop: 'assessmentUnit',
    placeholder: '请选择评定单位',
    type: 'input',
    forView:true
  },
  {
    label: '评定日期',
    prop: 'assessmentData',
    placeholder: '请选择评定日期',
    type: 'input',
    forView:true
  },
  {
    label: '隧道孔类型',
    prop: 'holeTypeCode',
    placeholder: '请选择隧道孔类型',
    type: 'dictSelect',
    dict: 'tunnel_hole_type_code',
    rules: [{ required: true, message: '请选择隧道孔类型', trigger: 'change' }]
  },
  {
    label: '修建年度',
    prop: 'buildDate',
    placeholder: '请选择修建年度',
    type: 'year',
    rules: [{ required: true, message: '请输入修建年度', trigger: 'blur' }]
  },
  {
    label: '建成通车日期',
    prop: 'operationDate',
    placeholder: '请选择建成通车日期',
    type: 'date',
    rules: [{ required: true, message: '请选择建成通车日期', trigger: 'change' }]
  },
  {
    label: '是否水下隧道',
    prop: 'underwaterTunnel',
    placeholder: '请选择是否水下隧道',
    type: 'dictSelect',
    dict: 'base_data_yes_no',
    rules: [{ required: true, message: '请选择是否水下隧道', trigger: 'change' }]
  },
  {
    label: '入口桩号',
    prop: 'startStake',
    placeholder: '填写纯数字需保留三位小数,例如:1.111',
    type: 'pileInput',
    precision: 3,
    rules: [{ required: true, message: '请输入入口桩号', trigger: 'blur' }]
  },
  {
    label: '中心桩号',
    prop: 'stake',
    placeholder: '填写纯数字需保留三位小数,例如:1.111',
    type: 'pileInput',
    precision: 3,
    rules: [{ required: true, message: '请输入中心桩号', trigger: 'blur' }]
  },
  {
    label: '出口桩号',
    prop: 'endStake',
    placeholder: '填写纯数字需保留三位小数,例如:1.111',
    type: 'pileInput',
    precision: 3,
    rules: [{ required: true, message: '请输入出口桩号', trigger: 'blur' }]
  },
  {
    label: '中心施工桩号',
    prop: 'constructionStake',
    placeholder: '填写纯数字需保留三位小数,例如:1.111',
    type: 'pileInput',
    precision: 3,

  },
  {
    label: '中心国高网桩号',
    prop: 'nationalNetworkStake',
    placeholder: '填写纯数字需保留三位小数,例如:1.111',
    type: 'pileInput',
    precision: 3,

  },
  {
    label: '中心统一里程桩号',
    prop: 'unifiedMileageStake',
    placeholder: '填写纯数字需保留三位小数,例如:1.111',
    type: 'pileInput',
    precision: 3,

  },


  {
    label: '是否跨省隧道',
    prop: 'crossProvinceTunnel',
    placeholder: '请选择是否跨省隧道',
    type: 'dictSelect',
    dict: 'base_data_yes_no',
    rules: [{ required: true, message: '请输入是否跨省隧道', trigger: 'blur' }]
  },
  {
    label: '隧道链接省份',
    prop: 'linkProvince',
    placeholder: '请输入隧道链接省份',
    type: 'input'
  },
  {
    label: '是否共同管养隧道',
    prop: 'whetherJointlyOwned',
    placeholder: '请选择是否共同管养隧道',
    type: 'dictSelect',
    dict: 'base_data_yes_no',
  },
  {
    label: '本省管养隧道起点桩号',
    prop: 'jointlyStakeStart',
    placeholder: '请输入本省管养隧道起点桩号',
    type: 'pileInput',
    precision: 3,
  },
  {
    label: '本省管养隧道止点桩号',
    prop: 'jointlyStakeEnd',
    placeholder: '请输入本省管养隧道止点桩号',
    type: 'pileInput',
    precision: 3,
  },
  {
    label: '是否在长大隧道目录',
    prop: 'inLongTunnelDirectory',
    placeholder: '请选择是否在长大隧道目录',
    type: 'dictSelect',
    dict: 'base_data_yes_no',
    rules: [{ required: true, message: '请输入是否在长大隧道目录', trigger: 'blur' }]
  },
  {
    label: '是否有健康监测系统',
    prop: 'whetherHealthMonitorSystemType',
    placeholder: '请选择是否有健康监测系统',
    type: 'dictSelect',
    dict: 'base_data_yes_no'
  },
  {
    label: '设计时速(km/h)',
    prop: 'designSpeed',
    placeholder: '请输入设计时速',
    type: 'inputNumber',
    rules: [
      { required: true, message: '请输入设计时速', trigger: 'blur' },
    ]
  },

  {
    label: '施工工法',
    prop: 'buildMethod',
    placeholder: '请输入施工工法',
    type: 'dictSelect',
    dict: 'tunnel_build_method',
    rules: [{ required: true, message: '填写符合实际规范的隧道施工工法', trigger: 'blur' }]

  },
  {
    label: '隧道养护等级',
    prop: 'maintenanceGrade',
    placeholder: '请选择隧道养护等级',
    type: 'dictSelect',
    dict: 'tunnel_maintenance_grade',
    forView:true
  },
  // {
  //   label: '围岩等级',
  //   prop: 'surroundingRockGrade',
  //   placeholder: '请输入围岩等级',
  //   type: 'input'
  // },



  {
    label: '管养长度(m)',
    prop: 'jointlyLength',
    placeholder: '请输入管养长度(m)',
    type: 'inputNumber',
  },
  {
    label: '路面类型',
    prop: 'pavementType',
    placeholder: '请输入路面类型',
    type: 'input'
  },
  // {
  //   label: '隧道位置',
  //   prop: 'tunnelLocation',
  //   placeholder: '请输入隧道位置',
  //   type: 'input'
  // },
  {
    label: '运营状态',
    prop: 'operationState',
    placeholder: '请选择运营状态',
    type: 'dictSelect',
    dict: 'sys_operation_state',

  },
  {
    label: '变更原因',
    prop: 'changeReasons',
    placeholder: '请选择变更原因',
    type: 'dictSelect',
    dict: 'tunnel_change_reason'
  },
  // {
  //   label: '交竣工图纸',
  //   prop: 'finishPapersPath',
  //   placeholder: '请输入交竣工图纸',
  //   type: 'input'
  // },
  // {
  //   label: '设计批复文件',
  //   prop: 'designFilesPath',
  //   placeholder: '填写文件路径，格式如：文件夹名称/附件名称.后缀',
  //   type: 'input',
  //   span:24
  // },
  {
    label: '隧道出口照片',
    prop: 'outHoleImage',
    placeholder: '请选择文件',
    type: 'uploadImg'
  },
  {
    label: '隧道入口照片',
    prop: 'inHoleImage',
    placeholder: '请选择文件',
    type: 'uploadImg'
  },
  {
    label: '隧道洞内典型照片',
    prop: 'innerImage',
    placeholder: '请选择文件',
    type: 'uploadImg'
  },
]
// 结构技术数据
const technology = [
  {
    label: '隧道长度(m)',
    prop: 'tunnelLength',
    placeholder: '请输入隧道长度',
    type: 'inputNumber',
    precision: 3,
    rules: [{ required: true, message: '请输入隧道长度', trigger: 'blur' }]
  },
  {
    label: '隧道净宽(m)',
    prop: 'tunnelWidth',
    placeholder: '请输入隧道净宽',
    type: 'inputNumber',
    precision: 3,
    rules: [{ required: true, message: '请输入隧道净宽', trigger: 'blur' }]
  },
  {
    label: '隧道净高(m)',
    prop: 'tunnelHeight',
    placeholder: '请输入隧道净高',
    type: 'inputNumber',
    precision: 3,
    rules: [{ required: true, message: '请输入隧道净高', trigger: 'blur' }]
  },

  {
    label: '拱腰半径(m)',
    prop: 'archRadius',
    placeholder: '请输入拱腰半径',
    type: 'inputNumber',
    precision: 3,
  },

  {
    label: '洞口形式',
    prop: 'holeStyle',
    placeholder: '请选择洞口形式',
    type: 'dictSelect',
    dict: 'tunnel_hole_style',
    rules: [{ required: true, message: '请选择洞口形式', trigger: 'blur' }]
  },
  {
    label: '是否衬砌',
    prop: 'whetherLiningMaterial',
    placeholder: '请选择是否衬砌',
    type: 'dictSelect',
    dict: 'base_data_yes_no'

  },
  {
    label: '衬砌类型',
    prop: 'liningType',
    placeholder: '请选择衬砌类型',
    type: 'dictSelect',
    dict: 'tunnel_lining_type',
    rules: [{ required: true, message: '请输入衬砌类型', trigger: 'blur' }]
  },
  {
    label: '衬砌材料',
    prop: 'liningMaterial',
    placeholder: '请输入衬砌材料',
    type: 'dictSelect',
    dict: 'tunnel_lining_material',
    rules: [{ required: true, message: '请输入衬砌材料', trigger: 'change' }]
  },
  {
    label:'衬砌厚度(cm)',
    prop:'liningThickness',
    placeholder:'请输入衬砌厚度(cm)',
    type:'input',
    // precision: 2,

  },

  {
    label: '进洞门类型',
    prop: 'tunnelDoorTypeIn',
    placeholder: '请输入进洞门类型',
    type: 'dictSelect',
    dict: 'tunnel_door_type'

  },
  {
    label: '出洞门类型',
    prop: 'tunnelDoorTypeOut',
    placeholder: '请输入出洞门类型',
    type: 'dictSelect',
    dict: 'tunnel_door_type'

  },
  {
    label: '是否危隧',
    prop: 'isDangerous',
    placeholder: '请否选择是否危隧',
    type: 'dictSelect',
    dict: 'base_data_yes_no',
    rules: [{ required: true, message: '请输入是否危隧', trigger: 'blur' }]
  },
  {
    label: '单车道宽度(m)',
    prop: 'singleLaneWidth',
    placeholder: '请输入单车道宽度(m)',
    type: 'inputNumber',
    precision: 3,
  },
  {
    label: '车道总宽度(m)',
    prop: 'totalLaneWidth',
    placeholder: '请输入车道总宽度',
    type: 'inputNumber',
    precision: 3,
  },
  {
    label: '检修道',
    prop: 'accessRoad',
    placeholder: '请输入检修道',
    type: 'input',
    rules: [{ required: true, message: '请输入检修道', trigger: 'blur' }]
  },
  {
    label: '检修道宽度(m)',
    prop: 'accessRoadWidth',
    placeholder: '请输入检修道宽度',
    type: 'inputNumber',
    precision: 3,
  },
  {
    label: '最大纵坡(%)',
    prop: 'maxLongitudinalSlope',
    placeholder: '请输入最大纵坡',
    type: 'inputNumber',
    min: 1,
    max: 100,
  },


  {
    label: '断面形式',
    prop: 'sectionForm',
    placeholder: '请输入断面形式',
    type: 'dictSelect',
    dict: 'tunnel_section_form',
    rules: [{ required: true, message: '请输入断面形式', trigger: 'change' }]
  },
  {
    label:'岩层地质',
    prop:'rockGeology',
    placeholder:'请输入岩层地质',
    type:'input',
  },
  {
    label:'围岩分类',
    prop:'rockClassification',
    placeholder:'请输入围岩分类',
    type:'input',
  },

  {
    label: '围岩等级',
    prop: 'surroundingRockGrade',
    placeholder: '请输入围岩等级',
    type: 'input',

  },

  {
    label:'照明设施',
    prop:'lightingFacility',
    placeholder:'请输入照明设施',
    type:'dictSelect',
    dict:'tunnel_lighting_facility'
  },
  {
    label:'通风设施',
    prop:'ventilationFacility',
    placeholder:'请输入通风设施',
    type:'dictSelect',
    dict:'tunnel_ventilation_facility'
  },
  {
    label:'监控设施',
    prop:'monitoringFacility',
    placeholder:'请输入监控设施',
    type:'input',

  },

  {
    label:'洞内纵坡',
    prop:'holeLongitudinalSlope',
    placeholder:'请输入洞内纵坡',
    type:'input',
  },
  {
    label: '路面宽度(m)',
    prop: 'pavementWidth',
    placeholder: '请输入路面宽度',
    type: 'inputNumber',
    precision: 3,
  },
  // {
  //   label: '路面厚度(m)',
  //   prop: 'pavementThickness',
  //   placeholder: '请输入路面厚度',
  //   type: 'inputNumber',
  //   precision: 2,
  // },
  // {
  //   label: '路面结构',
  //   prop: 'pavementStructure',
  //   placeholder: '请输入路面结构',
  //   type: 'input',
  // },



  // {
  //   label: '路面材料',
  //   prop: 'pavementMaterial',
  //   placeholder: '请输入路面材料',
  //   type: 'input',
  // },
  // {
  //   label: '路面等级',
  //   prop: 'pavementGrade',
  //   placeholder: '请输入路面等级',
  //   type: 'input',
  // },

  {
    label: '人行道宽',
    prop: 'personSidewalkWidth',
    placeholder: '请输入人行道宽',
    type: 'inputNumber',
    precision: 3,

  },
  {
    label: '消防设施',
    prop: 'fireFightingDevice',
    placeholder: '请选择消防设施',
    type: 'dictSelect',
    dict: 'base_data_yes_no'
  },
  {
    label:'供配电设施',
    prop:'distributionFacility',
    placeholder:'请输入供配电设施',
    type:'input',
  },
  {
    label: '路面面层类型',
    prop: 'surfaceType',
    placeholder: '请选择路面面层类型',
    type: 'dictSelect',
    dict: 'tunnel_surface_type'
  },
  {
    label: '防洪标准(年)',
    prop: 'floodStandard',
    placeholder: '请选择防洪标准(年)',
    type: 'dictSelect',
    dict: 'tunnel_flood_standard',
    rules: [{ required: true, message: '请选择防洪标准(年)', trigger: 'change' }]
  },
  {
    label: '抗震设防等级',
    prop: 'seismicGrade',
    placeholder: '请选择抗震设防等级',
    type: 'dictSelect',
    dict: 'tunnel_seismic_grade',
    rules: [{ required: true, message: '请选择抗震设防等级', trigger: 'change' }]
  },
  {
    label: '隧道排水类型',
    prop: 'drainageType',
    placeholder: '请选择隧道排水类型',
    type: 'dictSelect',
    dict: 'tunnel_drainage_type'
  },

  {
    label: '人行横洞数量',
    prop: 'personGalleryNum',
    placeholder: '请输入人行横洞数量',
    type: 'inputNumber',
    min: 0,
  },
  {
    label: '车行横洞数量',
    prop: 'carGalleryNum',
    placeholder: '请输入车行横洞数量',
    type: 'inputNumber',
    min: 0,
  },
  {
    label: '紧急停车带数量',
    prop: 'emergencyParkingStripNum',
    placeholder: '请输入紧急停车带数量',
    type: 'inputNumber',
    min: 0,
  },
  {
    label: '安全通道数量',
    prop: 'escapeRoute',
    placeholder: '请选择安全通道数量',
    type: 'inputNumber',
    min: 0,

  },
  {
    label: '配置等级',
    prop: 'allocationGrade',
    placeholder: '请输入配置等级',
    type: 'input',


  },

  {
    label: '车道数',
    prop: 'laneNum',
    placeholder: '请输入车道数',
    type: 'inputNumber',
    min: 0,
    rules: [{ required: true, message: '请选择车道数', trigger: 'blur' }],
  },
  {
    label: '车道',
    prop: 'lane',
    placeholder: '请输入车道',
    type: 'dictSelect',
    dict: 'lane',

  },

  {
    label: '设计洪水频率',
    prop: 'designedFloodFrequency',
    placeholder: '请输入设计洪水频率',
    type: 'input',
  },
  {
    label: '公用机电设施的隧道名称',
    prop: 'facilityName',
    placeholder: '请输入公用机电设施的隧道名称',
    type: 'input',
  },
  {
    label: '隧道电子设备',
    prop: 'electronicEquipment',
    placeholder: '请选择隧道电子设备',
    type: 'dictSelect',
    dict: 'tunnel_electronic_equipment'
  },
  {
    label: '机电设施类别',
    prop: 'facilityType',
    placeholder: '请输入机电设施类别',
    type: 'input',
  },
  {
    label: '共用机电设施的隧道编码',
    prop: 'facilityCode',
    placeholder: '请输入共用机电设施的隧道编码',
    type: 'input',
  },
  {
    label: '通风控制方式',
    prop: 'ventilationWay',
    placeholder: '请输入通风控制方式',
    type: 'input',
  },
  {
    label: '照明设施控制方式',
    prop: 'lightingWay',
    placeholder: '请输入照明设施控制方式',
    type: 'input',
  },
  {
    label: '进洞口防护和过渡',
    prop: 'protectionTransitionIn',
    placeholder: '请输入进洞口防护和过渡',
    type: 'input',
  },
  {
    label: '出洞口防护和过渡',
    prop: 'protectionTransitionOut',
    placeholder: '出洞口防护和过渡',
    type: 'input',
  },
]
// 隧道管理信息数据
const information = [
  {
    label: '管养单位类别',
    prop: 'mgrUnitCode',
    placeholder: '请输入管养单位类别',
    type: 'input',
  },
  {
    label: '管理单位名称',
    prop: 'mgrUnitName',
    placeholder: '请输入管理单位名称',
    type: 'input',
  },
  {
    label: '所属单位名称',
    prop: 'propertyUnitName',
    placeholder: '请输入所属单位名称',
    type: 'input',
  },
  {
    label: '监管单位名称',
    prop: 'superUnitName',
    placeholder: '请输入监管单位名称',
    type: 'input',
    rules: [{ required: true, message: '请输入监管单位', trigger: 'blur' }],
  },
  {
    label: '建设单位名称',
    prop: 'buildUnitName',
    placeholder: '请输入建设单位名称',
    type: 'input',
    rules: [{ required: true, message: '请输入建设单位', trigger: 'blur' }],
  },
  {
    label: '管理信息标注',
    prop: 'mgrRemark',
    placeholder: '请输入管理信息标注',

    type: 'input',
  },
  {
    label: '巡查单位',
    prop: 'patrolUnitName',
    placeholder: '请输入巡查单位',

    type: 'input',
  },
  {
    label: '设计单位名称',
    prop: 'designUnitName',
    placeholder: '请输入设计单位名称',
    type: 'input',
    rules: [{ required: true, message: '请输入设计单位', trigger: 'blur' }],
  },
  {
    label: '监理单位名称',
    prop: 'supervisionUnitName',
    placeholder: '请输入监理单位名称',
    type: 'input',
    rules: [{ required: true, message: '请输入监理单位', trigger: 'blur' }],
  },
  {
    label: '施工单位名称',
    prop: 'constructUnitName',
    placeholder: '请输入施工单位名称',
    type: 'input',
    rules: [{ required: true, message: '请输入施工单位', trigger: 'blur' }],
  },
  {
    label: '主管负责人',
    prop: 'mainCharger',
    placeholder: '请输入主管负责人',
    type: 'input',
  },
  {
    label: '移交管理单位',
    prop: 'transferManagementUnit',
    placeholder: '请输入移交管理单位',

    type: 'input',
  },
  {
    label: '备注',
    prop: 'remark',
    placeholder: '请输入备注',
    type: 'inputTextarea',
    span:12
  },
]


// @ApiModelProperty("设计图纸(字典项：base_archival_data)")
//     private String designDrawing;

//     @ApiModelProperty("设计文件(字典项：base_archival_data)")
//     private String designDocument;

//     @ApiModelProperty("施工文件(字典项：base_archival_data)")
//     private String constructionDocument;

//     @ApiModelProperty("竣工图纸(字典项：base_archival_data)")
//     private String completedDrawing;

//     @ApiModelProperty("验收文件(字典项：base_archival_data)")
//     private String acceptanceDocument;

//     @ApiModelProperty("定期检查报告(字典项：base_archival_data)")
//     private String periodicInspectionReportComplete;

//     @ApiModelProperty("特别检查报告(字典项：base_archival_data)")
//     private String particularlyReportComplete;

//     @ApiModelProperty("专项检查报告(字典项：base_archival_data)")
//     private String specialInspectionReportComplete;

//     @ApiModelProperty("历次维修资料(字典项：base_archival_data)")
//     private String previousMaintenanceComplete;

//     @ApiModelProperty("档案号")
//     private String fileNumberComplete;

//     @ApiModelProperty("存档案")
//     private String saveArchivesComplete;

//     @ApiModelProperty("建档时间")
//     private LocalDate filingTimeComplete;

const fileInfo=[
  {
    label: '设计图纸',
    prop: 'designDrawing',
    placeholder: '请选择设计图纸',
    type: 'dict',
    dict: 'base_archival_data'
  },

  {
    label: '设计文件',
    prop: 'designDocument',
    placeholder: '请选择设计文件',
    type: 'dict',
    dict: 'base_archival_data'
  },
  {
    label: '施工文件',
    prop: 'constructionDocument',
    placeholder: '请选择施工文件',
    type: 'dict',
    dict: 'base_archival_data'
  },
  {
    label: '竣工图纸',
    prop: 'completedDrawing',
    placeholder: '请选择竣工图纸',
    type: 'dict',
    dict: 'base_archival_data'
  },
  {
    label: '验收文件',
    prop: 'acceptanceDocument',
    placeholder: '请选择验收文件',
    type: 'dict',
    dict: 'base_archival_data'
  },
  {
    label: '定期检查报告',
    prop: 'periodicInspectionReportComplete',
    placeholder: '请选择定期检查报告',
    type: 'dict',
    dict: 'base_archival_data'
  },
  {
    label: '特别检查报告',
    prop: 'particularlyReportComplete',
    placeholder: '请选择特别检查报告',
    type: 'dict',
    dict: 'base_archival_data'
  },
  {
    label: '专项检查报告',
    prop: 'specialInspectionReportComplete',
    placeholder: '请选择专项检查报告',
    type: 'dict',
    dict: 'base_archival_data'
  },
  {
    label: '历次维修资料',
    prop: 'previousMaintenanceComplete',
    placeholder: '请选择历次维修资料',
    type: 'dict',
    dict: 'base_archival_data'
  },
  {
    label: '档案号',
    prop: 'fileNumberComplete',
    placeholder: '请输入档案号',
    type: 'input'
  },
  {
    label: '存档案',
    prop: 'saveArchivesComplete',
    placeholder: '请输入存档案',
    type: 'input'
  },
  {
    label: '建档时间',
    prop: 'filingTimeComplete',
    placeholder: '请选择建档时间',
    type: 'date'
  }
];

export default {
  baseInfo,
  technology,
  information,
  fileInfo
}
