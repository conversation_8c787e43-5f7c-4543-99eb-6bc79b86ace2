export const mainConfig = [
  {
      label: '温湿度分析',
    type: 'WSD',
    chart: [
      { label: '日温度极值时程图', type: 'WD', formType: 'default' },
      { label: '温度概率直方图', type: 'WDDIS', formType: 'maxMin' },
      { label: '湿度概率直方图', type: 'SD', formType: 'default' },
      { label: '湿度超限统计图', type: 'SDLIMIT', formType: 'default' },
    ],
  },
  {
    label: '车辆荷载分析',
    type: 'CLHZ',
    chart: [
        { label: '日车流量统计图', type: 'CLL' },
      { label: '车速概率直方统计图', type: 'CS' },
      { label: '超重车辆分布直方统计图', type: 'CZ' },
    ],
  },
  {
    label: '风速风向分析',
    type: 'WIND',
    chart: [
      { label: '风玫瑰图', type: 'ROSE' },
      { label: '风级概率分布柱状图', type: 'LEVEL' },
      { label: '10分钟风速折线图', type: 'WINDSPEED' },
    ],
  },
  {
    label: '结构温度及应变分析',
    type: 'YB',
    chart: [
      { label: '小时结构温度极值时程图', type: 'JGWD' },
      { label: '小时结构应变极值时程图', type: 'JGYB' },
      { label: '应变直方图', type: 'YBDIS' },
    ],
  },
  {
    label: '主梁竖向位移分析',
    type: 'ZLWY',
    chart: [
      { label: '位移温度相关性图', type: 'WYCOR' },
      { label: '竖向位移直方图', type: 'ZLWYDIS' },
    ],
  },
  {
    label: '伸缩缝支座梁端位移统计',
    type: 'WYTJ',
    chart: [
      { label: '累计位移时程图', type: 'LJWY' },
      { label: '位移温度相关性图', type: 'ZZWYCOR' },
    ],
  },
  {
    label: '主梁水平、塔、墩顶偏位分析',
    type: 'PW',
    chart: [
      { label: '小时均值散点图', type: 'MEANSCATTER' },
    ],
  },
  {
    label: '索力分析',
    type: 'SL',
    chart: [
      { label: '小时索力极值时程图', type: 'SLJZ' },
    ],
  },
  {
    label: '振动时程图分析',
    type: 'ZD',
    chart: [
      { label: '最大值均方根值时程图', type: 'ZDSCT' },
    ],
  },
  {
    label: '隧道边坡监测分析',
    type: 'SDJC',
    chart: [
      { label: '10分钟降雨量时辰图', type: 'JYL' },
    ],
  },
]
export const formConfig = {
  default: [
    { label: '配置项名称', type: 'text', prop: 'optionName', isRequired: 'true' },
    {
      label: '传感器选择',
      type: 'component',
      prop: 'sensorDatail',
      isRequired: 'true',
      component: 'SensorSelect',
    },
    { label: '顺序', type: 'number', prop: 'pictureOrder', isRequired: 'true' },
  ],
  maxMin: [
    { label: '配置项名称', type: 'text', prop: 'optionName', isRequired: 'true' },
    { label: '传感器选择', type: 'component', prop: 'sensorList', isRequired: 'true', component: 'SensorSelect' },
    { label: '最大值', type: 'number', prop: 'maxValue', isRequired: 'true', isDetail: true },
    { label: '最小值', type: 'number', prop: 'minValue', isRequired: 'true', isDetail: true },
    { label: '组距', type: 'number', prop: 'interval', isRequired: 'true', isDetail: true },
    { label: '顺序', type: 'number', prop: 'pictureOrder', isRequired: 'true', isDetail: true },
  ],
}
