<template>
  <div class="right-tree">
    <div class="head-container">
      <el-input
          v-model="keyword"
          placeholder="输入关键词检索"
          @change="handleSearch"
          clearable
          size="small"
          prefix-icon="el-icon-search"
          style="margin-bottom: 20px"
      />
    </div>
    <div class="tree-content">
      <el-tree
          :data="filteredTreeData"
          :expand-on-click-node="false"
          :filter-node-method="filterNode"
          ref="tree"
          node-key="id"
          highlight-current
          @node-click="handleNodeClick"
          :default-expand-all="true"
      >
      </el-tree>
    </div>
  </div>
</template>
<script>
import {findByYear,findShowDataByPlanId} from "@/api/budgetManage/planManage";

export default {
  data() {
    return {
      keyword: '',
      relaOptions: [],
      filteredTreeData: [],
      companyType:"",
      typeId:""
    }
  },
  created() {
  },
  methods: {
    // 关键词检索
    handleSearch() {
      const keyword = this.keyword.toLowerCase();
      this.filteredTreeData = this.relaOptions.filter(node => this.filterNode(node, keyword));
    },
    // 筛选节点
    filterNode(node, keyword) {
      if (String(node.label).indexOf(keyword) != -1) {
        return true;
      }
      if (node.children) {
        return node.children.some(childNode => this.filterNode(childNode, keyword));
      }
      return false;
    },
    // 查询部门下拉树结构
    getDeptTree(year) {
      findByYear(year).then(response => {
        const treeData = response.data
        this.buidTreeData(treeData);
      });
    },
    // 查询部门下拉树结构
    getShowDeptTree(id) {
      findShowDataByPlanId(id).then(response => {
        const treeData = response.data
        this.buidTreeData(treeData);
      });
    },
    //构建树结构
    buidTreeData(treeData){
      treeData.forEach(item => {
          getChild(item)
        })
        function getChild(node) {
          node.label = node.name || node.typeName || node.data.typeName
          if(node.type==0||node.type==1){
            node.id = node.type + "9999"
          }else{
            node.id = node.id || node.data.id
          }
          if ((node.configs&&node.configs.length>0)||(node.children&&node.children.length>0)) {
            node.children = node.configs || node.children
            delete node.configs
            node.children.forEach(item => {
              getChild(item)
            })
          }
        }
        this.relaOptions = treeData
        this.filteredTreeData = [...this.relaOptions]
        window.$Bus.$emit('rightTreeData', {treeData})
    },
    handleNodeClick(e,a,b) {
      console.log("分类id",e)
      if(e.id == "09999"||e.id == "19999"){
        this.typeId = ""
      }else{
        this.typeId = e.id
      }
      if(e.data){
        this.companyType = e.data.companyType
      }else{
        this.companyType = e.type
      }
      let treeIsDeep = false
      if (e.children && e.children.length > 0) {
        treeIsDeep = false
      } else {
        treeIsDeep = true
      }
      window.$Bus.$emit('passQueryParams', {companyType:this.companyType,typeId:this.typeId,label:e.label, treeIsDeep})
    },
  }
}
</script>
<style scoped lang="scss">
.right-tree {
  height: 100%;
  .head-container{
    padding: 0 10px;
  }
  .tree-content{
    overflow: auto;
    height: calc(100% - 52px);
  }
}
</style>
<style lang="scss" scoped>
@import "@/assets/styles/business.scss";
</style>
