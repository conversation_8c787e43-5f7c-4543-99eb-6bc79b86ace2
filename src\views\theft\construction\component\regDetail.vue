<template>
  <div class="app-container maindiv">
    <el-row :gutter="15">
      <el-tabs>
        <el-tab-pane label="待办" v-if="status != '-1'">
          <el-row :gutter="15">
            <el-row>
              <el-col :span="24">
                <el-form v-model="queryParams1" ref="queryForm" size="small" :inline="true" label-width="68px">
                  <el-col :span="12">
                    <el-form-item prop="code">
                      <el-input v-model="queryParams1.code" placeholder="请输入签证编码" clearable
                                style="width: 240px"/>
                    </el-form-item>
                    <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery1">搜索</el-button>
                  </el-col>
                </el-form>
              </el-col>
            </el-row>
            <el-row :gutter="10" class="mb8">
              <el-col :span="1.5">
                <el-button
                    type="success"
                    size="mini"
                    @click="handleOpenOperate"
                >操作记录
                </el-button>
              </el-col>
              <el-col :span="1.5">
                <el-button
                    type="primary"
                    size="mini"
                    v-has-menu-permi="['theft:finished:visapreview']"
                    @click="handlePreview(1)"
                >签证单预览
                </el-button>
              </el-col>
              <el-col :span="1.5">
                <el-button
                    type="warning"
                    size="mini"
                    v-has-menu-permi="['theft:finished:maintainpreview']"
                    @click="handlePreview(2)"
                >维修档案预览
                </el-button>
              </el-col>
              <el-col :span="1.5">
                <el-button
                    type="success"
                    size="mini"
                    v-has-menu-permi="['theft:finished:visaexport']"
                    @click="exportList(1)"
                >导出清单
                </el-button>
              </el-col>
              <el-col :span="1.5">
                <el-button
                    type="primary"
                    size="mini"
                    v-has-menu-permi="['theft:finished:repairpreview']"
                    @click="handlePreview(3)"
                >修复反馈
                </el-button>
              </el-col>
            </el-row>
            <el-table v-adjust-table
                size="mini"
                style="width: 100%"
                v-loading="loading"
                border
                :data="tableData1"
                row-key="id"
                ref="dataTable"
                stripe
                highlight-current-row
                @row-click="handleClickRow"
                @expand-change="loadData"
            >
              <el-table-column type="expand">
                <template slot-scope="props">
                  <el-table v-adjust-table :data="props.row.methodList" style="width: 100%" v-loading="methodLoading">
                    <el-table-column
                        prop=""
                        align="center"
                        label="">
                    </el-table-column>
                    <el-table-column
                        prop="schemeCode"
                        align="center"
                        label="子目号">
                    </el-table-column>
                    <el-table-column
                        prop="schemeName"
                        align="center"
                        label="子目名称">
                    </el-table-column>
                    <el-table-column
                        prop="calcDesc"
                        align="center"
                        label="计算式">
                    </el-table-column>
                    <el-table-column
                        prop="num"
                        align="center"
                        label="方法数量">
                    </el-table-column>
                    <el-table-column
                        prop="unit"
                        align="center"
                        label="方法单位">
                    </el-table-column>
                    <el-table-column
                        prop="price"
                        align="center"
                        label="单价">
                    </el-table-column>
                    <el-table-column
                        prop="amount"
                        align="center"
                        label="金额">
                    </el-table-column>
                    <el-table-column
                        prop="remark"
                        align="center"
                        label="备注">
                    </el-table-column>
                  </el-table>
                </template>
              </el-table-column>
              <el-table-column
                  label="序号"
                  align="center"
                  type="index"
                  width="50"
              />
              <template v-for="(column,index) in columns1">
                <el-table-column :label="column.label"
                                 v-if="column.visible" show-overflow-tooltip
                                 align="center"
                                 :prop="column.field">
                  <template slot-scope="scope">
                    <dict-tag v-if="column.dict" :options="dict.type[column.dict]" :value="scope.row[column.field]"/>
                    <template v-else-if="column.slots">
                      <RenderDom :row="scope.row" :index="index" :render="column.render"/>
                    </template>
                    <span v-else-if="column.isTime">{{ parseTime(scope.row[column.field], '{y}-{m}-{d}') }}</span>
                    <span v-else>{{ scope.row[column.field] }}</span>
                  </template>
                </el-table-column>
              </template>
              <el-table-column
                  label="操作"
                  fixed="right"
                  align="center"
                  width="250"
                  class-name="small-padding fixed-width"
              >
                <template slot-scope="scope" v-if="scope.row.userId !== 1">

                  <el-button
                      size="mini"
                      type="text"
                      icon="el-icon-edit"
                      v-if="status == 0"
                      v-has-menu-permi="['theft:finished:edit']"
                      @click="handleEdit (scope.row)"
                  >编辑
                  </el-button>
                  <el-button
                      size="mini"
                      type="text"
                      icon="el-icon-delete"
                      v-if="status == 0"
                      v-has-menu-permi="['theft:finished:remove']"
                      @click="handleDelete (scope.row)"
                  >删除
                  </el-button>
                  <el-button
                      size="mini"
                      type="text"
                      icon="el-icon-check"
                      v-if="status == 0"
                      v-has-menu-permi="['theft:finished:process']"
                      @click="handleSubmit (scope.row)"
                  >提交
                  </el-button>
                  <el-button
                      size="mini"
                      type="text"
                      icon="el-icon-check"
                      v-if="status == 1"
                      v-has-menu-permi="['theft:finished:process']"
                      @click="handleExamine (scope.row)"
                  >施工单位审核
                  </el-button>
                  <el-button
                      size="mini"
                      type="text"
                      icon="el-icon-check"
                      v-if="status == 2"
                      v-has-menu-permi="['theft:finished:process']"
                      @click="handleSupExamine (scope.row)"
                  >监理审核
                  </el-button>
                  <el-button
                      size="mini"
                      type="text"
                      icon="el-icon-check"
                      v-if="status == 3"
                      v-has-menu-permi="['theft:finished:process']"
                      @click="handleReg (scope.row)"
                  >验收登记
                  </el-button>
                </template>
              </el-table-column>
            </el-table>
            <pagination
                v-show="total1>0"
                :total="total1"
                :page.sync="queryParams1.pageNum"
                :limit.sync="queryParams1.pageSize"
                @pagination="handleQuery1"
            />
          </el-row>
        </el-tab-pane>
        <el-tab-pane label="签证单">
          <el-row :gutter="15">
            <el-row>
              <el-col :span="24">
                <el-form v-model="queryParams2" ref="queryForm" size="small" :inline="true" label-width="68px">
                  <el-col :span="24">
                    <el-form-item prop="code">
                      <el-input v-model="queryParams2.code" placeholder="请输入签证编码" clearable
                                style="width: 240px"/>
                    </el-form-item>
                    <el-form-item prop="status">
                      <el-input v-model="queryParams2.status" placeholder="请输入施工状态" clearable
                                style="width: 240px"/>
                    </el-form-item>
                    <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery2">搜索</el-button>
                  </el-col>
                </el-form>
              </el-col>
            </el-row>
            <el-row :gutter="10" class="mb8">
              <el-col :span="1.5">
                <el-button
                    type="success"
                    size="mini"
                    @click="handleOpenOperate"
                >操作记录
                </el-button>
              </el-col>
              <el-col :span="1.5">
                <el-button
                    type="primary"
                    size="mini"
                    @click="handlePreview(1)"
                >签证单预览
                </el-button>
              </el-col>
              <el-col :span="1.5">
                <el-button
                    type="warning"
                    size="mini"
                    @click="handlePreview(2)"
                >维修档案预览
                </el-button>
              </el-col>
              <el-col :span="1.5">
                <el-button
                    type="success"
                    size="mini"
                    v-has-menu-permi="['theft:finished:export']"
                    @click="exportList()"
                >导出清单
                </el-button>
              </el-col>
              <el-col :span="1.5">
                <el-button
                    type="primary"
                    size="mini"
                    @click="handlePreview(3)"
                >修复反馈
                </el-button>
              </el-col>
              <el-col :span="1.5">
                <el-button
                  v-if="status == '-1'"
                  type="warning"
                  size="mini"
                  v-has-menu-permi="['theft:finished:updateBaseInfo']"
                  @click="updateInfo">
                  修改签证单
                </el-button>
              </el-col>
              <el-col :span="1.5">
                <el-button
                  type="warning"
                  icon="el-icon-view"
                  size="mini"
                  v-has-permi="['settlement:repository:regenerate']"
                  @click="handleRegenerate"
                >重新生成报表
                </el-button>
              </el-col>
            </el-row>
            <el-table v-adjust-table
                size="mini"
                style="width: 100%"
                v-loading="loading"
                border
                :data="tableData2"
                row-key="id"
                ref="dataTable"
                stripe
                highlight-current-row
                @selection-change="handleSelectionChange"
                @row-click="handleClickRow"
                @expand-change="loadData"
            >
              <el-table-column type="selection" width="50" align="center"/>
              <el-table-column type="expand">
                <template slot-scope="props">
                  <el-table v-adjust-table :data="props.row.methodList" style="width: 100%" v-loading="methodLoading">
                    <el-table-column
                        prop=""
                        align="center"
                        label="">
                    </el-table-column>
                    <el-table-column
                        prop="schemeCode"
                        align="center"
                        label="子目号">
                    </el-table-column>
                    <el-table-column
                        prop="schemeName"
                        align="center"
                        label="子目名称">
                    </el-table-column>
                    <el-table-column
                        prop="calcDesc"
                        align="center"
                        label="计算式">
                    </el-table-column>
                    <el-table-column
                        prop="num"
                        align="center"
                        label="方法数量">
                    </el-table-column>
                    <el-table-column
                        prop="unit"
                        align="center"
                        label="方法单位">
                    </el-table-column>
                    <el-table-column
                        prop="price"
                        align="center"
                        label="单价">
                    </el-table-column>
                    <el-table-column
                        prop="amount"
                        align="center"
                        label="金额">
                    </el-table-column>
                    <el-table-column
                        prop="remark"
                        align="center"
                        label="备注">
                    </el-table-column>
                  </el-table>
                </template>
              </el-table-column>
              <el-table-column
                  label="序号"
                  align="center"
                  type="index"
                  width="50"
              />
              <template v-for="(column,index) in columns1">
                <el-table-column :label="column.label"
                                 v-if="column.visible" show-overflow-tooltip
                                 align="center"
                                 :prop="column.field">
                  <template slot-scope="scope">
                    <dict-tag v-if="column.dict" :options="dict.type[column.dict]" :value="scope.row[column.field]"/>
                    <template v-else-if="column.slots">
                      <RenderDom :row="scope.row" :index="index" :render="column.render"/>
                    </template>
                    <span v-else-if="column.isTime">{{ parseTime(scope.row[column.field], '{y}-{m}-{d}') }}</span>
                    <span v-else>{{ scope.row[column.field] }}</span>
                  </template>
                </el-table-column>
              </template>
            </el-table>
            <pagination
                v-show="total2>0"
                :total="total2"
                :page.sync="queryParams2.pageNum"
                :limit.sync="queryParams2.pageSize"
                @pagination="handleQuery2"
            />
          </el-row>
        </el-tab-pane>
        <el-tab-pane label="方法清单">
          <el-row :gutter="15">
            <el-row>
              <el-col :span="24">
                <el-form v-model="queryParams3" ref="queryForm" size="small" :inline="true" label-width="68px">
                  <el-col :span="24">
                    <el-form-item prop="schemeName">
                      <el-input v-model="queryParams3.schemeName" placeholder="请输入子目名称" clearable
                                style="width: 240px"/>
                    </el-form-item>
                    <el-form-item prop="schemeCode">
                      <el-input v-model="queryParams3.schemeCode" placeholder="请输入子目号" clearable
                                style="width: 240px"/>
                    </el-form-item>
                    <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery3">搜索</el-button>
                  </el-col>
                </el-form>
              </el-col>
            </el-row>
            <el-row :gutter="10" class="mb8">
              <el-col :span="1.5">
                <el-button
                    type="success"
                    size="mini"
                    v-has-menu-permi="['theft:method:export']"
                    @click="exportList2()"
                >导出清单
                </el-button>
              </el-col>
            </el-row>
            <el-table v-adjust-table
                size="mini"
                style="width: 100%"
                v-loading="loading"
                border
                :data="tableData3"
                row-key="id"
                ref="dataTable"
                stripe
                highlight-current-row
            >
              <el-table-column
                  label="序号"
                  align="center"
                  type="index"
                  width="50"
              />
              <template v-for="(column,index) in columns3">
                <el-table-column :label="column.label"
                                 v-if="column.visible" show-overflow-tooltip
                                 align="center"
                                 :prop="column.field">
                  <template slot-scope="scope">
                    <dict-tag v-if="column.dict" :options="dict.type[column.dict]" :value="scope.row[column.field]"/>
                    <template v-else-if="column.slots">
                      <RenderDom :row="scope.row" :index="index" :render="column.render"/>
                    </template>
                    <span v-else-if="column.isTime">{{ parseTime(scope.row[column.field], '{y}-{m}-{d}') }}</span>
                    <span v-else>{{ scope.row[column.field] }}</span>
                  </template>
                </el-table-column>
              </template>
            </el-table>
            <pagination
                v-show="total3>0"
                :total="total3"
                :page.sync="queryParams3.pageNum"
                :limit.sync="queryParams3.pageSize"
                @pagination="handleQuery3"
            />
          </el-row>
        </el-tab-pane>
      </el-tabs>
    </el-row>
    <el-drawer append-to-body modal-append-to-body :wrapperClosable="false" title="修改签证" destroy-on-close :visible.sync="openAdd" v-if="openAdd" size="70%">
      <detail :row-data="rowData" :detail-data="row" @close="handleCloseDetail"></detail>
    </el-drawer>
    <el-drawer append-to-body modal-append-to-body :wrapperClosable="false" title="施工单位审核" destroy-on-close :visible.sync="openExamine" v-if="openExamine" size="70%">
      <examine-detail :row-data="row" @close="handleCloseDetail"></examine-detail>
    </el-drawer>
    <el-drawer append-to-body modal-append-to-body :wrapperClosable="false" title="监理审核" destroy-on-close :visible.sync="openSupExamine" v-if="openSupExamine" size="70%">
      <sup-examine-detail :row-data="row" @close="handleCloseDetail"></sup-examine-detail>
    </el-drawer>
    <el-drawer append-to-body modal-append-to-body :wrapperClosable="false" title="验收登记" destroy-on-close :visible.sync="openReg" v-if="openReg" size="70%">
      <reg-detail :row-data="row" @close="handleCloseDetail"></reg-detail>
    </el-drawer>
    <IFramePreview ref="iframeRef" :srcdoc="preview.html" :down-url="preview.url" :file-name="preview.fileName"></IFramePreview>


    <el-dialog append-to-body modal-append-to-body  title="操作意见" :visible.sync="openOperateInfo" v-if="openOperateInfo" width="80%" destroy-on-close>
      <reg-record :row="clickRow" :row-data="rowData"></reg-record>
    </el-dialog>
    <el-dialog append-to-body modal-append-to-body  title="修改签证单" :visible.sync="openUpdateInfo" v-if="openUpdateInfo" width="50%" destroy-on-close>
      <el-form v-loading="loading" size="small" :inline="true" :model="updateForm" :rules="rules" ref="form" label-width="120px">
        <el-row>
          <el-col :span="24">
            <el-form-item label="签证单编码" prop="code">
              <el-input style="width: 38vw;" v-model="updateForm.code" placeholder="请输入签证单编码" clearable/>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="计算式说明" prop="calculationDesc">
              <el-input style="width: 38vw;" type="textarea" :rows="4" v-model="updateForm.calculationDesc" placeholder="请输入计算式说明" clearable/>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <div style="text-align: right">
              <el-button type="primary" @click="saveUpdate">保 存</el-button>
            </div>
          </el-col>
        </el-row>
      </el-form>
    </el-dialog>
  </div>
</template>
<script>
import {
  delFinishedDetail,
  getAllMethodList,
  getFinishedList,
  getMethodList,
  finishedProcess,
  getFinishedPendingList,
  getNodeInfo,
  previewVisa,
  previewMaintain,
  previewRepair,
  checkSupProcess,
  updateBaseInfo
} from "@/api/theft/construction";
import Detail from "@/views/theft/construction/completionReg/detail.vue";
import ExamineDetail from "@/views/theft/construction/constructionOrderReview/detail.vue";
import SupExamineDetail from "@/views/theft/construction/supervisionReview/detail.vue";
import RegDetail from "@/views/theft/construction/inspectionReg/detail.vue";

import operateInfo from "@/views/dailyMaintenance/component/operateInfo.vue";
import RegRecord from "./regRecord.vue";
import IFramePreview from "@/components/IFramePreview/index.vue";
import {regenerateReport} from "@/api/dailyMaintenance/metering/addPrice";

export default {
  components: {
    IFramePreview,
    RegRecord,
    operateInfo,
    Detail,
    ExamineDetail,
    SupExamineDetail,
    RegDetail,
    RenderDom: {
      functional: true,
      props: {
        row: Object,
        index: Number,
        render: Function
      },
      render(createElement, ctx) {
        const {row, index} = ctx.props
        return ctx.props.render(row, index)
      }
    }
  },
  dicts: ['project_visa_status'],
  props: {
    rowData: {
      type: Object,
      default: () => {
      }
    },
    status: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      loading: false,
      methodLoading: false,
      openAdd: false,
      openExamine: false,
      openSupExamine: false,
      openReg: false,
      tableData1: [],
      tableData2: [],
      tableData3: [],
      columns1: [
        {key: 0, width: 100, field: 'status', label: `状态`, visible: true, dict: 'project_visa_status'},
        {key: 1, width: 100, field: 'code', label: `签证编码`, visible: true},
        {key: 2, width: 100, field: 'sumFund', label: `签证金额`, visible: true},
        {key: 3, width: 100, field: 'createTime', label: `签证时间`, visible: true},
        {key: 4, width: 100, field: 'createuser', label: `签证人`, visible: true},
      ],
      columns3: [
        {key: 0, width: 100, field: 'schemeCode', label: `子目号`, visible: true},
        {key: 1, width: 100, field: 'schemeName', label: `子目名称`, visible: true},
        {key: 2, width: 100, field: 'unit', label: `单位`, visible: true},
        {key: 3, width: 100, field: 'price', label: `单价`, visible: true},
        {key: 4, width: 100, field: 'num', label: `数量`, visible: true},
        {key: 5, width: 100, field: 'amount', label: `金额`, visible: true},
      ],
      row: {},
      clickRow: {},
      queryParams1: {
        pageNum: 1,
        pageSize: 50
      },
      total1: 0,
      queryParams2: {
        pageNum: 1,
        pageSize: 50
      },
      total2: 0,
      queryParams3: {
        pageNum: 1,
        pageSize: 50
      },
      total3: 0,
      preview: {
        html: '',
        url: '',
        fileName: ''
      },
      openOperateInfo: false,
      openUpdateInfo: false,
      updateForm: {},
      rules: {
        code: [
          {required: true, message: '请输入签证单编码', trigger: 'blur'},
        ],
        calculationDesc: [
          {required: true, message: '请输入计算式说明', trigger: 'blur'},
        ]
      },
      ids: []
    }
  },
  mounted() {
    this.handleQuery1()
    this.handleQuery2()
    this.handleQuery3()

  },
  methods: {
    getNodeInfo,
    handleQuery1() {
      if (this.status == '-1') {
        return
      }
      this.loading = true
      this.queryParams1.conId = this.rowData.id
      this.queryParams1.status = this.status
      getFinishedPendingList(this.queryParams1).then(res => {
        this.tableData1 = res.rows
        this.total1 = res.total
        this.loading = false
      })
    },
    handleQuery2() {
      this.loading = true
      this.queryParams2.conId = this.rowData.id
      getFinishedList(this.queryParams2).then(res => {
        this.tableData2 = res.rows
        this.total2 = res.total
        this.loading = false
      })
    },
    handleQuery3() {
      this.loading = true
      this.queryParams3.constructionId = this.rowData.id
      getAllMethodList(this.queryParams3).then(res => {
        this.tableData3 = res.rows
        this.total3 = res.total
        this.loading = false
      })
    },
    loadData(row) {
      this.methodLoading = true
      getMethodList({finishedId: row.id, pageNum: 1, pageSize: 999}).then(res => {
        this.$set(row, 'methodList', res.rows)
        this.methodLoading = false
      })
    },
    handleEdit(row) {
      this.row = row
      this.openAdd = true
    },
    handleDelete(row) {
      this.$modal.confirm("是否确认删除").then(() => {
        this.loading = true
        delFinishedDetail(row.id).then(() => {
          this.$modal.msgSuccess("删除成功")
          this.handleQuery1()
          this.$emit("query")
        })
      })
    },
    handleSubmit(row) {
      this.$prompt('', '提交验收', {
        confirmButtonText: '提交',
        cancelButtonText: '取消',
        inputPlaceholder: '请输入',
        inputType: 'textarea'
      }).then(({ value }) => {
        this.loading = true
        finishedProcess({
          businessKey: row.id,
          taskId: row.taskId,
          approved: true,
          isSupProcess: 2,
          comment: value
        }).then(() => {
          this.$modal.msgSuccess("提交成功")
          this.handleQuery1()
        })
      })
    },
    handleExamine(row) {
      this.row = row
      this.openExamine = true
    },
    handleSupExamine(row) {
      checkSupProcess(row.conId).then(res => {
        if (res.data) {
          this.row = row
          this.openSupExamine = true
        } else {
          this.$modal.msgError("施工单未被监理接收,不能进行审核操作!")
        }
      })
    },
    handleReg(row) {
      this.row = row
      this.openReg = true
    },
    handleClickRow(e) {
      this.clickRow = e
    },
    handleOpenOperate() {
      if (!this.clickRow.id) {
        this.$message.warning('请先选择一条记录！')
        return
      }
      this.openOperateInfo = true
    },
    updateInfo() {
      if (!this.clickRow.id) {
        this.$message.warning('请先选择一条记录！')
        return
      }
      this.updateForm = {
        id: this.clickRow.id,
        code: this.clickRow.code,
        calculationDesc: this.clickRow.calculationDesc,
      }
      this.openUpdateInfo = true
    },
    saveUpdate() {
      this.$refs.form.validate(valid => {
        if (!valid) return
        this.loading = true
        updateBaseInfo(this.updateForm).then(res => {
          this.$modal.msgSuccess("修改成功")
          this.clickRow = {}
          this.openUpdateInfo = false
          this.handleQuery2()
        }).finally(() => {
          this.loading = false
        })
      })
    },
    // 导出清单按钮
    exportList(status) {
      this.queryParams1.status = status ? this.status : undefined
      this.queryParams1.conId = this.rowData.id
      this.download(
          'manager/theft/construction/finished/export/visa',
          {...this.queryParams1},
          `${status ? '待办签证单':'签证单'}.xlsx`,
          {
            headers: {'Content-Type': 'application/json;'},
            parameterType: 'body'
          }
      )
    },
    // 导出清单按钮
    exportList2(status) {
      this.queryParams3.constructionId = this.rowData.id
      this.download(
          'manager/theft/construction/finished/method/export',
          {...this.queryParams3},
          `方法清单.xlsx`,
          {
            headers: {'Content-Type': 'application/json;'},
            parameterType: 'body'
          }
      )
    },
    // 报表预览
    handlePreview(type) {
      if (!this.clickRow.id) {
        this.$message.warning('请先选择一条记录！')
        return
      }
      this.loading = true
      if (type == 1) {
        previewVisa(this.clickRow.id).then(res => {
          if (res.code == 200){
            this.preview.html = res.data.html
            this.preview.url = res.data.downUrl
            this.preview.fileName = res.data.fileName
            this.$refs.iframeRef.visible = true
          }
          this.loading = false
        })
      }

      if (type == 2) {
        previewMaintain(this.clickRow.id).then(res => {
          if (res.code == 200){
            this.preview.html = res.data.html
            this.preview.url = res.data.downUrl
            this.preview.fileName = res.data.fileName
            this.$refs.iframeRef.visible = true
          }
          this.loading = false
        })
      }

      if (type == 3) {
        previewRepair(this.clickRow.id).then(res => {
          if (res.code == 200){
            this.preview.html = res.data.html
            this.preview.url = res.data.downUrl
            this.preview.fileName = res.data.fileName
            this.$refs.iframeRef.visible = true
          }
          this.loading = false
        })
      }
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection
    },
    handleRegenerate() {
      if (this.ids.length == 0) {
        this.$modal.msgError("请勾选至少一条数据")
        return
      }
      const params = {
        idList: this.ids.map(item => item.id),
        type: 7
      }
      regenerateReport(params).then(res => {
        this.$modal.msgSuccess("操作成功")
      })
    },
    handleCloseDetail() {
      this.openAdd = false
      this.openExamine = false
      this.openSupExamine = false
      this.openReg = false
      this.handleQuery1()
      this.handleQuery2()
      this.handleQuery3()
      this.$emit('query')
    }
  }
}
</script>
<style scoped lang="scss">

</style>
<style lang="scss" scoped>
@import "@/assets/styles/business.scss";
</style>
