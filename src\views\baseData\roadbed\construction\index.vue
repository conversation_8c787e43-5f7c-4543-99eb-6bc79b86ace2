<template>
  <PageContainer>
    <template slot="search">
      <div style="display: flex; align-items: center; margin: 0">
        <CascadeSelection
          :form-data="queryParams"
          v-model="queryParams"
          types="201"
          multiple
        />
        <el-select
          style="margin-left: 20px; width: 170px"
          v-model="queryParams.status"
          placeholder="数据状态"
          clearable
        >
          <el-option
            v-for="dict in dict.type.base_data_state"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>

        <div style="min-width:220px;height:32px;margin-left: 20px;">
          <el-button
            type="primary"
            icon="el-icon-search"
            @click="handleQuery"
          >搜索</el-button>
          <el-button
            icon="el-icon-refresh"
            @click="resetQuery"
          >重置</el-button>
        </div>
      </div>
    </template>

    <template slot="header">
      <div class="button-list">
        <el-button
          v-hasPermi="['baseData:pavementConstruction:add']"
          type="primary"
          @click="handleAdd"
        >新增</el-button>
        <el-button
          v-hasPermi="['baseData:pavementConstruction:edit']"
          type="primary"
          @click="handleUpdate"
        >编辑</el-button>
        <el-button
          v-hasPermi="['baseData:pavementConstruction:remove']"
          type="primary"
          @click="handleDelete"
        >删除</el-button>
        <el-button
          v-hasPermi="['baseData:pavementConstruction:query']"
          type="primary"
          @click="handleView"
        >查看</el-button>
        <el-button
          type="primary"
          v-hasPermi="['baseData:import:execute']"

          @click="importUpdate"
        >导入更新</el-button>
        <el-button
          type="primary"
          v-hasPermi="['baseData:import:execute']"
          @click="importAdd"
        >导入新增</el-button>
        <el-button
          v-hasPermi="['baseData:pavementConstruction:export']"
          type="primary"
          @click="exportList"
        >数据导出</el-button>
        <!-- <el-button
          type="primary"
          @click="changeStatus"
        >运营状态变更</el-button> -->
        <!-- <el-button
          type="primary"
          @click="downloadQrcode"
        >二维码下载</el-button> -->
      </div>
    </template>

    <template slot="body">
      <el-table
        v-adjust-table
        v-loading="loading"
        ref="table"
        height="100%"
        style="width: 100%"
        border
        :data="tableData"
        :row-style="rowStyle"
        @selection-change="handleSelectionChange"
        @row-click="handleRowClick"
      >
        <el-table-column
          type="selection"
          width="50"
          align="center"
        />
        <el-table-column
          fixed
          label="序号"
          type="index"
          width="50"
          align="center"
        >
          <template v-slot="scope">
            {{
              scope.$index +
              (queryParams.pageNum - 1) * queryParams.pageSize +
              1
            }}
          </template>
        </el-table-column>
        <el-table-column
          fixed
          label="管理处"
          align="center"
          prop="managementMaintenanceName"
          min-width="120"
          show-overflow-tooltip
        />
        <el-table-column
          fixed
          label="管养分处"
          align="center"
          prop="managementMaintenanceBranchName"
          min-width="120"
          show-overflow-tooltip
        />
        <el-table-column
          label="路段名称"
          align="center"
          prop="maintenanceSectionName"
          min-width="120"
          show-overflow-tooltip
        />
        <el-table-column
          label="路线编码"
          align="center"
          prop="routeCode"
          min-width="120"
          show-overflow-tooltip
        />
        <el-table-column
          label="路线名称"
          align="center"
          prop="routeName"
          min-width="120"
          show-overflow-tooltip
        />
        <el-table-column
          label="调查方向"
          align="center"
          prop="direction"
          min-width="120"
          show-overflow-tooltip
        >
          <template slot-scope="scope">
            <dict-tag
              :options="dict.type.sys_route_direction"
              :value="scope.row.direction"
            />
          </template>
        </el-table-column>
        <el-table-column
          label="具体方向"
          align="center"
          prop="specificDirection"
          min-width="120"
          show-overflow-tooltip
        />

        <el-table-column
          label="路线技术等级"
          align="center"
          prop="techLevel"
          min-width="120"
          show-overflow-tooltip
        >
          <template slot-scope="scope">
            <dict-tag
              :options="dict.type.sys_route_grade"
              :value="scope.row.techLevel"
            />
          </template>
        </el-table-column>
        <el-table-column
          label="数据状态"
          align="center"
          prop="status"
          min-width="100"
        >
        <template slot-scope="scope">
            <el-link
              :type="{ 1: 'info', 2: 'success' }[scope.row.status]"
              :underline="false"
            >
              <DictTag
                :value="scope.row.status"
                :options="dict.type.base_data_state"
              />
            </el-link>
          </template>
        </el-table-column>

        <el-table-column
          label="路面类型"
          align="center"
          prop="pavType"
          min-width="120"
          show-overflow-tooltip
        >
        </el-table-column>
        <el-table-column
          label="统一里程"
          align="center"
        >
          <el-table-column
            prop="unifiedMileageStartStake"
            label="起点桩号"
            min-width="120"
            align="center"
          >
            <template slot-scope="scope">
              <span v-if="scope.row && scope.row.unifiedMileageStartStake">
                {{ formatPile(scope.row.unifiedMileageStartStake) }}
              </span>
            </template>
          </el-table-column>
          <el-table-column
            prop="unifiedMileageEndStake"
            label="终点桩号"
            min-width="120"
            align="center"
          >
            <template slot-scope="scope">
              <span v-if="scope.row && scope.row.unifiedMileageEndStake">
                {{ formatPile(scope.row.unifiedMileageEndStake) }}
              </span>
            </template>
          </el-table-column>
          <el-table-column
            prop="unifiedMileageRangeLength"
            label="管养里程(m)"
            min-width="120"
            align="center"
          >
          </el-table-column>
        </el-table-column>
        <el-table-column
          label="施工里程"
          align="center"
        >
          <el-table-column
            prop="constructionStartStake"
            label="起点桩号"
            min-width="120"
            align="center"
          >
            <template slot-scope="scope">
              <span v-if="scope.row && scope.row.constructionStartStake">
                {{ formatPile(scope.row.constructionStartStake) }}
              </span>
            </template>
          </el-table-column>
          <el-table-column
            prop="constructionEndStake"
            label="终点桩号"
            min-width="120"
            align="center"
          >
            <template slot-scope="scope">
              <span v-if="scope.row && scope.row.constructionEndStake">
                {{ formatPile(scope.row.constructionEndStake) }}
              </span>
            </template>
          </el-table-column>
          <el-table-column
            prop="constructionRangeLength"
            label="施工里程(m)"
            min-width="120"
            align="center"
          >
          </el-table-column>
        </el-table-column>
        <el-table-column
          label="位置"
          align="center"
          prop="location"
          min-width="120"
          show-overflow-tooltip
        />
        <el-table-column
          label="铺筑长度(m)"
          align="center"
          prop="auxiliaryBuildLength"
          min-width="120"
          show-overflow-tooltip
        />
        <el-table-column
          label="宽度"
          align="center"
          prop="width"
          min-width="120"
          show-overflow-tooltip
        />
        <el-table-column
          label="路基段"
          align="center"
        >
          <el-table-column
            label="底基层"
            align="center"
          >
            <el-table-column
              prop="roadbedBotBaseThickness"
              label="厚度(cm)"
              min-width="120"
              align="center"
            >
            </el-table-column>
            <el-table-column
              prop="roadbedBotBaseType"
              label="结构类型"
              min-width="120"
              align="center"
            >
            </el-table-column>
            <el-table-column
              prop="roadbedBotBaseWidth"
              label="宽度(m)"
              min-width="120"
              align="center"
            >
            </el-table-column>
          </el-table-column>
          <el-table-column
            label="基层"
            align="center"
          >
            <el-table-column
              prop="roadbedBaseThickness"
              label="厚度(cm)"
              min-width="120"
              align="center"
            >
            </el-table-column>
            <el-table-column
              prop="roadbedBaseType"
              label="结构类型"
              min-width="120"
              align="center"
            >
            </el-table-column>
            <el-table-column
              prop="roadbedBaseWidth"
              label="宽度(m)"
              min-width="120"
              align="center"
            >
            </el-table-column>
          </el-table-column>
        </el-table-column>


        <el-table-column
          label="路基段-面层"
          align="center"
        >
          <el-table-column
            prop="roadbedWidth"
            label="宽度(m"
            min-width="120"
            align="center"
          >
          </el-table-column>

          <el-table-column
            label="下面层"
            align="center"
          >
            <el-table-column
              prop="roadbedBottomThickness"
              label="厚度(cm)"
              min-width="120"
              align="center"
            />
            <el-table-column
              prop="roadbedBottomType"
              label="结构类型"
              min-width="120"
              align="center"
            />
          </el-table-column>
          <el-table-column
            label="中面层"
            align="center"
          >
            <el-table-column
              prop="roadbedCenterThickness"
              label="厚度(cm)"
              min-width="120"
              align="center"
            />
            <el-table-column
              prop="roadbedCenterType"
              label="结构类型"
              min-width="120"
              align="center"
            />
          </el-table-column>
          <el-table-column
            label="上面层"
            align="center"
          >
            <el-table-column
              prop="roadbedTopThickness"
              label="厚度(cm)"
              min-width="120"
              align="center"
            />
            <el-table-column
              prop="roadbedTopType"
              label="结构类型"
              min-width="120"
              align="center"
            />
          </el-table-column>
        </el-table-column>

        <el-table-column
          label="桥梁段"
          align="center"
        >
          <el-table-column
            prop="bridgeWidth"
            label="宽度(m)"
            min-width="120"
            align="center"
          >
          </el-table-column>
          <el-table-column
            label="中面层"
            align="center"
          >
            <el-table-column
              prop="bridgeCenterThickness"
              label="厚度(cm)"
              min-width="120"
              align="center"
            />
            <el-table-column
              prop="bridgeCenterType"
              label="结构类型"
              min-width="120"
              align="center"
            />
          </el-table-column>
          <el-table-column
            label="上面层"
            align="center"
          >
            <el-table-column
              prop="bridgeTopThickness"
              label="厚度(cm)"
              min-width="120"
              align="center"
            />
            <el-table-column
              prop="bridgeTopType"
              label="结构类型"
              min-width="120"
              align="center"
            />
          </el-table-column>
        </el-table-column>

        <el-table-column
          label="隧道段-面层"
          align="center"
        >
          <el-table-column
            prop="tunnelWidth"
            label="宽度(m)"
            min-width="120"
            align="center"
          >
          </el-table-column>

          <el-table-column
            label="下面层"
            align="center"
          >
            <el-table-column
              prop="tunnelBottomThickness"
              label="厚度(cm)"
              min-width="120"
              align="center"
            />
            <el-table-column
              prop="tunnelBottomType"
              label="结构类型"
              min-width="120"
              align="center"
            />
          </el-table-column>
          <el-table-column
            label="中面层"
            align="center"
          >
            <el-table-column
              prop="tunnelCenterThickness"
              label="厚度(cm)"
              min-width="120"
              align="center"
            />
            <el-table-column
              prop="tunnelCenterType"
              label="结构类型"
              min-width="120"
              align="center"
            />
          </el-table-column>
          <el-table-column
            label="上面层"
            align="center"
          >
            <el-table-column
              prop="tunnelTopThickness"
              label="厚度(cm)"
              min-width="120"
              align="center"
            />
            <el-table-column
              prop="tunnelTopType"
              label="结构类型"
              min-width="120"
              align="center"
            />
          </el-table-column>
        </el-table-column>
        <el-table-column
          label="加宽(m)"
          align="center"
          prop="increaseWidth"
          min-width="120"
          show-overflow-tooltip
        />

      </el-table>
      <pagination
        :total="total"
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
        :pageSizes="[10, 20, 30, 50, 100, 1000]"
        @pagination="getList"
      />
    </template>
    <Form
      v-if="showAddEdit"
      :forView="forView"
      :formData="formData"
      :title="title"
      :showAddEdit="showAddEdit"
      @close="() => {showAddEdit = false;formData ={}}"
      @refresh="() => {showAddEdit = false;formData ={};getList()}"
    />
    <Protections
      v-if="showDetail"
      :showDetail="showDetail"
      :constructionId="constructionId"
      :formData="formData"
      @close="() => {showDetail = false;formData ={}}"
    />
    <ImportData
      v-if="showImport"
      :is-update="isUpdate"
      :dialog-visible="showImport"
      :import-base-type="'23'"
      :import-type="importType"
      @close="closeImport"
    />
  </PageContainer>
</template>

<script>
import {
  getListPage,
  getConstruction,
  delConstruction
} from '@/api/baseData/roadbed/construction/index.js'
import Form from './form.vue'
import CascadeSelection from '@/components/CascadeSelection/index.vue'
import rangeInput from '@/views/baseData/components/rangeInput/index.vue'
import ImportData from '@/views/baseData/components/importData/index.vue'
import { statusDialog } from '@/views/baseData/components/statusDialog/index.js'
import { statusListDialog } from '@/views/baseData/components/statusDialog/list.js'

export default {
  name: 'Construction',
  components: {
    Form,
    CascadeSelection,
    rangeInput,
    ImportData
  },
  dicts: ['sys_route_type', 'sys_operation_state','sys_route_direction','sys_route_grade','base_data_state','sys_surface_type'],
  data() {
    return {
      loading: true,
      showAddEdit: false,
      forView: false,
      title: '',
      formData: {},
      ids: [],
      total: 0,
      tableData: [],
      queryParams: {
        pageNum: 1,
        pageSize: 20
      },
      showDetail: false,
      constructionId: '',
      showImport: false,
      isUpdate: false,
      importType: 1

    }
  },
  watch: {},
  created() {
    this.getList()
  },
  methods: {
    // 获取表格数据
    getList() {
      this.loading = true
      getListPage(this.queryParams)
        .then(response => {
          this.tableData = response.rows
          this.total = response.total
          this.loading = false
        })
        .catch(() => {
          this.loading = false
        })
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
    },
    // 表格点击勾选
    handleRowClick(row) {
      row.isSelected = !row.isSelected
      this.$refs.table.toggleRowSelection(row)
    },
    // 勾选高亮
    rowStyle({ row, rowIndex }) {
      if (this.ids.includes(row.id)) {
        return { 'background-color': '#b7daff', color: '#333' }
      } else {
        return { 'background-color': '#fff', color: '#333' }
      }
    },
    // 搜索按钮
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    // 重置按钮
    resetQuery() {
      this.queryParams = {
        pageNum: 1,
        pageSize: 20
      }
      this.handleQuery()
    },
    // 新增按钮操作
    handleAdd() {
      this.forView = false
      this.showAddEdit = true
      this.formData = {}
      this.title = '新增路面结构数据'
    },
    // 编辑按钮
    handleUpdate() {
      if (this.ids.length != 1) {
        this.$message.warning('请选择一条数据进行编辑！')
        return
      } else {
        getConstruction(this.ids[0]).then(res => {
          if (res.code === 200) {
            this.formData = res.data
            this.forView = false
            this.showAddEdit = true
            this.title = '编辑路面结构数据'
          }
        })
      }
    },
    // 导入更新按钮
    importUpdate() {
      this.isUpdate = true
      this.showImport = true
      this.importType = 1
    },
    // 导入新增按钮
    importAdd() {
      this.isUpdate = false
      this.showImport = true
      this.importType = 2
    },
    closeImport(v) {
      this.showImport = false
      if (v) this.getList()
    },
    // 删除按钮
    handleDelete() {
      if (this.ids.length == 0) {
        this.$message.warning('请选择至少一条数据进行删除！')
        return
      }
      this.$modal
        .confirm('确认删除？')
        .then(() => {
          delConstruction(this.ids).then(res => {
            if (res && res.code == '200') {
              this.getList()
              this.$modal.msgSuccess('删除成功')
            }
          })
        })
        .catch(() => {})
    },
    // 查看按钮
    handleView() {
      if (this.ids.length != 1) {
        this.$message.warning('请选择一条数据！')
        return
      } else {
        getConstruction(this.ids[0]).then(res => {
          if (res.code === 200) {
            this.formData = res.data
            this.forView = true
            this.showAddEdit = true
            this.title = '查看路面结构数据'
          }
        })
      }
    },
    // 导出按钮
    exportList() {
      if (this.ids.length === 0) {
        this.$modal
          .confirm('即将导出所有表格数据，此过程可能花费时间较长，是否继续？')
          .then(() => {
            this.download(
              '/baseData/pavement/construction/export',
              this.queryParams,
              `Construction${new Date().getTime()}.xlsx`,
              {
                headers: { 'Content-Type': 'application/json;' },
                parameterType: 'body'
              }
            )
          })
          .catch(() => {})
      } else {
        this.$modal
          .confirm(`已选择${this.ids.length}条路面结构数据，确认导出？`)
          .then(() => {
            this.download(
              '/baseData/pavement/construction/export',
              { ids: this.ids },
              `Construction${new Date().getTime()}.xlsx`,
              {
                headers: { 'Content-Type': 'application/json;' },
                parameterType: 'body'
              }
            )
          })
          .catch(() => {})
      }
    },
    // 导入按钮
    handleImport() {
      this.showImport = true
    },
    closeImport(v) {
      this.showImport = false
      if (v) this.getList()
    },
    // 二维码下载
    // downloadQrcode() {
    //   if (this.ids.length === 0) {
    //     this.$modal
    //       .confirm(
    //         '即将下载所有表格的二维码数据，此过程可能花费时间较长，是否继续？'
    //       )
    //       .then(() => {
    //         this.download(
    //           '/baseData/sideconstruction/basic/genQrCode',
    //           this.queryParams,
    //           `QrCode_${new Date().getTime()}`,
    //           {
    //             headers: { 'Content-Type': 'application/json;' },
    //             parameterType: 'body'
    //           }
    //         )
    //       })
    //       .catch(() => {})
    //   } else {
    //     this.$modal
    //       .confirm(`已选择${this.ids.length}条路面结构数据，是否下载二维码？`)
    //       .then(() => {
    //         let data = {
    //           ...this.queryParams,
    //           ids: this.ids
    //         }
    //         this.download(
    //           '/baseData/sideconstruction/basic/genQrCode',
    //           data,
    //           `QrCode_${new Date().getTime()}`,
    //           {
    //             headers: { 'Content-Type': 'application/json;' },
    //             parameterType: 'body'
    //           }
    //         )
    //       })
    //       .catch(() => {})
    //   }
    // },
    // 运营状态变更按钮
    changeStatus() {
      if (this.ids.length !== 1) {
        this.$message.warning('请选择一条数据！')
        return
      } else {
        // baseDataType 基础数据类型 ？路面结构
        statusDialog({ dataId: this.ids[0], baseDataType: 23 }).then(() => {
          this.getList()
        })
      }
    },
    // 表格操作-运营状态
    handleOperational(event, row) {
      event.stopPropagation()
      statusListDialog({ dataId: row.id, baseDataType: 23 })
    },
    // 防护形式详情
    handleDetail(row) {
      this.showDetail = true
      this.constructionId = row.id
      this.formData = row
    }
  }
}
</script>

<style lang="scss" scoped>

::v-deep .el-table .el-table__fixed-body-wrapper{
    top: 108px !important;
}
.button-list {
  border-radius: 4px;
  width: 100%;
  .el-button {
    margin-bottom: 10px;
    margin-right: 10px;
    margin-left: 0;
  }
}
</style>
