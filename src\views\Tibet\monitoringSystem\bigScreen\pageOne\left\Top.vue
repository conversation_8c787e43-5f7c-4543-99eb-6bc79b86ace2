<template>
  <Cards :title="title" w="100%" h="20vh" :isDtl="false">
    <div class="box" v-loading="loading" element-loading-spinner="el-icon-loading"
      element-loading-background="rgba(0, 0, 0, 0.3)">
      <el-row :gutter="20">
        <el-col :span="item.span" :offset="0" v-for="(item, index) in commonLabels" :key="index" class="set-margin">
          <div class="list-item">
            <i class="icon-right"></i>
            <span>{{ item.label }}：</span>
            <span style="max-width: 55%; white-space: nowrap; overflow: hidden; text-overflow: ellipsis;"
              :title="commonData[item.prop]">{{ commonData[item.prop] || '暂无' }}</span>
          </div>
        </el-col>
      </el-row>
    </div>
  </Cards>
</template>

<script>
import Cards from "../../components/cards.vue"
import { fetchGet } from '../../../utils/api.js'
import { listStatic as bridgeListStatic } from '@/api/baseData/bridge/baseInfo/index'
import { listStatic as tunnelListStatic } from '@/api/baseData/tunnel/baseInfo/index'
import { getListPage as sideSlopeListStatic } from '@/api/baseData/subgrade/sideSlope/index'
import { formatPile } from "@/utils/ruoyi";


export default {
  name: 'Top',
  inject: ['iThis'],
  props: {
    title: {
      type: String,
      default: '结构物基础信息'
    }
  },
  components: { Cards },
  data() {
    return {
      loading: false,
      commonLabels: [],
      commonData: {},
      bridge: [
        {
          label: '结构物',
          prop: 'bridgeName',
          span: 12
        },
        {
          label: '桥长(m)',
          prop: 'totalLength',
          span: 12
        },
        {
          label: '中心桩号',
          prop: 'centerStake',
          span: 12
        },
        {
          label: '最近技术状况',
          prop: 'bridgeTechAssessTypeName',
          span: 12
        },
        // {
        //   label: '桥面净宽(m)',
        //   prop: 'deckWidth',
        //   span: 12
        // },
        {
          label: '跨径分类',
          prop: 'spanClassifyTypeName',
          span: 12
        },
        {
          label: '主桥上部结构类型',
          prop: 'mainSuperstructureTypeName',
          span: 12
        },
        // {
        //   label: '是否启用应急管理',
        //   prop: 'if',
        //   span: 12
        // },
        {
          label: '修建年度',
          prop: 'constructionYear',
          span: 12
        },
        {
          label: '管养单位',
          prop: 'glcDomainName',
          span: 12
        },
        {
          label: '跨径组合',
          prop: 'spanGroups',
          span: 12
        },
      ],
      solop: [
        {
          label: '结构物',
          prop: 'structureName',
          span: 24
        },
        {
          label: '长度',
          prop: 'bridgeLength',
          span: 12
        },
        {
          label: '边坡总台数',
          prop: 'slopePlatformNumber',
          span: 12
        },
        {
          label: '主桥上部结构类型',
          prop: 'ustructType',
          span: 12
        },
        {
          label: '管养单位',
          prop: 'glcDomainName',
          span: 12
        },
      ],
      tunnel: [
        {
          label: '结构物',
          prop: 'structureName',
          span: 12
        },
        {
          label: '孔跨',
          prop: 'totalLength',
          span: 12
        },
        {
          label: '中心桩号',
          prop: 'centerStake',
          span: 12
        },
        {
          label: '隧道全长(m)',
          prop: 'tunnelLength',
          span: 12
        },
        {
          label: '隧道净高(m)',
          prop: 'tunnelHeight',
          span: 12
        },
        {
          label: '隧道净宽(m)',
          prop: 'tunnelWidth',
          span: 12
        },
        {
          label: '拱腰半径(m)',
          prop: 'archRadius',
          span: 12
        },
        {
          label: '衬砌类型',
          prop: 'liningType',
          span: 12
        },
        // {
        //   label: '进洞门类型',
        //   prop: 'tunnelDoorTypeIn',
        //   span: 12
        // },
        // {
        //   label: '隧道孔类型',
        //   prop: 'holeTypeCode',
        //   span: 12
        // },
        {
          label: '隧道种类',
          prop: 'structureType',
          span: 12
        },
        {
          label: '管养单位',
          prop: 'glcDomainName',
          span: 12
        },
        {
          label: '修建年度',
          prop: 'buildDate',
          span: 12
        },
        {
          label: '建成通车日期',
          prop: 'operationDate',
          span: 12
        },
      ],
      params: {},
    }
  },
  created() {
    this.params = this.iThis.params;
    this.commonLabels = this.params.type === '桥梁' ? this.bridge : this.params.type === '隧道' ? this.tunnel : this.solop;
    this.__init(); // 获取数据
  },
  methods: {
    // 获取数据
    __init() {
      const api = this.params.type === '桥梁' ? bridgeListStatic : this.params.type === '隧道' ? tunnelListStatic : sideSlopeListStatic
      api({
        assetId: this.params.asset_id,
        pageNum: 1,
        pageSize: 99999
      }).then(res => {
        if (res.code === 200) {
          this.commonData = {
            ...this.iThis.structureData,
            ...res.rows[0],
          };
          console.log(formatPile(this.commonData.centerStake))
          this.commonData.centerStake = this.commonData.centerStake ? formatPile(this.commonData.centerStake) : '暂无';
        }
      }).finally(() => {
        this.loading = false;
      })
    },
  },
  computed: {
  },
  watch: {},
}
</script>

<style lang="scss" scoped>
@import "@/assets/styles/utils.scss";

::-webkit-scrollbar {
  width: vwpx(14px);
  height: vwpx(14px);
}

::-webkit-scrollbar-thumb {
  background-color: rgba(1, 102, 254, 0.4);
}

::-webkit-scrollbar-track {
  background-color: rgba(0, 35, 94, .6);
}

.box {
  height: 100%;
  width: 100%;
  color: #eeeeee;
  padding: vwpx(20px);
  overflow-y: auto;
  overflow-x: hidden;

  .set-margin {
    margin-bottom: vwpx(20px);
    padding-left: vwpx(20px) !important;
    padding-right: vwpx(20px) !important;
  }

  .list-item {
    display: flex;
    align-items: center;

    .icon-right {
      background-image: url('~@/assets/monitoringSystem/right-icon.png');
      background-size: 100% 100%;
      width: vwpx(34px);
      height: vwpx(34px);
      display: inline-block;
    }

    span {
      font-weight: 400;
      font-size: vwpx(30px);
      color: #FFFFFF;
    }
  }
}
</style>