<template>
  <div class="app-container">
    <el-row :gutter="20">
      <!-- 筛选区开始 -->
      <el-col :span="24" :xs="24">
        <el-row>
          <el-col :span="24">
            <el-form ref="queryForm" :inline="true" :model="queryParams" label-width="68px" size="small">
              <el-form-item label="" prop="year">
                <el-date-picker
                  style="width: 240px"
                  v-model="queryParams.year"
                  type="year"
                  value-format="yyyy"
                  placeholder="年份"
                >
                </el-date-picker>
              </el-form-item>
              <el-form-item>
                <el-button icon="el-icon-search" size="mini" type="primary" @click="handleQuery">搜索</el-button>
                <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
              </el-form-item>
            </el-form>
          </el-col>
        </el-row>
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button
              icon="el-icon-download"
              size="mini"
              type="success"
              @click="exportToExcel"
            >导出
            </el-button>
          </el-col>
        </el-row>
        <!-- 筛选区结束 -->
        <!-- 数据表格开始 -->
        <div class="tableDiv">
          <el-table v-adjust-table v-loading="loading" :data="dataList" ref="tableRef" id='outTable'
                    :height="'calc(100vh - 260px)'" border size="mini"
                    style="width: 100%">
            <el-table-column
              type="selection"
              width="50">
            </el-table-column>
            <el-table-column align="center" width="150" label="管理处" prop="domainName"></el-table-column>
            <el-table-column align="center" width="150" label="巡查里程(KM)" prop="distance"></el-table-column>
            <el-table-column align="center" label="日常养护" prop="field">
              <el-table-column align="center" label="病害上报" prop="reportCountRY"></el-table-column>
              <el-table-column align="center" label="养护事件" prop="eventCountRY"></el-table-column>
              <el-table-column align="center" label="通知单接收" prop="receiveCountRY"></el-table-column>
              <el-table-column align="center" label="通知单下发" prop="issueCountRY"></el-table-column>
              <el-table-column align="center" label="待施工" prop="toBeConstructedRY"></el-table-column>
              <el-table-column align="center" label="施工中" prop="underConstructionRY"></el-table-column>
              <el-table-column align="center" label="已完工" prop="completedCountRY"></el-table-column>
              <el-table-column align="center" label="完工率" prop="completedRateRY">
                <template slot-scope="scope">
                  <div>{{(scope.row.completedRateRY * 100).toFixed(2) + '%'}}</div>
                </template>
              </el-table-column>
              <el-table-column align="center" label="加入中间单" prop="subCalcCountRY"></el-table-column>
              <el-table-column align="center" label="加入结算单" prop="calcCountRY"></el-table-column>
            </el-table-column>

            <el-table-column align="center" label="被损被盗" prop="field">
              <el-table-column align="center" label="立项" prop="itemCountBS"></el-table-column>
              <el-table-column align="center" label="通知单接收" prop="receiveCountBS"></el-table-column>
              <el-table-column align="center" label="通知单下发" prop="issueCountBS"></el-table-column>
              <el-table-column align="center" label="待施工" prop="toBeConstructedBS"></el-table-column>
              <el-table-column align="center" label="施工中" prop="underConstructionBS"></el-table-column>
              <el-table-column align="center" label="已完工" prop="completedCountBS"></el-table-column>
              <el-table-column align="center" label="完工率" prop="completedRateBS">
                <template slot-scope="scope">
                  <div>{{(scope.row.completedRateBS * 100).toFixed(2) + '%'}}</div>
                </template>
              </el-table-column>
              <el-table-column align="center" label="加入中间单" prop="subCalcCountBS"></el-table-column>
              <el-table-column align="center" label="加入结算单" prop="calcCountBS"></el-table-column>
            </el-table-column>

            <el-table-column align="center" label="养护工程" prop="field">
              <el-table-column align="center" label="立项" prop="itemCountYhgc"></el-table-column>
              <el-table-column align="center" label="通知单接收" prop="receiveCountYhgc"></el-table-column>
              <el-table-column align="center" label="通知单下发" prop="issueCountYhgc"></el-table-column>
              <el-table-column align="center" label="待施工" prop="toBeConstructedYhgc"></el-table-column>
              <el-table-column align="center" label="施工中" prop="underConstructionYhgc"></el-table-column>
              <el-table-column align="center" label="已完工" prop="completedCountYhgc"></el-table-column>
              <el-table-column align="center" label="完工率" prop="completedRateYhgc">
                <template slot-scope="scope">
                  <div>{{(scope.row.completedRateYhgc * 100).toFixed(2) + '%'}}</div>
                </template>
              </el-table-column>
              <el-table-column align="center" label="加入中间单" prop="subCalcCountYhgc"></el-table-column>
              <el-table-column align="center" label="加入结算单" prop="calcCountYhgc"></el-table-column>
            </el-table-column>

            <el-table-column align="center" label="定期检测" prop="field">
              <el-table-column align="center" label="立项" prop="itemCountDJ"></el-table-column>
              <el-table-column align="center" label="通知单下发" prop="issueCountDJ"></el-table-column>
              <el-table-column align="center" label="待施工" prop="toBeConstructedDJ"></el-table-column>
              <el-table-column align="center" label="施工中" prop="underConstructionDJ"></el-table-column>
              <el-table-column align="center" label="已完工" prop="completedCountDJ"></el-table-column>
              <el-table-column align="center" label="完工率" prop="completedRateDJ">
                <template slot-scope="scope">
                  <div>{{(scope.row.completedRateDJ * 100).toFixed(2) + '%'}}</div>
                </template>
              </el-table-column>
              <el-table-column align="center" label="加入中间单" prop="subCalcCountDJ"></el-table-column>
              <el-table-column align="center" label="加入结算单" prop="calcCountDJ"></el-table-column>
            </el-table-column>

          </el-table>
        </div>
        <!-- 数据表格结束 -->
      </el-col>
    </el-row>
  </div>
</template>

<script>
import RoadSection from "@/views/baseData/components/roadSection/index.vue";
import SelectTree from "@/components/DeptTmpl/selectTree.vue";
import {getElectricityFeeStatistics} from "@/api/calculate/operationManageFee/enterFee";
import {getNumbersByYear} from "@/api/dailyMaintenance/metering/settlementApplication";
import FileSaver from 'file-saver'
import * as XLSX from 'xlsx'
import {getDiseaseEventList} from "@/api/calculate/report/comprehensive";
import moment from "moment";
export default {
  name: "SystemUsage",
  components: {SelectTree, RoadSection},
  data() {
    return {
      // 遮罩层
      loading: false,
      total: 0,
      queryParams: {
        year: moment().format("YYYY"),
      },
      dataList: [],
      numbers: []
    };
  },
  created() {
    this.handleQuery()
  },
  methods: {
    handleQuery() {
      this.loading = true
      getDiseaseEventList(this.queryParams).then(res => {
        this.dataList = res.data
      }).finally(() => {
        this.loading = false
      })
    },
    //导出Excel
    exportToExcel() {
      /* generate workbook object from table */
      var wb = XLSX.utils.table_to_book(document.querySelector('#outTable'))
      /* get binary string as output */
      var wbout = XLSX.write(wb, { bookType: 'xlsx', bookSST: true, type: 'array' })
      try {
        FileSaver.saveAs(new Blob([wbout], { type: 'application/octet-stream' }), '系统使用情况.xlsx')
      } catch (e) { if (typeof console !== 'undefined') console.log(e, wbout) }
      return wbout
    },
    resetQuery() {
      this.queryParams = {}
    }
  }
};
</script>

<style scoped>
.tableDiv {
  margin-top: 20px;
}
</style>
<style lang="scss" scoped>
@import "@/assets/styles/business.scss";
</style>
