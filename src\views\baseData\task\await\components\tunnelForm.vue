<template>
    <div>
      
        <el-tabs
          v-model="activeName"
          type="card"
        >
          <el-tab-pane
            label="基础信息"
            name="baseInfo"
          >
            <div style="height: 100%;overflow-y: auto;padding: 0 10px 0 5px;">
              <el-form
              ref="baseInfo"
              :model="form"
              label-width="160px"
              :disabled="true"
            >
              <div style="display: flex; flex-wrap: wrap">
                <ManageSelectTree placeholder="请选择" :formObject="form" />
                <el-col
                  :span="item.span ? item.span : 12"
                  v-for="(item, index) in baseInfoFields"
                  :key="index"
                  v-if="!item.forView || forView"
                >
                  <el-form-item
                    :label="item.label"
                    :prop="item.prop"
                    
                  >
                    <span v-if="item.type === 'input'">
                      <el-input
                        v-model="form[item.prop]"
                        :placeholder="item.placeholder"
                        clearable
                      />
                    </span>
                    <span v-if="item.type === 'pileInput'">
                      <PileInput
                        v-model="form[item.prop]"
                        :placeholder="item.placeholder"
                      />
                    </span>
                    <span v-else-if="item.type === 'inputNumber'">
                      <el-input-number
                        style="width: 100%"
                        v-model="form[item.prop]"
                        :precision="item.precision"
                        clearable
                      ></el-input-number>
                    </span>
                    <span v-else-if="item.type === 'select'">
                      <el-select
                        v-model="form[item.prop]"
                        style="width: 100%"
                        :placeholder="item.placeholder"
                        clearable
                        filterable
                        :disabled="!form[item.disabledFieds]"
                        @change="
                          (val) => {
                            handleSelect(val, item);
                          }
                        "
                      >
                        <el-option
                          v-for="v in item.options"
                          :key="v[item.optionValue]"
                          :label="v[item.optionLabel]"
                          :value="v[item.optionValue]"
                        />
                      </el-select>
                    </span>

                    <span v-else-if="item.type === 'CascaderRegion'">
                      <CascaderRegion
                        v-model="form[item.prop]"
                        :deep="1"
                        @input="
                          (value) => {
                            form[item.prop] = value;
                          }
                        "
                      />
                    </span>
                    <span v-else-if="item.type === 'selectTree'">
                      <SelectTree
                        v-model="form[item.prop]"
                        :dept-type="item.deptType"
                        placeholder="请选择"
                      />
                    </span>
                    <span v-else-if="item.type === 'dictSelect'">
                      <el-select
                        v-model="form[item.prop]"
                        style="width: 100%"
                        placeholder="请选择"
                        :multiple="item.multiple"
                        clearable
                        :disabled="item.disabledFieds"
                      >
                        <el-option
                          v-for="i in dict.type[item.dict]"
                          :key="i.value"
                          :label="i.label"
                          :value="i.value"
                        />
                      </el-select>
                    </span>

                    <span v-else-if="item.type === 'multiDictSelect'">
                      <MultiDictSelect
                        v-model="form[item.prop]"
                        :disabled="item.disabledFieds"
                        :multiple="item.multiple"
                        :options="dict.type[item.dict]"/>
                    </span>
                    <div v-else-if="item.type == 'uploadImg'">
                      <ImageUpload
                        :key="item.ownerId"
                        v-model="form[item.prop]"
                        :limit="1"
                        :owner-id="item.ownerId"
                        storage-path="/base/tunnel/baseInfo"
                      />
                    </div>
                    <span v-else-if="item.type === 'date'">
                      <el-date-picker
                        v-model="form[item.prop]"
                        style="width: 100%"
                        type="date"
                        :placeholder="item.placeholder"
                        clearable
                        value-format="yyyy-MM-dd"
                      />
                    </span>
                    <span v-else-if="item.type === 'year'">
                      <el-date-picker
                        v-model="form[item.prop]"
                        style="width: 100%"
                        type="year"
                        :placeholder="item.placeholder"
                        clearable
                        value-format="yyyy"
                      />
                    </span>
                    <span v-else-if="item.type === 'tree'">
                      <select-tree
                        style="width: 100%"
                        v-model="form[item.prop]"
                        clearable
                      />
                    </span>
                    <span v-else-if="item.type === 'roadType'">
                      <SectionSelect
                        :style="forView ? 'pointer-events: none' : ''"
                        v-model="form[item.prop]"
                        :formObject="form"
                        :sectionId="form.maintenanceSectionId"
                        :disabled="!form.maintenanceSectionId"
                        clearable
                      />
                    </span>
                    <span v-else-if="item.type === 'coordinate'">
                      <lon-lat
                        :type="item.prepend"
                        :lon.sync="form[item.propLon]"
                        :lat.sync="form[item.propLat]"
                      />
                    </span>
                  </el-form-item>
                </el-col>
              </div>
            </el-form>
          </div>
        </el-tab-pane>
        <el-tab-pane label="结构技术数据" name="technology">
          <div style="height: 100%; overflow-y: auto; padding: 0 10px 0 5px">
            <el-form
              ref="technology"
              :model="form"
              label-width="190px"
              :disabled="true"
            >
              <div style="display: flex; flex-wrap: wrap">
                <el-col
                  :span="12"
                  v-for="(item, index) in technologyFields"
                  :key="index"
                  v-if="!item.forView || forView"
                >
                  <el-form-item
                    :label="item.label"
                    :prop="item.prop"
                    
                  >
                    <span v-if="item.type === 'dictSelect'">
                      <el-select
                        v-model="form[item.prop]"
                        style="width: 100%"
                        placeholder="请选择"
                        clearable
                      >
                        <el-option
                          v-for="i in dict.type[item.dict]"
                          :key="i.value"
                          :label="i.label"
                          :value="i.value"
                        />
                      </el-select>
                    </span>
                    <span v-else-if="item.type === 'inputNumber'">
                      <el-input-number
                        style="width: 100%"
                        v-model="form[item.prop]"
                        :precision="item.precision"
                        :min="item.min"
                        :max="item.max"
                        clearable
                      ></el-input-number>
                    </span>
                    <span v-else-if="item.type === 'input'">
                      <el-input
                        v-model="form[item.prop]"
                        :placeholder="item.placeholder"
                      />
                    </span>
                  </el-form-item>
                </el-col>
              </div>
            </el-form>
          </div>
        </el-tab-pane>
        <el-tab-pane label="隧道管理信息" name="information">
          <div style="height: 100%; overflow-y: auto; padding: 0 10px 0 5px">
            <el-form
              ref="information"
              :model="form"
              label-width="160px"
              :disabled="true"
            >
              <div style="display: flex; flex-wrap: wrap">
                <el-col
                  :span="item.span ? item.span : 12"
                  v-for="(item, index) in informationFields"
                  :key="index"
                  v-if="!item.forView || forView"
                >
                  <el-form-item
                    :label="item.label"
                    :prop="item.prop"
                    
                  >
                    <span v-if="item.type === 'input'">
                      <el-input
                        v-model="form[item.prop]"
                        :placeholder="item.placeholder"
                      />
                    </span>

                    <span v-else-if="item.type === 'inputTextarea'">
                      <el-input
                        v-model="form[item.prop]"
                        autosize
                        :placeholder="item.placeholder"
                        type="textarea"
                        clearable
                      />
                    </span>
                  </el-form-item>
                </el-col>
              </div>
            </el-form>
          </div>
        </el-tab-pane>
        <el-tab-pane label="隧道档案" name="fileInfo">
          <div style="height: 100%; overflow-y: auto; padding: 0 10px 0 5px">
            <el-form
              ref="fileInfo"
              :model="form"
              label-width="160px"
              :disabled="true"
            >
              <div style="display: flex; flex-wrap: wrap">
                <el-col
                  :span="12"
                  v-for="(item, index) in fileInfoFields"
                  :key="index"
                >
                  <el-form-item :label="item.label" :prop="item.prop">
                    <span v-if="item.type === 'dict'">
                      <el-select
                        v-model="form[item.prop]"
                        style="width: 100%"
                        placeholder="请选择"
                        clearable
                      >
                        <el-option
                          v-for="i in dict.type[item.dict]"
                          :key="i.value"
                          :label="i.label"
                          :value="i.value"
                        />
                      </el-select>
                    </span>
                    <span v-else-if="item.type === 'input'">
                      <el-input
                        style="width: 100%"
                        v-model="form[item.prop]"
                        :placeholder="item.placeholder"
                        clearable
                      ></el-input>
                    </span>
                    <span v-else-if="item.type === 'date'">
                      <el-date-picker
                        v-model="form[item.prop]"
                        style="width: 100%"
                        type="date"
                        :placeholder="item.placeholder"
                        clearable
                        value-format="yyyy-MM-dd"
                      />
                    </span>
                  </el-form-item>
                </el-col>
              </div>
            </el-form>
            </div>
          </el-tab-pane>
        </el-tabs>
    </div>
  </template>
  
  <script>
  import formFields from "@/views/baseData/tunnel/baseInfo/js/formFields";
  import dicts from './tunneljs/dicts'
  import PileInput from '@/components/PileInput/index.vue'
  import SelectTree from '@/components/DeptTmpl/selectTree'
  import SectionSelect from '@/components/SectionSelect'
  import ManageSelectTree from "@/components/manageSelectTree/index.vue";
  import MultiDictSelect from "@/views/baseData/components/MultiDictSelect";
  import lonLat from "@/components/mapPosition/lonLat.vue";
  import { getListPage } from '@/api/baseData/tunnel/baseInfo/manageList.js'
  import { createIdWorker } from '@/api/baseData/common'
  import { listMaintenanceSectionAll } from '@/api/system/maintenanceSection'
  import { listByMaintenanceSectionId } from '@/api/baseData/common/routeLine'
  import CascaderRegion from "@/views/baseData/components/CascaderRegion/index.vue";
  import {
    getStatic,
  } from '@/api/baseData/tunnel/baseInfo/index'
  
  export default {
    name: 'tunnel-form',
    props: {
     
      componentId: { type: String, default: '' },
      forView: { type: Boolean, default: false }
    },
    components: { PileInput, SelectTree, SectionSelect,ManageSelectTree,MultiDictSelect,lonLat,CascaderRegion },
    dicts: dicts,
    data() {
      return {
        loading: false,
        activeName: 'baseInfo',
        form: {},
        baseInfoFields: [],
        technologyFields: [],
        informationFields: [],
        fileInfoFields: [],
        manageListData: []
      }
    },
    created() {
      this.init()
    },
    methods: {
      init() {
        this.baseInfoFields = JSON.parse(JSON.stringify(formFields.baseInfo))
        this.technologyFields = JSON.parse(JSON.stringify(formFields.technology))
        this.informationFields = JSON.parse(
          JSON.stringify(formFields.information)
        )
        this.fileInfoFields = JSON.parse(JSON.stringify(formFields.fileInfo));
        getStatic(this.componentId).then(res => {
          if (res && res.data) {
            const data = JSON.parse(JSON.stringify(res.data))
            this.form = data
            }
        })

        if (!this.forView) {
          this.baseInfoFields.forEach(async el => {
            if (el.type === 'uploadImg') {
              try {
                let res = await createIdWorker()
                if (res.code === 200) el.ownerId = Number(res.data)
                this.$forceUpdate()
              } catch (error) {}
            }
          })
        }
      },
      handleSelect(e, i) {
        switch (i.prop) {
          case 'routeCode':
            if (e) {
              this.baseInfoFields.forEach(el => {
                if (el.prop === 'routeName') {
                  const option = el.options.find(i => i.routeCode === e)
                  if (option) {
                    this.form.routeId = option.routeId
                    this.form.routeName = option.routeName
                  }
                }
              })
            } else {
              this.form.routeId = ''
              this.form.routeName = ''
            }
            break
          case 'routeName':
            if (e) {
              this.baseInfoFields.forEach(el => {
                if (el.prop === 'routeCode') {
                  const option = el.options.find(i => i.routeName === e)
                  if (option) {
                    this.form.routeId = option.routeId
                    this.form.routeCode = option.routeCode
                  }
                }
              })
            } else {
              this.form.routeId = ''
              this.form.routeCode = ''
            }
            break
        }
      },
      // 监听选中管理处
      deptChange(e) {
        if (!e) return
        listMaintenanceSectionAll({ departmentId: e }).then(res => {
          if (res.code == 200) {
            this.baseInfoFields.forEach(el => {
              if (el.prop === 'maintenanceSectionId') {
                el.options = res.data
              }
            })
          }
        })
      },
      // 监听选中养护路段
      maintenanceSectionChange(e) {
        if (!e) return
        listByMaintenanceSectionId({ maintenanceSectionId: e }).then(res => {
          if (res.code == 200) {
            this.baseInfoFields.forEach(el => {
              if (el.prop === 'routeCode' || el.prop === 'routeName') {
                el.options = res.data
              }
            })
          }
        })
      },
      
      
    },
    computed: {},
    watch: {
      'form.managementMaintenanceId'(newVal, oldVal) {
        if (newVal) {
          this.deptChange(newVal)
        }
      },
      'form.maintenanceSectionId'(newVal, oldVal) {
        if (newVal) {
          this.maintenanceSectionChange(newVal)
        }
        if (!newVal&&this.form.routeCode) {
          this.form.routeCode = ''
          this.form.routeName = ''
        }
      }
    }
  }
  </script>
  
  <style lang="scss" scoped>
  @import '~@/assets/styles/el-tabs.scss';
  .forView ::v-deep .el-input.is-disabled .el-input__inner {
    background-color: white;
    border-color: #dfe4ed;
    color: #1d2129;
  }
  ::v-deep .el-dialog__header {
    border-bottom: 0;
    padding: 20px 30px 0 30px !important;
  }

  ::v-deep .el-input.is-disabled .el-input__inner{
    color: #000;
  }
  </style>
  