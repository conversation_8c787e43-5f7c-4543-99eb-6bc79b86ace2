// 卡片-行政识别数据
const administrativeCardData = [
  {
    label: '路线编码',
    prop: 'routeCode',
    cardType: '',
    index: 1
  },
  {
    label: '路线名称',
    prop: 'routeName',
    cardType: '',
    index: 2
  },
  {
    label:'路线等级',
    prop: 'routeLevel',
    cardType: 'select',
    dict: 'sys_route_grade',
    index: 3
  },
  {
    label: '桥梁编码',
    prop: 'bridgeCode',
    cardType: 'input',
    index: 4
  },
  {
    label: '桥梁名称',
    prop: 'bridgeName',
    cardType: 'input',
    index: 5
  },
  {
    label: '桥位桩号',
    prop: 'centerStake',
    cardType: 'pileInput',
    precision: 3,
    index: 6
  },
  {
    label: '功能类型',
    prop: 'functionType',
    cardType: 'select',
    dict: 'bridge_function_type',
    index: 7
  },
  {
    label: '被跨越道路(通道)名称',
    prop: 'underpassChannelName',
    cardType: 'input',
    index: 8
  },
  {
    label: '被跨越道路(通道)桩号',
    prop: 'underpassChannelunderStake',
    cardType: '',
    index: 9
  },
  {
    label: '设计荷载',
    prop: 'designLoadType',
    cardType: 'select',
    dict: 'sys_design_load',
    index: 10
  },
  {
    label: '桥面纵坡',
    prop: 'bridgeSlope',
    cardType: 'inputNumber',
    precision: 4,
    index: 11
  },
  {
    label: '桥梁平曲线半径',
    prop: 'planeCurveRadius',
    cardType: 'inputNumber',
    precision: 3,
    index: 12
  },
  {
    label: '建成年限',
    prop: 'buildDate',
    cardType: 'date',
    index: 13
  },
  {
    label: '设计单位',
    prop: 'designUnit',
    cardType: 'input',
    index: 14
  },
  {
    label: '施工单位',
    prop: 'constructionUnit',
    cardType: 'input',
    index: 15
  },
  {
    label: '监理单位',
    prop: 'supervisionUnit',
    cardType: 'input',
    index: 16
  },
  {
    label: '业主单位',
    prop: 'managementUnit',
    cardType: 'input',
    index: 17
  },
  {
    label: '管理处',
    prop: 'managementMaintenanceName',
    cardType: 'input',
    index: 18
  }
]
// 卡片-桥梁技术指标
const technologyCardData = [
  {
    column: 3,
    list: [
      {
        label: '桥梁全长(m)',
        prop: 'totalLength',
        cardType: 'inputNumber',
        precision: 2,
        index: 19
      },
      {
        label: '桥面总宽(m)',
        prop: 'deckTotalWidth',
        cardType: 'inputNumber',
        precision: 2,
        index: 20
      },
      {
        label: '车行道宽(m)',
        prop: 'laneWidth',
        cardType: 'inputNumber',
        precision: 2,
        index: 21
      },
      {
        label: '人行道宽度(m)',
        prop: 'sidewalkWidth',
        cardType: 'inputNumber',
        precision: 2,
        index: 22
      },
      {
        label: '护栏或防撞栏高度(m)',
        prop: 'guardrailHeight',
        cardType: 'inputNumber',
        precision: 2,
        index: 23
      },
      {
        label: '中央分隔带宽度(m)',
        prop: 'centralSeparatorWidth',
        cardType: 'inputNumber',
        precision: 2,
        index: 24
      },
      {
        label: '桥面标准净空(m)',
        prop: 'deckStandardClearance',
        cardType: 'inputNumber',
        precision: 2,
        index: 25
      },
      {
        label: '桥面实际净空(m)',
        prop: 'deckActualClearance',
        cardType: 'inputNumber',
        precision: 2,
        index: 26
      },
      {
        label: '桥下通航等级及标准净空(m)',
        prop: 'underStandardClearance',
        cardType: 'inputNumber',
        precision: 2,
        index: 27
      },
      {
        label: '桥下实际净空(m)',
        prop: 'underActualClearance',
        type: 'inputNumber',
        precision: 2,
        index: 28
      },
      {
        label: '引道总宽(m)',
        prop: 'totalChannelWidthEnd',
        cardType: 'inputNumber',
        precision: 2,
        index: 29
      },
      {
        label: '引道线形或曲线半径(m)',
        prop: 'channelAlignmentEndType',
        cardType: 'select',
        dict: 'bridge_approach_type',
        index: 30
      },
      {
        label: '设计洪水频率及其水位',
        prop: 'designFloodFrequencyType',
        cardType: 'select',
        dict: 'bridge_design_flood_frequency',
        index: 31
      },
      {
        label: '历史洪水位',
        prop: 'historicalFloodLevel',
        cardType: 'input',
        index: 32
      },
      {
        label: '地震动峰值加速度系数',
        prop: 'earthquakeAccelerationType',
        cardType: 'select',
        dict: 'bridge_earthquake_acceleration',
        index: 33
      }
    ]
  },
  {
    column: 1,
    list: [
      {
        label: '桥面高程(m)',
        prop: 'deckCenterHeight',
        cardType: 'add',
        index: 34
      }
    ]
  }
]

// 卡片-桥梁结构信息
const informationCardData = [
  {
    column: 1,
    cardType: 'add',
    prop: 'no',
    list: [
      {
        label: '桥梁分孔(m)',
        prop: 'spanGroups',
        cardType: 'add',
        index: 35
      }
    ]
  },
  {
    column: 1,
    cardType: 'add',
    prop: 'no',
    list: [
      {
        label: '结构体系',
        prop: 'structureSystem',
        cardType: 'add',
        index: 36
      }
    ]
  },
  {
    column: 3,
    cardType: 'tree',
    label: '上部结构形式与材料',
    prop: 'no',
    list: [
      {
        label: '主梁',
        prop: 'girder',
        cardType: 'input',
        index: 37,
        list: [
          {
            prop: 'no',
          }
        ]
      },
      {
        label: '主拱圈',
        prop: 'mainArchRing',
        cardType: 'input',
        index: 38,
        list: [
          {
            prop: 'no',
          }
        ]
      },
      {
        label: '桥(索)塔',
        prop: 'bridgeTower',
        cardType: 'input',
        index: 39,
        list: [
          {
            prop: 'no',
          }
        ]
      },
      {
        label: '拱上建筑',
        prop: 'archConstruction',
        cardType: 'input',
        index: 40,
        list: [
          {
            prop: 'no',
          }
        ]
      },
      {
        label: '主缆',
        prop: 'mainCable',
        cardType: 'input',
        index: 41,
        list: [
          {
            prop: 'no',
          }
        ]
      },
      {
        label: '斜拉索(含索力)',
        prop: 'stayCable',
        cardType: 'input',
        index: 42,
        list: [
          {
            prop: 'no',
          }
        ]
      },
      {
        label: '吊杆(含索力)',
        prop: 'derrick',
        cardType: 'input',
        index: 43,
        list: [
          {
            prop: 'no',
          }
        ]
      },
      {
        label: '系杆(含索力)',
        prop: 'tieBar',
        cardType: 'input',
        index: 44,
        list: [
          {
            prop: 'no',
          }
        ]
      }
    ]
  },
  {
    column: 3,
    cardType: 'tree',
    label: '桥面系形式与材料',
    prop: 'no',
    list: [
      {
        label: '桥面铺装',
        prop: 'deckPavementType',
        cardType: 'select',
        dict: 'bridge_deck_pavement_types',
        index: 45,
        list: [
          {
            prop: 'no',
          }
        ]
      },
      {
        label: '伸缩缝',
        prop: 'expansionJointType',
        cardType: 'select',
        dict: 'bridge_expansion_joint_types',
        index: 46,
        list: [
          {
            prop: 'no',
          }
        ]
      },
      {
        label: '人行道、路缘',
        prop: 'sidewalkCurb',
        cardType: 'input',
        index: 47,
        list: [
          {
            prop: 'no',
          }
        ]
      },
      {
        label: '栏杆、护栏',
        prop: 'railingGuardrail',
        cardType: 'input',
        index: 48,
        list: [
          {
            prop: 'no',
          }
        ]
      },
      {
        label: '照明、标志',
        prop: 'lightingSigns',
        cardType: 'input',
        index: 49,
        list: [
          {
            prop: 'no',
          }
        ]
      }
    ]
  },
  {
    column: 3,
    cardType: 'tree',
    label: '下部结构形式与材料',
    prop: 'no',
    list: [
      {
        label: '桥台',
        prop: 'abutmentType',
        cardType: 'select',
        dict: 'bridge_abutment_types',
        index: 50,
        list: [
          {
            prop: 'no',
          }
        ]
      },
      {
        label: '桥墩',
        prop: 'pierType',
        cardType: 'select',
        dict: 'bridge_pier_types',
        index: 51,
        list: [
          {
            prop: 'no',
          }
        ]
      },
      {
        label: '锥坡、护坡',
        prop: 'coneProtectionSlope',
        cardType: 'input',
        index: 52,
        list: [
          {
            prop: 'no',
          }
        ]
      },
      {
        label: '翼墙、耳墙',
        prop: 'wingEarWall',
        cardType: 'input',
        index: 53,
        list: [
          {
            prop: 'no',
          }
        ]
      }
    ]
  },
  {
    column: 3,
    cardType: 'tree',
    label: '基础形式与材料',
    prop: 'no',
    list: [
      {
        label: '基础',
        prop: 'baseType',
        cardType: 'select',
        dict: 'bridge_base_types',
        index: 54,
        list: [
          {
            prop: 'no',
          }
        ]
      },
      {
        label: '锚碇',
        prop: 'anchorage',
        cardType: 'input',
        index: 55,
        list: [
          {
            prop: 'no',
          }
        ]
      }
    ]
  },
  {
    column: 3,
    cardType: 'tree',
    label: '支座形式、材料与附属设施',
    prop: 'no',
    list: [
      {
        label: '支座',
        prop: 'bearingType',
        cardType: 'select',
        dict: 'bridge_bearing_types',
        index: 56,
        list: [
          {
            prop: 'no',
          }
        ]
      },
      {
        label: '桥梁防撞设施',
        prop: 'pierCollisionAvoidanceType',
        cardType: 'select',
        dict: 'bridge_pier_collision_avoidance',
        index: 57,
        list: [
          {
            prop: 'no',
          }
        ]
      },
      {
        label: '航标及排水系统',
        prop: 'aidsNavigationSewerageSystem',
        cardType: 'input',
        index: 58,
        list: [
          {
            prop: 'no',
          }
        ]
      },
      {
        label: '调治构造物',
        prop: 'regulatingStructure',
        cardType: 'input',
        index: 59,
        list: [
          {
            prop: 'no',
          }
        ]
      }
    ]
  }
]

const profileCardData=[
  {
    label: '设计图纸',
    prop: 'designDrawing',
    cardType: 'select',
    dict: 'base_archival_data',
    index: 60
  },
  {
    label: '设计文件',
    prop: 'designDocument',
    cardType: 'select',
    dict: 'base_archival_data',
    index: 61
  },
  {
    label: '竣工图纸',
    prop: 'completedDrawing',
    cardType: 'select',
    dict: 'base_archival_data',
    index: 62
  },
  {
    label: '施工文件(含施工缺陷处理)',
    prop: 'constructionDocument',
    cardType: 'select',
    dict: 'base_archival_data',
    index: 63
  },
  {
    label: '验收文件',
    prop: 'acceptanceDocument',
    cardType: 'select',
    dict: 'base_archival_data',
    index: 64
  },
  {
    label: '行政审批文件',
    prop: 'administrativeApprovalDocument',
    cardType: 'select',
    dict: 'base_archival_data',
    index: 65
  },
  {
    label: '定期检查资料',
    prop: 'periodicInspectionInformation',
    cardType: 'select',
    dict: 'base_archival_data',
    index: 66
  },
  {
    label: '特别检查资料',
    prop: 'specialInspectionInformation',
    cardType: 'select',
    dict: 'base_archival_data',
    index: 67
  },
  {
    label: '历次维修资料、加固资料',
    prop: 'previousMaintenanceInformation',
    cardType: 'select',
    dict: 'base_archival_data',
    index: 68
  },
  {
    label: '档案形式',
    prop: 'fileForm',
    cardType: 'select',
    dict: 'base_archival_data',
    index: 69
  },
  {
    label: '其他档案',
    prop: 'otherArchives',
    cardType: 'select',
    dict: 'base_archival_data',
    index: 70
  },
  {
    label: '建档时间',
    prop: 'filingTime',
    cardType: '',
    index: 71
  }
];

const imageCardData=[
  {
    label: '桥梁总体照片',
    prop: 'facadePhotoId',
    cardType: 'image',
    index: 89
  },
  {
    label: '桥梁正面照片',
    prop: 'frontPhotoId',
    cardType: 'image',
    index: 90
  },
]

const otherCardData = [
  {
    label: '桥梁工程师',
    prop: 'bridgeMaintenanceEngineer',
    cardType: 'input',
    index: 91
  },
  {
    label: '填卡人',
    prop: 'reportWriter',
    cardType: 'input',
    index: 92
  },

  {
    label: '填卡日期',
    prop: 'reportWriteDate',
    cardType: 'date',
    index: 93
  }
]



export default {
  administrativeCardData,
  technologyCardData,
  informationCardData,
  profileCardData,
  imageCardData,
  otherCardData
}
