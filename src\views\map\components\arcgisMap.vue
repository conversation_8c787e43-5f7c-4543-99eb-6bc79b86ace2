<template>
  <div class="arcgis-map" id="viewDiv"></div>
</template>

<script>
import { loadModules } from 'esri-loader';
const options = {
  url: 'https://js.arcgis.com/4.30/init.js',
  css: 'https://js.arcgis.com/4.30/esri/themes/dark/main.css',
};
var key = 'd5b63f3721794bb10ed0268465b16e1a';

export default {
  data() {
    return {

    }
  },
  mounted() {
    this.initMap();
  },
  unmounted() {
    window.mapLayer = null;
    window.mapView = null;
  },
  methods: {
    initMap() {
      loadModules([
        "esri/Map",
        "esri/views/MapView",
        "esri/layers/WebTileLayer",
        "esri/layers/FeatureLayer",
        "esri/layers/GraphicsLayer",
        "esri/geometry/geometryEngine",
        "esri/Graphic",
        "esri/geometry/Polygon",
        "esri/symbols/SimpleFillSymbol",
        "esri/symbols/SimpleLineSymbol"
      ], options)
        .then(([
          Map,
          MapView,
          WebTileLayer,
          FeatureLayer,
          GraphicsLayer,
          geometryEngine,
          Graphic,
          Polygon,
          SimpleFillSymbol,
          SimpleLineSymbol
        ]) => {

          // 影像图
          const imgLayer = new WebTileLayer({
            urlTemplate: `http://{subDomain}.tianditu.gov.cn/DataServer?T=img_w&x={x}&y={y}&l={z}&tk=${key}`,
            subDomains: ['t0', 't1', 't2', 't3', 't4', 't5', 't6', 't7'],
          });
          // 注记
          const ciaLayer = new WebTileLayer({
            urlTemplate: `http://{subDomain}.tianditu.com/DataServer?T=cia_w&x={x}&y={y}&l={z}&tk=${key}`,
            subDomains: ['t0', 't1', 't2', 't3', 't4', 't5', 't6', 't7'],
          });
          window.mapLayer = new Map({
            basemap: 'satellite',
          });
          window.mapLayer.addMany([imgLayer, ciaLayer]);
          // 创建MapView实例
          window.mapView = new MapView({
            container: 'viewDiv', // 视图的DOM容器
            map: window.mapLayer, // 关联Map实例
            center: [102, 25], // 初始中心点坐标
            zoom: 6, // 初始缩放级别
            constraints: {
              minZoom: 5,
              maxZoom: 18,
            },
          });
          window.mapView.ui.components = [];

          const polygon = new Polygon({
            rings: [
              [
                [-180, 89],
                [-180, -89],
                [180, -89],
                [180, 89],
                [-180, 89],
              ],
            ],
            spatialReference: { wkid: 4326 },
          });
          // let jsonPolygon = function () {
          //   fetch('https://geo.datav.aliyun.com/areas_v3/bound/530000_full.json', {
          //     method: 'GET',
          //     mode: 'cors'
          //   }).then(response => response.json()).then(res => {
          //     if(res) {
          //       let features = res.features
          //     }
          //   })
          // }
          // jsonPolygon()
          let maskLayer = new GraphicsLayer();
          window.mapLayer.add(maskLayer);
          // 创建遮罩图形
          const maskGraphic = new Graphic({
            geometry: polygon,
            symbol: new SimpleFillSymbol({
              color: [0, 0, 0, 0.5], // 遮罩颜色，半透明白色
              outline: new SimpleLineSymbol({
                color: [0, 0, 0, 0.5], // 轮廓线颜色，半透明黑色
                width: 2
              })
            })
          });

          // 添加到GraphicsLayer
          maskLayer.add(maskGraphic);
          // const wkt = geometryEngine.geometriesToWkt(['https://geo.datav.aliyun.com/areas_v3/bound/530000_full.json']);
          // const clippedGeometry = geometryEngine.difference(polygon, wkt);
          let JsonData = null;
          fetch('https://geo.datav.aliyun.com/areas_v3/bound/530000_full.json', {
            mode: 'cors',
          }).then(response => response.json())
            .then(res => {
              console.log('返回json数据', res.features)
              JsonData = res
            });
          const countries = new FeatureLayer({
            url: "https://services.arcgis.com/P3ePLMYs2RVChkJx/arcgis/rest/services/World_Countries_(Generalized)/FeatureServer/0"
          });
          const query = countries.createQuery();
          query.where = "ISO = 'IT'";
          query.outSpatialReference = { wkid: 4326 }
          console.log('数据', query)
          window.mapView.when(function () {
            countries.queryFeatures(query).then((result) => {
              console.log('结果', result.features)
              const resultPolys = result.features.map(function (item) {
                return item.geometry
              })
              const union = geometryEngine.union(resultPolys)

              const clippedGeometry = geometryEngine.difference(polygon, union);

              const fillSymbol = {
                type: "simple-fill", // 自动转换为新的SimpleFillSymbol
                color: [0, 0, 0, 0.7],
                outline: {
                  // 自动转换为新的simplelinesymsymbol ()
                  color: [255, 255, 255, 0.7],
                  width: 1
                }
              };
              // 将几何图形和符号添加到新图形中
              const polygonGraphic = new Graphic({
                geometry: clippedGeometry,
                symbol: fillSymbol
              });
              window.mapView.graphics.add(polygonGraphic);
              // 设置一个自动循环，在不同的几何形状之间切换。
              function next() {
                window.mapView.goTo(resultPolys).catch((error) => {
                  if (error.name !== "AbortError") {
                    console.error(error);
                  }
                });
              }
              // next();
            });
          });
        })
    },
    // 树选中 触发事件
    setMapShape(data) {
      console.log('地图页面', data)
    },
  }
}
</script>

<style lang="scss" scoped>
.arcgis-map {
  width: 100%;
  height: 100%;
}

.esri-view-surface {
  &:after {
    display: none;
  }
}

/* 取消地图聚焦边框效果 */
.esri-view {
  --esri-view-outline-color: rgba(0, 0, 0, 0) !important;
}
</style>