<template>
	<PageContainer :ifSearch="false" :ifHeader="false">
		<template slot="body">
			<el-tabs v-model="activeName" type="card" @tab-click="handleClick">
				<el-tab-pane label="预警级别设置" name="first"></el-tab-pane>
				<el-tab-pane label="短信模版设置" name="second"></el-tab-pane>
			</el-tabs>
			<div class="button-list">
				<el-button v-hasPermi="['system:weather:add']" type="primary" @click="handleAdd">
					{{ activeName === 'first' ? '新增预警级别' : '新增短信模版' }}
				</el-button>
				<el-button v-hasPermi="['system:weather:edit']" type="success" @click="handleEdit">
					修改
				</el-button>
				<el-button v-hasPermi="['system:weather:remove']" type="danger" @click="handleRemove">
					删除
				</el-button>
				<!-- <el-button v-hasPermi="['system:weather:export']" type="warning" @click="handleExport">
					导出
				</el-button> -->
			</div>
			<el-table
				v-adjust-table
				v-loading="loading"
				ref="table"
				height="100%"
				style="width: 100%"
				border
				:data="tableData"
				:row-style="rowStyle"
				@selection-change="handleSelectionChange"
				@row-click="handleRowClick"
			>
				<el-table-column type="selection" width="50" align="center" />
				<el-table-column label="序号" type="index" width="50" align="center" />
				<el-table-column
					v-if="activeName === 'first'"
					label="预警级别"
					align="center"
					prop="level"
					min-width="140"
				>
					<template slot-scope="scope">
						<dict-tag :options="dict.type.map_weather_warning_Level" :value="scope.row.level" />
					</template>
				</el-table-column>
				<el-table-column
					v-if="activeName === 'first'"
					label="气象类型"
					align="center"
					prop="type"
					min-width="140"
				>
					<template slot-scope="scope">
						<dict-tag :options="dict.type.map_weather_warning_type" :value="scope.row.type" />
					</template>
				</el-table-column>
				<el-table-column
					v-if="activeName === 'first'"
					label="预警角色"
					align="center"
					prop="roleIdsName"
					min-width="140"
					show-overflow-tooltip
				/>
				<el-table-column
					v-if="activeName === 'second'"
					key="name"
					label="短信模版类型"
					align="center"
					prop="name"
					min-width="140"
				/>
				<el-table-column
					v-if="activeName === 'second'"
					key="content"
					label="短信内容"
					align="center"
					prop="content"
					min-width="140"
					show-overflow-tooltip
				/>
				<el-table-column
					v-if="activeName === 'first'"
					label="状态"
					align="center"
					prop="status"
					min-width="140"
				>
					<template slot-scope="scope">
						<dict-tag
							:options="dict.type.map_weather_warning_level_status"
							:value="scope.row.status"
						/>
					</template>
				</el-table-column>
				<el-table-column
					label="创建时间"
					align="center"
					prop="createTime"
					min-width="140"
					show-overflow-tooltip
				>
					<template slot-scope="scope">
						{{ scope.row.createTime | formatDate('YYYY年MM月DD日 HH:mm:ss') }}
					</template>
				</el-table-column>
				<el-table-column label="操作" align="center" prop="typeName" min-width="280">
					<template slot-scope="scope">
						<el-link
							v-hasPermi="['system:weather:edit']"
							type="primary"
							:underline="false"
							style="margin-left: 10px"
							@click.stop="handleDetail(scope.row)"
						>
							{{ activeName === 'first' ? '设置预警权限' : '查看' }}
						</el-link>
						<el-link
							v-hasPermi="['system:weather:edit']"
							type="primary"
							:underline="false"
							style="margin-left: 10px"
							@click.stop="handleEdit(scope.row)"
						>
							修改
						</el-link>
						<el-link
							v-hasPermi="['system:weather:remove']"
							type="danger"
							:underline="false"
							style="margin-left: 10px"
							@click.stop="handleRemove(scope.row)"
						>
							删除
						</el-link>
					</template>
				</el-table-column>
			</el-table>
			<pagination
				:total="total"
				:page.sync="form.pageNum"
				:limit.sync="form.pageSize"
				:pageSizes="[10, 20, 30, 50, 100, 1000]"
				@pagination="getList"
			/>
		</template>
		<Dialog
			ref="dialog"
			:title="title"
			:show.sync="visible"
			width="35%"
			:close-on-click-modal="false"
			:close-on-press-escape="false"
		>
			<WeatherSettingForm
				v-if="visible"
				:data="formData"
				:readonly="readonly"
				:activeName="activeName"
				:statusOption="dict.type.map_weather_warning_level_status"
				:roleOptions="roleOptions"
				@onCancel="closeDialog"
			/>
		</Dialog>
	</PageContainer>
</template>

<script>
import { getListPage, getMsgListPage, del, delMsg } from '@/api/system/weatherSetting'
import Dialog from '@/components/Dialog/index.vue'
import WeatherSettingForm from './components/addOrEdit.vue'
import { getUser } from '@/api/system/user'

export default {
	name: 'WeatherSetting',
	props: {},
	components: { Dialog, WeatherSettingForm },
	dicts: [
		'map_weather_warning_level_status',
		'map_weather_warning_type',
		'map_weather_warning_Level',
	],
	data() {
		return {
			activeName: 'first',
			tableData: [],
			ids: [],
			form: {
				pageNum: 1,
				pageSize: 10,
			},
			total: 0,
			loading: false,
			visible: false,
			readonly: false,
			timeout: null,
			formData: {},
			selection: [],
			title: '',
			roleOptions: [],
		}
	},
	async created() {
		const res = await getUser()
		if (res.code === 200) {
			this.roleOptions = res.roles || []
		}
		this.getList()
	},
	methods: {
		getList() {
			this.loading = true
			let api = this.activeName === 'first' ? getListPage : getMsgListPage
			api(this.form)
				.then((res) => {
					if (res.code === 200) {
						res.rows.forEach((item) => {
							let roleIdsName = []
							item.roleIds?.map((id) => {
								roleIdsName.push(this.roleOptions.find((role) => role.roleId == id))
							})
							item.roleIdsName = roleIdsName?.map((item) => item.roleName).join(', ')
						})
						this.tableData = res.rows || []
						this.total = res.total || 0
					}
				})
				.finally(() => {
					setTimeout(() => {
						this.loading = false
					}, 300)
				})
		},
		// 勾选高亮
		rowStyle({ row, rowIndex }) {
			if (this.ids.includes(row.id)) {
				return { 'background-color': '#b7daff', color: '#333' }
			} else {
				return { 'background-color': '#fff', color: '#333' }
			}
		},
		// 多选框选中数据
		handleSelectionChange(selection) {
			this.ids = selection.map((item) => item.id)
			this.selection = selection
		},
		// 表格点击勾选
		handleRowClick(row) {
			row.isSelected = !row.isSelected
			this.$refs.table.toggleRowSelection(row)
		},
		handleClick() {
			clearTimeout(this.timeout)
			this.timeout = setTimeout(() => {
				this.getList()
			}, 300)
		},
		handleAdd() {
			this.title = this.activeName === 'first' ? '新增预警级别' : '新增短信模版'
			this.formData = {}
			this.visible = true
			this.readonly = false
		},
		handleEdit(row) {
			this.title = this.activeName === 'first' ? '修改预警级别' : '修改短信模版'
			if (row.id) {
				this.formData = row
			} else {
				if (this.selection.length !== 1) {
					this.$message.warning('请选择一条数据')
					return
				}
				this.formData = this.selection[0]
			}
			this.visible = true
			this.readonly = false
		},
		handleRemove(row) {
			let api = this.activeName === 'first' ? del : delMsg
			if (row.id) {
				this.$confirm('确定删除吗？', '提示', {
					confirmButtonText: '确定',
					cancelButtonText: '取消',
					type: 'warning',
				}).then(() => {
					api(row.id).then((res) => {
						if (res.code === 200) {
							this.$message.success('删除成功')
							this.getList()
						}
					})
				})
			} else {
				if (this.selection.length === 0) {
					this.$message.warning('请选择一条数据')
					return
				} else {
					this.$confirm(`已选择${this.selection.length}条数据，确认删除吗？`, '提示', {
						confirmButtonText: '确定',
						cancelButtonText: '取消',
						type: 'warning',
					}).then(() => {
						let ids = this.selection.map((item) => item.id).join(',')
						api(ids).then((res) => {
							if (res.code === 200) {
								this.$message.success('删除成功')
								this.getList()
							}
						})
					})
				}
			}
		},
		delete(row) {
			this.loading = true
			deleteById(row.id)
				.then((res) => {
					if (res.code === 200) {
						this.$message.success('删除成功')
						this.getList()
					}
				})
				.finally(() => {
					this.loading = false
				})
		},
		// handleExport() {},
		handleDetail(row) {
			this.title = this.activeName === 'first' ? '设置预警权限' : '查看短信模版'
			if (row.id) {
				this.formData = row
			} else {
				if (this.selection.length !== 1) {
					this.$message.warning('请选择一条数据')
					return
				}
				this.formData = this.selection[0]
			}
			this.visible = true
			this.readonly = true
		},
		closeDialog(status) {
			this.visible = false
			if (status) {
				this.getList()
			}
		},
	},
	computed: {},
	watch: {},
}
</script>

<style lang="scss" scoped>
.button-list {
	border-radius: 4px;
	width: 100%;
	.el-button {
		margin-bottom: 10px;
		margin-right: 10px;
		margin-left: 0;
	}
}
</style>
