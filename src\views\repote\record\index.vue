<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryForm"
      size="small"
      :inline="true"
      label-width="68px"
    >
      <el-form-item label="" prop="missionNameLike">
        <el-input
          v-model="queryParams.missionNameLike"
          placeholder="请输入填报任务名称"
          clearable
          style="width: 240px"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="" prop="formNameLike">
        <el-input
          v-model="queryParams.formNameLike"
          placeholder="请输入填报表格名称"
          clearable
          style="width: 240px"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="" prop="userNameLike">
        <el-input
          v-model="queryParams.userNameLike"
          placeholder="请输入填报人名字"
          clearable
          style="width: 240px"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>

      <el-form-item label="" prop="status">
        <el-select
          v-model="queryParams.status"
          placeholder="请选择填报状态"
          clearable
          style="width: 240px"
        >
          <el-option label="全部" :value="null" />
          <el-option label="待填写" :value="1" />
          <el-option label="待提交" :value="2" />
          <el-option label="已提交" :value="3" />
        </el-select>
      </el-form-item>

      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
          >搜索</el-button
        >
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
          >重置</el-button
        >
      </el-form-item>
    </el-form>
    <!--默认折叠-->

    <!--默认折叠 ,此处仅作为示例-->
    <el-form
      :model="queryParams"
      size="small"
      :inline="true"
      v-show="showSearch"
      label-width="68px"
    >
    </el-form>
    <!--筛选区结束-->

    <!--操作按钮区开始-->
    <el-row :gutter="10" class="mb8" v-if="false">
      <el-col :span="1.5">
        <el-button
          type="primary"
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          >新增
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          >修改
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          >删除
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="info"
          plain
          icon="el-icon-upload2"
          size="mini"
          @click="handleImport"
          >导入
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          >导出
        </el-button>
      </el-col>
      <right-toolbar
        :showSearch.sync="showSearch"
        @queryTable="getList"
        :columns="columns"
      ></right-toolbar>
    </el-row>
    <!--操作按钮区结束-->

    <!--数据表格开始-->
    <div class="tableDiv">
      <el-table
        v-adjust-table
        size="mini"
        :height="showSearch ? 'calc(100vh - 320px)' : 'calc(100vh - 230px)'"
        style="width: 100%"
        v-loading="loading"
        border
        :data="repoteRecordList"
        @selection-change="handleSelectionChange"
        ref="table"
        :row-style="rowStyle"
        @row-click="handleRowClick"
      >
        <el-table-column type="selection" width="50" align="center" />
        <el-table-column label="序号" type="index" width="50" />
        <el-table-column
          label="填报任务名称"
          width="220"
          align="center"
          prop="missionName"
        />
        <el-table-column
          label="填报表格名称"
          width="220"
          align="center"
          prop="formName"
        />
        <el-table-column label="填报要求" align="center" prop="request" />
        <el-table-column label="填报人" align="center" prop="userName" />
        <el-table-column
          label="任务填报要求文件"
          align="center"
          prop="missionUrl"
        >
          <template v-slot="scope">
            <FileUpload
              previewWidth="80%"
              for-view
              :value="scope.row.missionUrl"
              :showTitle="true"
              :download-name="`${scope.row.missionName}填报要求`"
            ></FileUpload>
          </template>
        </el-table-column>
        <el-table-column label="模板" align="center" prop="url">
          <template v-slot="scope">
            <FileUpload
              previewWidth="80%"
              for-view
              :value="scope.row.formUrl"
              :download-name="`${scope.row.formName}_${scope.row.userName}_${scope.row.createTime}`"
            ></FileUpload>
          </template>
        </el-table-column>
        <el-table-column label="填报状态" align="center" prop="status">
          <template v-slot="scope">
            <el-tag
              :type="
                scope.row.status === 1
                  ? 'success'
                  : scope.row.status === 2
                  ? 'info'
                  : ''
              "
            >
              {{
                scope.row.status === 1
                  ? '待填写'
                  : scope.row.status === 2
                  ? '待提交'
                  : '已提交'
              }}
            </el-tag>
            <div
              v-if="scope.row.status === 3"
              style="font-size: 12px; margin-top: 5px"
            >
              {{ scope.row.updateTime | formatDate('YYYY-MM-DD') }}<br />
              {{ scope.row.updateTime | formatDate('HH:mm:ss') }}
            </div>
          </template>
        </el-table-column>
        <el-table-column
          label="填报要求截止时间"
          align="center"
          prop="expiry"
          width="150"
        >
          <template slot-scope="scope">
            <span :style="getExpiryStyle(scope.row)">
              {{ scope.row.expiry | formatDate('YYYY年MM月DD日') }}
            </span>
          </template>
        </el-table-column>
        <el-table-column label="任务备注" align="center" prop="missionRemake" />
        <el-table-column label="备注" align="center" prop="remake" />
        <el-table-column
          label="操作"
          align="center"
          width="160"
          class-name="small-padding"
        >
          <template slot-scope="scope" v-if="scope.row.userId !== 1">
            <el-button
              v-if="scope.row.status === 1"
              size="mini"
              type="text"
              icon="el-icon-edit"
              @click="handleUpdate(scope.row, '填报记录')"
              >填报
            </el-button>
            <el-button
              v-if="scope.row.status === 2"
              size="mini"
              type="text"
              icon="el-icon-edit"
              @click="handleUpdate(scope.row, '填报记录')"
              >编辑
            </el-button>
            <el-button
              v-if="scope.row.status === 2"
              size="mini"
              type="text"
              icon="el-icon-document-checked"
              @click="handleUpdate(scope.row, '提交记录')"
              >提交
            </el-button>
            <el-button
              v-if="scope.row.status === 3"
              size="mini"
              type="text"
              icon="el-icon-view"
              @click="handleUpdate(scope.row, '查看记录')"
              >查看
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <pagination
        v-show="total > 0"
        :total="total"
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
        @pagination="getList"
      />
    </div>
    <!--数据表格结束-->

    <!-- 添加或修改填报记录对话框 -->
    <el-dialog
      :title="title"
      :visible.sync="open"
      v-if="open"
      width="600px"
      append-to-body
      destroy-on-close
      :close-on-click-modal="false"
    >
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="填报人" prop="userName">
          <el-input
            v-model="form.userName"
            placeholder="请输入填报人名字"
            :readonly="title.substring(0, 2) !== '填报'"
          />
        </el-form-item>

        <!--        <el-row :gutter="20">-->
        <!--          <el-col :span="12">-->
        <el-form-item label="备注" prop="remake">
          <el-input
            type="textarea"
            :rows="4"
            v-model="form.remake"
            placeholder="请输入备注"
            :readonly="title.substring(0, 2) !== '填报'"
          />
        </el-form-item>
        <!--          </el-col>-->
        <!--          <el-col :span="12">-->
        <el-form-item label="填报表格" prop="url">
          <FileUpload
            :owner-id="Date.now()"
            previewWidth="80%"
            ref=""
            v-model="form.url"
            :limit="1"
            :fileType="['xlsx']"
            :for-view="title.substring(0, 2) !== '填报'"
            :is-show-tip="title.substring(0, 2) == '填报'"
            @input="(value) => (this.form.url = value[0])"
          ></FileUpload>
        </el-form-item>
        <!--        </el-col>-->
        <!--        </el-row>-->
        <div class="headRow-tip" v-if="title.substring(0, 2) == '填报'">
          上传的“填报表格”表，表头所在行中与模板所在行保持一致。
        </div>
        <el-form-item label="表格附件" prop="annexUrls">
          <FileUpload
            previewWidth="80%"
            :owner-id="Date.now()"
            :for-view="title.substring(0, 2) !== '填报'"
            v-model="form.annexUrls"
            @input="(value) => (this.form.annexUrls = value[0])"
          ></FileUpload>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button
          v-if="title.substring(0, 2) !== '查看'"
          type="primary"
          @click="submitForm"
        >
          {{ title.substring(0, 2) === '填报' ? '确 定' : '提 交' }}
        </el-button>
        <el-button @click="cancel">{{
          title.substring(0, 2) !== '查看' ? '取 消' : '关 闭'
        }}</el-button>
      </div>
    </el-dialog>

    <!-- 用户导入对话框 -->
    <el-dialog
      :title="upload.title"
      :visible.sync="upload.open"
      v-if="upload.open"
      width="400px"
      append-to-body
      destroy-on-close
      :close-on-click-modal="false"
    >
      <el-upload
        ref="upload"
        :limit="1"
        accept=".xlsx, .xls"
        :headers="upload.headers"
        :action="upload.url + '?updateSupport=' + upload.updateSupport"
        :disabled="upload.isUploading"
        :on-progress="handleFileUploadProgress"
        :on-success="handleFileSuccess"
        :auto-upload="false"
        drag
      >
        <i class="el-icon-upload"></i>
        <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
        <div class="el-upload__tip text-center" slot="tip">
          <div class="el-upload__tip" slot="tip">
            <el-checkbox v-model="upload.updateSupport" />
            是否更新已经存在的用户数据
          </div>
          <span>仅允许导入xls、xlsx格式文件。</span>
          <el-link
            type="primary"
            :underline="false"
            style="font-size: 12px; vertical-align: baseline"
            @click="importTemplate"
            >下载模板
          </el-link>
        </div>
      </el-upload>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitFileForm">确 定</el-button>
        <el-button @click="upload.open = false">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import moment from 'moment';
import {
  listRepoteRecord,
  getRepoteRecord,
  delRepoteRecord,
  addRepoteRecord,
  updateRepoteRecord,
} from '@/api/repote/repoteRecord';
import { getToken } from '@/utils/auth';
import Treeselect from '@riophae/vue-treeselect';
import '@riophae/vue-treeselect/dist/vue-treeselect.css';
import { getUserProfile } from '@/api/system/user';

export default {
  name: 'RepoteRecord',
  components: {},
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: false,
      dictType: [],
      // 总条数
      total: 0,
      // 填报记录表格数据
      repoteRecordList: null,
      // 弹出层标题
      title: '',
      // 部门树选项
      deptOptions: undefined,
      // 是否显示弹出层
      open: false,

      // 表单参数
      form: {},
      defaultProps: {
        children: 'children',
        label: 'label',
      },
      // 用户导入参数
      upload: {
        // 是否显示弹出层（用户导入）
        open: false,
        // 弹出层标题（用户导入）
        title: '',
        // 是否禁用上传
        isUploading: false,
        // 是否更新已经存在的用户数据
        updateSupport: 0,
        // 设置上传的请求头部
        headers: { Authorization: 'Bearer ' + getToken() },
        // 上传的地址
        url: process.env.VUE_APP_BASE_API + '/system/user/importData',
      },
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        formId: null,
        userId: null,
        missionName: null,
        missionNameLike: null,
        formName: null,
        formNameLike: null,
        userName: null,
        userNameLike: null,
        status: 1,
      },
      // 列信息
      columns: [
        { key: 0, label: `填报表格ID`, visible: true },
        { key: 1, label: `填报人ID`, visible: true },
        { key: 2, label: `填报人名字`, visible: true },
        { key: 3, label: `填报后表格地址`, visible: true },
        { key: 4, label: `备注`, visible: true },
        { key: 5, label: `填报状态`, visible: true },
      ],
      // 表单校验
      rules: {
        userName: [{ require: true, message: '请输入填报人', trigger: 'blur' }],
        url: [{ require: true, message: '请上传表格', trigger: 'blur' }],
      },
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询用户列表 */
    getList() {
      this.loading = true;
      listRepoteRecord(this.queryParams).then((response) => {
        this.repoteRecordList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 表格点击勾选
    handleRowClick(row) {
      row.isSelected = !row.isSelected;
      this.$refs.table.toggleRowSelection(row);
    },
    // 勾选高亮
    rowStyle({ row, rowIndex }) {
      if (this.ids.includes(row.id)) {
        return { 'background-color': '#b7daff', color: '#333' };
      } else {
        return { 'background-color': '#fff', color: '#333' };
      }
    },
    // 取消按钮
    cancel() {
      this.open = false;
      // this.reset();
    },
    // 表单重置
    reset() {
      this.resetForm('form');
      getUserProfile().then((res) => {
        let user = res.data;
        this.form = {
          formId: null,
          userId: user.userId,
          userName: user.nickName,
          url: null,
          annexUrls: null,
          remake: null,
          missionName: null,
          formName: null,
          status: 1,
        };
      });
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dateRange = [];
      this.resetForm('queryForm');
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id);
      this.single = selection.length != 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = '填报记录';
    },
    /** 修改按钮操作 */
    handleUpdate(row, title) {
      // this.reset();
      const id = row.id || this.ids;
      getRepoteRecord(id).then((response) => {
        this.form = response.data;
        this.open = true;
        this.title = title;
        if (title.startsWith('填报')) this.form.status = 2;
        if (title.startsWith('提交')) this.form.status = 3;
      });
    },
    /** 提交按钮 */
    submitForm: function () {
      this.$refs['form'].validate((valid) => {
        if (valid) {
          if (this.form.id != null) {
            updateRepoteRecord(this.form).then((response) => {
              this.$modal.msgSuccess('修改成功');
              this.open = false;
              this.getList();
            });
          } else {
            addRepoteRecord(this.form).then((response) => {
              this.$modal.msgSuccess('新增成功');
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    getExpiryStyle(row) {
      const now = moment();
      const expiryDate = moment(row.expiry);
      const diffDays = expiryDate.diff(now, 'days');

      if (row.status === 3) {
        return { color: '#67C23A' };
      } else if (diffDays < 0) {
        return { color: '#F56C6C' };
      } else if (diffDays <= 1) {
        return { color: '#E6A23C' };
      } else {
        return {};
      }
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const id = row.id || this.ids;
      this.$modal
        .confirm('是否确认删除填报记录编号为"' + id + '"的数据项？')
        .then(function () {
          return delRepoteRecord(id);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess('删除成功');
        })
        .catch(() => {});
    },

    /** 导出按钮操作 */
    handleExport() {
      this.download(
        'repote/repoteRecord/export',
        {
          ...this.queryParams,
        },
        `repoteRecord_${new Date().getTime()}.xlsx`
      );
    },

    /** 导入按钮操作 */
    handleImport() {
      this.upload.title = '用户导入';
      this.upload.open = true;
    },
    /** 下载模板操作 */
    importTemplate() {
      this.download('system/user/importTemplate', {}, `user_template.xlsx`);
    },
    // 文件上传中处理
    handleFileUploadProgress(event, file, fileList) {
      this.upload.isUploading = true;
    },
    // 文件上传成功处理
    handleFileSuccess(response, file, fileList) {
      this.upload.open = false;
      this.upload.isUploading = false;
      this.$refs.upload.clearFiles();
      this.$alert(
        "<div style='overflow: auto;overflow-x: hidden;max-height: 70vh;padding: 10px 20px 0;'>" +
          response.msg +
          '</div>',
        '导入结果',
        { dangerouslyUseHTMLString: true }
      );
      this.getList();
    },
    // 提交上传文件
    submitFileForm() {
      this.$refs.upload.submit();
    },
  },
};
</script>
<style scoped lang="scss">
// 去掉动画效果
::v-deep .el-list-enter-active,
::v-deep .el-list-leave-active {
  transition: all 0s;
}

::v-deep .el-list-enter,
.el-list-leave-active {
  opacity: 0;
  transform: translateY(0);
}
.tableDiv {
  ::v-deep .el-upload-list__item {
    width: 42px;
    height: 42px;
    margin: 0px 5px -6px 0 !important;
  }

  ::v-deep .el-upload--picture-card {
    width: 42px;
    height: 42px;
    margin: 4px 0;
  }
  ::v-deep .el-icon-plus {
    font-size: 1rem;
  }
  ::v-deep .el-upload-list__item-actions {
    font-size: 1rem;
  }

  ::v-deep .el-upload-list__item-actions span + span {
    margin-left: 5px !important;
  }
}
.headRow-tip {
  color: red;
  text-align: left;
  width: 90%;
  margin: 0 auto 10px;
}
</style>
