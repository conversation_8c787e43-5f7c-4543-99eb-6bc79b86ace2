<template>
  <el-container>
    <el-header>
      <el-form
          :model="form"
          :inline="true"
          label-position="right"
          label-suffix="："
          ref="tigaForm"
          label-width="85px">
        <el-form-item label="监测类型" prop="monitorType">
          <el-select
              v-model="form.monitorType"
              placeholder="请选择监测类型"
              @change="changeMonitorType"
              no-data-text="无数据"
              value-key="content"
              size="small"
          >
            <el-option
                v-for="item in (monitorTypeList.filter(item => item.content.indexOf('车辆荷载') >= 0))"
                :key="item.code"
                :label="item.content"
                :value="item">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="监测内容" prop="monitorContent">
          <el-select
              v-model="form.monitorContent"
              default-first-option
              placeholder="请选择监测内容"
              @change="changeMonitorContent"
              no-data-text="无数据"
              value-key="content"
              size="small"
          >
            <el-option
                v-for="item in monitorContentList"
                :key="item.code"
                :label="item.content"
                :value="item">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="监测位置" prop="monitorLocation">
          <el-select
              v-model="form.monitorLocation"
              default-first-option
              placeholder="请选择监测位置"
              @change="changeMonitorLocation"
              no-data-text="无数据"
              value-key="content"
              size="small"
          >
            <el-option
                v-for="item in monitorLocationList"
                :key="item.code"
                :label="item.content"
                :value="item">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="传感器" prop="sensorId">
          <el-select
              v-model="form.sensorId"
              placeholder="请选择传感器"
              @change="changeSensor"
              no-data-text="无数据"
              value-key="sensorId"
              size="small"
          >
            <el-option
                v-for="item in sensorInfoList"
                :key="item.code"
                :label="item.sensorInstallCode"
                :value="item">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="是否超载" prop="sensorId">
          <el-select
              v-model="form.overLoadSign"
              placeholder="请选择"
              no-data-text="无数据"
              value-key="index"
              size="small"
          >
            <el-option
                v-for="item in overLoadSignList"
                :key="item.index"
                :label="item.label"
                :value="item.index">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="时间范围" prop="timerange">
          <el-date-picker
              size="medium"
              style="width:200px"
              type="datetimerange"
              v-model="form.timerange"
              value-format="yyyy-MM-dd HH:mm:ss"
              format="yyyy-MM-dd HH:mm:ss"
              @on-change="statusDateChange"
              :transfer="true"

          />
        </el-form-item>
        <el-form-item>
          <el-button
              type="primary"
              @click="draw"
              size="small"
              :loading="loading">
            <span>绘制</span>
          </el-button>
        </el-form-item>
<!--        <el-form-item>-->
<!--          <el-button-->
<!--              type="primary"-->
<!--              size="small"-->
<!--              @click="downloadData"-->
<!--          >-->
<!--            <span>数据下载</span>-->
<!--          </el-button>-->
<!--        </el-form-item>-->
        <el-form-item>
          当前选择结构物为：{{currentStructure.label}}
        </el-form-item>
      </el-form>
    </el-header>
    <el-main>
      <div id="myChart8" v-loading="loading" style="width: 100%; height: 400px; overflow:hidden"></div>
    </el-main>
    <!--下载进度条-->
<!--    <DownLoadDialog ref="downLoadDialogRef" fatherName="vehicleLoad"></DownLoadDialog>-->
  </el-container>
</template>

<script>
import {
  getVelData,
  getSensorByDataCode, getMax, getMin
} from "@/api/jgjc/dataAnalysis/customize/index.js";
import * as echarts from "echarts";
export default {
  name: "VehicleLoad",
  props: ['monitorTypeList', 'currentStructure', 'isDraw'],
  emits: ['changeDraw'],
  data() {
    return {
      monitorContentList: [],
      monitorLocationList: [],
      sensorInfoList: [],
      form: {
        monitorType: '',
        monitorContent: '',
        monitorLocation: '',
        sensorId: '',
        timerange: '',
        overLoadSign: 1
      },
      sensorInfo: {},
      overLoadSignList:[{index:0, label:"未超载"}, {index:1, label:"超载"}, {index: 2, label: "全部"}],
      loading: false
    };
  },
  methods: {
    // 判断数据是否为空
    isNone(data) {
      if(this.isPreprocess){
        return false;
      }else {
        let flag = false;
        data.forEach((datai) => {
          if(datai.values.length !==0){
            flag = true;
          }
        });
        return !flag;
      }
    },
    // “绘制”按钮的方法
    async draw() {
      if(this.form.monitorType === '' ||
          this.form.monitorContent === '' ||
          this.form.monitorLocation === '' ||
          this.form.sensorId === '' ||
          this.form.timerange.length === 0 ||
          this.form.timerange[0] === ''
      ){
        this.$message({
          type: 'warning',
          message: "查询数据前请注意查询条件的完整性"
        });
        return;
      }
      this.loading = true
      await getVelData({
        dataCode: this.form.sensorId,
        structureCode: this.currentStructure.structureCode,
        sensorId: this.sensorInfo.sensorId,
        startTime: this.form.timerange[0],
        endTime: this.form.timerange[1],
        overLoadSign: this.form.overLoadSign,
        granularityNum: -1,
        granularityType: 0,
        analysisType: 2,
      }).then(async (res) => {
        this.loading = false
        if (res.data.length === 0 || this.isNone(res.data)) {
          this.setEmptyChart("没有数据");
        } else {
          this.setVehicleLoadChart("myChart8", res.data)
        }
      }).catch((err) => {
        console.log(err)
        this.loading = false
      });
      this.loading = false
    },
    // 绘制车辆荷载图
    setVehicleLoadChart(chartName, data) {
      let myChart = echarts.getInstanceByDom(
          document.getElementById(chartName)
      );
      if (myChart !== undefined) {
        myChart.dispose();
      }
      myChart = echarts.init(document.getElementById(chartName));

      let lane = {}
      let laneIndex = -1
      let carWeight = {}
      let carWeightIndex = -1
      let tmpData = []
      for(let i = 0; i < data.length; i++){
        if(data[i].name === '车道号'){
          lane = data[i]
          laneIndex = i
          continue;
        }else if(data[i].name === '总重'){
          carWeight = data[i]
          carWeightIndex = i
          continue;
        }
        tmpData.push(data[i])
      }
      if(laneIndex < 0 || carWeightIndex < 0){
        this.setEmptyChart("未选择车道号或总重数据")
        return
      }
      let drawData = []
      lane.values.forEach((item, index) =>{
        let tmpACar = [lane.times[index], lane.values[index], carWeight.values[index]]
        for(let i = 0; i < tmpData.length; i++){
          tmpACar.push(tmpData[i].values[index])
        }
        drawData.push(tmpACar)
      })
      // 取最大车重，归一化使用
      let maxWeight = getMax(carWeight.values)
      // 取最大最小值时间，控制横轴显示范围
      let minTime = lane.times[0]
      let maxTime = lane.times.slice(-1)[0]
      // 初始化车道号范围
      const laneInterval = [];
      let maxLane = getMax(lane.values)
      let minLane = getMin(lane.values)
      for(let i = minLane ; i <= maxLane ; i++){
        laneInterval.push(i)
      }

      const title = [];
      const singleAxis = [];
      const series = [];
      laneInterval.forEach(function (item, idx) {
        title.push({
          textBaseline: 'middle',
          top: ((idx + 0.5) * 100) / laneInterval.length - 6 + '%',
          text: "车道"+item,
          textStyle:{
            fontSize:15,
          }
        });
        singleAxis.push({
          min: minTime,
          max: maxTime,
          left: 150,
          boundaryGap: false,
          top: (idx * 100) / laneInterval.length - 1  + '%',
          height: 100 / laneInterval.length - 10 + '%',
          type: 'time',
          nameLocation: "middle",
          nameGap: 32,
          axisLine: {
            show: (idx === (laneInterval.length - 1)),
          },
          axisTick: {
            show: (idx === (laneInterval.length - 1)),
          },
          axisLabel: {
            show: (idx === (laneInterval.length - 1)),
            formatter: {
              year: '{yyyy}',
              month: '{MM}-{dd}\n{HH}:{mm}:{ss}',
              day: '{MM}-{dd}\n{HH}:{mm}:{ss}',
              hour: '{MM}-{dd}\n{HH}:{mm}:{ss}',
              minute: '{HH}:{mm}',
              second: '{HH}:{mm}:{ss}',
              millisecond: '{yyyy}-{MM}-{dd}\n{HH}:{mm}:{ss}',
              none: '{yyyy}-{MM}-{dd}\n{HH}:{mm}:{ss}'
            },
            showMinLabel: true,
            showMaxLabel: true,
            fontSize:11,
          },
        });
        series.push({
          singleAxisIndex: idx,
          coordinateSystem: 'singleAxis',
          type: 'scatter',
          data: [],
          symbolSize: function (dataItem) {
            return dataItem[2] / maxWeight * 30;
          }
        });
      });
      // 添加超载车辆数量
      title.push({
        top: '5%',
        text: this.overLoadSignList[this.form.overLoadSign].label+"车辆数为"+drawData.length+"辆",
        textStyle:{
          fontSize:15,
        }
      })
      drawData.forEach( (dataItem) => {
        let tmpIndex = dataItem[1]-minLane
        if(tmpIndex <= maxLane){
          series[tmpIndex].data.push(dataItem)
        }
      });
      let option = {
        tooltip: {
          textStyle:{
            align:'left'
          },
          trigger: "axis",
          formatter: function (params) {
            params = params[0].data
            let tmpResult = []
            tmpResult.push('时间：' + params[0])
            tmpResult.push('车道号：' + params[1])
            tmpResult.push('总重：' + params[2] + (carWeight.unit ? carWeight.unit : ''))
            for(let i = 3; i < params.length; i++){
              tmpResult.push(tmpData[i-3].name+"："+params[i] + (tmpData[i-3].unit ? tmpData[i-3].unit : ''))
            }
            return tmpResult.join('<br>');
          }
        },
        title: title,
        singleAxis: singleAxis,
        series: series
      };

      option && myChart.setOption(option);
      //自适应大小
      window.onresize = function () {
        myChart.resize();
      };
    },

    setEmptyChart(msg) {
      let myChart = echarts.getInstanceByDom(
          document.getElementById("myChart8")
      );
      if (myChart !== undefined) {
        myChart.dispose();
      }
      myChart = echarts.init(document.getElementById("myChart8"));
      let option = {
        title: {
          text: msg,
          left:'center',
        },
      };
      option && myChart.setOption(option);
      //自适应大小
      window.onresize = function () {
        myChart.resize();
      };
    },
    // 下面是一系列下拉框的change方法
    async changeMonitorType(e) {
      this.form.monitorType = e.content
      this.monitorContentList.splice(0)
      this.monitorLocationList.splice(0)
      this.sensorInfoList.splice(0)
      this.form.monitorContent = '';
      this.form.monitorLocation = '';
      this.form.sensorId = '';
      const monitorContentList = e.children;
      for (const monitorContent of monitorContentList) {
        this.monitorContentList.push(monitorContent)
      }
    },

    async changeMonitorContent(e) {
      this.form.monitorContent = e.content
      this.monitorLocationList.splice(0)
      this.sensorInfoList.splice(0)
      this.form.monitorLocation = '';
      this.form.sensorId = '';
      const monitorLocationList = e.children;
      for (const monitorLocation of monitorLocationList) {
        this.monitorLocationList.push(monitorLocation)
      }
    },

    async changeMonitorLocation(e) {
      this.form.monitorLocation = e.content
      this.sensorInfoList.splice(0)
      this.form.sensorId = '';
      const ref = await getSensorByDataCode({ dataCode: e.code })
      const sensorInfoList = ref.data
      for (const sensorInfo of sensorInfoList) {
        this.sensorInfoList.push({
          code: sensorInfo.code,
          sensorId: sensorInfo.sensorId,
          sensorInstallCode: sensorInfo.dataCode,
          specificMonitorTypeId: sensorInfo.children.map(item => item.sensorCode),
          specificMonitorTypeName: sensorInfo.children.map(item => item.typeName),
        })
      }
    },

    async changeSensor(e) {
      this.form.sensorId = e.sensorInstallCode
      this.sensorInfo = e
    },
    // 时间范围下拉框的change事件
    statusDateChange(e){
      this.form.timerange = e;
    },

    // 清空表单
    resetThisForm(){
      this.monitorContentList.splice(0)
      this.monitorLocationList.splice(0)
      this.sensorInfoList.splice(0)
      this.form.monitorType = '';
      this.form.monitorContent = '';
      this.form.monitorLocation = '';
      this.form.sensorId = '';
    },

    // 数据下载
    async downloadData() {
      if(this.form.monitorType === '' ||
          this.form.monitorContent === '' ||
          this.form.monitorLocation === '' ||
          this.form.sensorId === '' ||
          this.form.timerange.length === 0 ||
          this.form.timerange[0] === ''
      ){
        this.$message({
          type: 'warning',
          message: "下载数据前请注意查询条件的完整性"
        });
        return;
      }
      let fileName = this.sensorInfo.sensorInstallCode + "__" +
          this.form.timerange[0] + ' - ' + this.form.timerange[1]+".xlsx";
      //这里放参数
      const params = {
        nodeCode: this.sensorInfo.code,
        sensorId: this.sensorInfo.sensorId,
        structureNodeCode: this.currentStructure.code,
        startTime: this.form.timerange[0],
        endTime: this.form.timerange[1],
        granularityNum: -1,
        granularityType: 0,
        analysisType: 2,
        specificMonitorContentIds: [],
        dataType: 1,
        rawOrProcess: 0,
        overLoadSign: this.form.overLoadSign
      };
      // 先校验数据量
      if(await isDownLoadLegal(params)){
        return;
      }
      this.$bus.emit("vehicleLoad");
      await this.$refs.downLoadDialogRef.downloadData("/data/download", fileName, params)

    },
  }
}

</script>

<style scoped>
.el-header{
  height: auto !important;
  padding: 0;
  color: #333;
  border-bottom: 1px solid #eee;
}
.el-form-item {
  display: inline-block;
  height: 40px;
  margin: 5px 0 5px 5px;
}
.el-form {
  text-align: left;
}
.el-button {
  margin-left: 5px;
}
/deep/.ivu-input {
  font-size: 14px !important;
}
/deep/.el-input__inner {
  padding: 0 0 0 10px !important;
}
.el-main{
  padding: 0;
  text-align: center;
}
/deep/.tags-select-input {
  .el-select__tags {
    white-space: nowrap;
    overflow: hidden;
    flex-wrap: nowrap !important;
  }
}

/deep/.el-select__tags-text {
  display: inline-block;
  max-width: 75px;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

</style>
