<template>
  <div class="disease-report" v-loading="loading" element-loading-background="rgba(0, 0, 0, 0.4)">
    <div class="chars-pie">
      <Echarts :option="option" v-if="option" height="100%" key="reportKey" />
    </div>
    <div class="sta-data">
      <div class="data-list" v-for="(item, index) in rData" :key="'list' + index"
        :class="index < rData.length - 1 ? 'border-b' : ''">
        <div class="list-name" :style="{ width: isBig ? '40%' : '50%' }">
          <span class="legend-color" :style="{ backgroundColor: rColor[index] }"></span>
          <span>{{ item.name }}</span>
        </div>
        <div class="list-value">
          <div class="account-for">
            <span class="value">{{ item.value }} <span>个</span></span>
            <span class="account">（{{ item.accountFor }}）</span>
          </div>
          <div class="on-year">
            <span class="name">同比</span>
            <span class="value" :style="{ color: item.onYear > 0 ? '#EA0000' : '#95F204' }">
              {{ item.onYear }}
              <span v-if="item.onYear > 0">↑</span>
              <span v-else>↓</span>
            </span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import Echarts from '../../echarts/echarts.vue';
import { isBigScreen } from "@/views/cockpit/util/utils";
import { getProjectNumberStatistics } from '@/api/cockpit/maintain';

export default {
  name: "diseaseReport",
  components: {
    Echarts,
  },
  props: {
    year: {
      type: [String, Number],
      default: ''
    }
  },
  data() {
    return {
      isBig: isBigScreen(),
      loading: false,
      option: null,
      rData: [{
        name: '2025年',
        value: 1627,
        accountFor: '30%',
        onYear: 23.01,
      }, {
        name: '2024年',
        value: 1627,
        accountFor: '30%',
        onYear: 23.01,
      }, {
        name: '2023年',
        value: 1627,
        accountFor: '30%',
        onYear: -4.01,
      }],
      rColor: ['#3851E6', '#886EFF', '#FD4CDB'],
      totalCount: 0,
    }
  },
  mounted() {
    // this.init();
  },
  watch: {
    year: {
      handler(val) {
        this.init();
      },
      immediate: true,
      deep: true,
    },
  },
  methods: {
    async getStatisticsData() {
      return new Promise((resolve, reject) => {
        let year = this.year || new Date().getFullYear();
        getProjectNumberStatistics(year).then(res=>{
          let data = res.rows || res.data;
          if(res.code == 200 && data) {
            resolve(data)
          } else {
            resolve([])
          }
        }).catch(err => {
          reject(err)
        });
      });
    },
    async init() {
      this.loading = true;
      let d = await this.getStatisticsData();
      this.totalCount = d.totalCount || 0;
      if(d) {
        this.rData = [
          {
            name: d.beforeLastYear + '年',
            value: d.beforeLastYearCount || 0,
            accountFor: (d.beforeLastYearCount/d.totalCount * 100).toFixed(2) + '%',
            onYear: ((d.beforeLastYearCount - d.upYearCount) / d.upYearCount * 100).toFixed(2),
          },
          {
            name: d.lastYear + '年',
            value: d.lastYearCount || 0,
            accountFor: (d.lastYearCount/d.totalCount * 100).toFixed(2) + '%',
            onYear: ((d.lastYearCount - d.beforeLastYearCount) / d.beforeLastYearCount * 100).toFixed(2),
          },
          {
            name: d.currentYear + '年',
            value: d.currentYearCount || 0,
            accountFor: (d.currentYearCount/d.totalCount * 100).toFixed(2) + '%',
            onYear: ((d.currentYearCount - d.lastYearCount) / d.lastYearCount * 100).toFixed(2),
          },
        ];
      }
      let data = this.rData;
      const colorArr = this.rColor || ['#01FBEF', '#3851E6', '#C1A133']
      this.option = this.initPieCharts(data, colorArr);
      this.loading = false;
    },
    initPieCharts(dataArr = [], colorArr = []) {
      var trafficWay = dataArr || [];
      var data = [];

      let sum = dataArr.reduce((acc, curr) => acc + curr.value, 0);
      let avg = sum / (dataArr.length * 10);
      let totals = avg;

      var color = colorArr || ['#00ffff', '#00cfff', '#006ced', '#ffe000', '#ffa800', '#ff5b00', '#ff3000']
      for (var i = 0; i < trafficWay.length; i++) {
        data.push({
          value: trafficWay[i].value,
          name: trafficWay[i].name,
          itemStyle: {
            normal: {
              borderWidth: this.isBig ? 16 : 8,
              shadowBlur: 2,
              borderColor: color[i],
              shadowColor: color[i]
            }
          }
        }, {
          value: totals || 3,
          name: '',
          itemStyle: {
            normal: {
              label: {
                show: false
              },
              labelLine: {
                show: false
              },
              color: 'rgba(0, 0, 0, 0)',
              borderColor: 'rgba(0, 0, 0, 0)',
              borderWidth: 0
            }
          }
        });
      }
      var seriesOption = [
        {
          type: "pie",
          zlevel: 0,
          silent: true,
          radius: ["92%", "96%"],
          center: ['50%', '50%'],
          hoverAnimation: false,
          color: "rgba(0,62,122,1)",
          label: {
            normal: {
              show: false,
            },
          },
          labelLine: {
            normal: {
              show: false,
            },
          },
          data: [1],
        },
        {
          name: '',
          type: 'pie',
          clockWise: false,
          radius: ['80%', '80%'],
          hoverAnimation: false,
          label: {
            normal: {
              show: false,
            },
          },
          labelLine: {
            normal: {
              show: false,
            },
          },
          data: data
        }];
      let option = {
        backgroundColor: 'rgba(0,0,0,0)',
        color: color,
        title: {
          text: `{total|${this.totalCount}}`,
          subtext: `{text|个}`,
          top: this.isBig ? '45%' : '40%',
          textAlign: "center",
          left: "48%",
          textStyle: {
            color: '#fff',
            fontSize: this.isBig ? 42 : 18,
            fontWeight: '700',
            rich: {
              total: {
                fontSize: this.isBig ? 52 : 18,
                fontWeight: 'bold'
              }
            }
          },
          subtextStyle: {
            color: '#fff',
            fontSize: this.isBig ? 28 : 14,
            fontWeight: '400',
            rich: {
              text: {
                fontSize: this.isBig ? 28 : 14,
                fontWeight: 400
              },
            }
          }
        },
        tooltip: {
          show: false
        },
        toolbox: {
          show: false
        },
        series: seriesOption
      }
      return option;
    },
  }
}
</script>

<style lang="scss" scoped>
@import "@/assets/styles/utils.scss";

.disease-report {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  padding: vwpx(20px);

  .chars-pie {
    flex: 1;
    height: 100%;
  }

  .sta-data {
    flex: 1;
    display: flex;
    flex-direction: column;
    margin-top: vwpx(30px);

    .data-list {
      display: flex;
      // justify-content: space-between;
      margin: vwpx(20px);
      color: #ffffff;
      padding-bottom: vwpx(30px);
      font-size: vwpx(26px);

      .list-name {
        .legend-color {
          display: inline-block;
          width: vwpx(20px);
          height: vwpx(20px);
          border-radius: 50%;
          margin: 0 vwpx(10px);
        }
      }

      .list-value {
        // margin-right: vwpx(20px);
        display: flex;
        flex-direction: column;
        justify-content: flex-start;

        .account-for {
          .value {
            span {
              color: #B6B6B6;
            }
          }
        }

        .on-year {
          margin-top: vwpx(12px);

          .name {
            margin-right: vwpx(20px);
          }

          .value {
            // display: flex;
            // align-items: center;

            span {
              margin-left: vwpx(2px);
            }
          }
        }
      }
    }

    .border-b {
      border-bottom: vwpx(2px) dotted rgba(156, 189, 255, 0.5);
    }
  }
}
</style>