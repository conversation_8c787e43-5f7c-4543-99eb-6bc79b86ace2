<template>
    <div>
      <!--数据表格开始-->
        <el-table
          ref="table"
          size="mini"
          :height="showSearch ? 'calc(85vh- 140px)' : 'calc(85vh  - 160px)'"
          style="width: 100%"
          v-loading="loading"
          border
          :data="assetCheckList"
        >
          <el-table-column  label="序号" align="center" type="index" width="50">
            <template v-slot="scope">
              {{
                scope.$index +
                (queryParams.pageNum - 1) * queryParams.pageSize +
                1
              }}
            </template>
          </el-table-column>
          <el-table-column
            label="养护路段"
            align="center"
            prop="maintenanceSectionName"
            min-width="140"
            show-overflow-tooltip
          />
          <el-table-column
            label="路线编码"
            align="center"
            prop="routeCode"
            min-width="140"
            show-overflow-tooltip
          />
          <!--        名称-->
          <el-table-column
            :label="`${partsType[type].substring(0, 2)}${
              ['3', '4'].includes(type) ? '类型' : '名称'
            }`"
            align="center"
            prop="assetName"
            min-width="140"
            show-overflow-tooltip
          />
          <el-table-column
            :label="`${partsType[type].substring(0, 2)}编码`"
            align="center"
            prop="assetCode"
            min-width="140"
            show-overflow-tooltip
          />
          <el-table-column
            label="桩号"
            align="center"
            prop="centerStake"
            :formatter="(...arg) => formatPile(arg[2])"
          />
          <el-table-column
            label="管理处"
            align="center"
            prop="propertyUnitName"
            min-width="140"
            show-overflow-tooltip
          />
          <el-table-column
            label="养护单位"
            align="center"
            prop="maintainUnitName"
            min-width="140"
            show-overflow-tooltip
          />
          <el-table-column
            label="天气"
            align="center"
            prop="weather"
            v-if="['5', '6'].includes(type)"
          />
          <el-table-column label="负责人" align="center" prop="kahunaName" />
          <el-table-column
            label="记录人"
            align="center"
            prop="oprUserName"
            width="140"
          />
          <el-table-column
            label="巡查类别 "
            align="center"
            prop="category"
            v-if="['2', '4', '6'].includes(type)"
          >
            <template slot-scope="scope">
              <dict-tag
                :options="dict.type.patrol_inspection_type"
                :value="scope.row.category"
              />
            </template>
          </el-table-column>
          <el-table-column
            label="检查日期"
            align="center"
            prop="checkTime"
            min-width="140"
            show-overflow-tooltip
          >
            <template slot-scope="scope">
              <span>{{ parseTime(scope.row.checkTime, '{y}-{m}-{d}') }}</span>
            </template>
          </el-table-column>
          <el-table-column label="状态" align="center" prop="status" width="120">
            <template slot-scope="scope">
              <dict-tag
                :options="dict.type.patrol_inspection_status"
                :value="scope.row.status"
              />
            </template>
          </el-table-column>
          <el-table-column
            label="审核日期"
            align="center"
            prop="auditTime"
            min-width="140"
            show-overflow-tooltip
          >
            <template slot-scope="scope">
              <span>{{ parseTime(scope.row.auditTime, '{y}-{m}-{d}') }}</span>
            </template>
          </el-table-column>
          <el-table-column
            label="备注"
            align="center"
            prop="remark"
            show-overflow-tooltip
          />
  
          <el-table-column
            label="操作"
            align="center"
            width="80"
          >
          <!-- class-name="small-padding fixed-width" -->

            <template slot-scope="scope" v-if="scope.row.userId !== 1">
              <el-button
                size="mini"
                type="text"
                icon="el-icon-view"
                @click="handleView(scope.row)"
                >查看
              </el-button>
            </template>
          </el-table-column>
        </el-table>
  
        <pagination
          v-show="total > 0"
          :total="total"
          :page.sync="queryParams.pageNum"
          :limit.sync="queryParams.pageSize"
          @pagination="getList"
        />
    
      <!--数据表格结束-->
  
      <!-- 添加或修改资产寻检查主对话框 -->
      <el-dialog
        :title="title"
        :visible.sync="open"
        width="80%"
        append-to-body
        :close-on-click-modal="false"
        class="asset-check-edit-dialog"
      >
        <AssetCheckInsertOrUpdate
          :disabled="disabled"
          ref="assetCheckInsertOrUpdate"
          :check-entity="form"
        ></AssetCheckInsertOrUpdate>
        <div slot="footer" class="dialog-footer">

            <el-button @click="cancel">取 消</el-button>
        </div>
      </el-dialog>
  
    </div>
  </template>
  
  <script>
  import {
    selectAssetCheckData,
    listAssetCheck,
    getAssetCheck,
    delAssetCheck,
    addAssetCheck,
    updateAssetCheck,
    getAssetTotalCount,
  } from '@/api/patrol/assetCheck';
  import { getToken } from '@/utils/auth';
  
  import TreeSelect from '@riophae/vue-treeselect';
  import '@riophae/vue-treeselect/dist/vue-treeselect.css';
  
  import AssetCheckInsertOrUpdate from '@/views/patrol/assetCheck/insertOrUpdate.vue';
  import AssetCheckGenerate from '@/views/patrol/assetCheck/generate.vue';
  import { listMaintenanceSectionAll } from '@/api/system/maintenanceSection';
  import { listAllRoute } from '@/api/system/route';
  import { getTreeStruct } from '@/api/tmpl';
  import CascadeSelection from '@/components/CascadeSelection/index.vue';
  /**
   * 操作按钮暂不用系统权限控制
   */
  
  export default {
    name: 'DailyInspection',
    dicts: [
      'patrol_inspection_type',
      'patrol_inspection_category',
      'patrol_inspection_status',
    ],
    components: {
      TreeSelect,
      AssetCheckInsertOrUpdate,
      CascadeSelection,
      AssetCheckGenerate,
    },
    props: {
      /**
       * 巡查类型；桥梁日常巡查、桥梁经常检查、隧道日常巡查等
       */
      type: {
        type: String,
        // require: true
        default: '5',
      },
      /**
       * menuType 菜单类型 ps: 1:隧道日常巡查   2:隧道日常巡查审核  3:隧道日常巡查结果
       */
      menuType: {
        type: Number,
        // require: true
        default: 1,
      },
      assetId:{
        default: '',
      }
      ,pageSize:{
        default: 50,
      }
    },
    data() {
      return {
        // 遮罩层
        loading: true,
        // 选中数组
        ids: [],
        // 非单个禁用
        single: true,
        // 非多个禁用
        multiple: true,
        // 显示搜索条件
        showSearch: false,
        // 总条数
        total: 0,
        // 资产寻检查主表格数据
        assetCheckList: null,
        // 弹出层标题
        title: '',
        assetList: [],
        partsType: {
          1: '桥梁日常巡查',
          2: '桥梁经常检查',
          3: '涵洞定期检查',
          4: '涵洞经常检查',
          5: '隧道日常巡查',
          6: '隧道经常检查',
        },
        deptOptions: [],
        maintenanceSectionList: null,
        routeList: null,
        // 是否显示弹出层
        open: false,
        // 是否显示批量生成弹出层
        openGenerate: false,
        // 表单参数
        form: {
          type: this.type,
          partsType: this.type,
          details: null,
        },
        // 用户导入参数
        upload: {
          // 是否显示弹出层（用户导入）
          open: false,
          // 弹出层标题（用户导入）
          title: '',
          // 是否禁用上传
          isUploading: false,
          // 是否更新已经存在的用户数据
          updateSupport: 0,
          // 设置上传的请求头部
          headers: { Authorization: 'Bearer ' + getToken() },
          // 上传的地址
          url: process.env.VUE_APP_BASE_API + '/system/user/importData',
        },
        // 查询参数
        queryParams: {
          pageNum: 1,
          pageSize: this.pageSize,
          type: this.type,
          dataRule: true,
          category: null,
          kahunaId: null,
          kahunaName: null,
          kahunaSign: null,
          oprUserId: null,
          oprUserName: null,
          oprUserSign: null,
          checkTime: null,
          checkStartTime: null,
          checkEndTime: null,
          status: null,
          auditTime: null,
          image: null,
          assetId: null,
          assetName: null,
          assetId: null,
          routeId: null,
          routeName: null,
          routeCode: null,
          propertyUnitId: null,
          propertyUnitName: null,
          departmentIdList: null,
          maintainUnitName: null,
          maintenanceSectionId: null,
          maintenanceSectionName: null,
          centerStake: null,
          weather: null,
        },
        // 列信息
        columns: [
          { key: 0, label: `检查类型`, visible: true },
          { key: 1, label: `巡查类别 `, visible: true },
          { key: 2, label: `负责人`, visible: true },
          { key: 3, label: `记录人`, visible: true },
          { key: 4, label: `检查时间`, visible: true },
          { key: 5, label: `状态 审核状态`, visible: true },
          { key: 6, label: `图像`, visible: true },
          { key: 7, label: `备注`, visible: true },
          { key: 8, label: `删除标识`, visible: true },
          { key: 9, label: `资产id`, visible: true },
          { key: 10, label: `资产名称`, visible: true },
          { key: 11, label: `资产编码`, visible: true },
          { key: 12, label: `路线名称`, visible: true },
          { key: 13, label: `路线编码`, visible: true },
          { key: 14, label: `权属单位`, visible: true },
          { key: 15, label: `养护单位`, visible: true },
          { key: 16, label: `桩号`, visible: true },
          { key: 17, label: `天气`, visible: true },
        ],
      };
    },
    watch: {
      'queryParams.checkTime': function (val) {
        if (Array.isArray(val)) {
          this.queryParams.checkStartTime = val[0];
          this.queryParams.checkEndTime = val[1];
        }
      },
      menuType: {
        handler(value) {
          switch (value) {
            case 1:
              break;
            case 2:
              this.queryParams.status = '2';
              break;
            case 3:
              this.queryParams.statusList = ['3', '4'];
          }
        },
        immediate: true,
      },
    },
    computed: {
      disabled() {
        return (
          this.title.substring(0, 2) === '查看' ||
          this.title.substring(0, 2) === '审核' ||
          this.title.substring(0, 2) === '提交'
        );
      },
    },
    created() {
      this.getList();
    },
    methods: {
      /** 查询用户列表 */
      getList() {
        this.loading = true;
        const queryParams = { ...this.queryParams };
        queryParams.assetId=this.assetId
        delete queryParams.checkTime;
        delete queryParams.departmentIdList;

        selectAssetCheckData(queryParams).then((response) => {
          this.assetCheckList = response.rows;
          this.total = response.total;
          this.loading = false;
        });
      },
      //管理处下拉选点击事件
      deptChange() {
        listMaintenanceSectionAll({
          departmentIdList: this.queryParams.departmentIdList,
        }).then((res) => {
          this.maintenanceSectionList = res.data;
        });
      },

      // 取消按钮
      cancel() {
        this.open = false;
        // this.reset();
      },
      // 表单重置
      reset() {
        this.form = {
          type: this.type,
          partsType: this.type,
          details: null,
          category: null,
          kahunaId: null,
          kahunaName: null,
          kahunaSign: null,
          oprUserId: null,
          oprUserName: null,
          oprUserSign: null,
          checkTime: null,
          status: null,
          auditTime: null,
          image: null,
          remark: null,
          assetId: null,
          assetName: null,
          assetId: null,
          routeId: null,
          routeName: null,
          routeCode: null,
          propertyUnitId: null,
          propertyUnitName: null,
          maintainUnitId: null,
          maintainUnitName: null,
          maintenanceSectionId: null,
          maintenanceSectionName: null,
          centerStake: null,
          weather: null,
        };
      },
      // 多选框选中数据
      handleSelectionChange(selection) {
        this.ids = selection.map((item) => item.id);
        this.single = selection.length != 1;
        this.multiple = !selection.length;
      },
      handleView(row) {
        const id = row.id || this.ids;
        const type = this.type;
        getAssetCheck({ id, type }).then((response) => {
          this.form = { ...response.data, partsType: this.type };
          this.open = true;
          this.title = `查看${this.partsType[this.type]}`;
        });
      },
    },
  };
  </script>
  <style scoped>
  .tableDiv {
    /* background-color: white; */
    padding-bottom: 10px;
  }
  
  /*::v-deep .vue-treeselect__control {*/
  /*  height: auto;*/
  /*}*/
  
  .asset-check-edit-dialog ::v-deep .el-dialog__body {
    padding: 10px 20px;
    height: 85vh;
    overflow-y: auto;
    scrollbar-width: none·;
  }
  </style>
  