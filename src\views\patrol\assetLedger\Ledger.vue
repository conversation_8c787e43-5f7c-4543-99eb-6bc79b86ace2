<template>
  <div class="app-container">
    <!-- 查询 -->
    <div class="searchBox">
      <CascadeSelection style="grid-column: 1 / 3" :form-data="queryForm" v-model="queryForm"
        types="201" multiple />
      <el-input v-model="queryForm.assetCode" :placeholder="`请输入${partsType[type].substring(0, 2)}编码`" clearable
        prefix-icon="el-icon-user" @keyup.enter.native="handleQuery" />
      <el-input v-model="queryForm.assetName" :placeholder="`请输入${partsType[type].substring(0, 2)}名称`" clearable
        prefix-icon="el-icon-user" @keyup.enter.native="handleQuery" />
      <el-date-picker v-model="queryForm.expiry" type="year" :placeholder="`请选择${partsType[type].substring(0, 2)}年份`"
        clearable prefix-icon="el-icon-date" value-format="yyyy" @keyup.enter.native="handleQuery" />
      <el-checkbox v-model="queryForm.needNowDept">
        是否补充现在部门结构
      </el-checkbox>
      <div style="display: flex; gap: 10px">
        <el-button type="primary" icon="el-icon-search" size="mini" @click="queryhandle">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="queryReset">重置</el-button>
      </div>

    </div>
    <!-- 主表数据 -->
    <div class="tableDiv" :style="{ paddingTop: '10px' }">
      <iframe :src="src" v-if="showIframe" id="reportView" frameborder="no" style="width: 100%; height: 100%"
        scrolling="auto" />
    </div>
  </div>
</template>

<script>
import moment from 'moment';
import CascadeSelection from '@/components/CascadeSelection/index.vue';
import { getToken } from '@/utils/auth';

export default {
  name: 'AssetLedger',
  dicts: ['patrol_inspection_status'],
  components: {
    CascadeSelection,
  },
  props: {
    type: {
      type: Number,
      default: 2,
    },
  },
  data() {
    return {
      queryShow: false,
      showIframe: true,
      partsType: {
        2: '桥梁经常检查',
        4: '涵洞经常检查',
        6: '隧道经常检查',
      },
      queryForm: {
        type: this.type,
        assetName: null,
        assetCode: null,
        expiry: moment().format('YYYY'),
        managementMaintenanceIds: null,
        maintenanceSectionId: null,
        routeCodes: null,
        needNowDept: true,
      },
      // 查询参数
      queryParams: {
        type: this.type,
        assetName: null,
        assetCode: null,
        expiry: moment().format('YYYY'),
        managementMaintenanceIds: null,
        maintenanceSectionId: null,
        routeCodes: null,
        needNowDept: true,
      },
    };
  },
  computed: {
    src() {
      let reportId;
      switch (this.type) {
        case 2:
          reportId = process.env.VUE_APP_REPORT_VIEWER_REPORT_ID_BRIDGE_LEDGER;
          break;
        case 4:
          reportId = process.env.VUE_APP_REPORT_VIEWER_REPORT_ID_CULVERT_LEDGER;
          break;
        case 6:
          reportId = process.env.VUE_APP_REPORT_VIEWER_REPORT_ID_TUNNEL_LEDGER;
          break;
        default:
          reportId = process.env.VUE_APP_REPORT_VIEWER_REPORT_ID_BRIDGE_LEDGER;
      }
      var params = `${process.env.VUE_APP_REPORT_VIEWER_URL}${reportId}?token=${getToken()}`;
      params += '&type=' + this.type;
      params += '&expiry=' + moment(this.queryParams.expiry).endOf('year').format('YYYY-MM-DD HH:mm:ss');
      if (this.queryParams.maintenanceSectionId) {
        params += '&sectionId=' + this.queryParams.maintenanceSectionId;
      }

      if (this.queryParams.assetName) {
        params += '&assetName=' + this.queryParams.assetName;
      }
      if (this.queryParams.assetCode) {
        params += '&assetCode=' + this.queryParams.assetCode;
      }

      if (this.queryParams.routeCodes && this.queryParams.routeCodes.length) {
        // 确保是数组并且不是空字符串再进行连接
        params +=
          '&routeCodes=' +
          (Array.isArray(this.queryParams.routeCodes)
            ? this.queryParams.routeCodes.filter((code) => code).join(',')
            : this.queryParams.routeCodes);
      }

      if (this.queryParams.managementMaintenanceIds && this.queryParams.managementMaintenanceIds.length) {
        // 确保是数组并且不是空字符串再进行连接
        params +=
          '&maintenanceIds=' +
          (Array.isArray(this.queryParams.managementMaintenanceIds)
            ? this.queryParams.managementMaintenanceIds.filter((id) => id).join(',')
            : this.queryParams.managementMaintenanceIds);
      }
      params += '&needNowDept=' + this.queryParams.needNowDept;
      return params;
    },
  },
  methods: {
    queryhandle() {
      this.queryParams = { ...this.queryForm };
    },
    // 重置查询条件
    queryReset() {
      this.queryForm = {
        type: this.type,
        assetName: null,
        assetCode: null,
        expiry: moment().format('YYYY'),
        managementMaintenanceIds: null,
        maintenanceSectionId: null,
        routeCodes: null,
        needNowDept: true,
      };
      this.queryhandle();
    },
  },
};
</script>

<style lang="scss" scoped>
.app-container form:first-child .el-select,
.app-container form:nth-child(2) .el-select,
.app-container form:nth-child(2) ::v-deep .el-form-item__content,
.app-container form:first-child ::v-deep .el-form-item__content {
  width: 240px;
}

.app-container form:first-child .el-form-item:last-child ::v-deep .el-form-item__content {
  width: auto;
}

.app-container {
  display: flex;
  flex-direction: column;
  padding: 10px;
  background-color: #c0c0c0;
  box-sizing: border-box;
}

.formDialog {
  ::v-deep .el-dialog__body {
    height: 600px;
    overflow-y: auto;
  }

  .dialog-footer {
    width: 100%;

    .footerTip {
      color: #888888;
      font-size: 14px;
    }
  }

  .titleBox {
    height: 22px;
    position: relative;
    display: flex;
    flex-direction: row;
    align-items: center;

    .title {
      font-size: 16px;
      color: black;
      margin: 0;
    }

    .subTitle {
      margin-left: 15px;
      font-size: 12px;
      color: #888888;
    }

    .riskLevel {
      user-select: none;
      position: absolute;
      // top: 0;
      right: 5%;
      display: flex;
      align-items: center;
      flex-direction: row;

      .title {
        font-size: 16px;
        font-weight: bold;
      }

      .main {
        font-size: 16px;
        font-weight: bold;
        padding: 5px 10px 5px 10px;
        color: white;
        box-sizing: border-box;
        border-radius: 5px;
      }

      .score {
        color: #888888;
        font-size: 14px;
      }
    }
  }
}

.searchBox {
  padding: 10px;
  background: #fff;
  border-radius: 10px;
  transition: all 0.1s linear;
  display: grid;
	gap: 20px;
	grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));

  .searchMoreBox {
    min-width: 192px;
    margin-top: 10px;
    display: flex;
    align-items: center;
    flex-direction: row;
  }
}

.tableDiv {
  flex: 1;
  margin-top: 10px;
  background-color: white;
  padding-bottom: 10px;
  border-radius: 10px;
  transition: all 0.1s linear;
  display: flex;
  flex-direction: column;

  .btnBox {
    padding: 10px;
  }
}

.infoBox {
  padding: 15px 15px 0 15px;
  box-sizing: border-box;
  border-radius: 6px;
  border: 1px solid #c4c4c4;
  position: relative;

  .infoTitle {
    user-select: none;
    position: absolute;
    top: 0;
    left: 0;
    padding: 0 10px;
    font-size: 14px;
    line-height: 14px;
    font-weight: bold;
    transform: translateX(15px) translateY(-50%);
    background-color: white;
  }

  .imgBox {
    height: auto;
    width: 100%;
    display: flex;
    flex-wrap: wrap;
    align-content: flex-start;

    .imgItemBox {
      height: 240px;
      width: calc(100% / 3);
      box-sizing: border-box;
      padding: 10px;
      overflow-y: auto;
      display: flex;
      align-items: center;
      justify-content: center;
      position: relative;

      .imgDeleteBtn {
        position: absolute;
        z-index: 1;
        top: 10%;
        right: 10%;
      }
    }
  }
}

.coordinateDialog {
  .coordinateMap {
    height: 600px;
    width: 100%;
    position: relative;

    #coordinateBox {
      height: 100%;
      width: 100%;
      border-radius: 5px;
      position: relative;
      z-index: 0;
    }

    .coordinateSearch {
      position: absolute;
      z-index: 1;
      top: 10px;
      left: 10px;
      width: 50%;
      padding: 10px;
      box-sizing: border-box;
      background-color: #fff;
      border-radius: 5px;
      border: 1px solid #dcdfe6;
      box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.12), 0 0 6px 0 rgba(0, 0, 0, 0.04);
      display: flex;
      flex-direction: row;
    }

    .coordinateTip {
      position: absolute;
      z-index: 1;
      top: 10px;
      right: 10px;
      padding: 10px;
      box-sizing: border-box;
      background-color: #fff;
      border-radius: 5px;
      border: 1px solid #dcdfe6;
      box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.12), 0 0 6px 0 rgba(0, 0, 0, 0.04);
    }
  }
}

// v-if过渡动画
// 查询框
.search-enter-active {
  transition: all 0.1s linear;
}

.search-enter {
  opacity: 0;
}

.search-leave-active {
  transition: all 0.1s linear;
}

.search-leave-to {
  opacity: 0;
}

::v-deep .treeselect-main {
  line-height: 28px;
  font-size: 12px;
}

::v-deep .vue-treeselect__placeholder {
  line-height: 28px;
  color: #1d2129;
}

::v-deep .vue-treeselect__input {
  line-height: 28px;
}

::v-deep .vue-treeselect__control {
  height: 28px;
  font-size: 12px;
  font-weight: 400;
}

::v-deep .vue-treeselect__single-value {
  line-height: 28px;
}

::v-deep .vue-treeselect__menu-container {
  font-family: Arial;
  color: #1d2129;
}
</style>
