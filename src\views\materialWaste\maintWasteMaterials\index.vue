<template>
  <div class="app-container maindiv">
    <el-row>
      <el-col :span="24" :xs="24">
        <el-row>
          <el-form
            ref="queryForm"
            :model="queryParams"
            size="mini"
            :inline="true"
            label-width="68px"
          >
            <el-form-item>
              <el-input v-model="queryParams.materialName" placeholder="物资名称" />
            </el-form-item>
            <el-form-item>
              <el-input v-model="queryParams.unit" placeholder="单位" />
            </el-form-item>
            <el-form-item>
              <selectTree
                :key="'domainId'"
                style="width: 240px"
                v-model="queryParams.domainId"
                :dept-type="101"
                :expand-all="false"
                :dataRule="false"
                placeholder="送交部门"
                clearable
              />
            </el-form-item>
            <el-form-item>
              <el-date-picker
                v-model="queryParams.inTime"
                type="daterange"
                range-separator="至"
                start-placeholder="入库起始时间"
                end-placeholder="入库截止时间"
                style="width: 240px"
                value-format="yyyy-MM-dd"
              >
              </el-date-picker>
            </el-form-item>
            <el-form-item>
              <el-button
                type="primary"
                icon="el-icon-search"
                size="mini"
                @click="handleQuery"
              >搜索</el-button
              >
              <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
              >重置</el-button
              >
            </el-form-item>
          </el-form>
        </el-row>
      </el-col>
    </el-row>
    <!--操作按钮区开始-->
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          icon="el-icon-plus"
          size="mini"
          v-has-menu-permi="['waste:waste:add']"
          @click="openDetailDialog(null)"
        >新增
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          icon="el-icon-upload2"
          size="mini"
          @click="handleImport"
          v-has-menu-permi="['operate:detail:upload']"
        >导入
        </el-button>
      </el-col>
<!--      <el-col :span="1.5">-->
<!--        <el-button-->
<!--          type="warning"-->
<!--          icon="el-icon-upload"-->
<!--          size="mini"-->
<!--          @click="exportList"-->
<!--        >导入更新-->
<!--        </el-button>-->
<!--      </el-col>-->
      <el-col :span="1.5">
        <el-button
          type="warning"
          icon="el-icon-download"
          size="mini"
          @click="exportList"
        >导出
        </el-button>
      </el-col>
      <right-toolbar
        :showSearch.sync="showSearch"
        @queryTable="handleQuery"
        :columns="columns"
      ></right-toolbar>
    </el-row>
    <el-row>
      <div class="draggable">
        <el-table v-adjust-table
                  size="mini"
                  style="width: 100%"
                  v-loading="loading"
                  border
                  :data="tableData"
                  row-key="id"
                  ref="dataTable"
                  stripe
                  highlight-current-row
                  @row-click="handleClickRow"
                  :height="showSearch ? 'calc(100vh - 330px)' : 'calc(100vh - 270px)'"
        >
          <el-table-column
            label="序号"
            align="center"
            type="index"
            width="50"
          />
          <template v-for="(column, index) in columns">
            <el-table-column
              :label="column.label"
              v-if="column.visible"
              align="center"
              :prop="column.field"
              :width="column.width"
            >
              <template slot-scope="scope">
                <dict-tag
                  v-if="column.dict"
                  :options="dict.type[column.dict]"
                  :value="scope.row[column.field]"
                />
                <template v-else-if="column.slots">
                  <RenderDom
                    :row="scope.row"
                    :index="index"
                    :render="column.render"
                  />
                </template>
                <span v-else-if="column.isTime">{{
                    parseTime(scope.row[column.field], "{y}-{m}-{d}")
                  }}</span>
                <span v-else>{{ scope.row[column.field] }}</span>
              </template>
            </el-table-column>
          </template>
          <el-table-column
            label="操作"
            fixed="right"
            align="center"
            width="250"
            class-name="small-padding fixed-width"
          >
            <template slot-scope="scope">
              <el-button
                type="text"
                @click="openDetailDialog(scope.row)"
                v-has-menu-permi="['waste:waste:edit']"
                icon="el-icon-edit"
              >编辑</el-button
              >
              <el-button
                type="text"
                @click="handleDelete(scope.row)"
                v-has-menu-permi="['waste:waste:remove']"
                icon="el-icon-delete"
              >删除</el-button
              >
            </template>
          </el-table-column>
        </el-table>
        <pagination
          v-show="total > 0"
          :total="total"
          :page.sync="queryParams.pageNum"
          :limit.sync="queryParams.pageSize"
          @pagination="handleQuery"
        />
      </div>
    </el-row>
    <!-- 详情 -->
    <el-dialog
      :title="drawerTitle"
      destroy-on-close
      :visible.sync="drawer"
      :close-on-click-modal="false"
      width="70%"
      v-if="drawer"
    >
      <el-form
        ref="elForm"
        :model="formData"
        :rules="rules"
        label-width="120px"
        size="medium"
      >
        <el-row :gutter="15">
          <el-col :span="12">
            <el-form-item label="物资名称" prop="name">
              <el-input
                v-model="formData.name"
                :style="{ width: '100%' }"
                clearable
                placeholder="请输入物资名称"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="规格型号" prop="model">
              <el-input
                v-model="formData.model"
                :style="{ width: '100%' }"
                clearable
                placeholder="请输入规格型号"
              ></el-input>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="15">
          <el-col :span="12">
            <el-form-item label="单位" prop="unit">
              <el-input
                v-model="formData.unit"
                :style="{ width: '100%' }"
                clearable
                placeholder="请输入单位"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="数量" prop="quantity">
              <el-input-number
                v-model="formData.quantity"
                :style="{ width: '100%' }"
                controls-position="right"
                :min="0"
                placeholder="请输入数量"
              ></el-input-number>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="15">
          <el-col :span="12">
            <el-form-item label="送交部门" prop="deliverDomainId">
              <selectTree
                :key="'domainId'"
                style="width: 100%"
                v-model="formData.deliverDomainId"
                :dept-type="101"
                :expand-all="false"
                :dataRule="false"
                placeholder="送交部门"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="送交时间" prop="deliverTime">
              <el-date-picker
                v-model="formData.deliverTime"
                type="datetime"
                :style="{ width: '100%' }"
                placeholder="请选择送交时间"
                value-format="yyyy-MM-dd HH:mm:ss"
              ></el-date-picker>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="15">
          <el-col :span="12">
            <el-form-item label="送交人" prop="deliverPerson">
              <el-input
                v-model="formData.deliverPerson"
                :style="{ width: '100%' }"
                clearable
                placeholder="请输入送交人"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="接收人" prop="receivePerson">
              <el-input
                v-model="formData.receivePerson"
                :style="{ width: '100%' }"
                clearable
                placeholder="请输入送交人"
              ></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="15">
          <el-col :span="24">
            <el-form-item label="存放地点" prop="storageLocation">
              <el-input
                v-model="formData.storageLocation"
                :style="{ width: '100%' }"
                clearable
                placeholder="请输入存放地点"
              ></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <div style="text-align: right;">
          <el-button type="primary" @click="onSave">保 存</el-button>
          <el-button @click="drawer = false">退 出</el-button>
        </div>
      </el-form>
    </el-dialog>
    <el-dialog :title="upload.title" :visible.sync="upload.open" width="400px" append-to-body>
      <el-upload
        ref="upload"
        :limit="1"
        accept=".xlsx, .xls"
        :headers="upload.headers"
        :action="upload.url + '?deliverDomainId=' + upload.deliverDomainId + '&updateSupport=' + upload.updateSupport"
        :disabled="upload.isUploading"
        :on-progress="handleFileUploadProgress"
        :on-success="handleFileSuccess"
        :auto-upload="false"
        drag
      >
        <i class="el-icon-upload"></i>
        <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
        <div class="el-upload__tip text-center" slot="tip">
          <div class="el-upload__tip" slot="tip">
            <selectTree
              :key="'deliverDomainId'"
              style="width: 100%"
              v-model="upload.deliverDomainId"
              :dept-type="100"
              :expand-all="false"
              :dataRule="false"
              placeholder="选择部门"
              clearable
            />
          </div>
          <span>仅允许导入xls、xlsx格式文件。</span>
          <el-link type="primary" :underline="false" style="font-size:12px;vertical-align: baseline;"
                   @click="importTemplate">下载模板
          </el-link>
        </div>
      </el-upload>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitFileForm">确 定</el-button>
        <el-button @click="upload.open = false">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import selectTree from "@/components/DeptTmpl/selectTree.vue";
import { addWaste, deleteWaste, editWaste, queryWasteListPage } from '@/api/materialWaste/maintWasteMaterials'
import { getToken } from '@/utils/auth'
export default {
  name: 'MaintWasteMaterials',
  components: {
    selectTree,
    RenderDom: {
      functional: true,
      props: {
        row: Object,
        index: Number,
        render: Function,
      },
      render(createElement, ctx) {
        const { row, index } = ctx.props;
        return ctx.props.render(row, index);
      },
    },
  },
  props: [],
  dicts: [],
  data() {
    return {
      showSearch: false,
      queryParams: {
        pageNum: 1,
        pageSize: 50,
      },
      total: 0,
      loading: false,
      columns: [
        {
          key: 0,
          width: 100,
          field: "name",
          label: `物资名称`,
          visible: true,
        },
        {
          key: 1,
          width: 100,
          field: "model",
          label: `规格型号`,
          visible: true,
        },
        {
          key: 2,
          width: 100,
          field: "unit",
          label: `单位`,
          visible: true,
        },
        {
          key: 3,
          field: "quantity",
          label: `数量`,
          visible: true,
        },
        {
          key: 4,
          field: "deliverDomainName",
          label: `送交部门`,
          visible: true,
        },
        {
          key: 5,
          width: 100,
          field: "deliverTime",
          label: `送交时间`,
          visible: true,
        },
        {
          key: 6,
          field: "deliverPerson",
          label: `送交人`,
          visible: true,
        },
        {
          key: 7,
          width: 100,
          field: "receivePerson",
          label: `接收人`,
          visible: true,
        },
        {
          key: 8,
          field: "storageLocation",
          label: `存放地点`,
          visible: true,
        },
      ],
      tableData: [],
      rowData: {},
      drawerTitle: "新增",
      drawer: false,
      formData: {},
      rules: {
        name: [
          { required: true, message: '物资名称不能为空', trigger: 'blur' }
        ],
        model: [
          { required: true, message: '规格型号不能为空', trigger: 'blur' }
        ],
        unit: [
          { required: true, message: '单位不能为空', trigger: 'blur' }
        ],
        quantity: [
          { required: true, message: '数量不能为空', trigger: 'blur' },
          { type: 'number', message: '数量必须为数字值' }
        ],
        deliverDomainId: [
          { required: true, message: '送交部门不能为空', trigger: 'blur' }
        ],
        deliverTime: [
          { required: true, message: '送交时间不能为空', trigger: 'change' }
        ],
        deliverPerson: [
          { required: true, message: '送交人不能为空', trigger: 'blur' }
        ],
        receivePerson: [
          { required: true, message: '接收人不能为空', trigger: 'blur' }
        ],
        storageLocation: [
          { required: true, message: '存放地点不能为空', trigger: 'blur' }
        ],
      },
      upload: {
        // 是否显示弹出层（用户导入）
        open: false,
        // 弹出层标题（用户导入）
        title: "",
        // 是否禁用上传
        isUploading: false,
        // 是否更新已经存在的用户数据
        updateSupport: 0,
        // 设置上传的请求头部
        headers: {Authorization: "Bearer " + getToken()},
        // 上传的地址
        url: process.env.VUE_APP_BASE_API + "/manager/waste/uploadExcel",
        deliverDomainId: ''
      },
    };
  },
  computed: {},
  watch: {},
  created() {
    this.handleQuery();
  },
  mounted() {},
  methods: {
    handleQuery() {
      this.loading = true;
      if(this.queryParams.inTime && this.queryParams.inTime.length > 1) {
        this.queryParams.startDate = this.queryParams.inTime[0];
        this.queryParams.endDate = this.queryParams.inTime[1];
      }
      queryWasteListPage(this.queryParams).then(res => {
        this.loading = false;
        this.tableData = res.rows;
        this.total = res.total;
      })
    },
    resetQuery() {
      this.queryParams = {
        pageNum: 1,
        pageSize: 50,
      };
      this.handleQuery()
    },
    openDetailDialog(row) {
      if (row) {
        this.drawerTitle = "编辑"
      } else {
        this.drawerTitle = "新增"
      }
      this.formData = row || {};
      this.drawer = true;
    },
    onSave() {
      this.$refs.elForm.validate(valid => {
        if (valid) {
          if (this.formData.id) {
            editWaste(this.formData).then(res => {
              this.drawer = false;
              this.handleQuery();
            });
          } else {
            addWaste(this.formData).then(res => {
              this.drawer = false;
              this.handleQuery();
            });
          }
        }
      });
    },
    handleDelete(row) {
      this.$modal.confirm("是否删除").then(() => {
        deleteWaste(row.id).then(res => {
          this.handleQuery();
        });
      });
    },
    handleClickRow(row) {
      this.rowData = row;
    },
    // 导出清单按钮
    exportList() {
      this.download('manager/waste/export', {
          ...this.queryParams
        }, `物资废料导出_${new Date().getTime()}.xlsx`,
        {
          headers: { 'Content-Type': 'application/json;' },
          parameterType: 'body'
        })
    },
    /** 导入按钮操作 */
    handleImport() {
      this.upload.title = "用户导入";
      this.upload.open = true;
    },
    /** 下载模板操作 */
    importTemplate() {
      this.download('manager/waste/download', {}, `物资废料导入模板.xlsx`)
    },
    // 文件上传中处理
    handleFileUploadProgress(event, file, fileList) {
      this.upload.isUploading = true;
    },
    // 文件上传成功处理
    handleFileSuccess(response, file, fileList) {
      this.upload.open = false;
      this.upload.isUploading = false;
      this.$refs.upload.clearFiles();
      this.$alert("<div style='overflow: auto;overflow-x: hidden;max-height: 70vh;padding: 10px 20px 0;'>" + response.msg + "</div>", "导入结果", {dangerouslyUseHTMLString: true});
      this.handleQuery();
      this.$refs.dataTree.doLayout()
    },
    // 提交上传文件
    submitFileForm() {
      if (!this.upload.deliverDomainId) {
        this.$message.error("请选择部门");
        return;
      }
      this.$refs.upload.submit();
    },
  },
};
</script>

<style lang="scss" scoped>
::v-deep .dialog-footer {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  width: 100%;
}
</style>
<style lang="scss" scoped>
@import "@/assets/styles/business.scss";
</style>
