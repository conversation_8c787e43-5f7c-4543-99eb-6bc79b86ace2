<template>
  <div class="stake-mark">
    <el-form :model="form" ref="form" :rules="rules" label-width="40px" :inline="false">
      <el-form-item label="起点">
        <el-input placeholder="请设置" v-model="start" class="input-with-select">
          <div slot="suffix" @click="handleStartPoint">
            <i class="el-icon-location"></i>
          </div>
        </el-input>
      </el-form-item>
      <el-form-item label="终点">
        <el-input placeholder="请设置" v-model="end" class="input-with-select">
          <template slot="suffix">
            <i class="el-icon-location" @click="handleEndPoint"></i>
          </template>
        </el-input>
      </el-form-item>
      <el-form-item>
        <el-button type="success" @click="onCollect" style="width: 100%;">开始采集</el-button>
      </el-form-item>
    </el-form>
    <div class="btn-list">
      <el-button type="primary" @click="onSave('')" size="mini">保存</el-button>
      <el-button type="primary" @click="onQuit" size="mini" class="hollow-out">退出</el-button>
    </div>


    <!-- 新增路网线形 -->
    <Dialog title="新增路网线形" :show.sync="showVisible" :append="true" width="30%">
      <div class="road-net-work-add">
        <el-form ref="formRef" :model="form" :rules="rules" label-width="80px">
          <el-form-item label="养护路段" prop="maintenanceSectionId">
            <el-select v-model="form.maintenanceSectionId" @change="handleRouteChange" @clear="handleRouteClear"
              placeholder="请选择" clearable filterable style="width: 100%;">
              <el-option v-for="item in routeOptions" :key="item.maintenanceSectionId"
                :label="item.maintenanceSectionName" :value="item.maintenanceSectionId" />
            </el-select>
          </el-form-item>
          <el-form-item label="路线编码" prop="routeCode">
            <el-select v-model="form.routeCode" placeholder="请选择" clearable filterable style="width: 100%;">
              <el-option v-for="(item, index) in codeOptions" :key="'route' + index" :label="item.routeName"
                :value="item.routeCode" />
            </el-select>
          </el-form-item>
          <el-form-item label="起点桩号" prop="startStake" :rules="startRules">
            <el-row :gutter="0">
              <el-col :span="1" :offset="0">K</el-col>
              <el-col :span="11" :offset="0">
                <el-input v-model="form.startK" placeholder="请输入" clearable min="0" type="number" />
              </el-col>
              <el-col :span="1" :offset="0">
                <div style="width: 100%;text-align: center;">+</div>
              </el-col>
              <el-col :span="11" :offset="0">
                <el-input v-model="form.startB" placeholder="请输入" clearable min="0" type="number" />
              </el-col>
            </el-row>
          </el-form-item>
          <el-form-item label="终点桩号" prop="endStake" :rules="endRules">
            <el-row :gutter="0">
              <el-col :span="1" :offset="0">K</el-col>
              <el-col :span="11" :offset="0">
                <el-input v-model="form.endK" placeholder="请输入" clearable min="0" type="number" />
              </el-col>
              <el-col :span="1" :offset="0">
                <div style="width: 100%;text-align: center;">+</div>

              </el-col>
              <el-col :span="11" :offset="0">
                <el-input v-model="form.endB" placeholder="请输入" clearable min="0" type="number" />
              </el-col>
            </el-row>
          </el-form-item>
          <el-form-item label="备注" prop="description">
            <el-input type="textarea" :rows="4" placeholder="请输入内容" v-model="form.description" />
          </el-form-item>
        </el-form>

        <div class="btn-list">
          <el-button type="primary" @click="onSave('save')" size="mini">保存</el-button>
          <el-button type="primary" @click="onQuit" size="mini" class="hollow-out">退出</el-button>
        </div>
      </div>
    </Dialog>
  </div>
</template>

<script>
import { onMeasure, addClickFeature, removeLayer, featureToWkt, getLayersByName, wktToFeature } from '@/views/map/components/common/mapFun'
// ol api
import { toLonLat, fromLonLat, transform } from 'ol/proj';
import { Geometry, LineString as GeomLineString, Point } from 'ol/geom';
import { Feature } from 'ol';
import { fromWKT } from 'ol/geom/Geometry';
// 起始点图标
import StartPointIcon from '@/assets/map/start-point.png'
import EndPointIcon from '@/assets/map/end-point.png'

// 组件
import Dialog from "@/components/Dialog/index.vue";

// 接口Api
import {
  listByMaintenanceSectionId,
  getMaintenanceSectionListAll
} from '@/api/baseData/common/routeLine'
import { listAllRoute } from '@/api/system/route'
import { acquisitionAdd } from '@/api/oneMap/acquisition'
import { WKT } from 'ol/format';


export default {
  components: {
    Dialog,
  },
  data() {
    return {
      form: {
        starLocation: '',
        endLocation: '',
        shape: '',
        startK: null,
        startB: null,
        endK: null,
        endB: null,
      },
      start: '',
      end: '',
      rules: {
        maintenanceSectionId: [
          { required: true, message: '请选择养护路段', trigger: 'change' }
        ],
        routeCode: [
          { required: true, message: '请选择路线编码', trigger: 'change' }
        ],
        // startStake: [
        //   { required: true, message: '请输入起点桩号', trigger: 'change' }
        // ],
        // endStake: [
        //   { required: true, message: '请输入终点桩号', trigger: ['change'] }
        // ],
      },
      routeOptions: [],
      codeOptions: [],
      showVisible: false,
    }
  },
  created() {
    this.getRoutes();
    this.getCodes();
  },
  computed: {
    startRules() {
      let isRequired = (!this.form.startK && !this.form.startB) ? true : false;
      return [{ required: isRequired, message: '请输入起点桩号', trigger: ['change','blur'] }];
    },
    endRules() {
      let isRequired = (!this.form.endK && !this.form.endB) ? true : false;
      return [{ required: isRequired, message: '请输入终点桩号', trigger: ['change','blur'] }];
    },
  },
  methods: {
    // 获取养护路段数据
    getRoutes(e) {
      getMaintenanceSectionListAll({ departmentIdList: e }).then(res => {
        if (res.code == 200) {
          this.routeOptions = res.data || []
        }
      })
    },
    // 获取路线编码
    getCodes(e) {
      if (!e) {
        listAllRoute().then(res => {
          if (res.code == 200) {
            this.codeOptions = res.data || []
          }
        })
        return
      }
      this.$modal.loading();
      listByMaintenanceSectionId({ maintenanceSectionId: e }).then(res => {
        if (res.code == 200) {
          let arr = res.data.filter(v => v)
          this.codeOptions = arr || [];
        }
      }).finally(() => {
        this.$modal.closeLoading();
      });
    },
    // 监听路线选择变换
    handleRouteChange(e) {
      if (e) {
        this.form.lxbh = '';
        this.getCodes(e)
      }
    },
    // 清空 路线
    handleRouteClear() {
      this.form.lxbh = '';
      this.getCodes();
    },
    // 获取起点
    handleStartPoint() {
      onMeasure('Point', true, (e) => {
        if (e) {
          let point4326 = e.getGeometry().getCoordinates();
          let lonLat = toLonLat(point4326)
          this.form.starLocation = lonLat ? new WKT().writeGeometry(new Point(lonLat)) : '';
          this.start = lonLat.join(',')

          removeLayer(window.mapLayer, 'pointLayer1', 'name')
          addClickFeature(e, {}, StartPointIcon, false, true, 'pointLayer1', 1, '起点')
          this.clearDraw();
        }
      });
    },
    // 获取终点
    handleEndPoint() {
      onMeasure('Point', true, (e) => {
        if (e) {
          let point4326 = e.getGeometry().getCoordinates();
          let lonLat = toLonLat(point4326)
          this.form.endLocation = lonLat ? new WKT().writeGeometry(new Point(lonLat)) : '';
          this.end = lonLat.join(',')

          removeLayer(window.mapLayer, 'pointLayer2', 'name')
          addClickFeature(e, {}, EndPointIcon, false, true, 'pointLayer2', 1, '终点')
          this.clearDraw();
        }
      });
    },
    // 采集
    onCollect() {
      // 校验输入的经纬度是否·合法
      if (this.start && this.end) {
        const isStartValid = this.validateCoordinates(this.start);
        const isEndValid = this.validateCoordinates(this.end);
        if (!isStartValid) {
          this.$modal.msgError("请输入正确的起点经纬度(用英文逗号隔开)");
          return
        }
        if (!isEndValid) {
          this.$modal.msgError("请输入正确的终点经纬度(用英文逗号隔开)");
          return
        }
        this.toMap();
      } else {
        this.$modal.msgError("请输入或选取起点和终点！");
        return
      }
      onMeasure('LineString', true, (e) => {
        if (!e) return;
        let shape = featureToWkt(e)
        // 解析3857坐标系的WKT
        const geometry = new WKT().readGeometry(shape);
        // 将geometry从3857坐标系转换到4326坐标系
        const geometry4326 = geometry.clone().transform('EPSG:3857', 'EPSG:4326');
        // 将转换后的geometry转换为WKT
        const wkt4326 = new WKT().writeGeometry(geometry4326);

        this.form.shape = wkt4326
      });
    },
    // 保存
    onSave(type = null) {
      if (!this.form.shape) {
        this.$modal.msgError("请先采集数据");
        return
      }
      if (!type) {
        this.showVisible = true;
      } else {
        console.log('提交数据', this.form);
        this.form.startStake = (this.form.startK * 1000 - 0) + (this.form.startB - 0)
        this.form.startStake = this.form.startStake == 0 ? null : this.form.startStake
        this.form.endStake = (this.form.endK * 1000 - 0) + (this.form.endB - 0)
        this.form.endStake = this.form.endStake == 0 ? null : this.form.endStake
        this.$refs.formRef.validate((valid) => {
          if (!valid) return;
          this.$modal.confirm('确认保存该采集信息？').then(() => {
            this.$modal.loading();
            acquisitionAdd(this.form).then(res => {
              if (res.code === 200) {
                this.$modal.msgSuccess("提交成功");
                this.onQuit();
              }
            }).finally(() => {
              this.$modal.closeLoading();
            });
          }).then(() => {

          }).catch(() => { });
        });
      }
    },
    // 退出
    onQuit() {
      removeLayer(window.mapLayer, 'pointLayer1', 'name')
      removeLayer(window.mapLayer, 'pointLayer2', 'name')
      if (window.draw) {
        window.drawSource.clear()
        window.draw.finishDrawing() // 绘制完成
        window.mapLayer.removeInteraction(window.draw)
        window.draw = null;
      }

      this.$emit('quit')
    },

    // 清除
    clearDraw() {
      if (window.draw) {
        window.drawSource.clear()
        window.draw.finishDrawing() // 绘制完成
        window.mapLayer.removeInteraction(window.draw)
        // window.draw = null;
      }
    },
    // 跳转至
    toMap() {
      let startPoint = null;
      let endPoint = null;
      if (this.start) {
        startPoint = this.start.split(',')
      }
      if (this.end) {
        endPoint = this.end.split(',')
      }
      // 如果是直接输入-则需要添加2个点 通过一下几步
      // 1、判断两个点图层是否存在
      if (!getLayersByName('name', 'pointLayer1')) {
        // 转为数值
        let startArr = startPoint.map(v => v - 0)
        let shape = new WKT().writeGeometry(new Point(startArr))
        let feature = wktToFeature(shape);
        addClickFeature(feature, {}, StartPointIcon, false, true, 'pointLayer1', 1, '起点')
      }
      if (!getLayersByName('name', 'pointLayer2')) {
        let endArr = endPoint.map(v => v - 0)
        let shape = new WKT().writeGeometry(new Point(endArr))
        let feature = wktToFeature(shape);
        addClickFeature(feature, {}, EndPointIcon, false, true, 'pointLayer2', 1, '终点')
      }

      startPoint = startPoint.map(v => Number(v))
      endPoint = endPoint.map(v => Number(v))
      const centerX = ((startPoint[0] - 0) + (endPoint[0] - 0)) / 2;
      const centerY = ((startPoint[1] - 0) + (endPoint[1] - 0)) / 2;
      const center = [centerX, centerY];
      const lineCoordinates = [fromLonLat(startPoint), fromLonLat(endPoint), fromLonLat(center)];
      let lineFeature = new Feature({
        geometry: new GeomLineString(lineCoordinates)
      })
      window.mapLayer.getView().fit(lineFeature.getGeometry().getExtent(), {
        size: window.mapLayer.getSize(),
        duration: 1500,
        padding: [250, 250, 250, 250], // 视图边缘和地图边缘之间的距离
        easing: (t) => { // 可选的缓动函数
          return t * (2 - t); // 二次缓动
        },
        minResolution: 5,
      })
    },
    validateCoordinates(inputValue) {
      // 正则表达式，匹配由逗号分隔的两个数字部分
      const pattern = /^[-+]?(180(\.0+)?|((1[0-7]\d)|([1-9]?\d))(\.\d+)?),[-+]?([1-8]?\d(\.\d+)?|90(\.0+)?)$/;
      return pattern.test(inputValue);
    },
  }
} 
</script>

<style lang="scss" scoped>
$border-color: #0166FE;
$bg-color: rgba(1, 102, 254, 0.2);

.stake-mark {
  z-index: 9999;
  margin: 10px 0;
  color: #ffffff;

  .el-icon-location {
    font-size: 20px;
    color: #06FFFF;
    cursor: pointer;
    line-height: 30px;
  }



  .btn-list {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    margin-top: 30px;

    .hollow-out {
      background-color: rgba(1, 102, 254, 0.2);
      color: #ffffff;
    }
  }
}

.road-net-work-add {

  .btn-list {
    width: 100%;
    display: flex;
    justify-content: flex-end;
  }
}

::v-deep .el-dialog {
  margin: 0 auto 0;
  background: rgba(4, 17, 48, 0.8);
  box-shadow: inset 0px 0px 10px 0px #3662EC;
  border-radius: 10px 10px 10px 10px;
  border: 1px solid #0687FF;
  color: #ffffff !important;

  .el-dialog__header {
    border-bottom: none;
    padding: 10px 15px !important;

    .el-dialog__title {
      color: #ffffff;
    }

    .el-dialog__headerbtn {
      color: #ffffff;
      top: 10px;
    }
  }

  .el-dialog__body {
    padding: 10px;
    color: #ffffff !important;
  }
}

::v-deep .el-form {
  .el-form-item__label {
    color: #ffffff;
  }
}

::v-deep .el-input {
  .el-input__inner {
    background-color: rgba(1, 102, 254, 0.2);
    border: 1px solid #0166FE;
    color: #ffffff;
  }



  .el-input__inner::placeholder {
    color: #BBBBBB;
  }

  .el-input-group__append {
    background-color: rgba(1, 102, 254, 0.2);
    border: 1px solid #0166FE;
    color: #ffffff;
    border-left: none;
    padding: 0 10px;
    cursor: pointer;
  }
}

::v-deep .el-textarea {
  .el-textarea__inner {
    background-color: rgba(1, 102, 254, 0.2) !important;
    border: 1px solid #0166FE;
    color: #ffffff;
  }
}
</style>