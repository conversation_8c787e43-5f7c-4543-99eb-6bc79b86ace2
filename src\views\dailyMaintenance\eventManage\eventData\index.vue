<template>
  <div class="app-container maindiv">
    <el-row :gutter="20">
      <!--部门数据-->
<!--      <el-col :span="relaNav ? 5 : 0" :xs="24" class="leftDiv">-->
<!--        &lt;!&ndash;折叠图标&ndash;&gt;-->
<!--        <div class="leftIcon" @click="relaNav = false">-->
<!--          <span class="el-icon-caret-left"></span>-->
<!--        </div>-->
<!--        <div class="head-container">-->
<!--          <el-input-->
<!--            v-model="keyword"-->
<!--            placeholder="输入关键词检索"-->
<!--            @change="handleSearch"-->
<!--            clearable-->
<!--            size="small"-->
<!--            prefix-icon="el-icon-search"-->
<!--            style="margin-bottom: 20px"-->
<!--          />-->
<!--        </div>-->
<!--        <div class="left-total">共 {{ leftTotal }} 条</div>-->
<!--        <div class="head-container" style="width: 300px">-->
<!--          <el-tree-->
<!--            :data="filteredTreeData"-->
<!--            :expand-on-click-node="false"-->
<!--            :filter-node-method="filterNode"-->
<!--            :default-expanded-keys="[0]"-->
<!--            ref="tree"-->
<!--            node-key="id"-->
<!--            highlight-current-->
<!--            @node-click="handleNodeClick"-->
<!--          >-->
<!--          </el-tree>-->
<!--        </div>-->
<!--      </el-col>-->
      <!--角色数据-->
      <el-col :span="24" :xs="24">
        <!--展开图标-->
        <div class="rightIcon" @click="relaNav = true" v-show="!relaNav">
          <span class="el-icon-caret-right"></span>
        </div>
        <el-row>
          <el-col :span="24" :xs="24">
            <el-row>
              <el-form
                ref="queryForm"
                :model="queryParams"
                size="mini"
                :inline="true"
                label-width="68px"
              >
                <el-form-item label="" prop="domainId">
                  <el-date-picker
                    v-model="queryParams.yearStr"
                    style="width: 240px"
                    type="year"
                    value-format="yyyy"
                    placeholder="年份">
                  </el-date-picker>
                </el-form-item>
                <el-form-item label="" prop="domainId">
                  <selectTree
                    :key="'field2'"
                    style="width: 240px"
                    v-model="queryParams.domainIdStr"
                    :deptType="100" :deptTypeList="[1, 3, 4]"
                    placeholder="管养单位"
                    clearable
                    filterable
                  />
                </el-form-item>
                <el-form-item label="" prop="maiSecId">
                  <RoadSection v-model="queryParams.maiSecId" :deptId="queryParams.domainIdStr" placeholder="路段名称"/>
                </el-form-item>
                <el-form-item>
                  <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
                  <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
                  <el-button v-show="!showSearch" @click="showSearch=true" icon="el-icon-arrow-down" circle></el-button>
                  <el-button v-show="showSearch" @click="showSearch=false" icon="el-icon-arrow-up" circle></el-button>
                </el-form-item>
              </el-form>
              <el-col :span="24">
                <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch"
                         label-width="68px">
                  <el-form-item label="" prop="visaByArr">
                    <el-cascader
                      v-model="queryParams.reportNo"
                      :options="deptUserOptions"
                      :props="{
                        multiple: false,
                        value: 'id',
                        emitPath: false
                      }"
                      :show-all-levels="false"
                      placeholder="上报人"
                      filterable clearable style="width: 240px"></el-cascader>
                  </el-form-item>
                  <el-form-item label="" prop="visaByArr">
                    <el-cascader
                      v-model="queryParams.reviewNo"
                      :options="deptUserOptions"
                      :props="{
                        multiple: false,
                        value: 'id',
                        emitPath: false
                      }"
                      :show-all-levels="false"
                      placeholder="审核人"
                      filterable clearable style="width: 240px"></el-cascader>
                  </el-form-item>
                  <el-form-item label="" prop="disCode">
                    <el-input
                        v-model="queryParams.disCode"
                        placeholder="请输入事件编码"
                        clearable
                        style="width: 240px"
                    >
                    </el-input>
                  </el-form-item>
                  <el-form-item label="" prop="routeCode">
                    <el-input
                      v-model="queryParams.routeCode"
                      placeholder="请输入路线编码"
                      clearable
                      style="width: 240px"
                    >
                    </el-input>
                  </el-form-item>
                  <el-form-item label="" prop="direction">
                    <dict-select type="route_direction" clearable
                                 v-model="queryParams.direction" placeholder="请选择路线方向"
                                 style="width: 240px"></dict-select>
                  </el-form-item>
                  <el-form-item label="" prop="lane">
                    <dict-select type="lane" clearable
                                 v-model="queryParams.lane" placeholder="请选择位置"
                                 style="width: 240px"></dict-select>
                  </el-form-item>
                  <el-form-item label="" prop="beginMile">
                    <el-input
                      v-model="queryParams.beginMileStr"
                      placeholder="起点桩号"
                      clearable
                      style="width: 115px"
                    >
                    </el-input>
                    <span style="width: 10px;display: inline-block;text-align: center">~</span>
                    <el-input
                      v-model="queryParams.endMileStr"
                      placeholder="终点桩号"
                      clearable
                      style="width: 115px"
                    >
                    </el-input>
                  </el-form-item>
                  <el-form-item label="" prop="assetMainType">
                    <dict-select type="sys_asset_type" clearable v-model="queryParams.assetTypeStr" placeholder="请选择资产类型"
                                 style="width: 240px" :level="1" only-tail-node></dict-select>
                  </el-form-item>
                  <el-form-item label="" prop="disType">
                    <el-select v-model="queryParams.disType" style="width: 240px" clearable filterable placeholder="请选择事件类型" >
                      <el-option v-for="dict in disType" :key="dict.dictValue"
                                 :label="dict.dictLabel" :value="dict.dictValue">
                      </el-option>
                    </el-select>
                  </el-form-item>
                  <el-form-item label="" prop="disStatus">
                    <dict-select type="dis_status_name" clearable v-model="queryParams.disStatusStr" placeholder="请输入状态"
                                 style="width: 240px"></dict-select>
                  </el-form-item>
<!--                  <el-form-item label="" prop="stage">-->
<!--                    <dict-select type="dis_stage_name" clearable v-model="queryParams.stageStr" placeholder="请输入阶段"-->
<!--                                 style="width: 240px"></dict-select>-->
<!--                  </el-form-item>-->
                  <el-form-item label="" prop="code">
                    <el-input
                      v-model="queryParams.code"
                      placeholder="请输入施工单编码"
                      clearable
                      style="width: 240px"
                    >
                    </el-input>
                  </el-form-item>
                  <el-form-item label="" prop="collectTimes">
                    <el-date-picker
                      v-model="queryParams.collectTimes"
                      format="yyyy-MM-dd"
                      value-format="yyyy-MM-dd HH:mm:ss"
                      style="width: 240px"
                      placeholder="采集开始时间"
                      clearable
                    ></el-date-picker>
                  </el-form-item>
                  <el-form-item label="" prop="collectTimee">
                    <el-date-picker
                      v-model="queryParams.collectTimee"
                      format="yyyy-MM-dd"
                      value-format="yyyy-MM-dd HH:mm:ss"
                      style="width: 240px"
                      placeholder="采集结束时间"
                      clearable
                    ></el-date-picker>
                  </el-form-item>
                  <el-form-item label="" prop="disDesc">
                    <el-input
                        v-model="queryParams.disDesc"
                        placeholder="请输入事件描述"
                        clearable
                        style="width: 240px"
                    >
                    </el-input>
                  </el-form-item>
                  <el-form-item label="" prop="conDomainId">
                    <selectTree
                      :key="'conDomainId'"
                      style="width: 240px"
                      v-model="queryParams.conDomainIdStr" :data-rule="false"
                      :dept-type="100"
                      placeholder="施工单位"
                      :filter-keys="['云南省交通投资建设集团有限公司', '云南交投投资有限公司']"
                      :expand-all="false"
                      clearable
                      filterable
                    />
                  </el-form-item>
                  <el-select v-model="queryParams.disFrom" placeholder="事件来源" clearable>
                    <el-option label="巡查发现" value="巡查发现">
                    </el-option>
                    <el-option label="直接新增" value="直接新增">
                    </el-option>
                  </el-select>
                </el-form>
              </el-col>
            </el-row>
          </el-col>
        </el-row>
        <!--操作按钮区开始-->
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button
              type="primary"
              icon="el-icon-plus"
              size="mini"
              v-has-menu-permi="['disease:disease:add']"
              @click="handleAdd"
            >新增
            </el-button
            >
          </el-col>
          <el-col :span="1.5">
            <el-button
              type="danger"
              icon="el-icon-delete"
              size="mini"
              v-has-menu-permi="['disease:disease:removeBatchByIds']"
              @click="handleBatchDelete"
            >批量删除
            </el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button
              type="warning"
              icon="el-icon-download"
              size="mini"
              v-has-menu-permi="['disease:disease:export']"
              @click="exportList"
            >导出清单
            </el-button
            >
          </el-col>
          <el-col :span="1.5">
            <el-button
              type="success"
              icon="el-icon-view"
              size="mini"
              @click="openDisInfo"
            >事件信息
            </el-button
            >
          </el-col>
          <el-col :span="1.5">
            <el-button
              type="primary"
              v-has-menu-permi="['disease:disease:add']"
              icon="el-icon-copy-document"
              @click="handleCopy"
              size="mini"
            >事件复制
            </el-button
            >
          </el-col>
<!--          <el-col :span="1.5">-->
<!--            <el-button-->
<!--              type="success"-->
<!--              v-has-menu-permi="['disease:disease:add']"-->
<!--              icon="el-icon-upload2"-->
<!--              size="mini"-->
<!--            >导入事件-->
<!--            </el-button-->
<!--            >-->
<!--          </el-col>-->
          <right-toolbar
            :showSearch.sync="showSearch"
            @queryTable="handleQuery"
            :columns="columns"
          ></right-toolbar>
        </el-row>
        <el-row>
          <div class="draggable">
            <el-table v-adjust-table
              size="mini"
              style="width: 100%"
              v-loading="loading"
              border
              :data="tableData"
              ref="dataTable"
              stripe
              highlight-current-row
              @row-click="handleClickRow"
              @selection-change="handleSelectionChange"
              :height="
                showSearch ? 'calc(100vh - 330px)' : 'calc(100vh - 270px)'
              "
            >
              <el-table-column type="selection" width="50" align="center" :selectable="checkSelectable"/>
              <el-table-column
                label="序号"
                align="center"
                type="index"
                width="50"
              />
              <template v-for="(column,index) in columns">
                <el-table-column :label="column.label"
                                 v-if="column.visible"
                                 align="center"
                                 :prop="column.field"
                                 show-overflow-tooltip
                                 :width="column.width">
                  <template slot-scope="scope">
                    <dict-tag v-if="column.dict" :options="dict.type[column.dict]" :value="scope.row[column.field]"/>
                    <template v-else-if="column.slots">
                      <RenderDom :row="scope.row" :index="index" :render="column.render"/>
                    </template>
                    <span v-else-if="column.isTime">{{ parseTime(scope.row[column.field], '{y}-{m}-{d}') }}</span>
                    <span v-else>{{ scope.row[column.field] }}</span>
                  </template>
                </el-table-column>
              </template>
              <el-table-column
                label="操作"
                fixed="right"
                align="center"
                width="160"
                class-name="small-padding fixed-width"
              >
                <template slot-scope="scope">
                  <el-button
                    size="mini"
                    type="text"
                    icon="el-icon-edit"
                    v-has-menu-permi="['disease:disease:edit']"
                    @click="handleUpdate(scope.row)"
                  >编辑
                  </el-button
                  >
                  <el-popover
                    placement="top"
                    width="250"
                    trigger="hover">
                    <div style="text-align: center; margin: 0">
                      <el-button v-has-menu-permi="['disease:disease:saveToTheft']" type="text" size="mini" @click="handleSubmit(scope.row, 36)" :disabled="scope.row.disStatus != 0">提交到被损工程</el-button>
                      <el-button v-has-menu-permi="['disease:disease:saveToTheft']" type="text" size="mini" @click="handleSubmit(scope.row, 37)" :disabled="scope.row.disStatus != 0">提交到被盗工程</el-button>
                    </div>
                    <template #reference>
                      <el-button
                        size="mini"
                        type="text"
                        v-has-menu-permi="['disease:disease:saveToTheft']"
                        :disabled="scope.row.disStatus != 0"
                        icon="el-icon-edit"
                      >操作
                      </el-button>
                    </template>
                  </el-popover>

                  <el-button
                    size="mini"
                    type="text"
                    icon="el-icon-delete"
                    v-has-menu-permi="['disease:disease:delete']"
                    :disabled="!(scope.row.disStatus == 0 && scope.row.stageName == '新增')"
                    @click="handleDelete(scope.row)"
                  >删除
                  </el-button
                  >
                </template>
              </el-table-column>
            </el-table>
            <pagination
              v-show="total>0"
              :total="total"
              :page.sync="queryParams.pageNum"
              :limit.sync="queryParams.pageSize"
              @pagination="handleQuery"
            />
          </div>
        </el-row>
      </el-col>
    </el-row>
    <el-drawer :wrapperClosable="false" :title="drawerTitle" destroy-on-close :visible.sync="drawer" size="70%">
      <detail @close="handleCloseDetail" :row-data="rowData" :select-data="queryParams"></detail>
    </el-drawer>
    <el-dialog title="事件信息" destroy-on-close :visible.sync="dialogVisible" width="80%" v-if="dialogVisible">
      <event-detail :dis-info="rowData" :daliy-id="rowData.daliyId"/>
<!--      <event-info :dis-id="rowData.id" :daliy-id="rowData.daliyId"></event-info>-->
    </el-dialog>
    <el-dialog :title="disTitle" destroy-on-close :visible.sync="openFile" width="80%">
      <file-upload v-model="disFilePath" :forView="true"></file-upload>
    </el-dialog>
    <el-dialog title="提交到" destroy-on-close :visible.sync="openOperate" width="80%">
    </el-dialog>
  </div>
</template>

<script>
import Detail from "./detail.vue";
import {findUserDeptMaintenanceList2} from '@/api/system/maintenanceSection'
import {
	findDiseaseDataList,
	batchDeleteDiseaseData,
	deleteDiseaseData, saveToTheft, getDiseaseDataById
} from '@/api/dailyMaintenance/eventManage/eventData.js'
import selectTree from "@/components/DeptTmpl/selectTree.vue";
import RoadSection from "@/views/baseData/components/roadSection/index.vue";
import moment from "moment";
import {listAllDiseases} from "@/api/patrol/diseases";
import EventDetail from "@/views/dailyMaintenance/component/eventDetail.vue";
import {formatPile} from "@/utils/ruoyi";
import {getTreeStruct} from "@/api/tmpl";
import ConstructionSelect from "@/components/ConstructionSelect/index.vue";

export default {
  name: 'EventData',
  components: {
    ConstructionSelect,
    RoadSection, selectTree,EventDetail,
    Detail,
    RenderDom: {
      functional: true,
      props: {
        row: Object,
        index: Number,
        render: Function
      },
      render(createElement, ctx) {
        const {row, index} = ctx.props
        return ctx.props.render(row, index)
      }
    }
  },
  dicts: ['route_direction', 'lane', 'event_type', 'sys_asset_type'],
  props: [],
  data() {
    return {
      leftTotal: 1,
      showSearch: false,
      queryParams: {
        pageNum: 1,
        pageSize: 50
      },
      total: 0,
      loading: false,
      disType: [],
      // 部门-用户树选项
      deptUserOptions: [],
      disStatusRouteList: {
        1: '/dailyMaintenance/constructionOrder/noticeDraft', // 加入施工单->通知单拟定
        2: '/dailyMaintenance/constructionOrder/noticeReview', // 施工单审核->通知单审核
        3: '/dailyMaintenance/constructionOrder/noticeIssue', // 施工单签发->通知单签发
        4: '/dailyMaintenance/constructionOrder/taskReception', // 施工单接收->施工单位接收
      },
      stageRouteList: {
        1: '/dailyMaintenance/construction/registration',
        2: '/dailyMaintenance/construction/complete',
        3: '/dailyMaintenance/construction/acceptanceApplication',
        4: '/dailyMaintenance/construction/checkDomain',
        5: '/dailyMaintenance/construction/checkSupDomain',
        6: '/dailyMaintenance/construction/acceptanceRegistration',
        '-1': '/dailyMaintenance/construction/suspend',
        '-2': '/dailyMaintenance/construction/suspendPass'
      },
      columns: [
        {key: 0, width: 100, field: 'disStatusName', label: `状态`, visible: true},
        {key: 1, width: 100, field: 'stageName', label: `阶段`, visible: true},
        {key: 22, width: 200, field: 'disCode', label: `事件编码`, visible: true},
        {key: 2, width: 100, field: 'maiSecName', label: `路段名称`, visible: true},
        {key: 3, width: 100, field: 'routeCode', label: `路线编码`, visible: true},
        {key: 23, width: 200, field: 'subRouteCode', label: `养护子段`, visible: true},
        {key: 4, width: 100, field: 'domainName', label: `管养单位`, visible: true},
        {key: 5, width: 100, field: 'assetMainTypeName', label: `资产类型`, visible: true},
        {key: 6, width: 100, field: 'assetName', label: `资产`, visible: true},
        {key: 7, width: 200, field: 'name', label: `施工单`, visible: true},
        {
          key: 8,
          width: 200,
          field: 'code',
          label: `施工编号`,
          visible: true,
          slots: true,
          render: (row, index) => {
            if (row.code) {
              return (
                <el-link type="primary" style="font-size: 12px;" onClick={()=> this.routerChange(row)}>{row.code}</el-link>
              )
            } else {
              return ''
            }
          }
        },
        {key: 9, width: 100, field: 'beginMileShow', label: `起点桩号`, visible: true},
        {key: 10, width: 100, field: 'endMileShow', label: `终点桩号`, visible: true},
        {key: 11, width: 100, field: 'direction', label: `上下行`, visible: true, dict: 'route_direction'},
        {key: 12, width: 100, field: 'lane', label: `位置`, visible: true, dict: 'lane'},
        {key: 13, width: 100, field: 'disFrom', label: `事件来源`, visible: true},
        // {key: 14, width: 100, field: 'disSort', label: `事件分类`, visible: true, dict: 'event_type'},
        {key: 15, width: 150, field: 'disTypeName', label: `事件类型`, visible: true},
        {
          key: 16,
          width: 100,
          field: 'disFilePath',
          label: `附件`,
          visible: true,
          slots: true,
          render: (row, index) => {
            return (
              <el-button
                size="mini"
                disabled={!row.disFilePath}
                type="text" onClick={e => this.handleOpenFile(e, row)}>查看</el-button>
            )
          }
        },
        {
          key: 16,
          width: 100,
          field: 'disPicPath',
          label: `采集图片`,
          visible: true,
          slots: true,
          render: (row, index) => {
            return (
              <el-button
                size="mini"
                disabled={!row.disPicPath}
                type="text" onClick={e => this.handleOpenImage(e, row)}>查看</el-button>
            )
          }
        },
        {key: 17, width: 100, field: 'disDesc', label: `事件描述`, visible: true},
        {key: 18, width: 100, field: 'updateByName', label: `操作人`, visible: true},
        {key: 19, width: 100, field: 'reportName', label: `上报人`, visible: true},
        {key: 20, width: 100, field: 'reviewName', label: `审核人`, visible: true},
        {key: 21, width: 150, field: 'collectTime', label: `采集时间`, visible: true}
      ],
      tableData: [],
      rowData: {},
      drawerTitle: '新增事件',
      drawer: false,
      openFile: false,
      openOperate: false,
      dialogVisible: false,
      selectIds: [],
      clickRow: {},
      // 左侧组织树
      relaNav: true,
      disTitle: '附件查看',
      disFilePath: '',
      keyword: '',
      relaName: '',
      relaOptions: [],
      filteredTreeData: [],

    }
  },
  computed: {},
  watch: {
    'queryParams.assetTypeStr': function (val) {
      this.changeAsset(val)
    },
  },
  created() {
    this.getDeptTreeDef()
    this.getDeptTree()
    this.handleQuery()
    this.getDisType()
  },
  mounted() {
  },
  methods: {
    /** 查询部门-用户下拉树结构 */
    getDeptTreeDef() {
      getTreeStruct({types: 111}).then(response => {
        this.deptUserOptions = response.data;
      });
    },
    // 关键词检索
    handleSearch() {
      const keyword = this.keyword.toLowerCase();
      this.filteredTreeData = this.relaOptions.filter(node => this.filterNode(node, keyword));
    },
    // 筛选节点
    filterNode(node, keyword) {
      if (node.label.indexOf(keyword) != -1) {
        return true;
      }
      if (node.children) {
        return node.children.some(childNode => this.filterNode(childNode, keyword));
      }
      return false;
    },
    // 查询部门下拉树结构
    getDeptTree() {
      findUserDeptMaintenanceList2().then(response => {
        const treeData = response.data
        treeData.forEach(item => {
          getChild(item)
        })

        function getChild(node) {
          node.label = node.deptName || node.maintenanceSectionName
          node.id = node.deptId || node.maintenanceSectionId
          if (node.children) {
            node.children.forEach(item => {
              getChild(item)
            })
          }
        }

        // 增加一个最顶级
        const tree = [
          {
            label: '云南省交通投资建设集团有限公司',
            id: '1',
            deptId: '1',
            children: [...treeData]
          }
        ]
        this.relaOptions = tree
        this.filteredTreeData = [...this.relaOptions]
      });
    },
    handleNodeClick(e) {
      this.queryParams.domainId = ''
      this.queryParams.domainName = ''
      this.queryParams.maiSecName = ''
      this.queryParams.maiSecId = ''
      this.queryParams.domainIdStr = ''
      if (e.deptId) {
        this.queryParams.domainIdStr = String(e.id)
        this.queryParams.domainId = parseInt(e.id)
        this.queryParams.domainName = e.label
      } else {
        this.queryParams.domainIdStr = String(e.departmentId)
        this.queryParams.domainId = parseInt(e.departmentId)
        this.queryParams.domainName = e.departmentName
        this.queryParams.maiSecName = e.label
        this.queryParams.maiSecId = e.id
      }
      this.handleQuery()
    },
    // 选中
    handleSelectionChange(e) {
      this.selectIds = e.map(obj => obj.id)
    },
    // 查询
    handleQuery() {
      this.loading = true
      this.queryParams.year = parseInt(this.queryParams.yearStr) || null
      this.queryParams.domainId = parseInt(this.queryParams.domainIdStr) || null
      this.queryParams.endMile = parseInt(this.queryParams.endMileStr) || null
      this.queryParams.beginMile = parseInt(this.queryParams.beginMileStr) || null
      this.queryParams.assetMainType = parseInt(this.queryParams.assetTypeStr) || null
      this.queryParams.disStatus = parseInt(this.queryParams.disStatusStr)
      this.queryParams.stage = parseInt(this.queryParams.stageStr)
      this.queryParams.conDomainId = parseInt(this.queryParams.conDomainIdStr)
      if (this.queryParams.domainId == 1) {
        this.queryParams.domainId = null
      }
      const endTime = this.queryParams.collectTimee ? moment(this.queryParams.collectTimee).endOf("day").format('YYYY-MM-DD HH:mm:ss') : null
      this.$set(this.queryParams, 'collectTimee', endTime)
      findDiseaseDataList(this.queryParams).then(res => {
        if (res.code == 200) {
          this.loading = false
          res.rows.forEach(item => {
            item.beginMileShow = formatPile(item.beginMile)
            item.endMileShow = formatPile(item.endMile)
          })
          this.tableData = res.rows
          this.total = res.total
        }
      })
    },
    // 修改
    handleUpdate(e) {
      if (e.stageName != '新增') {
        let message = '该事件已加入任务单,是否继续编辑?'
        if (e.stageName == '加入中间计量单') {
          message = '该事件已加入中间计量单,是否继续编辑?'
        }
        if (e.stageName == '加入结算计量单') {
          message = '该事件已加入结算计量单,是否继续编辑?'
        }
        if (e.stageName == '已结算') {
          message = '该事件已结算,是否继续编辑?'
        }
        this.$modal.confirm(message).then(() => {
          this.drawerTitle = '修改事件'
          this.rowData = e
          this.drawer = true
        })
      } else {
        this.drawerTitle = '修改事件'
        this.rowData = e
        this.drawer = true
      }

    },
    // 新增
    handleAdd() {
      this.drawerTitle = '新增事件'
      this.rowData = {}
      this.drawer = true
    },
    // 删除
    handleDelete(e) {
      this.$modal.confirm('是否确认删除').then(() => {
        deleteDiseaseData(e.id).then(res => {
          if (res.code == 200) {
            this.$modal.msgSuccess('删除成功')
            this.handleQuery()
          }
        })
      })
    },
    checkSelectable(row) {
      return row.disStatus == 0 && row.stageName == '新增'
    },
    handleBatchDelete() {
      if (this.selectIds.length <= 0) {
        this.$modal.warning('请至少勾选一条数据')
        return
      }
      this.$modal.confirm('是否确认删除所选的数据').then(() => {
        batchDeleteDiseaseData(this.selectIds).then(res => {
          this.handleQuery();
          this.$modal.msgSuccess("删除成功");
        })
      })
    },
    resetQuery() {
      this.queryParams = {
        pageNum: 1,
        pageSize: 50
      }
      this.handleQuery()
    },
    getDisType() {
      listAllDiseases().then(res => {
        this.disType = res.data.map(item => {
          return {
            dictLabel: item.diseaseName,
            dictValue: item.id
          }
        })
      })
    },
    handleCloseDetail() {
      this.rowData = {}
      this.drawer = false
      this.handleQuery()
    },
    handleOpenFile(e, row) {
      this.disTitle = '附件查看'
      this.disFilePath = ''
      this.openFile = true
      this.disFilePath = row.disFilePath
    },
    handleOpenImage(e, row) {
      this.disTitle = '采集照片查看'
      this.disFilePath = ''
      this.openFile = true
      this.disFilePath = row.disPicPath
    },
    // 导出清单按钮
    exportList() {
      this.download(
        'manager/disease/export',
        {...this.queryParams, idList: this.selectIds},
        `disease_${new Date().getTime()}.xlsx`,
        {
          headers: { 'Content-Type': 'application/json;' },
          parameterType: 'body'
        }
      )
    },
    handleClickRow(e) {
      if(e.disStatus == 0 && e.stageName == '新增') {
          this.clickRow = e
          e.isSelected = !e.isSelected;
          this.$refs.dataTable.toggleRowSelection(e);
      }
      this.rowData = Object.assign({}, e)
    },
    handleSubmit(row, status) {
      if (status === 1) {
        this.$modal.confirm('是否将该事件作为被损工程提交到被损被盗项目中?').then(() => {
          row.mtype = status
          saveToTheft(row).then(res => {
            if (res.code == 200) {
              this.$modal.msgSuccess('提交成功')
              this.handleQuery()
            }
          })
        })
      } else {
        this.$modal.confirm('是否将该事件作为被盗工程提交到被损被盗项目中?').then(() => {
          row.mtype = status
          saveToTheft(row).then(res => {
            if (res.code == 200) {
              this.$modal.msgSuccess('提交成功')
              this.handleQuery()
            }
          })
        })
      }
    },
    openDisInfo(row) {
      if (!this.rowData.id) {
        this.$message.warning('请选择一条事件')
        return
      }
      this.dialogVisible = true
      this.disId = row.id
    },
    changeAsset(e) {
      this.$set(this.queryParams, 'disType', '')
      this.disType = []
      if (e) {
        listAllDiseases({assetType: e}).then(res => {
          this.disType = res.data.map(item => {
            return {
              dictLabel: item.diseaseName,
              dictValue: item.id
            }
          })
        })
      }
    },
    handleCopy() {
      if (!this.rowData.id) {
        this.$message.warning('请选择一条事件')
        return
      }
			this.loading = true
	    // 查询详情
	    getDiseaseDataById(this.rowData.id).then(res => {
				this.loading = false
		    this.rowData.schemeList = res.data.schemeList || []
		    this.rowData.id = null
		    this.rowData.disCode = null
		    this.drawerTitle = '新增事件'
		    this.drawer = true
	    })

    },
    routerChange(row) {
      let routeInfo = {
        path: '/dailyMaintenance/construction/constructionSituation',
        query: {code: row.code}
      }
      // if (row.stage) {
      //   if (row.stage <= 6) {
      //     routeInfo.path = this.stageRouteList[row.stage]
      //   } else {
      //     routeInfo.path = '/dailyMaintenance/construction/constructionSituation'
      //   }
      //   routeInfo.query = {
      //     code: row.code
      //   }
      // } else {
      //   routeInfo.path = this.disStatusRouteList[row.disStatus]
      //   routeInfo.query = {
      //     code: row.code
      //   }
      // }
      this.$router.push(routeInfo)
    }
  }
}
</script>

<style lang="scss" scoped>
.leftDiv {
  border-right: 1px solid #d8dce5;
  min-height: calc(100vh - 100px);
  overflow-y: auto;
  height: calc(80vh - 150px);
  position: relative;
  top: -20px;
  padding-top: 10px;
  background-color: white;
}

.leftIcon {
  border: 1px solid #dcdfe6;
  border-radius: 8px;
  width: 16px;
  height: 50px;
  line-height: 50px;
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
  position: absolute;
  right: 0;
  top: 300px;
  z-index: 2;
}

.leftIcon:hover {
  background-color: #dcdfe6;
}

.rightIcon {
  border: 1px solid #dcdfe6;
  border-radius: 8px;
  width: 16px;
  height: 50px;
  line-height: 50px;
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
  position: absolute;
  left: -10px;
  top: 280px;
  z-index: 10;
  background: white;
}

.rightIcon:hover {
  background-color: #dcdfe6;
}

.left-total {
  font-size: 13px;
  line-height: 28px;
  color: #606266;
  position: absolute;
  bottom: 10px;
  right: 10px;
}
::v-deep .el-form-item--small .el-form-item__content {
  height: 32px !important;
}
</style>
<style lang="scss" scoped>
@import "@/assets/styles/business.scss";
</style>
