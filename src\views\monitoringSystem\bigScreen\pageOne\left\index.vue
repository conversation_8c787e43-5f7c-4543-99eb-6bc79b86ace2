<template>
  <div class="body-left">
    <Top :title="topTitle" />
    <Mid :title="midTitle" />
    <Bottom :title="bottomTitle" />
  </div>
</template>

<script>
// import { getStrutureInfoByCode } from '@/api/monitoringSystem/index.js'
import Top from "./Top.vue"
import Mid from './Mid.vue'
import Bottom from './Bottom.vue'

export default {
  name: 'Left-index',
  inject: ['iThis'],
  props: {},
  components: { Top, Mid, Bottom },
  data() {
    return {
      // topTitle: '桥梁基础信息'
    }
  },
  created() {
    // const bridgeInfo = getStrutureInfoByCode()
  },
  methods: {
  },
  computed: {
    topTitle() {
      return this.iThis.params.type + '基础信息'
    },
    midTitle() {
      let type = this.iThis.params.type;
      return type === '桥梁' ? '挠度' : type === '隧道' ? '拱腰位移' : '降雨量'
    },
    bottomTitle() {
      let type = this.iThis.params.type;
      return type === '桥梁' ? '桥墩倾斜' : type === '隧道' ? '拱顶位移' : '拱顶位移'
    }
  },
  watch: {},
}
</script>

<style lang="scss" scoped>
.body-left {
  position: relative;
  z-index: 999;
  height: 100%;
  width: 24.5%;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}
</style>