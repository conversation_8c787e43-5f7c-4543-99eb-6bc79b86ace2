<template>
  <div class="maintenance-index">
    <el-row :gutter="10">
      <el-col :xs="6" :sm="6" :md="6" :lg="6" :xl="6">
        <CockpitCard title="养护工程项目" :isDtl="false" w="100%" :h="isBig ? '27vh' : 'calc(24vh - 4px)'" class="mt-1">
          <ProjectOverview :year="year" />
        </CockpitCard>
        <CockpitCard title="工程类别统计" :isDtl="false" w="100%" :h="isBig ? '27vh' : 'calc(24vh - 4px)'" class="mt-1">
          <PieECharts :data="lbObj.data" :color="lbObj.color" title="工程类别" key="lbKey" :year="year" type="4" />
        </CockpitCard>
        <CockpitCard title="养护项目立项趋势" :isDtl="false" w="100%" :h="isBig ? '31vh' : 'calc(30vh - 4px)'" class="mt-1">
          <ProjectProposalTrend :year="year" />
        </CockpitCard>
      </el-col>
      <el-col :xs="12" :sm="12" :md="12" :lg="12" :xl="12">
        <CockpitCard title="施工情况" :isDtl="false" w="100%" :h="isBig ? 'calc(58vh + 5px)' : 'calc(52vh + 1px)'" class="mt-1">
          <ConstructionProgress :year="year" />
          <template slot="more">
            <el-select v-model="year" placeholder="请选择年份" popper-class="select-popper" @change="onChange"
              :popper-append-to-body="false">
              <el-option v-for="item in years" :key="item" :label="item + '年'" :value="item"></el-option>
            </el-select>
          </template>
        </CockpitCard>
        <CockpitCard title="各管养单位资金使用情况" :isDtl="false" w="100%" :h="isBig ? '31vh' : 'calc(30vh - 4px)'" class="mt-1">
          <UnitFunds :year="year" type='2' />
        </CockpitCard>
      </el-col>
      <el-col :xs="6" :sm="6" :md="6" :lg="6" :xl="6">
        <CockpitCard title="专项养护资金使用情况" :isDtl="false" w="100%" :h="isBig ? '27vh' : 'calc(24vh - 4px)'" class="mt-1">
          <BudgetAndActual :dataObj="baDataObj" :year="year" type="2" />
          <template slot="more">
            <img src="@/assets/cockpit/back.png" @click="$emit('back')" style="width:118px;height:54px" v-if="isBig" />
          </template>
        </CockpitCard>
        <CockpitCard title="工程类型费用占比" :isDtl="false" w="100%" :h="isBig ? '27vh' : 'calc(24vh - 4px)'" class="mt-1">
          <PieECharts :data="lxObj.data" :color="lxObj.color" title="工程类型" :key="'lxKey'" :year="year" type="5" />
        </CockpitCard>
        <CockpitCard title="养护工程预算趋势对比" :isDtl="false" w="100%" :h="isBig ? '31vh' : 'calc(30vh - 4px)'" class="mt-1">
          <BudgetTrends :year="year" />
        </CockpitCard>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import { isBigScreen } from "../../util/utils";
import CockpitCard from '../cockpitCard.vue';
import UnitFunds from './component/unitFunds.vue';
import BudgetAndActual from './component/budgetAndActual.vue';
import PieECharts from './component/pieECharts.vue';
import BudgetTrends from './component/budgetTrends.vue';
import ProjectProposalTrend from './component/projectProposalTrend.vue';
import ProjectOverview from './component/projectOverview.vue';
import ConstructionProgress from './component/constructionProgress.vue';

export default {
  components: {
    CockpitCard,
    UnitFunds,
    BudgetAndActual,
    PieECharts,
    BudgetTrends,
    ProjectProposalTrend,
    ProjectOverview,
    ConstructionProgress
  },
  data() {
    return {
      isBig: isBigScreen(),
      lbObj: {
        data: [
          {
            name: '桥梁工程',
            value: 1627,
            unit: '件'
          },
          {
            name: '路基工程',
            value: 230,
            unit: '件'
          },
          {
            name: '路面工程',
            value: 230,
            unit: '件'
          },
          {
            name: '隧道工程',
            value: 1627,
            unit: '件'
          },
          {
            name: '机电工程',
            value: 1627,
            unit: '件'
          },
          {
            name: '交安工程',
            value: 230,
            unit: '件'
          },
          {
            name: '绿化工程',
            value: 230,
            unit: '件'
          },
          {
            name: '房建工程',
            value: 230,
            unit: '件'
          }
        ],
        color: ['#0054B5', '#008DEC', '#01FBEF', '#FFC721', '#3851E6', '#886EFF', '#2CDB44', '#94D632'],
      },
      lxObj: {
        data: [
          {
            name: '桥梁工程',
            value: 1627,
            unit: '万元'
          },
          {
            name: '路基工程',
            value: 230,
            unit: '万元'
          },
          {
            name: '路面工程',
            value: 230,
            unit: '万元'
          },
          {
            name: '隧道工程',
            value: 1627,
            unit: '万元'
          },
          {
            name: '机电工程',
            value: 1627,
            unit: '万元'
          },
          {
            name: '交安工程',
            value: 230,
            unit: '万元'
          },
          {
            name: '绿化工程',
            value: 230,
            unit: '万元'
          },
          {
            name: '房建工程',
            value: 230,
            unit: '万元'
          }
        ],
        color: ['#0054B5', '#008DEC', '#01FBEF', '#FFC721', '#3851E6', '#886EFF', '#2CDB44', '#94D632'],
      },
      baDataObj: {
        unit: 'K',
        xData: ['预防养护', '专项养护', '修复养护', '应急养护'],
        budget: [1.2, 2.2, 1.4, 2.3],
        actual: [2.5, 4.5, 2.4, 4.6],
      },
      years: [],
      year: '',
    }
  },
  mounted() {
    this.getYear();
  },
  methods: {
    getYear() {
      let date = new Date();
      let year = date.getFullYear();
      this.year = year;
      for (let i = year - 10; i < year + 1; i++) {
        this.years.push(i);
      }
    },
    onChange(value) {
      this.year = value;
      window.$Bus.$emit("onChangeYear", value);
    },
  },
}
</script>

<style lang="scss" scoped>
.maintenance-index {
  width: 100%;
  height: 100%;
  padding: 0 10px;

  .mt-1 {
    margin-top: 0.6rem;
  }

  ::v-deep .el-input {
    .el-input__inner {
      height: vwpx(300px);
      height: vwpx(60px);
      background-color: rgba(1, 102, 254, 0.2);
      border: 1px solid #0166fe;
      color: #ffffff;
      font-size: vwpx(30px);
    }

    .el-input__inner::placeholder {
      color: #bbbbbb;
    }

    .el-input-group__append {
      background-color: rgba(1, 102, 254, 0.2);
      border: 1px solid #0166fe;
      color: #ffffff;
      border-left: none;
      padding: 0 10px;
      cursor: pointer;
    }
  }

  ::v-deep .select-popper {
    background-color: rgba(1, 102, 254, 0.2);
    border: 1px solid #0166fe;
    color: #ffffff !important;
    font-size: vwpx(30px);
    margin: vwpx(10px) 0;
    // 添加以下样式
    position: absolute !important; // 确保使用绝对定位
    z-index: 2000; // 确保下拉框在其他元素之上
    top: 3vh !important;

    .popper__arrow {
      position: absolute;
      top: -0.7vh;
    }
  }

  ::v-deep .el-select-dropdown__item {
    color: #ffffff !important;
    font-size: vwpx(30px);
    margin: vwpx(15px) 0;

    &:hover {
      background-color: rgba(1, 102, 254, 0.2);
    }

    &.is-focus {
      background-color: rgba(1, 102, 254, 0.2);
    }
  }

  ::v-deep .el-select-dropdown__item.selected {
    color: #42abff !important;
  }
}
</style>