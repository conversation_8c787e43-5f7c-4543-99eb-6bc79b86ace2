  <template>
  <div class="roadbedadd">
    <el-dialog
      :title="title"
      :visible.sync="showAddEdit"
      width="60%"
      append-to-body
      :before-close="handleClose"
      :close-on-click-modal="false"
      :class="'roadbedDialog'"
    >
          <div style="height: 60vh;overflow-y: auto;padding: 0 10px 0 5px;">
            <div class="check-edit-title">路面评定信息</div>
            <el-button @click="getRoadbed" type="primary" size="mini" style="margin: 0 35px" v-if="title=='查看路面评定单元数据'?false:true">
              选择路面评定
            </el-button>
            <el-form
              ref="baseInfoData"
              :model="baseInfoForm"
               label-width="210px" size="small" style="margin: 10px"

              :disabled="title=='查看路面评定单元数据'?true:false"
            >
              <div style="display: flex; flex-wrap: wrap">
                <el-col
                  :span="i.span || 12"
                  v-for="(i,idx) in baseInfoData.slice(0, 9)"
                  :key="idx"
                >
                  <el-form-item
                    :label="i.label"
                    :prop="i.prop"
                    :rules="i.rules"
                  >
                    <span v-if="i.type === 'input'" >
                      <el-input
                        v-model="baseInfoForm[i.prop]"
                        :disabled="i.disabled?i.disabled:false"
                        :placeholder="i.placeholder"
                        clearable

                      />
                    </span>

                    <span v-if="i.type === 'pileInput'">
                      <PileInput
                        v-model="baseInfoForm[i.prop]"
                        :placeholder="i.placeholder"
                      />
                    </span>
                    <span v-else-if="i.type === 'inputNumber'">
                      <el-input-number
                        style="width: 100%"
                        v-model="baseInfoForm[i.prop]"

                        :precision="i.precision"
                        @change="changeInputNumber(i.precision,'baseInfoForm',i.prop)"
                        clearable
                      ></el-input-number>
                    </span>
                    <span v-else-if="i.type === 'select'">
                      <div v-if="i.dict">
                        <el-select
                          style="width: 100%"
                          v-model="baseInfoForm[i.prop]"
                          placeholder="请选择"
                          :disabled="i.disabled?i.disabled:false"
                          clearable
                          @change="changeSelect($event, i)"
                        >
                          <el-option
                            v-for="dict in dict.type[i.dict]"
                            :key="dict.value"
                            :label="dict.label"
                            :value="dict.value"
                          >
                          </el-option>
                        </el-select>
                      </div>
                      <div v-else-if="i.deptType">
                        <SelectTree  v-model="baseInfoForm[i.prop]" :dept-type="i.deptType"  clearable :disabled="i.disabled?i.disabled:false"/>
                      </div>
                      <div v-else>
                        <el-select
                          style="width:100%"
                          v-model="baseInfoForm[i.prop]"
                          :placeholder="i.placeholder"
                          clearable
                          filterable
                          :disabled="(baseInfoForm&&baseInfoForm.id)?false:i.disabled"

                        >
                          <el-option
                            v-for="(v,index) in i.options"
                            :key="index"
                            :label="v[i.optionLabel]"
                            :value="v[i.optionValue]"
                          />
                        </el-select>
                      </div>
                    </span>
                    <span v-else-if="i.type === 'date'">
                      <el-date-picker
                        style="width:100%"
                        v-model="baseInfoForm[i.prop]"

                        :placeholder="i.placeholder"
                        clearable
                        value-format="yyyy-MM-dd"
                      />
                    </span>
                    <span v-else-if="i.type === 'year'">
                      <el-date-picker
                        style="width:100%"
                        v-model="baseInfoForm[i.prop]"
                        :type="'year'"
                        :placeholder="i.placeholder"
                        clearable
                        value-format="yyyy"
                      />
                    </span>
                    <span v-else-if="i.type === 'inputUser'">
                      <el-cascader
                        ref="userCascade"
                        v-model="oprUser"
                        :options="deptUserOptions"
                        :props="{ multiple: true, value: 'id', emitPath: false}"
                        :show-all-levels="false"
                        filterable clearable
                        style="width: 100%"/>
                    </span>
                    <span v-else-if="i.type === 'updataFile'">
                      <FileUpload
                        :key="ownerId"
                        v-model="form.reportPath"
                        :limit="1"
                        :owner-id="ownerId"
                        storage-path="/base/roadbed/periodic"
                        platform="mpkj"
                      />
                    </span>
                  </el-form-item>
                </el-col>
              </div>
            </el-form>
            <div class="check-edit-title">路面评定项目</div>
            <el-button @click="getProject" type="primary" size="mini" style="margin: 0 35px" v-if="title=='查看路面评定单元数据'?false:true">
              选择项目
            </el-button>
            <el-form
              ref="baseInfoData"
              :model="baseInfoForm"
               label-width="210px" size="small" style="margin: 10px"

              :disabled="title=='查看路面评定单元数据'?true:false"
            >
              <div style="display: flex; flex-wrap: wrap">
                <el-col
                  :span="i.span || 12"
                  v-for="(i,idx) in baseInfoData.slice(9,12)"
                  :key="idx"
                >
                  <el-form-item
                    :label="i.label"
                    :prop="i.prop"
                    :rules="i.rules"
                  >
                    <span v-if="i.type === 'input'" >
                      <el-input
                        v-model="baseInfoForm[i.prop]"
                        :disabled="i.disabled?i.disabled:false"
                        :placeholder="i.placeholder"
                        clearable

                      />
                    </span>
                    <span v-else-if="i.type === 'year'">
                      <el-date-picker
                        style="width:100%"
                        v-model="baseInfoForm[i.prop]"
                        :disabled="i.disabled?i.disabled:false"
                        :type="'year'"
                        :placeholder="i.placeholder"
                        clearable
                        value-format="yyyy"
                      />
                    </span>
                    <span v-else-if="i.type === 'date'">
                      <el-date-picker
                          style="width:100%"
                          v-model="baseInfoForm[i.prop]"
                          type="date"
                          :placeholder="i.placeholder"
                          clearable
                          value-format="yyyy-MM-dd"
                      />
                    </span>
                  </el-form-item>
                </el-col>
              </div>
            </el-form>
            <div class="check-edit-title">路面技术评定结果</div>


            <el-form
              ref="baseInfoData"
              :model="baseInfoForm"
               label-width="210px" size="small" style="margin: 10px"

              :disabled="title=='查看路面评定单元数据'?true:false"
            >
              <div style="display: flex; flex-wrap: wrap">
                <el-col
                  :span="i.span || 12"
                  v-for="(i,idx) in baseInfoData.slice(12,)"
                  :key="idx"
                >
                  <el-form-item
                    :label="i.label"
                    :prop="i.prop"
                    :rules="i.rules"
                  >
                    <span v-if="i.type === 'input'" >
                      <el-input
                        v-model="baseInfoForm[i.prop]"
                        :disabled="i.disabled?i.disabled:false"
                        :placeholder="i.placeholder"
                        clearable

                      />
                    </span>

                    <span v-if="i.type === 'pileInput'">
                      <PileInput
                        v-model="baseInfoForm[i.prop]"
                        :placeholder="i.placeholder"
                      />
                    </span>
                    <span v-else-if="i.type === 'inputNumber'">
                      <el-input-number
                        style="width: 100%"
                        v-model="baseInfoForm[i.prop]"

                        :precision="i.precision"
                        @change="changeInputNumber(i.precision,'baseInfoForm',i.prop)"
                        clearable
                      ></el-input-number>
                    </span>
                    <span v-else-if="i.type === 'select'">
                      <div v-if="i.dict">
                        <el-select
                          style="width: 100%"
                          v-model="baseInfoForm[i.prop]"
                          placeholder="请选择"
                          :disabled="i.disabled?i.disabled:false"
                          clearable
                          @change="changeSelect($event, i)"
                        >
                          <el-option
                            v-for="dict in dict.type[i.dict]"
                            :key="dict.value"
                            :label="dict.label"
                            :value="dict.value"
                          >
                          </el-option>
                        </el-select>

                      </div>
                      <div v-else-if="i.deptType">
                        <SelectTree  v-model="baseInfoForm[i.prop]" :dept-type="i.deptType"  clearable />
                      </div>
                      <div v-else>
                        <el-select
                          style="width:100%"
                          v-model="baseInfoForm[i.prop]"
                          :placeholder="i.placeholder"
                          clearable
                          filterable
                          :disabled="(baseInfoForm&&baseInfoForm.id)?false:i.disabled"

                        >
                          <el-option
                            v-for="(v,index) in i.options"
                            :key="index"
                            :label="v[i.optionLabel]"
                            :value="v[i.optionValue]"
                          />
                        </el-select>
                      </div>
                    </span>
                    <span v-else-if="i.type === 'date'">
                      <el-date-picker
                        style="width:100%"
                        v-model="baseInfoForm[i.prop]"
                        type="date"
                        :placeholder="i.placeholder"
                        clearable
                        value-format="yyyy-MM-dd"
                      />
                    </span>
                    <span v-else-if="i.type === 'year'">
                      <el-date-picker
                        style="width:100%"
                        v-model="baseInfoForm[i.prop]"
                        :type="'year'"
                        :placeholder="i.placeholder"
                        clearable
                        value-format="yyyy"
                      />
                    </span>
                    <span v-else-if="i.type === 'inputUser'">
                      <el-cascader
                        ref="userCascade"
                        v-model="oprUser"
                        :options="deptUserOptions"
                        :props="{ multiple: true, value: 'id', emitPath: false}"
                        :show-all-levels="false"
                        filterable clearable
                        style="width: 100%"/>
                    </span>
                    <span v-else-if="i.type === 'updataFile'">
                      <FileUpload
                        :key="ownerId"
                        v-model="form.reportPath"
                        :limit="1"
                        :owner-id="ownerId"
                        storage-path="/base/roadbed/periodic"
                        platform="mpkj"
                      />
                    </span>
                  </el-form-item>
                </el-col>
              </div>
            </el-form>
          </div>
      <div
        slot="footer"
        class="dialog-footer"
      >
        <el-button
          v-if="title=='查看路面评定单元数据'?false:true"
          type="primary"
          @click="handleSubmit('submit')"
        >提 交</el-button>
        <el-button @click="handleClose">取 消</el-button>
      </div>
    </el-dialog>
    <el-dialog
      title="选择路面"
      :visible.sync="showRoadbedSelect"
      width="80%"
      append-to-body
      :before-close="handleRoadbedClose"
      :close-on-click-modal="false"
    >
    <div style="height: 70vh;">
    <div style="display: flex; align-items: center; margin: 0">

        <CascadeSelection
          :form-data="queryParams"
          v-model="queryParams"
          types="201"
          multiple
        />
        <el-select
          style="margin-left: 15px;"
          v-model="queryParams.routeLevel"
          placeholder="路线技术等级"
          clearable
          multiple
          collapse-tags
        >
          <el-option
            v-for="dict in dict.type.sys_route_grade"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
        <el-select
          style="margin-left: 15px;"
          v-model="queryParams.pavType"
          placeholder="路面类型"
          clearable
        >
          <el-option
            v-for="dict in dict.type.sys_surface_type"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
        <el-select
          style="margin:0 15px;"
          v-model="queryParams.hasRecords"
          placeholder="是否有养护记录"
          clearable
          collapse-tags
        >
          <el-option
            v-for="dict in dict.type.base_data_yes_no"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
        <div style="min-width:220px">
          <el-button
            type="primary"
            icon="el-icon-search"
            size="mini"
            @click="handleQuery"
          >搜索</el-button>
          <el-button
            style="background:#F2F3F5;border:1px #F2F3F5 solid"
            icon="el-icon-refresh"
            size="mini"
            @click="resetQuery"
          >重置</el-button>
        </div>

      </div>
      <el-divider></el-divider>
      <el-table
        ref="table"
        v-loading="loading"
        height="80%"
        style="width: 100%"
        :data="staticList"
        :row-style="rowStyle"
        @row-click="handleRowClick"
      >

        <el-table-column
          fixed
          label="序号"
          type="index"
          width="50"
          align="center"
        />
        <el-table-column
          label="管理处名称"
          align="center"
          prop="managementMaintenanceName"
          min-width="120"
          show-overflow-tooltip
        />
        <el-table-column
          label="路线编码"
          align="center"
          prop="routeCode"
          min-width="120"
          show-overflow-tooltip
        ></el-table-column>
        <el-table-column
          label="路线全称"
          align="center"
          prop="routeName"
          min-width="120"
          show-overflow-tooltip
        ></el-table-column>
        <el-table-column
          label="养护路段名称"
          align="center"
          prop="maintenanceSectionName"
          min-width="120"
          show-overflow-tooltip
        ></el-table-column>
        <el-table-column
          label="调查方向"
          align="center"
          prop="direction"
          min-width="120"
          show-overflow-tooltip
        >
          <template slot-scope="scope">
            <dict-tag
              :options="dict.type.sys_route_direction"
              :value="scope.row.direction"
            />
          </template>
        </el-table-column>
        <el-table-column
          label="具体方向"
          align="center"
          prop="specificDirection"
          min-width="120"
          show-overflow-tooltip
        ></el-table-column>
        <el-table-column
          label="路线技术等级"
          align="center"
          prop="routeLevel"
          min-width="120"
          show-overflow-tooltip
        >
          <template slot-scope="scope">
            <dict-tag
              :options="dict.type.sys_route_grade"
              :value="scope.row.routeLevel"
            />
          </template>
        </el-table-column>
        <el-table-column
          label="路面类型"
          align="center"
          prop="pavType"
          min-width="120"
          show-overflow-tooltip
        >
          <template slot-scope="scope">
            <dict-tag
              :options="dict.type.sys_surface_type"
              :value="scope.row.pavType"
            />
          </template>
        </el-table-column>
        <el-table-column
          label="评定长度"
          align="center"
          prop="ratingLength"
          min-width="120"
          show-overflow-tooltip
        ></el-table-column>
        <el-table-column
          label="起点桩号"
          align="center"
          prop="startStake"
          min-width="120"
          show-overflow-tooltip
        >
          <template slot-scope="scope">
            <span v-if="scope.row && scope.row.startStake">
              {{ formatPile(scope.row.startStake) }}
            </span>
          </template>
        </el-table-column>
        <el-table-column
          label="终点桩号"
          align="center"
          prop="endStake"
          min-width="120"
          show-overflow-tooltip
        >
          <template slot-scope="scope">
            <span v-if="scope.row && scope.row.endStake">
              {{ formatPile(scope.row.endStake) }}
            </span>
          </template>
        </el-table-column>

      </el-table>
      <pagination
        :total="total"
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
        :pageSizes="[10, 20, 30, 50, 100, 1000]"
        @pagination="getList"
      />
    </div>
    </el-dialog>

    <projectSelect :showProjectSelect="showProjectSelect" @close="()=>showProjectSelect=false" @projectClick="projectClick"></projectSelect>
  </div>
</template>

<script>
import dictionary from '../dataDictionary'
import projectSelect from './projectSelect'

import { addRoadbedTechnical,updateRoadbedTechnical} from '@/api/baseData/roadbed/technical/index'


import {
  maintenanceSectionList,
} from '@/api/baseData/subgrade/baseInfo/getSections'

import { getListPage,getRoadbedAssess} from '@/api/baseData/roadbed/assess/index'

import { listMaintenanceSectionAll } from "@/api/system/maintenanceSection";
import CascadeSelection from '@/components/CascadeSelection/index.vue'
import { createIdWorker } from '@/api/baseData/common'
import { listAllHighwaySections } from '@/api/system/highwaySections.js'
import SelectTree from '@/components/DeptTmpl/selectTree'
import SectionSelect from '@/components/SectionSelect'
import { listAllRoute } from '@/api/system/route.js'
import "@riophae/vue-treeselect/dist/vue-treeselect.css";
import Treeselect from "@riophae/vue-treeselect";
import {getTreeStruct} from "@/api/tmpl";
import PileInput from '@/components/PileInput/index.vue'
import { listByMaintenanceSectionId } from "@/api/baseData/common/routeLine";

const apis = {
  listAllHighwaySections: listAllHighwaySections,
  listAllRoute: listAllRoute,
  maintenanceSectionList: maintenanceSectionList,
  listMaintenanceSectionAll:listMaintenanceSectionAll,
  listByMaintenanceSectionId:listByMaintenanceSectionId
}

export default {
  name: 'subgrade-baseInfo-addAndEdit',
  components: {SelectTree,Treeselect,SectionSelect,PileInput,CascadeSelection,projectSelect},
  props: {
    formData: {
      default: {}
    },
    showAddEdit: {
      default: false
    },
    title: {
      default: '添加路面评定单元静态数据'
    },
    forView: {
      default: false
    }
  },
  dicts: [
    'roadbed_elect_assess_type',
    'base_data_yes_no',
    'sys_route_nature',
    'sys_route_direction',
    'sys_surface_type',
    'sys_route_grade',
    'lane'
  ],
  data() {
    return {
      activeName: 'baseInfoData',
      sectionId:'',
      baseInfoData: [],
      specialExamData:[],
      deptUserOptions: [],

      queryParams: {
        pageNum: 1,
        pageSize: 20
      },
      ids: [],
      baseInfoForm: {},
      showRoadbedSelect:false,
      staticList: [],
      total: 0,
      form: {},
      loading: false,
      types: 101,
      ownerId: undefined,
      showProjectSelect:false,
    }
  },
  created() {
    if(this.formData.baseInfoForm){
      this.baseInfoForm = JSON.parse(JSON.stringify(this.formData.baseInfoForm) )
    }

    createIdWorker().then(res => {
        if (res.code === 200) {
          this.ownerId = Number(res.data)
        }
      })

    getTreeStruct({types: 111,deptTypeList:null}).then(response => {
      this.deptUserOptions = response.data;
    });
  },
  mounted() {
    this.initFormSlections()
  },
  methods: {


    // 获取表格数据
    getList() {

      let data={...this.queryParams}
      if (data.routeLevel && Array.isArray(data.routeLevel)) {
        data.routeLevel = data.routeLevel.join(",");
      }

      if (data.pavType && Array.isArray(data.pavType)) {
        data.pavType = data.pavType.join(",");
      }

      this.loading = true
      getListPage(data).then(response => {
        this.staticList = response.rows
        this.total = response.total
        this.loading = false
      })
    },
    // 搜索按钮
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    // 重置按钮
    resetQuery() {
      this.queryParams = {
        pageNum: 1,
        pageSize: 20
      }
      this.handleQuery()
    },

    // 获取表单字段以及表单选项列表
    initFormSlections() {
      // 行政识别数据

      this.$nextTick(()=>{
        this.baseInfoData = JSON.parse(JSON.stringify(dictionary.baseInfoData));
        this.baseInfoData.map(async (el,i) => {
        if (el.api&&el.type=='select') {
          const r = await apis[el.api](
                [this.baseInfoForm.managementMaintenanceId] || ['']
              )
            // 临时编码，返回的数据不合规范，数组去重
            if (r.row||r.data) {
              r.list = Array.from(new Set((r.row||r.data).map(JSON.stringify))).map(
                JSON.parse
              )
            }
            el.options = r.list

          }
      })

      })
      // 结构技术数据
    },

    changeSelect(e, i) {
      this.$forceUpdate()
      if(i.prop=='sectionType'){
        const item = this.baseInfoData.find(obj => obj.prop === 'sectionType');
        item.disabled=false;
      }
    },
    getRoadbed(e, i) {


      this.showRoadbedSelect=true
      this.getList()

    },
    getProject(){
      this.showProjectSelect=true
    },
    async handleSubmit(type) {
      const formNames = [
        'baseInfoData',
      ]
      switch (type) {
        case 'submit':
          this.form.isStaging = false
          break
        case 'save':
          this.form.isStaging = true
          break
      }

      if (!this.form.isStaging) {
        for (let index = 0; index < formNames?.length; index++) {
          const element = formNames[index]
          const valid = await new Promise(resolve => {
            this.$refs[element].validate(valid => {
              resolve(valid)
            })
          })
          if (!valid) {
            // 如果表单校验不通过，定位到对应的tab
            this.activeName = element
            return // 中止提交操作
          }
        }
      }
      let listBase=JSON.parse(JSON.stringify(this.baseInfoForm))


      this.baseInfoData.map(el => {
        if(el.type=='pileInput'&&el.precision&&this.baseInfoForm[el.prop]){

          listBase[el.prop]=(this.baseInfoForm?.[el.prop]?.toFixed(el.precision)).toString()
        }

      })

      if(this.form.isStaging){

      }else{
        if (this.baseInfoForm.id != null) {
          updateRoadbedTechnical(listBase).then(response => {
            this.$modal.msgSuccess(
              this.form.isStaging == true ? '暂存成功' : '新增成功'
            )
            this.$emit('refresh')
            this.initFormSlections()
          })
        } else {
          addRoadbedTechnical(listBase).then(response => {
            this.$modal.msgSuccess(
              this.form.isStaging == true ? '暂存成功' : '新增成功'
            )
            this.$emit('refresh')
            this.initFormSlections()
          })
        }
      }
    },

    // 勾选高亮
    rowStyle({ row, rowIndex }) {
      if (this.ids.includes(row.id)) {
        return { 'background-color': '#EFF5FF' }
      } else {
        return { 'background-color': '#fff' }
      }
    },

    changeInputNumber(precision,info,val) {
      let num=parseFloat(this[info][val]).toFixed(precision)
      this.$nextTick(() => {
        this[info][val] = num
      })
    },

    // 表格点击勾选
    handleRowClick(row) {
      row.isSelected = !row.isSelected
      this.$refs.table.toggleRowSelection(row)
      this.ids.push(row.id)

      this.showRoadbedSelect=false
      //将this.baseInfodata中得prop变成数组
      let propArr=this.baseInfoData.map(el=>el.prop)
      this.$set(this.baseInfoForm, 'assessId', row.id)
      this.$set(this.baseInfoForm, 'routeId', row.routeId)

      for (let key in row) {
        if (propArr.includes(key)) {
          this.$set(this.baseInfoForm, key, row[key])
        }
      }
    },

    // 监听选中管理处
    deptChange(e) {
      if (!e) return

      listMaintenanceSectionAll({ departmentId: e }).then(res => {
        if (res.code == 200) {
          this.baseInfoData.forEach(el => {
            if (el.prop === 'maintenanceSectionId') {
              el.options = res.data
            }
          })
        }
      })
    },

    handleRoadbedClose() {
      this.showRoadbedSelect=false
    },
    handleClose() {
      if (this.forView) {
        this.form = {}
        this.$emit('close', false)
      } else {
        this.$modal
          .confirm('确认退出？')
          .then(() => {
            this.form = {}
            this.$emit('close', false)
            this.initFormSlections()
          })
          .catch(() => {})
      }
    },
    projectClick(row){

      this.$set(this.baseInfoForm, 'project', row.project)
      this.$set(this.baseInfoForm, 'year', row.year)
      this.$set(this.baseInfoForm, 'projectId', row.id)

      this.showProjectSelect=false

    }

  },
  watch: {
    'baseInfoForm.managementMaintenanceId'(newVal, oldVal) {
      if(oldVal && this.baseInfoForm.maintenanceSectionId){
        this.baseInfoForm.maintenanceSectionId = ''
      }

      if (newVal) {
        this.deptChange(newVal)
      }
    },
  },
  computed: {
    oprUser: {
      set() {
        if(this.$refs.userCascade){
          let userList = this.$refs.userCascade[0].getCheckedNodes()
          this.baseInfoForm.recorder = userList?.map(item => item.value).join(',')
          this.baseInfoForm.recorderName = userList?.map(item => item.label).join(',')
        }

      },
      get() {
        // let idListList = this.checkEntity.oprUserId.split(',')
        return this.baseInfoForm?.recorder?.split(',') ?? null
      }
    }
  }
}
</script>

<style  scoped>
.roadbedDialog ::v-deep .el-input.is-disabled .el-input__inner{
  background-color: white;
  border-color: #dfe4ed;
  color: black;
}

.check-edit-title {
  font-weight: bold;
  font-size: 1.15rem;
  margin-left: 20px;
  padding: 10px 0;
  /*  color: #333333;*/
  /*  color: #72767b;*/
  &:before {
    content: '';
    display: inline-block;
    width: 5px;
    height: 1.5rem;
    vertical-align: bottom;
    margin-right: 0.8rem;
    background: #3797EB;
  }
}

</style>
