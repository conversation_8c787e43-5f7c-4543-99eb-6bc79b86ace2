<template>
  <el-select
    style="width: 100%;"
    v-model="localValue"
    :placeholder="placeholder"
    :disabled="disabled"
    :multiple="multiple"
    clearable
    filterable
    @clear="handleClear"
    @change="handleChange">
    <el-option
      v-for="item in options"
      :key="item.value"
      :label="item.label"
      :value="item.value">
    </el-option>
  </el-select>
</template>

<script>
export default {
  name: 'MultiDictSelect',
  props: {
    value: {
      type: [Array, String],
      default: () => [],
    },
    options: {
      type: Array,
      required: true,
      default: () => [],
    },
    placeholder: {
      type: String,
      default: '请选择',
    },
    disabled: {
      type: Boolean,
      default: false,
    },
    multiple: {
      type: Boolean,
      default: false,
    },
    delimiter: {
      type: String,
      default: ',', // 分隔符，用于字符串拆分和合并
    },
  },
  computed: {
    localValue: {
      get() {
        // 如果传入的是字符串则进行拆分，转为数组；如果是数组则直接使用
        if (typeof this.value === 'string') {
          return this.value.split(this.delimiter);
        }
        return this.value;
      },
      set(val) {
        // 当值变化时，向父组件传递数据
        const formattedValue = Array.isArray(val) ? val.join(this.delimiter) : val;
        this.$emit('input', formattedValue);
        this.$emit('update:value', formattedValue); // 支持 v-model:value
      },
    },
  },
  methods: {
    handleChange(val) {
      this.$emit('change', val); // 触发 change 事件
    },
    handleClear() {
      this.localValue = null; // 或 this.selectedValue = []，如果是多选
    }
  },
};
</script>

<style scoped>
/* 根据需求自定义样式 */

</style>
