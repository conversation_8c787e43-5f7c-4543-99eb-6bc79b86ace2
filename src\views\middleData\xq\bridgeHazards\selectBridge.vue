<template>
  <el-dialog title="选择桥梁" :visible.sync="visible" width="80%" append-to-body v-dialog-drag>
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true">
      <div style="display: flex; align-items: center; margin: 0">
        <CascadeSelection
          style="min-width: 240px;margin: 0 10px 18px 0"
          :form-data="queryParams"
          v-model="queryParams"
          types="201"
          multiple
        />
        <el-form-item label="" prop="assetName">
          <el-input
            size="mini"
            v-model="queryParams.assetName"
            placeholder="请输入桥梁名称"
            clearable
            style="width: 220px"
            @keyup.enter.native="handleQuery"
          />
        </el-form-item>
        <el-form-item label="" prop="assetCode">
          <el-input
            size="mini"
            v-model="queryParams.assetCode"
            placeholder="请输入桥梁编码"
            clearable
            style="width: 220px"
            @keyup.enter.native="handleQuery"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
          <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
        </el-form-item>
      </div>
    </el-form>
    <el-row>
      <el-table
        ref="table"
        v-loading="loading"
        height="396px"
        style="width: 100%"
        border
        :data="bridgeList"
        @row-click="handleRowClick"
        highlight-current-row
      >
        <el-table-column type="index" width="55">
          <template v-slot="scope">
            {{ (scope.$index + (queryParams.pageNum - 1) * queryParams.pageSize) + 1 }}
          </template>
        </el-table-column>
        <el-table-column label="路线编码" align="center" prop="routeCode"/>
        <el-table-column label="中心桩号" align="center" prop="centerStake"/>
        <el-table-column label="桥梁编码" align="center" prop="assetCode"/>
        <el-table-column label="桥梁名称" align="center" prop="assetName"/>
        <el-table-column label="管理处" align="center" prop="managementOffice"/>
        <el-table-column label="分处" align="center" prop="subOffice"/>
        <el-table-column label="养护路段" align="center" prop="maintenanceSection"/>
      </el-table>
      <pagination
        v-show="total>0"
        :total="total"
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
        @pagination="getList"
      />
    </el-row>
    <div slot="footer" class="dialog-footer">
      <el-button type="primary" @click="handleSelectBridge">确 定</el-button>
      <el-button @click="visible = false">取 消</el-button>
    </div>
  </el-dialog>
</template>

<script>
import {listStatic} from "@/api/patrol/assetInfo";
import CascadeSelection from '@/components/CascadeSelectionManagementOffice/index.vue';

export default {
  name: "SelectBridge",
  components: {CascadeSelection},
  data() {
    return {
      visible: false,
      loading: false,
      total: 0,
      bridgeList: [],
      currentRow: null,
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        assetName: null,
        assetCode: null,
      }
    };
  },
  methods: {
    show() {
      this.getList();
      this.visible = true;
    },
    handleRowClick(row) {
      this.currentRow = row;
    },
    getList() {
      this.loading = true;
      listStatic(this.queryParams).then(res => {
        this.bridgeList = res.rows.map(item => {
          return {
            ...item,
            managementOffice: item.managementMaintenanceName,
            subOffice: item.managementMaintenanceBranchName,
            maintenanceSection: item.maintenanceSectionName,
            routeCode: item.routeCode,
            centerStake: item.centerStake,
            assetCode: item.assetCode || item.bridgeCode,
            assetName: item.assetName || item.bridgeName
          };
        });
        this.total = res.total;
        this.loading = false;
      });
    },
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    resetQuery() {
      this.resetForm("queryForm");

      this.queryParams = {
        pageNum: 1,
        pageSize: 10,
        assetName: null,
        assetCode: null,
      }
      this.handleQuery();
    },
    handleSelectBridge() {
      if (!this.currentRow) {
        this.$modal.msgError("请选择桥梁");
        return;
      }
      this.$emit('select', this.currentRow);
      this.visible = false;
    }
  }
};
</script>

<style lang="scss" scoped>
.el-form-item {
  margin-bottom: 10px;
}
</style>
