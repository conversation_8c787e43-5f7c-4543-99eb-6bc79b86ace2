<template>
  <div class="container-view-list">
    <section class="container-header" v-if="ifSearch">
      <slot name="search" />
    </section>

    <section class="container-body" :style="{ marginTop: ifSearch ? '10px' : '' }">
      <div class="table-header" v-if="ifHeader">
        <slot name="header" />
      </div>

      <div class="divider" v-if="ifHeader"></div>

      <div class="table-body" :style="{ height: ifSearch ? 'calc(100% - 120px)' : 'calc(100% - 70px)' }">
        <slot name="body"></slot>
      </div>
    </section>

    <slot></slot>
  </div>
</template>

<script>
// 插槽body 里面的table height设为 100%
export default {
  props: {
    ifSearch: {
      type: Boolean,
      default: true,
    },
    ifHeader: {
      type: Boolean,
      default: true,
    }
  },
}
</script>

<style lang="scss" scoped>
.container-view-list {
  .container-header {
    // min-height: 50px;
    background: #ffffff;
    box-shadow: 0px 0px 10px 0px rgba(0, 0, 0, 0.2);
    border-radius: 10px;
    display: flex;
    flex-direction: column;
    padding: 10px;
  }

  .container-body {
    background: #ffffff;
    box-shadow: 0px 0px 10px 0px rgba(0, 0, 0, 0.2);
    border-radius: 10px;
    flex: 1;
    overflow: hidden;

    display: flex;
    flex-direction: column;
    .table-header {
      padding: 10px 10px 0px 10px;
    }

    .divider {
      width: calc(100% - 20px);
      height: 1px;
      background-color: #e5e6eb;
      margin: 0 auto;
    }

    .table-body {
      margin: 10px;
      flex: 1;
      // height: calc(100% - 120px);
      display: flex;
      flex-direction: column;
      ::v-deep .el-table {
        flex: 1;
      }
    }
  }
}
</style>


<style>
.container-view-list .el-table__fixed-body-wrapper .el-table__body {
		padding-bottom: 10px; 
	}
</style>