<template>
  <div class="draggable">
    <el-table ref="dataTable"
              v-adjust-table
              v-loading="loading"
              :data="tableData"
              :height="'calc(100vh - 270px)'"
              border
              row-key="id"
              size="mini"
              stripe
              style="width: 200%"
              @expand-change="loadData"
    >
      <el-table-column
        align="center"
        label="序号"
        type="index"
        width="50"
      />
      <el-table-column v-if="config.expandColumns" type="expand">
        <template slot-scope="props">
          <el-table v-adjust-table v-loading="expandLoading" :data="props.row.expandData" height="300px"
                    style="width: 100%">
            <el-table-column
              align="center"
              label="序号"
              type="index"
              width="50"
            />
            <template v-for="(column, index) in config.expandColumns">
              <el-table-column
                v-if="column.visible"
                :label="column.label"
                :prop="column.field"
                align="center"
                show-overflow-tooltip
              >
                <template slot-scope="scope">
                  <dict-tag
                    v-if="column.dict"
                    :options="dict.type[column.dict]"
                    :value="scope.row[column.field]"
                  />
                  <template v-else-if="column.slots">
                    <RenderDom
                      :index="index"
                      :render="column.render"
                      :row="scope.row"
                    />
                  </template>
                  <span v-else-if="column.isTime">{{
                      parseTime(scope.row[column.field], "{y}-{m}-{d}")
                    }}</span>
                  <span v-else>{{ scope.row[column.field] }}</span>
                </template>
              </el-table-column>
            </template>
          </el-table>
        </template>
      </el-table-column>
      <template v-for="(column, index) in config.columns">
        <el-table-column
          v-if="column.visible"
          :label="column.label"
          :prop="column.field"
          :width="column.width"
          align="center"
          show-overflow-tooltip
        >
          <template slot-scope="scope">
            <dict-tag
              v-if="column.dict"
              :options="dict.type[column.dict]"
              :value="scope.row[column.field]"
            />
            <template v-else-if="column.slots">
              <RenderDom
                :index="index"
                :render="column.render"
                :row="scope.row"
              />
            </template>
            <span v-else-if="column.isTime">{{
                parseTime(scope.row[column.field], "{y}-{m}-{d}")
              }}</span>
            <el-link type="primary" v-else-if="column.isLink" @click="handlePreview(scope.row)">{{ scope.row[column.field] }}</el-link>
            <span v-else>{{ scope.row[column.field] }}</span>
          </template>
        </el-table-column>
      </template>
    </el-table>
    <pagination
      v-show="total > 0"
      :limit.sync="queryParams.pageSize"
      :page.sync="queryParams.pageNum"
      :total="total"
      @pagination="handleQuery"
    />
    <IFramePreview ref="iframeRef" :srcdoc="preview.html" :down-url="preview.url" :file-name="preview.fileName"></IFramePreview>

  </div>
</template>

<script>
import {
  checkList,
  dailyList,
  dailySupList,
  operateList,
  otherConList,
  projDesignList,
  projList,
  projSupList,
  theftList,
  theftSupList
} from "@/api/calculate/report/comprehensive/meterNum";
import {listEvent as dailyEvent, previewSettlecalcReport as preview1} from "@/api/dailyMaintenance/metering/settlementApplication";
import {listEvent as theftEvent, previewSettlecalc as preview3} from "@/api/calculate/theft/settlementApplication";
import {listEvent as projectEvent, previewSettlecalc as preview5} from "@/api/calculate/project/settlementApplication";
import {getPreviewInfoById as preview8, listEvent as testingEvent} from "@/api/regularTesting/metering/settlementApplication";
import {contractOtherFundCalcList, getContractOtherFundCalcDetail} from "@/api/calculate/contractCalc/contractCalc";
import {getDicts} from "@/api/system/dict/data";
import {listDetail} from "@/api/calculate/operationManageFee/enterFee";
import {previewReport as preview2} from "@/api/calculate/calcdailysup/supApplication";
import {previewReport as preview4} from "@/api/calculate/calctheftsup/supApplication";
import {previewReport as preview6} from "@/api/calculate/maintenanceSup/supApplication";
import {previewReport as preview7} from "@/api/calculate/calcProjectDesign/designApplication";
import IFramePreview from "@/components/IFramePreview/index.vue";

export default {
  components: {IFramePreview},

  dicts: ['route_direction', 'lane', 'sys_asset_type', 'company_type', 'operate_metering_type', 'maintenance_type', 'affiliation_project_type',
    'calc_daliy_settlement_status', 'supervision_measurement_state', 'project_clac_settlement_status', 'design_measurement_state', 'testing_settlement_status'],

  props: {
    infoParams: {
      type: Object,
      default: () => {
      }
    },
    infoIndex: {
      type: Number,
      default: 0
    }
  },
  data() {
    return {
      queryParams: {
        pageNum: 1,
        pageSize: 50
      },
      total: 0,
      tableData: false,
      loading: false,
      config: {},
      expandLoading: false,
      preview: {
        html: '',
        url: '',
        fileName: ''
      },
    }
  },
  created() {
    this.config = config[this.infoIndex]
  },
  mounted() {
    this.handleQuery()
  },
  methods: {
    handleQuery() {
      this.loading = true
      const apiMethod = apiMethods[this.infoIndex];
      apiMethod({
        ...this.queryParams,
        ...this.infoParams
      }).then(res => {
        this.tableData = res.rows
        this.total = res.total
      }).finally(() => {
        this.loading = false
      })
    },
    loadData(row) {
      if (row.expandData) return
      if (this.infoIndex == 0) {
        this.expandLoading = true
        const params = {
          settlecalcId: row.id,
          pageNum: 1,
          pageSize: 100
        }
        dailyEvent(params).then(res => {
          this.$set(row, 'expandData', res.rows)
          this.expandLoading = false
        })
      }

      if (this.infoIndex == 2) {
        this.expandLoading = true
        const params = {
          settleCalcId: row.id,
          pageNum: 1,
          pageSize: 100
        }
        theftEvent(params).then(res => {
          this.$set(row, 'expandData', res.rows)
          this.expandLoading = false
        })
      }

      if (this.infoIndex == 4) {
        this.expandLoading = true
        const params = {
          settleCalcId: row.id,
          pageNum: 1,
          pageSize: 100
        }
        projectEvent(params).then(res => {
          this.$set(row, 'expandData', res.rows)
          this.expandLoading = false
        })
      }

      if (this.infoIndex == 7) {
        this.expandLoading = true
        const params = {
          settleCalcId: row.id,
          pageNum: 1,
          pageSize: 100
        }
        testingEvent(params).then(res => {
          this.$set(row, 'expandData', res.rows)
          this.expandLoading = false
        })
      }

      if (this.infoIndex == 8) {
        this.expandLoading = true
        const params = {
          meterId: row.id,
          pageNum: 1,
          pageSize: 100
        }
        listDetail(params).then(res => {
          this.$set(row, 'expandData', res.rows)
          this.expandLoading = false
        })
      }

      if (this.infoIndex == 9) {
        this.expandLoading = true
        const params = {
          id: row.id,
          pageNum: 1,
          pageSize: 100
        }
        contractOtherFundCalcList(params).then(res => {
          this.$set(row, 'expandData', res.rows)
          this.expandLoading = false
        })
      }
    },
    handlePreview(row) {
      this.loading = true
      const apiMethod = apiPreviewMethods[this.infoIndex];
      apiMethod(row.id).then(res => {
        this.preview.html = res.data.html
        this.preview.url = res.data.downUrl
        this.preview.fileName = res.data.fileName
        this.$refs.iframeRef.visible = true
      }).finally(() => {
        this.loading = false
      })
    }
  }
}
const apiMethods = [
  dailyList,
  dailySupList,
  theftList,
  theftSupList,
  projList,
  projSupList,
  projDesignList,
  checkList,
  operateList,
  otherConList
];

const apiPreviewMethods = [
  preview1,
  preview2,
  preview3,
  preview4,
  preview5,
  preview6,
  preview7,
  preview8
];

const config = [
  {
    columns: [
      {key: 0, width: 100, field: 'numberName', label: `期数`, visible: true},
      {key: 1, width: 200, field: 'name', label: `计量结算单名称`, visible: true, isLink: true},
      {key: 2, width: 200, field: 'code', label: `计量结算单编号`, visible: true, isLink: true},
      {key: 3, width: 150, field: 'domainName', label: `管养单位`, visible: true},
      {key: 4, width: 200, field: 'calcDomainName', label: `申请计量单位`, visible: true},
      {key: 5, width: 150, field: 'maiSecName', label: `路段名称`, visible: true},
      {key: 6, width: 200, field: 'conName', label: `合同名称`, visible: true},
      {key: 7, width: 100, field: 'constructionFund', label: `施工总费用(元)`, visible: true},
      {key: 8, width: 100, field: 'sumFund', label: `工程量费用(元)`, visible: true},
      {key: 9, width: 100, field: 'productionFund', label: `安全生产费(元)`, visible: true},
      {key: 10, width: 100, field: 'guaranteeFund', label: `安全保通费(元)`, visible: true},
      {key: 11, width: 100, field: 'supFund', label: `监理费(元)`, visible: true},
      {key: 12, width: 100, field: 'eductionFund', label: `扣款金额(元)`, visible: true},
      {key: 13, width: 200, field: 'calcDateStr', label: `计量日期`, visible: true},
      {key: 14, width: 200, field: 'createTimeStr', label: `操作日期`, visible: true},
      // {key: 15, width: 200, field: 'remark', label: `操作意见`, visible: true},
      // {key: 16, width: 200, field: 'updateuser', label: `操作人`, visible: true},
      {key: 17, width: 100, field: 'status', label: `状态`, visible: true, dict: 'calc_daliy_settlement_status'},
    ],
    expandColumns: [
      {key: 0, width: 100, field: 'code', label: `施工单编号`, visible: true},
      {key: 1, width: 100, field: 'assetMainTypeName', label: `资产类型`, visible: true, dict: 'sys_asset_type'},
      {key: 2, width: 100, field: 'maiSecName', label: `路段名称`, visible: true},
      {key: 3, width: 100, field: 'routeCode', label: `路线编码`, visible: true},
      {key: 4, width: 100, field: 'direction', label: `上下行`, visible: true, dict: 'route_direction'},
      {key: 5, width: 100, field: 'lane', label: `位置`, visible: true, dict: 'lane'},
      {
        key: 6, width: 100, field: 'beginMile', label: `起点桩号`, visible: true, slots: true, render: (row, index) => {
          return (
            <span>{this.formatPile(row.beginMile)}</span>
          )
        }
      },
      {
        key: 7, width: 100, field: 'endMile', label: `终点桩号`, visible: true, slots: true, render: (row, index) => {
          return (
            <span>{this.formatPile(row.endMile)}</span>
          )
        }
      },
      {key: 8, width: 100, field: 'disTypeName', label: `事件类型`, visible: true, dict: 'sys_asset_type'},
      {key: 9, width: 100, field: 'disDesc', label: `事件描述`, visible: true},
      {
        key: 10, width: 100, field: 'sumFund', label: `费用(元)`, visible: true, slots: true, render: (row) => {
          return (
            <span>{row.sumFund?.toFixed(0)}</span>
          );
        }
      },
      {
        key: 11,
        width: 100,
        field: 'productionFund',
        label: `安全生产费`,
        visible: true,
        slots: true,
        render: (row) => {
          return (
            <span>{row.productionFund?.toFixed(0)}</span>
          );
        }
      },
      {
        key: 12, width: 100, field: 'guaranteeFund', label: `安全保通费`, visible: true, slots: true, render: (row) => {
          return (
            <span>{row.guaranteeFund?.toFixed(0)}</span>
          );
        }
      },
      {
        key: 13, width: 100, field: 'supFund', label: `监理费`, visible: true, slots: true, render: (row) => {
          return (
            <span>{row.supFund?.toFixed(0)}</span>
          );
        }
      },
      {key: 15, width: 100, field: 'supDomainName', label: `监理单位`, visible: true},
      {key: 16, width: 100, field: 'operator', label: `操作人`, visible: true},
      {key: 17, width: 100, field: 'operatorTime', label: `操作时间`, visible: true},
    ]
  },
  {
    columns: [
      {key: 0, width: 100, field: 'status', label: `状态`, visible: true, dict: 'supervision_measurement_state'},
      {key: 1, width: 100, field: 'numberName', label: `期数`, visible: true},
      {key: 2, width: 200, field: 'name', label: `监理计量单名称`, visible: true, isLink: true},
      {key: 3, width: 200, field: 'code', label: `监理计量单编号`, visible: true, isLink: true},
      {key: 4, width: 200, field: 'preCalcName', label: `上期名称`, visible: true},
      {key: 5, width: 200, field: 'preCalcCode', label: `上期编号`, visible: true},
      {key: 6, width: 150, field: 'domainName', label: `管养单位`, visible: true},
      {key: 7, width: 150, field: 'maiSecName', label: `养护路段`, visible: true},
      {key: 8, width: 200, field: 'calcDomainName', label: `申请计量单位`, visible: true},
      {key: 9, width: 200, field: 'conName', label: `监理合同`, visible: true},
      {key: 10, width: 100, field: 'supFund', label: `监理费(元)`, visible: true},
      {key: 11, width: 100, field: 'deductionFund', label: `扣款金额(元)`, visible: true},
      {key: 12, width: 200, field: 'calcDate', label: `计量日期`, visible: true},
      {key: 13, width: 200, field: 'operateDate', label: `操作日期`, visible: true},
      {key: 14, width: 200, field: 'updateuser', label: `操作人`, visible: true},
      // {key: 15, width: 200, field: 'field15', label: `附件`, visible: true},
      {key: 16, width: 200, field: 'acceptPersonName', label: `验收人`, visible: true},
      {key: 17, width: 200, field: 'checkPersonName', label: `审核人`, visible: true},
    ],
  },
  {
    columns: [
      {key: 0, width: 100, field: 'numberName', label: `期数`, visible: true},
      {key: 1, width: 200, field: 'name', label: `结算计量名称`, visible: true, isLink: true},
      {key: 2, width: 200, field: 'code', label: `结算计量编号`, visible: true, isLink: true},
      {key: 3, width: 150, field: 'domainName', label: `管养单位`, visible: true},
      {key: 4, width: 200, field: 'calcDomainName', label: `申请计量单位`, visible: true},
      {key: 5, width: 150, field: 'maiSecName', label: `路段名称`, visible: true},
      {key: 6, width: 200, field: 'conName', label: `合同名称`, visible: true},
      {key: 7, width: 100, field: 'calcFund', label: `核定计量金额`, visible: true},
      {key: 8, width: 100, field: 'sumFund', label: `基本费用`, visible: true},
      {key: 9, width: 100, field: 'productionFund', label: `安全生产费`, visible: true},
      {key: 10, width: 100, field: 'guaranteeFund', label: `安全保通费`, visible: true},
      {key: 11, width: 100, field: 'supFund', label: `监理费`, visible: true},
      {key: 12, width: 100, field: 'eductionFund', label: `扣款金额`, visible: true},
      {key: 13, width: 200, field: 'calcDate', label: `计量日期`, visible: true},
      {key: 14, width: 100, field: 'status', label: `状态`, visible: true, dict: 'project_clac_settlement_status'},
    ],
    expandColumns: [
      {
        key: 2,
        field: "projectName",
        label: `项目名称`,
        visible: true,
      },
      {
        key: 1,
        width: 100,
        field: "constructionName",
        label: `任务单名称`,
        visible: true,
      },
      {
        key: 2,
        width: 100,
        field: "constructionCode",
        label: `任务单编号`,
        visible: true,
      },
      {
        key: 4,
        field: "domainName",
        label: `管养单位`,
        visible: true,
      },
      {
        key: 4,
        field: "maiSecName",
        label: `路段名称`,
        visible: true,
      },
      {
        key: 12,
        width: 100,
        field: "conDomainName",
        label: `施工单位`,
        visible: true,
      },
      {
        key: 12,
        width: 100,
        field: "supDomainName",
        label: `监理单位`,
        visible: true,
      },
      {
        key: 14,
        width: 100,
        field: "endTime",
        label: `完工日期`,
        visible: true,
      },
      {
        key: 12,
        width: 100,
        field: "defLiaPer",
        label: `缺陷责任期(月)`,
        visible: true,
      },
      {
        key: 9,
        width: 100,
        field: "sumFund",
        label: `费用`,
        visible: true,
      },
    ]
  },
  {
    columns: [
      {key: 0, width: 100, field: 'status', label: `状态`, visible: true, dict: 'supervision_measurement_state'},
      {key: 1, width: 100, field: 'numberName', label: `期数`, visible: true},
      {key: 2, width: 200, field: 'name', label: `监理计量单名称`, visible: true, isLink: true},
      {key: 3, width: 200, field: 'code', label: `监理计量单编号`, visible: true, isLink: true},
      {key: 4, width: 200, field: 'preCalcName', label: `上期名称`, visible: true},
      {key: 5, width: 200, field: 'preCalcCode', label: `上期编号`, visible: true},
      {key: 6, width: 150, field: 'domainName', label: `管养单位`, visible: true},
      {key: 7, width: 150, field: 'maiSecName', label: `养护路段`, visible: true},
      {key: 8, width: 200, field: 'calcDomainName', label: `申请计量单位`, visible: true},
      {key: 9, width: 200, field: 'conName', label: `监理合同`, visible: true},
      {key: 10, width: 100, field: 'supFund', label: `监理费(元)`, visible: true},
      {key: 11, width: 100, field: 'deductionFund', label: `扣款金额(元)`, visible: true},
      {key: 12, width: 200, field: 'calcDate', label: `计量日期`, visible: true},
      {key: 13, width: 200, field: 'operateDate', label: `操作日期`, visible: true},
      {key: 14, width: 200, field: 'updateuser', label: `操作人`, visible: true},
      // {key: 15, width: 200, field: 'field15', label: `附件`, visible: true},
      {key: 16, width: 200, field: 'acceptPersonName', label: `验收人`, visible: true},
      {key: 17, width: 200, field: 'checkPersonName', label: `审核人`, visible: true},
    ],
  },
  {
    columns: [
      {key: 0, width: 100, field: 'numberName', label: `期数`, visible: true},
      {key: 1, width: 200, field: 'name', label: `结算计量名称`, visible: true, isLink: true},
      {key: 2, width: 200, field: 'code', label: `结算计量编号`, visible: true, isLink: true},
      {key: 3, width: 150, field: 'domainName', label: `管养单位`, visible: true},
      {key: 4, width: 200, field: 'calcDomainName', label: `申请计量单位`, visible: true},
      {key: 5, width: 150, field: 'maiSecName', label: `路段名称`, visible: true},
      {key: 6, width: 200, field: 'conName', label: `合同名称`, visible: true},
      {key: 7, width: 100, field: 'calcFund', label: `核定计量金额`, visible: true},
      {key: 8, width: 100, field: 'sumFund', label: `基本费用`, visible: true},
      {key: 9, width: 100, field: 'productionFund', label: `安全生产费`, visible: true},
      {key: 10, width: 100, field: 'guaranteeFund', label: `安全保通费`, visible: true},
      {key: 11, width: 100, field: 'supFund', label: `监理费`, visible: true},
      {key: 12, width: 100, field: 'designFund', label: `设计费`, visible: true},
      {key: 13, width: 100, field: 'eductionFund', label: `扣款金额`, visible: true},
      {key: 14, width: 200, field: 'calcDate', label: `计量日期`, visible: true},
      {key: 15, width: 100, field: 'status', label: `状态`, visible: true, dict: 'project_clac_settlement_status'},
    ],
    expandColumns: [
      {
        key: 2,
        field: "projectName",
        label: `项目名称`,
        visible: true,
      },
      {
        key: 1,
        width: 100,
        field: "constructionName",
        label: `任务单名称`,
        visible: true,
      },
      {
        key: 2,
        width: 100,
        field: "constructionCode",
        label: `任务单编号`,
        visible: true,
      },
      {
        key: 4,
        field: "domainName",
        label: `管养单位`,
        visible: true,
      },
      {
        key: 4,
        field: "maiSecName",
        label: `路段名称`,
        visible: true,
      },
      {
        key: 12,
        width: 100,
        field: "conDomainName",
        label: `施工单位`,
        visible: true,
      },
      {
        key: 12,
        width: 100,
        field: "supDomainName",
        label: `监理单位`,
        visible: true,
      },
      {
        key: 14,
        width: 100,
        field: "endTime",
        label: `完工日期`,
        visible: true,
      },
      {
        key: 12,
        width: 100,
        field: "defLiaPer",
        label: `缺陷责任期(月)`,
        visible: true,
      },
      {
        key: 9,
        width: 100,
        field: "sumFund",
        label: `金额`,
        visible: true,
      },
    ]
  },
  {
    columns: [
      {key: 0, width: 100, field: 'status', label: `状态`, visible: true, dict: 'supervision_measurement_state'},
      {key: 1, width: 100, field: 'numberName', label: `期数`, visible: true},
      {key: 2, width: 200, field: 'name', label: `监理计量单名称`, visible: true, isLink: true},
      {key: 3, width: 200, field: 'code', label: `监理计量单编号`, visible: true, isLink: true},
      {key: 4, width: 200, field: 'preCalcName', label: `上期名称`, visible: true},
      {key: 5, width: 200, field: 'preCalcCode', label: `上期编号`, visible: true},
      {key: 6, width: 150, field: 'domainName', label: `管养单位`, visible: true},
      {key: 7, width: 150, field: 'maiSecName', label: `养护路段`, visible: true},
      {key: 8, width: 200, field: 'calcDomainName', label: `申请计量单位`, visible: true},
      {key: 9, width: 200, field: 'conName', label: `监理合同`, visible: true},
      {key: 10, width: 100, field: 'supFund', label: `监理费(元)`, visible: true},
      {key: 11, width: 100, field: 'deductionFund', label: `扣款金额(元)`, visible: true},
      {key: 12, width: 200, field: 'calcDate', label: `计量日期`, visible: true},
      {key: 13, width: 200, field: 'operateDate', label: `操作日期`, visible: true},
      {key: 14, width: 200, field: 'updateuser', label: `操作人`, visible: true},
      // {key: 15, width: 200, field: 'field15', label: `附件`, visible: true},
      {key: 16, width: 200, field: 'acceptPersonName', label: `验收人`, visible: true},
      {key: 17, width: 200, field: 'checkPersonName', label: `审核人`, visible: true},
    ],
  },
  {
    columns: [
      {key: 0, width: 100, field: 'status', label: `状态`, visible: true, dict: 'design_measurement_state'},
      {key: 1, width: 100, field: 'numberName', label: `期数`, visible: true},
      {key: 2, width: 200, field: 'name', label: `设计计量单名称`, visible: true, isLink: true},
      {key: 3, width: 200, field: 'code', label: `设计计量单编号`, visible: true, isLink: true},
      {key: 4, width: 200, field: 'preCalcName', label: `上期名称`, visible: true},
      {key: 5, width: 200, field: 'preCalcCode', label: `上期编号`, visible: true},
      {key: 6, width: 150, field: 'domainName', label: `管养单位`, visible: true},
      {key: 7, width: 150, field: 'maiSecName', label: `养护路段`, visible: true},
      {key: 8, width: 200, field: 'calcDomainName', label: `申请计量单位`, visible: true},
      {key: 9, width: 200, field: 'conName', label: `设计合同`, visible: true},
      {key: 10, width: 100, field: 'designFund', label: `设计费(元)`, visible: true},
      {key: 11, width: 100, field: 'deductionFund', label: `扣款金额(元)`, visible: true},
      {key: 12, width: 200, field: 'calcDate', label: `计量日期`, visible: true},
      {key: 13, width: 200, field: 'operateDate', label: `操作日期`, visible: true},
      {key: 14, width: 200, field: 'updateuser', label: `操作人`, visible: true},
      // {key: 15, width: 200, field: 'field15', label: `附件`, visible: true},
      {key: 16, width: 200, field: 'acceptPersonName', label: `验收人`, visible: true},
      {key: 17, width: 200, field: 'checkPersonName', label: `审核人`, visible: true},
    ],
  },
  {
    columns: [
      {key: 0, width: 100, field: 'numberName', label: `期数`, visible: true},
      {key: 1, width: 200, field: 'name', label: `结算计量名称`, visible: true, isLink: true},
      {key: 2, width: 200, field: 'code', label: `结算计量编号`, visible: true, isLink: true},
      {key: 3, width: 150, field: 'domainName', label: `管养单位`, visible: true},
      {key: 4, width: 200, field: 'calcDomainName', label: `申请计量单位`, visible: true},
      {key: 5, width: 150, field: 'maiSecName', label: `路段名称`, visible: true},
      {key: 6, width: 200, field: 'conName', label: `合同名称`, visible: true},
      {key: 7, width: 100, field: 'calcFund', label: `核定计量金额`, visible: true},
      {key: 8, width: 100, field: 'sumFund', label: `基本费用`, visible: true},
      {key: 9, width: 100, field: 'productionFund', label: `安全生产费`, visible: true},
      {key: 10, width: 100, field: 'guaranteeFund', label: `安全保通费`, visible: true},
      {key: 11, width: 100, field: 'supFund', label: `监理费`, visible: true},
      {key: 12, width: 100, field: 'eductionFund', label: `扣款金额`, visible: true},
      {key: 13, width: 200, field: 'calcDate', label: `计量日期`, visible: true},
      {key: 14, width: 100, field: 'status', label: `状态`, visible: true, dict: 'testing_settlement_status'},
    ],
    expandColumns: [
      {
        key: 1,
        field: "projectName",
        label: `项目名称`,
        visible: true,
      },
      {
        key: 2,
        field: "constructionName",
        label: `任务单名称`,
        visible: true,
      },
      {
        key: 3,
        field: "constructionCode",
        label: `任务单编号`,
        visible: true,
      },
      {
        key: 4,
        field: "maiSecName",
        label: `路段名称`,
        visible: true,
      },
      {
        key: 5,
        field: "domainName",
        label: `管养单位`,
        visible: true,
      },
      {
        key: 6,
        field: "checkDomainName",
        label: `检测单位`,
        visible: true,
      },
      {
        key: 7,
        field: "checkConName",
        label: `检测合同`,
        visible: true,
      },
      {
        key: 8,
        field: "endTime",
        label: `完工日期`,
        visible: true,
      },
      {
        key: 9,
        field: "sumFund",
        label: `金额`,
        visible: true,
      },
    ]
  },
  {
    columns: [
      {key: 0, field: 'numberName', label: `期数`, visible: true},
      {key: 1, field: 'name', label: `计量名称`, visible: true},
      {key: 2, field: 'code', label: `计量编码`, visible: true},
      {key: 3, field: 'fund', label: `计量金额`, visible: true},
      {key: 4, field: 'domainName', label: `计量单位`, visible: true},
      {key: 5, field: 'maiSecName', label: `养护路段`, visible: true},
      // {key: 6, width: 200, field: 'field6', label: `计量人员`, visible: true},
    ],
    expandColumns: [
      {key: 0, width: 100, field: 'companyType', label: `养护公司类型`, visible: true, dict: 'company_type'},
      {key: 1, width: 100, field: 'typeId', label: `费用类型`, visible: true, dict: 'operate_metering_type'},
      {key: 2, width: 100, field: 'month', label: `月份`, visible: true},
      {key: 3, width: 100, field: 'mileStr', label: `里程桩号`, visible: true},
      {key: 4, width: 100, field: 'fund', label: `总费`, visible: true},
      {key: 5, width: 100, field: 'remark', label: `备注`, visible: true},
    ]
  },
  {
    columns: [
      {key: 0, width: 100, field: 'stageName', label: `状态`, visible: true},
      {key: 1, width: 100, field: 'numberName', label: `期数`, visible: true},
      {key: 2, width: 200, field: 'calcName', label: `计量单名称`, visible: true},
      {key: 3, width: 200, field: 'calcCode', label: `计量单编号`, visible: true},
      {key: 4, width: 150, field: 'domainName', label: `管养单位`, visible: true},
      {key: 5, width: 200, field: 'conDomainName', label: `申请计量单位`, visible: true},
      {key: 6, width: 150, field: 'maiSecName', label: `路段名称`, visible: true},
      {key: 7, width: 100, field: 'sumCost', label: `计量费用`, visible: true},
      {key: 8, width: 200, field: 'preCalcName', label: `上一期名称`, visible: true},
      {key: 9, width: 200, field: 'preCalcCode', label: `上一期编号`, visible: true},
      {key: 10, width: 200, field: 'calcDate', label: `计量日期`, visible: true},
      {key: 11, width: 200, field: 'operateDate', label: `操作日期`, visible: true},
      {key: 12, width: 200, field: 'updateuser', label: `操作人`, visible: true},
      {key: 13, width: 200, field: 'remark', label: `备注`, visible: true},
      // {key: 14, width: 200, field: 'field14', label: `附件`, visible: true},
    ],
    expandColumns: [
      {key: 0, width: 100, field: 'mtype', label: `养护类型`, visible: true, dict: 'maintenance_type'},
      {key: 1, width: 100, field: 'projType', label: `所属工程类型`, visible: true, dict: 'affiliation_project_type'},
      {key: 2, width: 100, field: 'fundType', label: `费用类型`, visible: true},
      {key: 3, width: 100, field: 'schemeCode', label: `子目号`, visible: true},
      {key: 4, width: 100, field: 'schemeName', label: `子目名称`, visible: true},
      {key: 5, width: 100, field: 'price', label: `单价`, visible: true},
      {key: 6, width: 100, field: 'unit', label: `单位`, visible: true},
      {key: 7, width: 100, field: 'schemeNum', label: `数量`, visible: true},
      {key: 8, width: 100, field: 'schemeFund', label: `金额`, visible: true},
      {key: 9, width: 100, field: 'remark', label: `备注`, visible: true},
      {key: 10, width: 100, field: 'updateuser', label: `操作人`, visible: true},
      {key: 11, width: 100, field: 'createTime', label: `操作时间`, visible: true},
    ]
  }
]

</script>

<style lang="scss" scoped>
</style>
