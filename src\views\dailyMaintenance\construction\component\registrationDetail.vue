<template>
  <div class="app-container">
    <el-form ref="elForm" :model="formData" :inline="true" label-width="180px">
      <el-row :gutter="20">
        <el-col :span="24" style="margin-bottom: 18px">
          <div class="card_title">施工前采集照片</div>
          <el-card class="box-card" shadow="never">
            <el-form-item label="">
              <file-upload v-model="formData.disPicPath" :ownerId="123"></file-upload>
            </el-form-item>
          </el-card>
        </el-col>
        <el-col :span="12">
          <div class="card_title">施工简图</div>
          <el-card class="box-card" shadow="never">
            <el-form-item label="">
              <file-upload v-model="formData.disPicPath" :ownerId="1231"></file-upload>
            </el-form-item>
          </el-card>
        </el-col>
        <el-col :span="12">
          <div class="card_title">施工附件</div>
          <el-card class="box-card" shadow="never">
            <el-form-item label="">
              <file-upload v-model="formData.disPicPath" :ownerId="1232"></file-upload>
            </el-form-item>
          </el-card>
        </el-col>
        <el-col :span="24" style="margin-top: 18px">
          <div class="card_title" style="display: flex;width: 100%;justify-content: space-between"><div>事件方法数量</div><el-button icon="el-icon-plus" circle @click="openLibModel"></el-button></div>
          <el-table v-adjust-table
              :data="schemeList"
              border
              height="200px"
              ref="tableRef"
              style="width: 100%">
            <el-table-column
                label="序号"
                align="center"
                type="index"
                width="50"
            />
            <el-table-column
                prop="schemeCode"
                align="center"
                label="子目号"
                width="100">
            </el-table-column>
            <el-table-column
                prop="schemeName"
                align="center"
                label="子目名称"
                width="100">
            </el-table-column>
            <el-table-column
                prop="unit"
                align="center"
                label="单位"
                width="100">
            </el-table-column>
            <el-table-column
                prop="price"
                align="center"
                label="单价"
                width="100">
            </el-table-column>
            <el-table-column
                prop="calculation"
                align="center"
                label="计算式"
                width="100">
              <template slot-scope="scope">
                <el-input v-model="scope.row.calculation" @change="changeCalculation(scope.row)">
                </el-input>
              </template>
            </el-table-column>
            <el-table-column
                prop="schemeNum"
                align="center"
                label="方法数量"
                width="100">
              <template slot-scope="scope">
                <el-input v-model="scope.row.schemeNum"  @change="changeSchemeNum(scope.row)">
                </el-input>
              </template>
            </el-table-column>
            <el-table-column
                prop="money"
                align="center"
                label="资金"
                width="100">
            </el-table-column>
            <el-table-column
                prop="remark"
                align="center"
                label="备注"
                width="100">
              <template slot-scope="scope">
                <el-input v-model="scope.row.remark">
                </el-input>
              </template>
            </el-table-column>
            <el-table-column
                prop="field101"
                align="center"
                label="移除">
              <template slot-scope="scope">
                <el-button
                    size="mini"
                    type="text"
                    @click="handleDelete(scope.row)"
                >移除
                </el-button
                >
              </template>
            </el-table-column>
          </el-table>
        </el-col>
        <el-col :span="24" style="margin-top: 18px">
          <div class="card_title" style="display: flex;width: 100%;justify-content: space-between"><div>事件方法数量</div><el-link type="primary" @click="openLibModel">生成计算式说明</el-link></div>
          <el-input v-model="formData.field1" type="textarea" :rows="4">
          </el-input>
        </el-col>
        <el-col :span="24" style="margin-top: 18px">
          <div class="card_title">登记备注</div>
          <el-input v-model="formData.field1" type="textarea" :rows="4">
          </el-input>
        </el-col>
      </el-row>
      <div style="text-align: right;padding-right: 7.5px;margin-top: 18px">
        <el-button type="primary" @click="onSubmit">保 存</el-button>
        <el-button type="success" @click="onSubmit">提 交</el-button>
        <el-button @click="onClose">退出</el-button>
      </div>
    </el-form>
    <el-dialog title="选择报价" destroy-on-close :visible.sync="libModel" width="80%" append-to-body>
      <el-input
          placeholder="输入关键字进行过滤"
          v-model="filterText">
      </el-input>

      <el-tree
          class="filter-tree"
          :data="libData"
          style="height: 300px;overflow-y: scroll"
          show-checkbox
          :props="defaultProps"
          default-expand-all
          :filter-node-method="filterNode"
          ref="tree">
      </el-tree>
      <div style="text-align: right;padding-right: 7.5px">
        <el-button type="primary" @click="checkLib">保 存</el-button>
        <el-button @click="libModel = false">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import {getTreeData} from "@/api/contract/quotationSystem"

export default {
  name: "index",
  data() {
    return {
      formData: {
        field1: '',
        field2: '',
      },
      libModel: false,
      filterText: '',
      libData: [],
      defaultProps: {
        children: 'children',
        label: 'schemeName'
      }
    }
  },
  mounted() {
    this.getLibData()
  },
  methods: {
    checkLib() {
      let checkDatas = this.$refs.tree.getCheckedNodes()
      checkDatas = checkDatas.filter(item => {
        return item.nodeType == 2
      })
      this.schemeList = checkDatas
      this.libModel = false
    },
    getLibData() {
      // 获取报价体系
      getTreeData({
        schemeType: '日常养护'
      }).then(res => {
        this.libData = res.rows || []
        // 过滤libData中children为空的
        // this.libData = this.libData.filter(item => {
        //   return item.children && item.children.length > 0
        // })
      })
    },
    openLibModel() {
      this.libModel = true
    },
    filterNode(value, data) {
      if (!value) return true;
      return data.label.indexOf(value) !== -1;
    },
    changeCalculation(row) {
      if (!this.isValidMathFormula(row.calculation)) {
        this.$modal.msgError('计算式错误，请检查')
        return
      }
      this.$set(row, 'schemeNum', this.math.evaluate(row.calculation))
      this.$set(row, 'money', row.schemeNum * row.price)
      this.total = this.schemeList.reduce((acc, curr) => acc + curr.money, 0)
    },
    changeSchemeNum(row) {
      this.$set(row, 'money', row.schemeNum * row.price)
      this.total = this.schemeList.reduce((acc, curr) => acc + curr.money, 0)
    },
    onClose() {
      this.$emit("close")
    }
  }
}
</script>
<style scoped lang="scss">
.card_title {
  width: 200px;
  text-align: left;
  margin-bottom: 15px;
  font-weight: bold;
}
</style>
<style lang="scss" scoped>
@import "@/assets/styles/business.scss";
</style>
