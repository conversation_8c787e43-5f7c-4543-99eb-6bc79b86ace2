<template>
  <PageContainer>
    <template slot="search">
      <div style="display: flex; align-items: center; margin: 0">
        <CascadeSelection
          style="min-width: 192px"
          :form-data="queryParams"
          v-model="queryParams"
          types="201"
          multiple
        />
        <el-select
          v-model="queryParams.lengthClassification"
          placeholder="隧道种类"
          clearable
          style="margin-left: 20px"
        >
          <el-option
            v-for="dict in dict.type.tunnel_length_classification"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          >
          </el-option>
        </el-select>
        <el-input
          v-model="queryParams.tunnelName"
          placeholder="隧道名称"
          clearable
          style="width: 172px; margin: 0 20px"
        />
        <div style="min-width: 240px">
          <el-button
            v-hasPermi="['baseData:tunnel:listPage']"
            icon="el-icon-search"
            type="primary"
            @click="handleQuery"
            >搜索</el-button
          >
          <el-button icon="el-icon-refresh" @click="resetQuery">重置</el-button>
          <el-button
            v-show="!showSearch"
            @click="showSearch = true"
            icon="el-icon-arrow-down"
            circle
          ></el-button>
          <el-button
            v-show="showSearch"
            @click="showSearch = false"
            icon="el-icon-arrow-up"
            style="
              color: #1890ff;
              border-color: #badeff;
              background-color: #e8f4ff;
            "
            circle
          ></el-button>
        </div>
      </div>
      <el-form
        :model="queryParams"
        ref="queryForm"
        :inline="true"
        v-show="showSearch"
      >
        <div class="first-divider" />
        <el-form-item style="margin: 5px 10px 0 0">
          <el-select
            v-model="queryParams.operationState"
            placeholder="运营状态"
            clearable
          >
            <el-option
              v-for="dict in dict.type.sys_operation_state"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item style="margin: 5px 10px 0 0">
          <div style="width: 100%">
            <el-input
              v-model="queryParams.startStake"
              placeholder="起始桩号"
              clearable
              style="width: calc(50% - 8px)"
            />
            -
            <el-input
              v-model="queryParams.endStake"
              placeholder="结束桩号"
              clearable
              style="width: calc(50% - 8px)"
            />
          </div>
        </el-form-item>
        <el-form-item style="margin: 5px 10px 0 0">
          <el-select
            v-model="queryParams.status"
            placeholder="数据状态"
            clearable
          >
            <el-option
              v-for="dict in dict.type.base_data_state"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item style="margin: 5px 10px 0 0">
          <el-input
            style="width: 100%"
            v-model="queryParams.tunnelCode"
            placeholder="隧道编码"
            clearable
          />
        </el-form-item>
        <el-form-item style="margin: 5px 10px 0 0">
          <el-select
            v-model="queryParams.inLongTunnelDirectory"
            placeholder="是否在长大隧道"
            clearable
          >
            <el-option
              v-for="dict in dict.type.base_data_yes_no"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item style="margin: 5px 10px 0 0">
          <el-select
            v-model="queryParams.whetherHealthMonitorSystemType"
            placeholder="是否有健康监测系统"
            clearable
          >
            <el-option
              v-for="dict in dict.type.base_data_yes_no"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item style="margin: 5px 10px 0 0">
          <el-select
            v-model="queryParams.assessmentGrade"
            placeholder="评定等级"
            clearable
          >
            <el-option
              v-for="dict in dict.type.tunnel_assess_grade"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item style="margin: 5px 10px 0 0">
          <el-select
            v-model="queryParams.liningType"
            placeholder="衬砌类型"
            clearable
          >
            <el-option
              v-for="dict in dict.type.tunnel_lining_type"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="建成时间" style="margin: 5px 10px 0 0">
          <el-date-picker
            v-model="dateRange"
            type="daterange"
            range-separator="-"
            start-placeholder="年-月-日"
            end-placeholder="年-月-日"
            value-format="yyyy-MM-dd"
          />
        </el-form-item>
      </el-form>
    </template>
    <template slot="header">
      <div class="button-list">
        <el-button
          v-hasPermi="['baseData:tunnel:add']"
          type="primary"
          @click="handleAdd"
          >新增</el-button
        >
        <el-button
          v-hasPermi="['baseData:tunnel:edit']"
          type="primary"
          @click="handleUpdate"
          >编辑</el-button
        >
        <el-button
          v-hasPermi="['baseData:tunnel:remove']"
          type="primary"
          @click="handleDelete"
          >删除</el-button
        >
        <el-button
          v-hasPermi="['baseData:tunnel:query']"
          type="primary"
          @click="handleView"
          >查看</el-button
        >
        <el-button
          v-hasPermi="['baseData:tunnel:updateLocked']"
          type="primary"
          @click="handleLocked"
          >是否锁定</el-button
        >
        <el-button
          v-hasPermi="['baseData:businessStatusRecord:add']"
          type="primary"
          @click="changeStatus"
          >运营状态变更</el-button
        >
        <el-button
          v-hasPermi="['baseData:tunnel:drawing']"
          type="primary"
          @click="handleViewDrawings"
          >查看图纸</el-button
        >
        <el-button
          v-hasPermi="['baseData:tunnel:getCard']"
          type="primary"
          @click="handleViewCard"
          >查看卡片</el-button
        >
        <el-button
          v-hasPermi="['baseData:tunnel:cardExport']"
          type="primary"
          @click="handleExportCard"
          >导出卡片</el-button
        >
        <!-- <el-button
          v-hasPermi="['baseData:tunnel:card']"
          type="primary"
          @click="handleEditCard"
        >编辑卡片</el-button> -->
        <el-button
          v-hasPermi="['baseData:tunnel:export']"
          type="primary"
          @click="exportList"
          >导出清单</el-button
        >
        <el-button
          v-hasPermi="['baseData:tunnel:exportAll']"
          type="primary"
          @click="exportAllList"
          >数据导出</el-button
        >
        <el-button
          v-hasPermi="['baseData:import:execute']"
          type="primary"
          @click="importUpdate"
          >导入更新</el-button
        >
        <el-button
          v-hasPermi="['baseData:import:execute']"
          type="primary"
          @click="importAdd"
          >导入新增</el-button
        >
        <el-button
          v-hasPermi="['baseData:tunnel:genQrCode']"
          type="primary"
          @click="downloadQrcode"
          >二维码下载</el-button
        >
        <el-button
          v-hasPermi="['baseData:tunnel:submitAudit']"
          type="primary"
          @click="taskBridge"
          >提交审核</el-button
        >
        <el-button type="primary"
                   v-hasPermi="['baseData:tunnel:exportAnnualReport']"
                   @click="taskExportAnnualReport"
          >导出年报</el-button
        >
        <el-button type="primary" v-hasPermi="['baseData:tunnel:device']" @click="deviceCheck">机电设备</el-button>
      </div>
    </template>
    <template slot="body">
      <el-table
        v-adjust-table
        ref="table"
        height="100%"
        v-loading="loading"
        border
        :data="staticList"
        :header-cell-style="{ height: '36px' }"
        :row-style="rowStyle"
        @selection-change="handleSelectionChange"
        @row-click="handleRowClick"
      >
        <el-table-column type="selection" width="50" align="center">
        </el-table-column>
        <el-table-column
          fixed
          label="序号"
          type="index"
          width="50"
          align="center"
        >
          <template v-slot="scope">
            {{
              scope.$index +
              (queryParams.pageNum - 1) * queryParams.pageSize +
              1
            }}
          </template>
        </el-table-column>
        <el-table-column fixed label="操作" align="center" width="50">
          <template slot-scope="scope">
            <el-link
              type="primary"
              :disabled="!scope.row.shape"
              @click.stop="handleLocation(scope.row)"
              >定位</el-link
            >
          </template>
        </el-table-column>
        <el-table-column
          fixed
          label="隧道名称"
          align="center"
          prop="tunnelName"
          min-width="140"
          show-overflow-tooltip
        />
        <el-table-column
          label="隧道编码"
          align="center"
          prop="tunnelCode"
          min-width="140"
          show-overflow-tooltip
        />
        <el-table-column
          label="管理处"
          align="center"
          prop="managementMaintenanceName"
          min-width="140"
        >
        </el-table-column>

        <el-table-column
          label="管养分处"
          align="center"
          prop="managementMaintenanceBranchName"
          min-width="140"
        />
        <el-table-column
          label="养护路段"
          align="center"
          prop="maintenanceSectionName"
          min-width="140"
        >
        </el-table-column>
        <el-table-column
          label="路线名称"
          align="center"
          prop="routeName"
          min-width="140"
          show-overflow-tooltip
        />
        <el-table-column
          label="路线编码"
          align="center"
          prop="routeCode"
          min-width="140"
          show-overflow-tooltip
        />
        <el-table-column
          label="隧道种类"
          align="center"
          prop="lengthClassification"
          min-width="140"
          show-overflow-tooltip
        >
          <template slot-scope="scope">
            <dict-tag
              :options="dict.type.tunnel_length_classification"
              :value="scope.row.lengthClassification"
            />
          </template>
        </el-table-column>
        <el-table-column
          label="隧道所在位置"
          align="center"
          prop="direction"
          min-width="140"
          show-overflow-tooltip
        >
          <template slot-scope="scope">
            <dict-tag
              :options="dict.type.sys_route_direction"
              :value="scope.row.direction"
            />
          </template>
        </el-table-column>
        <el-table-column
          label="隧道养护等级"
          align="center"
          prop="maintenanceGrade"
          min-width="140"
          show-overflow-tooltip
        >
          <template slot-scope="scope">
            <dict-tag
              :options="dict.type.tunnel_maintenance_grade"
              :value="scope.row.maintenanceGrade"
            />
          </template>
        </el-table-column>
        <el-table-column
          label="总体评定等级"
          align="center"
          prop="assessmentGrade"
          min-width="140"
          show-overflow-tooltip
        >
          <template slot-scope="scope">
            <dict-tag
              :options="dict.type.tunnel_assess_grade"
              :value="scope.row.assessmentGrade"
            />
          </template>
        </el-table-column>
        <el-table-column
          label="中心桩号"
          align="center"
          prop="centerStake"
          min-width="140"
          show-overflow-tooltip
        >
          <template slot-scope="scope">
            {{ formatPile(scope.row.centerStake) }}
          </template>
        </el-table-column>
        <el-table-column
          label="隧道长度(m)"
          align="center"
          prop="tunnelLength"
          min-width="140"
          show-overflow-tooltip
        />
        <el-table-column
          label="修建年度"
          align="center"
          prop="buildDate"
          min-width="140"
          show-overflow-tooltip
        />
        <el-table-column
          label="建成通车时间"
          align="center"
          prop="operationDate"
          min-width="140"
          show-overflow-tooltip
        />
        <el-table-column
          label="运营状态"
          align="center"
          prop="operationState"
          min-width="140"
          show-overflow-tooltip
        >
          <template slot-scope="{ row }">
            <el-link
              :underline="false"
              :type="
                { 1: 'info', 2: 'success', 3: 'danger', 4: 'primary' }[
                  row.operationState
                ]
              "
              @click="handleOperational($event, row)"
            >
              <DictTag
                :value="row.operationState"
                :options="dict.type.sys_operation_state"
              />
            </el-link>
          </template>
        </el-table-column>
        <el-table-column
          label="所在政区编码"
          align="center"
          prop="areaCode"
          min-width="140"
          show-overflow-tooltip
        />
        <el-table-column
          label="是否跨省隧道"
          align="center"
          prop="crossProvinceTunnel"
          min-width="140"
          show-overflow-tooltip
        >
          <template slot-scope="scope">
            <dict-tag
              :options="dict.type.base_data_yes_no"
              :value="scope.row.crossProvinceTunnel"
            />
          </template>
        </el-table-column>
        <el-table-column
          label="是否在长大隧道目录"
          align="center"
          prop="inLongTunnelDirectory"
          min-width="150"
          show-overflow-tooltip
        >
          <template slot-scope="scope">
            <dict-tag
              :options="dict.type.base_data_yes_no"
              :value="scope.row.inLongTunnelDirectory"
            />
          </template>
        </el-table-column>
        <el-table-column
          label="是否水下隧道"
          align="center"
          prop="underwaterTunnel"
          min-width="140"
          show-overflow-tooltip
        >
          <template slot-scope="scope">
            <dict-tag
              :options="dict.type.base_data_yes_no"
              :value="scope.row.underwaterTunnel"
            />
          </template>
        </el-table-column>

        <el-table-column
          label="是否锁定"
          align="center"
          prop="isLocked"
          min-width="140"
          show-overflow-tooltip
        >
          <template slot-scope="{ row }">
            <el-link
              :underline="false"
              :type="row.isLocked ? 'danger' : 'info'"
              @click.stop="handleLocked(row)"
            >
              {{ row.isLocked ? "是" : "否" }}
            </el-link>
          </template>
        </el-table-column>
        <el-table-column
          label="设计时速(km/h)"
          align="center"
          prop="designSpeed"
          min-width="140"
          show-overflow-tooltip
        />
        <el-table-column
          label="衬砌类型"
          align="center"
          prop="liningType"
          min-width="140"
          show-overflow-tooltip
        >
          <template slot-scope="scope">
            <dict-tag
              :options="dict.type.tunnel_lining_type"
              :value="scope.row.liningType"
            />
          </template>
        </el-table-column>
        <el-table-column
          label="围岩等级"
          align="center"
          prop="surroundingRockGrade"
          min-width="140"
          show-overflow-tooltip
        />
        <el-table-column
          label="数据状态"
          align="center"
          prop="status"
          min-width="140"
          show-overflow-tooltip
        >
          <template slot-scope="{ row }">
            <el-link
              :underline="false"
              :type="
                {
                  1: 'success',
                  2: 'primary',
                  3: 'info',
                  4: 'danger',
                  5: 'success',
                }[row.status]
              "
            >
              <DictTag
                :value="row.status"
                :options="dict.type.base_data_state"
              />
            </el-link>
          </template>
        </el-table-column>
        <!-- <el-table-column
          label="建成时间"
          align="center"
          prop="buildDate"
          min-width="140"
          show-overflow-tooltip
        /> -->
        <!-- <el-table-column
          label="交竣工图纸"
          align="center"
          prop="finishPapersPath"
          min-width="140"
          show-overflow-tooltip
        /> -->
        <el-table-column fixed="right" label="档案" align="center" width="50">
          <template slot-scope="scope">
            <el-button
              v-hasPermi="['baseData:tunnel:query']"
              type="text"
              @click="goArchivesPage(scope.row)"
              >查看</el-button
            >
          </template>
        </el-table-column>
      </el-table>
      <pagination
        :total="total"
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
        :pageSizes="[10, 20, 30, 50, 100, 1000]"
        @pagination="getList"
      />
    </template>
    <Form
      v-if="showAddEdit"
      :for-view="forView"
      :form-data="formData"
      :title="title"
      :show-form="showAddEdit"
      @close="
        () => {
          showAddEdit = false;
          formData = {};
        }
      "
      @refresh="
        () => {
          showAddEdit = false;
          getList();
        }
      "
    />
    <ImportData
      v-if="showImportAdd"
      :is-update="isUpdate"
      :dialog-visible="showImportAdd"
      :import-base-type="importBaseType"
      :import-type="importType"
      @close="closeImportAdd"
    />
    <Drawings
      v-if="showDrawing"
      :showDrawing="showDrawing"
      :typeCode="'BS130'"
      :id="assetIds[0]"
      title="隧道图纸文件"
      @close="showDrawing = false"
    />
    <Card
      :dialogVisible="showCard"
      :forView="forView"
      :title="title"
      :cardId="cardId"
      :cardCode="cardCode"
      @close="
        () => {
          showCard = false;
          forView = false;
          title = '';
        }
      "
    />
    <MapPosition
      v-if="showMapPosition"
      :dialogVisible="showMapPosition"
      :data="mapPositionData"
      @close="showMapPosition = false"
    />
    <drawer-panel
      :show.sync="panelShow"
      :title="panelTitle"
      size="87%"
      @close="onPanelClose"
    >
      <ArchiveIndex :key="archiveKey" />
    </drawer-panel>
    <DeviceList
      v-if="deviceForView"
      :deviceForView="deviceForView"
      :tunnelId="ids[0]"
      @close="deviceForView = false"
    />
  </PageContainer>
</template>

<script>
import {
  listStatic,
  getStatic,
  delStatic,
  importStatic,
  changeLockedStatus,
} from "@/api/baseData/tunnel/baseInfo/index";

import { audit } from "@/api/baseData/bridge/baseInfo/index";

import DeviceList from "./components/deviceList.vue";

import Form from "./form.vue";
import ImportData from "@/views/baseData/components/importData/index.vue";
import CascadeSelection from "@/components/CascadeSelection/index.vue";
import { statusDialog } from "@/views/baseData/components/statusDialog/index.js";
import { statusListDialog } from "@/views/baseData/components/statusDialog/list.js";
import Drawings from "@/views/baseData/components/drawings/index.vue";
import Card from "./components/card.vue";
import MapPosition from "@/components/mapPosition/index.vue";
import DrawerPanel from "@/components/RightPanel/drawer.vue";
import ArchiveIndex from "@/views/baseData/tunnel/baseInfo/archives/index.vue";
import { errorLog } from "@/settings";

export default {
  name: "BaseInfo",
  components: {
    Form,
    ImportData,
    CascadeSelection,
    statusDialog,
    Drawings,
    Card,
    MapPosition,
    DrawerPanel,
    ArchiveIndex,
    DeviceList,
  },
  dicts: [
    "tunnel_maintenance_grade",
    "sys_route_direction",
    "tunnel_length_classification",
    "tunnel_assess_grade",
    "base_data_yes_no",
    "sys_operation_state",
    "tunnel_lining_type",
    "base_data_state",
    "sys_route_grade",
  ],
  data() {
    return {
      loading: true,
      showAddEdit: false,
      forView: false,
      title: "",
      formData: {},
      showUpload: false,
      importBaseType: "2",
      isUpdate: false,
      importType: 0,
      ids: [],
      selectdTables: [],
      showSearch: false,
      showImportAdd: false,
      total: 0,
      staticList: null,
      queryParams: {
        pageNum: 1,
        pageSize: 20,
        operationState: "2",
      },
      dateRange: [],
      showDrawing: false,
      showCard: false,
      cardId: "",
      cardCode: "",
      showMapPosition: false,
      mapPositionData: undefined,
      panelShow: false,
      panelTitle: "隧道档案",
      archiveKey: new Date().getTime(),
      deviceForView: false,
      assetIds: []
    };
  },
  watch: {},
  created() {
    this.getList();
  },
  methods: {
    // 获取表格数据
    getList() {
      this.loading = true;
      if (this.dateRange?.length > 0) {
        this.queryParams.constructionStartDate = this.dateRange[0];
        this.queryParams.constructionEndDate = this.dateRange[1];
      } else {
        this.queryParams.constructionStartDate = "";
        this.queryParams.constructionEndDate = "";
      }
      listStatic(this.queryParams)
        .then((response) => {
          this.staticList = response.rows;
          this.total = response.total;
          this.loading = false;
        })
        .catch(() => {
          this.loading = false;
        });
    },
    // 勾选高亮
    rowStyle({ row, rowIndex }) {
      if (this.ids.includes(row.id)) {
        return { "background-color": "#b7daff", color: "#333" };
      } else {
        return { "background-color": "#fff", color: "#333" };
      }
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.selectdTables = selection;
      this.ids = selection.map((item) => item.id);
      this.assetIds = selection.map((item) => item.assetId);
    },
    // 搜索按钮
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    deviceCheck() {
      if (this.ids.length === 1) {
        this.deviceForView = true;
      } else {
        this.$message.warning("请选择一条数据进行查看！");
      }
    },
    // 重置按钮
    resetQuery() {
      this.dateRange = [];
      this.queryParams = {
        pageNum: 1,
        pageSize: 20,
        operationState: "2",
      };
      this.handleQuery();
    },
    // 新增按钮操作
    handleAdd() {
      this.forView = false;
      this.showAddEdit = true;
      this.title = "新增隧道静态数据";
    },
    taskBridge() {
      //this.selectdTables数组中的status，只要有一个不是2或者4.就不能提交审核
      let flag = false;
      this.selectdTables.forEach((item, index) => {
        if (item.status != 2 && item.status != 4 && item.isLocked) {
          flag = true;
        }
      });
      if (this.ids.length === 0) {
        this.$message.warning("请选择至少一条数据进行审核！");
        return;
      } else if (flag) {
        this.$message.warning("此数据无法提交审核！");
        return;
      }
      this.$modal
        .confirm("确认审核？")
        .then(() => {
          let list = [];
          this.selectdTables.forEach((item, index) => {
            list.push({
              businessKey: item.id,
              variables: {
                managementMaintenanceId:item.managementMaintenanceId,
                managementMaintenanceBranchId:item.managementMaintenanceBranchId,
                maintenanceSectionId:item.maintenanceSectionId,
                assetId: item.assetId,
                type: "194",
                assetCode: item.tunnelCode,
                assetName: item.tunnelName,
                routeCode: item.routeCode,
                routeName: item.routeName,
                maintenanceSectionName: item.maintenanceSectionName,
                managementMaintenanceName: item.managementMaintenanceName,
                managementMaintenanceBranchName:
                  item.managementMaintenanceBranchName,
              },
            });
          });

          audit({
            businessDTOS: list,
            processDefinitionKey: "base_data_audit",
          }).then((res) => {
            if (res.code === 200) {
              this.getList();
              this.$modal.msgSuccess("审核成功");
            }
          });
        })
        .catch(() => {});
    },
    closeImportAdd(v) {
      this.showImportAdd = false;
      if (v) this.getList();
    },
    // 表格点击勾选
    handleRowClick(row) {
      row.isSelected = !row.isSelected;
      this.$refs.table.toggleRowSelection(row);
    },
    // 编辑按钮
    handleUpdate() {
      if (this.ids.length != 1) {
        this.$message.warning("请选择一条数据进行编辑！");
        return;
      } else {
        this.forView = false;
        let canEdit = true;
        this.staticList.map((e) => {
          if (this.ids[0] == e.id && e.isLocked) {
            canEdit = false;
            this.$message.error(
              "隧道名称：" + e.tunnelName + "已锁定，不允许编辑！"
            );
          }
        });
        if (canEdit) {
          getStatic(this.ids[0]).then((res) => {
            if (res && res.data) {
              this.formData = res.data;
              this.showAddEdit = true;
              this.title = "修改隧道静态数据";
            }
          });
        }
      }
    },
    // 是否锁定按钮
    handleLocked(row) {
      if (this.ids.length === 0 && !row?.id) {
        this.$message.warning("请选择至少一条数据！");
        return;
      }
      let arr = [];
      let ids = [];
      if (row.id) {
        arr = [row];
        ids = [row.id];
      } else {
        arr = JSON.parse(JSON.stringify(this.selectdTables));
        ids = this.ids;
      }
      const allTrue = arr.every((table) => table.isLocked === true);
      const allFalse = arr.every((table) => table.isLocked === false);
      if (allTrue || allFalse) {
        this.$modal
          .confirm(allFalse ? "确认锁定？" : "确认解锁？")
          .then(() => {
            changeLockedStatus({ ids: ids.join() }).then((res) => {
              if (res.code === 200) {
                this.getList();
                this.$modal.msgSuccess("操作成功");
              }
            });
          })
          .catch(() => {});
      } else {
        this.$message.warning("请选择锁定状态相同的数据进行操作！");
      }
    },
    // 删除按钮
    handleDelete() {
      if (this.ids.length == 0) {
        this.$message.warning("请选择至少一条数据进行删除！");
        return;
      }
      let canDel = true;
      this.ids.map((el) => {
        if (!canDel) {
          return;
        }
        this.staticList.map((e) => {
          if (el == e.id && e.isLocked) {
            canDel = false;
            this.$message.error(
              "隧道名称：" + e.tunnelName + "已锁定，不允许删除！"
            );
          }
        });
      });
      if (canDel) {
        this.$modal
          .confirm("确认删除？")
          .then(() => {
            delStatic(this.ids).then((res) => {
              if (res && res.code == "200") {
                this.getList();
                this.$modal.msgSuccess("删除成功");
              }
            });
          })
          .catch(() => {});
      }
    },

    taskExportAnnualReport() {
      if (this.ids.length === 0) {
        this.$modal
          .confirm("即将导出隧道年报数据，此过程可能花费时间较长，是否继续？")
          .then(() => {
            this.download(
              "/baseData/tunnel/exportAnnualReport",
              this.queryParams,
              `road_interflow_${new Date().getTime()}.xlsx`,
              {
                headers: { "Content-Type": "application/json;" },
                parameterType: "body",
              }
            );
          })
          .catch(() => {});
      } else {
        this.$modal
          .confirm(`已选择${this.ids.length}条隧道年报数据，确认导出数据？`)
          .then(() => {
            this.download(
              "/baseData/tunnel/exportAnnualReport",
              { ids: this.ids },
              `static_${new Date().getTime()}.xlsx`,
              {
                headers: { "Content-Type": "application/json;" },
                parameterType: "body",
              }
            );
          })
          .catch(() => {});
      }
    },

    // 导出清单按钮
    exportList() {
      if (this.ids.length === 0) {
        this.$modal
          .confirm("即将导出所有表格数据，此过程可能花费时间较长，是否继续？")
          .then(() => {
            this.download(
              "/baseData/tunnel/export",
              this.queryParams,
              `road_interflow_${new Date().getTime()}.xlsx`,
              {
                headers: { "Content-Type": "application/json;" },
                parameterType: "body",
              }
            );
          })
          .catch(() => {});
      } else {
        this.$modal
          .confirm(`已选择${this.ids.length}条隧道数据，确认导出清单？`)
          .then(() => {
            this.download(
              "/baseData/tunnel/export",
              { ids: this.ids },
              `static_${new Date().getTime()}.xlsx`,
              {
                headers: { "Content-Type": "application/json;" },
                parameterType: "body",
              }
            );
          })
          .catch(() => {});
      }
    },
    exportAllList() {
      if (this.ids.length === 0) {
        this.$modal
          .confirm("即将导出所有表格数据，此过程可能花费时间较长，是否继续？")
          .then(() => {
            this.download(
              "/baseData/tunnel/exportAll",
              this.queryParams,
              `road_interflow_${new Date().getTime()}.xlsx`,
              {
                headers: { "Content-Type": "application/json;" },
                parameterType: "body",
              }
            );
          })
          .catch(() => {});
      } else {
        this.$modal
          .confirm(`已选择${this.ids.length}条隧道数据，确认导出数据？`)
          .then(() => {
            this.download(
              "/baseData/tunnel/exportAll",
              { ids: this.ids },
              `static_${new Date().getTime()}.xlsx`,
              {
                headers: { "Content-Type": "application/json;" },
                parameterType: "body",
              }
            );
          })
          .catch(() => {});
      }
    },
    // 二维码下载
    downloadQrcode() {
      let msg =
        this.ids && this.ids.length > 0
          ? "确定下载选中数据相关二维码？"
          : "即将下载所有表格的二维码数据，此过程可能花费时间较长，是否继续？";
      this.$modal
        .confirm(msg)
        .then(() => {
          let list = JSON.parse(JSON.stringify(this.queryParams));
          list.ids = this.ids;

          this.download(
            "/baseData/tunnel/genQrCode",
            list,
            `二维码_${new Date().getTime()}.xlsx`,
            {
              headers: { "Content-Type": "application/json;" },
              parameterType: "body",
            }
          );
        })
        .catch(() => {});
    },
    // 查看按钮
    handleView() {
      if (this.ids.length != 1) {
        this.$message.warning("请选择一条数据！");
        return;
      } else {
        this.forView = true;
        getStatic(this.ids[0]).then((res) => {
          if (res && res.data) {
            this.formData = res.data;
            this.showAddEdit = true;
            this.title = "查看隧道数据";
          }
        });
      }
    },
    // 查看卡片按钮
    handleViewCard() {
      if (this.ids.length != 1) {
        this.$message.warning("请选择一条数据！");
        return;
      } else {
        this.forView = true;
        this.showCard = true;
        this.title = "查看隧道卡片";
        this.cardId = this.ids[0];
        this.cardCode = this.selectdTables[0].tunnelCode;
        // getStatic(this.ids[0]).then(res => {
        //   if (res && res.data) {
        //     this.formData = res.data
        //     this.showAddEdit = true
        //     this.title = '查看隧道数据'
        //   }
        // })
      }
    },
    // 编辑卡片按钮
    handleEditCard() {
      if (this.ids.length != 1) {
        this.$message.warning("请选择一条数据！");
        return;
      } else {
        this.forView = false;
        this.showCard = true;
        this.title = "编辑隧道卡片";
        this.cardId = this.ids[0];
        this.cardCode = this.selectdTables[0].tunnelCode;
        // getStatic(this.ids[0]).then(res => {
        //   if (res && res.data) {
        //     this.formData = res.data
        //     this.showAddEdit = true
        //     this.title = '查看隧道数据'
        //   }
        // })
      }
    },
    // 导出卡片按钮
    handleExportCard() {
      if (this.ids.length === 0) {
        this.$message.warning("请选择一条数据！");
      } else {
        let params = {
          ...this.searchForm,
          ids: this.ids,
        };
        this.$modal
          .confirm(`已选择${this.ids.length}条隧道数据，确认导出数据？`)
          .then(() => {
            this.download(
              "/baseData/tunnel/cardExport",
              params,
              `卡片_${new Date().getTime()}.xlsx`,
              {
                headers: { "Content-Type": "application/json;" },
                parameterType: "body",
              }
            );
          })
          .catch(() => {});
      }
    },
    // 查看图纸按钮
    handleViewDrawings() {
      if (this.ids.length !== 1) {
        this.$message.warning("请选择一条数据！");
        return;
      } else {
        this.showDrawing = true;
      }
    },
    // 表格操作-运营状态
    handleOperational(event, row) {
      event.stopPropagation();
      statusListDialog({ dataId: row.assetId, baseDataType: 2 });
    },
    // 运营状态变更按钮
    changeStatus() {
      if (this.ids.length !== 1) {
        this.$message.warning("请选择一条数据！");
        return;
      } else {
        // baseDataType 基础数据类型 1隧道 2隧道 3涵洞 4互通
        statusDialog({
          dataId: this.selectdTables[0].assetId,
          baseDataType: 2,
        }).then((res) => {
          if (res) {
            this.getList();
          }
        });
      }
    },
    // 导入更新按钮
    importUpdate() {
      this.isUpdate = true;
      this.showImportAdd = true;
      this.importType = 1;
    },
    // 导入新增按钮
    importAdd() {
      this.isUpdate = false;
      this.showImportAdd = true;
      this.importType = 2;
    },
    // 表格操作-定位
    handleLocation(row) {
      this.mapPositionData = row;
      this.showMapPosition = true;
    },
    // 表格操作-档案查看
    goArchivesPage(rows) {


       getStatic(rows.id).then(res => {
          if (res && res.data) {
            let row=res.data
            let { path } = this.$route;
            let query = {
              tunnelName: row.tunnelName,
              id: row.id,
              tunnelCode: row.tunnelCode,
              assetId: row.assetId,
              assetCode:row.assetCode,
              assetName:row.assetName,
              whetherHealthMonitorSystemType: row.whetherHealthMonitorSystemType,
            };
            this.$router.push({
              path,
              query,
            });
            // 刷新组件
            this.archiveKey = new Date().getTime();
            this.panelShow = true;
            return;
            this.$router.push(
              `baseInfo/archives?tunnelName=${row.tunnelName}&id=${row.id}&tunnelCode=${row.tunnelCode}`
            );
          }
        })


    },
    onPanelClose() {
      this.panelShow = false;
      let { path } = this.$route;
      this.$router.push({
        path,
        query: {},
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.first-divider {
  width: 100%;
  height: 1px;
  //border-bottom: 1px solid #dcdfe6;
  //margin: 10px 0 5px 0 !important;
}
.button-list {
  border-radius: 4px;
  width: 100%;
  .el-button {
    margin-bottom: 10px;
    margin-right: 10px;
    margin-left: 0;
  }
}
</style>
