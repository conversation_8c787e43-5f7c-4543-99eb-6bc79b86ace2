<template>
  <div class="app-container maindiv">
    <el-row :gutter="20" style="display: flex">
      <!--部门数据-->
      <maintenance-tree @rowClick="handleNodeClick" :realNav="realNav" @closeNav="realNav=false"></maintenance-tree>
      <!--角色数据-->
      <el-col :span="realNav ? 19 : 24" :xs="24">
        <!--展开图标-->
        <div class="rightIcon" @click="realNav = true" v-show="!realNav">
          <span class="el-icon-caret-right"></span>
        </div>
        <el-row>
          <el-col :span="24" :xs="24">
            <el-row>
              <el-form
                ref="queryForm"
                :inline="true"
                :model="queryParams"
                label-width="68px"
                size="mini"
              >
                <el-form-item label="" prop="year">
                  <el-date-picker
                    v-model="queryParams.year"
                    placeholder="年份"
                    style="width: 240px"
                    type="year"
                    value-format="yyyy"
                  >
                  </el-date-picker>
                </el-form-item>
                <el-form-item>
                  <selectTree
                    :key="'domainId'"
                    v-model="queryParams.domainId"
                    :deptType="100"
                    :deptTypeList="[1, 3, 4]"
                    clearable
                    filterable
                    placeholder="管养单位"
                    style="width: 240px"
                  />
                </el-form-item>
                <el-form-item>
                  <selectTree
                    :key="'constructionUnit'"
                    v-model="queryParams.checkDomainId" :data-rule="false"
                    clearable
                    filterable
                    :dept-type="100"
                    :filter-keys="['云南省交通投资建设集团有限公司', '云南交投投资有限公司']"
                    :expand-all="false"
                    placeholder="施工单位"
                    style="width: 240px"
                  />
                </el-form-item>
                <el-form-item>
                  <RoadSection
                    v-model="queryParams.maiSecId"
                    :deptId="queryParams.domainId"
                    placeholder="路段"
                  />
                </el-form-item>
                <el-form-item>
                  <el-input
                    v-model="queryParams.projectName"
                    placeholder="项目名称"
                    style="width: 240px"
                  />
                </el-form-item>
                <el-form-item>
                  <el-input
                    v-model="queryParams.constructionName"
                    placeholder="任务单名称"
                    style="width: 240px"
                  />
                </el-form-item>
                <el-form-item>
                  <el-input
                    v-model="queryParams.constructionCode"
                    placeholder="任务单编号"
                    style="width: 240px"
                  />
                </el-form-item>
                <el-form-item>
                  <el-input
                    v-model="queryParams.schemeCode"
                    placeholder="子目号"
                    style="width: 240px"
                  />
                </el-form-item>
                <el-form-item>
                  <dict-select
                    v-model="queryParams.calcStatus"
                    clearable
                    placeholder="计量情况"
                    style="width: 240px"
                    type="testing_calc_status"
                  ></dict-select>
                </el-form-item>

                <el-form-item>
                  <el-button
                    icon="el-icon-search"
                    size="mini"
                    type="primary"
                    @click="handleQuery"
                    >搜索</el-button
                  >
                  <el-button
                    icon="el-icon-refresh"
                    size="mini"
                    @click="resetQuery"
                    >重置</el-button
                  >
                  <el-button
                    v-show="!showSearch"
                    circle
                    icon="el-icon-arrow-down"
                    @click="showSearch = true"
                  ></el-button>
                  <el-button
                    v-show="showSearch"
                    circle
                    icon="el-icon-arrow-up"
                    @click="showSearch = false"
                  ></el-button>
                </el-form-item>
              </el-form>
              <!--默认折叠-->
              <el-col :span="24">
                <el-form
                  v-show="showSearch"
                  ref="queryForm"
                  :inline="true"
                  :model="queryParams"
                  label-width="68px"
                  size="small"
                >
                  <el-form-item>
                    <dict-select
                      v-model="queryParams.isGuarantee"
                      clearable
                      placeholder="请选择是否计算安全保通费"
                      style="width: 240px"
                      type="bridge_simple_bool"
                    ></dict-select>
                  </el-form-item>
                  <el-form-item>
                    <dict-select
                      v-model="queryParams.isProduction"
                      clearable
                      placeholder="请选择是否计算安全生产费"
                      style="width: 240px"
                      type="bridge_simple_bool"
                    ></dict-select>
                  </el-form-item>
                  <el-form-item>
                    <el-date-picker
                      v-model="queryParams.realEDate"
                      end-placeholder="完工日期"
                      range-separator="至"
                      start-placeholder="完工日期"
                      style="width: 240px"
                      type="daterange"
                      value-format="yyyy-MM-dd"
                    >
                    </el-date-picker>
                  </el-form-item>
                  <el-form-item>
                    <dict-select
                      v-model="queryParams.status"
                      clearable
                      placeholder="任务单状态"
                      style="width: 240px"
                      type="testing_calc_task_status"
                    ></dict-select>
                  </el-form-item>
                  <el-form-item>
                    <el-input
                      v-model="queryParams.intermediateCode"
                      placeholder="中间计量单编码"
                      style="width: 240px"
                    ></el-input>
                  </el-form-item>
                  <el-form-item>
                    <el-input
                      v-model="queryParams.settleCode"
                      placeholder="结算计量单编码"
                      style="width: 240px"
                    ></el-input>
                  </el-form-item>
                </el-form>
              </el-col>
            </el-row>
          </el-col>
        </el-row>
        <!--操作按钮区开始-->
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button
              type="danger"
              icon="el-icon-refresh-left"
              size="mini"
              v-has-permi="['check:construction:withdraw']"
              @click="handleWithdraw"
            >撤回
            </el-button>
          </el-col>
          <right-toolbar
            :showSearch.sync="showSearch"
            @queryTable="handleQuery"
            :columns="columns"
          ></right-toolbar>
        </el-row>
        <el-row>
          <div class="draggable">
            <el-table v-adjust-table
              size="mini"
              style="width: 100%"
              v-loading="loading"
              border
              :data="tableData"
              row-key="settleId"
              ref="dataTable"
              stripe
              highlight-current-row
              @expand-change="loadData"
              :height="
                showSearch ? 'calc(100vh - 330px)' : 'calc(100vh - 270px)'
              "
            >
              <el-table-column type="expand">
                <template slot-scope="props">
                  <el-table v-adjust-table :data="props.row.methodList" style="width: 100%">
                    <el-table-column align="center" label="" prop="">
                    </el-table-column>
                    <el-table-column
                      align="center"
                      label="子目号"
                      prop="schemeCode"
                    >
                    </el-table-column>
                    <el-table-column
                      align="center"
                      label="子目名称"
                      prop="schemeName"
                    >
                    </el-table-column>
                    <el-table-column align="center" label="单价" prop="price">
                    </el-table-column>
                    <el-table-column
                      align="center"
                      label="计算式"
                      prop="calcDesc"
                    >
                    </el-table-column>
                    <el-table-column align="center" label="数量" prop="num">
                    </el-table-column>
                    <el-table-column align="center" label="金额" prop="amount">
                    </el-table-column>
                    <el-table-column align="center" label="备注" prop="remark">
                    </el-table-column>
                  </el-table>
                  <pagination
                    v-show="props.row.totalNum > 0"
                    :limit.sync="props.row.queryData.pageSize"
                    :page.sync="props.row.queryData.pageNum"
                    :total="props.row.totalNum"
                    @pagination="getRowDetailList(props.row)"
                  />
                </template>
              </el-table-column>
              <el-table-column
                label="序号"
                align="center"
                type="index"
                width="50"
              />
              <template v-for="(column, index) in columns">
                <el-table-column
                  :label="column.label"
                  v-if="column.visible"
                  align="center"
                  :prop="column.field"
                  :width="column.width"
                  show-overflow-tooltip
                >
                  <template slot-scope="scope">
                    <dict-tag
                      v-if="column.dict"
                      :options="dict.type[column.dict]"
                      :value="scope.row[column.field]"
                    />
                    <template v-else-if="column.slots">
                      <RenderDom
                        :row="scope.row"
                        :index="index"
                        :render="column.render"
                      />
                    </template>
                    <span v-else-if="column.isTime">{{
                      parseTime(scope.row[column.field], "{y}-{m}-{d}")
                    }}</span>
                    <span v-else>{{ scope.row[column.field] }}</span>
                  </template>
                </el-table-column>
              </template>
              <el-table-column
                label="操作"
                fixed="right"
                align="center"
                width="250"
                class-name="small-padding fixed-width"
              >
                <template slot-scope="scope">
                  <el-button
                    type="text"
                    size="small"
                    v-has-menu-permi="['calccheck:method:edit']"
                    @click="openEditListDialog(scope.row)"
                    >编辑清单</el-button
                  >
                  <el-button
                    type="text"
                    size="small"
                    v-has-menu-permi="['calccheck:method:remove']"
                    @click="openDelItemDialog(scope.row)"
                    >删除新增单价子目</el-button
                  >
                </template>
              </el-table-column>
            </el-table>
            <pagination
              v-show="total > 0"
              :total="total"
              :page.sync="queryParams.pageNum"
              :limit.sync="queryParams.pageSize"
              @pagination="handleQuery"
            />
          </div>
        </el-row>
      </el-col>
    </el-row>
    <el-dialog title="编辑清单" :visible.sync="editListDialog" width="80%">
      <el-table v-adjust-table
        :data="settleMethodList"
        border
        height="200px"
        ref="tableRef"
        style="width: 100%"
      >
        <el-table-column label="序号" align="center" type="index" width="50" />
        <el-table-column
          prop="schemeCode"
          align="center"
          label="子目号"
          width="100"
        >
        </el-table-column>
        <el-table-column
          prop="schemeName"
          align="center"
          label="子目名称"
          width="100"
        >
           <template slot-scope="scope">
              <el-input v-model="scope.row.schemeName">
              </el-input>
          </template>
        </el-table-column>
        <el-table-column prop="unit" align="center" label="单位" width="100">
          <template slot-scope="scope">
              <el-input v-model="scope.row.unit">
              </el-input>
          </template>
        </el-table-column>
        <el-table-column prop="price" align="center" label="单价" width="100">
        </el-table-column>
        <el-table-column
          prop="calcDesc"
          align="center"
          label="计算式"
          width="100"
        >
          <template slot-scope="scope">
            <el-input
              v-model="scope.row.calcDesc"
              @change="changeCalculation(scope.row)"
            >
            </el-input>
          </template>
        </el-table-column>
        <el-table-column prop="num" align="center" label="方法数量" width="100">
          <template slot-scope="scope">
            <el-input
              v-model="scope.row.num"
              @change="changeSchemeNum(scope.row)"
            >
            </el-input>
          </template>
        </el-table-column>
        <el-table-column prop="amount" align="center" label="资金" width="100">
        </el-table-column>
        <el-table-column prop="calcStatus" label="是否计量">
          <template slot-scope="scope">
            <dict-tag
              :options="dict.type.testing_calc_status"
              :value="scope.row.calcStatus"
            />
          </template>
        </el-table-column>
        <el-table-column prop="remark" align="center" label="备注" width="100">
          <template slot-scope="scope">
            <el-input v-model="scope.row.remark"> </el-input>
          </template>
        </el-table-column>
      </el-table>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitEditList">保存</el-button>
        <el-button @click="editListDialog = false">退出</el-button>
      </span>
    </el-dialog>
    <el-dialog
      title="删除新增单价子目"
      :visible.sync="delItemDialog"
      width="80%"
    >
      <el-table v-adjust-table
        :data="settleMethodList"
        border
        height="200px"
        ref="delTableRef"
        @selection-change="handleSelectionChange"
        style="width: 100%"
      >
        <el-table-column type="selection" width="55"> </el-table-column>
        <el-table-column label="序号" align="center" type="index" width="50" />
        <el-table-column
          prop="schemeCode"
          align="center"
          label="子目号"
          width="100"
        >
        </el-table-column>
        <el-table-column
          prop="schemeName"
          align="center"
          label="子目名称"
          width="100"
        >
        </el-table-column>
        <el-table-column prop="unit" align="center" label="单位" width="100">
        </el-table-column>
        <el-table-column prop="price" align="center" label="单价" width="100">
        </el-table-column>
        <el-table-column
          prop="calcDesc"
          align="center"
          label="计算式"
          width="100"
        />
        <el-table-column
          prop="num"
          align="center"
          label="方法数量"
          width="100"
        />
        <el-table-column prop="amount" align="center" label="资金" width="100">
        </el-table-column>
        <el-table-column prop="calcStatus" label="是否计量" />
        <el-table-column
          prop="remark"
          align="center"
          label="备注"
          width="100"
        />
      </el-table>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitDelItem">删除</el-button>
        <el-button @click="delItemDialog = false">退出</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import selectTree from "@/components/DeptTmpl/selectTree.vue";
import MaintenanceTree from "@/components/MaintenanceTree/index.vue"
import {
  otherRepositoryList,
  fetchMethodList,
  fetchMethodListAll, withdraw,
} from "@/api/regularTesting/metering/settlement";
import {
  editSettleMethodList,
  delSettleMethod,
} from "@/api/regularTesting/metering/addPrice";
import RoadSection from "@/views/baseData/components/roadSection/index.vue";
import { Decimal } from 'decimal.js';

export default {
  name: "AddPrice",
  components: {
    RoadSection,
    selectTree,
    RenderDom: {
      functional: true,
      props: {
        row: Object,
        index: Number,
        render: Function,
      },
      render(createElement, ctx) {
        const { row, index } = ctx.props;
        return ctx.props.render(row, index);
      },
    },
    MaintenanceTree
  },
  dicts: [
    "testing_calc_status",
    "testing_calc_task_status",
  ],
  props: [],
  data() {
    return {
      leftTotal: 1,
      showSearch: false,
      queryParams: {
        pageNum: 1,
        pageSize: 50,
        addUnitPrice: 1,
      },
      total: 0,
      loading: false,
      columns: [
        {
          key: 0,
          width: 110,
          field: "status",
          label: `状态`,
          visible: true,
          dict: "testing_calc_task_status",
        },
        {
          key: 1,
          width: 100,
          field: "calcStatus",
          label: `计量情况`,
          visible: true,
          dict: "testing_calc_status",
        },
        {
          key: 2,
          width: 100,
          field: "constructionCode",
          label: `任务单编号`,
          visible: true,
        },
        {
          key: 3,
          width: 100,
          field: "constructionName",
          label: `任务单名称`,
          visible: true,
        },
        {
          key: 4,
          width: 80,
          field: "productionFund",
          label: `金额`,
          visible: true,
        },
        {
          key: 5,
          width: 100,
          field: "residueFund",
          label: `剩余金额`,
          visible: true,
        },
        {
          key: 6,
          width: 100,
          field: "projectName",
          label: `项目名称`,
          visible: true,
        },
        {
          key: 7,
          width: 100,
          field: "maiSecName",
          label: `路段名称`,
          visible: true,
        },
        {
          key: 8,
          width: 100,
          field: "domainName",
          label: `管养单位`,
          visible: true,
        },
        {
          key: 9,
          width: 100,
          field: "productionFund",
          label: `安全生产费`,
          visible: true,
          slots: true,
          render: (row) => {
            return (
                <span>{row.productionFund?.toFixed(0)}</span>
            );
          }
        },
        {
          key: 10,
          width: 100,
          field: "guaranteeFund",
          label: `安全保通费`,
          visible: true,
          slots: true,
          render: (row) => {
            return (
                <span>{row.guaranteeFund?.toFixed(0)}</span>
            );
          }
        },
        {
          key: 11,
          width: 100,
          field: "supFund",
          label: `监理费`,
          visible: true,
          slots: true,
          render: (row) => {
            return (
                <span>{row.supFund?.toFixed(0)}</span>
            );
          }
        },
        {
          key: 12,
          width: 100,
          field: "checkDomainName",
          label: `检测单位`,
          visible: true,
        },
        {
          key: 13,
          width: 100,
          field: "checkConName",
          label: `检测合同`,
          visible: true,
        },
        {
          key: 14,
          width: 100,
          field: "endTime",
          label: `完工日期`,
          visible: true,
        },
      ],
      tableData: [],
      rowData: {},
      // 左侧组织树
      realNav: true,
      keyword: "",
      relaOptions: [],
      filteredTreeData: [],
      editListDialog: false,
      delItemDialog: false,
      settleMethodList: [],
      delIds: []
    };
  },
  computed: {},
  watch: {},
  created() {
    this.handleQuery();
  },
  mounted() {},
  methods: {
    handleNodeClick(e) {
      this.queryParams.domainId = e.domainId || "";
      this.queryParams.maiSecId = e.maiSecId || "";
      this.handleQuery();
    },
    handleQuery() {
      this.loading = true;
      otherRepositoryList(this.queryParams).then((res) => {
        this.tableData = res.rows;
        this.total = res.total;
        this.loading = false;
        this.tableData.forEach((item) => {
          item.queryData = {
            settleId: item.settleId,
            pageNum: 1,
            pageSize: 10,
          };
          item.totalNum = 0;
        });
      });
    },
    resetQuery() {
      this.queryParams = {
        pageNum: 1,
        pageSize: 50,
        addUnitPrice: 1,
      };
      this.handleQuery()
    },
    loadData(row, expandedRows) {
      const isExpand = expandedRows.some(
        (item) => row.settleId === item.settleId
      );
      if (isExpand) {
        this.getRowDetailList(row);
      }
    },
    getRowDetailList(row) {
      fetchMethodList(row.queryData).then((res) => {
        this.$set(row, "totalNum", res.total || 0);
        this.$set(row, "methodList", res.rows);
      });
    },
    // 打开 编辑清单弹窗
    openEditListDialog(row) {
      const params = {
        settleId: row.settleId,
        flag: 0,
      };
      fetchMethodListAll(params).then((res) => {
        this.settleMethodList = res.data;
        this.editListDialog = true;
      });
    },
    // 保存编辑清单
    submitEditList() {
      editSettleMethodList(this.settleMethodList).then((res) => {
        this.$message.success("保存成功");
        this.editListDialog = false;
      });
    },
    // 打开新增单价子目弹窗
    openDelItemDialog(row) {
      const params = {
        settleId: row.settleId,
        flag: 1,
      };
      fetchMethodListAll(params).then((res) => {
        this.settleMethodList = res.data;
        this.delItemDialog = true;
      });
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.delIds = selection.map(item => item.settleId)
    },
    // 删除选中新增单价子目
    submitDelItem() {
      delSettleMethod({settleIds: this.delIds}).then(res=> {
        this.$message.success("保存成功");
        this.delItemDialog = false
      })
    },
    changeCalculation(row) {
      if (!this.isValidMathFormula(row.calcDesc)) {
        this.$modal.msgError('计算式错误，请检查')
        return
      }
      this.$set(row, 'num', this.ceilToTwo(this.math.evaluate(row.calcDesc), row.decimalPlaces))
      this.$set(row, "amount", Math.round(new Decimal(row.num || 0).times(row.price || 0).toNumber()));
      this.total = this.formData.settleMethodList.reduce(
        (acc, curr) => acc + curr.amount,
        0
      );
    },
    changeSchemeNum(row) {
      this.$set(row, "amount", Math.round(new Decimal(row.num || 0).times(row.price || 0).toNumber()));
      this.total = this.formData.settleMethodList.reduce(
        (acc, curr) => acc + curr.amount,
        0
      );
    },
    handleWithdraw() {
      if (!this.rowData.constructionId) {
        this.$message.warning('请先选择一条记录！')
        return
      }
      this.$confirm('确定撤回吗？', '警告', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        const params = {
          businessKey: this.rowData.constructionId
        }
        withdraw(params).then(res => {
          this.$message.success('撤回成功')
          this.handleQuery()
        })
      })
    },
  },
};
</script>

<style lang="scss" scoped>


.rightIcon {
  border: 1px solid #dcdfe6;
  border-radius: 8px;
  width: 16px;
  height: 50px;
  line-height: 50px;
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
  position: absolute;
  left: -10px;
  top: 280px;
  z-index: 10;
  background: white;
}

.rightIcon:hover {
  background-color: #dcdfe6;
}

.left-total {
  font-size: 13px;
  line-height: 28px;
  color: #606266;
  position: absolute;
  bottom: 10px;
  right: 10px;
}
</style>
<style lang="scss" scoped>
@import "@/assets/styles/business.scss";
</style>
