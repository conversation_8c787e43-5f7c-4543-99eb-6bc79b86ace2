<template>
  <div>
    <el-dialog
      :title="title"
      :visible.sync="dialogVisible"
      :before-close="handleClose"
      :close-on-click-modal="false"
      width="80%"
    >
      <div style="overflow-y: auto;height: 65vh;">
        <el-descriptions
          :label-style="{'display': 'none'}"
          :column="1"
          border
        >
          <el-descriptions-item>A.{{ '桥梁所处行政区划编码:' + form.areaCode }}</el-descriptions-item>
          <el-descriptions-item>B.行政识别数据</el-descriptions-item>
        </el-descriptions>
        <el-descriptions
          :label-style="labelStyle"
          :content-style="contentStyle"
          :column="3"
          border
        >
          <el-descriptions-item
            v-for="(item, index) in administrativeCardData"
            :key="Math.floor(Math.random() * 90000)+index"
            :label="item.index"
          >
            <el-row>
              <el-col
                :span="12"
                style="text-align: center; border-right: 1px solid #e6ebf5;height: 100%;line-height: 41px;"
              >
                {{ item.label }}
              </el-col>
              <el-col
                :span="12"
                style="text-align: center;height: 100%;line-height: 41px;"
              >
                <el-input
                  v-if="item.cardType == 'input'"
                  v-model="form[item.prop]"
                  class="bridge-card-input"
                  style="width: 100%; border: 0"
                  :disabled="forView"
                />
                <!-- <el-input-number
                  v-else-if="item.cardType === 'inputNumber'"
                  v-model="form[item.prop]"
                  class="bridge-card-input"
                  style="width: 100%"
                  controls-position="right"
                  :precision="item.precision"
                  :disabled="forView"
                /> -->
                <el-input-number
                  v-else-if="item.cardType === 'inputNumber'"
                  style="width: 100%"
                  v-model="form[item.prop]"
                  :precision="item.precision"
                  :min="item.min"
                  :max="item.max"
                  :disabled="forView"
                ></el-input-number>

                <el-select
                  v-else-if="item.cardType === 'select' && item.dict"
                  style="width: 100%"
                  v-model="form[item.prop]"
                  placeholder="请选择"
                  clearable
                  :disabled="forView"
                >
                  <el-option
                    v-for="dict in dict.type[item.dict]"
                    :key="dict.value"
                    :label="dict.label"
                    :value="dict.value"
                  >
                  </el-option>
                </el-select>

                <el-date-picker
                  v-else-if="item.cardType === 'date'"
                  v-model="form[item.prop]"
                  class="bridge-card-input"
                  style="width:100%"
                  type="date"
                  :placeholder="item.placeholder"
                  value-format="yyyy-MM-dd"
                  :disabled="forView"
                />
                <span v-else>{{ form[item.prop] }}</span>
              </el-col>
            </el-row>
          </el-descriptions-item>
        </el-descriptions>
        <el-descriptions
          :label-style="{'display': 'none'}"
          :content-style="{'border-top': '0'}"
          :column="1"
          border
        >
          <el-descriptions-item>C.桥梁技术指标</el-descriptions-item>
        </el-descriptions>
        <el-descriptions
          v-for="(item, index) in technologyCardData"
          :key="Math.floor(Math.random() * 90000)+index"
          :label-style="labelStyle"
          :content-style="contentStyle"
          :column="item.column"
          border
        >
          <el-descriptions-item
            v-for="(i, idx) in item.list"
            :key="Math.floor(Math.random() * 90000)+idx"
            :label="i.index"
          >
            <el-row>
              <el-col
                :span="i.cardType =='add' ? 6 : 12"
                style="text-align: center; border-right: 1px solid #e6ebf5;height: 100%;line-height: 41px;"
              >
                {{ i.label }}
              </el-col>
              <el-col
                :span="i.cardType =='add' ? 18 : 12"
                style="text-align: center;height: 100%;line-height: 41px;"
              >
                <el-input
                  v-if="i.cardType == 'input'"
                  v-model="form[i.prop]"
                  class="bridge-card-input"
                  style="width: 100%; border: 0"
                  :disabled="forView"
                />
                <el-select
                  v-else-if="i.cardType === 'select' && i.dict"
                  style="width: 100%"
                  v-model="form[i.prop]"
                  placeholder="请选择"
                  clearable
                  :disabled="forView"
                >
                  <el-option
                    v-for="dict in dict.type[i.dict]"
                    :key="dict.value"
                    :label="dict.label"
                    :value="dict.value"
                  >
                  </el-option>
                </el-select>
                <!-- <el-input-number
                  v-else-if="i.cardType === 'inputNumber'"
                  v-model="form[i.prop]"
                  class="bridge-card-input"
                  style="width: 100%"
                  controls-position="right"
                  :precision="i.precision"
                  :disabled="forView"
                /> -->
                <el-input-number
                  v-else-if="item.cardType === 'inputNumber'"
                  style="width: 100%"
                  v-model="form[item.prop]"
                  :precision="item.precision"
                  :disabled="forView"
                  :min="item.min"
                  :max="item.max"
                ></el-input-number>
                <span v-else-if="i.cardType === 'add'">{{ form[i.prop] }}
                  <i
                    v-if="!forView"
                    class="el-icon-circle-plus-outline"
                    style="color: #1890ff;cursor: pointer;"
                  />
                </span>
                <span v-else>{{ form[i.prop] }}</span>
              </el-col>
            </el-row>
          </el-descriptions-item>
        </el-descriptions>
        <el-descriptions
          :label-style="{'display': 'none'}"
          :content-style="{'border-top': '0'}"
          :column="1"
          border
        >
          <el-descriptions-item>D.桥梁结构信息</el-descriptions-item>
        </el-descriptions>
        <el-descriptions
          v-for="(item, index) in informationCardData.filter(card => card.cardType === 'add')"
          :key="index"
          :label-style="labelStyle"
          :content-style="contentStyle"
          :column="item.column"
          border
        >
          <el-descriptions-item
            v-for="(i, idx) in item.list"
            :key="Math.floor(Math.random() * 90000)+idx"
            :label="i.cardType =='tree' ? i.label : i.index"
          >
            <el-row v-if="i.cardType =='add'">
              <el-col
                :span="6"
                style="text-align: center; border-right: 1px solid #e6ebf5;height: 100%;line-height: 41px;"
              >
                {{ i.label }}
              </el-col>
              <el-col
                :span="18"
                style="text-align: center;height: 100%;line-height: 41px;"
              >
                <span v-if="i.cardType === 'add'">{{ form[i.prop] }}
                  <i
                    v-if="!forView"
                    class="el-icon-circle-plus-outline"
                    style="color: #1890ff;cursor: pointer;"
                  />
                </span>
              </el-col>
            </el-row>

          </el-descriptions-item>
        </el-descriptions>
        <el-row
          style="border: 1px solid #e6ebf5;border-top: 0;border-bottom: 0;"
          class="elrowhead"
          :key="Math.floor(Math.random() * 90000)+index"
          v-for="(item, index) in informationCardData.filter(card => card.cardType == 'tree')"
        >
          <el-col
            :span="4"
            class="elcolhead"
          >
            {{ item.label }}
          </el-col>

          <el-col :span="20">
            <el-descriptions
              :label-style="labelStyle"
              :content-style="contentStyle"
              :column="1"
              border
            >
              <el-descriptions-item
                labelClassName="labelClassName"
                v-for="(i, idx) in item.list"
                :key="Math.floor(Math.random() * 90000)+idx"
                :label="i.index"
              >
                <el-row>
                  <el-col
                    :span="4"
                    style="text-align: center; border-right: 1px solid #e6ebf5;height: 100%;line-height: 41px;"
                  >
                    {{ i.label }}
                  </el-col>
                  <el-col
                    :span="20"
                    style="text-align: center;height: 100%;line-height: 41px;"
                  >
                    <el-input
                      v-if="i.cardType == 'input'"
                      v-model="form[i.prop]"
                      class="bridge-card-input"
                      style="width: 100%; border: 0"
                      :disabled="forView"
                    />
                    <el-select
                      v-else-if="i.cardType === 'select' && i.dict"
                      style="width: 100%"
                      v-model="form[i.prop]"
                      placeholder="请选择"
                      clearable
                      :disabled="forView"
                    >
                      <el-option
                        v-for="dict in dict.type[i.dict]"
                        :key="dict.value"
                        :label="dict.label"
                        :value="dict.value"
                      >
                      </el-option>
                    </el-select>
                    <!-- <el-input-number
                    v-else-if="i.cardType === 'inputNumber'"
                    v-model="form[i.prop]"
                    class="bridge-card-input"
                    style="width: 100%"
                    controls-position="right"
                    :precision="item.precision"
                    :disabled="forView"
                  /> -->
                    <el-input-number
                      v-else-if="item.cardType === 'inputNumber'"
                      style="width: 100%"
                      v-model="form[item.prop]"
                      :precision="item.precision"
                      :disabled="forView"
                      :min="item.min"
                      :max="item.max"
                    ></el-input-number>
                    <el-date-picker
                      v-else-if="i.cardType === 'date'"
                      v-model="form[i.prop]"
                      class="bridge-card-input"
                      style="width:100%"
                      type="date"
                      :placeholder="i.placeholder"
                      value-format="yyyy-MM-dd"
                      :disabled="forView"
                    />
                    <span v-else>{{ form[i.prop] }}</span>
                  </el-col>
                </el-row>
              </el-descriptions-item>
            </el-descriptions>
          </el-col>
        </el-row>
        <el-descriptions
          :label-style="{'display': 'none'}"
          :column="1"
          border
        >
          <el-descriptions-item>E.档案资料(全、不全或无)</el-descriptions-item>
        </el-descriptions>
        <el-descriptions
          :label-style="labelStyle"
          :content-style="contentStyle"
          :column="3"
          border
        >
          <el-descriptions-item
            v-for="(item, index) in profileCardData"
            :key="Math.floor(Math.random() * 90000)+index"
            :label="item.index"
          >
            <el-row>
              <el-col
                :span="8"
                style="text-align: center; border-right: 1px solid #e6ebf5;height: 100%;line-height: 41px;"
              >
                {{ item.label }}
              </el-col>
              <el-col
                :span="16"
                style="text-align: center;height: 100%;line-height: 41px;"
              >
                <el-input
                  v-if="item.cardType == 'input'"
                  v-model="form[item.prop]"
                  class="bridge-card-input"
                  style="width: 100%; border: 0"
                  :disabled="forView"
                />
                <el-select
                  v-else-if="item.cardType === 'select' && item.dict"
                  style="width: 100%"
                  v-model="form[item.prop]"
                  placeholder="请选择"
                  clearable
                  :disabled="forView"
                >
                  <el-option
                    v-for="dict in dict.type[item.dict]"
                    :key="dict.value"
                    :label="dict.label"
                    :value="dict.value"
                  >
                  </el-option>
                </el-select>
                <!-- <el-input-number
                  v-else-if="item.cardType === 'inputNumber'"
                  v-model="form[item.prop]"
                  class="bridge-card-input"
                  style="width: 100%"
                  controls-position="right"
                  :precision="item.precision"
                  :disabled="forView"
                /> -->
                <el-input-number
                  v-else-if="item.cardType === 'inputNumber'"
                  style="width: 100%"
                  v-model="form[item.prop]"
                  :precision="item.precision"
                  :disabled="forView"
                  :min="item.min"
                  :max="item.max"
                ></el-input-number>
                <el-date-picker
                  v-else-if="item.cardType === 'date'"
                  v-model="form[item.prop]"
                  class="bridge-card-input"
                  style="width:100%"
                  type="date"
                  :placeholder="item.placeholder"
                  value-format="yyyy-MM-dd"
                  :disabled="forView"
                />
                <span v-else>{{ form[item.prop] }}</span>
              </el-col>
            </el-row>
          </el-descriptions-item>
        </el-descriptions>
        <div class="GTitle">F.桥梁检测评定历史</div>
        <el-table
          height="30%"
          border
          :data="staticList"
          :header-cell-style="{'height': '36px'}"
        >

          <el-table-column
            label="72.评定时间"
            align="center"
            prop="assessDate"
            min-width="60"
            show-overflow-tooltip
          />
          <el-table-column
            label="73.检测类别"
            align="center"
            prop="checkType"
            min-width="60"
            show-overflow-tooltip
          >


          </el-table-column>

          <el-table-column
            label="74.桥梁技术状况评定结果/特殊检查结论"
            align="center"
            prop="checkResult"
            min-width="90"
            show-overflow-tooltip
          >
            <template slot-scope="scope">
              <span v-if="scope.row && scope.row.checkResult && scope.row.checkType==='定期检查'">
                <DictTag
                  :value="scope.row.checkResult"
                  :options="dict.type.bridge_tec_condition_level"
                />
              </span>
              <span v-else>
                {{scope.row.checkResult}}
              </span>
            </template>
          </el-table-column>




          <el-table-column
            label="75.处治对策"
            align="center"
            prop="treatMeasure"
            min-width="140"
            show-overflow-tooltip
          />
          <el-table-column
            label="76.下次检测时间"
            align="center"
            prop="nextDetectionTime"
            min-width="60"
            show-overflow-tooltip
          />
        </el-table>
        <div class="GTitle">G.养护处治记录</div>
        <el-table
          height="30%"
          border
          :data="staticList1"
          :header-cell-style="{'height': '36px'}"
        >
          <el-table-column
            label="77.项目编码"
            align="center"
            prop="projectCode"
            min-width="130"
            show-overflow-tooltip
          />
          <el-table-column
            label="78.年度"
            align="center"
            prop="year"
            min-width="130"
            show-overflow-tooltip
          />
          <el-table-column
            label="79.预计工期"
            align="center"
            prop="estimatedDuration"
            min-width="130"
            show-overflow-tooltip
          />
          <el-table-column
            label="80.预计开始时间"
            align="center"
            prop="estimatedStartTime"
            min-width="130"
            show-overflow-tooltip
          />
          <el-table-column
            label="81.预计结束时间"
            align="center"
            prop="estimatedEndTime"
            min-width="130"
            show-overflow-tooltip
          />
          <el-table-column
            label="82.处治范围"
            align="center"
            prop="treatmentScope"
            min-width="130"
            show-overflow-tooltip
          />
          <el-table-column
            label="83.工程费用(万元)"
            align="center"
            prop="projectCost"
            min-width="160"
            show-overflow-tooltip
          />
          <el-table-column
            label="84.工程分类"
            align="center"
            prop="projectClassification"
            min-width="130"
            show-overflow-tooltip
          />
          <el-table-column
            label="85.缺陷责任期"
            align="center"
            prop="defectLiabilityPeriod"
            min-width="130"
            show-overflow-tooltip
          />
          <el-table-column
            label="86.上报人"
            align="center"
            prop="reporter"
            min-width="130"
            show-overflow-tooltip
          />
          <el-table-column
            label="87.备注"
            align="center"
            prop="remarks"
            min-width="130"
            show-overflow-tooltip
          />
        </el-table>
        <div class="GTitle">H.需要说明的事项(含桥梁管理处的变更情况)</div>
        <el-descriptions
          :label-style="{'display': 'none'}"
          :content-style="{'border-top': '0'}"
          :column="1"
          border
        >
          <el-descriptions-item>
            <el-row>
              <el-col
                :span="4"
                style="text-align: center; border-right: 1px solid #e6ebf5;height: 100%;line-height: 41px;"
              >
                88
              </el-col>
              <el-col
                :span="20"
                style="text-align: center;height: 100%;line-height: 41px;"
              >
                <el-input
                  v-model="form.explanationItem"
                  class="bridge-card-input"
                  style="width: 100%; border: 0;text-align: left;"
                  :disabled="forView"
                />
              </el-col>
            </el-row>
          </el-descriptions-item>
        </el-descriptions>

        <el-descriptions
          :label-style="labelStyle"
          :content-style="contentStyle"
          :column="3"
          border
        >
          <el-descriptions-item
            v-for="(item, index) in imageCardData"
            :key="Math.floor(Math.random() * 90000)+index"
            :label="item.index"
          >
            <el-row>
              <el-col
                :span="12"
                style="text-align: center; border-right: 1px solid #e6ebf5;height: 100%;line-height: 190px;"
              >
                {{ item.label }}
              </el-col>
              <el-col
                :span="12"
                style="text-align: center;height: 100%;line-height: 41px;"
              >
                <ImagePreview
                  style="width: 150px;height: 150px;margin-top: 25px;"
                  :owner-id="form[item.prop]"
                  v-model="form[item.prop]"
                  :limit="1"
                />
              </el-col>
            </el-row>
          </el-descriptions-item>
        </el-descriptions>
        <el-descriptions
          :label-style="labelStyle"
          :content-style="contentStyle"
          :column="3"
          border
        >
          <el-descriptions-item
            v-for="(item, index) in otherCardData"
            :key="Math.floor(Math.random() * 90000)+index"
            :label="item.index"
          >
            <el-row>
              <el-col
                :span="12"
                style="text-align: center; border-right: 1px solid #e6ebf5;height: 100%;line-height: 41px;"
              >
                {{ item.label }}
              </el-col>
              <el-col
                :span="12"
                style="text-align: center;height: 100%;line-height: 41px;"
              >
                <el-input
                  v-if="item.cardType == 'input'"
                  v-model="form[item.prop]"
                  class="bridge-card-input"
                  style="width: 100%; border: 0;text-align: center;"
                  :disabled="forView"
                />
                <el-input-number
                  v-else-if="item.cardType === 'inputNumber'"
                  style="width: 100%;text-align: center;"
                  v-model="form[item.prop]"
                  :precision="item.precision"
                  :disabled="forView"
                  :min="item.min"
                  :max="item.max"
                ></el-input-number>
                <el-date-picker
                  v-else-if="item.cardType === 'date'"
                  v-model="form[item.prop]"
                  class="bridge-card-input"
                  style="width:100%;text-align: center;"
                  type="date"
                  :placeholder="item.placeholder"
                  value-format="yyyy-MM-dd"
                  :disabled="forView"
                />
                <span v-else>{{ form[item.prop] }}</span>
              </el-col>
            </el-row>
          </el-descriptions-item>
        </el-descriptions>
      </div>
      <div slot="footer">
        <el-button
          v-if="!forView"
          type="primary"
          @click="handleSubmit"
        >保 存
        </el-button>
        <el-button @click="handleClose">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import dictionary from './cardDictionary'
import {listBridgePeriodicRecords} from '@/api/baseData/bridge/periodic/index'

export default {
  name: 'BridgeCard',
  components: {},
  props: {
    title: {
      type: String,
      default: '桥梁基本状况卡片'
    },
    bridgeTitle: {
      type: String || Number,
      default: ''
    },
    formData: {
      type: undefined, default: () => {
      }
    },
    dialogVisible: {
      type: Boolean,
      default: false
    },
    forView: {
      type: Boolean,
      default: false
    }
  },
  dicts: [
    'base_data_yes_no',
    'bridge_tec_condition_level',
    'bridge_main_superstructure_type',
    'sys_route_grade',
    'bridge_deck_pavement_types',
    'bridge_expansion_joint_types',
    'bridge_abutment_types',
    'bridge_pier_types',
    'bridge_base_types',
    'bridge_pier_collision_avoidance',
    'bridge_design_flood_frequency',
    'bridge_earthquake_acceleration',
    'bridge_approach_type',
    'bridge_function_type',
    'base_archival_data',
    'bridge_bearing_types',
    'sys_design_load'
  ],
  data() {
    return {
      labelStyle: {
        'border-top': '0',
        'background-color': '#FFFFFF',
        width: '70px',
        'text-align': 'center'
      },
      contentStyle: {'border-top': '0', padding: '0'},
      form: {},
      administrativeCardData: [],
      technologyCardData: [],
      informationCardData: [],
      profileCardData: [],
      otherCardData: [],
      staticList: [],
      staticList1: [],
      imageCardData: []
    }
  },
  computed: {},
  watch: {},
  created() {
    this.initForm()
    const data = JSON.parse(JSON.stringify(this.formData))
    this.form = data.bridge;
    this.staticList = data.multiFill_assessList;
    // listBridgePeriodicRecords({
    //   bridgeId: this.form.id,
    //   pageNum: 1,
    //   pageSize: 200
    // }).then(res => {
    //   this.staticList = res.rows
    // })
  },
  methods: {
    initForm() {
      this.administrativeCardData = dictionary.administrativeCardData
      this.technologyCardData = dictionary.technologyCardData
      this.informationCardData = dictionary.informationCardData
      this.profileCardData = dictionary.profileCardData
      this.imageCardData = dictionary.imageCardData
      this.otherCardData = dictionary.otherCardData
    },
    handleClose() {
      this.$emit('close')
    },
    handleSubmit() {
    }
  }
}
</script>

<style lang="scss" scoped>
.bridge-card-input {
  .el-input__inner {
    border: none;
    background-color: #fff !important;
    padding-right: 15px !important;
  }
  .el-input-number__increase,
  .el-input-number__decrease {
    display: none;
  }
}

//::v-deep .el-select .el-input.is-disabled .el-input__inner:hover{
//  text-align: center;
//}

::v-deep {
  .el-input__inner {
    border: 0;
  }
  .el-input.is-disabled .el-input__inner {
    background-color: white;
    border-color: #dfe4ed;
    color: #606266;
  }
}


.elcolhead {
  border-bottom: 1px solid rgb(230, 235, 245);
  align-items: center;
  display: flex;
  justify-content: center;
}

.elrowhead {
  display: flex;
}

.labelClassName {
  width: 150px;
}

.GTitle {
  font-size: 12px;
  font-weight: normal;
  border-left: 1px solid #e6ebf5;
  border-right: 1px solid #e6ebf5;
  height: 35px;
  line-height: 35px;
  padding-left: 10px;
  border-bottom: 1px solid #e6ebf5;
}
</style>
