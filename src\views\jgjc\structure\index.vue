<template>
  <div class='app-container maindiv'>
    <el-row :gutter='20'>
      <!--部门数据-->
      <el-col :span='relaNav ? 4 : 0' :xs='24' class='leftDiv'>
        <!--折叠图标-->
        <div class='leftIcon' @click='relaNav = false'>
          <span class='el-icon-caret-left'></span>
        </div>
        <div class='head-container'>
          <el-input
            v-model='keyword'
            clearable
            placeholder='输入关键词检索'
            prefix-icon='el-icon-search'
            size='small'
            style='margin-bottom: 20px'
            @change='handleSearch'
          />
        </div>
        <div class='left-total'>共 {{ leftTotal }} 条</div>
        <div class='head-container' style='width: 300px'>
          <el-tree
            ref='tree'
            :data='filteredTreeData'
            :default-expanded-keys='[1]'
            :expand-on-click-node='false'
            :filter-node-method='filterNode'
            :props='defaultProps'
            highlight-current
            node-key='code'
            @node-click='handleNodeClick'
          >
          </el-tree>
        </div>
      </el-col>
      <!--角色数据-->
      <el-col :span='relaNav ? 20 : 24' :xs='24'>
        <!--展开图标-->
        <div v-show='!relaNav' class='rightIcon' @click='relaNav = true'>
          <span class='el-icon-caret-right'></span>
        </div>
        <!--操作按钮区开始-->
        <el-row :gutter='10' class='mb8'>
          <el-col :span='24'>
            <el-form ref='queryForm' :inline='true' :model='queryParams' label-width='68px' size='small'>
              <el-form-item label=''>
                <el-input
                  v-model='queryParams.code'
                  placeholder='请输入结构物编码'
                  clearable
                  size='small'
                  @keyup.enter.native='handleQuery'
                ></el-input>
              </el-form-item>
              <el-form-item label=''>
                <el-input
                  v-model='queryParams.name'
                  placeholder='请输入结构物名称'
                  clearable
                  size='small'
                  @keyup.enter.native='handleQuery'
                ></el-input>
              </el-form-item>
              <el-form-item label='' prop='planStatus'>
                <dict-select v-model='queryParams.planStatus' placeholder='请选择预案状态' type='plan_status' />
              </el-form-item>
              <el-form-item label='' prop='status'>
                <dict-select v-model='queryParams.status' placeholder='请选择结构物状态' type='structure_status' />
              </el-form-item>
              <el-form-item label='' prop='isHasOffFlashDev'>
                <el-select v-model='queryParams.isHasOffFlashDev' placeholder='请选择设备在线状态' size='small'>
                  <el-option label='掉线' value='1'></el-option>
                  <el-option label='在线' value='0'></el-option>
                </el-select>
              </el-form-item>
              <el-form-item label='' prop='structureType'>
                <el-select
                  v-model='queryParams.structureType'
                  :style="{ width: '100%' }"
                  placeholder='请选择结构物类型'
                >
                  <el-option label='桥梁' value='桥梁' />
                  <el-option label='隧道' value='隧道' />
                  <el-option label='边坡' value='边坡' />
                </el-select>
              </el-form-item>
              <el-form-item>
                <el-button icon='el-icon-search' size='mini' type='primary' @click='handleQuery'>搜索</el-button>
                <el-button icon='el-icon-refresh' size='mini' @click='resetQuery'>重置</el-button>
              </el-form-item>
            </el-form>
          </el-col>
          <!--          <el-col :span='1.5'>-->
          <!--            <el-button-->
          <!--              icon='el-icon-plus'-->
          <!--              size='mini'-->
          <!--              type='primary'-->
          <!--              @click='handleMatch'-->
          <!--            >匹配-->
          <!--            </el-button-->
          <!--            >-->
          <!--          </el-col>-->
          <el-col :span='1.5'>
            <el-button
              icon='el-icon-plus'
              size='mini'
              type='primary'
              v-has-menu-permi="['jgjc:structure:add']"
              @click='openDetail'
            >新增
            </el-button
            >
          </el-col>
          <right-toolbar
            :columns='columns'
            :showSearch.sync='showSearch'
            @queryTable='handleQuery'
          ></right-toolbar>
        </el-row>
        <el-row>
          <div class='draggable'>
            <el-table
              ref='dataTable'
              v-loading='loading'
              :data='tableData'
              :height="'calc(100vh - 330px)'"
              border
              row-key='id'
              size='mini'
              stripe
              highlight-current-row
              style='width: 100%'
            >
              <el-table-column
                align='center'
                label='序号'
                type='index'
                width='50'
              />
              <template v-for='(column,index) in columns'>
                <el-table-column v-if='column.visible'
                                 :label='column.label'
                                 :prop='column.field'
                                 :width='column.width'
                                 :fixed='column.fixed'
                                 align='center'
                                 show-overflow-tooltip>
                  <template slot-scope='scope'>
                    <dict-tag v-if='column.dict' :options='dict.type[column.dict]' :value='scope.row[column.field]' />
                    <template v-else-if='column.slots'>
                      <RenderDom :index='index' :render='column.render' :row='scope.row' />
                    </template>
                    <span v-else-if='column.isTime'>{{ parseTime(scope.row[column.field], '{y}-{m}-{d}') }}</span>
                    <span v-else>{{ scope.row[column.field] }}</span>
                  </template>
                </el-table-column>
              </template>
              <el-table-column
                align='center'
                class-name='small-padding fixed-width'
                fixed='right'
                label='操作'
                width='280'
              >
                <template slot-scope='scope'>
                  <el-button
                    icon='el-icon-edit'
                    size='mini'
                    type='text'
                    v-has-menu-permi="['jgjc:structure:video']"
                    @click='handleVideoInfo(scope.row)'
                  >视频信息
                  </el-button
                  >
                  <el-button
                    icon='el-icon-edit'
                    size='mini'
                    type='text'
                    v-has-menu-permi="['jgjc:structure:flash']"
                    @click='handleOpenFlash(scope.row)'
                  >爆闪设备
                  </el-button
                  >
                  <el-button
                    icon='el-icon-edit'
                    size='mini'
                    type='text'
                    v-has-menu-permi="['jgjc:structure:gate']"
                    @click='handleOpenGate(scope.row)'
                  >道闸设备
                  </el-button>
                  <el-button
                    icon='el-icon-edit'
                    size='mini'
                    type='text'
                    v-has-menu-permi="['jgjc:structure:edit']"
                    @click='handleUpdate(scope.row)'
                  >修改
                  </el-button
                  >
                  <el-button
                    icon='el-icon-delete'
                    size='mini'
                    type='text'
                    v-has-menu-permi="['jgjc:structure:delete']"
                    @click='handleDelete(scope.row)'
                  >删除
                  </el-button
                  >
                </template>
              </el-table-column>
            </el-table>
            <pagination
              v-show='total > 0'
              :limit.sync='queryParams.pageSize'
              :page.sync='queryParams.pageNum'
              :total='total'
              @pagination='handleQuery'
            />
          </div>
        </el-row>
      </el-col>
    </el-row>
    <el-dialog :append-to-body='true' :destroy-on-close='true' :visible.sync='relaFlag' title='结构物'
               width='70%' @close='handleClose'>
      <el-form
        ref='elForm'
        :model='formData'
        :rules='rules'
        label-width='140px'
        size='medium'
        style='height: 80vh; overflow-y: scroll; overflow-x: hidden'
      >
        <el-row :gutter='15'>
          <el-col :span='12'>
            <el-form-item label='名称' prop='name'>
              <el-input
                v-model='formData.name'
                :style="{ width: '100%' }"
                clearable
                placeholder='请输入名称'
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span='12'>
            <el-form-item label='编码' prop='code'>
              <el-input
                v-model='formData.code'
                :style="{ width: '100%' }"
                clearable
                placeholder='请输入编码'
              ></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span='12'>
            <el-form-item label='结构物类型' prop='structureType'>
              <el-select
                v-model='formData.structureType'
                :style="{ width: '100%' }"
                placeholder='请选择结构物类型'
                :disabled='formData.id'
              >
                <el-option label='桥梁' value='桥梁' />
                <el-option label='隧道' value='隧道' />
                <el-option label='边坡' value='边坡' />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span='12'>
            <el-form-item label='风险类型' prop='riskType'>
              <dict-select v-model='formData.riskType' type='structure_rise_type' style='width: 100%;' />
<!--              <a-select :style="{ width: '100%' }" :defaultProps="{label: 'dictValue', value: 'dictValue'}" :api-method="listDictData" :params="{pageNum: 1,pageSize: 100,dictType: '边坡类型'}" v-model="formData.riskType"></a-select>-->
            </el-form-item>
          </el-col>
          <el-col :span='12'>
            <el-form-item label='风险等级' prop='riskLevel'>
              <dict-select v-model='formData.riskLevel' type='structure_rise_grade' style='width: 100%;' />
<!--              <a-select :style="{ width: '100%' }" :defaultProps="{label: 'dictValue', value: 'dictValue'}" :api-method="listDictData" :params="{pageNum: 1,pageSize: 100,dictType: '风险等级'}" v-model="formData.riskLevel"></a-select>-->
            </el-form-item>
          </el-col>
          <el-col :span='12'>
            <el-form-item label='实施单位' prop='implementUnit'>
              <dict-select v-model='formData.implementUnit' type='structure_implement_unit' style='width: 100%;' />
              <!--              <a-select :style="{ width: '100%' }" :defaultProps="{label: 'dictValue', value: 'dictValue'}" :api-method="listDictData" :params="{pageNum: 1,pageSize: 100,dictType: '实施单位'}" v-model="formData.implementUnit"></a-select>-->
            </el-form-item>
          </el-col>
          <el-col :span='12' v-if='formData.id'>
            <el-form-item label='是否有爆闪设备' prop='isHasFlashDev'>
              <dict-select v-model='formData.isHasFlashDev' :disabled='formData.id' :style="{ width: '100%' }"
                           type='is_has_flash_dev' />
            </el-form-item>
          </el-col>
          <el-col :span='12'>
            <el-form-item label='爆闪类型' prop='flashType'>
              <dict-select v-model='formData.flashType' :style="{ width: '100%' }"
                           type='flash_type' />
            </el-form-item>
          </el-col>
          <el-col :span='12' v-if='formData.id'>
            <el-form-item label='预案状态' prop='planStatus'>
              <dict-select v-model='formData.planStatus' :style="{ width: '100%' }" type='plan_status'
                           :disabled='formData.id' />
            </el-form-item>
          </el-col>
          <el-col :span='12' v-if='formData.id'>
            <el-form-item label='结构物状态' prop='status'>
              <dict-select v-model='formData.status' :style="{ width: '100%' }" type='structure_status'
                           :disabled='formData.id' />
            </el-form-item>
          </el-col>
          <el-col :span='12'>
            <el-form-item label='结构物位置' prop='position'>
              <el-input
                v-model='formData.position'
                :style="{ width: '100%' }"
                clearable
                placeholder='请输入结构物位置'
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span='12'>
            <el-form-item label='经度' prop='lon'>
              <el-input
                v-model='formData.lon'
                :style="{ width: '100%' }"
                clearable
                placeholder='请输入经度'
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span='12'>
            <el-form-item label='纬度' prop='lat'>
              <el-input
                v-model='formData.lat'
                :style="{ width: '100%' }"
                clearable
                placeholder='请输入纬度'
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span='12'>
            <el-form-item label='路段' prop='roadName'>
              <!--                  <el-input v-model='formData.roadName' placeholder="请输入路段名称" style="width: 100%;"></el-input>-->
              <el-select class='main-select-tree' ref='selectTree' v-model='formData.roadName' style='width: 100%;'
                         filterable>
                <el-option v-for='item in formatData(deptTree)' :key='item.value' :label='item.label'
                           :value='item.value' style='display: none;' />
                <el-tree
                  ref='tree'
                  :data='deptTree'
                  @node-click='handleDeptNodeClick'
                  :default-expanded-keys='[1]'
                  :expand-on-click-node='false'
                  :filter-node-method='filterNode'
                  :props='defaultProps'
                  highlight-current
                  node-key='code'
                >
                </el-tree>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span='12'>
            <el-form-item label='管养分处' prop='domainSubId'>
              <selectTree
                ref='subTree'
                :style="{width: '100%'}"
                v-model='formData.domainSubId'
                :deptType='101' :deptTypeList='[1, 3, 4]'
                :onlySelectChild='true'
                placeholder='管养分处'
                clearable
                filterable
              />
            </el-form-item>
          </el-col>
          <el-col :span='12'>
            <el-form-item label='资产id' prop='assetId'>
              <el-input v-model='formData.assetId' placeholder='请输入资产id' style='width: 100%;'></el-input>
            </el-form-item>
          </el-col>
          <el-col :span='12' v-if="formData.flashType == '第三方'">
            <el-form-item label='第三方厂家域名' prop='thirdPartyDomainName'>
              <el-input v-model='formData.thirdPartyDomainName' placeholder='请输入第三方厂家域名' style='width: 100%;'></el-input>
            </el-form-item>
          </el-col>
          <el-col :span='12' v-if="formData.flashType == '第三方'">
            <el-form-item label='第三方用户名' prop='thirdPartyUserName'>
              <el-input v-model='formData.thirdPartyUserName' placeholder='请输入第三方用户名' style='width: 100%;'></el-input>
            </el-form-item>
          </el-col>
          <el-col :span='12' v-if="formData.flashType == '第三方'">
            <el-form-item label='第三方用户名密码' prop='thirdPartyPassword'>
              <el-input v-model='formData.thirdPartyPassword' placeholder='请输入第三方用户名密码' style='width: 100%;'></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter='15'>
          <el-col :span='24'>
            <el-form-item label='说明' prop='description'>
              <editor v-model='formData.description' :min-height='192' :style="{ width: '100%' }" />
              <!--                  <el-input-->
              <!--                    v-model="formData.description"-->
              <!--                    :rows="3"-->
              <!--                    :style="{ width: '100%' }"-->
              <!--                    clearable-->
              <!--                    placeholder="请输入说明"-->
              <!--                    type="textarea"-->
              <!--                  ></el-input>-->
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter='15'>
          <el-col :span='24'>
            <el-form-item label='图片' prop='imgPath'>
              <file-upload platform='jgjc' ref='fileUpload' v-model='formData.imgPath' :limit='1' />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div class='mt10' style='width: 100%; text-align: right'>
        <el-button size='mini' type='primary' @click='handleSave'>保存</el-button>
        <el-button size='mini' @click='relaFlag = false'>退出</el-button>
      </div>
    </el-dialog>
    <el-dialog :append-to-body='true' :destroy-on-close='true' :visible.sync='matchFlag' title='选择结构物' width='70%'>
      <div style='height: 70vh; overflow-y: auto'>
        <el-form ref='queryForm' :inline='true' :model='matchParams' label-width='120px' size='small'>
          <el-form-item label='结构物类型' prop='structureType'>
            <el-select
              v-model='matchParams.structureType'
              :style="{ width: '100%' }"
              placeholder='请选择结构物类型'
            >
              <el-option label='桥梁' value='桥梁' />
              <el-option label='隧道' value='隧道' />
              <el-option label='边坡' value='边坡' />
            </el-select>
          </el-form-item>
          <el-form-item label=''>
            <el-button type='primary' @click='handleMatchQuery'>查询</el-button>
          </el-form-item>
        </el-form>
        <div class='draggable'>
          <el-table
            v-adjust-table
            ref='dataTable'
            v-loading='loading'
            :data='matchData'
            height='500px'
            border
            row-key='id'
            size='mini'
            stripe
            style='width: 100%'
            @selection-change='handleSelectionChange'
          >
            <el-table-column
              align='center'
              type='selection'
              width='50'
            />
            <el-table-column
              align='center'
              label='序号'
              type='index'
              width='50'
            />
            <template v-for='(column,index) in matchColumns'>
              <el-table-column v-if='column.visible'
                               :label='column.label'
                               :prop='column.field'
                               :width='column.width'
                               align='center'
                               show-overflow-tooltip>
                <template slot-scope='scope'>
                  <dict-tag v-if='column.dict' :options='dict.type[column.dict]' :value='scope.row[column.field]' />
                  <template v-else-if='column.slots'>
                    <RenderDom :index='index' :render='column.render' :row='scope.row' />
                  </template>
                  <span v-else-if='column.isTime'>{{ parseTime(scope.row[column.field], '{y}-{m}-{d}') }}</span>
                  <span v-else>{{ scope.row[column.field] }}</span>
                </template>
              </el-table-column>
            </template>
          </el-table>
          <pagination
            v-show='matchParamsTotal > 0'
            :limit.sync='matchParams.pageSize'
            :page.sync='matchParams.pageNum'
            :total='matchParamsTotal'
            @pagination='handleMatchQuery'
          />
          <div style='text-align: right'>
            <el-button type='primary' @click='saveMatch'>保存</el-button>
          </div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  addStructure, batchSave,
  delStructure,
  editStructure,
  getDomainTree, getMaintStructInfo,
  getManageDomainTree,
  listStructure, updateIsHasSensor,
} from '@/api/jgjc/structure/structure'
import { getToken } from '@/utils/auth'
import axios from 'axios'
import { v4 as uuidv4 } from 'uuid'
import { getFile } from '@/api/file'
import selectTree from '@/components/DeptTmpl/selectTree.vue'
import ASelect from '@/views/jgjc/component/aSelect/index.vue'
import { listDictData } from '@/api/jgjc/dataDict/commDict'

export default {
  name: 'structure',
  components: {
    ASelect,
    selectTree,
    RenderDom: {
      functional: true,
      props: {
        row: Object,
        index: Number,
        render: Function,
      },
      render(createElement, ctx) {
        const { row, index } = ctx.props
        return ctx.props.render(row, index)
      },
    },
  },
  props: [],
  dicts: ['plan_status', 'is_has_flash_dev', 'structure_status'],
  data() {
    return {
      leftTotal: 1,
      showSearch: false,
      loading: false,
      columns: [
        { key: 0, width: '200', field: 'name', label: '名称', visible: true, fixed: 'left' },
        { key: 1, width: '150', field: 'code', label: '编码', visible: true },
        { key: 2, width: '150', field: 'domainName', label: '管理处', visible: true },
        { key: 14, width: '150', field: 'domainSub', label: '管养分处', visible: true },
        { key: 3, width: '150', field: 'roadName', label: '路段', visible: true },
        { key: 4, width: '100', field: 'structureType', label: '结构物类型', visible: true },
        { key: 5, width: '100', field: 'flashType', label: '爆闪类型', visible: true },
        { key: 6, width: '100', field: 'planStatus', label: '预案状态', visible: true, dict: 'plan_status' },
        {
          key: 7,
          width: '200',
          field: 'isHasFlashDev',
          label: '是否有爆闪设备',
          visible: true,
          dict: 'is_has_flash_dev',
        },
        { key: 8, width: '100', field: 'status', label: '结构物状态', visible: true, dict: 'structure_status' },
        { key: 9, width: '150', field: 'position', label: '结构物位置', visible: true },
        {
          key: 10, width: '100', field: 'imgPath', label: '图片', visible: true, slots: true, render: (row, index) => {
            return (
              <img style='width: 80px;height: 45px'
                   src={row.imgUrl} preview-src-list={[row.imgUrl]}></img>
            )
          },
        },
        {
          key: 11,
          width: '100',
          field: 'isHasVideo',
          label: '是否有视频',
          visible: true,
          slots: true,
          render: (row, index) => {
            if (row.isHasVideo == 1) {
              return <div>有</div>
            } else {
              return <div>无</div>
            }
          },
        },
        {
          key: 12,
          width: '200',
          field: 'isHasOffFlashDev',
          label: '是否有离线爆闪设备',
          visible: true,
          slots: true,
          render: (row, index) => {
            if (row.isHasVideo == 1) {
              return <div>有</div>
            } else {
              return <div>无</div>
            }
          },
        },
        {
          key: 13, width: '150', field: 'isHasSensor', label: '是否有监测数据', visible: true,
          slots: true,
          render: (row, index) => {
            return <el-switch active-value='1' inactive-value='0' v-model={row.isHasSensor}
                              onChange={e => this.changeSensor(e, row)}></el-switch>
          },
        },
      ],
      tableData: [],
      // 左侧组织树
      relaNav: true,
      keyword: '',
      relaOptions: [],
      filteredTreeData: [],
      defaultProps: {
        children: 'children',
        label: 'label',
      },
      relaFlag: false,
      formData: {},
      total: 0,
      queryParams: {
        pageNum: 1,
        pageSize: 10,
      },
      // 养护路段
      rules: {
        name: [{ required: true, message: '请输入名称', trigger: 'blur' }],
        code: [{ required: true, message: '请输入编码', trigger: 'blur' }],
        // structureType: [{ required: true, message: '请输入结构物类型', trigger: 'blur' }],
        flashType: [
          { validator: this.validateFlashType, trigger: 'change' },
        ], planStatus: [{ required: true, message: '请选择预案状态', trigger: 'change' }],
        isHasFlashDev: [{ required: true, message: '请选择是否有爆闪设备', trigger: 'change' }],
        status: [{ required: true, message: '请选择结构物状态', trigger: 'change' }],
        // assetId: [{required: true, message: '请输入资产id', trigger: 'blur'}],
      },
      upload: {
        open: false,
        title: '',
        isUploading: false,
        headers: { Authorization: 'Bearer ' + getToken() },
        url: process.env.VUE_APP_BASE_API + '/file/upload',
      },
      fileList: [],
      deptTree: [],
      matchFlag: false,
      matchParamsTotal: 0,
      matchParams: {
        pageNum: 1,
        pageSize: 10,
        structureType: '桥梁',
      },
      matchData: [],
      matchColumns: [
        { key: 0, field: 'name', label: '名称', visible: true },
        { key: 1, field: 'code', label: '编码', visible: true },
        { key: 2, field: 'structureCode', label: '结构物编码', visible: true },
        { key: 3, field: 'lat', label: '纬度', visible: true },
        { key: 4, field: 'lon', label: '经度', visible: true },
        {
          key: 10, width: '100', field: 'imgPath', label: '图片', visible: true,
        },
      ],
      selection: [],
    }
  },
  computed: {},
  watch: {},
  created() {
    this.getDeptTree()
  },
  mounted() {
    this.handleQuery()
  },
  methods: {
    listDictData,
    handleVideoInfo(row) {
      const params = {
        structureId: row.id,
        structureType: row.structureType,
      }
      this.$router.push({
        path: '/jgjc/videoInfo',
        query: params,
      })
    },
    validateFlashType(rule, value, callback) {
      if (this.formData.isHasFlashDev === '1' && !value) {
        callback(new Error('请选择爆闪类型'))
      } else {
        callback()
      }
    },
    // 查询
    handleQuery() {
      this.loading = true
      listStructure(this.queryParams).then(res => {
        this.tableData = res.rows
        this.tableData.forEach(item => {
          if (item.imgPath) {
            getFile({ ownerId: item.imgPath }).then(async res => {
              if (res.code == 200) {
                this.$set(item, 'imgUrl', res.data?.thumbUrl)
              }
            }).finally(() => {
            })
          }
        })
        this.loading = false
        this.total = res.total
        this.$nextTick(() => {
          this.$refs.dataTable.doLayout()
        })
      })
    },
    // 修改
    handleUpdate(e) {
      this.formData = JSON.parse(JSON.stringify(e))
      // this.formData.description = this.formData.description.replaceAll('&xg', '/')
      this.fileList = [{ url: e.imgPath }]
      this.relaFlag = true
    },
    // 删除
    handleDelete(e) {
      this.$modal.confirm('是否确认删除').then(() => {
        this.loading = true
        const ids = [e.id]
        delStructure({ ids: ids }).then(res => {
          this.$modal.msgSuccess('删除成功')
          this.handleQuery()
        })
      })
    },
    // 新增
    openDetail() {
      if (!this.queryParams.roadIdF) {
        this.$modal.msgError('请先选择路段！')
        return
      }
      this.formData = {}
      this.formData.roadName = this.queryParams.roadName
      this.formData.domainName = this.queryParams.domainNameF
      this.formData.domainId = this.queryParams.domainIdF
      this.formData.roadId = this.queryParams.roadIdF
      this.relaFlag = true
    },
    // 保存
    handleSave() {
      this.$refs['elForm'].validate((valid) => {
        if (!valid) return
        if (this.formData.imgPath && Array.isArray(this.formData.imgPath)) {
          this.formData.imgPath = this.formData.imgPath[0]
        }
        this.formData.description = btoa(unescape(encodeURIComponent(this.formData.description)))
        if (this.formData.domainSubId) {
          this.formData.domainSub = this.$refs.subTree.getLabel()
        }
        if (this.formData.id) {
          editStructure(this.formData).then(res => {
            this.$modal.msgSuccess('保存成功')
            this.handleQuery()
          })
        } else {
          addStructure(this.formData).then(res => {
            this.$modal.msgSuccess('保存成功')
            this.handleQuery()
          })
        }
        this.relaFlag = false
      })
    },
    // 查询部门下拉树结构
    getDeptTree() {
      getManageDomainTree({}).then(response => {
        this.relaOptions = response.data.data.map(item => ({
          id: item.deptId,
          label: item.deptName,
          type: '管理处',
          children: item.children ? item.children.map(child => ({
            label: child.maintenanceSectionId,
            id: child.maintenanceSectionName,
            type: '路段',
          })) : [],
        }))
        this.filteredTreeData = [...this.relaOptions]
        this.deptTree = [...this.relaOptions]

      })
    },
    handleDeptNodeClick(data, node) {
      if (!data.children || data.children.length == 0) {
        this.formData.domainName = node.parent.data.label
        this.formData.domainId = node.parent.data.id
        this.$set(this.formData, 'roadName', node.data.label)
        this.formData.roadId = node.data.id
        this.$refs.selectTree.blur()
      }
    },
    // 关键词检索
    handleSearch() {
      const keyword = this.keyword.toLowerCase()
      this.filteredTreeData = this.relaOptions.filter(node => this.filterNode(node, keyword))
    },
    resetQuery() {
      this.queryParams = {
        pageNum: 1,
        pageSize: 10,
      }
      this.filteredTreeData = [...this.relaOptions]
      this.handleQuery()

    },
    filterNode(node, keyword) {
      if (node.content.indexOf(keyword) != -1) {
        return true
      }
      if (node.children) {
        return node.children.some(childNode => this.filterNode(childNode, keyword))
      }
      return false
    },
    handleNodeClick(e, node) {
      this.queryParams = {
        pageNum: 1,
        pageSize: 10,
      }
      if (e.type == '管理处') {
        this.queryParams.domainName = e.label
      } else if (e.type == '路段') {
        this.queryParams.roadName = e.label
        this.matchParams.maintenanceSectionId = e.id
        this.queryParams.domainNameF = node.parent.data.label
        this.queryParams.domainIdF = node.parent.data.id
        this.queryParams.roadIdF = node.data.id
      }
      this.handleQuery()
    },

    findNodeByCode(data, code) {
      for (const node of data) {
        if (node.code === code) {
          return node
        }
        if (node.children && node.children.length > 0) {
          const found = this.findNodeByCode(node.children, code)
          if (found) return found
        }
      }
      return null
    },
    findParentSection(data, targetCode, type) {
      const targetNode = this.findNodeByCode(data, targetCode)
      if (!targetNode) return null
      let currentParentCode = targetNode.parentCode
      while (currentParentCode) {
        const parentNode = this.findNodeByCode(data, currentParentCode)
        if (!parentNode) return null
        if (parentNode.type === type) {
          return parentNode
        }
        currentParentCode = parentNode.parentCode
      }
      return null
    },
    handleOpenFlash(row) {
      const params = {
        structureId: row.id,
        structureType: row.structureType,
        structureName: row.name
      }
      this.$router.push({
        path: '/jgjc/flashdevice',
        query: params,
      })
    },
    handleOpenGate(row) {
      const params = {
        structureId: row.id,
        structureType: row.structureType,
        structureName: row.name
      }
      this.$router.push({
        path: '/jgjc/gatedevice',
        query: params,
      })
    },
    handleClose() {
      if (this.formData.id) {
        this.formData = {}
        this.fileList = []
      }
    },
    // 四级菜单
    formatData(data) {
      let options = []
      data.forEach((item, key) => {
        options.push({ label: item.label, value: item.id })
        if (item.children) {
          item.children.forEach((items, keys) => {
            options.push({ label: items.label, value: items.label })
            if (items.children) {
              items.children.forEach((itemss, keyss) => {
                options.push({ label: itemss.label, value: itemss.label })
                if (itemss.children) {
                  itemss.children.forEach((itemsss, keysss) => {
                    options.push({ label: itemsss.label, value: itemsss.label })
                  })
                }
              })
            }
          })
        }
      })
      return options
    },
    handleMatch() {
      if (!this.matchParams.maintenanceSectionId) {
        this.$modal.msgError('请选择路段')
        return
      }
      this.matchFlag = true
      this.selection = []
      this.handleMatchQuery()
    },
    handleMatchQuery() {
      this.loading = true
      getMaintStructInfo(this.matchParams).then(res => {
        this.matchData = res.data.rows
        // this.matchData.forEach(item => {
        //   // 使用正则表达式匹配经纬度
        //   const matches = item.shape.match(/[-+]?[0-9]*\.?[0-9]+/g);
        //
        //   if (matches && matches.length >= 2) {
        //     item.lon = parseFloat(matches[0]); // 经度
        //     item.lat = parseFloat(matches[1]);  // 纬度
        //   }
        // })
        this.matchParamsTotal = res.data.total
      }).finally(() => {
        this.loading = false
      })
    },
    handleSelectionChange(e) {
      this.selection = e
    },
    saveMatch() {
      if (this.selection.length == 0) {
        this.$modal.msgError('请选择匹配数据')
        return
      }
      // const batchData = this.selection.map(item => {
      //   return {
      //     domainId: item.managementMaintenanceId,
      //     roadId: this.matchParams.maintenanceSectionId,
      //     structureType: this.matchParams.structureType,
      //     assetId: item.assetId,
      //     // nodeId,
      //     code: item.assetCode,
      //     name: item.assetName,
      //     // flashType,
      //     // position,
      //     lat: item.lat,
      //     lon: item.lon,
      //     // imgPath,
      //     description: ''
      //   }
      // })
      const batchData = this.selection.map(item => {
        return {
          ...item,
          lat: item.lat || '',
          lon: item.lon || '',
          structureType: item.structureType || this.matchParams.structureType || '',
          description: item.description || '',
        }
      })
      batchSave(batchData).then(res => {
        this.$modal.msgSuccess('保存成功')
        this.handleQuery()
        this.matchFlag = false
      })
    },
    changeSensor(e, row) {
      updateIsHasSensor(row).then(res => {
        this.$modal.msgSuccess('保存成功')
        this.handleQuery()
      })
    },
  },
}
</script>

<style lang='scss' scoped>
@import "@/assets/styles/business.scss";

.leftDiv {
  border-right: 1px solid #d8dce5;
  min-height: calc(100vh - 100px);
  overflow-y: auto;
  height: calc(80vh - 150px);
  position: relative;
  top: -20px;
  padding-top: 10px;
  background-color: white;
}

.leftIcon {
  border: 1px solid #dcdfe6;
  border-radius: 8px;
  width: 16px;
  height: 50px;
  line-height: 50px;
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
  position: absolute;
  right: 0;
  top: 300px;
  z-index: 2;
}

.leftIcon:hover {
  background-color: #dcdfe6;
}

.rightIcon {
  border: 1px solid #dcdfe6;
  border-radius: 8px;
  width: 16px;
  height: 50px;
  line-height: 50px;
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
  position: absolute;
  left: -10px;
  top: 280px;
  z-index: 10;
  background: white;
}

.rightIcon:hover {
  background-color: #dcdfe6;
}

.left-total {
  font-size: 13px;
  line-height: 28px;
  color: #606266;
  position: absolute;
  bottom: 10px;
  right: 10px;
}

::v-deep .el-table th .gutter {
  display: table-cell !important;
}


// 新增样式以限制拖拽区域大小
::v-deep .el-upload-dragger {
  width: 148px;
  height: 148px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

::v-deep .el-upload--picture-card {
  width: 148px;
  height: 148px;
  line-height: normal;
}

::v-deep .el-upload-dragger .el-icon-plus {
  font-size: 28px;
  color: #8c939d;
  margin-bottom: 8px;
}

::v-deep .el-upload-dragger .el-upload__text {
  font-size: 12px;
  color: #606266;
  text-align: center;
  line-height: 1.2;
  padding: 0 5px;
}


::v-deep {
  .el-tabs__header {
    padding-left: 20px;
    border: 0;
  }

  .el-tabs__content {
    border: 0;
  }

  .el-form-item__label {
    font-family: Microsoft YaHei, Microsoft YaHei;
    font-weight: 400;
    font-size: 14px;
    color: #333333;
  }

  .el-input.is-disabled .el-input__inner {
    background-color: white;
    border-color: #dfe4ed;
    color: #1d2129;
  }

  .el-textarea.is-disabled .el-textarea__inner {
    background-color: white;
    border-color: #dfe4ed;
    color: #1d2129;
  }
}

::v-deep .el-input-group__prepend {
  padding: 0 5px !important;
  background: rgba(245, 247, 250, 0.5);
  font-size: 1.25rem;
}

::v-deep .el-input-group > .el-input__inner {
  padding: 0 5px !important;
}
</style>
