<template>
  <div class="detail-table">
    <el-dialog :title="title" :visible.sync="showTable" width="70%" :before-close="handleClose"
      :close-on-click-modal="false">
      <div style="width: 100%; height: 70vh">
        <el-input style=" width: 170px" v-model="queryParams.bridgeName" placeholder="桥梁名称" clearable />
        <el-input style="margin: 0 20px; width: 170px" v-model="queryParams.bridgeCode" placeholder="桥梁编码" clearable />

        <el-button type="primary" icon="el-icon-search" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" @click="resetQuery">重置</el-button>
        <el-button style="margin-bottom: 10px" type="primary" @click="exportList">导出列表</el-button>
        <el-table ref="table" v-loading="loading" height="calc(100% - 88px)" border :data="tableData"
          :header-cell-style="{ height: '36px' }">
          <el-table-column fixed label="序号" type="index" width="50" align="center">
            <template v-slot="scope">
              {{
                scope.$index +
                (queryParams.pageNum - 1) * queryParams.pageSize +
                1
              }}
            </template>
          </el-table-column>
          <el-table-column fixed label="桥梁名称" align="center" prop="bridgeName" min-width="130" show-overflow-tooltip />
          <el-table-column label="桥梁编码" align="center" prop="bridgeCode" min-width="130" show-overflow-tooltip />
          <el-table-column label="养护路段" align="center" prop="maintenanceSectionName" min-width="130"
            show-overflow-tooltip />
          <el-table-column label="路线编码" align="center" prop="routeCode" min-width="130" show-overflow-tooltip />
          <el-table-column label="管理处" align="center" prop="managementMaintenanceName" min-width="130"
            show-overflow-tooltip />
          <el-table-column label="管养分处" align="center" prop="managementMaintenanceBranchName" min-width="130"
            show-overflow-tooltip />
          <el-table-column label="结构形式" align="center" prop="mainSuperstructureTypeName" min-width="130"
            show-overflow-tooltip />
          <el-table-column label="桥位桩号" align="center" prop="centerStake" min-width="130" show-overflow-tooltip>
            <template slot-scope="scope">
              {{ formatPile(scope.row.centerStake) }}
            </template>
          </el-table-column>
          <el-table-column label="桥长" align="center" prop="totalLength" min-width="130" show-overflow-tooltip />
          <el-table-column label="桥梁评定等级" align="center" prop="bridgeTechAssessTypeName" min-width="130"
            show-overflow-tooltip />
          <el-table-column label="跨径分类" align="center" prop="spanClassifyTypeName" min-width="130"
            show-overflow-tooltip />
          <el-table-column label="跨径组合" align="center" prop="spanGroups" min-width="130" show-overflow-tooltip />
          <el-table-column label="固定编码" align="center" prop="fixedCode" min-width="130" show-overflow-tooltip />
          <el-table-column label="是否是独柱墩" align="center" prop="whetherSingleColumnPierTypeName" min-width="130"
            show-overflow-tooltip>
            <template slot-scope="scope">
              <dict-tag :options="dict.type.base_data_yes_no" :value="scope.row.whetherSingleColumnPierType" />
            </template>
          </el-table-column>
          <el-table-column label="运营状态" align="center" prop="operationStateName" min-width="130" show-overflow-tooltip>
            <template slot-scope="{ row }">
              <el-link :underline="false" :type="{ 1: 'info', 2: 'success', 3: 'danger', 4: 'primary' }[
                row.operationState
                ]
                ">
                <DictTag :value="row.operationState" :options="dict.type.sys_operation_state" />
              </el-link>
            </template>
          </el-table-column>
          <el-table-column label="是否锁定" align="center" prop="isLocked" min-width="130" show-overflow-tooltip>
            <template slot-scope="{ row }">
              <el-link :underline="false" :type="row.isLocked ? 'danger' : 'info'">
                {{ row.isLocked ? "是" : "否" }}
              </el-link>
            </template>
          </el-table-column>
          <el-table-column label="是否长大桥梁目录" align="center" prop="whetherInLongSpanCatalogTypeName" min-width="140"
            show-overflow-tooltip>
            <template slot-scope="scope">
              <dict-tag :options="dict.type.base_data_yes_no" :value="scope.row.whetherInLongSpanCatalogType" />
            </template>
          </el-table-column>
          <el-table-column label="建成时间" align="center" prop="buildDate" min-width="130" show-overflow-tooltip />
          <!-- <el-table-column
            label="图片"
            align="center"
            width="220"
          >
            <template
              slot-scope="scope"
              style="display: flex;justify-content: space-around"
            >
              <el-button
                :disabled="!scope.row.frontPhotoId"
                type="text"
                @click="handlePhotos(scope.row, 1)"
              >正面照</el-button>
              <el-button
                :disabled="!scope.row.facadePhotoId"
                type="text"
                @click="handlePhotos(scope.row, 2)"
              >立面照</el-button>
              <el-button
                :disabled="!scope.row.typicalPhotoId"
                type="text"
                @click="handlePhotos(scope.row, 3)"
              >典型照</el-button>
            </template>
          </el-table-column> -->
        </el-table>
        <pagination :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize"
          :pageSizes="[10, 20, 30, 50, 100, 1000]" @pagination="getList" />
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listStatic } from "@/api/baseData/bridge/statistics/index";

export default {
  name: "bridge-statistics-detail-table",
  props: {
    showTable: { type: Boolean, default: false },
    row: { type: undefined, default: "" },
    params: { type: undefined, default: "" },
  },
  dicts: ["base_data_yes_no", "sys_operation_state"],
  components: {},
  data() {
    return {
      title: "",
      loading: false,
      queryParams: {
        pageNum: 1,
        pageSize: 20,
      },
      total: 0,
      tableData: [],
      paramsObj: {},
    };
  },
  created() {
    this.getTitle();
    this.getParams();
    this.getList();
  },
  methods: {
    getTitle() {
      if (this.row) {
        this.title =
          (this.row.managementMaintenanceName
            ? this.row.managementMaintenanceName + "-"
            : "") +
          this.row.maintenanceSectionName +
          "-" +
          this.row.clickName +
          "列表";
      }
    },
    getParams() {
      if (this.params) {
        let ids = this.row.managementMaintenanceIds
          ? this.row.managementMaintenanceIds
          : this.row.managementMaintenanceId
            ? [this.row.managementMaintenanceId]
            : undefined;
        this.paramsObj = {
          ...this.queryParams,
          ...this.params,
          managementMaintenanceIds: ids,
          maintenanceSectionId:
            this.row.maintenanceSectionId || this.params.maintenanceSectionId,
          maintenanceSectionIds:
            this.row.maintenanceSectionIds || this.params.maintenanceSectionIds,
          routeCodes: this.row.routeCodes || this.params.routeCodes,
          operationState: this.row.operationState || this.params.operationState,
          years: this.row.years || this.params.years,
        };
      }
    },
    // 搜索按钮
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    // 重置按钮
    resetQuery() {
      this.queryParams = {
        pageNum: 1,
        pageSize: 20,
      };
      this.handleQuery();
    },
    getList() {
      this.loading = true;
      let paramsObj = {
        ...this.paramsObj,
        ...this.queryParams,
      };
      listStatic(paramsObj)
        .then((res) => {
          if (res.code === 200) {
            this.tableData = res.rows;
            this.total = res.total;
          }
        })
        .finally(() => {
          this.loading = false;
        });
    },
    exportList() {
      if (this.tableData.length === 0) return;
      let url = ''
      let paramsObj = {
        ...this.paramsObj,
        ...this.queryParams,
      };
      let nameArr = ['一类', '二类', '三类', '四类', '五类', '未评定', '评定总数']
      if (nameArr.includes(this.row.clickName)) {
        url = '/baseData/bridge/statistics/exportRecord'
      } else {
        url = '/baseData/bridge/exportPageTable'
      }

      this.$modal
        .confirm("导出所有表格数据，是否继续？")
        .then(() => {
          this.download(
            url,
            paramsObj,
            `road_interflow_${new Date().getTime()}.xlsx`,
            {
              headers: { "Content-Type": "application/json;" },
              parameterType: "body",
            }
          );
        })
        .catch(() => { });
    },
    handleClose() {
      this.$emit("close");
    },
  },
  computed: {},
  watch: {},
};
</script>

<style scoped lang="scss">
.detail-table {
  ::v-deep .el-table .el-table__fixed-body-wrapper {
    top: 36px !important;
  }
}
</style>