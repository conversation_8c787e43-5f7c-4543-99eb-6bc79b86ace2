<template>
  <div class="app-container">
    <el-row :gutter="20">
      <!--筛选区开始-->
      <el-col :span="24" :xs="24">
        <el-row>
          <el-col :span="24">
            <el-form ref="queryForm" :inline="true" :model="queryParams" label-width="68px" size="small">
              <el-form-item label="" prop="year">
                <el-date-picker
                  v-model="queryParams.year"
                  placeholder="年份"
                  style="width: 240px"
                  type="year"
                  value-format="yyyy"
                >
                </el-date-picker>
              </el-form-item>
              <el-form-item>
                <selectTree
                    :key="'domainId'"
                    v-model="queryParams.domainId"
                    :deptType="100"
                    :deptTypeList="[1, 3, 4]" clearable
                    filterable
                    placeholder="管养单位"
                    style="width: 240px"
                />
              </el-form-item>
              <el-form-item>
                <selectTree
                    :key="'constructionUnit'"
                    v-model="queryParams.conDomainId" :data-rule="false"
                    :dept-type="100"
                    placeholder="施工单位"
                    :filter-keys="['云南省交通投资建设集团有限公司', '云南交投投资有限公司']"
                    :expand-all="false"
                    clearable
                    filterable
                    style="width: 240px"
                />
              </el-form-item>
              <el-form-item>
                <RoadSection v-model="queryParams.maiSecId" :deptId="queryParams.domainId" placeholder="路段"
                             style="width: 240px"/>
              </el-form-item>
              <el-form-item>
                <el-input v-model="queryParams.projName" placeholder="项目名称" style="width: 240px"></el-input>
              </el-form-item>
              <el-form-item>
                <el-input v-model="queryParams.projCode" placeholder="项目编码" style="width: 240px"></el-input>
              </el-form-item>
              <el-form-item>
                <el-input v-model="queryParams.name" placeholder="任务单名称" style="width: 240px"></el-input>
              </el-form-item>
              <el-form-item>
                <el-input v-model="queryParams.code" placeholder="任务单编码" style="width: 240px"></el-input>
              </el-form-item>
              <el-form-item>
                <dict-select type="maintenance_project_type" v-model="queryParams.status" placeholder="工程施工状态"
                             style="width: 240px"></dict-select>
              </el-form-item>
              <el-form-item>
                <dict-select type="receive_status" v-model="queryParams.receiveStatus" placeholder="接收状态"
                             style="width: 240px"></dict-select>
              </el-form-item>
              <el-form-item>
                <el-date-picker
                  v-model="queryParams.issueDate"
                  type="daterange"
                  range-separator="至"
                  start-placeholder="发出起始时间"
                  end-placeholder="发出截止时间"
                  style="width: 240px">
                </el-date-picker>
              </el-form-item>
              <el-form-item label="" prop="operator">
                <el-cascader
                  v-model="queryParams.operator"
                  :options="deptUserOptions"
                  :props="{
                    multiple: false, //是否多选
                    value: 'id',
                    emitPath: false,
                  }"
                  :show-all-levels="false"
                  ref="deptUser"
                  placeholder="通知单拟定人" clearable style="width: 240px"
                  filterable clearable></el-cascader>
              </el-form-item>
              <el-form-item>
                <el-button icon="el-icon-search" size="mini" type="primary" @click="handleQuery">搜索</el-button>
                <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
              </el-form-item>
            </el-form>
          </el-col>
        </el-row>
        <!--筛选区结束-->

        <!--操作按钮区开始-->
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button
                icon="el-icon-download"
                size="mini"
                type="success"
                v-has-menu-permi="['projConstruction:construction:viewexport']"
                @click="exportList"
            >导出
            </el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button
                icon="el-icon-view"
                size="mini"
                type="warning"
                @click="handleOpenOperate"
            >审核意见
            </el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button
                icon="el-icon-zoom-in"
                size="mini"
                @click="handlePreview"
                v-has-menu-permi="['project:construction:conpreview']"
                type="primary"
            >任务单预览
            </el-button>
          </el-col>
<!--          <el-col :span="1.5">-->
<!--            <el-button-->
<!--                icon="el-icon-time"-->
<!--                size="mini"-->
<!--                type="success"-->
<!--                @click="openTimeInfo = true"-->
<!--            >任务单时间查看-->
<!--            </el-button>-->
<!--          </el-col>-->
<!--          <el-col :span="1.5">-->
<!--            <el-button-->
<!--                icon="el-icon-timer"-->
<!--                size="mini"-->
<!--                type="warning"-->
<!--                @click="openTimeInfo = true"-->
<!--            >任务单时间编辑-->
<!--            </el-button>-->
<!--          </el-col>-->
          <el-col :span="1.5">
            <el-button
                icon="el-icon-postcard"
                size="mini"
                type="primary"
                @click="handleOpenTaskInfo"
            >任务单信息
            </el-button>
          </el-col>
<!--          <el-col :span="1.5">-->
<!--            <el-button-->
<!--                icon="el-icon-view"-->
<!--                size="mini"-->
<!--                type="success"-->
<!--                @click="handlePreviewSup"-->
<!--                v-has-menu-permi="['project:construction:suppreview']"-->
<!--            >监理通知书预览-->
<!--            </el-button>-->
<!--          </el-col>-->
          <el-col :span="1.5">
            <el-button
                icon="el-icon-postcard"
                size="mini"
                type="warning"
                @click="handleExportLedger"
            >施工单台账
            </el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button
              type="warning"
              icon="el-icon-view"
              size="mini"
              v-has-permi="['settlement:repository:regenerate']"
              @click="handleRegenerate"
            >重新生成报表
            </el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button
              type="danger"
              icon="el-icon-refresh-left"
              size="mini"
              v-has-permi="['projConstruction:construction:withdraw']"
              @click="handleWithdraw"
            >撤回
            </el-button>
          </el-col>
          <right-toolbar :columns="columns" :showSearch.sync="showSearch" @queryTable="handleQuery"></right-toolbar>
        </el-row>
        <!--操作按钮区结束-->
        <!--数据表格开始-->
        <div class="tableDiv">
          <el-table v-adjust-table
              ref="dataTable"
              v-loading="loading"
              :data="tableData"
              :height="
                showSearch ? 'calc(100vh - 330px)' : 'calc(100vh - 270px)'
              "
              border
              highlight-current-row
              row-key="id"
              size="mini"
              @row-click="handleClickRow"
              @selection-change="handleSelectionChange"
              stripe
              style="width: 100%"
          >
            <el-table-column type="selection" width="50" align="center"/>
            <el-table-column type="expand">
              <template slot-scope="props">
                <el-table v-adjust-table :data="props.row.constructionDetailList" style="width: 100%">
                  <el-table-column
                    prop=""
                    align="center"
                    label="">
                  </el-table-column>
                  <el-table-column
                    prop="schemeCode"
                    align="center"
                    label="子目号">
                  </el-table-column>
                  <el-table-column
                    prop="schemeName"
                    align="center"
                    label="养护方法">
                  </el-table-column>
                  <el-table-column
                    prop="calcDesc"
                    align="center"
                    label="计算式">
                  </el-table-column>
                  <el-table-column
                    prop="num"
                    align="center"
                    label="方法数量">
                  </el-table-column>
                  <el-table-column
                    prop="unit"
                    align="center"
                    label="方法单位">
                  </el-table-column>
                  <el-table-column
                    prop="price"
                    align="center"
                    label="单价">
                  </el-table-column>
                  <el-table-column
                    prop="amount"
                    align="center"
                    label="金额">
                  </el-table-column>
                  <el-table-column
                    prop="remark"
                    align="center"
                    label="备注">
                  </el-table-column>
                </el-table>
              </template>
            </el-table-column>
            <el-table-column
                align="center"
                label="序号"
                type="index"
                width="50"
            />
            <template v-for="(column,index) in columns">
              <el-table-column v-if="column.visible" show-overflow-tooltip
                               :label="column.label"
                               :prop="column.field"
                               :width="column.width"
                               align="center">
                <template slot-scope="scope">
                  <dict-tag v-if="column.dict" :options="dict.type[column.dict]" :value="scope.row[column.field]"/>
                  <template v-else-if="column.slots">
                    <RenderDom :index="index" :render="column.render" :row="scope.row"/>
                  </template>
                  <span v-else-if="column.isTime">{{ parseTime(scope.row[column.field], '{y}-{m}-{d}') }}</span>
                  <span v-else>{{ scope.row[column.field] }}</span>
                </template>
              </el-table-column>
            </template>
            <el-table-column
                align="center"
                class-name="small-padding fixed-width"
                fixed="right"
                label="操作"
                width="250"
            >
              <template slot-scope="scope">
                <template v-if="prosStatus == 1">
                  <el-button
                    icon="el-icon-edit"
                    size="mini"
                    type="text"
                    v-has-menu-permi="['projConstruction:construction:edit']"
                    @click="handleDetail(scope.row)"
                  >编辑
                  </el-button>
                  <el-button
                      icon="el-icon-view"
                      size="mini"
                      type="text"
                      @click="openFileModel(scope.row)"
                  >附件查看
                  </el-button>
                  <el-button
                      icon="el-icon-view"
                      size="mini"
                      type="text"
                      @click="handleView(scope.row)"
                  >任务单查看
                  </el-button>
                </template>
              </template>
            </el-table-column>
          </el-table>
          <pagination
              v-show="total>0"
              :limit.sync="queryParams.pageSize"
              :page.sync="queryParams.pageNum"
              :total="total"
              @pagination="handleQuery"
          />
        </div>
        <!--数据表格结束-->
      </el-col>
    </el-row>
    <el-drawer :wrapperClosable="false" :title="detailTitle" :visible.sync="openDetail" size="70%" destroy-on-close>
      <task-detail :status="status" :type="type" :read-only="readonly" :row="row"></task-detail>
    </el-drawer>

    <el-dialog title="操作意见" :visible.sync="openOperateInfo" width="80%" v-if="openOperateInfo" destroy-on-close>
      <operateInfo type="proj"  :business-key="clickRow.id" :get-node-info="getNodeInfo"></operateInfo>
    </el-dialog>

    <el-dialog title="任务单时间" :visible.sync="openTimeInfo" width="80%" destroy-on-close>
      <time-info></time-info>
    </el-dialog>

    <el-dialog title="任务单信息" :visible.sync="openTaskInfo" v-if="openTaskInfo" width="80%" destroy-on-close>
      <task-record :id="clickRow.id"></task-record>
    </el-dialog>
    <IFramePreview ref="iframeRef" :srcdoc="preview.html" :down-url="preview.url" :file-name="preview.fileName"></IFramePreview>

    <el-dialog title="附件列表" destroy-on-close :visible.sync="openFile" width="80%" v-if="openFile">
      <file-upload v-if="row.fileId" v-model="row.fileId" :owner-id="row.fileId" for-view></file-upload>
    </el-dialog>
    <el-drawer
      :wrapperClosable="false"
      title="编辑任务单"
      :visible.sync="openEditDetail"
      size="70%"
      destroy-on-close
    >
      <task-detail
        :status="1"
        :read-only="false"
        :row="row"
        @close="closeDetail"
      ></task-detail>
    </el-drawer>
  </div>
</template>

<script>
import Tables from "@/views/patrol/frequencySettings/tables.vue";
import RoadSection from "@/views/baseData/components/roadSection/index.vue";
import selectTree from "@/components/DeptTmpl/selectTree.vue";
import TaskDetail from "@/views/maintenanceProject/taskList/component/taskDetail.vue";
import operateInfo from "@/views/dailyMaintenance/component/operateInfo.vue";
import TimeInfo from "@/views/maintenanceProject/taskList/component/timeInfo.vue";
import TaskInfo from "@/views/maintenanceProject/taskList/component/taskInfo.vue";
import {
  getConstructionFileList,
  getNodeInfo, previewConstruction,
  viewConstructionList, withdraw
} from "@/api/maintenanceProject/taskList";
import Luckysheet from "@/components/Luckysheet/index.vue";
import IFramePreview from "@/components/IFramePreview/index.vue";
import TaskRecord from "@/views/maintenanceProject/taskList/component/taskRecord.vue";
import {regenerateReport} from "@/api/dailyMaintenance/metering/addPrice";
import {getTreeStruct} from "@/api/tmpl";

export default {
  name: 'ConstructionTask',
  components: {
    TaskRecord,
    IFramePreview, Luckysheet, TaskInfo, TimeInfo, operateInfo, TaskDetail, selectTree, RoadSection, Tables},
  dicts: ['maintenance_project_type', 'task_type', 'project_type', 'affiliation_project_type'],
  data() {
    return {
      showSearch: false,
      queryParams: {
        pageNum: 1,
        pageSize: 50
      },
      total: 0,
      loading: false,
      status: 4,
      type: 1,
      readonly: true,
      openFile: false,
      columns: [
        {key: 0, width: 100, field: 'timeOutMsg', label: `超期提醒`, visible: true},
        {key: 2, width: 100, field: 'receiveStatus', label: `接收状态`, visible: true},
        {key: 3, width: 100, field: 'status', label: `状态`, visible: true, dict: 'maintenance_project_type'},
        {key: 4, width: 200, field: 'projName', label: `项目名称`, visible: true},
        {key: 5, width: 200, field: 'projCode', label: `项目编码`, visible: true},
        // {key: 6, width: 100, field: 'type', label: `任务单类型`, visible: true, dict: 'task_type'},
        {key: 7, width: 200, field: 'name', label: `任务单名称`, visible: true},
        {key: 8, width: 200, field: 'code', label: `任务单编号`, visible: true},
        {key: 9, width: 100, field: 'maiSecName', label: `路段名称`, visible: true},
        {key: 10, width: 100, field: 'domainName', label: `管养单位`, visible: true},
        {key: 11, width: 200, field: 'conDomainName', label: `施工单位`, visible: true},
        {key: 12, width: 200, field: 'conConName', label: `施工合同`, visible: true},
        {key: 13, width: 200, field: 'supDomainName', label: `监理单位`, visible: true},
        {key: 14, width: 200, field: 'supConName', label: `监理合同`, visible: true},
        {key: 15, width: 200, field: 'designDomainName', label: `设计单位`, visible: true},
        {key: 16, width: 100, field: 'issueDate', label: `发出时间`, visible: true},
        {key: 17, width: 100, field: 'defLiaPer', label: `缺陷责任期(月)`, visible: true},
        {key: 18, width: 200, field: 'content', label: `工作内容`, visible: true},
        {key: 19, width: 200, field: 'exeRequire', label: `实施要求`, visible: true},
        {key: 20, width: 100, field: 'projectType', label: `工程分类`, visible: true, dict: 'project_type'},
        {key: 21, width: 100, field: 'mtype', label: `所属工程类别`, visible: true, dict: 'affiliation_project_type'},
        {key: 22, width: 100, field: 'beginDate', label: `计划开始时间`, visible: true},
        {key: 23, width: 100, field: 'endDate', label: `计划完成时间`, visible: true},
        {key: 24, width: 100, field: 'acceptancePerson', label: `验收人员`, visible: true},
        // {key: 25, width: 100, field: 'receivePerson', label: `接收人`, visible: true},
        // {key: 26, width: 100, field: 'receiveTime', label: `接收时间`, visible: true},
        // {key: 27, width: 100, field: 'remark', label: `接收备注`, visible: true},

      ],
      tableData: [],
      detailTitle: '任务单详情',
      openDetail: false,
      openOperateInfo: false,
      openTimeInfo: false,
      openTaskInfo: false,
      row: {},
      clickRow: {},
      preview: {
        html: '',
        url: '',
        fileName: ''
      },
      openEditDetail: false,
      ids: [],
      deptUserOptions: []
    }
  },
  props: {
    prosStatus: {
      type: Number,
      default: 1
    }
  },
  mounted() {
    this.getDeptTreeDef()
    this.handleQuery()
  },
  methods: {
    /** 查询部门-用户下拉树结构 */
    getDeptTreeDef() {
      getTreeStruct({types:111}).then(response => {
        this.deptUserOptions = response.data;
      });
    },
    getNodeInfo,
    handleQuery() {
      if (this.queryParams.issueDate && this.queryParams.issueDate.length == 2) {
        this.queryParams.issueDate.startIssueDate = this.queryParams.issueDate[0]
        this.queryParams.issueDate.endIssueDate = this.queryParams.issueDate[1]
      }
      this.loading = true
      this.queryParams.type = 1
      viewConstructionList(this.queryParams).then(res => {
        this.tableData = res.rows
        this.total = res.total
        this.loading = false
      })
    },
    // 接收
    handleReceive(row) {
      this.$prompt('', '任务单接收', {
        confirmButtonText: '接收',
        cancelButtonText: '取消',
        inputPlaceholder: '请输入',
        inputType: 'textarea'
      }).then(({ value }) => {

      })
    },
    handleOpenOperate() {
      if (!this.clickRow.id) {
        this.$message.warning('请先选择一条记录！')
        return
      }
      this.openOperateInfo = true
    },
    handleOpenTaskInfo() {
      if (!this.clickRow.id) {
        this.$message.warning('请先选择一条记录！')
        return
      }
      this.openTaskInfo = true
    },
    // 撤回
    handleRevoke(row) {
      this.type = row.type
      this.status = 4
      this.openDetail = true
    },
    // 查看
    handleView(row) {
      this.row = row
      this.type = row.type
      this.status = 5
      this.openDetail = true
    },
    async openFileModel(row) {
      this.row = row
      this.loading = true
      // 查询附件
      await getConstructionFileList({projConId: this.row.id}).then(async res => {
        if (res.data) {
          this.row.fileId = res.data.fileId
          this.openFile = true
        } else {
          this.$message.warning('暂无附件！')
        }
        this.loading = false
      })
    },
    handleClickRow(e) {
      this.clickRow = e
    },
    // 报表预览
    handlePreview() {
      if (!this.clickRow.id) {
        this.$message.warning('请先选择一条记录！')
        return
      }
      this.loading = true
      previewConstruction(this.clickRow.id).then(res => {
        if (res.code == 200){
          this.preview.html = res.data.html
          this.preview.url = res.data.downUrl
          this.preview.fileName = res.data.fileName
          this.$refs.iframeRef.visible = true
        }
        this.loading = false
      })
    },
    resetQuery() {
      this.queryParams = {
        pageNum: 1,
        pageSize: 50
      }
      this.handleQuery()
    },
    handleDetail(row) {
      this.row = row;
      delete this.row.issueDate
      this.openEditDetail = true;
    },
    closeDetail() {
      this.openEditDetail = false;
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection
    },
    handleRegenerate() {
      if (this.ids.length == 0) {
        this.$modal.msgError("请勾选至少一条数据")
        return
      }
      const params = {
        idList: this.ids.map(item => item.id),
        type: 3
      }
      regenerateReport(params).then(res => {
        this.$modal.msgSuccess("操作成功")
      })
    },
    handleWithdraw() {
      if (!this.clickRow.id) {
        this.$message.warning('请先选择一条记录！')
        return
      }
      this.$confirm('确定撤回吗？', '警告', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        const params = {
          businessKey: this.clickRow.id
        }
        withdraw(params).then(res => {
          this.$message.success('撤回成功')
          this.handleQuery()
        })
      })
    },
    // 导出清单按钮
    exportList() {
      if (this.queryParams.issueDate && this.queryParams.issueDate.length == 2) {
        this.queryParams.issueDate.startIssueDate = this.queryParams.issueDate[0]
        this.queryParams.issueDate.endIssueDate = this.queryParams.issueDate[1]
      }
      this.queryParams.type = 1
      this.download(
        'manager/proj/construction/view/export',
        {...this.queryParams},
        `buildTask_${new Date().getTime()}.xlsx`,
        {
          headers: {'Content-Type': 'application/json;'},
          parameterType: 'body'
        }
      )
    },
    // 施工单台账
    handleExportLedger() {
      if (this.queryParams.issueDate && this.queryParams.issueDate.length == 2) {
        this.queryParams.issueDate.startIssueDate = this.queryParams.issueDate[0]
        this.queryParams.issueDate.endIssueDate = this.queryParams.issueDate[1]
      }
      this.queryParams.type = 1
      this.download(
        'manager/proj/construction/exportLedger',
        {...this.queryParams},
        `buildTask_${new Date().getTime()}.xlsx`,
        {
          headers: {'Content-Type': 'application/json;'},
          parameterType: 'body'
        }
      )
    }
  }
}
</script>

<style lang="scss" scoped>

</style>
<style lang="scss" scoped>
@import "@/assets/styles/business.scss";
</style>
