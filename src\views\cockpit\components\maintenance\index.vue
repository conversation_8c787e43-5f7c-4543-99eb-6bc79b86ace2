<template>
  <div class="maintenance-index">
    <el-row :gutter="10">
      <el-col :xs="6" :sm="6" :md="6" :lg="6" :xl="6">
        <CockpitCard title="日常养护事件(处置类型)" :isDtl="false" w="100%" :h="isBig ? '27vh' : 'calc(24vh - 4px)'" class="mt-1">
          <PieECharts :data="czObj.data" :color="czObj.color" title="处置类型" :year="year" key="rcKey" />
        </CockpitCard>
        <CockpitCard title="事件类型占比" :isDtl="false" w="100%" :h="isBig ? '27vh' : 'calc(24vh - 4px)'" class="mt-1">
          <PieECharts :data="sjObj.data" :color="sjObj.color" title="事件分类" :year="year" type="2" key="sjKey" />
        </CockpitCard>
        <CockpitCard title="事件趋势" :isDtl="false" w="100%" :h="isBig ? '31vh' : 'calc(30vh - 4px)'"  class="mt-1">
          <EventTrends :year="year" :key="'eventTrends' + year"/>
        </CockpitCard>
      </el-col>
      <el-col :xs="12" :sm="12" :md="12" :lg="12" :xl="12">
        <CockpitCard title="施工趋势" :isDtl="false" w="100%" :h="isBig ? 'calc(58vh + 4px)' : 'calc(52vh + 1px)'" class="mt-1">
          <YearStatistics />
          <template slot="more">
            <el-select v-model="year" placeholder="请选择年份" popper-class="select-popper" @change="onChange"
              :popper-append-to-body="false">
              <el-option v-for="item in years" :key="item" :label="item + '年'" :value="item"></el-option>
            </el-select>
          </template>
        </CockpitCard>
        <CockpitCard title="各管养单位资金使用情况" :isDtl="false" w="100%" :h="isBig ? '31.3vh' : 'calc(30vh - 4px)'" class="mt-1">
          <UnitFunds :year="year"  :key="'UnitFunds' + year"/>
        </CockpitCard>
      </el-col>
      <el-col :xs="6" :sm="6" :md="6" :lg="6" :xl="6">
        <CockpitCard title="预算与实际完成对比分析" :isDtl="false" w="100%" :h="isBig ? '27vh' : 'calc(24vh - 4px)'" class="mt-1">
          <BudgetAndActual :year="year" :key="'db' + year" />
          <template slot="more">
            <img src="@/assets/cockpit/back.png" @click="$emit('back')" style="width:118px;height:54px" v-if="isBig" />
          </template>
        </CockpitCard>
        <CockpitCard title="修复费用统计" :isDtl="false" w="100%" :h="isBig ? '27vh' : 'calc(24vh - 4px)'" class="mt-1">
          <PieECharts :data="xfObj.data" :color="xfObj.color" title="修复费用" :year="year" type="3" key="fyKey" />
        </CockpitCard>
        <CockpitCard title="事件数量对比" :isDtl="false" w="100%" :h="isBig ? '31vh' : 'calc(30vh - 4px)'" class="mt-1">
          <NumberOfEvents :year="year" />
        </CockpitCard>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import { isBigScreen } from "@/views/cockpit/util/utils";
import CockpitCard from '../cockpitCard.vue';
import PieECharts from './component/pieECharts.vue';
import BudgetAndActual from './component/budgetAndActual.vue';
import UnitFunds from './component/unitFunds.vue';
import EventTrends from './component/eventTrends.vue';
import NumberOfEvents from './component/numberOfEvents.vue';
import YearStatistics from '../yearStatistics.vue';

export default {
  components: {
    CockpitCard,
    PieECharts,
    BudgetAndActual,
    UnitFunds,
    EventTrends,
    NumberOfEvents,
    YearStatistics,
  },
  data() {
    return {
      isBig: isBigScreen(),
      czObj: {
        data: [
          {
            name: '普通事件',
            value: 1627,
            unit: '件'
          },
          {
            name: '缺陷责任期',
            value: 1627,
            unit: '件'
          },
          {
            name: '及时事件',
            value: 230,
            unit: '件'
          },
        ],
        color: ['#01FBEF', '#3851E6', '#FFC722']
      },
      sjObj: {
        data: [
          {
            name: '被损被盗',
            value: 1627,
            unit: '件'
          },
          {
            name: '日常养护',
            value: 230,
            unit: '件'
          }
        ],
        color: ['#3851E6', '#10A2FD']
      },
      xfObj: {
        data: [
          {
            name: '路基',
            value: 1627,
            unit: '万元'
          },
          {
            name: '路面',
            value: 230,
            unit: '万元'
          },
          {
            name: '桥隧涵',
            value: 1627,
            unit: '万元'
          },
          {
            name: '交通安全设施',
            value: 230,
            unit: '万元'
          },
          {
            name: '绿化',
            value: 1627,
            unit: '万元'
          },
          {
            name: '附属设施',
            value: 230,
            unit: '万元'
          },
          {
            name: '其他',
            value: 230,
            unit: '万元'
          }
        ],
        color: ['#0054B5', '#008DEC', '#01FBEF', '#FFC721', '#3851E6', '#886EFF', '#FFFFFF'],
      },
      years: [],
      year: '',
    }
  },
  mounted() {
    this.getYear();
  },
  methods: {
    getYear() {
      let date = new Date();
      let year = date.getFullYear();
      this.year = year;
      for (let i = year - 10; i < year + 1; i++) {
        this.years.push(i);
      }
    },
    onChange(value) {
      this.year = value;
      window.$Bus.$emit("onChangeYear", this.year);
    },
  }
}
</script>

<style lang="scss" scoped>
.maintenance-index {
  width: 100%;
  height: 100%;
  overflow: hidden;
  padding: 0 10px;

  .mt-1 {
    margin-top: 0.6rem;
  }

  ::v-deep .el-input {
    .el-input__inner {
      height: vwpx(300px);
      height: vwpx(60px);
      background-color: rgba(1, 102, 254, 0.2);
      border: 1px solid #0166fe;
      color: #ffffff;
      font-size: vwpx(30px);
    }

    .el-input__inner::placeholder {
      color: #bbbbbb;
    }

    .el-input-group__append {
      background-color: rgba(1, 102, 254, 0.2);
      border: 1px solid #0166fe;
      color: #ffffff;
      border-left: none;
      padding: 0 10px;
      cursor: pointer;
    }
  }

  ::v-deep .select-popper {
    background-color: rgba(1, 102, 254, 0.2);
    border: 1px solid #0166fe;
    color: #ffffff !important;
    font-size: vwpx(30px);
    margin: vwpx(10px) 0;
    // 添加以下样式
    position: absolute !important; // 确保使用绝对定位
    z-index: 2000; // 确保下拉框在其他元素之上
    top: 3vh !important;

    .popper__arrow {
      position: absolute;
      top: -0.7vh;
    }
  }

  ::v-deep .el-select-dropdown__item {
    color: #ffffff !important;
    font-size: vwpx(30px);
    margin: vwpx(15px) 0;

    &:hover {
      background-color: rgba(1, 102, 254, 0.2);
    }

    &.is-focus {
      background-color: rgba(1, 102, 254, 0.2);
    }
  }

  ::v-deep .el-select-dropdown__item.selected {
    color: #42abff !important;
  }
}
</style>