<template>
  <div class="route-index">
    <div class="total-mileage">
      <span>养护路段总里程：</span>
      <span>5880.597 <small>km</small></span>
    </div>
    <div class="divider"></div>
    <section class="route-info mb-5 mt-2">
      <div class="info">
        <div>集团公司路段养护里程</div>
        <div>3380.419 <small>km</small></div>
      </div>
      <div class="divider-vertical"></div>
      <div class="info">
        <div>集团公司养护分段数</div>
        <div>51 <small>条</small></div>
      </div>
    </section>

    <section class="route-info">
      <div class="info">
        <div>项目公司路段养护里程</div>
        <div>2500.178 <small>km</small></div>
      </div>
      <div class="divider-vertical"></div>
      <div class="info">
        <div>集团公司养护分段数</div>
        <div>41 <small>条</small></div>
      </div>
    </section>
  </div>
</template>

<script>
export default {

}
</script>

<style lang="scss" scoped>
@import "@/assets/styles/utils.scss";

.route-index {
  padding: 8px;
  color: white;

  .total-mileage {
    display: flex;
    align-items: center;

    span:last-child {
      font-family: Microsoft YaHei UI, Microsoft YaHei UI;
      font-weight: 700;
      font-size: vwpx(48px);
      color: #42ABFF;
      text-align: center;
      font-style: normal;
      text-transform: none;

      small {
        font-family: Source Han Sans, Source Han Sans;
        font-weight: 400;
        font-size: vwpx(24px);
        color: rgba(255, 255, 255, 0.8);
        text-align: center;
        font-style: normal;
        text-transform: none;
      }
    }
  }

  .divider {
    width: 100%;
    border: 1px dotted rgba(156, 189, 255, 0.5);
    margin: 6px 0;
  }

  .route-info {
    display: flex;
    align-items: center;
    // justify-content: space-between;

    .divider-vertical {
      width: 0;
      height: vwpx(70px);
      border: 1px dotted rgba(156, 189, 255, 0.5);
      margin: 0 vwpx(80px) 0 vwpx(120px);
    }

    .info {
      font-weight: 400;
      font-size: vwpx(32px);
      color: #B6B6B6;
      text-align: left;
      font-style: normal;
      text-transform: none;

      div:last-child {
        font-family: Microsoft YaHei UI, Microsoft YaHei UI;
        font-weight: 700;
        font-size: vwpx(32px);
        color: #42ABFF;
        text-align: left;
        font-style: normal;
        text-transform: none;
        margin-top: 3px;

        small {
          font-family: Microsoft YaHei UI, Microsoft YaHei UI;
          font-weight: 400;
          font-size: vwpx(24px);
          color: #B6B6B6;
          text-shadow: 0px 0px 10px rgba(27,126,242,0.8);
          text-align: center;
          font-style: normal;
          text-transform: none;
        }
      }
    }
  }

  .mb-5 {
    margin-bottom: vwpx(50px);
  }
  .mt-2 {
    margin-top: vwpx(20px);
  }
}
</style>