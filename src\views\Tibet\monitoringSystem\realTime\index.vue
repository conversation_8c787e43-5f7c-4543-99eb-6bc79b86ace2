<template>
  <div class="sub-page" :style="themeStyle.common">
    <section class="sub-page-main" :style="mainStyle">
      <div class="sub-page-one">
        <Header title="西藏高等公路监测预警系统" fixed="static" :isSub="true" :theme="theme">
          <template #btn>
            <img :src="themeButtonStyle.theme" @click="changeTheme"
              :style="{ width: isBig ? '1.91vw' : '4.45vw', height: isBig ? '0.834vw' : '1.8vw', marginRight: isBig ? '0.4vw' : '0.6vw' }" />
            <img :src="themeButtonStyle.back" @click="handleBack"
              :style="{ width: isBig ? '1.823vw' : '3.45vw', height: isBig ? '0.834vw' : '1.8vw' }" />
          </template>
        </Header>
        <el-container class="page-one">
          <!-- 左侧tree -->
          <transition name="el-zoom-in-left">
            <el-aside v-show="!isSpread" ref="leftAside" :width="asideWidth" :style="themeStyle.boxStyle">
              <Tree :theme="theme" @treeNodeClick="treeNodeClick"
                @getStructureDisplayHtmlText="getStructureDisplayHtmlText" @openVideo="openVideo" />
            </el-aside>
          </transition>
          <!-- 展开、收缩左侧tree按钮-深色 -->
          <div v-if="theme === 'dark'" class="spread-packup spread-packup-dark" :style="{ left: asideWidth }"
            @click="handleSpread" @mousedown="widthDrag">
            <img src="@/assets/map/spread.png" :class="isSpread ? 'spread-img' : 'img'" @dragstart.prevent
              @contextmenu.prevent />
          </div>
          <!-- 展开、收缩左侧tree树按钮-浅色 -->
          <div v-else>
            <div v-if="isSpread" class="spread-packup spread-packup-light-open" :style="{ left: asideWidth }"
              @click="handleSpread" @mousedown="widthDrag" />
            <div v-else class="spread-packup spread-packup-light-close" :style="{ left: asideWidth }"
              @click="handleSpread" @mousedown="widthDrag" />
          </div>

          <el-main v-if="!isBig" style="display: flex;">
            <div class="right-box" :style="{ ...themeStyle.boxStyle, height: '100%', width: 'calc(100% - 33vw)' }"
              v-if="!isBig">
              <Charts :theme="theme" :sensorData="sensorData" :structureDisplayHtmlText="structureDisplayHtmlText" />
            </div>
            <div class="drag-line" @mousedown="startDrag" style="height: 100%; width: 10px; cursor: col-resize;"></div>
            <div class="right-box2" :style="{ ...themeStyle.boxStyle }" v-if="!isBig">
              <BimAndPictures :theme="theme" />
            </div>
          </el-main>
          <el-main v-else>
            <div class="right-box" :style="{ ...themeStyle.boxStyle, height: '100%', marginRight: 0 }">
              <Charts :theme="theme" :sensorData="sensorData" :structureDisplayHtmlText="structureDisplayHtmlText" />
            </div>
          </el-main>
          <!-- 右侧内容 -->
          <!-- <el-main :style="themeStyle.boxStyle">
            <Charts :theme="theme" :sensorData="sensorData" v-if="isBig"
              :structureDisplayHtmlText="structureDisplayHtmlText" />
            
          </el-main> -->
        </el-container>
      </div>
      <div class="sub-page-two" v-if="isBig">
        <div class="page-two">
          <el-card :style="{ ...themeStyle.boxStyle, height: '100%' }" shadow="never">
            <BimAndPictures :theme="theme" />
          </el-card>
        </div>
      </div>
    </section>
    <playModal ref="playModal" :theme="theme"></playModal>
  </div>
</template>

<script>
import { isBigScreen } from '../utils/utils.js';
import Tree from './Tree.vue'
import Header from '../components/Header.vue';
import Charts from './Charts.vue';
import BimAndPictures from './BimAndPictures.vue';
import playModal from './components/Video/index.vue';

export default {
  name: 'RealTime',
  props: {},
  components: { Header, Tree, Charts, BimAndPictures, playModal },
  data() {
    return {
      isBig: isBigScreen(),
      theme: 'dark',
      asideTotalWidth: 0,
      isSpread: false,
      asideWidth: isBigScreen() ? '10.5vw' : '15vw',
      isDragging: false, // 新增标志位，用于判断是否正在拖拽
      startX: 0, // 新增变量，记录鼠标按下时的初始 X 坐标
      startWidth: 0, // 新增变量，记录 aside 初始宽度
      sensorData: null, // 传感器数据
      structureDisplayHtmlText: '',
      timeKey: 'timeKey',
      isDragging2: false,
      startX2: 0,
      startLeftWidth: 0,
      startRightWidth: 0
    }
  },
  mounted() { 
	  document.title = '西藏高等级公路风险点位监测预警系统';  
  },
  methods: {
    // 返回上一页
    handleBack() {
      // 关闭当前页面
      window.close();
      // this.$router.go(-1);
    },
    // 切换主题
    changeTheme() {
      this.theme = this.theme == 'dark' ? 'light' : 'dark'
    },
    // tree节点点击, 返回传感器数据
    treeNodeClick(data) {
      this.sensorData = data;
    },
    getStructureDisplayHtmlText(structureDisplayHtmlText) {
      this.structureDisplayHtmlText = structureDisplayHtmlText
    },
    // 打开视频
    openVideo(data) {
      this.$refs.playModal.open(data);
    },
    // 展开、收缩左侧tree
    handleSpread() {
      if (!this.isDragging) {
        this.isSpread = !this.isSpread;
        if (this.isSpread) {
          this.asideWidth = '0vw';
        } else {
          this.asideWidth = this.isBig ? '10.5vw' : '15vw';
        }
      }
      // 在!isBig时，重置right-box和right-box2的宽度
      if (!this.isBig) {
        const leftBox = this.$el.querySelector('.right-box');
        const rightBox = this.$el.querySelector('.right-box2');
        if (leftBox && rightBox) {
          leftBox.style.width = 'calc(100% - 33vw)';
          rightBox.style.width = '33vw';
        }
      }
    },
    // 左侧tree宽度拖拽
    widthDrag(e) {
      if (this.isSpread) return;
      // 记录鼠标按下时的初始 X 坐标
      this.startX = e.clientX;
      // 记录 aside 初始宽度
      this.startWidth = parseFloat(this.asideWidth);
      this.isDragging = true;

      const onMouseMove = (moveEvent) => {
        if (this.isDragging) {
          const diffX = moveEvent.clientX - this.startX;
          const newWidth = this.startWidth + diffX * 0.1;
          const minWidth = 5; // 最小宽度 5vw
          const maxWidth = 30; // 最大宽度 30vw
          const validWidth = Math.max(minWidth, Math.min(maxWidth, newWidth));
          this.asideWidth = `${validWidth}vw`;
        }
      };

      const onMouseUp = () => {
        // 移除鼠标移动和鼠标抬起事件监听器
        if (this.isDragging) {
          document.removeEventListener('mousemove', onMouseMove);
          document.removeEventListener('mouseup', onMouseUp);
          this.isDragging = false;
        }
      };

      // 添加鼠标移动和鼠标抬起事件监听器
      document.addEventListener('mousemove', onMouseMove);
      document.addEventListener('mouseup', onMouseUp);
    },
    startDrag(e) {
      this.isDragging2 = true;
      this.startX2 = e.clientX;
      const leftBox = this.$el.querySelector('.right-box');
      const rightBox = this.$el.querySelector('.right-box2');
      if (leftBox && rightBox) {
        this.startLeftWidth = parseFloat(getComputedStyle(leftBox).width);
        this.startRightWidth = parseFloat(getComputedStyle(rightBox).width);
      }

      const onMouseMove = (moveEvent) => {
        if (this.isDragging2) {
          const diffX = moveEvent.clientX - this.startX2;
          const newLeftWidth = this.startLeftWidth + diffX;
          const newRightWidth = this.startRightWidth - diffX;
          const minWidth = 300; // 最小宽度
          const leftBox = this.$el.querySelector('.right-box');
          const rightBox = this.$el.querySelector('.right-box2');
          if (leftBox && rightBox && newLeftWidth > minWidth && newRightWidth > minWidth) {
            leftBox.style.width = `${newLeftWidth}px`;
            rightBox.style.width = `${newRightWidth}px`;
          }
        }
      };

      const onMouseUp = () => {
        if (this.isDragging2) {
          document.removeEventListener('mousemove', onMouseMove);
          document.removeEventListener('mouseup', onMouseUp);
          this.isDragging2 = false;
        }
      };

      document.addEventListener('mousemove', onMouseMove);
      document.addEventListener('mouseup', onMouseUp);
    },
  },
  computed: {
    mainStyle() {
      let style = {};
      if (this.isBig) {
        style = {
          height: '100vh',
          overflow: 'hidden',
          alignItems: 'flex-end',
        }
      } else {
        style = {
          height: '100vh',
          overflow: 'hidden',
          flexDirection: 'column',
          // alignItems: 'flex-end',
        }
      }
      return style;
    },
    themeStyle() {
      // el-aside和el-card深色主题
      let boxStyle = {
        background: 'linear-gradient( 270deg, #020A1E 0%, rgba(12,42,86,0.2) 100%)',
        boxShadow: 'inset 0px 0px 10px 0px rgba(35,134,255,0.5)',
        border: '1px solid #0687FF',
        color: '#fff',
        // height: this.isBig ? '100%' : '50%'
      };
      return {
        // 深色主题-公共
        common: {
          backgroundColor: this.theme == 'dark' ? 'rgba(9, 25, 45, 1)' : '#fff'
        },
        boxStyle: this.theme == 'dark' ? boxStyle : {},

      }
    },
    themeButtonStyle() {
      return {
        theme: this.theme == 'dark' ? require('@/assets/monitoringSystem/theme-button.png') : require('@/assets/monitoringSystem/theme-btn-light.png'),
        back: this.theme == 'dark' ? require('@/assets/cockpit/back.png') : require('@/assets/monitoringSystem/back.png')
      }
    },
  },
  watch: {},
}
</script>
<style>
.sub-page {
  font-family: Arial, sans-serif !important;
}
</style>
<style lang="scss" scoped>
@import "@/assets/styles/utils.scss";

.sub-page {
  touch-action: none;
  user-select: none;
  transition: 0.3s;

  .sub-page-main {
    width: 100%;
    display: flex;
    overflow: hidden;

    .sub-page-one {
      flex: 1;

      .page-one {
        position: relative;
        width: 100%;
        height: 93vh;
        padding: vwpx(20px) vwpx(20px) vwpx(20px) 0;

        ::v-deep {
          .el-main {
            margin: 0 0 0 vwpx(20px);
            padding: 0;
            // border-radius: vwpx(14px);
            // border: 1px solid #e6ebf5;
            // border-radius: vwpx(14px);
            // box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
            // background-color: #FFF;
            // transition: 0.3s;
            // height: 100%;
          }

          .el-aside {
            position: relative;
            margin: 0;
            border: 1px solid #e6ebf5;
            border-radius: vwpx(14px);
            box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
            background-color: #FFF;
            transition: 0.3s;
            padding: vwpx(30px);
            overflow: hidden;
            height: 100%;
          }
        }

        .spread-packup {
          position: absolute;
          top: 41.5%;
          height: vwpx(330px);
          width: vwpx(36px);
          cursor: pointer;
          background-repeat: no-repeat;
          background-size: 100% 100%;
          display: flex;
          align-items: center;
          transition: 0.3s;
        }

        .spread-packup-dark {
          background-image: url("~@/assets/map/left-close.png");

          .img {
            transform: rotate(90deg);
            width: 100%;
          }

          .spread-img {
            transform: rotate(270deg);
            width: 100%;
          }
        }

        .spread-packup-light-open {
          background-image: url("~@/assets/monitoringSystem/open.png");
        }

        .spread-packup-light-close {
          background-image: url("~@/assets/monitoringSystem/close.png");
        }

        .right-box {
          padding: vwpx(20px);
          border: 1px solid #e6ebf5;
          border-radius: vwpx(14px);
          // margin-right: vwpx(20px);
          box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
        }

        .right-box2 {
          padding: vwpx(20px);
          border: 1px solid #e6ebf5;
          border-radius: vwpx(14px);
          box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
          // height: calc(50% - #{vwpx(10px)});
          height: 100%;
          width: 33vw;
        }
      }
    }

    .sub-page-two {
      flex: 1;

      .page-two {
        width: 100%;
        height: 93vh;
        padding: vwpx(20px);

        ::v-deep {
          .el-card {
            border-radius: vwpx(14px);
          }

          .el-card__body {
            padding: vwpx(20px);
            height: 100%;

          }
        }
      }
    }
  }
}
</style>