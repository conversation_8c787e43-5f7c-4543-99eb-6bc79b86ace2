<template>
  <div class="app-container">
    <el-row :gutter="20">
      <!--筛选区开始-->
      <el-col :span="24" :xs="24">
        <el-row>
          <el-col :span="24">
            <el-form
              ref="queryForm"
              :inline="true"
              :model="queryParams"
              label-width="68px"
              size="small"
            >
              <el-form-item label="" prop="year">
                <el-date-picker
                  v-model="queryParams.year"
                  placeholder="年份"
                  style="width: 240px"
                  type="year"
                  value-format="yyyy"
                >
                </el-date-picker>
              </el-form-item>
              <el-form-item>
                <selectTree
                  :key="'domainId'"
                  v-model="queryParams.domainId"
                  :deptType="100"
                  :deptTypeList="[1, 3, 4]"
                  clearable
                  filterable
                  placeholder="管养单位"
                  style="width: 240px"
                />
              </el-form-item>
              <el-form-item>
                <selectTree
                  :key="'constructionUnit'"
                  v-model="queryParams.checkDomainId" :data-rule="false"
                  clearable
                  filterable
                  :dept-type="100"
                  :filter-keys="['云南省交通投资建设集团有限公司', '云南交投投资有限公司']"
                  :expand-all="false"
                  placeholder="施工单位"
                  style="width: 240px"
                />
              </el-form-item>
              <el-form-item>
                <RoadSection
                  v-model="queryParams.maiSecId"
                  :deptId="queryParams.domainId"
                  placeholder="路段"
                  style="width: 240px"
                />
              </el-form-item>
              <el-form-item>
                <el-input
                  v-model="queryParams.projName"
                  placeholder="项目名称"
                  style="width: 240px"
                ></el-input>
              </el-form-item>
              <el-form-item>
                <el-input
                  v-model="queryParams.projCode"
                  placeholder="项目编码"
                  style="width: 240px"
                ></el-input>
              </el-form-item>
              <el-form-item>
                <el-input
                  v-model="queryParams.name"
                  placeholder="任务单名称"
                  style="width: 240px"
                ></el-input>
              </el-form-item>
              <el-form-item>
                <el-input
                  v-model="queryParams.code"
                  placeholder="任务单编码"
                  style="width: 240px"
                ></el-input>
              </el-form-item>
              <el-form-item>
                <el-date-picker
                  v-model="issueDate"
                  type="daterange"
                  range-separator="至"
                  start-placeholder="发出起始时间"
                  end-placeholder="发出截止时间"
                  value-format="yyyy-MM-dd"
                  style="width: 240px"
                >
                </el-date-picker>
              </el-form-item>
              <el-form-item>
                <el-button
                  icon="el-icon-search"
                  size="mini"
                  type="primary"
                  @click="handleQuery"
                  >搜索</el-button
                >
                <el-button
                  icon="el-icon-refresh"
                  size="mini"
                  @click="resetQuery"
                  >重置</el-button
                >
              </el-form-item>
            </el-form>
          </el-col>
        </el-row>
        <!--筛选区结束-->

        <!--操作按钮区开始-->
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button
              icon="el-icon-view"
              size="mini"
              type="warning"
              @click="openOperateDialog"
              >审核意见
            </el-button>
          </el-col>

          <el-col :span="1.5">
            <el-button
              icon="el-icon-download"
              size="mini"
              type="success"
              @click="exportList"
              v-has-menu-permi="['check:construction:pendingexport']"
              >导出
            </el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button
              icon="el-icon-time"
              size="mini"
              type="success"
              v-has-menu-permi="['check:construction:filelist']"
              @click="openFileModel"
              >附件管理
            </el-button>
          </el-col>
          <right-toolbar
            :columns="columns"
            :showSearch.sync="showSearch"
            @queryTable="handleQuery"
          ></right-toolbar>
        </el-row>
        <!--操作按钮区结束-->
        <!--数据表格开始-->
        <div class="tableDiv">
          <el-table v-adjust-table
            ref="dataTable"
            v-loading="loading"
            :data="tableData"
            :height="showSearch ? 'calc(100vh - 330px)' : 'calc(100vh - 270px)'"
            border
            highlight-current-row
            row-key="id"
            size="mini"
            stripe
            style="width: 100%"
            @row-click="handleClickRow"
            @expand-change="tableExpand"
          >
            <el-table-column type="expand">
              <template slot-scope="props">
                <el-table v-adjust-table
                  :data="props.row.constructionDetailList"
                  style="width: 100%"
                >
                  <el-table-column prop="" align="center" label="">
                  </el-table-column>
                  <el-table-column
                    prop="schemeCode"
                    align="center"
                    label="子目号"
                  >
                  </el-table-column>
                  <el-table-column
                    prop="schemeName"
                    align="center"
                    label="养护方法"
                  >
                  </el-table-column>
                  <el-table-column
                    prop="calcDesc"
                    align="center"
                    label="计算式"
                  >
                  </el-table-column>
                  <el-table-column prop="num" align="center" label="方法数量">
                  </el-table-column>
                  <el-table-column prop="unit" align="center" label="方法单位">
                  </el-table-column>
                  <el-table-column prop="price" align="center" label="单价">
                  </el-table-column>
                  <el-table-column prop="amount" align="center" label="金额">
                  </el-table-column>
                  <el-table-column prop="remark" align="center" label="备注">
                  </el-table-column>
                </el-table>
                <pagination
                  v-show="props.row.totalNum > 0"
                  :limit.sync="props.row.queryData.pageSize"
                  :page.sync="props.row.queryData.pageNum"
                  :total="props.row.totalNum"
                  @pagination="getRowDetailList(props.row)"
                />
              </template>
            </el-table-column>
            <el-table-column
              align="center"
              label="序号"
              type="index"
              width="50"
            />
            <template v-for="(column, index) in columns">
              <el-table-column
                v-if="column.visible"
                :label="column.label"
                :prop="column.field"
                :width="column.width"
                align="center"
                show-overflow-tooltip
              >
                <template slot-scope="scope">
                  <dict-tag
                    v-if="column.dict"
                    :options="dict.type[column.dict]"
                    :value="scope.row[column.field]"
                  />
                  <template v-else-if="column.slots">
                    <RenderDom
                      :index="index"
                      :render="column.render"
                      :row="scope.row"
                    />
                  </template>
                  <span v-else-if="column.isTime">{{
                    parseTime(scope.row[column.field], "{y}-{m}-{d}")
                  }}</span>
                  <span v-else>{{ scope.row[column.field] }}</span>
                </template>
              </el-table-column>
            </template>
            <el-table-column
              align="center"
              class-name="small-padding fixed-width"
              fixed="right"
              label="操作"
              width="250"
            >
              <template slot-scope="scope">
                <template v-if="prosStatus == 1">
                  <el-button
                    icon="el-icon-view"
                    size="mini"
                    type="text"
                    v-has-menu-permi="['check:construction:edit']"
                    @click="handleDetail(scope.row, 1, '编辑任务单')"
                    >编辑
                  </el-button>
                  <el-button
                    icon="el-icon-view"
                    size="mini"
                    type="text"
                    v-has-menu-permi="['check:construction:remove']"
                    @click="handleDelete(scope.row)"
                    >删除
                  </el-button>
                  <el-button
                    icon="el-icon-view"
                    size="mini"
                    type="text"
                    v-has-menu-permi="['check:construction:process']"
                    @click="handleDetail(scope.row, 2, '提交任务单')"
                    >提交
                  </el-button>
                </template>
                <template v-if="prosStatus == 2">
                  <el-button
                    icon="el-icon-view"
                    size="mini"
                    type="text"
                    v-has-menu-permi="['check:construction:process']"
                    @click="handleDetail(scope.row, 3, '审核任务单')"
                    >审核
                  </el-button>
                  <el-button
                    icon="el-icon-view"
                    size="mini"
                    type="text"
                    v-has-menu-permi="['check:construction:edit']"
                    @click="handleDetail(scope.row, 1, '编辑任务单')"
                  >编辑
                  </el-button>
                </template>
                <template v-if="prosStatus == 3">
                  <el-button
                    icon="el-icon-delete"
                    size="mini"
                    type="text"
                    v-has-menu-permi="['check:construction:process']"
                    @click="handleDetail(scope.row, 4, '签发任务单')"
                    >签发
                  </el-button>
                  <el-button
                    icon="el-icon-view"
                    size="mini"
                    type="text"
                    v-has-menu-permi="['check:construction:edit']"
                    @click="handleDetail(scope.row, 1, '编辑任务单')"
                  >编辑
                  </el-button>
                </template>
                <template v-if="prosStatus == 4">
                  <el-button
                    size="mini"
                    type="text"
                    v-has-menu-permi="['check:construction:process']"
                    @click="handleDetail(scope.row, 5, '接收任务单')"
                    >接收
                  </el-button>
                  <!-- <el-button
                    size="mini"
                    type="text"
                    v-has-menu-permi="['check:construction:process']"
                    @click="handleRevoke(scope.row, 5, '拒收任务单')"
                    >拒收
                  </el-button> -->
                </template>
              </template>
            </el-table-column>
          </el-table>
          <pagination
            v-show="total > 0"
            :limit.sync="queryParams.pageSize"
            :page.sync="queryParams.pageNum"
            :total="total"
            @pagination="handleQuery"
          />
        </div>
        <!--数据表格结束-->
      </el-col>
    </el-row>
    <el-drawer
      :wrapperClosable="false"
      :title="detailTitle"
      :visible.sync="openDetail"
      size="70%"
      destroy-on-close
    >
      <task-detail
        :status="status"
        :type="type"
        :read-only="readonly"
        :row="row"
        @close="closeDetail"
      ></task-detail>
    </el-drawer>
    <el-dialog
      title="操作意见"
      :visible.sync="openOperateInfo"
      width="80%"
      destroy-on-close
    >
      <operateInfo
        :businessKey="row.id"
        :getNodeInfo="getCurNodeInfo"
      ></operateInfo>
    </el-dialog>
    <el-dialog
      title="附件列表"
      destroy-on-close
      :visible.sync="openFile"
      width="500px"
    >
      <file-upload
        v-if="row.fileId"
        v-model="row.fileId"
        :owner-id="row.fileId"
      ></file-upload>
    </el-dialog>
  </div>
</template>

<script>
import Tables from "@/views/patrol/frequencySettings/tables.vue";
import RoadSection from "@/views/baseData/components/roadSection/index.vue";
import selectTree from "@/components/DeptTmpl/selectTree.vue";
import TaskDetail from "@/views/regularTesting/taskManage/component/taskDetail.vue";
import operateInfo from "@/views/dailyMaintenance/component/operateInfo.vue";
import {
  getConstructionList,
  deleteConstruction,
  getConstructionFileList,
  addConstructionFile,
  getConstructionNodeInfo,
  getConstructionDetailList,
} from "@/api/regularTesting/taskManage/taskList";
import { v4 as uuidv4 } from "uuid";

export default {
  components: {
    operateInfo,
    TaskDetail,
    selectTree,
    RoadSection,
    Tables,
  },
  dicts: [
    "test_task_type",
    "task_type",
    "project_type",
    "affiliation_project_type",
  ],
  data() {
    return {
      showSearch: false,
      queryParams: {
        pageNum: 1,
        pageSize: 50,
      },
      total: 0,
      loading: false,
      status: 4,
      type: 1,
      readonly: true,
      columns: [
        {
          key: 2,
          width: 100,
          field: "status",
          label: `状态`,
          visible: true,
          dict: "test_task_type",
        },
        {
          key: 3,
          width: 100,
          field: "projName",
          label: `项目名称`,
          visible: true,
        },
        {
          key: 4,
          width: 100,
          field: "projCode",
          label: `项目编码`,
          visible: true,
        },
        {
          key: 6,
          width: 100,
          field: "name",
          label: `任务单名称`,
          visible: true,
        },
        {
          key: 7,
          width: 100,
          field: "code",
          label: `任务单编号`,
          visible: true,
        },
        {
          key: 8,
          width: 100,
          field: "maiSecName",
          label: `路段名称`,
          visible: true,
        },
        {
          key: 9,
          width: 100,
          field: "domainName",
          label: `管养单位`,
          visible: true,
        },
        {
          key: 10,
          width: 100,
          field: "checkDomainName",
          label: `施工单位`,
          visible: true,
        },
        {
          key: 11,
          width: 100,
          field: "checkConName",
          label: `施工合同`,
          visible: true,
        },
        {
          key: 12,
          width: 100,
          field: "issueDate",
          label: `发出时间`,
          visible: true,
        },
        {
          key: 18,
          width: 100,
          field: "content",
          label: `工作内容`,
          visible: true,
        },
        {
          key: 22,
          width: 120,
          field: "beginDate",
          label: `计划开始时间`,
          visible: true,
        },
        {
          key: 23,
          width: 120,
          field: "endDate",
          label: `计划完成时间`,
          visible: true,
        },
      ],
      tableData: [],
      detailTitle: "编辑任务单",
      openDetail: false,
      openOperateInfo: false,
      row: {},
      openFile: false,
      issueDate: []
    };
  },
  props: {
    prosStatus: {
      type: Number,
      default: 1,
    },
  },
  watch: {
    prosStatus: {
      handler(val) {
        if (val === 1) {
          this.queryParams.status = 0;
        }
        if (val === 2) {
          this.queryParams.status = 1;
        }
        if (val === 3) {
          this.queryParams.status = 2;
        }
        if (val === 4) {
          this.queryParams.status = 3;
        }
      },
      immediate: true,
    },
  },
  mounted() {
    if (this.prosStatus == 1) this.$set(this.queryParams, 'status', '0')
    this.handleQuery();
  },
  methods: {
    handleQuery() {
      if (
        this.issueDate &&
        this.issueDate.length == 2
      ) {
        this.queryParams.issueStartDate =
          this.issueDate[0];
        this.queryParams.issueEndDate = this.issueDate[1];
      }
      this.loading = true;
      getConstructionList(this.queryParams).then((res) => {
        this.tableData = res.rows;
        this.total = res.total;
        this.loading = false;
        this.tableData.forEach((item) => {
          item.queryData = {
            projConId: item.id,
            pageNum: 1,
            pageSize: 10,
          };
          item.totalNum = 0;
        });
      });
    },
    resetQuery() {
      this.queryParams = {
        pageNum: 1,
        pageSize: 50,
      };
      if (this.prosStatus === 1) {
        this.queryParams.status = 0;
      }
      if (this.prosStatus === 2) {
        this.queryParams.status = 1;
      }
      if (this.prosStatus === 3) {
        this.queryParams.status = 2;
      }
      this.handleQuery()
    },
    // 导出清单按钮
    exportList() {
      if (
        this.issueDate &&
        this.issueDate.length == 2
      ) {
        this.queryParams.issueStartDate =
          this.issueDate[0];
        this.queryParams.issueEndDate = this.issueDate[1];
      }
      this.download(
        "manager/check/construction/pending/export",
        { ...this.queryParams },
        `checksTask_${new Date().getTime()}.xlsx`,
        {
          headers: { "Content-Type": "application/json;" },
          parameterType: "body",
        }
      );
    },
    tableExpand(row, expandedRows) {
      const isExpand = expandedRows.some((item) => row.id === item.id);
      if (isExpand) {
        this.getRowDetailList(row);
      }
    },
    getRowDetailList(row) {
      getConstructionDetailList(row.queryData).then((res) => {
        this.$set(row, "totalNum", res.total || 0);
        if (res.rows) {
          this.$set(row, "constructionDetailList", res.rows);
        }
      });
    },
    handleClickRow(e) {
      this.row = e;
    },
    handleDetail(row, status, title) {
      this.status = status;
      this.readonly = false;
      this.detailTitle = title;
      this.row = row;
      this.openDetail = true;
    },
    handleRevoke(row, status, title) {
      this.type = 2;
      this.handleDetail(row, status, title);
    },
    closeDetail() {
      this.type = 1;
      this.openDetail = false;
      this.handleQuery();
    },
    handleDelete(row) {
      this.$modal.confirm("是否删除").then(() => {
        deleteConstruction(row.id).then((res) => {
          this.handleQuery();
        });
      });
    },
    getCurNodeInfo(data) {
      return getConstructionNodeInfo(data);
    },
    openOperateDialog() {
      if (!this.row.id) {
        this.$message.warning("请先选择一条记录！");
        return;
      }
      this.openOperateInfo = true;
    },
    async openFileModel() {
      if (!this.row.id) {
        this.$message.warning("请先选择一条记录！");
        return;
      }
      this.loading = true;
      // 查询附件
      await getConstructionFileList({ inId: this.row.id })
        .then(async (res) => {
          if (res.data) {
            this.row.fileId = res.data.fileId;
          } else {
            // 没有新增一条
            const requestData = {
              fileId: uuidv4().substring(0, 20),
              inId: this.row.id,
              registerType: 0,
            };
            this.row.fileId = requestData.fileId;

            await addConstructionFile(requestData);
          }
          this.openFile = true;
        })
        .finally(() => {
          this.loading = false;
        });
    },
  },
};
</script>

<style lang="scss" scoped></style>
<style lang="scss" scoped>
@import "@/assets/styles/business.scss";
</style>
