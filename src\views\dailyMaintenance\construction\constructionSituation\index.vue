<template>
  <div class="app-container maindiv">
    <registration-list ref="regis" :page-list="pageList" :disStage="-2">
      <template #btn>
        <el-col :span="1.5">
          <el-button
              icon="el-icon-download"
              size="mini"
              v-has-menu-permi="['constructionStage:construction:export']"
              type="warning"
              @click="exportList"
          >导出清单
          </el-button
          >
        </el-col>
      </template>
    </registration-list>
  </div>
</template>
<script>
import registrationList from '../component/registrationList.vue'
import {pageList} from '@/api/dailyMaintenance/construction/constructionSituation'
export default {
  name: 'ConstructionSituation',
  components: {registrationList},
  data() {
    return {}
  },
  mounted() {
		this.$refs.regis.columns.splice(6, 0, {key: 23, width: 120, field: 'sumFund', label: `总金额`, visible: true, slots: true, render: (row, index) => {
      return (<div>{Math.round(row.sumFund)}</div>)
      }})
  },
	methods: {
    pageList,
    // 导出清单按钮
    exportList() {
      this.$refs.regis.queryParams.year = this.$refs.regis.queryParams.yearStr ? parseInt(this.$refs.regis.queryParams.yearStr) : null
      this.download(
          'manager/constructionStage/export',
          {...this.$refs.regis.queryParams},
          `constructionStage_${new Date().getTime()}.xlsx`,
          {
            headers: {'Content-Type': 'application/json;'},
            parameterType: 'body'
          }
      )
    },
  }
}
</script>
