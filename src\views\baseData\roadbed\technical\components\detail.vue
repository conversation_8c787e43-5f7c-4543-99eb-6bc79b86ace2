<template>
    <div>
      <el-dialog
        title="路面养护处治情况"
        :visible.sync="showDetail"
        width="85%"
        append-to-body
        :before-close="handleClose"
        :close-on-click-modal="true"
      >
        <div style="display: flex; align-items: center; margin-bottom: 10px">
          <el-button
            type="primary"
            @click="handleAdd"
          >新增</el-button>
          <el-button
            type="primary"
            @click="handleEdit"
          >编辑</el-button>
          <el-button
            type="primary"
            @click="handleRemove"
          >删除</el-button>
          <el-button
            v-hasPermi="['baseData:import:execute']"
            type="primary"
            @click="importUpdate"
          >导入更新</el-button>
          <el-button
            v-hasPermi="['baseData:import:execute']"
            type="primary"
            @click="importAdd"
          >导入新增</el-button>
        </div>
        <el-table
          ref="table"
          v-loading="loading"
          height="250px"
          border
          :data="tableData"
          :header-cell-style="{'background':'#F2F3F5','color': '#212529','height': '36px','font-weight': '700','font-size': '14px'}"
          :cell-style="{'height': '36px'}"
          @selection-change="handleSelectionChange"
          @row-click="handleRowClick"
        >
          <el-table-column
            fixed
            type="selection"
            width="50"
            align="center"
          />
          <el-table-column
            label="序号"
            type="index"
            width="50"
            align="center"
          />

          <el-table-column
            label="处治年份"
            align="center"
            prop="dealYear"
            min-width="120"
            show-overflow-tooltip
          />
          <el-table-column
            label="养护分类"
            align="center"
            prop="maintenanceType"
            min-width="120"
            show-overflow-tooltip
          >
            <template slot-scope="scope">
              <span v-if="scope.row && scope.row.maintenanceType">
                <DictTag
                  :value="scope.row.maintenanceType"
                  :options="dict.type.base_maintenance_type"
                />
              </span>
            </template>
          </el-table-column>
          <el-table-column
            label="养护工程类别"
            align="center"
            prop="projectType"
            min-width="120"
            show-overflow-tooltip
          >
            <template slot-scope="scope">
              <span v-if="scope.row && scope.row.projectType">
                <DictTag
                  :value="scope.row.projectType"
                  :options="dict.type.base_project_type"
                />
              </span>
            </template>
          </el-table-column>
          <el-table-column
            label="结算金额"
            align="center"
            prop="calcFund"
            min-width="120"
            show-overflow-tooltip
          />
          <el-table-column
            label="处治车道"
            align="center"
            prop="lane"
            min-width="120"
            show-overflow-tooltip
          >
            <template slot-scope="scope">
              <span v-if="scope.row && scope.row.lane">
                <DictTag
                  :value="scope.row.lane"
                  :options="dict.type.lane"
                />
              </span>
            </template>
          </el-table-column>
          <el-table-column
            label="起点桩号"
            align="center"
            prop="beginStake"
            min-width="120"
            show-overflow-tooltip
          >
            <template slot-scope="scope">
              {{ formatPile(scope.row.beginStake) }}
            </template>
          </el-table-column>
          <el-table-column
            label="终点桩号"
            align="center"
            prop="endStake"
            min-width="120"
            show-overflow-tooltip
          >
            <template slot-scope="scope">
              {{ formatPile(scope.row.endStake) }}
            </template>
          </el-table-column>
          <el-table-column
            label="长度(m)"
            align="center"
            prop="length"
            min-width="120"
            show-overflow-tooltip
          />
          <el-table-column
            label="宽度(m)"
            align="center"
            prop="width"
            min-width="120"
            show-overflow-tooltip
          />
          <el-table-column
            label="面积(m²)"
            align="center"
            prop="area"
            min-width="120"
            show-overflow-tooltip
          />
          <el-table-column
            label="养护措施"
            align="center"
            prop="measure"
            min-width="120"
            show-overflow-tooltip
          />
        </el-table>
        <pagination
          :total="total"
          :page.sync="pageNum"
          :limit.sync="pageSize"
          @pagination="getList"
        />
        <div slot="footer">
          <el-button @click="handleClose">取 消</el-button>
        </div>
      </el-dialog>
      <Form
        :showAddEdit="showComDetail"
        :formData="detailFormData"
        :choseId="choseId"
        @close="()=>showComDetail=false"
        @refresh="() => {showComDetail = false;getList()}"
      />

      <ImportData
        v-if="showImportAdd"
        :is-update="isUpdate"
        :dialog-visible="showImportAdd"
        :import-base-type="importBaseType"
        :import-type="importType"
        @close="closeImportAdd"
      />
     
      </div>
  </template>
  
  <script>
  import {
    getMaintainListPage,
    deleteMaintain
  } from '@/api/baseData/roadbed/assess/index'
  import DictTag from "@/components/DictTag";
  import Form from './form.vue'
  import ImportData from '@/views/baseData/components/importData/index.vue'
  
  export default {
    name: 'roadbed-maintenance',
    components: {Form,ImportData},
    props: {
      showDetail: { type: Boolean, default: false },
      choseId: { type: undefined, default: '' },
      formData: { type: Object, default: () => {} }
    },
    dicts: ['base_maintenance_type', 'base_project_type', 'lane','sys_surface_type'],
    data() {
      return {
        loading: false,
        pageNum: 1,
        pageSize: 20,
        total: 0,
        detailFormData: {},
        selection: [],
        tableData: [],
        showComDetail:false,
        componId:null,
        importBaseType: '32',
        showImportAdd: false,
        importType: 1,
        isUpdate: false,
        ids: []
        
      }
    },
    created() {
      
    },
    mounted() {
      this.getList()
    },
    methods: {
      handleAdd() {
        this.showComDetail = true
      
      },

      handleEdit() {
        if (this.ids.length === 1) {
          this.showComDetail = true
          this.detailFormData = this.selection[0]
        }else{
          this.$message.warning('请选择一条数据进行编辑！')
          return
        }
        
      },
      getList() {
        if (!this.choseId) return
        this.loading = true
        let obj = {
          id:this.choseId,
          pageNum: this.pageNum,
          pageSize: this.pageSize
        }
        getMaintainListPage(obj)
          .then(res => {
            if (res.code === 200) {
              this.tableData = res.rows
              this.total = res.total
            }
            this.loading = false
          })
          .catch(() => {
            this.loading = false
          })
      },
      handleClose() {
        this.$emit('close')
      },
          // 导入更新按钮
    importUpdate() {
      this.isUpdate = true
      this.showImportAdd = true
      this.importType = 1
    },
    // 导入新增按钮
    importAdd() {
      this.isUpdate = false
      this.showImportAdd = true
      this.importType = 2
    },
   
      handleView(row) {
        this.showComDetail = true
        this.componId = row.id
      },
      closeImportAdd(v) {
        this.showImportAdd = false
        if (v) this.getList()
      },
      handleRemove() {
        if (this.ids.length === 0) {
          this.$message.warning('请选择至少一条数据进行删除！')
          return
        }
        this.$modal
          .confirm('确认删除？')
          .then(() => {
            deleteMaintain(this.ids.join('')).then(res => {
              if (res.code === 200) {
                this.getList()
                this.$modal.msgSuccess('删除成功')
              }
            })
          })
          .catch(() => {})
      },
      // 表格点击勾选
      handleRowClick(row) {
        row.isSelected = !row.isSelected
        this.$refs.table.toggleRowSelection(row)
      },
      // 多选框选中数据
      handleSelectionChange(selection) {
        this.ids = selection.map(item => item.id)
        this.selection = selection
      },
    },
    watch: {
      showDetail(val) {
        if (val) {
          this.getList()
        }
      }
    }
  }
  </script>
  
  <style lang="scss" scoped>
  ::v-deep .el-form-item {
    margin-bottom: 10px;
    color: #1d2129;
  }
  </style>