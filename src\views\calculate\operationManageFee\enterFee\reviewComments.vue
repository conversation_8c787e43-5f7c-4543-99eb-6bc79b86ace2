<template>
  <div class="maindiv">
    <el-row :gutter="20">
      <el-col :span="24" class="mb8">
        <el-button size="mini"
                   type="primary"
                   icon="el-icon-download">导出
        </el-button>
      </el-col>
      <el-col :span="24" :xs="24">
        <!--数据表格开始-->
        <div class="tableDiv">
          <el-table v-adjust-table stripe size="mini" height="200px"
                    style="width: 100%" v-loading="loading" border :data="dataList"
                    @selection-change="handleSelectionChange">
            <el-table-column type="selection" width="50" align="center"/>
            <el-table-column fixed label="序号" type="index" width="50"></el-table-column>
            <template v-for="column in columns">
              <el-table-column :label="column.label"
                               v-if="column.visible"
                               align="center"
                               :prop="column.field">
                <template slot-scope="scope">
                  <dict-tag v-if="column.dict" :options="dict.type[column.dict]" :value="scope.row.type"/>
                  <span v-else>{{ scope.row[column.field] }}</span>
                </template>
              </el-table-column>
            </template>
          </el-table>
          <pagination
            v-show="total>0"
            :total="total"
            :page.sync="queryParams.pageNum"
            :limit.sync="queryParams.pageSize"
            @pagination="getList"
          />
        </div>
        <!--数据表格结束-->
      </el-col>
    </el-row>
  </div>
</template>

<script>

export default {
  name: "operateInfo",
  components: {},
  data() {
    return {
      loading: false,
      queryParams: {
        pageNum: 1,
        pageSize: 3,
      },
      // 列信息
      columns: [
        {key: 0, field: 'field1', label: `操作节点`, visible: true},
        {key: 1, field: 'field2', label: `操作类型`, visible: true},
        {key: 2, field: 'field3', label: `操作人`, visible: true},
        {key: 3, field: 'field4', label: `操作时间`, visible: true},
        {key: 4, field: 'field5', label: `操作意见`, visible: true},
        {key: 5, field: 'field6', label: `方向`, visible: true}
      ],
      // 表格数据
      dataList: [
      ],
      // 总条数
      total: 5,
    };
  },
  watch: {},
  created() {

  },
  methods: {
  }
};
</script>
<style>
</style>
<style lang="scss" scoped>
@import "@/assets/styles/business.scss";
</style>
