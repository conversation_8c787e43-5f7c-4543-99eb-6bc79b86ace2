<template>
  <div class="app-container">
    <el-row :gutter="20">
      <!--部门数据-->
      <el-col :span="relaNav ? 4 : 0" :xs="24" class="leftDiv">
        <!--折叠图标-->
        <div class="leftIcon" @click="relaNav = false">
          <span class="el-icon-caret-left"></span>
        </div>
        <div class="head-container" style="width: 300px">
          <el-tree
            ref="tree"
            :data="filteredTreeData"
            :default-expanded-keys="[1]"
            :expand-on-click-node="false"
            :props="defaultProps"
            highlight-current
            node-key="code"
            @node-click="handleNodeClick"
          >
          </el-tree>
        </div>
      </el-col>
      <!--角色数据-->
      <el-col :span="relaNav ? 20 : 24" :xs="24">
        <!--展开图标-->
        <div v-show="!relaNav" class="rightIcon" @click="relaNav = true">
          <span class="el-icon-caret-right"></span>
        </div>
        <el-row :gutter="10" class="mb8">
          <el-form ref="queryForm" :inline="true" :model="queryParams" label-width="68px" size="small">
            <el-form-item label="">
              <el-input
                v-model="queryParams.sensorInstallCode"
                placeholder="请输入安装代码"
                clearable
                size="small"
                @keyup.enter.native="handleQuery"
              ></el-input>
            </el-form-item>
            <el-form-item label="">
              <el-input
                v-model="queryParams.monitorTypeCode"
                placeholder="请输入监测值"
                clearable
                size="small"
                @keyup.enter.native="handleQuery"
              ></el-input>
            </el-form-item>
            <el-form-item>
              <el-button icon="el-icon-search" size="mini" type="primary" @click="handleQuery">搜索</el-button>
              <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
              <el-button icon="el-icon-delete" size="mini" type="danger" @click="handleDelete">删除</el-button>

            </el-form-item>
          </el-form>
        </el-row>
        <!-- 筛选区结束 -->
        <!-- 数据表格开始 -->
        <div class="tableDiv">
          <el-table v-adjust-table v-loading="loading" :data="dataList"
                    :height="'calc(100vh - 260px)'" border size="mini"
                    @selection-change="handleSelectionChange"
                    style="width: 100%">
            <el-table-column align="center" fixed="left" type="selection" width="55"></el-table-column>
            <el-table-column align="center" fixed label="序号" type="index" width="100"></el-table-column>
            <template v-for="(column, index) in columns">
              <el-table-column v-if="column.visible" :key="index" :label="column.label" :prop="column.field"
                               align="center" show-overflow-tooltip>
                <template slot-scope="scope">
                  <span v-if="column.field != 'alertLevel'">{{ scope.row[column.field] }}</span>
                  <span v-else style='display: flex; justify-content: space-evenly'>
                    <el-tag v-for='item in scope.row[column.field]' :key="item" :type="item == '一级' ? 'danger' : item == '二级' ? 'warning' : 'success'">{{item}}</el-tag>
                  </span>
                </template>
              </el-table-column>
            </template>
            <el-table-column align="center" fixed="right" label="操作" width="150">
              <template slot-scope="scope">
                <el-button
                  size="mini"
                  type="text"
                  icon="el-icon-edit"
                  @click="handleUpdate(scope.row)"
                >编辑</el-button>
              </template>
            </el-table-column>
          </el-table>
          <pagination
            v-show="total > 0"
            :limit.sync="queryParams.pageSize"
            :page.sync="queryParams.pageNum"
            :total="total"
            @pagination="handleQuery"
          />
        </div>
        <!-- 数据表格结束 -->
      </el-col>
    </el-row>
    <el-dialog title="编辑" :visible.sync="editDialog" v-if="editDialog" append-to-body width="80%" @close="editDialog = false">
      <el-form ref="elForm" :model="formData" :rules="rules" label-width="160px" size="medium" v-loading="loading">
        <el-row>
          <el-col :span="24">
            <el-form-item label="开启预警">
              <el-switch
                v-model="formData.openFlag"
                :active-value="true"
                :inactive-value="false"
              ></el-switch>
            </el-form-item>
          </el-col>
          <template v-if="formData.openFlag">
            <el-col :span="24" style="display: flex;flex-wrap: nowrap;">
              <el-form-item label="蓝色预警">
                <el-switch
                  v-model="formData.oneFlag"
                  :active-value="true"
                  :inactive-value="false"
                ></el-switch>
              </el-form-item>
              <el-form-item label="黄色预警">
                <el-switch
                  v-model="formData.twoFlag"
                  :active-value="true"
                  :inactive-value="false"
                ></el-switch>
              </el-form-item>
              <el-form-item label="橙色预警">
                <el-switch
                  v-model="formData.threeFlag"
                  :active-value="true"
                  :inactive-value="false"
                ></el-switch>
              </el-form-item>
              <el-form-item label="红色预警">
                <el-switch
                  v-model="formData.fourFlag"
                  :active-value="true"
                  :inactive-value="false"
                ></el-switch>
              </el-form-item>
              <el-form-item label="预警间隔（分钟）">
                <el-input-number v-model="formData.triggerIntervalMin" placeholder="请输入预警间隔（分钟）" size="small"></el-input-number>
              </el-form-item>
              <el-form-item label="生成记录间隔（分钟）">
                <el-input-number v-model="formData.alertIntervalMin" placeholder="请输入预警间隔（分钟）" size="small"></el-input-number>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="预警规则">
                <a-select ref="aSelect" @change="changeRuleType" :api-method="getAlertRuleTypePage" :params="ruleTypePage" :default-props="{label: 'ruleName',value: 'id'}" v-model="formData.alertRuleTypeId" style="width: 300px;"></a-select>
              </el-form-item>
            </el-col>
            <template v-if="formData.alertRuleTypeId">
              <el-col :span="24">
                <div class="mb10">
                  <span class="title">规则描述：</span>
                  <span>{{formData.ruleDescription || ''}}</span>
                </div>
              </el-col>
              <el-col :span="24">
                <div class="mb10">
                  <span class="title">参数描述：</span>
                  <span>$1 < x < $2,x >= $3
        &lt;x&gt;代表获取到的数据，<$1>代表第一个参数，判断式权重高者优先判断，逗号分隔的判断式满足其一即判断为该级预警。</span>
                </div>
              </el-col>
              <el-col :span="24">
                <div class="mb10">
                  <span class="title">参数详情：</span>
                </div>
              </el-col>
              <el-col :span="24" v-for="(item,index) in ruleData.alertRuleChainList">
                <div style='display: flex;flex-wrap: nowrap;justify-content: flex-start;' class="mb10" v-if="formData.ruleParameterList && formData.ruleParameterList.length > 0">
                  <el-form-item :label="item.detailRuleName">
                  </el-form-item>
                  <template :span="8" v-for="(detail, detailIndex) in item.ruleDetailList">
                    <el-form-item :label="detail">
                      <el-input style="width: 150px" v-model="formData.ruleParameterList[getGlobalIndex(index, detailIndex)].param" size="small"></el-input>
                      {{ item.judgeMethod == 'DEFAULT' ? unit : detailIndex == 0 ? '级' : '次' }}
                    </el-form-item>
                  </template>
                </div>
              </el-col>
            </template>
          </template>
        </el-row>
        <div style='text-align: right'>
          <el-button @click='editDialog = false'>取消</el-button>
          <el-button type='primary' @click='handleConfirm'>确定</el-button>
        </div>
      </el-form>
    </el-dialog>
  </div>
</template>

<script>
import {getJCDomainTree, getSensorDeviceTreeNode, getAlertRecordPage, deleteAlertRecord} from "@/api/jgjc/baseInfo/alarmLog";
import {getSensorTreeNode} from "@/api/jgjc/baseInfo/sensorManagement";
import {
  addThresholdInfo,
  deleteAlertManage,
  getAlertManagePage,
  getAlertManageRuleParamsDetail, getAlertRuleTypePage, getValueTypePage,
} from '@/api/jgjc/healthLevel/thresholdManage'
import ASelect from "@/views/jgjc/component/aSelect/index.vue";

export default {
  name: "ContentManagement",
  components: {ASelect},
  data() {
    return {
      // 遮罩层
      loading: false,
      // 左侧组织树
      relaNav: true,
      relaOptions: [],
      filteredTreeData: [],
      defaultProps: {
        children: 'children',
        label: 'label',
        isLeaf: (data) => {
          // 通过该字段判断是否是叶子节点（没有子节点）
          // 这里假设接口返回的节点中，有hasChildren字段标识是否有子节点
          return !data.children
        }
      },
      loadedKeys: new Set(), // 记录已加载过的节点key，避免重复加载
      // 总条数
      total: 0,
      dataList: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
      },
      // 列信息
      columns: [
        {key: 0, field: 'structureName', label: '结构物名称', visible: true},
        {key: 1, field: 'sensorInstallCode', label: '设备安装代码', visible: true},
        {key: 2, field: 'monitorTypeName', label: '监测值', visible: true},
        {key: 3, field: 'alertTypeShow', label: '预警类型', visible: true},
        {key: 4, field: 'alertLevel', label: '预警级别', visible: true},
        {key: 5, field: 'configYear', label: '年份', visible: true},
      ],
      ids: [],
      editDialog: false,
      formData: {
        ruleParameterList: []
      },
      rules: {},
      ruleTypePage: {
        pageNum: 1,
        pageSize: 100
      },
      ruleData: {},
      unit: ''
    };
  },
  created() {
    this.getDeptTree()
  },
  methods: {
    getAlertRuleTypePage,
    // 查询部门下拉树结构
    getDeptTree() {
      getJCDomainTree({}).then(response => {
        this.relaOptions = response.data;
        this.filteredTreeData = [...this.relaOptions]
      });
    },
    // 树节点点击事件
    handleNodeClick(nodeData, node) {
      // 如果节点没有子节点且未被加载过
      if (node.level == 4 && !node.childNodes.length && nodeData.structureId && !this.loadedKeys.has(nodeData.structureId) && node.level == 4 ) {
        this.loadChildren(node)
      }
      let params = {}
      if (node.level == 1) {
        params.domainName = nodeData.deptName
      } else if (node.level == 2) {
        params.domainName = node.parent.data.deptName
        params.roadName = nodeData.label
      } else if (node.level == 3) {
        params.domainName = node.parent.parent.data.deptName
        params.roadName = node.parent.data.label
        params.structureType = nodeData.structureType
      } else {
        params = JSON.parse(JSON.stringify(nodeData))
        // 判断params里面有没有structureId 没有就从父级找 一直找到为止 找到后赋值给params
        let tempNode = node
        while (!tempNode.data.structureId) {
          tempNode = tempNode.parent
        }
        params.structureId = tempNode.data.structureId
        this.structureId = params.structureId
        delete params.children
      }
      this.queryParams = {
        pageNum: 1,
        pageSize: 10,
        level: node.level,
        ...params
      }
      this.handleQuery()
    },

    // 加载子节点数据
    async loadChildren(node) {
      const nodeData = node.data
      // 显示加载状态
      node.loading = true
      try {
        const res = await getSensorTreeNode({ structureId: nodeData.structureId, pageNum: 1, pageSize: 100 })
        if (res.data && res.data.length > 0) {
          // 给树节点添加子节点（Vue.set确保响应式）
          this.$set(nodeData, 'children', res.data)
          // 展开当前节点（可选）
          this.$nextTick(() => {
            node.expanded = true
          })
        }
        // 标记为已加载
        this.loadedKeys.add(nodeData.id)
      } finally {
        node.loading = false
      }
    },
    handleQuery() {
      if (!this.queryParams.level) {
        this.$message.warning('未传监测位置的节点code!');
        return
      }
      this.loading = true;
      getAlertManagePage(this.queryParams).then(res => {
        this.dataList = res.rows;
        this.dataList.forEach(item => {
          item.alertTypeShow = item.alertType == 1 ? '传感器预警' : '结构物预警'
          item.alertLevel = []
          if (item.openFlag) {
            if (item.oneFlag) {
              item.alertLevel.push('一级')
            }
            if (item.twoFlag) {
              item.alertLevel.push('二级')
            }
            if (item.threeFlag) {
              item.alertLevel.push('三级')
            }
            if (item.fourFlag) {
              item.alertLevel.push('四级')
            }
          }
        })
        this.total = res.total;
        this.loading = false;
      }).catch(err => {
        this.loading = false;
        console.error(err);
      });
    },
    resetQuery() {
      this.queryParams = {
        pageNum: 1,
        pageSize: 10,
        title: ''
      };
      this.handleQuery();
    },
    handleUpdate(row) {
      getAlertManageRuleParamsDetail({alertManageId: row.id}).then(res => {
        getValueTypePage({ code: row.monitorTypeCode }).then(res => {
          if (res.rows.length > 0) {
            this.unit = res.rows[0].unit
          }
        })
        this.editDialog = true
        this.formData = {
          ...row,
          ruleParameterList: res.data.map(item => {
            return {
              param: item.param
            }
          })
        }
        if(this.formData.alertInterval) this.formData.alertIntervalMin = parseInt(this.formData.alertInterval/60)
        if(this.formData.triggerInterval) this.formData.triggerIntervalMin = parseInt(this.formData.triggerInterval/60)

      })
    },
    changeRuleType(e) {
      if (!this.formData.alertRuleTypeId) return
      if (!e || !e.ruleName) {
        this.$nextTick(() => {
          this.$set(this, 'ruleData', this.$refs.aSelect.getCheckData())
        })
      } else {
        this.$set(this, 'ruleData', e)
      }
      this.$set(this.formData, 'ruleDescription', this.ruleData?.ruleDescription)
      this.formData.alertRuleType = this.ruleData.ruleName
      this.$nextTick(() => {
        const list = [];
        for (let i = 0; i < this.ruleData.alertRuleChainList.length; i++) {
          const item = this.ruleData.alertRuleChainList[i];
          this.$set(item, 'ruleDetailList', item.alertParameterDescription.split(","));
          for (let j = 0; j < item.ruleDetailList.length; j++) {
            list.push({ param: '' });
          }
        }
        if (!e || !e.ruleName) {
          this.$set(this.formData, 'ruleParameterList', list);
        }
      });
    },
    handleConfirm() {
      this.$refs.elForm.validate(valid => {
        if (valid) {
          this.loading = true;
          if(this.formData.alertIntervalMin) this.formData.alertInterval = parseInt(this.formData.alertIntervalMin*60)
          if(this.formData.triggerIntervalMin) this.formData.triggerInterval = parseInt(this.formData.triggerIntervalMin*60)
          if (this.formData.alertLevel) this.formData.alertLevel = this.formData.alertLevel.join(',')
          addThresholdInfo(this.formData).then(res => {
            this.$modal.msgSuccess('新增成功');
            this.editDialog = false;
            this.handleQuery();
          })
        }
      })
    },
    getGlobalIndex(outerIndex, innerIndex) {
      const data = this.ruleData.alertRuleChainList
      const arr = []
      // 提取所有的下标
      for (let i = 0; i < data.length; i++) {
        let ruleExpression = data[i].ruleExpression
        const matches = ruleExpression.match(/\$(\d+)/g); // 匹配所有 $数字
        let temp = matches.map(m => parseInt(m.replace('$', '')))
        arr.push(...temp)
      }
      // arr升序排序
      arr.sort((a, b) => a - b)
      let ruleExpression = data[outerIndex].ruleExpression
      const matches = ruleExpression.match(/\$(\d+)/g); // 匹配所有 $数字
      const num = matches.map(m => parseInt(m.replace('$', '')))[innerIndex]
      console.log(arr.indexOf(num))
      return arr.indexOf(num)
    },
    handleSelectionChange(e) {
      this.ids = e.map(item => item.id);
    },
    handleDelete() {
      if (this.ids.length <= 0) {
        this.$message.warning('请勾选需要删除的数据');
        return
      }
      this.$confirm('是否确认删除选中的数据？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.loading = true;
        deleteAlertManage({ids: this.ids}).then(res => {
          if (res.code === 200) {
            this.$message.success('删除成功');
            this.handleQuery();
          } else {
            this.$message.error(res.msg);
          }
          this.loading = false;
        }).catch(err => {
          this.loading = false;
          console.error(err);
        });
      })
    },
  }
};
</script>

<style scoped>
.leftDiv {
  border-right: 1px solid #d8dce5;
  min-height: calc(100vh - 100px);
  overflow-y: auto;
  height: calc(80vh - 150px);
  position: relative;
  top: -20px;
  padding-top: 10px;
  background-color: white;
}

.leftIcon {
  border: 1px solid #dcdfe6;
  border-radius: 8px;
  width: 16px;
  height: 50px;
  line-height: 50px;
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
  position: absolute;
  right: 0;
  top: 300px;
  z-index: 2;
}

.leftIcon:hover {
  background-color: #dcdfe6;
}

.rightIcon {
  border: 1px solid #dcdfe6;
  border-radius: 8px;
  width: 16px;
  height: 50px;
  line-height: 50px;
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
  position: absolute;
  left: -10px;
  top: 280px;
  z-index: 10;
  background: white;
}

.rightIcon:hover {
  background-color: #dcdfe6;
}
.tableDiv {
  margin-top: 20px;
}
.title {
  font-weight: bold;
}
</style>
<style lang="scss" scoped>
@import "@/assets/styles/business.scss";
</style>
