<template>
  <PageRow>
    <template slot="left">
      <el-input placeholder="输入关键字进行过滤" v-model="filterText" class="mb5" size="mini" clearable />
      <el-tree :data="layerMenuList" :props="{ children: 'child', label: 'name' }" :filter-node-method="filterNode"
        ref="tree" :expand-on-click-node="false" @node-click="handleTreeClick" highlight-current node-key="id"/>
    </template>
    <template slot="search">
      <el-row :gutter="20">
        <el-col :span="3" :offset="0">
          <el-input v-model="queryParams.layerSubName" placeholder="请输入图层名称" clearable size="mini"
            @keyup.enter.native="handleQuery" />
        </el-col>
        <el-col :span="3" :offset="0">
          <el-select v-model="queryParams.subStatType" placeholder="统计类型" clearable size="mini">
            <el-option label="count" :value="1" />
            <el-option label="sum" :value="2" />
          </el-select>
        </el-col>
        <el-col :span="6" :offset="0">
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">
            搜索
          </el-button>
          <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">
            重置
          </el-button>
        </el-col>
      </el-row>
    </template>
    <template slot="header">
      <el-row :gutter="20">
        <el-col :span="1.5" :offset="0" class="mb8">
          <el-button v-hasPermi="['baseData:culvert:add']" type="primary" size="mini" @click="handleAdd">
            新增
          </el-button>
        </el-col>
        <el-col :span="1.5" :offset="0" class="mb8">
          <el-button v-hasPermi="['baseData:culvert:edit']" type="primary" size="mini" @click="handleUpdate">
            编辑
          </el-button>
        </el-col>
        <el-col :span="1.5" :offset="0" class="mb8">
          <el-button v-hasPermi="['baseData:culvert:delete']" type="primary" size="mini" @click="handleDelete">
            删除
          </el-button>
        </el-col>
        <el-col :span="1.5" :offset="0" class="mb8">
          <el-button v-hasPermi="['baseData:culvert:getInfoById']" type="primary" size="mini" @click="onDetail">
            查看
          </el-button>
        </el-col>
      </el-row>
    </template>
    <template slot="body">
      <el-table v-adjust-table v-loading="loading" :data="menuSubList" @selection-change="handleSelectionChange" :row-style="rowStyle"
        @row-click="handleRowClick" :row-key="(row) => row.id" border height="100%" ref="tableRef" :tree-props="{children: 'children', hasChildren: 'hasChildren'}">
        <el-table-column type="selection" width="55" align="center" fixed="left" :reserve-selection="true" />
        <el-table-column label="图层名称" min-width="220" fixed="left" prop="name" />
        <el-table-column label="主键id" min-width="150" prop="id" show-overflow-tooltip/>
        <el-table-column label="图层数据表名" min-width="150" align="center" prop="tableInfo" show-overflow-tooltip />
        <el-table-column label="注记显示层级" min-width="110" align="center" prop="textShowLevel" />
        <el-table-column label="注记显示名称列" width="120" align="center" prop="textShowNameColumnInfo" show-overflow-tooltip/>
        <el-table-column label="空间坐标字段" width="110" align="center" prop="geomColumnInfo" show-overflow-tooltip />
        <el-table-column label="显示层级" width="140" align="center" prop="showLevel" />
        <el-table-column label="填充颜色" align="center" prop="interiorColour">
          <template slot-scope="{row}">
            <span style="width: 100%;height: 20px;display: block;" :style="{ background: row.interiorColour }"></span>
          </template>
        </el-table-column>
        <el-table-column label="边框颜色" align="center" prop="borderColour">
          <template slot-scope="{row}">
            <span style="width: 100%;height: 20px;display: block;" :style="{ background: row.borderColour }"></span>
          </template>
        </el-table-column>
        <el-table-column label="边框粗细" align="center" prop="borderWidth">
          <template slot-scope="{row}">
            {{ row.borderWidth }}
          </template>
        </el-table-column>
        <el-table-column label="是否符号化设置" width="120" align="center" prop="ifPersonalize">
          <template slot-scope="{row}">
            <el-link :type="row.ifPersonalize == 0 ? 'info' : 'success'" :underline="false">{{ row.ifPersonalize == 0 ?
              '否' : '是' }}</el-link>
          </template>
        </el-table-column>
        <el-table-column label="符号化列" width="110" align="center" prop="personalizeColumnInfo" show-overflow-tooltip />

        <el-table-column label="一张图是否显示" width="120" align="center" prop="oneMapShow">
          <template slot-scope="{row}">
            <el-link :type="row.oneMapShow == 0 ? 'info' : 'success'" :underline="false">{{ row.oneMapShow == 0 ? '不显示' :
              '显示' }}</el-link>
          </template>
        </el-table-column>
        <el-table-column label="排序" align="center" prop="showIndex" />
        <el-table-column label="操作" min-width="140" align="center" prop="iconId" fixed="right">
          <template slot-scope="{row}">
            <el-popover placement="right" width="50" trigger="click">
              <img :src="img" style="width: 100%; height: 100%;" v-for="(img, index) in imgUrlList" :key="index" />
              <el-link type="primary" :underline="false" slot="reference" @click.stop="imgPreview(row)">图层图标</el-link>
            </el-popover>
            <el-popover placement="bottom" width="60" trigger="click" class="ml5">
              <el-link type="primary" :underline="false" class="ml8 mb5" @click.stop="showDialog(row, 1)">
                <i class="el-icon-monitor el-icon--right mr10"></i>子分类管理
              </el-link>
              <el-link type="primary" :underline="false" class="ml8 mb5" @click.stop="showDialog(row, 2)">
                <i class="el-icon-view el-icon--right mr10"></i>显示字段设置
              </el-link>
              <el-link type="primary" :underline="false" class="ml8 mb5" @click.stop="showDialog(row, 3)">
                <i class="el-icon-search el-icon--right mr10"></i>查询项设置
              </el-link>
              <el-link type="primary" :underline="false" class="ml8" @click.stop="showDialog(row, 4)"
                :disabled="row.ifPersonalize == 0 ? true : false">
                <i class="el-icon-s-flag el-icon--right mr10"></i>个性化设置
              </el-link>
              <el-link type="primary" :underline="false" class="ml8" @click.stop="deleteCache(row)">
                <i class="el-icon-delete el-icon--right mr10"></i>清除切片缓存
              </el-link>
              <el-link type="primary" :underline="false" slot="reference" @click.stop="">更多操作</el-link>
            </el-popover>

          </template>
        </el-table-column>
      </el-table>

      <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize"
        @pagination="getList" />
    </template>

    <!-- 新增编辑查看 -->
    <Dialog :title="title" :show.sync="open">
      <AddOrEdit :form="form" @close="handleClose" @refresh="getList" :readonly="readonly" />
    </Dialog>

    <!-- 更多操作弹窗 -->
    <Dialog :title="dialogTitle" :show.sync="dialogVisible" width='90%'>
        <SubClassify />
    </Dialog>
  </PageRow>
</template>

<script>
import { getListPage, getMenuSub, delMenuSub, clearTileCache } from "@/api/oneMap/menuSub";
import { getListTree } from "@/api/oneMap/layerMenu";
import AddOrEdit from "./components/addOrEdit.vue";
import Dialog from "@/components/Dialog/index.vue";
import PageRow from "@/components/PageContainer/row.vue";
import SubClassify from '@/views/system/mapManage/subClassify/index.vue';
import { findFiles } from '@/api/file/index.js'
import { publicRequest } from "@/api/oneMap/tableInfo";

export default {
  name: "MenuSub",
  components: {
    AddOrEdit,
    Dialog,
    PageRow,
    SubClassify,
  },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 图层目录数据配置表格数据
      menuSubList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        layerMenuId: null,
        layerName: null,
        layerTableName: null,
        description: null,
        shapeType: null,
        textShowLevel: null,
        interiorColour: null,
        borderColour: null,
        borderWidth: null,
        dataType: null,
        ifPersonalize: null,
        personalizeGroupType: null,
        showIndex: null,
        personalizeColumn: null,
        textShowNameColumn: null,
        layerShowLevel: null,
        statisticComponentId: null,
        iconId: null,
        oneMapShow: null
      },
      // 表单参数
      form: {},
      tableSelects: [],
      readonly: false,
      layerMenuList: [],
      filterText: '',
      imgUrlList: [],
      dialogVisible: false,
      dialogTitle: '',
    };
  },
  async created() {
    await this.getLayerMenuTree();
    this.getList();
  },
  watch: {
    filterText(val) {
      this.$refs.tree.filter(val);
    }
  },
  methods: {
    /** 查询图层目录数据配置列表 */
    getList() {
      this.loading = true;
      getListPage(this.queryParams).then(response => {
        this.menuSubList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 获取目录树
    async getLayerMenuTree() {
      await getListTree().then(res => {
        if (res) {
          this.layerMenuList = res || [];
          this.$nextTick(() => {
            this.$refs.tree.setCurrentNode(res[0])
          })
          this.queryParams.layerMenuId = res ? res[0].id : '' || ''
        }
      })
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        layerMenuId: null,
        layerName: null,
        layerTableName: null,
        description: null,
        shapeType: null,
        textShowLevel: null,
        interiorColour: null,
        borderColour: null,
        borderWidth: null,
        dataType: null,
        ifPersonalize: null,
        personalizeGroupType: null,
        showIndex: null,
        personalizeColumn: null,
        textShowNameColumn: null,
        layerShowLevel: null,
        statisticComponentId: null,
        iconId: null,
        oneMapShow: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.queryParams.layerMenuId = null;
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.tableSelects = selection || []
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    // 勾选高亮
    rowStyle({ row, rowIndex }) {
      if (this.ids.includes(row.id)) {
        return { 'background-color': '#b7daff', color: '#333' }
      } else {
        return { 'background-color': '#fff', color: '#333' }
      }
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.readonly = false;
      this.title = "添加图层目录数据配置";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      if(this.tableSelects.length !== 1) {
        this.$modal.msgWarning("请选择一条数据编辑");
        return;
      }
      this.reset();
      let id = this.tableSelects[0].id
      this.$modal.loading();
      getMenuSub(id).then(response => {
        this.form = response.data;
        this.form.layerMenuId = [this.form.layerMenuId]
        this.open = true;
        this.readonly = false;
        this.title = "修改图层目录数据配置";
      }).finally(() => {
        this.$modal.closeLoading();
      });
    },
    onDetail() {
      if(this.tableSelects.length !== 1) {
        this.$modal.msgWarning("请选择一条数据查看");
        return;
      }
      this.reset();
      let id = this.tableSelects[0]?.id
      this.readonly = true;
      this.$modal.loading();
      getMenuSub(id).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "查看图层目录数据配置";
      }).finally(() => {
        this.$modal.closeLoading();
      });
    },
    handleClose(e) {
      this.readonly = false;
      this.open = e;
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      if(!this.tableSelects.length) {
        this.$modal.msgWarning("至少选择一条数据删除");
        return;
      }
      let ids = this.tableSelects.map(v => v.id)
      this.$modal.confirm('是否确认删除选中的图层目录数据配置数据项？').then(function () {
        return delMenuSub(ids);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => { });
    },
    handleRowClick(row) {
      row.isSelected = !row.isSelected
      this.$refs.tableRef.toggleRowSelection(row)
    },
    // 过滤树
    filterNode(value, data) {
      if (!value) return true;
      return data.menuName.indexOf(value) !== -1;
    },
    // 点击树
    handleTreeClick(node) {
      this.queryParams.layerMenuId = node.id || ''
      this.tableSelects = [];
      this.$refs.tableRef.clearSelection()
      this.getList();
    },
    // 查看图片
    imgPreview(row) {
      if (!row.iconId) {
        this.$modal.msgWarning("暂无数据");
        return;
      }
      this.$modal.loading();
      findFiles({ ownerId: row.iconId }).then(res => {
        if (res.code === 200) {
          this.imgUrlList = res.data.map(file => file.url);
        }
      }).finally(() => {
        this.$modal.closeLoading();
      })
    },
    // 显示下级操作 type: 1,子类管理、2、显示字段，3、查询项
    showDialog(row, type) {
      switch (type) {
        case 1:
          this.dialogTitle = '子分类管理';
          this.$router.push({ path: '/system/onemap/subClassify', query: { id: row.id } })
          break;
        case 2:
          this.dialogTitle = '显示字段设置';
          this.$router.push({ path: '/system/onemap/menuSubField', query: { id: row.id } })
          break;
        case 3:
          this.dialogTitle = '查询项设置';
          this.$router.push({ path: '/system/onemap/menuSubQuery', query: { id: row.id } })
          break;
        case 4:
          this.dialogTitle = '个性化设置';
          this.$router.push({ path: '/system/onemap/menuSubSet', query: { id: row.id } })
          break;
        default:
          break;
      }

      // this.dialogVisible = true;
    },
    deleteCache(row) {
      // this.$modal.confirm("确认清除缓存？").then(() => {
      //   clearTileCache({id: row.id}).then(res => {
      //     if (res.code === 200) {
      //       this.$modal.msgSuccess("清除成功")
      //     }
      //   })
      // })


      this.$modal
        .confirm("确认清除缓存？")
        .then(() => {
          let url = "/oneMap/common/server/callAllService";
          publicRequest(url, "post", {
            interfacePath: "/menuSub/clearTileCache",
            p: {id: row.id}
          })
            .then((res) => {
              if (res.code === 200) {
                this.$modal.msgSuccess("清除成功");
              }
            })
            .catch(() => {
              this.$modal.msgError("清除失败");
            });
        })
        .catch(() => {});
    }
  }
};
</script>
<style lang="scss" scoped></style>
