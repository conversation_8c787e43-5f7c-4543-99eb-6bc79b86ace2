<template>
  <div v-loading="loading" class="road-interflow-edit" style="padding: 20px">
    <el-row :gutter="20">
      <el-form
          ref="elForm"
          :model="formData"
          :rules="rules"
          :disabled="readOnly"
          label-width="130px"
          size="medium"
      >
        <el-col :span="24">
          <el-form-item label="管理处" prop="domainId">
            <select-tree
                v-model="formData.domainId"
                placeholder="请选择管养单位"
                :deptType="100"
                :deptTypeList="[3]"
                clearable
                :disabled="type == 4"
                only-select-child
            ></select-tree>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="申请事由" prop="applyReason">
            <el-input :disabled="type == 4" type="textarea" :rows="4" v-model="formData.applyReason" placeholder="请输入申请事由" style="width: 100%"/>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="预约时间段" prop="reservationTimeArr">
            <el-date-picker
                :disabled="type == 4"
                v-model="formData.reservationTimeArr"
                type="datetimerange"
                placeholder="选择预约时间段"
                range-separator="至"
                start-placeholder="发始时间"
                end-placeholder="截止时间"
                value-format="yyyy-MM-dd HH:mm:ss"
                style="width: 100%"
            />
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="办理人" prop="handleNo">
            <el-input :disabled="type == 4" v-model="formData.handleNo" placeholder="请输入办理人" style="width: 100%"/>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="授权附件" prop="fileId">
            <file-upload key="fileId" v-model="formData.fileId" :owner-id="formData.fileId" :for-view="readOnly || type == 4"></file-upload>
          </el-form-item>
        </el-col>
        <template v-if="type >= 3 || type == 0">
          <el-col :span="24">
            <el-form-item :disabled="type == 4" label="领出时间" prop="takeOutTime">
              <el-date-picker
                :disabled="type == 4"
                v-model="formData.takeOutTime"
                type="datetime"
                placeholder="选择领出时间"
                value-format="yyyy-MM-dd HH:mm:ss"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="领出人" prop="takeOutNo">
              <el-input :disabled="type == 4" v-model="formData.takeOutNo" placeholder="请输入领出人" style="width: 100%"/>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="领出附件" prop="takeOutFile">
              <file-upload key="takeOutFile" v-model="formData.takeOutFile" :owner-id="formData.takeOutFile" :for-view="readOnly || type == 4"></file-upload>
            </el-form-item>
          </el-col>
        </template>
        <template v-if="type >=4 || type == 0">
          <el-col :span="24">
            <el-form-item label="办理附件" prop="handleFile">
              <file-upload key="handleFile" v-model="formData.handleFile" :owner-id="formData.handleFile" :for-view="readOnly"></file-upload>
            </el-form-item>
          </el-col>
        </template>
        <template v-if="type >= 5 || type == 0">
          <el-col :span="24">
            <el-form-item label="归还时间" prop="returnTime">
              <el-date-picker
                v-model="formData.returnTime"
                type="datetime"
                placeholder="选择归还时间"
                value-format="yyyy-MM-dd HH:mm:ss"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="归还人" prop="returnNo">
              <el-input v-model="formData.returnNo" placeholder="请输入归还人" style="width: 100%"/>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="归还附件" prop="returnFile">
              <file-upload key="returnFile" v-model="formData.returnFile" :owner-id="formData.returnFile" :for-view="readOnly"></file-upload>
            </el-form-item>
          </el-col>
        </template>
        <el-col :span="24" v-if="type >= 2">
          <el-form-item label="审核意见" prop="remark">
            <el-input v-model="formData.remark" type="textarea" :rows="4" placeholder="请输入审核意见" style="width: 100%"/>
          </el-form-item>
        </el-col>
      </el-form>
      <el-col :span="24">

        <div class="mb10" style="display: flex;justify-content: space-between;">
          <div>
            <el-input style="width: 230px;margin-right: 20px;" v-model="filterText" placeholder="用电户号" />
            <el-select v-if="type >= 4 || type == 0" v-model="filterType" placeholder="是否上传附件" clearable>
              <el-option label="否" value="0"></el-option>
              <el-option label="是" value="1"></el-option>
            </el-select>
          </div>
          <el-button v-if="!readOnly" type="primary" @click="addDetailDialog = true">新增</el-button>
        </div>
        <el-table v-adjust-table
            :data="filterData"
            border
            height="500px"
            ref="tableRef"
            style="width: 100%">
          <el-table-column label="序号" type="index" width="50" align="center"/>
          <el-table-column label="户号" width="200" prop="houseNum">
          </el-table-column>
          <el-table-column label="名称" width="200" prop="houseName">
          </el-table-column>
          <el-table-column label="办理内容" prop="handleContent">
            <template slot-scope="scope">
              <el-input v-model="scope.row.handleContent" placeholder="请输入办理内容" style="width: 100%"/>
            </template>
          </el-table-column>
          <el-table-column label="附件" width="100" prop="fileId" v-if="type >= 4 || type == 0">
            <template slot-scope="scope">
              <el-button type="text" @click="handleOpenFile(scope.row)">{{ type == 0 ? '查看' : '上传附件' }}</el-button>
            </template>
          </el-table-column>
          <el-table-column
              align="center"
              v-if="!readOnly"
              class-name="small-padding fixed-width"
              fixed="right"
              label="操作"
              width="70"
          >
            <template v-if="!readOnly" slot-scope="scope">
              <el-button type="text" @click="handleRemove(scope.row)">移除</el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-col>
      <el-col :span="24" v-if="!readOnly">
        <div style="text-align: right;margin-right: 30px" class="mt20">
          <el-button v-if="type >= 4" type="primary" @click="handleSaveTemp">暂存</el-button>
          <el-button type="primary" @click="handleSave">{{ btnText }}</el-button>
          <el-button v-if="type >= 2" v-has-menu-permi="['operate:application:reject']" type="danger" @click="handleReject">驳回</el-button>
        </div>
      </el-col>
    </el-row>
    <el-dialog :visible.sync="addDetailDialog" v-if="addDetailDialog" title="添加明细" width="90%" append-to-body modal-append-to-body>
      <el-tabs>
        <el-tab-pane label="隧道电费">
          <TunnelElectricity :domain-id="formData.domainId" ref="tunnel" page-type="view" style="height: 70vh" @check="handleCheck"></TunnelElectricity>
        </el-tab-pane>
        <el-tab-pane label="三大系统电费">
          <ThreeElectricity :domain-id="formData.domainId" ref="three" page-type="view" style="height: 70vh" @check="handleCheck"></ThreeElectricity>
        </el-tab-pane>
      </el-tabs>
    </el-dialog>
    <el-dialog :visible.sync="openFile" v-if="openFile" destroy-on-close append-to-body modal-append-to-body title="附件列表" width="90%">
      <file-upload v-model="fileId" :owner-id="fileId" :for-view="readOnly"></file-upload>
      <div style="text-align: right">
        <el-button type="primary" @click="saveFile">确定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import ThreeElectricity from './component/threeElectricity'
import TunnelElectricity from './component/tunnelElectricity'
import {
  addApplication,
  getApplicationDetail, rejectApplication, submitApplication,
  updateApplication
} from "@/api/calculate/operationManageFee/fillApplication";
import moment from "moment";
import {v4 as uuidv4} from "uuid";
import SelectTree from "@/components/DeptTmpl/selectTree.vue";
export default {
  components: {SelectTree, ThreeElectricity, TunnelElectricity },
  props: {
    row: {
      type: Object,
      default: {}
    },
    readOnly: {
      type: Boolean,
      default: false
    },
    type: {
      type: Number,
      default: 1
    }
  },
  watch: {
    row: {
      handler: function (val) {
        if (val.id) {
          // 查询明细
          this.loading = true
          getApplicationDetail(val.id).then(res => {
            this.formData = res.data
            this.formData.taskId = val.taskId
            this.detailList = res.data?.detailList || []
            // this.detailList.forEach(item => {
            //   if (!item.fileId) item.fileId = uuidv4().replace(/-/g, '').slice(0, 20)
            // })
            if (this.type == 3 && !this.formData.takeOutTime) {
              this.formData.takeOutTime = moment().format('YYYY-MM-DD HH:mm:ss')
            }
            if (this.type == 5 && !this.formData.returnTime) {
              this.formData.returnTime = moment().format('YYYY-MM-DD HH:mm:ss')
            }
            // if (!this.formData.fileId) this.formData.fileId = uuidv4().replace(/-/g, '').slice(0, 20)
            if (!this.formData.takeOutFile) this.formData.takeOutFile = uuidv4().replace(/-/g, '').slice(0, 20)
            if (!this.formData.returnFile) this.formData.returnFile = uuidv4().replace(/-/g, '').slice(0, 20)
            if (!this.formData.handleFile) this.formData.handleFile = uuidv4().replace(/-/g, '').slice(0, 20)
            if (this.formData.reservationTime && this.formData.reservationTimeEnd ) {
              this.$set(this.formData, 'reservationTimeArr', [this.formData.reservationTime, this.formData.reservationTimeEnd])
            }
            this.loading = false
          })
        }
      },
      immediate: true,
      deep: true
    }
  },
  computed: {
    filterData: function () {
      return this.detailList.filter(item => {
        const houseNumMatch = item.houseNum.indexOf(this.filterText) > -1;
        let fileidMatch = true
        if (this.filterType) fileidMatch = this.filterType == '1' ? item.fileId : !item.fileId;
        return houseNumMatch && fileidMatch;
      });
      // return this.detailList.filter(item => {
      //   return item.houseNum.indexOf(this.filterText) > -1
      // })
    }
  },
  data() {
    return {
      loading: false,
      formData: {},
      detailList: [],
      filterText: '',
      filterType: null,
      rules: {
        domainId: [
          { required: true, message: '请输入管理处', trigger: 'blur' },
        ],
        applyReason: [
          { required: true, message: '请输入申请事由', trigger: 'blur' },
        ],
        reservationTimeArr: [
          { required: true, message: '请选择预约时间', trigger: 'change' }
        ],
        handleNo: [
          { required: true, message: '请输入办理人', trigger: 'blur' },
        ],
        fileId: [
          { required: true, message: '请选择授权附件', trigger: 'change' }
        ],
        takeOutTime: [
          { required: true, message: '请选择领出时间', trigger: 'change' }
        ],
        takeOutNo: [
          { required: true, message: '请输入领出人', trigger: 'blur' },
        ],
        returnTime: [
          { required: true, message: '请选择归还时间', trigger: 'change' }
        ],
        returnNo: [
          { required: true, message: '请输入归还人', trigger: 'blur' },
        ],
      },
      addDetailDialog: false,
      openFile: false,
      fileId: '',
      checkRow: {},
      btnText: '保存'
    }
  },
  mounted() {
    switch (this.type) {
      case 1:
        this.btnText = '保存'
        break
      case 2:
        this.btnText = '审核'
        break
      case 3:
      case 4:
      case 5:
        this.btnText = '登记'
        break
    }
    // if (!this.formData.fileId) this.formData.fileId = uuidv4().replace(/-/g, '').slice(0, 20)
  },
  methods: {
    handleSave() {
      this.$refs.elForm.validate(valid => {
        if (!valid) return
        if (this.formData.fileId && Array.isArray(this.formData.fileId) && this.formData.fileId.length > 0) {
          this.formData.fileId = this.formData.fileId[0]
        } else if (Array.isArray(this.formData.fileId) &&
          this.formData.fileId.length == 0){
          this.formData.fileId = null
        }
        if (this.formData.takeOutFile && Array.isArray(this.formData.takeOutFile) && this.formData.takeOutFile.length > 0) {
          this.formData.takeOutFile = this.formData.takeOutFile[0]
        } else if (Array.isArray(this.formData.takeOutFile) &&
          this.formData.takeOutFile.length == 0) {
          this.formData.takeOutFile = null
        }
        if (this.formData.handleFile && Array.isArray(this.formData.handleFile) && this.formData.handleFile.length > 0) {
          this.formData.handleFile = this.formData.handleFile[0]
        } else if (Array.isArray(this.formData.handleFile) &&
          this.formData.handleFile.length == 0) {
          this.formData.handleFile = null
        }
        if (this.formData.returnFile && Array.isArray(this.formData.returnFile) && this.formData.returnFile.length > 0) {
          this.formData.returnFile = this.formData.returnFile[0]
        } else if (Array.isArray(this.formData.returnFile) &&
          this.formData.returnFile.length == 0) {
          this.formData.returnFile = null
        }
        this.formData.detailList = this.detailList || []
        for (let i = 0; i < this.formData.detailList.length; i++) {
          let item = this.formData.detailList[i]
          if (item.fileId && Array.isArray(item.fileId) && item.fileId.length > 0) {
            item.fileId = item.fileId[0]
          }
        }
        this.formData.reservationTime = this.formData.reservationTimeArr[0]
        this.formData.reservationTimeEnd = this.formData.reservationTimeArr[1]
        if (this.formData.id) {
          if (this.type >= 2) {
            submitApplication(this.formData).then(res => {
              if (res.code == 200) {
                this.$message.success('提交成功')
                this.$emit('close')
              }
            })
          } else {
            updateApplication(this.formData).then(res => {
              if (res.code == 200) {
                this.$message.success('修改成功')
                this.$emit('close')
              }
            })
          }
        } else {
          addApplication(this.formData).then(res => {
            if (res.code == 200) {
              this.$message.success('添加成功')
              this.$emit('close')
            }
          })
        }
      })
    },
    // 暂存
    handleSaveTemp() {
      this.$refs.elForm.validate(valid => {
        if (!valid) return
        if (this.formData.fileId && Array.isArray(this.formData.fileId) && this.formData.fileId.length > 0) {
          this.formData.fileId = this.formData.fileId[0]
        } else if (Array.isArray(this.formData.fileId) &&
          this.formData.fileId.length == 0){
          this.formData.fileId = null
        }
        if (this.formData.takeOutFile && Array.isArray(this.formData.takeOutFile) && this.formData.takeOutFile.length > 0) {
          this.formData.takeOutFile = this.formData.takeOutFile[0]
        } else if (Array.isArray(this.formData.takeOutFile) &&
          this.formData.takeOutFile.length == 0) {
          this.formData.takeOutFile = null
        }
        if (this.formData.handleFile && Array.isArray(this.formData.handleFile) && this.formData.handleFile.length > 0) {
          this.formData.handleFile = this.formData.handleFile[0]
        } else if (Array.isArray(this.formData.handleFile) &&
          this.formData.handleFile.length == 0) {
          this.formData.handleFile = null
        }
        if (this.formData.returnFile && Array.isArray(this.formData.returnFile) && this.formData.returnFile.length > 0) {
          this.formData.returnFile = this.formData.returnFile[0]
        } else if (Array.isArray(this.formData.returnFile) &&
          this.formData.returnFile.length == 0) {
          this.formData.returnFile = null
        }
        this.formData.detailList = this.detailList || []
        for (let i = 0; i < this.formData.detailList.length; i++) {
          let item = this.formData.detailList[i]
          if (item.fileId && Array.isArray(item.fileId) && item.fileId.length > 0) {
            item.fileId = item.fileId[0]
          }
        }
        this.formData.reservationTime = this.formData.reservationTimeArr[0]
        this.formData.reservationTimeEnd = this.formData.reservationTimeArr[1]
        updateApplication(this.formData).then(res => {
          if (res.code == 200) {
            this.$message.success('保存成功')
            this.$emit('close')
          }
        })
      })
    },
    handleReject() {
      rejectApplication(this.formData).then(res => {
        if (res.code == 200) {
          this.$message.success('驳回成功')
          this.$emit('close')
        }
      })
    },
    handleCheck() {
      const tunnelData = this.$refs.tunnel.selectDatas
      const three = this.$refs.three.selectDatas
      this.detailList.push(
          ...tunnelData.concat(three).map(item => {
            return {
              id: item.id,
              houseNum: item.houseNum,
              houseName: item.houseName,
              handleContent: ''
            }
          })
      )
      // id 去重
      this.detailList = this.detailList.filter((item, index, self) => {
        return self.findIndex(v => v.id === item.id) === index
      })
      this.addDetailDialog = false
    },
    handleRemove(row) {
      this.detailList = this.detailList.filter(item => item.id !== row.id);
    },
    handleOpenFile(row) {
      this.checkRow = row
      this.fileId = row.fileId ? row.fileId : uuidv4().replace(/-/g, '').slice(0, 20)
      this.openFile = true
    },
    saveFile() {
      const row = this.detailList.find(item => item.id === this.checkRow.id)
      row.fileId = this.fileId
      this.openFile = false
    }
  }
}
</script>

<style scoped lang="scss">

</style>
<style lang="scss" scoped>
@import "@/assets/styles/business.scss";
</style>
