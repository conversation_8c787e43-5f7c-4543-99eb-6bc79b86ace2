<template>
  <div class="app-container maindiv">
    <el-row :gutter="20" style="display: flex">
      <!--部门数据-->
      <maintenance-tree
          @rowClick="handleNodeClick"
          :realNav="realNav"
          @closeNav="realNav = false"
      ></maintenance-tree>
      <!--角色数据-->
      <el-col :span="realNav ? 19 : 24" :xs="24">
        <!--展开图标-->
        <div class="rightIcon" @click="realNav = true" v-show="!realNav">
          <span class="el-icon-caret-right"></span>
        </div>
        <el-row>
          <el-col :span="24" :xs="24">
            <el-row>
              <el-form
                  ref="queryForm"
                  :model="queryParams"
                  size="mini"
                  :inline="true"
                  label-width="68px"
              >
                <el-form-item label="">
                  <el-input style="width: 240px" v-model="queryParams.calcCode" placeholder="计量单编码"/>
                </el-form-item>
                <el-form-item label="">
                  <el-input style="width: 240px" v-model="queryParams.calcName" placeholder="计量单名称"/>
                </el-form-item>
                <el-form-item>
                  <el-date-picker
                      v-model="queryParams.calcDate"
                      style="width: 240px"
                      type="daterange"
                      value-format="yyyy-MM-dd"
                      range-separator="至"
                      start-placeholder="计量时间"
                      end-placeholder="计量时间">
                  </el-date-picker>
                </el-form-item>
                <el-form-item>
                  <el-button
                      type="primary"
                      icon="el-icon-search"
                      size="mini"
                      @click="handleQuery">搜索
                  </el-button>
                  <el-button
                      icon="el-icon-refresh"
                      size="mini"
                      @click="resetQuery">重置
                  </el-button>
                </el-form-item>
              </el-form>
              <!--默认折叠-->
            </el-row>
          </el-col>
        </el-row>
        <!--操作按钮区开始-->
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button
                type="primary"
                icon="el-icon-plus"
                size="mini"
                v-if="type == 1"
                v-has-menu-permi="['operate:contractOtherFundCalc:add']"
                @click="handleAdd"
            >新增
            </el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button
                type="success"
                icon="el-icon-view"
                size="mini"
                @click="handleOpenOperate"
            >操作意见
            </el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button
                type="warning"
                icon="el-icon-download"
                size="mini"
                v-has-menu-permi="['operate:contractOtherFundCalc:export', 'contract:contractOtherFundCalc:exportView']"
                @click="exportList"
            >导出清单
            </el-button>
          </el-col>
          <right-toolbar
              :showSearch.sync="showSearch"
              @queryTable="handleQuery"
              :columns="columns"
          ></right-toolbar>
        </el-row>
        <el-row>
          <div class="draggable">
            <el-table v-adjust-table
                size="mini"
                style="width: 100%"
                v-loading="loading"
                border
                :data="tableData"
                row-key="id"
                ref="dataTable"
                stripe
                highlight-current-row
                @row-click="handleClickRow"
                :height="
                showSearch ? 'calc(100vh - 330px)' : 'calc(100vh - 270px)'
              "
            >
              <el-table-column
                  label="序号"
                  align="center"
                  type="index"
                  width="50"
              />
              <template v-for="(column, index) in columns">
                <el-table-column
                    :label="column.label"
                    v-if="column.visible"
                    align="center"
                    :prop="column.field"
                    :width="column.width"
                >
                  <template slot-scope="scope">
                    <dict-tag
                        v-if="column.dict"
                        :options="dict.type[column.dict]"
                        :value="scope.row[column.field]"
                    />
                    <template v-else-if="column.slots">
                      <RenderDom
                          :row="scope.row"
                          :index="index"
                          :render="column.render"
                      />
                    </template>
                    <span v-else-if="column.isTime">{{
                        parseTime(scope.row[column.field], "{y}-{m}-{d}")
                      }}</span>
                    <span v-else>{{ scope.row[column.field] }}</span>
                  </template>
                </el-table-column>
              </template>
              <el-table-column
                  label="操作"
                  fixed="right"
                  align="center"
                  width="250"
                  class-name="small-padding fixed-width"
              >
                <template slot-scope="scope">
                  <el-button
                      type="text"
                      icon="el-icon-view"
                      @click="showInfo(scope.row)">查看明细
                  </el-button>
                  <el-button
                      type="text"
                      icon="el-icon-edit"
                      v-if="type == 1"
                      v-has-menu-permi="['operate:contractOtherFundCalc:edit']"
                      @click="handleEdit(scope.row)">编辑
                  </el-button>
                  <el-button
                      type="text"
                      icon="el-icon-delete"
                      v-if="type == 1"
                      v-has-menu-permi="['operate:contractOtherFundCalc:delete']"
                      @click="handleDelete(scope.row)">删除
                  </el-button>
                  <el-button
                      type="text"
                      icon="el-icon-check"
                      v-if="type == 1"
                      v-has-menu-permi="['operate:contractOtherFundCalc:submit']"
                      @click="handleSubmit(scope.row)">提交
                  </el-button>
                  <el-button
                      type="text"
                      icon="el-icon-check"
                      v-if="type == 2"
                      v-has-menu-permi="['operate:contractOtherFundCalc:submit']"
                      @click="handleExamine(scope.row)">审核
                  </el-button>
                  <el-button
                      type="text"
                      icon="el-icon-close"
                      v-if="type == 2"
                      v-has-menu-permi="['operate:contractOtherFundCalc:rejec']"
                      @click="handleReject(scope.row)">撤回
                  </el-button>
                </template>
              </el-table-column>
            </el-table>
            <pagination
                v-show="total > 0"
                :total="total"
                :page.sync="queryParams.pageNum"
                :limit.sync="queryParams.pageSize"
                @pagination="handleQuery"
            />
          </div>
        </el-row>
      </el-col>
    </el-row>
    <el-dialog
        :title="detailTitle"
        append-to-body
        modal-append-to-body
        :visible.sync="detailDialog"
        width="90%"
        v-if="detailDialog"
    >
      <detail :row-data="row" @close="handleClose"/>
    </el-dialog>
    <el-dialog
        title="明细查看"
        append-to-body
        modal-append-to-body
        :visible.sync="infoDialog"
        width="90%"
        v-if="infoDialog"
    >
      <el-table v-adjust-table
          size="mini"
          style="width: 100%"
          v-loading="loading"
          border
          :data="detailData"
          row-key="id"
          ref="detailTable"
          stripe
          highlight-current-row
          height="500px"
      >
        <el-table-column
            label="序号"
            align="center"
            type="index"
            width="50"
        />
        <template v-for="(column, index) in detailColumns">
          <el-table-column
              :label="column.label"
              v-if="column.visible"
              align="center"
              :prop="column.field"
          >
            <template slot-scope="scope">
              <dict-tag
                  v-if="column.dict"
                  :options="dict.type[column.dict]"
                  :value="scope.row[column.field]"
              />
              <template v-else-if="column.slots">
                <RenderDom
                    :row="scope.row"
                    :index="index"
                    :render="column.render"
                />
              </template>
              <span v-else-if="column.isTime">{{
                  parseTime(scope.row[column.field], "{y}-{m}-{d}")
                }}</span>
              <span v-else>{{ scope.row[column.field] }}</span>
            </template>
          </el-table-column>
        </template>
        <el-table-column
            v-if="type == 1"
            label="操作"
            fixed="right"
            align="center"
            width="50"
            class-name="small-padding fixed-width"
        >
          <el-button type="text">删除</el-button>
        </el-table-column>
      </el-table>
    </el-dialog>
    <el-dialog title="附件列表" destroy-on-close :visible.sync="openFile" width="90%">
      <file-upload v-model="filePath" :forView="true"></file-upload>
    </el-dialog>
    <el-dialog
      title="操作意见"
      :visible.sync="openOperateInfo"
      width="90%"
      destroy-on-close
      v-if="openOperateInfo"
    >
      <operateInfo
        @close="openOperateInfo = false"
        :businessKey="clickRow.id"
        :getNodeInfo="getNodeInfo"
      ></operateInfo>
    </el-dialog>
  </div>
</template>

<script>
import MaintenanceTree from "@/components/MaintenanceTree/index.vue";
import {
  deleteContractOtherFundCalc, getContractOtherFundCalcDetail,
  listContractOtherFundCalc, rejectContractOtherFundCalc,
  submitContractOtherFundCalc, viewContractOtherFundCalcList, getNodeInfo
} from "@/api/calculate/contractCalc/contractCalc";
import Detail from "./detail.vue"
import {getDicts} from "@/api/system/dict/data";
import operateInfo from "@/views/dailyMaintenance/component/operateInfo.vue";

export default {
  name: 'ViewCalc',
  components: {
    operateInfo,
    Detail,
    MaintenanceTree,
    RenderDom: {
      functional: true,
      props: {
        row: Object,
        index: Number,
        render: Function,
      },
      render(createElement, ctx) {
        const {row, index} = ctx.props;
        return ctx.props.render(row, index);
      },
    },
  },
  props: [],
  dicts: ['maintenance_type', 'affiliation_project_type'],
  data() {
    return {
      // 左侧组织树
      realNav: true,
      queryParams: {
        pageNum: 1,
        pageSize: 50,
      },
      total: 0,
      tableData: [],
      columns: [
        {key: 0, width: 100, field: 'stageName', label: `状态`, visible: true},
        {key: 1, width: 100, field: 'numberName', label: `期数`, visible: true},
        {key: 2, width: 100, field: 'calcName', label: `项目名称`, visible: true},
        {key: 3, width: 100, field: 'calcCode', label: `计量单编号`, visible: true},
        {key: 4, width: 100, field: 'domainName', label: `管养单位`, visible: true},
        {key: 5, width: 100, field: 'conDomainName', label: `申请计量单位`, visible: true},
        {key: 6, width: 100, field: 'maiSecId', label: `路段名称`, visible: true},
        {key: 7, width: 100, field: 'sumCost', label: `计量费用`, visible: true},
        {key: 8, width: 100, field: 'preCalcName', label: `上一期名称`, visible: true},
        {key: 9, width: 100, field: 'preCalcCode', label: `上一期编号`, visible: true},
        {key: 10, width: 100, field: 'calcDate', label: `计量日期`, visible: true},
        {key: 11, width: 100, field: 'createTime', label: `操作日期`, visible: true},
        {key: 12, width: 100, field: 'createuser', label: `操作人`, visible: true},
        {key: 13, width: 100, field: 'remark', label: `备注`, visible: true},
        {key: 14, width: 100, field: 'fileId', label: `附件`, visible: true, slots: true, render: (row, index) => {
            return (
              <el-button
                size="mini"
                disabled={!row.fileId}
                type="text"
                onClick={(e) => this.handleOpenFile(e, row)}
              >
                查看
              </el-button>
            );
          }
        },
      ],
      detailData: [],
      detailColumns: [
        {key: 0, width: 100, field: 'mtype', label: `养护类型`, visible: true, dict: 'maintenance_type'},
        {key: 1, width: 100, field: 'projType', label: `所属工程类型`, visible: true, dict: 'affiliation_project_type'},
        {key: 2, width: 100, field: 'fundType', label: `费用类型`, visible: true},
        {key: 3, width: 100, field: 'schemeCode', label: `子目号`, visible: true},
        {key: 4, width: 100, field: 'schemeName', label: `子目名称`, visible: true},
        {key: 5, width: 100, field: 'price', label: `单价`, visible: true},
        {key: 6, width: 100, field: 'unit', label: `单位`, visible: true},
        {key: 7, width: 100, field: 'schemeNum', label: `数量`, visible: true},
        {key: 8, width: 100, field: 'schemeFund', label: `金额`, visible: true},
        {key: 9, width: 100, field: 'remark', label: `备注`, visible: true},
        {key: 10, width: 100, field: 'updateuser', label: `操作人`, visible: true},
        {key: 11, width: 100, field: 'createTime', label: `操作时间`, visible: true},
      ],
      loading: false,
      showSearch: false,
      clickRow: {},
      row: {},
      detailTitle: '新增',
      detailDialog: false,
      infoDialog: false,
      type: 1,
      openFile: false,
      filePath: '',
      openOperateInfo: false,
    };
  },
  computed: {},
  watch: {},
  created() {
    this.type = Number(this.$route.query.type)
    this.handleQuery()
  },
  mounted() {
  },
  methods: {
    getNodeInfo,
    handleNodeClick(e) {
      this.queryParams.domainId = e.domainId || "";
      this.queryParams.maiSecId = e.maiSecId || "";
      this.handleQuery();
    },
    handleQuery() {
      this.loading = true
      if (this.type) {
        this.queryParams.stage = this.type
        listContractOtherFundCalc(this.queryParams).then(response => {
          this.loading = false
          this.tableData = response.rows
          this.total = response.total
        });
      } else {
        viewContractOtherFundCalcList(this.queryParams).then(response => {
          this.loading = false
          this.tableData = response.rows
          this.total = response.total
        });
      }
    },
    resetQuery() {
      this.queryParams = {
        pageNum: 1,
        pageSize: 50,
      };
      this.handleQuery()
    },
    handleAdd() {
      this.detailTitle = '新增'
      this.row = {}
      this.detailDialog = true
    },
    handleEdit(row) {
      this.detailTitle = '编辑'
      this.row = row
      this.detailDialog = true
    },
    handleDelete(row) {
      this.$confirm('确定删除吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        deleteContractOtherFundCalc(row.id).then(res => {
          this.$message.success('删除成功')
          this.handleQuery()
        })
      })
    },
    showInfo(row) {
      this.infoDialog = true
      this.loading = true
      getContractOtherFundCalcDetail(row.id).then(res => {
        this.detailData = res.data?.detailList || []
        this.detailData.forEach(item => {
          let fundDict = ''
          switch (item.mtype) {
            case 1:
              fundDict = 'cost_name'
              break
            case 2:
              fundDict = 'project_type'
              break
            case 3:
              fundDict = 'theft_m_type'
              break
            case 4:
              fundDict = 'test_project_type'
              break
          }
          getDicts(fundDict).then(res => {
            if (res.code === 200) {
              const fund = res.data.find(dict => dict.dictValue == item.fundType)
              if (fund) item.fundType = fund.dictLabel
            }
          })
        })
        this.loading = false
      })
    },
    handleSubmit(row) {
      this.$prompt('', '提交', {
        confirmButtonText: '提交',
        cancelButtonText: '取消',
        inputPlaceholder: '请输入',
        inputType: 'textarea'
      }).then(({value}) => {
        row.remark = value
        submitContractOtherFundCalc(row).then(res => {
          this.$message.success('提交成功')
          this.handleQuery()
        })
      })
    },
    handleExamine(row) {
      this.$confirm('确定审核吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        submitContractOtherFundCalc(row).then(res => {
          this.$message.success('审核成功')
          this.handleQuery()
        })
      })
    },
    handleOpenOperate() {
      if (!this.clickRow.id) {
        this.$modal.msgError("请选择一条数据");
        return;
      }
      this.openOperateInfo = true;
    },
    handleReject(row) {
      this.$confirm('确定撤回吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        rejectContractOtherFundCalc(row).then(res => {
          this.$message.success('撤回成功')
          this.handleQuery()
        })
      })
    },
    handleClose() {
      this.detailDialog = false
      this.infoDialog = false
      this.handleQuery()
    },
    handleClickRow(e) {
      this.clickRow = e
    },
    handleOpenFile(e, row) {
      this.filePath = ''
      this.openFile = true
      this.filePath = row.fileId
    },
    exportList() {
      if (this.type) this.queryParams.stage = this.type
      this.download(
          "manager/contractOtherFundCalcDetail/export",
          {...this.queryParams},
          `contractOtherFundCalcDetail_${new Date().getTime()}.xlsx`,
          {
            headers: {"Content-Type": "application/json;"},
            parameterType: "body",
          }
      );
    },
  },
};
</script>

<style lang="scss" scoped>
.rightIcon {
  border: 1px solid #dcdfe6;
  border-radius: 8px;
  width: 16px;
  height: 50px;
  line-height: 50px;
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
  position: absolute;
  left: -10px;
  top: 280px;
  z-index: 10;
  background: white;
}

.rightIcon:hover {
  background-color: #dcdfe6;
}

.left-total {
  font-size: 13px;
  line-height: 28px;
  color: #606266;
  position: absolute;
  bottom: 10px;
  right: 10px;
}
</style>
<style lang="scss" scoped>
@import "@/assets/styles/business.scss";
</style>
