<template>
  <div v-loading="loading" class="route-road">
    <el-select
      filterable
      v-model="selectValue"
      style="width: 100%"
      clearable
      v-bind="$attrs"
      :disabled="disabled"
      @change="changeSelect"
    >
      <el-option
        v-for="(item,index) in routeOptions"
        :key="index"
        :label="item.showLabel"
        :value="item.baseRouteCode"
      />
    </el-select>
  </div>
</template>

<script>
import {listByMaintenanceSectionId} from "@/api/baseData/common/routeLine";
import {formatPile } from "@/utils/ruoyi";
import {listAllRoute} from "@/api/system/route";
import {listAllRouteSegments} from "@/api/system/routeSegments";
export default {
  props: {
    value: {
      type: [String,Number],
      default: ''
    },
    maintenanceSectionId: {
      type: [Number, String],
      default: null,
    },
    routeType: {
      type: String,
      default: ''
    },
    disabled: {
      type: Boolean,
      default: false
    },
    maintenanceUnitId: {
      type: [Number, String],
      default: null,
    }
  },
  data() {
    return {
      routeOptions: [], // 路段数据
      loading: false,
      isFirstLoad: true
    };
  },
  computed: {
    selectValue: {
      get: function() {
        return this.value
      },
      set: function(val) {
        this.$emit('input', val)
        this.$emit('change', val)
      }
    }
  },
  watch: {
    maintenanceSectionId(val) {
      if (val) {
        if (!this.isFirstLoad) {
          this.$emit('input', '')
        }
        this.getOptions()
        this.isFirstLoad = false;
      }
    }
  },
  created() {
    this.getOptions();
  },
  methods: {
    getOptions() {
      listAllRouteSegments({maintenanceSectionId: this.maintenanceSectionId, roadType: this.routeType, merge: true}).then((res) => {
        if (res.code == 200) {
          let routeList = res.data || [];
          routeList = routeList.filter(item => item != null && item.baseRouteCode)
          this.routeOptions = routeList
          this.routeOptions.forEach(item => {
            item.showLabel = `${item.baseRouteCode}/${formatPile(item.pileStart) || ''}~${formatPile(item.pileEnd || '')}(${this.getRouteType(item.roadType)})`
          })
        }
      });
    },
    getRouteType(type) {
      return type == '0' ? '主线' : type == '1' ? '连接线' : '匝道'
    },
    changeSelect(e) {
      this.$emit('update:value',e)
    },
    getRouteInfo() {
      return this.routeOptions.find(item => item.baseRouteCode == this.value)
    }
  },
};
</script>

<style lang="scss" scoped></style>
