<template>
  <div class="app-container">

    <!-- 查询 -->
    <div class="searchBox" :style="{ 'height': queryShow ? '86px' : '48px' }">
      <el-row :gutter="12">
        <el-col :span="4">
          <el-input v-model="queryParams.key" placeholder="请输入流程ID"></el-input>
        </el-col>
        <el-col :span="4">
          <el-input v-model="queryParams.name" placeholder="请输入流程名称"></el-input>
        </el-col>
        <el-col :span="4">
          <el-select v-model="queryParams.category" placeholder="请选择流程分类" style="width: 100%;" clearable>
            <el-option v-for="dict in dict.type.process_type" :key="dict.value" :label="dict.label" :value="dict.label" />
          </el-select>
        </el-col>

        <el-col :span="4">
          <el-select v-model="queryParams.suspendState" placeholder="请选择状态" style="width: 100%;" clearable>
            <el-option  label="已激活" value="1" />
            <el-option  label="未激活" value="2" />
          </el-select>
        </el-col>
        <el-col :span="4" style="display: flex; align-items: center">

          <el-button type="primary" icon="el-icon-search" size="mini" @click="queryList">搜索</el-button>
          <el-button icon="el-icon-refresh" size="mini" @click="">重置</el-button>
          <!-- <el-button
            :icon="queryShow ? 'el-icon-arrow-up' : 'el-icon-arrow-down'"
            style="color: #1890ff;border-color: #badeff;background-color: #e8f4ff;"
            circle
            @click="queryShow = !queryShow"
          /> -->
        </el-col>
      </el-row>
      <transition name="search">
        <div class="searchMoreBox" v-if="queryShow">
          <el-row :gutter="20" style="width: 639px;">
            <el-col :span="8" :offset="0">
              <el-select v-model="queryParams.disasterType" placeholder="请选择灾害类型" clearable>
                <el-option v-for="dict in dict.type.disaster_risk_type" :key="dict.value" :label="dict.label"
                  :value="dict.label" />
              </el-select>
            </el-col>
            <el-col :span="8" :offset="0">
              <el-input v-model="queryParams.disasterNum" placeholder="请输入灾害编号" clearable @keyup.enter.native="queryhandle" />
            </el-col>
            <el-col :span="8" :offset="0">
              <el-input v-model="queryParams.disasterName" placeholder="请输入灾害名称" clearable
                @keyup.enter.native="queryhandle" />
            </el-col>
          </el-row>
          <el-row :gutter="20" style="width: 416.45px; min-width: 210px; margin-left: 10px">
            <el-col :span="24" :offset="0">
              <el-date-picker
                v-model="queryTime"
                @change="() => {queryParams.startTime = queryTime[0] + ' 00:00:00'; queryParams.endTime = queryTime[1] + ' 23:59:59'}"
                type="daterange"
                style="width: 100%;"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                format="yyyy-MM-dd"
                value-format="yyyy-MM-dd"
              >
              </el-date-picker>
            </el-col>
          </el-row>
        </div>
      </transition>
    </div>

    <!-- 主表数据 -->
    <div class="tableDiv" :style="{ 'height': queryShow ? 'calc(100% - 96px)' : 'calc(100% - 58px)' }">
      <!-- 功能按钮 -->
      <div class="btnBox">
        <el-row :gutter="10">
          <el-col :span="1.5">
            <!-- <el-button type="primary" icon="el-icon-plus" size="mini" @click="formInit('add')">新增
            </el-button> -->
            <el-dropdown trigger="click" size="medium" @command="formDialogShow">
              <el-button type="primary" icon="el-icon-plus" size="mini">
                新增<i class="el-icon-arrow-down el-icon--right"></i>
              </el-button>
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item v-for="dict in dict.type.process_type" :command="dict.label" icon="el-icon-tickets">{{ dict.label }}</el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
          </el-col>
          <el-col :span="1.5">
            <el-button type="success" icon="el-icon-edit" size="mini" :disabled="tableSelection.length !== 1"
              @click="formEdit('select')">修改
            </el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="danger" icon="el-icon-delete" size="mini" :disabled="tableSelection.length === 0"
              @click="formDelete('batch')">删除
            </el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="primary" icon="el-icon-document-add" size="mini"@click="handleImport">导入</el-button>
          </el-col>
          <!-- <el-col :span="1.5">
            <el-button type="warning" icon="el-icon-download" size="mini" @click="formExport">导出</el-button>
          </el-col> -->
        </el-row>
      </div>

      <!-- 数据表格 -->
      <el-table v-adjust-table size="mini" height="calc(100% - 98px)" border ref="tableRef"
        v-loading="tableLoading" :data="tableData" @selection-change="tableSelectionChange" @row-click="tableRowClick">
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="序号" fixed align="center" type="index" width="50"></el-table-column>
        <el-table-column label="所属分类" fixed :show-overflow-tooltip="true" align="center" prop="category" />
        <el-table-column label="流程名称" fixed :show-overflow-tooltip="true" align="center" prop="name" />
        <el-table-column label="流程KEY" fixed :show-overflow-tooltip="true" align="center" prop="key" />
        <el-table-column label="流程ID" fixed :show-overflow-tooltip="true" align="center" prop="id" />
        <el-table-column label="部署ID" fixed :show-overflow-tooltip="true" align="center" prop="deploymentId" />
        <el-table-column label="流程版本" fixed :show-overflow-tooltip="true" align="center" prop="version" />
        <el-table-column label="流程实例状态" fixed :show-overflow-tooltip="true" align="center" prop="suspendStateName">
          <template slot-scope="scope">
            <el-tag :type="scope.row.suspendStateName == '已激活' ? 'success' : 'warning'">{{scope.row.suspendStateName}}</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center"  width="300" class-name="small-padding fixed-width" fixed="right">
          <template slot-scope="scope">
            <el-button
              size="mini"
              type="text"
              :icon="scope.row.suspendStateName == '已激活' ? 'el-icon-video-pause' : 'el-icon-video-play'"
              @click="bpmnStatusChange(scope.row)"
            >
              {{ scope.row.suspendStateName == '已激活' ? '挂起' : '激活' }}
            </el-button>
            <el-button size="mini" type="text" icon="el-icon-edit" @click="formEdit('click', scope.row)">修改
            </el-button>
            <el-button size="mini" type="text" icon="el-icon-setting" @click="fromConfig( scope.row)">配置
            </el-button>
            <el-button size="mini" type="text" icon="el-icon-delete" @click="formDelete('single', scope.row)">删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <pagination :total="queryTotal" :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize" @pagination="queryList" style="margin-right: 10px;" />

    </div>

    <!-- 表单 -->
    <el-dialog :visible.sync="formDialog" width="70%" max-height="50%" :close-on-press-escape="false" :close-on-click-modal="false" append-to-body class="formDialog">
      <template slot="title">
        <div class="titleBox">
          <div class="title">{{ formType === 'add' ? '新建流程' : '编辑流程' }}</div>
          <div class="subTitle" v-if="formParams.id">ID：</div>
          <div class="typeBox" v-if="formType === 'edit'">
            所属分类：
            <el-select v-model="bpmnType" placeholder="请选择流程分类">
              <el-option v-for="dict in dict.type.process_type" :key="dict.value" :label="dict.label" :value="dict.label" />
            </el-select>
          </div>
        </div>
      </template>

      <div class="formMain" v-if="formDialog" v-loading="bpmnLoading">
        <div class="mainCanvas" ref="canvas"></div>
        <div class="mainPanel" id="js-properties-panel"></div>
      </div>

      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="formSave" :loading="formBtnLoading">保 存</el-button>
        <el-button type="success" @click="bpmnDownload">另存为bpmn文件</el-button>
      </div>
    </el-dialog>

    <el-dialog :visible.sync="userDialog" :title="userDialogTitle" width="40%" :close-on-press-escape="false" :close-on-click-modal="false" append-to-body class="bpmnDialog">
      <div class="bpmnGroundMain" v-if="userType !== 'candidateGroups'">
        <div class="searchBox">
          <el-row>
            <el-col :span="20">
              <el-input v-model="userParams.nickName" placeholder="请输入用户昵称" clearable @keyup.enter.native="userList"></el-input>
            </el-col>
            <el-col :span="4" style="display: flex; justify-content: flex-end;">
              <el-button type="primary" icon="el-icon-search" size="mini" @click="userList">搜索</el-button>
            </el-col>
          </el-row>
        </div>
        <div class="dataBox">
          <el-table size="mini" height="calc(100% - 50px)" border ref="tableRef" v-loading="userTableLoading" :data="userTableData">
            <el-table-column label="用户名称" fixed :show-overflow-tooltip="true" align="center" prop="userName" />
            <el-table-column label="用户昵称" fixed :show-overflow-tooltip="true" align="center" prop="nickName" />
            <el-table-column label="用户编号" fixed :show-overflow-tooltip="true" align="center" prop="userId" />
            <el-table-column label="操作" align="center" class-name="small-padding fixed-width" fixed="right">
              <template slot-scope="scope">
                <el-button size="mini" type="text" @click="userSelect(scope.row)">选择
                </el-button>
              </template>
            </el-table-column>
          </el-table>

          <pagination :total="userTableTotal" :page.sync="userParams.pageNum" :pager-count="2"
            :limit.sync="userParams.pageSize" @pagination="userList" style="margin-right: 10px;" />
        </div>
        <div class="selectBox">
          <!-- <div class="selectTitle">
            已选择
          </div> -->
          <el-tag
            v-if="userType === 'assignee' && userSingleData.userId"
            :key="userSingleData.userId"
            @close="userSelctCancel('single')"
            closable
          >
            {{userSingleData.nickName}}
          </el-tag>
          <el-tag
            v-if="userType === 'candidateUser' && userGrounpData.length > 0"
            v-for="item in userGrounpData"
            :key="item.userId"
            style="margin: 5px"
            @close="userSelctCancel('group', item)"
            closable
          >
            {{item.nickName}}
          </el-tag>
        </div>
      </div>
      <div class="bpmnGroundMain" v-else>
        <div class="searchBox">
          <el-row>
            <el-col :span="20">
              <el-input v-model="roleParams.roleName" placeholder="请输入角色名称" clearable @keyup.enter.native="roleList"></el-input>
            </el-col>
            <el-col :span="4" style="display: flex; justify-content: flex-end;">
              <el-button type="primary" icon="el-icon-search" size="mini" @click="roleList">搜索</el-button>
            </el-col>
          </el-row>
        </div>
        <div class="dataBox">
          <el-table size="mini" height="calc(100% - 50px)" border ref="tableRef" v-loading="roleTableLoading" :data="roleTableData">
            <el-table-column label="角色名称" fixed :show-overflow-tooltip="true" align="center" prop="roleName" />
            <el-table-column label="角色编码" fixed :show-overflow-tooltip="true" align="center" prop="roleKey" />
            <el-table-column label="角色ID" fixed :show-overflow-tooltip="true" align="center" prop="roleId" />
            <el-table-column label="操作" align="center" class-name="small-padding fixed-width" fixed="right">
              <template slot-scope="scope">
                <el-button size="mini" type="text" @click="roleSelect(scope.row)">选择
                </el-button>
              </template>
            </el-table-column>
          </el-table>

          <pagination :total="roleTableTotal" :page.sync="roleParams.pageNum" :pager-count="2"
            :limit.sync="roleParams.pageSize" @pagination="roleList" style="margin-right: 10px;" />
        </div>
        <div class="selectBox">
          <!-- <div class="selectTitle">
            已选择
          </div> -->
          <el-tag
            v-for="item in roleGrounpData"
            :key="item.roleKey"
            style="margin: 5px"
            @close="roleSelctCancel(item)"
            closable
          >
            {{item.roleName}}
          </el-tag>
        </div>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="bpmnGrounpSave">保 存</el-button>
      </div>
    </el-dialog>

    <!-- 导入对话框 -->
    <el-dialog :title="upload.title" :visible.sync="upload.open" width="400px" :close-on-press-escape="false" :close-on-click-modal="false" append-to-body>
      <el-upload
        ref="upload"
        :limit="1"
        accept=".xml, .bpmn"
        :headers="upload.headers"
        :action="upload.url"
        :disabled="upload.isUploading"
        :on-progress="handleFileUploadProgress"
        :on-success="handleFileSuccess"
        :auto-upload="false"
        drag
      >
        <i class="el-icon-upload"></i>
        <div class="el-upload__text">
          将文件拖到此处，或
          <em>点击上传</em>
        </div>
        <div class="el-upload__tip" slot="tip">
          <!-- <el-checkbox v-model="upload.updateSupport" />是否更新已经存在的灾害数据 -->
          <!-- <el-link type="primary" style="font-size:12px" @click="importTemplate">下载模板</el-link> -->
        </div>
        <div class="el-upload__tip" style="color:red" slot="tip">提示：仅允许导入“xml”或“bpmn”格式文件！</div>
        </el-upload>
        <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitFileForm">确 定</el-button>
        <!-- <el-button @click="upload.open = false">取 消</el-button> -->
      </div>
    </el-dialog>


    <el-dialog title="节点配置" :visible.sync="showConfig" width="40%" :close-on-press-escape="false" :close-on-click-modal="false" append-to-body>
      <div style="min-height: 40vh">
        <el-form >
          <el-form-item :label="taskExt.taskName"  label-width="200px" v-for="taskExt in taskExtList" >
            <el-input  v-model="taskExt.url" placeholder="请输入跳转地址"></el-input>
          </el-form-item>
        </el-form>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="onConfigSubmit">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
// -------------------- 引入 --------------------

// 流程图相关
import BpmnModeler from 'bpmn-js/lib/Modeler' // 流程图核心
import propertiesPanelModule from 'bpmn-js-properties-panel' // 右侧工具栏
// import propertiesProviderModule from 'bpmn-js-properties-panel/lib/provider/camunda' // 左侧工具栏
// import camundaModdleDescriptor from 'camunda-bpmn-moddle/resources/camunda' // 扩展属性
import customPropertiesProviderModule from './modeler/components/provider/custom' // 自定义属性
import customModdleDescriptor from './modeler/components/descriptors/custom.json' // 自定义描述文件
import newDiagram from './mock/newDiagram.bpmn' // 空白模板
// 以下为bpmn工作流绘图工具的样式
import 'bpmn-js/dist/assets/diagram-js.css'
import 'bpmn-js/dist/assets/bpmn-font/css/bpmn.css'
import 'bpmn-js/dist/assets/bpmn-font/css/bpmn-codes.css'
import 'bpmn-js/dist/assets/bpmn-font/css/bpmn-embedded.css'
import 'bpmn-js-properties-panel/dist/assets/bpmn-js-properties-panel.css'
// 汉化
import customTranslate from './modeler/translations/customTranslate'
// 校验
import lintModule from 'bpmn-js-bpmnlint'; // 校验依赖
import 'bpmn-js-bpmnlint/dist/assets/css/bpmn-js-bpmnlint.css' // 校验样式
import bpmnlintConfig from './modeler/lint/.bpmnlintrc'; // 校验规则

// 导入相关
import { getToken } from "@/utils/auth";

// API
import { queryPageProcess, queryProcessChart, createProcess, deleteProcess, updateProcessStatus } from '@/api/process/manage/manage' // 流程模块管理
import { listUser } from "@/api/system/user"; // 用户管理模块
// import { listPost } from "@/api/system/post"; // 岗位管理模块
import { listRole } from "@/api/system/role"; // 角色管理模块


import {listTaskExt, saveTaskExt} from  "@/api/process/tastExt/taskExt"

export default {
  name: "Manage",
  // 数据字典
  dicts: [
    'process_type', // 流程分类
  ],
  // 组件
  components: {},

  // -------------------- 变量 --------------------
  data() {
    return {

      /**
       * 查询相关
       */
      queryShow: false, // 查询更多参数显隐
      queryParams: { // 查询参数
        suspendState: undefined,
        name: '', // 流程名称
        key: '', // 流程ID
        category: '', // 流程分类
        pageNum: 1, // 页码
        pageSize: 10, // 每页条数
      },
      queryTotal: 0, // 查询总数

      /**
       * 表格相关
       */
      tableData: [], // 表格数据
      tableSelection: [], // 表格选中数据
      tableLoading: false, // 表格加载

      /**
       * 表单相关
       */
      formDialog: false, // 表单弹窗
      formParams: { // 表单参数
        id: '', // 流程ID
      },
      formType: '', // 表单类型
      formBtnLoading: false, // 表单按钮加载

      /**
       * 流程图相关
       */
      // bpmn建模器
      bpmnModeler: null, // 流程图核心
      bpmnTranslateModule: null, // 流程图汉化模块
      bpmnLoading: false, // 流程图加载
      bpmnType: '', // 流程分类

      /**
       * 用户相关
       */
      userDialog: false, // 用户dialog
      userDialogTitle: '', // 流程图用户/组dialog标题
      userType: '', // 用户选择弹窗类型
      userElement: null, // 用户元素
      userParams: { // 用户查询参数
        pageNum: 1,
        pageSize: 10,
        nickName: '',
        userId: '',
        userName: '',
        phonenumber: '',
        status: '',
        deptId: ''
      },
      userTableData: [], // 用户数据
      userTableTotal: 0, // 用户数据总数
      userTableLoading: false, // 用户数据loading
      userSingleData: '', // 用户单选数据
      userGrounpData: [], // 用户多选数据

      /**
       * 角色相关
       */
       roleParams: { // 角色查询参数
        pageNum: 1,
        pageSize: 10,
        roleName: '',
      },
      roleTableData: [], // 角色数据
      roleTableTotal: 0, // 角色数据总数
      roleTableLoading: false, // 角色数据loading
      roleGrounpData: [], // 角色多选数据

      /**
       * 导入相关
       */
      // 用户导入参数
      upload: {
        // 是否显示弹出层（灾害数据导入）
        open: false,
        // 弹出层标题（灾害数据导入）
        title: "",
        // 是否禁用上传
        isUploading: false,
        // 是否更新已经存在的灾害数据
        // updateSupport: 0,
        // 设置上传的请求头部
        headers: { Authorization: "Bearer " + getToken() },
        // 上传的地址
        url: process.env.VUE_APP_BASE_API + "/workFlow/process-definition/uploadStreamAndDeployment"
      },

      showConfig: false,
      taskExtList: []

    }
  },
  mounted() {
    this.initPage()
  },
  // -------------------- 方法 --------------------
  methods: {

    /**
     * 页面相关
     */
    // 初始化页面
    initPage() {
      this.queryList()
      // 弹窗方法全局设置
      window.bpmnAssigneeDialog = this.bpmnAssigneeDialog
      window.bpmnCandidateUsersDialog = this.bpmnCandidateUsersDialog
      window.bpmnCandidateGroupsDialog = this.bpmnCandidateGroupsDialog
    },

    /**
     * 查询相关
     */
    // 查询数据
    queryList() {
      this.tableLoading = true
      console.log('提交参数：', this.queryParams)
      queryPageProcess(this.queryParams).then(res => {
        console.log('流程数据：', res)
        if (res.code === 200) {
          this.tableData = res.rows
          this.queryTotal = res.total
        }
        this.tableLoading = false
      }).catch(() => {
        this.tableLoading = false
      })
    },

    // 查询流程图数据
    queryBpmnChart(item) {
      return new Promise((resolve, reject) => {
        this.bpmnLoading = true
        let vo = {
          deploymentId: item.deploymentId,
          resourceName: item.resourceName
        }
        queryProcessChart(vo).then((res) => {
          console.log('流程图数据：', res)
          resolve(res)
          this.bpmnLoading = false
        }).catch(() => {
          this.bpmnLoading = false
          reject('加载流程图失败')
        })
      })
    },

    // 查询重置
    queryReset() {

    },

    /**
     * 表格相关
     */
    // 勾选表格项改变时
    tableSelectionChange(val) {
      this.tableSelection = val
    },

    // 点击一行时
    tableRowClick(val) {
      this.$refs.tableRef.toggleRowSelection(val)
    },

    /**
     * 表单相关
     */

    // 新增表单
    formDialogShow(val) {
      this.bpmnType = val
      this.formInit('add')
    },

    // 初始化表单
    formInit(type, data) {
      this.formType = type
      let bpmnData = null
      switch (type) {
        case 'add':
          bpmnData = newDiagram
          this.formDialog = true
          this.$nextTick(() => {
            this.bpmnInit(bpmnData)
          })
          break;

        case 'edit':
          this.formDialog = true
          this.queryBpmnChart(data).then((res) => {
            bpmnData = res
            this.$nextTick(() => {
              this.bpmnInit(bpmnData)
            })
          }).catch((err) => {
            bpmnData = newDiagram
            this.$message.warning(err)
            this.$nextTick(() => {
              this.bpmnInit(bpmnData)
            })
          })
          break;
      }

    },

    // 表单修改
    formEdit(type, item) {
      switch (type) {
        case 'click':
          this.formInit('edit', item)
          break;

        case 'select':
          let val = this.tableSelection[0]
          this.formInit('edit', val)
          break;
      }
    },

    // 表单删除
    formDelete(type, item) {
      let deleteId = ''
      switch (type) {
        case 'batch':
          let ids = []
          this.tableSelection.forEach((row) => {
            ids.push(row.deploymentId)
          })
          deleteId = ids.join(',')
          break;
        case 'single':
          deleteId = item.deploymentId
          break;
      }
      this.$modal
      .confirm('确认删除？')
      .then(async () => {
        this.tableLoading = true
        console.log('删除参数：', deleteId)
        await deleteProcess(deleteId).then(res => {
          if (res && res.code == '200') {
            this.queryList()
            this.$message.success('操作成功！')
          }
          this.tableLoading = false
        }).catch((err) => {
          this.tableLoading = false
        })
      })
    },

    // 表单保存
    async formSave() {
      // 流程分类赋值
      let definitions = this.bpmnModeler.getDefinitions();
      definitions.set("targetNamespace", this.bpmnType);
      await this.bpmnModeler._setDefinitions(definitions)
      // 开始加载
      this.formBtnLoading = true

      const { xml } = await this.bpmnModeler.saveXML({ format: true });
      // let vo = {
      //   stringBPMN: xml
      // }
      createProcess(xml).then((res) => {
        console.log('部署结果：', res)
        if(res.code === 200) {
          this.$message.success('操作成功')
          this.queryList()
          this.formDialog = false
        }
        this.formBtnLoading = false
      }).catch(() => {
        this.formBtnLoading = false
      })
    },

    /**
     * 流程图相关
     */
    // 流程图初始化
    bpmnInit(data) {
      if(!data) return
      this.bpmnTranslateModule = {
        translate: ['value', customTranslate]
      }
      // 获取到属性ref为“canvas”的dom节点
      const canvas = this.$refs.canvas
      // 建模
      console.log('模块初始化4，获取到的元素：', canvas)
      console.log('模块初始化4，bpmn依赖：', BpmnModeler)
      console.log('模块初始化4，bpmn右侧面板：', propertiesPanelModule)
      this.bpmnModeler = new BpmnModeler({
        container: canvas,
        // 添加校验
        linting: {
          active: true, // 开启校验
          bpmnlint: bpmnlintConfig // 校验规则
        },
        //添加控制板
        propertiesPanel: {
          parent: '#js-properties-panel'
        },
        additionalModules: [
          lintModule, // 校验模块
          propertiesPanelModule,
          // propertiesProviderModule,
          customPropertiesProviderModule,
          this.bpmnTranslateModule
        ],
        moddleExtensions: {
          //如果要在属性面板中维护camunda：XXX属性，则需要此
          // camunda: camundaModdleDescriptor,
          custom: customModdleDescriptor
        }
      })
      console.log('加载流程图：', data)
      this.bpmnModeler.importXML(data).then(() => {
        // 流程分类赋值
        let definitions = this.bpmnModeler.getDefinitions();
        console.log('流程基础信息：', definitions)
        switch (this.formType) {
          case 'add':
            definitions.set("targetNamespace", this.bpmnType);
            this.bpmnModeler._setDefinitions(definitions)
            break;

          case 'edit':
            this.bpmnType = definitions.targetNamespace
            break;
        }

      })
    },

    // 流程用户单选
    bpmnAssigneeDialog(e) {
      this.userType = 'assignee'
      this.bpmnDialog(e)
    },

    // 流程用户多选
    bpmnCandidateUsersDialog(e) {
      this.userType = 'candidateUser'
      this.bpmnDialog(e)
    },

    // 流程组多选
    bpmnCandidateGroupsDialog(e) {
      this.userType = 'candidateGroups'
      this.bpmnDialog(e)
    },

    // 流程图选择用户/组
    bpmnDialog(e) {
      // 获取元素
      this.userElement = e
      // 初始化变量
      this.userSingleData = {
        userId: '',
        nickName: ''
      }
      this.userGrounpData = []
      this.roleGrounpData = []
      // 获取输入框内的值
      let input = $(this.userElement).prev().children().eq(0);
      let val = $(input).val()
      if(val) {
        switch (this.userType) {
          case 'assignee':
            this.userDialogTitle = '代理用户选择'
            this.userSingleData.nickName = val.split('@')[0]
            this.userSingleData.userId = val.split('@')[1]
            // let vo = {
            //   pageNum: 1,
            //   pageSize: 10,
            //   userId: this.userSingleData.userId
            // }
            // listUser(vo).then((res) => {
            //   console.log('获取的用户信息：', res)
            //   if(res.code === 200) {
            //     this.userSingleData.nickName = res.rows[0].nickName
            //   } else {
            //     this.userSingleData.nickName = '代理人'
            //   }
            // }).catch(() => {
            //   this.userSingleData.nickName = '代理人'
            // })
            break;

          case 'candidateUser':
            this.userDialogTitle = '代理用户组选择'
            let userNames = val.split(',')
            this.userGrounpData = userNames.map((item, index) => {
              return {
                userId: item.split('@')[1],
                nickName: item.split('@')[0]
              }
            })
            // group.forEach((item) => {
            //   let vo = {
            //     pageNum: 1,
            //     pageSize: 10,
            //     userId: item
            //   }
            //   listUser(vo).then((res) => {
            //     console.log('获取的用户信息：', res)
            //     if(res.code === 200) {
            //       this.userGrounpData.push({
            //         userId: res.rows[0].userId,
            //         nickName: res.rows[0].nickName
            //       })
            //     } else {
            //       this.userGrounpData.push({
            //         userId: item,
            //         nickName: '代理人'
            //       })
            //     }
            //   }).catch(() => {
            //     this.userGrounpData.push({
            //       userId: item,
            //       nickName: '代理人'
            //     })
            //   })
            // })
            break;

          case 'candidateGroups':
            this.userDialogTitle = '代理组选择'
            let roleNames = val.split(','); // 使用自定义属性的 roleName 列表
            this.roleGrounpData = roleNames.map((item, index) => {
              return {
                roleKey: item.split('@')[1],
                roleName: item.split('@')[0]
              }
            })
            // postGroup.forEach((item) => {
            //   let vo = {
            //     pageNum: 1,
            //     pageSize: 10,
            //     roleKey: item
            //   }
            //   listRole(vo).then((res) => {
            //     console.log('获取的角色信息：', res)
            //     if(res.code === 200) {
            //       this.roleGrounpData.push({
            //         roleKey: res.rows[0].roleKey,
            //         roleName: res.rows[0].roleName
            //       })
            //     } else {
            //       this.userGrounpData.push({
            //         roleKey: item,
            //         roleName: '角色'
            //       })
            //     }
            //   }).catch(() => {
            //     this.userGrounpData.push({
            //       roleKey: item,
            //       roleName: '角色'
            //     })
            //   })
            // })
            break;
        }
      }
      if(this.userType !== 'candidateGroups') {
        // 用户/组选择初始化
        this.userParams = {
          pageNum: 1,
          pageSize: 10,
          nickName: ''
        }
        // 查询用户数据
        this.userList()
      } else {
        // 重置查询参数
        this.roleParams = {
          pageNum: 1,
          pageSize: 10,
          roleName: ''
        }
        // 查询用户数据
        this.roleList()
      }
      this.userDialog = true
    },

    // 流程图选择用户/组后保存
    async bpmnGrounpSave() {
      let input = $(this.userElement).prev().children().eq(0);
      let inputValue = '';      // 用于显示在输入框中的用户名称

      switch (this.userType) {
        case 'assignee':
          // 设置单个用户的名称和 ID
          inputValue = this.userSingleData.nickName + "@" + this.userSingleData.userId;   // 显示用户名称
          break;

        case 'candidateUser':
          let userNames = [];
          await this.userGrounpData.forEach((item) => {
            userNames.push(item.nickName + "@" + item.userId);
          })
          inputValue = userNames.join(',');            // 显示用户名称列表
          break;

        case 'candidateGroups':
          let roleNames = [];
          await this.roleGrounpData.forEach((item) => {
            roleNames.push(item.roleName + "@" + item.roleKey);
          })
          inputValue = roleNames.join(',');            // 显示角色名称列表
          break;
      }

      // 修改输入框的显示值（用户名称）
      $(input).val(inputValue);

      //上面只是单纯修改当前输入框文本，但是重新加载后会发现输入框还是恢复成原来的文本
      //因为只是单纯的修改了，并没有修改绑定的业务数据信息，当重新加载的时候读到的还是原来的文本信息
      //创建输入框修改事件
      let changeEvent = document.createEvent ("HTMLEvents");
      changeEvent.initEvent ("change", true, true);
      //触发修改事件，触发绑定的事件，更新数据
      $(input)[0].dispatchEvent (changeEvent);
      this.userDialog = false
    },

    // 流程图下载
    async bpmnDownload() {
      let { xml } = await this.bpmnModeler.saveXML({ format: true })
      let encodedData = encodeURIComponent(xml)
      const link = document.createElement('a')
      link.href = 'data:application/bpmn20-xml;charset=UTF-8,' + encodedData
      link.setAttribute('download', '流程图模板' + new Date().getTime() + '.bpmn')
      link.style.display = 'none'
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
    },

    // 流程激活/挂起
    bpmnStatusChange(item) {
      console.log('流程激活/挂起：', item)
      this.$modal.loading('正在切换状态，请稍候...')
      let vo = {
        id: item.id,
        suspendState: item.suspendState
      }
      updateProcessStatus(vo).then((res) => {
        console.log('流程状态：', res)
        if(res.code === 200) {
          this.$message.success('状态切换成功')
          this.queryList()
        }
        this.$modal.closeLoading()
      }).catch(() => {
        this.$modal.closeLoading()
      })
    },

    /**
     * 用户相关
     */
    // 查询用户信息
    userList() {
      this.userTableLoading = true
      listUser(this.userParams).then((res) => {
        console.log('获取的用户信息：', res)
        if(res.code === 200) {
          this.userTableData = res.rows
          this.userTableTotal = res.total
        }
        this.userTableLoading = false
      }).catch(() => {
        this.userTableLoading = false
      })
    },

    // 用户/组选择
    userSelect(item) {
      console.log('单个用户信息：', item)
      switch (this.userType) {
        case 'assignee':
          this.userSingleData.userId = item.userId
          this.userSingleData.nickName = item.nickName
          break;

        case 'candidateUser':
          const index = this.userGrounpData.findIndex(user => user.userId === item.userId && user.nickName === item.nickName);
          if(index !== -1) {
            return
          } else {
            this.userGrounpData.push({
              userId: item.userId,
              nickName: item.nickName
            })
          }
          break;
      }
    },

    // 用户/组取消选择
    userSelctCancel(type, item) {
      switch (type) {
        case 'single':
          this.userSingleData.userId = ''
          this.userSingleData.nickName = ''
          break;

        case 'group':
          const index = this.userGrounpData.findIndex(user => user.userId === item.userId && user.nickName === item.nickName);
          if (index !== -1) {
            this.userGrounpData.splice(index, 1);
          }
          break;
      }
    },

    /**
     * 角色相关
     */
    // 查询角色信息
    roleList() {
      this.roleTableLoading = true
      listRole(this.roleParams).then((res) => {
        console.log('获取的角色信息：', res)
        if(res.code === 200) {
          this.roleTableData = res.rows
          this.roleTableTotal = res.total
        }
        this.roleTableLoading = false
      }).catch(() => {
        this.roleTableLoading = false
      })
    },

    // 角色组选择
    roleSelect(item) {
      const index = this.roleGrounpData.findIndex(role => role.roleKey === item.roleKey && role.roleName === item.roleName);
      if(index !== -1) {
        return
      } else {
        this.roleGrounpData.push({
          roleKey: item.roleKey,
          roleName: item.roleName
        })
      }
    },

    // 角色组取消选择
    roleSelctCancel(item) {
      const index = this.roleGrounpData.findIndex(role => role.roleKey === item.roleKey && role.roleName === item.roleName);
      if (index !== -1) {
        this.roleGrounpData.splice(index, 1);
      }
    },

    /**
     * 导入相关
     */
    /** 导入按钮操作 */
    handleImport() {
      this.upload.title = "流程模板导入";
      this.upload.open = true;
    },

    /** 下载模板操作 */
    importTemplate() {
      let vo = {

      }
      this.download('/workFlow/process-definition/importTemplate',
      {...vo},
      `流程模板_${new Date().getTime()}.xlsx`)
    },

    // 文件上传中处理
    handleFileUploadProgress(event, file, fileList) {
      this.upload.isUploading = true;
    },

    // 文件上传成功处理
    handleFileSuccess(response, file, fileList) {
      this.upload.open = false;
      this.upload.isUploading = false;
      this.$refs.upload.clearFiles();
      // this.$alert(response.msg, "导入结果", { dangerouslyUseHTMLString: true });
      this.$alert("<div style='overflow: auto;overflow-x: hidden;max-height: 70vh;padding: 10px 20px 0;'>" + response.msg + "</div>", "导入结果", { dangerouslyUseHTMLString: true });
      this.queryList();
    },

    // 提交上传文件
    submitFileForm() {
      this.$refs.upload.submit();
    },

    /**
     * 导出相关
     */

    // 导出表单
    formExport() {
      let vo = JSON.parse(JSON.stringify(this.queryParams))
      // if (this.tableSelection.length === 0) {
      this.$modal
        .confirm('即将根据查询条件导出所有流程数据，此过程可能花费时间较长，是否继续？')
        .then(() => {
          this.download(
            '/workFlow/process-definition/export',
            {...vo},
            `流程模板数据_${new Date().getTime()}.xlsx`,
            // {
            //   headers: { 'Content-Type': 'application/json;' },
            //   parameterType: 'body',
            // }
          )
        }).catch(() => {})
    },
    fromConfig(row){
      this.showConfig = true
      let data = {
        procDefId: row.id,
        processDefinitionKey: row.key
      }
      listTaskExt(data).then(res => {
        this.taskExtList = res.data
      })
    },
    onConfigSubmit(){
      saveTaskExt(this.taskExtList).then(res => {
        this.showConfig = false
        this.$modal.msgSuccess("保存成功");
      })
    }
  },
}

</script>

<style lang="scss" scoped>
.app-container form:first-child .el-select,
.app-container form:nth-child(2) .el-select,
.app-container form:nth-child(2) ::v-deep .el-form-item__content,
.app-container form:first-child ::v-deep .el-form-item__content {
  width: 240px;
}

.app-container form:first-child .el-form-item:last-child ::v-deep .el-form-item__content {
  width: auto;
}

.app-container {
  padding: 10px;
  background-color: #c0c0c0;
  box-sizing: border-box;
}

.formDialog {
  ::v-deep .el-dialog__body {
    height: 600px;
    overflow-y: auto;
  }

  .titleBox {
    height: 22px;
    position: relative;
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-between;

    .title {
      font-size: 16px;
      color: black;
      margin: 0;
    }

    .subTitle {
      margin-left: 15px;
      font-size: 12px;
      color: #888888;
    }

    .typeBox {
      font-size: 14px;
      font-weight: bold;
      margin-right: 20px;
    }
  }

  .formMain {
    height: 100%;
    width: 100%;
    box-sizing: border-box;
    border: 1px solid #888888;
    position: relative;

    .mainCanvas {
      height: 100%;
      width: 100%;
    }

    .mainPanel {
      position: absolute;
      top: 0;
      right: 0;
      height: 100%;
      width: 300px;
      border-left: 1px solid #888888;
      background-color: rgb(248, 248, 248);
      overflow-y: auto;
    }
  }
}

.searchBox {
  padding: 10px;
  background: #fff;
  border-radius: 10px;
  transition: all .1s linear;
  display: flex;
  flex-direction: column;

  .searchMoreBox {
    min-width: 192px;
    margin-top: 10px;
    display: flex;
    align-items: center;
    flex-direction: row;
  }
}

.tableDiv {
  margin-top: 10px;
  background-color: white;
  padding-bottom: 10px;
  border-radius: 10px;
  transition: all .1s linear;
  display: flex;
  flex-direction: column;

  .btnBox {
    padding: 10px;
  }
}

.infoBox {
  padding: 15px 15px 0 15px;
  box-sizing: border-box;
  border-radius: 6px;
  border: 1px solid #C4C4C4;
  position: relative;

  .infoTitle {
    user-select: none;
    position: absolute;
    top: 0;
    left: 0;
    padding: 0 10px;
    font-size: 14px;
    line-height: 14px;
    font-weight: bold;
    transform: translateX(15px) translateY(-50%);
    background-color: white;
  }

  .imgBox {
    height: auto;
    width: 100%;
    display: flex;
    flex-wrap: wrap;
    align-content: flex-start;

    .imgItemBox {
      height: 240px;
      width: calc(100% / 3);
      box-sizing: border-box;
      padding: 10px;
      overflow-y: auto;
      display: flex;
      align-items: center;
      justify-content: center;
      position: relative;

      .imgDeleteBtn {
        position: absolute;
        z-index: 1;
        top: 10%;
        right: 10%;
      }
    }
  }
}

// 流程图相关
.bpmnDialog {
  ::v-deep .el-dialog__body {
    height: 600px;
    overflow-y: auto;
  }

  .bpmnGroundMain {
    height: 100%;
    width: 100%;
    display: flex;
    flex-direction: column;

    .searchBox {
      height: 48px;
      width: 100%;
    }

    .dataBox {
      height: calc(100% - 148px);
      width: 100%;
      overflow-y: auto;
    }

    .selectBox {
      height: 100px;
      width: 100%;
      box-sizing: border-box;
      padding: 10px;
      border: 1px solid #C4C4C4;
      border-radius: 5px;
      display: flex;
      flex-direction: row;
      flex-wrap: wrap;
      align-content: flex-start;
      overflow-y: scroll;
      position: relative;

      // .selectTitle {
      //   position: absolute;
      //   top: 0;
      //   left: 10px;
      //   padding: 5px 10px;
      //   font-size: 14px;
      //   font-weight: bold;
      //   background-color: white;
      //   transform: translateY(-50%);
      // }
    }
  }
}

::v-deep .bjs-powered-by {
  opacity: 0;
  user-select: none;
  pointer-events: none;
}

// v-if过渡动画
// 查询框
.search-enter-active {
  transition: all .1s linear;
}
.search-enter {
  opacity: 0;
}
.search-leave-active {
  transition: all .1s linear;
}
.search-leave-to {
  opacity: 0;
}
</style>
