<template>
  <div>
    <el-dialog
      :visible.sync="showTable"
      width="70%"
      :before-close="handleClose"
      :close-on-click-modal="false"
    >
      <div
        slot="title"
        style="display: flex"
      >
        <DictTag
          :value="row.culvertType"
          :options="dict.type.sys_culvert_type"
        />
        {{ '总数量列表' }}
      </div>
      <div style="width: 100%;height: 70vh;">
        <el-input
          style="margin-right:20px; width: 170px"
          v-model="queryParams.culvertCode"
          placeholder="涵洞编码"
          clearable
        />

        <el-button
          type="primary"
          icon="el-icon-search"
          @click="handleQuery"
          >搜索</el-button
        >
        <el-button icon="el-icon-refresh" @click="resetQuery">重置</el-button>
        <el-button
          style="margin-bottom: 10px"
          type="primary"
          @click="exportList"
          >导出列表</el-button
        >
        <el-table
          v-adjust-table
          ref="table"
          v-loading="loading"
          height="calc(100% - 88px)"
          border
          :data="tableData"
          :header-cell-style="{'height': '36px'}"
        >
          <el-table-column
            label="序号"
            fixed="left"
            type="index"
            width="55"
            align="center"
            >
          <template v-slot="scope">
            {{
              scope.$index +
              (queryParams.pageNum - 1) * queryParams.pageSize +
              1
            }}
          </template>
        </el-table-column>
          <el-table-column
            label="涵洞编码"
            fixed="left"
            width="180"
            align="center"
            prop="culvertCode"
          />
          <el-table-column
            label="管理处"
            width="150"
            align="center"
            prop="managementMaintenanceName"
          />
          <el-table-column
            label="管辖分处"
            width="150"
            align="center"
            prop="managementMaintenanceBranchName"
          />
          <el-table-column
            label="养护路段"
            width="150"
            align="center"
            prop="maintenanceSectionName"
          />
          <el-table-column
            label="路线编码"
            width="80"
            align="center"
            prop="routeCode"
          />
          <el-table-column
            label="中心桩号"
            width="180"
            align="center"
          >
            <template slot-scope="scope">
              <span v-if="scope.row && scope.row.centerStake">{{formatPile(scope.row.centerStake)}}</span>
            </template>
          </el-table-column>

          <el-table-column
            label="涵洞类型"
            width="150"
            align="center"
            prop="startPointName"
          >
            <template slot-scope="scope">
              <span v-if="scope.row && scope.row.culvertType">
                <DictTag
                  :value="scope.row.culvertType"
                  :options="dict.type.sys_culvert_type"
                />
              </span>
            </template>
          </el-table-column>

          <el-table-column
            align="center"
            prop="operationState"
            width="110"
            label="运营状态"
          >
            <template slot-scope="{ row }">
              <el-link
                :underline="false"
                :type="{ 1: 'info', 2: 'success', 3: 'danger', 4: 'primary' }[row.operationState]"
              >
                <DictTag
                  :value="row.operationState"
                  :options="dict.type.sys_operation_state"
                />
              </el-link>
            </template>
          </el-table-column>
          <el-table-column
            align="center"
            prop="operationState"
            width="110"
            label="数据状态"
          >
            <template slot-scope="{ row }">
              <el-link :underline="false">
                {{ row.status == 1 ? "暂存" : "启用" }}
              </el-link>
            </template>
          </el-table-column>
          <el-table-column
            label="涵洞跨径(米)"
            width="150"
            align="center"
            prop="culvertSpan"
          />
          <el-table-column
            label="涵洞长度(米)"
            width="150"
            align="center"
            prop="culvertLength"
          />
          <el-table-column
            label="涵洞净高(米)"
            width="150"
            align="center"
            prop="culvertHeight"
          />
        </el-table>
        <pagination
          :total="total"
          :page.sync="queryParams.pageNum"
          :limit.sync="queryParams.pageSize"
          :pageSizes="[10, 20, 30, 50, 100, 1000]"
          @pagination="getList"
        />
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { culvertList } from '@/api/baseData/culvert/culvertInfo/index.js'

export default {
  name: 'culvert-statistics-detail-table',
  props: {
    showTable: { type: Boolean, default: false },
    row: { type: undefined, default: '' },
    params: { type: undefined, default: '' }
  },
  dicts: ['base_data_yes_no', 'sys_culvert_type', 'sys_operation_state'],
  components: {},
  data() {
    return {
      loading: false,
      queryParams: {
        pageNum: 1,
        pageSize: 20
      },
      total: 0,
      tableData: [],
      paramsObj: {}
    }
  },
  created() {
    this.getParams()
    this.getList()
  },
  methods: {
    getParams() {
      if (this.params) {
        this.paramsObj = {
          ...this.queryParams,
          ...this.params
        }
      }
    },
     // 搜索按钮
     handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    // 重置按钮
    resetQuery() {
      this.queryParams = {
        pageNum: 1,
        pageSize: 20,
      };
      this.handleQuery();
    },
    exportList() {
      if (this.tableData.length === 0) return;
      this.$modal
        .confirm("导出所有表格数据，是否继续？")
        .then(() => {
          let paramsObj = {
            ...this.paramsObj,
            ...this.queryParams,
            statusList: [2, 3, 4, 5]
          };
          this.download(
            "/baseData/culvert/exportPageTable",
            paramsObj,
            `culvert_interflow_${new Date().getTime()}.xlsx`,
            {
              headers: { "Content-Type": "application/json;" },
              parameterType: "body",
            }
          );
        })
        .catch(() => {});
    },
    getList() {
      this.loading = true
      let paramsObj = {
        ...this.paramsObj,
        ...this.queryParams,
        statusList: [2, 3, 4, 5]
      }
      culvertList(paramsObj)
        .then(res => {
          if (res.code === 200) {
            this.tableData = res.rows
            this.total = res.total
          }
        })
        .finally(() => {
          this.loading = false
        })
    },
    handleClose() {
      this.$emit('close')
    }
  },
  computed: {},
  watch: {}
}
</script>

<style scoped>
</style>