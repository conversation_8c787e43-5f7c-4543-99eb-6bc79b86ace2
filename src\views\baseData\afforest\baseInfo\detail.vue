<template>
  <div
    v-loading="loading"
    :class="oneMap ? 'one-map' : ''"
  >
    <el-form
      ref="form"
      :model="form"
      :rules="rules"
      label-width="150px"
      :disabled="true"
    >
      <div style="display: flex; flex-wrap: wrap; margin-bottom: 20px">
        <ManageSelectTree placeholder="请选择" :formObject="form" />

        <el-col :span="12">
          <el-form-item label="养护路段" prop="maintenanceSectionId">
            <el-select
              v-model="form.maintenanceSectionId"
              style="width: 100%"
              placeholder="请选择"
              clearable
              :disabled="!form.managementMaintenanceBranchId"
            >
              <el-option
                v-for="item in routeOptions"
                :key="item.maintenanceSectionId"
                :label="item.maintenanceSectionName"
                :value="item.maintenanceSectionId"
              />
            </el-select>
          </el-form-item>
        </el-col>

        <el-col :span="12">
          <el-form-item label="路线编码" prop="routeCode">
            <el-select
              v-model="form.routeCode"
              placeholder="请选择"
              :disabled="!form.maintenanceSectionId"
              clearable
              style="width: 100%"
            >
              <el-option
                v-for="item in routeList"
                :key="item.routeCode"
                :label="item.routeCode"
                :value="item.routeCode"
              >
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="资产子类" prop="typeId">
            <el-select
              v-model="form.typeId"
              style="width: 100%"
              placeholder="请选择"
              clearable
            >
              <el-option
                v-for="item in assetSubclassList"
                :key="item.id"
                :label="item.typeName"
                :value="item.id"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="资产编码" prop="assetCode">
            <el-input v-model="form.assetCode" placeholder="请输入" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="资产名称" prop="assetName">
            <el-input v-model="form.assetName" placeholder="请输入" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="方向" prop="direction">
            <el-select
              v-model="form.direction"
              style="width: 100%"
              placeholder="请选择"
              clearable
            >
              <el-option
                v-for="i in dict.type.sys_route_direction"
                :key="i.value"
                :label="i.label"
                :value="i.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="位置" prop="lane">
            <el-select
              v-model="form.lane"
              style="width: 100%"
              placeholder="位置"
              clearable
            >
              <el-option
                v-for="i in dict.type.lane"
                :key="i.value"
                :label="i.label"
                :value="i.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="左右" prop="leftOrRight">
            <el-select
              v-model="form.leftOrRight"
              style="width: 100%"
              placeholder="请选择"
              clearable
            >
              <el-option
                v-for="i in dict.type.left_right"
                :key="i.value"
                :label="i.label"
                :value="i.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="起点桩号" prop="startStake">
            <PileInput v-model="form.startStake" placeholder="请输入" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="终点桩号" prop="endStake">
            <PileInput v-model="form.endStake" placeholder="请输入" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="经纬度" prop="longitude">
            <lon-lat
              type="lonlat"
              :lon.sync="form.longitude"
              :lat.sync="form.latitude"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="施工里程桩号" prop="constructionStake">
            <PileInput v-model="form.constructionStake" placeholder="请输入" />
          </el-form-item>
        </el-col>

        <el-col :span="12">
          <el-form-item label="统一里程桩号" prop="unifiedMileageStake">
            <PileInput
              v-model="form.unifiedMileageStake"
              placeholder="请输入"
            />
          </el-form-item>
        </el-col>

        <el-col :span="12">
          <el-form-item label="国高网桩号" prop="nationalNetworkStake">
            <PileInput
              v-model="form.nationalNetworkStake"
              placeholder="请输入"
            />
          </el-form-item>
        </el-col>

        <el-col :span="12">
          <el-form-item label="建成时间" prop="buildDate">
            <el-date-picker
              style="width: 100%"
              v-model="form.buildDate"
              type="date"
              placeholder="请选择建成时间"
              :picker-options="pickerOptions"
              clearable
              value-format="yyyy-MM-dd"
            />
          </el-form-item>
        </el-col>

        <el-col :span="12" v-for="item in formHead" :key="item.id">
          <el-form-item :label="item.alias" :prop="item.columnName">
            <el-input
              v-if="item.columnType == '2'"
              v-model="itemForm[item.columnName]"
              placeholder="请输入"
            />
            <el-input-number
              v-else-if="item.columnType == '3'"
              v-model="itemForm[item.columnName]"
              style="width: 100%"
              :min="0"
              :max="999999999"
              class="inputNumber"
            />
            <el-input-number
              v-else-if="item.columnType == '4'"
              v-model="itemForm[item.columnName]"
              style="width: 100%"
              :min="0"
              :max="999999999"
              :precision="3"
              class="inputNumber"
            />
            <span v-else-if="item.columnType == '5' || item.columnType == '6'">
              <el-date-picker
                style="width: 100%"
                v-model="itemForm[item.columnName]"
                type="date"
                placeholder="请选择"
                clearable
                value-format="yyyy-MM-dd"
              />
            </span>
            <el-select
              v-else-if="item.columnType == '7'"
              v-model="itemForm[item.columnName]"
              style="width: 100%"
              placeholder="请选择"
              clearable
            >
              <el-option
                v-for="i in [
                  { label: '是', value: '1' },
                  { label: '否', value: '0' },
                ]"
                :key="i.value"
                :label="i.label"
                :value="i.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="移交管理单位" prop="transferManagementUnit">
            <el-input
              v-model="form.transferManagementUnit"
              style="width: 100%"
              clearable
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="备注" prop="remark">
            <el-input
              v-model="form.remark"
              type="textarea"
              autosize
              clearable
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="图片" prop="picPath">
            <ImageUpload
              :key="ownerId"
              v-model="form.picPath"
              :limit="1"
              :owner-id="ownerId"
              storage-path="/base/facility/baseInfo"
            />
          </el-form-item>
        </el-col>
      </div>
    </el-form>
  </div>
</template>
  
  <script>
import PileInput from "@/components/PileInput/index.vue";
import SelectTree from "@/components/DeptTmpl/selectTree";
import SectionSelect from "@/components/SectionSelect";
import ManageSelectTree from "@/components/manageSelectTree/index.vue";
import lonLat from "@/components/mapPosition/lonLat.vue";

import {
  getFacility,
  getAssetSubclass,
  getDynamicData,
} from "@/api/baseData/facility/baseInfo/index";

export default {
  name: "facility-form",
  components: {
    PileInput,
    SelectTree,
    SectionSelect,
    ManageSelectTree,
    lonLat,
  },
  inject: ["oneMap"],
  props: {
    formData: {
      default: {},
    },
    id: {
      type: [String, Number],
      default: "",
    },
  },
  dicts: ["sys_route_direction", "left_right", "lane"],
  data() {
    return {
      loading: false,
      form: {
        managementMaintenanceId: "",
        managementMaintenanceBranchId: "",
      },
      pickerOptions: {
        disabledDate(v) {
          return v.getTime() > new Date().getTime();
        },
      },
      itemForm: {},
      assetSubclassList: [],
      formHead: [],
      rules: {
        managementMaintenanceId: [
          { required: true, message: "请选择管理处", trigger: "change" },
        ],
        managementMaintenanceBranchId: [
          { required: true, message: "请选择管养分处", trigger: "change" },
        ],
        maintenanceSectionId: [
          { required: true, message: "请选择养护路段", trigger: "change" },
        ],
        routeCode: [
          { required: true, message: "请选择路线编码", trigger: "change" },
        ],
        typeId: [
          { required: true, message: "请选择资产类型", trigger: "change" },
        ],

        startStake: [
          { required: true, message: "请输入起点桩号", trigger: "blur" },
        ],

        endStake: [
          { required: true, message: "请输入终点桩号", trigger: "blur" },
        ],
      },
      routeOptions: [],
      routeList: [],
      ownerId: null,
    };
  },
  created() {
    this.init();
  },
  mounted() {},
  methods: {
    init() {
      getFacility(this.id).then((res) => {
        if (res.code === 200) {
          this.form = res.data;
          this.itemForm = res.data?.items || {};
          this.getAssetSubclassList();
        }
      });
    },
    getAssetSubclassList() {
      getAssetSubclass({ mainTypeId: 7 }).then((res) => {
        this.assetSubclassList = res;

        if (this.form.typeId) {
          let data = this.assetSubclassList.find(
            (item) => item.id == this.form.typeId
          );
          this.form.mainTypeId = data.mainType;
          this.getDynamicList();
        }
      });
    },
    getDynamicList() {
      getDynamicData({
        mainTypeId: this.form.mainTypeId,
        typeId: this.form.typeId,
        isDynamic: "Y",
      }).then((res) => {
        if (res.code == 200) {
          this.formHead = res.data;
        }
      });
    },
  },
};
</script>
  
<style lang="scss" scoped>
@import "@/assets/styles/common.scss";

::v-deep .el-textarea.is-disabled .el-textarea__inner {
  background-color: rgba(1, 102, 254, 0.2);
  border-color: #0166fe;
}
</style>
  