<template>
  <div class="stake-mark">
    <el-form :model="form" ref="form" :rules="rules" :label-width="isBig ? '160px' : '80px'" :inline="false">
      <el-form-item label="管理处">
        <el-select v-model="form.managementMaintenanceId" @change="handleDeptChange" @clear="handleDeptClear"
          placeholder="请选择" clearable filterable style="width: 100%;" :popper-append-to-body="false"
          popper-class="select-popper">
          <el-option v-for="item in deptOptions" :key="item.id" :label="item.label" :value="item.id" />
        </el-select>
      </el-form-item>
      <el-form-item label="养护路段">
        <el-select v-model="form.maintenanceSectionId" placeholder="请选择" clearable filterable style="width: 100%;"
        :popper-append-to-body="false" popper-class="select-popper">
          <el-option v-for="item in routeOptions" :key="item.maintenanceSectionId" :label="item.maintenanceSectionName"
            :value="item.maintenanceSectionId" />
        </el-select>
      </el-form-item>
      <el-form-item label="起点">
        <el-select v-model="form.routeCode" placeholder="请选择" clearable filterable style="width: 100%;"
        :popper-append-to-body="false" popper-class="select-popper">
          <el-option v-for="item in startOptions" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
      </el-form-item>
      <el-form-item label="终点">
        <el-select v-model="form.routeCode" placeholder="请选择" clearable filterable style="width: 100%;"
        :popper-append-to-body="false" popper-class="select-popper">
          <el-option v-for="item in endOptions" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
      </el-form-item>
      <el-form-item label="总重(吨)">
        <el-input v-model="form.totalWeight" placeholder="请输入..." clearable />
      </el-form-item>
      <el-form-item label="轴重(吨)">
        <el-input v-model="form.grossWeight" placeholder="请输入..." clearable />
      </el-form-item>
      <el-form-item label="轴距(米)">
        <el-input v-model="form.wheelbase" placeholder="请输入..." clearable />
      </el-form-item>
    </el-form>
    <div class="btn-list">
      <el-button type="primary" @click="onSearch" size="mini">搜索</el-button>
      <el-button type="primary" @click="onQuit" size="mini" class="hollow-out">退出</el-button>
    </div>
  </div>
</template>

<script>
import { deptTreeSelect } from '@/api/tmpl'
import {
  getMaintenanceSectionListAll
} from '@/api/baseData/common/routeLine'
import { isBigScreen } from '../common/util';

export default {
  data() {
    return {
      isBig: isBigScreen(),
      form: {
        totalWeight: '',
        grossWeight: '',
        wheelbase: '',
      },
      rules: {},
      deptOptions: [],
      routeOptions: [],
      startOptions: [],
      endOptions: [],
    }
  },
  created() {
    this.getDept();
    this.getRoutes();
  },
  methods: {
    // 获取管理处
    getDept() {
      deptTreeSelect({ types: 201 }).then(res => {
        if (res.code === 200) {
          this.deptOptions = res.data || [];
        }
      })
    },
    // 获取养护路段数据
    getRoutes(e) {
      getMaintenanceSectionListAll({ departmentIdList: e }).then(res => {
        if (res.code == 200) {
          this.routeOptions = res.data || []
        }
      })
    },
    // 管养出监听选择变化
    handleDeptChange(e) {
      if (e) {
        this.getRoutes(e);
      }
    },
    // 清空管养出
    handleDeptClear(e) {
      this.form.maintenanceSectionId = '';
      this.getRoutes();
    },
    // 查询
    onSearch() { },
    // 退出
    onQuit() {
      this.$emit('quit');
    },
  }
}
</script>

<style lang="scss" scoped>
@import "@/assets/styles/utils.scss";

.stake-mark {
  z-index: 9999;
  margin: 10px 0;
  color: #ffffff;
  overflow: hidden;

  ::v-deep .el-form {
    .el-form-item__label {
      color: #ffffff;
      font-size: vwpx(30px);
      line-height: vwpx(72px);
    }
    .el-form-item--mini .el-form-item__label {
      line-height: unset !important;
    }
    .el-input--mini .el-input__inner {
      height: vwpx(68px);
      min-height: vwpx(66px);
    }
  }

  ::v-deep .el-input {
    .el-input__inner {
      background-color: rgba(1, 102, 254, 0.2);
      border: 1px solid #0166FE;
      color: #ffffff;
      font-size: vwpx(28px);
    }

    .el-input__inner::placeholder {
      color: #BBBBBB;
    }

    .el-input-group__append {
      background-color: rgba(1, 102, 254, 0.2);
      border: 1px solid #0166FE;
      color: #ffffff;
      border-left: none;
      padding: 0 vwpx(20px);
      cursor: pointer;
    }
  }

  .btn-list {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    margin-top: vwpx(60px);

    button {
      height: vwpx(72px);
      min-height: vwpx(66px);
      width: vwpx(150px);
      height: 3vh;
      font-size: 1.4vh;
    }

    .hollow-out {
      background-color: rgba(1, 102, 254, 0.2);
      color: #ffffff;
    }
  }

  ::v-deep .el-select-dropdown__item {
    font-size: vwpx(30px);
    margin: vwpx(15px) 0;
  }
}
</style>
