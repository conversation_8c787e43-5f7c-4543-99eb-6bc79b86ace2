<template>
    <div class="repair-detail">
        <Describe style="margin-bottom: 10px;"></Describe>
        <DetailedList style="margin-bottom: 10px;"></DetailedList>
        <Structure></Structure>
    </div>
  </template>
  
  <script>
  import Describe from './components/Describe.vue'
  import DetailedList from './components/DetailedList.vue'
  import Structure from './components/Structure.vue'
  export default {
    name: 'RepairDetail',
    components:{ Describe, DetailedList, Structure },
    data(){
      return {
      }
    },
    watch:{},
    computed:{},
    created(){},
    mounted(){},
    methods:{},
  }
  </script>
  
  <style lang="scss" scoped>
  .repair-detail {}
  </style>