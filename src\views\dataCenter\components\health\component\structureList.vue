<template>
  <div class="structure-list">
    <Tables :columns="clumns" :data="tableData" v-on="$listeners" />
  </div>
</template>

<script>
import Tables from '@/views/cockpit/components/tables.vue';
import { getAllStructureInfoGroupByDomain } from '@/api/cockpit/health';

export default {
  name: 'structureList',
  components: {
    Tables
  },
  data() {
    return {
      clumns: [
        {
          label: '名称',
          prop: 'structureName',
        },
        {
          label: '类型',
          prop: 'structureType',
          width: 50
        },
        {
          label: '等级',
          prop: 'level',
          width: 50
        },
      ],
      tableData: [],
      structureList: [], // 存放所有结构数据
    }
  },
  methods: {
    getList() {
      getAllStructureInfoGroupByDomain().then(res => {
        let data = res.rows || res.data || res.result || [];
        if (res.code === 200 && data) {
          let tableList = [];
          data.forEach(item => {
            if (item.structureNormalDataManageList && item.structureNormalDataManageList.length) {
              tableList = tableList.concat(item.structureNormalDataManageList);
            }
          });
          this.tableData = tableList;
          this.structureList = data;
        }
      })
    },
    // 根据名称 筛选过滤数据
    onFilter(name) {
      if(!name) return;
      let filterList = this.structureList.filter(item => {
        return item.domainName === name;
      });
      let list = filterList ? filterList[0]?.structureNormalDataManageList : [];
      this.tableData = list;
    },
    onSearch(keywords = '', clear = false) {
      if(keywords) {
        let list = this.tableData.filter(v=> v.structureName == keywords)
        this.tableData = list;
      } else if(this.domainName){
        this.onFilter(this.domainName)
      } else {
        let tableList = [];
        this.structureList.forEach(item => {
          if (item.structureNormalDataManageList && item.structureNormalDataManageList.length) {
            tableList = tableList.concat(item.structureNormalDataManageList);
          }
        });
        this.tableData = tableList;
      }
    }
  },
  mounted() {
    this.getList();
  }
}
</script>

<style lang="scss" scoped>
@import "@/assets/styles/utils.scss";

.structure-list {
  width: 100%;
  height: 100%;
  padding: vwpx(10px);
}
</style>