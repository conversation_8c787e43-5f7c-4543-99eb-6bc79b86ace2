<template>
  <div class="app-container">
    <el-form ref="elForm" :rules="rules" :model="formData" :inline="true" label-width="200px" v-loading="loading">
      <el-row :gutter="20" style="display: flex; flex-wrap: wrap;">
        <el-col :span="24" class="mb20">
          <div class="card_title">项目信息</div>
          <el-descriptions size="mini" :column="3" border :labelStyle="{width: '150px'}" :contentStyle="{width: '300px'}">
            <el-descriptions-item v-for="(item, index) in projectColumns" :key="index" :label="item.name">
              <el-tooltip :content="projectData[item.field]" placement="top">
                <dict-tag v-if="item.dict" :options="dict.type[item.dict]" :value="projectData[item.field]"/>
                <span v-else>{{ projectData[item.field] }}</span>
              </el-tooltip>
            </el-descriptions-item>
          </el-descriptions>
        </el-col>
        <el-col :span="12">
          <el-form-item label="签证单编码" prop="code">
            <el-input v-model="formData.code" :disabled="fromEvent"/>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="是否完结" prop="isEnd">
            <el-radio-group v-model="formData.isEnd" size="medium">
              <el-radio v-for="(item, index) in isEndOptions" :key="index" :label="item.value"
                        :disabled="item.disabled || fromEvent">{{ item.label }}
              </el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="是否计算安全保通费" prop="isGuarantee">
            <el-radio-group v-model="formData.isGuarantee" size="medium" @change="calculate">
              <el-radio v-for="(item, index) in urgentDegreeOptions" :key="index" :label="item.value"
                        :disabled="item.disabled || fromEvent">{{ item.label }}
              </el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="是否计算安全生产费" prop="isProduction">
            <el-radio-group v-model="formData.isProduction" size="medium" @change="calculate">
              <el-radio v-for="(item, index) in urgentDegreeOptions" :key="index" :label="item.value"
                        :disabled="item.disabled || fromEvent">{{ item.label }}
              </el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="修补完成时间" prop="endTime">
            <el-date-picker v-model="formData.endTime" format="yyyy-MM-dd" value-format="yyyy-MM-dd"
                            :style="{width: '100%'}" placeholder="请选择修补完成时间" clearable></el-date-picker>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="24" style="margin-bottom: 18px">
          <div class="card_title">施工前图片</div>
          <el-card class="box-card" shadow="never">
            <el-form-item label="">
              <sort-file-upload v-model="sgqcjzp" can-sort ref="sgqcjzp" :fileType="['png', 'jpg', 'jpeg']"></sort-file-upload>
            </el-form-item>
          </el-card>
        </el-col>
        <el-col :span="24" style="margin-bottom: 18px">
          <div class="card_title">施工后图片</div>
          <el-card class="box-card" shadow="never">
            <el-form-item label="">
              <sort-file-upload v-model="sgfj" can-sort ref="sgfj" :fileType="['png', 'jpg', 'jpeg']"></sort-file-upload>
            </el-form-item>
          </el-card>
        </el-col>
        <el-col :span="12">
          <div class="card_title">施工简图</div>
          <el-card class="box-card" shadow="never">
            <el-form-item label="">
              <sort-file-upload limit="1" v-model="sgjt" can-sort ref="sgjt" :fileType="['png', 'jpg', 'jpeg']"></sort-file-upload>
            </el-form-item>
          </el-card>
        </el-col>
        <el-col :span="12">
          <div class="card_title">签证附件</div>
          <el-card class="box-card" shadow="never">
            <el-form-item label="">
              <sort-file-upload v-model="wgfj" can-sort ref="wgfj"></sort-file-upload>
            </el-form-item>
          </el-card>
        </el-col>
        <el-col :span="24" style="margin-top: 18px">
          <el-tabs v-if="finishedStructureList.length > 0" v-model="editableTabsValue" :closable="!fromEvent" @tab-remove="removeTab">
            <el-tab-pane v-for="item in finishedStructureList" :label="item.name" :key="item.name" :name="item.name" >
              <div class="card_title" style="display: flex;width: 100%;justify-content: space-between">
                <div>登记子目</div>
                <el-button icon="el-icon-plus" circle @click="openLibModel" v-if="!fromEvent"></el-button>
              </div>
              <methods-list editSafetyFee :value.sync="item.finishedMethodList" @price-change="calculate" :read-only="fromEvent"></methods-list>
              <el-col :span="24" style="margin-top: 18px">
                <div class="card_title">养护措施</div>
                <el-select v-model="item.maintainMeasureName" placeholder="请选择养护措施" clearable style="width: 240px" :disabled="fromEvent">
                  <el-option v-for="item in maintainList" :key="item.dictValue" :label="item.dictLabel"
                              :value="item.dictLabel">
                  </el-option>
                </el-select>
              </el-col>
            </el-tab-pane>
          </el-tabs>
          <template v-else>
            <div class="card_title" style="display: flex;width: 100%;justify-content: space-between">
              <div>登记子目</div>
              <el-button icon="el-icon-plus" circle @click="openLibModel" v-if="!fromEvent"></el-button>
            </div>
            <methods-list editSafetyFee :value.sync="methodList" @price-change="calculate" :read-only="fromEvent"></methods-list>
          </template>
        </el-col>
        <el-col :span="24" style="margin-top: 18px">
          <div class="card_title" style="display: flex;width: 100%;justify-content: space-between">
            <div>计算式及说明</div>
            <el-link type="primary" @click="generateInstructions">生成计算式说明</el-link>
          </div>
          <el-input class="calculation_desc" v-model="formData.calculationDesc" type="textarea" :rows="4">
          </el-input>
        </el-col>
        <el-col :span="24" style="margin-top: 18px">
          <div class="card_title">登记备注</div>
          <el-input v-model="formData.remark" type="textarea" :rows="4" :disabled="fromEvent">
          </el-input>
        </el-col>
      </el-row>
      <div style="text-align: right;padding-right: 7.5px;margin-top: 18px">
        <el-button type="primary" @click="onSave">保 存</el-button>
        <el-button @click="onClose">退出</el-button>
      </div>
    </el-form>
    <methods-tree scheme-type="专项养护" :con-id="conId" ref="methodsRef" @input="checkLib" :domain-id="rowData.domainId"></methods-tree>
  </div>
</template>
<script>
import sortFileUpload from "@/components/SortFileUpload/index.vue";
import moment from "moment";
import {
  addFinishedDetail,
  editFinishedDetail,
  getDetailList,
  getFinishedInfo, getMethodsList, getStructureByProjId
} from "@/api/maintenanceProject/construction/completionReg";
import MethodsTree from "@/components/MethodsTree/index.vue";
import MethodsList from "@/components/MethodsList/index.vue";
import {listAllData} from "@/api/system/dict/data";
import {getSafetyFeeList} from "@/api/contract/quotationSystem";
import {getDetail as getContractInfo} from "@/api/contract/info";
import {v4 as uuidv4} from "uuid";

export default {
  name: "index",
  components: {MethodsTree, sortFileUpload,MethodsList},
  data() {
    return {
      formData: {
        isEnd: 0,
        isProduction: 1,
        isGuarantee: 1,
        endTime: moment().format('YYYY-MM-DD')
      },
      sgqcjzp: '',
      sgjt: '',
      sgfj: '',
      wgfj: '',
      loading: false,
      rules: {
        code: [{required: true, message: '请输入签证单编码', trigger: 'blur'}],
      },
      maintainList: [],
      methodList: [],
      finishedStructureList: [],
      editableTabsValue: '',
      taskId: '',
      conId: '',
      libData: [],
      contractInfo: {},
      isEndOptions: [{
        "label": "否",
        "value": 0
      }, {
        "label": "是",
        "value": 1
      }],
      urgentDegreeOptions: [{
        "label": "计算",
        "value": 1
      }, {
        "label": "不计算",
        "value": 2
      }],
      defaultProps: {
        children: 'children',
        label: 'schemeName'
      },
      projectColumns: [
        { name: '项目名称', field: 'projName', dict: '' },
        { name: '任务单名称', field: 'name', dict: '' },
        { name: '任务单编码', field: 'code', dict: '' },
        { name: '路段名称', field: 'maiSecName', dict: '' },
        { name: '位置', field: 'mileRange', dict: '' },
        { name: '实施要求', field: 'exeRequire', dict: '' },
        { name: '施工单位', field: 'conDomainName', dict: '' },
        { name: '施工合同', field: 'conConName', dict: '' },
        { name: '监理单位', field: 'supDomainName', dict: '' },
        { name: '监理合同', field: 'supConName', dict: '' },
        { name: '设计单位', field: 'designDomainName', dict: '' },
        { name: '设计合同', field: 'designConName', dict: '' },
        { name: '工作内容', field: 'content', dict: '' },
      ],
      projectData: {}
    }
  },
  props: {
    rowData: {
      type: Object,
      default: () => {
        return {}
      }
    },
    detailData: {
      type: Object,
      default: () => {
        return {}
      }
    },
    fromEvent: {
      type: Boolean,
      default: () => {
        return false
      }
    }
  },
  watch: {
    rowData: {
      handler(val) {
        if (val.id) {
          this.formData.conId = val.id
          this.projectData = val
          this.projectData.domainName = val.domainName
          this.projectData.maiSecName = val.maiSecName

          if (val.type == 1) {
            this.conId = val.conConId
          } else if (val.type == 2) {
            this.conId = val.checkConId
          } else if (val.type == 3) {
            this.conId = val.designConId
          }
          if (val.projId) {
            getStructureByProjId(val.projId).then(res => {
              if (res.data.length > 0) {
                this.editableTabsValue = res.data[0].structureName
                res.data.forEach(item => {
                  this.finishedStructureList.push({
                    name: item.structureName,
                    maintainMeasureName: '',
                    finishedMethodList: []
                  })
                })
              } else {
                if (!this.detailData.id) this.getDetail(val.id)
              }

              if (!this.detailData.id) {
                this.initSafetyFeeList()
              }
            })
          }
        }
      },
      immediate: true
    },
    detailData: {
      handler(val) {
        if (val.id) {
          getFinishedInfo(val.id).then(res => {
            this.formData = res.data
            this.methodList = this.formData.constructionFinishedMethodList || []
            this.finishedStructureList = this.formData.finishedStructureList || []
            const sgqcjzp = this.formData.fileList.filter(item => item.registerType == 1)
            this.sgqcjzp = sgqcjzp.map(item => item.fileId)

            const sgfj = this.formData.fileList.filter(item => item.registerType == 2)
            this.sgfj = sgfj.map(item => item.fileId)

            const sgjt = this.formData.fileList.filter(item => item.registerType == 3)
            this.sgjt = sgjt.map(item => item.fileId)

            const wgfj = this.formData.fileList.filter(item => item.registerType == 4)
            this.wgfj = wgfj.map(item => item.fileId)
          })
        }
      },
      immediate: true
    },
  },
  mounted() {
    // this.getLibData()
    listAllData({dictType: 'maintain_measure_name'}).then(res => {
      this.maintainList = this.handleTree(res.data, "dictCode", "dictParentCode");
    })
    getContractInfo(this.rowData.conConId).then(res => {
      this.contractInfo = res.rows[0]
    })
  },
  methods: {
    getDetail(id) {
      getDetailList({projConId: id}).then(res => {
        res.rows.forEach(item => {
          item.id = undefined
        })
        this.methodList = res.rows
      })
    },
    getTaskDetail() {
      getMethodsList(this.rowData.id).then(res => {
        if (res.data) {
          const filteredData = res.data
          if (this.editableTabsValue) {
            this.finishedStructureList.forEach(structure => {
              for (let i = 0; i < filteredData.length; i++) {
                let item = filteredData[i]
                structure.finishedMethodList.push({
                  schemeId: item.schemeId ? item.schemeId : item.id,
                  schemeCode: item.schemeCode,
                  schemeName: item.schemeName,
                  unit: item.unit,
                  num: item.num,
                  calcDesc: item.calcDesc,
                  price: item.price,
                  amount: item.amount,
                  priceRate: item.priceRate,
                  decimalPlaces: item.decimalPlaces,
                  isProduction: item.isProduction
                })
              }
              structure.finishedMethodList = structure.finishedMethodList.filter((item, index, arr) => {
                return arr.findIndex(otherItem => otherItem.schemeCode === item.schemeCode) === index;
              });
              this.calculate()
            })
          } else {
            for (let i = 0; i < filteredData.length; i++) {
              let item = filteredData[i]
              this.methodList.push({
                schemeId: item.schemeId ? item.schemeId : item.id,
                schemeCode: item.schemeCode,
                schemeName: item.schemeName,
                unit: item.unit,
                price: item.price,
                priceRate: item.priceRate,
                decimalPlaces: item.decimalPlaces,
                isProduction: item.isProduction
              })
            }
            this.methodList = this.methodList.filter((item, index, arr) => {
              return arr.findIndex(otherItem => otherItem.schemeCode === item.schemeCode) === index;
            });
            this.calculate()
          }
        }
      })
    },
    onSave() {
      this.$refs.elForm.validate(valid => {
        if (!valid) return
        try {
          this.generateParams()
        } catch (e) {
          console.error('Error occurred while generating params:', e)
          return;
        }
        // if (!this.formData.constructionFinishedMethodList || this.formData.constructionFinishedMethodList.length == 0 || !this.formData.calculationDesc) {
        //   this.$message.warning('计算式、方法不能为空')
        //   return
        // }
        if (this.rowData.mtype == '3') {
          for (let i = 0; i < this.formData.finishedStructureList?.length; i++) {
            let item = this.formData.finishedStructureList[i]
            if (!item.maintainMeasureName) {
              this.$message.warning('养护措施不能为空！')
              return
            }
          }
        }
        for (let i = 0; i < this.formData.finishedStructureList?.length; i++) {
          let item = this.formData.finishedStructureList[i]
          for (let j = 0; j < item.finishedMethodList.length; j++) {
            let method = item.finishedMethodList[j]
            if (method.schemeName != '安全生产费' && method.schemeName != '安全保通费' && (method.num == null || method.num == undefined)) {
              this.$message.warning('子目数量不能为空！')
              return;
            }
          }
        }
        if (this.fromEvent) this.formData.flag = 1
        this.loading = true
        if (this.formData.id) {
          editFinishedDetail(this.formData).then(res => {
            this.loading = false
            this.$message.success('保存成功')
            this.onClose()
          }).finally(() => {
            this.loading = false
          })
        } else {
          addFinishedDetail(this.formData).then(res => {
            this.loading = false
            this.$message.success('保存成功')
            this.onClose()
          }).finally(() => {
            this.loading = false
          })
        }
      })
    },
    // 拼接参数
    generateParams() {
      this.$refs.sgqcjzp.save()
      this.$refs.sgfj.save()
      this.$refs.sgjt.save()
      this.$refs.wgfj.save()
      if (this.finishedStructureList.length > 0) {
        this.finishedStructureList.forEach(structure => {
          structure.finishedMethodList = structure.finishedMethodList.map(item => ({
            ...item,
            schemeId: item.schemeId ? item.schemeId : item.id
          }))
        })
        this.formData.finishedStructureList = this.finishedStructureList
      } else {
        this.methodList = this.methodList.map(item => ({
          ...item,
          schemeId: item.schemeId ? item.schemeId : item.id
        }))
        // 拼接参数
        this.formData.constructionFinishedMethodList = this.methodList
      }
      const fileList = []
      if (this.sgqcjzp) {
        this.sgqcjzp.forEach((item, index) => {
          fileList.push({
            fileId: item,
            indexOrder: index,
            registerType: 1
          })
        })
      }
      if (this.sgfj) {
        this.sgfj.forEach((item, index) => {
          fileList.push({
            fileId: item,
            indexOrder: index,
            registerType: 2
          })
        })
      }
      if (this.sgjt) {
        this.sgjt.forEach((item, index) => {
          fileList.push({
            fileId: item,
            indexOrder: index,
            registerType: 3
          })
        })
      }
      if (this.wgfj) {
        this.wgfj.forEach((item, index) => {
          fileList.push({
            fileId: item,
            indexOrder: index,
            registerType: 4
          })
        })
      }
      this.formData.fileList = fileList
    },
    checkLib(checkDatas) {
      let methodList = []
      const schemeList = []
      if (this.finishedStructureList.length == 0) {
        methodList = this.methodList
      } else {
        methodList = this.finishedStructureList.filter(item => item.name == this.editableTabsValue)[0].finishedMethodList
      }
      console.log(methodList)
      checkDatas = checkDatas.filter(item => item.nodeType === 2 && (item.rateFlag === 1 || !methodList.some(filterItem => filterItem.schemeCode === item.schemeCode)));
      checkDatas.forEach(item => {
        schemeList.push({
          schemeId: item.schemeId ? item.schemeId : item.id,
          id: uuidv4().replace(/-/g, '').slice(0, 20),
          schemeCode: item.schemeCode,
          schemeName: item.schemeName,
          unit: item.unit,
          price: item.price,
          priceRate: item.priceRate,
          isProduction: item.safetyFeeFlag,
          rateFlag: item.rateFlag,
          decimalPlaces: item.decimalPlaces
        })
      })
      if (this.finishedStructureList.length == 0) {
        this.methodList.push(...schemeList)
        // this.methodList = this.methodList.reduce((acc, curr) => {
        //   const exists = acc.some(item => item.schemeId === curr.schemeId);
        //   return exists ? acc : [...acc, curr];
        // }, []);
      } else {
        let methodList = this.finishedStructureList.filter(item => item.name == this.editableTabsValue)[0].finishedMethodList
        methodList.push(...schemeList)
        // methodList = methodList.reduce((acc, curr) => {
        //   const exists = acc.some(item => item.schemeId === curr.schemeId);
        //   return exists ? acc : [...acc, curr];
        // }, []);
      }
    },
    openLibModel() {
      this.$refs.methodsRef.openLibModel()
    },
    removeTab(targetName) {
      let tabs = this.finishedStructureList;
      let activeName = this.editableTabsValue;
      if (activeName === targetName) {
        tabs.forEach((tab, index) => {
          if (tab.name === targetName) {
            let nextTab = tabs[index + 1] || tabs[index - 1];
            if (nextTab) {
              activeName = nextTab.name;
            }
          }
        });
      }
      this.editableTabsValue = activeName;
      this.finishedStructureList = tabs.filter(tab => tab.name !== targetName);
    },
    // getSafetyFeeList() {
    //   getSafetyFeeList.then()
    // },
    onClose() {
      this.$emit("close")
    },
    calculate () {
      if (this.finishedStructureList.length > 0) {
        this.finishedStructureList.forEach(item => {
          let methodList = item.finishedMethodList
          let lib1 = methodList.find(item => item.schemeName == '安全生产费')
          if (lib1) {
            this.$set(lib1, 'amount', 0)
            // 计算安全费和保通费
            if (this.formData.isProduction == '1') {
              let aqf = 0
              for (let i = 0; i < methodList.length; i++) {
                let item = methodList[i]
                if( item.isProduction == '1' && item.schemeName != '安全生产费' && item.schemeName != '安全保通费') {
                  aqf += (lib1.amount || 0) + Math.round(item.amount || 0)
                }
              }
              this.$set(lib1, 'amount', Math.round(aqf * this.contractInfo.pSafeProductionRate))
            }
          }
          let lib2 = methodList.find(item => item.schemeName == '安全保通费')
          if (lib2) {
            this.$set(lib2, 'amount', 0)
            if (this.formData.isGuarantee == '1') {
              let aqf = 0
              for (let i = 0; i < methodList.length; i++) {
                let item = methodList[i]
                if( item.isProduction == '1' && item.schemeName != '安全生产费' && item.schemeName != '安全保通费') {
                  aqf += (lib2.amount || 0) + Math.round(item.amount || 0)
                }
              }
              this.$set(lib2, 'amount', Math.round(aqf * this.contractInfo.pSafeGuaranteeRate))
            }
          }
        })
      } else {
        let methodList = this.methodList
        let lib1 = methodList.find(item => item.schemeName == '安全生产费')
        if (lib1) {
          this.$set(lib1, 'amount', 0)
          // 计算安全费和保通费
          if (this.formData.isProduction == '1') {
            let aqf = 0
            for (let i = 0; i < methodList.length; i++) {
              let item = methodList[i]
              if( item.isProduction == '1' && item.schemeName != '安全生产费' && item.schemeName != '安全保通费') {
                aqf += (lib1.amount || 0) + Math.round(item.amount || 0)
              }
            }
            this.$set(lib1, 'amount', Math.round(aqf * this.contractInfo.pSafeProductionRate))
          }
        }
        let lib2 = methodList.find(item => item.schemeName == '安全保通费')
        if (lib2) {
          this.$set(lib2, 'amount', 0)
          if (this.formData.isGuarantee == '1') {
            let aqf = 0
            for (let i = 0; i < methodList.length; i++) {
              let item = methodList[i]
              if( item.schemeName != '安全生产费' && item.schemeName != '安全保通费') {
                aqf += (lib2.amount || 0) + Math.round(item.amount || 0)
              }
            }
            this.$set(lib2, 'amount', Math.round(aqf * this.contractInfo.pSafeGuaranteeRate))
          }
        }
      }
    },
    initSafetyFeeList(methodList) {
      // 初始化添加安全费保通费
      getSafetyFeeList(this.rowData.conConId).then(res => {
        // 过滤出schemeType为日常养护的，并且对schemeName去重
        const filteredData = res.data.filter(item => item.schemeType === '专项养护').reduce((acc, item) => {
          if (!acc.find(accItem => accItem.schemeName === item.schemeName)) {
            acc.push(item);
          }
          return acc;
        }, []).sort((a, b) => a.schemeName.localeCompare(b.schemeName));
        if (this.editableTabsValue) {
          for (let i = 0; i < this.finishedStructureList.length; i++) {
            let structure = this.finishedStructureList[i]
            for (let i = 0; i < filteredData.length; i++) {
              let item = filteredData[i]
              structure.finishedMethodList.splice(0, 0, {
                schemeId: item.schemeId ? item.schemeId : item.id,
                schemeCode: item.schemeCode,
                schemeName: item.schemeName,
                unit: item.unit,
                price: item.price,
                priceRate: item.priceRate,
                decimalPlaces: item.decimalPlaces,
                isProduction: item.safetyFeeFlag
              })
            }
          }
        } else {
          for (let i = 0; i < filteredData.length; i++) {
            let item = filteredData[i]
            this.methodList.splice(0, 0, {
              schemeId: item.schemeId ? item.schemeId : item.id,
              schemeCode: item.schemeCode,
              schemeName: item.schemeName,
              unit: item.unit,
              price: item.price,
              priceRate: item.priceRate,
              decimalPlaces: item.decimalPlaces,
              isProduction: item.safetyFeeFlag
            })
          }
        }
        this.getTaskDetail()
      })
    },
    // 生成计算式说明
    generateInstructions() {
      let calculationDesc = ''
      this.methodList.forEach(item => {
        if (item.schemeName == '安全生产费' || item.schemeName == '安全保通费') return
        calculationDesc += `${item.schemeName || ''}:(${item.calcDesc || item.num || ''})=${item.num || 0}${item.unit || ''}\n`
      })
      this.finishedStructureList.forEach(structure => {
        structure.finishedMethodList.forEach(item => {
          if (item.schemeName == '安全生产费' || item.schemeName == '安全保通费') return
          calculationDesc += `${item.schemeName || ''}:(${item.calcDesc || item.num || ''})=${item.num || 0}${item.unit}\n`
        })
      })
      this.$set(this.formData, 'calculationDesc', calculationDesc)
    }
  }
}
</script>
<style scoped lang="scss">
.card_title {
  width: 200px;
  text-align: left;
  margin-bottom: 15px;
  font-weight: bold;
}
</style>
<style lang="scss" scoped>
@import "@/assets/styles/business.scss";
</style>
