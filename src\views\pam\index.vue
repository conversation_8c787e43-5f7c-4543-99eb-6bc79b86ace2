<template>
  <div class="map" id="mapfull">
    <map-view ref="mapRef" v-if="!loading && isFinish" @click="onMapClick" />
    <!-- <arcgis-map ref="mapRef"/> -->
    <!-- <GeosceneMap ref="mapRef" /> -->
    <!-- 全屏操作 -->
    <div
      class="full-screen"
      title="退出"
      :style="{ top: !isFull ? '5px' : '-10px' }"
    >
      <svg-icon
        :icon-class="isFullscreen ? 'exit-fullscreen' : 'fullscreen'"
        @click="fullClick"
        v-if="!isFull"
      />
      <div v-else class="icon-img">
        <img
          :src="isFullscreen ? QuitFullImg : FullImg"
          @click="fullClick"
          class="click-full"
        />
        <img src="@/assets/map/quite.png" @click="handleQuit" />
      </div>
    </div>
    <!-- 操作目录 -->
    <Tree @check="handleTreeChecked" @finish="isFinish = true" ref="treeRef" />
    <!-- 图例 -->
    <Legend />
    <!-- 图层 -->
    <layer-controll />
    <!-- 左侧操作按钮 -->
    <left-menu v-if="!loading" />
    <!-- 右侧操作按钮 -->
    <!-- <right-menu @openTable="(val, flag, title) => { tableData = val; showRouteTable = flag; tableTitle = title; }" /> -->
    <route-table
      v-if="showRouteTable"
      :data="tableData"
      :tableTitle="tableTitle"
      @close="onCloseTable"
    />
    <right-menu @openTable="onClickTable" />

    <!-- 详情弹窗 -->
    <div v-if="showDVisible" class="dialog-detail">
      <Dialog
        :title="title"
        :show.sync="showDVisible"
        top="0"
        :append="false"
        width="70%"
        :modal="true"
      >
        <div class="dialog-content">
          <iframe
            :src="detailUrl"
            frameborder="0"
            style="width: 100%; height: 80vh"
          ></iframe>
        </div>
      </Dialog>
    </div>
    <!-- 点击地图弹出表格详情 - 接口类 -->
    <TableInfo
      :show.sync="tableShow"
      :url="infoUrl"
      :key="tableKey"
      :data-obj="dataObj"
    />
    <!-- 顶部标题 -->
    <div class="map-title">
      <span>公路智慧养护一张图</span>
    </div>
    <!-- 路线信息弹窗 -->
    <LineDialog
      :show.sync="showLVisible"
      :id="lineId"
      :coordinate="coordinate"
    />
  </div>
</template>

<script>
import Vue from 'vue';
// api
import { getListPage } from '@/api/oneMap/layer';

import screenfull from 'screenfull';
// 组件
import Tree from './components/tree.vue';
import Legend from './components/legend.vue';
import LayerControll from './components/layer.vue';
import MapView from './components/mapView.vue';
import ArcgisMap from './components/arcgisMap.vue';
import GeosceneMap from './components/geosceneMap.vue';
import LeftMenu from './components/left/leftMenu.vue';
import RightMenu from './components/right/menu.vue';
import RouteTable from './components/bottom/routeTable.vue';
import Dialog from '@/components/Dialog/index.vue';
import TableInfo from '@/components/table/tableInfo.vue';
import LineDialog from './components/lineDialog.vue';
// vuex
import { mapState, mapActions, mapMutations } from 'vuex';
import {
  addWidthFeature,
  removeLayer,
  addClickFeature,
  isValidWKT,
  lineId,
  featureToWkt,
} from './components/common/mapFun';
import { WKT } from 'ol/format';
import FullImg from '@/assets/map/full.png';
import QuitFullImg from '@/assets/map/quit-full.png';
import { getDataList } from '@/api/oneMap/layerData';
import cache from '@/plugins/cache';
import { isBigScreen } from './components/common/util';

const list = require.context('@/views', true, /\.vue$/);

export default {
  components: {
    Tree,
    Legend,
    LayerControll,
    MapView,
    ArcgisMap,
    LeftMenu,
    RightMenu,
    GeosceneMap,
    Dialog,
    TableInfo,
    RouteTable,
    LineDialog,
  },
  provide() {
    return {
      oneMap: this.isOneMap,
      instance: this,
    };
  },
  data() {
    return {
      mapLayer: null,
      isFullscreen: false,
      loading: false,
      isFinish: false, // 用户树组件 部门权限数据请求完成
      showVisible: false,
      componentName: '',
      formData: {}, // 详情参数
      tableShow: false, // 接口详情数据
      infoUrl: '', // 地图详情接口
      tableKey: '',
      dataObj: {}, // 请求参数
      detailType: null, // 弹窗详情类型
      isFull: false, //
      FullImg,
      QuitFullImg,
      showRouteTable: false,
      tableData: [],
      isOneMap: true,
      tableTitle: '',
      title: '详情',
      isTap: false,
      lineId: null,
      coordinate: null,
      detailUrl: 'https://zhyhpt.yciccloud.com:9000/webApp/brochure/test', // iframe加载的URL
    };
  },
  computed: {
    ...mapState({
      detlCondf: (state) => state.map.detlCondf,
      legendList: (state) => state.map.legendList,
      tableHeader: (state) => state.map.tableHeader,
      lineClick: (state) => state.map.lineClick,
    }),
    dynamicStyle() {
      let strStyle = '';
      if (this.isFull) {
        if (this.detailType === 3) {
          strStyle = 'height: calc(100vh - 170px);';
        } else {
          strStyle = 'height: calc(100vh - 80px);';
        }
      } else {
        if (this.detailType === 3) {
          strStyle = 'height: calc(100vh - 210px);';
        } else {
          strStyle = 'height: calc(100vh - 110px);';
        }
      }
      return strStyle;
    },
    // 是否显示详情弹窗
    showDVisible: {
      get() {
        return this.showVisible && !this.isTap;
      },
      set(val) {
        this.showVisible = false;
      },
    },
    // 是否显示路线弹窗
    showLVisible: {
      get() {
        return this.showVisible && this.isTap;
      },
      set(val) {
        this.showVisible = false;
      },
    },
  },
  created() {
    // 设置 树宽度
    let bigBool = isBigScreen();
    let width = bigBool ? 500 : 320;
    this.setSpread(width);

    this.getMapLayerList();
    // const objString = decodeURIComponent(this.$route.query.data);
    // const query = JSON.parse(objString)
    const query = this.$route.query;
    if (query.shape) {
      setTimeout(() => {
        let shape = query.shape;
        let feature = new WKT().readFeature(shape, {
          dataProjection: 'EPSG:4326',
          featureProjection: 'EPSG:3857',
        });
        let data = {
          ...query,
        };
        addClickFeature(
          feature,
          data,
          null,
          true,
          true,
          'clickLayer',
          2,
          query.name
        );
        window.mapLayer.getView().fit(feature.getGeometry().getExtent(), {
          duration: 300,
        });
        this.$router.replace({
          path: this.$route.path,
          query: {}, // 移除所有查询参数
        });
      }, 1000);
    }
    if (this.$route.name == 'oneMap') {
      this.isFull = true;
    }
  },
  mounted() {
    screenfull.on('change', this.change);
  },
  unmounted() {
    screenfull.off('change', this.change);
    window.mapLayer = null;
  },
  watch: {
    showVisible(val) {
      if (!val) {
        let { path } = this.$route;
        this.$router.push({ path });
      }
    },
  },
  methods: {
    ...mapActions({
      getMapLayer: 'map/getMapLayer',
      getDetlCondf: 'map/getDetlCondf',
      setSpread: 'map/setSpread',
    }),
    ...mapMutations({
      setTableHeader: 'map/setTableHeader',
      setLayer: 'map/setLayer',
    }),
    // 获取地图图层
    getMapLayerList() {
      this.$modal.loading();
      this.loading = true;
      getListPage({ pageNum: 1, pageSize: 99 })
        .then((res) => {
          if (res.code == 200 && res.rows) {
            let arr = res.rows.filter((v) => v.defaultMap == 1 || v.defaultMap);
            arr = arr.map((v) => {
              v.ifShow = !!v.ifShow;
              v.opacity = 100;
              v.name = v.layerName;
              return v;
            });
            this.getMapLayer(arr);
            this.setLayer({ layer: arr });
          }
        })
        .finally(() => {
          this.$modal.closeLoading();
          this.loading = false;
        });
    },
    change() {
      this.isFullscreen = screenfull.isFullscreen;
    },
    // 点击全屏
    fullClick() {
      let fullDom = document.getElementById('mapfull');
      if (fullDom) screenfull.toggle(fullDom);
    },
    // 选中树
    handleTreeChecked(data) {
      data = data ? data : [];
      if (this.$refs.mapRef) {
        // 将数据加载到地图
        // this.$refs.mapRef.setMapShape(data)
        console.log(data);
      }
    },
    // 地图点击事件
    async onMapClick(data) {
      console.log('点击', data);
      // 如果 存在面数据 并且 是点选则阻止
      if (data && data.mgeom && this.lineClick) return;
      this.isTap = false;
      this.title = '详情';
      let detlCondf;
      // 和图列数据匹配
      if (data && data.layer) {
        let arr = this.legendList.filter((v) => v.layerMenuSubId == data.layer);
        let listQueryConfig = arr && arr.length ? arr[0].listQueryConfig : null;
        if (listQueryConfig && typeof listQueryConfig == 'string') {
          listQueryConfig = JSON.parse(listQueryConfig);
        } else {
          listQueryConfig = listQueryConfig ? listQueryConfig : null;
        }

        if (listQueryConfig && typeof listQueryConfig.detlCondf == 'string') {
          detlCondf = JSON.parse(listQueryConfig.detlCondf);
        } else {
          detlCondf = listQueryConfig ? listQueryConfig.detlCondf : null;
        }
        this.setTableHeader({
          header:
            listQueryConfig && listQueryConfig.tableHeader
              ? listQueryConfig.tableHeader
              : this.tableHeader,
        });
      }

      if (!detlCondf) {
        let listQueryConfig = data ? data.listQueryConfig : null;
        if (listQueryConfig && typeof listQueryConfig.detlCondf == 'string') {
          detlCondf = JSON.parse(listQueryConfig.detlCondf);
        } else {
          detlCondf = listQueryConfig ? listQueryConfig.detlCondf : null;
        }
      }

      // 线图层
      if (data.layer == lineId && !detlCondf) {
        this.title = '路线信息';
        this.isTap = true;
        this.lineId = data.id;
        this.coordinate = data.coordinate;
        // 线图层数据 缓存
        let lineObj = cache.session.getJSON('lineObj');
        if (lineObj) {
          let listQueryConfig = lineObj.listQueryConfig;
          listQueryConfig =
            typeof listQueryConfig == 'string'
              ? JSON.parse(listQueryConfig)
              : listQueryConfig;
          if (listQueryConfig && typeof listQueryConfig.detlCondf == 'string') {
            detlCondf = JSON.parse(listQueryConfig.detlCondf);
          } else {
            detlCondf = listQueryConfig ? listQueryConfig.detlCondf : null;
          }
        }
        let shape = data.LineString
          ? new WKT().writeGeometry(data.LineString)
          : null;
        if (shape) {
          let feature = new WKT().readFeature(shape);
          // 移除
          removeLayer(window.mapLayer, 'clickLayer');
          // 添加到地图
          // addClickFeature(feature, data, null, true, true, 'clickLayer', 1, data.typename);
          // 移动到地图
          // window.mapLayer.getView().fit(feature.getGeometry().getExtent(), {
          //   duration: 500
          // })
        }
      }
      // 如果存在
      if (detlCondf) {
        this.getDetlCondf(detlCondf);
      }

      if (!data || !this.detlCondf) return;

      if (this.detlCondf.type && data.id) {
        this.formData = data || {};
        // this.detailUrl = `您的详情页URL?id=${data.id}`;
        this.showVisible = true;
      }
    },
    firstToUpperCase(str) {
      return str.charAt(0).toUpperCase() + str.slice(1);
    },
    // 退出、返回上个路由
    handleQuit() {
      // this.$router.back();
      this.$router.push({ path: 'home' });
    },
    // 点击表格
    onClickTable(val, flag, title) {
      // (val, flag, title) => { tableData = val; showRouteTable = flag; tableTitle = title; }
      this.tableData = val;
      // this.showRouteTable = flag;
      this.tableTitle = title;

      // 不显示table表格直接加载 ？
      removeLayer(window.mapLayer, 'clickLayer', 'name');
      removeLayer(window.mapLayer, 'clickLayer');
      let obj = {
        ...val,
        pageNum: 1,
        pageSize: 10000,
      };
      this.$modal.loading();
      getDataList(obj)
        .then((res) => {
          if (res.code === 200 && res.rows) {
            let arr = res.rows.map((row) => {
              row.name = row.lxmc || row.zdmc || row.qdmc || '';
              return row;
            });
            let features = [];
            // addWidthFeature(window.mapLayer, arr, 'clickLayer', null, true)
            arr.forEach((row) => {
              let shape = row.shape;
              if (shape) {
                if (!isValidWKT(shape)) return;
                let feature = new WKT().readFeature(shape, {
                  dataProjection: 'EPSG:4326',
                  featureProjection: 'EPSG:3857',
                });
                feature.set('data', {
                  ...row,
                  listQueryConfig: { detlCondf: this.detlCondf },
                });
                features = [...features, ...[feature]];
                // addClickFeature(feature, row, null, false, false, 'clickLayer', 1)
              }
            });
            addClickFeature(features, null, null, true, false, 'clickLayer', 1);
          }
        })
        .finally(() => {
          this.$modal.closeLoading();
        });
    },
    // 关闭表格弹窗
    onCloseTable() {
      this.showRouteTable = false;
      this.tableData = [];
      this.tableTitle = '';
    },
  },
};
</script>

<style lang="scss" scoped>
@import '@/assets/styles/utils.scss';

@font-face {
  font-family: 'YouSheBiaoTiHei';
  /* 自定义的字体名称 */
  src: url('~@/assets/home/<USER>') format('truetype');
  /* 字体文件路径和格式 */
  /* 可选属性，根据需要设置 */
  font-weight: normal;
  font-style: normal;
}

.map {
  width: 100%;
  height: 100%;
  position: relative;

  .map-view {
    width: 100%;
    height: 100%;
  }

  .full-screen {
    position: absolute;
    right: vwpx(10px);
    cursor: pointer;
    color: #ffffff;
    z-index: 10000;

    .icon-img {
      display: flex;
      align-items: center;

      .click-full {
        width: vwpx(52px);
        height: vwpx(52px);
      }

      img {
        width: vwpx(100px);
        height: vwpx(100px);
      }
    }
  }

  .map-title {
    position: absolute;
    top: 3%;
    left: 50%;
    width: vwpx(900px);
    height: vwpx(120px);
    transform: translate(-50%, -50%);
    background-image: url('~@/assets/map/map-title-bg.png');
    background-repeat: no-repeat;
    background-size: 100% 100%;

    display: flex;
    align-items: center;
    justify-content: center;

    span {
      font-family: YouSheBiaoTiHei;
      font-weight: 400;
      font-size: vwpx(60px);
      letter-spacing: 2px;
      text-align: center;
      font-style: normal;
      text-transform: none;
      background: linear-gradient(
        180deg,
        #ffffff 0%,
        #ffffff 60%,
        #20a9ff 100%
      );
      background-clip: text;
      -webkit-background-clip: text;
      color: transparent;
    }
  }
}

.dialog-map {
  .dialog-content {
    // max-height: calc(100vh - 110px);
    // height: calc(100vh - 210px);
    overflow-y: auto;
  }
}

.dialog-body {
  ::v-deep .el-dialog {
    margin: 0 auto 0;

    &:not(.is-fullscreen) {
      margin-top: 0vh !important;
    }
  }

  .dialog-content {
    // height: calc(100vh - 110px);
    overflow-y: auto;
  }
}

.dialog-detail {
  ::v-deep .el-dialog {
    margin: 0 auto 0;
    background: rgba(4, 17, 48, 0.8);
    box-shadow: inset 0px 0px 10px 0px #3662ec;
    border-radius: 10px 10px 10px 10px;
    border: 1px solid #0687ff;
    color: #ffffff !important;

    .el-dialog__header {
      border-bottom: none;
      padding: 10px 15px !important;

      .el-dialog__title {
        color: #ffffff;
      }

      .el-dialog__headerbtn {
        color: #ffffff;
        top: 10px;
      }
    }

    .el-dialog__body {
      padding: 10px;
      color: #ffffff !important;
    }
  }
}
</style>
