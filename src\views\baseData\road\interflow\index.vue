<template>
  <PageContainer>
    <template slot="search">
      <div style="display: flex; align-items: center; margin: 0">
        <CascadeSelection
          style="min-width: 192px"
          :form-data="searchForm"
          v-model="searchForm"
          types="201"
          multiple
        />
        <RangeInput
          :clearData="clearData"
          @startValue="
            (v) => {
              searchForm.startStake = v;
            }
          "
          @endValue="
            (v) => {
              searchForm.endStake = v;
            }
          "
        />
        <div style="min-width: 240px">
          <el-button
            v-hasPermi="['baseData:interFlow:query']"
            type="primary"
            icon="el-icon-search"
            @click="onSearch"
            >搜索</el-button
          >
          <el-button icon="el-icon-refresh" @click="resetQuery">重置</el-button>
          <el-button
            v-show="!showSearch"
            icon="el-icon-arrow-down"
            circle
            @click="showSearch = true"
          />
          <el-button
            v-show="showSearch"
            icon="el-icon-arrow-up"
            style="
              color: #1890ff;
              border-color: #badeff;
              background-color: #e8f4ff;
            "
            circle
            @click="showSearch = false"
          />
        </div>
      </div>
      <el-form
        v-show="showSearch"
        ref="queryForm"
        :model="searchForm"
        :inline="true"
      >
        <el-form-item style="margin: 5px 10px 0 0">
          <el-input
            v-model="searchForm.assetName"
            style="width: 100%"
            placeholder="互通名称"
            clearable
          />
        </el-form-item>
        <el-form-item style="margin: 5px 10px 0 0">
          <el-input
            v-model="searchForm.assetCode"
            style="width: 100%"
            placeholder="互通编码"
            clearable
          />
        </el-form-item>

        <el-form-item style="margin: 5px 10px 0 0">
          <el-select
            v-model="searchForm.operationState"
            placeholder="运营状态"
            clearable
            collapse-tags
          >
            <el-option
              v-for="dict in dict.type.sys_operation_state"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item style="margin: 5px 10px 0 0">
          <el-select
            v-model="searchForm.status"
            placeholder="数据状态"
            clearable
          >
            <el-option
              v-for="dict in dict.type.base_data_state"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
      </el-form>
    </template>

    <template slot="header">
      <el-row :gutter="10">
        <el-button
          v-hasPermi="['baseData:interFlow:add']"
          type="primary"
          @click="onAdd"
          class="mb8"
          >新增</el-button
        >
        <el-button
          v-hasPermi="['baseData:interFlow:edit']"
          type="primary"
          @click="onEdit"
          class="mb8"
          >编辑</el-button
        >
        <el-button
          v-hasPermi="['baseData:interFlow:getInfoById']"
          type="primary"
          @click="onDetail"
          class="mb8"
          >查看</el-button
        >
        <el-button
          v-hasPermi="['baseData:interFlow:delete']"
          type="primary"
          @click="onRemove"
          class="mb8"
          >删除</el-button
        >
        <el-button
          v-hasPermi="['baseData:interFlow:genQrCode']"
          type="primary"
          @click="onQrCode"
          class="mb8"
        >
          二维码下载
        </el-button>
        <el-button
          v-hasPermi="['baseData:interFlow:status']"
          type="primary"
          @click="onStatusChange"
          class="mb8"
        >
          运营状态变更
        </el-button>
        <el-button
          v-hasPermi="['baseData:interFlow:importAdd']"
          type="primary"
          @click="onImport"
          class="mb8"
          >导入新增</el-button
        >
        <el-button
          v-hasPermi="['baseData:interFlow:exportPageTable']"
          type="primary"
          @click="onExport"
          class="mb8"
          >导出清单</el-button
        >
        <!-- <el-button
        v-hasPermi="['manager:road:lock']"
        :disabled="!(tableSelects.length > 0)"
        type="primary"

        @click="onLock"
      >是否锁定</el-button> -->
      </el-row>
    </template>
    <template slot="body">
      <el-table
        v-adjust-table
        ref="table"
        v-loading="loading"
        border
        height="100%"
        style="width: 100%"
        :data="tableData"
        :row-style="rowStyle"
        @selection-change="onSelectTable"
        @row-click="handleRowClick"
        :row-key="(row) => row.id"
      >
        <el-table-column
          fixed="left"
          type="selection"
          width="50"
          align="center"
          :reserve-selection="true"
        />
        <el-table-column
          fixed
          label="序号"
          type="index"
          width="50"
          align="center"
        >
          <template v-slot="scope">
            {{
              scope.$index +
              (pages.pageNum - 1) * pages.pageSize +
              1
            }}
          </template>
        </el-table-column>
        <el-table-column
          fixed
          label="操作"
          min-width="50"
          align="center"
          prop="interFlowCode"
        >
          <template #default="{ row }">
            <el-link
              type="primary"
              :underline="false"
              :disabled="!row.shape"
              @click.stop="onToMap(row)"
              >定位</el-link
            >
          </template>
        </el-table-column>
        <el-table-column
          fixed
          align="center"
          prop="interFlowName"
          label="互通名称"
        >
        </el-table-column>
        <el-table-column
          label="互通编码"
          min-width="120"
          align="center"
          prop="interFlowCode"
        />

        <el-table-column
          label="管理处"
          min-width="120"
          align="center"
          prop="managementMaintenanceName"
        />
        <el-table-column
          label="管养分处"
          min-width="180"
          align="center"
          prop="managementMaintenanceBranchName"
        />
        <el-table-column
          label="养护路段"
          min-width="180"
          align="center"
          prop="maintenanceSectionName"
        />
        <el-table-column
          label="数据状态"
          align="center"
          prop="status"
          min-width="100"
        >
        <template slot-scope="scope">
            <el-link
              :type="{ 1: 'info', 2: 'success' }[scope.row.status]"
              :underline="false"
            >
              <DictTag
                :value="scope.row.status"
                :options="dict.type.base_data_state"
              />
            </el-link>
          </template>
        </el-table-column>

        <el-table-column align="center" prop="" label="运营状态">
          <template slot-scope="{ row }">
            <el-link
              :underline="false"
              :type="
                { 1: 'info', 2: 'success', 3: 'danger', 4: 'primary' }[
                  row.operationState
                ]
              "
              @click.stop="onStatusList(row)"
            >
              <DictTag
                :value="row.operationState"
                :options="dict.type.sys_operation_state"
              />
            </el-link>
          </template>
        </el-table-column>
        <el-table-column
          label="起点桩号"
          min-width="140"
          align="center"
          prop="startStake"
        >
          <template slot-scope="scope">
            {{ formatPile(scope.row.startStake) }}
          </template>
        </el-table-column>
        <el-table-column
          label="巡查方向"
          min-width="140"
          align="center"
          prop="direction"
        >
          <template #default="{ row }">
            <DictTag
              :value="row.direction"
              :options="dict.type.sys_route_direction"
            />
          </template>
        </el-table-column>
        <el-table-column
          label="位置"
          min-width="140"
          align="center"
          prop="lane"
        >
          <template #default="{ row }">
            <DictTag :value="row.lane" :options="dict.type.lane" />
          </template>
        </el-table-column>
        <el-table-column
          label="经度"
          min-width="140"
          align="center"
          prop="longitude"
        />
        <el-table-column
          label="纬度"
          min-width="140"
          align="center"
          prop="latitude"
        />
        <el-table-column
          label="施工里程桩号"
          min-width="120"
          align="center"
          prop="constructionStake"
        >
          <template slot-scope="scope">
            {{ formatPile(scope.row.constructionStake) }}
          </template>
        </el-table-column>
        <el-table-column
          label="统一里程桩号"
          min-width="120"
          align="center"
          prop="unifiedMileageStake"
        >
          <template slot-scope="scope">
            {{ formatPile(scope.row.unifiedMileageStake) }}
          </template>
        </el-table-column>
        <el-table-column
          label="互通形式"
          min-width="120"
          align="center"
          prop="interFlowModality"
        />
        <el-table-column
          label="被交叉道"
          min-width="120"
          align="center"
          prop="crossingRoadBy"
        />
        <el-table-column
          label="交叉方式"
          min-width="120"
          align="center"
          prop="crossingRoadWay"
        />
        <el-table-column
          label="交角(°)"
          min-width="120"
          align="center"
          prop="overlapAngle"
        />
        <el-table-column
          label="交叉点桩号"
          min-width="120"
          align="center"
          prop="overlapPointStake"
        />
        <el-table-column
          label="详情"
          width="80"
          align="center"
          prop="samplePictureId"
          fixed="right"
        >
          <template slot-scope="scope">
            <el-button
              style="font-size: 12px; font-weight: 500"
              type="text"
              @click.stop="onDetail(scope.row)"
              v-hasPermi="['baseData:interFlow:getInfoById']"
              >查看</el-button
            >
          </template>
        </el-table-column>
        <!-- <el-table-column
        label="操作"
        width="160"
        align="center"
        class-name="small-padding fixed-right"
        fixed="right"
      >
        <template slot-scope="scope">
          <el-button
            v-hasPermi="['manager:road:edit']"

            type="text"
            icon="el-icon-edit"
            @click="onEdit(scope.row)"
          >编辑</el-button>
          <el-button
            v-hasPermi="['manager:road:detail']"

            type="text"
            icon="el-icon-search"
            @click="onDetail(scope.row)"
          >查看</el-button>
          <el-button
            v-hasPermi="['manager:road:delete']"

            type="text"
            icon="el-icon-delete"
            @click="onRemove(scope.row.id)"
          >删除</el-button>
        </template>
      </el-table-column> -->
      </el-table>

      <pagination
        v-show="pages.total > 0"
        :total="pages.total"
        :page.sync="pages.pageNum"
        :limit.sync="pages.pageSize"
        @pagination="getList"
      />
    </template>

    <Edit
      v-if="showAddEdit"
      :title="title"
      :showAddEdit="showAddEdit"
      :form-data="modelData"
      :forView="forView"
      @close="editClose"
    />

    <el-dialog
      title="图片"
      :visible.sync="showImage"
      width="500px"
      append-to-body
    >
      <ImagePreview :owner-id="imageId" width="100%" height="100%" />
    </el-dialog>
    <ImportData
      v-if="showImport"
      :is-update="false"
      :dialog-visible="showImport"
      :import-base-type="'4'"
      :import-type="2"
      @close="closeImport"
    />

    <Dialog :title="showTitle" :show.sync="showVisible">
      <div>暂无数据</div>
    </Dialog>
    <MapPosition
      v-if="showMapPosition"
      :dialogVisible="showMapPosition"
      :data="mapPositionData"
      @close="showMapPosition = false"
    />
  </PageContainer>
</template>

<script>
import {
  interflowList,
  interflowDelete,
  routeInterflowLock,
  importData,
  interflowGetById,
} from "@/api/baseData/road/interflow/index.js";
import SelectTreeCheckbox from "@/components/DeptTmpl/selectTreeCheckbox";
import RouteLine from "@/components/RouteLine";
import RouteRoad from "@/components/RouteRoad";
import Dialog from "@/components/Dialog/index.vue";
import Edit from "./edit.vue";
import ImportData from "@/views/baseData/components/importData/index.vue";
import CascadeSelection from "@/components/CascadeSelection/index.vue";
import { statusDialog } from "@/views/baseData/components/statusDialog/index.js";
import { statusListDialog } from "@/views/baseData/components/statusDialog/list.js";
import MapPosition from "@/components/mapPosition/index.vue";
import RangeInput from "@/views/baseData/components/rangeInput/index.vue";

export default {
  name: "Interflow",
  dicts: [
    "sys_route_direction",
    "lane",
    "patrol_inspection_direction",
    "sys_operation_state",
    "base_data_state",
  ],
  components: {
    RangeInput,
    SelectTreeCheckbox,
    RouteLine,
    RouteRoad,
    Dialog,
    Edit,
    ImportData,
    CascadeSelection,
    MapPosition,
  },
  data() {
    return {
      loading: false,
      managementMaintenanceIds: [], // 选中数组
      tableSelects: [], // table选中数组
      tableData: [],
      clearData: false,
      showAddEdit: false, // 添加/编辑
      showDetail: false, // 查看
      modelData: {
        managementMaintenanceId: "",
        maintenanceSectionId: "",
        managementMaintenanceBranchId: "",
      }, // 模态框数据
      searchForm: {
        operationState: "2", // 默认2 运营中
      },
      showSearch: false,
      pages: {
        pageNum: 1,
        pageSize: 20,
        total: 0,
      },
      sysSurfaceTypeOptions: [],
      sysRouteGradeOptions: [],
      showImage: false,
      imageId: undefined,
      ids: [],
      title: "",
      showImport: false,
      forView: false,
      showVisible: false, // 二维码弹窗显示隐藏
      showTitle: "二维码下载", // 二维码弹窗 标题
      tableH: "92%", // 表格高度
      showMapPosition: false,
      mapPositionData: undefined,
    };
  },
  created() {
    this.init();
    // 改变表格高度
    this.tableH = window.innerHeight - 300 + "px";
    window.addEventListener("resize", () => {
      this.tableH = window.innerHeight - 300 + "px";
    });
  },
  methods: {
    init() {
      this.getDicts("sys_surface_type").then((res) => {
        this.sysSurfaceTypeOptions = res.data;
      });
      this.getDicts("sys_route_grade").then((res) => {
        this.sysRouteGradeOptions = res.data;
      });
      this.getList();
    },
    // 查询列表
    getList() {
      this.loading = true;
      this.searchForm = Object.assign(this.searchForm, this.pages);
      interflowList(this.searchForm)
        .then((res) => {
          if (res.code === 200) {
            this.tableData = res.rows;
            this.pages.total = res.total;
            this.loading = false;
            this.clearData = false;

            this.clearRowClick();
          }
        })
        .finally(() => {
          this.loading = false;
        });
    },
    // 搜索按钮操作
    onSearch() {
      this.pages.pageNum = 1;
      this.getList();
    },
    // 是否锁定按钮-未完成
    onLock() {
      const lockStatus = this.tableSelects[0].isLock;
      if (this.tableSelects.every((el) => el.isLock !== lockStatus)) {
        this.$message.warning("请勾选同种锁定状态的数据！");
        return;
      }
      this.loading = true;
      routeInterflowLock({
        ids: this.tableSelects.map((el) => el.id),
        isLock: !lockStatus,
      })
        .then((res) => {
          if (res.code === 200) {
            this.$message.success("操作成功！");
            this.getList();
          }
        })
        .finally(() => {
          this.loading = false;
        });
    },
    // 重置按钮操作
    resetQuery() {
      this.searchForm = {operationState: "2",};
      this.clearData = true;
      this.onSearch();
    },
    // 新增按钮
    onAdd() {
      this.showAddEdit = true;
      this.forView = false;
      this.title = "新增互通数据";
    },
    onDetail(row) {
      let id;
      if (row && !row.id) {
        if (this.tableSelects && this.tableSelects.length !== 1) {
          this.$modal.msgWarning("请选择一条数据");
          return;
        }
        id = this.tableSelects[0].id;
      } else {
        id = row.id;
      }
      this.$modal.loading();
      interflowGetById(id)
        .then((res) => {
          if (res.code === 200) {
            this.modelData = res.data;
            this.showAddEdit = true;
            this.forView = true;
            this.title = "查看互通信息";
          }
        })
        .finally(() => {
          this.$modal.closeLoading();
        });
    },
    // 导入按钮-未完成
    onImport() {
      this.showImport = true;
    },
    // 导出按钮
    onExport() {
      if (this.ids.length === 0) {
        this.$modal
          .confirm("即将导出所有表格数据，此过程可能花费时间较长，是否继续？")
          .then(() => {
            this.download(
              "baseData/routeInterFlow/exportPageTable",
              this.searchForm,
              `road_interflow_${new Date().getTime()}.xlsx`,
              {
                headers: { "Content-Type": "application/json;" },
                parameterType: "body",
              }
            );
          })
          .catch(() => {});
      } else {
        this.download(
          "baseData/routeInterFlow/exportPageTable",
          { ids: this.ids },
          `road_interflow_${new Date().getTime()}.xlsx`,
          {
            headers: { "Content-Type": "application/json;" },
            parameterType: "body",
          }
        );
      }
    },
    // 显示二维码下载弹窗
    onQrCode() {
      // this.showVisible = true;
      this.$modal
        .confirm("确定下载二维码？")
        .then(() => {
          if (this.tableSelects && this.tableSelects.length) {
            this.searchForm.ids = this.tableSelects.map((v) => v.id);
          }
          this.download(
            "/baseData/routeInterFlow/genQrCode",
            this.searchForm,
            `二维码_${new Date().getTime()}.zip`,
            {
              headers: { "Content-Type": "application/json;" },
              parameterType: "body",
            }
          );
        })
        .then(() => {
          this.searchForm.ids = [];
        })
        .catch(() => {});
    },
    // 运营状态变更
    onStatusChange() {
      if (this.tableSelects && this.tableSelects.length !== 1) {
        this.$modal.msgWarning("请选择一条数据！");
        return;
      }
      let row = this.tableSelects[0];
      // baseDataType 基础数据类型 1桥梁 2隧道 3涵洞 4互通
      statusDialog({ dataId: row.assetId, baseDataType: 4 }).then((res) => {
        if (res) {
          this.getList();
        }
      });
    },
    // 查看运营变更记录
    onStatusList(row) {
      statusListDialog({ dataId: row.assetId, baseDataType: 4 });
    },
    // 表格勾选操作
    onSelectTable(list) {
      this.tableSelects = list;
      this.ids = list.map((item) => item.id);
    },
    // 编辑按钮
    onEdit() {
      if (this.tableSelects && this.tableSelects.length !== 1) {
        this.$modal.msgWarning("请选择一条数据进行编辑！");
        return;
      }
      let id = this.tableSelects[0].id;
      interflowGetById(id).then((res) => {
        if (res.code === 200) {
          this.modelData = res.data;
          this.showAddEdit = true;
          this.forView = false;
          this.title = "编辑互通数据";
        }
      });
    },
    // 删除按钮
    onRemove(val) {
      if (this.ids.length === 0) {
        this.$message.warning("请选择至少一条数据进行删除！");
        return;
      }
      let canDel = true;
      this.ids.map((el) => {
        if (!canDel) {
          return;
        }
        this.tableData.map((e) => {
          if (el === e.id && e.isLocked) {
            canDel = false;
            this.$message.error(
              "互通名称：" + e.bridgeName + "已锁定，不允许删除！"
            );
          }
        });
      });
      if (canDel) {
        this.$modal
          .confirm("确认删除？")
          .then(() => {
            interflowDelete(this.ids).then((res) => {
              if (res.code == 200) {
                this.getList();
                this.clearRowClick();
                this.$modal.msgSuccess("删除成功");
              }
            });
          })
          .catch(() => {});
      }
    },
    // 查看图片按钮
    handlePhotos(v) {
      this.showImage = true;
      this.imageId = v;
    },
    // 表格点击勾选
    handleRowClick(row) {
      row.isSelected = !row.isSelected;
      this.$refs.table.toggleRowSelection(row);
    },
    clearRowClick() {
      this.ids = [];
      this.tableSelects = [];
      this.$refs.table.clearSelection();
    },
    // 勾选高亮
    rowStyle({ row, rowIndex }) {
      if (this.tableSelects.includes(row.id)) {
        return { "background-color": "#d5e8fa" };
      }
    },
    // 编辑弹框关闭
    editClose(flag) {
      this.showAddEdit = false;
      this.modelData = {
        managementMaintenanceId: "",
        maintenanceSectionId: "",
        managementMaintenanceBranchId: "",
      };
      if (flag) this.getList();
    },
    // 关闭导入弹窗
    closeImport(v) {
      this.showImport = false;
      if (v) this.getList();
    },
    // 跳转地图
    onToMap(row) {
      this.mapPositionData = row;
      this.showMapPosition = true;
    },
  },
};
</script>

<style lang="scss" scoped>
.container-view-list {
  v-deep .el-table {
    flex: 1;
  }
}
</style>
