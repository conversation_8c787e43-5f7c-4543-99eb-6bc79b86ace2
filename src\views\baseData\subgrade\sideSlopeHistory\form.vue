<template>
  <div>
    <el-dialog
      :title="title"
      :visible.sync="showAddEdit"
      width="60%"
      append-to-body
      :before-close="handleClose"
      :close-on-click-modal="false"
      :class="forView ? 'forView':''"
    >
      <div
        v-loading="loading"
        style="height: 60vh;overflow-y: auto;padding: 0 10px 0 5px;"
      >
        <el-form
          ref="form"
          :model="form"
          :rules="rules"
          label-width="150px"
          :disabled="forView ? true : false"
        >
          <div style="display: flex; flex-wrap: wrap; margin-bottom: 20px;">
            <el-divider content-position="left">基础数据</el-divider>
            <el-col :span="12">
              <el-form-item
                label="管理处"
                prop="managementMaintenanceId"
              >
                <SelectTree
                  v-model="form.managementMaintenanceId"
                  :dept-type="201"
                  placeholder="请选择"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item
                label="管养分处"
                prop="managementMaintenanceBranchId"
              >
                <SelectTree
                  v-model="form.managementMaintenanceBranchId"
                  :dept-type="202"
                  placeholder="请选择"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item
                label="养护路段"
                prop="maintenanceSectionId"
              >
                <el-select
                  v-model="form.maintenanceSectionId"
                  style="width: 100%;"
                  placeholder="请选择"
                  clearable
                  :disabled="!form.managementMaintenanceId"
                >
                  <el-option
                    v-for="item in routeOptions"
                    :key="item.maintenanceSectionId"
                    :label="item.maintenanceSectionName"
                    :value="item.maintenanceSectionId"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item
                label="路段类型"
                prop="sectionType"
              >
                <SectionSelect
                  v-model="form.sectionType"
                  :style="'width: 100%;' + (forView ? 'pointer-events: none' : '')"
                  :formObject="form"
                  :sectionId="form.maintenanceSectionId"
                  :disabled="!form.maintenanceSectionId"
                  clearable
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item
                label="路线编码"
                prop="routeCode"
              >
                <el-select
                  v-model="form.routeCode"
                  placeholder="请选择"
                  :disabled="!form.maintenanceSectionId"
                  clearable
                  style="width: 100%;"
                  @change="onRouteCodeChange"
                >
                  <el-option
                    v-for="item in routeList"
                    :key="item.routeId"
                    :label="item.routeName"
                    :value="item.routeCode"
                  >
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <!-- <el-col :span="12">
              <el-form-item
                label="路线名称"
                prop="routeName"
              >
                <el-select
                  v-model="form.routeName"
                  style="width: 100%;"
                  placeholder="请选择"
                  clearable
                >
                  <el-option
                    v-for="item in lx_Options"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  />
                </el-select>
              </el-form-item>
            </el-col> -->
            <el-col :span="12">
              <el-form-item
                label="路基编码"
                prop="subgradeCode"
              >
                <el-input-number
                  v-model="form.subgradeCode"
                  style="width: 100%;"
                  clearable
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item
                label="车道数"
                prop="laneCount"
              >
                <el-input-number
                  v-model="form.laneCount"
                  style="width: 100%;"
                  clearable
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item
                label="面层类型"
                prop="surfaceType"
              >
                <el-select
                  v-model="form.surfaceType"
                  style="width: 100%;"
                  placeholder="请选择"
                  clearable
                >
                  <el-option
                    v-for="item in dict.type.subgrade_suface_type"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item
                label="运营状态"
                prop="operationState"
              >
                <el-select
                  v-model="form.operationState"
                  style="width: 100%;"
                  placeholder="请选择"
                  clearable
                >
                  <el-option
                    v-for="item in dict.type.bridge_business_state"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item
                label="建成时间"
                prop="buildDate"
              >
                <el-date-picker
                  v-model="form.buildDate"
                  style="width:100%"
                  type="date"
                  placeholder="请选择"
                  clearable
                  value-format="yyyy-MM-dd"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item
                label="边坡名称"
                prop="slopeName"
              >
                <el-input
                  v-model="form.slopeName"
                  placeholder="请输入边坡名称"
                  clearable
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item
                label="边坡编码"
                prop="slopeCode"
              >
                <el-input
                  v-model="form.slopeCode"
                  placeholder="请输入边坡编码"
                  clearable
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item
                label="路段技术等级"
                prop="sectionTechLevelType"
              >
                <el-select
                  v-model="form.sectionTechLevelType"
                  style="width: 100%;"
                  placeholder="请选择"
                  clearable
                >
                  <el-option
                    v-for="item in dict.type.bridge_route_level"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item
                label="路基类型"
                prop="subgradeType"
              >
                <el-input
                  v-model="form.subgradeType"
                  placeholder="请输入路基类型"
                  clearable
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item
                label="平台数量"
                prop="platformNumber"
              >
                <el-input-number
                  v-model="form.platformNumber"
                  style="width: 100%;"
                  clearable
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item
                label="检查梯"
                prop="checkLadder"
              >
                <el-input
                  v-model="form.checkLadder"
                  placeholder="请输入路检查梯"
                  clearable
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item
                label="土质"
                prop="soil"
              >
                <el-input
                  v-model="form.soil"
                  placeholder="请输入土质"
                  clearable
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item
                label="坡脚长度"
                prop="slopingFootLength"
              >
                <el-input-number
                  v-model="form.slopingFootLength"
                  style="width: 100%;"
                  :precision="2"
                  clearable
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item
                label="落台宽度"
                prop="dropWidth"
              >
                <el-input-number
                  v-model="form.dropWidth"
                  style="width: 100%;"
                  :precision="2"
                  clearable
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item
                label="相对位置"
                prop="relativePosition"
              >
                <el-select
                  v-model="form.relativePosition"
                  style="width: 100%;"
                  placeholder="请选择"
                  clearable
                >
                  <el-option
                    v-for="item in dict.type.side_slope_relative_position"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item
                label="长度(米)"
                prop="slopeLength"
              >
                <el-input-number
                  v-model="form.slopeLength"
                  style="width: 100%;"
                  :precision="2"
                  clearable
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item
                label="路基宽度(米)"
                prop="subgradWidth"
              >
                <el-input-number
                  v-model="form.subgradWidth"
                  style="width: 100%;"
                  :precision="2"
                  clearable
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item
                label="路面宽度(米)"
                prop="pavementWidth"
              >
                <el-input-number
                  v-model="form.pavementWidth"
                  style="width: 100%;"
                  :precision="2"
                  clearable
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item
                label="总高度(米)"
                prop="totleHeight"
              >
                <el-input-number
                  v-model="form.totalHeight"
                  style="width: 100%;"
                  :precision="3"
                  clearable
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item
                label="边坡总台数"
                prop="slopePlatformNumber"
              >
                <el-input-number
                  v-model="form.slopePlatformNumber"
                  style="width: 100%;"
                  clearable
                />
              </el-form-item>
            </el-col>
            <!-- <el-col :span="12">
              <el-form-item
                label="防护形式总数"
                prop="protectionFormNumber"
              >
                <el-input-number
                  ref="inputNumber"
                  class="inputNumber"
                  v-model="form.protectionFormNumber"
                  style="width: 100%;"
                  clearable
                  :min="0"
                  :max="50"
                  :step="1"
                  @change="changeNumber"
                  @keydown.native="onKeydown"
                />
              </el-form-item>
            </el-col> -->
            <el-col :span="12">
              <el-form-item
                label="建造年度"
                prop="constructionDate"
              >
                <el-date-picker
                  v-model="form.constructionDate"
                  style="width:100%"
                  type="date"
                  placeholder="请选择"
                  clearable
                  value-format="yyyy-MM-dd"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item
                label="改建年度"
                prop="renovationDate"
              >
                <el-date-picker
                  v-model="form.renovationDate"
                  style="width:100%"
                  type="date"
                  placeholder="请选择"
                  clearable
                  value-format="yyyy-MM-dd"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item
                label="抗震设防等级"
                prop="seismicGradeType"
              >
                <el-select
                  v-model="form.seismicGradeType"
                  style="width: 100%;"
                  placeholder="请选择"
                  clearable
                >
                  <el-option
                    v-for="item in dict.type.side_slope_seismic_grade"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item
                label="防洪标准"
                prop="floodControlType"
              >
                <el-select
                  v-model="form.floodControlType"
                  style="width: 100%;"
                  placeholder="请选择"
                  clearable
                >
                  <el-option
                    v-for="item in dict.type.side_slop_flood_control"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item
                label="示例图像"
                prop="samplePictureId"
              >
                <span v-if="forView && !form.samplePictureId">无</span>
                <span v-else>
                  <ImageUpload
                    v-if="!forView"
                    :key="ownerId"
                    v-model="form.samplePictureId"
                    :limit="1"
                    :owner-id="ownerId"
                    storage-path="/base/subgrade/sideSlope"
                  />
                  <ImagePreview
                    v-else
                    :owner-id="form.samplePictureId"
                    width="146px"
                    height="146px"
                  />
                </span>
              </el-form-item>
            </el-col>
            <el-divider content-position="left">边坡类型</el-divider>
            <el-col :span="24">
              <el-form-item label-width="40px">
                <el-radio
                  v-for="item in dict.type.side_slope_type"
                  v-model="form.slopeType"
                  :label="item.value"
                  :key="item.value"
                >{{item.label}}</el-radio>
              </el-form-item>
            </el-col>
            <div v-if="form.slopeType == '1'">
              <el-col :span="12">
                <el-form-item
                  label="路堤坡高(m)"
                  prop="embankmentSlopeHeight"
                >
                  <el-input-number
                    v-model="form.embankmentSlopeHeight"
                    style="width: 100%;"
                    :precision="2"
                    clearable
                  />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item
                  label="路堤坡度"
                  prop="embankmentSlope"
                >
                  <el-input-number
                    v-model="form.embankmentSlope"
                    style="width: 100%;"
                    :precision="2"
                    clearable
                  />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item
                  label="路堤分级"
                  prop="embankmentClassification"
                >
                  <el-input-number
                    v-model="form.embankmentClassification"
                    style="width: 100%;"
                    :precision="2"
                    clearable
                  />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item
                  label="是否临河"
                  prop="nearRiverType"
                >
                  <el-select
                    v-model="form.nearRiverType"
                    style="width: 100%;"
                    placeholder="请选择"
                    clearable
                  >
                    <el-option
                      v-for="item in dict.type.base_data_yes_no"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item
                  label="临河地形"
                  prop="riverTerrainType"
                >
                  <el-select
                    v-model="form.riverTerrainType"
                    style="width: 100%;"
                    placeholder="请选择"
                    clearable
                  >
                    <el-option
                      v-for="item in dict.type.side_slope_river_terrain"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
            </div>
            <div v-else-if="form.slopeType == '2'">
              <el-col :span="12">
                <el-form-item
                  label="路堑坡高(m)"
                  prop="cuttingSlopeHeight"
                >
                  <el-input-number
                    v-model="form.cuttingSlopeHeight"
                    style="width: 100%;"
                    :precision="2"
                    clearable
                  />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item
                  label="路堑坡度"
                  prop="cuttingSlope"
                >
                  <el-input-number
                    v-model="form.cuttingSlope"
                    style="width: 100%;"
                    :precision="2"
                    clearable
                  />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item
                  label="路堑分级"
                  prop="cuttingClassification"
                >
                  <el-input-number
                    v-model="form.cuttingClassification"
                    style="width: 100%;"
                    :precision="2"
                    clearable
                  />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item
                  label="岩性"
                  prop="lithologyType"
                >
                  <el-select
                    v-model="form.lithologyType"
                    style="width: 100%;"
                    placeholder="请选择"
                    clearable
                  >
                    <el-option
                      v-for="item in dict.type.side_slope_lithology"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
            </div>
            <div v-else>
              <el-form-item label-width="40px">请选择边坡类型</el-form-item>
            </div>
            <el-divider content-position="left">排水设施类型</el-divider>
            <el-col :span="12">
              <el-form-item
                label="地表排水设施"
                prop="surfaceDrainageFacilitiesType"
              >
                <el-select
                  v-model="form.surfaceDrainageFacilitiesType"
                  style="width: 100%;"
                  placeholder="请选择"
                  clearable
                >
                  <el-option
                    v-for="item in dict.type.side_slope_surface_drainage_facilities"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item
                label="地下排水设施"
                prop="undergroundDrainageFacilitiesType"
              >
                <el-select
                  v-model="form.undergroundDrainageFacilitiesType"
                  style="width: 100%;"
                  placeholder="请选择"
                  clearable
                >
                  <el-option
                    v-for="item in dict.type.side_slope_underground_drainage_facilities"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-divider content-position="left">防护设施类型</el-divider>
            <el-col :span="12">
              <el-form-item
                label="坡面防护"
                prop="slopeProtectionType"
              >
                <el-select
                  v-model="form.slopeProtectionType"
                  style="width: 100%;"
                  placeholder="请选择"
                  clearable
                >
                  <el-option
                    v-for="item in dict.type.side_slope_protection"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item
                label="沿河防护"
                prop="reiverProtectionType"
              >
                <el-select
                  v-model="form.reiverProtectionType"
                  style="width: 100%;"
                  placeholder="请选择"
                  clearable
                >
                  <el-option
                    v-for="item in dict.type.base_river_protection"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item
                label="支挡设施"
                prop="supportFacilitiesType"
              >
                <el-select
                  v-model="form.supportFacilitiesType"
                  style="width: 100%;"
                  placeholder="请选择"
                  clearable
                >
                  <el-option
                    v-for="item in dict.type.side_slope_support_facilities"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-divider content-position="left">统一里程</el-divider>
            <el-col :span="12">
              <el-form-item
                label="边坡起点桩号"
                prop="unifiedMileageStartStake"
              >
                <PileInput v-model="form.unifiedMileageStartStake" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="起点经纬度">
                <div class="flex-between">
                  <el-input
                    v-model="form.unifiedMileageStartLongitude"
                    type="number"
                    placeholder="经度"
                    class="longitude-latitude"
                  >
                    <template slot="prepend">Lon</template>
                  </el-input>
                  <el-input
                    v-model="form.unifiedMileageStartLatitude"
                    type="number"
                    placeholder="纬度"
                    class="longitude-latitude"
                  >
                    <template slot="prepend">Lat</template>
                  </el-input>
                </div>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item
                label="边坡终点桩号"
                prop="unifiedMileageEndStake"
              >
                <PileInput v-model="form.unifiedMileageEndStake" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="终点经纬度">
                <div class="flex-between">
                  <el-input
                    v-model="form.unifiedMileageEndLongitude"
                    type="number"
                    placeholder="经度"
                    class="longitude-latitude"
                  >
                    <template slot="prepend">Lon</template>
                  </el-input>
                  <el-input
                    v-model="form.unifiedMileageEndLatitude"
                    type="number"
                    placeholder="纬度"
                    class="longitude-latitude"
                  >
                    <template slot="prepend">Lat</template>
                  </el-input>
                </div>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item
                label="统一里程桩号"
                prop="unifiedMileageStake"
              >
                <PileInput v-model="form.unifiedMileageStake" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item
                label="管养里程(km)"
                prop="maintenanceMileage"
              >
                <el-input-number
                  v-model="form.maintenanceMileage"
                  style="width: 100%;"
                  :precision="3"
                  clearable
                />
              </el-form-item>
            </el-col>
            <el-divider content-position="left">国高网里程</el-divider>
            <el-col :span="12">
              <el-form-item
                label="起点桩号"
                prop="nationalNetworkStartStake"
              >
                <PileInput v-model="form.nationalNetworkStartStake" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item
                label="终点桩号"
                prop="nationalNetworkEndStake"
              >
                <PileInput v-model="form.nationalNetworkEndStake" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item
                label="国高网里程(km)"
                prop="nationalNetworkMileage"
              >
                <el-input-number
                  v-model="form.nationalNetworkMileage"
                  style="width: 100%;"
                  :precision="3"
                  clearable
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item
                label="国高网桩号"
                prop="nationalNetworkStake"
              >
                <PileInput v-model="form.nationalNetworkStake" />
              </el-form-item>
            </el-col>
            <el-divider content-position="left">施工里程</el-divider>
            <el-col :span="12">
              <el-form-item
                label="起点桩号"
                prop="constructionStartStake"
              >
                <PileInput v-model="form.constructionStartStake" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item
                label="终点桩号"
                prop="constructionEndStake"
              >
                <PileInput v-model="form.constructionEndStake" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item
                label="施工里程(km)"
                prop="constructionMileage"
              >
                <el-input-number
                  v-model="form.constructionMileage"
                  style="width: 100%;"
                  :precision="3"
                  clearable
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item
                label="施工里程桩号"
                prop="constructionStake"
              >
                <PileInput v-model="form.constructionStake" />
              </el-form-item>
            </el-col>
            <el-divider content-position="left">防护形式</el-divider>
            <el-col :span="24">
              <el-form-item label="防护形式总数">
                <el-input-number
                  ref="inputNumber"
                  class="inputNumber"
                  v-model="form.protectionFormNumber"
                  style="width: 200px"
                  clearable
                  :min="0"
                  :max="50"
                  :step="1"
                  @change="changeNumber"
                  @keydown.native="onKeydown"
                />
              </el-form-item>
            </el-col>
            <div
              class="slope-protection-card"
              v-for="(item,index) in form.slopeProtectionRecords"
              :key="index"
            >
              <i
                v-if="!forView"
                class="el-icon-remove"
                @click="removeCard(item,index)"
              ></i>
              <el-col :span="12">
                <el-form-item
                  label="台高(m)"
                  prop="platformHeight"
                >
                  <el-input-number
                    v-model="item.platformHeight"
                    style="width: 100%;"
                    :precision="3"
                    clearable
                  />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item
                  label="防护形式"
                  prop="protectionForm"
                >
                  <el-input
                    v-model="item.protectionForm"
                    placeholder="请输入防护形式"
                    clearable
                  />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item
                  label="坡比"
                  prop="slopeRatio"
                >
                  <el-input-number
                    v-model="item.slopeRatio"
                    style="width: 100%;"
                    clearable
                  />
                </el-form-item>
              </el-col>
            </div>
          </div>
        </el-form>
      </div>
      <div slot="footer">
        <el-button
          v-if="!forView"
          type="primary"
          :loading="loading"
          @click="handleSubmit('submit')"
        >提 交</el-button>
        <el-button
          v-if="!forView && !formData.id"
          type="primary"
          :loading="loading"
          @click="handleSubmit('save')"
        >暂 存</el-button>
        <el-button @click="handleClose">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import PileInput from '@/components/PileInput/index.vue'
import SelectTree from '@/components/DeptTmpl/selectTree'
import SectionSelect from '@/components/SectionSelect'
import { createIdWorker } from '@/api/baseData/common'
import { listMaintenanceSectionAll } from '@/api/system/maintenanceSection'
import { listByMaintenanceSectionId } from '@/api/baseData/common/routeLine'
import {
  addSideSlope,
  tempSideSlope,
  updateSideSlope
} from '@/api/baseData/subgrade/sideSlope/index.js'

export default {
  name: 'sideSlope-addAndEdit',
  components: { PileInput, SelectTree, SectionSelect },
  props: {
    formData: {
      default: {}
    },
    showAddEdit: {
      default: false
    },
    title: {
      default: '添加边坡静态数据'
    },
    forView: {
      default: false
    }
  },
  dicts: [
    'bridge_route_level',
    'side_slope_relative_position',
    'side_slope_seismic_grade',
    'side_slop_flood_control',
    'base_data_yes_no',
    'side_slope_river_terrain',
    'side_slope_lithology',
    'side_slope_surface_drainage_facilities',
    'side_slope_underground_drainage_facilities',
    'side_slope_protection',
    'base_river_protection',
    'side_slope_support_facilities',
    'subgrade_suface_type',
    'bridge_business_state',
    'side_slope_type'
  ],
  data() {
    return {
      loading: false,
      form: {
        slopeProtectionRecords: []
      },
      rules: {
        managementMaintenanceId: [
          { required: true, message: '请选择管理处', trigger: 'change' }
        ],
        managementMaintenanceBranchId: [
          { required: true, message: '请选择管养分处', trigger: 'change' }
        ],
        maintenanceSectionId: [
          { required: true, message: '请选择养护路段', trigger: 'change' }
        ],
        routeCode: [
          { required: true, message: '请选择路线编码', trigger: 'change' }
        ],
        constructionDate: [
          { required: true, message: '请选择建造年度', trigger: 'change' }
        ],
        slopeName: [
          { required: true, message: '请选输入边坡名称', trigger: 'blur' }
        ],
        slopeCode: [
          { required: true, message: '请选输入边坡编码', trigger: 'blur' }
        ]
      },
      routeOptions: [],
      routeList: [],
      ownerId: ''
    }
  },
  created() {
    this.init()
  },
  mounted() {},
  methods: {
    init() {
      if (this.formData.id) {
        this.form = JSON.parse(JSON.stringify(this.formData))
        if (this.form?.slopeProtectionRecords?.length > 0) {
          this.form.protectionFormNumber = this.form?.slopeProtectionRecords?.length
        } else {
          this.form.slopeProtectionRecords = []
        }
      }
      if (!this.forView) {
        createIdWorker().then(res => {
          if (res.code === 200) {
            this.ownerId = Number(res.data)
          }
        })
      }
    },
    onKeydown(event) {
      if (event.key !== 'ArrowUp' && event.key !== 'ArrowDown') {
        event.preventDefault()
      }
    },
    handleSubmit(type) {
      let pass = true
      if (this.form?.slopeProtectionRecords?.length > 0) {
        this.form?.slopeProtectionRecords?.map(el => {
          if (!el.platformHeight && !el.protectionForm && !el.slopeRatio) {
            this.$modal.msgWarning(
              `有边坡形式记录未填写，请完善后再${
                type === 'submit' ? '提交' : '暂存'
              }`
            )
            pass = false
            return
          }
        })
      }
      switch (type) {
        case 'submit':
          this.form.status = 2
          break
        case 'save':
          this.form.status = 1
          break
      }
      if (this.form.status == 2) {
        this.$refs.form.validate(valid => {
          if (valid) {
            pass = true
          } else {
            pass = false
            return false
          }
        })
      }
      if (!pass) return
      this.form.samplePictureId =
        this.form?.samplePictureId?.length > 0
          ? this.form?.samplePictureId[0]
          : ''
      this.form.longitude = this.form.unifiedMileageStartLongitude
      this.form.latitude = this.form.unifiedMileageStartLatitude
      let arr = this.routeList.filter(el => {
        return el.routeCode === this.form.routeCode
      })
      this.form.routeId = arr[0]?.routeId
      this.form.routeName = arr[0]?.routeName
      this.loading = true
      if (this.form.id != null) {
        updateSideSlope(this.form)
          .then(response => {
            this.$modal.msgSuccess('修改成功')
            this.$emit('refresh')
          })
          .catch(() => {
            this.loading = false
          })
      } else {
        const api = this.form.status === 1 ? tempSideSlope : addSideSlope
        api(this.form)
          .then(response => {
            this.$modal.msgSuccess(
              this.form.status === 1 ? '暂存成功' : '新增成功'
            )
            this.$emit('refresh')
          })
          .catch(() => {
            this.loading = false
          })
      }
    },
    deptChange(e) {
      if (!e) return
      listMaintenanceSectionAll({ departmentId: e }).then(res => {
        if (res.code == 200) {
          this.routeOptions = res.data
        }
      })
    },
    maintenanceSectionChange(e) {
      if (!e) return
      listByMaintenanceSectionId({ maintenanceSectionId: e }).then(res => {
        if (res.code == 200) {
          this.routeList = res.data || []
          // this.form.routeCode = ''
          this.$forceUpdate()
        }
      })
    },
    removeCard(item, index) {
      if (!item.platformHeight && !item.protectionForm && !item.slopeRatio) {
        this.form.slopeProtectionRecords.splice(index, 1)
        this.form.protectionFormNumber = this.form.slopeProtectionRecords.length
      } else {
        this.$modal
          .confirm('确认删除改防护形式记录？')
          .then(() => {
            this.form.slopeProtectionRecords.splice(index, 1)
            this.form.protectionFormNumber = this.form.slopeProtectionRecords.length
          })
          .catch(() => {})
      }
    },
    changeNumber(newVal, oldVal) {
      if (oldVal === null || oldVal === undefined) oldVal = 0
      if (newVal > oldVal) {
        // 当数量增加时，在数组末尾添加新元素
        const lastIndex = this.form.slopeProtectionRecords.length - 1
        this.form.slopeProtectionRecords.push({
          platformHeight: '',
          protectionForm: '',
          slopeRatio: ''
        })
      } else if (newVal < oldVal) {
        // 当数量减少时，优先删除没有数据的元素，其次再从末尾删除
        const originalNumber = oldVal
        let deleteIndex = -1
        for (let i = this.form.slopeProtectionRecords.length - 1; i >= 0; i--) {
          const record = this.form.slopeProtectionRecords[i]
          if (
            !record.platformHeight &&
            !record.protectionForm &&
            !record.slopeRatio
          ) {
            deleteIndex = i
            break
          }
        }
        if (deleteIndex !== -1) {
          this.form.slopeProtectionRecords.splice(deleteIndex, 1)
        } else {
          this.$modal
            .confirm('该操作将删除已添加的防护形式记录，是否继续？')
            .then(() => {
              this.form.slopeProtectionRecords.pop()
            })
            .catch(() => {
              this.form.protectionFormNumber = originalNumber
            })
        }
      }
    },
    handleClose() {
      if (this.forView) {
        this.form = {}
        this.$emit('close', false)
      } else {
        this.$modal
          .confirm('确认退出？')
          .then(() => {
            this.form = {}
            this.$emit('close', false)
          })
          .catch(() => {})
      }
    },
    // 路线编码
    onRouteCodeChange(e) {
      let findObj = this.routeList.find((item) => item.routeCode == e);
      if(findObj) {
        this.form.routeId = findObj.routeId;
        this.$forceUpdate();
      }
    },
  },
  watch: {
    'form.managementMaintenanceId'(newVal, oldVal) {
      if (newVal) {
        this.deptChange(newVal)
      }
      if (!newVal&&this.form.routeCode) {
        this.form.routeCode = ''
      }
    },
    'form.maintenanceSectionId'(newVal, oldVal) {
      if (newVal) {
        this.maintenanceSectionChange(newVal)
      }
      if (!newVal&&this.form.routeCode) {
        this.form.routeCode = ''
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.forView ::v-deep .el-input.is-disabled .el-input__inner {
  background-color: white;
  border-color: #dfe4ed;
  color: black;
}
::v-deep .el-dialog__header {
  border-bottom: 1px #dfe4ed solid;
  padding: 20px 30px !important;
}
::v-deep .el-divider--horizontal {
  margin: 20px 0 !important;
}
.slope-protection-card {
  width: 100%;
  border: #dfe4ed 1px solid;
  border-radius: 10px;
  padding: 20px 40px 0 0;
  position: relative;
  margin-bottom: 10px;
  i {
    cursor: pointer;
    position: absolute;
    right: 5px;
    top: 45%;
    color: #f56c6c;
  }
}
// ::v-deep .inputNumber {
//   .el-input-number__decrease,
//   .el-input-number__increase {
//     display: block;
//     height: 30px;
//     margin-top: 1px;
//     display: flex;
//     justify-content: center;
//     align-items: center;
//   }
// }
</style>
<style lang="scss">
.longitude-latitude {
  .el-input-group__prepend {
    padding: 0 10px;
  }
  .el-input__inner {
    padding: 0 5px;
  }
}
</style>
