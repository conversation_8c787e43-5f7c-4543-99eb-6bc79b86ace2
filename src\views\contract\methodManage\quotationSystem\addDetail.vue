<template>
  <div class="road-interflow-edit" v-loading="loading">
    <el-row :gutter="15">
      <el-form
        ref="elForm"
        :model="formData"
        size="medium"
        :rules="rules"
        label-width="140px"
      >
        <el-col :span="12">
          <el-form-item label="方案类型" prop="schemeType">
            <el-input
              v-model="checkPNode"
              disabled
              placeholder="请输入方案类型"
              clearable
              :style="{ width: '100%' }"
            >
            </el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="节点类型" prop="type">
            <el-select
              v-model="formData.nodeType"
              placeholder="请选择节点类型"
              :style="{ width: '100%' }"
            >
              <el-option
                v-for="(item, index) in typeOptions"
                :key="index"
                :label="item.label"
                :value="item.value"
                :disabled="item.disabled"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="子目号" prop="schemeCode">
            <el-input
              v-model="formData.schemeCode"
              placeholder="请输入子目号"
              clearable
              :style="{ width: '100%' }"
            >
            </el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="子目名称" prop="schemeName">
            <el-input
              v-model="formData.schemeName"
              placeholder="请输入子目名称"
              clearable
              :style="{ width: '100%' }"
            >
            </el-input>
          </el-form-item>
        </el-col>
        <div v-if="formData.nodeType === 2">
          <el-col :span="12">
            <el-form-item label="适用管理处" prop="applyDomainArr">
              <el-select v-model="formData.applyDomainArr" multiple placeholder="请选择" :style="{ width: '100%' }" collapse-tags>
                <el-option
                    v-for="item in deptList"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="单价" prop="price">
              <el-input
                v-model="formData.price"
                placeholder="请输入单价"
                clearable
                :style="{ width: '100%' }"
              >
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="单位" prop="unit">
              <el-input
                v-model="formData.unit"
                placeholder="请输入单位"
                clearable
                :style="{ width: '100%' }"
              >
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="质保期" prop="guaranteePeriod">
              <el-input
                v-model="formData.guaranteePeriod"
                placeholder="请输入质保期"
                clearable
                :style="{ width: '100%' }"
              >
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="保留位数" prop="decimalPlaces">
              <el-input
                v-model="formData.decimalPlaces"
                placeholder="请输入保留位数"
                clearable
                :style="{ width: '100%' }"
              >
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="是否计算安全费用" prop="safetyFeeFlag">
              <DictSelect
                v-model="formData.safetyFeeFlag"
                :type="'bridge_simple_bool'"
                :placeholder="'计算安全费用'"
                clearable
              ></DictSelect>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="是否为费率章节" prop="rateFlag">
              <DictSelect
                v-model="formData.rateFlag"
                :type="'bridge_simple_bool'"
                :placeholder="'费率章节'"
                clearable
              ></DictSelect>
            </el-form-item>
          </el-col>
        </div>
        <el-col :span="12">
          <el-form-item label="当前节点" prop="rootId">
            <el-input
              v-model="formData.rootId"
              placeholder="请输入当前节点"
              clearable
              :style="{ width: '100%' }"
            >
            </el-input>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="备注" prop="remark">
            <el-input
              v-model="formData.remark"
              type="textarea"
              placeholder="请输入备注"
              :autosize="{ minRows: 4, maxRows: 4 }"
              :style="{ width: '100%' }"
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <div class="text-center">
            <el-button type="primary" @click="onSubmit">保 存</el-button>
            <el-button @click="onClose">取 消</el-button>
          </div>
        </el-col>
      </el-form>
    </el-row>
  </div>
</template>
<script>
import {
  addLibschpricetree,
  updateLibschpricetree,
  addLibschprice,
} from "@/api/contract/quotationSystem.js";
import { deptTreeSelect } from "@/api/tmpl";

import selectTree from "@/components/DeptTmpl/selectTree.vue";

export default {
  inheritAttrs: false,
  components: {selectTree},
  props: {
    lib: {
      type: Object,
      default: () => {
      }
    },
    formdata: {
      type: Object,
      default: () => {
      }
    },
    check: {
      type: Object,
      default: () => {
      }
    },
    checkPNode: {
      type: String,
      default: () => ''
    },
    editType: {
      type: String,
      default: () => ''
    }
  },
  data() {
    return {
      formData: {
        nodeType: 1,
      },
      deptList: [],
      rules: {
        nodeType: [
          { required: true, message: '请选择节点类型', trigger: 'change' }
        ],
        schemeCode: [
          { required: true, message: '请输入子目号', trigger: 'blur' }
        ],
        schemeName: [
          { required: true, message: '请输入子目名称', trigger: 'blur' }
        ],
        price: [
          { required: true, message: '请输入单价', trigger: 'blur' }
        ],
        unit: [
          { required: true, message: '请输入单位', trigger: 'blur' }
        ],
        safetyFeeFlag: [
          { required: true, message: '请选择是否计算安全费用', trigger: 'change' }
        ],
        rateFlag: [
          { required: true, message: '请选择是否为费率章节', trigger: 'change' }
        ]
      },
      typeOptions: [
        {
          label: "方法类型",
          value: 1,
        },
        {
          label: "方法",
          value: 2,
        },
      ]
    };
  },
  computed: {},
  watch: {},
  created() {
    var deptType ={types:101};
    deptTreeSelect(deptType).then(response => {
      response.data.forEach(item => {
        this.deptList.push({
          value: item.id,
          label: item.label
        })
      })
    })
  },
  mounted() {
    if (this.editType === 'update') this.formData = this.formdata
    if (this.formData.applyDomain) {
      this.$set(this.formData, 'applyDomainArr', this.formData.applyDomain.split(','))
    }
  },
  methods: {
    onOpen() {
    },
    onClose() {
      this.$emit("close");
    },
    handleConfirm() {
      this.$refs["elForm"].validate((valid) => {
        if (!valid) return;
        this.onClose();
      });
    },
    onSubmit() {
      this.formData.schemeType = this.checkPNode
      if (this.formData.applyDomainArr) this.formData.applyDomain = this.formData.applyDomainArr.join(',')
      this.$refs.elForm.validate(valid => {
        if (!valid) return;
        this.formData.libId = this.lib.id
        this.formData.safetyFeeFlag = this.formData.safetyFeeFlag || 2
        this.formData.rateFlag = this.formData.rateFlag || 2
        if (this.editType === 'update') {
          updateLibschpricetree(this.formData).then(res => {
            this.$modal.msgSuccess('保存成功')
            this.onClose();
          })
        } else {
          this.formData.parentId = this.check.id
          addLibschpricetree(this.formData).then(res => {
            this.$modal.msgSuccess('保存成功')
            this.onClose();
          })
        }
      })
    }
  },
};
</script>
<style></style>
<style lang="scss" scoped>
@import "@/assets/styles/business.scss";
</style>
