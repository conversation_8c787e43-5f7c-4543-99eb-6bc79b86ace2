<template>
  <div class="app-container">
    <el-row :gutter="20">
      <!--部门数据-->
      <el-col :span="deptNav ? 5 : 0" :xs="24" class="leftDiv">
        <!--折叠图标-->
        <div class="leftIcon" @click="deptNav = false">
          <span class="el-icon-caret-left"></span>
        </div>
        <div class="head-container">
          <el-input
            v-model="deptName"
            placeholder="部门名称"
            clearable
            size="small"
            prefix-icon="el-icon-search"
            style="margin-bottom: 20px"
          />
        </div>
        <div class="head-container" style="width: 300px">
          <el-tree
            :data="deptOptions"
            :props="defaultProps"
            :expand-on-click-node="false"
            :filter-node-method="filterNode"
            :default-expanded-keys="[1]"
            ref="tree"
            node-key="id"
            highlight-current
            @node-click="handleNodeClick"
          />
        </div>
      </el-col>
      <!--角色数据-->
      <el-col :span="deptNav ? 19 : 24" :xs="24">
        <!--展开图标-->
        <div class="rightIcon" @click="deptNav = true" v-show="!deptNav">
          <span class="el-icon-caret-right"></span>
        </div>
        <el-row>
          <el-col :span="24">
            <el-form
              :model="queryParams"
              ref="queryForm"
              size="small"
              :inline="true"
              label-width="68px"
            >
              <el-form-item>
                <el-button
                  type="primary"
                  @click="deptNav = !deptNav"
                  :icon="deptNavIcon"
                  size="mini"
                >
                  <span v-show="deptNav">折叠</span>
                  <span v-show="!deptNav">展开</span>
                </el-button>
              </el-form-item>

              <el-form-item label="" prop="roleName">
                <el-input
                  v-model="queryParams.roleName"
                  placeholder="角色名称"
                  clearable
                  prefix-icon="el-icon-user"
                  style="width: 240px"
                  @keyup.enter.native="handleQuery"
                />
              </el-form-item>
              <el-form-item label="" prop="roleKey">
                <el-input
                  v-model="queryParams.roleKey"
                  placeholder="权限字符"
                  clearable
                  style="width: 240px"
                  @keyup.enter.native="handleQuery"
                />
              </el-form-item>
              <el-form-item label="" prop="status">
                <el-select
                  v-model="queryParams.status"
                  placeholder="状态"
                  clearable
                  style="width: 240px"
                >
                  <el-option
                    v-for="dict in dict.type.sys_normal_disable"
                    :key="dict.value"
                    :label="dict.label"
                    :value="dict.value"
                  />
                </el-select>
              </el-form-item>
              <el-form-item>
                <el-button
                  type="primary"
                  icon="el-icon-search"
                  size="mini"
                  @click="handleQuery"
                >搜索
                </el-button
                >
                <el-button
                  icon="el-icon-refresh"
                  size="mini"
                  @click="resetQuery"
                >重置
                </el-button
                >
                <el-button
                  v-show="!showSearch"
                  @click="showSearch = true"
                  icon="el-icon-arrow-down"
                  circle
                ></el-button>
                <el-button
                  v-show="showSearch"
                  @click="showSearch = false"
                  icon="el-icon-arrow-up"
                  circle
                ></el-button>
              </el-form-item>
              <el-form-item v-show="showSearch" label="创建时间">
                <el-date-picker
                  v-model="dateRange"
                  style="width: 240px"
                  value-format="yyyy-MM-dd"
                  type="daterange"
                  range-separator="-"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                ></el-date-picker>
              </el-form-item>
            </el-form>
          </el-col>
        </el-row>

        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button
              type="primary"
              icon="el-icon-plus"
              size="mini"
              @click="handleAdd"
              v-hasPermi="['system:role:add']"
            >新增
            </el-button
            >
          </el-col>
          <el-col :span="1.5">
            <el-button
              type="success"
              icon="el-icon-edit"
              size="mini"
              :disabled="single"
              @click="handleUpdate"
              v-hasPermi="['system:role:edit']"
            >修改
            </el-button
            >
          </el-col>
          <el-col :span="1.5">
            <el-button
              type="danger"
              icon="el-icon-delete"
              size="mini"
              :disabled="multiple"
              @click="handleDelete"
              v-hasPermi="['system:role:remove']"
            >删除
            </el-button
            >
          </el-col>
          <el-col :span="1.5">
            <el-button
              type="warning"
              icon="el-icon-download"
              size="mini"
              @click="handleExport"
              v-hasPermi="['system:role:export']"
            >导出
            </el-button
            >
          </el-col>
          <right-toolbar
            :showSearch.sync="showSearch"
            @queryTable="getList"
          ></right-toolbar>
        </el-row>

        <div class="tableDiv">
          <el-table
            v-adjust-table
            size="mini"
            :height="showSearch ? 'calc(100vh - 320px)' : 'calc(100vh - 260px)'"
            border
            style="width: 100%"
            v-loading="loading"
            :data="roleList"
            @selection-change="handleSelectionChange"
            ref="table"
            :row-style="rowStyle"
            @row-click="handleRowClick"
          >
            <el-table-column fixed type="selection" width="55" align="center"/>
            <el-table-column label="角色编号" prop="roleId" min-width="120"/>
            <el-table-column
              label="角色名称"
              prop="roleName"
              :show-overflow-tooltip="true"
              min-width="150"
            />
            <el-table-column
              label="权限字符"
              prop="roleKey"
              :show-overflow-tooltip="true"
              min-width="150"
            />
            <el-table-column label="显示顺序" prop="roleSort" width="100"/>
            <el-table-column label="状态" align="center" width="100">
              <template slot-scope="scope">
                <el-switch
                  v-model="scope.row.status"
                  active-value="0"
                  inactive-value="1"
                  @change="handleStatusChange(scope.row)"
                ></el-switch>
              </template>
            </el-table-column>
            <el-table-column
              label="创建时间"
              align="center"
              prop="createTime"
              min-width="180"
            >
              <template slot-scope="scope">
                <span>{{ parseTime(scope.row.createTime) }}</span>
              </template>
            </el-table-column>
            <el-table-column
              fixed="right"
              label="操作"
              align="center"
              class-name="small-padding fixed-width"
              min-width="180"
            >
              <template slot-scope="scope" v-if="scope.row.roleId !== 1">
                <el-button
                  size="mini"
                  type="text"
                  icon="el-icon-edit"
                  @click="handleUpdate(scope.row)"
                  v-hasPermi="['system:role:edit']"
                >修改
                </el-button
                >
                <el-button
                  size="mini"
                  type="text"
                  icon="el-icon-delete"
                  @click="handleDelete(scope.row)"
                  v-hasPermi="['system:role:remove']"
                >删除
                </el-button
                >
                <el-dropdown
                  size="mini"
                  @command="(command) => handleCommand(command, scope.row)"
                  v-hasPermi="['system:role:edit']"
                >
                  <el-button
                    size="mini"
                    type="text"
                    icon="el-icon-d-arrow-right"
                  >更多
                  </el-button
                  >
                  <el-dropdown-menu slot="dropdown">
                    <el-dropdown-item
                      command="handleDataScope"
                      icon="el-icon-circle-check"
                      v-hasPermi="['system:role:edit']"
                    >数据权限
                    </el-dropdown-item
                    >
                    <el-dropdown-item
                      command="handleAuthUser"
                      icon="el-icon-user"
                      v-hasPermi="['system:role:edit']"
                    >分配用户
                    </el-dropdown-item
                    >
                  </el-dropdown-menu>
                </el-dropdown>
              </template>
            </el-table-column>
          </el-table>
        </div>
        <pagination
          v-show="total > 0"
          :total="total"
          :page.sync="queryParams.pageNum"
          :limit.sync="queryParams.pageSize"
          @pagination="getList"
        />
      </el-col>
    </el-row>

    <!-- 添加或修改角色配置对话框 -->
    <el-dialog
      :title="title"
      :visible.sync="open"
      append-to-body
      destroy-on-close
      :close-on-click-modal="false"
    >
      <div class="add-or-edit">
        <el-form ref="form" :model="form" :rules="rules" label-width="100px">
          <el-form-item label="角色名称" prop="roleName">
            <el-input v-model="form.roleName" placeholder="请输入角色名称" @change="onRoleNameChange"/>
          </el-form-item>
          <el-form-item prop="roleKey">
            <span slot="label">
              <el-tooltip
                content="控制器中定义的权限字符，如：@PreAuthorize(`@ss.hasRole('admin')`)"
                placement="top"
              >
                <i class="el-icon-question"></i>
              </el-tooltip>
              权限字符
            </span>
            <el-input v-model="form.roleKey" readonly placeholder="根据角色名称自动生成"/>
          </el-form-item>
          <el-row>
            <el-col :span="6">
              <el-form-item label="角色顺序" prop="roleSort">
                <el-input-number
                  v-model="form.roleSort"
                  controls-position="right"
                  :min="0"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="状态">
                <el-radio-group v-model="form.status">
                  <el-radio
                    v-for="dict in dict.type.sys_normal_disable"
                    :key="dict.value"
                    :label="dict.value"
                  >{{ dict.label }}
                  </el-radio
                  >
                </el-radio-group>
              </el-form-item>
            </el-col>
          </el-row>


          <el-form-item label="归属部门" prop="deptId">
            <treeselect
              v-model="form.deptId"
              :options="deptOptions"
              :show-count="true"
              placeholder="请选择归属部门"
            />
          </el-form-item>
          <el-form-item label="菜单权限">
            <el-checkbox
              v-model="menuExpand"
              @change="handleCheckedTreeExpand($event, 'menu')"
            >展开/折叠
            </el-checkbox
            >
            <el-checkbox
              v-model="menuNodeAll"
              @change="handleCheckedTreeNodeAll($event, 'menu')"
            >全选/全不选
            </el-checkbox
            >
            <el-checkbox
              v-model="form.menuCheckStrictly"
              @change="handleCheckedTreeConnect($event, 'menu')"
            >父子联动
            </el-checkbox
            >
            <el-tree
              class="tree-border"
              :data="menuOptions"
              show-checkbox
              ref="menu"
              node-key="id"
              :check-strictly="!form.menuCheckStrictly"
              empty-text="加载中，请稍候"
              :props="defaultProps"
            ></el-tree>
          </el-form-item>
          <el-form-item label="APP菜单权限">
            <el-checkbox
              v-model="menuAppExpand"
              @change="handleCheckedTreeExpand($event, 'menuApp')"
            >展开/折叠
            </el-checkbox
            >
            <el-checkbox
              v-model="menuAppNodeAll"
              @change="handleCheckedTreeNodeAll($event, 'menuApp')"
            >全选/全不选
            </el-checkbox
            >
            <el-checkbox
              v-model="form.menuAppCheckStrictly"
              @change="handleCheckedTreeConnect($event, 'menuApp')"
            >父子联动
            </el-checkbox
            >
            <el-tree
              class="tree-border"
              :data="menuAppOptions"
              show-checkbox
              ref="menuApp"
              node-key="id"
              :check-strictly="!form.menuAppCheckStrictly"
              empty-text="加载中，请稍候"
              :props="defaultProps"
            ></el-tree>
          </el-form-item>
          <el-form-item label="备注">
            <el-input
              v-model="form.remark"
              type="textarea"
              placeholder="请输入内容"
            ></el-input>
          </el-form-item>
        </el-form>

      </div>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>

        <el-button type="success" @click="showRoleCopy = true" style="float: right">权限复制</el-button>
      </div>
    </el-dialog>

    <!-- 分配角色数据权限对话框 -->
    <el-dialog
      :title="title"
      :visible.sync="openDataScope"
      width="500px"
      append-to-body
    >
      <el-form :model="form" label-width="80px">
        <el-form-item label="角色名称">
          <el-input v-model="form.roleName" :disabled="true"/>
        </el-form-item>
        <el-form-item label="权限字符">
          <el-input v-model="form.roleKey" :disabled="true"/>
        </el-form-item>
        <el-form-item label="权限范围">
          <el-select v-model="form.dataScope" @change="dataScopeSelectChange">
            <el-option
              v-for="item in dataScopeOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="数据权限" v-show="form.dataScope == 2">
          <el-checkbox
            v-model="deptExpand"
            @change="handleCheckedTreeExpand($event, 'dept')"
          >展开/折叠
          </el-checkbox
          >
          <el-checkbox
            v-model="deptNodeAll"
            @change="handleCheckedTreeNodeAll($event, 'dept')"
          >全选/全不选
          </el-checkbox
          >
          <el-checkbox
            v-model="form.deptCheckStrictly"
            @change="handleCheckedTreeConnect($event, 'dept')"
          >父子联动
          </el-checkbox
          >
          <el-tree
            class="tree-border"
            :data="deptOptions"
            show-checkbox
            default-expand-all
            ref="dept"
            node-key="id"
            :check-strictly="!form.deptCheckStrictly"
            empty-text="加载中，请稍候"
            :props="defaultProps"
          ></el-tree>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitDataScope">确 定</el-button>
        <el-button @click="cancelDataScope">取 消</el-button>
      </div>
    </el-dialog>

    <!--    角色复制-->
    <el-dialog
      title="权限复制"
      :visible.sync="showRoleCopy"
      append-to-body
    >
      <el-form :model="form" label-width="80px" style="height: 50vh">
        <el-form-item label="源角色">

          <el-select
            v-model="roleCopyForm.source"
            placeholder="请选择角色"
            multiple
            filterable
            clearable
            style="width: 100%"
          >
            <el-option
              v-for="dict in roleCopyForm.array"
              :key="dict.roleId"
              :label="dict.roleName"
              :value="dict.roleId"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="复制方式">
          <el-radio-group v-model="roleCopyForm.type">
            <el-radio label="1">追加</el-radio>
            <el-radio label="2">覆盖</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="复制范围">
          <el-checkbox-group v-model="roleCopyForm.scoped">
            <el-checkbox label="1">系统菜单权限</el-checkbox>
            <el-checkbox label="2">app菜单权限</el-checkbox>
          </el-checkbox-group>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="onCopy">确 定</el-button>
        <el-button @click="showRoleCopy = false">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  listRole,
  getRole,
  delRole,
  addRole,
  updateRole,
  dataScope,
  changeRoleStatus,
  deptTreeSelect,
} from "@/api/system/role";
import {
  treeselect as menuTreeselect,
  roleMenuTreeselect,
} from "@/api/system/menu";
import {deptTreeSelect as deptTreeSelectDef} from "@/api/system/user";
import Treeselect from "@riophae/vue-treeselect";
import "@riophae/vue-treeselect/dist/vue-treeselect.css";
import pinyin from "js-pinyin";

export default {
  name: "Role",
  dicts: ["sys_normal_disable"],
  components: {Treeselect},
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: false,
      showRoleCopy: false,
      // 左侧组织树
      deptNav: true,
      // 总条数
      total: 0,
      // 角色表格数据
      roleList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 是否显示弹出层（数据权限）
      openDataScope: false,
      menuExpand: false,
      menuNodeAll: false,
      menuAppExpand: false,
      menuAppNodeAll: false,
      deptExpand: true,
      deptNodeAll: false,
      // 日期范围
      dateRange: [],
      // 数据范围选项
      dataScopeOptions: [
        {
          value: "1",
          label: "全部数据权限",
        },
        {
          value: "2",
          label: "自定数据权限",
        },
        {
          value: "3",
          label: "本部门数据权限",
        },
        {
          value: "4",
          label: "本部门及以下数据权限",
        },
        {
          value: "5",
          label: "仅本人数据权限",
        },
      ],
      roleCopyForm: {
        source: [],
        type: '1',
        scoped: ['1','2'],
        array: []
      },
      // 菜单列表
      menuOptions: [],
      // APP菜单列表
      menuAppOptions: [],
      // 部门列表
      deptOptions: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        roleName: undefined,
        roleKey: undefined,
        status: undefined,
      },
      // 表单参数
      form: {},
      defaultProps: {
        children: "children",
        label: "label",
      },
      // 表单校验
      rules: {
        roleName: [
          {required: true, message: "角色名称不能为空", trigger: "blur"},
        ],
        // roleKey: [
        //   {required: true, message: "权限字符不能为空", trigger: "blur"},
        // ],
        roleSort: [
          {required: true, message: "角色顺序不能为空", trigger: "blur"},
        ],
      },
      // 部门名称
      deptName: undefined,
    };
  },
  created() {
    this.getList();
    this.getRole();
    this.getDeptTreeDef();
  },
  watch: {
    // 根据名称筛选部门树
    deptName(val) {
      this.$refs.tree.filter(val);
    },
  },
  computed: {
    deptNavIcon() {
      return this.deptNav ? "el-icon-arrow-left" : "el-icon-arrow-right";
    },
  },
  methods: {
    onCopy() {
      for (let roleId of this.roleCopyForm.source) {
        if (this.roleCopyForm.scoped.includes('1')) {
          const roleMenu = roleMenuTreeselect(roleId, 0)
          if (this.roleCopyForm.type === '2') {
            roleMenu.then(res=>{
              this.menuOptions = res.menus;
            })

          }
          roleMenu.then((res) => {
            let checkedKeys = res.checkedKeys;
            checkedKeys.forEach((v) => {
              this.$nextTick(() => {
                this.$refs.menu.setChecked(v, true, false);
              });
            });
          });
        }
        if (this.roleCopyForm.scoped.includes('2')){
          const roleAppMenu = roleMenuTreeselect(roleId, 1)
          if (this.roleCopyForm.type === '2'){
            roleAppMenu.then((res) => {
              this.menuAppOptions = res.menus;
            });
          }
          this.$nextTick(() => {
            roleAppMenu.then((res) => {
              let checkedKeys = res.checkedKeys;
              checkedKeys.forEach((v) => {
                this.$nextTick(() => {
                  this.$refs.menuApp.setChecked(v, true, false);
                });
              });
            });
          });
        }
      }
      this.roleCopyForm.source = []
      this.roleCopyForm.type = '1'
      this.roleCopyForm.scoped =  ['1','2']
      this.showRoleCopy = false
    },
    getRole() {
      listRole(
        {
          pageNum: 1,
          pageSize: 1000000,
          roleName: undefined,
          roleKey: undefined,
          status: undefined,
        }
      ).then(
        (response) => {
          this.roleCopyForm.array = response.rows
        }
      );
    },
    onRoleNameChange(name) {
      let pinyin = require('js-pinyin');
      pinyin.setOptions({checkPolyphone: false, charCase: 2});
      let flagStr = pinyin.getCamelChars(name || this.form.roleName)
      if (this.title.substring(0, 2) === "添加") {

        const regex = /[a-zA-Z0-9]+/g;
        let alphanumeric = flagStr.match(regex);
        this.form.roleKey = alphanumeric + Math.floor(Math.random() * (999 - 100 + 1))
      }
   },
    // onRoleNameChange(name) {
    //   let pinyin = require('js-pinyin');
    //   pinyin.setOptions({checkPolyphone: false, charCase: 1});
    //   let flagStr = pinyin.getCamelChars(name || this.form.roleName)
    //   if (this.title.substring(0, 2) === "添加") {
    //     listRole(
    //       {
    //         pageNum: 1,
    //         pageSize: 1000000,
    //         roleName: undefined,
    //         roleKey: undefined,
    //         status: undefined,
    //       }
    //     ).then(
    //       (response) => {
    //         getRoleKey(flagStr, response.rows.map(i => i.roleKey), 1)
    //         this.form.roleKey = flagStr
    //       }
    //     );
    //   }
    //   function getRoleKey(tempKey, keyArray, num = 1) {
    //     if (keyArray.includes(tempKey)) {
    //       getRoleKey(tempKey + num, keyArray, num + 1)
    //     } else {
    //       flagStr = tempKey
    //     }
    //   }
    // },
    /** 查询角色列表 */
    getList() {
      this.loading = true;
      listRole(this.addDateRange(this.queryParams, this.dateRange)).then(
        (response) => {
          this.roleList = response.rows;
          this.total = response.total;
          this.loading = false;
        }
      );
    },
    // 表格点击勾选
    handleRowClick(row) {
      row.isSelected = !row.isSelected;
      this.$refs.table.toggleRowSelection(row);
    },
    // 勾选高亮
    rowStyle({row, rowIndex}) {
      if (this.ids.includes(row.id)) {
        return {'background-color': '#b7daff', color: '#333'}
      } else {
        return {'background-color': '#fff', color: '#333'}
      }
    },
    /** 查询菜单树结构 */
    getMenuTreeselect() {
      menuTreeselect({isApp: 0}).then((response) => {
        this.menuOptions = response.data;
      });
      menuTreeselect({isApp: 1}).then((response) => {
        this.menuAppOptions = response.data;
      });
    },
    // 所有菜单节点数据
    getMenuAllCheckedKeys(isApp) {
      let checkedKeys;
      let halfCheckedKeys;
      if (isApp == 0) {
        // 目前被选中的菜单节点
        checkedKeys = this.$refs.menu.getCheckedKeys();
        // 半选中的菜单节点
        halfCheckedKeys = this.$refs.menu.getHalfCheckedKeys();
      } else {
        // 目前被选中的菜单节点
        checkedKeys = this.$refs.menuApp.getCheckedKeys();
        // 半选中的菜单节点
        halfCheckedKeys = this.$refs.menuApp.getHalfCheckedKeys();
      }
      checkedKeys.unshift.apply(checkedKeys, halfCheckedKeys);
      return checkedKeys;
    },
    // 所有部门节点数据
    getDeptAllCheckedKeys() {
      // 目前被选中的部门节点
      let checkedKeys = this.$refs.dept.getCheckedKeys();
      // 半选中的部门节点
      let halfCheckedKeys = this.$refs.dept.getHalfCheckedKeys();
      checkedKeys.unshift.apply(checkedKeys, halfCheckedKeys);
      return checkedKeys;
    },
    /** 根据角色ID查询菜单树结构 */
    getRoleMenuTreeselect(roleId, isApp) {
      return roleMenuTreeselect(roleId, isApp).then((response) => {
        if (isApp == 0) {
          this.menuOptions = response.menus;
        } else {
          this.menuAppOptions = response.menus;
        }
        return response;
      });
    },
    /** 根据角色ID查询部门树结构 */
    getDeptTree(roleId) {
      return deptTreeSelect(roleId).then((response) => {
        this.deptOptions = response.depts;
        return response;
      });
    },
    // 角色状态修改
    handleStatusChange(row) {
      let text = row.status === "0" ? "启用" : "停用";
      this.$modal
        .confirm('确认要"' + text + '""' + row.roleName + '"角色吗？')
        .then(function () {
          return changeRoleStatus(row.roleId, row.status);
        })
        .then(() => {
          this.$modal.msgSuccess(text + "成功");
        })
        .catch(function () {
          row.status = row.status === "0" ? "1" : "0";
        });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 取消按钮（数据权限）
    cancelDataScope() {
      this.openDataScope = false;
      this.reset();
    },
    // 表单重置
    reset() {
      if (this.$refs.menu != undefined) {
        this.$refs.menu.setCheckedKeys([]);
      }
      if (this.$refs.menuApp != undefined) {
        this.$refs.menuApp.setCheckedKeys([]);
      }
      (this.menuExpand = false),
        (this.menuNodeAll = false),
        (this.menuAppExpand = false),
        (this.menuAppNodeAll = false),
        (this.deptExpand = true),
        (this.deptNodeAll = false),
        (this.form = {
          roleId: undefined,
          roleName: undefined,
          roleKey: undefined,
          roleSort: 0,
          status: "0",
          menuIds: [],
          deptIds: [],
          menuCheckStrictly: true,
          menuAppCheckStrictly: true,
          deptCheckStrictly: true,
          remark: undefined,
        });
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dateRange = [];
      this.resetForm("queryForm");
      this.queryParams.deptId = undefined;
      this.$refs.tree.setCurrentKey(null);
      this.handleQuery();
    },
    /** 查询部门下拉树结构 */
    getDeptTreeDef() {
      deptTreeSelectDef().then((response) => {
        this.deptOptions = response.data;
      });
    },
    // 筛选节点
    filterNode(value, data) {
      if (!value) return true;
      return data.label.indexOf(value) !== -1;
    },
    // 节点单击事件
    handleNodeClick(data) {
      this.queryParams.deptId = data.id;
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.roleId);
      this.single = selection.length != 1;
      this.multiple = !selection.length;
    },
    // 更多操作触发
    handleCommand(command, row) {
      switch (command) {
        case "handleDataScope":
          this.handleDataScope(row);
          break;
        case "handleAuthUser":
          this.handleAuthUser(row);
          break;
        default:
          break;
      }
    },
    // 树权限（展开/折叠）
    handleCheckedTreeExpand(value, type) {
      if (type == "menu") {
        let treeList = this.menuOptions;
        for (let i = 0; i < treeList.length; i++) {
          this.$refs.menu.store.nodesMap[treeList[i].id].expanded = value;
        }
      } else if (type == "menuApp") {
        let treeList = this.menuAppOptions;
        for (let i = 0; i < treeList.length; i++) {
          this.$refs.menuApp.store.nodesMap[treeList[i].id].expanded = value;
        }
      } else if (type == "dept") {
        let treeList = this.deptOptions;
        for (let i = 0; i < treeList.length; i++) {
          this.$refs.dept.store.nodesMap[treeList[i].id].expanded = value;
        }
      }
    },
    // 树权限（全选/全不选）
    handleCheckedTreeNodeAll(value, type) {
      if (type == "menu") {
        this.$refs.menu.setCheckedNodes(value ? this.menuOptions : []);
      } else if (type == "menuApp") {
        this.$refs.menuApp.setCheckedNodes(value ? this.menuAppOptions : []);
      } else if (type == "dept") {
        this.$refs.dept.setCheckedNodes(value ? this.deptOptions : []);
      }
    },
    // 树权限（父子联动）
    handleCheckedTreeConnect(value, type) {
      if (type == "menu") {
        this.form.menuCheckStrictly = value ? true : false;
      } else if (type == "menuApp") {
        this.form.menuAppCheckStrictly = value ? true : false;
      } else if (type == "dept") {
        this.form.deptCheckStrictly = value ? true : false;
      }
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.getMenuTreeselect();
      this.open = true;
      this.title = "添加角色";
      this.form.deptId = this.queryParams.deptId;
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const roleId = row.roleId || this.ids;
      const roleMenu = this.getRoleMenuTreeselect(roleId, 0);
      const roleAppMenu = this.getRoleMenuTreeselect(roleId, 1);
      getRole(roleId).then((response) => {
        this.form = response.data;
        this.open = true;
        this.$nextTick(() => {
          roleMenu.then((res) => {
            let checkedKeys = res.checkedKeys;
            checkedKeys.forEach((v) => {
              this.$nextTick(() => {
                this.$refs.menu.setChecked(v, true, false);
              });
            });
          });
          roleAppMenu.then((res) => {
            let checkedKeys = res.checkedKeys;
            checkedKeys.forEach((v) => {
              this.$nextTick(() => {
                this.$refs.menuApp.setChecked(v, true, false);
              });
            });
          });
        });
        this.title = "修改角色";
      });
    },
    /** 选择角色权限范围触发 */
    dataScopeSelectChange(value) {
      if (value !== "2") {
        this.$refs.dept.setCheckedKeys([]);
      }
    },
    /** 分配数据权限操作 */
    handleDataScope(row) {
      this.reset();
      const deptTreeSelect = this.getDeptTree(row.roleId);
      getRole(row.roleId).then((response) => {
        this.form = response.data;
        this.openDataScope = true;
        this.$nextTick(() => {
          deptTreeSelect.then((res) => {
            this.$refs.dept.setCheckedKeys(res.checkedKeys);
          });
        });
        this.title = "分配数据权限";
      });
    },
    /** 分配用户操作 */
    handleAuthUser: function (row) {
      const roleId = row.roleId;
      this.$router.push("/system/role-auth/user/" + roleId);
    },
    /** 提交按钮 */
    submitForm: function () {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          if (this.form.roleId != undefined) {
            this.form.menuIds = this.getMenuAllCheckedKeys(0);
            this.form.menuAppIds = this.getMenuAllCheckedKeys(1);
            updateRole(this.form).then((response) => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            this.form.menuIds = this.getMenuAllCheckedKeys(0);
            this.form.menuAppIds = this.getMenuAllCheckedKeys(1);
            addRole(this.form).then((response) => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 提交按钮（数据权限） */
    submitDataScope: function () {
      if (this.form.roleId != undefined) {
        this.form.deptIds = this.getDeptAllCheckedKeys();
        dataScope(this.form).then((response) => {
          this.$modal.msgSuccess("修改成功");
          this.openDataScope = false;
          this.getList();
        });
      }
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const roleIds = row.roleId || this.ids;
      this.$modal
        .confirm('是否确认删除角色编号为"' + roleIds + '"的数据项？')
        .then(function () {
          return delRole(roleIds);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => {
        });
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download(
        "system/role/export",
        {
          ...this.queryParams,
        },
        `role_${new Date().getTime()}.xlsx`
      );
    },
  },
};
</script>
<style>
.hasTagsView .app-main[data-v-078753dd] {
  background: #f5f7fa;
}

.leftDiv {
  border-right: 1px solid #d8dce5;
  min-height: calc(100vh - 100px);
  overflow-y: auto;
  height: calc(80vh - 150px);
  position: relative;
  top: -20px;
  padding-top: 10px;
  background-color: white;
}

.tableDiv {
  background-color: white;
  padding-bottom: 10px;
}

.leftIcon {
  border: 1px solid #dcdfe6;
  border-radius: 8px;
  width: 16px;
  height: 50px;
  line-height: 50px;
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
  position: absolute;
  right: 0;
  top: 300px;
  z-index: 2;
}

.leftIcon:hover {
  background-color: #dcdfe6;
}

.rightIcon {
  border: 1px solid #dcdfe6;
  border-radius: 8px;
  width: 16px;
  height: 50px;
  line-height: 50px;
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
  position: absolute;
  left: -10px;
  top: 280px;
  z-index: 10;
  background: white;
}

.rightIcon:hover {
  background-color: #dcdfe6;
}

.el-dialog__body {
  max-height: 75vh; /* 设置一个最大高度 */
  overflow-y: auto; /* 超出部分显示滚动条 */
  padding: 15px 30px;
}
</style>
