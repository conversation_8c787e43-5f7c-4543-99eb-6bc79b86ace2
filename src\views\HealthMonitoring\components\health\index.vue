<template>
	<div class="bridge-special">
		<div class="special-c" v-loading="loading" element-loading-background="rgba(0, 0, 0, 0.3)">
			<AMap ref="ampRef" @mapLoaded="afterLoaded"></AMap>
		</div>
		<section class="special-m">
			<CockpitCard :isHeader="false" w="100%" h="100%" :class="isBig ? 'mb-2' : 'mb-3'" :isDtl="false">
				<div class="content">
					<div class="title-box title-box-b"  :class="{ 'highlight': activeFilter ==null}" @click="UpdateActiveFilter(null)">
						<span>总数</span>
						<!-- 	<span style="font-weight: bold">&nbsp;{{ statisticsData.all }}</span> -->
						<span style="font-weight: bold">&nbsp;137</span>
					</div>
					<div class="title-box title-box-y" :class="{ 'highlight': activeFilter =='status'}" @click="UpdateActiveFilter('status')">
						<audio class="warning-audio" ref="warningAudio" src="@/assets/other/warning.mp3" preload="auto"
							loop></audio>
						<span>超限警告</span>
						<span style="font-weight: bold">&nbsp;{{ statisticsData.warning }}</span>
					</div>
					<div class="title-box title-box-r" :class="{ 'highlight': activeFilter =='planStatus'}" @click="UpdateActiveFilter('planStatus')">
						<span>正在预警</span>
						<span style="font-weight: bold">&nbsp;{{ statisticsData.abnormal }}</span>
					</div>

					<!-- 					<div class="title-box title-box-g">
						<span>爆闪掉线</span>
						<span style="font-weight: bold">
							&nbsp;{{ statisticsData.disconnect }}
						</span>
					</div> -->
				</div>
			</CockpitCard>
		</section>
		<section class="special-l">
			<CockpitCard title="监测结构物统计" :w="isBig ? '10vw' : '20vw'" h="calc(10vh - 5px)"
				:class="isBig ? 'mb-2' : 'mb-3'" :isDtl="false">
				<fitness-index type="2" @click="onFClick" top="3vh" :isAct="true"
					:structure="healthData.structureType"></fitness-index>
			</CockpitCard>
			<CockpitCard title="分布情况" :w="isBig ? '10vw' : '20vw'" h="calc(40vh)" :class="isBig ? 'mb-2' : 'mb-3'"
				:isDtl="false">
				<Echarts :option="dbOption" v-if="dbOption" height="40vh" key="jkKey" v-loading="fLoading"
					element-loading-background="rgba(0, 0, 0, 0.4)" @click="onEChartsClick" />
			</CockpitCard>
			<!-- <CockpitCard title="结构物基本信息" :w="isBig ? '10vw' : '20vw'" h="calc(19vh - 5px)" :isDtl="false">
        <div class="base-info">
          <vue-seamless-scroll :data="structureData" :class-option="optionHover" class="seamless-warp">
            <div v-for="item in structureData" :key="item.id">
              <div class="info-item">
                <img :src="item.structureImage" class="img" />
                <div class="info-data">
                  <span>{{ item.structureName }}</span>
                  <span>主桥上部结构类型：{{ item.ustructType }}</span>
                  <span>技术状况：{{ item.level }}</span>
                  <span>按跨径分类：{{ item.structureType }}</span>
                </div>
                <img src="@/assets/cockpit/addres-info.png" class="img-icon" @click="setPosition(item)">
              </div>
              <div class="divider"></div>
            </div>
          </vue-seamless-scroll>
        </div>
      </CockpitCard> -->
			<CockpitCard title="结构物清单" :w="isBig ? '10vw' : '20vw'" :h="isBig ? '28vh' : 'calc(28vh - 5px)'"
				:class="isBig ? 'mb-2' : 'mb-3'" :isDtl="false">
				<!-- <Echarts :option="levelOption" v-if="levelOption" height="25vh" key="jkKey" /> -->
				<el-input v-model="selectKey" placeholder="输入内容以搜索" class="custom-input"></el-input>
				<StructureList ref="struRef" :structure="filteredList" :searchAll="searchAll" :structureType="name"
					@row-click="onTableClick" />
			</CockpitCard>
		</section>
		<section class="special-r">
			<div class="info">
				<!-- <div>
          <CockpitCard title="健康度等级" :w="isBig ? '9vw' : '18vw'" h="calc(26vh - 5px)" :class="isBig ? 'mb-2' : 'mb-3'"
            :isDtl="false">
            <Echarts :option="levelOption" v-if="levelOption" height="25vh" key="jkKey" />
          </CockpitCard>
          <CockpitCard title="报警状态" :w="isBig ? '9vw' : '18vw'" h="calc(26vh - 5px)" :class="isBig ? 'mb-2' : 'mb-3'"
            :isDtl="false">
            <div class="warning-status">
              <div class="warning-status-list" v-for="(item, index) in warningList" :key="'warning' + index">
                <img :src="item.img" class="img" />
                <span>{{ item.name }}</span>
              </div>
            </div>
            <div class="warning-content">
              <vue-seamless-scroll :data="warningData" :class-option="optionHover" class="seamless-warp">
                <div class="warning-content-list" v-for="(item, index) in warningData" :key="index">
                  <span class="warning-content-id">{{ index + 1 }}</span>
                  <span class="warning-content-val">{{ item.alertContent }}</span>
                </div>
              </vue-seamless-scroll>
            </div>
          </CockpitCard>
          <CockpitCard title="监测类别" :w="isBig ? '9vw' : '18vw'" h="calc(26vh - 5px)" :isDtl="false">
            <div class="monitoring-category">
              <div class="monitoring-category-list" v-for="(item, index) in categoryList" :key="'category' + index">
                <img :src="item.img" class="img" />
                <span class="name" :style="{ fontSize: isBig ? '0.4vw' : '0.8vw' }">{{ item.name }}</span>
                <span class="val" :style="{ fontSize: isBig ? '0.4vw' : '0.8vw' }">({{ item.val }})</span>
              </div>
            </div>
          </CockpitCard>
        </div> -->


				<!-- 
        <div class="ml-2">
          <CockpitCard title="监测类别" :w="isBig ? '9vw' : '18vw'" h="calc(26vh - 5px)" :isDtl="false">
            <div class="monitoring-category">
              <div class="monitoring-category-list" v-for="(item, index) in categoryList" :key="'category' + index">
                <img :src="item.img" class="img" />
                <span class="name" :style="{ fontSize: isBig ? '0.4vw' : '0.8vw' }">{{ item.name }}</span>
                <span class="val" :style="{ fontSize: isBig ? '0.4vw' : '0.8vw' }">({{ item.val }})</span>
              </div>
            </div>
          </CockpitCard> -->


				<div class="ml-2">
					<CockpitCard title="风险类型" :w="isBig ? '9vw' : '18vw'" h="calc(24vh -5px)" :isDtl="false"
						style="margin-bottom: 10px;">
						<div class="monitoring-category">
							<div class="monitoring-category-list" @click="toggleSelection(riseType.value)"
								v-for=" (riseType, index)  in healthData.monitorType">
								<img :src="categoryList[index].img" class="img" />
								<span class="name"
									:style="{ fontSize: isBig ? '0.4vw' : '0.8vw' }">{{riseType.monitorType}}</span>
								<span class="val"
									:style="{ fontSize: isBig ? '0.4vw' : '0.8vw' }">({{riseType.count}})</span>
							</div>

						</div>
					</CockpitCard>



					<CockpitCard title="风险等级" :w="isBig ? '9vw' : '18vw'" h="calc(5vh - 5px)" :isDtl="false"
						style="margin-bottom: 10px;">
						<div class="monitoring-category">
							<div class="monitoring-category-list" @click="toggleType(riseType.value)"
								v-for=" (riseType, index)  in healthData.riskLevel">
								<img :src="categoryList[index].img" class="img" />
								<span class="name"
									:style="{ fontSize: isBig ? '0.4vw' : '0.8vw' }">{{riseType.riskLevel}}</span>
								<span class="val"
									:style="{ fontSize: isBig ? '0.4vw' : '0.8vw' }">({{riseType.count}})</span>
							</div>
						</div>
					</CockpitCard>








					<CockpitCard title="传感器运营状态" :w="isBig ? '9vw' : '18vw'" h="calc(20vh - 5px)"
						:class="isBig ? 'mb-2' : 'mb-3'" :isDtl="false">
						<Echarts :option="cgOption" v-if="cgOption" height="20vh" key="jkKey" />
					</CockpitCard>
					<CockpitCard title="报警状态" :w="isBig ? '9vw' : '18vw'"
						:h="isBig ? 'calc(28vh + 5px)' : 'calc(29vh - 5px)'" :class="isBig ? 'mb-2' : 'mb-3'"
						:isDtl="false">
						<div class="warning-status">
							<div class="warning-status-list" v-for="(item, index) in warningList"
								:key="'warning' + index" @click="onLevelClick(item, index)">
								<img :src="item.img" class="img" />
								<span :style="{ color: warningIndex === index ? '#42ABFF' : '' }">{{ item.name }}</span>
							</div>
						</div>
						<div class="warning-content">
							<vue-seamless-scroll :data="warningData" :class-option="optionHover" class="seamless-warp">
								<div class="warning-content-list" v-for="(item, index) in warningData" :key="index">
									<span class="warning-content-id">
										<!-- {{ index + 1 }} -->
										<img src="@/assets/cockpit/health/level-1.png" class="img"
											v-if="item.alertLevel == '一级'" />
										<img src="@/assets/cockpit/health/level-2.png" class="img"
											v-if="item.alertLevel == '二级'" />
										<img src="@/assets/cockpit/health/level-3.png" class="img"
											v-if="item.alertLevel == '三级'" />
									</span>
									<span class="warning-content-val">{{ item.alertContent }}</span>
								</div>
							</vue-seamless-scroll>
						</div>
					</CockpitCard>
					<!-- <CockpitCard title="技术状况" :w="isBig ? '9vw' : '18vw'" h="calc(26vh - 5px)" :class="isBig ? 'mb-2' : 'mb-3'"
            :isDtl="false">
            <Echarts :option="jsOption" v-if="jsOption" height="25vh" key="jkKey" />
          </CockpitCard>
          <CockpitCard title="传感器运营状态" :w="isBig ? '9vw' : '18vw'" h="calc(26vh - 5px)" :class="isBig ? 'mb-2' : 'mb-3'"
            :isDtl="false">
            <Echarts :option="cgOption" v-if="cgOption" height="25vh" key="jkKey" />
          </CockpitCard>
          <CockpitCard title="监测数据统计" :w="isBig ? '9vw' : '18vw'" h="calc(26vh - 5px)" :isDtl="false">
            <Echarts :option="mlOption" v-if="mlOption" height="25vh" key="jkKey" />
            <div class="jc-data-statistics">
              <div class="statistics-info">
                <span class="info-today"></span>
                <span class="info-val">今日数据: <strong>{{ todayCount }}</strong></span>
                <span class="info-unit">条</span>
              </div>

              <div class="statistics-info">
                <span class="info-yesterday"></span>
                <span class="info-val">昨日数据: <strong>{{ ytodayCount }}</strong></span>
                <span class="info-unit">条</span>
              </div>
            </div>
          </CockpitCard> -->
				</div>
			</div>
		</section>
	</div>
</template>

<script>
	import * as echarts from 'echarts'
	import {
		isBigScreen
	} from '../../util/utils';
	import CockpitCard from '../cockpitCard.vue';
	import Echarts from '../echarts/echarts.vue';
	import Tables from "../tables.vue";
	// import MapView from '../mapView.vue';
	import AMap from '../aMap.vue';
	// import AMap from '@/views/cockpit/components/aMap.vue'
	import FitnessIndex from '../fitness/index.vue';
	import StructureList from './component/structureList.vue'
	import {
		getSensorCountKindByMonitorTypeTibet,
		getNotDealRecordInformationTibet,
		getWarningListTibet
	} from "@/api/Tibet/index";
	import {
		getAllStructureInfoGroupByDomain,
		getDataCount,
		getNotDealRecordInformation
	} from "@/api/cockpit/index";

	import {
		getHealthList
	} from "@/api/HealthMonitoring/healthMain";

	import {
		Loading
	} from 'element-ui';
	import cache from "@/plugins/cache";
	import {
		listStructure
	} from "@/api/jgjc/earlyWarning/deviceModel";

	export default {
		name: 'Special',
		dicts: ['structure_rise_grade', 'structure_rise_type'],
		components: {
			CockpitCard,
			Echarts,
			Tables,
			// MapView,
			FitnessIndex,
			AMap,
			StructureList
		},
		data() {
			return {
				activeFilter: null, //预警状态筛选
				healthData: null,
				selectKey: '',
				map: null,
				isBig: isBigScreen(),
				jsOption: null,
				mlOption: null,
				dbOption: null,
				levelOption: null, // 桥梁技术状况评定
				cgOption: null, // 传感器运营状态
				todayCount: 0,
				structureData: [],
				ytodayCount: 0,
				warningData: [],
				levelList: [],
				warningIndex: null,
				sourceData: [], // 原始数据
				refreshTimer: null, // 刷新定时器
				structureList: [], // 结构物列表
				statisticsData: {
					all: 0, // 总数
					abnormal: 0, // 正在预警
					warning: 0, // 超限警告 
					disconnect: 0, // 爆闪掉线
				},
				dept: '',
				structureType: '全部',
				warningList: [{
						name: '一级',
						color: '#F9E21F',
						img: require('@/assets/cockpit/health/level-3.png'),
					},
					{
						name: '二级',
						color: '#FF9803',
						img: require('@/assets/cockpit/health/level-2.png'),
					},
					{
						name: '三级',
						color: '#FA5151',
						img: require('@/assets/cockpit/health/level-1.png'),
					},
				],
				categoryList: [{
						name: '温湿度',
						val: 0,
						img: require('@/assets/cockpit/health/humiture.png'),
					},
					{
						name: '衬砌变形',
						val: 0,
						img: require('@/assets/cockpit/health/lining.png'),
					},
					{
						name: '裂缝',
						val: 0,
						img: require('@/assets/cockpit/health/crack.png'),
					},
					{
						name: '应变',
						val: 0,
						img: require('@/assets/cockpit/health/strain.png'),
					},
					{
						name: '倾斜',
						val: 0,
						img: require('@/assets/cockpit/health/tilt.png'),
					},
					{
						name: '体外预应力',
						val: 0,
						img: require('@/assets/cockpit/health/prestress.png'),
					},
					{
						name: '位移',
						val: 0,
						img: require('@/assets/cockpit/health/displacement.png'),
					},
					{
						name: '振动',
						val: 0,
						img: require('@/assets/cockpit/health/vibrate.png'),
					},
					{
						name: '雨量',
						val: 0,
						img: require('@/assets/cockpit/health/hyetal.png'),
					},
					{
						name: '结构温度',
						val: 0,
						img: require('@/assets/cockpit/health/temperature.png'),
					},
					{
						name: '索力',
						val: 0,
						img: require('@/assets/cockpit/health/cable-force.png'),
					},
					{
						name: '转角',
						val: 0,
						img: require('@/assets/cockpit/health/corner.png'),
					},
					{
						name: '地震',
						val: 0,
						img: require('@/assets/cockpit/health/earthquake.png'),
					},
					{
						name: '车辆荷载',
						val: 0,
						img: require('@/assets/cockpit/health/vehicle-load.png'),
					},
					{
						name: '风速、风向',
						val: 0,
						img: require('@/assets/cockpit/health/wind.png'),
					},
				],
				name: '全部',
				obj: {
					"deptIds": [],
					"id": "1821371445660356609",
					icon: require('@/assets/cockpit/bridge.png'),
					paramsDTO: {
						"precisionParams": {
							"type": "桥梁",
							"customParams": [{
									"col": "code",
									"compareSymbol": "=",
									"type": "2",
									"colValue": ""
								},
								{
									"col": "content",
									"compareSymbol": "like",
									"type": "2",
									"colValue": ""
								},
								{
									"col": "management_maintenance_name",
									"compareSymbol": "like",
									"type": "2",
									"colValue": ""
								},
								{
									"col": "maintenance_section_name",
									"compareSymbol": "like",
									"type": "2",
									"colValue": ""
								},
								{
									"col": "route_code",
									"compareSymbol": "=",
									"type": "2",
									"colValue": ""
								},
								{
									"col": "direction",
									"compareSymbol": "like",
									"type": "2",
									"colValue": ""
								},
								{
									"col": "bridge_length",
									"compareSymbol": "=",
									"dataType": "2",
									"type": "2",
									"colValue": ""
								},
								{
									"col": "zhuang_hao",
									"compareSymbol": "=",
									"dataType": "2",
									"type": "2",
									"colValue": ""
								}
							]
						},
						"ks": ""
					}
				},
				queryList: [{
						"deptIds": [],
						"id": "1821371445660356609",
						icon: require('@/assets/cockpit/bridge.png'),
						paramsDTO: {
							"precisionParams": {
								"type": "桥梁",
								"customParams": [{
										"col": "code",
										"compareSymbol": "=",
										"type": "2",
										"colValue": ""
									},
									{
										"col": "content",
										"compareSymbol": "like",
										"type": "2",
										"colValue": ""
									},
									{
										"col": "management_maintenance_name",
										"compareSymbol": "like",
										"type": "2",
										"colValue": ""
									},
									{
										"col": "maintenance_section_name",
										"compareSymbol": "like",
										"type": "2",
										"colValue": ""
									},
									{
										"col": "route_code",
										"compareSymbol": "=",
										"type": "2",
										"colValue": ""
									},
									{
										"col": "direction",
										"compareSymbol": "like",
										"type": "2",
										"colValue": ""
									},
									{
										"col": "bridge_length",
										"compareSymbol": "=",
										"dataType": "2",
										"type": "2",
										"colValue": ""
									},
									{
										"col": "zhuang_hao",
										"compareSymbol": "=",
										"dataType": "2",
										"type": "2",
										"colValue": ""
									}
								]
							},
							"ks": ""
						}
					},
					{
						"deptIds": [],
						"id": "1821371445660356609",
						icon: require('@/assets/cockpit/slope.png'),
						paramsDTO: {
							"precisionParams": {
								"type": "边坡",
								"customParams": [{
										"col": "code",
										"compareSymbol": "=",
										"type": "2",
										"colValue": ""
									},
									{
										"col": "content",
										"compareSymbol": "like",
										"type": "2",
										"colValue": ""
									},
									{
										"col": "management_maintenance_name",
										"compareSymbol": "like",
										"type": "2",
										"colValue": ""
									},
									{
										"col": "maintenance_section_name",
										"compareSymbol": "like",
										"type": "2",
										"colValue": ""
									},
									{
										"col": "route_code",
										"compareSymbol": "=",
										"type": "2",
										"colValue": ""
									},
									{
										"col": "direction",
										"compareSymbol": "like",
										"type": "2",
										"colValue": ""
									},
									{
										"col": "bridge_length",
										"compareSymbol": "=",
										"dataType": "2",
										"type": "2",
										"colValue": ""
									},
									{
										"col": "zhuang_hao",
										"compareSymbol": "=",
										"dataType": "2",
										"type": "2",
										"colValue": ""
									}
								]
							},
							"ks": ""
						}
					},
					{
						"deptIds": [],
						"id": "1821371445660356609",
						icon: require('@/assets/cockpit/tunnel-icon.png'),
						url: '/oneMap/layerData/getDataInfo',
						paramsDTO: {
							"precisionParams": {
								"type": "隧道",
								"customParams": [{
										"col": "code",
										"compareSymbol": "=",
										"type": "2",
										"colValue": ""
									},
									{
										"col": "content",
										"compareSymbol": "like",
										"type": "2",
										"colValue": ""
									},
									{
										"col": "management_maintenance_name",
										"compareSymbol": "like",
										"type": "2",
										"colValue": ""
									},
									{
										"col": "maintenance_section_name",
										"compareSymbol": "like",
										"type": "2",
										"colValue": ""
									},
									{
										"col": "route_code",
										"compareSymbol": "=",
										"type": "2",
										"colValue": ""
									},
									{
										"col": "direction",
										"compareSymbol": "like",
										"type": "2",
										"colValue": ""
									},
									{
										"col": "bridge_length",
										"compareSymbol": "=",
										"dataType": "2",
										"type": "2",
										"colValue": ""
									},
									{
										"col": "zhuang_hao",
										"compareSymbol": "=",
										"dataType": "2",
										"type": "2",
										"colValue": ""
									}
								]
							},
							"ks": ""
						}
					},
				],
				loading: true,
				fLoading: false,
				searchAll: true,
			}
		},
		computed: {
			// 滚动设置
			optionHover() {
				return {
					hoverStop: true,
					step: this.isBig ? 1.0 : 0.2, // 数值越大速度滚动越快
					limitMoveNum: 2, // 开始无缝滚动的数据量 this.dataList.length
					direction: 1, // 0向下 1向上 2向左 3向右
					openWatch: true, // 开启数据实时监控刷新dom
					singleHeight: 0, // 单步运动停止的高度(默认值0是无缝不停止的滚动) direction => 0/1
					singleWidth: 0, // 单步运动停止的宽度(默认值0是无缝不停止的滚动) direction => 2/3
					waitTime: 1000, // 单步运动停止的时间(默认值1000ms)
				};
			},
			filteredList() {
				let filtered = [...this.healthData.list];

				// 首先应用单选按钮的筛选
				if (this.activeFilter == 'planStatus') {
					filtered = filtered.filter(item => item.planStatus == 1);
				} else if (this.activeFilter == 'status') {
					filtered = filtered.filter(item => item.status == 1);
				}

				// 然后应用搜索框的筛选
				if (this.selectKey.trim()) {
					const searchTerm = this.selectKey.trim().toLowerCase();
					filtered = filtered.filter(item =>
						item.name.toLowerCase().includes(searchTerm) ||
						item.code.toLowerCase().includes(searchTerm)
					);
				}

				return filtered;
			}
			// filteredList() {
			// 	if (!this.selectKey.trim()) {
			// 		return [...this.healthData.list];
			// 	}
			// 	const newStructureList = this.healthData.list;
			// 	const searchTerm = this.selectKey.trim().toLowerCase();
			// 	return newStructureList.filter(item =>
			// 		item.name.toLowerCase().includes(searchTerm) ||
			// 		item.code.toLowerCase().includes(searchTerm)
			// 	);

			// 	item.planStatus=1 //正在预警
			// 	item.status=1//超限警告
			// }
		},
		async mounted() {
			setTimeout(() => {
				this.$nextTick(async () => {
					try {
						await this.handleQuery();
						await this.getHealthList();
						this.initMap();
						this.initDbBarECharts();
						this.initRefresh();
						this.loading = false;
					} catch (error) {
						console.error('数据加载失败:', error);
						this.loading = false;
						// 可以添加错误提示
					}
				});
			}, 500)
			this.initBarsCharts();
			// 获取健康度等级 图标数据
			let leveData = [99, 99, 99];
			let levelXData = ["等级Ⅰ", "等级Ⅱ", "等级Ⅲ"];
			let levelColorObj = {
				barColor0: 'rgba(0,185,254,0.1)',
				barColor6: 'rgba(0,185,254,0.6)',
				barColor: 'rgba(0,185,254,1)',
				barTColor: 'rgba(132,222,255,1)',
			}
			this.levelOption = this.initBEcharts(levelXData, leveData, levelColorObj);
			// 获取技术状况 图标数据
			let jsData = [99, 99, 99];
			let jsXData = ["1类", "2类", "3类"];
			let jsColorObj = {
				barColor0: 'rgba(2,255,131,0.1)',
				barColor6: 'rgba(2,255,131,0.6)',
				barColor: 'rgba(2,255,131,1)',
				barTColor: 'rgba(143,255,201,1)',
			}
			this.jsOption = this.initBEcharts(jsXData, jsData, jsColorObj);
			// this.monitoringCategory();
			this.getWarningList();
			// let data = await this.getDataInfo();
			// this.initCGEcharts(data);

		},
		destroyed() {
			this.cleanupResources(); // 新增：组件销毁时清理资源
			if (this.refreshTimer) {
				clearInterval(this.refreshTimer);
				this.refreshTimer = null;
			}
		},
		methods: {
			async UpdateActiveFilter(data) {
				this.activeFilter = data;

				try {
					this.name = ''; // 重置类型筛选
					this.searchAll = true;
					this.structureType = '全部'; // 新增：重置结构类型

					// 重置地图显示 - 显示所有结构物
					if (this.$refs.ampRef) {
						await this.$refs.ampRef?.removeMarker();
						this.$refs.ampRef?.removeDistrictHight();

						// 使用默认配置显示所有点
						await this.$refs.ampRef?.setVector(this.filteredList, {
								"id": "1821371445660356609",
							icon: require('@/assets/map/pile.png'),
							paramsDTO: {
								precisionParams: {
									type: '全部',
									customParams: []
								}
							}
						});
					}

					// 强制更新列表显示
					if (this.$refs.struRef) {
						this.$refs.struRef.getList('');
					}
					this.filterData();
					// 重置图表显示
					this.initDbBarECharts();
					return;
				} catch (error) {
					console.error('处理"全部"点击时出错:', error);
					return;
				}


			},
			// riskLevel(value) {
			// 	console.log("this.structureList", this.structureList);
			// 	console.log("value", value);
			// 	return this.structureList.filter(item => item.riskLevel === value).length;
			// },
			// riskType(value) {
			// 	console.log("this.structureList", this.structureList);
			// 	console.log("value", value);
			// 	return this.structureList.filter(item => item.riskType === value).length;
			// },
			// afterLoaded() {
			// 	this.$nextTick(async () => {
			// 		await this.handleQuery();
			// 		this.initDbBarECharts();
			// 		this.initRefresh();
			// 		this.loading = false;
			// 		this.initMap();
			// 	})
			// },
			// 修改：优化地图和数据加载顺序
			afterLoaded() {
				// 其他异步操作在地图初始化后进行
				this.$nextTick(async () => {
					try {
						await this.handleQuery();
						this.initMap();
						this.initDbBarECharts();
						this.initRefresh();
						this.loading = false;
						console.log('数据加载完成，地图标记点已设置');
					} catch (error) {
						console.error('数据加载失败:', error);
						this.loading = false;
						// 可以添加错误提示
					}
				});
			},
			// 新增：资源清理方法
			cleanupResources() {
				// 清理定时器
				if (this.refreshTimer) {
					clearInterval(this.refreshTimer);
					this.refreshTimer = null;
				}

				// 清理地图资源
				if (this.$refs.ampRef && typeof this.$refs.ampRef.cleanup === 'function') {
					this.$refs.ampRef.cleanup();
				}
			},
			// 新增：定时刷新数据
			initRefresh() {
				// 定时刷新数据
				this.refreshTimer = setInterval(async () => {
					await this.handleQuery();

					this.getWarningList();
					this.monitoringCategory();
					// let data = await this.getDataInfo();
					let data = [{
							"disPlayName": "设备在线率",
							"disPlayNumber": 0.98625819018948114
						},
						{
							"disPlayName": "传感器报警",
							"disPlayNumber": 0.0030694764181571337
						},
						{
							"disPlayName": "数据完整率",
							"disPlayNumber": 0.9868207958233909
						}
					];
					this.initCGEcharts(data);
				}, 60000); // 每分钟刷新一次
			},
			onLevelClick(item, index) {
				if (this.warningIndex == index) {
					this.warningIndex = null;
					this.warningData = this.levelList
				} else {
					this.warningIndex = index
					this.warningData = this.levelList.filter(v => v.alertLevel == item.name)
				}
			},
			async onFClick(data) {
				if (!data) return;

				// 处理"全部"点击
				if (data.name === '全部' || !data.name) {
					try {
						this.name = ''; // 重置类型筛选
						this.searchAll = true;
						this.structureType = '全部'; // 新增：重置结构类型

						// 重置地图显示 - 显示所有结构物
						if (this.$refs.ampRef) {
							await this.$refs.ampRef?.removeMarker();
							this.$refs.ampRef?.removeDistrictHight();

							// 使用默认配置显示所有点
							await this.$refs.ampRef?.setVector(this.healthData.list, {
									"id": "1821371445660356609",
								icon: require('@/assets/map/pile.png'),
								paramsDTO: {
									precisionParams: {
										type: '全部',
										customParams: []
									}
								}
							});
						}

						// 强制更新列表显示
						if (this.$refs.struRef) {
							this.$refs.struRef.getList('');
						}
						this.filterData();
						// 重置图表显示
						this.initDbBarECharts();
						return;
					} catch (error) {
						console.error('处理"全部"点击时出错:', error);
						return;
					}
				}

				// 处理特定类型点击（桥梁/边坡）
				try {
					this.name = data.name;
					this.structureType = data.name; // 新增：设置当前结构类型
					const foundItem = this.queryList.find(v =>
						v.paramsDTO?.precisionParams?.type === data.name
					);

					if (foundItem) {
						this.obj = {
							...foundItem
						};

						if (this.$refs.ampRef) {
							await this.$refs.ampRef?.removeMarker();

							setTimeout(async () => {
								try {
									this.$refs.ampRef?.removeDistrictHight();
									await this.$refs.ampRef?.setVector(this.healthData.list, this.obj);
									this.searchAll = false;

									if (this.$refs.struRef) {
										this.$refs.struRef.getList();
									}
								} catch (innerError) {
									console.error('设置地图标记时出错:', innerError);
								}
							}, 500);
						}

						this.initDbBarECharts(data.name);
					} else {
						console.warn(`未找到匹配的查询配置: ${data.name}`);
					}
				} catch (error) {
					console.error('处理类型点击时出错:', error);
				}

				this.filterData();
			},
			getWarningList() {
				getNotDealRecordInformation().then(res => {
					if (res.code == 200 && res.result) {
						this.warningData = [];
						// this.warningData = [...res.result.三级预警, ...res.result.二级预警, ...res.result.一级预警]
						for (let key in res.result) {
							this.warningData = [...this.warningData, ...res.result[key]]
						}
						this.levelList = this.warningData || [];
					}
				});
			},
			setPosition(item) {
				window.mapConfig.setZoomAndCenter(14, [item.longitude, item.latitude]);
			},
			initMap() {
				this.$nextTick(() => {
					if (this.$refs.ampRef) {
						this.$refs.ampRef.setVector(this.healthData.list, {
							"id": "1821371445660356609",
							icon: require('@/assets/map/pile.png'),
							paramsDTO: {
								precisionParams: {
									type: '全部',
									customParams: []
								}
							}
						})
					}
				})
			},
			// initMap() {
			// 	this.$nextTick(() => {
			// 		if (this.$refs.ampRef) {
			// 			// 使用默认配置显示所有点
			// 			this.$refs.ampRef.setVector(this.healthData.list, {
			// 				paramsDTO: {
			// 					precisionParams: {
			// 						type: '全部',
			// 						customParams: []
			// 					}
			// 				}
			// 			});
			// 		}
			// 	});
			// },
			// // 修改：优化地图初始化流程
			// initMap() {
			// 	this.$nextTick(() => {
			// 		if (this.$refs.ampRef) {
			// 			// 设置地图基础配置（如果AMap组件支持）
			// 			if (typeof this.$refs.ampRef.setBaseMapConfig === 'function') {
			// 				this.$refs.ampRef.setBaseMapConfig();
			// 			}

			// 			// 设置地图标记点
			// 			this.$refs.ampRef.setVector(this.structureList, this.obj);

			// 			// 监听地图加载完成事件（如果AMap组件支持）
			// 			if (typeof this.$refs.ampRef.onMapReady === 'function') {
			// 				this.$refs.ampRef.onMapReady(() => {
			// 					console.log('地图完全加载完成');
			// 				});
			// 			}
			// 		}
			// 	});
			// },
			monitoringCategory() {
				getSensorCountKindByMonitorTypeTibet().then(res => {
					if (res.code == 200) {
						for (let key in res.result) {
							this.categoryList.forEach(item => {
								if (item.name == key) {
									item.val = res.result[key]
								}
							})
						}
					}
				})
			},
			initBarsCharts() {
				getDataCount().then(res => {
					if (res.code == 200) {
						let xData = ["四月", "五月", "六月", "七月", "八月", "九月"];
						let data = JSON.parse(res.result.eachMonthCount)

						this.todayCount = (res.result.realTimeData / 100000000).toFixed(2) + ' 亿'
						this.ytodayCount = (res.result.yesterdayData / 100000000).toFixed(2) + ' 亿'

						let mlSColor = '#00FDFD';
						let mlEColor = 'rgba(0,253,253,0.1)';
						this.mlOption = this.initBarCharts(xData, data, mlSColor, mlEColor);
					}
				})
			},
			initBarCharts(xData = [], data = [], startColor = '', endColor = '', legend = []) {

				let option = {
					backgroundColor: "rgba(0,0,0,0)",
					grid: {
						left: "3%",
						right: "4%",
						top: "25%",
						bottom: "1%",
						containLabel: true,
					},
					legend: {
						show: legend.length > 0 ? true : false,
						data: legend,
						x: 'center',
						y: '2%',
						itemWidth: 10,
						itemHeight: 10,
						itemGap: 10,
						textStyle: {
							color: '#ffffff',
						},
					},
					xAxis: {
						data: xData,
						axisLine: {
							lineStyle: {
								color: "rgba(110,112,121,0.5)",
							},
						},
						axisLabel: {
							color: "#8B8C8C",
							fontSize: this.isBig ? 22 : 14,
							// rotate: 40,
							interval: false,
						},
					},
					yAxis: {
						name: "",
						nameTextStyle: {
							color: "#999999",
							fontSize: this.isBig ? 22 : 13,
						},
						axisLabel: {
							color: "#999999",
							fontSize: this.isBig ? 22 : 13,
							formatter: function(value) {
								return (value / 100000000) + ' 亿';
							}
						},
						splitLine: {
							show: true,
							lineStyle: {
								color: "rgba(110,112,121,0.5)",
							},
						},
						splitArea: {
							show: false,
						},
					},
					series: [{
						name: legend.length ? legend[0] : "",
						type: "bar",
						barWidth: this.isBig ? 15 : 10,
						itemStyle: {
							normal: {
								color: new echarts.graphic.LinearGradient(
									0,
									0,
									0,
									1,
									[{
											offset: 0,
											color: startColor,
										},
										{
											offset: 0.8,
											color: endColor,
										},
									],
									false
								),
							},
						},
						data,
					}, ],
				};
				return option;
			},
			initDbBarECharts(name = '') {
				// 清空现有图表选项
				this.dbOption = null;

				// 准备数据
				let domainObj = {};
				let xData = [];
				let structureData = [];

				// 处理"全部"类型
				if (!name || name === '全部') {
					// 统计所有类型的结构物
					this.healthData.list.forEach((item) => {
						if (domainObj.hasOwnProperty(item.domainName)) {
							domainObj[item.domainName]['item'].push(item);
						} else {
							domainObj[item.domainName] = {
								name: item.domainName,
								item: [item]
							};
						}
					});
				}
				// 处理特定类型（桥梁/边坡）
				else {
					this.healthData.list.forEach((item) => {
						if (item.structureType === name) {
							if (domainObj.hasOwnProperty(item.domainName)) {
								domainObj[item.domainName]['item'].push(item);
							} else {
								domainObj[item.domainName] = {
									name: item.domainName,
									item: [item]
								};
							}
						}
					});
				}

				// 准备x轴数据
				const keys = Object.keys(domainObj);
				if (keys.length) {
					xData = keys;
					structureData = keys.map(key => domainObj[key].item.length);
				}

				// 定义不同类型的系列样式
				const seriesTemplates = {
					'桥梁': {
						name: "桥梁数量",
						color: new echarts.graphic.LinearGradient(1, 0, 0, 0, [{
								offset: 0,
								color: "rgba(0,184,250,1)"
							},
							{
								offset: 1,
								color: "rgba(0,184,250,0)"
							}
						])
					},
					'边坡': {
						name: "边坡数量",
						color: new echarts.graphic.LinearGradient(1, 0, 0, 0, [{
								offset: 0,
								color: "rgba(238,148,51,1)"
							},
							{
								offset: 1,
								color: "rgba(238,148,51,0)"
							}
						])
					},
					'全部': {
						name: "结构物总数",
						color: new echarts.graphic.LinearGradient(1, 0, 0, 0, [{
								offset: 0,
								color: "rgba(100,200,100,1)"
							},
							{
								offset: 1,
								color: "rgba(100,200,100,0)"
							}
						])
					}
				};

				// 获取当前类型的系列配置
				const currentSeries = seriesTemplates[name] || seriesTemplates['全部'];

				// 构建图表选项
				this.dbOption = {
					backgroundColor: "rgba(0,0,0,0)",
					legend: {
						show: true,
						x: "center",
						y: "2%",
						textStyle: {
							color: "#fff",
							fontSize: this.isBig ? 22 : 12,
						},
						data: [currentSeries.name],
					},
					grid: {
						left: "3%",
						right: "7%",
						top: "10%",
						bottom: "3%",
						containLabel: true,
					},
					tooltip: {
						show: true,
						trigger: "axis",
						axisPointer: {
							type: "shadow",
						},
						formatter: params => {
							const data = params[0];
							return `
			          <div style="font-weight:bold">${data.name}</div>
			          <div>${currentSeries.name}: ${data.value}</div>
			        `;
						}
					},
					xAxis: {
						type: "value",
						name: '个',
						nameTextStyle: {
							color: "#999999",
							fontSize: this.isBig ? 25 : 14,
							padding: this.isBig ? [10, 0, 0, 2] : [6, 0, 0, -4],
						},
						axisLine: {
							show: true,
							lineStyle: {
								color: "rgba(110,112,121,0.5)",
							},
						},
						axisLabel: {
							color: "#8B8C8C",
							fontSize: this.isBig ? 25 : 12,
						},
						splitLine: {
							show: true,
							lineStyle: {
								color: "rgba(110,112,121,0.5)",
							},
						},
					},
					yAxis: [{
						type: "category",
						axisTick: {
							show: true,
							length: -5,
						},
						axisLine: {
							show: true,
							lineStyle: {
								color: "rgba(110,112,121,0.5)",
							},
						},
						axisLabel: {
							color: "#fff",
							fontSize: this.isBig ? 22 : 12,
						},
						data: xData,
					}],
					series: [{
						...currentSeries,
						type: "bar",
						barWidth: this.isBig ? 20 : 10,
						itemStyle: {
							normal: {
								color: currentSeries.color
							}
						},
						data: structureData,
						label: {
							show: true,
							position: 'right',
							formatter: '{c}',
							color: '#fff',
							fontSize: this.isBig ? 14 : 10
						},
						emphasis: {
							itemStyle: {
								shadowBlur: 10,
								shadowOffsetX: 0,
								shadowColor: 'rgba(0, 0, 0, 0.5)'
							}
						}
					}]
				};

				// 调试日志
				console.log('图表数据:', {
					name,
					xData,
					structureData,
					series: currentSeries.name
				});
			},
			initBEcharts(xData, data, colorObj) {
				const sideData = data.map((item) => item + 1);

				let option = {
					backgroundColor: "rgba(0,0,0,0)",
					grid: {
						top: "15%",
						left: "3%",
						right: "3%",
						bottom: "2%",
						containLabel: true,
					},
					xAxis: {
						data: xData,
						//坐标轴
						axisLine: {
							lineStyle: {
								color: "rgba(110,112,121,0.5)",
							},
						},
						//坐标值标注
						axisLabel: {
							show: true,
							textStyle: {
								color: "#8B8C8C",
								fontSize: this.isBig ? 22 : 12,
							},
						},
					},
					yAxis: {
						type: "value",
						name: '个',
						nameTextStyle: {
							color: "#999999",
							algin: 'right',
							fontSize: this.isBig ? 25 : 14,
							padding: [0, 30, 0, 0],
						},
						//坐标轴
						axisLine: {
							show: false,
						},
						//坐标值标注
						axisLabel: {
							show: true,
							textStyle: {
								color: "#999999",
								fontSize: this.isBig ? 22 : 12,
							},
						},
						//分格线
						splitLine: {
							lineStyle: {
								color: "rgba(110,112,121,0.5)",
							},
						},
						splitArea: {
							show: false,
						},
						interval: 10,
					},
					series: [{
							name: "a",
							tooltip: {
								show: false,
							},
							label: {
								normal: {
									show: true,
									position: 'top',
									fontSize: this.isBig ? 24 : 16,
									color: '#fff',
									offset: [0, -10],
								},
							},
							type: "bar",
							barWidth: 24.5,
							itemStyle: {
								normal: {
									color: new echarts.graphic.LinearGradient(
										0,
										1,
										0,
										0,
										[{
												offset: 0,
												color: colorObj.barColor0, // 0% 处的颜色
											},
											{
												offset: 0.6,
												color: colorObj.barColor6, // 60% 处的颜色
											},
											{
												offset: 1,
												color: colorObj.barColor, // 100% 处的颜色
											},
										],
										false
									),
								},
							},
							data: data,
							barGap: 0,
						},
						{
							type: "bar",
							barWidth: 8,
							itemStyle: {
								normal: {
									color: new echarts.graphic.LinearGradient(
										0,
										1,
										0,
										0,
										[{
												offset: 0,
												color: colorObj.barColor0, // 0% 处的颜色
											},
											{
												offset: 0.6,
												color: colorObj.barColor6, // 60% 处的颜色
											},
											{
												offset: 1,
												color: colorObj.barColor, // 100% 处的颜色
											},
										],
										false
									),
								},
							},
							barGap: 0,
							data: sideData,
						},
						{
							name: "b",
							tooltip: {
								show: false,
							},
							type: "pictorialBar",
							itemStyle: {
								borderWidth: 1,
								borderColor: colorObj.barTColor,
								color: colorObj.barTColor,
							},

							symbol: "path://M 0,0 l 120,0 l -30,60 l -120,0 z",
							symbolSize: ["30", "12"],
							symbolOffset: ["0", "-8"],
							symbolRotate: -15,
							symbolPosition: "end",
							data: data,
							z: 3,
						},
					],
				};
				return option;
			},
			initCGEcharts(data) {
				var dataStyle = {
					normal: {
						label: {
							show: false
						},
						labelLine: {
							show: false
						},
						shadowBlur: 0,
						shadowColor: '#203665'
					}
				};
				let data1 = data.filter(v => v.disPlayName == '设备在线率');
				let data2 = data.filter(v => v.disPlayName == '传感器报警');
				let data3 = data.filter(v => v.disPlayName == '数据完整率');
				let circleRadius1 = data1 ? (data1[0]?.disPlayNumber * 100).toFixed(0) - 0 : 0;
				let circleRadius2 = data2 ? (data2[0]?.disPlayNumber * 100).toFixed(0) - 0 : 0;
				let circleRadius3 = data3 ? (data3[0]?.disPlayNumber * 100).toFixed(0) - 0 : 0;
				let radius = ['35%', '40%'];
				this.cgOption = {
					backgroundColor: "rgba(0,0,0,0)",
					series: [{
						name: '第一个圆环',
						type: 'pie',
						clockWise: false,
						radius: radius || [40, 50],
						itemStyle: dataStyle,
						hoverAnimation: false,
						center: ['18%', '45%'],
						data: [{
							value: circleRadius1,
							label: {
								normal: {
									rich: {
										a: {
											color: '#FFFFFF',
											align: 'center',
											fontSize: this.isBig ? 40 : 20,
											fontWeight: "bold",
											padding: this.isBig ? [140, 0, 0, 0] : [70, 0, 0, 0],
										},
										b: {
											color: '#fff',
											align: 'center',
											fontSize: this.isBig ? 32 : 16,
											padding: this.isBig ? [100, 0, 0, 0] : [50, 0, 0, 0]
										}
									},
									formatter: (params) => {
										return "{a|" + params.value + "%}" + "\n{b|设备在线率}";
									},
									position: 'center',
									show: true,
									textStyle: {
										fontSize: this.isBig ? 24 : '14',
										fontWeight: 'normal',
										color: '#fff',
									}
								}
							},
							itemStyle: {
								normal: {
									color: '#90EF7F',
									shadowColor: '#90EF7F',
									shadowBlur: 0,
									borderRadius: 10,
								}
							}
						}, {
							value: 100 - circleRadius1,
							name: 'invisible',
							itemStyle: {
								normal: {
									color: 'rgba(23,116,255,0.2)'
								},
								emphasis: {
									color: 'rgba(23,116,255,0.2)'
								}
							}
						}]
					}, {
						name: '第二个圆环',
						type: 'pie',
						clockWise: false,
						radius: radius || [40, 50],
						itemStyle: dataStyle,
						hoverAnimation: false,
						center: ['50%', '45%'],
						data: [{
							value: circleRadius2,
							label: {
								normal: {
									rich: {
										a: {
											color: '#FFFFFF',
											align: 'center',
											fontSize: this.isBig ? 40 : 20,
											fontWeight: "bold",
											padding: this.isBig ? [140, 0, 0, 0] : [70, 0, 0, 0],
										},
										b: {
											color: '#fff',
											align: 'center',
											fontSize: this.isBig ? 32 : 16,
											padding: this.isBig ? [100, 0, 0, 0] : [50, 0, 0, 0]
										}
									},
									formatter: function(params) {
										return "{a|" + params.value + "%}" + "\n{b|传感器报警}";
									},
									position: 'center',
									show: true,
									textStyle: {
										fontSize: '14',
										fontWeight: 'normal',
										color: '#fff'
									}
								}
							},
							itemStyle: {
								normal: {
									color: '#2196F3',
									shadowColor: '#2196F3',
									shadowBlur: 0,
									borderRadius: 10,
								}
							}
						}, {
							value: 100 - circleRadius2,
							name: 'invisible',
							itemStyle: {
								normal: {
									color: 'rgba(23,116,255,0.2)'
								},
								emphasis: {
									color: 'rgba(23,116,255,0.2)'
								}
							}
						}]
					}, {
						name: '第三个圆环',
						type: 'pie',
						clockWise: false,
						radius: radius || [40, 50],
						itemStyle: dataStyle,
						hoverAnimation: false,
						center: ['82%', '45%'],
						data: [{
							value: circleRadius3,
							label: {
								normal: {
									rich: {
										a: {
											color: '#FFFFFF',
											align: 'center',
											fontSize: this.isBig ? 40 : 20,
											fontWeight: "bold",
											padding: this.isBig ? [140, 0, 0, 0] : [70, 0, 0, 0],
										},
										b: {
											color: '#fff',
											align: 'center',
											fontSize: this.isBig ? 32 : 16,
											padding: this.isBig ? [100, 0, 0, 0] : [50, 0, 0, 0]
										}
									},
									formatter: function(params) {
										return "{a|" + params.value + "%}" + "\n\{b|数据完整率}";
									},
									position: 'center',
									show: true,
									textStyle: {
										fontSize: '14',
										fontWeight: 'normal',
										color: '#fff'
									}
								}
							},
							itemStyle: {
								normal: {
									color: '#A569FF',
									shadowColor: '#A569FF',
									shadowBlur: 0,
									borderRadius: 10,
								}
							}
						}, {
							value: 100 - circleRadius3,
							name: 'invisible',
							itemStyle: {
								normal: {
									color: 'rgba(23,116,255,0.2)'
								},
								emphasis: {
									color: 'rgba(23,116,255,0.2)'
								}
							}
						}]
					}]
				}
			},
			// 分布情况 图表点击事件
			async onEChartsClick(data) {
				if (!data) return;
				let {
					name
				} = data;
				this.searchAll = false;
				if (this.$refs.struRef) {
					this.$refs.struRef.getList(name);
				}
				// 地图行政区
				let rangeData = cache.session.getJSON("rangeData");
				if (rangeData && rangeData.length) {
					let d = rangeData.find(v => v.name == name);
					if (d && this.$refs.ampRef) {
						this.$refs.ampRef.setDistrictHight(d);
						let index = this.queryList.findIndex(v => v.paramsDTO.precisionParams.type == this.name);
						if (index != -1) {
							this.obj = JSON.parse(JSON.stringify(this.queryList[index])) || {};
							this.obj.managementMaintenanceIds = [d.sys_dept_id]
							// let loadingInstance = Loading.service({ fullscreen: true, background: 'rgba(0, 0, 0, 0.4)', spinner: 'el-icon-loading' });
							await this.$refs.ampRef?.removeMarker();
							setTimeout(() => {
								this.$nextTick(async () => {
									let data = this.healthData.list.filter((item) => {
										return (item.domainName == name && item
											.structureType == this.name)
									})
									await this.$refs.ampRef?.setVector(data, this.obj);
									// loadingInstance.close();
								});
							}, 500)
						}
					}
				}
			},
			// 结构物清单列表点击事件
			onTableClick(data) {
				let serviceUrl2 = window.location.origin;
				let directUrl2 = serviceUrl2 + `/healthMonitoringRealTime/realTime?code=${data.nodeId}`
				// localStorage.setItem('mapData', JSON.stringify(data));
				const iconPath = data.icon || this.getIconByStructureType(data.structureType);
				localStorage.setItem('mapData', JSON.stringify({
					lon: data.lon,
					lat: data.lat,
					name: data.name,
					icon: iconPath,
					zoom: 18, // 可选：自定义缩放级别,
					...data,
				}));

				window.open(`${directUrl2}`, '_blank');

				if (!data) return;
				let {
					lon,
					lat,
					nodeId
				} = data;
				let shape = `POINT(${lon} ${lat})`;

				// 设置地图层级为 12
				this.$refs.ampRef?.map.setZoom(18, {
					duration: 1000, // 动画持续时间（毫秒）
					easing: 'cubicOut' // 动画效果
				});
				this.$refs.ampRef?.setCenter(shape);

			},
			// 辅助函数：根据结构类型获取图标路径（组件内版本）
			getIconByStructureType(type) {
				const iconMap = {
					'桥梁': require('@/assets/cockpit/bridge.png'),
					'隧道': require('@/assets/cockpit/tunnel-icon.png'),
					'边坡': require('@/assets/cockpit/slope.png'),
					default: '/static/img/default-marker.png'
				};
				return iconMap[type] || iconMap.default;
			},
			// 获取传感器运营状态数据
			getDataInfo(nodeCode = null) {
				return new Promise((resolve, reject) => {
					getWarningListTibet({
						nodeCode
					}).then(res => {
						if (res.code === 200 && res.result) {
							resolve(res.result)
						} else {
							resolve(null)
						}
					}).catch(err => {
						reject(err)
					});
				});
			},
			handleQuery() {
				return new Promise((resolve, reject) => {
					listStructure({}).then(res => {
						this.structureList = res.data
						// 排序 status = 1 的放在最前面，其次是planStatus = 1 的，最后是status = 0 的
						this.structureList.sort((a, b) => {
							if (a.status == 1) {
								return -1;
							} else if (b.status == 1) {
								return 1;
							} else if (a.planStatus == 1) {
								return -1;
							} else if (b.planStatus == 1) {
								return 1;
							}
							return 0;
						});
						this.filterData()
						resolve(true)
					})
				})
			},
			getHealthList() {
				return new Promise((resolve, reject) => {
					getHealthList({}).then(res => {
						this.healthData = res.data
						// // 排序 status = 1 的放在最前面，其次是planStatus = 1 的，最后是status = 0 的
						// this.structureList.sort((a, b) => {
						// 	if (a.status == 1) {
						// 		return -1;
						// 	} else if (b.status == 1) {
						// 		return 1;
						// 	} else if (a.planStatus == 1) {
						// 		return -1;
						// 	} else if (b.planStatus == 1) {
						// 		return 1;
						// 	}
						// 	return 0;
						// });
						// this.filterData()
						resolve(true)
					})
				})
			},
			initRefresh() {
				this.refreshTimer = setInterval(() => {
					this.handleQuery();
				}, 15000);
			},
			filterData() {
				this.sourceData = this.structureList.filter((item) => {
					return (this.structureType == '全部' ? true : item.structureType == this.structureType) && (item
						.domainName.includes(this.dept) || item.roadName.includes(this.dept) || item.name
						.includes(this.dept))
				})
				this.statistics();
				//根据报警传感器数量计算报警率
				// let disPlayNumber = (this.statisticsData.warning + this.statisticsData.abnormal) / 137;
				let data = [{
						"disPlayName": "设备在线率",
						"disPlayNumber": 0.98625819018948114
					},
					{
						"disPlayName": "传感器报警",
						"disPlayNumber": 0.01
					},
					{
						"disPlayName": "数据完整率",
						"disPlayNumber": 0.98948207958233909
					}
				];
				this.initCGEcharts(data);
			},
			statistics() {
				this.statisticsData = {
					all: 0,
					normal: 0,
					abnormal: 0,
					warning: 0,
					disconnect: 0
				}
				this.sourceData.forEach((item) => {
					if (item.planStatus == 0 && item.status == 0) {
						this.statisticsData.normal++
					}
					if (item.planStatus == 1) {
						this.statisticsData.abnormal++
					}
					if (item.status == 1) {
						this.statisticsData.warning++
					}
					if (item.isHasOffFlashDev != 0) {
						this.statisticsData.disconnect++
					}
					this.statisticsData.all++
				})
			},
		},
		// beforeDestroy() {
		// 	if (this.refreshTimer) {
		// 		clearInterval(this.refreshTimer)
		// 		this.refreshTimer = null;
		// 	}
		// },
	}
</script>

<style lang="scss" scoped>
	@import "@/assets/styles/utils.scss";

	.bridge-special {
		width: 100%;
		height: 100%;
		position: relative;
		margin-top: vwpx(15px);

		.special-c {
			width: 100%;
			height: 91vh;
			position: absolute;
			top: 0;
			left: 0;
			right: 0;
			bottom: 0;
		}

		.mb-2 {
			margin-bottom: vwpx(20px);
		}

		.mb-3 {
			margin-bottom: vwpx(30px);
		}

		.ml-2 {
			margin-left: vwpx(20px);
		}

		.special-m {
			position: absolute;
			left: 50%;
			transform: translateX(-50%);
			top: 2.125rem;

			.content {
				display: flex;
				flex-wrap: wrap;
				justify-content: space-between;

				.title-box {
					height: vwpx(80px); // 60 * 2
					color: white;
					font-size: vwpx(32px); // 30 * 2
					display: flex;
					justify-content: space-between;
					align-items: center;
					padding: 0 vwpx(60px); // 30 * 2
					background-size: 100% 100%;
					// cursor: pointer;
				}

				.title-box-b {
					background-image: url("~@/assets/earlyWarning/title-box-b.png");
				}

				.title-box-g {
					background-image: url("~@/assets/earlyWarning/title-box-r.png");
				}

				.title-box-r {
					background-image: url("~@/assets/earlyWarning/title-box-r.png");
				}

				.title-box-y {
					background-image: url("~@/assets/earlyWarning/title-box-r.png");
				}
			}
		}

		.special-l {
			position: absolute;
			left: vwpx(30px);
			top: 0;

			.base-info {
				padding: vwpx(10px) vwpx(20px);
				height: 100%;
				overflow-y: auto;

				&::-webkit-scrollbar {
					width: vwpx(12px);
					height: vwpx(12px);
				}

				&::-webkit-scrollbar-thumb {
					background-color: rgba(1, 102, 254, 0.3);
					border-radius: 2px;
				}

				&::-webkit-scrollbar-track {
					background-color: rgba(1, 102, 254, 0.2);
				}

				.info-item {
					display: flex;
					align-items: center;

					.img {
						width: vwpx(120px);
						height: vwpx(140px);
					}

					.img-icon {
						width: vwpx(68px);
						height: vwpx(68px);
						margin-left: auto;
						cursor: pointer;
					}

					.info-data {
						font-family: Source Han Sans, Source Han Sans;
						// font-weight: 400;
						font-size: vwpx(28px);
						color: #FFFFFF;
						text-align: left;
						// font-style: normal;
						// text-transform: none;
						margin-left: vwpx(30px);

						display: flex;
						flex-direction: column;

						span:first-child {
							font-family: Source Han Sans, Source Han Sans;
							// font-weight: 500;
							font-size: vwpx(30px);
							color: #00B9FE;
							line-height: vwpx(40px);
							text-align: left;
							// font-style: normal;
							// text-transform: none;
							margin-bottom: vwpx(12px);
							cursor: pointer;
						}
					}
				}

				.divider {
					border: 1px dotted rgba(156, 189, 255, 0.5);
					margin: vwpx(20px) 0;
				}
			}
		}

		.special-r {
			position: absolute;
			right: vwpx(30px);
			top: 0;

			.info {
				display: flex;

				.warning-content {
					height: 60%;
					overflow-y: auto;

					&::-webkit-scrollbar {
						width: vwpx(12px);
						height: vwpx(12px);
					}

					&::-webkit-scrollbar-thumb {
						background-color: rgba(1, 102, 254, 0.3);
						border-radius: 2px;
					}

					&::-webkit-scrollbar-track {
						background-color: rgba(1, 102, 254, 0.2);
					}

					.warning-content-list {
						height: 45%;
						color: white;
						margin-bottom: vwpx(20px);
						font-size: vwpx(28px);
						padding: 0 13px;
						display: flex;
						align-items: center;
						cursor: pointer;

						.warning-content-id {
							width: vwpx(80px);
							height: vwpx(80px);
							display: inline-flex;
							/* text-align: center; */
							justify-content: center;
							align-items: center;
							// background: #024d98;
							border: 1px solid gray;

							.img {
								width: 100%;
								height: 100%;
							}
						}

						.warning-content-val {
							height: 100%;
							display: inline-flex;
							width: 94%;
							margin-left: 1%;
						}
					}
				}

				.warning-status {
					display: flex;
					align-items: center;
					justify-content: space-between;
					padding: vwpx(20px);
					flex-wrap: wrap;

					.warning-status-list {
						margin: 0 vwpx(10px) vwpx(20px) vwpx(10px);
						width: 25%;
						height: 25%;
						display: flex;
						align-items: center;
						flex-direction: column;
						cursor: pointer;

						.img {
							width: 60%;
							height: 60%;
						}

						span {
							font-family: Source Han Sans, Source Han Sans;
							font-weight: 500;
							font-size: vwpx(28px);
							color: #FFFFFF;
							text-align: center;
							font-style: normal;
							margin-top: vwpx(10px);
						}
					}
				}
			}

			.jc-data-statistics {
				position: absolute;
				top: vwpx(5px);
				left: 0;
				right: 0;
				width: 100%;

				display: flex;
				flex-direction: column;

				.statistics-info {
					margin-bottom: vwpx(10px);
					display: flex;
					align-items: center;
					justify-content: flex-start;
					margin-left: 50%;
					transform: translateX(-40%);

					.info-today {
						display: block;
						width: vwpx(25px);
						height: vwpx(25px);
						background: #02FF83;
					}

					.info-yesterday {
						display: block;
						width: vwpx(25px);
						height: vwpx(25px);
						background: #FFBE27;
					}

					.info-val {
						font-weight: 400;
						font-size: 1.3vh;
						color: #FFFFFF;
						margin: 0 vwpx(20px);
					}

					.info-unit {
						font-weight: 400;
						font-size: 1.25vh;
						color: rgba(255, 255, 255, 0.6);
					}
				}
			}

			.monitoring-category {
				width: 100%;
				display: flex;
				align-items: center;
				justify-content: space-between;
				flex-wrap: wrap;
				padding: vwpx(20px);

				.monitoring-category-list {
					width: 50%;
					margin-bottom: vwpx(15px);
					display: flex;
					align-items: center;

					.img {
						width: vwpx(50px);
						height: vwpx(50px);
					}

					.name {
						font-family: Source Han Sans, Source Han Sans;
						font-weight: 400;
						// font-size: vwpx(30px);
						// font-size: '0.6vw';
						color: #FFFFFF;
						text-align: left;
						font-style: normal;
						text-transform: none;
						margin: 0 vwpx(20px);
					}

					.val {
						font-family: Source Han Sans, Source Han Sans;
						font-weight: 400;
						font-size: vwpx(30px);
						color: #F2AF4A;
						text-align: left;
						font-style: normal;
						text-transform: none;
					}
				}
			}
		}

		.seamless-warp {
			widows: 100%;
			height: 100%;
			// overflow: hidden;
		}
	}
	.custom-input {

		/* 正常状态 */
		::v-deep .el-input__inner {
			background-color: #052343;
			border-color: #e4e7ed;
			/* 浅灰色边框 */
			border-radius: 4px;
			color: white;
		}

		/* 聚焦状态 */
		::v-deep .el-input__inner:focus {
			border-color: #409eff;
			/* 蓝色边框（Element UI主题色） */
			box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.1);
			/* 轻微阴影效果 */
		}

		/* 鼠标悬停状态 */
		::v-deep .el-input__inner:hover {
			border-color: #c0c4cc;
			/* 悬停时边框加深 */
		}
	}
	
	/* 确保高亮样式优先级高于固定样式 */
	.highlight {
		box-shadow: 0 0 0 1px rgb(0 236 254) !important;
		background-color: rgba(0, 185, 254, 0.1) !important;
		transition: all 0.2s;
	}
</style>