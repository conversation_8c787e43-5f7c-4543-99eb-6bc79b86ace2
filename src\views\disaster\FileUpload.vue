<template>
  <div class="imgUploadBox">
    <el-upload
      ref="imageUploadRef"
      list-type="text"
      multiple
      :limit="limit"
      :headers="uploadHeaders"
      :show-file-list="true"
      :action="uploadUrl"
      :before-upload="uploadBefore"
      :on-success="uploadSuccess"
      :on-error="uploadError"
      :on-exceed="uploadExceed"
      :before-remove="uploadRemoveBefore"
      :on-remove="uploadRemove"
      :on-preview="uploadPreview"
    >
      <el-button size="small" type="primary">点击上传</el-button>
    </el-upload>

    <!-- 上传提示 -->
    <div v-if="isShowTip" slot="tip" class="el-upload__tip">
      请上传
      <template v-if="fileSize"> 大小不超过 <b style="color: #f56c6c">{{ fileSize }}MB</b> </template>
      <template v-if="fileType"> 格式为 <b style="color: #f56c6c">{{ fileType.join("/") }}</b> </template>
      的文件，文件名称中不得包含特殊符号（./*）
    </div>

    <!-- <el-dialog :visible.sync="uploadPreviewDialog" title="预览" width="800" append-to-body>
      <img :src="uploadPreviewImageUrl" style="display: block; max-width: 100%; margin: 0 auto">
    </el-dialog> -->
  </div>
</template>

<script>
// -------------------- 引入 --------------------
// API
import { findFiles } from '@/api/file/index.js' // 查询文件
import { removeFile } from '@/api/system/fileUpload.js' // 移除文件
import { getToken } from '@/utils/auth' // 获取token

export default {
  // -------------------- 接收参数 --------------------
  props: {
    value: { // 组件绑定值，会返回字符串，多个文件以英文逗号分隔，格式为：182747XXXX-1827473XXXX，前面是ownerId，用于查询文件，后面是fileId，用于删除文件
      type: undefined,
      default: () => []
    },
    // 文件数量限制
    limit: {
      type: Number,
      default: 20
    },
    // 大小限制(MB)
    fileSize: {
      type: Number,
      default: 20
    },
    // 文件类型, 例如['png', 'jpg', 'jpeg']
    fileType: {
      type: Array,
      default: () => ['png', 'jpg', 'jpeg', 'pdf', 'docx']
    },
    // 是否显示提示
    isShowTip: {
      type: Boolean,
      default: true
    },
    ownerId: {
      type: String,
      default: new Date().getTime().toString()
    },
    // 文件存储路径，用于分类
    storagePath: {
      type: String,
      default: ''
    }
  },
  // -------------------- 变量 --------------------
  data() {
    return {
      /**
       * 请求相关
       */

      // 上传文件请求头
      uploadHeaders: {
        Authorization: 'Bearer ' + getToken()
      },
      // 上传文件地址（上传的文件服务器地址）(不主动更新ownerId，由后端自动生成)
      // uploadUrl: process.env.VUE_APP_BASE_API + `/file/upload?platform=ylzx&ownerId=&storagePath=${this.storagePath}`,

      /**
       * 预览相关
       */

      // 预览dialog
      // uploadPreviewDialog: false,
      // 预览已上传文件地址
      // uploadPreviewImageUrl: '',
    }
  },
  // -------------------- 计算属性 --------------------
  computed: {
    // 上传文件地址（上传的图片服务器地址）
    uploadUrl() {
      // return process.env.VUE_APP_BASE_API + `/file/upload?platform=ylzx&ownerId=&storagePath=${this.storagePath}`
      return process.env.VUE_APP_BASE_API + `/file/upload?platform=ylzx&ownerId=${this.ownerId}&storagePath=${this.storagePath}`
    }
  },
  // -------------------- 方法 --------------------
  methods: {

    // 文件上传前
    uploadBefore(file) {
      console.log('文件上传前：', file)
      // 文件格式验证
      let imgType = file.name.split('.')[1]
      if(!this.fileType.includes(imgType)) {
        this.$modal.msgError(`文件格式不正确，请上传${this.fileType.join('/')}格式文件!`)
        return false
      }
      // 文件大小验证
      if(file.size > this.fileSize * 1024 * 1024) {
        this.$modal.msgError(`文件大小不能超过 ${this.fileSize}MB!`)
        return false
      }
      this.$modal.loading('正在上传文件，请稍候...')
    },

    // 文件上传成功
    uploadSuccess(res, file, fileList) {
      console.log('文件上传成功回调：', res)
      if(res.code === 200) {
        this.$message.success(`文件 “${res.data.originalFilename}” 上传成功`)
        // 返回上传成功的文件列表
        // let uploadFileList = []
        // fileList.forEach((item) => {
        //   if(item.response) {
        //     let ofId = item.response.data.ownerId + '-' + item.response.data.id
        //     uploadFileList.push(ofId)
        //   }
        // })
        // this.$emit('input', uploadFileList.join(','))
        this.$emit('input', file.response.data.ownerId)
        console.log('返回的绑定值：', file.response.data.ownerId)
      }
      this.$modal.closeLoading()
    },

    // 文件上传失败
    uploadError(res, file, fileList) {
      if(file) {
        this.$message.error(`文件 “${file.name}” 上传失败，请重试`)
      }
      this.$modal.closeLoading()
    },
    
    // 文件移除前
    uploadRemoveBefore(file, fileList) {
      return this.uploadRemoveBeforeTip(file, fileList)
    },

    // 文件移除前提示
    uploadRemoveBeforeTip(file, fileList) {
      console.log('文件移除前：', file)
      return new Promise((resolve, reject) => {
        // 处理非文件文件
        if(!this.fileType.includes(file.name.split('.')[1])) {
          resolve(true)
        } else if(file.size > this.fileSize * 1024 * 1024) {
          // 文件大小超出限制处理
          resolve(true)
        } else {
          // 正常请求接口移除文件
          this.$modal.confirm(`确认要移除文件 “${file.name}” 吗？`).then(() => {
            this.$modal.loading('正在移除文件，请稍候...')
            removeFile(file.response.data.id).then(res => {
              if (res.code === 200) {
                this.$message.success(`文件 “${file.name}” 移除成功`)
                this.$modal.closeLoading()
                resolve(true)
              } else {
                this.$message.error(`文件 “${file.name}” 移除失败，请重试`)
                this.$modal.closeLoading()
                reject(false)
              }
            }).catch(() => {
              this.$message.error(`文件 “${file.name}” 移除失败，请重试`)
              this.$modal.closeLoading()
              reject(false)
            })
          }).catch(() => {
            console.log('点了取消')
            reject(false)
          })
        }
      })
    },

    // 文件移除
    uploadRemove(file, fileList) {
      // 返回上传成功的文件列表
      // let uploadFileList = []
      // fileList.forEach((item) => {
      //   let ofId = item.response.data.ownerId + '-' + item.response.data.id
      //   uploadFileList.push(ofId)
      // })
      // this.$emit('input', uploadFileList.join(','))
      this.$emit('input', file.response.data.ownerId)
      console.log('返回的绑定值：', file.response.data.ownerId)
    },

    // 文件个数超出
    uploadExceed(files, fileList) {
      this.$modal.msgError(`上传文件数量不能超过 ${this.limit} 个!`)
    },
    
    // 文件上传列表点击预览时
    uploadPreview(file) {
      this.uploadPreviewImageUrl = file.url
      this.uploadPreviewDialog = true
    },
  }
}
</script>
<style scoped lang="scss">
// .el-upload--picture-card 控制加号部分
::v-deep.hide .el-upload--picture-card {
  display: none;
}

// 去掉动画效果
::v-deep .el-list-enter-active,
::v-deep .el-list-leave-active {
  transition: all 0s;
}

::v-deep .el-list-enter,
.el-list-leave-active {
  opacity: 0;
  transform: translateY(0);
}

.imgUploadBox {
  height: 100%;
  width: 100%;
  padding: 10px;
}
</style>

