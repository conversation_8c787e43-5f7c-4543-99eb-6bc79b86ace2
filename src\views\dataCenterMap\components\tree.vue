<template>
  <div class="tree" :class="treeClass">
    <div :style="{
      width: treeW + 'px',
      border: !isSpread ? '1px solid #0687FF' : '',
      padding: !isSpread ? '5px 10px' : '',
      maxHeight: oneMap ? 'calc(100vh - 15px)' : 'calc(100vh - 95px)',
    }" class="tree-operate" ref="treeBody" :class="isBig ? 'tree-operate-big' : ''">
      <template v-if="!isSpread">
        <el-row :gutter="20" class="mb10">
          <el-col :span="this.menuShowType === 1 && treeChecked ? 17 : 24" :offset="0" v-if="!clickBoolean">
            <el-input placeholder="输入名称进行过滤" v-model="keyWords">
              <template slot="suffix">
                <i class="el-icon-search input-search-btn" @click="toSearch"></i>
              </template>
            </el-input>
          </el-col>
          <el-col v-if="this.menuShowType === 1 && treeChecked" :span="6" :offset="0" style="padding: 0">
            <el-button type="primary" @click="handleCancelTree">取消选中</el-button>
          </el-col>
        </el-row>

        <div class="tree-body" :style="{ maxHeight: oneMap ? 'calc(100vh - 65px)' : 'calc(100vh - 145px)' }">
          <template v-if="clickBoolean && menuShowType == 2">
            <!-- <div v-if="healthMonitoring" class="healthMonitoring-title">结构物监测</div> -->

            <el-breadcrumb separator-class="el-icon-arrow-right">
              <el-breadcrumb-item v-for="(item, index) in breadcrumbData" :key="item.id">
                <el-link :style="{
                  color: index + 1 == breadcrumbData.length ? '#00FDFD' : '#dddddd',
                  fontSize: isBig ? '0.32vw' : '0.7vw'
                }" :underline="false" @click="handleBack(index)">
                  {{ item.name || "" }}
                </el-link>
              </el-breadcrumb-item>
            </el-breadcrumb>

            <div class="data-list mt5">
              <div class="list-item" v-if="!isLine">
                <div class="item-title" :style="{ backgroundImage: 'url(' + bgImgUlr + ')' }" v-if="breadcrumbData">
                  {{ breadcrumbData[0].name || "" }}
                  <!-- <div @click.stop="jumpClick" v-if="healthMonitoring" class="analysis-button">
                    {{ healthMonitoring.largeScreenButtonName }}
                  </div> -->
                </div>
                <div @click="handleClickAll(currentData)" class="item-content mt5 mb5"
                  :class="isClick ? 'item-content-list-active' : 'item-content-list-unactive'">
                  <ImgView :id="currentData.bigIconId" width="98" height="98" v-if="currentData.bigIconId"
                    class="my-1 ml-2" />
                  <img src="@/assets/home/<USER>" alt="" v-else />
                  <div class="base-content" v-if="currentData">
                    <span :style="{ fontSize: isBig ? '0.36vw' : '0.8vw' }">{{ currentData.name }}</span>
                    <div class="base-content-divider"></div>
                    <div class="base-content-data">
                      <span>{{ currentData.total || 0 }}</span>
                      <span :style="{ fontSize: isBig ? '0.32vw' : '0.7vw' }">{{ currentData.statUnit || "" }}</span>
                    </div>
                  </div>
                </div>
              </div>

              <div class="list-item" v-for="(item, index) in classifyData" :key="index">
                <div class="item-title" :style="{ backgroundImage: 'url(' + bgImgUlr2 + ')' }">
                  {{ item.subClassify.name == '结构物类型' ? '结构监测' : item.subClassify.name }}
                </div>
                <div class="item-content mt10 mb10" v-if="item.child">
                  <template v-if="!isLine">
                    <div class="item-content-list py-1"
                      :class="(documentActive === item1.id && !multiChoice) || (activeIds.includes(item1.id) && multiChoice) ? 'item-content-list-active' : ''"
                      v-for="(item1, index1) in item.child" :key="'stat' + index1" :style="setStyle(item)"
                      @click="handleItemClick(item1, item.subClassify)"
                      v-show="item.subClassify.name == '结构物类型' && deptIds.length === 0 ? item1.name !== '边坡' : true">
                      <span :title="item1.name" class="list-name" :style="{ fontSize: isBig ? '0.35vw' : '0.75vw' }">
                        {{ item1.name || " " }}
                      </span>
                      <div class="divider"></div>
                      <div class="list-number-unit">
                        <span class="list-number" :style="{ color: index == 1 ? '#00FDFD' : '#F2AF4A' }">
                          {{ item1.total || 0 }}
                        </span>
                        <span style="color: rgba(255, 255, 255, 0.64);"
                          :style="{ fontSize: isBig ? '0.3vw' : '0.55vw' }">
                          <sub>{{ item.subClassify.statUnit || "" }}</sub>
                        </span>
                      </div>
                    </div>
                  </template>
                  <template v-else>
                    <div class="line-list py-1" :class="documentActive === item1.id ? 'item-content-list-active' : ''"
                      v-for="(item1, index1) in item.child" :key="'stat' + index1"
                      @click="handleItemClick(item1, item.subClassify)">
                      <span class="line-name">{{ item1.name }}</span>
                      <span class="line-total">{{ item1.total }}</span>
                      <span class="line-total">{{ item1.total1 }}</span>
                    </div>
                  </template>
                </div>
              </div>
            </div>
          </template>
          <template v-else-if="treeData && treeData.length && routeType != '1'">
            <div class="tree-content" v-for="(item, index) in treeData" :key="index" @click="treeShow(item)"
              v-loading="treeLoading">
              <div class="tree-content-title">
                {{ item.name }}
                <el-button v-if="item.name === '基础设施图层'" :class="item.btnActive ? 'btn-active' : 'btn'"
                  type="primary" size="mini" @click.stop="handleResult(item, index)">默认图层</el-button>
                <span class="down" :style="{ transform: !item.spread ? '' : 'rotate(270deg)' }"></span>
              </div>

              <el-tree :data="item.child" :show-checkbox="menuShowType == 1 ? true : false" node-key="id"
                :expand-on-click-node="true" highlight-current :props="{ label: 'name', children: 'child' }"
                class="layer-tree" :ref="'treeRef' + index" v-show="!item.spread" @check="handleCheck"
                @node-click="handleNodeClick(item, $event)" @node-expand="handleExpand" :filter-node-method="filterNode"
                @check-change="(node, checked) => {
                  checkChangeClick(node, checked, index);
                }" :check-strictly="false">
                <div class="el-tree-node" slot-scope="{ data }" style="width: 100%">
                  <template v-if="menuShowType == 1">
                    <div class="tree-layer">
                      <span v-if="data.name" class="name">{{ data.name }}</span>
                      <span v-if="data.menuType !== 1 && !data.baseLayer" style="
                          color: #ffbe27;
                          display: flex;
                          align-items: center;
                          margin-left: auto;
                        " :style="{ fontSize: isBig ? '0.32vw' : '0.7vw' }">
                        {{ data.total || 0 }}
                        <img v-if="data.listQueryConfig" :src="require(`@/assets/map/${documentActive === data.id ? 'document-active.png' : 'document.png'}`)
                          " alt="" style="margin-left: 5px" @click.stop="handleDocument(data)" class="img-document" />
                      </span>
                    </div>
                  </template>
                  <template v-else>
                    <div class="tree-layer">
                      <ImgView :id="data.iconId" v-if="data.iconId" style="padding-top: 5px" />
                      <img src="@/assets/map/yh.png" class="img" v-else />
                      <span v-if="data.name" class="name" :style="{ fontSize: isBig ? '0.36vw' : '0.8vw' }">{{ data.name
                        }}</span>
                      <span class="total" :style="{ fontSize: isBig ? '0.32vw' : '0.7vw' }">
                        {{ data.name === '结构物监测图层' && deptIds.length === 0 ? 232 : data.total }} {{ data.statUnit || ""
                        }}
                      </span>
                      <img @click.stop="handleShowTable(data, false)" :src="require(`@/assets/map/${documentActive === data.id
                        ? 'document-active.png'
                        : 'document.png'
                        }`)
                        " class="img-document" />
                    </div>
                  </template>
                </div>
              </el-tree>
            </div>
          </template>
        </div>
      </template>
    </div>
    <div class="spread-packup" @click="handleSpread" :class="mToClass">
      <img src="@/assets/map/spread.png" :class="isSpread ? 'spread-img' : 'img'" />
    </div>
    <div v-if="!isSpread && currentData.name === '结构物监测图层' && showMoreBox" style="position: absolute;" :style="{
      width: treeW + 'px',
      border: !isSpread ? '1px solid #0687FF' : '',
      padding: !isSpread ? '5px 10px' : '',
      // maxHeight: oneMap ? 'calc(100vh - 15px)' : 'calc(100vh - 95px)',
      bottom: isBig ? '-330px' : '-175px',
      height: isBig ? '320px' : '170px',
      minHeight: isBig ? '320px' : '170px',
    }" class="tree-operate" :class="isBig ? 'tree-operate-big' : ''">
      <div class="tree-body">
        <template v-if="clickBoolean && menuShowType == 2">
          <div class="data-list mt5">
            <div class="list-item" v-for="(item, index) in classifyData" :key="index">
              <div class="item-title" :style="{ backgroundImage: 'url(' + bgImgUlr2 + ')' }">
                监测预警
              </div>
              <div class="item-content mt10 mb10">
                <div class="item-content-list py-1" :style="setStyle({ child: [1] })" @click="goPage">
                  <span class="list-name" :style="{ fontSize: isBig ? '0.35vw' : '0.75vw' }">
                    总数
                  </span>
                  <div class="divider"></div>
                  <div class="list-number-unit">
                    <span class="list-number" :style="{ color: index == 1 ? '#00FDFD' : '#F2AF4A' }">
                      121
                    </span>
                    <span style="color: rgba(255, 255, 255, 0.64);" :style="{ fontSize: isBig ? '0.3vw' : '0.55vw' }">
                      个
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </template>
      </div>
    </div>
  </div>
</template>

<script>
import { getShapeList, getClassifyDataStat } from "@/api/oneMap/layerData";
import { deptTreeSelect } from "@/api/tmpl";
import { getVectorTile } from "@/api/cockpit/index";
import screenfull from "screenfull";

import { mapState, mapActions, mapMutations } from "vuex";
import ImgView from "./common/imgView.vue";
import {
  addWidthFeature,
  removeLayer,
  removeAllLayer,
  getImageById,
  addVectorTile,
  toBack,
  getLineConfig,
  onMatching,
  lineId,
  addHeatmap,
  removeHeatLayer,
} from "./common/mapFun";
import TileLayer from "ol/layer/Tile";
import XYZ from "ol/source/XYZ";
import cache from "@/plugins/cache";
import { isBigScreen } from "./common/util";
import store from "@/store";
import { iframeDialog } from "./common/iframe";

export default {
  components: {
    ImgView,
  },
  data() {
    return {
      keyWords: "",
      deptIds: [],
      deptOptions: [],
      classifyData: null,
      currentData: {},
      breadcrumbData: [],
      bgImgUlr: require("@/assets/map/title-bg.png"),
      bgImgUlr2: require("@/assets/map/title-bg-2.png"),
      menuType: null,
      params: {},
      indexNum: 0, // 用数值来判断只加载一次
      ltNum: 0, // 用数值来判断只加载一次
      listQueryConfig: {}, // 列表配置参数
      parentNode: {}, // 点击的父节点
      treeLoading: false, // 树加载
      documentActive: "",
      singleRangeData: [],
      singleNode: undefined,
      treeChecked: false, // 是否有选中树
      oneMap: false,
      mIds: [], // 路段id
      loadQueryType: false,
      healthMonitoring: null,
      routeType: '',
      isBig: isBigScreen(),
      isClick: false,
      isLine: false, // 基础数据路线
      rowAll: null,
      multiChoice: false, // 是否点击了可多选
      activeIds: [], // 当前选中的id
      activeList: [], // 多选当前存储
      otherConfigData: [], // 其他配置
      pbfObj: null, // 矢量瓦片请求参数
      currentObj: null, // 参数请求
      showMoreBox: false,
    };
  },
  async created() {
    this.routeType = this.$route.query.type || ''

    await this.getDeptList();
    this.treeLoading = true;
    let p = store.getters.roles?.includes("admin")
      ? []
      : this.authDeptIds || (this.deptId === 1 ? [] : (this.deptId ? [this.deptId] : []));
    // 获取树数据
    this.getTreeList({ deptIds: p }).then((res) => {
      this.treeLoading = false;
    });
    // 页面刷新 或 取消操作
    this.handleCancel();
    if (this.$route.name == "oneMap") {
      this.oneMap = true;
    }
    window.$Bus.$on("reSet", () => {
      this.documentActive = "";
    });
    window.$Bus.$on("refreshMap", () => {
      if (this.menuShowType === 2) {
        removeLayer(window.mapLayer, "pbfLayer", "name");
        addVectorTile(
          window.mapLayer,
          "name",
          "pbfLayer",
          JSON.stringify(this.pbfObj),
          this.rowAll
        );
        if (this.currentObj) {
          let { params, item, node } = this.currentObj;
          this.getClassifyData(params, item, node);
        }
      } else if (this.menuShowType === 1) {
        // 1、重新获取数据
        this.getTreeList({ deptIds: this.deptIds, isRefresh: true }).finally(() => { });
        // 2、地图缩放
        if (this.mapZoom > 0) {
          window.mapLayer.getView().setZoom(this.mapZoom + 1);
        }
      }
      // 关闭表格弹窗
      this.getTableShow(false);
    });
  },
  mounted() {
    // 监听地图点击
    window.$Bus.$on("mapClick", (data) => {
      // *修改当前 数据 总数
      // this.currentData.total = data.total || 0
      if (this.legendList && this.legendList.length) {
        // 重新获取数据
        let params = {
          managementMaintenanceIds: [data.dept_id || data.sys_dept_id],
          id: this.legendList[0].id,
          menuType: this.legendList[0].menuType,
          z: this.mapZoom,
        };
        this.getClassifyData(params, this.parentNode);
      }
    });
    window.$Bus.$on("treeClick", (data, isHighway) => {
      this.documentActive = "";
      this.isClick = false;
      this.getTableShow(false);
      // this.changeClickBoolean(false);
      this.deptIds = data.deptIds;
      this.mIds = data.maintenanceSectionIds;
      // 获取树数据
      this.treeLoading = true;
      this.getTreeList({
        deptIds: data.deptIds,
        maintenanceSectionIds: this.mIds,
      }).then((res) => {
        this.treeLoading = false;
        if (isHighway) {
          // 进入这一步说明dpTree点击的是高速路，调用方法时多给一个参数true
          this.handleShowTable(res[0].child[0], true)
        }
      });
      this.$nextTick(() => {
        this.setCatalogW({ catalogW: this.$refs.treeBody?.clientWidth || 0 });
      });
      window.addEventListener("resize", () => {
        this.$nextTick(() => {
          this.setCatalogW({ catalogW: this.$refs.treeBody?.clientWidth || 0 });
        });
      });
    });
    // 监听切换类型
    window.$Bus.$on('reSetDept', () => {
      this.deptIds = [];
      this.mIds = [];
      removeLayer(window.mapLayer, 'highwayLayer');
      removeLayer(window.mapLayer, "highwayLayer", "name");
    });
  },
  destroyed() {
    window.$Bus.$off("mapClick");
    window.$Bus.$off("treeClick");
    window.$Bus.$off("reSet");
    window.$Bus.$off("reSetDept", () => { });
    window.$Bus.$off("refreshMap", () => { });
  },
  computed: {
    ...mapState({
      treeW: (state) => state.map.treeW,
      isSpread: (state) => state.map.isSpread,
      menuShowType: (state) => state.map.menuShowType,
      treeData1: (state) => state.map.treeData1,
      treeData2: (state) => state.map.treeData2,
      clickBoolean: (state) => state.map.clickBoolean,
      mapZoom: (state) => state.map.mapZoom,
      deptId: (state) => state.user.deptId,
      showLevel: (state) => state.map.showLevel,
      legendList: (state) => state.map.legendList,
      authDeptIds: (state) => state.map.authDeptIds,
      deptShow: (state) => state.map.deptShow,
    }),
    treeData() {
      // 设置静态数据-为大数据中心展示所用
      const list = this.menuShowType == 1 ? this.treeData1 : this.treeData2;
      if (list && list.length) {
        list[1].child = list[1]?.child.filter(item => item.name !== '急弯路段');
        if (this.menuShowType == 1) {
          list[0]?.child.map(el => {
            switch (el.name) {
              case '路线':
                el.total = 78;
                break;
              case '桥梁':
                el.total = 14110;
                break;
              case '隧道':
                el.total = 1405;
                break;
            }
          })
        } else {
          if (this.deptIds.length === 0) {
            list[0]?.child.map(el => {
              switch (el.name) {
                case '路线':
                  el.total = 78;
                  break;
                case '桥梁':
                  el.total = 14110;
                  break;
                case '隧道':
                  el.total = 1405;
                  break;
              }
            })
          }
        }
      }

      return list;
    },
    mToClass() {
      return this.isSpread ? "closeMTop" : "";
    },
    treeClass() {
      return this.deptShow ? "tree-1" : "tree-2";
    },
  },
  watch: {
    menuShowType(newVal, oldVal) {
      this.documentActive = "";
      const list = window.mapLayer.getLayers().getArray();
      const arr = [];
      list.map((layer) => {
        // 移除勾选的图层
        const val = layer.get("treeLayerName");
        if (val) arr.push(layer);
        // 移除表格点击加载的闪烁高亮图层
        if (layer.get("name") === "clickLayer") {
          arr.push(layer);
        }
      });
      arr.map((l) => {
        window.mapLayer.removeLayer(l);
      });
    },
    treeData1(newVal, oldVal) {
      if (!newVal) return;
      this.$nextTick(() => {
        this.setCatalogW({ catalogW: this.$refs.treeBody?.clientWidth || 0 });
      });
    },
    treeData2(newVal, oldVal) {
      if (!newVal) return;
      this.$nextTick(() => {
        this.setCatalogW({ catalogW: this.$refs.treeBody?.clientWidth || 0 });
      });
    },
    treeData(newVal) {
      // 在home路由中进入时默认加载
      if (
        newVal &&
        this.routeType == "1" &&
        !this.loadQueryType
      ) {
        this.$nextTick(() => {
          newVal.forEach((item) => {
            item.child.forEach((data) => {
              let listQueryConfig = JSON.parse(data.listQueryConfig);
              if (listQueryConfig.healthMonitoring) {
                this.handleNodeClick(item, data);
              }
            });
          });
          this.loadQueryType = true;
          return
        });
      }
      if (newVal && this.clickBoolean) {
        this.$nextTick(() => {
          newVal.forEach((item) => {
            item.child.forEach((data) => {
              if (data.name && data.name == this.breadcrumbData[1].name) {
                this.handleNodeClick(item, data);
              }
            });
          });
        });
      }
    },
  },
  methods: {
    ...mapActions({
      setSpread: "map/setSpread",
      getTreeList: "map/getTreeList",
      changeClickBoolean: "map/changeClickBoolean",
      getLegend: "map/getLegend",
      getTableShow: "map/getTableShow",
      getDetlCondf: "map/getDetlCondf",
      getShowLevel: "map/getShowLevel",
      legendShowOrHide: "map/legendShowOrHide",
    }),
    ...mapMutations({
      setTableHeader: "map/setTableHeader",
      setAuthDeptIds: "map/setAuthDeptIds",
      setCatalogW: "map/setCatalogW",
    }),
    setStyle(item) {
      return {
        width:
          item.child.length <= 3 ? 100 / item.child.length - 3.5 + "%" : "30%",
      };
    },
    goPage() {
      window.open(`https://zhyhpt.yciccloud.com/jgjc/earlyWarning?isFull=1`, '_blank')
    },
    async getDeptList() {
      return new Promise((resolve, reject) => {
        deptTreeSelect({ types: 201 })
          .then((res) => {
            if (res.code == 200) {
              let deptIds = res.data.map((v) => v.id);
              this.setAuthDeptIds({ deptIds });
              this.deptOptions = [
                {
                  id: "",
                  label: "集团公司",
                  children: res.data || [],
                },
              ];
            }
            // 加载成功 返回
            this.$emit("finish");
            resolve("");
          })
          .catch((err) => {
            reject(err);
          });
      });
    },
    jumpClick() {
      this.$router.push(`/cockpit?jumpType=1`);
      // let param = {
      //   componentL: 'HealthIndex',
      //   componentR: "",
      //   jumpType: 1,
      // };
      // iframeDialog({ iframeUrl: null, componentName: 'subpage', params: param, isBack: false })
    },
    // 左侧数 展开收起
    handleSpread() {
      let bigBool = isBigScreen();
      let width = bigBool ? 640 : 320;
      this.setSpread(this.treeW == 0 ? width : 0);
      this.$nextTick(() => {
        this.setCatalogW({ catalogW: this.$refs.treeBody?.clientWidth || 0 });
      });
    },
    // 一级树展开收起
    treeShow(row) {
      row.spread = !row.spread;
      this.$forceUpdate();
    },
    // 搜索
    toSearch() {
      this.treeData.forEach((item, index) => {
        if (this.$refs["treeRef" + index]) {
          let ref = this.$refs["treeRef" + index][0];
          ref.filter(this.keyWords);
        }
      });
    },
    filterNode(value, data) {
      if (!value) return true;
      if (!data) return true;
      return data.name.indexOf(value) !== -1;
    },
    // 选中树
    async handleCheck(node, nodes) {
      let { checkedKeys, checkedNodes } = nodes;
      let { layerMenuSubId, listQueryConfig, child } = node;
      let menuId = null;
      let config = null;
      let params = {};
      if ((!layerMenuSubId || !listQueryConfig) && (child && child.length)) {
        menuId = child[0].layerMenuSubId;
        config = child[0].listQueryConfig ? JSON.parse(child[0].listQueryConfig) : child[0].listQueryConfig;
      } else {
        menuId = checkedNodes && checkedNodes.length ? checkedNodes[0].layerMenuSubId : layerMenuSubId;
        config = listQueryConfig ? JSON.parse(listQueryConfig) : listQueryConfig;
      }
      // 配置里添加 heat 为true 时添加热力图
      let isHeat = config.heat || false;
      let heatLayer = 'heatLayer' + menuId
      if (!isHeat) return;
      // 获取最下级的节点
      let arr = checkedNodes.filter(f => !f.child);
      let typeArr = arr.map(v => v.name)
      params.id = menuId;
      params.paramsDTO = {
        precisionParams: {
          asset_type: typeArr || [],
        }
      }
      // 完全取消
      if (checkedKeys.length == 0) {
        removeLayer(window.mapLayer, heatLayer, 'name');
        // 移除带有 heatLayer 的图层
        removeHeatLayer();
        return;
      } else {
        removeLayer(window.mapLayer, heatLayer, 'name');
        removeHeatLayer();
        let jsonData = await this.getGeoJson(params);
        if (!jsonData) return;
        addHeatmap(jsonData, 'name', heatLayer);
      }
    },
    // 获取shape
    async getShape(row, lId = null) {
      let deptIdArr = this.deptId == 1 ? [] : (this.deptId ? [this.deptId] : []);
      let params = {
        maintenanceSectionIds: this.mIds || undefined,
        managementMaintenanceIds: [...this.deptIds, ...deptIdArr] || [],
        id: lId || row.id,
        menuType: row.menuType == "" ? "" : this.menuType,
        menuSubClassifyId: row.menuSubClassifyId,
        z: this.mapZoom,
        paramsDTO: {
          precisionParams: row.precisionParams || null,
          ks: "",
        },
      };
      this.params = params;

      return new Promise(async (resolve, reject) => {
        await getShapeList(params)
          .then((res) => {
            if (res.code == 200 && res.data) {
              // 获取所有的 缓存部门数据
              let deptArr = onMatching();
              let arr = res.data || [];
              for (let item of arr) {
                const foundItem = deptArr.find(
                  (longerItem) =>
                    longerItem.sys_dept_id === item.sys_dept_id ||
                    longerItem.sys_dept_id == item.dept_id
                );
                if (foundItem) {
                  item.mgeom = foundItem.mgeom;
                }
              }
              resolve(arr);
              return arr;
            } else {
              resolve([]);
            }
          })
          .catch(() => {
            reject("");
          });
      });
    },
    // 展开树节点
    async handleExpand(obj, node) { },
    // 点击树节点
    async handleNodeClick(item, node) {
      if (this.menuShowType == 1) return;
      if (node.listQueryConfig && Object.prototype.toString.call(node.listQueryConfig) == '[object String]') {
        let config = JSON.parse(node.listQueryConfig)
        if (config.line) {
          this.isLine = true
        } else {
          this.isLine = false
        }
      }
      // 先关闭表格
      this.getTableShow(false);
      this.getLegend([]);
      this.documentActive = "";
      this.parentNode = item;
      this.menuType = node.menuType || null;
      let isAdmin = this.$store.getters.roles.includes("admin");
      let deptIdArr = this.deptId == 1 || isAdmin ? [] : (this.deptId ? [this.deptId] : []);
      let params = {
        maintenanceSectionIds: this.mIds,
        managementMaintenanceIds: [...this.deptIds, ...deptIdArr] || [],
        id: node.id,
        menuType: node.menuType,
        z: this.mapZoom,
      };
      if (!node.id) {
        node.child = [];
        return;
      }

      // 获取当前 地图显示层级
      this.getShowLevel(node.showLevel);
      // 加载图标
      let iconImg = [];
      if (node.figureId) {
        iconImg = await getImageById(node.figureId);
        node.icon = iconImg[0];
      }
      this.listQueryConfig = node.listQueryConfig || {};
      let config = null;
      if (typeof node.listQueryConfig == "string") {
        config = JSON.parse(node.listQueryConfig);
      } else {
        config = node.listQueryConfig;
      }
      this.healthMonitoring = config.healthMonitoring || null;
      // 将表头信息保存到状态管理中
      this.setTableHeader({ header: config.tableHeader });
      if (config && config.detlCondf) {
        // 设置详情配置
        this.getDetlCondf(config.detlCondf);
      }
      // 设置图列数据
      node.layerName = "pbfLayer";
      node.ifShow = true;
      node.opacity = 100;
      this.getLegend([node]);
      // 获取 对应数据
      await this.getClassifyData(params, item, node);
      if (this.menuShowType == 2) {
        this.singleRangeData = [];
        let obj = {
          managementMaintenanceIds: [...this.deptIds, ...deptIdArr] || [],
          id: node.id,
          menuType: node.menuType,
          maintenanceSectionIds: this.mIds,
          menuSubClassifyId: item.subClassifyId || node.subClassifyId || '',
        };
        this.rowAll = node;
        // 加载热力图
        if (config.ifGeojson) {
          removeLayer(window.mapLayer, 'heatLayer', 'name');
          removeHeatLayer();
          let jsonData = await this.getGeoJson(obj);
          if (!jsonData) return;
          addHeatmap(jsonData, 'name', 'heatLayer');
        } else {
          addVectorTile(
            window.mapLayer,
            "name",
            "pbfLayer",
            JSON.stringify(obj),
            node
          );
        }
        let arr = await this.getShape(node);
        if (!arr || (arr && !arr.length)) return;
        this.singleRangeData = arr;
        // 点击树节点移除地图数据
        removeLayer(window.mapLayer, "dataLayer", "name");
        removeLayer(window.mapLayer, "dataLayer");
        // 将数据加载到地图
        addWidthFeature(window.mapLayer, arr, "dataLayer", null, true, this.$store.state.map.deptMinZoom);
        this.singleNode = node;
      }
      // 移除点击表格事件的图层数据
      // removeLayer(window.mapLayer, 'clickLayer')
    },
    // 获取树下级数据
    getClassifyData(params, item, node = null) {
      this.currentObj = { params, item, node };
      let nodeData = {};
      if (this.menuShowType == 1) {
        node.loading = true;
      } else {
        nodeData = node || this.legendList[0];
      }
      return new Promise(async (resolve, reject) => {
        getClassifyDataStat(params).then(async (res) => {
          if (res.code == 200 && res.data) {
            // 设置静态数据-为大数据中心展示所用
            if (this.menuShowType == 2 && params.maintenanceSectionIds.length === 0 && params.managementMaintenanceIds.length === 0) {
              if (nodeData.name === '桥梁' || nodeData.name === '隧道' || nodeData.name === '路线') {
                res.data[0]?.child.map(el => {
                  switch (el.name) {
                    case '地高':
                      el.total1 = '1922.699km'
                      break;
                    case '合计':
                      el.total1 = '6013.358km'
                      break;
                    case '中桥':
                      el.total = 4551
                      break;
                    case '中隧道':
                      el.total = 335
                      break;
                  }
                })
                res.data[1]?.child.map(el => {
                  switch (el.name) {
                    case '项目公司':
                      el.total1 = '2755.651km'
                      break;
                    case '合计':
                      el.total1 = '6013.358km'
                      break;
                  }
                })
                res.data[2]?.child.map(el => {
                  switch (el.name) {
                    case '集团公司':
                      el.total = '42条'
                      break;
                    case '项目公司':
                      el.total = '36条'
                      el.total1 = '2755.651km'
                      break;
                    case '合计':
                      el.total = '78条'
                      el.total1 = '6013.358km'
                      break;
                  }
                })
              }
              if (nodeData.name === '结构物监测图层') {
                res.data[0].child.map(el => {
                  switch (el.name) {
                    case '桥梁':
                      el.total = 108
                      break;
                    case '边坡':
                      el.total = 93
                      break;
                    case '隧道':
                      el.total = 3
                      break;

                  }
                })
              }
            }
            let data = res.data.map((v) => v.subClassify);
            // otherConfig配置
            this.otherConfigData = data || [];
            this.classifyData = res.data || [];
            if (this.menuShowType == 2) {
              this.currentData = JSON.parse(JSON.stringify(nodeData));
              // 设置静态数据-为大数据中心展示所用
              if (params.maintenanceSectionIds.length === 0 && params.managementMaintenanceIds.length === 0) {
                if (this.currentData.name === '桥梁') {
                  this.currentData.total = 14110
                }
                if (this.currentData.name === '隧道') {
                  this.currentData.total = 1405
                }
                if (this.currentData.name === '结构物监测图层') {
                  this.currentData.total = 111
                  this.currentData.child = this.currentData.child.filter(item => item.name !== '边坡')
                  this.showMoreBox = true
                }
              } else { this.showMoreBox = false }
              this.breadcrumbData = [item, nodeData];
              this.changeClickBoolean(true);
            } else {
            }
            if (data && data.length > 0) {
              // 点击地图-到子级部门
              if (!node) {
                this.currentData = JSON.parse(JSON.stringify(data[0]));
                let obj = this.legendList[0];
                this.currentData.name = obj.name;
                this.currentData.bigIconId = obj.bigIconId;
                // 先移除
                removeLayer(window.mapLayer, "dataLayer");
                removeLayer(window.mapLayer, "dataLayer", "name");
                let arr = await this.getShape(params);
                // 将数据加载到地图
                addWidthFeature(window.mapLayer, arr, "dataLayer", null, true, this.$store.state.map.deptMinZoom);
              }
            }
            resolve(this.otherConfigData);
          } else {
            resolve([]);
          }
        }).catch(err => {
          reject(err);
        });
      });
    },
    // 返回树 结构
    async handleBack(index) {
      this.isClick = false;
      this.documentActive = "";
      this.multiChoice = false;
      this.activeIds = [];
      this.activeList = [];
      this.showMoreBox = false
      if (this.routeType == '1') {
        return
      }
      if (index == 0) {
        this.changeClickBoolean(false);
        this.getTableShow(false);
        // 置空图列数据
        this.getLegend([]);
        removeLayer(window.mapLayer, "pbfLayer");
        removeLayer(window.mapLayer, "pbfLayer", "name");
        removeLayer(window.mapLayer, "clickLayer");
        removeLayer(window.mapLayer, "dataLayer", "name");
        // 避免失效调用2次清空
        removeLayer(window.mapLayer, "dataLayer");
        // 移除热力图
        removeLayer(window.mapLayer, 'heatLayer', 'name');
        removeHeatLayer();
        // removeLayer(window.mapLayer, "maskLayer", "name");
        // removeLayer(window.mapLayer, 'pbfLayer', 'lineLayer')
        // getLineConfig([this.deptId])
        // 移除图层并重新添加
        this.rowAll = null;
        await toBack(this.deptIds, this.mIds)
        let highwayLayer = window.mapLayer.getLayers().getArray().find((v) => {
          return v.get("name") == "highwayLayer";
        });
        setTimeout(() => {
          if (highwayLayer) {
            const arr = highwayLayer.getSource().getFeatures()
            arr.map(feature => {
              let view = window.mapLayer.getView()
              // 跳转到地图
              view.fit(feature.getGeometry().getExtent(), {
                duration: 500,
                maxZoom: this.isBig ? 12 : 10,
                padding: [0, 500, 0, 0],
              });
            })
          }
        }, 500);
        cache.session.remove('otherData');
      }
    },
    handleNodeCheck(_node, nodes) {
      this.deptIds =
        nodes.checkedKeys.filter(
          (v) => v != "" && v != null && v != undefined
        ) || [];
      // 触发相关联操作
    },
    // 显示表格数据
    getAllTable(row, customParams, tableShow = true, isHighway = false) {
      let paramsDTO = { precisionParams: {} };
      if (row.columnName) {
        paramsDTO = {
          precisionParams: {
            [row.columnName]: row.value,
          },
        };
      }
      paramsDTO.precisionParams.customParams = customParams;
      let deptIdArr = this.deptId == 1 ? [] : (this.deptId ? [this.deptId] : []);
      let obj = {
        query: this.listQueryConfig || "{}",
        params: {
          ...this.params,
          menuType: this.menuType,
          id:
            row && row.layerMenuSubId
              ? row.layerMenuSubId
              : row.id
                ? row.id
                : undefined,
          paramsDTO: paramsDTO,
          managementMaintenanceIds: [...this.deptIds, ...deptIdArr] || [],
          maintenanceSectionIds: this.menuShowType == 1 ? undefined : this.mIds,
        },
        menuShowType: this.menuShowType,
        row: row,
      }
      // 全局触发获取表格数据
      if (isHighway) {
        window.$Bus.$emit("getTable", obj, isHighway);
      } else {
        window.$Bus.$emit("getTable", obj);
      }
      // 显示右侧 table
      this.getTableShow(tableShow);
    },
    // 监听清空事件
    selectClear() {
      // 清空树选中数据
      this.$refs.treeRef.setCheckedKeys([]);
    },
    // 点击事件
    async handleItemClick(row, item) {
      // 2025-3-7
      let otherConfig = item.otherConfig ? JSON.parse(item.otherConfig) : item.otherConfig || {};
      let config = row.listQueryConfig ? JSON.parse(row.listQueryConfig) : {};
      this.multiChoice = otherConfig.mult_choice || false;
      // 存储 otherConfig 用于 详情页使用
      if (this.otherConfigData && this.otherConfigData.length) {
        let cId = item.id || row.subClassifyId;
        let otherData = this.otherConfigData.filter(v => v.id == cId);
        cache.session.setJSON('otherData', otherData);
      }
      if (item.iconId.includes('[')) {
        let arr = JSON.parse(item.iconId);
        let iconList = arr.filter(v => v.name === row.name);
        let iconIds = iconList && iconList.length ? iconList[0].iconId : '';
        let iconId = typeof iconIds === 'string' ? iconIds : iconIds[0];
        let iconImg = await getImageById(iconId);
        item.icon = iconImg ? iconImg[0] : '';
        row.icon = iconImg ? iconImg[0] : '';
      }
      if (this.multiChoice) {
        // 判断是否已经存在
        let findIndex = this.activeList.findIndex(v => v.subClassifyId === row.subClassifyId);
        if (findIndex != -1) {
          // 如果已存在则移除
          let cIndex = this.activeList.findIndex(v => v.id === row.id);
          if (cIndex != -1) {
            this.activeIds.splice(cIndex, 1);
            this.activeList.splice(cIndex, 1);
          } else {
            this.activeIds[findIndex] = row.id;
            this.activeList[findIndex] = row;
          }
        } else {
          this.activeIds.push(row.id);
          this.activeList.push(row)
        }
        this.activeIds = [...new Set(this.activeIds)];

      } else {
        this.activeIds = [];
        this.activeList = [];
      }
      // 2025-3-7
      this.isClick = false;
      if (this.documentActive !== row.id && !this.multiChoice) {
        this.documentActive = row.id;
      } else if (this.activeList && !this.activeList.length) {
        this.documentActive = "";
        const list = window.mapLayer.getLayers().getArray();
        const arr = [];
        list.map((layer) => {
          if (
            layer.get("name") === "dataLayer" ||
            layer.get("name") === "pbfLayer" ||
            layer.get("name") === "clickLayer"
          ) {
            arr.push(layer);
          }
        });
        arr.map((l) => {
          window.mapLayer.removeLayer(l);
        });
        let data = cache.session.getJSON("rangeData");
        let deptData = [];
        if (this.deptIds && this.deptIds.length) {
          deptData = data.filter(v => this.deptIds.includes(v.sys_dept_id))
        } else {
          deptData = data || [];
        }
        if (this.singleRangeData && this.singleRangeData.length) {
          deptData = this.singleRangeData
        }
        // 先移除
        removeLayer(window.mapLayer, "dataLayer");
        removeLayer(window.mapLayer, "dataLayer", 'name');
        addWidthFeature(
          window.mapLayer,
          deptData,
          "dataLayer",
          null,
          true,
          this.$store.state.map.deptMinZoom
        );
        let deptIdArr = this.deptId == 1 ? [] : (this.deptId ? [this.deptId] : []);
        if (!this.singleNode) {
          this.singleNode = this.rowAll;
        };
        let obj = {
          managementMaintenanceIds: [...this.deptIds, ...deptIdArr] || [],
          id: this.singleNode ? this.singleNode?.id : null,
          menuType: this.singleNode.menuType,
          maintenanceSectionIds: this.mIds,
          menuSubClassifyId: row.subClassifyId || '',
        };
        if (!this.isLine) {
          let iconImg = await getImageById(this.singleNode.figureId || this.rowAll.figureId);
          if (this.multiChoice) {
            this.singleNode.icon = iconImg[0] || this.singleNode.icon;
          }
          // 加载热力图
          if (config.ifGeojson) {
            removeLayer(window.mapLayer, 'heatLayer', 'name');
            removeHeatLayer();
            let jsonData = await this.getGeoJson(obj);
            if (!jsonData) return;
            addHeatmap(jsonData, 'name', 'heatLayer');
          } else {
            addVectorTile(
              window.mapLayer,
              "name",
              "pbfLayer",
              JSON.stringify(obj),
              this.singleNode
            );
          }
        }
        this.getTableShow(false);
        return;
      }
      row.precisionParams = {};
      // row.precisionParams[row.columnName] = row.value || null;
      // let config = JSON.parse(row.listQueryConfig);
      // row.precisionParams.customParams = config.listQueryParams || null;
      // 2025-3-8 ↓
      if (this.multiChoice && this.activeList && this.activeList.length) {
        // 处理“灾害风险点”的图标展示逻辑
        let strArr = ['一级', '二级', '三级', '四级']
        const flag = this.activeList.length > 0 && strArr.some(element => element === this.activeList[this.activeList.length - 1].value);
        const isMatch = item.name === '风险等级' ? true : false
        if (isMatch && this.activeList.length == 1 && flag) {
          let iconImg = await getImageById(row?.figureId);
          this.rowAll.icon = iconImg[0]
        }
        if (isMatch && this.activeList.length == 2) {
          // let iconImg = await getImageById(row?.figureId);
          // this.rowAll.icon = iconImg[0]
        }
        if (!isMatch) {
          this.rowAll.icon = item.icon || row.icon;
        }
        if (!isMatch && this.activeList.length == 1 && flag) {
          let iconImg = await getImageById(this.activeList[0].figureId);
          this.rowAll.icon = iconImg[0]
        }
        if (this.activeList.length > 1) {
          this.activeList.forEach(item => {
            item.precisionParams[item.columnName] = item.value || null;
            item.precisionParams.customParams = config.listQueryParams || null;
            row.precisionParams = {
              ...item.precisionParams,
              ...row.precisionParams,
            }
          })
        } else {
          this.activeList.forEach(item => {
            item.precisionParams = {};
            row.precisionParams[item.columnName] = item.value || null;
            row.precisionParams.customParams = config.listQueryParams || null;
            item.precisionParams[item.columnName] = item.value || null;
            item.precisionParams.customParams = config.listQueryParams || null;
            if (!this.rowAll.icon) {
              this.rowAll.icon = item.icon
            }
          })
        }
      } else {
        row.precisionParams[row.columnName] = row.value || null;
        row.precisionParams.customParams = config.listQueryParams || null;
      }
      // 2025-3-8 ↑
      let obj = {
        ...row,
        ...item,
        id: row.layerMenuSubId || item.layerMenuSubId,
        menuSubClassifyId: row.subClassifyId || null,
        menuType: "",
      };
      let deptIdArr = this.deptId == 1 ? [] : (this.deptId ? [this.deptId] : []);
      let params = {
        // deptIds: [...this.deptIds, ...deptIdArr] || [],
        managementMaintenanceIds: [...this.deptIds, ...deptIdArr] || [],
        maintenanceSectionIds: this.mIds,
        id: row.id,
        menuType: row.menuType == "" ? "" : this.menuType,
        z: this.mapZoom,
        paramsDTO: {
          precisionParams: {
            ...row.precisionParams,
          },
          ks: "",
        },
      };

      let arr = await this.getShape(obj);
      let dataArr = cache.session.getJSON("rangeData");
      if (dataArr && dataArr.length) {
        dataArr.forEach(item => {
          for (let i = 0; i < arr.length; i++) {
            if (item.sys_dept_id == arr[i].sys_dept_id) {
              item.name = arr[i].typeName || arr[i].name;
            }
          }
        })
      } else {
        dataArr = arr;
      }
      if (this.deptIds && this.deptIds.length) {
        dataArr = dataArr.filter(i => this.deptIds.includes(i.sys_dept_id));
      }
      if (arr && arr.length) {
        // 先移除
        removeLayer(window.mapLayer, "dataLayer");
        removeLayer(window.mapLayer, "dataLayer", 'name');
        // 将数据加载到地图
        addWidthFeature(window.mapLayer, dataArr, "dataLayer", null, true, this.$store.state.map.deptMinZoom);
      }
      removeLayer(window.mapLayer, "pbfLayer", "name");

      let objs = {
        managementMaintenanceIds: [...this.deptIds, ...deptIdArr] || [],
        id: item.layerMenuSubId || row.id,
        menuType: "",
        paramsDTO: {
          precisionParams: {
            ...row.precisionParams,
          },
          ks: "",
        },
        maintenanceSectionIds: this.mIds,
        menuSubClassifyId: row.subClassifyId || '',
      };
      this.pbfObj = objs;
      // 加载热力图
      if (config.ifGeojson) {
        removeLayer(window.mapLayer, 'heatLayer', 'name');
        removeHeatLayer();
        let jsonData = await this.getGeoJson(objs);
        if (!jsonData) return;
        addHeatmap(jsonData, 'name', 'heatLayer');
      } else {
        addVectorTile(
          window.mapLayer,
          "name",
          "pbfLayer",
          JSON.stringify(objs),
          this.rowAll
        );
      }

      removeLayer(window.mapLayer, "clickLayer", "name");

      // 触发列表数据加载
      // 全局触发获取表格数据
      let tableObj = {
        query: row.listQueryConfig || this.listQueryConfig || "{}",
        params: { ...params, ...obj },
        menuShowType: this.menuShowType,
        row: row,
        managementMaintenanceIds: [...this.deptIds, ...deptIdArr] || [],
        maintenanceSectionIds: this.mIds,
      }

      window.$Bus.$emit("getTable", tableObj);
      let conf = row.listQueryConfig || this.listQueryConfig || {};
      if (typeof conf == "string") {
        conf = JSON.parse(conf)
      }
      this.setTableHeader({ header: conf.tableHeader });
      // 显示右侧 table
      this.getTableShow(true);
    },
    // 取消事件 2024年8月1日10:07:46 取消地图图层数据-重置数据
    async handleCancel() {
      // 清除图层
      // removeAllLayer(window.mapLayer);
      removeLayer(window.mapLayer, "dataLayer");
      removeLayer(window.mapLayer, "pbfLayer");
      // 重新加载 - 基础数据
      this.changeClickBoolean(false);
      this.getTableShow(false);
      this.getDetlCondf({});
    },
    // 展开选中的未展开的节点
    expandCheckedNotExpandNodes(node, index) {
      let tree = this.$refs["treeRef" + index][0];
      if (node.checked && !node.expanded && !node.isLeaf) {
        node.expand(function () {
          let childNodes = node.childNodes;
          for (let i = 0; i < childNodes.length; i++) {
            let childNode = childNodes[i];
            //手动触发check-change事件，事件处理函数中回继续调用此函数，形成递归展开
            tree.$emit(
              "check-change",
              childNode.data,
              childNode.checked,
              index,
              childNode.indeterminate
            );
          }
        });
      }
    },
    async checkChangeClick(nodeData, checked, index) {
      let tree = this.$refs["treeRef" + index][0];
      let node = tree.getNode(nodeData);
      let value = nodeData?.columnName
        ? nodeData?.columnName + nodeData?.value || nodeData.id
        : nodeData.id;

      this.$nextTick(() => {
        const checkedNodes = this.$refs["treeRef" + index][0].getCheckedNodes(
          false,
          true
        );
        let arr = [];
        checkedNodes.map((el) => {
          arr.push(this.$refs["treeRef" + index][0].getNode(el));
        });
        arr = arr.filter((el) => el?.level == 1);
        // 获取当前树素有选中
        // let legendArr = []
        // this.treeData.forEach((item, index1) => {
        //   let checkArr = this.$refs['treeRef' + index1][0].getCheckedNodes(false, true)
        //   if (checkArr) {
        //     legendArr = legendArr.concat(checkArr)
        //   }
        // })
        let legendArr = this.getTreeCheckedData();
        legendArr = legendArr.filter((v) => v.menuType == 2);
        this.treeChecked = legendArr.length > 0 ? true : false;
        this.getLegend(legendArr);
        // 设置详情配置
        // this.getDetlCondf(config.detlCondf)
        // this.getLegend(arr.map(v => v.data))
      });

      //展开选中的未展开的节点
      this.expandCheckedNotExpandNodes(node, index);
      if (checked) {
        let iconImg = [];
        if (nodeData.figureId && !nodeData.icon) {
          iconImg = await getImageById(nodeData.figureId);
          nodeData.icon = iconImg[0];
        }

        let deptIdArr = this.deptId == 1 ? [] : (this.deptId ? [this.deptId] : []);
        let obj = {
          deptIds: [...this.deptIds, ...deptIdArr] || [],
          id: nodeData.layerMenuSubId,
          menuType: this.menuShowType,
          paramsDTO: {
            precisionParams: nodeData.columnName
              ? { [nodeData.columnName]: nodeData.value }
              : {},
          },
          maintenanceSectionIds: this.mIds,
          menuSubClassifyId: nodeData.subClassifyId || null,
        };
        if (node.isLeaf && nodeData.baseLayer?.layerUrl) {
          let layer = new TileLayer({
            name: nodeData.baseLayer.layerName,
            zIndex: nodeData.baseLayer.showIndex,
            source: new XYZ({
              url: nodeData.baseLayer.layerUrl,
              attributions: nodeData.baseLayer.layerName,
              wrapX: false, // 是否水平包裹
              crossOrigin: "anonymous", // 常见的设置为 'anonymous' 或 'use-credentials'
            }),
            preload: Infinity,
            visible: true,
          });
          layer.set("treeLayerName", nodeData.id);
          layer.set("name", nodeData.baseLayer.layerName);
          window.mapLayer.addLayer(layer);
          return;
        }
        if (node.isLeaf) {
          this.getShowLevel(nodeData.showLevel);
          addVectorTile(
            window.mapLayer,
            "treeLayerName",
            value,
            JSON.stringify(obj),
            nodeData
          );
        }
        // 2025-3-30 ↓
        let treeDataArr = [];
        this.treeData[1].child.forEach(i => {
          treeDataArr = [...treeDataArr, ...i.child];
        });
        let otherData = [];
        treeDataArr = treeDataArr.filter(i => i.subClassify && i.subClassify.otherConfig);
        otherData = treeDataArr.map(v => v.subClassify);
        let strArr = ['未排查', '影响道路安全', '不影响道路安全', '排查状态'];
        if (strArr.includes(nodeData.name)) {
          cache.session.setJSON('otherData', otherData);
        } else {
          cache.session.remove('otherData');
        }
        // 2025-3-30 ↑
        if (index === 2) {
          // let allNodes = this.$refs["treeRef" + index][0].store._getAllNodes();
          // let arr = allNodes.filter((i) => i.checked === false);
          let a = ['1821009355233918977', '1821003006425006082', '1861320360096460802', '1821014785246904322', '1821011983095398401']
          let allNodes = []
          a.map(el => {
            allNodes.push(this.$refs["treeRef" + index][0].getNode(el))
          })
          let arr = allNodes.filter((i) => i.checked === false);
          if (!arr.length) {
            this.treeData[index].btnActive = true;
          } else {
            this.treeData[index].btnActive = false;
          }
          this.$forceUpdate();
        }
      } else {
        const list = window.mapLayer.getLayers().getArray();
        const arr = [];
        list.map((layer) => {
          const val = layer.get("treeLayerName");
          if (val && val === value) arr.push(layer);
        });
        arr.map((l) => {
          window.mapLayer.removeLayer(l);
        });
        if (index === 2) {
          let a = ['1821009355233918977', '1821003006425006082', '1861320360096460802', '1821014785246904322', '1821011983095398401']
          let allNodes = []
          a.map(el => {
            allNodes.push(this.$refs["treeRef" + index][0].getNode(el))
          })
          allNodes.map((i) => {
            if (i.data.id === nodeData.id) {
              this.treeData[index].btnActive = false;
            }
          });
          this.$forceUpdate();
        }
      }
    },
    // 获取 树 选中的数据
    getTreeCheckedData() {
      let legendArr = [];
      this.treeData.forEach((item, index1) => {
        let checkArr = this.$refs["treeRef" + index1][0].getCheckedNodes(
          false,
          true
        );
        if (checkArr) {
          legendArr = legendArr.concat(checkArr);
        }
      });
      return legendArr;
    },
    async handleDocument(row) {
      if (this.documentActive !== row.id) {
        this.documentActive = row.id;
        this.listQueryConfig = row.listQueryConfig || {};
        let config = null;
        if (typeof row.listQueryConfig == "string") {
          config = JSON.parse(row.listQueryConfig);
        } else {
          config = row.listQueryConfig;
        }
        if (config && config.detlCondf) {
          // 将表头信息保存到状态管理中
          this.setTableHeader({ header: config.tableHeader });
          // 设置详情配置
          this.getDetlCondf(config.detlCondf);
        }
        let iconImg = [];
        if (row.figureId) {
          iconImg = await getImageById(row.figureId);
        }
        row.icon = iconImg[0];
        this.getAllTable(row, config.listQueryParams);
        this.getLegend([row])
      } else {
        this.documentActive = "";
        const list = window.mapLayer.getLayers().getArray();
        const arr = [];
        list.map((layer) => {
          if (layer.get("name") === "clickLayer") {
            arr.push(layer);
          }
        });
        arr.map((l) => {
          window.mapLayer.removeLayer(l);
        });
        this.getTableShow(false);
      }
    },
    // 列表数据展示
    async handleShowTable(row, isHighway = false) {
      const list = window.mapLayer.getLayers().getArray();
      const arr = [];
      // 根据是否为高速路设置要移除的图层数组
      let removeLayers = isHighway ? ['pbfLayer', 'clickLayer'] : ['dataLayer', 'pbfLayer', 'clickLayer']
      list.map((layer) => {
        // 动态判断当前图层名称是否在要移除的图层数组中
        if (removeLayers.includes(layer.get("name"))) {
          arr.push(layer);
        }
        // if (
        //   layer.get("name") === "dataLayer" ||
        //   layer.get("name") === "pbfLayer" ||
        //   layer.get("name") === "clickLayer"
        // ) {
        //   arr.push(layer);
        // }
      });
      arr.map((l) => {
        window.mapLayer.removeLayer(l);
      });
      this.getLegend([]);
      if (this.documentActive !== row.id) {
        this.documentActive = row.id;
      } else {
        this.documentActive = "";
        let data = cache.session.getJSON("rangeData");
        if (this.deptIds && this.deptIds.length) {
          // 过滤出符合条件的路段
          data = data.filter(item => {
            return this.deptIds.includes(item.sys_dept_id || item.id)
          })
        }
        // 先移除
        removeLayer(window.mapLayer, "dataLayer");
        removeLayer(window.mapLayer, "dataLayer", 'name');
        addWidthFeature(window.mapLayer, data, "dataLayer", null, true, this.$store.state.map.deptMinZoom);
        this.getTableShow(false);
        return;
      }
      //1、 加载图标
      let iconImg = [];
      if (row.figureId) {
        iconImg = await getImageById(row.figureId);
        row.icon = iconImg[0];
      } else {
        row.icon = "";
      }
      //2、 设置图列信息
      this.getLegend([row]);
      // 3、获取当前 地图显示层级
      this.getShowLevel(row.showLevel);
      // 4、设置列表参数
      this.listQueryConfig = row.listQueryConfig;
      // 10、设置详情配置
      let config = null;
      if (typeof row.listQueryConfig == "string") {
        config = JSON.parse(row.listQueryConfig);
      } else {
        config = row.listQueryConfig;
      }
      // 5、触发表格显示
      if (isHighway) {
        this.getAllTable(row, config.listQueryParams, false, isHighway);
      } else {
        this.getAllTable(row, config.listQueryParams);
      }
      // 如果点击的是高速路，也就是dpTree点击的是节点层级为2的情况
      if (isHighway) {
        this.documentActive = "";
        this.getLegend([]);
        return;
      }
      // 6、加载矢量切片

      let d = await this.getShape(row);
      // 7、有数据再移除图层数据
      if (d && (Object.prototype.toString.call(d) == "[object Array]" && d.length)) {
        // 点击树节点移除地图数据
        removeLayer(window.mapLayer, "dataLayer");
        removeLayer(window.mapLayer, "dataLayer", 'name');
        // 8、将数据加载到地图
        addWidthFeature(window.mapLayer, d, "dataLayer", null, true, this.$store.state.map.deptMinZoom);
      } else {
        let data = cache.session.getJSON("rangeData");
        if (this.deptIds && this.deptIds.length) {
          // 过滤出符合条件的路段
          data = data.filter(item => {
            return this.deptIds.includes(item.sys_dept_id || item.id)
          })
        }
        removeLayer(window.mapLayer, "dataLayer");
        removeLayer(window.mapLayer, "dataLayer", "name");
        addWidthFeature(window.mapLayer, data, "dataLayer", null, true, this.$store.state.map.deptMinZoom);
      }

      // 9、加载矢量数据-先移除切片图层数据
      removeLayer(window.mapLayer, "pbfLayer");
      let obj = {
        managementMaintenanceIds: [...this.deptIds] || [],
        id: row.layerMenuSubId || row.id,
        menuType: row.menuType,
        maintenanceSectionIds: this.mIds,
        menuSubClassifyId: row.subClassifyId || '',
      };
      // 加载热力图
      if (config.ifGeojson) {
        removeLayer(window.mapLayer, 'heatLayer', 'name');
        removeHeatLayer();
        let jsonData = await this.getGeoJson(obj);
        if (!jsonData) return;
        addHeatmap(jsonData, 'name', 'heatLayer');
      } else {
        addVectorTile(
          window.mapLayer,
          "name",
          "pbfLayer",
          JSON.stringify(obj),
          row
        );
      }

      // 11、将表头信息保存到状态管理中
      this.setTableHeader({ header: config.tableHeader });
      if (config && config.detlCondf) {
        // 设置详情配置
        this.getDetlCondf(config.detlCondf);
      }
    },
    // 点击加载所有数据
    async handleClickAll(row) {
      this.documentActive = "";
      this.multiChoice = false;
      this.activeIds = [];
      this.activeList = [];
      this.rowAll = row;
      this.rowAll.icon = row.icon;

      this.isClick = !this.isClick;

      let config = null;
      if (typeof row.listQueryConfig == "string") {
        config = JSON.parse(row.listQueryConfig);
      } else {
        config = row.listQueryConfig;
      }
      // 1、重新获取表格数据
      this.getAllTable(row, config.listQueryParams, this.isClick);
      // 2、移除图层数据
      removeLayer(window.mapLayer, "pbfLayer");
      let deptIdArr = this.deptId == 1 ? [] : (this.deptId ? [this.deptId] : []);
      // 2025-3-9 ↓
      row.icon = await getImageById(row.figureId);
      // 2025-3-9 ↑
      let obj = {
        maintenanceSectionIds: this.mIds,
        managementMaintenanceIds: [...this.deptIds, ...deptIdArr] || [],
        id: row.layerMenuSubId || row.id,
        menuType: "",
        paramsDTO: {
          precisionParams: {
            ...row.precisionParams,
          },
          ks: "",
        },
        menuSubClassifyId: row.subClassifyId || '',
      };
      // 加载热力图
      if (config.ifGeojson) {
        removeLayer(window.mapLayer, 'heatLayer', 'name');
        removeHeatLayer();
        let jsonData = await this.getGeoJson(obj);
        if (!jsonData) return;
        addHeatmap(jsonData, 'name', 'heatLayer');
      } else {
        // 3、重新加载矢量切片数据
        addVectorTile(
          window.mapLayer,
          "name",
          "pbfLayer",
          JSON.stringify(obj),
          row
        );
      }
      let arr = await this.getShape(obj);
      if (arr && arr.length == 1) {
        this.currentData.total = arr[0].total || 0;
      }
      if (arr && arr.length) {
        // 4、 重新加载部门数据
        removeLayer(window.mapLayer, "dataLayer");
        removeLayer(window.mapLayer, "dataLayer", 'name');
        // 将数据加载到地图
        addWidthFeature(window.mapLayer, arr, "dataLayer", null, true, this.$store.state.map.deptMinZoom);
      }
      // 移除缓存数据
      cache.session.remove('otherData');
    },
    // 取消选中按钮
    handleCancelTree() {
      for (let index = 0; index < this.treeData.length; index++) {
        this.$refs["treeRef" + index][0].setCheckedKeys([]);
        this.treeData[index].btnActive = false;
      }
      const list = window.mapLayer.getLayers().getArray();
      const arr = [];
      list.map((layer) => {
        // 移除勾选的图层
        const val = layer.get("treeLayerName");
        if (val || layer.get('name').includes('heatLayer')) arr.push(layer);
        // 移除表格点击加载的闪烁高亮图层
        // if (layer.get('name') === 'clickLayer') {
        //   arr.push(layer);
        // }
      });
      arr.map((l) => {
        window.mapLayer.removeLayer(l);
      });
    },
    // 成果展示按钮
    handleResult(item, index) {
      let tree = this.$refs["treeRef" + index][0];
      if (item.btnActive) {
        item.btnActive = false;
        tree.setCheckedKeys([]);
      } else {
        item.btnActive = true;
        // let arr = item.child.map((i) => i.id);
        let arr = ['1821009355233918977', '1821003006425006082', '1861320360096460802', '1821014785246904322', '1821011983095398401']
        tree.setCheckedKeys(arr);
      }
      this.$forceUpdate();
    },
    // 获取geoJson数据
    async getGeoJson(params) {
      return new Promise((resolve, reject) => {
        this.$modal.loading();
        getVectorTile(params).then(res => {
          resolve(res);
        }).catch(err => {
          reject(err);
        }).finally(() => {
          this.$modal.closeLoading();
        });
      })
    },
  },
};
</script>

<style lang="scss" scoped>
@import "@/assets/styles/utils.scss";

@font-face {
  font-family: "YouSheBiaoTiHei";
  /* 自定义的字体名称 */
  src: url("~@/assets/home/<USER>") format("truetype");
  /* 字体文件路径和格式 */
  /* 可选属性，根据需要设置 */
  font-weight: normal;
  font-style: normal;
}

.healthMonitoring-title {
  font-weight: 700;
  font-size: 1.7vh;
  color: #ffffff;
  letter-spacing: vwpx(2px);
  height: vwpx(64px);
  line-height: vwpx(40px);
}

.item-content-list-active {
  box-shadow: inset 0px 0px 5px 0px #fdaf34 !important;
  border: 1px solid #fdaf34 !important;
}

.item-content-list-unactive {
  background: rgba(0, 0, 0, 0.1);
  box-shadow: inset 0px 0px vwpx(20px) 0px #0065FF;
  border-radius: vwpx(12px);
  border: vwpx(2px) solid #20A9FF;
}

.tree-1 {
  left: vwpx(670px);
}

.tree-2 {
  left: vwpx(10px);
}

.tree {
  display: flex;
  align-items: center;
  z-index: 8;
  position: absolute;
  // left: 5px;
  // left: vwpx(670px);
  top: vwpx(10px);

  .tree-operate {
    min-height: vwpx(376px);
    overflow-y: auto;
    background: linear-gradient(90deg,
        rgba(2, 10, 30, 0.8) 0%,
        rgba(12, 42, 86, 0.2) 100%);
    box-shadow: inset 0px 0px 10px 0px rgba(35, 134, 255, 0.8);
    border-radius: vwpx(20px);

    .tree-body {
      overflow-y: auto;
    }

    .input-search-btn {
      font-size: vwpx(32px);
      width: vwpx(60px);
      height: vwpx(52px);
      line-height: vwpx(52px);
      cursor: pointer;
    }

    .data-list {
      .list-item {
        .item-title {
          width: 100%;
          height: vwpx(64px);
          background-repeat: no-repeat;
          background-size: 100% 100%;

          display: flex;
          justify-content: space-between;
          align-items: center;
          padding-left: vwpx(20px);

          font-family: YouSheBiaoTiHei, YouSheBiaoTiHei;
          font-weight: 400;
          // font-size: vwpx(32px);
          font-size: 1.6vh;
          color: #ffffff;
          letter-spacing: vwpx(2px);
          line-height: vwpx(64px);
          // text-shadow: 0px 0px 6px #273dff;
        }
      }

      .item-content {
        display: flex;
        align-items: center;
        flex-wrap: wrap;
        max-height: calc(100vh - 480px);
        overflow-y: auto;
        cursor: pointer;

        .base-content {
          flex: 1;
          display: flex;
          margin: 0 vwpx(40px);
          flex-direction: column;

          span {
            color: #ffffff;
          }

          .base-content-divider {
            width: 100%;
            border: vwpx(3px) solid rgba(156, 189, 255, 0.5);
            margin: vwpx(10px) 0;
          }

          .base-content-data {
            span:first-child {
              font-family: Microsoft YaHei, Microsoft YaHei;
              font-weight: 700;
              font-size: vwpx(48px);
              text-align: left;
              font-style: normal;
              text-transform: none;
              background: linear-gradient(360deg,
                  #ffb200 0%,
                  #ffffff 74%,
                  #ffffff 100%);
              background-clip: text;
              -webkit-background-clip: text;
              color: transparent;
            }

            span:last-child {
              font-weight: 400;
              font-size: vwpx(26px);
              color: rgba(255, 255, 255, 0.7);
              text-align: left;
              font-style: normal;
              text-transform: none;
              margin-left: vwpx(6px);
            }
          }
        }

        .item-content-list {
          display: flex;
          align-items: center;
          justify-content: center;
          flex-direction: column;
          cursor: pointer;
          margin: 0 1.6% 8px 1.6%;
          max-height: vwpx(120px);
          background: rgba(0, 19, 71, 0.1);
          box-shadow: inset 0px 0px 5px 0px #0687ff;
          border-radius: vwpx(12px);
          border: 1px solid #0687ff;

          .list-name {
            font-family: YouSheBiaoTiHei, YouSheBiaoTiHei;
            min-width: vwpx(140px);
            max-width: vwpx(172px);
            font-size: vwpx(32px);
            flex-shrink: 0;
          }

          .divider {
            width: 80%;
            margin: vwpx(6px) 10%;
            border-top: 1px dashed rgba(6, 135, 255, 0.3);
          }

          .list-number-unit {
            width: 100%;
            display: flex;
            justify-content: center;

            .list-number {
              font-family: Microsoft YaHei, Microsoft YaHei;
              margin-right: 2px;
              font-size: vwpx(32px);
            }
          }

          span {
            font-weight: 500;
            font-size: vwpx(32px);
            text-shadow: 0px 0px 20px #105ed9;
            text-align: center;
            font-style: normal;
            text-transform: none;
            background: linear-gradient(180deg,
                #ffffff 0%,
                #ffffff 60%,
                #20a9ff 100%);
            background-clip: text;
            -webkit-background-clip: text;
            color: transparent;

            display: block;

            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }
        }

        .line-list {
          width: 100%;
          min-height: vwpx(64px);
          display: flex;
          align-items: center;
          justify-content: space-between;
          cursor: pointer;

          background: rgba(0, 19, 71, 0.1);
          box-shadow: inset 0px 0px 5px 0px #0687ff;
          border-radius: vwpx(12px);
          border: 1px solid #0687ff;
          margin-bottom: vwpx(10px);
          padding: vwpx(30px) vwpx(20px);

          .line-name {
            font-family: YouSheBiaoTiHei, YouSheBiaoTiHei;
            font-weight: 400;
            font-size: vwpx(36px);
            color: #ffffff;
            text-shadow: 0px 0px 6px #273dff;
          }

          .line-total {
            font-family: Microsoft YaHei, Microsoft YaHei;
            font-size: vwpx(32px);
            color: #ffffff;
          }
        }
      }
    }

    .tree-content {
      color: #ffffff;
      width: 100%;
      height: auto;
      margin-bottom: 5px;

      .tree-content-title {
        position: relative;
        width: 100%;
        height: 32px;
        background-image: url("~@/assets/map/title-bg.png");
        background-repeat: no-repeat;
        background-size: 100% 100%;
        margin-bottom: 5px;
        cursor: pointer;
        display: flex;
        align-items: center;
        padding-left: vwpx(30px);

        font-family: YouSheBiaoTiHei, YouSheBiaoTiHei;
        font-weight: 400;
        font-size: vwpx(36px);
        color: #ffffff;
        text-shadow: 0px 0px 6px #273dff;
        font-style: normal;

        .down {
          width: 15px;
          height: 15px;
          background-image: url("~@/assets/map/down.png");
          background-repeat: no-repeat;
          background-size: 100% 100%;
          margin-left: auto;
        }
      }

      .btn {
        background-color: #1890ff;
        border-color: #1890ff;
        margin-left: vwpx(20px);
        padding: vwpx(10px) vwpx(15px);
      }

      .btn-active {
        background: #ffbe27;
        border-color: #ffbe27;
        margin-left: vwpx(20px);
        padding: vwpx(10px) vwpx(15px);
      }
    }

    .tree-layer {
      display: flex;
      align-items: center;
      width: 100%;
      background: transparent;
      height: vwpx(76px);

      .name {
        font-family: Microsoft YaHei, Microsoft YaHei;
        font-weight: 400;
        font-size: vwpx(30px);
        color: #ffffff;
        text-align: left;
        font-style: normal;
        text-transform: none;
      }

      .img {
        width: vwpx(64px);
        height: vwpx(64px);
        margin-right: vwpx(12px);
      }

      .img-document {
        width: vwpx(32px);
        height: vwpx(32px);
      }

      .total {
        margin-left: auto;
        color: #ffbe27;
        font-size: vwpx(28px);
      }
    }

    ::v-deep .el-tree {
      background: transparent;
      color: #ffffff;

      .el-checkbox__input.is-disabled {
        display: none;
      }

      .el-tree-node {
        // background: linear-gradient(90deg, rgba(0, 94, 255, 0.1) 0%, rgba(1, 102, 254, 0.01) 100%);
        margin-bottom: vwpx(6px);

        .el-tree-node__children {
          // .el-tree-node__content {
          //   padding-left: vwpx(30px) !important;
          // }

          .el-tree-node__expand-icon {
            font-size: vwpx(35px);
          }
        }
      }

      .el-tree-node__content {
        height: vwpx(76px);

        .el-tree-node__expand-icon {
          font-size: vwpx(35px);
        }

        &:hover {
          background: linear-gradient(90deg,
              rgba(0, 94, 255, 0.5) 0%,
              rgba(1, 102, 254, 0.05) 100%);
        }

        .el-tree-node__expand-icon {
          padding: 2px;
        }
      }

      // 鼠标点击时节点的背景颜色
      .el-tree-node:focus>.el-tree-node__content {
        background: linear-gradient(90deg,
            rgba(0, 94, 255, 0.5) 0%,
            rgba(1, 102, 254, 0.05) 100%);
      }

      // 鼠标失去焦点时节点背景的颜色
      .is-current>.el-tree-node__content:first-child {
        background: linear-gradient(90deg,
            rgba(0, 94, 255, 0.5) 0%,
            rgba(1, 102, 254, 0.05) 100%);
      }

      /* 修改边框颜色 */
      .el-checkbox .el-checkbox__inner {
        border-color: #409eff;
        background-color: unset;
        width: vwpx(30px);
        height: vwpx(30px);

      }

      /* 修改边框颜色 */
      .el-checkbox .el-checkbox__inner:hover {
        border-color: #409eff;
      }
    }

    ::v-deep .el-input {
      .el-input__inner {
        background-color: rgba(1, 102, 254, 0.2);
        border: 1px solid #0166fe;
        color: #ffffff;
        // border-right: none;
      }

      .el-input__inner::placeholder {
        color: #bbbbbb;
      }

      .el-input-group__append {
        background-color: rgba(1, 102, 254, 0.2);
        border: 1px solid #0166fe;
        color: #ffffff;
        border-left: none;
        padding: 0 10px;
        cursor: pointer;
      }
    }
  }

  .tree-operate-big {
    ::v-deep .el-tree {
      .el-checkbox .el-checkbox__inner {
        margin: 0 vwpx(10px);
      }
    }
  }

  .spread-packup {
    height: vwpx(330px);
    width: vwpx(36px);
    cursor: pointer;

    background-image: url("~@/assets/map/left-close.png");
    background-repeat: no-repeat;
    background-size: 100% 100%;

    // background: linear-gradient(90deg, rgba(35, 134, 255, 0.5) 0%, rgba(35, 134, 255, 0.3) 90%, rgba(255, 255, 255, 0.8) 100%);
    // box-shadow: inset 0px 6px 6px 0px rgba(0, 85, 255, 0.3);
    // clip-path: polygon(0 0, 100% 15%, 100% 85%, 0 100%);

    display: flex;
    align-items: center;

    .img {
      transform: rotate(90deg);
      width: 100%;
    }

    .spread-img {
      transform: rotate(270deg);
      width: 100%;
    }
  }

  .closeMTop {
    margin-top: vwpx(380px);
  }

  ::v-deep .el-select__tags {
    flex-wrap: nowrap;
    overflow: auto;
  }
}

::-webkit-scrollbar {
  width: 5px;
  height: 5px;
}

/* 轨道颜色 */
::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.2);
}

/* 滑块颜色 */
::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.6);
}

/* 滑块悬停或活动时的颜色 */
::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.6);
}

.analysis-button {
  width: 95px;
  height: vwpx(56px);
  background: linear-gradient(180deg,
      rgba(0, 120, 255, 0.3) 0%,
      rgba(0, 0, 0, 0.5) 52%,
      rgba(0, 120, 255, 0.3) 100%);
  box-shadow: inset 0px 0px 10px 0px #0078ff;
  border-radius: 4px;
  border: 1px solid #0078ff;
  // border-image: linear-gradient(270deg, rgba(0, 120.00000044703484, 255, 1), rgba(192.95849472284317, 222.14450061321259, 255, 0.9589097499847412), rgba(94.86358925700188, 168.97495687007904, 252.23031342029572, 0.8679342865943909), rgba(193.00000369548798, 222.00000196695328, 255, 0.9599999785423279), rgba(0, 120.00000044703484, 255, 1)) 1 1;
  font-weight: 400;
  font-size: 12px;
  color: #ffffff;
  line-height: 16px;
  text-align: left;
  font-style: normal;
  text-transform: none;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
}

.my-1 {
  margin: vwpx(10px) 0;
}

.ml-1 {
  margin-left: vwpx(10px);
}

.ml-2 {
  margin-left: vwpx(20px);
}

.my-2 {
  margin: vwpx(20px) 0;
}

.py-1 {
  padding: vwpx(10px) 0;
}
</style>
