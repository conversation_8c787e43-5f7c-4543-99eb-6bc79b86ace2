<template>
    <el-row :gutter="12">
        <el-col :span="8">
            <el-form-item :prop="provincialProp">
                <span slot="label">
                    <el-tooltip content="省级区划代码" placement="top">
                        <i class="el-icon-question"></i>
                    </el-tooltip>
                    省级区划
                </span>
                <el-select v-model="provincialValue" disabled style="width: 100%">
                    <el-option
                        v-for="item in provincialOptions"
                        :label="`${item.label}（${item.id}）`"
                        :value="item.id"
                        :key="item.id"
                    ></el-option>
                </el-select>
            </el-form-item>
        </el-col>
        <el-col :span="8">
            <el-form-item :prop="municipalProp">
                <span slot="label">
                    <el-tooltip content="市级区划代码" placement="top">
                        <i class="el-icon-question"></i>
                    </el-tooltip>
                    市级区划
                </span>
                <el-select v-model="municipalModel" clearable style="width: 100%" @change="handleMunicipalChange">
                    <el-option
                        v-for="item in municipalOptions"
                        :label="`${item.label}（${item.id}）`"
                        :value="item.id"
                        :key="item.id"
                    ></el-option>
                </el-select>
            </el-form-item>
        </el-col>
        <el-col :span="8">
            <el-form-item :prop="countyProp">
                <span slot="label">
                    <el-tooltip content="县级区划代码，用于生成灾害编码" placement="top">
                        <i class="el-icon-question"></i>
                    </el-tooltip>
                    县级区划
                </span>
                <el-select v-model="countyModel" clearable style="width: 100%">
                    <el-option
                        v-for="item in countyOptions"
                        :label="`${item.label}（${item.id}）`"
                        :value="item.id"
                        :key="item.id"
                    ></el-option>
                </el-select>
            </el-form-item>
        </el-col>
    </el-row>
</template>

<script>
    export default {
        name: 'RegionSelector',
        props: {
            // 省级区划值
            provincialValue: {
                type: String,
                default: '53',
            },
            // 省级区划选项
            provincialOptions: {
                type: Array,
                default: () => [
                    {
                        disabled: true,
                        id: '53',
                        label: '云南省',
                        level: 1,
                    },
                ],
            },
            // 市级区划值
            municipalValue: {
                type: String,
                default: '',
            },
            // 市级区划选项
            municipalOptions: {
                type: Array,
                default: () => [],
            },
            // 县级区划值
            countyValue: {
                type: String,
                default: '',
            },
            // 县级区划选项
            countyOptions: {
                type: Array,
                default: () => [],
            },
            // 表单属性名
            provincialProp: {
                type: String,
                default: '',
            },
            municipalProp: {
                type: String,
                default: '',
            },
            countyProp: {
                type: String,
                default: 'areaCode',
            },
        },
        computed: {
            municipalModel: {
                get() {
                    return this.municipalValue;
                },
                set(val) {
                    this.$emit('update:municipalValue', val);
                },
            },
            countyModel: {
                get() {
                    return this.countyValue;
                },
                set(val) {
                    this.$emit('update:countyValue', val);
                },
            },
        },
        methods: {
            // 市级区划改变时
            handleMunicipalChange(val) {
                this.$emit('update:countyValue', '');
                this.$emit('municipalChange', val);
            },
        },
    };
</script>
