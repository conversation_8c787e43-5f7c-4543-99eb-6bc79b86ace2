<template>
  <PageContainer>
    <template slot="search">
      <div style="display: flex; align-items: center; margin: 0">
        <CascadeSelection style="min-width: 192px" :form-data="queryParams" v-model="queryParams" types="201"
          multiple />
        <!-- <el-select
          style="margin: 0 20px"
          v-model="queryParams.operationState"
          placeholder="运营状态"
          clearable
        >
          <el-option
            v-for="dict in dict.type.sys_operation_state"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select> -->
        <el-date-picker v-model="queryParams.checkYear" type="year" value-format="yyyy" placeholder="年份"
          style="margin: 0 20px" />
        <el-button v-hasPermi="['baseData:tunnelStatistics:getStatistics']" type="primary" icon="el-icon-search"
          @click="handleQuery">
          搜索
        </el-button>
        <el-button icon="el-icon-refresh" @click="onReset">重置</el-button>
      </div>
    </template>
    <template slot="header">
      <el-button v-hasPermi="['baseData:tunnelStatistics:export']" style="margin-bottom: 10px" type="primary"
        @click="exportList">
        导出报表
      </el-button>
    </template>
    <template slot="body">
      <!-- <div class="body">
      </div> -->
      <el-table v-adjust-table ref="table" v-loading="loading" height="100%" border :span-method="objectSpanMethod" :data="tableData"
        :header-cell-style="{ height: '36px' }" :row-class-name="tableRowClassName">
        <el-table-column fixed label="序号" type="index" width="50" align="center" />

        <el-table-column fixed="left" label="管理处名称" align="center" prop="managementMaintenanceName" min-width="120"
          show-overflow-tooltip />
        <el-table-column fixed="left" label="路段名称" align="center" prop="maintenanceSectionName" min-width="120"
          show-overflow-tooltip />
        <el-table-column label="跨径分类" align="center">
          <el-table-column label="特长隧道" align="center" prop="superMajorTunnel" min-width="120">
            <template slot-scope="{ row }">
              <el-link type="primary" :underline="false" @click="
                handleConditionLevel({ ...row, clickName: '特长隧道' }, '1', row.superMajorTunnel)
                ">
                {{ row.superMajorTunnel }}
              </el-link>
            </template>
          </el-table-column>

          <el-table-column label="长隧道" align="center" prop="greatTunnel" min-width="120">
            <template slot-scope="{ row }">
              <el-link type="primary" :underline="false"
                @click="handleConditionLevel({ ...row, clickName: '长隧道' }, '2', row.greatTunnel)">
                {{ row.greatTunnel }}
              </el-link>
            </template>
          </el-table-column>

          <el-table-column label="中隧道" align="center" prop="mediumTunnel" min-width="120">
            <template slot-scope="{ row }">
              <el-link type="primary" :underline="false" @click="
                handleConditionLevel({ ...row, clickName: '中隧道' }, '3', row.mediumTunnel)
                ">
                {{ row.mediumTunnel }}
              </el-link>
            </template>
          </el-table-column>

          <el-table-column label="短隧道" align="center" prop="smallTunnel" min-width="120">
            <template slot-scope="{ row }">
              <el-link type="primary" :underline="false"
                @click="handleConditionLevel({ ...row, clickName: '短隧道' }, '4', row.smallTunnel)">
                {{ row.smallTunnel }}
              </el-link>
            </template>
          </el-table-column>
          <el-table-column label="总体" align="center" prop="totalCount" min-width="120">
            <template slot-scope="{ row }">
              <el-link type="primary" :underline="false"
                @click="handleSpanClassify({ ...row, clickName: '总体' }, [], row.totalCount)">
                {{ row.totalCount }}
              </el-link>
            </template>
          </el-table-column>
        </el-table-column>
        <el-table-column label="一类" align="center">
          <el-table-column label="土建" align="center" prop="oneTypeCivil" min-width="120">
            <template slot-scope="{ row }">
              <el-link type="primary" :underline="false" @click="
                handleSpanClassify(
                  { ...row, clickName: '一类土建' },
                  row.oneTypeCivilIds,
                  row.oneTypeCivil
                )
                ">
                {{ row.oneTypeCivil }}
              </el-link>
            </template>
          </el-table-column>
          <el-table-column label="机电" align="center" prop="oneTypeElect" min-width="120">
            <template slot-scope="{ row }">
              <el-link type="primary" :underline="false" @click="
                handleSpanClassify(
                  { ...row, clickName: '一类机电' },
                  row.oneTypeElectIds,
                  row.oneTypeElect
                )
                ">
                {{ row.oneTypeElect }}
              </el-link>
            </template>
          </el-table-column>
          <el-table-column label="其他" align="center" prop="oneTypeOther" min-width="120">
            <template slot-scope="{ row }">
              <el-link type="primary" :underline="false" @click="
                handleSpanClassify(
                  { ...row, clickName: '一类其他' },
                  row.oneTypeOtherIds,
                  row.oneTypeOther
                )
                ">
                {{ row.oneTypeOther }}
              </el-link>
            </template>
          </el-table-column>
          <el-table-column label="总体" align="center" prop="oneTypeTotal" min-width="120">
            <template slot-scope="{ row }">
              <el-link type="primary" :underline="false" @click="
                handleSpanClassify(
                  { ...row, clickName: '一类总体' },
                  row.oneTypeTotalIds,
                  row.oneTypeTotal
                )
                ">
                {{ row.oneTypeTotal }}
              </el-link>
            </template>
          </el-table-column>
        </el-table-column>
        <el-table-column label="二类" align="center">
          <el-table-column label="土建" align="center" prop="twoTypeCivil" min-width="120">
            <template slot-scope="{ row }">
              <el-link type="primary" :underline="false" @click="
                handleSpanClassify(
                  { ...row, clickName: '二类土建' },
                  row.twoTypeCivilIds,
                  row.twoTypeCivil
                )
                ">
                {{ row.twoTypeCivil }}
              </el-link>
            </template>
          </el-table-column>
          <el-table-column label="机电" align="center" prop="twoTypeElect" min-width="120">
            <template slot-scope="{ row }">
              <el-link type="primary" :underline="false" @click="
                handleSpanClassify(
                  { ...row, clickName: '二类机电' },
                  row.twoTypeElectIds,
                  row.twoTypeElect
                )
                ">
                {{ row.twoTypeElect }}
              </el-link>
            </template>
          </el-table-column>
          <el-table-column label="其他" align="center" prop="twoTypeOther" min-width="120">
            <template slot-scope="{ row }">
              <el-link type="primary" :underline="false" @click="
                handleSpanClassify(
                  { ...row, clickName: '二类其他' },
                  row.twoTypeOtherIds,
                  row.twoTypeOther
                )
                ">
                {{ row.twoTypeOther }}
              </el-link>
            </template>
          </el-table-column>
          <el-table-column label="总体" align="center" prop="twoTypeTotal" min-width="120">
            <template slot-scope="{ row }">
              <el-link type="primary" :underline="false" @click="
                handleSpanClassify(
                  { ...row, clickName: '二类总体' },
                  row.twoTypeTotalIds,
                  row.twoTypeTotal
                )
                ">
                {{ row.twoTypeTotal }}
              </el-link>
            </template>
          </el-table-column>
        </el-table-column>
        <el-table-column label="三类" align="center">
          <el-table-column label="土建" align="center" prop="threeTypeCivil" min-width="120">
            <template slot-scope="{ row }">
              <el-link type="primary" :underline="false" @click="
                handleSpanClassify(
                  { ...row, clickName: '三类土建' },
                  row.threeTypeCivilIds,
                  row.threeTypeCivil
                )
                ">
                {{ row.threeTypeCivil }}
              </el-link>
            </template>
          </el-table-column>
          <el-table-column label="机电" align="center" prop="threeTypeElect" min-width="120">
            <template slot-scope="{ row }">
              <el-link type="primary" :underline="false" @click="
                handleSpanClassify(
                  { ...row, clickName: '三类机电' },
                  row.threeTypeElectIds,
                  row.threeTypeElect
                )
                ">
                {{ row.threeTypeElect }}
              </el-link>
            </template>
          </el-table-column>
          <el-table-column label="其他" align="center" prop="threeTypeOther" min-width="120">
            <template slot-scope="{ row }">
              <el-link type="primary" :underline="false" @click="
                handleSpanClassify(
                  { ...row, clickName: '三类其他' },
                  row.threeTypeOtherIds,
                  row.threeTypeOther
                )
                ">
                {{ row.threeTypeOther }}
              </el-link>
            </template>
          </el-table-column>
          <el-table-column label="总体" align="center" prop="threeTypeTotal" min-width="120">
            <template slot-scope="{ row }">
              <el-link type="primary" :underline="false" @click="
                handleSpanClassify(
                  { ...row, clickName: '三类总体' },
                  row.threeTypeTotalIds,
                  row.threeTypeTotal
                )
                ">
                {{ row.threeTypeTotal }}
              </el-link>
            </template>
          </el-table-column>
        </el-table-column>
        <el-table-column label="四类" align="center">
          <el-table-column label="土建" align="center" prop="fourTypeCivil" min-width="120">
            <template slot-scope="{ row }">
              <el-link type="primary" :underline="false" @click="
                handleSpanClassify(
                  { ...row, clickName: '四类土建' },
                  row.fourTypeCivilIds,
                  row.fourTypeCivil
                )
                ">
                {{ row.fourTypeCivil }}
              </el-link>
            </template>
          </el-table-column>
          <el-table-column label="机电" align="center" prop="fourTypeElect" min-width="120">
            <template slot-scope="{ row }">
              <el-link type="primary" :underline="false" @click="
                handleSpanClassify(
                  { ...row, clickName: '四类机电' },
                  row.fourTypeElectIds,
                  row.fourTypeElect
                )
                ">
                {{ row.fourTypeElect }}
              </el-link>
            </template>
          </el-table-column>
          <el-table-column label="其他" align="center" prop="fourTypeOther" min-width="120">
            <template slot-scope="{ row }">
              <el-link type="primary" :underline="false" @click="
                handleSpanClassify(
                  { ...row, clickName: '四类其他' },
                  row.fourTypeOtherIds,
                  row.fourTypeOther
                )
                ">
                {{ row.fourTypeOther }}
              </el-link>
            </template>
          </el-table-column>
          <el-table-column label="总体" align="center" prop="fourTypeTotal" min-width="120">
            <template slot-scope="{ row }">
              <el-link type="primary" :underline="false" @click="
                handleSpanClassify(
                  { ...row, clickName: '四类总体' },
                  row.fourTypeTotalIds,
                  row.fourTypeTotal
                )
                ">
                {{ row.fourTypeTotal }}
              </el-link>
            </template>
          </el-table-column>
        </el-table-column>
        <el-table-column label="五类" align="center">
          <el-table-column label="土建" align="center" prop="fiveTypeCivil" min-width="120">
            <template slot-scope="{ row }">
              <el-link type="primary" :underline="false" @click="
                handleSpanClassify(
                  { ...row, clickName: '五类土建' },
                  row.fiveTypeCivilIds,
                  row.fiveTypeCivil
                )
                ">
                {{ row.fiveTypeCivil }}
              </el-link>
            </template>
          </el-table-column>
          <el-table-column label="机电" align="center" prop="fiveTypeElect" min-width="120">
            <template slot-scope="{ row }">
              <el-link type="primary" :underline="false" @click="
                handleSpanClassify(
                  { ...row, clickName: '五类机电' },
                  row.fiveTypeElectIds,
                  row.fiveTypeElect
                )
                ">
                {{ row.fiveTypeElect }}
              </el-link>
            </template>
          </el-table-column>
          <el-table-column label="其他" align="center" prop="fiveTypeOther" min-width="120">
            <template slot-scope="{ row }">
              <el-link type="primary" :underline="false" @click="
                handleSpanClassify(
                  { ...row, clickName: '五类其他' },
                  row.fiveTypeOtherIds,
                  row.fiveTypeOther
                )
                ">
                {{ row.fiveTypeOther }}
              </el-link>
            </template>
          </el-table-column>
          <el-table-column label="总体" align="center" prop="fiveTypeTotal" min-width="120">
            <template slot-scope="{ row }">
              <el-link type="primary" :underline="false" @click="
                handleSpanClassify(
                  { ...row, clickName: '五类总体' },
                  row.fiveTypeTotalIds,
                  row.fiveTypeTotal
                )
                ">
                {{ row.fiveTypeTotal }}
              </el-link>
            </template>
          </el-table-column>
        </el-table-column>
        <el-table-column label="未评定" align="center" prop="notTypeCount" min-width="120">
          <template slot-scope="{ row }">
            <el-link type="primary" :underline="false" @click="
              handleSpanClassify(
                { ...row, clickName: '未评定' },
                row.totalTypeCountIds,
                row.notTypeCount
              )
              ">
              {{ row.notTypeCount }}
            </el-link>
          </template>
        </el-table-column>
        <el-table-column label="评定总数" align="center" prop="totalTypeCount" min-width="120">
          <template slot-scope="{ row }">
            <el-link type="primary" :underline="false" @click="
              handleSpanClassify(
                { ...row, clickName: '评定总数' },
                row.totalTypeCountIds,
                row.totalTypeCount
              )
              ">
              {{ row.totalTypeCount }}
            </el-link>
          </template>
        </el-table-column>

        <el-table-column label="总长度(m)" align="center" prop="totalLength" min-width="120"></el-table-column>
        <el-table-column label="特长隧道长度(m)" align="center" prop="superMajorTunnelLength"
          min-width="120"></el-table-column>
        <el-table-column label="长隧道长度(m)" align="center" prop="greatTunnelLength" min-width="120"></el-table-column>
        <el-table-column label="中隧道长度(m)" align="center" prop="mediumTunnelLength" min-width="120"></el-table-column>
        <el-table-column label="短隧道长度(m)" align="center" prop="smallTunnelLength" min-width="120"></el-table-column>
      </el-table>
      <!-- <pagination
        :total="total"
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
        :pageSizes="[10, 20, 30, 50, 100, 1000]"
        @pagination="getList"
      /> -->
    </template>
    <DetailTable v-if="showTable" :showTable="showTable" :row="row" :params="params" @close="closeTable" />
  </PageContainer>
</template>

<script>
import CascadeSelection from '@/components/CascadeSelection/index.vue'
import { getStatistics } from '@/api/baseData/tunnel/statistics/index'
import DetailTable from './components/DetailTable.vue'

export default {
  name: 'Statistics',
  props: {},
  dicts: ['sys_operation_state'],
  components: { CascadeSelection, DetailTable },
  data() {
    return {
      queryParams: {
        //默认当前年份
        checkYear: new Date().getFullYear().toString(),
        operationState: "2"
      },
      tableData: [],
      loading: false,
      cellList: [],
      count: null,
      showTable: false,
      row: {},
      params: {},
    }
  },
  created() {
    this.getList()
  },
  methods: {
    handleQuery() {
      this.getList()
    },
    // 重置
    onReset() {
      this.queryParams = {
        //默认当前年份
        checkYear: new Date().getFullYear().toString(),
        operationState: '2',
      }
      this.handleQuery();
    },
    getList() {
      this.loading = true
      this.tableData = []
      this.cellList = []
      this.count = null
      getStatistics(this.queryParams)
        .then((res) => {
          if (res) {
            this.tableData = res
            this.computeCell(this.tableData)
          }
        })
        .finally(() => {
          this.loading = false
        })
    },
    computeCell(table) {
      for (let index = 0; index < table.length; index++) {
        if (index == 0) {
          this.cellList.push(1)
          this.count = 0
        } else {
          if (
            table[index].managementMaintenanceName == table[index - 1].managementMaintenanceName
          ) {
            this.cellList[this.count] += 1
            this.cellList.push(0)
          } else {
            this.cellList.push(1)
            this.count = index
          }
        }
      }
    },
    objectSpanMethod({ row, column, rowIndex, columnIndex }) {
      if (columnIndex === 1) {
        const rowCell = this.cellList[rowIndex]
        if (rowCell > 0) {
          const colCell = 1
          return {
            rowspan: rowCell,
            colspan: colCell,
          }
        } else {
          return {
            rowspan: 0,
            colspan: 0,
          }
        }
      }
    },
    exportList() {
      this.$modal
        .confirm('导出所有表格数据，是否继续？')
        .then(() => {
          this.download(
            '/baseData/tunnel/statistics/export',
            this.queryParams,
            `隧道统计_${new Date().getTime()}.xlsx`,
            {
              headers: { 'Content-Type': 'application/json;' },
              parameterType: 'body',
            }
          )
        })
        .catch(() => { })
    },
    handleSpanClassify(row, list, Num) {
      if (Num == 0) return
      this.showTable = true
      this.row = row
      this.params = { ids: list }
    },
    handleConditionLevel(row, list, Num) {
      if (Num == 0) return
      this.showTable = true
      this.row = row
      this.params = { lengthClassification: list }
    },
    closeTable() {
      this.showTable = false
      this.row = {}
    },
    tableRowClassName(row_params) {
      let { row } = row_params
      if (row.maintenanceSectionName == '总计') {
        return `tr-fixed fixed-row`
      } else {
        return ``
      }
    },
  },
  computed: {},
  watch: {},
}
</script>

<style lang="scss" scoped>
::v-deep .el-table .el-table__fixed-body-wrapper {
  top: 72px !important;
}

.body {
  height: calc(100vh - 230px);
  overflow-y: auto;
}

::v-deep .el-table {
  .tr-fixed {
    display: table-row;
    position: sticky;
    bottom: 0;
    width: 100%;

    td {
      border-top: 1px solid #f3f5fa;
      background: #fff;
    }
  }

  .fixed-row {
    bottom: 0;
  }
}
</style>
