<template>
  <!-- 授权用户 -->
  <el-dialog title="选择桥梁" :visible.sync="visible" width="800px" top="5vh" append-to-body>
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true">
      <el-form-item label="桥梁名称" prop="assetName">
        <el-input
          v-model="queryParams.assetName"
          placeholder="请输入桥梁名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>
    <el-row>
      <el-table @row-click="clickRow" @select="selectChange" ref="table" :data="bridgeList"
                @select-all="handleSelectionChange" height="260px">
        <el-table-column type="selection" width="55"></el-table-column>
        <el-table-column label="桥梁编码" align="center" prop="assetCode"/>
        <el-table-column label="桥梁名称" align="center" prop="assetName"/>
      </el-table>
      <pagination
        v-show="total>0"
        :total="total"
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
        @pagination="getList"
      />
    </el-row>
    <div slot="footer" class="dialog-footer">
      <el-button type="primary" @click="handleSelectUser">确 定</el-button>
      <el-button @click="visible = false">取 消</el-button>
    </div>
  </el-dialog>
</template>

<script>
import {listStatic} from "@/api/patrol/assetInfo";
import {getDefaultSelected, addDeptBridge} from "@/api/system/sysBridge";
export default {
  dicts: ['sys_normal_disable'],
  props: {
    deptData: {
      type: Object,
      require: true,
    },
  },
  data() {
    return {
      // 遮罩层
      visible: false,
      // 选中数组值
      bridgeIds: [],
      // 总条数
      total: 0,
      // 桥梁数据
      bridgeList: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        maintenanceSectionId: undefined,
        assetName: undefined,
      },
      //提交参数
      params: {}
    };
  },
  methods: {
    // 显示弹框
    show() {
      this.queryParams.deptId = this.deptData.deptId;
      this.queryParams.maintenanceSectionId = this.deptData.maintenanceSectionId;
      this.getDefault();
      this.getList();
      this.visible = true;
    },
    clickRow(row) {
      this.$refs.table.toggleRowSelection(row);
      this.setChecked(row);
    },
    selectChange(arr, row) {
      //退选和选中处理
      this.setChecked(row);
    },
    //退选和选中处理
    setChecked(row){
      if (this.bridgeIds.includes(row.assetId)) {
        this.bridgeIds = this.bridgeIds.filter(i => i !== row.assetId);
      } else {
        this.bridgeIds.push(row.assetId)
      }
    },

    // 多选框选中数据
    handleSelectionChange(selection) {
      if (selection.length > 0) {
        selection.forEach(item => {
          // this.setChecked(item);
          if (!this.bridgeIds || !this.bridgeIds.includes(item.assetId)) {
            this.bridgeIds.push(item.assetId)
          }
        })
      } else {
        this.bridgeList.forEach(item => {
          this.bridgeIds = this.bridgeIds.filter(i => i !== item.assetId);
        })
      }
    },
    // 查询默认选中数据
    getDefault() {
      getDefaultSelected(this.queryParams).then(data => {
        if (data) {
          this.bridgeIds = data;
        }
      });
    },
    // 查询表数据
    getList() {
      // this.queryParams.maintenanceSectionId = null;
      listStatic(this.queryParams).then(res => {
        this.bridgeList = res.rows;
        this.total = res.total;
        this.setSelected(this.bridgeList);
      });
    },
    //设置默认选中
    setSelected(rows) {
      this.$nextTick(()=> {//渲染后执行
        // const intersection = [];
        rows.forEach(row => {
          if (this.bridgeIds.includes(row.assetId)) {
            this.$refs.table.toggleRowSelection(row,true);
            // intersection.push(row.assetId);
          }
        });
        // this.bridgeIds = intersection;
      })
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    /** 提交选择桥梁操作 */
    handleSelectUser() {
      const bridgeIds = this.bridgeIds.join(",");
      // if (bridgeIds == "") {
      //   this.$modal.msgError("请选择要分配的桥梁");
      //   return;
      // }
      const deptId = this.deptData.deptId;
      this.params.deptId = deptId;
      this.params.bridgeIds = bridgeIds;
      this.params.maintenanceSectionId = this.deptData.maintenanceSectionId;
      addDeptBridge(this.params).then(response => {
        this.$modal.msgSuccess("操作成功");
        this.$parent.handleDeptAsset(deptId);
        this.getList();
        this.visible=false;
      });
    }
  }
};
</script>
