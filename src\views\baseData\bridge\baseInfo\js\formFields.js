// 行政识别数据
const administrative = [

  // {
  //   label: '养护单位',
  //   prop: 'managementMaintenanceId',
  //   placeholder: '请选择养护单位(管理处)',
  //   type: 'selectTree',
  //   deptType: 201,
  //   rules: [{ required: true, message: '请选择养护单位(管理处)', trigger: 'change' }]
  // },
  // {
  //   label: '管养分处',
  //   prop: 'managementMaintenanceBranchId',
  //   placeholder: '请选择管养分处',
  //   type: 'selectTree',
  //   deptType: 202,
  //   rules: [{ required: true, message: '请选择管养分处', trigger: 'change' }]
  // },
  {
    label: '养护路段',
    prop: 'maintenanceSectionId',
    placeholder: '请选择养护路段',
    type: 'select',
    rules: [{ required: true, message: '请选择养护路段', trigger: 'change' }],
    options: [],
    api: 'maintenanceSectionList',
    optionLabel: 'maintenanceSectionName',
    optionValue: 'maintenanceSectionId',
    disabledFieds: 'managementMaintenanceBranchId'
  },
  {
    label: '路线编码',
    prop: 'routeCode',
    placeholder: '请选择路线编码',
    type: 'select',
    rules: [{ required: true, message: '请选择路线编码', trigger: 'change' }],
    options: [],
    disabled: true,
    api: 'routeListAll',
    optionLabel: 'routeCode',
    optionValue: 'routeCode',
    disabledFieds: 'maintenanceSectionId'
  },
  {
    label: '路线名称',
    prop: 'routeName',
    placeholder: '请输入路线名称',
    type: 'select',
    api: 'routeListAll',
    optionLabel: 'routeName',
    optionValue: 'routeName',
    disabledFieds: '',
    rules: [{ required: true, message: '路线名称不能为空，选择路线编码自动带出。', trigger: 'change' }],
  },
  {
    label: '路线技术等级',
    prop: 'routeLevel',
    placeholder: '请选择路线技术等级',
    // type: 'dictSelect',
    multiple: true,
    dict: 'sys_route_grade',
    disabledFieds:'',
    type: 'multiDictSelect',
    rules: [{required: true, message: '路线技术等级不能为空，选择养护路段自动带出。', trigger: 'change'}],
  },
  {
    label: '路段类型',
    prop: 'sectionType',
    placeholder: '请选择路段类型',
    type: 'roadType',
    options: []
  },

  {
    label: '固定编码',
    prop: 'fixedCode',
    placeholder: '请输入固定编码',
    type: 'input',
    rules: [{ required: true, message: '请输入固定编码', trigger: 'blur' }]
  },

  {
    label: '所在行政区编码',
    prop: 'areaCode',
    placeholder: '请选择所在行政区编码',
    type: 'CascaderRegion',
    options: [],
    rules: [{ required: true, message: '请选择所在行政区编码', trigger: 'blur' }],
  },
  {
    label: '桥梁编码',
    prop: 'bridgeCode',
    placeholder: '请输入桥梁编码',
    type: 'input',
    rules: [{ required: true, message: '请输入桥梁编码', trigger: 'blur' }]
  },
  {
    label: '桥梁名称',
    prop: 'bridgeName',
    placeholder: '请输入桥梁名称',
    type: 'input',
    rules: [{ required: true, message: '请输入桥梁名称', trigger: 'blur' }]
  },
  {
    label: '桥梁竣工名称',
    prop: 'bridgeCompletedName',
    placeholder: '请输入桥梁竣工名称',
    type: 'input',
  },
  {
    label: '中心桩号',
    prop: 'centerStake',
    placeholder: '请输入中心桩号',
    type: 'pileInput',
    precision: 3,
    rules: [{ required: true, message: '请输入中心桩号', trigger: 'blur' }]
  },
  {
    label: '施工桩号',
    prop: 'constructionStake',
    placeholder: '请输入施工桩号',
    type: 'pileInput',
    precision: 3,
    // rules: [{ required: true, message: '请输入施工桩号', trigger: 'blur' }]
  },
  {
    label: '统一里程桩号',
    prop: 'unifiedMileageStake',
    placeholder: '请输入养护桩号',
    type: 'pileInput',
    precision: 3
  },
  {
    label: '国高网桩号',
    prop: 'nationalNetworkStake',
    placeholder: '请输入国高网桩号',
    type: 'pileInput',
    precision: 3
  },
  {
    label: '桥梁所在位置',
    prop: 'direction',
    placeholder: '请选择桥梁所在位置',
    type: 'dictSelect',
    dict: 'sys_route_direction',
    rules: [{ required: true, message: '请选择桥梁所在位置', trigger: 'change' }]
  },
  {
    label: '车道',
    prop: 'lane',
    placeholder: '请输入车道',
    type: 'dictSelect',
    dict: 'lane',
    rules: [{ required: true, message: '请输入车道', trigger: 'blur' }]
  },
  {
    label: '按跨径分类',
    prop: 'spanClassifyType',
    placeholder: '请选择按跨径分类',
    type: 'dictSelect',
    dict: 'bridge_span_classify',
    rules: [{ required: true, message: '请选择按跨径分类', trigger: 'change' }]
  },
  {
    label: '桥梁用途分类',
    prop: 'functionType',
    placeholder: '请选择桥梁用途分类',
    type: 'dictSelect',
    dict: 'bridge_function_type'
  },
  {
    label: '下穿通道名',
    prop: 'underpassChannelName',
    placeholder: '请输入下穿通道名',
    type: 'input'
  },
  {
    label: '下穿通道桩号',
    prop: 'underpassChannelunderStake',
    placeholder: '请输入下穿通道桩号',
    type: 'pileInput',
    precision: 3
  },
  {
    label: '设计荷载等级',
    prop: 'designLoadType',
    placeholder: '请选择设计荷载等级',
    type: 'dictSelect',
    dict: 'sys_design_load',
    // rules: [{ required: true, message: '请选择设计荷载等级', trigger: 'change' }]
  },
  {
    label: '通行载重',
    prop: 'trafficLoadType',
    placeholder: '请选择通行载重',
    type: 'dictSelect',
    dict: 'bridge_traffic_load'
  },
  {
    label: '桥面纵坡',
    prop: 'bridgeSlope',
    placeholder: '请输入桥面纵坡',
    type: 'inputNumber',
    precision: 4
  },
  {
    label: '桥梁平曲线半径',
    prop: 'planeCurveRadius',
    placeholder: '请输入桥梁平曲线半径',
    type: 'inputNumber',
    precision: 3
  },
  {
    label: '修建年度',
    prop: 'constructionYear',
    placeholder: '请选择修建年度',
    type: 'year',
    rules: [{ required: true, message: '请选择修建年度', trigger: 'change' }]
  },
  {
    label: '建设单位',
    prop: 'buildUnit',
    placeholder: '请输入建设单位',
    type: 'input',
    rules: [{ required: true, message: '请输入建设单位', trigger: 'blur' }]
  },
  {
    label: '设计单位',
    prop: 'designUnit',
    placeholder: '请输入设计单位',
    type: 'input',
    rules: [{ required: true, message: '请输入设计单位', trigger: 'blur' }]
  },
  {
    label: '施工单位',
    prop: 'constructionUnit',
    placeholder: '请输入施工单位',
    type: 'input',
    rules: [{ required: true, message: '请输入施工单位', trigger: 'blur' }]
  },
  {
    label: '监理单位',
    prop: 'supervisionUnit',
    placeholder: '请输入监理单位',
    type: 'input',
    rules: [{ required: true, message: '请输入监理单位', trigger: 'blur' }]
  },
  {
    label: '通航等级',
    prop: 'navigationGradeType',
    placeholder: '请选择通航等级',
    type: 'dictSelect',
    dict: 'bridge_navigation_grade',
    rules: [{ required: true, message: '请选择通航等级', trigger: 'change' }]
  },
  {
    label: '墩台防撞设施类型',
    prop: 'pierCollisionAvoidanceType',
    placeholder: '请选择墩台防撞设施类型',
    type: 'dictSelect',
    dict: 'bridge_pier_collision_avoidance',
    rules: [{ required: true, message: '请选择墩台防撞设施类型', trigger: 'change' }]
  },
  {
    label: '是否互通立交',
    prop: 'whetherInterchangeType',
    placeholder: '请选择是否互通立交',
    type: 'dictSelect',
    dict: 'base_data_yes_no',
    rules: [{ required: true, message: '请选择是否互通立交', trigger: 'change' }]
  },
  {
    label: '是否宽路窄桥',
    prop: 'whetherNarrowBridgeType',
    placeholder: '请选择是否宽路窄桥',
    type: 'dictSelect',
    dict: 'base_data_yes_no',
    rules: [{ required: true, message: '请选择是否宽路窄桥', trigger: 'change' }]
  },
  {
    label: '是否在长大桥梁目录',
    prop: 'whetherInLongSpanCatalogType',
    placeholder: '请选择是否在长大桥梁目录',
    type: 'dictSelect',
    dict: 'base_data_yes_no',
    rules: [{ required: true, message: '请选择是否在长大桥梁目录', trigger: 'change' }]
  },
  {
    label: '抗震设防等级',
    prop: 'seismicGradeType',
    placeholder: '请选择抗震设防等级',
    type: 'dictSelect',
    dict: 'bridge_seismic_grade',
    rules: [{ required: true, message: '请选择抗震设防等级', trigger: 'change' }]
  },
  {
    label: '收费性质',
    prop: 'chargePropertyType',
    placeholder: '请选择收费性质',
    type: 'dictSelect',
    dict: 'bridge_charge_property',
    rules: [{ required: true, message: '请选择收费性质', trigger: 'change' }]
  },
  {
    label: '是否有健康监测系统',
    prop: 'whetherHealthMonitorSystemType',
    placeholder: '请选择是否有健康监测系统',
    type: 'dictSelect',
    dict: 'base_data_yes_no'
  },
  {
    label: '是否独柱墩',
    prop: 'whetherSingleColumnPierType',
    placeholder: '请选择是否独柱墩',
    type: 'dictSelect',
    dict: 'base_data_yes_no'
  },
  {
    label: '桥梁经纬度',
    propLon: 'longitude',
    propLat: 'latitude',
    placeholder: '经纬度',
    type: 'coordinate',
    prepend: 'lonlat',
    // span:12
  },
  // {
  //   label: '桥梁纬度',
  //   prop: 'latitude',
  //   placeholder: '纬度',
  //   type: 'coordinate',
  //   prepend: 'lat'
  // },
  {
    label: '是否公路界河桥梁',
    prop: 'ifHighwayBoundaryRiverBridge',
    placeholder: '请选择是否公路界河桥梁',
    type: 'dictSelect',
    dict: 'base_data_yes_no',
    rules: [{ required: true, message: '请选择是否公路界河桥梁', trigger: 'change' }]
  },
  {
    label: '立交桥类别',
    prop: 'gradeSeparationType',
    placeholder: '请输入立交桥类别',
    type: 'dictSelect',
    dict: 'grade_separation_type'
  },
  {
    label: '是否公铁立交',
    prop: 'ifHighwayAndRailwayGrade',
    placeholder: '是否公铁立交',
    type: 'dictSelect',
    dict: 'base_data_yes_no'
  },
  {
    label: '工程号',
    prop: 'projectNumber',
    placeholder: '请输入工程号',
    type: 'input'
  },
  {
    label: '是否公铁两用桥梁',
    prop: 'ifHighwayRailwayBridge',
    placeholder: '是否公铁两用桥梁',
    type: 'dictSelect',
    dict: 'base_data_yes_no'
  },
  {
    label: '原桥梁编码',
    prop: 'originalBridgeCode',
    placeholder: '请输入原桥梁编码',
    type: 'input'
  },
  {
    label: '管养单位性质',
    prop: 'bridgeMaintenanceUnitQuality',
    placeholder: '请输入管养单位性质',
    type: 'dictSelect',
    dict: 'bridge_maintenance_unit_quality',
  },
  {
    label: '管养单位编码',
    prop: 'bridgeMaintenanceUnitCode',
    placeholder: '请输入管养单位编码',
    type: 'input'
  },
  {
    label: '总造价(万元)',
    prop: 'totalCostInTenThousands',
    placeholder: '请输入总造价(万元)',
    type: 'inputNumber'
  },
  {
    label: '桥梁所在地点',
    prop: 'bridgeLocation',
    placeholder: '请输入桥梁所在地点',
    type: 'input'
  },
  {
    label: '是否为拆除还建桥',
    prop: 'ifRebuildAfterDemolition',
    placeholder: '是否为拆除还建桥',
    type: 'dictSelect',
    dict: 'base_data_yes_no'
  },
  {
    label: '是否为新增上跨桥',
    prop: 'ifAdditionalOverpassBridge',
    placeholder: '是否为新增上跨桥',
    type: 'dictSelect',
    dict: 'base_data_yes_no'
  },
  // {
  //   label: '修建年度',
  //   prop: 'buildDate',
  //   placeholder: '请选择修建年度',
  //   type: 'date',
  //   rules: [{ required: true, message: '请选择修建年度', trigger: 'change' }]
  // },
  {
    label: '桥梁类型',
    prop: 'bridgeType',
    placeholder: '请输入桥梁类型',
    type: 'dictSelect',
    dict: 'bridge_type'
  },
]
// 结构技术数据
const technology = [
  {
    label: '桥梁全长(m)',
    prop: 'totalLength',
    placeholder: '请输入桥梁全长',
    type: 'inputNumber',
    precision: 2,
    rules: [{ required: true, message: '请输入桥梁全长', trigger: 'blur' }]
  },
  {
    label: '桥面全宽(m)',
    prop: 'deckTotalWidth',
    placeholder: '请输入桥面全宽',
    type: 'inputNumber',
    precision: 2,
    rules: [{ required: true, message: '请输入桥面全宽', trigger: 'blur' }]
  },
  {
    label: '桥面净宽(m)',
    prop: 'deckWidth',
    placeholder: '请输入桥面净宽',
    type: 'inputNumber',
    precision: 2,
    min: 0,
    max: 999.99,
    rules: [{ required: true, message: '请输入桥面净宽', trigger: 'blur' }]
  },
  {
    label: '车行道宽(m)',
    prop: 'laneWidth',
    placeholder: '请输入车行道宽',
    type: 'inputNumber',
    precision: 2,
    rules: [{ required: true, message: '请输入车行道宽', trigger: 'blur' }]
  },
  {
    label: '桥面标高(m)',
    prop: 'deckCenterHeight',
    placeholder: '请输入桥面标高',
    type: 'inputNumber',
    precision: 2,
    rules: [{ required: true, message: '请输入桥面标高', trigger: 'blur' }]
  },
  {
    label: '桥下净高(m)',
    prop: 'underVerticalHeight',
    placeholder: '请输入桥下净高',
    type: 'inputNumber',
    precision: 2
  },
  {
    label: '桥上净高(m)',
    prop: 'clearHeightOnBridge',
    placeholder: '请输入桥上净高',
    type: 'inputNumber',
    precision: 2
  },
  {
    label: '线路起点岸-引道总宽(m)',
    prop: 'totalChannelWidthStart',
    placeholder: '请输入线路起点岸-引道总宽',
    type: 'inputNumber',
    precision: 2
  },
  {
    label: '线路止点岸-引道总宽(m)',
    prop: 'totalChannelWidthEnd',
    placeholder: '请输入线路止点岸-引道总宽',
    type: 'inputNumber',
    precision: 2
  },
  {
    label: '线路起点岸-引道路面宽(m)',
    prop: 'channelDeckWidthStart',
    placeholder: '请输入线路起点岸-引道路面宽',
    type: 'inputNumber',
    precision: 2
  },
  {
    label: '线路止点岸-引道路面宽(m)',
    prop: 'channelDeckWidthEnd',
    placeholder: '请输入线路止点岸-引道路面宽',
    type: 'inputNumber',
    precision: 2
  },
  {
    label: '线路起点岸-引道线形',
    prop: 'channelAlignmentStartType',
    placeholder: '请选择线路起点岸-引道线形',
    type: 'dictSelect',
    dict: 'bridge_approach_type'
  },
  {
    label: '线路止点岸-引道线形',
    prop: 'channelAlignmentEndType',
    placeholder: '请选择线路止点岸-引道线形',
    type: 'dictSelect',
    dict: 'bridge_approach_type'
  },
  {
    label: '线路起点岸-引道曲线半径(m)',
    prop: 'channelCurveRadiusStart',
    placeholder: '请选择线路起点岸-引道曲线半径',
    type: 'inputNumber',
    precision: 2
  },
  {
    label: '线路止点岸-引道曲线半径(m)',
    prop: 'channelCurveRadiusEnd',
    placeholder: '请选择线路止点岸-引道曲线半径',
    type: 'inputNumber',
    precision: 2
  },
  {
    label: '人行道宽度(m)',
    prop: 'sidewalkWidth',
    placeholder: '请输入人行道宽度',
    type: 'inputNumber',
    precision: 2
  },
  {
    label: '中护栏等级',
    prop: 'mediumBarrierGradeType',
    placeholder: '请选择中护栏等级',
    type: 'dictSelect',
    dict: 'bridge_barrier_grade'
  },
  {
    label: '边护栏等级',
    prop: 'gradeOfSideRailType',
    placeholder: '请选择边护栏等级',
    type: 'dictSelect',
    dict: 'bridge_barrier_grade'
  },
  {
    label: '护栏或防撞栏高度(m)',
    prop: 'guardrailHeight',
    placeholder: '请输入护栏或防撞栏高度',
    type: 'inputNumber',
    precision: 2
  },
  {
    label: '中央分隔带宽度(m)',
    prop: 'centralSeparatorWidth',
    placeholder: '请输入中央分隔带宽度',
    type: 'inputNumber',
    precision: 2
  },
  {
    label: '桥面标准净空(m)',
    prop: 'deckStandardClearance',
    placeholder: '请输入桥面标准净空',
    type: 'inputNumber',
    precision: 2
  },
  {
    label: '桥面实际净空(m)',
    prop: 'deckActualClearance',
    placeholder: '请输入桥面实际净空',
    type: 'inputNumber',
    precision: 2
  },
  {
    label: '桥下实际净空(m)',
    prop: 'underActualClearance',
    placeholder: '请输入桥下实际净空',
    type: 'inputNumber',
    precision: 2
  },
  {
    label: '桥下通航等级及标准净空(m)',
    prop: 'underStandardClearance',
    placeholder: '请输入桥下通航等级及标准净空',
    type: 'inputNumber',
    precision: 2
  },
  {
    label: '设计洪水频率(年)',
    prop: 'designFloodFrequencyType',
    placeholder: '请选择设计洪水频率(年)',
    type: 'dictSelect',
    dict: 'bridge_design_flood_frequency',
    rules: [{ required: true, message: '请选择设计洪水频率(年)', trigger: 'change' }]
  },
  {
    label: '设计洪水位',
    prop: 'designFloodLevelType',
    placeholder: '请输入设计洪水位',
    type: 'input'
  },
  {
    label: '历史洪水位',
    prop: 'historicalFloodLevel',
    placeholder: '请输入历史洪水位',
    type: 'input'
  },
  {
    label: '地震动峰值加速度系数',
    prop: 'earthquakeAccelerationType',
    placeholder: '请选择地震动峰值加速度系数',
    type: 'dictSelect',
    dict: 'bridge_earthquake_acceleration'
  },
]
// 结构信息数据
const information = [
  {
    label: '跨径组合(孔*米)',
    prop: 'spanGroups',
    placeholder: '请输入跨径组合(孔*米)',
    type: 'input',
  },
  {
    label: '主桥上部构造结构形式',
    prop: 'mainSuperstructureType',
    placeholder: '请选择主桥上部构造结构形式',
    type: 'dictSelect',
    dict: 'bridge_main_superstructure_type',
    rules: [{ required: true, message: '请选择主桥上部构造结构形式', trigger: 'change' }]
  },
  {
    label: '主桥上部构造材料类型',
    prop: 'mainSuperstructureMaterialType',
    placeholder: '请输入主桥上部构造材料类型',
    type: 'dictSelect',
    dict: 'bridge_main_superstructure_material_type',
    rules: [{ required: true, message: '请输入主桥上部构造材料类型', trigger: 'change' }]
  },
  {
    label: '桥墩类型',
    prop: 'pierType',
    placeholder: '请选择桥墩类型',
    type: 'dictSelect',
    dict: 'bridge_pier_types',
    multiple: true,
    rules: [{ required: true, message: '请选择桥墩类型', trigger: 'change' }]
  },
  {
    label: '桥墩材料',
    prop: 'pierMaterialsType',
    placeholder: '请选择桥墩材料',
    type: 'dictSelect',
    dict: 'bridge_pier_materials'
  },
  {
    label: '桥台类型',
    prop: 'abutmentType',
    placeholder: '请选择桥台类型',
    type: 'dictSelect',
    dict: 'bridge_abutment_types',
    multiple: true
  },
  {
    label: '桥台材料',
    prop: 'abutmentMaterialsType',
    placeholder: '请选择桥台材料',
    type: 'dictSelect',
    dict: 'bridge_abutment_materials'
  },
  {
    label: '基础形式',
    prop: 'baseType',
    placeholder: '请选择基础形式',
    type: 'dictSelect',
    dict: 'bridge_base_types'
  },
  {
    label: '基础材料',
    prop: 'baseMaterialsType',
    placeholder: '请选择基础材料',
    type: 'dictSelect',
    dict: 'bridge_base_materials'
  },
  {
    label: '支座类型',
    prop: 'bearingType',
    placeholder: '请选择支座类型',
    type: 'dictSelect',
    dict: 'bridge_bearing_types',
    multiple: true
  },
  {
    label: '伸缩缝类型',
    prop: 'expansionJointType',
    placeholder: '请选择伸缩缝类型',
    type: 'dictSelect',
    dict: 'bridge_expansion_joint_types',
    multiple: true
  },
  {
    label: '桥面铺装类型',
    prop: 'deckPavementType',
    placeholder: '请选择桥面铺装类型',
    type: 'dictSelect',
    dict: 'bridge_deck_pavement_types'
  },
  {
    label: '拱桥矢跨比',
    prop: 'archRiseSpanRatio',
    placeholder: '请输入拱桥矢跨比',
    type: 'input'
  },
  {
    label: '桥梁验算荷载',
    prop: 'bridgeCheckLoad',
    placeholder: '请输入桥梁验算荷载',
    type: 'dictSelect',
    dict: 'bridge_check_load',
  },
  {
    label: '防洪标准(年)',
    prop: 'bridgeFloodStandard',
    placeholder: '请选择防洪标准(年)',
    type: 'dictSelect',
    dict: 'bridge_flood_standard',
  },
  {
    label: '下部结构形式',
    prop: 'bridgeSubstructureType',
    placeholder: '请输入下部结构形式',
    type: 'dictSelect',
    dict: 'bridge_substructure_type'
  },
  {
    label: '下部材料类型',
    prop: 'bridgeSubstructureMaterialType',
    placeholder: '请输入下部材料类型',
    type: 'input'
  },
  {
    label: '主桥主跨',
    prop: 'mainBridgeMainSpan',
    placeholder: '请输入主桥主跨',
    type: 'input'
  },
  {
    label: '主桥边跨',
    prop: 'mainBridgeSideSpan',
    placeholder: '请输入主桥边跨',
    type: 'input'
  },
  {
    label: '前引桥长',
    prop: 'frontApproachBridgeLength',
    placeholder: '请输入前引桥长',
    type: 'inputNumber'
  },
  {
    label: '后引桥长',
    prop: 'rearApproachBridgeLength',
    placeholder: '请输入后引桥长',
    type: 'inputNumber'
  },
  {
    label: '主桥孔数',
    prop: 'mainBridgeHoleCount',
    placeholder: '请输入主桥孔数',
    type: 'inputNumber'
  },
  {
    label: '桥梁孔数',
    prop: 'bridgeHoleCount',
    placeholder: '请输入桥梁孔数',
    type: 'inputNumber'
  },
  {
    label: '桥梁高度(米)',
    prop: 'bridgeHeight',
    placeholder: '请输入桥梁高度(米)',
    type: 'inputNumber'
  },
  {
    label: '弯坡斜特征',
    prop: 'bridgeCurvedSlopeFeatures',
    placeholder: '请输入弯坡斜特征',
    type: 'dictSelect',
    dict: 'bridge_curved_slope_features',
  },
  {
    label: '桥面线型',
    prop: 'bridgeDeckType',
    placeholder: '请输入桥面线型',
    type: 'dictSelect',
    dict: 'bridge_deck_type',
  },
  {
    label: '桥位地形',
    prop: 'bridgeSiteTopography',
    placeholder: '请输入桥位地形',
    type: 'dictSelect',
    dict: 'bridge_site_topography',
  },
  {
    label: '跨线桥类型',
    prop: 'overpassBridgeType',
    placeholder: '请输入跨线桥类型 ',
    type: 'dictSelect',
    dict: 'overpass_bridge_type',
  },
]
// 桥梁照片
const images = [
  {
    label: '桥梁正面照',
    prop: 'frontPhotoId',
    placeholder: '请选择文件'
  },
  {
    label: '桥梁立面照',
    prop: 'facadePhotoId',
    placeholder: '请选择文件'
  },
  {
    label: '桥梁典型照',
    prop: 'typicalPhotoId',
    placeholder: '请选择文件'
  }
]
// 档案资料
const archives = [
  // {
  //   label: '设计图纸',
  //   prop: 'designPapersId',
  //   placeholder: '请选择文件'
  // },
  // {
  //   label: '设计文件',
  //   prop: 'designFilesId',
  //   placeholder: '请选择文件'
  // },
  // {
  //   label: '竣工图纸',
  //   prop: 'finishPapersId',
  //   placeholder: '请选择文件'
  // },
  // {
  //   label: '施工文件(含施工缺陷处理)',
  //   prop: 'constructFilesId',
  //   placeholder: '请选择文件'
  // },
  // {
  //   label: '验收文件',
  //   prop: 'acceptanceFilesId',
  //   placeholder: '请选择文件'
  // },
  // {
  //   label: '行政审批文件',
  //   prop: 'administrationFilesId',
  //   placeholder: '请选择文件'
  // }

  {
    label: '设计图纸',
    prop: 'designDrawing',
    type: 'dictSelect',
    dict: 'base_archival_data'
  },
  {
    label: '设计文件',
    prop: 'designDocument',
    type: 'dictSelect',
    dict: 'base_archival_data'
  },
  {
    label: '施工文件(含施工缺陷处理)',
    prop: 'constructionDocument',
    type: 'dictSelect',
    dict: 'base_archival_data'
  },
  {
    label: '竣工图纸',
    prop: 'completedDrawing',
    type: 'dictSelect',
    dict: 'base_archival_data'
  },
  {
    label: '验收文件',
    prop: 'acceptanceDocument',
    type: 'dictSelect',
    dict: 'base_archival_data'
  },
  {
    label: '行政审批文件',
    prop: 'administrativeApprovalDocument',
    type: 'dictSelect',
    dict: 'base_archival_data'
  },
  {
    label: '定期检资料',
    prop: 'periodicInspectionInformation',
    type: 'dictSelect',
    dict: 'base_archival_data'
  },
  {
    label: '特别检查资料',
    prop: 'specialInspectionInformation',
    type: 'dictSelect',
    dict: 'base_archival_data'
  },
  {
    label: '历次维修、加固资料',
    prop: 'previousMaintenanceInformation',
    type: 'dictSelect',
    dict: 'base_archival_data'
  },
  {
    label: '其他档案',
    prop: 'otherArchives',
    type: 'dictSelect',
    dict: 'base_archival_data'
  },
  {
    label: '档案形式',
    prop: 'fileForm',
    type: 'input'
  },
  {
    label: '建档时间',
    prop: 'filingTime',
    type: 'date',
    placeholder: '请选择时间 yyyy-mm-dd',
  }
]
// 涉水信息
const wade = [
  {
    label: '是否涉水桥梁',
    prop: 'whetherWaterBridge',
    placeholder: '请选择是否涉水桥梁',
    type: 'dictSelect',
    dict: 'base_data_yes_no'
  },
  {
    label: '墩柱是否在行洪范围内',
    prop: 'whetherPiersInFlood',
    placeholder: '请选择墩柱是否在行洪范围内',
    type: 'dictSelect',
    dict: 'base_data_yes_no'
  },
  {
    label: '桥梁墩柱是否在水中',
    prop: 'whetherPiersInWater',
    placeholder: '请选择桥梁墩柱是否在水中',
    type: 'dictSelect',
    dict: 'base_data_yes_no'
  },
  {
    label: '是否跨越饮用水水源保护区',
    prop: 'whetherCrossProtectionWater',
    placeholder: '请选择是否跨越饮用水水源保护区',
    type: 'dictSelect',
    dict: 'base_data_yes_no'
  },
  {
    label: '是否跨越集中式饮用水水源取水口',
    prop: 'whetherCrossDrinkingWater',
    placeholder: '请选择是否跨越集中式饮用水水源取水口',
    type: 'dictSelect',
    dict: 'base_data_yes_no'
  },
  {
    label: '跨越水质类别',
    prop: 'crossingWaterQualityType',
    placeholder: '请选择是否跨越集中式饮用水水源取水口',
    type: 'dictSelect',
    dict: 'mpkj_water_type'
  },
  {
    label: '危险货物道路运输车辆禁限行情况',
    prop: 'dangerousGoodsRoadTransportation',
    placeholder: '请输入',
    type: 'input',
  },
  {
    label: '涉水摸排时间',
    prop: 'waterWadingTime',
    placeholder: '请选择涉水摸排时间',
    type: 'date',
  },
  {
    label: '涉水摸排负责人',
    prop: 'chargeOfWaterWading',
    placeholder: '请输入',
    type: 'input',
  },
  {
    label: '桥梁径流收集系统、事故应急池等环保设施建设情况',
    prop: 'runoffCollectionEmergencyEnvironmental',
    placeholder: '请输入',
    type: 'input',
    span: 12
  },
]
// 其他数据
const other = [
  {
    label: '养护检查等级',
    prop: 'maintenanceCheckGrade',
    placeholder: '请选择养护检查等级',
    type: 'dictSelect',
    dict: 'bridge_maintenance_check_grade'
  },
  {
    label: '是否跨国界',
    prop: 'isCrossBorderType',
    placeholder: '请选择是否跨国界',
    type: 'dictSelect',
    dict: 'base_data_yes_no'
  },
  {
    label: '是否危桥',
    prop: 'isDangerType',
    placeholder: '请选择是否危桥',
    type: 'dictSelect',
    dict: 'base_data_yes_no'
  },
  {
    label: '是否跨省桥梁',
    prop: 'isCrossProvinceType',
    placeholder: '请选择是否跨省桥梁',
    type: 'dictSelect',
    dict: 'base_data_yes_no',
    rules: [{ required: true, message: '请选择是否跨省桥梁', trigger: 'change' }]
  },
  {
    label: '路线类型',
    prop: 'routeType',
    placeholder: '请选择路线类型',
    type: 'dictSelect',
    dict: 'bridge_route_type',

  },
  {
    label: '跨越地物类型',
    prop: 'acrossType',
    placeholder: '请选择跨越地物类型',
    type: 'dictSelect',
    dict: 'bridge_across_type',
    rules: [{ required: true, message: '请选择跨越地物类型', trigger: 'change' }]
  },
  {
    label: '跨越地物名称',
    prop: 'acrossName',
    placeholder: '请输入跨越地物名称',
    type: 'input',
    rules: [{ required: true, message: '请输入跨越地物名称', trigger: 'blur' }]
  },
  // {
  //   label: '主桥上部构造材料',
  //   prop: 'mainMaterialType',
  //   placeholder: '请选择主桥上部构造材料',
  //   type: 'dictSelect',
  //   dict: 'bridge_main_material'
  // },
  {
    label: '交通管制措施',
    prop: 'trafficMeasureType',
    placeholder: '请选择交通管制措施',
    type: 'dictSelect',
    dict: 'bridge_traffic_measure',
    rules: [{ required: true, message: '请选择交通管制措施', trigger: 'change' }]
  },
  {
    label: '单孔最大跨径(m)',
    prop: 'maxSpan',
    placeholder: '请输入单孔最大跨径',
    type: 'inputNumber',
    precision: 3,
    min: 0,
    max: 9999.999,
    rules: [{ required: true, message: '请输入单孔最大跨径', trigger: 'blur' }]
  },
  {
    label: '跨径总长',
    prop: 'spanTotalLength',
    placeholder: '请输入跨径总长',
    type: 'inputNumber',
    precision: 3,
    min: 0,
    max: 9999.999,
    rules: [{ required: true, message: '请输入跨径总长', trigger: 'blur' }]
  },
  {
    label: '路线属性',
    prop: 'routeClassifyType',
    placeholder: '请选择路线属性',
    type: 'dictSelect',
    dict: 'bridge_route_classify',
  },
  {
    label: '运营状态',
    prop: 'operationState',
    placeholder: '请选择运营状态',
    type: 'dictSelect',
    dict: 'sys_operation_state'
  },
  {
    label: '桥梁身份码',
    prop: 'bridgeIdentityCode',
    placeholder: '请输入桥梁身份码',
    type: 'input',
    // rules: [{ required: true, message: '请输入桥梁身份码', trigger: 'blur' }]
  },
  {
    label: '最近技术状况',
    prop: 'bridgeTechAssessType',
    placeholder: '请选择最近技术状况',
    type: 'dictSelect',
    dict: 'bridge_tec_condition_level',

    forView:true
  },
  {
    label: '评定日期',
    prop: 'bridgeTechAssessDate',
    placeholder: '请选择评定日期',
    type: 'date',
    forView:true
  },
  {
    label: '评定单位',
    prop: 'bridgeTechAssessUnit',
    placeholder: '请输入评定单位',
    type: 'input',

    forView:true
  },
  // {
  //   label: '评定为四五类桥梁的原因',
  //   prop: 'none',
  //   placeholder: '评定为四五类桥梁的原因',
  //   type: 'input'
  // },
  {
    label: '桥梁养护工程师',
    prop: 'bridgeMaintenanceEngineer',
    placeholder: '请输入桥梁养护工程师',
    type: 'input'
  },
  {
    label: '建成通车日期',
    prop: 'buildOpenedDate',
    placeholder: '请选择建成通车日期',
    type: 'date',
  },
  {
    label: '按使用年限分类',
    prop: 'bridgeYearUsed',
    placeholder: '请选择按使用年限分类',
    type: 'dictSelect',
    dict: 'bridge_year_used',
    rules: [{ required: true, message: '请选择按使用年限分类', trigger: 'change' }]
  },
  {
    label: '管理单位',
    prop: 'managementUnit',
    placeholder: '请输入管理单位',
    type: 'input',
    rules: [{ required: true, message: '请输入管理单位', trigger: 'blur' }]
  },
  {
    label: '监管单位',
    prop: 'superUnitName',
    placeholder: '请输入监管单位',
    type: 'input',
    rules: [{ required: true, message: '请输入监管单位', trigger: 'blur' }]
  },

  {
    label: '竣工日期',
    prop: 'finishPapersDate',
    placeholder: '请选择竣工日期',
    type: 'date'
  },
  {
    label: '填报单位',
    prop: 'reportingUnit',
    placeholder: '请输入填报单位',
    type: 'input'
  },
  {
    label: '填报单位编码',
    prop: 'reportingUnitCode',
    placeholder: '请输入填报单位编码',
    type: 'input'
  },
  // {
  //   label: '最近定期检查日期',
  //   prop: 'none',
  //   placeholder: '请选择最近定期检查日期',
  //   type: 'date'
  // },

  {
    label: '移交管理单位',
    prop: 'transferManagementUnit',
    placeholder: '请输入移交管理单位',

    type: 'input',
  },

  {
    label: '备注',
    prop: 'remark',
    placeholder: '请输入备注',
    type: 'inputTextarea',
    span:12
  },
]

export default {
  administrative,
  technology,
  information,
  images,
  archives,
  wade,
  other
}
