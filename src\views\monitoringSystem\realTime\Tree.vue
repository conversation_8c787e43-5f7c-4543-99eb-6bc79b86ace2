<template>
  <div class="tree-box" :class="theme" v-loading="loading" element-loading-background="rgba(0, 0, 0, 0.05)">
    <div class="title">{{ title }}</div>
    <el-input ref="inputRef" placeholder="输入关键字进行过滤" v-model="filterText">
      <i slot="suffix" class="el-input__icon el-icon-search"></i>
    </el-input>
    <el-tree v-if="flag" ref="tree" :data="treeData" :props="treeProps" @node-click="handleNodeClick"
      :default-expand-all="treeExpandAll" node-key="code" :default-expanded-keys="expandKeys"
      :filter-node-method="filterNode" :check-strictly="true" :lazy="lazy" :load="loadNode">
      <template slot-scope="{ node, data }">
        <span :class="[node.level == 1 ? 'first-level' : 'children-level']">
          {{ data.label }}
        </span>
      </template>
    </el-tree>
  </div>
</template>

<script>
import { fetchGet } from './api.js'

export default {
  name: 'Tree',
  props: {
    theme: {
      type: String,
      default: 'dark'
    }
  },
  components: {},
  data() {
    return {
      treeData: [
      ],
      treeProps: {
        children: "children",
        label: "label",
        isLeaf: "isLeaf",
      },
      treeExpandAll: false,
      loading: false,
      expandKeys: [],
      filterText: '',
      title: "桥梁",
      code: '',
      lazy: false,
      facilityInfo: {},
      videoInfo: {},
      flag: false,
      canTreeClick: true
    }
  },
  created() {
    this.init()
    window.$Bus.$on('treeClick', (f) => {
      this.canTreeClick = f;
    })
    window.$Bus.$on('bimToCharts', (list) => {
      if (!list || !list.length || !list[0].code) return;
      // 根据code反向勾选树节点
      this.$refs.tree.setCheckedKeys([list[0].code]);
      // 先关闭所有节点
      Object.values(this.$refs.tree.store.nodesMap).forEach(node => {
        node.expanded = false;
      });
      const node = this.$refs.tree.getNode(list[0].code);
      // 展开目标节点及其所有父节点
      const expandParents = (node) => {
        if (node.parent) {
          expandParents(node.parent);
          node.expanded = true;
        }
      };
      expandParents(node);
      this.$emit('treeNodeClick', list);
    })
    window.$Bus.$on('changeTreeNode', (obj) => {
      if (!obj || !obj.code) return;
      // 根据code反向勾选树节点
      this.$refs.tree.setCheckedKeys([obj.code]);
      // 先关闭所有节点
      Object.values(this.$refs.tree.store.nodesMap).forEach(node => {
        node.expanded = false;
      });
      const node = this.$refs.tree.getNode(obj.code);
      // 展开目标节点及其所有父节点
      const expandParents = (node) => {
        if (node.parent) {
          expandParents(node.parent);
          node.expanded = true;
        }
      };
      expandParents(node);
    })
  },
  methods: {
    // 初始化
    async init() {
      this.code = this.$route.query.code;
      if (!this.code) return;
      try {
        this.loading = true;
        const [facilityInfo, sensorInfo, videoInfo] = await Promise.all([
          this.getFacilityInfo(),
          this.getSensorInfo(),
          this.getVideoList()
        ]);
        // 告诉BimAndPicture组件，有没有bim模型、全景图、视频、图片路径
        let obj = {
          fileId: facilityInfo?.fileId || null,
          structureImage: facilityInfo?.structureImage || null,
          panoramic720view: facilityInfo?.panoramic720view || null,
          videoInfo: videoInfo?.children || null,
          code: facilityInfo?.code
        }
        window.$Bus.$emit('treeToBim', obj);
        // 当getSensorInfo方法返回空数组时，懒加载
        if (sensorInfo.length === 0) {
          this.facilityInfo = facilityInfo;
          this.videoInfo = videoInfo
          this.lazy = true;
          this.treeData = [facilityInfo];
          this.$forceUpdate();
          this.flag = true;
          return;
        }
        if (facilityInfo && sensorInfo) {
          this.$emit('getStructureDisplayHtmlText', facilityInfo.structureDisplayHtmlText || '')
          this.title = facilityInfo.structureType;
          this.setTreeData(facilityInfo, sensorInfo, videoInfo);
          this.flag = true;
        }
      } catch (error) {
        console.error('初始化数据时出错:');
      } finally {
        this.loading = false;
      }
    },
    /**
     * 根据 code 获取设施信息（理解为外层的父节点）
     * @async
     * @returns {Promise<null|Object>}
     */
    async getFacilityInfo() {
      const url = "https://jkjc.yciccloud.com:8000/xboot/structureNormalDataManage/getByCode";
      const params = { code: this.code };
      const res = await fetchGet(url, params);
      return res?.code === 200 ? res.result : [];
    },
    /**
     * 根据 code 获取目录树节点（理解为子节点）
     * @async
     * @returns {Promise<null|Array>}
     */
    async getSensorInfo() {
      const url = "https://jkjc.yciccloud.com:8000/xboot/projectStructureManage/recursionNodeUnderStructure";
      const params = { structureNodeCode: this.code };
      const res = await fetchGet(url, params);
      // 临时：“车辆荷载”节点做特殊处理
      res.result.forEach(item => {
        if(item.content.includes('车辆')) {
          item.children.forEach(el => {
            el.vehicleChild = el.children
            delete el.children
          })
        }
      })
      return res?.code === 200 ? res.result : [];
    },
    // 通过接口判断是否有视频
    async getVideoList() {
      const url = "https://jkjc.yciccloud.com:8000/xboot/structurevideo/getByStructureCode"
      const params = { nodeCode: this.code, currentPage: -1, pageSize: -1 };
      const res = await fetchGet(url, params);
      let b = res.result.records;
      for (let i = 0; i < b.length; i++) {
        b[i].type = "video";
        b[i].label = b[i].videoDisplayName || b[i].videoIp;
      }
      let a = undefined;
      if (res.code == '200' && b.length > 0) {
        a = {
          id: "1X",
          label: "视频",
          type: "videoIcon",
          children: b
        }
      }
      return a;
    },
    /**
     * 组装树数据，将设施信息（父）和目录树信息（子）组装成适合 el-tree 组件使用的树结构数据。
     * @param {Object} facilityInfo - 设施信息对象（父）
     * @param {Array} sensorInfo - 目录树信息（子）
     */
    setTreeData(facilityInfo, sensorInfo, videoInfo) {
      const treeD = this.transform(sensorInfo, this.code);
      this.treeData.push({
        ...facilityInfo,
        label: facilityInfo.structureName,
        children: treeD
      });
      this.expandKeys = [facilityInfo.code]
      // if (videoInfo) {
      //   this.treeData[0].children.push(videoInfo)
      // }
    },
    // 递归
    transform(arr = [], pcode) {
      return arr.map(item => ({
        ...item,
        label: item.content,
        parentCode: pcode,
        children: item.children?.length ? this.transform(item.children, item.code) : []
      }));
    },
    // 懒加载，当getSensorInfo方法返回空数组时
    loadNode(node, resolve) {
      if (node.level === 0) {
        const arr = [{
          ...this.facilityInfo,
          label: this.facilityInfo.structureName,
        }]
        resolve(arr);
      }
      if (node.level >= 1 && node.data.label !== '视频') {
        const url = "https://jkjc.yciccloud.com:8000/xboot/projectStructureManage/getChildren"
        const params = { nodeCode: node.data.code };
        fetchGet(url, params).then(res => {
          if (res.code === 200) {
            const children = res.result
            children.forEach(item => {
              if (item.nodeLevel == 8) {
                item.children = [];
                item.isLeaf = true;
              }
            })
            const treeD = this.transform(children, this.code);
            if (node.level === 1) {
              if (this.videoInfo) {
                treeD.push(this.videoInfo);
              }
            }
            resolve(treeD);
          }
        })
      }
      if (node.data.label == '视频') {
        this.videoInfo.children.forEach(item => {
          item.children = [];
          item.isLeaf = true;
        })
        resolve(this.videoInfo.children);
      }
    },
    async getLastOneDataForSKWY(query) {
      const url = "https://jkjc.yciccloud.com:8000/xboot/data/getLastOneDataForSKWY";
      const params = { structureNodeCode: this.code, ...query };
      const res = await fetchGet(url, params);
      return res?.code === 200 ? res.result : [];
    },
    // 点击节点
    async handleNodeClick(data, node) { // 标记为 async 函数
      if (data.nodeLevel == 8) {
        if (!this.canTreeClick) return;
        const checkKeys = this.$refs.tree.getCheckedKeys() || []
        if (checkKeys[0] === data.code) return;
        // 如果是叶子节点勾选当前节点, 为了给改节点加上.is-checked样式
        this.$refs.tree.setCheckedKeys([data.code]);
        // 获取节点下的传感器信息
        const url = "https://jkjc.yciccloud.com:8000/xboot/sensorManage/getSensorGroupBySensorIdByCode";
        const params = { nodeCode: data.code };
        this.loading = true

        fetchGet(url, params).then(res => {
          if (res.code === 200) {
            let dataForSKWY = []
            if (node.parent?.data?.content.includes("深孔位移")) {
              let num = res.result.length
              let sensorId = res.result[0].sensorId
              let nodeCode = res.result[0].code
              let query = {
                num,
                sensorId,
                nodeCode,
              }
              this.getLastOneDataForSKWY(query).then((res1) => {
                dataForSKWY = res1
                this.$emit('treeNodeClick', res.result, dataForSKWY);
              })
            } else {
              this.$emit('treeNodeClick', res.result);
              window.$Bus.$emit("BimShowLable", res.result[0]?.sensorId || null);
            }
          }
        }).catch((error) => {
          this.$message.error('没有传感器数据')
        }).finally(() => {
          this.loading = false
        });
      }
      if (data.type == 'video') {
        this.$emit('openVideo', data);
      }
      if (data.vehicleChild) {
        this.loading = true
        let a = []
        const promises = data.vehicleChild.map(async(el) => {
          const url = "https://jkjc.yciccloud.com:8000/xboot/sensorManage/getSensorGroupBySensorIdByCode";
          const params = { nodeCode: el.code };
          const res = await fetchGet(url, params)
          if (res.code === 200) {
            res.result[0].isVehicle = true
            return res.result[0]
          }
          return null
        })

        const results = await Promise.all(promises)
        a = results.filter(result => result !== null)
        this.loading = false
        this.$emit('treeNodeClick', a);
        window.$Bus.$emit("BimShowLable", a[0]?.sensorId || null);
      }
    },
    // 过滤节点
    filterNode(value, data) {
      if (!value) return true;
      return data.label.indexOf(value) !== -1;
    },
  },
  computed: {},
  watch: {
    // 监听输入框filterText变化
    filterText(val) {
      this.$refs.tree.filter(val);
    }
  },
  beforeDestroy() {
    window.$Bus.$off('treeClick');
    window.$Bus.$off('bimToCharts');
    window.$Bus.$off('changeTreeNode');
  }
}
</script>

<style lang="scss" scoped>
@import "@/assets/styles/utils.scss";

.tree-box {
  width: 100%;
  height: 100%;
  overflow: hidden;

  .title {
    width: 100%;
    height: vwpx(68px);
    background-repeat: no-repeat;
    background-size: 100% 100%;
    background-image: url("~@/assets/map/title-bg.png");
    margin-bottom: vwpx(20px);
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-left: vwpx(50px);
    font-family: YouSheBiaoTiHei, YouSheBiaoTiHei;
    font-weight: 400;
    // font-size: vwpx(32px);
    font-size: 1.8vh;
    color: #ffffff;
    letter-spacing: vwpx(2px);
    line-height: vwpx(64px);
  }
}

::-webkit-scrollbar {
  width: vwpx(14px);
  height: vwpx(14px);
}

.dark {
  ::v-deep {
    .el-input__inner {
      background-color: rgba(1, 102, 254, 0.2);
      border: 1px solid #0166fe;
      color: #ffffff;
      padding-right: vwpx(70px);
      padding-left: vwpx(30px);
    }
  }

  ::-webkit-scrollbar-thumb {
    background-color: rgba(1, 102, 254, 0.4);
  }

  ::-webkit-scrollbar-track {
    background-color: rgba(0, 35, 94, .6);
  }
}

.first-level {
  font-size: vwpx(36px);
}

.children-level {
  font-size: vwpx(32px);
}

::v-deep {
  .el-tree {
    background: inherit; // 继承父元素的背景
    color: inherit; // 继承父元素的文本颜色
    height: calc(100% - #{vwpx(176px)});
    overflow: auto;
    font-family: Arial, sans-serif !important;
  }

  // 勾选的节点高亮，注意设置check-strictly为true，让父子节点互不影响
  .is-checked {
    background: linear-gradient(270deg, rgba(0, 94, 255, 0.05) 0%, rgba(1, 102, 254, 0.5) 100%) !important;
  }

  // 节点颜色
  .el-tree-node__content {
    height: vwpx(70px);
    background: linear-gradient(270deg, rgba(0, 94, 255, 0.1) 0%, rgba(1, 102, 254, 0.01) 100%);
    margin-bottom: vwpx(6px);
  }

  // 鼠标经过的颜色
  .el-tree-node__content:hover {
    background: linear-gradient(270deg, rgba(0, 94, 255, 0.05) 0%, rgba(1, 102, 254, 0.5) 100%);
  }

  // 节点被选中的颜色（不是勾选的意思，是鼠标focus时的颜色）
  .el-tree-node:focus>.el-tree-node__content,
  .el-tree--highlight-current .el-tree-node.is-current>.el-tree-node__content {
    background: linear-gradient(270deg, rgba(0, 94, 255, 0.05) 0%, rgba(1, 102, 254, 0.5) 100%);
  }

  .el-tree-node__expand-icon {
    font-size: vwpx(32.4px) !important;
  }

  .el-tree-node__content>.el-tree-node__expand-icon {
    padding: vwpx(13.3px)
  }

  .el-loading-mask {
    border-radius: vwpx(14px);
  }

  .el-input__inner {
    height: vwpx(68px);
    font-size: vwpx(32.4px);
    margin-bottom: vwpx(20px);
    line-height: vwpx(68px);
    transition: 0.3s;
  }

  .el-input__icon {
    height: vwpx(68px);
    font-size: vwpx(32.4px);
    line-height: vwpx(68px);
  }
}
</style>