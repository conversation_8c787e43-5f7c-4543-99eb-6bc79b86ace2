<template>
  <el-container>
    <el-header>
      <div class="data2">
        <el-form
            :model="form"
            :inline="true"
            label-position="right"
            label-suffix="："
            ref="tigaForm"
            label-width="85px">
          <el-form-item label="监测类型" prop="monitorType">
            <el-select
                v-model="form.monitorType"
                placeholder="请选择监测类型"
                @change="changeMonitorType"
                no-data-text="无数据"
                value-key="content"
                size="small"
            >
              <el-option
                  v-for="item in monitorTypeList"
                  :key="item.code"
                  :label="item.content"
                  :value="item">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="监测内容" prop="monitorContent">
            <el-select
                v-model="form.monitorContent"
                default-first-option
                placeholder="请选择监测内容"
                @change="changeMonitorContent"
                no-data-text="无数据"
                value-key="content"
                size="small"

            >
              <el-option
                  v-for="item in monitorContentList"
                  :key="item.code"
                  :label="item.content"
                  :value="item">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="监测位置" prop="monitorLocation">
            <el-select
                v-model="form.monitorLocation"
                default-first-option
                placeholder="请选择监测位置"
                @change="changeMonitorLocation"
                no-data-text="无数据"
                value-key="content"
                size="small"
            >
              <el-option
                  v-for="item in monitorLocationList"
                  :key="item.code"
                  :label="item.content"
                  :value="item">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="传感器" prop="sensorId">
            <el-select
                v-model="form.sensorId"
                placeholder="请选择传感器"
                @change="changeSensor"
                no-data-text="无数据"
                value-key="sensorId"
                size="small"
            >
              <el-option
                  v-for="item in sensorInfoList"
                  :key="item.code"
                  :label="item.sensorInstallCode"
                  :value="item">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="具体类型" prop="specificMonitorType">
            <el-select
                v-model="form.specificMonitorType"
                filterable
                collapse-tags
                :placeholder="tips1"
                no-data-text="无数据"
                size="small"
                value-key="specificMonitorTypeId"
                :disabled="isDraw"
            >
              <el-option
                  v-for="item in specificMonitorTypeList"
                  :label="item.specificMonitorTypeName"
                  :key="item.specificMonitorTypeId"
                  :value="item.specificMonitorTypeId"
              >
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="时间范围" prop="timerange">
            <el-date-picker
                size="medium"
                style="width:200px"
                type="datetimerange"
                v-model="form.timerange"
                value-format="yyyy-MM-dd HH:mm:ss"
                format="yyyy-MM-dd HH:mm:ss"
                @on-change="statusDateChange"
                :transfer="true"
            ></el-date-picker>
          </el-form-item>
          <el-form-item>
            <el-button
                type="primary"
                @click="draw1"
                size="small"
                :loading="loading">
              <span>绘制</span>
            </el-button>
          </el-form-item>
          <el-form-item>
            当前选择结构物为：{{currentStructure.label}}
          </el-form-item>
        </el-form>
      </div>
    </el-header>
    <el-main>
      <div id="dailyVariationChart" v-loading="loading" style="width: 100%; height: 400px; overflow:hidden"></div>
    </el-main>
  </el-container>
</template>

<script>
import {
  dailyVariation,
  getSensorByDataCode,getMax, getMin
} from "@/api/jgjc/dataAnalysis/customize/index.js";
import * as echarts from "echarts";
export default {
  name: "dailyVariation",
  props: ['monitorTypeList', 'currentStructure', 'isDraw'],
  emits: ['changeDraw'],
  data() {
    return {
      referenceValue: 0,
      isZD: false,
      tips1: "请选择具体监测类型",
      monitorContentList: [],
      monitorLocationList: [],
      sensorInfoList: [],
      specificMonitorTypeList: [],
      currentStructure: {},
      form: {
        monitorType: '',
        monitorContent: '',
        monitorLocation: '',
        sensorId: '',
        specificMonitorType: '',
        timerange: ''
      },
      sensorInfo: {},
      loading: false
    };
  },
  methods: {
    // 判断是否为长时查询
    async isLongCheck(isZD, isPreprocess) {
      // 计算时间差值
      let startTime = new Date(this.form.timerange[0]);
      let endTime = new Date(this.form.timerange[1]);
      let deltaTime = endTime.getTime() - startTime.getTime()
      let flag = false

      if(isZD){
        // 振动数据 1天+原始值 或 任意时间+预处理
        if(isPreprocess){
          flag = true
        }else{
          if(deltaTime > 86400000){
            flag = true
          }
        }
      }else {
        // 普通数据 6月+原始值 或 2月+预处理
        if(isPreprocess){
          if(deltaTime > 86400000 * 60){
            flag = true
          }
        }else{
          if(deltaTime > 86400000 * 180){
            flag = true
          }
        }
      }

      if(flag){
        const res = await this.$confirm('当前条件可能造成查询时间过长, 是否继续?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).catch(err => err)
        return res === 'confirm';
      }
      return true
    },
    // “绘制1”方法
    async draw1() {
      if (this.form.monitorType === '' ||
          this.form.monitorContent === '' ||
          this.form.monitorLocation === '' ||
          this.form.sensorId === '' ||
          this.form.specificMonitorType === '' ||
          this.form.timerange.length === 0 ||
          this.form.timerange[0] === ''
      ) {
        this.$message({
          type: 'warning',
          message: "查询数据前请注意查询条件的完整性"
        });
        return;
      }
      if (await this.isLongCheck(this.isZD, true)) {
        this.loading = true
        dailyVariation({
          dataCode: this.form.sensorId,
          structureCode: this.currentStructure.structureCode,
          sensorCode: this.form.specificMonitorType,
          startTime: this.form.timerange[0],
          endTime: this.form.timerange[1],
          granularityNum: 1,
          granularityType: 5,
        }).then((res) => {
          if (res.data.processedData?.errMsg) {
            this.$message.error(res.data.processedData?.errMsg);
            this.setEmptyChart("没有数据");
          } else {
            this.setChart('dailyVariationChart', res.data);
          }
          this.loading = false
        }).catch((err) => {
          console.log(err.message);
          this.setEmptyChart("绘图失败");
          this.loading = false
        });
      } else {
        // 用户取消了本次查询
      }
    },
    setChart(chartName, data) {
      let myChart = echarts.getInstanceByDom(
          document.getElementById(chartName)
      );
      if (myChart !== undefined) {
        myChart.dispose();
      }
      myChart = echarts.init(document.getElementById(chartName));
      // 数据处理 给值增加时间戳
      let tmpDotList0 = []
      for(let i = 0; i < data.processedData.values.length; i++) {
        tmpDotList0.push([data.processedData.times[i], data.processedData.values[i].toFixed(data.accuracy >= 0  ? data.accuracy : 4)])
      }
      data.processedData.dotList = tmpDotList0
      let option = {
        title: {
          left: 'center',
          show: false,
        },
        toolbox: {
          right: 0,
          top: 25,
          feature: {
            saveAsImage: {
              title: '保存'
            },
          },
        },
        tooltip: {
          textStyle:{
            align:'left'
          },
          trigger: "axis",
          confine: true
        },
        xAxis: {
          type: 'category',
          name: "时间",
          nameLocation: "middle",
          nameGap: 22,
          axisLabel: {
            formatter: function (value, index) {
              return value.substring(0, value.length - 12);
            },
            showMinLabel: true,
            showMaxLabel: true,
            fontSize: 11,
          },
        },
        yAxis: [],
        dataZoom: [
          {
            type: "inside",
          },
        ],
        grid: [
          {
            top: "10%",
            left: "2%",
            right: "2%",
            bottom: "10%",
            containLabel: true,
          },
        ],
        series: [],
        legend: {
          type: 'scroll',
          data: [],
          x: 'center',
          y: 'bottom',
          selected: {},
        },
      };
      option.series.push({
        type: "line",
        name: data.name,
        showSymbol: false,
        data: data.processedData.dotList,
      });
      //计算最大值与最小值
      let minValue = getMin(data.processedData.values); // 输出最小值
      let maxValue = getMax(data.processedData.values); // 输出最大值
      // 一些特殊情况处理
      if (maxValue === 0 && minValue === 0) {
        maxValue = 1
        minValue = -1
      } else {
        let delta = Math.ceil((maxValue - minValue) * 0.2 * (10 ** data.accuracy))/(10 ** data.accuracy)
        if(delta === 0){
          delta = 1
        }
        let midValue = Math.ceil((maxValue + minValue) / 2 * (10 ** data.accuracy))/(10 ** data.accuracy)
        maxValue = midValue + 3 * delta
        minValue = midValue - 3 * delta
      }
      option.yAxis.push({
        name: '裂缝日变化值/'+data.unit,
        nameTextStyle: {
          fontWeight: "bold",
          fontSize: 12
        },
        axisLabel: {
          formatter: function (value) {
            return value.toFixed(data.accuracy >= 0  ? data.accuracy : 4); // 2表示小数为2位
          },
        },
        splitNumber: 6,
        min: minValue,
        max: maxValue,
        interval: (maxValue - minValue) / 6, // 标轴分割间隔
      })
      option && myChart.setOption(option);
      window.onresize = function () {
        myChart.resize();
      };
    },
    //绘制空表 提示信息
    setEmptyChart(msg) {
      let myChart = echarts.getInstanceByDom(
          document.getElementById("dailyVariationChart")
      );
      if (myChart !== undefined) {
        myChart.dispose();
      }
      myChart = echarts.init(document.getElementById("dailyVariationChart"));
      let option = {
        title: {
          text: msg,
          left:'center',
        },
      };
      option && myChart.setOption(option);
      //自适应大小
      window.onresize = function () {
        myChart.resize();
      };
    },
    // 下面是一系列下拉框的change方法
    async changeMonitorType(e) {
      this.form.monitorType = e.content
      this.isZD = (
          e.content === "振动" ||
          e.content === "地震" ||
          e.content === "索力"
      )
      this.monitorContentList.splice(0)
      this.monitorLocationList.splice(0)
      this.sensorInfoList.splice(0)
      this.specificMonitorTypeList.splice(0)
      this.form.monitorContent = '';
      this.form.monitorLocation = '';
      this.form.sensorId = '';
      this.form.specificMonitorType = '';
      const monitorContentList = e.children;
      for (const monitorContent of monitorContentList) {
        this.monitorContentList.push(monitorContent)
      }
    },

    async changeMonitorContent(e) {
      this.form.monitorContent = e.content
      this.monitorLocationList.splice(0)
      this.sensorInfoList.splice(0)
      this.specificMonitorTypeList.splice(0)
      this.form.monitorLocation = '';
      this.form.sensorId = '';
      this.form.specificMonitorType = '';
      const monitorLocationList = e.children;
      for (const monitorLocation of monitorLocationList) {
        this.monitorLocationList.push(monitorLocation)
      }
    },

    async changeMonitorLocation(e) {
      this.form.monitorLocation = e.content
      this.sensorInfoList.splice(0)
      this.specificMonitorTypeList.splice(0)
      this.form.sensorId = '';
      this.form.specificMonitorType = '';
      const ref = await getSensorByDataCode({ dataCode: e.code })
      const sensorInfoList = ref.data
      for (const sensorInfo of sensorInfoList) {
        this.sensorInfoList.push({
          code: sensorInfo.code,
          sensorId: sensorInfo.sensorId,
          sensorInstallCode: sensorInfo.dataCode,
          specificMonitorTypeId: sensorInfo.children.map(item => item.sensorCode),
          specificMonitorTypeName: sensorInfo.children.map(item => item.typeName),
        })
      }
    },

    async changeSensor(e) {
      this.specificMonitorTypeList.splice(0)
      this.form.specificMonitorType = ''
      this.form.sensorId = e.sensorInstallCode
      this.sensorInfo = e
      e.specificMonitorTypeId.forEach((value, index)=>{
        this.specificMonitorTypeList.push({
          specificMonitorTypeName: e.specificMonitorTypeName[index],
          specificMonitorTypeId: e.specificMonitorTypeId[index],
        })
      })
    },
    // 时间范围下拉框的change事件
    statusDateChange(e){
      this.form.timerange = e;
    },

    // 清空表单
    resetThisForm(){
      this.monitorContentList.splice(0)
      this.monitorLocationList.splice(0)
      this.sensorInfoList.splice(0)
      this.specificMonitorTypeList.splice(0)
      this.form.monitorContent = '';
      this.form.monitorLocation = '';
      this.form.sensorId = '';
      this.form.specificMonitorType = '';
      this.form.monitorType = '';
    }
  }
}

</script>

<style scoped>
.el-header{
  height: auto !important;
  padding: 0;
  color: #333;
  border-bottom: 1px solid #eee;
}
.el-form-item {
  display: inline-block;
  height: 40px;
  margin: 5px 0 5px 5px;
}
.el-form {
  text-align: left;
}
.el-button {
  margin-left: 5px;
}
/deep/.ivu-input {
  font-size: 14px !important;
}
/deep/.el-input__inner {
  padding: 0 0 0 10px !important;
}
.el-main{
  padding: 0;
  text-align: center;
}
.data1 {
  width: 100%;
  font-family: "Microsoft YaHei";
  display: flex;
  justify-content: left;
}
.data2 {
  width: 100%;
  font-family: "Microsoft YaHei";
  display: flex;
  justify-content: left;
  border-bottom: 1px solid #eee;
}
</style>
