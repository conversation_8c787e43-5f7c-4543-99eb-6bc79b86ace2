<template>
  <div class="app-container maindiv">
    <el-row :gutter="20" style="width: 100%;height: 100%;">
      <!--部门数据-->
      <el-col :span="relaNav ? 3 : 0" :xs="24" class="leftDiv">
        <!--折叠图标-->
        <div class="leftIcon" @click="relaNav = false">
          <span class="el-icon-caret-left"></span>
        </div>
        <left-tree @rowClick="leftTreeQuery"></left-tree>
      </el-col>
      <!--角色数据-->
      <el-col :span="relaNav ? 21 : 24" :xs="24" style="height: 100%">
        <!--展开图标-->
        <div class="rightIcon" @click="relaNav = true" v-show="!relaNav">
          <span class="el-icon-caret-right"></span>
        </div>

        <!-- 上表格 -->
        <!-- 计划申报上 -->
        <template>
          <PlanDeclarationUp ref="planUp" v-if="!this.isAdjust"/>
          <planAdjustUp ref="planUp" v-if="this.isAdjust"/>
        </template>
      </el-col>
    </el-row>

  </div>
</template>

<script>
import LeftTree from "./leftTree.vue";
import RightTree from "./rightTree.vue";
import PlanDeclarationUp from "./planDeclaration/planDeclarationUp.vue";
import planAdjustUp from "./planDeclaration/planAdjustUp.vue";
import PlanDeclarationDown from "./planDeclaration/planDeclarationDown.vue";
export default {
  name: 'AdvanceView',
  components: {
    LeftTree,
    RightTree,
    PlanDeclarationUp, // 计划申报上
    PlanDeclarationDown, // 计划申报下
    planAdjustUp,
    RenderDom: {
      functional: true,
      props: {
        row: Object,
        index: Number,
        render: Function
      },
      render(createElement, ctx) {
        const {row, index} = ctx.props
        return ctx.props.render(row, index)
      }
    }
  },
  props: [],
  data() {
    return {
      isAdjust: '1', // 共用页面的传参
      // 左侧组织树
      relaNav: true,
      relaNavRight: true,
    }
  },
  computed: {},
  watch: {},
  created() {
    this.isAdjust = this.$route.query.isAdjust
  },
  mounted() {
  },
  methods: {
    leftTreeQuery(data) {
      this.$refs.planUp.queryDetailTree(data)
    },
    handleQuery() {
      // this.loading = true
    },
    resetQuery() {
      this.queryParams = {
        pageNum: 1,
        pageSize: 50
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.leftDiv {
  border-right: 1px solid #d8dce5;
  height:  100%;
  padding-top: 10px;
  background-color: white;
  position: relative;
}

.RightDiv {
  border-right: 1px solid #d8dce5;
  height: 36rem;
  position: relative;
  padding-top: 10px;
  background-color: white;
  margin-left: 10px;
}

.leftIcon {
  border: 1px solid #dcdfe6;
  border-radius: 8px;
  width: 16px;
  height: 50px;
  line-height: 50px;
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
  position: absolute;
  right: 0;
  top: 300px;
  z-index: 2;
}

.leftIcon:hover {
  background-color: #dcdfe6;
}

.rightIcon {
  border: 1px solid #dcdfe6;
  border-radius: 8px;
  width: 16px;
  height: 50px;
  line-height: 50px;
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
  position: absolute;
  left: -10px;
  top: 280px;
  z-index: 10;
  background: white;
}

.rightIcon:hover {
  background-color: #dcdfe6;
}
</style>
<style lang="scss" scoped>
@import "@/assets/styles/business.scss";
</style>
