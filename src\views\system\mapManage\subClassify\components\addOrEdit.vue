<template>
  <el-form ref="form" :model="ruleForm" :rules="rules" label-width="110px">
    <el-row :gutter="10" style="display: flex; flex-wrap: wrap;">
      <el-col :span="12" :offset="0">
        <el-form-item label="父级" prop="parentId">
          <el-cascader :options="menuSubClassifyTree"
            :props="{ checkStrictly: true, label: 'name', value: 'id', children: 'child', leaf: true }" clearable
            style="width: 100%;" @change="handleCascaderChange" />
        </el-form-item>
      </el-col>
      <el-col :span="12" :offset="0">
        <el-form-item label="分类名称" prop="name">
          <el-input v-model="ruleForm.name" placeholder="请输入分类名称" />
        </el-form-item>
      </el-col>
      <el-col :span="12" :offset="0">
        <el-form-item label="分类统计单位" prop="classifyStatUnit">
          <el-input v-model="ruleForm.statUnit" placeholder="请输入分类统计单位" />
        </el-form-item>
      </el-col>
      <el-col :span="12" :offset="0">
        <el-form-item label="分类统计类型" prop="classifyStatType">
          <el-select v-model="ruleForm.statType" placeholder="请选择" clearable style="width: 100%;">
            <el-option label="count" :value="1" />
            <el-option label="sum" :value="2" />
          </el-select>
        </el-form-item>
      </el-col>

<!--      <el-col :span="12" :offset="0">-->
<!--        <el-form-item label="所在数据表" prop="classifyTableName">-->
<!--          <el-select v-model="ruleForm.classifyTableName" placeholder="请选择" clearable filterable style="width: 100%;"-->
<!--            @change="handleTableChange">-->
<!--            <el-option :label="item.tableComment + ' &#45;&#45;&#45;&#45; ' + item.tableName" :value="item.tableName"-->
<!--              v-for="item in tableList" :key="item.tableName" />-->
<!--          </el-select>-->
<!--        </el-form-item>-->
<!--      </el-col>-->
<!--      <el-col :span="12" :offset="0">-->
<!--        <el-form-item label="所在数据表字段" prop="classifyTableColumn">-->
<!--          <el-select v-model="ruleForm.classifyTableColumn" placeholder="请选择" clearable filterable style="width: 100%;"-->
<!--            @disabled="!ruleForm.classifyTableName ? true : false">-->
<!--            <el-option :label="item.columnComment + ' &#45;&#45;&#45;&#45; ' + item.columnName" :value="item.columnName"-->
<!--              v-for="item in columnList" :key="item.columnName" />-->
<!--          </el-select>-->
<!--        </el-form-item>-->
<!--      </el-col>-->

      <el-col :span="12" :offset="0">
        <el-form-item label="一张图显示" prop="oneMapShow">
          <el-select v-model="ruleForm.oneMapShow" placeholder="请选择" clearable style="width: 100%;">
            <el-option label="显示" :value="1" />
            <el-option label="不显示" :value="0" />
          </el-select>
        </el-form-item>
      </el-col>
      <el-col :span="12" :offset="0">
        <el-form-item label="图层显示层级" prop="classifyShowLevel">
          <el-input-number v-model="ruleForm.showLevel" :step="1" :min="1" style="width: 100%;" />
        </el-form-item>
      </el-col>
      <el-col :span="12" :offset="0">
        <el-form-item label="排序" prop="showIndex">
          <el-input-number v-model="ruleForm.showIndex" :step="1" :min="0" style="width: 100%;" />
        </el-form-item>
      </el-col>
      <el-col :span="12" :offset="0">
        <el-form-item label="数据库表字段" prop="tableColumnInfo">
          <el-input v-model="ruleForm.tableColumnInfo" placeholder="请输入数据库表字段" />
        </el-form-item>
      </el-col>
      <el-col :span="12" :offset="0">
        <el-form-item label="查询数据源" prop="datasourceName">
          <el-input v-model="ruleForm.datasourceName" placeholder="请输入查询数据源" />
        </el-form-item>
      </el-col>
      <el-col :span="12" :offset="0">
        <el-form-item label="目录展示类型" prop="menuShowType">
          <el-select v-model="ruleForm.menuShowType" placeholder="请选择" clearable style="width: 100%;">
            <el-option label="都显示" :value="0" />
            <el-option label="树形显示" :value="1" />
            <el-option label="数据总览" :value="2" />
          </el-select>
        </el-form-item>
      </el-col>
      <el-col :span="12" :offset="0">
        <el-form-item label="分类统计sql" prop="statSql">
          <el-input v-model="ruleForm.statSql" type="textarea" :rows="2" placeholder="请输入图层描述信息" />
        </el-form-item>
      </el-col>
      <el-col :span="12" :offset="0">
        <el-form-item label="图层描述信息" prop="description">
          <el-input v-model="ruleForm.description" type="textarea" :rows="2" placeholder="请输入图层描述信息" />
        </el-form-item>
      </el-col>
      <el-col :span="24" :offset="0">
        <el-form-item label="其他需要配置" prop="otherConfig">
          <vue-json-editor
              v-model="ruleForm.otherConfig"
              :mode="'code'"
          />
          <!-- <el-input v-model="ruleForm.otherConfig" type="textarea" :rows="2" placeholder="请输入图层描述信息" /> -->
        </el-form-item>
      </el-col>
      <el-col :span="12" :offset="0">
        <el-form-item label="图标" prop="iconId">
          <ImageUpload v-model="ruleForm.iconId" :limit="1" :owner-id="enterOwnerId" />
        </el-form-item>
      </el-col>
      <el-col :span="24" :offset="0">
        <el-form-item label="子类图标" v-if="subIconList && subIconList.length">
          <div v-for="(item,index) in subIconList" :key="'sub'+index" class="mb20">
            <el-input v-model="item.name" placeholder="请输入图标名称" class="mb10"/>
            <ImageUpload v-model="item.iconId" :limit="1" :owner-id="item.iconId" />
            <el-button type="danger" @click="onSubRemove(index)">删除</el-button>
          </div>
        </el-form-item>
        <el-button type="primary" @click="onSubAdd" style="margin-left:100px;">新增图标</el-button>
      </el-col>
    </el-row>

    <el-form-item style="margin: auto;display: flex;justify-content: center;">
      <el-button type="primary" @click="onSubmit" v-loading="loading" v-if="!readonly">确定</el-button>
      <el-button @click="onCancel">取消</el-button>
    </el-form-item>
  </el-form>
</template>

<script>
import { addMenuSubClassify, updateMenuSubClassify, getMenuSubClassifyTree } from '@/api/oneMap/subClassify'
import { createIdWorker } from '@/api/baseData/common'
import { getTableList, getTableColumnList } from '@/api/oneMap/tableInfo'
import vueJsonEditor from 'vue-json-editor'
import CryptoJS from 'crypto-js';

export default {
  props: {
    form: {
      type: Object,
      default: {},
    },
    id: {
      type: [String, Number],
      default: ''
    },
    readonly: {
      type: Boolean,
      default: false,
    },
  },
  components: { vueJsonEditor },
  data() {
    return {
      ruleForm: {
        oneMapShow: 1,
      },
      rules: {
        name: [
          { required: true, message: '请输入分类名称', trigger: 'blur' },
        ],
        menuType: [
          { required: true, message: '请选择目录类型', trigger: ['change', 'blur'] },
        ],
      },
      loading: false,
      menuSubClassifyList: [],
      layerMenuList: [],
      enterOwnerId: '', // 图标Id
      menuSubClassifyTree: [],
      tableList: [], // 数据库表
      columnList: [],// 数据库表字段
      subIconList: [], // 子分类图标
    }
  },
  created() {
    this.ruleForm = { ...this.form,classifyShowLevel:8 }
    if (this.ruleForm.otherConfig) {
      this.ruleForm.otherConfig = JSON.parse(this.ruleForm.otherConfig)
    }
    console.log(this.ruleForm)
    // 2025-3-7 ↓
    if (this.ruleForm.iconId) {
      if(typeof this.ruleForm.iconId === 'string' && this.ruleForm.iconId.indexOf('[') === -1) {
        this.ruleForm.iconId = [this.ruleForm.iconId]
      }else {
        let iconStr = this.ruleForm.iconId
        iconStr = iconStr.replace(/\s+/g, '')
        let arr = JSON.parse(iconStr) 
        this.ruleForm.iconId = arr.filter(item => item.name === 'default').map(item => item.iconId)
        this.subIconList = arr.filter(item => item.name !== 'default');
      }
    }
    // 2025-3-7 ↑
    this.getList();
    this.getTableList();
    this.getColumnList();
    createIdWorker().then(res => {
      if (res.code === 200) this.enterOwnerId = res.data
    })
  },
  methods: {
    // 获取所有列表数据
    getList() {
      getMenuSubClassifyTree({layerMenuSubId:this.form.layerMenuSubId}).then(res => {
        if (res) {
          this.menuSubClassifyTree = res || []
        }
      });
    },
    // 获取数据库表
    getTableList() {
      getTableList({ pageNum: 1, pageSize: 200 }).then(res => {
        if (res.code === 200) {
          this.tableList = res.rows || [];
        }
      })
    },
    // 获取数据表字段
    getColumnList() {
      getTableColumnList({ pageNum: 1, pageSize: 200, tableName: this.ruleForm.classifyTableName }).then(res => {
        if (res.code === 200) {
          this.columnList = res.rows || [];
        }
      })
    },
    // 数据库表下拉选择变化监听
    handleTableChange(e) {
      this.ruleForm.classifyTableName = e
      this.getColumnList();
    },
    // 提交
    onSubmit() {
      this.$refs.form.validate(vali => {
        if (!vali) return
        if (this.ruleForm.statSql) {
          this.ruleForm.statSql = this.encryptData(this.ruleForm.statSql);
        }
        if (this.ruleForm.otherConfig) {
          this.ruleForm.otherConfig = JSON.stringify(this.ruleForm.otherConfig)
          this.ruleForm.otherConfig = this.encryptData(this.ruleForm.otherConfig)
        }
        this.ruleForm.iconId = this.ruleForm.iconId && this.ruleForm.iconId.length ? this.ruleForm.iconId[0] : ''
        if(this.subIconList.length) {
          let arr = [];
          arr.push({
            name: 'default',
            iconId: this.ruleForm.iconId && this.ruleForm.iconId.length ? this.ruleForm.iconId[0] : '',
          })
          arr = arr.concat(this.subIconList)
          this.ruleForm.iconId = JSON.stringify(arr);
        }
        // 编辑或新增数据
        let request = this.ruleForm.id ? updateMenuSubClassify(this.ruleForm) : addMenuSubClassify(this.ruleForm)
        let msg = this.ruleForm.id ? '编辑成功' : '新增成功';
        this.loading = true;
        request.then(res => {
          if (res.code == 200) {
            this.$modal.msgSuccess(msg);
            this.$emit('refresh')
            this.onCancel();
          }
        }).finally(() => {
          this.loading = false;
        })
      })
    },
    // 取消关闭
    onCancel() {
      this.$emit('close', false)
    },
    // 级联选择器监听变化
    handleCascaderChange(e) {
      if (e) {
        this.ruleForm.parentId = e[e.length - 1]
      }
    },
    // 子类图标添加
    onSubAdd() {
      this.subIconList.push({
        name: '',
        iconId: '',
      })
    },
    // 子类图标删除
    onSubRemove(index) {
      this.subIconList.splice(index, 1)
    },
    //Base64解密
    decodeBase64UsingTextDecoder(text) {
      const binaryString = window.atob(text);
      const bytes = new Uint8Array(binaryString.length);
      for (let i = 0; i < binaryString.length; i++) {
        bytes[i] = binaryString.charCodeAt(i);
      }
      const decoder = new TextDecoder();
      return decoder.decode(bytes);
    },
    // 加密函数
    encryptData(plainText) {
      let str = 'OWUzMjEwYzlhZTM0MWRlMWFiMTZjY2UwZjFhN2I5MDk='
      let key = CryptoJS.enc.Utf8.parse(this.decodeBase64UsingTextDecoder(str));
      return CryptoJS.AES.encrypt(plainText, key,{mode: CryptoJS.mode.ECB, padding: CryptoJS.pad.Pkcs7}).toString();
      // const encrypted = CryptoJS.AES.encrypt(plainText, key);
      // return encrypted.toString();
    }
  },
};
</script>

<style lang="scss" scoped></style>
