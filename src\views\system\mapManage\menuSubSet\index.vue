<template>
  <PageContainer>
    <template slot="search">
      <el-row :gutter="20">
        <el-col :span="3" :offset="0">
          <el-input v-model="queryParams.attributeTypeName" placeholder="请输入属性类型名称" clearable size="mini"
            @keyup.enter.native="handleQuery" />
        </el-col>
        <el-col :span="6" :offset="0">
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">
            搜索
          </el-button>
          <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">
            重置
          </el-button>
        </el-col>
      </el-row>
    </template>
    <template slot="header">
      <el-row :gutter="20">
        <el-col :span="1.5" :offset="0" class="mb8">
          <el-button v-hasPermi="['baseData:culvert:add']" type="primary" size="mini" @click="handleAdd">
            新增
          </el-button>
        </el-col>
        <el-col :span="1.5" :offset="0" class="mb8">
          <el-button v-hasPermi="['baseData:culvert:edit']" :disabled="!(tableSelects.length === 1)" type="primary"
            size="mini" @click="handleUpdate">
            编辑
          </el-button>
        </el-col>
        <el-col :span="1.5" :offset="0" class="mb8">
          <el-button v-hasPermi="['baseData:culvert:delete']" :disabled="!(tableSelects.length > 0)" type="primary"
            size="mini" @click="handleDelete">
            删除
          </el-button>
        </el-col>
        <el-col :span="1.5" :offset="0" class="mb8">
          <el-button v-hasPermi="['baseData:culvert:getInfoById']" :disabled="!(tableSelects.length === 1)" type="primary"
            size="mini" @click="handleDetail">
            查看
          </el-button>
        </el-col>
      </el-row>
    </template>
    <template slot="body">
      <el-table v-loading="loading" :data="menuSubSetList" @selection-change="handleSelectionChange"
        @row-click="handleRowClick" border height="100%" :row-key="(row) => row.id" ref="tableRef">
        <el-table-column type="selection" width="55" align="center" fixed="left" :reserve-selection="true" />
        <el-table-column label="属性类型名称" min-width="110" align="center" prop="attributeTypeName" />
        <el-table-column label="属性类型代码" min-width="110" align="center" prop="attributeTypeCode">
          <template slot-scope="{row}">
            {{ row.attributeTypeCode }}
          </template>
        </el-table-column>
        <el-table-column label="填充颜色" align="center" prop="interiorColour">
          <template slot-scope="{row}">
            {{ row.interiorColour }}
          </template>
        </el-table-column>
        <el-table-column label="边框粗细" align="center" prop="borderWidth">
          <template slot-scope="{row}">
            {{ row.borderWidth }}
          </template>
        </el-table-column>
        <el-table-column label="边框颜色" align="center" prop="borderColour">
          <template slot-scope="{row}">
            {{ row.borderColour }}
          </template>
        </el-table-column>
        <el-table-column label="图标" align="center" prop="iconId">
          <template slot-scope="{row}">
            <ImagePreview :owner-id="row.iconId" />
          </template>
        </el-table-column>
      </el-table>

      <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize"
        @pagination="getList" />
    </template>

    <!-- 新增编辑查看 -->
    <Dialog :title="title" :show.sync="open">
      <AddOrEdit :form="form" @close="handleClose" @refresh="getList" :readonly="readonly" />
    </Dialog>
  </PageContainer>
</template>
 
<script>
import { getSubSetList, getMenuSubSet, delMenuSubSet } from "@/api/oneMap/menuSubSet";
import AddOrEdit from "./components/addOrEdit.vue";
import Dialog from "@/components/Dialog/index.vue";

export default {
  name: "MenuSubSet",
  components: {
    AddOrEdit,
    Dialog,
  },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 图层个性化设置表格数据
      menuSubSetList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        layerMenuSubId: null,
        attributeTypeName: null,
        attributeTypeCode: null,
        interiorColour: null,
        borderWidth: null,
        borderColour: null,
        iconId: null
      },
      // 表单参数
      form: {},
      tableSelects: [],
      readonly: false,
    };
  },
  created() {
    // 获取图层id
    this.queryParams.layerMenuSubId = this.$route.query.id
    this.getList();
  },
  methods: {
    /** 查询图层个性化设置列表 */
    getList() {
      this.loading = true;
      getSubSetList(this.queryParams).then(response => {
        this.menuSubSetList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        layerMenuSubId: null,
        attributeTypeName: null,
        attributeTypeCode: null,
        interiorColour: null,
        borderWidth: null,
        borderColour: null,
        iconId: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.tableSelects = selection || [];
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.form.layerMenuSubId = this.$route.query.id
      this.open = true;
      this.title = "添加图层个性化设置";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = this.tableSelects[0].id
      getMenuSubSet(id).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改图层个性化设置";
      });
    },
    handleDetail() {
      this.reset();
      const id = this.tableSelects[0].id
      this.readonly = true;
      getMenuSubSet(id).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "查看图层个性化设置";
      });
    },
    handleClose(e) {
      this.open = e;
      this.readonly = false;
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = this.tableSelects.map(v => v.id)
      this.$modal.confirm('是否确认删除图层个性化设置的数据项？').then(function () {
        return delMenuSubSet(ids);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => { });
    },
    handleRowClick(row) {
      let arr = this.tableSelects.filter((v) => v.id == row.id);
      // 点击行 选中复选框
      if (arr && arr.length > 0) {
        this.$refs.tableRef.toggleRowSelection(row, false);
        this.tableSelects = this.tableSelects.filter((v) => v.id != row.id);
      } else {
        this.tableSelects = [...this.tableSelects, ...[row]];
        this.$refs.tableRef.toggleRowSelection(row, true);
      }
    },
  }
};
</script>
 
<style lang="scss" scoped></style>