<template>
  <div class="road-interflow-edit">
    <el-tabs v-model="tab" @tab-click="tabClick">
      <el-tab-pane name="1" label="中间计量清单">
        <el-row :gutter="15">
          <el-col :span="24">
            <el-form
              ref="queryForm"
              :inline="true"
              :model="queryParams"
              label-width="68px"
              size="mini"
            >
              <el-form-item>
                <el-input
                  v-model="queryParams.intermediateName"
                  placeholder="中间计量单名称"
                  style="width: 190px"
                ></el-input>
              </el-form-item>
              <el-form-item>
                <el-input
                  v-model="queryParams.intermediateCode"
                  placeholder="中间计量单编码"
                  style="width: 190px"
                ></el-input>
              </el-form-item>
              <el-form-item>
                <el-button
                  icon="el-icon-search"
                  size="mini"
                  type="primary"
                  @click="handleQuery1"
                  >搜索</el-button
                >
                <el-button
                  icon="el-icon-refresh"
                  size="mini"
                  @click="resetQuery"
                  >重置</el-button
                >
              </el-form-item>
            </el-form>
            <el-row :gutter="10" class="mb8">
              <el-col :span="1.5">
                <el-button
                  icon="el-icon-download"
                  size="mini"
                  type="primary"
                  @click="exportList1"
                  >导出清单
                </el-button>
              </el-col>
            </el-row>
          </el-col>
          <el-col :span="24">
            <div class="draggable">
              <el-table v-adjust-table
                ref="dataTable"
                v-loading="loading"
                :data="tableData1"
                border
                height="300"
                highlight-current-row
                row-key="id"
                size="mini"
                stripe
                style="width: 100%"
              >
                <el-table-column
                  align="center"
                  label="序号"
                  type="index"
                  width="50"
                />
                <template v-for="(column, index) in columns1">
                  <el-table-column
                    v-if="column.visible"
                    :label="column.label"
                    :prop="column.field"
                    align="center"
                  >
                    <template slot-scope="scope">
                      <dict-tag
                        v-if="column.dict"
                        :options="dict.type[column.dict]"
                        :value="scope.row[column.field]"
                      />
                      <template v-else-if="column.slots">
                        <RenderDom
                          :index="index"
                          :render="column.render"
                          :row="scope.row"
                        />
                      </template>
                      <span v-else-if="column.isTime">{{
                        parseTime(scope.row[column.field], "{y}-{m}-{d}")
                      }}</span>
                      <span v-else>{{ scope.row[column.field] }}</span>
                    </template>
                  </el-table-column>
                </template>
              </el-table>
              <pagination
                v-show="total1 > 0"
                :limit.sync="queryParams.pageSize"
                :page.sync="queryParams.pageNum"
                :total="total1"
                @pagination="handleQuery1"
              />
            </div>
          </el-col>
        </el-row>
      </el-tab-pane>
      <el-tab-pane name="2" label="任务清单">
        <el-row>
          <el-row>
            <el-col :span="24">
              <el-form
                ref="queryForm"
                :inline="true"
                :model="queryParams"
                label-width="68px"
                size="mini"
              >
                <el-form-item>
                  <el-input
                    style="width: 190px"
                    placeholder="任务单名称"
                    v-model="queryParams.constructionName"
                  ></el-input>
                </el-form-item>
                <el-form-item>
                  <el-input
                    style="width: 190px"
                    placeholder="任务单编码"
                    v-model="queryParams.constructionCode"
                  ></el-input>
                </el-form-item>
                <el-form-item>
                  <el-button
                    icon="el-icon-search"
                    size="mini"
                    type="primary"
                    @click="handleQuery2"
                    >搜索</el-button
                  >
                  <el-button
                    icon="el-icon-refresh"
                    size="mini"
                    @click="resetQuery"
                    >重置</el-button
                  >
                </el-form-item>
              </el-form>
            </el-col>
          </el-row>
          <el-row :gutter="10" class="mb8">
            <el-col :span="1.5">
              <el-button icon="el-icon-view" size="mini" type="primary"
                >操作记录
              </el-button>
            </el-col>
            <el-col :span="1.5">
              <el-button
                icon="el-icon-download"
                size="mini"
                type="warning"
                @click="exportList2"
                >导出清单
              </el-button>
            </el-col>
          </el-row>
          <el-col :span="24">
            <div class="draggable">
              <el-table v-adjust-table
                ref="dataTable"
                v-loading="loading"
                :data="tableData2"
                border
                height="300"
                highlight-current-row
                row-key="detailId"
                size="mini"
                stripe
                style="width: 100%"
                @selection-change="handleSelectionChange"
                @expand-change="loadData"
              >
                <el-table-column type="index" label="序号"></el-table-column>
                <el-table-column type="expand" label="报价清单" width="100">
                  <template slot-scope="props">
                    <el-table v-adjust-table
                      :data="props.row.methodList"
                      style="width: 100%"
                      v-loading="methodLoading"
                    >
                      <el-table-column prop="" align="center" label="">
                      </el-table-column>
                      <el-table-column
                        prop="schemeCode"
                        align="center"
                        label="子目号"
                      >
                      </el-table-column>
                      <el-table-column
                        prop="schemeName"
                        align="center"
                        label="养护方法"
                      >
                      </el-table-column>
                      <el-table-column
                        prop="calcDesc"
                        align="center"
                        label="计算式"
                      >
                      </el-table-column>
                      <el-table-column
                        prop="num"
                        align="center"
                        label="方法数量"
                      >
                      </el-table-column>
                      <el-table-column
                        prop="unit"
                        align="center"
                        label="方法单位"
                      >
                      </el-table-column>
                      <el-table-column prop="price" align="center" label="单价">
                      </el-table-column>
                      <el-table-column
                        prop="amount"
                        align="center"
                        label="金额"
                      >
                      </el-table-column>
                    </el-table>
                    <pagination
                      v-show="props.row.childTotal > 0"
                      :limit.sync="props.row.queryParams.pageSize"
                      :page.sync="props.row.queryParams.pageNum"
                      :total="props.row.childTotal"
                      @pagination="loadData(props.row)"
                    />
                  </template>
                </el-table-column>
                <template v-for="(column, index) in columns2">
                  <el-table-column
                    v-if="column.visible"
                    :label="column.label"
                    :prop="column.field"
                    :width="column.width"
                    align="center"
                  >
                    <template slot-scope="scope">
                      <dict-tag
                        v-if="column.dict"
                        :options="dict.type[column.dict]"
                        :value="scope.row[column.field]"
                      />
                      <template v-else-if="column.slots">
                        <RenderDom
                          :index="index"
                          :render="column.render"
                          :row="scope.row"
                        />
                      </template>
                      <span v-else-if="column.isTime">{{
                        parseTime(scope.row[column.field], "{y}-{m}-{d}")
                      }}</span>
                      <span v-else>{{ scope.row[column.field] }}</span>
                    </template>
                  </el-table-column>
                </template>
              </el-table>
              <pagination
                v-show="total2 > 0"
                :limit.sync="queryParams.pageSize"
                :page.sync="queryParams.pageNum"
                :total="total2"
                @pagination="handleQuery2"
              />
            </div>
          </el-col>
        </el-row>
      </el-tab-pane>
      <el-tab-pane name="3" label="报价清单">
        <el-row :gutter="15">
          <el-col :span="24">
            <el-row :gutter="10" class="mb8">
              <el-col :span="1.5">
                <el-button
                  icon="el-icon-download"
                  size="mini"
                  type="primary"
                  @click="exportList3"
                  >导出清单
                </el-button>
              </el-col>
            </el-row>
          </el-col>
          <el-col :span="24">
            <div class="draggable">
              <el-table v-adjust-table
                ref="dataTable"
                v-loading="loading"
                :data="tableData3"
                border
                height="300"
                highlight-current-row
                row-key="id"
                size="mini"
                stripe
                style="width: 100%"
              >
                <el-table-column
                  align="center"
                  label="序号"
                  type="index"
                  width="50"
                />
                <template v-for="(column, index) in columns3">
                  <el-table-column
                    v-if="column.visible"
                    :label="column.label"
                    :prop="column.field"
                    align="center"
                  >
                    <template slot-scope="scope">
                      <dict-tag
                        v-if="column.dict"
                        :options="dict.type[column.dict]"
                        :value="scope.row[column.field]"
                      />
                      <template v-else-if="column.slots">
                        <RenderDom
                          :index="index"
                          :render="column.render"
                          :row="scope.row"
                        />
                      </template>
                      <span v-else-if="column.isTime">{{
                        parseTime(scope.row[column.field], "{y}-{m}-{d}")
                      }}</span>
                      <span v-else>{{ scope.row[column.field] }}</span>
                    </template>
                  </el-table-column>
                </template>
              </el-table>
              <pagination
                v-show="total3 > 0"
                :limit.sync="queryParams.pageSize"
                :page.sync="queryParams.pageNum"
                :total="total3"
                @pagination="handleQuery3"
              />
            </div>
          </el-col>
        </el-row>
      </el-tab-pane>
      <el-tab-pane name="4" label="扣款清单">
        <el-row :gutter="15">
          <el-col :span="24">
            <el-row :gutter="10" class="mb8">
              <el-col :span="1.5">
                <el-button
                  icon="el-icon-download"
                  size="mini"
                  type="primary"
                  @click="exportList4"
                  >导出清单
                </el-button>
              </el-col>
            </el-row>
          </el-col>
          <el-col :span="24">
            <div class="draggable">
              <el-table v-adjust-table
                ref="dataTable"
                v-loading="loading"
                :data="tableData4"
                border
                height="300"
                highlight-current-row
                row-key="id"
                size="mini"
                stripe
                style="width: 100%"
              >
                <el-table-column
                  align="center"
                  label="序号"
                  type="index"
                  width="50"
                />
                <template v-for="(column, index) in columns4">
                  <el-table-column
                    v-if="column.visible"
                    :label="column.label"
                    :prop="column.field"
                    align="center"
                  >
                    <template slot-scope="scope">
                      <dict-tag
                        v-if="column.dict"
                        :options="dict.type[column.dict]"
                        :value="scope.row[column.field]"
                      />
                      <template v-else-if="column.slots">
                        <RenderDom
                          :index="index"
                          :render="column.render"
                          :row="scope.row"
                        />
                      </template>
                      <span v-else-if="column.isTime">{{
                        parseTime(scope.row[column.field], "{y}-{m}-{d}")
                      }}</span>
                      <span v-else>{{ scope.row[column.field] }}</span>
                    </template>
                  </el-table-column>
                </template>
              </el-table>
              <pagination
                v-show="total4 > 0"
                :limit.sync="queryParams.pageSize"
                :page.sync="queryParams.pageNum"
                :total="total4"
                @pagination="handleQuery4"
              />
            </div>
          </el-col>
        </el-row>
      </el-tab-pane>
      <el-tab-pane name="5" label="计量调整">
        <el-row :gutter="15">
          <el-col :span="24">
            <el-form
              ref="queryForm"
              :inline="true"
              :model="queryParams"
              label-width="68px"
              size="mini"
            >
              <el-form-item>
                <cost-select :type="6"
                  v-model="queryParams.dataType"
                  placeholder="费用类型"
                  style="width: 190px"
                ></cost-select>
              </el-form-item>
              <el-form-item>
                <el-input
                  v-model="queryParams.schemeCode"
                  placeholder="子目号"
                  style="width: 190px"
                ></el-input>
              </el-form-item>
              <el-form-item>
                <el-input
                  v-model="queryParams.schemeName"
                  placeholder="子目名称"
                  style="width: 190px"
                ></el-input>
              </el-form-item>
              <el-form-item>
                <el-button
                  icon="el-icon-search"
                  size="mini"
                  type="primary"
                  @click="handleQuery5"
                  >搜索</el-button
                >
                <el-button
                  icon="el-icon-refresh"
                  size="mini"
                  @click="resetQuery"
                  >重置</el-button
                >
              </el-form-item>
            </el-form>
          </el-col>
          <el-col :span="24">
            <div class="draggable">
              <el-table v-adjust-table
                ref="dataTable"
                v-loading="loading"
                :data="tableData5"
                border
                height="300"
                highlight-current-row
                row-key="id"
                size="mini"
                stripe
                style="width: 100%"
              >
                <el-table-column
                  align="center"
                  label="序号"
                  type="index"
                  width="50"
                />
                <template v-for="(column, index) in columns5">
                  <el-table-column
                    v-if="column.visible"
                    :fixed="column.fixed"
                    :label="column.label"
                    :prop="column.field"
                    :width="column.width"
                    align="center"
                  >
                    <template slot-scope="scope">
                      <dict-tag
                        v-if="column.dict"
                        :options="dict.type[column.dict]"
                        :value="scope.row[column.field]"
                      />
                      <template v-else-if="column.slots">
                        <RenderDom
                          :index="index"
                          :render="column.render"
                          :row="scope.row"
                        />
                      </template>
                      <span v-else-if="column.isTime">{{
                        parseTime(scope.row[column.field], "{y}-{m}-{d}")
                      }}</span>
                      <span v-else>{{ scope.row[column.field] }}</span>
                    </template>
                  </el-table-column>
                </template>
              </el-table>
              <pagination
                v-show="total5 > 0"
                :limit.sync="queryParams.pageSize"
                :page.sync="queryParams.pageNum"
                :total="total5"
                @pagination="handleQuery5"
              />
            </div>
          </el-col>
        </el-row>
      </el-tab-pane>
      <el-tab-pane name="6" label="材料调差">
        <el-row :gutter="15">
          <el-col :span="24">
            <el-form
              ref="queryForm"
              :inline="true"
              :model="queryParams"
              label-width="68px"
              size="mini"
            >
              <el-form-item>
                <cost-select :type="6"
                  v-model="queryParams.dataType"
                  placeholder="费用类型"
                  style="width: 190px"
                ></cost-select>
              </el-form-item>
              <el-form-item>
                <el-input
                  v-model="queryParams.schemeCode"
                  placeholder="子目号"
                  style="width: 190px"
                ></el-input>
              </el-form-item>
              <el-form-item>
                <el-input
                  v-model="queryParams.schemeName"
                  placeholder="子目名称"
                  style="width: 190px"
                ></el-input>
              </el-form-item>
              <el-form-item>
                <el-button
                  icon="el-icon-search"
                  size="mini"
                  type="primary"
                  @click="handleQuery6"
                  >搜索</el-button
                >
                <el-button
                  icon="el-icon-refresh"
                  size="mini"
                  @click="resetQuery"
                  >重置</el-button
                >
              </el-form-item>
            </el-form>
          </el-col>
          <el-col :span="24">
            <div class="draggable">
              <el-table v-adjust-table
                ref="dataTable"
                v-loading="loading"
                :data="tableData6"
                border
                height="300"
                highlight-current-row
                row-key="id"
                size="mini"
                stripe
                style="width: 100%"
              >
                <el-table-column
                  align="center"
                  label="序号"
                  type="index"
                  width="50"
                />
                <template v-for="(column, index) in columns6">
                  <el-table-column
                    v-if="column.visible"
                    :fixed="column.fixed"
                    :label="column.label"
                    :prop="column.field"
                    :width="column.width"
                    align="center"
                  >
                    <template slot-scope="scope">
                      <dict-tag
                        v-if="column.dict"
                        :options="dict.type[column.dict]"
                        :value="scope.row[column.field]"
                      />
                      <template v-else-if="column.slots">
                        <RenderDom
                          :index="index"
                          :render="column.render"
                          :row="scope.row"
                        />
                      </template>
                      <span v-else-if="column.isTime">{{
                        parseTime(scope.row[column.field], "{y}-{m}-{d}")
                      }}</span>
                      <span v-else>{{ scope.row[column.field] }}</span>
                    </template>
                  </el-table-column>
                </template>
              </el-table>
              <pagination
                v-show="total6 > 0"
                :limit.sync="queryParams.pageSize"
                :page.sync="queryParams.pageNum"
                :total="total6"
                @pagination="handleQuery6"
              />
            </div>
          </el-col>
        </el-row>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>
<script>
import {
  middleListBySid,
  listEvent,
  listScheme,
  fetchDeductionList,
  fetchFundAdjustList,
  fetchMaterialAdjustList,
} from "@/api/regularTesting/metering/settlementApplication";
import { listEventBySettleId } from "@/api/regularTesting/metering/middleApplication";
import RoadSection from "@/views/baseData/components/roadSection/index.vue";
import RouteCodeSection from "@/components/RouteCodeSection/index.vue";
import CostSelect from "@/components/CostSelect/index.vue";

export default {
  components: {
    CostSelect,
    RouteCodeSection,
    RoadSection,
    RenderDom: {
      functional: true,
      props: {
        row: Object,
        index: Number,
        render: Function,
      },
      render(createElement, ctx) {
        const { row, index } = ctx.props;
        return ctx.props.render(row, index);
      },
    },
  },
  dicts: [
    "route_direction",
    "lane",
    "sys_asset_type",
    "deduction_type",
    "adjustment_type",
    "test_project_type",
    "testing_intermediate_status",
  ],
  data() {
    return {
      queryParams: {
        pageNum: 1,
        pageSize: 10,
      },
      loading: false,
      tableData1: [],
      total1: 0,
      columns1: [
        {
          key: 0,
          width: 100,
          field: "name",
          label: `中间计量名称`,
          visible: true,
        },
        {
          key: 1,
          width: 100,
          field: "code",
          label: `中间计量编号`,
          visible: true,
        },
        {
          key: 2,
          width: 100,
          field: "domainName",
          label: `管养单位`,
          visible: true,
        },
        {
          key: 3,
          width: 100,
          field: "calcDomainName",
          label: `申请计量单位`,
          visible: true,
        },
        {
          key: 4,
          width: 100,
          field: "maiSecId",
          label: `路段名称`,
          visible: true,
        },
        {
          key: 5,
          width: 100,
          field: "conName",
          label: `合同名称`,
          visible: true,
        },
        {
          key: 6,
          width: 100,
          field: "calcFund",
          label: `计量资金(元)`,
          visible: true,
        },
        {
          key: 7,
          width: 100,
          field: "productionFund",
          label: `安全生产费(元)`,
          visible: true,
        },
        {
          key: 8,
          width: 100,
          field: "guaranteeFund",
          label: `安全保通费(元)`,
          visible: true,
        },
        {
          key: 9,
          width: 100,
          field: "supFund",
          label: `监理费(元)`,
          visible: true,
        },
        {
          key: 10,
          width: 100,
          field: "calcDate",
          label: `计量日期`,
          visible: true,
        },
        {
          key: 14,
          width: 100,
          field: "status",
          label: `状态`,
          visible: true,
          dict: "testing_intermediate_status",
        },
      ],
      tableData2: [],
      total2: 0,
      columns2: [
        {
          key: 1,
          field: "projectName",
          label: `项目名称`,
          visible: true,
        },
        {
          key: 2,
          field: "constructionName",
          label: `任务单名称`,
          visible: true,
        },
        {
          key: 3,
          field: "constructionCode",
          label: `任务单编号`,
          visible: true,
        },
        {
          key: 4,
          field: "maiSecName",
          label: `路段名称`,
          visible: true,
        },
        {
          key: 5,
          field: "domainName",
          label: `管养单位`,
          visible: true,
        },
        {
          key: 6,
          field: "checkDomainName",
          label: `检测单位`,
          visible: true,
        },
        {
          key: 7,
          field: "checkConName",
          label: `检测合同`,
          visible: true,
        },
        {
          key: 8,
          field: "endTime",
          label: `完工日期`,
          visible: true,
        },
        {
          key: 9,
          field: "sumFund",
          label: `金额`,
          visible: true,
        },
      ],
      tableData3: [],
      total3: 0,
      columns3: [
        { key: 0, field: "schemeCode", label: `子目号`, visible: true },
        { key: 1, field: "schemeName", label: `子目名称`, visible: true },
        { key: 2, field: "price", label: `单价`, visible: true },
        { key: 3, field: "calcDesc", label: `计算式`, visible: true },
        { key: 4, field: "num", label: `数量`, visible: true },
        { key: 5, field: "amount", label: `金额`, visible: true },
        { key: 5, field: "remark", label: `备注`, visible: true },
      ],
      tableData4: [],
      total4: 0,
      columns4: [
        {
          key: 1,
          width: 100,
          field: "mtype",
          label: `扣款类型`,
          visible: true,
          dict: "deduction_type",
        },
        { key: 2, width: 100, field: "fund", label: `扣款金额`, visible: true },
        {
          key: 3,
          width: 100,
          field: "isSupFund",
          label: `是否计算监理费`,
          visible: true,
          slots: true,
          render: (row, index) => {
            if (row.isSupFund == 0) {
              return <div>不计算</div>;
            } else {
              return <div>计算</div>;
            }
          },
        },
        {
          key: 4,
          width: 100,
          field: "remark",
          label: `扣款描述`,
          visible: true,
        },
      ],
      tableData5: [],
      total5: 0,
      columns5: [
        {
          key: 0,
          field: "adjustmentType",
          label: `调整类型`,
          visible: true,
          dict: "adjustment_type",
        },
        {
          key: 1,
          field: "dataType",
          label: `费用类型`,
          visible: true,
          dict: "test_project_type",
        },
        {
          key: 2,
          field: "schemeId",
          label: `子目号`,
          visible: true,
        },
        {
          key: 3,
          field: "schemeName",
          label: `子目名称`,
          visible: true,
        },
        { key: 4, field: "price", label: `单价`, visible: true },
        { key: 5, field: "unit", label: `单位`, visible: true },
        { key: 6, field: "num", label: `数量`, visible: true },
        { key: 7, field: "amount", label: `金额`, visible: true },
        { key: 10, field: "remark", label: `备注`, visible: true },
        {
          key: 11,
          width: 100,
          field: "createBy",
          label: `操作人`,
          visible: true,
        },
        {
          key: 12,
          width: 100,
          field: "createTime",
          label: `操作时间`,
          visible: true,
        },
      ],
      tableData6: [],
      total6: 0,
      columns6: [
        {
          key: 0,
          field: "dataType",
          label: `费用类型`,
          visible: true,
          dict: "test_project_type",
        },
        {
          key: 1,
          field: "projName",
          label: `调差项目`,
          visible: true,
        },
        {
          key: 2,
          field: "schemeCode",
          label: `子目号`,
          visible: true,
        },
        {
          key: 3,
          field: "schemeName",
          label: `子目名称`,
          visible: true,
        },
        { key: 4, field: "price", label: `单价`, visible: true },
        { key: 5, field: "unit", label: `单位`, visible: true },
        {
          key: 6,
          field: "calcDesc",
          label: `计算式`,
          visible: true,
        },
        { key: 7, field: "num", label: `数量`, visible: true },
        { key: 8, field: "amount", label: `金额`, visible: true },
        { key: 9, field: "remark", label: `备注`, visible: true },
      ],
      tab: '1'
    };
  },
  props: {
    rowData: {
      type: Object,
      default: () => {},
    },
  },
  created() {
    this.handleQuery1();
    this.handleQuery2();
    this.handleQuery3();
    this.handleQuery4();
    this.handleQuery5();
    this.handleQuery6();
  },
  methods: {
    handleQuery1() {
      this.loading = true;
      this.queryParams.settleId = this.rowData.id;
      middleListBySid(this.queryParams).then((res) => {
        this.tableData1 = res.rows;
        this.total1 = res.total;
        this.loading = false;
      });
    },
    handleQuery2() {
      this.loading = true;
      this.queryParams.settleCalcId = this.rowData.id;
      listEvent(this.queryParams).then((res) => {
        this.tableData2 = res.rows;
        this.tableData2.forEach(item => {
          item.queryParams = {
            settleId: item.settleId,
            detailId: item.detailId,
            pageNum: 1,
            pageSize: 10,
          }
          item.childTotal = 0
        });
        this.total2 = res.total;
        this.loading = false;
      });
    },
    handleQuery3() {
      this.loading = true;
      this.queryParams.settleCalcId = this.rowData.id;
      listScheme(this.queryParams).then((res) => {
        this.tableData3 = res.rows;
        this.total3 = res.total;
        this.loading = false;
      });
    },
    handleQuery4() {
      this.loading = true;
      this.queryParams.settleCalcId = this.rowData.id;
      fetchDeductionList(this.queryParams).then((res) => {
        this.tableData4 = res.rows;
        this.total4 = res.total;
        this.loading = false;
      });
    },
    handleQuery5() {
      this.loading = true;
      this.queryParams.settleId = this.rowData.id;
      fetchFundAdjustList(this.queryParams).then((res) => {
        this.tableData5 = res.rows;
        this.total5 = res.total;
        this.loading = false;
      });
    },
    handleQuery6() {
      this.loading = true;
      this.queryParams.settleCalcId = this.rowData.id;
      fetchMaterialAdjustList(this.queryParams).then((res) => {
        this.tableData6 = res.rows;
        this.total6 = res.total;
        this.loading = false;
      });
    },
    tabClick(event) {
      this.queryParams = {
        pageNum: 1,
        pageSize: 10,
      }
      this[`handleQuery${event.name}`]()
    },
    exportList1() {
      this.download(
        "manager/calc/check/settlecalc/detail/export",
        { settleId: this.rowData.id },
        `settlecalc_detail_${new Date().getTime()}.xlsx`,
        {
          headers: { "Content-Type": "application/json;" },
          parameterType: "body",
        }
      );
    },
    exportList2() {
      this.download(
        "manager/calc/check/settlecalc/detail/export/visa",
        { ...this.queryParams },
        `intermediate_detail_${new Date().getTime()}.xlsx`,
        {
          headers: { "Content-Type": "application/json;" },
          parameterType: "body",
        }
      );
    },
    exportList3() {
      this.download(
        "manager/calc/check/settle/method/export/data",
        { ...this.queryParams },
        `method_settlecalc_${new Date().getTime()}.xlsx`,
        {
          headers: { "Content-Type": "application/json;" },
          parameterType: "body",
        }
      );
    },
    exportList4() {
      this.download(
        "manager/calc/check/deduction/export",
        { ...this.queryParams },
        `method_deduction_${new Date().getTime()}.xlsx`,
        {
          headers: { "Content-Type": "application/json;" },
          parameterType: "body",
        }
      );
    },
    // 选中
    handleSelectionChange(e) {
      this.selectIds = e.map((item) => item.detailId);
    },
    loadData(row) {
      this.methodLoading = true;
      listEventBySettleId(row.queryParams).then((res) => {
        this.$set(row, "methodList", res.rows);
        this.$set(row, "childTotal", res.total)
        this.methodLoading = false;
      });
    },
    resetQuery() {
      this.queryParams = {
        pageNum: 1,
        pageSize: 10,
      };
    },
  },
};
</script>
<style lang="scss" scoped></style>
<style lang="scss" scoped>
@import "@/assets/styles/business.scss";
</style>
