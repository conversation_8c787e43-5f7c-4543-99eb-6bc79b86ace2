<template>
  <div
    v-loading="loading"
    class="road-interflow-edit"
    :class="oneMap ? 'one-map' : ''"
  >
    <el-form
      ref="ruleFormEl"
      :model="ruleForm"
      label-width="auto"
      :disabled="forView"
    >
      <MepuColumn
        :is-manger-unit="true"
        :fields="baseInfoFields"
        :from="ruleForm"
        :forView="forView"
      />
    </el-form>
  </div>
</template>
  
  <script>
import { assetInfo } from "@/api/baseData/subgrade/sideSlope/index.js";
import PileInput from "@/components/PileInput/index.vue";
import SelectTree from "@/components/DeptTmpl/selectTree";
import RouteRoad from "@/components/RouteRoad";
import RouteRoadSub from "@/components/RouteRoadSub";
import Dialog from "@/components/Dialog/index.vue";
import "@riophae/vue-treeselect/dist/vue-treeselect.css";
import Treeselect from "@riophae/vue-treeselect";
import RoadSection from "@/views/baseData/components/roadSection";
import RouteCoding from "@/views/baseData/components/routeCoding";
import ManageSelectTree from "@/components/manageSelectTree/index.vue";
import MepuColumn from "@/views/baseData/components/MepuColumn/index.vue";
import lonLat from "@/components/mapPosition/lonLat.vue";
import formFields from "../dataDictionary.js";

export default {
  name: "RoadInterflowEdit",
  components: {
    lonLat,
    MepuColumn,
    ManageSelectTree,
    PileInput,
    SelectTree,
    RouteRoad,
    RouteRoadSub,
    Dialog,
    Treeselect,
    RoadSection,
    RouteCoding,
  },
  dicts: ["left_right", "lane", "patrol_inspection_direction"],
  inject: ["oneMap"],
  props: {
    formData: {
      default: {},
    },
    id: {
      type: [String, Number],
      default: "",
    },
    forView: {
      default: true,
    },
  },
  data() {
    return {
      loading: false,

      ruleForm: {
        flowRampList: [],
        managementMaintenanceId: "",
        maintenanceSectionId: "",
        managementMaintenanceBranchId: "",
      },

      showRamp: false, // 选择匝道
      formH: 500, // form最大高度
      deptOptions: [], // 部门树数据
      ownerOptions: [], //
      types: 201, // 编码规划
      branchDeptOptions: [], // 管养分出数据
      baseInfoFields: [],
    };
  },
  computed: {},
  watch: {},
  created() {
    this.init();
  },
  mounted() {},
  methods: {
    init() {
      this.baseInfoFields = JSON.parse(JSON.stringify(formFields.baseInfoData));

      assetInfo(this.id).then((res) => {
        if (res.code === 200) {
          this.ruleForm = res.data;
        }
      });
    },
  },
};
</script>
  
  <style lang="scss" scoped>
@import "@/assets/styles/common.scss";

.road-interflow-edit {
  /* max-height: 75vh;
      padding: 10px;*/
  overflow-y: auto;
  overflow-x: hidden;
}

::v-deep .el-textarea.is-disabled .el-textarea__inner {
  background-color: rgba(1, 102, 254, 0.2);
  border-color: #0166fe;
}

.el-divider--horizontal {
    margin: 24px 0 !important;
  }
</style>
  