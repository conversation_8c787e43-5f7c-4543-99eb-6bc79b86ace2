<template>
  <PageContainer>
    <template slot="search">
      <div style="display: flex; align-items: center; margin: 0">
        <CascadeSelection
          :form-data="queryParams"
          v-model="queryParams"
          types="201"
          multiple
        />
        <div style="margin-left: 20px">
          <el-input
            v-model="queryParams.tunnelName"
            style="width: 100%"
            placeholder="隧道名称"
            clearable
          />
        </div>
        <div style="margin-left: 20px">
          <el-input
            v-model="queryParams.tunnelCode"
            style="width: 100%"
            placeholder="隧道编码"
            clearable
          />
        </div>
        <div style="margin-left: 20px">
          <el-date-picker
            style="width: 100%"
            v-model="queryParams.checkYear"
            type="year"
            placeholder="年份"
            value-format="yyyy"
          />
        </div>
        <div style="margin: 0 20px">
          <el-select
            v-model="queryParams.checkType"
            style="width: 100%"
            placeholder="检查类型"
            clearable
          >
            <el-option
              v-for="item in dict.type.tunnel_periodic_detection_type"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </div>
        <div style="min-width: 240px">
          <el-button
            v-hasPermi="['baseData:tunnelDiseases:listPage']"
            type="primary"
            icon="el-icon-search"
            @click="handleQuery"
            >搜索</el-button
          >
          <el-button icon="el-icon-refresh" @click="resetQuery">重置</el-button>
        </div>
      </div>
    </template>
    <template slot="header">
      <div class="button-list">
        <!-- <el-button
          v-hasPermi="['baseData:import:execute']"
          type="primary"
          @click="handleImport"
        >导入</el-button> -->
        <el-button
          v-hasPermi="['baseData:tunnelDiseases:delete']"
          type="primary"
          @click="handleRemove"
          >删除</el-button
        >
        <el-button v-hasPermi="['baseData:import:execute']" type="primary" @click="importUpdate">导入更新</el-button>
        <el-button v-hasPermi="['baseData:import:execute']" type="primary" @click="importAdd">导入新增</el-button>
        <el-button type="primary" class="mb8" @click="exportList"
          >数据导出</el-button
        >
      </div>
    </template>
    <template slot="body">
      <el-table
        v-adjust-table
        ref="table"
        v-loading="loading"
        height="99%"
        border
        :data="tableData"
        :header-cell-style="{ height: '36px' }"
        :row-style="rowStyle"
        @selection-change="handleSelectionChange"
        @row-click="handleRowClick"
      >
        <el-table-column fixed type="selection" width="50" align="center" />
        <el-table-column
          fixed
          label="序号"
          type="index"
          width="50"
          align="center"
        >
          <template v-slot="scope">
            {{
              scope.$index +
              (queryParams.pageNum - 1) * queryParams.pageSize +
              1
            }}
          </template>
        </el-table-column>
        <el-table-column
          fixed
          label="年度"
          align="center"
          prop="checkYear"
          min-width="120"
          show-overflow-tooltip
        />
        <el-table-column
          label="隧道名称"
          align="center"
          prop="tunnelName"
          min-width="120"
          show-overflow-tooltip
        />
        <el-table-column
          label="隧道编码"
          align="center"
          prop="tunnelCode"
          min-width="120"
          show-overflow-tooltip
        />
        <el-table-column
          label="管理处"
          align="center"
          prop="managementMaintenanceName"
          min-width="120"
          show-overflow-tooltip
        />
        <el-table-column
          label="管养分处"
          align="center"
          prop="managementMaintenanceBranchName"
          min-width="120"
          show-overflow-tooltip
        />
        <el-table-column
          label="养护路段"
          align="center"
          prop="maintenanceSectionName"
          min-width="120"
          show-overflow-tooltip
        />
        <el-table-column
          label="路线编码"
          align="center"
          prop="routeCode"
          min-width="120"
          show-overflow-tooltip
        />
        <el-table-column
          label="检查日期"
          align="center"
          prop="checkDate"
          min-width="120"
          show-overflow-tooltip
        />
        <el-table-column
          label="检查类型"
          align="center"
          prop="checkType"
          min-width="120"
          show-overflow-tooltip
        >
          <template slot-scope="scope">
            <dict-tag
              :options="dict.type.tunnel_periodic_detection_type"
              :value="scope.row.checkType"
            />
          </template>
        </el-table-column>
        <el-table-column
          label="起点桩号"
          align="center"
          prop="startStake"
          min-width="120"
          show-overflow-tooltip
        >
          <template slot-scope="scope">
            {{ formatPile(scope.row.startStake) }}
          </template>
        </el-table-column>
        <el-table-column
          label="终点桩号"
          align="center"
          prop="endStake"
          min-width="120"
          show-overflow-tooltip
        >
          <template slot-scope="scope">
            {{ formatPile(scope.row.endStake) }}
          </template>
        </el-table-column>
        <el-table-column
          label="分项"
          align="center"
          prop="itemize"
          min-width="120"
          show-overflow-tooltip
        />
        <el-table-column
          label="缺陷位置"
          align="center"
          prop="diseasePos"
          min-width="120"
          show-overflow-tooltip
        />
        <el-table-column
          label="病害类型"
          align="center"
          prop="diseaseType"
          min-width="120"
          show-overflow-tooltip
        />
        <el-table-column
          label="长度(m)"
          align="center"
          prop="length"
          min-width="120"
          show-overflow-tooltip
        />
        <el-table-column
          label="宽度(m)"
          align="center"
          prop="width"
          min-width="120"
          show-overflow-tooltip
        />
        <el-table-column
          label="面积(㎡)"
          align="center"
          prop="area"
          min-width="120"
          show-overflow-tooltip
        />
        <el-table-column
          label="技术状况描述"
          align="center"
          prop="techDescription"
          min-width="300"
          show-overflow-tooltip
        />
        <el-table-column
          label="状况值"
          align="center"
          prop="conditionValue"
          min-width="120"
          show-overflow-tooltip
        />
        <el-table-column
          label="计量基数"
          align="center"
          prop="measurementBase"
          min-width="120"
          show-overflow-tooltip
        />
        <el-table-column
          label="单位"
          align="center"
          prop="unit"
          min-width="120"
          show-overflow-tooltip
        />
        <el-table-column
          label="备注"
          align="center"
          prop="remark"
          min-width="120"
          show-overflow-tooltip
        />
      </el-table>
      <pagination
        :total="total"
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
        :pageSizes="[10, 20, 30, 50, 100, 1000]"
        @pagination="getList"
      />
    </template>
    <ImportData
      v-if="showImport"
      :is-update="false"
      :dialog-visible="showImport"
      :import-base-type="'13'"
      :import-type="importType"
      @close="closeImportAdd"
    />
  </PageContainer>
</template>

<script>
import CascadeSelection from "@/components/CascadeSelection/index.vue";
import ImportData from "@/views/baseData/components/importData/index.vue";
import { getListPage, deleteByIds } from "@/api/baseData/tunnel/disease/index";

export default {
  name: "Disease",
  props: {},
  dicts: ["tunnel_periodic_detection_type", "tunnel_assess_grade"],
  components: { CascadeSelection, ImportData },
  data() {
    return {
      loading: false,
      queryParams: { pageNum: 1, pageSize: 20, checkYear: new Date().getFullYear() + '' },
      total: 0,
      tableData: [],
      showImport: false,
      ids: [],
      isUpdate: false,
      importType: 2,
    };
  },
  created() {
    this.getList();
  },
  methods: {
    getList() {
      this.loading = true;
      getListPage(this.queryParams)
        .then((res) => {
          if (res.code === 200) {
            this.tableData = res.rows;
            this.total = res.total;
          }
        })
        .finally(() => {
          this.loading = false;
        });
    },
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    resetQuery() {
      this.queryParams = {
        pageNum: 1,
        pageSize: 20,
        checkYear: new Date().getFullYear() + ''
      };
      this.getList();
    },
    handleImport() {
      this.showImport = true;
    },
    // 导入更新按钮
    importUpdate() {
      this.isUpdate = true;
      this.showImport = true;
      this.importType = 1;
    },
    // 导入新增按钮
    importAdd() {
      this.isUpdate = false;
      this.showImport = true;
      this.importType = 2;
    },
    closeImportAdd(v) {
      this.showImport = false;
      if (v) this.getList();
    },
    handleRemove() {
      if (this.ids.length == 0) {
        this.$message.warning("请选择至少一条数据进行删除！");
        return;
      }
      this.$modal
        .confirm("确认删除？")
        .then(() => {
          deleteByIds(this.ids).then((res) => {
            if (res && res.code == "200") {
              this.getList();
              this.$modal.msgSuccess("删除成功");
            }
          });
        })
        .catch(() => {});
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id);
    },
    // 表格点击勾选
    handleRowClick(row) {
      row.isSelected = !row.isSelected;
      this.$refs.table.toggleRowSelection(row);
    },
    // 导出清单按钮
    exportList() {
      this.download(
        "/baseData/tunnel/diseases/export",
        { ids: this.ids, ...this.queryParams },
        ``,
        {
          headers: { "Content-Type": "application/json;" },
          parameterType: "body",
        }
      );
    },
    // 勾选高亮
    rowStyle({ row, rowIndex }) {
      if (this.ids.includes(row.id)) {
        return { "background-color": "#b7daff", color: "#333" };
      } else {
        return { "background-color": "#fff", color: "#333" };
      }
    },
  },
  computed: {},
  watch: {},
};
</script>

<style lang="scss" scoped>
.button-list {
  border-radius: 4px;
  width: 100%;
  .el-button {
    margin-bottom: 10px;
    margin-right: 10px;
    margin-left: 0;
  }
}
</style>
