<template>
  <div class="app-container maindiv">
    <el-row :gutter="20" style="display: flex;">
      <!--部门数据-->
      <maintenance-tree @rowClick="handleNodeClick" :realNav="realNav" @closeNav="realNav=false"></maintenance-tree>
      <!--角色数据-->
      <el-col :span="realNav ? 19 : 24" :xs="24">
        <!--展开图标-->
        <div class="rightIcon" @click="realNav = true" v-show="!realNav">
          <span class="el-icon-caret-right"></span>
        </div>
        <el-row>
          <el-col :span="24" :xs="24">
            <el-row>
              <el-form
                ref="queryForm"
                :inline="true"
                :model="queryParams"
                label-width="68px"
                size="mini"
              >
                <el-form-item label="" prop="year">
                  <el-date-picker
                    v-model="queryParams.year"
                    placeholder="年份"
                    style="width: 240px"
                    type="year"
                    value-format="yyyy"
                  >
                  </el-date-picker>
                </el-form-item>
                <el-form-item>
                  <selectTree
                    :key="'domainId'"
                    v-model="queryParams.domainId"
                    :deptType="100"
                    :deptTypeList="[1, 3, 4]"
                    clearable
                    filterable
                    placeholder="管养单位"
                    style="width: 240px"
                  />
                </el-form-item>
                <el-form-item>
                  <selectTree
                    :key="'conDomainId'"
                    v-model="queryParams.conDomainId" :data-rule="false"
                    :dept-type="100"
                    placeholder="施工单位"
                    :filter-keys="['云南省交通投资建设集团有限公司', '云南交投投资有限公司']"
                    :expand-all="false"
                    clearable
                    filterable
                    style="width: 240px"
                  />
                </el-form-item>
                <el-form-item>
                  <RoadSection
                    v-model="queryParams.maiSecId"
                    :deptId="queryParams.domainId"
                    placeholder="路段"
                    style="width: 240px"
                  />
                </el-form-item>
                <el-form-item>
                  <el-input
                    v-model="queryParams.projectName"
                    placeholder="项目名称"
                    style="width: 240px"
                  />
                </el-form-item>
                <el-form-item>
                  <el-input
                    v-model="queryParams.constructionName"
                    placeholder="任务单名称"
                    style="width: 240px"
                  />
                </el-form-item>
                <el-form-item>
                  <el-input
                    v-model="queryParams.constructionCode"
                    placeholder="任务单编号"
                    style="width: 240px"
                  />
                </el-form-item>
                <el-form-item>
                  <el-input
                    v-model="queryParams.schemeCode"
                    placeholder="子目号"
                    style="width: 240px"
                  />
                </el-form-item>
                <el-form-item>
                  <dict-select
                    v-model="queryParams.calcStatus"
                    clearable
                    placeholder="计量情况"
                    style="width: 240px"
                    type="testing_calc_status"
                  ></dict-select>
                </el-form-item>

                <el-form-item>
                  <el-button
                    icon="el-icon-search"
                    size="mini"
                    type="primary"
                    @click="handleQuery"
                    >搜索</el-button
                  >
                  <el-button
                    icon="el-icon-refresh"
                    size="mini"
                    @click="resetQuery"
                    >重置</el-button
                  >
                  <el-button
                    v-show="!showSearch"
                    circle
                    icon="el-icon-arrow-down"
                    @click="showSearch = true"
                  ></el-button>
                  <el-button
                    v-show="showSearch"
                    circle
                    icon="el-icon-arrow-up"
                    @click="showSearch = false"
                  ></el-button>
                </el-form-item>
              </el-form>
              <!--默认折叠-->
              <el-col :span="24">
                <el-form
                  v-show="showSearch"
                  ref="queryForm"
                  :inline="true"
                  :model="queryParams"
                  label-width="68px"
                  size="small"
                >
                  <el-form-item>
                    <dict-select
                        v-model="queryParams.isGuarantee"
                        clearable
                        placeholder="请选择是否计算安全保通费"
                        style="width: 240px"
                        type="bridge_simple_bool"
                    ></dict-select>
                  </el-form-item>
                  <el-form-item>
                    <dict-select
                        v-model="queryParams.isProduction"
                        clearable
                        placeholder="请选择是否计算安全生产费"
                        style="width: 240px"
                        type="bridge_simple_bool"
                    ></dict-select>
                  </el-form-item>
                  <el-form-item>
                    <el-date-picker
                      v-model="queryParams.realEDate"
                      end-placeholder="完工日期"
                      range-separator="至"
                      start-placeholder="完工日期"
                      style="width: 240px"
                      type="daterange"
                      value-format="yyyy-MM-dd"
                    >
                    </el-date-picker>
                  </el-form-item>
                  <el-form-item>
                    <dict-select
                        v-model="queryParams.status"
                        clearable
                        placeholder="任务单状态"
                        style="width: 240px"
                        type="settlement_status"
                    ></dict-select>
                    </el-form-item>
                  <el-form-item>
                    <el-input
                      v-model="queryParams.intermediateCode"
                      placeholder="中间计量单编码"
                      style="width: 240px"
                    ></el-input>
                  </el-form-item>
                  <el-form-item>
                    <el-input
                      v-model="queryParams.settleCode"
                      placeholder="结算计量单编码"
                      style="width: 240px"
                    ></el-input>
                  </el-form-item>
                  <el-form-item>
                    <el-input
                      v-model="queryParams.finishedCode"
                      placeholder="签证单编码"
                      style="width: 240px"
                    ></el-input>
                  </el-form-item>
                  <el-form-item label="" prop="operator">
                    <el-cascader
                      v-model="queryParams.operator"
                      :options="deptUserOptions"
                      :props="visaCheckProps"
                      :show-all-levels="false"
                      ref="deptUser"
                      placeholder="通知单拟定人" clearable style="width: 240px"
                      filterable clearable></el-cascader>
                  </el-form-item>
                </el-form>
              </el-col>
            </el-row>
          </el-col>
        </el-row>
        <!--操作按钮区开始-->
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5" class="mb10">
            <el-button
              type="primary"
              icon="el-icon-view"
              size="mini"
              v-has-menu-permi="['calctheft:settle:visapreview']"
              @click="previewVisa"
              >签证单预览
            </el-button>
          </el-col>
          <el-col :span="1.5" class="mb10">
            <el-button
              type="success"
              icon="el-icon-view"
              size="mini"
              v-has-menu-permi="['calctheft:settle:maintainpreview']"
              @click="previewMaintenance"
              >维修档案预览
            </el-button>
          </el-col>
          <el-col :span="1.5" class="mb10">

          </el-col>
          <el-col :span="1.5" class="mb10">
            <el-button
              type="warning"
              icon="el-icon-view"
              size="mini"
              v-has-menu-permi="['calctheft:settle:repairpreview']"
              @click="previewRepair"
              >修复反馈预览
            </el-button>
          </el-col>
          <el-col :span="1.5" class="mb10">
            <el-button
              type="primary"
              icon="el-icon-download"
              size="mini"
              v-has-menu-permi="['calctheft:settle:filedownload']"
              @click="archivesDownload"
            >施工档案下载
            </el-button>
          </el-col>
          <el-col :span="1.5" class="mb10">
            <el-button type="primary" v-has-menu-permi="['calctheft:settle:updatevisacheck']" icon="el-icon-edit" size="mini" @click="openEditUserInfoDialog"
            >人员信息修改
            </el-button>
          </el-col>
          <el-col :span="1.5" class="mb10">
            <el-button
              type="warning"
              icon="el-icon-download"
              size="mini"
              v-has-menu-permi="['calctheft:settle:export']"
              @click="exportList"
              >导出清单
            </el-button>
          </el-col>
          <el-col :span="1.5" class="mb10">
            <el-button
              class="mb10"
              type="primary"
              icon="el-icon-download"
              size="mini"
              v-has-menu-permi="['calctheft:settle:maintaindownload']"
              @click="constructionFileDownload"
              >维修档案批量导出
            </el-button>
          </el-col>
          <el-col :span="1.5" class="mb10">
            <el-button
              size="mini"
              type="success"
              v-has-menu-permi="['calctheft:method:updateSafetyFee']"
              @click="handleUpdate"
            >更新是否计算安全费
            </el-button>
          </el-col>
          <el-col :span="1.5" class="mb10">
            <el-button
              type="warning"
              icon="el-icon-view"
              size="mini"
              v-has-permi="['settlement:repository:regenerate']"
              @click="handleRegenerate"
            >重新生成报表
            </el-button>
          </el-col>
          <el-col :span="1.5" class="mb10">
            <el-button
              type="danger"
              icon="el-icon-refresh-left"
              size="mini"
              v-has-permi="['theft:finished:withdraw']"
              @click="handleWithdraw"
            >撤回
            </el-button>
          </el-col>
          <right-toolbar
            :showSearch.sync="showSearch"
            @queryTable="handleQuery"
            :columns="columns"
          ></right-toolbar>
        </el-row>
        <el-row>
          <div class="draggable">
            <el-table v-adjust-table
              size="mini"
              style="width: 100%"
              v-loading="loading"
              border
              :data="tableData"
              row-key="settleId"
              ref="dataTable"
              stripe
              highlight-current-row
              @selection-change="handleSelectionChange"
              @row-click="handleRowClick"
              @expand-change="loadData"
              :height="
                showSearch ? 'calc(100vh - 430px)' : 'calc(100vh - 370px)'
              "
            >
              <el-table-column type="expand">
                <template slot-scope="props">
                  <el-table v-adjust-table :data="props.row.methodList" style="width: 100%">
                    <el-table-column
                        align="center"
                        label=""
                        width="100"
                    />
                    <el-table-column
                        align="center"
                        label="序号"
                        type="index"
                        width="50"
                    />
                    <el-table-column
                      align="center"
                      label="子目号"
                      width="200"
                      prop="schemeCode"
                    >
                    </el-table-column>
                    <el-table-column
                      align="center"
                      label="子目名称"
                      width="200"
                      prop="schemeName"
                    >
                    </el-table-column>
                    <el-table-column
                      align="center"
                      label="单价"
                      width="100"
                      prop="price"
                    >
                    </el-table-column>
                    <el-table-column
                      align="center"
                      label="计算式"
                      width="200"
                      prop="calcDesc"
                    >
                    </el-table-column>
                    <el-table-column align="center" label="数量" prop="num" width="100">
                    </el-table-column>
                    <el-table-column align="center" label="金额" prop="amount" width="100">
                    </el-table-column>
                    <el-table-column align="center" label="备注" prop="remark" width="200" show-overflow-tooltip>
                    </el-table-column>
                  </el-table>
                  <pagination
                    v-show="props.row.totalNum > 0"
                    :limit.sync="props.row.queryData.pageSize"
                    :page.sync="props.row.queryData.pageNum"
                    :total="props.row.totalNum"
                    @pagination="getRowDetailList(props.row)"
                  />
                </template>
              </el-table-column>
              <el-table-column type="selection" width="50" align="center" />
              <el-table-column
                label="序号"
                align="center"
                type="index"
                width="50"
              />
              <template v-for="(column, index) in columns">
                <el-table-column
                  :label="column.label"
                  v-if="column.visible"
                  align="center"
                  :prop="column.field"
                  :width="column.width"
                >
                  <template slot-scope="scope">
                    <dict-tag
                      v-if="column.dict"
                      :options="dict.type[column.dict]"
                      :value="scope.row[column.field]"
                    />
                    <template v-else-if="column.slots">
                      <RenderDom
                        :row="scope.row"
                        :index="index"
                        :render="column.render"
                      />
                    </template>
                    <span v-else-if="column.isTime">{{
                      parseTime(scope.row[column.field], "{y}-{m}-{d}")
                    }}</span>
                    <span v-else>{{ scope.row[column.field] }}</span>
                  </template>
                </el-table-column>
              </template>
              <el-table-column
                label="操作"
                fixed="right"
                align="center"
                width="250"
                class-name="small-padding fixed-width"
              >
                <template slot-scope="scope">
                  <el-button
                    size="mini"
                    type="text"
                    icon="el-icon-edit"
                    v-has-menu-permi="['calctheft:method:updateNumber']"
                    @click="handleEdit(scope.row)"
                    >修改
                  </el-button>
                  <el-button
                    type="text"
                    icon="el-icon-view"
                    size="mini"
                    @click="hanldeViewOperation(scope.row)"
                    >操作记录
                  </el-button>
                </template>
              </el-table-column>
            </el-table>
            <pagination
              v-show="total > 0"
              :total="total"
              :page.sync="queryParams.pageNum"
              :limit.sync="queryParams.pageSize"
              @pagination="handleQuery"
            />
          </div>
        </el-row>
      </el-col>
    </el-row>
    <el-dialog
      :title="drawerTitle"
      destroy-on-close
      :visible.sync="drawer"
      :close-on-click-modal="false"
      size="50%"
    >
      <detail @close="handleCloseDetail" :row-data="rowData"></detail>
    </el-dialog>
    <el-dialog
      title="事件信息"
      destroy-on-close
      v-if="dialogVisible"
      :visible.sync="dialogVisible"
      :close-on-click-modal="false"
      width="90%"
    >
      <event-info :row="rowData"></event-info>
    </el-dialog>
    <el-dialog
      title="修改计算类型"
      destroy-on-close
      :visible.sync="updateDialog"
      :close-on-click-modal="false"
      width="500px"
    >
      <div class="road-interflow-edit">
        <el-row :gutter="15">
          <el-form
            ref="updateForm"
            :model="rowData"
            size="medium"
            label-width="180px"
            :inline="true"
          >
            <el-col :span="24">
              <el-form-item label="是否计算安全保通费" prop="isGuaranteeTemp">
                <dict-select
                  type="bridge_simple_bool"
                  v-model="rowData.isGuaranteeTemp"
                  placeholder="请选择是否计算安全保通费"
                  style="width: 125%"
                ></dict-select>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="是否计算安全生产费" prop="isProductionTemp">
                <dict-select
                  type="bridge_simple_bool"
                  v-model="rowData.isProductionTemp"
                  placeholder="请选择是否计算安全生产费"
                  style="width: 125%"
                ></dict-select>
              </el-form-item>
            </el-col>
          </el-form>
          <div style="text-align: right">
            <el-button type="primary" size="mini" @click="handleUpdateSafetyFee">保存</el-button>
          </div>
        </el-row>
      </div>
    </el-dialog>
    <el-dialog
      title="编辑人员信息"
      destroy-on-close
      :visible.sync="userInfoDialog"
      :close-on-click-modal="false"
      size="50%"
    >
      <el-form  ref="userInfoRef" :model="userInfo" :rules="userInfoRules">
        <el-form-item label="验收人员" prop="visaBy" label-width="100px">
          <el-cascader
            v-model="userInfo.visaBy"
            :options="deptUserOptions"
            :props="visaProps"
            :show-all-levels="false"
            ref="visaRef"
            filterable
            clearable
            collapse-tags
            style="width: 100%"
          ></el-cascader>
        </el-form-item>
        <el-form-item label="审核人" prop="visaCheckBy" label-width="100px">
          <el-cascader
            ref="visaCheckRef"
            v-model="userInfo.visaCheckBy"
            :options="deptUserOptions"
            :props="visaCheckProps"
            :show-all-levels="false"
            filterable
            clearable
            collapse-tags
            style="width: 100%"
          ></el-cascader>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button size="small" @click="userInfoDialog = false">取 消</el-button>
        <el-button size="small" type="primary" @click="saveUserInfo">确 定</el-button>
      </span>
    </el-dialog>
    <IFramePreview ref="iframeRef" :srcdoc="preview.html" :down-url="preview.url" :file-name="preview.fileName"></IFramePreview>

  </div>
</template>

<script>
import Detail from "./detail.vue";
import EventInfo from "../component/eventTreeInfo.vue";
import selectTree from "@/components/DeptTmpl/selectTree.vue";
import MaintenanceTree from "@/components/MaintenanceTree/index.vue"
import {
  otherRepositoryList,
  fetchMethodList,
  fetchMethodListAll,
  previewVisa,
  preViewMaintenance,
  previewRepair,
  updateSafetyFee, downloadConstruction, updateVisaCheck, getVisaCheck, withdraw
} from '@/api/calculate/theft/settlement'
import { findUserDeptMaintenanceList2 } from "@/api/system/maintenanceSection";
import RoadSection from "@/views/baseData/components/roadSection/index.vue";
import axios from "axios";
import IFramePreview from "@/components/IFramePreview/index.vue";
import {getTreeStruct} from "@/api/tmpl";
import {regenerateReport} from "@/api/dailyMaintenance/metering/addPrice";
export default {
  name: 'Settlement',
  components: {
    IFramePreview,
    MaintenanceTree,
    RoadSection,
    selectTree,
    EventInfo,
    Detail,
    RenderDom: {
      functional: true,
      props: {
        row: Object,
        index: Number,
        render: Function,
      },
      render(createElement, ctx) {
        const { row, index } = ctx.props;
        return ctx.props.render(row, index);
      },
    },
  },
  dicts: ["cost_name", "sys_asset_type", "testing_calc_status", "settlement_status"],
  props: [],
  data() {
    return {
      leftTotal: 1,
      showSearch: false,
      queryParams: {
        pageNum: 1,
        pageSize: 50,
        addUnitPrice: 0
      },
      total: 0,
      loading: false,
      columns: [
        {
          key: 0,
          width: 150,
          field: "status",
          label: `状态`,
          visible: true,
          dict: "settlement_status",
        },
        {
          key: 1,
          width: 100,
          field: "calcStatus",
          label: `计量情况`,
          visible: true,
          dict: "testing_calc_status",
        },
        {
          key: 2,
          width: 200,
          field: "constructionCode",
          label: `任务单编号`,
          visible: true,
        },
        {
          key: 3,
          width: 200,
          field: "constructionName",
          label: `任务单名称`,
          visible: true,
        },
        {
          key: 4,
          width: 100,
          field: "sumFund",
          label: `金额`,
          visible: true,
        },
        {
          key: 5,
          width: 100,
          field: "residueFund",
          label: `剩余金额`,
          visible: true,
        },
        {
          key: 6,
          width: 200,
          field: "projectName",
          label: `项目名称`,
          visible: true,
        },
        {
          key: 7,
          width: 100,
          field: "maiSecName",
          label: `路段名称`,
          visible: true,
        },
        {
          key: 8,
          width: 100,
          field: "domainName",
          label: `管养单位`,
          visible: true,
        },
        {
          key: 9,
          width: 100,
          field: "productionFund",
          label: `安全生产费`,
          visible: true,
          slots: true,
          render: (row) => {
            return (
                <span>{row.productionFund?.toFixed(0)}</span>
            );
          }
        },
        {
          key: 10,
          width: 100,
          field: "guaranteeFund",
          label: `安全保通费`,
          visible: true,
          slots: true,
          render: (row) => {
            return (
                <span>{row.guaranteeFund?.toFixed(0)}</span>
            );
          }
        },
        {
          key: 11,
          width: 100,
          field: "supFund",
          label: `监理费`,
          visible: true,
          slots: true,
          render: (row) => {
            return (
                <span>{row.supFund?.toFixed(0)}</span>
            );
          }
        },

        {
          key: 12,
          width: 200,
          field: "conDomainName",
          label: `施工单位`,
          visible: true,
        },
        {
          key: 13,
          width: 200,
          field: "conConName",
          label: `施工合同`,
          visible: true,
        },
        {
          key: 12,
          width: 200,
          field: "supDomainName",
          label: `监理单位`,
          visible: true,
        },
        {
          key: 12,
          width: 200,
          field: "designDomainName",
          label: `设计单位`,
          visible: true,
        },
        {
          key: 14,
          width: 100,
          field: "endTime",
          label: `完工日期`,
          visible: true,
        },
        {
          key: 12,
          width: 100,
          field: "defLiaPer",
          label: `缺陷责任期(月)`,
          visible: true,
        },
        {
          key: 14,
          width: 200,
          field: "acceptancePerson",
          label: `验收人员`,
          visible: true,
        },
      ],
      tableData: [],
      rowData: {},
      drawerTitle: "修改",
      drawer: false,
      updateDialog: false,
      dialogVisible: false,
      // 左侧组织树
      realNav: true,
      keyword: "",
      preview: {
        html: '',
        url: '',
        fileName: ''
      },
      settleId: "",
      relaOptions: [],
      filteredTreeData: [],
      deptUserOptions: [],
      visaProps: {
        multiple: true, //是否多选
        value: "id",
        emitPath: false,
      },
      visaCheckProps: {
        multiple: false, //是否多选
        value: "id",
        emitPath: false,
      },
      userInfoDialog: false,
      userInfo: {
        id: '',
        visaBy: '',
        visaCheckBy: '',
        visaCheckName: '',
        visaName: ''
      },
      userInfoRules: {
        visaBy: [
          {required: true, type: 'array', message: '请选择验收人员'}
        ],
        visaCheckBy: [
          {required: true, message: '请选择审核人'}
        ]
      },
      selectIds: []
    };
  },
  computed: {},
  watch: {},
  created() {
    this.getDeptTree();
    this.handleQuery();
    this.getDeptTreeDef();
  },
  mounted() {},
  methods: {
    // 关键词检索
    handleSearch() {
      const keyword = this.keyword.toLowerCase();
      this.filteredTreeData = this.relaOptions.filter((node) =>
        this.filterNode(node, keyword)
      );
    },
    // 筛选节点
    filterNode(node, keyword) {
      if (node.label.indexOf(keyword) != -1) {
        return true;
      }
      if (node.children) {
        return node.children.some((childNode) =>
          this.filterNode(childNode, keyword)
        );
      }
      return false;
    },
    /** 查询部门-用户下拉树结构 */
    getDeptTreeDef() {
      getTreeStruct({types:111}).then(response => {
        this.deptUserOptions = response.data;
      });
    },
    // 查询部门下拉树结构
    getDeptTree() {
      findUserDeptMaintenanceList2().then((response) => {
        const treeData = response.data;
        treeData.forEach((item) => {
          getChild(item);
        });

        function getChild(node) {
          node.label = node.deptName || node.maintenanceSectionName;
          node.id = node.deptId || node.maintenanceSectionId;
          if (node.children) {
            node.children.forEach((item) => {
              getChild(item);
            });
          }
        }

        // 增加一个最顶级
        const tree = [
          {
            label: "云南省交通投资建设集团有限公司",
            id: "1",
            children: [...treeData],
          },
        ];
        this.relaOptions = tree;
        this.filteredTreeData = [...this.relaOptions];
      });
    },
    handleNodeClick(e) {
      this.queryParams.domainId = e.domainId || "";
      this.queryParams.maiSecId = e.maiSecId || "";
      this.handleQuery();
    },
    handleQuery() {
      this.loading = true;
      otherRepositoryList(this.queryParams).then((res) => {
        this.tableData = res.rows;
        this.total = res.total;
        this.loading = false;
        this.tableData.forEach((item) => {
            item.queryData = {
              settleId: item.settleId,
              pageNum: 1,
              pageSize: 10
            };
            item.totalNum = 0;
          });
      });
    },
    resetQuery() {
      this.queryParams = {
        pageNum: 1,
        pageSize: 50,
      };
      this.handleQuery()
    },
    handleCloseDetail() {
      this.drawer = false;
    },
    async handleEdit(row) {
      const params = {
        settleId: row.settleId,
        flag: 0
      }
      await fetchMethodListAll(params).then((res) => {
        this.rowData = row;
        this.rowData.methodList = res.data
        this.drawer = true;
      });
    },
    handleUpdate() {
      if (!this.rowData.settleId) {
        this.$message.error('请选择结算单')
        return
      }
      this.$set(this.rowData, "isProductionTemp", this.rowData.isProduction)
      this.$set(this.rowData, "isGuaranteeTemp", this.rowData.isGuarantee)
      this.updateDialog = true;
    },
    handleUpdateSafetyFee() {
      const params = {
        id: this.rowData.settleId,
        isProduction: this.rowData.isProductionTemp,
        isGuarantee: this.rowData.isGuaranteeTemp
      }
      updateSafetyFee(params).then(res => {
        this.$message.success('修改成功')
        this.updateDialog = false
        this.rowData = {}
        this.handleQuery()
      })
    },
    handleRowClick(row) {
      this.rowData = row
      row.isSelected = !row.isSelected;
      this.settleId = row.settleId;
      this.$refs.dataTable.toggleRowSelection(row);
    },
    loadData(row,expandedRows) {
      const isExpand = expandedRows.some((item) => row.settleId === item.settleId);
      if (isExpand) {
        this.getRowDetailList(row)
      }
    },
    getRowDetailList(row) {
      fetchMethodList(row.queryData).then((res) => {
          this.$set(row, "totalNum", res.total || 0);
          this.$set(row, "methodList", res.rows);
      });
    },
    handleSelectionChange(e) {
      this.selectIds = e.map(obj => obj.settleId)
    },
    openEditUserInfoDialog() {
      if (!this.rowData.settleId) {
        this.$message.warning("请先选择一条数据！");
        return;
      }
      getVisaCheck(this.rowData.finishedId).then(res=> {
        this.userInfo = res.data
        this.userInfo.visaBy =  this.userInfo.visaBy.split(',')
        this.userInfoDialog = true
      })
    },
    saveUserInfo() {
      this.$refs.userInfoRef.validate((valid)=> {
        if (valid) {
          const formData = JSON.parse(JSON.stringify(this.userInfo))
          const visaNode = this.$refs.visaRef.getCheckedNodes()
          const visaCheckNode = this.$refs.visaCheckRef.getCheckedNodes()
          formData.visaName = visaNode.map(item=> {
            return item.label
          }).join()
          formData.visaBy = formData.visaBy.join()
          formData.visaCheckName = visaCheckNode[0].label
          updateVisaCheck(formData).then(res=> {
            this.$message.success("修改成功！");
            this.userInfoDialog = false
          })
        }
      })
    },
    previewVisa() {
      if (!this.settleId) {
        this.$message.error('请选择结算单')
        return
      }
      this.loading = true
      previewVisa(this.settleId).then(res => {
        if (res.code == 200){
          this.preview.html = res.data.html
          this.preview.url = res.data.downUrl
          this.preview.fileName = res.data.fileName
          this.$refs.iframeRef.visible = true
        }
        this.loading = false
      })
    },
    previewMaintenance() {
      if (!this.settleId) {
        this.$message.error('请选择结算单')
        return
      }
      this.loading = true
      preViewMaintenance(this.settleId).then(res => {
        if (res.code == 200){
          this.preview.html = res.data.html
          this.preview.url = res.data.downUrl
          this.preview.fileName = res.data.fileName
          this.$refs.iframeRef.visible = true
        }
        this.loading = false
      })
    },

    previewRepair() {
      if (!this.settleId) {
        this.$message.error('请选择结算单')
        return
      }
      this.loading = true
      previewRepair(this.settleId).then(res => {
        if (res.code == 200){
          this.preview.html = res.data.html
          this.preview.url = res.data.downUrl
          this.preview.fileName = res.data.fileName
          this.$refs.iframeRef.visible = true
        }
        this.loading = false
      })
    },
    // 导出清单按钮
    exportList() {
      this.download(
        "manager/calc/theft/settle/export",
        { ...this.queryParams },
        `结算库导出.xlsx`,
        {
          headers: { "Content-Type": "application/json;" },
          parameterType: "body",
        }
      );
    },
    archivesDownload() {
      if (!this.settleId) {
        this.$message.error('请选择结算单')
        return
      }
      this.loading = true
      downloadConstruction(this.settleId).then(res => {
        if (res.code == 200) {
          if (res.data.fileName.endsWith('.zip')) {
            let link = document.createElement('a')
            link.download = res.data.fileName
            link.style.display = 'none'
            link.href = res.data.downUrl
            document.body.appendChild(link)
            link.click()
            document.body.removeChild(link)
            this.loading = false
          } else {
            axios({
              method: "get",
              responseType: 'arraybuffer',
              url: res.data.downUrl,
              headers: {}
            }).then((res) => {
              const arrayBuffer = res.data;
              // 创建一个Blob对象
              const blob = new Blob([arrayBuffer], {type: 'application/zip'}); // 对于.xls文件
              saveAs(blob, res.data.fileName)
            }).finally(() => {
              this.loading = false
            })
          }
        }
      })
    },
    handleRegenerate() {
      if (this.selectIds.length == 0) {
        this.$modal.msgError("请勾选至少一条数据")
        return
      }
      const params = {
        idList: this.selectIds,
        type: 8
      }
      regenerateReport(params).then(res => {
        this.$modal.msgSuccess("操作成功")
      })
    },
    hanldeViewOperation(row) {
      this.rowData = row
      this.dialogVisible = true
    },
    handleWithdraw() {
      if (!this.rowData.finishedId) {
        this.$message.warning('请先选择一条记录！')
        return
      }
      this.$confirm('确定撤回吗？', '警告', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        const params = {
          businessKey: this.rowData.finishedId
        }
        withdraw(params).then(res => {
          this.$message.success('撤回成功')
          this.handleQuery()
        })
      })
    },
    constructionFileDownload() {
      if (this.selectIds.length == 0) {
        this.$message.error('请勾选结算单')
        return
      }
      const params = {
        settleId: this.selectIds.join(',')
      }
      this.download(
          'manager/calc/theft/settle/maintain/download',
          {...params},
          `维修档案批量导出.xlsx`,
          {
            headers: {'Content-Type': 'application/json;'},
            parameterType: 'body'
          }
      )
    },
  },
};
</script>

<style lang="scss" scoped>
.rightIcon {
  border: 1px solid #dcdfe6;
  border-radius: 8px;
  width: 16px;
  height: 50px;
  line-height: 50px;
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
  position: absolute;
  left: -10px;
  top: 280px;
  z-index: 10;
  background: white;
}

.rightIcon:hover {
  background-color: #dcdfe6;
}

.left-total {
  font-size: 13px;
  line-height: 28px;
  color: #606266;
  position: absolute;
  bottom: 10px;
  right: 10px;
}
</style>
<style lang="scss" scoped>
@import "@/assets/styles/business.scss";
</style>
