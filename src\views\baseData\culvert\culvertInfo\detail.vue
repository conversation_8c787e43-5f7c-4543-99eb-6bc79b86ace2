<template>
  <div v-loading="loading" class="culvert-info-edit" :class="oneMap?'one-map':'culvert-one'" >
    <el-tabs v-model="activeName" type="card">
      <el-tab-pane label="基本信息" name="0"/>
      <el-tab-pane label="结构技术" name="1"/>
      <el-tab-pane label="档案资料" name="2"/>
    </el-tabs>

    <!--    <div class="culvert-info-edit-base" :style="{ height: formH + 'px' ,overflowY:auto} ">-->
    <div style="height: 60vh;padding: 0 10px 0 5px;" >
      <el-form
          ref="ruleFormEl"
          :model="ruleForm"
          :rules="rules"
          label-width="160px"
          :disabled="true"
      >
        <template v-if="activeName === '0'">
          <MepuColumn :is-manger-unit="true" :fields="baseInfoFields" :from="ruleForm"/>
        </template>

        <template v-if="activeName === '1'">
          <MepuColumn :fields="structureInfoFields" :from="ruleForm"/>

        </template>

        <template v-if="activeName === '2'">
          <MepuColumn :fields="archivesInfoFields" :from="ruleForm"/>
        </template>
      </el-form>
    </div>

    <div class="text-center">
      <!-- <el-button @click="onClose">关 闭</el-button> -->
    </div>
  </div>
</template>

<script>
import {
  culvertAdd,
  culvertEdit,
  tempAdd,
  getInfoById,
} from "@/api/baseData/culvert/culvertInfo/index.js";
import {deptTreeSelect} from "@/api/tmpl";
import "@riophae/vue-treeselect/dist/vue-treeselect.css";
import Treeselect from "@riophae/vue-treeselect";
import {listMaintenanceSectionAll} from "@/api/system/maintenanceSection";
import {listByMaintenanceSectionId} from "@/api/baseData/common/routeLine";
import {listAllRoute} from "@/api/system/route.js";
import PileInput from "@/components/PileInput/index.vue";
import lonLat from '@/components/mapPosition/lonLat.vue'
import SectionSelect from '@/components/SectionSelect'
import ManageSelectTree from "@/components/manageSelectTree/index.vue";
import formFields from './js/formFields'
import SelectTree from "@/components/DeptTmpl/selectTree.vue";
import CascaderRegion from "@/views/baseData/components/CascaderRegion/index.vue";
import MepuColumn from "@/views/baseData/components/MepuColumn/index.vue";

export default {
  name: "CulvertInfoEdit",
  dicts: [
    "bridge_route_level",
    "sys_route_type",
    "sys_culvert_type",
    "sys_design_load",
    "base_archives_type",
    'sys_design_load'
  ],
  components: {
    CascaderRegion,
    SelectTree,
    ManageSelectTree, Treeselect, PileInput, lonLat, SectionSelect, MepuColumn
  },
  inject: {
    oneMap: {
      default: false,
    },
  },
  props: {
    formData: {
      type: Object,
      default: () => {
      },
    },
    componentId: {
      type: String,
      default: ''
    },
    id: {
      type: [String, Number],
      default: ''
    },
  },
  data() {
    return {
      loading: false,
      activeName: "0",
      rules: {},
      enterOwnerId: "", // 进洞口图片id
      leaveOwnerId: "", // 出洞口图片id
      formH: 600, // form 表单最大高度
      deptOptions: [], // 部门树数据
      types: 101, // 编码规划
      routeOptions: [], // 路段数据
      routeList: [], // 路线编码数据

      baseInfoFields: [],
      structureInfoFields: [],
      archivesInfoFields: [],
      ruleForm: {
        managementMaintenanceId: '',
        managementMaintenanceBranchId: '',
        id: ''
      },
    };
  },
  computed: {},
  watch: {
    formData: {
      handler(val) {
        if (Object.keys(val).length > 0) {
          this.ruleForm = JSON.parse(JSON.stringify(val));
        }
      },
      deep: true,
      immediate: true,
    },
    id: {
      handler(val) {
        this.getInfo(val)
      },
      deep: true,
    }
  },
  created() {
    this.init();
  },
  mounted() {
    this.formH = window.innerHeight - 460;
    window.addEventListener("resize", () => {
      this.formH = window.innerHeight - 460;
    });
  },
  methods: {
    init() {

      this.baseInfoFields = JSON.parse(
          JSON.stringify(formFields.baseInfo)
      )
      this.structureInfoFields = JSON.parse(JSON.stringify(formFields.structureInfo))
      this.archivesInfoFields = JSON.parse(
          JSON.stringify(formFields.archivesInfo
          )
      )
      if (Object.keys(this.formData).length > 0) {
        this.ruleForm = JSON.parse(JSON.stringify(this.formData));
        this.getInfo();
      }
      this.getDeptTree();
      this.getOptions();
    },
    getInfo(val) {
      if (val || this.ruleForm.id) {
        this.$modal.loading();
        getInfoById(val || this.ruleForm.id).then((res) => {
          if (res.code == 200) {
            this.ruleForm = res.data || {};
          }
        }).finally(() => {
          this.$modal.closeLoading();
        });
      }
    },
    onClose() {
      this.$emit("close");
    },
    /** 查询部门下拉树结构 */
    getDeptTree() {
      return deptTreeSelect({types: this.types}).then((response) => {
        this.deptOptions = response.data;
      });
    },
    getOptions() {
      listMaintenanceSectionAll({departmentId: null}).then((res) => {
        if (res.code == 200) {
          this.routeOptions = res.data;
        }
      });
      listAllRoute().then((res) => {
        if (res.code == 200) {
          this.routeList = res.data || [];
        }
      });
    },
  },
};
</script>

<style lang="scss" scoped>
@import "@/assets/styles/common.scss";

.culvert-info-edit {
  height: 75vh;
  padding: 10px;
  overflow: auto;
}
.culvert-one{
  ::v-deep .el-input__suffix {
    display: none;
  }

  ::v-deep .el-input.is-disabled .el-input__inner {
    background-color: white;
    border-color: #dfe4ed;
    color: #1d2129;
  }
}
</style>
