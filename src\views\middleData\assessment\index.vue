<template>
  <PageContainer>
    <template slot="search">
      <el-form :model="queryParams" ref="queryForm" :inline="true">
        <div style="display: flex; align-items: center; margin: 0">
          <el-form-item label="" prop="roadType" style="margin: 5px 10px 0 0">
            <CascadeSelection style="min-width: 192px" :form-data="queryParams" v-model="queryParams" types="201"
              multiple />
          </el-form-item>
          <!-- <RangeInput @startValue="
          (v) => {
            queryParams.startStake
              = v;
          }
        " @endValue="
          (v) => {
            queryParams.endStake = v;
          }
        " /> -->
          <el-form-item label="" prop="roadType" style="margin: 5px 10px 0 0">
            <el-select v-model="queryParams.roadType" placeholder="请选择公路类型" clearable>
              <el-option v-for="dict in dict.type.sys_road_type" :key="dict.value" :label="dict.label"
                :value="dict.value" />
            </el-select>
          </el-form-item>
          <el-form-item label="" prop="roadName" style="margin: 5px 10px 0 0">
            <el-input v-model="queryParams.roadName" placeholder="请输入公路名称" clearable
              @keyup.enter.native="handleQuery" />
          </el-form-item>
          <el-form-item label="" prop="roadType" style="margin: 5px 10px 0 0">
            <div style="min-width: 240px">
              <el-button type="primary" icon="el-icon-search" @click="handleQuery">搜索</el-button>
              <el-button icon="el-icon-refresh" @click="resetQuery">重置</el-button>
              <el-button v-show="!showSearch" icon="el-icon-arrow-down" circle @click="showSearch = true" />
              <el-button v-show="showSearch" icon="el-icon-arrow-up" style="
              color: #1890ff;
              border-color: #badeff;
              background-color: #e8f4ff;
            " circle @click="showSearch = false" />
            </div>
          </el-form-item>
        </div>
        <template v-if="showSearch">
          <el-form-item label="" prop="riskLevel" style="margin: 5px 10px 0 0">
            <el-select v-model="queryParams.riskLevel" placeholder="请选择风险等级" style="width: 100%;" clearable>
              <el-option v-for="dict in dict.type.geological_points_risk_grade" :key="dict.value" :label="dict.label"
                :value="parseInt(dict.value)"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="" prop="measuresTaken" style="margin: 5px 10px 0 0">
            <el-select v-model="queryParams.measuresTaken" placeholder="请选择是否已采取措施" style="width: 100%;" clearable>
              <el-option label="是" value="是"></el-option>
              <el-option label="否" value="否"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="" prop="riskRoadType" style="margin: 5px 10px 0 0">
            <el-select v-model="queryParams.riskRoadType" placeholder="请选择涉灾路段类型" style="width: 100%;" clearable>
              <el-option v-for="dict in dict.type.risk_road_type" :key="dict.value" :label="dict.label"
                :value="parseInt(dict.value)"></el-option>
            </el-select>
          </el-form-item>
        </template>
      </el-form>
    </template>
    <template slot="header">
      <!--操作按钮区开始-->
      <el-row :gutter="10" class="mb8">
        <el-col :span="1.5">
          <el-button type="primary" icon="el-icon-plus" size="mini" @click="handleAdd"
            v-hasPermi="['middleData:assessment:add']">新增</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button type="success" icon="el-icon-edit" size="mini" :disabled="single" @click="handleUpdate"
            v-hasPermi="['middleData:assessment:edit']">修改</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button type="danger" icon="el-icon-delete" size="mini" :disabled="multiple" @click="handleDelete"
            v-hasPermi="['middleData:assessment:remove']">删除</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button type="info" plain icon="el-icon-upload2" size="mini" @click="handleImport"
            v-hasPermi="['middleData:assessment:import']">导入</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button type="warning" icon="el-icon-download" size="mini" @click="handleExport"
            v-hasPermi="['middleData:assessment:export']">导出</el-button>
        </el-col>
        <right-toolbar :showSearch.sync="showSearch" @queryTable="getList" :columns="columns"></right-toolbar>
      </el-row>
      <!--操作按钮区结束-->
    </template>
    <template slot="body">
      <!--数据表格开始-->
      <el-table v-adjust-table ref="table" v-loading="loading" height="99%" border :data="assessmentList"
        @selection-change="handleSelectionChange" :row-style="rowStyle" @row-click="handleRowClick">
        <el-table-column type="selection" width="50" align="center" />
        <el-table-column fixed label="序号" type="index" width="50">
          <template v-slot="scope">
            {{ (scope.$index + (queryParams.pageNum - 1) * queryParams.pageSize) + 1 }}
          </template>
        </el-table-column>
        <el-table-column label="省份" align="center" prop="provinceName" v-if="cmpColumns('省份')" />
        <el-table-column label="市/州" align="center" prop="cityName" v-if="cmpColumns('市/州')" />
        <el-table-column label="管理处" align="center" prop="managementOffice" v-if="cmpColumns('管理处')" />
        <el-table-column label="养护路段" align="center" prop="maintenanceSection" v-if="cmpColumns('养护路段')" />
        <el-table-column label="公路类型" align="center" prop="roadType" v-if="cmpColumns('公路类型')">
          <template v-slot="scope">
            <dict-tag :options="dict.type.sys_road_type" :value="scope.row.roadType" />
          </template>
        </el-table-column>
        <el-table-column label="公路编号" align="center" prop="roadNumber" v-if="cmpColumns('公路编号')" />
        <el-table-column label="公路名称" align="center" prop="roadName" v-if="cmpColumns('公路名称')" />
        <el-table-column label="起点桩号" align="center" prop="startStake" v-if="cmpColumns('起点桩号')" />
        <el-table-column label="止点桩号" align="center" prop="endStake" v-if="cmpColumns('止点桩号')" />
        <el-table-column label="长度(km)" align="center" prop="length" v-if="cmpColumns('长度(km)')" />
        <el-table-column label="风险等级" align="center" prop="riskLevel" v-if="cmpColumns('风险等级')">
          <template v-slot="scope">
            <dict-tag :options="dict.type.geological_points_risk_grade" :value="scope.row.riskLevel" />
          </template>
        </el-table-column>
        <!-- <el-table-column label="路段风险描述" align="center" prop="riskDescription" /> -->
        <el-table-column label="是否已采取措施" align="center" prop="measuresTaken" v-if="cmpColumns('是否已采取措施')">
          <!-- <template v-slot="scope">
            <dict-tag :options="dict.type.sys_no_yes" :value="scope.row.measuresTaken" />
          </template> -->
        </el-table-column>
        <!-- <el-table-column label="已(拟)采取的措施" align="center" prop="measuresDetail" /> -->
        <el-table-column label="省级责任单位及人员" align="center" prop="provincialResponsible" show-overflow-tooltip
          v-if="cmpColumns('省级责任单位及人员')" />
        <el-table-column label="复核责任单位及人员" align="center" prop="reviewResponsible" show-overflow-tooltip
          v-if="cmpColumns('复核责任单位及人员')" />
        <el-table-column label="排查责任单位及人员" align="center" prop="inspectionResponsible" show-overflow-tooltip
          v-if="cmpColumns('排查责任单位及人员')" />
        <!-- <el-table-column label="备注" align="center" prop="remarks" /> -->
        <el-table-column label="涉灾路段类型" align="center" prop="riskRoadType" v-if="cmpColumns('涉灾路段类型')">
          <template v-slot="scope">
            <dict-tag :options="dict.type.risk_road_type" :value="scope.row.riskRoadType" />
          </template>
        </el-table-column>
        <el-table-column label="操作" fixed="right" align="center" width="160" class-name="small-padding fixed-width">
          <template slot-scope="scope" v-if="scope.row.userId !== 1">
            <el-button size="mini" type="text" icon="el-icon-edit" @click="handleUpdate(scope.row)"
              v-hasPermi="['middleData:assessment:edit']">修改</el-button>
            <el-button size="mini" type="text" icon="el-icon-delete" @click="handleDelete(scope.row)"
              v-hasPermi="['middleData:assessment:remove']">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize"
        @pagination="getList" />
      <!--数据表格结束-->
    </template>
    <!-- 添加或修改风险路段和山洪淹没路段对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="70%" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="160px">
        <div class="infoBox" style="margin-bottom: 20px;">
          <div class="infoTitle">
            基础信息
          </div>
          <el-row>
            <el-col :span="12">
              <el-form-item label="省份" prop="provinceName">
                <el-input v-model="form.provinceName" placeholder="请输入省份名称" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="市/州" prop="cityName">
                <el-select v-model="form.cityName" placeholder="请选择市/州" style="width: 100%;">
                  <el-option v-for="item in cityList" :key="item" :label="item" :value="item">
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row>
            <el-col :span="12">
              <el-form-item label="管理处" prop="managementOfficeId">
                <selectTree :key="'field2'" v-model="form.managementOfficeId" :deptType="100" :deptTypeList="[1, 3, 4]"
                  placeholder="请选择管理处" clearable filterable @node-selected="handleNodeSelected" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="养护路段" prop="maintenanceSectionId">
                <RoadSection v-model="form.maintenanceSectionId" :deptId="form.managementOfficeId"
                  @change="maintenanceSectionIdChange" placeholder="请选择养护路段" style="width: 100%;" />
              </el-form-item>
            </el-col>
          </el-row>

          <el-row>
            <el-col :span="12">
              <el-form-item label="公路编号" prop="roadNumber">
                <el-select v-model="form.roadNumber" placeholder="请选择公路编号" clearable filterable style="width: 100%"
                  collapse-tags @change="routeCodeChange">
                  <el-option v-for="item in routeList" :key="item.routeId" :label="item.routeName"
                    :value="item.routeCode">
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="公路名称" prop="roadName">
                <el-input v-model="form.roadName" placeholder="请输入公路名称" disabled />
              </el-form-item>
            </el-col>
          </el-row>

          <el-row>
            <el-col :span="12">
              <el-form-item label="公路类型" prop="roadType">
                <!-- <el-select v-model="form.roadType" placeholder="请选择公路类型" style="width: 100%;">
                  <el-option v-for="dict in dict.type.sys_road_type" :key="dict.value" :label="dict.label"
                    :value="parseInt(dict.value)"></el-option>
                </el-select> -->
                <el-radio-group v-model="form.roadType">
                  <el-radio v-for="dict in dict.type.sys_road_type" :label="parseInt(dict.value)">{{
                    dict.label }}</el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="长度(km)" prop="length">
                <el-input v-model="form.length" placeholder="请输入长度(km)" disabled />
              </el-form-item>
            </el-col>
          </el-row>

          <el-row>
            <el-col :span="12">
              <el-tooltip effect="dark" content="起点桩号(KXXX+XXX格式)" placement="top">
                <el-form-item label="起点桩号" prop="startStake">
                  <div class="input-row">
                    <span class="dwClass">K</span>
                    <el-input type="number" :min="0" v-model="form.beginMile1" @input="cmpLength" class="input-box" />
                    <span class="dwClass">-</span>
                    <el-input type="number" :min="0" v-model="form.beginMile2" @input="cmpLength" class="input-box" />
                  </div>
                </el-form-item>
              </el-tooltip>
            </el-col>
            <el-col :span="12">
              <el-tooltip effect="dark" content="终点桩号(KXXX+XXX格式)" placement="top">
                <el-form-item label="终点桩号" prop="endStake">
                  <div class="input-row">
                    <span class="dwClass">K</span>
                    <el-input type="number" :min="0" v-model="form.endMile1" @input="cmpLength" class="input-box" />
                    <span class="dwClass">-</span>
                    <el-input type="number" :min="0" v-model="form.endMile2" @input="cmpLength" class="input-box" />
                  </div>
                </el-form-item>
              </el-tooltip>
            </el-col>
          </el-row>
        </div>

        <div class="infoBox" style="margin-bottom: 20px;">
          <div class="infoTitle">
            风险评估
          </div>
          <el-row>
            <el-col :span="12">
              <el-form-item label="涉灾路段类型" prop="riskRoadType">
                <el-select v-model="form.riskRoadType" placeholder="请选择涉灾路段类型" style="width: 100%;">
                  <el-option v-for="dict in dict.type.risk_road_type" :key="dict.value" :label="dict.label"
                    :value="parseInt(dict.value)"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="风险等级" prop="riskLevel">
                <el-select v-model="form.riskLevel" placeholder="请选择风险等级" style="width: 100%;">
                  <el-option v-for="dict in dict.type.geological_points_risk_grade" :key="dict.value"
                    :label="dict.label" :value="parseInt(dict.value)"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row>
            <el-col :span="12">
              <el-form-item label="路段风险描述" prop="riskDescription">
                <el-input v-model="form.riskDescription" type="textarea" placeholder="请输入内容" />
              </el-form-item>
            </el-col>
          </el-row>

          <el-row>
            <el-col :span="12">
              <el-form-item label="是否已采取措施" prop="measuresTaken">
                <!-- <el-select v-model="form.measuresTaken" placeholder="请选择是否已采取措施" style="width: 100%;">
                  <el-option v-for="dict in dict.type.sys_no_yes" :key="dict.value" :label="dict.label"
                    :value="parseInt(dict.value)"></el-option>
                </el-select> -->
                <!-- <el-radio-group v-model="form.measuresTaken">
                  <el-radio v-for="dict in dict.type.sys_no_yes" :label="dict.value">{{
                    dict.label }}</el-radio>
                </el-radio-group> -->
                <el-radio-group v-model="form.measuresTaken">
                  <el-radio label="是">{{ '是' }}</el-radio>
                  <el-radio label="否">{{ '否' }}</el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="已(拟)采取的措施" prop="measuresDetail">
                <el-input v-model="form.measuresDetail" type="textarea" placeholder="请输入内容" />
              </el-form-item>
            </el-col>
          </el-row>

          <el-row>
            <el-col :span="12">
              <el-form-item label="省级责任单位及人员" prop="provincialResponsible">
                <el-input v-model="form.provincialResponsible" placeholder="请输入省级责任单位及人员" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="复核责任单位及人员" prop="reviewResponsible">
                <el-input v-model="form.reviewResponsible" placeholder="请输入复核责任单位及人员" />
              </el-form-item>
            </el-col>
          </el-row>

          <el-row>
            <el-col :span="12">
              <el-form-item label="排查责任单位及人员" prop="inspectionResponsible">
                <el-input v-model="form.inspectionResponsible" placeholder="请输入排查责任单位及人员" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="备注" prop="remarks">
                <el-input v-model="form.remarks" type="textarea" placeholder="请输入内容" />
              </el-form-item>
            </el-col>
          </el-row>
        </div>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 用户导入对话框 -->
    <el-dialog :title="upload.title" :visible.sync="upload.open" width="400px" append-to-body>
      <el-upload ref="upload" :limit="1" accept=".xlsx, .xls" :headers="upload.headers"
        :action="upload.url + '?updateSupport=' + upload.updateSupport" :disabled="upload.isUploading"
        :on-progress="handleFileUploadProgress" :on-success="handleFileSuccess" :auto-upload="false" drag>
        <i class="el-icon-upload"></i>
        <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
        <div class="el-upload__tip text-center" slot="tip">
          <div class="el-upload__tip" slot="tip">
            <el-checkbox v-model="upload.updateSupport" /> 是否更新已经存在的用户数据
          </div>
          <span>仅允许导入xls、xlsx格式文件。</span>
          <el-link type="primary" :underline="false" style="font-size:12px;vertical-align: baseline;"
            @click="importTemplate">下载模板</el-link>
        </div>
      </el-upload>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitFileForm">确 定</el-button>
        <el-button @click="upload.open = false">取 消</el-button>
      </div>
    </el-dialog>
  </PageContainer>
</template>

<script>
import { listAssessment, getAssessment, delAssessment, delAssessmentBatch, addAssessment, updateAssessment } from "@/api/middleData/assessment";
import { getToken } from "@/utils/auth";
import Treeselect from "@riophae/vue-treeselect";
import "@riophae/vue-treeselect/dist/vue-treeselect.css";
import selectTree from "@/components/DeptTmpl/selectTree.vue";
import RoadSection from "./components/index.vue"
import CascadeSelection from '@/components/CascadeSelectionManagementOffice/index.vue'; // 管养处/路段/路线
import RangeInput from "@/views/baseData/components/rangeInput/index.vue";

import { listByMaintenanceSectionId } from '@/api/baseData/common/routeLine'
import { listAllRoute } from '@/api/system/route'
export default {
  name: "Assessment",
  dicts: ['sys_road_type', 'risk_road_type', 'geological_points_risk_grade', 'sys_no_yes'],
  components: {
    selectTree,
    RoadSection,
    CascadeSelection,
    RangeInput,
  },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: false,
      dictType: [],
      // 日期范围
      dateRange: [],
      // 总条数
      total: 0,
      // 风险路段和山洪淹没路段表格数据
      assessmentList: null,
      // 弹出层标题
      title: "",
      // 部门树选项
      deptOptions: undefined,
      // 是否显示弹出层
      open: false,

      // 表单参数
      form: {
        provinceName: '云南省'
      },
      defaultProps: {
        children: "children",
        label: "label"
      },
      // 用户导入参数
      upload: {
        // 是否显示弹出层（用户导入）
        open: false,
        // 弹出层标题（用户导入）
        title: "",
        // 是否禁用上传
        isUploading: false,
        // 是否更新已经存在的用户数据
        updateSupport: 0,
        // 设置上传的请求头部
        headers: { Authorization: "Bearer " + getToken() },
        // 上传的地址
        url: process.env.VUE_APP_BASE_API + "/system/user/importData"
      },
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 50,
        provinceName: null,
        cityName: null,
        managementOffice: null,
        maintenanceSection: null,
        roadType: null,
        roadNumber: null,
        roadName: null,
        startStake: null,
        endStake: null,
        length: null,
        riskLevel: null,
        riskDescription: null,
        measuresTaken: null,
        measuresDetail: null,
        provincialResponsible: null,
        reviewResponsible: null,
        inspectionResponsible: null,
        remarks: null,
        managementOfficeId: null,
        maintenanceSectionId: null,
        shape: null,
        riskRoadType: null
      },
      // 列信息
      columns: [
        { key: 0, label: `省份`, visible: true },
        { key: 1, label: `市/州`, visible: true },
        { key: 2, label: `管理处`, visible: true },
        { key: 3, label: `养护路段`, visible: true },
        { key: 4, label: `公路类型`, visible: true },
        { key: 5, label: `公路编号`, visible: true },
        { key: 6, label: `公路名称`, visible: true },
        { key: 7, label: `起点桩号`, visible: true },
        { key: 8, label: `止点桩号`, visible: true },
        { key: 9, label: `长度(km)`, visible: true },
        { key: 10, label: `风险等级`, visible: true },
        { key: 11, label: `是否已采取措施`, visible: true },
        { key: 12, label: `省级责任单位及人员`, visible: true },
        { key: 13, label: `复核责任单位及人员`, visible: true },
        { key: 14, label: `排查责任单位及人员`, visible: true },
        { key: 15, label: `涉灾路段类型`, visible: true }
      ],
      // 表单校验
      rules: {


      },
      cityList: [
        '昆明市', '曲靖市', '玉溪市', '保山市', '昭通市', '丽江市', '普洱市', '临沧市',
        '楚雄彝族自治州', '红河哈尼族彝族自治州', '文山壮族苗族自治州',
        '西双版纳傣族自治州', '大理白族自治州', '德宏傣族景颇族自治州',
        '怒江傈僳族自治州', '迪庆藏族自治州'
      ],
      routeList: [],
    };
  },
  watch: {
    // 根据名称筛选部门树
  },
  created() {
    this.getList();
    // this.getDeptTree();
    // this.getConfigKey("sys.user.initPassword").then(response => {
    //   this.initPassword = response.msg;
    // });
  },
  computed: {
    cmpColumns() {
      return (val) => {
        let el = this.columns.find(item => item.label === val)
        if (el) {
          return el.visible
        } else {
          return true
        }
      }
    }
  },
  methods: {
    /** 查询用户列表 */
    getList() {
      this.loading = true;
      // this.queryParams.createTimee = this.dateRange[0]
      // this.queryParams.createTimes = this.dateRange[1]
      listAssessment(this.queryParams).then(response => {
        this.assessmentList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        provinceName: '云南省',
        cityName: null,
        managementOffice: null,
        maintenanceSection: null,
        roadType: null,
        roadNumber: null,
        roadName: null,
        startStake: null,
        endStake: null,
        length: null,
        riskLevel: null,
        riskDescription: null,
        measuresTaken: null,
        measuresDetail: null,
        provincialResponsible: null,
        reviewResponsible: null,
        inspectionResponsible: null,
        remarks: null,
        managementOfficeId: null,
        maintenanceSectionId: null,
        shape: null,
        riskRoadType: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dateRange = [];
      this.resetForm("queryForm");
      this.queryParams.managementMaintenanceBranchId = null
      this.queryParams.managementMaintenanceBranchIds = null
      this.queryParams.managementMaintenanceIds = null
      this.queryParams.routeCodes = null
      this.queryParams.maintenanceSectionId = null
      this.queryParams.startStake = null
      this.queryParams.endStake = null
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id);
      this.single = selection.length != 1;
      this.multiple = !selection.length;
    },
    // 表格点击勾选
    handleRowClick(row) {
      row.isSelected = !row.isSelected;
      this.$refs.table.toggleRowSelection(row);
    },
    // 勾选高亮
    rowStyle({ row, rowIndex }) {
      if (this.ids.includes(row.id)) {
        return { 'background-color': '#E1F0FF', color: '#333' }
      } else {
        return { 'background-color': '#fff', color: '#333' }
      }
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加风险路段和山洪淹没路段";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids;
      getAssessment(id).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改风险路段和山洪淹没路段";

        /* 起始桩号回显相关 */
        const beginMile1 = Math.floor(Number(this.form.startStake) / 1000);
        const beginMile2 = Number(this.form.startStake) % 1000;
        this.$set(this.form, 'beginMile1', beginMile1);
        this.$set(this.form, 'beginMile2', beginMile2);

        const endMile1 = Math.floor(Number(this.form.endStake) / 1000);
        const endMile2 = Number(this.form.endStake) % 1000;
        this.$set(this.form, 'endMile1', endMile1);
        this.$set(this.form, 'endMile2', endMile2);
        this.cmpLength()
      });

    },
    /** 提交按钮 */
    submitForm: function () {
      this.$refs["form"].validate(valid => {
        if (valid) {
          /* 起始桩号上传相关 */
          this.form.startStake = Number(this.form.beginMile1) * 1000 + Number(this.form.beginMile2) *
            1; // 起始里程
          this.form.endStake = Number(this.form.endMile1) * 1000 + Number(this.form.endMile2) *
            1; // 结束里程
          if (this.form.id != null) {
            updateAssessment(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addAssessment(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const id = row.id ? [row.id] : this.ids;
      this.$modal.confirm('是否确认删除风险路段和山洪淹没路段的数据项？').then(function () {
        return delAssessmentBatch(id);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => { });
    },

    /** 导出按钮操作 */
    handleExport() {

      this.download('middleData/roadRiskAssessment/export', {
        ...this.queryParams
      }, `assessment_${new Date().getTime()}.xlsx`)

    },
    /** 导入按钮操作 */
    handleImport() {
      this.upload.title = "用户导入";
      this.upload.open = true;
    },
    /** 下载模板操作 */
    importTemplate() {
      this.download('system/user/importTemplate', {
      }, `user_template.xlsx`)
    },
    // 文件上传中处理
    handleFileUploadProgress(event, file, fileList) {
      this.upload.isUploading = true;
    },
    // 文件上传成功处理
    handleFileSuccess(response, file, fileList) {
      this.upload.open = false;
      this.upload.isUploading = false;
      this.$refs.upload.clearFiles();
      this.$alert("<div style='overflow: auto;overflow-x: hidden;max-height: 70vh;padding: 10px 20px 0;'>" + response.msg + "</div>", "导入结果", { dangerouslyUseHTMLString: true });
      this.getList();
    },
    // 提交上传文件
    submitFileForm() {
      this.$refs.upload.submit();
    },
    /* 公路编号相关 */
    routeCodeChange(val) {
      if (val) {
        let el = this.routeList.find(item => val == item.routeCode)
        this.form.roadName = el?.routeName
      } else {
        this.form.roadName = null
      }
    },
    async maintenanceSectionIdChange(val) {
      if (this.form.maintenanceSectionId) {
        await this.managementChange(this.form.maintenanceSectionId)
      } else {
        this.listAllRoute()
      }
      this.form.maintenanceSection = val?.maintenanceSectionName
    },
    listAllRoute() {
      listAllRoute().then((res) => {
        if (res.code == 200) {
          this.routeList = res.data || [];
        }
      })
    },
    //路段下拉选改变事件
    managementChange(routeSectionId) {
      return new Promise((resolve, reject) => {
        this.queryParams.roadNumber = null;
        this.form.roadNumber = null;
        listByMaintenanceSectionId({ maintenanceSectionId: routeSectionId }).then((res) => {
          if (res.code == 200) {
            res.data.forEach((item) => {
              item.routeName = item.routeName + '(' + item.routeCode + ')';
            });
            this.routeList = res.data || [];
            resolve(this.routeList)
          } else {
            reject(res)
          }
        });
      })
    },
    isNumber(val) {
      if (val) {
        return typeof Number(val) === 'number'
      } else {
        return false
      }
    },
    /* 根据桩号计算长度 */
    cmpLength() {
      if (this.isNumber(this.form.endMile1) && this.isNumber(this.form.endMile2) && this.isNumber(this.form.beginMile1) && this.isNumber(this.form.beginMile2)) {
        this.form.length = Math.abs((Number(this.form.endMile1) * 1000 + Number(this.form.endMile2)) - (Number(this.form.beginMile1) * 1000 + Number(this.form.beginMile2)))
        this.form.length /= 1000
      } else {
        this.form.length = 0
      }
    },
    handleNodeSelected(node) {
      this.form.managementOffice = node.label; // 获取对应的 name
    },
  }
};
</script>
<style lang="scss" scoped>
.hasTagsView .app-main[data-v-078753dd] {
  background: #f5f7fa;
}

.infoBox {
  padding: 15px 15px 0 15px;
  box-sizing: border-box;
  border-radius: 6px;
  border: 1px solid #C4C4C4;
  position: relative;

  .infoTitle {
    user-select: none;
    position: absolute;
    top: 0;
    left: 0;
    padding: 0 10px;
    font-size: 14px;
    line-height: 14px;
    font-weight: bold;
    transform: translateX(15px) translateY(-50%);
    background-color: white;
  }

  .imgBox {
    height: auto;
    width: 100%;

    ::v-deep .el-card__body {
      width: 100%;
      display: flex;
      flex-direction: row;
      flex-wrap: wrap;
      align-content: flex-start;
    }

    .imgBoxCard {
      width: 100%;

      .cardMain {
        height: 260px;
        width: 33%;
        box-sizing: border-box;
        padding: 0 10px;
        display: flex;
        flex-direction: column;
        position: relative;

        .imgDeleteBtn {
          position: absolute;
          z-index: 1;
          top: 20%;
          right: 5%;
        }

        .imgTitle {
          height: 28px;
          width: 100%;
          font-size: 16px;
          font-weight: bold;
          white-space: nowrap;
          text-overflow: ellipsis;
          overflow: hidden;
        }

        .img {
          height: calc(100% - (28px + 28px));
          width: 100%;
          padding: 10px 0;
          position: relative;
          z-index: 0;
        }

        .footer {
          height: 28px;
          color: #888888;
          font-size: 14px;
        }
      }
    }
  }

  .noneBox {
    user-select: none;
    height: 200px;
    width: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    color: #888888;
  }
}

.dwClass {
  font-size: 12px;
  line-height: 3;
  color: #007aff;
  margin-left: 5px;
}

.input-row {
  display: flex;
  align-items: center;
}
</style>
