<template>
  <div class="body-mid">
    <Top class="top"/>
    <Bottom />
  </div>
</template>

<script>
import Top from "../mid/Top.vue"
import Bottom from "../mid/Bottom.vue"

export default {
  name: 'Mid-index',
  props: {},
  components: { Top, Bottom },
  data() {
    return {}
  },
  created() { },
  methods: {},
  computed: {},
  watch: {},
}
</script>

<style lang="scss" scoped>
.body-mid {
  position: relative;
  top: 0;
  height: 95%;
  width: 49%;
  display: flex;
  flex-direction: column;

  .top {
    flex: 1;
  }
}
</style>