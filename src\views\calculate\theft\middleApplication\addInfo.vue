<template>
  <div class="road-interflow-edit">
    <el-row :gutter="15">
      <el-row>
        <el-col :span="24">
          <el-form
            ref="queryForm"
            :inline="true"
            :model="queryParams"
            label-width="68px"
            size="mini"
          >
            <el-form-item>
              <el-input
                v-model="queryParams.projectName"
                placeholder="项目名称"
                style="width: 190px"
              ></el-input>
            </el-form-item>
            <el-form-item>
              <el-input
                v-model="queryParams.constructionName"
                placeholder="任务单名称"
                style="width: 190px"
              ></el-input>
            </el-form-item>
            <el-form-item>
              <el-input
                v-model="queryParams.constructionCode"
                placeholder="任务单编号"
                style="width: 190px"
              ></el-input>
            </el-form-item>
            <el-form-item>
                  <dict-select
                    v-model="queryParams.calcStatus"
                    clearable
                    placeholder="计量情况"
                    style="width: 240px"
                    type="testing_calc_status"
                  ></dict-select>
                </el-form-item>
                <el-form-item>
                <el-date-picker
                  v-model="completeDate"
                  type="daterange"
                  range-separator="至"
                  start-placeholder="完工起始时间"
                  end-placeholder="完工截止时间"
                  style="width: 240px"
                >
                </el-date-picker>
              </el-form-item>
            <el-form-item>
              <el-button
                icon="el-icon-search"
                size="mini"
                type="primary"
                @click="handleQuery"
                >搜索</el-button
              >
              <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
                >重置</el-button
              >
            </el-form-item>
          </el-form>
        </el-col>
      </el-row>
      <el-row :gutter="10" class="mb8">
        <el-col :span="1.5">
          <el-button
            icon="el-icon-view"
            size="mini"
            type="primary"
            @click="dialogVisible = true"
            >操作记录
          </el-button>
        </el-col>
      </el-row>
      <el-row>
        <div class="draggable">
          <el-table v-adjust-table
            ref="dataTable"
            v-loading="loading"
            :data="tableData"
            border
            height="600"
            highlight-current-row
            row-key="settleId"
            size="mini"
            stripe
            style="width: 100%"
            @row-click="handleClickRow"
            @selection-change="handleSelectionChange"
            @expand-change="loadData"
          >
            <el-table-column type="expand">
              <template slot-scope="props">
                <el-table v-adjust-table
                  :data="props.row.methodList"
                  style="width: 100%"
                  v-loading="methodLoading"
                >
                  <el-table-column prop="" align="center" label="">
                  </el-table-column>
                  <el-table-column
                    prop="schemeCode"
                    align="center"
                    label="子目号"
                  >
                  </el-table-column>
                  <el-table-column
                    prop="schemeName"
                    align="center"
                    label="养护方法"
                  >
                  </el-table-column>
                  <el-table-column
                    prop="calcDesc"
                    align="center"
                    label="计算式"
                  >
                  </el-table-column>
                  <el-table-column prop="num" align="center" label="方法数量">
                  </el-table-column>
                  <el-table-column prop="unit" align="center" label="方法单位">
                  </el-table-column>
                  <el-table-column prop="price" align="center" label="单价">
                  </el-table-column>
                  <el-table-column prop="amount" align="center" label="金额">
                  </el-table-column>
                  <el-table-column prop="remark" align="center" label="备注">
                  </el-table-column>
                  <el-table-column prop="status" align="center" label="状态">
                    <template slot-scope="scope">
                      <dict-tag
                        :options="dict.type.testing_calc_status"
                        :value="scope.row.status"
                      />
                    </template>
                  </el-table-column>
                </el-table>
              </template>
            </el-table-column>
            <el-table-column align="center" type="selection" width="50" />
            <el-table-column
              align="center"
              label="序号"
              type="index"
              width="50"
            />
            <template v-for="(column, index) in columns">
              <el-table-column
                v-if="column.visible"
                :label="column.label"
                :prop="column.field"
                :width="column.width"
                align="center" show-overflow-tooltip
              >
                <template slot-scope="scope">
                  <dict-tag
                    v-if="column.dict"
                    :options="dict.type[column.dict]"
                    :value="scope.row[column.field]"
                  />
                  <template v-else-if="column.slots">
                    <RenderDom
                      :index="index"
                      :render="column.render"
                      :row="scope.row"
                    />
                  </template>
                  <span v-else-if="column.isTime">{{
                    parseTime(scope.row[column.field], "{y}-{m}-{d}")
                  }}</span>
                  <span v-else>{{ scope.row[column.field] }}</span>
                </template>
              </el-table-column>
            </template>
          </el-table>
          <pagination
            v-show="total > 0"
            :limit.sync="queryParams.pageSize"
            :page.sync="queryParams.pageNum"
            :total="total"
            @pagination="handleQuery"
          />
        </div>
      </el-row>
      <el-col
        :span="24"
        style="text-align: right; padding-right: 7.5px; margin-top: 18px"
      >
        <el-button type="primary" @click="onSave">保 存</el-button>
        <el-button @click="onClose">退 出</el-button>
      </el-col>
    </el-row>
    <el-dialog
      :visible.sync="dialogVisible"
      append-to-body
      destroy-on-close
      modal-append-to-body
      title="事件信息"
      width="50%"
    >
      <event-tree-info></event-tree-info>
    </el-dialog>
  </div>
</template>
<script>
import {addDetail, listSettle,listMethodBySettleId } from "@/api/calculate/theft/middleApplication";
import EventTreeInfo from "@/views/dailyMaintenance/component/eventTreeInfo.vue";
import RoadSection from "@/views/baseData/components/roadSection/index.vue";
import RouteCodeSection from "@/components/RouteCodeSection/index.vue";

export default {
  dicts: [ "task_type", "testing_calc_status"],
  components: {
    RoadSection,
    EventTreeInfo,
    RouteCodeSection,
    RenderDom: {
      functional: true,
      props: {
        row: Object,
        index: Number,
        render: Function,
      },
      render(createElement, ctx) {
        const { row, index } = ctx.props;
        return ctx.props.render(row, index);
      },
    },
  },
  data() {
    return {
      columns: [
        { key: 0, width: 100, field: "projectName", label: `项目名称`, visible: true },
        {
          key: -1,
          width: 100,
          field: "constructionType",
          label: `任务单类型`,
          visible: true,
          dict: "task_type",
        },
        {
          key: 1,
          width: 100,
          field: "constructionName",
          label: `任务单名称`,
          visible: true,
        },
        {
          key: 2,
          width: 100,
          field: "constructionCode",
          label: `任务单编号`,
          visible: true,
        },
        {
          key: 3,
          width: 100,
          field: "maiSecName",
          label: `路段名称`,
          visible: true,
        },
        {
          key: 4,
          width: 100,
          field: "domainName",
          label: `管养单位`,
          visible: true,
        },
        {
          key: 5,
          width: 100,
          field: "conDomainName",
          label: `施工单位`,
          visible: true,
        },
        {
          key: 6,
          width: 100,
          field: "supDomainName",
          label: `监理单位`,
          visible: true,
        },
        {
          key: 7,
          width: 100,
          field: "endTime",
          label: `完工日期`,
          visible: true,
        },
        {
          key: 8,
          width: 100,
          field: "settleContent",
          label: `施工内容`,
          visible: true,
        },
        {
          key: 9,
          width: 100,
          field: "defLiaPer",
          label: `缺陷责任期`,
          visible: true,
        },
        {
          key: 10,
          width: 100,
          field: "sumFund",
          label: `金额`,
          visible: true,
        },
        {
          key: 10,
          width: 100,
          field: "residueFund",
          label: `未计量金额`,
          visible: true,
        },
        {
          key: 11,
          width: 100,
          field: "calcStatus",
          label: `计量情况`,
          visible: true,
          dict: "testing_calc_status",
        },
        {
          key: 12,
          width: 100,
          field: "addUnitPrice",
          label: `有无新增单价`,
          visible: true,
          slots: true,
          render: (row, index)=> {
            return (
              <span>{row.addUnitPrice == 1 ? '有':'无'}</span>
            )
          }
        },
      ],
      queryParams: {
        pageNum: 1,
        pageSize: 10,
      },
      tableData: [],
      selectIds: [],
      completeDate: [],
      total: 0,
      dialogVisible: false,
      loading: true,
      methodLoading: true,
    };
  },
  props: {
    maiSecId: {
      type: String,
      default: "",
    },
    calcId: {
      type: String,
      default: "",
    },
  },
  watch: {
    calcId: {
      handler(val) {
        if (val) {
          this.handleQuery();
        }
      },
    },
  },
  created() {
    this.handleQuery();
  },
  methods: {
    handleQuery() {
      this.loading = true;
      this.queryParams.calcId = this.calcId;
      if (this.completeDate.length > 1) {
        this.queryParams.startDate = this.completeDate[0]
        this.queryParams.endDate = this.completeDate[1]
      }
      listSettle(this.queryParams).then((res) => {
        this.tableData = res.rows;
        this.total = res.total;
        this.loading = false;
      });
    },
    resetQuery() {
      this.queryParams = {};
      this.handleQuery()
    },
    // 选中
    handleSelectionChange(e) {
      this.selectIds = e;
    },

    handleClickRow(e) {
      e.isSelected = !e.isSelected;
      this.$refs.dataTable.toggleRowSelection(e);
    },
    loadData(row) {
      this.methodLoading = true;
      listMethodBySettleId({ settleId: row.settleId }).then((res) => {
        this.$set(row, "methodList", res.rows);
        this.methodLoading = false;
      });
    },

    onSave() {
      const params = [];
      if (this.selectIds.length === 0) {
        this.$modal.msgWarning("没有选中的数据");
        return;
      }
      for (let i = 0; i < this.selectIds.length; i++) {
        params.push({
          calcId: this.calcId,
          settleId: this.selectIds[i].settleId,
        });
      }
      addDetail(params).then((res) => {
        this.$modal.msgSuccess("保存成功");
        this.onClose();
      });
    },
    onClose() {
      this.$emit("close");
    },
  },
};
</script>
<style lang="scss" scoped></style>
<style lang="scss" scoped>
@import "@/assets/styles/business.scss";
</style>
