<template>
  <div class="table-info-dialog">
    <Dialog title="详情" :show.sync="showVisible" :append="false" center
      :class="isBig ? (isHB ? 'bigScreenDialogHB' : 'bigScreenDialog') : ''" :width="isBig ? '35%' : '60%'"
      :ifHeader="healthMonitoring ? true : false">
      <template slot="header" v-if="healthMonitoring">
        <div class="table-buttonList">
          <div v-if="healthMonitoring.buttonName !== '实时监测'" class="table-button" @click="jumpClick(1)">监测大屏</div>
          <div v-if="!header" class="table-button" @click="jumpClick(2)">实时监测</div>
        </div>
      </template>
      <div class="table-info" v-if="!ifChart">
        <el-descriptions class="margin-top" :column="1" border v-if="!loading">
          <el-descriptions-item v-for="item in showHeaderList" :key="item.col">
            <template slot="label">
              <span
                v-if="otherConfig && otherConfig.writeable && (item.writeable || item.writeable == 'true') && item.writeRequired"
                style="color: red;">*</span>
              <span style="width: 100px;">{{ item.name }}</span>
            </template>
            <span v-if="item.type == 12">{{ stakeFilter(tableInfo[item.col]) }}</span>
            <template v-else-if="otherConfig && otherConfig.writeable && (item.writeable || item.writeable == 'true')">
              <el-input placeholder="请输入内容" v-model="form[item.col]" v-if="item.type === 2"
                @input="onInput($event, item.col)" />
              <el-input type="textarea" :rows="2" placeholder="请输入内容" v-model="form[item.col]" v-if="item.type === 3" />
              <!-- <el-radio-group v-model="form[item.col]" v-else-if="item.type === 4" :key="item.name + item.col" @change="onRadioChange($event, item.col)">
                <el-radio :label="dict.value" v-for="dict in dict.type[item.dictType]" :key="item.dictType + dict.value"
                  :value="dict.value">{{ dict.label }}</el-radio>
              </el-radio-group> -->
              <template v-else-if="item.type === 4">
                <el-select v-model="form[item.col]" :placeholder="'请选择'" clearable style="width: 100%;">
                  <el-option v-for="dict in dict.type[item.dictType]" :key="item.dictType + dict.value"
                    :label="dict.label" :value="dict.value"></el-option>
                </el-select>
              </template>
              <template v-else-if="item.type === 8">
                <!-- <ImageUpload :key="item.col" v-model="form[item.col]" :limit="1" :owner-id="form[item.col]" /> -->
                <file-upload :for-view="false" v-model="form[item.col]" :owner-id="form[item.col]" />
              </template>
              <template v-else-if="item.type === 13 || item.type === 14">
                <el-select v-model="form[item.col]" :placeholder="'请选择'" clearable filterable style="width: 100%;"
                  :multiple="item.type === 14 ? true : false" @change="onChange($event, item)" :key="item.col">
                  <el-option
                    v-for="item in item.managementMaintenanceFlag ? deptOptions : item.maintenanceSectionFlag ? routeOptions : item.options"
                    :key="item.value" :label="item.label" :value="item.value">
                  </el-option>
                </el-select>
              </template>
              <span v-if="item.writeRequired && item.required" style="color: red;">
                {{ item.type === 13 || item.type === 14 ? '请选择' : '请输入' }}
              </span>
            </template>
            <span v-else-if="item.type === 4">
              <dict-tag :options="dict.type[item.dictType]" :value="form[item.col]" />
            </span>
            <span v-else-if="item.managementMaintenanceFlag || item.maintenanceSectionFlag">
              {{ tableInfo[item.col.replace('id', 'name')] }}
            </span>
            <span v-else-if="item.type === 8 && tableInfo[item.col]">
              <!-- <ImagePreview :owner-id="tableInfo[item.col]" :key="item.col + new Date().getTime()" /> -->
              <file-upload :for-view="true" v-model="form[item.col]" :owner-id="form[item.col]" />
            </span>
            <span v-else-if="item.type === 11">
              <span>{{ item.dictData[tableInfo[item.col]] }}</span>
            </span>
            <span v-else-if="item.type === 2 && item.enumsType">
              <span>{{ tableInfo[item.col] == 1 ? '正在预警' : '正常' }}</span>
            </span>
            <span v-else-if="item.type && item.type == 7 && item.precise " :style="{ fontSize: isBig ? '0.4vw' : '' }">
              {{ tableInfo[item.col] | formatDate('YYYY年MM月DD日 hh:mm:ss') }}
            </span>
            <span v-else>{{ tableInfo[item.col]}}</span>
          </el-descriptions-item>
        </el-descriptions>

        <section v-if="otherConfig && otherConfig.writeable" class="sub-btn">
          <el-button type="primary" @click="onSub" v-loading="subLoading" :disabled="subLoading">提交</el-button>
        </section>
      </div>
      <div class="chart-info" v-else>
        <Echarts :option="option" v-if="option" height="45vh" style="width: 90%;" />
      </div>
    </Dialog>
    <div v-if="showHttp" class="fullscreen">
      <span>
        <img src="@/assets/map/quite.png" @click="showHttp = false" />
      </span>
      <iframe v-bind:src="iframeSrc" width="100%" height="100%" frameborder="0" scrolling="auto"></iframe>
    </div>
  </div>
</template>

<script>
import Echarts from './echarts.vue'
import Dialog from "@/components/Dialog/index.vue"
import ImagePreview from '@/components/ImagePreview/index.vue'
import { publicRequest } from '@/api/oneMap/tableInfo'
import fileUpload from "@/components/FileUpload/index.vue";

import { mapState } from 'vuex'
import cache from "@/plugins/cache";
import { getTreeStruct } from '@/api/tmpl';
import { getMaintenanceSectionListAll } from '@/api/baseData/common/routeLine';

export default {
  dicts: ['data_type', 'is_affects_road_safety', 'sys_yes_no', 'geological_points_risk_grade'],
  components: {
    Dialog,
    Echarts,
    ImagePreview,
    fileUpload
  },
  props: {
    show: {
      type: Boolean,
      default: false,
    },
    url: {
      type: String,
      default: ''
    },
    dataObj: {
      type: Object,
      default: ''
    },
    method: {
      type: String,
      default: ''
    },
    healthMonitoring: {
      type: Object,
      default: null
    },
    ifChart: {
      type: Boolean,
      default: false
    },
    isBig: {
      type: Boolean,
      default: false
    },
    header: {
      default: () => []
    }
  },
  data() {
    return {
      tableInfo: {},
      showBriage: false,
      showHeaderList: [],
      showHttp: false,
      option: null,
      iframeSrc: '',
      detlCondf: this.$store.state.map.detlCondf,
      form: {
        affects: null, // 是否影响道路安全
        remarks: '', // 备注
        photos: '', // 图片
        management_maintenance_id: '', // 维护管理id
        maintenance_section_id: '', // 维护管理段id
        investigation_anager: '', // 调查负责人
        investigation_names: '', // 调查人
        risk_grade: '', // 风险等级
        risk_remark: '', // 风险描述
        route_section_id: '', // 路段id
      },
      otherConfig: null,
      subLoading: false,
      loading: true,
      deptOptions: [],
      routeOptions: [],
      timer: null,
      isHB: false,
    }
  },
  computed: {
    ...mapState({
      tableHeader: state => state.map.tableHeader
    }),
    showVisible: {
      set(val) {
        this.$emit('update:show', val)
      },
      get(val) {
        return this.show
      },
    },
    tableHeaderList() {
      if (!this.tableHeader.length) return [];
      let notShapeArr
      if (this.header && this.header.length > 0) {
        notShapeArr = this.header.filter(v => v.col != 'shape' && v.columnType != 1);
      } else {
        notShapeArr = this.tableHeader.filter(v => v.col != 'shape' && v.columnType != 1);
      }
      // let notShapeArr = this.tableHeader.filter(v => v.col != 'shape' && v.columnType != 1);
      let isDetailArr = notShapeArr.filter(v => v.ifDetailShow);
      let isTableArr = notShapeArr.filter(v => v.ifTableShow != 0);
      let arr = [];
      if (isDetailArr && isDetailArr.length) {
        arr = isDetailArr;
      } else {
        arr = isTableArr;
      }
      let lastArr = arr.filter(v => v.col != 'id' && !v.name.includes('主键') && v.col != 'mainLength123') || [];
      this.form.id = this.dataObj.dataId;
      // 获取otherConfig
      let otherData = cache.session.getJSON("otherData");
      if (otherData) {
        let otherConfig = otherData.find(v => v.layerMenuSubId === this.dataObj.id)?.otherConfig || null;
        this.otherConfig = otherConfig ? JSON.parse(otherConfig) : null;
      }
      lastArr.forEach(item => {
        if ([2, 3, 4, 8, 13, 14].includes(item.type) && this.tableInfo[item.col] && item.writeable) {
          this.form[item.col] = this.tableInfo[item.col] || '';
        }
        if (item.writeRequired) {
          item.required = false;
        }
        if (item.type == 8) {
          console.log('form', this.form)
          this.form[item.col] = this.tableInfo[item.col] || this.generateUUID();
        }
      });
      let detlCondf = this.$store.state.map.detlCondf;
      if (detlCondf && detlCondf.writeable && detlCondf.writeUrl) {
        this.otherConfig = this.otherConfig || {};
        if (this.otherConfig.writeable) this.otherConfig.writeable = false;
        if (this.otherConfig.writeUrl) this.otherConfig.writeable = '';
        this.otherConfig.writeable = detlCondf.writeable || false;
        this.otherConfig.writeUrl = detlCondf.writeUrl || '';
      }
      this.$forceUpdate();
      return lastArr;
    },
  },
  watch: {
    url: {
      handler(val) {
        if (val) {
          this.isHB = this.$route.path === "/dataCenterMap" || this.$route.path === "/dataCenterMap?type=1" ? true : false
          this.getDeptList();
          this.getRoutes();
          this.getInfo()
        }
      },
      deep: true,
      immediate: true,
    }
  },
  // 销毁
  beforeDestroy() {
    this.timer && clearTimeout(this.timer);
  },
  methods: {
    getInfo() {
      this.loading = true;
      publicRequest(this.url, this.method || 'post', { ...this.dataObj }).then(res => {
        if (res.code === 200) {
          if (this.ifChart) {
            this.option = this.initOption(res.data)
            return
          }
          if (Object.prototype.toString.call(res.data) !== '[object Array]') {
            this.tableInfo = res.data || {}
          } else {
            this.tableInfo = res.data[0] || {}
          }
          // 健康监测按桥隧涵进行展示
          if (this.healthMonitoring && this.tableInfo.type && !this.healthMonitoring.buttonName) {
            let tempList = this.tableHeaderList
            if (this.tableInfo.type != '桥梁') {
              tempList = tempList.filter(item => item.name !== "孔跨" && item.name !== "主桥上部结构类型")
            }
            this.showHeaderList = tempList.filter(item => item.name !== "资产id")
          } else {
            this.showHeaderList = this.tableHeaderList
          }
        }
      }).finally(() => {
        this.loading = false;
      });
    },
    // 获取管理处数据
    async getDeptList() {
      getTreeStruct({ types: '201' }).then(res => {
        if (res.code === 200 && res.data) {
          this.deptOptions = res.data.map(v => {
            v.value = v.id;
            return v;
          }) || [];
        }
        this.$forceUpdate();
      }).catch(err => {
        reject(err)
      });
    },
    // 获取 路线数据
    async getRoutes(e = null) {
      getMaintenanceSectionListAll({ departmentIdList: e }).then((res) => {
        if (res.code == 200 && res.data) {
          this.routeOptions = res.data.map(v => {
            v.value = v.maintenanceSectionId;
            v.label = v.maintenanceSectionName;
            return v;
          }) || [];
        }
        this.$forceUpdate();
      }).catch(err => {
        reject(err)
      });
    },
    // 监听select
    onChange(e, item) {
      if (item.managementMaintenanceFlag) {
        this.getRoutes(e);
      }
      this.$forceUpdate();
    },
    stakeFilter(value) {
      if (!value) return "";
      if (
        typeof value == "string" &&
        (value.includes("k") || value.includes("K"))
      ) {
        return value;
      } else if (typeof value == "string" && value.includes("~")) {
        // 判断是否 ~连接
        let splitArr = value.split("~");
        return (
          this.stakeFormat(splitArr[0]) + " ~ " + this.stakeFormat(splitArr[1])
        );
      } else {
        return this.stakeFormat(value);
      }
    },
    safeDivide(a, b, decimalPlaces) {
      const factor = Math.pow(10, decimalPlaces);
      return (a * factor) / (b * factor);
    },
    safeMultiply(a, b) {
      const decimalA = (a.toString().split('.')[1] || '').length;
      const decimalB = (b.toString().split('.')[1] || '').length;
      const factor = Math.pow(10, decimalA + decimalB); // 放大倍数为两数小数位数之和
      return (a * factor) * (b * factor) / (factor * factor);
    },
    stakeFormat(value) {
      if (!value) return ''
      let prefix = parseInt(value / 1000)
      let val = ''
      if (prefix < 1) {
        val = 'k0+' + value
      } else {
        let str = this.safeDivide(value, 1000, 3) + ''
        const parts = str.split('.')
        const integerPart = parseInt(parts[0])
        const decimalPart = parts[1] ? parseFloat('0.' + parts[1]) : 0
        val = 'k' + integerPart + "+" + this.safeMultiply(decimalPart, 1000)
      }
      return val
    },
    jumpClick(type) {
      switch (type) {
        case 1:
          // if (document.fullscreenElement !== null) {
          //   this.showHttp = true
          //   this.iframeSrc = `https://jkjc.yciccloud.com:8000/singleBridgeNoToken?code=${this.tableInfo.code}`
          // } else {
          //   // window.location.href = `https://jkjc.yciccloud.com:8000/singleBridgeNoToken?code=${this.tableInfo.code}`;
          //   window.open(`https://jkjc.yciccloud.com:8000/singleBridgeNoToken?code=${this.tableInfo.code}`, '_blank')
          // }
          // 获取当前地址栏域名
          let serviceUrl = window.location.origin;
          // 拼接跳转地址
          let params = `id=${this.tableInfo.id}&type=${this.tableInfo.type}&code=${this.tableInfo.code}&asset_id=${this.tableInfo.asset_id}&openType=window`;
          // 加密参数
          params = encodeURIComponent(params);
          // 跳转地址
          let directUrl = serviceUrl + `/monitoringSystem/bigScreen?${params}`
          window.open(`${directUrl}`, '_blank')
          break
        case 2:
          if (this.healthMonitoring.buttonName !== '实时监测') {
            let serviceUrl2 = window.location.origin;
            let directUrl2 = serviceUrl2 + `/monitoringSystem/realTime?code=${this.tableInfo.code}`
            window.open(`${directUrl2}`, '_blank')
          } else {
            let serviceUrl2 = window.location.origin;
            let directUrl2 = serviceUrl2 + `/healthMonitoringRealTime/realTime?code=${this.tableInfo.node_id}`
            window.open(`${directUrl2}`, '_blank')
          }
          
        // this.$router.push(`/monitoringSystem/realTime?code=${this.tableInfo.code}`)
        // if (document.fullscreenElement !== null) {
        //   this.showHttp = true
        //   this.iframeSrc = `https://jkjc.yciccloud.com:8000/realtimenotoken?code=${this.tableInfo.code}`
        // } else {
        //   // window.location.href = `https://jkjc.yciccloud.com:8000/realtimenotoken?code=${this.tableInfo.code}`;
        //   window.open(`https://jkjc.yciccloud.com:8000/realtimenotoken?code=${this.tableInfo.code}`, '_blank')
        // }
        // break
      }
    },
    initOption(val) {
      const arr = val
      var Line = ["MQI", "PQI", "PCI", "RQI", "RDI"]
      let data1 = {
        mqi: [],
        pqi: [],
        pci: [],
        rqi: [],
        rdi: []
      }
      let l = ["mqi", "pqi", "pci", "rqi", "rdi"]
      var color = ['#a2e8b5', '#67d7ea', '#4e7bd0', '#ca2e84', '#ddb858']
      arr?.map((el, index) => {
        data1.mqi.push(el.mqi)
        data1.pqi.push(el.pqi)
        data1.pci.push(el.pci)
        data1.rqi.push(el.rqi)
        data1.rdi.push(el.rdi)
      })
      //数据处理
      var datas = []
      Line.map((item, index) => {
        datas.push(
          {
            symbolSize: 5,
            symbol: "circle",
            name: item,
            type: "line",
            color: color[index],
            data: data1[l[index]],
            lineStyle: {
              width: this.isBig ? 6 : 1,
            },
          }
        )
      })
      return {
        backgroundColor: '#05102d',
        grid: {
          left: '10%',
          top: '15%',
          bottom: '15%',
          right: '10%',
        },
        tooltip: {
          show: true,
          trigger: "axis",
        },
        legend: {
          data: Line,
          itemGap: this.isBig ? 130 : 50,
          textStyle: {
            color: "#fff",
            fontSize: this.isBig ? 40 : 14,
          },
        },
        yAxis: {
          name: '',
          type: 'value',
          splitLine: {
            show: false
          },
          axisLine: {
            show: true,
            lineStyle: {
              type: 'dashed',
              color: '#909399',
              width: this.isBig ? 6 : 1,
            }
          },
          axisTick: {
            show: false
          },
          axisLabel: {
            formatter: '{value}',
            color: '#fff',
            margin: this.isBig ? 40 : 10,
            fontSize: this.isBig ? 40 : 14,
          },
          splitArea: {
            show: false,
          },
        },
        xAxis: [
          {
            type: 'category',
            boundaryGap: false,
            axisTick: {
              show: true,
            },
            axisLine: {
              show: true,
              lineStyle: {
                color: '#909399',
                width: this.isBig ? 6 : 1,
                fontSize: this.isBig ? 40 : 14,
              }
            },
            axisLabel: {
              inside: false,
              margin: this.isBig ? 40 : 10,
              textStyle: {
                color: '#fff',// x轴颜色
                fontWeight: 'normal',

                fontSize: this.isBig ? 40 : 14,
                lineHeight: 22
              }

            },
            data: arr?.map(el => el.year),
          }
        ],
        series: datas,
      }
    },
    convertKeysToCamelCase(obj) {
      if (typeof obj !== 'object' || obj === null) {
        return obj; // 非对象直接返回
      }
      if (Array.isArray(obj)) {
        return obj.map(this.convertKeysToCamelCase); // 递归处理数组元素
      }
      const newObj = {};
      for (const key in obj) {
        if (Object.prototype.hasOwnProperty.call(obj, key)) {
          // 将下划线后的字母转为大写，并移除下划线
          const camelKey = key.replace(/_+(\w)/g, (_, char) => char.toUpperCase());
          newObj[camelKey] = this.convertKeysToCamelCase(obj[key]); // 递归处理属性值
        }
      }
      return newObj;
    },
    // 提交
    onSub() {
      if (!this.otherConfig || (this.otherConfig && !this.otherConfig.writeUrl)) return;
      if (this.form.photos && Object.prototype.toString.call(this.form.photos) === "[object Array]") {
        this.form.photos = this.form.photos.length ? this.form.photos[0] : '';
      }
      this.tableHeader.forEach(item => {
        if (item.writeRequired && !this.form[item['col']]) {
          item.required = true;
        } else {
          item.required = false;
        }
      })
      let isRequired = false;
      this.tableHeader.forEach(item => {
        if (item.required) {
          isRequired = true;
        }
      })
      this.$forceUpdate();
      if (isRequired) {
        this.$message.error('请填写必填项');
        return;
      }
      let obj = JSON.parse(JSON.stringify(this.form));
      let params = this.convertKeysToCamelCase(obj);
      publicRequest('/oneMap' + this.otherConfig.writeUrl, 'put', params).then(res => {
        if (res.code == 200) {
          this.$message.success('提交成功');
          this.$store.dispatch('map/getMapZoom', this.$store.state.map.mapZoom + 0.5);
          this.$emit('update:show', false);
          window.$Bus.$emit('refreshMap');
        }
      });
    },
    onRadioChange(e, col) {
      this.form[col] = e;
      this.$nextTick(() => {
        this.$forceUpdate();
      });
    },

    onInput(e, col) {
      if (this.timer) {
        clearTimeout(this.timer);
      }
      this.timer = setTimeout(() => {
        this.form[col] = e;
        this.$forceUpdate();
      }, 0);
    },
    generateUUID() {
      return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {
        var r = crypto.getRandomValues(new Uint8Array(1))[0] % 16 | 0;
        var v = c === 'x' ? r : (r & 0x3 | 0x8);
        return v.toString(16);
      }).slice(0, 20); // 截取前 20 位
    },
  }
}
</script>

<style lang="scss" scoped>
@import "@/assets/styles/utils.scss";

.fullscreen {
  position: absolute;
  top: 0;
  width: 100vw;
  height: 100vh;
  z-index: 100001;

  span {
    position: absolute;
    top: 0;
    left: 0;
    cursor: pointer;

    img {
      width: vwpx(100px);
      height: vwpx(100px);
      transform: rotate(180deg);
    }
  }
}

.chart-info {
  width: 100%;
  height: 50vh;
  display: flex;
  align-items: center;
  justify-content: center;
}

.table-buttonList {
  display: flex;
  position: absolute;
  right: vwpx(120px);
  top: 0;
  font-size: 1.5vh;

  .table-button {
    color: #0078FF;
    margin: 0 10px;
    cursor: pointer;
  }
}

.table-info-dialog {
  position: relative;

  .sub-btn {
    // position: absolute;
    // right: vwpx(20px);
    // bottom: vwpx(20px);
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-top: vwpx(20px);
  }
}

.table-info {
  max-height: 80vh;
  // overflow-y: auto;
  position: relative;
  padding-bottom: vwpx(20px);

  ::v-deep .el-input {
    .el-input__inner {
      background-color: rgba(1, 102, 254, 0.2);
      border: 1px solid #0166FE;
      color: #ffffff;
      font-size: vwpx(28px);
    }

    .el-input__inner::placeholder {
      color: #BBBBBB;
    }

    .el-input-group__append {
      background-color: rgba(1, 102, 254, 0.2);
      border: 1px solid #0166FE;
      color: #ffffff;
      border-left: none;
      padding: 0 vwpx(20px);
      cursor: pointer;
    }
  }
}


::v-deep .bigScreenDialog {

  .el-dialog .el-dialog__header {
    padding: 2% !important;
  }

  .el-dialog__title {

    font-size: 0.7vw !important;

  }

  .el-dialog__headerbtn {
    font-size: 1vw !important;
  }

  .el-descriptions-item__label.is-bordered-label {
    background-color: rgba(4, 17, 48, 0.6) !important;
    color: #ffffff;
    font-size: 0.5vw !important;
  }

  .el-descriptions-item__content {
    font-size: 0.5vw !important;
  }

  .el-dialog .el-dialog__body {
    padding: 2% !important;
  }

}

::v-deep .bigScreenDialogHB {

  .el-dialog .el-dialog__header {
    padding: 2% !important;
  }

  .el-dialog__title {

    font-size: 1.5vh !important;

  }

  .el-dialog__headerbtn {
    font-size: 0.7vw !important;
  }

  .el-descriptions-item__label.is-bordered-label {
    background-color: rgba(4, 17, 48, 0.6) !important;
    color: #ffffff;
    font-size: 1.4vh !important;
  }

  .el-descriptions-item__content {
    font-size: 1.4vh !important;
  }

  .el-dialog .el-dialog__body {
    padding: 2% !important;
  }

}

::v-deep .el-dialog {
  margin: 0 auto 0;
  background: rgba(4, 17, 48, 0.8);
  box-shadow: inset 0px 0px 10px 0px #3662EC;
  border-radius: 10px 10px 10px 10px;
  border: 1px solid #0687FF;

  .el-dialog__header {
    border-bottom: none;
    padding: 10px 15px !important;

    .el-dialog__title {
      color: #ffffff;
      font-size: vwpx(28px);
    }

    .el-dialog__headerbtn {
      color: #ffffff;
      top: vwpx(20px);
      font-size: vwpx(38px);
    }
  }

  .el-dialog__body {
    padding: 10px;

    &::-webkit-scrollbar {
      width: vwpx(16px);
    }

    &::-webkit-scrollbar-thumb {
      background: rgba(35, 134, 255, 0.3);
      border-radius: vwpx(8px);
    }

    &::-webkit-scrollbar-track {
      background: rgba(2, 10, 30, 0.8);
      border-radius: vwpx(8px);
    }
  }
}

::v-deep .el-descriptions {

  .el-descriptions__body {
    background-color: rgba(4, 17, 48, 0.6) !important;
    color: #ffffff;
  }

  .is-bordered .el-descriptions-item__cell {
    border: 1px solid rgba(1, 102, 254, 0.4);
  }

  .el-descriptions-item__label.is-bordered-label {
    background-color: rgba(4, 17, 48, 0.6) !important;
    color: #ffffff;
    font-size: 1.4vh;
    min-width: 100px
  }

  .el-descriptions-item__content {
    font-size: 1.3vh;
  }
}

::v-deep .el-descriptions-row {

  th,
  td {
    width: 50%;
  }
}
</style>