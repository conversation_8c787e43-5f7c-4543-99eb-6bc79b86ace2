<template>
  <div class="app-container maindiv">
    <el-row :gutter="20">
      <!--部门数据-->
      <el-col :span="relaNav ? 5 : 0" :xs="24" class="leftDiv">
        <!--折叠图标-->
        <div class="leftIcon" @click="relaNav = false">
          <span class="el-icon-caret-left"></span>
        </div>
        <div class="head-container">
          <el-input
            v-model="keyword"
            @change="handleSearch"
            placeholder="输入关键词检索"
            clearable
            size="small"
            prefix-icon="el-icon-search"
            style="margin-bottom: 20px"
          />
        </div>
        <div class="left-total">共 {{ leftTotal }} 条</div>
        <div class="head-container" style="width: 300px">
          <el-tree
            :data="filteredTreeData"
            :expand-on-click-node="false"
            :filter-node-method="filterNode"
            :default-expanded-keys="[1]"
            ref="tree"
            node-key="id"
            highlight-current
            @node-click="handleNodeClick"
          >
          </el-tree>
        </div>
      </el-col>
      <!--角色数据-->
      <el-col :span="relaNav ? 19 : 24" :xs="24">
        <!--展开图标-->
        <div class="rightIcon" @click="relaNav = true" v-show="!relaNav">
          <span class="el-icon-caret-right"></span>
        </div>
        <!--操作按钮区开始-->
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button
              type="primary"
              icon="el-icon-plus"
              size="mini"
              v-has-menu-permi="['contract:maiconunitrelation:add']"
              @click="openDetail"
            >新增
            </el-button
            >
          </el-col>
          <el-col :span="1.5">
            <el-button
              type="warning"
              icon="el-icon-download"
              size="mini"
              v-has-menu-permi="['contract:maiconunitrelation:export']"
              @click="exportList"
            >导出
            </el-button
            >
          </el-col>
          <right-toolbar
            :showSearch.sync="showSearch"
            @queryTable="handleQuery"
            :columns="columns"
          ></right-toolbar>
        </el-row>
        <el-row>
          <div class="draggable">
            <el-table v-adjust-table
              size="mini"
              style="width: 100%"
              v-loading="loading"
              border
              :data="tableData"
              row-key="id"
              ref="dataTable"
              stripe
              :height="
                showSearch ? 'calc(100vh - 330px)' : 'calc(100vh - 270px)'
              "
            >
              <el-table-column
                label="序号"
                align="center"
                type="index"
                width="50"
              />
              <el-table-column label="管养单位" align="center" prop="maiDomainName" v-if="columns[0].visible"/>
              <el-table-column label="施工单位" align="center" prop="conDomainName" v-if="columns[1].visible"/>
              <el-table-column label="路段" align="center" prop="maiSecName" v-if="columns[2].visible"/>
              <el-table-column label="类型" align="center" prop="type" v-if="columns[3].visible">
                <template slot-scope="scope">
                  <dict-tag :options="dict.type.maiconunitrelation_type" :value="scope.row.type"/>
                </template>
              </el-table-column>
              <el-table-column label="备注" align="center" prop="remark" v-if="columns[4].visible"/>
              <el-table-column
                label="操作"
                fixed="right"
                align="center"
                width="160"
                class-name="small-padding fixed-width"
              >
                <template slot-scope="scope">
                  <el-button
                    size="mini"
                    type="text"
                    v-has-menu-permi="['contract:maiconunitrelation:edit']"
                    icon="el-icon-edit"
                    @click="handleUpdate(scope.row)"
                  >修改
                  </el-button
                  >
                  <el-button
                    size="mini"
                    type="text"
                    v-has-menu-permi="['contract:maiconunitrelation:delete']"
                    icon="el-icon-delete"
                    @click="handleDelete(scope.row)"
                  >删除
                  </el-button
                  >
                </template>
              </el-table-column>
            </el-table>
            <pagination
              v-show="total > 0"
              :total="total"
              :page.sync="queryParams.pageNum"
              :limit.sync="queryParams.pageSize"
              @pagination="handleQuery"
            />
          </div>
        </el-row>
      </el-col>
    </el-row>
    <el-dialog title="新增管养施工关系" :destroy-on-close="true" :append-to-body="true" :visible.sync="relaFlag" width="70%">
      <div class="road-interflow-edit" v-loading="loading">
        <el-row :gutter="15">
          <el-form
            ref="elForm"
            :model="formData"
            :rules="rules"
            size="medium"
            label-width="100px"
          >
            <el-form-item label="管养单位" prop="mobile">
              <el-input
                v-model="formData.maiDomainName"
                placeholder="请输入管养单位"
                :disabled="true"
                :maxlength="11"
                show-word-limit
                clearable
                prefix-icon="el-icon-mobile"
                :style="{ width: '100%' }"
              ></el-input>
            </el-form-item>
            <el-form-item label="施工单位" prop="conDomainId">
              <selectTree
                :style="{ width: '100%' }"
                v-model="formData.conDomainId"
                :dept-type="100"
                :expandAll="false"
                placeholder="施工单位"
                clearable
                filterable
              />
            </el-form-item>
            <el-form-item label="养护路段" prop="maiSecArr">
              <el-select v-model="formData.maiSecArr" multiple filterable placeholder="请选择养护路段" clearable :style="{ width: '100%' }" @change="$forceUpdate()">
                <el-option
                  v-for="item in maintenanceSectionList"
                  :key="item.maintenanceSectionName"
                  :label="item.maintenanceSectionName"
                  :value="item.maintenanceSectionName"
                >
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="类型" prop="type">
              <DictSelect
                v-model="formData.type"
                :type="'maiconunitrelation_type'"
                :placeholder="'类型'"
                clearable
              ></DictSelect>
            </el-form-item>
            <el-form-item label="备注" prop="remark">
              <el-input
                v-model="formData.remark"
                placeholder="请输入备注"
                clearable
                :style="{ width: '100%' }"
              >
              </el-input>
            </el-form-item>
          </el-form>
        </el-row>
        <div style="text-align: right; margin-top: 20px">
          <el-button type="primary" size="mini" @click="handleSave"
          >保存
          </el-button
          >
          <el-button size="mini" @click="relaFlag = false">退出</el-button>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import Sortable from 'sortablejs'
import {deptTreeSelect, getTreeStruct} from '@/api/tmpl.js'
import {addMgmtCOnstRela, getList, editMgmtConstRela, deleteMgmtConstRela} from '@/api/contract/mgmtConstRela'
import { listMaintenanceSectionAll } from '@/api/system/maintenanceSection'
import selectTree from '@/components/DeptTmpl/selectTree'

export default {
  name: 'MgmtConstRela',
  components: {selectTree},
  props: [],
  dicts: ['maiconunitrelation_type'],
  data() {
    return {
      leftTotal: 1,
      showSearch: false,
      loading: false,
      columns: [
        {key: 0, label: `管养单位`, visible: true},
        {key: 1, label: `施工单位`, visible: true},
        {key: 2, label: `路段`, visible: true},
        {key: 3, label: `类型`, visible: true},
        {key: 4, label: `备注`, visible: true}
      ],
      tableData: [],
      // 左侧组织树
      relaNav: true,
      keyword: '',
      relaOptions: [],
      filteredTreeData: [],
      relaFlag: false,
      formData: {
        maiDomainName: '',
        maiDomainId: ''
      },
      total: 0,
      queryParams: {
        pageNum: 1,
        pageSize: 10
      },
      // 养护路段
      maintenanceSectionList: [],
      rules: {
        conDomainId: [
          {
            required: true,
            message: '请选择施工单位',
            trigger: 'change'
          }
        ],
        maiSecArr: [
          {
            required: true,
            message: '请选择养护路段',
            trigger: 'change'
          }
        ],
        type: [
          {
            required: true,
            message: '请选择类型',
            trigger: 'blur'
          }
        ]
      }
    }
  },
  computed: {},
  watch: {},
  created() {
    this.getDeptTree()
    this.getMaintenanceSection()
    this.handleQuery()
  },
  mounted() {
  },
  methods: {
    // 查询
    handleQuery() {
      this.loading = true
      this.queryParams.maiDomainId = parseInt(this.formData.maiDomainId)
      getList(this.queryParams).then(res => {
        this.tableData = res.rows
        this.loading = false
        this.total = res.total
      })
    },
    // 修改
    handleUpdate(e) {
      this.formData = JSON.parse(JSON.stringify(e))
      this.formData.maiSecArr = this.formData.maiSecName ? this.formData.maiSecName.split(',') : []
      this.formData.conDomainId = String(this.formData.conDomainId)
      this.formData.type = String(this.formData.type)
      this.relaFlag = true
    },
    // 删除
    handleDelete(e) {
      this.$modal.confirm('是否确认删除').then(() => {
        this.loading = true
        deleteMgmtConstRela(e.id).then(res => {
          this.$modal.msgSuccess('删除成功');
          this.handleQuery()
        })
      })
    },
    // 新增
    openDetail() {
      if (!this.formData.maiDomainId) {
        this.$modal.msgError('请先选择管养单位！')
        return
      }
      const maiDomainName = this.formData.maiDomainName
      const maiDomainId = this.formData.maiDomainId
      this.formData = {
        maiDomainName: maiDomainName,
        maiDomainId: maiDomainId
      }
      this.relaFlag = true
    },
    // 保存
    handleSave() {
      this.$refs["elForm"].validate((valid) => {
        if (!valid) return;
        this.formData.maiSecName =  this.formData.maiSecArr ?  this.formData.maiSecArr.join(',') : ''
        if (this.formData.id) {
          editMgmtConstRela(this.formData).then(res => {
            this.$modal.msgSuccess('保存成功')
          })
        } else {
          addMgmtCOnstRela(this.formData).then(res => {
            this.$modal.msgSuccess('保存成功')
          })
        }
        this.handleQuery()
        this.relaFlag = false
      });
    },
    // 导出清单按钮
    exportList() {
      this.download(
        'manager/maiconunitrelation/export',
        {maiDomainId: parseInt(this.formData.maiDomainId)},
        `maiconunitrelation_${new Date().getTime()}.xlsx`,
        {
          headers: { 'Content-Type': 'application/json;' },
          parameterType: 'body'
        }
      )
    },
    getMaintenanceSection() {
      listMaintenanceSectionAll({ departmentId: this.formData.maiDomainId }).then(res => {
        this.maintenanceSectionList = res.data
      })
    },
    // 查询部门下拉树结构
    getDeptTree() {
      let deptType ={types:100, deptTypeList: [1,3,4,6,8]};

      deptTreeSelect(deptType).then(response => {
        this.relaOptions = response.data;
        this.filteredTreeData = [...this.relaOptions]
      });
    },
    // 关键词检索
    handleSearch() {
      const keyword = this.keyword.toLowerCase();
      this.filteredTreeData = this.relaOptions.filter(node => this.filterNode(node, keyword));
    },
    filterNode(node, keyword) {
      if (node.label.indexOf(keyword) != -1) {
        return true;
      }
      if (node.children) {
        return node.children.some(childNode => this.filterNode(childNode, keyword));
      }
      return false;
    },
    handleNodeClick(e) {
      this.formData.maiDomainName = e.label
      this.formData.maiDomainId = e.id
      this.getMaintenanceSection()
      this.handleQuery()
    }
  }
}
</script>

<style lang="scss" scoped>
.leftDiv {
  border-right: 1px solid #d8dce5;
  min-height: calc(100vh - 100px);
  overflow-y: auto;
  height: calc(80vh - 150px);
  position: relative;
  top: -20px;
  padding-top: 10px;
  background-color: white;
}

.leftIcon {
  border: 1px solid #dcdfe6;
  border-radius: 8px;
  width: 16px;
  height: 50px;
  line-height: 50px;
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
  position: absolute;
  right: 0;
  top: 300px;
  z-index: 2;
}

.leftIcon:hover {
  background-color: #dcdfe6;
}

.rightIcon {
  border: 1px solid #dcdfe6;
  border-radius: 8px;
  width: 16px;
  height: 50px;
  line-height: 50px;
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
  position: absolute;
  left: -10px;
  top: 280px;
  z-index: 10;
  background: white;
}

.rightIcon:hover {
  background-color: #dcdfe6;
}

.left-total {
  font-size: 13px;
  line-height: 28px;
  color: #606266;
  position: absolute;
  bottom: 10px;
  right: 10px;
}
</style>
<style lang="scss" scoped>
@import "@/assets/styles/business.scss";
</style>
