<template>
  <div class="app-container">
    <el-row :gutter="20">

      <!--筛选区开始-->
      <el-col :span="24" :xs="24">
        <el-row>
          <el-col :span="24">
            <el-form ref="queryForm" :inline="true" :model="queryParams" label-width="68px" size="small">
              <el-form-item label="" prop="year">
                <el-date-picker
                    v-model="queryParams.year"
                    format="yyyy"
                    placeholder="年份"
                    style="width: 240px"
                    type="year"
                    value-format="yyyy">
                </el-date-picker>
              </el-form-item>
              <el-form-item>
                <el-button icon="el-icon-search" size="mini" type="primary" @click="handleQuery">搜索</el-button>
                <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
              </el-form-item>
            </el-form>
            <!--默认折叠-->
          </el-col>
        </el-row>
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button
                icon="el-icon-plus"
                size="mini"
                type="primary"
                v-has-menu-permi="['calcdaliy:adjust:add']"
                @click="handleAdd"
            >新增
            </el-button>
          </el-col>
          <right-toolbar
              :columns="columns"
              :showSearch.sync="showSearch"
              @queryTable="handleQuery"
          ></right-toolbar>
        </el-row>
        <!--筛选区结束-->
        <!--数据表格开始-->
        <div class="tableDiv">
          <el-table v-adjust-table v-loading="loading" :data="dataList"
                    :height="showSearch ? 'calc(100vh - 320px)' : 'calc(100vh - 260px)'"
                    border size="mini" style="width: 100%">
            <el-table-column align="center" fixed label="序号" type="index" width="100"></el-table-column>
            <template v-for="(column,index) in columns">
              <el-table-column v-if="column.visible"
                               :label="column.label"
                               :prop="column.field"
                               align="center">
                <template slot-scope="scope">
                  <dict-tag v-if="column.dict" :options="dict.type[column.dict]" :value="scope.row[column.field]"/>
                  <template v-else-if="column.slots">
                    <RenderDom :index="index" :render="column.render" :row="scope.row"/>
                  </template>
                  <span v-else-if="column.isTime">{{ parseTime(scope.row[column.field], '{y}-{m}-{d}') }}</span>
                    <span v-else>{{ scope.row[column.field] }}</span>
                </template>
              </el-table-column>
            </template>
            <el-table-column
                label="操作"
                fixed="right"
                align="center"
                width="300"
                class-name="small-padding fixed-width"
            >
              <template slot-scope="scope">
                <el-button
                    size="mini"
                    type="text"
                    icon="el-icon-delete"
                    v-has-menu-permi="['calcdaliy:number:edit']"
                    @click="handleDelete(scope.row)"
                >删除
                </el-button>
                <el-button
                    size="mini"
                    type="text"
                    icon="el-icon-turn-off"
                    v-if="scope.row.isOpen == 0"
                    v-has-menu-permi="['calcdaliy:number:edit']"
                    @click="handleChange(scope.row, 'isOpen')"
                >开启上报
                </el-button>
                <el-button
                    size="mini"
                    type="text"
                    icon="el-icon-open"
                    v-else
                    v-has-menu-permi="['calcdaliy:number:edit']"
                    @click="handleChange(scope.row, 'isOpen')"
                >关闭上报
                </el-button>
              </template>
            </el-table-column>
          </el-table>

          <pagination
              v-show="total>0"
              :limit.sync="queryParams.pageSize"
              :page.sync="queryParams.pageNum"
              :total="total"
              @pagination="handleQuery"
          />
        </div>
        <!--数据表格结束-->
      </el-col>
    </el-row>
    <el-dialog :visible.sync="open" append-to-body destroy-on-close title="新增" width="80%">
      <el-form ref="elForm" :model="formData" :rules="rules" label-width="100px" size="medium">
        <el-form-item label="年份" prop="year">
          <el-date-picker
              v-model="formData.year"
              :style="{width: '100%'}"
              format="yyyy"
              placeholder="年份"
              type="year"
              value-format="yyyy">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="期数" prop="number">
          <el-input v-model="formData.number" type="number" min="1" max="9999" :style="{width: '100%'}"></el-input>
        </el-form-item>
        <el-form-item label="上报开关" prop="isOpen">
          <el-select v-model="formData.isOpen" :style="{width: '100%'}" clearable placeholder="请输入上报开关">
            <el-option v-for="(item, index) in options" :key="index" :disabled="item.disabled"
                       :label="item.label" :value="item.value"></el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <div style="text-align: right">
        <el-button @click="close">取消</el-button>
        <el-button type="primary" @click="handleConfirm">确定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  addCalcTCNumber, deleteCalcTCNumber,
  queryCalcTCNumberList,
  toggleCalcTCNumber
} from "@/api/dailyMaintenance/metering/meteringSwitch";

export default {
  name: "MeteringSwitch",
  components: {
    RenderDom: {
      functional: true,
      props: {
        row: Object,
        index: Number,
        render: Function
      },
      render(createElement, ctx) {
        const {row, index} = ctx.props
        return ctx.props.render(row, index)
      }
    }
  },
  data() {
    return {
      // 遮罩层
      loading: false,
      // 显示搜索条件
      showSearch: false,
      // 总条数
      total: 0,
      dataList: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 50,
      },
      open: false,
      // 列信息
      columns: [
        {key: 0, field: 'year', label: `年份`, visible: true},
        {key: 1, field: 'number', label: `期数`, visible: true},
        {key: 2, field: 'name', label: `期数名称`, visible: true},
        {
          key: 3, field: 'isOpen', label: `开关`, visible: true, slots: true, render: (row, index) => {
            if (row.isOpen === 0) {
              return (
                  <el-tag type="danger">关闭</el-tag>
              )
            } else {
              return (
                  <el-tag type="success">开启</el-tag>
              )
            }
          }
        }
      ],
      formData: {
        isOpen: 0,
        isReport: 0,
      },
      rules: {
        year: [{
          required: true,
          message: '请输入年份',
          trigger: 'change'
        }],
        isOpen: [{
          required: true,
          message: '请输入调整开关',
          trigger: 'change'
        }],
        number: [{
          required: true,
          message: '请输入版本号',
          trigger: 'blur'
        }],
      },
      options: [{
        "label": "关闭",
        "value": 0
      }, {
        "label": "开启",
        "value": 1
      }],
    };
  },
  watch: {
    // 根据名称筛选部门树
  },
  created() {
    this.handleQuery()
  },
  methods: {
    /** 重置按钮操作 */
    resetQuery() {
      this.queryParams = {
        pageNum: 1,
        pageSize: 50,
      }
      this.handleQuery()
    },
    handleQuery() {
      this.loading = true
      queryCalcTCNumberList(this.queryParams).then(res => {
        this.dataList = res.rows
        this.total = res.total
        this.loading = false
      })
    },
    handleAdd() {
      this.formData = {
        isOpen: 0,
        isReport: 0
      }
      this.open = true
    },
    handleConfirm() {
      this.$refs.elForm.validate(valid => {
        if (!valid) return
        this.loading = true
        addCalcTCNumber(this.formData).then(res => {
          if (res.code === 200) {
            this.$modal.msgSuccess("新增成功")
            this.handleQuery()
          } else {
            this.loading = false
            this.$modal.msgError(res.msg)
          }
          this.close()
        })
      })
    },
    handleDelete(row) {
      this.$modal.confirm("是否确认删除").then(() => {
        this.loading = true
        deleteCalcTCNumber(row.id).then(res => {
          this.$modal.msgSuccess("删除成功")
          this.handleQuery()
        })
      });
    },
    handleChange(row, column) {
      row[column] = row[column] === 1 ? 0 : 1
      this.loading = true
      toggleCalcTCNumber(row).then(res => {
        if (res.code === 200) {
          this.$modal.msgSuccess("更新成功")
          this.handleQuery()
        } else {
          this.loading = false
          this.$modal.msgError(res.msg)
        }
      })
    },
    close() {
      this.open = false
    },
  }
};
</script>
<style>
</style>
<style lang="scss" scoped>
@import "@/assets/styles/business.scss";
</style>
