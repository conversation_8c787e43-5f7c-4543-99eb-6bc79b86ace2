// 行政识别数据
const baseInfoData = [
  {
    label: '路线编码',
    prop: 'routeCode',
    type: 'input',
    disabled: true,
    span:12,
  },

  {
    label:'路线名称',
    prop: 'routeName',
    type: 'input',
    span:12,
    disabled: true,
  },
  {
    label:"调查方向",
    prop:"direction",
    type:"select",
    span:12,
    dict: 'sys_route_direction',
    disabled: true,
  },
  {
    label: '养护单位',
    prop: 'managementMaintenanceId',
    placeholder: '请选择养护单位(管理处)',
    type: 'select',
    disabled: true,
    deptType: 201,
    rules: [{ required: true, message: '请选择养护单位(管理处)', trigger: 'change' }]
  },
  {
    label: '路段名称',
    prop: 'maintenanceSectionId',
    placeholder: '请选择路段名称',
    type: 'select',
    rules: [{ required: true, message: '请选择路段名称', trigger: 'change' }],
    options: [],
    disabled: true,
    api: 'maintenanceSectionList',
    optionLabel: 'maintenanceSectionName',
    optionValue: 'maintenanceSectionId',
    disabledFieds: 'managementMaintenanceId'
  },
  // {
  //   label:"调查方向",
  //   prop:"direction",
  //   type:"select",
  //   span:12,
  //   dict: 'sys_route_direction',
  //   disabled: true,
  // },

  {
    label:"具体方向",
    prop:"specificDirection",
    type:"input",
    span:12,
    disabled: true,
  },
  {
    label:"路面类型",
    prop:"pavType",
    type:"select",
    span:12,
    dict:'sys_surface_type',
    disabled: true,
  },
  {
    label:"起点桩号",
    prop:"startStake",
    type:"input",
    disabled: true,
    span:12,
  },
  {
    label:"终点桩号",
    prop:"endStake",
    type:"input",
    disabled: true,
    span:12,
  }
  ,
  {
    label:"项目名称",
    prop:"project",
    type:"input",
    disabled: true,
    span:12,
    rules: [{ required: true, message: '请选择项目', trigger: 'change' }],
  },
  {
    label:"年度",
    prop:"year",
    type:"year",
    disabled: true,
    span:12,
    rules: [{ required: true, message: '请选择年度', trigger: 'change' }],
  },
  {
    label:"检测日期",
    prop:"checkDate",
    type:"date",
    span:12,
    rules: [{ required: true, message: '请选择年度', trigger: 'change' }],
  },
  {
    label:"检测单位",
    prop:"detectionUnit",
    type:"input",
    span:12,
    rules: [{ required: true, message: '请输入检测单位', trigger: 'change' }],
  },
  {
    label:"车道",
    prop:"lane",
    dict: 'lane',
    type:"select",
    span:12,
    rules: [{ required: true, message: '请输入车道', trigger: 'change' }],
  },
  {
    label:"MQI(公路技术状况指数)",
    prop:"mqi",
    type:"inputNumber",
    span:12,
    rules: [{ required: true, message: '请输入MQI', trigger: 'change' }],
  },
  // {
  //   label:"MQI等级",
  //   prop:"mqil",
  //   type:"input",
  //   span:12,
  //   rules: [{ required: true, message: '请输入MQI等级', trigger: 'change' }],
  // },
  {
    label:"PBI(路面跳车指数)",
    prop:"pbi",
    type:"inputNumber",
    span:12,
    rules: [{ required: true, message: '请输入PBI(路面跳车指数)', trigger: 'change' }],
  },
  {
    label:"PCI(路面损坏状况指数)",
    prop:"pci",
    type:"inputNumber",
    span:12,
    rules: [{ required: true, message: '请输入PCI(路面损坏状况指数)', trigger: 'change' }],
  },
  {
    label:"PQI(路面技术状况指数)",
    prop:"pqi",
    type:"inputNumber",
    span:12,
    rules: [{ required: true, message: '请输入PQI(路面技术状况指数)', trigger: 'change' }],
  },
  // {
  //   label:"PQI等级",
  //   prop:"pqil",
  //   type:"input",
  //   span:12,
  //   rules: [{ required: true, message: '请输入PQI等级', trigger: 'change' }],
  // },
  {
    label:"PSSI(路面结构强度指数)",
    prop:"pssi",
    type:"inputNumber",
    span:12,
    rules: [{ required: true, message: '请输入PSSI(路面结构强度指数)', trigger: 'change' }],
  },
  {
    label:"PWI(路面磨耗指数)",
    prop:"pwi",
    type:"inputNumber",
    span:12,
    rules: [{ required: true, message: '请输入PWI(路面磨耗指数)', trigger: 'change' }],
  },
  {
    label:"RDI(路面车辙深度指数)",
    prop:"rdi",
    type:"inputNumber",
    span:12,
    rules: [{ required: true, message: '请输入RDI(路面车辙深度指数)', trigger: 'change' }],
  },
  {
    label:"RQI(路面行驶质量指数)",
    prop:"rqi",
    type:"inputNumber",
    span:12,
    rules: [{ required: true, message: '请输入RQI(路面行驶质量指数)', trigger: 'change' }],
  },
  {
    label:"SCI(路基技术状况指数)",
    prop:"sci",
    type:"inputNumber",
    span:12,
    rules: [{ required: true, message: '请输入SCI(路基技术状况指数)', trigger: 'change' }],
  },
  {
    label:"SRI(路面抗滑性能指数)",
    prop:"sri",
    type:"inputNumber",
    span:12,
    rules: [{ required: true, message: '请输入SRI(路面抗滑性能指数)', trigger: 'change' }],
  },
  {
    label:"TCI(沿线设施技术状况指数)",
    prop:"tci",
    type:"inputNumber",
    span:12,
    rules: [{ required: true, message: '请输入TCI(沿线设施技术状况指数)', trigger: 'change' }],
  },
//   bci	number
// BCI(桥隧构造物技术状况指数)
  {
    label:"BCI(桥隧构造物技术状况指数)",
    prop:"bci",
    type:"inputNumber",
    span:12,
    rules: [{ required: true, message: '请输入BCI(桥隧构造物技术状况指数)', trigger: 'change' }],
  },
  {
    label:"描述",
    prop:"remark",
    type:"input",
    span:12,
  },



]





export default {
  baseInfoData,

}
