<template>
    <div class="app-container">
        <!--查询条件开始-->
        <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" label-width="68px">
            <div style="display: flex; align-items: center; flex-wrap: nowrap">
                <CascadeSelection
                    style="min-width: 192px; margin-right: 10px"
                    :form-data="queryParams"
                    v-model="queryParams"
                    types="201"
                    multiple
                />

                <el-form-item label="" prop="assetCode" style="margin-bottom: 0; margin-right: 10px">
                    <el-input
                        v-model="queryParams.assetCode"
                        :placeholder="`请输入${partsType[type].substring(0, 2)}编码`"
                        clearable
                        prefix-icon="el-icon-user"
                        style="width: 240px"
                        @keyup.enter.native="handleQuery"
                    />
                </el-form-item>

                <el-form-item label="" prop="assetName" style="margin-bottom: 0; margin-right: 10px">
                    <el-input
                        v-model="queryParams.assetName"
                        :placeholder="`请输入${partsType[type].substring(0, 2)}${
                            ['3', '4'].includes(type) ? '类型' : '名称'
                        }`"
                        clearable
                        prefix-icon="el-icon-user"
                        style="width: 240px"
                        @keyup.enter.native="handleQuery"
                    />
                </el-form-item>

                <el-form-item label="" prop="hasDisease" style="margin-bottom: 0; margin-right: 10px">
                    <el-select v-model="queryParams.hasDisease" placeholder="是否有病害" clearable style="width: 120px">
                        <el-option label="是" :value="true"></el-option>
                        <el-option label="否" :value="false"></el-option>
                    </el-select>
                </el-form-item>

                <el-form-item style="margin-bottom: 0">
                    <div style="white-space: nowrap">
                        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">
                            搜索
                        </el-button>
                        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
                        <el-button
                            v-show="!showSearch"
                            @click="showSearch = true"
                            icon="el-icon-arrow-down"
                            circle
                        ></el-button>
                        <el-button
                            v-show="showSearch"
                            @click="showSearch = false"
                            icon="el-icon-arrow-up"
                            circle
                        ></el-button>
                    </div>
                </el-form-item>
            </div>
            <!--默认折叠-->
            <br />
            <el-form-item v-show="showSearch" label="" prop="checkTime">
                <el-date-picker
                    clearable
                    v-model="queryParams.checkTime"
                    type="daterange"
                    value-format="yyyy-MM-dd HH:mm:ss"
                    start-placeholder="检查开始时间"
                    end-placeholder="检查结束时间"
                    style="width: 240px"
                ></el-date-picker>
            </el-form-item>

            <el-form-item v-show="showSearch" label="" prop="status" v-if="menuType === 1">
                <DictSelect
                    v-model="queryParams.status"
                    type="patrol_inspection_status"
                    placeholder="请选择状态"
                    clearable
                    style="width: 240px"
                ></DictSelect>
            </el-form-item>

            <el-form-item v-show="showSearch" label="" prop="remark">
                <el-input
                    v-model="queryParams.remark"
                    placeholder="请输入备注"
                    clearable
                    prefix-icon="el-icon-edit"
                    style="width: 240px"
                    @keyup.enter.native="handleQuery"
                />
            </el-form-item>
        </el-form>
        <!--查询条件结束-->

        <!--操作按钮区开始-->
        <el-row :gutter="10" style="margin-bottom: 8px">
            <el-col :span="1.5" v-if="menuType === 1">
                <el-button type="primary" icon="el-icon-plus" size="mini" @click="handleAdd"> 新增 </el-button>
            </el-col>
            <el-col :span="1.5" v-if="menuType === 1">
                <el-button type="primary" icon="el-icon-edit" size="mini" :disabled="single" @click="handleUpdate">
                    修改
                </el-button>
            </el-col>
            <el-col :span="1.5" v-if="menuType === 1">
                <el-button type="danger" icon="el-icon-delete" size="mini" :disabled="multiple" @click="handleDelete">
                    删除
                </el-button>
            </el-col>

            <el-col :span="1.5" v-if="menuType === 1">
                <el-button type="primary" size="mini" @click="handleCommit" :disabled="multiple"> 提交 </el-button>
            </el-col>
            <el-col :span="1.5" v-if="menuType === 2">
                <el-button type="primary" size="mini" @click="handleAudit" :disabled="multiple"> 审核 </el-button>
            </el-col>
            <el-col :span="1.5" v-if="menuType === 2">
                <el-button type="primary" size="mini" @click="handleBatchAuditAll"> 批量审核 </el-button>
            </el-col>
            <el-col :span="1.5" v-if="menuType === 3">
                <el-button type="primary" size="mini" @click="handleUpdateAuditTime" :disabled="multiple" v-hasPermi="['patrol:assetCheck:updateAuditTime']">
                    修改审核时间
                </el-button>
            </el-col>
            <el-col :span="1.5" v-if="menuType === 1">
                <el-button type="primary" icon="el-icon-upload2" size="mini" @click="handleImport"> 导入 </el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="primary" icon="el-icon-download" size="mini" @click="handleExport"> 导出 </el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="primary" icon="el-icon-download" size="mini" @click="handleExportCard">
                    导出卡片
                </el-button>
            </el-col>
            <el-col :span="1.5" v-if="menuType === 1 && ['1', '3', '5'].includes(type)">
                <el-button
                    type="primary"
                    icon="el-icon-plus"
                    size="mini"
                    v-hasPermi="['patrol:assetCheck:generation']"
                    @click="handleGenerate"
                >
                    批量生成
                </el-button>
            </el-col>
            <right-toolbar :showSearch.sync="showSearch" @queryTable="getList" :columns="columns"></right-toolbar>
        </el-row>
        <!--操作按钮区结束-->

        <!--数据表格开始-->
        <div class="tableDiv">
            <el-table
                v-adjust-table
                ref="table"
                size="mini"
                :height="showSearch ? 'calc(100vh - 320px)' : 'calc(100vh - 260px)'"
                style="width: 100%"
                v-loading="loading"
                border
                :data="assetCheckList"
                @selection-change="handleSelectionChange"
                :row-style="rowStyle"
                @row-click="handleRowClick"
            >
                <el-table-column type="selection" width="50" align="center" />
                <el-table-column fixed label="序号" type="index" width="50">
                    <template v-slot="scope">
                        {{ scope.$index + (queryParams.pageNum - 1) * queryParams.pageSize + 1 }}
                    </template>
                </el-table-column>
                <el-table-column
                    label="养护路段"
                    align="center"
                    prop="maintenanceSectionName"
                    min-width="140"
                    show-overflow-tooltip
                />
                <el-table-column
                    label="路线编码"
                    align="center"
                    prop="routeCode"
                    min-width="140"
                    show-overflow-tooltip
                />
                <!--        名称-->
                <el-table-column
                    :label="`${partsType[type].substring(0, 2)}${['3', '4'].includes(type) ? '类型' : '名称'}`"
                    align="center"
                    prop="assetName"
                    min-width="140"
                    show-overflow-tooltip
                />
                <el-table-column
                    :label="`${partsType[type].substring(0, 2)}编码`"
                    align="center"
                    prop="assetCode"
                    min-width="140"
                    show-overflow-tooltip
                />
                <el-table-column
                    label="桩号"
                    align="center"
                    prop="centerStake"
                    :formatter="(...arg) => formatPile(arg[2])"
                    v-if="computedColumns('桩号')"
                />
                <el-table-column
                    label="管理处"
                    align="center"
                    prop="maintainUnitName"
                    min-width="140"
                    show-overflow-tooltip
                />
                <el-table-column
                    label="养护单位"
                    align="center"
                    prop="propertyUnitName"
                    min-width="140"
                    show-overflow-tooltip
                />
                <el-table-column
                    label="天气"
                    align="center"
                    prop="weather"
                    v-if="['5', '6'].includes(type) && computedColumns('天气')"
                />
                <el-table-column 
                    label="负责人" 
                    align="center" 
                    prop="kahunaName" 
                    v-if="computedColumns('负责人')"
                >
                    <template slot-scope="scope">
                        <span v-if="scope.row.status != 0 && scope.row.status != 1">{{ scope.row.kahunaName }}</span>
                    </template>
                </el-table-column>
                <el-table-column
                    label="记录人"
                    align="center"
                    prop="oprUserName"
                    width="140"
                    v-if="computedColumns('记录人')"
                />
                <el-table-column
                    label="巡查类别"
                    align="center"
                    prop="category"
                    v-if="['2', '4', '6'].includes(type) && computedColumns('巡查类别')"
                >
                    <template slot-scope="scope">
                        <dict-tag :options="dict.type.patrol_inspection_ilk" :value="scope.row.category" />
                    </template>
                </el-table-column>
                <el-table-column
                    label="检查日期"
                    align="center"
                    prop="checkTime"
                    min-width="140"
                    show-overflow-tooltip
                    v-if="computedColumns('检查日期')"
                >
                    <template slot-scope="scope">
                        <span>{{ parseTime(scope.row.checkTime, '{y}-{m}-{d}') }}</span>
                    </template>
                </el-table-column>
                <el-table-column label="状态" align="center" prop="status" width="120" v-if="computedColumns('状态')">
                    <template slot-scope="scope">
                        <dict-tag :options="dict.type.patrol_inspection_status" :value="scope.row.status" />
                    </template>
                </el-table-column>
                <el-table-column label="审核日期" align="center" prop="auditTime" min-width="140" show-overflow-tooltip>
                    <template slot-scope="scope">
                        <span v-if="scope.row.status != 0 && scope.row.status != 1">{{ parseTime(scope.row.auditTime, '{y}-{m}-{d}') }}</span>
                    </template>
                </el-table-column>
                <el-table-column
                    label="备注"
                    align="center"
                    prop="remark"
                    show-overflow-tooltip
                    v-if="computedColumns('备注')"
                />

                <el-table-column
                    label="操作"
                    fixed="right"
                    align="center"
                    width="320"
                    class-name="small-padding fixed-width"
                >
                    <template slot-scope="scope" v-if="scope.row.userId !== 1">
                        <el-button size="mini" type="text" icon="el-icon-view" @click="handleView(scope.row)">
                            查看
                        </el-button>

                        <el-button
                            v-if="menuType === 1 && (scope.row.status == 1 || scope.row.status == 4)"
                            size="mini"
                            type="text"
                            icon="el-icon-edit"
                            @click="handleUpdate(scope.row)"
                        >
                            修改
                        </el-button>
                        <el-button
                            v-if="menuType === 1 && (scope.row.status == 1 || scope.row.status == 4)"
                            size="mini"
                            type="text"
                            icon="el-icon-view"
                            @click="handleCommit(scope.row)"
                        >
                            提交
                        </el-button>
                        <el-button
                            v-if="menuType === 2 && scope.row.status == 2"
                            size="mini"
                            type="text"
                            icon="el-icon-view"
                            @click="handleAudit(scope.row)"
                        >
                            审核
                        </el-button>
                        <el-button
                            v-if="menuType === 1 && (scope.row.status == 1 || scope.row.status == 4)"
                            size="mini"
                            type="text"
                            icon="el-icon-delete"
                            @click="handleDelete(scope.row)"
                        >
                            删除
                        </el-button>
                    </template>
                </el-table-column>
            </el-table>
            <pagination
                v-show="total > 0"
                :total="total"
                :page.sync="queryParams.pageNum"
                :limit.sync="queryParams.pageSize"
                :page-sizes="[10, 20, 30, 50, 100, 1000]"
                @pagination="getList"
            />
        </div>
        <!--数据表格结束-->

        <!-- 添加或修改资产寻检查主对话框 -->
        <el-dialog
            :title="title"
            :visible.sync="open"
            width="80%"
            append-to-body
            :close-on-click-modal="false"
            class="asset-check-edit-dialog"
        >
            <AssetCheckInsertOrUpdate
                :disabled="disabled"
                ref="assetCheckInsertOrUpdate"
                :check-entity="form"
                :title="title"
            ></AssetCheckInsertOrUpdate>
            <div slot="footer" class="dialog-footer">
                <template v-if="title.substring(0, 2) === '审核'">
                    <el-button type="danger" @click="submitForm({ id: form.id, status: 4, type: form.type })">
                        驳 回
                    </el-button>
                    <el-button
                        type="primary"
                        @click="submitForm({ id: form.id, status: 3, type: form.type, auditTime: form.auditTime })"
                    >
                        通 过
                    </el-button>
                </template>
                <template v-else-if="title.substring(0, 2) === '提交'">
                    <el-button type="primary" @click="submitForm({ id: form.id, status: 2, type: form.type })">
                        提 交
                    </el-button>
                </template>
                <template v-else-if="title.substring(0, 2) !== '查看'">
                    <el-button type="primary" @click="submitForm">确 定</el-button>
                    <el-button @click="cancel">取 消</el-button>
                </template>
            </div>
        </el-dialog>

        <!-- 批量生成资产寻检查主对话框 -->
        <AssetCheckGenerate
            :disabled="disabled"
            ref="assetCheckGenerate"
            :visible.sync="openGenerate"
            width="80%"
            append-to-body
            :close-on-click-modal="false"
            :type="type"
            :generate="true"
            @update:visible="handleGenerateClose"
        ></AssetCheckGenerate>

        <!-- 用户导入对话框 -->
        <el-dialog :title="upload.title" :visible.sync="upload.open" width="400px" append-to-body>
            <el-upload
                ref="upload"
                :limit="1"
                accept=".xlsx, .xls"
                :headers="upload.headers"
                :action="upload.url + '?updateSupport=' + upload.updateSupport"
                :disabled="upload.isUploading"
                :on-progress="handleFileUploadProgress"
                :on-success="handleFileSuccess"
                :auto-upload="false"
                drag
            >
                <i class="el-icon-upload"></i>
                <div class="el-upload__text">
                    将文件拖到此处，或
                    <em>点击上传</em>
                </div>
                <div class="el-upload__tip text-center" slot="tip">
                    <div class="el-upload__tip" slot="tip">
                        <el-checkbox v-model="upload.updateSupport" />
                        是否更新已经存在的用户数据
                    </div>
                    <span>仅允许导入xls、xlsx格式文件。</span>
                    <el-link
                        type="primary"
                        :underline="false"
                        style="font-size: 12px; vertical-align: baseline"
                        @click="importTemplate"
                    >
                        下载模板
                    </el-link>
                </div>
            </el-upload>
            <div slot="footer" class="dialog-footer">
                <el-button type="primary" @click="submitFileForm">确 定</el-button>
                <el-button @click="upload.open = false">取 消</el-button>
            </div>
        </el-dialog>
        <!-- 批量审核对话框 -->
        <el-dialog title="批量审核" :visible.sync="auditDialogVisible" width="400px" append-to-body>
            <div style="margin-bottom: 20px">
                <div style="margin-bottom: 10px">请选择审核时间:</div>
                <el-date-picker
                    v-model="auditTime"
                    type="datetime"
                    placeholder="选择审核时间"
                    value-format="yyyy-MM-dd HH:mm:ss"
                    style="width: 100%"
                ></el-date-picker>
            </div>
            <div slot="footer" class="dialog-footer">
                <el-button type="danger" @click="handleBatchAudit(4)">驳 回</el-button>
                <el-button type="primary" @click="handleBatchAudit(3)">通 过</el-button>
            </div>
        </el-dialog>

        <!-- 批量修改审核时间对话框 -->
        <el-dialog title="批量修改审核时间" :visible.sync="updateAuditTimeDialogVisible" width="400px" append-to-body>
            <div style="margin-bottom: 20px">
                <div style="margin-bottom: 10px">请选择新的审核时间:</div>
                <el-date-picker
                    v-model="newAuditTime"
                    type="datetime"
                    placeholder="选择审核时间"
                    value-format="yyyy-MM-dd HH:mm:ss"
                    style="width: 100%"
                ></el-date-picker>
            </div>
            <div slot="footer" class="dialog-footer">
                <el-button @click="updateAuditTimeDialogVisible = false">取 消</el-button>
                <el-button type="primary" @click="submitUpdateAuditTime">确 定</el-button>
            </div>
        </el-dialog>

        <!-- 批量审核高级对话框 -->
        <el-dialog title="批量审核" :visible.sync="batchAuditDialogVisible" width="600px" append-to-body>
            <el-form :model="batchAuditForm" ref="batchAuditForm" label-width="120px" size="small">
                <el-form-item label="审核模式" prop="auditMode">
                    <el-radio-group v-model="batchAuditForm.auditMode">
                        <el-radio label="condition">按条件筛选</el-radio>
                        <el-radio label="selected">已选中项 <span v-if="ids.length > 0">(已选择 {{ids.length}} 条)</span></el-radio>
                    </el-radio-group>
                </el-form-item>
                <template v-if="batchAuditForm.auditMode === 'condition'">
                    <el-form-item label="检查日期范围">
                        <el-date-picker
                            v-model="checkTimeRange"
                            type="daterange"
                            value-format="yyyy-MM-dd"
                            start-placeholder="开始日期"
                            end-placeholder="结束日期"
                            style="width: 100%"
                        ></el-date-picker>
                    </el-form-item>
                    <el-form-item label="状态" prop="statusList">
                        <el-select v-model="batchAuditForm.statusList" placeholder="请选择状态" multiple style="width: 100%">
                            <el-option
                                v-for="item in statusOptions"
                                :key="item.value"
                                :label="item.label"
                                :value="item.value"
                            ></el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item label="部门" prop="deptId">
                        <el-select 
                            v-model="batchAuditForm.deptId" 
                            placeholder="请选择部门" 
                            clearable 
                            style="width: 100%" 
                            @clear="selectClear"
                            ref="deptSelect"
                        >
                            <el-option
                                v-for="item in formatData(deptOptions)"
                                :key="item.value"
                                :label="item.label"
                                :value="item.value"
                                hidden
                            ></el-option>
                            <el-tree
                                style="font-weight: 400"
                                :data="deptOptions"
                                :props="defaultProps"
                                @node-click="handleNodeClick"
                                node-key="id"
                                ref="treeRef"
                            ></el-tree>
                        </el-select>
                    </el-form-item>
                </template>
                <template v-else>
                    <div class="selected-info" style="padding: 10px; background-color: #f8f8f8; margin-bottom: 15px; border-radius: 4px;">
                        <div style="font-weight: bold; margin-bottom: 5px;">已选中项目信息</div>
                        <div>共选择了 <span style="color: #409EFF; font-weight: bold;">{{ids.length}}</span> 条记录进行批量审核</div>
                    </div>
                </template>

                <div style="border-top: 1px solid #EBEEF5; margin: 15px 0;"></div>
                
                <el-form-item label="审核人" prop="reviewerId">
                    <el-select 
                        v-model="batchAuditForm.reviewerName"
                        placeholder="请选择审核人" 
                        clearable 
                        style="width: 100%" 
                        @clear="clearReviewer"
                        ref="reviewerSelect"
                    >
                        <el-option
                            v-for="item in formatUserData(reviewerOptions)"
                            :key="item.value"
                            :label="item.label"
                            :value="item.value"
                            hidden
                        ></el-option>
                        <el-tree
                            style="font-weight: 400"
                            :data="reviewerOptions"
                            :props="userProps"
                            @node-click="handleUserNodeClick"
                            node-key="id"
                            :filter-node-method="filterUserNode"
                            :render-content="renderUserTreeNode"
                            ref="userTreeRef"
                        ></el-tree>
                    </el-select>
                </el-form-item>
                <el-form-item label="审核时间设置" prop="timeMode">
                    <el-radio-group v-model="batchAuditForm.timeMode">
                        <el-radio label="exact">精确时间</el-radio>
                        <el-radio label="random">随机延迟</el-radio>
                    </el-radio-group>
                </el-form-item>
                <el-form-item label="精确审核时间" prop="exactAuditTime" v-if="batchAuditForm.timeMode === 'exact'">
                    <el-date-picker
                        v-model="batchAuditForm.exactAuditTime"
                        type="date"
                        placeholder="选择审核时间"
                        value-format="yyyy-MM-dd"
                        style="width: 100%"
                        @change="datePickerChanged"
                    ></el-date-picker>
                </el-form-item>
                <el-form-item label="延迟天数" v-if="batchAuditForm.timeMode === 'random'">
                    <div style="display: flex; align-items: center;">
                        <el-form-item prop="minDelayDays" style="margin-bottom: 0;">
                            <el-input-number v-model="batchAuditForm.minDelayDays" :min="1" :max="30" style="width: 120px"></el-input-number>
                        </el-form-item>
                        <span style="margin: 0 10px;">~</span>
                        <el-form-item prop="maxDelayDays" style="margin-bottom: 0;">
                            <el-input-number v-model="batchAuditForm.maxDelayDays" :min="1" :max="30" style="width: 120px"></el-input-number>
                        </el-form-item>
                        <span style="margin-left: 10px;">天</span>
                    </div>
                </el-form-item>
            </el-form>
            <div slot="footer" class="dialog-footer">
                <el-button @click="batchAuditDialogVisible = false">取 消</el-button>
                <el-button type="danger" @click="submitBatchAudit(4)">批量驳回</el-button>
                <el-button type="primary" @click="submitBatchAudit(3)">批量通过</el-button>
            </div>
        </el-dialog>
    </div>
</template>

<script>
    import {
        selectAssetCheckData,
        listAssetCheck,
        getAssetCheck,
        delAssetCheck,
        addAssetCheck,
        updateAssetCheck,
        getAssetTotalCount,
        updateStatusByIds,
        batchComplete,
    } from '@/api/patrol/assetCheck';
    import { getToken } from '@/utils/auth';

    import TreeSelect from '@riophae/vue-treeselect';
    import '@riophae/vue-treeselect/dist/vue-treeselect.css';

    import AssetCheckInsertOrUpdate from '@/views/patrol/assetCheck/insertOrUpdate.vue';
    import AssetCheckGenerate from '@/views/patrol/assetCheck/generate.vue';
    import { listMaintenanceSectionAll } from '@/api/system/maintenanceSection';
    import { listAllRoute } from '@/api/system/route';
    import { getTreeStruct } from '@/api/tmpl';
    import CascadeSelection from '@/components/CascadeSelectionManagementOffice/index.vue';
    /**
     * 操作按钮暂不用系统权限控制
     */

    export default {
        name: 'AssetCheck',
        dicts: ['patrol_inspection_ilk', 'patrol_inspection_category', 'patrol_inspection_status'],
        components: {
            TreeSelect,
            AssetCheckInsertOrUpdate,
            CascadeSelection,
            AssetCheckGenerate,
        },
        props: {
            /**
             * 巡查类型；桥梁日常巡查、桥梁经常检查、隧道日常巡查等
             */
            type: {
                type: String,
                // require: true
                default: '5',
            },
            /**
             * menuType 菜单类型 ps: 1:隧道日常巡查   2:隧道日常巡查审核  3:隧道日常巡查结果
             */
            menuType: {
                type: Number,
                // require: true
                default: 1,
            },
        },
        data() {
            return {
                // 遮罩层
                loading: true,
                // 选中数组
                ids: [],
                // 非单个禁用
                single: true,
                // 非多个禁用
                multiple: true,
                // 显示搜索条件
                showSearch: false,
                // 总条数
                total: 0,
                // 资产寻检查主表格数据
                assetCheckList: null,
                // 弹出层标题
                title: '',
                assetList: [],
                partsType: {
                    1: '桥梁日常巡查',
                    2: '桥梁经常检查',
                    3: '涵洞定期检查',
                    4: '涵洞经常检查',
                    5: '隧道日常巡查',
                    6: '隧道经常检查',
                },
                deptOptions: [],
                maintenanceSectionList: null,
                routeList: null,
                // 是否显示弹出层
                open: false,
                // 是否显示批量生成弹出层
                openGenerate: false,
                // 表单参数
                form: {
                    type: this.type,
                    partsType: this.type,
                    details: null,
                },
                // 用户导入参数
                upload: {
                    // 是否显示弹出层（用户导入）
                    open: false,
                    // 弹出层标题（用户导入）
                    title: '',
                    // 是否禁用上传
                    isUploading: false,
                    // 是否更新已经存在的用户数据
                    updateSupport: 0,
                    // 设置上传的请求头部
                    headers: { Authorization: 'Bearer ' + getToken() },
                    // 上传的地址
                    url: process.env.VUE_APP_BASE_API + '/system/user/importData',
                },
                // 查询参数
                queryParams: {
                    pageNum: 1,
                    pageSize: 50,
                    type: this.type,
                    stage: ['2', '4', '6'].includes(this.type) ? '1' : null,
                    dataRule: true,
                    category: null,
                    kahunaId: null,
                    kahunaName: null,
                    kahunaSign: null,
                    oprUserId: null,
                    oprUserName: null,
                    oprUserSign: null,
                    checkTime: null,
                    checkStartTime: null,
                    checkEndTime: null,
                    status: null,
                    auditTime: null,
                    image: null,
                    assetId: null,
                    assetName: null,
                    assetCode: null,
                    routeId: null,
                    routeName: null,
                    routeCode: null,
                    propertyUnitId: null,
                    propertyUnitName: null,
                    departmentIdList: null,
                    maintainUnitName: null,
                    maintenanceSectionId: null,
                    maintenanceSectionName: null,
                    centerStake: null,
                    weather: null,
                    hasDisease: null,
                    remark: null,
                },
                // 列信息
                columns: [
                    // { key: 0, label: `检查类型`, visible: true },
                    { key: 1, label: `巡查类别 `, visible: true },
                    { key: 2, label: `负责人`, visible: true },
                    { key: 3, label: `记录人`, visible: true },
                    { key: 4, label: `检查日期`, visible: true },
                    { key: 5, label: `状态`, visible: true },
                    // { key: 6, label: `图像`, visible: true },
                    { key: 7, label: `备注`, visible: true },
                    // { key: 8, label: `删除标识`, visible: true },
                    // { key: 9, label: `资产id`, visible: true },
                    // { key: 10, label: `资产名称`, visible: true },
                    // { key: 11, label: `资产编码`, visible: true },
                    // { key: 12, label: `路线名称`, visible: true },
                    // { key: 13, label: `路线编码`, visible: true },
                    // { key: 14, label: `权属单位`, visible: true },
                    // { key: 15, label: `养护单位`, visible: true },
                    { key: 16, label: `桩号`, visible: true },
                    { key: 17, label: `天气`, visible: true },
                    // { key: 18, label: `是否异常`, visible: true },
                ],
                // 审核时间
                auditTime: null,
                // 审核对话框可见性
                auditDialogVisible: false,
                // 修改审核时间
                newAuditTime: null,
                // 修改审核时间对话框可见性
                updateAuditTimeDialogVisible: false,
                // 批量审核对话框可见性
                batchAuditDialogVisible: false,
                // 批量审核表单数据
                batchAuditForm: {
                    type: this.type,
                    checkStartTime: null,
                    checkEndTime: null,
                    checkTimeRange: [],
                    statusList: ['2'], // 默认待审核
                    deptId: null,
                    reviewerId: null,
                    reviewerName: null,
                    timeMode: 'exact', // 'exact'精确时间 'random'随机延迟
                    minDelayDays: 3,
                    maxDelayDays: 7,
                    auditMode: 'condition', // 'condition'按条件筛选 'selected'已选择项
                    exactAuditTime: '',
                },
                // 审核人选项数组
                reviewerOptions: [],
                // 状态选项
                statusOptions: [
                    { value: '1', label: '待提交' },
                    { value: '2', label: '待审核' },
                    { value: '3', label: '审核通过' },
                    { value: '4', label: '审核不通过' }
                ],
                // 树形组件属性
                defaultProps: {
                    children: 'children',
                    label: 'label'
                },
                // 级联选择器配置
                props: {
                    multiple: false, // 设置为单选
                    value: 'id',
                    checkStrictly: true, // 允许选择任何级别的节点
                    disabled: (data, node) => {
                        // 禁用非user类型的节点
                        return data.type !== 'user';
                    }
                },
                userProps: {
                    multiple: false, // 设置为单选
                    value: 'id',
                    checkStrictly: true, // 允许选择任何级别的节点
                    disabled: (data, node) => {
                        // 禁用非user类型的节点
                        return data.type !== 'user';
                    },
                    class: (data) => {
                        return data.type === 'user' ? 'user-node' : 'dept-node';
                    }
                },
            };
        },
        watch: {
            'queryParams.checkTime': function (val) {
                if (Array.isArray(val)) {
                    this.queryParams.checkStartTime = val[0] ? val[0].substring(0, 10) + ' 00:00:00' : null;
                    this.queryParams.checkEndTime = val[1] ? val[1].substring(0, 10) + ' 23:59:59' : null;
                } else {
                    // 当值为null或undefined时
                    this.queryParams.checkStartTime = null;
                    this.queryParams.checkEndTime = null;
                }
            },
            menuType: {
                handler(value) {
                    switch (value) {
                        case 1:
                            this.queryParams.status = '1';
                            break;
                        case 2:
                            this.queryParams.status = '2';
                            break;
                        case 3:
                            this.queryParams.statusList = ['3', '4'];
                    }
                },
                immediate: true,
            },
        },
        computed: {
            disabled() {
                return (
                    this.title.substring(0, 2) === '查看' ||
                    this.title.substring(0, 2) === '审核' ||
                    this.title.substring(0, 2) === '提交'
                );
            },
            computedColumns() {
                /* return () => {
                    return true;
                }; */
                return (label) => {
                    let visible
                    this.columns.forEach((item)=>{
                        if(item.label === label){
                            visible = item.visible
                        }
                    })
                    return visible
                }
            },
            checkTimeRange: {
                get() {
                    if (this.batchAuditForm.checkStartTime && this.batchAuditForm.checkEndTime) {
                        return [this.batchAuditForm.checkStartTime, this.batchAuditForm.checkEndTime];
                    }
                    return [];
                },
                set(val) {
                    if (val && val.length === 2) {
                        this.batchAuditForm.checkStartTime = val[0];
                        this.batchAuditForm.checkEndTime = val[1];
                    } else {
                        this.batchAuditForm.checkStartTime = null;
                        this.batchAuditForm.checkEndTime = null;
                    }
                }
            }
        },
        created() {
            this.getList();
            this.getDeptTree();
            this.getRouteList();
            this.getMaintenanceSection();
            // 在页面加载时加载部门列表和人员信息，只需要加载一次
            this.loadDeptOptions();
            this.loadReviewerOptions();
        },
        methods: {
            /** 查询用户列表 */
            getList() {
                this.loading = true;
                const queryParams = { ...this.queryParams };
                // 删除checkTime但保留checkStartTime和checkEndTime
                const { checkTime, departmentIdList, ...restParams } = queryParams;
                selectAssetCheckData(restParams)
                    .then((response) => {
                        this.assetCheckList = response.rows;
                        this.total = response.total;
                    })
                    .catch((error) => {
                        console.error('获取数据失败:', error);
                    })
                    .finally(() => {
                        this.loading = false;
                    });
            },
            /** 查询部门下拉树结构 */
            getDeptTree() {
                return getTreeStruct({ types: 201 }).then((response) => {
                    this.deptOptions = response.data;
                });
            },
            //管理处下拉选点击事件
            deptChange() {
                listMaintenanceSectionAll({
                    departmentIdList: this.queryParams.departmentIdList,
                }).then((res) => {
                    this.maintenanceSectionList = res.data;
                });
            },
            /** 查询养护路段下拉列表 */
            getMaintenanceSection() {
                listMaintenanceSectionAll().then((res) => {
                    this.maintenanceSectionList = res.data;
                });
            },
            /** 查询路线列表 */
            getRouteList() {
                listAllRoute().then((res) => {
                    this.routeList = res.data;
                });
            },
            // 取消按钮
            cancel() {
                this.open = false;
                // this.reset();
            },
            // 表单重置
            reset() {
                this.form = {
                    type: this.type,
                    partsType: this.type,
                    details: null,
                    category: null,
                    kahunaId: null,
                    kahunaName: null,
                    kahunaSign: null,
                    oprUserId: null,
                    oprUserName: null,
                    oprUserSign: null,
                    checkTime: null,
                    status: null,
                    auditTime: null,
                    image: null,
                    remark: null,
                    assetId: null,
                    assetName: null,
                    assetCode: null,
                    routeId: null,
                    routeName: null,
                    routeCode: null,
                    propertyUnitId: null,
                    propertyUnitName: null,
                    maintainUnitId: null,
                    maintainUnitName: null,
                    maintenanceSectionId: null,
                    maintenanceSectionName: null,
                    centerStake: null,
                    weather: null,
                    hasDisease: null,
                    deptUserOptions: [],
                };
            },
            /** 搜索按钮操作 */
            handleQuery() {
                this.queryParams.pageNum = 1;
                this.getList();
            },
            /** 重置按钮操作 */
            resetQuery() {
                this.resetForm('queryForm');
                this.queryParams.managementMaintenanceBranchId = undefined;
                this.queryParams.maintenanceSectionId = undefined;
                this.queryParams.routeCodes = undefined;
                this.$nextTick(() => {
                    this.handleQuery();
                });
            },
            // 多选框选中数据
            handleSelectionChange(selection) {
                console.log(selection);
                this.ids = selection.map((item) => item.id);
                this.single = selection.length != 1;
                this.multiple = !selection.length;
            },
            // 表格点击勾选
            handleRowClick(row) {
                row.isSelected = !row.isSelected;
                this.$refs.table.toggleRowSelection(row);
            },
            // 勾选高亮
            rowStyle({ row, rowIndex }) {
                if (this.ids.includes(row.id)) {
                    return { 'background-color': '#b7daff', color: '#333' };
                } else {
                    return { 'background-color': '#fff', color: '#333' };
                }
            },
            handleCommit(row) {
                const id = row.id || this.ids;
                const type = this.type;
                if (row.id) {
                    getAssetCheck({ id, type }).then((response) => {
                        this.form = { ...response.data, partsType: this.type };
                        this.open = true;
                        this.title = `提交${this.partsType[this.type]}`;
                    });
                } else {
                    this.$modal
                        .confirm(`是否确认提交选中的 ${id.length} 条数据项？`)
                        .then(function () {
                            return updateStatusByIds({ checkIds: id, status: 2, type });
                        })
                        .then(() => {
                            this.getList();
                            this.$modal.msgSuccess('提交成功');
                        });
                }
            },
            handleAudit(row) {
                const id = row.id || this.ids;
                const type = this.type;
                if (row.id) {
                    getAssetCheck({ id, type }).then((response) => {
                        this.form = { ...response.data, partsType: this.type };
                        this.open = true;
                        this.title = `审核${this.partsType[this.type]}`;
                    });
                } else {
                    // 设置默认时间为当前时间
                    const now = new Date();
                    const year = now.getFullYear();
                    const month = String(now.getMonth() + 1).padStart(2, '0');
                    const day = String(now.getDate()).padStart(2, '0');
                    const hours = String(now.getHours()).padStart(2, '0');
                    const minutes = String(now.getMinutes()).padStart(2, '0');
                    const seconds = String(now.getSeconds()).padStart(2, '0');
                    this.auditTime = `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
                    this.auditDialogVisible = true;
                }
            },
            /** 批量审核确认 */
            handleBatchAudit(status) {
                if (!this.auditTime) {
                    this.$message.warning('请选择审核时间');
                    return;
                }
                
                updateStatusByIds({
                    checkIds: this.ids,
                    type: this.type,
                    status: status,
                    auditTime: this.auditTime
                }).then(() => {
                    this.auditDialogVisible = false;
                    this.auditTime = null;
                    this.getList();
                    this.$modal.msgSuccess('审核成功');
                });
            },
            handleView(row) {
                const id = row.id || this.ids;
                const type = this.type;
                getAssetCheck({ id, type }).then((response) => {
                    this.form = { ...response.data, partsType: this.type };
                    this.open = true;
                    this.title = `查看${this.partsType[this.type]}`;
                });
            },
            /** 新增按钮操作 */
            handleAdd() {
                this.reset();
                this.open = true;
                this.title = `添加${this.partsType[this.type]}`;
            },
            /** 新增按钮操作 */
            handleGenerate() {
                this.openGenerate = true;
            },
            /** 修改按钮操作 */
            handleUpdate(row) {
                // this.reset();
                const id = row.id || this.ids;
                const type = this.type;
                getAssetCheck({ id, type }).then((response) => {
                    this.form = { ...response.data, partsType: this.type };
                    this.open = true;
                    this.title = `修改${this.partsType[this.type]}`;
                });
            },
            /** 提交按钮 */
            submitForm: function (data) {
                if (!(data instanceof PointerEvent)) {
                    // 处理状态描述为空的情况
                    if (this.form.details) {
                        this.form.details.forEach((item) => {
                            if (!item.ignore && (!item.defect || item.defect.trim() === '')) {
                                item.defect = '未见异常';
                            }
                            if (!item.ignore && (!item.advice || item.advice.trim() === '')) {
                                item.advice = '正常保养';
                            }
                        });
                    }
                    updateAssetCheck(data).then((response) => {
                        this.$modal.msgSuccess(`${this.title.substring(0, 2)}成功`);
                        this.open = false;
                        this.getList();
                    });
                } else {
                    this.$refs.assetCheckInsertOrUpdate.$refs['form'].validate((valid) => {
                        if (valid) {
                            // 处理状态描述为空的情况
                            if (this.form.details) {
                                this.form.details.forEach((item) => {
                                    if (!item.ignore && (!item.defect || item.defect.trim() === '')) {
                                        item.defect = '未见异常';
                                    }
                                    if (!item.ignore && (!item.advice || item.advice.trim() === '')) {
                                        item.advice = '正常保养';
                                    }
                                });
                            }

                            if (this.form.id != null) {
                                updateAssetCheck(this.form).then((response) => {
                                    this.$modal.msgSuccess('修改成功');
                                    this.open = false;
                                    this.getList();
                                });
                            } else {
                                addAssetCheck(this.form).then((response) => {
                                    this.$modal.msgSuccess('新增成功');
                                    this.open = false;
                                    this.getList();
                                });
                            }
                        }
                    });
                }
            },
            /** 删除按钮操作 */
            handleDelete(row) {
                const id = row.id || this.ids;
                const type = this.type;
                if (row.id) {
                    this.$modal
                        .confirm(
                            `是否确认删除${this.partsType[type].substring(0, 2)}名称是 ${row.assetName} 的数据项？`
                        )
                        .then(function () {
                            return delAssetCheck({ idList: [row.id], type });
                        })
                        .then(() => {
                            this.getList();
                            this.$modal.msgSuccess('删除成功');
                        });
                } else {
                    // console.log(id.map(i => `idlist=${encodeURIComponent(i)}`).join('&'))
                    this.$modal
                        .confirm(`是否确认删除选中的 ${id.length} 条数据项？`)
                        .then(function () {
                            return delAssetCheck({ idList: id, type });
                        })
                        .then(() => {
                            this.getList();
                            this.$modal.msgSuccess('删除成功');
                        });
                }
            },
            /** 导出按钮操作 */
            handleExportCard() {
                let queryParams = {};
                if (this.ids.length === 0) {
                    queryParams = {
                        ...this.queryParams,
                        type: this.type,
                        dataRule: true,
                    };
                    delete queryParams.checkTime;
                    delete queryParams.departmentIdList;
                } else {
                    queryParams = {
                        checkIds: this.ids,
                        type: this.type,
                        dataRule: true,
                        status: this.queryParams.status,
                        statusList: this.queryParams.statusList,
                    };
                }
                // 先获取数据总数，等待结果后再显示确认框
                getAssetTotalCount(queryParams).then((res) => {
                    const export_count = res.data;

                    const confirmMessage =
                        this.ids.length === 0
                            ? `根据搜索条件，本次导出共有 ${export_count} 条数据，是否确认导出？`
                            : `根据选中条件，本次导出共有 ${export_count} 条数据，是否确认导出？`;

                    this.$confirm(confirmMessage, '提示', {
                        confirmButtonText: '确定',
                        cancelButtonText: '取消',
                        type: 'warning',
                    })
                        .then(() => {
                            const now = new Date(); // 添加 now 的定义
                            const timeStr = `${now.getFullYear()}年${
                                now.getMonth() + 1
                            }月${now.getDate()}日${now.getHours()}时${now.getMinutes()}分${now.getSeconds()}秒`;
                            const fileName = `${this.partsType[this.type]}_${timeStr}.xlsx`;
                            this.download(
                                'patrol/assetCheck/exportAssetReportCard',
                                {
                                    ...queryParams,
                                },
                                fileName,
                                {
                                    parameterType: 'body', // 设置参数类型为 body
                                    headers: {
                                        'Content-Type': 'application/json', // 设置请求头为 JSON
                                    },
                                }
                            );
                        })
                        .catch((e) => {
                            console.log(e);
                            this.$message({
                                type: 'info',
                                message: '已取消导出',
                            });
                        });
                });
            },

            /** 导出按钮操作 */
            handleExport() {
                let queryParams = {};
                if (this.ids.length === 0) {
                    queryParams = {
                        ...this.queryParams,
                        type: this.type,
                        dataRule: true,
                    };
                    delete queryParams.checkTime;
                    delete queryParams.departmentIdList;
                } else {
                    queryParams = {
                        checkIds: this.ids,
                        type: this.type,
                        dataRule: true,
                        status: this.queryParams.status,
                        statusList: this.queryParams.statusList,
                    };
                }
                // 先获取数据总数，等待结果后再显示确认框
                getAssetTotalCount(queryParams).then((res) => {
                    const export_count = res.data;

                    const confirmMessage =
                        this.ids.length === 0
                            ? `根据搜索条件，本次导出共有 ${export_count} 条数据，是否确认导出？`
                            : `根据选中条件，本次导出共有 ${export_count} 条数据，是否确认导出？`;

                    this.$confirm(confirmMessage, '提示', {
                        confirmButtonText: '确定',
                        cancelButtonText: '取消',
                        type: 'warning',
                    })
                        .then(() => {
                            const now = new Date(); // 添加 now 的定义
                            const timeStr = `${now.getFullYear()}年${
                                now.getMonth() + 1
                            }月${now.getDate()}日${now.getHours()}时${now.getMinutes()}分${now.getSeconds()}秒`;
                            const fileName = `${this.partsType[this.type]}_${timeStr}.xlsx`;
                            this.download(
                                'patrol/assetCheck/export',
                                {
                                    ...queryParams,
                                },
                                fileName,
                                {
                                    parameterType: 'body', // 设置参数类型为 body
                                    headers: {
                                        'Content-Type': 'application/json', // 设置请求头为 JSON
                                    },
                                }
                            );
                        })
                        .catch((e) => {
                            console.log(e);
                            this.$message({
                                type: 'info',
                                message: '已取消导出',
                            });
                        });
                });
            },
            /** 导入按钮操作 */
            handleImport() {
                this.upload.title = '用户导入';
                this.upload.open = true;
            },
            /** 下载模板操作 */
            importTemplate() {
                this.download('system/user/importTemplate', {}, `user_template.xlsx`);
            },
            // 文件上传中处理
            handleFileUploadProgress(event, file, fileList) {
                this.upload.isUploading = true;
            },
            // 文件上传成功处理
            handleFileSuccess(response, file, fileList) {
                this.upload.open = false;
                this.upload.isUploading = false;
                this.$refs.upload.clearFiles();
                this.$alert(
                    "<div style='overflow: auto;overflow-x: hidden;max-height: 70vh;padding: 10px 20px 0;'>" +
                        response.msg +
                        '</div>',
                    '导入结果',
                    { dangerouslyUseHTMLString: true }
                );
                this.getList();
            },
            // 提交上传文件
            submitFileForm() {
                this.$refs.upload.submit();
            },
            /** 处理生成对话框关闭 */
            handleGenerateClose(visible) {
                if (!visible) {
                    this.$nextTick(() => {
                        this.$refs.assetCheckGenerate.reset();
                    });
                }
            },
            /** 打开批量审核对话框 */
            handleBatchAuditAll() {
                // 重置表单数据
                this.batchAuditForm = {
                    types: [this.type],
                    checkStartTime: null,
                    checkEndTime: null,
                    checkTimeRange: [],
                    statusList: ['2'], // 默认待审核
                    deptId: null,
                    reviewerId: null,
                    reviewerName: null,
                    timeMode: 'exact', // 'exact'精确时间 'random'随机延迟
                    minDelayDays: 3,
                    maxDelayDays: 7,
                    auditMode: this.ids.length > 0 ? 'selected' : 'condition' // 如果有选中项则默认使用选中项模式
                };
                
                // 设置默认时间为当前时间
                const now = new Date();
                const year = now.getFullYear();
                const month = String(now.getMonth() + 1).padStart(2, '0');
                const day = String(now.getDate()).padStart(2, '0');
                const formattedDate = `${year}-${month}-${day}`;
                this.$set(this.batchAuditForm, "exactAuditTime", formattedDate);
                
                this.batchAuditDialogVisible = true;
            },
            
            /** 加载部门选项 */
            loadDeptOptions() {
                getTreeStruct({ types: 101 }).then((response) => {
                    this.deptOptions = response.data;
                });
            },
            
            /** 格式化部门树数据为扁平结构，用于el-select显示 */
            formatData(data) {
                let options = [];
                if (!data) return options;
                
                const traverseTree = (nodes) => {
                    nodes.forEach(node => {
                        options.push({ label: node.label, value: node.id });
                        if (node.children && node.children.length > 0) {
                            traverseTree(node.children);
                        }
                    });
                };
                
                traverseTree(data);
                return options;
            },
            
            /** 处理树节点点击 */
            handleNodeClick(item) {
                // 单选逻辑，不检查节点类型
                this.batchAuditForm.deptId = item.id;
                // 关闭下拉框
                if (this.$refs.deptSelect) {
                    this.$refs.deptSelect.blur();
                    this.$refs.deptSelect.visible = false;
                }
            },
            
            /** 处理标签移除 */
            removeTag(tag) {
                if (this.$refs.treeRef) {
                    let checkedNodes = this.$refs.treeRef.getCheckedNodes();
                    // 删除节点
                    for (let i = 0; i < checkedNodes.length; i++) {
                        if (checkedNodes[i].id == tag) {
                            checkedNodes.splice(i, 1);
                            break;
                        }
                    }
                    // 设置 tree 选中的节点
                    this.$refs.treeRef.setCheckedNodes(checkedNodes);
                    // 更新表单数据
                    this.batchAuditForm.deptId = checkedNodes.map((node) => node.id);

                    if (this.batchAuditForm.deptId.length === 0) {
                        // 如果没有选中的部门，清空审核人选择
                        this.batchAuditForm.reviewerId = null;
                        this.batchAuditForm.reviewerName = null;
                    }
                }
            },
            
            /** 清空选择 */
            selectClear() {
                if (this.$refs.treeRef) {
                    // 清空表单数据
                    this.batchAuditForm.deptId = null;
                    this.batchAuditForm.reviewerId = null;
                    this.batchAuditForm.reviewerName = null;
                }
            },
            
            /** 处理部门选择变化 */
            handleDeptChange() {
                if (!this.batchAuditForm.deptId) {
                    this.batchAuditForm.reviewerId = null;
                    this.batchAuditForm.reviewerName = null;
                }
            },
            
            /** 格式化用户树数据为扁平结构，用于el-select显示 */
            formatUserData(data) {
                let options = [];
                if (!data) return options;
                
                const traverseTree = (nodes) => {
                    nodes.forEach(node => {
                        if (node.type === 'user') {
                            options.push({ label: node.label, value: node.id });
                        }
                        if (node.children && node.children.length > 0) {
                            traverseTree(node.children);
                        }
                    });
                };
                
                traverseTree(data);
                return options;
            },
            
            /** 处理用户树节点点击 */
            handleUserNodeClick(item) {
                // 只有类型为user的节点可以被选择
                if (item.type === 'user') {
                    this.batchAuditForm.reviewerId = item.id;
                    this.batchAuditForm.reviewerName = item.label;
                    // 关闭下拉框
                    if (this.$refs.reviewerSelect) {
                        this.$refs.reviewerSelect.blur();
                        this.$refs.reviewerSelect.visible = false;
                    }
                }
            },
            
            /** 清空审核人选择 */
            clearReviewer() {
                this.batchAuditForm.reviewerId = null;
                this.batchAuditForm.reviewerName = null;
            },
            
            /** 过滤用户节点方法 */
            filterUserNode(value, data) {
                if (!value) return true;
                return data.label.indexOf(value) !== -1;
            },
            
            /** 加载审核人选项 */
            loadReviewerOptions() {
                // 使用types=111加载部门-用户树数据
                getTreeStruct({ types: 111 }).then((response) => {
                    this.reviewerOptions = response.data;
                });
            },
            
            /** 验证批量审核表单 */
            validateBatchAuditForm() {
                if (this.batchAuditForm.auditMode === 'condition') {
                    if (!this.checkTimeRange || this.checkTimeRange.length !== 2) {
                        this.$message.warning('请选择检查日期范围');
                        return false;
                    }
                    
                    if (!this.batchAuditForm.statusList || this.batchAuditForm.statusList.length === 0) {
                        this.$message.warning('请选择状态');
                        return false;
                    }
                    
                    if (!this.batchAuditForm.deptId) {
                        this.$message.warning('请选择部门');
                        return false;
                    }
                } else {
                    // 选中项模式验证
                    if (this.ids.length === 0) {
                        this.$message.warning('请先选择要审核的记录');
                        return false;
                    }
                }
                
                if (this.batchAuditForm.timeMode === 'exact' && !this.batchAuditForm.exactAuditTime) {
                    this.$message.warning('请选择精确审核时间');
                    return false;
                }
                
                if (this.batchAuditForm.timeMode === 'random') {
                    if (!this.batchAuditForm.minDelayDays || !this.batchAuditForm.maxDelayDays) {
                        this.$message.warning('请设置延迟天数范围');
                        return false;
                    }
                    
                    if (this.batchAuditForm.minDelayDays > this.batchAuditForm.maxDelayDays) {
                        this.$message.warning('最小延迟天数不能大于最大延迟天数');
                        return false;
                    }
                }
                
                // 验证审核人是否已选择
                if (!this.batchAuditForm.reviewerId) {
                    this.$message.warning('请选择审核人');
                    return false;
                }
                
                return true;
            },
            
            /** 提交批量审核 */
            submitBatchAudit(status) {
                if (!this.validateBatchAuditForm()) {
                    return;
                }
                
                let params = {
                    types: this.batchAuditForm.types,
                    reviewerId: this.batchAuditForm.reviewerId,
                    status: status
                };
                
                // 添加时间模式参数
                if (this.batchAuditForm.timeMode === 'exact') {
                    params.exactAuditTime = this.batchAuditForm.exactAuditTime;
                } else {
                    params.minDelayDays = this.batchAuditForm.minDelayDays;
                    params.maxDelayDays = this.batchAuditForm.maxDelayDays;
                }
                
                // 根据选择的模式设置不同的参数
                if (this.batchAuditForm.auditMode === 'condition') {
                    // 条件筛选模式
                    params.checkStartTime = this.checkTimeRange[0];
                    params.checkEndTime = this.checkTimeRange[1];
                    params.statusList = this.batchAuditForm.statusList;
                    params.deptId = this.batchAuditForm.deptId;
                    
                    // 创建用于查询总数的参数（包含完整时间部分）
                    const countParams = { ...params, type: this.type };
                    // countParams 删除 status条件
                    delete countParams.status;
                    if (countParams.checkStartTime) {
                        countParams.checkStartTime = countParams.checkStartTime + ' 00:00:00';
                    }
                    if (countParams.checkEndTime) {
                        countParams.checkEndTime = countParams.checkEndTime + ' 23:59:59';
                    }
                    
                    // 获取符合条件的记录数量
                    getAssetTotalCount(countParams).then((res) => {
                        const count = res.data;
                        const modeText = '符合条件的';
                        this.showConfirmAndExecute(status, modeText, count, params);
                    });
                } else {
                    // 已选择项模式
                    params.checkIds = this.ids;
                    const count = this.ids.length;
                    const modeText = '选中的';
                    this.showConfirmAndExecute(status, modeText, count, params);
                }
            },
            
            /** 显示确认对话框并执行批量审核 */
            showConfirmAndExecute(status, modeText, count, params) {
                this.$modal.confirm(`确认要批量${status === 3 ? '通过' : '驳回'}${modeText}${count}条数据吗？`).then(() => {
                    // 立即关闭对话框并提示后台运行
                    this.batchAuditDialogVisible = false;
                    this.$modal.msgSuccess(`批量${status === 3 ? '通过' : '驳回'}操作已在后台运行`);
                    
                    // 后台执行批量操作
                    batchComplete(params).then(() => {
                        // 操作完成后刷新列表
                        // this.getList();
                    }).catch(error => {
                        console.error('批量操作执行失败:', error);
                    });
                }).catch(() => {});
            },
            /** 批量修改审核时间操作 */
            handleUpdateAuditTime() {
                if (this.multiple) {
                    this.$message.warning('请至少选择一条记录');
                    return;
                }
                // 设置默认时间为当前时间
                const now = new Date();
                const year = now.getFullYear();
                const month = String(now.getMonth() + 1).padStart(2, '0');
                const day = String(now.getDate()).padStart(2, '0');
                const hours = String(now.getHours()).padStart(2, '0');
                const minutes = String(now.getMinutes()).padStart(2, '0');
                const seconds = String(now.getSeconds()).padStart(2, '0');
                this.newAuditTime = `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
                this.updateAuditTimeDialogVisible = true;
            },

            /** 提交修改审核时间 */
            submitUpdateAuditTime() {
                if (!this.newAuditTime) {
                    this.$message.warning('请选择审核时间');
                    return;
                }

                updateStatusByIds({
                    checkIds: this.ids,
                    type: this.type,
                    auditTime: this.newAuditTime
                }).then(() => {
                    this.updateAuditTimeDialogVisible = false;
                    this.newAuditTime = null;
                    this.getList();
                    this.$modal.msgSuccess('修改审核时间成功');
                });
            },
            
            /** 渲染用户树节点，突出显示用户节点 */
            renderUserTreeNode(h, { node, data }) {
                return h('span', {
                    style: {
                        color: data.type === 'user' ? '#409EFF' : '#909399',
                        fontWeight: data.type === 'user' ? '500' : 'normal',
                        backgroundColor: data.type === 'user' ? '' : '#f5f7fa',
                        padding: '5px 0',
                        display: 'block',
                        cursor: data.type === 'user' ? 'pointer' : 'default'
                    }
                }, node.label);
            },
            datePickerChanged(value) {
                this.$set(this.batchAuditForm, "exactAuditTime", value);
                // Force UI update when date is selected
                // this.$forceUpdate();
            }
        },
    };
</script>
<style scoped>
    .tableDiv {
        background-color: white;
        padding-bottom: 10px;
    }

    /*::v-deep .vue-treeselect__control {*/
    /*  height: auto;*/
    /*}*/

    .asset-check-edit-dialog ::v-deep .el-dialog__body {
        padding: 10px 20px;
        height: 75vh;
        overflow-y: auto;
        scrollbar-width: none·;
    }
    .export-preview-dialog ::v-deep .el-dialog__body {
        padding: 10px;
    }
    
    /* 树形选择组件样式 */
    ::v-deep .el-select-dropdown__item {
        padding: 0;
    }
    
    ::v-deep .el-select__tags {
        flex-wrap: nowrap;
        overflow: auto;
    }
    
    /* 非用户节点样式 - 使其显示为灰色 */
    ::v-deep .el-tree-node[aria-disabled="true"] > .el-tree-node__content {
        color: #909399 !important;
        background-color: #f5f7fa !important;
        cursor: not-allowed !important;
    }
    
    /* 可选用户节点样式 - 保持原样或稍微突出 */
    ::v-deep .el-tree-node:not([aria-disabled="true"]) > .el-tree-node__content {
        font-weight: 500;
        color: #409EFF;
    }
</style>
