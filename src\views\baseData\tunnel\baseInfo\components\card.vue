<template>
  <div>
    <el-dialog
      v-loading="loading"
      :title="title"
      :visible.sync="dialogVisible"
      :before-close="handleClose"
      :close-on-click-modal="false"
      width="80%"
    >
      <div style="overflow-y: auto;height: 65vh;">
        <el-descriptions
          :label-style="{'display': 'none'}"
          :content-style="{'height': '41px'}"
          :column="1"
          border
        >
          <el-descriptions-item>1.行政识别数据</el-descriptions-item>
        </el-descriptions>
        <el-descriptions
          :label-style="labelStyle"
          :content-style="contentStyle"
          :column="3"
          border
        >
          <el-descriptions-item
            v-for="(item,index) in fields1"
            :key="index"
          >
            <el-col
              :span="12"
              style="text-align: center; border-right: 1px solid #e6ebf5;height: 100%;line-height: 41px;"
            >
              {{ item.label }}
            </el-col>
            <el-col
              :span="12"
              style="text-align: center;height: 100%;line-height: 41px;"
            >
              <span v-if="(item.type === 'input' && !forView) || (item.type === 'stake' && !forView)">
                <el-input v-model="form[item.prop]"></el-input>
              </span>
              <span v-else-if="item.type === 'date' && !forView">
                <el-date-picker
                  v-model="form[item.prop]"
                  class="bridge-card-input"
                  style="width:100%"
                  type="date"
                  value-format="yyyy-MM-dd"
                />
              </span>
              <span v-else-if="item.type === 'dict'">
                <el-select
                  v-model="form[item.prop]"
                  style="width: 100%;text-align: center;"
                  placeholder=""
                  :disabled="forView"
                >
                  <el-option
                    v-for="i in dict.type[item.dict]"
                    :key="i.value"
                    :label="i.label"
                    :value="i.value"
                  />
                </el-select>
              </span>
              <span v-else-if="item.type === 'stake' && forView">
                {{ formatPile(form[item.prop]) }}
              </span>
              <!-- <span v-else-if="item.type === 'date' && forView">
                {{ parseTime(form[item.prop]) }}
              </span> -->
              <span v-else>{{ form[item.prop] }}</span>
            </el-col>
          </el-descriptions-item>
        </el-descriptions>
        <el-descriptions
          :label-style="{'display': 'none'}"
          :content-style="{'height': '41px', 'border-top': '0'}"
          :column="1"
          border
        >
          <el-descriptions-item>2.结构数据</el-descriptions-item>
        </el-descriptions>
        <el-descriptions
          :label-style="labelStyle"
          :content-style="contentStyle"
          :column="4"
          border
        >
          <el-descriptions-item
            v-for="(item,index) in fields2"
            :key="index"
          >
            <el-col
              :span="12"
              style="text-align: center; border-right: 1px solid #e6ebf5;height: 100%;line-height: 41px;"
            >
              {{ item.label }}
            </el-col>
            <el-col
              :span="12"
              style="text-align: center;height: 100%;line-height: 41px;"
            >
              <span v-if="item.type === 'input' && !forView">
                <el-input v-model="form[item.prop]"></el-input>
              </span>
              <span v-else-if="item.type === 'dict'">
                <el-select
                  v-model="form[item.prop]"
                  style="width: 100%;"
                  placeholder=""
                  :disabled="forView"
                >
                  <el-option
                    v-for="i in dict.type[item.dict]"
                    :key="i.value"
                    :label="i.label"
                    :value="i.value"
                  />
                </el-select>
              </span>
              <span v-else>{{ form[item.prop] }}</span>
            </el-col>
          </el-descriptions-item>
        </el-descriptions>
        <el-descriptions
          :label-style="{'display': 'none'}"
          :content-style="{'height': '41px', 'border-top': '0'}"
          :column="1"
          border
        >
          <el-descriptions-item>3.档案资料(全、不全或无)</el-descriptions-item>
        </el-descriptions>
        <el-descriptions
          :label-style="labelStyle"
          :content-style="contentStyle"
          :column="4"
          border
        >
          <el-descriptions-item
            v-for="(item,index) in fields3"
            :key="index"
          >
            <el-col
              :span="12"
              style="text-align: center; border-right: 1px solid #e6ebf5;height: 100%;line-height: 41px;"
            >
              {{ item.label }}
            </el-col>
            <el-col
              :span="12"
              style="text-align: center;height: 100%;line-height: 41px;"
            >
              <span v-if="forView">
                <dict-tag
                  :options="dict.type.base_archives_type"
                  :value="form[item.prop]"
                />
              </span>
              <span v-else-if="item.type === 'dict'">
                <el-select
                  v-model="form[item.prop]"
                  style="width: 100%;"
                  placeholder=""
                  :disabled="forView"
                >
                  <el-option
                    v-for="i in dict.type.base_archives_type"
                    :key="i.value"
                    :label="i.label"
                    :value="i.value"
                  />
                </el-select>
              </span>
              <span v-else-if="item.type === 'date'">
                <el-date-picker
                  v-model="form[item.prop]"
                  class="bridge-card-input"
                  style="width:100%"
                  type="date"
                  value-format="yyyy-MM-dd"
                />
              </span>
            </el-col>
          </el-descriptions-item>
        </el-descriptions>
        <el-descriptions
          :label-style="{'display': 'none'}"
          :content-style="{'height': '41px', 'border-top': '0', 'border-bottom': '0'}"
          :column="1"
          border
        >
          <el-descriptions-item>4.隧道检测评定历史</el-descriptions-item>
        </el-descriptions>
        <el-table
          height="30%"
          border
          :data="tableData1"
          :header-cell-style="{'height': '36px'}"
        >

          <el-table-column
            label="年份"
            align="center"
            prop="checkYear"
            min-width="120"
          />
          <el-table-column
            label="检查类型"
            align="center"
            prop="checkType"
            min-width="120"
            show-overflow-tooltip
          >
            <template slot-scope="scope">
              <dict-tag
                :options="dict.type.tunnel_periodic_detection_type"
                :value="scope.row.checkType"
              />
            </template>
          </el-table-column>
          <el-table-column
            label="检查名称"
            align="center"
            prop="checkName"
            min-width="120"
            show-overflow-tooltip
          />

          <el-table-column
            label="检查单位"
            align="center"
            prop="checkUnit"
            min-width="120"
            show-overflow-tooltip
          />

          <el-table-column
            label="开始时间"
            align="center"
            prop="startDate"
            min-width="120"
            show-overflow-tooltip
          />
          <el-table-column
            label="结束时间"
            align="center"
            prop="endDate"
            min-width="140"
            show-overflow-tooltip
          />

          <el-table-column
            label="检查结果"
            align="center"
            prop="checkResult"
            min-width="140"
            show-overflow-tooltip
          />
          <el-table-column
            label="养护建议"
            align="center"
            prop="suggestions"
            min-width="140"
            show-overflow-tooltip
          />
          <el-table-column
            label="评定单位"
            align="center"
            prop="assessmentUnit"
            min-width="140"
            show-overflow-tooltip
          />
          <el-table-column
            label="评定等级"
            align="center"
            prop="assessmentGrade"
            min-width="140"
            show-overflow-tooltip
          >
            <template slot-scope="scope">
              <dict-tag
                :options="dict.type.tunnel_assess_grade"
                :value="scope.row.assessmentGrade"
              />
            </template>
          </el-table-column>
          <el-table-column
            label="检查报告"
            align="center"
            prop="reportPath"
            min-width="140"
            show-overflow-tooltip
          >
            <template slot-scope="scope">
              <el-button
                v-if="scope.row.reportPath && scope.row.originalFilename"
                type="text"
                icon="el-icon-paperclip"
                @click.stop="handleDownload(scope.row)"
              >{{scope.row.originalFilename}}</el-button>
              <span v-else>无</span>
            </template>
          </el-table-column>
          <el-table-column
            label="报告编写人"
            align="center"
            prop="reportWriter"
            min-width="140"
            show-overflow-tooltip
            >
            
          </el-table-column>
          <el-table-column
            label="报告编制日期"
            align="center"
            prop="reportWriteDate"
            min-width="140"
            show-overflow-tooltip
          />
          <el-table-column
            label="负责人"
            align="center"
            prop="personInCharge"
            min-width="140"
            show-overflow-tooltip
          />
        </el-table>
        <el-descriptions
          :label-style="{'display': 'none'}"
          :content-style="{'height': '41px', 'border-top': '0', 'border-bottom': '0'}"
          :column="1"
          border
        >
          <el-descriptions-item>5.建设及维修记录</el-descriptions-item>
        </el-descriptions>
        <el-table
          height="30%"
          border
          :data="tableData2"
          :header-cell-style="{'height': '36px'}"
        >
          <el-table-column
            label="项目编码"
            align="center"
            prop="bridgeName"
            min-width="120"
            show-overflow-tooltip
          />
          <el-table-column
            label="年度"
            align="center"
            prop="bridgeName"
            min-width="120"
          />
          <el-table-column
            label="预计工期"
            align="center"
            prop="bridgeName"
            min-width="120"
            show-overflow-tooltip
          />
          <el-table-column
            label="预计开始时间"
            align="center"
            prop="bridgeName"
            min-width="120"
            show-overflow-tooltip
          />
          <el-table-column
            label="预计结束时间"
            align="center"
            prop="bridgeName"
            min-width="120"
            show-overflow-tooltip
          />
          <el-table-column
            label="处置范围"
            align="center"
            prop="bridgeName"
            min-width="120"
            show-overflow-tooltip
          />
          <el-table-column
            label="工程费用(万元)"
            align="center"
            prop="bridgeName"
            min-width="120"
            show-overflow-tooltip
          />
          <el-table-column
            label="工程分类"
            align="center"
            prop="bridgeName"
            min-width="120"
            show-overflow-tooltip
          />
          <el-table-column
            label="缺陷责任期"
            align="center"
            prop="bridgeName"
            min-width="120"
            show-overflow-tooltip
          />
          <el-table-column
            label="上报人"
            align="center"
            prop="bridgeName"
            min-width="120"
            show-overflow-tooltip
          />
          <el-table-column
            label="备注"
            align="center"
            prop="bridgeName"
            min-width="120"
            show-overflow-tooltip
          />
        </el-table>
        <el-descriptions
          :label-style="{'display': 'none'}"
          :content-style="{'height': '41px', 'border-top': '0'}"
          :column="1"
          border
        >
          <el-descriptions-item>6.隧道照片</el-descriptions-item>
        </el-descriptions>
        <el-descriptions
          :label-style="labelStyle"
          :content-style="contentStyle"
          :column="2"
          border
        >
          <el-descriptions-item>
            <el-col
              :span="12"
              style="border-right: 1px solid #e6ebf5; display: flex; justify-content: center;align-items: center;height: 100%"
            >
              进洞照片
            </el-col>
            <el-col
              :span="12"
              style="text-align: center;height: 100%;"
            >
              <span v-if="!forView">
                <ImageUpload
                  style="margin:10px"
                  v-model="form.inHoleImage"
                  :limit="1"
                />
              </span>
              <span v-else>
                <span v-if="form.inHoleImage">
                  <ImagePreview
                    width="148px"
                    height="148px"
                    style="margin:10px"
                    :owner-id="form.inHoleImage"
                  />
                </span>
                <span v-else>
                  <div style="width: 148px; height: 148px">
                  </div>
                </span>
              </span>
            </el-col>
          </el-descriptions-item>
          <el-descriptions-item>
            <el-col
              :span="12"
              style="border-right: 1px solid #e6ebf5; display: flex; justify-content: center;align-items: center;height: 100%"
            >
              出洞照片
            </el-col>
            <el-col
              :span="12"
              style="text-align: center;height: 100%;"
            >
              <span v-if="!forView">
                <ImageUpload
                  style="margin:10px"
                  v-model="form.outHoleImage"
                  :limit="1"
                />
              </span>
              <span v-else>
                <span v-if="form.outHoleImage">
                  <ImagePreview
                    width="148px"
                    height="148px"
                    style="margin:10px"
                    :owner-id="form.outHoleImage"
                  />
                </span>
                <span v-else>
                  <div style="width: 148px; height: 148px">
                  </div>
                </span>
              </span>
            </el-col>
          </el-descriptions-item>
        </el-descriptions>
        <el-descriptions
          :label-style="labelStyle"
          :content-style="contentStyle"
          :column="3"
          border
        >
          <el-descriptions-item
            v-for="(item,index) in fields6"
            :key="index"
          >
            <el-col
              :span="12"
              style="text-align: center; border-right: 1px solid #e6ebf5;height: 100%;line-height: 41px;"
            >
              {{ item.label }}
            </el-col>
            <el-col
              :span="12"
              style="text-align: center;height: 100%;line-height: 41px;"
            >
              <span v-if="item.type === 'input' && !forView">
                <el-input v-model="form[item.prop]"></el-input>
              </span>
              <span v-else-if="item.type === 'date' && !forView">
                <el-date-picker
                  v-model="form[item.prop]"
                  class="bridge-card-input"
                  style="width:100%"
                  type="date"
                  value-format="yyyy-MM-dd"
                />
              </span>
              <span v-else>{{ form[item.prop] }}</span>
            </el-col>
          </el-descriptions-item>
        </el-descriptions>
      </div>
      <div slot="footer">
        <el-button
          v-if="!forView"
          type="primary"
          @click="handleSubmit"
        >保 存</el-button>
        <el-button @click="handleClose">取 消</el-button>
      </div>
    </el-dialog>

  </div>
</template>

<script>
import dictionary from '../js/cardFields'
import { getCard } from '@/api/baseData/tunnel/baseInfo/index'
import { parseTime } from '@/utils/ruoyi'
import {
  getListPage,
} from '@/api/baseData/tunnel/periodic/index'
import { getFile } from '@/api/file'

export default {
  name: 'tunnel-card',
  props: {
    dialogVisible: {
      type: Boolean,
      default: false
    },
    forView: {
      type: Boolean,
      default: false
    },
    title: {
      type: String,
      default: '查看隧道卡片'
    },
    cardId: {
      type: undefined,
      default: ''
    },
    cardCode: {
      type: String,
      default: ''
    }
  },
  dicts: [
    'base_archives_type',
    'tunnel_lining_type',
    'tunnel_hole_style',
    'base_data_yes_no',
    'sys_route_grade',
    'tunnel_build_method',
    'tunnel_ventilation_facility',
    'tunnel_lighting_facility',
    'unnel_lining_material',
    'tunnel_assess_grade',
    'tunnel_periodic_detection_type',
    'tunnel_lining_material',
    'tunnel_section_form',
    'tunnel_door_type'
  ],
  components: {},
  data() {
    return {
      loading: false,
      labelStyle: {
        display: 'none'
      },
      contentStyle: { 'border-top': '0', padding: '0', height: '41px' },
      form: {},
      fields1: [],
      fields2: [],
      fields3: [],
      tableData1: [],
      tableData2: [],
      fields6: []
    }
  },
  created() {
    this.fields1 = dictionary.fields1
    this.fields2 = dictionary.fields2
    this.fields3 = dictionary.fields3
    this.fields6 = dictionary.fields6


  },
  methods: {
    async handleDownload(row) {
      if (!row.fileUrl) {
        this.$message.error('文件无法下载，未找到文件的URL')
        return
      }
      this.$modal
        .confirm('确认下载文件？')
        .then(async () => {
          const url = row.fileUrl
          try {
            const response = await fetch(url)
            if (!response.ok) {
              throw new Error('网络响应不是 2xx')
            }
            const blob = await response.blob()
            const link = document.createElement('a')
            link.href = URL.createObjectURL(blob)
            const finalFileName = row.originalFilename
            link.download = finalFileName
            document.body.appendChild(link)
            link.click()
            document.body.removeChild(link)
            URL.revokeObjectURL(link.href)
          } catch (error) {
            this.$message.error('文件无法下载，未找到文件的URL')
          }
        })
        .catch(() => {})
    },
    getForm() {
      this.loading = true
      getCard({ids:[this.cardId]})
      .then(res => {
        if (res) {
          this.form = res[0].tunnel
        }
      })
      .finally(() => {
        this.loading = false
      })

      getListPage({tunnelCode:this.cardCode, pageSize: 1000, pageNum: 1})
      .then(async res => {
        this.tableData1 = res.rows
        let dataList=JSON.parse(JSON.stringify(res.rows))
        for (let index = 0; index < dataList.length; index++) {
          const el = dataList[index]
          const r = await getFile({ ownerId: el.reportPath })
          if (r.code === 200) {
            el.originalFilename = r.data?.originalFilename
            el.fileUrl = r.data?.url
          }
        }
        this.tableData1 = dataList
      })
    },
    handleSubmit() {},
    handleClose() {
      if (this.forView) {
        this.$emit('close', false)
      } else {
        this.$modal
          .confirm('确认退出？')
          .then(() => {
            this.form = {}
            this.$emit('close', false)
          })
          .catch(() => {})
      }
    }
  },
  computed: {},
  watch: {
    cardId: {
      handler(val) {
        if (val) {
          this.getForm()
        }
      },
      deep: true
    }
  }
}
</script>

<style lang="scss" scoped>

::v-deep .el-select .el-input.is-disabled .el-input__inner:hover{
  text-align: center;
}

::v-deep .el-select .el-input.is-disabled .el-input__inner{
  text-align: center;
}

::v-deep {
  .el-input__inner {
    border: 0;
  }
  .el-input.is-disabled .el-input__inner {
    background-color: white;
    border-color: #dfe4ed;
    color: #606266;
  }
}
</style>
