<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" label-width="68px">



<!--      <el-form-item label="" prop="roadSectionCode">-->
<!--        <el-input-->
<!--          v-model="queryParams.roadSectionCode"-->
<!--          placeholder="请输入公路路段编号"-->
<!--          clearable-->
<!--          @keyup.enter.native="handleQuery"-->
<!--        />-->
<!--      </el-form-item>-->

      <el-form-item label="" prop="roadSectionCode">
        <el-select v-model="queryParams.roadSectionCode" clearable filterable placeholder="请选择路段编码">
          <el-option
            v-for="item in routeList"
            :key="item.routeId"
            :label="item.routeCode"
            :value="item.routeCode">
          </el-option>
        </el-select>
      </el-form-item>

      <el-form-item label="" prop="roadSectionName">
        <el-input
          v-model="queryParams.roadSectionName"
          placeholder="请输入公路路段名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="" prop="state">
        <el-select v-model="queryParams.state" placeholder="请选择运营状态" clearable>
          <el-option
            v-for="dict in dict.type.sys_operation_state"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="" prop="ownerUnit">
        <el-select v-model="queryParams.ownerUnit" placeholder="请选择权属类型" clearable>
          <el-option label="集团公司" value="集团公司"></el-option>
          <el-option label="项目公司" value="项目公司"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['system:highwaySections:add']"
        >新增
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['system:highwaySections:edit']"
        >修改
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['system:highwaySections:remove']"
        >删除
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['system:highwaySections:export']"
        >导出
        </el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <!--数据表格开始-->
    <div class="tableDiv">
      <el-table v-adjust-table size="mini" :height="showSearch ? 'calc(100vh - 320px)' : 'calc(100vh - 260px)'" border
                v-loading="loading" :data="highwaySectionsList" @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" align="center"/>
        <el-table-column label="序号"     fixed align="center" type="index" width="50">
          <template v-slot="scope">
            {{ (scope.$index + (queryParams.pageNum - 1) * queryParams.pageSize)+1 }}
          </template>
        </el-table-column>
        <el-table-column label="公路路段名称" width="120" fixed :show-overflow-tooltip="true" align="center" prop="roadSectionName"/>
        <el-table-column label="公路路段编号" width="120" fixed :show-overflow-tooltip="true" align="center" prop="roadSectionCode"/>
        <el-table-column label="路线等级" :show-overflow-tooltip="true" align="center" prop="roadGrade">
          <template slot-scope="scope">
            <dict-tag :options="dict.type.sys_route_grade" :value="scope.row.roadGrade"/>
          </template>
        </el-table-column>
        <el-table-column label="通车时间" :show-overflow-tooltip="true" align="center" prop="openingTime" width="180">
          <template slot-scope="scope">
            <span>{{ parseTime(scope.row.openingTime, '{y}-{m}-{d}') }}</span>
          </template>
        </el-table-column>
        <el-table-column label="车道数"  width="120"  :show-overflow-tooltip="true" align="center" prop="lanes"/>
        <el-table-column label="起点名称"  :show-overflow-tooltip="true" align="center" prop="placeStartName"/>
        <el-table-column label="终点名称"  :show-overflow-tooltip="true"align="center" prop="placeEndName"/>
        <el-table-column label="施工起点桩号"  :show-overflow-tooltip="true" align="center" width="120" prop="constructionPileStart"
                         :formatter="(...arg)=>formatPile(arg[2])"/>
        <el-table-column label="施工终点桩号"  :show-overflow-tooltip="true" align="center" width="120" prop="constructionPileEnd"
                         :formatter="(...arg)=>formatPile(arg[2])"/>
        <el-table-column label="统一里程起点桩号" :show-overflow-tooltip="true"  align="center" width="140" prop="unifiedMileagePileStart"
                         :formatter="(...arg)=>formatPile(arg[2])"/>
        <el-table-column label="统一里程终点桩号" :show-overflow-tooltip="true"  align="center" width="140" prop="unifiedMileagePileEnd"
                         :formatter="(...arg)=>formatPile(arg[2])"/>
        <el-table-column label="施工里程(km)" :show-overflow-tooltip="true" width="120" align="center" prop="constructionMileage"
                         :formatter="(...arg)=>{if(arg[2]) return (arg[2]/1000).toFixed(3).toLocaleString()}"/>
        <el-table-column label="养护里程(km)" :show-overflow-tooltip="true"  width="120" align="center" prop="maintenanceMileage"
                         :formatter="(...arg)=>{if(arg[2]) return (arg[2]/1000).toFixed(3).toLocaleString()}"/>
        <el-table-column label="产权单位" :show-overflow-tooltip="true"  align="center" prop="propertyUnitName" width="240"/>
        <el-table-column label="运营状态" :show-overflow-tooltip="true" align="center" prop="state" width="120">
          <template v-slot="scope">
            <dict-tag :options="dict.type.sys_operation_state" :value="scope.row.state"/>
          </template>
        </el-table-column>
        <el-table-column label="备注" :show-overflow-tooltip="true"  align="center" prop="dse"/>
        <el-table-column label="操作" align="center" width="180" class-name="small-padding fixed-width" fixed="right">
          <template slot-scope="scope">
            <el-button
              size="mini"
              type="text"
              icon="el-icon-edit"
              @click="handleUpdate(scope.row)"
              v-hasPermi="['system:highwaySections:edit']"
            >修改
            </el-button>
            <el-button
              size="mini"
              type="text"
              icon="el-icon-delete"
              @click="handleDelete(scope.row)"
              v-hasPermi="['system:highwaySections:remove']"
            >删除
            </el-button>
            <el-button
              size="mini"
              type="text"
              icon="el-icon-view"
              @click="onDetail(scope.row)"
              v-hasPermi="['system:highwaySections:query']"
            >查看
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <pagination
        :totalMileage="(totalMileage/1000).toFixed(3).toLocaleString()"
        v-show="total>0"
        :total="total"
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
        @pagination="getList"
      >
        <span slot="mileage"  style="margin-left: 50px;" v-text="'总施工里程：' + (constructionMileage/1000).toFixed(3).toLocaleString() + ' km'"></span>
      </pagination>
    </div>
    <!--数据表格结束-->
    <!-- 添加或修改公路路段管理对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="1000px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="140px" inline>
        <el-form-item label="公路路段名称" prop="roadSectionName">
          <el-input v-model="form.roadSectionName" placeholder="请输入公路路段名称"/>
        </el-form-item>
        <el-form-item label="产权单位" prop="propertyUnitId" >
          <treeselect v-model="form.propertyUnitId" :options="deptOptions" :show-count="true"
                      placeholder="请选择产权单位"/>
        </el-form-item>
<!--        <el-form-item label="公路路段编号" prop="roadSectionCode">-->
<!--          <el-input v-model="form.roadSectionCode" placeholder="请输入公路路段编号"/>-->
<!--        </el-form-item>-->
        <el-form-item label="路线等级" prop="roadGrade">
          <el-select v-model="form.roadGrade" placeholder="请选择路线等级">
            <el-option
              v-for="dict in dict.type.sys_route_grade"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="车道数" prop="lanes">


          <el-select v-model="form.lanes" placeholder="请选择车道数" clearable multiple>
            <el-option
              v-for="dict in lanesOptions"
              :label="dict"
              :value="dict"
            />
          </el-select>
<!--          <el-input v-model="form.lanes" placeholder="请输入车道数"/>-->
        </el-form-item>
        <el-form-item label="起点名称" prop="placeStartName">
          <el-input v-model="form.placeStartName" placeholder="请输入起点名称"/>
        </el-form-item>
        <el-form-item label="终点名称" prop="placeEndName">
          <el-input v-model="form.placeEndName" placeholder="请输入终点名称"/>
        </el-form-item>
        <el-form-item label="施工起点桩号" prop="constructionPileStart">
          <PileInput v-model="form.constructionPileStart" @input="onPileChange(1)" class="pile-input"
                     placeholder="请输入施工起点桩号"></PileInput>
        </el-form-item>
        <el-form-item label="施工终点桩号" prop="constructionPileEnd">
          <PileInput v-model="form.constructionPileEnd" @input="onPileChange(1)" class="pile-input"
                     placeholder="请输入施工终点桩号"></PileInput>
        </el-form-item>
        <el-form-item label="统一里程起点桩号" prop="unifiedMileagePileStart">
          <PileInput v-model="form.unifiedMileagePileStart" @input="onPileChange(2)" class="pile-input"
                     placeholder="请输入统一里程起点桩号"></PileInput>
        </el-form-item>
        <el-form-item label="统一里程终点桩号" prop="unifiedMileagePileEnd">
          <PileInput v-model="form.unifiedMileagePileEnd" @input="onPileChange(2)" class="pile-input"
                     placeholder="请输入统一里程终点桩号"></PileInput>
        </el-form-item>
        <el-form-item label="施工里程(m)" prop="constructionMileage">
          <el-input v-model="form.constructionMileage" placeholder="请输入施工里程(m)"/>
        </el-form-item>
        <el-form-item label="养护里程(m)" prop="maintenanceMileage">
          <el-input v-model="form.maintenanceMileage" placeholder="请输入养护里程(m)"/>
        </el-form-item>
        <el-form-item label="通车时间" prop="openingTime">
          <el-date-picker clearable
                          v-model="form.openingTime"
                          type="date"
                          value-format="yyyy-MM-dd"
                          placeholder="请选择通车时间">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="运营状态" prop="state">
          <el-select v-model="form.state" placeholder="请选择运营状态">
            <el-option
              v-for="dict in dict.type.sys_operation_state"
              :key="dict.value"
              :label="dict.label"
              :value="parseInt(dict.value)"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="备注" prop="dse" style="width: 100%" >
          <el-input v-model="form.dse" type="textarea" :rows="3" placeholder="请输入备注"  />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
    <HighwaySectionsDetail :visible.sync="showDetail" :data="currentRow"  title="详情" :append-to-body="true" size="60%"></HighwaySectionsDetail>
  </div>
</template>

<script>
import {
  listHighwaySections,
  getHighwaySections,
  delHighwaySections,
  addHighwaySections,
  updateHighwaySections, getTotalMileage
} from "@/api/system/highwaySections";
import {listAllRoute, routeCacheClear} from "@/api/system/route";
import Treeselect from "@riophae/vue-treeselect";
import "@riophae/vue-treeselect/dist/vue-treeselect.css";
import {deptTreeSelect} from "@/api/system/user";
import PileInput from "@/views/system/route/pileinput.vue";
import HighwaySectionsDetail from "@/views/system/highwaySections/detail.vue";

export default {
  name: "HighwaySections",
  components: {HighwaySectionsDetail, PileInput, Treeselect},
  dicts: ['sys_route_grade', 'sys_operation_state'],
  data() {
    return {
      routeList: [],

      lanesOptions: ['2', '4', '6', '8'],
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: false,
      currentRow: {},
      showDetail: false,
      // 总条数
      total: 0,
      // 总养护里程
      totalMileage: 0,
      // 施工里程
      constructionMileage: 0,
      // 部门树选项
      deptOptions: undefined,
      // 公路路段管理表格数据
      highwaySectionsList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 50,
        state: '2',
        ownerUnit: null,
        roadSectionCode: null,
        roadSectionName: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        state: [
          {required: true, message: "运营状态不能为空", trigger: "blur"}
        ],
        roadSectionCode: [
          {required: true, message: "公路路段编码不能为空", trigger: "blur"}
        ],
        roadSectionName: [
          {required: true, message: "公路路段名称不能为空", trigger: "blur"}
        ],
        roadGrade: [
          {required: true, message: "路线等级不能为空", trigger: "blur"}
        ],
        lanes: [
          {required: true, message: "车道数不能为空", trigger: "blur"},
          // {type: 'number', message: '请输入数字', trigger: 'blur'}
        ],
        openingTime: [
          {required: true, message: "通车时间不能为空", trigger: "blur"}
        ],
        propertyUnitId: [
          {required: true, message: "产权单位", trigger: "change"}
        ],
        constructionMileage1: [
          {type: 'number', message: '请输入数字', trigger: 'blur'}
        ],
        maintenanceMileage1: [
          {type: 'number', message: '请输入数字', trigger: 'blur'}
        ]
      }
    };
  },
  async created() {
    this.getRouteList()
    await this.getDeptTree();
    this.getList();
  },
  methods: {
    onDetail(row){
      this.currentRow = row
      this.showDetail = true
    },
    onPileChange(type) {
      if (type === 1)
        if (!isNaN(this.form.constructionPileEnd) && !isNaN(this.form.constructionPileStart))
          this.form.constructionMileage = (this.form.constructionPileEnd - this.form.constructionPileStart).toFixed(2)
      if (type === 2)
        if (!isNaN(this.form.unifiedMileagePileEnd) && !isNaN(this.form.unifiedMileagePileStart))
          this.form.maintenanceMileage = (this.form.unifiedMileagePileEnd - this.form.unifiedMileagePileStart).toFixed(2)
    },
    /** 查询公路路段管理列表 */
    getList() {
      this.loading = true;
      listHighwaySections(this.queryParams).then(response => {
        this.highwaySectionsList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
      this.getTotalMileage();
    },
    /** 查询总养护里程 */
    getTotalMileage() {
      getTotalMileage(this.queryParams).then(response => {
        console.log(response,'dd')

        this.totalMileage = response.data.maintenanceMileage;
        this.constructionMileage = response.data.constructionMileage;
      });
    },
    getRouteList() {
      listAllRoute().then(res => {
        this.routeList = res.data
      })
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        roadSectionId: null,
        roadSectionCode: null,
        roadSectionName: null,
        openingTime: null,
        lanes: null,
        placeStartName: null,
        placeEndName: null,
        constructionPileStart: null,
        constructionPileEnd: null,
        unifiedMileagePileStart: null,
        unifiedMileagePileEnd: null,
        constructionMileage: null,
        maintenanceMileage: null,
        propertyUnitId: null,
        createBy: null,
        createdTime: null,
        updatedBy: null,
        updatedTime: null,
        dse: null,
        state: 2,
        roadGrade: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.roadSectionId)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加公路路段管理";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const roadSectionId = row.roadSectionId || this.ids
      getHighwaySections(roadSectionId).then(response => {
        this.form = response.data;
        this.form.lanes =  this.form.lanes.split(',')
        this.open = true;
        this.title = "修改公路路段管理";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          this.form.lanes =  this.form.lanes.join(',')
          if (this.form.roadSectionId != null) {
            updateHighwaySections(this.form).then(res => {
              if (res.code === 200) {
                this.$modal.msgSuccess("修改成功");
                this.open = false;
                this.getList();
                routeCacheClear()
              }
            });
          } else {
            addHighwaySections(this.form).then(res => {
              if (res.code === 200) {
                this.$modal.msgSuccess("新增成功");
                this.open = false;
                this.getList();
              }
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const roadSectionIds = row.roadSectionId || this.ids;
      let str = row.roadSectionCode?"编号为"+row.roadSectionCode:"选中"
      this.$modal.confirm('是否确认删除' + str + '的数据？').then(function () {
        return delHighwaySections(roadSectionIds);
      }).then((res) => {
        if (res.code === 200) {
          routeCacheClear()
          this.getList();
          this.$modal.msgSuccess("删除成功");
        }
      }).catch(() => {
      });
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('system/highwaySections/export', {
        ...this.queryParams
      }, `公路路段信息导出清单_${new Date().getTime()}.xlsx`)
    },
    /** 查询部门下拉树结构 */
    getDeptTree() {
      return deptTreeSelect().then(response => {
        this.deptOptions = response.data;
      });
    },
  }
};
</script>
<style scoped>
.app-container form:first-child .el-select,
.app-container form:nth-child(2) .el-select,
.app-container form:nth-child(2) ::v-deep .el-form-item__content,
.app-container form:first-child ::v-deep .el-form-item__content {
  width: 240px;
}
.app-container  form:first-child  .el-form-item:last-child ::v-deep .el-form-item__content {
  width: auto;
}

.el-dialog .el-input,
.el-dialog .pile-input,
.el-dialog .el-select,
.el-dialog .el-date-editor,
.el-dialog .el-textarea,
.el-dialog .vue-treeselect {
  width: 300px;
}

.el-dialog .el-textarea{
  width: 752px;
}
.tableDiv {
  background-color: white;
  padding-bottom: 10px;
}
</style>
