<template>
  <PageContainer>
    <template slot="search">
      <div style="display: flex; align-items: center; margin: 0">
        <CascadeSelection
          :form-data="queryParams"
          v-model="queryParams"
          types="201"
          multiple
        />
        <div style="margin-left: 20px">
          <el-input
            v-model="queryParams.tunnelName"
            style="width: 100%"
            placeholder="隧道名称"
            clearable
          />
        </div>
        <div style="margin-left: 20px">
          <el-input
            v-model="queryParams.tunnelCode"
            style="width: 100%"
            placeholder="隧道编码"
            clearable
          />
        </div>
        <div style="margin-left: 20px">
          <el-date-picker
            style="width: 100%"
            v-model="queryParams.checkYear"
            type="year"
            placeholder="年份"
            value-format="yyyy"
          />
        </div>
        <div style="margin-left: 20px">
          <el-select
            v-model="queryParams.checkType"
            style="width: 100%"
            placeholder="检查类型"
            clearable
          >
            <el-option
              v-for="item in dict.type.tunnel_periodic_detection_type"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </div>
        <div style="margin: 0 20px">
          <el-select
            v-model="queryParams.assessmentGrade"
            placeholder="评定等级"
            clearable
          >
            <el-option
              v-for="dict in dict.type.tunnel_assess_grade"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </div>
        <div style="min-width: 240px">
          <el-button
            v-hasPermi="['baseData:tunnelDetection:listPage']"
            type="primary"
            icon="el-icon-search"
            @click="handleQuery"
            >搜索</el-button
          >
          <el-button icon="el-icon-refresh" @click="resetQuery">重置</el-button>
        </div>
      </div>
    </template>
    <template slot="header">
      <div class="button-list">
        <el-button
          v-hasPermi="['baseData:tunnelDetection:add']"
          type="primary"
          @click="handleAdd"
          >新增</el-button
        >
        <el-button
          v-hasPermi="['baseData:tunnelDetection:edit']"
          type="primary"
          @click="handleEdit"
          >编辑</el-button
        >
        <el-button
          v-hasPermi="['baseData:tunnelDetection:remove']"
          type="primary"
          @click="handleRemove"
          >删除</el-button
        >
        <el-button
          class="mb8"
          v-hasPermi="['baseData:tunnelDetection:recheck']"
          type="primary"
          @click="recheckData"
          >复核</el-button
        >

        <el-button
          v-hasPermi="['baseData:tunnelDetection:recheck']"
          class="mb8"
          type="primary"
          @click="deleteRecheckData"
          >取消复核</el-button
        >
        <el-button v-hasPermi="['baseData:import:execute']" type="primary" @click="importUpdate">导入更新</el-button>
        <el-button v-hasPermi="['baseData:import:execute']" type="primary" @click="importAdd">导入新增</el-button>

        <el-button
          v-hasPermi="['baseData:tunnelDetection:import']"
          type="primary"
          class="mb8"
          @click="importZip"
          >导入附件</el-button
        >

        <el-button
          v-hasPermi="['baseData:tunnelDetection:export']"
          type="primary"
          class="mb8"
          @click="exportList"
          >数据导出</el-button
        >

        <el-button
          v-hasPermi="['baseData:tunnelDetection:exportReport']"
          type="primary"
          class="mb8"
          @click="exportYearList"
          :disabled="this.ids.length ? true : false"
          >导出隧道年度评定结果</el-button
        >
        <el-button
          v-hasPermi="['baseData:tunnelDetection:exportAll']"
          class="mb8"
          type="primary"
          @click="evaluateExport"
          :disabled="this.ids.length ? true : false"
          >导出评定结果</el-button
        >
        <el-button
          v-hasPermi="['baseData:import:execute']"
          class="mb8"
          type="primary"
          @click="exportAll"
          >导入评定结果</el-button
        >
      </div>
    </template>
    <template slot="body">
      <el-table
        v-adjust-table
        ref="table"
        v-loading="loading"
        height="99%"
        border
        :data="tableData"
        :header-cell-style="{ height: '36px' }"
        :row-style="rowStyle"
        @selection-change="handleSelectionChange"
        @row-click="handleRowClick"
      >
        <el-table-column fixed type="selection" width="50" align="center" />
        <el-table-column label="序号" type="index" width="50" align="center">
          <template v-slot="scope">
            {{
              scope.$index +
              (queryParams.pageNum - 1) * queryParams.pageSize +
              1
            }}
          </template>
        </el-table-column>
        <el-table-column
          label="年度"
          align="center"
          prop="checkYear"
          min-width="120"
          show-overflow-tooltip
        />
        <el-table-column
          label="隧道名称"
          align="center"
          prop="tunnelName"
          min-width="120"
          show-overflow-tooltip
        />
        <el-table-column
          label="隧道编码"
          align="center"
          prop="tunnelCode"
          min-width="120"
          show-overflow-tooltip
        />
        <el-table-column
          label="管理处"
          align="center"
          prop="managementMaintenanceName"
          min-width="120"
          show-overflow-tooltip
        />
        <el-table-column
          label="管养分处"
          align="center"
          prop="managementMaintenanceBranchName"
          min-width="120"
          show-overflow-tooltip
        />
        <el-table-column
          label="养护路段"
          align="center"
          prop="maintenanceSectionName"
          min-width="120"
          show-overflow-tooltip
        />
        <el-table-column
          label="路线编码"
          align="center"
          prop="routeCode"
          min-width="120"
          show-overflow-tooltip
        />
        <el-table-column
          label="检查类型"
          align="center"
          prop="checkType"
          min-width="120"
          show-overflow-tooltip
        >
          <template slot-scope="scope">
            <dict-tag
              :options="dict.type.tunnel_periodic_detection_type"
              :value="scope.row.checkType"
            />
          </template>
        </el-table-column>
        <el-table-column
          label="检查描述"
          align="center"
          prop="remark"
          min-width="120"
          show-overflow-tooltip
        />
        <el-table-column
          label="检查名称"
          align="center"
          prop="checkName"
          min-width="120"
          show-overflow-tooltip
        />
        <!-- <el-table-column
          label="养护单位"
          align="center"
          prop="managementMaintenanceName"
          min-width="120"
          show-overflow-tooltip
        /> -->
        <el-table-column
          label="检查单位"
          align="center"
          prop="checkUnit"
          min-width="120"
          show-overflow-tooltip
        />
        <!-- <el-table-column
          label="检查描述"
          align="center"
          prop="narrative"
          min-width="120"
          show-overflow-tooltip
        /> -->
        <!-- <el-table-column
          label="开始时间"
          align="center"
          prop="startDate"
          min-width="120"
          show-overflow-tooltip
        /> -->
        <!-- <el-table-column
          label="结束时间"
          align="center"
          prop="endDate"
          min-width="120"
          show-overflow-tooltip
        /> -->
        <el-table-column
          label="检查日期"
          align="center"
          prop="checkDate"
          min-width="120"
          show-overflow-tooltip
        />
        <el-table-column
          label="检查评分"
          align="center"
          prop="checkResult"
          min-width="120"
          show-overflow-tooltip
        />
        <el-table-column
          label="养护建议"
          align="center"
          prop="suggestions"
          min-width="120"
          show-overflow-tooltip
        />
        <el-table-column
          label="评定单位"
          align="center"
          prop="assessmentUnit"
          min-width="120"
          show-overflow-tooltip
        />
        <el-table-column
          label="评定等级"
          align="center"
          prop="assessmentGrade"
          min-width="120"
          show-overflow-tooltip
        >
          <template slot-scope="scope">
            <dict-tag
              :options="dict.type.tunnel_assess_grade"
              :value="scope.row.assessmentGrade"
            />
          </template>
        </el-table-column>
        <el-table-column
          label="检查报告"
          align="center"
          prop="originalFilename"
          min-width="300"
          show-overflow-tooltip
        >
          <template slot-scope="scope">
            <el-button
              v-if="scope.row.reportPath"
              type="text"
              icon="el-icon-paperclip"
              @click.stop="handlePreview(scope.row)"
              >{{ scope.row.reportName }}
            </el-button
            >
          </template>
        </el-table-column>
        <el-table-column
          label="报告编写人"
          align="center"
          prop="reportWriter"
          min-width="120"
          show-overflow-tooltip
        />
        <el-table-column
          label="报告编制日期"
          align="center"
          prop="reportWriteDate"
          min-width="120"
          show-overflow-tooltip
        />
        <el-table-column
          label="负责人"
          align="center"
          prop="personInCharge"
          min-width="120"
          show-overflow-tooltip
        />
        <el-table-column
          label="复核单位名称"
          align="center"
          prop="recheckOrganization"
          min-width="140"
          show-overflow-tooltip
        />
        <el-table-column
          label="复核人"
          align="center"
          prop="recheckPerson"
          min-width="140"
          show-overflow-tooltip
        />
        <el-table-column
          label="复核时间"
          align="center"
          prop="recheckTime"
          min-width="140"
          show-overflow-tooltip
        />
        <el-table-column
          label="记录人"
          align="center"
          prop="createBy"
          min-width="140"
          show-overflow-tooltip
        />
        <el-table-column
          label="记录时间"
          align="center"
          prop="createTime"
          min-width="140"
          show-overflow-tooltip
        />
        <el-table-column
          fixed="right"
          label="明细"
          align="center"
          min-width="120"
        >
          <template slot-scope="scope">
            <el-button
              style="font-size: 14px; font-weight: 400"
              type="text"
              @click.stop="handleView(scope.row)"
              >查看</el-button
            >
          </template>
        </el-table-column>
      </el-table>

      <pagination
        :total="total"
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
        :pageSizes="[10, 20, 30, 50, 100, 1000]"
        @pagination="getList"
      />
    </template>
    <Form
      v-if="showDialog"
      :title="title"
      :showDialog="showDialog"
      :forEdit="forEdit"
      :formData="formData"
      @close="closeForm"
    />
    <ImportData
      v-if="showImport"
      :is-update="this.isUpdate"
      :dialog-visible="showImport"
      :import-base-type="importBaseType"
      :import-type="importType"
      @close="closeImport"
    />
    <ImportZip
      v-if="showImportZip"
      :is-update="false"
      :dialog-visible="showImportZip"
      :import-base-type="importBaseType"
      :import-type="importType"
      @close="closeImportZip"
    />
    <Detail
      v-if="showDetail"
      :showDetail="showDetail"
      :choseId="choseId"
      @close="
        () => {
          showDetail = false;
        }
      "
    />
      <FilePreview :office-preview-visible="officePreviewVisible" :owner-id="ownerId" @close="officePreviewVisible=false"/>
  </PageContainer>
</template>

<script>
import CascadeSelection from "@/components/CascadeSelection/index.vue";
import ImportData from "@/views/baseData/components/importData/index.vue";

import Form from "./form.vue";
import {
  getListPage,
  deleteByIds,
  getInfoById,
  detectionRecheck,
  detectionRollback,
} from "@/api/baseData/tunnel/periodic/index";
import { getFile } from "@/api/file";
import Detail from "./components/detail.vue";
import ImportZip from "./components/importZip.vue";
import FilePreview from "@/components/FilePreview/index.vue";


export default {
  name: "Periodic",
  props: {},
  components: {FilePreview, CascadeSelection, Form, ImportData, Detail,ImportZip },
  dicts: ["tunnel_periodic_detection_type", "tunnel_assess_grade"],
  data() {
    return {
      loading: false,
      queryParams: {
        pageNum: 1,
        pageSize: 20,
        checkYear: new Date().getFullYear().toString(),
      },
      total: 0,
      tableData: [],
      title: "",
      forEdit: false,
      showDialog: false,
      formData: {},
      ids: [],
      showImport: false,
      isUpdate: false,
      showImportZip:false,
      importType: 1,
      showDetail: false,
      choseId: "",
      officePreviewVisible:false,
      ownerId:''
    };
  },
  created() {
    this.getList();
  },
  methods: {
    async handlePreview(row) {
      this.officePreviewVisible=true;
      this.ownerId=row.reportPath;
    },
    getList() {
      this.loading = true;
      getListPage(this.queryParams)
        .then(async (res) => {
          if (res.code === 200) {
            this.tableData = res.rows;
            this.total = res.total;
            this.loading = false;
            let dataList = JSON.parse(JSON.stringify(res.rows));
            for (let index = 0; index < dataList.length; index++) {
              const el = dataList[index];
            }
            this.tableData = dataList;
          }
        })
        .finally(() => {
          this.loading = false;
        });
    },
    handleQuery() {
      this.getList();
    },
    resetQuery() {
      this.queryParams = {
        pageNum: 1,
        pageSize: 20,
      };
      this.getList();
    },
    handleAdd() {
      this.title = "新增隧道定期数据";
      this.forEdit = false;
      this.showDialog = true;
    },
    handleEdit() {
      if (this.ids.length !== 1) {
        this.$message.warning("请选择一条数据编辑");
        return;
      }
      getInfoById(this.ids[0]).then((res) => {
        if (res.code === 200) {
          this.formData = res.data;
          this.forEdit = true;
          this.showDialog = true;
          this.title = "编辑隧道定期数据";
        }
      });
    },
    closeImport(v) {
      this.showImport = false;
      if (v) this.getList();
    },
    closeImportZip(v) {
      this.showImportZip = false;
      if (v) this.getList();
    },
    // 导入更新按钮
    importUpdate() {
      this.isUpdate = true;
      this.showImport = true;
      this.importBaseType = "18";
      this.importType = 1;
    },
    // 导入新增按钮
    importAdd() {
      this.isUpdate = false;
      this.showImport = true;
      this.importBaseType = "18";
      this.importType = 2;
    },
    // 导入附件按钮
    importZip() {
      this.isUpdate = false;
      this.showImportZip = true;
      this.importBaseType = "18";
      this.importType = 2;
    },
    handleRemove() {
      if (this.ids.length === 0) {
        this.$message.warning("请选择至少一条数据进行删除！");
        return;
      }
      this.$modal
        .confirm("确认删除？")
        .then(() => {
          deleteByIds(this.ids).then((res) => {
            if (res.code === 200) {
              this.getList();
              this.$modal.msgSuccess("删除成功");
            }
          });
        })
        .catch(() => {});
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id);
    },
    recheckData() {
      //出现弹出框并且要输入复核单位名称
      if (this.ids.length == 0) {
        this.$message.error("请选择需要复核的数据");
        return;
      }
      this.$prompt("请输入复核单位名称", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        inputPattern: /\S/,
        inputErrorMessage: "复核单位名称不能为空",
      }).then(({ value }) => {
        detectionRecheck({
          periodicDetectionIds: this.ids,
          recheckOrganization: value,
        })
          .then(() => {
            this.$message({
              type: "success",
              message: "复核成功",
            });
            this.getList();
          })
          .catch(() => {});
      });
    },
    exportYearList() {
      this.download(
        "/baseData/tunnel/detection/exportReport",
        { ids: this.ids, ...this.queryParams },
        ``,
        {
          headers: { "Content-Type": "application/json;" },
          parameterType: "body",
        }
      );
    },
    deleteRecheckData() {
      if (this.ids.length == 0) {
        this.$message.error("请选择需要取消复核的数据");
        return;
      }
      this.$confirm("是否取消复核？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        detectionRollback({ periodicDetectionIds: this.ids })
          .then(() => {
            this.$message({
              type: "success",
              message: "取消复核成功",
            });
            this.getList();
          })
          .catch(() => {
            // this.$message({
            //   type: 'info',
            //   message: '取消复核'
            // });
          });
      });
    },

    exportAll() {
      this.isUpdate = false;
      this.showImport = true;
      this.importType = 2;
      this.importBaseType = "34";
    },
    evaluateExport() {
      this.download(
        "/baseData//tunnel/detection/exportAll",
        { ...this.queryParams },
        ``,
        {
          headers: { "Content-Type": "application/json;" },
          parameterType: "body",
        }
      );
    },
    // 表格点击勾选
    handleRowClick(row) {
      row.isSelected = !row.isSelected;
      this.$refs.table.toggleRowSelection(row);
    },
    // 勾选高亮
    rowStyle({ row, rowIndex }) {
      if (this.ids.includes(row.id)) {
        return { "background-color": "#b7daff", color: "#333" };
      } else {
        return { "background-color": "#fff", color: "#333" };
      }
    },
    // 导出数据按钮
    exportList() {
      this.download(
        "/baseData/tunnel/detection/export",
        { ids: this.ids, ...this.queryParams },
        ``,
        {
          headers: { "Content-Type": "application/json;" },
          parameterType: "body",
        }
      );
    },
    handleView(row) {
      this.showDetail = true;
      this.choseId = row.id;
    },
    closeForm(v) {
      this.showDialog = false;
      this.formData = {};
      if (v) this.getList();
    },
  },
  computed: {},
  watch: {},
};
</script>

<style lang="scss" scoped>
.button-list {
  border-radius: 4px;
  width: 100%;
  .el-button {
    margin-bottom: 10px;
    margin-right: 10px;
    margin-left: 0;
  }
}
</style>
