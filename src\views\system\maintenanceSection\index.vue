<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" label-width="96px">

      <el-form-item label="" prop="departmentId" >
          <el-select ref="formMainRef" v-model="queryParams.departmentId" placeholder="请选择管养单位"
                     style="width: 100%;"  >
            <el-option v-for="item in deptOptions" :label="item.label" :value="item.id"
                       :key="item.id" ></el-option>

          </el-select>

<!--        <treeselect v-model="queryParams.departmentId" :options="deptOptions" :show-count="true"-->
<!--                    placeholder="请选择管养单位"/>-->
      </el-form-item>
      <el-form-item label="" prop="maintenanceSectionName">
        <el-input
          v-model="queryParams.maintenanceSectionName"
          placeholder="请输入养护路段名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="" prop="roadSectionId">
        <el-select v-model="queryParams.roadSectionId" clearable filterable placeholder="请选择公路路段">
          <el-option
            v-for="item in highwaySectionList"
            :key="item.roadSectionId"
            :label="item.roadSectionName"
            :value="item.roadSectionId">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item v-if="false" label="" prop="routeGrade">
        <el-select v-model="queryParams.routeGrade" placeholder="请选择路线等级" clearable>
          <el-option
            v-for="dict in dict.type.sys_route_grade"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="" prop="state">
        <el-select v-model="queryParams.state" placeholder="请选择运营状态" clearable>
          <el-option
            v-for="dict in dict.type.sys_operation_state"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="" prop="ownerUnit">
        <el-select v-model="queryParams.ownerUnit" placeholder="请选择权属类型" clearable>
          <el-option label="集团公司" value="集团公司"></el-option>
          <el-option label="项目公司" value="项目公司"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['system:maintenanceSection:add']"
        >新增
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['system:maintenanceSection:edit']"
        >修改
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['system:maintenanceSection:remove']"
        >删除
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['system:maintenanceSection:export']"
        >导出
        </el-button>
      </el-col>

      <el-col :span="1.5">
        <el-button
          type="danger"
          icon="el-icon-search"
          size="mini"
          @click="toMergeSection"
        >高级查询
        </el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>
    <!--数据表格开始-->
    <div class="tableDiv">
      <el-table v-adjust-table size="mini" :height="showSearch ? 'calc(100vh - 320px)' : 'calc(100vh - 260px)'" border
                v-loading="loading" :data="maintenanceSectionList" @selection-change="handleSelectionChange" :header-cell-style="{'height': '36px'}">
        <el-table-column type="selection" width="55" align="center"/>
        <el-table-column label="序号" fixed align="center" type="index" width="50">
          <template v-slot="scope">
            {{ (scope.$index + (queryParams.pageNum - 1) * queryParams.pageSize)+1 }}
          </template>
        </el-table-column>
        <el-table-column label="养护路段名称" fixed :show-overflow-tooltip="true" align="center" width="120"
                         prop="maintenanceSectionName"/>
        <el-table-column label="管养单位" :show-overflow-tooltip="true" align="center" width="240"
                         prop="departmentName"/>
        <el-table-column label="公路路段" :show-overflow-tooltip="true" align="center" prop="roadSectionName" width="120"/>
        <el-table-column v-if="false" label="起点桩号" :show-overflow-tooltip="true" align="center" prop="pileStart" width="100"
                         :formatter="(...arg)=>formatPile(arg[2])"/>
        <el-table-column v-if="false" label="终点桩号" :show-overflow-tooltip="true" align="center" prop="pileEnd" width="100"
                         :formatter="(...arg)=>formatPile(arg[2])"/>
        <el-table-column v-if="false" label="起点名称" :show-overflow-tooltip="true" align="center" prop="placeStartName"/>
        <el-table-column v-if="false" label="终点名称" :show-overflow-tooltip="true" align="center" prop="placeEndName"/>
        <el-table-column label="路线等级" width="120" :show-overflow-tooltip="true" align="center" prop="routeGrade">
          <template slot-scope="scope">
            <dict-tag :options="dict.type.sys_route_grade" :value="scope.row.routeGrade"  />
          </template>
        </el-table-column>
        <el-table-column label="统一里程起点桩号" :show-overflow-tooltip="true" align="center" width="140"
                         prop="unifiedMileagePileStart"
                         :formatter="(...arg)=>formatPile(arg[2])"/>
        <el-table-column label="统一里程终点桩号" :show-overflow-tooltip="true" align="center" width="140"
                         prop="unifiedMileagePileEnd"
                         :formatter="(...arg)=>formatPile(arg[2])"/>
        <el-table-column label="统计里程(km)" :show-overflow-tooltip="true" align="center" prop="roadSectionLength"
                         :formatter="(...arg)=>{if(arg[2]) return (arg[2]/1000).toFixed(3).toLocaleString()}"/>
        <el-table-column label="养护里程(km)" :show-overflow-tooltip="true" align="center" prop="mainLength"
                         :formatter="(...arg)=>{if(arg[2]) return (arg[2]/1000).toFixed(3).toLocaleString()}"/>
        <el-table-column label="通车时间" :show-overflow-tooltip="true" align="center" prop="openingTime" width="120">
          <template v-slot="scope">
            <span>{{ parseTime(scope.row.openingTime, '{y}-{m}-{d}') }}</span>
          </template>
        </el-table-column>
        <el-table-column label="运营状态" :show-overflow-tooltip="true" align="center" prop="state" width="120">
          <template v-slot="scope">
            <dict-tag :options="dict.type.sys_operation_state" :value="scope.row.state"/>
          </template>
        </el-table-column>
        <el-table-column v-if="false"  label="路段长度" :show-overflow-tooltip="true" align="center" prop="roadSectionLength"
                         :formatter="(...arg)=>{if(arg[2]) return arg[2].toLocaleString()}"/>
        <el-table-column label="操作" align="center" width="180" class-name="small-padding fixed-width" fixed="right">
          <template slot-scope="scope">
            <el-button
              size="mini"
              type="text"
              icon="el-icon-edit"
              @click="handleUpdate(scope.row)"
              v-hasPermi="['system:maintenanceSection:edit']"
            >修改
            </el-button>
            <el-button
              size="mini"
              type="text"
              icon="el-icon-delete"
              @click="handleDelete(scope.row)"
              v-hasPermi="['system:maintenanceSection:remove']"
            >删除
            </el-button>
            <el-button
              size="mini"
              type="text"
              icon="el-icon-view"
              @click="onDetail(scope.row)"
              v-hasPermi="['system:maintenanceSection:query']"
            >查看
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <pagination
        :totalMileage="(totalMileage/1000).toFixed(3).toLocaleString()"
        v-show="total>0"
        :total="total"
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
        @pagination="getList"
      >
        <span slot="mileage"  style="margin-left: 50px;" v-text="'总统计里程：' + (roadSectionLength/1000).toFixed(3).toLocaleString() + ' km'"></span>
      </pagination>
    </div>
    <!--数据表格结束-->
    <!-- 添加或修改养护路段管理对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="800px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="130px" >
        <el-form-item label="公路路段" prop="roadSectionId">
          <el-select v-model="form.roadSectionId" filterable placeholder="请选择">
            <el-option
              v-for="item in highwaySectionList"
              :key="item.roadSectionId"
              :label="item.roadSectionName"
              :value="item.roadSectionId">
            </el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="管养单位" prop="departmentId">
          <el-select ref="formMainRef" v-model="form.departmentId" placeholder="请选择管养单位"
                     style="width: 100%;"  >
            <el-option v-for="item in deptOptions" :label="item.label" :value="item.id"
                       :key="item.id" ></el-option>

          </el-select>
<!--          <treeselect v-model="form.departmentId" :options="deptOptions" :show-count="true" placeholder="请选择管养单位"-->
<!--          />-->
        </el-form-item>
        <el-form-item label="养护路段名称" prop="maintenanceSectionName">
          <el-input v-model="form.maintenanceSectionName" placeholder="请输入养护路段名称"/>
        </el-form-item>
        <el-form-item label="路线等级" prop="routeGrade">
          <el-select v-model="form.routeGrade" placeholder="请选择路线等级" multiple >
            <el-option
              v-for="dict in dict.type.sys_route_grade"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item v-if="false" label="起点名称" prop="placeStartName">
          <el-input v-model="form.placeStartName" placeholder="请输入起点名称"/>
        </el-form-item>
        <el-form-item v-if="false" label="终点名称" prop="placeEndName">
          <el-input v-model="form.placeEndName" placeholder="请输入终点名称"/>
        </el-form-item>
        <el-form-item v-if="false" label="起点桩号" prop="pileStart">
          <PileInput   v-model="form.pileStart" @input="onPileChange(1)" class="pile-input"
                     placeholder="请输入起点桩号"></PileInput>
        </el-form-item>
        <el-form-item v-if="false" label="终点桩号" prop="pileEnd">
          <PileInput v-model="form.pileEnd" @input="onPileChange(1)" class="pile-input"
                     placeholder="请输入终点桩号"></PileInput>
        </el-form-item>
        <el-form-item label="统一里程起点桩号" prop="unifiedMileagePileStart">
          <PileInput v-model="form.unifiedMileagePileStart" @input="onPileChange(2)" class="pile-input"
                     placeholder="请输入统一里程起点桩号"></PileInput>
        </el-form-item>
        <el-form-item label="统一里程终点桩号" prop="unifiedMileagePileEnd">
          <PileInput v-model="form.unifiedMileagePileEnd" @input="onPileChange(2)" class="pile-input"
                     placeholder="请输入统一里程终点桩号"></PileInput>
        </el-form-item>
<!--        <el-form-item  label="养护里程(m)" prop="mainLength">-->
<!--          <el-input v-model="form.mainLength" placeholder="请输入养护里程(m)"/>-->
<!--        </el-form-item>-->
        <el-form-item  label="管理单位性质代码" prop="manageNatureCode">
          <el-input v-model="form.manageNatureCode" placeholder="请输入管理单位性质代码"/>
        </el-form-item>
        <el-form-item  label="管理单位" prop="managementUnit">
          <el-input v-model="form.managementUnit" placeholder="请输入管理单位"/>
        </el-form-item>
        <el-form-item  label="监管单位" prop="superUnitName">
          <el-input v-model="form.superUnitName" placeholder="请输入监管单位"/>
        </el-form-item>
        <el-form-item v-if="false" label="路段长度" prop="roadSectionLength">
          <el-input v-model="form.roadSectionLength" placeholder="请输入路段长度"/>
        </el-form-item>
        <el-form-item label="通车时间" prop="openingTime">
          <el-date-picker clearable
                          v-model="form.openingTime"
                          type="date"
                          value-format="yyyy-MM-dd"
                          placeholder="请选择通车时间">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="运营状态" prop="state">
          <el-select v-model="form.state" placeholder="请选择运营状态">
            <el-option
              v-for="dict in dict.type.sys_operation_state"
              :key="dict.value"
              :label="dict.label"
              :value="parseInt(dict.value)"
            ></el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

    <MaintenanceDetail :visible.sync="showDetail" :data="currentRow"  title="详情" :append-to-body="true" size="60%"></MaintenanceDetail>

<!--    <index-merge></index-merge>-->
  </div>
</template>

<script>
import {
  listMaintenanceSection,
  getMaintenanceSection,
  delMaintenanceSection,
  addMaintenanceSection,
  updateMaintenanceSection,
  getTotalMileage
} from "@/api/system/maintenanceSection";
import { routeCacheClear } from "@/api/system/route";
import Treeselect from "@riophae/vue-treeselect";
import "@riophae/vue-treeselect/dist/vue-treeselect.css";
// import {deptTreeSelect} from "@/api/system/user";
import {deptTreeSelect} from "@/api/tmpl";
import {listAllHighwaySections} from "@/api/system/highwaySections";
import PileInput from "@/views/system/route/pileinput.vue";
import MaintenanceDetail from "@/views/system/maintenanceSection/detail.vue";
import IndexMerge from "@/views/system/routeSegments/indexMerge.vue";
import {formatPile} from "@/utils/ruoyi";
export default {
  name: "MaintenanceSection",
  components: {PileInput, Treeselect, MaintenanceDetail, IndexMerge},
  dicts: ['sys_route_grade', 'sys_operation_state'],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: false,
      currentRow: {},
      showDetail: false,
      // 总条数
      total: 0,
      // 总养护里程
      totalMileage: 0,
      // 总统计里程
      roadSectionLength: null,
      // 养护路段管理表格数据
      maintenanceSectionList: [],
      deptOptions: [],
      highwaySectionList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 50,
        state: '2',
        ownerUnit: null,
        maintenanceSectionName: null,
        departmentId: null,
        roadSectionId: null,
        routeGrade: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        state: [
          {required: true, message: "运营状态不能为空", trigger: "blur"}
        ],
        maintenanceSectionName: [
          {required: true, message: "养护路段名称不能为空", trigger: "blur"}
        ],
        departmentId: [
          {required: true, message: "管理处不能为空", trigger: "blur"}
        ],
        roadSectionId: [
          {required: true, message: "路段不能为空", trigger: "blur"},

        ],
        routeGrade: [
          {required: true, message: "技术等级不能为空", trigger: "change"}
        ],
        roadSectionLength1: [
          {type: 'number', message: '请输入数字', trigger: 'blur'}
        ]
      }
    };
  },
  created() {
    this.getList();
    this.getDeptTree()
    this.getRoadSection()
  },
  methods: {
    formatPile,
    onDetail(row){
      this.currentRow = row
      this.showDetail = true
    },
    toMergeSection(){
      this.$router.push("/mergeRoadSection/info")
    },
    onPileChange(type) {
      if (type === 1)
        if (!isNaN(this.form.pileEnd) && !isNaN(this.form.pileStart))
          this.form.mainLength = (this.form.pileEnd - this.form.pileStart).toFixed(2)
      if (type === 2)
        if (!isNaN(this.form.unifiedMileagePileEnd) && !isNaN(this.form.unifiedMileagePileStart))
          this.form.roadSectionLength = (this.form.unifiedMileagePileEnd - this.form.unifiedMileagePileStart).toFixed(2)
    },
    /** 查询养护路段管理列表 */
    getList() {
      this.loading = true;
      listMaintenanceSection(this.queryParams).then(response => {
        this.maintenanceSectionList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
      this.getTotalMileage()
    },
    /** 查询总养护里程 */
    getTotalMileage() {
      getTotalMileage(this.queryParams).then(response => {
        this.totalMileage = response.data.mainLength;
        this.roadSectionLength = response.data.roadSectionLength;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        maintenanceSectionId: null,
        maintenanceSectionName: null,
        departmentId: null,
        roadSectionId: null,
        pileStart: null,
        pileEnd: null,
        placeStartName: null,
        placeEndName: null,
        mainLength: null,
        routeGrade: null,
        unifiedMileagePileStart: null,
        unifiedMileagePileEnd: null,
        openingTime: null,
        roadSectionLength: null,
        createBy: null,
        createdTime: null,
        updatedBy: null,
        manageNatureCode: null,
        managementUnit: null,
        superUnitName: null,
        state: 2,
        updatedTime: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.maintenanceSectionId)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加养护路段管理";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const maintenanceSectionId = row.maintenanceSectionId || this.ids
      getMaintenanceSection(encodeURIComponent(maintenanceSectionId)).then(response => {
        this.form = response.data;
        this.form.routeGrade && (this.form.routeGrade = this.form.routeGrade.split(","))
        this.open = true;
        this.title = "修改养护路段管理";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          this.form.routeGrade && (this.form.routeGrade = this.form.routeGrade.join(','))
          if (this.form.maintenanceSectionId != null) {
            updateMaintenanceSection(this.form).then(res => {
              if (res.code === 200) {
                this.$modal.msgSuccess("修改成功");
                this.open = false;
                this.getList();
                routeCacheClear()
              }
            });
          } else {
            addMaintenanceSection(this.form).then(res => {
              if (res.code === 200) {
                this.$modal.msgSuccess("新增成功");
                this.open = false;
                this.getList();
              }
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const maintenanceSectionIds = row.maintenanceSectionId || this.ids;

      let str = row.maintenanceSectionId?"名称为"+row.maintenanceSectionId:"选中"
      this.$modal.confirm('是否确认删除' + str + '的数据？').then(function () {

      // this.$modal.confirm('是否确认删除养护路段管理 名称为"' + (row.maintenanceSectionName ?? this.ids )+ '"的数据项？').then(function () {
        return delMaintenanceSection(maintenanceSectionIds);
      }).then((res) => {
        if (res.code === 200) {
          routeCacheClear()
          this.getList();
          this.$modal.msgSuccess("删除成功");
        }
      }).catch(() => {
      });
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('system/maintenanceSection/export', {
        ...this.queryParams
      }, `maintenanceSection_${new Date().getTime()}.xlsx`)
    },
    /** 查询部门下拉树结构 */
    getDeptTree() {
      return deptTreeSelect({types:201}).then(response => {
        this.deptOptions = response.data;
      });
    },
    getRoadSection() {
      listAllHighwaySections().then(res => {
        let data = res.data
        this.highwaySectionList = data
      })
    }
  }
};
</script>
<style scoped lang="scss">

::v-deep .el-table .el-table__fixed-body-wrapper{
    top: 46px !important;
}

::v-deep .el-table__fixed-header-wrapper{
    top: 0 !important;
}


.app-container form:first-child .el-select,
.app-container form:nth-child(2) .el-select,
.app-container form:nth-child(2) ::v-deep .el-form-item__content,
.app-container form:first-child ::v-deep .el-form-item__content {
  width: 240px;
}
.app-container  form:first-child  .el-form-item:last-child ::v-deep .el-form-item__content {
  width: auto;
}
.app-container form:nth-child(1) ::v-deep .vue-treeselect__control,
.app-container form:nth-child(2) ::v-deep .vue-treeselect__control{
  height: auto;
  line-height: 30px;
}
.el-dialog .el-input,
.el-dialog .pile-input,
.el-dialog .el-select,
.el-dialog .el-date-editor,
.el-dialog .el-textarea,
.el-dialog .vue-treeselect {
  width: 100%;
}
.tableDiv {
  background-color: white;
  padding-bottom: 10px;
}
</style>
