<template>
  <el-form ref="form" :model="ruleForm" :rules="rules" label-width="160px">
    <el-row :gutter="20" :style="{ pointerEvents: readonly ? 'none' : '', display: 'flex', flexWrap: 'wrap' }">
      <el-col :span="12" :offset="0">
        <el-form-item label="字段名称" prop="columnName">
          <el-input v-model="ruleForm.columnName" placeholder="请输入字段名称" :readonly="readonly"/>
        </el-form-item>
      </el-col>
      <el-col :span="12" :offset="0">
        <el-form-item label="中文名称" prop="columnNameZh">
          <el-input v-model="ruleForm.columnNameZh" placeholder="请输入名称" :readonly="readonly"/>
        </el-form-item>
      </el-col>

      <el-col :span="12" :offset="0">
        <el-form-item label="字段数据类型" prop="columnType">
          <el-select v-model="ruleForm.columnType" placeholder="请选择" clearable style="width: 100%;" :disabled="readonly">
            <el-option v-for="dict in dict.type.data_type" :key="dict.value" :label="dict.label" :value="dict.value" />
          </el-select>
        </el-form-item>
      </el-col>
      <el-col :span="12" :offset="0">
        <el-form-item label="输入框类型" prop="inputBoxType">
          <el-select v-model="ruleForm.inputBoxType" placeholder="请选择" clearable style="width: 100%;" :disabled="readonly">
            <el-option v-for="dict in dict.type.input_type" :key="dict.value" :label="dict.label" :value="dict.value" />
          </el-select>
        </el-form-item>
      </el-col>
      <el-col :span="12" :offset="0">
        <el-form-item label="排序" prop="showIndex">
          <el-input-number v-model="ruleForm.showIndex" :step="1" step-strictly style="width: 100%;" :disabled="readonly"/>
        </el-form-item>
      </el-col>
      <el-col :span="12" :offset="0" v-if="ruleForm.inputType == 4 || ruleForm.inputType == 5">
        <el-form-item label="选择项" prop="optionName">
          <el-input v-model="ruleForm.optionName" placeholder="请输入选择项,英文逗号隔开" :readonly="readonly"/>
        </el-form-item>
      </el-col>

      <!-- <el-col :span="12" :offset="0">
        <el-form-item label="是否必传" prop="ifRequired">
          <el-select v-model="ruleForm.ifRequired" placeholder="请选择" clearable style="width: 100%;" :disabled="readonly">
            <el-option  label="是" :value="1" />
            <el-option  label="否" :value="0" />
          </el-select>
        </el-form-item>
      </el-col> -->

      <!-- <el-col :span="12" :offset="0">
        <el-form-item label="是否显示" prop="ifShow">
          <el-select v-model="ruleForm.ifShow" placeholder="请选择" clearable style="width: 100%;" :disabled="readonly">
            <el-option  label="是" :value="1" />
            <el-option  label="否" :value="0" />
          </el-select>
        </el-form-item>
      </el-col> -->
      <el-col :span="12" :offset="0">
        <el-form-item label="是否显示在列表上" prop="ifTableShow">
          <el-select v-model="ruleForm.ifTableShow" placeholder="请选择" clearable style="width: 100%;" :disabled="readonly">
            <el-option  label="是" :value="1" />
            <el-option  label="否" :value="0" />
          </el-select>
        </el-form-item>
      </el-col>
      <el-col :span="12" :offset="0">
        <el-form-item label="显示字段是否模糊搜索" prop="ifLikeSearch">
          <el-select v-model="ruleForm.ifLikeSearch" placeholder="请选择" clearable style="width: 100%;" :disabled="readonly">
            <el-option  label="是" :value="1" />
            <el-option  label="否" :value="0" />
          </el-select>
        </el-form-item>
      </el-col>
      <el-col :span="12" :offset="0">
        <el-form-item label="数据表头字段名" prop="tableHeadName">
          <el-input v-model="ruleForm.tableHeadName" :readonly="readonly"/>
        </el-form-item>
      </el-col>
      <el-col :span="24" :offset="0">
        <el-form-item label="其他需要配置" prop="otherConfig">
          <vue-json-editor
              v-model="ruleForm.otherConfig"
              :mode="'code'"
          />
          <!-- <el-input v-model="ruleForm.otherConfig" type="textarea" :readonly="readonly"/> -->
        </el-form-item>
      </el-col>
      <!-- <el-col :span="12" :offset="0">
        <el-form-item label="是否用于排序" prop="ifSort">
          <el-select v-model="ruleForm.ifSort" placeholder="请选择" clearable style="width: 100%;" :disabled="readonly">
            <el-option  label="是" :value="1" />
            <el-option  label="否" :value="0" />
          </el-select>
        </el-form-item>
      </el-col> -->
      <!-- <el-col :span="12" :offset="0" v-if="ruleForm.ifSort">
        <el-form-item label="转数字排序" prop="ifSortSigned">
          <el-select v-model="ruleForm.ifSortSigned" placeholder="请选择" clearable style="width: 100%;" :disabled="readonly">
            <el-option  label="是" :value="1" />
            <el-option  label="否" :value="0" />
          </el-select>
        </el-form-item>
      </el-col> -->

      <!-- <el-col :span="12" :offset="0">
        <el-form-item label="shape 数据类型" prop="shapeType">
          <el-select v-model="ruleForm.shapeType" placeholder="请选择" clearable style="width: 100%;" :disabled="readonly">
            <el-option v-for="dict in dict.type.shape_type" :key="dict.value" :label="dict.label" :value="dict.value" />
          </el-select>
        </el-form-item>
      </el-col> -->

      <!-- <el-col :span="12" :offset="0">
        <el-form-item label="正则表达式" prop="regEx">
          <el-input v-model="ruleForm.regEx" placeholder="请输入" :readonly="readonly"/>
        </el-form-item>
      </el-col> -->
    </el-row>
    <el-form-item style="margin: auto;display: flex;justify-content: center;">
      <el-button type="primary" @click="onSubmit" v-loading="loading" v-if="!readonly">确定</el-button>
      <el-button @click="onCancel">取消</el-button>
    </el-form-item>
  </el-form>
</template>

<script>
import { addMenuSubField, updateMenuSubField } from "@/api/oneMap/menuSubField";
import { createIdWorker } from '@/api/baseData/common'
import vueJsonEditor from 'vue-json-editor'

export default {
  dicts: ['data_type', 'input_type','shape_type'],
  props: {
    form: {
      type: Object,
      default: {},
    },
    id: {
      type: [String, Number],
      default: ''
    },
    readonly: {
      type: Boolean,
      default: false,
    },
  },
  components: { vueJsonEditor },
  data() {
    return {
      ruleForm: {
        oneMapShow: 1,
      },
      rules: {
        columnName: [
          { required: true, message: '请输入字段名称', trigger: 'blur' },
        ],
      },
      loading: false,
      enterOwnerId: '',
    }
  },
  created() {
    this.ruleForm = { ...this.form }
    if (this.ruleForm.columnType) {
      this.ruleForm.columnType = this.ruleForm.columnType + ''
    }
    if (this.ruleForm.inputBoxType) {
      this.ruleForm.inputBoxType = this.ruleForm.inputBoxType + ''
    }
    if (this.ruleForm.otherConfig) {
      this.ruleForm.otherConfig = JSON.parse(this.ruleForm.otherConfig)
    }
    if (!this.form.id) {
      this.ruleForm = {
        ...this.ruleForm,
        columnType: '2',
        inputBoxType: '2',
        ifTableShow: 1,
        ifLikeSearch: 0
      }
    }
    // createIdWorker().then(res => {
    //   if (res.code === 200) this.enterOwnerId = res.data
    // })
  },
  methods: {
    // 提交
    onSubmit() {
      this.$refs.form.validate(vali => {
        if (!vali) return
        if (this.ruleForm.otherConfig) {
          this.ruleForm.otherConfig = JSON.stringify(this.ruleForm.otherConfig)
        }
        this.ruleForm.iconId = Object.prototype.toString.call(this.ruleForm.iconId) === "[object Array]" ? this.ruleForm.iconId[0] : this.ruleForm.iconId
        let request = this.ruleForm.id ? updateMenuSubField(this.ruleForm) : addMenuSubField(this.ruleForm)
        let msg = this.ruleForm.id ? '编辑成功' : '新增成功';
        this.loading = true;
        request.then(res => {
          if (res.code == 200) {
            this.$modal.msgSuccess(msg);
            this.$emit('refresh')
            this.onCancel();
          }
        }).finally(() => {
          this.loading = false;
        })
      })
    },
    // 取消关闭
    onCancel() {
      this.$emit('close', false)
    },
  },
};
</script>

<style lang="scss" scoped></style>
