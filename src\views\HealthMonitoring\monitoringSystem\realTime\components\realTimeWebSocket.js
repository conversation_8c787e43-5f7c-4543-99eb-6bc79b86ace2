const { HubConnectionBuilder, LogLevel, HubConnectionState } =  require('@microsoft/signalr')
import * as signalR from '@microsoft/signalr'
import { Message } from 'element-ui'


export default class RealTimeWebSocket {
  socket; // WebSocket connection
  heartbeatInterval = undefined; // 发送心跳包的计时器（function: _startTimeSendData()）
  reconnectInterval = undefined; // 重连计时器（function: _reconnect()）

  currSensorInfoCmd = null; // 前端传入的传感器信息（格式：{ constructionCode, sensorId }）
  startReceive = false; // 是否开始发送数据
  currentSensorInfo = null; // 后端传回的传感器信息
  isReconnect = false; // 是否重连

  /**
   * @param initChart 绘图函数 - 初始化绘图框
   * @param addChartData 绘图函数 - 添加/修改 绘图数据
   * @param vueComponent 绘图函数调用时绑定的this对象
   * @param currSensorInfoCmd 传感器信息（格式：{ constructionCode, sensorId }），可后面修改
   */
  constructor(initChart, addChartData, vueComponent, currSensorInfoCmd) {
    if (currSensorInfoCmd && currSensorInfoCmd?.constructionCode && currSensorInfoCmd?.sensorId) {
      this.currSensorInfoCmd = currSensorInfoCmd;
    }

    // 创建 WebSocket 对象
    this.socket = new HubConnectionBuilder()
      .withUrl("https://jkjc.glyhgl.com:22584/RealTimeSensorDataHub",{
        transport: signalR.HttpTransportType.WebSockets,  // 强制使用 WebSocket
        skipNegotiation: true  // 跳过协商，直接使用 WebSocket 
      })
      .configureLogging(LogLevel.Information)
      //.withAutomaticReconnect()//自动重连
      .build();

    // 设置监听器
    // 重连中
    this.socket.onreconnecting(error => {
      console.assert(this.socket.state === HubConnectionState.Reconnecting);
    });

    // 重连后
    this.socket.onreconnected(connectionId => {
      console.assert(this.socket.state === HubConnectionState.Connected);
    });

    //连接断开
    this.socket.onclose(error => {
      console.assert(this.socket.state === HubConnectionState.Disconnected);
      // 关闭定时任务
      this._clearAllInterval();
    });

    // 获取传感器信息回调
    this.socket.on('FromHub_SendToClient_GetSensorInfo_Response', (message) => {
      this.currentSensorInfo = JSON.parse(message)
      initChart.call(vueComponent, this.currentSensorInfo)
    });
    // 接收传感器数据
    this.socket.on('FromHub_SendToClient_SendSensorData', (message) => {
      const data = JSON.parse(message)
      addChartData.call(vueComponent, data)
    });
    // 与上述方法同级，但是是无数据的响应方法处理
    this.socket.on('FromHub_SendToClient_StartSendData_Response', (message) => {
      console.log(JSON.parse(message))
      initChart.call(vueComponent, null)
    });

    // 创建定时任务
    // this._addAllInterval()
  }


  // 连接hub
  async start() {
    if (!this.socket) {
      Message.error("尚未建立WebSocket连接")
      return;
    }
    try {
      await this.socket.start(); // 开始连接
      this.isReconnect = true; // 需要重连
      //await this.socket.invoke('FromAll_SendRegister', 'Client') // 注册，客户端类型为Client，表式查看即时数据端
      Message.success("连接hub成功");
      console.log("SignalR Connected.");
    } catch (err) {
      console.log(err);
      // Message.error("连接hub失败，5s后尝试重新连接...");
    }
  }

  // 断开hub
  async stop() {
    try {
      this.isReconnect = false; // 关闭重连开关
      this.startReceive = false; // 修改推送数据状态
      await this.socket.stop(); //断开连接
      this._clearAllInterval()
      // Message.success("断开hub成功");
    } catch (err) {
      console.log(err);
      // Message.error("断开hub失败");
    }
  }

  // 重连逻辑
  async _reconnect() {
    try {
      if (!this.isReconnect) {
        return;
      }
      if (this.socket.state === HubConnectionState.Connected) {
        return; // 状态是已连接，不用调用 start 方法
      }
      await this.start(); // 开始连接
      await this.startSendData(); // 重新开始发送数据
    } catch (err) {
      console.log(err);
    }
  }

  // 重新设置传感器信息，并重置心跳定时任务
  async setSensorInfo(currSensorInfoCmd) {
    this.currSensorInfoCmd = {};
    this.currSensorInfoCmd.constructionCode = currSensorInfoCmd.constructionCode;
    this.currSensorInfoCmd.sensorId = currSensorInfoCmd.sensorId;
    this._clearAllInterval(); // 关闭之前的定时任务
    this._addAllInterval(); // 设置新的定时任务
  }

  // 获取传感器信息（若传入传感器信息，则先调用setSensorInfo）
  async getSensorInfo(currSensorInfoCmd) {
    if (currSensorInfoCmd && currSensorInfoCmd?.constructionCode && currSensorInfoCmd?.sensorId) {
      await this.setSensorInfo(currSensorInfoCmd);
    }
    try {
      //获取传感器信息，用于画图表外观
      await this.socket.invoke('FromClient_GetSensorInfo', JSON.stringify(this.currSensorInfoCmd))
    } catch (err) {
      console.error(err);
      // Message.error("获取传感器信息失败");
    }
  }

  // 接收实时数据过程中，每隔一段时间需要调用该函数
  // 备注：需定时执行发送FromClient_StartReceiveData，否则后端过10秒自动停止发送数据
  async _startTimeSendData() {
    if (this.startReceive) {
      try {
        if (this.currSensorInfoCmd == null) {
          return;
        }
        //获取传感器信息，用于画图表外观
        await this.socket.invoke('FromClient_TimedStartReceiveData', JSON.stringify(this.currSensorInfoCmd))
      } catch (err) {
        console.error(err);
      }
    }
  }

  // 开始接收实时数据
  async startSendData() {
    try {
      if (this.currSensorInfoCmd == null) {
        // Message.error("传感器信息为空");
        return;
      }
      //获取传感器信息，用于画图表外观
      await this.socket.invoke('FromClient_StartReceiveData', JSON.stringify(this.currSensorInfoCmd))
      this.startReceive = true;
      Message.success("已开始接收实时数据...");
    } catch (err) {
      console.error(err);
      // Message.error("接收实时数据失败");
    }
  }

  // 停止接收实时数据
  async stopSendData() {
    this.startReceive = false;
    try {
      if (this.currSensorInfoCmd == null) {
        // Message.error("传感器信息为空");
        return;
      }
      // 获取传感器信息，用于画图表外观
      await this.socket.invoke('FromClient_StopReceiveData', JSON.stringify(this.currSensorInfoCmd))
      // Message.success("已停止接收实时数据...");
    } catch (err) {
      console.error(err);
    }
  }

  // 关闭 WebSocket 服务
  async close() {
    // 关闭定时任务
    this._clearAllInterval()
    // 关闭数据接收
    if (this.startReceive) {
      await this.stopSendData()
    }
  }


  // 创建定时任务
  _addAllInterval() {
    this._clearAllInterval() // 确保之前的定时任务处于关闭状态
    this.heartbeatInterval = setInterval(this._startTimeSendData.bind(this), 5000);
    this.reconnectInterval = setInterval(this._reconnect.bind(this), 5000);
  }

  // 关闭所有定时任务
  _clearAllInterval() {
    if (this.heartbeatInterval) {
      clearInterval(this.heartbeatInterval);
    }
    if (this.reconnectInterval) {
      clearInterval(this.reconnectInterval)
    }
  }

}