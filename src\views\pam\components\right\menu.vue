<template>
  <div class="right-menu" :style="menuStyle">
    <section class="right-menu-list" :style="{
      marginRight: tableShow ? '10px' : '',
      marginTop: tableShow ? '10px' : '',
    }">
      <div v-for="(item, index) in menuList" :key="index" class="menu-list" @click="handleMenuClick(item, index)">
        <img :src="item.icon" alt="" :class="clickIndex == index ? 'click-act' : ''" />
        <span>{{ item.name }}</span>
      </div>
      <div class="menu-child" v-if="childShow" :style="{ bottom: tableShow ? '49vh' : '-50px' }">
        <div v-for="(item, index) in menuChildList" :key="index" class="menu-child-list"
          @click="handleChildClick(item, index)">
          <img :src="menuIndex == index ? menuChildList[index].iconAct : item.icon" alt="" class="child-list-img" />
          <span :style="menuIndex == index ? 'color:#00FDFD' : ''">{{
            item.name
          }}</span>
          <div class="divider" v-if="index < menuChildList.length - 1"></div>
        </div>
      </div>
      <div class="dialog-content" v-if="show" :style="{ right: childShow ? '155px' : '80px' }">
        <div class="header">
          <span>{{ title }}</span>
          <i class="el-icon-close" @click="handleClose"></i>
        </div>
        <div class="body">
          <component :is="page" @quit="handleClose" />
        </div>
      </div>
    </section>

    <section class="table-list" v-if="tableShow" ref="resizable" :style="tableStyle">
      <div class="spread-packup" @click="hanleSpread">
        <img src="@/assets/map/spread.png" :class="!isSpread ? 'spread-img' : 'img'" />
      </div>

      <div class="handler" @mousedown.stop="startResize"></div>

      <div v-if="isSpread">
        <div class="table-top">
          <span>{{ tableTitle }}</span>
          <div class="btn">
            <el-button type="primary" @click="onExport">导出</el-button>
            <!-- <el-button type="info" @click="">关闭</el-button> -->
          </div>
        </div>
        <el-row :gutter="20" class="mt5 mb5">
          <el-col :span="24" :offset="0">
            <el-input v-model="keyWords" placeholder="请输入名称进行过滤" clearable>
              <template slot="suffix">
                <i class="el-icon-search" style="line-height: 30px; font-size: 16px; cursor: pointer"
                  @click="getTableList"></i>
              </template>
            </el-input>
          </el-col>
        </el-row>
        <div class="table-content">
          <el-table :data="tableData" border :height="tableH" @row-click="handleRowClick" v-loading="loading"
            :highlight-current-row="highlight">
            <el-table-column v-for="(col, index) in tableHeader" :prop="col.col" :key="'table' + col.col + index"
              :label="col.name" :min-width="col.width" show-overflow-tooltip>
              <template #default="{ row }">
                <div v-if="
                  col.dictType && col.dictData && col.type && col.type == 11
                ">
                  <span>{{ col.dictData[row[col.col]] }}</span>
                </div>
                <span v-else-if="col.type && col.type == 12">{{
                  stakeFilter(row[col.col])
                }}</span>
                <span v-else-if="col.type && col.type == 7">{{
                  row[col.col] | formatDate()
                }}</span>
                <span v-else>{{ row[col.col] }}</span>
              </template>
            </el-table-column>
          </el-table>
          <el-pagination @size-change="handleSizeChange" @current-change="handleCurrentChange" style="float: right"
            :current-page.sync="params.pageNum" :page-size="params.pageSize" layout="total, prev, pager, next"
            :total="total" background :pager-count="3">
          </el-pagination>
        </div>
      </div>
    </section>
  </div>
</template>

<script>
import StakeMark from "./stakeMark.vue";
import RoadSection from "./roadSection.vue";
import RoadNetwork from "./roadNetwork.vue";
import RoutePlanning from "./routePlanning.vue";
import TollStation from "./tollStation.vue";
import DictLable from "../common/dictLable.vue";

import { mapState, mapActions, mapMutations } from "vuex";
import {
  getLonLat,
  gcj02towgs84,
  addWidthFeature,
  isValidWKT,
  addClickFeature,
  getImageById,
} from "../common/mapFun";
import { Point } from "ol/geom";
import { transform as projTransform } from "ol/proj";
import { WKT } from "ol/format";
// api
import { publicRequest, exportTable } from "@/api/oneMap/tableInfo";
import { getDictData, getDataList } from "@/api/oneMap/layerData";
import { formatDate } from "@/utils/index";
import { isBigScreen } from "../common/util";

export default {
  components: {
    StakeMark,
    RoadSection,
    RoadNetwork,
    RoutePlanning,
    TollStation,
    DictLable,
  },
  data() {
    return {
      clickIndex: null,
      menuList: [
        {
          name: "桩号定位",
          icon: require("@/assets/map/location.png"),
          value: 1,
          page: StakeMark,
        },
        {
          name: "养护路段",
          icon: require("@/assets/map/section.png"),
          value: 2,
          page: RoadSection,
        },
        {
          name: "路网线形采集",
          icon: require("@/assets/map/network-acquisition.png"),
          value: 3,
          page: RoadNetwork,
        },
        {
          name: "大件运输",
          icon: require("@/assets/map/transportation.png"),
          value: 4,
          page: "",
        },
      ],
      menuIndex: null,
      menuChildList: [
        {
          name: "路线规划",
          icon: require("@/assets/map/route-planning.png"),
          iconAct: require("@/assets/map/route-planning-act.png"),
          page: RoutePlanning,
        },
        {
          name: "收费站查看",
          icon: require("@/assets/map/toll-station.png"),
          iconAct: require("@/assets/map/toll-station-act.png"),
          page: TollStation,
        },
      ],
      show: false,
      childShow: false,
      title: "",
      page: null,

      tableTitle: "数据列表",
      keyWords: "",
      isSpread: true,
      tableHeader: [], // 表头数据
      tableData: [], // 表格数据
      exportAction: {}, // 导出接口及请求方式
      listAction: {}, // 列表接口及请求方式
      tableH: "100%", // 表格高度
      params: {
        pageNum: 1,
        pageSize: 15,
      }, // 列表请求参数
      total: 0, // 列表数据总数
      loading: false,
      changeKey: new Date().getTime(),
      dictData: {}, // 当前列表字典数据
      menuShowType: undefined,
      highlight: true,
      pointIcon: "",
      coordinateSystem: "",
      tableWidth: 400, // 初始宽度
      startX: 0, // 鼠标按下时的X坐标
      startWidth: 0, // 元素按下时的宽度
      isOneMap: false, // 是否是全屏
      bigBool: false, // 是否是大屏
    };
  },
  computed: {
    ...mapState({
      tableShow: (state) => state.map.tableShow,
      legendList: (state) => state.map.legendList,
    }),
    menuStyle() {
      return {
        top: this.bigBool ? '50px' : this.tableShow ? '30px' : '100px',
        right: this.tableShow ? '5px' : '15px',
        zIndex: this.isSpread ? 2 : 1,
      }
    },
    tableStyle() {
      this.tableWidth = this.bigBool ? 1600 : 400;
      let height = this.bigBool ? 'calc(100vh - 110px)' : this.isOneMap ? 'calc(100vh - 55px)' : 'calc(100vh - 140px)'
      return {
        width: this.isSpread ? this.tableWidth + 'px' : '0px',
        padding: this.isSpread ? '6px 10px' : '',
        border: this.isSpread ? '1px solid #0687FF' : '',
        height: height,
        marginTop: this.bigBool ? '50px' : '0px',
      }
    },
  },
  async created() {
    this.isOneMap = this.$route.name === "oneMap";
    this.bigBool = isBigScreen();
    window.$Bus.$on("getTable", async (data) => {
      let { params, query, menuShowType, row } = data;
      this.isSpread = true;
      this.menuShowType = menuShowType;
      if (row && row.icon) {
        this.pointIcon = row.icon;
      } else {
        this.pointIcon = "";
      }
      if (row && row.coordinateSystem) {
        this.coordinateSystem = row.coordinateSystem;
      } else {
        this.coordinateSystem = "";
      }
      // 设置列表标题
      if (row) {
        if (this.menuShowType === 1) {
          this.tableTitle = row.name ? row.name + "列表" : "数据列表";
        } else {
          this.tableTitle =
            this.legendList && this.legendList.length
              ? this.legendList[0].name + "列表"
              : row.name + "列表" || "数据列表";
        }
      }
      let queryConfig = query ? JSON.parse(query) : {};
      this.tableHeader = [];
      if (!queryConfig.tableHeader.length) return;
      (await queryConfig.tableHeader.map(async (v) => {
        if (v.name.length <= 2) {
          v.width = v.name.length * 30;
        } else {
          v.width = v.name.length * 20;
        }
        // if (v.dictType) {
        //   await this.getDictList(v.dictType).then(res => {
        //     v.dictData = res || []
        //   })
        // }
        // 判断是否包含有 ifTableShow 字段
        if (!v.hasOwnProperty("ifTableShow")) {
          this.tableHeader.push(v);
        } else {
          if (v.ifTableShow == 1) {
            this.tableHeader.push(v);
          }
        }
      })) || [];
      // 导出相关
      this.exportAction = {
        url: queryConfig.exportActionUrl,
        method: queryConfig.exportActionMethod,
      };
      // 列表相关
      this.listAction = {
        url: queryConfig.listActionUrl,
        method: queryConfig.listActionMethod,
      };
      this.params = {
        ...this.params,
        ...params,
        mainTypeId: queryConfig.mainTypeId || null,
        paramsDTO: {
          ...params.paramsDTO,
          ks: "",
        },
      };
      // 处理动态表格头部数据
      let header = await this.getTableHead(queryConfig, row);
      if (header) {
        this.tableHeader = [...this.tableHeader, ...header];
      }
      setTimeout(() => {
        this.getTableList();
      }, 300);
    });
  },
  watch: {
    tableData: {
      handler(val) {
        if (!val.length) {
          // this.getTableShow(false)
        }
      },
      deep: true,
    },
    tableShow(newValue, oldValue) {
      if (!newValue) {
        this.$emit("openTable", [], false, "");
      }
      // 处理 computed 值变化的逻辑
    },
  },
  mounted() {
    // 处理表格高度
    let num = this.bigBool ? 260 : this.isOneMap ? 165 : 250;
    this.tableH = window.innerHeight - num;
    window.addEventListener("resize", () => {
      this.tableH = window.innerHeight - num;
    });
  },
  destroyed() {
    window.$Bus.$off("getTable");
  },
  methods: {
    ...mapActions({
      getTableShow: "map/getTableShow",
    }),
    ...mapMutations({
      setTableHeader: "map/setTableHeader",
    }),
    // 获取动态表格头数据
    async getTableHead(config, data) {
      let heaerObj = config.dynamicTableHeader;
      if (!heaerObj) return;
      let params = {};
      heaerObj.params.forEach((item) => {
        params[item.paramColName] =
          config[item.dataColName] || data[item.dataColName];
      });
      return new Promise((resolve, reject) => {
        publicRequest(heaerObj.actionUrl, heaerObj.actionMethod, params)
          .then((res) => {
            if (res.code === 200 && res.data) {
              let arr =
                res.data.map((v) => {
                  v.width =
                    v.name.length > 3 ? v.name.length * 20 : v.name.length * 35;
                  return v;
                }) || [];
              resolve(arr);
            } else {
              resolve([]);
            }
          })
          .catch((err) => {
            reject(err);
          });
      });
    },
    // 获取列表数据
    getTableList() {
      this.params.paramsDTO.ks = this.keyWords;
      this.loading = true;
      // this.tableData = []
      publicRequest(this.listAction.url, this.listAction.method, this.params)
        .then((res) => {
          if (res.code === 200) {
            this.tableData = res.rows || [];
            this.total = res.total || 0;
            // this.$forceUpdate();
          }
        })
        .finally(() => {
          this.loading = false;
        });
    },
    // 获取字典数据
    async getDictList(dictType) {
      return new Promise(async (resolve, reject) => {
        await getDictData({ dictType }).then((res) => {
          if (res.code == 200) {
            resolve(res.data);
            return res.data;
          } else {
            reject("");
          }
        });
      });
    },
    handleMenuClick(item, index) {
      let type = item.value;
      this.clickIndex = index;
      switch (type) {
        case 1:
        case 2:
        case 3:
          this.show = true;
          this.title = item.name;
          this.page = item.page;
          this.childShow = false;
          this.menuIndex = null;
          break;
        case 4:
          this.childShow = !this.childShow;
          this.menuIndex = null;
          this.show = false;
          break;
        default:
          break;
      }
    },
    // 点击子菜单
    handleChildClick(item, index) {
      this.menuIndex = index;
      this.show = true;
      this.title = item.name;
      this.page = item.page;
    },
    // 关闭弹窗
    handleClose() {
      this.show = false;
      this.title = "";
      this.menuIndex = null;
    },

    // 表格展开收起
    hanleSpread() {
      const list = window.mapLayer.getLayers().getArray();
      const arr = [];
      list.map((layer) => {
        if (layer.get("name") === "clickLayer") {
          arr.push(layer);
        }
      });
      arr.map((l) => {
        window.mapLayer.removeLayer(l);
      });
      this.$emit("openTable", [], false);
      this.isSpread = !this.isSpread;
    },
    // 页容量
    handleSizeChange(val) {
      this.params.pageSize = val;
      this.getTableList();
    },
    // 分页
    handleCurrentChange(val) {
      this.params.pageNum = val;
      this.changeKey = new Date().getTime();
      this.getTableList();
    },
    // 点击表格行
    async handleRowClick(row) {
      if (row.road_section_code) {
        // 路线表格
        let obj = {
          id: "1824259894327382017",
          paramsDTO: {
            precisionParams: {
              lxbh: row.road_section_code,
            },
          },
        };
        this.$emit("openTable", obj, true, row.road_section_name);
        // this.loading = true;
        // getDataList(obj).then(res => {
        //   if(res.code === 200) {
        //     this.highlight = true
        //     row.bottomTable = res.rows
        //     this.$emit('openTable', res.rows, true, row.road_section_name)
        //   }
        // }).finally(() => {
        //   this.loading = false;
        // })
        return;
      }
      let shape = row.shape;
      if (!shape) {
        shape = await this.getLonLatByRouteCode(row);
      }
      if (!shape) return;
      const list = window.mapLayer.getLayers().getArray();
      const arr = [];
      list.map((layer) => {
        if (layer.get("layerId") === row.id) {
          arr.push(layer);
        }
      });
      if (arr.length > 0) {
        arr.map((l) => {
          window.mapLayer.removeLayer(l);
        });
        this.highlight = false;
        return;
      } else {
        this.highlight = true;
      }

      if (!isValidWKT(shape)) return;
      let feature = undefined;
      if (!this.coordinateSystem || this.coordinateSystem == "") {
        this.coordinateSystem =
          this.legendList && this.legendList.length
            ? this.legendList[0].coordinateSystem
            : "";
      }
      if (shape.startsWith("POINT") && this.coordinateSystem === "GCJ_02") {
        let { lon, lat } = getLonLat(shape);
        let wgs84Coordinates = gcj02towgs84(lon, lat);
        const projectedCoords = projTransform(
          wgs84Coordinates,
          "EPSG:4326",
          "EPSG:3857"
        );
        let point = new Point(projectedCoords);
        let newShape = new WKT().writeGeometry(point);
        feature = new WKT().readFeature(newShape);
        // addClickFeature(feature, row, this.pointIcon, true, true, 'clickLayer', this.menuShowType)
      } else {
        feature = new WKT().readFeature(shape, {
          dataProjection: "EPSG:4326",
          featureProjection: "EPSG:3857",
        });
      }
      // 将数据加载到地图
      if (shape.startsWith("POINT") || shape.startsWith("MULTIPOINT")) {
        addClickFeature(
          feature,
          row,
          this.pointIcon,
          true,
          true,
          "clickLayer",
          this.menuShowType
        );
      } else {
        addClickFeature(
          feature,
          row,
          null,
          true,
          true,
          "clickLayer",
          this.menuShowType
        );
      }
      // 跳转到地图
      window.mapLayer.getView().fit(feature.getGeometry().getExtent(), {
        duration: 300,
        maxZoom: 18,
      });
    },
    // 通过桩号和路线编码获取 经纬度
    async getLonLatByRouteCode(row) {
      let stakeNumber =
        row.constructionStake ||
        row.nationalNetworkStake ||
        row.unifiedMileageStake ||
        row.startStake ||
        row.endStake;
      let routeCode = row.routeCode;
      let params = {
        stakeNumber,
        routeCode,
      };
      if (!stakeNumber || !routeCode) return null;
      return new Promise((resolve, reject) => {
        this.$modal.loading("请求中，请稍后...");
        publicRequest("/system/stationLine/getRouteData", "post", params)
          .then((res) => {
            if (res.code == 200 && res.data) {
              let data = res.data || {};
              if (data.wkt) {
                let isBool = isValidWKT(data.wkt);
                if (isBool) {
                  resolve(data.wkt);
                }
              } else {
                resolve(null);
              }
            }
          })
          .catch((err) => {
            reject(err);
          })
          .finally(() => {
            this.$modal.closeLoading();
          });
        return;
      });
    },
    // 数据导出
    onExport() {
      this.$modal
        .confirm("确定导出数据？")
        .then(() => {
          this.download(
            this.exportAction.url,
            this.params,
            `${new Date().getTime()}.xlsx`,
            {
              headers: { "Content-Type": "application/json;" },
              parameterType: this.exportAction.method == "post" ? "body" : "",
            }
          );
        })
        .then(() => { })
        .catch(() => { });
    },
    // 获取 字典数据
    getDictLabel(val, data) {
      if (val && data) {
        let arr = data.filter((v) => v.dictValue == val);
        return arr && arr.length > 0 ? arr[0].dictLabel : "";
      }
    },
    stakeFilter(value) {
      if (!value) return "";
      if (
        typeof value == "string" &&
        (value.includes("k") || value.includes("K"))
      ) {
        return value;
      } else if (typeof value == "string" && value.includes("~")) {
        // 判断是否 ~连接
        let splitArr = value.split("~");
        return (
          this.stakeFormat(splitArr[0]) + " ~ " + this.stakeFormat(splitArr[1])
        );
      } else {
        return this.stakeFormat(value);
      }
    },
    stakeFormat(value) {
      if (!value) return "";
      let prefix = parseInt(value / 1000);
      let val = "";
      if (prefix < 1) {
        val = "k0+" + value;
      } else {
        let str = value / 1000 + "";
        const parts = str.split(".");
        const integerPart = parseInt(parts[0]);
        const decimalPart = parts[1] ? parseFloat("0." + parts[1]) : 0;
        val = "k" + integerPart + "+" + decimalPart * 1000;
      }
      return val;
    },
    startResize(event) {
      this.startX = event.clientX;
      this.startWidth = this.$refs.resizable.offsetWidth;

      document.addEventListener("mousemove", this.doResize);
      document.addEventListener("mouseup", this.stopResize);
    },
    doResize(event) {
      this.tableWidth = this.startWidth + this.startX - event.clientX;
    },
    stopResize() {
      document.removeEventListener("mousemove", this.doResize);
      document.removeEventListener("mouseup", this.stopResize);
    },
  },
};
</script>

<style lang="scss" scoped>
@import "@/assets/styles/utils.scss";

@font-face {
  font-family: "YouSheBiaoTiHei";
  /* 自定义的字体名称 */
  src: url("~@/assets/home/<USER>") format("truetype");
  /* 字体文件路径和格式 */
  /* 可选属性，根据需要设置 */
  font-weight: normal;
  font-style: normal;
}

.right-menu {
  position: absolute;
  display: flex;

  .right-menu-list {
    position: relative;

    .menu-list {
      width: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
      flex-direction: column;
      margin-bottom: vwpx(12px);
      cursor: pointer;

      img {
        width: vwpx(90px);
        height: vwpx(90px);
        margin-bottom: vwpx(6px);
      }

      span {
        font-family: Microsoft YaHei, Microsoft YaHei;
        font-weight: 400;
        font-size: vwpx(26px);
        color: #ffffff;
      }

      .click-act {
        border: 1px dashed #dddddd;
      }
    }

    .menu-child {
      position: absolute;
      right: vwpx(160px);

      width: vwpx(140px);
      height: vwpx(240px);

      background: linear-gradient(90deg,
          rgba(2, 10, 30, 0.7) 0%,
          rgba(12, 42, 86, 0.2) 100%);
      box-shadow: inset 0px 0px vwpx(10px) 0px #3662ec;
      border-radius: vwpx(5px);
      border: 1px solid #0687ff;
      padding: vwpx(8px) vwpx(2px);
      display: flex;
      flex-direction: column;

      .menu-child-list {
        display: flex;
        align-items: center;
        justify-content: center;
        flex-direction: column;
        flex: 1;
        color: #ffffff;
        font-size: vwpx(24px);
        cursor: pointer;

        .child-list-img {
          width: vwpx(48px);
          height: vwpx(48px);
        }

        .divider {
          width: calc(100% - 8px);
          margin: 6px 4px 0 4px;
          border: 1px dashed rgba(6, 135, 255, 0.5);
        }
      }
    }

    .dialog-content {
      position: absolute;
      top: 0px;
      width: vwpx(640px);
      max-height: vwpx(990px);
      background: linear-gradient(90deg,
          rgba(2, 10, 30, 0.7) 0%,
          rgba(12, 42, 86, 0.2) 100%);
      box-shadow: inset 0px 0px 10px 0px #3662ec;
      border-radius: 10px;
      border: 1px solid #0687ff;
      padding: 8px;

      .header {
        width: 100%;
        height: vwpx(60px);
        background-image: url("~@/assets/map/title-bg-2.png");
        background-repeat: no-repeat;
        background-size: 100% 100%;

        display: flex;
        align-items: center;

        span {
          padding-left: vwpx(30px);
          font-weight: 400;
          font-size: vwpx(32px);
          color: #ffffff;
          text-shadow: 0px 0px 16px #0088ff;
          text-align: left;
          font-style: normal;
          text-transform: none;
        }

        i {
          color: #ffffff;
          cursor: pointer;
          margin-left: auto;
          margin-top: -10px;
          font-size: vwpx(36px);
        }
      }
    }
  }

  .table-list {
    background: linear-gradient(90deg,
        rgba(2, 10, 30, 0.7) 0%,
        rgba(12, 42, 86, 0.2) 100%);
    box-shadow: inset 0px 0px 10px 0px rgba(35, 134, 255, 0.5);
    border-radius: 10px;
    color: #ffffff;
    position: relative;

    .spread-packup {
      position: absolute;
      top: 40%;
      left: vwpx(-40px);
      height: vwpx(330px);
      width: vwpx(36px);
      cursor: pointer;

      background-image: url("~@/assets/map/right-close.png");
      background-repeat: no-repeat;
      background-size: 100% 100%;

      // background: linear-gradient(270deg, rgba(35, 134, 255, 0.5) 0%, rgba(35, 134, 255, 0.3) 90%, rgba(255, 255, 255, 0.8) 100%);
      // box-shadow: inset 0px 6px 6px 0px rgba(0, 85, 255, 0.3);
      // clip-path: polygon(0% 15%, 100% 0%, 100% 100%, 0 85%);

      display: flex;
      align-items: center;
      border-radius: 5px 0 0 5px;

      .img {
        transform: rotate(270deg);
        width: 100%;
      }

      .spread-img {
        transform: rotate(90deg);
        width: 100%;
      }
    }

    .table-top {
      display: flex;
      align-items: center;
      width: 100%;
      height: vwpx(60px);
      background-image: url("~@/assets/map/title-bg.png");
      background-repeat: no-repeat;
      background-size: 100% 100%;

      span {
        margin-left: vwpx(30px);
        font-family: YouSheBiaoTiHei, YouSheBiaoTiHei;
        font-weight: 400;
        font-size: vwpx(36px);
        text-shadow: 0px 0px 6px #273dff;
      }

      .btn {
        margin-left: auto;
      }
    }

    ::v-deep .el-input {
      .el-input__inner {
        background-color: rgba(1, 102, 254, 0.2);
        border: 1px solid #0166fe;
        color: #ffffff;
        // border-right: none;
      }

      .el-input__inner::placeholder {
        color: #bbbbbb;
      }

      .el-input-group__append {
        background-color: rgba(1, 102, 254, 0.2);
        border: 1px solid #0166fe;
        color: #ffffff;
        border-left: none;
        padding: 0 vwpx(20px);
        cursor: pointer;
      }
    }

    .table-content {
      ::v-deep .el-table {
        background: unset;
        border: unset;

        &::before {
          background-color: unset;
        }

        &::after {
          background-color: unset;
        }

        tr {
          background-color: unset;
        }

        tr:nth-child(even) {
          background: rgba(86, 145, 255, 0);
          color: #ffffff;
        }

        td {
          color: #ffffff;
        }

        td,
        th.is-leaf {
          border: 1px solid rgba(1, 102, 254, 0.4);
        }

        .el-table__header-wrapper tr th {
          background-color: rgba(1, 102, 254, 0.2);
          color: #ffffff !important;
          font-size: vwpx(28px);
        }

        tbody {
          background-color: unset;
          border: none;
          cursor: pointer;
        }

        .el-table__body tr:hover>td {
          background-color: unset;
        }

        .el-table__body tr.current-row>td {
          background-color: rgba(1, 102, 254, 0.2) !important;
        }

        .el-table__body tr.current-row:hover {
          background-color: rgba(1, 102, 254, 0.2);
        }
      }

      ::v-deep .el-pagination {
        .el-pagination__total {
          color: #ffffff;
        }

        .btn-prev,
        .btn-next {
          background-color: rgba(255, 255, 255, 0.1);
          border: 1px solid rgba(1, 102, 254, 0.6);
          color: #ffffff;
        }

        .el-pager {
          li {
            background-color: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(1, 102, 254, 0.6);
            color: #ffffff;
          }

          li:not(.disabled).active {
            background-color: rgba(1, 102, 254, 0.8);
            border: 1px solid rgba(1, 102, 254, 0.6);
            color: #ffffff;
          }
        }
      }

      ::-webkit-scrollbar {
        width: 10px;
        /* 滚动条宽度 */
        height: 10px;
        /* 垂直滚动条高度 */
      }

      /* 滚动条滑块 */
      ::-webkit-scrollbar-thumb {
        background-color: #ffffff;
        /* 滑块颜色 */
        border-radius: 10px;
        /* 滑块边角圆滑度 */
        border: 2px solid transparent;
        /* 滑块边框，透明边框可以增加滑块大小 */
        background-clip: content-box;
        /* 背景裁剪为内容区域，使边框可见 */
      }

      /* 滚动条轨道 */
      ::-webkit-scrollbar-track {
        background: gray;
        /* 轨道颜色 */
        border-radius: 8px;
        /* 轨道边角圆滑度 */
      }
    }

    .handler {
      position: absolute;
      top: 0;
      left: 0;
      width: vwpx(10px);
      height: 100%;
      cursor: col-resize;
    }
  }
}
</style>
