<template>
  <AssetCheck :type="type" :menu-type="menuType"></AssetCheck>
</template>

<script>
import AssetCheck from "@/views/patrol/assetCheck/index.vue";

export default {
  name: "TunnelAssetCheck",
  components: {AssetCheck},
  data(){
    return {
      type: null,
      menuType: null
    }
  },
  created() {
    console.log(this.type)
    this.type = this.$route.query.type
    this.menuType = Number(this.$route.query.menuType)
  }
};
</script>

<style scoped>

</style>
