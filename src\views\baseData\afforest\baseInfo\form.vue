<template>
  <div class="roadbedadd">
    <el-dialog
      :title="title"
      :visible.sync="showAddEdit"
      width="60%"
      append-to-body
      :before-close="handleClose"
      :close-on-click-modal="false"
      :class="forView ? 'forView':''"
    >
      <div
        v-loading="loading"
        style="height: 60vh;overflow-y: auto;padding: 0 10px 0 5px;"
      >
        <el-form
          ref="form"
          :model="form"
          :rules="rules"
          label-width="150px"
          :disabled="forView ? true : false"
        >
          <div style="display: flex; flex-wrap: wrap; margin-bottom: 20px;">
            <ManageSelectTree placeholder="请选择" :formObject="form" />
            <!-- <el-col :span="12">
              <el-form-item
                label="管理处"
                prop="managementMaintenanceId"
              >
                <SelectTree
                  v-model="form.managementMaintenanceId"
                  :dept-type="201"
                  placeholder="请选择"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item
                label="管养分处"
                prop="managementMaintenanceBranchId"
              >
                <SelectTree
                  v-model="form.managementMaintenanceBranchId"
                  :dept-type="202"
                  placeholder="请选择"
                />
              </el-form-item>
            </el-col> -->
            <el-col :span="12">
              <el-form-item
                label="养护路段"
                prop="maintenanceSectionId"
              >
                <el-select
                  v-model="form.maintenanceSectionId"
                  style="width: 100%;"
                  placeholder="请选择"
                  clearable
                  :disabled="!form.managementMaintenanceBranchId"
                >
                  <el-option
                    v-for="item in routeOptions"
                    :key="item.maintenanceSectionId"
                    :label="item.maintenanceSectionName"
                    :value="item.maintenanceSectionId"
                  />
                </el-select>
              </el-form-item>
            </el-col>

            <el-col :span="12">
              <el-form-item
                label="路线编码"
                prop="routeCode"
              >
                <el-select
                  v-model="form.routeCode"
                  placeholder="请选择"
                  :disabled="!form.maintenanceSectionId"
                  clearable
                  style="width: 100%;"
                >
                  <el-option
                    v-for="item in routeList"
                    :key="item.routeCode"
                    :label="item.routeCode"
                    :value="item.routeCode"
                  >
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item
                label="资产子类"
                prop="typeId"
              >
                <el-select
                  v-model="form.typeId"
                  style="width: 100%;"
                  placeholder="请选择"
                  clearable
                  :disabled="formData.id?true:false"
                >
                  <el-option
                    v-for="item in assetSubclassList"
                    :key="item.id"
                    :label="item.typeName"
                    :value="item.id"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item
                label="资产编码"
                prop="assetCode"
              >
                <el-input
                  v-model="form.assetCode"
                  placeholder="请输入"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item
                label="资产名称"
                prop="assetName"
              >
                <el-input
                  v-model="form.assetName"
                  placeholder="请输入"
                />
              </el-form-item>
            </el-col>
          <el-col :span="12">
            <el-form-item
              label="方向"
              prop="direction"
            >
              <el-select
                v-model="form.direction"
                style="width: 100%;"
                placeholder="请选择"
                clearable
              >
                <el-option
                  v-for="i in dict.type.sys_route_direction"
                  :key="i.value"
                  :label="i.label"
                  :value="i.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item
              label="位置"
              prop="lane"
            >
            <el-select
                v-model="form.lane"
                style="width: 100%;"
                placeholder="位置"
                clearable
              >
                <el-option
                  v-for="i in dict.type.lane"
                  :key="i.value"
                  :label="i.label"
                  :value="i.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item
              label="左右"
              prop="leftOrRight"
            >
              <el-select
                v-model="form.leftOrRight"
                style="width: 100%;"
                placeholder="请选择"
                clearable
              >
                <el-option
                  v-for="i in dict.type.left_right"
                  :key="i.value"
                  :label="i.label"
                  :value="i.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item
              label="起点桩号"
              prop="startStake"
            >
            <PileInput
                v-model="form.startStake"
                placeholder="请输入"
                />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item
              label="终点桩号"
              prop="endStake"
            >
            <PileInput
                v-model="form.endStake"
                placeholder="请输入"
                />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item
              label="经纬度"
              prop="longitude"
            >
            <lon-lat
                  type="lonlat"
                  :lon.sync="form.longitude"
                  :lat.sync="form.latitude"
                />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item
              label="施工里程桩号"
              prop="constructionStake"
            >
            <PileInput
                v-model="form.constructionStake"
                placeholder="请输入"
                />
            </el-form-item>
          </el-col>

          <el-col :span="12">
            <el-form-item
              label="统一里程桩号"
              prop="unifiedMileageStake"
            >
            <PileInput
                v-model="form.unifiedMileageStake"
                placeholder="请输入"
                />
            </el-form-item>
          </el-col>

          <el-col :span="12">
            <el-form-item
              label="国高网桩号"
              prop="nationalNetworkStake"
            >
              <PileInput
                v-model="form.nationalNetworkStake"
                placeholder="请输入"
                />

            </el-form-item>
          </el-col>

          <el-col :span="12">
            <el-form-item
              label="建成时间"
              prop="buildDate"
            >
              <el-date-picker
                style="width:100%"
                v-model="form.buildDate"
                type="date"
                placeholder="请选择建成时间"
                :picker-options="pickerOptions"
                clearable
                value-format="yyyy-MM-dd"
              />
            </el-form-item>
          </el-col>
          
          <el-col :span="12" v-for="item in formHead" :key="item.id">
            <el-form-item
              :label="item.alias"
              :prop="item.columnName"
            >
              <el-input
                v-if="item.columnType=='2'"
                v-model="itemForm[item.columnName]"
                placeholder="请输入"
              />
              <el-input-number
                v-else-if="item.columnType=='3'"
                v-model="itemForm[item.columnName]"
                style="width: 100%;"
                :min="0"
                :max="999999999"
                class="inputNumber"
              />
              <el-input-number
                v-else-if="item.columnType=='4'"
                v-model="itemForm[item.columnName]"
                style="width: 100%;"
                :min="0"
                :max="999999999"
                :precision="3"
                class="inputNumber"
              />
              <span v-else-if="item.columnType=='5'||item.columnType=='6'">
                <el-date-picker
                  style="width:100%"
                  v-model="itemForm[item.columnName]"
                  type="date"
                  placeholder="请选择"
                  clearable
                  value-format="yyyy-MM-dd"
                />
              </span>
              <el-select
                v-else-if="item.columnType=='7'"
                v-model="itemForm[item.columnName]"
                style="width: 100%;"
                placeholder="请选择"
                clearable
              >
                <el-option
                  v-for="i in [{label:'是',value:'1'},{label:'否',value:'0'}]"
                  :key="i.value"
                  :label="i.label"
                  :value="i.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
              <el-form-item label="移交管理单位" prop="transferManagementUnit">
                <el-input
                  v-model="form.transferManagementUnit"
                  style="width: 100%"
                  clearable
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="备注" prop="remark">
                <el-input
                  v-model="form.remark"
                  type="textarea"
                  autosize
                  clearable
                />
              </el-form-item>
            </el-col>
          <el-col :span="12">
            <el-form-item
              label="图片"
              prop="picPath"
            >
                <ImageUpload
                  :key="ownerId"
                  v-model="form.picPath"
                  :limit="1"
                  :owner-id="ownerId"
                  storage-path="/base/facility/baseInfo"
                />
            </el-form-item>
          </el-col>
          </div>
        </el-form>
      </div>
      <div slot="footer">
        <el-button
          v-if="!forView"
          type="primary"
          :loading="loading"
          @click="handleSubmit('submit')"
        >提 交</el-button>
        <el-button
          v-if="!forView && (!formData.id || formData.status == 1)"
          v-hasPermi="['baseData:facility:add']"
          type="primary"
          :loading="loading"
          @click="handleSubmit('save')"
        >暂 存</el-button>
        <el-button @click="handleClose">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import PileInput from '@/components/PileInput/index.vue'
import SelectTree from '@/components/DeptTmpl/selectTree'
import SectionSelect from '@/components/SectionSelect'
import ManageSelectTree from "@/components/manageSelectTree/index.vue";
import { listMaintenanceSectionAll } from '@/api/system/maintenanceSection'
import { listByMaintenanceSectionId } from '@/api/baseData/common/routeLine'
import lonLat from "@/components/mapPosition/lonLat.vue";


import {
  addFacility,
  tempFacility,
  updateFacility,
  getAssetSubclass,
  getDynamicData
} from '@/api/baseData/facility/baseInfo/index'
import { createIdWorker } from '@/api/baseData/common'

export default {
  name: 'facility-form',
  components: { PileInput, SelectTree, SectionSelect, ManageSelectTree,lonLat },
  props: {
    formData: {
      default: {}
    },
    showAddEdit: {
      default: false
    },
    title: {
      default: '添加附属设施数据'
    },
    forView: {
      default: false
    }
  },
  dicts: ['sys_route_direction','left_right','lane'],
  data() {
    return {
      loading: false,
      form: {
        managementMaintenanceId: "",
        managementMaintenanceBranchId: "",
      },
      pickerOptions: {
        disabledDate(v) {
          return v.getTime() > new Date().getTime() 
        }
      },
      itemForm: {},
      assetSubclassList: [],
      formHead: [],
      rules: {
        managementMaintenanceId: [
          { required: true, message: '请选择管理处', trigger: 'change' }
        ],
        managementMaintenanceBranchId: [
          { required: true, message: '请选择管养分处', trigger: 'change' }
        ],
        maintenanceSectionId: [
          { required: true, message: '请选择养护路段', trigger: 'change' }
        ],
        routeCode: [
          { required: true, message: '请选择路线编码', trigger: 'change' }
        ],
        typeId: [
          { required: true, message: '请选择资产类型', trigger: 'change' }
        ],

        startStake: [
          { required: true, message: '请输入起点桩号', trigger: 'blur' }
        ],

        endStake: [
          { required: true, message: '请输入终点桩号', trigger: 'blur' }
        ],
      },
      routeOptions: [],
      routeList: [],
      ownerId: null
    }
  },
  created() {
    this.init()
    this.getAssetSubclassList()
  },
  mounted() {},
  methods: {
    init() {

      if (this.formData.id) {

        this.formData.managementMaintenanceId?'':this.formData.managementMaintenanceId='';
        this.formData.managementMaintenanceBranchId?'':this.formData.managementMaintenanceBranchId='';

        this.form = JSON.parse(JSON.stringify(this.formData))
        this.itemForm = JSON.parse(JSON.stringify(this.formData.items))
      }

      createIdWorker().then(res => {
          if (res.code === 200) {
            this.ownerId = Number(res.data)
          }
        })
    },
    getAssetSubclassList() {
      getAssetSubclass({mainTypeId:7}).then(res => {
          this.assetSubclassList = res

          if(this.form.typeId){
            let data=this.assetSubclassList.find(item=>item.id==this.form.typeId)
            this.form.mainTypeId=data.mainType
            this.getDynamicList();

          }
      })
    },
    getDynamicList() {

     getDynamicData({mainTypeId:this.form.mainTypeId,typeId:this.form.typeId,isDynamic:'Y'}).then(res => {
      if(res.code==200){
         this.formHead = res.data
         let obj = {};
          // for (let key in res.data) {
          //   if (this.itemForm[key] !== undefined) {
          //     obj[key] = this.itemForm[key];
          //   }
          // }
          res.data.forEach(item => {
            for (let key in this.itemForm) {
              if (this.itemForm[item.columnName] !== undefined) {
                obj[item.columnName] = this.itemForm[item.columnName];
              }else{
                obj[item.columnName] = '';
              }
            }
          });
         
          this.itemForm={...obj};
      }
     })
   },

    handleSubmit(type) {
      let pass = true
      switch (type) {
        case 'submit':
          this.form.status = 2
          break
        case 'save':
          this.form.status = 1
          break
      }
      if (this.form.status == 2) {
        this.$refs.form.validate(valid => {
          if (valid) {
            pass = true
          } else {
            pass = false
            return false
          }
        })
      }
      if (!pass) return

      this.loading = true

      //找出this.form中所有带有Stake的字段将值变为保留三位小数
      for (let key in this.form) {
        if (Array.isArray(this.form[key])) {
          this.form[key] = this.form[key].join(',')
        }
        if (key.includes('Stake')) {
          this.form[key] = this.form[key] ? Number(this.form[key]).toFixed(3) : ''
        }
      }

      let data1=[];
      for (let key in this.itemForm) {
        if(key!=='assetId'&&key!=='id'){
          data1.push({columnName:key,columnValue:this.itemForm[key]})
        }
      }
      this.form.items=data1;

      if (this.form.id != null) {
        const api =
          this.form.status === 1 ? tempFacility :updateFacility;

        api(this.form)
          .then(response => {
            this.$modal.msgSuccess('修改成功')
            this.$emit('refresh')
          })
          .catch(() => {
            this.loading = false
          })
      } else {
        const api =
          this.form.status === 1 ? tempFacility :addFacility;

        api(this.form)
          .then(response => {
            this.$modal.msgSuccess(
              this.form.status === 1 ? '暂存成功' : '新增成功'
            )
            this.$emit('refresh')
          })
          .catch(() => {
            this.loading = false
          })
      }
    },
    deptChange(e) {
      if (!e) return
      listMaintenanceSectionAll({ departmentId: e }).then(res => {
        if (res.code == 200) {
          this.routeOptions = res.data
        }
      })
    },
    maintenanceSectionChange(e) {
      if (!e) return
      listByMaintenanceSectionId({ maintenanceSectionId: e }).then(res => {
        if (res.code == 200) {
          this.routeList = res.data || []
          // this.form.routeCode = ''
          this.$forceUpdate()
        }
      })
    },
    handleClose() {
      if (this.forView) {
        this.form = {}
        this.$emit('close', false)
      } else {
        this.$modal
          .confirm('确认退出？')
          .then(() => {
            this.form = {}
            this.$emit('close', false)
          })
          .catch(() => {})
      }
    }
  },
  watch: {
    'form.typeId'(newVal, oldVal) {

      if(this.assetSubclassList.length>0){
        let data=this.assetSubclassList.find(item=>item.id==newVal)
        this.form.mainTypeId=data.mainType
        this.getDynamicList();
      }
    },
    'form.managementMaintenanceBranchId'(newVal, oldVal) {
      if (newVal) {
        this.deptChange(newVal)
      }
      if (oldVal) {
        if(this.form.maintenanceSectionId)this.form.maintenanceSectionId=''
        if(this.form.routeCode)this.form.routeCode=''
      }
    },
    'form.maintenanceSectionId'(newVal, oldVal) {
      if (newVal) {
        this.maintenanceSectionChange(newVal)
      }
      if (oldVal && this.form.routeCode) {
        this.form.routeCode = "";
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.forView ::v-deep .el-input.is-disabled .el-input__inner {
  background-color: white;
  border-color: #dfe4ed;
  color: black;
}
::v-deep .el-dialog__header {
  border-bottom: 1px #dfe4ed solid;
  padding: 20px 30px !important;
}
::v-deep .el-divider--horizontal {
  margin: 20px 0 !important;
}
.facility-protection-card {
  width: 100%;
  border: #dfe4ed 1px solid;
  border-radius: 10px;
  padding: 20px 40px 0 0;
  position: relative;
  margin-bottom: 10px;
  i {
    cursor: pointer;
    position: absolute;
    right: 5px;
    top: 45%;
    color: #f56c6c;
  }
}
::v-deep .inputNumber {
  .el-input-number__decrease,
  .el-input-number__increase {
    display: block;
    height: 30px;
    margin-top: 1px;
    display: flex;
    justify-content: center;
    align-items: center;
  }
}
</style>
<style lang="scss">
.longitude-latitude {
  .el-input-group__prepend {
    padding: 0 10px;
  }
  .el-input__inner {
    padding: 0 5px;
  }
}
</style>
