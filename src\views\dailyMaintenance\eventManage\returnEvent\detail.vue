<template>
  <div class="road-interflow-edit" v-loading="loading">
    <el-row :gutter="15">
      <el-form ref="elForm" :model="formData" :rules="rules" size="medium" label-width="100px">
        <el-row>
          <el-col :span="12">
            <el-form-item label="管养单位" prop="domainId">
              <selectTree
                  :style="{width: '100%'}"
                  v-model="formData.domainId"
                  :deptType="100" :deptTypeList="[1, 3, 4]"
                  placeholder="管养单位"
                  clearable
                  :disabled="type !== 'add'"
                  filterable
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="养护路段" prop="maiSecId">
              <el-select v-model="formData.maiSecId" :disabled="type !== 'add'" placeholder="养护路段" clearable style="width: 100%;"
                         @change="handleChangeRoute">
                <el-option v-for="item in routeOptions"
                           :key="item.maintenanceSectionId"
                           :label="item.maintenanceSectionName"
                           :value="item.maintenanceSectionId">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="路线编码" prop="routeCode">
              <el-select
                  v-model="formData.routeCode"
                  placeholder="路线编码"
                  :disabled="type !== 'add'"
                  style="width: 100%"
              >
                <el-option
                    v-for="item in routeList"
                    :key="item.routeCode"
                    :label="item.routeCode"
                    :value="item.routeCode"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="事件编号" prop="disCode">
              <el-input v-model="formData.disCode" placeholder="请输入事件编号"  :disabled="type !== 'add'" readonly :style="{width: '100%'}">
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="起点桩号" prop="beginMile">
              <el-input v-model="formData.beginMile1" :disabled="type !== 'add'" :style="{width: '45%'}">
                <template slot="prepend">K</template>
              </el-input>
              <div style="width: 10%;text-align: center;display: inline-block;">+</div>
              <el-input v-model="formData.beginMile2" :disabled="type !== 'add'" :style="{width: '45%'}">
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="终点桩号" prop="endMile">
              <el-input v-model="formData.endMile1" :disabled="type !== 'add'" :style="{width: '45%'}">
                <template slot="prepend">K</template>
              </el-input>
              <div style="width: 10%;text-align: center;display: inline-block;">+</div>
              <el-input v-model="formData.endMile2" :disabled="type !== 'add'" :style="{width: '45%'}">
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="路线方向" prop="direction">
              <dict-select type="route_direction" clearable
                           v-model="formData.direction" placeholder="请选择路线方向"
                           :disabled="type !== 'add'"
                           style="width: 100%"></dict-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="路线车道" prop="laneArr">
              <dict-select type="lane" clearable multiple
                           v-model="formData.laneArr" placeholder="请选择路线车道"
                           :disabled="type !== 'add'"
                           style="width: 100%"></dict-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="事件分类" prop="createReason">
              <dict-select type="event_type" clearable
                           v-model="formData.createReason" placeholder="请选择事件分类"
                           :disabled="type !== 'add'"
                           style="width: 100%"></dict-select>
            </el-form-item>
          </el-col>
          <el-col :span="12" v-if="formData.createReason == 4">
            <el-form-item label="工程分类" prop="mtype">
              <dict-select v-model="formData.mtype" :disabled="type !== 'add'"
                           clearable placeholder="请选择工程分类"
                           style="width: 100%"
                           type="disease_mtype"></dict-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="处置类型" prop="dealType">
              <dict-select type="disposal_type" clearable
                           v-model="formData.dealType" placeholder="请选择处置类型"
                           :disabled="type !== 'add'"
                           style="width: 100%"></dict-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="资产类型" prop="assetMainType">
              <el-select v-model="formData.assetMainType" style="width: 100%" :disabled="type !== 'add'">
                <el-option v-for="dict in assetMainType" :key="dict.dictValue"
                           :label="dict.dictLabel" :value="dict.dictValue">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="资产名称" prop="assetType">
              <el-select v-model="formData.assetType" style="width: 100%" :disabled="type !== 'add'">
                <el-option v-for="dict in  assetType" :key="dict.dictValue"
                           :label="dict.dictLabel" :value="dict.dictValue">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="事件类型" prop="diseaseType">
              <el-select v-model="formData.diseaseType" style="width: 100%" :disabled="type !== 'add'">
                <el-option v-for="dict in disType" :key="dict.dictValue"
                           :label="dict.dictLabel" :value="dict.dictValue">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12" v-if="formData.assetMainType == '4'">
            <el-form-item label="资产" prop="assetName">
              <el-input v-model="formData.assetName" readonly @focus="handleOpenAsset"/>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="尺寸" prop="disSize">
              <el-input v-model="formData.disSize" :disabled="type !== 'add'" :style="{width: '100%'}"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="数量" prop="disQuantity">
              <el-input v-model="formData.disQuantity" :disabled="type !== 'add'" :style="{width: '100%'}"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="事件描述" prop="diseaseDesc">
              <el-input v-model="formData.diseaseDesc" type="textarea" :disabled="type !== 'add'"
                        :autosize="{minRows: 4, maxRows: 4}" :style="{width: '100%'}"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="24" v-if="type === 'examine'">
            <el-form-item label="审核意见" prop="receiveComment">
              <el-input v-model="formData.receiveComment" type="textarea"
                        :autosize="{minRows: 4, maxRows: 4}" :style="{width: '100%'}"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="附件" prop="disPicPath">
              <file-upload v-model="formData.disPicPath" :forView="type !== 'add'"></file-upload>
            </el-form-item>
          </el-col>
        </el-row>
        <div style="text-align: right;padding-right: 7.5px">
          <template v-if="type === 'add'">
            <el-button type="primary" @click="onSubmit">保 存</el-button>
          </template>
          <template v-if="type === 'examine'">
            <el-button type="primary" @click="onReview(1)">有 效</el-button>
            <el-button @click="onReview(0)">无效</el-button>
          </template>
          <el-button @click="onClose">取 消</el-button>
        </div>
      </el-form>
    </el-row>
    <el-dialog title="选择资产" destroy-on-close :visible.sync="openAsset" width="65%" append-to-body v-if="openAsset">
      <asset-select @checkAsset="checkAsset"
                    :asset-type="formData.assetType == 107 ? 31 : formData.assetType == 145 ? 32 : 33"
                    :mai-sec-id="formData.maiSecId"></asset-select>
    </el-dialog>
  </div>
</template>
<script>
import fileUpload from "@/components/FileUpload/index.vue";
import {listMaintenanceSectionAll} from "@/api/system/maintenanceSection";
import {listAllRoute} from "@/api/system/route";
import {addAuditData, editAuditData, reviewAuditData, reviewInvalidAuditData} from "@/api/dailyMaintenance/eventManage/eventReview"
import selectTree from "@/components/DeptTmpl/selectTree.vue";
import { listAllData } from "@/api/system/dict/data";
import {listAllDiseases} from "@/api/patrol/diseases";
import assetSelect from "@/components/AssetSelect/index.vue";

export default {
  inheritAttrs: false,
  components: {assetSelect, selectTree, fileUpload},
  props: {
    type: {
      type: String,
      default: ''
    },
    rowData: {
      type: Object,
      default: () => {
        return {}
      }
    },
    selectData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  data() {
    return {
      formData: {},
      rules: {},
      routeOptions: [], // 路段数据
      routeList: [], // 路线编码数据
      assetMainType: [],
      disType: [],
      openAsset: false
    }
  },
  computed: {
     assetType: function() {
      const nameList = this.assetMainType.find(item => item.dictValue === this.formData.assetMainType)
      return nameList ? nameList.children : []
    }
  },
  watch: {
    rowData: {
      handler: function (val, oldVal) {
        if (val) {
          this.formData = JSON.parse(JSON.stringify(val))
          if (this.formData.beginMile) {
            this.$set(this.formData,'beginMile1', parseInt(this.formData.beginMile / 1000))
            this.$set(this.formData,'beginMile2',this.formData.beginMile % 1000)
          } else {
            this.$set(this.formData,'beginMile1', 0)
            this.$set(this.formData,'beginMile2', 0)
          }
          if (this.formData.endMile) {
            this.$set(this.formData,'endMile1', parseInt(this.formData.endMile / 1000))
            this.$set(this.formData,'endMile2', this.formData.endMile % 1000)
          } else {
            this.$set(this.formData,'endMile1', 0)
            this.$set(this.formData,'endMile2', 0)
          }
          if (!this.formData.id) {
            this.formData = JSON.parse(JSON.stringify(this.selectData))
            this.formData.domainId = String(this.formData.domainId)
          }
          if (this.formData.lane) {
            this.formData.laneArr = this.formData.lane.split(',');
          }
          if (this.formData.assetMainType) {
            this.formData.assetMainType = String(this.formData.assetMainType)
          }
          if (this.formData.domainId) {
            this.formData.domainId = String(this.formData.domainId)
          }
          if (this.formData.assetType) {
            this.formData.assetType = String(this.formData.assetType)
          }
          if (this.formData.diseaseType) {
            this.formData.diseaseType = String(this.formData.diseaseType)
          }
          if (this.formData.maiSecId) {
            this.handleChangeRoute(this.formData.maiSecId)
          }
        }
      },
      immediate: true
    },
    "formData.domainId": {
      handler: function (val) {
        if (val) {
          if ((!this.formData.id) && (this.formData.domainId != this.selectData.domainId || this.formData.maiSecId != this.selectData.maiSecId)) {
            this.formData.maiSecName = ''
            this.formData.maiSecId = ''
          }
          listMaintenanceSectionAll({departmentId: val}).then(res => {
            if (res.code == 200) {
              this.routeOptions = res.data
            }
          })
        }
      },
      immediate: true
    },
    "formData.assetType": {
      handler: function (val) {
        this.$set(this.formData, 'disType' , '')
        this.$set(this.formData, 'assetId' , '')
        this.$set(this.formData, 'assetName' , '')
        this.disType = []
        if (val) {
          listAllDiseases({assetName: val}).then(res => {
            this.disType = res.data.map(item => {
              return {
                dictLabel: item.diseaseName,
                dictValue: item.id
              }
            })
          })
        }
      },
      immediate: true
    },
  },
  created() {
    this.getAssetType()
  },
  mounted() {
  },
  methods: {
    // 新增保存
    onSubmit() {
      // 校验表单，调用保存接口
      this.$refs['elForm'].validate(valid => {
        if (!valid) return
        this.formData.beginMile = this.formData.beginMile1 * 1000 + Number(this.formData.beginMile2)
        this.formData.endMile =  this.formData.endMile1 * 1000 + Number(this.formData.endMile2)
        if (this.formData.disPicPath && Array.isArray(this.formData.disPicPath) && this.formData.disPicPath.length > 0) {
          this.formData.disPicPath = this.formData.disPicPath[0]
        } else if (Array.isArray(this.formData.disPicPath) &&
          this.formData.disPicPath.length == 0) {
          this.formData.disPicPath = null
        }
        this.formData.handleStatus = 1
        this.formData.lane = this.formData.laneArr.join(',');
        if(this.formData.id) {
          editAuditData(this.formData).then(res => {
            if (res.code == 200) {
              this.$modal.msgSuccess('保存成功')
              this.$emit("close");
            }
          })
        } else {
          addAuditData(this.formData).then(res => {
            if (res.code == 200) {
              this.$modal.msgSuccess('保存成功')
              this.$emit("close");
            }
          })
        }
      })
    },
    onReview(status) {
      // 根据状态调用审核接口
      if (status == 1) {
        this.formData.handleStatus = 2
        reviewAuditData(this.formData).then(res => {
          if (res.code == 200) {
            this.$modal.msgSuccess('操作成功')
            this.$emit("close");
          }
        })
      } else {
        this.formData.handleStatus = 3
        reviewInvalidAuditData(this.formData).then(res => {
          if (res.code == 200) {
            this.$modal.msgSuccess('操作成功')
            this.$emit("close");
          }
        })
      }
    },
    // 选中养护路段
    handleChangeRoute(e) {
      //  获取路线数据
      listAllRoute().then((res) => {
        if (res.code == 200) {
          this.routeList = res.data || [];
        }
      });
    },
    onOpen() {
    },
    onClose() {
      this.$emit("close");
    },
    close() {
      this.$emit('update:visible', false)
    },
    handleConfirm() {
      this.$refs['elForm'].validate(valid => {
        if (!valid) return
        this.close()
      })
    },
    handleOpenAsset() {
      if (!this.formData.assetType) {
        this.$modal.msgWarning('请选择资产子类型')
        return
      }
      this.openAsset = true
    },
    checkAsset(asset) {
      this.formData.assetName = asset.name
      this.formData.assetId = asset.id
      this.openAsset = false
    },
    getAssetType() {
      listAllData({ dictType: 'sys_asset_type' }).then(res => {
        this.assetMainType = this.handleTree(res.data, "dictCode", "dictParentCode");
      })
    }
  }
}

</script>
<style lang="scss" scoped>
::v-deep {
  .el-tabs__header {
    padding-left: 20px;
    border: 0;
  }
  .el-tabs__content {
    border: 0;
  }
  .el-form-item__label {
    font-family: Microsoft YaHei, Microsoft YaHei;
    font-weight: 400;
    font-size: 14px;
    color: #333333;
  }
  .el-input.is-disabled .el-input__inner {
    background-color: white;
    border-color: #dfe4ed;
    color: #1d2129;
  }
  .el-textarea.is-disabled .el-textarea__inner {
    background-color: white;
    border-color: #dfe4ed;
    color: #1d2129;
  }
}
</style>
<style lang="scss" scoped>
@import "@/assets/styles/business.scss";
</style>
