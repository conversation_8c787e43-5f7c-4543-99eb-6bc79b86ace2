<template>
  <div class="app-container">
    <el-row :gutter="20">
      <!-- 筛选区开始 -->
      <el-col :span="24" :xs="24">
        <el-row>
          <el-col :span="24">
            <el-form ref="queryForm" :inline="true" :model="queryParams" label-width="68px" size="small">
              <el-form-item label="" prop="year">
                <el-date-picker
                  style="width: 240px"
                  @change="getNumbers"
                  v-model="queryParams.year"
                  type="year"
                  value-format="yyyy"
                  placeholder="年份"
                >
                </el-date-picker>
              </el-form-item>
              <el-form-item label="" prop="number">
                <el-select
                  v-model="queryParams.number"
                  placeholder="期数"
                  clearable
                  style="width: 240px"
                >
                  <el-option
                    v-for="item in numbers"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  >
                  </el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="" prop="maintenanceRoad">
                <RoadSection ref="roadSection" v-model="queryParams.maiSecId" placeholder="路段"
                             style="width: 100%"/>
              </el-form-item>
              <el-form-item>
                <el-button icon="el-icon-search" size="mini" type="primary" @click="handleQuery">搜索</el-button>
                <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
              </el-form-item>
            </el-form>
          </el-col>
        </el-row>
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button
              icon="el-icon-download"
              size="mini"
              type="success"
              @click="exportToExcel"
            >导出
            </el-button>
          </el-col>
        </el-row>
        <!-- 筛选区结束 -->
        <!-- 数据表格开始 -->
        <div class="tableDiv">
          <el-table v-adjust-table v-loading="loading" :data="dataList" ref="tableRef" id='outTable'
                    :height="'calc(100vh - 260px)'" border size="mini"
                    style="width: 100%">
            <el-table-column
              type="selection"
              width="50">
            </el-table-column>
            <el-table-column align="center" label="养护路段" prop="maiSecName"></el-table-column>
            <el-table-column align="center" label="年度" prop="year"></el-table-column>
            <el-table-column align="center" label="期数" prop="num"></el-table-column>
            <el-table-column align="center" label="费用权属" prop="typeName"></el-table-column>
            <el-table-column align="center" label="电费" prop="field">
              <el-table-column align="center" label="隧道电费" prop="field">
                <el-table-column align="center" label="隧道电费" prop="fund"></el-table-column>
                <el-table-column align="center" label="隧道发电油费" prop="fund2"></el-table-column>
                <el-table-column align="center" label="隧道电费合计" prop="fund3"></el-table-column>
              </el-table-column>
              <el-table-column align="center" label="三大系统电费" prop="field">
                <el-table-column align="center" label="三大系统电费" prop="fund4"></el-table-column>
                <el-table-column align="center" label="三大系统发电油费" prop="fund5"></el-table-column>
                <el-table-column align="center" label="三大系统电费合计" prop="fund6"></el-table-column>
              </el-table-column>
              <el-table-column align="center" label="电费合计" prop="fund7"></el-table-column>
            </el-table-column>
            <el-table-column align="center" label="消防车辆使用费" prop="field">
              <el-table-column align="center" label="车辆使用费" prop="fund8"></el-table-column>
            </el-table-column>
            <el-table-column align="center" label="运营非合计" prop="fund9"></el-table-column>

          </el-table>
        </div>
        <!-- 数据表格结束 -->
      </el-col>
    </el-row>
  </div>
</template>

<script>
import RoadSection from "@/views/baseData/components/roadSection/index.vue";
import SelectTree from "@/components/DeptTmpl/selectTree.vue";
import {getElectricityFeeStatistics} from "@/api/calculate/operationManageFee/enterFee";
import {getNumbersByYear} from "@/api/dailyMaintenance/metering/settlementApplication";
import FileSaver from 'file-saver'
import * as XLSX from 'xlsx'
export default {
  name: "YourComponentName",
  components: {SelectTree, RoadSection},
  data() {
    return {
      // 遮罩层
      loading: false,
      total: 0,
      queryParams: {},
      dataList: [],
      numbers: []
    };
  },
  created() {
    this.$set(this.queryParams, 'year', String(new Date().getFullYear()))
    this.handleQuery()
    this.getNumbers()
  },
  methods: {
    handleQuery() {
      this.loading = true
      getElectricityFeeStatistics(this.queryParams).then(res => {
        this.dataList = res.data
        this.loading = false
      })
    },
    getNumbers() {
      this.numbers = []
      getNumbersByYear({year: this.queryParams.year || null}).then((res) => {
        res.rows.forEach((item) => {
          this.numbers.push({
            label: item.name,
            value: item.id,
          });
        });
      });
    },
    //导出Excel
    exportToExcel() {
      /* generate workbook object from table */
      var wb = XLSX.utils.table_to_book(document.querySelector('#outTable'))
      /* get binary string as output */
      var wbout = XLSX.write(wb, { bookType: 'xlsx', bookSST: true, type: 'array' })
      try {
        FileSaver.saveAs(new Blob([wbout], { type: 'application/octet-stream' }), '电费统计.xlsx')
      } catch (e) { if (typeof console !== 'undefined') console.log(e, wbout) }
      return wbout
    },
    resetQuery() {
      this.queryParams = {}
      this.$set(this.queryParams, 'year', String(new Date().getFullYear()))
      this.handleQuery()
    }
  }
};
</script>

<style scoped>
.tableDiv {
  margin-top: 20px;
}
</style>
<style lang="scss" scoped>
@import "@/assets/styles/business.scss";
</style>
