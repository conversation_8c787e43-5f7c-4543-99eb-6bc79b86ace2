<template>
  <div>
    <el-dialog
      :visible.sync="dialogVisible"
      :before-close="handleClose"
      width="400px"
      append-to-body
      :close-on-click-modal="false"
      custom-class="custom-dialog"
    >
      <span slot="title">
        <div style="display: flex; justify-content: space-between">
          <span style="font-size: 16px; color: #1d2129">
            {{ baseTypeText || "桥梁" }}{{ this.importTypeText }}附件导入
          </span>
        </div>
      </span>
      <div
        style="
          width: 100%;
          display: flex;
          justify-content: space-between;
          margin-bottom: 10px;
        "
      >
        <div
          style="
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            line-height: 36px;
            flex: 4;
          "
        >
          <FileUpload
            :fileType="['zip']"
            :limit="1"
            v-model="reportPath"
            :storage-path="upload.storagePath"
            :owner-id="upload.ownerId"
            platform="mpkj"
          />
          <span style="color: red;">注：检查报告格式:文件名_id.后缀(id请自行导出数据获取)</span>
        </div>
      </div>

      <div slot="footer">
        <el-button :loading="loading" type="primary" @click="handleImport">
          导 入
        </el-button>
        <el-button @click="handleClose">退 出</el-button>
      </div>
    </el-dialog>

  </div>
</template>

<script>
import { periodicAdd } from "@/api/baseData/tunnel/periodic/index";
import { getToken } from "@/utils/auth";
import { createIdWorker } from "@/api/baseData/common";
 import { getFile } from "@/api/file";

export default {
  name: "ImportData",
   props: {
    dialogVisible: {
      type: Boolean,
      default: false,
    },
    id: {
      type: String || Number,
      default: "",
    },
    isUpdate: {
      type: Boolean,
      default: false,
    },
    importType: {
      type: Number,
    },
    importBaseType: {
      type: String,
      require: true,
      default: "",
    },
    mainTypeId: {
      type: Number || String,
      default: 0,
    },
    typeId: {
      type: String,
      default: "",
    },
  },
  data() {
    return {
       dialogUrl: "",
      contentType: "",
      viewLogVisible: false,
      importTypeText: this.importType === 1 ? "更新" : "新增",
       upload: {
        ownerId: "",
        storagePath:
          "base/file/import/" +(this.importBaseType
            ? this.importBaseType + "/"
            : ""),
        headers: {
          Authorization: "Bearer " + getToken(),
        },
        url: "",
      },
      flag: true,
      timeKey: undefined,
      reportPath: [],

      loading: false, // 表格数据加载状态
      updateNumber: 0, // 用于处理当前上传文件 监听触发上传文件状态改变 事件触发次数
    };
  },
  computed: {
    baseTypeText() {
      let obj = {
        1: "桥梁",
        2: "隧道",
        3: "涵洞",
        4: "互通",
        5: "路基",
        9: "挡墙",
        11: "平曲线",
        12: "桥梁定期检查病害",
        13: "隧道定期检查病害",
        14: "中央集水沟基础数据",
        15: "隧道定期检查评定记录",
        16: "积雪路段",
        17: "结冰路段",
        18: "隧道定期检查",
        19: "路肩基础数据",
        20: "交通安全设施基础 ",
        21: "附属设施基础数据",
        22: "路面基础数据",
        23: "路面结构数据",
        24: "绿化基础数据",
        25: "一级绿化基础数据",
        26: "二级绿化基础数据",
        27: "桥梁评定记录-部件",
        28: "桥梁评定记录-构件",
        29: "隧道定期检查明细",
        31: "桥梁机电设备",
        32: "路面养护处治",
        34: "隧道定期检查及病害数据",
      };
      return obj[this.importBaseType];
    },
  },
  watch: {},
  created(){
    this.idWorker()
  },
  methods: {
    async idWorker() {
      await createIdWorker()
        .then((res) => {
          if (res.code === 200) {
            this.upload.ownerId = res.data;
          } else {
            // 设置一个默认值
            // this.upload.ownerId = uuidv4();
          }
        })
        .catch((error) => {
          // 设置一个默认值
          // this.upload.ownerId = uuidv4();
        });
    },
    async uploadSubmit() {
      // 设置文件上传路径
      await this.setUploadFileUrl();
      // 文件上传
      this.$refs.upload.submit();
    },

    async handleImport() {
       if (this.reportPath.length <= 0) {
        this.$message.warning("请选择一条数据进行数据导入！");
        return;
      }

      await periodicAdd(this.reportPath).then((res) => {
        if (res.code === 200) {
          if (res.data.length > 0 && res.data[0]) {
            this.$message.error("添加失败");
            this.getZipFile(res.data[0]);
          } else {
            this.$message.success("添加成功");
            this.$emit("close", this.flag);
          }
        } else {
          this.$message.error(res.data || res.msg);
        }
        this.loading = false;
      });
    },

    async getZipFile(ownerId) {
      const r = await getFile({ ownerId: ownerId });
      if (r.code === 200) {
        this.reportPath=[]

        let url = r.data.url;
        let link = document.createElement("a");
        link.href = url; //url文件地址
        link.download = r.data.originalFilename?r.data.originalFilename:r.data.fileName //加上下载的文件名
        link.click();
        link.remove();
        this.$emit("close", this.flag);

      }
    },
    handleClose() {
      this.$modal
        .confirm("确认退出？")
        .then(() => {
          this.$emit("close", this.flag);
        })
        .catch(() => {});
    },
  },
};
</script>

<style lang="scss">
// .custom-dialog {
//   .el-dialog__body {
//     padding: 0 10px;
//   }
// }

.logo-content {
  white-space: pre-wrap; /* 保留空白符，自动换行 */
  word-wrap: break-word; /* 长单词或URL内部进行换行 */
}
</style>
<style lang="scss" scoped>
// @import '~@/assets/styles/element-ui-global.scss';
</style>
