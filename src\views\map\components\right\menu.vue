<template>
  <div class="right-menu" :style="menuStyle">
    <section v-if="routeType != '1'" class="right-menu-list" :style="{
      marginRight: tableShow ? '10px' : '',
      marginTop: tableShow ? '20px' : isBig ? '50px' : '',
    }">
      <div v-for="(item, index) in menuList" :key="index" class="menu-list" @click="handleMenuClick(item, index)">
        <img :src="item.icon" alt="" :class="clickIndex == index ? 'click-act' : ''" />
        <span>{{ item.name }}</span>
      </div>
      <div class="menu-child" :class="'h-' + menuChildList.length" v-if="childShow">
        <div v-for="(item, index) in menuChildList" :key="index" class="menu-child-list"
          @click="handleChildClick(item, index)">
          <img :src="menuIndex == index ? menuChildList[index].iconAct : item.icon" alt="" class="child-list-img" />
          <span :style="menuIndex == index ? 'color:#00FDFD' : ''">
            {{ item.name }}
          </span>
          <div class="divider" v-if="index < menuChildList.length - 1"></div>
        </div>
      </div>
      <div class="dialog-content" v-if="show" :class="dialogClass">
        <div class="header">
          <span>{{ title }}</span>
          <i class="el-icon-close" @click="handleClose"></i>
        </div>
        <div class="body">
          <component :is="page" @quit="handleClose" />
        </div>
      </div>
    </section>

    <section class="table-list" v-if="tableShow" ref="resizable" :style="tableStyle">
      <div class="spread-packup" @click="hanleSpread">
        <img src="@/assets/map/spread.png" :class="!isSpread ? 'spread-img' : 'img'" />
      </div>

      <div class="handler" @mousedown.stop="startResize"></div>

      <div v-if="isSpread">
        <div class="table-top">
          <span>{{ tableTitle }}</span>
          <div class="btn">
            <el-button type="primary" @click="onExport">导出</el-button>
            <!-- <el-button type="info" @click="">关闭</el-button> -->
          </div>
        </div>
        <el-row :gutter="10" class="mt5 mb5">
          <el-col :span="24" :offset="0">
            <el-input v-model="keyWords" :placeholder="placeholder || '请输入搜索内容'" clearable
              @keyup.enter.native.prevent="getTableList">
              <template slot="append">
                <i class="el-icon-search" @click="getTableList"></i>
              </template>
            </el-input>
          </el-col>
        </el-row>
        <div class="table-content">
          <el-table :data="tableData" border :height="tableH" @row-click="handleRowClick" v-loading="loading"
            :highlight-current-row="highlight" element-loading-background="rgba(2, 10, 30, 0.5)">
            <el-table-column v-for="(col, index) in tableHeader" :prop="col.col" :key="'table' + col.col + index"
              :label="col.name" :min-width="col.width" show-overflow-tooltip>
              <template #default="{ row }">
                <div v-if="col.dictType && col.dictData && col.type && col.type == 11"
                  :style="{ fontSize: isBig ? '0.4vw' : '' }">
                  <span>{{ col.dictData[row[col.col]] || row.mainSuperstructureTypeName }}</span>
                </div>
                <div v-else-if="col.dictType && col.type == 4" :style="{ fontSize: isBig ? '0.4vw' : '' }">
                  <dict-tag :options="dict.type[col.dictType]" :value="row[col.col]" />
                </div>
                <span v-else-if="col.type && col.type == 12" :style="{ fontSize: isBig ? '0.4vw' : '' }">
                  {{ stakeFilter(row[col.col]) }}
                </span>
                <span v-else-if="col.type && col.type == 7 && !col.precise" :style="{ fontSize: isBig ? '0.4vw' : '' }">
                  {{ row[col.col] | formatDate() }}
                </span>
                <span v-else-if="col.type && col.type == 7 && col.precise " :style="{ fontSize: isBig ? '0.4vw' : '' }">
                  {{ row[col.col] | formatDate('YYYY年MM月DD日 hh:mm:ss') }}
                </span>
                <span v-else-if="col.type && col.type == 2 && col.enumsType " :style="{ fontSize: isBig ? '0.4vw' : '' }">
                  {{ row[col.col] == 1 ? '正在预警' : '正常' }}
                </span>
                <span v-else-if="col.type && col.type == 8">
                  <ImagePreview :owner-id="row[col.col]" :key="col.col + row[col.col]" />
                </span>
                <span v-else :style="{ fontSize: isBig ? '0.36vw' : '1.4vh' }">{{ row[col.col] }}</span>
              </template>
            </el-table-column>
          </el-table>
          <el-pagination @size-change="handleSizeChange" @current-change="handleCurrentChange" style="float: right"
            :current-page.sync="params.pageNum" :page-size="params.pageSize" layout="total, prev, pager, next"
            :total="total" background :pager-count="3">
          </el-pagination>
        </div>
      </div>
    </section>
  </div>
</template>

<script>
import StakeMark from "./stakeMark.vue";
import RoadSection from "./roadSection.vue";
import RoadNetwork from "./roadNetwork.vue";
import RoutePlanning from "./routePlanning.vue";
import TollStation from "./tollStation.vue";
import DictLable from "../common/dictLable.vue";
import ImagePreview from '@/components/ImagePreview/index.vue'

import { mapState, mapActions, mapMutations } from "vuex";
import {
  getLonLat,
  gcj02towgs84,
  isValidWKT,
  addClickFeature,
  getImageById,
  lineId,
  wktToFeature,
  removeLayer,
  addFeatureToMap,
} from "../common/mapFun";
import { MultiLineString, Point } from "ol/geom";
import { transform as projTransform } from "ol/proj";
import { WKT } from "ol/format";
import * as turf from '@turf/turf';
// api
import { publicRequest, exportTable } from "@/api/oneMap/tableInfo";
import { getDictData, getDataList } from "@/api/oneMap/layerData";
import { formatDate } from "@/utils/index";
import { isBigScreen } from "../common/util";


export default {
  components: {
    StakeMark,
    RoadSection,
    RoadNetwork,
    RoutePlanning,
    TollStation,
    DictLable,
    ImagePreview,
  },
  dicts: [
    "sys_route_type",
    "sys_operation_state",
    "sys_route_direction",
    "lane",
    "base_data_state",
    "base_transform_duty",
    "is_affects_road_safety",
    "geological_points_risk_grade",
  ],
  data() {
    return {
      clickIndex: null,
      menuList: [
        {
          name: "桩号定位",
          icon: require("@/assets/map/location.png"),
          value: 1,
          page: StakeMark,
        },
        {
          name: "养护路段",
          icon: require("@/assets/map/section.png"),
          value: 2,
          page: RoadSection,
        },
        {
          name: "路网线形采集",
          icon: require("@/assets/map/network-acquisition.png"),
          value: 3,
          page: RoadNetwork,
        },
        {
          name: "大件运输",
          icon: require("@/assets/map/transportation.png"),
          value: 4,
          page: "",
        },
      ],
      menuIndex: null,
      menuChildList: [
        // {
        //   name: "路线规划",
        //   icon: require("@/assets/map/route-planning.png"),
        //   iconAct: require("@/assets/map/route-planning-act.png"),
        //   page: RoutePlanning,
        // },
        {
          // name: "收费站查看",
          name: "路线规划",
          icon: require("@/assets/map/toll-station.png"),
          iconAct: require("@/assets/map/toll-station-act.png"),
          page: TollStation,
        },
      ],
      show: false,
      childShow: false,
      title: "",
      page: null,

      tableTitle: "数据列表",
      keyWords: "",
      isSpread: true,
      tableHeader: [], // 表头数据
      tableData: [], // 表格数据
      exportAction: {}, // 导出接口及请求方式
      listAction: {}, // 列表接口及请求方式
      tableH: "100%", // 表格高度
      params: {
        pageNum: 1,
        pageSize: 20,
      }, // 列表请求参数
      total: 0, // 列表数据总数
      loading: false,
      changeKey: new Date().getTime(),
      dictData: {}, // 当前列表字典数据
      menuShowType: undefined,
      highlight: true,
      pointIcon: "",
      coordinateSystem: "",
      tableWidth: isBigScreen() ? 1600 : 400, // 初始宽度
      startX: 0, // 鼠标按下时的X坐标
      startWidth: 0, // 元素按下时的宽度
      isOneMap: false, // 是否是全屏
      isBig: false, // 是否是大屏
      routeType: '',
      managementMaintenanceIds: null,
      maintenanceSectionIds: null,
      configs: null,
      layerStyle: null,
      healthMonitoring: null,
      listQueryConfig: null,
      isHighway: false,
      placeholder: '请输入搜索内容',
      mapHighwayLayer: null,
    };
  },
  computed: {
    ...mapState({
      tableShow: (state) => state.map.tableShow,
      legendList: (state) => state.map.legendList,
      mapZoom: (state) => state.map.mapZoom,
      menuType: (state) => state.map.menuType,
    }),
    menuStyle() {
      return {
        top: this.isBig ? '50px' : this.tableShow ? '30px' : '50px',
        right: this.tableShow ? '5px' : '15px',
        zIndex: this.isSpread ? 2 : 1,
      }
    },
    tableStyle() {
      // this.tableWidth = this.isBig ? 1600 : 400;
      let height = this.isBig ? 'calc(100vh - 150px)' : this.isOneMap ? 'calc(100vh - 55px)' : 'calc(100vh - 140px)'
      return {
        width: this.isSpread ? this.tableWidth + 'px' : '0px',
        padding: this.isSpread ? '6px 10px' : '',
        border: this.isSpread ? '1px solid #0687FF' : '',
        height: height,
        marginTop: this.isBig ? '50px' : '0px',
      }
    },
    dialogClass() {
      let str = 'dialog-mr'
      if (this.childShow) {
        str = 'dialog-mr-l'
      } else {
        str = 'dialog-mr-s'
      }
      return str;
    }
  },
  async created() {
    this.routeType = this.$route.query.type || ''

    this.isOneMap = this.$route.name === "oneMap";
    this.isBig = isBigScreen();
  },
  watch: {
    tableData: {
      handler(val) {
        if (!val.length) {
          // this.getTableShow(false)
        }
      },
      deep: true,
    },
    tableShow(newValue, oldValue) {

      if (!newValue) {
        this.$emit("openTable", [], false, "");
        this.keyWords = '';
      }
      // 处理 computed 值变化的逻辑
    },
  },
  mounted() {
    // 处理表格高度
    let num = this.isBig ? 350 : this.isOneMap ? 165 : 250;
    this.tableH = window.innerHeight - num;
    window.addEventListener("resize", () => {
      this.tableH = window.innerHeight - num;
    });

    window.$Bus.$on("getTable", async (data, isHighway) => {
      this.keyWords = "";
      this.isHighway = isHighway
      this.tableData = [];
      let { params, query, menuShowType, row } = data;
      this.layerStyle = {
        borderColour: row.borderColour,
        borderWidth: row.borderWidth
      }
      this.managementMaintenanceIds = data.managementMaintenanceIds || null
      this.maintenanceSectionIds = data.maintenanceSectionIds || null
      this.isSpread = true;
      this.menuShowType = menuShowType;
      if (row && row.icon) {
        this.pointIcon = row.icon;
      } else {
        this.pointIcon = "";
      }
      if (row && row.coordinateSystem) {
        this.coordinateSystem = row.coordinateSystem;
      } else {
        this.coordinateSystem = "";
      }
      // 设置列表标题
      if (row) {
        if (this.menuShowType === 1) {
          this.tableTitle = row.name ? row.name + "列表" : "数据列表";
        } else {
          this.tableTitle =
            this.legendList && this.legendList.length
              ? this.legendList[0].name + "列表"
              : row.name + "列表" || "数据列表";
        }
      }
      let queryConfig = query ? JSON.parse(query) : {};
      this.listQueryConfig = queryConfig
      this.healthMonitoring = queryConfig?.healthMonitoring
      this.configs = queryConfig;
      this.tableHeader = [];
      if (!queryConfig.tableHeader.length) return;

      (await queryConfig.tableHeader.map(async (v) => {
        if (v.name.length <= 3) {
          v.width = v.width ? v.width : v.name.length * 40;
        } else {
          v.width = v.width ? v.width : v.name.length * 30;
        }
        // if (v.dictType) {
        //   await this.getDictList(v.dictType).then(res => {
        //     v.dictData = res || []
        //   })
        // }
        // 判断是否包含有 ifTableShow 字段
        if (!v.hasOwnProperty("ifTableShow")) {
          this.tableHeader.push(v);
        } else {
          if (v.ifTableShow == 1) {
            this.tableHeader.push(v);
          }
        }
      })) || [];

      // 导出相关
      this.exportAction = {
        url: queryConfig.exportActionUrl,
        method: queryConfig.exportActionMethod,
      };
      // 列表相关
      this.listAction = {
        url: queryConfig.listActionUrl,
        method: queryConfig.listActionMethod,
      };
      this.params = {
        pageNum: 1,
        pageSize: 20,
        ...params,
        mainTypeId: queryConfig.mainTypeId || null,
        menuSubClassifyId: row.subClassifyId || row.id || null,
        paramsDTO: {
          ...params.paramsDTO,
          ks: "",
        },
      };
      // 设计回溯单独处理
      if (this.listQueryConfig.hiddenDanger && this.listQueryConfig.detlCondf.type == 3) {
        this.params.sjhsId = row.layerMenuSubId
        this.params.menuSubClassifyId = row.subClassifyId
      }
      // 处理动态表格头部数据
      let header = await this.getTableHead(queryConfig, row);
      if (header) {
        this.tableHeader = [...this.tableHeader, ...header];
      }
      // 输入框 placeholder
      let searchFields = this.tableHeader.filter(item => item.ifLikeSearch == 1);
      if (searchFields && searchFields.length) {
        this.placeholder = '请输入' + searchFields.map(v => v.name).join('、') + '进行搜索';
      } else {
        this.placeholder = '请输入关键字进行搜索';
      }
      setTimeout(() => {
        this.getTableList();
      }, 300);
    });
  },
  destroyed() {
    window.$Bus.$off("getTable");
    this.keyWords = "";
  },
  methods: {
    ...mapActions({
      getTableShow: "map/getTableShow",
    }),
    ...mapMutations({
      setTableHeader: "map/setTableHeader",
      setLoaclBol: "map/setLoaclBol",
    }),
    // 获取动态表格头数据
    async getTableHead(config, data) {
      let heaerObj = config.dynamicTableHeader;
      if (!heaerObj) return;
      let params = {};
      heaerObj.params.forEach((item) => {
        params[item.paramColName] =
          config[item.dataColName] || data[item.dataColName];
      });
      return new Promise((resolve, reject) => {
        publicRequest(heaerObj.actionUrl, heaerObj.actionMethod, params)
          .then((res) => {
            if (res.code === 200 && res.data) {
              let arr =
                res.data.map((v) => {
                  v.width =
                    v.name.length > 3 ? v.name.length * 20 : v.name.length * 35;
                  return v;
                }) || [];
              resolve(arr);
            } else {
              resolve([]);
            }
          })
          .catch((err) => {
            reject(err);
          });
      });
    },
    // 获取列表数据
    getTableList() {
      this.params.paramsDTO.ks = this.keyWords;
      this.loading = true;
      // this.tableData = []

      // 处理正式环境对sql语句的拦截
      let params = JSON.parse(JSON.stringify(this.params));
      if (params.menuSub) delete params.menuSub;
      if (params.menuSubClassify) delete params.menuSubClassify;
      if (params.listQueryConfig) delete params.listQueryConfig;
      if (params.otherConfig) delete params.otherConfig;
      if (params.statSql) delete params.statSql;
      // 删除status字段
      if (params.status) delete params.status;
      if (params.columnName) delete params.columnName;

      publicRequest(this.listAction.url, this.listAction.method, params)
        .then((res) => {
          if (res.code === 200) {
            this.tableData = res.rows || [];
            this.total = res.total || 0;
            if (this.isHighway) {
              this.handleRowClick(res.rows[0], 'treeHighwayLayer');
            }
            // this.$forceUpdate();
          }
        })
        .finally(() => {
          this.loading = false;
        });
    },
    // 获取字典数据
    async getDictList(dictType) {
      return new Promise(async (resolve, reject) => {
        await getDictData({ dictType }).then((res) => {
          if (res.code == 200) {
            resolve(res.data);
            return res.data;
          } else {
            reject("");
          }
        });
      });
    },
    handleMenuClick(item, index) {
      let type = item.value;
      this.clickIndex = index;
      switch (type) {
        case 1:
        case 2:
        case 3:
          this.show = true;
          this.title = item.name;
          this.page = item.page;
          this.childShow = false;
          this.menuIndex = null;
          break;
        case 4:
          this.childShow = !this.childShow;
          this.menuIndex = null;
          this.show = false;
          break;
        default:
          break;
      }
    },
    // 点击子菜单
    handleChildClick(item, index) {
      this.menuIndex = index;
      this.show = true;
      this.title = item.name;
      this.page = item.page;
    },
    // 关闭弹窗
    handleClose() {
      this.show = false;
      this.title = "";
      this.menuIndex = null;
      this.setLoaclBol({ bol: false });
      removeLayer(window.mapLayer, 'clickLayer', 'name');
    },

    // 表格展开收起
    hanleSpread() {
      const list = window.mapLayer.getLayers().getArray();
      const arr = [];
      list.map((layer) => {
        if (layer.get("name") === "clickLayer") {
          arr.push(layer);
        }
      });
      arr.map((l) => {
        window.mapLayer.removeLayer(l);
      });
      this.$emit("openTable", [], false);
      this.isSpread = !this.isSpread;
    },
    // 页容量
    handleSizeChange(val) {
      this.params.pageSize = val;
      this.getTableList();
    },
    // 分页
    handleCurrentChange(val) {
      this.params.pageNum = val;
      this.changeKey = new Date().getTime();
      this.getTableList();
    },
    // 点击表格行
    async handleRowClick(row, data = null) {
      if(data && data === 'treeHighwayLayer') {
        this.mapHighwayLayer = data
      } else {
        this.mapHighwayLayer = null;
      }
      if (row.roadSectionId) {
        // 路线表格
        let obj = {
          id: "1824259894327382017",
          paramsDTO: {
            precisionParams: {
              lxbh: row.road_section_code,
            },
          },
        };
        // this.$emit("openTable", obj, true, row.road_section_name);
        await this.getData(row)
        // this.loading = true;
        // getDataList(obj).then(res => {
        //   if(res.code === 200) {
        //     this.highlight = true
        //     row.bottomTable = res.rows
        //     this.$emit('openTable', res.rows, true, row.road_section_name)
        //   }
        // }).finally(() => {
        //   this.loading = false;
        // })
        return;
      }
      let shape = row.shape;
      if (!shape) {
        shape = await this.getLonLatByRouteCode(row);
      }
      // 如果是路线 并且 shape 不存在
      if (!shape && this.configs.line) {
        this.isHighway = true;
        shape = await this.getData(row)
        return;
      }
      if (!shape) return;
      const list = window.mapLayer.getLayers().getArray();
      const arr = [];
      if (!row.id) return;
      list.map((layer) => {
        if (layer.get("layerId") === row.id) {
          arr.push(layer);
        }
      });
      if (arr.length > 0) {
        arr.map((l) => {
          window.mapLayer.removeLayer(l);
        });
        this.highlight = false;
        return;
      } else {
        this.highlight = true;
      }
      if (!isValidWKT(shape)) return;
      let feature = undefined;
      if (!this.coordinateSystem || this.coordinateSystem == "") {
        this.coordinateSystem =
          this.legendList && this.legendList.length
            ? this.legendList[0].coordinateSystem
            : "";
      }
      if ((shape.startsWith("POINT") || shape.startsWith("point")) && this.coordinateSystem === "GCJ_02") {
        let { lon, lat } = getLonLat(shape);
        let wgs84Coordinates = gcj02towgs84(lon, lat);
        const projectedCoords = projTransform(
          wgs84Coordinates,
          "EPSG:4326",
          "EPSG:3857"
        );
        let point = new Point(projectedCoords);
        let newShape = new WKT().writeGeometry(point);
        feature = new WKT().readFeature(newShape);
        // addClickFeature(feature, row, this.pointIcon, true, true, 'clickLayer', this.menuShowType)
      } else {
        feature = new WKT().readFeature(shape, {
          dataProjection: "EPSG:4326",
          featureProjection: "EPSG:3857",
        });
      }
      if (this.params.sjhsId) {
        row.sjhsId = this.params.sjhsId
        row.menuSubClassifyId = this.params.menuSubClassifyId
      }
      row.treeId = this.params.id
      // 单独处理结构监测图标
      let jgjcIcon = null;
      if (row.treeId === '1821371445660356609') {
        if (row.type === '边坡') {
          jgjcIcon = require("@/assets/map/slope.png");
        }
        if (row.type === '隧道') {
          jgjcIcon = require("@/assets/map/tunnel.png");
        }
        if (row.type === '桥梁') {
          jgjcIcon = require("@/assets/map/bridge-n.png");
        }
      }
      // 单独处理监测预警图层
      if (this.params.name === "超限警告" && row.plan_status == 1) {
        jgjcIcon = require("@/assets/map/slope-red.png");
      }
      row.style = this.layerStyle
      row.healthMonitoring = this.healthMonitoring ? this.healthMonitoring : null
      row.listQueryConfig = this.listQueryConfig ? this.listQueryConfig : null
      // 将数据加载到地图
      if (shape.startsWith("POINT") || shape.startsWith("MULTIPOINT")) {
        addClickFeature(
          feature,
          row,
          jgjcIcon ? jgjcIcon : this.pointIcon,
          true,
          true,
          "clickLayer",
          this.menuShowType
        );
      } else {
        addClickFeature(
          feature,
          row,
          null,
          true,
          true,
          "clickLayer",
          this.menuShowType
        );
      }
      // 跳转到地图
      window.mapLayer.getView().fit(feature.getGeometry().getExtent(), {
        duration: 800,
        maxZoom: 18,
        padding: [0, 500, 0, 0],
      });
    },
    // 通过桩号和路线编码获取 经纬度
    async getLonLatByRouteCode(row) {
      let stakeNumber =
        row.constructionStake ||
        row.nationalNetworkStake ||
        row.unifiedMileageStake ||
        row.startStake ||
        row.endStake;
      let routeCode = row.routeCode;
      let params = {
        stakeNumber,
        routeCode,
      };
      if (!stakeNumber || !routeCode) return null;
      return new Promise((resolve, reject) => {
        this.$modal.loading("请求中，请稍后...");
        publicRequest("/system/stationLine/getRouteData", "post", params)
          .then((res) => {
            if (res.code == 200 && res.data) {
              let data = res.data || {};
              if (data.wkt) {
                let isBool = isValidWKT(data.wkt);
                if (isBool) {
                  resolve(data.wkt);
                }
              } else {
                resolve(null);
              }
            }
          })
          .catch((err) => {
            reject(err);
          })
          .finally(() => {
            this.$modal.closeLoading();
          });
        return;
      });
    },
    // 获取shape
    async getData(row) {
      let routeCode = row.routeCode
      let precisionParams = null;
      let listQueryConfig = null;
      if (routeCode) {
        precisionParams = {
          lxbh: routeCode,
        }
        listQueryConfig = {
          detlCondf: {
            params: {
              routeId: row.routeId,
            },
            path: '/system/route/routeStatistics',
            type: 3,
            method: 'get'
          }
        }
      } else if (row.roadSectionId) { // 公路的详细信息
        precisionParams = {
          road_section_id: row.roadSectionId,
        }
        listQueryConfig = {
          detlCondf: {
            params: {
              roadSectionId: row.roadSectionId,
            },
            path: '/system/highwaySections/highwayStatistics',
            type: 3,
            method: 'get'
          }
        }
      } else if (row.maintenanceSectionId) { // 养护路段的信息
        precisionParams = {
          maintenance_section_id: row.maintenanceSectionId,
        }
        listQueryConfig = {
          detlCondf: {
            params: {
              maintenanceSectionId: row.maintenanceSectionId,
            },
            path: '/system/maintenanceSection/maintenanceStatistics',
            type: 3,
            method: 'get'
          }
        }
      }
      let params = {
        id: lineId,
        maintenanceSectionIds: this.maintenanceSectionIds,
        managementMaintenanceIds: this.managementMaintenanceIds,
        menuType: this.menuType,
        z: this.mapZoom,
        paramsDTO: {
          precisionParams: precisionParams || {
            lxbh: routeCode,
          } || null,
        },
        pageNum: 1,
        pageSize: 999,
      }
      return new Promise((resolve, reject) => {
        getDataList(params).then(res => {
          if (res.code == 200 && res.rows) {
            let data = res.rows || null;
            if (!data || (data && data.length == 0)) return;
            // 移除
            if (!this.isHighway) {
              removeLayer(window.mapLayer, 'dataLayer');
              removeLayer(window.mapLayer, "dataLayer", "name");
            } else {
              removeLayer(window.mapLayer, 'highwayLayer');
              removeLayer(window.mapLayer, "highwayLayer", "name");
            }
            let arr = [];
            res.rows.map((v, index) => {
              let shape = v.shape;
              if (!shape) return;
              let feature = wktToFeature(shape);
              if (!feature) return;
              if (index === 0) {
                let view = window.mapLayer.getView()
                // 跳转到地图
                view.fit(feature.getGeometry().getExtent(), {
                  duration: 500,
                  maxZoom: this.isBig ? 12 : 10,
                  padding: [0, 500, 0, 0],
                });

                // view.setZoom(this.isBig ? 10 : 8);
              }
              v.listQueryConfig = listQueryConfig;
              feature.set('data', v)
              // addClickFeature(feature, v, null,
              //   false,
              //   true,
              //   "clickLayer",
              //   3)
              arr.push(feature);
            });
            // addFeatureToMap(arr)
            if (this.isHighway) {
              let layerName = this.mapHighwayLayer || "highwayLayer";
              addFeatureToMap(arr, layerName)
            } else {
              addFeatureToMap(arr)
            }
            resolve(arr);
          } else {
            reject();
          }
        }).catch((err) => {
          reject(err);
        })
      })
    },
    // 数据导出
    onExport() {
      // 处理正式环境对sql语句的拦截
      let params = JSON.parse(JSON.stringify(this.params));
      if (params.menuSub) delete params.menuSub;
      if (params.menuSubClassify) delete params.menuSubClassify;
      if (params.listQueryConfig) delete params.listQueryConfig;
      if (params.otherConfig) delete params.otherConfig;
      if (params.statSql) delete params.statSql;
      // 删除status字段
      if (params.status) delete params.status;
      if (params.columnName) delete params.columnName;
      this.$modal
        .confirm("确定导出数据？")
        .then(() => {
          this.download(
            this.exportAction.url,
            params,
            `${new Date().getTime()}.xlsx`,
            {
              headers: { "Content-Type": "application/json;" },
              parameterType: this.exportAction.method == "post" ? "body" : "",
            }
          );
        })
        .then(() => { })
        .catch(() => { });
    },
    // 获取 字典数据
    getDictLabel(val, data) {
      if (val && data) {
        let arr = data.filter((v) => v.dictValue == val);
        return arr && arr.length > 0 ? arr[0].dictLabel : "";
      }
    },
    stakeFilter(value) {
      if (!value) return "";
      if (
        typeof value == "string" &&
        (value.includes("k") || value.includes("K"))
      ) {
        return value;
      } else if (typeof value == "string" && value.includes("~")) {
        // 判断是否 ~连接
        let splitArr = value.split("~");
        return (
          this.stakeFormat(splitArr[0]) + " ~ " + this.stakeFormat(splitArr[1])
        );
      } else {
        return this.stakeFormat(value);
      }
    },

    safeDivide(a, b, decimalPlaces) {
      const factor = Math.pow(10, decimalPlaces);
      return (a * factor) / (b * factor);
    },
    safeMultiply(a, b) {
      const decimalA = (a.toString().split('.')[1] || '').length;
      const decimalB = (b.toString().split('.')[1] || '').length;
      const factor = Math.pow(10, decimalA + decimalB); // 放大倍数为两数小数位数之和
      let num = (a * factor) * (b * factor) / (factor * factor)
      num = num.toString().split('.')[1]?.length > 3 ? Number(num.toFixed(3)) : num;
      return num;
    },
    stakeFormat(value) {

      if (!value) return "";
      let prefix = parseInt(value / 1000);
      let val = "";
      if (prefix < 1) {
        val = "k0+" + value;
      } else {
        let str = this.safeDivide(value, 1000, 3) + "";

        const parts = str.split(".");
        const integerPart = parseInt(parts[0]);

        const decimalPart = parts[1] ? parseFloat("0." + parts[1]) : 0;

        val = "k" + integerPart + "+" + this.safeMultiply(decimalPart, 1000);
      }
      return val;
    },
    startResize(event) {
      this.startX = event.clientX;
      this.startWidth = this.$refs.resizable.offsetWidth;

      document.addEventListener("mousemove", this.doResize);
      document.addEventListener("mouseup", this.stopResize);
    },
    doResize(event) {
      this.tableWidth = this.startWidth + this.startX - event.clientX;
    },
    stopResize() {
      document.removeEventListener("mousemove", this.doResize);
      document.removeEventListener("mouseup", this.stopResize);
    },
  },
};
</script>

<style lang="scss" scoped>
@import "@/assets/styles/utils.scss";

@font-face {
  font-family: "YouSheBiaoTiHei";
  /* 自定义的字体名称 */
  src: url("~@/assets/home/<USER>") format("truetype");
  /* 字体文件路径和格式 */
  /* 可选属性，根据需要设置 */
  font-weight: normal;
  font-style: normal;
}

.right-menu {
  position: absolute;
  display: flex;

  .right-menu-list {
    position: relative;

    .menu-list {
      width: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
      flex-direction: column;
      margin-bottom: vwpx(12px);
      cursor: pointer;

      img {
        width: vwpx(90px);
        height: vwpx(90px);
        margin-bottom: vwpx(6px);
      }

      span {
        font-family: Microsoft YaHei, Microsoft YaHei;
        font-weight: 400;
        font-size: vwpx(26px);
        color: #ffffff;
      }

      .click-act {
        border: 1px dashed #dddddd;
      }
    }

    .menu-child {
      position: absolute;
      right: vwpx(160px);
      width: vwpx(140px);
      top: vwpx(420px);

      background: linear-gradient(90deg,
          rgba(2, 10, 30, 0.7) 0%,
          rgba(12, 42, 86, 0.2) 100%);
      box-shadow: inset 0px 0px vwpx(10px) 0px #3662ec;
      border-radius: vwpx(5px);
      border: 1px solid #0687ff;
      padding: vwpx(8px) vwpx(2px);
      display: flex;
      flex-direction: column;

      .menu-child-list {
        display: flex;
        align-items: center;
        justify-content: center;
        flex-direction: column;
        flex: 1;
        color: #ffffff;
        font-size: vwpx(24px);
        cursor: pointer;

        .child-list-img {
          width: vwpx(48px);
          height: vwpx(48px);
        }

        .divider {
          width: calc(100% - 8px);
          margin: 6px 4px 0 4px;
          border: 1px dashed rgba(6, 135, 255, 0.5);
        }
      }
    }

    .h-1 {
      height: vwpx(120px);
    }

    .h-2 {
      height: vwpx(240px);
    }

    .dialog-content {
      position: absolute;
      top: 0px;
      min-width: vwpx(660px);
      min-height: vhpx(800px);
      max-height: vwpx(1200px);
      background: linear-gradient(90deg,
          rgba(2, 10, 30, 0.7) 0%,
          rgba(12, 42, 86, 0.2) 100%);
      box-shadow: inset 0px 0px 10px 0px #3662ec;
      border-radius: 10px;
      border: 1px solid #0687ff;
      padding: 8px;
      overflow-x: hidden;
      overflow-y: auto;

      .header {
        width: 100%;
        height: vwpx(60px);
        background-image: url("~@/assets/map/title-bg-2.png");
        background-repeat: no-repeat;
        background-size: 100% 100%;

        display: flex;
        align-items: center;

        span {
          padding-left: vwpx(30px);
          font-weight: 400;
          font-size: vwpx(32px);
          color: #ffffff;
          text-shadow: 0px 0px 16px #0088ff;
          text-align: left;
          font-style: normal;
          text-transform: none;
        }

        i {
          color: #ffffff;
          cursor: pointer;
          margin-left: auto;
          margin-top: -10px;
          font-size: vwpx(36px);
        }
      }

      .body {
        width: 100%;
        height: calc(100% - 60px);
        overflow-y: auto;
      }
    }

    .dialog-mr-l {
      right: vwpx(310px);
    }

    .dialog-mr-s {
      right: vwpx(160px);
    }
  }

  .table-list {
    background: linear-gradient(90deg,
        rgba(2, 10, 30, 0.7) 0%,
        rgba(12, 42, 86, 0.2) 100%);
    box-shadow: inset 0px 0px 10px 0px rgba(35, 134, 255, 0.5);
    border-radius: 10px;
    color: #ffffff;
    position: relative;

    .spread-packup {
      position: absolute;
      top: 40%;
      left: vwpx(-40px);
      height: vwpx(330px);
      width: vwpx(36px);
      cursor: pointer;

      background-image: url("~@/assets/map/right-close.png");
      background-repeat: no-repeat;
      background-size: 100% 100%;

      // background: linear-gradient(270deg, rgba(35, 134, 255, 0.5) 0%, rgba(35, 134, 255, 0.3) 90%, rgba(255, 255, 255, 0.8) 100%);
      // box-shadow: inset 0px 6px 6px 0px rgba(0, 85, 255, 0.3);
      // clip-path: polygon(0% 15%, 100% 0%, 100% 100%, 0 85%);

      display: flex;
      align-items: center;
      border-radius: 5px 0 0 5px;

      .img {
        transform: rotate(270deg);
        width: 100%;
      }

      .spread-img {
        transform: rotate(90deg);
        width: 100%;
      }
    }

    .table-top {
      display: flex;
      align-items: center;
      width: 100%;
      height: vwpx(60px);
      background-image: url("~@/assets/map/title-bg.png");
      background-repeat: no-repeat;
      background-size: 100% 100%;

      span {
        margin-left: 1.4vh;
        font-family: YouSheBiaoTiHei, YouSheBiaoTiHei;
        font-weight: 400;
        font-size: vwpx(36px);
        text-shadow: 0px 0px 6px #273dff;
        flex-shrink: 0;
      }

      .btn {
        margin-left: auto;
        padding: unset;
        button {
          font-size: 1.25vh;
        }
      }
    }

    ::v-deep .el-input {

      .el-input__inner {
        background-color: rgba(1, 102, 254, 0.2);
        border: 1px solid #0166fe;
        color: #ffffff;
        height: vwpx(70px);
        font-size: vwpx(28px);
      }

      .el-input__inner::placeholder {
        color: #bbbbbb;
      }

      .el-input-group__append {
        background-color: rgba(1, 102, 254, 0.2);
        border: 1px solid #0166fe;
        color: #ffffff;
        border-left: none;
        padding: 0 vwpx(20px);
        cursor: pointer;
        font-size: vwpx(28px);
      }
    }

    .table-content {
      margin-top: vwpx(15px);

      ::v-deep .el-table {
        background: unset;
        border: unset;
        min-width: unset;

        &::before {
          background-color: unset;
        }

        &::after {
          background-color: unset;
        }

        tr {
          background-color: unset;
        }

        tr:nth-child(even) {
          background: rgba(86, 145, 255, 0);
          color: #ffffff;
        }

        td {
          color: #ffffff;
        }

        td,
        th.is-leaf {
          border: 1px solid rgba(1, 102, 254, 0.4);
        }

        .el-table__header-wrapper tr th {
          background-color: rgba(1, 102, 254, 0.2);
          color: #ffffff !important;
          font-size: vwpx(28px);
        }

        tbody {
          background-color: unset;
          border: none;
          cursor: pointer;
        }

        .el-table__body tr:hover>td {
          background-color: unset;
        }

        .el-table__body tr.current-row>td {
          background-color: rgba(1, 102, 254, 0.2) !important;
        }

        .el-table__body tr.current-row:hover {
          background-color: rgba(1, 102, 254, 0.2);
        }

        .el-table__row {
          height: vwpx(76px) !important;
          line-height: vwpx(76px) !important;
        }

        .cell {
          line-height: unset !important;
        }
      }

      ::v-deep .el-pagination {
        .el-pagination__total {
          color: #ffffff;
          font-size: vwpx(28px);
        }

        .btn-prev,
        .btn-next {
          background-color: rgba(255, 255, 255, 0.1);
          border: 1px solid rgba(1, 102, 254, 0.6);
          color: #ffffff;
          font-size: vwpx(28px);
        }

        .el-pager {
          li {
            background-color: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(1, 102, 254, 0.6);
            color: #ffffff;
            font-size: vwpx(28px);
          }

          li:not(.disabled).active {
            background-color: rgba(1, 102, 254, 0.8);
            border: 1px solid rgba(1, 102, 254, 0.6);
            color: #ffffff;
            font-size: vwpx(28px);
          }
        }
      }

      ::-webkit-scrollbar {
        width: vwpx(20px);
        /* 滚动条宽度 */
        height: vwpx(20px);
        /* 垂直滚动条高度 */
      }

      /* 滚动条滑块 */
      ::-webkit-scrollbar-thumb {
        background-color: rgba(35, 134, 255, 0.3);
        /* 滑块颜色 */
        border-radius: vwpx(10px);
        /* 滑块边角圆滑度 */
        border: vwpx(4px) solid transparent;
        /* 滑块边框，透明边框可以增加滑块大小 */
        background-clip: content-box;
        /* 背景裁剪为内容区域，使边框可见 */
      }

      /* 滚动条轨道 */
      ::-webkit-scrollbar-track {
        background: rgba(2, 10, 30, 0.8);
        /* 轨道颜色 */
        border-radius: vwpx(16px);
        /* 轨道边角圆滑度 */
      }
    }

    .handler {
      position: absolute;
      top: 0;
      left: 0;
      width: vwpx(10px);
      height: 100%;
      cursor: col-resize;
    }
  }
}
</style>
