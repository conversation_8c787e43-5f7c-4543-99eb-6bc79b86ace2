<template>
  <el-dialog title="选择报价" destroy-on-close :visible.sync="libModel" width="500px" append-to-body v-if="libModel">
    <el-input
        placeholder="输入关键字进行过滤"
        v-model="filterText">
    </el-input>

    <el-tree
        class="filter-tree"
        node-key="id"
        :data="libData"
        style="height: 300px;overflow-y: scroll"
        :show-checkbox="!isSingle"
        :props="defaultProps"
        :default-expanded-keys="idArr"
        :check-on-click-node="true"
        :filter-node-method="filterNode"
        v-loading="loading"
        @node-click="clickNode"
        @node-expand="expandNode"
        ref="tree">
    </el-tree>
    <div style="text-align: right;padding-right: 7.5px">
      <el-button type="primary" @click="checkLib">保 存</el-button>
      <el-button @click="libModel = false">取 消</el-button>
    </div>
  </el-dialog>
</template>
<script>
import {getTreeData} from "@/api/contract/quotationSystem";

export default {
  data() {
    return {
      libModel: false,
      filterText: '',
      libData: [],
      loading: false,
      expandedIndex: 0,
      idArr: [],
      defaultProps: {
        children: 'children',
        label: 'showName',
        disabled: function (data) {
          if (data.nodeType != 2 || !data.children) {
            return true
          }
        }
      }
    }
  },
  props: {
    conId: {
      type: String,
      default: ''
    },
    schemeType: {
      type: String,
      default: ''
    },
    // 是否单选
    isSingle: {
      type: Boolean,
      default: false
    },
    domainId: {
      type: String,
      default: ''
    }
  },
  watch: {
    conId: {
      handler(val) {
        if (val) {
          this.getLibData()
        }
      },
      immediate: true
    },
    filterText(val) {
      this.$refs.tree.filter(val);
    }
  },
  methods: {
    checkLib() {
      let checkDatas = this.$refs.tree.getCheckedNodes()
      console.log("checkDatas",checkDatas)
      checkDatas = checkDatas.filter(item => {
        return item.nodeType == 2
      })
      this.$attrs.value = checkDatas
      this.$emit('input', checkDatas)
      this.libModel = false
    },
    getLibData() {
      this.loading = true
      // 获取报价体系
      getTreeData({
        conId: this.conId,
        schemeType: this.schemeType,
        isConstruction: '是',
        domain: this.domainId
      }).then(res => {
        this.changeName(res.rows)
        this.libData = res.rows || []
        this.loading = false
      })
    },
    changeName(nodeArr) {
      this.expandedIndex ++
      nodeArr.forEach(item => {
        if (this.expandedIndex == 2) this.idArr.push(item.id)
        item.showName = item.schemeName
        if (item.schemeCode != item.schemeName) item.showName = item.schemeCode + '    ' + item.schemeName
        if (item.children) {
          this.changeName(item.children)
        }
      })
    },
    openLibModel() {
      this.libModel = true
    },
    filterNode(value, data) {
      if (!value) return true;
      return data.schemeName.indexOf(value) !== -1 || data.schemeCode.indexOf(value) !== -1;
    },
    clickNode(e) {
      if (!this.isSingle) return
      if (e.nodeType != 2) return;
      this.$attrs.value = e
      this.$emit('input', e)
      this.libModel = false
    },
    expandNode(node) {
      if (node.parentId != '0') {
	      this.idArr = []
        this.expandAllDescendants(node);
      }
    },
    expandAllDescendants(node) {
      if (node.children && node.children.length > 0) {
        node.children.forEach(child => {
	        this.idArr.push(child.id)
          this.expandAllDescendants(child);
        });
      }
    },
  }
}
</script>
<style scoped lang="scss">

</style>
