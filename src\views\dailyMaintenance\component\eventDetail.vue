<template>
  <div style="overflow-y: hidden; overflow-x: hidden">
    <el-row :gutter="15" v-loading="!flag" style="height: 80vh;">
      <el-col :span="daliyId ? 10 : 24">
        <event-detail-left v-if="flag" :row-data="disData" :read-only="true"></event-detail-left>
      </el-col>
      <el-col v-if="daliyId" :span="14">
        <el-collapse v-model="activeNames">
          <el-collapse-item shadow="never" title="事件处理流程" name="1">
            <div style="overflow-y: scroll; height:1160px" v-if="nodeList.diseaseNodeNameList">
              <el-steps direction="vertical" :active="nodeList.diseaseNodeNameList.length">
                <el-step v-for="(item,index) in nodeList.diseaseNodeNameList">
                  <template #icon>{{ nodeList.diseaseNodeNameList.length - index }}</template>
                  <template #title>
                    <div :style="{color: item.direction ? 'none' : 'red'}">{{ item.nodeName }}
                      <i v-if="item.direction" class="el-icon-circle-check"/>
                      <i v-else class="el-icon-circle-close"/>
                      &nbsp&nbsp&nbsp{{ item.endTime }}
                      <i v-hasPermi="['comm:node:opinion:edit']" class="el-icon-edit-outline" @click="openEditDialog(item)" style="cursor: pointer;"></i>
                      <i v-hasPermi="['comm:node:edit']" v-if="item.nodeName == '施工登记' || item.nodeName == '完工登记'"  class="el-icon-document" @click="openDrawer(item)" style="cursor: pointer;"></i>
                    </div>
                  </template>
                  <template #description>
                    <el-descriptions :column="1" :colon="false">
                      <el-descriptions-item label="操作人">{{ item.assigneeName }}</el-descriptions-item>
                      <el-descriptions-item label="意见">{{ item.comment }}</el-descriptions-item>
                      <template v-if="item.attachment && item.attachment.length > 0">
                        <el-descriptions-item label="附件">
                        </el-descriptions-item>
                        <el-descriptions-item>
                          <file-upload v-for="file in item.attachment" v-model="file.fileId" :forView="true"></file-upload>
                        </el-descriptions-item>
                      </template>
                      <template v-if="item.image && item.image.length > 0">
                        <el-descriptions-item label="登记照片">
                        </el-descriptions-item>
                        <el-descriptions-item>
                          <file-upload v-for="file in item.image" v-model="file.fileId" :forView="true"></file-upload>
                        </el-descriptions-item>
                      </template>
                      <el-descriptions-item v-if="item.methodList && item.methodList.length > 0" label="方法清单">
                      </el-descriptions-item>
                      <el-descriptions-item v-if="item.methodList && item.methodList.length > 0">
                        <el-table v-adjust-table :data="item.methodList" border size="mini">
                          <el-table-column prop="schemeCode" label="子目号" align="center"/>
                          <el-table-column prop="schemeName" label="方法名" align="center"/>
                          <el-table-column prop="calcDesc" label="计算式" align="center"/>
                          <el-table-column prop="num" label="数量" align="center"/>
                          <el-table-column prop="remark" label="备注" align="center"/>
                        </el-table>
                      </el-descriptions-item>
                    </el-descriptions>
                  </template>
                </el-step>
              </el-steps>
            </div>
          </el-collapse-item>
<!--          <el-collapse-item class="mt20" shadow="never" title="任务单流程" name="2">-->
<!--            <div style="overflow-y: scroll; height:580px" v-if="nodeList.nodeNameList">-->
<!--              <el-steps direction="vertical" :active="nodeList.nodeNameList.length">-->
<!--                <el-step v-for="(item,index) in nodeList.nodeNameList">-->
<!--                  <template #icon>{{ nodeList.nodeNameList.length - index }}</template>-->
<!--                  <template #title>-->
<!--                    <div :style="{color: item.direction ? 'none' : 'red'}">{{ item.nodeName }}-->
<!--                      <i v-if="item.direction" class="el-icon-circle-check"/>-->
<!--                      <i v-else class="el-icon-circle-close"/>-->
<!--                      &nbsp&nbsp&nbsp{{ item.endTime }}-->
<!--                      <i v-hasPermi="['comm:node:opinion:edit']" class="el-icon-edit-outline" @click="openEditDialog(item)" style="cursor: pointer;"></i>-->
<!--                    </div>-->
<!--                  </template>-->
<!--                  <template #description>-->
<!--                    <el-descriptions :column="1">-->
<!--                      <el-descriptions-item label="操作人">{{ item.assigneeName }}</el-descriptions-item>-->
<!--                      <el-descriptions-item label="意见">{{ item.comment }}</el-descriptions-item>-->
<!--                    </el-descriptions>-->
<!--                  </template>-->
<!--                </el-step>-->
<!--              </el-steps>-->
<!--            </div>-->
<!--          </el-collapse-item>-->
        </el-collapse>
      </el-col>
    </el-row>
    <el-dialog
      append-to-body
      :visible.sync="editDialog"
      width="650px"
    >
      <el-form :model="editForm">
        <el-form-item label="操作人" label-width="100px">
          <el-cascader
              v-model="editForm.assignee"
              :options="deptUserOptions"
              :props="assigneeProps"
              :show-all-levels="false"
              ref="assigneeRef"
              filterable
              clearable
              @change="assigneeChange"
              style="width: 460px"
            >
          </el-cascader>
        </el-form-item>
        <el-form-item label="操作意见" label-width="100px">
          <el-input v-model="editForm.content" style="width: 460px;"/>
        </el-form-item>
        <el-form-item label="操作时间" label-width="100px">
          <el-date-picker
            v-model="editForm.endTime"
            type="datetime"
            format="yyyy-MM-dd HH:mm:ss"
            value-format="yyyy-MM-dd HH:mm:ss"
            placeholder="结束时间">
          </el-date-picker>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="editDialog = false">取 消</el-button>
        <el-button type="primary" @click="saveEdit">确 定</el-button>
      </span>
    </el-dialog>
    <el-drawer
      append-to-body
      modal-append-to-body
      :wrapperClosable="false"
      :title="drawerTitle"
      destroy-on-close
      :visible.sync="drawer"
      size="70%"
    >
      <!-- 施工登记详情 -->
      <RegDetail v-if="detailType==='reg'" @close="handleCloseDetail" :row-data="detailData" :from-event="true"></RegDetail>
      <!-- 完工登记详情 -->
      <ComDetail v-if="detailType==='com'" @close="handleCloseDetail" :row-data="detailData" :from-event="true"></ComDetail>
    </el-drawer>
  </div>
</template>

<script>
import {disNodeInfo, getFinishedId, getRegisterId} from "@/api/dailyMaintenance/construction/registration";
import {getDiseaseDataById} from "@/api/dailyMaintenance/eventManage/eventData";
import { getTreeStruct,editNodeInfo } from "@/api/tmpl";
import EventDetailLeft from "./eventDetailLeft.vue";
import RegDetail from "@/views/dailyMaintenance/construction/registration/detail.vue"
import ComDetail from "@/views/dailyMaintenance/construction/complete/detail.vue"
export default {
  name: 'eventInfo',
  components: {EventDetailLeft, RegDetail, ComDetail},
  dicts: ['route_direction', 'lane', 'event_type', 'sys_asset_type'],

  data() {
    return {
      nodeList: [],
      disData: {},
      flag: false,
      activeNames: ['1', '2'],
      editDialog: false,
      editForm: {
        id: '',
        assignee: '',
        content: '',
        startTime: '',
        endTime: '',
        taskId: ''
      },
      deptUserOptions: [],
      assigneeProps: {
        multiple: false, //是否多选
        value: 'id',
        emitPath: false,
      },
      curUser: null,
      drawer: false,
      detailType: '',
      finishedId: '',
      registerId: '',
      detailData: {},
      drawerTitle: '',
    }
  },
  props: {
    daliyId: {
      type: String,
      default: ''
    },
    disInfo: {
      type: Object,
      default: {}
    },
    disId: {
      type: String,
      default: ''
    }
  },
  mounted() {
    const id = this.disId || this.disInfo.id
    if (id) {
      getDiseaseDataById(id).then(res => {
        this.disData = res.data
        if (!this.daliyId) this.daliyId = res.data.daliyId
        this.getNodeInfo()
        this.flag = true
      })
    }
    if (id) {
      getFinishedId(id).then(res=> {
        this.finishedId = res.msg
      })
      getRegisterId(id).then(res=> {
        this.registerId = res.msg
      })
    }
    this.getDeptTreeDef()
  },
  methods: {
    getNodeInfo() {
      disNodeInfo({
        daliyId: this.daliyId,
        detailId: this.disData.detailId
      }).then(res => {
        if (res.data.diseaseNodeNameList) {
          if (res.data.nodeNameList) {
            res.data.nodeNameList.slice().reverse()
            res.data.diseaseNodeNameList.push(...res.data.nodeNameList)
            delete res.data.nodeNameList
          }
          res.data.diseaseNodeNameList.slice().reverse()
          for (let i = 0; i < res.data.diseaseNodeNameList.length; i++) {
            let item = res.data.diseaseNodeNameList[i]
            if (item.fileList) {
              if (item.nodeName == '完工登记') {
                item.attachment = item.fileList.filter(item => item.registerType == 5)
                item.image = item.fileList.filter(item => item.registerType == 6)
              }
              if (item.nodeName == '施工登记') {
                item.attachment = item.fileList.filter(item => item.registerType == 2)
                item.image = item.fileList.filter(item => item.registerType == 0)
              }
              if (item.nodeName == '施工单位审核') {
                item.attachment = item.fileList.filter(item => item.registerType == 8)
              }
              if (item.nodeName == '监理单位审核') {
                item.attachment = item.fileList.filter(item => item.registerType == 9)
              }
              if (item.nodeName == '验收登记') {
                item.attachment = item.fileList.filter(item => item.registerType == 7)
              }
            }
          }
        }
        // if (res.data.nodeNameList) {
        //   res.data.nodeNameList.slice().reverse()
        // }
        this.nodeList = res.data
      })
    },
    openEditDialog(row) {
      this.editForm.id = row.id
      this.editForm.taskId = row.taskId
      this.editForm.assignee = row.userId
      this.editForm.content = row.comment
      this.editForm.startTime = row.startTime
      this.editForm.endTime = row.endTime
      this.curUser = {
        label: row.assignee,
        value: row.userId
      }
      this.editDialog = true
    },
    assigneeChange(value) {
       this.$nextTick(() => {
        this.curUser = this.$refs.assigneeRef.getCheckedNodes()[0]
      })
    },
    saveEdit() {
      const submitData = {
        ...this.editForm
      }
      submitData.assignee = `${this.curUser.label}@${this.curUser.value}`
      submitData.startTime = submitData.endTime
      editNodeInfo(submitData).then(()=> {
        this.getNodeInfo()
        this.editDialog = false
      })
    },
    getDeptTreeDef() {
      getTreeStruct({ types: 111, dataRule: false }).then((response) => {
        this.deptUserOptions = response.data;
      });
    },
    handleCloseDetail() {
      this.drawer = false
      this.getNodeInfo()
    },
    openDrawer(item) {
      if(item.nodeName == '施工登记') {
        this.detailType = 'reg'
        this.detailData.id = this.registerId
        this.drawerTitle = '施工登记'
      }
      if(item.nodeName == '完工登记') {
        this.detailType = 'com'
        this.detailData.id = this.finishedId
        this.drawerTitle = '完工登记'
      }
      this.$nextTick(()=> {
        this.drawer = true
      })
    }
  }
}
</script>

<style lang="scss" scoped>
::v-deep .el-step__description {
  padding: 0!important;
}
::v-deep .el-step__title.is-finish {
  color: #069847;
}
::v-deep .el-step__head.is-finish {
  color: #069847;
  border-color: #069847;
}
</style>
<style lang="scss" scoped>
@import "@/assets/styles/business.scss";
</style>
