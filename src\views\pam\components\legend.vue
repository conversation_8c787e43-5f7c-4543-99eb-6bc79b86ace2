<template>
  <div class="legend" v-show="legendShow" :style="{ left: treeW + 100 + 'px' }">
    <!-- 标题、关闭 -->
    <div class="legend-header">
      <span>图例</span>
      <i class="el-icon-close close" @click="handleClose"></i>
    </div>
    <!-- 分割线 -->
    <div class="divider"></div>

    <!-- 图例内容 -->
    <div class="legend-body">
      <div class="list" v-for="(item, index) in legendList" :key="index">
        <template v-if="item.figureId">
          <img :src="item.icon" v-if="item.icon" alt="" />
          <ImgView :id="item.figureId" v-else-if="item.figureId" style="padding-top: 5px;" />
          <img :src="normalUrl" v-else alt="" />
        </template>
        <span v-else class="cover"
          :style="{ backgroundColor: item.interiorColour, borderColor: item.borderColour }"></span>
        <span class="name">{{ item.name }}</span>
      </div>
    </div>
  </div>
</template>

<script>
import { mapState, mapActions, mapMutations } from 'vuex';
import ImgView from './common/imgView.vue'
import { removeLayer } from './common/mapFun'

export default {
  components: {
    ImgView,
  },
  data() {
    return {
      normalUrl: require("@/assets/map/bridge.png"),
    }
  },
  computed: {
    ...mapState({
      treeW: state => state.map.treeW,
      legendList: state => state.map.legendList,
      legendShow: state => state.map.legendShow,
      layerSetting: state => state.map.layerSetting,
      menuShowType: state => state.map.menuShowType,
    })
  },
  watch: {
    legendList: {
      async handler(val) {
        // 如果图列列表为空则清楚适量数据加载
        if (!val || (val && !val.length)) {
          removeLayer(window.mapLayer, 'pbfLayer')
          let arr = this.layerSetting
          arr.length = 3
          this.setLayer({ layer: arr })
        } else {
          // 清空 图列数据
          let arr = this.layerSetting
          arr.length = 3
          this.setLayer({ layer: arr })

          // 重新加载图例数据到 图层设置
          let arrs = val.map(v=>{
            v.layerName = this.menuShowType == 1 ? v?.columnName ? ((v?.columnName + v?.value) || v.id) : v.id : 'pbfLayer';
            v.ifShow = true;
            v.opacity = 100;
            return v;
          })
          this.setLayer({ layer: arrs });
        }
      },
      deep: true,
    }
  },
  methods: {
    ...mapActions({
      legendShowOrHide: 'map/legendShowOrHide',
      getLegend: 'map/getLegend',
    }),
    ...mapMutations({
      setLayer: 'map/setLayer',
    }),
    handleClose() {
      this.legendShowOrHide(false)
      // 全局触发 关闭图列
      window.$Bus.$emit('legendClose', false);
    },
  }
}
</script>

<style lang="scss" scoped>
@import "@/assets/styles/utils.scss";

.legend {
  min-width: vwpx(228px);
  min-height: vwpx(200px);
  max-height: vwpx(580px);

  overflow-y: auto;
  background: rgba(4, 17, 48, 0.6);
  box-shadow: inset 0px 0px 10px 0px #3662EC;
  border-radius: 4px;
  border: 1px solid #0687FF;

  position: absolute;
  bottom: 2px;
  padding: vwpx(10px);

  .legend-header {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #ffffff;

    span {
      flex: 5;
      text-align: center;
      margin-left: vwpx(10px);
    }

    .close {
      flex: 1;
      cursor: pointer;
    }
  }

  .divider {
    border: 1px dashed rgba(6, 135, 255, 0.3);
    margin: 5px 0;
  }

  .legend-body {
    .list {
      display: flex;
      align-items: center;
      margin-bottom: vwpx(10px);

      img {
        width: vwpx(40px);
        height: vwpx(40px);
      }

      .cover {
        display: block;
        width: vwpx(40px);
        height: vwpx(20px);
      }

      .name {
        font-family: Microsoft YaHei, Microsoft YaHei;
        font-weight: 400;
        font-size: vwpx(26px);
        color: #FFFFFF;
        margin-left: vwpx(10px);
      }
    }
  }
}
</style>