{"printWidth": 100, "tabWidth": 2, "useTabs": true, "semi": false, "singleQuote": true, "proseWrap": "preserve", "arrowParens": "always", "bracketSpacing": true, "endOfLine": "auto", "eslintIntegration": false, "htmlWhitespaceSensitivity": "ignore", "ignorePath": ".prettieri<PERSON>re", "jsxBracketSameLine": false, "jsxSingleQuote": false, "parser": "vue", "requireConfig": false, "stylelintIntegration": false, "trailingComma": "es5", "tslintIntegration": false}