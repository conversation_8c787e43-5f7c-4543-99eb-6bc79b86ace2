<template>
  <div class="app-container maindiv">
    <el-row :gutter="20">
      <!--部门数据-->
      <el-col :span="relaNav ? 5 : 0" :xs="24" class="leftDiv">
        <!--折叠图标-->
        <div class="leftIcon" @click="relaNav = false">
          <span class="el-icon-caret-left"></span>
        </div>
        <div class="head-container">
          <el-input
            v-model="keyword"
            clearable
            placeholder="输入关键词检索"
            prefix-icon="el-icon-search"
            size="small"
            style="margin-bottom: 20px"
            @change="handleSearch"
          />
        </div>
        <div class="head-container" style="width: 300px">
          <el-tree
            ref="tree"
            :data="filteredTreeData"
            :default-expanded-keys="[1]"
            :expand-on-click-node="false"
            :filter-node-method="filterNode"
            highlight-current
            node-key="id"
            @node-click="handleNodeClick"
          >
          </el-tree>
        </div>
      </el-col>
      <!--角色数据-->
      <el-col :span="relaNav ? 19 : 24" :xs="24">
        <!--展开图标-->
        <div v-show="!relaNav" class="rightIcon" @click="relaNav = true">
          <span class="el-icon-caret-right"></span>
        </div>
        <!--操作按钮区开始-->
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button
              icon="el-icon-download"
              size="mini"
              type="primary"
              @click="exportList"
            >导出报表
            </el-button
            >
          </el-col>
          <el-col :span="1.5">
            <el-button
              icon="el-icon-download"
              size="mini"
              type="warning"
              @click="exportList"
            >导出清单
            </el-button
            >
          </el-col>
          <right-toolbar
            :columns="columns"
            :showSearch.sync="showSearch"
            @queryTable="handleQuery"
          ></right-toolbar>
        </el-row>
        <el-row>
          <div class="draggable">
            <el-table ref="dataTable"
                      v-adjust-table
                      v-loading="loading"
                      :data="tableData"
                      :height="'calc(100vh - 270px)'"
                      border
                      row-key="id"
                      size="mini"
                      stripe
                      style="width: 100%"
            >
              <el-table-column
                align="center"
                label="序号"
                type="index"
                width="50"
              />
              <template v-for="(column, index) in columns">
                <el-table-column
                  v-if="column.visible"
                  :label="column.label"
                  :prop="column.field"
                  :width="column.width"
                  align="center"
                  show-overflow-tooltip
                >
                  <template slot-scope="scope">
                    <dict-tag
                      v-if="column.dict"
                      :options="dict.type[column.dict]"
                      :value="scope.row[column.field]"
                    />
                    <template v-else-if="column.slots">
                      <RenderDom
                        :index="index"
                        :render="column.render"
                        :row="scope.row"
                      />
                    </template>
                    <span v-else-if="column.isTime">{{
                        parseTime(scope.row[column.field], "{y}-{m}-{d}")
                      }}</span>
                    <span v-else>{{ scope.row[column.field] }}</span>
                  </template>
                </el-table-column>
              </template>
            </el-table>
            <pagination
              v-show="total > 0"
              :limit.sync="queryParams.pageSize"
              :page.sync="queryParams.pageNum"
              :total="total"
              @pagination="handleQuery"
            />
          </div>
        </el-row>
      </el-col>
    </el-row>
    <el-dialog v-if="infoDialog"
               :visible.sync="infoDialog"
               :title="infoDialogTitle"
               width="80%">
      <el-tabs>
        <el-tab-pane label="日常养护" name="0">
          <detail1 :infoParams="infoParams" :info-index="0"></detail1>
        </el-tab-pane>
        <el-tab-pane label="日常养护监理计量" name="1">
          <detail1 :infoParams="infoParams" :info-index="1"></detail1>
        </el-tab-pane>
        <el-tab-pane label="被损被盗" name="2">
          <detail1 :infoParams="infoParams" :info-index="2"></detail1>
        </el-tab-pane>
        <el-tab-pane label="被损被盗监理计量" name="3">
          <detail1 :infoParams="infoParams" :info-index="3"></detail1>
        </el-tab-pane>
        <el-tab-pane label="养护工程" name="4">
          <detail1 :infoParams="infoParams" :info-index="4"></detail1>
        </el-tab-pane>
        <el-tab-pane label="养护工程监理计量" name="5">
          <detail1 :infoParams="infoParams" :info-index="5"></detail1>
        </el-tab-pane>
        <el-tab-pane label="养护工程设计计量" name="6">
          <detail1 :infoParams="infoParams" :info-index="6"></detail1>
        </el-tab-pane>
        <el-tab-pane label="定期检测" name="7">
          <detail1 :infoParams="infoParams" :info-index="7"></detail1>
        </el-tab-pane>
        <el-tab-pane label="运营费用" name="8">
          <detail1 :infoParams="infoParams" :info-index="8"></detail1>
        </el-tab-pane>
        <el-tab-pane label="其他合同费用" name="9">
          <detail1 :infoParams="infoParams" :info-index="9"></detail1>
        </el-tab-pane>
      </el-tabs>
    </el-dialog>
  </div>
</template>

<script>
import {getGLCReport, getJTReport, getTree} from "@/api/calculate/report/comprehensive/meterNum";
import Detail1 from "./detail1.vue";

export default {
  name: 'MeterNum',
  props: [],
  components: {
    Detail1,
    RenderDom: {
      functional: true,
      props: {
        row: Object,
        index: Number,
        render: Function,
      },
      render(createElement, ctx) {
        const {row, index} = ctx.props;
        return ctx.props.render(row, index);
      },
    }
  },
  data() {
    return {
      showSearch: false,
      loading: false,
      columns: [
        {key: 0, width: 100, field: 'status', label: `提交状态`, visible: true},
        {
          key: 1, width: 100, field: 'field1', label: `操作`, visible: true, slots: true, render: (row, index) => {
            return (
              <el-button
                size="mini"
                type="text"
                onClick={(e) => this.handleOpenInfo(e, row)}
              >
                查看
              </el-button>
            );
          },
        },
        {key: 2, width: 100, field: 'numberName', label: `期数`, visible: true},
        {key: 3, width: 100, field: 'maiSecName', label: `养护路段`, visible: true},
        {key: 4, width: 100, field: 'dailyFund', label: `日常养护费用`, visible: true},
        {key: 5, width: 100, field: 'dailySupFund', label: `日常养护监理费用`, visible: true},
        {key: 6, width: 100, field: 'operationFund', label: `运营费用`, visible: true},
        {key: 7, width: 100, field: 'theftFund', label: `被损被盗费用`, visible: true},
        {key: 8, width: 100, field: 'theftSupFund', label: `被损被盗监理费用`, visible: true},
        {key: 9, width: 100, field: 'projFund', label: `养护工程费用`, visible: true},
        {key: 10, width: 100, field: 'projSupFund', label: `养护工程监理费用`, visible: true},
        {key: 11, width: 100, field: 'projDesignFund', label: `养护工程设计费用`, visible: true},
        {key: 12, width: 100, field: 'checkFund', label: `养护检测费用`, visible: true},
        {key: 13, width: 100, field: 'otherConFund', label: `其他合同费用`, visible: true},
        {key: 14, width: 100, field: 'totalFund', label: `费用合计`, visible: true},
      ],
      tableData: [],
      // 左侧组织树
      relaNav: true,
      keyword: '',
      relaOptions: [],
      filteredTreeData: [],
      relaFlag: false,
      formData: {
        maiDomainName: '',
        maiDomainId: ''
      },
      total: 0,
      queryParams: {
        pageNum: 1,
        pageSize: 50
      },
      pageType: '1',
      infoDialog: false,
      infoDialogTitle: '',
      infoParams: {

      }
    }
  },
  computed: {},
  watch: {},
  created() {
    this.pageType = this.$route.query.type
    this.getDeptTree()
  },
  mounted() {
    this.handleQuery()
  },
  methods: {
    // 查询
    handleQuery() {
      this.loading = true
      if (this.pageType == '1') {
        getJTReport(this.queryParams).then(res => {
          this.tableData = res.rows
          this.loading = false
          this.total = res.total
        })
      } else {
        getGLCReport(this.queryParams).then(res => {
          this.tableData = res.rows
          this.loading = false
          this.total = res.total
        })
      }
    },
    // 导出清单按钮
    exportList() {
      let url = 'manager/meter/num/jt/export'
      if (this.pageType == '2') {
        url = 'manager/meter/num/glc/export'
      }
      this.download(
        url,
        this.queryParams,
        `maiconunitrelation_${new Date().getTime()}.xlsx`,
        {
          headers: {'Content-Type': 'application/json;'},
          parameterType: 'body'
        }
      )
    },
    // 查询部门下拉树结构
    getDeptTree() {
      getTree().then(async response => {
        this.relaOptions = await this.transformData(response.data);
        this.filteredTreeData = [...this.relaOptions]
      });
    },
    // 格式转换
    transformData(input) {
      return input.map(item => {
        const transformedItem = {
          label: item.deptName,
          deptId: item.deptId.toString(),
          children: []
        };
        if (item.children && item.children.length > 0) {
          item.children.forEach(yearGroup => {
            if (yearGroup.children && yearGroup.children.length > 0) {
              const yearItem = {
                label: yearGroup.year.toString(),
                deptId: item.deptId.toString(),
                year: yearGroup.year,
                children: yearGroup.children.map(period => ({
                  label: period.name,
                  deptId: item.deptId.toString(),
                  year: period.year,
                  numberId: period.id
                }))
              };
              transformedItem.children.push(yearItem);
            }
          });
        }
        return transformedItem;
      });
    },
    // 关键词检索
    handleSearch() {
      const keyword = this.keyword.toLowerCase();
      this.filteredTreeData = this.relaOptions.filter(node => this.filterNode(node, keyword));
    },
    filterNode(node, keyword) {
      if (node.label.indexOf(keyword) != -1) {
        return true;
      }
      if (node.children) {
        return node.children.some(childNode => this.filterNode(childNode, keyword));
      }
      return false;
    },
    handleNodeClick(e) {
      delete e.children
      this.queryParams = {
        ...e,
        pageNum: 1,
        pageSize: 50
      }
      this.handleQuery()
    },
    handleOpenInfo(e, row) {
      this.infoParams = {
        status: row.status == '已提交' ? 1 : 0,
        numberId: row.numberId,
        maiSecId: row.maiSecId
      }
      this.infoDialogTitle = row.numberName + '计量统计'
      this.infoDialog = true
    }
  }
}
</script>

<style lang="scss" scoped>
.leftDiv {
  border-right: 1px solid #d8dce5;
  min-height: calc(100vh - 100px);
  overflow-y: auto;
  height: calc(80vh - 150px);
  position: relative;
  top: -20px;
  padding-top: 10px;
  background-color: white;
}

.leftIcon {
  border: 1px solid #dcdfe6;
  border-radius: 8px;
  width: 16px;
  height: 50px;
  line-height: 50px;
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
  position: absolute;
  right: 0;
  top: 300px;
  z-index: 2;
}

.leftIcon:hover {
  background-color: #dcdfe6;
}

.rightIcon {
  border: 1px solid #dcdfe6;
  border-radius: 8px;
  width: 16px;
  height: 50px;
  line-height: 50px;
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
  position: absolute;
  left: -10px;
  top: 280px;
  z-index: 10;
  background: white;
}

.rightIcon:hover {
  background-color: #dcdfe6;
}

.left-total {
  font-size: 13px;
  line-height: 28px;
  color: #606266;
  position: absolute;
  bottom: 10px;
  right: 10px;
}
</style>
<style lang="scss" scoped>
@import "@/assets/styles/business.scss";
</style>
