<template>
  <div class="img-view">
    <img :src="imgUrl" alt="" v-for="(imgUrl, index) in imgUrlList" :key="index"
      :style="{ width: width + 'px', height: height + 'px' }" />
  </div>
</template>

<script>
import { findFiles } from '@/api/file/index.js'

export default {
  props: {
    id: {
      type: [Number, String],
      default: ''
    },
    width: {
      type: [Number, String],
      default: 18
    },
    height: {
      type: [Number, String],
      default: 18
    }
  },
  data() {
    return {
      imgUrlList: [],
    }
  },
  watch: {
    id: {
      handler(val) {
        if (val) {
          this.getImgById(val)
        }
      },
      deep: true,
      immediate: true,
    }
  },
  methods: {
    getImgById(id) {
      findFiles({ ownerId: id }).then(res => {
        if (res.code === 200 && res.data) {
          this.imgUrlList = res.data.map(file => file.url);
        }
      })
    },
  }
}
</script>

<style lang="scss" scoped>
.img-view {
  margin-right: 6px;
}
</style>