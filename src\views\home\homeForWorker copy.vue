

<template>
  <el-row class="mainContent route-load">
<!-- 左-->
    <el-col :span="16">
        <el-row>
<!--          快捷方式-->
          <el-col :span="16">
            <el-card class="box-card">
              <el-tabs v-model="activeName" @tab-click="handleClick">
                <el-tab-pane label="快捷操作" name="first">

                 <el-row>
                   <el-col :span="4" v-for="i in 8">
                     <div class="iconDivs" >
                       <div class="iconDiv">
                         <img src="../../assets/alyicon/印章认证.png" height="30" width="30"/>
                       </div>
                       <div class="iconText">流程审批</div>
                     </div>
                   </el-col>
                 </el-row>



                </el-tab-pane>
                <el-tab-pane label="全部应用" name="second">这里是全部功能清单</el-tab-pane>
              </el-tabs>
            </el-card>
            <el-row>
              <el-col :span="8">
                <el-card class="box-card" style="height: 110px;background: linear-gradient(to right, #2d8bfe, #5b9bfa);color: white;">
                 <div class="cardContent">
                   <div class="leftDiv">
                     <div class="iconDiv1">
                       <img src="../../assets/alyicon/Bridge.png" height="30" width="30"/>
                     </div>
                     <div class="nums2">桥梁</div>
                   </div>
                   <div class="rightDiv">
                     <div class="textTitle">桥梁数量：129 座</div>
                     <div class="textTitle">养护覆盖率：98%</div>
                     <div class="textTitle">待处理病害：338 个</div>
                   </div>
                 </div>


                </el-card>
              </el-col>
              <el-col :span="8">
                <el-card class="box-card " style="height: 110px;background:linear-gradient(to right, #55c489, #6fd5a1);color: white">
                  <div class="cardContent">
                    <div class="leftDiv">
                      <div class="iconDiv1">
                        <img src="../../assets/alyicon/tunnel.png" height="30" width="30"/>
                      </div>
                      <div class="nums2">隧道</div>
                    </div>
                    <div class="rightDiv">
                      <div class="textTitle">隧道数量：129 座</div>
                      <div class="textTitle">养护覆盖率：98%</div>
                      <div class="textTitle">待处理病害：338 个</div>
                    </div>
                  </div>
                </el-card>
              </el-col>

              <el-col :span="8">
                <el-card class="box-card " style="height: 110px;background: linear-gradient(to right, #a071f5, #93a0f5);color: white;">
                  <div class="cardContent">
                    <div class="leftDiv">
                      <div class="iconDiv1">
                        <img src="../../assets/alyicon/culvert.png" height="30" width="30"/>

                      </div>
                      <div class="nums2">涵洞</div>
                    </div>
                    <div class="rightDiv">
                      <div class="textTitle">涵洞数量：129 座</div>
                      <div class="textTitle">养护覆盖率：98%</div>
                      <div class="textTitle">待处理病害：338 个</div>
                    </div>
                  </div>
                </el-card>
              </el-col>
            </el-row>


          </el-col>
<!--          待办事项-->
          <el-col :span="8">
            <el-card class="box-card" style="height: 400px;padding-left: 0;overflow-y: auto">
<!--              日常养护-->
              <div>
                <div class="listIcon2"><i class="el-icon-s-order"></i></div>
                <div class="listContent1">
                  <div class="numTitle1">日常养护</div>
                </div>
                <br>
                <el-carousel height="70px"  arrow="always" :autoplay="false">
                  <el-carousel-item v-for="item in 4" :key="item">
                    <el-row style="margin-top: 15px;text-align: center;">
                      <el-col :span="8">
                        <div class="numTitle">待拟定</div>
                        <div class="nums1">202</div>
                      </el-col>
                      <el-col :span="8">
                        <div class="numTitle">待提交</div>
                        <div class="nums1">108</div>
                      </el-col>
                      <el-col :span="8">
                        <div class="numTitle">待审核</div>
                        <div class="nums1">21</div>
                      </el-col>
                    </el-row>
                  </el-carousel-item>
                </el-carousel>
              </div>
<!--              养护工程-->
              <div>
                <div class="listIcon2"><i class="el-icon-s-order"></i></div>
                <div class="listContent1">
                  <div class="numTitle1">被损被盗</div>
                </div>
                <br>
                <el-carousel height="70px"  arrow="always">
                  <el-carousel-item v-for="item in 4" :key="item">
                    <el-row style="margin-top: 15px;text-align: center;">
                      <el-col :span="8">
                        <div class="numTitle">待下发</div>
                        <div class="nums1">202</div>
                      </el-col>
                      <el-col :span="8">
                        <div class="numTitle">待施工</div>
                        <div class="nums1">108</div>
                      </el-col>
                      <el-col :span="8">
                        <div class="numTitle">待开工</div>
                        <div class="nums1">21</div>
                      </el-col>
                    </el-row>
                  </el-carousel-item>
                </el-carousel>
              </div>
       <!--养护工程-->
              <div>
                <div class="listIcon2"><i class="el-icon-s-order"></i></div>
                <div class="listContent1">
                  <div class="numTitle1">养护工程</div>
                </div>
                <br>
                <el-carousel height="70px"  arrow="always">
                  <el-carousel-item v-for="item in 4" :key="item">
                    <el-row style="margin-top: 15px;text-align: center;">
                      <el-col :span="8">
                        <div class="numTitle">待提交</div>
                        <div class="nums1">202</div>
                      </el-col>
                      <el-col :span="8">
                        <div class="numTitle">待施工</div>
                        <div class="nums1">108</div>
                      </el-col>
                      <el-col :span="8">
                        <div class="numTitle">待完工</div>
                        <div class="nums1">21</div>
                      </el-col>
                    </el-row>
                  </el-carousel-item>
                </el-carousel>

              </div>
<!--定期检查-->
              <div>
                <div class="listIcon2"><i class="el-icon-s-order"></i></div>
                <div class="listContent1">
                  <div class="numTitle1">定期检查</div>
                </div>
                <br>
                <el-carousel height="70px"  arrow="always">
                  <el-carousel-item v-for="item in 4" :key="item">
                    <el-row style="margin-top: 15px;text-align: center;">
                      <el-col :span="8">
                        <div class="numTitle">待验收</div>
                        <div class="nums1">202</div>
                      </el-col>
                      <el-col :span="8">
                        <div class="numTitle">待审核</div>
                        <div class="nums1">108</div>
                      </el-col>
                      <el-col :span="8">
                        <div class="numTitle">待计量</div>
                        <div class="nums1">21</div>
                      </el-col>
                    </el-row>
                  </el-carousel-item>
                </el-carousel>
              </div>

            </el-card>
          </el-col>
        </el-row>

      <el-row>
        <el-col :span="24">
          <el-card class="box-card">
            <div slot="header" class="clearfix">
              <span class="cardTitle">辖区事件</span>
              <el-button style="float: right; padding: 3px 0" type="text">查看更多</el-button>
            </div>
<!--            <div v-for="o in 4" :key="o" class="text item">-->
<!--              {{'列表内容 ' + o }}，分两页-->
<!--            </div>-->
            <el-row>
              <el-col :span="12">
                <div v-for="i in 3">
                  <div class="listIcon"><i class="el-icon-s-order"></i></div>
                  <div class="listContent">
                    <el-descriptions column="2" title="鹤关高速大理至丽江方向K12+000至K11+820需对增设护栏板">
                      <el-descriptions-item label="管养单位">大理管理处</el-descriptions-item>
                      <el-descriptions-item label="状态">
                        <el-tag size="small" type="warning">施工单位施工中</el-tag>
                      </el-descriptions-item>
                      <el-descriptions-item label="提交人">大飞</el-descriptions-item>
                      <el-descriptions-item label="提交时间">2024.05.24 16:30</el-descriptions-item>
                    </el-descriptions>
                  </div>
                </div>
              </el-col>
              <el-col :span="12">
                <div v-for="i in 3" style="padding-left: 10px">
                  <div class="listIcon"><i class="el-icon-s-order"></i></div>
                  <div class="listContent">
                    <el-descriptions column="2" title="妙峰出口匝道楚雄至大姚方向反光立柱损坏2根">
                      <el-descriptions-item label="管养单位">大理管理处</el-descriptions-item>
                      <el-descriptions-item label="状态">
                        <el-tag size="small" type="warning">施工单位施工中</el-tag>
                      </el-descriptions-item>
                      <el-descriptions-item label="提交人">李荣峰</el-descriptions-item>
                      <el-descriptions-item label="提交时间">2024.05.24 16:30</el-descriptions-item>
                    </el-descriptions>
                  </div>
                </div>
              </el-col>
            </el-row>


          </el-card>
        </el-col>
        <el-col :span="14">
          <el-card class="box-card">
            <div slot="header" class="clearfix">
              <span class="cardTitle">辖区结构物事件趋势</span>
              <el-button style="float: right; padding: 3px 0" type="text">查看更多</el-button>
            </div>
            <div class="bottomContent" style="text-align: center">
             <!-- 演示使用-->
              <img src="../../assets/other/chart1.png"  height="100%"/>
            </div>
          </el-card>
        </el-col>
        <el-col :span="10">
          <el-card class="box-card">
            <div slot="header" class="clearfix">
              <span class="cardTitle">辖区结构物事件占比</span>
              <el-button style="float: right; padding: 3px 0" type="text">查看更多</el-button>
            </div>
            <div class="bottomContent" style="text-align: center">
              <!-- 演示使用-->
              <img src="../../assets/other/chart3.png" height="100%"/></div>
          </el-card>
        </el-col>
      </el-row>

      <el-row>
        <el-col :span="12">

        </el-col>
        <el-col :span="12">

        </el-col>
      </el-row>


    </el-col>
<!--    右-->
    <el-col :span="8">
      <el-card class="box-card leftTop1" >
        <div  class="textItem">
           您好，Admin
           <div class="textItemSpan">欢迎使用高速公路智慧养护管理平台<br>时间：{{ times }}</div>
        </div>
      </el-card>

    <!--      通知公告模块-->
      <el-card class="box-card">
        <div slot="header" class="clearfix">
          <span class="cardTitle">通知公告</span>
          <el-button style="float: right; padding: 3px 0" type="text">查看更多</el-button>
        </div>
        <el-descriptions column="2" title="交通运输部办公厅关于印发《交通运输行政执法质量效能提升三年行动方案（2024—2026年）》的通知">

          <el-descriptions-item label="状态"> <el-tag size="small" type="warning">未读</el-tag></el-descriptions-item><br/>
          <el-descriptions-item label="等级">
            <el-tag size="small">一般性通知</el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="发布人">大理管理处</el-descriptions-item>
          <el-descriptions-item label="发布时间">2024.05.24 16:30</el-descriptions-item>
        </el-descriptions>

        <el-descriptions column="2" title="关于支持引导公路水路交通运输基础设施数字化转型升级的通知解读">

          <el-descriptions-item label="状态"> <el-tag size="small" type="warning">未读</el-tag></el-descriptions-item>
          <el-descriptions-item label="等级">
            <el-tag size="small">一般性通知</el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="发布人">大理管理处</el-descriptions-item>
          <el-descriptions-item label="发布时间">2024.05.24 16:30</el-descriptions-item>
        </el-descriptions>
      </el-card>

     <!-- 辖区事件情况-->
      <el-card class="box-card">
        <div slot="header" class="clearfix">
          <span class="cardTitle">辖区事件情况</span>
          <el-button style="float: right; padding: 3px 0" type="text">查看更多</el-button>
        </div>
        <el-row style="margin-top: 10px">
          <el-col :span="12">
            <div>
              <div class="listIcon1"><i class="el-icon-news"></i></div>
              <div class="listContent1">
                <div class="numTitle">辖区新增事件 <span class="el-icon-question" title="未审核事件"></span></div>
                <div class="nums">208</div>
              </div>
            </div>

          </el-col>
          <el-col :span="12" style="padding-left: 20px">
            <div>
              <div class="listIcon1"><i class="el-icon-notebook-1"></i></div>
              <div class="listContent1">
                <div class="numTitle">辖区进行中事件 <span class="el-icon-question" title="已审核正在处理的事件"></span></div>
                <div class="nums">103</div>
              </div>
            </div>
          </el-col>
        </el-row>


        <el-row style="margin-top:30px">
          <el-col :span="12">
            <div>
              <div class="listIcon2"><i class="el-icon-s-order"></i></div>
              <div class="listContent1">
                <div class="numTitle1">事件原因</div>
              </div>
              <el-row style="margin-top: 15px;text-align: center;">
                <el-col :span="8">
                  <div class="numTitle">日常养护</div>
                  <div class="nums1">202</div>
                </el-col>
                <el-col :span="8">
                  <div class="numTitle">自然灾害</div>
                  <div class="nums1">108</div>
                </el-col>
                <el-col :span="8">
                  <div class="numTitle">被损被盗</div>
                  <div class="nums1">21</div>
                </el-col>
              </el-row>
            </div>

          </el-col>
          <el-col :span="12" style="padding-left: 20px">
            <div>
              <div class="listIcon3"><i class="el-icon-s-cooperation"></i></div>
              <div class="listContent1">
                <div class="numTitle1">处理类型</div>
              </div>
              <el-row style="margin-top: 20px;text-align: center;">
                <el-col :span="8">
                  <div class="numTitle">普通事件</div>
                  <div class="nums1">8</div>
                </el-col>
                <el-col :span="8">
                  <div class="numTitle">及时事件</div>
                  <div class="nums1">20</div>
                </el-col>
                <el-col :span="8">
                  <div class="numTitle">缺陷责任期</div>
                  <div class="nums1">20</div>
                </el-col>
              </el-row>
            </div>
          </el-col>
        </el-row>

      </el-card>

    <!--      巡检查-->
      <el-card class="box-card">
        <div slot="header" class="clearfix">
          <span class="cardTitle">巡检查情况</span>
          <el-button style="float: right; padding: 3px 0" type="text">查看更多</el-button>
        </div>
        <el-row style="margin-top: 10px">
          <el-col :span="12">
            <div>
              <div class="listIcon1"><i class="el-icon-map-location"></i></div>
              <div class="listContent1">
                <div class="numTitle">辖区已巡查里程） <span class="el-icon-question" title="本月巡查里程，月初清零"></span></div>
                <div class="nums">258<span class="numTitle">（千米/km）</span></div>
              </div>
            </div>

          </el-col>
          <el-col :span="12" style="padding-left: 20px">
            <div>
              <div class="listIcon1"><i class="el-icon-place"></i></div>
              <div class="listContent1">
                <div class="numTitle">计划巡查里程 <span class="el-icon-question" title="辖区内每月定量必须要完成的巡查里程数"></span></div>
                <div class="nums">1023<span class="numTitle">（千米/km）</span></div>
              </div>
            </div>
          </el-col>
        </el-row>


        <el-row style="margin-top:30px">
          <el-col :span="8">
            <div>
              <div class="listIcon2"><i class="el-icon-s-order"></i></div>
              <div class="listContent1">
                <div class="numTitle1">桥梁日常巡查</div>
              </div>
              <el-row style="margin-top: 15px;text-align: center;">
                <el-col :span="12">
                  <div class="numTitle">已巡查</div>
                  <div class="nums1">202</div>
                </el-col>
                <el-col :span="12">
                  <div class="numTitle">待巡查</div>
                  <div class="nums1">108</div>
                </el-col>
              </el-row>
            </div>

          </el-col>
          <el-col :span="8" style="padding-left: 20px">
            <div>
              <div class="listIcon3"><i class="el-icon-s-cooperation"></i></div>
              <div class="listContent1">
                <div class="numTitle1">隧道日常巡查</div>
              </div>
              <el-row style="margin-top: 20px;text-align: center;">
                <el-col :span="12">
                  <div class="numTitle">已巡查</div>
                  <div class="nums1">8</div>
                </el-col>
                <el-col :span="12">
                  <div class="numTitle">待巡查</div>
                  <div class="nums1">20</div>
                </el-col>
              </el-row>
            </div>
          </el-col>
          <el-col :span="8" style="padding-left: 20px">
            <div>
              <div class="listIcon3" style="background-color:#607d8b"><i class="el-icon-s-cooperation"></i></div>
              <div class="listContent1">
                <div class="numTitle1">涵洞日常巡查</div>
              </div>
              <el-row style="margin-top: 20px;text-align: center;">
                <el-col :span="12">
                  <div class="numTitle">已巡查</div>
                  <div class="nums1">8</div>
                </el-col>
                <el-col :span="12">
                  <div class="numTitle">待巡查</div>
                  <div class="nums1">20</div>
                </el-col>
              </el-row>
            </div>
          </el-col>
        </el-row>
      </el-card>

    </el-col>
  </el-row>
</template>

<script>
export default {
  data() {
    return {
      activeName: 'first',
      todoName:'rcyh',
      like: true,
      value1: 4154.564,
      value2: 1314,
      title: "增长人数",
      times:"",
    };
  },
  methods: {
    handleClick(tab, event) {
      console.log(tab, event);
    }
  },
  created() {
    const now = new Date();
    // this.year = now.getFullYear(); // 获取年份
    // // this.month = now.getMonth() + 1; // 获取月份（注意，月份是从0开始的，所以需要+1）
    // // this.day = now.getDate(); // 获取日期
    // // 如果你想要月份和日期都是两位数（比如09而不是9），你可以使用以下方式：
    //  this.month = String(now.getMonth() + 1).padStart(2, '0');
    //  this.day = String(now.getDate()).padStart(2, '0');
      this.times =now.getFullYear()+"年" + String(now.getMonth() + 1).padStart(2, '0') +"月" +String(now.getDate()).padStart(2, '0')+"日";
  },
};
</script>
<style lang="scss" scoped>
.route-load{
  ::v-deep .el-tabs__content{
    height: 190px;
    overflow: auto
  }

  ::v-deep .el-tabs--left .el-tabs__item.is-left{
    text-align: left;
  }
  ::v-deep .el-carousel__button{
    width: 10px;
    background-color: #2196F3;
  }
  ::v-deep .el-carousel__arrow{
    width: 20px;
    height: 20px;
  }
  ::v-deep .el-carousel__container{
    top: -18px;
  }
  ::v-deep .el-carousel__arrow--left{
    left: 0;
  }
  ::v-deep .el-carousel__arrow--right{
    right: 0;
  }
  ::v-deep .el-descriptions__title{
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis
  }
  ::v-deep .el-tabs__nav-wrap::after{
    background-color: #ffffff;
  }
}

.mainContent{
    padding: 10px;
    height: 100%;
    overflow-y: auto;
  }
  .el-card{
    margin: 5px;
  }
  .leftTop1{
    /* 原始背景图片 */
    //background-image: url('../../assets/other/1.png');
    background-image: url('../../assets/other/2.jpg');
    /* 确保背景图片覆盖整个div */
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    /* 背景不重复 */

    /* 其他样式 */
    position: relative;
    width: 100%;
    height: 150px;

  }
  .leftTop1::before {
    content: "";
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    background-image: -webkit-gradient(linear, left top, right top, from(rgba(255,0,0,0)), to(rgba(255,0,0,1)));
    background-image: linear-gradient(to right, rgb(4 144 68), rgb(5 145 68 / 20%));
    background-position: top left;
    background-size: 100% 100%;
    opacity: 0.9;
    border-radius: 10px;
  }
  .el-card{
    border-radius: 10px;
  }
  .textItem{
    z-index: 2;
    color: white;
    position: absolute;
    font-size: 20px;
    font-weight: 600;
    line-height: 2;
    width: 100%;
  }
  .textItemSpan{
    font-size: 18px;
    font-weight: 400;
    position: absolute;
  }
  .cardTitle{
    font-size: 20px;
    font-weight: 600;
  }


  .iconDiv{
    width: 50px;
    height: 50px;
    background-color: #e6ebf5;
    border-radius: 50%;
    line-height: 50px;
    text-align: center;
    padding-top: 8px;
  }
  .iconDivs{
    width: 80px;
    display: grid;
    place-items: center;
    margin-bottom: 10px;
    margin-top: 5px;
  }

  .iconText{
    margin-top: 5px;
    color: #607D8B;
  }
  .listIcon{
    color: #2196F3;
    background-color: #d8ebf3;
    text-align: center;
    width: 35px;
    height: 35px;
    line-height: 35px;
    border-radius: 50%;
    font-size: 24px;
    float: left;
    margin-top: 15px;
    margin-right: 10px;
  }
  .listContent{
    display: flow-root;
  }

.listIcon1{
  color: #ffffff;
  background-color: #1890ff;
  text-align: center;
  width: 35px;
  height: 35px;
  line-height: 35px;
  border-radius: 50%;
  font-size: 24px;
  float: left;
  margin-top: 5px;
  margin-right: 10px;
}
.listContent1{
  display: flow-root;
  margin-left: 10px;
}
.listIcon2 {
  color: #ffffff;
  background-color: #FF9800;
  text-align: center;
  width: 25px;
  height: 25px;
  line-height: 25px;
  border-radius: 50%;
  font-size: 14px;
  float: left;
  margin-top: 5px;
  margin-right: 5px;
  margin-left: 10px;
}
.listIcon3 {
  color: #ffffff;
  background-color: #c73adf;
  text-align: center;
  width: 25px;
  height: 25px;
  line-height: 25px;
  border-radius: 50%;
  font-size: 14px;
  float: left;
  margin-top: 5px;
  margin-right: 5px;
  margin-left: 10px;
}

.numTitle{
  font-size: 14px;
  color: #82848a;
  margin-bottom: 5px;
}
.numTitle1{
  font-size: 16px;
  color: #54565a;
  font-weight: 600;
  margin-top: 5px;
}
.nums{
  font-size: 26px;
  font-weight: bolder
}
.nums1{
  font-size: 22px;
  font-weight: bolder
}

.nums2{
  font-size: 16px;
  font-weight: bolder;
  padding-left:8px;
}

.textTitle{
  color: white;
  line-height: 2;
  font-size:14px;
}

.iconDiv1{
  width: 50px;
  height: 50px;
  background-color: #46a6ff36;
  text-align: center;
  line-height: 50px;
  border-radius: 50%;
  border: 1px solid #fff;
  padding-top: 8px;
  margin-bottom: 5px;
  margin-top: 5px;
}
.cardContent{
  display: flex; /* 使用 Flexbox 布局 */
  align-items: flex-start; /* 垂直对齐方式，根据需要调整 */
  margin-bottom: 10px;
}
.leftDiv {
  width: 60px; /* 假设我们设置 leftDiv 的宽度为 200px */
  flex-shrink: 0; /* 防止 leftDiv 在容器变小时缩小 */
  /* 其他样式... */
}

.rightDiv {
  flex-grow: 1; /* 使得 rightDiv 占据剩余的空间 */
  margin-left: 10px;
  /* 其他样式... */
}
.bottomContent{
  height: 200px;
}


</style>
