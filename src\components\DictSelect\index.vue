<template>
  <div>
    <el-select filterable :clearable="clearable" :placeholder="placeholder" ref="elSelect" v-bind="$attrs" v-on="$listeners" style="width: 100%" :disabled="disabled" @change="handleChange">
      <el-option v-if="isTree" value="" :class="{ is_tree: isTree }">
        <el-tree v-bind="$attrs" v-on="$listeners" id="tree-option" ref="selectTree" :data="dataOptions" :props="props"
          node-key="dictValue" accordion check-strictly :show-checkbox="multiple" :expand-on-click-node="onlyTailNode"
          :check-on-click-node="true" :default-expanded-keys="defaultExpandedKey" @node-click="handleNodeClick"
          @check="handlerCheck">
        </el-tree>
      </el-option>
      <el-option :style="{ display: isTree ? 'none' : 'block' }" v-for="dict in dict.type[$options.dicts[0]]" :key="dict.value"
        :label="dict.label" :value="dict.value">
      </el-option>
    </el-select>
  </div>
</template>
<script>
import { optionselect } from "@/api/system/dict/type";
import { listAllData } from "@/api/system/dict/data";

/**
 * DictSelect属性、方法、事件请参照 el-select与el-tree。
 * 关键属性如下：
 * props:{
 *   数据字典类型
 *   type:{
 *     type:String
 *   },
 *   字典选中值
 *   value:{
 *     type:[String, Array]
 *   },
 *   单选or多选（默认单选）
 *   multiple:{
 *     type: Boolean,
 *     default: false
 *   },
 *   是否只允许选叶子节点
 *   onlyTailNode:{
 *     type: Boolean,
 *     default: false
 *   }
 *   显示层级（0:不做限制）
 *   level:{
 *     type: Number,
 *     default: -1
 *   }
 * }
 */
export default {
  name: "DictSelect",
  dicts: [],
  props: {
    onlyTailNode: {
      type: Boolean,
      default: false
    },
    level: {
      type: Number,
      default: 0
    },
    placeholder: {
      type: String,
      default: '请选择'
    },
    disabled: {
      type: Boolean,
      default: false
    },
    clearable: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      isTree: false,
      multiple: false,
      dataOptions: [],
      defaultExpandedKey: [],
      props: {
        value: 'dictValue',
        label: 'dictLabel',
        children: 'children',
        disabled: (...arg) => {
          if (arg[1].childNodes && arg[1].childNodes.length > 0) {
            return this.onlyTailNode
          }
        }
      }
    }
  },
  beforeCreate() {
    this.$options.dicts = [this.$attrs.type]
    if (typeof this.$attrs.value == 'number') {
      this.$attrs.value = String(this.$attrs.value)
      this.$emit('input', String(this.$attrs.value))
    }
  },
  watch: {
    isTree(value) {
      if (value) {
        this.getTreeselect()
      }
    }
  },
  methods: {
    handleChange(e) {
      this.$emit('change', e)
    },
    // 根据value获取整个数据字典对象,单选可不用传参
    getDictDataByValue(value = this.$attrs.value) {
      if (this.dict.type[this.$options.dicts[0]]) {
        let tempArray
        tempArray = this.dict.type[this.$options.dicts[0]].filter(item => item.value === value)
        if (tempArray.length === 1) {
          return tempArray[0]
        }
      }
    },
    // 根据value获取父节点,单选可不用传参
    getParentByValue(value = this.$attrs.value, data = this.dataOptions) {
      for (let temp of data) {
        if (temp.children && temp.children.length > 0) {
          for (let tempp of temp.children) {
            if (tempp.dictValue === value) {
              return temp
            }
            else
              this.getParentByValue(value, temp.children)
          }
        }
      }

    },
    // 初始化值
    initHandle() {
      if (this.$attrs.value || this.$attrs.value == 0) {
        if (typeof this.$attrs.value == 'number') {
          this.$attrs.value = String(this.$attrs.value)
          this.$emit('input', String(this.$attrs.value))
        }
        if (this.multiple) {
          this.$refs.selectTree?.setCheckedKeys(this.$attrs.value)
        } else {
          this.$refs.selectTree?.setCurrentKey(this.$attrs.value)
        }
      } else {
        this.$refs.selectTree?.setCurrentKey(null)
      }
      let scrollWrap = document.querySelectorAll('.el-scrollbar .el-select-dropdown__wrap')[0]
      let scrollBar = document.querySelectorAll('.el-scrollbar .el-scrollbar__bar')
      scrollWrap.style.cssText = 'margin: 0px; max-height: none; overflow: hidden;'
      scrollBar.forEach(ele => ele.style.width = 0)
    },
    // 切换选项
    handleNodeClick(node) {
      let value
      if (this.multiple) {
        value = this.$refs.selectTree.getCheckedKeys()
      } else {
        value = node[this.props.value]
        if (this.onlyTailNode) {
          if (node.children && node.children.length > 0) {
            value = null
          } else
            this.$refs.elSelect.blur()
        } else
          this.$refs.elSelect.blur()
      }

      this.$emit('input', value)
      this.defaultExpandedKey = []
    },
    handlerCheck(arg1, { checkedKeys }) {
      if (this.multiple)
        this.$emit('input', checkedKeys)
    },
    // 清除选中
    clearHandle() {
      this.defaultExpandedKey = []
      this.clearSelected()
      this.$emit('input', null)
    },
    /* 清空选中样式 */
    clearSelected() {
      let allNode = document.querySelectorAll('#tree-option .el-tree-node')
      allNode.forEach((element) => element.classList.remove('is-current'))
    },

    getTreeselect() {
      listAllData({ dictType: this.$attrs.type }).then(response => {
        this.dataOptions = this.handleTree(response.data, "dictCode", "dictParentCode");
        if (this.level !== 0) {
          loop(this.dataOptions, this.level)
        }
        function loop(array = [], level) {
          if (array.length > 0) {
            for (let temp of array) {
              if (temp.children && temp.children.length > 0) {
                if (level === 1) {
                  delete temp.children
                }
                else
                  loop(temp.children, level - 1)
              }
            }
          }
        }
      });
    },
  },
  created() {
    if (this.$attrs.type) {
      optionselect().then(res => {
        let dictTypes = res.data.filter(item => item.dictType === this.$attrs.type)
        if (dictTypes.length === 1)
          this.isTree = dictTypes[0].dictClass === '2'
      })
    }
    if ('multiple' in this.$attrs)
      this.multiple = true

  },
  updated() {
    this.$nextTick(() => {
      this.initHandle()
    })
  }
};
</script>
<style scoped>
.el-select-dropdown__item {
  height: auto;
  max-height: 274px;
  /*padding: 0; overflow: hidden;*/
  overflow-y: auto;
}

.el-select-dropdown__item.selected {
  font-weight: normal;
}

ul li>>>.el-tree .el-tree-node__content {
  height: auto;
  padding: 0 20px;
}

.el-tree-node__label {
  font-weight: normal;
}

.el-tree>>>.is-current .el-tree-node__label {
  color: #409EFF;
  font-weight: 700;
}

.el-tree>>>.is-current .el-tree-node__children .el-tree-node__label {
  color: #606266;
  font-weight: normal;
}

.is_tree,
.is_tree:hover {
  background-color: unset !important;
}
/* 输入框超出隐藏，不换行*/
::v-deep .el-select__tags {
  flex-wrap: nowrap;
  overflow: auto;
}
</style>


