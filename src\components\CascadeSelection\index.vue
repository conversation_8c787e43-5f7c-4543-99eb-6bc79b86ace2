<template>
  <div class="cascade-selection">
    <el-row :gutter="gutter">
      <el-col :span="showCodes?8:12" :offset="0">
        <!-- <treeselect
          v-model="form.managementMaintenanceId"
          :options="deptOptions"
          :show-count="true"
          :size="size"
          placeholder="管养单位"
          @input="deptChange"
          clearable
          filterable
          :class="'tree-select-' + size"
          :appendToBody="true"
        /> -->
        <!-- <selectTreeCheckbox :deptType="201" v-model="form.managementMaintenanceId" placeholder="管养处"/> -->
        <el-select
          v-model="form.managementMaintenanceBranchId"
          clearable
          ref="stlectTree"
          class="custom-select"
          placeholder="管养分处"
          @clear="selectClear"
          @remove-tag="removeTag"
        >
          <el-option
            hidden
            v-for="(item,index) in formatData(deptOptions)"
            :key="item.label + item.value + '-' + index" :label="item.label" :value="item.value"
          >
          </el-option>
          <el-tree
            style="font-weight: 400"
            :data="deptOptions"
            :props="defaultProps"
            @check="handleNodeCheck"
            @node-click="nodeClick"
            node-key="value"
            check-on-click-node
            ref="treeRef"

          ></el-tree>
        </el-select>
      </el-col>
      <el-col :span="showCodes?8:12" :offset="0">
        <el-select
          v-model="form.maintenanceSectionId"
          placeholder="路段名称"
          clearable
          filterable
          :size="size"
          @change="maintenanceSectionChange"
          style="width: 100%"
        >
          <el-option
            v-for="item in routeOptions"
            :key="item.maintenanceSectionId"
            :label="item.maintenanceSectionName"
            :value="item.maintenanceSectionId"
          >
          </el-option>
        </el-select>
      </el-col>
      <el-col :span="showCodes?8:0" :offset="0">
        <el-select
          v-model="form.routeCodes"
          placeholder="路线编码"
          clearable
          filterable
          :multiple="multiple"
          :size="size"
          @change="routeCodesChange"
          style="width: 100%"
          collapse-tags
        >
          <el-option
            v-for="item in routeList"
            :key="item.routeId"
            :label="item.routeName"
            :value="item.routeCode"
          >
          </el-option>
        </el-select>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import Treeselect from '@riophae/vue-treeselect';
import '@riophae/vue-treeselect/dist/vue-treeselect.css';
import { deptTreeSelect,getTreeStruct } from '@/api/tmpl';
import {
  listByMaintenanceSectionId,
  getMaintenanceSectionListAll,
} from '@/api/baseData/common/routeLine';
import { listAllRoute } from '@/api/system/route.js';
import selectTreeCheckbox from '@/components/DeptTmpl/selectTreeCheckbox.vue';
import deptTreeCheckbox from '@/components/DeptTmpl/deptTreeCheckbox.vue';

/**
 * 管理处下拉组件，包含分处
 */
export default {
  components: {
    Treeselect,
    selectTreeCheckbox,
    deptTreeCheckbox,
  },
  props: {
    /**
     * 管养处-数据类型
     * 一、最项层级 只是当前级别：200、包括下级：100
     * 二、管理处 只是当前级别：201、包括下级：101
     * 三、分处 只是当前级别：202、包括下级：102
     * 四、隧管站 只是当前级别：203 、包括下级：103
     * 五、施工单位 、只是当前级别：204 、包括下级：104
     * 六、只要监理单位（包括下级） 只是当前级别：205 、包括下级：105
     * 七、设计单位 、只是当前级别：206 、包括下级：106
     */
    types: {
      type: [String, Number],
      default: 101,
    },
    // 绑定数据
    formData: {
      type: Object,
      default: () => {
        managementMaintenanceBranchId: [];
      },
    },
    //可选值 medium/small/mini，默认 ''或者 medium
    size: {
      type: String,
      default: '',
    },
    //是否多选
    multiple: {
      type: Boolean,
      default: false,
    },
    gutter: {
      default: 20,
    },
    showCodes:{
      default: true,
    }
  },
  data() {
    return {
      deptOptions: [], // 管养处数据
      routeOptions: [], //
      routeList: [], //
      obj: {}, // 返回数据
      form: {
        managementMaintenanceBranchId: '', //
        managementMaintenanceBranchIds: [], //
      }, // 为了操作数据
      defaultProps: {
        children: 'children',
        label: 'label',
      },
      treeValue: {},
    };
  },
  created() {
    this.getDeptList();
    // this.deptChange(null);
    // this.maintenanceSectionChange(null);
  },
  mounted() {
    // 处理初始值
    if (this.form.managementMaintenanceBranchId) {
      console.log('初始化')

      this.deptChange(this.form.managementMaintenanceBranchId);
    }
  },
  watch: {
    formData: {
      handler(val) {
        if (val && Object.keys(val).length) {
          this.form = val;
        }
      },
      deep: true,
      immediate: true,
    },
    'form.managementMaintenanceBranchId': {
      handler(val) {

         this.$nextTick(() => {
             if (!val) {
                this.selectClear();
                this.deptChange([]);
               }else if (val && !val.length) {
                 this.deptChange(val);
               }
        });
      },
      deep: true,
    },
  },
  methods: {
    getDeptList() {
      getTreeStruct({ types:'101',deptTypeList:[3,4],dataRule:true }).then((response) => {
        this.deptOptions = response.data;
      });
    },
    // 四级菜单
    formatData(data){
      let options = [];
      data.forEach((item,key) => {
        options.push({label:item.label,value:item.id});
        if(item.children){
          item.children.forEach((items,keys) => {
            options.push({label:items.label,value:items.id});
            if(items.children){
              items.children.forEach((itemss,keyss) => {
                options.push({label:itemss.label,value:itemss.id});
                if(itemss.children){
                  itemss.children.forEach((itemsss,keysss) => {
                    options.push({label:itemsss.label,value:itemsss.id});
                  });
                }
              });
            }
          });
        }
      });
      return options;
    },
    // 监听选中
    deptChange(e) {
      if (e && e.length == 0) {
        this.maintenanceSectionChange(null);
      }

      this.routeOptions = [];
      this.form.maintenanceSectionId = '';
      this.form.routeCodes = [];
      this.routeList = [];
      getMaintenanceSectionListAll({ departmentIdList: e }).then((res) => {
        if (res.code == 200) {
          this.routeOptions = res.data || [];
        }
      });
      this.obj = {
        managementMaintenanceBranchId: e,
      };

      this.$emit('update:fromData', this.obj);
    },
    // 监听路段 变化
    maintenanceSectionChange(e) {
      if (!e) {
        this.routeList = [];
        this.form.routeCodes = [];
        this.form.maintenanceSectionId = '';

        // 更新 obj 对象
        this.obj = {
          ...this.obj,
          maintenanceSectionId: '',
          routeCodes: [],
        };
        // 监听路段为空 则加载所有线路数据
        listAllRoute().then((res) => {
          if (res.code == 200) {
            this.routeList = res.data || [];
          }
        });
        this.$forceUpdate();
        this.$emit('update:fromData', this.obj);
        return;
      }
      listByMaintenanceSectionId({ maintenanceSectionId: e }).then((res) => {
        if (res.code == 200) {
          res.data.forEach((item) => {
            item.routeName = item.routeName + '(' + item.routeCode + ')';
          });
          this.routeList = res.data || [];
        }
      });

      this.obj = {
        ...this.obj,
        maintenanceSectionId: e,
        // maintenanceSectionIds: [e],
      };
      // this.form.maintenanceSectionIds = [e];
      this.$forceUpdate();
      this.$emit('update:fromData', this.obj);
    },

    routeCodesChange(e) {
      this.obj = {
        ...this.obj,
        routeCodes: e,
        routeCode: !this.multiple ? e : null,
      };
      this.$forceUpdate();
      this.$emit('update:fromData', this.obj);
    },
    // 树复选框选中
    handleNodeCheck(node, nodes) {

      console.log('node', node);

      this.form.managementMaintenanceBranchId =node.id

      this.deptChange(this.form.managementMaintenanceBranchId);
      this.$forceUpdate();
    },
    nodeClick(item) {
      const { id, label } = item;
      this.treeValue.id = id;
      this.treeValue.label = label;

      this.form.managementMaintenanceBranchIds = [];
      this.form.managementMaintenanceBranchIds.push(item.id);
      if(item.children&&item.children.length>0){
        item.children.forEach((item) => {
          this.form.managementMaintenanceBranchIds.push(item.id);
        });

      }

    },
    removeTag(tag) {
      let checkedNodes = this.$refs.treeRef.getCheckedNodes();
      // 删除节点
      for (let i = 0; i < checkedNodes.length; i++) {
        if (checkedNodes[i].id == tag) {
          checkedNodes.splice(i, 1);
          break;
        }
      }
      // 设置 tree 选中的节点
      this.$refs.treeRef.setCheckedNodes(checkedNodes);
      // 更新表单数据并触发搜索
      this.form.managementMaintenanceBranchId = checkedNodes.map((node) => node.id);

      this.deptChange(this.form.managementMaintenanceBranchId);
    },
    // 监听清空事件
    selectClear() {
      if (this.$refs.treeRef) {
        // 清空树选中数据
        this.$refs.treeRef.setCheckedKeys([]);

        // 清空表单数据
        this.form.managementMaintenanceBranchId = '';
        this.form.maintenanceSectionId = '';
        this.form.routeCodes = [];
        this.form.managementMaintenanceBranchIds=[];
        // 更新对象数据
        this.obj = {
          managementMaintenanceBranchId: [],
          maintenanceSectionId: '',
          routeCodes: [],
        };

        this.$forceUpdate();
        this.$emit('update:fromData', this.obj);
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.cascade-selection {
  display: flex;
  align-items: center;

  .tree-select-mini {
    font-size: 13px;
    font-weight: unset;

    ::v-deep .vue-treeselect__control {
      height: 26px;
      line-height: 26px;

      .vue-treeselect__placeholder,
      .vue-treeselect__single-value {
        line-height: 26px;
      }
    }

    ::v-deep .vue-treeselect__menu {
      .vue-treeselect__label {
        font-weight: unset;
        color: #606266;
      }
    }
  }

  .tree-select-small {
    font-size: 13px;

    ::v-deep .vue-treeselect__control {
      height: 30px;
      line-height: 30px;
      font-weight: 200;

      .vue-treeselect__placeholder,
      .vue-treeselect__single-value {
        line-height: 30px;
      }
    }

    ::v-deep .vue-treeselect__menu {
      .vue-treeselect__label {
        font-weight: unset;
        color: #606266;
      }
    }
  }

  /* 输入框超出隐藏，不换行*/
  ::v-deep .el-select__tags {
    flex-wrap: nowrap;
    overflow: auto;
  }

  ::v-deep .el-scrollbar {
    .el-select-dropdown__item {
      padding: 0;
    }
  }

  .custom-select {
    .el-select-dropdown__item {
      padding: 0;
      background-color: #0f0;
      /* 修改背景颜色为绿色 */
    }
  }
}
</style>
