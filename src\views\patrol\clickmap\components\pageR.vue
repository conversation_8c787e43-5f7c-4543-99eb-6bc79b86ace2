<template>
  <div class="page-r">
    <div class="select-year">
      <!-- <lon-lat v-model="vals" type="lon" :lon.sync="lon" :lat.sync="lat"></lon-lat> -->
      <el-select v-model="year" placeholder="请选择年份" :popper-append-to-body="false" @change="onChange">
        <el-option v-for="item in years" :key="item" :label="item + '年'" :value="item"></el-option>
      </el-select>
    </div>
    <el-row :gutter="20">
      <el-col :xs="8" :sm="6" :md="6" :lg="6" :xl="6">
        <CockpitCard title="养护工程" :class="screenBool ? 'mb-3' : 'mb-2'" :w="screenBool ? '43vh' : '24vw'" h="24.5vh"
          :isDtl="false">
          <Engineering></Engineering>
        </CockpitCard>
        <CockpitCard title="被损被盗" unit="个" :class="screenBool ? 'mb-3' : 'mb-2'" :w="screenBool ? '43vh' : '24vw'"
          h="24.5vh" :isDtl="false">
          <Echarts :option="option" v-if="option" height="24vh" />
        </CockpitCard>
        <CockpitCard title="定期检查" unit="个" :w="screenBool ? '43vh' : '24vw'" h="24.5vh" :isDtl="false">
          <Echarts :option="dqOption" v-if="dqOption" height="24vh" />
        </CockpitCard>
      </el-col>
      <el-col :xs="8" :sm="12" :md="12" :lg="12" :xl="12">
        <CockpitCard title="管理处年度统计" style="flex: 1" :class="screenBool ? 'mb-3' : 'mb-2'"
          :w="screenBool ? '24vw' : '48.5vw'" :h="screenBool ? '54.5vh' : '53.7vh'" :isDtl="false">
          <YearStatistics></YearStatistics>
        </CockpitCard>

        <div style="display: flex;">
          <CockpitCard title="年度养护经费预算" :w="screenBool ? 'calc(12vw - 20px)' : '40vw'" h="24.5vh" :isDtl="false">
            <Echarts :option="yOption" v-if="yOption" height="24vh" key="yKey" />
          </CockpitCard>
          <CockpitCard title="日常养护事件" :w="screenBool ? 'calc(12vw - 20px)' : '40vw'" h="24.5vh" class="ml-4"
            :isDtl="false">
            <Echarts :option="dOption" v-if="dOption" height="24vh" key="dKey" />
          </CockpitCard>
        </div>
      </el-col>
      <el-col :xs="8" :sm="6" :md="6" :lg="6" :xl="6">
        <CockpitCard title="今日巡查" :class="screenBool ? 'mb-3' : 'mb-2'" :w="screenBool ? '43vh' : '24vw'" h="24.5vh"
          :isDtl="false">
          <Echarts :option="dpOption" v-if="dpOption" height="24vh" key="dpKey" />
        </CockpitCard>
        <CockpitCard title="今日事件" :class="screenBool ? 'mb-3' : 'mb-2'" :w="screenBool ? '43vh' : '24vw'" h="24.5vh"
          :isDtl="false">
          <Tables :columns="dpColumns" :data="dpData"></Tables>
        </CockpitCard>
        <CockpitCard title="今日检查" :class="screenBool ? 'mb-3' : 'mb-2'" :w="screenBool ? '43vh' : '24vw'" h="24.5vh"
          :isDtl="false">
          <Tables :columns="diColumns" :data="diData"></Tables>
        </CockpitCard>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import * as echarts from "echarts";
import { isBigScreen } from "../util/utils";
import CockpitCard from "./cockpitCard.vue";
import Engineering from "./maintenance/engineering.vue";
import Echarts from "./echarts/echarts.vue";
import Tables from "./tables.vue";
import YearStatistics from "./yearStatistics.vue";
import LonLat from "@/components/mapPosition/lonLat.vue";

export default {
  components: {
    CockpitCard,
    Engineering,
    Echarts,
    Tables,
    YearStatistics,
    LonLat,
  },
  data() {
    return {
      screenBool: isBigScreen(),
      year: null,
      years: [],
      option: null,
      yOption: null, // 年度养护经费预算 图表信息
      dOption: null, // 日常养护事件 图表信息
      dpOption: null, // 今日巡查 图表信息
      dqOption: null, // 定期检查 图表信息
      dpColumns: [
        {
          label: "序号",
          prop: "index",
          width: 40,
        },
        {
          label: "管理处",
          prop: "name",
        },
        {
          label: "今日（个）",
          prop: "value",
          width: 60,
        },
        {
          label: "昨日（个）",
          prop: "percent",
          width: 60,
        },
      ], // 今日事件 表头信息
      dpData: [
        {
          index: "index",
          name: "保山管理处",
          value: "1025",
          percent: "448",
        },
        {
          index: "index",
          name: "大理管理处",
          value: "1025",
          percent: "448",
        },
        {
          index: "index",
          name: "红河管理处",
          value: "1025",
          percent: "448",
        },
        {
          index: "index",
          name: "昆明东管理处",
          value: "1025",
          percent: "448",
        },
        {
          index: "index",
          name: "昆明西管理处",
          value: "1025",
          percent: "448",
        },
        {
          index: "index",
          name: "曲靖管理处",
          value: "1025",
          percent: "448",
        },
        {
          index: "index",
          name: "丽江管理处",
          value: "1025",
          percent: "448",
        },
        {
          index: "index",
          name: "临沧管理处",
          value: "1025",
          percent: "448",
        },
        {
          index: "index",
          name: "普洱管理处",
          value: "1025",
          percent: "448",
        },
        {
          index: "index",
          name: "文山管理处",
          value: "1025",
          percent: "448",
        },
        {
          index: "index",
          name: "西双版纳管理处",
          value: "1025",
          percent: "448",
        },
        {
          index: "index",
          name: "昭通管理处",
          value: "1025",
          percent: "448",
        },
      ], // 今日事件 表格数据
      diColumns: [
        {
          label: "序号",
          prop: "index",
          width: 40,
        },
        {
          label: "管理处",
          prop: "name",
        },
        {
          label: "桥梁（座）",
          prop: "bridge",
          width: 70,
        },
        {
          label: "隧道（座）",
          prop: "tunnel",
          width: 70,
        },
        {
          label: "涵洞（个）",
          prop: "cable",
          width: 70,
        },
      ], // 今日检查 表头信息
      diData: [
        {
          index: "index",
          name: "保山管理处",
          bridge: "21",
          tunnel: "1024",
          cable: "448",
        },
        {
          index: "index",
          name: "大理管理处",
          bridge: "21",
          tunnel: "1024",
          cable: "448",
        },
        {
          index: "index",
          name: "红河管理处",
          bridge: "21",
          tunnel: "448",
          cable: "448",
        },
        {
          index: "index",
          name: "昆明东管理处",
          bridge: "21",
          tunnel: "1024",
          cable: "448",
        },
        {
          index: "index",
          name: "昆明西管理处",
          bridge: "21",
          tunnel: "1024",
          cable: "448",
        },
        {
          index: "index",
          name: "曲靖管理处",
          bridge: "21",
          tunnel: "1024",
          cable: "448",
        },
        {
          index: "index",
          name: "丽江管理处",
          bridge: "21",
          tunnel: "1024",
          cable: "448",
        },
        {
          index: "index",
          name: "临沧管理处",
          bridge: "21",
          tunnel: "1024",
          cable: "448",
        },
        {
          index: "index",
          name: "普洱管理处",
          bridge: "21",
          tunnel: "1024",
          cable: "448",
        },
        {
          index: "index",
          name: "文山管理处",
          bridge: "21",
          tunnel: "1024",
          cable: "448",
        },
        {
          index: "index",
          name: "西双版纳管理处",
          bridge: "21",
          tunnel: "1024",
          cable: "448",
        },
        {
          index: "index",
          name: "昭通管理处",
          bridge: "21",
          tunnel: "1024",
          cable: "448",
        },
      ], // 今日检查 表格数据
      vals: '', //
      lon: '102.44',
      lat: '23.12',
    };
  },
  created() {
    this.getYear();
    this.option = this.initEcharts();
    let color = [
      {
        leftColor: ['rgba(255,143,31,0.1)', 'rgba(255,143,31,1)'],
        rightColor: ['rgba(255,143,31,0.06)', 'rgba(255,204,152,0.6)'],
        topColor: ['#D9B376']
      },
      {
        leftColor: ['rgba(0,157,255,0.1)', 'rgba(0,157,255,1)'],
        rightColor: ['rgba(0,157,255,0.06)', 'rgba(0,157,255,0.6)'],
        topColor: ['#4CBAFF']
      },
      {
        leftColor: ['rgba(28,255,251,0.1)', 'rgba(28,255,251,1)'],
        rightColor: ['rgba(28,255,251,0.06)', 'rgba(0,255,251,0.6)'],
        topColor: ['#7DFFFD']
      },
    ]
    this.dqOption = this.initEcharts(['未开始', '进行中', '已完成'], [30, 124, 175], color);
    this.initYOption();
    this.initDOption();
    this.initDpOption();
  },
  methods: {
    initEcharts(xData = ['已完成', '进行中', '未开始'], data = [175, 124, 30], color = null) {
      var iconData = data || []
      color = color || [
        {
          leftColor: ['rgba(28,255,251,0.1)', 'rgba(28,255,251,1)'],
          rightColor: ['rgba(28,255,251,0.06)', 'rgba(0,255,251,0.6)'],
          topColor: ['#7DFFFD']
        },
        {
          leftColor: ['rgba(0,157,255,0.1)', 'rgba(0,157,255,1)'],
          rightColor: ['rgba(0,157,255,0.06)', 'rgba(0,157,255,0.6)'],
          topColor: ['#4CBAFF']
        },
        {
          leftColor: ['rgba(255,143,31,0.1)', 'rgba(255,143,31,1)'],
          rightColor: ['rgba(255,143,31,0.06)', 'rgba(255,204,152,0.6)'],
          topColor: ['#D9B376']
        },
      ]
      // 绘制左侧面
      const CubeLeft = echarts.graphic.extendShape({
        shape: {
          x: 0,
          y: 0
        },
        buildPath: function (ctx, shape) {
          // 会canvas的应该都能看得懂，shape是从custom传入的
          const xAxisPoint = shape.xAxisPoint
          const c0 = [shape.x + 12, shape.y]
          const c1 = [shape.x - 10, shape.y]
          const c2 = [xAxisPoint[0] - 10, xAxisPoint[1]]
          const c3 = [xAxisPoint[0] + 12, xAxisPoint[1]]
          ctx.moveTo(c0[0], c0[1]).lineTo(c1[0], c1[1]).lineTo(c2[0], c2[1]).lineTo(c3[0], c3[1]).closePath()
        }
      })
      // 绘制右侧面
      const CubeRight = echarts.graphic.extendShape({
        shape: {
          x: 0,
          y: 0
        },
        buildPath: function (ctx, shape) {
          const xAxisPoint = shape.xAxisPoint
          const c1 = [shape.x + 12, shape.y]
          const c2 = [xAxisPoint[0] + 12, xAxisPoint[1]]
          const c3 = [xAxisPoint[0] + 25, xAxisPoint[1] - 5]
          const c4 = [shape.x + 25, shape.y - 8]
          ctx.moveTo(c1[0], c1[1]).lineTo(c2[0], c2[1]).lineTo(c3[0], c3[1]).lineTo(c4[0], c4[1]).closePath()
        }
      })
      // 绘制顶面
      const CubeTop = echarts.graphic.extendShape({
        shape: {
          x: 0,
          y: 0
        },
        buildPath: function (ctx, shape) {
          const c1 = [shape.x + 12, shape.y]
          const c2 = [shape.x + 25, shape.y - 8] //右点
          const c3 = [shape.x + 2, shape.y - 8]
          const c4 = [shape.x - 10, shape.y]
          ctx.moveTo(c1[0], c1[1]).lineTo(c2[0], c2[1]).lineTo(c3[0], c3[1]).lineTo(c4[0], c4[1]).closePath()
        }
      })
      // 注册三个面图形
      echarts.graphic.registerShape('CubeLeft', CubeLeft)
      echarts.graphic.registerShape('CubeRight', CubeRight)
      echarts.graphic.registerShape('CubeTop', CubeTop)
      const VALUE = data || [];

      var option = {
        backgroundColor: 'rgba(0,0,0,0)',
        tooltip: {
          show: false
        },
        grid: {
          left: '2%',
          right: '1%',
          top: '15%',
          bottom: '2%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          data: xData,
          axisLine: {
            show: true,
            lineStyle: {
              color: 'rgba(110,112,121,0.5)'
            }
          },
          axisTick: {
            show: false,
            length: 9,
            alignWithLabel: true,
            lineStyle: {
              color: '#7DFFFD'
            }
          },
          axisLabel: {
            fontSize: 16,
            interval: 0,
            color: '#fff'
          },

          splitArea: {
            show: false,
          }
        },
        yAxis: {
          show: true,
          type: 'value',
          name: '个',
          nameTextStyle: {
            color: "#999999",
            align: 'right',
          },
          axisLine: {
            show: false,
            lineStyle: {
              color: 'white'
            }
          },
          splitLine: {
            show: true,
            lineStyle: {
              color: "rgba(110,112,121,0.5)",
            },
          },
          splitArea: {
            show: false,
          },
          axisTick: {
            show: false
          },
          axisLabel: {
            fontSize: 14,
            interval: 0,
            color: "#999999",
          },
          boundaryGap: ['20%', '20%']
        },
        series: [{
          type: 'custom',
          renderItem: (params, api) => {
            const location = api.coord([api.value(0), api.value(1)])
            return {
              type: 'group',
              children: [{
                type: 'CubeLeft',
                shape: {
                  api,
                  xValue: api.value(0),
                  yValue: api.value(1),
                  x: location[0],
                  y: location[1],
                  xAxisPoint: api.coord([api.value(0), 0])
                },
                style: {
                  fill: new echarts.graphic.LinearGradient(0, 0, 0, 1, [{
                    offset: 0,
                    color: color[params.dataIndexInside].leftColor[1],
                  },
                  {
                    offset: 1,
                    color: color[params.dataIndexInside].leftColor[0]
                  }
                  ])
                }
              }, {
                type: 'CubeRight',
                shape: {
                  api,
                  xValue: api.value(0),
                  yValue: api.value(1),
                  x: location[0],
                  y: location[1],
                  xAxisPoint: api.coord([api.value(0), 0])
                },
                style: {
                  fill: new echarts.graphic.LinearGradient(0, 0, 0, 1, [{
                    offset: 0,
                    color: color[params.dataIndexInside].rightColor[1]
                  },
                  {
                    offset: 1,
                    color: color[params.dataIndexInside].rightColor[0]
                  }
                  ])
                }
              }, {
                type: 'CubeTop',
                shape: {
                  api,
                  xValue: api.value(0),
                  yValue: api.value(1),
                  x: location[0],
                  y: location[1],
                  xAxisPoint: api.coord([api.value(0), 0])
                },
                style: {
                  fill: new echarts.graphic.LinearGradient(0, 0, 0, 1, [{
                    offset: 0,
                    color: color[params.dataIndexInside].topColor[0]
                  },
                  {
                    offset: 1,
                    color: color[params.dataIndexInside].topColor[0]
                  }
                  ])
                }
              }]
            }
          },
          data: VALUE,
        },
        {
          type: 'pictorialBar',
          label: {
            normal: {
              show: true,
              position: 'top',
              distance: -50,
              formatter: (e) => {

              },
              fontSize: 14,
              color: '#fff',
              fontWeight: 'bold',
              offset: [7, -30]
            }
          },
          tooltip: {
            show: false
          },
          z: 2,
          symbolPosition: 'end',
          symbolSize: [0, 0],
          symbolOffset: [5, -30],
          itemStyle: {
            shadowColor: 'rgba(0,0,0,0)',
            shadowBlur: 10,
            shadowOffsetY: 1,
            shadowOffsetX: 1,
          },
          data: iconData
        }]
      }
      return option;
      this.option = option;
    },
    // 初始化 年度养护经费预算 图表信息
    initYOption() {
      var trafficWay = [
        {
          name: "被损被盗 6055 万元",
          value: 6055,
        },
        {
          name: "日常养护费用 51222 万元",
          value: 51222,
        },
        {
          name: "养护检测费用 4544 万元",
          value: 4544,
        },
        {
          name: "运营费用 15641 万元",
          value: 15641,
        },
        {
          name: "专项工程费用 66157 万元",
          value: 66157,
        },
      ];

      var data = [];
      var color = [
        "#016DFF",
        "#00DFFF",
        "#FF5B00",
        "#FFDD21",
        "#A569FF",
        "#ff5b00",
        "#ff3000",
      ];
      for (var i = 0; i < trafficWay.length; i++) {
        data.push(
          {
            value: trafficWay[i].value,
            name: trafficWay[i].name,
            itemStyle: {
              normal: {
                borderWidth: 2,
                shadowBlur: 10,
                borderColor: color[i],
                shadowColor: color[i],
              },
            },
          },
          {
            value: 2,
            name: "",
            itemStyle: {
              normal: {
                label: {
                  show: false,
                },
                labelLine: {
                  show: false,
                },
                color: "rgba(0, 0, 0, 0)",
                borderColor: "rgba(0, 0, 0, 0)",
                borderWidth: 0,
              },
            },
          }
        );
      }
      var seriesOption = [
        {
          name: "",
          type: "pie",
          clockWise: false,
          radius: ["55%", "60%"],
          center: this.bigBool ? ["50%", "50%"] : ["50%", "40%"],
          hoverAnimation: false,
          startAngle: '90',
          gap: 20,
          itemStyle: {
            normal: {
              label: {
                show: true,
                position: "outside",
                color: "#ddd",
                fontSize: this.screenBool ? 22 : 14,
                formatter: function (params) {
                  var percent = 0;
                  var total = 0;
                  for (var i = 0; i < trafficWay.length; i++) {
                    total += trafficWay[i].value;
                  }
                  percent = ((params.value / total) * 100).toFixed(2);
                  if (params.name !== "") {
                    return percent + "%";
                  } else {
                    return "";
                  }
                },
              },
              labelLine: {
                length: 10,
                length2: 50,
                show: true,
                color: "#00ffff",
              },
            },
          },
          data: data,
          animationType: 'scale',
          animationEasing: 'elasticOut',
          animationDelay: function (idx) {
            return idx * 50;
          },
        },
      ];
      this.yOption = {
        backgroundColor: "rgba(0,0,0,0)",
        color: color,
        title: {
          text: "147687",
          // subtext: "（万元）",
          subtext: "{a|年度养护经费\n 预算}{c| (万元)}",
          top: this.screenBool ? "32%" : "28%",
          textAlign: "center",
          left: "49%",
          textStyle: {
            color: "#fff",
            fontSize: this.screenBool ? 28 : 18,
            fontWeight: "700",
          },
          subtextStyle: {
            fontSize: this.screenBool ? 22 : 14,
            align: "center",
            color: "#fff",
            rich: {
              a: {
                color: "#fff",
              },
              c: {
                color: "rgba(255,255,255,0.8)",
              },
            }
          },
        },
        tooltip: {
          show: false,
        },
        legend: {
          icon: "circle",
          orient: "horizontal",
          data: trafficWay.map((v) => v.name),
          bottom: "0%",
          itemWidth: 5, // 设置宽度
          itemHeight: 5, // 设置高度
          textStyle: {
            color: "#fff",
            fontSize: this.screenBool ? 24 : 12,
          },
          itemGap: 10,
        },
        toolbox: {
          show: false,
        },
        series: seriesOption,
      };
    },
    // 初始化 日常养护事件 图表信息
    initDOption() {
      var fontColor = "#ffffff";
      this.dOption = {
        backgroundColor: "rgba(0,0,0,0)",
        grid: {
          left: "2%",
          right: "3%",
          top: "15%",
          bottom: "5%",
          containLabel: true,
        },
        tooltip: {
          show: true,
          trigger: "item",
        },
        legend: {
          show: true,
          x: "center",
          top: "2%",
          y: "35",
          icon: "circle",
          itemWidth: 10,
          itemHeight: 10,
          textStyle: {
            color: "#1bb4f6",
          },
          data: ["下发", "完工"],
        },
        xAxis: [
          {
            type: "category",
            boundaryGap: false,
            axisLabel: {
              color: fontColor,
            },
            axisLine: {
              show: false,
              lineStyle: {
                color: "#ffffff",
              },
            },
            axisTick: {
              show: false,
            },
            splitLine: {
              show: false,
              lineStyle: {
                color: "#ffffff",
              },
            },
            data: [
              "1",
              "2",
              "3",
              "4",
              "5",
              "6",
              "7",
              "8",
              "9",
              "10",
              "11",
              "12",
            ],
          },
        ],
        yAxis: [
          {
            type: "value",
            name: "个",
            min: 0,
            max: 10000,
            axisLabel: {
              formatter: "{value}",
              textStyle: {
                color: "#999999",
              },
            },
            axisLine: {
              show: false,
              lineStyle: {
                color: "#999999",
              },
            },
            axisTick: {
              show: false,
            },
            splitLine: {
              show: true,
              lineStyle: {
                color: "rgba(153,153,153,0.3)",
              },
            },
            splitArea: {
              show: false,
            },
          },
        ],
        series: [
          {
            name: "下发",
            type: "line",
            stack: "总量",
            symbol: "emptyCircle",
            symbolSize: 8,
            itemStyle: {
              normal: {
                color: "#1CFFBC",
                lineStyle: {
                  color: "#1CFFBC",
                  width: 1,
                },
                areaStyle: {
                  color: new echarts.graphic.LinearGradient(0, 1, 0, 0, [
                    {
                      offset: 0,
                      color: "rgba(28,255,188,0.3)",
                    },
                    {
                      offset: 1,
                      color: "rgba(28,255,188,0.9)",
                    },
                  ]),
                },
              },
            },
            markPoint: {
              itemStyle: {
                normal: {
                  color: "red",
                },
              },
            },
            data: [
              120, 132, 1010, 134, 90, 2300, 5210, 182, 191, 234, 2600, 280,
            ],
          },
          {
            name: "完工",
            type: "line",
            stack: "总量",
            symbol: "emptyCircle",
            symbolSize: 8,
            itemStyle: {
              normal: {
                color: "#0154FB",
                lineStyle: {
                  color: "#0154FB",
                  width: 1,
                },
                areaStyle: {
                  //color: '#94C9EC'
                  color: new echarts.graphic.LinearGradient(0, 1, 0, 0, [
                    {
                      offset: 0,
                      color: "rgba(1,84,251,0.3)",
                    },
                    {
                      offset: 1,
                      color: "rgba(1,84,251,0.9)",
                    },
                  ]),
                },
              },
            },
            data: [
              220, 182, 191, 210, 2300, 270, 4270, 201, 1540, 140, 240, 250,
            ],
          },
        ],
      };
    },
    // 初始化 今日巡查 图表信息
    initDpOption() {
      this.dpOption = {
        backgroundColor: "rgba(0,0,0,0)",
        tooltip: {
          trigger: "axis",
          axisPointer: {
            type: "shadow",
          },
        },
        grid: {
          left: "2%",
          right: "3%",
          top: "15%",
          bottom: "5%",
          containLabel: true,
        },
        legend: {
          data: ["今日", "昨日"],
          icon: "rect",
          itemWidth: 10,
          itemHeight: 10,
          borderRadius: 10,
          textStyle: {
            fontSize: this.screenBool ? 22 : 14,
            color: "#FFFFFF",
          },
        },
        xAxis: [
          {
            type: "category",
            data: [
              "保山",
              "大理",
              "昆明东",
              "昆明西",
              "丽江",
              "临沧",
              "普洱",
              "曲靖",
              "文山",
              "昭通",
              "红河",
              "西双版纳",
            ],
            axisPointer: {
              type: "shadow",
            },
            axisLine: {
              show: true,
              lineStyle: {
                color: "rgba(153,153,153,0.3)",
              },
            },
            axisLabel: {
              rotate: 30,
              textStyle: {
                color: "#F5F5F5", //更改坐标轴文字颜色
                fontSize: this.screenBool ? 22 : 12, //更改坐标轴文字大小
              },
            },
          },
        ],
        yAxis: [
          {
            type: "value",
            name: "(km)",
            min: 0,
            max: 5000,
            axisLabel: {
              textStyle: {
                color: "#F5F5F5", //更改坐标轴文字颜色
                fontSize: this.screenBool ? 20 : 12, //更改坐标轴文字大小
              },
            },
            axisLine: {
              show: true,
              lineStyle: {
                color: "rgba(153,153,153,0.3)",
              },
            },
            splitLine: {
              show: true,
              lineStyle: {
                color: "rgba(153,153,153,0.3)",
              },
            },
            splitArea: {
              show: false,
            },
          },
        ],
        series: [
          {
            name: "今日",
            type: "bar",
            itemStyle: {
              opacity: 1, // 这个是 透明度
              color: new echarts.graphic.LinearGradient(
                0,
                1,
                0,
                0,
                [
                  {
                    offset: 0,
                    color: "rgba(33,155,255, 0)", // 0% 处的颜色
                  },
                  {
                    offset: 1,
                    color: "rgba(33,155,255, 1)", // 100% 处的颜色
                  },
                ],
                false
              ),
            },
            // 实现数字展示在柱状图
            label: {
              show: false,
              position: "top",
              fontSize: 12,
              color: "#F5F5F5",
              offset: [0, -10],
              formatter: "{c}", //添加单位
            },
            data: [
              2180, 2170, 1110, 1370, 137, 1260, 420, 420, 1232, 2121, 4542,
              533, 833,
            ],
          },
          {
            name: "昨日",
            type: "bar",
            itemStyle: {
              // lenged文本
              opacity: 1, // 这个是 透明度
              color: new echarts.graphic.LinearGradient(
                0,
                1,
                0,
                0,
                [
                  {
                    offset: 0,
                    color: "rgba(118,255,49, 0)", // 0% 处的颜色
                  },
                  {
                    offset: 1,
                    color: "rgba(118,255,49, 1)", // 100% 处的颜色
                  },
                ],
                false
              ),
            },
            label: {
              show: false,
              position: "top",
              fontSize: 12,
              color: "#F5F5F5",
              offset: [0, -10],
              formatter: "{c}", //添加单位
            },
            data: [
              490, 380, 200, 480, 1480, 2310, 3370, 4250, 3110, 370, 937, 260,
              420,
            ],
          },
        ],
      };
    },
    onChange(e) {
      console.log('onChange', e)
      console.log(this.vals)
      console.log('lon', this.lon)
      console.log('lat', this.lat)
    },
    getYear() {
      let date = new Date();
      let year = date.getFullYear();
      this.year = year;
      for (let i = year - 5; i < year + 5; i++) {
        this.years.push(i);
      }
    },
  },
};
</script>

<style lang="scss" scoped>
@import "@/assets/styles/utils.scss";

.page-r {
  width: 100%;
  min-width: vwpx(2000px);

  .select-year {
    width: 100%;
    height: 4vh;
    margin-bottom: 5px;

    display: flex;
    align-items: center;
    justify-content: flex-end;

    ::v-deep .el-input {
      .el-input__inner {
        background-color: rgba(1, 102, 254, 0.2);
        border: 1px solid #0166fe;
        color: #ffffff;
      }

      .el-input__inner::placeholder {
        color: #bbbbbb;
      }

      .el-input-group__append {
        background-color: rgba(1, 102, 254, 0.2);
        border: 1px solid #0166fe;
        color: #ffffff;
        border-left: none;
        padding: 0 10px;
        cursor: pointer;
      }
    }
  }

  .mb-2 {
    margin-bottom: vwpx(20px);
  }

  .mb-3 {
    margin-bottom: vwpx(32px);
  }

  .ml-2 {
    margin-left: vwpx(20px);
  }

  .ml-4 {
    margin-left: vwpx(40px);
  }

  .mt-3 {
    margin-top: vwpx(30px);
  }

  .px-2 {
    padding: 0 vwpx(20px);
  }
}
</style>
