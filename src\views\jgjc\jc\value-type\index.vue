<template>
  <div class="app-container">
    <el-row :gutter="20">

      <!--筛选区开始-->
      <el-col :span="24" :xs="24">
        <el-row>
          <el-col :span="24">
            <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" label-width="90px">
              <el-form-item label="采样方案：" prop="sampleProjectId">
                <el-select
                  v-model="queryParams.sampleProjectId"
                  placeholder="请输入采样方案"
                  clearable
                >
                  <el-option
                    v-for="dict in projectList"
                    :key="dict.id"
                    :label="dict.name"
                    :value="dict.id"
                  />
                </el-select>
              </el-form-item>
              <el-form-item label="编码：" prop="code">
                <el-input
                  v-model="queryParams.code"
                  placeholder="请输入编码"
                  clearable
                  prefix-icon="el-icon-user"
                  style="width: 240px"
                  maxlength="200" show-word-limit
                  @keyup.enter.native="handleQuery"
                />
              </el-form-item>
              <el-form-item label="名称：" prop="name">
                <el-input
                  v-model="queryParams.name"
                  placeholder="请输入名称"
                  clearable
                  prefix-icon="el-icon-user"
                  style="width: 240px"
                  maxlength="200" show-word-limit
                  @keyup.enter.native="handleQuery"
                />
              </el-form-item>
              <el-form-item label="单位：" prop="unit">
                <el-input
                  v-model="queryParams.unit"
                  placeholder="请输入单位"
                  clearable
                  prefix-icon="el-icon-user"
                  style="width: 240px"
                  maxlength="200" show-word-limit
                  @keyup.enter.native="handleQuery"
                />
              </el-form-item>
              <el-form-item>
                <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
                <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
                <el-button v-show="!showSearch" @click="showSearch=true" icon="el-icon-arrow-down" circle></el-button>
                <el-button v-show="showSearch" @click="showSearch=false" icon="el-icon-arrow-up" circle></el-button>
              </el-form-item>
            </el-form>
            <!--默认折叠-->
          </el-col>
          <!--默认折叠 ,此处仅作为示例-->
          <el-col :span="24">
            <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch"
                     label-width="90px">
              <el-form-item label="默认显示：" prop="defaultShow">
                <el-select
                  v-model="queryParams.defaultShow"
                  placeholder="请输入是否默认显示"
                  prefix-icon="el-icon-edit"
                  style="width: 240px"
                >
                  <el-option
                    v-for="d in yesNoList"
                    :key="d.id"
                    :label="d.label"
                    :value="d.id"
                  />
                </el-select>
              </el-form-item>
              <el-form-item label="存储类型：" prop="storageType">
                <el-select
                  v-model="queryParams.storageType"
                  placeholder="请输入存储类型"
                  prefix-icon="el-icon-edit"
                  style="width: 240px"
                >
                  <el-option
                    v-for="d in storageTypeList"
                    :key="d.id"
                    :label="d.label"
                    :value="d.id"
                  />
                </el-select>
              </el-form-item>
              <el-form-item label="默认采样类型名称：" label-width="100" prop="defaultSampleTypeName">
                <el-select
                  v-model="queryParams.defaultSampleTypeName"
                  placeholder="请输入默认采样类型名称"
                  prefix-icon="el-icon-edit"
                  style="width: 240px"
                >
                  <el-option
                    v-for="d in sampleTypeList"
                    :key="d.name"
                    :label="d.title"
                    :value="d.name"
                  />
                </el-select>
              </el-form-item>
            </el-form>
          </el-col>
        </el-row>
        <!--筛选区结束-->

        <!--操作按钮区开始-->
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button
              type="primary"
              icon="el-icon-plus"
              size="mini"
              @click="handleAdd"
              v-hasPermi="['system:type:add']"
            >新增
            </el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button
              type="success"
              icon="el-icon-edit"
              size="mini"
              :disabled="single"
              @click="handleUpdate"
              v-hasPermi="['system:type:edit']"
            >修改
            </el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button
              type="danger"
              icon="el-icon-delete"
              size="mini"
              :disabled="multiple"
              @click="handleDelete"
              v-hasPermi="['system:type:remove']"
            >删除
            </el-button>
          </el-col>
          <right-toolbar :showSearch.sync="showSearch" @queryTable="getList" :columns="columns"></right-toolbar>
        </el-row>
        <!--操作按钮区结束-->

        <!--数据表格开始-->
        <div class="tableDiv">
          <el-table
            ref="table"
            size="mini"
            :height="showSearch ? 'calc(100vh - 320px)' : 'calc(100vh - 260px)'"
            style="width: 100%"
            v-loading="loading"
            border
            :data="typeList"
            @selection-change="handleSelectionChange"
            :row-style="rowStyle"
            @row-click="handleRowClick"
          >
            <el-table-column type="selection" width="50" align="center" />
            <el-table-column fixed align="right" type="index" width="50">
            </el-table-column>
            <el-table-column label="采样方案" align="left" header-align="center" prop="sampleProjectIdDisplay"
                             width="130" :show-overflow-tooltip="true" />
            <el-table-column label="编码" align="left" header-align="center" prop="code" width="200"
                             :show-overflow-tooltip="true" />
            <el-table-column label="名称" align="left" header-align="center" prop="name" width="200"
                             :show-overflow-tooltip="true" />
            <el-table-column label="单位" align="left" header-align="center" prop="unit" width="80"
                             :show-overflow-tooltip="true" />
            <el-table-column label="小数位数" align="right" header-align="center" prop="numericScale" width="80" />
            <el-table-column label="默认显示" align="center" header-align="center" prop="defaultShow" width="80">
              <template slot-scope="scope">
                <el-tag v-if="scope.row.defaultShow" type="success">是</el-tag>
                <el-tag v-if="!scope.row.defaultShow" type="danger">否</el-tag>
              </template>
            </el-table-column>
            <el-table-column label="显示顺序" align="right" header-align="center" prop="showOrders" width="80" />
            <el-table-column label="存储类型" align="left" header-align="center" prop="storageType" width="80"
                             :show-overflow-tooltip="true" />
            <el-table-column label="默认采样类型名称" align="left" header-align="center" prop="defaultSampleTypeName"
                             width="140" :show-overflow-tooltip="true" />
            <el-table-column label="说明" align="left" header-align="center" prop="description"
                             :show-overflow-tooltip="true" />
            <el-table-column label="序号" align="right" header-align="center" prop="orders" width="50" />
            <el-table-column
              label="操作"
              fixed="right"
              align="center"
              width="160"
              class-name="small-padding fixed-width"
            >
              <template slot-scope="scope" v-if="scope.row.userId !== 1">
                <el-button
                  size="mini"
                  type="text"
                  icon="el-icon-edit"
                  @click="handleUpdate(scope.row)"
                  v-hasPermi="['system:type:edit']"
                >修改
                </el-button>
                <el-button
                  size="mini"
                  type="text"
                  icon="el-icon-delete"
                  @click="handleDelete(scope.row)"
                  v-hasPermi="['system:type:remove']"
                >删除
                </el-button>
              </template>
            </el-table-column>
          </el-table>
          <pagination
            v-show="total>0"
            :total="total"
            :page.sync="queryParams.pageNum"
            :limit.sync="queryParams.pageSize"
            @pagination="getList"
          />
        </div>
        <!--数据表格结束-->
      </el-col>
    </el-row>

    <!-- 添加或修改值类型对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="700px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="130px">
        <el-col :span="24">
          <el-form-item label="采样方案" prop="sampleProjectId">
            <el-select
              v-model="form.sampleProjectId"
              placeholder="请输入采样方案"
              style="width: 100%;"
            >
              <el-option
                v-for="dict in projectList"
                :key="dict.id"
                :label="dict.name"
                :value="dict.id"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="编码" prop="code">
            <el-input v-model="form.code" placeholder="请输入编码" maxlength="200" show-word-limit />
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="名称" prop="name">
            <el-input v-model="form.name" placeholder="请输入名称" maxlength="200" show-word-limit />
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="单位" prop="unit">
            <el-input v-model="form.unit" placeholder="请输入单位" maxlength="200" show-word-limit />
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="小数位数" prop="numericScale">
            <el-input-number v-model="form.numericScale" placeholder="请输入小数位数" style="width:100%;"
                             controls-position="right" :min="0" :step="1" :max="8"></el-input-number>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="默认显示" prop="defaultShow">
            <el-switch v-model="form.defaultShow">
            </el-switch>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="显示顺序" prop="showOrders">
            <el-input-number v-model="form.showOrders" placeholder="请输入显示序号" style="width:100%;"
                             controls-position="right" :min="1" :step="1" :max="1000000"></el-input-number>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="存储类型" prop="storageType">
            <el-select
              v-model="form.storageType"
              placeholder="请输入存储类型"
              prefix-icon="el-icon-edit"
              style="width: 100%;"
            >
              <el-option
                v-for="d in storageTypeList"
                :key="d.id"
                :label="d.label"
                :value="d.id"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="默认采样类型名称" prop="defaultSampleTypeName">
            <el-select
              v-model="form.defaultSampleTypeName"
              placeholder="请输入默认采样类型名称"
              prefix-icon="el-icon-edit"
              style="width: 100%;"
            >
              <el-option
                v-for="d in sampleTypeList"
                :key="d.name"
                :label="d.title"
                :value="d.name"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="序号" prop="orders">
            <el-input-number v-model="form.orders" placeholder="请输入序号" style="width:100%;"
                             controls-position="right" :min="1" :step="1" :max="1000000"></el-input-number>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="说明" prop="description">
            <el-input v-model="form.description" type="textarea" :rows="3" placeholder="请输入内容" maxlength="10000"
                      show-word-limit />
          </el-form-item>
        </el-col>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

  </div>
</template>

<script>
import {
  addValueType,
  delValueType,
  delValueTypeBatch,
  getValueType,
  listValueType,
  updateValueType,
} from '@/api/jgjc/jc/valueType'
import { getFindAllSampleType } from '@/api/jgjc/jc/sampleType'
import { getToken } from '@/utils/auth'
import '@riophae/vue-treeselect/dist/vue-treeselect.css'
import { getFindAllSampleProject } from '@/api/jgjc/jc/sampleProject'

export default {
  name: 'JcValueType',
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      delText: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: false,
      dictType: [],
      // 总条数
      total: 0,
      // 值类型表格数据
      typeList: null,
      projectList: null,
      // 弹出层标题
      title: '',
      // 部门树选项
      deptOptions: undefined,
      // 是否显示弹出层
      open: false,
      // 表单参数
      form: {},
      defaultProps: {
        children: 'children',
        label: 'label',
      },
      yesNoList: [{ id: 'true', label: '是' }, { id: 'false', label: '否' }],
      storageTypeList: [{ id: 'common', label: '普通数据' }, { id: 'jsd', label: '动态大数据' }, {
        id: 'vehicle-load',
        label: '车辆荷载数据',
      }],
      sampleTypeList: [],
      // 用户导入参数
      upload: {
        // 是否显示弹出层（用户导入）
        open: false,
        // 弹出层标题（用户导入）
        title: '',
        // 是否禁用上传
        isUploading: false,
        // 是否更新已经存在的用户数据
        updateSupport: 0,
        // 设置上传的请求头部
        headers: { Authorization: 'Bearer ' + getToken() },
        // 上传的地址
        url: process.env.VUE_APP_BASE_API + '/system/user/importData',
      },
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 50,
        sampleProjectId: null,
        code: null,
        name: null,
        unit: null,
        defaultShow: null,
        dataStorageType: null,
        defaultSampleTypeName: null,
        orderDirection: 'ASC',
      },
      // 列信息
      columns: [
        { key: 0, label: `采样方案`, visible: true },
        { key: 1, label: `编码`, visible: true },
        { key: 2, label: `名称`, visible: true },
        { key: 3, label: `单位`, visible: true },
        { key: 4, label: `小数位数`, visible: true },
        { key: 5, label: `默认显示`, visible: true },
        { key: 6, label: `显示顺序`, visible: true },
        { key: 7, label: `序号`, visible: true },
        { key: 8, label: `说明`, visible: true },
      ],
      // 表单校验
      rules: {
        sampleProjectId: [
          { required: true, message: '采样方案不能为空', trigger: 'blur' },
        ],
        code: [
          { required: true, message: '编码不能为空', trigger: 'blur' },
        ],
        name: [
          { required: true, message: '名称不能为空', trigger: 'blur' },
        ],
        numericScale: [
          { required: true, message: '小数位数不能为空', trigger: 'blur' },
        ],
        defaultShow: [
          { required: true, message: '默认显示不能为空', trigger: 'blur' },
        ],
        showOrders: [
          { required: true, message: '显示顺序不能为空', trigger: 'blur' },
        ],
        orders: [
          { required: true, message: '序号不能为空', trigger: 'blur' },
        ],
      },
    }
  },
  watch: {
    // 根据名称筛选部门树
  },
  created() {
    this.getOtherData()
    this.getList()
    // this.getDeptTree();
    // this.getConfigKey("sys.user.initPassword").then(response => {
    //   this.initPassword = response.msg;
    // });
  },
  methods: {
    getOtherData() {
      this.loading = true
      getFindAllSampleProject().then(response => {
        this.projectList = response.data
        this.loading = false
      })
      getFindAllSampleType().then(response => {
        this.sampleTypeList = response.data
        this.loading = false
      })
    },
    /** 查询用户列表 */
    getList() {
      this.loading = true
      listValueType(this.queryParams).then(response => {
        this.typeList = response.data
        this.total = response.total
        this.loading = false
      })
    },
    // 取消按钮
    cancel() {
      this.open = false
      this.reset()
    },
    // 表单重置
    reset() {
      this.form = {
        sampleProjectId: null,
        code: null,
        name: null,
        unit: '',
        numericScale: 0,
        defaultShow: false,
        showOrders: 1,
        orders: 1,
        description: '',
      }
      this.resetForm('form')
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dateRange = []
      this.resetForm('queryForm')
      this.handleQuery()
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.delText = selection.map(item => item.name)
      this.single = selection.length != 1
      this.multiple = !selection.length
    },
    // 表格点击勾选
    handleRowClick(row) {
      row.isSelected = !row.isSelected
      this.$refs.table.toggleRowSelection(row)
    },
    // 勾选高亮
    rowStyle({ row, rowIndex }) {
      if (this.ids.includes(row.id)) {
        return { 'background-color': '#E1F0FF', color: '#333' }
      } else {
        return { 'background-color': '#fff', color: '#333' }
      }
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset()
      this.open = true
      this.title = '添加值类型'
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset()
      const id = row.id || this.ids
      getValueType(id).then(response => {
        this.form = response.data
        this.open = true
        this.title = '修改值类型'
      })
    },
    /** 提交按钮 */
    submitForm: function() {
      this.$refs['form'].validate(valid => {
        if (valid) {
          if (this.form.id != null) {
            updateValueType(this.form).then(response => {
              if (response.success) {
                this.$modal.msgSuccess('修改成功')
                this.open = false
                this.getList()
              } else {
                this.$modal.msgWarning('修改失败，编码不能重复')
              }
            })
          } else {
            addValueType(this.form).then(response => {
              if (response.success) {
                this.$modal.msgSuccess('新增成功')
                this.open = false
                this.getList()
              } else {
                this.$modal.msgWarning('新增失败，编码不能重复')
              }
            })
          }
        }
      })
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      if (row.id) {
        this.$modal.confirm('是否确认删除值类型名称为"' + row.name + '"的数据项？').then(function() {
          return delValueType(row.id)
        }).then(() => {
          this.getList()
          this.$modal.msgSuccess('删除成功')
        }).catch(() => {
        })
      } else {
        var ids = this.ids
        this.$modal.confirm('是否确认删除值类型名称为"' + this.delText + '"的数据项？').then(function() {
          return delValueTypeBatch(ids)
        }).then(() => {
          this.getList()
          this.$modal.msgSuccess('删除成功')
        }).catch(() => {
        })
      }
    },
  },
}
</script>
<style>
.hasTagsView .app-main[data-v-078753dd] {
  background: #f5f7fa;
}

.tableDiv {
  background-color: white;
  padding-bottom: 10px;
}
</style>
