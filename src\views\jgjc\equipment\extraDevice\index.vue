<template>
  <div class='app-container'>
    <el-row :gutter='20'>
      <!--部门数据-->
      <el-col :span='relaNav ? 4 : 0' :xs='24' class='leftDiv'>
        <!--折叠图标-->
        <div class='leftIcon' @click='relaNav = false'>
          <span class='el-icon-caret-left'></span>
        </div>
        <div class='head-container' style='width: 300px'>
          <el-tree
            ref='tree'
            :data='filteredTreeData'
            :default-expanded-keys='[1]'
            :expand-on-click-node='false'
            :props='defaultProps'
            highlight-current
            node-key='code'
            @node-click='handleNodeClick'
          >
          </el-tree>
        </div>
      </el-col>
      <!--角色数据-->
      <el-col :span='relaNav ? 20 : 24' :xs='24'>
        <!--展开图标-->
        <div v-show='!relaNav' class='rightIcon' @click='relaNav = true'>
          <span class='el-icon-caret-right'></span>
        </div>
        <el-row :gutter='10'>
          <el-form ref='queryForm' :inline='true' :model='queryParams' label-width='68px' size='small'>
            <el-form-item label='' prop='deviceName'>
              <el-input v-model='queryParams.deviceName' clearable placeholder='请输入名称' />
            </el-form-item>
            <el-form-item label='' prop='deviceCode'>
              <el-input v-model='queryParams.deviceCode' clearable placeholder='请输入编码' />
            </el-form-item>
            <el-form-item>
              <el-button icon='el-icon-search' size='mini' type='primary' @click='handleQuery'>搜索</el-button>
              <el-button icon='el-icon-refresh' size='mini' @click='resetQuery'>重置</el-button>
            </el-form-item>
          </el-form>
        </el-row>
        <el-row :gutter='10' class='mb8'>
          <el-col :span='1.5'>
            <el-button
              icon='el-icon-plus'
              size='mini'
              type='primary'
              @click='handleAdd'
            >新增
            </el-button>
          </el-col>
          <el-col :span='1.5'>
            <el-button
              icon='el-icon-delete'
              size='mini'
              type='danger'
              @click='handleDelete'
            >删除
            </el-button>
          </el-col>
        </el-row>
        <!-- 筛选区结束 -->
        <!-- 数据表格开始 -->
        <div class='tableDiv'>
          <el-table v-adjust-table v-loading='loading' :data='dataList'
                    :height="'calc(100vh - 260px)'" border size='mini'
                    @selection-change='handleSelectionChange'>
            <el-table-column align='center' fixed='left' type='selection' width='55'></el-table-column>
            <el-table-column align='center' fixed label='序号' type='index' width='100'></el-table-column>
            <template v-for='(column, index) in columns'>
              <el-table-column v-if='column.visible' :key='index' :label='column.label' :prop='column.field' :width='column.width'
                               align='center' show-overflow-tooltip>
                <template slot-scope='scope'>
                  <span>{{ scope.row[column.field] }}</span>
                </template>
              </el-table-column>
            </template>
            <el-table-column align='center' fixed='right' label='操作' width='150'>
              <template slot-scope='scope'>
                <el-button
                  icon='el-icon-edit'
                  size='mini'
                  type='text'
                  @click='handleEdit(scope.row)'
                >编辑
                </el-button>
              </template>
            </el-table-column>
          </el-table>
          <pagination
            v-show='total > 0'
            :limit.sync='queryParams.pageSize'
            :page.sync='queryParams.pageNum'
            :total='total'
            @pagination='handleQuery'
          />
        </div>
        <!-- 数据表格结束 -->
      </el-col>
    </el-row>
    <el-dialog :dialogTitle='dialogTitle' v-if='dialogVisible' :visible.sync='dialogVisible' width='800px' append-to-body>
      <el-form ref='elForm' :model='formData' :rules='rules' label-width='100px' size="medium">
        <el-row :gutter='20'>
          <el-col :span='12'>
            <el-form-item label='所在结构物' prop='structureName'>
              <el-input v-model='formData.structureName' placeholder='请输入所在结构物' readonly/>
            </el-form-item>
          </el-col>
          <el-col :span='12'>
            <el-form-item label='设备名称' prop='deviceName'>
              <el-select v-model='formData.deviceName' placeholder='请选择设备名称' style='width: 100%' @change='changeDeviceName'>
                <el-option
                  v-for='item in deviceNameList'
                  :key='item.id'
                  :label='item.name'
                  :value='item.name'
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span='12'>
            <el-form-item label='设备编码' prop='deviceCode'>
              <el-input v-model='formData.deviceCode' placeholder='请输入设备编码' />
            </el-form-item>
          </el-col>
          <el-col :span='12'>
            <el-form-item label='安装代码' prop='installCode'>
              <el-input v-model='formData.installCode' placeholder='请输入安装代码' />
            </el-form-item>
          </el-col>
          <el-col :span='12'>
            <el-form-item label='安装位置' prop='installLocation'>
              <el-input v-model='formData.installLocation' placeholder='请输入安装位置' />
            </el-form-item>
          </el-col>
          <el-col :span='12'>
            <el-form-item label='设备类型' prop='deviceTypeName'>
              <el-input v-model='formData.deviceTypeName' placeholder='请输入设备类型' readonly/>
            </el-form-item>
          </el-col>
          <el-col :span='12'>
            <el-form-item label='生产厂家' prop='manufacturerName'>
              <el-input v-model='formData.manufacturerName' placeholder='请输入生产厂家' readonly/>
            </el-form-item>
          </el-col>
          <el-col :span='12'>
            <el-form-item label='出厂编号' prop='deviceFactoryCode'>
              <el-input v-model='formData.deviceFactoryCode' placeholder='请输入出厂编号' />
            </el-form-item>
          </el-col>
          <el-col :span='12'>
            <el-form-item label='出厂日期' prop='dateOfManufacture'>
              <el-date-picker
                v-model='formData.dateOfManufacture'
                type='date'
                value-format='yyyy-MM-dd'
                placeholder='请选择出厂日期'
                style='width: 100%'
              />
            </el-form-item>
          </el-col>
          <el-col :span='24'>
            <el-form-item label='参数' prop='parameters'>
              <el-input
                v-model='formData.parameters'
                placeholder='请输入设备参数'
              />
            </el-form-item>
          </el-col>
          <el-col :span='12'>
            <el-form-item label='型号' prop='type'>
              <el-input v-model='formData.type' placeholder='请输入设备型号' readonly/>
            </el-form-item>
          </el-col>
          <el-col :span='12'>
            <el-form-item label='排序号' prop='orders'>
              <el-input-number v-model="formData.orders" :min="0" controls-position="right"/>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div style="text-align: right">
        <el-button type='primary' @click='handleConfirm'>确 定</el-button>
        <el-button @click='cancel'>取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  getOtherDevicePage,
  saveOtherDevice,
  updateOtherDevice,
  deleteOtherDevice,
  getMaintenanceTree, getDeviceTypePage,
} from '@/api/jgjc/equipment/extraDevice'
import { listDeviceModels } from '@/api/jgjc/equipment/equipmentModel'

export default {
  name: 'ContentManagement',
  data() {
    return {
      // 遮罩层
      loading: false,
      // 左侧组织树
      relaNav: true,
      relaOptions: [],
      filteredTreeData: [],
      defaultProps: {
        children: 'children',
        label: 'label',
        isLeaf: (data) => {
          // 通过该字段判断是否是叶子节点（没有子节点）
          // 这里假设接口返回的节点中，有hasChildren字段标识是否有子节点
          return !data.children
        },
      },
      loadedKeys: new Set(), // 记录已加载过的节点key，避免重复加载
      // 总条数
      total: 0,
      dataList: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
      },
      // 列信息
      columns: [
        { key: 0, width: '150', field: 'deviceCode', label: '编码', visible: true },
        { key: 1, width: '200', field: 'deviceName', label: '设备名称', visible: true },
        { key: 2, width: '100', field: 'type', label: '型号', visible: true },
        { key: 3, width: '200', field: 'structureName', label: '所在结构物', visible: true },
        { key: 4, width: '150', field: 'deviceFactoryCode', label: '出厂编号', visible: true },
        { key: 5, width: '200', field: 'manufacturerName', label: '生产厂家', visible: true },
        { key: 6, width: '100', field: 'deviceTypeName', label: '类型', visible: true },
        { key: 7, width: '200', field: 'dateOfManufacture', label: '出厂日期', visible: true },
        { key: 8, width: '200', field: 'parameters', label: '参数', visible: true },
        { key: 9, width: '100', field: 'installCode', label: '安装代码', visible: true },
        { key: 10, field: 'installLocation', label: '安装位置', visible: true },
      ],
      ids: [],
      structureId: '',
      structureName: '',
      deviceTypeId: '',
      dialogTitle: '', // 对话框标题
      dialogVisible: false, // 对话框显示状态
      formData: {},
      deviceNameList: [],
      rules: {
        deviceName: [
          { required: true, message: '设备名称不能为空', trigger: 'blur' },
        ],
        deviceCode: [
          { required: true, message: '设备编码不能为空', trigger: 'blur' }
        ],
        installCode: [
          { required: true, message: '安装代码不能为空', trigger: 'blur' }
        ],
        installLocation: [
          { required: true, message: '安装位置不能为空', trigger: 'blur' }
        ]
      }
    }
  },
  created() {
    this.getDeptTree()
  },
  methods: {
    // 查询部门下拉树结构
    getDeptTree() {
      getMaintenanceTree({}).then(response => {
        this.relaOptions = response.data
        this.filteredTreeData = [...this.relaOptions]
      })
    },
    // 树节点点击事件
    handleNodeClick(nodeData, node) {
      // 如果节点没有子节点且未被加载过
      if (node.level == 4 && !node.childNodes.length && nodeData.assetId && !this.loadedKeys.has(nodeData.assetId)) {
        this.loadChildren(node)
      }
      if (node.level == 5) {
        console.log(node)
        this.queryParams = {
          pageNum: 1,
          pageSize: 10,
          deviceTypeId: nodeData.id,
        }
        this.structureId = node.parent.data.id
        this.structureName = node.parent.data.label
        this.deviceTypeId = nodeData.id
        this.getDeviceName()
        this.handleQuery()
      }
    },

    // 加载子节点数据
    async loadChildren(node) {
      const nodeData = node.data
      // 显示加载状态
      node.loading = true
      try {
        const res = await getDeviceTypePage({ nameNot: nodeData.label, pageNum: 1, pageSize: 100 })
        if (res.rows && res.rows.length > 0) {
          // 给树节点添加子节点（Vue.set确保响应式）
          for (let i = 0; i < res.rows.length; i++) {
            res.rows[i].label = res.rows[i].name
          }
          this.$set(nodeData, 'children', res.rows)
          // 展开当前节点（可选）
          this.$nextTick(() => {
            node.expanded = true
          })
        }
        // 标记为已加载
        this.loadedKeys.add(nodeData.assetId)
      } finally {
        node.loading = false
      }
    },
    handleQuery() {
      this.loading = true
      getOtherDevicePage(this.queryParams).then(res => {
        this.dataList = res.rows
        this.total = res.total
        this.loading = false
      }).catch(err => {
        this.loading = false
        console.error(err)
      })
    },
    resetQuery() {
      this.queryParams = {
        pageNum: 1,
        pageSize: 10,
      }
      this.handleQuery()
    },
    handleSelectionChange(e) {
      this.ids = e.map(item => item.id)
    },
    handleDelete() {
      if (this.ids.length <= 0) {
        this.$message.warning('请勾选需要删除的数据')
        return
      }
      this.$confirm('是否确认删除选中的数据？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }).then(() => {
        this.loading = true
        deleteOtherDevice({ ids: this.ids }).then(res => {
          if (res.code === 200) {
            this.$message.success('删除成功')
            this.handleQuery()
          } else {
            this.$message.error(res.msg)
          }
          this.loading = false
        }).catch(err => {
          this.loading = false
          console.error(err)
        })
      })
    },
    handleAdd() {
      if (!this.deviceTypeId) {
        this.$message.warning('请选择设备类型')
        return
      }
      this.formData = {}
      this.formData.structureId = this.structureId
      this.formData.structureName = this.structureName
      this.formData.deviceTypeId = this.deviceTypeId
      this.dialogTitle = '新增'
      this.dialogVisible = true
    },
    handleEdit(row) {
      this.dialogTitle = '修改'
      this.formData = { ...row }
      this.dialogVisible = true
    },
    handleConfirm() {
      this.$refs['elForm'].validate(valid => {
        if (valid) {
          if (this.formData.id) {
            updateOtherDevice(this.formData).then(res => {
              this.$modal.msgSuccess('修改成功')
              this.dialogVisible = false
              this.handleQuery()
            })
          } else {
            saveOtherDevice(this.formData).then(res => {
              this.$modal.msgSuccess('新增成功')
              this.dialogVisible = false
              this.handleQuery()
            })
          }
        }
      })
    },
    getDeviceName() {
      listDeviceModels({deviceTypeId: this.deviceTypeId, pageNum: 1, pageSize: 100}).then(res => {
        this.deviceNameList = res.rows
      })
    },
    changeDeviceName() {
      const device = this.deviceNameList.find(item => item.name == this.formData.deviceName)
      this.formData.deviceTypeName = device.deviceTypeName
      this.formData.deviceTypeId = device.deviceTypeId

      this.formData.manufacturerName = device.manufacturerName
      this.formData.factoryId = device.manufacturerId


      this.formData.type = device.mode
    },
    cancel() {
      this.dialogVisible = false
      this.formData = {}
    },
  },
}
</script>

<style scoped>
.leftDiv {
  border-right: 1px solid #d8dce5;
  min-height: calc(100vh - 100px);
  overflow-y: auto;
  height: calc(80vh - 150px);
  position: relative;
  top: -20px;
  padding-top: 10px;
  background-color: white;
}

.leftIcon {
  border: 1px solid #dcdfe6;
  border-radius: 8px;
  width: 16px;
  height: 50px;
  line-height: 50px;
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
  position: absolute;
  right: 0;
  top: 300px;
  z-index: 2;
}

.leftIcon:hover {
  background-color: #dcdfe6;
}

.rightIcon {
  border: 1px solid #dcdfe6;
  border-radius: 8px;
  width: 16px;
  height: 50px;
  line-height: 50px;
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
  position: absolute;
  left: -10px;
  top: 280px;
  z-index: 10;
  background: white;
}

.rightIcon:hover {
  background-color: #dcdfe6;
}

.tableDiv {
  margin-top: 20px;
}
</style>
<style lang='scss' scoped>
@import "@/assets/styles/business.scss";
</style>
