<template>
  <div>
    <div v-if="!nothing" class="formDialog">
      <div class="titleBox">
        <div class="title">事件查看</div>
        <div class="subTitle">{{ formTitle }}</div>
      </div>
      <div class="dialogBox">
        <div class="boxLeft">
          <div class="infoBox">
            <div class="infoTitle">
              基础信息
            </div>
            <el-form
              :model="formParams"
              label-position="right"
              label-width="96px"
            >
              <el-row>
                <el-col :span="24">
                  <el-form-item label="隐患名称：">
                    <el-input
                      v-model="formParams.disasterName"
                      readonly
                    ></el-input>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row>
                <el-col :span="24">
                  <el-form-item label="管理处名称：">
                    <el-input
                      v-model="formParams.managementOfficeName"
                      readonly
                    ></el-input>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row>
                <el-col :span="24">
                  <el-form-item label="路线编码：">
                    <el-input
                      v-model="formParams.routeCode"
                      readonly
                    ></el-input>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row>
                <el-col :span="24">
                  <el-form-item label="路线名称：">
                    <el-input
                      v-model="formParams.routeName"
                      readonly
                    ></el-input>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row>
                <el-col :span="24">
                  <el-form-item label="灾害类型：">
                    <el-input
                      v-model="formParams.disasterType"
                      readonly
                    ></el-input>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row>
                <el-col :span="12">
                  <el-form-item label="起点经度：">
                    <el-input
                      v-model="formParams.startLongitude"
                      readonly
                    ></el-input>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="起点纬度：">
                    <el-input
                      v-model="formParams.startLatitude"
                      readonly
                    ></el-input>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row>
                <el-col :span="24">
                  <el-form-item label="坐标系：">
                    <el-input
                      v-model="formParams.coordinateSystem"
                      readonly
                    ></el-input>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row>
                <el-col :span="12">
                  <el-form-item label="状态：">
                    <el-input
                      v-model="formParams.status"
                      readonly
                    ></el-input>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="评级：">
                    <el-input
                      v-model="formParams.rating"
                      readonly
                    ></el-input>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row>
                <el-col :span="24">
                  <el-form-item label="描述：">
                    <el-input
                      v-model="formParams.description"
                      placeholder="描述信息"
                      readonly
                    ></el-input>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row>
                <el-col :span="24">
                  <el-form-item label="处理方式：">
                    <el-input
                      v-model="formParams.handlingMethod"
                      placeholder="处理方式"
                      readonly
                    ></el-input>
                  </el-form-item>
                </el-col>
              </el-row>
            </el-form>
          </div>
        </div>
        <div
          class="boxRight"
          v-if="formParams.redirectLink"
        >
          <el-timeline>
            <el-timeline-item
              v-for="item in formParams.redirectLink"
              :key="item.createTime"
              :timestamp="item.createTime"
              color="#4ab382"
              placement="top"
              size="large"
            >
              <el-card class="cardBox">
                <div
                  slot="header"
                  class="cardHeader"
                >
                  {{ item.stepTitle }}
                </div>
                <div class="cardBody">
                  <div class="bodyMain">
                    {{ item.stepContent }}
                  </div>
                  <div
                    class="bodyImg"
                    v-if="item.sceneImg"
                  >
                    <div class="imgTitle"><i class="el-icon-caret-bottom"></i>现场照片</div>
                    <div class="imgBox">
                      <div
                        class="imgItemBox"
                        v-for="img in item.sceneImg"
                      >
                        <el-image
                          style="width: 100%; height: 100%"
                          :src="img"
                          fit="cover"
                          :preview-src-list="item.sceneImg"
                        ></el-image>
                      </div>
                    </div>
                  </div>
                </div>
                <div class="cardFooter">
                  <p>操作人：{{ item.createBy }}</p>
                </div>
              </el-card>
            </el-timeline-item>
          </el-timeline>
        </div>
      </div>
    </div>
    <div v-else>
      <el-empty></el-empty>
    </div>
  </div>
</template>

<script>
// -------------------- 引入 --------------------
// 数据
import { getHiddenData } from './data.js'

// API
import { queryPageHidden } from '@/api/disaster/hidden/hidden'

export default {
  name: 'Hidden',
  props: {
    id: {
      default: ''
    }
  },
  // -------------------- 变量 --------------------
  data() {
    return {
      /**
       * 查询相关
       */
      queryShow: false, // 隐藏筛选显隐
      queryParams: {
        // 查询参数
        pageNum: 1, // 页码
        pageSize: 10, // 每页条数
        disasterName: '', // 隐患名称
        managementOfficeName: '', // 管理处名称
        managementOfficeId: '', // 管理处ID
        roadSegmentName: '', // 路段名称
        roadSegmentId: '', // 路段ID
        routeName: '', // 路线名称
        routeCode: '' // 路线编码
      },
      queryTime: [], // 查询时间
      queryTotal: 0, // 总条数

      /**
       * 表单相关
       */
      formParams: {
        // 表单参数
        id: '', // ID
        managementOfficeName: '', // 管理处名称
        managementOfficeId: '', // 管理处ID
        roadSegmentName: '', // 路段名称
        roadSegmentId: '', // 路段ID
        routeName: '', // 路线名称
        routeCode: '', // 路线编码
        startLongitude: '', // 起点经度
        startLatitude: '', // 起点纬度
        endLongitude: '', // 终点经度
        endLatitude: '', // 终点纬度
        redirectLink: '', // 跳转后链接
        linkAdjustmentMethod: '', // 链接调整方式
        icon: '', // 图标
        coordinateSystem: '', // 坐标系
        disasterName: '', // 名称
        disasterType: '', // 类型
        status: '', // 状态
        description: '', // 描述
        rating: '', // 评级
        handlingMethod: '' // 处理方式
      },
      formTitle: '', // 表单dialog标题
      formDialog: false, // 表单dialog显隐
      formLoading: false, // 表单加载
      nothing: false
    }
  },
  created() {
    if (this.id) {
      this.initPage(this.id)
    }
  },
  // -------------------- 方法 --------------------
  methods: {
    /**
     * 页面相关
     */
    // 初始化页面
    initPage(id) {
      if (!id) {
        this.$message.warning('未获取到事件ID')
        return
      }
      console.log('获取的id:', id)
      let res = getHiddenData(id)
      console.log('获取的值：', res)
      if (res) {
        this.formInit(res)
        this.formDialog = true
        this.nothing = false
      } else {
        this.$message.warning('未查询到数据，请检查事件ID')
        this.nothing = true
        return
      }
    },

    /**
     * 表单相关
     */

    // 初始化表单
    async formInit(item) {
      // ID赋值
      this.formTitle = `ID:${item.id}`
      // 表单赋值
      this.formParams = {
        id: item.id, // ID
        managementOfficeName: item.managementOfficeName, // 管理处名称
        managementOfficeId: item.managementOfficeId, // 管理处ID
        roadSegmentName: item.roadSegmentName, // 路段名称
        roadSegmentId: item.roadSegmentId, // 路段ID
        routeName: item.routeName, // 路线名称
        routeCode: item.routeCode, // 路线编码
        startLongitude: item.startLongitude, // 起点经度
        startLatitude: item.startLatitude, // 起点纬度
        endLongitude: item.endLongitude, // 终点经度
        endLatitude: item.endLatitude, // 终点纬度
        redirectLink: item.redirectLink, // 跳转后链接
        linkAdjustmentMethod: item.linkAdjustmentMethod, // 链接调整方式
        icon: item.icon, // 图标
        coordinateSystem: item.coordinateSystem, // 坐标系
        disasterName: item.disasterName, // 名称
        disasterType: item.disasterType, // 类型
        status: item.status, // 状态
        description: item.description, // 描述
        rating: item.rating, // 评级
        handlingMethod: item.handlingMethod // 处理方式
      }
      this.formDialog = true
    },

    // 关闭表单
    formClose() {
      this.formDialog = false
    }
  }
}
</script>

<style lang="scss" scoped>
.app-container form:first-child .el-select,
.app-container form:nth-child(2) .el-select,
.app-container form:nth-child(2) ::v-deep .el-form-item__content,
.app-container form:first-child ::v-deep .el-form-item__content {
  width: 240px;
}

.app-container
  form:first-child
  .el-form-item:last-child
  ::v-deep
  .el-form-item__content {
  width: auto;
}

.app-container {
  padding: 10px;
  background-color: #c0c0c0;
  box-sizing: border-box;
}

.formDialog {
  padding-right: 10px;
  height: calc(100vh - 110px);
  overflow-y: auto;

  .titleBox {
    height: 22px;
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: flex-start;
    margin-bottom: 10px;

    .title {
      font-size: 16px;
      margin: 0;
      color: black;
    }

    .subTitle {
      margin-left: 15px;
      font-size: 12px;
      color: #888888;
    }
  }

  .dialogBox {
    height: 100%;
    width: 100%;
    display: flex;
    flex-direction: row;

    .boxLeft {
      height: 100%;
      width: 50%;
      box-sizing: border-box;
      padding: 5px;
    }

    .boxRight {
      height: 100%;
      width: 50%;
      box-sizing: border-box;
      padding: 5px;
      overflow-y: auto;

      .cardBox {
        .cardHeader {
          font-size: 20px;
          font-weight: bold;
        }

        .cardBody {
          display: flex;
          flex-direction: column;

          .bodyMain {
            width: 100%;
            margin-bottom: 10px;
          }

          .bodyImg {
            width: 100%;
            font-size: 12px;
            color: #888888;
            display: flex;
            flex-direction: column;

            .imgTitle {
              width: 100%;
              margin-bottom: 10px;
            }

            .imgBox {
              width: 100%;
              display: flex;
              flex-direction: row;
              flex-wrap: wrap;
              align-content: flex-start;

              .imgItemBox {
                height: 100px;
                width: calc(100% / 3);
                box-sizing: border-box;
                padding: 5px;
              }
            }
          }
        }

        .cardFooter {
          font-size: 12px;
          color: #888888;
          display: flex;
          justify-content: flex-end;
        }
      }
    }
  }
}

.searchBox {
  padding: 10px;
  background: #fff;
  border-radius: 10px;
  transition: all 0.1s linear;
  display: flex;
  flex-direction: column;

  .searchMoreBox {
    min-width: 192px;
    margin-top: 10px;
    display: flex;
    align-items: center;
    flex-direction: row;
  }
}

.tableDiv {
  margin-top: 10px;
  background-color: white;
  padding-bottom: 10px;
  border-radius: 10px;
  transition: all 0.1s linear;
  display: flex;
  flex-direction: column;
}

.infoBox {
  padding: 15px 15px 0 15px;
  box-sizing: border-box;
  border-radius: 6px;
  border: 1px solid #c4c4c4;
  position: relative;

  .infoTitle {
    user-select: none;
    position: absolute;
    top: 0;
    left: 0;
    padding: 0 10px;
    font-size: 14px;
    line-height: 14px;
    font-weight: bold;
    transform: translateX(15px) translateY(-50%);
    background-color: white;
  }

  .imgBox {
    height: auto;
    width: 100%;
    display: flex;
    flex-wrap: wrap;
    align-content: flex-start;

    .imgItemBox {
      height: 240px;
      width: calc(100% / 3);
      box-sizing: border-box;
      padding: 10px;
      overflow-y: auto;
      display: flex;
      align-items: center;
      justify-content: center;
      position: relative;

      .imgDeleteBtn {
        position: absolute;
        z-index: 1;
        top: 10%;
        right: 10%;
      }
    }
  }
}
</style>