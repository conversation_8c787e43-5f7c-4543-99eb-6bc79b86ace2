<template>
  <div class="repair-detail-structure">
    <el-descriptions title="3.构造物" />

    <el-table
      :data="tableData"
      style="width: 100%"
    >
      <el-table-column
        prop="item1"
        label="路线编码"
      >
      </el-table-column>
      <el-table-column
        prop="item2"
        label="车道"
      >
      </el-table-column>
      <el-table-column
        prop="item3"
        label="上下行"
      >
      </el-table-column>
      <el-table-column
        prop="item4"
        label="起点桩号"
      >
      </el-table-column>
      <el-table-column
        prop="item5"
        label="终点桩号"
      >
      </el-table-column>
      <el-table-column
        prop="item6"
        label="长度"
      >
      </el-table-column>
    </el-table>
  </div>
</template>

<script>
export default {
  name: 'RepairDetailStructure',
  components:{},
  data(){
    return {
        tableData: []
    }
  },
  watch:{},
  computed:{},
  created(){},
  mounted(){
    this.tableData.length = 0
    for (let i = 0; i < 2; i++) {
        this.tableData.push({
            item1: 'G5611',
            item2: '隧道内',
            item3: '上行',
            item4: 'K160+0' ,
            item5: 'K180+0',
            item6: 20000,
        })    
    }
  },
  methods:{},
}
</script>

<style lang="scss" scoped>
.repair-detail-structure {}
</style>