<template>
  <div class="app-container">
    <el-row :gutter="20">

      <el-col :span="24">
        <!--操作按钮区开始-->
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button type="primary" icon="el-icon-plus" size="mini" @click="handleAdd"
                v-hasPermi="['patrol:laborpersoninsure:add']"
            >新增</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="danger" icon="el-icon-delete" size="mini" @click="handleDelete"
                v-hasPermi="['patrol:laborpersoninsure:remove']"
            >批量删除</el-button>
          </el-col>
        </el-row>
        <!--操作按钮区结束-->

        <!--数据表格开始-->
        <div class="tableDiv">
          <el-table v-adjust-table size="mini" style="width: 100%" :height="440"
                v-loading="loading" border :data="tableData" @selection-change="handleSelectionChange">
            <el-table-column type="selection" width="50" align="center" />
            <el-table-column fixed label="序号" type="index" width="50">
              <template v-slot="scope">
                {{ (scope.$index + (queryParams.pageNum - 1) * queryParams.pageSize)+1 }}
              </template>
            </el-table-column>
            <el-table-column label="姓名" align="center" width="100" prop="userName" />
            <el-table-column label="所属劳务单位" align="center" width="180" prop="domainName" />
            <el-table-column label="证书类型" align="center" width="120" prop="cerType" />
            <el-table-column label="证书名称" align="center" width="120" prop="cerName" />
            <el-table-column label="证书编号" align="center" width="120" prop="cerCode" />
            <el-table-column label="颁发机构" align="center" prop="cerAuthority" />
            <el-table-column label="颁发日期" align="center" prop="issueDate" width="180">
            <template slot-scope="scope">
                <span>{{ parseTime(scope.row.issueDate, '{y}-{m}-{d}') }}</span>
            </template>
            </el-table-column>
            <el-table-column label="有效期至" align="center" prop="validDate" width="180">
            <template slot-scope="scope">
                <span>{{ parseTime(scope.row.validDate, '{y}-{m}-{d}') }}</span>
            </template>
            </el-table-column>
            <el-table-column label="附件" align="center" prop="cerFile">
            <template slot-scope="scope">
                <div @click="downloadAttFile(scope.row.cerFile)" v-if="scope.row.cerFile">
                    <el-link type="primary" :underline="false" title="查看">查看</el-link>
                </div>
            </template>
            </el-table-column>
            <el-table-column label="操作" fixed="right" align="center" width="160" class-name="small-padding fixed-width">
              <template slot-scope="scope" v-if="scope.row.userId !== 1">
                <el-button size="mini" type="text" icon="el-icon-edit" @click="handleUpdate(scope.row)"
                    v-hasPermi="['patrol:laborpersoninsure:edit']"
                >修改</el-button>
                <el-button size="mini" type="text" icon="el-icon-delete" @click="handleDelete(scope.row, true)"
                    v-hasPermi="['patrol:laborpersoninsure:remove']"
                >删除</el-button>
              </template>
            </el-table-column>
          </el-table>

          <!-- <pagination
              v-show="total>0"
              :total="total"
              :page.sync="queryParams.pageNum"
              :limit.sync="queryParams.pageSize"
              @pagination="getList"
          /> -->
        </div>
        <!--数据表格结束-->
      </el-col>
    </el-row>

    <!-- 新增编辑弹窗 -->
    <el-dialog :title="title" :visible.sync="open" width="700px" append-to-body :close-on-click-modal="false">
      <el-form ref="form" :model="form" :rules="rules" label-width="100px">
        <el-col :span="12">
            <el-form-item label="证书类型" prop="cerType" :required="true">
                <el-input v-model="form.cerType" placeholder="请输入" />
            </el-form-item>
        </el-col>
        <el-col :span="12">
            <el-form-item label="证书名称" prop="cerName" :required="true">
                <el-input v-model="form.cerName" placeholder="请输入" />
            </el-form-item>
        </el-col>
        <el-col :span="12">
            <el-form-item label="证书编号" prop="cerCode" :required="true">
                <el-input v-model="form.cerCode" placeholder="请输入" />
            </el-form-item>
        </el-col>
        <el-col :span="12">
            <el-form-item label="颁发机构" prop="cerAuthority" :required="true">
                <el-input v-model="form.cerAuthority" placeholder="请输入" />
            </el-form-item>
        </el-col>
        <el-col :span="12">
            <el-form-item label="颁发日期" prop="issueDate" :required="true">
                <el-date-picker clearable v-model="form.issueDate" type="date"
                                value-format="yyyy-MM-dd" placeholder="请选择颁发日期">
                </el-date-picker>
            </el-form-item>
        </el-col>
        <el-col :span="12">
            <el-form-item label="有效期至" prop="validDate" :required="true">
                <el-date-picker clearable v-model="form.validDate" type="date"
                                value-format="yyyy-MM-dd" placeholder="请选择有效期至">
                </el-date-picker>
            </el-form-item>
        </el-col>
        <el-col>
            <el-form-item label="上传附件" prop="cerFile" :required="true">
                <file-upload ref="fileUpload" v-model="form.cerFile" :limit=1 :ownerId="attFileOwnerId"
                    @input="onAttUploadCompelte"/>
            </el-form-item>
        </el-col>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 用户导入对话框 -->
    <el-dialog :title="upload.title" :visible.sync="upload.open" width="400px" append-to-body>
      <el-upload
          ref="upload"
          :limit="1"
          accept=".xlsx, .xls"
          :headers="upload.headers"
          :action="upload.url + '?updateSupport=' + upload.updateSupport"
          :disabled="upload.isUploading"
          :on-progress="handleFileUploadProgress"
          :on-success="handleFileSuccess"
          :auto-upload="false"
          drag
      >
        <i class="el-icon-upload"></i>
        <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
        <div class="el-upload__tip text-center" slot="tip">
          <div class="el-upload__tip" slot="tip">
            <el-checkbox v-model="upload.updateSupport" /> 是否更新已经存在的用户数据
          </div>
          <span>仅允许导入xls、xlsx格式文件。</span>
          <el-link type="primary" :underline="false" style="font-size:12px;vertical-align: baseline;" @click="importTemplate">下载模板</el-link>
        </div>
      </el-upload>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitFileForm">确 定</el-button>
        <el-button @click="upload.open = false">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
  import { getListbyParam, getLaborpersoncertificate, delspersoncertificate, delLaborpersoncertificate, addLaborpersoncertificate, updateLaborpersoncertificate } from "@/api/patrol/laborpersoncertificate";
  import { getToken } from "@/utils/auth";
  import { findFiles } from '@/api/file/index.js'
  import Treeselect from "@riophae/vue-treeselect";
  import "@riophae/vue-treeselect/dist/vue-treeselect.css";

  export default {
    name: "CertificateInfo",
    components: { Treeselect },
    props:{
        domainId:{
            type:String,
            default:0
        },
        domainName: {
            type: String,
            default:''
        },
        userName: {
            type: String,
            default:''
        },
        userId:{
            type: String,
            default:''
        }
    },
    data() {
      return {
        // 遮罩层
        loading: false,
        // 选中数组
        ids: [],
        // 非单个禁用
        single: true,
        // 非多个禁用
        multiple: true,
        // 显示搜索条件
        showSearch: false,
        dictType:[],
        // 总条数
        total: 0,
        // 弹出层标题
        title: "",
        // 是否显示弹出层
        open: false,
        //附件的唯一标识
        attFileOwnerId: 0,
        //持证列表
        tableData: [],
        // 表单参数
        form: {},
        defaultProps: {
          children: "children",
          label: "label"
        },
        // 用户导入参数
        upload: {
          // 是否显示弹出层（用户导入）
          open: false,
          // 弹出层标题（用户导入）
          title: "",
          // 是否禁用上传
          isUploading: false,
          // 是否更新已经存在的用户数据
          updateSupport: 0,
          // 设置上传的请求头部
          headers: { Authorization: "Bearer " + getToken() },
          // 上传的地址
          url: process.env.VUE_APP_BASE_API + "/system/user/importData"
        },
        // 查询参数
        queryParams: {
          pageNum: 1,
          pageSize: 100,
          userId: ""
        },
        // 列信息
        columns: [
        ],
        // 表单校验
        rules: {
          issueDate: [{ required: true, message: '请选择颁发日期', trigger: 'change' },{ validator: this.validStartEndDate, trigger: 'change' }],
          validDate: [{ required: true, message: '请选择有效期至', trigger: 'change' },{ validator: this.validStartEndDate, trigger: 'change' }],
        }
      };
    },
    watch: {
      domainId: {
        immediate: true,
        handler(newVal){

        }
      },
      userId:{
        immediate: true,
        handler(newVal) {
          this.queryParams.userId = newVal;
          this.form.userId = newVal;
          this.handleQuery()
        }
      }
    },
    created() {
    //    this.getList();
    },
    computed: {

    },
    methods: {
      /** 查询用户列表 */
      getList() {
        this.loading = true;
        getListbyParam(this.queryParams).then(response => {
          this.tableData = response.rows;
          this.total = response.total;
          this.loading = false;
        });
      },
      // 取消按钮
      cancel() {
        this.open = false;
        this.reset();
      },
      // 表单重置
      reset() {
        this.form = {
            cerType: null,
            cerName: null,
            cerCode: null,
            cerAuthority: null,
            issueDate: null,
            validDate: null,
            cerFile: null
        };
        this.resetForm("form");
      },
      /** 搜索按钮操作 */
      handleQuery() {
        // this.queryParams.pageNum = 1;
        this.getList();
      },
      /** 重置按钮操作 */
      resetQuery() {
        this.dateRange = [];
        this.resetForm("queryForm");
        this.handleQuery();
      },
      // 多选框选中数据
      handleSelectionChange(selection) {
        this.ids = selection.map(item => item.id);
        this.single = selection.length != 1;
        this.multiple = !selection.length;
      },
      //获取附件的唯一id
      getAttfileOwnerId(){
        this.attFileOwnerId = new Date().getTime();
      },
      /** 新增按钮操作 */
      handleAdd() {
        this.getAttfileOwnerId();
        this.reset();
        this.open = true;
        this.title = "新增";
        this.form.domainName = this.$props.domainName;
        this.form.domainId = this.$props.domainId;
        this.form.userId = this.$props.userId;
      },
      /** 修改按钮操作 */
      handleUpdate(row) {
        this.getAttfileOwnerId();
        this.reset();
        const id = row.id || this.ids;
        getLaborpersoncertificate(id).then(response => {
            this.form = response.data;
            this.open = true;
            this.title = "修改";
        });
      },
      validStartEndDate(rule, value, callback){
        if (new Date(this.form.issueDate).getTime() > new Date(this.form.validDate).getTime()) {
            callback(new Error("有效期至应该大于颁发日期"))
        }else {
            callback()
        }
      },
      /** 提交按钮 */
      submitForm: function() {
        this.$refs["form"].validate(valid => {
          if (valid) {
            this.form.labisureType = this.$props.labisureType;
            if (this.form.id != null) {
                updateLaborpersoncertificate(this.form).then(response => {
                  this.$modal.msgSuccess("修改成功");
                  this.open = false;
                  this.getList();
              });
            } else {
                addLaborpersoncertificate(this.form).then(response => {
                  this.$modal.msgSuccess("新增成功");
                  this.open = false;
                  this.getList();
              });
            }
          }
        });
      },
      /** 删除按钮操作 */
      handleDelete(row, isSingle) {
        let selectIds = [];
        if (isSingle) {
          selectIds.push(row.id)
        } else {
          selectIds = this.ids;
        }

        if (selectIds === null || selectIds.length === 0){
          this.$modal.msgSuccess("请选择至少一条数据");
          return;
        }

        let idStr = selectIds.join(",")
        this.$modal.confirm('是否确认删除劳务人员参保情况编号为"' + idStr + '"的数据项？').then(function() {
          return delspersoncertificate(selectIds);
        }).then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        }).catch(() => {});
      },

      /** 导出按钮操作 */
      handleExport() {
        this.download('manager/laborpersoninsure/export', {
        ...this.queryParams
        }, `laborpersoninsure_${new Date().getTime()}.xlsx`)
      },
      /** 导入按钮操作 */
      handleImport() {
        this.upload.title = "用户导入";
        this.upload.open = true;
      },
      /** 下载模板操作 */
      importTemplate() {
        this.download('manager/laborpersoninsure/importTemplate', {
        }, `劳务人员保险模板.xlsx`)
      },
      // 文件上传中处理
      handleFileUploadProgress(event, file, fileList) {
        this.upload.isUploading = true;
      },
      // 文件上传成功处理
      handleFileSuccess(response, file, fileList) {
        this.upload.open = false;
        this.upload.isUploading = false;
        this.$refs.upload.clearFiles();
        this.$alert("<div style='overflow: auto;overflow-x: hidden;max-height: 70vh;padding: 10px 20px 0;'>" + response.msg + "</div>", "导入结果", { dangerouslyUseHTMLString: true });
        this.getList();
      },
      // 提交上传文件
      submitFileForm() {
        this.$refs.upload.submit();
      },
      //下载附件
      downloadAttFile(ownerId){
        findFiles({ ownerId }).then(res => {
          let file = res.data[0];
          const url = file.url || (file.response && file.response.url)
          if (url) {
            fetch(url)
              .then((response) => response.blob())
              .then((blob) => {
                const link = document.createElement('a')
                link.href = URL.createObjectURL(blob)
                link.download = file.originalFilename || 'download'
                document.body.appendChild(link)
                link.click()
                document.body.removeChild(link)
              })
              .catch(() => {
                this.$message.error('文件无法下载，未找到文件的URL')
              })
          } else {
            this.$message.error('文件无法下载，未找到文件的URL')
          }
        });
      },
      //附件上传完成
      onAttUploadCompelte(val){
        //返回的是一个数组
        //附件存储的是一个数字的字符串，下载需要调findFiles接口获取信息
        this.form.cerFile = val[0];
      }
  }
  };
</script>
<style scoped>
  .hasTagsView .app-main[data-v-078753dd]{
    background: #f5f7fa;
  }

  .tableDiv{
    background-color: white;
    padding-bottom: 10px;
  }

  .custom-tree-node{
      display: flex;
      align-items: center;
      width: 100%;
      justify-content: space-between;
      font-size:14px;
  }

  .leftDiv{
    border-right: 1px solid #d8dce5;
    min-height: calc(100vh - 110px);
    overflow-y: auto;
    height: calc(80vh - 110px);
    position: relative;
    top: -20px;
    padding-top: 10px;
    background-color: white;
  }

  .leftIcon{
    border: 1px solid #DCDFE6;
    border-radius: 8px;
    width: 16px;
    height: 50px;
    line-height: 50px;
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
    position: absolute;
    right: 0;
    top: 300px;
    z-index: 2;
  }
  .leftIcon:hover{
    background-color: #DCDFE6;
  }

  .rightIcon{
    border: 1px solid #DCDFE6;
    border-radius: 8px;
    width: 16px;
    height: 50px;
    line-height: 50px;
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
    position: absolute;
    left: -10px;
    top: 280px;
    z-index: 10;
    background: white;
  }
  .rightIcon:hover{
    background-color: #DCDFE6;
  }

  .txt_height{
    height:30px
  }

</style>
