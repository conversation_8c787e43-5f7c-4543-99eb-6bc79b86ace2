<template>
	<PageContainer>
		<template slot="search">
			<div style="display: flex; align-items: center; margin: 0">
				<CascadeSelection
					style="min-width: 192px"
					:form-data="queryParams"
					v-model="queryParams"
					types="201"
					multiple
				/>

				<el-select
					style="margin: 0 20px"
					v-model="queryParams.typeId"
					placeholder="资产名称"
					clearable
					@change="getDynamicSelect"
				>
					<el-option
						v-for="dict in assetSubclassList"
						:key="dict.id"
						:label="dict.typeName"
						:value="dict.id"
					/>
				</el-select>

				<div style="min-width: 220px; height: 32px">
					<el-button
						v-hasPermi="['baseData:facility:getStatistics']"
						type="primary"
						icon="el-icon-search"
						class="mb8"
						@click="handleQuery"
					>
						搜索
					</el-button>
					<el-button icon="el-icon-refresh" class="mb8" @click="resetQuery">重置</el-button>
				</div>
			</div>
		</template>
		<template slot="header">
			<div class="button-list">
				<el-button
					v-hasPermi="['baseData:facility:export']"
					style="margin-bottom: 10px"
					type="primary"
					@click="handleExport"
				>
					数据导出
				</el-button>
			</div>
		</template>
		<template slot="body">
			<el-table
				v-adjust-table
				ref="table"
				height="100%"
				style="width: 100%"
				:header-cell-style="{ height: '36px' }"
				border
				v-loading="loading"
				:data="staticList"
				:row-class-name="tableRowClassName"
			>
				<el-table-column label="序号" type="index" width="50" align="center">
					<template v-slot="scope">
						<span v-if="scope.row.maintenanceSectionName !== '合计'">
							{{ scope.$index + (queryParams.pageNum - 1) * queryParams.pageSize + 1 }}
						</span>
					</template>
				</el-table-column>
				<el-table-column
					label="养护路段"
					align="center"
					prop="maintenanceSectionName"
					min-width="140"
					show-overflow-tooltip
				/>
				<el-table-column
					label="路线编码"
					align="center"
					prop="routeCode"
					min-width="140"
					show-overflow-tooltip
				/>
				<el-table-column
					label="路线名称"
					align="center"
					prop="routeName"
					min-width="140"
					show-overflow-tooltip
				/>
				<el-table-column
					label="资产主类"
					align="center"
					prop="assetMainTypeName"
					min-width="140"
					show-overflow-tooltip
				></el-table-column>

				<el-table-column
					label="资产子类"
					align="center"
					prop="assetTypeName"
					min-width="140"
					show-overflow-tooltip
				/>

				<el-table-column
					label="统计数据参数"
					align="center"
					prop="statisticsValue"
					min-width="140"
					show-overflow-tooltip
				>
					<template slot-scope="{ row }" v-if="row.statisticsValue">
						<el-link
							v-if="row.maintenanceSectionName === '合计' && !queryParams.typeId"
							:underline="false"
							style="pointer-events: none"
						>
							{{ row.statisticsValue + row.statisticsUnit }}
						</el-link>
						<el-link v-else type="primary" :underline="false" @click="handleView(row)">
							{{ row.statisticsValue + row.statisticsUnit }}
						</el-link>
					</template>
				</el-table-column>
			</el-table>
			<pagination
				:total="total"
				:page.sync="queryParams.pageNum"
				:limit.sync="queryParams.pageSize"
				:pageSizes="[10, 20, 30, 50, 100, 1000]"
				@pagination="getList"
			/>
		</template>
		<Protections
			v-if="showDetail"
			:routeCode="routeCode"
			:typeId="typeId"
			:showDetail="showDetail"
			:maintenanceSectionId="maintenanceSectionId"
			:managementMaintenanceBranchId="managementMaintenanceBranchId"
			:managementMaintenanceBranchIds="managementMaintenanceBranchIds"
			:maintenanceSectionIds="maintenanceSectionIds"
			:routeCodes="routeCodes"
			@close="
				() => {
					showDetail = false
				}
			"
		/>
	</PageContainer>
</template>

<script>
import { listFacilityStatisticsRecords } from '@/api/baseData/afforest/statistic/index'
import Protections from './components/protections.vue'
import { getAssetSubclass } from '@/api/baseData/facility/baseInfo/index'

import { getToken } from '@/utils/auth'
import CascadeSelection from '@/components/CascadeSelection/index.vue'

export default {
	name: 'Statistics',
	components: { CascadeSelection, Protections },
	dicts: ['sys_asset_detail_type', 'sys_build_status'],

	data() {
		return {
			loading: true,
			title: '',
			assetSubclassList: [],
			ids: [],
			single: true,
			multiple: true,
			total: 0,
			staticList: null,
			typeId: '',
			routeCode: '',
			maintenanceSectionId: '',
			managementMaintenanceBranchId: '',
			managementMaintenanceBranchIds: [],
			maintenanceSectionIds: [],
			routeCodes: [],
			showDetail: false,
			queryParams: {
				pageNum: 1,
				pageSize: 20,
				mainTypeId: 6,
			},
		}
	},
	watch: {
		// queryParams: {
		// 	handler(newVal, oldVal) {
		// 		if (newVal.managementMaintenanceBranchId) {
		// 			this.getList()
		// 		}
		// 	},
		// 	deep: true,
		// },
	},
	created() {
		this.getList()
		this.getAssetSubclassList()
		this.getOptions()
	},
	methods: {
		// 获取搜索栏相关字典数据
		getOptions() {},
		//查询字长子类
		getAssetSubclassList() {
			getAssetSubclass({ mainTypeId: 6 }).then((res) => {
				this.assetSubclassList = res
			})
		},
		getDynamicSelect(val) {
			let data = this.assetSubclassList.find((item) => item.id == this.queryParams.typeId)
			this.queryParams.mainTypeId = data.mainType ? data.mainType : 6
			this.getList()
		},

		// 数据导出按钮
		handleExport() {
			if (this.ids.length === 0) {
				this.$modal
					.confirm('即将导出所有表格数据，此过程可能花费时间较长，是否继续？')
					.then(() => {
						this.download(
							'/baseData/facility/statistics/export',
							this.queryParams,
							`road_interflow_${new Date().getTime()}.xlsx`,
							{
								headers: { 'Content-Type': 'application/json;' },
								parameterType: 'body',
							}
						)
					})
					.catch(() => {})
			} else {
				this.$modal
					.confirm(`已选择${this.ids.length}条数据，确认导出数据？`)
					.then(() => {
						this.download(
							'/baseData/facility/statistics/export',
							{ ids: this.ids, mainTypeId: 6 },
							`static_${new Date().getTime()}.xlsx`,
							{
								headers: { 'Content-Type': 'application/json;' },
								parameterType: 'body',
							}
						)
					})
					.catch(() => {})
			}
		},

		// 获取表格数据
		getList() {
			this.loading = true

			listFacilityStatisticsRecords(this.queryParams).then((response) => {
				if (response.rows.length > 0 && this.queryParams.typeId) {
					let sum = 0
					let maintenanceSectionIds = []
					let routeCodes = []
					response.rows.map((el) => {
						sum += Number(el.statisticsValue)
						if (el.maintenanceSectionId) {
							maintenanceSectionIds.push(el.maintenanceSectionId)
						}
						if (el.routeCode) {
							routeCodes.push(el.routeCode)
						}
					})
					const formattedSum = sum.toFixed(3).replace(/\.?0+$/, '');
					response.rows.push({
						maintenanceSectionName: '合计',
						maintenanceSectionIds: maintenanceSectionIds,
						routeCodes: routeCodes,
						typeId: response.rows[0].typeId || this.queryParams.typeId,
						statisticsUnit: response.rows[0].statisticsUnit || 'km',
						statisticsValue: formattedSum,
					})
				}
				this.staticList = response.rows
				this.total = response.total
				this.loading = false
			})
		},
		// 多选框选中数据
		handleSelectionChange(selection) {
			this.ids = selection.map((item) => item.id)
			this.single = selection.length != 1
			this.multiple = !selection.length
		},
		// 勾选高亮
		rowStyle({ row, rowIndex }) {
			if (this.ids.includes(row.id)) {
				return { 'background-color': '#b7daff', color: '#333' }
			} else {
				return { 'background-color': '#fff', color: '#333' }
			}
		},
		// 搜索按钮
		handleQuery() {
			this.queryParams.pageNum = 1
			this.getList()
		},
		// 重置按钮
		resetQuery() {
			this.dateRange = []
			this.queryParams = {
				pageNum: 1,
				pageSize: 20,
				mainTypeId: 6,
			}
			this.handleQuery()
		},
		// 表单重置
		reset() {
			this.resetForm('form')
		},

		closeImportAdd(v) {
			this.showImportAdd = false
			if (v) this.getList()
		},
		handleDateChange() {
			this.queryParams.constructionStartDate = this.dateRange[0]
			this.queryParams.constructionEndDate = this.dateRange[1]
		},
		// 表格点击勾选
		handleRowClick(row) {
			row.isSelected = !row.isSelected
			this.$refs.table.toggleRowSelection(row)
		},
		handleView(row) {
			this.showDetail = true
			this.routeCode = row.routeCode
			this.maintenanceSectionId = row.maintenanceSectionId
			this.typeId = row.typeId
			this.managementMaintenanceBranchId = this.queryParams.managementMaintenanceBranchId
			this.managementMaintenanceBranchIds = this.queryParams.managementMaintenanceBranchIds
			if (row.maintenanceSectionName === '合计') {
				this.maintenanceSectionId = undefined
				this.routeCode = undefined
				this.maintenanceSectionIds = row.maintenanceSectionIds
				this.routeCodes = row.routeCodes
			}
		},
		tableRowClassName(row_params) {
			let { row } = row_params
			if (row.maintenanceSectionName == '合计') {
				return `tr-fixed fixed-row`
			} else {
				return ``
			}
		},
	},
}
</script>

<style lang="scss" scoped>
::v-deep .el-table {
	.tr-fixed {
		display: table-row;
		position: sticky;
		bottom: 0;
		width: 100%;
		td {
			border-top: 1px solid #f3f5fa;
			background: #fff;
		}
	}
	.fixed-row {
		bottom: 0;
	}
}
</style>
