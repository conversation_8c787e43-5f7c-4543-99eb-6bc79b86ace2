<template>
  <div class="app-container maindiv">
    <el-row :gutter="20" style="display: flex">
      <!--部门数据-->
      <maintenance-tree
        @rowClick="handleNodeClick"
        :realNav="realNav"
        @closeNav="realNav = false"
      ></maintenance-tree>
      <!--角色数据-->
      <el-col :span="realNav ? 19 : 24" :xs="24">
        <!--展开图标-->
        <div class="rightIcon" @click="realNav = true" v-show="!realNav">
          <span class="el-icon-caret-right"></span>
        </div>
        <el-row>
          <el-col :span="24" :xs="24">
            <el-row>
              <el-form
                ref="queryForm"
                :model="queryParams"
                size="mini"
                :inline="true"
                label-width="68px"
              >
                <el-form-item label="">
                  <el-date-picker
                    style="width: 240px"
                    v-model="queryParams.year"
                    type="year"
                    value-format="yyyy"
                    placeholder="年份"
                  >
                  </el-date-picker>
                </el-form-item>
                <el-form-item>
                  <dict-select type="project_clac_middle_status" style="width: 240px" placeholder="计量状态" v-model="queryParams.status"></dict-select>
                </el-form-item>
                <el-form-item>
                  <selectTree
                    :key="'domainId'"
                    style="width: 240px"
                    v-model="queryParams.domainId"
                    :deptType="100"
                    :deptTypeList="[1, 3, 4]"
                    placeholder="管养单位"
                    clearable
                    filterable
                  />
                </el-form-item>
                <el-form-item>
                  <selectTree
                    :key="'constructionUnit'"
                    style="width: 240px"
                    v-model="queryParams.calcDomainId" :data-rule="false"
                    :dept-type="100"
                    placeholder="施工单位"
                    :filter-keys="['云南省交通投资建设集团有限公司', '云南交投投资有限公司']"
                    :expand-all="false"
                    clearable
                    filterable
                  />
                </el-form-item>
                <el-form-item>
                  <RoadSection
                    v-model="queryParams.maiSecId"
                    :deptId="queryParams.domainId"
                    placeholder="路段"
                  />
                </el-form-item>
                <el-form-item>
                  <el-button
                    type="primary"
                    icon="el-icon-search"
                    size="mini"
                    @click="handleQuery"
                    >搜索</el-button
                  >
                  <el-button
                    icon="el-icon-refresh"
                    size="mini"
                    @click="resetQuery"
                    >重置</el-button
                  >
                  <el-button
                    v-show="!showSearch"
                    @click="showSearch = true"
                    icon="el-icon-arrow-down"
                    circle
                  ></el-button>
                  <el-button
                    v-show="showSearch"
                    @click="showSearch = false"
                    icon="el-icon-arrow-up"
                    circle
                  ></el-button>
                </el-form-item>
              </el-form>
              <!--默认折叠-->
              <el-col :span="24">
                <el-form
                  :model="queryParams"
                  ref="queryForm"
                  size="small"
                  :inline="true"
                  v-show="showSearch"
                  label-width="68px"
                >
                  <el-form-item>
                    <el-input
                      style="width: 240px"
                      placeholder="中间计量名称"
                      v-model="queryParams.name"
                    ></el-input>
                  </el-form-item>
                  <el-form-item>
                    <el-input
                      style="width: 240px"
                      placeholder="中间计量编号"
                      v-model="queryParams.code"
                    ></el-input>
                  </el-form-item>
                  <el-form-item>
                    <el-input
                      style="width: 240px"
                      placeholder="任务单编号"
                      v-model="queryParams.constructionCode"
                    ></el-input>
                  </el-form-item>
                  <el-form-item>
                    <el-input
                      style="width: 240px"
                      placeholder="结算计量单编码"
                      v-model="queryParams.settleCode"
                    ></el-input>
                  </el-form-item>
                </el-form>
              </el-col>
            </el-row>
          </el-col>
        </el-row>
        <!--操作按钮区开始-->
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button
              type="primary"
              icon="el-icon-plus"
              size="mini"
              v-has-menu-permi="['calctheft:intermediate:add']"
              @click="handleAdd"
              >新增
            </el-button>
          </el-col>
          <el-col :span="1.5"
          ><el-button
            type="success"
            v-has-menu-permi="['calctheft:intermediate:reportpreview']"
            @click="handlePreview"
            icon="el-icon-view"
          >报表预览</el-button
          ></el-col
          >
          <el-col :span="1.5"
          ><el-button
            type="primary"
            v-has-menu-permi="['calctheft:intermediate:reportdownload']"
            @click="handleDownload"
            icon="el-icon-download"
          >报表下载</el-button
          ></el-col
          >
          <el-col :span="1.5">
            <el-button
              type="warning"
              icon="el-icon-download"
              size="mini"
              v-has-menu-permi="['calctheft:intermediate:export']"
              @click="exportList"
              >导出清单
            </el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button
              type="warning"
              icon="el-icon-view"
              size="mini"
              v-has-permi="['settlement:repository:regenerate']"
              @click="handleRegenerate"
            >重新生成报表
            </el-button>
          </el-col>
          <right-toolbar
            :showSearch.sync="showSearch"
            @queryTable="handleQuery"
            :columns="columns"
          ></right-toolbar>
        </el-row>
        <el-row>
          <div class="draggable">
            <el-table v-adjust-table
              size="mini"
              style="width: 100%"
              v-loading="loading"
              border
              :data="tableData"
              row-key="id"
              ref="dataTable"
              stripe
              highlight-current-row
              @row-click="handleClickRow"
              @selection-change="handleSelectionChange"
              :height="
                showSearch ? 'calc(100vh - 330px)' : 'calc(100vh - 270px)'
              "
            >
              <el-table-column type="selection" width="50" align="center"/>
              <el-table-column
                label="序号"
                align="center"
                type="index"
                width="50"
              />
              <template v-for="(column, index) in columns">
                <el-table-column
                  :label="column.label"
                  v-if="column.visible"
                  align="center"
                  :prop="column.field"
                  :width="column.width"
                >
                  <template slot-scope="scope">
                    <dict-tag
                      v-if="column.dict"
                      :options="dict.type[column.dict]"
                      :value="scope.row[column.field]"
                    />
                    <template v-else-if="column.slots">
                      <RenderDom
                        :row="scope.row"
                        :index="index"
                        :render="column.render"
                      />
                    </template>
                    <span v-else-if="column.isTime">{{
                      parseTime(scope.row[column.field], "{y}-{m}-{d}")
                    }}</span>
                    <span v-else>{{ scope.row[column.field] }}</span>
                  </template>
                </el-table-column>
              </template>
              <el-table-column
                label="操作"
                fixed="right"
                align="center"
                width="250"
                class-name="small-padding fixed-width"
              >
                <template slot-scope="scope">
                  <el-button
                    size="mini"
                    type="text"
                    icon="el-icon-delete"
                    :disabled="scope.row.status != 0"
                    v-has-menu-permi="['calctheft:intermediate:remove']"
                    @click="handleDelete(scope.row)"
                    >删除
                  </el-button>
                  <el-button
                    size="mini"
                    type="text"
                    icon="el-icon-edit"
                    :disabled="scope.row.status != 0"
                    v-has-menu-permi="['calctheft:intermediate:edit']"
                    @click="handleEdit(scope.row)"
                    >修改
                  </el-button>
                  <el-dropdown size="mini">
                    <el-button
                      size="mini"
                      type="text"
                      icon="el-icon-d-arrow-right"
                      >更多</el-button
                    >
                    <el-dropdown-menu slot="dropdown">
                      <el-dropdown-item
                        ><el-button
                          type="text"
                          @click="showInfo(scope.row)"
                          icon="el-icon-view"
                          >查看明细</el-button
                        ></el-dropdown-item
                      >
                      <el-dropdown-item
                        ><el-button
                          type="text"
                          v-has-menu-permi="['theft:detail:add']"
                          @click="openAddInfo(scope.row)"
                          icon="el-icon-plus"
                          :disabled="scope.row.status != 0"
                          >添加明细</el-button
                        ></el-dropdown-item
                      >
                      <el-dropdown-item
                        ><el-button
                          type="text"
                          v-has-menu-permi="['calctheft:intermediate:process']"
                          @click="handleOpenSubmit(scope.row)"
                          icon="el-icon-check"
                          :disabled="scope.row.status != 0"
                          >提交</el-button
                        ></el-dropdown-item
                      >
                      <el-dropdown-item
                        ><el-button
                          type="text"
                          icon="el-icon-refresh-left"
                          @click="updateFund(scope.row)"
                          v-has-menu-permi="['calctheft:intermediate:updateFund']"
                          :disabled="scope.row.status != 0"
                          >更新费用</el-button
                        ></el-dropdown-item
                      >
                    </el-dropdown-menu>
                  </el-dropdown>
                </template>
              </el-table-column>
            </el-table>
            <pagination
              v-show="total > 0"
              :total="total"
              :page.sync="queryParams.pageNum"
              :limit.sync="queryParams.pageSize"
              @pagination="handleQuery"
            />
          </div>
        </el-row>
      </el-col>
    </el-row>

    <el-dialog
      title="明细查看"
      append-to-body
      modal-append-to-body
      destroy-on-close
      :visible.sync="infoDialog"
      width="90%"
      v-if="infoDialog"
    >
      <info @close="modelClose" :maiSecId="maiSecId" @query="handleQuery" :calcId="calcId"></info>
    </el-dialog>
    <el-dialog
      title="新增明细"
      :visible.sync="addInfoDialog"
      destroy-on-close
      width="90%"
      v-if="addInfoDialog"
    >
      <add-info
        @close="modelClose"
        :maiSecId="maiSecId"
        :calcId="calcId"
      ></add-info>
    </el-dialog>
    <el-dialog
      :title="drawerTitle"
      destroy-on-close
      :visible.sync="drawer"
      :close-on-click-modal="false"
      v-if="drawer"
      size="50%"
    >
      <detail @close="handleCloseDetail" :row-data="rowData"></detail>
    </el-dialog>
    <el-dialog
      title="附件列表"
      :visible.sync="openFile"
      width="500px"
      v-if="openFile"
    >
      <file-upload
        @close="modelClose"
        v-model="fileId"
        :forView="true"
      ></file-upload>
    </el-dialog>
    <IFramePreview ref="iframeRef" :srcdoc="preview.html" :down-url="preview.url" :file-name="preview.fileName"></IFramePreview>

    <el-dialog
      title="提交"
      :visible.sync="confirmDialog"
      width="30%"
      v-if="confirmDialog"
    >
      <el-form
        v-model="formData"
        size="small"
        :inline="true"
        label-width="120px"
      >
        <el-form-item label="核定计量金额">
          <el-input v-model="formData.calcFund" style="width: 230px" readonly />
        </el-form-item>
        <el-form-item label="监理费">
          <el-input v-model="formData.supFund" style="width: 230px" readonly />
        </el-form-item>
        <el-form-item label="提交意见">
          <el-input
            v-model="formData.comment"
            type="textarea"
            style="width: 230px"
          />
        </el-form-item>
      </el-form>
      <div style="text-align: right">
        <el-button size="mini" @click="confirmDialog = false">取 消</el-button>
        <el-button size="mini" type="primary" @click="handleSubmit"
          >确 定</el-button
        >
      </div>
    </el-dialog>
  </div>
</template>

<script>
import Detail from "./detail.vue";
import EventInfo from "../component/eventTreeInfo.vue";
import selectTree from "@/components/DeptTmpl/selectTree.vue";
import MaintenanceTree from "@/components/MaintenanceTree/index.vue";
import {
  listDaliyIntermediate,
  deleteDaliyIntermediate,
  middleProcess, previewIntermediateReport, downloadIntermediateReport,updateFund
} from "@/api/calculate/theft/middleApplication";
import info from "./info.vue";
import addInfo from "./addInfo.vue";
import RoadSection from "@/views/baseData/components/roadSection/index.vue";
import Luckysheet from "@/components/Luckysheet/index.vue";
import IFramePreview from "@/components/IFramePreview/index.vue";
import axios from "axios";
import {regenerateReport} from "@/api/dailyMaintenance/metering/addPrice";

export default {
  name: 'MiddleApplication',
  components: {
    IFramePreview,
    MaintenanceTree,
    Luckysheet,
    RoadSection,
    selectTree,
    EventInfo,
    Detail,
    info,
    addInfo,
    RenderDom: {
      functional: true,
      props: {
        row: Object,
        index: Number,
        render: Function,
      },
      render(createElement, ctx) {
        const { row, index } = ctx.props;
        return ctx.props.render(row, index);
      },
    },
  },
  dicts: ['project_clac_middle_status'],
  props: [],
  data() {
    return {
      realNav: true,
      showSearch: false,
      queryParams: {
        pageNum: 1,
        pageSize: 50,
      },
      total: 0,
      loading: false,
      preview: {
        html: '',
        url: '',
        fileName: ''
      },
      columns: [
        {
          key: 0,
          width: 150,
          field: "status",
          label: `状态`,
          visible: true,
          dict: "project_clac_middle_status",
        },
        {
          key: 2,
          width: 200,
          field: "name",
          label: `中间计量名称`,
          visible: true,
        },
        {
          key: 3,
          width: 200,
          field: "code",
          label: `中间计量编号`,
          visible: true,
        },
        {
          key: 4,
          width: 100,
          field: "domainName",
          label: `管养单位`,
          visible: true,
        },
        {
          key: 5,
          width: 200,
          field: "calcDomainName",
          label: `申请计量单位`,
          visible: true,
        },
        {
          key: 6,
          width: 100,
          field: "maiSecId",
          label: `路段名称`,
          visible: true,
        },
        {key: 7, width: 200, field: 'conName', label: `合同名称`, visible: true},
        {
          key: 7,
          width: 100,
          field: "calcFund",
          label: `核定计量金额`,
          visible: true,
        },
        {
          key: 8,
          width: 100,
          field: "sumFund",
          label: `基本费用`,
          visible: true,
        },
        {
          key: 9,
          width: 100,
          field: "productionFund",
          label: `安全生产费`,
          visible: true,
        },
        {
          key: 10,
          width: 100,
          field: "guaranteeFund",
          label: `安全保通费`,
          visible: true,
        },
        {
          key: 11,
          width: 100,
          field: "supFund",
          label: `监理费`,
          visible: true,
        },

        {
          key: 12,
          width: 100,
          field: "calcDate",
          label: `计量日期`,
          visible: true,
        },
        {
          key: 17,
          width: 100,
          field: "fileId",
          label: `附件`,
          visible: true,
          slots: true,
          render: (row, index) => {
            return (
              <el-button
                size="mini"
                disabled={!row.fileId}
                type="text"
                onClick={(e) => this.handleOpenFile(e, row)}
              >
                查看
              </el-button>
            );
          },
        },
      ],
      tableData: [],
      rowData: {},
      drawerTitle: "中间计量申请",
      fileId: "",
      drawer: false,
      openFile: false,
      infoDialog: false,
      addInfoDialog: false,
      maiSecId: "",
      calcId: "",
      confirmDialog: false,
      formData: {},
      row: {},
      ids: []
    };
  },
  computed: {},
  watch: {},
  created() {
    this.handleQuery();
  },
  mounted() {},
  methods: {
    handleNodeClick(e) {
      this.queryParams.domainId = e.domainId || "";
      this.queryParams.maiSecId = e.maiSecId || "";
      this.handleQuery();
    },
    handleQuery() {
      this.loading = true;
      if (this.queryParams.calcDate) {
        this.queryParams.startCalcDate = this.queryParams.calcDate[0];
        this.queryParams.endCalcDate = this.queryParams.calcDate[1];
      }
      listDaliyIntermediate(this.queryParams).then((res) => {
        this.loading = false;
        this.tableData = res.rows;
        this.total = res.total;
      });
    },
    resetQuery() {
      this.queryParams = {
        pageNum: 1,
        pageSize: 50,
      };
      this.handleQuery()
    },
    handleCloseDetail() {
      this.rowData = {};
      this.drawer = false;
      this.handleQuery();
    },
    handleEdit(row) {
      this.rowData = row;
      this.drawer = true;
    },
    handleDelete(row) {
      this.$modal.confirm("是否确认删除").then(() => {
        this.loading = true;
        deleteDaliyIntermediate(row.id).then(() => {
          this.$modal.msgSuccess("删除成功");
          this.handleQuery();
        });
      });
    },
    handleOpenFile(e, row) {
      this.fileId = "";
      this.openFile = true;
      this.fileId = row.fileId;
    },
    handleAdd() {
      this.rowData = {};
      this.drawer = true;
    },
    showInfo(row) {
      this.infoDialog = true;
      this.maiSecId = row.maiSecId;
      this.calcId = row.id;
    },
    openAddInfo(row) {
      this.addInfoDialog = true;
      this.maiSecId = row.maiSecId;
      this.calcId = row.id;
    },
    handleClickRow(row) {
      this.row = row;
    },
    handleOpenSubmit(row) {
      this.formData = {
        businessKey: row.id,
        taskId: row.taskId,
        approved: true,
        supFund: row.supFund,
        calcFund: row.calcFund,
      };
      this.confirmDialog = true;
    },
    // 提交
    handleSubmit() {
      middleProcess(this.formData).then((res) => {
        this.$modal.msgSuccess("提交成功");
        this.confirmDialog = false;
        this.handleQuery();
      });
    },
    // 导出清单按钮
    exportList() {
      this.download(
        "manager/calc/theft/intermediate/export",
        { ...this.queryParams },
        `intermediate_${new Date().getTime()}.xlsx`,
        {
          headers: { "Content-Type": "application/json;" },
          parameterType: "body",
        }
      );
    },
    // 报表预览
    handlePreview(row) {
      if (!this.row.id) {
        this.$modal.msgWarning('请选择一条数据')
        return
      }
      this.loading = true
      previewIntermediateReport(this.row.id).then(res => {
        if (res.code == 200){
          this.preview.html = res.data.html
          this.preview.url = res.data.downUrl
          this.preview.fileName = res.data.fileName
          this.$refs.iframeRef.visible = true
        }
        this.loading = false
      })
    },
    // 报表下载
    handleDownload(row) {
      if (!this.row.id) {
        this.$modal.msgWarning('请选择一条数据')
        return
      }
      this.loading = true
      downloadIntermediateReport(this.row.id).then(res => {
        const url = res.data.downUrl
        const fileName = res.data.fileName
        if (res.code == 200) {
          if (res.data.fileName.endsWith('.zip')) {
            let link = document.createElement('a')
            link.download = res.data.fileName
            link.style.display = 'none'
            link.href = res.data.downUrl
            document.body.appendChild(link)
            link.click()
            document.body.removeChild(link)
            this.loading = false
          } else {
            axios({
              method: "get",
              responseType: 'arraybuffer',
              url: res.data.downUrl,
              headers: {}
            }).then((blobRes) => {
              const arrayBuffer = blobRes.data;
              // 创建一个Blob对象
              const blob = new Blob([arrayBuffer], {type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'}); // 对于.xls文件
              saveAs(blob, res.data.fileName)
            }).finally(() => {
              this.loading = false
            })
          }
        }
      })
    },
    updateFund(row) {
      this.$modal.confirm('是否确定更新费用').then(() => {
        this.loading = true
        updateFund(row.id).then(res => {
          this.handleQuery()
        })
      })
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection
    },
    handleRegenerate() {
      if (this.ids.length == 0) {
        this.$modal.msgError("请勾选至少一条数据")
        return
      }
      const params = {
        idList: this.ids.map(item => item.id),
        type: 8
      }
      regenerateReport(params).then(res => {
        this.$modal.msgSuccess("操作成功")
      })
    },
    modelClose() {
      this.drawer = false;
      this.openFile = false;
      this.infoDialog = false;
      this.addInfoDialog = false;
      this.handleQuery()
    },
  },
};
</script>

<style lang="scss" scoped>
.rightIcon {
  border: 1px solid #dcdfe6;
  border-radius: 8px;
  width: 16px;
  height: 50px;
  line-height: 50px;
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
  position: absolute;
  left: -10px;
  top: 280px;
  z-index: 10;
  background: white;
}

.rightIcon:hover {
  background-color: #dcdfe6;
}

.left-total {
  font-size: 13px;
  line-height: 28px;
  color: #606266;
  position: absolute;
  bottom: 10px;
  right: 10px;
}
</style>
<style lang="scss" scoped>
@import "@/assets/styles/business.scss";
</style>
