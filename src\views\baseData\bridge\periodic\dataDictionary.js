// 行政识别数据
const baseInfoData = [
  {
    label: '路线编码',
    prop: 'routeCode',
    type: 'input',
    disabled: true,
    span:12,
  },

  {
    label:'路段名称',
    prop: 'maintenanceSectionName',
    type: 'input',
    span:12,
    disabled: true,
  },

  {
    label:'中心桩号',
    prop: 'centerStake',
    type: 'pileInput',
    span:12,
    disabled: true,
  },
  {
    label:'桥梁编码',
    prop: 'bridgeCode',
    type: 'input',
    rules: [{ required: true, message: '请选择桥梁', trigger: 'change' }],
    disabled: true,
    span:12,
  },
  {
    label:'桥梁名称',
    prop: 'bridgeName',
    type: 'input',
    rules: [{ required: true, message: '请选择桥梁', trigger: 'change' }],
    span:12,
    disabled: true,
  },
  {
    label:'路线名称',
    prop: 'routeName',
    type: 'input',
    span:12,
    disabled: true,
  },
  {
    label:'桥梁全长(m)',
    prop: 'totalLength',
    type: 'input',
    span:12,
    disabled: true,
  },
  {
    label:'上部结构形式',
    prop: 'mainSuperstructureType',
    type: 'select',
    dict: 'bridge_main_superstructure_type',
    span:12,
    disabled: true,
  },

  {
    label:'修建年度',
    prop: 'constructionYear',
    type: 'input',
    span:12,
    disabled: true,
  },
  {
    label:'定期检查时间',
    prop: 'checkDate',
    type: 'date',
    rules: [{ required: true, message: '请选择检查时间', trigger: 'change' }],
    span:12,
  },
  // {
  //   label:'年度',
  //   prop: 'checkYear',
  //   type: 'year',
  //   span:12,
  // },
  {
    label: '定期检查评定等级',
    prop: 'level',
    placeholder: '请选择定期检查评定等级',
    rules: [{ required: true, message: '请选择定期检查评定等级', trigger: 'change' }],
    type: 'select',
    dict: 'bridge_tec_condition_level',
    span:12,
  },
  {
    label:'评定为四五类桥的原因',
    prop: 'fourthFifthClassRatingReason',
    type: 'select',
    dict: 'bridge_four_five_evaluate_reasons',
    span:12,
  },


  {
    label:'定期检查承担单位',
    prop: 'organization',
    type: 'input',
    rules: [{ required: true, message: '请输入定期检查承担单位', trigger: 'change' }],
    span:12,

  },
  {
    label:'总体评分',
    prop: 'overallScore',
    type: 'input',
    span:12,

  },
  {
    label:'报告编制日期',
    prop: 'reportWriteDate',
    type: 'date',
    span:12,
  },
  {
    label:'报告编写人',
    prop: 'reportWriter',
    type: 'input',
    span:12,

  },
  {
    label:'检查报告',
    prop: 'reportPath',
    type: 'updataFile',
    rules: [{ required: true, message: '请选择报告', trigger: 'change' }],
    span:12,
  },


]





export default {
  baseInfoData,

}
