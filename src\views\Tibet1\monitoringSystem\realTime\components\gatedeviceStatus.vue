<template>
  <div class="tableDiv">
    <el-table v-adjust-table v-loading="loading" :data="dataList" height="100%" size="mini" style="width: 100%">
      <template v-for="(column, index) in columns">
        <el-table-column v-if="column.visible" :key="index" :label="column.label" :prop="column.field" align="center"
          show-overflow-tooltip>
          <template slot-scope="scope">
            <span v-if="column.field === 'deviceStatus'">{{ scope.row[column.field] == 1 ? '在线' : '离线' }}</span>
            <span v-else>{{ scope.row[column.field] }}</span>
          </template>
        </el-table-column>
      </template>
      <el-table-column fixed="right" label="操作" width="150" align="center">
        <template slot-scope="scope">
          <template v-if="scope.row.deviceStatus == 1">
            <el-button v-if="scope.row.switchStatus == 0" size="mini" type="text"
              @click="handleOpen(scope.row)">打开</el-button>
            <el-button v-else-if="scope.row.switchStatus == 1" size="mini" type="text"
              @click="handleEnd(scope.row)">关闭</el-button>
          </template>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>

<script>
import {
  getBoundDevices,
  xzSwitchCtl
} from '@/api/Tibet/index'

export default {
  name: 'equipmentonlineStatus',
  components: {
    RenderDom: {
      functional: true,
      props: {
        row: Object,
        index: Number,
        render: Function,
      },
      render(createElement, ctx) {
        const { row, index } = ctx.props;
        return ctx.props.render(row, index);
      },
    }
  },
  props: {
    checkData: {
      type: Object,
      default: () => { }
    }
  },
  data() {
    return {
      loading: false,
      dataList: [],
      columns: [
        {
          key: 0,
          field: 'aliasName',
          label: '道闸名称',
          visible: true
        },
        {
          key: 1,
          field: 'deviceId',
          label: '设备ID',
          visible: true
        },
        {
          key: 2,
          field: 'deviceStatus',
          label: '设备状态',
          visible: true
        }
      ],
    }
  },
  mounted() {
    this.fetchData();
  },
  methods: {
    // 获取数据
    fetchData() {
      let data = JSON.parse(localStorage.getItem('mapData'));
      this.getEquipmentonlineStatus(data);
    },
    async getEquipmentonlineStatus(data) {
      if (!data) return;
      try {
        this.loading = true;
        const res = await getBoundDevices({ riskId: data.id });
        this.dataList = res.data;
      } catch (error) {
        this.$message.error('获取道闸设备列表失败');
      } finally {
        this.loading = false;
      }
    },
    handleEnd(row) {
      xzSwitchCtl({ deviceId: row.deviceId, state: 0 }).then(res => {
        if (res.data.msg == "终端无响应") {
          this.$message.error(res.data.msg);
        } else {
          this.$message.success(res.data.msg);
          this.fetchData(); // 操作成功后立即刷新数据
        }
      }).catch(err => {
        this.$message.error('关闭失败');
      });
    },
    handleOpen(row) {
      xzSwitchCtl({ deviceId: row.deviceId, state: 1 }).then(res => {
        if (res.data.msg == "终端无响应") {
          this.$message.error(res.data.msg);
        } else {
          this.$message.success(res.data.msg);
          this.fetchData(); // 操作成功后立即刷新数据
        }
      }).catch(err => {
        this.$message.error('打开失败');
      });
    },
  }
}
</script>

<style lang="scss" scoped>
@import "./index.scss";

/* 闪烁动画效果 */
@keyframes blink {

  /* 开始状态：红色背景，白色文字，完全不透明 */
  0% {
    opacity: 1;
    background-color: #ffc107;
    color: white;
  }

  /* 中间状态：半透明 */
  50% {
    opacity: 0.5;
    background-color: #ff0000;
    color: white;
  }

  /* 结束状态：恢复完全不透明 */
  100% {
    opacity: 1;
    background-color: #ff0000;
    color: white;
  }
}

.blinking {
  animation: blink 1s linear infinite;
  padding: 2px 8px;
  border-radius: 4px;
  display: inline-block;
}

.tableDiv {
  width: 100%;
}

/* 可根据需要调整动画速度或颜色 */
.fast-blink {
  animation-duration: 0.5s;
}

.slow-blink {
  animation-duration: 2s;
}

.blue-blink {
  background-color: #0066ff;
}
</style>