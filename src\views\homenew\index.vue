<template>
  <div class="home-content">
    <template v-if="showHome">
      <el-row class="mb30" :gutter="30">
        <el-col :span="5">
          <div class="summary1">
            <div class="summary-top">
              <div class="summary-title">
                <img src="@/assets/homenew/road.png">
                <span>路面巡查里程/km</span>
              </div>
              <div class="summary-more" @click="routeChange('patrol/inspectionLogs')">
                （日）
              </div>
            </div>
            <div class="summary-bottom">
              <div class="summary-item">
                <div class="summary-value">
                  {{ patrolData ? patrolData.totalMileage : 0 }}
                  <span class="unite">km</span>
                </div>
                <div class="summary-name">计划</div>
              </div>
              <div class="line"></div>
              <div class="summary-item">
                <div class="summary-value">
                  {{ patrolData ? patrolData.inspectedMileage : 0 }}
                  <span class="unite">km</span>
                </div>
                <div class="summary-name">已巡查</div>
              </div>
            </div>
          </div>
        </el-col>
        <el-col :span="5">
          <div class="summary2">
            <div class="summary-top">
              <div class="summary-title">
                <img src="@/assets/homenew/bridge.png">
                <span>桥梁经常巡查/座</span>
              </div>
              <div class="summary-more" @click="routeChange('patrol/inspectionLogs')">
                （月）
              </div>
            </div>
            <div class="summary-bottom">
              <div class="summary-item">
                <div class="summary-value">
                  {{ patrolData ? patrolData.bridgeTotalCount : 0 }}
                  <span class="unite">座</span>
                </div>
                <div class="summary-name">计划</div>
              </div>
              <div class="line"></div>
              <div class="summary-item">
                <div class="summary-value">
                  {{ patrolData ? patrolData.bridgeInspectedCount : 0 }}
                  <span class="unite">座</span>
                </div>
                <div class="summary-name">已巡查</div>
              </div>
            </div>
          </div>
        </el-col>
        <el-col :span="5">
          <div class="summary3">
            <div class="summary-top">
              <div class="summary-title">
                <img src="@/assets/homenew/culvert.png">
                <span>涵洞经常巡查/座</span>
              </div>
              <div class="summary-more" @click="routeChange('patrol/inspectionLogs')">
                （月）
              </div>
            </div>
            <div class="summary-bottom">
              <div class="summary-item">
                <div class="summary-value">
                  {{ patrolData ? patrolData.culvertTotalCount : 0 }}
                  <span class="unite">座</span>
                </div>
                <div class="summary-name">计划</div>
              </div>
              <div class="line"></div>
              <div class="summary-item">
                <div class="summary-value">
                  {{ patrolData ? patrolData.culvertInspectedCount : 0 }}
                  <span class="unite">座</span>
                </div>
                <div class="summary-name">已巡查</div>
              </div>
            </div>
          </div>
        </el-col>
        <el-col :span="9">
          <div class="summary4">
            <div class="summary-top">
              <div class="summary-title">
                <img src="@/assets/homenew/tunnel.png">
                <span>隧道经常巡查/座</span>
              </div>
              <div class="summary-more" @click="routeChange('patrol/inspectionLogs')">
                （月）
              </div>
            </div>
            <div class="summary-bottom">
              <div class="summary-item">
                <div class="summary-value">
                  {{ patrolData ? patrolData.tunnelTotalCount : 0 }}
                  <span class="unite">座</span>
                </div>
                <div class="summary-name">土建计划</div>
              </div>
              <div class="line"></div>
              <div class="summary-item">
                <div class="summary-value">
                  {{ patrolData ? patrolData.tunnelInspectedCount : 0 }}
                  <span class="unite">座</span>
                </div>
                <div class="summary-name">土建已巡查</div>
              </div>
              <div class="line"></div>
              <div class="summary-item">
                <div class="summary-value">
                  {{ patrolData ? patrolData.tunnelDeviceTotalCount : 0 }}
                  <span class="unite">座</span>
                </div>
                <div class="summary-name">机电计划</div>
              </div>
              <div class="line"></div>
              <div class="summary-item">
                <div class="summary-value">
                  {{ patrolData ? patrolData.tunnelDeviceInspectedCount : 0 }}
                  <span class="unite">座</span>
                </div>
                <div class="summary-name">机电已巡查</div>
              </div>
            </div>
          </div>
        </el-col>
      </el-row>
      <el-row class="mb30" :gutter="30">
        <el-col :span="15">
          <el-card class="task-center">
            <template slot="header">
              <div class="card-title">
                <div style="display: flex;">
                  <div style="margin-right: 60px;cursor: pointer;" :class="{ bottom_line: isPlanCenter }"
                    @click="isPlanCenter = true">任务中心
                  </div>
                  <div style="cursor: pointer;" :class="{ bottom_line: !isPlanCenter }" @click="isPlanCenter = false">
                    填报任务
                  </div>
                </div>
                <el-button type="text"
                  @click="() => { isPlanCenter ? routeChange('task/NextIndex') : routeChange('repote/repote/record') }"
                  class="more">查看更多</el-button>
              </div>
            </template>
            <template v-if="isPlanCenter">
              <el-table :data="taskList" stripe header-cell-class-name="table-header"
                v-loading="loadingList.taskLoading" height="100%" border key="任务中心">
                <el-table-column align="center" prop="name" label="流程类型" show-overflow-tooltip></el-table-column>
                <el-table-column align="center" prop="category" label="审批节点" show-overflow-tooltip>
                  <template slot-scope="row">
                    <el-link :underline="false" @click="handlerClick(row.row)">
                      <i class="el-icon-user-solid"></i>
                      {{ row.row.taskname }}
                    </el-link>
                  </template>
                </el-table-column>
                <el-table-column align="center" prop="taskName" label="待办数量" show-overflow-tooltip>
                  <template slot-scope="row">
                    <el-link :underline="false" @click="handlerClick(row.row)" type="primary">
                      {{ row.row.total }}
                    </el-link>
                  </template>
                </el-table-column>
                <el-table-column align="center" label="操作" show-overflow-tooltip>
                  <template slot-scope="scope">
                    <el-button size="mini" type="text" @click="onHandler(scope.row)">
                      办理
                    </el-button>
                  </template>
                </el-table-column>
              </el-table>
            </template>
            <template v-else>
              <el-table :data="writeTaskList" stripe header-cell-class-name="table-header"
                v-loading="loadingList.writeTaskLoading" height="100%" border key="填报任务">
                <el-table-column align="center" prop="missionName" label="任务标题" show-overflow-tooltip></el-table-column>
                <el-table-column align="center" prop="createTime" label="下发时间" show-overflow-tooltip></el-table-column>
                <el-table-column align="center" prop="status" label="填报状态" show-overflow-tooltip>
                  <template v-slot="scope">
                    {{
                      scope.row.status === 1 ? '待填写' : scope.row.status === 2 ? '待提交' : '已提交'
                    }}
                  </template>
                </el-table-column>
              </el-table>
            </template>
          </el-card>
        </el-col>
        <el-col :span="9">
          <el-row class="mb30" v-if="isClose">
            <div class="leftTop1">
              <div class="textItem ">
                <span style="font-size: 24px;font-weight: 700;">您好，</span>{{ nickName }}
                <div class="textItemSpan">
                  <div>欢迎使用高速公路智慧养护管理平台</div>
                  <div style="font-size: 12px;font-weight: 400;">{{ times }}</div>
                </div>
              </div>
              <img src="@/assets/homenew/close.png" class="colse_btn" @click="isClose = false">
            </div>
          </el-row>
          <!-- 填报任务 -->
          <el-row>
            <el-card :body-style="{ height: isClose ? '170px' : '350px' }">
              <template slot="header">
                <div class="card-title">
                  <div class="bottom_line">消息通知</div>
                  <el-button type="text" @click="routeChange('system/notice')" class="more">
                    查看更多
                  </el-button>
                </div>
              </template>
              <div class="notice_list" v-loading="loadingList.noticeLoading">
                <template v-if="noticeList.length > 0">
                  <div class="notice_item" v-for="item in noticeList">
                    <div class="notice_title ellipsis">
                      <!-- 1通知、2公告 -->
                      <img v-if="item.noticeType == '1'" src="@/assets/homenew/notice.png">
                      <img v-if="item.noticeType == '2'" src="@/assets/homenew/bulletin.png">
                      <span>{{ item.noticeTitle }}</span>
                    </div>
                    <div class="notice_time">
                      {{ item.createTime }}
                    </div>
                  </div>
                </template>
                <template v-else>
                  <div style="text-align: center;color: #606266;font-size: 12px;">暂无通知</div>
                </template>
              </div>
            </el-card>
          </el-row>
        </el-col>
      </el-row>
      <el-row class="mb30" :gutter="30">
        <el-col :span="12">
          <el-card class="jurisdiction-incident" v-loading="loadingList.eventLoading">
            <template slot="header">
              <div class="card-title">
                <div class="bottom_line">辖区事件</div>
                <el-button type="text" @click="routeChange('dailyMaintenance/eventManage/eventData')" class="more">
                  查看更多
                </el-button>
              </div>
            </template>
            <div class="incident-item">
              <el-row class="incident_row">
                <el-col :span="12" v-for="(item, index) in eventData" class="mb20">
                  <div class="listContent" :class="{ mr20: (index + 1) % 2 != 0 }">
                    <el-descriptions :column="2" :title="item.disCode">
                      <template #title>
                        <p class="dis-title ellipsis">
                          <img src="@/assets/homenew/event.png">
                          {{ item.disCode }}({{ item.reportName }})
                        </p>
                      </template>
                      <el-descriptions-item label="管养单位">
                        {{ item.domainName }}
                      </el-descriptions-item>
                      <el-descriptions-item label="采集时间">
                        {{ item.collectTime }}
                      </el-descriptions-item>
                      <el-descriptions-item label="事件类型">
                        {{ item.disName }}
                      </el-descriptions-item>
                      <el-descriptions-item label="描述"
                        contentStyle="white-space: nowrap; overflow: hidden; cursor: pointer;">
                        <el-tooltip effect="dark" :content="item.disDesc" placement="top-start">
                          <span>{{ item.disDesc }}</span>
                        </el-tooltip>
                      </el-descriptions-item>
                    </el-descriptions>
                    <div class="disStatusName">{{ item.disStatusName }}</div>
                  </div>
                </el-col>
              </el-row>
            </div>
          </el-card>
        </el-col>
        <el-col :span="12">
          <el-card class="budget-card" v-loading="loadingList.sumFundInfoLoading">
            <template slot="header">
              <div class="card-title">
                <div class="bottom_line">预算执行情况</div>
                <!-- <el-button type="text">查看更多</el-button> -->
              </div>
            </template>
            <Echarts :option="option" v-if="option" height="100%"></Echarts>
          </el-card>
        </el-col>
      </el-row>
      <el-row :gutter="30">
        <el-col :span="8">
          <el-card class="line-info">
            <template slot="header">
              <div class="card-title">
                <div class="bottom_line">路线基础信息</div>
                <el-button type="text" @click="routeChange('base/road/statistics')" class="more">
                  查看更多
                </el-button>
              </div>
            </template>
            <el-table :data="mainSecList" stripe header-cell-class-name="table-header" height="100%"
              v-loading="loadingList.mainSecLoading" border>
              <el-table-column align="center" prop="managementMaintenanceName" label="管理处"
                show-overflow-tooltip></el-table-column>
              <el-table-column align="center" prop="maintenanceSectionName" label="路段名称"
                show-overflow-tooltip></el-table-column>
              <el-table-column align="center" prop="maintenanceMileage" label="里程(km)"
                show-overflow-tooltip></el-table-column>
            </el-table>
          </el-card>
        </el-col>
        <el-col :span="8">
          <el-card class="bridge-info">
            <template slot="header">
              <div class="card-title">
                <div class="bottom_line">桥梁基础信息</div>
                <el-button type="text" @click="routeChange('base/bridge/statistics')" class="more">
                  查看更多
                </el-button>
              </div>
            </template>
            <el-table :data="bridgeList" stripe header-cell-class-name="table-header"
              v-loading="loadingList.bridgeLoading" height="100%" border>
              <el-table-column align="center" prop="managementMaintenanceName" label="管理处"
                show-overflow-tooltip></el-table-column>
              <el-table-column align="center" prop="maintenanceSectionName" label="养护路段名称"
                show-overflow-tooltip></el-table-column>
              <el-table-column align="center" prop="superMajorBridge" label="特大桥" width="80"></el-table-column>
              <el-table-column align="center" prop="greatBridge" label="大桥" width="80"></el-table-column>
              <el-table-column align="center" prop="mediumBridge" label="中桥" width="80"></el-table-column>
              <el-table-column align="center" prop="smallBridge" label="小桥" width="60"></el-table-column>
            </el-table>
          </el-card>
        </el-col>
        <el-col :span="8">
          <el-card class="tunnel-info">
            <template slot="header">
              <div class="card-title">
                <div class="bottom_line">隧道基础信息</div>
                <el-button type="text" @click="routeChange('base/tunnel/statistics')" class="more">
                  查看更多
                </el-button>
              </div>
            </template>
            <el-table :data="tunnelList" stripe header-cell-class-name="table-header"
              v-loading="loadingList.tunnelLoading" height="100%" border>
              <el-table-column align="center" prop="managementMaintenanceName" label="管理处"
                show-overflow-tooltip></el-table-column>
              <el-table-column align="center" prop="maintenanceSectionName" label="养护路段名称"
                show-overflow-tooltip></el-table-column>
              <el-table-column align="center" prop="superMajorTunnel" label="特长隧道" width="80"></el-table-column>
              <el-table-column align="center" prop="greatTunnel" label="长隧道" width="80"></el-table-column>
              <el-table-column align="center" prop="mediumTunnel" label="中隧道" width="70"></el-table-column>
              <el-table-column align="center" prop="smallTunnel" label="短隧道" width="70"></el-table-column>
            </el-table>
          </el-card>
        </el-col>
      </el-row>
    </template>
    <template v-else>
      <div class="welcome">
        <div class="welcome_info">
          <img :src="logoInfo.middleLogUrl" alt="" class="login-img" />
          <div class="leftTop">
            <div class="textItem">
              <span style="font-size: 24px;font-weight: 700;">您好，</span>{{ nickName }}
              <div class="textItemSpan">
                <div>欢迎使用高速公路智慧养护管理平台</div>
                <div style="font-size: 12px;font-weight: 400;">{{ times }}</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </template>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import { findDiseaseDataList } from '@/api/dailyMaintenance/eventManage/eventData.js'
import request from '@/utils/request'
import Echarts from './echarts.vue'
import { divide, round } from 'lodash'
import * as echarts from 'echarts'
import { getStatistics } from '@/api/baseData/road/statistics/index'
import { getStatistics as bridgeListApi } from '@/api/baseData/bridge/statistics/index'
import { getStatistics as tunnelListApi } from '@/api/baseData/tunnel/statistics/index'
import { queryPageTodoTaskGroup } from '@/api/process/task/task' // 流程任务模块
import { listRepoteRecord } from '@/api/repote/repoteRecord'
import moment from 'moment/moment'
import { listNotice } from "@/api/system/notice";
import auth from "@/plugins/auth"
export default {
  components: {
    Echarts
  },
  data() {
    return {
      isClose: true,
      isPlanCenter: true,
      taskList: [],
      writeTaskList: [],
      chart: null,
      eventData: [],
      loadingList: {
        taskLoading: false,
        mainSecLoading: false,
        bridgeLoading: false,
        tunnelLoading: false,
        writeTaskLoading: false,
        eventLoading: false,
        noticeLoading: false,
        sumFundInfoLoading: false,
      },
      option: null,
      fundObj: {
        日常养护: {
          fund1: 0, // 预算
          fund2: 0, // 实际
        },
        养护工程: {
          fund1: 0,
          fund2: 0,
        },
        被损被盗: {
          fund1: 0,
          fund2: 0,
        },
        定期检测: {
          fund1: 0,
          fund2: 0,
        },
        运营维护费: {
          fund1: 0,
          fund2: 0,
        },
      },
      taskParams: {
        applyUserName: '',
        businessKey: '',
        category: '',
        endTime: '',
        maintenanceSectionId: '',
        managementMaintenanceIds: [],
        pageNum: 1,
        pageSize: 20,
        startTime: '',
        title: '',
      },
      mainSecList: [],
      bridgeList: [],
      tunnelList: [],
      patrolData: null,
      times: '',
      noticeList: [],
      showHome: false,
      logoInfo: {},
    }
  },
  computed: {
    ...mapGetters(['nickName']),
  },
  created() {
    this.showHome = auth.hasPermi('showHome')
    this.times = moment().format('YYYY年MM月DD日')
    this.showHome && this.init()
  },
  mounted() {
    this.$nextTick(() => {
      const Element = document.querySelector('.leftTop1');
      let logoInfo = JSON.parse(localStorage.getItem('logoInfo'));
      this.logoInfo = logoInfo
      if (logoInfo?.backgroundImage) {
        Element.style.backgroundImage = `url("${logoInfo.backgroundImage}")`;
      }
    });
  },
  methods: {
    init() {
      this.getTaskList()
      this.getNoticeList()
      this.getWriteTask()
      this.getPatrolData()
      this.getEventData()
      this.getSumFundInfo()
      this.getMainSecList()
      this.getBridgeList()
      this.getTunnelList()
    },
    // 获取任务列表
    getTaskList() {
      this.loadingList.taskLoading = true
      queryPageTodoTaskGroup(this.taskParams).then((res) => {
        this.taskList = res?.data?.records || []
        this.loadingList.taskLoading = false
      })
    },
    // 任务单列表点击
    handlerClick(row) {
      this.$router.push({ path: '/task/next', query: { ...row, ...this.taskParams } })
    },
    onHandler(row) {
      if (row.url) {
        this.$router.push({ path: row.url })
      } else {
        this.$modal.msgError('当前节点未配置地址')
      }
    },
    // 获取填报任务
    getWriteTask() {
      this.loadingList.writeTaskLoading = true
      listRepoteRecord().then((res) => {
        this.writeTaskList = res.rows
        this.loadingList.writeTaskLoading = false
      })
    },
    // 获取事件数据
    getEventData() {
      const params = {}
      this.loadingList.eventLoading = true
      findDiseaseDataList(params).then((res) => {
        this.eventData = res.rows
        this.loadingList.eventLoading = false
      })
    },
    getSumFundInfo() {
      this.loadingList.sumFundInfoLoading = true
      request({
        url: '/manager/disease/getProjTypeSumFundInfo',
        method: 'post',
        data: {
          year: new Date().getFullYear(),
        },
      }).then((res) => {
        const resultData = res.rows
        resultData.forEach((item) => {
          if (item.type === '日常养护费用') {
            this.fundObj['日常养护'].fund1 = item.sumFund
          }
          if (item.type === '日常养护实际费用') {
            this.fundObj['日常养护'].fund2 = item.sumFund
          }
          if (item.type === '专项工程费用') {
            this.fundObj['养护工程'].fund1 = item.sumFund
          }
          if (item.type === '专项养护实际费用') {
            this.fundObj['养护工程'].fund2 = item.sumFund
          }
          if (item.type === '被损被盗') {
            this.fundObj['被损被盗'].fund1 = item.sumFund
          }
          if (item.type === '被损被盗实际费用') {
            this.fundObj['被损被盗'].fund2 = item.sumFund
          }
          if (item.type === '养护检测费用') {
            this.fundObj['定期检测'].fund1 = item.sumFund
          }
          if (item.type === '养护检测实际费用') {
            this.fundObj['定期检测'].fund2 = item.sumFund
          }
          if (item.type === '运营费用') {
            this.fundObj['运营维护费'].fund1 = item.sumFund
          }
          if (item.type === '运营费用实际费用') {
            this.fundObj['运营维护费'].fund2 = item.sumFund
          }
        })
        this.setOption()
        this.loadingList.sumFundInfoLoading = false
      })
    },
    setOption() {
      const data1 = []
      const data2 = []
      const xAxisData = []
      for (let key in this.fundObj) {
        xAxisData.push(key)
        const item = this.fundObj[key]
        data1.push(round(divide(item.fund1, 10000), 2))
        data2.push(round(divide(item.fund2, 10000), 2))
      }
      this.option = {
        tooltip: {
          show: true,
          trigger: 'axis',
          axisPointer: {
            type: 'shadow',
          },
          valueFormatter: (value) => {
            return `${value}万元`
          },
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '50',
          containLabel: true,
        },
        legend: {
          data: ['预算', '实际完成'],
          top: '0',
          right: '0',
        },
        xAxis: {
          type: 'category',
          boundaryGap: false,
          data: xAxisData,
          axisLabel: {
            width: 70,
            interval: 0,
            overflow: 'truncate',
            ellipsis: '...',
          },
          axisLine: {
            show: false,
          },
          axisTick: {
            show: false,
          }
        },
        yAxis: {
          name: '万元',
          type: 'value',
        },
        series: [
          {
            name: '预算',
            data: data1,
            type: 'line',
            stack: 'Total',
            smooth: true,
            lineStyle: {
              width: 0
            },
            showSymbol: false,
            areaStyle: {
              opacity: 0.8,
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                {
                  offset: 0,
                  color: 'rgba(41, 157, 234, 1)'
                },
                {
                  offset: 1,
                  color: 'rgba(71, 229, 229, 0.06)'
                }
              ])
            },
            emphasis: {
              focus: 'series'
            },
          },
          {
            name: '实际完成',
            data: data2,
            type: 'line',
            stack: 'Total',
            smooth: true,
            lineStyle: {
              width: 0
            },
            showSymbol: false,
            areaStyle: {
              opacity: 0.8,
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                {
                  offset: 0,
                  color: 'rgba(67, 207, 124, 1)'
                },
                {
                  offset: 1,
                  color: 'rgba(71, 229, 229, 0.06)'
                }
              ])
            },
            emphasis: {
              focus: 'series'
            },
          },
        ],
      }
    },
    // 获取路线基础信息
    getMainSecList() {
      const params = {
        pageNum: 10,
        pageSize: 1,
        managementMaintenanceIds: [],
        routeCodes: [],
        maintenanceSectionId: '',
      }
      this.loadingList.mainSecLoading = true
      getStatistics(params).then((res) => {
        this.mainSecList = res
        this.loadingList.mainSecLoading = false
      })
    },
    // 获取桥梁基础信息
    getBridgeList() {
      const params = {
        operationState: '2',
        managementMaintenanceIds: [],
        routeCodes: [],
        maintenanceSectionId: '',
      }
      this.loadingList.bridgeLoading = true
      bridgeListApi(params).then((res) => {
        this.bridgeList = res
        this.loadingList.bridgeLoading = false
      })
    },
    // 获取隧道基础信息
    getTunnelList() {
      const params = {
        operationState: '',
        checkYear: '2024',
        managementMaintenanceIds: [],
        routeCodes: [],
        maintenanceSectionId: '',
      }
      this.loadingList.tunnelLoading = true
      tunnelListApi(params).then((res) => {
        this.tunnelList = res
        this.loadingList.tunnelLoading = false
      })
    },
    // 获取巡检查数据
    getPatrolData() {
      request({
        url: '/patrol/inspectionLogs/patrolMonthlyDetail',
        method: 'get',
      }).then((res) => {
        this.patrolData = res.data
      })
    },
    /** 查询公告列表 */
    getNoticeList() {
      this.loadingList.noticeLoading = true;
      listNotice().then(response => {
        this.noticeList = response.rows;
        this.loadingList.noticeLoading = false;
      });
    },
    routeChange(path) {
      this.$router.push(path)
    },
  },
}
</script>
<style lang="scss" scoped>
.home-content {
  padding: 15px 30px;
  height: 100%;
  overflow-y: auto;

  ::v-deep .table-header {
    background-color: rgba(24, 144, 255, 0.1);
    font-size: 14px;
  }

  ::v-deep .el-table--striped .el-table__body tr.el-table__row--striped td.el-table__cell {
    background-color: rgba(24, 144, 255, 0.05);
  }
}

.summary1,
.summary2,
.summary3,
.summary4 {
  height: 200px;
  background-color: #ffffff;
  border-radius: 10px;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  padding: 15px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.summary-top,
.summary-bottom {
  flex: 1;
  display: flex;
  justify-content: space-between;
}

.summary-bottom {
  justify-content: space-around;
}

.summary-top {
  font-size: 16px;
  color: #303133;

  .summary-more {
    cursor: pointer;

    &::after {
      display: inline-block;
      width: 8px;
      height: 8px;
      content: '';
      border-top: 1px solid #303133;
      border-right: 1px solid #303133;
      transform: rotate(45deg);
    }
  }
}

.summary-title {
  img {
    width: 36px;
    height: 36px;
    vertical-align: middle;
    margin-right: 10px;
  }
}

.summary-value {
  font-size: 32px;
  font-weight: 600;
  color: #282D30;
  word-break: break-word;
}

.summary-item {
  text-align: center;
}

.unite {
  font-size: 14px;
}

.summary-name {
  margin-top: 12px;
  font-size: 16px;
  font-weight: 400;
  color: #606266;
}

.line {
  width: 2px;
  height: 30px;
  background-color: #B6B6B6;
}

.el-card {
  border-radius: 10px;

  .card-title {
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 56px;
    font-size: 20px;
    font-weight: 600;
  }

  ::v-deep .el-card__body {
    height: 350px;
    overflow-y: auto;
  }
}

.bottom_line {
  &::after {
    margin-top: 8px;
    content: '';
    display: block;
    width: 100%;
    height: 4px;
    border-radius: 2px;
    background-color: #1890FF;
  }
}

.more {
  &::after {
    margin-left: 5px;
    display: inline-block;
    width: 8px;
    height: 8px;
    content: '';
    border-top: 1px solid #1890FF;
    border-right: 1px solid #1890FF;
    transform: rotate(45deg);
  }
}

.incident-item {
  min-width: 750px;
  overflow-x: auto;
}

.listContent {
  position: relative;
  border-radius: 7px;
  background: rgba(24, 144, 255, 0.05);
  padding: 6px 20px 14px;
}

.disStatusName {
  position: absolute;
  top: 0;
  right: 0;
  font-size: 10px;
  color: #ffffff;
  background-color: #1890FF;
  border-radius: 0 5px;
  padding: 2px 5px 2px 5px;
}

.mr20 {
  margin-right: 20px;
}

.mb30 {
  margin-bottom: 30px;
}

.mb20 {
  margin-bottom: 20px;
}

.incident_row div:nth-last-child(-n+2) {
  margin-bottom: 0;
}

::v-deep .el-descriptions__header {
  margin-bottom: 0;
}

::v-deep .el-descriptions__body {
  background-color: transparent;
}

::v-deep .el-descriptions__title {
  font-size: 14px;
  font-weight: 400;
}

::v-deep .el-descriptions--mini {
  font-size: 10px;
}

.leftTop1 {
  border-radius: 10px;
  padding: 15px 20px 20px 20px;
  color: #fff;
  /* 确保背景图片覆盖整个div */
  background-size: cover;
  background-position: 50% 75%;
  background-repeat: no-repeat;
  /* 背景不重复 */

  /* 其他样式 */
  position: relative;
  width: 100%;
  height: 150px;
  display: flex;
  justify-content: end;
  flex-direction: column;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  user-select: none;
}

.leftTop {
  border-radius: 10px;
  padding: 15px 20px 20px 20px;
  color: #fff;
  /* 其他样式 */
  position: relative;
  width: 100%;
  height: 150px;
  display: flex;
  justify-content: center;
  flex-direction: column;
  user-select: none;
}

.textItem {
  text-align: left;
  font-family: 'NotoSerifCJKsc';
  font-size: 14px;
  font-weight: 700;
  width: 100%;
  text-shadow: 2px 2px 4px #000000;
}

.textItemSpan {
  display: flex;
  justify-content: space-between;
}

.colse_btn {
  width: 16px;
  height: 16px;
  position: absolute;
  top: 20px;
  right: 20px;
  cursor: pointer;
}

.ellipsis {
  width: 100%;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.dis-title {
  img {
    width: 32px;
    height: 32px;
    vertical-align: middle;
    margin-right: 10px;
  }
}

.notice_item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 44px;
  border-bottom: 1px solid #F1F1F1;

  .notice_title {
    flex: 1;
    font-size: 14px;
    color: rgba(48, 49, 51, 1);

    img {
      width: 24px;
      height: 24px;
      vertical-align: middle;
      margin-right: 10px;
    }
  }

  .notice_time {
    margin-left: 10px;
    font-size: 12px;
    color: rgba(96, 98, 102, 1);
  }
}

.welcome {
  width: 100%;
  height: 100%;
  position: relative;

  .welcome_info {
    width: 40%;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    text-align: center;

    .login-img {
      width: 200px;
      margin-bottom: 40px;
    }
  }
}
</style>
