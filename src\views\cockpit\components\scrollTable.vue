<template>
  <div class="el-table-body" :class="isBig ? 'big-table' : 'normal-table'">
    <table class="table-head">
      <thead>
        <tr>
          <th :style="{
            width:
              col.prop === 'index' ? '8%' : (92 / (columns.length)) + '%',
          }" v-for="(col, colIndex) in columns" :key="colIndex">
            {{ col.label }}
          </th>
        </tr>
      </thead>
    </table>

    <vue-seamless-scroll :data="data" :class-option="optionHover" class="seamless-warp">
      <table class="table-body">
        <tbody>
          <tr v-for="(item, index) in data" :key="index" class="table-row">
            <td v-for="(col, colIndex) in columns" :key="colIndex" :style="{
              width:
                col.prop === 'index' ? '8%' : 92 / (columns.length) + '%',
            }" class="table-cell">
              <div class="table-index" v-if="col.prop === 'index'" :style="indexStyle(index)">
                {{ index + 1 }}
              </div>
              <div class="table-name" v-else-if="col.prop === 'name'" :title="item[col.prop]">
                {{ item[col.prop] }}
              </div>
              <div class="table-val" v-else>{{ item[col.prop] }}</div>
            </td>
          </tr>
        </tbody>
      </table>
    </vue-seamless-scroll>
  </div>
</template>

<script>
import { isBigScreen } from "../util/utils";

export default {
  name: "tables",
  props: {
    columns: {
      type: Array,
      default: () => [],
    },
    data: {
      type: [Array, Object],
      default: () => [],
    },
    isScroll: {
      type: Boolean,
      default: true,
    },
  },
  data() {
    return {
      isBig: isBigScreen(),
      rolltimer: null,
    };
  },
  computed: {
    // 滚动设置
    optionHover() {
      return {
        hoverStop: true,
        step: 0.3, // 数值越大速度滚动越快
        limitMoveNum: 2, // 开始无缝滚动的数据量 this.dataList.length
        direction: 1, // 0向下 1向上 2向左 3向右
        openWatch: true, // 开启数据实时监控刷新dom
        singleHeight: 0, // 单步运动停止的高度(默认值0是无缝不停止的滚动) direction => 0/1
        singleWidth: 0, // 单步运动停止的宽度(默认值0是无缝不停止的滚动) direction => 2/3
        waitTime: 1000, // 单步运动停止的时间(默认值1000ms)
      };
    },
  },
  mounted() { },
  destroyed() { },
  methods: {
    indexStyle(index) {
      let bs = "";
      let border = "";
      let color = "";
      if (index === 0) {
        bs = "inset 0px 0px 15px 0px rgba(175,0,0,0.6)";
        border = "1px solid #AF0000";
        color = "#FF4646";
      } else if (index === 1) {
        bs = "inset 0px 0px 15px 0px rgba(175,107,0,0.6)";
        border = "1px solid #AF6B00";
        color = "#FD9500";
      } else if (index === 2) {
        bs = "inset 0px 0px 15px 0px rgba(175,156,0,0.6)";
        border = "1px solid #AF9C00";
        color = "#FCFF00";
      } else {
        bs = "inset 0px 0px 15px 0px rgba(0,115,175,0.6)";
        border = "1px solid #0073AF";
        color = "#00EAFF";
      }

      let obj = {
        textAlign: "center",
        width: this.isBig ? "40px" : "22px",
        height: this.isBig ? "40px" : "22px",
        lineHeight: this.isBig ? "40px" : "22px",
        background: "rgba(4,17,48,0.4)",
        boxShadow: bs,
        border,
        color,
      };
      return obj;
    },
  },
};
</script>

<style lang="scss" scoped>
@import "@/assets/styles/utils.scss";

.el-table-body {
  width: 100%;
  height: 100%;
  padding: 0 0 10px 0;
  overflow: hidden;

  .table-head {
    width: 100%;
    height: vwpx(50px);
    background-color: #071a2e;
    color: #018cb3;
    font-size: vwpx(28px);
    z-index: 1;
  }

  .table-body {
    width: calc(99%);
    height: calc(100% - vwpx(50px));
    color: #fff;
    font-size: vwpx(28px);
    z-index: 9;
    border-collapse: collapse;
    margin-left: vwpx(4px);
    margin-right: vwpx(4px);

    .table-row {
      border-bottom: 1px solid #0072fd !important;

      .table-cell {
        text-align: center;

        height: vwpx(90px);

        .table-index {
          flex-shrink: 0;
          min-width: vwpx(40px);
          margin-left: vwpx(4px);
        }

        .table-name {
          flex-shrink: 0;
          max-width: vwpx(220px);
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }

        // 文字超出隐藏
        .table-val {
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
      }
    }
  }
}

.normal-table {
  ::v-deep .el-table {
    .el-table__row {
      height: vwpx(90px) !important;
    }
  }
}

.big-table {
  ::v-deep .el-table {
    .el-table__row {
      height: vwpx(105px) !important;
    }
  }
}

.seamless-warp {
  height: calc(100% - vwpx(40px));
  overflow: hidden;
}
</style>
