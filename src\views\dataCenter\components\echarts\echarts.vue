<template>
  <div class="e-charts" :style="{ height: height }"></div>
</template>

<script>
import * as echarts from "echarts";
require("echarts/theme/macarons"); // echarts theme

// 动态计算字体大小函数
function fontSize(res) {
  let docEl = document.documentElement;
  let clientWidth = window.innerWidth || docEl.clientWidth || document.body.clientWidth;
  if (!clientWidth) return;
  let baseWidth = 1920; // 基准宽度
  return res * (clientWidth / baseWidth);
}

export default {
  props: {
    height: {
      type: String,
      default: "260px",
    },
    option: {
      type: Object,
      default: () => { },
    },
  },
  data() {
    return {
      chart: null,
    };
  },
  mounted() {
    this.initChart();
  },
  methods: {
    initChart() {
      this.chart = echarts.init(this.$el, "macarons", { renderer: "svg" });
      this.chart.setOption(this.option);
      // 监听窗口变化，重新渲染图表
      this.chart.resize();
      window.addEventListener("reload", () => {
        this.chart.setOption(this.option);
      })

      window.addEventListener("resize", () => {
        this.chart.resize();
        this.updateChartOption();
        this.$forceUpdate();
      });
      this.chart.on('click', (params) => {
        this.$emit('click', params)
      })
    },
    updateChartOption() {
      const newOption = JSON.parse(JSON.stringify(this.option));
      this.deepUpdateFontSize(newOption);
      this.chart.setOption(newOption);
      this.chart.resize();
      this.$forceUpdate();
    },
    deepUpdateFontSize(obj) {
      if (typeof obj === 'object' && obj !== null) {
        if (obj.textStyle && obj.textStyle.fontSize) {
          obj.textStyle.fontSize = fontSize(obj.textStyle.fontSize);
        }
        if (obj.axisLabel && obj.axisLabel.fontSize) {
          obj.axisLabel.fontSize = fontSize(obj.axisLabel.fontSize);
        }
        if (obj.legend && obj.legend.textStyle && obj.legend.textStyle.fontSize) {
          obj.legend.textStyle.fontSize = fontSize(obj.legend.textStyle.fontSize);
        }
        for (const key in obj) {
          this.deepUpdateFontSize(obj[key]);
        }
      }
    }
  },
};
</script>

<style lang="scss" scoped>
.e-charts {
  width: 100%;
}
</style>
