<template>
  <div class="app-container">

    <!-- 查询 -->
    <div class="searchBox" style="height: 48px;">
      <el-row :gutter="12">
        <el-col :span="4">
          <el-input v-model="queryParams.key" placeholder="请输入流程ID"></el-input>
        </el-col>
        <el-col :span="4">
          <el-input v-model="queryParams.name" placeholder="请输入流程名称"></el-input>
        </el-col>
        <el-col :span="4">
          <el-input v-model="queryParams.category" placeholder="请输入流程类型"></el-input>
        </el-col>
        <el-col :span="12" style="display: flex; align-items: center">

          <el-button type="primary" icon="el-icon-search" size="mini" @click="queryList">搜索</el-button>
          <el-button icon="el-icon-refresh" size="mini" @click="">重置</el-button>

        </el-col>
      </el-row>
    </div>
    
    <!-- 主表数据 -->
    <div class="tableDiv" style="height: calc(100% - 58px)">
      <!-- 功能按钮 -->
      <div class="btnBox">
        <el-row :gutter="10">
          <el-col :span="1.5">
            <el-button type="primary" icon="el-icon-plus" size="mini" @click="formInit('add')">新增
            </el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="success" icon="el-icon-edit" size="mini" :disabled="tableSelection.length !== 1"
              @click="formEdit('select')">修改
            </el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="danger" icon="el-icon-delete" size="mini" :disabled="tableSelection.length === 0"
              @click="formDelete('batch')">删除
            </el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="primary" icon="el-icon-document-add" size="mini"@click="handleImport">导入</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="warning" icon="el-icon-download" size="mini" @click="formExport">导出</el-button>
          </el-col>
        </el-row>
      </div>

      <!-- 数据表格 -->
      <el-table size="mini" height="calc(100% - 98px)" border ref="tableRef"
        v-loading="tableLoading" :data="tableData" @selection-change="tableSelectionChange" @row-click="tableRowClick">
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="序号" fixed align="center" type="index" width="50"></el-table-column>
        <el-table-column label="所属分类" fixed :show-overflow-tooltip="true" align="center" prop="category" />
        <el-table-column label="流程名称" fixed :show-overflow-tooltip="true" align="center" prop="name" />
        <el-table-column label="流程KEY" fixed :show-overflow-tooltip="true" align="center" prop="key" />
        <el-table-column label="流程ID" fixed :show-overflow-tooltip="true" align="center" prop="id" />
        <el-table-column label="部署ID" fixed :show-overflow-tooltip="true" align="center" prop="deploymentId" />
        <el-table-column label="流程版本" fixed :show-overflow-tooltip="true" align="center" prop="version" />
        <el-table-column label="流程实例状态" fixed :show-overflow-tooltip="true" align="center" prop="suspendStateName">
          <template slot-scope="scope">
            <el-tag :type="scope.row.suspendStateName == '已激活' ? 'success' : 'warning'">{{scope.row.suspendStateName}}</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width" fixed="right">
          <template slot-scope="scope">
            <el-button 
              size="mini" 
              type="text"
              :icon="scope.row.suspendStateName == '已激活' ? 'el-icon-video-pause' : 'el-icon-video-play'" 
              @click="bpmnStatusChange(scope.row)"
            >
              {{ scope.row.suspendStateName == '已激活' ? '挂起' : '激活' }}
            </el-button>
            <el-button size="mini" type="text" icon="el-icon-edit" @click="formEdit('click', scope.row)">修改
            </el-button>
            <el-button size="mini" type="text" icon="el-icon-delete" @click="formDelete('single', scope.row)">删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <pagination :total="queryTotal" :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize" @pagination="queryList" style="margin-right: 10px;" />

    </div>

    <!-- 表单 -->
    <el-dialog :visible.sync="formDialog" width="70%" max-height="50%" :close-on-press-escape="false" :close-on-click-modal="false" append-to-body class="formDialog">
      <template slot="title">
        <div class="titleBox">
          <div class="title">{{ formType === 'add' ? '新建流程' : '编辑流程' }}</div>
          <div class="subTitle" v-if="formParams.id">ID：</div>
        </div>
      </template>

      
    </el-dialog>

  </div>
</template> 

<script>
// -------------------- 引入 --------------------

// API

export default {
  name: "Await",
  // 数据字典
  dicts: [],
  // 组件
  components: {},

  // -------------------- 变量 --------------------
  data() {
    return {

      /**
       * 查询相关
       */
      queryShow: false, // 查询更多参数显隐
      queryParams: { // 查询参数
        name: '', // 流程名称
        key: '', // 流程ID
        category: '', // 流程类型
        pageNum: 1, // 页码
        pageSize: 10, // 每页条数
      },
      queryTotal: 0, // 查询总数

      /**
       * 表格相关
       */
      tableData: [], // 表格数据
      tableSelection: [], // 表格选中数据
      tableLoading: false, // 表格加载

      /**
       * 表单相关
       */
      formDialog: false, // 表单弹窗
      formParams: { // 表单参数
        id: '', // 流程ID
      },
      formType: '', // 表单类型
      formBtnLoading: false, // 表单按钮加载
    }
  },
  mounted() {
    this.initPage()
  },
  // -------------------- 方法 --------------------
  methods: {

    /**
     * 页面相关
     */
    // 初始化页面
    initPage() {
      this.queryList()
    },

    /**
     * 查询相关
     */
    // 查询数据
    queryList() {
      this.tableLoading = false
    },

    // 查询重置
    queryReset() {

    },

    /**
     * 表格相关
     */
    // 勾选表格项改变时
    tableSelectionChange(val) {
      this.tableSelection = val
    },

    // 点击一行时
    tableRowClick(val) {
      this.$refs.tableRef.toggleRowSelection(val)
    },

    /**
     * 表单相关
     */
    // 初始化表单
    formInit(type, data) {
      this.formType = type
      switch (type) {
        case 'add':
       
          break;
      
        case 'edit':
        
          break;
      }
    },
  },
}

</script>

<style lang="scss" scoped>
.app-container form:first-child .el-select,
.app-container form:nth-child(2) .el-select,
.app-container form:nth-child(2) ::v-deep .el-form-item__content,
.app-container form:first-child ::v-deep .el-form-item__content {
  width: 240px;
}

.app-container form:first-child .el-form-item:last-child ::v-deep .el-form-item__content {
  width: auto;
}

.app-container {
  padding: 10px;
  background-color: #c0c0c0;
  box-sizing: border-box;
}

.formDialog {
  ::v-deep .el-dialog__body {
    height: 600px;
    overflow-y: auto;
  }

  .titleBox {
    height: 22px;
    position: relative;
    display: flex;
    flex-direction: row;
    align-items: center;

    .title {
      font-size: 16px;
      color: black;
      margin: 0;
    }

    .subTitle {
      margin-left: 15px;
      font-size: 12px;
      color: #888888;
    }

    .riskLevel {
      user-select: none;
      position: absolute;
      // top: 0;
      right: 5%;
      display: flex;
      align-items: center;
      flex-direction: row;

      .title {
        font-size: 16px;
        font-weight: bold;
      }

      .main {
        font-size: 16px;
        font-weight: bold;
        padding: 5px 10px 5px 10px;
        color: white;
        box-sizing: border-box;
        border-radius: 5px;
      }

    }
  }

  .formMain {
    height: 100%;
    width: 100%;
    box-sizing: border-box;
    border: 1px solid #888888;
    position: relative;

    .mainCanvas {
      height: 100%;
      width: 100%;
    }

    .mainPanel {
      position: absolute;
      top: 0;
      right: 0;
      height: 100%;
      width: 300px;
      border-left: 1px solid #888888;
      background-color: rgb(248, 248, 248);
      overflow-y: auto;
    }
  }
}

.searchBox {
  padding: 10px;
  background: #fff;
  border-radius: 10px;
  transition: all .1s linear;
  display: flex;
  flex-direction: column;

  .searchMoreBox {
    min-width: 192px;
    margin-top: 10px; 
    display: flex;
    align-items: center;
    flex-direction: row;
  }
}

.tableDiv {
  margin-top: 10px;
  background-color: white;
  padding-bottom: 10px;
  border-radius: 10px;
  transition: all .1s linear;
  display: flex;
  flex-direction: column;

  .btnBox {
    padding: 10px;
  }
}

.infoBox {
  padding: 15px 15px 0 15px;
  box-sizing: border-box;
  border-radius: 6px;
  border: 1px solid #C4C4C4;
  position: relative;

  .infoTitle {
    user-select: none;
    position: absolute;
    top: 0;
    left: 0;
    padding: 0 10px;
    font-size: 14px;
    line-height: 14px;
    font-weight: bold;
    transform: translateX(15px) translateY(-50%);
    background-color: white;
  }

  .imgBox {
    height: auto;
    width: 100%;
    display: flex;
    flex-wrap: wrap;
    align-content: flex-start;

    .imgItemBox {
      height: 240px;
      width: calc(100% / 3);
      box-sizing: border-box;
      padding: 10px;
      overflow-y: auto;
      display: flex;
      align-items: center;
      justify-content: center;
      position: relative;

      .imgDeleteBtn {
        position: absolute;
        z-index: 1;
        top: 10%;
        right: 10%;
      }
    }
  }
}

// 流程图相关
.bpmnDialog {
  ::v-deep .el-dialog__body {
    height: 600px;
    overflow-y: auto;
  }

  .bpmnGroundMain {
    height: 100%;
    width: 100%;
    display: flex;
    flex-direction: column;

    .searchBox {
      height: 48px;
      width: 100%;
    }

    .dataBox {
      height: calc(100% - 148px);
      width: 100%;
      overflow-y: auto;
    }

    .selectBox {
      height: 100px;
      width: 100%;
      box-sizing: border-box;
      padding: 10px;
      border: 1px solid #C4C4C4;
      border-radius: 5px;
      display: flex;
      flex-direction: row;
      flex-wrap: wrap;
      align-content: flex-start;
      overflow-y: scroll;
      position: relative;

      // .selectTitle {
      //   position: absolute;
      //   top: 0;
      //   left: 10px;
      //   padding: 5px 10px;
      //   font-size: 14px;
      //   font-weight: bold;
      //   background-color: white;
      //   transform: translateY(-50%);
      // }
    }
  }
}

::v-deep .bjs-powered-by {
  opacity: 0;
  user-select: none;
  pointer-events: none;
}

// v-if过渡动画
// 查询框
.search-enter-active {
  transition: all .1s linear;
}
.search-enter {
  opacity: 0;
}
.search-leave-active {
  transition: all .1s linear;
}
.search-leave-to {
  opacity: 0;
}
</style>