<template>
  <div class="road-animation">
    <div class="header">
      <span>{{ form.title }}</span>
      <i class="el-icon-close body-close" @click="handleClose"></i>
    </div>
    <div style="display: flex; align-items: center;">
      <el-popover placement="bottom" :width="isBig ? '400' : '200'" trigger="click" :append-to-body="false">
        <div style="height: 40vh;overflow-y: auto;">
          <el-form label-position="top" label-width="80px" :model="form">
            <el-form-item label="起点桩号 (km)">
              <el-input-number v-model="form.startKm" controls-position="right" :controls="false" style="width: 100%;"
                @blur="onStartKm"></el-input-number>
            </el-form-item>
            <el-form-item label="终点桩号 (km)">
              <el-input-number v-model="form.endKm" controls-position="right" :controls="false" style="width: 100%;"
                @blur="onEndKm"></el-input-number>
            </el-form-item>
            <el-form-item :label="'总里程 (km): ' + form.totalKm.toFixed(3)">
            </el-form-item>
            <el-form-item label="每公里像素宽度">
              <el-slider v-model="form.kmWidth" :min="200" :max="isBig ? 5000 : 1000"></el-slider>
            </el-form-item>
            <el-form-item label="车道数">
              <el-select v-model="form.laneCount" placeholder="请选择" style="width: 100%">
                <el-option v-for="item in laneCountOptions" :key="item.value" :label="item.label" :value="item.value">
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="方向">
              <el-select v-model="form.direction" placeholder="请选择" style="width: 100%">
                <el-option v-for="item in directionOptions" :key="item.value" :label="item.label" :value="item.value">
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="选择车道">
              <el-select v-model="form.lane" placeholder="请选择" style="width: 100%">
                <el-option v-for="item in lanesOptions" :key="item" :value="item"
                  :label="form.direction === 'up' ? `上行${item}车道` : `下行${item}车道`">
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="速度">
              <el-slider v-model="form.speed" :min="100" :max="10000"></el-slider>
            </el-form-item>
            <!-- <el-form-item label="小车定位 (km)">
              <el-input-number v-model="form.carPosition" controls-position="right"
                style="width: 100%;"></el-input-number>
            </el-form-item> -->
          </el-form>
        </div>
        <el-button class="setting-button" type="primary" slot="reference">
          设置
        </el-button>
      </el-popover>
      <div class="control-buttons">
        <el-button v-if="!isEnd" type="success" :icon="isPaused ? 'el-icon-video-play' : 'el-icon-video-pause'"
          @click="handleAnimation">
          {{ isPaused ? '启动' : '暂停' }}
        </el-button>
        <el-button type="primary" icon="el-icon-refresh" @click="handleRemake">重置</el-button>
      </div>
    </div>
    <div class="viewport" ref="viewport" v-loading="loading">
      <div class="highway" ref="highway" :style="`width: ${highwayWidth}; left: ${highwayLeft};`">
        <!-- 车道和分隔线 -->
        <div v-for="(lane, index) in lanes" :key="index" class="lane-divider" :style="lane.style" />
        <!-- 黄线 -->
        <div class="yellow-line" />
        <!-- 应急车道 -->
        <div class="emergency-lane" style="top: 0" />
        <div class="emergency-divider" style="top: 8%" />
        <div class="emergency-lane" style="bottom: 0" />
        <div class="emergency-divider" style="bottom: 8%" />
        <!-- 里程碑 -->
        <div v-for="(milestone, index) in milestones" :key="'milestone' + index" class="milestone"
          :style="milestone.style">
          K{{ milestone.zh }}
        </div>
        <!-- 特殊区域 -->
        <div v-for="(bridge, index) in bridges" :key="'bridge' + index" class="bridge" :style="bridge.style"
          @click.stop="handleBridge(bridge)">
          <div class="bridge-name">{{ bridge.name }}</div>
        </div>
        <div v-for="(tunnel, index) in tunnels" :key="'tunnel' + index" class="tunnel" :style="tunnel.style"
          @click.stop="handleTunnel(tunnel)">
          <div class="tunnel-entrance left"></div>
          <div class="tunnel-entrance right"></div>
          <div class="tunnel-name">{{ tunnel.name }}</div>
          <div class="tunnel-lights top">
            <div v-for="(light, index) in tunnel.topLights" :key="'tunnelLightsTop' + index" class="tunnel-light"
              :style="light.style"></div>
          </div>
          <div class="tunnel-lights bottom">
            <div v-for="(light, index) in tunnel.bottomLights" :key="'tunnelLightsBottom' + index" class="tunnel-light"
              :style="light.style"></div>
          </div>
        </div>
        <div v-for="(area, index) in maintenanceAreas" :key="'area' + index" class="maintenance-area"
          :style="{ ...area.style, 'borderColor': area.style.borderColor }" @click.stop="handleMaintenanceAreas(area)"
          @mouseenter="changeColor(area)" @mouseleave="resetColor(area)">
          <div class="maintenance-label">{{ area.description }}</div>
        </div>
      </div>
      <!-- 小车 -->
      <div v-if="car.visible" class="car" :style="`top: ${carTop}; left: ${carLeft}`">
        <div class="car-info" :style="isEnd ? 'left: -90%' : ''">
          速度: {{ form.speed.toFixed(2) }} km/h | 位置: K{{ car.kmPart }}+{{ car.mPart ? car.mPart.toString().padStart(3,
            '0') : 0 }}
        </div>
        <div class="following-tooltip" style="left: 100%; right: auto; --tooltip-arrow: left;" v-show="currentArea">
          {{ currentArea }}
        </div>
      </div>
    </div>
    <div v-if="openMaintenanceInfo">
      <Dialog class="dialog-detail" :title="'养护详情'" :show.sync="openMaintenanceInfo" top="0" :append="true" width="50%"
        :modal="true">
        <div>
          <el-descriptions class="margin-top" :column="1" border>
            <el-descriptions-item v-for="item in showHeaderList" :key="item.id">
              <template slot="label">
                <span style="width: 100px;">{{ item.label }}</span>
              </template>
              <span>{{ maintenanceInfo[item.prop] }}</span>
            </el-descriptions-item>
          </el-descriptions>
        </div>
      </Dialog>
    </div>
  </div>
</template>

<script>
import { getRoadNetData } from "@/api/oneMap/animation"
import Dialog from "@/components/Dialog/index.vue"
import { isBigScreen } from '.././common/util';

export default {
  name: '',
  props: {
    roadData: {
      default: () => { }
    }
  },
  components: { Dialog },
  dicts: [
    "lane", // 1车道、2车道、3车道、4车道
    // "sys_route_direction", // 1上行、2下行
    "base_maintenance_type",
    "base_project_type"
  ],
  data() {
    return {
      loading: false,
      form: {
        startKm: 0,
        endKm: 100,
        totalKm: 100,
        kmWidth: isBigScreen() ? 5000 : 1000,
        laneCount: 4,
        direction: "up",
        lane: 1,
        speed: 1000
      },
      laneCountOptions: [
        {
          label: "2车道",
          value: 2
        },
        {
          label: "4车道",
          value: 4
        },
        {
          label: "6车道",
          value: 6
        },
        {
          label: "8车道",
          value: 8
        },
      ],
      directionOptions: [
        {
          label: "上行",
          value: "up"
        },
        {
          label: "下行",
          value: "down"
        },
      ],
      isPaused: true,
      bridges: [],
      tunnels: [],
      maintenanceAreas: [],
      car: {
        visible: true,
        kmPart: 0,
        mPart: 0,
      },
      carLeft: "0px",
      highwayLeft: "0px",
      currentArea: null,
      animationId: null,
      pausedProgress: 0,
      startTime: null,
      lastTimestamp: null,
      isEnd: false,
      originalStartKm: 0,
      originalEndKm: 100,
      openMaintenanceInfo: false,
      maintenanceInfo: {},
      showHeaderList: [
        { label: "路线编码", prop: "routeCode" },
        { label: "养护措施", prop: "description" },
        { label: "养护分类", prop: "maintenanceTypeName" },
        { label: "养护工程类别", prop: "projectTypeName" },
        { label: "方向", prop: "directionName" },
        { label: "车道", prop: "laneName" },
        { label: "结算金额（元）", prop: "calcFund" },
        { label: "处置年份", prop: "dealYear" },
        { label: "长度（m）", prop: "length" },
        { label: "宽度（m）", prop: "width" },
        { label: "面积（m²）", prop: "area" },
        { label: "起点桩号", prop: "startStakeName" },
        { label: "终点桩号", prop: "endStakeName" },
      ],
      btnFlag: false,
      isBig: isBigScreen(),
    }
  },
  created() {
    this.form.kmWidth = this.isBig ? 7000 : 1000
    this.getSpecialList()
    window.$Bus.$on("closeInfoForAnimation", () => {
      if (this.btnFlag) {
        this.isPaused = true
      } else {
        this.isPaused = false
        this.startAnimation()
      }
    })
  },
  methods: {
    // 关闭表格弹窗
    handleClose() {
      this.$emit('close')
      window.$Bus.$emit('closeBtn')
    },
    // 获取特殊区域信息
    getSpecialList() {
      if (!this.roadData.id) return
      this.loading = true
      getRoadNetData({ gisRouteId: this.roadData.id }).then(res => {
        if (res.code === 200) {
          this.form.title = res.data.maintenanceSectionName + "（" + this.roadData.name + "）"
          this.form.startKm = Number(res.data.qdzh / 1000)
          this.form.endKm = Number(res.data.zdzh / 1000)
          this.originalStartKm = Number(res.data.qdzh / 1000)
          this.originalEndKm = Number(res.data.zdzh / 1000)
          this.form.totalKm = this.originalEndKm - this.originalStartKm
          let [kmPart, mPart] = this.originalStartKm.toString().split('.')
          this.car.kmPart = Number(kmPart)
          mPart = '0.' + mPart
          mPart = (mPart - 0) * 1000
          this.car.mPart = Number(mPart)
          // let directionOptions = this.dict.type.sys_route_direction
          this.bridges = res.data.other.bridges.map(el => {
            const startKm = Number(el.startStake) / 1000
            const endKm = Number(el.endStake) / 1000
            const dir = el.direction === "1" ? "up" : "down"
            return {
              id: el.id,
              name: el.asseName,
              startKm: startKm,
              endKm: endKm,
              direction: dir,
              style: {
                left: `${(startKm - this.form.startKm) * this.form.kmWidth}px`,
                width: `${(endKm - startKm) * this.form.kmWidth}px`,
                top: `${dir === 'up' ? 8 : 50}%`,
                height: `42%`
              }
            }
          })
          const maintenanceTypes = this.dict.type.base_maintenance_type
          const projectTypes = this.dict.type.base_project_type
          let laneOptions = this.dict.type.lane
          this.maintenanceAreas = res.data.other.maintenanceManage.map(el => {
            const startKm = Number(el.startStake) / 1000
            const endKm = Number(el.endStake) / 1000
            const dir = el.direction === "1" ? "up" : "down"
            const maintenanceTypeName = maintenanceTypes.find(item => item.value === el.maintenanceType)?.label
            const projectTypeName = projectTypes.find(item => item.value === el.projectType)?.label
            const laneName = laneOptions.find(item => item.value === el.lane)?.label
            let [start1, start2] = startKm.toString().split('.')
            const startStakeName = `K${start1 || 0} + ${start2 || 0}`
            let [end1, end2] = endKm.toString().split('.')
            const endStakeName = `K${end1 || 0} + ${end2 || 0}`
            return {
              ...el,
              description: el.measure,
              startKm: startKm,
              endKm: endKm,
              direction: dir,
              directionName: dir === "up" ? "上行" : "下行",
              lane: el.lane,
              maintenanceTypeName: maintenanceTypeName,
              projectTypeName: projectTypeName,
              laneName: laneName,
              startStakeName: startStakeName,
              endStakeName: endStakeName,
              style: {
                left: `${(startKm - this.form.startKm) * this.form.kmWidth}px`,
                width: `${(endKm - startKm) * this.form.kmWidth}px`,
                top: `${(dir === 'up' ? 8 : 50) + (el.lane - 1) * this.laneHeight}%`,
                height: `${this.laneHeight}%`,
                borderColor: "rgba(255, 165, 0, 0.3)"
              }
            }
          })
          this.tunnels = res.data.other.tunnel.map(el => {
            const startKm = Number(el.startStake) / 1000
            const endKm = Number(el.endStake) / 1000
            const dir = el.direction === "1" ? "up" : "down"
            const lightCount = Math.max(5, Math.floor((endKm - startKm) * this.form.kmWidth / 30))
            let topLights = []
            let bottomLights = []
            for (let i = 0; i < lightCount; i++) {
              topLights.push({ style: { left: `${(i / (lightCount - 1)) * 100}%` } })
              bottomLights.push({ style: { left: `${(i / (lightCount - 1)) * 100}%` } })
            }
            return {
              id: el.id,
              tunnelCode: el.asseCode,
              name: el.asseName,
              startKm: startKm,
              endKm: endKm,
              direction: dir,
              topLights: topLights,
              bottomLights: bottomLights,
              style: {
                left: `${(startKm - this.form.startKm) * this.form.kmWidth}px`,
                width: `${(endKm - startKm) * this.form.kmWidth}px`,
                top: `${dir === 'up' ? 8 : 50}%`,
                height: `42%`
              }
            }
          })
        }
      }).finally(() => {
        this.loading = false
      })
    },
    // 启动/暂停按钮
    handleAnimation() {
      this.isPaused = !this.isPaused
      if (!this.isPaused) {
        this.startAnimation()
      }
    },
    // 重置按钮
    handleRemake() {
      this.isEnd = false
      this.isPaused = true
      let [kmPart, mPart] = this.originalStartKm.toString().split('.')
      this.car.kmPart = Number(kmPart)
      mPart = '0.' + mPart
      mPart = (mPart - 0) * 1000
      this.car.mPart = Number(mPart)
      this.carLeft = "0px"
      this.highwayLeft = "0px"
      this.currentArea = null
      this.animationId = null
      this.pausedProgress = 0
      this.startTime = null
      this.lastTimestamp = null
    },
    // 动画定时器
    startAnimation() {
      this.startTime = null
      this.lastTimestamp = null
      this.animationId = requestAnimationFrame(this.animate)
    },
    // 动画功能
    animate(timestamp) {
      const viewportWidth = document.querySelector('.viewport').offsetWidth
      const totalWidth = this.form.totalKm * this.form.kmWidth
      const carWidth = this.isBig ? 120 : 60; // 假设小车宽度为60px
      const specialAreas = document.querySelectorAll('.bridge, .tunnel, .maintenance-area')
      if (!this.startTime) this.startTime = timestamp
      if (!this.lastTimestamp) this.lastTimestamp = timestamp
      const deltaTime = timestamp - this.lastTimestamp
      if (!this.isPaused) {
        const progressPerSecond = this.form.speed / (this.form.totalKm * 3600)
        this.pausedProgress += progressPerSecond * (deltaTime / 1000)
        if (this.pausedProgress < 1) {
          const distance = totalWidth * this.pausedProgress
          const lastMilestonePosition = totalWidth - viewportWidth
          this.updateCarPosition(distance, viewportWidth, lastMilestonePosition, carWidth)
          this.checkSpecialAreas(distance, specialAreas)
          this.updateCarInfo(distance)
          this.lastTimestamp = timestamp
          this.animationId = requestAnimationFrame(this.animate)
        } else { // 动画结束
          const distance = totalWidth * this.pausedProgress
          if(viewportWidth<totalWidth){
            this.highwayLeft = `${-(totalWidth - viewportWidth)}px`
            this.carLeft = `${viewportWidth - carWidth}px`
          }
          
          this.isPaused = true
          this.isEnd = true
          this.updateCarInfo(distance)
        }
      }
    },
    // 更新小车位置
    updateCarPosition(distance, viewportWidth, lastMilestonePosition, carWidth) {
      if (distance < viewportWidth / 2 - carWidth / 2) {
        this.carLeft = `${distance}px`
        this.highwayLeft = '0px'
      } else if (distance < lastMilestonePosition) {
        this.carLeft = `${viewportWidth / 2 - carWidth / 2}px`
        this.highwayLeft = `${-(distance - viewportWidth / 2 + carWidth / 2)}px`
      } else {
        this.highwayLeft = `${-lastMilestonePosition}px`
        const carPosition = viewportWidth / 2 - carWidth / 2 + (distance - lastMilestonePosition)
        this.carLeft = `${Math.min(viewportWidth - carWidth, carPosition)}px`
      }
    },
    // 检查小车是否经过特殊区域
    checkSpecialAreas(distance, specialAreas) {
      this.currentArea = null
      specialAreas.forEach(area => {
        const areaLeft = parseFloat(area.style.left)
        const areaWidth = parseFloat(area.style.width)
        const carPosition = distance
        if (carPosition >= areaLeft && carPosition <= areaLeft + areaWidth) {
          const areaName = area.querySelector('.bridge-name, .tunnel-name, .maintenance-label').textContent
          this.currentArea = areaName
        }
      })
    },
    // 更新小车信息
    updateCarInfo(distance) {
      const currentKm = this.form.startKm * 1000 + (distance / this.form.kmWidth) * 1000
      const kmPart = Math.floor(currentKm / 1000)
      const mPart = (currentKm % 1000).toFixed(0)
      let endKm = this.originalEndKm * 1000
      if (currentKm > endKm) {
        let [kmPart, mPart] = this.originalEndKm.toString().split('.')
        this.car.kmPart = Number(kmPart)
        this.car.mPart = Number(mPart)
      } else {
        this.car.kmPart = kmPart
        this.car.mPart = mPart
      }
    },
    // 监听起点桩号输入事件
    onStartKm() {
      if (this.form.startKm > this.form.endKm) {
        this.form.startKm = this.originalStartKm
        this.$message.error('起点不能大于终点')
        return
      } else if (this.form.startKm < this.originalStartKm) {
        this.form.startKm = this.originalStartKm
        this.$message.error('请输入正确起点桩号')
        return
      }
      this.form.totalKm = this.form.endKm - this.form.startKm
    },
    // 监听终点桩号输入事件
    onEndKm(val) {
      if (val < this.form.startKm) {
        this.form.endKm = this.originalEndKm
        this.$message.error('终点不能小于起点')
        return
      } else if (val > this.originalEndKm) {
        this.form.endKm = this.originalEndKm
        this.$message.error('请输入正确终点桩号')
        return
      }
      this.form.totalKm = this.form.endKm - this.form.startKm
    },
    // 点击桥梁区域事件
    handleBridge(data) {
      // 先判断是先暂停再点击，还是先点击再暂停
      if (this.isPaused) {
        this.btnFlag = true
      } else {
        this.btnFlag = false
        this.isPaused = true
      }
      let obj = {
        data: data,
        componentUrl: './baseData/bridge/baseInfo/archives/index.vue',
        componentName: 'BaseInfo',
        query: {
          bridgeName: data.name,
          id: data.id,
        }
      }
      this.$emit('showInfo', obj)
    },
    // 点击隧道区域事件
    handleTunnel(data) {
      // 先判断是先暂停再点击，还是先点击再暂停
      if (this.isPaused) {
        this.btnFlag = true
      } else {
        this.btnFlag = false
        this.isPaused = true
      }
      let obj = {
        data: data,
        componentUrl: './baseData/tunnel/baseInfo/archives/index.vue',
        componentName: 'BaseInfo',
        query: {
          tunnelName: data.name,
          id: data.id,
          tunnelCode: data.tunnelCode
        }
      }
      this.$emit('showInfo', obj)
    },
    // 点击养护区域事件
    handleMaintenanceAreas(data) {
      // 先判断是先暂停再点击，还是先点击再暂停
      if (this.isPaused) {
        this.btnFlag = true
      } else {
        this.btnFlag = false
        this.isPaused = true
      }
      this.maintenanceInfo = data
      this.openMaintenanceInfo = true
    },
    // 养护区域鼠标移入
    changeColor(item) {
      item.style.borderColor = "red"
    },
    // 养护区域鼠标移出
    resetColor(item) {
      item.style.borderColor = "rgba(255, 165, 0, 0.3)"
    }
  },
  computed: {
    laneHeight() {
      return 42 / Math.floor(this.form.laneCount / 2)
    },
    lanesOptions() {
      return Math.floor(this.form.laneCount / 2)
    },
    highwayWidth() {
      return `${this.form.totalKm * this.form.kmWidth}px`
    },
    lanes() {
      this.form.lane = 1
      const lanes = []
      for (let i = 1; i < this.form.laneCount; i++) {
        lanes.push({
          style: {
            top: `${8 + ((i / this.form.laneCount) * 84)}%`
          }
        })
      }
      return lanes
    },
    milestones() {
      let arr = []
      for (let i = Math.floor(this.form.startKm); i <= Math.ceil(this.form.endKm); i++) {
        if (i >= this.form.startKm && i <= this.form.endKm) {
          const kmPart = Math.floor(i)
          arr.push({
            id: i,
            zh: kmPart,
            style: {
              left: `${(i - this.form.startKm) * this.form.kmWidth}px`
            }
          })
        }
      }
      return arr
    },
    carTop() {
      const lanesPerDirection = Math.floor(this.form.laneCount / 2)
      if (this.form.direction === 'up') {
        this.car.top = `${8 + ((lanesPerDirection - this.form.lane + 0.5) / lanesPerDirection) * 42}%`
      } else {
        this.car.top = `${50 + ((this.form.lane - 0.5) / lanesPerDirection) * 42}%`
      }
      return this.car.top
    },
  },
  watch: {
    "roadData.id"() {
      this.handleRemake()
      this.getSpecialList()
    },
    "form.kmWidth"() {
      this.bridges.map(el => {
        el.style.left = `${(el.startKm - this.form.startKm) * this.form.kmWidth}px`
        el.style.width = `${(el.endKm - el.startKm) * this.form.kmWidth}px`
      })
      this.maintenanceAreas.map(el => {
        el.style.left = `${(el.startKm - this.form.startKm) * this.form.kmWidth}px`
        el.style.width = `${(el.endKm - el.startKm) * this.form.kmWidth}px`
      })
      this.tunnels.map(el => {
        const lightCount = Math.max(5, Math.floor((el.endKm - el.startKm) * this.form.kmWidth / 30))
        let topLights = []
        let bottomLights = []
        for (let i = 0; i < lightCount; i++) {
          topLights.push({ style: { left: `${(i / (lightCount - 1)) * 100}%` } })
          bottomLights.push({ style: { left: `${(i / (lightCount - 1)) * 100}%` } })
        }
        el.topLights = topLights
        el.bottomLights = bottomLights
        el.style.left = `${(el.startKm - this.form.startKm) * this.form.kmWidth}px`
        el.style.width = `${(el.endKm - el.startKm) * this.form.kmWidth}px`
      })
    },
    "form.laneCount"() {
      this.maintenanceAreas.map(el => {
        el.style.top = `${(el.direction === 'up' ? 8 : 50) + (el.lane - 1) * this.laneHeight}%`,
          el.style.height = `${this.laneHeight}%`
      })
    },
    openMaintenanceInfo(val) {
      if (!val) {
        if (this.btnFlag) {
          this.isPaused = true
        } else {
          this.isPaused = false
          this.startAnimation()
        }
      }
    }
  },
  beforeDestroy() {
    window.$Bus.$off("closeInfoForAnimation");
  }
}
</script>

<style lang="scss" scoped>
@import "@/assets/styles/utils.scss";
.road-animation {
  width: 100%;
  position: absolute;
  bottom: 0 !important;
  left: 0;
  z-index: 99;
  background: linear-gradient(90deg,
      rgba(2, 10, 30, 0.8) 0%,
      rgba(2, 10, 30, 0.8) 100%);
  box-shadow: inset 0px 0px 10px 0px rgba(35, 134, 255, 0.5);
  border-radius: 10px 10px 0 0;
  color: #ffffff;
  border: 1px solid rgb(6, 135, 255);
  padding: vwpx(20px);

  .header {
    width: 100%;
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: vwpx(30px);
  }

  .body-close {
    position: absolute;
    top: vwpx(10px);
    right: vwpx(10px);
    z-index: 100;
    font-size: vwpx(36px);
    cursor: pointer;
  }

  .setting-button {
    // position: absolute;
    // top: vwpx(82px);
    // left: vwpx(20px);
    font-size: vwpx(24px);
    margin-right: 10px;
  }

  .control-buttons {
    // text-align: center;
    padding: vwpx(20px) 0;
    // padding-left: vwpx(150px);
    .el-button {
      font-size: vwpx(24px);
    }
  }

  .viewport {
    cursor: grab;
    position: relative;
    width: 100%;
    height: vwpx(800px);
    overflow: hidden;
    background-color: #555;
    border-radius: 8px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);

    .highway {
      height: 100%;
      background-color: #555;
      position: absolute;
      left: 0;
      transition: left 0.1s linear;
    }

    .lane-divider {
      position: absolute;
      width: 100%;
      height: 10px;
      background: repeating-linear-gradient(90deg,
          transparent,
          transparent 20px,
          #fff 20px,
          #fff 40px);
    }

    .yellow-line {
      position: absolute;
      top: 50%;
      width: 100%;
      height: 20px;
      background-color: #FFD700;
      transform: translateY(-50%);
    }

    .emergency-lane {
      position: absolute;
      width: 100%;
      height: 8%;
      background-color: #555;
      z-index: 5;
    }

    .emergency-divider {
      position: absolute;
      width: 100%;
      height: 5px;
      background-color: white;
      z-index: 6;
    }

    .milestone {
      position: absolute;
      top: 50%;
      transform: translate(-50%, -50%);
      background-color: #008000;
      color: #fff;
      padding: 2px 5px;
      border-radius: 10px;
      font-weight: bold;
      font-size: vwpx(24px);
      z-index: 15;
    }

    .bridge {
      cursor: pointer;
      position: absolute;
      background: linear-gradient(to bottom, rgba(160, 160, 160, 0.6), rgba(128, 128, 128, 0.6));
      border-top: 4px solid rgba(192, 192, 192, 0.8);
      border-bottom: 4px solid rgba(192, 192, 192, 0.8);
      z-index: 8;
      box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
    }

    .bridge::before,
    .bridge::after {
      content: '';
      position: absolute;
      width: 100%;
      height: 8px;
      background: repeating-linear-gradient(90deg,
          rgba(255, 255, 0, 0.8),
          rgba(255, 255, 0, 0.8) 30px,
          rgba(0, 0, 0, 0.8) 30px,
          rgba(0, 0, 0, 0.8) 60px);
    }

    .bridge::before {
      top: 0;
    }

    .bridge::after {
      bottom: 0;
    }

    .bridge-name {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      color: #000;
      background-color: rgba(255, 255, 255, 0.8);
      padding: 5px 10px;
      border-radius: 5px;
      font-weight: bold;
      font-size: vwpx(28px) !important;
      white-space: nowrap;
      z-index: 9;
    }

    .tunnel {
      cursor: pointer;
      position: absolute;
      background-color: rgba(34, 34, 34, 0.6);
      z-index: 8;
      overflow: hidden;
      border: none;
    }

    .tunnel::before {
      content: none;
      /* 移除上方的线条 */
    }

    .tunnel::after {
      content: none;
      /* 保持底部没有线条 */
    }

    .tunnel-entrance {
      position: absolute;
      top: 0;
      bottom: 0;
      width: 20px;
      background: rgba(68, 68, 68, 0.8);
    }

    .tunnel-entrance.left {
      left: 0;
      border-right: 5px solid #ffff00;
    }

    .tunnel-entrance.right {
      right: 0;
      border-left: 5px solid #ffff00;
    }

    .tunnel-lights {
      position: absolute;
      left: 30px;
      right: 30px;
      height: 10px;
    }

    .tunnel-lights.top {
      top: 20px;
    }

    .tunnel-lights.bottom {
      bottom: 20px;
    }

    .tunnel-light {
      position: absolute;
      width: 10px;
      height: 10px;
      background-color: #ffff99;
      border-radius: 50%;
      box-shadow: 0 0 10px 5px rgba(255, 255, 153, 0.7);
      transform: translateX(-50%);
    }

    .car {
      position: absolute;
      // width: 60px;
      // height: 30px;
      width: vwpx(120px);
      height: vwpx(60px);
      background-image: url('car.png');
      background-size: cover;
      background-position: center;
      z-index: 20;
      left: 30px;
      transform: translateY(-50%);
    }

    .car-info {
      position: absolute;
      top: -40px;
      left: 50%;
      transform: translateX(-50%);
      background-color: rgba(0, 0, 0, 0.7);
      color: white;
      padding: 5px 10px;
      border-radius: 5px;
      font-size: vwpx(24px);
      white-space: nowrap;
    }

    .following-tooltip {
      --tooltip-arrow: left;
      position: absolute;
      top: 50%;
      left: 100%;
      /* 将气泡放在小车右侧 */
      transform: translateY(-50%);
      background-color: rgba(0, 0, 0, 0.7);
      color: white;
      padding: 5px 10px;
      border-radius: 5px;
      font-size: vwpx(24px);
      white-space: nowrap;
      z-index: 25;
      pointer-events: none;
    }

    .following-tooltip::after {
      content: '';
      position: absolute;
      top: 50%;
      border-width: 5px;
      border-style: solid;
      transform: translateY(-50%);
    }

    .following-tooltip[style*="--tooltip-arrow: left"]::after {
      right: 100%;
      border-color: transparent rgba(0, 0, 0, 0.7) transparent transparent;
    }

    .following-tooltip[style*="--tooltip-arrow: right"]::after {
      left: 100%;
      border-color: transparent transparent transparent rgba(0, 0, 0, 0.7);
    }

    .maintenance-area {
      cursor: pointer;
      position: absolute;
      background-color: rgba(255, 165, 0, 0.3);
      z-index: 7;
      border: 2px solid rgba(255, 140, 0, 0.8);
    }

    .maintenance-label {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      background-color: rgba(255, 255, 255, 0.8);
      color: black;
      padding: 5px 10px;
      border-radius: 5px;
      font-weight: bold;
      font-size: vwpx(28px);
      white-space: nowrap;
      z-index: 9;
    }

    .bridge-name,
    .tunnel-name,
    .maintenance-label {
      color: #000;
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      background-color: rgba(255, 255, 255, 0.8);
      padding: 5px 10px;
      border-radius: 5px;
      font-weight: bold;
      font-size: vwpx(28px);
      white-space: nowrap;
      z-index: 9;
    }

  }
}

.dialog-detail {
  ::v-deep .el-dialog {
    margin: 0 auto 0;
    background: rgba(4, 17, 48, 0.8);
    box-shadow: inset 0px 0px 10px 0px #3662ec;
    border-radius: 10px 10px 10px 10px;
    border: 1px solid #0687ff;
    color: #ffffff !important;

    .el-dialog__header {
      border-bottom: none;
      padding: 10px 15px !important;

      .el-dialog__title {
        color: #ffffff;
      }

      .el-dialog__headerbtn {
        color: #ffffff;
        top: 10px;
      }
    }

    .el-dialog__body {
      padding: 10px;
      color: #ffffff !important;
    }
  }
}

::v-deep .el-descriptions {

  .el-descriptions__body {
    background-color: rgba(4, 17, 48, 0.6) !important;
    color: #ffffff;
  }

  .is-bordered .el-descriptions-item__cell {
    border: 1px solid rgba(1, 102, 254, 0.4);
  }

  .el-descriptions-item__label.is-bordered-label {
    background-color: rgba(4, 17, 48, 0.6) !important;
    color: #ffffff;
    font-size: 1.4vh;
  }

  .el-descriptions-item__content {
    font-size: 1.3vh;
  }
}
</style>