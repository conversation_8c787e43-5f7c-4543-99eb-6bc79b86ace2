<template>
  <div>
    <el-row :gutter="15">
      <el-col :span="8" class="mb8" v-if="!isShow">
        <el-upload
            ref="uploadRef"
            style="width:100px !important;"
            :action="upload.url"
            :file-list="fileList"
            :headers="upload.headers"
            :show-file-list="false"
            :on-progress="handleFileUploadProgress"
            :on-remove="handleUpdFileList"
            :on-success="handleFileSuccess"
            multiple
        >
          <el-button
              icon="el-icon-plus"
              size="mini"
              type="primary"
          >新增附件
          </el-button>
        </el-upload>
      </el-col>
      <el-col :span="24">
        <el-table v-adjust-table
            v-loading="loading"
            :data="tableData"
            border
            size="mini"
            style="width: 100%"
            @selection-change="handleSelectionChange"
        >
          <el-table-column align="center" label="序号" type="index"/>
          <el-table-column align="center" label="文件名称" prop="originalFilename">
          </el-table-column>
          <el-table-column align="center" label="文件类型" prop="contentType">
          </el-table-column>
          <el-table-column align="center" label="预览" prop="annexPath">
            <template slot-scope="props">
              <el-button type="text" @click="handleView(props.row)">查看</el-button>
            </template>
          </el-table-column>
          <el-table-column align="center" label="操作" prop="annexPath" v-if="!isShow">
            <template slot-scope="props">
              <el-button type="text" @click="handleDelete(props.row)">删除</el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-col>
    </el-row>
    <el-dialog title="附件预览" destroy-on-close append-to-body modal-append-to-body :visible.sync="openFile" width="80%" v-if="openFile">
      <file-upload v-model="filePath" :forView="true"></file-upload>
    </el-dialog>
  </div>
</template>
<script>
import {getToken} from "@/utils/auth";
import Dialog from "@/components/Dialog/index.vue";
import {addFiles, deleteFiles, listFiles} from "@/api/dailyMaintenance/constructionOrder/noticeDraft";
import {getFile} from "@/api/file";

export default {
  name: 'fileInfo',
  components: {Dialog},
  props: {
    inId: {
      type: String,
      default: ''
    },
    isShow: {
      type: Boolean,
      default: false
    }
  },
  dicts: ['contract_annex_type', 'contract_file_type'],
  data() {
    return {
      loading: false,
      openFlag: false,
      tableData: [],
      queryParams: {
        annexType: null,
        fileType: null
      },
      fileForm: {
        annexType: null,
        fileType: null,
        remark: null
      },
      openFile: false,
      filePath: '',
      // 用户导入参数
      upload: {
        // 是否禁用上传
        isUploading: false,
        // 设置上传的请求头部
        headers: {Authorization: "Bearer " + getToken()},
        // 上传的地址
        url: process.env.VUE_APP_BASE_API + "/ruoyi-file/upload?platform=ylzx"
      }
    };
  },
  computed: {},
  watch: {},
  created() {
  },
  mounted() {
    this.getList()
  },
  methods: {
    handleView(row) {
      this.filePath = row.ownerId
      this.openFile = true
    },
    getList() {
      this.loading = true
      this.tableData = []
      listFiles({inId: this.inId}).then(async res => {
        for (let i = 0; i < res.rows.length; i++) {
          const file = await getFile({ownerId:res.rows[i].fileId})
          if (file.code == 200) {
            file.data.id = res.rows[i].id
            this.tableData.push(file.data)
          }
        }
        this.loading = false
      })
    },
    // 文件上传中处理
    handleFileUploadProgress(event, file, fileList) {
      this.upload.isUploading = true;
    },
    // 文件上传成功处理
    handleFileSuccess(response, file, fileList) {
      this.upload.isUploading = false;
      this.fileList = fileList
      this.handleAddFile()
    },
    handleUpdFileList(file, fileList) {
      this.fileList = fileList
    },
    handleDelete(row) {
      const that = this
      this.$modal.confirm('是否删除').then(function () {
        deleteFiles(row.id).then(res => {
          that.$modal.msgSuccess('删除成功')
          that.getList()
        })
      })
    },
    async handleAddFile() {
      this.loading = true
      for (let key in this.fileList) {
        const file = this.fileList[key]
        const fileModel = {
          inId: this.inId,
          fileId: file.response?.data?.ownerId,
          registerType: 9
        }
        await addFiles(fileModel)
      }
      // 清空已上传的文件
      this.$refs.uploadRef.clearFiles()
      this.getList()
    }
  },
};
</script>
<style lang="scss" scoped>
::v-deep .el-upload--text {
  width: 100% !important;
}

::v-deep .el-link {
  width: 200px;
  white-space: nowrap;
  overflow: hidden !important;
  text-overflow: ellipsis;
  display: inline-block;
}
</style>
<style lang="scss" scoped>
@import "@/assets/styles/business.scss";
</style>
