<template>
  <div class="road-interflow-edit">
    <el-tabs>
      <el-tab-pane label="任务清单">
        <el-row :gutter="15">
          <el-col :span="24">
            <el-form
              ref="queryForm"
              :model="queryParams"
              size="mini"
              :inline="true"
              label-width="68px"
            >
            <el-form-item>
                <el-input
                  style="width: 190px"
                  placeholder="任务单名称"
                  v-model="queryParams.constructionName"
                ></el-input>
              </el-form-item>
              <el-form-item>
                <el-input
                  style="width: 190px"
                  placeholder="任务单编码"
                  v-model="queryParams.constructionCode"
                ></el-input>
              </el-form-item>
              <el-form-item>
                <el-button
                  type="primary"
                  icon="el-icon-search"
                  size="mini"
                  @click="handleQueryLeft"
                  >搜索</el-button
                >
                <el-button
                  icon="el-icon-refresh"
                  size="mini"
                  @click="resetQuery"
                  >重置</el-button
                >
              </el-form-item>
            </el-form>
          </el-col>
        </el-row>
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button
              type="primary"
              icon="el-icon-view"
              size="mini"
              @click="dialogVisible = true"
              >操作记录
            </el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button
              type="warning"
              icon="el-icon-download"
              size="mini"
              @click="exportListLeft"
              >导出清单
            </el-button>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <div class="draggable">
              <el-table v-adjust-table
                size="mini"
                style="width: 100%"
                v-loading="loading"
                border
                :data="tableDataLeft"
                row-key="id"
                height="300"
                ref="dataTable"
                @row-click="handleClickRow"
                @selection-change="handleSelectionChange"
                @expand-change="loadData"
                stripe
                highlight-current-row
              >
                <el-table-column type="expand">
                  <template slot-scope="props">
                    <el-table v-adjust-table
                      :data="props.row.methodList"
                      style="width: 100%"
                      v-loading="methodLoading"
                    >
                      <el-table-column prop="" align="center" label="">
                      </el-table-column>
                      <el-table-column
                        prop="schemeCode"
                        align="center"
                        label="子目号"
                      >
                      </el-table-column>
                      <el-table-column
                        prop="schemeName"
                        align="center"
                        label="养护方法"
                      >
                      </el-table-column>
                      <el-table-column
                        prop="calcDesc"
                        align="center"
                        label="计算式"
                      >
                      </el-table-column>
                      <el-table-column
                        prop="num"
                        align="center"
                        label="方法数量"
                      >
                      </el-table-column>
                      <el-table-column
                        prop="unit"
                        align="center"
                        label="方法单位"
                      >
                      </el-table-column>
                      <el-table-column prop="price" align="center" label="单价">
                      </el-table-column>
                      <el-table-column
                        prop="amount"
                        align="center"
                        label="金额"
                      >
                      </el-table-column>
                    </el-table>
                    <pagination
                      v-show="props.row.childTotal > 0"
                      :total="props.row.childTotal"
                      :page.sync="props.row.queryParams.pageNum"
                      :limit.sync="props.row.queryParams.pageSize"
                      @pagination="loadData(props.row)"
                    />
                  </template>
                </el-table-column>
                <el-table-column type="selection" width="50" align="center" />
                <el-table-column
                  label="序号"
                  align="center"
                  type="index"
                  width="50"
                />
                <template v-for="(column, index) in columnsLeft">
                  <el-table-column
                    :label="column.label"
                    v-if="column.visible"
                    align="center"
                    :prop="column.field"
                    :width="column.width"
                  >
                    <template slot-scope="scope">
                      <dict-tag
                        v-if="column.dict"
                        :options="dict.type[column.dict]"
                        :value="scope.row[column.field]"
                      />
                      <template v-else-if="column.slots">
                        <RenderDom
                          :row="scope.row"
                          :index="index"
                          :render="column.render"
                        />
                      </template>
                      <span v-else-if="column.isTime">{{
                        parseTime(scope.row[column.field], "{y}-{m}-{d}")
                      }}</span>
                      <span v-else>{{ scope.row[column.field] }}</span>
                    </template>
                  </el-table-column>
                </template>
              </el-table>
              <pagination
                v-show="totalLeft > 0"
                :total="totalLeft"
                :page.sync="queryParams.pageNum"
                :limit.sync="queryParams.pageSize"
                @pagination="handleQueryLeft"
              />
            </div>
          </el-col>
        </el-row>
      </el-tab-pane>
      <el-tab-pane label="报价清单">
        <el-row :gutter="15">
          <el-col :span="24">
            <el-row :gutter="10" class="mb8">
              <el-col :span="1.5">
                <el-button
                  type="primary"
                  icon="el-icon-download"
                  size="mini"
                  @click="exportListRight"
                  >导出清单
                </el-button>
              </el-col>
            </el-row>
          </el-col>
          <el-col :span="24">
            <div class="draggable">
              <el-table v-adjust-table
                size="mini"
                style="width: 100%"
                v-loading="loading"
                border
                :data="tableDataRight"
                row-key="id"
                height="300"
                ref="dataTable2"
                stripe
                highlight-current-row
              >
                <el-table-column
                  label="序号"
                  align="center"
                  type="index"
                  width="50"
                />
                <template v-for="(column, index) in columnsRight">
                  <el-table-column
                    :label="column.label"
                    v-if="column.visible"
                    align="center"
                    :prop="column.field"
                  >
                    <template slot-scope="scope">
                      <dict-tag
                        v-if="column.dict"
                        :options="dict.type[column.dict]"
                        :value="scope.row[column.field]"
                      />
                      <template v-else-if="column.slots">
                        <RenderDom
                          :row="scope.row"
                          :index="index"
                          :render="column.render"
                        />
                      </template>
                      <span v-else-if="column.isTime">{{
                        parseTime(scope.row[column.field], "{y}-{m}-{d}")
                      }}</span>
                      <span v-else>{{ scope.row[column.field] }}</span>
                    </template>
                  </el-table-column>
                </template>
              </el-table>
              <pagination
                v-show="totalRight > 0"
                :total="totalRight"
                :page.sync="queryParams.pageNum"
                :limit.sync="queryParams.pageSize"
                @pagination="handleQueryRight"
              />
            </div>
          </el-col>
        </el-row>
      </el-tab-pane>
    </el-tabs>
    <el-dialog
      title="事件信息"
      destroy-on-close
      :visible.sync="dialogVisible"
      width="50%"
      append-to-body
      modal-append-to-body
    >
      <event-info></event-info>
    </el-dialog>
  </div>
</template>
<script>
import { listEvent, listEventBySettleId, listMethod } from "@/api/regularTesting/metering/middleApplication";
import EventInfo from "@/views/dailyMaintenance/component/eventTreeInfo.vue";
export default {
  dicts: [],
  components: { EventInfo },
  data() {
    return {
      queryParams: {
        pageNum: 1,
        pageSize: 10,
      },
      loading: false,
      tableDataLeft: [],
      totalLeft: 0,
      columnsLeft: [
        {
          key: 0,
          width: 100,
          field: "constructionCode",
          label: `任务单编号`,
          visible: true,
        },
        {
          key: 1,
          field: "constructionName",
          label: `任务单名称`,
          visible: true,
        },
        {
          key: 2,
          field: "projectName",
          label: `项目名称`,
          visible: true,
        },
        {
          key: 3,
          field: "maiSecName",
          label: `路段名称`,
          visible: true,
        },
        {
          key: 4,
          field: "domainName",
          label: `管养单位`,
          visible: true,
        },
        {
          key: 5,
          field: "checkDomainName",
          label: `检测单位`,
          visible: true,
        },
        {
          key: 6,
          field: "checkConName",
          label: `检测合同`,
          visible: true,
        },
        {
          key: 7,
          field: "endTime",
          label: `完工日期`,
          visible: true,
        },
        {
          key: 8,
          width: 100,
          field: "sumFund",
          label: `金额`,
          visible: true,
        },
      ],
      tableDataRight: [],
      selectIds: [],
      totalRight: 0,
      columnsRight: [
        { key: 0, field: "schemeCode", label: `子目号`, visible: true },
        { key: 1, field: "schemeName", label: `子目名称`, visible: true },
        { key: 2, field: "price", label: `单价`, visible: true },
        { key: 3, field: "calcDesc", label: `计算式`, visible: true },
        { key: 4, field: "num", label: `数量`, visible: true },
        { key: 5, field: "amount", label: `金额`, visible: true },
        { key: 5, field: "remark", label: `备注`, visible: true },
      ],
      methodLoading: true,
      dialogVisible: false,
    };
  },
  props: {
    maiSecId: {
      type: String,
      default: "",
    },
    calcId: {
      type: String,
      default: "",
    },
  },
  watch: {
    calcId: {
      handler(val) {
        if (val) {
          this.handleQueryLeft();
          this.handleQueryRight();
        }
      },
    },
  },
  created() {
    this.handleQueryLeft();
    this.handleQueryRight();
  },
  methods: {
    handleQueryLeft() {
      this.loading = true;
      this.queryParams.calcId = this.calcId;
      listEvent(this.queryParams).then((res) => {
        this.tableDataLeft = res.rows;
        this.totalLeft = res.total;
        this.tableDataLeft.forEach(item => {
          item.queryParams = {
            settleId: item.settleId,
            detailId: item.detailId,
            pageNum: 1,
            pageSize: 10,
          }
          item.childTotal = 0
        });
        this.loading = false;
      });
    },
    handleQueryRight() {
      this.loading = true;
      listMethod({ calcId: this.calcId }).then((res) => {
        this.tableDataRight = res.rows;
        this.totalRight = res.total;
        this.loading = false;
      });
    },
    exportListLeft() {
      if (this.selectIds.length > 0) {
        this.queryParams.ids = this.selectIds;
      }
      this.download(
        "manager/calc/check/intermediate/detail/export",
        { ...this.queryParams },
        `intermediate_detail_${new Date().getTime()}.xlsx`,
        {
          headers: { "Content-Type": "application/json;" },
          parameterType: "body",
        }
      );
    },
    exportListRight() {
      this.download(
        "manager/calc/check/settle/method/export",
        { calcId: this.calcId },
        `method_${new Date().getTime()}.xlsx`,
        {
          headers: { "Content-Type": "application/json;" },
          parameterType: "body",
        }
      );
    },
    handleClickRow(e) {
      e.isSelected = !e.isSelected;
      this.$refs.dataTable.toggleRowSelection(e);
    },
    // 选中
    handleSelectionChange(e) {
      this.selectIds = e.map((item) => item.detailId);
    },
    loadData(row) {
      this.methodLoading = true;
      listEventBySettleId(row.queryParams).then((res) => {
        this.$set(row, "methodList", res.rows);
        this.$set(row, "childTotal", res.total);
        this.methodLoading = false;
      });
    },
    resetQuery() {
      this.queryParams = {
        pageNum: 1,
        pageSize: 10,
      };
    },
  },
};
</script>
<style scoped lang="scss"></style>
<style lang="scss" scoped>
@import "@/assets/styles/business.scss";
</style>
