<template>
  <div class="app-container">
    <el-row :gutter="20">
      <!--筛选区开始-->
      <el-col :span="24" :xs="24">
        <el-row>
          <el-col :span="24">
            <el-form ref="queryForm" :inline="true" :model="queryParams" label-width="68px" size="small">
              <el-form-item>
                <el-input v-model="queryParams.projName" placeholder="项目名称" style="width: 240px"></el-input>
              </el-form-item>
              <el-form-item>
                <el-input v-model="queryParams.projCode" placeholder="项目编码" style="width: 240px"></el-input>
              </el-form-item>
              <el-form-item>
                <el-input v-model="queryParams.code" placeholder="赔偿协议编号" style="width: 240px"></el-input>
              </el-form-item>
              <el-form-item>
                <dict-select v-model="queryParams.isClaim" type="is_claim" placeholder="是否赔付完成" style="width: 240px"></dict-select>
              </el-form-item>
              <el-form-item>
                <el-input v-model="queryParams.claimBy" placeholder="索赔人" style="width: 240px"></el-input>
              </el-form-item>
              <el-form-item>
                <el-button icon="el-icon-search" size="mini" type="primary" @click="handleQuery">搜索</el-button>
                <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
              </el-form-item>
            </el-form>
          </el-col>
        </el-row>
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button
              icon="el-icon-plus"
              size="mini"
              type="primary"
              @click="handleAdd"
            >新增
            </el-button>
          </el-col>
        </el-row>
        <div class="tableDiv">
          <el-table v-adjust-table
            ref="dataTable"
            v-loading="loading"
            :data="tableData"
            :height="'calc(100vh - 330px)'"
            border
            highlight-current-row
            row-key="id"
            size="mini"
            @row-click="handleClickRow"
            stripe
            style="width: 100%"
          >
            <el-table-column
              align="center"
              label="序号"
              type="index"
              width="50"
            />
            <template v-for="(column,index) in columns">
              <el-table-column v-if="column.visible" show-overflow-tooltip
                               :label="column.label"
                               :prop="column.field"
                               :width="column.width"
                               align="center">
                <template slot-scope="scope">
                  <dict-tag v-if="column.dict" :options="dict.type[column.dict]" :value="scope.row[column.field]"/>
                  <template v-else-if="column.slots">
                    <RenderDom :index="index" :render="column.render" :row="scope.row"/>
                  </template>
                  <span v-else-if="column.isTime">{{ parseTime(scope.row[column.field], '{y}-{m}-{d}') }}</span>
                  <span v-else>{{ scope.row[column.field] }}</span>
                </template>
              </el-table-column>
            </template>
            <el-table-column
              align="center"
              class-name="small-padding fixed-width"
              fixed="right"
              label="操作"
              width="200"
            >
              <template slot-scope="scope">
                <el-button
                  icon="el-icon-delete"
                  size="mini"
                  type="text"
                  @click="handleDelete(scope.row)"
                >删除
                </el-button>
                <el-button
                  icon="el-icon-edit"
                  size="mini"
                  type="text"
                  @click="handleEdit (scope.row)"
                >修改
                </el-button>
              </template>
            </el-table-column>
          </el-table>
          <pagination
            v-show="total>0"
            :limit.sync="queryParams.pageSize"
            :page.sync="queryParams.pageNum"
            :total="total"
            @pagination="handleQuery"
          />
        </div>
      </el-col>
    </el-row>
    <!-- 新增编辑的弹框 -->
    <el-dialog :title="dialogTitle" :visible.sync="dialogVisible" width="50%">
      <el-form ref="dataForm" :rules="rules" :model="formData" label-width="120px">
        <el-form-item label="项目名称" prop="projName">
          <el-input v-model="formData.projName" placeholder="项目名称" @focus="openProjectDialogAndQuery"></el-input>
        </el-form-item>
        <el-form-item label="赔偿协议编号" prop="code">
          <el-input v-model="formData.code" placeholder="赔偿协议编号"></el-input>
        </el-form-item>
        <el-form-item label="索赔人" prop="claimBy">
          <el-input v-model="formData.claimBy" placeholder="索赔人"></el-input>
        </el-form-item>
        <el-form-item label="索赔金额" prop="claimFund">
          <el-input v-model="formData.claimFund" placeholder="索赔金额"></el-input>
        </el-form-item>
        <el-form-item label="是否赔付完成" prop="isClaim">
          <dict-select v-model="formData.isClaim" type="is_claim" placeholder="是否赔付完成"></dict-select>
        </el-form-item>
        <el-form-item label="赔付说明" prop="claimDesc">
          <el-input type="textarea" :rows="4" v-model="formData.claimDesc" placeholder="赔付说明"></el-input>
        </el-form-item>
        <el-form-item label="附件" prop="fileId">
          <file-upload key="fileId" v-model="formData.fileId" :owner-id="formData.fileId"></file-upload>
        </el-form-item>
      </el-form>
      <div style="text-align: right">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleSave">保存</el-button>
      </div>
    </el-dialog>
    <!-- 项目选择弹出框 -->
    <el-dialog title="选择项目" :visible.sync="showProjectDialog" width="80%">
      <el-form ref="projectQueryForm" :inline="true" :model="projectQueryParams" label-width="80px" size="small">
        <el-form-item label="项目名称">
          <el-input v-model="projectQueryParams.projName" placeholder="项目名称" style="width: 240px"></el-input>
        </el-form-item>
        <el-form-item label="项目编码">
          <el-input v-model="projectQueryParams.projCode" placeholder="项目编码" style="width: 240px"></el-input>
        </el-form-item>
        <el-form-item label="结算单编码">
          <el-input v-model="projectQueryParams.settlementCode" placeholder="结算单编码" style="width: 240px"></el-input>
        </el-form-item>
        <el-form-item>
          <el-button icon="el-icon-search" size="mini" type="primary" @click="handleProjectQuery">搜索</el-button>
          <el-button icon="el-icon-refresh" size="mini" @click="resetProjectQuery">重置</el-button>
        </el-form-item>
      </el-form>
      <el-table v-adjust-table
        ref="projectTable"
        v-loading="projectLoading"
        :data="projectData"
        border
        highlight-current-row
        size="mini"
        height="500px"
        @row-click="handleSelectProject"
        stripe
        style="width: 100%"
      >
        <el-table-column
          align="center"
          label="序号"
          type="index"
          width="50"
        />
        <template v-for="(column, index) in projectColumns">
          <el-table-column v-if="column.visible" show-overflow-tooltip
                       :label="column.label"
                       :prop="column.field"
                       :width="column.width"
                       align="center"
                       :key="index">
            <template slot-scope="scope">
              <dict-tag v-if="column.dict" :options="dict.type[column.dict]" :value="scope.row[column.field]"/>
              <template v-else-if="column.slots">
                <RenderDom :index="index" :render="column.render" :row="scope.row"/>
              </template>
              <span v-else-if="column.isTime">{{ parseTime(scope.row[column.field], '{y}-{m}-{d}') }}</span>
              <span v-else>{{ scope.row[column.field] }}</span>
            </template>
          </el-table-column>
        </template>
      </el-table>
      <pagination
        v-show="projectTotal>0"
        :limit.sync="projectQueryParams.pageSize"
        :page.sync="projectQueryParams.pageNum"
        :total="projectTotal"
        @pagination="handleProjectQuery"
      />
      <div style="text-align: right">
        <el-button @click="showProjectDialog = false">取消</el-button>
      </div>
    </el-dialog>
    <el-dialog title="附件列表" destroy-on-close :visible.sync="openFile" width="80%" v-if="openFile">
      <file-upload v-if="fileId" v-model="fileId" :owner-id="fileId" for-view></file-upload>
    </el-dialog>
  </div>
</template>

<script>
import {queryList, addClaim, editClaim, deleteClaim} from "@/api/theft/claimRecord";
import {queryList as listProject} from "@/api/theft/projectManage";
import {v4 as uuidv4} from "uuid";

export default {
  name: 'claimRecord',
  dicts: ['is_claim', 'theft_m_type', 'route_direction', 'lane', 'project_status_type'],
  components: {
    RenderDom: {
      functional: true,
      props: {
        row: Object,
        index: Number,
        render: Function
      },
      render(createElement, ctx) {
        const {row, index} = ctx.props
        return ctx.props.render(row, index)
      }
    }
  },
  data() {
    return {
      loading: false,
      queryParams: {
        pageNum: 1,
        pageSize: 50
      },
      tableData: [],
      total: 0,
      columns: [
        {key: 0, width: 200, field: 'projName', label: `项目名称`, visible: true},
        {key: 1, width: 200, field: 'code', label: `赔偿协议编码`, visible: true},
        {key: 2, width: 100, field: 'claimBy', label: `索赔人`, visible: true},
        {key: 3, width: 100, field: 'claimFund', label: `索赔金额`, visible: true},
        {key: 4, width: 300, field: 'claimDesc', label: `索赔描述`, visible: true},
        {key: 5, width: 100, field: 'createuser', label: `登记人`, visible: true},
        {key: 6, width: 150, field: 'createTime', label: `登记时间`, visible: true},
        {key: 7, width: 100, field: 'fileId', label: `附件查看`, visible: true, slots: true,
          render: (row, index) => {
            return (
              <el-button
                size="mini"
                disabled={!row.fileId}
                type="text"
                onClick={(e) => this.handleOpenFile(row)}
              >
                查看
              </el-button>
            );
          }
        },
        {key: 8, width: 150, field: 'isClaim', label: `是否赔付完成`, visible: true, dict: 'is_claim'},
      ],
      dialogVisible: false,
      dialogTitle: '',
      formData: {},
      rules: {
        projName: [{ required: true, message: '项目名称不能为空', trigger: 'blur' }],
        code: [{ required: true, message: '赔偿协议编号不能为空', trigger: 'blur' }],
        claimBy: [{ required: true, message: '索赔人不能为空', trigger: 'blur' }],
        claimFund: [{ required: true, message: '索赔金额不能为空', trigger: 'blur' }],
        isClaim: [{ required: true, message: '请选择是否赔付完成', trigger: 'blur' }],
      },
      showProjectDialog: false,
      projectLoading: false,
      projectData: [],
      projectTotal: 0,
      projectQueryParams: {
        pageNum: 1,
        pageSize: 50,
        projName: '',
        projCode: '',
        settlementCode: ''
      },
      projectColumns: [
        {key: 0, width: 100, field: 'mtype', label: `工程类型`, visible: true,dict: 'theft_m_type'},
        {key: 1, width: 150, field: 'name', label: `工程名称`, visible: true},
        {key: 2, width: 150, field: 'code', label: `工程编码`, visible: true},
        {key: 3, width: 150, field: 'maiSecName', label: `路段名称`, visible: true},
        {key: 4, width: 150, field: 'routeCode', label: `路线编码`, visible: true},
        {key: 5, width: 150, field: 'beginMileStr', label: `起点桩号`, visible: true},
        {key: 6, width: 150, field: 'endMileStr', label: `终点桩号`, visible: true},
        {key: 7, width: 100, field: 'direction', label: `上下行`, visible: true,dict: 'route_direction'},
        {key: 8, width: 150, field: 'lane', label: `位置`, visible: true, dict: 'lane'},
        {key: 9, width: 100, field: 'status', label: `状态`, visible: true, dict: 'project_status_type'},
      ],
      openFile: false,
      fileId: ''
    }
  },
  created() {
    this.handleQuery()
  },
  methods: {
    handleQuery() {
      this.loading = true
      queryList(this.queryParams).then(response => {
        this.tableData = response.rows
        this.total = response.total
        this.loading = false
      })
    },
    resetQuery() {
      this.queryParams = {
        pageNum: 1,
        pageSize: 50
      }
      this.handleQuery()
    },
    handleAdd() {
      this.dialogTitle = '新增'
      this.dialogVisible = true
      this.formData = {}
      this.formData.fileId = uuidv4().replace(/-/g, '').slice(0, 20)
    },
    handleEdit(row) {
      this.dialogTitle = '编辑'
      this.dialogVisible = true
      this.formData = Object.assign({}, row)
      this.formData.isClaim = String(this.formData.isClaim)
      if (!this.formData.fileId) this.formData.fileId = uuidv4().replace(/-/g, '').slice(0, 20)
    },
    handleDelete(row) {
      this.$confirm('是否确认删除该记录?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        deleteClaim(row.id).then(response => {
          this.$message.success('删除成功')
          this.handleQuery()
        })
      })
    },
    handleSave() {
      this.$refs.dataForm.validate(valid => {
        if (valid) {
          if (this.formData.fileId && Array.isArray(this.formData.fileId) && this.formData.fileId.length > 0) {
            this.formData.fileId = this.formData.fileId[0]
          } else if (Array.isArray(this.formData.fileId) &&
            this.formData.fileId.length == 0) {
            this.formData.fileId = null
          }
          if (this.dialogTitle === '新增') {
            addClaim(this.formData).then(response => {
              this.$message.success('新增成功')
              this.dialogVisible = false
              this.handleQuery()
            })
          } else {
            editClaim(this.formData).then(response => {
              this.$message.success('编辑成功')
              this.dialogVisible = false
              this.handleQuery()
            })
          }
        }
      })
    },
    resetProjectQuery() {
      this.projectQueryParams = {
        pageNum: 1,
        pageSize: 50,
        projName: '',
        projCode: '',
        settlementCode: ''
      }
      this.handleProjectQuery()
    },
    handleProjectQuery() {
      this.projectLoading = true
      listProject(this.projectQueryParams).then(response => {
        this.projectData = response.rows
        this.projectTotal = response.total
        this.projectLoading = false
      })
    },
    handleSelectProject(row) {
      this.formData.projName = row.name
      this.formData.projCode = row.code
      this.formData.projId = row.id
      this.showProjectDialog = false
    },
    openProjectDialogAndQuery() {
      this.showProjectDialog = true
      this.resetProjectQuery()
    },
    handleOpenFile(row) {
      this.fileId = row.fileId
      this.openFile = true
    }
  }
}
</script>

<style scoped lang="scss">

</style>
<style lang="scss" scoped>
@import "@/assets/styles/business.scss";
</style>
