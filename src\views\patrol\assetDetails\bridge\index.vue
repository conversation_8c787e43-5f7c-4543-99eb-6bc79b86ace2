<template>
	<Details ref="details" :type="type" :key="'BridgeDetails'" />
</template>

<script>
// import Details from '@/views/patrol/assetDetails/Details.vue'
import Details from '@/views/patrol/assetDetails/DetailsNew.vue'

export default {
	name: 'BridgeDetails',
	data() {
		return {
			type: 1
		}
	},
	components: {
		Details,
	},
	watch: {
		"$route": {
			handler(newParams, oldParams) {
				this.$nextTick(() => {
					if (newParams.name == 'BridgeDetails') {
						if (Object.keys(newParams.params).length > 0) {
							if (newParams.params.type == this.type) {
								this.$refs.details.getListByParams(newParams.params)
							}
						}
					}
				})
			},
			deep: true, // 深度监听，以捕捉对象内部的变化
			immediate: true,//一定要添加，否则当变量第一次赋值的时候，watch监听是不会生效的，watch只能监听值的变化，不能监听赋值‘
		},
	}
}
</script>
