<template>
  <div :style="{width: '100%', height: option ? '100%' : dataForSKWY ? '100%' : '0'}">
    <triplePositionChart v-if="dataForSKWY" :chartData="dataForSKWY"/>
    <Echarts ref="chart" v-if="option" :option="option" height="100%" :key="optionKey"/>
    <Echarts v-if="echartsArr && index !== 0" v-for="(item, index) in echartsArr" :key="item.optionKey"
      :option="item.option" height="100%" style="margin-top: 3vw;" :ref="item.chartRef"/>
    <!-- <Echarts ref="chart1" v-if="option1" :option="option1" height="100%" :key="optionKey1" style="margin-top: 3vw;"/>
    <Echarts ref="chart2" v-if="option2" :option="option2" height="100%" :key="optionKey2" style="margin-top: 3vw;"/>
    <Echarts ref="chart3" v-if="option3" :option="option3" height="100%" :key="optionKey3" style="margin-top: 3vw;"/> -->
  </div>
</template>

<script>
import Echarts from '../../components/echarts.vue';
import { fetchGet } from '../api.js'
import { isBigScreen } from '../../utils/utils.js';
import * as echarts from "echarts";
import { vehicleTypeList } from "@/assets/vehiclePic/vehicleType";
const triplePositionChart = () => import('./triplePositionChart.vue')

export default {
  name: 'ChartOneAndTwo',
  props: {
    chartBoxHeight: {
      type: String,
      default: '260px',
    },
    theme: {
      type: String,
      default: 'dark',
    },
    activeName: {
      type: String,
      default: '1',
    }
  },
  components: { Echarts, triplePositionChart },
  data() {
    return {
      isBig: isBigScreen(),
      option: null,
      optionKey: 'option1',
      sgWindowLength: 11,
      sgPolyOrder: 1,
      analysisType: { index: 2, label: '均值' },
      searchForm: {
        sensorId: '',
        specificMonitorContentIds: [],
        isPreprocess: false,
        isShowLimit: false,
        granularityNum: 1,
        granularityType: { index: 5, label: '天' },
        formatter: "{yyyy}-{MM}-{dd}",
        formatterNum: 12,
        granularityTypeName: '日',
      },
      tabNum: '',
      realTimeWebSocket: null,
      realTimePushData: {},
      myChart: null,
      option2: null,
      option3: null,
      optionKey2: 'option2',
      optionKey3: 'option3',
      option1: null,
      optionKey1: 'option1',
      dataForSKWY: false,
      echartsArr: null
    }
  },
  created() {
  },
  methods: {
    reLoad() {
      if (this.$refs.chart) {
        this.$refs.chart.reload()
      }
      if (this.echartsArr) {
        this.echartsArr.forEach(item => {
          if (item.chartRef) {
            this.$refs[item.chartRef].reload()
          }
        })
      }
    },

    //获取实时数据后绘制图表
    async drawRealTimeData(searchForm, selectedSensor, vehicleArr = null, dataForSKWY = null) {
      this.tabNum = 1
      this.searchForm = searchForm
      let tmpType = await this.monitorTypeJudge(selectedSensor)
      if (!searchForm.sensorId) {
        this.$message.warning("查询数据前请注意查询条件的完整性")
        return;
      }
      this.option = null
      this.echartsArr = null
      this.dataForSKWY = null
      if (searchForm.isPreprocess) {
        // 预处理数据
        if (dataForSKWY) {
          setTimeout(() => {
            this.dataForSKWY = dataForSKWY
          },100)
        }
        const url = "https://jkjc.yciccloud.com:8000/xboot/displayScreen/default/preprocess"
        const params = {
          nodeCode: selectedSensor.code,
          sensorId: selectedSensor.sensorId,
          structureNodeCode: this.$route.query.code,
          granularityNum: 1,
          granularityType: 0,
          analysisType: 2,
          sgWindowLength: this.sgWindowLength,
          sgPolyOrder: this.sgPolyOrder,
          dataType: 0,
          specificMonitorContentIds: searchForm.specificMonitorContentIds,
        }
        try {
          const res = await fetchGet(url, params)
          if (res.code === 200) {
            if (res.result?.length && res.result[0].processedData?.errMsg) {
              this.$message.error(res.result[0].processedData?.errMsg || "获取历史数据失败")
              this.setEmptyChart('无实时数据')
              return
            }
            // 判断 GNSS，也可以判断降雨量，总之这个可以判断是否是多表
            let ifGNSS = false
            const names = res.result.map(item => item.name);
            if (res.result.length === 3) {
              const targetNames = ["北方向坐标变化", "东方向坐标变化", "高程坐标变化"];
              if (names.every(name => targetNames.includes(name)) && targetNames.every(name => names.includes(name))) {
                ifGNSS = true
              }
            }
            const hasRainfall = names.some(name => name.includes('降雨量'));
            if (hasRainfall) {
              ifGNSS = true
              // 查找 name 为 “降雨量” 的元素
              const rainfallIndex = res.result.findIndex(item => item.name === "降雨量");
              if (rainfallIndex !== -1) {
                // 将 “降雨量” 元素移到数组首位
                const rainfallItem = res.result.splice(rainfallIndex, 1)[0];
                res.result.unshift(rainfallItem);
              }
            }
            const hasGravityAcceleration = names.some(name => name.includes('重力加速度'));
            if (hasGravityAcceleration) {
              ifGNSS = true
            }
            const hasTilt = names.some(name => name.includes('倾角变化值'));
            if (hasTilt) {
              ifGNSS = true
            }
            if (res.result.length > 1) {
              ifGNSS = true
            }
            switch (tmpType) {
              case 0:
                if (ifGNSS) {
                  this.echartsArr = res.result
                  this.echartsArr.map((el, index) => {
                    if (index === 0) {
                      this.option = this.setNormalChart([el]);
                      this.optionKey = new Date().getTime() + index;
                    } else {
                      el.option = this.setNormalChart([el]);
                      el.optionKey = new Date().getTime() + index;
                    }
                  })
                } else {
                  this.option = this.setNormalChart(res.result);
                  this.optionKey = new Date().getTime();
                }
                break;
              case 1:
                this.option = this.setWindChart2(res.result)
                this.optionKey = new Date().getTime();
                break;
              case 2:
                this.option = this.setVehicleLoadChart(res.result)
                this.optionKey = new Date().getTime();
                break;
            }
            this.$message.success(res.message || "数据获取成功")
            return true
          }
        } catch (err) {
          this.$message.error(err || '数据获取失败')
          this.setEmptyChart('无实时数据')
        }
      } else {
        // 非预处理数据
        if (vehicleArr) {
          this.getVehicleData(vehicleArr)
          return;
        }
        if (dataForSKWY) {
          setTimeout(() => {
            this.dataForSKWY = dataForSKWY
          },100)
        }
        const url = "https://jkjc.yciccloud.com:8000/xboot/displayScreen/default/getCalibratedRealTimeDataYH"
        const params = {
          nodeCode: selectedSensor.code,
          sensorId: selectedSensor.sensorId,
          structureNodeCode: this.$route.query.code,
          granularityNum: 1,
          granularityType: 0,
          analysisType: 2,
          specificMonitorContentIds: searchForm.specificMonitorContentIds,
        }
        try {
          const res = await fetchGet(url, params)
          if (res.code === 200) {
            if (res.result?.length && res.result[0].values?.length === 0) {
              this.setEmptyChart('无实时数据')
              return
            }
            // 判断 GNSS，也可以判断降雨量，总之这个可以判断是否是多表
            let ifGNSS = false
            const names = res.result.map(item => item.name);
            if (res.result.length === 3) {
              const targetNames = ["北方向坐标变化", "东方向坐标变化", "高程坐标变化"];
              if (names.every(name => targetNames.includes(name)) && targetNames.every(name => names.includes(name))) {
                ifGNSS = true
              }
            }
            const hasRainfall = names.some(name => name.includes('降雨量'));
            if (hasRainfall) {
              ifGNSS = true
              // 查找 name 为 “降雨量” 的元素
              const rainfallIndex = res.result.findIndex(item => item.name === "降雨量");
              if (rainfallIndex !== -1) {
                // 将 “降雨量” 元素移到数组首位
                const rainfallItem = res.result.splice(rainfallIndex, 1)[0];
                res.result.unshift(rainfallItem);
              }
            }
            const hasGravityAcceleration = names.some(name => name.includes('重力加速度'));
            if (hasGravityAcceleration) {
              ifGNSS = true
            }
            const hasTilt = names.some(name => name.includes('倾角变化值'));
            if (hasTilt) {
              ifGNSS = true
            }
            if (res.result.length > 1) {
              ifGNSS = true
            }
            switch (tmpType) {
              case 0:
                if (ifGNSS) {
                  this.echartsArr = res.result
                  this.echartsArr.map((el, index) => {
                    if (index === 0) {
                      this.option = this.setRealChart([el]);
                      this.optionKey = new Date().getTime() + index;
                    } else {
                      el.option = this.setRealChart([el]);
                      el.optionKey = new Date().getTime() + index;
                    }
                  })
                } else {
                  this.option = this.setRealChart(res.result);
                  this.optionKey = new Date().getTime();
                }
                break;
              case 1:
                this.option = this.setWindChart(res.result)
                this.optionKey = new Date().getTime();
                break;
              case 2:
                this.option = this.setVehicleLoadChart(res.result)
                this.optionKey = new Date().getTime();
                break;
            }
            this.$message.success(res.message || "数据获取成功")
            return true
          }
        } catch (err) {
          this.$message.error('数据获取失败')
          this.setEmptyChart('无实时数据')
        }
      }
    },

    //获取历史数据后绘制图表
    async drawHistoryData(searchForm, selectedSensor, vehicleArr = null, dataForSKWY = null) {
      this.searchForm = searchForm
      let tmpType = await this.monitorTypeJudge(selectedSensor)
      if (
        searchForm.sensorId === "" ||
        searchForm.timeRange.length === 0 ||
        searchForm.timeRange[0] === ""
      ) {
        this.$message.warning("查询数据前请注意查询条件的完整性")
        return;
      }
      this.option = null
      this.echartsArr = null
      this.dataForSKWY = null
      if (searchForm.isPreprocess) {
        // 取预处理数据
        if (dataForSKWY) {
          setTimeout(() => {
            this.dataForSKWY = dataForSKWY
          },100)
          return;
        }
        const url = "https://jkjc.yciccloud.com:8000/xboot/displayScreen/default/preprocess"
        const params = {
          nodeCode: selectedSensor.code,
          sensorId: selectedSensor.sensorId,
          structureNodeCode: this.$route.query.code,
          startTime: searchForm.timeRange[0],
          endTime: searchForm.timeRange[1],
          granularityNum: searchForm.granularityNum,
          granularityType: searchForm.granularityType?.index,
          analysisType: this.analysisType?.index,
          sgWindowLength: this.sgWindowLength,
          sgPolyOrder: this.sgPolyOrder,
          specificMonitorContentIds: searchForm.specificMonitorContentIds
        }
        try {
          const res = await fetchGet(url, params)
          if (res.code === 200) {
            if (res.result?.length && res.result[0].processedData?.errMsg) {
              this.$message.error(res.result[0].processedData?.errMsg || "获取历史数据失败")
              this.setEmptyChart('无历史数据')
              return
            }
            // 判断 GNSS，也可以判断降雨量，总之这个可以判断是否是多表
            let ifGNSS = false
            const names = res.result.map(item => item.name);
            if (res.result.length === 3) {
              const targetNames = ["北方向坐标变化", "东方向坐标变化", "高程坐标变化"];
              if (names.every(name => targetNames.includes(name)) && targetNames.every(name => names.includes(name))) {
                ifGNSS = true
              }
            }
            const hasRainfall = names.some(name => name.includes('降雨量'));
            if (hasRainfall) {
              ifGNSS = true
              // 查找 name 为 “降雨量” 的元素
              const rainfallIndex = res.result.findIndex(item => item.name === "降雨量");
              if (rainfallIndex !== -1) {
                // 将 “降雨量” 元素移到数组首位
                const rainfallItem = res.result.splice(rainfallIndex, 1)[0];
                res.result.unshift(rainfallItem);
              }
            }
            const hasGravityAcceleration = names.some(name => name.includes('重力加速度'));
            if (hasGravityAcceleration) {
              ifGNSS = true
            }
            const hasTilt = names.some(name => name.includes('倾角变化值'));
            if (hasTilt) {
              ifGNSS = true
            }
            if (res.result.length > 1) {
              ifGNSS = true
            }
            switch (tmpType) {
              case 0:
                if (ifGNSS) {
                  this.echartsArr = res.result
                  this.echartsArr.map((el, index) => {
                    if (index === 0) {
                      this.option = this.setNormalChart([el]);
                      this.optionKey = new Date().getTime() + index;
                    } else {
                      el.option = this.setNormalChart([el]);
                      el.optionKey = new Date().getTime() + index;
                    }
                  })
                } else {
                  this.option = this.setNormalChart(res.result)
                  this.optionKey = new Date().getTime();
                }
                break;
              case 1:
                this.option = this.setWindChart2(res.result)
                this.optionKey = new Date().getTime();
                break;
              case 2:
                this.option = this.setVehicleLoadChart(res.result)
                this.optionKey = new Date().getTime();
                break;
            }
            this.$message.success(res.message || "获取历史数据成功")
            return true
          }
        } catch (error) {
          this.$message.error('获取历史数据失败')
          this.setEmptyChart('无历史数据')
        }
      } else {
        // 不做预处理直接取历史数据
        if (vehicleArr) {
          this.getVehicleData(vehicleArr)
          return;
        }
        if (dataForSKWY) {
          setTimeout(() => {
            this.dataForSKWY = dataForSKWY
          },100)
          return;
        }
        const url = "https://jkjc.yciccloud.com:8000/xboot/displayScreen/default/getCalibratedHistoryData"
        const params = {
          nodeCode: selectedSensor.code,
          sensorId: selectedSensor.sensorId,
          structureNodeCode: this.$route.query.code,
          startTime: searchForm.timeRange[0],
          endTime: searchForm.timeRange[1],
          granularityNum: searchForm.granularityNum,
          granularityType: searchForm.granularityType?.index,
          analysisType: this.analysisType?.index,
          specificMonitorContentIds: searchForm.specificMonitorContentIds,
          timeOutMsg: "请求时间数据范围太大,请调整采样周期或周期单位"
        }
        try {
          const res = await fetchGet(url, params)
          if (res.code === 200) {
            if (res.result?.length && res.result[0].values?.length === 0) {
              this.setEmptyChart('无历史数据')
              return
            }
            // 判断 GNSS，也可以判断降雨量，总之这个可以判断是否是多表
            let ifGNSS = false
            const names = res.result.map(item => item.name);
            if (res.result.length === 3) {
              const targetNames = ["北方向坐标变化", "东方向坐标变化", "高程坐标变化"];
              if (names.every(name => targetNames.includes(name)) && targetNames.every(name => names.includes(name))) {
                ifGNSS = true
              }
            }
            const hasRainfall = names.some(name => name.includes('降雨量'));
            if (hasRainfall) {
              ifGNSS = true
              // 查找 name 为 “降雨量” 的元素
              const rainfallIndex = res.result.findIndex(item => item.name === "降雨量");
              if (rainfallIndex !== -1) {
                // 将 “降雨量” 元素移到数组首位
                const rainfallItem = res.result.splice(rainfallIndex, 1)[0];
                res.result.unshift(rainfallItem);
              }
            }
            const hasGravityAcceleration = names.some(name => name.includes('重力加速度'));
            if (hasGravityAcceleration) {
              ifGNSS = true
            }
            const hasTilt = names.some(name => name.includes('倾角变化值'));
            if (hasTilt) {
              ifGNSS = true
            }
            if (res.result.length > 1) {
              ifGNSS = true
            }
            this.changeGranularityTypeRe(res.result[0].granularityNum, res.result[0].granularityType)
            switch (tmpType) {
              case 0:
                if (ifGNSS) {
                  this.echartsArr = res.result
                  this.echartsArr.map((el, index) => {
                    if (index === 0) {
                      this.option = this.setRealChart([el]);
                      this.optionKey = new Date().getTime() + index;
                    } else {
                      el.option = this.setRealChart([el]);
                      el.optionKey = new Date().getTime() + index;
                    }
                  })
                } else {
                  this.option = this.setRealChart(res.result);
                  this.optionKey = new Date().getTime();
                }
                break;
              case 1:
                this.option = this.setWindChart(res.result)
                this.optionKey = new Date().getTime();
                break;
              case 2:
                this.option = this.setVehicleLoadChart(res.result)
                this.optionKey = new Date().getTime();
                break;
            }
            this.$message.success(res.message || "数据获取成功")
            return true
          } else {
            this.$message.error(res.message || '数据获取失败')
            this.setEmptyChart('无历史数据')
          }
        } catch (error) {
          this.$message.error('数据获取失败')
          this.setEmptyChart('无历史数据')
        }
      }
    },

    //反向转换粒度
    changeGranularityTypeRe(granularityNum, granularityType) {
      if (granularityType === 0) {
        this.searchForm.granularityNum = 1
        this.searchForm.granularityType = { index: 0, label: '原始' }
        this.searchForm.granularityTypeName = '原始'
      } else if (granularityNum === 1 && granularityType === 3) {
        this.searchForm.granularityNum = 1
        this.searchForm.granularityType = { index: 3, label: '分' }
        this.searchForm.granularityTypeName = '分'
      } else if (granularityNum === 10 && granularityType === 3) {
        this.searchForm.granularityNum = 10
        this.searchForm.granularityType = { index: 3, label: '分' }
        this.searchForm.granularityTypeName = '10分'
      } else if (granularityType === 4) {
        this.searchForm.granularityNum = 1
        this.searchForm.granularityType = { index: 4, label: '时' }
        this.searchForm.granularityTypeName = '小时'
      } else if (granularityType === 5) {
        this.searchForm.granularityNum = 1
        this.searchForm.granularityType = { index: 5, label: '天' }
        this.searchForm.granularityTypeName = '日'
      } else {
        this.searchForm.granularityNum = 1
        this.searchForm.granularityType = { index: 0, label: '原始' }
        this.searchForm.granularityTypeName = '原始'
      }
      this.$emit('changeGranularity', this.searchForm)
    },
    // 绘制数据平滑滤噪后的历史数据
    setNormalChart(data) {
      // 更改数据格式
      data.forEach((datai) => {
        if (
          datai.name === '3小时降雨量' ||
          datai.name === '6小时降雨量' ||
          datai.name === '12H累计降雨量' ||
          datai.name === '24H累计降雨量'
        ) {
          const groupedData = {};
          let intervalHours;
          if (datai.name === '3小时降雨量') {
            intervalHours = 3;
          } else if (datai.name === '6小时降雨量') {
            intervalHours = 6;
          } else if (datai.name === '12H累计降雨量') {
            intervalHours = 12;
          } else if (datai.name === '24H累计降雨量') {
            intervalHours = 24;
          }

          datai.originData.times.forEach((time, index) => {
            if (datai.originData.values[index] === null) return;
            // 转换时间为 Date 对象
            const date = new Date(time);
            // 计算对应时间间隔的起始时间
            const hour = date.getHours();
            const startHour = Math.floor(hour / intervalHours) * intervalHours;
            date.setHours(startHour, 0, 0, 0);
            const key = date.toISOString();

            if (!groupedData[key]) {
              groupedData[key] = {
                time: date,
                value: 0
              };
            }
            groupedData[key].value += datai.originData.values[index];
          });

          // 重新生成 times 和 values 数组
          datai.originData.times = [];
          datai.originData.values = [];
          Object.values(groupedData).forEach(item => {
            datai.originData.times.push(item.time.toISOString().replace('T', ' ').replace(/\.\d+Z$/, ''));
            datai.originData.values.push(item.value);
          });

          // 同样处理 processedData
          const processedGroupedData = {};
          datai.processedData.times.forEach((time, index) => {
            if (datai.processedData.values[index] === null) return;
            const date = new Date(time);
            const hour = date.getHours();
            const startHour = Math.floor(hour / intervalHours) * intervalHours;
            date.setHours(startHour, 0, 0, 0);
            const key = date.toISOString();

            if (!processedGroupedData[key]) {
              processedGroupedData[key] = {
                time: date,
                value: 0
              };
            }
            processedGroupedData[key].value += datai.processedData.values[index];
          });

          datai.processedData.times = [];
          datai.processedData.values = [];
          Object.values(processedGroupedData).forEach(item => {
            datai.processedData.times.push(item.time.toISOString().replace('T', ' ').replace(/\.\d+Z$/, ''));
            datai.processedData.values.push(item.value);
          });
        }

        let tmpOriginDotList = [];
        let tmpProcessDotList = [];
        datai.originData.times.forEach((value, index) => {
          if (datai.originData.times[index].includes('.')) {
            datai.originData.times[index] = datai.originData.times[index].split('.')[0]
          }
          if (datai.processedData.times[index].includes('.')) {
            datai.processedData.times[index] = datai.processedData.times[index].split('.')[0]
          }
          datai.originData.values[index] = parseFloat(datai.originData.values[index].toFixed(datai.accuracy >= 0 ? datai.accuracy : 4))
          tmpOriginDotList.push(
            [datai.originData.times[index], datai.originData.values[index]]
          );
          datai.processedData.values[index] = parseFloat(datai.processedData.values[index].toFixed(datai.accuracy >= 0 ? datai.accuracy : 4))
          tmpProcessDotList.push(
            [datai.processedData.times[index], datai.processedData.values[index]]
          )
        })
        datai.originData.dot = tmpOriginDotList
        datai.processedData.dot = tmpProcessDotList
      })

      // 计算指定天数的数据在总数据量中的百分比
      const calculateDayPercentage = (intervalHours, days = 1) => {
        const totalHours = 24 * days;
        const intervalsPerDay = totalHours / intervalHours;
        const totalIntervals = data[0].originData.times.length;
        return (intervalsPerDay / totalIntervals) * 100;
      };

      // 新增：判断数据量是否大
      let isLargeData = data.some(item => item.originData?.values?.length > 100); // 假设数据量超过100认为是大数据
      let dataZoomMinSpan = this.isBig ? 2 : 1;
      let dataZoomMaxSpan = this.isBig ? 20 : 10;
      const has3HourRainfall = data.some(item => item.name === '3小时降雨量');
      const has6HourRainfall = data.some(item => item.name === '6小时降雨量');
      const has12HRainfall = data.some(item => item.name === '12H累计降雨量');
      const has24HRainfall = data.some(item => item.name === '24H累计降雨量');
      if (has3HourRainfall) {
        isLargeData = true
        const dayPercentage = calculateDayPercentage(3, 3);
        dataZoomMinSpan = dayPercentage;
        dataZoomMaxSpan = dayPercentage;
      } else if (has6HourRainfall) {
        isLargeData = true
        const dayPercentage = calculateDayPercentage(6, 6);
        dataZoomMinSpan = dayPercentage;
        dataZoomMaxSpan = dayPercentage;
      } else if (has12HRainfall) {
        isLargeData = true
        const dayPercentage = calculateDayPercentage(12, 12);
        dataZoomMinSpan = dayPercentage;
        dataZoomMaxSpan = dayPercentage;
      } else if (has24HRainfall) {
        isLargeData = true
        const dayPercentage = calculateDayPercentage(24, 24);
        dataZoomMinSpan = dayPercentage;
        dataZoomMaxSpan = dayPercentage;
      }
      let formatterNum = this.tabNum === 1 ? 4 : this.searchForm.formatterNum
      // echarts配置项
      let option = {
        toolbox: {
          right: 0,
          top: "3%",
          itemSize: this.isBig ? 30 : 12,
          feature: {
            saveAsImage: {
              title: '保存',
              type: 'png',
              backgroundColor: this.theme === 'dark' ? 'rgba(4, 37, 72, 0.8)' : '#fff',
            },
          },
        },
        tooltip: {
          textStyle: {
            align: 'left',
            fontSize: this.isBig ? 24 : 14,
          },
          trigger: "axis",
        },
        xAxis: {
          type: 'category',
          name: "时间",
          nameLocation: "middle",
          nameGap: this.isBig ? 64 : 32,
          nameTextStyle: {
            fontSize: this.isBig ? 24 : 12,
            color: this.theme === 'dark' ? '#fff' : '#000',
            lineHeight: this.isBig ? 120 : 20,
          },
          axisLabel: {
            showMinLabel: true,
            showMaxLabel: true,
            fontSize: this.isBig ? 24 : 11,
            color: this.theme === 'dark' ? '#fff' : '#000',
            formatter: function (value, index) {
              const a = value + '.000'
              let dateTimeStr = a.substring(0, a.length - formatterNum)
              const parts = dateTimeStr.split(' ');
              return parts[0] + '\n' + parts[1];
            }
          },
          axisTick: {
            alignWithLabel: true, // 刻度线与标签对齐
            inside: false, // 刻度线朝上
            lineStyle: {
              color: this.theme === 'dark' ? '#fff' : '#000',
            }
          },
          axisLine: {
            show: true,
            lineStyle: {
              color: this.theme === 'dark' ? '#fff' : '#000',
            },
            onZero: false,
          },
        },
        yAxis: [],
        dataZoom: [
          {
            type: "inside",
            ...(isLargeData && {
              minSpan: dataZoomMinSpan,
              maxSpan: dataZoomMaxSpan
            })
          },
          {
            type: 'slider',
            xAxisIndex: 0,
            minSpan: this.isBig ? 20 : 5,
            height: this.isBig ? 80 : 20,
            bottom: this.isBig ? 120 : 5,
            ...(isLargeData && {
              minSpan: dataZoomMinSpan,
              maxSpan: dataZoomMaxSpan
            })
          }
        ],
        grid: [
          {
            top: this.isBig ? "20%" : "15%",
            left: this.isBig ? "3%" : "2%",
            right: 5 * (data.length > 2 ? data.length - 2 : 1) + "%",
            bottom: this.isBig ? "20%" : "15%",
            containLabel: true,
          },
        ],
        series: [],
        legend: {
          type: 'scroll',
          top: this.isBig ? "10%" : '0',
          itemGap: this.isBig ? 40 : 10,
          data: [],
          selected: {},
          textStyle: {
            color: this.theme === 'dark' ? '#fff' : '#000',
            fontSize: this.isBig ? 25 : 14,
          },
        },
      };
      //加入多行数据
      for (let i = 0; i < data.length; i++) {
        option.legend.data.push(data[i].name);
        option.legend.data.push(data[i].name + "预处理结果");
        if (data[i].name.includes("降雨量") && !data[i].name.startsWith("降雨量")) {
          option.series.push({
            type: "bar",
            name: data[i].name,
            showSymbol: false,
            data: data[i].originData.dot,
            yAxisIndex: i,
            barGap: '0%', 
            barCategoryGap: '0%' 
          });
          // push处理后的值
          option.series.push({
            type: "bar",
            name: data[i].name + "预处理结果",
            showSymbol: false,
            data: data[i].processedData.dot,
            yAxisIndex: i,
            barGap: '0%', 
            barCategoryGap: '0%' 
          });
        } else {
          option.series.push({
            type: "line",
            name: data[i].name,
            showSymbol: false,
            data: data[i].originData.dot,
            yAxisIndex: i,
            smooth: true,
            // 添加 areaStyle 属性实现填充颜色
            areaStyle: {
              opacity: data[i].name.includes("水位") ? 0.3 : 0, // 填充区域的透明度，取值范围 0 - 1
              color: 'rgba(73, 153, 247)' // 根据主题设置填充颜色
            },
          });
          // push处理后的值
          option.series.push({
            type: "line",
            name: data[i].name + "预处理结果",
            showSymbol: false,
            data: data[i].processedData.dot,
            yAxisIndex: i,
            smooth: true,
            // 添加 areaStyle 属性实现填充颜色
            areaStyle: {
              opacity: data[i].name.includes("水位") ? 0.3 : 0, // 填充区域的透明度，取值范围 0 - 1
              color: 'rgba(73, 153, 247)' // 根据主题设置填充颜色
            },
          });
        }
        // 画预警线
        let limitInfoList = [];
        let markLineDataList = []
        if (data[i].limitInfo && this.searchForm.isShowLimit) {
          option.legend.data.push("预警线")
          option.series.push({
            type: "line",
            name: "预警线",
            showSymbol: false,
            yAxisIndex: i,
            animation: false,
            markLine: {
              symbol: 'none',
              data: markLineDataList,
              lineStyle: {
                color: "red"
              }
            },
            itemStyle: {
              color: "red"
            }
          });
          Object.keys(data[i].limitInfo).forEach((key) => {
            markLineDataList.push({
              yAxis: data[i].limitInfo[key],
              lineStyle: {
                type: 'solid'
              },
              emphasis: {
                label: {
                  position: 'outside',
                  formatter: data[i].name + key + data[i].limitInfo[key],
                  show: true,
                },
              },
              label: {
                show: false,
              },
            })
            limitInfoList.push(data[i].limitInfo[key])
          })
        }
        //计算最大值与最小值
        let minValue = this.getMin(data[i].originData.values); // 输出最小值
        let maxValue = this.getMax(data[i].originData.values); // 输出最大值
        limitInfoList.push(minValue)
        limitInfoList.push(maxValue)
        minValue = this.getMin(limitInfoList)
        maxValue = this.getMax(limitInfoList)
        // 一些特殊情况处理
        if (maxValue === 0 && minValue === 0) {
          maxValue = 1
          minValue = -1
        } else {
          let delta = Math.ceil((maxValue - minValue) * 0.2 * (10 ** data[i].accuracy)) / (10 ** data[i].accuracy)
          if (delta === 0) {
            delta = 1
          }
          let midValue = Math.ceil((maxValue + minValue) / 2 * (10 ** data[i].accuracy)) / (10 ** data[i].accuracy)
          maxValue = midValue + 3 * delta
          minValue = midValue - 3 * delta
        }
        // 湿度坐标轴范围特殊处理
        if (data[i].name.indexOf('湿度') >= 0) {
          if (minValue < 0) {
            minValue = 0;
          }
          if (maxValue > 100) {
            maxValue = 100;
          }
        }
        if (data[i].name.indexOf('降雨量') >= 0) {
          if (minValue <= 0) {
            minValue = 0;
          }
        }
        // 索力坐标轴范围特殊处理
        // if (data[i].name.indexOf('索力') >= 0) {
        //   if (minValue < 0) {
        //     minValue = 0;
        //   }
        // }
        // 如果超过3列数据，则多余的y轴需要偏移
        if (i > 1) {
          option.yAxis.push({
            name: data[i].name + "/" + data[i].unit,
            nameTextStyle: {
              fontWeight: "bold",
              fontSize: this.isBig ? 24 : 12,
              color: this.theme === 'dark' ? '#fff' : '#000',
              borderWidth: 12,
              padding: data[i].name.length > 6 ? [0, 0, 0, 20] : [0, 0, 0, 5]
            },
            axisLine: {
              show: true,
              lineStyle: {
                width: '1'//坐标线的宽度
              },
              onZero: false,
            },
            axisLabel: {
              fontSize: this.isBig ? 22 : 11,
              color: this.theme === 'dark' ? '#fff' : '#000',
              formatter: function (value) {
                // 判断 value 是否为整数
                if (Number.isInteger(value)) {
                  return value.toString();
                } else {
                  // 小数且超过两位小数时保留两位小数
                  return value.toFixed(2);
                }
              },
            },
            splitNumber: 10,
            min: minValue,
            max: maxValue,
            interval: (maxValue - minValue) / 10, // 标轴分割间隔
            offset: 120 * (i - 1),
            splitLine: {
              show: true,
              lineStyle: {
                color: this.theme === 'dark' ? 'rgba(110, 112, 121, 0.20)' : '#eee',
              }
            },
            splitArea: {
              show: false,
            },
          });
        } else {
          option.yAxis.push({
            name: data[i].name + "/" + data[i].unit,
            nameTextStyle: {
              fontWeight: "bold",
              fontSize: this.isBig ? 24 : 12,
              color: this.theme === 'dark' ? '#fff' : '#000',
              padding: data[i].name.length > 6 ? [0, 0, 0, data[i].name.length * 6] : [0, 0, 0, 5]
            },
            axisLabel: {
              fontSize: this.isBig ? 22 : 11,
              color: this.theme === 'dark' ? '#fff' : '#000',
              formatter: function (value) {
                // 判断 value 是否为整数
                if (Number.isInteger(value)) {
                  return value.toString();
                } else {
                  // 小数且超过两位小数时保留两位小数
                  return value.toFixed(2);
                }
              },
            },
            splitNumber: 10,
            min: minValue,
            max: maxValue,
            interval: (maxValue - minValue) / 10, // 标轴分割间隔
            splitLine: {
              show: true,
              lineStyle: {
                color: this.theme === 'dark' ? 'rgba(110, 112, 121, 0.20)' : '#eee',
              }
            },
            splitArea: {
              show: false,
            },
          })
        }
      }
      return option
    },
    //绘制实时数据 带预警值线
    setRealChart(data) {
      // 修改数据格式
      data.forEach((datai) => {
        if (
          datai.name === '3小时降雨量' ||
          datai.name === '6小时降雨量' ||
          datai.name === '12H累计降雨量' ||
          datai.name === '24H累计降雨量'
        ) {
          const groupedData = {};
          let intervalHours;
          if (datai.name === '3小时降雨量') {
            intervalHours = 3;
          } else if (datai.name === '6小时降雨量') {
            intervalHours = 6;
          } else if (datai.name === '12H累计降雨量') {
            intervalHours = 12;
          } else if (datai.name === '24H累计降雨量') {
            intervalHours = 24;
          }

          datai.times.forEach((time, index) => {
            if (datai.values[index] === null) return;
            // 转换时间为 Date 对象
            const date = new Date(time);
            // 计算对应时间间隔的起始时间
            const hour = date.getHours();
            const startHour = Math.floor(hour / intervalHours) * intervalHours;
            date.setHours(startHour, 0, 0, 0);
            const key = date.toISOString();

            if (!groupedData[key]) {
              groupedData[key] = {
                time: date,
                value: 0
              };
            }
            groupedData[key].value += datai.values[index];
          });

          // 重新生成 times 和 values 数组
          datai.times = [];
          datai.values = [];
          Object.values(groupedData).forEach(item => {
            datai.times.push(item.time.toISOString().replace('T', ' ').replace(/\.\d+Z$/, ''));
            datai.values.push(item.value);
          });
        }

        let tmpDotList = [];
        datai.times.forEach((value, index) => {
          if (datai.times[index].includes('.')) {
            datai.times[index] = datai.times[index].split('.')[0]
          }
          if (datai.values[index] === null) {
            // 处理 null 值
          } else {
            datai.values[index] = parseFloat(datai.values[index].toFixed(datai.accuracy >= 0 ? datai.accuracy : 4))
          }
          tmpDotList.push(
            [datai.times[index], datai.values[index]]
          )
        })
        datai.dot = tmpDotList
      })

      // 计算一天的数据在总数据量中的百分比
      const calculateDayPercentage = (intervalHours, days = 1) => {
        const totalHours = 24 * days;
        const intervalsPerDay = totalHours / intervalHours;
        const totalIntervals = data[0].times.length;
        return (intervalsPerDay / totalIntervals) * 100;
      };

      // 新增：判断数据量是否大
      let isLargeData = data.some(item => item.values.length > 50) // 假设数据量超过100认为是大数据
      let dataZoomMinSpan = this.isBig ? 2 : 1;
      let dataZoomMaxSpan = this.isBig ? 20 : 10;
      const has3HourRainfall = data.some(item => item.name === '3小时降雨量');
      const has6HourRainfall = data.some(item => item.name === '6小时降雨量');
      const has12HRainfall = data.some(item => item.name === '12H累计降雨量');
      const has24HRainfall = data.some(item => item.name === '24H累计降雨量');

      if (has3HourRainfall) {
        isLargeData = true
        const dayPercentage = calculateDayPercentage(3, 3);
        dataZoomMinSpan = dayPercentage;
        dataZoomMaxSpan = dayPercentage;
      } else if (has6HourRainfall) {
        isLargeData = true
        const dayPercentage = calculateDayPercentage(6, 6);
        dataZoomMinSpan = dayPercentage;
        dataZoomMaxSpan = dayPercentage;
      } else if (has12HRainfall) {
        isLargeData = true
        const dayPercentage = calculateDayPercentage(12, 12);
        dataZoomMinSpan = dayPercentage;
        dataZoomMaxSpan = dayPercentage;
      } else if (has24HRainfall) {
        isLargeData = true
        const dayPercentage = calculateDayPercentage(24, 24);
        dataZoomMinSpan = dayPercentage;
        dataZoomMaxSpan = dayPercentage;
      }
      
      let formatterNum = this.tabNum === 1 ? 4 : this.searchForm.formatterNum
      let option = {
        toolbox: {
          right: 0,
          top: 0,
          itemSize: this.isBig ? 30 : 12,
          feature: {
            saveAsImage: {
              title: '保存',
              type: 'png',
              backgroundColor: this.theme === 'dark' ? 'rgba(4, 37, 72, 0.8)' : '#fff',
            },
          },
        },
        tooltip: {
          textStyle: {
            align: 'left',
            fontSize: this.isBig ? 24 : 14,
          },
          trigger: "axis",
          confine: true,
        },
        xAxis: {
          type: 'category',
          name: "时间",
          nameLocation: "middle",
          nameTextStyle: {
            fontSize: this.isBig ? 24 : 12,
            color: this.theme === 'dark' ? '#fff' : '#000',
          },
          nameGap: this.isBig ? 60 : 30,
          axisLabel: {
            showMinLabel: true,
            showMaxLabel: true,
            fontSize: this.isBig ? 24 : 11,
            color: this.theme === 'dark' ? '#fff' : '#000',
            formatter: function (value, index) {
              const a = value + '.000'
              let dateTimeStr = a.substring(0, a.length - formatterNum)
              const parts = dateTimeStr.split(' ');
              return parts[0] + '\n' + parts[1];
            }
          },
          axisTick: {
            alignWithLabel: true, // 刻度线与标签对齐
            inside: false, // 刻度线朝上
            lineStyle: {
              color: this.theme === 'dark' ? '#fff' : '#000',
            }
          },
          axisLine: {
            show: true,
            lineStyle: {
              color: this.theme === 'dark' ? '#fff' : '#000',
            },
            onZero: false,
          },
        },
        yAxis: [],
        dataZoom: [
          {
            type: "inside",
            ...(isLargeData && {
              minSpan: dataZoomMinSpan,
              maxSpan: dataZoomMaxSpan
            })
          },
          {
            type: 'slider',
            xAxisIndex: 0,
            minSpan: this.isBig ? 20 : 5,
            height: this.isBig ? 80 : 20,
            bottom: this.isBig ? 120 : 5,
            ...(isLargeData && {
              minSpan: dataZoomMinSpan,
              maxSpan: dataZoomMaxSpan
            })
          }
        ],
        grid: [
          {
            top: this.isBig ? "20%" : "15%",
            left: this.isBig ? "3%" : "2%",
            right: 5 * (data.length > 2 ? data.length - 2 : 1) + "%",
            bottom: this.isBig ? "20%" : "15%",
            containLabel: true,
          },
        ],
        series: [],
        legend: {
          type: 'scroll',
          top: this.isBig ? "10%" : '0',
          itemGap: this.isBig ? 40 : 10,
          data: [],
          selected: {},
          textStyle: {
            color: this.theme === 'dark' ? '#fff' : '#000',
            fontSize: this.isBig ? 25 : 14,
          },
        },
      };
      // 不单独加legend图例也可以，此处单独给其data添加是为了控制换行
      //加入多行数据
      for (let i = 0; i < data.length; i++) {
        option.legend.data.push(data[i].name);
        if (data[i].name.length > 3 && data[i].name.includes("降雨量")) {
          option.series.push({
            type: "bar",
            name: data[i].name,
            showSymbol: false,
            data: data[i].dot,
            yAxisIndex: i,
            barCategoryGap: '0%',
          });
        } else {
          option.series.push({
            type: "line",
            name: data[i].name,
            showSymbol: false,
            data: data[i].dot,
            yAxisIndex: i,
            smooth: true,
            // 添加 areaStyle 属性实现填充颜色
            areaStyle: {
              opacity: data[i].name.includes("水位") ? 0.3 : 0, // 填充区域的透明度，取值范围 0 - 1
              color: 'rgba(73, 153, 247)' // 根据主题设置填充颜色
            },
          });
        }
        let limitInfoList = [];
        let markLineDataList = []
        if (data[i].limitInfo && this.searchForm.isShowLimit) {
          option.legend.data.push("预警线")
          option.series.push({
            type: "line",
            name: "预警线",
            showSymbol: false,
            yAxisIndex: i,
            animation: false,
            markLine: {
              symbol: 'none',
              data: markLineDataList,
              lineStyle: {
                color: "red"
              }
            },
            itemStyle: {
              color: "red"
            }
          });
          Object.keys(data[i].limitInfo).forEach((key) => {
            markLineDataList.push({
              yAxis: data[i].limitInfo[key],
              emphasis: {
                label: {
                  position: 'outside',
                  formatter: data[i].name + key + data[i].limitInfo[key],
                  show: true,
                },
              },
              label: {
                show: false,
              },
              axisLabel: {
                formatter: function (value) {
                  // 判断 value 是否为整数
                  if (Number.isInteger(value)) {
                    return value.toString();
                  } else {
                    // 小数且超过两位小数时保留两位小数
                    return value.toFixed(2);
                  }
                },
                fontSize: this.isBig ? 22 : 11,
                color: this.theme === 'dark' ? '#fff' : '#000',
              },
            })
            limitInfoList.push(data[i].limitInfo[key])
          })
        }
        // 设置图例换行
        // option.legend.data.push("")
        //计算最大值与最小值
        let minValue = this.getMin(data[i].values); // 输出最小值
        let maxValue = this.getMax(data[i].values); // 输出最大值
        limitInfoList.push(minValue)
        limitInfoList.push(maxValue)
        minValue = this.getMin(limitInfoList)
        maxValue = this.getMax(limitInfoList)
        // 一些特殊情况处理
        if (maxValue === 0 && minValue === 0) {
          maxValue = 1
          minValue = -1
        } else {
          let delta = Math.ceil((maxValue - minValue) * 0.2 * (10 ** data[i].accuracy)) / (10 ** data[i].accuracy)
          if (delta === 0) {
            delta = 1
          }
          let midValue = Math.ceil((maxValue + minValue) / 2 * (10 ** data[i].accuracy)) / (10 ** data[i].accuracy)
          maxValue = midValue + 3 * delta
          minValue = midValue - 3 * delta
        }
        // 湿度坐标轴范围特殊处理
        if (data[i].name.indexOf('湿度') >= 0) {
          if (minValue < 0) {
            minValue = 0;
          }
          if (maxValue > 100) {
            maxValue = 100;
          }
        }
        if (data[i].name.indexOf('降雨量') >= 0) {
          if (minValue <= 0) {
            minValue = 0;
          }
        }
        // 索力坐标轴范围特殊处理
        // if (data[i].name.indexOf('索力') >= 0) {
        //   if (minValue < 0) {
        //     minValue = 0;
        //   }
        // }
        // 如果超过3列数据，则多余的y轴需要偏移
        if (i > 1) {
          option.yAxis.push({
            name: data[i].name + "/" + data[i].unit,
            nameTextStyle: {
              fontWeight: "bold",
              fontSize: this.isBig ? 24 : 12,
              color: this.theme === 'dark' ? '#fff' : '#000',
              padding: data[i].name.length > 6 ? [0, 0, 0, 20] : [0, 0, 0, 5]
            },
            axisLabel: {
              fontSize: this.isBig ? 22 : 11,
              color: this.theme === 'dark' ? '#fff' : '#000',
              formatter: function (value) {
                // 判断 value 是否为整数
                if (Number.isInteger(value)) {
                  return value.toString();
                } else {
                  // 小数且超过两位小数时保留两位小数
                  return value.toFixed(2);
                }
              },
            },
            splitNumber: 10,
            min: minValue,
            max: maxValue,
            interval: (maxValue - minValue) / 10, // 标轴分割间隔
            offset: 120 * (i - 1),
            splitLine: {
              show: true,
              lineStyle: {
                color: this.theme === 'dark' ? 'rgba(110, 112, 121, 0.20)' : '#eee',
              }
            },
            splitArea: {
              show: false,
            },
          });
        } else {
          option.yAxis.push({
            name: data[i].name + "/" + data[i].unit,
            nameTextStyle: {
              fontWeight: "bold",
              fontSize: this.isBig ? 24 : 12,
              color: this.theme === 'dark' ? '#fff' : '#000',
              padding: data[i].name.length > 6 ? [0, 0, 0, 20] : [0, 0, 0, 5]
            },
            axisLabel: {
              fontSize: this.isBig ? 22 : 11,
              color: this.theme === 'dark' ? '#fff' : '#000',
              formatter: function (value) {
                // 判断 value 是否为整数
                if (Number.isInteger(value)) {
                  return value.toString();
                } else {
                  // 小数且超过两位小数时保留两位小数
                  return value.toFixed(2);
                }
              },
            },
            splitNumber: 10,
            min: minValue,
            max: maxValue,
            interval: (maxValue - minValue) / 10, // 标轴分割间隔
            splitLine: {
              show: true,
              lineStyle: {
                color: this.theme === 'dark' ? 'rgba(110, 112, 121, 0.20)' : '#eee',
              }
            },
            splitArea: {
              show: false,
            },
          })
        }
      }
      // option.yAxis.push({
      //   splitNumber: 10, // 设置 Y 轴分割段数为 10
      // });
      return option
    },
    // 绘制风速风向时程图
    setWindChart(data) {
      // 调整风速风向数据位置，风速为第一列，风向为第二列
      let tmpData = []
      for (let i = 0; i < data.length; i++) {
        if (data[i].name === '风速') {
          tmpData.push(data[i])
        }
      }
      for (let i = 0; i < data.length; i++) {
        if (data[i].name === '风向') {
          tmpData.push(data[i])
        }
      }
      if (tmpData.length !== 2) {
        return this.setRealChart(data)
      }
      // 若风速数据缺失，则补全为0
      if (tmpData[0].times.length === 0) {
        tmpData[1].times.forEach(function (name, index) {
          tmpData[0].times.push(tmpData[1].times[index])
          tmpData[0].values.push(0)
        });
      }
      // 数据格式标准化
      let windData = []
      tmpData[0].values.forEach(function (name, index) {
        if (tmpData[0].values[index] === null) {

        } else {
          tmpData[0].values[index] = parseFloat(tmpData[0].values[index].toFixed(tmpData[0].accuracy >= 0 ? tmpData[0].accuracy : 4))
        }
        windData[index] = [tmpData[0].times[index], tmpData[0].values[index],
        tmpData[1].values[index]]
      });
      const dims = {
        time: 0,
        windSpeed: 1,
        R: 2,
      };
      const arrowSize = this.isBig ? 36 : 18;
      const renderArrow = function (param, api) {
        const point = api.coord([
          api.value(dims.time),
          api.value(dims.windSpeed)
        ]);
        return {
          type: 'path',
          shape: {
            pathData: 'M31 16l-15-15v9h-26v12h26v9z',
            x: -arrowSize / 2,
            y: -arrowSize / 2,
            width: arrowSize,
            height: arrowSize
          },
          rotation: -(api.value(dims.R) / 360 * 2 * Math.PI) + (Math.PI / 2),
          position: point,
          style: api.style({
            stroke: '#555',
            lineWidth: 1
          })
        };
      };
      let formatterNum = this.tabNum === 1 ? 4 : this.searchForm.formatterNum
      let option = {
        toolbox: {
          right: 0,
          top: 0,
          itemSize: this.isBig ? 30 : 12,
          feature: {
            saveAsImage: {
              title: '保存',
              type: 'png',
              backgroundColor: this.theme === 'dark' ? 'rgba(4, 37, 72, 0.8)' : '#fff',
            },
          },
        },
        tooltip: {
          textStyle: {
            align: 'left',
            fontSize: this.isBig ? 24 : 14,
          },
          trigger: "axis",
          formatter: function (params) {
            return [
              echarts.format.formatTime(
                'yyyy-MM-dd',
                params[0].value[dims.time]
              ) +
              ' ' +
              echarts.format.formatTime('hh:mm', params[0].value[dims.time]),
              '风速：' + params[0].value[dims.windSpeed],
              '风向：' + (params[0].value[dims.R] ? params[0].value[dims.R] : '空'),
            ].join('<br>');
          }
        },
        xAxis: {
          type: 'category',
          name: "时间",
          nameLocation: "middle",
          nameTextStyle: {
            fontSize: this.isBig ? 24 : 12,
            color: this.theme === 'dark' ? '#fff' : '#000',
          },
          nameGap: this.isBig ? 60 : 30,
          axisLabel: {
            showMinLabel: true,
            showMaxLabel: true,
            fontSize: this.isBig ? 24 : 11,
            color: this.theme === 'dark' ? '#fff' : '#000',
            formatter: function (value, index) {
              let dateTimeStr = value.substring(0, value.length - formatterNum)
              const parts = dateTimeStr.split(' ');
              return parts[0] + '\n' + parts[1];
            }
          },
        },
        yAxis: [],
        dataZoom: [
          {
            type: "inside",
          },
        ],
        grid: [
          {
            top: "25%",
            left: "5%",
            right: "5%",
            bottom: this.isBig ? "20%" : "5%",
            containLabel: true,
          },
        ],
        series: [
          {
            type: 'custom',
            renderItem: renderArrow,
            encode: {
              x: dims.time,
              y: dims.windSpeed
            },
            data: windData,
            z: 10
          },
          {
            type: 'line',
            symbol: 'none',
            encode: {
              x: dims.time,
              y: dims.windSpeed
            },
            lineStyle: {
              color: 'rgba(110, 112, 121, 0.50)',
              type: 'dotted',
            },
            data: windData,
            z: 1
          },
        ],
        visualMap: {
          type: 'piecewise',
          // show: false,
          orient: 'horizontal',
          left: 'center',
          top: this.isBig ? 200 : 10,
          textStyle: {
            color: this.theme === 'dark' ? '#fff' : '#000', // 根据主题设置文本颜色
            fontSize: this.isBig ? 28 : 12 // 根据屏幕大小设置字体大小
          },
          itemWidth: this.isBig ? 40 : 20,
          itemHeight: this.isBig ? 30 : 14,
          itemGap: this.isBig ? 40 : 10,
          pieces: [
            {
              gte: 17,
              color: '#D33C3E',
              label: '大风（>=17）'
            },
            {
              gte: 11,
              lt: 17,
              color: '#f4e9a3',
              label: '中风（11 ~ 17）'
            },
            {
              lt: 11,
              color: '#18BF12',
              label: '微风（<11）'
            }
          ],
          seriesIndex: 0,
          dimension: 1
        },
      };
      // 使得曲线的最大值与最小值顶格
      let minValue = this.getMin(tmpData[0].values); // 输出最小值
      let maxValue = this.getMax(tmpData[0].values); // 输出最大值
      // 一些特殊情况处理
      if (maxValue === 0 && minValue === 0) {
        maxValue = 1
        minValue = -1
      } else {
        let delta = (maxValue - minValue) * 0.2
        if (delta === 0) {
          delta = 1
        }
        maxValue = maxValue + delta
        minValue = minValue - delta
      }
      if (minValue < 0) {
        minValue = 0
      }
      option.yAxis.push({
        name: "风速/" + tmpData[0].unit,
        nameTextStyle: {
          fontWeight: "bold",
          fontSize: this.isBig ? 24 : 12,
          color: this.theme === 'dark' ? '#fff' : '#000',
        },
        axisLabel: {
          fontSize: this.isBig ? 22 : 11,
          color: this.theme === 'dark' ? '#fff' : '#000',
          formatter: function (value) {
            return value.toFixed(tmpData[0].accuracy >= 0 ? tmpData[0].accuracy : 4); // 2表示小数为2位
          },
        },
        splitNumber: 10,
        min: minValue,
        max: maxValue,
        interval: (maxValue - minValue) / 10, // 标轴分割间隔
        splitLine: {
          show: true,
          lineStyle: {
            color: this.theme === 'dark' ? 'rgba(110, 112, 121, 0.20)' : '#eee',
          }
        },
        splitArea: {
          show: false,
        },
      })
      // 若风向数据缺失，则省去箭头
      if (tmpData[1].times.length === 0) {
        option.series[0] = {
          type: 'line',
        }
      }

      return option
    },
    // 绘制风速风向时程图-预处理
    setWindChart2(data) {
      let tmpData = []
      for (let i = 0; i < data.length; i++) {
        if (data[i].name === '风速') {
          tmpData.push(data[i])
        }
      }
      for (let i = 0; i < data.length; i++) {
        if (data[i].name === '风向') {
          tmpData.push(data[i])
        }
      }
      if (tmpData.length !== 2) {
        this.setNormalChart(data)
        return
      }
      // 若风速数据缺失，则补全为0
      if (tmpData[0].originData === null) {
        tmpData[0].originData = {
          times: [],
          values: []
        }
        tmpData[0].processedData = {
          times: [],
          values: []
        }
        tmpData[1].originData.times.forEach(function (name, index) {
          tmpData[0].originData.times.push(tmpData[1].times[index])
          tmpData[0].originData.values.push(0)
          tmpData[0].processedData.times.push(tmpData[1].times[index])
          tmpData[0].processedData.values.push(0)
        });
      }
      // 数据格式标准化
      let windData = []
      tmpData[0].originData.values.forEach(function (name, index) {
        tmpData[0].originData.values[index] = parseFloat(tmpData[0].originData.values[index].toFixed(tmpData[0].accuracy >= 0 ? tmpData[0].accuracy : 4))
        windData[index] = [tmpData[0].originData.times[index], tmpData[0].originData.values[index]
          , tmpData[1].originData?.values[index]]
      });
      let windData2 = []
      tmpData[0].processedData.values.forEach(function (name, index) {
        tmpData[0].processedData.values[index] = parseFloat(tmpData[0].processedData.values[index].toFixed(tmpData[0].accuracy >= 0 ? tmpData[0].accuracy : 4))
        windData2[index] = [tmpData[0].processedData.times[index], tmpData[0].processedData.values[index]
          , tmpData[1].processedData?.values[index]]
      });
      const dims = {
        time: 0,
        windSpeed: 1,
        R: 2,
      };
      const arrowSize = this.isBig ? 36 : 18;
      const renderArrow = function (param, api) {
        const point = api.coord([
          api.value(dims.time),
          api.value(dims.windSpeed)
        ]);
        return {
          type: 'path',
          shape: {
            pathData: 'M31 16l-15-15v9h-26v12h26v9z',
            x: -arrowSize / 2,
            y: -arrowSize / 2,
            width: arrowSize,
            height: arrowSize
          },
          rotation: -(api.value(dims.R) / 360 * 2 * Math.PI) + (Math.PI / 2),
          position: point,
          style: api.style({
            stroke: '#555',
            lineWidth: 1
          })
        };
      };
      let formatterNum = this.tabNum === 1 ? 4 : this.searchForm.formatterNum
      let option = {
        toolbox: {
          right: 0,
          top: 0,
          itemSize: this.isBig ? 30 : 12,
          feature: {
            saveAsImage: {
              title: '保存',
              type: 'png',
              backgroundColor: this.theme === 'dark' ? 'rgba(4, 37, 72, 0.8)' : '#fff',
            },
          },
        },
        tooltip: {
          textStyle: {
            align: 'left',
            fontSize: this.isBig ? 24 : 14,
          },
          trigger: "axis",
          formatter: function (params) {
            return [
              echarts.format.formatTime(
                'yyyy-MM-dd',
                params[0].value[dims.time]
              ) +
              ' ' +
              echarts.format.formatTime('hh:mm', params[0].value[dims.time]),
              '风速：' + params[0].value[dims.windSpeed],
              '风向：' + (params[0].value[dims.R] ? params[0].value[dims.R] : '空'),
            ].join('<br>');
          }
        },
        xAxis: {
          type: 'category',
          name: "时间",
          nameLocation: "middle",
          nameTextStyle: {
            fontSize: this.isBig ? 24 : 12,
            color: this.theme === 'dark' ? '#fff' : '#000',
          },
          nameGap: this.isBig ? 60 : 30,
          axisLabel: {
            showMinLabel: true,
            showMaxLabel: true,
            fontSize: this.isBig ? 24 : 11,
            color: this.theme === 'dark' ? '#fff' : '#000',
            formatter: function (value, index) {
              let dateTimeStr = value.substring(0, value.length - formatterNum)
              const parts = dateTimeStr.split(' ');
              return parts[0] + '\n' + parts[1];
            }
          },
        },
        yAxis: [],
        dataZoom: [
          {
            type: "inside",
          },
        ],
        grid: [
          {
            top: "30%",
            left: "5%",
            right: "5%",
            bottom: "5%",
            bottom: this.isBig ? "20%" : "5%",
            containLabel: true,
          },
        ],
        legend: {},
        series: [
          {
            name: '风向',
            type: 'custom',
            renderItem: renderArrow,
            encode: {
              x: dims.time,
              y: dims.windSpeed
            },
            data: windData,
            z: 10
          },
          {
            name: '风速',
            type: 'line',
            symbol: 'none',
            encode: {
              x: dims.time,
              y: dims.windSpeed
            },
            lineStyle: {
              color: 'rgba(110, 112, 121, 0.50)',
              type: 'dotted'
            },
            data: windData,
            z: 1
          },
          {
            name: '风向预处理结果',
            type: 'custom',
            renderItem: renderArrow,
            encode: {
              x: dims.time,
              y: dims.windSpeed
            },
            data: windData2,
            z: 10
          },
          {
            name: '风速预处理结果',
            type: 'line',
            symbol: 'none',
            encode: {
              x: dims.time,
              y: dims.windSpeed
            },
            lineStyle: {
              color: 'red',
              type: 'dotted'
            },
            data: windData2,
            z: 1
          },
        ],
        visualMap: [
          {
            type: 'piecewise',
            // show: false,
            orient: 'horizontal',
            left: 'center',
            top: this.isBig ? 160 : 23,
            textStyle: {
              color: this.theme === 'dark' ? '#fff' : '#000', // 根据主题设置文本颜色
              fontSize: this.isBig ? 28 : 12 // 根据屏幕大小设置字体大小
            },
            itemWidth: this.isBig ? 40 : 20,
            itemHeight: this.isBig ? 30 : 14,
            itemGap: this.isBig ? 40 : 10,
            pieces: [
              {
                gte: 17,
                color: '#D33C3E',
                label: '大风（>=17）'
              },
              {
                gte: 11,
                lt: 17,
                color: '#f4e9a3',
                label: '中风（11 ~ 17）'
              },
              {
                lt: 11,
                color: '#18BF12',
                label: '微风（<11）'
              }
            ],
            seriesIndex: 0,
            dimension: 1
          },
          {
            type: 'piecewise',
            // show: false,
            orient: 'horizontal',
            left: 'center',
            top: this.isBig ? 200 : 43,
            textStyle: {
              color: this.theme === 'dark' ? '#fff' : '#000', // 根据主题设置文本颜色
              fontSize: this.isBig ? 28 : 12 // 根据屏幕大小设置字体大小
            },
            itemWidth: this.isBig ? 40 : 20,
            itemHeight: this.isBig ? 30 : 14,
            itemGap: this.isBig ? 40 : 10,
            pieces: [
              {
                gte: 17,
                color: '#D33C3E',
                label: '大风（>=17）预处理'
              },
              {
                gte: 11,
                lt: 17,
                color: '#f4e9a3',
                label: '中风（11 ~ 17）预处理'
              },
              {
                lt: 11,
                color: '#18BF12',
                label: '微风（<11）预处理'
              }
            ],
            seriesIndex: 2,
            dimension: 1
          }
        ],
      };
      // 使得曲线的最大值与最小值顶格
      let minValue = this.getMin(tmpData[0].originData.values); // 输出最小值
      let maxValue = this.getMax(tmpData[0].originData.values); // 输出最大值
      // 一些特殊情况处理
      if (maxValue === 0 && minValue === 0) {
        maxValue = 1
        minValue = -1
      } else {
        let delta = (maxValue - minValue) * 0.2
        if (delta === 0) {
          delta = 1
        }
        maxValue = maxValue + delta
        minValue = minValue - delta
      }
      if (minValue < 0) {
        minValue = 0
      }
      option.yAxis.push({
        name: "风速/" + tmpData[0].unit,
        nameTextStyle: {
          fontWeight: "bold",
          fontSize: this.isBig ? 24 : 12,
          color: this.theme === 'dark' ? '#fff' : '#000',
        },
        axisLabel: {
          fontSize: this.isBig ? 22 : 11,
          color: this.theme === 'dark' ? '#fff' : '#000',
          formatter: function (value) {
            return value.toFixed(tmpData[0].accuracy >= 0 ? tmpData[0].accuracy : 4); // 2表示小数为2位
          },
        },
        splitNumber: 10,
        min: minValue,
        max: maxValue,
        interval: (maxValue - minValue) / 10, // 标轴分割间隔
        splitLine: {
          show: true,
          lineStyle: {
            color: this.theme === 'dark' ? 'rgba(110, 112, 121, 0.20)' : '#eee',
          }
        },
        splitArea: {
          show: false,
        },
      })
      // 若风向数据缺失，则省去箭头
      if (tmpData[1].originData === null) {
        option.series[0] = {
          type: 'line',
        }
        option.series[2] = {
          type: 'line',
        }
      }
      return option
    },
    // 绘制车辆荷载图
    setVehicleLoadChart(data) {
      let axles = {}
      let axlesIndex = -1
      let carWeight = {}
      let carWeightIndex = -1
      let lane = {}
      let laneIndex = -1
      for (let i = 0; i < data.length; i++) {
        if (data[i].name === '车型') {
          axles = data[i]
          axlesIndex = i
        } else if (data[i].name === '总重') {
          carWeight = data[i]
          carWeightIndex = i
        }  else if (data[i].name === '车道号') { // 新增：查找车道数据
          lane = data[i]
          laneIndex = i
        }
      }
      if (axlesIndex < 0 || carWeightIndex < 0 || laneIndex < 0) {
        // this.setEmptyChart("未选择车型或总重数据")
        return
      }
      let drawData = []
      for (let i = 0; i < 23; i++) {
        drawData.push([])
      }
      lane.values.forEach((item, index) => {
        let tmpACar = [lane.times[index]]
        for (let i = 0; i < data.length; i++) {
          tmpACar.push(data[i].values[index])
        }
        axles.values[index] = parseInt(axles.values[index])
        if (axles.values[index] < 0 || axles.values[index] > 22) {
          axles.values[index] = 0
        }
        // 将车道号值转换为对应的 yAxis.data 索引
        const laneValue = data[laneIndex].values[index];
        const laneIndexInYAxis = laneValue - 1; // 因为 yAxis.data 索引从 0 开始
        tmpACar[laneIndex + 1] = laneIndexInYAxis;
        drawData[axles.values[index]].push(tmpACar)
      })
      let dataZoomMinSpan = this.isBig ? 2 : 1;
      let dataZoomMaxSpan = this.isBig ? 20 : 10;

      const renderImg = (param, api) => {
        const point = api.coord([
          api.value(0),
          api.value(laneIndex + 1)
        ]);
        let tmpAxles = api.value(axlesIndex + 1)
        return {
          type: 'image',
          style: {
            image: require('@/assets/vehiclePic/' + tmpAxles + '.png'),
            x: -30,
            y: -7.5,
            width: this.isBig ? 120 : 60,
            height: this.isBig ? 30 : 15,
            opacity: 0.8
          },
          position: point,
        };
      };

      let option = {
        toolbox: {
          right: 0,
          top: 0,
          itemSize: this.isBig ? 30 : 12,
          feature: {
            saveAsImage: {
              title: '保存',
              type: 'png',
              backgroundColor: this.theme === 'dark' ? 'rgba(4, 37, 72, 0.8)' : '#fff',
            },
          },
        },
        tooltip: {
          textStyle: {
            align: 'left',
            fontSize: this.isBig ? 24 : 14,
          },
          // trigger: "axis",
          trigger: "item", 
          confine: true,
          axisPointer: {
            type: 'cross', // 设置为 cross 以实现竖向提示框
            crossStyle: {
              color: '#999'
            }
          },
          formatter: (params) => {
            params = params.data
            let tmpResult = []
            tmpResult.push('时间：' + params[0].split('.')[0])
            for (let i = 0; i < data.length; i++) {
              if (i === axlesIndex) {
                tmpResult.push(data[i].name + "：" + vehicleTypeList[params[i + 1]].name + (data[i].unit ? data[i].unit : ''))
              } else if (i === laneIndex) {
                // 还原车道号，加 1 显示正确的值
                const originalLaneNumber = params[i + 1] + 1; 
                tmpResult.push("车道号：" + originalLaneNumber + (data[i].unit ? data[i].unit : ''))
              } else {
                tmpResult.push(data[i].name + "：" + params[i + 1] + (data[i].unit ? data[i].unit : ''))
              }
            }
            return tmpResult.join('<br>');
          }
        },
        xAxis: {
          type: 'time',
          name: "时间",
          nameLocation: "middle",
          nameTextStyle: {
            fontSize: this.isBig ? 24 : 12,
            color: this.theme === 'dark' ? '#fff' : '#000',
          },
          nameGap: this.isBig ? 60 : 30,
          axisLabel: {
            formatter: "{yyyy}-{MM}-{dd}\n{hh}:{mm}:{ss}",
            showMinLabel: true,
            showMaxLabel: true,
            fontSize: 11,
            fontSize: this.isBig ? 24 : 11,
            color: this.theme === 'dark' ? '#fff' : '#000',
          },
        },
        yAxis: [],
        dataZoom: [
          {
            type: "inside",
            minSpan: dataZoomMinSpan,
            maxSpan: dataZoomMaxSpan
          },
          {
            type: 'slider',
            xAxisIndex: 0,
            height: this.isBig ? 40 : 20,
            bottom: this.isBig ? 120 : 5,
            minSpan: dataZoomMinSpan,
            maxSpan: dataZoomMaxSpan
          }
        ],
        grid: [
          {
            top: "20%",
            left: "2%",
            right: "5%",
            bottom: this.isBig ? "20%" : "15%",
            containLabel: true,
          },
        ],
        series: [],
        legend: {
          type: 'scroll',
          top: this.isBig ? "10%" : '0',
          itemGap: this.isBig ? 40 : 10,
          data: [],
          itemWidth: this.isBig ? 120 : 60,
          itemHeight: this.isBig ? 30 : 15,
          itemStyle: {
            opacity: 0.8,
          },
          textStyle: {
            color: this.theme === 'dark' ? '#fff' : '#000',
            fontSize: this.isBig ? 25 : 14,
          },
        },
      };
      let tmpDrawData = [];
      for (let i = 0; i < 23; i++) {
        if (drawData[i].length !== 0) {
          tmpDrawData.push({
            type: i,
            value: drawData[i]
          })
        }
      }
      drawData = tmpDrawData;
      //加入多行数据
      for (let i = 0; i < drawData.length; i++) {
        option.series.push({
          type: "custom",
          name: vehicleTypeList[drawData[i].type].name,
          renderItem: renderImg,
          data: drawData[i].value,
          encode: {
            x: 0,
            y: laneIndex + 1
          },
        });
        option.legend.data.push({
          name: vehicleTypeList[drawData[i].type].name,
          icon: 'image://' + require('@/assets/vehiclePic/' + drawData[i].type + '.png'),
        })
      }
      option.yAxis = {
        // name: lane.name + "/" + lane.unit,
        name: lane.name,
        type: 'category', // 设置为分类轴
        data: [1, 2, 3, 4], // 固定车道号
        nameTextStyle: {
          fontWeight: "bold",
          fontSize: 12,
          fontSize: this.isBig ? 24 : 12,
          color: this.theme === 'dark' ? '#fff' : '#000',
        },
        axisLabel: {
          fontSize: this.isBig ? 22 : 11,
          color: this.theme === 'dark' ? '#fff' : '#000',
          // formatter: function (value) {
          //   return value.toFixed(lane.accuracy >= 0 ? lane.accuracy : 4); // 2表示小数为2位
          // },
        },
        splitLine: {
          show: true,
          lineStyle: {
            color: this.theme === 'dark' ? 'rgba(110, 112, 121, 0.20)' : '#eee',
          }
        },
        splitArea: {
          show: false,
        },
        // splitNumber: 6,
        // min: minValue,
        // max: maxValue,
        // interval: (maxValue - minValue) / 6, // 标轴分割间隔
      }
      return option
    },
    // 临时：便利获取车辆数据并组装起来
    async getVehicleData(arr) {
      const promises = arr.map(async(el) => {
        const url = "https://jkjc.yciccloud.com:8000/xboot/displayScreen/default/getCalibratedRealTimeDataYH";
        const params = {
          nodeCode: el.code,
          sensorId: el.sensorId,
          structureNodeCode: this.$route.query.code,
          granularityNum: 1,
          granularityType: 0,
          analysisType: 2,
        }
        const res = await fetchGet(url, params)
        if (res.code === 200) {
          return res.result
        }
        return null
      })
      const result = await Promise.all(promises)
      // 用于存储中间结果的对象
      const mergedObj = {};

      // 遍历 newArray 中的每个子数组
      result.forEach(subArray => {
        subArray.forEach(item => {
          const { name, times, values, unit, accuracy, frequency } = item;
          if (!mergedObj[name]) {
            // 如果该 name 还未在 mergedObj 中，初始化一个新对象
            mergedObj[name] = {
              times: [...times],
              values: [...values],
              unit,
              name,
              accuracy,
              frequency
            };
          } else {
            // 如果该 name 已存在，合并 times 和 values 数组
            mergedObj[name].times = [...mergedObj[name].times, ...times];
            mergedObj[name].values = [...mergedObj[name].values, ...values];
          }
        });
      });
      const mergedArray = Object.values(mergedObj);
      this.option = this.setVehicleLoadChart(mergedArray)
      this.optionKey = new Date().getTime();
    },
    // 判别监测类型 车辆荷载2  风速风向1  其他0
    async monitorTypeJudge(selectedSensor) {
      for (let i = 0; i < selectedSensor.specificMonitorDataType.length; i++) {
        if (selectedSensor.specificMonitorDataType[i] === 2) {
          this.searchForm.granularityNum = 1
          this.searchForm.granularityType = { index: 0, label: '原始' }
          this.searchForm.formatter = "{yyyy}-{MM}-{dd}\n{hh}:{mm}:{ss}";
          this.searchForm.formatterNum = 4;
          this.searchForm.granularityTypeName = '原始'
          this.$emit('changeGranularity', this.searchForm)
          this.$emit('setDisabled', true)
          return 2;
        }
      }
      for (let i = 0; i < selectedSensor.specificMonitorTypeName.length; i++) {
        if (selectedSensor.specificMonitorTypeName[i].indexOf('风') >= 0) {
          return 1;
        }
      }
      return 0;
    },
    //绘制空表 提示信息
    setEmptyChart(msg) {
      let option = {
        title: {
          text: msg,
          left: 'center',
          top: 'center',
          textStyle: {
            fontSize: this.isBig ? 32 : 14,
            color: '#606266',
            fontWeight: 700
          },
        },
      };
      this.option = option
      this.optionKey = new Date().getTime();
    },
    // 公共方法-获取最小值
    getMin(arr) {
      if (arr.length === 0) {
        return 0;
      }
      let tmpValue = arr[0];
      arr.forEach((value, index) => {
        if (tmpValue > value) {
          tmpValue = value
        }
      })
      return tmpValue
    },
    // 公共方法-获取最大值
    getMax(arr) {
      if (arr.length === 0) {
        return 0;
      }
      let tmpValue = arr[0];
      arr.forEach((value, index) => {
        if (tmpValue < value) {
          tmpValue = value
        }
      })
      return tmpValue
    }
  },
  computed: {},
  watch: {
  },
}
</script>

<style lang="scss" scoped>
@import "@/assets/styles/utils.scss";

.empty-box {
  height: 100%;
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: vwpx(40px);
  color: #666
}

.chart {
  width: 100%;
  height: 100%;
}
</style>