<template>
  <div class="right-box" :class="oneMap ? 'one-map' : ''">
    <div class="right-box-head">
      <h5>定期检查</h5>
    </div>
    <div
      style="height: calc(100% - 53px); width: 100%"
      class="container-view-list"
    >
      <el-table
        ref="table"
        v-loading="loading"
        height="99%"
        border
        :data="tableData"
        :header-cell-style="{ height: '36px' }"
      >
        <el-table-column label="序号" type="index" width="50" align="center" />
        <el-table-column
          label="年份"
          align="center"
          prop="checkYear"
          min-width="120"
          show-overflow-tooltip
        />
        <el-table-column
          label="管理处"
          align="center"
          prop="managementMaintenanceName"
          min-width="120"
          show-overflow-tooltip
        />
        <el-table-column
          label="隧道编码"
          align="center"
          prop="tunnelCode"
          min-width="120"
          show-overflow-tooltip
        />
        <el-table-column
          label="隧道名称"
          align="center"
          prop="tunnelName"
          min-width="120"
          show-overflow-tooltip
        />
        <el-table-column
          label="检查类型"
          align="center"
          prop="checkType"
          min-width="120"
          show-overflow-tooltip
        >
          <template slot-scope="scope">
            <dict-tag
              :options="dict.type.tunnel_periodic_detection_type"
              :value="scope.row.checkType"
            />
          </template>
        </el-table-column>
        <el-table-column
          label="检查名称"
          align="center"
          prop="checkName"
          min-width="120"
          show-overflow-tooltip
        />
        <el-table-column
          label="养护单位"
          align="center"
          prop="managementMaintenanceName"
          min-width="120"
          show-overflow-tooltip
        />
        <el-table-column
          label="检查单位"
          align="center"
          prop="checkUnit"
          min-width="120"
          show-overflow-tooltip
        />
        <el-table-column
          label="检查描述"
          align="center"
          prop="remark"
          min-width="120"
          show-overflow-tooltip
        />
        <el-table-column
          label="开始时间"
          align="center"
          prop="startDate"
          min-width="120"
          show-overflow-tooltip
        />
        <el-table-column
          label="结束时间"
          align="center"
          prop="endDate"
          min-width="120"
          show-overflow-tooltip
        />
        <el-table-column
          label="检查结果"
          align="center"
          prop="checkResult"
          min-width="120"
          show-overflow-tooltip
        />
        <el-table-column
          label="养护建议"
          align="center"
          prop="suggestions"
          min-width="120"
          show-overflow-tooltip
        />
        <el-table-column
          label="评定单位"
          align="center"
          prop="assessmentUnit"
          min-width="120"
          show-overflow-tooltip
        />
        <el-table-column
          label="评定等级"
          align="center"
          prop="assessmentGrade"
          min-width="120"
          show-overflow-tooltip
        >
          <template slot-scope="scope">
            <dict-tag
              :options="dict.type.tunnel_assess_grade"
              :value="scope.row.assessmentGrade"
            />
          </template>
        </el-table-column>
        <el-table-column
          label="检查报告"
          align="center"
          prop="originalFilename"
          min-width="300"
          show-overflow-tooltip
        >
          <template slot-scope="scope">
            <el-button
               v-if="scope.row.reportName"
              type="text"
              icon="el-icon-paperclip"
              @click.stop="handlePreview(scope.row)"
              >{{ scope.row.reportName }}</el-button
            >
            <span v-else></span>
          </template>
        </el-table-column>
        <el-table-column
          label="负责人"
          align="center"
          prop="personInCharge"
          min-width="120"
          show-overflow-tooltip
        />
      </el-table>
      <FilePreview :owner-id="ownerId" :office-preview-visible="officePreviewVisible" @close="officePreviewVisible=false"/>

      <pagination
        :total="total"
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
        :pageSizes="[10, 20, 30, 50, 100, 1000]"
        @pagination="getList"
      />
    </div>
  </div>
</template>

<script>
import { getListPage } from "@/api/baseData/tunnel/periodic/index";
import { getFile } from "@/api/file";
import FilePreview from "@/components/FilePreview/index.vue";

export default {
  name: "tunnel-archives-periodic",
  inject: ["oneMap"],
  props: {
    tunnelCode: {
      type: undefined,
      default: "",
    },
  },
  components: {FilePreview},
  dicts: ["tunnel_periodic_detection_type", "tunnel_assess_grade"],
  data() {
    return {
      loading: false,
      queryParams: { pageNum: 1, pageSize: 10 },
      total: 0,
      tableData: [],
      officePreviewVisible:false,
      ownerId:''
    };
  },
  created() {
    this.getList();
  },
  methods: {
    getList() {
      this.loading = true;
      getListPage({ ...this.queryParams, tunnelCode: this.tunnelCode })
        .then(async (res) => {
          if (res.code === 200) {
            for (let index = 0; index < res.rows.length; index++) {
              const el = res.rows[index];
              if (el.reportPath == parseInt(el.reportPath)) {
                const r = await getFile({ ownerId: el.reportPath });
                if (r.code === 200) {
                  el.originalFilename = r.data?.originalFilename;
                  el.fileUrl = r.data?.url;
                }
              }
            }
            this.tableData = res.rows;
            this.total = res.total;
          }
        })
        .finally(() => {
          this.loading = false;
        });
    },
    async handlePreview(row) {
      this.officePreviewVisible=true;
      this.ownerId=row.reportPath;
    },
  },
  computed: {},
  watch: {},
};
</script>

<style lang="scss" scoped>
@import "@/assets/styles/common.scss";
::v-deep {
  .el-table__row:hover td {
    background: #b7daff !important;
  }
}
.container-view-list {
  // padding: 0;
}
.right-box {
  width: 100%;
  height: 100%;
  background-color: #fff;
  border-radius: 10px;
  box-shadow: 0px 0px 10px 0px rgba(0, 0, 0, 0.2);
  position: relative;
  .right-box-head {
    width: 100%;
    height: 52px;
    background: #409eff;
    border-radius: 10px 10px 0 0;
    display: flex;
    justify-content: center;
    align-items: center;
    h5 {
      font-family: Microsoft YaHei, Microsoft YaHei;
      font-weight: 700;
      font-size: 14px;
      color: #ffffff;
    }
  }
}
</style>