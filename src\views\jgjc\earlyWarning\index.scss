
.type-popper {
  .el-select-dropdown__item {
    color: white;
    font-size: vwpx(32px) !important;
  }

  .hover {
    background: #42abff !important;
    color: white;
  }
}

::v-deep .el-image__error {
  background-color: rgba(0, 0, 0, 0) !important;
}

::v-deep .el-image__placeholder {
  background-color: rgba(0, 0, 0, 0) !important;
}

::v-deep .el-form-item__label {
  color: #42abff;
  font-size: vwpx(40px);
  line-height: 3vmin;
  padding-right: 2vmin;
}

::v-deep .el-form-item__content {
  font-size: vwpx(40px);
  line-height: 3vmin;
}

::v-deep .el-input {
  .el-input__inner {
    height: vwpx(68px);
    background-color: rgba(1, 102, 254, 0.7);
    border: 1px solid #0166fe;
    color: #ffffff;
    font-size: vwpx(32px);
  }

  .el-input__inner::placeholder {
    color: #bbbbbb;
  }

  .el-input-group__append {
    background-color: rgba(1, 102, 254, 0.7);
    border: 1px solid #0166fe;
    color: #ffffff;
    border-left: none;
    padding: 0 vwpx(20px); // 10 * 2
    cursor: pointer;
  }

  ::v-deep .el-select-dropdown__item {
    color: #ffffff !important;
    font-size: vwpx(32px) !important;
    margin: vwpx(30px) 0;
  }

  ::v-deep .el-select-dropdown__item.selected {
    color: #42abff !important;
  }

  ::v-deep .el-input--mini .el-input__inner {
    height: vwpx(67px);
    line-height: vwpx(97px);
  }
}

::v-deep .el-select-dropdown {
  .el-select-dropdown__item {
    color: white;
    height: 3vmin;
    line-height: 2vmin;
    padding: 0.5vmin;
  }

  .hover {
    background: #42abff !important;
    color: white;
  }
}

::v-deep .el-select-dropdown {
  background: rgba(7, 40, 87, 0.8) !important;
  border-color: #0166FE;
}

::v-deep .el-form-item--mini.el-form-item {
  margin-bottom: vwpx(120px);
}

.model-btn {
  width: vwpx(200px);
  height: vwpx(68px);
  font-size: vwpx(32px) !important;
}

//::v-deep .el-pager li {
//  min-width: 20px !important;
//  min-height: 20px !important;
//}
//::v-deep .btn-prev {
//  min-width: 20px !important;
//  min-height: 20px !important;
//}
//
//::v-deep .btn-next {
//  min-width: 20px !important;
//  min-height: 20px !important;
//}

// 文件：styles/global-scrollbar.scss
// 注意：需要在主样式文件中导入（如 main.js 或 App.vue）

/*-----------------------------
  1. Element UI 滚动条样式修改 (el-scrollbar)
-----------------------------*/
// 覆盖所有 el-scrollbar 组件
::v-deep {
  .el-scrollbar {
    // 滚动条轨道
    &__bar {
      // 垂直滚动条
      &.is-vertical {
        width: 8px !important; // 加宽滚动条
        .el-scrollbar__thumb {
          background-color: rgba(66, 171, 255, 0.6) !important; // 主色调蓝色
          &:hover {
            background-color: #42abff !important; // 悬停时更亮
          }
        }
      }

      // 水平滚动条
      &.is-horizontal {
        height: 8px !important;

        .el-scrollbar__thumb {
          background-color: rgba(66, 171, 255, 0.6) !important;
        }
      }
    }

    // 滚动条角落（横向和纵向交汇处）
    &__corner {
      background-color: #0b1c3a; // 与大屏背景色一致
    }
  }
}

/*-----------------------------
  2. 原生浏览器滚动条样式（影响全局）
-----------------------------*/
::-webkit-scrollbar {
  width: 8px; // 垂直滚动条宽度
  height: 8px; // 水平滚动条高度
}

// 滚动条轨道
::-webkit-scrollbar-track {
  background: #0b1c3a; // 与大屏深色背景匹配
  border-radius: 4px;
}

// 滚动条滑块
::-webkit-scrollbar-thumb {
  background: rgba(66, 171, 255, 0.6);
  border-radius: 4px;

  &:hover {
    background: #42abff; // 悬停高亮
  }
}

// 滚动条角落
::-webkit-scrollbar-corner {
  background: #0b1c3a;
}

.tableDiv {
  margin-top: vwpx(130px);
  height: 82%;
  // 表头
  ::v-deep .el-table th {
    background-color: transparent;
    background: #1650ad !important;
    background-size: 100% 100%;
  }

  /* 修改表头文字颜色 */
  ::v-deep .el-table .cell {
    color: #ffffff;
  }

  // 去除头部边框线
  ::v-deep .el-table td.el-table__cell, .el-table th.el-table__cell.is-leaf {
    border-bottom: none !important;
  }

  // 表格格栅的第一种颜色
  ::v-deep .el-table tr {
    background: #0d2852;
  }

  ::v-deep .el-table--enable-row-hover .el-table__body tr:hover > td.el-table__cell {
    background: #063d8f;
  }

  // 表格格栅的第er种颜色
  ::v-deep .el-table--striped .el-table__body tr.el-table__row--striped td.el-table__cell {
    background: #063279;
  }

  // 去除body部边框线
  ::v-deep .el-table td.el-table__cell, .el-table th.el-table__cell.is-leaf {
    border-bottom: none !important;
  }

  ::v-deep .el-table__body-wrapper {
    background-color: #0b1c3a;
  }
}

::v-deep .pagination-container {
  background: #0b1c3a!important;
}

::v-deep .el-dialog {
  background: #0b1c3a!important;
}

::v-deep .el-dialog__title {
  color: #fff!important;
}

::v-deep .el-descriptions__body {
  color: #fff!important;
  background-color: #0b1c3a!important;
}
::v-deep .el-descriptions-item__label.is-bordered-label {
  color: #fff!important;
  background-color: #0b1c3a!important;
}

::v-deep .el-textarea__inner {
  color: #fff!important;
  background-color: #0b1c3a!important;
}

::v-deep .el-upload-dragger {
  background-color: #0b1c3a!important;
}

::v-deep .el-tabs__item {
  color: #fff;
}

::v-deep .el-tabs__item.is-active {
  color: #42abff;
}

::v-deep .el-table th.el-table__cell.is-leaf, .el-table td.el-table__cell {
  border-bottom: none;
}

::v-deep .el-table__fixed::before, .el-table__fixed-right::before {
  background-color: #0b1c3a !important;
}

::v-deep .el-table::before, .el-table--group::after, .el-table--border::after {
  background-color: #0b1c3a !important;
}
