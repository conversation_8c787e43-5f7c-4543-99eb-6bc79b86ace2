<template>
  <PageContainer :ifHeader="false">
    <template slot="search">
      <div style="display: flex; align-items: center; margin: 0">
        <CascadeSelection
          style="min-width: 192px"
          :form-data="queryParams"
          v-model="queryParams"
          types="201"
          multiple
        />
        <el-select
          v-model="queryParams.lengthClassification"
          placeholder="隧道种类"
          clearable
          style="margin-left: 20px"
        >
          <el-option
            v-for="dict in dict.type.tunnel_length_classification"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          >
          </el-option>
        </el-select>
        <el-input
          v-model="queryParams.tunnelName"
          placeholder="隧道名称"
          clearable
          style="width: 172px; margin: 0 20px"
        />
        <div style="min-width: 240px">
          <el-button
            v-hasPermi="['baseData:bridgeHis:selectPage']"
            icon="el-icon-search"
            type="primary"
            @click="handleQuery"
            >搜索</el-button
          >
          <el-button icon="el-icon-refresh" @click="resetQuery">重置</el-button>
          <el-button
            v-show="!showSearch"
            @click="showSearch = true"
            icon="el-icon-arrow-down"
            circle
          ></el-button>
          <el-button
            v-show="showSearch"
            @click="showSearch = false"
            icon="el-icon-arrow-up"
            style="
              color: #1890ff;
              border-color: #badeff;
              background-color: #e8f4ff;
            "
            circle
          ></el-button>
        </div>
      </div>
      <el-form
        :model="queryParams"
        ref="queryForm"
        :inline="true"
        v-show="showSearch"
      >
        <div class="first-divider" />
        <el-form-item style="margin: 5px 10px 0 0">
          <el-select
            v-model="queryParams.operationState"
            placeholder="运营状态"
            clearable
          >
            <el-option
              v-for="dict in dict.type.sys_operation_state"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item style="margin: 5px 10px 0 0">
          <div style="width: 100%">
            <el-input
              v-model="queryParams.startStake"
              placeholder="起始桩号"
              clearable
              style="width: calc(50% - 8px)"
            />
            -
            <el-input
              v-model="queryParams.endStake"
              placeholder="结束桩号"
              clearable
              style="width: calc(50% - 8px)"
            />
          </div>
        </el-form-item>
        <el-form-item style="margin: 5px 10px 0 0">
          <el-input
            style="width: 100%"
            v-model="queryParams.tunnelCode"
            placeholder="隧道编码"
            clearable
          />
        </el-form-item>
        <el-form-item style="margin: 5px 10px 0 0">
          <el-select
            v-model="queryParams.inLongTunnelDirectory"
            placeholder="是否在长大隧道"
            clearable
          >
            <el-option
              v-for="dict in dict.type.base_data_yes_no"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item style="margin: 5px 10px 0 0">
          <el-select
            v-model="queryParams.assessmentGrade"
            placeholder="评定等级"
            clearable
          >
            <el-option
              v-for="dict in dict.type.tunnel_assess_grade"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item style="margin: 5px 10px 0 0">
          <el-select
            v-model="queryParams.liningType"
            placeholder="衬砌类型"
            clearable
          >
            <el-option
              v-for="dict in dict.type.tunnel_lining_type"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="建成时间" style="margin: 5px 10px 0 0">
          <el-date-picker
            v-model="dateRange"
            type="daterange"
            range-separator="-"
            start-placeholder="年-月-日"
            end-placeholder="年-月-日"
            value-format="yyyy-MM-dd"
          />
        </el-form-item>
      </el-form>
    </template>
    <template slot="header"> </template>
    <template slot="body">
      <el-table
        v-adjust-table
        ref="table"
        height="100%"
        v-loading="loading"
        border
        :data="staticList"
        :header-cell-style="{ height: '36px' }"
        :row-style="rowStyle"
        @selection-change="handleSelectionChange"
        @row-click="handleRowClick"
      >
        <el-table-column type="selection" width="50" align="center" />
        <el-table-column
          fixed
          label="序号"
          type="index"
          width="50"
          align="center"
        >
          <template v-slot="scope">
            {{
              scope.$index +
              (queryParams.pageNum - 1) * queryParams.pageSize +
              1
            }}
          </template>
        </el-table-column>
        <el-table-column fixed label="操作" align="center" width="50">
          <template slot-scope="scope">
            <el-link
              type="primary"
              :disabled="!scope.row.shape"
              @click.stop="handleLocation(scope.row)"
              >定位</el-link
            >
          </template>
        </el-table-column>
        <el-table-column
          fixed
          label="隧道名称"
          align="center"
          prop="tunnelName"
          min-width="140"
          show-overflow-tooltip
        />
        <el-table-column
          label="隧道编码"
          align="center"
          prop="tunnelCode"
          min-width="140"
          show-overflow-tooltip
        />
        <el-table-column
          label="管理处"
          align="center"
          prop="managementMaintenanceName"
          min-width="140"
        >
        </el-table-column>
        <el-table-column
          label="管养分处"
          align="center"
          prop="managementMaintenanceBranchName"
          min-width="140"
        >
        </el-table-column>
        <el-table-column
          label="养护路段"
          align="center"
          prop="maintenanceSectionName"
          min-width="140"
        >
        </el-table-column>
        <el-table-column
          label="路线名称"
          align="center"
          prop="routeName"
          min-width="140"
          show-overflow-tooltip
        />
        <el-table-column
          label="路线编码"
          align="center"
          prop="routeCode"
          min-width="140"
          show-overflow-tooltip
        />
        <el-table-column
          label="隧道种类"
          align="center"
          prop="lengthClassification"
          min-width="140"
          show-overflow-tooltip
        >
          <template slot-scope="scope">
            <dict-tag
              :options="dict.type.tunnel_length_classification"
              :value="scope.row.lengthClassification"
            />
          </template>
        </el-table-column>
        <el-table-column
          label="隧道所在位置"
          align="center"
          prop="direction"
          min-width="140"
          show-overflow-tooltip
        >
          <template slot-scope="scope">
            <dict-tag
              :options="dict.type.sys_route_direction"
              :value="scope.row.direction"
            />
          </template>
        </el-table-column>
        <el-table-column
          label="隧道养护等级"
          align="center"
          prop="maintenanceGrade"
          min-width="140"
          show-overflow-tooltip
        >
          <template slot-scope="scope">
            <dict-tag
              :options="dict.type.tunnel_maintenance_grade"
              :value="scope.row.maintenanceGrade"
            />
          </template>
        </el-table-column>
        <el-table-column
          label="总体评定等级"
          align="center"
          prop="assessmentGrade"
          min-width="140"
          show-overflow-tooltip
        >
          <template slot-scope="scope">
            <dict-tag
              :options="dict.type.tunnel_assess_grade"
              :value="scope.row.assessmentGrade"
            />
          </template>
        </el-table-column>  
        <el-table-column
          label="中心统一里程"
          align="center"
          prop="unifiedMileageStake"
          min-width="140"
          show-overflow-tooltip
        >
          <template slot-scope="scope">
            {{ formatPile(scope.row.unifiedMileageStake) }}
          </template>
        </el-table-column>
        <el-table-column
          label="隧道长度(m)"
          align="center"
          prop="tunnelLength"
          min-width="140"
          show-overflow-tooltip
        />
        <el-table-column
          label="修建年度"
          align="center"
          prop="buildDate"
          min-width="140"
          show-overflow-tooltip
        />
        <el-table-column
          label="建成通车时间"
          align="center"
          prop="operationDate"
          min-width="140"
          show-overflow-tooltip
        />
        <el-table-column
          label="运营状态"
          align="center"
          prop="operationState"
          min-width="140"
          show-overflow-tooltip
        >
          <template slot-scope="{ row }">
            <el-link
              :underline="false"
              :type="
                { 1: 'info', 2: 'success', 3: 'danger', 4: 'primary' }[
                  row.operationState
                ]
              "
            >
              <DictTag
                :value="row.operationState"
                :options="dict.type.sys_operation_state"
              />
            </el-link>
          </template>
        </el-table-column>
        <el-table-column
          label="所在政区编码"
          align="center"
          prop="areaCode"
          min-width="140"
          show-overflow-tooltip
        />
        <el-table-column
          label="是否跨省隧道"
          align="center"
          prop="crossProvinceTunnel"
          min-width="140"
          show-overflow-tooltip
        >
          <template slot-scope="scope">
            <dict-tag
              :options="dict.type.base_data_yes_no"
              :value="scope.row.crossProvinceTunnel"
            />
          </template>
        </el-table-column>
        <el-table-column
          label="是否在长大隧道目录"
          align="center"
          prop="inLongTunnelDirectory"
          min-width="150"
          show-overflow-tooltip
        >
          <template slot-scope="scope">
            <dict-tag
              :options="dict.type.base_data_yes_no"
              :value="scope.row.inLongTunnelDirectory"
            />
          </template>
        </el-table-column>
        <el-table-column
          label="是否水下隧道"
          align="center"
          prop="underwaterTunnel"
          min-width="140"
          show-overflow-tooltip
        >
          <template slot-scope="scope">
            <dict-tag
              :options="dict.type.base_data_yes_no"
              :value="scope.row.underwaterTunnel"
            />
          </template>
        </el-table-column>
        <el-table-column
          label="是否锁定"
          align="center"
          prop="isLocked"
          min-width="140"
          show-overflow-tooltip
        >
          <template slot-scope="{ row }">
            <el-link
              :underline="false"
              :type="row.isLocked ? 'danger' : 'info'"
            >
              {{ row.isLocked ? "是" : "否" }}
            </el-link>
          </template>
        </el-table-column>
        <el-table-column
          label="设计时速(km/h)"
          align="center"
          prop="designSpeed"
          min-width="140"
          show-overflow-tooltip
        />
        <el-table-column
          label="衬砌类型"
          align="center"
          prop="liningType"
          min-width="140"
          show-overflow-tooltip
        >
          <template slot-scope="scope">
            <dict-tag
              :options="dict.type.tunnel_lining_type"
              :value="scope.row.liningType"
            />
          </template>
        </el-table-column>
        <el-table-column
          label="围岩等级"
          align="center"
          prop="surroundingRockGrade"
          min-width="140"
          show-overflow-tooltip
        />
        <el-table-column
          label="数据状态"
          align="center"
          prop="status"
          min-width="140"
          show-overflow-tooltip
        >
          <template slot-scope="{ row }">
            <el-link
              :underline="false"
              :type="
                {
                  1: 'success',
                  2: 'primary',
                  3: 'info',
                  4: 'danger',
                  5: 'success',
                }[row.status]
              "
            >
              <DictTag
                :value="row.status"
                :options="dict.type.base_data_state"
              />
            </el-link>
          </template>
        </el-table-column>

        <el-table-column
          label="变更人"
          align="center"
          prop="operateBy"
          min-width="140"
          show-overflow-tooltip
        />
        <el-table-column
          label="变更时间"
          align="center"
          prop="operateTime"
          min-width="140"
          show-overflow-tooltip
        />
        <el-table-column fixed="right" label="档案" align="center" width="50">
          <template slot-scope="scope">
            <el-button
              v-hasPermi="['baseData:bridgeHis:info']"
              type="text"
              @click.stop="goArchivesPage(scope.row)"
              >查看</el-button
            >
          </template>
        </el-table-column>
      </el-table>
      <pagination
        :total="total"
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
        :pageSizes="[10, 20, 30, 50, 100, 1000]"
        @pagination="getList"
      />
    </template>

    <Drawings
      v-if="showDrawing"
      :showDrawing="showDrawing"
      :typeCode="'BS130'"
      :id="ids[0]"
      @close="showDrawing = false"
    />
    <MapPosition
      v-if="showMapPosition"
      :dialogVisible="showMapPosition"
      :data="mapPositionData"
      @close="showMapPosition = false"
    />
    <drawer-panel
      :show.sync="panelShow"
      :title="panelTitle"
      size="87%"
      @close="onPanelClose"
    >
      <ArchiveIndex :key="archiveKey" />
    </drawer-panel>
  </PageContainer>
</template>

<script>
import { listStatic } from "@/api/baseData/tunnel/history/index";
import { statusListDialog } from "@/views/baseData/components/statusDialog/list.js";
import CascadeSelection from "@/components/CascadeSelection/index.vue";
import { statusDialog } from "@/views/baseData/components/statusDialog/index.js";
import Drawings from "@/views/baseData/components/drawings/index.vue";
import MapPosition from "@/components/mapPosition/index.vue";
import DrawerPanel from "@/components/RightPanel/drawer.vue";
import ArchiveIndex from "@/views/baseData/tunnel/baseInfo/archives/index.vue";

export default {
  name: "History",
  provide() {
    return {
      oneMap: null,
    };
  },
  components: {
    CascadeSelection,
    statusDialog,
    Drawings,
    MapPosition,
    DrawerPanel,
    ArchiveIndex,
  },
  dicts: [
    "tunnel_maintenance_grade",
    "sys_route_direction",
    "tunnel_length_classification",
    "tunnel_assess_grade",
    "base_data_yes_no",
    "sys_operation_state",
    "tunnel_lining_type",
    'base_data_state'
  ],
  data() {
    return {
      loading: true,
      showAddEdit: false,
      forView: false,
      title: "",
      formData: {},
      showUpload: false,
      importBaseType: "2",
      isUpdate: false,
      importType: 0,
      ids: [],
      selectdTables: [],
      showSearch: false,
      showImportAdd: false,
      total: 0,
      staticList: null,
      queryParams: {
        pageNum: 1,
        pageSize: 20,
        operationState: "2",
      },
      dateRange: [],
      showDrawing: false,
      showMapPosition: false,
      mapPositionData: undefined,
      panelShow: false,
      panelTitle: "隧道档案",
      archiveKey: new Date().getTime(),
    };
  },
  watch: {},
  created() {
    this.getList();
  },
  methods: {
    // 获取表格数据
    getList() {
      this.loading = true;
      if (this.dateRange?.length > 0) {
        this.queryParams.constructionStartDate = this.dateRange[0];
        this.queryParams.constructionEndDate = this.dateRange[1];
      } else {
        this.queryParams.constructionStartDate = "";
        this.queryParams.constructionEndDate = "";
      }
      listStatic(this.queryParams)
        .then((response) => {
          this.staticList = response.rows;
          this.total = response.total;
          this.loading = false;
        })
        .catch(() => {
          this.loading = false;
        });
    },
    // 勾选高亮
    rowStyle({ row, rowIndex }) {
      if (this.ids.includes(row.id)) {
        return { "background-color": "#b7daff", color: "#333" };
      } else {
        return { "background-color": "#fff", color: "#333" };
      }
    },
    // 表格操作-运营状态
    handleOperational(event, row) {
      event.stopPropagation();
      statusListDialog({ dataId: row.assetId, baseDataType: 2 });
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.selectdTables = selection;
      this.ids = selection.map((item) => item.id);
    },
    // 搜索按钮
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    // 重置按钮
    resetQuery() {
      this.dateRange = [];
      this.queryParams = {
        pageNum: 1,
        pageSize: 20,
        operationState: "2",
      };
      this.handleQuery();
    },

    closeImportAdd(v) {
      this.showImportAdd = false;
      if (v) this.getList();
    },
    // 表格点击勾选
    handleRowClick(row) {
      row.isSelected = !row.isSelected;
      this.$refs.table.toggleRowSelection(row);
    },

    // 表格操作-档案查看
    goArchivesPage(row) {
      let { path } = this.$route;
      let query = {
        tunnelName: row.tunnelName,
        id: row.id,
        tunnelCode: row.tunnelCode,
        type: "history",
      };
      this.$router.push({
        path,
        query,
      });
      // 刷新组件
      this.archiveKey = new Date().getTime();
      this.panelShow = true;
      return;
      this.$router.push(
        `baseInfo/archives?tunnelName=${row.tunnelName}&id=${row.id}&tunnelCode=${row.tunnelCode}&type=history`
      );
    },
    // 定位操作
    handleLocation(row) {
      this.mapPositionData = row;
      this.showMapPosition = true;
    },
    onPanelClose() {
      this.panelShow = false;
      let { path } = this.$route;
      this.$router.push({
        path,
        query: {},
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.first-divider {
  width: 100%;
  height: 1px;
  //border-bottom: 1px solid #dcdfe6;
  //margin: 10px 0 5px 0 !important;
}
</style>
