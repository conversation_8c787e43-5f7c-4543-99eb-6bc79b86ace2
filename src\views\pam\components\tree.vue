<template>
  <div class="tree">
    <div class="tree-operate"
      :style="{ width: treeW + 'px', border: !isSpread ? '1px solid #0687FF' : '', padding: !isSpread ? '5px 10px' : '', maxHeight: oneMap ? 'calc(100vh - 15px)' : 'calc(100vh - 95px)' }">
      <template v-if="!isSpread">
        <el-row :gutter="20" class="mb10">
          <el-col :span="(this.menuShowType === 1 && treeChecked) ? 17 : 24" :offset="0" v-if="!clickBoolean">
            <el-input placeholder="输入名称进行过滤" v-model="keyWords">
              <template slot="suffix">
                <i class="el-icon-search input-search-btn" @click="toSearch"></i>
              </template>
            </el-input>
          </el-col>
          <el-col v-if="this.menuShowType === 1 && treeChecked" :span="6" :offset="0" style="padding: 0">
            <el-button type="primary" @click="handleCancelTree">取消选中</el-button>
          </el-col>
          <!-- <el-col :span="18" :offset="0">
            <el-select v-model="deptIds" placeholder="请选择" multiple clearable filterable style="width: 100%;"
              @clear="selectClear">
              <el-option hidden :key="item.id" :value="item.id" :label="item.label"
                v-for="item in deptOptions"></el-option>
              <el-tree :data="deptOptions" :props="{ children: 'children', label: 'label' }" @check="handleNodeCheck"
                node-key="id" show-checkbox check-on-click-node ref="treeRef"></el-tree>
            </el-select>
          </el-col>
          <el-col :span="6" :offset="0">
            <el-button type="primary" @click="handleCancel">取消</el-button>
          </el-col> -->
        </el-row>

        <div class="tree-body" :style="{ maxHeight: oneMap ? 'calc(100vh - 65px)' : 'calc(100vh - 145px)' }">
          <template v-if="clickBoolean && menuShowType == 2">
            <el-breadcrumb separator-class="el-icon-arrow-right">
              <el-breadcrumb-item v-for="(item, index) in breadcrumbData" :key="item.id">
                <el-link :style="{ color: index + 1 == breadcrumbData.length ? '#00FDFD' : '#dddddd' }"
                  :underline="false" @click="handleBack(index)">
                  {{ item.name || '' }}
                </el-link>
              </el-breadcrumb-item>
            </el-breadcrumb>

            <div class="data-list mt5">
              <div class="list-item">
                <div class="item-title" :style="{ backgroundImage: 'url(' + bgImgUlr + ')' }" v-if="breadcrumbData">
                  {{ breadcrumbData[0].name || '' }}
                </div>
                <div @click="handleClickAll(cureentData)" style="cursor: pointer;"
                  :class="documentActive === '' ? 'item-content mt5 mb5 item-content-list-active' : 'item-content mt5 mb5'">
                  <ImgView :id="cureentData.bigIconId" width="132" height="100" v-if="cureentData.bigIconId" />
                  <img src="@/assets/home/<USER>" alt="" v-else />
                  <div class="base-content" v-if="cureentData">
                    <span>{{ cureentData.name }}</span>
                    <div class="base-content-divider"></div>
                    <div class="base-content-data">
                      <span>{{ cureentData.total || 0 }}</span>
                      <span>{{ cureentData.statUnit || '' }}</span>
                    </div>
                  </div>
                </div>
              </div>

              <div class="list-item" v-for="(item, index) in classifyData" :key="index">
                <div class="item-title" :style="{ backgroundImage: 'url(' + bgImgUlr2 + ')' }">
                  {{ item.subClassify ? item.subClassify.name : '' }}
                </div>
                <div class="item-content mt10 mb10" v-if="item.child">
                  <div
                    :class="documentActive === item1.id ? 'item-content-list item-content-list-active' : 'item-content-list'"
                    v-for="(item1, index1) in item.child" :key="'stat' + index1"
                    :style="{ width: item.child.length <= 3 ? (100 / item.child.length - 3.5) + '%' : '30%' }"
                    @click="handleItemClick(item1, item.subClassify)">
                    <span :title="item1.name" class="list-name">{{ item1.name || ' ' }}</span>
                    <div class="linst-number-unit">
                      <span class="list-number" :style="{ color: index == 1 ? '#00FDFD' : '#F2AF4A' }">
                        {{ item1.total || 0 }}
                      </span>
                      <span style="color: rgba(255,255,255,0.64);font-size: 13px;">
                        <sub>{{ item.subClassify.statUnit || '' }}</sub>
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </template>
          <template v-else-if="treeData && treeData.length">
            <div class="tree-content" v-for="(item, index) in treeData" :key="index" @click="treeShow(item)"
              v-loading="treeLoading">
              <div class="tree-content-title">
                {{ item.name }}
                <span class="down" :style="{ transform: !item.spread ? '' : 'rotate(270deg)' }"></span>
              </div>
              <el-tree :data="item.child" :show-checkbox="menuShowType == 1 ? true : false" node-key="id"
                :expand-on-click-node="true" highlight-current :props="{ label: 'name', children: 'child' }"
                class="layer-tree" :ref="'treeRef' + index" :indent="0" v-show="!item.spread" @check="handleCheck"
                @node-click="handleNodeClick(item, $event)" @node-expand="handleExpand" :filter-node-method="filterNode"
                @check-change="(node, checked) => { checkChangeClick(node, checked, index) }" :check-strictly="false">
                <div class="el-tree-node" slot-scope="{ data }" style="width: 100%;">
                  <template v-if="menuShowType == 1">
                    <div style="display: flex;justify-content: space-between;align-items: center;">
                      <span v-if="data.name">{{ data.name }}</span>
                      <span v-if="data.menuType !== 1 && !data.baseLayer"
                        style="color:#FFBE27;display: flex;align-items: center">
                        {{ data.total || 0 }}
                        <img v-if="data.listQueryConfig"
                          :src="require(`@/assets/map/${documentActive === data.id ? 'document-active.png' : 'document.png'}`)"
                          alt="" style="margin-left:5px;" @click.stop="handleDocument(data)" />
                      </span>
                    </div>
                  </template>
                  <template v-else>
                    <div class="el-tree-node__content"
                      style="display: flex;align-items: center;width: 100%;justify-content: flex-start;">
                      <ImgView :id="data.iconId" v-if="data.iconId" style="padding-top: 5px;" />
                      <img src="@/assets/map/yh.png" alt="" style="width: 16px;height: 16px;margin-right: 6px;"
                        v-else />
                      <span v-if="data.name" style="font-family: Microsoft YaHei">{{ data.name }}</span>
                      <span style="margin-left: auto;color: #FFBE27;font-size: 14px;">
                        {{ data.total }} {{ data.statUnit || '' }}
                      </span>
                      <!-- <i class="el-icon-tickets" v-if="data.ifSubClassify == 0"></i> -->
                      <img @click.stop="handleShowTable(data)"
                        :src="require(`@/assets/map/${documentActive === data.id ? 'document-active.png' : 'document.png'}`)" />
                    </div>
                  </template>
                </div>
              </el-tree>
            </div>
          </template>
        </div>
      </template>
    </div>
    <div class="spread-packup" @click="handleSpread">
      <img src="@/assets/map/spread.png" :class="isSpread ? 'spread-img' : 'img'" />
    </div>
  </div>
</template>

<script>
import { getShapeList, getClassifyDataStat } from "@/api/oneMap/layerData"
import { deptTreeSelect } from "@/api/tmpl";
import { mapState, mapActions, mapMutations } from 'vuex';
import ImgView from './common/imgView.vue'
import { addWidthFeature, removeLayer, removeAllLayer, getImageById, addVectorTile, toBack, getLineConfig, onMatching } from './common/mapFun'
import TileLayer from 'ol/layer/Tile';
import XYZ from 'ol/source/XYZ';
import cache from "@/plugins/cache";
import { isBigScreen } from "./common/util";

export default {
  components: {
    ImgView
  },
  data() {
    return {
      keyWords: '',
      deptIds: [],
      deptOptions: [],
      classifyData: null,
      cureentData: {},
      breadcrumbData: [],
      bgImgUlr: require('@/assets/map/title-bg.png'),
      bgImgUlr2: require('@/assets/map/title-bg-2.png'),
      menuType: null,
      params: {},
      indexNum: 0,// 用数值来判断只加载一次
      ltNum: 0, // 用数值来判断只加载一次
      listQueryConfig: {}, // 列表配置参数
      parentNode: {}, // 点击的父节点
      treeLoading: false,// 树加载
      documentActive: '',
      singleRangeData: [],
      singleNode: undefined,
      treeChecked: false, // 是否有选中树
      oneMap: false,
    }
  },
  async created() {
    await this.getDeptList();
    this.treeLoading = true;
    this.getTreeList({ deptIds: this.authDeptIds || (this.deptId === 1 ? [] : [this.deptId]) }).then((res) => {
      this.treeLoading = false
    });
    // 页面刷新 或 取消操作
    this.handleCancel();

    // 监听地图点击
    window.$Bus.$on('mapClick', (data) => {
      // *修改当前 数据 总数
      // this.cureentData.total = data.total || 0
      if (this.legendList && this.legendList.length) {
        // 重新获取数据
        let params = {
          managementMaintenanceIds: [data.dept_id || data.sys_dept_id],
          id: this.legendList[0].id,
          menuType: this.legendList[0].menuType,
          z: this.mapZoom
        }
        this.getClassifyData(params, this.parentNode)
      }
    })
    if (this.$route.name == 'oneMap') {
      this.oneMap = true;
    }
  },
  destroyed() {
    window.$Bus.$off('mapClick', () => { });
  },
  computed: {
    ...mapState({
      treeW: state => state.map.treeW,
      isSpread: state => state.map.isSpread,
      menuShowType: state => state.map.menuShowType,
      treeData1: state => state.map.treeData1,
      treeData2: state => state.map.treeData2,
      clickBoolean: state => state.map.clickBoolean,
      mapZoom: state => state.map.mapZoom,
      deptId: state => state.user.deptId,
      showLevel: state => state.map.showLevel,
      legendList: state => state.map.legendList,
      authDeptIds: state => state.map.authDeptIds,
    }),
    treeData() {
      return this.menuShowType == 1 ? this.treeData1 : this.treeData2;
    },
  },
  watch: {
    menuShowType(newVal, oldVal) {
      this.documentActive = ''
      const list = window.mapLayer.getLayers().getArray();
      const arr = [];
      list.map((layer) => {
        // 移除勾选的图层
        const val = layer.get('treeLayerName');
        if (val) arr.push(layer);
        // 移除表格点击加载的闪烁高亮图层
        if (layer.get('name') === 'clickLayer') {
          arr.push(layer);
        }
      });
      arr.map((l) => {
        window.mapLayer.removeLayer(l);
      });
    }
  },
  methods: {
    ...mapActions({
      setSpread: 'map/setSpread',
      getTreeList: 'map/getTreeList',
      changeClickBoolean: 'map/changeClickBoolean',
      getLegend: 'map/getLegend',
      getTableShow: 'map/getTableShow',
      getDetlCondf: 'map/getDetlCondf',
      getShowLevel: 'map/getShowLevel',
      legendShowOrHide: 'map/legendShowOrHide',
    }),
    ...mapMutations({
      setTableHeader: 'map/setTableHeader',
      setAuthDeptIds: 'map/setAuthDeptIds',
    }),
    async getDeptList() {
      return new Promise((resolve, reject) => {
        deptTreeSelect({ types: 201 }).then((res) => {
          if (res.code == 200) {
            let deptIds = res.data.map(v => v.id)
            this.setAuthDeptIds({ deptIds })
            this.deptOptions = [
              {
                id: '',
                label: '集团公司',
                children: res.data || []
              }
            ]
          }
          // 加载成功 返回
          this.$emit('finish')
          resolve('');
        }).catch(err => {
          reject(err);
        });
      });
    },
    // 左侧数 展开收起
    handleSpread() {
      let bigBool = isBigScreen();
      let width = bigBool ? 500 : 320;
      this.setSpread(this.treeW == 0 ? width : 0)
    },
    // 一级树展开收起
    treeShow(row) {
      row.spread = !row.spread
      this.$forceUpdate();
    },
    // 搜索
    toSearch() {
      this.treeData.forEach((item, index) => {
        if (this.$refs['treeRef' + index]) {
          let ref = this.$refs['treeRef' + index][0]
          ref.filter(this.keyWords);
        }
      })
    },
    filterNode(value, data) {
      if (!value) return true;
      if (!data) return true;
      return data.name.indexOf(value) !== -1;
    },
    // 选中树
    async handleCheck(node, nodes) {
    },
    // 获取shape
    async getShape(row) {
      let deptIdArr = this.deptId == 1 ? [] : [this.deptId]
      let params = {
        managementMaintenanceIds: [...this.deptIds, ...deptIdArr] || [],
        id: row.id,
        menuType: row.menuType == '' ? "" : this.menuType,
        z: this.mapZoom,
        paramsDTO: {
          precisionParams: row.precisionParams || null,
          ks: ''
        }
      }
      this.params = params

      return new Promise(async (resolve, reject) => {
        this.$modal.loading()
        await getShapeList(params).then(res => {
          if (res.code == 200 && res.data) {
            // 获取所有的 缓存部门数据
            let deptArr = onMatching();
            let arr = res.data || []
            for (let item of arr) {
              const foundItem = deptArr.find((longerItem) => longerItem.sys_dept_id === item.sys_dept_id || longerItem.sys_dept_id == item.dept_id);
              if (foundItem) {
                item.mgeom = foundItem.mgeom;
              }
            }
            resolve(arr)
            return arr
          } else {
            resolve([])
          }
        }).catch(() => {
          reject('')
        }).finally(() => {
          this.$modal.closeLoading();
        });
      })
    },
    // 展开树节点
    async handleExpand(obj, node) { },
    // 点击树节点
    async handleNodeClick(item, node) {
      if (this.menuShowType == 1) return
      // 先关闭表格
      this.getTableShow(false)
      this.$modal.loading();
      // const list = window.mapLayer.getLayers().getArray();
      // const arr = [];
      // list.map((layer) => {
      //   if ((layer.get('name') === 'dataLayer') || (layer.get('name') === 'pbfLayer') || (layer.get('name') === 'clickLayer')) {
      //     arr.push(layer);
      //   }
      // });
      // arr.map((l) => {
      //   window.mapLayer.removeLayer(l);
      // });
      this.getLegend([])

      this.documentActive = ''
      this.parentNode = item;
      this.menuType = node.menuType || null;
      let deptIdArr = this.deptId == 1 ? [] : [this.deptId]
      let params = {
        // deptIds: [...this.deptIds, ...deptIdArr] || [],
        managementMaintenanceIds: [...this.deptIds, ...deptIdArr] || [],
        id: node.id,
        menuType: node.menuType,
        z: this.mapZoom
      }
      if (!node.id) {
        node.child = []
        return
      }

      // 获取当前 地图显示层级
      this.getShowLevel(node.showLevel)
      // 加载图标
      let iconImg = [];
      if (node.figureId) {
        iconImg = await getImageById(node.figureId)
        node.icon = iconImg[0]
      }
      this.listQueryConfig = node.listQueryConfig || {}
      let config = null;
      if (typeof node.listQueryConfig == 'string') {
        config = JSON.parse(node.listQueryConfig)
      } else {
        config = node.listQueryConfig
      }
      // 将表头信息保存到状态管理中
      this.setTableHeader({ header: config.tableHeader })
      if (config && config.detlCondf) {
        // 设置详情配置
        this.getDetlCondf(config.detlCondf)
      }
      // 设置图列数据
      node.layerName = 'pbfLayer'
      node.ifShow = true;
      node.opacity = 100;
      this.getLegend([node])
      // 获取 对应数据
      this.getClassifyData(params, item, node);
      if (this.menuShowType == 2) {
        let obj = { managementMaintenanceIds: [...this.deptIds, ...deptIdArr] || [], id: node.id, menuType: node.menuType }
        addVectorTile(window.mapLayer, 'name', 'pbfLayer', JSON.stringify(obj), node)
        let arr = await this.getShape(node)
        if (!arr || (arr && !arr.length)) return
        this.singleRangeData = arr
        // 点击树节点移除地图数据
        removeLayer(window.mapLayer, 'dataLayer', 'name')
        removeLayer(window.mapLayer, 'dataLayer')
        this.singleNode = node
        // 将数据加载到地图
        addWidthFeature(window.mapLayer, arr, 'dataLayer', null, true)
      }
      // 移除点击表格事件的图层数据
      // removeLayer(window.mapLayer, 'clickLayer')
    },
    // 获取树下级数据
    getClassifyData(params, item, node = null) {
      let nodeData = {};
      if (this.menuShowType == 1) {
        node.loading = true
      } else {
        nodeData = node || this.legendList[0]
      }
      this.$modal.loading()
      getClassifyDataStat(params).then(async (res) => {
        if (res.code == 200 && res.data) {
          let data = res.data.map(v => v.subClassify)
          this.classifyData = res.data || []
          if (this.menuShowType == 2) {
            this.cureentData = JSON.parse(JSON.stringify(nodeData))
            this.breadcrumbData = [item, nodeData]
            this.changeClickBoolean(true)
          } else {
          }
          if (data && data.length > 0) {
            // 点击地图-到子级部门
            if (!node) {
              this.cureentData = JSON.parse(JSON.stringify(data[0]))
              let obj = this.legendList[0]
              this.cureentData.name = obj.name
              this.cureentData.bigIconId = obj.bigIconId
              // 先移除
              removeLayer(window.mapLayer, 'dataLayer')
              removeLayer(window.mapLayer, 'dataLayer', 'name')
              let arr = await this.getShape(params)
              // 将数据加载到地图
              addWidthFeature(window.mapLayer, arr, 'dataLayer', null, true)
            }
          }
        }
      }).finally(() => {
        this.$modal.closeLoading();
      });
    },
    // 返回树 结构
    handleBack(index) {
      if (index == 0) {
        this.changeClickBoolean(false)
        this.getTableShow(false)
        // 置空图列数据
        this.getLegend([])
        removeLayer(window.mapLayer, 'pbfLayer')
        removeLayer(window.mapLayer, 'clickLayer')
        removeLayer(window.mapLayer, 'dataLayer', 'name')
        removeLayer(window.mapLayer, 'maskLayer', 'name')
        // removeLayer(window.mapLayer, 'pbfLayer', 'lineLayer')
        // getLineConfig([this.deptId])
        // 移除图层并重新添加
        this.$modal.loading();
        toBack().then(() => {
          this.$modal.closeLoading();
        })
      }
    },
    handleNodeCheck(_node, nodes) {
      this.deptIds = nodes.checkedKeys.filter(v => v != '' && v != null && v != undefined) || [];
      // 触发相关联操作
    },
    // 显示表格数据
    getAllTable(row) {
      let paramsDTO = undefined
      if (row.columnName) {
        paramsDTO = {
          precisionParams: {
            [row.columnName]: row.value
          }
        }
      }
      let deptIdArr = this.deptId == 1 ? [] : [this.deptId]
      // 全局触发获取表格数据
      window.$Bus.$emit('getTable', {
        query: this.listQueryConfig || '{}',
        params: {
          ...this.params,
          menuType: this.menuType,
          id: row && row.layerMenuSubId ? row.layerMenuSubId : row.id ? row.id : undefined,
          paramsDTO: paramsDTO,
          managementMaintenanceIds: [...this.deptIds, ...deptIdArr] || [],
        },
        menuShowType: this.menuShowType,
        row: row
      })
      // 显示右侧 table
      this.getTableShow(true);
    },
    // 监听清空事件
    selectClear() {
      // 清空树选中数据
      this.$refs.treeRef.setCheckedKeys([]);
    },
    // 点击事件
    async handleItemClick(row, item) {
      if (this.documentActive !== row.id) {
        this.documentActive = row.id
      } else {
        this.documentActive = ''
        const list = window.mapLayer.getLayers().getArray();
        const arr = [];
        list.map((layer) => {
          if ((layer.get('name') === 'dataLayer') || (layer.get('name') === 'pbfLayer') || (layer.get('name') === 'clickLayer')) {
            arr.push(layer);
          }
        });
        arr.map((l) => {
          window.mapLayer.removeLayer(l);
        });
        addWidthFeature(window.mapLayer, this.singleRangeData, "dataLayer", null, true);
        let deptIdArr = this.deptId == 1 ? [] : [this.deptId]
        let obj = { managementMaintenanceIds: [...this.deptIds, ...deptIdArr] || [], id: this.singleNode.id, menuType: this.singleNode.menuType }
        addVectorTile(window.mapLayer, 'name', 'pbfLayer', JSON.stringify(obj), this.singleNode)
        this.getTableShow(false);
        return
      }
      row.precisionParams = {}
      row.precisionParams[row.columnName] = row.value
      let obj = {
        ...row,
        ...item,
        id: item.layerMenuSubId,
        menuType: ''
      }
      let deptIdArr = this.deptId == 1 ? [] : [this.deptId]
      let params = {
        // deptIds: [...this.deptIds, ...deptIdArr] || [],
        managementMaintenanceIds: [...this.deptIds, ...deptIdArr] || [],
        id: row.id,
        menuType: row.menuType == '' ? "" : this.menuType,
        z: this.mapZoom,
        paramsDTO: {
          precisionParams: {
            ...row.precisionParams
          },
          ks: ''
        }
      }


      let arr = await this.getShape(obj)
      if (arr && arr.length) {
        // 先移除
        removeLayer(window.mapLayer, 'dataLayer')
        // 将数据加载到地图
        addWidthFeature(window.mapLayer, arr, 'dataLayer', null, true)
        // 触发移除
        removeLayer(window.mapLayer, 'dataLayer', 'name')
      }
      removeLayer(window.mapLayer, 'pbfLayer', 'name')
      let objs = {
        managementMaintenanceIds: [...this.deptIds, ...deptIdArr] || [],
        id: item.layerMenuSubId || row.id,
        menuType: '',
        paramsDTO: {
          precisionParams: {
            ...row.precisionParams
          },
          ks: ''
        }
      }
      addVectorTile(window.mapLayer, 'name', 'pbfLayer', JSON.stringify(objs), row)
      removeLayer(window.mapLayer, 'clickLayer', 'name')
      // 触发列表数据加载
      // 全局触发获取表格数据
      window.$Bus.$emit('getTable', {
        query: this.listQueryConfig || '{}',
        params: { ...params, ...obj },
        menuShowType: this.menuShowType,
        row: row
      })
      // 显示右侧 table
      this.getTableShow(true);
    },
    // 取消事件 2024年8月1日10:07:46 取消地图图层数据-重置数据
    async handleCancel() {
      // 清除图层
      // removeAllLayer(window.mapLayer);
      removeLayer(window.mapLayer, 'dataLayer')
      removeLayer(window.mapLayer, 'pbfLayer')
      // 重新加载 - 基础数据
      this.changeClickBoolean(false);
      this.getTableShow(false);
      this.getDetlCondf({});

      // let arr = await this.getShape(this.params)
      // 将数据加载到地图
      // addWidthFeature(window.mapLayer, arr)
    },
    // 展开选中的未展开的节点
    expandCheckedNotExpandNodes(node, index) {
      let tree = this.$refs['treeRef' + index][0]
      if (node.checked && !node.expanded && !node.isLeaf) {
        node.expand(function () {
          let childNodes = node.childNodes;
          for (let i = 0; i < childNodes.length; i++) {
            let childNode = childNodes[i];
            //手动触发check-change事件，事件处理函数中回继续调用此函数，形成递归展开
            tree.$emit('check-change', childNode.data, childNode.checked, index, childNode.indeterminate);
          }
        })
      }
    },
    async checkChangeClick(nodeData, checked, index) {
      let tree = this.$refs['treeRef' + index][0]
      let node = tree.getNode(nodeData)
      let value = nodeData?.columnName ? ((nodeData?.columnName + nodeData?.value) || nodeData.id) : nodeData.id

      this.$nextTick(() => {
        const checkedNodes = this.$refs['treeRef' + index][0].getCheckedNodes(false, true);
        let arr = []
        checkedNodes.map(el => {
          arr.push(this.$refs['treeRef' + index][0].getNode(el))
        })
        arr = arr.filter(el => el?.level == 1)
        // 获取当前树素有选中
        // let legendArr = []
        // this.treeData.forEach((item, index1) => {
        //   let checkArr = this.$refs['treeRef' + index1][0].getCheckedNodes(false, true)
        //   if (checkArr) {
        //     legendArr = legendArr.concat(checkArr)
        //   }
        // })
        let legendArr = this.getTreeCheckedData();
        legendArr = legendArr.filter(v => v.menuType == 2)
        this.treeChecked = legendArr.length > 0 ? true : false
        this.getLegend(legendArr)
        // 设置详情配置
        // this.getDetlCondf(config.detlCondf)
        // this.getLegend(arr.map(v => v.data))
      });

      //展开选中的未展开的节点
      this.expandCheckedNotExpandNodes(node, index);
      if (checked) {

        let iconImg = [];
        if (nodeData.figureId && !nodeData.icon) {
          iconImg = await getImageById(nodeData.figureId)
          nodeData.icon = iconImg[0]
        }

        let deptIdArr = this.deptId == 1 ? [] : [this.deptId]
        let obj = {
          deptIds: [...this.deptIds, ...deptIdArr] || [],
          id: nodeData.layerMenuSubId,
          menuType: nodeData.menuType,
          paramsDTO: {
            precisionParams: nodeData.columnName ? { [nodeData.columnName]: nodeData.value } : {}
          }
        }
        if (node.isLeaf && nodeData.baseLayer?.layerUrl) {
          let layer = new TileLayer({
            name: nodeData.baseLayer.layerName,
            zIndex: nodeData.baseLayer.showIndex,
            source: new XYZ({
              url: nodeData.baseLayer.layerUrl,
              attributions: nodeData.baseLayer.layerName,
              wrapX: false, // 是否水平包裹
              crossOrigin: 'anonymous'  // 常见的设置为 'anonymous' 或 'use-credentials'
            }),
            preload: Infinity,
            visible: true,
          })
          layer.set("treeLayerName", nodeData.id);
          layer.set("name", nodeData.baseLayer.layerName);
          window.mapLayer.addLayer(layer)
          return
        }
        if (node.isLeaf) {
          this.getShowLevel(nodeData.showLevel)
          addVectorTile(window.mapLayer, 'treeLayerName', value, JSON.stringify(obj), nodeData)
        }
      } else {
        const list = window.mapLayer.getLayers().getArray();
        const arr = [];
        list.map((layer) => {
          const val = layer.get('treeLayerName');
          if (val && val === value) arr.push(layer);
        });
        arr.map((l) => {
          window.mapLayer.removeLayer(l);
        });
      }

    },
    // 获取 树 选中的数据
    getTreeCheckedData() {
      let legendArr = []
      this.treeData.forEach((item, index1) => {
        let checkArr = this.$refs['treeRef' + index1][0].getCheckedNodes(false, true)
        if (checkArr) {
          legendArr = legendArr.concat(checkArr)
        }
      })
      return legendArr
    },
    async handleDocument(row) {
      if (this.documentActive !== row.id) {
        this.documentActive = row.id
        this.listQueryConfig = row.listQueryConfig || {}
        let config = null;
        if (typeof row.listQueryConfig == 'string') {
          config = JSON.parse(row.listQueryConfig)
        } else {
          config = row.listQueryConfig
        }
        if (config && config.detlCondf) {
          // 将表头信息保存到状态管理中
          this.setTableHeader({ header: config.tableHeader })
          // 设置详情配置
          this.getDetlCondf(config.detlCondf)
        }
        let iconImg = [];
        if (row.figureId) {
          iconImg = await getImageById(row.figureId)
        }
        row.icon = iconImg[0]
        this.getAllTable(row)
      } else {
        this.documentActive = ''
        const list = window.mapLayer.getLayers().getArray();
        const arr = [];
        list.map((layer) => {
          if (layer.get('name') === 'clickLayer') {
            arr.push(layer);
          }
        });
        arr.map((l) => {
          window.mapLayer.removeLayer(l);
        });
        this.getTableShow(false);
      }
    },
    // 列表数据展示
    async handleShowTable(row) {
      const list = window.mapLayer.getLayers().getArray();
      const arr = [];
      list.map((layer) => {
        if ((layer.get('name') === 'dataLayer') || (layer.get('name') === 'pbfLayer') || (layer.get('name') === 'clickLayer')) {
          arr.push(layer);
        }
      });
      arr.map((l) => {
        window.mapLayer.removeLayer(l);
      });
      this.getLegend([])
      if (this.documentActive !== row.id) {
        this.documentActive = row.id
      } else {
        this.documentActive = ''
        let data = cache.session.getJSON('rangeData')
        addWidthFeature(window.mapLayer, data, "dataLayer", null, true);
        this.getTableShow(false);
        return
      }
      //1、 加载图标
      let iconImg = [];
      if (row.figureId) {
        iconImg = await getImageById(row.figureId)
        row.icon = iconImg[0]
      } else {
        row.icon = '';
      }
      //2、 设置图列信息
      this.getLegend([row])
      // 3、获取当前 地图显示层级
      this.getShowLevel(row.showLevel)
      // 4、设置列表参数
      this.listQueryConfig = row.listQueryConfig
      // 5、触发表格显示
      this.getAllTable(row)
      // 6、加载矢量切片

      let d = await this.getShape(row)
      // 7、有数据再移除图层数据
      if (d) {
        // 点击树节点移除地图数据
        removeLayer(window.mapLayer, 'dataLayer')
      }
      // 8、将数据加载到地图
      addWidthFeature(window.mapLayer, d, 'dataLayer', null, true)

      // 9、加载矢量数据-先移除切片图层数据
      removeLayer(window.mapLayer, 'pbfLayer')
      let obj = { managementMaintenanceIds: [...this.deptIds] || [], id: row.layerMenuSubId || row.id, menuType: row.menuType }
      addVectorTile(window.mapLayer, 'name', 'pbfLayer', JSON.stringify(obj), row)

      // 10、设置详情配置
      let config = null;
      if (typeof row.listQueryConfig == 'string') {
        config = JSON.parse(row.listQueryConfig)
      } else {
        config = row.listQueryConfig
      }
      // 11、将表头信息保存到状态管理中
      this.setTableHeader({ header: config.tableHeader })
      if (config && config.detlCondf) {
        // 设置详情配置
        this.getDetlCondf(config.detlCondf)
      }

    },
    // 点击加载所有数据
    async handleClickAll(row) {
      this.documentActive = ''
      // 1、重新获取表格数据
      this.getAllTable(row)
      // 2、移除图层数据
      removeLayer(window.mapLayer, 'pbfLayer')
      let deptIdArr = this.deptId == 1 ? [] : [this.deptId]
      let obj = {
        managementMaintenanceIds: [...this.deptIds, ...deptIdArr] || [],
        id: row.layerMenuSubId || row.id,
        menuType: '',
        paramsDTO: {
          precisionParams: {
            ...row.precisionParams
          },
          ks: ''
        }
      }
      // 3、重新加载矢量切片数据
      addVectorTile(window.mapLayer, 'name', 'pbfLayer', JSON.stringify(obj), row)
      let arr = await this.getShape(obj)
      if (arr && arr.length == 1) {
        this.cureentData.total = arr[0].total || 0;
      }
      if (arr && arr.length) {
        // 4、 重新加载部门数据
        removeLayer(window.mapLayer, 'dataLayer', 'name');
        // 将数据加载到地图
        addWidthFeature(window.mapLayer, arr, 'dataLayer', null, true)
      }
    },
    // 取消选中按钮
    handleCancelTree() {
      for (let index = 0; index < this.treeData.length; index++) {
        this.$refs['treeRef' + index][0].setCheckedKeys([])
      }
      const list = window.mapLayer.getLayers().getArray();
      const arr = [];
      list.map((layer) => {
        // 移除勾选的图层
        const val = layer.get('treeLayerName');
        if (val) arr.push(layer);
        // 移除表格点击加载的闪烁高亮图层
        // if (layer.get('name') === 'clickLayer') {
        //   arr.push(layer);
        // }
      });
      arr.map((l) => {
        window.mapLayer.removeLayer(l);
      });
    }
  }
}
</script>

<style lang="scss" scoped>
@import "@/assets/styles/utils.scss";

@font-face {
  font-family: 'YouSheBiaoTiHei';
  /* 自定义的字体名称 */
  src: url('~@/assets/home/<USER>') format('truetype');
  /* 字体文件路径和格式 */
  /* 可选属性，根据需要设置 */
  font-weight: normal;
  font-style: normal;
}

.item-content-list-active {
  box-shadow: inset 0px 0px 5px 0px #FDAF34 !important;
  border: 1px solid #FDAF34 !important;
}

.tree {
  display: flex;
  align-items: center;
  z-index: 8;
  position: absolute;
  left: 5px;
  top: 5px;

  .tree-operate {
    min-height: vwpx(376px);
    overflow-y: auto;
    background: linear-gradient(90deg, rgba(2, 10, 30, 0.8) 0%, rgba(12, 42, 86, 0.2) 100%);
    box-shadow: inset 0px 0px 10px 0px rgba(35, 134, 255, 0.8);
    border-radius: vwpx(20px);

    .tree-body {
      overflow-y: auto;
    }

    .input-search-btn {
      font-size: vwpx(32px);
      width: vwpx(60px);
      height: vwpx(52px);
      line-height: vwpx(52px);
      cursor: pointer;
    }

    .data-list {
      .list-item {

        .item-title {
          width: 100%;
          height: vwpx(64px);
          background-repeat: no-repeat;
          background-size: 100% 100%;

          display: flex;
          align-items: center;
          padding-left: 10px;

          font-family: YouSheBiaoTiHei, YouSheBiaoTiHei;
          font-weight: 400;
          font-size: vwpx(32px);
          color: #FFFFFF;
          line-height: vwpx(26px);
          letter-spacing: 1px;
          text-shadow: 0px 0px 6px #273DFF;
          text-align: left;
          text-transform: none;
        }
      }

      .item-content {
        display: flex;
        align-items: center;
        flex-wrap: wrap;
        max-height: calc(100vh - 480px);
        overflow-y: auto;

        .base-content {
          flex: 1;
          display: flex;
          margin: 0 20px;
          flex-direction: column;

          span {
            color: #ffffff;
          }

          .base-content-divider {
            width: 100%;
            border: 1px solid rgba(156, 189, 255, 0.5);
            margin: 5px 0;
          }

          .base-content-data {

            span:first-child {
              font-family: Microsoft YaHei, Microsoft YaHei;
              font-weight: 700;
              font-size: vwpx(48px);
              text-align: left;
              font-style: normal;
              text-transform: none;
              background: linear-gradient(360deg, #FFB200 0%, #FFFFFF 74%, #FFFFFF 100%);
              background-clip: text;
              -webkit-background-clip: text;
              color: transparent;
            }

            span:last-child {
              font-weight: 400;
              font-size: vwpx(26px);
              color: rgba(255, 255, 255, 0.7);
              text-align: left;
              font-style: normal;
              text-transform: none;
              margin-left: vwpx(6px);
            }
          }
        }

        .item-content-list {
          display: flex;
          align-items: center;
          justify-content: center;
          flex-direction: column;
          cursor: pointer;
          margin: 0 1.6% 8px 1.6%;
          max-height: 58px;
          background: rgba(0, 19, 71, 0.1);
          box-shadow: inset 0px 0px 5px 0px #0687FF;
          border-radius: 6px;
          border: 1px solid #0687FF;

          .list-name {
            font-family: YouSheBiaoTiHei, YouSheBiaoTiHei;
            min-width: vwpx(140px);
            max-width: vwpx(172px);
            font-size: vwpx(32px);
          }

          .linst-number-unit {
            width: 100%;
            display: flex;
            justify-content: center;

            .list-number {
              font-family: Microsoft YaHei, Microsoft YaHei;
              margin-right: 2px;
              font-size: vwpx(32px);
            }
          }

          span {
            font-weight: 500;
            font-size: vwpx(32px);
            text-shadow: 0px 0px 20px #105ED9;
            text-align: center;
            font-style: normal;
            text-transform: none;
            background: linear-gradient(180deg, #FFFFFF 0%, #FFFFFF 60%, #20A9FF 100%);
            background-clip: text;
            -webkit-background-clip: text;
            color: transparent;

            display: block;

            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }
        }
      }
    }

    .tree-content {
      color: #ffffff;
      width: 100%;
      height: auto;
      margin-bottom: 5px;

      .tree-content-title {
        width: 100%;
        height: 32px;
        background-image: url("~@/assets/map/title-bg.png");
        background-repeat: no-repeat;
        background-size: 100% 100%;
        margin-bottom: 5px;
        cursor: pointer;
        display: flex;
        align-items: center;
        padding-left: vwpx(30px);

        font-family: YouSheBiaoTiHei, YouSheBiaoTiHei;
        font-weight: 400;
        font-size: vwpx(36px);
        color: #FFFFFF;
        text-shadow: 0px 0px 6px #273DFF;
        font-style: normal;

        .down {
          width: 15px;
          height: 15px;
          background-image: url("~@/assets/map/down.png");
          background-repeat: no-repeat;
          background-size: 100% 100%;
          margin-left: auto;
        }
      }
    }

    ::v-deep .el-tree {
      background-color: unset;
      color: #ffffff;

      .el-checkbox__input.is-disabled {
        display: none;
      }

      .el-tree-node__content {
        &:hover {
          background: linear-gradient(90deg, rgba(0, 94, 255, 0.5) 0%, rgba(1, 102, 254, 0.05) 100%);
          // border: 1px solid;
          // border-image: linear-gradient(90deg, rgba(0, 120.00000044703484, 254.00000005960464, 1), rgba(0, 120.00000044703484, 254.00000005960464, 0)) 1 1;
        }

        .el-tree-node__expand-icon {
          padding: 2px;
        }
      }

      // 鼠标点击时节点的背景颜色
      .el-tree-node:focus>.el-tree-node__content {
        background: linear-gradient(90deg, rgba(0, 94, 255, 0.5) 0%, rgba(1, 102, 254, 0.05) 100%);
        // border: 1px solid;
        // border-image: linear-gradient(90deg, rgba(0, 120.00000044703484, 254.00000005960464, 1), rgba(0, 120.00000044703484, 254.00000005960464, 0)) 1 1;
      }

      // 鼠标失去焦点时节点背景的颜色
      .is-current>.el-tree-node__content:first-child {
        background: linear-gradient(90deg, rgba(0, 94, 255, 0.5) 0%, rgba(1, 102, 254, 0.05) 100%);
        border: 1px solid;
        border-image: linear-gradient(90deg, rgba(0, 120.00000044703484, 254.00000005960464, 1), rgba(0, 120.00000044703484, 254.00000005960464, 0)) 1 1;
      }

      /* 修改边框颜色 */
      .el-checkbox .el-checkbox__inner {
        border-color: #409eff;
        background-color: unset;
      }

      /* 修改边框颜色 */
      .el-checkbox .el-checkbox__inner:hover {
        border-color: #409eff;
      }

      .el-tree-node>.el-tree-node__children {
        margin-left: 15px;
      }
    }

    ::v-deep .el-input {
      .el-input__inner {
        background-color: rgba(1, 102, 254, 0.2);
        border: 1px solid #0166FE;
        color: #ffffff;
        // border-right: none;
      }

      .el-input__inner::placeholder {
        color: #BBBBBB;
      }

      .el-input-group__append {
        background-color: rgba(1, 102, 254, 0.2);
        border: 1px solid #0166FE;
        color: #ffffff;
        border-left: none;
        padding: 0 10px;
        cursor: pointer;
      }
    }
  }

  .spread-packup {
    height: vwpx(330px);
    width: vwpx(36px);
    cursor: pointer;

    background-image: url('~@/assets/map/left-close.png');
    background-repeat: no-repeat;
    background-size: 100% 100%;

    // background: linear-gradient(90deg, rgba(35, 134, 255, 0.5) 0%, rgba(35, 134, 255, 0.3) 90%, rgba(255, 255, 255, 0.8) 100%);
    // box-shadow: inset 0px 6px 6px 0px rgba(0, 85, 255, 0.3);
    // clip-path: polygon(0 0, 100% 15%, 100% 85%, 0 100%);

    display: flex;
    align-items: center;

    .img {
      transform: rotate(90deg);
      width: 100%;
    }

    .spread-img {
      transform: rotate(270deg);
      width: 100%;
    }
  }

  ::v-deep .el-select__tags {
    flex-wrap: nowrap;
    overflow: auto;
  }
}

::-webkit-scrollbar {
  width: 5px;
  height: 5px;
}

/* 轨道颜色 */
::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.2);
}

/* 滑块颜色 */
::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.6);
}

/* 滑块悬停或活动时的颜色 */
::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.6);
}
</style>