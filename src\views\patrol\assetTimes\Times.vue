<template>
    <div class="app-container">
        <!--查询条件开始-->
        <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" label-width="68px">
            <div style="display: flex; align-items: center; flex-wrap: nowrap">
                <CascadeSelection
                    style="min-width: 192px; margin-right: 10px"
                    :form-data="queryParams"
                    v-model="queryParams"
                    types="201"
                    multiple
                />

                <el-form-item label="" prop="assetCode" style="margin-bottom: 0; margin-right: 10px">
                    <el-input
                        v-model="queryParams.assetCode"
                        :placeholder="`请输入${partsType[type].substring(0, 2)}编码`"
                        clearable
                        prefix-icon="el-icon-user"
                        style="width: 240px"
                        @keyup.enter.native="handleQuery"
                    />
                </el-form-item>

                <el-form-item label="" prop="assetName" style="margin-bottom: 0; margin-right: 10px">
                    <el-input
                        v-model="queryParams.assetName"
                        :placeholder="`请输入${partsType[type].substring(0, 2)}名称`"
                        clearable
                        prefix-icon="el-icon-user"
                        style="width: 240px"
                        @keyup.enter.native="handleQuery"
                    />
                </el-form-item>
                <el-form-item style="margin-bottom: 0">
                    <div style="white-space: nowrap">
                        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">
                            搜索
                        </el-button>
                        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
                    </div>
                </el-form-item>
            </div>
        </el-form>
        <!--查询条件结束-->

        <!--操作按钮区开始-->
        <el-row :gutter="10" style="margin-bottom: 8px">
            <el-col :span="1.5">
                <el-button type="primary" icon="el-icon-download" size="mini" @click="handleExport"> 导出 </el-button>
            </el-col>
        </el-row>
        <!--操作按钮区结束-->

        <!--数据表格开始-->
        <div class="tableDiv">
            <el-table
                v-adjust-table
                ref="table"
                size="mini"
                height="calc(100vh - 260px)"
                style="width: 100%"
                v-loading="loading"
                border
                :data="statisticsList"
                @selection-change="handleSelectionChange"
                :row-style="rowStyle"
                @row-click="handleRowClick"
            >
                <el-table-column type="selection" width="50" align="center" />
                <el-table-column fixed label="序号" type="index" width="50">
                    <template v-slot="scope">
                        {{ scope.$index + (queryParams.pageNum - 1) * queryParams.pageSize + 1 }}
                    </template>
                </el-table-column>
                <el-table-column
                    :label="type === '4' ? '涵洞编码' : `${partsType[type].substring(0, 2)}编码`"
                    align="center"
                    prop="assetCode"
                    min-width="140"
                    show-overflow-tooltip
                />
                <el-table-column
                    v-if="type !== '4'"
                    :label="`${partsType[type].substring(0, 2)}名称`"
                    align="center"
                    prop="assetName"
                    min-width="140"
                    show-overflow-tooltip
                />
                <el-table-column
                    label="养护路段"
                    align="center"
                    prop="maintenanceSectionName"
                    min-width="140"
                    show-overflow-tooltip
                />
                <el-table-column
                    label="路线编码"
                    align="center"
                    prop="routeCode"
                    min-width="120"
                    show-overflow-tooltip
                />
                <el-table-column
                    label="桩号"
                    align="center"
                    prop="centerStakeFrom"
                    min-width="120"
                    show-overflow-tooltip
                />
                <el-table-column label="本月应检查次数" align="center" prop="monthRequiredCount" min-width="120" />
                <el-table-column label="本月已检查次数" align="center" prop="monthCheckedCount" min-width="120" />
                <el-table-column label="本季度应检查次数" align="center" prop="quarterRequiredCount" min-width="120" />
                <el-table-column label="本季度已检查次数" align="center" prop="quarterCheckedCount" min-width="120" />
                <el-table-column label="本年应检查次数" align="center" prop="yearRequiredCount" min-width="120" />
                <el-table-column label="本年已检查次数" align="center" prop="yearCheckedCount" min-width="120" />
            </el-table>

            <pagination
                v-show="total > 0"
                :total="total"
                :page.sync="queryParams.pageNum"
                :limit.sync="queryParams.pageSize"
                @pagination="getList"
            />
        </div>
        <!--数据表格结束-->
    </div>
</template>

<script>
    import moment from 'moment';
    import { getCheckTimesPage, getTotalCount } from '@/api/patrol/assetCheck';
    import CascadeSelection from '@/components/CascadeSelection/index.vue';

    export default {
        name: 'AssetTimes',
        dicts: ['patrol_inspection_status'],
        components: {
            CascadeSelection,
        },
        props: {
            type: {
                type: Number,
                default: 2,
            },
        },
        data() {
            return {
                // 遮罩层
                loading: true,
                // 选中数组
                ids: [],
                // 总条数
                total: 0,
                // 统计数据列表
                statisticsList: [],
                partsType: {
                    2: '桥梁经常检查',
                    4: '涵洞经常检查',
                    6: '隧道经常检查',
                },
                // 查询参数
                queryParams: {
                    pageNum: 1,
                    pageSize: 50,
                    type: this.type,
                    status: null,
                    dataRule: true,
                    assetName: null,
                    assetCode: null,
                    checkTime: moment().format('YYYY-MM-DD HH:mm:ss'),
                    maintenanceSectionId: null,
                    managementMaintenanceIds: null,
                    routeCodes: null,
                },
            };
        },
        created() {
            this.getList();
        },
        methods: {
            /** 查询统计列表 */
            getList() {
                this.loading = true;
                const queryParams = { ...this.queryParams };
                getCheckTimesPage(queryParams)
                    .then((response) => {
                        this.statisticsList = response.rows;
                        this.total = response.total;
                    })
                    .finally(() => {
                        // 无论成功还是失败都关闭loading
                        this.loading = false;
                    });
            },
            /** 搜索按钮操作 */
            handleQuery() {
                this.queryParams.pageNum = 1;
                this.getList();
            },
            /** 重置按钮操作 */
            resetQuery() {
                this.resetForm('queryForm');
                // 手动重置查询参数到初始状态
                this.queryParams = {
                    pageNum: 1,
                    pageSize: 50,
                    type: this.type,
                    status: null,
                    dataRule: true,
                    assetName: null,
                    assetCode: null,
                    checkTime: moment().format('YYYY-MM-DD HH:mm:ss'),
                    maintenanceSectionId: null,
                    managementMaintenanceIds: null,
                    routeCodes: null,
                };
                this.$nextTick(() => {
                    this.handleQuery();
                });
            },
            // 多选框选中数据
            handleSelectionChange(selection) {
                this.ids = selection.map((item) => item.id);
                this.single = selection.length != 1;
                this.multiple = !selection.length;
            },
            // 表格点击勾选
            handleRowClick(row) {
                this.$refs.table.toggleRowSelection(row);
            },
            // 勾选高亮
            rowStyle({ row }) {
                const isSelected = this.$refs.table.selection.some((selected) => selected.id === row.id);
                return isSelected
                    ? { 'background-color': '#b7daff', color: '#333' }
                    : { 'background-color': '#fff', color: '#333' };
            },
            /** 导出按钮操作 */
            handleExport() {
                let queryParams = {};
                if (this.ids.length === 0) {
                    queryParams = {
                        ...this.queryParams,
                        type: this.type,
                        dataRule: true,
                    };
                } else {
                    queryParams = {
                        ids: this.ids,
                        type: this.type,
                        checkTime: this.queryParams.checkTime,
                    };
                }

                // 先获取数据总数
                getTotalCount(queryParams).then((res) => {
                    const export_count = res.data;

                    const confirmMessage =
                        this.ids.length === 0
                            ? `根据搜索条件，本次导出共有 ${export_count} 条数据，是否确认导出？`
                            : `根据选中条件，本次导出共有 ${export_count} 条数据，是否确认导出？`;

                    this.$confirm(confirmMessage, '提示', {
                        confirmButtonText: '确定',
                        cancelButtonText: '取消',
                        type: 'warning',
                    })
                        .then(() => {
                            const now = new Date(); // 创建日期对象
                            const timeStr = `${now.getFullYear()}年${
                                now.getMonth() + 1
                            }月${now.getDate()}日${now.getHours()}时${now.getMinutes()}分${now.getSeconds()}秒`;
                            const fileName = `${this.partsType[this.type]}明细_${timeStr}.xlsx`;
                            this.download(
                                'patrol/assetCheck/exportCheckTimes',
                                {
                                    ...queryParams,
                                },
                                fileName,
                                {
                                    parameterType: 'body', // 设置参数类型为 body
                                    headers: {
                                        'Content-Type': 'application/json', // 设置请求头为 JSON
                                    },
                                }
                            );
                        })
                        .catch(() => {
                            this.$message({
                                type: 'info',
                                message: '已取消导出',
                            });
                        });
                });
            },
        },
    };
</script>

<style scoped>
    .tableDiv {
        background-color: white;
        padding-bottom: 10px;
    }
</style>
