<template>
  <div>
    <el-dialog
      :title="title"
      :visible.sync="showAddEdit"
      width="60%"
      append-to-body
      :before-close="onClose"
      :close-on-click-modal="false"
      :class="forView ? 'forView' : ''"
    >
      <div
        v-loading="loading"
        class="road-interflow-edit"
        style="height: 65vh; overflow-y: auto; padding: 0 10px 0 5px"
      >
        <el-form
          ref="ruleFormEl"
          :model="ruleForm"
          label-width="auto"
          :disabled="forView"
        >
          <MepuColumn
            :is-manger-unit="true"
            :fields="baseInfoFields"
            :from="ruleForm"
            :forView="forView"
          />
        </el-form>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button
          v-if="!forView"
          :loading="loading"
          type="primary"
          @click="handleSubmit('submit')"
          >提 交
        </el-button>
        <el-button
           v-if="!forView &&  (!formData.baseInfoForm || formData.baseInfoForm.status == 1)"
          v-hasPermi="['baseData:roadbed:tempAdd']"
           :loading="loading"
          type="primary"
          @click="handleSubmit('save')"
          >暂 存
        </el-button>
        <el-button @click="onClose">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { createIdWorker } from "@/api/baseData/common";
import {
  subgradeAdd,
  addStatic,
  subgradeEdit,
  subgradeTempAdd,
} from "@/api/baseData/subgrade/baseInfo/index";
import PileInput from "@/components/PileInput/index.vue";
import SelectTree from "@/components/DeptTmpl/selectTree";
import RouteRoad from "@/components/RouteRoad";
import RouteRoadSub from "@/components/RouteRoadSub";
import Dialog from "@/components/Dialog/index.vue";
import "@riophae/vue-treeselect/dist/vue-treeselect.css";
import Treeselect from "@riophae/vue-treeselect";
import { deptTreeSelect } from "@/api/tmpl";
import RoadSection from "@/views/baseData/components/roadSection";
import RouteCoding from "@/views/baseData/components/routeCoding";
import ManageSelectTree from "@/components/manageSelectTree/index.vue";
import formFields from "../dataDictionary.js";
import MepuColumn from "@/views/baseData/components/MepuColumn/index.vue";
import lonLat from "@/components/mapPosition/lonLat.vue";

export default {
  name: "RoadInterflowEdit",
  components: {
    lonLat,
    MepuColumn,
    ManageSelectTree,
    PileInput,
    SelectTree,
    RouteRoad,
    RouteRoadSub,
    Dialog,
    Treeselect,
    RoadSection,
    RouteCoding,
  },
  dicts: ["left_right", "lane", "patrol_inspection_direction"],
  props: {
    formData: {
      type: undefined,
      default: () => {},
    },
    showAddEdit: {
      type: Boolean,
      default: false,
    },
    title: {
      type: String,
      default: "添加互通",
    },
    forView: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      loading: false,

      ruleForm: {
        flowRampList: [],
        managementMaintenanceId: "",
        maintenanceSectionId: "",
        managementMaintenanceBranchId: "",
      },

      showRamp: false, // 选择匝道
      formH: 500, // form最大高度
      deptOptions: [], // 部门树数据
      ownerOptions: [], //
      types: 201, // 编码规划
      branchDeptOptions: [], // 管养分出数据
      baseInfoFields: [],
    };
  },
  computed: {},
  watch: {},
  created() {
    this.init();
    // this.getDeptTree()
  },
  mounted() {
    this.formH = window.innerHeight - 240;
    window.addEventListener("resize", (e) => {
      let evt = e.target || window;
      this.formH = evt.innerHeight - 240;
    });
  },
  methods: {
    init() {
      this.baseInfoFields = JSON.parse(JSON.stringify(formFields.baseInfoData));
      let data = JSON.parse(JSON.stringify(this.formData));
      if (!data.flowRampList) {
        data.flowRampList = [];
      }

      if (data.baseInfoForm && data.baseInfoForm.managementMaintenanceId) {
        this.ruleForm = data.baseInfoForm;
      }
    },
    onSelectRamp() {
      this.showRamp = true;
    },
    async handleSubmit(type) {
      switch (type) {
        case "submit":
          this.ruleForm.status = 2;
          break;
        case "save":
          this.ruleForm.status = 1;
          break;
      }
      if (this.ruleForm.status == 2) {
        const valid = await new Promise((resolve) => {
          this.$refs["ruleFormEl"].validate((valid) => {
            resolve(valid);
          });
        });
        if (!valid) {
          return; // 中止提交操作
        }
      }

      if (Array.isArray(this.ruleForm.samplePictureId)) {
        this.ruleForm.samplePictureId = this.ruleForm.samplePictureId.join(",");
      }

      if (Array.isArray(this.ruleForm.routeLevel)) {
        this.ruleForm.routeLevel = this.ruleForm.routeLevel.join(",");
      }
      // 提交
      if (this.ruleForm.id != null) {
        const api = this.ruleForm.status === 1 ? subgradeTempAdd : subgradeEdit;

        api(this.ruleForm)
          .then((response) => {
            this.$modal.msgSuccess("修改成功");
            this.$emit("refresh", true);
          })
          .catch(() => {
            this.loading = false;
          });
      } else {
        const api = this.ruleForm.status === 1 ? subgradeTempAdd : subgradeAdd;
        api(this.ruleForm)
          .then((response) => {
            this.$modal.msgSuccess(
              this.ruleForm.status === 1 ? "暂存成功" : "新增成功"
            );
            this.$emit("refresh", true);
          })
          .catch(() => {
            this.loading = false;
          });
      }
    },
    onClose() {
      if (this.forView) {
        this.form = {};
        this.$emit("close", false);
      } else {
        this.$modal
          .confirm("确认退出？")
          .then(() => {
            this.form = {};
            this.$emit("close", false);
          })
          .catch(() => {});
      }
    },
    /** 查询部门下拉树结构 */
    getDeptTree() {
      deptTreeSelect({ types: this.types }).then((response) => {
        this.deptOptions = response.data;
      });
      deptTreeSelect({ types: 100 }).then((response) => {
        this.ownerOptions = response.data;
      });
      deptTreeSelect({ types: 202 }).then((response) => {
        this.branchDeptOptions = response.data;
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.road-interflow-edit {
  /* max-height: 75vh;
    padding: 10px;*/
  overflow-y: auto;
  overflow-x: hidden;
}

.forView ::v-deep .el-input.is-disabled .el-input__inner {
  background-color: white;
  border-color: #dfe4ed;
  color: black;
}
</style>
