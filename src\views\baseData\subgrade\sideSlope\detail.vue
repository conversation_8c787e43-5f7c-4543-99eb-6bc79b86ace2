<template>
  <div v-loading="loading" :class="oneMap ? 'one-map' : ''">
    <el-tabs v-model="activeName" type="card">
      <el-tab-pane label="边坡基础数据" name="basicData">
        <div style="height: calc(-170px + 100vh); overflow-y: auto; padding: 0 10px 0 5px">
          <el-form
            ref="form"
            :model="form"
            :rules="rules"
            label-width="150px"
            :disabled="true"
          >
            <div style="display: flex; flex-wrap: wrap; margin-bottom: 20px">
              <el-divider content-position="left">基础数据</el-divider>
              <ManageSelectTree placeholder="请选择" :formObject="form" />

              <el-col :span="12">
                <el-form-item label="养护路段" prop="maintenanceSectionId">
                  <el-select
                    v-model="form.maintenanceSectionId"
                    style="width: 100%"
                    placeholder="请选择"
                    clearable
                    :disabled="!form.managementMaintenanceBranchId"
                  >
                    <el-option
                      v-for="item in routeOptions"
                      :key="item.maintenanceSectionId"
                      :label="item.maintenanceSectionName"
                      :value="item.maintenanceSectionId"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="路段类型" prop="sectionType">
                  <SectionSelect
                    v-model="form.sectionType"
                    :style="
                      'width: 100%;' + (forView ? 'pointer-events: none' : '')
                    "
                    :formObject="form"
                    :sectionId="form.maintenanceSectionId"
                    :disabled="!form.maintenanceSectionId"
                    clearable
                  />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="路线编码" prop="routeCode">
                  <el-select
                    v-model="form.routeCode"
                    placeholder="请选择"
                    :disabled="!form.maintenanceSectionId"
                    clearable
                    style="width: 100%"
                    @change="
                      (val) => {
                        handleSelect(val);
                      }
                    "
                  >
                    <el-option
                      v-for="item in routeList"
                      :key="item.routeId"
                      :label="item.routeCode"
                      :value="item.routeCode"
                    >
                    </el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="路线名称" prop="routeName">
                  <el-select
                    v-model="form.routeName"
                    style="width: 100%"
                    placeholder="请选择"
                    :disabled="true"
                    clearable
                  >
                    <el-option
                      v-for="item in []"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="路线技术等级" prop="routeLevel">
                  <MultiDictSelect
                    v-model="form.routeLevel"
                    :disabled="''"
                    :multiple="true"
                    :options="dict.type['sys_route_grade']"
                  />
                </el-form-item>
              </el-col>

              <el-col :span="12">
                <el-form-item label="边坡名称" prop="slopeName">
                  <el-input
                    v-model="form.slopeName"
                    placeholder="请输入边坡名称"
                    clearable
                  />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="边坡编码" prop="slopeCode">
                  <el-input
                    v-model="form.slopeCode"
                    placeholder="请输入边坡编码"
                    clearable
                  />
                </el-form-item>
              </el-col>

              <el-col :span="12">
                <el-form-item label="车道数" prop="laneNumber">
                  <el-input-number
                    v-model="form.laneNumber"
                    style="width: 100%"
                    clearable
                  />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="面层类型" prop="pavementType">
                  <el-select
                    v-model="form.pavementType"
                    style="width: 100%"
                    placeholder="请选择"
                    clearable
                  >
                    <el-option
                      v-for="item in dict.type.sys_surface_type"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="方向" prop="direction">
                  <el-select
                    v-model="form.direction"
                    style="width: 100%"
                    placeholder="请选择"
                    clearable
                  >
                    <el-option
                      v-for="i in dict.type.sys_route_direction"
                      :key="i.value"
                      :label="i.label"
                      :value="i.value"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="左右" prop="leftOrRight">
                  <el-select
                    v-model="form.leftOrRight"
                    style="width: 100%"
                    placeholder="请选择"
                    clearable
                  >
                    <el-option
                      v-for="i in dict.type.left_right"
                      :key="i.value"
                      :label="i.label"
                      :value="i.value"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="位置" prop="lane">
                  <el-select
                    v-model="form.lane"
                    style="width: 100%"
                    placeholder="请选择"
                    clearable
                  >
                    <el-option
                      v-for="i in dict.type.lane"
                      :key="i.value"
                      :label="i.label"
                      :value="i.value"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="起点桩号" prop="startStake">
                  <PileInput v-model="form.startStake" placeholder="请输入" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="终点桩号" prop="endStake">
                  <PileInput v-model="form.endStake" placeholder="请输入" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="经纬度" prop="longitude">
                  <lon-lat
                    type="lonlat"
                    :lon.sync="form.longitude"
                    :lat.sync="form.latitude"
                  />
                </el-form-item>
              </el-col>

              <el-col :span="12">
                <el-form-item label="建成时间" prop="buildDate">
                  <el-date-picker
                    v-model="form.buildDate"
                    style="width: 100%"
                    type="date"
                    placeholder="请选择"
                    :picker-options="pickerOptions"
                    clearable
                    value-format="yyyy-MM-dd"
                  />
                </el-form-item>
              </el-col>

              <el-col :span="12">
                <el-form-item label="路基类型" prop="roadbedType">
                  <el-input
                    v-model="form.roadbedType"
                    placeholder="请输入路基类型"
                    clearable
                  />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="平台数量" prop="platformNumber">
                  <el-input-number
                    v-model="form.platformNumber"
                    style="width: 100%"
                    clearable
                  />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="检查梯" prop="checkLadder">
                  <el-input
                    v-model="form.checkLadder"
                    placeholder="请输入检查梯"
                    clearable
                  />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="土质" prop="soil">
                  <el-input
                    v-model="form.soil"
                    placeholder="请输入土质"
                    clearable
                  />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="坡脚长度(m)" prop="slopingFootLength">
                  <el-input-number
                    v-model="form.slopingFootLength"
                    style="width: 100%"
                    :precision="2"
                    clearable
                  />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="落台宽度(m)" prop="dropWidth">
                  <el-input-number
                    v-model="form.dropWidth"
                    style="width: 100%"
                    :precision="2"
                    clearable
                  />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="相对位置" prop="relativePosition">
                  <el-select
                    v-model="form.relativePosition"
                    style="width: 100%"
                    placeholder="请选择"
                    clearable
                  >
                    <el-option
                      v-for="item in dict.type.side_slope_relative_position"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="长度(m)" prop="length">
                  <el-input-number
                    v-model="form.length"
                    style="width: 100%"
                    :precision="2"
                    clearable
                  />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="路基宽度(m)" prop="roadbedWidth">
                  <el-input-number
                    v-model="form.roadbedWidth"
                    style="width: 100%"
                    :precision="2"
                    clearable
                  />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="路面宽度(m)" prop="pavementWidth">
                  <el-input-number
                    v-model="form.pavementWidth"
                    style="width: 100%"
                    :precision="2"
                    clearable
                  />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="总高度(m)" prop="totleHeight">
                  <el-input-number
                    v-model="form.totalHeight"
                    style="width: 100%"
                    :precision="3"
                    clearable
                  />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="边坡总台数" prop="slopePlatformNumber">
                  <el-input-number
                    v-model="form.slopePlatformNumber"
                    style="width: 100%"
                    clearable
                  />
                </el-form-item>
              </el-col>

              <el-col :span="12">
                <el-form-item label="建造年度" prop="constructionDate">
                  <el-date-picker
                    v-model="form.constructionDate"
                    style="width: 100%"
                    type="date"
                    placeholder="请选择"
                    clearable
                    value-format="yyyy-MM-dd"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="改建年度" prop="renovationDate">
                  <el-date-picker
                    v-model="form.renovationDate"
                    style="width: 100%"
                    type="date"
                    placeholder="请选择"
                    clearable
                    value-format="yyyy-MM-dd"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="抗震设防等级" prop="seismicGradeType">
                  <el-select
                    v-model="form.seismicGradeType"
                    style="width: 100%"
                    placeholder="请选择"
                    clearable
                  >
                    <el-option
                      v-for="item in dict.type.bridge_seismic_grade"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="防洪标准" prop="floodControlType">
                  <el-select
                    v-model="form.floodControlType"
                    style="width: 100%"
                    placeholder="请选择"
                    clearable
                  >
                    <el-option
                      v-for="item in dict.type.side_slop_flood_control"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item
                  label="移交管理单位"
                  prop="transferManagementUnit"
                >
                  <el-input
                    v-model="form.transferManagementUnit"
                    style="width: 100%"
                    clearable
                  />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item
                  label="是否有健康监测系统"
                  prop="whetherHealthMonitorSystemType"
                >
                  <el-select
                    v-model="form.whetherHealthMonitorSystemType"
                    style="width: 100%"
                    placeholder="请选择"
                    clearable
                  >
                    <el-option
                      v-for="item in dict.type.base_data_yes_no"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="备注" prop="remark">
                  <el-input
                    type="textarea"
                    autosize
                    v-model="form.remark"
                    placeholder="请输入备注"
                  />
                </el-form-item>
              </el-col>
              <el-divider content-position="left">边坡类型</el-divider>
              <el-col :span="24"  class="onemap-radio-col">
                <el-form-item label-width="40px">
                  <el-radio
                    v-for="item in dict.type.side_slope_type"
                    v-model="form.slopeType"
                    :label="item.value"
                    :key="item.value"
                    >{{ item.label }}</el-radio
                  >
                </el-form-item>
              </el-col>
              <div  class="onemap-radio-col" v-if="form.slopeType == '1'">
                <el-col :span="12">
                  <el-form-item
                    label="路堤坡高(m)"
                    prop="embankmentSlopeHeight"
                  >
                    <el-input-number
                      v-model="form.embankmentSlopeHeight"
                      style="width: 100%"
                      :precision="2"
                      clearable
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="路堤坡度(°)" prop="embankmentSlope">
                    <el-input-number
                      v-model="form.embankmentSlope"
                      style="width: 100%"
                      :precision="2"
                      clearable
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item
                    label="路堤分级(级)"
                    prop="embankmentClassification"
                  >
                    <el-input-number
                      v-model="form.embankmentClassification"
                      style="width: 100%"
                      :precision="2"
                      clearable
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="是否临河" prop="nearRiverType">
                    <el-select
                      v-model="form.nearRiverType"
                      style="width: 100%"
                      placeholder="请选择"
                      clearable
                    >
                      <el-option
                        v-for="item in dict.type.base_data_yes_no"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value"
                      />
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="临河地形" prop="riverTerrainType">
                    <el-select
                      v-model="form.riverTerrainType"
                      style="width: 100%"
                      placeholder="请选择"
                      clearable
                    >
                      <el-option
                        v-for="item in dict.type.side_slope_river_terrain"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value"
                      />
                    </el-select>
                  </el-form-item>
                </el-col>
              </div>
              <div  class="onemap-radio-col" v-else-if="form.slopeType == '2'">
                <el-col :span="12">
                  <el-form-item label="路堑坡高(m)" prop="cuttingSlopeHeight">
                    <el-input-number
                      v-model="form.cuttingSlopeHeight"
                      style="width: 100%"
                      :precision="2"
                      clearable
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="路堑坡度(°)" prop="cuttingSlope">
                    <el-input-number
                      v-model="form.cuttingSlope"
                      style="width: 100%"
                      :precision="2"
                      clearable
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item
                    label="路堑分级(级)"
                    prop="cuttingClassification"
                  >
                    <el-input-number
                      v-model="form.cuttingClassification"
                      style="width: 100%"
                      :precision="2"
                      clearable
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="岩性" prop="lithologyType">
                    <el-select
                      v-model="form.lithologyType"
                      style="width: 100%"
                      placeholder="请选择"
                      clearable
                    >
                      <el-option
                        v-for="item in dict.type.side_slope_lithology"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value"
                      />
                    </el-select>
                  </el-form-item>
                </el-col>
              </div>
              <div  class="onemap-radio-col" v-else>
                <el-form-item label-width="40px">请选择边坡类型</el-form-item>
              </div>
              <el-divider content-position="left">排水设施类型</el-divider>
              <el-col :span="12">
                <el-form-item
                  label="地表排水设施"
                  prop="surfaceDrainageFacilitiesType"
                >
                  <el-select
                    v-model="form.surfaceDrainageFacilitiesType"
                    style="width: 100%"
                    placeholder="请选择"
                    clearable
                  >
                    <el-option
                      v-for="item in dict.type
                        .side_slope_surface_drainage_facilities"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item
                  label="地下排水设施"
                  prop="undergroundDrainageFacilitiesType"
                >
                  <el-select
                    v-model="form.undergroundDrainageFacilitiesType"
                    style="width: 100%"
                    placeholder="请选择"
                    clearable
                  >
                    <el-option
                      v-for="item in dict.type
                        .side_slope_underground_drainage_facilities"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-divider content-position="left">防护设施类型</el-divider>
              <el-col :span="12">
                <el-form-item label="坡面防护" prop="slopeProtectionType">
                  <el-select
                    v-model="form.slopeProtectionType"
                    style="width: 100%"
                    placeholder="请选择"
                    clearable
                  >
                    <el-option
                      v-for="item in dict.type.side_slope_protection"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="沿河防护" prop="riverProtectionType">
                  <el-select
                    v-model="form.riverProtectionType"
                    style="width: 100%"
                    placeholder="请选择"
                    clearable
                  >
                    <el-option
                      v-for="item in dict.type.side_slope_river_protection"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="支挡设施" prop="supportFacilitiesType">
                  <el-select
                    v-model="form.supportFacilitiesType"
                    style="width: 100%"
                    placeholder="请选择"
                    clearable
                  >
                    <el-option
                      v-for="item in dict.type.side_slope_support_facilities"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-divider content-position="left">统一里程</el-divider>
              <el-col :span="12">
                <el-form-item label="起点桩号" prop="unifiedMileageStartStake">
                  <PileInput v-model="form.unifiedMileageStartStake" />
                </el-form-item>
              </el-col>

              <el-col :span="12">
                <el-form-item label="终点桩号" prop="unifiedMileageEndStake">
                  <PileInput v-model="form.unifiedMileageEndStake" />
                </el-form-item>
              </el-col>

              <el-col :span="12">
                <el-form-item label="统一里程桩号" prop="unifiedMileageStake">
                  <PileInput v-model="form.unifiedMileageStake" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="管养里程(km)" prop="maintenanceMileage">
                  <el-input-number
                    v-model="form.maintenanceMileage"
                    style="width: 100%"
                    :precision="3"
                    clearable
                  />
                </el-form-item>
              </el-col>
              <el-divider content-position="left">国高网里程</el-divider>
              <el-col :span="12">
                <el-form-item label="起点桩号" prop="nationalNetworkStartStake">
                  <PileInput v-model="form.nationalNetworkStartStake" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="终点桩号" prop="nationalNetworkEndStake">
                  <PileInput v-model="form.nationalNetworkEndStake" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item
                  label="国高网里程(km)"
                  prop="nationalNetworkMileage"
                >
                  <el-input-number
                    v-model="form.nationalNetworkMileage"
                    style="width: 100%"
                    :precision="3"
                    clearable
                  />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="国高网桩号" prop="nationalNetworkStake">
                  <PileInput v-model="form.nationalNetworkStake" />
                </el-form-item>
              </el-col>
              <el-divider content-position="left">施工里程</el-divider>
              <el-col :span="12">
                <el-form-item label="起点桩号" prop="constructionStartStake">
                  <PileInput v-model="form.constructionStartStake" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="终点桩号" prop="constructionEndStake">
                  <PileInput v-model="form.constructionEndStake" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="施工里程(km)" prop="constructionMileage">
                  <el-input-number
                    v-model="form.constructionMileage"
                    style="width: 100%"
                    :precision="3"
                    clearable
                  />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="施工桩号" prop="constructionStake">
                  <PileInput v-model="form.constructionStake" />
                </el-form-item>
              </el-col>
              <el-divider content-position="left">防护形式</el-divider>
              <el-col :span="24">
                <el-form-item label="防护形式总数">
                  <el-input-number
                    ref="inputNumber"
                    class="inputNumber"
                    v-model="form.protectionFormNumber"
                    style="width: 200px"
                    clearable
                    :min="0"
                    :max="50"
                    :step="1"
                    @change="changeNumber"
                    @keydown.native="onKeydown"
                  />
                </el-form-item>
              </el-col>
              <div
                class="slope-protection-card"
                v-for="(item, index) in form.slopeProtectionRecords"
                :key="index"
              >
                <i
                  v-if="!forView"
                  class="el-icon-remove"
                  @click="removeCard(item, index)"
                ></i>
                <el-col :span="12">
                  <el-form-item label="台高(m)" prop="platformHeight">
                    <el-input-number
                      v-model="item.platformHeight"
                      style="width: 100%"
                      :precision="3"
                      clearable
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="防护形式" prop="protectionForm">
                    <el-input
                      v-model="item.protectionForm"
                      placeholder="请输入防护形式"
                      clearable
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="坡比" prop="slopeRatio">
                    <el-input-number
                      v-model="item.slopeRatio"
                      style="width: 100%"
                      clearable
                    />
                  </el-form-item>
                </el-col>
              </div>
            </div>
          </el-form>
        </div>
      </el-tab-pane>
      <el-tab-pane label="边坡图片" name="picture">
        <el-col :span="12">
          <div style="height: 60vh; overflow-y: auto; padding: 0 10px 0 5px">
            <el-form
              ref="form"
              :model="form"
              :rules="rules"
              label-width="50px"
              :disabled="forView ? true : false"
            >
              <el-form-item label="图片" prop="samplePictureId">
                <span v-if="forView && !form.samplePictureId">无</span>
                <span v-else>
                  <ImageUpload
                    v-if="!forView"
                    :key="ownerId"
                    v-model="form.samplePictureId"
                    :limit="1"
                    :owner-id="ownerId"
                    storage-path="/base/subgrade/sideSlope"
                  />
                  <ImagePreview
                    v-else
                    :owner-id="form.samplePictureId"
                    width="146px"
                    height="146px"
                  />
                </span>
              </el-form-item>
            </el-form>
          </div>
        </el-col>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>
  
  <script>
import PileInput from "@/components/PileInput/index.vue";
import SelectTree from "@/components/DeptTmpl/selectTree";
import SectionSelect from "@/components/SectionSelect";
import ManageSelectTree from "@/components/manageSelectTree/index.vue";
import lonLat from "@/components/mapPosition/lonLat.vue";
import MultiDictSelect from "@/views/baseData/components/MultiDictSelect/index.vue";
import { listMaintenanceSectionAll } from "@/api/system/maintenanceSection";

import {
    assetInfo
} from "@/api/baseData/subgrade/sideSlope/index.js";

export default {
  name: "sideSlope-addAndEdit",
  components: {
    PileInput,
    SelectTree,
    SectionSelect,
    ManageSelectTree,
    lonLat,
    MultiDictSelect,
  },
  inject: ["oneMap"],
  props: {
    formData: {
      default: {},
    },
    id: {
      type: [String, Number],
      default: "",
    },
    forView:{
        default: true,
    }
  },
  dicts: [
    "sys_route_grade",
    "side_slope_relative_position",
    "bridge_seismic_grade",
    "side_slop_flood_control",
    "base_data_yes_no",
    "side_slope_river_terrain",
    "side_slope_lithology",
    "side_slope_surface_drainage_facilities",
    "side_slope_underground_drainage_facilities",
    "side_slope_protection",
    "side_slope_river_protection",
    "side_slope_support_facilities",
    "sys_surface_type",
    "bridge_business_state",
    "side_slope_type",
    "sys_route_direction",
    "lane",
    "left_right",
  ],
  data() {
    return {
      loading: false,
      form: {
        slopeProtectionRecords: [],
        managementMaintenanceId: "",
        managementMaintenanceBranchId: "",
      },
      pickerOptions: {
        disabledDate(v) {
          return v.getTime() > new Date().getTime();
        },
      },
      rules: {
        managementMaintenanceId: [
          { required: true, message: "请选择管理处", trigger: "change" },
        ],
        managementMaintenanceBranchId: [
          { required: true, message: "请选择管养分处", trigger: "change" },
        ],
        maintenanceSectionId: [
          { required: true, message: "请选择养护路段", trigger: "change" },
        ],
        slopeCode: [
          { required: true, message: "请选择边坡编码", trigger: "change" },
        ],
        routeName: [
          { required: true, message: "请选择路线名称", trigger: "change" },
        ],
        routeCode: [
          { required: true, message: "请选择路线编码", trigger: "change" },
        ],
        startStake: [
          { required: true, message: "请输入起点桩号", trigger: "blur" },
        ],
        endStake: [
          { required: true, message: "请输入终点桩号", trigger: "blur" },
        ],
        relativePosition: [
          { required: true, message: "请输入相对位置", trigger: "blur" },
        ],
      },
      routeOptions: [],
      routeList: [],
      ownerId: "",
      activeName: "basicData",
    };
  },
  created() {
    this.init();
  },
  mounted() {},
  methods: {
    handleSelect(e) {
      const option = this.routeList.find((i) => i.routeCode === e);

      if (option) {
        this.form.routeId = option.routeId;
        this.form.routeName = option.routeName;
      }
    },
    init() {
        assetInfo(this.id).then((res) => {
          if (res.code === 200 && res.data) {
            this.form = res.data;
            listMaintenanceSectionAll({ departmentId: this.form.managementMaintenanceBranchId }).then((res) => {
              if (res.code == 200) {
                this.routeOptions = res.data;
              }
            });
          }
        });
    },
    onKeydown(event) {
      if (event.key !== "ArrowUp" && event.key !== "ArrowDown") {
        event.preventDefault();
      }
    },
    removeCard(item, index) {
      if (!item.platformHeight && !item.protectionForm && !item.slopeRatio) {
        this.form.slopeProtectionRecords.splice(index, 1);
        this.form.protectionFormNumber =
          this.form.slopeProtectionRecords.length;
      } else {
        this.$modal
          .confirm("确认删除改防护形式记录？")
          .then(() => {
            this.form.slopeProtectionRecords.splice(index, 1);
            this.form.protectionFormNumber =
              this.form.slopeProtectionRecords.length;
          })
          .catch(() => {});
      }
    },
    changeNumber(newVal, oldVal) {
      if (oldVal === null || oldVal === undefined) oldVal = 0;
      if (newVal > oldVal) {
        // 当数量增加时，在数组末尾添加新元素
        const lastIndex = this.form.slopeProtectionRecords.length - 1;
        this.form.slopeProtectionRecords.push({
          // platformHeight: "",
          // protectionForm: "",
          // slopeRatio: "",
        });
      } else if (newVal < oldVal) {
        // 当数量减少时，优先删除没有数据的元素，其次再从末尾删除
        const originalNumber = oldVal;
        let deleteIndex = -1;
        for (let i = this.form.slopeProtectionRecords.length - 1; i >= 0; i--) {
          const record = this.form.slopeProtectionRecords[i];
          if (
            !record.platformHeight &&
            !record.protectionForm &&
            !record.slopeRatio
          ) {
            deleteIndex = i;
            break;
          }
        }
        if (deleteIndex !== -1) {
          this.form.slopeProtectionRecords.splice(deleteIndex, 1);
        } else {
          this.$modal
            .confirm("该操作将删除已添加的防护形式记录，是否继续？")
            .then(() => {
              this.form.slopeProtectionRecords.pop();
            })
            .catch(() => {
              this.form.protectionFormNumber = originalNumber;
            });
        }
      }
    },
  },
};
</script>

<style lang="scss" scoped>
@import "@/assets/styles/common.scss";

::v-deep .el-textarea.is-disabled .el-textarea__inner {
  background-color: rgba(1, 102, 254, 0.2);
  border-color: #0166fe;
}

.el-divider {
    background-color: #0166fe;
  }

.el-divider__text {
    background: rgba(4, 17, 48, 0.8);
    background-image: initial;
    background-position-x: initial;
    background-position-y: initial;
    background-size: initial;
    background-repeat: initial;
    background-attachment: initial;
    background-origin: initial;
    background-clip: initial;
    color: #fff;
    border: 1.5px solid #0166fe;
    padding: 10px 20px;
  }

  .el-divider--horizontal {
    margin: 24px 0 !important;
  }
</style>
  
  