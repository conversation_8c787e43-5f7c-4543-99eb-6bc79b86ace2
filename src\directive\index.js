import hasRole from './permission/hasRole'
import hasPermi from './permission/hasPermi'
import dialogDrag from './dialog/drag'
import dialogDragWidth from './dialog/dragWidth'
import dialogDragHeight from './dialog/dragHeight'
import clipboard from './module/clipboard'
import hasMenuPermi from './permission/hasMenuPermi'
import adjustTable from './table/adjustTable'

const install = function(Vue) {
  Vue.directive('hasRole', hasRole)
  Vue.directive('hasPermi', hasPermi)
  Vue.directive('clipboard', clipboard)
  Vue.directive('dialogDrag', dialogDrag)
  Vue.directive('dialogDragWidth', dialogDragWidth)
  Vue.directive('dialogDragHeight', dialogDragHeight)
  Vue.directive('hasMenuPermi', hasMenuPermi)
  Vue.directive('adjustTable', adjustTable)

}

if (window.Vue) {
  window['hasRole'] = hasRole
  window['hasPermi'] = hasPermi
  window['hasMenuPermi'] = hasMenuPermi
  window['adjustTable'] = adjustTable
  Vue.use(install); // eslint-disable-line
}

export default install
