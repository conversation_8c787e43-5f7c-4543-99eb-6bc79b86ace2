<template>
  <el-dialog title="选择设备型号" :visible.sync="visible" width="1000px" top="5vh" append-to-body>
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true">
      <el-form-item label="" prop="manufacturerIdDisplay">
        <el-input
          v-model="queryParams.manufacturerIdDisplay"
          placeholder="请输入厂家"
          clearable
          :readonly="true"
          prefix-icon="el-icon-edit"
          style="width: 240px"
          @keyup.enter.native="handleQuery"
        >
          <el-button slot="append" icon="el-icon-search" @click="onSelectManufacturerId"></el-button>
        </el-input>
      </el-form-item>
      <el-form-item label="" prop="deviceTypeId">
        <el-select
          v-model="queryParams.deviceTypeId"
          placeholder="请输入设备类型"
          clearable
        >
          <el-option
            v-for="dict in deviceTypeList"
            :key="dict.id"
            :label="dict.name"
            :value="dict.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="" prop="name">
        <el-input
          v-model="queryParams.name"
          placeholder="请输入名称"
          clearable
          prefix-icon="el-icon-edit"
          style="width: 240px"
          maxlength="200" show-word-limit
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>
    <el-row>
      <el-table ref="table" :data="list" height="260px" stripe border highlight-current-row @current-change="handleCurrentChange" >
        <el-table-column fixed type="index" align="right" width="50">
        </el-table-column>
        <el-table-column label="名称" align="left" header-align="center" prop="name" width="250" :show-overflow-tooltip="true" />
        <el-table-column label="厂家" align="left" header-align="center" prop="manufacturerIdDisplay" width="250" :show-overflow-tooltip="true"/>
        <el-table-column label="设备类型" align="left" header-align="center" prop="deviceTypeIdDisplay" width="100" :show-overflow-tooltip="true"/>
        <el-table-column label="说明" align="left" header-align="center" prop="description" :show-overflow-tooltip="true"/>
        <el-table-column label="序号" align="right" header-align="center" prop="orders" width="50"/>
      </el-table>
      <pagination
        v-show="total>0"
        :total="total"
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
        @pagination="getList"
      />
    </el-row>
    <div slot="footer" class="dialog-footer">
      <el-button type="primary" @click="handleSelect">确 定</el-button>
      <el-button @click="visible = false">取 消</el-button>
    </div>
    <SelectManufacturer ref="selectManufacturerRef" @select="selectManufacturer"></SelectManufacturer>
  </el-dialog>
</template>
<script>
import { listDeviceModel} from "@/api/jgjc/jc/deviceModel";
import { findAllDeviceType } from "@/api/jgjc/jc/deviceType";
import SelectManufacturer from '@/views/jgjc/jc/manufacturer/selectManufacturer.vue'
export default {
  components: { SelectManufacturer },
  data() {
    return {
      // 遮罩层
      visible: false,
      selectData:null,
      // 总条数
      total: 0,
      list: [],
      deviceTypeList:[],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        manufacturerId: null,
        manufacturerIdDisplay: null,
        deviceTypeId: null,
        deviceTypeIdDisplay:null,
        name: null,
        orderDirection: 'ASC'
      },
      //提交参数
      params: {}
    };
  },
  methods: {
    // 显示弹框
    show() {
      this.getDeviceTypeList();
      this.getList();
      this.visible = true;
    },
    getDeviceTypeList() {
      this.loading = true
      findAllDeviceType().then(response => {
        this.deviceTypeList = response.data;
        this.loading = false;
      });
    },
    handleCurrentChange(row) {
      this.selectData = row
    },
    // 查询表数据
    getList() {
      listDeviceModel(this.queryParams).then(res => {
        this.list = res.data;
        this.total = res.total;
      });
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    onSelectManufacturerId(){
      this.$refs.selectManufacturerRef.show()
    },
    selectManufacturer(res){
      this.queryParams.manufacturerId = res.id
      this.queryParams.manufacturerIdDisplay=res.name
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    /** 提交选择隧道操作 */
    handleSelect() {
      if(this.selectData!=null){
        this.$emit('select',this.selectData)
        this.visible = false;
        return
      }
      this.$modal.msgWarning("请选择一条厂家记录");
    }
  }
};
</script>
