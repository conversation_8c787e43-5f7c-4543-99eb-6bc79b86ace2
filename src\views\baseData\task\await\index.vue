<template>
  <PageContainer>
    <!-- 查询 -->
    <template slot="search">
      <div style="display: flex; align-items: center; margin: 0">
        <CascadeSelectionManagementOffice style="min-width: 192px" :form-data="queryParams" v-model="queryParams" :showCodes="false"
          types="201" multiple />
        <div style=" margin: 0 20px;">
          <el-date-picker v-model="queryTime" @change="
            () => {
              queryParams.startTime = queryTime
                ? queryTime[0] + ' 00:00:00'
                : '';
              queryParams.endTime = queryTime
                ? queryTime[1] + ' 23:59:59'
                : '';
            }
          " type="daterange" clearable style="width: 100%" range-separator="至" start-placeholder="开始日期"
            end-placeholder="结束日期" format="yyyy-MM-dd" value-format="yyyy-MM-dd">
          </el-date-picker>
        </div>
        <el-col :span="4" style="display: flex; align-items: center">
          <el-button type="primary" icon="el-icon-search" size="mini" @click="searchList">搜索</el-button>
          <el-button icon="el-icon-refresh" size="mini" @click="queryReset">重置</el-button>
          <el-button v-show="!showSearch" icon="el-icon-arrow-down" circle @click="showSearch = true" />
          <el-button v-show="showSearch" icon="el-icon-arrow-up" style="
              color: #1890ff;
              border-color: #badeff;
              background-color: #e8f4ff;
            " circle @click="showSearch = false" />
        </el-col>
      </div>
      <el-row :gutter="12" v-if="showSearch" style="margin-top: 5px">
        <el-col :span="4">
          <el-input v-model="queryParams.title" clearable placeholder="请输入流程标题"></el-input>
        </el-col>
        <el-col :span="4">
          <el-select v-model="queryParams.category" placeholder="请选择流程分类" style="width: 100%" clearable>
            <el-option v-for="dict in dict.type.process_type" :key="dict.value" :label="dict.label"
              :value="dict.label" />
          </el-select>
        </el-col>
        <el-col :span="4">
          <div style="
              width: 100%;
              display: flex;
              flex-direction: row;
              flex-wrap: nowrap;
            ">
            <el-select v-model="queryParams.applyUserName" clearable placeholder="请选择创建人" style="width: 80%">
              <el-option v-for="item in userSelectData" :key="item.value" :label="item.label" :value="item.value">
              </el-option>
            </el-select>
            <el-button icon="el-icon-search" type="primary" @click="userDialogOpen" style="width: 20%"></el-button>
          </div>
        </el-col>
      </el-row>
    </template>

    <!-- 主表数据 -->
    <!-- 功能按钮 -->
    <template slot="header">
      <div class="btnBox">
        <el-row :gutter="10">
          <el-col :span="1.5">
            <el-button type="primary" icon="el-icon-plus" size="mini" style="margin-bottom: 10px;"
              @click="setMuAudit()">批量审核
            </el-button>
          </el-col>
        </el-row>
      </div>
    </template>

    <template slot="body">
      <!-- 数据表格 -->
      <el-table v-adjust-table height="100%" border ref="tableRef" style="border-radius: 10px" :header-cell-style="{ height: '36px' }"
        v-loading="tableLoading" :data="tableData" @selection-change="tableSelectionChange" @row-click="tableRowClick">
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="序号" align="center" width="60px" type="index" fixed>
          <template v-slot="scope">
            {{
              scope.$index +
              (queryParams.pageNum - 1) * queryParams.pageSize +
              1
            }}
          </template>
        </el-table-column>
        <el-table-column label="资产类型 " min-width="130" :show-overflow-tooltip="true" align="center" prop="type" fixed>
          <template slot-scope="{row}">
            <el-tag type="success" size="mini">{{ { 8: '桥梁', 194: '隧道', 10: '涵洞' }[row.type] }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="资产编码" min-width="150" :show-overflow-tooltip="true" align="center" prop="assetCode"  fixed/>
        <el-table-column label="资产名称" min-width="150" :show-overflow-tooltip="true" align="center" prop="assetName" />
        <el-table-column label="路线编码" min-width="80" :show-overflow-tooltip="true" align="center" prop="routeCode" />
        <el-table-column label="路线名称" min-width="150" :show-overflow-tooltip="true" align="center" prop="routeName" />
        <el-table-column label="养护路段" min-width="150" :show-overflow-tooltip="true" align="center" prop="maintenanceSectionName" />
        <el-table-column label="管理处" min-width="100" :show-overflow-tooltip="true" align="center" prop="managementMaintenanceName" />
        <el-table-column label="管养分处" min-width="100" :show-overflow-tooltip="true" align="center" prop="managementMaintenanceBranchName" />
        <el-table-column label="当前节点" min-width="130" :show-overflow-tooltip="true" align="center" prop="taskName" />
        <el-table-column label="创建日期" min-width="180" :show-overflow-tooltip="true" align="center" prop="createTime" />
        <el-table-column label="流程标题" min-width="150" :show-overflow-tooltip="true" align="center" prop="title" />
        <!--        <el-table-column label="流程类型" min-width="150"  :show-overflow-tooltip="true" align="center">-->
        <!--          <template slot-scope="scope">-->
        <!--            <dict-tag :options="dict.type.process_type" :value="scope.row.processDefinitionKey"/>-->
        <!--          </template>-->
        <!--        </el-table-column>-->
        <el-table-column label="流程实例ID" min-width="150" :show-overflow-tooltip="true" align="center" prop="processInstanceId" />
        <el-table-column label="操作" align="center" fixed="right">
          <template slot-scope="scope">
            <el-button type="text" @click.stop="setAudit(scope.row)">
              审批
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </template>
    <pagination :total="queryTotal" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize"
      :pageSizes="[10, 20, 30, 50, 100, 1000]" @pagination="queryList" style="margin-right: 10px" />

    <!-- 表单 -->
    <el-dialog :visible.sync="formDialog" width="70%" max-height="50%" :close-on-press-escape="false"
      :close-on-click-modal="false" append-to-body class="formDialog">
      <template slot="title">
        <div class="titleBox">
          <div class="title">
            {{ formType === "examine" ? "审批任务" : "查看任务" }}
          </div>
          <div class="subTitle" v-if="formParams.id">ID：</div>
        </div>
      </template>
    </el-dialog>

    <el-dialog :visible.sync="userDialog" title="创建人选择" width="40%" :close-on-press-escape="false"
      :close-on-click-modal="false" append-to-body class="userDialogBox">
      <div class="userBoxMain">
        <div class="searchBox">
          <el-row>
            <el-col :span="20">
              <el-input v-model="userParams.nickName" placeholder="请输入用户昵称" clearable
                @keyup.enter.native="userList"></el-input>
            </el-col>
            <el-col :span="4" style="display: flex; justify-content: flex-end">
              <el-button type="primary" icon="el-icon-search" size="mini" @click="userList">搜索</el-button>
            </el-col>
          </el-row>
        </div>
        <div class="dataBox">
          <el-table size="mini" height="calc(100% - 50px)" border ref="tableRef" v-loading="userTableLoading"
            :data="userTableData">
            <el-table-column label="用户名称" fixed :show-overflow-tooltip="true" align="center" prop="userName" />
            <el-table-column label="用户昵称" fixed :show-overflow-tooltip="true" align="center" prop="nickName" />
            <el-table-column label="用户编码" fixed :show-overflow-tooltip="true" align="center" prop="userId" />
            <el-table-column label="操作" align="center" class-name="small-padding fixed-width" fixed="right">
              <template slot-scope="scope">
                <el-button size="mini" type="text" @click="userSelect(scope.row)">选择
                </el-button>
              </template>
            </el-table-column>
          </el-table>

          <pagination :total="userTableTotal" :page.sync="userParams.pageNum" :pager-count="2"
            :limit.sync="userParams.pageSize" @pagination="userList" style="margin-right: 10px" />
        </div>
        <!-- <div class="selectBox">
          <el-tag
            v-if="userSelectData.userId"
            :key="userSelectData.userId"
            @close="userSelctCancel"
            closable
          >
            {{userSelectData.nickName}}
          </el-tag>
        </div> -->
      </div>
      <!-- <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="userSelectDown">保 存</el-button>
      </div> -->
    </el-dialog>
    <Form v-if="showForm" :showForm="showForm" :formData="tableSelection" @close="
      () => {
        showForm = false;
      }
    " @refresh="queryList"></Form>
  </PageContainer>
</template>

<script>
// -------------------- 引入 --------------------

// API
import { queryPageTodoTask, queryPageTodoTaskList } from "@/api/process/task/task"; // 流程任务模块
import { listUser } from "@/api/system/user"; // 用户管理模块
import Form from "./components/form.vue";
import CascadeSelection from "@/components/CascadeSelection/index.vue";
import CascadeSelectionManagementOffice from "@/components/CascadeSelectionManagementOffice/index.vue";

export default {
  name: "Await",
  // 数据字典
  dicts: ["process_type"],
  // 组件
  components: { Form, CascadeSelection ,CascadeSelectionManagementOffice},

  // -------------------- 变量 --------------------
  data() {
    return {
      /**
       * 查询相关
       */
      queryShow: false, // 查询更多参数显隐
      queryParams: {
        // 查询参数
        title: "", // 流程标题
        category: "基础数据审核", // 流程类型
        applyUserName: "", // 创建人
        startTime: "", // 开始时间
        endTime: "", // 结束时间
        pageNum: 1, // 页码
        pageSize: 10, // 每页条数
      },
      queryTime: "", // 时间范围
      queryTotal: 0, // 查询总数

      /**
       * 表格相关
       */
      tableData: [], // 表格数据
      tableSelection: [], // 表格选中数据
      tableLoading: false, // 表格加载

      /**
       * 表单相关
       */
      formDialog: false, // 表单弹窗
      formParams: {
        // 表单参数
        id: "", // 流程ID
      },
      formType: "", // 表单类型
      formProcessType: [], // 表单字典数据

      /**
       * 用户相关
       */
      userDialog: false, // 用户dialog
      userParams: {
        // 用户查询参数
        pageNum: 1, // 页码
        pageSize: 10, // 每页条数
        nickName: "", // 用户昵称
      },
      userTableLoading: false, // 用户数据加载
      userTableData: [], // 用户数据
      userTableTotal: 0, // 用户数据总数
      userSelectData: [], // 用户选中数据
      ids: [], // 选中的ID
      showForm: false,
      showSearch: false,
    };
  },
  mounted() {
    this.initPage();
    // 增加标签页监听
    document.addEventListener("visibilitychange", this.pageVisibilityChange);
  },
  beforeDestroy() {
    // 移除标签页监听
    document.removeEventListener("visibilitychange", this.pageVisibilityChange);
  },
  // -------------------- 方法 --------------------
  methods: {
    /**
     * 页面相关
     */
    // 初始化页面
    initPage() {
      // 查询待办数据
      this.queryList();
      // 查询字典类型数据
      this.queryType();
    },
    searchList() {
      this.queryParams.pageNum = 1;
      this.queryList();
    },
    // 当进入标签页时
    pageVisibilityChange() {
      if (document.visibilityState === "visible") {
        this.queryList();
      }
    },
    /**
     * 查询相关
     */
    // 查询数据
    queryList() {
      this.tableLoading = true;
      queryPageTodoTaskList(this.queryParams)
        .then((res) => {
          if (res.code === 200 && res.data) {
            // res.data.records.forEach((item) => {
            //   let content = item.content ? JSON.parse(item.content) : {};
            //   if (content) {
            //     (item.assetId = content.assetId),
            //       (item.type = content.type),
            //       (item.assetCode = content.assetCode),
            //       (item.assetName = content.assetName),
            //       (item.routeCode = content.routeCode),
            //       (item.routeName = content.routeName),
            //       (item.maintenanceSectionName = content.maintenanceSectionName),
            //       (item.managementMaintenanceName =
            //         content.managementMaintenanceName),
            //       (item.managementMaintenanceBranchName =
            //         content.managementMaintenanceBranchName);
            //   }
            // });
            this.tableData = res.data.records?.map(v => {
              let content = v.content ? JSON.parse(v.content) : {};
              // 管理处
              v.managementMaintenanceName = v.managementMaintenanceName || v.managementOfficeName || content.managementMaintenanceName || content.managementOfficeName;
              // 管养分处
              v.managementMaintenanceBranchName = v.managementMaintenanceBranchName || content.managementMaintenanceBranchName;
              // 资产编码
              v.assetCode = v.assetCode || content.assetCode;
              // 资产名称
              v.assetName = v.assetName || content.assetName;
              // 路线编码
              v.routeCode = v.routeCode || content.routeCode;
              // 路线名称
              v.routeName = v.routeName || content.routeName;
              // 养护路段
              v.maintenanceSectionName = v.maintenanceSectionName || v.routerName || content.maintenanceSectionName || content.routerName;
              // 类型
              v.type = content.type;
              return v;
            });
            this.queryTotal = res.data.total;
          }
          this.tableLoading = false;
        })
        .catch(() => {
          this.tableLoading = false;
        });
    },

    // 查询重置
    queryReset() {
      this.queryParams = {
        // 查询参数
        title: "", // 流程标题
        category: "基础数据审核", // 流程类型
        applyUserName: "", // 创建人
        startTime: "", // 开始时间
        endTime: "", // 结束时间
        pageNum: 1, // 页码
        pageSize: 10, // 每页条数
      };
      this.queryTime = "";
      this.queryList();
    },

    // 查询流程应对路由表单
    queryType() {
      this.getDicts("process_type").then((res) => {
        if (res.code === 200) {
          this.formProcessType = res.data;
        }
      });
    },

    /**
     * 表格相关
     */
    // 勾选表格项改变时
    tableSelectionChange(val) {
      this.tableSelection = val;
      this.ids = val.map((item) => item.businessKey);
    },

    setMuAudit() {
      if (this.tableSelection.length == 0) {
        this.$message.warning("请选择至少一条数据");
        return;
      } else {
        this.showForm = true;
      }
    },

    setAudit(value) {
      // if(this.ids.length == 0) {
      //   this.$message.warning('请选择一条数据')
      //   return
      // }else{
      //   this.showForm = true
      // }
      this.tableSelection = [value];
      this.showForm = true;
    },

    // 点击一行时
    tableRowClick(val) {
      this.$refs.tableRef.toggleRowSelection(val);
    },

    /**
     * 表单相关
     */
    // 初始化表单
    async formInit(type, data) {
      this.formType = type;
      switch (type) {
        case "examine":
          let routePath = await this.formCheckTyoe(data.processDefinitionKey);
          if (!routePath) {
            this.$message.warning("未查询到流程表单信息");
            return;
          }
          const routeUrl = this.$router.resolve({
            name: routePath,
            query: {
              taskId: data.taskId,
              businessKey: data.businessKey,
              processInstanceId: data.processInstanceId,
              taskKey: data.taskKey,
              route: routePath,
              name: data.category,
              title: data.title,
              isApprove: "0",
            },
          });
          window.open(routeUrl.href, "_blank");
          break;

        case "check":
          break;
      }
    },

    // 确认流程类型
    formCheckTyoe(type) {

      let check = this.formProcessType.findIndex(
        (item) => item.dictValue === type
      );
      if (check !== -1) {
        return this.formProcessType[check].remark;
      } else {
        return null;
      }
    },

    /**
     * 用户相关
     */

    // 打开用户查询框
    userDialogOpen() {
      this.userList();
      this.userDialog = true;
    },

    // 查询用户信息
    userList() {
      this.userTableLoading = true;
      listUser(this.userParams)
        .then((res) => {
          if (res.code === 200) {
            this.userTableData = res.rows;
            this.userTableTotal = res.total;
          }
          this.userTableLoading = false;
        })
        .catch(() => {
          this.userTableLoading = false;
        });
    },

    // 确认选择用户
    userSelect(item) {
      this.userSelectData = [];
      this.userSelectData.push({ value: item.userId, label: item.nickName });
      this.queryParams.applyUserName = item.userId;
      this.userDialog = false;
    },

    // 取消选择用户
    userSelctCancel() {
      this.userSelectData = [];
      this.queryParams.applyUserName = "";
    },
  },
};
</script>

<style lang="scss" scoped>
.app-container form:first-child .el-select,
.app-container form:nth-child(2) .el-select,
.app-container form:nth-child(2) ::v-deep .el-form-item__content,
.app-container form:first-child ::v-deep .el-form-item__content {
  width: 240px;
}

.app-container form:first-child .el-form-item:last-child ::v-deep .el-form-item__content {
  width: auto;
}

.app-container {
  padding: 10px;
  background-color: #c0c0c0;
  box-sizing: border-box;
}

.formDialog {
  ::v-deep .el-dialog__body {
    height: 600px;
    overflow-y: auto;
  }

  .titleBox {
    height: 22px;
    position: relative;
    display: flex;
    flex-direction: row;
    align-items: center;

    .title {
      font-size: 16px;
      color: black;
      margin: 0;
    }

    .subTitle {
      margin-left: 15px;
      font-size: 12px;
      color: #888888;
    }

    .riskLevel {
      user-select: none;
      position: absolute;
      // top: 0;
      right: 5%;
      display: flex;
      align-items: center;
      flex-direction: row;

      .title {
        font-size: 16px;
        font-weight: bold;
      }

      .main {
        font-size: 16px;
        font-weight: bold;
        padding: 5px 10px 5px 10px;
        color: white;
        box-sizing: border-box;
        border-radius: 5px;
      }
    }
  }

  .formMain {
    height: 100%;
    width: 100%;
    box-sizing: border-box;
    border: 1px solid #888888;
    position: relative;

    .mainCanvas {
      height: 100%;
      width: 100%;
    }

    .mainPanel {
      position: absolute;
      top: 0;
      right: 0;
      height: 100%;
      width: 300px;
      border-left: 1px solid #888888;
      background-color: rgb(248, 248, 248);
      overflow-y: auto;
    }
  }
}

.searchBox {
  padding: 10px;
  background: #fff;
  border-radius: 10px;
  transition: all 0.1s linear;
  display: flex;
  flex-direction: column;

  .searchMoreBox {
    min-width: 192px;
    margin-top: 10px;
    display: flex;
    align-items: center;
    flex-direction: row;
  }
}

.tableDiv {
  margin-top: 10px;
  background-color: white;
  padding-bottom: 10px;
  border-radius: 10px;
  transition: all 0.1s linear;
  display: flex;
  flex-direction: column;

  .btnBox {
    padding: 10px;
  }
}

.infoBox {
  padding: 15px 15px 0 15px;
  box-sizing: border-box;
  border-radius: 6px;
  border: 1px solid #c4c4c4;
  position: relative;

  .infoTitle {
    user-select: none;
    position: absolute;
    top: 0;
    left: 0;
    padding: 0 10px;
    font-size: 14px;
    line-height: 14px;
    font-weight: bold;
    transform: translateX(15px) translateY(-50%);
    background-color: white;
  }

  .imgBox {
    height: auto;
    width: 100%;
    display: flex;
    flex-wrap: wrap;
    align-content: flex-start;

    .imgItemBox {
      height: 240px;
      width: calc(100% / 3);
      box-sizing: border-box;
      padding: 10px;
      overflow-y: auto;
      display: flex;
      align-items: center;
      justify-content: center;
      position: relative;

      .imgDeleteBtn {
        position: absolute;
        z-index: 1;
        top: 10%;
        right: 10%;
      }
    }
  }
}

// 流程图相关
.userDialogBox {
  ::v-deep .el-dialog__body {
    height: 600px;
    overflow-y: auto;
  }

  .userBoxMain {
    height: 100%;
    width: 100%;
    display: flex;
    flex-direction: column;

    .searchBox {
      height: 48px;
      width: 100%;
    }

    .dataBox {
      height: calc(100% - 48px);
      width: 100%;
      overflow-y: auto;
    }
  }
}

::v-deep .bjs-powered-by {
  opacity: 0;
  user-select: none;
  pointer-events: none;
}

::v-deep .el-tabs .el-tabs__header {
  height: 54px;
}

// v-if过渡动画
// 查询框
.search-enter-active {
  transition: all 0.1s linear;
}

.search-enter {
  opacity: 0;
}

.search-leave-active {
  transition: all 0.1s linear;
}

.search-leave-to {
  opacity: 0;
}
</style>
