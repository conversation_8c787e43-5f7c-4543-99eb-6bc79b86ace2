<template>
  <div class="a-map" ref="mapRef" v-loading="loading" element-loading-spinner="el-icon-loading"
    element-loading-background="rgba(0, 0, 0, 0.8)"></div>
</template>

<script>
import AMapLoader from "@amap/amap-jsapi-loader";
import { getShapeList } from "@/api/oneMap/deptInfo";
import { getMenuSub } from "@/api/oneMap/menuSub";
import { getVectorTile } from "@/api/cockpit/index";
import cache from "@/plugins/cache";
import { WKT } from "ol/format";
import { Point } from "ol/geom";
import spotImg from "@/assets/map/spot.png";
import pointImg from "@/assets/map/point.png";
import * as turf from "@turf/turf";
import bgImg from "@/assets/cockpit/yunnan.png";
let AMap = null;
export default {
  name: "mapView",
  data() {
    return {
      loading: false,
      zoom: 7.8,
      center: [102.0007599, 23.7085797], // 中心点
      version: "1.4.15", // 版本号
      map: null, // 地图实例
      object3Dlayer: null, // 3D图层实例
      markerObj: {
        region: {},
        dPoint: {},
      }, // 管理处
      mergeData: [], // 合并数据
      markerLayer: [], // 点图层
    };
  },
  mounted() {
    this.__init();
  },
  methods: {
    async __init() {
      this.loading = true;
      let data = await this.getShapeList();
      this.mergeData = this.toMerge(data);
      AMap = await this.initMap();
      this.$emit('mapLoaded')
      this.loading = false;
      this.add3DMap();
      // 行政区
      await this.setDistrict(data);
      // 管理处点
      await this.setPoint(data);
      this.addPoint();
      // 图片图层
      // this.drawerLayer();
      // await this.setLineJson(); // 路线数据加载到地图上 - GeoJson
      // await this.setLineTile(); // 路线数据加载到地图上 - 图层
    },
    initMap() {
      window._AMapSecurityConfig = {
        securityJsCode: "8ba5a60100192adc21a2044b9582e26e",
      };
      return new Promise((resolve, reject) => {
        AMapLoader.load({
          key: "38ce82094eecafcb00a7dd5b323cc4d0",
          version: this.version,
          plugins: [
            "AMap.DistrictSearch",
            "AMap.MarkerClusterer",
            "AMap.Object3D",
            "AMap.CustomLayer",
            "ui/misc/PathSimplifier"
          ],
        }).then((AMap) => {
          this.map = new AMap.Map(this.$refs.mapRef, {
            viewMode: "3D",
            pitch: 55, // 设置俯仰角
            zoom: this.zoom,
            center: this.center || [102.0007599, 23.7085797],
            backgroundColor: "transparent", // 设置地图背景透明
            layers: [], // 空数组表示不显示任何底图图层
            features: [], // 空数组表示不显示任何覆盖物
            skyColor: "transparent", // 设置天空颜色为透明
            // showLabel: false, // 不显示地图文字标注
            disableSocket: true, // 禁用socket
            rotation: 5, // 设置旋转角度
          });
          resolve(AMap)
        })
      })
    },
    addPoint() {
      let data = JSON.parse(localStorage.getItem('mapData'));
      this.setPointByZoom([data]);
    },
    setPointByZoom(data) {
      let displayData = data || [];
      displayData.forEach((item, index) => {
        const wktString = item.dgeom;
        const wktFormat = new WKT();
        const geometry = wktFormat.readGeometry(wktString);
        const coordinates = geometry.getCoordinates();
        let labelContent = item.icon
          ? item.name
          : `<div class='labelContent'>${item.name}</div>`;
        let mapSize = this.isBig ? 80 : 40;
        // 创建高德地图点标记
        this.markerLayer[index] = new AMap.Marker({
          position: [coordinates[0], coordinates[1]], // 经纬度坐标
          icon: new AMap.Icon({
            image: item.icon || (item.sys_dept_id == 1 ? pointImg : spotImg),
            size: new AMap.Size(mapSize, mapSize), // Icon size in pixels
            imageSize: new AMap.Size(mapSize, mapSize), // Image size in pixels
          }),
          offset: new AMap.Pixel(-13, -30), // 设置点标记偏移量
          zIndex: 900,
          anchor: "top",
          scale: 0.8,
          // 文字
          label: {
            content: labelContent, // 设置文字内容
            direction: "top", // 设置文字方向
            offset: new AMap.Pixel(0, -4), // 设置文字偏移量
            style: {
              backgroundColor: !item.icon ? "rgba(0, 0, 0, 0.5)" : "", // 半透明黑色背景
              padding: "4px 2px",
              borderRadius: "4px",
              color: "#fff",
              fontSize: this.isBig ? "24px" : "14px",
            },
          },
          extData: {
            ...item,
          },
        });
        // 将标记添加到地图
        this.markerLayer[index].setMap(this.map);
        // 设置中心点
        this.map.setCenter(coordinates);
        this.map.setZoom(14, {
          duration: 1000, // 动画持续时间
          easing: 'cubicOut' // 动画效果
        });
      });
      // 创建聚合器
      this.markerCluster = new AMap.MarkerClusterer(this.map, this.markerLayer, {
        gridSize: 100, // 聚合的网格大小
        maxZoom: 12, // 最大缩放级别
        minZoom: 6, // 最小缩放级别
        minClusterSize: 4, // 聚合的最小数量。默认值为2，即小于2个点则不能成为一个聚合
        averageCenter: true, // 聚合点坐标为聚合内点的平均坐标
      });
    },
    // 获取 行政区域
    async getShapeList(deptId = null) {
      return new Promise((resolve, reject) => {
        let localData = cache.session.getJSON("rangeData");
        if (localData && localData.length) {
          resolve(localData);
        } else {
          this.$modal.loading();
          getShapeList({ sysDeptIds: deptId ? [deptId] : [] })
            .then((res) => {
              if (res.code == 200 && res.data) {
                cache.session.setJSON("rangeData", res.data);
                resolve(res.data);
              } else {
                resolve(null);
              }
            })
            .catch((err) => {
              reject(err);
            })
            .finally(() => {
              setTimeout(() => {
                this.$modal.closeLoading();
              }, 0);
            });
        }
      });
    },
    // 合并
    toMerge(data) {
      let arr = [];
      for (let index = 0; index < data.length; index++) {
        const mgeom = data[index].mgeom;
        // // 解析WKT字符串
        const geometry = new WKT().readGeometry(mgeom);
        // 获取所有点的数组
        const coordinates = geometry.getCoordinates();
        arr[index] = turf.polygon(coordinates[0], { combine: "yes" });
      }
      if (arr.length > 1) {
        var union = turf.union(turf.featureCollection(arr));
      } else {
        var union = arr[0];
      }
      return union;
    },
    // 添加行政区立体效果
    async add3DMap() {
      // 设置光照
      this.map.AmbientLight = new AMap.Lights.AmbientLight([1, 1, 1], 0.9);
      this.map.DirectionLight = new AMap.Lights.DirectionLight(
        [0, 0, 1],
        [1, 1, 1],
        0.1
      );

      this.object3Dlayer = new AMap.Object3DLayer({ zIndex: 1 });
      this.map.add(this.object3Dlayer);
      let coordinates = this.mergeData.geometry.coordinates[0];
      let height = 0;
      let color = "rgba(1,49,93, 0.7)";
      let prism = new AMap.Object3D.Prism({
        path: coordinates,
        height: height,
        color: color,
      });

      prism.transparent = true;
      // prism.backOrFront = "both";
      this.object3Dlayer.add(prism);

      let h3d = -99999;
      let c3d = "rgba(58,131,185,0.3)"; // 设置颜色为半透明 - 填充色
      let wall = new AMap.Object3D.Wall({
        path: coordinates,
        height: h3d,
        color: c3d,
      });
      wall.transparent = true;
      wall.backOrFront = "front";
      this.object3Dlayer.add(wall);
      new AMap.Polyline({
        path: coordinates,
        strokeColor: "#A0DAFF",
        strokeWeight: 3.5,
        strokeOpacity: 0.6,
        map: this.map,
        zIndex: 100,
      });
    },
    // 设置行政区域
    setDistrict(data) {
      return new Promise((resolve) => {
        let processed = 0;
        data.forEach((item) => {
          const wktString = item.mgeom;
          const wktFormat = new WKT();
          const geometry = wktFormat.readGeometry(wktString);
          const coordinates = geometry.getCoordinates();

          const polygon = new AMap.Polygon({
            path: coordinates,
            fillColor: "rgba(14,86,137,0)",
            fillOpacity: 0,
            strokeColor: "#021C2F",
            strokeWeight: 1.2,
            strokeOpacity: 0.2,
          });
          polygon.setMap(this.map);

          processed++;
          if (processed === data.length) {
            resolve();
          }
        });
      });
    },
    // 设置点
    setPoint(data, zIndex = 1000, size = 40, marker = 'region') {
      if (marker !== 'region') {
        this.pointList = data;
      }
      return new Promise((resolve) => {
        // 遍历点
        data.forEach((item, index) => {
          // 解析WKT字符串为坐标
          const wktString = item.dgeom || item.lastPoint;
          const wktFormat = new WKT();
          const geometry = wktFormat.readGeometry(wktString);
          const coordinates = geometry.getCoordinates();
          let labelContent = item.icon
            ? item.name
            : `<div class='label-content'>${item.name}</div>`;
          let mapSize = this.isBig ? size * 2 : size;
          // 创建高德地图点标记
          this.markerObj[marker][index] = new AMap.Marker({
            position: [coordinates[0], coordinates[1]], // 经纬度坐标
            icon: new AMap.Icon({
              image: item.icon || (item.sys_dept_id == 1 ? pointImg : spotImg),
              size: new AMap.Size(mapSize, mapSize), // Icon size in pixels
              imageSize: new AMap.Size(mapSize, mapSize), // Image size in pixels
            }),
            offset: new AMap.Pixel(-13, -30), // 设置点标记偏移量
            zIndex: zIndex,
            anchor: "top",
            scale: 0.7,
            // 文字
            label: {
              content: labelContent, // 设置文字内容
              direction: "top", // 设置文字方向
              offset: new AMap.Pixel(0, -4), // 设置文字偏移量
              style: {
                backgroundColor: !item.icon ? "rgba(0, 0, 0, 0.5)" : "", // 半透明黑色背景
                padding: "4px 2px",
                borderRadius: "4px",
                color: "#fff",
                fontSize: this.isBig ? "24px" : "14px",
                fontWeight: "bold",
              },
            },
            extData: {
              ...item,
            },
          });
          // 将标记添加到地图
          this.markerObj[marker][index].setMap(this.map);
          this.markerObj[marker][index].on("click", (event) => {
            // 点击事件处理逻辑
            let wkt = this.markerObj[marker][index].getExtData().mgeom;
            if (wkt) {
              let wktFormat = new WKT();
              let geometry = wktFormat.readGeometry(wkt);
              let coordinates = geometry.getCoordinates();
              let options = {
                hideWithoutStyle: false, //是否隐藏设定区域外的楼块
                areas: [
                  {
                    //visible:false,//是否可见
                    rejectTexture: true, // 是否屏蔽自定义地图的纹理
                    color1: "ffffff00", // 楼顶颜色
                    color2: "ffffcc00", // 楼面颜色
                    path: coordinates[0][0],
                  },
                ],
              };
              if (this.deptPolygon) {
                this.map.remove(this.deptPolygon);
              }
              this.deptPolygon = new AMap.Polygon({
                bubble: true,
                fillColor: "rgba(0,99,183, 0.6)",
                fillOpacity: 0.4,
                path: options.areas[0].path,
                map: this.map,
                strokeWeight: 1,
                strokeColor: "#ffffff",
                strokeOpacity: 0.8,
              });

              let array = options.areas[0].path.map((v) => {
                return [v.R || v.lng, v.Q || v.lat];
              });

              // const mesh = new AMap.Object3D.Mesh({
              //   path: options.areas[0].path,
              //   height: 5000, // 设置高度
              //   color: "#0088ff", // 设置颜色
              //   opacity: 0.8, // 设置透明度
              // });

              // this.object3Dlayer.add(mesh);

              // let prism = new AMap.Object3D.Prism({
              //   path: array,
              //   height: 5000,
              //   color: "#0088ffcc",
              // });

              // prism.transparent = true;
              // this.object3Dlayer.add(prism);
            }
            // 发送事件
            window.$Bus.$emit("deptClick", this.markerObj[marker][index].getExtData());
            this.$emit('click', this.markerObj[marker][index].getExtData());
          });
        });
        let zoom = 7;
        this.map.on("zoomchange", () => {
          zoom = this.map.getZoom();
          // 当缩放级别大于等于12时显示底图
          if (zoom >= 10) {
            // 显示地图标注
            this.map.setFeatures(["bg", "road", "point", "building"]);
            this.map.setMapStyle("amap://styles/darkblue");
          } else {
            // 小于12级时清空图层
            this.map.setFeatures([]);
            this.map.setMapStyle("");
          }
        });
        resolve();
      });
    },
    drawerLayer() {
      // 云南省边界坐标范围
      const yunnanBounds = new AMap.Bounds(
        // [96.653361356, 20.92568843],
        // [107.440941074, 29.364810714]
        [96.583361356, 20.80568843],
        [107.300941074, 29.193810714]
      );
      let coordinates = this.mergeData.geometry.coordinates[0];
      let arr = coordinates.map((item) => {
        return [item.R, item.Q];
      });

      // // 计算边界范围
      // let bounds = new AMap.Bounds(
      //   [arr[0][0], arr[0][1]],
      //   [arr[arr.length-1][0], arr[arr.length-1][1]]
      // );
      // arr.forEach((coord) => {
      //   bounds.extend(new AMap.LngLat(coord[0], coord[1]));
      // });

      // 计算边界范围
      let bounds = new AMap.Bounds(
        [arr[0][0], arr[0][1]],
        [arr[arr.length - 1][0], arr[arr.length - 1][1]]
      );

      // 遍历所有坐标点来扩展边界范围
      arr.forEach((coord) => {
        bounds.extend(new AMap.LngLat(coord[0], coord[1]));
      });

      // 获取边界的四个角点
      const southWest = bounds.getSouthWest();
      const northEast = bounds.getNorthEast();
      // 使用精确的边界点创建新的bounds
      bounds = new AMap.Bounds(
        [southWest.lng - 0.95, southWest.lat - 0.395],
        [northEast.lng + 1.14, northEast.lat - 0.03]
      );
      // 创建图片图层
      const imageLayer = new AMap.ImageLayer({
        url: bgImg,
        zooms: [3, 20],
        opacity: 0.8,
        zIndex: 10,
        bounds: bounds,
      });

      // 将图层添加到地图
      this.map.add(imageLayer);
    },
    // 设置切片数据
    async setLineTile() {
      let wms = new AMap.TileLayer.WMS({
        url: "https://gis.glyhgl.com/server/services/yanghu/gis_route_shape4/MapServer/WMSServer",
        blend: true,
        tileSize: 256,
        opacity: 1.0,
        visible: true,
        params: {
          FORMAT: "image/png", // 图像格式，如image/png
          LAYERS: "0", // 图层名称
          TILED: true, // 使用切片
          VERSION: "1.3.0", // WMS版本号，默认1.1.1
          STYLES: "", // 图层样式
        },
        zIndex: 100,
      });
      wms.setMap(this.map);
    },
  }
}
</script>

<style lang="scss" scoped>
.a-map {
  width: 100%;
  height: 100%;
  background-image: none;
  background-color: transparent !important;
}

.element {
  background-image: none;
  background-color: transparent;
}

img {
  opacity: 0;
  /* 完全透明 */
}

::v-deep .amap-logo {
  display: none !important;
}

::v-deep .amap-copyright {
  display: none !important;
}

::v-deep .labelContent {
  min-width: vwpx(200px);
  max-width: vwpx(300px);
  background: linear-gradient(90deg,
      rgba(13, 27, 43, 0) 0%,
      rgba(13, 27, 43, 0.8) 21%,
      rgba(13, 27, 43, 0.8) 77%,
      rgba(13, 27, 43, 0) 100%);
}

::v-deep .label-content {
  min-width: vwpx(200px);
  max-width: vwpx(300px);
  line-height: vwpx(52px);
  padding: 0 vwpx(10px);
  background: linear-gradient(90deg,
      rgba(90, 64, 56, 0.8) 0%,
      rgba(90, 64, 56, 0.8) 21%,
      rgba(90, 64, 56, 0.8) 77%,
      rgba(90, 64, 56, 0.8) 100%);
  font-weight: bold;
}

::v-deep .amap-marker-label {
  background: transparent;
  // min-width: vwpx(250px);
  // max-width: vwpx(300px);
  height: vwpx(56px);
  line-height: vwpx(46px);
  // background: linear-gradient(90deg, rgba(13, 27, 43, 0) 0%, rgba(13, 27, 43, 0.8) 21%, rgba(13, 27, 43, 0.8) 77%, rgba(13, 27, 43, 0) 100%);
  font-weight: 500;
  font-size: vwpx(28px);
  color: #ffffff;
  text-align: center;
  border: none;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
</style>