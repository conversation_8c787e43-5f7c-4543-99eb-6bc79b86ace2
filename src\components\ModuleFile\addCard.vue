<template>
  <div>
    <el-card
      class="card-box add-card"
      v-show="currentPage"
      @click.native="addNewCard"
    >
      <i class="el-icon-circle-plus"></i>
    </el-card>
    <el-card class="card-box" v-show="!currentPage">
      <!-- 编辑页面的内容 -->
      <el-page-header @back="addNewCard" content="新增页面"></el-page-header>
      <el-form ref="form" :model="form" label-width="80px">
        <el-form-item label="模块名称">
          <el-input v-model="form.moduleName"></el-input>
        </el-form-item>
        <el-form-item label="备注">
          <el-input v-model="form.remark"></el-input>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="toggleSave" :disabled="false">{{
            "保存"
          }}</el-button>
        </el-form-item>
      </el-form>
    </el-card>
  </div>
</template>
<script>
import { saveModuleFile } from "@/api/system/moduleFile.js";
export default {
  name: "addCard",
  props: {
    typeId: {
      type: Number,
      required: true,
    },
  },
  data() {
    return {
      currentPage: true, // 控制显示哪个页面
      form: {
        moduleName: "",
        remark: "",
      },
    };
  },
  methods: {
    addNewCard() {
      this.currentPage = !this.currentPage;
      this.form = {
        moduleName: "",
        remark: "",
        typeId: this.typeId,
      };
    },
    toggleSave() {
      saveModuleFile(this.form).then(() => {
        this.$emit("refresh");
        this.addNewCard();
      });
    },
  },
};
</script>

<style>
.card-box {
  cursor: pointer;
  justify-content: center;
  align-items: center;
  height: 100%;
  min-height: 200px;
  background: rgba(255, 255, 255, 0.5); /* 半透明背景 */
  backdrop-filter: blur(10px); /* 磨砂效果 */
  transition: transform 0.3s ease; /* 平滑过渡效果 */
}
.add-card {
  cursor: pointer;
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  min-height: 200px;
  background: rgba(255, 255, 255, 0.5); /* 半透明背景 */
  backdrop-filter: blur(10px); /* 磨砂效果 */
  transition: transform 0.3s ease; /* 平滑过渡效果 */
}

.add-card:hover {
  transform: scale(1.05); /* 鼠标悬停时放大 */
}

.card-box:hover {
  transform: scale(1.05); /* 鼠标悬停时放大 */
}

.el-icon-circle-plus {
  font-size: 3em; /* 调整图标大小 */
}
</style>