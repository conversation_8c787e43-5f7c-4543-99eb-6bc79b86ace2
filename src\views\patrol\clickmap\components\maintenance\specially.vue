<template>
  <div class="specially-maintenance">
    <Echarts :option="option" v-if="option" height="28vh" />
    <div class="divider"></div>
    <section class="info">
      <div class="info-list">
        <span>预算费用</span>
        <span>1084 <small>万元</small></span>
      </div>
      <div class="divider-column"></div>
      <div class="info-list">
        <span>实际费用</span>
        <span>999 <small>万元</small></span>
      </div>
      <div class="divider-column"></div>
      <div class="info-list">
        <span>实际费用占比</span>
        <span>86.31 <small>%</small></span>
      </div>
    </section>
  </div>
</template>

<script>
import { isBigScreen } from '../../util/utils';
import Echarts from '../echarts/echarts.vue';

export default {
  components: {
    Echarts,
  },
  data() {
    return {
      option: null,
      bigBool: isBigScreen(),
    }
  },
  mounted() {
    // 初始化图表数据
    this.initChartData();
  },
  methods: {
    initChartData() {
      let color = ["#01FBEF", "#F0AE4B", "#0154FB"];
      let chartData = [
        {
          name: "已完成",
          value: 1016,
        },
        {
          name: "暂未实施",
          value: 60,
        },
        {
          name: "正在实施",
          value: 403,
        },
      ];
      let arrName = [];
      let arrValue = [];
      let sum = 0;
      let pieSeries = [],
        lineYAxis = [];
      function getArrayValue(array, key) {
        var key = key || "value";
        var res = [];
        if (array) {
          array.forEach(function (t) {
            res.push(t[key]);
          });
        }
        return res;
      }
      // 数据处理
      chartData.forEach((v, i) => {
        arrName.push(v.name);
        arrValue.push(v.value);
        sum = sum + v.value;
      });

      // 图表option整理
      chartData.forEach((v, i) => {
        pieSeries.push({
          name: v.name || "",
          type: "pie",
          clockWise: false,
          hoverAnimation: false,
          radius: [83 - i * 15 + '%', 78 - i * 15 + '%'],
          center: ["35%", "55%"],
          label: {
            show: false,
          },
          itemStyle: {
            label: {
              show: false,
            },
            labelLine: {
              show: false
            },
            borderWidth: 15,
            borderRadius: 10,
          },
          data: [
            {
              value: v.value,
              name: v.name,
            },
            {
              value: sum - v.value,
              name: "",
              itemStyle: {
                color: "rgba(0,0,0,0)",
              },
            },
          ],
        });
        console.log('数据', v)
        pieSeries.push({
          name: v.name || "",
          type: "pie",
          silent: true,
          z: 1,
          clockWise: false, //顺时加载
          hoverAnimation: false, //鼠标移入变大
          radius: [83 - i * 15 + '%', 78 - i * 15 + '%'],
          center: ["35%", "55%"],
          label: {
            show: false,
          },
          itemStyle: {
            label: {
              show: false,
            },
            labelLine: {
              show: false
            },
            borderWidth: 15,
          },
          data: [
            {
              value: 7.5,
              itemStyle: {
                color: "rgb(7,41,86)",
              },
            },
            {
              value: 2.5,
              name: "",
              itemStyle: {
                color: "rgba(0,0,0,0)",
              },
            },
          ],
        });
        v.percent = ((v.value / sum) * 100).toFixed(1) + "%";
        lineYAxis.push({
          value: i,
          textStyle: {
            rich: {
              circle: {
                color: color[i],
                padding: [0, 5],
              },
            },
          },
        });
      });

      this.option = {
        backgroundColor: "rgba(0,0,0,0)",
        color: color,
        legend: {
          show: true,
          icon: "rect",
          itemWidth: 12,
          itemHeight: 12,
          borderRadius: 10,
          data: arrName,
          formatter: function (name) {
            return "{title|" + name + "}"
          },

          textStyle: {
            rich: {
              title: {
                fontSize: 13,
                lineHeight: 15,
                color: "rgb(255, 255, 255)"
              },
              value: {
                fontSize: 14,
                lineHeight: 20,
                color: "#fff"
              }
            }
          },
        },
        grid: {
          top: "10%",
          bottom: "66%",
          left: "35%",
          containLabel: false,
        },
        yAxis: [
          {
            type: "category",
            inverse: true,
            axisLine: {
              show: false,
            },
            axisTick: {
              show: false,
            },
            axisLabel: {
              formatter: function (params) {
                let item = chartData[params];
                return (
                  "{line|}{circle|●}{name|" +
                  item.name +
                  "}{bd|}{value|" +
                  item.value +
                  "}"
                );
              },
              interval: 0,
              inside: true,
              textStyle: {
                color: "#333",
                fontSize: 14,
                rich: {
                  line: {
                    width: 70,
                    height: 0,
                    borderWidth: 1,
                    borderColor: "#9CBDFF",
                    borderType: "dashed",
                  },
                  name: {
                    color: "#9CBDFF",
                    fontSize: 14,
                  },
                  bd: {
                    color: "#ccc",
                    padding: [0, 5],
                    fontSize: 14,
                  },
                  percent: {
                    color: "#fff",
                    fontSize: 14,
                  },
                  value: {
                    color: "#fff",
                    fontSize: 16,
                    fontWeight: 500,
                    padding: [0, 0, 0, 5],
                  },
                },
              },
              show: true,
            },
            data: lineYAxis,
          },
        ],
        xAxis: [
          {
            y: '0',
            x: 'center',
            show: false,
            data: chartData.map(v => v.name)
          },
        ],
        series: pieSeries,
      };

    },
  },
}
</script>

<style lang="scss" scoped>
@import "@/assets/styles/utils.scss";

.specially-maintenance {
  padding: vwpx(16px);
  color: #ffffff;
  width: 100%;
  height: 100%;

  .divider {
    width: 90%;
    margin: vwpx(20px) 5%;
    border: 1px dotted rgba(156, 189, 255, 0.5);
  }

  .info {
    width: 90%;
    height: vwpx(200px);
    margin-left: 5%;
    background: rgba(0, 0, 0, 0.1);
    box-shadow: inset 0px 0px 10px 0px #0065FF;
    border-radius: vwpx(12px);
    border: 1px solid #20A9FF;
    padding: 0 vwpx(30px);

    display: flex;
    align-items: center;
    justify-content: space-between;

    .info-list {
      display: flex;
      align-items: center;
      justify-content: center;
      flex-direction: column;

      span:first-child {
        font-family: Microsoft YaHei UI, Microsoft YaHei UI;
        font-weight: 700;
        font-size: vwpx(30px);
        color: #FFFFFF;
        text-align: center;
        font-style: normal;
        text-transform: none;
      }

      span:last-child {
        font-family: Microsoft YaHei UI, Microsoft YaHei UI;
        font-weight: 700;
        font-size: vwpx(48px);
        color: #42ABFF;
        text-align: center;
        font-style: normal;
        text-transform: none;
        margin-top: vwpx(15px);

        small {
          font-family: Microsoft YaHei UI, Microsoft YaHei UI;
          font-weight: 400;
          font-size: vwpx(24px);
          color: rgba(255, 255, 255, 0.8);
          text-shadow: 0px 0px 10px rgba(27, 126, 242, 0.8);
          text-align: center;
          font-style: normal;
          text-transform: none;
        }
      }
    }

    .divider-column {
      height: vwpx(110px);
      border: 1px dotted rgba(23, 116, 255, 0.5);
    }
  }
}
</style>