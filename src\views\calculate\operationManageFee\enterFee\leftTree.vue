<template>
  <div>
    <div class="head-container">
      <el-input
        v-model="keyword"
        placeholder="输入关键词检索"
        @change="handleSearch"
        clearable
        size="small"
        prefix-icon="el-icon-search"
        style="margin-bottom: 20px"
      />
    </div>
    <div class="head-container" style="width: 300px">
      <el-tree
        :data="filteredTreeData"
        :expand-on-click-node="false"
        :filter-node-method="filterNode"
        ref="tree"
        node-key="id"
        highlight-current
        @node-click="handleNodeClick"
      >
      </el-tree>
    </div>
  </div>
</template>
<script>
import {domainTree} from "@/api/calculate/operationManageFee/enterFee";

export default {
  data() {
    return {
      keyword: '',
      relaOptions: [],
      filteredTreeData: [],
    }
  },
  created() {
    this.getDeptTree()
  },
  methods: {
    // 关键词检索
    handleSearch() {
      const keyword = this.keyword.toLowerCase();
      this.filteredTreeData = this.relaOptions.filter(node => this.filterNode(node, keyword));
    },
    // 筛选节点
    filterNode(node, keyword) {
      if (String(node.label).indexOf(keyword) != -1) {
        return true;
      }
      if (node.children) {
        return node.children.some(childNode => this.filterNode(childNode, keyword));
      }
      return false;
    },
    // 查询部门下拉树结构
    getDeptTree() {
      domainTree().then(response => {
        const treeData = response.data
        treeData.forEach(item => {
          getChild(item)
        })
        function getChild(node) {
          node.label = node.deptName || node.year || node.maintenanceSectionName
          node.id = node.deptId || node.year || node.maintenanceSectionId
          if (node.children) {
            node.children.forEach(item => {
              if (node.year && node.children) {
                for (let i = 0; i < node.children.length; i++) {
                  node.children[i].prntYear = node.year
                }
              }
              getChild(item)
            })
          }
        }
        this.relaOptions = treeData
        this.filteredTreeData = [...this.relaOptions]
      });
    },
    handleNodeClick(e) {
      if (e.maintenanceSectionId) {
        const parmas = {
          maiSecId: e.maintenanceSectionId,
          year: e.prntYear,
          domainId: e.departmentId
        }
        this.$emit('query', parmas)
      }
    },
  }
}
</script>
<style scoped lang="scss">
.left-total {
  font-size: 13px;
  line-height: 28px;
  color: #606266;
  position: absolute;
  bottom: 10px;
  right: 10px;
}
</style>
<style lang="scss" scoped>
@import "@/assets/styles/business.scss";
</style>
