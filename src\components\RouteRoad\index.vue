<template>
    <div class="route-road" v-loading="loading">
        <el-select @change="changeSelect" :multiple="multiple" :disabled="disabled" filterable style="width: 100%;" :size="size" clearable v-model="selectValue" :placeholder="placeholder">
            <el-option v-for="item in routeOptions" :key="item.roadSectionId" :label="item.roadSectionName"
                :value="item.roadSectionId"></el-option>
        </el-select>
    </div>
</template>

<script>
import { listAllHighwaySections } from '@/api/system/highwaySections.js'
export default { // 路段选择器
    name: 'RouteRoad',
    components: {},
    props: {
        value: {
            type: [String, Array],
            default: ''
        },
        placeholder: {
            type: String,
            default: '请选择路段'
        },
        size: {
            type: String,
            default: 'medium'
        },
        disabled: {
            type: Boolean,
            default: false
        },
        multiple: {
            type: Boolean,
            default: false
        }
    },
    data() {
        return {
            loading: false,
            routeOptions: []
        }
    },
    watch: {},
    computed: {
        selectValue: {
            get: function () {
                return this.value
            },
            set: function(val) {
                this.$emit('input', val)
            }
        }
    },
    created() {
        this.getOptions()
    },
    mounted() { },
    methods: {
        getOptions() {
            this.loading = true
            listAllHighwaySections().then(res => {
                if (res.code === 200) {
                    this.routeOptions = res.data
                }
            }).finally(() => {
                this.loading = false
            })
        },
        changeSelect(val) { 
            this.$emit('change', val)
        }
    },
}
</script>

<style lang="scss" scoped>
.route-road {}
</style>