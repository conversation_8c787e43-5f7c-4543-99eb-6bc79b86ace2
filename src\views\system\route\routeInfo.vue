<template>
  <div class="container">
    <el-form ref="form" label-width="96px" disabled>
      <el-form-item label="路线编码" prop="routeCode">
        <el-input v-model="form.routeCode" placeholder=""/>
      </el-form-item>
      <el-form-item label="路线名称" prop="routeName">
        <el-input v-model="form.routeName" placeholder=""/>
      </el-form-item>
      <el-form-item label="路线长度" prop="roadSectionLength">
        <el-input v-model="form.roadSectionLength" placeholder=""/>
      </el-form-item>
      <el-form-item label="起点名称" prop="placeStartName">
        <el-input v-model="form.placeStartName" placeholder=""/>
      </el-form-item>
      <el-form-item label="终点名称" prop="placeEndName">
        <el-input v-model="form.placeEndName" placeholder=""/>
      </el-form-item>
      <el-form-item label="起点桩号" prop="pileStart">
        <PileInput format v-model="form.pileStart" @input="onPileChange" placeholder=""></PileInput>
      </el-form-item>
      <el-form-item label="终点桩号" prop="pileEnd">
        <PileInput format v-model="form.pileEnd" @input="onPileChange" placeholder=""></PileInput>
      </el-form-item>
      <el-form-item label="路线性质" prop="routeType">
        <el-select v-model="form.routeType" placeholder="" style="width: 100%">
          <el-option
            v-for="dict in dict.type.sys_route_nature"
            :key="dict.value"
            :label="dict.label"
            :value="parseInt(dict.value)"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="抗震等级" prop="seismicGrade">
        <dict-select placeholder="" type="side_slope_seismic_grade" v-model="form.seismicGrade"></dict-select>
      </el-form-item>
    </el-form>

  </div>
</template>
<script>

import PileInput from "@/views/system/route/pileinput.vue";
import {getRoute} from "@/api/system/route";

export default {
  name: "RouteInfo",
  dicts: ['sys_route_nature', 'side_slope_seismic_grade'],
  components: {PileInput},
  props: {
    id: {
      type: String,
    }
  },
  data() {
    return {
      form: {},
    }
  },
  watch: {
    id: {
      handler(val) {
        if (!val) {
          return
        }
        this.getInfo()
      },
      immediate: true
    }
  },
  methods: {
    async getInfo() {
      let {data} = await getRoute(this.id)
      this.form = data
    }
  },
};
</script>
<style scoped>
::v-deep .el-select .el-input.is-disabled .el-input__inner,
::v-deep .el-input.is-disabled .el-input__inner {
  color: #1d2129;
  background-color: transparent;
  cursor: auto;
}
</style>
