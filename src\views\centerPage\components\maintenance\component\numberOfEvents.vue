<template>
  <div class="number-of-events" v-loading="loading" element-loading-background="rgba(0, 0, 0, 0.4)">
    <Echarts :option="option" v-if="option" height="100%" key="eventsKey" />
  </div>
</template>

<script>
import * as echarts from "echarts";
import Echarts from '../../echarts/echarts.vue';
import { isBigScreen } from "@/views/cockpit/util/utils";
// api
import { getDiseaseEventCount } from '@/api/cockpit/maintain';

export default {
  name: 'NumberOfEvents',
  components: {
    Echarts
  },
  props: {
    year: {
      type: [String, Number],
      default: '',
    }
  },
  data() {
    return {
      isBig: isBigScreen(),
      option: null,
      loading: false,
    }
  },
  created() { },
  mounted() {
    // let legendData = ['2003年', '2004年'];
    // this.option = this.initCharts(legendData);
  },
  watch: {
    year: {
      async handler(newVal) {
        if (newVal) {
          this.loading = true;
          let data = await this.getData();
          this.loading = false;
          const uniqueYears = [...new Set(data.map(item => item.year))];
          let legendData = [];
          legendData = uniqueYears.map(year => `${year}年`);
          let cYear = this.year - 0;
          let cSeriesData = [];
          let pYear = cYear - 1;
          let pSeriesData = [];
          cSeriesData = data.filter(v => v.year == cYear).map(v => v.count);
          pSeriesData = data.filter(v => v.year == pYear).map(v => v.count);
          let xAxisData = data.filter(v => v.year == cYear).map(v => v.month);
          this.option = this.initCharts(legendData, xAxisData, cSeriesData, pSeriesData);
        }
      },
      immediate: true,
      deep: true,
    }
  },
  methods: {
    // 获取数据
    getData() {
      return new Promise((resolve, reject) => {
        getDiseaseEventCount(this.year).then((res) => {
          if (res.code === 200 && res.rows) {
            resolve(res.rows)
          } else {
            reject(res.msg)
          }
        }).catch(err => {
          reject(err)
        })
      })
    },
    initCharts(legendData = [], xData, data1 = [], data2 = []) {
      let option = {
        backgroundColor: 'rgba(255,255,255,0)',
        grid: {
          left: '2%',
          right: '4%',
          top: '15%',
          bottom: '2%',
          containLabel: true
        },
        tooltip: {
          show: true,
          trigger: 'item'
        },
        legend: {
          show: true,
          x: 'center',
          y: '0',
          icon: 'circle',
          itemWidth: this.isBig ? 30 : 10,
          itemHeight: this.isBig ? 30 : 10,
          textStyle: {
            color: '#fff',
            fontSize: this.isBig ? 24 : 12,
          },
          data: legendData
        },
        xAxis: [
          {
            type: 'category',
            boundaryGap: false,
            axisLabel: {
              color: '#fff',
              fontSize: this.isBig ? 24 : 12,
            },
            axisLine: {
              show: true,
              lineStyle: {
                color: 'transparent'
              }
            },
            axisTick: {
              show: false,
            },
            splitLine: {
              show: false,
            },
            data: xData || ['1', '2', '3', '4', '5', '6', '7', '8', '9', '10']
          }
        ],
        yAxis: [
          {
            type: 'value',
            name: '件',
            nameTextStyle: {
              color: '#999999',
              padding: [0, 0, 0, -30],
              fontSize: this.isBig ? 24 : 12,
            },
            min: 0,
            axisLabel: {
              formatter: '{value}',
              fontSize: this.isBig ? 24 : 12,
              textStyle: {
                color: '#999999'
              }
            },
            axisLine: {
              lineStyle: {
                color: '#27b4c2'
              }
            },
            axisTick: {
              show: false,
            },
            splitLine: {
              show: true,
              lineStyle: {
                color: 'rgba(110,112,121,0.5)'
              }
            },
            splitArea: false
          },
        ],
        series: [
          {
            name: legendData[0],
            type: 'line',
            stack: '总量',
            symbol: 'circle',
            symbolSize: 8,
            smooth: true, // 平滑曲线
            itemStyle: {
              normal: {
                color: '#1CFFBC',
                lineStyle: {
                  color: "#1CFFBC",
                  width: 1
                },
                areaStyle: {
                  color: new echarts.graphic.LinearGradient(0, 1, 0, 0, [{
                    offset: 0,
                    color: 'rgba(28,255,188,0.1)'
                  }, {
                    offset: 1,
                    color: 'rgba(28,255,188,0.6)'
                  }]),
                }
              }
            },
            markPoint: {
              itemStyle: {
                normal: {
                  color: 'red'
                }
              }
            },
            data: data1 || [7800, 9000, 7000, 10000, 12000, 12800, 8200, 9600, 9900, 10000]
          },
          {
            name: legendData[1],
            type: 'line',
            stack: '总量',
            symbol: 'circle',
            smooth: true,
            symbolSize: 8,
            itemStyle: {
              normal: {
                color: '#0154FB',
                lineStyle: {
                  color: "#0154FB",
                  width: 1
                },
                areaStyle: {
                  color: new echarts.graphic.LinearGradient(0, 1, 0, 0, [{
                    offset: 0,
                    color: 'rgba(1,84,251,0.1)'
                  }, {
                    offset: 1,
                    color: 'rgba(1,84,251,0.6)'
                  }]),
                }
              }
            },
            data: data2 || [5000, 7100, 5100, 8400, 7800, 7900, 5000, 5800, 6000, 6200]
          },
        ]
      };
      return option;
    }
  }
}
</script>

<style lang="scss" scoped>
.number-of-events {
  width: 100%;
  height: 100%;
  padding: 5px;
}
</style>