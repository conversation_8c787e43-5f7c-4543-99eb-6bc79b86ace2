<template>
  <div class="record-main">
    <el-descriptions :column="1" border>
      <el-descriptions-item
        :label="item.label"
        v-for="(item, index) in myTableData"
        :key="'myTable' + index"
        >{{ row[item.key] || "" }}</el-descriptions-item
      >
    </el-descriptions>

    <div class="right-steps">
      <el-timeline>
        <el-timeline-item
          v-for="(item, index) in stepsData"
          :key="'steps' + index"
          hide-timestamp
          :color="item.approved ? 'green' : 'red'"
        >
          <div class="time-line-item">
            <div class="time-line-timestamp">
              {{ item.endTime }}
            </div>
            <div class="time-line-content">
              <p class="content-title">{{ item.nodeName }}</p>
              <p>操作人：{{ item.assigneeName }}</p>
            </div>
          </div>
        </el-timeline-item>
      </el-timeline>
    </div>
  </div>
</template>
<script>

export default {
  data() {
    return {
      myTableData: [
        {
          label: "项目名称",
          key: "projectName",
        },
        {
          label: "任务单名称",
          key: "constructionName",
        },
        {
          label: "任务单编码",
          key: "constructionCode",
        },
        {
          label: "路段名称",
          key: "maiSecName",
        },
        {
          label: "位置",
          key: "position",
        },
      ],
      stepsData: [],
    };
  },
  props: {
    row: {
      type: Object,
      default: ()=> {},
    },
    stepsData: {
      type: Array,
      default: ()=> [],
    }
  },
};
</script>
<style lang="scss" scoped>
.record-main {
  display: flex;
  .el-descriptions {
    width: 250px;
  }
  .right-steps {
    flex: 1;
    padding-left: 20px;
    height: 500px;
    overflow-y: auto;

    ::v-deep .el-timeline {
      padding-left: 120px;
    }

    .time-line-item {
      display: flex;

      .time-line-timestamp {
        position: absolute;
        left: -120px;
        font-size: 18px;
        width: 105px;
        word-break: break-all;
      }

      .time-line-content {
        p {
            margin-top: 0;
            margin-bottom: 8px;
        }
        .content-title {
          font-size: 18px;
        }

      }
    }
  }
}
</style>
<style lang="scss" scoped>
@import "@/assets/styles/business.scss";
</style>
