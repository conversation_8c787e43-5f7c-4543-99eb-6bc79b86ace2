<template>
  <div class="formDialog" :class="oneMap?'one-map':''">
    <div class="titleBox">
      <div class="title">{{ formTitle }}</div>
      <div class="subTitle" v-if="formParams.id">ID：{{ formParams.id }}</div>
      <div class="riskLevel" v-if="!formParams.disasterType.includes('其它')">
        <p class="title">风险等级：</p>
        <p class="main" :style="{ 'background':
              formParams.riskGrade.includes('一') ? 'rgb(254, 0, 0)' :
              formParams.riskGrade.includes('二') ? 'rgb(255, 192, 1)' :
              formParams.riskGrade.includes('三') ? 'rgb(255, 255, 3)' : 'rgb(1, 176, 241)' }"
        >{{ formParams.riskGrade }}</p>
        <el-tag
          type="info"
          effect="plain"
          style="margin-left: 5px;"
        >{{ `${formParams.riskScore}分` }}
        </el-tag>
      </div>
    </div>
    <el-form ref="formRef" :model="formParams" disabled label-width="125px" label-position="right" style="width: 99%;padding-top: 10px">
      <div class="infoBox" style="margin-bottom: 20px;" v-loading="formLoading">
        <div class="infoTitle">
          基础信息
        </div>
        <el-row :gutter="12">
          <el-col :span="12">
            <el-form-item label="管养单位名称" prop="maintenanceUnitId">
              <el-select ref="formMainRef" v-model="formParams.maintenanceUnitId" placeholder="请选择管养单位"
                         style="width: 100%;">
                <el-option v-for="item in formMaintenanceRenderList" :label="item.label" :value="item.id"
                           :key="item.id" style="display: none;"></el-option>
                <el-tree
                  :data="formMaintenanceList"
                  :props="{ children: 'children', label: 'label', value: 'id' }"
                  :expand-on-click-node="false"
                  highlight-current
                  default-expand-all
                >
                </el-tree>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item prop="roadSectionId">
                <span slot="label">
                  <el-tooltip content="选择管养单位后带出" placement="top">
                    <i class="el-icon-question"></i>
                  </el-tooltip>
                  路段名称
                </span>
              <el-select v-model="formParams.roadSectionId" placeholder="请选择路段" style="width: 100%;">
                <el-option v-for="item in formRoadSectionList" :label="item.maintenanceSectionName"
                           :value="item.maintenanceSectionId" :key="item.maintenanceSectionId"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="12">
          <el-col :span="12">
            <el-form-item prop="routerNum">
                <span slot="label">
                  <el-tooltip content="选择路段后带出" placement="top">
                    <i class="el-icon-question"></i>
                  </el-tooltip>
                  路线名称
                </span>
              <el-select v-model="formParams.routerNum" placeholder="请选择路线" style="width: 100%;">
                <el-option v-for="item in formRouteList" :label="`${item.routeName}（${item.routeCode}）`"
                           :value="item.routeCode" :key="item.routeCode"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item prop="technicalGrade">
                <span slot="label">
                  <el-tooltip content="选择路段后带出" placement="top">
                    <i class="el-icon-question"></i>
                  </el-tooltip>
                  技术等级
                </span>
              <el-select disabled v-model="formParams.technicalGrade" placeholder="请选择技术等级"
                         style="width: 100%;">
                <el-option v-for="dict in dict.type.sys_route_grade" :label="dict.label" :value="dict.value"
                           :key="dict.value"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="12">
          <el-col :span="8">
            <el-form-item prop="">
                <span slot="label">
                  <el-tooltip content="省级区划代码" placement="top">
                    <i class="el-icon-question"></i>
                  </el-tooltip>
                  省级区划
                </span>
              <el-select v-model="divisionsProvincial" disabled style="width: 100%;">
                <el-option v-for="item in divisionsProvincialList" :label="`${item.label}（${item.id}）`"
                           :value="item.id" :key="item.id"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item prop="">
                <span slot="label">
                  <el-tooltip content="市级区划代码" placement="top">
                    <i class="el-icon-question"></i>
                  </el-tooltip>
                  市级区划
                </span>
              <el-select v-model="divisionsMunicipal" clearable style="width: 100%;">
                <el-option v-for="item in divisionsMunicipalList" :label="`${item.label}（${item.id}）`"
                           :value="item.id" :key="item.id"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item prop="areaCode">
                <span slot="label">
                  <el-tooltip content="县级区划代码，用于生成灾害编码" placement="top">
                    <i class="el-icon-question"></i>
                  </el-tooltip>
                  县级区划
                </span>
              <el-select v-model="formParams.areaCode" clearable style="width: 100%;">
                <el-option v-for="item in areaCodeList" :label="`${item.label}（${item.id}）`" :value="item.id"
                           :key="item.id"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="12">
          <el-col :span="12">
            <el-form-item prop="disasterName">
                <span slot="label">
                  <el-tooltip
                    :content="`灾害名称，依据“中心桩号+${formParams.disasterType.includes('其它') ? '其它' : formParams.disasterType}” 保存后自动生成（例如“K0+100${formParams.disasterType.includes('其它') ? '其它' : formParams.disasterType}”）`"
                    placement="top">
                    <i class="el-icon-question"></i>
                  </el-tooltip>
                  灾害名称
                </span>
              <el-input v-model="formParams.disasterName" disabled placeholder="请输入灾害名称"/>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item prop="disasterNum">
                <span slot="label">
                  <el-tooltip
                    :content="`灾害编号，按“路线编号+县级行政区划代码+${formRiskTypeShort}+四位数字”编号（例如“S81520303CT0001”）自动生成`"
                    placement="top">
                    <i class="el-icon-question"></i>
                  </el-tooltip>
                  灾害编号
                </span>
              <el-input v-model="formParams.disasterNum" disabled placeholder="请输入灾害编号"/>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="12">
          <el-col :span="12">
            <el-form-item prop="startLatitudeAndLongitude">
                <span slot="label">
                  <el-tooltip content="起点经纬度利用数据采集系统移动端 APP 采集" placement="top">
                    <i class="el-icon-question"></i>
                  </el-tooltip>
                  起点经纬度
                </span>
              <el-input v-model="formParams.startLatitudeAndLongitude" placeholder="请输入起点经纬度，以英文逗号分隔">
                <el-button slot="append" icon="el-icon-location">坐标拾取
                </el-button>
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item prop="endLatitudeAndLongitude">
                <span slot="label">
                  <el-tooltip content="止点经纬度利用数据采集系统移动端 APP 采集" placement="top">
                    <i class="el-icon-question"></i>
                  </el-tooltip>
                  止点经纬度
                </span>
              <el-input v-model="formParams.endLatitudeAndLongitude" placeholder="请输入止点经纬度，以英文逗号分隔">
                <el-button slot="append" icon="el-icon-location">坐标拾取
                </el-button>
              </el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="12">
          <el-col :span="12">
            <el-form-item prop="pileStartNum">
                <span slot="label">
                  <el-tooltip
                    content="起止点桩号，根据现场调查填写起点桩号和止点桩号（例如起点桩号 K0+100，止点桩号 K0+200）"
                    placement="top">
                    <i class="el-icon-question"></i>
                  </el-tooltip>
                  起点桩号
                </span>
              <div style="width: 100%; display: flex; flex-direction: row; flex-wrap: nowrap;">
                <el-tag
                  type="info"
                  effect="plain"
                  size="medium"
                >
                  K
                </el-tag>
                <el-input v-model="pileStartK" controls-position="right" style="width: 50%;"
                          placeholder="请输入起点桩号"/>
                <el-tag type="info" effect="plain" size="medium">
                  +
                </el-tag>
                <el-input v-model="pileStartAdd" controls-position="right" style="width: 50%;"
                          placeholder="请输入起点桩号"/>
              </div>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item prop="pileEndNum">
                <span slot="label">
                  <el-tooltip
                    content="起止点桩号，根据现场调查填写起点桩号和止点桩号（例如起点桩号 K0+100，止点桩号 K0+200）"
                    placement="top">
                    <i class="el-icon-question"></i>
                  </el-tooltip>
                  终点桩号
                </span>
              <div style="width: 100%; display: flex; flex-direction: row; flex-wrap: nowrap;">
                <el-tag type="info" effect="plain" size="medium">
                  K
                </el-tag>
                <el-input v-model="pileEndK" controls-position="right" style="width: 50%;"
                          placeholder="请输入终点桩号"/>
                <el-tag type="info" effect="plain" size="medium">
                  +
                </el-tag>
                <el-input v-model="pileEndAdd" controls-position="right" style="width: 50%;"
                          placeholder="请输入终点桩号"/>
              </div>
            </el-form-item>
          </el-col>

        </el-row>
      </div>

      <Crumble v-if="formParams.disasterType === '崩塌'" ref="formSubRef"/>

      <LandSlide v-if="formParams.disasterType === '滑坡'" ref="formSubRef"/>

      <DebrisFlow v-if="formParams.disasterType === '泥石流'" ref="formSubRef"/>

      <SubSide v-if="formParams.disasterType === '沉陷与塌陷'" ref="formSubRef"/>

      <WaterDestruction v-if="formParams.disasterType === '水毁'" ref="formSubRef"/>

      <Other v-if="formParams.disasterType.includes('其它')" ref="formSubRef"/>

    </el-form>
    <div slot="footer" class="dialog-footer">
      <el-row class="footerTip" v-if="formParams.createName">
        <el-col :span="12" style="display: flex; justify-content: flex-start;">
          采集人：{{ formParams.createName }}
        </el-col>
        <el-col :span="12" style="display: flex; justify-content: flex-end;">
          采集时间：{{ formParams.createTime }}
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<script>
import {queryPageRiskForm} from "@/api/disaster/risk/risk";
import {deptTreeSelect} from "@/api/tmpl";
import {getTreeByEntity} from "@/api/system/geography";
import {listByMaintenanceSectionId} from "@/api/baseData/common/routeLine";
import Crumble from "@/views/disaster/risk/view/crumble";
import LandSlide from "@/views/disaster/risk/view/landslide";
import DebrisFlow from "@/views/disaster/risk/view/debrisFlow";
import SubSide from "@/views/disaster/risk/view/subside";
import WaterDestruction from "@/views/disaster/risk/view/waterDestruction";
import Other from "@/views/disaster/risk/view/other";

export default {
  props: {
    id: { // 父组件传递的ID
      type: String,
      required: true
    }
  },
  inject: ['oneMap'],
  dicts: [
    'disaster_risk_type',  // 灾害类型
    'disaster_risk_other_type', // 灾害类型（其它）
    'sys_route_grade', // 路线等级
  ],
  components: {
    Crumble,
    LandSlide,
    DebrisFlow,
    SubSide,
    WaterDestruction,
    Other
  },
  data() {
    return {
      formDialog: false, // 控制弹窗显示
      formLoading: false, // 表单加载
      formTitle: "信息调查表",
      formParams: {},
      formRiskGrade: '', // 风险等级
      formMaintenanceList: [], // 管养单位列表
      formMaintenanceRenderList: [], // 管养单位渲染列表
      formRoadSectionList: [], // 路段列表
      formRouteList: [], // 路线列表
      /**
       * 桩号相关
       */
      pileStartK: '',
      pileStartAdd: '',
      pileEndK: '',
      pileEndAdd: '',

      /**
       * 区划相关
       */
      divisionsProvincial: '53', // 省级区划
      divisionsProvincialList: [ // 省级区划列表
        {
          disabled: true,
          id: "53",
          label: "云南省",
          level: 1
        }
      ],
      divisionsMunicipal: '', // 市级区划
      divisionsMunicipalList: [], // 市级区划列表
      areaCodeList: [], // 县级区划列表
      formRiskTypeShort: '', // 表单灾害类型英文简写
    };
  },
  mounted() {
    if (this.id) {
      this.fetchFormData(this.id);
      // 获取管养单位数据
      this.queryMaintenanceList()
      this.formDialog = true
    }
  },
  methods: {
    async fetchFormData(id) {
      this.formLoading = true
      await queryPageRiskForm(id).then((res) => {
        if (res.code === 200) {
          // 备份当前表单数据
          this.formParams = JSON.parse(JSON.stringify(res.data.disasterRisk))
          this.formSubData = JSON.parse(JSON.stringify(res.data.data))
          if (this.formParams.disasterType.includes('其它')) {
            this.formTitle = "其它信息调查表"
          } else {
            this.formTitle = this.formParams.disasterType + "信息调查表"
          }

          if (this.formParams.pileStartNum) {
            this.pileStartK = this.formParams.pileStartNum.split('+')[0].split('K')[1]
            this.pileStartAdd = this.formParams.pileStartNum.split('+')[1]
          }
          if (this.formParams.pileEndNum) {
            this.pileEndK = this.formParams.pileEndNum.split('+')[0].split('K')[1]
            this.pileEndAdd = this.formParams.pileEndNum.split('+')[1]
          }

          this.queryRouterList(this.formParams.roadSectionId)

          switch (this.formParams.disasterType) {
            case '崩塌':
              this.formRiskTypeShort = 'BT'
              break;

            case '滑坡':
              this.formRiskTypeShort = 'HP'
              break;

            case '泥石流':
              this.formRiskTypeShort = 'NL'
              break;

            case '沉陷与塌陷':
              this.formRiskTypeShort = 'CT'
              break;

            case '水毁':
              this.formRiskTypeShort = 'SH'
              break;

            default:
              this.formRiskTypeShort = 'QT'
              break;
          }

          let area = this.formParams.disasterNum.split(this.formRiskTypeShort)[0]
          console.log(area)
          this.formParams.areaCode = area.substr(-6)
          if (this.formParams.areaCode === this.formParams.routerNum) {
            this.formParams.areaCode = ''
          }

          this.$nextTick(() => {
            console.log('触发初始化')
            // 清除主表表单验证
            this.$refs.formRef.clearValidate()
            // 初始化子表
            this.$refs.formSubRef.initPage("view", this.formParams.id, this.formParams.disasterType)
            this.$refs.formSubRef.$refs.subFormRef.clearValidate()
          })
          // 获取区划数据（市级）
          this.queryDivisionsTree()

        }
        this.formLoading = false
      }).catch(() => {
        this.formLoading = false
      })
    },
    // 获取管养单位
    queryMaintenanceList() {
      let vo = {
        deptTypeList: [1, 3, 4],
        types: 100
      }
      deptTreeSelect(vo).then((res) => {
        if (res.code === 200) {
          this.formMaintenanceList = res.data
          this.handleMainRender(this.formMaintenanceList)
        }
      }).catch((err) => {
        this.$message.error(err)
        this.formMaintenanceList = []
        this.formMaintenanceRenderList = []
      })
    },
    // 管养单位渲染列表处理
    handleMainRender(data) {
      data.forEach(item => {
        this.formMaintenanceRenderList.push(item)
        if (item.children) {
          this.handleMainRender(item.children)
        }
      })
    },
    // 获取路线信息
    queryRouterList(val) {
      listByMaintenanceSectionId({maintenanceSectionId: val}).then(res => {
        if (res.code == 200) {
          this.formRouteList = res.data
        }
      }).catch((err) => {
        this.$message.error(err)
        this.formRouteList = []
      })
    },

    // 获取区划树
    queryDivisionsTree() {
      let vo = {
        supCode: this.divisionsProvincial
      }
      getTreeByEntity(vo).then((res) => {
        if (res) {
          this.divisionsMunicipalList = res.data

          let check = false
          for (let i = 0; i < this.divisionsMunicipalList.length; i++) {
            if (check) break
            for (let j = 0; j < this.divisionsMunicipalList[i].children.length; j++) {
              if (this.divisionsMunicipalList[i].children[j].id === this.formParams.areaCode) {
                this.divisionsMunicipal = this.divisionsMunicipalList[i].id
                this.areaCodeList = this.divisionsMunicipalList[i].children
                check = true
                break
              }
            }
          }

        }
      })
    },
  },
}
</script>

<style lang="scss" scoped>
@import "@/assets/styles/common.scss";
.formDialog {
  .dialog-footer {
    width: 100%;

    .footerTip {
      color: #ffffff;
      font-size: 12px;
    }
  }

  .titleBox {
    height: 50px;
    display: flex;
    flex-direction: row;
    align-items: center;

    .title {
      font-size: 16px;
      margin: 0;
    }

    .subTitle {
      margin-left: 15px;
      font-size: 12px;
      color: #888888;
    }

    .riskLevel {
      user-select: none;
      position: absolute;
      // top: 0;
      right: 5%;
      display: flex;
      align-items: center;
      flex-direction: row;

      .title {
        font-size: 16px;
        font-weight: bold;
      }

      .main {
        font-size: 16px;
        font-weight: bold;
        padding: 5px 10px 5px 10px;
        color: white;
        box-sizing: border-box;
        border-radius: 5px;
      }

      .score {
        color: #888888;
        font-size: 14px;
      }

    }
  }

  .infoBox {
    padding: 15px 15px 0 15px;
    box-sizing: border-box;
    border-radius: 6px;
    border: 1px solid #C4C4C4;
    position: relative;

    .infoTitle {
      user-select: none;
      position: absolute;
      top: 0;
      left: 0;
      padding: 0 10px;
      font-size: 14px;
      line-height: 16px;
      font-weight: bold;
      transform: translateX(15px) translateY(-50%);
      background-color: #02063f;
    }

    .imgBox {
      height: auto;
      width: 100%;

      ::v-deep .el-card__body {
        width: 100%;
        display: flex;
        flex-direction: row;
        flex-wrap: wrap;
        align-content: flex-start;
      }

      .imgBoxCard {
        width: 100%;

        .cardMain {
          height: 260px;
          width: 33%;
          box-sizing: border-box;
          padding: 0 10px;
          display: flex;
          flex-direction: column;
          position: relative;

          .imgDeleteBtn {
            position: absolute;
            z-index: 1;
            top: 20%;
            right: 5%;
          }

          .imgTitle {
            height: 28px;
            width: 100%;
            font-size: 16px;
            font-weight: bold;
            white-space: nowrap;
            text-overflow: ellipsis;
            overflow: hidden;
          }

          .img {
            height: calc(100% - (28px + 28px));
            width: 100%;
            padding: 10px 0;
            position: relative;
            z-index: 0;
          }

          .footer {
            height: 28px;
            color: #888888;
            font-size: 14px;
          }
        }
      }
    }

    .noneBox {
      user-select: none;
      height: 200px;
      width: 100%;
      display: flex;
      justify-content: center;
      align-items: center;
      color: #888888;
    }
  }
}

::v-deep .el-tag--plain.el-tag--info {
  background-color: #BBBBBB;
}
</style>
