<template>
  <div class="page-two" :class="isBig ? 'no-margin' : 'has-margin'" v-loading="loading" element-loading-text="加载中"
    element-loading-spinner="el-icon-loading" element-loading-background="rgba(0, 0, 0, 0.3)">
    <div class="empty" v-if="isEmpty"></div>
    <div v-else class="main">
      <el-tabs v-model="activeName" tab-position="left" @tab-click="tabClick" :before-leave="beforeLeave">
        <el-tab-pane v-for="item in categoryList" :key="item.id" :label="item.name" :name="item.id">
          <div class="right-box" :style="isBig ? { height: '100vh' } : { height: '92vh' }">
            <component :is="item.id" :key="`component-${item.id}`" :currentStructure="currentStructure" :ref="item.id"
              @changeDraw="changeDraw">
            </component>
          </div>
        </el-tab-pane>
      </el-tabs>
    </div>
  </div>
</template>

<script>
import { isBigScreen } from '../../utils/utils.js';
import Cards from "../components/cards.vue"
import { fetchGet } from "../../utils/api.js"
// 批量导入 components 目录下的所有 .vue 文件
const componentContext = require.context('./components', false, /\.vue$/)
const components = {}
componentContext.keys().forEach(key => {
  const componentName = key.split('/').pop().replace(/\.\w+$/, '')
  components[componentName] = componentContext(key).default
})

export default {
  name: 'pageTwo',
  props: {},
  inject: ['iThis'],
  components: { Cards, ...components },
  data() {
    return {
      isBig: isBigScreen(),
      isEmpty: false,
      loading: false,
      activeName: 'WSD',
      currentStructure: {},
      list: [
        {
          id: 'WSD',
          name: '温湿度分析',
        },
        {
          id: 'CLHZ',
          name: '车辆荷载分析',
        },
        {
          id: 'WIND',
          name: '风速风向分析',
        },
        {
          id: 'YB',
          name: '结构温度及应变分析',
        },
        {
          id: 'ZLWY',
          name: '主梁竖向位移分析',
        },
        {
          id: 'WYTJ',
          name: '伸缩缝支座梁端位移统计',
        },
        {
          id: 'PW',
          name: '主梁水平、塔、墩顶偏位分析',
        },
        {
          id: 'SL',
          name: '索力分析',
        },
        {
          id: 'ZD',
          name: '振动时程图分析',
        },
        {
          id: 'SDJC',
          name: '隧道边坡监测分析',
        },
      ],
      categoryList: [],
      isDraw: false,
    }
  },
  created() {
    this.init();
  },
  methods: {
    init() {
      this.loading = true;
      this.currentStructure = this.iThis.structureData
      const url = 'https://jkjc.glyhgl.com:22586/bigScreen/structure/analysisConfig/getConfigUnderStructureCode'
      const params = {
        structureCode: this.iThis.params.code,
      };
      fetchGet(url, params).then(res => {
        if (res.code === 200) {
          this.isEmpty = res.data.result.length === 0 ? true : false;
          this.$emit('setAnalysis', !this.isEmpty)
          console.log(this.isEmpty, 'this.isEmpty')
          const types = res.data.result.map(item => item.analysisType);
          this.categoryList = this.list.filter(item => types.includes(item.id));
          this.activeName = this.categoryList[0].id;
          setTimeout(() => {
            this.$refs[this.activeName][0]?.draw();
          }, 300)
        }
      })
        .catch(() => {
          this.isEmpty = true;
          this.$emit('setAnalysis', !this.isEmpty)
        })
        .finally(() => {
          this.loading = false;
        });
    },
    tabClick() {
      if (this.isDraw === true) return;
      const component = this.$refs[this.activeName];
      if (component && component[0].dataList.length === 0) component[0].draw();
    },
    // 切换绘图状态
    changeDraw(state) {
      this.isDraw = state;
    },
    // 绘图时禁止切换
    beforeLeave() {
      if (this.isDraw === true) {
        this.$message({ type: 'warning', message: "绘图时不能切换Tab栏", duration: 2000 });
        return false
      } else {
        return true
      }
    },
  },
  watch: {},
}
</script>

<style lang="scss" scoped>
@import "@/assets/styles/utils.scss";

.page-two {
  width: calc(100% - 10px);
  height: 100%;
  margin-right: 10px;
  background-color: rgba(9, 25, 45, 1);
  touch-action: none;
  user-select: none;
  overflow: hidden;

  .empty {
    width: 100%;
    height: 100%;
    background: url('~@/assets/monitoringSystem/empty.png') no-repeat center;
  }

  .main {
    width: 100%;
    height: 100%;

    ::v-deep {

      .el-tabs__nav-wrap::after {
        display: none;
      }

      .el-tabs__active-bar {
        display: none;
      }

      .el-tabs__item {
        height: vwpx(90px);
        line-height: vwpx(90px);
        font-size: vwpx(32px);
        background: rgba(1, 102, 254, 0.2);
        border-radius: 0px vwpx(14px) vwpx(14px) 0px;
        border: 1px solid #1D77FF;
        border-left: none;
        padding: 0 vwpx(30px);
        color: #ffffff;
        margin-bottom: vwpx(15px);
      }

      .el-tabs__item.is-active {
        background-color: #238DFF;
      }
    }

    .right-box {
      width: 100%;
      height: 100%;
    }
  }
}

.has-margin {
  margin-top: vwpx(160px);
  height: 92vh;
}

.no-margin {
  margin-top: vwpx(160px);
  height: 92vh;
  // height: 100vh;
}
</style>

<style lang="scss">
@import "@/assets/styles/utils.scss";

.page-two-box {
  width: 100%;
  height: 100%;

  ::-webkit-scrollbar {
    width: vwpx(14px);
    height: vwpx(14px);
  }

  ::-webkit-scrollbar-thumb {
    background-color: rgba(1, 102, 254, 0.4);
  }

  ::-webkit-scrollbar-track {
    background-color: rgba(0, 35, 94, .6);
  }

  // 搜索框
  .search-box {
    width: 100%;
    min-height: vwpx(120px);
    background: rgba(1, 102, 254, 0.3) !important;
    border: 0;
    color: #fff;
    font-size: vwpx(32px);
    border-radius: vwpx(24px);
    margin-bottom: vwpx(20px);
    padding: 0 vwpx(40px);
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    transition: all 0.3s;

    .search-item-span {
      margin-right: vwpx(40px) !important;
    }

    // 公共样式
    .search-item {
      margin: vwpx(10px) vwpx(60px) vwpx(10px) 0;
      display: flex;
      justify-content: center;
      align-items: center;
    }
  }

  // 图表区域
  .chart-box {
    width: 100%;
    height: calc(100% - #{vwpx(140px)});
    // border: 1px solid #0166fe;
    overflow-y: auto;
    display: grid;
    grid-template-columns: repeat(2, 1fr); // 两列布局
    gap: vwpx(20px); // 元素之间的间距

    .chart {
      width: 100%;
      height: 100%;
    }
  }

  .empty-box {
    width: 100%;
    height: calc(100% - #{vwpx(140px)});
    display: grid;
    place-items: center;
    font-size: vwpx(36px);
    color: #666;
    font-weight: 700;
  }
}
</style>