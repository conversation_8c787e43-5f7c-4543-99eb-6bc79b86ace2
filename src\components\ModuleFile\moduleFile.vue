<template>
  <el-tabs tab-position="left" style="height: 100vh">
    <el-tab-pane
      v-for="(item, index) in list"
      :key="'index-' + index + '-' + item.id"
      :label="item.name"
    >
      <template v-slot:label>
        <div
          v-if="!item.isEditing"
          class="tab-label-container tab-label-span"
          :ref="'tab-' + item.id"
        >
          <span @dblclick="startEditing(item)" :ref="'span-' + item.id">
            {{ item.name }}
          </span>
          <el-button
            type="text"
            icon="el-icon-edit"
            @click.stop="startEditing(item)"
          ></el-button>
          <el-button
            type="text"
            icon="el-icon-delete"
            @click.stop="deleteItem(item)"
          ></el-button>
        </div>
        <div v-else class="tab-label-container tab-label-edit">
          <el-input
            v-model="item.editingName"
            class="tab-input"
            size="mini"
            :style="{
              width: item.editWidth + 'px',
            }"
            @keyup.enter="confirmEdit(item)"
          ></el-input>

          <el-button
            type="text"
            icon="el-icon-check"
            @click.stop="confirmEdit(item)"
          ></el-button>
          <el-button
            type="text"
            icon="el-icon-close"
            @click.stop="cancelEdit(item)"
          ></el-button>
        </div>
      </template>
      <cardBox :type-id="item.id" :is-upload="isUpload"></cardBox>
    </el-tab-pane>
    <el-tab-pane :key="'static-999'" :disabled="true">
      <template v-slot:label>
        <div
          class="tab-plus-label"
          v-if="!addItem.isEditing"
          @click.stop="startAddItem()"
        >
          <i class="el-icon-plus"></i>
        </div>
        <div class="tab-plus-label" v-else>
          <el-input
            v-model="addItem.name"
            class="tab-input"
            size="mini"
            :style="{
              width: addItem.editWidth + 'px',
            }"
            @keyup.enter="confirmAddItem()"
          ></el-input>
          <el-button
            type="text"
            icon="el-icon-check"
            @click.stop="confirmAddItem()"
          ></el-button>
          <el-button
            type="text"
            icon="el-icon-close"
            @click.stop="cancelAddItem()"
          ></el-button>
        </div>
      </template>
    </el-tab-pane>
  </el-tabs>
</template>

<script>
import cardBox from "@/components/ModuleFile/cardBox.vue";
import { getList, saveModule, deleteModule } from "@/api/system/moduleType.js";

export default {
  name: "moduleFile",
  components: {
    cardBox,
  },
  data() {
    return {
      list: [],
      addItem: {
        isEditing: false,
        name: "",
        editWidth: 100,
      },
    };
  },
  props: {
    isUpload: {
      type: Boolean,
      default: false,
    },
  },
  created() {
    this.fetchList();
  },
  methods: {
    fetchList() {
      getList()
        .then((response) => {
          this.list = response.data;
        })
        .catch((error) => {
          console.error("请求列表数据失败:", error);
        });
    },
    startAddItem() {
      this.addItem.isEditing = true;
      this.addItem.name = "";
      this.addItem.editWidth = this.findMaxWidth();
    },
    confirmAddItem() {
      this.saveForm(this.addItem);
      this.addItem.isEditing = false;
      this.addItem.name = "";
    },
    cancelAddItem() {
      this.addItem.isEditing = false;
      this.addItem.name = "";
    },
    startEditing(item) {
      this.list = this.list.map((i) => {
        if (i.id === item.id) {
          const span = this.$refs["span-" + item.id]; // 获取对应的span元素
          if (span) {
            const width = span[0].offsetWidth; // 计算span元素的宽度
            return {
              ...i,
              isEditing: true,
              editingName: i.name,
              editWidth: width,
            };
          }
        }
        return { ...i, isEditing: false };
      });
    },
    confirmEdit(item) {
      // 处理确认编辑逻辑
      item.name = item.editingName;
      item.isEditing = false;
      // 发送更新请求...
      this.saveForm(this.addItem);
    },
    cancelEdit(item) {
      item.isEditing = false;
    },
    deleteItem(item) {
      this.deleteForm(item.id);
    },
    saveForm(item) {
      saveModule(item).then(() => {
        this.$message.success("保存成功");
        this.fetchList();
      });
    },
    deleteForm(id) {
      this.$confirm("此操作将永久删除该文件, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          // 调用删除接口
          deleteModule(id).then(() => {
            this.$message.success("删除成功");
            this.fetchList();
          });
        })
        .catch(() => {
          this.$message.info("已取消删除");
        });
    },
    findMaxWidth() {
      // 检查 this.list 是否存在且长度大于0
      if (!Array.isArray(this.list) || this.list.length === 0) {
        return 20; // 直接返回默认值
      }

      const maxWidth = this.list.reduce((max, item) => {
        // 使用可选链和空值合并运算符简化表达式
        const width = this.$refs[`span-${item.id}`]?.[0]?.offsetWidth ?? 0;
        return Math.max(max, width);
      }, 0); // 初始值为 0

      return maxWidth || 100; // 如果 maxWidth 为 0，则返回 20
    },
  },
};
</script>
<style scoped>
.app-container home {
  min-height: 100vh;
}

.tab-plus-label {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 5px;
  border: 1px solid transparent;
  border-radius: 0%;
  transition: border-color 0.3s ease;
}

.tab-plus-label:hover {
  border-color: #409eff;
  cursor: pointer;
}
</style>

<style>
.tab-input .el-input__inner {
  padding-left: 5px;
  padding-right: 5px;
  box-sizing: border-box;
}
</style>
