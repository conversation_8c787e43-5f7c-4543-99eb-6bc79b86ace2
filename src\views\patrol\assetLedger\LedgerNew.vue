<template>
  <PageContainer>
    <template slot="search">
      <!-- 查询 -->
      <div class="searchBox">
        <CascadeSelection style="grid-column: 1 / 3" :form-data="queryForm" v-model="queryForm" types="201" multiple />
        <el-input v-model="queryForm.assetCode" :placeholder="`请输入${partsType[type].substring(0, 2)}编码`" clearable
          prefix-icon="el-icon-user" @keyup.enter.native="handleQuery" />
        <el-input v-model="queryForm.assetName" :placeholder="`请输入${partsType[type].substring(0, 2)}名称`" clearable
          prefix-icon="el-icon-user" @keyup.enter.native="handleQuery" />
        <el-date-picker v-model="queryForm.checkTimeHas" type="year" :placeholder="`请选择${partsType[type].substring(0, 2)}年份`"
          clearable prefix-icon="el-icon-date" value-format="yyyy" @keyup.enter.native="handleQuery" />
        <div style="display: flex; gap: 10px">
          <el-button type="primary" icon="el-icon-search" size="mini" @click="queryhandle">搜索</el-button>
          <el-button icon="el-icon-refresh" size="mini" @click="queryReset">重置</el-button>
        </div>
      </div>
    </template>
    <!-- <template slot="header">
      <el-button v-hasPermi="['baseData:bridge:export']" type="primary" @click="handleExport">导出</el-button>
    </template> -->
    <template slot="body">
      <el-table v-adjust-table ref="table" v-loading="loading" height="100%" border :data="staticList"
        :span-method="objectSpanMethod">
        <el-table-column label="序号" type="index" width="80" align="center">
          <template v-slot="scope">
            {{ scope.$index + (pageNum - 1) * pageSize + 1 }}
          </template>
        </el-table-column>
        <el-table-column label="管理处" align="center" prop="managementOffice" min-width="130" show-overflow-tooltip />
        <el-table-column label="路段" align="center" prop="sectionName" min-width="130" show-overflow-tooltip />
        <el-table-column label="月份" align="center" prop="month" min-width="130" show-overflow-tooltip>
          <template v-slot="scope">
            {{ `${queryForm.checkTimeHas}-${scope.row.month <= 9 ? '0' + scope.row.month : scope.row.month}` }} </template>
        </el-table-column>
        <el-table-column label="应检" align="center" prop="totalCount" min-width="130" show-overflow-tooltip>
          <template v-slot="scope">
            <el-button type="text" @click="gotoDetails(scope.row)">{{ scope.row.totalCount }}</el-button>
          </template>
        </el-table-column>
        <el-table-column label="已检" align="center" prop="completedCount" min-width="130" show-overflow-tooltip>
          <template v-slot="scope">
            <el-button type="text" @click="gotoDetails(scope.row, true)">{{ scope.row.completedCount }}</el-button>
          </template>
        </el-table-column>
        <el-table-column label="未检" align="center" prop="inPeriodCount" min-width="130" show-overflow-tooltip>
          <template v-slot="scope">
            <el-button type="text" @click="gotoDetails(scope.row, false)">{{ scope.row.inPeriodCount }}</el-button>
          </template>
        </el-table-column>
      </el-table>
      <!-- <pagination :total="total" :page.sync="pageNum" :limit.sync="pageSize" @pagination="handleCurrentChange" /> -->
    </template>
  </PageContainer>
</template>

<script>
import moment from 'moment';
import CascadeSelection from '@/components/CascadeSelectionManagementOffice/index.vue';
import { assetCheckGetYearlyStatistics } from '@/api/patrol/assetLedger'

export default {
  name: 'AssetLedger',
  dicts: ['patrol_inspection_status'],
  components: {
    CascadeSelection,
  },
  props: {
    type: {
      type: Number,
      default: 2,
    },
  },
  data() {
    return {
      queryShow: false,
      showIframe: true,
      partsType: {
        2: '桥梁经常检查',
        4: '涵洞经常检查',
        6: '隧道经常检查',
      },
      queryForm: {
        type: this.type,
        assetName: null,
        assetCode: null,
        checkTimeHas: moment().format('YYYY'),
        managementMaintenanceIds: null,
        maintenanceSectionId: null,
        routeCodes: null,
        needNowDept: true,
      },
      // 查询参数
      queryParams: {
        type: this.type,
        assetName: null,
        assetCode: null,
        checkTimeHas: moment().format('YYYY'),
        managementMaintenanceIds: null,
        maintenanceSectionId: null,
        routeCodes: null,
        needNowDept: true,
      },
      staticList: [],
      loading: false,
      pageNum: 1,
      pageSize: 50,
      managementOfficeSpanArr: [], // 存储管理处列的合并信息
      sectionNameSpanArr: [], // 存储路段列的合并信息
    };
  },
  computed: {
    // 计算当前页显示的数据
    currentPageData() {
      return this.staticList.slice(
        (this.pageNum - 1) * this.pageSize,
        this.pageNum * this.pageSize
      );
    },
    // 数据总数
    total() {
      return this.staticList.length;
    }
  },
  mounted() {
    this.queryhandle()
  },
  methods: {
    queryhandle() {
      // 创建临时表单对象进行格式转换
      const tempForm = { ...this.queryForm };
      
      // 将年份转换为完整的日期时间格式 (年份-01-01 00:00:00)
      if (tempForm.checkTimeHas) {
        tempForm.checkTimeHas = `${tempForm.checkTimeHas}-01-01 00:00:00`;
      }
      
      this.queryParams = tempForm;
      this.getList()
    },
    // 重置查询条件
    queryReset() {
      this.queryForm = {
        type: this.type,
        assetName: null,
        assetCode: null,
        checkTimeHas: moment().format('YYYY'),
        managementMaintenanceIds: null,
        maintenanceSectionId: null,
        routeCodes: null,
        needNowDept: true,
      };
      this.queryhandle();
    },
    handleExport() { },
    async getList() {
      this.loading = true
      let res = await assetCheckGetYearlyStatistics(this.queryParams)
      if (res.code === 200) {
        this.staticList = res.data
        this.computeSpans('managementOffice', 'managementOfficeSpanArr');
        this.computeSpans('sectionName', 'sectionNameSpanArr');
      }
      this.loading = false
    },
    // 处理每页条数变化
    handleSizeChange(val) {
      this.pageSize = val;
      this.pageNum = 1; // 重置到第一页
    },
    // 处理页码变化
    handleCurrentChange({ page, limit }) {
      this.pageSize = limit;
      this.pageNum = page;
    },
    // 计算合并信息
    computeSpans(prop, spanArrName) {
      const spans = [];
      let count = 0; // 连续相同项计数
      let startIndex = 0; // 合并起始行

      for (let i = 0; i < this.staticList.length; i++) {
        if (i === 0) {
          spans.push(1); // 第一行默认合并1行
          startIndex = 0;
          continue;
        }
        // 当前行与上一行相同，计数增加，当前行合并0
        if (this.staticList[i][prop] === this.staticList[i - 1][prop]) {
          spans[startIndex] += 1; // 起始行合并数+1
          spans.push(0); // 当前行合并0
        } else {
          spans.push(1); // 不同则合并1行
          startIndex = i; // 更新起始行
        }
      }
      this[spanArrName] = spans;
    },

    // 合并单元格方法
    objectSpanMethod({ rowIndex, columnIndex }) {
      if (columnIndex === 1) { // 管理处列
        return {
          rowspan: this.managementOfficeSpanArr[rowIndex],
          colspan: this.managementOfficeSpanArr[rowIndex] > 0 ? 1 : 0
        };
      } else if (columnIndex === 2) { // 路段列
        return {
          rowspan: this.sectionNameSpanArr[rowIndex],
          colspan: this.sectionNameSpanArr[rowIndex] > 0 ? 1 : 0
        };
      } else {
        return { rowspan: 1, colspan: 1 }; // 其他列不合并
      }
    },
    gotoDetails(row, state = null) {
      let params = {
        isInspected: state,
        maintenanceSectionId: row.sectionName,
        expiry: row.formatTime,
        type: null,
      }
      // 确保expiry是完整的日期时间格式
      if (params.expiry && params.expiry.length === 7) { // YYYY-MM 格式
        params.expiry = `${params.expiry}-01 00:00:00`;
      }
      
      let name = ''
      if (this.type == 2) { // 桥梁经常检查
        params.type = 1
        name = 'BridgeDetails'
      } else if (this.type == 4) { // 涵洞经常检查
        params.type = 3
        name = 'CulvertDetails'
      } else if (this.type == 6) { // 隧道经常检查
        params.type = 2
        name = 'TunnelDetails'
      }
      this.$router.push({ name, params });
    },
  },
};
</script>

<style lang="scss" scoped>
.app-container form:first-child .el-select,
.app-container form:nth-child(2) .el-select,
.app-container form:nth-child(2) ::v-deep .el-form-item__content,
.app-container form:first-child ::v-deep .el-form-item__content {
  width: 240px;
}

.app-container form:first-child .el-form-item:last-child ::v-deep .el-form-item__content {
  width: auto;
}

.app-container {
  display: flex;
  flex-direction: column;
  padding: 10px;
  background-color: #c0c0c0;
  box-sizing: border-box;
}

.formDialog {
  ::v-deep .el-dialog__body {
    height: 600px;
    overflow-y: auto;
  }

  .dialog-footer {
    width: 100%;

    .footerTip {
      color: #888888;
      font-size: 14px;
    }
  }

  .titleBox {
    height: 22px;
    position: relative;
    display: flex;
    flex-direction: row;
    align-items: center;

    .title {
      font-size: 16px;
      color: black;
      margin: 0;
    }

    .subTitle {
      margin-left: 15px;
      font-size: 12px;
      color: #888888;
    }

    .riskLevel {
      user-select: none;
      position: absolute;
      // top: 0;
      right: 5%;
      display: flex;
      align-items: center;
      flex-direction: row;

      .title {
        font-size: 16px;
        font-weight: bold;
      }

      .main {
        font-size: 16px;
        font-weight: bold;
        padding: 5px 10px 5px 10px;
        color: white;
        box-sizing: border-box;
        border-radius: 5px;
      }

      .score {
        color: #888888;
        font-size: 14px;
      }
    }
  }
}

.searchBox {
  padding: 10px;
  background: #fff;
  border-radius: 10px;
  transition: all 0.1s linear;
  display: grid;
  gap: 20px;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));

  .searchMoreBox {
    min-width: 192px;
    margin-top: 10px;
    display: flex;
    align-items: center;
    flex-direction: row;
  }
}

.tableDiv {
  flex: 1;
  margin-top: 10px;
  background-color: white;
  padding-bottom: 10px;
  border-radius: 10px;
  transition: all 0.1s linear;
  display: flex;
  flex-direction: column;

  .btnBox {
    padding: 10px;
  }
}

.infoBox {
  padding: 15px 15px 0 15px;
  box-sizing: border-box;
  border-radius: 6px;
  border: 1px solid #c4c4c4;
  position: relative;

  .infoTitle {
    user-select: none;
    position: absolute;
    top: 0;
    left: 0;
    padding: 0 10px;
    font-size: 14px;
    line-height: 14px;
    font-weight: bold;
    transform: translateX(15px) translateY(-50%);
    background-color: white;
  }

  .imgBox {
    height: auto;
    width: 100%;
    display: flex;
    flex-wrap: wrap;
    align-content: flex-start;

    .imgItemBox {
      height: 240px;
      width: calc(100% / 3);
      box-sizing: border-box;
      padding: 10px;
      overflow-y: auto;
      display: flex;
      align-items: center;
      justify-content: center;
      position: relative;

      .imgDeleteBtn {
        position: absolute;
        z-index: 1;
        top: 10%;
        right: 10%;
      }
    }
  }
}

.coordinateDialog {
  .coordinateMap {
    height: 600px;
    width: 100%;
    position: relative;

    #coordinateBox {
      height: 100%;
      width: 100%;
      border-radius: 5px;
      position: relative;
      z-index: 0;
    }

    .coordinateSearch {
      position: absolute;
      z-index: 1;
      top: 10px;
      left: 10px;
      width: 50%;
      padding: 10px;
      box-sizing: border-box;
      background-color: #fff;
      border-radius: 5px;
      border: 1px solid #dcdfe6;
      box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.12), 0 0 6px 0 rgba(0, 0, 0, 0.04);
      display: flex;
      flex-direction: row;
    }

    .coordinateTip {
      position: absolute;
      z-index: 1;
      top: 10px;
      right: 10px;
      padding: 10px;
      box-sizing: border-box;
      background-color: #fff;
      border-radius: 5px;
      border: 1px solid #dcdfe6;
      box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.12), 0 0 6px 0 rgba(0, 0, 0, 0.04);
    }
  }
}

// v-if过渡动画
// 查询框
.search-enter-active {
  transition: all 0.1s linear;
}

.search-enter {
  opacity: 0;
}

.search-leave-active {
  transition: all 0.1s linear;
}

.search-leave-to {
  opacity: 0;
}

::v-deep .treeselect-main {
  line-height: 28px;
  font-size: 12px;
}

::v-deep .vue-treeselect__placeholder {
  line-height: 28px;
  color: #1d2129;
}

::v-deep .vue-treeselect__input {
  line-height: 28px;
}

::v-deep .vue-treeselect__control {
  height: 28px;
  font-size: 12px;
  font-weight: 400;
}

::v-deep .vue-treeselect__single-value {
  line-height: 28px;
}

::v-deep .vue-treeselect__menu-container {
  font-family: Arial;
  color: #1d2129;
}
</style>
