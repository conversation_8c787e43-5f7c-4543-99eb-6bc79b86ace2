<template>
  <div class="road-interflow-edit">
    <el-tabs>
      <el-tab-pane label="施工计量单清单">
        <el-row :gutter="15">
          <el-col :span="24">
            <el-form
              ref="queryForm"
              :inline="true"
              :model="queryParams"
              label-width="68px"
              size="mini"
            >
              <el-form-item>
                <el-input
                  v-model="queryParams.settleCode"
                  placeholder="施工计量单编码"
                  style="width: 190px"
                ></el-input>
              </el-form-item>
              <el-form-item>
                <el-input
                  v-model="queryParams.operator"
                  placeholder="操作人"
                  style="width: 190px"
                ></el-input>
              </el-form-item>
              <el-form-item>
                <el-date-picker
                  v-model="queryParams.operatorDate"
                  type="daterange"
                  range-separator="至"
                  start-placeholder="操作起始时间"
                  end-placeholder="操作截止时间"
                  style="width: 240px"
                >
                </el-date-picker>
              </el-form-item>
              <el-form-item>
                <el-button
                  icon="el-icon-search"
                  size="mini"
                  type="primary"
                  @click="handleQuery1"
                >搜索</el-button
                >
                <el-button
                  icon="el-icon-refresh"
                  size="mini"
                  @click="resetQuery"
                >重置</el-button
                >
              </el-form-item>
            </el-form>
            <el-row :gutter="10" class="mb8">
              <el-col :span="1.5">
                <el-button
                  icon="el-icon-download"
                  size="mini"
                  type="primary"
                  @click="exportList1"
                >导出清单
                </el-button>
              </el-col>
            </el-row>
          </el-col>
          <el-col :span="24">
            <div class="draggable">
              <el-table v-adjust-table
                ref="dataTable"
                v-loading="loading"
                :data="tableData1"
                border
                height="600"
                highlight-current-row
                row-key="id"
                size="mini"
                stripe
                style="width: 100%"
                @selection-change="table1SelectionChange"
              >
                <el-table-column type="selection" width="55"> </el-table-column>
                <el-table-column
                  align="center"
                  label="序号"
                  type="index"
                  width="50"
                />
                <template v-for="(column, index) in columns1">
                  <el-table-column
                    v-if="column.visible"
                    :label="column.label"
                    :prop="column.field"
                    :width="column.width"
                    align="center"
                  >
                    <template slot-scope="scope">
                      <dict-tag
                        v-if="column.dict"
                        :options="dict.type[column.dict]"
                        :value="scope.row[column.field]"
                      />
                      <template v-else-if="column.slots">
                        <RenderDom
                          :index="index"
                          :render="column.render"
                          :row="scope.row"
                        />
                      </template>
                      <span v-else-if="column.isTime">{{
                          parseTime(scope.row[column.field], "{y}-{m}-{d}")
                        }}</span>
                      <span v-else>{{ scope.row[column.field] }}</span>
                    </template>
                  </el-table-column>
                </template>
              </el-table>
              <pagination
                v-show="total1 > 0"
                :limit.sync="queryParams.pageSize"
                :page.sync="queryParams.pageNum"
                :total="total1"
                @pagination="handleQuery1"
              />
            </div>
          </el-col>
        </el-row>
      </el-tab-pane>
      <el-tab-pane label="扣款清单">
        <el-row :gutter="15">
          <el-col :span="24">
            <el-row :gutter="10" class="mb8">
              <el-col :span="1.5">
                <el-button
                  icon="el-icon-plus"
                  size="mini"
                  type="primary"
                  v-has-menu-permi="['calcprojectdes:deduction:add']"
                  @click="
                    openDeduction = true;
                    deduction = {};
                  "
                >新增
                </el-button>
              </el-col>
              <el-col :span="1.5">
                <el-button
                  icon="el-icon-download"
                  size="mini"
                  type="primary"
                  @click="exportList4"
                >导出清单
                </el-button>
              </el-col>
            </el-row>
          </el-col>
          <el-col :span="24">
            <div class="draggable">
              <el-table v-adjust-table
                ref="dataTable"
                v-loading="loading"
                :data="tableData4"
                border
                height="600"
                highlight-current-row
                row-key="id"
                size="mini"
                stripe
                style="width: 100%"
              >
                <el-table-column
                  align="center"
                  label="序号"
                  type="index"
                  width="50"
                />
                <template v-for="(column, index) in columns4">
                  <el-table-column
                    v-if="column.visible"
                    :label="column.label"
                    :prop="column.field"
                    align="center"
                  >
                    <template slot-scope="scope">
                      <dict-tag
                        v-if="column.dict"
                        :options="dict.type[column.dict]"
                        :value="scope.row[column.field]"
                      />
                      <template v-else-if="column.slots">
                        <RenderDom
                          :index="index"
                          :render="column.render"
                          :row="scope.row"
                        />
                      </template>
                      <span v-else-if="column.isTime">{{
                          parseTime(scope.row[column.field], "{y}-{m}-{d}")
                        }}</span>
                      <span v-else>{{ scope.row[column.field] }}</span>
                    </template>
                  </el-table-column>
                </template>
              </el-table>
              <pagination
                v-show="total4 > 0"
                :limit.sync="queryParams.pageSize"
                :page.sync="queryParams.pageNum"
                :total="total4"
                @pagination="handleQuery1"
              />
            </div>
          </el-col>
        </el-row>
      </el-tab-pane>
      <el-tab-pane label="计量调整">
        <el-row :gutter="15">
          <el-col :span="24">
            <el-form
              ref="queryForm"
              :inline="true"
              :model="queryParams"
              label-width="68px"
              size="mini"
            >
              <el-form-item>
                <dict-select
                  v-model="queryParams.dataType"
                  type="project_type"
                  placeholder="费用类型"
                  style="width: 190px"
                ></dict-select>
              </el-form-item>
              <el-form-item>
                <el-button
                  icon="el-icon-search"
                  size="mini"
                  type="primary"
                  @click="handleQuery5"
                >搜索</el-button
                >
                <el-button
                  icon="el-icon-refresh"
                  size="mini"
                  @click="resetQuery"
                >重置</el-button
                >
              </el-form-item>
            </el-form>
          </el-col>
          <el-col :span="24">
            <el-row :gutter="10" class="mb8">
              <el-col :span="1.5">
                <el-button
                  icon="el-icon-plus"
                  size="mini"
                  type="primary"
                  v-has-menu-permi="['calcprojectdes:fundadjust:add']"
                  @click="openCost = true;cost={}"
                >新增
                </el-button>
              </el-col>
            </el-row>
          </el-col>
          <el-col :span="24">
            <div class="draggable">
              <el-table v-adjust-table
                ref="dataTable"
                v-loading="loading"
                :data="tableData5"
                border
                height="600"
                highlight-current-row
                row-key="id"
                size="mini"
                stripe
                style="width: 100%"
              >
                <el-table-column
                  align="center"
                  label="序号"
                  type="index"
                  width="50"
                />
                <template v-for="(column, index) in columns5">
                  <el-table-column
                    v-if="column.visible"
                    :fixed="column.fixed"
                    :label="column.label"
                    :prop="column.field"
                    align="center"
                  >
                    <template slot-scope="scope">
                      <dict-tag
                        v-if="column.dict"
                        :options="dict.type[column.dict]"
                        :value="scope.row[column.field]"
                      />
                      <template v-else-if="column.slots">
                        <RenderDom
                          :index="index"
                          :render="column.render"
                          :row="scope.row"
                        />
                      </template>
                      <span v-else-if="column.isTime">{{
                          parseTime(scope.row[column.field], "{y}-{m}-{d}")
                        }}</span>
                      <span v-else>{{ scope.row[column.field] }}</span>
                    </template>
                  </el-table-column>
                </template>
              </el-table>
              <pagination
                v-show="total5 > 0"
                :limit.sync="queryParams.pageSize"
                :page.sync="queryParams.pageNum"
                :total="total5"
                @pagination="handleQuery1"
              />
            </div>
          </el-col>
        </el-row>
      </el-tab-pane>
    </el-tabs>
    <el-dialog
      v-if="openDeduction"
      :visible.sync="openDeduction"
      append-to-body
      modal-append-to-body
      title="扣款信息"
      width="500px"
    >
      <add-deduction
        :row-data="deduction"
        :designId="rowData.id"
        @close="modelClose"
      ></add-deduction>
    </el-dialog>
    <el-dialog
      v-if="openCost"
      :visible.sync="openCost"
      append-to-body
      modal-append-to-body
      title="计量调整"
      width="800px"
    >
      <add-cost
        :row-data="cost"
        :designId="rowData.id"
        @close="modelClose"
        :maiSecId="rowData.maiSecId"
      ></add-cost>
    </el-dialog>
    <el-dialog
      title="附件列表"
      :visible.sync="openFile"
      width="500px"
      v-if="openFile"
      append-to-body
      modal-append-to-body
    >
      <file-upload
        @close="openFile=false"
        v-model="disFilePath"
        :forView="true"
      ></file-upload>
    </el-dialog>
  </div>
</template>
<script>
import {
  middleListBySid,
  deleteMiddle,
  fetchDeductionList,
  deleteDeduction,
  fetchFundAdjustList,
  deleteFundAdjust,
} from "@/api/calculate/calcProjectDesign/designApplication";
import AddDeduction from "../designApplication/addDeduction.vue";
import AddCost from "../designApplication/addCost.vue";
import RoadSection from "@/views/baseData/components/roadSection/index.vue";
import RouteCodeSection from "@/components/RouteCodeSection/index.vue";

export default {
  components: {
    RouteCodeSection,
    RoadSection,
    AddDeduction,
    AddCost,
    RenderDom: {
      functional: true,
      props: {
        row: Object,
        index: Number,
        render: Function,
      },
      render(createElement, ctx) {
        const { row, index } = ctx.props;
        return ctx.props.render(row, index);
      },
    },
  },
  dicts: [
    "deduction_type",
    "adjustment_type",
    "project_type",
    "project_clac_settlement_status",
  ],
  data() {
    return {
      queryParams: {
        pageNum: 1,
        pageSize: 10,
      },
      loading: false,
      tableData1: [],
      total1: 0,
      columns1: [
        {
          key: 0,
          width: 120,
          field: "name",
          label: `结算计量名称`,
          visible: true,
        },
        {
          key: 1,
          width: 120,
          field: "code",
          label: `结算计量编号`,
          visible: true,
        },
        {
          key: 2,
          field: "domainName",
          label: `管养单位`,
          visible: true,
        },
        {
          key: 3,
          width: 120,
          field: "calcDomainName",
          label: `申请计量单位`,
          visible: true,
        },
        {
          key: 4,
          width: 100,
          field: "maiSecId",
          label: `路段名称`,
          visible: true,
        },
        {
          key: 5,
          width: 100,
          field: "conName",
          label: `合同名称`,
          visible: true,
        },
        {
          key: 6,
          width: 100,
          field: "calcFund",
          label: `计量资金(元)`,
          visible: true,
        },
        {
          key: 7,
          width: 120,
          field: "productionFund",
          label: `安全生产费(元)`,
          visible: true,
        },
        {
          key: 8,
          width: 120,
          field: "guaranteeFund",
          label: `安全保通费(元)`,
          visible: true,
        },
        {
          key: 9,
          width: 100,
          field: "designFund",
          label: `设计费(元)`,
          visible: true,
        },
        {
          key: 39,
          width: 100,
          field: "adjustFund",
          label: `调整费用(元)`,
          visible: true,
        },
        {
          key: 40,
          width: 130,
          field: "adjustAfterFund",
          label: `调整后设计费(元)`,
          visible: true,
        },
        {
          key: 10,
          width: 100,
          field: "calcDate",
          label: `计量日期`,
          visible: true,
        },
        {
          key: 11,
          width: 100,
          field: "createTime",
          label: `操作日期`,
          visible: true,
        },
        {
          key: 12,
          width: 100,
          field: "field12",
          label: `操作意见`,
          visible: true,
        },
        { key: 13, width: 100, field: "remark", label: `备注`, visible: true },
        {
          key: 14,
          width: 100,
          field: "status",
          label: `状态`,
          visible: true,
          dict: "project_clac_settlement_status",
        },
      ],
      table1MultipleSelection: [],
      tableData4: [],
      total4: 0,
      columns4: [

        {
          key: 1,
          width: 100,
          field: "mtype",
          label: `扣款类型`,
          visible: true,
          dict: "deduction_type",
        },
        { key: 2, width: 100, field: "fund", label: `扣款金额`, visible: true },

        {
          key: 3,
          width: 100,
          field: "remark",
          label: `扣款描述`,
          visible: true,
        },
        {
          key: 4,
          width: 100,
          field: "fileId",
          label: `附件`,
          visible: true,
          slots: true,
          render: (row, index) => {
            return (
              <el-button
                size="mini"
                disabled={!row.fileId}
                type="text"
                onClick={(e) => this.handleOpenFile(e, row)}
              >
                查看
              </el-button>
            );
          },
        },
        {
          key: 5,
          width: 100,
          field: "fileId",
          label: `操作`,
          visible: true,
          fixed: "right",
          slots: true,
          render: (row, index) => {
            return (
              <div>
                <el-button
                  size="mini"
                  type="text"
                  v-has-menu-permi={['calcprojectdes:deduction:edit']}
                  onClick={(e) => this.handleOpenEdit(row, "4")}
                >
                  编辑
                </el-button>
                <el-button
                  size="mini"
                  type="text"
                  v-has-menu-permi={['calcprojectdes:deduction:remove']}
                  onClick={(e) => this.handleDeleteOther(row, "4")}
                >
                  删除
                </el-button>
              </div>
            );
          },
        },
      ],
      tableData5: [],
      total5: 0,
      columns5: [
        {
          key: 1,
          width: 100,
          field: "settleCode",
          label: `施工计量单编码`,
          visible: true,
        },
        {
          key: 2,
          width: 100,
          field: "settleName",
          label: `施工计量单名称`,
          visible: true,
        },
        {
          key: 3,
          width: 100,
          field: "dataType",
          label: `费用类型`,
          visible: true,
          dict: "project_type",
        },
        { key: 4, width: 100, field: "amount", label: `调整金额`, visible: true },
        { key: 5, width: 100, field: "remark", label: `备注`, visible: true },
        {
          key: 6,
          width: 100,
          field: "updateuser",
          label: `操作人`,
          visible: true,
        },
        {
          key: 7,
          width: 100,
          field: "createTime",
          label: `操作时间`,
          visible: true,
        },
        {
          key: 8,
          width: 150,
          field: "fileId",
          label: `操作`,
          visible: true,
          fixed: "right",
          slots: true,
          render: (row, index) => {
            return (
              <div>
                <el-button
                  size="mini"
                  type="text"
                  v-has-menu-permi={['calcprojectdes:fundadjust:edit']}
                  onClick={(e) => this.handleOpenEdit(row, "5")}
                >
                  编辑
                </el-button>
                <el-button
                  size="mini"
                  type="text"
                  v-has-menu-permi={['calcprojectdes:fundadjust:remove']}
                  onClick={(e) => this.handleDeleteOther(row, "5")}
                >
                  删除
                </el-button>
              </div>
            );
          },
        },
      ],
      openDeduction: false,
      deduction: {},
      openCost: false,
      cost: {},
      disFilePath: '',
      openFile: false
    };
  },
  props: {
    rowData: {
      type: Object,
      default: () => {},
    },
    maiSecId: {
      type: String,
      default: ''
    }
  },
  created() {
    this.handleQuery1();
    this.handleQuery4();
    this.handleQuery5();
  },
  methods: {
    handleQuery1() {
      this.loading = true;
      const paramsData = {
        designId: this.rowData.id,
        pageSize: this.queryParams.pageSize,
        pageNum: this.queryParams.pageNum,
      };
      if (this.queryParams.settleCode) {
        paramsData.settleCode = this.queryParams.settleCode;
      }
      if (
        this.queryParams.operatorDate &&
        this.queryParams.operatorDate.length === 2
      ) {
        paramsData.operatorStartDate = this.queryParams.operatorDate[0];
        paramsData.operatorEndDate = this.queryParams.operatorDate[1];
      }

      middleListBySid(paramsData).then((res) => {
        this.tableData1 = res.rows;
        this.total1 = res.total;
        this.loading = false;
      });
    },
    handleQuery4() {
      this.loading = true;
      this.queryParams.designId = this.rowData.id;
      fetchDeductionList(this.queryParams).then((res) => {
        this.tableData4 = res.rows;
        this.total4 = res.total;
        this.loading = false;
      });
    },
    handleQuery5() {
      this.loading = true;
      this.queryParams.designId = this.rowData.id;
      fetchFundAdjustList(this.queryParams).then((res) => {
        this.tableData5 = res.rows;
        this.total5 = res.total;
        this.loading = false;
      });
    },
    table1SelectionChange(val) {
      this.table1MultipleSelection = val;
    },
    handleDelete() {
      if (this.table1MultipleSelection.length <= 0) {
        this.$modal.msgError("请勾选需要删除的数据");
        return;
      }
      const idList = this.table1MultipleSelection.map((item) => {
        return item.detailId;
      });
      this.$modal.confirm("是否确认删除").then(() => {
        deleteMiddle({idList}).then(() => {
          this.$modal.msgSuccess("删除成功");
          this.handleQuery1();
        });
      });
    },

    exportList1() {
      this.download(
        "manager/project/designcalc/detail/export",
        { designId: this.rowData.id,},
        `designcalc_detail_${new Date().getTime()}.xlsx`,
        {
          headers: { "Content-Type": "application/json;" },
          parameterType: "body",
        }
      );
    },

    exportList4() {
      this.download(
        "manager/project/designcalc/deduction/export",
        { ...this.queryParams },
        `method_deduction_${new Date().getTime()}.xlsx`,
        {
          headers: { "Content-Type": "application/json;" },
          parameterType: "body",
        }
      );
    },

    resetQuery() {
      this.queryParams = {
        pageNum: 1,
        pageSize: 10,
      };
    },
    modelClose(index) {
      this.openDeduction = false;
      this.openCost = false;
      switch (index) {
        case "4":
          this.handleQuery4();
          break;
        case "5":
          this.handleQuery5();
          break;
      }
    },
    handleDeleteOther(row, index) {
      this.$modal.confirm("是否确认删除").then(() => {
        switch (index) {
          case "4":
            deleteDeduction(row.id).then(() => {
              this.$modal.msgSuccess("删除成功");
              this.handleQuery4();
            });
            break;
          case "5":
            deleteFundAdjust(row.id).then(() => {
              this.$modal.msgSuccess("删除成功");
              this.handleQuery5();
            });
            break;
        }
      });
    },
    handleOpenEdit(row, index) {
      switch (index) {
        case "4":
          this.deduction = row;
          this.openDeduction = true;
          break;
        case "5":
          this.cost = row;
          this.openCost = true;
          this.handleQuery5();
          break;
      }
    },
    handleOpenFile(e, row) {
      this.disFilePath = "";
      this.disFilePath = row.fileId;
      this.openFile = true;
    },
  },
};
</script>
<style lang="scss" scoped></style>
<style lang="scss" scoped>
@import "@/assets/styles/business.scss";
</style>
