<template>

  <div
    class="route-road"
    v-loading="loading"
  >
    <!-- <div
      @click="changeEditType"
      style="height: 28px;"
    >
      <div
        :disabled="disabled"
        :style="disabled?'background-color: #F5F7FA;border-color:#dfe4ed;color:#C0C4CC;':'background-color: white;'"
        class="inputStyle"
      >{{title}}</div>
    </div> -->
    <el-cascader
      v-if="editType"
      :key="key"
      @change="changeSelect"
      ref="cascader"
      :disabled="disabled"
      filterable
      style="width: 100%;"
      clearable
      v-model="selectValue"
      :placeholder="placeholder"
      :props="{lazy:true,lazyLoad}"
    >
    </el-cascader>
    <div
      v-else
      class="click-input"
      @click="changeEditType"
    >
      <el-input
        :disabled="disabled"
        v-model="title"
        readonly
      />
    </div>
  </div>
</template>

<script>
import { getDicts } from '@/api/system/dict/data'
import { segmentsList } from '@/api/baseData/tunnel/baseInfo/getSections'
export default {
  // 路段选择器
  name: 'SectionSelect',
  components: {},
  props: {
    value: {
      type: [String, Array],
      default: ''
    },
    placeholder: {
      type: String,
      default: '请选择路段类型'
    },
    size: {
      type: String,
      default: 'medium'
    },
    disabled: {
      type: Boolean,
      default: false
    },
    sectionId: {
      default: ''
    },
    selectValueName: {
      type: String,
      default: ''
    },
    formObject: {
      type: Object,
      default: {}
    }
  },
  data() {
    return {
      loading: false,
      routeOptions: [],

      key: 0,
      id: 0,
      editType: false
    }
  },

  computed: {
    title: {
      get: function() {
        let formObject = this.formObject
        let name = ''
        if (formObject['sectionTypeName']) {
          name =
            formObject['sectionTypeName'] +
            (formObject['belongConnectingLineName']
              ? '/' + formObject['belongConnectingLineName']
              : formObject['belongRampName']
              ? '/' + formObject['belongRampName']
              : '')
        } else if (this.selectValueName) {
          name = this.selectValueName
        } else {
          name = '请选择路段类型'
        }

        return name
      },
      set: function(val) {
        this.$emit('input', val)
      }
    },
    formData: {
      get: function(val) {
        return this.formObject
      }
    },
    selectValue: {
      get: function(val) {
        this.$emit('change', val)
      },
      set: function(val) {
        this.$emit('input', val)
      }
    }
  },
  created() {},
  mounted() {},
  methods: {
    changeEditType() {
      this.editType = true
    },
    lazyLoad(node, resolve) {
      const { level } = node
      setTimeout(() => {
        if (level === 0) {
          getDicts('sys_route_type')
            .then(res => {
              if (res.code === 200) {
                let nodes=[];
                res.data.map(item => {
                  segmentsList({
                    maintenanceSectionId: this.sectionId,
                    roadType:item.dictValue
                  })
                    .then(res2 => {
                      if (res2.code === 200) {
                        nodes.push({
                          value: item.dictValue,
                          label: item.dictLabel,
                          leaf: res2.data.length<1||item.dictLabel == '主线'
                        })

                        if(nodes.length==3){
                          console.log(nodes)
                          resolve(nodes)
                        }
                      }
                    })
                })
                
              }
            })
            .finally(() => {
              this.loading = false
            })
        } else if (node.label !== '主线' && level === 1) {
          segmentsList({
            maintenanceSectionId: this.sectionId,
            roadType: node.value
          })
            .then(res => {
              if (res.code === 200) {
                const nodes = res?.data?.map(item => ({
                  value: item.routeSegmentsId,
                  label: item.routeSegmentsName,
                  leaf: true
                }))
                resolve(nodes)
              }
            })
            .finally(() => {
              this.loading = false
            })
        }
      }, 1000)
    },

    changeSelect(val) {
      this.formData.sectionType = val[0]
      console.log(val,'val')
      if (val[0] == '1' && val.length > 1) {
        console.log(val)
        this.formData.belongConnectingLineId = val[1]
        this.formData.belongRampId = ''
      } else if (val[0] == '2' && val.length > 1) {
        this.formData.belongConnectingLineId = ''
        this.formData.belongRampId = val[1]
      } else {
        this.formData.belongConnectingLineId = ''
        this.formData.belongRampId = ''
      }

      this.$emit('update:formObject', this.formData)
    }
  },
  watch: {
    sectionId: function(val) {
      this.selectValue = ''
      if (this.$refs?.cascader?.$refs) {
        this.$refs.cascader.$refs.panel.checkedValue = []
        this.$refs.cascader.$refs.panel.activePath = []
        this.$refs.cascader.$refs.panel.store.nodes = []
        this.$refs.cascader.$refs.panel.syncActivePath()
        this.$refs.cascader.$refs.panel.clearCheckedNodes()
        this.key++
      }
    }
  }
}
</script>



<style lang="scss" scoped>
.inputStyle {
  color: black;
  border: 1px solid #dcdfe6;
  border-radius: 5px;
  padding-left: 15px;
  min-height: 28px;
  font-size: 12px;
  background: red;
}
.click-input ::v-deep .el-input .el-input__inner {
  cursor: pointer;
}
</style>