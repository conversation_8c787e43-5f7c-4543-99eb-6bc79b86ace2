<template>
  <div style="width: 100%">
    <el-col :span="12">
      <el-form-item
        label="养护单位"
        :rules="[
          { required: true, message: '请输入养护单位', trigger: 'blur' },
        ]"
      >
        <el-select
          :disabled="disabled"
          class="main-select-tree"
          ref="selectTree"
          v-model="formData.managementMaintenanceId"
          style="width: 100%"
          :size="size"
          :placeholder="placeholder"
          :clearable="clearable"
          filterable
          :filter-method="filterMethod"
          @clear="handleClear"
        >
          <el-option
            v-for="item in formatData(datas)"
            :key="item.value"
            :label="item.label"
            :value="item.value"
            style="display: none"
          />
          <el-tree
            class="main-select-el-tree"
            ref="selecteltree"
            :data="datas"
            node-key="id"
            highlight-current
            :props="defaultProps"
            @node-click="handleNodeClick"
            :current-node-key="formData.managementMaintenanceId"
            :expand-on-click-node="expandOnClickNode"
            :default-expand-all="expandAll"
            :render-after-expand="false"
          />
        </el-select>
      </el-form-item>
    </el-col>
    <el-col :span="12">
      <el-form-item
        label="管养分处"
        :rules="[
          { required: true, message: '请输入管养分处', trigger: 'blur' },
        ]"
      >
        <el-select
          :disabled="disabled"
          class="main-select-tree"
          ref="selectTree2"
          v-model="formData.managementMaintenanceBranchId"
          style="width: 100%"
          :size="size"
          :placeholder="placeholder"
          :clearable="clearable"
          filterable
          :filter-method="filterMethod"
          @clear="handleClear"
        >
          <el-option
            v-for="item in formatData(datas2)"
            :key="item.value"
            :label="item.label"
            :value="item.value"
            style="display: none"
          />
          <el-tree
            class="main-select-el-tree"
            ref="selecteltree"
            :data="datas2"
            node-key="id"
            highlight-current
            :props="defaultProps"
            @node-click="handleChilNodeClick"
            :current-node-key="formData.managementMaintenanceBranchId"
            :expand-on-click-node="expandOnClickNode"
            :default-expand-all="expandAll"
            :render-after-expand="false"
          />
        </el-select>
      </el-form-item>
    </el-col>
  </div>
</template>

<script>
import { deptTreeSelect } from "@/api/tmpl";

export default {
  props: {
    value: {
      type: String,
      default: "",
    },
    formObject: {
      type: Object,
      default: {},
    },

    deptTypeList: {
      type: Array,
      default: null,
    },
    size: {
      type: String,
      default: "",
    },
    placeholder: {
      type: String,
      default: "",
    },
    disabled: {
      type: Boolean,
      default: false,
    },
    clearable: {
      type: Boolean,
      default: false,
    },
    expandAll: {
      type: Boolean,
      default: true,
    },
    onlySelectChild: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      // value: 6,
      expandOnClickNode: true,
      options: [],
      datas2: [],
      ans: "",
      datas: [
        {
          id: 1,
          label: "云南",
          children: [
            {
              id: 2,
              label: "昆明",
              children: [
                {
                  id: 3,
                  label: "五华区",
                  children: [{ id: 8, label: "北辰小区" }],
                },
                { id: 4, label: "盘龙区" },
              ],
            },
          ],
        },
        {
          id: 5,
          label: "湖南",
          children: [
            { id: 6, label: "长沙" },
            { id: 7, label: "永州" },
          ],
        },
      ],
      defaultProps: {
        children: "children",
        label: "label",
      },
    };
  },
  created() {
    
  },
  mounted() {
    this.getDept();
  },
  computed: {
    formData: {
      get: function (val) {
        if(this.formObject.managementMaintenanceId){
          
          this.getDept();
        }
        return this.formObject;
        
      },
    },
  },
  methods: {
    getDept() {
      var deptType = { types: 201, deptTypeList: this.deptTypeList };
      deptTreeSelect(deptType).then((response) => {
        this.datas = response.data;
       
        if (this.formObject.managementMaintenanceId) {
          this.getDept2(this.formObject.managementMaintenanceId);
        }
      });
    },
    getDept2(node) {
      var deptType = { types: 100, deptTypeList: [3, 4] };
      deptTreeSelect(deptType).then((response) => {
        let children = response.data[0].children;
        let data
        if (node.id) {
           data = children.filter((item) => item.id == node.id);
        } else {
           data = children.filter((item) => item.id == node);
        }
        this.datas2 = data;
      });
    },

    handleChilNodeClick(node) {
      this.formData.managementMaintenanceBranchId = node.id;
      this.$forceUpdate();
      this.$emit("update:formObject", this.formData);
      this.$refs.selectTree2.blur();
    },
    handleClear() {
      this.$emit("input", "");
    },
    // 四级菜单
    formatData(data) {
      let options = [];
      data.forEach((item, key) => {
        options.push({ label: item.label, value: item.id });
        if (item.children) {
          item.children.forEach((items, keys) => {
            options.push({ label: items.label, value: items.id });
            if (items.children) {
              items.children.forEach((itemss, keyss) => {
                options.push({ label: itemss.label, value: itemss.id });
                if (itemss.children) {
                  itemss.children.forEach((itemsss, keysss) => {
                    options.push({ label: itemsss.label, value: itemsss.id });
                  });
                }
              });
            }
          });
        }
      });
      return options;
    },
    filterMethod(e) {
      this.$refs.selecteltree.filter(e);
      return true;
    },
    // filterNode(value, data) {
    //   if (!value) return true;
    //   return data.label.indexOf(value) !== -1;
    // },
    handleNodeClick(node) {
      this.formData.managementMaintenanceBranchId = "";
      this.getDept2(node);
      this.formData.managementMaintenanceId = node.id;
      this.$forceUpdate();
      this.$emit("update:formObject", this.formData);
      this.$refs.selectTree.blur();

      // if (this.onlySelectChild) {
      //   if (!node.children ||node.children.length == 0) {
      //     this.$emit('input', node.id)
      //     this.$refs.selectTree.blur();
      //   }
      // } else {
      //   this.$emit('input', node.id)
      //   this.$refs.selectTree.blur();
      // }
    },
  },
};
</script>
<style>
.main-select-el-tree .el-tree-node .is-current > .el-tree-node__content {
  font-weight: bold;
  color: #409eff;
}
.main-select-el-tree .el-tree-node.is-current > .el-tree-node__content {
  font-weight: bold;
  color: #409eff;
}
</style>
