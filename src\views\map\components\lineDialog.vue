<template>
  <div
    class="line-dialog"
    :style="{
      width: isBig ? width * 2 + 'px' : width + 'px',
      height: isBig ? height * 2 + 'px' : height + 'px',
      top: toTop,
      left: toLeft,
    }"
    ref="moveElRef"
    v-if="showVisible"
    v-drag
  >
    <div class="dialog-title">
      <!-- <draggable class="move">
        <div ref="dargRef">
          <span>路线信息</span>
        </div>
      </draggable> -->
      <div class="move">
        <span>路线信息</span>
        <el-button
          :style="isBig ? 'margin-left: 40px; padding: 10px 14px' : 'margin-left: 20px; padding: 5px 7px'"
          :class="isAnimation ? 'btn btn-active' : 'btn'"
          type="primary"
          @click="showAnimation"
          >动画模拟</el-button
        >
      </div>
      <i class="el-icon-close close" @click="close"></i>
    </div>
    <div class="dialog-body">
      <el-row :gutter="5" style="height: 100%">
        <el-col :span="6" :offset="0" style="height: 100%">
          <div class="tree-content">
            <el-tree
              ref="treeRef"
              :data="treeData"
              :props="{ label: 'name', children: 'childs' }"
              :highlight-current="true"
              @node-click="onNodeClick"
              :default-checked-keys="defaultCheckedKeys"
              :default-expanded-keys="defaultExpandedKeys"
              node-key="id"
            >
              <div class="el-tree-node" slot-scope="{ data, node }">
                <span class="custom-tree-node">
                  <i
                    :class="
                      node.expanded
                        ? 'el-icon-arrow-down'
                        : 'el-icon-arrow-right'
                    "
                    v-if="data.childs && data.childs.length > 0"
                    :style="isBig ? 'font-size: 32px': 'font-size: 16px'"
                  ></i>
                  <i
                    class="ml5"
                    :class="
                      data.childs && data.childs.length > 0
                        ? 'el-icon-folder-opened'
                        : 'el-icon-document-checked'
                    "
                    :style="isBig ? 'font-size: 32px': 'font-size: 16px'"
                  ></i>
                  <span :style="isBig ? 'font-size: 32px': 'font-size: 16px'" class="node-name ml5" :title="data.name">
                    {{ data.name || data.lxmc || data.lxbh || data.zhfw }}
                  </span>
                </span>
              </div>
            </el-tree>
          </div>
        </el-col>
        <el-col :span="18" :offset="0">
          <section class="line-info">
            <el-row :gutter="0">
              <el-col :span="12" :offset="0">
                <div
                  class="tab-item"
                  :class="tabIndex === 0 ? 'tab-item-act' : ''"
                  @click="onTabClick(0)"
                >
                  基本信息
                </div>
              </el-col>
              <el-col :span="12" :offset="0">
                <div
                  class="tab-item"
                  :class="tabIndex === 1 ? 'tab-item-act' : ''"
                  @click="onTabClick(1)"
                >
                  养护记录
                </div>
              </el-col>
            </el-row>

            <div
              v-if="tabIndex === 0"
              class="mt5"
              style="overflow: auto"
              :style="{ height: isBig ? (height * 2 - 200) + 'px' : (height - 105) + 'px' }"
            >
              <el-descriptions :column="2" border>
                <el-descriptions-item
                  v-for="item in infoList"
                  :key="item.value"
                >
                  <template slot="label">
                    {{ item.label }}
                  </template>
                  <span>{{ info[item.value] }}{{item.value=='lxcd'?'km':''}}</span>
                </el-descriptions-item>
              </el-descriptions>
            </div>
            <div v-else class="mt5">
              <el-table
                :data="mRecord"
                border
                :height="isBig ? (height * 2 - 200) : (height - 105)"
                size="mini"
              >
                <el-table-column
                  type="index"
                  label="序号"
                  :width="isBig ? 120 : 60"
                  align="center"
                />
                <!-- <el-table-column
                  prop="regionCode"
                  label="政区编码"
                  width="180"
                /> -->
                <el-table-column
                  prop="routeCode"
                  label="路线明细编码"
                  :width="isBig ? 240 : 120"
                  align="center"
                />
                <el-table-column
                  prop="qdmc"
                  label="起点名称"
                  :width="isBig ? 320 : 180"
                  align="center"
                  show-overflow-tooltip
                />
                <el-table-column
                  prop="zdmc"
                  label="终点名称"
                  :width="isBig ? 320 : 180"
                  align="center"
                  show-overflow-tooltip
                />
                <el-table-column
                  prop="startStake"
                  label="起点桩号(km)"
                  :width="isBig ? 320 : 180"
                  align="center"
                >
                  <template slot-scope="{ row }">
                    {{ row.startStake | formatNumber }}
                  </template>
                </el-table-column>
                <el-table-column
                  prop="endStake"
                  label="终点桩号(km)"
                  :width="isBig ? 320 : 180"
                  align="center"
                >
                  <template slot-scope="{ row }">
                    {{ row.endStake | formatNumber }}
                  </template>
                </el-table-column>
                <el-table-column
                  prop="lane"
                  label="车道数量"
                  :width="isBig ? 200 : 100"
                  align="center"
                />
                <el-table-column
                  prop="measure"
                  label="养护措施"
                  :min-width="isBig ? 320 : 180"
                  show-overflow-tooltip
                />
              </el-table>

              <el-pagination
                :current-page="pageNo"
                :page-sizes="[10, 20, 50, 100]"
                :page-size="pageSize"
                layout="prev, pager, next, sizes"
                :total="total"
                @current-change="onPageChange"
                :pager-count="5"
                @size-change="onSizeChange"
              >
              </el-pagination>
            </div>
          </section>
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<script>
import draggable from 'vuedraggable';
import { getRouteInfo, getMRecord } from '@/api/oneMap/route';
import { Polygon, Circle } from 'ol/geom';
import { WKT } from 'ol/format';
import { Feature } from 'ol';
import { addClickFeature, featureToWkt, removeLayer } from './common/mapFun';
import { fromLonLat, toLonLat, transform } from 'ol/proj';
import { isBigScreen } from './common/util';

export default {
  name: 'lineDialog',
  components: {
    draggable,
  },
  data() {
    return {
      toTop: '0px',
      toLeft: '0px',
      treeData: [],
      info: {}, // 基础信息
      infoList: [
        {
          label: '路线编码：',
          value: 'lxbh',
        },
        {
          label: '路线名称：',
          value: 'lxmc',
        },
        {
          label: '起点名称：',
          value: 'qdmc',
        },
        {
          label: '终点名称：',
          value: 'zdmc',
        },
        {
          label: '起点桩号：',
          value: 'qdzh',
        },
        {
          label: '终点桩号：',
          value: 'zdzh',
        },
        {
          label: '路线长度：',
          value: 'lxcd',
        },
        {
          label: '路面类型：',
          value: 'ldlmlxName',
        },
        {
          label: '养护等级：',
          value: 'yhdjName',
        },
        {
          label: '路线性质：',
          value: 'yhdjName',
        },
        {
          label: '建设时间：',
          value: 'jcsj',
        },
        {
          label: '设计年限(年)：',
          value: 'sjnx',
        },
      ], // 基础信息列表
      mRecord: [], // 养护记录
      tabIndex: 0, // tab索引
      pageNo: 1,
      pageSize: 10,
      total: 0,
      defaultCheckedKeys: [], // 选中的节点
      defaultExpandedKeys: [], // 展开的节点
      size: 'mini',
      isBig: isBigScreen(),
      isAnimation: false,
    };
  },
  props: {
    show: {
      type: Boolean,
      default: false,
    },
    width: {
      type: [String, Number],
      default: 900,
    },
    height: {
      type: [String, Number],
      default: 300,
    },
    top: {
      type: [String, Number],
      default: '15vh',
    },
    left: {
      type: [String, Number],
      default: '55%',
    },
    isDrag: {
      type: Boolean,
      default: true,
    },
    id: {
      type: [String, Number, null],
      default: '',
    },
    coordinate: {
      type: [Array, Object, String],
      default: () => [],
    },
  },
  computed: {
    showVisible: {
      get(val) {
        return this.show;
      },
      set(val) {
        this.$emit('update:show', val);
      },
    },
  },
  filters: {
    formatNumber(val) {
      if (!val) return '';
      // Convert to km+ format (e.g. 1234.56 -> K1+234.56)
      if (typeof val === 'number') {
        const km = Math.floor(val / 1000);
        const m = val % 1000;
        return `K${km}+${m.toFixed(2)}`;
      }
      return val.toFixed(2);
    },
  },
  // 自定义指令 实现可拖动
  directives: {
    drag(el, bindings) {
      el.onmousedown = (e) => {
        var disx = e.pageX - el.offsetLeft;
        var disy = e.pageY - el.offsetTop;
        document.onmousemove = (e) => {
          el.style.left = e.pageX - disx + 'px';
          el.style.top = e.pageY - disy + 'px';
        };
        document.onmouseup = () => {
          document.onmousemove = document.onmouseup = null;
        };
      };
    },
  },
  watch: {
    top: {
      immediate: true,
      handler(newVal) {
        this.toTop = newVal;
      },
      deep: true,
    },
    left: {
      immediate: true,
      handler(newVal) {
        this.toLeft = newVal;
      },
      deep: true,
    },
    id: {
      immediate: true,
      handler(newVal) {
        if (newVal) {
          this.isAnimation = false;
          this.getCurrentId();
          // 获取养护记录
          this.getMRecord();
          this.$emit('showAnimation', {}, false);
        }
      },
      deep: true,
    },
  },
  mounted() {
    window.$Bus.$on('closeBtn', () => {
      this.isAnimation = false;
    });
  },
  methods: {
    close() {
      this.$emit('update:show', false);
      removeLayer(window.mapLayer, 'clickLayer');
      this.isAnimation = false;
      this.$emit('showAnimation', this.info, false);
    },
    // 获取当前Id数据
    getCurrentId() {
      this.size = this.$store.getters.size;
      let wkt = this.getPlogon();
      let lonlat = toLonLat(this.coordinate);
      let [x, y] = lonlat;
      this.$modal.loading();
      getRouteInfo({ wkt, x, y }, {})
        .then((res) => {
          if (res) {
            this.treeData = res;
            this.formatTreeData(this.treeData);
            let arr = JSON.parse(JSON.stringify(this.treeData));
            let result = this.getLastChild(arr);
            if (result && result.length > 0) {
              this.info = result[0];
            }
            // 展开树节点
            this.defaultExpandedKeys = [this.info.id];
            this.defaultCheckedKeys = [this.info.id || this.treeData[0].id];
            // element ui tree 回显默认选中
            this.$nextTick(() => {
              this.$refs.treeRef.setCurrentKey(this.info.id);
              this.$forceUpdate();
            });

            // 获取wkt
            let shape = this.info.shape;
            if (!shape) return;
            let feature = new WKT().readFeature(shape, {
              dataProjection: 'EPSG:4326',
              featureProjection: 'EPSG:3857',
            });
            // 先从地图移除
            removeLayer(window.mapLayer, 'clickLayer');
            // 添加到地图
            addClickFeature(feature, {}, null, true, true, 'clickLayer', 1);
            // 移动到地图
            window.mapLayer.getView().fit(feature.getGeometry().getExtent(), {
              duration: 500,
            });
          }
        })
        .finally(() => {
          this.$modal.closeLoading();
        });
    },
    // 获取养护记录
    getMRecord(id = null) {
      this.$modal.loading();
      getMRecord({
        gisRouteId: id || this.id,
        pageNum: this.pageNo,
        pageSize: this.pageSize,
      })
        .then((res) => {
          if (res && res.code === 200) {
            this.mRecord = res.rows;
            this.total = res.total;
          }
        })
        .finally(() => {
          this.$modal.closeLoading();
        });
    },
    // 重组数据
    formatTreeData(arr) {
      // 递归遍历子节点
      arr.forEach((item) => {
        if (item.childs && item.childs.length > 0) {
          item.name = item.ldlmlxName || item.lxmc || item.lxbh;
          this.formatTreeData(item.childs);
        } else {
          item.name = item.zhfw || item.lxmc;
        }
      });
    },
    // 获取数组对象 childs 最后一级的数据
    getLastChild(arr) {
      const result = [];
      arr.forEach((item) => {
        if (item.childs && item.childs.length > 0) {
          result.push(...this.getLastChild(item.childs));
        } else {
          result.push(item);
        }
      });
      return result;
    },
    // 获取坐标 并按100的半径绘面
    getPlogon() {
      // 定义中心点的经纬度坐标
      const center = this.coordinate;
      let lonLat = toLonLat(center);
      // 创建一个半径为 100 米的圆形面
      const radius = 1500; // 半径
      let [centerX, centerY] = center;
      // 自定义方法：获取近似的多边形边界（例如，使用 36 个点来近似一个圆）
      const numPoints = 56; // 使用 36 个点（或更多）来近似圆形
      const coordinates = [];
      for (let i = 0; i < numPoints; i++) {
        const angle = (i / numPoints) * 2 * Math.PI; // 计算角度
        const x = centerX + radius * Math.cos(angle); // 计算 x 坐标
        const y = centerY + radius * Math.sin(angle); // 计算 y 坐标
        coordinates.push([x, y]); // 将坐标添加到数组中
      }
      // 设置闭合
      coordinates[coordinates.length] = coordinates[0];
      let polygon = new Polygon([coordinates]);
      let shape = new WKT().writeGeometry(polygon);

      let feature4326 = new WKT().readFeature(shape, {
        dataProjection: 'EPSG:3857',
        featureProjection: 'EPSG:4326',
      });
      let shape4326 = featureToWkt(feature4326);
      let wkt = this.CreateSimpleCircleWKT(lonLat[0], lonLat[1], radius, 56);

      return shape4326;
      let feature = new WKT().readFeature(shape);
      addClickFeature(feature, {}, null, true, true, 'clickLayer', 1);
    },
    CreateSimpleCircleWKT(lng, lat, radius, pointCount) {
      var points = this.CreateSimpleCircle(lng, lat, radius, pointCount);
      var wkt = ['POLYGON(('];
      for (var i = 0; i < points.length; i++) {
        wkt.push(
          (i > 0 ? ',' : '') +
            +points[i][0].toFixed(6) +
            ' ' +
            +points[i][1].toFixed(6)
        );
      }
      wkt.push('))');
      return wkt.join('');
    },
    CreateSimpleCircle(lng, lat, radius, pointCount) {
      //球面坐标不会算，转换成三角坐标简单点，经度代表值大约：0.01≈1km 0.1≈10km 1≈100km 10≈1000km
      var km = radius / 1000;
      var a = km < 5 ? 0.01 : km < 50 ? 0.1 : km < 500 ? 1 : 10;
      var b = this.Distance(lng, lat, lng + a, lat);
      var c = this.Distance(lng, lat, lng, lat + a);
      var rb = (radius / b) * a;
      var rc = (radius / c) * a;
      var arr = [];
      var n = 0,
        step = 360.0 / pointCount,
        N = 360 - step / 2; //注意浮点数±0.000000001的差异
      for (var i = 0; n < N; i++, n += step) {
        var x = lng + rb * Math.cos((n * Math.PI) / 180);
        var y = lat + rc * Math.sin((n * Math.PI) / 180);
        arr[i] = [x, y];
      }
      arr.push([arr[0][0], arr[0][1]]); //闭环
      return arr;
    },
    Distance(lng1, lat1, lng2, lat2) {
      //采用Haversine formula算法，高德地图的js计算代码，比较简洁 https://www.cnblogs.com/ggz19/p/7551088.html
      var d = Math.PI / 180;
      var f = lat1 * d,
        h = lat2 * d;
      var i = lng2 * d - lng1 * d;
      var e =
        (1 - Math.cos(h - f) + (1 - Math.cos(i)) * Math.cos(f) * Math.cos(h)) /
        2;
      return 2 * 6378137 * Math.asin(Math.sqrt(e));
    },
    // 节点点击事件
    onNodeClick(data, node) {
      if (node && node.isLeaf) {
        this.info = data;
        if (this.isAnimation) {
          this.$emit('showAnimation', this.info, true);
        }
        this.getMRecord(this.info.id);
        // 获取wkt
        let shape = this.info.shape;
        if (!shape) return;
        let feature = new WKT().readFeature(shape, {
          dataProjection: 'EPSG:4326',
          featureProjection: 'EPSG:3857',
        });
        // 先从地图移除
        removeLayer(window.mapLayer, 'clickLayer');
        // 添加到地图
        addClickFeature(feature, {}, null, true, true, 'clickLayer', 1);
        // 移动到地图
        window.mapLayer.getView().fit(feature.getGeometry().getExtent(), {
          duration: 500,
        });
      }
    },
    // 切换tab
    onTabClick(index) {
      this.tabIndex = index;
    },
    showAnimation(visible) {
      this.isAnimation = !this.isAnimation;
      this.$emit('showAnimation', this.info, this.isAnimation);
    },
    // 分页
    onPageChange(page) {
      this.pageNo = page;
      this.getMRecord();
    },
    // 分页大小
    onSizeChange(size) {
      this.pageSize = size;
      this.getMRecord();
    },
  },
  beforeDestroy() {
    window.$Bus.$off('closeBtn');
  },
};
</script>

<style lang="scss" scoped>
@import "@/assets/styles/utils.scss";
$h: 300px;
$w: 230px;

.line-dialog {
  position: fixed;
  z-index: 1000;
  background: rgba(4, 17, 48, 0.85);
  border-radius: 10px;
  transform: translateX(-50%);
  border: 1px solid #0687ff;
  user-select: none;

  .dialog-title {
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 4vh;
    border-bottom: 1px solid #444;
    color: #ffffff;

    .move {
      display: flex;
      align-items: center;
      flex: 1;
      width: 100%;
      line-height: 4vh;
      cursor: url('~@/assets/map/cursor-move.png'), auto;
      user-select: none;
      /* 防止拖拽时选中文本 */

      span {
        font-size: vwpx(36px);
        padding-left: vwpx(20px);
      }

      .btn {
        font-size: vwpx(32px);
      }
      .btn-active {
        font-size: vwpx(32px);
        background-color: #ffbe27;
        border-color: #ffbe27;
      }
    }

    .close {
      margin-left: vwpx(30px);
      cursor: pointer;
      font-size: vwpx(38px);
      padding-right: vwpx(12px);

      &:hover {
        color: #0687ff;
      }
    }
  }

  .dialog-body {
    width: 100%;
    height: calc(100% - 4vh);

    .tree-content {
      width: vwpx(460px);
      height: 100%;
      padding: 0 0 vwpx(4px) 0;
      overflow: auto;

      &::-webkit-scrollbar {
        width: vwpx(12px);
        height: vwpx(12px);
      }

      &::-webkit-scrollbar-thumb {
        background-color: rgba(1, 102, 254, 0.3);
        border-radius: 2px;
      }

      &::-webkit-scrollbar-track {
        background-color: rgba(1, 102, 254, 0.2);
      }

      .el-tree {
        min-width: 260px;
        // max-width: $h;
        height: 100%;
      }

      ::v-deep .el-tree {
        background-color: transparent;
        border: none;
        color: #ffffff;

        /* 自定义样式，去除左侧三角形 */
        .el-tree-node__expand-icon {
          display: none;
        }

        .el-tree-node__content {
          height: vwpx(54px);
          &:hover {
            background: linear-gradient(
              90deg,
              rgba(0, 94, 255, 0.5) 0%,
              rgba(1, 102, 254, 0.05) 100%
            );
          }

          .el-tree-node__expand-icon {
            padding: 2px;
          }
        }

        // 鼠标点击时节点的背景颜色
        .el-tree-node:focus > .el-tree-node__content {
          background: linear-gradient(
            90deg,
            rgba(0, 94, 255, 0.5) 0%,
            rgba(1, 102, 254, 0.05) 100%
          );
        }

        // 鼠标失去焦点时节点背景的颜色
        .is-current > .el-tree-node__content:first-child {
          background: linear-gradient(
            90deg,
            rgba(0, 94, 255, 0.5) 0%,
            rgba(1, 102, 254, 0.05) 100%
          );
        }
      }
    }

    .line-info {
      margin: 4px 6px;
      // height: $h;
      overflow-y: hidden;

      .tab-item {
        background-color: rgba(6, 135, 255, 0.35);
        font-size: vwpx(32px);
        display: flex;
        align-items: center;
        justify-content: center;
        height: vwpx(56px);
        border-radius: 4px;
        cursor: pointer;
        color: rgba(255, 255, 255, 0.65);
      }

      .tab-item-act {
        background-color: rgba(0, 94, 255, 0.6);
        font-size: vwpx(30px);
        display: flex;
        align-items: center;
        justify-content: center;
        height: vwpx(56px);
        color: #ffffff;
      }

      ::v-deep .el-table {
        background: unset;
        border: unset;
        &::before {
          background-color: unset;
        }

        &::after {
          background-color: unset;
        }

        tr {
          background-color: unset;
        }

        tr:nth-child(even) {
          background: rgba(86, 145, 255, 0);
          color: #ffffff;
        }

        td {
          color: #ffffff;
        }

        td,
        th.is-leaf {
          border: 1px solid rgba(1, 102, 254, 0.4);
        }

        .el-table__row {
          height: vwpx(72px) !important;
          line-height: vwpx(72px) !important;
        }
        .el-table__header-wrapper tr th {
          background-color: rgba(1, 102, 254, 0.2);
          color: #ffffff !important;
          font-size: vwpx(28px);
          height: vwpx(72px) !important;
          line-height: vwpx(72px) !important;
        }

        tbody {
          background-color: unset;
          border: none;
          cursor: pointer;
        }

        .el-table__body tr:hover > td {
          background-color: unset;
        }

        .el-table__body tr.current-row > td {
          background-color: rgba(1, 102, 254, 0.2) !important;
        }

        .el-table__body tr.current-row:hover {
          background-color: rgba(1, 102, 254, 0.2);
        }

        .el-table__body-wrapper::-webkit-scrollbar-thumb {
          // border-radius: 2px !important; /* 设置滑块的圆角 */
          background-color: rgba(1, 102, 254, 0.3) !important;
          /* 设置滑块的背景色 */
        }

        .el-table__body-wrapper::-webkit-scrollbar-track {
          background-color: rgba(1, 102, 254, 0.2) !important;
          /* 设置轨道的背景色 */
        }
      }
    }

    ::v-deep .el-pagination {
      .el-pagination__total {
        color: #ffffff;
        font-size: vwpx(28px);
      }

      .btn-prev,
      .btn-next {
        background-color: rgba(255, 255, 255, 0.1);
        border: 1px solid rgba(1, 102, 254, 0.6);
        color: #ffffff;
        font-size: vwpx(28px);
      }

      .el-pager {
        li {
          background-color: rgba(255, 255, 255, 0.1);
          border: 1px solid rgba(1, 102, 254, 0.6);
          color: #ffffff;
          font-size: vwpx(28px);
        }

        li:not(.disabled).active {
          background-color: rgba(1, 102, 254, 0.8);
          border: 1px solid rgba(1, 102, 254, 0.6);
          color: #ffffff;
          font-size: vwpx(28px);
        }
      }

      .el-pagination .el-select .el-input .el-input__inner {
        background-color: unset;
      }
    }

    ::v-deep .el-descriptions {
      .el-descriptions--mini {
        font-size: vwpx(24px);
      }
      
      .el-descriptions__body {
        background-color: rgba(4, 17, 48, 0.6) !important;
        color: #ffffff;
      }

      .is-bordered .el-descriptions-item__cell {
        border: 1px solid rgba(1, 102, 254, 0.4);
      }

      .el-descriptions-item__label.is-bordered-label {
        background-color: rgba(4, 17, 48, 0.8) !important;
        color: #ffffff;
      }
    }
  }
}
::v-deep .el-table--mini {
  font-size: vwpx(24px);
}
</style>
