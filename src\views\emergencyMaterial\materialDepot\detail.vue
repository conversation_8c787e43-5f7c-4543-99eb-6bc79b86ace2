<template>
  <el-row style="display: flex">
    <el-col :span="15" class="left-form">
      <el-form
        ref="elForm"
        :model="formData"
        :rules="rules"
        size="medium"
        label-width="110px"
      >
        <el-col :span="12">
          <el-form-item label="物资库编码" prop="code">
            <el-input v-model="formData.code" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="物资库名称" prop="name">
            <el-input v-model="formData.name" clearable> </el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="管养单位" prop="domainId">
            <select-tree
              v-model="formData.domainId"
              placeholder="请选择管养单位"
              :dept-type="100"
              :expand-all="false"
              clearable
            ></select-tree>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="路线名称" prop="maiSecId">
            <RoadSection
              v-model="formData.maiSecId"
              :deptId="formData.domainId"
              @change="maiSecIdChange"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="路线编码" prop="routeCode">
            <route-code-section
              v-model="formData.routeCode"
              placeholder="路线编码"
              ref="routeRef"
             :maintenanceSectionId="formData.maiSecId"
             >
            </route-code-section>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="桩号位置" prop="milestr">
            <el-input v-model="formData.milestr" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="建筑结构" prop="struct">
            <el-select v-model="formData.struct">
              <el-option v-for="item in structList" :value="item" :label="item"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="原有或新建" prop="isNew">
            <el-select v-model="formData.isNew">
              <el-option label="新建" value="1"></el-option>
              <el-option label="原有" value="2"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="投入运营时间" prop="investOperationTime">
            <el-date-picker
              clearable
              v-model="formData.investOperationTime"
              type="date"
              value-format="yyyy-MM-dd"
              style="width: 100%"
            >
            </el-date-picker>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="经度" prop="lon">
            <el-input v-model="formData.lon" clearable> </el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="纬度" prop="lat">
            <el-input v-model="formData.lat" clearable> </el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="覆盖路段" prop="coverMaiSecName">
            <el-input v-model="formData.coverMaiSecName" clearable> </el-input>
            <RoadSection
              v-model="coverMaiSecName"
              :deptId="formData.domainId"
              @change="coverMaiSecNameChange"
              :multiple="true"
              style="position: absolute;top:0;left:0;width:100%;opacity: 0;"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="占地面积" prop="area">
            <el-input v-model="formData.area" clearable> </el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="建筑面积㎡" prop="buildArea">
            <el-input v-model="formData.buildArea"> </el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="管理人员">
            <el-input v-model="formData.person" />
            <!-- <el-cascader
              v-model="formData.person"
              :options="deptUserOptions"
              :props="cascaderProps"
              :show-all-levels="false"
              ref="deptUser"
              filterable
              clearable
            ></el-cascader> -->
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="联系电话" prop="telNumber">
            <el-input v-model="formData.telNumber" clearable> </el-input>
          </el-form-item>
        </el-col>

        <el-col :span="24">
          <el-form-item label="物资库介绍" prop="describe">
            <el-input type="textarea" v-model="formData.describe"> </el-input>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="备注" prop="remark">
            <el-input type="textarea" v-model="formData.remark"> </el-input>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="附件">
            <file-upload v-model="formData.fileId" :owner-id="formData.fileId"></file-upload>
          </el-form-item>
        </el-col>
        <el-col
          :span="24"
          style="text-align: right; padding-right: 7.5px; margin-top: 18px"
        >
          <el-button type="primary" @click="onSave">保 存</el-button>
          <el-button @click="onClose">退 出</el-button>
        </el-col>
      </el-form>
    </el-col>
    <el-col :span="9">
      <div class="tree-box">
        <el-tree
          ref="typeTree"
          :data="treeData"
          show-checkbox
          node-key="assetsId"
          :expand-on-click-node="false"
          :props="treeProps"
          :default-checked-keys="defaultCheckedKeys"
          :default-expanded-keys="defaultExpandedKeys"
        >
          <span class="custom-tree-node" slot-scope="{ node, data }">
            <span>{{
              !data.hasOwnProperty("materialDetailList")
                ? `${data.assetsName}(${data.unit})`
                : `${data.name}`
            }}</span>
            <el-input
              v-if="!data.hasOwnProperty('materialDetailList')"
              class="tree-ipt"
              v-model="data.stock"
            />
          </span>
        </el-tree>
      </div>
    </el-col>
  </el-row>
</template>
<script>
import SelectTree from "@/components/DeptTmpl/selectTree.vue";
import RoadSection from "@/views/baseData/components/roadSection/index.vue";1
import ContractSection from "@/components/ContractSection/index.vue";
import ConstructionSelect from "@/components/ConstructionSelect/index.vue";
import { getMaterialTypeList, addMaterialDepot,getDetailMaterialList, editMaterialDepot } from "@/api/emergencyMaterial/materialDepot";
import { getTreeStruct } from "@/api/tmpl";
import RouteCodeSection from "@/components/RouteCodeSection/index.vue";
export default {
  name: "index",
  components: { ConstructionSelect, ContractSection, RoadSection, SelectTree,RouteCodeSection },
  data() {
    return {
      formData: {
        isNew: '2'
      },
      loading: false,
      methodList: [],
      rules: {
        code: [
          { required: true, message: "请输入物资库编码", trigger: "change" },
        ],
        name: [
          { required: true, message: "请输入物资库名称", trigger: "change" },
        ],
        domainId: [
          { required: true, message: "请选择管养单位", trigger: "change" },
        ],
        maiSecId: [
          { required: true, message: "请选择线路", trigger: "change" },
        ],
        routeCode: [
          { required: true, message: "请选择路段编码", trigger: "change" },
        ],
        milestr: [{ required: true, message: "请输入桩号位置", trigger: "change" },],
        struct: [{ required: true, message: "请输入建筑结构", trigger: "change" }],
        isNew: [{ required: true, message: "请选择原有或新建", trigger: "change" },],
        investOperationTime: [{ required: true, message: "请选择投入运营时间", trigger: "change" },],
        coverMaiSecName: [{ required: true, message: "请选择覆盖路段", trigger: "blur" },],
      },
      treeData: [],
      treeProps: {
        children: "materialDetailList",
      },
      defaultCheckedKeys: [],
      defaultExpandedKeys: [],
      coverMaiSecName: '',
      structList: [
        '彩钢瓦结构',
        '彩钢瓦钢结构',
        '钢架结构',
        '钢结构',
        '混凝土结构',
        '砖混结构',
      ],
      deptUserOptions: [],
      cascaderProps: {
        multiple: false, //是否多选
        value: "id",
        emitPath: false,
      },
      isEditFirst: false
    };
  },
  props: {
    rowData: {
      type: Object,
      default: () => {
        return {};
      },
    },
  },
  watch: {
    rowData: {
      handler(val) {
        if (val && val.id) {
          this.isEditFirst = true
          getDetailMaterialList(val.id).then(res=> {
            this.formData = res.data;
            this.coverMaiSecName = this.formData.coverMaiSecName
            this.defaultCheckedKeys = res.data.materialDetailList.map(item=> item.assetsId)
            findAndUpdate(this.treeData)
            // this.maintenanceSectionChange(this.formData.maiSecId)
            function findAndUpdate(treeData) {
              for (let i = 0; i < treeData.length; i++) {
                const temp = res.data.materialDetailList.filter(item=> item.assetsId === treeData[i].assetsId)[0]
                if (temp) {
                  if (!treeData[i].stock || treeData[i].stock == 0 ) treeData[i].stock = temp.stock
                  // 如果实际填写数量小于标配数量，显示标配数量
                  if (temp.stock >= treeData[i].stock) {
                    treeData[i].stock = temp.stock
                  }
                }
                if (treeData[i].materialDetailList) {
                  findAndUpdate(treeData[i].materialDetailList);
                }
              }
            }
            this.isEditFirst = false
          })
        }
      },
      immediate: true,
    },
  },
  created() {
    this.getTypeTree();
    this.getDeptTreeDef()
  },
  mounted() {},
  methods: {
    getDeptTreeDef() {
      getTreeStruct({ types: 111 }).then((response) => {
        this.deptUserOptions = response.data;
      });
    },
    getTypeTree() {
      const params = {}
      if (!this.rowData) {
        params.isStandard = 1
      }
      getMaterialTypeList(params).then((res) => {
        const cloneData = JSON.parse(JSON.stringify(res.data));
        cloneData.materialDetailList = cloneData.children;
        cloneData.assetsId = cloneData.id
        if (!this.rowData) {
          this.defaultCheckedKeys = [cloneData.id]
        }
        this.defaultExpandedKeys = [cloneData.id]
        this.treeData = [cloneData];
      });
    },
    coverMaiSecNameChange(e) {
      if (Array.isArray(e)) {
        this.formData.coverMaiSecName = e.join(',')
      } else{
        this.formData.coverMaiSecName = e
      }
    },
    onSave() {
      this.$refs.elForm.validate((valid) => {
        if (!valid) return;
        const selectType = this.$refs.typeTree.getCheckedNodes().filter(item=> item.stock > 0)
        if(selectType.length <= 0) {
          this.$message.warning('请选择物资')
          return
        }
        this.formData.materialDetailList = selectType
        if (
          this.formData.fileId &&
          Array.isArray(this.formData.fileId) &&
          this.formData.fileId.length > 0
        ) {
          this.formData.fileId = this.formData.fileId[0];
        }
        if (this.formData.id) {
          editMaterialDepot(this.formData).then(() => {
            this.$modal.msgSuccess("保存成功");
            this.onClose();
          });
        } else {
          addMaterialDepot(this.formData).then(() => {
            this.$modal.msgSuccess("保存成功");
            this.onClose();
          });
        }
      });
    },
    onClose() {
      this.$emit("close");
    },
    maiSecIdChange(e) {
      console.log(this.$refs.routeRef);
      if (!this.isEditFirst) {
        this.$refs.routeRef.selectValue = ''
        this.$forceUpdate()
      }
    },
  },
};
</script>
<style scoped lang="scss">
.card_title {
  width: 200px;
  text-align: left;
  margin-bottom: 15px;
  font-weight: bold;
}

::v-deep .el-form-item {
  margin-bottom: 12px;
}

.left-form {
  margin-right: 10px;
  padding-right: 10px;
  border-right: solid 1px #eee;
}

.tree-box {
  height: 680px;
  overflow-y: auto;

  .tree-ipt {
    width: 80px;
    height: 22px;
    margin-left: 10px;

    ::v-deep .el-input__inner {
      height: 100%;
      line-height: 22px;
    }
  }
}
</style>
<style lang="scss" scoped>
@import "@/assets/styles/business.scss";
</style>
