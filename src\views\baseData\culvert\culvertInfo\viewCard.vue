<template>
  <div class="culvert-info-detail" :style="{height: contentH + 'px'}">
    <el-divider content-position="left">管理处：{{ ruleForm.manageMaintainUnitName }}</el-divider>
    <el-descriptions title="1.行政识别数据">
      <el-descriptions-item label="路线编码">{{ ruleForm.routeCode }}</el-descriptions-item>
      <el-descriptions-item label="路线名称">{{ ruleForm.routeName }}</el-descriptions-item>
      <el-descriptions-item label="路线等级">
        <DictTag :value="ruleForm.routeLevel" :options="dict.type.bridge_route_level" />
      </el-descriptions-item>
      <el-descriptions-item label="涵洞编码">{{ ruleForm.culvertCode }}</el-descriptions-item>
      <el-descriptions-item label="匝道编码">{{ ruleForm.rampCode }}</el-descriptions-item>
      <el-descriptions-item label="中心桩号">
        {{ formatPile(ruleForm.centerStake) }}
      </el-descriptions-item>
      <el-descriptions-item label="养护路段">{{ ruleForm.maintenanceSectionName }}</el-descriptions-item>
      <el-descriptions-item label="路段类型">
        <DictTag :value="ruleForm.sectionType" :options="dict.type.sys_route_type" />
      </el-descriptions-item>
      <el-descriptions-item label="涵洞类型">
        <DictTag :value="ruleForm.culvertType" :options="dict.type.sys_culvert_type" />
      </el-descriptions-item>
      <el-descriptions-item label="设计荷载">
        <DictTag :value="ruleForm.designLoad" :options="dict.type.bridge_design_load" />
      </el-descriptions-item>
      <el-descriptions-item label="施工桩号">
        {{ formatPile(ruleForm.constructionStake) }}
      </el-descriptions-item>
      <el-descriptions-item label="建成时间">{{ parseTime(ruleForm.completionTime) }}</el-descriptions-item>
      <el-descriptions-item label="涵洞跨径(m)">{{ ruleForm.culvertSpan }}</el-descriptions-item>
    </el-descriptions>

    <el-descriptions title="2.结构技术数据">
      <el-descriptions-item label="涵身长度(米)">{{ ruleForm.culvertBodyLength }}</el-descriptions-item>
      <el-descriptions-item label="进口形式">{{ ruleForm.inletPortForm }}</el-descriptions-item>
      <el-descriptions-item label="涵底纵坡">{{ ruleForm.culvertBottomLongitudinalRamp }}</el-descriptions-item>
      <el-descriptions-item label="路面宽度(米)">{{ ruleForm.pavementWidth }}</el-descriptions-item>
      <el-descriptions-item label="孔径(米)">{{ ruleForm.aperture }}</el-descriptions-item>
      <el-descriptions-item label="出口形式">{{ ruleForm.exitForm }}</el-descriptions-item>
      <el-descriptions-item label="涵底铺砌">{{ ruleForm.culvertBottomPave }}</el-descriptions-item>
      <el-descriptions-item label="路基宽度(米)">{{ ruleForm.subgradeWidth }}</el-descriptions-item>
      <el-descriptions-item label="净高(米)">{{ ruleForm.clearHeight }}</el-descriptions-item>
      <el-descriptions-item label="基础形式">{{ ruleForm.basicsForm }}</el-descriptions-item>
      <el-descriptions-item label="填土高度(米)">{{ ruleForm.fillHeight }}</el-descriptions-item>
      <el-descriptions-item label="路面类型">{{ ruleForm.pavementType }}</el-descriptions-item>
    </el-descriptions>

    <el-descriptions title="3.档案资料(全、不全或无)" :column="4">
      <el-descriptions-item label="设计图纸">
        <dict-tag
          :options="dict.type.base_archives_type"
          :value="ruleForm.designDrawingComplete"
        />
      </el-descriptions-item>
      <el-descriptions-item label="竣工文件">
        <dict-tag
          :options="dict.type.base_archives_type"
          :value="ruleForm.completionDocumentComplete"
        />
      </el-descriptions-item>
      <el-descriptions-item label="特别检查报告">
        <dict-tag
          :options="dict.type.base_archives_type"
          :value="ruleForm.particularlyReportComplete"
        />
      </el-descriptions-item>
      <el-descriptions-item label="档案号">
        <dict-tag
          :options="dict.type.base_archives_type"
          :value="ruleForm.fileNumberComplete"
        />
      </el-descriptions-item>
      <el-descriptions-item label="设计文件">
        <dict-tag
          :options="dict.type.base_archives_type"
          :value="ruleForm.designDocumentComplete"
        />
      </el-descriptions-item>
      <el-descriptions-item label="验收文件">
        <dict-tag
          :options="dict.type.base_archives_type"
          :value="ruleForm.acceptanceDocumentComplete"
        />
      </el-descriptions-item>
      <el-descriptions-item label="专项检查报告">
        <dict-tag
          :options="dict.type.base_archives_type"
          :value="ruleForm.specialInspectionReportComplete"
        />
      </el-descriptions-item>
      <el-descriptions-item label="存档案">
        <dict-tag
          :options="dict.type.base_archives_type"
          :value="ruleForm.saveArchivesComplete"
        />
      </el-descriptions-item>
      <el-descriptions-item label="施工文件">
        <dict-tag
          :options="dict.type.base_archives_type"
          :value="ruleForm.constructionDocumentsComplete"
        />
      </el-descriptions-item>
      <el-descriptions-item label="定期检查报告">
        <dict-tag
          :options="dict.type.base_archives_type"
          :value="ruleForm.periodicInspectionReportComplete"
        />
      </el-descriptions-item>
      <el-descriptions-item label="历次维修资料">
        <dict-tag
          :options="dict.type.base_archival_data"
          :value="ruleForm.previousMaintenanceComplete"
        />
      </el-descriptions-item>
      <el-descriptions-item label="建档时间">
        <dict-tag
          :options="dict.type.base_archives_type"
          :value="ruleForm.filingTimeComplete"
        />
      </el-descriptions-item>
    </el-descriptions>

    <el-descriptions title="4.最近技术状况评定" />
    <el-table
      :data="tableData"
      border
      style="width: 100%"
    >
      <el-table-column
        prop="date"
        align="center"
        label="检查时间"
      />
      <el-table-column
        prop="date"
        align="center"
        label="全涵评定等级"
      />
      <el-table-column
        prop="date"
        align="center"
        label="进水口"
      />
      <el-table-column
        prop="date"
        align="center"
        label="出水口"
      />
      <el-table-column
        prop="date"
        align="center"
        label="涵身两侧"
      />
      <el-table-column
        prop="date"
        align="center"
        label="涵身顶部"
      />
      <el-table-column
        prop="date"
        align="center"
        label="涵底铺砌"
      />
      <el-table-column
        prop="date"
        align="center"
        label="涵附近填土"
      />
      <el-table-column
        prop="date"
        align="center"
        label="经常保养小修"
      />
      <el-table-column
        prop="date"
        align="center"
        label="处治对策"
      />
      <el-table-column
        prop="date"
        align="center"
        label="下次检查时间"
      />
    </el-table>

    <el-descriptions style="margin-top: 10px;" title="5.建设及维修记录" />
    <el-table
      :data="tableData"
      border
      style="width: 100%"
    >
      <el-table-column
        prop="date"
        align="center"
        label="施工日期"
      >
        <el-table-column
          prop="date"
          align="center"
          label="开工"
        />
        <el-table-column
          prop="date"
          align="center"
          label="竣工"
        />
      </el-table-column>
      <el-table-column
        prop="date"
        align="center"
        label="修建类别"
      />
      <el-table-column
        prop="date"
        align="center"
        label="修建原因"
      />
      <el-table-column
        prop="date"
        align="center"
        label="工程范围"
      />
      <el-table-column
        prop="date"
        align="center"
        label="工程费用(万元)"
      />
      <el-table-column
        prop="date"
        align="center"
        label="经费来源"
      />
      <el-table-column
        prop="date"
        align="center"
        label="质量评定"
      />
      <el-table-column
        prop="date"
        align="center"
        label="建设单位"
      />
      <el-table-column
        prop="date"
        align="center"
        label="设计单位"
      />
      <el-table-column
        prop="date"
        align="center"
        label="监理单位"
      />
      <el-table-column
        prop="date"
        align="center"
        label="施工单位"
      />
    </el-table>

    <el-descriptions style="margin-top: 10px;" title="6.涵洞照片">
      <el-descriptions-item label="进洞口图片">
        <ImagePreview :owner-id="formData.entranceHoleImageId" />
      </el-descriptions-item>
      <el-descriptions-item label="出洞口图片">
        <ImagePreview :owner-id="formData.exitHoleImageId" />
      </el-descriptions-item>
    </el-descriptions>
  </div>
</template>

<script>
import {
  getInfoById,
} from "@/api/baseData/culvert/culvertInfo/index.js";
import { parseTime } from "@/utils/ruoyi"
export default {
  name: 'CulvertInfoCard',
  dicts: ['bridge_route_level', 'sys_route_type', 'sys_culvert_type', 'bridge_design_load','base_archives_type','base_archival_data'],
  components: {},
  props: {
    formData: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      tableData: [],
      contentH: 500,
      ruleForm: {}
    }
  },
  computed: {},
  watch: {
    formData: {
      handler(val) {
        if(val && val.id) {
          getInfoById(val.id).then((res) => {
            if(res.code == 200) {
              this.ruleForm = res.data || {};
              this.ruleForm.entranceHoleImage = [this.ruleForm.entranceHoleImageId]
              this.ruleForm.exitHoleImage = [this.ruleForm.exitHoleImageId]
            }
          });
        }
      },
      deep: true,
      immediate: true,
    }
  },
  created() { },
  mounted() {
    this.contentH = window.innerHeight - 300
    window.addEventListener("resize",()=>{
      this.contentH = window.innerHeight - 300
    })
  },
  methods: {}
}
</script>

<style lang="scss" scoped>
.culvert-info-detail {
  overflow-y: auto;
}
</style>
