<template>
  <div class="year-statistics">
    <div class="year-selector">
      <!-- <el-select v-model="year" placeholder="请选择年份" :popper-append-to-body="false" popper-class="select-popper" @change="onChange">
        <el-option v-for="item in [2021, 2022, 2023, 2024]" :key="item" :label="item + '年'" :value="item"></el-option>
      </el-select> -->
    </div>
    <el-row :gutter="10">
      <el-col :span="16" :offset="0">
        <Echarts :height="isBig ? '40vh' : '34vh'" :option="option" v-if="isShow" />
      </el-col>
      <el-col :span="8">
        <div class="right-list" :style="{ marginTop: isBig ? '80px' : '0px' }">
          <div v-for="(item, index) in staData" :key="index">
            <div class="list-item">
              <div class="list-item-row1">
                <i :style="{ background: item.color }"></i>
                <span>{{ item.name }}</span>
                <span>{{ "(" + item.ratio }}<small>%</small>{{ ")" }}</span>
              </div>
              <div class="list-item-row2" :style="{ color: item.color }">
                {{ item.value }}
                <span>件</span>
              </div>
            </div>
          </div>
        </div>
      </el-col>
      <el-col :span="24">
        <el-row class="bottom-list" :gutter="20">
          <el-col :span="6" v-for="(item, index) in list" :key="'list' + index" class="list-items">
            <div class="list-item">
              <div class="list-item-row1 list-item-t">
                <i :style="{ background: item.color }"></i>
                <span>{{ item.name }}</span>
                <span>{{ "(" + item.ratio }}<small>%</small>{{ ")" }}</span>
              </div>
              <div class="list-item-row2" :style="{ color: item.color }">
                {{ item.value }}
                <span>件</span>
              </div>
            </div>
          </el-col>
        </el-row>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import { isBigScreen } from "../util/utils";
import Echarts from "./echarts/echarts.vue";
import { getDisFromCountInfo } from "@/api/cockpit/index";

export default {
  components: {
    Echarts,
  },
  props: {
    parentYear: { default: 2024 },
  },
  data() {
    return {
      option: null,
      isBig: isBigScreen(),
      num: 37417,
      numArr: [],
      chart: "",
      year: new Date().getFullYear(),
      isShow: false,
      staData: [
        {
          name: "巡查发现",
          value: 22602,
          color: "#00BCF2",
          ratio: 59.75,
        },
        {
          name: "直接新增",
          value: 15224,
          color: "#8148FE",
          ratio: 40.25,
        },
      ],
      list: [
        {
          name: "附属设施",
          value: 1513,
          color: "#307BFB",
          ratio: 4.0,
        },
        {
          name: "交通安全设施",
          value: 9714,
          color: "#EE9434",
          ratio: 25.68,
        },
        {
          name: "路基",
          value: 5056,
          color: "#00EAFF",
          ratio: 13.37,
        },
        {
          name: "路面",
          value: 5826,
          color: "#FD4CDB",
          ratio: 15.4,
        },
        {
          name: "绿化",
          value: 2501,
          color: "#2DDB44",
          ratio: 6.61,
        },
        {
          name: "桥隧涵",
          value: 8675,
          color: "#EE4344",
          ratio: 22.93,
        },
        {
          name: "其他",
          value: 4541,
          color: "#FFEA00",
          ratio: 12.0,
        },
      ],
    };
  },
  mounted() {
    this.numArr = this.num.toString().split("");
    this.initChart();
  },
  beforeDestroy() {
    window.$Bus.$off("onChangeYear");
  },
  created() {
    window.$Bus.$on("onChangeYear", (y) => {
      this.year = y;
      this.initChart();
    });
  },

  watch: {
    parentYear() {
      if (this.year == this.parentYear) return;
      this.year = this.parentYear;
      this.initChart();
    },
  },
  methods: {
    // 初始化图表
    initChart() {
      this.isShow = false;
      getDisFromCountInfo({ year: this.year }).then((res) => {
        if ((res.code = 200)) {
          let staData = JSON.parse(JSON.stringify(this.staData));
          let list = JSON.parse(JSON.stringify(this.list));
          let total = 0;
          let total1 = 0;
          let total2 = 0;
          const mergedArray = list.concat(staData);

          res.rows.forEach((item, index) => {
            staData.forEach((item1, index1) => {
              if (item.type == item1.name) {
                total1 += item.count;
              }
            });

            list.forEach((item1, index1) => {
              if (item.type == item1.name) {
                total2 += item.count;
              }
            });
          });

          res.rows.forEach((item, index) => {
            total += item.count;
            list.forEach((item1, index1) => {
              if (item.type == item1.name) {
                item1.value = item.count;
                item1.ratio = ((item.count / total2) * 100).toFixed(2);
              }
            });

            staData.forEach((item1, index1) => {
              if (item.type == item1.name) {
                item1.value = item.count;
                item1.ratio = ((item.count / total1) * 100).toFixed(2);
              }
            });
          });

          let a = staData.filter(
            (item2) => !res.rows.some((item1) => item1.type === item2.name)
          );
          a.forEach((item, index) => {
            item.value = 0;
            item.ratio = 0;
          });

          let b = list.filter(
            (item2) => !res.rows.some((item1) => item1.type === item2.name)
          );
          b.forEach((item, index) => {
            item.value = 0;
            item.ratio = 0;
          });
          this.staData = staData;
          this.list = list;
          let colorList = [
            "#307BFB",
            "#EE9434",
            "#00EAFF",
            "#FD4CDB",
            "#2DDB44",
            "#EE4344",
            "#FFEA00",
          ];
          let colorList1 = ["#00BCF2", "#8148FE"];
          let dataArr = JSON.parse(JSON.stringify(this.list));
          let data1 = JSON.parse(JSON.stringify(this.staData));
          let count1 = this.list.reduce((acc, cur) => acc + cur.value, 0);
          let count2 = this.staData.reduce((acc, cur) => acc + cur.value, 0);
          let all1 = {
            name: "",
            // 计算list value 值总和的三分之一
            value: count1 / 3,
            color: "rgba(255, 255, 255, 0)",
            ratio: 12.0,
          };
          dataArr.push(all1);

          // 计算list value 值总和的三分之一
          let all2 = {
            name: "",
            value: count2 / 3,
            color: "rgba(255, 255, 255, 0)",
            ratio: 12.0,
          };
          data1.push(all2);

          this.option = {
            tooltip: {
              trigger: "item",
            },
            legend: {
              show: false,
            },
            grid: {
              top: "10%",
              bottom: "15%",
              right: "2%",
              left: "10%",
            },
            series: [
              {
                type: "pie",
                center: ["50%", "55%"],
                radius: ["76%", "84%"],
                clockwise: true,
                startAngle: 225,
                endAngle: 315,
                avoidLabelOverlap: true,
                hoverOffset: 15,
                itemStyle: {
                  normal: {
                    color: function (params) {
                      return colorList1[params.dataIndex];
                    },
                  },
                },
                label: {
                  show: false,
                },
                data: data1,
              },
              {
                type: "pie",
                center: ["50%", "55%"],
                radius: ["92%", "100%"],
                clockwise: true,
                startAngle: 225,
                endAngle: 315,
                avoidLabelOverlap: true,
                hoverOffset: 15,
                itemStyle: {
                  normal: {
                    color: function (params) {
                      return colorList[params.dataIndex];
                    },
                  },
                },
                label: {
                  show: false,
                },
                data: dataArr,
              },
            ],

            title: {
              text: `${total1.toString()}{unit|件}`,
              subtext: "事件数量",
              top: "42%",
              textAlign: "center",
              left: "49%",
              itemGap: 10, // 设置主标题和副标题之间的间距
              // 主标题样式
              textStyle: {
                color: "#fff",
                fontSize: this.isBig ? 120 : 30,
                fontWeight: "700",
                rich: {
                  unit: {
                    color: "#fff",
                    fontSize: this.isBig ? 60 : 18,
                    padding: [8, 0, 0, 5]
                  },
                },
              },
              subtextStyle: {
                color: "#fff",
                fontSize: this.isBig ? 60 : 16,
                padding: [20, 0, 0, 0],
              },
            },
          };
        }
        this.isShow = true;
      });
    },
    onChange() {
      this.initChart();
    },
  },

};
</script>

<style lang="scss" scoped>
@import "@/assets/styles/utils.scss";

.year-statistics {
  padding: vwpx(10px);
  height: 100%;
  position: relative;

  .right-list {
    height: 34vh;
    display: flex;
    justify-content: center;
    // align-items: center;
    flex-direction: column;

    .list-item {
      margin: vwpx(60px) vwpx(30px);
      flex-shrink: 0;
    }
  }

  .bottom-list {
    width: 100%;
    padding-left: 11.5%;

    .list-items {
      .list-item {
        margin: vwpx(20px) 0;

        .list-item-t {
          width: vwpx(360px);
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
      }
    }
  }

  .list-item {
    display: flex;
    flex-direction: column;

    .list-item-row1 {
      display: flex;
      align-items: center;

      i {
        display: block;
        width: vwpx(24px);
        height: vwpx(24px);
        flex-shrink: 0;
      }

      span {
        // font-family: Source Han Sans, Source Han Sans;
        font-family: Helvetica Neue, Helvetica, PingFang SC, Microsoft YaHei, Arial,
          sans-serif;
        font-weight: 500;
        // font-size: vwpx(32px);
        font-size: clamp(14px, 0.5vw, 36px);
        color: #ffffff;
        line-height: vwpx(80px);
        text-align: center;
        // font-style: normal;
        // text-transform: none;
        margin-left: vwpx(20px);
      }

      span:last-child {
        // font-size: vwpx(26px);
        font-size: clamp(13px, 0.5vw, 32px);
      }
    }

    .list-item-row2 {
      font-family: Microsoft YaHei UI, Microsoft YaHei UI;
      font-weight: bold;
      // font-size: vwpx(50px);
      font-size: clamp(14px, 4vw, 52px);
      color: #42abff;
      line-height: vwpx(40px);

      span {
        // font-size: vwpx(30px);
        font-size: clamp(13px, 0.5vw, 32px);
      }
    }
  }
}

.year-selector {
  z-index: 99;
  height: 4vh;
  position: absolute;
  top: vwpx(20px);
  right: vwpx(40px);

  ::v-deep .el-input {
    .el-input__inner {
      width: vwpx(220px);
      height: vwpx(70px);
      background-color: rgba(1, 102, 254, 0.2);
      border: 1px solid #0166fe;
      color: #ffffff;
      font-size: vwpx(30px);
    }

    .el-input__inner::placeholder {
      color: #bbbbbb;
    }

    .el-input-group__append {
      background-color: rgba(1, 102, 254, 0.2);
      border: 1px solid #0166fe;
      color: #ffffff;
      border-left: none;
      padding: 0 10px;
      cursor: pointer;
    }
  }

  ::v-deep .select-popper {
    background-color: rgba(1, 102, 254, 0.2);
    border: 1px solid #0166fe;
    color: #ffffff !important;
    font-size: vwpx(30px);
    margin: vwpx(10px) 0;
  }

  ::v-deep .el-select-dropdown__item {
    color: #ffffff !important;
    font-size: vwpx(30px);
    margin: vwpx(15px) 0;
  }

  ::v-deep .el-select-dropdown__item.selected {
    color: #42abff !important;
  }
}
</style>
