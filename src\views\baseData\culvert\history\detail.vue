<template>
  <div v-loading="loading" class="culvert-info-edit">
    <el-tabs v-model="activeName" type="card">
      <el-tab-pane label="基本信息" name="0"/>
      <el-tab-pane label="结构技术" name="1"/>
      <el-tab-pane label="档案资料" name="2"/>
    </el-tabs>


    <div style="height: 60vh;overflow-y: auto;padding: 0 10px 0 5px;">
      <el-form
          ref="ruleFormEl"
          :model="ruleForm"
          :rules="rules"
          label-width="100px"
      >

        <template v-if="activeName === '0'">
          <MepuDetail :is-manger-unit="true" :fields="baseInfoFields" :from="ruleForm"/>
        </template>

        <template v-if="activeName === '1'">
          <MepuDetail :fields="structureInfoFields" :from="ruleForm"/>

        </template>

        <template v-if="activeName === '2'">
          <MepuDetail :fields="archivesInfoFields" :from="ruleForm"/>

        </template>


      </el-form>
    </div>

    <div class="text-center">
      <!-- <el-button @click="onClose">关 闭</el-button> -->
    </div>
  </div>
</template>

<script>
import {

  getHisStatic,
} from "@/api/baseData/culvert/history/index.js";
import {deptTreeSelect} from "@/api/tmpl";
import "@riophae/vue-treeselect/dist/vue-treeselect.css";
import Treeselect from "@riophae/vue-treeselect";
import {listMaintenanceSectionAll} from "@/api/system/maintenanceSection";
import {listByMaintenanceSectionId} from "@/api/baseData/common/routeLine";
import {listAllRoute} from "@/api/system/route.js";
import PileInput from "@/components/PileInput/index.vue";
import MepuDetail from "@/views/baseData/components/MepuColumn/index.vue";

import formFields from '../culvertInfo/js/formFields'

export default {
  name: "CulvertInfoEdit",
  dicts: [
    "bridge_route_level",
    "sys_route_type",
    "sys_culvert_type",
    "bridge_design_load",
    "base_archives_type",
  ],
  components: { Treeselect, PileInput, MepuDetail},
  props: {
    formData: {
      type: Object,
      default: () => {
      },
    },
    id: {
      type: [String, Number],
      default: ''
    }
  },
  data() {
    return {
      loading: false,
      activeName: "0",
      ruleForm: {},
      rules: {},
      enterOwnerId: "", // 进洞口图片id
      leaveOwnerId: "", // 出洞口图片id
      formH: 600, // form 表单最大高度
      deptOptions: [], // 部门树数据
      types: 101, // 编码规划
      routeOptions: [], // 路段数据
      routeList: [], // 路线编码数据


      baseInfoFields: [],
      structureInfoFields: [],
      archivesInfoFields: [],
    };
  },
  computed: {},
  watch: {
    formData: {
      handler(val) {
        if (Object.keys(val).length > 0) {
          this.ruleForm = JSON.parse(JSON.stringify(val));
        }
      },
      deep: true,
      immediate: true,
    },
    id: {
      handler(val) {
        this.getInfo(val)
      },
      deep: true,
    }
  },
  created() {
    this.init();
  },
  mounted() {
    this.formH = window.innerHeight - 360;
    window.addEventListener("resize", () => {
      this.formH = window.innerHeight - 360;
    });
  },
  methods: {
    init() {
      this.baseInfoFields = JSON.parse(
          JSON.stringify(formFields.baseInfo)
      )
      this.structureInfoFields = JSON.parse(JSON.stringify(formFields.structureInfo))
      this.archivesInfoFields = JSON.parse(
          JSON.stringify(formFields.archivesInfo
          )
      )
      if (Object.keys(this.formData).length > 0) {
        this.ruleForm = JSON.parse(JSON.stringify(this.formData));
        this.getInfo();
      }
      this.getDeptTree();
      this.getOptions();
    },
    getInfo(val) {
      if (this.ruleForm.id) {
        this.$modal.loading();
        getHisStatic(this.ruleForm.id).then((res) => {
          if (res.code == 200) {
            this.ruleForm = res.data || {};
          }
        }).finally(() => {
          this.$modal.closeLoading();
        });
      }
    },
    onClose() {
      this.$emit("close");
    },
    /** 查询部门下拉树结构 */
    getDeptTree() {
      return deptTreeSelect({types: this.types}).then((response) => {
        this.deptOptions = response.data;
      });
    },
    getOptions() {
      listMaintenanceSectionAll({departmentId: null}).then((res) => {
        if (res.code == 200) {
          this.routeOptions = res.data;
        }
      });
      listAllRoute().then((res) => {
        if (res.code == 200) {
          this.routeList = res.data || [];
        }
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.culvert-info-edit {
  /* max-height: 75vh;
    padding: 10px;
    overflow: auto; */

  ::v-deep .el-input__suffix {
    display: none;
  }
}
</style>
