<template>
  <PageContainer :ifHeader="false">
    <template slot="search">

      <div style="display: flex; align-items: center; margin: 0">
        <CascadeSelection
            :form-data="searchForm"
            v-model="searchForm"
            types="201"
            multiple
        />
        <el-select v-model="searchForm.culvertTypes" multiple clearable filterable  placeholder="涵洞类型" style="margin:0 20px">
          <el-option  v-for="dict in dict.type.sys_culvert_type" :key="dict.value" :label="dict.label"
                      :value="dict.value">
          </el-option>
        </el-select>

        <div style="min-width:220px;">
          <el-button v-hasPermi="['baseData:culvert:query']" type="primary" icon="el-icon-search" @click="onSearch">搜索</el-button>
          <el-button icon="el-icon-refresh" @click="resetQuery">
            重置
          </el-button>
          <el-button
              v-show="!showSearch"
              icon="el-icon-arrow-down"
              circle
              @click="showSearch=true"
          />
          <el-button
              v-show="showSearch"
              icon="el-icon-arrow-up"
              style="color: #1890ff;border-color: #badeff;background-color: #e8f4ff;"
              circle
              @click="showSearch=false"
          />
        </div>

      </div>
      <div v-if="showSearch" style="margin-top: 5px">
        <el-input v-model="searchForm.culvertCode" placeholder="涵洞编码" @keyup.enter.native="onSearch" style="margin-right:20px;width: 170px" clearable/>
        <el-select
            v-model="searchForm.operationState"
            placeholder="运营状态"
            clearable
            collapse-tags
            style="width: 170px"
        >
          <el-option
              v-for="dict in dict.type.sys_operation_state"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
          />
        </el-select>
        <el-select
            style="margin-left:20px;width: 170px"
            v-model="searchForm.status"
            placeholder="数据状态"
            clearable
        >
          <el-option
              v-for="dict in dict.type.base_data_state"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
          />
        </el-select>
      </div>
    </template>
    <template slot="header">

    </template>
    <template slot="body">
      <el-table v-adjust-table v-loading="loading" :data="tableData" height="100%" border
         ref="tableRef">
        <!-- <el-table-column fixed="left" type="selection" width="50" align="center" :selectable="checkSelectable"
          :reserve-selection="true" /> -->
        <el-table-column label="序号" fixed="left" type="index" width="55" align="center" >
          <template v-slot="scope">
            {{ (scope.$index + (searchForm.pageNum - 1) * searchForm.pageSize)+1 }}
          </template>
        </el-table-column>
        <el-table-column label="操作" fixed width="50" align="center" class-name="small-padding fixed-right">
          <template slot-scope="{ row }">
            <el-link
            type="primary" :disabled="!row.shape" @click.stop="onToMap(row)">定位</el-link>
          </template>
        </el-table-column>
        <el-table-column label="涵洞编码" fixed="left" width="180" align="center" prop="culvertCode" />
        <el-table-column label="管理处" width="150" align="center" prop="managementMaintenanceName" />
        <el-table-column label="管辖分处" width="150" align="center" prop="managementMaintenanceBranchName" />
        <el-table-column label="养护路段" width="150" align="center" prop="maintenanceSectionName" />
        <el-table-column label="路线编码" width="80" align="center" prop="routeCode" />
        <el-table-column label="中心桩号" width="180" align="center">
          <template slot-scope="scope">
            <span v-if="scope.row && scope.row.centerStake">{{
              formatPile(scope.row.centerStake)
            }}</span>
          </template>
        </el-table-column>

        <el-table-column label="涵洞类型" width="150" align="center" prop="startPointName">
          <template slot-scope="scope">
            <span v-if="scope.row && scope.row.culvertType">
              <DictTag :value="scope.row.culvertType" :options="dict.type.sys_culvert_type" />
            </span>
          </template>
        </el-table-column>

        <el-table-column align="center" prop="operationState" width="110" label="运营状态">
          <template slot-scope="{ row }">
            <el-link :underline="false" :type="{ 1: 'info', 2: 'success', 3: 'danger', 4: 'primary' }[
              row.operationState
            ]
              " >
              <DictTag :value="row.operationState" :options="dict.type.sys_operation_state" />
            </el-link>
          </template>
        </el-table-column>
        <el-table-column align="center" prop="operationState" width="110" label="是否锁定">
          <template slot-scope="{ row }">
            <el-link :underline="false" :type="row.isLocked ? 'danger' : 'info'" >
              {{ row.isLocked ? "是" : "否" }}
            </el-link>
          </template>
        </el-table-column>
        <el-table-column align="center" prop="operationState" width="110" label="数据状态">
          <template slot-scope="{ row }">
            <el-link :underline="false">
              {{ row.status == 1 ? "暂存" : "启用" }}
            </el-link>
          </template>
        </el-table-column>
        <el-table-column label="涵洞跨径(米)" width="150" align="center" prop="culvertSpan" />
        <el-table-column label="涵洞长度(米)" width="150" align="center" prop="culvertLength" />
        <el-table-column label="涵洞净高(米)" width="150" align="center" prop="culvertHeight" />

        <el-table-column
          label="变更人"
          align="center"
          prop="operateBy"
          min-width="150"
        />
        <el-table-column
          label="变更时间"
          align="center"
          prop="operateTime"
          min-width="150"
        />
        <el-table-column label="查看" fixed="right" align="center">
          <template #default="{ row }">
            <el-link type="primary" :underline="false" v-hasPermi="['baseData:culvertHis:getInfoById']"
              @click.stop="onDetail(row)">查看</el-link>
          </template>
        </el-table-column>
      </el-table>
      <pagination v-show="pages.total > 0" :total="pages.total" :page.sync="pages.pageNum" :limit.sync="pages.pageSize"
        @pagination="getList" />
    </template>



    <Dialog title="查看涵洞信息" :show.sync="showDetail">
      <Detail v-if="showDetail" :form-data="modelData" @close="() => {
        showDetail = false;
      }
        " />
    </Dialog>

    <!-- 查看图纸、二维码下载、照片弹窗 -->
    <Dialog :title="showTitle" :show.sync="showVisible">
      <div v-if="showType == 1">暂无数据</div>
      <div v-else-if="showType == 2"></div>
    </Dialog>
    <Drawings
      v-if="showDrawing"
      :showDrawing="showDrawing"
      :typeCode="'BS131'"
      :id="tableSelects[0].assetId"
      @close="showDrawing = false"
    />
    <MapPosition
      v-if="showMapPosition"
      :dialogVisible="showMapPosition"
      :data="mapPositionData"
      @close="showMapPosition = false"
    />
  </PageContainer>
</template>

<script>
import {
  listStatic,

} from "@/api/baseData/culvert/history/index.js";
import SelectTreeCheckbox from "@/components/DeptTmpl/selectTreeCheckbox";
import selectTree from "@/components/DeptTmpl/selectTree";
import RouteLine from "@/components/RouteLine";
import RouteRoad from "@/components/RouteRoad";
import DictTag from "@/components/DictTag";
import Dialog from "@/components/Dialog/index.vue";
import Detail from "./detail.vue";

import ImportData from "@/views/baseData/components/importData/index.vue";
import Drawings from '@/views/baseData/components/drawings/index.vue'
import CascadeSelection from "@/components/CascadeSelection/index.vue";
import { listMaintenanceSectionAll } from "@/api/system/maintenanceSection";
import { listByMaintenanceSectionId } from "@/api/baseData/common/routeLine";
import { statusListDialog } from "@/views/baseData/components/statusDialog/list.js";
import MapPosition from '@/components/mapPosition/index.vue'

export default {
  name: "History",
  dicts: ["sys_culvert_type", "sys_operation_state",'base_data_state'],
  components: {
    SelectTreeCheckbox,
    selectTree,
    RouteLine,
    RouteRoad,
    Dialog,
    DictTag,
    ImportData,
    Detail,
    CascadeSelection,
    Drawings,
    MapPosition
  },
  data() {
    return {
      loading: false,
      showMore: false, // 更多搜索条件
      manageIds: "", // 选中数组
      tableSelects: [], // table选中数组
      tableData: [],
      showEdit: false, // 添加/编辑
      showSearch: false,
      showCard: false, // 查看卡片
      showDetail: false, // 查看
      modelData: {}, // 模态框数据
      searchForm: {
        operationState: "2", // 默认2 运营中
      },
      pages: {
        pageNum: 1,
        pageSize: 20,
        total: 0,
      },
      showImportAdd: false, // 导入弹窗
      isUpdate: false,
      importBaseType: "3", // 导入基础数据类型 1桥梁 2隧道 3涵洞 4互通
      importType: 2, // 导入类型 1更新 2新增
      routeOptions: [], // 路段数据
      routeList: [], // 路线编码数据
      showVisible: false,
      showTitle: "",
      showType: 1, // 1、显示查看图纸、二维码下载,2、查看图片
      tableH: "92%", // 表格高度
      showDrawing: false,
      showMapPosition: false,
      mapPositionData: undefined
    };
  },
  created() {
    this.init();
    // 改变表格高度
    this.tableH = window.innerHeight - 300 + "px";
    window.addEventListener("resize", () => {
      this.tableH = window.innerHeight - 300 + "px";
    });
  },
  methods: {
    init() {
      this.getList();
    },
    /** 查询列表 */
    getList() {
      this.loading = true;
      // this.searchForm.manageMaintainUnitId = this.manageIds.join(',')
      // 2024年6月21日
      // this.searchForm.managementMaintenanceId = '';
      // 路段
      // this.searchForm.routeIds = this.searchForm.maintenanceSectionId?[this.searchForm.maintenanceSectionId]:null
      this.searchForm = Object.assign(this.searchForm, this.pages);
      listStatic(this.searchForm)
        .then((res) => {
          if (res.code === 200) {
            this.tableData = res.rows || [];
            this.pages.total = res.total;
          }
        })
        .finally(() => {
          this.loading = false;
        });
    },
    /** 搜索按钮操作 */
    onSearch() {
      this.pages.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.searchForm = {
        pageNum: 1,
        pageSize: 20,
        operationState: "2"
      }
      this.onSearch();
    },

    onDetail(row) {
      let data;
      if (row && !row.id) {
        if (this.tableSelects && this.tableSelects.length != 1) {
          this.$modal.msgWarning("请选择一条数据");
          return;
        }
        data = this.tableSelects[0];
      } else {
        data = row;
      }
      this.modelData = data;
      this.showDetail = true;
    },

    // 查看图纸
    onDrawing(row) {
      if (this.tableSelects.length !== 1) {
        this.$message.warning('请选择一条数据！')
        return
      } else {
        this.showDrawing = true
      }
    },

    onSelectTable(list) {
      this.tableSelects = list;
    },

    // 点击行
    clickTable(row) {
      // if(row.isLocked) return;
      let arr = this.tableSelects.filter((v) => v.id == row.id);
      // 点击行 选中复选框
      if (arr && arr.length > 0) {
        this.$refs.tableRef.toggleRowSelection(row, false);
        this.tableSelects = this.tableSelects.filter((v) => v.id != row.id);
      } else {
        this.tableSelects = [...this.tableSelects, ...[row]];
        this.$refs.tableRef.toggleRowSelection(row, true);
      }
    },

    // 跳转
    onToMap(row) {
      this.mapPositionData = row
      this.showMapPosition = true
    },

    closeImportAdd(v) {
      this.showImportAdd = false;
      if (v) this.getList();
    },
    // 监听 管理处选择
    handleTreeChange(e) {
      if (!e) return;
      listMaintenanceSectionAll({ departmentId: e }).then((res) => {
        if (res.code == 200) {
          this.routeOptions = res.data || [];
          // 置空选中路段
          this.searchForm.maintenanceSectionId = "";
          // 置空选中路线编码
          this.searchForm.routeCodes = [];
          // 置空线路编码数据
          this.routeList = [];
        }
      });
    },
    // 监听路段选择变化
    maintenanceSectionChange(e) {
      if (!e) return;
      listByMaintenanceSectionId({ maintenanceSectionId: e }).then((res) => {
        if (res.code == 200) {
          this.routeList = res.data || [];
          // 置空选中路线编码
          this.searchForm.routeCodes = [];
        }
      });
    },
    checkSelectable(row, index) {

    },

    // 查看运营变更记录
    onStatusList(row) {
      statusListDialog({ dataId: row.assetId, baseDataType: 3 });
    },

    // 查看图片
    onImgPreview(row) {
      this.showVisible = true;
      this.showTitle = "照片";
      this.showType = 2;
    },
  },
};
</script>

<style lang="scss" scoped>
/* 输入框超出隐藏，不换行*/
::v-deep .el-select__tags {
    flex-wrap: nowrap;
    overflow: auto;
  }

::v-deep .el-scrollbar {
  .el-select-dropdown__item {
    padding: 0;
  }
}
.custom-select {
    .el-select-dropdown__item {
      padding: 0;
      background-color: #0f0;
      /* 修改背景颜色为绿色 */
    }
  }
</style>
