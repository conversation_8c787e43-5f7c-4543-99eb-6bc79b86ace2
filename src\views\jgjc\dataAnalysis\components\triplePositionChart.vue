<template>
  <div class="triple-chart-container">
    <div ref="xzChart" class="chart"></div>
    <div ref="yzChart" class="chart"></div>
    <div ref="xyChart" class="chart"></div>
  </div>
</template>

<script>
import * as echarts from 'echarts'

export default {
  name: 'TriplePositionChart',
  props: {
    chartData: {
      type: Array,
      required: true,
      default() {
        return [
          [0, 0, 0],
          [0, 0, 0],
          [54.77, 29.04, 996.33],
          [108.15, 53.89, 1992.97],
          [140.62, 37.48, 2990.6],
          [189.01, 41.48, 3987.6],
          [232.84, 64.05, 4984.68],
          [283.83, 65.31, 5981.6904],
          [327.28, 67.29, 6979.1904],
          [342.04, 55.4, 7977.4404],
          [377.26, 37.75, 8974.99],
          [406.7, 16.82, 9972.73],
          [451.03, 9.51, 10970.13],
          [499.86, -14.65, 11967.07],
          [550.29, -22.72, 12964.09],
          [506.58, 13.3, 13960.909],
          [470.11, 11.61, 14958.68],
          [488.18, 63.24, 15955.591],
          [513.7, 93.06, 16953.047],
          [549.1, 91.26, 17950.877],
          [591.44, 98.06, 18948.336],
          [634.65, 109.84, 19945.678],
          [697.15, 156.77, 20941.166],
          [745.84, 180.88, 21938.035],
          [778.15, 201.19, 22935.785],
          [795, 233.66, 23933.424],
          [807.73, 245.87, 24931.664],
          [830.71, 250.68, 25929.686],
          [849.49, 249.33, 26927.885],
          [877.64, 276.21, 27925.385],
          [903.71, 289.92, 28923.295],
          [914.98, 322.79, 29921.098],
          [924.26, 328.59, 30919.459],
          [950.72, 333.54, 31917.334],
          [977.4601, 348.9, 32915.094],
          [1013.8801, 388.34, 33912.086],
          [1030.8502, 417.51, 34909.953],
          [1047.9, 430.99, 35908.094],
          [1066.3301, 445.9, 36906.324],
          [1063.4596, 457.458, 37865.02]
        ]
      }
    }
  },
  data() {
    return {
      xyChart: null,
      xzChart: null,
      yzChart: null
    }
  },
  watch: {
    chartData: {
      handler(newVal) {
        this.updateCharts(newVal)
      },
      deep: true
    }
  },
  mounted() {
    this.initCharts()
    window.addEventListener('resize', this.handleResize)
  },
  beforeDestroy() {
    window.removeEventListener('resize', this.handleResize)
    if (this.xyChart) {
      this.xyChart.dispose()
    }
    if (this.xzChart) {
      this.xzChart.dispose()
    }
    if (this.yzChart) {
      this.yzChart.dispose()
    }
  },
  methods: {
    initCharts() {
      this.xyChart = echarts.init(this.$refs.xyChart)
      this.xzChart = echarts.init(this.$refs.xzChart)
      this.yzChart = echarts.init(this.$refs.yzChart)

      const baseOption = {
        grid: {
          left: '25%',
          right: '10%',
          bottom: '15%'
        },
        xAxis: {
          nameLocation: 'center',
          nameGap: 25,
          type: 'value',
          splitNumber: 2,
          axisLine: {
            lineStyle: {
              color: '#0297a1'
            }
          },
          axisLabel: {
            formatter: '{value}'
          },
          splitLine: {
            show: false
          }
        },
        yAxis: {
          nameLocation: 'center',
          nameGap: 25,
          type: 'value',
          axisLine: {
            lineStyle: {
              color: '#0297a1'
            }
          },
          axisLabel: {
            formatter: '{value}'
          },
          splitLine: {
            show: false
          }
        },
        series: [{
          type: 'line',
          symbol: 'none',
          smooth: 0.3,
          lineStyle: {
            color: '#f00'
          }
        }],
      }

      // XY Chart
      const xyOption = JSON.parse(JSON.stringify(baseOption))
      xyOption.xAxis.name = 'X方向位置 (mm)'
      xyOption.yAxis.name = 'Y方向位置 (mm)'
      xyOption.yAxis.nameGap = 75
      this.xyChart.setOption(xyOption)

      // XZ Chart
      const xzOption = JSON.parse(JSON.stringify(baseOption))
      xzOption.xAxis.name = 'X方向位置 (mm)'
      xzOption.yAxis.name = 'Z方向位置 (mm)'
      xzOption.yAxis.nameGap = 75
      this.xzChart.setOption(xzOption)

      // YZ Chart
      const yzOption = JSON.parse(JSON.stringify(baseOption))
      yzOption.xAxis.name = 'Y方向位置 (mm)'
      yzOption.yAxis.name = 'Z方向位置 (mm)'
      yzOption.yAxis.nameGap = 75
      this.yzChart.setOption(yzOption)

      this.updateCharts(this.chartData)
    },
    updateCharts(data) {
      if (!data || data.length === 0) return

      const xyData = data.map(item => [item[0], item[1]])
      const xzData = data.map(item => [item[0], item[2]])
      const yzData = data.map(item => [item[1], item[2]])

      this.xyChart.setOption({
        series: [{
          data: xyData
        }]
      })

      this.xzChart.setOption({
        series: [{
          data: xzData
        }]
      })

      this.yzChart.setOption({
        series: [{
          data: yzData
        }]
      })
    },
    handleResize() {
      if (this.xyChart) {
        this.xyChart.resize()
      }
      if (this.xzChart) {
        this.xzChart.resize()
      }
      if (this.yzChart) {
        this.yzChart.resize()
      }
    }
  }
}
</script>

<style scoped>
.triple-chart-container {
  display: flex;
  width: 100%;
  height: 100%;
  gap: 20px;
}

.chart {
  flex: 1;
  height: 100%; /* 可以根据需要调整高度 */
  min-width: 0; /* 防止flex item溢出 */
}

@media (max-width: 768px) {
  .triple-chart-container {
    flex-direction: column;
  }
  .chart {
    height: 300px;
  }
}
</style>
