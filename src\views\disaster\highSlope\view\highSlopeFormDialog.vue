<template>
  <div class="formDialog" :class="oneMap?'one-map':''">
<!--    <div class="titleBox">-->
<!--      <div class="title">{{ formTitle }}</div>-->
<!--      <div class="subTitle" v-if="formParams.id">ID：{{ formParams.id }}</div>-->
<!--    </div>-->
    <el-form ref="formRef" :model="formParams" disabled label-width="140px" label-position="right" style="width: 99%;padding-top: 10px">
      <div class="infoBox" style="margin-bottom: 20px;" v-loading="formLoading">
        <div class="infoTitle">
          基础信息
        </div>
        <el-row :gutter="12">
          <el-col :span="12">
            <el-form-item label="管养单位名称" prop="maintenanceUnitId">
              <el-select ref="formMainRef" v-model="formParams.maintenanceUnitId" placeholder="请选择管养单位"
                         style="width: 100%;">
                <el-option v-for="item in formMaintenanceRenderList" :label="item.label" :value="item.id"
                           :key="item.id" style="display: none;"></el-option>
                <el-tree
                  :data="formMaintenanceList"
                  :props="{ children: 'children', label: 'label', value: 'id' }"
                  :expand-on-click-node="false"
                  highlight-current
                  default-expand-all>
                </el-tree>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item prop="roadSectionId">
                <span slot="label">
                  <el-tooltip content="选择管养单位后带出" placement="top">
                    <i class="el-icon-question"></i>
                  </el-tooltip>
                  路段名称
                </span>
              <el-select v-model="formParams.roadSectionId" placeholder="请选择路段" style="width: 100%;">
                <el-option v-for="item in formRoadSectionList" :label="item.maintenanceSectionName"
                           :value="item.maintenanceSectionId" :key="item.maintenanceSectionId"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="12">
          <el-col :span="12">
            <el-form-item prop="routerNum">
                <span slot="label">
                  <el-tooltip content="选择路段后带出" placement="top">
                    <i class="el-icon-question"></i>
                  </el-tooltip>
                  路线名称
                </span>
              <el-select v-model="formParams.routerNum" placeholder="请选择路线" style="width: 100%;">
                <el-option v-for="item in formRouteList" :label="`${item.routeName}（${item.routeCode}）`"
                           :value="item.routeCode" :key="item.routeCode"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item prop="technicalGrade">
                <span slot="label">
                  <el-tooltip content="选择路段后带出" placement="top">
                    <i class="el-icon-question"></i>
                  </el-tooltip>
                  技术等级
                </span>
              <!-- <el-input readonly v-model="formParams.technicalGrade" placeholder="请输入技术等级" /> -->
              <el-select disabled v-model="formParams.technicalGrade" placeholder="请选择技术等级"
                         style="width: 100%;">
                <el-option v-for="dict in dict.type.sys_route_grade" :label="dict.label" :value="dict.value"
                           :key="dict.value"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="12">
          <el-col :span="8">
            <el-form-item prop="">
                <span slot="label">
                  <el-tooltip content="省级区划代码" placement="top">
                    <i class="el-icon-question"></i>
                  </el-tooltip>
                  省级区划
                </span>
              <el-select v-model="divisionsProvincial" disabled style="width: 100%;">
                <el-option v-for="item in divisionsProvincialList" :label="`${item.label}（${item.id}）`"
                           :value="item.id" :key="item.id"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item prop="">
                <span slot="label">
                  <el-tooltip content="市级区划代码" placement="top">
                    <i class="el-icon-question"></i>
                  </el-tooltip>
                  市级区划
                </span>
              <el-select v-model="divisionsMunicipal" clearable style="width: 100%;">
                <el-option v-for="item in divisionsMunicipalList" :label="`${item.label}（${item.id}）`"
                           :value="item.id" :key="item.id"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item prop="areaCode">
                <span slot="label">
                  <el-tooltip content="县级区划代码，用于生成灾害编码" placement="top">
                    <i class="el-icon-question"></i>
                  </el-tooltip>
                  县级区划
                </span>
              <el-select v-model="formParams.areaCode" clearable style="width: 100%;">
                <el-option v-for="item in areaCodeList" :label="`${item.label}（${item.id}）`" :value="item.id"
                           :key="item.id"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="12">
          <el-col :span="12">
            <el-form-item prop="highSlopeName">
                <span slot="label">
                  <el-tooltip content="按“里程桩号+段高边坡”保存后自动生成（例如“K0+000 至K0+100 段高边坡”）"
                              placement="top">
                    <i class="el-icon-question"></i>
                  </el-tooltip>
                  高边坡名称
                </span>
              <el-input v-model="formParams.highSlopeName" disabled placeholder="请输入高边坡名称"/>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item prop="highSlopeNum">
                <span slot="label">
                  <el-tooltip
                    content="按“路线编号+县级行政区划代码+B+四位编号+L（R）”进行编号（边坡四位编码后加 L 或 R 代表左侧或右侧，例如G75520303B0002L）,保存后自动生成"
                    placement="top">
                    <i class="el-icon-question"></i>
                  </el-tooltip>
                  高边坡编号
                </span>
              <el-input v-model="formParams.highSlopeNum" disabled placeholder="请输入高边坡编号"/>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="12">
          <el-col :span="12">
            <el-form-item prop="pileStartNum">
                <span slot="label">
                  <el-tooltip
                    content="起止点桩号，根据现场调查填写起点桩号和止点桩号（例如起点桩号 K0+100，止点桩号 K0+200）"
                    placement="top">
                    <i class="el-icon-question"></i>
                  </el-tooltip>
                  边坡起点桩号
                </span>
              <!-- <PileInput v-model="formParams.pileStartNum" placeholder="请输入起点桩号"></PileInput> -->
              <div style="width: 100%; display: flex; flex-direction: row; flex-wrap: nowrap;">
                <el-tag
                  type="info"
                  effect="plain"
                  size="medium"
                >
                  K
                </el-tag>
                <el-input v-model="pileStartK" controls-position="right" style="width: 50%;"
                          placeholder="请输入起点桩号"/>
                <el-tag
                  type="info"
                  effect="plain"
                  size="medium"
                >
                  +
                </el-tag>
                <el-input v-model="pileStartAdd" controls-position="right" style="width: 50%;"
                          placeholder="请输入起点桩号"/>
              </div>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item prop="pileEndNum">
                <span slot="label">
                  <el-tooltip
                    content="起止点桩号，根据现场调查填写起点桩号和止点桩号（例如起点桩号 K0+100，止点桩号 K0+200）"
                    placement="top">
                    <i class="el-icon-question"></i>
                  </el-tooltip>
                  边坡终点桩号
                </span>
              <!-- <PileInput v-model="formParams.pileEndNum" placeholder="请输入终点桩号"></PileInput> -->
              <div style="width: 100%; display: flex; flex-direction: row; flex-wrap: nowrap;">
                <el-tag
                  type="info"
                  effect="plain"
                  size="medium"
                >
                  K
                </el-tag>
                <el-input v-model="pileEndK" controls-position="right" style="width: 50%;"
                          placeholder="请输入终点桩号"/>
                <el-tag
                  type="info"
                  effect="plain"
                  size="medium"
                >
                  +
                </el-tag>
                <el-input v-model="pileEndAdd" controls-position="right" style="width: 50%;"
                          placeholder="请输入终点桩号"/>
              </div>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="12">
          <el-col :span="12">
            <el-form-item prop="startLatitudeAndLongitude">
                <span slot="label">
                  <el-tooltip content="起点经纬度利用数据采集系统采集" placement="top">
                    <i class="el-icon-question"></i>
                  </el-tooltip>
                  边坡起点经纬度
                </span>
              <el-input v-model="formParams.startLatitudeAndLongitude" placeholder="请输入起点经纬度，以英文逗号分隔">
                <el-button slot="append" icon="el-icon-location">坐标拾取
                </el-button>
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item prop="endLatitudeAndLongitude">
                <span slot="label">
                  <el-tooltip content="止点经纬度利用数据采集系统采集" placement="top">
                    <i class="el-icon-question"></i>
                  </el-tooltip>
                  边坡止点经纬度
                </span>
              <el-input v-model="formParams.endLatitudeAndLongitude" placeholder="请输入止点经纬度，以英文逗号分隔">
                <el-button slot="append" icon="el-icon-location">坐标拾取
                </el-button>
              </el-input>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="12">
          <el-col :span="12">
            <el-form-item label="建造年度" prop="constructionYear">
              <el-date-picker
                style="width: 100%;"
                v-model="formParams.constructionYear"
                type="year"
                value-format="yyyy"
                placeholder="建造年度"
              >
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="改建年度" prop="renovationYear">
              <el-date-picker
                style="width: 100%;"
                v-model="formParams.renovationYear"
                type="year"
                value-format="yyyy"
                placeholder="改建年度"
              >
              </el-date-picker>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="12">
          <el-col :span="12">
            <el-form-item prop="slopeLength">
                <span slot="label">
                  <el-tooltip content="根据起止点桩号，保存后自动计算" placement="top">
                    <i class="el-icon-question"></i>
                  </el-tooltip>
                  边坡长度(米)
                </span>
              <el-input v-model="formParams.slopeLength" disabled style="width: 100%;"
                        placeholder="请输入边坡长度(米)"/>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="车道数量(个)" prop="laneNum">
              <el-input-number v-model="formParams.laneNum" controls-position="right" :min="0" :max="8" :precision="0"
                               style="width: 100%;" placeholder="请输入车道数量(个)"/>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="12">
          <el-col :span="12">
            <el-form-item label="路基宽度(米)" prop="roadbedWidth">
              <el-input-number v-model="formParams.roadbedWidth" controls-position="right" :min="0" :precision="2"
                               style="width: 100%;" placeholder="请输入路基宽度(米)"/>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="路面宽度(米)" prop="roadWidth">
              <el-input-number v-model="formParams.roadWidth" controls-position="right" :min="0" :precision="2"
                               style="width: 100%;" placeholder="请输入路面宽度(米)"/>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="12">
          <el-col :span="12">
            <el-form-item label="相对位置" prop="relativePosition">
              <el-radio-group v-model="formParams.relativePosition" ref="formRadio1">
                <el-radio v-for="dict in dict.type.side_slope_relative_position" :key="dict.value"
                          :label="dict.value">{{
                    dict.label
                  }}
                </el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="12">
          <el-col :span="24">
            <el-form-item label="面层类型" prop="surfaceLayerType">
              <el-radio-group v-model="formParams.surfaceLayerType" ref="formRadio2">
                <el-radio v-for="dict in dict.type.roadbed_surface_type" :key="dict.value" :label="dict.value">{{
                    dict.label
                  }}
                </el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>

      </div>

      <div class="infoBox" style="margin-bottom: 20px;" v-loading="formLoading">
        <div class="infoTitle">
          边坡类型
        </div>
        <el-row :gutter="12">
          <el-col :span="24">
            <el-form-item label="边坡类型">
              <el-radio-group v-model="slopeType" ref="formRadio8">
                <el-radio label="0">路堤边坡</el-radio>
                <el-radio label="1">路堑边坡</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>
      </div>

      <div class="infoBox" style="margin-bottom: 20px;" v-loading="formLoading" v-show="slopeType === '0'">
        <div class="infoTitle">
          边坡类型（路堤边坡）
        </div>
        <el-row :gutter="12">
          <el-col :span="8">
            <el-form-item label="坡高(米)" prop="embankmentSlopeHeight">
              <el-input-number v-model="formParams.embankmentSlopeHeight" controls-position="right" :min="0"
                               :precision="2" style="width: 100%;" placeholder="请输入坡高(米)"/>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="坡度(度)" prop="embankmentSlope">
              <el-input-number v-model="formParams.embankmentSlope" controls-position="right" :min="0" :precision="2"
                               style="width: 100%;" placeholder="请输入坡度(度)"/>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="分级(级)" prop="embankmentGrade">
              <el-input-number v-model="formParams.embankmentGrade" controls-position="right" :min="0" :precision="2"
                               style="width: 100%;" placeholder="请输入分级(级)"/>
            </el-form-item>
          </el-col>
        </el-row>

        <el-divider></el-divider>

        <el-row :gutter="12">
          <el-col :span="24">
            <el-form-item label="是否临河" prop="embankmentByARiver">
              <el-radio-group v-model="formParams.embankmentByARiver" ref="formRadio3">
                <el-radio :label="'0'">是</el-radio>
                <el-radio :label="'1'">否</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>

        <el-divider></el-divider>

        <el-row :gutter="12" v-show="formParams.embankmentByARiver === '0'">
          <el-col :span="24">
            <el-form-item label="临河地形" prop="embankmentRiversideTerrain">
              <el-radio-group v-model="formParams.embankmentRiversideTerrain" ref="formRadio4">
                <el-radio v-for="dict in dict.type.side_slope_river_terrain" :key="dict.value" :label="dict.value">{{
                    dict.label
                  }}
                </el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>

      </div>

      <div class="infoBox" style="margin-bottom: 20px;" v-loading="formLoading" v-show="slopeType === '1'">
        <div class="infoTitle">
          边坡类型（路堑边坡）
        </div>
        <el-row :gutter="12">
          <el-col :span="8">
            <el-form-item label="坡高(米)" prop="cuttingSlopeHeight">
              <el-input-number v-model="formParams.cuttingSlopeHeight" controls-position="right" :min="0"
                               :precision="2" style="width: 100%;" placeholder="请输入坡高(米)"/>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="坡度(度)" prop="cuttingSlope">
              <el-input-number v-model="formParams.cuttingSlope" controls-position="right" :min="0" :precision="2"
                               style="width: 100%;" placeholder="请输入坡度(度)"/>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="分级(级)" prop="cuttingGrade">
              <el-input-number v-model="formParams.cuttingGrade" controls-position="right" :min="0" :precision="0"
                               style="width: 100%;" placeholder="请输入分级(级)"/>
            </el-form-item>
          </el-col>
        </el-row>

        <el-divider></el-divider>

        <el-row :gutter="12">
          <el-col :span="24">
            <el-form-item label="岩性" prop="cuttingLithology">
              <el-radio-group v-model="formParams.cuttingLithology" ref="formRadio5">
                <el-radio v-for="dict in dict.type.side_slope_lithology" :key="dict.value" :label="dict.value">{{
                    dict.label
                  }}
                </el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>

      </div>

      <div class="infoBox" style="margin-bottom: 20px;" v-loading="formLoading">
        <div class="infoTitle">
          排水设施类型
        </div>
        <el-row :gutter="12">
          <el-col :span="24">
            <el-form-item label="地表排水设施" prop="surfaceDrainageFacilities">
              <el-checkbox-group v-model="formParams.surfaceDrainageFacilities">
                <el-checkbox v-for="dict in dict.type.surface_drainage_facilities" :key="dict.value"
                             :label="dict.value">
                  {{ dict.label }}
                </el-checkbox>
              </el-checkbox-group>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="12">
          <el-col :span="24">
            <el-form-item label="" prop="surfaceDrainageFacilitiesRemark">
              <el-input v-model="formParams.surfaceDrainageFacilitiesRemark" placeholder="其它"/>
            </el-form-item>
          </el-col>
        </el-row>

        <el-divider></el-divider>

        <el-row :gutter="12">
          <el-col :span="24">
            <el-form-item label="地下排水设施" prop="undergroundDrainageFacilities">
              <el-checkbox-group v-model="formParams.undergroundDrainageFacilities">
                <el-checkbox v-for="dict in dict.type.side_slope_underground_drainage_facilities" :key="dict.value"
                             :label="dict.value">
                  {{ dict.label }}
                </el-checkbox>
              </el-checkbox-group>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="12">
          <el-col :span="24">
            <el-form-item label="" prop="undergroundDrainageFacilitiesRemark">
              <el-input v-model="formParams.undergroundDrainageFacilitiesRemark" placeholder="其它"/>
            </el-form-item>
          </el-col>
        </el-row>

      </div>

      <div class="infoBox" style="margin-bottom: 20px;" v-loading="formLoading">
        <div class="infoTitle">
          防护设施类型
        </div>

        <el-row :gutter="12">
          <el-col :span="24">
            <el-form-item label="坡面防护" prop="curDamagedSlopeSurface">
              <el-checkbox-group v-model="formParams.curDamagedSlopeSurface">
                <el-checkbox v-for="dict in dict.type.side_slope_protection" :key="dict.value" :label="dict.value">{{
                    dict.label
                  }}
                </el-checkbox>
              </el-checkbox-group>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="12">
          <el-col :span="24">
            <el-form-item label="" prop="curDamagedSlopeSurfaceRemark">
              <el-input v-model="formParams.curDamagedSlopeSurfaceRemark" placeholder="其它"/>
            </el-form-item>
          </el-col>
        </el-row>

        <el-divider></el-divider>

        <el-row :gutter="12">
          <el-col :span="24">
            <el-form-item label="沿河防护" prop="curDamagedRiverBank">
              <el-checkbox-group v-model="formParams.curDamagedRiverBank">
                <el-checkbox v-for="dict in dict.type.protection_along_the_river" :key="dict.value"
                             :label="dict.value">{{
                    dict.label
                  }}
                </el-checkbox>
              </el-checkbox-group>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="12">
          <el-col :span="24">
            <el-form-item label="" prop="curDamagedRiverBankRemark">
              <el-input v-model="formParams.curDamagedRiverBankRemark" placeholder="其它"/>
            </el-form-item>
          </el-col>
        </el-row>

        <el-divider></el-divider>

        <el-row :gutter="12">
          <el-col :span="24">
            <el-form-item label="支挡设施" prop="curDamagedSupportFacility">
              <el-checkbox-group v-model="formParams.curDamagedSupportFacility">
                <el-checkbox v-for="dict in dict.type.side_slope_support_facilities" :key="dict.value"
                             :label="dict.value">{{
                    dict.label
                  }}
                </el-checkbox>
              </el-checkbox-group>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="12">
          <el-col :span="24">
            <el-form-item label="" prop="curDamagedSupportFacilityRemark">
              <el-input v-model="formParams.curDamagedSupportFacilityRemark" placeholder="其它"/>
            </el-form-item>
          </el-col>
        </el-row>

      </div>

      <div class="infoBox" style="margin-bottom: 20px;" v-loading="formLoading">
        <div class="infoTitle">
          抗震设防等级
        </div>
        <el-row :gutter="12">
          <el-col :span="24">
            <el-form-item label-width="0" prop="seismicFortificationLevel">
              <el-radio-group v-model="formParams.seismicFortificationLevel" ref="formRadio6">
                <el-radio v-for="dict in dict.type.seismic_fortification_level" :key="dict.value" :label="dict.value">
                  {{
                    dict.label
                  }}
                </el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>
      </div>

      <div class="infoBox" style="margin-bottom: 20px;" v-loading="formLoading">
        <div class="infoTitle">
          防洪标准
        </div>
        <el-row :gutter="12">
          <el-col :span="24">
            <el-form-item label-width="0" prop="floodControlStandard">
              <el-radio-group v-model="formParams.floodControlStandard"
                              @input="formRadioChange('floodControlStandard', 'floodControlStandardRemark')"
                              ref="subFormRadio8">
                <el-radio v-for="dict in dict.type.side_slop_flood_control" :key="dict.value" :label="dict.value"
                          style="margin-right: 30px; margin-top: 5px; margin-bottom: 5px;">{{ dict.label }}
                </el-radio>
                <el-radio label="99" style="margin-top: 5px; margin-bottom: 5px;">其它</el-radio>
              </el-radio-group>
              <!-- <el-radio-group v-model="formParams.floodControlStandard" ref="formRadio7">
                <el-radio v-for="dict in dict.type.side_slop_flood_control" :label="dict.value">{{ dict.label }}</el-radio>
              </el-radio-group> -->
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="12" v-show="formParams.floodControlStandard === '99'">
          <el-col :span="24">
            <el-form-item label-width="0" prop="floodControlStandardRemark">
              <el-input v-model="formParams.floodControlStandardRemark" placeholder="其它"/>
            </el-form-item>
          </el-col>
        </el-row>
      </div>

      <div class="infoBox" style="margin-bottom: 20px;" v-loading="formLoading">
        <div class="infoTitle">
          其他需要说明的信息
        </div>
        <el-row :gutter="12">
          <el-col :span="24">
            <el-form-item label-width="0" prop="supplementaryInformation">
              <el-input
                :autosize="{ minRows: 4 }"
                type="textarea"
                placeholder="主要填写表格中未明确提出的其他需要上报的信息。"
                v-model="formParams.supplementaryInformation"
                maxlength="500"
                show-word-limit
              ></el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </div>

      <div class="infoBox" style="margin-bottom: 20px;" v-loading="formLoading">
        <div class="infoTitle">
          照片（边坡典型照片）
        </div>
        <el-row :gutter="12">
          <el-col :span="24">
            <el-form-item label-width="0" prop="riskDescription">
              <div class="imgBox" v-if="formImgSrcList.length > 0">
                <el-timeline>
                  <el-timeline-item v-for="item in formImgDateLine" :key="item.time" :timestamp="item.time"
                                    placement="top"
                                    color="#5cbb7a">
                    <el-card class="imgBoxCard">
                      <div class="cardMain" v-for="itemC in item.data">
                        <div class="imgTitle">
                          <el-tooltip class="item" effect="dark" :content="itemC.name" placement="top">
                            <i class="el-icon-info"></i>
                          </el-tooltip>
                          {{ itemC.name }}
                        </div>
                        <el-image
                          fit="cover"
                          class="img"
                          :src="itemC.url"
                          @click="formImgPreview(itemC.imgUrl)"
                          :preview-src-list="formImgUrlList"
                        ></el-image>
                        <div class="footer">
                          {{`由 ${itemC.createBy} 上传于 ${itemC.createTime}` }}
                        </div>
                      </div>
                    </el-card>
                  </el-timeline-item>
                </el-timeline>
              </div>
              <div class="noneBox" v-else>
                暂无内容
              </div>
            </el-form-item>
          </el-col>
        </el-row>
      </div>

    </el-form>
    <div slot="footer" class="dialog-footer">
      <el-row class="footerTip" v-if="formParams.createName">
        <el-col :span="12" style="display: flex; justify-content: flex-start;">
          采集人：{{ formParams.createName }}
        </el-col>
        <el-col :span="12" style="display: flex; justify-content: flex-end;">
          采集时间：{{ formParams.createTime }}
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<script>
import {deptTreeSelect} from "@/api/tmpl";
import {getTreeByEntity} from "@/api/system/geography";
import {queryHighSlopeForm} from "@/api/disaster/highSlope/highSlope";
import {findFiles} from "@/api/file";
import {listByMaintenanceSectionId} from "@/api/baseData/common/routeLine";

export default {
  props: {
    id: { // 父组件传递的ID
      type: String,
      required: true
    }
  },
  inject: ['oneMap'],
  dicts: [
    'side_slope_relative_position', // 相对位置
    'roadbed_surface_type',  // 面层类型
    'side_slope_river_terrain', // 临河地形
    'side_slope_lithology', // 岩性
    'surface_drainage_facilities', // 地表排水设施
    'side_slope_underground_drainage_facilities', // 地下排水设施
    'side_slope_protection', // 坡面防护
    'protection_along_the_river', // 沿河防护
    'side_slope_support_facilities', // 支挡设施
    'seismic_fortification_level', // 抗震设防等级
    'side_slop_flood_control', // 防洪标准
    'sys_route_grade', // 路线等级
    'process_type'
  ],
  data() {
    return {
      formDialog: false, // 控制弹窗显示
      formTitle: "高边坡信息调查表",
      formParams: {},
      formLoading: false,
      formMaintenanceList: [], // 管养单位列表
      formMaintenanceRenderList: [], // 管养单位渲染列表
      formRoadSectionList: [], // 路段列表
      formRouteList: [], // 路线列表
      /**
       * 区划相关
       */
      divisionsProvincial: '53', // 省级区划
      divisionsProvincialList: [ // 省级区划列表
        {
          disabled: true,
          id: "53",
          label: "云南省",
          level: 1
        }
      ],
      divisionsMunicipal: '', // 市级区划
      divisionsMunicipalList: [], // 市级区划列表
      areaCodeList: [], // 县级区划列表
      slopeType: '0', // 边坡类型
      formImgSrcList: [], // 图片渲染列表
      formImgUrlList: [], // 图片预览列表
      formImgNum: [], // 图片数量（图片集用）
      /**
       * 桩号相关
       */
      pileStartK: '', // 起点桩号K
      pileStartAdd: '', // 起点桩号+
      pileEndK: '', // 终点桩号K
      pileEndAdd: '', // 终点桩号+
    };
  },
  computed: {
    formImgDateLine() {
      let dateLine = []
      if (this.formImgSrcList.length > 0) {
        this.formImgSrcList.forEach(item => {
          let date = item.createTime.split('T')[0]
          if (dateLine.length === 0) {
            dateLine.push({
              time: date,
              data: [item]
            })
          } else {
            let index = dateLine.findIndex(item2 => item2.time === date)
            if (index !== -1) {
              dateLine[index].data.push(item)
            } else {
              dateLine.push({
                time: date,
                data: [item]
              })
            }
          }
        })
        // 时间线排序
        dateLine.sort((a, b) => {
          // 将日期字符串分割并转换为日期对象
          let dateA = new Date(a.time);
          let dateB = new Date(b.time);

          // 比较日期
          return dateA - dateB;
        })
      }
      return dateLine
    }
  },
  methods: {
    // 根据ID获取数据
    async fetchFormData(id) {
      this.formLoading = true
      await queryHighSlopeForm(id).then((res) => {
        if (res.code === 200) {
          this.formParams = JSON.parse(JSON.stringify(res.data))
          if (res.data.pileStartNum) {
            this.pileStartK = res.data.pileStartNum.split('+')[0].split('K')[1]
            this.pileStartAdd = res.data.pileStartNum.split('+')[1]
          }
          if (res.data.pileEndNum) {
            this.pileEndK = res.data.pileEndNum.split('+')[0].split('K')[1]
            this.pileEndAdd = res.data.pileEndNum.split('+')[1]
          }
          if (res.data.embankmentSlopeHeight) {
            this.slopeType = '0'
          } else {
            this.slopeType = '1'
          }
          this.formParams.surfaceDrainageFacilities = this.formArrayChange(res.data.surfaceDrainageFacilities) // 地表排水设施
          this.formParams.undergroundDrainageFacilities = this.formArrayChange(res.data.undergroundDrainageFacilities) // 地下排水设施
          this.formParams.curDamagedSlopeSurface = this.formArrayChange(res.data.curDamagedSlopeSurface) // 坡面防护
          this.formParams.curDamagedRiverBank = this.formArrayChange(res.data.curDamagedRiverBank) // 沿河防护
          this.formParams.curDamagedSupportFacility = this.formArrayChange(res.data.curDamagedSupportFacility) // 支挡设施
          if (res.data.imgIds) {
            this.queryImg(res.data.imgIds)
            this.formImgOwnerId = res.data.imgIds
          } else {
            this.formImgOwnerId = new Date().getTime().toString()
          }

          this.queryRouterList(this.formParams.roadSectionId)

          // 获取区划数据（市级）
          this.queryDivisionsTree()
        }
        this.formLoading = false
      }).catch((err) => {
        this.formLoading = false
        this.$message.error(err)
      })
    },

    // 表单数组转换
    formArrayChange(item) {
      if (item) {
        let check = item.includes(',')
        if (check) {
          return item.split(',')
        } else {
          return [item]
        }
      } else {
        return []
      }
    },

    // 获取路线信息
    queryRouterList(val) {
      listByMaintenanceSectionId({maintenanceSectionId: val}).then(res => {
        if (res.code == 200) {
          this.formRouteList = res.data
        }
      }).catch((err) => {
        this.$message.error(err)
        this.formRouteList = []
      })
    },

    // 获取图片数据
    async queryImg(id) {
      await findFiles({ownerId: id}).then((res) => {
        if (res.code === 200 && res.data.length > 0) {

          this.formImgSrcList = res.data.map(item => ({
            id: item.ownerId + '-' + item.id,
            name: item.originalFilename,
            url: item.thumbUrl,
            imgUrl: item.url,
            remark: item.remark,
            createTime: item.createTime,
            createBy: item.createBy
          }))
          this.formImgUrlList = res.data.map(item => item.url)

        }
      }).catch(() => {
        this.$message.error('图片查询失败，请重新打开表单尝试')
      })
    },

    // 获取管养单位
    queryMaintenanceList() {
      let vo = {
        deptTypeList: [1, 3, 4],
        types: 100
      }
      deptTreeSelect(vo).then((res) => {
        if (res.code === 200) {
          this.formMaintenanceList = res.data
          this.handleMainRender(this.formMaintenanceList)
        }
      }).catch((err) => {
        this.$message.error(err)
        this.formMaintenanceList = []
        this.formMaintenanceRenderList = []
      })
    },

    // 管养单位渲染列表处理
    handleMainRender(data) {
      data.forEach(item => {
        this.formMaintenanceRenderList.push(item)
        if (item.children) {
          this.handleMainRender(item.children)
        }
      })
    },

    // 获取区划树
    queryDivisionsTree() {
      let vo = {
        supCode: this.divisionsProvincial
      }
      getTreeByEntity(vo).then((res) => {
        if (res) {
          this.divisionsMunicipalList = res.data

          // 根据县级获取市级编码
          let check = false
          for (let i = 0; i < this.divisionsMunicipalList.length; i++) {
            if (check) break
            for (let j = 0; j < this.divisionsMunicipalList[i].children.length; j++) {
              if (this.divisionsMunicipalList[i].children[j].id === this.formParams.areaCode) {
                this.divisionsMunicipal = this.divisionsMunicipalList[i].id
                this.areaCodeList = this.divisionsMunicipalList[i].children
                check = true
                break
              }
            }
          }

          console.log(this.areaCodeList)
        }
      })
    },

    // 点击预览图片时
    formImgPreview(url) {
      let index = this.formImgUrlList.findIndex(item => item === url)
      if (index !== -1) {
        let moveUrl = this.formImgUrlList.splice(index, this.formImgUrlList.length - index)
        this.formImgUrlList.unshift(...moveUrl)
      }
    },
  },
  mounted() {
    if (this.id) {
      this.fetchFormData(this.id);
      // 获取管养单位数据
      this.queryMaintenanceList()
    }
  }
};
</script>

<style lang="scss" scoped>
@import "@/assets/styles/common.scss";

.formDialog {
  .dialog-footer {
    width: 99%;

    .footerTip {
      color: #ffffff;
      font-size: 12px;
    }
  }

  .titleBox {
    height: 50px;
    display: flex;
    flex-direction: row;
    align-items: center;

    .title {
      font-size: 16px;
      margin: 0;
    }

    .subTitle {
      margin-left: 15px;
      font-size: 12px;
      color: #888888;
    }
  }

  .infoBox {
    padding: 15px 15px 0 15px;
    box-sizing: border-box;
    border-radius: 6px;
    border: 1px solid #C4C4C4;
    position: relative;

    .infoTitle {
      user-select: none;
      position: absolute;
      top: 0;
      left: 0;
      padding: 0 10px;
      font-size: 14px;
      line-height: 16px;
      font-weight: bold;
      transform: translateX(15px) translateY(-50%);
      background-color: #02063f;
    }

    .imgBox {
      height: auto;
      width: 100%;

      ::v-deep .el-card__body {
        width: 100%;
        display: flex;
        flex-direction: row;
        flex-wrap: wrap;
        align-content: flex-start;
      }

      ::v-deep .el-card {
        background-color: rgba(1, 102, 254, 0.2);
      }

      .imgBoxCard {
        width: 100%;

        .cardMain {
          height: 260px;
          width: 33%;
          box-sizing: border-box;
          padding: 0 10px;
          display: flex;
          flex-direction: column;
          position: relative;

          .imgDeleteBtn {
            position: absolute;
            z-index: 1;
            top: 20%;
            right: 5%;
          }

          .imgTitle {
            height: 28px;
            width: 100%;
            color: #BBBBBB;
            font-size: 16px;
            font-weight: bold;
            white-space: nowrap;
            text-overflow: ellipsis;
            overflow: hidden;
          }

          .img {
            height: calc(100% - (28px + 28px));
            width: 100%;
            padding: 10px 0;
            position: relative;
            z-index: 0;
          }

          .footer {
            height: 28px;
            color: #888888;
            font-size: 14px;
          }
        }
      }
    }

    .noneBox {
      user-select: none;
      height: 200px;
      width: 100%;
      display: flex;
      justify-content: center;
      align-items: center;
      color: #888888;
    }
  }
}

::v-deep .el-tag--plain.el-tag--info {
  background-color: #BBBBBB;
  }

::v-deep .el-textarea.is-disabled .el-textarea__inner {
  background-color: rgba(1, 102, 254, 0.2);
}
</style>
