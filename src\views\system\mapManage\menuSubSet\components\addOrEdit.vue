<template>
  <el-form ref="form" :model="ruleForm" :rules="rules" label-width="110px">
    <el-row :gutter="20" :style="{ pointerEvents: readonly ? 'none' : '' }">
      <el-col :span="12" :offset="0">
        <el-form-item label="属性类型名称" prop="attributeTypeName">
          <el-input v-model="ruleForm.attributeTypeName" placeholder="请输入属性类型名称" />
        </el-form-item>
      </el-col>
      <el-col :span="12" :offset="0">
        <el-form-item label="属性类型代码" prop="attributeTypeCode">
          <el-input v-model="ruleForm.attributeTypeCode" placeholder="请输入属性类型代码" />
        </el-form-item>
      </el-col>

      <el-col :span="12" :offset="0">
        <el-form-item label="填充颜色" prop="interiorColour">
          <el-color-picker v-model="ruleForm.interiorColour" show-alpha />
        </el-form-item>
      </el-col>
      <el-col :span="12" :offset="0">
        <el-form-item label="边框颜色" prop="borderColour">
          <el-color-picker v-model="ruleForm.borderColour" show-alpha />
        </el-form-item>
      </el-col>
      <el-col :span="12" :offset="0">
        <el-form-item label="边框粗细" prop="borderWidth">
          <el-input-number v-model="ruleForm.borderWidth" :step="1" step-strictly style="width: 100%;" />
        </el-form-item>
      </el-col>
      <el-col :span="12" :offset="0">
        <el-form-item label="图标" prop="iconId">
          <ImageUpload v-model="ruleForm.iconId" :limit="1" :owner-id="enterOwnerId" />
        </el-form-item>
      </el-col>
    </el-row>
    <el-form-item style="margin: auto;display: flex;justify-content: center;">
      <el-button type="primary" @click="onSubmit" v-loading="loading" v-if="!readonly">确定</el-button>
      <el-button @click="onCancel">取消</el-button>
    </el-form-item>
  </el-form>
</template>

<script>
import { addMenuSubSet, updateMenuSubSet } from "@/api/oneMap/menuSubSet";
import { createIdWorker } from '@/api/baseData/common'

export default {
  props: {
    form: {
      type: Object,
      default: {},
    },
    id: {
      type: [String, Number],
      default: ''
    },
    readonly: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      ruleForm: {
        oneMapShow: 1,
      },
      rules: {
        attributeTypeName: [
          { required: true, message: '请输入属性类型名称', trigger: 'blur' },
        ],
      },
      loading: false,
      enterOwnerId: '',
    }
  },
  created() {
    this.ruleForm = { ...this.form }
    createIdWorker().then(res => {
      if (res.code === 200) this.enterOwnerId = res.data
    })
  },
  methods: {
    // 提交
    onSubmit() {
      this.$refs.form.validate(vali => {
        if (!vali) return
        this.ruleForm.iconId = Object.prototype.toString.call(this.ruleForm.iconId) === "[object Array]" ? this.ruleForm.iconId[0] : this.ruleForm.iconId
        let request = this.ruleForm.id ? updateMenuSubSet(this.ruleForm) : addMenuSubSet(this.ruleForm)
        let msg = this.ruleForm.id ? '编辑成功' : '新增成功';
        this.loading = true;
        request.then(res => {
          if (res.code == 200) {
            this.$modal.msgSuccess(msg);
            this.$emit('refresh')
            this.onCancel();
          }
        }).finally(() => {
          this.loading = false;
        })
      })
    },
    // 取消关闭
    onCancel() {
      this.$emit('close', false)
    },
  },
};
</script>

<style lang="scss" scoped></style>
