<template>
	<div
		class="drawing-box"
		:style="oneMap ? 'background:unset' : 'background: #fff;'"
		v-loading="loading"
		element-loading-background="rgba(0, 0, 0, 0.01)"
	>
		<div
			class="drawing-box-card-item"
			v-loading="item.loading"
			:element-loading-background="oneMap ? 'rgba(2, 10, 30, 0.5)' : ''"
			:style="{
				height: boxHeight,
				background: oneMap ? '#0166fe33' : '',
				border: oneMap ? '1px solid #0166fe' : '1px solid #E4E7ED',
			}"
			v-for="(item, index) in list"
			:key="item.moduleName + index + '-' + item.timeKey"
		>
			<div class="header" :style="oneMap ? 'border-bottom: 1px solid #0166fe' : ''">
				<i></i>
				<span :style="oneMap ? 'color: #fff' : ''">{{ item.moduleName }}</span>
			</div>
			<div class="body">
				<div class="body-item" v-for="(i, idx) in item.children" :key="i.categoryName + idx">
					<span
						class="label"
						@click="handleDetail(i, item.moduleName, index)"
						:style="oneMap ? 'color: #fff' : ''"
					>
						{{ i.categoryName }}
						<el-tag :type="i.fileList.length === 0 ? 'danger' : 'success'" style="font-size: 12px">
							{{ i.fileList.length || 0 }}
						</el-tag>
					</span>
					<el-button
						v-hasPermi="['baseData:drawing:upload']"
						type="primary"
						icon="el-icon-upload2"
						@click="openUpload(i, item.moduleName, index)"
					>
						上传
					</el-button>
				</div>
			</div>
			<div class="footer" :style="oneMap ? 'border-top: 1px solid #0166fe' : ''">
				<!-- <span style="color: #409EFF;">{{ '全部(' + item.total + ')' }}</span> -->
				<!-- <i></i> -->
				<span style="color: #67c23a">{{ '已上传(' + item.upload + ')' }}</span>
				<i></i>
				<span style="color: #f56c6c">{{ '未上传(' + item.noUpload + ')' }}</span>
			</div>
		</div>
		<el-dialog
			:visible.sync="showVisible"
			:title="dialogTitle"
			@close="closeUpload"
			width="1176px"
			append-to-body
      :class="oneMap ? 'dark' : ''"
		>
			<file-upload v-if="showVisible" :upLoadBind="upLoadBind" :oneMap="oneMap"></file-upload>
		</el-dialog>
		<el-dialog
			:visible.sync="showDetailVisible"
			:title="detailTitle"
			@close="closeDetail"
			width="1176px"
			append-to-body
			:class="oneMap ? 'dark one-map' : ''"
		>
			<div class="button-list">
				<el-button
					v-hasPermi="['baseData:drawing:upload']"
					type="primary"
					icon="el-icon-upload2"
					@click="detailUpload()"
				>
					上传
				</el-button>
				<el-button
					v-hasPermi="['baseData:drawing:download']"
					type="success"
					icon="el-icon-download"
					@click="detailDownloadAll()"
					:disabled="ids.length === 0"
				>
					下载
				</el-button>
				<el-button
					v-hasPermi="['baseData:drawing:remove']"
					type="danger"
					icon="el-icon-delete"
					@click="detailRemoveAll()"
					:disabled="ids.length === 0"
				>
					删除
				</el-button>
			</div>
			<div class="detail-body">
				<el-table
					v-loading="tableLoading"
					ref="table"
					height="99%"
					border
					:data="detailData"
					:header-cell-style="{ height: '36px' }"
					:row-style="rowStyle"
					@selection-change="handleSelectionChange"
					@row-click="handleRowClick"
				>
					<el-table-column
						type="selection"
						width="50"
						align="center"
						:selectable="checkSelectable"
					/>
					<el-table-column label="序号" type="index" width="50" align="center" />
					<el-table-column
						label="文件名称"
						align="center"
						prop="fileName"
						min-width="180"
						show-overflow-tooltip
					/>
					<el-table-column
						label="文件类型"
						align="center"
						prop=""
						min-width="80"
						show-overflow-tooltip
					>
						<template slot-scope="scope">
							{{ scope.row.fileName.split('.').pop() }}
						</template>
					</el-table-column>
					<el-table-column
						label="文件大小（KB）"
						align="center"
						prop="fileSize"
						min-width="80"
						show-overflow-tooltip
					>
						<template slot-scope="scope">
							{{ (Number(scope.row.fileSize) / 1024).toFixed(2) }}
						</template>
					</el-table-column>
					<el-table-column
						label="操作"
						align="center"
						prop="bridgeName"
						min-width="100"
						show-overflow-tooltip
					>
						<template slot-scope="scope">
							<el-link
								style="margin-right: 10px"
								type="primary"
								:underline="false"
								@click.stop="detailPreview(scope.row)"
							>
								预览
							</el-link>
							<el-link
								v-hasPermi="['baseData:drawing:download']"
								style="margin-right: 10px"
								type="primary"
								:underline="false"
								:key="downloadKey + '123'"
								@click.stop="detailDownload(scope.row)"
								:disabled="scope.row.downloadLoading"
							>
								{{ scope.row.downloadLoading ? '下载中...' : '下载' }}
							</el-link>
							<el-link
								style="margin-right: 10px"
								type="primary"
								:underline="false"
								@click.stop="openByBrowser(scope.row)"
								:key="downloadKey + '456'"
							>
								浏览器打开
							</el-link>
							<el-link
								v-hasPermi="['baseData:drawing:remove']"
								type="danger"
								:underline="false"
								@click.stop="detailRemove(scope.row)"
								:disabled="scope.row.downloadLoading"
								:key="downloadKey + '456'"
							>
								删除
							</el-link>
						</template>
					</el-table-column>
				</el-table>
			</div>
			<!-- <pagination :total="total" :page.sync="pageNum" :limit.sync="pageSize" @pagination="getDetailList" /> -->
			<el-image
				style="display: none; width: 100px; height: 100px"
				:src="imageUrl"
				:preview-src-list="[imageUrl]"
				ref="imageRefs"
			></el-image>
			<div v-if="isPreview && !wordUrl" class="mask"></div>
			<div v-if="isPreview" class="preview-box">
				<i class="el-icon-close body-close" @click="closePreview"></i>
				<div ref="container" class="box-container" v-if="pdfUrl">
					<iframe :src="pdfUrl" style="width: 100%; height: 100%; border-radius: 6px" />
					<!-- <VueOfficePdf :src="pdfUrl" style="width: 100%; height: calc(100% - 30px); border-radius: 6px;" /> -->
				</div>
				<div ref="container" class="box-container" v-if="excelUrl">
					<Luckysheet :file-url="excelUrl" style="width: 100%; height: 100%; border-radius: 6px" />
				</div>
				<div ref="container" class="box-container" v-if="wordUrl">
					<vue-office-docx :src="wordUrl" style="width: 100%; height: 100%; border-radius: 6px" />
				</div>
			</div>
		</el-dialog>
	</div>
</template>

<script>
import { getListByEntity, getModuleFileCount, isExistFileDraw } from '@/api/system/moduleFile.js'
import { getFileSubListByEntity } from '@/api/system/moduleFileSub.js'
import { getOneByCode } from '@/api/system/moduleType.js'
import { getListByEntity as getListByEntity2 } from '@/api/system/fileUpload.js'
import FileUpload from '@/components/Drawing/upload.vue'
import { removeFile, addUploadRecord, removeUploadRecord } from '@/api/system/fileUpload.js'
import { findFiles, updateFileSort, updateFileRemark } from '@/api/file/index.js'
import axios from 'axios'
import Luckysheet from '@/components/Luckysheet'
import VueOfficeDocx from '@vue-office/docx'
import VueOfficePdf from '@vue-office/pdf'
import { mapGetters } from 'vuex'

export default {
	name: 'cardBox',
	inject: ['oneMap'],
	components: {
		FileUpload,
		Luckysheet,
		VueOfficeDocx,
		VueOfficePdf,
	},
	props: {
		typeCode: {
			type: String,
			default: '',
		},
		bizId: {
			type: String,
			default: '0',
		},
		showFileName: {
			type: Boolean,
			default: true,
		},
		forView: {
			type: Boolean,
			default: false,
		},
		assetType: {
			type: Number,
			default: null,
		},
		fileList: [],
		boxHeight: {
			type: String,
			default: '270px',
		},
	},
	data() {
		return {
			loading: false,
			cardList: [],
			list: [],
			typeId: null,
			dialogTitle: '',
			showVisible: false,
			upLoadBind: {
				limit: 0,
				fileSize: 0,
				fileType: [],
				fileSubId: '',
				bizId: '',
				ownerIdList: [],
				assetType: null,
			},
			currentItem: {},
			showDetailVisible: false,
			detailTitle: '',
			total: 0,
			detailData: [],
			ids: [],
			selectdTables: [],
			detailItem: {},
			tableLoading: false,
			imageUrl: '',
			isPreview: false,
			pdfUrl: '',
			excelUrl: '',
			wordUrl: '',
			downloadKey: '',
		}
	},
	methods: {
		async init() {
			this.loading = true
			await this.fetchList()
			await this.getList()
			await this.getListItem()
			this.list = this.cardList
			this.list.map((el) => {
				el.total = el.children.length
				el.upload = el.children.filter((i) => i.fileList.length > 0).length
				el.noUpload = el.children.filter((i) => i.fileList.length === 0).length
			})
			this.loading = false
		},
		async fetchList() {
			try {
				const res = await getOneByCode(this.typeCode)
				this.typeId = res.data.id
			} catch (error) {
				console.error('获取typeId错误:', error)
				return
			}

			try {
				const res = await getListByEntity({ typeId: this.typeId })
				this.cardList = res.data
			} catch (error) {
				console.error('获取文件模块错误 :', error)
			}
		},
		async getList() {
			try {
				for (let index = 0; index < this.cardList.length; index++) {
					const el = this.cardList[index]
					const res = await getFileSubListByEntity({ moduleId: el.id })
					if (res.code === 200) {
						el.children = res.data
					}
				}
			} catch (error) {
				console.error('获取文件子模块错误 :', error)
			}
		},
		async getListItem() {
			try {
				for (let index = 0; index < this.cardList.length; index++) {
					const element = this.cardList[index]
					for (let i = 0; i < element.children.length; i++) {
						const el = element.children[i]
						const r = await getListByEntity2({ bizId: this.bizId, fileSubId: el.id })
						if (r.code === 200) {
							el.fileList = r.data || []
						}
					}
				}
			} catch (error) {
				console.error('获取文件子模块错误 :', error)
			}
		},

		/* ------------------------- 详情窗口相关方法 ------------------------ */
		handleDetail(i, name, index) {
			this.detailTitle = name + '-' + i.categoryName
			this.detailItem = i
			this.detailData = i.fileList
			this.total = this.detailData.length
			this.currentItem = {
				index: index,
				item: i,
			}
			this.showDetailVisible = true
		},
		// 多选框选中数据
		handleSelectionChange(selection) {
			this.selectdTables = selection
			this.ids = selection.map((item) => item.ownerId)
		},
		// 表格点击勾选
		handleRowClick(row) {
			if (row.downloadLoading) return
			row.isSelected = !row.isSelected
			this.$refs.table.toggleRowSelection(row)
		},
		// 勾选高亮
		rowStyle({ row, rowIndex }) {
			if (this.ids.includes(row.ownerId)) {
				return { 'background-color': '#b7daff', color: '#333' }
			} else {
				return { 'background-color': '#fff', color: '#333' }
			}
		},
		// 关闭详情窗口
		async closeDetail() {
			const currentCard = this.list[this.currentItem.index]
			currentCard.loading = true
			const obj = currentCard.children.find((item) => item.id === this.currentItem.item.id)
			try {
				const r = await getListByEntity2({ bizId: this.bizId, fileSubId: obj.id })
				if (r.code === 200) {
					obj.fileList = r.data || []
					currentCard.total = currentCard.children.length
					currentCard.upload = currentCard.children.filter((i) => i.fileList.length > 0).length
					currentCard.noUpload = currentCard.children.filter((i) => i.fileList.length === 0).length
					this.$nextTick(() => {
						currentCard.loading = false
						currentCard.timeKey = new Date().getTime()
						this.$forceUpdate()
					})
				}
			} catch (error) {
				currentCard.loading = false
			}
			this.detailTitle = ''
			this.detailItem = {}
			this.detailData = []
			this.total = 0
			this.currentItem = {}
			this.ids = []
			this.selectdTables = []
			this.showDetailVisible = false
		},
		// 详情-上传
		detailUpload() {
			this.dialogTitle = '上传' + this.detailTitle
			this.upLoadBind = this.getComponentProps(this.detailItem)
			this.upLoadBind.value = undefined
			this.showVisible = true
		},
		// 详情-批量删除
		async detailRemoveAll() {
			this.$confirm('确定删除所选文件吗？', '提示', {
				confirmButtonText: '确定',
				cancelButtonText: '取消',
				type: 'warning',
			}).then(async () => {
				const promises = []
				for (let i = 0; i < this.selectdTables.length; i++) {
					const element = this.selectdTables[i]
					// 将每个异步操作添加到数组中
					promises.push(this.removeFun(element))
				}
				// 等待所有异步操作完成
				await Promise.all(promises)

				const currentCard = this.list[this.currentItem.index]
				const obj = currentCard.children.find((item) => item.id === this.currentItem.item.id)
				const r = await getListByEntity2({ bizId: this.bizId, fileSubId: obj.id })
				if (r.code === 200) {
					this.detailData = r.data || []
					this.total = this.detailData.length
					this.$refs.table.clearSelection()
					this.selectdTables = []
					this.ids = []
				}
			})
		},
		// 详情-删除
		async detailRemove(file) {
			this.$confirm('确定删除该文件吗？', '提示', {
				confirmButtonText: '确定',
				cancelButtonText: '取消',
				type: 'warning',
			}).then(() => {
				this.removeFun(file)
			})
		},
		// 删除方法
		async removeFun(file) {
			if (!file.fileId) return
			const findex = this.detailData.map((f) => f.fileId).indexOf(file.fileId)
			if (findex > -1) {
				if (this.bizId === '' && this.detailItem.id === 0) {
					const res = removeFile(file.fileId)
					if (res.code === 200) {
						this.detailData.splice(findex, 1)
						this.$message({
							type: 'success',
							message: '删除成功!',
						})
						this.changeStatus()
					}
				} else {
					await removeUploadRecord({
						fileId: file.fileId,
						assetType: this.assetType,
						fileSubId: this.detailItem.id,
					})
					const r = await removeFile(file.fileId)
					if (r.code === 200) {
						this.detailData.splice(findex, 1)
						this.$message({
							type: 'success',
							message: '删除成功!',
						})
						this.changeStatus()
					}
				}
			}
		},
		// 详情-批量下载
		detailDownloadAll() {
			if (this.selectdTables.length === 0) {
				this.$message.error('请选择要下载的文件')
				return
			}
			this.selectdTables.forEach((item) => {
				this.detailDownload(item)
			})
			this.selectdTables = []
		},
		// 详情-下载
		async detailDownload(item) {
			const tempArr = []
			let ownerId = item.ownerId
			item.downloadLoading = true
			this.downloadKey = new Date().getTime()
			item.isSelected = false
			this.$refs.table.toggleRowSelection(item)
			const { data } = await findFiles({ ownerId })
			tempArr.push(
				...data.map((file) => {
					return {
						...file,
						name: file.originalFilename,
						url: file.url,
						ownerId: file.ownerId,
						sort: file.sort,
					}
				})
			)
			let file = tempArr[0]
			const url = file.url || (file.response && file.response.url)
			if (!url) {
				this.$message.error('文件无法下载，未找到文件的URL')
				return
			}

			try {
				// 创建一个不包含 Authorization 的请求头
				const downloadHeaders = {
					...this.headers,
					'Content-Disposition': 'attachment', // 添加强制下载头
					'Content-Type': 'application/octet-stream', // 使用通用的二进制流类型
				}
				delete downloadHeaders.Authorization

				// 使用 axios 进行下载，因为它可以自动处理跨域认证
				const response = await axios({
					url: url,
					method: 'GET',
					responseType: 'blob',
					headers: downloadHeaders,
				})

				// 从响应头中获取文件类型
				const contentType = response.headers['content-type']
				const blob = new Blob([response.data], { type: contentType || 'application/octet-stream' })

				// 创建一个隐藏的 iframe 来处理下载
				const iframe = document.createElement('iframe')
				iframe.style.display = 'none'
				document.body.appendChild(iframe)

				const originalFileName = this.getFileName(file.name) || 'download'
				const fileExtension = originalFileName.split('.').pop()
				let fileName = this.downloadName || originalFileName

				// 如果 fileName 已经有后缀，去掉后缀
				if (fileName.includes('.')) {
					fileName = fileName.substring(0, fileName.lastIndexOf('.'))
				}

				const timestamp = this.needTime ? `_${this.$moment().format('YYYY-MM-DDTHH_mm_ss')}` : ''
				const finalFileName = `${fileName}${timestamp}.${fileExtension}`

				// 在 iframe 中创建 blob URL 并触发下载
				const iframeWindow = iframe.contentWindow
				const blobUrl = iframeWindow.URL.createObjectURL(blob)
				const link = iframeWindow.document.createElement('a')
				link.href = blobUrl
				link.download = finalFileName
				link.click()

				// 清理资源
				setTimeout(() => {
					iframeWindow.URL.revokeObjectURL(blobUrl)
					document.body.removeChild(iframe)
				}, 1000)
				item.downloadLoading = false
				this.downloadKey = new Date().getTime()
			} catch (error) {
				console.error('下载文件时发生错误:', error)
				this.$message.error('下载文件失败，请重试')
				item.downloadLoading = false
				this.downloadKey = new Date().getTime()
			}
		},
		// 获取文件名称
		getFileName(name) {
			// 如果是url那么取最后的名字 如果不是直接返回
			if (name.lastIndexOf('/') > -1) {
				return name.slice(name.lastIndexOf('/') + 1)
			} else {
				return name
			}
		},
		// 详情-预览
		async detailPreview(item) {
			const tempArr = []
			let ownerId = item.ownerId
			const { data } = await findFiles({ ownerId })
			tempArr.push(
				...data.map((file) => {
					return {
						...file,
						name: file.originalFilename,
						url: file.url,
						ownerId: file.ownerId,
						sort: file.sort,
					}
				})
			)
			let file = tempArr[0]

			const contentType = file.contentType

			// 获取文件扩展名
			let fileExt = ''
			if (file.ext) {
				fileExt = file.ext.toLowerCase()
			} else if (file.name) {
				fileExt = file.name.split('.').pop().toLowerCase()
			}

			if (contentType) {
				if (contentType.startsWith('image/')) {
					this.imageUrl = file.url
					this.$refs.imageRefs.showViewer = true
				} else {
					switch (contentType) {
						case 'application/pdf':
							const pdfjsURL = '/pdfjs/web/viewer.html?file='
							// 确保 file 参数是完整的绝对 URL
							const pdfFileUrl = encodeURIComponent(file.url)
							this.pdfUrl = pdfjsURL + pdfFileUrl
							this.isPreview = true
							this.$nextTick(() => {
								this.addWatermark(this.nickName, this.$refs.container)
							})
							break
						case 'application/vnd.ms-excel':
						case 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet':
							this.excelUrl = file.url
							this.isPreview = true
							this.$nextTick(() => {
								this.addWatermark(this.nickName, this.$refs.container)
							})
							break
						case 'application/msword':
						case 'application/vnd.openxmlformats-officedocument.wordprocessingml.document':
							this.wordUrl = file.url
							this.isPreview = true
							this.$nextTick(() => {
								this.addWatermark(this.nickName, this.$refs.container)
							})
							break
						case 'application/zip':
						case 'application/x-zip-compressed':
						case 'application/x-rar-compressed':
						case 'application/x-7z-compressed':
						case 'application/x-tar':
						case 'application/gzip':
						case 'application/x-gzip':
						case 'application/vnd.openxmlformats-officedocument.presentationml.presentation':
							this.$modal.msgError('此格式暂不支持预览，请自行下载')
							break
						// case '':
						//   this.$modal.msgError('该文件以损坏，无法预览')
						//   break;
					}
				}
				return
			}

			if (fileExt) {
				switch (fileExt) {
					case 'pdf':
						const pdfjsURL = '/pdfjs/web/viewer.html?file='
						// 确保 file 参数是完整的绝对 URL
						const pdfFileUrl = encodeURIComponent(file.url)
						this.pdfUrl = pdfjsURL + pdfFileUrl
						this.isPreview = true
						this.$nextTick(() => {
							this.addWatermark(this.nickName, this.$refs.container)
						})
						break
					case 'doc':
					case 'docx':
						this.wordUrl = file.url
						this.isPreview = true
						this.$nextTick(() => {
							this.addWatermark(this.nickName, this.$refs.container)
						})
						break
					case 'xls':
					case 'xlsx':
						this.excelUrl = file.url
						this.isPreview = true
						this.$nextTick(() => {
							this.addWatermark(this.nickName, this.$refs.container)
						})
						break
					case 'zip':
					case 'rar':
					case '7z':
					case 'tar':
					case 'gz':
					case 'ppt':
					case 'pptx':
						this.$modal.msgError('此格式暂不支持预览，请自行下载')
						break
					case 'jpg':
					case 'jpeg':
					case 'png':
					case 'gif':
					case 'bmp':
					case 'webp':
						this.imageUrl = file.url
						this.$refs.imageRefs.showViewer = true
						break
				}
				return
			}
		},
		// 关闭预览
		closePreview() {
			this.isPreview = false
			this.pdfUrl = ''
			this.excelUrl = ''
			this.wordUrl = ''
		},
		// 详情-禁用表格选择框
		checkSelectable(row) {
			return !row.downloadLoading
		},

		/* ------------------------- 上传按钮 ------------------------ */
		openUpload(i, name, index) {
			this.dialogTitle = '上传' + name + '-' + i.categoryName
			this.upLoadBind = this.getComponentProps(i)
			this.currentItem = {
				index: index,
				item: i,
			}
			this.showVisible = true
		},
		async closeUpload() {
			if (this.detailTitle) {
				this.showVisible = false
				this.tableLoading = true
				const currentCard = this.list[this.currentItem.index]
				const obj = currentCard.children.find((item) => item.id === this.currentItem.item.id)
				const r = await getListByEntity2({ bizId: this.bizId, fileSubId: obj.id })
				if (r.code === 200) {
					this.detailData = r.data || []
					this.total = this.detailData.length
					this.$refs.table.clearSelection()
					this.selectdTables = []
					this.ids = []
				}
				this
				this.tableLoading = false
			} else {
				this.showVisible = false
				const currentCard = this.list[this.currentItem.index]
				currentCard.loading = true
				const obj = currentCard.children.find((item) => item.id === this.currentItem.item.id)
				try {
					const r = await getListByEntity2({ bizId: this.bizId, fileSubId: obj.id })
					if (r.code === 200) {
						obj.fileList = r.data || []
						currentCard.total = currentCard.children.length
						currentCard.upload = currentCard.children.filter((i) => i.fileList.length > 0).length
						currentCard.noUpload = currentCard.children.filter(
							(i) => i.fileList.length === 0
						).length
						this.$nextTick(() => {
							currentCard.loading = false
							currentCard.timeKey = new Date().getTime()
							this.dialogTitle = ''
							this.upLoadBind = {}
							this.currentItem = {}
							this.$forceUpdate()
						})
					}
				} catch (error) {
					currentCard.loading = false
				}
			}
		},

		/* ------------------------- 公共方法 ------------------------ */
		getComponentProps(item) {
			return {
				limit: item.limit ? item.limit : undefined,
				fileSize: item.fileSize ? item.fileSize : undefined,
				fileType:
					item.fileTypeArray && item.fileTypeArray.length > 0 ? item.fileTypeArray : undefined,
				fileSubId: Number(item.id),
				bizId: this.bizId,
				storagePath: '/ylzx',
				showFileName: this.showFileName,
				forView: this.forView,
				assetType: this.assetType,
				value: item.fileList.map((i) => i.ownerId),
				typeCode: this.typeCode,
			}
		},
		// 修改图纸状态
		async changeStatus() {
			let hasFiles = false
			const res = await getModuleFileCount({ bizId: this.bizId })
			res.data?.find((el) => el.count > 0) ? (hasFiles = true) : (hasFiles = false)
			isExistFileDraw({
				assetType: this.assetType,
				bizId: this.bizId,
				isExistFileDraw: hasFiles ? '1' : '2',
			})
		},
		// Canvas 绘制水印
		addWatermark(text, container = document.body) {
			const canvas = document.createElement('canvas')
			canvas.width = 200
			canvas.height = 150
			canvas.style.display = 'none'

			const ctx = canvas.getContext('2d')
			ctx.font = '16px Arial'
			ctx.fillStyle = 'rgba(200, 200, 200, 0.3)'
			ctx.rotate((-20 * Math.PI) / 180)
			ctx.fillText(text, 10, 100)

			const watermarkDiv = document.createElement('div')
			watermarkDiv.style.position = 'fixed'
			watermarkDiv.style.top = '0'
			watermarkDiv.style.left = '0'
			watermarkDiv.style.width = '100%'
			watermarkDiv.style.height = '100%'
			watermarkDiv.style.pointerEvents = 'none'
			watermarkDiv.style.backgroundImage = `url(${canvas.toDataURL()})`
			watermarkDiv.style.zIndex = '9999'

			container.appendChild(watermarkDiv)
		},
		async openByBrowser(row){
			const { data } = await findFiles({ ownerId: row.ownerId })
			if(Array.isArray(data) && data[0].url){
				window.open(data[0].url, '_blank')
			}
		}
	},
	mounted() {
		this.init()
	},
	watch: {
		bizId: {
			handler() {
				this.init()
			},
			immediate: true,
		},
	},
	computed: {
		...mapGetters(['nickName']),
	},
}
</script>

<style lang="scss" scoped>
@import '@/assets/styles/common.scss';

.drawing-box {
	width: 100%;
	// max-height: 100%;
	display: grid;
	grid-template-columns: repeat(3, 1fr);
	grid-gap: 15px;
	padding: 15px;
	box-sizing: border-box;
	position: relative;
	overflow: auto;

	.drawing-box-card-item {
		width: 100%;
		// height: 270px;
		box-shadow: 0px 4px 10px 0px rgba(0, 0, 0, 0.1);
		border-radius: 4px 4px 4px 4px;
		border: 1px solid #e5e6eb;
		background: #fff;

		&:hover {
			box-shadow: 0px 4px 10px 0px rgba(64, 158, 255, 0.2);
		}

		.header {
			width: calc(100% - 30px);
			height: 48px;
			margin: 0 15px;
			border-bottom: 1px solid #e5e6eb;
			display: flex;
			align-items: center;

			i {
				width: 4px;
				height: 15px;
				background: #409eff;
				border-radius: 2px;
				margin-right: 8px;
			}

			span {
				color: #333;
				font-size: 14px;
			}
		}

		.body {
			width: calc(100% - 30px);
			height: calc(100% - 98px);
			margin: 0 15px;
			overflow: auto;

			.body-item {
				width: 100%;
				min-height: 28px;
				padding: 0 10px;
				margin-top: 10px;
				color: #333;
				display: flex;
				align-items: center;
				justify-content: space-between;
				flex-wrap: nowrap;
				font-size: 12px;

				.label {
					font-size: 14px;
					cursor: pointer;

					&:hover {
						color: #409eff;
					}
				}
			}
		}

		.footer {
			width: calc(100% - 30px);
			height: 48px;
			margin: 0 15px;
			border-top: 1px solid #e5e6eb;
			display: flex;
			align-items: center;
			justify-content: space-evenly;

			i {
				width: 1px;
				height: 16px;
				border-left: 1px solid #d8d8d8;
			}

			span {
				font-size: 0.75vw;
			}
		}
	}
}

.button-list {
	width: 100%;
	display: flex;
	align-items: center;
	margin-bottom: 15px;
}

.detail-body {
	width: 100%;
	height: 45vh;
}

.mask {
	position: fixed;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	background-color: rgba(0, 0, 0, 0.5);
	z-index: 999;
}

.preview-box {
	width: 80vw;
	height: 80vh;
	position: fixed;
	top: 50%;
	left: 50%;
	// 使用 transform 向上和向左移动自身宽度和高度的一半
	transform: translate(-50%, -50%);
	z-index: 1000;
	border-radius: 6px;

	.body-close {
		position: absolute;
		top: 5px;
		right: 5px;
		height: 23px;
		width: 23px;
		z-index: 9999;
		font-size: 21px;
		color: #fff;
		margin: 5px;
		border: 1px solid #909399;
		border-radius: 4px;
		background: #909399;
		cursor: pointer;
	}

	.box-container {
		position: relative;
		width: 100%;
		height: 100%;
		overflow: hidden;
	}
}

.dark {
	::v-deep .el-dialog {
		background-color: rgba(4, 17, 48, 0.8);
		box-shadow: inset 0px 0px 10px 0px #3662ec;
		border-radius: 10px 10px 10px 10px;
		border: 1px solid #0687ff;
		color: #ffffff !important;

		.el-dialog__header {
			border-bottom: none;
			padding: 10px 15px !important;

			.el-dialog__title {
				color: #ffffff;
			}

			.el-dialog__headerbtn {
				color: #ffffff;
				top: 10px;
			}
		}

		.el-dialog__body {
			padding: 10px;
			color: #ffffff !important;

			.archives-left-body {
				top: vwpx(100px);
			}
		}
	}

	.dialog-content {
		min-height: 70vh;
		max-height: 80vh;
		overflow-y: auto;
		overflow-x: hidden;

		&::-webkit-scrollbar {
			width: vwpx(16px);
			background: rgba(2, 10, 30, 0.8);
		}

		&::-webkit-scrollbar-thumb {
			background: rgba(35, 134, 255, 0.3);
			border-radius: vwpx(8px);
		}

		&::-webkit-scrollbar-track {
			background: rgba(2, 10, 30, 0.8);
			border-radius: vwpx(8px);
		}
	}
}
</style>
