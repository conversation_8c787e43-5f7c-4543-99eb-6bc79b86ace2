<template>
  <div class="cache-container">
    <el-button type="primary" @click="onClear"
      >清除本地字典、路线、部门基础数据缓存</el-button
    >

    <el-button type="primary" v-if="this.query.type==2" @click="onClearOneMap">
      清除一张图图层配置相关缓存
    </el-button>
  </div>
</template>

<script>
import { publicRequest } from "@/api/oneMap/tableInfo";

export default {
  data() {
    return {
      query: null,
    };
  },
  created() {
    console.log("路由参数", this.$route);
    this.query = this.$route.query || {};
  },
  methods: {
    onClear() {
      this.$modal
        .confirm("确认清除缓存？")
        .then(() => {
          let type = this.query.type || null;
          let url =
            type && type == 1
              ? "/baseData/common/server/callAllService"
              : "/oneMap/common/server/callAllService";
            publicRequest(url, "post", {
            interfacePath: "/common/server/baseCacheClear",
          })
            .then((res) => {
              if (res.code === 200) {
                this.$modal.msgSuccess("清除成功");
              }
            })
            .catch(() => {
              this.$modal.msgError("清除失败");
            });
        })
        .catch(() => {});
    },
    onClearOneMap() {
      this.$modal
        .confirm("确认清除缓存？")
        .then(() => {
          publicRequest("/oneMap/common/server/callAllService", "post", {
            interfacePath: "/inner/common/cacheClear",
          })
            .then((res) => {
              if (res.code === 200) {
                this.$modal.msgSuccess("清除成功");
              }
            })
            .catch(() => {
              this.$modal.msgError("清除失败");
            });
        })
        .catch(() => {});
    },
  },
};
</script>

<style lang="scss" scoped>
.cache-container {
  width: 100%;
  height: 100vh;
  padding: 10px;
  // display: flex;
  // align-items: center;
  // justify-content: center;
}
</style>
