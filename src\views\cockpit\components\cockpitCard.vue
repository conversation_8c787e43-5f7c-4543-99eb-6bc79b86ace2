<template>
  <div class="cockpit-card" :style="{ width: w }">
    <div class="card-header" :style="headerStyle" ref="headRef" v-if="isHeader">
      <!-- <img src="@/assets/cockpit/card-icon.png" alt="icon" class="img-l" /> -->
      <span class="header-title" :class="isBig ? 'title-big' : 'title-small'">{{ title }}</span>
      <span class="header-unit" v-if="unit">（单位：{{ unit }}）</span>
      <div class="header-r" @click="onClick" v-if="isDtl" v-loading="loading">
        <span>详情</span>
        <img src="@/assets/cockpit/card-more.png" alt="icon" />
      </div>
      <template v-else>
        <div class="header-r">
          <slot name="more" />
        </div>
      </template>
    </div>
    <div class="card-body" :style="bodyStyle">
      <template v-if="!isBig">
        <span class="upper-left"></span>
        <span class="upper-right"></span>
        <span class="lower-left"></span>
        <span class="lower-right"></span>
      </template>
      <slot />
    </div>
  </div>
</template>

<script>
import headerBgS from '@/assets/cockpit/card-header-bg-s.png'
import headerBgM from '@/assets/cockpit/card-header-bg.png'
import headerBgL from '@/assets/cockpit/card-header-bg-l.png'
import { isBigScreen } from '../util/utils';

export default {
  name: "CockpitCard",
  props: {
    title: {
      type: String,
      default: '路网'
    },
    unit: {
      type: String,
      default: ''

    },
    isDtl: {
      type: Boolean,
      default: true
    },
    isHeader: {
      type: Boolean,
      default: true
    },
    w: {
      type: String,
      default: '45vh'
    },
    h: {
      type: String,
      default: '400px'
    },
    hBg: {
      type: String,
      default: '', // s，小，m，中，l大
    }
  },
  data() {
    return {
      isBig: isBigScreen(),
      loading: false,
    };
  },
  computed: {
    headerStyle() {
      let width = this.w
      if (width.includes('calc')) {
        let arrR = width.split('(')
        let arrL = arrR[1].split(')')
        let arrC = arrL[0].split(' - ')
        width = arrC[0].replace('vw', '')
      } else if (width.includes('vh')) {
        width = width.replace('vh', '')
      } else if (width.includes('px')) {
        width = width.replace('px', '')
      } else {
        width = width.replace('vw', '')
      }
      let w = width - 0
      let bg = headerBgL
      let numL = this.isBig ? 15 : 25;
      let numR = this.isBig ? 50 : 48;
      if (w >= numR) {
        bg = headerBgL
      } else if (w < numR && w > numL) {
        bg = headerBgM
      } else {
        bg = headerBgS
      }
      if (this.hBg === 's') {
        bg = headerBgS
      } else if (this.hBg === 'm') {
        bg = headerBgM
      } else if (this.hBg === 'l') {
        bg = headerBgL
      }
      return {
        backgroundImage: `url('${bg}')`,
      }
    },
    bodyStyle() {
      let obj = {};
      if (!this.isBig) {
        obj = { border: '1px solid rgba(0, 166, 255, 0.5)' }
      } else {
        obj = {
          // borderLeft: '1px solid',
          // borderRight: '1px solid',
          // borderImage: 'linear-gradient(360deg, rgba(0, 166, 255, 1) 0%, rgba(0, 166, 255, 0.2) 100%) 1 1 1 1',
          // borderBottom: '1px solid rgba(0, 166, 255, 1)',
        };
      }
      obj.height = this.h
      return obj
    }
  },
  methods: {
    onClick() {
      this.$emit('click')
      this.loading = true;
      setTimeout(() => {
        this.loading = false;
      }, 1000);
    }
  },
  components: {},
  mounted() { },
}
</script>

<style lang="scss" scoped>
@import "@/assets/styles/utils.scss";

.cockpit-card {
  min-width: vwpx(350px);

  .card-header {
    width: 100%;
    height: vwpx(76px);
    // background-image: url('~@/assets/cockpit/card-header-bg.png');
    background-repeat: no-repeat;
    background-size: auto 100%;
    background-clip: content-box;
    display: flex;
    align-items: center;
    object-fit: fill;

    .header-title {
      margin-left: vwpx(45px);
      font-family: Microsoft YaHei UI, Microsoft YaHei UI;
      color: #FFFFFF;
      margin-top: vwpx(5px);
    }

    .title-big {
      font-size: 0.45vw;
    }

    .title-small {
      font-size: vwpx(36px);
      text-shadow: 0px 0px 16px #0088FF;
      font-weight: 700;
    }

    .header-unit {
      font-weight: 700;
      color: #B6B6B6;
      font-size: vwpx(24px);
      margin-top: vwpx(7px);
    }

    .img-l {
      width: vwpx(50px);
      height: vwpx(50px);
    }

    .header-r {
      margin-left: auto;
      display: flex;
      align-items: center;
      cursor: pointer;
      margin-top: vwpx(5px);

      span {
        color: #42ABFF;
        margin-right: 6px;
        font-size: vwpx(26px);
      }

      img {
        width: vwpx(30px);
        height: vwpx(30px);
      }
    }
  }

  .card-body {
    width: 100%;
    // height: vwpx(395px);
    background-color: rgba(4, 37, 72, 0.8);
    // box-shadow: inset 0px 0px 15px 0px rgba(0, 101, 255, 0.8);
    // border: 1px solid rgba(0, 166, 255, 0.5);
    margin-top: vwpx(8px);

    position: relative;

    &::-webkit-scrollbar {
      width: vwpx(16px);
      background: rgba(2, 10, 30, 0.8);
    }

    &::-webkit-scrollbar-thumb {
      background: rgba(35, 134, 255, 0.3);
      border-radius: vwpx(8px);
    }

    &::-webkit-scrollbar-track {
      background: rgba(2, 10, 30, 0.8);
      border-radius: vwpx(8px);
    }

    .upper-left {
      position: absolute;
      top: -1px;
      left: -1px;
      width: vwpx(30px);
      height: vwpx(30px);
      background-image: url('~@/assets/cockpit/card-box.png');
      background-repeat: no-repeat;
      background-size: 100% 100%;
      transform: rotate(180deg);
    }

    .upper-right {
      position: absolute;
      top: -1px;
      right: -1px;
      width: vwpx(30px);
      height: vwpx(30px);
      background-image: url('~@/assets/cockpit/card-box.png');
      background-repeat: no-repeat;
      background-size: 100% 100%;
      transform: rotate(270deg);
    }

    .lower-left {
      position: absolute;
      bottom: -1px;
      left: -1px;
      width: vwpx(30px);
      height: vwpx(30px);
      background-image: url('~@/assets/cockpit/card-box.png');
      background-repeat: no-repeat;
      background-size: 100% 100%;
      transform: rotate(90deg);
    }

    .lower-right {
      position: absolute;
      bottom: -1px;
      right: -1px;
      width: vwpx(30px);
      height: vwpx(30px);
      background-image: url('~@/assets/cockpit/card-box.png');
      background-repeat: no-repeat;
      background-size: 100% 100%;
      transform: rotate(0deg);
    }
  }
}
</style>