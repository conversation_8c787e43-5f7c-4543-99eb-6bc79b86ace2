<template>
  <div class='page-container'>
    <!-- 左侧管理处及路段树形结构 -->
    <div class='left-panel' :class="{ 'collapsed': !relaNav }">
      <div class='panel-content'>
        <h3>管理处及路段</h3>
        <el-tree
          ref='tree'
          :data='filteredTreeData'
          :default-expanded-keys='[1]'
          :expand-on-click-node='false'
          :props='defaultProps'
          highlight-current
          node-key='code'
          @node-click='handleNodeClick'
        ></el-tree>
      </div>
      <!--      <div class="collapse-btn" @click='relaNav = !relaNav'>-->
      <!--        <el-icon><ArrowLeft v-if="relaNav" /><ArrowRight v-else /></el-icon>-->
      <!--      </div>-->
    </div>

    <!-- 中间分析类型列表 -->
    <div class='middle-panel'>
      <div class='panel-content'>
        <h3>分析类型</h3>
        <!-- 分析类型列表 -->
        <div
          v-for='item in mainConfig'
          :key='item.type'
          class='analysis-type-item'
          :class='{ active: mainIndex === item.type }'
          @click='handleCardClick(item)'
        >
          <div class='analysis-type-item-header'>
            <span class='analysis-type-item-title'>{{ item.label }}</span>
          </div>
          <div class='analysis-type-item-description'>{{ item.chart.length }}种分析图表</div>
        </div>
      </div>
    </div>

    <!-- 右侧主内容区域 -->
    <div class='right-panel'>
      <div class='analysis-card'>
        <div class='analysis-card-header'>
          <div class='analysis-card-title'>{{ getCurrentTabLabel }}</div>
          <div class='analysis-card-description'>请选择配置或添加新配置</div>
        </div>
        <div class='analysis-card-content'>
          <el-form ref='queryForm' :model='queryParams' label-width='80px' size="mini" :inline="true">
            <el-form-item label='时间范围'>
              <el-date-picker
                v-model='queryParams.timeRange'
                type='daterange'
                placeholder='请选择时间范围'
                value-format='yyyy-MM-dd HH:mm:ss'
                style='width: 100%'
              ></el-date-picker>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="el-icon-search" size="mini" @click="handleDrawChart" :loading='loading'>绘制</el-button>
            </el-form-item>
          </el-form>
          <div class="drawArea">
            <template v-for="(item, index) in dataList">
              <div :id="item.chartName" class="drawItem" v-show='mainIndex == item.type'></div>
            </template>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { getJCDomainTree } from '@/api/jgjc/baseInfo/alarmLog'
import {
  drawChart,
  getConfigUnderStructureCode,
} from '@/api/jgjc/dataAnalysis/presets'
import { mainConfig } from '@/views/jgjc/dataAnalysis/comm/chartConfig'
import moment from "moment/moment";
import { WSD } from '@/views/jgjc/dataAnalysis/dataView/drawMethods/WSD'
import { WIND } from '@/views/jgjc/dataAnalysis/dataView/drawMethods/WIND'
import { YB } from '@/views/jgjc/dataAnalysis/dataView/drawMethods/YB'
import { CLHZ } from '@/views/jgjc/dataAnalysis/dataView/drawMethods/CLHZ'
import { ZLWY } from '@/views/jgjc/dataAnalysis/dataView/drawMethods/ZLWY'
import { WYTJ } from '@/views/jgjc/dataAnalysis/dataView/drawMethods/WYTJ'
import { PW } from '@/views/jgjc/dataAnalysis/dataView/drawMethods/PW'
import { SL } from '@/views/jgjc/dataAnalysis/dataView/drawMethods/SL'
import { ZD } from '@/views/jgjc/dataAnalysis/dataView/drawMethods/ZD'
import { SDJC } from '@/views/jgjc/dataAnalysis/dataView/drawMethods/SDJC'

export default {
  name: 'DataView',
  provide() {
    return {
      getStructureCode: () => this.structureCode,
    }
  },
  data() {
    return {
      // 遮罩层
      loading: false,
      // 左侧组织树
      relaNav: true,
      relaOptions: [],
      filteredTreeData: [],
      defaultProps: {
        children: 'children',
        label: 'label',
      },
      mainConfig: [],
      mainIndex: '',
      structureCode: '',
      queryParams: {
        timeRange: []
      },
      dataList: [],
    }
  },
  computed: {
    getCurrentTab: function() {
      return this.mainConfig.find(item => item.type === this.mainIndex) || {}
    },
    getCurrentTabLabel: function() {
      return this.getCurrentTab.label || ''
    },
  },
  created() {
    this.getDeptTree()
    this.queryParams.timeRange = [
      moment().startOf('month').format('YYYY-MM-DD HH:mm:ss'),
      moment().endOf('month').format('YYYY-MM-DD HH:mm:ss')
    ]
  },
  methods: {
    // 查询部门下拉树结构
    getDeptTree() {
      getJCDomainTree({}).then(response => {
        this.relaOptions = response.data
        this.filteredTreeData = [...this.relaOptions]
      })
    },
    // 树节点点击事件
    handleNodeClick(nodeData, node) {
      if (node.level == 4) {
        this.structureId = nodeData.structureId
        this.structureCode = nodeData.structureCode
        this.handleQuery()
      }
    },
    handleQuery() {
      this.loading = true
      getConfigUnderStructureCode({ structureCode: this.structureCode }).then(res => {
        this.loading = false
        this.mainConfig = []
        const grouped = res.result.reduce((acc, item) => {
          if (!acc[item.analysisType]) {
            acc[item.analysisType] = []
          }
          acc[item.analysisType].push(item)
          return acc
        }, {})
        const result = Object.entries(grouped).map(([type, items]) => {
          const config = mainConfig.find(c => c.type === type)
          return {
            label: config ? config.label : type,
            type: type,
            chart: items.map(item => {
              const chartConfig = config?.chart?.find(c => c.type === item.drawType)
              return {
                label: chartConfig ? chartConfig.label : item.optionName,
                type: item.drawType,
                structureCode: item.structureCode
              }
            }),
          }
        })
        this.mainConfig = result
        if (this.mainConfig.length > 0) {
          this.mainIndex = this.mainConfig[0].type
        }
        this.initCharts(this.mainConfig[0].chart, this.mainConfig[0].type)
      })
    },
    handleCardClick(config) {
      this.mainIndex = config.type
      // 如果已经渲染过了 不再次渲染 除非主动点击渲染按钮
      if (this.dataList.filter(item => item.type === config.type).length > 0) return
      this.initCharts(config.chart, config.type)
    },
    handleDrawChart() {
      if (!this.structureCode) {
        this.$message.warning('请选择结构物！');
        return
      }
      const config = this.mainConfig.find(item => item.type === this.mainIndex)
      this.initCharts(config.chart, config.type)
    },
    initCharts(charts, type) {
      const structureCode = this.structureCode
      const params = {
        structureCode: structureCode,
        analysisType: type,
        startTime: this.queryParams.timeRange[0],
        endTime: this.queryParams.timeRange[1],
      }
      this.loading = true
      drawChart(params).then(res => {
        for(let i = 0 ; i < res.result.length; i++){
          let drawTypeObject = res.result[i]
          for(let j = 0 ; j < drawTypeObject.sortedResponseList.length; j++){
            let chartObject = drawTypeObject.sortedResponseList[j]
            if(chartObject.status === 0){
              this.$message({
                type: 'warning',
                message: chartObject?.errorMsg
              });
            }else {
              let chartName = drawTypeObject.drawType + j
              this.dataList.push({
                type,
                chartName: chartName
              })
              this.$nextTick(() => {
                let funcName = 'set' + drawTypeObject.drawType + 'Chart';
                switch (type) {
                  case 'WSD':
                    this.callModelFun(WSD, funcName, chartName, chartObject)
                    break
                  case 'WIND':
                    if (drawTypeObject.drawType === 'ROSE') {
                      WIND.setROSEChart(chartName, chartObject, "%")
                    } else {
                      this.callModelFun(WIND, funcName, chartName, chartObject)
                    }
                    break
                  case 'YB':
                    this.callModelFun(YB, funcName, chartName, chartObject)
                    break
                  case 'CLHZ':
                    this.callModelFun(CLHZ, funcName, chartName, chartObject)
                    break
                  case 'ZLWY':
                    this.callModelFun(ZLWY, funcName, chartName, chartObject)
                    break
                  case 'WYTJ':
                    if (drawTypeObject.drawType === 'ZZWYCOR') {
                      WYTJ.setZZWYCORChart(chartName, chartObject)
                    } else {
                      this.callModelFun(WYTJ, funcName, chartName, chartObject)
                    }
                    break
                  case 'PW':
                    this.callModelFun(PW, funcName, chartName, chartObject)
                    break
                  case 'SL':
                    this.callModelFun(SL, funcName, chartName, chartObject)
                    break
                  case 'ZD':
                    this.callModelFun(ZD, funcName, chartName, chartObject)
                    break
                  case 'SDJC':
                    this.callModelFun(SDJC, funcName, chartName, chartObject)
                    break
                  default:
                    this.$message.error('暂不支持的类型')
                    break
                }
              })
            }
          }
        }
      }).finally(() => {
        this.loading = false
      })
    },
    // 画图中转函数
    callModelFun(object, funcName, chartName, data){
      object[funcName](chartName, data);
    },
  },
}
</script>

<style scoped>
.page-container {
  display: flex;
  height: 100%;
  overflow: hidden;
  background-color: #f5f5f5;
}

/* 左侧树形结构样式 */
.left-panel {
  width: 280px;
  background-color: #ffffff;
  border-right: 1px solid #e6e6e6;
  position: relative;
  transition: all 0.3s;
  display: flex;
}

.left-panel.collapsed {
  width: 0;
  overflow: hidden;
}

/* 中间分析类型面板 */
.middle-panel {
  width: 300px;
  background-color: #ffffff;
  border-right: 1px solid #e6e6e6;
  position: relative;
  transition: all 0.3s;
}

.panel-content {
  padding: 20px;
  overflow-y: auto;
  height: 100%;
  width: 100%;
}

.collapse-btn {
  width: 20px;
  height: 40px;
  background-color: #ffffff;
  border: 1px solid #e6e6e6;
  border-left: none;
  border-radius: 0 4px 4px 0;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  position: absolute;
  right: -20px;
  top: 50%;
  transform: translateY(-50%);
  z-index: 1;
}

.collapse-btn:hover {
  background-color: #f0f7ff;
}

/* 分析类型列表项样式 */
.analysis-type-item {
  padding: 12px;
  margin-bottom: 10px;
  border-radius: 4px;
  border: 1px solid #e6e6e6;
  cursor: pointer;
  transition: all 0.3s;
}

.analysis-type-item:hover {
  background-color: #f0f7ff;
}

.analysis-type-item.active {
  background-color: #e6f4ff;
  border-color: #1890ff;
}

.analysis-type-item-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.analysis-type-item-title {
  font-size: 14px;
  font-weight: bold;
  color: #333333;
}

.analysis-type-item-description {
  font-size: 12px;
  color: #666666;
}

/* 右侧主内容区域 */
.right-panel {
  flex: 1;
  padding: 20px;
  overflow-y: auto;
  background-color: #ffffff;
}

/* 分析卡片样式 */
.analysis-card {
  margin-bottom: 20px;
  border-radius: 4px;
  background-color: #ffffff;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border: 1px solid #e6e6e6;
}

.analysis-card-header {
  padding: 15px;
  border-bottom: 1px solid #e6e6e6;
}

.analysis-card-title {
  font-size: 16px;
  font-weight: bold;
  color: #333333;
  margin-bottom: 8px;
}

.analysis-card-description {
  font-size: 14px;
  color: #666666;
}

.analysis-card-content {
  padding: 20px;
}

.tab-actions {
  margin-bottom: 15px;
}

.el-table {
  width: 100%;
}

.mb10 {
  margin-bottom: 10px;
}

.mt10 {
  margin-top: 10px;
}

/* 响应式调整 */
@media (max-width: 1200px) {
  .middle-panel {
    width: 250px;
  }
}

@media (max-width: 992px) {
  .left-panel {
    width: 220px;
  }

  .middle-panel {
    width: 200px;
  }
}

@media (max-width: 768px) {
  .page-container {
    flex-direction: column;
  }

  .left-panel,
  .middle-panel {
    width: 100%;
    height: auto;
    max-height: 300px;
  }

  .left-panel.collapsed,
  .middle-panel.collapsed {
    height: 0;
    max-height: 0;
    overflow: hidden;
  }

  .collapse-btn {
    top: auto;
    bottom: -20px;
    left: 50%;
    transform: translateX(-50%) rotate(90deg);
    right: auto;
  }
}
.drawArea {
  width: 100%;
  height: 100%;
}
.drawItem{
  width: calc(50%);
  height: 400px;
  overflow:hidden;
  display: inline-block;
  border: 1px solid #eee;
  border-radius: 5px;
}
.drawItem:hover{
  width: calc(50%);
  height: 400px;
  overflow:hidden;
  display: inline-block;
  border-radius: 5px;
  box-shadow: 2px 2px 5px #888888;
}
</style>
