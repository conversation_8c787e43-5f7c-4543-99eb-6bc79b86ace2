<template>
  <div class="road-interflow-edit">
    <el-form
      ref="queryForm"
      :inline="true"
      :model="queryParams"
      label-width="68px"
      size="mini"
    >
      <el-form-item label="">
        <el-date-picker
          style="width: 240px"
          v-model="queryParams.year"
          type="year"
          value-format="yyyy"
          placeholder="年份"
        >
        </el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-select
          v-model="queryParams.number"
          placeholder="期数"
          clearable
          style="width: 100%"
        >
          <el-option
            v-for="item in numbers"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          >
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <selectTree
          :key="'domainId'"
          style="width: 240px"
          v-model="queryParams.domainId"
          :deptType="100"
          :deptTypeList="[1, 3, 4]"
          placeholder="管养单位"
          clearable
          filterable
        />
      </el-form-item>
      <el-form-item>
        <selectTree
          :key="'constructionUnit'"
          style="width: 240px"
          v-model="queryParams.calcDomainId" :data-rule="false"
          :dept-type="100"
          placeholder="施工单位"
          :filter-keys="['云南省交通投资建设集团有限公司', '云南交投投资有限公司']"
          :expand-all="false"
          clearable
          filterable
        />
      </el-form-item>
      <el-form-item>
        <RoadSection
          v-model="queryParams.maiSecId"
          :deptId="queryParams.domainId"
          placeholder="路段"
        />
      </el-form-item>
      <el-form-item>
        <el-input
          style="width: 240px"
          placeholder="结算计量单编码"
          v-model="queryParams.code"
        ></el-input>
      </el-form-item>
      <el-form-item>
        <el-button
          icon="el-icon-search"
          size="mini"
          type="primary"
          @click="handleQuery"
        >搜索
        </el-button
        >
        <el-button
          icon="el-icon-refresh"
          size="mini"
          @click="resetQuery"
        >重置
        </el-button
        >
      </el-form-item>
    </el-form>
    <el-table v-adjust-table v-loading="loading" :data="tableData" style="width: 100%" @row-click="checkSettle" height="450px">
      <el-table-column
        align="center"
        label="序号"
        type="index"
        width="50"
      />
      <template v-for="(column, index) in columns">
        <el-table-column
          v-if="column.visible"
          :fixed="column.fixed"
          :label="column.label"
          :prop="column.field"
          :width="column.width"
          show-overflow-tooltip
          align="center"
        >
          <template slot-scope="scope">
            <dict-tag
              v-if="column.dict"
              :options="dict.type[column.dict]"
              :value="scope.row[column.field]"
            />
            <template v-else-if="column.slots">
              <RenderDom
                :index="index"
                :render="column.render"
                :row="scope.row"
              />
            </template>
            <span v-else-if="column.isTime">{{
                parseTime(scope.row[column.field], "{y}-{m}-{d}")
              }}</span>
            <span v-else>{{ scope.row[column.field] }}</span>
          </template>
        </el-table-column>
      </template>
    </el-table>
  </div>
</template>

<script>
import { viewListSettlecalc} from "@/api/calculate/project/settlementApplication";
import selectTree from "@/components/DeptTmpl/selectTree.vue";
import RoadSection from "@/views/baseData/components/roadSection/index.vue";

export default {
  components: {RoadSection, selectTree},
  props: {
    flag: {
      type: Number,
      default: 1
    },
    settleId: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      loading: false,
      total: 0,
      queryParams: {
        pageNum: 1,
        pageSize: 50,
      },
      tableData: [],
      columns: [
        {
          key: 1,
          width: 100,
          field: "numberName",
          label: `期数`,
          visible: true,
        },
        {
          key: 2,
          width: 200,
          field: "name",
          label: `结算计量名称`,
          visible: true,
        },
        {
          key: 3,
          width: 200,
          field: "code",
          label: `结算计量编号`,
          visible: true,
        },
        {
          key: 4,
          width: 100,
          field: "domainName",
          label: `管养单位`,
          visible: true,
        },
        {
          key: 5,
          width: 200,
          field: "calcDomainName",
          label: `申请计量单位`,
          visible: true,
        },
        {
          key: 6,
          width: 100,
          field: "maiSecId",
          label: `路段名称`,
          visible: true,
        },
        {key: 7, width: 200, field: 'conName', label: `合同名称`, visible: true},
        {
          key: 8,
          width: 100,
          field: "sumFund",
          label: `基本费用`,
          visible: true,
        },
        {
          key: 12,
          width: 100,
          field: "supFund",
          label: `监理费`,
          visible: true,
        },
        {
          key: 31,
          width: 100,
          field: "designFund",
          label: `设计费`,
          visible: true,
        },
        {
          key: 17,
          width: 100,
          field: "calcDate",
          label: `计量日期`,
          visible: true,
        },
      ]
    }
  },
  mounted() {
    this.handleQuery()
  },
  methods: {
    handleQuery() {
      viewListSettlecalc(this.queryParams).then(response => {
        if (response.code == 200) {
          this.loading = false
          this.tableData = response.rows
          this.total = response.total
        }
      })
    },
    checkSettle(e) {
      const param = {
        settleId: e.id,
        settleName: e.name
      }
      this.$emit('checkSettle', param)
    },
    resetQuery() {
      this.queryParams = {
        pageNum: 1,
        pageSize: 50,
      }
      this.handleQuery()
    }
  }
}
</script>

<style lang="scss" scoped>

</style>
<style lang="scss" scoped>
@import "@/assets/styles/business.scss";
</style>
