<template>
  <el-dialog :visible.sync="sensorTreeDialogVisible" width="500px" center append-to-body>
    <span slot="title" class="title">
      选择传感器
    </span>
    <el-form
        :model="form"
        :inline="true"
        label-position="left"
        label-width="140px"
        ref="ruleForm"
        label-suffix="："
    >
      <el-form-item label="当前选择传感器" prop="currentSelectSensorLabel">
        <span style="float: left">{{form.currentSelectSensorLabel}}</span>
      </el-form-item>
    </el-form>
    <div class="reportTree">
      <el-tree
          ref="reportTree"
          :data="sensorTreeNode"
          node-key="code"
          :empty-text="emptyText"
          :highlight-current="true"
          @node-click="handleNodeClick"
          style='height: 400px; overflow-y: scroll'
      >
      </el-tree>
    </div>
    <span slot="footer" class="dialog-footer">
      <el-button size="mini" @click="sensorTreeDialogVisible = false">取 消</el-button>
      <el-button size="mini" type="primary" @click="confirmSelect">确 定</el-button>
    </span>
  </el-dialog>
</template>

<script>

import { getSensorTreeNodeByCode } from '@/api/jgjc/dataAnalysis/customize'
import { cloneDeep } from 'lodash'

export default {
  name: "DataCompositionTree",
  inject: ["getStructureCode"],
  computed: {
    structureCode () {
      return this.getStructureCode();
    }
  },
  data() {
    return {
      // dialog是否显示
      sensorTreeDialogVisible: false,
      // 无数据时显示文字
      emptyText: "暂无数据",
      form: {
        currentSelectSensor: {},
        currentSelectSensorLabel: ''
      },
      formRules: {
        currentSelectSensorLabel: [
          {required: true, message: '选择传感器不可为空', trigger: 'blur'}
        ]
      },
      sensorTreeNode: []
    }
  },
  methods: {
    open() {
      this.sensorTreeDialogVisible = true;
      this.getTreeData()
    },
    getTreeData() {

      getSensorTreeNodeByCode({ code: this.structureCode }).then(res => {
        this.sensorTreeNode = res.data
      })
    },
    // 点击节点
    handleNodeClick(data, node) {
      if(node.level === 3){
        this.form.currentSelectSensor = cloneDeep(data)
        this.form.currentSelectSensorLabel = data.dataCode
      }
    },

    // 确认选择
    confirmSelect(){
      if(this.form.currentSelectSensorLabel === ''){
        this.$message({type: 'warning', message: "选择传感器不可为空", duration:1000});
        return
      }
      this.$emit('selectNewSensorCallBack', this.form.currentSelectSensor)
      this.sensorTreeDialogVisible = false
    }
  }
}
</script>

<style scoped>
.reportTree{
  margin: 5px 0 0 15px;
  border-width: 1px;
  border-left:1px solid #D0D0D0;
}
.title {
  font-family: "Courier New", Courier, monospace;
  font-size: 16px;
  font-weight: bolder;
}
</style>
