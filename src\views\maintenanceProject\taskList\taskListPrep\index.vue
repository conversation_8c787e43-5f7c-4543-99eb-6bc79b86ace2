<template>
  <task-list :pros-status="1" :proj-code="projCode" :task-type="taskType"></task-list>
</template>

<script>
import TaskList from "@/views/maintenanceProject/taskList/component/taskList.vue";

export default {
  name: 'TaskListPrep',
  components: {TaskList},
  data() {
    return {
      projCode: '',
      taskType: ''
    }
  },
  created() {
    this.projCode = this.$route.query.projCode
    this.taskType = this.$route.query.taskType
  }
}
</script>

<style lang="scss" scoped>

</style>
<style lang="scss" scoped>
@import "@/assets/styles/business.scss";
</style>
