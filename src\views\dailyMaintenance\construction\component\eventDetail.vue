<template>
  <div class="app-container maindiv">
    <el-row :gutter="20">
      <!--筛选区开始-->
      <el-col :span="24" :xs="24">
        <el-row>
          <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" label-width="68px">
            <el-form-item label="" prop="field1">
              <el-date-picker
                  v-model="queryParams.field1"
                  style="width: 240px"
                  type="year"
                  placeholder="事件类型">
              </el-date-picker>
            </el-form-item>
            <el-form-item label="" prop="field4">
              <el-input
                  v-model="queryParams.field4"
                  placeholder="路线编码"
                  clearable
                  style="width: 240px"
              >
              </el-input>
            </el-form-item>
            <el-form-item label="" prop="field4">
              <el-input
                  v-model="queryParams.field4"
                  placeholder="起止桩号"
                  clearable
                  style="width: 110px"
              >
              </el-input>
              <div style="width: 20px;display: inline-block;text-align: center">~</div>
              <el-input
                  v-model="queryParams.field4"
                  placeholder="起止桩号"
                  clearable
                  style="width: 110px"
              >
              </el-input>
            </el-form-item>
            <el-form-item label="" prop="field4">
              <el-input
                  v-model="queryParams.field4"
                  placeholder="子目号"
                  clearable
                  style="width: 240px"
              >
              </el-input>
            </el-form-item>
            <el-form-item label="" prop="field4">
              <el-input
                  v-model="queryParams.field4"
                  placeholder="方法名"
                  clearable
                  style="width: 240px"
              >
              </el-input>
            </el-form-item>
            <el-form-item label="" prop="field4">
              <el-input
                  v-model="queryParams.field4"
                  placeholder="事件描述"
                  clearable
                  style="width: 240px"
              >
              </el-input>
            </el-form-item>
            <el-form-item label="" prop="field4">
              <el-input
                  v-model="queryParams.field4"
                  placeholder="事件状态"
                  clearable
                  style="width: 240px"
              >
              </el-input>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
              <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
            </el-form-item>
          </el-form>
        </el-row>
        <!--筛选区结束-->
        <!--操作按钮区开始-->
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button
                type="success"
                icon="el-icon-view"
                size="mini"
                @click="handleInfo"
            >事件信息
            </el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button
                type="primary"
                icon="el-icon-download"
                size="mini"
                @click="handleExport"
            >导出明细
            </el-button>
          </el-col>
        </el-row>

        <!--数据表格开始-->
        <div class="tableDiv">
          <el-table v-adjust-table stripe size="mini" height="200px"
                    style="width: 100%" v-loading="loading" border :data="dataList">
            <el-table-column type="selection" width="50" align="center"/>
            <el-table-column fixed label="序号" type="index" width="50"></el-table-column>
            <template v-for="column in columns">
              <el-table-column :label="column.label"
                               v-if="column.visible"
                               align="center"
                               :prop="column.field"
                               :width="column.width">
                <template slot-scope="scope">
                  <dict-tag v-if="column.dict" :options="dict.type[column.dict]" :value="scope.row.type"/>
                  <span v-else-if="column.isTime">{{ parseTime(scope.row[column.field], '{y}-{m}-{d}') }}</span>
                    <span v-else>{{ scope.row[column.field] }}</span>
                </template>
              </el-table-column>
            </template>
          </el-table>
          <pagination
              v-show="total>0"
              :total="total"
              :page.sync="queryParams.pageNum"
              :limit.sync="queryParams.pageSize"
              @pagination="getList"
          />
        </div>
        <!--数据表格结束-->
      </el-col>
    </el-row>
    <el-dialog title="事件信息" destroy-on-close append-to-body modal-append-to-body :visible.sync="dialogVisible" width="80%">
      <event-tree-info></event-tree-info>
    </el-dialog>
  </div>
</template>

<script>

import selectTree from "@/components/DeptTmpl/selectTree.vue";
import EventTreeInfo from "../../component/eventTreeInfo.vue";
export default {
  name: "eventDetail",
  components: {EventTreeInfo, selectTree},
  data() {
    return {
      showSearch: false,
      loading: false,
      queryParams: {
        field1: undefined,
        field2: undefined,
        field3: undefined,
        field4: undefined,
        field5: undefined,
        field6: undefined,
        field7: undefined,
        field8: undefined,
        field9: undefined,
        pageNum: 1,
        pageSize: 50,
      },
      // 列信息
      columns: [
        {key: 0, width: 100, field: 'field1', label: `资产类型`, visible: true},
        {key: 1, width: 100, field: 'field2', label: `路段`, visible: true},
        {key: 2, width: 100, field: 'field3', label: `路线编码`, visible: true},
        {key: 3, width: 100, field: 'field4', label: `上下行`, visible: true},
        {key: 4, width: 100, field: 'field5', label: `位置`, visible: true},
        {key: 5, width: 100, field: 'field6', label: `起点桩号`, visible: true},
        {key: 6, width: 100, field: 'field7', label: `终点桩号`, visible: true},
        {key: 7, width: 100, field: 'field8', label: `事件类型`, visible: true},
        {key: 8, width: 100, field: 'field9', label: `处置类型`, visible: true},
        {key: 9, width: 100, field: 'field10', label: `描述`, visible: true},
        {key: 10, width: 100, field: 'field11', label: `备注`, visible: true},
        {key: 11, width: 100, field: 'field12', label: `采集时间`, visible: true},
        {key: 12, width: 100, field: 'field13', label: `操作人`, visible: true},
      ],
      // 表格数据
      dataList: [
        {field1: '1', field2: '101'},
        {field1: '1', field2: '101'},
        {field1: '2', field2: '101'},
        {field1: '3', field2: '101'},
        {field1: '4', field2: '101'}
      ],
      // 总条数
      total: 0,
      // 选中数组
      ids: [],
      dialogVisible: false
    };
  },
  watch: {},
  created() {

  },
  methods: {
    handleInfo() {
      this.dialogVisible = true
    }
  }
};
</script>
<style>
</style>
<style lang="scss" scoped>
@import "@/assets/styles/business.scss";
</style>
