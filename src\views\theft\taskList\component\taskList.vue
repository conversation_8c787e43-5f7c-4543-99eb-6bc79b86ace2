<template>
  <div class="app-container">
    <el-row :gutter="20">
      <!--筛选区开始-->
      <el-col :span="24" :xs="24">
        <el-row>
          <el-col :span="24">
            <el-form ref="queryForm" :inline="true" :model="queryParams" label-width="68px" size="small">
              <el-form-item label="" prop="disType">
                <el-date-picker
                    v-model="queryParams.year"
                    placeholder="年份"
                    style="width: 240px"
                    type="year"
                    value-format="yyyy"
                >
                </el-date-picker>
              </el-form-item>
              <el-form-item>
                <selectTree
                    :key="'domainId'"
                    v-model="queryParams.domainId"
                    :deptType="100"
                    :deptTypeList="[1, 3, 4]" clearable
                    filterable
                    placeholder="管养单位"
                    style="width: 240px"
                />
              </el-form-item>
              <el-form-item>
                <selectTree
                    :key="'constructionUnit'"
                    v-model="queryParams.conDomainId" :data-rule="false"
                    :dept-type="100"
                    placeholder="施工单位"
                    :filter-keys="['云南省交通投资建设集团有限公司', '云南交投投资有限公司']"
                    :expand-all="false"
                    clearable
                    filterable
                    style="width: 240px"
                />
              </el-form-item>
              <el-form-item>
                <RoadSection v-model="queryParams.maiSecId" :deptId="queryParams.domainId" placeholder="路段"
                             style="width: 240px"/>
              </el-form-item>
              <el-form-item>
                <el-input v-model="queryParams.projName" placeholder="项目名称" style="width: 240px"></el-input>
              </el-form-item>
              <el-form-item>
                <el-input v-model="queryParams.projCode" placeholder="项目编码" style="width: 240px"></el-input>
              </el-form-item>
              <el-form-item>
                <dict-select type="task_type" v-model="queryParams.type" placeholder="任务类型"
                             style="width: 240px"></dict-select>
              </el-form-item>
              <el-form-item>
                <el-input v-model="queryParams.name" placeholder="任务单名称" style="width: 240px"></el-input>
              </el-form-item>
              <el-form-item>
                <el-input v-model="queryParams.code" placeholder="任务单编码" style="width: 240px"></el-input>
              </el-form-item>
              <el-form-item>
                <dict-select clearable type="maintenance_project_type" v-model="queryParams.status" placeholder="状态"
                             v-if="prosStatus == 1" style="width: 240px"></dict-select>
              </el-form-item>
              <el-form-item>
              <el-date-picker
                v-model="queryParams.issueDate"
                type="daterange"
                range-separator="至"
                start-placeholder="发出起始时间"
                end-placeholder="发出截止时间"
                style="width: 240px">
              </el-date-picker>
            </el-form-item>
              <el-form-item>
                <el-button icon="el-icon-search" size="mini" type="primary" @click="handleQuery">搜索</el-button>
                <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
              </el-form-item>
            </el-form>
          </el-col>
        </el-row>
        <!--筛选区结束-->

        <!--操作按钮区开始-->
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button
                icon="el-icon-download"
                size="mini"
                type="success"
                v-has-menu-permi="['theft:construction:export', 'theft:construction:pendingexport', 'theft:construction:suppendingexport', 'theft:construction:designpendingexport']"
                @click="exportList"
            >导出
            </el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button
                icon="el-icon-view"
                size="mini"
                type="warning"
                @click="handleOpenOperate"
            >审核意见
            </el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button
                icon="el-icon-folder-opened"
                size="mini"
                type="primary"
                v-has-menu-permi="['theft:construction:filelist']"
                @click="openFileModel"
            >附件管理
            </el-button>
          </el-col>
          <el-col :span="1.5" v-if="prosStatus == 1">
            <el-button
                icon="el-icon-folder-opened"
                size="mini"
                type="success"
                v-has-menu-permi="['theft:construction:add']"
                @click="handleCopy"
            >任务单复制
            </el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button
              icon="el-icon-view"
              size="mini"
              type="warning"
              v-has-menu-permi="['theft:construction:preview', 'theft:construction:suppreview', 'theft:construction:designpreview']"
              @click="handlePreview"
            >任务单预览
            </el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button
              type="warning"
              icon="el-icon-view"
              size="mini"
              v-has-permi="['settlement:repository:regenerate']"
              @click="handleRegenerate"
            >重新生成报表
            </el-button>
          </el-col>
          <right-toolbar :columns="columns" :showSearch.sync="showSearch" @queryTable="handleQuery"></right-toolbar>
        </el-row>
        <!--操作按钮区结束-->
        <!--数据表格开始-->
        <div class="tableDiv">
          <el-table v-adjust-table
              ref="dataTable"
              v-loading="loading"
              :data="tableData"
              :height="
                showSearch ? 'calc(100vh - 430px)' : 'calc(100vh - 370px)'
              "
              border
              highlight-current-row
              row-key="id"
              size="mini"
              stripe
              @row-click="handleClickRow"
              @selection-change="handleSelectionChange"
              @expand-change="loadData"
              style="width: 100%"
          >
            <el-table-column type="selection" width="50" align="center"/>
            <el-table-column type="expand">
              <template slot-scope="props">
                <el-table v-adjust-table :data="props.row.constructionDetailList" style="width: 100%" v-loading="methodLoading">
                  <el-table-column
                      prop=""
                      align="center"
                      label="">
                  </el-table-column>
                  <el-table-column
                      prop="schemeCode"
                      align="center"
                      label="子目号">
                  </el-table-column>
                  <el-table-column
                      prop="schemeName"
                      align="center"
                      label="养护方法">
                  </el-table-column>
                  <el-table-column
                      prop="calcDesc"
                      align="center"
                      label="计算式">
                  </el-table-column>
                  <el-table-column
                      prop="num"
                      align="center"
                      label="方法数量">
                  </el-table-column>
                  <el-table-column
                      prop="unit"
                      align="center"
                      label="方法单位">
                  </el-table-column>
                  <el-table-column
                      prop="price"
                      align="center"
                      label="单价">
                  </el-table-column>
                  <el-table-column
                      prop="amount"
                      align="center"
                      label="金额">
                  </el-table-column>
                  <el-table-column
                      prop="remark"
                      align="center"
                      label="备注">
                  </el-table-column>
                </el-table>
              </template>
            </el-table-column>
            <el-table-column
                align="center"
                label="序号"
                type="index"
                width="50"
            />
            <template v-for="(column,index) in columns">
              <el-table-column v-if="column.visible" show-overflow-tooltip
                               :label="column.label"
                               :prop="column.field"
                               :width="column.width"
                               align="center">
                <template slot-scope="scope">
                  <dict-tag v-if="column.dict" :options="dict.type[column.dict]" :value="scope.row[column.field]"/>
                  <template v-else-if="column.slots">
                    <RenderDom :index="index" :render="column.render" :row="scope.row"/>
                  </template>
                  <span v-else-if="column.isTime">{{ parseTime(scope.row[column.field], '{y}-{m}-{d}') }}</span>
                  <span v-else>{{ scope.row[column.field] }}</span>
                </template>
              </el-table-column>
            </template>
            <el-table-column
                align="center"
                class-name="small-padding fixed-width"
                fixed="right"
                label="操作"
                width="250"
            >
              <template slot-scope="scope">
               <template v-if="prosStatus == 1">
                 <el-button
                     icon="el-icon-delete"
                     size="mini"
                     type="text"
                     v-has-menu-permi="['theft:construction:remove']"
                     :disabled="scope.row.status != 0"
                     @click="handleDelete(scope.row)"
                 >删除
                 </el-button>
                 <el-button
                     icon="el-icon-edit"
                     size="mini"
                     type="text"
                     v-has-menu-permi="['theft:construction:edit']"
                     :disabled="scope.row.status != 0"
                     @click="handleEdit (scope.row)"
                 >修改
                 </el-button>
                 <el-button
                     icon="el-icon-check"
                     size="mini"
                     type="text"
                     v-has-menu-permi="['theft:construction:process']"
                     :disabled="scope.row.status != 0"
                     @click="handleSubmit (scope.row)"
                 >提交
                 </el-button>
               </template>
                <template v-else-if="prosStatus == 2">
                  <el-button
                      icon="el-icon-check"
                      size="mini"
                      type="text"
                      v-has-menu-permi="['theft:construction:process']"
                      @click="handleReview (scope.row)"
                  >审核
                  </el-button>
                  <el-button
                    icon="el-icon-edit"
                    size="mini"
                    type="text"
                    v-has-menu-permi="['theft:construction:edit']"
                    @click="handleEdit (scope.row)"
                  >修改
                  </el-button>
                </template>
                <template v-else-if="prosStatus == 3">
                  <el-button
                      icon="el-icon-delete"
                      size="mini"
                      type="text"
                      v-has-menu-permi="['theft:construction:process']"
                      @click="handleReject(scope.row)"
                  >撤回
                  </el-button>
                  <el-button
                      icon="el-icon-check"
                      size="mini"
                      type="text"
                      v-has-menu-permi="['theft:construction:process']"
                      @click="handleIssued (scope.row)"
                  >签发
                  </el-button>
                  <el-button
                    icon="el-icon-edit"
                    size="mini"
                    type="text"
                    v-has-menu-permi="['theft:construction:edit']"
                    @click="handleEdit (scope.row)"
                  >修改
                  </el-button>
                </template>
                <template v-else>
                  <el-button
                      icon="el-icon-check"
                      size="mini"
                      type="text"
                      v-has-menu-permi="['theft:construction:process', 'theft:construction:supprocess', 'theft:construction:designprocess']"
                      @click="handleReceive (scope.row)"
                  >接收
                  </el-button>
                </template>
              </template>
            </el-table-column>
          </el-table>
          <pagination
              v-show="total>0"
              :limit.sync="queryParams.pageSize"
              :page.sync="queryParams.pageNum"
              :total="total"
              @pagination="handleQuery"
          />
        </div>
        <!--数据表格结束-->
      </el-col>
    </el-row>
    <el-drawer :wrapperClosable="false" :title="detailTitle" :visible.sync="openDetail" size="70%" destroy-on-close>
      <task-detail :is-copy="isCopy" :status="status" :type="type" :row="row" @close="handleCloseDetail"></task-detail>
    </el-drawer>

    <el-dialog title="操作意见" :visible.sync="openOperateInfo" v-if="openOperateInfo" width="80%" destroy-on-close>
      <operateInfo type="theft" :business-key="clickRow.id" :get-node-info="getNodeInfo"></operateInfo>
    </el-dialog>

    <el-dialog title="附件列表" destroy-on-close :visible.sync="openFile" width="80%">
      <file-upload v-if="clickRow.fileId" v-model="clickRow.fileId" :owner-id="clickRow.fileId"></file-upload>
    </el-dialog>

    <IFramePreview ref="iframeRef" :srcdoc="preview.html" :down-url="preview.url" :file-name="preview.fileName"></IFramePreview>
  </div>
</template>

<script>
import Tables from "@/views/patrol/frequencySettings/tables.vue";
import RoadSection from "@/views/baseData/components/roadSection/index.vue";
import selectTree from "@/components/DeptTmpl/selectTree.vue";
import TaskDetail from "@/views/theft/taskList/component/taskDetail.vue";
import operateInfo from "@/views/dailyMaintenance/component/operateInfo.vue";
import {
  addFileConstruction,
  deleteConstruction,
  fileListConstruction,
  listConstruction,
  getNodeInfo,
  pendingListConstruction,
  viewConstructionDetail,
  supPendingList,
  designPendingList,
  previewConstruction, previewCheck, previewDesign, previewSup
} from "@/api/theft/taskList";
import { v4 as uuidv4 } from 'uuid';
import IFramePreview from "@/components/IFramePreview/index.vue";
import {regenerateReport} from "@/api/dailyMaintenance/metering/addPrice";
export default {
  components: {IFramePreview, operateInfo, TaskDetail, selectTree, RoadSection, Tables},
  dicts: ['maintenance_project_type', 'task_type', 'project_type', 'affiliation_project_type'],
  data() {
    return {
      showSearch: false,
      openFile: false,
      queryParams: {
        pageNum: 1,
        pageSize: 50
      },
      total: 0,
      loading: false,
      status: 1,
      type: 1,
      columns: [
        {key: 2, width: 100, field: 'status', label: `状态`, visible: true, dict: 'maintenance_project_type'},
        {key: 3, width: 200, field: 'projName', label: `项目名称`, visible: true},
        {key: 4, width: 200, field: 'projCode', label: `项目编码`, visible: true},
        {key: 5, width: 100, field: 'type', label: `任务单类型`, visible: true, dict: 'task_type'},
        {key: 6, width: 200, field: 'name', label: `任务单名称`, visible: true},
        {key: 7, width: 200, field: 'code', label: `任务单编号`, visible: true},
        {key: 8, width: 100, field: 'maiSecName', label: `路段名称`, visible: true},
        {key: 9, width: 100, field: 'domainName', label: `管养单位`, visible: true},
        {key: 10, width: 200, field: 'conDomainName', label: `施工单位`, visible: true},
        {key: 11, width: 200, field: 'conConName', label: `施工合同`, visible: true},
        {key: 12, width: 200, field: 'supDomainName', label: `监理单位`, visible: true},
        {key: 13, width: 200, field: 'supConName', label: `监理合同`, visible: true},
        {key: 15, width: 100, field: 'issueDate', label: `发出时间`, visible: true},
        {key: 16, width: 100, field: 'defLiaPer', label: `缺陷责任期(月)`, visible: true},
        {key: 17, width: 200, field: 'content', label: `工作内容`, visible: true},
        {key: 18, width: 200, field: 'exeRequire', label: `实施要求`, visible: true},
        {key: 21, width: 100, field: 'beginDate', label: `计划开始时间`, visible: true},
        {key: 22, width: 100, field: 'endDate', label: `计划完成时间`, visible: true},
        {key: 23, width: 100, field: 'receiveByName', label: `接收人`, visible: true},
        {key: 23, width: 100, field: 'receivePerson', label: `验收人员`, visible: true},
      ],
      tableData: [],
      detailTitle: '编辑任务单',
      openDetail: false,
      openOperateInfo: false,
      clickRow: {},
      row: {},
      methodLoading: false,
      isCopy: false,
      preview: {
        html: '',
        url: '',
        fileName: ''
      },
      ids: []
    }
  },
  props: {
    prosStatus: {
      type: Number,
      default: 1
    },
    projCode: {
      type: String,
      default: ''
    },
    taskType: {
      type: String,
      default: ''
    }
  },
  mounted() {
    if (this.projCode) this.$set(this.queryParams, 'projCode', this.projCode)
    if (this.taskType) this.$set(this.queryParams, 'type', this.taskType)
    if (this.prosStatus == 1 && !this.projCode) this.$set(this.queryParams, 'status', '0')
    this.handleQuery()
  },
  methods: {
    getNodeInfo,
    handleQuery() {
      if (this.queryParams.issueDate && this.queryParams.issueDate.length == 2) {
        this.queryParams.issueDate.startIssueDate = this.queryParams.issueDate[0]
        this.queryParams.issueDate.endIssueDate = this.queryParams.issueDate[1]
      }
      this.loading = true
      if (this.prosStatus == 2 ) {
        this.queryParams.status = 1
      }

      if (this.prosStatus == 3 ) {
        this.queryParams.status = 2
      }

      if (this.prosStatus == 4 ) {
        this.queryParams.status = 3
      }

      if (this.prosStatus == 1) {
        listConstruction(this.queryParams).then(res => {
          this.tableData = res.rows
          this.total = res.total
          this.loading = false
        })
      } else if (this.prosStatus == 5) {
        supPendingList(this.queryParams).then(res => {
          this.tableData = res.rows
          this.total = res.total
          this.loading = false
        })
      } else if (this.prosStatus == 6) {
        designPendingList(this.queryParams).then(res => {
          this.tableData = res.rows
          this.total = res.total
          this.loading = false
        })
      } else {
        pendingListConstruction(this.queryParams).then(res => {
          this.tableData = res.rows
          this.total = res.total
          this.loading = false
        })
      }

    },
    handleCloseDetail() {
      this.openDetail = false
      this.handleQuery()
    },
    resetQuery() {
      this.queryParams = {
        pageNum: 1,
        pageSize: 50
      }
      if (this.prosStatus == 1) this.$set(this.queryParams, 'status', '0')
      this.handleQuery()
    },
    handleEdit(row) {
      this.detailTitle = '编辑任务单'
      this.row = row
      this.isCopy = false
      this.openDetail = true
      this.type = row.type
      this.status = 1
    },
    handleSubmit(row) {
      this.detailTitle = '提交任务单'
      this.row = row
      this.isCopy = false
      this.openDetail = true
      this.type = row.type
      this.status = 2
    },
    handleReview(row) {
      this.detailTitle = '审核任务单'
      this.row = row
      this.openDetail = true
      this.type = row.type
      this.status = 3
    },
    handleReject(row) {
      this.detailTitle = '撤回任务单'
      this.row = row
      this.openDetail = true
      this.type = row.type
      this.status = 4
    },
    handleIssued(row) {
      this.detailTitle = '签发任务单'
      this.row = row
      this.openDetail = true
      this.type = row.type
      this.status = 5
    },
    handleReceive(row) {
      this.detailTitle = '接收任务单'
      this.row = row
      this.openDetail = true
      this.type = row.type
      this.status = 6
      if (this.prosStatus == 5) {
        this.status = 7
      }
      if (this.prosStatus == 6) {
        this.status = 8
      }
    },
    handleClickRow(e) {
      this.clickRow = e
    },
    handleOpenOperate() {
      if (!this.clickRow.id) {
        this.$message.warning('请先选择一条记录！')
        return
      }
      this.openOperateInfo = true
    },
    handleCopy() {
      if (!this.clickRow.id) {
        this.$message.warning('请先选择一条记录！')
        return
      }
      this.row = JSON.parse(JSON.stringify(this.clickRow))
      this.detailTitle = '编辑任务单'
      this.isCopy = true
      this.openDetail = true
      this.type = this.row.type
      this.status = 1
    },
    handlePreview() {
      if (!this.clickRow.id) {
        this.$modal.msgWarning("请选择一条数据")
        return
      }
      this.loading = true
      if (this.clickRow.type == 2) {
        previewCheck(this.clickRow.id).then(res => {
          if (res.code == 200){
            this.preview.html = res.data.html
            this.preview.url = res.data.downUrl
            this.preview.fileName = res.data.fileName
            this.$refs.iframeRef.visible = true
          }
          this.loading = false
        })
      } else if (this.prosStatus == 5) {
        previewSup(this.clickRow.id).then(res => {
          if (res.code == 200){
            this.preview.html = res.data.html
            this.preview.url = res.data.downUrl
            this.preview.fileName = res.data.fileName
            this.$refs.iframeRef.visible = true
          }
          this.loading = false
        })
      } else if (this.prosStatus == 6) {
        previewDesign(this.clickRow.id).then(res => {
          if (res.code == 200){
            this.preview.html = res.data.html
            this.preview.url = res.data.downUrl
            this.preview.fileName = res.data.fileName
            this.$refs.iframeRef.visible = true
          }
          this.loading = false
        })
      } else {
        previewConstruction(this.clickRow.id).then(res => {
          if (res.code == 200){
            this.preview.html = res.data.html
            this.preview.url = res.data.downUrl
            this.preview.fileName = res.data.fileName
            this.$refs.iframeRef.visible = true
          }
          this.loading = false
        })
      }
    },
    async openFileModel() {
      if (!this.clickRow.id) {
        this.$message.warning('请先选择一条记录！')
        return
      }
      this.loading = true
      // 查询附件
      await fileListConstruction(this.clickRow.id).then(async res => {
        if (res.data) {
          this.clickRow.fileId = res.data.fileId
        } else {
          // 没有新增一条
          this.clickRow.fileId = uuidv4().substring(0, 20)
          await addFileConstruction(this.clickRow)
        }
        this.loading = false
        this.openFile = true
      })
    },
    handleDelete(row) {
      this.$modal.confirm('是否删除').then(() => {
        deleteConstruction(row.id).then(res => {
          this.handleQuery()
        })
      })
    },
    loadData(row) {
      this.methodLoading = true
      viewConstructionDetail({projConId: row.id}).then(res => {
        this.$set(row, 'constructionDetailList', res.rows)
        this.methodLoading = false
      })
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection
    },
    handleRegenerate() {
      if (this.ids.length == 0) {
        this.$modal.msgError("请勾选至少一条数据")
        return
      }
      const params = {
        idList: this.ids.map(item => item.id),
        type: 7
      }
      regenerateReport(params).then(res => {
        this.$modal.msgSuccess("操作成功")
      })
    },
    // 导出清单按钮
    exportList() {
      if (this.queryParams.issueDate && this.queryParams.issueDate.length == 2) {
        this.queryParams.issueDate.startIssueDate = this.queryParams.issueDate[0]
        this.queryParams.issueDate.endIssueDate = this.queryParams.issueDate[1]
      }
      if (this.prosStatus == 2 ) {
        this.queryParams.status = 1
      }
      if (this.prosStatus == 3 ) {
        this.queryParams.status = 2
      }
      if (this.prosStatus == 4 ) {
        this.queryParams.status = 3
      }
      let url = ''
      if (this.prosStatus == 1) {
        url = 'manager/theft/construction/export'
      } else if (this.prosStatus == 5) {
        url = 'manager/theft/construction/sup/pending/export'
      } else if (this.prosStatus == 6) {
        url = 'manager/theft/construction/design/pending/export'
      } else {
        url = 'manager/theft/construction/pending/export'
      }
      this.download(
          url,
        {...this.queryParams},
        `taskList_${new Date().getTime()}.xlsx`,
        {
          headers: {'Content-Type': 'application/json;'},
          parameterType: 'body'
        }
      )
    },
  }
}
</script>

<style lang="scss" scoped>

</style>
<style lang="scss" scoped>
@import "@/assets/styles/business.scss";
</style>
