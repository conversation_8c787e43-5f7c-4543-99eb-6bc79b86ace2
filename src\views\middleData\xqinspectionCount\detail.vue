<template>
  <div >
    <el-row>
      <el-table size="mini" style="width: 100%" v-loading="loading" border
                highlight-current-row
                @row-click="clickRow" @select="selectChange"
                @select-all="handleSelectionChange"
                ref="table"
                :data="tableDataList"
                :height="showSearch ? 'calc(100vh - 320px)' : 'calc(100vh - 260px)'"
      >
        <el-table-column fixed label="序号" type="index" width="50" align="center">
          <template v-slot="scope">
            {{ (scope.$index + (queryParams.pageNum - 1) * queryParams.pageSize) + 1 }}
          </template>
        </el-table-column>
        <el-table-column label="巡查时间" align="center" prop="inspectionTime" show-overflow-tooltip/>
        <el-table-column label="巡查状态" align="center" prop="stage" show-overflow-tooltip>
          <template slot-scope="scope">
            <el-tag v-if="scope.row.stage == '0'" type="plain">未巡查</el-tag>
            <el-tag v-else type="success">已巡查</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="名称" align="center" prop="riskName" show-overflow-tooltip/>
        <el-table-column label="管理处" align="center" prop="managementOffice" show-overflow-tooltip/>
        <el-table-column label="养护路段" align="center" prop="maintenanceSection" show-overflow-tooltip/>
        <el-table-column label="公路编号" align="center" prop="roadNumber" show-overflow-tooltip/>
        <el-table-column label="隐患类型" align="center" prop="disasterType" width="100" show-overflow-tooltip/>
        <el-table-column label="隐患等级" align="center" prop="hazardLevel" show-overflow-tooltip>
          <template slot-scope="scope">
            <span>{{ formatHazardLevel(scope.row.hazardLevel) }}</span>
          </template>
        </el-table-column>
        <el-table-column label="桩号" align="center" prop="stake" show-overflow-tooltip>
          <template slot-scope="scope">
            {{ formatPile(scope.row.stake) }}
            <!--            <span>{{ formatStakeNumber(scope.row.stake) }}</span>-->
          </template>
        </el-table-column>

      </el-table>
<!--      <pagination-->
<!--        v-show="total>0"-->
<!--        :total="total"-->
<!--        :page.sync="queryParams.pageNum"-->
<!--        :limit.sync="queryParams.pageSize"-->
<!--        @pagination="getList"-->
<!--      />-->
    </el-row>
  </div>
</template>

<script>
import {listAllRoute} from "@/api/system/route";
import CascadeSelection from "./CascadeSelectionManagementOffice.vue";
import {countXqHazard} from "@/api/middleData/xqinspection";
import {formatPile} from "@/utils/ruoyi"

export default {
  components: {CascadeSelection},
  props: {
    maintenanceSectionId: {
      type: String,
      default: '',
    },
  },
  data() {
    return {
      // 显示搜索条件
      showSearch: false,
      // 遮罩层
      visible: false,
      loading: false,
      // 选中数组值
      assetIds: [],
      // 总条数
      total: 0,
      // 表数据
      tableDataList: [],
      routeList: [],
      patrolTime: [
        this.getDate(),
        this.getDate()
      ],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 20,
        tunnelName: null,
        tunnelCode: null,
        routeCode: null,
        managementOfficeId: null,
        maintenanceSectionId: null,
        hazardLevel: null,
        roadNumber: null,
        // isCheck: '0',
        // patrolTime: this.getDate(),

      },
      //提交参数
      params: {},
      maintenanceSectionList: [],
      // 部门树选项
      deptOptions: [],
      currentRow: null,
      // 字典数据
      hazardLevelOptions: [
        {dictLabel: '重大隐患', dictValue: 1},
        {dictLabel: '较大隐患', dictValue: 2},
        {dictLabel: '一般隐患', dictValue: 3},
        {dictLabel: '无', dictValue: 0},
      ],
      // 审核状态字典
      auditStatusOptions: [
        {dictLabel: '待审核', dictValue: 1},
        {dictLabel: '审核通过', dictValue: 2},
        {dictLabel: '审核不通过', dictValue: 3},
      ],  // 公路类型字典
      roadTypeOptions: [
        {dictLabel: '高速公路', dictValue: 1},
        {dictLabel: '普通公路', dictValue: 2},
      ],
      measuresOptions: [
        {dictLabel: '是', dictValue: 1},
        {dictLabel: '否', dictValue: 0},
      ],
      // 图片预览数据
      imagePreview: {
        visible: false,
        url: null
      },
      pickerOptions: {
        shortcuts: [
          {
            text: '最近一周',
            onClick(picker) {
              const end = new Date()
              const start = new Date()
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 7)
              picker.$emit('pick', [start, end])
            },
          },
          {
            text: '最近一个月',
            onClick(picker) {
              const end = new Date()
              const start = new Date()
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 30)
              picker.$emit('pick', [start, end])
            },
          },
          {
            text: '最近三个月',
            onClick(picker) {
              const end = new Date()
              const start = new Date()
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 90)
              picker.$emit('pick', [start, end])
            },
          },
        ],
      },
    };
  },
  created() {

    // this.handleQuery()
  },
  methods: {
    formatPile,
    // 显示弹框
    show() {
      this.getList();
      this.visible = true;
    },
    clickRow(row) {
      this.$refs.table.toggleRowSelection(row);
      this.setChecked(row);
    },
    selectChange(arr, row) {
      //退选和选中处理
      this.setChecked(row);
    },
    //退选和选中处理
    setChecked(row) {
      if (this.assetIds.includes(row.assetId)) {
        this.assetIds = this.assetIds.filter(i => i !== row.assetId);
      } else {
        this.assetIds.push(row.assetId)
      }
      if (!this.isAddAll) this.currentRow = row;
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      if (selection.length > 0) {
        selection.forEach(item => {
          // this.setChecked(item);
          if (!this.assetIds || !this.assetIds.includes(item.assetId)) {
            this.assetIds.push(item.assetId)
          }
        })
      } else {
        this.tableDataList.forEach(item => {
          this.assetIds = this.assetIds.filter(i => i !== item.assetId);
        })
      }
    },
    /** 查询路线列表 */
    getRouteList() {
      listAllRoute().then(res => {
        this.routeList = res.data
      })
    },
    // 查询表数据
    getList() {
      this.loading = true;

      const tempParams = {
        ...this.queryParams,
        tempName: this.queryParams.riskName
      }

      if (this.patrolTime) {
        tempParams.startTime = this.patrolTime[0];
        tempParams.endTime = this.patrolTime[1];
      }
      // if (this.queryParams.patrolTime) {
      //   tempParams.patrolTime += " 23:59:59"
      // }
      this.queryParams.ruleIdsFlag = true;
      countXqHazard(tempParams).then(response => {
        this.tableDataList = response.rows
        this.total = response.total
        this.loading = false
      })
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.queryParams.maintenanceSectionId = null
      this.queryParams.roadNumber = null

      this.handleQuery();

    },
    /** 提交选择养护路段操作 */
    handleSelectBridge() {
      let data;
      if (this.isAddAll) {
        if (!this.assetIds) {
          this.$modal.msgError("请选择隧道");
          return;
        }
        data = this.assetIds;
      } else {
        if (!this.currentRow) {
          this.$modal.msgError("请选择隐患");
          return;
        }
        data = this.currentRow;
      }
      this.$emit('update:checkEntity', data);
      this.$emit('change', data);
      this.assetIds = [];
      this.visible = false;
    },
    // 格式化隐患等级显示
    formatHazardLevel(hazardLevel) {
      const dict = this.hazardLevelOptions.find((item) => item.dictValue === hazardLevel);
      return dict ? dict.dictLabel : hazardLevel;
    },

    // 格式化风险等级显示
    formatRiskLevel(riskLevel) {
      const dict = this.riskLevelOptions.find((item) => item.dictValue === riskLevel);
      return dict ? dict.dictLabel : riskLevel;
    },

    // 格式化是否采取措施显示
    formatMeasures(status) {
      const dict = this.measuresOptions.find((item) => item.dictValue === status);
      return dict ? dict.dictLabel : status;
    },

    // 格式化审核状态显示
    formatAuditStatus(status) {
      const dict = this.auditStatusOptions.find((item) => item.dictValue === status);
      return dict ? dict.dictLabel : status;
    },

    // 格式化公路类型显示
    formatRoadType(type) {
      const dict = this.roadTypeOptions.find((item) => item.dictValue === type);
      return dict ? dict.dictLabel : type;
    },
    /** 格式化桩号显示 */
    formatStakeNumber(value) {
      return formatPile(value);
    },
    /** 图片预览 */
    previewImage(url) {
      this.imagePreview.url = url;
      this.imagePreview.visible = true;
    },
    getDate() {
      const now = new Date();
      const formatted = `${now.getFullYear()}-${
        (now.getMonth() + 1).toString().padStart(2, '0') // 补零至两位数
      }-${now.getDate().toString().padStart(2, '0')}`;
      console.log(formatted);
      return formatted
    }
  }
};
</script>
<style>
.el-table__body tr.current-row > td.el-table__cell {
  background: #b7daff;
}

</style>
