<template>
	<div class="upload-file" @click.stop>
		<el-upload
			ref="fileUpload"
			multiple
			:action="uploadFileUrl"
			:before-upload="handleBeforeUpload"
			:before-remove="beforeRemove"
			:http-request="customUpload"
			:file-list="fileList"
			:limit="limit"
			:accept="computedAccept"
			:on-error="handleUploadError"
			:on-exceed="handleExceed"
			:on-success="handleUploadSuccess"
			:show-download-icon="true"
			:class="'upload-file-uploader' + ' ' + (forView || isLimitReached ? 'uploadBox-hide' : '')"
			:headers="headers"
			:drag="false"
		>
			<el-button size="small" type="primary" v-if="!forView">点击上传</el-button>
			<div slot="file" slot-scope="{file}">
				<el-popover
          :key="file.id"
					placement="top-start"
					trigger="click"
					:ref="`popover-${file.id}`"
					@show="showPop(`popover-${file.id}`)"
					>
          <div style="margin: 0; padding: 0; display: flex; align-items: center; justify-content: center;">
						<el-button-group>
							<el-button size="mini" type="primary" @click="handlePictureCardPreview(file)" icon="el-icon-view"></el-button>
							<el-button size="mini" v-if="!disabled" type="primary" @click="handleDownload(file)" icon="el-icon-download" ></el-button>
							<el-button size="mini" v-if="!disabled && !forView" type="primary" @click="handleRemove(file)" icon="el-icon-delete"></el-button>
						</el-button-group>
					</div>
          <template #reference>
            <el-tooltip
              :content="file.name"
              placement="top"
              :disabled="!file.name"
            >
            <span class="el-upload-list__item-name" style="text-align: left; display: inline-flex; align-items: center;" @click.stop>
              <i class="el-icon-document"></i>
              <span style="margin-left: 5px; max-width: 70px; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;">
                {{ file.name }}
              </span>
            </span>
            </el-tooltip>
          </template>
				</el-popover>

				<label class="el-upload-list__item-status-label">
					<i class="el-icon-upload-success el-icon-circle-check"></i>
				</label>
			</div>
			<!-- 上传提示 -->
			<div v-if="showTip && !forView" slot="tip" class="el-upload__tip">
				请上传
				<template v-if="fileSize">
					大小不超过
					<b style="color: #f56c6c">{{ fileSize }}MB</b>
				</template>
				<template v-if="fileType">
					格式为
					<b style="color: #f56c6c">{{ fileType.join('/') }}</b>
				</template>
				的文件
			</div>
		</el-upload>
		<el-dialog
			:width="previewWidth"
			:visible.sync="dialogVisible"
			custom-class="fixed-dialog"
			append-to-body
		>
			<template v-if="showTitle && dialogTitle" #title>
				<span v-html="dialogTitle" class="dialog-title"></span>
			</template>
			<OfficePreview
				:excel-options="excelOptions"
				:dialog-url="dialogUrl"
				:content-type="contentType"
				v-if="dialogVisible"
			/>
		</el-dialog>
	</div>
</template>

<script>
import { getToken } from '@/utils/auth'
import { findFiles, updateFileSort, updateFileRemark } from '@/api/file/index.js'
import { removeFile, addUploadRecord, removeUploadRecord } from '@/api/system/fileUpload.js'
import OfficePreview from '@/components/OfficePreview/index.vue'
import Sortable from 'sortablejs'
import { v4 as uuidv4 } from 'uuid'
import axios from 'axios'

export default {
	name: 'FileUpload4Examine',
	components: {
		OfficePreview,
	},
	props: {
		value: {
			// 绑定值传ownerId，可以是id数组
			type: undefined,
			default: () => [],
		},
		// 数量限制
		limit: {
			type: Number,
			default: null,
		},
		// 大小限制(MB)
		fileSize: {
			type: Number,
			default: null,
		},
		// 文件类型, 例如['png', 'jpg', 'jpeg']
		fileType: {
			type: Array,
			//   default: () => ["doc", "xls", "ppt", "txt", "pdf", "png", "jpg", "jpeg"],
			default: null,
		},
		accept: {
			type: String,
			default: null,
		},
		// 是否显示提示
		isShowTip: {
			type: Boolean,
			default: true,
		},
		fileSubId: {
			type: Number,
			default: 0,
		},
		bizId: {
			type: String,
			default: '',
		},
		assetType: {
			// 资产文件管理部分。添加assetType后，会根据assetType返回相应的是否还有数据
			type: Number,
			default: null,
		},
		ownerId: {
      type: String,
      default: null,
		},
		storagePath: {
			// 存路径用于分类
			type: String,
			default: '',
		},
		platform: {
			// 存储桶用于分类
			type: String,
			default: 'ylzx',
		},
		forView: {
			// 当查看时传true
			type: Boolean,
			default: false,
		},
		showFileName: {
			// 是否显示文件名
			type: Boolean,
			default: false,
		},
		showTitle: {
			// 是否显示标题
			type: Boolean,
			default: false,
		},
		previewWidth: {
			type: String,
			default: '',
		},
		downloadName: {
			// 自定义下载文件名
			type: String,
			default: null,
		},
		needTime: {
			// 是否需要给下载文件名加年月日时分秒 例如：文件名_2021-01-01T12_00_00
			type: Boolean,
			default: false,
		},
		successHook: {
			// 上传成功后的回调
			type: Function,
			default: () => {},
		},
		excelOptions: {
			// Luckeysheet的配置项
			type: Object,
			default: () => ({}),
		},
		isStatic: {
			// 是否是本地存储，url永不过期
			type: Boolean,
			default: false,
		},
		canSort: {
			// 开启拖动排序
			type: Boolean,
			default: false,
		},
		dragSortHook: {
			// 拖拽排序后的回调
			type: Function,
			default: null,
		},
		needRemark: {
			// 是否需要添加备注
			type: Boolean,
			default: false,
		},
	},
	data() {
		return {
			number: 0,
			currentSort: 0,
			uploadList: [],
			uploadFileUrl: `${process.env.VUE_APP_BASE_API}/file/upload`,
			headers: {
				Authorization: 'Bearer ' + getToken(),
			},
			fileList: [],
			dialogUrl: '',
			dialogTitle: '',
			contentType: '',
			dialogVisible: false,
			disabled: false,
			editingFile: null,
			isClickingButton: false,
      actualOwnerId: this.ownerId || new Date().getTime().toString(),

    }
	},
	computed: {
		// 是显示提示
		showTip() {
			return this.isShowTip && (this.fileType || this.fileSize)
		},
		// 生成 accept 属性值
		computedAccept() {
			// 如果 accept 传入了（不是 null），直接使用传入的值
			if (this.accept !== null) {
				return this.accept
			}
			// 如果 fileType 不为空，则生成 accept 字符串
			if (this.fileType && this.fileType.length > 0) {
				return this.fileType.map((type) => `.${type}`).join(',')
			}
			// 否则返回空字符串
			return ''
		},
		isLimitReached() {
			return this.limit !== null && this.fileList.length >= this.limit
		},
	},
	watch: {
		value: {
			handler(val) {
				if (val) {
					// 首先将值转为数组
					const list = Array.from(new Set(Array.isArray(val) ? val : this.value.split(',')))
					// 然后将数组转为对象数组
					const tempArr = []
					list.forEach(async (ownerId) => {
						const { data } = await findFiles({ ownerId })
						tempArr.push(
							...data.map((file) => {
								return {
									...file,
									name: file.originalFilename,
									url: file.url,
									ownerId: file.ownerId,
									sort: file.sort,
									thumbUrl: this.getFileThumbnailUrl(file.contentType, file.thumbUrl),
								}
							})
						)
					})
					this.fileList = tempArr
				} else {
					this.fileList = []
				}
			},
			deep: true,
			immediate: true,
		},
	},
	mounted() {
		if (this.canSort) {
			this.$nextTick(() => {
				this.initDragSort()
			})
		}
	},
	methods: {
		// 自定义上传函数
		customUpload({ file }) {
			const formData = new FormData()
			formData.append('file', file)

			const url = `${this.uploadFileUrl}?platform=${this.platform}&ownerId=${this.actualOwnerId}&storagePath=${this.storagePath}&isStatic=${this.isStatic}&sort=${this.currentSort}`

			axios
				.post(url, formData, {
					headers: {
						'Content-Type': 'multipart/form-data',
						...this.headers,
					},
				})
				.then((response) => {
					this.handleUploadSuccess(response.data, file)
				})
				.catch((error) => {
					this.handleUploadError(error)
				})
		},
		// 上传前校检格式和大小
		handleBeforeUpload(file) {
			// 校检文件类型
			if (this.fileType) {
				const fileName = file.name.split('.')
				const fileExt = fileName[fileName.length - 1]
				const isTypeOk = this.fileType.indexOf(fileExt) >= 0
				if (!isTypeOk) {
					this.$modal.msgError(`文件格式不正确, 请上传${this.fileType.join('/')}格式文件!`)
					return false
				}
			}
			// 校检文件大小
			if (this.fileSize) {
				const isLt = file.size / 1024 / 1024 < this.fileSize
				if (!isLt) {
					this.$modal.msgError(`上传文件大小不能超过 ${this.fileSize} MB!`)
					return false
				}
			}
			// 更新 sort 值
			this.currentSort = this.getMaxSort() + this.number
			this.$modal.loading('正在上传文件，请稍候...')
			this.number++
			return true
		},
		getMaxSort() {
			const fileListMax = Math.max(...this.fileList.map((file) => file.sort || 0), 0)
			const uploadListMax = Math.max(...this.uploadList.map((file) => file.sort || 0), 0)
			return (
				Math.max(
					fileListMax,
					uploadListMax,
					(this.fileList.length + this.uploadList.length - 1) * 1000
				) + 1000
			)
		},
		// 文个数超出
		handleExceed() {
			this.$modal.msgError(`上传文件数量不能超过 ${this.limit} 个!`)
		},
		// 上传失败
		handleUploadError(err) {
			this.$modal.msgError('上传文件失败，请重试')
			this.$modal.closeLoading()
		},
		// 上传成功回调
		handleUploadSuccess(res, file) {
			if (res.code === 200) {
				// 根据文件类型设置缩略图
				const thumbUrl = this.getFileThumbnailUrl(res.data.contentType, res.data.thumbUrl)
				const temp = {
					ext: res.data.ext,
					thumbUrl: thumbUrl,
					id: res.data.id,
					remoteUrl: res.data.url,
					contentType: res.data.contentType,
					name: res.data.originalFilename,
					url: res.data.url,
					uid: file.uid,
					ownerId: res.data.ownerId,
					sort: res.data.sort,
				}
				this.uploadList.push(temp)
				if (!(this.bizId === '' && this.fileSubId === 0)) {
					addUploadRecord({
						fileSubId: this.fileSubId,
						bizId: this.bizId,
						fileName: res.data.originalFilename,
						fileId: res.data.id,
						fileSize: res.data.fileSize,
						ownerId: res.data.ownerId,
						assetType: this.assetType,
					}).then()
				}
				this.uploadedSuccessfully()
				if (this.successHook) {
					this.successHook(res.data)
				}
        // 触发自定义事件，将 ownerId 返回给父组件
        this.$emit('update:ownerId', res.data.ownerId)
			} else {
				this.number--
				// this.$modal.closeLoading();
				this.$modal.msgError(res.msg)
				this.fileList.splice(findex, 1)
				// this.$emit("input", this.formatValue(this.fileList));
				// this.$refs.fileUpload.handleRemove(file);
				this.uploadedSuccessfully()
			}
		},
		// 上传结束处理
		uploadedSuccessfully() {
			if (this.number > 0 && this.uploadList.length === this.number) {
				this.fileList = this.fileList.concat(this.uploadList)
				// 按照fileList的sort排序
				this.fileList.sort((a, b) => a.sort - b.sort)
				this.uploadList = []
				this.number = 0
				this.$emit('input', this.formatValue(this.fileList))
				this.$modal.closeLoading()
			}
		},
		// 获取文件名称
		getFileName(name) {
			// 如果是url那么取最后的名字 如果不是直接返回
			if (name.lastIndexOf('/') > -1) {
				return name.slice(name.lastIndexOf('/') + 1)
			} else {
				return name
			}
		},
		// 格式化返回对象
		formatValue(list) {
			return list.filter((item) => item.ownerId).map((el) => el.ownerId)
		},
		handleRemove(file) {
			this.$confirm('确定删除该文件吗？', '提示', {
				confirmButtonText: '确定',
				cancelButtonText: '取消',
				type: 'warning',
			}).then(() => {
				if (!file.id) return
				const findex = this.fileList.map((f) => f.id).indexOf(file.id)
				if (findex > -1) {
					if (this.bizId === '' && this.fileSubId === 0) {
						removeFile(file.id).then((res) => {
							if (res.code === 200) {
								this.fileList.splice(findex, 1)
								this.$emit('input', this.formatValue(this.fileList))
								this.$message({
									type: 'success',
									message: '删除成功!',
								})
							}
						})
					} else {
						removeUploadRecord({
							fileId: file.id,
							assetType: this.assetType,
							fileSubId: this.fileSubId,
						}).then(() => {
							removeFile(file.id).then((res) => {
								if (res.code === 200) {
									this.fileList.splice(findex, 1)
									this.$emit('input', this.formatValue(this.fileList))
									this.$message({
										type: 'success',
										message: '删除成功!',
									})
								}
							})
						})
					}
				}
			})
		},
		handlePictureCardPreview(file) {
			this.contentType = file.contentType
			this.dialogUrl = file.url
			this.dialogTitle = file.name
			this.dialogVisible = true
		},
		showPop(popid){
			for (const key in this.$refs) {
				if (key.indexOf('popover-') !== -1 && key!=popid) {
					let pop=this.$refs[key];
					if(pop){
						this.$refs[key].doClose();
					}
				}
			}
		},
		async handleDownload(file) {
			const url = file.url || (file.response && file.response.url)
			if (!url) {
				this.$message.error('文件无法下载，未找到文件的URL')
				return
			}

			try {
				// 创建一个不包含 Authorization 的请求头
				const downloadHeaders = { ...this.headers }
				delete downloadHeaders.Authorization
				// 使用 axios 进行下载，因为它可以自动处理跨域认证
				const response = await axios({
					url: url,
					method: 'GET',
					responseType: 'blob',
					headers: downloadHeaders, // 使用不含 Authorization 的请求头
				})

				const blob = new Blob([response.data])
				const link = document.createElement('a')
				link.href = URL.createObjectURL(blob)

				const originalFileName = this.getFileName(file.name) || 'download'
				const fileExtension = originalFileName.split('.').pop()
				let fileName = this.downloadName || originalFileName

				// 如果 fileName 已经有后缀，去掉后缀
				if (fileName.includes('.')) {
					fileName = fileName.substring(0, fileName.lastIndexOf('.'))
				}

				const timestamp = this.needTime ? `_${this.$moment().format('YYYY-MM-DDTHH_mm_ss')}` : ''

				// 生成最终的文件名，确保后缀在最后
				const finalFileName = `${fileName}${timestamp}.${fileExtension}`

				link.download = finalFileName
				document.body.appendChild(link)
				link.click()
				document.body.removeChild(link)
				URL.revokeObjectURL(link.href)
			} catch (error) {
				console.error('下载文件时发生错误:', error)
				this.$message.error('下载文件失败，请重试')
			}
		},
		initDragSort() {
			if (this.$refs.fileUpload && this.$refs.fileUpload.$el) {
				const el = this.$refs.fileUpload.$el.querySelector('.el-upload-list')
				if (el) {
					Sortable.create(el, {
						onEnd: ({ oldIndex, newIndex }) => {
							if (oldIndex !== newIndex) {
								// 只有当位置确实发生变化时才执行
								const arr = this.fileList
								const page = arr[oldIndex]
								arr.splice(oldIndex, 1)
								arr.splice(newIndex, 0, page)

								// 上传fileList
								this.dragSort()

								// 执行钩子函数
								if (this.dragSortHook) {
									this.dragSortHook(this.fileList)
								}
							}
						},
					})
				}
			}
		},
		dragSort() {
			const request = this.fileList.map((file) => {
				return {
					fileId: file.id,
					sort: file.sort,
				}
			})
			updateFileSort(request).then((res) => {
				if (res.code === 200) {
					const data = res.data
					// 创建一个以 fileId 为键，sort 为值的对象
					const sortMap = {}
					data.forEach((item) => {
						sortMap[item.id] = item.sort
					})
					// 更新 fileList 中的 sort 值
					this.fileList.forEach((file) => {
						if (sortMap.hasOwnProperty(file.id)) {
							file.sort = sortMap[file.id]
						}
					})
				}
			})
		},
		// 开始编辑备注
		startEditing(file) {
			if (!file.isEditing) {
				this.$set(file, 'isEditing', true)
				this.isClickingButton = false // 重置交互标志
			}
			if (this.editingFile && this.editingFile !== file && !this.forView) {
				this.$confirm('是否保存当前编辑的备注?', '提示', {
					confirmButtonText: '确定',
					cancelButtonText: '取消',
					type: 'warning',
				})
					.then(() => {
						this.saveRemark(this.editingFile)
						this.setFileEditing(file)
						this.$nextTick(() => {
							this.$refs['remarkTextarea-' + file.id].focus()
						})
					})
					.catch(() => {
						this.cancelEdit(this.editingFile)
						this.setFileEditing(file)
						this.$nextTick(() => {
							this.$refs['remarkTextarea-' + file.id].focus()
						})
					})
			} else {
				this.setFileEditing(file)
				this.$nextTick(() => {
					this.$refs['remarkTextarea-' + file.id].focus()
				})
			}
		},

		// 设置文件为编辑状态
		setFileEditing(file) {
			this.editingFile = file
			this.$set(file, 'isEditing', true)
			this.$set(file, 'tempRemark', file.remark || '')
			this.isClickingButton = false // 重置交互标志
		},

		// 保存备注
		saveRemark(file) {
			if (this.forView) return
			this.isClickingButton = true
			this.$set(file, 'remark', file.tempRemark)
			this.$set(file, 'isEditing', false)
			this.editingFile = null
			const entity = {
				fileId: file.id,
				remark: file.remark,
			}
			// 保存备注
			updateFileRemark(entity).then((res) => {
				// 可以在这里添加成功提示
			})
		},

		// 取消编辑
		cancelEdit(file) {
			this.isClickingButton = true
			this.$set(file, 'isEditing', false)
			this.editingFile = null
		},
		// 输入框失焦时的处理
		blurRemark(file) {
			setTimeout(() => {
				if (file.isEditing && !this.isClickingButton) {
					if (file.tempRemark !== file.remark && !this.forView) {
						// 只有当备注内容发生变化时才弹出确认框
						this.$confirm('是否保存当前编辑的备注?', '提示', {
							confirmButtonText: '保存',
							cancelButtonText: '放弃',
							type: 'warning',
						})
							.then(() => {
								this.saveRemark(file)
							})
							.catch(() => {
								this.cancelEdit(file)
							})
					} else {
						// 如果内容没有变化，直接取消编辑
						this.cancelEdit(file)
					}
				}
				this.isClickingButton = false
			}, 100)
		},
		beforeRemove(file, fileList) {
			// 避免点击 Backspace 删除
			if (window.event.type == 'keydown' && window.event.keyCode == 8) return false

			return true
		},
		reset() {
			this.fileList = []
		},
		handleBackspace(event, file) {
			// 只有当输入框为空时才阻止默认行为
			if (file.tempRemark === '') {
				event.preventDefault()
			}
		},
		// 根据文件类型获取缩略图
		getFileThumbnailUrl(contentType, thumbUrl) {
			if (contentType.startsWith('image/')) {
				// 如果是图片类型，直接返回图片 URL
				return thumbUrl
			} else {
				switch (contentType) {
					case 'application/pdf':
						return require('@/assets/images/Ipdf.png')
					case 'application/msword':
					case 'application/vnd.openxmlformats-officedocument.wordprocessingml.document':
						return require('@/assets/images/Iword.png')
					case 'application/vnd.ms-excel':
					case 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet':
						return require('@/assets/images/Iexcel.png')
					case 'application/vnd.ms-powerpoint':
					case 'application/vnd.openxmlformats-officedocument.presentationml.presentation':
						return require('@/assets/images/Ippt.png')
					case 'application/zip':
					case 'application/x-zip-compressed':
					case 'application/x-rar-compressed':
					case 'application/x-7z-compressed':
					case 'application/x-tar':
					case 'application/gzip':
						return require('@/assets/images/Izip.png')
					default:
						return require('@/assets/images/Iother.png') // 默认文件图标
				}
			}
		},
	},
}
</script>

<style scoped lang="scss">
.upload-file-uploader {
	margin-bottom: 5px;
}
.fixed-dialog {
	position: fixed !important;
	top: 50% !important;
	left: 50% !important;
	transform: translate(-50%, -50%) !important;
	z-index: 999 !important; /* 确保弹窗在所有元素之上 */
}
::v-deep .uploadBox-hide .el-upload--picture-card {
	display: none;
}

.file-name-overlay {
	position: absolute;
	bottom: 0; /* 或者根据需求调整位置 */
	left: 0;
	width: 100%;
	background-color: rgba(0, 0, 0, 0.5); /* 半透明背景 */
	color: white; /* 文本颜色 */
	padding: 2px 5px;
	text-align: center; /* 文本居中 */
	z-index: 1; /* 确保在最上层 */
	box-sizing: border-box; /* 确保 padding 不影响宽度 */
}
.el-upload-list__item-actions {
	z-index: 2;
}

.el-upload-list__item-actions > span {
	cursor: pointer;
}

.dialog-title {
	font-size: 20px; /* 调整字体大小 */
	font-weight: bold; /* 加粗 */
	color: #708090;
}

.remark-container {
	position: absolute;
	bottom: -60px;
	left: 0;
	width: 100%;
	background-color: rgba(255, 255, 255, 0.8);
	backdrop-filter: blur(5px);
	color: #333;
	padding: 5px;
	box-sizing: border-box;
	border-radius: 0 0 4px 4px;
	box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
	transition: bottom 0.3s ease;
	z-index: 3;

	&.editing {
		bottom: 0;
		transition: none;
	}
}

.el-upload-list__item:hover .remark-container:not(.editing) {
	bottom: 0;
}

.remark-display {
	cursor: pointer;
	white-space: nowrap;
	overflow: hidden;
	text-overflow: ellipsis;
}

.remark-edit {
	display: flex;
	flex-direction: column;

	textarea {
		width: 100%;
		background-color: rgba(255, 255, 255, 0.8);
		border: 1px solid #dcdfe6;
		padding: 5px;
		margin-bottom: 5px;
		border-radius: 2px;
		resize: none; // 禁止拉伸
		min-height: 60px; // 设置最小高度
		overflow-y: auto; // 添加垂直滚动条
	}
}

.remark-actions {
	display: flex;
	justify-content: space-around;
	margin-top: 5px;

	.el-button {
		padding: 5px;
		width: 24px;
		height: 24px;

		&.el-button--mini {
			font-size: 12px;
		}

		i {
			margin-right: 0;
		}
	}
}

// 调整上传列表项的样式以适应新的备注区域
.el-upload-list__item {
	margin-bottom: 65px; // 为悬浮的备注区域留出空间

	&:hover .remark-container {
		bottom: 0; // 鼠标悬停时显示完整的备注区域
	}
}

// 确保操作按钮不被备注区域遮挡
.el-upload-list__item-actions {
	z-index: 2; // 增加z-index以确保在备注区域之上
}

// 添加这个样式来确保图标正确显示
.el-icon-close::before {
	content: '\e6db';
}

::v-deep .el-upload-dragger .el-icon-plus {
	font-size: 28px;
	color: #8c939d;
	margin-bottom: 8px;
}

::v-deep .el-upload-dragger .el-upload__text {
	font-size: 12px;
	color: #606266;
	text-align: center;
	line-height: 1.2;
	padding: 0 5px;
}
</style>
