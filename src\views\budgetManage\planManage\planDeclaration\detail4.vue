<template>
  <div class="maindiv">
    <el-form :model="formData"  ref="queryForm" :inline="true" class="demo-form-inline" :rules="rules" label-width="150px">
      <el-form-item label="里程桩号" prop="mileageStr">
        <el-input v-model="formData.mileageStr" placeholder="请输入里程桩号" style="width:220px" clearable :disabled="optionModel=='read'">
        </el-input>
      </el-form-item>
      <el-form-item label="是否缺陷责任期内" prop="isDuty">
        <el-radio v-model="formData.isDuty" :label="0" :disabled="optionModel=='read'">否</el-radio>
        <el-radio v-model="formData.isDuty" :label="1" :disabled="optionModel=='read'">是</el-radio>
      </el-form-item>
      <el-form-item label="主线里程(双幅)(km)" prop="mianMileage">
        <el-input v-model="formData.mianMileage" @input="handleInput" type="number" placeholder="请输入主线里程" style="width:220px" clearable :disabled="optionModel=='read'">
        </el-input>
      </el-form-item>
      <el-form-item label="隧道里程(双幅)(km)" prop="tunnlMileage">
        <el-input v-model="formData.tunnlMileage" @input="handleInput" type="number" placeholder="请输入隧道里程" style="width:220px" clearable :disabled="optionModel=='read'">
        </el-input>
      </el-form-item>
      <el-form-item label="桥梁里程(双幅)(km)" prop="bridgeMileage">
        <el-input v-model="formData.bridgeMileage" @input="handleInput" type="number" placeholder="请输入桥梁里程" style="width:220px" clearable :disabled="optionModel=='read'">
        </el-input>
      </el-form-item>
      <el-form-item label="公路等级" prop="hgrade">
        <dict-select type="sys_budget_route_grade" onlyTailNode @change="routeGradeChange" clearable v-model="formData.hgrade" placeholder="请选择公路等级" style="width: 220px" :disabled="optionModel=='read'"></dict-select>
      </el-form-item>
      <el-form-item label="车道数" prop="lane">
        <dict-select type="sys_budget_lane" onlyTailNode clearable v-model="formData.lane" @change="laneChange" placeholder="请选择车道数" style="width: 220px" :disabled="optionModel=='read'"></dict-select>
      </el-form-item>
      <el-form-item label="公路基本费用(元)" prop="baseFund">
        <el-input v-model="formData.baseFund" placeholder="请输入公路基本费用" disabled style="width:220px" clearable>
        </el-input>
      </el-form-item>
      <el-form-item label="通行费调整系数" prop="adCoefficient">
        <el-input v-model="formData.adCoefficient" @input="handleInput" type="number" placeholder="请输入通行费调整系数" style="width:220px" clearable :disabled="optionModel=='read'">
        </el-input>
      </el-form-item>
      <el-form-item label="通行年限调整系数" prop="yearCoefficient">
        <el-input v-model="formData.yearCoefficient" @input="handleInput" type="number" placeholder="请输入通行年限调整系数" style="width:220px" clearable :disabled="optionModel=='read'">
        </el-input>
      </el-form-item>
      <el-form-item label="绿化调整系数" prop="greenCoefficient">
        <el-input v-model="formData.greenCoefficient" @input="handleInput" type="number" placeholder="请输入绿化调整系数" style="width:220px" clearable :disabled="optionModel=='read'">
        </el-input>
      </el-form-item>
      <el-form-item label="总费用" prop="sumFund">
        <el-input v-model="formData.sumFund" type="number" placeholder="请输入总费用" style="width:220px" clearable :disabled="optionModel=='read'">
        </el-input>
      </el-form-item>
      <el-form-item label="备注" prop="remark">
        <el-input v-model="formData.remark" type="textarea" placeholder="请输入备注" style="width:600px" clearable :disabled="optionModel=='read'">
        </el-input>
      </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer" style="display: flex;justify-content: flex-end;margin-top: 10px;">
      <el-button type="primary" @click="onSubmit" v-if="optionModel != 'read'" :loading="submitLoading">确定</el-button>
      <el-button @click="closeDialog()">取消</el-button>
    </span>
  </div>

</template>

<script>
import { AddPlanDetail, EditPlanDetail } from "@/api/budgetManage/planManage";
export default {
  props: {
    planId: {
      type: String,
      default: "",
    },
    typeId: {
      type: [String, Number],
      default: "",
    },
    companyType: {
      type: Number,
      default: 0,
    },
    currentRow: {
      type: Object,
      default: null,
    },
    optionModel: {
      type: String,
      default: "add",
    },
  },
  data() {
    return {
      formData: {
        mileageStr:"",
        isDuty:0,
        mianMileage:1,
        tunnlMileage:null,
        bridgeMileage:null,
        hgrade:null,
        lane:null,
        baseFund:null,
        adCoefficient:1,
        yearCoefficient:1,
        greenCoefficient:1,
        sumFund:null,
        remark:"",
      },
      rules: {
        mileageStr: [
          { required: true, message: '请输入里程桩号', trigger: 'blur' },
        ],
        sumFund: [
          { required: true, message: '请输入总费用', trigger: 'blur' },
        ],
        isDuty:[
          { required: true, message: '请选择是否缺陷责任期内', trigger: 'blur' },
        ],
        mianMileage:[
          { required: true, message: '请输入主线里程', trigger: 'blur' },
        ],
        tunnlMileage:[
          { required: true, message: '请输入隧道里程', trigger: 'blur' },
        ],
        bridgeMileage:[
          { required: true, message: '请输入桥梁里程', trigger: 'blur' },
        ],
        hgrade:[
          { required: true, message: '请选择公路等级', trigger: 'blur' },
        ],
        adCoefficient:[
          { required: true, message: '请输入通行费调整系数', trigger: 'blur' },
        ],
        yearCoefficient:[
          { required: true, message: '请输入通行年限调整系数', trigger: 'blur' },
        ],
        greenCoefficient:[
          { required: true, message: '请输入绿化调整系数', trigger: 'blur' },
        ],
      },
      projectNames:{
        11:"公路维护费"
      },
      submitLoading: false
    }
  },
  methods:{
    handleInput(){
    let  routeLength = parseFloat(this.formData.mianMileage) || 0,
      tunnelLength = parseFloat(this.formData.tunnlMileage) || 0,
      bridgeLength = parseFloat(this.formData.bridgeMileage) || 0,
      tollRatio = parseFloat(this.formData.adCoefficient) || 0,
      yearRatio = parseFloat(this.formData.yearCoefficient) || 0,
      greenRatio = parseFloat(this.formData.greenCoefficient) || 0,
      basefund = parseFloat(this.formData.baseFund) || 0;
      this.formData.sumFund = Math.round((routeLength - tunnelLength) * basefund * (1 + tollRatio + yearRatio) + (routeLength - tunnelLength - bridgeLength) * basefund * greenRatio);
    },
    routeGradeChange(){
      this.calcBaseFund()
    },
    laneChange(){
      this.calcBaseFund()
    },
    calcBaseFund(){
      if(this.formData.hgrade==3){ // 二级公路
        this.formData.baseFund = 26000
      }
      if(this.formData.hgrade==2){ // 一级公路
        this.formData.baseFund = 32000
      }
      if(this.formData.hgrade==1){ // 高速公路
        if(this.formData.lane == 2 ){
          this.formData.baseFund = 35000
        }
        if(this.formData.lane == 4 ){
          this.formData.baseFund = 40000
        }
        if(this.formData.lane == 6 ){
          this.formData.baseFund = 45000
        }
        if(this.formData.lane == 8 ){
          this.formData.baseFund = 50000
        }
      }
      this.handleInput();
    },
    onSubmit() {
      console.log("this.planId",this.planId)
      console.log("this.typeId",this.typeId)
      this.$refs["queryForm"].validate(async (valid) => {
          if (valid) {
            this.submitLoading = true
            // 提交接口
            if(this.currentRow){ // 修改
              this.formData.planId = this.planId
              const res = await EditPlanDetail(this.formData)
              this.submitLoading = false
              if(res.code==200){
                this.$modal.msgSuccess('保存成功')
                this.closeDialog()
              }
            }else{ // 新增
              const params = {
                projectName:this.projectNames[this.typeId],
                companyType:this.companyType,
                planId:this.planId,
                typeId:this.typeId,
                ...this.formData,
              }
              const res = await AddPlanDetail(params)
              this.submitLoading = false
              if(res.code==200){
                this.$modal.msgSuccess('保存成功')
                this.closeDialog()
              }
            }
          }
        });
    },
    closeDialog(){
      this.$refs["queryForm"].resetFields();
      this.$emit('closeDialog')
    }
  },
  created(){
    if(this.optionModel == "edit" || this.optionModel == "read"){ // 编辑 查看 状态回显
      this.typeId = this.currentRow.typeId
      this.companyType = this.currentRow.companyType
      this.formData = { ...this.currentRow }
    }
  },
}

</script>


<style scoped lang="scss">
.maindiv{

}

</style>
<style lang="scss" scoped>
@import "@/assets/styles/business.scss";
</style>
