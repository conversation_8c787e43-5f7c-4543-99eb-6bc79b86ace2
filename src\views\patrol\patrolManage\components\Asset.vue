<template>
	<div class="component-container">
		<!-- 查询条件区域 -->
		<div class="query-container">
			<el-row :gutter="24" style="width: 80%">
				<!-- 选择桥梁按钮-->
				<el-col :span="4" :offset="0">
					<el-button size="small" type="primary" @click="handleSelectBridge">选择桥梁</el-button>
				</el-col>
				<!--有无事件-->
				<el-col :span="4" :offset="0">
					<el-select v-model="queryForm.hasEvent" placeholder="有无异常" size="small">
						<el-option label="有异常" value="1" />
						<el-option label="无异常" value="0" />
					</el-select>
				</el-col>
				<!--搜索按钮 导出清单 导出卡片-->
				<el-col :span="12" :offset="0">
					<el-button size="small" type="primary" @click="handleSearch">搜索</el-button>
					<el-button size="small" type="primary" @click="handleExport">导出清单</el-button>
					<el-button size="small" type="primary" @click="handleExport">导出卡片</el-button>
				</el-col>
			</el-row>
		</div>
		<!-- 表格区域 -->
		<div class="table-container">
			<el-table v-adjust-table :data="tableData" style="width: 100%" height="calc(100% - 50px)">
				<el-table-column prop="number" label="序号" width="60" />
				<el-table-column prop="maintenanceSection" label="养护路段" />
				<el-table-column prop="routeCode" label="路线编码" />
				<el-table-column
					:prop="type === 1 ? 'bridgeCode' : 'tunnelCode'"
					:label="type === 1 ? '桥梁编码' : '隧道编码'"
				/>
				<el-table-column
					:prop="type === 1 ? 'bridgeName' : 'tunnelName'"
					:label="type === 1 ? '桥梁名称' : '隧道名称'"
				/>
				<el-table-column
					:prop="type === 1 ? 'bridgeStation' : 'centerStation'"
					:label="type === 1 ? '桥位桩号' : '中心桩号'"
				/>
				<el-table-column prop="managementOffice" label="管理处" />
				<el-table-column prop="maintenanceUnit" label="养护单位" />
				<el-table-column prop="responsiblePerson" label="负责人" />
				<el-table-column prop="recorder" label="记录人" />
				<el-table-column prop="inspectionDate" label="检查日期" />
				<el-table-column prop="inspectionDetails" label="检查明细" />
			</el-table>
			<pagination
				v-show="total > 0"
				:total="total"
				:page.sync="queryForm.pageNum"
				:limit.sync="queryForm.pageSize"
				@pagination="getList"
			/>
		</div>
	</div>
</template>

<script>
export default {
	props: {
		type: {
			type: Number,
			default: 1,
		},
	},
	data() {
		return {
			queryForm: {
				pageNum: 1,
				pageSize: 10,
			},
			tableData: [],
			total: 0,
		}
	},
	methods: {
		handleSearch() {
			this.getList()
		},
		handleExport() {
			console.log('导出')
		},
		handleExportCard() {
			console.log('导出卡片')
		},
		getList() {
			console.log('获取列表')
		},
	},
}
</script>

<style scoped>
.component-container {
	height: 100%;
	display: flex;
	flex-direction: column;
}

.query-container {
	padding-bottom: 20px;
}

.table-container {
	flex: 1;
	overflow: hidden;
}

.el-input,
.el-select {
	width: 100%;
}

.el-button {
	margin-right: 10px;
}

.el-button:last-child {
	margin-right: 0;
}
</style>
