<template>
  <div class="down">
    <div v-show="planId">
      <div class="detail-container">
        <el-table v-adjust-table
          ref="detaildataTable"
          :data="detailDialogData"
          show-summary
          height="132"
          :show-summary="true"
          :summary-method="getSummaries"
          border
        >
          <template v-for="(column, index) in detailDialogColumns">
            <el-table-column
              v-if="column.visible"
              :label="column.label"
              align="center"
              :prop="column.field"
              :width="column.width"
            >
              <template slot-scope="scope">
                <dict-tag
                  v-if="column.dict"
                  :options="dict.type[column.dict]"
                  :value="scope.row[column.field]"
                />
                <template v-else-if="column.slots">
                  <RenderDom
                    :row="scope.row"
                    :index="index"
                    :render="column.render"
                  />
                </template>
                <span v-else>{{ scope.row[column.field] }}</span>
              </template>
            </el-table-column>
          </template>
        </el-table>
      </div>
      <div class="opt-container">
        <el-button
          type="primary"
          @click="addDetail"
          v-if="deepAndNew"
          >新增明细</el-button
        >
      </div>
      <el-table v-adjust-table
        v-if="tableData"
        size="mini"
        style="width: 100%"
        v-loading="loading"
        border
        :data="tableData"
        row-key="id"
        ref="dataTable"
        stripe
        highlight-current-row
      >
        <el-table-column label="序号" align="center" type="index" width="50" />
        <template v-for="(column, index) in columns">
          <el-table-column
            :label="column.label"
            v-if="column.visible"
            align="center"
            :prop="column.field"
          >
            <template slot-scope="scope">
              <dict-tag
                v-if="column.dict"
                :options="dict.type[column.dict]"
                :value="scope.row[column.field]"
              />
              <template v-else-if="column.slots">
                <RenderDom
                  :row="scope.row"
                  :index="index"
                  :render="column.render"
                />
              </template>
              <span v-else>{{ scope.row[column.field] }}</span>
            </template>
          </el-table-column>
        </template>
        <el-table-column
          align="center"
          class-name="small-padding fixed-width"
          fixed="right"
          label="操作"
          width="250"
        >
          <template slot-scope="scope">
            <el-button
              icon="el-icon-edit"
              size="mini"
              type="text"
              @click="handlerEdit(scope.row)"
              v-if="deepAndNew"
              >编辑
            </el-button>
            <el-button
              icon="el-icon-edit"
              size="mini"
              type="text"
              @click="handlerChangeCompany(scope.row)"
              v-if="deepAndNew"
              >切换公司
            </el-button>
            <el-button
              icon="el-icon-edit"
              size="mini"
              type="text"
              @click="handlerRead(scope.row)"
              >查看
            </el-button>
            <el-button
              icon="el-icon-delete"
              size="mini"
              type="text"
              style="color: red"
              v-has-menu-permi="['budget:plan:remove']"
              @click="handlerDelete(scope.row)"
              v-if="deepAndNew"
              >删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 弹窗 -->
    <!-- 新增明细弹窗有5种根据不同项目类型来区分 -->
    <!-- 新增明细1 -->
    <el-dialog
      v-if="detailVisible"
      :visible.sync="detailVisible"
      :width="dialogWidth"
      :title="dialogTitle"
      append-to-body
      destroy-on-close
    >
      <Detail1
        :planId="planId"
        :typeId="typeId"
        :companyType="companyType"
        :optionModel="optionModel"
        :currentRow="currentRow"
        v-if="showDialogFlag(detail1Arr)"
        @closeDialog="closeDialog()"
      ></Detail1>

      <Detail2
        :planId="planId"
        :typeId="typeId"
        :companyType="companyType"
        :optionModel="optionModel"
        :currentRow="currentRow"
        v-if="showDialogFlag(detail2Arr)"
        @closeDialog="closeDialog()"
      ></Detail2>

      <Detail3
        :planId="planId"
        :maiSecId="maiSecId"
        :typeId="typeId"
        :optionModel="optionModel"
        :companyType="companyType"
        :currentRow="currentRow"
        :domainId="domainId"
        v-if="showDialogFlag(detail3Arr)"
        @closeDialog="closeDialog()"

      ></Detail3>

      <Detail4
        :planId="planId"
        :maiSecId="maiSecId"
        :typeId="typeId"
        :optionModel="optionModel"
        :companyType="companyType"
        :currentRow="currentRow"
        v-if="showDialogFlag(detail4Arr)"
        @closeDialog="closeDialog()"
      ></Detail4>

      <Detail5
        :planId="planId"
        :maiSecId="maiSecId"
        :typeId="typeId"
        :optionModel="optionModel"
        :companyType="companyType"
        :currentRow="currentRow"
        v-if="showDialogFlag(detail5Arr)"
        @closeDialog="closeDialog()"
      ></Detail5>

      <Detail6
        :planId="planId"
        :maiSecId="maiSecId"
        :typeId="typeId"
        :optionModel="optionModel"
        :companyType="companyType"
        :currentRow="currentRow"
        v-if="showDialogFlag(detail6Arr)"
        @closeDialog="closeDialog()"
      ></Detail6>

      <Detail7
        :planId="planId"
        :maiSecId="maiSecId"
        :domainId="domainId"
        :typeId="typeId"
        :typeLabel="typeLabel"
        :optionModel="optionModel"
        :companyType="companyType"
        :currentRow="currentRow"
        :treeData="treeData"
        v-if="showDialogFlag(detail7Arr)"
        @closeDialog="closeDialog()"
      ></Detail7>
    </el-dialog>
  </div>
</template>
<script>
import Detail1 from "./detail1.vue";
import Detail2 from "./detail2.vue";
import Detail3 from "./detail3.vue";
import Detail4 from "./detail4.vue";
import Detail5 from "./detail5.vue";
import Detail6 from "./detail6.vue";
import Detail7 from "./detail7.vue";
import {
  GetPlanDetail,
  GetSinglePlanDetailList,
  DelPlanDetail,
  ChangeCompany,
} from "@/api/budgetManage/planManage";
import { formatDateNoTime } from "@/utils/index";
export default {
  components: {
    Detail1,
    Detail2,
    Detail3,
    Detail4,
    Detail5,
    Detail6,
    Detail7,
    RenderDom: {
      functional: true,
      props: {
        row: Object,
        index: Number,
        render: Function,
      },
      render(createElement, ctx) {
        const { row, index } = ctx.props;
        return ctx.props.render(row, index);
      },
    },
  },
  dicts: ["sys_budget_route_grade", "sys_budget_lane"],
  computed: {
    dialogWidth() {
      let width = "400px";
      if (
        this.showDialogFlag(this.detail1Arr) ||
        this.showDialogFlag(this.detail2Arr)
      ) {
        width = "400px";
      }
      if (this.showDialogFlag(this.detail3Arr)) {
        width = "1200px";
      }
      if (
        this.showDialogFlag(this.detail4Arr) ||
        this.showDialogFlag(this.detail5Arr) ||
        this.showDialogFlag(this.detail6Arr)
      ) {
        width = "880px";
      }
      if (this.showDialogFlag(this.detail7Arr)) {
        width = "1700px";
      }
      return width;
    },
    dialogTitle() {
      return this.optionModel == "add"
        ? "新增"
        : this.optionModel == "edit"
        ? "编辑"
        : "查看";
    },
    deepAndNew() {
      return this.isNew === 'true' && this.treeIsDeep
    }
  },
  data() {
    return {
      columns: [],
      tableData: [],
      total: 0,
      reviewFlag: false,
      infoFlag: false,
      loading: false,
      queryId: null,
      planId: "",
      companyType: 0,
      typeId: "",
      detailVisible: false,
      currentRow: null,
      maiSecId: "",
      optionModel: "add", // add edit read 新增  编辑  查看 三种模式
      parentsArr: [
        "",
        "09999",
        "19999",
        "6",
        "9",
        "25",
        "35",
        "1",
        "10",
        "14",
        "16",
        "18",
        "21",
        "23",
        "2",
      ],
      detail1Arr: ["3", "4", "12", "13", "15", "20", "22", "24", "36", "37"],
      detail2Arr: ["5"],
      detail3Arr: ["7", "8"],
      detail4Arr: ["11"],
      detail5Arr: ["17"],
      detail6Arr: ["19"],
      detail7Arr: ["26", "27", "28", "29", "30", "31", "32", "33"],
      typeLabel: "",
      detailDialogData: [],
      detailDialogColumns: [
        { key: 0, width: 200, field: "code", label: `计划编码`, visible: true },
        {
          key: 1,
          width: 100,
          field: "companyType",
          label: `公司类型`,
          visible: true,
          slots: true,
          render: (row, index) => {
            return (
              <span>{row.companyType == 0 ? "集团公司" : "项目公司"}</span>
            );
          },
        },
        {
          key: 2,
          width: 50,
          field: "budgetYear",
          label: `年份`,
          visible: true,
        },
        {
          key: 3,
          width: 150,
          field: "maiSecName",
          label: `路段名称`,
          visible: true,
        },
        {
          key: 4,
          width: 100,
          field: "operatefund",
          label: `运营费用`,
          visible: true,
        },
        {
          key: 5,
          width: 100,
          field: "testfund",
          label: `养护检测费`,
          visible: true,
        },
        {
          key: 6,
          width: 100,
          field: "dailyfund",
          label: `日常养护费`,
          visible: true,
        },
        {
          key: 7,
          width: 100,
          field: "specialfund",
          label: `专项工程费`,
          visible: true,
        },
        {
          key: 8,
          width: 100,
          field: "damagedfund",
          label: `被损被盗`,
          visible: true,
        },
        {
          key: 9,
          width: 100,
          field: "sumFund",
          label: `总费用`,
          visible: true,
        },
        {
          key: 10,
          width: 100,
          field: "createTime",
          label: `编制日期`,
          visible: true,
          slots: true,
          render: (row, index) => {
            return <span>{formatDateNoTime(row.createTime)}</span>;
          },
        },
      ],
      treeIsDeep: false,
      domainId: '',
      treeData: []
    };
  },
  methods: {
    showDialogFlag(detailArr) {
      return detailArr.indexOf(this.typeId) != -1;
    },
    getRandom() {
      return Math.floor(Math.random() * 10000);
    },
    getSummaries(param) {
      const { columns, data } = param;
      const sums = [];
      columns.forEach((column, index) => {
        if (index === 0) {
          sums[index] = "合计";
          return;
        }
        const canCalc = [
          "operatefund",
          "testfund",
          "dailyfund",
          "specialfund",
          "damagedfund",
          "sumFund",
        ];
        if (canCalc.includes(column.property)) {
          const values = data.map((item) => Number(item[column.property]));
          if (!values.every((value) => isNaN(value))) {
            sums[index] = values.reduce((prev, curr) => {
              const value = Number(curr);
              if (!isNaN(value)) {
                return prev + curr;
              } else {
                return prev;
              }
            }, 0);
            sums[index] += " 元";
          } else {
            sums[index] = "N/A";
          }
        }
      });
      return sums;
    },
    //不同分类的明细表不同
    getColumns() {
      this.columns = [];
      if (this.showDialogFlag(this.parentsArr)) {
        // 点击父节点
        this.columns = [
          {
            key: this.getRandom(),
            field: "projectName",
            label: `项目名称`,
            visible: true,
          },
          {
            key: this.getRandom(),
            field: "sumFund",
            label: `总费`,
            visible: true,
          },
          {
            key: this.getRandom(),
            field: "companyType",
            label: `公司类型`,
            visible: true,
            slots: true,
            render: (row) => {
              return (
                <span>{row.companyType == 0 ? "集团公司" : "项目公司"}</span>
              );
            },
          },
          {
            key: this.getRandom(),
            field: "remark",
            label: `备注`,
            visible: true,
          },
        ];
        return;
      }
      if (this.showDialogFlag(this.detail1Arr)) {
        this.columns = [
          {
            key: this.getRandom(),
            field: "mileageStr",
            label: `里程桩号`,
            visible: true,
          },
          {
            key: this.getRandom(),
            field: "sumFund",
            label: `总费`,
            visible: true,
          },
          {
            key: this.getRandom(),
            field: "companyType",
            label: `公司类型`,
            visible: true,
            slots: true,
            render: (row) => {
              return (
                <span>{row.companyType == 0 ? "集团公司" : "项目公司"}</span>
              );
            },
          },
          {
            key: this.getRandom(),
            field: "remark",
            label: `备注`,
            visible: true,
          },
        ];
        return;
      }
      if (this.showDialogFlag(this.detail2Arr)) {
        this.columns = [
          {
            key: this.getRandom(),
            field: "mileageStr",
            label: `里程桩号`,
            visible: true,
          },
          {
            key: this.getRandom(),
            field: "sumFund",
            label: `总费`,
            visible: true,
          },
          {
            key: this.getRandom(),
            field: "companyType",
            label: `公司类型`,
            visible: true,
            slots: true,
            render: (row) => {
              return (
                <span>{row.companyType == 0 ? "集团公司" : "项目公司"}</span>
              );
            },
          },
          {
            key: this.getRandom(),
            field: "remark",
            label: `备注`,
            visible: true,
          },
        ];
        return;
      }
      if (this.showDialogFlag(this.detail3Arr)) {
        // 集团公司 项目公司
        this.columns = [
          {
            key: this.getRandom(),
            field: "projectName",
            label: `项目名称`,
            visible: true,
          },
          {
            key: this.getRandom(),
            field: "sumFund",
            label: `总费`,
            visible: true,
          },
          {
            key: this.getRandom(),
            field: "conNames",
            label: `合同`,
            visible: true,
          },
          {
            key: this.getRandom(),
            field: "projectReason",
            label: `立项理由`,
            visible: true,
          },
          {
            key: this.getRandom(),
            field: "companyType",
            label: `公司类型`,
            visible: true,
            slots: true,
            render: (row) => {
              return (
                <span>{row.companyType == 0 ? "集团公司" : "项目公司"}</span>
              );
            },
          },
          {
            key: this.getRandom(),
            field: "remark",
            label: `备注`,
            visible: true,
          },
        ];
        return;
      }
      if (this.showDialogFlag(this.detail4Arr)) {
        this.columns = [
          {
            key: this.getRandom(),
            field: "mileageStr",
            label: `里程桩号`,
            visible: true,
          },
          {
            key: this.getRandom(),
            field: "mianMileage",
            label: `主线里程`,
            visible: true,
          },
          {
            key: this.getRandom(),
            field: "tunnlMileage",
            label: `隧道里程`,
            visible: true,
          },
          {
            key: this.getRandom(),
            field: "bridgeMileage",
            label: `桥梁里程`,
            visible: true,
          },
          {
            key: this.getRandom(),
            field: "hgrade",
            label: `公路等级`,
            dict: "sys_budget_route_grade",
            visible: true,
          },
          {
            key: this.getRandom(),
            field: "lane",
            label: `车道数`,
            dict: "sys_budget_lane",
            visible: true,
          },
          {
            key: this.getRandom(),
            field: "sumFund",
            label: `总费`,
            visible: true,
          },
          {
            key: this.getRandom(),
            field: "baseFund",
            label: `基本费用`,
            visible: true,
          },
          {
            key: this.getRandom(),
            field: "yearCoefficient",
            label: `通车年限调整系数`,
            visible: true,
          },
          {
            key: this.getRandom(),
            field: "greenCoefficient",
            label: `绿化调整系数`,
            visible: true,
          },
          {
            key: this.getRandom(),
            field: "adCoefficient",
            label: `通行费调整系数`,
            visible: true,
          },
          {
            key: this.getRandom(),
            field: "companyType",
            label: `公司类型`,
            visible: true,
            slots: true,
            render: (row) => {
              return (
                <span>{row.companyType == 0 ? "集团公司" : "项目公司"}</span>
              );
            },
          },
          {
            key: this.getRandom(),
            field: "remark",
            label: `备注`,
            visible: true,
          },
        ];
        return;
      }
      if (this.showDialogFlag(this.detail5Arr)) {
        this.columns = [
          {
            key: this.getRandom(),
            field: "mileageStr",
            label: `里程桩号`,
            visible: true,
          },
          {
            key: this.getRandom(),
            field: "hgrade",
            label: `公路等级`,
            dict: "sys_budget_route_grade",
            visible: true,
          },
          {
            key: this.getRandom(),
            field: "tunnlMileage",
            label: `隧道里程`,
            visible: true,
          },
          {
            key: this.getRandom(),
            field: "sumFund",
            label: `总费`,
            visible: true,
          },
          {
            key: this.getRandom(),
            field: "baseFund",
            label: `基本费用`,
            visible: true,
          },
          {
            key: this.getRandom(),
            field: "yearCoefficient",
            label: `通车年限调整系数`,
            visible: true,
          },
          {
            key: this.getRandom(),
            field: "adCoefficient",
            label: `通行费调整系数`,
            visible: true,
          },
          {
            key: this.getRandom(),
            field: "companyType",
            label: `公司类型`,
            visible: true,
            slots: true,
            render: (row) => {
              return (
                <span>{row.companyType == 0 ? "集团公司" : "项目公司"}</span>
              );
            },
          },
          {
            key: this.getRandom(),
            field: "remark",
            label: `备注`,
            visible: true,
          },
        ];
        return;
      }
      if (this.showDialogFlag(this.detail6Arr)) {
        this.columns = [
          {
            key: this.getRandom(),
            field: "mileageStr",
            label: `里程桩号`,
            visible: true,
          },
          {
            key: this.getRandom(),
            field: "hgrade",
            label: `公路等级`,
            dict: "sys_budget_route_grade",
            visible: true,
          },
          {
            key: this.getRandom(),
            field: "baseFund",
            label: `基本费用`,
            visible: true,
          },
          {
            key: this.getRandom(),
            field: "tunnlMileage",
            label: `隧道里程`,
            visible: true,
          },
          {
            key: this.getRandom(),
            field: "sumFund",
            label: `总费`,
            visible: true,
          },
          {
            key: this.getRandom(),
            field: "dutyYear",
            label: `缺陷责任期后年数`,
            visible: true,
          },
          {
            key: this.getRandom(),
            field: "coefficient",
            label: `机电维修维护费调整系数`,
            visible: true,
          },
          {
            key: this.getRandom(),
            field: "companyType",
            label: `公司类型`,
            visible: true,
            slots: true,
            render: (row) => {
              return (
                <span>{row.companyType == 0 ? "集团公司" : "项目公司"}</span>
              );
            },
          },
          {
            key: this.getRandom(),
            field: "remark",
            label: `备注`,
            visible: true,
          },
        ];
        return;
      }
      if (this.showDialogFlag(this.detail7Arr)) {
        this.columns = [
          {
            key: this.getRandom(),
            field: "mileageStr",
            label: `里程桩号`,
            visible: true,
          },
          {
            key: this.getRandom(),
            field: "sumFund",
            label: `总费`,
            visible: true,
          },
          {
            key: this.getRandom(),
            field: "companyType",
            label: `公司类型`,
            visible: true,
            slots: true,
            render: (row) => {
              return (
                <span>{row.companyType == 0 ? "集团公司" : "项目公司"}</span>
              );
            },
          },
          {
            key: this.getRandom(),
            field: "remark",
            label: `备注`,
            visible: true,
          },
        ];
        return;
      }
    },
    async getPlanId(id) {
      console.log("aaaa" + id);
      if (this.planId === id) return;
      this.planId = id;
      // 选择单位之后具体分项的参数初始化
      this.companyType = 0;
      this.typeId = "";
      //查询表格
      this.queryDetailData();
    },
    async queryDetailData() {
      this.loading = true;
      this.getColumns();
      const res = await GetPlanDetail({
        planId: this.planId,
        companyType: this.companyType,
        typeId: this.typeId,
      });
      this.tableData = res.data;
      this.handleFundView();
      this.loading = false;
    },
    async handleFundView() {
      const res = await GetSinglePlanDetailList(this.planId);
      this.detailDialogData = res.data || [];
    },
    addDetail() {
      this.currentRow = null;
      this.optionModel = "add";
      this.detailVisible = true;
    },
    closeDialog() {
      this.detailVisible = false;
      this.queryDetailData()
    },
    handlerEdit(row) {
      this.currentRow = row;
      this.companyType = row.companyType;
      this.optionModel = "edit";
      this.typeId = row.typeId;
      this.detailVisible = true;
    },
    handlerRead(row) {
      console.log(row);
      this.currentRow = row;
      this.companyType = row.companyType;
      this.optionModel = "read";
      this.typeId = row.typeId;
      this.detailVisible = true;
    },
    handlerChangeCompany(row) {
      this.$modal
        .confirm("是否确认切换公司？")
        .then(function () {
          const companyType = row.companyType == 0 ? 1 : 0;
          return ChangeCompany({ id: row.id, type: companyType });
        })
        .then(() => {
          this.queryDetailData();
          this.$modal.msgSuccess("切换公司成功");
        })
        .catch(() => {});
    },
    handlerDelete(row) {
      this.$modal
        .confirm("是否确认删除？")
        .then(function () {
          return DelPlanDetail(row.id);
        })
        .then(() => {
          this.queryDetailData();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => {});
    },
    queryUp(data) {
      this.maiSecId = data.maintenanceSectionId;
    },
    async passQueryParams(data) {
      this.typeLabel = data.label;
      this.companyType = data.companyType;
      this.typeId = String(data.typeId);
      this.detailVisible = false;
      this.treeIsDeep = data.treeIsDeep
      //查询表格
      await this.queryDetailData();
    },
    getDomainId(data) {
      this.domainId = data
    },
    getTreeData(data) {
      this.treeData = data.treeData
    }
  },
  created() {
    window.$Bus.$on("getPlanId", this.getPlanId);
    window.$Bus.$on("passQueryParams", this.passQueryParams);
    window.$Bus.$on("queryUp", this.queryUp);
    window.$Bus.$on("domainId", this.getDomainId);
    window.$Bus.$on("rightTreeData", this.getTreeData);
    this.isNew = this.$route.query.isnew
  },
  updated() {
    this.$nextTick(() => {
      this.$refs["dataTable"].doLayout();
      this.$refs["detaildataTable"].doLayout();
    });
  },
  beforeDestroy() {
    window.$Bus.$off("getPlanId", this.getPlanId);
    window.$Bus.$off("passQueryParams", this.passQueryParams);
    window.$Bus.$off("queryUp", this.queryUp);
    window.$Bus.$off("domainId", this.getDomainId);
  },
};
</script>
<style scoped lang="scss">
.down {
  padding: 10px;
  background-color: #fff;
  height: 46rem;
  overflow: auto;
  .opt-container {
    margin-bottom: 5px;
  }
}
.detail-container {
  padding-bottom: 10px;
}
</style>
<style lang="scss" scoped>
@import "@/assets/styles/business.scss";
</style>
