<template>
  <div class="app-container">
    <el-row :gutter="20">
      <!-- 筛选区开始 -->
      <el-col :span="24" :xs="24">
        <el-row>
          <el-col :span="24">
            <el-form ref="queryForm" :inline="true" :model="queryParams" label-width="68px" size="small">
              <el-form-item label="" prop="name">
                <el-input v-model="queryParams.name" clearable placeholder="请输入名称"/>
              </el-form-item>
              <el-form-item>
                <el-button icon="el-icon-search" size="mini" type="primary" @click="handleQuery">搜索</el-button>
                <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
              </el-form-item>
            </el-form>
          </el-col>
        </el-row>
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button
              icon="el-icon-plus"
              size="mini"
              type="primary"
              @click="handleAdd"
            >新增
            </el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button
              icon="el-icon-delete"
              size="mini"
              type="danger"
              @click="handleDelete"
            >删除
            </el-button>
          </el-col>
        </el-row>
        <!-- 筛选区结束 -->
        <!-- 数据表格开始 -->
        <div class="tableDiv">
          <el-table v-adjust-table v-loading="loading" :data="dataList"
                    :height="'calc(100vh - 260px)'" border size="mini"
                    @selection-change="handleSelectionChange"
                    style="width: 100%">
            <el-table-column align="center" fixed="left" type="selection" width="55"></el-table-column>
            <el-table-column align="center" fixed label="序号" type="index" width="100"></el-table-column>
            <template v-for="(column,index) in columns">
              <el-table-column v-if="column.visible"
                               :label="column.label"
                               :prop="column.field"
                               :width="column.width"
                               align="center"
                               show-overflow-tooltip>
                <template slot-scope="scope">
                  <dict-tag v-if="column.dict" :options="dict.type[column.dict]" :value="scope.row[column.field]"/>
                  <template v-else-if="column.slots">
                    <RenderDom :index="index" :render="column.render" :row="scope.row"/>
                  </template>
                  <span v-else-if="column.isTime">{{ parseTime(scope.row[column.field], '{y}-{m}-{d}') }}</span>
                  <span v-else>{{ scope.row[column.field] }}</span>
                </template>
              </el-table-column>
            </template>
            <el-table-column align="center" class-name="small-padding fixed-width" fixed="right" label="操作"
                             width="150">
              <template slot-scope="scope">
                <el-button icon="el-icon-edit" size="mini" type="text"
                           @click="handleEdit(scope.row)">编辑
                </el-button>
              </template>
            </el-table-column>
          </el-table>
          <pagination
            v-show="total > 0"
            :limit.sync="queryParams.pageSize"
            :page.sync="queryParams.pageNum"
            :total="total"
            @pagination="handleQuery"
          />
        </div>
        <!-- 数据表格结束 -->
      </el-col>
    </el-row>
    <el-dialog :title="dialogTitle" :visible.sync="dialogVisible" append-to-body destroy-on-close width="50%">
      <el-form ref="elForm" :model="formData" :rules="rules" label-width="140px" size="medium">
        <el-form-item label="采样方案" prop="sampleProjectId">
          <a-select v-model="formData.sampleProjectId" :api-method="listSampleProjects" :params="selectParams" />
        </el-form-item>

        <el-form-item label="编码" prop="code">
          <el-input v-model="formData.code" placeholder="请输入编码" clearable />
        </el-form-item>

        <el-form-item label="名称" prop="name">
          <el-input v-model="formData.name" placeholder="请输入名称" clearable />
        </el-form-item>

        <el-form-item label="单位" prop="unit">
          <el-input v-model="formData.unit" placeholder="请输入单位" clearable />
        </el-form-item>

        <el-form-item label="小数位数" prop="numericScale">
          <el-input-number
            v-model="formData.numericScale"
            :min="0"
            :max="10"
            controls-position="right"
            placeholder="请输入小数位数"
          />
        </el-form-item>

        <el-form-item label="默认显示" prop="defaultShow">
          <el-switch
            v-model="formData.defaultShow"
            :active-value="true"
            :inactive-value="false"
            active-text="是"
            inactive-text="否"
          />
        </el-form-item>

        <el-form-item label="显示顺序" prop="showOrders">
          <el-input-number
            v-model="formData.showOrders"
            :min="0"
            controls-position="right"
            placeholder="请输入显示顺序"
          />
        </el-form-item>

        <el-form-item label="存储类型" prop="storageType">
          <dict-select v-model="formData.storageType" type="monitoring_storage_type" style="width: 100%;"/>
        </el-form-item>

        <el-form-item label="默认采样类型" prop="defaultSampleTypeName">
          <el-input
            v-model="formData.defaultSampleTypeName"
            placeholder="请输入默认采样类型名称"
            clearable
          />
        </el-form-item>

        <el-form-item label="排序" prop="orders">
          <el-input-number
            v-model="formData.orders"
            :min="0"
            controls-position="right"
            placeholder="请输入排序值"
          />
        </el-form-item>

        <el-form-item label="说明" prop="description">
          <el-input
            v-model="formData.description"
            type="textarea"
            :rows="3"
            placeholder="请输入说明"
          />
        </el-form-item>
      </el-form>
      <div style="text-align: right">
        <el-button @click="closeDialog">取消</el-button>
        <el-button type="primary" @click="handleConfirm">确定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {addValueType, deleteValueType, updateValueType, listValueTypes} from '@/api/jgjc/baseInfo/monitoringValueType';
import ASelect from "@/views/jgjc/component/aSelect/index.vue";
import {listSampleProjects} from "@/api/jgjc/baseInfo/samplingPlan";

export default {
  name: "EquipmentModel",
  components: {ASelect},
  dicts: ['monitoring_storage_type'],
  data() {
    return {
      // 遮罩层
      loading: false,
      // 总条数
      total: 0,
      dataList: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        title: ''
      },
      // 列信息
      columns: [
        { key: 1, field: 'sampleProjectName', label: '采样方案', visible: true },
        { key: 1, field: 'code', label: '编码', visible: true },
        { key: 2, field: 'name', label: '名称', visible: true },
        { key: 3, field: 'unit', label: '单位', visible: true },
        { key: 6, field: 'numericScale', label: '小数位数', visible: true },
        { key: 4, field: 'defaultShow', label: '默认显示', visible: true },
        { key: 5, field: 'showOrders', label: '显示顺序', visible: true },
        { key: 7, field: 'storageType', label: '存储类型', visible: true, dict: 'monitoring_storage_type' },
        { key: 8, field: 'defaultSampleTypeName', label: '默认采样类型名称', visible: true },
        { key: 9, field: 'orders', label: '排序', visible: true },
        { key: 10, field: 'description', label: '说明', visible: true },
      ],
      // 对话框标题
      dialogTitle: '',
      dialogVisible: false,
      formData: {
        name: '',
        title: '',
        orders: 0
      },
      selectParams: {
        pageNum: 1,
        pageSize: 100,
      },
      ids: [],
      rules:  {
        sampleProjectId: [
          { required: true, message: '请输入采样方案ID', trigger: 'blur' }
        ],
        code: [
          { required: true, message: '请输入编码', trigger: 'blur' }
        ],
        name: [
          { required: true, message: '请输入名称', trigger: 'blur' }
        ],
        unit: [
          { required: true, message: '请输入单位', trigger: 'blur' }
        ],
        numericScale: [
          { required: true, message: '请输入小数位数', trigger: 'blur' },
          { type: 'number', message: '小数位数必须为数字值' }
        ],
        defaultShow: [
          { required: true, message: '请选择默认显示选项', trigger: 'change' }
        ],
        showOrders: [
          { required: true, message: '请输入显示顺序', trigger: 'blur' },
          { type: 'number', message: '显示顺序必须为数字值' }
        ],
        storageType: [
          { required: true, message: '请输入存储类型', trigger: 'blur' }
        ],
        defaultSampleTypeName: [
          { required: true, message: '请输入默认采样类型名称', trigger: 'blur' }
        ],
        orders: [
          { type: 'number', message: '排序必须为数字值' }
        ]
      }
    };
  },
  created() {
    this.handleQuery();
  },
  methods: {
    listSampleProjects,
    handleQuery() {
      this.loading = true;
      listValueTypes(this.queryParams).then(res => {
        this.dataList = res.rows;
        this.total = res.total;
        this.loading = false;
      }).catch(err => {
        this.loading = false;
        console.error(err);
      });
    },
    resetQuery() {
      this.queryParams = {
        pageNum: 1,
        pageSize: 10,
        title: ''
      };
      this.handleQuery();
    },
    handleAdd() {
      this.dialogTitle = '新增';
      this.formData = {
        name: '',
        title: '',
        orders: 0
      };
      this.dialogVisible = true;
    },
    handleEdit(row) {
      this.dialogTitle = '编辑';
      this.formData = {...row};
      this.dialogVisible = true;
    },
    handleConfirm() {
      this.$refs.elForm.validate(valid => {
        if (valid) {
          this.loading = true;
          if (this.formData.id) {
            updateValueType(this.formData).then(res => {
              if (res.code === 200) {
                this.$message.success('编辑成功');
                this.handleQuery();
              } else {
                this.$message.error(res.msg);
              }
              this.loading = false;
              this.closeDialog();
            }).catch(err => {
              this.loading = false;
              console.error(err);
            });
          } else {
            addValueType(this.formData).then(res => {
              if (res.code === 200) {
                this.$message.success('新增成功');
                this.handleQuery();
              } else {
                this.$message.error(res.msg);
              }
              this.loading = false;
              this.closeDialog();
            }).catch(err => {
              this.loading = false;
              console.error(err);
            });
          }
        }
      });
    },
    closeDialog() {
      this.dialogVisible = false;
      this.$refs.elForm.resetFields();
    },
    handleSelectionChange(e) {
      this.ids = e.map(item => item.id);
    },
    handleDelete() {
      if (this.ids.length <= 0) {
        this.$message.warning('请勾选需要删除的数据');
        return
      }
      this.$confirm('是否确认删除选中的数据？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.loading = true;
        deleteValueType({ids: this.ids}).then(res => {
          if (res.code === 200) {
            this.$message.success('删除成功');
            this.handleQuery();
          } else {
            this.$message.error(res.msg);
          }
          this.loading = false;
        }).catch(err => {
          this.loading = false;
          console.error(err);
        });
      })
    },
  }
};
</script>

<style scoped>
.tableDiv {
  margin-top: 20px;
}
</style>
<style lang="scss" scoped>
@import "@/assets/styles/business.scss";
</style>
