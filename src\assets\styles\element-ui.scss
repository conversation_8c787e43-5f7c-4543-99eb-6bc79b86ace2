// cover some element-ui styles

.el-breadcrumb__inner,
.el-breadcrumb__inner a {
  font-weight: 400 !important;
}

.el-upload {
  input[type="file"] {
    display: none !important;
  }
}

.el-upload__input {
  display: none;
}

.cell {
  .el-tag {
    margin-right: 0px;
  }
}

.small-padding {
  .cell {
    padding-left: 5px;
    padding-right: 5px;
  }
}

.fixed-width {
  .el-button--mini {
    padding: 7px 10px;
    width: 60px;
  }
}

.status-col {
  .cell {
    padding: 0 10px;
    text-align: center;

    .el-tag {
      margin-right: 0px;
    }
  }
}

// to fixed https://github.com/ElemeFE/element/issues/2461
.el-dialog {
  transform: none;
  left: 0;
  position: relative;
  margin: 0 auto;
}

// refine element ui upload
.upload-container {
  .el-upload {
    width: 100%;

    .el-upload-dragger {
      width: 100%;
      height: 200px;
    }
  }
}

// dropdown
.el-dropdown-menu {
  a {
    display: block
  }
}

// fix date-picker ui bug in filter-item
.el-range-editor.el-input__inner {
  display: inline-flex !important;
}

// to fix el-date-picker css style
.el-range-separator {
  box-sizing: content-box;
}

$trheight: 36px;
:root {
  --my-element-top: 36px;
}
// 全局表格样式
.el-table {
  .el-table__row {
    height: $trheight !important;
    line-height: $trheight !important;
  }

  .el-table__body-wrapper::-webkit-scrollbar {
    height:10px; // 纵向滚动条 必写
  }
  // 原来滚动条宽度为6px，现在修改为10px，中间差4px，会导致滚动时会出现表格错位问题，所以用padding-bottom补上
  .el-table__fixed-body-wrapper{
    padding-bottom: 4px;
    top: var(--my-element-top) !important; // 防止列表项显示隐藏出现的值重置为默认值，导致表格错位
  }

  .el-table__fixed {
    height:auto !important;
    bottom:10px !important;
  }

  .el-table__fixed-right {
    height:auto !important;
    bottom:10px !important;
}

.el-table__body-wrapper.is-scrolling-none~.el-table__fixed-right,
	.el-table__body-wrapper.is-scrolling-none~.el-table__fixed {
    bottom:0px !important
	}



  .el-table__body .el-table__row.hover-row td {
    background-color: #b7daff !important;
  }
  .el-table__cell {
    padding: 0;
    color: #333333;
  }
  .el-table__header-wrapper tr {
    height: 36px !important;
    color: #212529 !important;
    font-size: 14px;
  }
  .el-table__header-wrapper tr th {
    background-color: #F2F3F5;
    height: 36px !important;
    color: #212529 !important;
    font-size: 14px;
  }
  .el-table__fixed-header-wrapper th {
    background-color: #F2F3F5 !important;
    height: 36px !important;
    color: #212529 !important;
    font-size: 14px;
  }
}

// 输入框相关样式
.el-range-input {
  color: #1d2129;
}
.el-range-separator {
  color: #666666 !important;
}
.el-range-editor .el-range-input::-webkit-input-placeholder {
  color: #666666;
}
.el-tag {
  background: #f2f3f5;
  border-radius: 4px 4px 4px 4px;
  color: #1d2129 !important;
}
.el-range-separator {
  color: #c9cdd4;
}
.el-input__inner::-webkit-input-placeholder {
  font-weight: 400;
  color: #666666;
}
.el-input__inner {
  font-weight: 400;
  color: #1d2129;
  // height: 32px;
}
.el-form-item__label {
  font-weight: 400;
  color: #333333;
}

/**
* 修改菜单样式
*/
.el-menu {
  .el-menu-item {
    height: 40px !important;
    line-height: 40px !important;
  }
  .el-submenu {
    .el-submenu__title {
      height: 40px !important;
      line-height: 40px !important;
    }
  }
}
