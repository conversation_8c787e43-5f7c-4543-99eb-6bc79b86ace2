<template>
  <div class="bridge-special">
    <div class="special-c">
      <MapView :padding="[50, 50, 50, -250]" ref="mpRef"></MapView>
    </div>
    <section class="special-l">
      <CockpitCard title="运营桥梁信息" :w="isBig ? '10vw' : '20vw'" h="calc(40vh - 5px)" :class="isBig ? 'mb-2' : 'mb-3'"
        :isDtl="false">
        <div class="bridge-info">
          <span>桥梁总数量</span>
          <span v-for="(item, index) in bridgeNum" :key="index" class="info-num">
            {{ item }}
          </span>
          <span>座</span>
        </div>
        <Echarts :option="btOption" v-if="btOption" height="16vh" key="btKey" />
        <div class="divider"></div>
        <Echarts :option="sOption" v-if="sOption" height="16vh" key="sKey" />
      </CockpitCard>
      <CockpitCard title="技术状况等级" :w="isBig ? '10vw' : '20vw'" h="calc(19vh - 5px)" :class="isBig ? 'mb-2' : 'mb-3'"
        :isDtl="false">
        <Echarts :option="lvOption" v-if="lvOption" height="18vh" key="lKey" />
      </CockpitCard>
      <CockpitCard title="通车桥梁趋势" :w="isBig ? '10vw' : '20vw'" h="calc(19vh - 5px)" :isDtl="false">
        <Echarts :option="tcOption" v-if="tcOption" height="18vh" key="tcKey" />
      </CockpitCard>
    </section>
    <section class="special-r">
      <div class="info">
        <div>
          <CockpitCard title="管理处桥梁分布情况" :w="isBig ? '9vw' : '18vw'" :isDtl="false"
            :h="isBig ? 'calc(57vh - 12px)' : 'calc(57vh - 6px)'" :class="isBig ? 'mb-2' : 'mb-3'">
            <Tables :columns="columns" :data="tableData" :isScroll="false"></Tables>
          </CockpitCard>
          <CockpitCard title="桥梁养护经费" :w="isBig ? '9vw' : '18vw'" h="calc(26vh - 5px)" :class="isBig ? 'mb-2' : 'mb-3'"
            :isDtl="false">
            <Echarts :option="edOption" v-if="edOption" height="25vh" key="edKey" />
          </CockpitCard>
        </div>
        <div class="ml-2">
          <CockpitCard title="健康监测" :w="isBig ? '9vw' : '18vw'" h="calc(26vh - 5px)" :class="isBig ? 'mb-2' : 'mb-3'"
            :isDtl="false">
            <Echarts :option="jkOption" v-if="jkOption" height="25vh" key="jkKey" />
          </CockpitCard>
          <CockpitCard title="长大桥目录" :w="isBig ? '9vw' : '18vw'" h="calc(26vh - 5px)" :class="isBig ? 'mb-2' : 'mb-3'"
            :isDtl="false">
            <Echarts :option="mlOption" v-if="mlOption" height="25vh" key="jkKey" />
          </CockpitCard>
          <CockpitCard title="独柱墩" :w="isBig ? '9vw' : '18vw'" h="calc(26vh - 5px)" :isDtl="false">
            <Echarts :option="dzOption" v-if="dzOption" height="25vh" key="jkKey" />
          </CockpitCard>
        </div>
      </div>
    </section>
  </div>
</template>

<script>
import * as echarts from 'echarts'
import { isBigScreen } from '../../util/utils';
import CockpitCard from '../cockpitCard.vue';
import Echarts from '../echarts/echarts.vue';
import Tables from "../tables.vue";
import MapView from '../mapView.vue';

export default {
  name: 'Special',
  components: {
    CockpitCard,
    Echarts,
    Tables,
    MapView,
  },
  data() {
    return {
      map: null,
      isBig: isBigScreen(),
      bridgeNum: ['1', '3', '8', '5', '5'],
      btOption: null,
      sOption: null,
      lvOption: null,
      tcOption: null, // 通车桥梁趋势
      columns: [
        {
          label: "序号",
          prop: "index",
          width: 40,
        },
        {
          label: "管理处",
          prop: "name",
        },
        {
          label: "特大桥",
          prop: "outsize",
          width: 50,
        },
        {
          label: "大桥",
          prop: "big",
          width: 40,
        },
        {
          label: "中桥",
          prop: "middle",
          width: 40,
        },
        {
          label: "小桥",
          prop: "sml",
          width: 40,
        },
      ],
      tableData: [
        {
          index: "index",
          name: "保山管理处",
          outsize: 21,
          big: 1025,
          middle: 448,
          sml: 98
        },
        {
          index: "index",
          name: "大理管理处",
          outsize: 21,
          big: 1025,
          middle: 448,
          sml: 98
        },
        {
          index: "index",
          name: "红河管理处",
          outsize: 21,
          big: 1025,
          middle: 448,
          sml: 98
        },
        {
          index: "index",
          name: "昆明东管理处",
          outsize: 21,
          big: 1025,
          middle: 448,
          sml: 98
        },
        {
          index: "index",
          name: "昆明西管理处",
          outsize: 21,
          big: 1025,
          middle: 448,
          sml: 98
        },
        {
          index: "index",
          name: "丽江管理处",
          outsize: 21,
          big: 1025,
          middle: 448,
          sml: 98
        },
        {
          index: "index",
          name: "临沧管理处",
          outsize: 21,
          big: 1025,
          middle: 448,
          sml: 98
        },
        {
          index: "index",
          name: "普洱管理处",
          outsize: 21,
          big: 1025,
          middle: 448,
          sml: 98
        },
        {
          index: "index",
          name: "曲靖管理处",
          outsize: 21,
          big: 1025,
          middle: 448,
          sml: 98
        },
        {
          index: "index",
          name: "文山管理处",
          outsize: 21,
          big: 1025,
          middle: 448,
          sml: 98
        },
        {
          index: "index",
          name: "西双版纳管理处",
          outsize: 21,
          big: 1025,
          middle: 448,
          sml: 98
        },
        {
          index: "index",
          name: "昭通管理处",
          outsize: 21,
          big: 1025,
          middle: 448,
          sml: 98
        },
      ],
      edOption: null,
      jkOption: null,
      mlOption: null,
      dzOption: null,
      obj: {
        "deptIds": [],
        "id": "1815935561620328449",
        icon: require('@/assets/cockpit/bridge.png'),
        "paramsDTO": {
          "precisionParams": {
            "base.span_classify_type": "1"
          }
        }
      }
    }
  },
  mounted() {
    this.$nextTick(() => {
      this.initMap();
    })
    // 初始化桥梁类型图表
    this.initBtECharts();
    this.initSECharts();
    // 技术等级
    this.initLvECharts();
    // 通车桥梁趋势-折线图
    this.initTcEcharts();
    // 桥梁养护经费-柱状图
    this.initEdEcharts();
    this.initBarsCharts();
  },
  destroyed() {
    if(this.$refs.mpRef) {
      this.$refs.mpRef.removeVector('桥梁');
    }
  },
  methods: {
    initMap() {
      this.$nextTick(() => {
        this.$refs.mpRef.setVector('桥梁', this.obj)
      })
    },
    // 桥梁类型-饼图
    initBtECharts() {
      var outr = this.isBig ? 135 : 63;
      var inr = this.isBig ? 125 : 60;
      var size = 13;
      var numberdata = [252, 7748, 4544, 1315];
      var titledata = ['特大桥', '大桥', '中桥', '小桥'];
      let color = ['#01FBEF', '#00AEFF', '#0154FB', '#518BFF'];
      let title = '桥梁类型';
      this.btOption = this.initPieCharts(outr, inr, size, numberdata, titledata, color, title);
    },
    // 主桥上部结构形式-饼图
    initSECharts() {
      var outr = this.isBig ? 135 : 63;
      var inr = this.isBig ? 125 : 60;
      var size = 13;
      var numberdata = [6055, 7775, 4544, 6055, 7775, 4544];
      var titledata = ['空心板梁', 'T梁', '箱形梁', '连续钢构', '悬索桥', '斜拉桥'];
      let color = ['#7C8EFF', '#DD47FF', '#8B34FF', '#FFE000', '#FFA800', '#FF5B00'];
      let title = '主桥上部\n\n\n结构形式';
      this.sOption = this.initPieCharts(outr, inr, size, numberdata, titledata, color, title);
    },
    initLvECharts() {
      var outr = this.isBig ? 135 : 63;
      var inr = this.isBig ? 125 : 60;
      var size = 13;
      var numberdata = [6055, 7775, 4544, 6055, 7775, 4544];
      var titledata = ['1类', '2类', '3类', '4类', '5类', '未评定'];
      let color = ['#FF9803', '#00DFFF', '#008CFF', '#0154FB', '#518BFF', '#9151FF'];
      let title = '最新技术状况';
      this.lvOption = this.initPieCharts(outr, inr, size, numberdata, titledata, color, title);
    },
    // 公共的饼图创建
    initPieCharts(outr = 135, inr = 125, size = 13, numberdata = [], titledata = [], color = [], title = '') {
      var total = 0;
      //计算总和
      for (var i = 0; i < numberdata.length; i++) {
        total += Number(numberdata[i]);
      }
      let placeHolderStyle = {
        normal: {
          label: {
            show: false,
          },
          labelLine: {
            show: false,
          },
          color: 'rgba(0, 0, 0, 0)',
          borderColor: 'rgba(0, 0, 0, 0)',
          borderWidth: 0,
        },
      };

      var data = [];
      for (var i = 0; i < numberdata.length; i++) {
        data.push(
          {
            value: numberdata[i],
            name: titledata[i],
            itemStyle: {
              normal: {
                borderWidth: 5,
                shadowBlur: 10,
                borderColor: color[i],
                shadowColor: color[i],
                color: color[i],
              },
            },
          },
          {
            value: total / 30,
            name: '',
            itemStyle: placeHolderStyle,
          }
        );
      }

      var seriesObj = [
        {
          name: '',
          type: 'pie',
          clockWise: false,
          startAngle: '90',
          center: ['30%', '50%'], //此处调整圆环的位置
          radius: [outr, inr], //此处可以调整圆环的大小
          hoverAnimation: false,
          itemStyle: {
            normal: {
              label: {
                show: false,
              },
              labelLine: {
                show: false,
              },
            },
          },
          data: data,
          animationType: 'scale',
          animationEasing: 'elasticOut',
          animationDelay: function (idx) {
            return idx * 50;
          },
        },
        {
          name: '',
          type: 'pie',
          center: ['30%', '50%'],
          radius: ['49%', '49%'],
          itemStyle: {
            color: 'transparant',
          },
          startAngle: '90',
          data: [
            {
              value: total,
              name: '',
              label: {
                normal: {
                  show: true,
                  formatter: '{b| ' + title + '}',
                  rich: {
                    b: {
                      color: 'rgb(255,255,255)',
                      fontSize: this.isBig ? size * 2 : size,
                      lineHeight: 5,
                    },
                  },
                  textStyle: {
                    fontSize: 28,
                    fontWeight: 'bold',
                  },
                  position: 'center',
                },
              },
            },
          ],
        },
      ];

      let option = {
        backgroundColor: 'rgba(0,0,0,0)',
        tooltip: {
          show: false,
        },
        legend: {
          show: true,
          right: this.isBig ? '15%' : '5%',
          y: 'center',
          icon: "circle",
          itemWidth: 3, // 设置宽度
          itemHeight: 3, // 设置高度
          itemGap: 0,
          formatter: (name) => {
            let d = data.filter(v => v.name == name);
            let num = d[0].value;
            return " {title|" + name + "：}" + "{num|" + num + "} " + "{title|座}";
          },
          textStyle: {
            color: '#fff',
            fontSize: size,
            rich: {
              title: {
                color: 'rgba(255,255,255,0.8)',
                fontSize: this.isBig ? size + 4 : size - 1,
                padding: [3, 0],
              },
              num: {
                color: '#fff',
                fontSize: this.isBig ? size + 4 : size,
              }
            }
          },
          itemWidth: 10,
          itemHeight: 10,
          itemGap: 10,
        },
        toolbox: {
          show: false,
        },
        series: seriesObj,
      };
      return option;
    },
    initBarsCharts() {
      let xData = ["保山", "大理", "昆明东", "昆明西", "丽江", "临沧", "普洱", "曲靖", "文山", "西双版纳", "昭通", "红河"];
      let data = [2.5, 3.2, 4.8, 2, 1.5, 1, 1.2, 1.5, 2.2, 3, 1.5, 1.5];
      let jkSColor = '#00B9FE';
      let jkEColor = 'rgba(0,185,254,0.1)';
      this.jkOption = this.initBarCharts(xData, data, jkSColor, jkEColor);

      let mlSColor = '#76FF30';
      let mlEColor = 'rgba(118,255,48,0.1)';
      this.mlOption = this.initBarCharts(xData, data, mlSColor, mlEColor, ['长大桥']);

      let dzSColor = '#FEB247';
      let dzEColor = 'rgba(254,178,71,0.1)';
      this.dzOption = this.initBarCharts(xData, data, dzSColor, dzEColor);
    },
    initBarCharts(xData = [], data = [], startColor = '', endColor = '', legend = []) {

      let option = {
        backgroundColor: "rgba(0,0,0,0)",
        grid: {
          left: "3%",
          right: "4%",
          top: "15%",
          bottom: "1%",
          containLabel: true,
        },
        legend: {
          show: legend.length > 0 ? true : false,
          data: legend,
          x: 'center',
          y: '2%',
          itemWidth: 10,
          itemHeight: 10,
          itemGap: 10,
          textStyle: {
            color: '#ffffff',
          },
        },
        xAxis: {
          data: xData,
          axisLine: {
            lineStyle: {
              color: "rgba(110,112,121,0.5)",
            },
          },
          axisLabel: {
            color: "#fff",
            fontSize: 14,
            rotate: 40,
            interval: false,
          },
        },
        yAxis: {
          name: "",
          nameTextStyle: {
            color: "#999999",
            fontSize: 13,
          },
          axisLabel: {
            color: "#999999",
            fontSize: 13,
            formatter: '{value} k'
          },
          splitLine: {
            show: true,
            lineStyle: {
              color: "rgba(110,112,121,0.5)",
            },
          },
          splitArea: {
            show: false,
          },
          interval: 1,
          max: 5,
        },
        series: [
          {
            name: legend.length ? legend[0] : "",
            type: "bar",
            barWidth: 10,
            itemStyle: {
              normal: {
                color: new echarts.graphic.LinearGradient(
                  0,
                  0,
                  0,
                  1,
                  [
                    {
                      offset: 0,
                      color: startColor,
                    },
                    {
                      offset: 0.8,
                      color: endColor,
                    },
                  ],
                  false
                ),
              },
            },
            data,
          },
        ],
      };
      return option;
    },
    initTcEcharts() {
      var fontColor = '#ffffff';
      this.tcOption = {
        backgroundColor: 'rgba(0,0,0,0)',
        grid: {
          left: '5%',
          right: '5%',
          top: '16%',
          bottom: '3%',
          containLabel: true
        },
        tooltip: {
          show: true,
          trigger: 'item'
        },
        legend: {
          show: false
        },
        xAxis: [
          {
            type: 'category',
            boundaryGap: false,
            axisLabel: {
              color: fontColor,
              rotate: 30,
              interval: false,
            },
            axisLine: {
              show: true,
              lineStyle: {
                color: 'rgba(28,255,188,0.04)'
              }
            },
            axisTick: {
              show: false,
            },
            splitLine: {
              show: false,
              lineStyle: {
                color: '#195384'
              }
            },
            data: ['2016', '2017', '2018', '2019', '2020', '2021', '2022', '2023', '2024']
          }
        ],
        yAxis: [
          {
            type: 'value',
            name: 'k',
            min: 0,
            max: 200,
            axisLabel: {
              formatter: '{value}',
              textStyle: {
                color: '#999999'
              }
            },
            axisLine: {
              lineStyle: {
                color: '#27b4c2'
              }
            },
            axisTick: {
              show: false,
            },
            splitLine: {
              show: true,
              lineStyle: {
                color: 'rgba(110,112,121,0.5)',
              }
            },
            splitArea: {
              show: false,
            }
          },
        ],
        series: [
          {
            name: '',
            type: 'line',
            smooth: true,
            symbol: 'emptyCircle',
            symbolSize: 8,
            emphasis: {
              focus: 'series'
            },
            itemStyle: {
              normal: {
                color: '#1CFFBC',
                lineStyle: {
                  width: 1
                },
                areaStyle: {
                  color: new echarts.graphic.LinearGradient(0, 1, 0, 0, [{
                    offset: 0,
                    color: 'rgba(28,255,188,0.2)'
                  }, {
                    offset: 1,
                    color: 'rgba(28,255,188,0.9)'
                  }]),
                }
              }
            },
            markPoint: {
              itemStyle: {
                normal: {
                  color: 'red'
                }
              }
            },
            data: [120, 132, 101, 134, 90, 130, 110, 182, 191]
          },
        ]
      };
    },
    // 桥梁养护经费
    initEdEcharts() {
      let index = 0;
      this.edOption = {
        tooltip: {
          trigger: "axis",
          axisPointer: {
            // 坐标轴指示器，坐标轴触发有效
            type: "shadow", // 默认为直线，可选为：'line' | 'shadow'
          },
        },
        legend: [
          {
            show: true,
            x: '50%',
            y: '0%',
            textStyle: {
              color: '#ffffff',
            },
            formatter: (name) => {
              index++;
              return name;
            },
            data: ["预算", "完成"],
          },
          {
            show: true,
            x: '50%',
            y: '10%',
            textStyle: {
              color: '#ffffff',
            },
            formatter: (name) => {
              index++;
              return name;
            },
            data: [" 预算", "完成 "],
          },
        ],
        grid: {
          left: "3%",
          right: "4%",
          bottom: "3%",
          containLabel: true,
        },
        xAxis: [
          {
            type: "category",
            data: [
              "保山",
              "大理",
              "昆明东",
              "昆明西",
              "丽江",
              "临沧",
              "普洱",
              "曲靖",
              "文山",
              "西双版纳",
              "昭通",
              "红河",
            ],
            splitLine: {
              show: false
            },
            axisLine: {
              lineStyle: {
                color: "#3f7fb2",
              },
            },
            axisLabel: {
              rotate: 35,
              interval: false,
              textStyle: {
                color: "#fff",
              },
            },
          },
        ],
        yAxis: [
          {
            type: "value",
            name: "",
            nameTextStyle: {
              color: "#999999",
            },
            max: 4000,
            min: 0,
            axisTick: {
              show: false,
            },
            axisLine: {
              lineStyle: {
                color: "rgba(110,112,121,0.5)",
              },
            },
            axisLabel: {
              textStyle: {
                color: "#999999",
              },
              formatter: '{value} k'
            },
            splitLine: {
              show: true,
              lineStyle: {
                color: "rgba(110,112,121,0.5)",
              }
            },
            splitArea: {
              show: false,
            }
          },
        ],
        series: [
          {
            name: "完成",
            type: "bar",
            stack: "日常养护",
            data: [1320, 2332, 1301, 1334, 1390, 2330, 1320, 2390, 1330, 2320, 2330, 2330],
            itemStyle: {
              normal: {
                color: "#76FF31",
              },
            },
          },
          {
            name: "预算",
            type: "bar",
            stack: "日常养护",
            data: [562, 477, 456, 744, 785, 756, 654, 654, 354, 524, 524, 524],
            itemStyle: {
              normal: {
                color: "rgba(118,255,49,0.5)",
              },
            },
          },

          {
            name: "完成 ",
            type: "bar",
            stack: "养护工程",
            data: [213, 456, 456, 45, 455, 312, 211, 456, 645, 787, 456, 645],
            itemStyle: {
              normal: {
                color: "#219BFF",
              },
            },
          },
          {
            name: " 预算",
            type: "bar",
            stack: "养护工程",
            data: [741, 978, 456, 456, 456, 456, 456, 978, 786, 654, 654, 654],
            itemStyle: {
              normal: {
                color: "rgba(33,155,255,0.5)",
              },
            },
          },
        ],
      };
    },
  }
}
</script>

<style lang="scss" scoped>
@import "@/assets/styles/utils.scss";

.bridge-special {
  width: 100%;
  height: 100%;
  position: relative;
  margin-top: vwpx(15px);

  .special-c {
    width: 100%;
    height: 91vh;
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
  }

  .mb-2 {
    margin-bottom: vwpx(20px);
  }

  .mb-3 {
    margin-bottom: vwpx(30px);
  }

  .ml-2 {
    margin-left: vwpx(20px);
  }

  .special-l {
    position: absolute;
    left: vwpx(30px);
    top: 0;

    .bridge-info {
      width: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
      margin: vwpx(20px) 0;

      span {
        font-family: Source Han Sans, Source Han Sans;
        font-weight: 400;
        font-size: vwpx(32px);
        color: #01FBEF;
        text-align: left;
        font-style: normal;
        text-transform: none;
        margin: 0 vwpx(10px);
      }

      .info-num {
        background: rgba(4, 17, 48, 0.4);
        box-shadow: inset 0px 0px 6px 0px #0154FB;
        border: 1px solid #009DFF;
        margin: 0 vwpx(5px);
        padding: vwpx(6px) vwpx(12px);
        color: #FFFFFF;
        font-size: vwpx(34px);
      }
    }

    .divider {
      width: 90%;
      margin: vwpx(20px) 5%;
      border: 1px dotted rgba(156, 189, 255, 0.5);
    }
  }

  .special-r {
    position: absolute;
    right: vwpx(30px);
    top: 0;

    .info {
      display: flex;
    }
  }
}
</style>