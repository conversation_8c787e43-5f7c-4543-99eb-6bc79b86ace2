<template>
  <div class="app-container">
    <el-row :gutter="20">

      <!--筛选区开始-->
      <el-col :span="24" :xs="24">
        <el-row>
          <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" label-width="68px">
            <el-col :span="24">
                  <el-form-item label="" prop="inspectionId">
                    <el-input
                        v-model="queryParams.inspectionId"
                        placeholder="请输入关联的巡查记录ID"
                        clearable
                        prefix-icon="el-icon-user"
                        style="width: 240px"
                        @keyup.enter.native="handleQuery"
                    />
                  </el-form-item>
                  <el-form-item label="" prop="userId">
                    <el-input
                        v-model="queryParams.userId"
                        placeholder="请输入关联的用户ID"
                        clearable
                        prefix-icon="el-icon-user"
                        style="width: 240px"
                        @keyup.enter.native="handleQuery"
                    />
                  </el-form-item>
                  <el-form-item label="" prop="userName">
                    <el-input
                        v-model="queryParams.userName"
                        placeholder="请输入用户姓名"
                        clearable
                        prefix-icon="el-icon-user"
                        style="width: 240px"
                        @keyup.enter.native="handleQuery"
                    />
                  </el-form-item>
                  <el-form-item label="" prop="userSign">
                    <el-input
                        v-model="queryParams.userSign"
                        placeholder="请输入用户签名"
                        clearable
                        prefix-icon="el-icon-user"
                        style="width: 240px"
                        @keyup.enter.native="handleQuery"
                    />
                  </el-form-item>
              <el-form-item>
                <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
                <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
                <el-button v-show="!showSearch" @click="showSearch=true" icon="el-icon-arrow-down" circle></el-button>
                <el-button v-show="showSearch" @click="showSearch=false" icon="el-icon-arrow-up" circle></el-button>
              </el-form-item>
              <!--默认折叠 ,此处仅作为示例-->
              <el-form-item v-show="showSearch" label="创建时间" prop="createTime">
                <el-date-picker
                    v-model="dateRange"
                    style="width: 240px"
                    value-format="yyyy-MM-dd"
                    type="daterange"
                    range-separator="-"
                    start-placeholder="开始日期"
                    end-placeholder="结束日期"
                ></el-date-picker>
              </el-form-item>
            </el-col>
          </el-form>
        </el-row>
        <!--筛选区结束-->


        <!--操作按钮区开始-->
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button
                type="primary"
                icon="el-icon-plus"
                size="mini"
                @click="handleAdd"
                v-hasPermi="['middleData:xquser:add']"
            >新增</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button
                type="success"
                icon="el-icon-edit"
                size="mini"
                :disabled="single"
                @click="handleUpdate"
                v-hasPermi="['middleData:xquser:edit']"
            >修改</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button
                type="danger"
                icon="el-icon-delete"
                size="mini"
                :disabled="multiple"
                @click="handleDelete"
                v-hasPermi="['middleData:xquser:remove']"
            >删除</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button
                type="info"
                plain
                icon="el-icon-upload2"
                size="mini"
                @click="handleImport"
                v-hasPermi="['middleData:xquser:export']"
            >导入</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button
                type="warning"
                icon="el-icon-download"
                size="mini"
                @click="handleExport"
                v-hasPermi="['middleData:xquser:export']"
            >导出</el-button>
          </el-col>
          <right-toolbar :showSearch.sync="showSearch" @queryTable="getList" :columns="columns"></right-toolbar>
        </el-row>
        <!--操作按钮区结束-->

        <!--数据表格开始-->
        <div class="tableDiv">
          <el-table
              ref="table"
              size="mini"
              :height="showSearch ? 'calc(100vh - 320px)' : 'calc(100vh - 260px)'"
              style="width: 100%"
              v-loading="loading"
              border
              :data="xquserList"
              @selection-change="handleSelectionChange"
              :row-style="rowStyle"
              @row-click="handleRowClick"
          >
            <el-table-column type="selection" width="50" align="center" />
            <el-table-column fixed label="序号" type="index" width="50">
              <template v-slot="scope">
                {{ (scope.$index + (queryParams.pageNum - 1) * queryParams.pageSize)+1 }}
              </template>
            </el-table-column>
                <el-table-column label="关联的巡查记录ID" align="center" prop="inspectionId" />
                <el-table-column label="关联的用户ID" align="center" prop="userId" />
                <el-table-column label="用户姓名" align="center" prop="userName" />
                <el-table-column label="用户签名" align="center" prop="userSign" />
            <el-table-column
                label="操作"
                fixed="right"
                align="center"
                width="160"
                class-name="small-padding fixed-width"
            >
              <template slot-scope="scope" v-if="scope.row.userId !== 1">
                <el-button
                    size="mini"
                    type="text"
                    icon="el-icon-edit"
                    @click="handleUpdate(scope.row)"
                    v-hasPermi="['middleData:xquser:edit']"
                >修改</el-button>
                <el-button
                    size="mini"
                    type="text"
                    icon="el-icon-delete"
                    @click="handleDelete(scope.row)"
                    v-hasPermi="['middleData:xquser:remove']"
                >删除</el-button>
              </template>
            </el-table-column>
          </el-table>

          <pagination
              v-show="total>0"
              :total="total"
              :page.sync="queryParams.pageNum"
              :limit.sync="queryParams.pageSize"
              @pagination="getList"
          />
        </div>
        <!--数据表格结束-->
      </el-col>
    </el-row>

    <!-- 添加或修改汛期巡查人员对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="600px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">

                      <el-col :span="12">
                        <el-form-item label="关联的巡查记录ID" prop="inspectionId">
                          <el-input v-model="form.inspectionId" placeholder="请输入关联的巡查记录ID" />
                        </el-form-item>
                      </el-col>


                      <el-col :span="12">
                        <el-form-item label="关联的用户ID" prop="userId">
                          <el-input v-model="form.userId" placeholder="请输入关联的用户ID" />
                        </el-form-item>
                      </el-col>


                      <el-col :span="12">
                        <el-form-item label="用户姓名" prop="userName">
                          <el-input v-model="form.userName" placeholder="请输入用户姓名" />
                        </el-form-item>
                      </el-col>


                      <el-col :span="12">
                        <el-form-item label="用户签名" prop="userSign">
                          <el-input v-model="form.userSign" placeholder="请输入用户签名" />
                        </el-form-item>
                      </el-col>


                      <el-col :span="12">
                        <el-form-item label="删除标志(0:正常;1:删除)" prop="delFlag">
                          <el-input v-model="form.delFlag" placeholder="请输入删除标志(0:正常;1:删除)" />
                        </el-form-item>
                      </el-col>



      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 用户导入对话框 -->
    <el-dialog :title="upload.title" :visible.sync="upload.open" width="400px" append-to-body>
      <el-upload
          ref="upload"
          :limit="1"
          accept=".xlsx, .xls"
          :headers="upload.headers"
          :action="upload.url + '?updateSupport=' + upload.updateSupport"
          :disabled="upload.isUploading"
          :on-progress="handleFileUploadProgress"
          :on-success="handleFileSuccess"
          :auto-upload="false"
          drag
      >
        <i class="el-icon-upload"></i>
        <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
        <div class="el-upload__tip text-center" slot="tip">
          <div class="el-upload__tip" slot="tip">
            <el-checkbox v-model="upload.updateSupport" /> 是否更新已经存在的用户数据
          </div>
          <span>仅允许导入xls、xlsx格式文件。</span>
          <el-link type="primary" :underline="false" style="font-size:12px;vertical-align: baseline;" @click="importTemplate">下载模板</el-link>
        </div>
      </el-upload>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitFileForm">确 定</el-button>
        <el-button @click="upload.open = false">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
  import { listXquser, getXquser, delXquser, addXquser, updateXquser } from "@/api/middleData/xquser";
  import { getToken } from "@/utils/auth";
  import Treeselect from "@riophae/vue-treeselect";
  import "@riophae/vue-treeselect/dist/vue-treeselect.css";

  export default {
    name: "Xquser",
    components: {  },
    data() {
      return {
        // 遮罩层
        loading: true,
        // 选中数组
        ids: [],
        // 非单个禁用
        single: true,
        // 非多个禁用
        multiple: true,
        // 显示搜索条件
        showSearch: false,
        dictType:[],
        // 日期范围
        dateRange: [],
        // 总条数
        total: 0,
        // 汛期巡查人员表格数据
        xquserList: null,
        // 弹出层标题
        title: "",
        // 部门树选项
        deptOptions: undefined,
        // 是否显示弹出层
        open: false,

        // 表单参数
        form: {},
        defaultProps: {
          children: "children",
          label: "label"
        },
        // 用户导入参数
        upload: {
          // 是否显示弹出层（用户导入）
          open: false,
          // 弹出层标题（用户导入）
          title: "",
          // 是否禁用上传
          isUploading: false,
          // 是否更新已经存在的用户数据
          updateSupport: 0,
          // 设置上传的请求头部
          headers: { Authorization: "Bearer " + getToken() },
          // 上传的地址
          url: process.env.VUE_APP_BASE_API + "/system/user/importData"
        },
        // 查询参数
        queryParams: {
          pageNum: 1,
          pageSize: 50,
            inspectionId: null,
            userId: null,
            userName: null,
            userSign: null,
        },
        // 列信息
        columns: [
        { key: 0, label: `关联的巡查记录ID`, visible: true },
        { key: 1, label: `关联的用户ID`, visible: true },
        { key: 2, label: `用户姓名`, visible: true },
        { key: 3, label: `用户签名`, visible: true },
        { key: 4, label: `删除标志(0:正常;1:删除)`, visible: true }
        ],
        // 表单校验
        rules: {
    inspectionId: [
        { required: true, message: "关联的巡查记录ID不能为空", trigger: "blur" }
    ],
    userId: [
        { required: true, message: "关联的用户ID不能为空", trigger: "blur" }
    ],
    userName: [
        { required: true, message: "用户姓名不能为空", trigger: "blur" }
    ],


        }
      };
    },
    watch: {
      // 根据名称筛选部门树
                      },
    created() {
      this.getList();
      // this.getDeptTree();
      // this.getConfigKey("sys.user.initPassword").then(response => {
      //   this.initPassword = response.msg;
      // });
    },
    methods: {
      /** 查询用户列表 */
      getList() {
        this.loading = true;
        // this.queryParams.createTimee = this.dateRange[0]
        // this.queryParams.createTimes = this.dateRange[1]
        listXquser(this.queryParams).then(response => {
          this.xquserList = response.rows;
          this.total = response.total;
          this.loading = false;
        });
      },
      // 取消按钮
      cancel() {
        this.open = false;
        this.reset();
      },
      // 表单重置
      reset() {
        this.form = {
            inspectionId: null,
            userId: null,
            userName: null,
            userSign: null,
            delFlag: null
        };
        this.resetForm("form");
      },
      /** 搜索按钮操作 */
      handleQuery() {
        this.queryParams.pageNum = 1;
        this.getList();
      },
      /** 重置按钮操作 */
      resetQuery() {
        this.dateRange = [];
        this.resetForm("queryForm");
        this.handleQuery();
      },
      // 多选框选中数据
      handleSelectionChange(selection) {
        this.ids = selection.map(item => item.id);
        this.single = selection.length != 1;
        this.multiple = !selection.length;
      },
      // 表格点击勾选
      handleRowClick(row) {
        row.isSelected = !row.isSelected;
        this.$refs.table.toggleRowSelection(row);
      },
      // 勾选高亮
      rowStyle({ row, rowIndex }) {
        if (this.ids.includes(row.id)) {
          return { 'background-color': '#E1F0FF', color: '#333' }
        } else {
          return { 'background-color': '#fff', color: '#333' }
        }
      },
      /** 新增按钮操作 */
      handleAdd() {
        this.reset();
        this.open = true;
        this.title = "添加汛期巡查人员";
      },
      /** 修改按钮操作 */
      handleUpdate(row) {
        this.reset();
        const id = row.id || this.ids;
        getXquser(id).then(response => {
          this.form = response.data;
          this.open = true;
          this.title = "修改汛期巡查人员";
        });

      },
      /** 提交按钮 */
      submitForm: function() {
        this.$refs["form"].validate(valid => {
          if (valid) {
            if (this.form.id != null) {
              updateXquser(this.form).then(response => {
                this.$modal.msgSuccess("修改成功");
                this.open = false;
                this.getList();
              });
            } else {
              addXquser(this.form).then(response => {
                this.$modal.msgSuccess("新增成功");
                this.open = false;
                this.getList();
              });
            }
          }
        });
      },
      /** 删除按钮操作 */
      handleDelete(row) {
        const id = row.id || this.ids;
        this.$modal.confirm('是否确认删除汛期巡查人员编号为"' + id + '"的数据项？').then(function() {
          return delXquser(id);
        }).then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        }).catch(() => {});
      },

      /** 导出按钮操作 */
      handleExport() {

    this.download('middleData/xquser/export', {
      ...this.queryParams
    }, `xquser_${new Date().getTime()}.xlsx`)

      },
      /** 导入按钮操作 */
      handleImport() {
        this.upload.title = "用户导入";
        this.upload.open = true;
      },
      /** 下载模板操作 */
      importTemplate() {
        this.download('system/user/importTemplate', {
        }, `user_template.xlsx`)
      },
      // 文件上传中处理
      handleFileUploadProgress(event, file, fileList) {
        this.upload.isUploading = true;
      },
      // 文件上传成功处理
      handleFileSuccess(response, file, fileList) {
        this.upload.open = false;
        this.upload.isUploading = false;
        this.$refs.upload.clearFiles();
        this.$alert("<div style='overflow: auto;overflow-x: hidden;max-height: 70vh;padding: 10px 20px 0;'>" + response.msg + "</div>", "导入结果", { dangerouslyUseHTMLString: true });
        this.getList();
      },
      // 提交上传文件
      submitFileForm() {
        this.$refs.upload.submit();
      }
  }
  };
</script>
<style>
  .hasTagsView .app-main[data-v-078753dd]{
    background: #f5f7fa;
  }

  .tableDiv{
    background-color: white;
    padding-bottom: 10px;
  }
</style>
