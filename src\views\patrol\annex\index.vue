<template>
  <div class="app-container">
    <el-row :gutter="20">

      <!--筛选区开始-->
      <el-col :span="24" :xs="24">
        <el-row>
          <el-col :span="24" >
            <el-form :model="queryParams" ref="queryForm" size="small" :inline="true"  label-width="68px">

                          <el-form-item label="" prop="conId">
                            <el-input
                                v-model="queryParams.conId"
                                placeholder="请输入合同id"
                                clearable
                                prefix-icon="el-icon-user"
                                style="width: 240px"
                                @keyup.enter.native="handleQuery"
                            />
                          </el-form-item>
                          <el-form-item label="" prop="annexId">
                            <el-input
                                v-model="queryParams.annexId"
                                placeholder="请输入附件id"
                                clearable
                                prefix-icon="el-icon-user"
                                style="width: 240px"
                                @keyup.enter.native="handleQuery"
                            />
                          </el-form-item>
              <el-form-item>
                <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
                <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
                <el-button v-show="!showSearch" @click="showSearch=true" icon="el-icon-arrow-down" circle></el-button>
                <el-button  v-show="showSearch" @click="showSearch=false"  icon="el-icon-arrow-up" circle></el-button>
              </el-form-item>
            </el-form>
            <!--默认折叠-->
          </el-col>

          <!--默认折叠 ,此处仅作为示例-->
          <el-col :span="24" >
            <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">

              <el-form-item label="" prop="status">
                <el-select
                    v-model="queryParams.status"
                    placeholder="示例状态"
                    clearable
                    style="width: 240px"
                >
                  <el-option
                      v-for="dict in dictType"
                      :key="dict.value"
                      :label="dict.label"
                      :value="dict.value"
                  />
                </el-select>
              </el-form-item>
              <el-form-item label="" prop="status">
                <el-select
                    v-model="queryParams.status"
                    placeholder="示例状态"
                    clearable
                    style="width: 240px"
                >
                  <el-option
                      v-for="dict in dictType"
                      :key="dict.value"
                      :label="dict.label"
                      :value="dict.value"
                  />
                </el-select>
              </el-form-item>

            </el-form>
          </el-col>
        </el-row>
        <!--筛选区结束-->


        <!--操作按钮区开始-->
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button
                type="primary"
                icon="el-icon-plus"
                size="mini"
                @click="handleAdd"
                v-hasPermi="['patrol:annex:add']"
            >新增</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button
                type="success"
                icon="el-icon-edit"
                size="mini"
                :disabled="single"
                @click="handleUpdate"
                v-hasPermi="['patrol:annex:edit']"
            >修改</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button
                type="danger"
                icon="el-icon-delete"
                size="mini"
                :disabled="multiple"
                @click="handleDelete"
                v-hasPermi="['patrol:annex:remove']"
            >删除</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button
                type="info"
                plain
                icon="el-icon-upload2"
                size="mini"
                @click="handleImport"
                v-hasPermi="['patrol:annex:export']"
            >导入</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button
                type="warning"
                icon="el-icon-download"
                size="mini"
                @click="handleExport"
                v-hasPermi="['system:user:export']"
            >导出</el-button>
          </el-col>
          <right-toolbar :showSearch.sync="showSearch" @queryTable="getList" :columns="columns"></right-toolbar>
        </el-row>
        <!--操作按钮区结束-->

        <!--数据表格开始-->
        <div class="tableDiv">
          <el-table  size="mini" :height="showSearch ? 'calc(100vh - 320px)' : 'calc(100vh - 260px)'"   style="width: 100%" v-loading="loading" border :data="annexList" @selection-change="handleSelectionChange">
            <el-table-column type="selection" width="50" align="center" />
            <el-table-column fixed label="序号" type="index" width="50">
              <template v-slot="scope">
                {{ (scope.$index + (queryParams.pageNum - 1) * queryParams.pageSize)+1 }}
              </template>
            </el-table-column>
                    <el-table-column label="合同id" align="center" prop="conId" />
                    <el-table-column label="附件id" align="center" prop="annexId" />
                    <el-table-column label="附件名称" align="center" prop="annexName" />
                    <el-table-column label="附件类型" align="center" prop="annexType" />
                    <el-table-column label="备注" align="center" prop="remark" />
                    <el-table-column label="文件类型" align="center" prop="fileType" />
                    <el-table-column label="附件路径" align="center" prop="annexPath" />
                    <el-table-column label="状态" align="center" prop="status" />
            <el-table-column
                label="操作"
                fixed="right"
                align="center"
                width="160"
                class-name="small-padding fixed-width"
            >
              <template slot-scope="scope" v-if="scope.row.userId !== 1">
                <el-button
                    size="mini"
                    type="text"
                    icon="el-icon-edit"
                    @click="handleUpdate(scope.row)"
                    v-hasPermi="['patrol:annex:edit']"
                >修改</el-button>
                <el-button
                    size="mini"
                    type="text"
                    icon="el-icon-delete"
                    @click="handleDelete(scope.row)"
                    v-hasPermi="['patrol:annex:remove']"
                >删除</el-button>
              </template>
            </el-table-column>
          </el-table>

          <pagination
              v-show="total>0"
              :total="total"
              :page.sync="queryParams.pageNum"
              :limit.sync="queryParams.pageSize"
              @pagination="getList"
          />
        </div>
        <!--数据表格结束-->
      </el-col>
    </el-row>

    <!-- 添加或修改合同关联的路段及路段编码对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="600px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">

                      <el-col :span="12">
                        <el-form-item label="合同id" prop="conId">
                          <el-input v-model="form.conId" placeholder="请输入合同id" />
                        </el-form-item>
                      </el-col>


                      <el-col :span="12">
                        <el-form-item label="附件id" prop="annexId">
                          <el-input v-model="form.annexId" placeholder="请输入附件id" />
                        </el-form-item>
                      </el-col>


                      <el-col :span="12">
                        <el-form-item label="附件名称" prop="annexName">
                          <el-input v-model="form.annexName" type="textarea" placeholder="请输入内容" />
                        </el-form-item>
                      </el-col>




                      <el-col :span="12">
                        <el-form-item label="备注" prop="remark">
                          <el-input v-model="form.remark" type="textarea" placeholder="请输入内容" />
                        </el-form-item>
                      </el-col>




                      <el-col :span="12">
                        <el-form-item label="附件路径" prop="annexPath">
                          <el-input v-model="form.annexPath" type="textarea" placeholder="请输入内容" />
                        </el-form-item>
                      </el-col>




                      <el-col :span="12">
                        <el-form-item label="删除标志" prop="delFlag">
                          <el-input v-model="form.delFlag" placeholder="请输入删除标志" />
                        </el-form-item>
                      </el-col>



      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 用户导入对话框 -->
    <el-dialog :title="upload.title" :visible.sync="upload.open" width="400px" append-to-body>
      <el-upload
          ref="upload"
          :limit="1"
          accept=".xlsx, .xls"
          :headers="upload.headers"
          :action="upload.url + '?updateSupport=' + upload.updateSupport"
          :disabled="upload.isUploading"
          :on-progress="handleFileUploadProgress"
          :on-success="handleFileSuccess"
          :auto-upload="false"
          drag
      >
        <i class="el-icon-upload"></i>
        <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
        <div class="el-upload__tip text-center" slot="tip">
          <div class="el-upload__tip" slot="tip">
            <el-checkbox v-model="upload.updateSupport" /> 是否更新已经存在的用户数据
          </div>
          <span>仅允许导入xls、xlsx格式文件。</span>
          <el-link type="primary" :underline="false" style="font-size:12px;vertical-align: baseline;" @click="importTemplate">下载模板</el-link>
        </div>
      </el-upload>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitFileForm">确 定</el-button>
        <el-button @click="upload.open = false">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
  import { listAnnex, getAnnex, delAnnex, addAnnex, updateAnnex } from "@/api/patrol/annex";
  import { getToken } from "@/utils/auth";
  import Treeselect from "@riophae/vue-treeselect";
  import "@riophae/vue-treeselect/dist/vue-treeselect.css";

  export default {
    name: "Annex",
    components: {  },
    data() {
      return {
        // 遮罩层
        loading: true,
        // 选中数组
        ids: [],
        // 非单个禁用
        single: true,
        // 非多个禁用
        multiple: true,
        // 显示搜索条件
        showSearch: false,
        dictType:[],
        // 总条数
        total: 0,
        // 合同关联的路段及路段编码表格数据
        annexList: null,
        // 弹出层标题
        title: "",
        // 部门树选项
        deptOptions: undefined,
        // 是否显示弹出层
        open: false,

        // 表单参数
        form: {},
        defaultProps: {
          children: "children",
          label: "label"
        },
        // 用户导入参数
        upload: {
          // 是否显示弹出层（用户导入）
          open: false,
          // 弹出层标题（用户导入）
          title: "",
          // 是否禁用上传
          isUploading: false,
          // 是否更新已经存在的用户数据
          updateSupport: 0,
          // 设置上传的请求头部
          headers: { Authorization: "Bearer " + getToken() },
          // 上传的地址
          url: process.env.VUE_APP_BASE_API + "/system/user/importData"
        },
        // 查询参数
        queryParams: {
          pageNum: 1,
          pageSize: 50,
            conId: null,
            annexId: null,
            annexName: null,
            annexType: null,
            fileType: null,
            annexPath: null,
            status: null,
        },
        // 列信息
        columns: [
        { key: 0, label: `合同id`, visible: true },
        { key: 1, label: `附件id`, visible: true },
        { key: 2, label: `附件名称`, visible: true },
        { key: 3, label: `附件类型`, visible: true },
        { key: 4, label: `备注`, visible: true },
        { key: 5, label: `文件类型`, visible: true },
        { key: 6, label: `附件路径`, visible: true },
        { key: 7, label: `状态（0-正常，1-停用）`, visible: true },
        { key: 8, label: `删除标志（0代表存在，1代表删除）`, visible: true }
        ],
        // 表单校验
        rules: {
    conId: [
        { required: true, message: "合同id不能为空", trigger: "blur" }
    ],
    annexName: [
        { required: true, message: "附件名称不能为空", trigger: "blur" }
    ],
    remark: [
        { required: true, message: "备注不能为空", trigger: "blur" }
    ],


        }
      };
    },
    watch: {
      // 根据名称筛选部门树
                      },
    created() {
      this.getList();
      // this.getDeptTree();
      // this.getConfigKey("sys.user.initPassword").then(response => {
      //   this.initPassword = response.msg;
      // });
    },
    methods: {
      /** 查询用户列表 */
      getList() {
        this.loading = true;
        listAnnex(this.queryParams).then(response => {
          this.annexList = response.rows;
          this.total = response.total;
          this.loading = false;
        });
      },
      // 取消按钮
      cancel() {
        this.open = false;
        this.reset();
      },
      // 表单重置
      reset() {
        this.form = {
            conId: null,
            annexId: null,
            annexName: null,
            annexType: null,
            remark: null,
            fileType: null,
            annexPath: null,
            status: null,
            delFlag: null
        };
        this.resetForm("form");
      },
      /** 搜索按钮操作 */
      handleQuery() {
        this.queryParams.pageNum = 1;
        this.getList();
      },
      /** 重置按钮操作 */
      resetQuery() {
        this.dateRange = [];
        this.resetForm("queryForm");
        this.handleQuery();
      },
      // 多选框选中数据
      handleSelectionChange(selection) {
        this.ids = selection.map(item => item.id);
        this.single = selection.length != 1;
        this.multiple = !selection.length;
      },
      /** 新增按钮操作 */
      handleAdd() {
        this.reset();
        this.open = true;
        this.title = "添加合同关联的路段及路段编码";
      },
      /** 修改按钮操作 */
      handleUpdate(row) {
        this.reset();
        const id = row.id || this.ids;
        getAnnex(id).then(response => {
          this.form = response.data;
          this.open = true;
          this.title = "修改合同关联的路段及路段编码";
        });

      },
      /** 提交按钮 */
      submitForm: function() {
        this.$refs["form"].validate(valid => {
          if (valid) {
            if (this.form.id != null) {
              updateAnnex(this.form).then(response => {
                this.$modal.msgSuccess("修改成功");
                this.open = false;
                this.getList();
              });
            } else {
              addAnnex(this.form).then(response => {
                this.$modal.msgSuccess("新增成功");
                this.open = false;
                this.getList();
              });
            }
          }
        });
      },
      /** 删除按钮操作 */
      handleDelete(row) {
        const id = row.id || this.ids;
        this.$modal.confirm('是否确认删除合同关联的路段及路段编码编号为"' + id + '"的数据项？').then(function() {
          return delAnnex(id);
        }).then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        }).catch(() => {});
      },

      /** 导出按钮操作 */
      handleExport() {

    this.download('patrol/annex/export', {
      ...this.queryParams
    }, `annex_${new Date().getTime()}.xlsx`)

      },
      /** 导入按钮操作 */
      handleImport() {
        this.upload.title = "用户导入";
        this.upload.open = true;
      },
      /** 下载模板操作 */
      importTemplate() {
        this.download('system/user/importTemplate', {
        }, `user_template.xlsx`)
      },
      // 文件上传中处理
      handleFileUploadProgress(event, file, fileList) {
        this.upload.isUploading = true;
      },
      // 文件上传成功处理
      handleFileSuccess(response, file, fileList) {
        this.upload.open = false;
        this.upload.isUploading = false;
        this.$refs.upload.clearFiles();
        this.$alert("<div style='overflow: auto;overflow-x: hidden;max-height: 70vh;padding: 10px 20px 0;'>" + response.msg + "</div>", "导入结果", { dangerouslyUseHTMLString: true });
        this.getList();
      },
      // 提交上传文件
      submitFileForm() {
        this.$refs.upload.submit();
      }
  }
  };
</script>
<style>
  .hasTagsView .app-main[data-v-078753dd]{
    background: #f5f7fa;
  }

  .tableDiv{
    background-color: white;
    padding-bottom: 10px;
  }
</style>
