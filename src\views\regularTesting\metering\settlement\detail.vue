<template>
  <div class="road-interflow-edit">
    <el-row :gutter="15">
      <el-form ref="elForm" :model="formData" :rules="rules" size="medium" label-width="100px">
        <el-col :span="24" style="margin-top: 18px">

          <el-table v-adjust-table
              :data="formData.settleMethodList"
              border
              height="200px"
              ref="tableRef"
              style="width: 100%">
            <el-table-column
                label="序号"
                align="center"
                type="index"
                width="50"
            />
            <el-table-column
                prop="schemeCode"
                align="center"
                label="子目号"
                width="100">
            </el-table-column>
            <el-table-column
              prop="schemeName"
              align="center"
              label="子目名称"
              width="100">
                <template slot-scope="scope">
                  <el-input v-model="scope.row.schemeName">
                  </el-input>
              </template>
            </el-table-column>
            <el-table-column
              prop="unit"
              align="center"
              label="单位"
              width="100">
                <template slot-scope="scope">
                  <el-input v-model="scope.row.unit">
                  </el-input>
              </template>
            </el-table-column>
            <el-table-column
                prop="price"
                align="center"
                label="单价"
                width="100">
            </el-table-column>
            <el-table-column
                prop="calcDesc"
                align="center"
                label="计算式"
                width="100">
              <template slot-scope="scope">
                <el-input v-model="scope.row.calcDesc" @change="changeCalculation(scope.row)">
                </el-input>
              </template>
            </el-table-column>
            <el-table-column
                prop="num"
                align="center"
                label="方法数量"
                width="100">
              <template slot-scope="scope">
                <el-input v-model="scope.row.num" @change="changeSchemeNum(scope.row)">
                </el-input>
              </template>
            </el-table-column>
            <el-table-column
                prop="amount"
                align="center"
                label="资金"
                width="100">
            </el-table-column>
            <el-table-column
                prop="remark"
                align="center"
                label="备注"
                width="100">
              <template slot-scope="scope">
                <el-input v-model="scope.row.remark">
                </el-input>
              </template>
            </el-table-column>
          </el-table>
        </el-col>
        <el-col :span="24" style="margin-top: 18px">
          <div class="card_title">
            <div>计算式及说明</div>
          </div>
          <el-input class="calculation_desc" v-model="formData.calculationDesc" type="textarea" :rows="3">
          </el-input>
        </el-col>
        <el-col :span="24" style="text-align: right;padding-right: 7.5px;margin-top: 18px">
          <el-button type="primary" @click="onSave">确 定</el-button>
          <el-button @click="onClose">退出</el-button>
        </el-col>
      </el-form>
    </el-row>
  </div>
</template>
<script>
import {updateOtherMethodList} from "@/api/regularTesting/metering/settlement";
import { Decimal } from 'decimal.js';

export default {
  name: "index",
  data() {
    return {
      formData: {
        settleMethodList: []
      },
      loading: false,
      urgentDegreeOptions: [{
        "label": "计算",
        "value": 1
      }, {
        "label": "不计算",
        "value": 2
      }]
    }
  },
  props: {
    rowData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  watch: {
    rowData: {
      handler(val) {
        if (val) {
          this.formData = {
            id: val.settleId,
            settleMethodList: val.methodList,
            calculationDesc: val.calculationDesc
          }
        }
      },
      immediate: true
    }
  },
  mounted() {
  },
  methods: {
    onSave() {
      this.loading = true
      updateOtherMethodList(this.formData).then(res => {
        this.loading = false
        this.$message.success('保存成功')
        this.onClose()
      })
    },
    changeCalculation(row) {
      if (!this.isValidMathFormula(row.calcDesc)) {
        this.$modal.msgError('计算式错误，请检查')
        return
      }
      this.$set(row, 'num', this.ceilToTwo(this.math.evaluate(row.calcDesc), row.decimalPlaces))
      this.$set(row, 'amount', Math.round(new Decimal(row.num || 0).times(row.price || 0).toNumber()))
      this.total = this.formData.settleMethodList.reduce((acc, curr) => acc + curr.amount, 0)
    },
    changeSchemeNum(row) {
      this.$set(row, 'amount', Math.round(new Decimal(row.num || 0).times(row.price || 0).toNumber()))
      this.total = this.formData.settleMethodList.reduce((acc, curr) => acc + curr.amount, 0)
    },
    onClose() {
      this.$emit("close")
    },
  }
}
</script>
<style scoped lang="scss">
.card_title {
  width: 200px;
  text-align: left;
  margin-bottom: 15px;
  font-weight: bold;
}
</style>
<style lang="scss" scoped>
@import "@/assets/styles/business.scss";
</style>
