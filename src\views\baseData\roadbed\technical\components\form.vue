<template>
  <div>
    <el-dialog
      :title="title"
      :visible.sync="showAddEdit"
      width="60%"
      append-to-body
      :before-close="handleClose"
      :close-on-click-modal="false"
      :class="forView ? 'forView':''"
    >
      <div
        v-loading="loading"
        style="height: 60vh;overflow-y: auto;padding: 0 10px 0 5px;"
      >
        <el-form
          ref="form"
          :model="form"
          :rules="rules"
          label-width="120px"
          :disabled="forView ? true : false"
        >
          <div style="display: flex; flex-wrap: wrap; margin-bottom: 20px;">

            <el-col :span="12">
              <el-form-item
                label="处治年份"
                prop="dealYear"
              >
                <el-date-picker
                  v-model="form.dealYear"
                  type="year"
                  style="width: 100%;"
                  placeholder="请选择"
                  clearable
                  value-format="yyyy"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item
                label="养护分类"
                prop="maintenanceType"
              >
                <el-select
                  v-model="form.maintenanceType"
                  style="width: 100%;"
                  placeholder="请选择"
                  clearable
                >
                  <el-option
                    v-for="item in dict.type.base_maintenance_type"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item
                label="养护工程类别"
                prop="projectType"
              >
                <el-select
                  v-model="form.projectType"
                  style="width: 100%;"
                  placeholder="请选择"
                  clearable
                >
                  <el-option
                    v-for="item in dict.type.base_project_type"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item
                label="结算金额"
                prop="calcFund"
              >
                <el-input-number
                  v-model="form.calcFund"
                  style="width: 100%;"
                  clearable
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item
                label="处治车道"
                prop="lane"
              >
                <el-select
                  v-model="form.lane"
                  style="width: 100%;"
                  placeholder="请选择"
                  clearable
                >
                  <el-option
                    v-for="item in dict.type.lane"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item
                label="起点桩号"
                prop="beginStake"
              >
                <PileInput v-model="form.beginStake" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item
                label="终点桩号"
                prop="endStake"
              >
                <PileInput v-model="form.endStake" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item
                label="长度(m)"
                prop="length"
              >
                <el-input-number
                  v-model="form.length"
                  style="width: 100%;"
                  clearable
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item
                label="宽度(m)"
                prop="width"
              >
                <el-input-number
                  v-model="form.width"
                  style="width: 100%;"
                  clearable
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item
                label="面积(m²)"
                prop="area"
              >
                <el-input-number
                  v-model="form.area"
                  style="width: 100%;"
                  clearable
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item
                label="纬度"
                prop="latitude"
              >
                <el-input-number
                  v-model="form.latitude"
                  style="width: 100%;"
                  clearable
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item
                label="经度"
                prop="longitude"
              >
                <el-input-number
                  v-model="form.longitude"
                  style="width: 100%;"
                  clearable
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item
                label="养护措施"
                prop="measure"
              >
                <el-input
                  v-model="form.measure"
                  style="width: 100%;"
                  clearable
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item
                label="备注"
                prop="remark"
              >
                <el-input
                  type="textarea"
                  :rows="3"
                  v-model="form.remark"
                  placeholder="请输入备注"
                />
              </el-form-item>
            </el-col>
            <!-- <el-col :span="12">
              <el-form-item
                label="数据状态"
                prop="status"
              >
                <el-select
                  v-model="form.status"
                  style="width: 100%;"
                  placeholder="请选择"
                  clearable
                >
                  <el-option
                    v-for="item in dict.type.base_data_state"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  />
                </el-select>
              </el-form-item>
            </el-col> -->
          </div>
        </el-form>
      </div>
      <div slot="footer">
        <el-button
          v-if="!forView"
          type="primary"
          :loading="loading"
          @click="handleSubmit('submit')"
        >提 交</el-button>

        <el-button @click="handleClose">取 消</el-button>
      </div>
    </el-dialog>

  </div>
</template>

<script>
import PileInput from '@/components/PileInput/index.vue'

import { createIdWorker } from '@/api/baseData/common'
import { listMaintenanceSectionAll } from '@/api/system/maintenanceSection'
import { listByMaintenanceSectionId } from '@/api/baseData/common/routeLine'
import {
  addMaintain,
  updateMaintain,
  
} from '@/api/baseData/roadbed/assess/index'


export default {
  name: 'maintain',
  props: {
    formData: {
      default: {}
    },
    showAddEdit: {
      default: false
    },
    title: {
      default: '添加路面养护'
    },
    forView: {
      default: false
    },
    choseId: {
      default: ''
    }
  },
  dicts: [
    'lane',
    'base_data_state',
    'base_maintenance_type',
    'base_project_type',
    'sys_surface_type'
  ],
  components: { PileInput },
  data() {
    return {
      loading: false,
      form: {},
      rules: {
        // 处治年份  养护分类  养护工程类别 结算金额  处治车道  起点桩号 终点桩号 长度 宽度 面积

        dealYear: [
          { required: true, message: '请选择处治年份', trigger: 'change' }
        ],
        maintenanceType: [
          { required: true, message: '请选择养护分类', trigger: 'change' }
        ],
        projectType: [
          { required: true, message: '请选择养护工程类别', trigger: 'change' }
        ],
        calcFund: [
          { required: true, message: '请输入结算金额', trigger: 'change' }
        ],
        lane: [
          { required: true, message: '请选择处治车道', trigger: 'change' }
        ],
        beginStake: [
          { required: true, message: '请输入起点桩号', trigger: 'change' }
        ],
        endStake: [
          { required: true, message: '请输入终点桩号', trigger: 'change' }
        ],
        length: [
          { required: true, message: '请输入长度', trigger: 'change' }
        ],
        width: [
          { required: true, message: '请输入宽度', trigger: 'change' }
        ],
        area: [
          { required: true, message: '请输入面积', trigger: 'change' }
        ],
      
      },
      routeOptions: [],
      routeList: [],
     
    }
  },
  created() {
    this.init()
  },
  methods: {
    init() {
      if (this.formData.id) {
        this.form = JSON.parse(JSON.stringify(this.formData))
      }
    },
    handleSubmit(type) {
      let pass = true
      switch (type) {
        case 'submit':
          this.form.isStaging = 2
          break
        case 'save':
          this.form.isStaging = 1
          break
      }
      if (this.form.isStaging == 2) {
        this.$refs.form.validate(valid => {
          if (valid) {
            pass = true
          } else {
            pass = false
            return false
          }
        })
      }
      if (!pass) return
     
      this.form.mainId=this.choseId
      this.loading = true
      if (this.form.id != null) {
        updateMaintain(this.form)
          .then(response => {
            this.$modal.msgSuccess('修改成功')
            this.$emit('refresh')
          })
          .catch(() => {
            this.loading = false
          })
      } else {
        const api =addMaintain
        api(this.form)
          .then(response => {
            this.$modal.msgSuccess(
              this.form.isStaging === 1 ? '暂存成功' : '新增成功'
            )
            this.$emit('refresh')
          })
          .catch(() => {
            this.loading = false
          })
      }
    },
    handleClose() {
      if (this.forView) {
        this.form = {}
        this.$emit('close', false)
      } else {
        this.$modal
          .confirm('确认退出？')
          .then(() => {
            this.form = {}
            this.$emit('close', false)
          })
          .catch(() => {})
      }
    },
   
  },
  computed: {},
  watch: {
    showAddEdit: {
      handler(val) {
        if (val) {
          this.init()
        }
      },
      immediate: true
    }
  }
}
</script>

<style lang="scss" scoped>
.forView ::v-deep .el-input.is-disabled .el-input__inner {
  background-color: white;
  border-color: #dfe4ed;
  color: black;
}
::v-deep .el-dialog__header {
  border-bottom: 1px #dfe4ed solid;
  padding: 20px 30px !important;
}
::v-deep .el-divider--horizontal {
  margin: 20px 0 !important;
}
</style>
