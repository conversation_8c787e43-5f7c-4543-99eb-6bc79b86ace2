<template>
  <div class="event-trends" v-loading="loading" element-loading-background="rgba(0, 0, 0, 0.3)">
    <el-tabs v-model="activeName" @tab-click="handleClick" style="height: 100%;">
      <el-tab-pane label="总趋势" name="zsj" style="height: 100%;">
        <Echarts :option="qsOption" v-if="qsOption" height="100%" :key="'zqsKey' + year" />
      </el-tab-pane>
      <el-tab-pane label="事件分类" name="sj" style="height: 100%;">
        <Echarts :option="sjOption" v-if="sjOption && activeName === 'sj'" height="100%" :key="'sjKey' + year" />
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
import * as echarts from "echarts";
import Echarts from '../../echarts/echarts.vue';
import { isBigScreen } from "@/views/cockpit/util/utils";
import { getDiseaseEventTrends } from '@/api/cockpit/maintain';

export default {
  name: 'eventTrends',
  components: {
    Echarts
  },
  props: {
    year: {
      type: [String, Number],
      default: ''
    }
  },
  data() {
    return {
      isBig: isBigScreen(),
      activeName: 'zsj',
      qsOption: null,
      sjOption: null,
      loading: false,
    }
  },
  mounted() { },
  watch: {
    year: {
      async handler(newVal) {
        if (newVal) {
          this.loading = true;
          let data = await this.getData();
          if (data) {
            let qsObj = {
              xAxisData: [],
              data1: [],
              data2: [],
            };
            let sjObj = {
              xAxisData: [],
              data1: [],
              data2: [],
            }
            let xfData = data.filter(x => x.name === '下发')
            let wgData = data.filter(x => x.name === '完工')
            let ycData = data.filter(x => x.name === '日常养护')
            let blData = data.filter(x => x.name === '被损被盗')
            qsObj.data1 = xfData.map(v => v.count) || [];
            qsObj.data2 = wgData.map(v => v.count) || [];
            sjObj.data1 = ycData.map(v => v.count) || [];
            sjObj.data2 = blData.map(v => v.count) || [];

            // 比较 xfData 和 wgData 数组长度，用长的数组的 month 作为 xAxisData 的数据
            if (xfData.length >= wgData.length) {
              qsObj.xAxisData = xfData.map(item => item.month);
            } else {
              qsObj.xAxisData = wgData.map(item => item.month);
            }

            // 同样比较 ycData 和 blData 数组长度，用长的数组的 month 作为 xAxisData 的数据
            if (ycData.length >= blData.length) {
              sjObj.xAxisData = ycData.map(item => item.month);
            } else {
              sjObj.xAxisData = blData.map(item => item.month);
            }

            let qsLegendData = ['下发', '完工'];
            this.qsOption = this.initCharts(qsLegendData, qsObj);
            let sjLegendData = ['日常养护', '被损被盗'];
            this.sjOption = this.initCharts(sjLegendData, sjObj);
            this.loading = false;
          }
        }
      },
      immediate: true,
      deep: true,
    }
  },
  methods: {
    handleClick(tab, event) {
      // console.log(tab, event);
    },
    // 获取数据
    getData() {
      return new Promise((resolve, reject) => {
        getDiseaseEventTrends(this.year).then((res) => {
          if (res.code === 200 && res.rows) {
            resolve(res.rows)
          } else {
            resolve('')
          }
        }).catch(err => {
          reject(err)
        })
      })
    },
    initCharts(legendData = [], obj = {}) {
      let option = {
        backgroundColor: 'rgba(255,255,255,0)',
        grid: {
          left: '1%',
          right: '4%',
          top: '15%',
          bottom: '2%',
          containLabel: true
        },
        tooltip: {
          show: true,
          trigger: 'item'
        },
        legend: {
          show: true,
          x: 'center',
          y: '0',
          icon: 'circle',
          itemWidth: this.isBig ? 30 : 10,
          itemHeight: this.isBig ? 30 : 10,
          textStyle: {
            color: '#fff',
            fontSize: this.isBig ? 24 : 12,
          },
          data: legendData
        },
        xAxis: [
          {
            type: 'category',
            boundaryGap: false,
            axisLabel: {
              color: '#fff',
              fontSize: this.isBig ? 24 : 12,
            },
            axisLine: {
              show: true,
              lineStyle: {
                color: 'transparent'
              }
            },
            axisTick: {
              show: false,
            },
            splitLine: {
              show: false,
            },
            data: obj.xAxisData || ['1', '2', '3', '4', '5', '6', '7', '8', '9', '10']
          }
        ],
        yAxis: [
          {
            type: 'value',
            name: '个',
            nameTextStyle: {
              color: '#999999',
              fontSize: this.isBig ? 24 : 12,
              padding: [0, 0, 0, -30]
            },
            min: 0,
            axisLabel: {
              formatter: '{value}',
              fontSize: this.isBig ? 24 : 12,
              textStyle: {
                color: '#999999'
              }
            },
            axisLine: {
              lineStyle: {
                color: '#27b4c2'
              }
            },
            axisTick: {
              show: false,
            },
            splitLine: {
              show: true,
              lineStyle: {
                color: 'rgba(110,112,121,0.5)'
              }
            },
            splitArea: false
          },
        ],
        series: [
          {
            name: legendData[0],
            type: 'line',
            stack: '总量',
            symbol: 'circle',
            symbolSize: 8,
            smooth: true, // 平滑曲线
            itemStyle: {
              normal: {
                color: '#1CFFBC',
                lineStyle: {
                  color: "#1CFFBC",
                  width: 1
                },
                areaStyle: {
                  color: new echarts.graphic.LinearGradient(0, 1, 0, 0, [{
                    offset: 0,
                    color: 'rgba(28,255,188,0.1)'
                  }, {
                    offset: 1,
                    color: 'rgba(28,255,188,0.6)'
                  }]),
                }
              }
            },
            markPoint: {
              itemStyle: {
                normal: {
                  color: 'red'
                }
              }
            },
            data: obj.data1 || [7800, 9000, 7000, 10000, 12000, 12800, 8200, 9600, 9900, 10000]
          },
          {
            name: legendData[1],
            type: 'line',
            stack: '总量',
            symbol: 'circle',
            smooth: true,
            symbolSize: 8,
            itemStyle: {
              normal: {
                color: '#0154FB',
                lineStyle: {
                  color: "#0154FB",
                  width: 1
                },
                areaStyle: {
                  color: new echarts.graphic.LinearGradient(0, 1, 0, 0, [{
                    offset: 0,
                    color: 'rgba(1,84,251,0.1)'
                  }, {
                    offset: 1,
                    color: 'rgba(1,84,251,0.6)'
                  }]),
                }
              }
            },
            data: obj.data2 || [5000, 7100, 5100, 8400, 7800, 7900, 5000, 5800, 6000, 6200]
          },
        ]
      };
      return option;
    },
  }
}
</script>

<style lang="scss" scoped>
.event-trends {
  width: 100%;
  height: 100%;
  padding: 0 10px;

  ::v-deep .el-tabs {
    .el-tabs__item {
      font-family: Microsoft YaHei UI, Microsoft YaHei UI;
      font-weight: 700;
      font-size: 14px;
      color: rgba(255, 255, 255, 0.6);
      padding-right: 10px;

      .is-active {
        font-family: Microsoft YaHei UI, Microsoft YaHei UI;
        font-weight: 700;
        font-size: 15px;
        color: #FFFFFF !important;
        text-shadow: 0px 0px 16px #0088FF;
      }
    }

    .el-tabs__item.is-active {
      color: #FFFFFF !important;
    }

    .el-tabs__nav-wrap::after {
      background-color: #9CBDFF;
    }

    .el-tabs__content {
      height: calc(100% - 60px);
    }
  }
}
</style>