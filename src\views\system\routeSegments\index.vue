<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" label-width="86px">
      <el-form-item label="" prop="baseRouteId">
        <el-select v-model="queryParams.baseRouteId" clearable filterable placeholder="请选择路线">
          <el-option
            v-for="item in routeList"
            :key="item.routeId"
            :label="item.routeName"
            :value="item.routeId">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="" prop="maintenanceSectionId">
        <el-select v-model="queryParams.maintenanceSectionId" clearable filterable placeholder="请选择养护路段">
          <el-option
            v-for="item in maintenanceSectionList"
            :key="item.maintenanceSectionId"
            :label="item.maintenanceSectionName"
            :value="item.maintenanceSectionId">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="" prop="routeSegmentsName">
        <el-input
          v-model="queryParams.routeSegmentsName"
          placeholder="请输入子段名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>





      <el-form-item label="" prop="roadType">
        <el-select v-model="queryParams.roadType" placeholder="请选择路段类型" clearable>
          <el-option
            v-for="dict in dict.type.sys_route_type"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>

      <el-form-item label="" prop="ownerUnit">
        <el-select v-model="queryParams.ownerUnit" placeholder="请选择权属类型" clearable>
          <el-option label="集团公司" value="集团公司"></el-option>
          <el-option label="项目公司" value="项目公司"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
        <el-button v-show="!showSearch" @click="showSearch=true" icon="el-icon-arrow-down" circle></el-button>
        <el-button v-show="showSearch" @click="showSearch=false" icon="el-icon-arrow-up" circle></el-button>
      </el-form-item>
      <br>
      <el-form-item v-show="showSearch" label="" prop="maintenanceUnit">
        <treeselect v-model="queryParams.maintenanceUnit" :options="deptOptions" :show-count="true"
                    placeholder="请选择管养单位"/>
      </el-form-item>
      <el-form-item v-show="showSearch" label="" prop="roadSurfaceType">
        <el-select v-model="queryParams.roadSurfaceType" placeholder="请选择路面类型" clearable>
          <el-option
            v-for="dict in dict.type.sys_surface_type"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>

      <el-form-item v-show="showSearch" label="" prop="state">
        <el-select v-model="queryParams.state" placeholder="请选择运营状态" clearable>
          <el-option
            v-for="dict in dict.type.sys_operation_state"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>

      <el-form-item  v-show="showSearch"  prop="lanesStr">
        <el-select v-model="queryParams.lanesStr" placeholder="请选择车道数" clearable multiple>
          <el-option
            v-for="dict in lanesOptions"
            :label="dict"
            :value="dict"
          />
        </el-select>
        <!--          <el-input v-model="form.lanes" placeholder="请输入车道数"/>-->
      </el-form-item>


      <el-form-item  v-show="showSearch" label="" prop="roadGrade">
        <el-select v-model="queryParams.roadGrade" placeholder="请选择路线等级" clearable>
          <el-option
            v-for="dict in dict.type.sys_route_grade"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>

      <el-form-item v-show="showSearch" label="" prop="dataRange">
        <!--        <el-date-picker clearable-->
        <!--                        style="width: 240px;"-->
        <!--                        v-model="queryParams.openingTime"-->
        <!--                        type="date"-->
        <!--                        value-format="yyyy-MM-dd"-->
        <!--                        placeholder="请选择通车时间">-->
        <!--        </el-date-picker>-->

        <el-date-picker
          v-model="queryParams.dataRange"
          type="datetimerange"
          value-format="yyyy-MM-dd"
          range-separator="至"
          start-placeholder="通车时间"
          end-placeholder="结束日期">
        </el-date-picker>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['system:routeSegments:add']"
        >新增
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['system:routeSegments:edit']"
        >修改
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['system:routeSegments:remove']"
        >删除
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['system:routeSegments:export']"
        >导出
        </el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>
    <!--数据表格开始-->
    <div class="tableDiv">
      <el-table v-adjust-table size="mini" :height="showSearch ? 'calc(100vh - 320px)' : 'calc(100vh - 260px)'" border
                v-loading="loading" :data="routeSegmentsList" @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" align="center"/>
        <el-table-column label="序号" fixed align="center" type="index" width="50">
          <template v-slot="scope">
            {{ (scope.$index + (queryParams.pageNum - 1) * queryParams.pageSize)+1 }}
          </template>
        </el-table-column>
        <el-table-column label="子段名称" fixed :show-overflow-tooltip="true" align="center" prop="routeSegmentsName" width="240"/>
        <el-table-column label="养护路段"  :show-overflow-tooltip="true" align="center"
                         prop="maintenanceSectionName" width="120"/>
        <el-table-column label="路线编码" :show-overflow-tooltip="true" align="center" prop="baseRouteCode" width="120"/>
<!--        <el-table-column label="路线" :show-overflow-tooltip="true" align="center" prop="baseRouteName" width="120"/>-->
        <el-table-column label="管养单位" :show-overflow-tooltip="true" align="center" prop="maintenanceUnitName"
                         width="120"/>
        <el-table-column label="统计里程(km)" :show-overflow-tooltip="true" align="center" prop="roadSectionLength"
                         :formatter="(...arg)=>{if(arg[2]) return (arg[2]/1000).toFixed(3).toLocaleString()}"
                         width="130"/>
        <el-table-column label="养护里程(km)" :show-overflow-tooltip="true" align="center" prop="mainLength"
                         width="120"
                         :formatter="(...arg)=>{if(arg[2]) return (arg[2]/1000).toFixed(3).toLocaleString()}"/>
        <el-table-column label="路线等级" :show-overflow-tooltip="true" align="center" prop="roadGrade">
          <template slot-scope="scope">
            <dict-tag :options="dict.type.sys_route_grade" :value="scope.row.roadGrade"/>
          </template>
        </el-table-column>
        <el-table-column label="路段类型" :show-overflow-tooltip="true" align="center" prop="roadType">
          <template slot-scope="scope">
            <dict-tag :options="dict.type.sys_route_type" :value="scope.row.roadType"/>
          </template>
        </el-table-column>
        <el-table-column label="路面类型" :show-overflow-tooltip="true" align="center" prop="roadSurfaceType"
                         width="120">
          <template slot-scope="scope">
            <dict-tag :options="dict.type.sys_surface_type" :value="scope.row.roadSurfaceType"/>
          </template>
        </el-table-column>
        <el-table-column label="匝道口" :show-overflow-tooltip="true" align="center" prop="ramp">
          <template slot-scope="scope">
            <dict-tag :options="dict.type.sys_ramp" :value="scope.row.ramp"/>
          </template>
        </el-table-column>
        <el-table-column label="车道数" :show-overflow-tooltip="true" align="center" prop="lanes"/>
        <el-table-column label="通车时间" :show-overflow-tooltip="true" align="center" prop="openingTime" width="180">
          <template slot-scope="scope">
            <span>{{ parseTime(scope.row.openingTime, '{y}-{m}-{d}') }}</span>
          </template>
        </el-table-column>
        <el-table-column label="起点桩号" :show-overflow-tooltip="true" align="center" prop="pileStart" width="100" :formatter="(...arg)=>formatPile(arg[2])"/>
        <el-table-column label="终点桩号" :show-overflow-tooltip="true" align="center" prop="pileEnd"   width="100"  :formatter="(...arg)=>formatPile(arg[2])"/>
        <el-table-column label="起点名称" :show-overflow-tooltip="true" align="center" prop="placeStartName" width="120"/>
        <el-table-column label="终点名称" :show-overflow-tooltip="true" align="center" prop="placeEndName" width="120"/>
        <el-table-column label="统一里程起点桩号" :show-overflow-tooltip="true" align="center" width="140"
                         prop="unifiedMileagePileStart"
                         :formatter="(...arg)=>formatPile(arg[2])"/>
        <el-table-column label="统一里程终点桩号" :show-overflow-tooltip="true" align="center" width="140"
                         prop="unifiedMileagePileEnd"
                         :formatter="(...arg)=>formatPile(arg[2])"/>
        <el-table-column label="运营状态" :show-overflow-tooltip="true" align="center" prop="state" width="120">
          <template slot-scope="scope">
            <dict-tag :options="dict.type.sys_operation_state" :value="scope.row.state"/>
          </template>
        </el-table-column>


        <el-table-column label="行政区域代码" width="120" align="center" prop="areaCode" />
        <el-table-column label="是否一幅高速" width="140" align="center" prop="isSingleExpressway">
        <template v-slot="scope">
          <dict-tag :options="dict.type.sys_yes_no" :value="scope.row.isSingleExpressway"/>
        </template>
        </el-table-column>
        <el-table-column label="路基宽度(米)" width="140" align="center" prop="subgradeWidth" />
        <el-table-column label="路面宽度(米)" width="140" align="center" prop="pavementWidth" />
        <el-table-column label="面层厚度(厘米)" width="140" align="center" prop="surfaceThickness" />
        <el-table-column label="设计时速" align="center" prop="designSpeed" />
        <el-table-column label="抗震等级" width="120" show-overflow-tooltip align="center" prop="seismicGrade">
          <template v-slot="scope">
            <dict-tag :options="dict.type.bridge_seismic_grade" :value="scope.row.seismicGrade"/>
          </template>
        </el-table-column>
        <el-table-column label="路基设计洪水频率" width="220" align="center" prop="floodFrequency">
          <template v-slot="scope">
            <dict-tag :options="dict.type.sys_segment_flood_frequency" :value="scope.row.floodFrequency"/>
          </template>
        </el-table-column>
        <el-table-column label="修建年度" align="center" prop="constructionYear" />
        <el-table-column  label="断链类型" align="center" prop="brokenChainType" width="220" show-overflow-tooltip>
          <template v-slot="scope">
            <dict-tag :options="dict.type.sys_segment_broken_chain_type" :value="scope.row.brokenChainType"/>
          </template>
        </el-table-column>
        <el-table-column label="是否断头路段" width="120" align="center" prop="isDeadEnd">
        <template v-slot="scope">
          <dict-tag :options="dict.type.sys_yes_no" :value="scope.row.isDeadEnd"/>
        </template>
        </el-table-column>
        <el-table-column label="是否渡口路段" width="140" align="center" prop="isFerrySection">
          <template v-slot="scope">
            <dict-tag :options="dict.type.sys_yes_no" :value="scope.row.isFerrySection"/>
          </template>
        </el-table-column>
        <el-table-column label="是否渡运车辆" width="140" align="center" prop="isVehicleFerry">
          <template v-slot="scope">
            <dict-tag :options="dict.type.sys_yes_no" :value="scope.row.isVehicleFerry"/>
          </template>
        </el-table-column>
        <el-table-column label="是否长链" align="center" prop="isLongChain">
        <template v-slot="scope">
          <dict-tag :options="dict.type.sys_yes_no" :value="scope.row.isLongChain"/>
        </template>
        </el-table-column>
        <el-table-column label="地貌" align="center" prop="terrain">
          <template v-slot="scope">
            <dict-tag :options="dict.type.sys_segment_terrain" :value="scope.row.terrain"/>
          </template>
        </el-table-column>
        <el-table-column show-overflow-tooltip label="省际出入口" width="240" align="center" prop="provincialBoundary">
          <template v-slot="scope">
            <dict-tag :options="dict.type.sys_segment_provincial_boundary" :value="scope.row.provincialBoundary"/>
          </template>
        </el-table-column>
        <el-table-column label="备注" width="120" align="center" prop="remark" />


        <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="120" fixed="right">
          <template slot-scope="scope">
            <el-button
              size="mini"
              type="text"
              icon="el-icon-edit"
              @click="handleUpdate(scope.row)"
              v-hasPermi="['system:routeSegments:edit']"
            >修改
            </el-button>
            <el-button
              size="mini"
              type="text"
              icon="el-icon-delete"
              @click="handleDelete(scope.row)"
              v-hasPermi="['system:routeSegments:remove']"
            >删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <pagination
        :totalMileage="(totalMileage/1000).toFixed(3).toLocaleString()"
        v-show="total>0"
        :total="total"
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
        @pagination="getList"
      >

        <span slot="mileage"  style="margin-left: 50px;" v-text="'总统计里程：' + (roadSectionLength/1000).toFixed(3).toLocaleString() + ' km'"></span>


      </pagination>
    </div>
    <!--数据表格结束-->
    <!-- 添加或修改养护子段管理对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="1100px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="140px" inline>
        <el-form-item label="养护路段" prop="maintenanceSectionId">
          <el-select v-model="form.maintenanceSectionId" filterable placeholder="请选择">
            <el-option
              v-for="item in maintenanceSectionList"
              :key="item.maintenanceSectionId"
              :label="item.maintenanceSectionName"
              :value="item.maintenanceSectionId">
            </el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="管养单位" prop="maintenanceUnit">
          <treeselect v-model="form.maintenanceUnit" :options="deptOptions" :show-count="true"
                      placeholder="请选择管养单位"/>
        </el-form-item>


        <el-form-item label="子段名称" prop="routeSegmentsName">
          <el-input v-model="form.routeSegmentsName" placeholder="请输入子段名称"/>
        </el-form-item>

        <el-form-item label="路线" prop="baseRouteId">
          <el-select v-model="form.baseRouteId" filterable placeholder="请选择">
            <el-option
              v-for="item in routeList"
              :key="item.routeId"
              :label="item.routeName"
              :value="item.routeId">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="起点名称" prop="placeStartName">
          <el-input v-model="form.placeStartName" placeholder="请输入起点名称"/>
        </el-form-item>
        <el-form-item label="终点名称" prop="placeEndName">
          <el-input v-model="form.placeEndName" placeholder="请输入终点名称"/>
        </el-form-item>
        <el-form-item label="起点桩号" prop="pileStart">
          <PileInput v-model="form.pileStart" @input="onPileChange(1)" class="pile-input"
                     placeholder="请输入起点桩号"></PileInput>
        </el-form-item>
        <el-form-item label="终点桩号" prop="pileEnd">
          <PileInput v-model="form.pileEnd" @input="onPileChange(1)" class="pile-input"
                     placeholder="请输入终点桩号"></PileInput>
        </el-form-item>
        <el-form-item label="统一里程起点桩号" prop="unifiedMileagePileStart">
          <PileInput v-model="form.unifiedMileagePileStart"  class="pile-input"
                     placeholder="请输入统一里程起点桩号"></PileInput>
        </el-form-item>
        <el-form-item label="统一里程终点桩号" prop="unifiedMileagePileEnd">
          <PileInput v-model="form.unifiedMileagePileEnd" class="pile-input"
                     placeholder="请输入统一里程终点桩号"></PileInput>
        </el-form-item>
        <el-form-item label="养护里程(m)" prop="mainLength">
          <el-input v-model="form.mainLength" placeholder="请输入主线里程(m)"/>
        </el-form-item>
        <el-form-item label="统计里程(m)" prop="roadSectionLength">
          <el-input v-model="form.roadSectionLength" placeholder="请输入子路段长度(m)"/>
        </el-form-item>
        <el-form-item label="车道数" prop="lanes">
          <el-input v-model.number="form.lanes" placeholder="请输入车道数"/>
        </el-form-item>
        <el-form-item label="路线等级" prop="roadGrade">
          <el-select v-model="form.roadGrade" placeholder="请选择路线等级">
            <el-option
              v-for="dict in dict.type.sys_route_grade"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="路段类型" prop="roadType" :key="1">
          <el-select v-model="form.roadType" placeholder="请选择路段类型">
            <el-option
              v-for="dict in dict.type.sys_route_type"
              :key="dict.value"
              :label="dict.label"
              :value="parseInt(dict.value)"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="匝道口" prop="ramp" v-if="form.roadType === 2" :key="2">
          <el-select  v-model="form.ramp" placeholder="请选择路匝道口">
            <el-option
              v-for="dict in dict.type.sys_ramp"
              :key="dict.value"
              :label="dict.label"
              :value="parseInt(dict.value)"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="路面类型" prop="roadSurfaceType">
          <el-select v-model="form.roadSurfaceType" placeholder="请选择路面类型">
            <el-option
              v-for="dict in dict.type.sys_surface_type"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            ></el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="通车时间" prop="openingTime">
          <el-date-picker clearable
                          v-model="form.openingTime"
                          type="date"
                          value-format="yyyy-MM-dd"
                          placeholder="请选择通车时间">
          </el-date-picker>
        </el-form-item>


        <el-form-item label="运营状态" prop="state">
          <el-select v-model="form.state" placeholder="请选择运营状态">
            <el-option
              v-for="dict in dict.type.sys_operation_state"
              :key="dict.value"
              :label="dict.label"
              :value="parseInt(dict.value)"
            ></el-option>
          </el-select>
        </el-form-item>



          <el-form-item label="所在行政区域代码" prop="areaCode">
            <el-input v-model="form.areaCode" placeholder="请输入所在行政区域代码" />
          </el-form-item>


          <el-form-item label="是否一幅高速" prop="isSingleExpressway">

            <el-select v-model="form.isSingleExpressway" placeholder="请选择是否一幅高速">
              <el-option
                v-for="dict in dict.type.sys_yes_no"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              ></el-option>
            </el-select>
          </el-form-item>


          <el-form-item label="路基宽度(米)" prop="subgradeWidth">
            <el-input v-number-only v-model="form.subgradeWidth" placeholder="请输入路基宽度(米)" />
          </el-form-item>


          <el-form-item label="路面宽度(米)" prop="pavementWidth">
            <el-input v-number-only v-model="form.pavementWidth" placeholder="请输入路面宽度(米)" />
          </el-form-item>


          <el-form-item label="面层厚度(厘米)" prop="surfaceThickness">
            <el-input v-number-only v-model="form.surfaceThickness" placeholder="请输入面层厚度(厘米)" />
          </el-form-item>

        <el-form-item label="设计时速" prop="surfaceThickness">
          <el-input v-number-only v-model.number="form.designSpeed" placeholder="设计时速（公里/小时）" />
        </el-form-item>


          <el-form-item label="抗震等级" prop="seismicGrade">


            <el-select v-model="form.seismicGrade" placeholder="请选择抗震等级">
              <el-option
                v-for="dict in dict.type.bridge_seismic_grade"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              ></el-option>
            </el-select>
          </el-form-item>


          <el-form-item label="路基设计洪水频率" prop="floodFrequency">

            <el-select v-model="form.floodFrequency" placeholder="请选择路基设计洪水频率">
              <el-option
                v-for="dict in dict.type.sys_segment_flood_frequency"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              ></el-option>
            </el-select>

          </el-form-item>

        <el-form-item label="修建年度" prop="constructionYear">

          <el-input v-number-only v-model="form.constructionYear" placeholder="请输入修建年度"></el-input>

        </el-form-item>




          <el-form-item label="断链类型" prop="brokenChainType">
            <el-select v-model="form.brokenChainType" placeholder="请选择断链类型">
              <el-option
                v-for="dict in dict.type.sys_segment_broken_chain_type"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              ></el-option>
            </el-select>
          </el-form-item>


          <el-form-item label="是否断头路段" prop="isDeadEnd">

            <el-select v-model="form.isDeadEnd" placeholder="请选择是否断头路段">
              <el-option
                v-for="dict in dict.type.sys_yes_no"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              ></el-option>
            </el-select>



          </el-form-item>


          <el-form-item label="是否渡口路段" prop="isFerrySection">
            <el-select v-model="form.isFerrySection" placeholder="请选择是否渡口路段">
              <el-option
                v-for="dict in dict.type.sys_yes_no"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              ></el-option>
            </el-select>

          </el-form-item>


          <el-form-item label="是否渡运车辆" prop="isVehicleFerry">

            <el-select v-model="form.isVehicleFerry" placeholder="请选择是否渡运车辆">
              <el-option
                v-for="dict in dict.type.sys_yes_no"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              ></el-option>
            </el-select>
          </el-form-item>


          <el-form-item label="是否长链" prop="isLongChain">


            <el-select v-model="form.isLongChain" placeholder="请选择是否长链">
              <el-option
                v-for="dict in dict.type.sys_yes_no"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              ></el-option>
            </el-select>
          </el-form-item>


          <el-form-item label="地貌" prop="terrain">


            <el-select v-model="form.terrain" placeholder="请选择地貌">
              <el-option
                v-for="dict in dict.type.sys_segment_terrain"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              ></el-option>
            </el-select>
          </el-form-item>


          <el-form-item label="省际出入口" prop="provincialBoundary">
            <el-select v-model="form.provincialBoundary" placeholder="请选择省际出入口">
              <el-option
                v-for="dict in dict.type.sys_segment_provincial_boundary"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              ></el-option>
            </el-select>

          </el-form-item>

        <el-row>
          <el-form-item label="备注" prop="remark">
            <el-input  v-model="form.remark" placeholder="请输备注" />
          </el-form-item>
        </el-row>



      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  listRouteSegments,
  getRouteSegments,
  delRouteSegments,
  addRouteSegments,
  updateRouteSegments,
  getTotalMileage
} from "@/api/system/routeSegments";
import Treeselect from "@riophae/vue-treeselect";
import "@riophae/vue-treeselect/dist/vue-treeselect.css";
// import {deptTreeSelect} from "@/api/system/user";


import {listMaintenanceSectionAll, syncMileage} from "@/api/system/maintenanceSection";
import { listAllRoute, routeCacheClear } from "@/api/system/route";
import PileInput from "@/views/system/route/pileinput.vue";
import {deptTreeSelect} from "@/api/tmpl";

export default {
  name: "RouteSegments",
  components: {PileInput, Treeselect},
  dicts: ['sys_route_type', 'sys_route_grade', 'sys_surface_type', 'sys_operation_state', 'sys_ramp', 'sys_yes_no', 'bridge_seismic_grade', 'sys_segment_flood_frequency', 'sys_segment_broken_chain_type', 'sys_segment_terrain', 'sys_segment_provincial_boundary'],
  data() {
    return {
      lanesOptions: ['2', '4', '6', '8'],
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: false,
      // 总条数
      total: 0,
      // 总养护里程
      totalMileage: 0,
      // 总统计里程
      roadSectionLength: null,
      // 养护子段管理表格数据
      routeSegmentsList: [],
      maintenanceSectionList: [],
      deptOptions: [],
      routeList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 50,
        state: '2',
        ownerUnit: null,
        lanesStr: null,
        dataRange: null,
        routeSegmentsName: null,
        maintenanceSectionId: null,
        baseRouteId: null,
        maintenanceUnit: null,
        roadSectionLength: null,
        roadGrade: null,
        mainLength: null,
        roadType: null,
        roadSurfaceType: null,
        lanes: null,
        openingTime: null,
        pileStart: null,
        pileEnd: null,
        placeStartName: null,
        placeEndName: null,
        unifiedMileagePileStart: null,
        unifiedMileagePileEnd: null,
        areaCode: null,
        isSingleExpressway: null,
        subgradeWidth: null,
        pavementWidth: null,
        surfaceThickness: null,
        designSpeed: null,
        seismicGrade: null,
        floodFrequency: null,
        constructionYear: null,
        brokenChainType: null,
        isDeadEnd: null,
        isFerrySection: null,
        isVehicleFerry: null,
        isLongChain: null,
        terrain: null,
        provincialBoundary: null
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        routeSegmentsName: [
          {required: true, message: "子段名称不能为空", trigger: "blur"}
        ],
        maintenanceSectionId: [
          {required: true, message: "养护路段不能为空", trigger: "blur"}
        ],
        baseRouteId: [
          {required: true, message: "路线不能为空", trigger: "blur"}
        ],
        roadGrade: [
          {required: true, message: "路线等级不能为空", trigger: "change"}
        ],
        roadSurfaceType: [
          {required: true, message: "路面类型不能为空", trigger: "change"}
        ],
        maintenanceUnit: [
          {required: true, message: "管养单位不能为空", trigger: "change"}
        ],
        roadType: [
          {required: true, message: "管养单位不能为空", trigger: "change"}
        ],
        lanes: [
          {required: true, message: "车道数不能为空", trigger: "blur"},
          {type: 'number', message: '请输入数字', trigger: 'blur'}
        ],
        state: [
          {required: true, message: '运营状态不能为空', trigger: "blur"}
        ],
        mainLength: [
          {required: true, message: '养护里程不能为空', trigger: "blur"}
        ],

      }
    };
  },
  watch: {
    'form.roadType':function (value){
      if (value !== 3){
        // this.form.ramp = 0
      }
    }
  },
  directives: {
    'number-only': {
      bind: function (el) {
        const input = el.querySelector('input');
        input.addEventListener('input', function () {
          const pattern = /^\d*\.?\d*$/;
          if (!pattern.test(this.value)) {
            this.value = this.value.slice(0, -1);
          }
        });
      }
    }
  },
  created() {
    this.getList();
    this.getDeptTree()
    this.getRouteList()
    this.getMaintenanceSection()
  },
  methods: {
    onPileChange(type) {
      if (type === 1)
        if (!isNaN(this.form.pileEnd) && !isNaN(this.form.pileStart)){
          this.form.roadSectionLength = (this.form.pileEnd - this.form.pileStart).toFixed(2)
          this.form.mainLength = this.form.roadSectionLength
        }

      if (type === 2)
        if (!isNaN(this.form.unifiedMileagePileEnd) && !isNaN(this.form.unifiedMileagePileStart))
          this.form.roadSectionLength = (this.form.unifiedMileagePileEnd - this.form.unifiedMileagePileStart).toFixed(2)
          this.form.mainLength = this.form.roadSectionLength
    },
    /** 查询养护子段管理列表 */
    getList() {
      this.loading = true;

      let tempQueryParams = {...this.queryParams}

      if (this.queryParams.lanesStr){
        tempQueryParams.lanesStr = this.queryParams.lanesStr.join(',')
      }

      if (this.queryParams.dataRange){

        console.log(this.queryParams.dataRange)
        tempQueryParams.startOpeningTime =  this.queryParams.dataRange[0]
        tempQueryParams.endOpeningTime =   this.queryParams.dataRange[1]

      }

      listRouteSegments(tempQueryParams).then(response => {
        this.routeSegmentsList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
      this.getTotalMileage();
    },
    /** 查询总养护里程 */
    getTotalMileage() {

      let tempQueryParams = {...this.queryParams}

      if (this.queryParams.lanesStr){
        tempQueryParams.lanesStr = this.queryParams.lanesStr.join(',')
      }


      getTotalMileage(tempQueryParams).then(response => {
        // this.totalMileage = response.data;

        this.totalMileage = response.data.mainLength;
        this.roadSectionLength = response.data.roadSectionLength;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        routeSegmentsId: null,
        routeSegmentsName: null,
        maintenanceSectionId: null,
        baseRouteId: null,
        maintenanceUnit: null,
        roadSectionLength: null,
        roadGrade: null,
        roadType: null,
        ramp: null,
        roadSurfaceType: null,
        lanes: null,
        openingTime: null,
        pileStart: null,
        pileEnd: null,
        placeStartName: null,
        placeEndName: null,
        createBy: null,
        createdTime: null,
        updatedBy: null,
        updatedTime: null,
        state: null,
        areaCode: null,
        isSingleExpressway: null,
        subgradeWidth: null,
        pavementWidth: null,
        surfaceThickness: null,
        designSpeed: null,
        seismicGrade: null,
        floodFrequency: null,
        constructionYear: null,
        brokenChainType: null,
        isDeadEnd: null,
        isFerrySection: null,
        isVehicleFerry: null,
        isLongChain: null,
        terrain: null,
        provincialBoundary: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.routeSegmentsId)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加养护子段管理";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const routeSegmentsId = row.routeSegmentsId || this.ids
      getRouteSegments(routeSegmentsId).then(response => {
        console.log(response.data)
        this.form = response.data;
        this.open = true;
        this.title = "修改养护子段管理";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.routeSegmentsId != null) {
            updateRouteSegments(this.form).then(res => {
              if (res.code === 200) {
                routeCacheClear()
                this.$modal.msgSuccess("修改成功");
                this.open = false;
                this.getList();
                syncMileage()
              }
            });
          } else {
            addRouteSegments(this.form).then(res => {
              if (res.code === 200) {
                this.$modal.msgSuccess("新增成功");
                this.open = false;
                this.getList();
                syncMileage()
              }
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const routeSegmentsIds = row.routeSegmentsId || this.ids;
      let str = row.routeSegmentsName?"名称为"+row.routeSegmentsName:"选中"
      this.$modal.confirm('是否确认删除' + str + '的数据？').then(function () {
      // this.$modal.confirm('是否确认删除养护子段管理 名称为"' + row.routeSegmentsName + '"的数据项？').then(function () {
        return delRouteSegments(routeSegmentsIds);
      }).then((res) => {
        if (res.code === 200) {
          routeCacheClear()
          this.getList();
          syncMileage()
          this.$modal.msgSuccess("删除成功");
        }
      }).catch(() => {
      });
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('system/routeSegments/export', {
        ...this.queryParams
      }, `routeSegments_${new Date().getTime()}.xlsx`)
    },
    /** 查询部门下拉树结构 */
    getDeptTree() {
      return deptTreeSelect({types:101}).then(response => {
        this.deptOptions = response.data;
      });
    },
    getMaintenanceSection() {
      listMaintenanceSectionAll().then(res => {
        this.maintenanceSectionList = res.data
      })
    },
    getRouteList() {
      listAllRoute().then(res => {
        this.routeList = res.data
      })
    }
  }
};
</script>
<style scoped>
.app-container form:first-child .el-select,
.app-container form:nth-child(2) .el-select,
.app-container form:nth-child(2) ::v-deep .el-form-item__content,
.app-container form:first-child ::v-deep .el-form-item__content {
  width: 240px;
}
.app-container form:nth-child(1) ::v-deep .vue-treeselect__control,
.app-container form:nth-child(2) ::v-deep .vue-treeselect__control{
  height: auto;
  line-height: 30px;
}
.app-container  form:first-child  .el-form-item:last-child ::v-deep .el-form-item__content {
  width: auto;
}
.el-dialog .el-input,
.el-dialog .pile-input,
.el-dialog .el-select,
.el-dialog .el-date-editor,
.el-dialog .el-textarea,
.el-dialog .vue-treeselect {
  width: 350px;
}
.tableDiv {
  background-color: white;
  padding-bottom: 10px;
}
</style>
