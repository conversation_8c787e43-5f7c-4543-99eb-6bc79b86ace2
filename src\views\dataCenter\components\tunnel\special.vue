<template>
  <div class="tunnel-special">
    <div class="special-c">
      <!-- <map-view :padding="[50, 0, 50, -250]" ref="mpRef"></map-view> -->
      <AMap ref="ampRef"></AMap>
    </div>
    <section class="special-l">
      <CockpitCard title="运营隧道信息" :w="isBig ? '10vw' : '20vw'" :h="isBig ? 'calc(40vh - 5px)' : 'calc(40vh + 5px)'"
        :class="isBig ? 'mb-2' : 'mb-3'" :isDtl="false">
        <div class="bridge-info">
          <span>隧道总数量</span>
          <span v-for="(item, index) in bridgeNum" :key="index" class="info-num">
            {{ item }}
          </span>
          <span>座</span>
        </div>
        <Echarts :option="btOption" v-if="btOption" height="16vh" key="btKey" />
        <div class="divider"></div>
        <Echarts :option="sOption" v-if="sOption" height="16vh" key="sKey" />
      </CockpitCard>
      <CockpitCard title="养护数量趋势" :w="isBig ? '10vw' : '20vw'" :h="isBig ? 'calc(19vh - 5px)' : 'calc(19vh - 3px)'"
        :isDtl="false">
        <Echarts :option="tcOption" v-if="tcOption" height="18vh" key="tcKey" />
      </CockpitCard>
      <CockpitCard title="长大隧道目录" :w="isBig ? '10vw' : '20vw'" :h="isBig ? 'calc(19vh - 5px)' : 'calc(19vh - 3px)'"
        :class="isBig ? 'mb-2' : 'mb-3'" :isDtl="false">
        <Echarts :option="mlOption" v-if="mlOption" height="19vh" key="jkKey" />
      </CockpitCard>
    </section>
    <section class="special-r">
      <div class="info">
        <div>
          <CockpitCard title="管理处隧道分布情况" :w="isBig ? '10vw' : '20vw'" :isDtl="false"
            :h="isBig ? 'calc(57vh - 20px)' : 'calc(57vh - 6px)'" :class="isBig ? 'mb-2' : 'mb-3'">
            <Tables :columns="columns" :data="tableData" :isScroll="false"></Tables>
          </CockpitCard>
          <CockpitCard title="隧道经常检查" :w="isBig ? '10vw' : '20vw'" h="calc(26vh - 5px)" :class="isBig ? 'mb-2' : 'mb-3'"
            :isDtl="false">
            <Echarts :option="edOption" v-if="edOption" height="25vh" key="edKey" />
          </CockpitCard>
        </div>
      </div>
    </section>
  </div>
</template>

<script>
import * as echarts from 'echarts'
import { isBigScreen } from '../../util/utils';
import CockpitCard from '../cockpitCard.vue';
import Echarts from '../echarts/echarts.vue';
import Tables from "../tables.vue";
import MapView from '../mapView.vue';
import AMap from '@/components/Map/aMap.vue'
import { getTunnelList, getMaintenanceQuantity, findByDoubleTime } from "@/api/cockpit/index";

export default {
  name: 'Special',
  components: {
    CockpitCard,
    Echarts,
    Tables,
    MapView,
    AMap
  },
  props: {
    statisticsData: {
      type: Object,
      default: () => { }
    }
  },
  data() {
    return {
      map: null,
      isBig: isBigScreen(),
      bridgeNum: [],
      btOption: null,
      sOption: null,
      tcOption: null, // 通车隧道趋势
      columns: [
        {
          label: "序号",
          prop: "index",
          width: 40,
        },
        {
          label: "管理处",
          prop: "managementMaintenanceName",
        },
        {
          label: "特长隧道",
          prop: "superMajorTunnel",
          width: isBigScreen() ? 65 : 60,
        },
        {
          label: "长隧道",
          prop: "greatTunnel",
          width: 50,
        },
        {
          label: "中隧道",
          prop: "mediumTunnel",
          width: 50,
        },
        {
          label: "短隧道",
          prop: "smallTunnel",
          width: 50,
        },
      ],
      tableData: [],
      edOption: null,
      mlOption: null,
      obj: {
        "deptIds": [],
        "id": "1816037501012348929",
        icon: require('@/assets/cockpit/tunnel-icon.png'),
        "paramsDTO": {
          "precisionParams": {
            "base.length_classification": "1"
          }
        }
      },
      year: new Date().getFullYear(),
      arr: [
        {
          typeCount: 119,
          typeLabel: '特长隧道'
        },
        {
          typeCount: 428,
          typeLabel: '长隧道'
        },
        {
          typeCount: 316,
          typeLabel: '中隧道'
        },
        {
          typeCount: 538,
          typeLabel: '短隧道'
        }
      ],
    }
  },
  async mounted() {
    await this.initTableData();
    this.$nextTick(() => {
      this.initMap();
    })
    // 初始化隧道类型图表
    this.initBtECharts();
    this.initSECharts();
    // 通车隧道趋势-折线图
    this.initTcEcharts();
    // 隧道养护经费-柱状图
    this.initEdEcharts();
    this.initBarsCharts();
  },
  destroyed() {
    if (this.$refs.animationDelaympRef) {
      this.$refs.mpRef.removeVector('隧道');
    }
  },
  methods: {
    initMap() {
      this.$nextTick(() => {
        if (this.$refs.ampRef) {
          this.$refs.ampRef.setVector('隧道', this.obj)
        }
      })
    },
    // 隧道类型-饼图
    initBtECharts() {
      var outr = this.isBig ? "83%" : "79%";
      var inr = this.isBig ? '74%' : '74%';
      var size = 13;
      var numberdata = []
      var titledata = []
      var color = []
      const colorObj = {
        '特长隧道': '#01FBEF',
        '长隧道': '#00AEFF',
        '中隧道': '#0154FB',
        '短隧道': '#518BFF',
      }
      for (let index = 0; index < this.arr.length; index++) {
        const el = this.arr[index];
        if (colorObj[el.typeLabel]) {
          numberdata.push(el.typeCount)
          titledata.push(el.typeLabel)
          color.push(colorObj[el.typeLabel])
        }
      }

      let sum = numberdata.reduce((a, b) => a + b, 0);
      sum = sum.toString();
      let sumArray = sum ? sum.split('') : null;
      this.bridgeNum = ['1','3','8','5'] || sumArray ? sumArray : ['0'];
      let title = '隧道长度';
      this.btOption = this.initPieCharts(outr, inr, size, numberdata, titledata, color, title);
    },
    // 主桥上部结构形式-饼图
    initSECharts() {
      var outr = this.isBig ? "83%" : "79%";
      var inr = this.isBig ? '74%' : '74%';
      var size = 13;
      var numberdata = [];
      var titledata = [];
      let color = [];
      const colorObj = {
        '1类': '#FF9803',
        '2类': '#00DFFF',
        '3类': '#008CFF',
        '4类': '#0154FB',
        '5类': '#518BFF',
        '未评定': '#9151FF',
      }
      let arr = [
        {
          typeCount: 758,
          typeLabel: '1类'
        },
        {
          typeCount: 623,
          typeLabel: '2类'
        },
        {
          typeCount: 20,
          typeLabel: '3类'
        },
        {
          typeCount: 0,
          typeLabel: '4类'
        },
        {
          typeCount: 0,
          typeLabel: '5类'
        },
      ]
      for (let index = 0; index < arr.length; index++) {
        const el = arr[index];
        if (colorObj[el.typeLabel]) {
          numberdata.push(el.typeCount)
          titledata.push(el.typeLabel)
          color.push(colorObj[el.typeLabel])
        }
      }

      // if(!titledata.includes('5类')){
      //   titledata.push('5类')
      //   numberdata.push(0)
      // }

      // let index4=titledata.findIndex(element => element =='4类');
      // let index3=titledata.findIndex(element => element =='3类');
      // let index2=titledata.findIndex(element => element =='2类');
     
      // numberdata[index4]=0;
      // numberdata[index2]=486;
      // numberdata[index3]=8;

     
      let wrap = this.isBig ? '\n\n\n\n\n' : '\n\n\n';
      let title = `最新技术${wrap}状况`;
      this.sOption = this.initPieCharts(outr, inr, size, numberdata, titledata, color, title);
    },
    // 公共的饼图创建
    initPieCharts(outr = 135, inr = 125, size = 13, numberdata = [], titledata = [], color = [], title = '') {
      var total = 0;
      //计算总和
      for (var i = 0; i < numberdata.length; i++) {
        total += Number(numberdata[i]);
      }
      let placeHolderStyle = {
        normal: {
          label: {
            show: false,
          },
          labelLine: {
            show: false,
          },
          color: 'rgba(0, 0, 0, 0)',
          borderColor: 'rgba(0, 0, 0, 0)',
          borderWidth: 0,
        },
      };

      var data = [];
      for (var i = 0; i < numberdata.length; i++) {
        data.push(
          {
            value: numberdata[i],
            name: titledata[i],
            itemStyle: {
              normal: {
                borderWidth: 5,
                shadowBlur: 2,
                borderColor: color[i],
                shadowColor: color[i],
                color: color[i],
              },
            },
          },
          {
            value: total / 30,
            name: '',
            itemStyle: placeHolderStyle,
          }
        );
      }

      var seriesObj = [
        {
          type: "pie",
          zlevel: 0,
          silent: true,
          center: ['30%', '50%'], //此处调整圆环的位置
          radius: ["93%", "96%"],
          hoverAnimation: false,
          color: "rgba(0,62,122,0.9)",
          label: {
            normal: {
              show: false,
            },
          },
          labelLine: {
            normal: {
              show: false,
            },
          },
          data: [1],
        },
        {
          name: '',
          type: 'pie',
          clockWise: false,
          startAngle: '90',
          center: ['30%', '50%'], //此处调整圆环的位置
          radius: [outr, inr], //此处可以调整圆环的大小
          hoverAnimation: false,
          itemStyle: {
            normal: {
              label: {
                show: false,
              },
              labelLine: {
                show: false,
              },
            },
          },
          data: data,
          animationType: 'scale',
          animationEasing: 'elasticOut',
          animationDelay: function (idx) {
            return idx * 50;
          },
        },
        {
          name: '',
          type: 'pie',
          center: ['30%', '50%'],
          radius: ['49%', '49%'],
          itemStyle: {
            color: 'transparant',
          },
          startAngle: '90',
          data: [
            {
              value: total,
              name: '',
              label: {
                normal: {
                  show: true,
                  formatter: '{b| ' + title + '}',
                  rich: {
                    b: {
                      color: 'rgb(255,255,255)',
                      fontSize: this.isBig ? size * 3 : size,
                      lineHeight: this.isBig ? 12 : 5,
                      fontWeight: 'bold',
                    },
                  },
                  textStyle: {
                    fontSize: 28,
                    fontWeight: 'bold',
                  },
                  position: 'center',
                },
              },
            },
          ],
        },
      ];

      let option = {
        backgroundColor: 'rgba(0,0,0,0)',
        tooltip: {
          show: false,
        },
        legend: {
          show: true,
          // right: this.isBig ? '6%' : '5%',
          left: '57%',
          y: 'center',
          icon: "circle",
          itemWidth: this.isBig ? 30 : 3, // 设置宽度
          itemHeight: this.isBig ? 30 : 3, // 设置高度
          itemGap: this.isBig ? 25 : 0,
          formatter: (name) => {
            let d = data.filter(v => v.name == name);
            let num = d[0].value;
            return " {title|" + name + "：}" + "{num|" + num + "} " + "{unit|座}";
          },
          textStyle: {
            color: '#fff',
            fontSize: this.isBig ? 30 : size,
            rich: {
              title: {
                color: '#fff',
                fontSize: this.isBig ? 30 : size - 1,
                padding: [3, 0],
              },
              num: {
                color: '#fff',
                fontSize: this.isBig ? 30 : size,
              },
              unit: {
                color: '#fff',
                fontSize: this.isBig ? 28 : size - 1,
              }
            }
          },
        },
        toolbox: {
          show: false,
        },
        series: seriesObj,
      };
      return option;
    },
    initBarsCharts() {
      let xData = this.tableData.map(v => v.managementMaintenanceName.replace("管理处", ""));
      let jkData = this.tableData.map(v => v.inLongTunnelDirectory);
      let mlSColor = '#76FF30';
      let mlEColor = 'rgba(118,255,48,0.1)';
      this.mlOption = this.initBarCharts(xData, jkData, mlSColor, mlEColor, ['长大隧道']);
    },
    initBarCharts(xData = [], data = [], startColor = '', endColor = '', legend = []) {
      let option = {
        backgroundColor: "rgba(0,0,0,0)",
        grid: {
          left: "3%",
          right: "4%",
          top: "15%",
          bottom: "1%",
          containLabel: true,
        },
        legend: {
          show: legend.length > 0 ? true : false,
          data: legend,
          x: 'center',
          y: '2%',
          itemWidth: this.isBig ? 20 : 10,
          itemHeight: this.isBig ? 20 : 10,
          itemGap: this.isBig ? 20 : 10,
          textStyle: {
            color: '#ffffff',
            fontSize: this.isBig ? 25 : 14,
          },
        },
        xAxis: {
          data: xData,
          axisLine: {
            lineStyle: {
              color: "rgba(110,112,121,0.5)",
            },
          },
          axisLabel: {
            color: "#8B8C8C",
            fontSize: this.isBig ? 25 : 14,
            rotate: 40,
            interval: false,
          },
        },
        yAxis: {
          name: "",
          nameTextStyle: {
            color: "#999999",
            fontSize: 13,
          },
          axisLabel: {
            color: "#999999",
            fontSize: this.isBig ? 25 : 14,
            formatter: '{value}'
          },
          splitLine: {
            show: true,
            lineStyle: {
              color: "rgba(110,112,121,0.5)",
            },
          },
          splitArea: {
            show: false,
          },
        },
        series: [
          {
            name: legend.length ? legend[0] : "",
            type: "bar",
            barWidth: this.isBig ? 12 : 6,
            itemStyle: {
              normal: {
                color: new echarts.graphic.LinearGradient(
                  0,
                  0,
                  0,
                  1,
                  [
                    {
                      offset: 0,
                      color: startColor,
                    },
                    {
                      offset: 0.8,
                      color: endColor,
                    },
                  ],
                  false
                ),
              },
            },
            data,
          },
        ],
      };
      return option;
    },
    async initTcEcharts() {
      getMaintenanceQuantity({dataType:194}).then(res =>{

        let list=[];
        if(res.data.length>10){
          list=res.data.slice(-10)
        }else{
          list=res.data
        }

        let xData = list.map(v => v.year);
        let yData = list.map(v => v.quantity);

        var fontColor = '#8B8C8C';
        this.tcOption = {
          backgroundColor: 'rgba(0,0,0,0)',
          grid: {
            left: '5%',
            right: '5%',
            top: '16%',
            bottom: '3%',
            containLabel: true
          },
          tooltip: {
            show: true,
            trigger: 'item'
          },
          legend: {
            show: false
          },
          xAxis: [
            {
              type: 'category',
              name: '',
              nameTextStyle: {
                color: "#999999",
                algin: 'right',
                fontSize: this.isBig ? 25 : 14,
                padding: [6, 0, 0, -12],
                verticalAlign: 'top',
              },
              boundaryGap: false,
              axisLabel: {
                color: fontColor,
                rotate: 35,
                interval: false,
                textStyle: {
                  fontSize: this.isBig ? 25 : 14,
                },
              },
              axisLine: {
                show: true,
                lineStyle: {
                  color: 'rgba(28,255,188,0.04)'
                }
              },
              axisTick: {
                show: false,
              },
              splitLine: {
                show: false,
                lineStyle: {
                  color: '#195384'
                }
              },
              data: xData
            }
          ],
          yAxis: [
            {
              type: 'value',
              name: '',
             
              axisLabel: {
                formatter: '{value}座',
                textStyle: {
                  color: '#999999',
                  fontSize: this.isBig ? 25 : 14,
                }
              },
              axisLine: {
                lineStyle: {
                  color: '#27b4c2'
                }
              },
              axisTick: {
                show: false,
              },
              splitLine: {
                show: true,
                lineStyle: {
                  color: 'rgba(110,112,121,0.5)',
                }
              },
              splitArea: {
                show: false,
              }
            },
          ],
          series: [
            {
              name: '',
              type: 'line',
              smooth: true,
              symbol: 'emptyCircle',
              symbolSize: 8,
              emphasis: {
                focus: 'series'
              },
              itemStyle: {
                normal: {
                  color: '#1CFFBC',
                  lineStyle: {
                    width: 1
                  },
                  areaStyle: {
                    color: new echarts.graphic.LinearGradient(0, 1, 0, 0, [{
                      offset: 0,
                      color: 'rgba(28,255,188,0.2)'
                    }, {
                      offset: 1,
                      color: 'rgba(28,255,188,0.9)'
                    }]),
                  }
                }
              },
              markPoint: {
                itemStyle: {
                  normal: {
                    color: 'red'
                  }
                }
              },
              data: yData
            },
          ]
        };

      })
    },
    // 隧道养护经费
    async initEdEcharts() {
      let xData = ["保山", "大理", "昆明东", "昆明西", "丽江", "临沧", "普洱", "曲靖", "文山", "西双版纳", "昭通", "红河"];
      let lastInspectedCounts = []
      let nextInspectedCounts = []
      const res = await findByDoubleTime()
      if (res.code === 200 && res.data) {
        xData = res.data.map(e => {
          return e.managementMaintenanceName.replace("管理处", "");
        })
        lastInspectedCounts = res.data.map(e => {
          return e.lastInspectedCount
        })
        nextInspectedCounts = res.data.map(e => {
          return e.nextInspectedCount
        })
      }
      this.edOption = {
        backgroundColor: "rgba(0,0,0,0)",
        tooltip: {
          trigger: "axis",
          axisPointer: {
            type: "shadow",
          },
        },
        legend: {
          data: ["上月", "本月"],
          x: 'center',
          textStyle: {
            color: "#fff",
            fontSize: this.isBig ? 24: 13,
          },
          itemWidth: this.isBig ? 20:  10,
          itemHeight: this.isBig ? 20:  10,
          itemGap: 25,
        },
        grid: {
          left: "3%",
          right: "4%",
          top: "10%",
          bottom: "2%",
          containLabel: true,
        },
        xAxis: [
          {
            type: "category",
            data: xData,
            axisLine: {
              show: true,
              lineStyle: {
                color: "#063374",
                width: 1,
                type: "solid",
              },
            },
            axisTick: {
              show: false,
            },
            axisLabel: {
              show: true,
              interval: false,
              rotate: 40,
              textStyle: {
                color: "#8B8C8C",
                fontSize: this.isBig ? 25 : 14,
              },
            },
          },
        ],
        yAxis: [
          {
            type: "value",
            axisLabel: {
              formatter: "{value}",
              textStyle: {
                color: "#999999",
                fontSize: this.isBig ? 25 : 14,
              },
            },
            axisTick: {
              show: false,
            },
            axisLine: {
              show: false,
              lineStyle: {
                color: "#00c7ff",
                width: 1,
                type: "solid",
              },
            },
            splitLine: {
              lineStyle: {
                color: "rgba(110,112,121,0.5)",
              },
            },
            splitArea: {
              show: false,
            },
          },
        ],
        series: [
          {
            name: "上月",
            type: "bar",
            data: lastInspectedCounts,
            barWidth: this.isBig ? 12 : 6, //柱子宽度
            // barGap: 1, //柱子之间间距
            itemStyle: {
              normal: {
                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                  {
                    offset: 0,
                    color: "#219BFF",
                  },
                  {
                    offset: 1,
                    color: "#005193",
                  },
                ]),
                opacity: 1,
              },
            },
          },
          {
            name: "本月",
            type: "bar",
            data: nextInspectedCounts,
            barWidth: this.isBig ? 12 : 6,
            // barGap: 1,
            itemStyle: {
              normal: {
                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                  {
                    offset: 0,
                    color: "#76FF31",
                  },
                  {
                    offset: 1,
                    color: "#007a55",
                  },
                ]),
                opacity: 1,
              },
            },
          },

        ],
      };
    },
    // 表格
    async initTableData() {
      try {
        let res = await getTunnelList()
        this.tableData = res?.data || []
      } catch (error) {
        this.tableData = []
      }
    }
  }
}
</script>

<style lang="scss" scoped>
@import "@/assets/styles/utils.scss";

.tunnel-special {
  width: 100%;
  height: 100%;
  position: relative;
  margin-top: vwpx(15px);

  .special-c {
    width: 100%;
    height: 91vh;
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
  }

  .mb-2 {
    margin-bottom: vwpx(20px);
  }

  .mb-3 {
    margin-bottom: vwpx(30px);
  }

  .ml-2 {
    margin-left: vwpx(20px);
  }

  .special-l {
    position: absolute;
    left: vwpx(30px);
    top: 0;

    .bridge-info {
      width: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
      margin: vwpx(20px) 0;

      span {
        font-family: Source Han Sans, Source Han Sans;
        font-weight: 400;
        font-size: vwpx(32px);
        color: #01FBEF;
        text-align: left;
        font-style: normal;
        text-transform: none;
        margin: 0 vwpx(10px);
      }

      .info-num {
        background: rgba(4, 17, 48, 0.4);
        box-shadow: inset 0px 0px 6px 0px #0154FB;
        border: 1px solid #009DFF;
        margin: 0 vwpx(5px);
        padding: vwpx(6px) vwpx(12px);
        color: #FFFFFF;
        font-size: vwpx(34px);
      }
    }

    .divider {
      width: 90%;
      margin: vwpx(20px) 5%;
      border: 1px dotted rgba(156, 189, 255, 0.5);
    }
  }

  .special-r {
    position: absolute;
    right: vwpx(30px);
    top: 0;

    .info {
      display: flex;
    }
  }
}
</style>