<template>
  <div class="static">
    <span></span>
    <span>运营中</span>
  </div>
</template>

<script>
export default {
  name: 'static',
  data() {
    return {
      msg: 'static'
    }
  },
  methods: {
    getNum(num) {
      let arr = []
      for (let i = 0; i < num; i++) {
        arr.push(i)
      }
      return arr
    }
  }
}
</script>

<style lang="scss" scoped>
@import "@/assets/styles/utils.scss";

.static {
  width: vwpx(152px);
  height: vwpx(56px);
  background: rgba(8, 24, 41, 0.3);
  box-shadow: inset 0px 0px 5px 0px #1CFFBC;
  border-radius: vwpx(8px);
  border: 1px solid #1CFFBC;
  margin: 0 vwpx(20px);

  display: flex;
  justify-content: center;
  align-items: center;

  span:first-child {
    display: block;
    width: vwpx(16px);
    height: vwpx(16px);
    background: #1CFFBC;
    border-radius: 50%;
    margin: 0 5px;
  }

  span:last-child {
    font-family: <PERSON> YaHei, Microsoft YaHei;
    font-weight: 400;
    font-size: vwpx(28px);
    color: #1CFFBC;
    text-align: left;
    // font-style: normal;
    // text-transform: none;
    letter-spacing: 0px;
  }
}
</style>