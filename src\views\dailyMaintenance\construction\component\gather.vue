<template>
  <div class="app-container maindiv">
    <el-row :gutter="15">
      <el-tabs v-model="activeName">
        <el-tab-pane label="人机料信息采集" name="1">
        </el-tab-pane>
        <el-tab-pane label="保通信息采集" name="2">
        </el-tab-pane>
        <el-tab-pane label="安全信息采集" name="3"></el-tab-pane>
      </el-tabs>
      <el-col :span="24" :xs="24">
        <!--数据表格开始-->
        <div class="tableDiv">
          <el-table v-adjust-table stripe size="mini" height="200px"
                    style="width: 100%" v-loading="loading" border :data="dataList">
            <el-table-column fixed label="序号" type="index" width="50"></el-table-column>
            <template v-for="column in columns">
              <el-table-column :label="column.label"
                               v-if="column.visible"
                               align="center"
                               :prop="column.field">
                <template slot-scope="scope">
                  <dict-tag v-if="column.dict" :options="dict.type[column.dict]" :value="scope.row.type"/>
                  <span v-else-if="column.isTime">{{ parseTime(scope.row[column.field], '{y}-{m}-{d}') }}</span>
                    <span v-else>{{ scope.row[column.field] }}</span>
                </template>
              </el-table-column>
            </template>
          </el-table>
          <pagination
              v-show="total>0"
              :total="total"
              :page.sync="queryParams.pageNum"
              :limit.sync="queryParams.pageSize"
              @pagination="getList"
          />
        </div>
        <!--数据表格结束-->
      </el-col>
    </el-row>
  </div>
</template>
<script>
export default {
  data() {
    return {
      activeName: '1',
      loading: false,
      queryParams: {
        pageNum: 1,
        pageSize: 50,
      },
      // 列信息
      columns: [
        {key: 0, field: 'field1', label: `所属单位`, visible: true},
        {key: 1, field: 'field2', label: `设备类型`, visible: true},
        {key: 2, field: 'field3', label: `设备名称`, visible: true},
        {key: 3, field: 'field4', label: `设备编码`, visible: true},
        {key: 4, field: 'field5', label: `设备状态`, visible: true},
        {key: 5, field: 'field6', label: `规格型号`, visible: true},
        {key: 5, field: 'field7', label: `单位`, visible: true},
        {key: 5, field: 'field8', label: `库存数量`, visible: true},
        {key: 5, field: 'field9', label: `存放地点`, visible: true},
        {key: 5, field: 'field10', label: `负责人`, visible: true},
        {key: 5, field: 'field11', label: `负责人电话`, visible: true},
        {key: 5, field: 'field12', label: `备注`, visible: true}

      ],
      // 表格数据
      dataList: [
        {field1: '1', field2: '101'},
        {field1: '1', field2: '101'},
        {field1: '2', field2: '101'},
        {field1: '3', field2: '101'},
        {field1: '4', field2: '101'}
      ],
      // 总条数
      total: 0,
    }
  },
  methods: {

  }
}
</script>
<style scoped lang="scss">

</style>
<style lang="scss" scoped>
@import "@/assets/styles/business.scss";
</style>
