<template>
  <el-container>
    <el-header>
      <el-form
          :model="form"
          :inline="true"
          label-position="right"
          label-suffix="："
          ref="tigaForm"
          label-width="85px">
        <el-form-item label="监测类型" prop="monitorType">
          <el-select
              v-model="form.monitorType"
              placeholder="请选择监测类型"
              @change="changeMonitorType"
              no-data-text="无数据"
              value-key="content"
              size="small"
          >
            <el-option
                v-for="item in monitorTypeList"
                :key="item.code"
                :label="item.content"
                :value="item">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="监测内容" prop="monitorContent">
          <el-select
              v-model="form.monitorContent"
              default-first-option
              placeholder="请选择监测内容"
              @change="changeMonitorContent"
              no-data-text="无数据"
              value-key="content"
              size="small"
          >
            <el-option
                v-for="item in monitorContentList"
                :key="item.code"
                :label="item.content"
                :value="item">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="监测位置" prop="monitorLocation">
          <el-select
              v-model="form.monitorLocation"
              default-first-option
              placeholder="请选择监测位置"
              @change="changeMonitorLocation"
              no-data-text="无数据"
              value-key="content"
              size="small"
          >
            <el-option
                v-for="item in monitorLocationList"
                :key="item.code"
                :label="item.content"
                :value="item">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="传感器" prop="sensorId">
          <el-select
              v-model="form.sensorId"
              placeholder="请选择传感器"
              @change="changeSensor"
              no-data-text="无数据"
              value-key="sensorId"
              size="small"
          >
            <el-option
                v-for="item in sensorInfoList"
                :key="item.code"
                :label="item.sensorInstallCode"
                :value="item">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="具体类型" prop="specificMonitorType">
            <el-select
                    class="selects"
                    v-model="specificMonitorContentIds"
                    multiple
                    filterable
                    collapse-tags
                    no-data-text="无数据"
                    style="width: 200px"
                    placeholder="请选择"
                    size="small"
                    value-key="specificMonitorTypeId"
                    :disabled="isDraw"
            >
                <el-option
                        v-for="item in specificMonitorTypeList"
                        :label="item.specificMonitorTypeName"
                        :key="item.specificMonitorTypeId"
                        :value="item.specificMonitorTypeId"
                >
                </el-option>
            </el-select>
        </el-form-item>
        <el-form-item label="时间范围" prop="timerange">
          <el-date-picker
              size="medium"
              style="width:200px"
              type="datetimerange"
              v-model="form.timerange"
              value-format="yyyy-MM-dd HH:mm:ss"
              format="yyyy-MM-dd HH:mm:ss"
              @on-change="statusDateChange"
              :transfer="true"

          ></el-date-picker>
        </el-form-item>
        <el-form-item>
          <el-button
              type="primary"
              @click="draw"
              size="small"
              :loading="loading">
            <span>绘制</span>
          </el-button>
        </el-form-item>
        <el-form-item>
          当前选择结构物为：{{currentStructure.label}}
        </el-form-item>
      </el-form>
    </el-header>
    <el-main>
      <el-form
          :inline="true"
          label-position="right"
          label-suffix="："
          label-width="110px"
      >
        <el-form-item label="采样频率(Hz)">
          <el-input
              v-model="sampleFreq"
              type="number"
              size="small"
              style="width: 60px"
             ></el-input>
        </el-form-item>
      </el-form>
      <div id="myChart3" v-loading="loading" style="width: 100%; height: 400px; overflow:hidden"></div>
    </el-main>
  </el-container>
</template>

<script>
import {
  getSensorByDataCode, getDataProcess
} from "@/api/jgjc/dataAnalysis/customize/index.js";
import * as echarts from "echarts";
export default {
  name: "Spectrum",
  props: ['monitorTypeList', 'currentStructure', 'isDraw'],
  emits: ['changeDraw'],
  data() {
    return {
      isZD: false,
      sampleFreq: 50,
      processMethod: "振动信号频谱分析",
      monitorContentList: [],
      monitorLocationList: [],
      sensorInfoList: [],
      form: {
        monitorType: '',
        monitorContent: '',
        monitorLocation: '',
        sensorId: '',
        timerange: ''
      },
      sensorInfo: {},
      specificMonitorContentIds: [],
      specificMonitorTypeList: [],
      loading: false
    };
  },
  methods: {
    // 判断是否为长时查询
    async isLongCheck(isZD, isPreprocess) {
      // 计算时间差值
      let startTime = new Date(this.form.timerange[0]);
      let endTime = new Date(this.form.timerange[1]);
      let deltaTime = endTime.getTime() - startTime.getTime()
      let flag = false

      if(isZD){
        // 振动数据 1天+原始值 或 任意时间+预处理
        if(isPreprocess){
          flag = true
        }else{
          if(deltaTime > 86400000){
            flag = true
          }
        }
      }else {
        // 普通数据 6月+原始值 或 2月+预处理
        if(isPreprocess){
          if(deltaTime > 86400000 * 60){
            flag = true
          }
        }else{
          if(deltaTime > 86400000 * 180){
            flag = true
          }
        }
      }

      if(flag){
        const res = await this.$confirm('当前条件可能造成查询时间过长, 是否继续?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).catch(err => err)
        return res === 'confirm';
      }
      return true
    },
    // “绘制”按钮的方法
    async draw() {
      if(this.form.monitorType === '' ||
          this.form.monitorContent === '' ||
          this.form.monitorLocation === '' ||
          this.form.sensorId === '' ||
          this.form.timerange.length === 0 ||
          this.form.timerange[0] === ''
      ){
        this.$message({
          type: 'warning',
          message: "查询数据前请注意查询条件的完整性"
        });
        return;
      }
      if(this.sampleFreq <= 0){
        this.$message({
          type: "warning",
          message: "采样频率必须大于0",
        });
        return;
      }
      if(await this.isLongCheck(this.isZD, true)){
        this.loading = true
        let result = []
        try{
          const sensorCode = this.specificMonitorContentIds
          result = await getDataProcess({
            dataCode: this.form.sensorId,
            structureCode: this.currentStructure.structureCode,
            startTime: this.form.timerange[0],
            endTime: this.form.timerange[1],
            granularityNum: -1,
            granularityType: 0,
            analysisType: 2,
            sensorCode: sensorCode? sensorCode.join(',') : null,
          })
        }catch(err){
          this.loading = false
        }
        if (result.data[0]?.processedData?.errMsg) {
          this.$message.error(result.data[0].processedData?.errMsg);
        } else {
          this.setSpectrumChart("myChart3", result.data)
        }
        this.loading = false
      }else {
        // 用户取消本次查询
      }
    },
    // 绘制频谱分析图表
    setSpectrumChart(chartName, data) {
      let myChart = echarts.getInstanceByDom(document.getElementById(chartName))
      if(myChart !== undefined){
        myChart.dispose()
      }
      myChart = echarts.init(document.getElementById(chartName))
      data[0].processedData.xdataList = data[0].processedData.xdataList.map(e => (e * this.sampleFreq/2).toFixed(2))
      let option = {
        toolbox: {
          feature: {
            saveAsImage: {
              title:'保存'
            },
          },
        },
        // title: {
        //   text: title,
        // },
        tooltip: {
          trigger: "axis",
          formatter: function (params) {
            let newParams = ['频率:'+params[0].axisValue+"Hz"]
            params = params.map(e => (e.seriesName + ":" + e.data.toFixed(data[0].accuracy >= 0  ? data[0].accuracy : 4)))
            newParams = newParams.concat(params)
            return newParams.join('<br>');
          },
          textStyle:{
            align:'left'
          }
        },
        xAxis: {
          name: "频率/Hz",
          data: data[0].processedData.xdataList,
          nameLocation: "middle",
          nameGap: 25,
        },
        yAxis: {
          name: "功率谱密度",
          nameTextStyle: {
            fontWeight: "bold",
            fontSize: 18,
          },
          axisLabel: {
            formatter: function (value) {
              return value.toFixed(data[0].accuracy >= 0  ? data[0].accuracy : 4); // 2表示小数为2位
            },
          },
        },
        dataZoom: [
          {
            type: "inside",
          },
          {
            type: 'slider',
            xAxisIndex: 0,
            minSpan: 5,
            bottom: '10%'
          }
        ],
        grid: [
          {
            top: "20%",
            left: "5%",
            right: "5%",
            bottom: "25%",
            containLabel: true,
          },
        ],
        series: [],
        legend:{}
      };
      //加入多行数据
      for(let i = 0; i < data.length; i++){
        // push处理后的值
        option.series.push({
          type: "line",
          name: data[i].name+"("+data[i].unit+")²/Hz",
          showSymbol: false,
          data: data[i].processedData.ydataList,
        })
      }
      option && myChart.setOption(option);
      //自适应大小
      window.onresize = function () {
        myChart.resize();
      };
    },
    // 下面是一系列下拉框的change方法
    async changeMonitorType(e) {
      this.form.monitorType = e.content
      this.isZD = (
          e.content === "振动" ||
          e.content === "地震" ||
          e.content === "索力"
      )
      this.monitorContentList.splice(0)
      this.monitorLocationList.splice(0)
      this.sensorInfoList.splice(0)
      this.form.monitorContent = '';
      this.form.monitorLocation = '';
      this.form.sensorId = '';
      this.specificMonitorContentIds.splice(0)
      this.specificMonitorTypeList.splice(0)
      const monitorContentList = e.children;
      for (const monitorContent of monitorContentList) {
        this.monitorContentList.push(monitorContent)
      }
    },

    async changeMonitorContent(e) {
      this.form.monitorContent = e.content
      this.monitorLocationList.splice(0)
      this.sensorInfoList.splice(0)
      this.form.monitorLocation = '';
      this.form.sensorId = '';
      this.specificMonitorContentIds.splice(0)
      this.specificMonitorTypeList.splice(0)
      const monitorLocationList = e.children;
      for (const monitorLocation of monitorLocationList) {
        this.monitorLocationList.push(monitorLocation)
      }
    },

    async changeMonitorLocation(e) {
      this.form.monitorLocation = e.content
      this.sensorInfoList.splice(0)
      this.form.sensorId = '';
      this.specificMonitorContentIds.splice(0)
      this.specificMonitorTypeList.splice(0)
      const ref = await getSensorByDataCode({dataCode: e.code})
      const sensorInfoList = ref.data
      for (const sensorInfo of sensorInfoList) {
        this.sensorInfoList.push({
          code: sensorInfo.code,
          sensorId: sensorInfo.sensorId,
          sensorInstallCode: sensorInfo.dataCode,
          specificMonitorTypeId: sensorInfo.children.map(item => item.sensorCode),
          specificMonitorTypeName: sensorInfo.children.map(item => item.typeName),
        })
      }
    },

    async changeSensor(e) {
      this.form.sensorId = e.sensorInstallCode
      this.sensorInfo = e

      // 更新具体监测类型列表
      this.specificMonitorContentIds.splice(0)
      this.specificMonitorTypeList.splice(0)
      e.specificMonitorTypeId.forEach((value, index)=>{
          this.specificMonitorTypeList.push({
              specificMonitorTypeName: e.specificMonitorTypeName[index],
              specificMonitorTypeId: e.specificMonitorTypeId[index],
          })
      })
    },
    // 时间范围下拉框的change事件
    statusDateChange(e){
      this.form.timerange = e;
    },

    // 清空表单
    resetThisForm(){
      this.monitorContentList.splice(0)
      this.monitorLocationList.splice(0)
      this.sensorInfoList.splice(0)
      this.form.monitorType = '';
      this.form.monitorContent = '';
      this.form.monitorLocation = '';
      this.form.sensorId = '';
      this.specificMonitorContentIds.splice(0)
      this.specificMonitorTypeList.splice(0)
    }
  }
}

</script>

<style scoped>
.el-header{
  height: auto !important;
  padding: 0;
  color: #333;
  border-bottom: 1px solid #eee;
}
.el-form-item {
  display: inline-block;
  height: 40px;
  margin: 5px 0 5px 5px;
}
.el-form {
  text-align: left;
}
.el-button {
  margin-left: 5px;
}
/deep/.ivu-input {
  font-size: 14px !important;
}
/deep/.el-input__inner {
  padding: 0 0 0 10px !important;
}
.el-main{
  padding: 0;
  text-align: center;
}
/deep/.tags-select-input {
        .el-select__tags {
            white-space: nowrap;
            overflow: hidden;
            flex-wrap: nowrap !important;
        }
    }

/deep/.el-select__tags-text {
        display: inline-block;
        max-width: 75px;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
    }

</style>
