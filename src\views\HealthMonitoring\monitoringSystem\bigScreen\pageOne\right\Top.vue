<template>
  <Cards :title="title" w="100%" h="20vh" :isDtl="false" v-if="title === '经常检查情况'">
    <div class="box" v-loading="loading" element-loading-spinner="el-icon-loading"
      element-loading-background="rgba(0, 0, 0, 0.3)">
      <el-row :gutter="20">
        <el-col :span="12" :offset="0" v-for="(item, index) in (type === '桥梁' ? bridge : tunnel)" :key="index"
          class="set-margin">
          <div class="list-item">
            <i class="icon-right"></i>
            <span>{{ item.label }}：</span>
            <!-- <span>{{ bridgeData[item.prop] || '暂无' }}</span> -->
            <el-link :type="commonData[item.prop] === 1 ? 'success' : 'danger'" :underline="false" class="list-link">
              {{ commonData[item.prop] === 1 ? '正常' : '异常' }}
            </el-link>
          </div>
        </el-col>
      </el-row>
    </div>
  </Cards>
  <Cards v-else :title="title" w="100%" h="20vh" :isDtl="false">
    <Echarts v-if="option" :option="option" :height="'100%'" :key="optionKey" v-loading="loading"
      element-loading-text="加载中" element-loading-spinner="el-icon-loading"
      element-loading-background="rgba(0, 0, 0, 0.3)" />
  </Cards>
</template>

<script>
import { isBigScreen } from '../../../utils/utils.js';
import Cards from "../../components/cards.vue"
import Echarts from "../../components/echarts.vue"
// api
import { get } from '../../../utils/request.js'
import { fetchGet } from '../../../utils/api.js'

export default {
  name: 'Top',
  inject: ['iThis'],
  props: {},
  components: { Cards, Echarts },
  data() {
    return {
      loading: false,
      title: '经常检查情况',
      type: '桥梁',
      isBig: isBigScreen(),
      bridge: [
        {
          label: '翼墙(耳墙、侧墙)',
          prop: 'yiQiang',
        },
        {
          label: '锥坡、护坡',
          prop: 'zhuiPo',
        },
        {
          label: '桥台及基础(含冲刷)',
          prop: 'qiaoTai',
        },
        {
          label: '栏杆、护栏',
          prop: 'lanGan',
        },
        {
          label: '主梁',
          prop: 'zhuLiang',
        },
        {
          label: '支座',
          prop: 'zhiZuo',
        },
        {
          label: '桥路连接处(桥头搭板)',
          prop: 'qiaoLuLianJie',
        },
        {
          label: '伸缩缝',
          prop: 'shenSuoFeng',
        },
        {
          label: '桥面铺装',
          prop: 'qiaoMianPuZhuang',
        },
        {
          label: '标志、标线',
          prop: 'biaoZhi',
        },
        {
          label: '标排水系统',
          prop: 'paiShui',
        },
      ],
      bridgeData: {
        yiQiang: 1,
        zhuiPo: 1,
        qiaoTai: 1,
        lanGan: 1,
        zhuLiang: 1,
        zhiZuo: 1,
        qiaoLuLianJie: 1,
        shenSuoFeng: 1,
        qiaoMianPuZhuang: 1,
        biaoZhi: 1,
        paiShui: 1,
      },
      tunnel: [
        {
          label: '洞口',
          prop: 'dongkou',
        },
        {
          label: '洞门',
          prop: 'dongmen',
        },
        {
          label: '衬砌',
          prop: 'cunqie',
        },
        {
          label: '路面',
          prop: 'luMian',
        },
        {
          label: '检修道',
          prop: 'jianXiuDao',
        },
        {
          label: '排水设施',
          prop: 'paiShuiSheShi',
        },
        {
          label: '吊顶及各种预埋件',
          prop: 'neiZhuangShi',
        },
        {
          label: '标志、标线、轮廓标',
          prop: 'biaoZhi',
        },
        {
          label: '车行横道',
          prop: 'cheXingHengDao',
        },
        {
          label: '人行横道',
          prop: 'renXingHengDao',
        },
      ],
      tunnelData: {
        dongkou: 1,
        dongmen: 1,
        cunqie: 1,
        luMian: 1,
        jianXiuDao: 1,
        paiShuiSheShi: 1,
        neiZhuangShi: 1,
        biaoZhi: 1,
        cheXingHengDao: 1,
        renXingHengDao: 1,
      },
      commonData: {},
      option: null,
      optionKey: 'optionKey'
    }
  },
  async created() {
    this.loading = true
    this.init()
    if (this.title === '经常检查情况') {
      await this.fetchData()
    } else {
      this.setOption()
    }
    this.loading = false
  },
  methods: {
    init() {
      this.title = this.iThis.params.type === '边坡' ? '高程坐标变化' : '经常检查情况'
      this.type = this.iThis.params.type
      this.type === '桥梁' ?
        this.commonData = this.bridgeData :
        this.commonData = this.tunnelData;
    },
    async fetchData() {
      // const url = '/bigScreen/structure/getRegularInspection'
      const url = 'https://jkjc.yciccloud.com:8000/xboot/displayScreen/default/findOneByCondition'
      const params = {
        structureCode: this.iThis.params.code
      }
      try {
        // const res = await get(url, { params: params })
        const res = await fetchGet(url, params)
        // if (res.code === 200 && res.data) {
        //   if (res.data.code === 200) {
        //     this.type === '桥梁' ?
        //       this.commonData = res.data.result || this.bridgeData :
        //       this.commonData = res.data.result || this.tunnelData; // 隧道暂时没有数据
        //   }
        // }
        if (res.code === 200) {
          this.type === '桥梁' ?
            this.commonData = res.result || this.bridgeData :
            this.commonData = res.result || this.tunnelData; // 隧道暂时没有数据
        }
      } catch (error) {
        this.$message.error(error)
      }
    },
    async setOption() {
      let data = []
      let sensorList = this.iThis.sensorList;
      let list = sensorList.filter((item) =>
        item.specificMonitorTypeName.includes("高程坐标变化")
      ) || []
      if (!list || list.length === 0) {
        this.option = {
          title: {
            text: '没有数据',
            left: 'center',
            top: 'center',
            textStyle: {
              fontSize: this.isBig ? 32 : 14,
              color: '#606266',
              fontWeight: 700
            }
          }
        }
        this.optionKey = new Date().getTime();
        return
      } else {
        this.loading = true;
        let params = {
          structureNodeCode: this.iThis.params.code,
          nodeCode: list[0].code,
          sensorId: list[0].sensorId,
        };
        const url = 'https://jkjc.yciccloud.com:8000/xboot/displayScreen/default/getCalibratedRealTimeData'
        let res = await fetchGet(url, params)
        if (res.code == 200) {
          data = res.result
        }
        this.loading = false
      }
      if (data.length === 0) {
        this.option = {
          title: {
            text: '没有数据',
            left: 'center',
            top: 'center',
            textStyle: {
              fontSize: this.isBig ? 32 : 14,
              color: '#606266',
              fontWeight: 700
            }
          }
        }
        this.optionKey = new Date().getTime();
        return
      }
      let myseries = [];
      let mylegend = [];
      let myXdata = [];
      data.forEach((item) => {
        myseries.push({
          name: item.name,
          type: "line",
          data: item.values,
          smooth: false,
          symbolSize: 7
        });
        mylegend.push(item.name);
        myXdata = item.times;
      });

      this.option = {
        tooltip: {
          trigger: "axis",
        },
        legend: {
          data: mylegend,
          type: 'scroll',
          top: "4%",
          left: "center",
          itemGap: this.isBig ? 20 : 10, // 每个图例间隔
          textStyle: {
            color: "#ffffff",
            fontSize: this.isBig ? 24 : 12,
          },
        },
        grid: {
          left: "4%",
          right: "4%",
          top: "20%",
          bottom: "6%",
          containLabel: true,
        },
        xAxis: {
          type: "category",
          boundaryGap: true,
          data: myXdata,
          axisLabel: {
            textStyle: {
              // 设置文本样式
              color: "#ffffff",
              fontSize: this.isBig ? 24 : 10,
            },
          },
          axisLine: {
            show: true,
            lineStyle: {
              color: 'rgba(110, 112, 121, 0.70)'
            }
          },
          axisTick: {
            alignWithLabel: true, // 刻度线与标签对齐
            inside: true // 刻度线朝上
          }
        },
        yAxis: {
          type: "value",
          boundaryGap: [0.1, 0.1], // 上下各预留 10% 的空间
          axisLabel: {
            // 添加或修改 axisLabel 配置项
            textStyle: {
              // 设置文本样式
              color: "rgba(153, 153, 153, 1)",
              fontSize: this.isBig ? 24 : 10,
            },
          },
          splitArea: {
            show: false,
          },
          splitLine: {
            lineStyle: {
              color: 'rgba(110, 112, 121, 0.70)' // 修改分割线颜色
            }
          },
        },
        series: myseries,
      }
      this.optionKey = new Date().getTime()
    },
  },
  computed: {},
  watch: {},
}
</script>

<style lang="scss" scoped>
@import "@/assets/styles/utils.scss";

::-webkit-scrollbar {
  width: vwpx(14px);
  height: vwpx(14px);
}

::-webkit-scrollbar-thumb {
  background-color: rgba(1, 102, 254, 0.4);
}

::-webkit-scrollbar-track {
  background-color: rgba(0, 35, 94, .6);
}

.box {
  height: 100%;
  width: 100%;
  color: #eeeeee;
  padding: vwpx(20px);
  overflow-y: auto;
  overflow-x: hidden;

  .set-margin {
    margin-bottom: vwpx(20px);
    padding-left: vwpx(20px) !important;
    padding-right: vwpx(20px) !important;
  }

  .list-item {
    display: flex;
    align-items: center;

    .icon-right {
      background-image: url('~@/assets/monitoringSystem/right-icon.png');
      background-size: 100% 100%;
      width: vwpx(34px);
      height: vwpx(34px);
      display: inline-block;
      flex-shrink: 0;
    }

    span {
      font-weight: 400;
      font-size: vwpx(30px);
      color: #FFFFFF;
      flex-shrink: 0;
    }

    .list-link {
      flex-shrink: 0;
      font-size: vwpx(30px);
    }
  }
}

::v-deep {
  .el-descriptions__body {
    padding: vwpx(40px) vwpx(60px) 0 vwpx(60px);
    background: transparent;
  }

  .el-descriptions-item__cell {
    padding-bottom: 1.35vh !important;
  }

  .el-descriptions-item__label {
    color: #69c9df;
    font-size: vwpx(32px);
    font-weight: bold;
    white-space: nowrap;
    line-height: 2vh;
  }

  .el-descriptions-item__content {
    white-space: normal;
    word-break: break-word;
    display: inline-block;
    line-height: 2vh;
    font-size: vwpx(32px);
    color: #eeeeee;
  }
}
</style>