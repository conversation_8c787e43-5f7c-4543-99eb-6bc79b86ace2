<template>
  <div class="pie-echarts" :style="{ height: height ? height : bigBool ? '340px' : '160px' }"></div>
</template>

<script>
import { isBigScreen } from '@/views/map/components/common/util';
import * as echarts from 'echarts'
require('echarts/theme/macarons') // echarts theme
export default {
  name: 'pieEcharts',
  data() {
    return {
      chart: null,
      option: null,
      bigBool: isBigScreen(),
    }
  },
  props: {
    data: {
      type: [Array,Object],
      default: () => {
        return [
          {
            name: "大桥",
            value: 3000,
          },
          {
            name: "特大桥",
            value: 870,
          },
          {
            name: "中桥",
            value: 5502,
          },
          {
            name: "小乔",
            value: 6789,
          },
        ]
      }
    },
    height: {
      type: String,
      default: ''
    },
    total: {
      type: [String, Number],
      default: 0,
    },
    name: {
      type: String,
      default: '桥梁'
    },
    color: {
      tyep: Array,
      default: () => {
        return [
          "#00ffff",
          "#00cfff",
          "#006ced",
          "#ffe000",
          "#ffa800",
          "#ff5b00",
          "#ff3000",
        ]
      }
    }
  },
  mounted() {
    this.initChart()
  },
  methods: {
    initChart() {
      let total = 0;
      this.chart = echarts.init(this.$el, "macarons", { renderer: "svg" });
      var trafficWay = this.data;
      var data = [];
      var color = this.color;
      let radius = this.bigBool ? ["75%", "79%"] : ["65%", "69%"];
      for (var i = 0; i < trafficWay.length; i++) {
        total += trafficWay[i].value;
        data.push(
          {
            value: trafficWay[i].value,
            name: trafficWay[i].name,
            itemStyle: {
              normal: {
                borderWidth: 4,
                shadowBlur: 5,
                borderColor: color[i],
                shadowColor: color[i],
              },
            },
          },
          {
            value: 2,
            name: "",
            itemStyle: {
              normal: {
                label: {
                  show: false,
                },
                labelLine: {
                  show: false,
                },
                color: "rgba(0, 0, 0, 0)",
                borderColor: "rgba(0, 0, 0, 0)",
                borderWidth: 0,
              },
            },
          }
        );
      }
      var seriesOption = [
        {
          name: "",
          type: "pie",
          clockWise: false,
          radius,
          hoverAnimation: false,
          gap: 10,
          itemStyle: {
            normal: {
              label: {
                show: true,
                position: "outside",
                color: "#ddd",
                formatter: function (params) {
                  var percent = 0;
                  var total = 1;
                  for (var i = 0; i < trafficWay.length; i++) {
                    total += trafficWay[i].value;
                  }
                  percent = ((params.value / total) * 100).toFixed(2);
                  if (params.name !== "") {
                    return (
                      params.name + `：${params.value}` +
                      "\n" +
                      "占比：" +
                      percent +
                      "%"
                    );
                  } else {
                    return "";
                  }
                },
                fontSize: this.bigBool ? 25 : 12,
              },
              labelLine: {
                length: this.bigBool ? 30 : 10,
                length2: this.bigBool ? 80 : 20,
                show: true,
                color: "#00ffff",
              },
            },
          },
          data: data,
        },
      ];

      let option = {
        backgroundColor: "rgba(0,0,0,0)",
        color: color,
        title: {
          text: this.name == '桥梁' ? 16292 : 16170 || `${total}`,
          subtext: this.name,
          top: "40%",
          textAlign: "center",
          left: "49%",
          textStyle: {
            color: "#fff",
            fontSize: this.bigBool ? 32 : 16,
            fontWeight: "700",
            shadowColor: 'rgba(27,126,242,0.8)',
            shadowBlur: 10,
            shadowOffsetX: 5,
            shadowOffsetY: 5,
          },
          subtextStyle: {
            fontSize: this.bigBool ? 32 : 16,
            align: "center",
            color: '#fff'
          },
        },
        tooltip: {
          show: false,
        },
        legend: {
          show: false,
        },
        toolbox: {
          show: false,
        },
        series: seriesOption,
      };
      this.chart.setOption(option);
      // 监听浏览器窗口变化，重新渲染图表
      window.addEventListener("resize", () => {
        this.chart.resize();
      });
    },
  }
}
</script>

<style lang="scss" scoped>
.pie-echarts {
  width: 100%;
}
</style>