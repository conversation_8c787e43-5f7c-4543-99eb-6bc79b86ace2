<template>
  <div class='app-container'>
    <el-row :gutter='20'>
      <!--部门数据-->
      <el-col :span='relaNav ? 4 : 0' :xs='24' class='leftDiv'>
        <!--折叠图标-->
        <div class='leftIcon' @click='relaNav = false'>
          <span class='el-icon-caret-left'></span>
        </div>
        <div class='head-container' style='width: 300px'>
          <el-tree
            ref='tree'
            :data='filteredTreeData'
            :default-expanded-keys='[1]'
            :expand-on-click-node='false'
            :props='defaultProps'
            highlight-current
            node-key='code'
            @node-click='handleNodeClick'
          >
          </el-tree>
        </div>
      </el-col>
      <!--角色数据-->
      <el-col :span='relaNav ? 20 : 24' :xs='24'>
        <!--展开图标-->
        <div v-show='!relaNav' class='rightIcon' @click='relaNav = true'>
          <span class='el-icon-caret-right'></span>
        </div>
        <el-row :gutter='10' class='mb8'>
          <el-col :span='1.5'>
            <el-button
              icon='el-icon-back'
              size='mini'
              type='warning'
              @click='handleBack'
            >返回上一级
            </el-button>
          </el-col>
          <el-col :span='1.5'>
            <el-button
              icon='el-icon-upload'
              size='mini'
              type='success'
              @click='handleUpload'
            >上传文件
            </el-button>
          </el-col>
          <el-col :span='1.5'>
            <el-button
              icon='el-icon-plus'
              size='mini'
              type='primary'
              @click='handleAdd'
            >新建文件夹
            </el-button>
          </el-col>
          <el-col :span='1.5'>
            <el-button
              icon='el-icon-refresh'
              size='mini'
              type='warning'
              @click='handleQuery'
            >刷新
            </el-button>
          </el-col>
        </el-row>
        <!-- 筛选区结束 -->
        <!-- 数据表格开始 -->
        <div class='tableDiv'>
          <el-table v-adjust-table v-loading='loading' :data='dataList'
                    :height="'calc(100vh - 260px)'" border size='mini'
                    @selection-change='handleSelectionChange'>
            <el-table-column align='center' fixed label='序号' type='index' width='100'></el-table-column>
            <el-table-column align='left' label='名称' prop='fileName'>
              <template slot-scope='scope'>
                <el-link
                  type="primary"
                  class='link-type'
                  @click='handleRowClick(scope.row)'
                ><el-icon v-if='scope.row.isDir' class='el-icon-folder' style='margin-right: 5px;'></el-icon>{{ scope.row.fileName }}
                </el-link>
              </template>
            </el-table-column>
            <el-table-column align='center' width='200' label='创建日期' prop='createTime'></el-table-column>
            <el-table-column align='center' fixed='right' label='操作' width='150'>
              <template slot-scope='scope'>
                <el-button
                  icon='el-icon-delete'
                  size='mini'
                  type='text'
                  @click='handleDelete(scope.row)'
                >删除
                </el-button>
              </template>
            </el-table-column>
          </el-table>
          <pagination
            v-show='total > 0'
            :limit.sync='queryParams.pageSize'
            :page.sync='queryParams.pageNum'
            :total='total'
            @pagination='handleQuery'
          />
        </div>
        <!-- 数据表格结束 -->
      </el-col>
    </el-row>
    <el-dialog dialogTitle='新建文件夹' v-if='dialogVisible' :visible.sync='dialogVisible' width='500px' append-to-body>
      <el-form ref='elForm' :model='formData' :rules='rules' size="medium" style='height: 200px'>
        <el-row :gutter='20'>
          <el-col :span='24'>
            <div class='mb20'>请输入新建文件夹名称：</div>
            <el-form-item label='' prop='fileName'>
              <el-input v-model='formData.fileName' placeholder='请输入新建文件夹名称'/>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div style="text-align: right">
        <el-button type='primary' @click='handleConfirm'>确 定</el-button>
        <el-button @click='cancel'>取 消</el-button>
      </div>
    </el-dialog>
    <el-dialog dialogTitle='上传文件' v-if='fileVisible' :visible.sync='fileVisible' width='500px' append-to-body>
      <file-upload platform='jgjc' ref='file' v-model="formData.url" :owner-id='formData.url' :limit='1'></file-upload>
      <div style="text-align: right">
        <el-button type='primary' @click='handleSaveFile'>确 定</el-button>
        <el-button @click='cancel'>取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getJCDomainTree } from '@/api/jgjc/baseInfo/alarmLog'
import { deleteFileManagement, getFileManagementPage, saveFileManagement } from '@/api/jgjc/baseInfo/docManager'
import { getFile } from '@/api/file'
import axios from 'axios'

export default {
  name: 'ContentManagement',
  data() {
    return {
      // 遮罩层
      loading: false,
      // 左侧组织树
      relaNav: true,
      relaOptions: [],
      filteredTreeData: [],
      defaultProps: {
        children: 'children',
        label: 'label',
      },
      // 总条数
      total: 0,
      dataList: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
      },
      parentId: '',
      oldParentId: [],
      ids: [],
      structureId: '',
      dialogVisible: false, // 对话框显示状态
      formData: {},
      rules: {
        fileName: [
          { required: true, message: '新建文件夹名称不能为空', trigger: 'blur' },
        ],
      },
      fileVisible: false
    }
  },
  created() {
    this.getDeptTree()
  },
  methods: {
    // 查询部门下拉树结构
    getDeptTree() {
      getJCDomainTree({}).then(response => {
        this.relaOptions = response.data
        this.filteredTreeData = [...this.relaOptions]
      })
    },
    // 树节点点击事件
    handleNodeClick(nodeData, node) {
      if (node.level == 4) {
        console.log(node)
        this.queryParams = {
          pageNum: 1,
          pageSize: 10,
          structureId: nodeData.structureId,
        }
        this.structureId = nodeData.structureId
        this.parentId = ''
        this.oldParentId = []
        this.handleQuery()
      }
    },
    handleQuery() {
      this.loading = true
      this.queryParams.parentId = this.parentId
      getFileManagementPage(this.queryParams).then(res => {
        this.dataList = res.rows
        this.total = res.total
        this.loading = false
      }).catch(err => {
        this.loading = false
        console.error(err)
      })
    },
    handleRowClick(row) {
      if (row.isDir) {
        this.oldParentId.push(this.parentId)
        this.parentId = row.id
        this.handleQuery()
      }
      this.loading = true
      getFile({ ownerId: row.url }).then(async res => {
        if (res.code == 200) {
          await this.handleDownload(res.data)
        }
      }).finally(() => {
        this.loading = false
      })
    },
    handleBack() {
      if (this.oldParentId.length == 0) {
        this.$message.warning('已经是最顶层文件夹')
        return
      }
      this.parentId = this.oldParentId[this.oldParentId.length - 1]
      this.oldParentId.pop()
      this.handleQuery()
    },
    handleDelete(row) {
      this.$confirm('是否确认删除？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }).then(() => {
        this.loading = true
        deleteFileManagement({ ids: [row.id] }).then(res => {
          if (res.code === 200) {
            this.$message.success('删除成功')
            this.handleQuery()
          } else {
            this.$message.error(res.msg)
          }
          this.loading = false
        }).catch(err => {
          this.loading = false
          console.error(err)
        })
      })
    },
    handleAdd() {
      if (!this.structureId) {
        this.$message.warning('请选择结构物')
        return
      }
      this.formData = {}
      this.formData.structureId = this.structureId
      this.formData.isDir = true
      this.formData.parentId = this.parentId
      this.dialogVisible = true
    },
    handleUpload() {
      if (!this.structureId) {
        this.$message.warning('请选择结构物')
        return
      }
      this.formData = {}
      this.formData.structureId = this.structureId
      this.formData.isDir = false
      this.formData.parentId = this.parentId
      this.fileVisible = true
    },
    handleConfirm() {
      this.$refs['elForm'].validate(valid => {
        if (valid) {
          saveFileManagement(this.formData).then(res => {
            this.$modal.msgSuccess('新增成功')
            this.dialogVisible = false
            this.handleQuery()
          })
        }
      })
    },
    handleSaveFile() {
      console.log(this.$refs.file.fileList)
      const fileList = this.$refs.file.fileList
      if (fileList.length != 1) {
        this.$message.warning('请上传文件')
        return
      }
      this.formData.fileName = fileList[0].name
      this.formData.url = fileList[0].ownerId
      saveFileManagement(this.formData).then(res => {
        this.$modal.msgSuccess('新增成功')
        this.fileVisible = false
        this.handleQuery()
      })
    },
    cancel() {
      this.dialogVisible = false
      this.fileVisible = false
      this.formData = {}
    },
    async handleDownload(file) {
      const url = file.url || (file.response && file.response.url)
      if (!url) {
        this.$message.error('文件无法下载，未找到文件的URL')
        return
      }

      try {
        // 创建一个不包含 Authorization 的请求头
        const downloadHeaders = {
          ...this.headers,
          'Content-Disposition': 'attachment', // 添加强制下载头
          'Content-Type': 'application/octet-stream', // 使用通用的二进制流类型
        }
        delete downloadHeaders.Authorization

        // 使用 axios 进行下载，因为它可以自动处理跨域认证
        const response = await axios({
          url: url,
          method: 'GET',
          responseType: 'blob',
          headers: downloadHeaders,
        })

        // 从响应头中获取文件类型
        const contentType = response.headers['content-type']
        const blob = new Blob([response.data], { type: contentType || 'application/octet-stream' })

        // 创建一个隐藏的 iframe 来处理下载
        const iframe = document.createElement('iframe')
        iframe.style.display = 'none'
        document.body.appendChild(iframe)
        const finalFileName = file.originalFilename

        // 在 iframe 中创建 blob URL 并触发下载
        const iframeWindow = iframe.contentWindow
        const blobUrl = iframeWindow.URL.createObjectURL(blob)
        const link = iframeWindow.document.createElement('a')
        link.href = blobUrl
        link.download = finalFileName
        link.click()

        // 清理资源
        setTimeout(() => {
          iframeWindow.URL.revokeObjectURL(blobUrl)
          document.body.removeChild(iframe)
        }, 1000)
      } catch (error) {
        console.error('下载文件时发生错误:', error)
        this.$message.error('下载文件失败，请重试')
      }
    },
  },
}
</script>

<style scoped>
.leftDiv {
  border-right: 1px solid #d8dce5;
  min-height: calc(100vh - 100px);
  overflow-y: auto;
  height: calc(80vh - 150px);
  position: relative;
  top: -20px;
  padding-top: 10px;
  background-color: white;
}

.leftIcon {
  border: 1px solid #dcdfe6;
  border-radius: 8px;
  width: 16px;
  height: 50px;
  line-height: 50px;
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
  position: absolute;
  right: 0;
  top: 300px;
  z-index: 2;
}

.leftIcon:hover {
  background-color: #dcdfe6;
}

.rightIcon {
  border: 1px solid #dcdfe6;
  border-radius: 8px;
  width: 16px;
  height: 50px;
  line-height: 50px;
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
  position: absolute;
  left: -10px;
  top: 280px;
  z-index: 10;
  background: white;
}

.rightIcon:hover {
  background-color: #dcdfe6;
}

.tableDiv {
  margin-top: 20px;
}
</style>
<style lang='scss' scoped>
@import "@/assets/styles/business.scss";
</style>
