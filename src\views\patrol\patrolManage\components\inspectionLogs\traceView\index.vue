<template>
	<el-drawer v-bind="$attrs" v-on="$listeners" @close="drawerClose" title="轨迹预览" size="60%">
		<div class="content_box" v-loading="loading">
			<div id="map_content" v-if="hasTrace"></div>
			<p class="no_trace" v-else>{{ tip }}</p>
		</div>
	</el-drawer>
</template>
<script>
import AMapLoader from '@amap/amap-jsapi-loader' // 高德地图
import { GetPatrolGeoById } from '@/api/patrol/inspectionLogs'

export default {
	name: 'traceDialog',
	data() {
		return {
			convertedArray: [],
			hasTrace: false,
			map: Object.freeze(null),
			loading: true,
			tip: '',
		}
	},
	props: {
		rowData: {
			type: Object,
			default: {},
		},
	},
	methods: {
		coordinateInit(lineArr) {
			AMapLoader.load({
				key: 'd12697699ae0de43e2cf383e80eaa375', // 申请好的Web端开发者Key，首次调用 load 时必填
				version: '2.0', // 指定要加载的 JSAPI 的版本，缺省时默认为 1.4.15
				// plugins: ["AMap.ToolBar", "AMap.Scale"], //需要使用的的插件列表，如比例尺'AMap.Scale'，支持添加多个如：['...','...']
			}).then((AMap) => {
				let positionInit = [lineArr[0][0], lineArr[0][1]] // 默认坐标昆明市
				// let positionInit = [116.368904, 39.913423] // 默认坐标昆明市
				this.map = new AMap.Map('map_content', {
					// 设置地图容器id
					viewMode: '2D', // 是否为3D地图模式
					zoom: 11, // 初始化地图级别
					center: positionInit, // 初始化地图中心点位置
				})
				let polyline = new AMap.Polyline({
					path: lineArr, // 设置线覆盖物路径
					strokeColor: '#1890ff', // 线颜色
					strokeOpacity: 1,
					lineJoin: 'round',
					strokeWeight: 5, // 线宽
					strokeStyle: 'dashed', // 线样式
				})
				this.map.add(polyline)
				this.loading = false
			})
		},
		getGeo() {
			return new Promise((resolve, reject) => {
				GetPatrolGeoById(this.rowData.id)
					.then((res) => {
						if (res.data) {
							this.hasTrace = true
							let Points = this.convertLineString(res.data.shape)
							Points = [...new Set(Points.map(JSON.stringify))].map(JSON.parse)
							let newArr = [] //经纬度互转
							for (let item of Points) {
								newArr.push({
									latitude: item[1],
									longitude: item[0],
								})
							}
							resolve(Points)
						} else {
							this.loading = false
							this.hasTrace = false
							this.tip = '暂无巡查轨迹数据'
						}
					})
					.catch((error) => {
						console.error('Error fetching images:', error)
					})
			})
		},
		convertLineString(lineString) {
			const coordinates = lineString
				.split('(')[1]
				.split(')')[0]
				.split(',')
				.map((coord) => {
					const parts = coord.trim().split(' ')
					return [parseFloat(parts[0]), parseFloat(parts[1])]
				})
			return coordinates
		},
		drawerClose() {
			this.hasTrace = false
			this.tip = ''
			this.map && this.map.destroy()
			this.$emit('visibleChange')
		},
	},
	watch: {
		rowData: {
			deep: true,
			handler(newValue, oldValue) {
				if (newValue.id) {
					this.$nextTick(async () => {
						this.loading = true
						let line = await this.getGeo()
						this.coordinateInit(line)
					})
				}
			},
		},
	},
}
</script>
<style lang="scss" scoped>
.content_box {
	width: 100%;
	height: 100%;
}
#map_content {
	width: 100%;
	height: 100%;
}
.no_trace {
	text-align: center;
	margin-top: 200px;
	background-color: none;
}
</style>
