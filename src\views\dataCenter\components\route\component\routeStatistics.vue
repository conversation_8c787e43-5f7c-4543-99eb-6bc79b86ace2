<template>
  <div class="route-statistics">
    <div class="route-statistics-list">
      <div class="route-statistics-item" v-for="(item, index) in list" :key="index">
        <div class="route-statistics-item-title">
          <img :src="item.icon" alt="" />
          <span>{{ item.name }}</span>
        </div>
        <div class="route-statistics-item-content">
          <div class="route-statistics-item-content-item" :class="index < item.child.length - 1 ? 'border-b' : ''"
            v-for="(child, index) in item.child" :key="index">
            <span>{{ child.name }}：</span>
            <span>{{ child.quantity }} <small>条</small></span>
            <span>{{ child.kilometer }} <small>km</small></span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
// api
import { getRouteStatistics } from "@/api/cockpit/route";

export default {
  data() {
    return {
      list: [
        {
          name: '路线',
          icon: require('@/assets/cockpit/route.png'),
          child: [
            {
              name: '国高',
              quantity: 1627,
              kilometer: 1627
            },
            {
              name: '地高',
              quantity: 1627,
              kilometer: 1627
            },
            {
              name: '合计',
              quantity: 1627,
              kilometer: 1627
            },
          ]
        },
        {
          name: '公路路段',
          icon: require('@/assets/cockpit/h_section.png'),
          child: [
            {
              name: '集团公司',
              quantity: 1627,
              kilometer: 1627
            },
            {
              name: '项目公司',
              quantity: 1627,
              kilometer: 1627
            },
            {
              name: '合计',
              quantity: 1627,
              kilometer: 1627
            },
          ]
        },
        {
          name: '养护路段',
          icon: require('@/assets/cockpit/m_section.png'),
          child: [
            {
              name: '集团公司',
              quantity: 1627,
              kilometer: 1627
            },
            {
              name: '项目公司',
              quantity: 1627,
              kilometer: 1627
            },
            {
              name: '合计',
              quantity: 1627,
              kilometer: 1627
            },
          ]
        },
      ],
    }
  },
  async mounted() {
    this.loading = true;
    let data = await this.getRouteCountAll();
    this.loading = false;
    if (data) {
      // 路线统计
      // 1、国高
      this.list[0].child[0].quantity = data.guoRouteCount || 0;
      this.list[0].child[0].kilometer = data.guoRouteLength ? data.guoRouteLength : 0;
      // 2、地高
      this.list[0].child[1].quantity = data.shenRouteCount || 0;
      this.list[0].child[1].kilometer = data.shenRouteLength ? data.shenRouteLength : 0;
      // 3、合计
      this.list[0].child[2].quantity = data.routeNumTotal || 0;
      this.list[0].child[2].kilometer = data.routeLengthTotal || 0;

      // 公路路段
      // 1、集团公司
      this.list[1].child[0].quantity = data.groupHighwayCount || 0;
      this.list[1].child[0].kilometer = data.groupHighwayLength ? data.groupHighwayLength : 0;
      // 2、项目公司
      this.list[1].child[1].quantity = data.projectHighwayCount || 0;
      this.list[1].child[1].kilometer = data.projectHighwayLength ? data.projectHighwayLength : 0;
      // 3、合计
      this.list[1].child[2].quantity = data.highwayNumTotal || 0;
      this.list[1].child[2].kilometer = data.highwayLengthTotal || 0;

      // 养护路段
      // 1、集团公司
      this.list[2].child[0].quantity = data.groupMaintainCount || 0;
      this.list[2].child[0].kilometer = data.groupMaintainLength ? data.groupMaintainLength : 0;
      // 2、项目公司
      this.list[2].child[1].quantity = data.projectMaintainCount || 0;
      this.list[2].child[1].kilometer = data.projectMaintainLength ? data.projectMaintainLength : 0;
      // 3、合计
      this.list[2].child[2].quantity = data.maintainNumTotal || 0;
      this.list[2].child[2].kilometer = data.maintainLengthTotal || 0;
    }
  },
  methods: {
    // 获取 路线统计 数据
    getRouteCountAll() {
      return new Promise((resolve, reject) => {
        getRouteStatistics().then((res) => {
          if (res.code === 200 && res.data) {
            resolve(res.data)
          } else {
            reject(res.message)
          }
        }).catch((err) => {
          reject(err)
        });
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.route-statistics {
  width: 100%;
  height: 100%;

  .route-statistics-list {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;

    .route-statistics-item {
      flex: 1;
      display: flex;
      align-items: center;
      margin: 20px;
      border-radius: 20px;
      border: 2px solid #2CB5FF;

      .route-statistics-item-title {
        width: 200px;
        height: 200%;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        margin-left: 10px;

        img {
          width: 150px;
          height: 150px;
        }

        span {
          font-family: Source Han Sans, Source Han Sans;
          font-weight: 700;
          font-size: 32px;
          color: #FFFFFF;
        }
      }

      .route-statistics-item-content {
        flex: 1;
        height: 100%;
        display: flex;
        flex-direction: column;
        background-color: rgba(0, 36, 73, 0.6);
        margin-left: 20px;
        padding: 30px;

        .route-statistics-item-content-item {
          flex: 1;
          display: flex;
          align-items: center;
          justify-content: space-between;

          span:first-child {
            width: 160px;
            font-family: Microsoft YaHei UI, Microsoft YaHei UI;
            font-weight: 700;
            font-size: 24px;
            color: #FFFFFF;
            flex-shrink: 0;
          }

          span:nth-child(2) {
            font-family: Microsoft YaHei UI, Microsoft YaHei UI;
            font-weight: 700;
            font-size: 32px;
            color: #017BFD;

            small {
              font-family: Microsoft YaHei UI, Microsoft YaHei UI;
              font-weight: 400;
              font-size: 24px;
              color: #B6B6B6;
            }
          }

          span:last-child {
            font-family: Microsoft YaHei UI, Microsoft YaHei UI;
            font-weight: 700;
            font-size: 32px;
            color: #2DDB44;
            margin-right: 40px;

            small {
              font-family: Microsoft YaHei UI, Microsoft YaHei UI;
              font-weight: 400;
              font-size: 24px;
              color: #B6B6B6;
            }
          }
        }

        .border-b {
          border-bottom: 2px dotted rgba(156, 189, 255, 0.5);
        }
      }
    }
  }
}
</style>