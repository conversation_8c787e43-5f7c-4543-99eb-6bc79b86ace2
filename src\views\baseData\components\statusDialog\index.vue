<template>
  <div>
    <el-dialog
      :title="title"
      :visible.sync="visibleSync"
      :before-close="handelClose"
      width="600px"
      append-to-body
    >
      <el-form ref="form" :model="form" label-width="100px">
        <el-form-item
          label="运营状态"
          prop="operationState"
          :rules="[
            { required: true, message: '请选择运营状态', trigger: 'blur' },
          ]"
        >
          <el-select v-model="form.operationState" clearable filterable style="width: 100%;" placeholder="运营状态">
            <el-option v-for="dict in dict.type.sys_operation_state"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item
          label="原因"
          prop="businessReason"
          :rules="[{ required: true, message: '请输入原因', trigger: 'blur' }]"
        >
          <el-input
            v-model="form.businessReason"
            placeholder="请输入"
          ></el-input>
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input
            type="textarea"
            v-model="form.remark"
            placeholder="请输入"
          ></el-input>
        </el-form-item>
        <el-form-item label="文件" prop="attachmentFilesId">
          <FileUpload v-model="form.attachmentFilesId" :limit="1"
                      platform="mpkj"/>
        </el-form-item>
      </el-form>
      <div slot="footer">
        <el-button type="primary" @click="handelCheck" v-loading="loading"
          >确 定</el-button>
        <el-button @click="handelClose">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { businessStatusRecord } from "@/api/baseData/common/index.js";

export default {
  name: "",
  dicts: ["sys_operation_state"],
  props: {
    visible: {
      default: false,
    },
    dataId: {
      default: "",
    },
    baseDataType: {
      default: null,
    },
    title: {
      type: String,
      default: null,
    },
  },
  components: {},
  data() {
    return {
      form: {},
      loading: false,
    };
  },
  computed: {
    visibleSync: {
      get() {
        return this.visible;
      },
      set(v) {
        this.$emit("update:visible", v);
      },
    },
  },
  methods: {
    handelCheck() {
      this.form.baseDataType = this.baseDataType;
      this.form.dataId = this.dataId;
      this.form.attachmentFilesId =
        this.form.attachmentFilesId && this.form.attachmentFilesId.length
          ? this.form.attachmentFilesId[0]
          : "";
      this.$refs["form"].validate((valid) => {
        if (valid) {
          this.loading = true;
          businessStatusRecord(this.form)
            .then((res) => {
              if (res.code == 200) {
                this.$modal.msgSuccess("变更成功");
                this.$emit("ok");
              }
            })
            .finally(() => {
              this.loading = false;
            });
        } else {
          return false;
        }
      });
    },
    handelClose() {
      this.$emit("cancel");
    },
  },
  watch: {},
};
</script>

<style lang="scss" scoped>
</style>
