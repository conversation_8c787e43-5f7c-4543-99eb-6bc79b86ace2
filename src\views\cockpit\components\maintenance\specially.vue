<template>
  <div class="specially-maintenance">
    <Echarts :key="key + component" :option="option" v-if="option" height="28vh" />
    <div class="divider"></div>
    <section class="info">
      <div class="info-list">
        <span>预算费用</span>
        <span :style="setColor(0)">{{ budgetNum }} <small>万元</small></span>
      </div>
      <div class="divider-column"></div>
      <div class="info-list">
        <span>实际费用</span>
        <span :style="setColor(1)">{{ actualNum }} <small>万元</small></span>
      </div>
      <div class="divider-column"></div>
      <div class="info-list">
        <span>实际费用占比</span>
        <span :style="setColor(2)">{{ ratio }} <small>%</small></span>
      </div>
    </section>
  </div>
</template>

<script>
import { calcFontSize, isBigScreen } from "../../util/utils";
import Echarts from "../echarts/echarts.vue";
import { getStatusCountList } from "@/api/cockpit/index";

export default {
  components: {
    Echarts,
  },
  props:{
    component: {
      type: String,
      default: "",
    }
  },
  data() {
    return {
      option: null,
      isBig: isBigScreen(),
      chartList: [],
      budgetNum: "-",
      actualNum: "-",
      ratio: "-",
      year: new Date().getFullYear(),
      key: "",
      color: ["#01FBEF", "#F0AE4B", "#0154FB"],
    };
  },
  created() {
    window.$Bus.$on("onChangeYear", (y) => {
      this.year = y;
      this.getData();
    });
    window.$Bus.$on("pageL-ZXYH", (sum1, sum2) => {
      // sum1预算费用，sum2世实际费用
      this.budgetNum = (sum1 / 10000).toFixed(2);
      this.actualNum = (sum2 / 10000).toFixed(2);
      this.ratio = sum1 ? ((sum2 / sum1) * 100).toFixed(2) : 0;
    });
  },
  async mounted() {
    await this.getData();
  },
  methods: {
    setColor(index) {
      return { color: this.color[index] };
    },
    async getData() {
      const res = await getStatusCountList({ year: this.year });
      if (res.code === 200 && res.rows) {
        let str = ["暂未实施", "正在实施", "已完成"];
        let arr = [];
        for (let index = 0; index < str.length; index++) {
          const found = res.rows.find((item) => item.type === str[index]);
          arr.push({
            name: str[index],
            value: found ? found.count : 0,
          });
        }
        this.chartList = arr;
        // 初始化图表数据
        this.initChartData();
      }
    },
    initChartData() {
      let color = this.color;
      let chartData = this.chartList;

      var data = [];
      for (var i = 0; i < chartData.length; i++) {
        data.push(
          {
            value: chartData[i].value,
            name: chartData[i].name,
            itemStyle: {
              normal: {
                borderWidth: 5,
                shadowBlur: 2,
                borderColor: color[i],
                shadowColor: color[i],
              },
            },
          },
          {
            value: 0.06,
            name: "",
            itemStyle: {
              normal: {
                label: {
                  show: false,
                },
                labelLine: {
                  show: false,
                },
                color: "rgba(0, 0, 0, 0)",
                borderColor: "rgba(0, 0, 0, 0)",
                borderWidth: 0,
              },
            },
          }
        );
      }
      let center = ["30%", "50%"];
      let size = 13;

      var seriesOption = [
        {
          type: "pie",
          zlevel: 0,
          silent: true,
          radius: ["80%", "83%"],
          center,
          hoverAnimation: false,
          color: "rgba(0,62,122,1)",
          label: {
            normal: {
              show: false,
            },
          },
          labelLine: {
            normal: {
              show: false,
            },
          },
          data: [1],
        },
        {
          name: "",
          type: "pie",
          clockWise: false,
          radius: ["68%", "72%"],
          center,
          hoverAnimation: false,
          itemStyle: {
            normal: {
              label: {
                show: false,
              },
              labelLine: {
                show: false,
              },
            },
          },
          data: data,
        },
      ];
      this.option = {
        backgroundColor: "rgba(0, 0, 0, 0)",
        color: color,
        title: {
          text: "完成状态",
          subtext: "占比",
          top: "42%",
          textAlign: "center",
          left: "28%",
          textStyle: {
            color: "#fff",
            fontSize: this.isBig ? 54 : calcFontSize(16),
            fontWeight: "700",
          },
          subtextStyle: {
            color: "#fff",
            fontSize: this.isBig ? 46 : calcFontSize(13),
            fontWeight: "700",
          },
        },
        tooltip: {
          show: false,
          formatter: (params) => {
            if (params.name == "") {
              return "";
            }
            return `${params.name}: ${params.value}`;
          },
        },
        toolbox: {
          show: false,
        },
        series: seriesOption,
        legend: {
          show: true,
          right: this.isBig ? "10%" : "5%",
          y: this.isBig ? "35%" : "32%",
          icon: "circle",
          itemWidth: this.isBig ? 30 : 10, // 设置宽度
          itemHeight: this.isBig ? 30 : 10, // 设置高度
          itemGap: this.isBig ? 35 : 16,
          formatter: (name) => {
            let d = data.filter((v) => v.name == name);
            let num = d[0].value;
            return "{title|" + name + "：}" + "{num|" + num + "} ";
          },
          textStyle: {
            color: "#fff",
            fontSize: this.isBig ? size * 2 : calcFontSize(13),
            rich: {
              title: {
                color: "rgba(255,255,255,0.8)",
                fontSize: this.isBig ? size * 2 : calcFontSize(13),
                padding: [3, 0],
              },
              num: {
                color: "#fff",
                fontSize: this.isBig ? size * 2 : calcFontSize(15),
              },
              unit: {
                color: "rgba(182,182,182,0.8)",
                fontSize: this.isBig ? size * 2 : calcFontSize(13),
              },
            },
          },
        },
      };

      this.key = new Date().getTime();
    },
  },
  beforeDestroy() {
    window.$Bus.$off("pageL-ZXYH");
    window.$Bus.$off("onChangeYear");
  },
};
</script>

<style lang="scss" scoped>
@import "@/assets/styles/utils.scss";

.specially-maintenance {
  padding: vwpx(16px);
  color: #ffffff;
  width: 100%;
  height: 100%;

  .divider {
    width: 90%;
    margin: 0 5% vwpx(30px);
    height: vwpx(2px);
    background: repeating-linear-gradient(
      to right,
      rgba(156, 189, 255, 0.5),
      rgba(156, 189, 255, 0.5) vwpx(2px),
      transparent vwpx(3px),
      transparent vwpx(8px)
    );
  }

  .info {
    width: 90%;
    height: vwpx(200px);
    margin-left: 5%;
    background: rgba(0, 0, 0, 0.1);
    box-shadow: inset 0px 0px 10px 0px #0065ff;
    border-radius: vwpx(12px);
    border: 1px solid #20a9ff;
    padding: 0 vwpx(30px);

    display: flex;
    align-items: center;
    justify-content: space-evenly;

    .info-list {
      display: flex;
      align-items: center;
      justify-content: center;
      flex-direction: column;

      span:first-child {
        font-family: Microsoft YaHei UI, Microsoft YaHei UI;
        font-weight: 700;
        font-size: vwpx(30px);
        color: #ffffff;
        text-align: center;
        font-style: normal;
        text-transform: none;
      }

      span:last-child {
        font-family: Microsoft YaHei UI, Microsoft YaHei UI;
        font-weight: 700;
        font-size: vwpx(48px);
        color: #42abff;
        text-align: center;
        font-style: normal;
        text-transform: none;
        margin-top: vwpx(15px);

        small {
          font-family: Microsoft YaHei UI, Microsoft YaHei UI;
          font-weight: 400;
          font-size: vwpx(24px);
          color: rgba(255, 255, 255, 0.8);
          text-shadow: 0px 0px 10px rgba(27, 126, 242, 0.8);
          text-align: center;
          font-style: normal;
          text-transform: none;
        }
      }
    }

    .divider-column {
      height: vwpx(110px);
      width: vwpx(2px);
      background: repeating-linear-gradient(
        to bottom,
        rgba(23, 116, 255, 0.5),
        rgba(23, 116, 255, 0.5) vwpx(2px),
        transparent vwpx(3px),
        transparent vwpx(8px)
      );
    }
  }
}
</style>
