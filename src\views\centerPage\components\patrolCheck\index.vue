<template>
  <div class="patrol-check" @click="onFullClick">
    <div class="map-container" v-loading="loading" element-loading-background="rgba(0, 0, 0, 0.4)">
      <AMap ref="ampRef" v-if="!loading" @click="onPointClick" :center="[100.9007599, 23.5085797]"></AMap>
    </div>
    <section class="patrol-check-l" :style="{ width: isBig ? '15vw' : '24vw' }">
      <CockpitCard title="管理处" :isDtl="false" :isHeader="false" w="100%" h="calc(43vh)" class="mb-1"
        :class="isBig ? 'big-table' : 'normal-table'">
        <el-table :data="deptData" style="width: 100%;" height="100%" @row-click="onDeptClick" ref="tableRef"
          highlight-current-row>
          <el-table-column prop="name" label="管理处" align="center">
            <template slot-scope="{row}">
              <span class="table-tag">{{ row.deptName }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="number" label="在线巡查车数量" align="center">
            <template slot-scope="{row}">
              <span class="table-tag">{{ row.carNum }}</span>
            </template>
          </el-table-column>
        </el-table>
        <!-- <ScrollTable :columns="deptColumns" :data="deptData" w="100%" h="100%" /> -->
      </CockpitCard>

      <CockpitCard title="巡查车辆列表" :isDtl="false" w="100%" h="calc(43vh)">
        <!-- <el-table :data="list" style="width: 100%;" height="100%">
          <el-table-column prop="name" label="管理处" align="center" />
          <el-table-column prop="ld" label="路段" align="center" />
          <el-table-column prop="carNumber" label="车辆编号" align="center" />
        </el-table> -->
        <Tables :columns="columns" :data="list" @row-click="onListClick" />
      </CockpitCard>
    </section>

    <MapDetail v-if="showD" :top="dTop" :left="dLeft" @close="showD = false" :form="dInfo" @click="onTrajectory">
    </MapDetail>
  </div>
</template>

<script>
import cache from "@/plugins/cache";
// 组件
import AMap from '@/components/Map/aMap.vue'
import CockpitCard from '../cockpitCard.vue';
import ScrollTable from '../scrollTable.vue';
import Tables from '../tables.vue';
import MapDetail from '../common/mapDetail.vue';
import { isBigScreen } from '../../util/utils';

// api
import { getOnlinePatrolCount, getOnlinePatrolList } from '@/api/cockpit/inspection';
import { GetPatrolGeoById } from '@/api/patrol/inspectionLogs';
import { getEventListByRecordId } from '@/api/dailyMaintenance/eventManage/eventReview';

import carIcon from '@/assets/cockpit/vehicle.png';
import carAiIcon from '@/assets/cockpit/vehicle-ai.png';

export default {
  components: {
    AMap,
    CockpitCard,
    ScrollTable,
    Tables,
    MapDetail,
  },
  data() {
    return {
      isBig: isBigScreen(),
      deptColumns: [
        {
          prop: 'deptName',
          label: '管理处',
          width: '100%',
        },
        {
          prop: 'carNum',
          label: '在线巡查车数量',
          width: '100%',
        }
      ],
      deptData: [],
      columns: [
        {
          prop: 'deptName',
          label: '管理处',
          width: '100%',
        },
        {
          prop: 'maintenanceSectionName',
          label: '路段',
          width: '100%',
        },
        {
          prop: 'carNum',
          label: '车辆编号',
          width: '100%',
        }
      ],
      list: [],
      loading: true,
      showD: false, // 详情展示
      showVideo: false, // 视频展
      dTop: 150, // 详情窗口 top
      dLeft: 600, // 详情窗口 left
      dInfo: {}, // 详情信息
      scrollInterval: null, // 用于存储滚动定时器
      scrollSpeed: 5, // 滚动速度，可根据需要调整
    }
  },
  created() {
    //获取在线巡查车数量
    this.getCount();
    //获取 在线巡查车列表
    this.getList();
  },
  mounted() {
    this.startAutoScroll();
  },
  beforeDestroy() {
    this.stopAutoScroll()
  },
  methods: {
    // 获取在线巡查车数量
    getCount() {
      this.deptData = [];
      getOnlinePatrolCount().then(res => {
        this.loading = true;
        if (res.code === 200) {
          if (typeof res.data === 'string') {
            this.$message.success(res.data)
          } else {
            if (Object.prototype.toString.call(res.data) == '[object Object]') {
              let obj = {};
              for (let key in res.data) {
                obj['name'] = key;
                obj['number'] = res.data[key];
                this.deptData.push(obj);
              }
            } else {
              this.deptData = res.data.map(v => {
                v.name = v.deptName;
                v.number = v.carNum;
                return v;
              }) || [];
            }
          }
        }
      }).finally(() => {
        this.loading = false;
      });
    },
    // 获取 在线巡查车列表
    async getList(deptId = null) {
      if (deptId) {
        await this.$refs.ampRef.removeMarker('dPoint');
      }
      this.list = [];
      getOnlinePatrolList({ deptId }).then(res => {
        if (res.code === 200) {
          this.list = res.data.map(v => {
            v.name = '';
            v.icon = v.catType == 2 ? carAiIcon : carIcon; // catType 1-普通巡查车，2-AI巡查车
            if (v.patrolMileage) {
              v.patrolMileage = v.patrolMileage - 0;
              v.patrolMileage = (v.patrolMileage / 1000).toFixed(4);
            }
            return v;
          }) || [];
          // 临时数据 ↓
        }
      }).finally(() => {
        this.$nextTick(() => {
          setTimeout(async () => {
            await this.$refs.ampRef.setPoint(this.list, 900, 30, 'dPoint');
          }, 2000);
        });
      })
    },
    async onDeptClick(row) {
      let rangeData = cache.session.getJSON("rangeData");
      if (rangeData && rangeData.length) {
        row.mgeom = rangeData.find(v => v.sys_dept_id == row.deptId)?.mgeom;
      }
      await this.getList(row.deptId);
      setTimeout(() => {
        this.$refs.ampRef.setDistrictHight(row);
      }, 2500);
    },
    onListClick(row) {
      this.$refs.ampRef.setCenter(row.lastPoint);
    },
    onFullClick(e) {
      this.dTop = e.clientY + 10;
      this.dLeft = e.clientX - 160;
      this.showD = false;
    },
    // 地图点击事件
    async onPointClick(e) {
      if (!e || (e && e.mgeom)) return;
      setTimeout(async () => {
        // 显示详情窗口
        if (e.startTime && !isNaN(Date.parse(e.startTime))) {
          e.startTime = new Date(e.startTime).toLocaleDateString().replace(/\//g, '-');
        }
        this.$modal.loading();
        let data = await this.getEventListById(e.logId);
        this.$modal.closeLoading();
        this.dInfo = e;
        this.dInfo.list = data;
        console.log('dInfo', this.dInfo)
        this.showD = true;
      }, 300)
    },
    startAutoScroll() {
      if (!this.deptData && (!this.deptData && this.deptData.length <= 10)) return;
      const tableBodyWrapper = this.$refs.tableRef.$refs.bodyWrapper;
      if (!tableBodyWrapper) return;
      this.scrollInterval = setInterval(() => {
        if (tableBodyWrapper.scrollTop >= tableBodyWrapper.scrollHeight - tableBodyWrapper.clientHeight) {
          tableBodyWrapper.scrollTop = 0
        } else {
          tableBodyWrapper.scrollTop += 1
        }
      }, 1000 / this.scrollSpeed);
    },
    stopAutoScroll() {
      if (this.scrollInterval) {
        clearInterval(this.scrollInterval)
        this.scrollInterval = null
      }
    },
    // 轨迹显示
    async onTrajectory(e) {
      let data = e || this.dInfo;
      this.$modal.loading();
      let res = await this.getTrajectory(data.logId);
      this.$modal.closeLoading();
      res = { ...res, ...data }
      this.$refs.ampRef.setPathSimplifier(res);
    },
    getTrajectory(logId) {
      return new Promise((resolve, reject) => {
        GetPatrolGeoById(logId).then(res => {
          if (res.code == 200 && res.data) {
            resolve(res.data)
          } else {
            resolve([])
          }
        }).catch(err => {
          reject(err)
        })
      })
    },
    // 获取事件
    async getEventListById(recordId) {
      return new Promise((resolve, reject) => {
        getEventListByRecordId(recordId).then(res => {
          if (res.code == 200 && res.data && res.data.length) {
            resolve(res.data)
          } else {
            resolve([])
          }
        }).catch(err => {
          reject(err)
        })
      })
    },
  }
}
</script>

<style lang="scss" scoped>
@import "@/assets/styles/utils.scss";

.patrol-check {
  width: 100%;
  position: relative;

  .map-container {
    width: 100%;
    height: calc(100vh - 60px);
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
  }

  .patrol-check-l {
    position: absolute;
    top: 0;
    left: 10px;
    bottom: 0px;
    width: 24vw;
    background-color: rgba(0, 0, 0, 0.1);

    ::v-deep .el-table {
      background-color: unset;
      border: unset;
      overflow-x: hidden;

      &::-webkit-scrollbar {
        width: vwpx(12px);
        height: vwpx(12px);
      }

      &::-webkit-scrollbar-thumb {
        background-color: rgba(1, 102, 254, 0.3);
        border-radius: 2px;
      }

      &::-webkit-scrollbar-track {
        background-color: rgba(1, 102, 254, 0.2);
      }

      &::before {
        background-color: unset;
      }

      tr {
        background-color: unset;
      }

      td {
        color: #ffffff;
      }

      th.is-leaf {
        border-bottom: none;
      }

      thead {
        background-color: unset;
        color: #ffffff;
      }

      .cell {
        line-height: unset !important;
      }

      .el-table__header-wrapper tr th {
        background: rgba(0, 114, 253, 0.2);
        color: #ffffff !important;
      }

      tbody {
        background-color: unset;
        border: none;

        .el-table__row {
          border-bottom: none !important;
          cursor: pointer;

          .el-table__cell {
            border-bottom: 1px solid rgba(2, 57, 128, 1);
            padding: 0;
            cursor: pointer;
          }
        }
      }

      .el-table__body tr:hover>td {
        background-color: unset;
      }

      .el-table__body tr.current-row>td.el-table__cell {
        background-color: rgba(0, 115, 232, 0);

      }

      .el-table__inner-wrapper::before {
        background-color: unset;
      }

      th.el-table__cell {
        background-color: unset;
        color: #ffffff;
      }

      .el-table__body-wrapper {
        &::-webkit-scrollbar {
          // 整个滚动条
          width: 0; // 纵向滚动条的宽度
          background: rgba(213, 215, 220, 0.3);
          border: none;
        }

        &::-webkit-scrollbar-track {
          // 滚动条轨道
          border: none;
        }
      }
    }

    .table-tag {
      display: inline-block;
      width: 100%;
      height: vwpx(65px);
      line-height: vwpx(65px);
      background: rgba(35, 141, 255, 0.4);
      border-radius: vwpx(8px);
      border: vwpx(2px) solid #238DFF;
      font-size: vwpx(28px);
    }

    .normal-table {
      ::v-deep .el-table {
        .el-table__row {
          height: vwpx(90px) !important;
          line-height: vwpx(90px) !important;
        }

        .el-table__header-wrapper tr th {
          font-size: 1.35vh;
        }

        tbody {
          .el-table__row {
            height: vwpx(56px);
            line-height: vwpx(56px);
          }
        }
      }
    }

    .big-table {
      ::v-deep .el-table {
        .el-table__row {
          height: vwpx(100px) !important;
          line-height: vwpx(100px) !important;
        }

        .el-table__header-wrapper tr th {
          font-size: 1.4vh !important;
        }

        tbody {
          .el-table__row {
            // height: vwpx(62px) !important;
            // line-height: vwpx(62px) !important;
          }
        }
      }
    }
  }

  .mb-1 {
    margin-bottom: 0.8rem;
  }
}
</style>