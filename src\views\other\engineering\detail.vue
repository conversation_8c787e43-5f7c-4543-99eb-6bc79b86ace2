<template>
  <el-drawer v-bind="$attrs" v-on="$listeners">
    <div class="container">
      <el-descriptions title="" size="small" :column="4">
        <el-descriptions-item label="涉路工程项目">{{
          data.documentReceived
        }}</el-descriptions-item>
        <!--        <el-descriptions-item label="车辆货物信息">{{-->
        <!--            data.vehicleCargoInfo-->
        <!--          }}</el-descriptions-item>-->
      </el-descriptions>

      <div style="margin: 20px 8%">
        <el-divider v-if="false">填报内容模板</el-divider>
      </div>

      <el-form :model="queryParams" ref="queryForm" size="small" :inline="true">
      </el-form>

      <!--      <div>-->
      <!--        <cardBox-->
      <!--          style="height: calc(100vh - 160px); padding: 10px;"-->
      <!--          typeCode="BS126"-->
      <!--          :bizId="this.data.id"-->
      <!--          :key="this.data.id"-->
      <!--        />-->
      <!--      </div>-->

      <div>
        <cardBox style="min-height: 300px;max-height: calc(100vh - 160px);" typeCode="BS126" :assetType="8"
          :forView="forView" :bizId="this.data.id" />
      </div>

      <el-empty :image="emptyImage" :image-size="320"></el-empty>

      <!-- 添加或修改督查详情对话框 -->
    </div>
  </el-drawer>
</template>

<script>
import { getToken } from "@/utils/auth";

import ImageUpload from "@/views/patrol/diseases/ImageUpload.vue";
import ImagePreview from "@/views/patrol/diseases/ImagePreview.vue";

import {
  addTransportationAttachment, deleteTransportationAttachment,
  getTransportationAttachment,
  listTransportationAttachment,
  updateTransportationAttachment
} from "@/api/other/transportationAttachment";
import cardBox from "@/components/Drawing/cardBox.vue";
import { listEngineeringAttachment } from "@/api/engineering/engineeringAttachment";

export default {
  name: "MissionDetail",
  components: { cardBox, ImageUpload, ImagePreview },
  provide() {
    return {
      oneMap: false
    };
  },
  props: {
    data: {
      type: Object,
      required: true
    },
  },
  data() {
    return {
      //附件类型字典
      attachmentType: [],
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: false,
      dictType: [],
      // 总条数
      total: 0,
      // 督查详情表格数据
      detailList: null,
      // 弹出层标题
      title: "",
      // 部门树选项
      deptOptions: undefined,
      // 是否显示弹出层
      open: false,
      //文件管理的ID
      treeId: [],
      //是否显示
      forView: true,
      // 表单参数
      form: {
        roadEngineeringId: null,
        url: null,
        name: null,
        attachmentType: null,
      },
      defaultProps: {
        children: "children",
        label: "label",
      },
      // 用户导入参数
      upload: {
        // 是否显示弹出层（用户导入）
        open: false,
        // 弹出层标题（用户导入）
        title: "",
        // 是否禁用上传
        isUploading: false,
        // 是否更新已经存在的用户数据
        updateSupport: 0,
        // 设置上传的请求头部
        headers: { Authorization: "Bearer " + getToken() },
        // 上传的地址
        url: process.env.VUE_APP_BASE_API + "/system/user/importData",
      },
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 50,
      },
      // 列信息

      // 表单校验
      rules: {
      },
      emptyImage: require('@/assets/drawing/empty.png'),

    };
  },
  computed: {
    // /**
    //  * excelOptions: {
    //  rowSelect: true,
    //  rangeSelect: [
    //  {
    //  row: [this.form.headRow - 1, this.form.headRow - 1],
    //  column: [0, 0],
    //  },
    //  ],
    //  },
    //  */
  },
  watch: {
    data: {
      handler() {
        this.queryParams.roadEngineeringId = this.data.id;
        this.form.roadEngineeringId = this.data.id;
        this.getList();
      },
      deep: true,
    },
  },
  created() {
    console.log("data:" + this.data);
    this.getList();
  },
  beforeDestroy() {
    this.clearDateRange();
  },
  methods: {
    //清空页面数据
    clearDateRange() {
      this.detailList = [];
    },
    // 获取字典数据
    /** 查询用户列表 */
    getList() {
      this.loading = true;
      // console.log("get方法获取的ID"+this.queryParams.transportationId);
      listEngineeringAttachment(this.queryParams).then((response) => {
        this.detailList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        transportationId: null,
        name: null,
        attachmentType: null,
        url: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dateRange = [];
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id);
      this.single = selection.length != 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加填报表格规范";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids;
      getTransportationAttachment(id).then((response) => {
        this.form = response.data;
        this.open = true;
        this.title = "修改添填报表格规范";
      });
    },
    /** 提交按钮 */
    submitForm: function () {
      console.log(this.form.transportationId)
      this.$refs["form"].validate((valid) => {
        if (valid) {
          if (this.form.id != null) {
            updateTransportationAttachment(this.form).then((response) => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addTransportationAttachment(this.form).then((response) => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      if (row.id) {
        const id = row.id;
        this.$modal
          .confirm("是否确认删除填报格规范名称为" + row.name + "的数据项？")
          .then(function () {
            return deleteTransportationAttachment(id);
          })
          .then(() => {
            this.getList();
            this.$modal.msgSuccess("删除成功");
          });
      } else {
        if (this.ids.length > 0) {
          this.$modal
            .confirm("是否确认删除选中的" + this.ids.length + "条数据项？")
            .then(() => {
              let delArray = [];
              this.ids.forEach((item) => {
                delArray.push(deleteTransportationAttachment(item));
              });
              Promise.all(delArray).then(
                () => this.$modal.msgSuccess("删除成功") || this.getList()
              );
            });
        }
      }
    },

    /** 导出按钮操作 */
    handleExport() {
      this.download(
        "supervise/detail/export",
        {
          ...this.queryParams,
        },
        `detail_${new Date().getTime()}.xlsx`
      );
    },
    /** 导入按钮操作 */
    handleImport() {
      this.upload.title = "用户导入";
      this.upload.open = true;
    },
    /** 下载模板操作 */
    importTemplate() {
      this.download("system/user/importTemplate", {}, `user_template.xlsx`);
    },
    // 文件上传中处理
    handleFileUploadProgress(event, file, fileList) {
      this.upload.isUploading = true;
    },
    // 文件上传成功处理
    handleFileSuccess(response, file, fileList) {
      this.upload.open = false;
      this.upload.isUploading = false;
      this.$refs.upload.clearFiles();
      this.$alert(
        "<div style='overflow: auto;overflow-x: hidden;max-height: 70vh;padding: 10px 20px 0;'>" +
        response.msg +
        "</div>",
        "导入结果",
        { dangerouslyUseHTMLString: true }
      );
      this.getList();
    },
    // 提交上传文件
    submitFileForm() {
      this.$refs.upload.submit();
    },
  },
};
</script>
<style scoped lang="scss">
.container {
  margin-top: -20px;
  padding: 20px;
}

::v-deep .el-list-enter-active,
::v-deep .el-list-leave-active {
  transition: all 0s;
}

::v-deep .el-list-enter,
.el-list-leave-active {
  opacity: 0;
  transform: translateY(0);
}

.tableDiv {
  ::v-deep .el-upload-list__item {
    width: 42px;
    height: 42px;
    margin: 0px 5px -6px 0 !important;
  }

  ::v-deep .el-upload--picture-card {
    width: 42px;
    height: 42px;
    margin: 4px 0;
  }

  ::v-deep .el-icon-plus {
    font-size: 1rem;
  }

  ::v-deep .el-upload-list__item-actions {
    font-size: 1rem;
  }

  ::v-deep .el-upload-list__item-actions span+span {
    margin-left: 5px !important;
  }
}

.headRow-tip {
  color: red;
  text-align: left;
  width: 90%;
  margin: 0 auto;
}
</style>
