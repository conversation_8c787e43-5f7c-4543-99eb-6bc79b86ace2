<template>
  <div class="app-container">
    <el-row :gutter="20" style="display: flex;">
      <el-col :span="24" :xs="24">
        <el-form ref="queryForm"
                 :inline="true"
                 :model="queryParams"
                 :rules="rules"
                 label-width="68px"
                 :validate-on-rule-change="false"
                 size="mini"
        >
          <el-form-item label="" prop="year">
            <el-date-picker
              v-model="queryParams.year"
              placeholder="年份"
              style="width: 240px"
              type="year"
              value-format="yyyy"
            >
            </el-date-picker>
          </el-form-item>
          <el-form-item prop="maiSecId">
            <road-section
              ref="roadSection"
              v-model="queryParams.maiSecId"
              placeholder="请选择路段"
              style="width: 240px"
            ></road-section>
          </el-form-item>
          <el-form-item>
            <el-button
              icon="el-icon-search"
              size="mini"
              type="primary"
              @click="handleQuery"
            >搜索
            </el-button>
            <el-button
              icon="el-icon-refresh"
              size="mini"
              @click="resetQuery"
            >重置
            </el-button
            >
            <el-button
              type="success"
              icon="el-icon-download"
              size="mini"
              @click="handleDownload"
            >导出报表
            </el-button>
          </el-form-item>
        </el-form>
        <el-row :gutter="10" class="mb8" v-loading="loading" style="height: 82vh">
          <iframe v-if="reportData.html" :srcdoc="reportData.html" frameborder="0" style="height: 100%" width="100%"></iframe>
        </el-row>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import selectTree from "@/components/DeptTmpl/selectTree.vue";
import axios from "axios";
import RoadSection from "@/views/baseData/components/roadSection/index.vue";
import {getTheftLedger} from "@/api/calculate/report/theftReport";

export default {
  components: {RoadSection, selectTree},
  data() {
    return {
      loading: false,
      queryParams: {},
      reportData: {},
      deptList: [],
      numbers: [],
      rules: {
        year: [
          { required: true, message: '请选择年份', trigger: 'change' }
        ],
        maiSecId: [
          { required: true, message: '请选择养护路段', trigger: 'change' }
        ]
      }
    }
  },

  methods: {
    handleQuery() {
      this.$refs.queryForm.validate(valid => {
        if (!valid) return
        this.loading = true
        getTheftLedger(this.queryParams).then(res => {
          this.reportData = res.data
        }).finally(() => {
          this.loading = false
        })
      });
    },
    resetQuery() {
      this.queryParams = {}
    },
    handleDownload() {
      if (!this.reportData.downUrl) {
        this.$message.warning('暂无数据')
        return
      }
      axios({
        method: "get",
        responseType: 'arraybuffer',
        url: this.reportData.downUrl,
        headers: {}
      }).then((res) => {
        const arrayBuffer = res.data;
        // 创建一个Blob对象
        const blob = new Blob([arrayBuffer], {type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'}); // 对于.xls文件
        saveAs(blob, this.reportData.fileName)
      })
    },
  }
}
</script>

<style scoped lang="scss">
.app-container {
  padding-bottom: 0px;
}
</style>
<style lang="scss" scoped>
@import "@/assets/styles/business.scss";
</style>
