<template>
  <div class='app-container'>
    <el-row :gutter='20'>
      <!--部门数据-->
      <el-col :span='relaNav ? 4 : 0' :xs='24' class='leftDiv'>
        <!--折叠图标-->
        <div class='leftIcon' @click='relaNav = false'>
          <span class='el-icon-caret-left'></span>
        </div>
        <div class='head-container' style='width: 300px'>
          <el-tree
            ref='tree'
            :data='filteredTreeData'
            :default-expanded-keys='[1]'
            :expand-on-click-node='false'
            :props='defaultProps'
            highlight-current
            node-key='code'
            @node-click='handleNodeClick'
          >
          </el-tree>
        </div>
      </el-col>
      <!--角色数据-->
      <el-col :span='relaNav ? 20 : 24' :xs='24'>
        <!--展开图标-->
        <div v-show='!relaNav' class='rightIcon' @click='relaNav = true'>
          <span class='el-icon-caret-right'></span>
        </div>
        <el-row :gutter='10' class='mb8'>
          <el-col :span='1.5'>
            <el-button type='primary' @click='handleAdd'>新增配置</el-button>
          </el-col>
        </el-row>
        <!-- 筛选区结束 -->
        <!-- 数据表格开始 -->
        <div class='tableDiv'>
          <el-table v-adjust-table v-loading='loading' :data='dataList'
                    :height="'calc(100vh - 260px)'" border size='mini'>
            <el-table-column align='center' fixed label='序号' type='index' width='100'></el-table-column>
            <el-table-column align='center' label='配置名称' prop='specificTime' />
            <el-table-column align='center' fixed='right' label='操作' width='150'>
              <template slot-scope='scope'>
                <el-button
                  icon='el-icon-edit'
                  size='mini'
                  type='text'
                  @click='handleEdit(scope.row)'
                >编辑
                </el-button>
                <el-button
                  icon='el-icon-delete'
                  size='mini'
                  type='text'
                  @click='handleDelete(scope.row)'
                >删除
                </el-button>
              </template>
            </el-table-column>
          </el-table>
          <pagination
            v-show='total > 0'
            :limit.sync='queryParams.pageSize'
            :page.sync='queryParams.pageNum'
            :total='total'
            @pagination='handleQuery'
          />
        </div>
        <!-- 数据表格结束 -->
      </el-col>
    </el-row>
    <detail :current-structure='currentStructure'/>
  </div>
</template>

<script>
import { getJCDomainTree } from '@/api/jgjc/baseInfo/alarmLog'
import { getReportStructurePage } from '@/api/jgjc/assessmentReport/reportConfig'
import Detail from './detail.vue'

export default {
  name: 'ContentManagement',
  components: { Detail },
  dicts: ['report_type'],
  data() {
    return {
      // 遮罩层
      loading: false,
      // 左侧组织树
      relaNav: true,
      relaOptions: [],
      filteredTreeData: [],
      defaultProps: {
        children: 'children',
        label: 'label',
      },
      // 总条数
      total: 0,
      dataList: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
      },
      structureId: '',
      structureName: '',
      formData: {},
      currentStructure: {}
    }
  },
  created() {
    this.getDeptTree()
  },
  methods: {
    // 查询部门下拉树结构
    getDeptTree() {
      getJCDomainTree({}).then(response => {
        this.relaOptions = response.data
        this.filteredTreeData = [...this.relaOptions]
      })
    },
    // 树节点点击事件
    handleNodeClick(nodeData, node) {
      if (node.level == 4) {
        console.log(node)
        this.queryParams = {
          pageNum: 1,
          pageSize: 10,
          structureId: nodeData.structureId,
          level: node.level,
        }
        this.structureId = nodeData.structureId
        this.structureName = nodeData.label
        this.currentStructure = nodeData
        this.handleQuery()
      }
    },
    handleQuery() {
      if (!this.structureId) {
        this.$message.warning('请选择结构物')
        return
      }
      this.queryParams.structureId = this.structureId
      this.loading = true
      getReportStructurePage(this.queryParams).then(res => {
        this.dataList = res.rows
        this.total = res.total
        this.loading = false
      })
    },
    handleEdit(row) {
      this.formData = Object.assign({}, row)
      this.formData.reportYear = String(row.reportYear)
      this.visible = true
    },
    handleAdd() {
      console.log(this.currentStructure)
      if(!this.currentStructure?.structureId){
        this.$message({
          type: 'warning',
          message: '结构物未选择'
        });
        return;
      }
      window.$Bus.$emit("showConfig");
    },
    handleDelete(row) {
      this.$confirm('是否确认删除？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }).then(() => {
        this.loading = true
        deleteReportManageForCustomer({ ids: [row.id] }).then(res => {
          if (res.code === 200) {
            this.$message.success('删除成功')
            this.handleQuery()
          } else {
            this.$message.error(res.msg)
          }
          this.loading = false
        }).catch(err => {
          this.loading = false
          console.error(err)
        })
      })
    },
  },
}
</script>

<style scoped>
.leftDiv {
  border-right: 1px solid #d8dce5;
  min-height: calc(100vh - 100px);
  overflow-y: auto;
  height: calc(80vh - 150px);
  position: relative;
  top: -20px;
  padding-top: 10px;
  background-color: white;
}

.leftIcon {
  border: 1px solid #dcdfe6;
  border-radius: 8px;
  width: 16px;
  height: 50px;
  line-height: 50px;
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
  position: absolute;
  right: 0;
  top: 300px;
  z-index: 2;
}

.leftIcon:hover {
  background-color: #dcdfe6;
}

.rightIcon {
  border: 1px solid #dcdfe6;
  border-radius: 8px;
  width: 16px;
  height: 50px;
  line-height: 50px;
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
  position: absolute;
  left: -10px;
  top: 280px;
  z-index: 10;
  background: white;
}

.rightIcon:hover {
  background-color: #dcdfe6;
}

.tableDiv {
  margin-top: 20px;
}
</style>
<style lang='scss' scoped>
@import "@/assets/styles/business.scss";
</style>
