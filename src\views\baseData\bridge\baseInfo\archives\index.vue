<template>
  <div class="archives">
    <div
      class="archives-left"
      :class="isBig?'isBig-left':''"
      :style="{ backgroundColor: oneMap ? 'rgba(1, 102, 254, 0.2)' : ''}"
    >
      <div class="archives-left-head" :class="isBig?'isBig-head':''">
        <h5 >{{ title }}</h5>
      </div>
      <div class="archives-left-body" :class="isBig?'isBig-left-body':''">
        <el-button
          v-for="item in buttonList"
          :key="item.key"
          :disabled="item.disabled"
          plain
          :class="currentButton === item.key ? 'is-active' : ''"
          @click="buttonClick(item)"
          :style="oneMap ? 'background-color:unset;color:white;' : ''"
        >
          {{ item.label }}
        </el-button>
      </div>
    </div>
    <div
      class="archives-right"
      :style="{ backgroundColor: oneMap ? 'rgba(1, 102, 254, 0.1)' : '',fontSize: oneMap ? 'vwpx(28px)' : '14px;' }"
    >
      <component
        :is="currentButton"
        :id="id"
        :assetId="assetId"
        :assetCode="assetCode"
        :assetName="assetName"
        @getBaseInfo="getBaseInfo"
      />

    </div>
  </div>
</template>

<script>
import BaseInfo from './components/baseInfo.vue'
import Drawing from './components/drawing.vue'
import Periodic from './components/periodic.vue'
import UpperPositions from './components/upperPositions.vue'
import BridgeOften from './components/bridgeOften.vue'
import BridgeDaily from './components/bridgeDaily.vue'
import DailyMaintenance from './components/dailyMaintenance.vue'
import DiseaseProjInfo from './components/diseaseProjInfo.vue'
import { isBigScreen } from "@/views/map/components/common/util";


import { containsNumber } from '@turf/turf'

export default {
  name: 'BridgeBaseInfoArchives',
  components: { BaseInfo, Drawing, Periodic, UpperPositions,BridgeDaily,BridgeOften,DailyMaintenance,DiseaseProjInfo },
  props: {},
  inject: {
    oneMap: {
      default: false,
    },
  },
  data() {
    return {
      currentButton: 'BaseInfo',
      buttonList: [
        { key: 'BaseInfo', label: '基本信息', disabled: false },
        { key: 'Drawing', label: '文件图纸', disabled: false },
        { key: 'BridgeOften', label: '经常检查', disabled: false },
        { key: 'BridgeDaily', label: '日常巡查', disabled: false },
        { key: 'Periodic', label: '定期检查', disabled: false },
        { key: 'DailyMaintenance', label: '日常养护', disabled: false },
        { key: 'DiseaseProjInfo', label: '养护工程', disabled: false },
        { key: 'UpperPositions', label: '构件数据', disabled: false }
      ],
      id: '',
      assetCode:'',
      assetName:'',
      title: '',
      assetId:'',
      formData: {},
      isBig: isBigScreen(),
    }
  },
  computed: {},
  watch: {},
  created() {
    this.title =
      this.$route.query.bridgeName === 'null'
        ? ''
        : this.$route.query.bridgeName
    this.id = this.$route.query.id||''
    this.assetId = this.$route.query.assetId||''
    this.assetName = this.$route.query.assetName||''
    this.assetCode = this.$route.query.assetCode
    if (this.$route.query.whetherHealthMonitorSystemType == '1') {
      this.buttonList.push({
        key: 8,
        label: '结构监测',
        disabled: false
      })
    }
  },
  methods: {
    buttonClick(item) {
      if (item.label === '结构监测') {
        this.$modal
          .confirm('该操作将跳转页面，是否继续？')
          .then(() => {})
          .catch(() => {})
      } else {
        this.currentButton = item.key
      }
    },
    // 当baseInfo组件获取到数据后会调用这个方法
    getBaseInfo(data) {
      // 路由里不一定会有bridgeName和whetherHealthMonitorSystemType，所以需要从基本信息里取
      this.title = this.title ? this.title : data.bridgeName
      if (!this.$route.query.whetherHealthMonitorSystemType && data.whetherHealthMonitorSystemType == '1') {
        this.buttonList.push({
          key: 8,
          label: '结构监测',
          disabled: false
        })
      }
      this.assetId = this.assetId ? this.assetId : data.assetId
    },
    tabClick() {}
  }
}
</script>

<style lang="scss" scoped>
@import "@/assets/styles/utils.scss";

.archives {
  width: 100%;
  height: 100%;
  padding: 10px;
  display: flex;
  .archives-left {
    width: 220px;
    height: 100%;
    background-color: white;
    position: relative;
    border-radius: 10px;
    box-shadow: 0px 0px 10px 0px rgba(0, 0, 0, 0.2);
    font-size: 14px;

    .archives-left-head {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 52px;
      background: #409eff;
      border-radius: 10px 10px 0 0;
      display: flex;
      justify-content: center;
      align-items: center;
      h5 {
        font-family: Microsoft YaHei, Microsoft YaHei;
        font-weight: 700;
        color: #ffffff;
      }
      &.isBig-head{
        height: vwpx(104px);
        font-size: vwpx(28px);
      }
    }
    .archives-left-body {
      position: absolute;
      bottom: 0;
      left: 0;
      width: 100%;
      height: calc(100% - 52px);
      padding: 15px 10px;
      display: flex;
      flex-direction: column;
      &.isBig-left-body{
       
        height: calc(100% -  4.8148148148vmin );
        padding: vwpx(30px) vwpx(20px);

        ::v-deep  .el-button--mini{
          font-size: vwpx(28px);
        }
      }
      .el-button {
        width: 100%;
        margin-left: 0;
        margin-bottom: 8px;
        background: #f7f8fa;
        // border: 0;
        font-family: Microsoft YaHei, Microsoft YaHei;
        font-weight: 400;
        
        color: #333;
      }
      .el-button.is-disabled {
        color: #c0c4cc;
        cursor: not-allowed;
      }
      .is-active {
        background: #eff5ff;
        color: #409eff;
        border-color: #409eff;
      }
    }

    &.isBig-left{
        width:vwpx(440px);
     }
  }

  .archives-right {
    width: calc(100% - 220px);
    height: 100%;
    padding-left: 10px;
    border-radius: 10px;
    // box-shadow: 0px 0px 10px 0px rgba(76, 149, 255, 0.2);
  }
}
</style>
<style lang="scss">
</style>
