<template>
  <div class="bridge-detail">
    <section class="flex-1 ml-3">
      <CockpitCard title="桥梁基础信息" :w="isBig ? '12vw' : '24vw'" h="calc(57vh)" :class="isBig ? 'mb-2' : 'mb-3'"
        :isDtl="false">
        <div class="bridge-info">
          <div v-for="(item, index) in infoList" :key="index" class="info-list">
            <span class="name">{{ item.name }}</span>
            <span class="value">{{ item.value }}</span>
          </div>
        </div>
      </CockpitCard>
      <CockpitCard title="桥梁正面照" :w="isBig ? '12vw' : '24vw'" :h="isBig ? 'calc(25vh + 20px)' : 'calc(25vh)'"
        :class="isBig ? 'mb-2' : 'mb-3'" :isDtl="false">
        <img src="@/assets/cockpit/model.png" class="img" />
      </CockpitCard>
    </section>
    <section class="flex-1">
      <CockpitCard title="巡查情况" :w="isBig ? '12vw' : '24vw'" h="calc(57vh)" :class="isBig ? 'mb-2' : 'mb-3'"
        :isDtl="false">
        <Tables :columns="pColumns" :data="pData"></Tables>
      </CockpitCard>
      <div class="flex">
        <CockpitCard title="桥梁立面照" :w="isBig ? 'calc(6vw - 12px)' : 'calc(12vw - 6px)'" :isDtl="false"
          :h="isBig ? 'calc(25vh + 20px)' : 'calc(25vh)'" :class="isBig ? 'mb-2' : 'mb-3'">
          <img src="@/assets/cockpit/elevation.png" class="img" />
        </CockpitCard>
        <CockpitCard title="桥梁典型照" :w="isBig ? 'calc(6vw - 12px)' : 'calc(12vw - 6px)'" :isDtl="false"
          :h="isBig ? 'calc(25vh + 20px)' : 'calc(25vh)'" :class="isBig ? 'mb-2' : 'mb-3'" class="ml-3">
          <img src="@/assets/cockpit/info.png" class="img" />
        </CockpitCard>
      </div>
    </section>
    <section class="flex-1">
      <CockpitCard title="桥梁技术状况评定" :w="isBig ? '12vw' : '24vw'" :h="isBig ? 'calc(41vh + 3px)' : '41vh'" class="mb-3"
        :isDtl="false">
        <Echarts :option="pdOption" v-if="pdOption" height="40vh" key="pdKey" />
      </CockpitCard>
      <CockpitCard title="桥梁经常检查情况" :w="isBig ? '12vw' : '24vw'" :h="isBig ? 'calc(41vh + 3px)' : '41vh'"
        :isDtl="false">
        <div class="inspect-info">
          <div v-for="(item, index) in inspectList" :key="'in' + index" class="list">
            <span class="name">{{ item.name }}</span>
            <div :class="item.value == 1 ? 'normal' : 'error'"><span></span> {{ item.value == 1 ? '未见异常' : '出现异常' }}
            </div>
          </div>
        </div>
      </CockpitCard>
    </section>
    <section class="flex-1">
      <CockpitCard title="最新技术评定结果" :w="isBig ? '12vw' : '24vw'" :h="isBig ? 'calc(87vh + 12px)' : 'calc(87vh + 3px)'"
        :isDtl="false">
        <Echarts :option="option" v-if="option" height="25vh" key="dpKey" />
        <div class="pr-table">
          <Tables :columns="prColumns" :data="prData"></Tables>
        </div>
        <div class="mt-advice">
          <div class="advice-name">
            <img src="@/assets/cockpit/mt-advice.png" />
            <span>养护意见</span>
          </div>
          <div class="advice-cont">
            桥梁整体状况较为良好，应进行“日常养护、预防养护”针对较为严重的病害现象建议立即进行处置。
          </div>
        </div>
      </CockpitCard>
    </section>
  </div>
</template>

<script>
import * as echarts from 'echarts'
import { isBigScreen } from '../../util/utils';
import CockpitCard from '../cockpitCard.vue';
import Tables from '../tables.vue';
import Echarts from '../echarts/echarts.vue';

export default {
  components: {
    CockpitCard,
    Tables,
    Echarts,
  },
  data() {
    return {
      isBig: isBigScreen(),
      info: {},
      infoList: [
        {
          name: '管养单位：',
          value: '保山管理处',
        },
        {
          name: '路线编码：',
          value: 'G5615',
        },
        {
          name: '路段名称：',
          value: '保腾高速',
        },
        {
          name: '桥梁名称：',
          value: '龙江特大桥',
        },
        {
          name: '桥梁编码：',
          value: 'G5615530581L1234',
        },
        {
          name: '桥位桩号：',
          value: 'K818+022',
        },
        {
          name: '桥梁长度：',
          value: '2235.16米',
        },
        {
          name: '结构形式：',
          value: '悬索桥',
        },

        {
          name: '跨径组合：',
          value: '3*13',
        },
        {
          name: '跨径类型：',
          value: '特大桥',
        },
        {
          name: '桥梁位置：',
          value: '保山市',
        },
        {
          name: '下一次评定日期：',
          value: '2024年',
        },
        {
          name: '养护工程处置次数：',
          value: '1次',
        },
        {
          name: '日常养护次数：',
          value: '1次',
        },
        {
          name: '本月日常巡查次数：',
          value: '4次',
        },
        {
          name: '本月经常检查次数：',
          value: '4次',
        },
      ],
      pColumns: [
        {
          label: "巡查时间",
          prop: "time",
        },
        {
          label: "巡查人",
          prop: "name",
        },
        {
          label: "天气状况",
          prop: "weather",
        },
        {
          label: "是否异常",
          prop: "abnormal",
        },
      ],
      pData: [
        {
          time: "2024-08-29",
          name: "余伟",
          weather: "晴",
          abnormal: "否",
        },
        {
          time: "2024-08-29",
          name: "余伟",
          weather: "晴",
          abnormal: "否",
        },
        {
          time: "2024-08-29",
          name: "余伟",
          weather: "晴",
          abnormal: "否",
        },
        {
          time: "2024-08-29",
          name: "余伟",
          weather: "晴",
          abnormal: "否",
        },
        {
          time: "2024-08-29",
          name: "余伟",
          weather: "晴",
          abnormal: "否",
        },
        {
          time: "2024-08-29",
          name: "余伟",
          weather: "晴",
          abnormal: "否",
        },
        {
          time: "2024-08-29",
          name: "余伟",
          weather: "晴",
          abnormal: "否",
        },
        {
          time: "2024-08-29",
          name: "余伟",
          weather: "晴",
          abnormal: "否",
        },
        {
          time: "2024-08-29",
          name: "余伟",
          weather: "晴",
          abnormal: "否",
        },
        {
          time: "2024-08-29",
          name: "余伟",
          weather: "晴",
          abnormal: "否",
        },
        {
          time: "2024-08-29",
          name: "余伟",
          weather: "晴",
          abnormal: "否",
        },
        {
          time: "2024-08-29",
          name: "余伟",
          weather: "晴",
          abnormal: "否",
        },
        {
          time: "2024-08-29",
          name: "余伟",
          weather: "晴",
          abnormal: "否",
        },
        {
          time: "2024-08-29",
          name: "余伟",
          weather: "晴",
          abnormal: "否",
        },
        {
          time: "2024-08-29",
          name: "余伟",
          weather: "晴",
          abnormal: "否",
        },
        {
          time: "2024-08-29",
          name: "余伟",
          weather: "晴",
          abnormal: "否",
        },

        {
          time: "2024-08-29",
          name: "余伟",
          weather: "晴",
          abnormal: "否",
        },
        {
          time: "2024-08-29",
          name: "余伟",
          weather: "晴",
          abnormal: "否",
        },
        {
          time: "2024-08-29",
          name: "余伟",
          weather: "晴",
          abnormal: "否",
        },
        {
          time: "2024-08-29",
          name: "余伟",
          weather: "晴",
          abnormal: "否",
        },
      ],
      pdOption: null,
      inspectList: [
        {
          name: '减震装置',
          value: 1,
        },
        {
          name: '调治构造物',
          value: 1,
        },
        {
          name: '排水系统',
          value: 0,
        },
        {
          name: '主梁',
          value: 1,
        },
        {
          name: '其他',
          value: 1,
        },
        {
          name: '标志、标线',
          value: 0,
        },
        {
          name: '栏杆、护栏',
          value: 1,
        },
        {
          name: '桥面铺装',
          value: 1,
        },
        {
          name: '伸缩缝',
          value: 1,
        },
        {
          name: '桥路连接处(桥头搭板)',
          value: 1,
        },
        {
          name: '支座',
          value: 1,
        },
        {
          name: '桥墩及基础(含冲刷)',
          value: 1,
        },
      ],
      option: null,
      prColumns: [{
        label: "部件名称",
        prop: "name",
      }, {
        label: "部件得分",
        prop: "score",
      }, {
        label: "部件等级",
        prop: "level",
      },],
      prData: [
        {
          name: '上部结构',
          score: '92.74',
          level: '2类',
        },
        {
          name: '上部结构',
          score: '92.74',
          level: '2类',
        },
        {
          name: '上部结构',
          score: '92.74',
          level: '2类',
        },
        {
          name: '上部结构',
          score: '92.74',
          level: '2类',
        },
        {
          name: '上部结构',
          score: '92.74',
          level: '2类',
        },
        {
          name: '上部结构',
          score: '92.74',
          level: '2类',
        },
        {
          name: '上部结构',
          score: '92.74',
          level: '2类',
        },
        {
          name: '上部结构',
          score: '92.74',
          level: '2类',
        },
        {
          name: '上部结构',
          score: '92.74',
          level: '2类',
        },
        {
          name: '上部结构',
          score: '92.74',
          level: '2类',
        },
        {
          name: '上部结构',
          score: '92.74',
          level: '2类',
        },
      ],
    }
  },
  mounted() {
    this.initPdEcharts();
    // 总得分
    this.initEcharts();
  },
  methods: {
    initPdEcharts() {
      const data = [40, 66, 57, 48, 68, 76, 50, 62, 54];
      const sideData = data.map((item) => item + 1);

      this.pdOption = {
        backgroundColor: "rgba(0,0,0,0)",
        title: [
          {
            text: "2022年9月评定结果：二类桥(89.07分)",
            left: "center",
            top: "2%",
            textStyle: {
              color: "#42ABFF",
            },
          },
        ],
        grid: {
          top: "15%",
          left: "3%",
          right: "3%",
          bottom: "2%",
          containLabel: true,
        },
        xAxis: {
          data: ["2016", "2017", "2018", "2019", "2020", "2021", "2022", "2023", "2024"],
          //坐标轴
          axisLine: {
            lineStyle: {
              color: "rgba(110,112,121,0.5)",
            },
          },
          //坐标值标注
          axisLabel: {
            show: true,
            textStyle: {
              color: "#fff",
            },
          },
        },
        yAxis: {
          //坐标轴
          axisLine: {
            show: false,
          },
          //坐标值标注
          axisLabel: {
            show: true,
            textStyle: {
              color: "#999999",
            },
          },
          //分格线
          splitLine: {
            lineStyle: {
              color: "rgba(110,112,121,0.5)",
            },
          },
          splitArea: {
            show: false,
          },
          interval: 10,
        },
        series: [
          {
            name: "a",
            tooltip: {
              show: false,
            },
            label: {
              normal: {
                show: true,
                position: 'top',
                fontSize: 16,
                color: '#fff',
                offset: [0, -10],
              },
            },
            type: "bar",
            barWidth: 24.5,
            itemStyle: {
              normal: {
                color: new echarts.graphic.LinearGradient(
                  0,
                  1,
                  0,
                  0,
                  [
                    {
                      offset: 0,
                      color: "rgba(0,255,251,0.1)", // 0% 处的颜色
                    },
                    {
                      offset: 0.6,
                      color: "rgba(0,255,251,0.6)", // 60% 处的颜色
                    },
                    {
                      offset: 1,
                      color: "rgba(0,255,251,1)", // 100% 处的颜色
                    },
                  ],
                  false
                ),
              },
            },
            data: data,
            barGap: 0,
          },
          {
            type: "bar",
            barWidth: 8,
            itemStyle: {
              normal: {
                color: new echarts.graphic.LinearGradient(
                  0,
                  1,
                  0,
                  0,
                  [
                    {
                      offset: 0,
                      color: "rgba(0,255,251,0.1)", // 0% 处的颜色
                    },
                    {
                      offset: 0.6,
                      color: "rgba(0,255,251,0.4)", // 60% 处的颜色
                    },
                    {
                      offset: 1,
                      color: "rgba(0,255,251,0.6)", // 100% 处的颜色
                    },
                  ],
                  false
                ),
              },
            },
            barGap: 0,
            data: sideData,
          },
          {
            name: "b",
            tooltip: {
              show: false,
            },
            type: "pictorialBar",
            itemStyle: {
              borderWidth: 1,
              borderColor: "rgba(125,255,253,0.7)",
              color: "#7DFFFD",
            },

            symbol: "path://M 0,0 l 120,0 l -30,60 l -120,0 z",
            symbolSize: ["30", "12"],
            symbolOffset: ["0", "-8"],
            symbolRotate: -15,
            symbolPosition: "end",
            data: data,
            z: 3,
          },
        ],
      };

    },
    initEcharts() {
      let angle = 0; //角度，用来做简单的动画效果的
      let num = 92.02;
      let unit = '分'
      let title = '总体得分'
      this.option = {
        tooltip: {
          formatter: "{a} <br/>{b} : {c}%",
        },
        title: [
          {
            // text: num + "%",
            text: '{a|' + num + '}{b| ' + unit + '}\n{c|' + title + '}',
            x: "center",
            y: "center",
            textStyle: {
              fontSize: "30",
              color: "#fff",
              fontFamily: "Lato",
              foontWeight: "600",
              rich: {
                a: {
                  fontSize: this.nowSize(20),
                  color: '#fff',
                  fontWeight: '700',
                },
                b: {
                  fontSize: this.nowSize(8),
                  color: '#B6B6B6',
                  padding: [0, 0, 0, 3]
                },
                c: {
                  fontSize: this.nowSize(12),
                  color: '#1CFFBC',
                  padding: [5, 0],
                  fontWeight: '400',
                }
              }
            },
          },
        ],
        grid: {
          top: "10%",
          left: "10%",
          right: "10%",
          bottom: "10%",
          containLabel: true,
        },
        series: [
          //内圆
          {
            type: "pie",
            radius: "55%",
            center: ["50%", "50%"],
            animation: false,
            z: 3,
            itemStyle: {
              normal: {
                color: "rgba(28,255,188,0.1)",
                label: {
                  show: false,
                },
                labelLine: {
                  show: false,
                },
                borderColor: "#1CFFBC",
                borderWidth: 1,
              },
            },
            hoverAnimation: false,
            label: {
              show: false,
            },
            tooltip: {
              show: false,
            },
            data: [100],
          },
          // 进度光环
          {
            name: "外部刻度",
            type: "gauge",
            z: 2,
            center: ["50%", "50%"],
            radius: "80%",
            min: 0, //最小刻度
            max: 100, //最大刻度
            splitNumber: 10, //刻度数量
            startAngle: 225,
            endAngle: -45,
            axisLine: {
              show: true,
              roundCap: true,
              lineStyle: {
                width: 10,
                color: [[num / 100, "#1CFFBC"]],
              },
            }, // 仪表盘轴线
            axisLabel: {
              show: false,
            }, //刻度标签。
            axisTick: {
              show: false,
            }, //刻度样式
            splitLine: {
              show: false,
            }, //分隔线样式
            detail: {
              show: false,
            }, //仪表盘详情，用于显示数据
            pointer: {
              show: false,
            }, //仪表盘指针。
          },
          // 进度光环背景
          {
            name: "外部刻度",
            type: "gauge",
            z: 1,
            center: ["50%", "50%"],
            radius: "80%",
            min: 0, //最小刻度
            max: 100, //最大刻度
            splitNumber: 10, //刻度数量
            startAngle: 225,
            endAngle: -45,
            animation: true,
            axisLine: {
              show: true,
              roundCap: true, // 设置为圆角
              lineStyle: {
                width: 10,
                color: [[1, "rgba(23,116,255,0.2)"]],
                borderRadius: 25,
                borderWidth: 10,
              },
            }, // 仪表盘轴线
            axisLabel: {
              show: false,
            }, //刻度标签。
            axisTick: {
              show: false,
            }, //刻度样式
            splitLine: {
              show: false,
            }, //分隔线样式
            detail: {
              show: false,
            }, //仪表盘详情，用于显示数据
            pointer: {
              show: false,
            }, //仪表盘指针。
            itemStyle: {
              normal: {
                borderRadius: 25,
              },
            },
          },

        ],
      };
    },
    nowSize(val) {
      let w = window.innerWidth || window.screen.width || window.screen.availWidth || 1920;
      let initW = this.isBig ? w * 3 : 1920
      let size = val * initW / w;
      return size;
    },
  },
}
</script>

<style lang="scss" scoped>
@import "@/assets/styles/utils.scss";

.bridge-detail {
  width: 100%;
  display: flex;

  .bridge-info {
    padding: vwpx(20px);

    .info-list {
      width: 100%;
      height: vwpx(70px);
      background-color: rgba(23, 116, 255, 0.1);
      margin: vwpx(4px) 0;
      padding: 0 vwpx(10px);

      display: flex;
      align-items: center;

      .name {
        font-family: Microsoft YaHei, Microsoft YaHei;
        font-weight: 400;
        font-size: vwpx(26px);
        color: #42ABFF;
      }

      .value {
        font-family: Microsoft YaHei, Microsoft YaHei;
        font-weight: 400;
        font-size: vwpx(26px);
        color: #FFFFFF;
        margin-left: vwpx(20px);
      }
    }
  }

  .inspect-info {
    padding: vwpx(20px);

    .list {
      height: vwpx(64px);
      background-color: rgba(23, 116, 255, 0.1);

      display: flex;
      align-items: center;
      justify-content: space-between;
      margin: vwpx(6px) 0;
      padding: 0 vwpx(10px);

      .name {
        font-family: Microsoft YaHei, Microsoft YaHei;
        font-weight: 400;
        font-size: vwpx(26px);
        color: #00CBFF;
      }

      .normal {
        font-family: Microsoft YaHei, Microsoft YaHei;
        font-weight: 400;
        font-size: vwpx(26px);
        color: #1CFFBC;
        display: flex;
        align-items: center;

        span {
          width: vwpx(15px);
          height: vwpx(15px);
          display: block;
          border-radius: 50%;
          background-color: #1CFFBC;
          margin-right: vwpx(10px);
        }
      }

      .error {
        font-family: Microsoft YaHei, Microsoft YaHei;
        font-weight: 400;
        font-size: vwpx(26px);
        color: #F53F3F;
        display: flex;
        align-items: center;

        span {
          width: vwpx(15px);
          height: vwpx(15px);
          display: block;
          border-radius: 50%;
          background-color: #F53F3F;
          margin-right: vwpx(10px);
        }
      }
    }
  }

  .pr-table {
    border: 1px solid #2CB5FF;
    margin: 0 vwpx(20px);
    height: 44vh;
  }

  .mt-advice {
    border: 1px solid #2CB5FF;
    height: vwpx(320px);
    background-color: rgba(23, 116, 255, 0.1);
    margin: vwpx(40px) vwpx(20px);

    display: flex;
    align-items: center;

    .advice-name {
      display: flex;
      align-items: center;
      justify-content: center;
      flex-direction: column;
      margin: 0 vwpx(20px);

      img {
        width: vwpx(130px);
        height: vwpx(130px);
      }

      span {
        font-family: Source Han Sans, Source Han Sans;
        font-weight: 500;
        font-size: vwpx(28px);
        color: #42ABFF;
        margin-top: vwpx(20px);
      }
    }

    .advice-cont {
      flex: 1;
      height: 100%;
      background: rgba(23, 116, 255, 0.1);
      font-family: Microsoft YaHei UI, Microsoft YaHei UI;
      font-weight: 400;
      font-size: vwpx(26px);
      color: #FFFFFF;

      display: flex;
      align-items: center;
      padding: vwpx(30px);
    }
  }

  .flex {
    display: flex;
  }

  .flex-1 {
    flex: 1;
  }

  .mb-2 {
    margin-bottom: vwpx(20px);
  }

  .mb-3 {
    margin-bottom: vwpx(30px);
  }

  .ml-3 {
    margin-left: vwpx(30px);
  }

  .img {
    width: 100%;
    height: 100%;
    padding: 5px;
    cursor: pointer;
    object-fit: fill;
  }
}
</style>