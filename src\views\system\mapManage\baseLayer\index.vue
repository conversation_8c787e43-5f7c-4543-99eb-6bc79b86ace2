<template>
  <PageContainer>
    <template slot="search">
      <el-row :gutter="20">
        <el-col :span="3" :offset="0">
          <el-input
            v-model="queryParams.layerName"
            placeholder="请输入图层名称"
            clearable
            size="mini"
          />
        </el-col>
        <el-col :span="3" :offset="0">
          <el-select
            v-model="queryParams.status"
            placeholder="请选择数据状态"
            clearable
            size="mini"
          >
            <el-option label="暂存" :value="1"> </el-option>
            <el-option label="启用" :value="2"> </el-option>
          </el-select>
        </el-col>
        <el-col :span="6" :offset="0">
          <el-button
            type="primary"
            icon="el-icon-search"
            size="mini"
            @click="handleQuery"
          >
            搜索
          </el-button>
          <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">
            重置
          </el-button>
        </el-col>
      </el-row>
    </template>
    <template slot="header">
      <el-row :gutter="20">
        <el-col :span="1.5" :offset="0" class="mb8">
          <el-button v-hasPermi="['baseData:culvert:add']" type="primary" size="mini" @click="handleAdd">
            新增
          </el-button>
        </el-col>
        <el-col :span="1.5" :offset="0" class="mb8">
          <el-button
            v-hasPermi="['baseData:culvert:edit']"
            :disabled="!(tableSelects.length === 1)"
            type="primary"
            size="mini"
            @click="handleUpdate"
          >
            编辑
          </el-button>
        </el-col>
        <el-col :span="1.5" :offset="0" class="mb8">
          <el-button
            v-hasPermi="['baseData:culvert:delete']"
            :disabled="!(tableSelects.length > 0)"
            type="primary"
            size="mini"
            @click="handleDelete"
          >
            删除
          </el-button>
        </el-col>
        <el-col :span="1.5" :offset="0" class="mb8">
          <el-button
            v-hasPermi="['baseData:culvert:getInfoById']"
            :disabled="!(tableSelects.length === 1)"
            type="primary"
            size="mini"
            @click="onDetail"
          >
            查看
          </el-button>
        </el-col>
        <!-- <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar> -->
      </el-row>
    </template>
    <template slot="body">
      <el-table v-adjust-table :data="layerList" @selection-change="handleSelectionChange" @row-click="handleRowClick" border height="100%" 
        ref="tableRef" style="width: 100%" :row-key="(row)=>row.id">
        <el-table-column type="selection" width="50" align="center" :reserve-selection="true" fixed/>
        <el-table-column
          v-for="col in columns"
          :prop="col.prop"
          :key="col.id"
          :label="col.label"
          :min-width="col.width"
          align="center"
          show-overflow-tooltip
        >
          <template #default="{row}">
            <el-link :underline="false" v-if="col.type && col.type=='select'">{{ col.options[row[col.prop]] }}</el-link>
            <span v-else>{{ row[col.prop] }}</span>
          </template>
        </el-table-column>
      </el-table>
      <pagination
        v-show="total > 0"
        :total="total"
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
        @pagination="getList"
      />
    </template>

    <!-- 新增编辑查看 -->
    <Dialog :title="title" :show.sync="open">
      <AddOrEdit :form="form" @close="handleClose" @refresh="getList" :readonly="readonly"/>
    </Dialog>
  </PageContainer>
</template>

<script>
import {
  getListPage,
  getBaseLayerById,
  delBaseLayer,
  addLayer,
  updateLayer,
} from "@/api/oneMap/layer";
import { fields } from "./columns";
import AddOrEdit from "./components/addOrEdit.vue";
import Dialog from "@/components/Dialog/index.vue";

export default {
  name: "Layer",
  components: {
    AddOrEdit,
    Dialog,
  },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 图层地图服务设置表格数据
      layerList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        regionCode: null,
        layerUrl: null,
        layerRange: null,
        layerName: null,
        mapMatrixSet: null,
        mapProjection: null,
        serviceType: null,
        centerPoint: null,
        minZoom: null,
        maxZoom: null,
        showIndex: null,
        ifShow: null,
        description: null,
        defaultMap: null,
      },
      // 表单参数
      form: {
        layerUrl: ''
      },
      tableSelects: [],
      columns: fields, // 列表字段
      title: "", // 新增编辑查看弹窗标题
      readonly: false,
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询图层地图服务设置列表 */
    getList() {
      this.loading = true;
      getListPage(this.queryParams).then((response) => {
        this.layerList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        regionCode: null,
        layerUrl: null,
        layerRange: null,
        layerName: null,
        mapMatrixSet: null,
        mapProjection: null,
        serviceType: null,
        centerPoint: null,
        minZoom: null,
        maxZoom: null,
        showIndex: null,
        ifShow: null,
        description: null,
        defaultMap: null,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(data) {
      this.tableSelects = data || [];
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加图层地图服务设置";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      let id = this.tableSelects[0].id || ''
      getBaseLayerById(id).then((response) => {
        this.form = response.data;
        this.open = true;
        this.title = "修改图层地图服务设置";
      });
    },
    handleClose(e){ 
      this.open = e
      this.readonly = false;
    },
    /** 删除按钮操作 */
    handleDelete() {
      let ids = this.tableSelects.map(v=>v.id)
      this.$modal
        .confirm(
          '是否确认删除图层地图服务？'
        )
        .then(function () {
          return delBaseLayer(ids);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");

        })
        .catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download(
        "oneMap/layer/export",
        {
          ...this.queryParams,
        },
        `layer_${new Date().getTime()}.xlsx`
      );
    },
    handleRowClick(row) {
      let arr = this.tableSelects.filter((v) => v.id == row.id);
      // 点击行 选中复选框
      if (arr && arr.length > 0) {
        this.$refs.tableRef.toggleRowSelection(row, false);
        this.tableSelects = this.tableSelects.filter((v) => v.id != row.id);
      } else {
        this.tableSelects = [...this.tableSelects, ...[row]];
        this.$refs.tableRef.toggleRowSelection(row, true);
      }
    },
    // 查看详情
    onDetail() {
      this.reset();
      this.readonly = true;
      let id = this.tableSelects[0].id || ''
      getBaseLayerById(id).then((response) => {
        this.form = response.data;
        this.open = true;
        this.title = "查看图层地图服务设置";
      });
    },
  },
};
</script>
