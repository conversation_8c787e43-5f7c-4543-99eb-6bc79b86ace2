<template>
  <div class="app-container">
    <el-row :gutter="20" style="display: flex;">
      <el-col :span="24" :xs="24">
        <el-form ref="queryForm"
          :inline="true"
          :model="queryParams"
          :rules="rules"
          label-width="68px"
          size="mini"
        >
          <el-form-item label="" prop="year">
            <el-date-picker
              v-model="queryParams.year"
              placeholder="年份"
              @change="getNumbers"
              style="width: 240px"
              type="year"
              value-format="yyyy"
            >
            </el-date-picker>
          </el-form-item>
          <el-form-item prop="number">
            <el-select
              v-model="queryParams.number"
              placeholder="期数"
              clearable
              style="width: 100%"
            >
              <el-option
                v-for="item in numbers"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              >
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item prop="domainId">
            <selectTree
              :key="'domainId'"
              v-model="queryParams.domainId"
              :deptType="100"
              only-select-child
              :deptTypeList="[1, 3]"
              clearable
              filterable
              placeholder="管养单位"
              style="width: 240px"
            />
          </el-form-item>
          <el-form-item>
            <el-button
              icon="el-icon-search"
              size="mini"
              type="primary"
              @click="handleQuery"
            >搜索
            </el-button>
            <el-button
              icon="el-icon-refresh"
              size="mini"
              @click="resetQuery"
            >重置
            </el-button
            >
            <el-button
              type="success"
              icon="el-icon-download"
              size="mini"
              @click="handleDownload"
            >导出报表
            </el-button>
          </el-form-item>
        </el-form>
        <el-row :gutter="10" class="mb8" v-loading="loading" style="height: 82vh">
          <iframe v-if="reportData.html" :srcdoc="reportData.html" frameborder="0" style="height: 100%" width="100%"></iframe>
        </el-row>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import selectTree from "@/components/DeptTmpl/selectTree.vue";
import {getNumbersByYear} from "@/api/dailyMaintenance/metering/settlementApplication";
import {s01report} from "@/api/calculate/report/actualCompletion";
import axios from "axios";

export default {
  components: {selectTree},
  data() {
    return {
      loading: false,
      queryParams: {},
      numbers: [],
      reportData: {},
      rules: {
        year: [
          { required: true, message: '请选择年份', trigger: 'change' }
        ],
        number: [
          { required: true, message: '请选择期数', trigger: 'change' }
        ],
        domainId: [
          { required: true, message: '请选择管养单位', trigger: 'change' }
        ]
      }
    }
  },
  methods: {
    handleQuery() {
      this.$refs.queryForm.validate(valid => {
        if (!valid) return
        this.loading = true
        s01report(this.queryParams).then(res => {
          this.reportData = res.data
        }).finally(() => {
          this.loading = false
        })
      });
    },
    resetQuery() {
      this.queryParams = {}
    },
    handleDownload() {
      if (!this.reportData.downUrl) {
        this.$message.warning('暂无数据')
        return
      }
      axios({
        method: "get",
        responseType: 'arraybuffer',
        url: this.reportData.downUrl,
        headers: {}
      }).then((res) => {
        const arrayBuffer = res.data;
        // 创建一个Blob对象
        const blob = new Blob([arrayBuffer], {type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'}); // 对于.xls文件
        saveAs(blob, this.reportData.fileName)
      })
    },
    getNumbers() {
      this.numbers = []
      this.$set(this.queryParams, 'number', null)
      getNumbersByYear({year: this.queryParams.year}).then((res) => {
        res.rows.forEach((item) => {
          this.numbers.push({
            label: item.name,
            value: item.number,
          });
        });
      });
    },
  }
}
</script>

<style scoped lang="scss">
.app-container {
  padding-bottom: 0px;
}
</style>
<style lang="scss" scoped>
@import "@/assets/styles/business.scss";
</style>
