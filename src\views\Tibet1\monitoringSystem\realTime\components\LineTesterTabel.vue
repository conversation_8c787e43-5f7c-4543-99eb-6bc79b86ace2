<template>
  <div class="data-table-container">
    <!-- 分页和时间范围显示 -->
 <!--   <div class="pagination-header">
      <el-pagination
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="currentPage"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="pageSize"
        layout="prev, pager, next"
        :total="total"
        background
        class="custom-pagination">
      </el-pagination>
      <div class="info-section">
        <span class="total-info">共{{ total }}条</span>
        <span class="date-range">{{ dateRange }}</span>
      </div>
    </div> -->

    <!-- 数据表格 -->
    <el-table
      :data="processedTableData"
	  height="720"
	  max-height="720"
      style="width: 100%"
      class="custom-table"
      :header-cell-style="headerCellStyle"
      :cell-style="cellStyle"
      :row-style="rowStyle">
      
      <el-table-column
        prop="serialNumber"
        label="序号"
        width="80"
        align="center">
      </el-table-column>
      
      <el-table-column
        prop="deviceLocation"
        label="设备位置"
        width="120"
        align="center">
        <template #default>
          | <!-- 固定值 -->
        </template>
      </el-table-column>
      
      <el-table-column
        prop="lineStatus"
        label="线路状态"
        width="100"
        align="center">
        <template #default="{ row }">
          <span :style="{ color: row.lineStatus === '通' ? '#67C23A' : '#F56C6C' }">
            {{ row.lineStatus }}
          </span>
        </template>
      </el-table-column>
      
      <el-table-column
        prop="deviceAddress"
        label="设备地址"
        width="100"
        align="center">
        <template #default>
          2 <!-- 固定值 -->
        </template>
      </el-table-column>
      
      <el-table-column
        prop="reportPeriod"
        label="正常上报周期(s)"
        width="140"
        align="center">
        <template #default>
          30 <!-- 固定值 -->
        </template>
      </el-table-column>
      
      <el-table-column
        prop="allowedNoReply"
        label="允许未回复次数"
        width="140"
        align="center">
        <template #default>
          10 <!-- 固定值 -->
        </template>
      </el-table-column>
      
      <el-table-column
        prop="voltage"
        label="电源电压(V)"
        width="120"
        align="center">
        <template #default>
          11.80 <!-- 固定值 -->
        </template>
      </el-table-column>
      
      <el-table-column
        prop="reportTime"
        label="上报时间"
        min-width="180"
        align="center">
      </el-table-column>
    </el-table>
  </div>
</template>

<script>
export default {
  name: 'LineTesterTabel',
  props: {
    sourceData: {
      type: Array,
      default: () => []
    },
    dateRange: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      currentPage: 1,
      pageSize: 10,
      // 其他固定字段值
      fixedValues: {
        deviceLocation: '|',
        deviceAddress: 2,
        reportPeriod: 30,
        allowedNoReply: 10,
        voltage: '11.80'
      }
    }
  },
  computed: {
    // 处理后的表格数据
    processedTableData() {
      if (!this.sourceData.length || !this.sourceData[0].times) return []
      
      return this.sourceData[0].times.map((time, index) => {
        const statusValue = this.sourceData[0].values[index]
        return {
          serialNumber: index + 1,
          reportTime: time,
          lineStatus: statusValue === 1 ? '通' : '不通',
          ...this.fixedValues
        }
      })
    },
    // 总条数
    total() {
      return this.processedTableData.length
    },
    headerCellStyle() {
      return {
        backgroundColor: '#1e3a5f',
        color: '#ffffff',
        fontSize: '14px',
        fontWeight: 'normal',
        textAlign: 'center',
        borderRight: '1px solid #2d4a6b'
      }
    },
    cellStyle() {
      return {
        backgroundColor: '#2d4a6b',
        color: '#ffffff',
        fontSize: '13px',
        textAlign: 'center',
        borderRight: '1px solid #3d5a7b',
        borderBottom: '1px solid #3d5a7b'
      }
    },
    rowStyle() {
      return {
        backgroundColor: '#2d4a6b'
      }
    }
  },
  methods: {
    handleSizeChange(val) {
      this.pageSize = val
      console.log(`每页 ${val} 条`)
    },
    handleCurrentChange(val) {
      this.currentPage = val
      console.log(`当前页: ${val}`)
    }
  }
}
</script>

<style scoped>
/* 保持原有的样式不变 */
.data-table-container {
  background-color: #1a2332;
  padding: 16px;
  border-radius: 4px;
}

.pagination-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding: 8px 0;
}

.custom-pagination {
  background-color: transparent;
}

.custom-pagination >>> .el-pager li {
  background-color: #2d4a6b;
  color: #ffffff;
  border: 1px solid #3d5a7b;
  margin: 0 2px;
  border-radius: 4px;
}

.custom-pagination >>> .el-pager li.active {
  background-color: #409eff;
  color: #ffffff;
}

.custom-pagination >>> .el-pager li:hover {
  background-color: #409eff;
  color: #ffffff;
}

.custom-pagination >>> .btn-prev,
.custom-pagination >>> .btn-next {
  background-color: #2d4a6b;
  color: #ffffff;
  border: 1px solid #3d5a7b;
  border-radius: 4px;
}

.custom-pagination >>> .btn-prev:hover,
.custom-pagination >>> .btn-next:hover {
  background-color: #409eff;
  color: #ffffff;
}

.info-section {
  display: flex;
  align-items: center;
  gap: 20px;
  color: #ffffff;
  font-size: 14px;
}

.total-info {
  color: #ffffff;
}

.date-range {
  color: #ffffff;
}

.custom-table {
  background-color: #2d4a6b;
  border: 1px solid #3d5a7b;
}

.custom-table >>> .el-table__header-wrapper {
  background-color: #1e3a5f;
}

.custom-table >>> .el-table__body-wrapper {
  background-color: #2d4a6b;
}

.custom-table >>> .el-table__row {
  background-color: #2d4a6b;
}

.custom-table >>> .el-table__row:hover > td {
  background-color: #3d5a7b !important;
}

.custom-table >>> .el-table td,
.custom-table >>> .el-table th {
  border-bottom: 1px solid #3d5a7b;
  border-right: 1px solid #3d5a7b;
}

.custom-table >>> .el-table--border::after,
.custom-table >>> .el-table--group::after,
.custom-table >>> .el-table::before {
  background-color: #3d5a7b;
}

.custom-table >>> .el-table--border,
.custom-table >>> .el-table--group {
  border: 1px solid #3d5a7b;
}

.el-table th.el-table__cell > .cell,.el-table .cell{
  color: white;
}

.data-table-container .custom-table >>> .el-table__header .cell {
  color: white !important;
}
</style>