// 基本信息
const baseInfo = [
  {
    label: '养护路段',
    prop: 'maintenanceSectionId',
    placeholder: '请选择养护路段',
    type: 'select',
    rules: [{required: true, message: '请选择养护路段', trigger: 'change'}],
    options: [],
    api: 'maintenanceSectionList',
    optionLabel: 'maintenanceSectionName',
    optionValue: 'maintenanceSectionId',
    disabledFieds: 'managementMaintenanceBranchId'
  },
  {
    label: '路段类型',
    prop: 'sectionType',
    placeholder: '请选择路段类型',
    type: 'roadType',
    options: []
  },
  {
    label: '路线编码',
    prop: 'routeCode',
    placeholder: '请选择路线编码',
    type: 'select',
    rules: [{required: true, message: '请选择路线编码', trigger: 'change'}],
    options: [],
    api: 'routeListAll',
    optionLabel: 'routeCode',
    optionValue: 'routeCode',
    disabledFieds: 'maintenanceSectionId'
  },
  {
    label: '路线名称',
    prop: 'routeName',
    placeholder: '请输入路线名称',
    type: 'select',
    api: 'routeListAll',
    optionLabel: 'routeName',
    optionValue: 'routeName',
    disabledFieds: '',
    rules: [{ required: true, message: '路线名称不能为空，选择路线编码自动带出。', trigger: 'change' }],
  },
  {
    label: '路线技术等级',
    prop: 'routeLevel',
    placeholder: '请选择路线技术等级',
    // type: 'dictSelect',
    multiple: true,
    dict: 'sys_route_grade',
    disabledFieds: '',
    type: 'multiDictSelect',
    rules: [{required: true, message: '路线技术等级不能为空，选择养护路段自动带出。', trigger: 'change'}],
  },
  {
    label: '涵洞编码',
    prop: 'culvertCode',
    placeholder: '请输入涵洞编码',
    type: 'input',
    rules: {required: true, message: '请输入涵洞编码', trigger: 'blur'}
  },
  {
    label: '涵洞经纬度',
    propLon: 'longitude',
    propLat: 'latitude',
    placeholder: '经纬度',
    type: 'coordinate',
    prepend: 'lonlat',
    // span: 24
  },

  {
    label: '涵洞类型',
    prop: 'culvertType',
    placeholder: '请选择涵洞类型',
    type: 'dictSelect',
    dict: 'sys_culvert_type',
    rules: {required: true, message: '请选择涵洞类型', trigger: 'change'}
  },
  {
    label: '中心桩号',
    prop: 'centerStake',
    placeholder: '请输入中心桩号',
    type: 'pileInput',
    rules: {required: true, message: '请输入中心桩号', trigger: 'change'}
  },

  {
    label: '施工桩号',
    prop: 'constructionStake',
    placeholder: '请输入施工桩号',
    type: 'pileInput'
  },
  {
    label: '匝道编码',
    prop: 'rampCode',
    placeholder: '请输入匝道编码',
    type: 'input',
  },
  {
    label: '涵洞跨径(m)',
    prop: 'culvertSpan',
    placeholder: '请输入涵洞跨径(m)',
    type: 'inputNumber',
  },
  {
    label: '移交管理单位',
    prop: 'transferManagementUnit',
    placeholder: '请输入移交管理单位',
    type: 'input',
  },
  {
    label: '建成时间',
    prop: 'buildDate',
    placeholder: '选择建成时间',
    type: 'date'
  },

  {
    label: '进洞口图片',
    prop: 'entranceHoleImageId',
    placeholder: '请选择文件',
    type: 'uploadImg',
    storagePath: "/base/culvert/culvertInfo"
  },
  {
    label: '出洞口图片',
    prop: 'exitHoleImageId',
    placeholder: '请选择文件',
    type: 'uploadImg',
    storagePath: "/base/culvert/culvertInfo"
  },
]

// 结构信息
const structureInfo = [
  {
    label: '设计荷载',
    prop: 'designLoad',
    placeholder: '请选择设计荷载',
    type: 'dictSelect',
    dict: 'sys_design_load'
  },
  {
    label: '涵身长度(m)',
    prop: 'culvertLength',
    placeholder: '请输入涵身长度(m)',
    type: 'inputNumber',
  },
  {
    label: '孔径(m)',
    prop: 'aperture',
    placeholder: '请输入孔径(m)',
    type: 'inputNumber',
  },
  {
    label: '净高(m)',
    prop: 'culvertHeight',
    placeholder: '请输入净高(m)',
    type: 'inputNumber',
  },
  {
    label: '进口形式',
    prop: 'inletPortForm',
    placeholder: '请输入进口形式',
    type: 'input',
  },
  {
    label: '出口形式',
    prop: 'exitForm',
    placeholder: '请输入出口形式',
    type: 'input',
  },

  {
    label: '基础形式',
    prop: 'basicsForm',
    placeholder: '请输入基础形式',
    type: 'input',
  },
  {
    label: '涵底纵坡',
    prop: 'culvertBottomLongitudinalRamp',
    placeholder: '请输入涵底纵坡',
    type: 'input',
  },
  {
    label: '涵底铺砌',
    prop: 'culvertBottomPave',
    placeholder: '请输入涵底铺砌',
    type: 'input',
  },
  {
    label: '填土高度(m)',
    prop: 'fillHeight',
    placeholder: '请输入填土高度(m)',
    type: 'inputNumber',
  },
  {
    label: '路面宽度(m)',
    prop: 'pavementWidth',
    placeholder: '请输入路面宽度(m)',
    type: 'inputNumber',
  },
  {
    label: '路基宽度(m)',
    prop: 'subgradeWidth',
    placeholder: '请输入路基宽度(m)',
    type: 'inputNumber',
  },
  {
    label: '路面类型',
    prop: 'pavementType',
    placeholder: '请输入路面类型',
    type: 'input',
  },

  {
    label: '功能类型',
    prop: 'functionType',
    placeholder: '请输入功能类型',
    type: 'input',
  },

  {
    label: '结构形式',
    prop: 'structType',
    placeholder: '请输入结构形式',
    type: 'input',
  },
  {
    label: '备注',
    prop: 'remark',
    placeholder: '请输入备注',
    type: 'inputTextarea',
    span: 12
  },
]

// 资料信息
const archivesInfo = [
  {
    label: '设计图纸',
    prop: 'designDrawingComplete',
    placeholder: '请选择',
    type: 'dictSelect',
    dict: 'base_archival_data'
  },
  {
    label: '设计文件',
    prop: 'designDocumentComplete',
    placeholder: '请选择',
    type: 'dictSelect',
    dict: 'base_archival_data'
  },
  {
    label: '施工文件',
    prop: 'constructionDocumentsComplete',
    placeholder: '请选择',
    type: 'dictSelect',
    dict: 'base_archival_data'
  },
  {
    label: '竣工文件',
    prop: 'completionDocumentComplete',
    placeholder: '请选择',
    type: 'dictSelect',
    dict: 'base_archival_data'
  },
  {
    label: '验收文件',
    prop: 'acceptanceDocumentComplete',
    placeholder: '请选择',
    type: 'dictSelect',
    dict: 'base_archival_data'
  },
  {
    label: '定期检查报告',
    prop: 'periodicInspectionReportComplete',
    placeholder: '请选择',
    type: 'dictSelect',
    dict: 'base_archival_data'
  },
  {
    label: '特别检查报告',
    prop: 'particularlyReportComplete',
    placeholder: '请选择',
    type: 'dictSelect',
    dict: 'base_archival_data'
  },
  {
    label: '专项检查报告',
    prop: 'specialInspectionReportComplete',
    placeholder: '请选择',
    type: 'dictSelect',
    dict: 'base_archival_data'
  },

  {
    label: '历次维修资料',
    prop: 'previousMaintenanceComplete',
    placeholder: '请选择',
    type: 'dictSelect',
    dict: 'base_archival_data'
  },
  {
    label: '档案号',
    prop: 'fileNumberComplete',
    placeholder: '请输入档案号',
    type: 'input',
  },
  {
    label: '存档案',
    prop: 'saveArchivesComplete',
    placeholder: '请选择',
    type: 'dictSelect',
    dict: 'base_archival_data'
  },
  {
    label: '建档时间',
    prop: 'filingTimeComplete',
    placeholder: '请输入建档时间',
    type: 'date',
  },
]

export default {
  baseInfo,
  structureInfo,
  archivesInfo,
}
