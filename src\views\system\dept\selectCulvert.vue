<template>
  <!-- 授权用户 -->
  <el-dialog title="选择涵洞" :visible.sync="visible" width="800px" top="5vh" append-to-body>
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true">
      <el-form-item label="涵洞名称" prop="culvertName">
        <el-input
          v-model="queryParams.culvertName"
          placeholder="请输入涵洞名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>
    <el-row>
      <el-table @row-click="clickRow" @select="selectChange" ref="table" :data="culvertList"
                @select-all="handleSelectionChange" height="260px">
        <el-table-column type="selection" width="55"></el-table-column>
        <el-table-column fixed label="序号" type="index" width="50">
          <template v-slot="scope">
            {{ (scope.$index + (queryParams.pageNum - 1) * queryParams.pageSize)+1 }}
          </template>
        </el-table-column>
        <el-table-column label="涵洞编码" align="center" prop="culvertCode"/>
        <el-table-column label="涵洞类型" align="center" prop="culvertType">
          <template slot-scope="scope">
          <span v-if="scope.row && scope.row.culvertType">
            <DictTag
              :value="scope.row.culvertType"
              :options="dict.type.sys_culvert_type"
            />
          </span>
          </template>
        </el-table-column>
        <el-table-column label="中心桩号" width="180" align="center">
          <template slot-scope="scope">
            <span v-if="scope.row && scope.row.centerStake">{{
                formatPile(scope.row.centerStake)
              }}</span>
          </template>
        </el-table-column>
<!--        <el-table-column label="路线编码" align="center" prop="routeCode"/>-->
<!--        <el-table-column label="路线名称" align="center" prop="routeName"/>-->
<!--        <el-table-column label="起点桩号" align="center" prop="startStake"/>-->
<!--        <el-table-column label="终点桩号" align="center" prop="endStake"/>-->
      </el-table>
      <pagination
        v-show="total>0"
        :total="total"
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
        @pagination="getList"
      />
    </el-row>
    <div slot="footer" class="dialog-footer">
      <el-button type="primary" @click="handleSelectUser">确 定</el-button>
      <el-button @click="visible = false">取 消</el-button>
    </div>
  </el-dialog>
</template>

<script>
import {culvertList} from "@/api/baseData/culvert/culvertInfo";
import {getDefaultSelected, addAllDeptCulvert} from "@/api/system/deptCulvert";
export default {
  dicts: ['sys_normal_disable','sys_culvert_type'],
  props: {
    deptData: {
      type: Object,
      require: true,
    },
  },
  data() {
    return {
      // 遮罩层
      visible: false,
      // 选中数组值
      culvertIds: [],
      // 总条数
      total: 0,
      // 涵洞数据
      culvertList: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        maintenanceSectionId: undefined,
        culvertName: undefined,
      },
      //提交参数
      params: {}
    };
  },
  methods: {
    // 显示弹框
    show() {
      this.queryParams.deptId = this.deptData.deptId;
      this.queryParams.maintenanceSectionId = this.deptData.maintenanceSectionId;
      this.getDefault();
      this.getList();
      this.visible = true;
    },
    clickRow(row) {
      this.$refs.table.toggleRowSelection(row);
      this.setChecked(row);
    },
    selectChange(arr, row) {
      //退选和选中处理
      this.setChecked(row);
    },
    //退选和选中处理
    setChecked(row){
      if (this.culvertIds.includes(row.assetId)) {
        this.culvertIds = this.culvertIds.filter(i => i !== row.assetId);
      } else {
        this.culvertIds.push(row.assetId)
      }
    },

    // 多选框选中数据
    handleSelectionChange(selection) {
      if (selection.length > 0) {
        selection.forEach(item => {
          // this.setChecked(item);
          if (!this.culvertIds || !this.culvertIds.includes(item.assetId)) {
            this.culvertIds.push(item.assetId)
          }
        })
      } else {
        this.culvertList.forEach(item => {
          this.culvertIds = this.culvertIds.filter(i => i !== item.assetId);
        })
      }
    },
    // 查询默认选中数据
    getDefault() {
      getDefaultSelected(this.queryParams).then(data => {
        if (data) {
          this.culvertIds = data;
        }
      });
    },
    // 查询表数据
    getList() {
      // this.queryParams.maintenanceSectionId = null;
      culvertList(this.queryParams).then(res => {
        this.culvertList = res.rows;
        this.total = res.total;
        this.setSelected(this.culvertList);
      });
    },
    //设置默认选中
    setSelected(rows) {
      this.$nextTick(()=> {//渲染后执行
        rows.forEach(row => {
          if (this.culvertIds.includes(row.assetId)) {
            this.$refs.table.toggleRowSelection(row,true);
          }
        });
      })
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    /** 提交选择涵洞操作 */
    handleSelectUser() {
      const culvertIds = this.culvertIds.join(",");
      // if (culvertIds == "") {
      //   this.$modal.msgError("请选择要分配的涵洞");
      //   return;
      // }
      const deptId = this.deptData.deptId;
      this.params.deptId = deptId;
      this.params.culvertIds = culvertIds;
      this.params.maintenanceSectionId = this.deptData.maintenanceSectionId;
      addAllDeptCulvert(this.params).then(response => {
        this.$modal.msgSuccess("操作成功");
        this.$parent.handleDeptAsset(deptId);
        this.getList();
        this.visible=false;
      });
    }
  }
};
</script>
