<template>
  <div class="map-view" ref="mapRef"></div>
</template>

<script>
// import Map from '@geoscene/core/Map';
// import MapView from '@geoscene/core/views/MapView';

export default {
  data() {
    return {
      map: null,
      view: null,
    };
  },
  mounted() {
    // this.initMap();
  },
  methods: {
    initMap() {
      const map = new Map({
        basemap: 'tianditu-image', // 底图图层服务
      });
      const view = new MapView({
        map: map,
        center: [102.72378400000002, 25.013740000000027], // 经度，纬度
        zoom: 7, // 缩放级别
        container: this.$refs.mapRef, // Div 元素
        constraints: {
          maxZoom: 18,
          minZoom: 6,
          rotationEnabled: false, // 禁用地图旋转
        },
      });
    },
  },
};
</script>

<style>
.map-view {
  width: 100%;
  height: 100vh;
}
</style>
