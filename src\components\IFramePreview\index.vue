<template>
  <el-dialog append-to-body modal-append-to-body v-if="visible" :visible.sync="visible" width="80%">
    <iframe :srcdoc="srcdoc" frameborder="0" style="height: 92vh" width="100%"></iframe>
    <img src="@/assets/images/download.png" class="down-btn" @click="handleDownload">
  </el-dialog>
</template>
<script>
import axios from "axios";

export default {
  props: {
    srcdoc: {
      type: String,
      required: true
    },
    downUrl: {
      type: String,
      default: ''
    },
    fileName: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      visible: false
    };
  },
  mounted() {
    if (this.srcdoc == null && this.downUrl != null) {
      this.$message.warning('文件过大，请直接下载')
    }
  },
  methods: {
    handleDownload() {
      axios({
        method: "get",
        responseType: 'arraybuffer',
        url: this.downUrl,
        headers: {}
      }).then((res) => {
        const arrayBuffer = res.data;
        // 创建一个Blob对象
        const blob = new Blob([arrayBuffer], {type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'}); // 对于.xls文件
        saveAs(blob, this.fileName)
      })
    }
  }

}
;
</script>
<style lang="scss" scoped>
.down-btn {
  position: absolute;
  right: 65px;
  bottom: 40px;
  width: 60px;
  cursor: pointer;
}

::v-deep .el-dialog__headerbtn {
  top: 10px;
}

::v-deep .el-dialog .el-dialog__header {
  border-bottom: none !important;
  padding: 0 !important;
}

::v-deep .el-dialog .el-dialog__header {
  padding: 0 0 10px 0 !important;
}

::v-deep .el-dialog:not(.is-fullscreen) {
  margin-top: 2vh !important;
}
</style>
