<template>
	<div>
		<div
			class="parts-options-list"
			v-for="(item, index) in list"
			:key="item.id"
			@click="handleClick(item, index)"
		>
			<el-popover placement="left" width="400" trigger="hover">
				<template #reference>
					<span class="file-sub-name">{{ item.categoryName }}</span>
				</template>
				<el-descriptions
					class="margin-top"
					:column="3"
					size="small"
					border
					:title="item.categoryName"
				>
					<el-descriptions-item v-for="field in formStruct" :key="field.prop" :label="field.label">
						<template v-if="field.prop === 'fileTypeArray'">
							<el-tag v-for="option in item[field.prop]" :key="option">{{ option }}</el-tag>
						</template>
						<template v-else>
							{{ item[field.prop] || '无' }}
						</template>
					</el-descriptions-item>
				</el-descriptions>
			</el-popover>

			<div>
				<span v-if="fileSizes[item.id]">
					<el-badge class="mark" type="warning" :value="fileSizes[item.id] | formatFileSize" />
				</span>
				<el-popover placement="right" width="480" trigger="hover" v-if="fileNums[item.id]">
					<el-table :data="fileMapList[item.id]" height="300" class="custom-table">
						<el-table-column width="300" property="name" label="名称"></el-table-column>
						<el-table-column width="100" label="大小">
							<template slot-scope="scope">
								{{ scope.row.size | formatFileSize }}
							</template>
						</el-table-column>
						<el-table-column label="操作" width="50">
							<template slot-scope="scope">
								<el-row :gutter="10" class="mb8">
									<el-col :span="1.5">
										<i class="el-icon-zoom-in" @click="handlePictureCardPreview(scope.row)" />
									</el-col>
									<el-col :span="1.5">
										<i class="el-icon-download" @click="handleDownload(scope.row)" />
									</el-col>
									<el-col :span="1.5">
										<i class="el-icon-delete" @click="handleRemove(scope.row, item.id)" />
									</el-col>
								</el-row>
							</template>
						</el-table-column>
					</el-table>
					<template #reference>
						<span>
							<el-badge class="mark" type="primary" :value="fileNums[item.id]" />
						</span>
					</template>
				</el-popover>
			</div>
		</div>
		<el-dialog
			:visible.sync="dialogVisible"
			:title="focusItem ? focusItem.categoryName : ''"
			@close="cancel"
			width="80%"
			class="upload-dialog"
			append-to-body
		>
			<FileUpload ref="fileUpload" v-bind="upLoadBind"></FileUpload>
		</el-dialog>
		<el-dialog :visible.sync="officeVisible" custom-class="fixed-dialog" append-to-body>
			<template v-if="showFileName" #title>
				<span v-html="dialogTitle" class="dialog-title"></span>
			</template>
			<OfficePreview :dialog-url="dialogUrl" :content-type="contentType" />
		</el-dialog>
	</div>
</template>

<script>
import { findFiles } from '@/api/file/index.js'
import formFile from '@/components/ModuleFile/formFile.vue'
import FileUpload from '@/components/FileUpload/index.vue'
import { getListByEntity } from '@/api/system/fileUpload.js'
import { mapState, mapActions, mapMutations, mapGetters } from 'vuex'
import OfficePreview from '@/components/OfficePreview/index.vue'
import { removeFile, removeUploadRecord } from '@/api/system/fileUpload.js'
export default {
	name: 'fileSub',
	components: {
		formFile,
		FileUpload,
		OfficePreview,
	},
	props: {
		fileUploadProps: {
			type: Object,
			default: () => ({}),
		},
		list: {
			type: Array,
			default: () => [],
		},
		activeNames: {
			type: String,
			default: '',
		},
	},
	inject: ['cardId', 'isUpload', 'bizId', 'showFileName', 'forView', 'assetType'],
	data() {
		return {
			previousIndex: null, // 用于跟踪上一个打开的索引
			ownerIdLists: {},
			dialogVisible: false,
			officeVisible: false,
			focusItem: null,
			dialogUrl: '',
			dialogTitle: '',
			contentType: '',
			upLoadBind: {
				limit: 0,
				fileSize: 0,
				fileType: [],
				fileSubId: '',
				bizId: '',
				ownerIdList: [],
				assetType: null,
			},
			formStruct: [
				{
					label: '类型名称',
					prop: 'categoryName',
				},
				{
					label: '文件类型',
					prop: 'fileTypeArray',
				},
				{
					label: '文件数量',
					prop: 'fileNum',
				},
				{
					label: '文件大小(MB)',
					prop: 'fileSize',
				},
				{
					label: '排序',
					prop: 'sort',
				},
			],
			fileNums: {}, // 用于存储每个 item 的文件数量
			fileSizes: {}, // 用于存储每个 item 的文件大小
			fileMapList: {}, // 用于存储每个 item 的文件列表
		}
	},
	computed: {
		...mapGetters('file', {
			getTotalFileSize: 'getTotalFileSize',
			getFileIdList: 'getFileIdList',
			getFileIdsMap: 'getFileIdsMap',
			getFileNum: 'getFileNum',
			getFileMapList: 'getFileMapList',
		}),
		componentName() {
			return this.isUpload ? 'FileUpload' : 'formFile'
		},
		localActiveNames: {
			get() {
				// Getter 返回 prop 的当前值
				return this.activeNames
			},
			set(newValue) {
				// Setter 发出一个事件来更新父组件的 prop
				this.$emit('update:activeNames', newValue)
			},
		},
	},
	methods: {
		...mapMutations('file', {
			changeList: 'CHANGE_LIST', // 映射 Vuex mutation
		}),
		...mapActions('file', {
			setFileMap: 'setFileMap',
			clearFileMap: 'clearFileMap',
		}),
		cancel() {
			this.fetchOwnerIdList(this.focusItem.id)
			this.dialogVisible = false
			this.$refs.fileUpload.reset()
		},
		handleChange(val) {
			this.$emit('update:activeNames', '' + val)
			this.changeList({ cardId: this.cardId })
		},
		async fetchOwnerIdList(fileSubId) {
			try {
				const res = await getListByEntity({ fileSubId, bizId: this.bizId })
				if (res.code !== 200 || !res.data || res.data.length === 0) return

				this.clearFileMap({ cardId: this.cardId, fileSubId })

				res.data.forEach((item) => {
					this.setSelfFileMap({
						fileSubId,
						name: item.fileName,
						size: Number(item.fileSize),
						ownerId: item.ownerId,
						fileId: item.fileId,
					})
				})

				// 更新 fileNums, fileSizes, fileMapList
				this.$set(this.fileNums, fileSubId, this.getSelfFileNum(fileSubId))
				this.$set(this.fileSizes, fileSubId, this.getSelfFileSize(fileSubId))
				this.$set(this.fileMapList, fileSubId, this.getSelfFileMapList(fileSubId))
			} catch (error) {
				console.error('获取fileList 信息失败:', error)
			}
		},
		setSelfFileMap({ fileSubId, name, size, ownerId, fileId }) {
			this.setFileMap({
				cardId: this.cardId,
				fileSubId: fileSubId,
				name: name,
				size: size,
				ownerId: ownerId,
				fileId: fileId,
			})
			this.$emit('updateProcess')
		},
		handleClick(item) {
			this.focusItem = item
			this.dialogVisible = true
			this.upLoadBind = this.getComponentProps(item)
		},
		// 获取当前 fileSubId 的文件数量
		getSelfFileNum(fileSubId) {
			return this.getFileNum(this.cardId, fileSubId)
		},
		// 获取当前 fileSubId 的文件大小
		getSelfFileSize(fileSubId) {
			return this.getTotalFileSize(this.cardId, fileSubId)
		},
		// 获取当前 fileSubId 的文件列表
		getSelfFileMapList(fileSubId) {
			return this.getFileMapList(this.cardId, fileSubId)
		},
		getComponentProps(item) {
			if (this.isUpload) {
				const fileSubId = item.id
				if (!this.getFileIdList(this.cardId, fileSubId)) {
					this.fetchOwnerIdList(fileSubId)
				}
				return {
					...this.fileUploadProps,
					limit: item.limit ? (item.limit === 0 ? undefined : item.limit) : undefined,
					fileSize: item.fileSize ? (item.fileSize === 0 ? undefined : item.fileSize) : undefined,
					fileType:
						item.fileTypeArray.length === 0 || item.fileTypeArray === null
							? undefined
							: item.fileTypeArray,
					fileSubId: Number(fileSubId),
					bizId: this.bizId,
					value: this.getFileIdList(this.cardId, fileSubId),
					storagePath: '/ylzx',
					showFileName: this.showFileName,
					forView: this.forView,
					assetType: this.assetType,
				}
			}
			return {}
		},

		// TODO:后续可以抽出组件
		async handlePictureCardPreview(entity) {
			const matchingItem = await this.fetchFile(entity)
			this.contentType = matchingItem.contentType
			this.dialogUrl = matchingItem.url
			this.dialogTitle = matchingItem.name
			this.officeVisible = true
		},

		async fetchFile(entity) {
			// 处理下载逻辑
			const ownerId = entity.ownerId
			const response = await findFiles({ ownerId })
			const data = response.data
      let matchingItem = {}
      if (data.length === 1) {
        matchingItem = {
          ...data[0],
          name: entity.name
        }
      } else if (data.length > 1) {
        matchingItem = {
          ...(data.find((item) => item.originalFilename === entity.name) ||
            data.find((item) => item.fileName === entity.name) ||
            data[0]),
          name: entity.name,
        }
      }
			return matchingItem
		},

		async handleDownload(entity) {
			const matchingItem = await this.fetchFile(entity)
			this.handleDownloadFile(matchingItem)
		},

		getFileName(name) {
			// 如果是url那么取最后的名字 如果不是直接返回
			if (name.lastIndexOf('/') > -1) {
				return name.slice(name.lastIndexOf('/') + 1)
			} else {
				return name
			}
		},

		handleDownloadFile(file) {
			const url = file.url || (file.response && file.response.url)
			if (url) {
				fetch(url)
					.then((response) => response.blob())
					.then((blob) => {
						const link = document.createElement('a')
						link.href = URL.createObjectURL(blob)
						link.download = this.getFileName(file.name) || 'download'
						document.body.appendChild(link)
						link.click()
						document.body.removeChild(link)
					})
					.catch(() => {
						this.$message.error('文件无法下载，未找到文件的1111URL')
					})
			} else {
				this.$message.error('文件无法下载，未找到文件的URL')
			}
		},

		handleRemove(file, fileSubId) {
			this.$confirm('确定删除该文件吗？', '提示', {
				confirmButtonText: '确定',
				cancelButtonText: '取消',
				type: 'warning',
			}).then(() => {
				if (!file.fileId) return
				removeUploadRecord({
					fileId: file.fileId,
					assetType: this.assetType,
					fileSubId: this.fileSubId,
				}).then((res) => {
					if (res.code === 200) {
						this.fetchOwnerIdList(fileSubId)
						removeFile(file.fileId)
							.then((res) => {
								this.$message({
									type: 'success',
									message: '删除成功!',
								})
							})
							.catch(() => {})
					}
				})
			})
		},
	},
	created() {},
	watch: {
		list: {
			immediate: true,
			deep: true,
			async handler(newList) {
				if (newList.length > 0) {
					try {
						const promises = newList.map((item) => this.fetchOwnerIdList(item.id))
						await Promise.all(promises)
					} catch (error) {
						console.error('Error processing list:', error)
					}
				}
			},
		},
	},
}
</script>

<style scoped>
.el-collapse-item:hover {
	background-color: #f5f5f5;
	transition: background-color 0.3s;
}

.parts-options-list {
	display: flex;
	justify-content: space-between;
	align-items: center;
	position: relative;
	user-select: none;
	height: 37.33px;
	padding: 10px;
	margin-bottom: 5px;
	font-size: 14px;
	color: rgb(114, 118, 123);
	border-radius: 4px;
	background: rgb(245, 247, 250);
}

.parts-options-list:hover > div {
	visibility: unset !important;
}

.parts-options-list:hover {
	background: rgb(245, 247, 250);
	box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
	border-radius: 4px;
	font-size: 15px;
}

.parts-options-list > :first-child {
	flex-grow: 1;
}
.file-sub-name {
	cursor: pointer;
	align-items: center;
	padding: 4px 8px; /* 可选的内边距 */
	background-color: #f0f0f0; /* 可选的背景颜色 */
	border-radius: 4px; /* 可选的圆角 */
}
.upload-dialog {
	overflow: auto; /* 显示滚动条 */
}

.custom-table .el-table__body-wrapper {
	max-height: 250px; /* 表格内容的最大高度，可以根据需要调整 */
	overflow: auto;
}
</style>
