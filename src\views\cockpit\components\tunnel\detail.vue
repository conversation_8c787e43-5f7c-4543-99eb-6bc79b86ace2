<template>
  <div class="tunnel-detail">
    <el-row :gutter="isBig ? 30 : 10" style="width: 105%">
      <el-col :xs="8" :sm="6" :md="6" :lg="6" :xl="6">
        <CockpitCard title="隧道基础信息" w="100%" :h="isBig ? 'calc(57vh - 20px)' : 'calc(57vh)'"
          :class="isBig ? 'mb-2' : 'mb-3'" :isDtl="false">
          <div class="bridge-info" v-if="info.id">
            <div v-for="(item, index) in infoList" :key="index" class="info-list">
              <span class="name">{{ item.name }}</span>
              <span v-if="item.type === 'dict'">
                <span class="value" v-if="info[item.value]">{{
                  dict.type[item.dict].filter(
                    (i) => i.value == info[item.value]
                  )[0].label
                }}</span>
              </span>
              <span v-else-if="item.type === 'stake'" class="value">
                {{ formatPile(info[item.value]) }}
              </span>
              <span v-else class="value">{{ info[item.value] }} <span v-if="item.unit">{{ info[item.value] ? item.unit :
                '' }}</span></span>
            </div>
          </div>
        </CockpitCard>
        <div class="flex">
          <CockpitCard title="隧道出口照片" w="50%" :isDtl="false" :h="isBig ? 'calc(25vh + 5px)' : 'calc(25vh)'"
            :class="isBig ? 'mb-2' : 'mb-3'">
            <div v-if="info.inHoleImage" style="
                width: 100%;
                height: 100%;
                display: flex;
                align-items: center;
                justify-content: center;
              ">
              <!-- <ImagePreview :owner-id="info.inHoleImage" width="95%" height="92%"></ImagePreview>  -->
              <ImgView :owner-id="info.inHoleImage" width="100%" height="100%" />
            </div>
            <div v-else class="img-empty">暂无照片</div>
          </CockpitCard>
          <CockpitCard title="隧道进口照片" w="50%" :isDtl="false" :h="isBig ? 'calc(25vh + 5px)' : 'calc(25vh)'"
            :class="isBig ? 'mb-2' : 'mb-3'" class="ml-3">
            <div v-if="info.outHoleImage" style="
                width: 100%;
                height: 100%;
                display: flex;
                align-items: center;
                justify-content: center;
              ">
              <!-- <ImagePreview :owner-id="info.outHoleImage" width="95%" height="92%"></ImagePreview> -->
              <ImgView :owner-id="info.outHoleImage" width="100%" height="100%" />
            </div>
            <div v-else class="img-empty">暂无照片</div>
          </CockpitCard>
        </div>
      </el-col>
      <el-col :xs="12" :sm="12" :md="12" :lg="12" :xl="12">
        <div class="flex mb-3">
          <CockpitCard title="巡查情况" w="50%" :h="isBig ? 'calc(41vh + 3px)' : '41vh'" :isDtl="false">
            <Tables :columns="pColumns" :data="pData"></Tables>
          </CockpitCard>
          <CockpitCard title="隧道技术状况评定" w="50%" :h="isBig ? 'calc(41vh + 3px)' : '41vh'" class="ml-3" :isDtl="false">
            <Echarts :option="pdOption" v-if="pdOption" height="40vh" :key="pdKey" />
          </CockpitCard>
        </div>

        <div class="inspect-situation">
          <div class="header">
            <span :class="isBig ? 'title-big' : 'title-small'">隧道经常检查情况</span>
          </div>
          <div class="main">
            <div class="tunnel-civil mr-2" :style="{ height: isBig ? '40.6vh' : '41.3vh' }">
              <template v-if="!isBig">
                <span class="upper-left"></span>
                <span class="upper-right"></span>
                <span class="lower-left"></span>
                <span class="lower-right"></span>
              </template>

              <div class="civil-title">
                <span></span>
                <span>隧道土建</span>
              </div>

              <div class="inspect-info">
                <template v-if="list1 && list1.length">
                  <div v-for="(item, index) in list1" :key="'in' + index" class="list">
                    <span class="name">{{ item.name }}</span>
                    <div :class="item.value == 1 ? 'normal' : 'error'">
                      <span></span> {{ item.value }}
                    </div>
                  </div>
                </template>
                <template v-else>
                  <div class="inspect-nothing">
                    <span>暂无数据</span>
                  </div>
                </template>
              </div>
            </div>
            <div class="tunnel-civil ml-2" :style="{ height: isBig ? '40.6vh' : '41.3vh' }">
              <template v-if="!isBig">
                <span class="upper-left"></span>
                <span class="upper-right"></span>
                <span class="lower-left"></span>
                <span class="lower-right"></span>
              </template>

              <div class="civil-title">
                <span></span>
                <span>隧道机电</span>
              </div>

              <div class="inspect-info">
                <template v-if="list2 && list2.length">
                  <vue-seamless-scroll :data="list2" :class-option="optionHover" class="seamless-warp">
                    <div v-for="(item, index) in list2" :key="'in' + index" class="list">
                      <el-tooltip class="box-item" effect="dark" :content="item.name" placement="top-start">
                        <span class="name">{{ item.name.length > 25 ? (item.name.slice(0, 25) + '...') : item.name
                        }}</span>
                      </el-tooltip>
                      <div :class="item.value == 1 ? 'normal' : 'error'">
                        <span></span> {{ item.value == 1 ? "正常" : item.value == 2 ? "异常" : "异常且严重" }}
                      </div>
                    </div>
                  </vue-seamless-scroll>
                </template>
                <template v-else>
                  <div class="inspect-nothing">
                    <span>暂无数据</span>
                  </div>
                </template>
              </div>
            </div>
          </div>
        </div>
      </el-col>
      <el-col :xs="8" :sm="6" :md="6" :lg="6" :xl="6">
        <CockpitCard title="最新技术评定结果" w="100%" :h="isBig ? 'calc(86vh - 5px)' : 'calc(87vh)'" :isDtl="false">
          <div class="pd-level">
            <div class="level">
              <span>总体评定等级</span>
              <span>{{ info.totalAssessmentGrade || "-" }} <small>类</small></span>
            </div>
            <div class="divider"></div>
            <div class="level">
              <span>养护等级</span>
              <span>{{ info.maintenanceGrade || "-" }} <small>类</small></span>
            </div>
          </div>

          <div class="pd-result">
            <div class="result-list" v-for="(item, index) in rList" :key="'r' + index">
              <div class="list-index" :class="'list-index' + (index + 1)">
                {{ index + 1 }}
              </div>
              <span class="value">{{ item.name }}</span>
              <span class="name">{{ item.type }}</span>
              <span class="value">{{ info[item.value] || "-" }} 类</span>
            </div>
          </div>
          <div class="tab-list">
            <div class="tab-item" :class="tabIndex == index ? 'tab-act' : ''" v-for="(item, index) in tabList"
              :key="'tab' + index" @click="onTabClick(index)">
              {{ item.name }}
            </div>
          </div>
          <div class="pr-table" style="height:49%">
            <Tables :columns="prColumns" :data="tabIndex === 0
              ? info.civilPeriodicDetectionDetails
              : tabIndex === 1
                ? info.electPeriodicDetectionDetails
                : info.otherPeriodicDetectionDetails
              "></Tables>
          </div>
          <div class="mt-advice" :style="{ height: isBig ? '295px' : '100px' }">
            <div class="advice-name">
              <img src="@/assets/cockpit/mt-advice.png" />
              <span>养护意见</span>
            </div>
            <div class="advice-cont">
              {{
                info.totalSuggestions ||
                "隧道整体状况较为良好，应进行“日常养护、预防养护”针对较为严重的病害现象建议立即进行处置。"
              }}
            </div>
          </div>
        </CockpitCard>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import * as echarts from "echarts"
import { isBigScreen } from "../../util/utils"
import CockpitCard from "../cockpitCard.vue"
import Tables from "../tables.vue"
import Echarts from "../echarts/echarts.vue"
import { getTunnelDetailInfo, selectAll, tunnelBaseInfo, getByCheckId, list, getDeviceCheckList, getDetailByCondition } from "@/api/cockpit/index"
import ImgView from "@/components/ImagePreview/imgView.vue";

export default {
  components: {
    CockpitCard,
    Tables,
    Echarts,
    ImgView,
  },
  dicts: ["tunnel_lining_type", "tunnel_hole_style", "tunnel_assess_grade"],
  data() {
    return {
      isBig: isBigScreen(),
      info: {},
      tunnelAssetId: '',
      infoList: [
        {
          name: "管养单位：",
          value: "managementMaintenanceName",
        },
        {
          name: "路线编码：",
          value: "routeCode",
        },
        {
          name: "路段名称：",
          value: "maintenanceSectionName",
        },
        {
          name: "隧道名称：",
          value: "tunnelName",
        },
        {
          name: "隧道编码：",
          value: "tunnelCode",
        },
        {
          name: "统一里程桩号：",
          value: "unifiedMileageStake",
          type: "stake",
        },
        {
          name: "隧道长度：",
          value: "tunnelLength",
        },
        {
          name: "衬砌类型：",
          value: "liningType",
          type: "dict",
          dict: "tunnel_lining_type",
        },
        {
          name: "洞门形式：",
          value: "holeStyle",
          type: "dict",
          dict: "tunnel_hole_style",
        },
        {
          name: "机电设施类别：",
          value: "facilityType",
        },
        {
          name: "隧道养护工程师：",
          value: "余正利",
        },
        {
          name: "下一次评定日期：",
          value: "2024年",
        },
        {
          name: "养护工程处置次数：",
          value: "1次",
        },
        {
          name: "日常养护次数：",
          value: "1次",
        },
        {
          name: "本月日常巡查次数：",
          value: "dailyCount",
          unit: "次"
        },
        {
          name: "本月经常检查次数：",
          value: "oftenCount",
          unit: "次"
        },
      ],
      pColumns: [
        {
          label: "巡查时间",
          prop: "checkTime",
        },
        {
          label: "巡查人",
          prop: "oprUserName",
        },
        {
          label: "天气状况",
          prop: "weather",
        },
        {
          label: "是否异常",
          prop: "abnormal",
        },
      ],
      pData: [],
      pdOption: null,
      // list1: [],
      // list2: [],
      list1: [],
      list2: [],
      prColumns: [
        {
          label: "结构名称",
          prop: "itemName",
        },
        {
          label: "状况值",
          prop: "state",
        },
      ],
      prData: [],
      rList: [
        {
          name: "土建评定结果",
          type: "技术状况类别",
          value: "civilAssessmentGrade",
        },
        {
          name: "机电设施评定结果",
          type: "技术状况类别",
          value: "electAssessmentGrade",
        },
        {
          name: "其他工程设施评定结果",
          type: "技术状况类别",
          value: "otherAssessmentGrade",
        },
      ],
      tabIndex: 0,
      tabList: [
        {
          name: "隧道土建",
        },
        {
          name: "隧道机电",
        },
        {
          name: "隧道其他工程设施",
        },
      ],
      tunnelId: '2077',
      year: new Date().getFullYear(),
      pdKey: 'pdKey'
    }
  },
  async created() {
    window.$Bus.$on('aMapClick', async (data) => {
      if (data.sys_dept_id) return
      this.pdOption = null;
      this.tunnelId = data.id
      await this.init()
    })

    window.$Bus.$on('pageSelectYear', async (data) => {
      this.year = data;
      this.init();
    })

    // 接收GeoJson 数据
    window.$Bus.$on('getGeoJson', async (data) => {
      if (!data) return;
      this.tunnelId = data.id;
      let userInfo = this.$store.state.user;
      if (userInfo.deptId == 1 || userInfo.roles?.includes('admin')) return;
      if (userInfo.deptId == 94) return;
      this.init();
    })
  },
  async mounted() {
    await this.getTunnelId();
    this.init()
  },
  computed: {
    // 滚动设置
    optionHover() {
      return {
        hoverStop: true,
        step: 0.2, // 数值越大速度滚动越快
        limitMoveNum: 2, // 开始无缝滚动的数据量 this.dataList.length
        direction: 1, // 0向下 1向上 2向左 3向右
        openWatch: true, // 开启数据实时监控刷新dom
        singleHeight: 0, // 单步运动停止的高度(默认值0是无缝不停止的滚动) direction => 0/1
        singleWidth: 0, // 单步运动停止的宽度(默认值0是无缝不停止的滚动) direction => 2/3
        waitTime: 1000, // 单步运动停止的时间(默认值1000ms)
      };
    },
  },
  destroyed() {
    window.$Bus.$off('aMapClick')
    window.$Bus.$off('pageSelectYear')
    window.$Bus.$off('getGeoJson')
  },
  methods: {
    async getTunnelId() {
      return new Promise((resolve, reject) => {
        this.getConfigKey('default_cockpit_tunnel_id').then(res => {
          if (res && res.code == 200) {
            this.tunnelId = res.data || res.msg;
            resolve(res.data || res.msg);
          } else {
            reject(res.msg || '获取隧道ID失败');
          }
        })
      })
    },
    async init() {
      await this.getBaseInfo()
      this.initPdEcharts()
      this.getPData()
      this.getListData()

    },
    initGetDeviceCheckList() {
      getDeviceCheckList({ assetId: this.tunnelId, year: this.year }).then(res => {

        if (res.code = 200) {
          let list = [];
          res.rows.forEach(element => {
            list.push({
              name: element.devName,
              value: element.result
            })
          });
          this.list2 = list;
        }
      })

      getDetailByCondition({ assetId: this.tunnelAssetId, tunnelId: this.tunnelId, year: this.year, type: 6 }).then(res => {
        if (res.code = 200) {
          let list = [];
          res.data.forEach(element => {
            list.push({
              name: element.partsTypeName,
              value: element.devName
            })
          });
          this.list1 = list;
        }
      })
    },
    async getBaseInfo() {
      let info = {}
      const { data } = await getTunnelDetailInfo({
        tunnelId: this.tunnelId,
        year: this.year,
      })
      if (data) {
        this.tunnelAssetId = data.assetId;
        this.initGetDeviceCheckList()
        const formattedDate = new Date(data.totalCheckDate)
        const year = formattedDate.getFullYear() ? formattedDate.getFullYear() : '2024'
        const month = formattedDate.getMonth() ? formattedDate.getMonth() + 1 : '1'
        data.formattedDateString = `${year}年${month}月`
        data.totalAssessmentGradeName =
          this.dict.type.tunnel_assess_grade.find(
            (i) => i.value === data.totalAssessmentGrade
          )?.label || ""
        info = { ...data }
        this.$emit('update:dTitle', data.tunnelName)
      }
      const res = await tunnelBaseInfo(this.tunnelId)
      if (res.code === 200) {
        delete res?.data?.managementMaintenanceName;
        info = { ...info, ...res.data }
      }
      this.info = info
    },
    initPdEcharts() {
      const data = this.info.tunnelExportReportVOS?.map((item) =>
        Number(item.totalScore)
      ) || [40, 66, 57, 48, 68, 76, 50, 62, 54]
      const sideData = data?.map((item) => item + 1)
      let checkDate = this.info.formattedDateString || '';
      let score = this.info.totalScore || '';
      let gradeName = this.info.totalAssessmentGradeName || '';
      const text = this.info.formattedDateString ? `${checkDate}评定结果：${gradeName}隧道(${score}分)` : "2022年9月评定结果：2类隧道(89.07分)"
      this.pdOption = {
        backgroundColor: "rgba(0,0,0,0)",
        title: [
          {
            text: text || '暂无数据',
            left: "center",
            top: "0%",
            textStyle: {
              color: "#42ABFF",
              fontSize: this.isBig ? 32 : 16,
            },
          },
        ],
        grid: {
          top: "25%",
          left: "3%",
          right: "5%",
          bottom: "1%",
          containLabel: true,
        },
        xAxis: {
          name: '',
          nameTextStyle: {
            color: "#999999",
            algin: 'right',
            fontSize: this.isBig ? 25 : 14,
            padding: [6, 0, 0, -10],
            verticalAlign: 'top',
          },
          data: this.info.tunnelExportReportVOS?.map((item) => item.year) || ["2016", "2017", "2018", "2019", "2020", "2021", "2022", "2023", "2024"],
          //坐标轴
          axisLine: {
            lineStyle: {
              color: "rgba(26,139,255,0.5)",
              type: 'dashed'
            },
          },
          //坐标值标注
          axisLabel: {
            show: true,
            textStyle: {
              color: "#8B8C8C",
              fontSize: this.isBig ? 25 : 14,
            },
          },
        },
        yAxis: [
          {
            type: "value",
            name: '技术状况评分',
            nameTextStyle: {
              color: "#999999",
              algin: 'left',
              fontSize: this.isBig ? 25 : 14,
              padding: [0, 0, 0, !data.length ? 70 : 0],
            },
            //坐标轴
            axisLine: {
              show: true,
              lineStyle: {
                color: 'rgba(110,112,121,0.5)',
                width: 1,
              },
            },
            //坐标值标注
            axisLabel: {
              show: true,
              textStyle: {
                color: "#999999",
                fontSize: this.isBig ? 25 : 14,
              },
            },
            //分格线
            splitLine: {
              show: false,
              lineStyle: {
                color: "rgba(110,112,121,0.5)",
              },
            },
            splitArea: {
              show: false,
            },
            interval: 10,
          },
          {
            type: "value",
            name: '技术状况等级',
            nameTextStyle: {
              color: "#1A8BFF",
              algin: 'right',
              fontSize: this.isBig ? 25 : 14,
              padding: [0, 60, 0, 0],
            },
            position: 'right',
            //分格线
            splitLine: {
              show: true,
              lineStyle: {
                color: "#1A8BFF",
                type: 'dashed',
                align: 'center',
              },
            },
          }
        ],
        series: [
          {
            name: "a",
            tooltip: {
              show: false,
            },
            label: {
              normal: {
                show: true,
                position: "top",
                fontSize: this.isBig ? 25 : 14,
                color: "#fff",
                offset: [0, -10],
              },
            },
            type: "bar",
            barWidth: 24.5,
            itemStyle: {
              normal: {
                color: new echarts.graphic.LinearGradient(
                  0,
                  1,
                  0,
                  0,
                  [
                    {
                      offset: 0,
                      color: "rgba(0,255,251,0.1)", // 0% 处的颜色
                    },
                    {
                      offset: 0.6,
                      color: "rgba(0,255,251,0.6)", // 60% 处的颜色
                    },
                    {
                      offset: 1,
                      color: "rgba(0,255,251,1)", // 100% 处的颜色
                    },
                  ],
                  false
                ),
              },
            },
            data: data,
            barGap: 0,
            markLine: {
              symbol: "none",
              silent: true,
              label: {
                position: 'middle',
                formatter: '{b}',
                color: '#1A8BFF',
                fontSize: this.isBig ? 25 : 13,
              },
              data: [
                {
                  name: '5类',
                  yAxis: 40,
                  lineStyle: {
                    color: 'rgba(26,139,255,0.5)',
                    width: 1,
                    type: 'dashed'
                  },
                  label: {
                    position: 'end',
                    formatter: '\n{b}',
                    offset: [-20, 50] // 右侧 -20，上方 40
                  },
                },
                {
                  name: '4类',
                  yAxis: 60,
                  lineStyle: {
                    color: 'rgba(26,139,255,0.5)',
                    width: 1,
                    type: 'dashed'
                  },
                  label: {
                    position: 'end',
                    formatter: '\n{b}',
                    offset: [-20, 25] // 右侧 -20，上方 40
                  },
                },
                {
                  name: '3类',
                  yAxis: 80,
                  lineStyle: {
                    color: 'rgba(26,139,255,0.5)',
                    width: 1,
                    type: 'dashed'
                  },
                  label: {
                    position: 'end',
                    formatter: '\n{b}',
                    offset: [-20, 25] // 右侧 -20，上方 40
                  },
                },
                {
                  name: '2类',
                  yAxis: 90,
                  lineStyle: {
                    color: 'rgba(26,139,255,0.5)',
                    width: 1,
                    type: 'dashed'
                  },
                  label: {
                    position: 'end',
                    formatter: '\n{b}',
                    offset: [-20, 10] // 右侧 -20，上方 40
                  },
                },
                {
                  name: '1类',
                  yAxis: 100,
                  lineStyle: {
                    color: 'rgba(26,139,255,0.5)',
                    width: 1,
                    type: 'dashed'
                  },
                  label: {
                    position: 'end',
                    formatter: '\n{b}',
                    offset: [-20, 10] // 右侧 -20，上方 40
                  },
                },
              ],
            }
          },
          {
            type: "bar",
            barWidth: 8,
            itemStyle: {
              normal: {
                color: new echarts.graphic.LinearGradient(
                  0,
                  1,
                  0,
                  0,
                  [
                    {
                      offset: 0,
                      color: "rgba(0,255,251,0.1)", // 0% 处的颜色
                    },
                    {
                      offset: 0.6,
                      color: "rgba(0,255,251,0.4)", // 60% 处的颜色
                    },
                    {
                      offset: 1,
                      color: "rgba(0,255,251,0.6)", // 100% 处的颜色
                    },
                  ],
                  false
                ),
              },
            },
            barGap: 0,
            data: sideData,
          },
          {
            name: "b",
            tooltip: {
              show: false,
            },
            type: "pictorialBar",
            itemStyle: {
              borderWidth: 1,
              borderColor: "rgba(125,255,253,0.7)",
              color: "#7DFFFD",
            },

            symbol: "path://M 0,0 l 120,0 l -30,60 l -120,0 z",
            symbolSize: ["30", "12"],
            symbolOffset: ["0", "-8"],
            symbolRotate: -15,
            symbolPosition: "end",
            data: data,
            z: 3,
          },
        ],
      }
      this.pdKey = 'pdKey' + new Date().getTime()
    },
    getPData() {
      selectAll({ assetId: String(this.tunnelId), year: this.year }).then(res => {
        if (res.code === 200) {
          this.pData = res.data.records
        }
      })
    },
    getListData() {
      // getByCheckId({ assetId: this.tunnelId, year: this.year }).then(res => {

      // })
      // list({ assetId: this.tunnelId, year: this.year }).then(res => {

      // })
    },
    onTabClick(index) {
      this.tabIndex = index
    },
  },
}
</script>

<style lang="scss" scoped>
@import "@/assets/styles/utils.scss";

.tunnel-detail {
  width: 100%;
  display: flex;
  padding: 0 vwpx(30px);
  margin-top: vwpx(15px);

  .bridge-info {
    padding: vwpx(20px);

    .info-list {
      width: 100%;
      height: vwpx(70px);
      background-color: rgba(23, 116, 255, 0.1);
      margin: vwpx(4px) 0;
      padding: 0 vwpx(10px);

      display: flex;
      align-items: center;

      .name {
        font-family: Microsoft YaHei, Microsoft YaHei;
        font-weight: 400;
        font-size: 1.25vh;
        color: #42abff;
        flex-shrink: 0;
      }

      .value {
        font-family: Microsoft YaHei, Microsoft YaHei;
        font-weight: 400;
        font-size: 1.2vh;
        color: #ffffff;
        margin-left: vwpx(20px);
        // 超出换行
        overflow-wrap: break-word;
        word-wrap: break-word;
        word-break: break-all;
      }
    }
  }

  .inspect-info {
    padding: vwpx(20px);
    height: 95%;
    overflow-y: auto;

    .inspect-nothing {
      width: 100%;
      height: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
      color: #8C9096;
      font-size: vwpx(26px);
    }

    &::-webkit-scrollbar {
      width: vwpx(12px);
      height: vwpx(12px);
    }

    &::-webkit-scrollbar-thumb {
      background-color: rgba(1, 102, 254, 0.3);
      border-radius: 2px;
    }

    &::-webkit-scrollbar-track {
      background-color: rgba(1, 102, 254, 0.2);
    }

    .list {
      height: vwpx(80px);
      background-color: rgba(23, 116, 255, 0.1);

      display: flex;
      align-items: center;
      justify-content: space-between;
      margin: vwpx(6px) 0;
      padding: 0 vwpx(10px);

      .name {
        font-family: Microsoft YaHei, Microsoft YaHei;
        font-weight: 400;
        font-size: 1.3vh;
        color: #00cbff;
      }

      .normal {
        font-family: Microsoft YaHei, Microsoft YaHei;
        font-weight: 400;
        font-size: 1.3vh;
        color: #1cffbc;
        display: flex;
        align-items: center;
        flex-shrink: 0;

        span {
          width: vwpx(15px);
          height: vwpx(15px);
          display: block;
          border-radius: 50%;
          background-color: #1cffbc;
          margin-right: vwpx(10px);
        }
      }

      .error {
        font-family: Microsoft YaHei, Microsoft YaHei;
        font-weight: 400;
        font-size: vwpx(26px);
        color: #f53f3f;
        display: flex;
        align-items: center;
        flex-shrink: 0;

        span {
          width: vwpx(15px);
          height: vwpx(15px);
          display: block;
          border-radius: 50%;
          background-color: #f53f3f;
          margin-right: vwpx(10px);
        }
      }
    }
  }

  .pd-level {
    margin: vwpx(30px) vwpx(20px);
    height: vwpx(184px);
    background: #002449;
    box-shadow: inset 0px 0px 10px 0px #0065ff;
    border-radius: 6px;
    border: 1px solid #20a9ff;

    display: flex;
    align-items: center;

    .level {
      flex: 1;

      display: flex;
      align-items: center;
      justify-content: center;
      flex-direction: column;

      span:first-child {
        font-family: Microsoft YaHei UI, Microsoft YaHei UI;
        font-weight: 400;
        font-size: 1.5vh;
        color: #ffffff;
        text-align: center;
        font-style: normal;
        text-transform: none;
      }

      span:last-child {
        font-family: Microsoft YaHei UI, Microsoft YaHei UI;
        font-weight: 700;
        font-size: 1.6vh;
        color: #f2af4a;
        text-align: center;
        font-style: normal;
        text-transform: none;
        margin-top: vwpx(10px);

        small {
          font-family: Microsoft YaHei UI, Microsoft YaHei UI;
          font-weight: 400;
          font-size: 1.2vh;
          color: #b6b6b6;
          text-shadow: 0px 0px 10px rgba(27, 126, 242, 0.8);
          text-align: center;
          font-style: normal;
          text-transform: none;
        }
      }
    }

    .divider {
      width: 0;
      height: vwpx(104px);
      border: 1px solid rgba(156, 189, 255, 0.5);
    }
  }

  .pd-result {
    margin: vwpx(30px) vwpx(20px);

    .result-list {
      height: vwpx(80px);
      background: #01315d;
      margin-bottom: vwpx(10px);
      color: #ffffff;
      font-size: 1.3vh;

      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 0 vwpx(30px);

      .list-index {
        width: vwpx(50px);
        height: vwpx(50px);
        background: rgba(4, 17, 48, 0.4);

        display: flex;
        align-items: center;
        justify-content: center;
      }

      .list-index1 {
        box-shadow: inset 0px 0px 15px 0px rgba(175, 0, 0, 0.6);
        border: 1px solid #AF0000;
        color: #FF4646;
      }

      .list-index2 {
        box-shadow: inset 0px 0px 15px 0px rgba(175, 107, 0, 0.6);
        border: 1px solid #AF6B00;
        color: #FD9500;
      }

      .list-index3 {
        box-shadow: inset 0px 0px 15px 0px rgba(175, 156, 0, 0.6);
        border: 1px solid #AF9C00;
        color: #FCFF00;
      }
    }
  }

  .tab-list {
    margin: 0 vwpx(20px) vwpx(10px) vwpx(20px);
    display: flex;
    align-items: center;
    height: vwpx(76px);

    .tab-item {
      flex: 1;
      height: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
      background: rgba(4, 17, 48, 0.4);
      box-shadow: inset 0px 0px 6px 0px #0154fb;

      font-family: Microsoft YaHei UI, Microsoft YaHei UI;

      font-size: 1.5vh;
      color: #ffffff;
      text-shadow: 0px 0px 10px rgba(27, 126, 242, 0.8);
      text-align: center;
      cursor: pointer;
    }

    .tab-act {
      background: #42abff;
      border: 1px solid #42abff;
    }
  }

  .pr-table {
    border: 1px solid #2cb5ff;
    margin: 0 vwpx(20px);
    // height: 40vh;
  }

  .mt-advice {
    border: 1px solid #2cb5ff;

    background-color: rgba(23, 116, 255, 0.1);
    margin: vwpx(40px) vwpx(20px);

    display: flex;
    align-items: center;

    .advice-name {
      display: flex;
      align-items: center;
      justify-content: center;
      flex-direction: column;
      margin: 0 vwpx(20px);

      img {
        width: vwpx(130px);
        height: vwpx(130px);
      }

      span {
        font-family: Source Han Sans, Source Han Sans;
        font-weight: 500;
        font-size: 1.35vh;
        color: #42abff;
        margin-top: vwpx(20px);
      }
    }

    .advice-cont {
      flex: 1;
      height: 100%;
      background: rgba(23, 116, 255, 0.1);
      font-family: Microsoft YaHei UI, Microsoft YaHei UI;
      font-weight: 400;
      font-size: 1.3vh;
      color: #ffffff;

      display: flex;
      align-items: center;
      padding: vwpx(30px);

      text-align: left;
      font-style: normal;
      text-transform: none;
    }
  }

  .img-empty {
    height: calc(100% - 15px);
    margin: vwpx(20px);
    background: #01315d;
    font-family: Microsoft YaHei, Microsoft YaHei;
    font-weight: 400;
    font-size: vwpx(24px);
    color: rgba(255, 255, 255, 0.6);
    border-radius: vwpx(5px);

    display: flex;
    align-items: center;
    justify-content: center;
  }

  .inspect-situation {
    width: calc(100%);

    .header {
      width: 100%;
      height: vwpx(76px);
      background-image: url("~@/assets/cockpit/card-header-bg-l.png");
      background-repeat: no-repeat;
      background-size: auto 100%;
      display: flex;
      align-items: center;
      object-fit: fill;

      .title-big {
        font-size: 0.63vw;
      }

      .title-small {
        font-size: vwpx(36px);
        text-shadow: 0px 0px 16px #0088ff;
        font-weight: 700;
      }

      span {
        margin-left: vwpx(45px);
        font-family: Microsoft YaHei UI, Microsoft YaHei UI;
        color: #ffffff;
        text-align: left;
        margin-top: vwpx(5px);
      }
    }

    .main {
      display: flex;
      align-items: center;
      justify-content: space-evenly;

      .tunnel-civil {
        position: relative;
        flex: 1;
        border: 1px solid rgba(0, 166, 255, 0.5);
        padding: vwpx(20px);

        .civil-title {
          display: flex;
          align-items: center;

          span:first-child {
            display: block;
            width: vwpx(8px);
            height: vwpx(32px);
            background: #42abff;
          }

          span:last-child {
            font-family: Microsoft YaHei, Microsoft YaHei;
            font-weight: 700;
            font-size: vwpx(32px);
            color: #ffffff;
            text-shadow: 0px 0px 10px rgba(27, 126, 242, 0.8);
            text-align: center;
            font-style: normal;
            text-transform: none;
            margin-left: vwpx(15px);
          }
        }

        .upper-left {
          position: absolute;
          top: -1px;
          left: -1px;
          width: vwpx(30px);
          height: vwpx(30px);
          background-image: url("~@/assets/cockpit/card-box.png");
          background-repeat: no-repeat;
          background-size: 100% 100%;
          transform: rotate(180deg);
        }

        .upper-right {
          position: absolute;
          top: -1px;
          right: -1px;
          width: vwpx(30px);
          height: vwpx(30px);
          background-image: url("~@/assets/cockpit/card-box.png");
          background-repeat: no-repeat;
          background-size: 100% 100%;
          transform: rotate(270deg);
        }

        .lower-left {
          position: absolute;
          bottom: -1px;
          left: -1px;
          width: vwpx(30px);
          height: vwpx(30px);
          background-image: url("~@/assets/cockpit/card-box.png");
          background-repeat: no-repeat;
          background-size: 100% 100%;
          transform: rotate(90deg);
        }

        .lower-right {
          position: absolute;
          bottom: -1px;
          right: -1px;
          width: vwpx(30px);
          height: vwpx(30px);
          background-image: url("~@/assets/cockpit/card-box.png");
          background-repeat: no-repeat;
          background-size: 100% 100%;
          transform: rotate(0deg);
        }
      }
    }
  }

  .flex {
    display: flex;
  }

  .flex-1 {
    flex: 1;
  }

  .flex-2 {
    flex: 2;
  }

  .mb-2 {
    margin-bottom: vwpx(20px);
  }

  .mb-3 {
    margin-bottom: vwpx(30px);
  }

  .ml-3 {
    margin-left: vwpx(30px);
  }

  .ml-2 {
    margin-left: vwpx(20px);
  }

  .mr-2 {
    margin-right: vwpx(20px);
  }

  .img {
    width: 100%;
    height: 100%;
    padding: 5px;
    cursor: pointer;
    object-fit: fill;
  }

  .seamless-warp {
    height: 100%;
  }
}
</style>
