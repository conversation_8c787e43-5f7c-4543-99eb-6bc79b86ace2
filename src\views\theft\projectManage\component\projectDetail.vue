<template>
  <div class="road-interflow-edit" style="padding: 20px" v-loading="loading">
    <el-row :gutter="20">
      <el-form
          ref="elForm"
          :model="formData"
          :rules="rules"
          :disabled="readonly"
          size="medium"
          label-width="130px"
      >
        <el-col :span="24">
          <div class="card_title">1.基本信息</div>
        </el-col>
        <el-row :gutter="20" style="flex-wrap: wrap; display: flex;">
          <el-col :span="8">
            <el-form-item label="管养单位" prop="domainId">
              <selectTree
                  :key="'domainId'"
                  v-model="formData.domainId"
                  :deptType="100"
                  :deptTypeList="[1, 3, 4]" clearable
                  filterable
                  placeholder="管养单位"
                  style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="年度" prop="year">
              <el-date-picker
                  v-model="formData.year"
                  placeholder="年度"
                  style="width: 100%"
                  type="year"
                  value-format="yyyy"
              >
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="工程分类" prop="mtype">
              <cost-select :type="35"
                  v-model="formData.mtype"
                  placeholder="工程分类"
                  style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="项目类别" prop="projectType">
              <dict-select
                  v-model="formData.projectType"
                  type="theft_project_type"
                  placeholder="工程分类"
                  style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="工程名称" prop="name">
              <el-input style="width: 100%"  v-model="formData.name" placeholder="项目名称"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="工程编码" prop="code">
              <el-input style="width: 100%"  v-model="formData.code" placeholder="项目编码"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="路段名称" prop="maiSecId">
              <RoadSection ref="roadSection" v-model="formData.maiSecId" :deptId="formData.domainId" placeholder="路段"
                           style="width: 100%" @change="changeMaiSecId"/>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="路线类型" prop="routeType">
              <DictSelect
                v-model="formData.routeType"
                @change="changeRouteType"
                :type="'sys_route_type'"
                :placeholder="'路线类型'"
                clearable
              ></DictSelect>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="路线编码" prop="routeCode">
              <el-select
                filterable
                @change="changeRouteCode"
                v-model="formData.routeCode"
                style="width: 100%"
                clearable
              >
                <el-option
                  v-for="(item,index) in routeList"
                  :key="index"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="养护子段" prop="subRouteCode">
              <el-select
                filterable
                v-model="formData.subRouteCode"
                style="width: 100%"
                clearable
              >
                <el-option
                  v-for="(item,index) in subRouteList"
                  :key="index"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item
                label="起点桩号"
                prop="beginMile"
            >
              <PileInput v-model="formData.beginMile" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item
                label="终点桩号"
                prop="endMile"
            >
              <PileInput v-model="formData.endMile" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="上下行" prop="direction">
              <dict-select type="route_direction" clearable
                           v-model="formData.direction" placeholder="请选择路线方向"
                           style="width: 100%"></dict-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="位置" prop="laneArr">
              <dict-select type="lane" clearable multiple
                           v-model="formData.laneArr" placeholder="请选择路线车道"
                           style="width: 100%"></dict-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="肇事日期" prop="causeTime">
              <el-date-picker
                  v-model="formData.causeTime"
                  placeholder="肇事日期"
                  style="width: 100%"
                  type="date"
                  value-format="yyyy-MM-dd"
              >
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="合同" prop="conId">
              <contract-section v-model="formData.conId" valueType="object" :params="contractFilterMap" placeholder="请选择合同" @change="addContract"></contract-section>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="资产类型" prop="assetMainType">
              <el-select v-model="formData.assetMainType" style="width: 100%" clearable>
                <el-option v-for="dict in assetMainType" :key="dict.dictValue"
                           :label="dict.dictLabel" :value="dict.dictValue">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="资产名称" prop="assetType">
              <el-select v-model="formData.assetType" style="width: 100%" @change="changeAsset" clearable>
                <el-option v-for="dict in  assetType" :key="dict.dictValue"
                           :label="dict.dictLabel" :value="dict.dictValue">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
              <el-form-item label="事件类型" prop="disType">
                  <el-select v-model="formData.disType" style="width: 100%" clearable>
                      <el-option v-for="dict in disType" :key="dict.dictValue"
                                 :label="dict.dictLabel" :value="dict.dictValue">
                      </el-option>
                  </el-select>
              </el-form-item>
          </el-col>
          <el-col :span="8" v-if="formData.assetMainType == '4'">
            <el-form-item label="资产" prop="assetName">
              <el-input v-model="formData.assetName" readonly @focus="handleOpenAsset"/>
            </el-form-item>
          </el-col>
        </el-row>
        <el-col :span="24">
          <el-form-item label="已选合同" prop="conNameArr">
            <el-input type="textarea" rows="4" v-model="formData.conNameArr" :style="{width: '100%'}" placeholder="已选合同" readonly></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="主要工程内容" prop="enContent">
            <el-input type="textarea" rows="4" v-model="formData.enContent" :style="{width: '100%'}" placeholder="主要工程内容"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="备注" prop="remark">
            <el-input type="textarea" rows="4" v-model="formData.remark" :style="{width: '100%'}" placeholder="备注"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <div class="card_title">2.合同清单</div>
        </el-col>
        <el-col :span="24">
          <el-tabs v-model="editableTabsValue" :closable="!readonly" @tab-remove="removeTab">
            <el-tab-pane
                v-for="(item, index) in contractList"
                :key="item.conId"
                :label="item.conName"
                :name="item.conId"
            >
              <el-button v-if="!readonly" type="primary" @click="addScheme(item.conId, index)" style="margin-bottom:10px;float:right">新增</el-button>
              <el-table v-adjust-table
                  :data="item.schemeList"
                  border
                  height="200px"
                  ref="tableRef"
                  style="width: 100%">
                <el-table-column
                    label="序号"
                    align="center"
                    type="index"
                    width="50"
                />
                <el-table-column
                    prop="schemeCode"
                    align="center"
                    label="子目号">
                </el-table-column>
                <el-table-column
                    prop="schemeName"
                    align="center"
                    label="子目名称">
                  <template slot-scope="scope">
                    <el-input v-if="scope.row.rateFlag == 1" v-model="scope.row.schemeName">
                    </el-input>
                    <div v-else>{{scope.row.schemeName}}</div>
                  </template>
                </el-table-column>
                <el-table-column
                    prop="unit"
                    align="center"
                    label="单位">
                  <template slot-scope="scope">
                    <el-input v-if="scope.row.rateFlag == 1" v-model="scope.row.unit">
                    </el-input>
                    <div v-else>{{scope.row.unit}}</div>
                  </template>
                </el-table-column>
                <el-table-column
                    prop="price"
                    align="center"
                    label="单价">
                  <template slot-scope="scope">
                    <el-input v-if="scope.row.rateFlag == 1" v-model="scope.row.price">
                    </el-input>
                    <div v-else>{{scope.row.price}}</div>
                  </template>
                </el-table-column>
                <el-table-column
                  prop="calcDesc"
                  align="center"
                  label="计算式"
                  width="200">
                  <template slot-scope="scope">
                    <el-input v-model="scope.row.calcDesc" @change="changeCalculation(scope.row)">
                    </el-input>
                  </template>
                </el-table-column>
                <el-table-column
                  prop="num"
                  align="center"
                  label="方法数量"
                  width="100">
                  <template slot-scope="scope">
                    <el-input v-model="scope.row.num" @change="changeSchemeNum(scope.row)">
                    </el-input>
                  </template>
                </el-table-column>
                <el-table-column
                  prop="amount"
                  align="center"
                  label="资金"
                  width="100">
                </el-table-column>
                <el-table-column
                  prop="remark"
                  align="center"
                  label="备注"
                  width="100">
                  <template slot-scope="scope">
                    <el-input v-model="scope.row.remark">
                    </el-input>
                  </template>
                </el-table-column>
                <el-table-column
                  v-if="!readonly"
                    prop="field101"
                    align="center"
                    label="操作">
                  <template slot-scope="scope">
                    <el-button
                        size="mini"
                        type="text"
                        @click="handleDelete(scope)"
                    >移除
                    </el-button
                    >
                  </template>
                </el-table-column>
              </el-table>
            </el-tab-pane>
          </el-tabs>
        </el-col>
        <el-col :span="24">
          <div class="card_title" style="margin-top: 15px">3.采集照片</div>
          <file-upload v-model="formData.fileId" :owner-id="formData.fileId"></file-upload>
        </el-col>
        <el-col :span="24">
        </el-col>
        <el-col v-if="!readonly" :span="24" class="mt10">
            <div style="text-align: right">
                <el-button type="primary" @click="handleSave">保存</el-button>
                <el-button @click="close">取消</el-button>
            </div>
        </el-col>
      </el-form>
    </el-row>
    <methods-tree v-if="this.contractList.length > 0" scheme-type="日常养护" :con-id="conId" ref="methodsRef" @input="checkLib" :domain-id="formData.domainId"></methods-tree>
    <el-dialog title="选择资产" destroy-on-close :visible.sync="openAsset" width="65%" append-to-body v-if="openAsset">
      <asset-select @checkAsset="checkAsset"
                    :asset-type="formData.assetType == 107 ? 31 : formData.assetType == 145 ? 32 : 33"
                    :mai-sec-id="formData.maiSecId"></asset-select>
    </el-dialog>
  </div>
</template>

<script>
import selectTree from "@/components/DeptTmpl/selectTree.vue";
import RoadSection from "@/views/baseData/components/roadSection/index.vue";
import ContractSection from "@/components/ContractSection/index.vue";
import assetSelect from "@/components/AssetSelect/index.vue";
import {addProject, editProject, getProjectById} from "@/api/theft/projectManage";
import MethodsTree from "@/components/MethodsTree/index.vue";
import RouteCodeSection from "@/components/RouteCodeSection/index.vue";
import PileInput from "@/components/PileInput/index.vue";
import {v4 as uuidv4} from "uuid";
import {listAllData} from "@/api/system/dict/data";
import moment from "moment/moment";
import CostSelect from "@/components/CostSelect/index.vue";
import {listAllRouteSegments} from "@/api/system/routeSegments";
import {listAllDiseases} from "@/api/patrol/diseases";
import {formatPile} from "@/utils/ruoyi";
import { Decimal } from 'decimal.js';

export default {
  components: {
    CostSelect,
    PileInput, RouteCodeSection, assetSelect, ContractSection, RoadSection, selectTree, MethodsTree},
  data() {
    return {
      loading: false,
      formData: {
        fileId: uuidv4().substring(0, 20),
        year: moment().format('YYYY')
      },
      rules: {
        domainId: [
          { required: true, message: '管养单位不能为空', trigger: 'change' }
        ],
        year: [
          { required: true, message: '年度不能为空', trigger: 'change' }
        ],
        mtype: [
          { required: true, message: '工程分类不能为空', trigger: 'change' }
        ],
        projectType: [
          { required: true, message: '项目类别不能为空', trigger: 'change' }
        ],
        name: [
          { required: true, message: '工程名称不能为空', trigger: 'blur' }
        ],
        code: [
          { required: true, message: '工程编码不能为空', trigger: 'blur' }
        ],
        maiSecId: [
          { required: true, message: '路段名称不能为空', trigger: 'change' }
        ],
        routeType: [
          { required: true, message: '路线类型不能为空', trigger: 'change' }
        ],
        routeCode: [
          { required: true, message: '路线编码不能为空', trigger: 'change' }
        ],
        subRouteCode: [
          { required: true, message: '养护子段不能为空', trigger: 'change' }
        ],
        beginMile: [
          { required: true, message: '起点桩号不能为空', trigger: 'change' },
          { validator: (rule, value, callback) => {
              if (!/^\d+(\.\d+)?$/.test(value)) {
                callback(new Error('起点桩号只能为数字'));
              } else {
                callback();
              }
            }, trigger: 'change' }
        ],
        endMile: [
          { required: true, message: '终点桩号不能为空', trigger: 'change' },
          { validator: (rule, value, callback) => {
              if (!/^\d+(\.\d+)?$/.test(value)) {
                callback(new Error('终点桩号只能为数字'));
              } else {
                callback();
              }
            }, trigger: 'change' }
        ],
        direction: [
          { required: true, message: '上下行不能为空', trigger: 'change' }
        ],
        laneArr: [
          { required: true, message: '位置不能为空', trigger: 'blur' }
        ],
        conNameArr: [
          { required: true, message: '合同不能为空', trigger: 'change' }
        ],
        enContent: [
          { required: true, message: '主要工程内容不能为空', trigger: 'blur' }
        ]
      },
      editableTabsValue: '',
      treeIndex: 0,
      conId: '',
      contractList: [],
      contractFilterMap: {},
      defaultProps: {
        children: 'children',
        label: 'schemeName'
      },
      structureModel: false,
      assetMainType: [],
      openAsset: false,
      disType: [],
      routeList: [], // 路线编码数据
      subRouteList: [],
    }
  },
  props: {
    rowData: {
      type: Object,
      default: {}
    },
    readonly: {
      type: Boolean,
      default: false
    }
  },
  watch: {
    rowData: {
      handler(val) {
        if (val.id) {
          getProjectById(val.id).then(res => {
            this.formData = res.data
            this.formData.year = String(this.formData.year)
            this.formData.domainId = String(this.formData.domainId)
            if (this.formData.lane) this.formData.laneArr = this.formData.lane.split(',')
            if (this.formData.assetType)this.formData.assetType = String(this.formData.assetType)
            if (this.formData.assetMainType)this.formData.assetMainType = String(this.formData.assetMainType)
            this.formData.direction = String(this.formData.direction)
            this.formData.projectDetailList?.forEach(item => {
              this.editableTabsValue ? this.editableTabsValue = item.conId : null
              this.contractList.push({
                conId: item.conId,
                conName: item.conName,
                schemeList: [...item.children]
              })
            })
	          listAllDiseases({assetName: this.formData.assetType}).then(res => {
		          this.disType = res.data.map(item => {
			          return {
				          dictLabel: item.diseaseName,
				          dictValue: item.id
			          }
		          })
	          })
            if(!this.formData.fileId) this.formData.fileId = uuidv4().substring(0, 20)
            this.formData.maiSecId = res.data.maiSecId
            this.getRouteList()
          })
        }
      },
      immediate: true,
      deep: true
    },
    contractList: {
      handler(val) {
        if (val){
          const conIds = val.map(item => item.conId)
          this.formData.conIds = conIds.join(',')
          this.formData.conNameArr = val.map(item => item.conName)
        }
      },
      immediate: true,
      deep: true
    },
    "formData.routeType": {
      handler(val) {
        this.getRouteList()
      }
    },
    "formData.routeCode": {
      handler(val) {
        if (val) this.subRouteList = this.routeList.find(item => item.value == this.formData.routeCode)?.children || []
      }
    }
  },
  created() {
    this.getAssetType()
  },
  computed: {
    assetType: function () {
      const nameList = this.assetMainType.find(item => item.dictValue === this.formData.assetMainType)
      return nameList ? nameList.children : []
    },
  },
  methods: {
    getAssetType() {
      listAllData({dictType: 'sys_asset_type'}).then(res => {
        this.assetMainType = this.handleTree(res.data, "dictCode", "dictParentCode");
      })
    },
    addContract(e) {
      if (e) {
        this.formData.conId = ''
        this.editableTabsValue = e.value
        const exists = this.contractList.some(contract => contract.conId === e.value);
        if (!exists) {
          // 如果不存在，则添加到 contractList
          this.contractList.push({
            conId: e.value,
            conName: e.name,
            schemeList: []
          });
        }
      }
    },
    removeTab(targetName) {
      let tabs = this.contractList;
      let activeName = this.editableTabsValue;
      if (activeName === targetName) {
        tabs.forEach((tab, index) => {
          if (tab.conId === targetName) {
            let nextTab = tabs[index + 1] || tabs[index - 1];
            if (nextTab) {
              activeName = nextTab.conId;
            }
          }
        });
      }
      this.editableTabsValue = activeName;
      this.contractList = tabs.filter(tab => tab.conId !== targetName);
    },
    addScheme(e, index) {
      this.conId = e
      this.treeIndex = index
      this.$refs.methodsRef.openLibModel()
    },
    checkLib(checkDatas) {
      let methodList = this.contractList[this.treeIndex].schemeList
      const schemeList = []
      checkDatas = checkDatas.filter(item => item.nodeType === 2 && (item.rateFlag === 1 || !methodList.some(filterItem => filterItem.schemeCode === item.schemeCode)));
      checkDatas.forEach(item => {
        schemeList.push({
          conId: this.contractList[this.treeIndex].conId,
          schemeId: item.id,
          id: uuidv4().replace(/-/g, '').slice(0, 20),
          schemeCode: item.schemeCode,
          schemeName: item.schemeName,
          unit: item.unit,
          price: item.price,
          priceRate: item.priceRate,
          isProduction: item.safetyFeeFlag,
          rateFlag: item.rateFlag,
          decimalPlaces: item.decimalPlaces
        })
      })
      this.contractList[this.treeIndex].schemeList.push(...schemeList)
      // 对this.contractList[this.treeIndex].schemeList根据schemeId去重
      this.contractList[this.treeIndex].schemeList = this.contractList[this.treeIndex].schemeList.reduce((acc, curr) => {
        const exists = acc.some(item => item.id === curr.id);
        return exists ? acc : [...acc, curr];
      }, []);
    },
    handleDelete(e) {
      this.contractList[this.treeIndex].schemeList.splice(e.$index, 1)
    },
    async changeCalculation(row) {
      if (!this.isValidMathFormula(row.calcDesc)) {
        this.$modal.msgError('计算式错误，请检查')
        return
      }
      let num =  this.math.evaluate(row.calcDesc) || 0
      this.$set(row, 'num', this.ceilToTwo(num, row.decimalPlaces))
      await this.changeSchemeNum(row)
    },
    async changeSchemeNum(row) {
      let money = new Decimal(row.num || 0).times(row.price || 0).toNumber()
      this.$set(row, 'amount', Math.round(money))
      this.total = await this.schemeList.reduce((acc, curr) => Number(acc) + Number(curr.amount), 0)
    },
    handleSave() {
      this.$refs.elForm.validate(valid => {
        if (!valid) return
        this.loading = true
        // 将this.contractList数组中的所有schemeList数组存入this.formData.projectDetailList中
        this.formData.projectDetailList = this.contractList.reduce((acc, curr) => {
          return acc.concat(curr.schemeList)
        }, [])
        this.formData.lane = this.formData.laneArr.join(',')
        if (this.formData.fileId && Array.isArray(this.formData.fileId) && this.formData.fileId.length > 0) {
          this.formData.fileId = this.formData.fileId[0]
        } else if (Array.isArray(this.formData.fileId) &&
          this.formData.fileId.length == 0){
          this.formData.fileId = null
        }
				if (this.formData.disType) {
					// 获取label
          this.formData.disTypeName = this.disType.find(item => item.dictValue == this.formData.disType).dictLabel
        }
        if (this.formData.id) {
          editProject(this.formData).then(res => {
            this.loading = false
            this.$message.success('保存成功')
            this.close()
          })
        } else {
          addProject(this.formData).then(res => {
            this.loading = false
            this.$message.success('保存成功')
            this.close()
          })
        }
      })
    },
    changeMaiSecId() {
      const sectionName = this.$refs.roadSection.getLabel(this.formData.maiSecId)
      this.contractFilterMap = {
        sectionName
      }
      this.getRouteList()
    },
    changeRouteType() {
      this.$set(this.formData, 'routeCode', '')
    },
    changeRouteCode() {
      this.$set(this.formData, 'subRouteCode', '')
    },
    handleOpenAsset() {
      if (!this.formData.assetType) {
        this.$modal.msgWarning('请选择资产子类型')
        return
      }
      this.openAsset = true
    },
    checkAsset(asset) {
      this.formData.assetName = asset.name
      this.formData.assetId = asset.id
      this.openAsset = false
    },
    getRouteList() {
      listAllRouteSegments({
        maintenanceSectionId: this.formData.maiSecId,
        roadType: this.formData.routeType,
        merge: true
      }).then((res) => {
        if (res.code == 200) {
          let routeList = res.data || [];
          routeList = routeList.filter(item => item != null)
          let routeTemp = []
          for (let i = 0; i < routeList.length; i++) {
            let item = routeList[i]
            if (item.baseRouteCode) {
              routeTemp.push({
                value: item.baseRouteCode,
                label: item.baseRouteCode,
                children: `${item.routeSegmentsName}/${formatPile(item.pileStart)}~${formatPile(item.pileEnd)}`
              })
            }
          }
          this.$nextTick(() => {
            // 对数组routeTemp里的baseRouteCode进行分组
            this.routeList = routeTemp.reduce((acc, item) => {
              const existingGroup = acc.find(group => group.value === item.value);
              if (existingGroup) {
                existingGroup.children.push({ label: item.children, value: item.children });
              } else {
                acc.push({
                  value: item.value,
                  label: item.label,
                  children: [{ label: item.children, value: item.children }]
                });
              }
              return acc;
            }, []);
            if (this.formData.routeCode) {
              this.subRouteList = this.routeList.find(item => item.value == this.formData.routeCode).children
            }
          })
        }
      });
    },
	  changeAsset(e) {
		  this.$set(this.formData, 'disType', '')
		  this.$set(this.formData, 'assetId', '')
		  this.$set(this.formData, 'assetName', '')
		  this.disType = []
		  if (e) {
			  listAllDiseases({assetName: e}).then(res => {
				  this.disType = res.data.map(item => {
					  return {
						  dictLabel: item.diseaseName,
						  dictValue: item.id
					  }
				  })
			  })
		  }
	  },
    close() {
      this.$emit('close')
    }
  }
}
</script>
<style lang="scss" scoped>
.card_title {
  width: 200px;
  text-align: left;
  margin-bottom: 15px;
  font-weight: bold;
}
::v-deep {
  .el-tabs__header {
    padding-left: 20px;
    border: 0;
  }

  .el-tabs__content {
    border: 0;
  }

  .el-form-item__label {
    font-family: Microsoft YaHei, Microsoft YaHei;
    font-weight: 400;
    font-size: 14px;
    color: #333333;
  }

  .el-input.is-disabled .el-input__inner {
    background-color: white;
    border-color: #dfe4ed;
    color: #1d2129;
  }

  .el-textarea.is-disabled .el-textarea__inner {
    background-color: white;
    border-color: #dfe4ed;
    color: #1d2129;
  }
  .el-range-editor.is-disabled {
    background-color: white;
    border-color: #dfe4ed;
    color: #1d2129;
  }
  .el-range-editor.is-disabled input {
    background-color: white;
    border-color: #dfe4ed;
    color: #1d2129;
  }
}
</style>
<style lang="scss" scoped>
@import "@/assets/styles/business.scss";
</style>
