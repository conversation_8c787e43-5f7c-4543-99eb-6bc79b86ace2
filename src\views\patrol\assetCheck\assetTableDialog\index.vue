<template>
  <component :is="is" v-bind="$attrs" v-on="$listeners" :type="type" @check-time-change="$emit('check-time-change', $event)"/>
</template>

<script>
import BridgeTableDialog from './bridge/BridgeTableDialog.vue';
import CulvertTableDialog from './culvert/CulvertTableDialog.vue';
import TunnelTableDialog from './tunnel/TunnelTableDialog.vue';
export default {
  name: 'AssetTableDialog',
  props: {
    /**
     * 资产类型：bridge, culvert, tunnel 或者 1 ，2 ，3， 4， 5， 6
     */
    type: {
      type: String,
      require: true,
    },
  },
  components: {
    BridgeTableDialog,
    CulvertTableDialog,
    TunnelTableDialog,
  },
  computed: {
    is() {
      switch (this.type) {
        case '1':
        case '2':
        case 'bridge':
          return 'BridgeTableDialog';
        case '3':
        case '4':
        case 'culvert':
          return 'CulvertTableDialog';
        case '5':
        case '6':
        case 'tunnel':
          return 'TunnelTableDialog';
        default:
          console.error(`components 'AssetTableDialog' miss a property : type`);
        // return 'TunnelTableDialog'
      }
    },
  },
};
</script>

<style lang="scss">
.el-dialog-wrapper {
  .el-dialog__body {
    padding: 20px;
    background: #fff;
  }

  .el-table {
    background: #fff;
  }
}
</style>
