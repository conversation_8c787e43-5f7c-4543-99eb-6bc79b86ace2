<template>
  <div class="daily-maintenance">
    <div v-for="(item, index) in list" :key="index" class="list">
      <span class="top-left"></span>
      <span class="top-right"></span>
      <span class="bottom-left"></span>
      <span class="bottom-right"></span>
      <div class="name">{{ item.name }}</div>
      <div class="divider"></div>
      <div class="value">{{ item.value }} <small>{{ item.unit }}</small></div>
    </div>
  </div>
</template>

<script>
export default {
  name: "DailyMaintenance",
  data() {
    return {
      list: [
        {
          name: "待下发病害",
          value: 2901,
          unit: "个"
        },
        {
          name: "待施工病害",
          value: 5120,
          unit: "个"
        },
        {
          name: "施工中病害",
          value: 454,
          unit: "个"
        },
        {
          name: "完工病害",
          value: 93564,
          unit: "个"
        },
        {
          name: "病害事件",
          value: 108405,
          unit: "个"
        },
        {
          name: "完工数量占比",
          value: 86.31,
          unit: "%"
        },
      ],
    };
  },
  methods: {},
  components: {},
  mounted() { }
}
</script>

<style lang="scss" scoped>
@import "@/assets/styles/utils.scss";

.daily-maintenance {
  padding: vwpx(16px);
  color: #ffffff;

  display: flex;
  flex-wrap: wrap;
  align-items: center;
  justify-content: center;

  .list {
    width: 31%;
    height: vwpx(140px);
    background: rgba(0, 0, 0, 0.1);
    box-shadow: inset 0px 0px 10px 0px rgba(0,101,255,0.8);
    margin: 2% 1.1%;
    position: relative;

    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;

    .name {
      font-size: vwpx(28px);
      font-family: Microsoft YaHei UI, Microsoft YaHei UI;
      font-weight: 700;
      color: #FFFFFF;
      text-align: center;
      font-style: normal;
      text-transform: none;
    }

    .divider {
      width: 80%;
      margin: 5px 10%;
      border: 1px dotted rgba(23,116,255,0.3);
    }

    .value {
      font-family: Microsoft YaHei UI, Microsoft YaHei UI;
      font-weight: 700;
      font-size: vwpx(36px);
      color: #42ABFF;
      text-align: left;
      font-style: normal;
      text-transform: none;

      small {
        font-family: Microsoft YaHei UI, Microsoft YaHei UI;
        font-weight: 400;
        font-size: vwpx(24px);
        color: rgba(255, 255, 255, 0.8);
        text-shadow: 0px 0px 10px rgba(27, 126, 242, 0.8);
        text-align: center;
        font-style: normal;
        text-transform: none;
      }
    }

    .top-left {
      position: absolute;
      top: 0;
      left: 0;
      display: block;
      width: vwpx(20px);
      height: vwpx(20px);
      border-top: 1px solid #105ED9;
      border-left: 1px solid #105ED9;
    }

    .top-right {
      position: absolute;
      top: 0;
      right: 0;
      display: block;
      width: vwpx(20px);
      height: vwpx(20px);
      border-top: 1px solid #105ED9;
      border-right: 1px solid #105ED9;
    }

    .bottom-left {
      position: absolute;
      bottom: 0;
      left: 0;
      display: block;
      width: vwpx(20px);
      height: vwpx(20px);
      border-bottom: 1px solid #105ED9;
      border-left: 1px solid #105ED9;
    }

    .bottom-right {
      position: absolute;
      bottom: 0;
      right: 0;
      display: block;
      width: vwpx(20px);
      height: vwpx(20px);
      border-bottom: 1px solid #105ED9;
      border-right: 1px solid #105ED9;
    }
  }
}
</style>