<template>
	<div id="home_container">
		<div class="header_box">
			<div class="logo_icon"></div>
			<el-dropdown class="dropdown-menu" trigger="click">
				<div class="user">
					<img src="@/assets/home/<USER>/user.png" />
					<span class="user_name">{{ nickName }}</span>
					<span class="more_btn"></span>
				</div>
				<el-dropdown-menu slot="dropdown">
					<el-dropdown-item divided @click.native="logout">
						<span>退出登录</span>
					</el-dropdown-item>
					<!-- <el-dropdown-item divided @click.native="tabChange">
						<span>{{ tabName }}</span>
					</el-dropdown-item> -->
				</el-dropdown-menu>
			</el-dropdown>
		</div>
		<div class="left_btn" @click="leftClick"></div>
		<div class="right_btn" @click="rightClick"></div>
		<transition name="fade-slide" mode="out-in">
			<section class="list_box" v-if="currentChildren.length > 0">
				<!-- <el-carousel height="165px" :autoplay="false" arrow="always">
				<el-carousel-item v-for="(item, index) in currentChildren" :key="index" :label="index">
					<img v-for="(citem, cindex) in item" class="list_item unselectable" :src="citem.icon"
						@click="handleRouteClick(citem)" :key="cindex" />
				</el-carousel-item>
			</el-carousel> -->
				<el-carousel style="height: 330px;display: flex;flex-direction: column;justify-content: center;"
					:autoplay="false" arrow="always">
					<el-carousel-item v-for="(item, index) in currentChildren" :key="index" :label="index">
						<div class="carouselBox">
							<div v-for="(citem, cindex) in item" class="horizontalBox"><!-- 水平方向 -->
								<div v-for="(ccitem, ccindex) in citem" class="verticalBox"><!-- 垂直方向 -->
									<img class="list_item unselectable" :src="ccitem.icon" @click="handleRouteClick(ccitem)"
										:key="ccindex" />
								</div>
							</div>
						</div>
					</el-carousel-item>
				</el-carousel>
			</section>
		</transition>
		<div class="menu_box">
			<section class="click_item item0" @click="handleRouteClick(clickItems[0])">
				<transition :name="cmpName(0)" mode="out-in">
					<div class="name_box0" :key="clickItems[0].name">{{ clickItems[0].name }}</div>
				</transition>
				<transition :name="cmpName(0)" mode="out-in">
					<img class="menu_img0" :src="clickItems[0].imgsrc" :key="clickItems[0].imgsrc" />
				</transition>
				<img :src="heel" />
			</section>
			<section class="click_item item1" @click="handleRouteClick(clickItems[1])">
				<transition :name="cmpName(1)" mode="out-in">
					<div class="name_box1" :key="clickItems[1].name">{{ clickItems[1].name }}</div>
				</transition>
				<transition :name="cmpName(1)" mode="out-in">
					<img class="menu_img1" :src="clickItems[1].imgsrc" :key="clickItems[1].imgsrc" />
				</transition>
			</section>
			<section class="click_item item2" @click="handleRouteClick(clickItems[2])">
				<transition :name="cmpName(2)" mode="out-in">
					<div class="name_box2" :key="clickItems[2].name">{{ clickItems[2].name }}</div>
				</transition>
				<transition :name="cmpName(2)" mode="out-in">
					<img class="menu_img2" :src="clickItems[2].imgsrc" :key="clickItems[2].imgsrc" />
				</transition>
			</section>
			<section class="click_item item3" @click="handleRouteClick(clickItems[3])">
				<transition :name="cmpName(3)" mode="out-in">
					<div class="name_box3" :key="clickItems[3].name">{{ clickItems[3].name }}</div>
				</transition>
				<transition :name="cmpName(3)" mode="out-in">
					<img class="menu_img3" :src="clickItems[3].imgsrc" :key="clickItems[3].imgsrc" />
				</transition>
			</section>
			<section class="click_item item4" @click="handleRouteClick(clickItems[4])">
				<transition :name="cmpName(4)" mode="out-in">
					<div class="name_box4" :key="clickItems[4].name">{{ clickItems[4].name }}</div>
				</transition>
				<transition :name="cmpName(4)" mode="out-in">
					<img class="menu_img4" :src="clickItems[4].imgsrc" :key="clickItems[4].imgsrc" />
				</transition>
			</section>
			<!-- <section v-for="(item, index) in clickItems" :class="`item${index}`" class="click_item"
				@click="handleRouteClick(item)">
				<transition :name="cmpName(index)" mode="out-in">
					<div :class="`name_box${index}`" :key="item.name">{{ item.name }}</div>
				</transition>
				<transition :name="cmpName(index)" mode="out-in">
					<img :class="`menu_img&{index}`" :src="item.imgsrc" :key="item.imgsrc" />
				</transition>
			</section> -->
		</div>

		<!-- <section class="click_item item0" :class="clickItems[0].currentClass">
			{{ clickItems[0].name + clickItems[0].currentClass }}
		</section>
		<section class="click_item item1" :class="clickItems[1].currentClass">
			{{ clickItems[1].name + clickItems[1].currentClass }}
		</section>
		<section class="click_item item2" :class="clickItems[2].currentClass">
			{{ clickItems[2].name + clickItems[2].currentClass }}
		</section>
		<section class="click_item item3" :class="clickItems[3].currentClass">
			{{ clickItems[3].name + clickItems[3].currentClass }}
		</section>
		<section class="click_item item4" :class="clickItems[4].currentClass">
			{{ clickItems[4].name + clickItems[4].currentClass }}
		</section> -->
	</div>
</template>

<script>
import list0 from '@/assets/home/<USER>/list0.png'
import list1 from '@/assets/home/<USER>/list1.png'
import list2 from '@/assets/home/<USER>/list2.png'
import list3 from '@/assets/home/<USER>/list3.png'
import list4 from '@/assets/home/<USER>/list4.png'
import list5 from '@/assets/home/<USER>/list5.png'
import list6 from '@/assets/home/<USER>/list6.png'
import list7 from '@/assets/home/<USER>/list7.png'
import list8 from '@/assets/home/<USER>/list8.png'
import list9 from '@/assets/home/<USER>/list9.png'
import list10 from '@/assets/home/<USER>/list10.png'
import list11 from '@/assets/home/<USER>/list11.png'
import list12 from '@/assets/home/<USER>/list12.png'
import list13 from '@/assets/home/<USER>/list13.png'
import list14 from '@/assets/home/<USER>/list14.png'
import list15 from '@/assets/home/<USER>/list15.png'
import list16 from '@/assets/home/<USER>/list16.png'
import list17 from '@/assets/home/<USER>/list17.png'
import list18 from '@/assets/home/<USER>/list18.png'
import list19 from '@/assets/home/<USER>/list19.png'
import list20 from '@/assets/home/<USER>/list20.png'

import menu0 from '@/assets/home/<USER>/menu0.png'
import menu1 from '@/assets/home/<USER>/menu1.png'
import menu2 from '@/assets/home/<USER>/menu2.png'
import menu3 from '@/assets/home/<USER>/menu3.png'
import menu4 from '@/assets/home/<USER>/menu4.png'
import heel from '@/assets/home/<USER>/heel.png'

import list_left from '@/assets/home/<USER>/list_left.png'
import list_right from '@/assets/home/<USER>/list_right.png'

import { mapGetters, mapState } from 'vuex'
export default {
	name: 'home',
	data() {
		return {
			tabName: '切正式',
			currentChildren: [],
			clickItems: [
				{
					name: '业务平台',
					currentValue: 0,
					currentClass: '',
					imgsrc: menu0,
					path: '',
					children: [
						[[{
							text: '数据驾驶舱',
							icon: list0,
							path: '/static/home?type=1',
						},
						{
							text: '大件运输管理',
							icon: list10,
							path: '/transporation/transportation',
						}],
						[{
							text: '任务管理',
							icon: list1,
							path: '/task/NextIndex',
						},
						{
							text: '督查管理',
							icon: list11,
							path: '/supervise/superviseRecord',
						}],
						[{
							text: '日常维护管理',
							icon: list2,
							path: '/dailyMaintenance/construction/ledger',
						},
						{
							text: '养护监管考核',
							icon: list12,
							path: '/examine/config',
						}],
						[{
							text: '养护工程',
							icon: list3,
							path: '/maintenanceProject/construction/constructionProgress',
						},
						{
							text: '合同管理',
							icon: list13,
							path: '/contract/contractInfo',
						}],
						[{
							text: '被损被盗',
							icon: list4,
							path: '/theft/construction/constructionProgress',
						},
						{
							text: '预算管理',
							icon: list14,
							path: '/budgetManage/declaration/advanceView?isshow=true',
						}],
						[{
							text: '定期检测',
							icon: list5,
							path: '/regularTesting/constructionManage/constructionProgress',
						},
						{
							text: '填报任务',
							icon: list15,
							path: '/repote/repote/record',
							// path: '/repote/repote/create',
						}],
						[{
							text: '巡检查管理',
							name: '巡检查管理',
							icon: list6,
							// path: 'https://zhyhpt.yciccloud.com/centerPage?page=patrolCheck',
							path: '/cockpit',
							query: { // 参数说明：isFromHome为了跳转到/cockpit页面的巡检查专题
								isFromHome: true,
							}
							// path: '/patrol/statistic/bridgesta',
						},
						{
							text: '应急物资',
							icon: list16,
							path: '/emergencyMaterial/materialDepot',
						}],
						[{
							text: '基础数据',
							icon: list7,
							path: '/base/bridge/statistics',
						},
						{
							text: '涉路工程管理',
							icon: list17,
							path: '/engineering/engineering',
						}],
						[{
							text: '计量管理',
							icon: list8,
							path: '/calculate/metering/settlementView?type=view',
						},
						{
							text: '养护安全管理',
							icon: list18,
							path: '/anquan/labordeviceinfo',
						}],
						[{
							text: '灾害管理',
							icon: list9,
							path: '/disaster/damage',
						},
						{
							text: '监测预警',
							icon: list20,
							path: '/jgjc/earlyWarning',
						}]],
					],
				},
				{
					name: '科学决策',
					currentValue: 1,
					currentClass: '',
					imgsrc: menu1,
					// path: 'https://zhyhpt.yciccloud.com:9000/webApp/yanghu/yanghu-static.html',
				},
				{
					name: '健康监测',
					currentValue: 2,
					currentClass: '',
					imgsrc: menu2,
					// path: 'https://zhyhpt.yciccloud.com/dataCenterMap?type=1',
					path: 'onemap?type=1',
				},
				{
					name: '养护数据库',
					currentValue: 3,
					currentClass: '',
					imgsrc: menu3,
					// path: 'https://zhyhpt.yciccloud.com/centerPage',
					path: '/cockpit',
				},
				{
					name: '养护一张图',
					currentValue: 4,
					currentClass: '',
					imgsrc: menu4,
					// path: 'https://zhyhpt.yciccloud.com/dataCenterMap', // 演示地址
					path: 'onemap',
				},
			],
			isActive: true,
			heel,
			isRight: true
		}
	},
	computed: {
		...mapGetters(['sidebarRouters', 'nickName']),
		userName() {
			return this.$store.getters.name || 'admin1'
		},
		cmpName() {
			return (val) => {
				if (this.isRight) {
					return `slide${val}`
				} else {
					return `lslide${val}`
				}
			}
		}
	},
	mounted() {
		this.currentChildren = this.clickItems[0].children
		/* this.tabName = localStorage.getItem('tabName') || '切正式';
		// 初始化path
		if (this.tabName == '切正式') {
			this.clickItems.forEach(item => {
				if (item.name === '养护一张图') {
					item.path = 'https://zhyhpt.yciccloud.com/dataCenterMap'
				}
				if (item.name === '养护数据库') {
					item.path = 'https://zhyhpt.yciccloud.com/centerPage'
				}
			});
		} else {
			this.clickItems.forEach(item => {
				if (item.name === '养护一张图') {
					item.path = 'onemap'
				}
				if (item.name === '养护数据库') {
					item.path = '/cockpit'
				}
			});
		} */
	},
	watch: {
		currentChildren: {
			handler(newValue) {
				if (newValue.length > 0) {
					this.$nextTick(() => {
						let rbtn = document.querySelector('.el-carousel__arrow--right')
						let lbtn = document.querySelector('.el-carousel__arrow--left')
						let rimg = document.createElement('img')
						let limg = document.createElement('img')
						rbtn.innerHTML = ''
						lbtn.innerHTML = ''
						rimg.src = list_right
						limg.src = list_left
						rimg.style = 'width: 100%;height: 100%;'
						limg.style = 'width: 100%;height: 100%;'
						rbtn.appendChild(rimg)
						lbtn.appendChild(limg)
					})
				}
			},
			immediate: true
		}
	},
	methods: {
		getCurrentChildren() {
			this.currentChildren = []
			if (this.clickItems[0].name === '业务平台') {
				this.currentChildren = this.clickItems[0].children
			}
		},
		rightClick() {
			this.isRight = true
			const firstItem = this.clickItems.pop() // 移除第一项并返回该值
			this.clickItems.unshift(firstItem) // 将移除的项添加到数组末尾
			this.getCurrentChildren()
			/* this.clickItems.forEach((item) => {
				if (item.currentValue + 1 > 4) {
					item.currentValue = 0
				} else {
					item.currentValue += 1
				}
				item.currentClass = `anim${item.currentValue}`
			}) */
		},
		leftClick() {
			this.isRight = false
			const firstItem = this.clickItems.shift() // 移除第一项并返回该值
			this.clickItems.push(firstItem) // 将移除的项添加到数组末尾
			this.getCurrentChildren()
		},
		async logout() {
			this.$confirm('确定注销并退出系统吗？', '提示', {
				confirmButtonText: '确定',
				cancelButtonText: '取消',
				type: 'warning',
			})
				.then(() => {
					this.$store.dispatch('LogOut').then(() => {
						location.href = '/index'
					})
				})
				.catch(() => { })
		},
		handleRouteClick(obj) {
			this.currentChildren = []
			if (['科学决策'].includes(obj.name)) {
				this.$message('系统开发中！');
			} else {
				if (obj.path) {
					/* if (this.tabName == '切正式') {
						if (['养护一张图', '养护数据库', '科学决策', '健康监测'].includes(obj.name)) {
							window.location.href = obj.path
						} else {
							this.$router.push({ path: obj.path, query: obj?.query }).catch(() => { })
						}
					} else {
						if (['科学决策'].includes(obj.name)) {
							window.location.href = obj.path
						} else {
							this.$router.push({ path: obj.path, query: obj?.query }).catch(() => { })
						}
					} */
					this.$router.push({ path: obj.path, query: obj?.query }).catch(() => { })
				} else if (obj?.children?.length > 0) {
					this.currentChildren = obj.children
				}
			}
		},
		tabChange() {
			if (this.tabName == '切正式') {
				this.tabName = '切演示'
				this.clickItems.forEach(item => {
					if (item.name === '养护一张图') {
						item.path = 'onemap'
					} else if (item.name === '养护数据库') {
						item.path = '/cockpit'
					} else if (item.name === '健康监测') {
						item.path = 'onemap?type=1'
					}
				});
			} else {
				this.tabName = '切正式'
				this.clickItems.forEach(item => {
					if (item.name === '养护一张图') {
						item.path = 'https://zhyhpt.yciccloud.com/dataCenterMap'
					} else if (item.name === '养护数据库') {
						item.path = 'https://zhyhpt.yciccloud.com/centerPage'
					} else if (item.name === '健康监测') {
						item.path = 'https://zhyhpt.yciccloud.com/dataCenterMap?type=1'
					}
				});
			}
			localStorage.setItem('tabName', this.tabName);
		}
	},
}


</script>

<style lang="scss" scoped>
$btn_width: 145px;


/* 组合动画 */
.fade-slide-enter-active {
	transition: all 1s ease-out;
}

.fade-slide-leave-active {
	transition: all 0.4s ease-in;
}

.fade-slide-enter {
	opacity: 0;
	transform: translateY(20px);
}

.fade-slide-leave-to {
	opacity: 0;
	transform: translateY(-20px);
}

/* 逆时针 */
/*0 向右 */
.slide0-enter-active,
.slide0-leave-active {
	transition: all 0.4s ease-in;
}

.slide0-enter {
	transform: translateX(0);
	opacity: 0;
}

.slide0-leave-to {
	transform: translateX(30%);
	opacity: 0;
}

/* 1 向上*/
.slide1-enter-active,
.slide1-leave-active {
	transition: all 0.4s ease-in;
}

.slide1-enter {
	transform: translateY(0);
	opacity: 0;
}

.slide1-leave-to {
	transform: translateY(-30%);
	opacity: 0;
}

/* 2 向左*/
.slide2-enter-active,
.slide2-leave-active {
	transition: all 0.4s ease-in;
}

.slide2-enter {
	transform: translateX(0);
	opacity: 0;
}

.slide2-leave-to {
	transform: translateX(-30%);
	opacity: 0;
}

/* 3向左 */
.slide3-enter-active,
.slide3-leave-active {
	transition: all 0.4s ease-in;
}

.slide3-enter {
	transform: translateX(0);
	opacity: 0;
}

.slide3-leave-to {
	transform: translateX(-30%);
	opacity: 0;
}

/* 4向右 */
.slide4-enter-active,
.slide4-leave-active {
	transition: all 0.4s ease-in;
}

.slide4-enter {
	transform: translateX(0);
	opacity: 0;
}

.slide4-leave-to {
	transform: translateX(30%);
	opacity: 0;
}

/* 顺时针 */
/*0 向左 */
.lslide0-enter-active,
.lslide0-leave-active {
	transition: all 0.4s ease-in;
}

.lslide0-enter {
	transform: translateX(0);
	opacity: 0;
}

.lslide0-leave-to {
	transform: translateX(-30%);
	opacity: 0;
}

/* 1 向下*/
.lslide1-enter-active,
.lslide1-leave-active {
	transition: all 0.4s ease-in;
}

.lslide1-enter {
	transform: translateY(0);
	opacity: 0;
}

.lslide1-leave-to {
	transform: translateY(30%);
	opacity: 0;
}

/* 2向右 */
.lslide2-enter-active,
.lslide2-leave-active {
	transition: all 0.4s ease-in;
}

.lslide2-enter {
	transform: translateX(0);
	opacity: 0;
}

.lslide2-leave-to {
	transform: translateX(30%);
	opacity: 0;
}

/* 3向右 */
.lslide3-enter-active,
.lslide3-leave-active {
	transition: all 0.4s ease-in;
}

.lslide3-enter {
	transform: translateX(0);
	opacity: 0;
}

.lslide3-leave-to {
	transform: translateX(30%);
	opacity: 0;
}

/* 4 向上*/
.lslide4-enter-active,
.lslide4-leave-active {
	transition: all 0.4s ease-in;
}

.lslide4-enter {
	transform: translateY(0);
	opacity: 0;
}

.lslide4-leave-to {
	transform: translateY(-30%);
	opacity: 0;
}

#home_container {
	width: 100%;
	height: 100%;
	background: url('~@/assets/home/<USER>/bg.png') no-repeat;
	background-size: 100% 100%;

	.header_box {
		display: flex;
		justify-content: space-between;
		width: 100%;

		.logo_icon {
			width: 180px;
			height: 58px;
			background: url('~@/assets/home/<USER>/logo_icon.png') no-repeat;
			background-size: 100%;
			margin-top: 10px;
			margin-left: 6px;
		}

		.user {
			cursor: pointer;
			display: flex;
			height: 100%;
			align-items: center;
			margin-right: 20px;

			.user_name {
				font-size: 23px;
				font-family: Source Han Sans CN, Source Han Sans CN-Bold;
				font-weight: 700;
				text-align: left;
				color: #ffffff;
			}

			img {
				width: 44px;
				height: 44px;
				margin-right: 14px;
			}

			.more_btn {
				display: inline-block;
				border-left: 2px solid #ffffff;
				border-top: 2px solid #ffffff;
				transform: rotate(225deg);
				width: 12px;
				height: 12px;
				margin-left: 14px;
			}
		}
	}

	.menu_item {
		display: flex;
		justify-content: space-between;
		width: 975px;
		height: 104px;
		margin: 0 auto;
		position: relative;
		top: 90px;

		img {
			cursor: pointer;
			transition: transform 0.3s ease;

			&:hover {
				transform: scale(1.1);
			}
		}

		.menu_item0,
		.menu_item6 {
			margin-top: 30px;
			width: 132px;
			height: 210px;
		}

		.menu_item1,
		.menu_item5 {
			margin-top: 17px;
			width: 114px;
			height: 168px;
		}

		.menu_item2,
		.menu_item4 {
			margin-top: 4px;
			width: 105px;
			height: 144px;
		}

		.menu_item3 {
			width: 97px;
			height: 136px;
		}
	}

	.left_btn,
	.right_btn {
		width: 30px;
		height: 30px;
		position: absolute;
		top: 50%;
		transform: translate(-50%);
		cursor: pointer;
		z-index: 999;
	}

	.left_btn {
		left: $btn_width;
		background: url('~@/assets/home/<USER>/left.png') no-repeat;
	}

	.right_btn {
		right: $btn_width;
		background: url('~@/assets/home/<USER>/right.png') no-repeat;
	}

	.menu_box {
		position: relative;
		top: 36%;
		margin: 0 auto;
		width: 70%;
		height: 50vh;
		// background-color: #00fecf26;

		.click_item {
			cursor: pointer;
			position: absolute;
			display: flex;
			flex-direction: column;
			align-items: center;
			transition: transform 0.3s ease;

			&:hover {
				transform: scale(1.1);
			}

			.menu_img0 {
				width: 259px;
				height: 202px;
			}

			.menu_img1,
			.menu_img4 {
				width: 214px;
				height: 172px;
			}

			.menu_img2,
			.menu_img3 {
				width: 166px;
				height: 133px;
			}

			.name_box0 {
				text-align: center;
				letter-spacing: 2px;
				font-size: 20px;
				font-weight: 700;
				color: #00fecf;
				line-height: 48px;
				margin-bottom: 4px;
				width: 184px;
				height: 48px;
				background: url('~@/assets/home/<USER>/name_highlight.png') no-repeat;
			}

			.name_box1,
			.name_box4 {
				text-align: center;
				letter-spacing: 2px;
				font-size: 18px;
				font-weight: 700;
				color: #ffffff;
				line-height: 40px;
				margin-bottom: 4px;
				width: 163px;
				height: 40px;
				background: url('~@/assets/home/<USER>/normal_bg.png') no-repeat;
				background-size: 163px 40px;
			}

			.name_box2,
			.name_box3 {
				text-align: center;
				letter-spacing: 2px;
				font-size: 16px;
				font-weight: 700;
				color: #ffffff;
				line-height: 34px;
				margin-bottom: 4px;
				width: 138px;
				height: 34px;
				background: url('~@/assets/home/<USER>/normal_bg.png') no-repeat;
				background-size: 138px 34px;
			}
		}
	}

	.item0 {
		bottom: -6%;
		left: 40%;
	}

	.item1 {
		bottom: 21%;
		right: 7.5%;

		.menu_img {
			transform: scale(0.8);
		}
	}

	.item2 {
		bottom: 53%;
		right: 23%;

		.menu_img {
			transform: scale(0.7);
		}
	}

	.item3 {
		bottom: 53%;
		left: 23%;

		.menu_img {
			transform: scale(0.7);
		}
	}

	.item4 {
		bottom: 21%;
		left: 7.5%;

		.menu_img {
			transform: scale(0.8);
		}
	}
}

.anim0 {
	animation: theanimation0 1s linear 1 forwards alternate;
}

@keyframes theanimation0 {
	0% {
		bottom: 13%;
		left: 46%;
		transform: scale(1);
	}

	50% {
		bottom: 14%;
		left: 35%;
	}

	100% {
		bottom: 24%;
		left: 23%;
		transform: scale(0.8);
	}
}

.anim1 {
	animation: theanimation1 1s linear 1 forwards alternate;
}

@keyframes theanimation1 {
	0% {
		bottom: 24%;
		left: 23%;
		transform: scale(1);
	}

	100% {
		bottom: 37%;
		left: 32%;
		transform: scale(0.6);
	}
}

.anim2 {
	animation: theanimation2 1s linear 1 forwards alternate;
}

@keyframes theanimation2 {
	0% {
		bottom: 37%;
		left: 32%;
		opacity: 1;
		transform: scale(1);
	}

	25% {
		opacity: 0.5;
	}

	50% {
		opacity: 0;
	}

	75% {
		opacity: 0.5;
	}

	100% {
		bottom: 37%;
		left: 61%;
		opacity: 1;
		transform: scale(0.6);
	}
}

.anim3 {
	animation: theanimation3 1s linear 1 forwards alternate;
}

@keyframes theanimation3 {
	0% {
		bottom: 37%;
		left: 61%;
	}

	100% {
		bottom: 24%;
		left: 70%;
		transform: scale(0.8);
	}
}

.anim4 {
	animation: theanimation4 1s linear 1 forwards alternate;
}

@keyframes theanimation4 {
	0% {
		bottom: 24%;
		left: 70%;
	}

	50% {
		bottom: 14%;
		left: 60%;
	}

	100% {
		bottom: 13%;
		left: 46%;
	}
}

.unselectable {
	-webkit-user-select: none;
	/* Safari 3.1+ */
	-moz-user-select: none;
	/* Firefox 2+ */
	-ms-user-select: none;
	/* IE 10+ */
	user-select: none;
	/* 标准语法 */
}

.list_box {
	width: 80%;
	position: absolute;
	top: 10%;
	left: 50%;
	transform: translateX(-50%);
	text-align: center;
}

.list_item {
	cursor: pointer;
	// width: 103px;
	height: 152px;
	// margin-top: 6px;
	// margin-right: 16px;
	transition: transform 0.3s ease;

	&:hover {
		transform: scale(1.05);
	}
}

::v-deep .el-carousel__indicators--horizontal {
	display: none;
}

::v-deep .el-carousel__arrow:hover {
	background-color: transparent;
}

::v-deep .el-carousel__arrow {
	width: 31px;
	height: 60px;

	img {
		-webkit-user-select: none;
		/* Safari 3.1+ */
		-moz-user-select: none;
		/* Firefox 2+ */
		-ms-user-select: none;
		/* IE 10+ */
		user-select: none;
		/* 标准语法 */
	}
}

::v-deep .el-carousel__container {
	height: 330px;
}

.carouselBox {
	display: flex;
	justify-content: center;
	margin: 10px;

	.horizontalBox {
		display: flex;
		flex-direction: column;
		margin-right: 6px;
	}
}
</style>