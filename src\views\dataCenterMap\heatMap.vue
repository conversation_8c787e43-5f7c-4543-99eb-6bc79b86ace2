<template>
  <div class="heat-map">
    <!-- <img class="heat-map-quite" src="@/assets/map/quite.png" @click="handleQuit" /> -->
    <div class="heat-map-quite" title="退出" :style="{ top: '5px' }">
      <div class="icon-img">
        <img src="@/assets/map/quite.png" @click="handleQuit" />
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'heatMap',
  data() {
    return {}
  },
  created() { },
  methods: {
    handleQuit() {
      this.$router.push({ path: "/homeHB" });
      // this.$router.go(-1);
    }
  }
}
</script>

<style lang="scss" scoped>
@import "@/assets/styles/utils.scss";

.heat-map {
  width: 100vw;
  height: 100vh;
  background: url('~@/assets/map/heat-map.jpg') no-repeat center center;
  background-size: 100% 100%;
  position: relative;
}

.heat-map-quite {
    position: absolute;
    right: vwpx(10px);
    cursor: pointer;
    color: #ffffff;
    z-index: 10000;

    .icon-img {
      display: flex;
      align-items: center;

      img {
        width: vwpx(100px);
        height: vwpx(100px);
      }
    }
  }
</style>