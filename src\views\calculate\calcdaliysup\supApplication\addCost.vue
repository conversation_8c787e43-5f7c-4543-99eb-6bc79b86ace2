<template>
  <div class="road-interflow-edit">
    <el-row :gutter="15">
      <el-form
        ref="elForm"
        :inline="true"
        :model="formData"
        :rules="rules"
        label-width="120px"
      >
        <el-col :span="12">
          <el-form-item label="施工计量单编码" prop="settleCode">
            <el-input
              @focus="openSettleDialog"
              readonly
              v-model="formData.settleCode"
              style="width: 230px"
              ></el-input
            >
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="费用类型" prop="costType">
            <dict-select
              v-model="formData.costType"
              type="cost_name"
              style="width: 230px"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="金额" prop="amount">
            <el-input
              v-model="formData.amount"
              type="number"
              style="width: 230px"
            />
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="备注" prop="remark">
            <el-input
              v-model="formData.remark"
              :rows="3"
              type="textarea"
              style="width: 610px"
            />
          </el-form-item>
        </el-col>
        <el-col
          :span="24"
          style="text-align: right; padding-right: 7.5px; margin-top: 18px"
        >
          <el-button type="primary" @click="onSave">保 存</el-button>
          <el-button @click="onClose">退 出</el-button>
        </el-col>
      </el-form>
    </el-row>

    <el-dialog
      v-if="settleDialog"
      :visible.sync="settleDialog"
      append-to-body
      modal-append-to-body
      title="选择结算计量单"
      width="1000px"
    >
      <el-form
        ref="settleForm"
        :inline="true"
        :model="settleParams"
      >
        <el-form-item>
          <selectTree
            :key="'domainId'"
            style="width: 120px"
            v-model="settleParams.domainId"
            :deptType="100"
            :deptTypeList="[1, 3, 4]"
            placeholder="管养单位"
            clearable
            filterable
          />
        </el-form-item>
        <el-form-item>
          <selectTree
            :key="'constructionUnit'"
            style="width: 120px"
            v-model="settleParams.calcDomainId"
            :dept-type="100"
            placeholder="施工单位"
            :filter-keys="['云南省交通投资建设集团有限公司', '云南交投投资有限公司']"
            :expand-all="false"
            clearable
            filterable
          />
        </el-form-item>
        <el-form-item>
          <el-input
            v-model="settleParams.settleCode"
            placeholder="计量单编码"
          />
        </el-form-item>
        <el-form-item label="">
          <el-date-picker
            style="width: 120px"
            v-model="settleParams.year"
            type="year"
            value-format="yyyy"
            placeholder="年份"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item>
          <el-input
            v-model="settleParams.number"
            placeholder="期数"
            style="width: 120px"
          />
        </el-form-item>
        <el-form-item>
          <el-button
            type="primary"
            icon="el-icon-search"
            size="mini"
            @click="getSettleData"
            >搜索</el-button
          >
          <el-button
            icon="el-icon-refresh"
            size="mini"
            @click="resetSettleQuery"
            >重置</el-button
          >
        </el-form-item>
      </el-form>
      <el-table v-adjust-table
        size="mini"
        style="width: 100%"
        border
        height="500px"
        :data="settleData"
        row-key="id"
        ref="dataTable"
        stripe
        highlight-current-row
        @row-click="handleClickRow"
      >
        <el-table-column label="序号" align="center" type="index" width="50" />
        <template v-for="(column, index) in settleColumns">
          <el-table-column
            :label="column.label"
            v-if="column.visible"
            align="center"
            :prop="column.field"
            :width="column.width"
            show-overflow-tooltip
          >
          </el-table-column>
        </template>

      </el-table>
      <pagination
        v-show="settleTotal > 0"
        :total="settleTotal"
        :page.sync="settleParams.pageNum"
        :limit.sync="settleParams.pageSize"
        @pagination="getSettleData"
      />
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="confirmCurSettle">确 定</el-button>
        <el-button @click="settleDialog = false">退出</el-button>
      </span>
    </el-dialog>
    <el-dialog
      v-if="projectDialog"
      :visible.sync="projectDialog"
      append-to-body
      modal-append-to-body
      title="选择项目"
      width="1000px"
    >
      <el-form
        ref="settleForm"
        :inline="true"
        :model="projectParams"
      >
        <el-form-item>
          <el-input
            v-model="projectParams.projName"
            placeholder="项目名称"
            style="width: 120px"
          />
        </el-form-item>
        <el-form-item>
          <el-input
            v-model="settleParams.settleCode"
            placeholder="结算单编码"
          />
        </el-form-item>
        <el-form-item>
          <el-button
            type="primary"
            icon="el-icon-search"
            size="mini"
            @click="getProjectData"
            >搜索</el-button
          >
          <el-button
            icon="el-icon-refresh"
            size="mini"
            @click="resetSettleQuery"
            >重置</el-button
          >
        </el-form-item>
      </el-form>
      <el-table v-adjust-table
        size="mini"
        style="width: 100%"
        border
        height="500px"
        :data="projectData"
        row-key="id"
        ref="dataTable"
        stripe
        highlight-current-row
        @row-click="handleClickRow"
      >
        <el-table-column label="序号" align="center" type="index" width="50" />
        <template v-for="(column, index) in projectColumns">
          <el-table-column
            :label="column.label"
            v-if="column.visible"
            align="center"
            :prop="column.field"
            show-overflow-tooltip
            :width="column.width"
          >
          </el-table-column>
        </template>

      </el-table>
      <pagination
        v-show="projectTotal > 0"
        :total="projectTotal"
        :page.sync="projectParams.pageNum"
        :limit.sync="projectParams.pageSize"
        @pagination="getProjectData"
      />
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="confirmCurProject">确 定</el-button>
        <el-button @click="projectDialog = false">退出</el-button>
      </span>
    </el-dialog>
  </div>

</template>

<script>
import {
  addFundAdjust,
  editFundAdjust,
  getFundadjustList,
  getFundadjustProjectList
} from "@/api/calculate/calcdailysup/supApplication";
import selectTree from "@/components/DeptTmpl/selectTree.vue";

export default {
  components: { selectTree },
  data() {
    return {
      formData: {},
      rules: {
        settleCode: [
          { required: true, message: "请选择计量单编码", trigger: "blur" },
        ],
        dataType: [
          { required: true, message: "请选择费用类型", trigger: "change" },
        ],
      },
      settleDialog: false,
      settleParams: {
        pageNum: 1,
        pageSize: 10,
      },
      settleData: [],
      settleTotal: 0,
      settleColumns: [
        {
          key: 0,
          field: "numberName",
          label: `期数`,
          visible: true,
        },
        {
          key: 1,
          field: "name",
          label: `计量结算单名称`,
          visible: true,
        },
        {
          key: 2,
          field: "code",
          label: `计量结算单编号`,
          visible: true,
        },
        {
          key: 3,
          field: "domainName",
          label: `管养单位`,
          visible: true,
        },
        {
          key: 4,
          field: "calcDomainName",
          label: `申请计量单位`,
          visible: true,
        },
        {
          key: 5,
          field: "maiSecName",
          label: `路段名称`,
          visible: true,
        },
      ],
      curSelectSettle: {},
      projectDialog: false,
      projectParams: {
        pageNum: 1,
        pageSize: 10,
      },
      projectData: [],
      projectTotal: 0,
      projectColumns: [
        {
          key: 0,
          field: "projName",
          label: `项目名称`,
          visible: true,
        },
        {
          key: 1,
          field: "maiSecName",
          label: `路段名称`,
          visible: true,
        },
        {
          key: 2,
          field: "code",
          label: `桩号范围`,
          visible: true,
        },
        {
          key: 3,
          field: "domainName",
          label: `工程分类`,
          visible: true,
        },
        {
          key: 4,
          field: "calcDomainName",
          label: `所属工程类别`,
          visible: true,
        },
        {
          key: 5,
          field: "maiSecName",
          label: `构造物名称`,
          visible: true,
        },
        {
          key: 6,
          field: "maiSecName",
          label: `缺陷责任期`,
          visible: true,
        },
        {
          key: 7,
          field: "maiSecName",
          label: `预计工期`,
          visible: true,
        },
        {
          key: 8,
          field: "maiSecName",
          label: `预计开始时间`,
          visible: true,
        },
        {
          key: 9,
          field: "maiSecName",
          label: `预计结束时间`,
          visible: true,
        },
        {
          key: 10,
          field: "maiSecName",
          label: `状态`,
          visible: true,
        },
        {
          key: 11,
          field: "maiSecName",
          label: `备注`,
          visible: true,
        },
      ],
    };
  },
  props: {
    superId: {
      type: String,
      default: "",
    },
    rowData: {
      type: Object,
      default: () => {
        return {};
      },
    },
    maiSecId: {
      type: String,
      default: "",
    },
  },
  watch: {
    rowData: {
      handler(val) {
        if (val.id) {
          this.formData = JSON.parse(JSON.stringify(val));
        }
      },
      immediate: true,
      deep: true,
    },
  },
  methods: {
    openSettleDialog() {
      this.resetSettleQuery()
      this.settleParams.maiSecId = this.maiSecId;
      this.getSettleData()
    },
    getSettleData() {
      getFundadjustList(this.settleParams).then((res) => {
        console.log(res);
        this.settleData = res.rows
        this.settleTotal = res.total
        this.settleDialog = true;
      });
    },
    resetSettleQuery() {
      this.settleParams = {
        pageNum: 1,
        pageSize: 10,
      };
      this.projectParams = {
        pageNum: 1,
        pageSize: 10,
      };
    },
    handleClickRow(row) {
      this.curSelectSettle = row
    },

    confirmCurSettle() {
      this.formData.settleCode = this.curSelectSettle.code
      this.formData.settleId = this.curSelectSettle.id
      this.settleDialog = false;
    },
    openProjectDialog() {
      if (!this.formData.settleId) {
        this.$modal.msgError("请先选择施工计量单");
        return
      }
      this.resetSettleQuery()
      this.projectParams.settleId = this.formData.settleId;
      this.getProjectData()
    },
    getProjectData() {
      getFundadjustProjectList(this.projectParams).then(res=> {
        console.log(res);
        this.projectData = res.rows
        this.projectTotal = res.total
        this.projectDialog = true;
      })
    },
    confirmCurProject() {
      this.formData.projCode = this.curSelectSettle.code
      this.formData.projId = this.curSelectSettle.id
      this.projectDialog = false;
    },
    onSave() {
      this.$refs.elForm.validate((valid) => {
        if (!valid) return;
        this.formData.superId = this.superId;
        if (this.formData.id) {
          editFundAdjust(this.formData).then(() => {
            this.$modal.msgSuccess("保存成功");
            this.onClose();
          });
        } else {
          addFundAdjust(this.formData).then(() => {
            this.$modal.msgSuccess("保存成功");
            this.onClose();
          });
        }
      });
    },

    onClose() {
      this.$emit("close", "5");
    },
  },
};
</script>

<style lang="scss" scoped></style>
<style lang="scss" scoped>
@import "@/assets/styles/business.scss";
</style>
