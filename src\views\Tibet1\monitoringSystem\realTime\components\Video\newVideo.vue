<template>
  <div v-if="isShowVideo" style="width: 100%; height: 100%; position: relative">
    <video id="video" preload="auto" class="video-js vjs-default-skin"
      style="width: 100%; height: 100%; object-fit: fill">
      <source :src="url" type="application/x-mpegURL" />
    </video>
  </div>
</template>
<script>
import videojs from "video.js";
import "videojs-contrib-hls";
export default {
  name: "newVideo",
  props: ["url"],
  data() {
    return {
      player: null,
      isShowVideo: false,
      timer: null,
    };
  },
  mounted() {
    if (this.url != "") {
      this.isShowVideo = true;
      this.$nextTick(() => {
        // console.log("url", this.url)
        this.getVideo();
      });
    }
  },
  beforeDestroy() {
    if (this.player) {
      this.player.dispose(); // Removing Players,该方法会重置videojs的内部状态并移除dom
    }
  },
  activated() {
    if (this.player) {
      this.player.play();
    }
  },
  deactivated() {
    if (this.player) {
      this.player.pause();
    }
  },
  methods: {
    getVideo() {
      this.player = videojs(
        "video",
        {
          bigPlayButton: true,
          textTrackDisplay: false,
          posterImage: false,
          errorDisplay: false,
          controls: true,
          hls: {
            withCredentials: true,
          },
        },
        // function () {
        //   // 延迟0.5秒播放
        //   this.timer = setTimeout(() => {
        //     // this.play();
        //   }, 500);
        // }
      );
    },
  },
};
</script>
