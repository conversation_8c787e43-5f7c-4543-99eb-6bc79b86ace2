<template>
  <div class="road-interflow-edit">
    <el-table v-adjust-table
        ref="dataTable"
        v-loading="loading"
        :data="tableData"
        height="500px"
        border
        highlight-current-row
        row-key="id"
        size="mini"
        stripe
        @row-click="checkBudget"
        style="width: 100%"
    >
      <el-table-column
          align="center"
          label="序号"
          type="index"
          width="50"
      />
      <template v-for="(column,index) in columns">
        <el-table-column v-if="column.visible"
                         :label="column.label"
                         :prop="column.field"
                         align="center">
          <template slot-scope="scope">
            <dict-tag v-if="column.dict" :options="dict.type[column.dict]" :value="scope.row[column.field]"/>
            <template v-else-if="column.slots">
              <RenderDom :index="index" :render="column.render" :row="scope.row"/>
            </template>
            <span v-else-if="column.isTime">{{ parseTime(scope.row[column.field], '{y}-{m}-{d}') }}</span>
            <span v-else>{{ scope.row[column.field] }}</span>
          </template>
        </el-table-column>
      </template>
    </el-table>
  </div>
</template>

<script>
import {getListBySectionParam} from "@/api/maintenanceProject/projectManage";

export default {
  dicts: ['project_type'],
  props: {
    budgetParams: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      tableData: [],
      loading: false,

      columns: [
        {key: 0, width: 100, field: 'projectName', label: `项目名称`, visible: true},
        {key: 1, width: 100, field: 'mileageStr', label: `工程桩号`, visible: true},
        {key: 2, width: 100, field: 'projType', label: `工程类别`, visible: true, dict: 'project_type'},
        {key: 3, width: 100, field: 'conNames', label: `合同`, visible: true},
        {key: 4, width: 100, field: 'sumFund', label: `专项预计划金额`, visible: true},
        {key: 5, width: 100, field: 'remark', label: `备注`, visible: true},
      ]
    }
  },
  mounted() {
    console.log(this.budgetParams)
    getListBySectionParam(this.budgetParams).then(res => {
      this.tableData = res.data
    })
  },
  methods: {
    checkBudget(e) {
      this.$emit('checkBudget', e)
    }
  }
}
</script>

<style scoped lang="scss">

</style>
<style lang="scss" scoped>
@import "@/assets/styles/business.scss";
</style>
