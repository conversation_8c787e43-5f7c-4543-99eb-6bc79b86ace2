<template>
  <div class="app-container">
    <!--筛选区开始-->
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" label-width="68px">
      <el-form-item label="隧道" prop="assetName">
        <el-input v-model="assetName" readonly @click.native="selectTunnel" placeholder="点击选择..."/>
      </el-form-item>
      <el-form-item label="年份" prop="year">
        <el-select v-model="queryParams.year" placeholder="选择年份" clearable style="width: 160px">
          <el-option v-for="year in yearsArray"
                     :label="year"
                     :value="year"/>
        </el-select>
      </el-form-item>
      <el-form-item label="月份" prop="month">
        <el-select v-model="queryParams.month" placeholder="选择月份" clearable style="width: 160px">
          <el-option v-for="dict in dict.type.each_month"
                     :key="dict.value"
                     :label="dict.label"
                     :value="dict.value"/>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <!--操作按钮区开始-->
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="warning"
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['patrol:deviceFaultRecord:export']"
        >导出
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="primary" icon="el-icon-download" size="mini" @click="handleExportCard">
          导出卡片
        </el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>
    <!--操作按钮区结束-->

    <!--数据表格开始-->
    <div class="tableDiv">
      <el-table
        ref="table"
        size="mini"
        :height="showSearch ? 'calc(100vh - 320px)' : 'calc(100vh - 260px)'"
        style="width: 100%"
        v-loading="loading"
        border
        highlight-current-row
        @current-change="handleCurrentChange"
        :data="deviceCheckList"
      >
<!--        <el-table-column type="selection" width="40" align="center"/>-->
        <el-table-column fixed label="序号" type="index" width="50">
          <template v-slot="scope">
            {{ (scope.$index + (queryParams.pageNum - 1) * queryParams.pageSize)+1 }}
          </template>
        </el-table-column>
        <el-table-column label="路线编码" align="center" prop="routeCode"/>
        <el-table-column label="养护路段" align="center" prop="routeName"/>
        <el-table-column label="隧道编码" align="center" prop="assetCode"/>
        <el-table-column label="隧道名称" align="center" prop="assetName" width="180"/>
        <el-table-column label="管养单位" align="center" prop="deptName"/>
        <el-table-column label="年份" align="center" prop="year"/>
        <el-table-column label="月份" align="center" prop="month"/>
        <el-table-column label="故障数量" align="center" prop="faultNum"/>
      </el-table>

      <pagination
        v-show="total>0"
        :total="total"
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
        @pagination="getList"
      />
    </div>
    <!--数据表格结束-->

    <!-- 选择隧道 -->
    <select-tunnel ref="select" @update:checkEntity="handleCheckEntityUpdate"/>

    <!-- 导出卡片iframe -->
    <el-dialog
      title="导出预览"
      :visible.sync="showIframe"
      width="90%"
      :close-on-click-modal="false"
      append-to-body
      class="export-preview-dialog"
    >
      <iframe
        :src="src"
        id="reportView"
        frameborder="no"
        style="width: 100%; height: 75vh"
        scrolling="auto"
      />
    </el-dialog>
  </div>
</template>

<script>
import { getAssetTotalCount, listDeviceCheck, listMonthlyReport } from '@/api/patrol/deviceCheck'
import {getToken} from "@/utils/auth";
import SelectTunnel from "../TunnelTableDialog.vue";
import {mapGetters} from "vuex";

export default {
  name: "FaultMonthlyReport",
  dicts: ['each_month'],
  components: {SelectTunnel},
  data() {
    return {
      // 遮罩层
      loading: true,
      // 是否显示导出卡片iframe
      showIframe: false,
      // 显示搜索条件
      showSearch: false,
      // 总条数
      total: 0,
      // 隧道机电日常巡查表格数据
      deviceCheckList: null,
      // 表单参数
      deviceForm: {},
      // 用户导入参数
      upload: {
        // 是否显示弹出层（用户导入）
        open: false,
        // 是否禁用上传
        isUploading: false,
        // 是否更新已经存在的用户数据
        updateSupport: 0,
        // 设置上传的请求头部
        headers: {Authorization: "Bearer " + getToken()},
        // 上传的地址
        url: process.env.VUE_APP_BASE_API + "/system/user/importData"
      },
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        assetId: null,
        year: null,
        month: null
      },
      assetName:null,
      //年份
      yearsArray: [],
      currentRow: null
    };
  },
  watch: {
    // 根据名称筛选部门树
  },
  computed: {
    ...mapGetters(['nickName','userId']),
    src() {
      let reportId = process.env.VUE_APP_REPORT_VIEWER_REPORT_ID_DEVICE_FAULT_MONTHLY
      let params = `${process.env.VUE_APP_REPORT_VIEWER_URL}${reportId}?token=${getToken()}`
      if (this.currentRow) {
        const assetId = this.currentRow.assetId
        const year = this.currentRow.year
        const month = this.currentRow.month
        params += '&assetId=' + assetId + '&year=' + year + '&month=' + month
      }
      console.log("params: ", params)
      return params
    },
  },
  created() {
    this.getList();
    this.getYear();
  },
  methods: {
    /** 选择桥梁 */
    selectTunnel() {
      this.$refs.select.show();
    },
    handleCheckEntityUpdate(newValue) {
      // 在这里更新父组件中的值
      this.queryParams.assetId = newValue.assetId;
      // this.queryParams.assetCode = newValue.tunnelCode;// 老数据有些同一个资产ID不同，需要用编码查询（墨临-镇沅隧道（右幅））
      this.assetName = newValue.tunnelName;
    },
    getYear() {
      let currentYear = new Date().getFullYear();
      for (let i = 0; i < 5; i++) {
        this.yearsArray.push(currentYear - i);
      }
    },
    /** 查询用户列表 */
    getList() {
      this.loading = true;
      listMonthlyReport(this.queryParams).then(response => {
        this.deviceCheckList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dateRange = [];
      this.assetName = null;
      this.queryParams.assetId = null;
      this.resetForm("queryForm");
      this.handleQuery();
    },

    /** 导出按钮操作 */
    handleExport() {
      this.download('patrol/deviceFaultRecord/export', {
        ...this.queryParams
      }, `机电故障月报清单_${new Date().getTime()}.xlsx`)

    },
    // 单选选中事件
    handleCurrentChange(val) {
      this.currentRow = val;
      console.log("this.currentRow : ", this.currentRow)
    },
    /** 导出按钮操作 */
    handleExportCard() {
      console.log("this.currentRow : ", this.currentRow)
      if (!this.currentRow || this.currentRow.assetId == null) {
        this.$modal.msgError("请选择要导出的数据!");
      } else {
        this.showIframe = true
      }

    },
  }
};
</script>
<style lang="scss" scoped>
.infoBox {
  padding: 15px 15px 0 15px;
  box-sizing: border-box;
  border-radius: 6px;
  border: 1px solid #C4C4C4;
  position: relative;

  .infoTitle {
    user-select: none;
    position: absolute;
    top: 0;
    left: 0;
    padding: 0 10px;
    font-size: 14px;
    line-height: 14px;
    font-weight: bold;
    transform: translateX(15px) translateY(-50%);
    background-color: white;
  }

  .imgBox {
    height: auto;
    width: 100%;
    display: flex;
    flex-wrap: wrap;
    align-content: flex-start;

    .imgItemBox {
      height: 240px;
      width: calc(100% / 3);
      box-sizing: border-box;
      padding: 10px;
      overflow-y: auto;
      display: flex;
      align-items: center;
      justify-content: center;
      position: relative;

      .imgDeleteBtn {
        position: absolute;
        z-index: 1;
        top: 10%;
        right: 10%;
      }
    }
  }
}

.check-edit-title {
  font-weight: bold;
  font-size: 1.15rem;
  padding: 10px 0;
  /* color: #333333;*/
  /* color: #72767b;*/
  &:before {
    content: '';
    display: inline-block;
    width: 5px;
    height: 1.5rem;
    vertical-align: bottom;
    margin-right: 0.8rem;
    background: #3797EB;
  }
}

.add_details {
  width: 100%;
  border-radius: 6px;
  border: 1px solid #C4C4C4;
}

.button-container {
  display: flex;
  justify-content: center;
  align-items: center;
}

.hasTagsView .app-main[data-v-078753dd] {
  background: #f5f7fa;
}

.tableDiv {
  background-color: white;
  padding-bottom: 10px;
}

.formDialog {
  ::v-deep .el-dialog__body {
    height: 600px;
    overflow-y: auto;
  }

  .titleBox {
    height: 22px;
    position: relative;
    display: flex;
    flex-direction: row;
    align-items: center;

    .title {
      font-size: 16px;
      color: black;
      margin: 0;
    }

    .subTitle {
      margin-left: 15px;
      font-size: 12px;
      color: #888888;
    }

    .riskLevel {
      user-select: none;
      position: absolute;
      // top: 0;
      right: 5%;
      display: flex;
      align-items: center;
      flex-direction: row;

      .title {
        font-size: 16px;
        font-weight: bold;
      }

      .main {
        font-size: 16px;
        font-weight: bold;
        padding: 5px 10px 5px 10px;
        color: white;
        box-sizing: border-box;
        border-radius: 5px;
      }

    }
  }
}

</style>
