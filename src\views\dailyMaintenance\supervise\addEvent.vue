<template>
  <div class="road-interflow-edit">
    <el-row :gutter="20">
      <el-col :span="24">
        <el-form ref="queryForm" :inline="true" :model="queryParams" label-width="68px" size="small">
          <el-form-item>
            <dict-select type="route_direction" clearable
                         v-model="queryParams.direction" placeholder="上下行"
                         style="width: 100%"></dict-select>
          </el-form-item>
          <el-form-item>
            <dict-select type="lane" clearable
                         v-model="queryParams.direction" placeholder="位置"
                         style="width: 100%"></dict-select>
          </el-form-item>
          <el-form-item prop="disType">
            <el-select v-model="queryParams.disType" filterable placeholder="请选择事件类型" clearable
                       style="width: 240px;">
              <el-option v-for="item in advicesList"
                         :key="item.value"
                         :label="item.label"
                         :value="item.value">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-input v-model="queryParams.code" placeholder="施工单编号" style="width: 240px;"/>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
            <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
          </el-form-item>
        </el-form>
      </el-col>
      <el-col :span="24">
        <el-table v-adjust-table
            :data="tableData"
            border
            height="300px"
            ref="dataTable"
            v-loading="loading"
            @selection-change="handleSelectionChange"
            @row-click="handleClickRow"
            style="width: 100%">
          <el-table-column
              type="selection"
              width="55">
          </el-table-column>
          <template v-for="(column,index) in columns">
            <el-table-column v-if="column.visible" show-overflow-tooltip
                             :label="column.label"
                             :prop="column.field"
                             :width="column.width"
                             align="center">
              <template slot-scope="scope">
                <dict-tag v-if="column.dict" :options="dict.type[column.dict]" :value="scope.row[column.field]"/>
                <template v-else-if="column.slots">
                  <RenderDom :index="index" :render="column.render" :row="scope.row"/>
                </template>
                <span v-else-if="column.isTime">{{ parseTime(scope.row[column.field], '{y}-{m}-{d}') }}</span>
                <span v-else >{{ scope.row[column.field] }}</span>
              </template>
            </el-table-column>
          </template>
        </el-table>
        <pagination
            v-show="total>0"
            :total="total"
            :page.sync="queryParams.pageNum"
            :limit.sync="queryParams.pageSize"
            @pagination="handleQuery"
        />
        <div style="text-align: right">
          <el-button type="primary" @click="handleSave">保存</el-button>
        </div>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import {listAllDiseases} from "@/api/patrol/diseases";
import {findDiseaseDataList} from "@/api/dailyMaintenance/eventManage/eventRepair";
import {formatPile} from "@/utils/ruoyi";

export default {
  components: {
    RenderDom: {
      functional: true,
      props: {
        row: Object,
        index: Number,
        render: Function
      },
      render(createElement, ctx) {
        const {row, index} = ctx.props
        return ctx.props.render(row, index)
      }
    }
  },
  props: {
    maiSecId: {
      type: String,
      default: ''
    }
  },
  dicts: ['route_direction', 'lane'],
  data() {
    return {
      queryParams: {
        pageNum: 1,
        pageSize: 50
      },
      total: 0,
      loading: false,
      advicesList: [],
      tableData: [],
      selectDatas: [],
      columns: [
        {key: 0, width: 100, field: 'timeOutMsg', label: `是否超期`, visible: true},
        {key: 1, width: 100, field: 'maiSecName', label: `路段名称`, visible: true},
        {key: 2, width: 100, field: 'routeCode', label: `路线编码`, visible: true},
        {key: 3, width: 100, field: 'direction', label: `上下行`, visible: true, dict: 'route_direction'},
        {key: 4, width: 100, field: 'lane', label: `位置`, visible: true, dict: 'lane'},
        {key: 5, width: 100, field: 'beginMileShow', label: `起点桩号`, visible: true},
        {key: 6, width: 100, field: 'endMileShow', label: `终点桩号`, visible: true},
        {key: 7, width: 100, field: 'disTypeName', label: `事件类型`, visible: true},
        {key: 8, width: 100, field: 'disDesc', label: `事件描述`, visible: true},
        {key: 9, width: 100, field: 'collectTime', label: `发现日期`, visible: true},
        {key: 10, width: 100, field: 'planEndDate', label: `计划完成日期`, visible: true},
        {key: 11, width: 100, field: 'code', label: `施工单编号`, visible: true},
        {key: 12, width: 100, field: 'domainName', label: `管养单位`, visible: true},
      ]
    }
  },
  created() {
    this.getDisList()
    this.handleQuery()
  },
  methods: {
    handleQuery() {
      this.loading = true
      this.queryParams.maiSecId = this.maiSecId
      // this.queryParams.stageList = [1, 2]
      findDiseaseDataList(this.queryParams).then(res => {
        if (res.code == 200) {
          this.loading = false
          res.rows.forEach(item => {
            item.beginMileShow = formatPile(item.beginMile)
            item.endMileShow = formatPile(item.endMile)
          })
          this.tableData = res.rows
          this.total = res.total
        }
      })
    },
    resetQuery() {
      this.queryParams = {
        pageNum: 1,
        pageSize: 50
      }
    },
    handleSave() {
      for (let i = 0; i < this.selectDatas.length; i++) {
        let item = this.selectDatas[i]
        item.id = item.disId
      }
      this.$emit('select', this.selectDatas)
    },
    // 选中
    handleSelectionChange(e) {
      this.selectDatas = e
    },
    handleClickRow(e) {
      e.isSelected = !e.isSelected;
      this.$refs.dataTable.toggleRowSelection(e);
    },
    // 获取事件列表
    getDisList() {
      listAllDiseases().then(res => {
        res.data.forEach(item => {
          this.advicesList.push({
            label: item.diseaseName,
            value: item.id
          })
        })
      })
    },
  }
}
</script>

<style scoped lang="scss">

</style>
<style lang="scss" scoped>
@import "@/assets/styles/business.scss";
</style>
