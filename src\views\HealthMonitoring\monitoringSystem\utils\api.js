import { getToken } from '@/utils/auth'
// 封装 GET 请求
export const fetchGet = async (url, params = {}, type = 'application/json') => {
  const queryString = Object.keys(params)
    .map(key => `${encodeURIComponent(key)}=${encodeURIComponent(params[key])}`)
    .join('&');

  const fullUrl = queryString ? `${url}?${queryString}` : url;

  try {
    const response = await fetch(fullUrl, {
      method: 'GET',
      mode: 'cors', // 'cors', 'no-cors', or 'same-origin'
      headers: {
        'Content-Type': type,
        'Authorization': `Bearer ${getToken()}` 
      },
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    return await response.json();
  } catch (error) {
    console.error('Fetch error:', error);
    throw error;
  }
};

// 封装 POST 请求
export const fetchPost = async (url, data = {}) => {
  try {
    const response = await fetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(data)
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    return await response.json();
  } catch (error) {
    console.error('Fetch error:', error);
    throw error;
  }
};
