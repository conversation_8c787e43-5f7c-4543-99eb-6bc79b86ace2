<template>
  <div class="roadbedadd">
    <el-dialog
      :title="title"
      :visible.sync="showAddEdit"
      width="60%"
      append-to-body
      :before-close="handleClose"
      :close-on-click-modal="false"
      :class="forView ? 'forView' : ''"
    >
      <div
        v-loading="loading"
        style="height: 60vh; overflow-y: auto; padding: 0 10px 0 5px"
      >
        <el-form
          ref="form"
          :model="form"
          :rules="rules"
          label-width="150px"
          :disabled="forView ? true : false"
        >
          <div style="display: flex; flex-wrap: wrap; margin-bottom: 20px">
            <el-divider content-position="left">基础数据</el-divider>
            <ManageSelectTree placeholder="请选择" :formObject="form" />
            <!-- <el-col :span="12">
              <el-form-item
                label="管理处"
                prop="managementMaintenanceId"
              >
                <SelectTree
                  v-model="form.managementMaintenanceId"
                  :dept-type="201"
                  placeholder="请选择"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item
                label="管养分处"
                prop="managementMaintenanceBranchId"
              >
                <SelectTree
                  v-model="form.managementMaintenanceBranchId"
                  :dept-type="202"
                  placeholder="请选择"
                />
              </el-form-item>
            </el-col> -->
            <el-col :span="12">
              <el-form-item label="养护路段" prop="maintenanceSectionId">
                <el-select
                  v-model="form.maintenanceSectionId"
                  style="width: 100%"
                  placeholder="请选择"
                  clearable
                  :disabled="!form.managementMaintenanceBranchId"
                >
                  <el-option
                    v-for="item in routeOptions"
                    :key="item.maintenanceSectionId"
                    :label="item.maintenanceSectionName"
                    :value="item.maintenanceSectionId"
                  />
                </el-select>
              </el-form-item>
            </el-col>

            <el-col :span="12">
              <el-form-item label="路线编码" prop="routeCode">
                <el-select
                  v-model="form.routeCode"
                  placeholder="请选择"
                  :disabled="!form.maintenanceSectionId"
                  clearable
                  style="width: 100%"
                  @change="
                    (val) => {
                      handleSelect(val);
                    }
                  "
                >
                  <el-option
                    v-for="item in routeList"
                    :key="item.routeCode"
                    :label="item.routeCode"
                    :value="item.routeCode"
                  >
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="路线名称" prop="routeName">
                <el-select
                  v-model="form.routeName"
                  style="width: 100%"
                  placeholder="请选择"
                  :disabled="true"
                  clearable
                >
                  <el-option
                    v-for="item in []"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="调查方向" prop="direction">
                <el-select
                  v-model="form.direction"
                  placeholder="请选择"
                  clearable
                  style="width: 100%"
                >
                  <el-option
                    v-for="item in dict.type.sys_route_direction || []"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="具体方向" prop="specificDirection">
                <el-input
                  v-model="form.specificDirection"
                  style="width: 100%"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="路线技术等级" prop="routeLevel">
                <MultiDictSelect
                  v-model="form.routeLevel"
                  :disabled="''"
                  :multiple="true"
                  :options="dict.type['sys_route_grade']"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="路面类型" prop="pavementType">
                <el-select
                  v-model="form.pavementType"
                  placeholder="请选择"
                  clearable
                  style="width: 100%"
                >
                  <el-option
                    v-for="item in dict.type.sys_surface_type || []"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="年度" prop="years">
                <el-date-picker
                  v-model="form.years"
                  style="width: 100%"
                  :type="'year'"
                  :placeholder="'请选择年份'"
                  :picker-options="pickerOptions"
                  clearable
                  :value-format="'yyyy'"
                />
              </el-form-item>
            </el-col>

            <el-divider content-position="left">统一里程</el-divider>
            <el-col :span="12">
              <el-form-item label="起点桩号" prop="unifiedMileageStartStake">
                <PileInput v-model="form.unifiedMileageStartStake" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="终点桩号" prop="unifiedMileageEndStake">
                <PileInput v-model="form.unifiedMileageEndStake" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="管养里程(km)" prop="maintenanceMileage">
                <el-input-number
                  v-model="form.maintenanceMileage"
                  :min="0"
                  style="width: 100%"
                />
              </el-form-item>
            </el-col>
            <el-divider content-position="left">施工里程</el-divider>
            <el-col :span="12">
              <el-form-item label="起点桩号" prop="constructionStartStake">
                <PileInput v-model="form.constructionStartStake" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="终点桩号" prop="constructionEndStake">
                <PileInput v-model="form.constructionEndStake" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="施工里程(km)" prop="constructionMileage">
                <el-input-number
                  v-model="form.constructionMileage"
                  :min="0"
                  style="width: 100%"
                />
              </el-form-item>
            </el-col>
            <el-divider content-position="left">其他</el-divider>
            <el-col :span="12">
              <el-form-item label="移交管理单位" prop="transferManagementUnit">
                <el-input
                  v-model="form.transferManagementUnit"
                  style="width: 100%"
                  controls-position="right"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="备注" prop="remark">
                <el-input
                  type="textarea"
                  v-model="form.remark"
                  placeholder="请输入备注"
                  clearable
                  autosize
                />
              </el-form-item>
            </el-col>
          </div>
        </el-form>
      </div>
      <div slot="footer">
        <el-button
          v-if="!forView"
          type="primary"
          :loading="loading"
          @click="handleSubmit('submit')"
          >提 交</el-button
        >
        <el-button
          v-if="!forView && (!formData.id || formData.status == 1)"
           v-hasPermi="['baseData:longDownhill:tempAdd']"
          type="primary"
          :loading="loading"
          @click="handleSubmit('save')"
        >暂 存</el-button>
        <el-button @click="handleClose">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import PileInput from "@/components/PileInput/index.vue";
import SelectTree from "@/components/DeptTmpl/selectTree";
import SectionSelect from "@/components/SectionSelect";
import ManageSelectTree from "@/components/manageSelectTree/index.vue";
import { listMaintenanceSectionAll } from "@/api/system/maintenanceSection";
import { listByMaintenanceSectionId } from "@/api/baseData/common/routeLine";
import MultiDictSelect from "@/views/baseData/components/MultiDictSelect/index.vue";

import {
  addIceCovered,
  tempIceCovered,
  updateIceCovered,
} from "@/api/baseData/road/longDownhill/index.js";

export default {
  name: "longDownhill-form",
  components: {
    PileInput,
    SelectTree,
    SectionSelect,
    ManageSelectTree,
    MultiDictSelect,
  },
  props: {
    formData: {
      default: {},
    },
    showAddEdit: {
      default: false,
    },
    title: {
      default: "添加结冰数据",
    },
    forView: {
      default: false,
    },
  },
  dicts: ["sys_route_direction", "sys_route_grade", "sys_surface_type"],
  data() {
    return {
      loading: false,
      form: {
        managementMaintenanceId: "",
        managementMaintenanceBranchId: "",
      },
      pickerOptions: {
        disabledDate(v) {
          return v.getTime() > new Date().getTime() 
        }
      },
      rules: {
        managementMaintenanceId: [
          { required: true, message: "请选择管理处", trigger: "change" },
        ],
        managementMaintenanceBranchId: [
          { required: true, message: "请选择管养分处", trigger: "change" },
        ],
        maintenanceSectionId: [
          { required: true, message: "请选择养护路段", trigger: "change" },
        ],
        routeCode: [
          { required: true, message: "请选择路线编码", trigger: "change" },
        ],
        routeName: [
          { required: true, message: "请输入路线名称", trigger: "blur" },
        ],
        years: [
          { required: true, message: "请选择年度", trigger: "change" },
        ],
        direction: [
          { required: true, message: "请选择调查方向", trigger: "change" },
        ],
        specificDirection: [
          { required: true, message: "请输入具体方向", trigger: "blur" },
        ],
        routeLevel: [
          { required: true, message: "请选择路线技术等级", trigger: "change" },
        ],
        pavementType: [
          { required: true, message: "请选择路面类型", trigger: "change" },
        ],
        unifiedMileageStartStake: [
          { required: true, message: "请输入起点桩号", trigger: "blur" },
        ],
        unifiedMileageEndStake: [
          { required: true, message: "请输入终点桩号", trigger: "blur" },
        ],
        maintenanceMileage: [
          { required: true, message: "请输入管养里程", trigger: "blur" },
        ],
        constructionStartStake: [
          { required: true, message: "请输入起点桩号", trigger: "blur" },
        ],
        constructionEndStake: [
          { required: true, message: "请输入终点桩号", trigger: "blur" },
        ],
        constructionMileage: [
          { required: true, message: "请输入施工里程", trigger: "blur" },
        ],
      },
      routeOptions: [],
      routeList: [],
    };
  },
  created() {
    this.init();
  },
  mounted() {},
  methods: {
    init() {
      if (this.formData.id) {
        this.form = JSON.parse(JSON.stringify(this.formData));
      }
    },
    handleSubmit(type) {
      let pass = true;
      switch (type) {
        case "submit":
          this.form.status = 2;
          break;
        case "save":
          this.form.status = 1;
          break;
      }
      if (this.form.status == 2) {
        this.$refs.form.validate((valid) => {
          if (valid) {
            pass = true;
          } else {
            pass = false;
            return false;
          }
        });
      }
      if (!pass) return;
      // 提交需要顺便提交routeId和routeName
      let arr = this.routeList.filter((el) => {
        return el.routeCode === this.form.routeCode;
      });
      this.form.routeId = arr[0]?.routeId;
      this.form.routeName = arr[0]?.routeName;
      this.loading = true;

      if (this.form.routeLevel && Array.isArray(this.form.routeLevel)) {
        this.form.routeLevel = this.form.routeLevel.join(",");
      }
      if (this.form.id != null) {

        const api = this.form.status === 1 ? tempIceCovered : updateIceCovered;

        api(this.form)
          .then((response) => {
            this.$modal.msgSuccess("修改成功");
            this.$emit("refresh");
          })
          .catch(() => {
            this.loading = false;
          });
      } else {
        const api = this.form.status === 1 ? tempIceCovered : addIceCovered;
        api(this.form)
          .then((response) => {
            this.$modal.msgSuccess(
              this.form.status === 1 ? "暂存成功" : "新增成功"
            );
            this.$emit("refresh");
          })
          .catch(() => {
            this.loading = false;
          });
      }
    },
    deptChange(e) {
      if (!e) return;
      listMaintenanceSectionAll({ departmentId: e }).then((res) => {
        if (res.code == 200) {
          this.routeOptions = res.data;
        }
      });
    },
    maintenanceSectionChange(e) {
      if (!e) return;

      const option = this.routeOptions.find(
        (i) => i.maintenanceSectionId === e
      );
      if (option && option.routeGrade) {
        this.form["routeLevel"] = option.routeGrade.split(",");
      }
      listByMaintenanceSectionId({ maintenanceSectionId: e }).then((res) => {
        if (res.code == 200) {
          this.routeList = res.data || [];
          // this.form.routeCode = ''
          this.$forceUpdate();
        }
      });
    },
    handleSelect(e) {
      const option = this.routeList.find((i) => i.routeCode === e);
      if (option) {
        this.form.routeId = option.routeId;
        this.form.routeName = option.routeName;
      }
    },
    handleClose() {
      if (this.forView) {
        this.form = {};
        this.$emit("close", false);
      } else {
        this.$modal
          .confirm("确认退出？")
          .then(() => {
            this.form = {};
            this.$emit("close", false);
          })
          .catch(() => {});
      }
    },
  },
  watch: {
    "form.managementMaintenanceBranchId"(newVal, oldVal) {
      if (newVal) {
        this.deptChange(newVal)
      }
      if (oldVal) {
        if(this.form.maintenanceSectionId)this.form.maintenanceSectionId=''
        if(this.form.routeCode)this.form.routeCode=''
      }
    },
    "form.maintenanceSectionId"(newVal, oldVal) {
      if (newVal) {
        this.maintenanceSectionChange(newVal);
      }
      if (oldVal && this.form.routeCode) {
        this.form.routeCode = "";
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.forView ::v-deep .el-input.is-disabled .el-input__inner,
.forView ::v-deep .el-textarea.is-disabled .el-textarea__inner {
  background-color: white;
  border-color: #dfe4ed;
  color: black;
}
::v-deep .el-dialog__header {
  border-bottom: 1px #dfe4ed solid;
  padding: 20px 30px !important;
}
::v-deep .el-divider--horizontal {
  margin: 20px 0 !important;
}
.longDownhill-protection-card {
  width: 100%;
  border: #dfe4ed 1px solid;
  border-radius: 10px;
  padding: 20px 40px 0 0;
  position: relative;
  margin-bottom: 10px;
  i {
    cursor: pointer;
    position: absolute;
    right: 5px;
    top: 45%;
    color: #f56c6c;
  }
}
::v-deep .inputNumber {
  .el-input-number__decrease,
  .el-input-number__increase {
    display: block;
    height: 30px;
    margin-top: 1px;
    display: flex;
    justify-content: center;
    align-items: center;
  }
}
</style>
<style lang="scss">
.longitude-latitude {
  .el-input-group__prepend {
    padding: 0 10px;
  }
  .el-input__inner {
    padding: 0 5px;
  }
}
</style>
