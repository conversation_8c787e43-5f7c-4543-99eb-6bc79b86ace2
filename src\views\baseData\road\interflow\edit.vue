<template>
  <div>
    <el-dialog
      :title="title"
      :visible.sync="showAddEdit"
      width="60%"
      append-to-body
      :before-close="onClose"
      :close-on-click-modal="false"
      :class="forView ? 'forView' : ''"
    >
      <div
        v-loading="loading"
        class="road-interflow-edit"
        style="height: 65vh; overflow-y: auto; padding: 0 10px 0 5px"
      >
        <el-form
          ref="ruleFormEl"
          :model="ruleForm"
          :rules="rules"
          label-width="auto"
          :disabled="forView"
        >
          <MepuColumn
            :is-manger-unit="true"
            :fields="baseInfoFields"
            :from="ruleForm"
          />
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="匝道编码" prop="flowRampList">
                <!-- <el-input
                  :value="`选择${ruleForm.flowRampList.length}个匝道编码`"
                  :style="{ pointerEvents: forView ? 'none' : '' }"
                  readonly
                  placeholder="请选择匝道编码"
                  @focus="onSelectRamp"
                /> -->
                <el-select ref="rampSelect" v-model="ruleForm.rampValue" style="width: 100%;" multiple @focus="onSelectRamp" @remove-tag="removeTag">
                  <el-option
                    v-for="i in ruleForm.list"
                    :key="i.id"
                    :label="i.label"
                    :value="i.id"
                  />
                </el-select>
              </el-form-item>
            </el-col>

            <el-col :span="12">
              <el-form-item label="经纬度" prop="经纬度">
                <lon-lat
                  type="lonlat"
                  :lon.sync="ruleForm.longitude"
                  :lat.sync="ruleForm.latitude"
                />
              </el-form-item>
            </el-col>
          </el-row>
          <MepuColumn :fields="baseInfo1Fields" :from="ruleForm" />
        </el-form>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button
          v-if="!forView"
          :loading="loading"
          type="primary"
          @click="handleSubmit('submit')"
          >提 交
        </el-button>
        <el-button
          v-if="!forView && (!formData.id || formData.status == 1)"
          :loading="loading"
          type="primary"
          @click="handleSubmit('save')"
          >暂 存
        </el-button>
        <el-button @click="onClose">取 消</el-button>
      </div>
    </el-dialog>
    <Dialog title="选择匝道" :show.sync="showRamp">
      <Gateway
        v-if="showRamp"
        :params="ruleForm"
        :list="ruleForm.flowRampList"
        @change="
          (list) => {
            ruleForm.flowRampList = list;
            ruleForm.list = list.map(el => { return {id: el.routeSegmentsId, label: el.routeSegmentsName} })
            ruleForm.rampValue = list.map(el => { return el.routeSegmentsId })
            showRamp = false;
            $forceUpdate();
          }
        "
      />
    </Dialog>
  </div>
</template>

<script>
import { createIdWorker } from "@/api/baseData/common";
import {
  interflowAdd,
  interflowEdit,
  interflowTemp,
} from "@/api/baseData/road/interflow/index.js";
import PileInput from "@/components/PileInput/index.vue";
import SelectTree from "@/components/DeptTmpl/selectTree";
import RouteRoad from "@/components/RouteRoad";
import RouteRoadSub from "@/components/RouteRoadSub";
import Dialog from "@/components/Dialog/index.vue";
import Gateway from "./gateway.vue";
import "@riophae/vue-treeselect/dist/vue-treeselect.css";
import Treeselect from "@riophae/vue-treeselect";
import { deptTreeSelect } from "@/api/tmpl";
import RoadSection from "@/views/baseData/components/roadSection";
import RouteCoding from "@/views/baseData/components/routeCoding";
import ManageSelectTree from "@/components/manageSelectTree/index.vue";
import formFields from "./js/formFieds";
import MepuColumn from "@/views/baseData/components/MepuColumn/index.vue";
import lonLat from "@/components/mapPosition/lonLat.vue";

export default {
  name: "RoadInterflowEdit",
  components: {
    lonLat,
    MepuColumn,
    ManageSelectTree,
    PileInput,
    SelectTree,
    RouteRoad,
    RouteRoadSub,
    Dialog,
    Gateway,
    Treeselect,
    RoadSection,
    RouteCoding,
  },
  dicts: ["left_right", "lane", "patrol_inspection_direction"],
  props: {
    formData: {
      type: undefined,
      default: () => {},
    },
    showAddEdit: {
      type: Boolean,
      default: false,
    },
    title: {
      type: String,
      default: "添加互通",
    },
    forView: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      loading: false,

      ruleForm: {
        flowRampList: [],
        managementMaintenanceId: "",
        maintenanceSectionId: "",
        managementMaintenanceBranchId: "",
      },
      rules: {
        // flowRampList: [
        //   {
        //     type: 'array',
        //     required: true,
        //     message: '匝道编码不能为空',
        //     trigger: ['blur', 'change']
        //   }
        // ]
      },
      ownerId: "", // 图片id
      showRamp: false, // 选择匝道
      formH: 500, // form最大高度
      deptOptions: [], // 部门树数据
      ownerOptions: [], //
      types: 201, // 编码规划
      branchDeptOptions: [], // 管养分出数据
      baseInfoFields: [],
      baseInfo1Fields: [],
    };
  },
  computed: {},
  watch: {},
  created() {
    this.init();
    // this.getDeptTree()
  },
  mounted() {
    this.formH = window.innerHeight - 240;
    window.addEventListener("resize", (e) => {
      let evt = e.target || window;
      this.formH = evt.innerHeight - 240;
    });
  },
  methods: {
    init() {
      this.baseInfoFields = JSON.parse(JSON.stringify(formFields.baseInfo));
      this.baseInfo1Fields = JSON.parse(JSON.stringify(formFields.baseInfo1));
      this.getCreateId();
      let data = JSON.parse(JSON.stringify(this.formData));
      if (!data.flowRampList) {
        data.flowRampList = [];
      }
      this.ruleForm = data;
    },
    getCreateId() {
      createIdWorker().then((res) => {
        if (res.code === 200) {
          this.ownerId = res.data;
        }
      });
    },
    onSelectRamp() {
      if (!this.ruleForm.managementMaintenanceBranchId) {
        this.$message.warning("请先选择管养分处");
        return;
      } else if (!this.ruleForm.maintenanceSectionId) {
        this.$message.warning("请输入养护路段");
        return;
      } else if (!this.ruleForm.routeCode) {
        this.$message.warning("请输入路线编码");
        return;
      }
      this.showRamp = true;
      this.$refs.rampSelect.blur();
    },
    removeTag(routeSegmentsId) {
      this.ruleForm.flowRampList = this.ruleForm.flowRampList.filter(el => el.routeSegmentsId !== routeSegmentsId)
    },
    handleSubmit(type) {
      this.loading = true;
      switch (type) {
        case "submit":
          this.ruleForm.status = 2;
          break;
        case "save":
          this.ruleForm.status = 1;
          break;
      }
      if ((this.ruleForm.status ==2)) {
        this.$refs["ruleFormEl"].validate((valid) => {
          if (!valid) return;
        });
      }

      if (Array.isArray(this.ruleForm.samplePictureId)) {
        this.ruleForm.samplePictureId = this.ruleForm.samplePictureId.join(",");
      }

      this.ruleForm.startStakeImport=this.ruleForm.startStake;

      if (this.ruleForm.id != null) {
        const api = this.ruleForm.status === 1 ? interflowTemp : interflowEdit;

        api(this.ruleForm)
          .then((response) => {
            this.$modal.msgSuccess("修改成功");
            this.$emit("close", true);
          })
          .catch(() => {
            this.loading = false;
          });
      } else {
        const api = this.ruleForm.status === 1 ? interflowTemp : interflowAdd;
        api(this.ruleForm)
          .then((response) => {
            this.$modal.msgSuccess(
              this.ruleForm.status === 1 ? "暂存成功" : "新增成功"
            );
            this.$emit("close", true);
          })
          .catch(() => {
            this.loading = false;
          });
      }
    },
    onClose() {
      if (this.forView) {
        this.form = {};
        this.$emit("close", false);
      } else {
        this.$modal
          .confirm("确认退出？")
          .then(() => {
            this.form = {};
            this.$emit("close", false);
          })
          .catch(() => {});
      }
    },
    /** 查询部门下拉树结构 */
    getDeptTree() {
      deptTreeSelect({ types: this.types }).then((response) => {
        this.deptOptions = response.data;
      });
      deptTreeSelect({ types: 100 }).then((response) => {
        this.ownerOptions = response.data;
      });
      deptTreeSelect({ types: 202 }).then((response) => {
        this.branchDeptOptions = response.data;
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.road-interflow-edit {
  /* max-height: 75vh;
    padding: 10px;*/
  overflow-y: auto;
  overflow-x: hidden;
}

.forView ::v-deep .el-input.is-disabled .el-input__inner {
  background-color: white;
  border-color: #dfe4ed;
  color: black;
}
</style>
