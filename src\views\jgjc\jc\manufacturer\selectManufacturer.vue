<template>
  <el-dialog title="选择厂家" :visible.sync="visible" width="800px" top="5vh" append-to-body>
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true">
      <el-form-item label="名称" prop="name">
        <el-input
          v-model="queryParams.name"
          placeholder="请输入名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>
    <el-row>
      <el-table ref="table" :data="list" height="260px" stripe border highlight-current-row @current-change="handleCurrentChange" >
        <el-table-column label="名称" align="left" header-align="center" prop="name" width="250" :show-overflow-tooltip="true"/>
        <el-table-column label="联系人姓名" align="left" header-align="center" prop="contactName" width="100" :show-overflow-tooltip="true"/>
        <el-table-column label="说明" align="left" header-align="center" prop="description" :show-overflow-tooltip="true"/>
      </el-table>
      <pagination
        v-show="total>0"
        :total="total"
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
        @pagination="getList"
      />
    </el-row>
    <div slot="footer" class="dialog-footer">
      <el-button type="primary" @click="handleSelect">确 定</el-button>
      <el-button @click="visible = false">取 消</el-button>
    </div>
  </el-dialog>
</template>
<script>
import { listManufacturer } from "@/api/jgjc/jc/manufacturer";

export default {
  data() {
    return {
      // 遮罩层
      visible: false,
      selectData:null,
      // 总条数
      total: 0,
      list: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        name: null,
        orderDirection: 'ASC'
      },
      //提交参数
      params: {}
    };
  },
  methods: {
    // 显示弹框
    show() {
      this.getList();
      this.visible = true;
    },
    handleCurrentChange(row) {
      this.selectData = row
    },
    // 查询表数据
    getList() {
      listManufacturer(this.queryParams).then(res => {
        this.list = res.data;
        this.total = res.total;
      });
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    /** 提交选择隧道操作 */
    handleSelect() {
      if(this.selectData!=null){
        this.$emit('select',this.selectData)
        this.visible = false;
        return
      }
      this.$modal.msgWarning("请选择一条厂家记录");
    }
  }
};
</script>
