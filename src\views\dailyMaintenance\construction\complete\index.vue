<template>
  <div class="app-container maindiv">
    <registration-list ref="regis" :page-list="pageList" :dis-stage="2">
      <template #btn>
        <el-col :span="1.5">
          <el-button
              icon="el-icon-download"
              size="mini"
              v-has-menu-permi="['finished1:construction:export']"
              type="warning"
              @click="exportList"
          >导出清单
          </el-button
          >
        </el-col>
      </template>
      <template #operate="scope" >
        <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleRegis(scope.scope.row)"
        >完工登记
        </el-button>
        <el-button
            size="mini"
            type="text"
            v-has-menu-permi="['finished1:construction:reject']"
            icon="el-icon-delete"
            @click="handleNotYetDispose(scope.scope.row)"
        >撤回待施工
        </el-button>
      </template>
    </registration-list>
    <el-drawer append-to-body modal-append-to-body :wrapperClosable="false" :title="drawerTitle" destroy-on-close :visible.sync="drawer" size="70%">
      <detail @close="handleCloseDetail" :row-data="rowData"></detail>
    </el-drawer>
  </div>
</template>
<script>
import registrationList from '../component/registrationList.vue'
import {pageList, rejectToRegister} from "@/api/dailyMaintenance/construction/complete";
import Detail from "./detail.vue";
export default {
  name: "Complete",
  components: {Detail, registrationList},
  data() {
    return {
      drawerTitle: '完工登记',
      drawer: false,
      rowData: {}
    }
  },
  methods: {
    pageList,
    handleRegis(rows) {
      this.rowData = rows
      this.drawer = true
    },
    // 暂不处理
    handleNotYetDispose(rows) {
      this.$modal.confirm('是否确认撤回').then(() => {
        rejectToRegister(rows).then(res => {
          this.$message.success('撤回成功')
          this.$refs.regis.handleQuery()
        })
      })
    },
    handleCloseDetail() {
      this.rowData = {}
      this.drawer = false
      this.$refs.regis.handleQuery()
    },
    // 导出清单按钮
    exportList() {
      this.$refs.regis.queryParams.year = this.$refs.regis.queryParams.yearStr ? parseInt(this.$refs.regis.queryParams.yearStr) : null

      this.download(
          'manager/finished1/export',
          {...this.$refs.regis.queryParams},
          `finished_${new Date().getTime()}.xlsx`,
          {
            headers: {'Content-Type': 'application/json;'},
            parameterType: 'body'
          }
      )
    },
  }
}
</script>
