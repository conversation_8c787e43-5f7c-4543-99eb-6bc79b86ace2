<template>
  <div class="app-container maindiv">
    <el-row :gutter="20">
      <!--角色数据-->
      <el-col :span="24" :xs="24">
        <el-row>
          <el-col :span="24" :xs="24">
            <el-row>
              <el-form
                ref="queryForm"
                :model="queryParams"
                size="mini"
                :inline="true"
                label-width="68px"
              >
                <el-form-item>
                  <el-input style="width: 240px" placeholder="调差项目" v-model="queryParams.name"></el-input>
                </el-form-item>
                <el-form-item>
                  <el-input style="width: 240px" placeholder="代号" v-model="queryParams.code"></el-input>
                </el-form-item>
                <el-form-item label="" prop="year">
                  <el-date-picker
                    v-model="queryParams.year"
                    format="yyyy"
                    placeholder="年份"
                    style="width: 240px"
                    type="year"
                    value-format="yyyy">
                  </el-date-picker>
                </el-form-item>
                <el-form-item>
                  <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
                  <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
                </el-form-item>
              </el-form>
              <!--默认折叠-->
            </el-row>
          </el-col>
        </el-row>
        <!--操作按钮区开始-->
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button
              type="primary"
              icon="el-icon-plus"
              size="mini"
              v-has-menu-permi="['calcdaliy:adjust:add']"
              @click="handleAdd"
            >新增
            </el-button
            >
          </el-col>
          <right-toolbar
            :showSearch.sync="showSearch"
            @queryTable="handleQuery"
            :columns="columns"
          ></right-toolbar>
        </el-row>
        <el-row>
          <div class="draggable">
            <el-table v-adjust-table
              size="mini"
              style="width: 100%"
              v-loading="loading"
              border
              :data="tableData"
              row-key="id"
              ref="dataTable"
              stripe
              highlight-current-row
              :height="
                showSearch ? 'calc(100vh - 330px)' : 'calc(100vh - 270px)'
              "
            >
              <el-table-column type="selection" width="50" align="center"/>
              <el-table-column
                label="序号"
                align="center"
                type="index"
                width="50"
              />
              <template v-for="(column,index) in columns">
                <el-table-column :label="column.label"
                                 v-if="column.visible"
                                 align="center"
                                 :prop="column.field"
                                 :width="column.width">
                  <template slot-scope="scope">
                    <dict-tag v-if="column.dict" :options="dict.type[column.dict]" :value="scope.row[column.field]"/>
                    <template v-else-if="column.slots">
                      <RenderDom :row="scope.row" :index="index" :render="column.render"/>
                    </template>
                    <span v-else-if="column.isTime">{{ parseTime(scope.row[column.field], '{y}-{m}-{d}') }}</span>
                    <span v-else>{{ scope.row[column.field] }}</span>
                  </template>
                </el-table-column>
              </template>
              <template  v-for="month in months">
                <el-table-column :label="month + '月'" align="center" width="200">
                  <template slot-scope="scope">
                    <div class="table-box" v-if="getDetailColums(scope.row.adjustDetailList, month, 'changeValue') == 0">
                      <div class="top">不调整</div>
                      <div class="bottom left">{{ getDetailColums(scope.row.adjustDetailList, month, 'price') }}</div>
                      <div class="bottom">{{ getDetailColums(scope.row.adjustDetailList, month, 'changeRange') }}</div>
                    </div>
                    <div class="table-box" v-else>
                      <div class="top" style="color: red">{{ getDetailColums(scope.row.adjustDetailList, month, 'changeValue') }}</div>
                      <div class="bottom left">{{ getDetailColums(scope.row.adjustDetailList, month, 'price') }}</div>
                      <div class="bottom" style="color: red">{{ getDetailColums(scope.row.adjustDetailList, month, 'changeRange') }}</div>
                    </div>
                  </template>
                </el-table-column>
              </template>
              <el-table-column
                label="操作"
                fixed="right"
                align="center"
                width="250"
                class-name="small-padding fixed-width"
              >
                <template slot-scope="scope">
                  <el-button
                    size="mini"
                    type="text"
                    v-has-menu-permi="['calcdaliy:adjust:edit']"
                    @click="handleEdit(scope.row)"
                    icon="el-icon-edit"
                  >编辑
                  </el-button>
                  <el-button
                    size="mini"
                    type="text"
                    v-has-menu-permi="['calcdaliy:adjust:remove']"
                    @click="handleDelete(scope.row)"
                    icon="el-icon-delete"
                  >删除
                  </el-button>
                </template>
              </el-table-column>
            </el-table>
            <pagination
              v-show="total>0"
              :total="total"
              :page.sync="queryParams.pageNum"
              :limit.sync="queryParams.pageSize"
              @pagination="handleQuery"
            />
          </div>
        </el-row>
      </el-col>
    </el-row>
    <el-dialog title="编辑调差项目" v-if="dialogVisible" :visible.sync="dialogVisible" width="80%" destroy-on-close>
      <detail @close="handleCloseDetail" :row-data="rowData"></detail>
    </el-dialog>
  </div>
</template>

<script>
import EventInfo from "../../component/eventTreeInfo.vue";
import selectTree from "@/components/DeptTmpl/selectTree.vue";
import Data from "@/views/tmplPage.vue";
import Detail from "./detail.vue";
import {queryMaterialAdjustList, deleteMaterialAdjust} from "@/api/dailyMaintenance/metering/materialAdjustment"
export default {
  name: 'MaterialAdjustment',
  components: {
    Detail,
    Data,
    selectTree,
    EventInfo,
    RenderDom: {
      functional: true,
      props: {
        row: Object,
        index: Number,
        render: Function
      },
      render(createElement, ctx) {
        const {row, index} = ctx.props
        return ctx.props.render(row, index)
      }
    }
  },
  props: [],
  data() {
    return {
      showSearch: false,
      queryParams: {
        pageNum: 1,
        pageSize: 50
      },
      total: 0,
      loading: false,
      dialogVisible: false,
      detailTitle: '编辑调差项目',
      columns: [
        {key: 0, width: 100, field: 'year', label: `年份`, visible: true},
        {key: 1, width: 100, field: 'name', label: `调差项目`, visible: true},
        {key: 2, width: 100, field: 'code', label: `代号`, visible: true},
        {key: 3, width: 100, field: 'unit', label: `单位`, visible: true},
        {key: 4, width: 100, field: 'basePrice', label: `基础单价`, visible: true}
      ],
      tableData: [],
      rowData: {},
      months: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12]
    }
  },
  computed: {},
  watch: {},
  created() {
  },
  mounted() {this.handleQuery()},
  methods: {
    handleQuery() {
      this.loading = true
      queryMaterialAdjustList(this.queryParams).then(res => {
        this.tableData = res.rows
        this.total = res.total
        this.loading = false
      })
    },
    resetQuery() {
      this.queryParams = {
        pageNum: 1,
        pageSize: 50
      }
      this.handleQuery()
    },
    handleCloseDetail() {
      this.rowData = {}
      this.dialogVisible = false
      this.handleQuery()
    },
    handleAdd() {
      this.detailTitle = '新增调差项目'
      this.rowData = {}
      this.dialogVisible = true
    },
    handleEdit(row) {
      this.detailTitle = '编辑调差项目'
      this.rowData = row
      this.dialogVisible = true
    },
    getDetailColums(adjustDetailList, month, column) {
      const detail = adjustDetailList.find(item => item.month == month)
      if (detail) {
        switch (column){
          case 'changeRange':
            return (detail[column] * 100).toFixed(2) + '%'
            break
          case 'changeValue':
            return detail[column]
            break
          default:
            return detail[column]
            break
        }
      }
      return ''
    },
    handleDelete(row) {
      this.$modal.confirm('是否确认删除').then(() => {
        this.loading = true
        deleteMaterialAdjust(row.id).then(() => {
          this.$modal.msgSuccess('删除成功')
          this.handleQuery()
        })
      })
    },
    // 导出清单按钮
    exportList() {
      this.download(
        'manager/adjust/export',
        {...this.queryParams},
        `materialAdjustment_${new Date().getTime()}.xlsx`,
        {
          headers: { 'Content-Type': 'application/json;' },
          parameterType: 'body'
        }
      )
    },
  }
}
</script>

<style lang="scss" scoped>
.leftDiv {
  border-right: 1px solid #d8dce5;
  min-height: calc(100vh - 100px);
  overflow-y: auto;
  height: calc(80vh - 150px);
  position: relative;
  top: -20px;
  padding-top: 10px;
  background-color: white;
}

.leftIcon {
  border: 1px solid #dcdfe6;
  border-radius: 8px;
  width: 16px;
  height: 50px;
  line-height: 50px;
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
  position: absolute;
  right: 0;
  top: 300px;
  z-index: 2;
}

.leftIcon:hover {
  background-color: #dcdfe6;
}

.rightIcon {
  border: 1px solid #dcdfe6;
  border-radius: 8px;
  width: 16px;
  height: 50px;
  line-height: 50px;
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
  position: absolute;
  left: -10px;
  top: 280px;
  z-index: 10;
  background: white;
}

.rightIcon:hover {
  background-color: #dcdfe6;
}

.left-total {
  font-size: 13px;
  line-height: 28px;
  color: #606266;
  position: absolute;
  bottom: 10px;
  right: 10px;
}
::v-deep .cell {
  padding: 0;
}
.table-box {
  width: 100%;
  height: 65px;
  display: flex;
  flex-wrap: wrap;
  text-align: center;
  line-height: 32.5px;
  .top {
    width: 100%;
    height: 50%;
    color: #3eacef;
    border-bottom: 1px solid rgb(223, 230, 236);
  }
  .left {
    border-right: 1px solid rgb(223, 230, 236);
  }
  .bottom {
    height: 50%;
    width: calc(50% - 0.5px);
  }
}
</style>
<style lang="scss" scoped>
@import "@/assets/styles/business.scss";
</style>
