<template>
  <div class="inspection-index">
    <div class="list" v-for="(item, index) in list" :key="index">
      <img class="img" :src="item.icon" :alt="item.name" />
      <span class="name">{{ item.name }}</span>
      <div class="list-n">
        <span>{{ item.number }} <small>{{ item.unit }}</small></span>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      list: [
        {
          icon: require('@/assets/cockpit/way.png'),
          name: '道路日常检查',
          number: 8623,
          unit: '公里',
        },
        {
          icon: require('@/assets/cockpit/bridge-patrol.png'),
          name: '桥梁经常检查',
          number: 13980,
          unit: '座',
        },
        {
          icon: require('@/assets/cockpit/tunnel-i.png'),
          name: '隧道经常检查',
          number: 1379,
          unit: '座',
        },
        {
          icon: require('@/assets/cockpit/culvert.png'),
          name: '涵洞经常检查',
          number: 1379,
          unit: '个',
        },
      ],
    }
  },
  mounted() {
  }
}
</script>

<style lang="scss" scoped>
@import "@/assets/styles/utils.scss";

.inspection-index {
  padding: vwpx(16px);
  height: 100%;
  overflow-y: auto;

  .list {
    display: flex;
    align-items: center;
    height: vwpx(75px);
    background: rgba(23, 116, 255, 0.1);
    padding: 0 vwpx(15px);
    margin: vwpx(10px) 0;
    border-radius: vwpx(5px);

    .img {
      width: vwpx(52px);
      height: vwpx(52px);
    }

    .name {
      font-family: Source Han Sans, Source Han Sans;
      font-weight: 400;
      font-size: vwpx(28px);
      color: #FFFFFF;
      text-align: center;
      font-style: normal;
      text-transform: none;
      margin-left: vwpx(20px);
    }

    .list-n {
      margin-left: auto;

      span:first-child {
        font-family: Microsoft YaHei UI, Microsoft YaHei UI;
        font-weight: 700;
        font-size: vwpx(36px);
        color: #42ABFF;
        text-align: right;
        font-style: normal;
        text-transform: none;

        small {
          font-family: Microsoft YaHei UI, Source Han Sans;
          font-weight: 400;
          font-size: vwpx(24px);
          color: rgba(255, 255, 255, 0.8);
          text-shadow: 0px 0px 10px rgba(27, 126, 242, 0.8);
          text-align: right;
          font-style: normal;
          text-transform: none;
        }
      }
    }
  }
}
</style>