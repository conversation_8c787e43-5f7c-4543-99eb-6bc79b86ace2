<template>
  <div class="component-upload-image">
    <el-upload
      ref="imageUpload"
      multiple
      :action="uploadImgUrl"
      list-type="picture-card"
      :on-success="handleUploadSuccess"
      :before-upload="handleBeforeUpload"
      :limit="limit"
      :on-error="handleUploadError"
      :on-exceed="handleExceed"
      :before-remove="beforeRemove"
      :on-remove="handleRemove"
      :show-file-list="true"
      :headers="headers"
      :file-list="fileList"
      :on-preview="handlePictureCardPreview"
      :disabled="disabled"
      :class="{ hide: this.fileList.length >= this.limit }"
    >
      <i class="el-icon-plus" />
    </el-upload>

    <!-- 上传提示 -->
    <div v-if="showTip" slot="tip" class="el-upload__tip">
      请上传
      <template v-if="fileSize"> 大小不超过 <b style="color: #f56c6c">{{ fileSize }}MB</b> </template>
      <template v-if="fileType"> 格式为 <b style="color: #f56c6c">{{ fileType.join("/") }}</b> </template>
      的文件
    </div>
    <div style="color: red;font-size: 12px;height: 35px;line-height: normal;">严禁在本互联网非涉密平台处理、传输国家秘密，请确认扫描、传输的文件资料不涉及国家秘密</div>
    <el-dialog :visible.sync="dialogVisible" title="预览" width="800" append-to-body>
      <img :src="dialogImageUrl" style="display: block; max-width: 100%; margin: 0 auto">
    </el-dialog>
  </div>
</template>

<script>
import { findFiles } from '@/api/file/index.js'
import {
  removeFile
} from '@/api/system/fileUpload.js'
import { getToken } from '@/utils/auth'
export default {
  props: {
    value: { // 绑定值传ownerId，可以是id数组
      type: undefined,
      default: () => []
    },
    // 图片数量限制
    limit: {
      type: Number,
      default: 5
    },
    // 大小限制(MB)
    fileSize: {
      type: Number,
      default: 5
    },
    // 文件类型, 例如['png', 'jpg', 'jpeg']
    fileType: {
      type: Array,
      default: () => ['png', 'jpg', 'jpeg']
    },
    // 是否显示提示
    isShowTip: {
      type: Boolean,
      default: true
    },
    ownerId: { // 文件唯一标识id
      type: [Number,String],
      default: +new Date()
    },
    storagePath: { // 存储路径用于分类
      type: String,
      default: ''
    },
    disabled:{
      default:false
    }
  },
  data() {
    return {
      number: 0,
      uploadList: [],
      dialogImageUrl: '',
      dialogVisible: false,
      hideUpload: false,
      uploadImgUrl: process.env.VUE_APP_BASE_API + `/file/upload?platform=ylzx&ownerId=${this.ownerId}&storagePath=${this.storagePath}`, // 上传的图片服务器地址
      headers: {
        Authorization: 'Bearer ' + getToken()
      },
      fileList: [],
      isUploadRejected: false // 是否跳过确认弹窗
    }
  },
  computed: {
    // 是否显示提示
    showTip() {
      return this.isShowTip && (this.fileType || this.fileSize)
    }
  },
  watch: {
    value: {
      handler(val) {
        if (val) {
          // 首先将值转为数组
          const list = Array.from(new Set(Array.isArray(val) ? val : this.value.split(',')))
          // 然后将数组转为对象数组
          const tempArr = []
          list.forEach(async(ownerId) => {
            const { data } = await findFiles({ ownerId })
            tempArr.push(...data.map(file => {
              return {
                name: file.originalFilename,
                url: file.url,
                ownerId: file.ownerId,
                ...file
              }
            }))
          })
          this.fileList = tempArr
        } else {
          this.fileList = []
        }
      },
      deep: true,
      immediate: true
    }
  },
  methods: {
    // 上传前loading加载
    handleBeforeUpload(file) {
      let isImg = false
      if (this.fileType.length) {
        let fileExtension = ''
        if (file.name.lastIndexOf('.') > -1) {
          fileExtension = file.name.slice(file.name.lastIndexOf('.') + 1)
        }
        isImg = this.fileType.some(type => {
          if (file.type.indexOf(type) > -1) return true
          if (fileExtension && fileExtension.indexOf(type) > -1) return true
          return false
        })
      } else {
        isImg = file.type.indexOf('image') > -1
      }

      if (!isImg) {
        this.$modal.msgError(`文件格式不正确, 请上传${this.fileType.join('/')}图片格式文件!`)
        this.isUploadRejected = true
        return false
      }
      if (this.fileSize) {
        const isLt = file.size / 1024 / 1024 < this.fileSize
        if (!isLt) {
          this.$modal.msgError(`上传头像图片大小不能超过 ${this.fileSize} MB!`)
          this.isUploadRejected = true
          return false
        }
      }
      this.isUploadRejected = false
      this.$modal.loading('正在上传图片，请稍候...')
      this.number++
    },
    // 文件个数超出
    handleExceed() {
      this.$modal.msgError(`上传文件数量不能超过 ${this.limit} 个!`)
    },
    // 上传成功回调
    handleUploadSuccess(res, file) {
      if (res.code === 200) {
        this.uploadList.push({ name: res.data.originalFilename, url: res.data.url, ownerId: res.data.ownerId })
        this.uploadedSuccessfully()
      } else {
        this.number--
        this.$modal.closeLoading()
        this.$modal.msgError(res.msg)
        this.$refs.imageUpload.handleRemove(file)
        this.uploadedSuccessfully()
      }
    },
    // 删除图片
    beforeRemove(file) {
      if (this.isUploadRejected) {
        return true // 跳过确认弹窗
      } else {
        return this.$confirm(`确定移除 ${file.name}？`)
      }
    },
    handleRemove(file) {
      if (!file.id) return
      const findex = this.fileList.map(f => f.id).indexOf(file.id)
      if (findex > -1) {
        removeFile(file.id).then(res => {
          if (res.code === 200) {
            this.fileList.splice(findex, 1)
            this.$emit('input', this.formatValue(this.fileList))
            this.$message({
              type: 'success',
              message: '删除成功!'
            })
          }
        })
      }
    },
    // 上传失败
    handleUploadError() {
      this.$modal.msgError('上传图片失败，请重试')
      this.$modal.closeLoading()
    },
    // 上传结束处理
    uploadedSuccessfully() {
      if (this.number > 0 && this.uploadList.length === this.number) {
        this.fileList = this.fileList.concat(this.uploadList)
        this.uploadList = []
        this.number = 0
        this.$emit('input', this.formatValue(this.fileList))
        this.$modal.closeLoading()
      }
    },
    // 预览
    handlePictureCardPreview(file) {
      this.dialogImageUrl = file.url
      this.dialogVisible = true
    },
    // 格式化返回对象
    formatValue(list) {
      return list.filter(item => item.ownerId).map(el => el.ownerId)
    }
  }
}
</script>
<style scoped lang="scss">
// .el-upload--picture-card 控制加号部分
::v-deep.hide .el-upload--picture-card {
  display: none;
}

// 去掉动画效果
::v-deep .el-list-enter-active,
::v-deep .el-list-leave-active {
  transition: all 0s;
}

::v-deep .el-list-enter,
.el-list-leave-active {
  opacity: 0;
  transform: translateY(0);
}
</style>

