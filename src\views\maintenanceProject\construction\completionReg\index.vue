<template>
  <div>
    <reg-list class="app-container" ref="regRef" :status="0">
      <template #operate="scope">
        <el-button
          size="mini"
          type="text"
          icon="el-icon-plus"
          :disabled="scope.scope.row.isEnd == 1"
          v-has-menu-permi="['projConstructionFinished:finished:add']"
          @click="handleAdd(scope.scope.row)"
        >添加签证
        </el-button>
        <el-button
          size="mini"
          type="text"
          :disabled="scope.scope.row.status == 6"
          icon="el-icon-delete"
          v-has-menu-permi="['projConstruction:construction:process']"
          @click="handleWithdraw(scope.scope.row)"
        >撤回到待施工
        </el-button>
        <el-button
          size="mini"
          type="text"
          :disabled="scope.scope.row.status == 6"
          icon="el-icon-check"
          v-has-menu-permi="['projConstruction:construction:process']"
          @click="handleCompleted(scope.scope.row)"
        >完工
        </el-button>
      </template>
    </reg-list>
    <el-drawer append-to-body modal-append-to-body :wrapperClosable="false" title="添加签证" destroy-on-close :visible.sync="openAdd" v-if="openAdd" size="70%">
      <detail :row-data="row" @close="handleCloseDetail"></detail>
    </el-drawer>
  </div>
</template>
<script>

import RegList from "@/views/maintenanceProject/construction/component/regList.vue";
import Detail from "./detail.vue";
import {withdraw} from "@/api/maintenanceProject/construction/completionReg";
import {process} from "@/api/maintenanceProject/taskList";

export default {
  name: 'CompletionReg',
  components: {Detail, RegList},
  data() {
    return {
      openAdd: false,
      row: {}
    }
  },
  methods: {
    handleAdd(row) {
      if (row.isEnd == 1) {
        this.$message.warning('该任务单已完结!')
        return
      }
      this.openAdd = true
      this.row = row
    },
    handleCloseDetail() {
      this.openAdd = false
      this.$refs.regRef.handleQuery()
    },
    handleWithdraw(row) {
      if (row.visaCount > 0) {
        this.$message.error('已添加签证单,无法撤回!');
        return
      }
      this.$confirm('确定撤回到待施工?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$refs.regRef.loading = true
        const params = {
          businessKey: row.id,
          taskId: row.taskId,
          approved: false,
          comment: '撤回到待施工'
        }
        process(params).then(res => {
          this.$message.success('撤回成功!');
          this.$refs.regRef.handleQuery()
        })
      })
    },

    handleCompleted(row) {
      this.$prompt('', '是否确认完工', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        inputPattern: /^.+$/,
        inputErrorMessage: '请填写审核意见'
      }).then(({value}) => {
        this.$refs.regRef.loading = true
        const params = {
          businessKey: row.id,
          taskId: row.taskId,
          approved: true,
          remark: value
        }
        process(params).then(res => {
          this.$message.success('撤回成功!');
          this.$refs.regRef.handleQuery()
        })
      })
    }
  }
}
</script>

<style scoped lang="scss">

</style>
<style lang="scss" scoped>
@import "@/assets/styles/business.scss";
</style>
