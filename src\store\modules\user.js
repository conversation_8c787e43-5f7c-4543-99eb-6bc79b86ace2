import { login, logout, getInfo, refreshToken, loginByUser, phoneLogin, phoneResetPwd } from '@/api/login'
import {getToken, setToken, setExpiresIn, removeToken, setHomeUrl,} from '@/utils/auth';
import cache from "@/plugins/cache";
import { removeIndexedDB } from '@/utils/IndexedDb.js'

const user = {
  state: {
    token: getToken(),
    id: '',
    name: '',
    avatar: '',
    nickName: '',
    deptId: null,
    roles: [],
    permissions: [],
    menuPerms: [],
    isJumpLogin: false,
  },

  mutations: {
    SET_TOKEN: (state, token) => {
      state.token = token;
    },
    SET_EXPIRES_IN: (state, time) => {
      state.expires_in = time;
    },
    SET_ID: (state, id) => {
      state.id = id;
    },
    SET_NAME: (state, name) => {
      state.name = name;
    },
    SET_NICKNAME: (state, nickName) => {
      state.nickName = nickName;
    },
    SET_AVATAR: (state, avatar) => {
      state.avatar = avatar;
    },
    SET_ROLES: (state, roles) => {
      state.roles = roles;
    },
    SET_PERMISSIONS: (state, permissions) => {
      state.permissions = permissions;
    },
    SET_MENU_PERMS: (state, menuPerms) => {
      state.menuPerms = menuPerms;
    },
    SET_DEPTID: (state, deptId) => {
      state.deptId = deptId;
    },
    IS_JUMP_LOGIN: (state, isJumpLogin) => {
      state.isJumpLogin = isJumpLogin;
    },
  },

  actions: {
    // 登录
    Login({ commit }, userInfo) {
      const username = userInfo.username.trim();
      const password = userInfo.password;
      const code = userInfo.code;
      const uuid = userInfo.uuid;
      return new Promise((resolve, reject) => {
        login(username, password, code, uuid)
          .then((res) => {
            let data = res.data;
            setToken(data.access_token);
            commit('SET_TOKEN', data.access_token);
            setExpiresIn(data.expires_in);
            commit('SET_EXPIRES_IN', data.expires_in);
            commit('IS_JUMP_LOGIN', false);//是否为外部跳转登录
            setHomeUrl(data.home_url ?? '');
            resolve();
          })
          .catch((error) => {
            reject(error);
          });
      });
    },
    LoginByUser({ commit }, userInfo) {
      const username = userInfo.username.trim();
      return new Promise((resolve, reject) => {
        loginByUser(username)
          .then((res) => {
            let data = res.data;
            setToken(data.access_token);
            commit('SET_TOKEN', data.access_token);
            setExpiresIn(data.expires_in);
            commit('SET_EXPIRES_IN', data.expires_in);
            commit('IS_JUMP_LOGIN', true);//是否为外部跳转登录
            setHomeUrl(data.home_url ?? '');
            resolve();
          })
          .catch((error) => {
            reject(error);
          });
      });
    },
    LoginByPhone({ commit }, phoneForm) {
      const phone = phoneForm.phone.trim();
      const smsCode = phoneForm.smsCode.trim();
      return new Promise((resolve, reject) => {
        phoneLogin(phone, smsCode)
          .then((res) => {
            let data = res.data;
            if (!Array.isArray(data)) {
              console.log("data :", data)
              setToken(data.access_token);
              commit('SET_TOKEN', data.access_token);
              setExpiresIn(data.expires_in);
              commit('SET_EXPIRES_IN', data.expires_in);
              commit('IS_JUMP_LOGIN', false);//是否为外部跳转登录
              setHomeUrl(data.home_url ?? '');
              resolve(data);
            } else {
              resolve(data)
            }
          })
          .catch((error) => {
            reject(error);
          });
      });
    },
    // 手机验证码重置密码
    ResetPassword({ commit }, resetInfo) {
      return new Promise((resolve, reject) => {
        phoneResetPwd(resetInfo).then(res => {
          resolve(res)
        }).catch(error => {
          reject(error)
        })
      })
    },
    // 获取用户信息
    GetInfo({ commit, state }) {
      return new Promise((resolve, reject) => {
        getInfo()
          .then((res) => {
            console.log(res)
            const user = res.user;
            const avatar =
              user.avatar == '' || user.avatar == null
                ? require('@/assets/images/profile.jpg')
                : user.avatar;
            if (res.roles && res.roles.length > 0) {
              // 验证返回的roles是否是一个非空数组
              commit('SET_ROLES', res.roles);
              commit('SET_PERMISSIONS', res.permissions);
              commit('SET_MENU_PERMS', res.menuPerms);
            } else {
              commit('SET_ROLES', ['ROLE_DEFAULT']);
            }
            commit('SET_ID', user.userId);
            commit('SET_NAME', user.userName);
            commit('SET_NICKNAME', user.nickName);
            commit('SET_AVATAR', avatar);
            commit('SET_DEPTID', user.deptId);
            resolve(res);
          })
          .catch((error) => {
            reject(error);
          });
      });
    },

    // 刷新token
    RefreshToken({ commit, state }) {
      return new Promise((resolve, reject) => {
        refreshToken(state.token)
          .then((res) => {
            setExpiresIn(res.data);
            commit('SET_EXPIRES_IN', res.data);
            resolve();
          })
          .catch((error) => {
            reject(error);
          });
      });
    },

    // 退出系统
    LogOut({ commit, state }) {
      return new Promise((resolve, reject) => {
        logout(state.token)
          .then(() => {
            commit('SET_TOKEN', '');
            commit('SET_ROLES', []);
            commit('SET_PERMISSIONS', []);
            removeToken();
            // 移除缓存
            cache.session.remove('lineObj');
            cache.session.remove('rangeData');
            cache.session.remove('sessionObj');
            cache.session.remove('otherData');
            // 移除 IndexedDB 数据库
            removeIndexedDB('mapData');
            resolve();
          })
          .catch((error) => {
            reject(error);
          });
      });
    },

    // 前端 登出
    FedLogOut({ commit }) {
      return new Promise((resolve) => {
        commit('SET_TOKEN', '');
        removeToken();
        resolve();
      });
    },
  },
};

export default user;
