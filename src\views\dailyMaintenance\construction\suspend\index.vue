<template>
  <div class="app-container maindiv">
    <registration-list ref="regis" :page-list="pageList" :stage="-1">
      <template #btn>
        <el-col :span="1.5">
          <el-button
              icon="el-icon-download"
              size="mini"
              type="warning"
              v-has-menu-permi="['dispose:construction:export']"
              @click="exportList"
          >导出清单
          </el-button
          >
        </el-col>
      </template>
      <template #operate="scope" >
        <el-button
          size="mini"
          type="text"
          v-has-menu-permi="['dispose:construction:review']"
          icon="el-icon-check"
          @click="handlePass (scope.scope.row)"
        >通过
        </el-button>
        <el-button
          size="mini"
          type="text"
          v-has-menu-permi="['dispose:construction:reject']"
          icon="el-icon-close"
          @click="handleReject(scope.scope.row)"
        >驳回
        </el-button>
      </template>
    </registration-list>
  </div>
</template>
<script>
import registrationList from '../component/registrationList.vue'
import {
  pageList,
  reviewNotYetDispose,
  notYetDispose
} from "@/api/dailyMaintenance/construction/suspend";
export default {
  name: "Suspend",
  components: {registrationList},
  data() {
    return {
      drawerTitle: '验收申请',
      drawer: false,
      rowData: {}
    }
  },
  methods: {
    pageList,
    handleRegis(rows) {
      this.rowData = rows
      this.drawer = true
    },
    // 暂不处理
    handlePass(rows) {
      this.$modal.confirm('是否确认通过').then(() => {
        if (rows.id) rows.daliyId = rows.id
        reviewNotYetDispose(rows).then(res => {
          this.$message.success('通过成功')
          this.$refs.regis.handleQuery()
        })
      })
    },
    handleReject(rows) {
      this.$modal.confirm('是否确认驳回').then(() => {
        if (rows.id) rows.daliyId = rows.id
        notYetDispose(rows).then(res => {
          this.$message.success('驳回成功')
          this.$refs.regis.handleQuery()
        })
      })
    },
    handleCloseDetail() {
      this.rowData = {}
      this.drawer = false
      this.$refs.regis.handleQuery()
    },
    // 导出清单按钮
    exportList() {
      this.$refs.regis.queryParams.year = this.$refs.regis.queryParams.yearStr ? parseInt(this.$refs.regis.queryParams.yearStr) : null

      this.download(
          'manager/dispose/export',
          {...this.$refs.regis.queryParams},
          `dispose_${new Date().getTime()}.xlsx`,
          {
            headers: {'Content-Type': 'application/json;'},
            parameterType: 'body'
          }
      )
    },
  }
}
</script>
