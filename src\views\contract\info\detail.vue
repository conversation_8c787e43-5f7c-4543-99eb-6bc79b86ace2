<template>
  <div class="road-interflow-edit" v-loading="loading">
    <el-row :gutter="15">
      <el-form
        ref="elForm"
        :model="formData"
        :rules="rules"
        size="medium"
        label-width="130px"
      >
        <el-col :span="12">
          <el-form-item label="合同名称" prop="name">
            <el-input
              v-model="formData.name"
              placeholder="请输入合同名称"
              clearable
              :style="{ width: '100%' }"
            >
            </el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="业主单位" prop="domainId">
            <selectTree
              :key="'domainId'"
              :style="{ width: '100%' }"
              v-model="formData.domainId"
              :deptType="100"
              :deptTypeList="[1, 3, 4]"
              placeholder="业主单位"
              clearable
              filterable
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="承包商" prop="conDomainId">
            <selectTree
              :key="'conDomainId'"
              :style="{ width: '100%' }"
              v-model="formData.conDomainId"
              :dept-type="100"
              :expandAll="false"
              placeholder="承包商"
              clearable
              filterable
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="报价体系" prop="libName">
            <el-input
              v-model="formData.libName"
              placeholder="请输入报价体系"
              @click.native="openLib=true"
              :style="{ width: '100%' }"
            >
              <i
                  slot="suffix"
                  v-show="formData['libName']"
                  @click.stop="clearLib"
                  class="el-input__icon el-icon-error err-style"
              ></i>
            </el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="金额(万元)" prop="amount">
            <el-input
              v-model="formData.amount"
              placeholder="请输入金额"
              clearable
              :style="{ width: '100%' }"
            >
            </el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="开始时间" prop="beginDate">
            <el-date-picker
              v-model="formData.beginDate"
              format="yyyy-MM-dd"
              value-format="yyyy-MM-dd"
              :style="{ width: '100%' }"
              placeholder="请输入开始时间"
              :picker-options="planStartPickerOptions"
              clearable
            ></el-date-picker>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="结束时间" prop="endDate">
            <el-date-picker
              v-model="formData.endDate"
              format="yyyy-MM-dd"
              value-format="yyyy-MM-dd"
              :style="{ width: '100%' }"
              placeholder="请输入结束时间"
              :picker-options="planEndPickerOptions"
              clearable
            ></el-date-picker>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="合同类型" prop="type">
            <DictSelect
              v-model="formData.type"
              :type="'contract_type'"
              :placeholder="'合同类型'"
              clearable
            ></DictSelect>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="监理费率" prop="superviseRate">
            <el-input
              v-model="formData.superviseRate"
              type="number"
              placeholder="请输入监理费率"
              clearable
              :style="{ width: '100%' }"
            >
              <template slot="append">%</template>
            </el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="设计费率" prop="designRate">
            <el-input
              v-model="formData.designRate"
              type="number"
              placeholder="请输入设计费率"
              clearable
              :style="{ width: '100%' }"
            >
              <template slot="append">%</template>
            </el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="设计方法" prop="designSchemeName">
            <el-input
              v-model="formData.designSchemeName"
              @click.native="openLibPrice=true;funName='designScheme'"
              placeholder="请输入设计方法"
              clearable
              :style="{ width: '100%' }"
            >
            </el-input>
          </el-form-item>
        </el-col>
        <el-col :spam="12"></el-col>
        <el-col :span="12">
          <el-form-item label="日养安全生产费率" prop="dSafeProductionRate">
            <el-input
              v-model="formData.dSafeProductionRate"
              type="number"
              placeholder="请输入日养安全生产费率"
              clearable
              :style="{ width: '100%' }"
            >
              <template slot="append">%</template>
            </el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="日养安全生产方法" prop="dSafeProductionSchemeName">
            <el-input
              v-model="formData.dSafeProductionSchemeName"
              @click.native="openLibPrice=true;funName='dSafeProductionScheme'"
              placeholder="请输入日养安全生产方法"
              clearable
              :style="{ width: '100%' }"
            >
            </el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="日养安全保通费率" prop="dSafeGuaranteeRate">
            <el-input
              v-model="formData.dSafeGuaranteeRate"
              type="number"
              placeholder="请输入日养安全保通费率"
              clearable
              :style="{ width: '100%' }"
            >
              <template slot="append">%</template>
            </el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="日养安全保通方法" prop="dSafeGuaranteeSchemeName">
            <el-input
              v-model="formData.dSafeGuaranteeSchemeName"
              @click.native="openLibPrice=true;funName='dSafeGuaranteeScheme'"
              placeholder="日养安全保通方法"
              clearable
              :style="{ width: '100%' }"
            >
            </el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="专项安全生产费率" prop="pSafeProductionRate">
            <el-input
              v-model="formData.pSafeProductionRate"
              type="number"
              placeholder="请输入专项安全生产费率"
              clearable
              :style="{ width: '100%' }"
            >
              <template slot="append">%</template>
            </el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="专项安全生产方法" prop="pSafeProductionSchemeName">
            <el-input
              v-model="formData.pSafeProductionSchemeName"
              @click.native="openLibPrice=true;funName='pSafeProductionScheme'"
              placeholder="专项安全生产方法"
              clearable
              :style="{ width: '100%' }"
            >
            </el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="专项安全保通费率" prop="pSafeGuaranteeRate">
            <el-input
              v-model="formData.pSafeGuaranteeRate"
              type="number"
              placeholder="请输入专项安全保通费率"
              clearable
              :style="{ width: '100%' }"
            >
              <template slot="append">%</template>
            </el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="专项安全保通方法" prop="pSafeGuaranteeSchemeName">
            <el-input
              v-model="formData.pSafeGuaranteeSchemeName"
              @click.native="openLibPrice=true;funName='pSafeGuaranteeScheme'"
              placeholder="请输入专项安全保通方法"
              clearable
              :style="{ width: '100%' }"
            >
            </el-input>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="养护路段" prop="field160">
            <el-select @change="checkMaintenanceSection" filterable placeholder="输入搜索..." style="width: 100%">
              <el-option
                  v-for="item in filteredMaintenanceSections"
                  :key="item.maintenanceSectionId"
                  :label="item.maintenanceSectionName"
                  :value="item.maintenanceSectionId">
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-table v-adjust-table
            size="mini"
            style="width: 100%"
            height="150px"
            v-loading="loading"
            border
            ref="routeTable"
            @selection-change="handleSelectionChange"
            :data="maintenanceSectionShowList"
          >
            <el-table-column type="selection" width="50" align="center"/>
            <el-table-column label="养护路段" align="center" prop="maiSecName"/>
            <el-table-column label="合同端编号" align="center" prop="conCode">
              <template slot-scope="scope">
                <el-input
                    v-model="scope.row.conCode"
                    size="small"
                    @input="handleInputConCode(scope.$index, 'conCode', $event)"
                ></el-input>
              </template>
            </el-table-column>
            <el-table-column label="操作" align="center">
              <template slot-scope="scope">
                <el-button type="text" @click="maintenanceSectionShowList = maintenanceSectionShowList.filter(item => item!== scope.row)">删除</el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-col>
        <el-col :span="24" style="margin-top: 20px">
          <el-form-item label="备注" prop="remark">
            <el-input
              type="textarea"
              v-model="formData.remark"
              placeholder="请输入备注"
              clearable
              :style="{ width: '100%' }"
            >
            </el-input>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <div class="text-center">
            <el-button type="primary" @click="onSubmit">保 存</el-button>
            <el-button @click="onClose">取 消</el-button>
          </div>
        </el-col>
      </el-form>
    </el-row>
    <el-dialog title="选择报价体系" destroy-on-close v-if="openLib" :visible.sync="openLib"
               width="1000px" append-to-body>
      <lib-table-select @checkLib="checkLib"></lib-table-select>
    </el-dialog>
    <el-dialog title="选择报价" destroy-on-close v-if="openLibPrice" :visible.sync="openLibPrice"
               width="500px" append-to-body>
      <lib-tree-select :lib-id="formData.libId" :funName="funName" @checkLibPrice="checkLibPrice"></lib-tree-select>
    </el-dialog>
  </div>
</template>
<script>
import {addContract, saveContract, getDetail} from '@/api/contract/info'
import selectTree from "@/components/DeptTmpl/selectTree.vue";
import libTableSelect from "@/views/contract/info/libTableSelect.vue";
import libTreeSelect from "@/views/contract/info/libTreeSelect.vue";
import {listMaintenanceSectionAll} from "@/api/system/maintenanceSection";
import moment from "moment";

export default {
  components: {selectTree, libTableSelect, libTreeSelect},
  props: {
    contract: {
      type: Object,
      default: () => {
      }
    }
  },
  computed: {
    // 计算属性用于过滤不需要显示的项
    filteredMaintenanceSections() {
      // 使用filter方法去除与maintenanceSectionShowList中maintenanceSectionId重复的项
      return this.maintenanceSectionList.filter(item =>
          !this.maintenanceSectionShowList.some(selectedItem => selectedItem.maiSecId === item.maintenanceSectionId)
      );
    },
  },
  data() {
    return {
      loading: false,
      formData: {},
      rules: {
        name: [
          {
            required: true,
            message: '请输入合同名称',
            trigger: 'blur'
          }
        ],
        domainId: [
          {
            required: true,
            message: '请输入业主单位',
            trigger: 'change'
          }
        ],
        conDomainId: [
          {
            required: true,
            message: '请输入承包商',
            trigger: 'change'
          }
        ],
        libName: [
          {
            required: true,
            message: '请输入报价体系',
            trigger: 'change'
          }
        ],
        amount: [
          {
            required: true,
            message: '请输入金额',
            trigger: 'blur'
          }
        ],
        beginDate: [
          {
            required: true,
            message: '请输入开始时间',
            trigger: 'change'
          }
        ],
        endDate: [
          {
            required: true,
            message: '请输入结束时间',
            trigger: 'change'
          }
        ],
        type: [
          {
            required: true,
            message: '请选择下拉选择',
            trigger: 'change'
          }
        ]
      },
      maintenanceSectionShowList: [], // 已选的路段
      openLib: false,
      openLibPrice: false,
      funName: '',
      routeList: [],
      maintenanceSectionList: [],
      planStartPickerOptions: this.beginDate(),
      planEndPickerOptions: this.processDate(),
    }
  },
  watch: {
    contract: {
      handler: function (val, oldVal) {
        if (val.id) {
          this.formData = JSON.parse(JSON.stringify(val))
          this.getDetail()
        }
      },
      immediate: true
    },
    'formData.domainId': {
      handler: function (val) {
        if (val) {
          if (val == '1') val = undefined
          listMaintenanceSectionAll({ departmentId: val }).then(res => {
            this.maintenanceSectionList = res.data
          })
        }
      }
    },
    'formData.libName': {
      handler: function() {
        this.formData.designSchemeName = ''
        this.formData.designSchemeId = ''
        this.formData.dSafeProductionSchemeCode = ''
        this.formData.dSafeProductionSchemeId = ''
        this.formData.dSafeGuaranteeSchemeId = ''
        this.formData.pSafeProductionSchemeId = ''
        this.formData.pSafeGuaranteeSchemeId = ''
        this.formData.dSafeProductionSchemeName = ''
        this.formData.dSafeGuaranteeSchemeCode = ''
        this.formData.dSafeGuaranteeSchemeName = ''
        this.formData.pSafeProductionSchemeCode = ''
        this.formData.pSafeProductionSchemeName = ''
        this.formData.pSafeGuaranteeSchemeCode = ''
        this.formData.pSafeGuaranteeSchemeName = ''
        this.$set(this.formData, 'designRate', 0)
        this.$set(this.formData, 'dSafeProductionRate', 0)
        this.$set(this.formData, 'dSafeGuaranteeRate', 0)
        this.$set(this.formData, 'pSafeProductionRate', 0)
        this.$set(this.formData, 'pSafeGuaranteeRate', 0)
      }
    }
  },
  created() {
  },
  methods: {
    getDetail() {
      getDetail(this.contract.id).then(res => {
        this.formData = res.rows[0]
        this.formData.domainId = this.formData.domainId.toString()
        this.formData.conDomainId = this.formData.conDomainId.toString()
        this.maintenanceSectionShowList = this.formData.list
        if(this.formData.superviseRate) this.$set(this.formData, 'superviseRate', this.formatNumber(this.formData.superviseRate * 100))
        if(this.formData.designRate) this.$set(this.formData, 'designRate', this.formatNumber(this.formData.designRate * 100))
        if(this.formData.dSafeProductionRate) this.$set(this.formData, 'dSafeProductionRate', this.formatNumber(this.formData.dSafeProductionRate * 100))
        if(this.formData.dSafeGuaranteeRate) this.$set(this.formData, 'dSafeGuaranteeRate', this.formatNumber(this.formData.dSafeGuaranteeRate * 100))
        if(this.formData.pSafeProductionRate) this.$set(this.formData, 'pSafeProductionRate', this.formatNumber(this.formData.pSafeProductionRate * 100))
        if(this.formData.pSafeGuaranteeRate) this.$set(this.formData, 'pSafeGuaranteeRate', this.formatNumber(this.formData.pSafeGuaranteeRate * 100))
      })
    },
    onSubmit() {
      this.$refs['elForm'].validate((valid) => {
        if (!valid) return
        this.formData.list = this.maintenanceSectionShowList
        this.formData.beginDate = moment(this.formData.beginDate).startOf("day").format('YYYY-MM-DD HH:mm:ss')
        this.formData.endDate = moment(this.formData.endDate).endOf("day").format('YYYY-MM-DD HH:mm:ss')
        if(this.formData.superviseRate) this.formData.superviseRate = this.formData.superviseRate / 100
        if(this.formData.designRate) this.formData.designRate = this.formData.designRate / 100
        if(this.formData.dSafeProductionRate) this.formData.dSafeProductionRate = this.formData.dSafeProductionRate / 100
        if(this.formData.dSafeGuaranteeRate) this.formData.dSafeGuaranteeRate = this.formData.dSafeGuaranteeRate / 100
        if(this.formData.pSafeProductionRate) this.formData.pSafeProductionRate = this.formData.pSafeProductionRate / 100
        if(this.formData.pSafeGuaranteeRate) this.formData.pSafeGuaranteeRate = this.formData.pSafeGuaranteeRate / 100
        if (this.formData.id) {
          saveContract(this.formData).then(res => {
            this.$modal.msgSuccess('保存成功')
            this.onClose()
          })
        } else {
          addContract(this.formData).then(res => {
            this.$modal.msgSuccess('保存成功')
            this.onClose()
          })
        }
      })
    },
    onClose() {
      this.$emit('close')
    },
    checkMaintenanceSection(e) {
      console.log(e)
      // 将列表数据赋值给已选路段
      let rowData = this.maintenanceSectionList.find(item => item.maintenanceSectionId === e);
      if (rowData) {
        const routeData = {
          maiSecId: rowData.maintenanceSectionId,
          maiSecName: rowData.maintenanceSectionName,
          conCode: ''
        }
        this.maintenanceSectionShowList.push(routeData)
      }
    },
    handleSelectionChange(e) {
      this.routeList = e
    },
    checkLib(e) {
      console.log(e)
      this.formData.libId = e.id
      this.formData.libName = e.libName
      this.openLib = false
    },

    checkLibPrice(e) {
      this.formData[e.name + 'Id'] = e.data.id
      this.formData[e.name + 'Name'] = e.data.schemeName
      this.openLibPrice = false
    },
    clearLib() {
      this.formData.libName = undefined;
      this.formData.libId = undefined
      this.$forceUpdate();
    },
    handleInputConCode(index, column, event) {
      this.maintenanceSectionShowList[index][column] = event;
    },
    beginDate() {
      const self = this;
      return {
        disabledDate(time) {
          if (self.formData.endDate) {
            //如果结束时间不为空，则小于结束时间
            return new Date(self.formData.endDate).getTime() < time.getTime();
          } else {
            return false;
          }
        },
      };
    },
    processDate() {
      const self = this;
      return {
        disabledDate(time) {
          if (self.formData.beginDate) {
            //如果开始时间不为空，则结束时间大于开始时间
            return new Date(self.formData.beginDate).getTime() > time.getTime();
          } else {
            return false;
          }
        },
      };
    },
    formatNumber(num) {
      num = parseFloat(num.toFixed(2))
      if (Number.isInteger(num) || (num.toString().indexOf('.') !== -1 && parseFloat(num.toFixed(2)) === parseFloat(num.toFixed(0)))) {
        return num.toString();
      }
      return num
    }
  }
}
</script>
<style lang="scss" scoped></style>
<style lang="scss" scoped>
@import "@/assets/styles/business.scss";
</style>
