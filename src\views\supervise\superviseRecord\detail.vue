<template>
  <el-drawer v-bind="$attrs" v-on="$listeners">
    <div class="container">
      <el-descriptions title="督查记录信息" size="small" :column="5" >
        <el-descriptions-item label="检查单位">{{ data.inspectionUnitName }}</el-descriptions-item>
        <el-descriptions-item label="受检单位">{{ data.inspectedUnitName }}</el-descriptions-item>
        <el-descriptions-item label="检查日期">{{
            parseTime(data.inspectionTime, '{y}-{m}-{d}')
          }}
        </el-descriptions-item>
        <el-descriptions-item label="责任人">{{ data.supervisorName }}</el-descriptions-item>
        <el-descriptions-item label="整改人">{{ data.rectifierName }}</el-descriptions-item>
      </el-descriptions>
      <div style="margin: 0 8%">
        <el-divider>检查详情</el-divider>
      </div>
      <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" >
                <el-form-item label="状态:" prop="status">
                  <el-radio-group v-model="queryParams.status"  @input="getList">
                    <el-radio  >全部</el-radio>
                    <el-radio  label="已整改">已整改</el-radio>
                    <el-radio   label="未整改">未整改</el-radio>
                  </el-radio-group>
                </el-form-item>
        <!--        <el-form-item label="" prop="inspectionRecordId">-->
        <!--          <el-input-->
        <!--            v-model="queryParams.inspectionRecordId"-->
        <!--            placeholder="请输入督察记录表id"-->
        <!--            clearable-->
        <!--            prefix-icon="el-icon-user"-->
        <!--            style="width: 240px"-->
        <!--            @keyup.enter.native="handleQuery"-->
        <!--          />-->
        <!--        </el-form-item>-->
        <!--        <el-form-item label="" prop="status">-->
        <!--          <el-select v-model="queryParams.status" style="width: 240px" clearable>-->
        <!--            <el-option value="已整改" label="已整改"></el-option>-->
        <!--            <el-option value="未整改" label="未整改"></el-option>-->
        <!--          </el-select>-->
        <!--        </el-form-item>-->
        <!--        <el-form-item label="" prop="rectificationTime">-->
        <!--          <el-date-picker clearable-->
        <!--                          v-model="queryParams.rectificationTime"-->
        <!--                          type="date"-->
        <!--                          value-format="yyyy-MM-dd"-->
        <!--                          placeholder="请选择整改时间">-->
        <!--          </el-date-picker>-->
        <!--        </el-form-item>-->
        <!--        <el-form-item>-->
        <!--          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>-->
        <!--          <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>-->
        <!--        </el-form-item>-->
      </el-form>
      <!--默认折叠-->

      <!--默认折叠 ,此处仅作为示例-->
      <!--筛选区结束-->


      <!--操作按钮区开始-->
      <el-row :gutter="10" class="mb8" v-if="false">
        <el-col :span="1.5">
          <el-button
            type="primary"
            icon="el-icon-plus"
            size="mini"
            @click="handleAdd"
            v-hasPermi="['supervise:detail:add']"
          >新增
          </el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button
            type="success"
            icon="el-icon-edit"
            size="mini"
            :disabled="single"
            @click="handleUpdate"
            v-hasPermi="['supervise:detail:edit']"
          >修改
          </el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button
            type="danger"
            icon="el-icon-delete"
            size="mini"
            :disabled="multiple"
            @click="handleDelete"
            v-hasPermi="['supervise:detail:remove']"
          >删除
          </el-button>
        </el-col>
      </el-row>
      <!--操作按钮区结束-->

      <!--数据表格开始-->
      <div class="tableDiv">
        <el-table v-adjust-table size="mini" :height="showSearch ? 'calc(100vh - 320px)' : 'calc(100vh - 360px)'" style="width: 100%"
                  v-loading="loading" border :data="detailList" @selection-change="handleSelectionChange">
          <el-table-column type="selection" width="50" align="center"/>
          <el-table-column fixed label="序号" type="index" width="50">
          </el-table-column>
          <el-table-column label="检查内容"   width="220" :show-overflow-tooltip="true"  align="center" prop="content"/>
          <el-table-column label="要求及建议" width="220" :show-overflow-tooltip="true" align="center" prop="suggestion"/>
          <el-table-column label="状态" align="center" prop="status">
            <template slot-scope="scope">
              <el-tag :type="scope.row.status.startsWith('已')?'success':''">{{scope.row.status}}</el-tag>
            </template>
          </el-table-column>
          <el-table-column label="整改前照片" align="center" prop="beforeImage" width="100">
            <template slot-scope="scope">
              <image-preview :owner-id="scope.row.beforeImage" :width="40" :height="40" :key="scope.row.id"/>
            </template>
          </el-table-column>
          <el-table-column label="整改后照片" align="center" prop="afterImage" width="100">
            <template slot-scope="scope">
              <image-preview :owner-id="scope.row.afterImage" :width="40" :height="40" :key="scope.row.id"/>
            </template>
          </el-table-column>
          <el-table-column label="整改时间" align="center" prop="rectificationTime" width="180">
            <template slot-scope="scope">
              <span>{{ parseTime(scope.row.rectificationTime, '{y}-{m}-{d}') }}</span>
            </template>
          </el-table-column>
          <el-table-column label="备注" :show-overflow-tooltip="true" align="center" prop="remark"/>
          <el-table-column
            v-if="false"
            label="操作"
            fixed="right"
            align="center"
            width="160"
            class-name="small-padding fixed-width"
          >
            <template slot-scope="scope">
              <el-button
                size="mini"
                type="text"
                icon="el-icon-edit"
                @click="handleUpdate(scope.row)"
                v-hasPermi="['supervise:detail:edit']"
              >修改
              </el-button>
              <el-button
                size="mini"
                type="text"
                icon="el-icon-delete"
                @click="handleDelete(scope.row)"
                v-hasPermi="['supervise:detail:remove']"
              >删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>

        <pagination
          v-show="total>0"
          :total="total"
          :page.sync="queryParams.pageNum"
          :limit.sync="queryParams.pageSize"
          @pagination="getList"
        />
      </div>
      <!--数据表格结束-->

      <!-- 添加或修改督查详情对话框 -->
      <el-dialog :title="title" :visible.sync="open" width="50%" append-to-body>
        <el-form ref="form" :model="form" :rules="rules" label-width="120px" style="height: 50vh;overflow: auto">
          <el-form-item label="督察记录表id" prop="inspectionRecordId">
            <el-input v-model="form.inspectionRecordId" placeholder="请输入督察记录表id"/>
          </el-form-item>
          <el-form-item label="检查内容" prop="content">
            <el-input v-model="form.content" type="textarea" placeholder="请输入内容"/>
          </el-form-item>
          <el-form-item label="要求及建议" prop="suggestion">
            <el-input v-model="form.suggestion" type="textarea" placeholder="请输入内容"/>
          </el-form-item>
          <el-form-item label="整改时间" prop="rectificationTime">
            <el-date-picker clearable
                            style="width: 100%"
                            v-model="form.rectificationTime"
                            type="date"
                            value-format="yyyy-MM-dd"
                            placeholder="请选择整改时间">
            </el-date-picker>
          </el-form-item>
          <el-form-item label="整改前照片" prop="beforeImage">
            <image-upload v-model="form.beforeImage"/>
          </el-form-item>
          <el-form-item label="整改后照片" prop="afterImage">
            <image-upload v-model="form.afterImage"/>
          </el-form-item>
          <el-form-item label="备注" prop="remark">
            <el-input v-model="form.remark" placeholder="请输入备注"/>
          </el-form-item>
          <el-form-item label="状态" prop="status">
            <el-radio-group v-model="form.status">
              <el-radio label="已整改">已整改</el-radio>
              <el-radio label="未整改">未整改</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </el-dialog>
    </div>
  </el-drawer>
</template>

<script>
import {getToken} from "@/utils/auth";
import {addDetail, delDetail, getDetail, listDetail, updateDetail} from "@/api/supervise/detail";
import ImageUpload from "@/views/patrol/diseases/ImageUpload.vue";
import ImagePreview from  "@/views/patrol/diseases/ImagePreview.vue";
export default {
  name: "SuperviseRecordDetail",
  components: {ImageUpload, ImagePreview},
  props: {
    data: {
      type: Object,
      default: {}
    }
  },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: false,
      dictType: [],
      // 总条数
      total: 0,
      // 督查详情表格数据
      detailList: null,
      // 弹出层标题
      title: "",
      // 部门树选项
      deptOptions: undefined,
      // 是否显示弹出层
      open: false,

      // 表单参数
      form: {},
      defaultProps: {
        children: "children",
        label: "label"
      },
      // 用户导入参数
      upload: {
        // 是否显示弹出层（用户导入）
        open: false,
        // 弹出层标题（用户导入）
        title: "",
        // 是否禁用上传
        isUploading: false,
        // 是否更新已经存在的用户数据
        updateSupport: 0,
        // 设置上传的请求头部
        headers: {Authorization: "Bearer " + getToken()},
        // 上传的地址
        url: process.env.VUE_APP_BASE_API + "/system/user/importData"
      },
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 50,
        inspectionRecordId: null,
        content: null,
        suggestion: null,
        status: undefined,
        rectificationTime: null,
        beforeImage: null,
        afterImage: null,
      },
      // 列信息
      columns: [
        {key: 0, label: `督察记录表id`, visible: true},
        {key: 1, label: `检查内容`, visible: true},
        {key: 2, label: `要求及建议`, visible: true},
        {key: 3, label: `状态（未整改、已整改）`, visible: true},
        {key: 4, label: `整改时间`, visible: true},
        {key: 5, label: `整改前照片`, visible: true},
        {key: 6, label: `整改后照片`, visible: true},
        {key: 7, label: `备注`, visible: true}
      ],
      // 表单校验
      rules: {
        inspectionRecordId: [
          {required: true, message: "督察记录表id不能为空", trigger: "blur"}
        ],
        content: [
          {required: true, message: "检查内容不能为空", trigger: "blur"}
        ],
        suggestion: [
          {required: true, message: "要求及建议不能为空", trigger: "blur"}
        ],
        status: [
          {required: true, message: "状态不能为空", trigger: "change"}
        ],


      }
    };
  },
  watch: {
    data() {
      this.queryParams.inspectionRecordId = this.data.id
      this.form.inspectionRecordId = this.data.id
      this.getList()
    }
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询用户列表 */
    getList() {
      this.loading = true;
      listDetail(this.queryParams).then(response => {
        this.detailList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        inspectionRecordId: this.data.id,
        // inspectionRecordId: null,
        content: null,
        suggestion: null,
        status: null,
        rectificationTime: null,
        beforeImage: null,
        afterImage: null,
        remark: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dateRange = [];
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id);
      this.single = selection.length != 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加督查详情";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids;
      getDetail(id).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改督查详情";
      });

    },
    /** 提交按钮 */
    submitForm: function () {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.id != null) {
            updateDetail(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addDetail(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const id = row.id || this.ids;
      this.$modal.confirm('是否确认删除督查详情编号为"' + id + '"的数据项？').then(function () {
        return delDetail(id);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {
      });
    },

    /** 导出按钮操作 */
    handleExport() {

      this.download('supervise/detail/export', {
        ...this.queryParams
      }, `detail_${new Date().getTime()}.xlsx`)

    },
    /** 导入按钮操作 */
    handleImport() {
      this.upload.title = "用户导入";
      this.upload.open = true;
    },
    /** 下载模板操作 */
    importTemplate() {
      this.download('system/user/importTemplate', {}, `user_template.xlsx`)
    },
    // 文件上传中处理
    handleFileUploadProgress(event, file, fileList) {
      this.upload.isUploading = true;
    },
    // 文件上传成功处理
    handleFileSuccess(response, file, fileList) {
      this.upload.open = false;
      this.upload.isUploading = false;
      this.$refs.upload.clearFiles();
      this.$alert("<div style='overflow: auto;overflow-x: hidden;max-height: 70vh;padding: 10px 20px 0;'>" + response.msg + "</div>", "导入结果", {dangerouslyUseHTMLString: true});
      this.getList();
    },
    // 提交上传文件
    submitFileForm() {
      this.$refs.upload.submit();
    }
  },

};
</script>
<style scoped>
.container {
  margin-top: -20px;
  padding: 20px;
}
</style>

