<template>
  <div v-loading="loading" class="road-interflow-edit" style="padding: 20px">
    <el-row :gutter="20">
      <el-col :span="24">
        <div class="card_title">项目信息</div>
        <el-descriptions :column="3" border>
          <el-descriptions-item
            :label="item.label"
            v-for="(item, index) in projectColumns"
            :key="'myTable' + index"
          >{{ projectData[item.key] || "" }}</el-descriptions-item
          >
        </el-descriptions>
      </el-col>
      <el-form
        ref="elForm"
        :model="curShowForm"
        :rules="rules"
        label-width="200px"
        size="medium"
        label-position="left"
      >
        <el-col :span="12" v-if="status === 5">
          <el-form-item label="签证人员" label-width="100px" prop="visaBy">
            <el-cascader
              v-model="curShowForm.visaBy"
              :options="deptUserOptions"
              :props="visaProps"
              :show-all-levels="false"
              ref="visaRef"
              filterable
              clearable
              collapse-tags
              style="width: 100%"
            ></el-cascader>
          </el-form-item>
        </el-col>
        <el-col :span="12" v-if="status === 5">
          <el-form-item
            label="签证单审核人"
            label-width="120px"
            prop="visaCheckBy"
          >
            <el-cascader
              v-model="curShowForm.visaCheckBy"
              :options="deptUserOptions"
              :props="visaCheckProps"
              :show-all-levels="false"
              ref="visaCheckRef"
              filterable
              clearable
              style="width: 100%"
            ></el-cascader>
          </el-form-item>
        </el-col>
        <el-col :span="12" v-if="status === 2">
          <el-form-item label="是否计算安全保通费" prop="isGuarantee">
            <el-radio-group v-model="curShowForm.isGuarantee" @change="calculate">
              <el-radio
                v-for="(item, index) in urgentDegreeOptions"
                :key="index"
                :label="item.value"
                :disabled="item.disabled || fromEvent"
              >
                {{ item.label }}
              </el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
        <el-col :span="12" v-if="status === 2">
          <el-form-item label="是否计算安全生产费" prop="isProduction">
            <el-radio-group v-model="curShowForm.isProduction" @change="calculate">
              <el-radio
                v-for="(item, index) in urgentDegreeOptions"
                :key="index"
                :label="item.value"
                :disabled="item.disabled || fromEvent"
              >
                {{ item.label }}
              </el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
        <el-col :span="12" v-if="status === 2">
          <el-form-item label="修补完成时间" prop="endTime">
            <el-date-picker
              v-model="curShowForm.endTime"
              format="yyyy-MM-dd"
              value-format="yyyy-MM-dd"
              :style="{ width: '100%' }"
              placeholder="请选择修补完成时间"
              clearable
            >
            </el-date-picker>
          </el-form-item>
        </el-col>
        <el-col :span="24" style="margin-bottom: 20px">
          <div class="card_title">检测报告</div>
          <el-card class="box-card" shadow="never">
            <el-form-item label="" label-width="0">
              <file-upload
                v-model="curShowForm.file"
                :owner-id="fileId"
              ></file-upload>
            </el-form-item>
          </el-card>
        </el-col>
        <el-col :span="24" style="margin-bottom: 20px">
          <div class="card_title">
            报价方法
            <el-button
              icon="el-icon-plus"
              circle
              @click="addScheme"
              v-if="(status === 1 || status === 2) && !fromEvent "
            ></el-button>
          </div>
        </el-col>
        <el-col :span="24" style="margin-bottom: 20px">
          <methods-list :value.sync="constructionDetailList" :readOnly="fromEvent" @price-change="calculate"></methods-list>
        </el-col>
        <el-col :span="24">
          <div class="card_title">
            计算式说明
            <el-link
              type="primary"
              v-if="status === 1 || status === 2"
              @click="generateInstructions"
              >生成计算式说明</el-link
            >
          </div>
          <el-form-item label="" label-width="0">
            <el-input
              v-model="curShowForm.calcDesc"
              placeholder="计算式说明"
              rows="2"
              type="textarea"
              :disabled="readOnly"
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <div class="card_title">{{ remarkTxtEnum[status] }}登记备注</div>
          <el-form-item label="" label-width="0">
            <el-input
              v-model="curShowForm.comment"
              :placeholder="remarkTxtEnum[status] + '登记意见'"
              rows="2"
              type="textarea"
              :disabled="fromEvent"
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="24" v-if="status === 5">
          <div class="card_title">路政反馈</div>
          <el-form-item label="" label-width="0">
            <el-input
              v-model="curShowForm.roadadminDesc"
              placeholder="路政反馈"
              rows="2"
              type="textarea"
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="24" class="mt10">
          <div style="text-align: right">
            <el-button v-if="status == 1" type="primary" @click="handleSave"
              >登记</el-button
            >
            <el-button
              v-if="status == 2 || status == 3"
              type="primary"
              @click="handleSave"
              >提交</el-button
            >
            <template v-if="status == 4 || status == 5">
              <el-button type="primary" @click="handleSave">通过</el-button>
              <el-button type="danger" @click="overrule">驳回</el-button>
            </template>
            <el-button @click="close">取消</el-button>
          </div>
        </el-col>
      </el-form>
    </el-row>
    <methods-tree
      ref="methodsTree"
      :con-id="row.checkConId"
      v-model="methodsTreeSelectData"
      :domain-id="row.domainId"
      :loading="loading"
    ></methods-tree>
  </div>
</template>

<script>
import sortFileUpload from "@/components/SortFileUpload/index.vue";
import MethodsList from "@/components/MethodsList/index.vue";
import MethodsTree from "@/components/MethodsTree/index.vue";
import {
  changeConstructionStatus, getConstruction,
  getConstructionDetailListAll,
} from "@/api/regularTesting/taskManage/taskList";
import {
  getRegisterMethods,
  getFinishedMethods,
  updateFinishedDetail,
} from "@/api/regularTesting/constructionManage/constructionManage";
import { getSafetyFeeList } from "@/api/contract/quotationSystem";
import { getDetail as getContractInfo } from "@/api/contract/info";
import { getTreeStruct } from "@/api/tmpl";
import { v4 as uuidv4 } from "uuid";
import moment from "moment";
export default {
  components: { sortFileUpload, MethodsList, MethodsTree },
  data() {
    return {
      loading: false,
      curShowForm: {
        calcDesc: "",
        comment: "",
        file: "",
        isGuarantee: 1,
        isProduction: 1,
        endTime: "",
        feedback: "",
      },
      defaultProps: {
        children: "children",
        label: "schemeName",
      },
      visaProps: {
        multiple: true, //是否多选
        value: "id",
        emitPath: false,
      },
      visaCheckProps: {
        multiple: false, //是否多选
        value: "id",
        emitPath: false,
      },
      constructionDetailList: [],
      fileId: '',
      urgentDegreeOptions: [
        {
          label: "计算",
          value: 1,
        },
        {
          label: "不计算",
          value: 2,
        },
      ],
      remarkTxtEnum: {
        1: "开工",
        2: "完工",
        3: "提交验收",
        4: "施工单位审核",
        5: "验收",
      },
      deptUserOptions: [],
      rules: {
        visaBy: [
          {
            required: true,
            message: "请选择签证人",
            trigger: "change",
          },
        ],
        visaCheckBy: [
          {
            required: true,
            message: "请选择签证审核人",
            trigger: "change",
          },
        ],
        endTime: [
          {
            required: true,
            message: "请选择修补完成时间",
            trigger: "change",
          },
        ],
      },
      methodsTreeSelectData: [],
      contractInfo: null,
      projectColumns: [
        {
          label: "项目名称",
          key: "projName",
        },
        {
          label: "任务单名称",
          key: "name",
        },
        {
          label: "任务单编码",
          key: "code",
        },
        {
          label: "路段名称",
          key: "maiSecName",
        },
        {
          label: "位置",
          key: "position",
        },
        {
          label: "施工单位",
          key: "checkDomainName",
        },
        {
          label: "施工合同",
          key: "checkConName",
        },
        {
          label: "验收人员",
          key: "acceptancePerson",
        },
        {
          label: "工作内容",
          key: "content",
        },
        {
          label: "实施要求",
          key: "exeRequire",
        },
      ],
      projectData: {}
    };
  },
  props: {
    status: {
      // 流程状态 1开工登记 2完工登记 3提交验收 4施工单位审核 5验收登记 6施工进度
      type: Number,
      default: 1,
    },
    readOnly: {
      type: Boolean,
      default: false,
    },
    row: {
      type: Object,
      default: () => {},
    },
    type: {
      type: Number,
      default: 1,
    },
    fromEvent: {
      type: Boolean,
      default: false
    }
  },
  watch: {
    row: {
      handler(val) {
        if (val) {
          this.projectData = val
          if (this.status === 1) {
            getConstructionDetailListAll({ projConId: val.id }).then((res) => {
              if (res.data) {
                this.constructionDetailList = res.data || [];
              }
            });
          } else if (this.status === 2 && (val.status != 6 && !this.fromEvent)) {
            this.curShowForm.endTime = moment(new Date()).format("YYYY-MM-DD");
            getRegisterMethods(val.id).then((res) => {
              this.constructionDetailList = res.data?.registerMethodList || [];
              this.curShowForm.calcDesc = res.data?.calcDesc || "";
              // 获取合同信息
              getContractInfo(val.checkConId).then((res1) => {
                this.contractInfo = res1.rows[0];
                // 获取合同下安全费保通费
                getSafetyFeeList(val.checkConId).then((res2) => {
                  const filteredData = res2.data.filter((item) => item.schemeType === "专项养护").reduce((acc, item) => {
                    if (!acc.find((accItem) => accItem.schemeName === item.schemeName)) {
                      acc.push(item);
                    }
                    return acc;
                  }, []).sort((a, b) => a.schemeName.localeCompare(b.schemeName));
                  const tempList = filteredData.map((item, index)=> {
                   return {
                      conId: val.checkConId,
                      schemeId: item.id,
                      schemeCode: item.schemeCode,
                      schemeName: item.schemeName,
                      unit: item.unit,
                      price: item.price,
                      priceRate: item.priceRate,
                      isProduction: item.safetyFeeFlag,
                      order: this.constructionDetailList.length + index + 1,
                    }

                  })
                  this.constructionDetailList = [...this.constructionDetailList,...tempList];
                  this.calculate()
                });
              });
            });
          } else {
            getFinishedMethods(val.id).then((res) => {
              this.constructionDetailList = res.data?.finishedMethodList || [];
              this.curShowForm.calcDesc = res.data?.calculationDesc || "";
              if (this.status === 2) {
                this.curShowForm.isGuarantee = res.data.isGuarantee;
                this.curShowForm.isProduction = res.data.isProduction;
                this.curShowForm.endTime = res.data.endTime;
                this.curShowForm.file = res.data.fileId;
                this.curShowForm.id = res.data.id;
                this.curShowForm.conId = res.data.conId;
                this.curShowForm.comment = res.data.remark;
              }
              if (this.status == 5) {

                const visaBy = JSON.parse(localStorage.getItem('visaByArr'))
                if (visaBy) this.$set(this.curShowForm, 'visaBy', visaBy)

                const visaCheckBy = JSON.parse(localStorage.getItem('visaCheckBy'))
                if (visaCheckBy) this.$set(this.curShowForm, 'visaCheckBy', visaCheckBy)
              }
            });
          }
        }
      },
      immediate: true,
      deep: true,
    },
    methodsTreeSelectData: {
      async handler(val) {
        if (val.length > 0) {
          const constructionDetailList = [];
          const conId = this.row.checkConId;
          for (let index = 0; index < val.length; index++) {
            const item = val[index];
            const hasFlag = this.constructionDetailList.some((value) => {
              return value.schemeId === item.id;
            });
            if (hasFlag) {
              try {
                await this.$confirm(
                  `存在与子目号${item.schemeCode}相同的数据，是否确认加入?`,
                  "确认",
                  {
                    confirmButtonText: "确定",
                    cancelButtonText: "取消",
                    type: "warning",
                    distinguishCancelAndClose: true,
                  }
                );
                constructionDetailList.push({
                  conId,
                  schemeId: item.id,
                  schemeCode: item.schemeCode,
                  schemeName: item.schemeName,
                  unit: item.unit,
                  price: item.price,
                  priceRate: item.priceRate,
                  isProduction: item.safetyFeeFlag,
                  order:
                    this.constructionDetailList.length +
                    constructionDetailList.length +
                    1,
                });
              } catch (error) {
                continue;
              }
            } else {
              constructionDetailList.push({
                conId,
                schemeId: item.id,
                schemeCode: item.schemeCode,
                schemeName: item.schemeName,
                unit: item.unit,
                price: item.price,
                priceRate: item.priceRate,
                isProduction: item.safetyFeeFlag,
                order:
                  this.constructionDetailList.length +
                  constructionDetailList.length +
                  1,
              });
            }
          }

          this.constructionDetailList = [
            ...this.constructionDetailList,
            ...constructionDetailList,
          ];
        }
      },
      immediate: true,
      deep: true,
    },
    'curShowForm.file': {
      handler(val) {
        if (val && val.length > 0) {
          this.fileId = val[0]
        }
      }
    }
  },
  created() {
    this.getDeptTreeDef();
  },
  methods: {
    /** 查询部门-用户下拉树结构 */
    getDeptTreeDef() {
      getTreeStruct({ types: 111 }).then((response) => {
        this.deptUserOptions = response.data;
      });
    },
    calculate() {
      let lib1 = this.constructionDetailList.find(item => item.schemeName == '安全生产费')
      if (lib1) {
        this.$set(lib1, 'amount', 0)
        // 计算安全费和保通费
        if (this.curShowForm.isProduction == '1') {
          let aqf = 0
          for (let i = 0; i < this.constructionDetailList.length; i++) {
            let item = this.constructionDetailList[i]
            if( item.isProduction == '1' && item.schemeName != '安全生产费' && item.schemeName != '安全保通费') {
              aqf += (lib1.amount || 0) + Math.round(item.amount)
            }
          }
          console.log(aqf);

          this.$set(lib1, 'amount', Math.round(aqf * this.contractInfo.dSafeProductionRate))
        }
      }
      let lib2 = this.constructionDetailList.find(item => item.schemeName == '安全保通费')
      if (lib2) {
        this.$set(lib2, 'amount', 0)
        if (this.curShowForm.isGuarantee == '1') {
          let aqf = 0
          for (let i = 0; i < this.constructionDetailList.length; i++) {
            let item = this.constructionDetailList[i]
            if( item.schemeName != '安全生产费' && item.schemeName != '安全保通费') {
              aqf += (lib2.amount || 0) + Math.round(item.amount)
            }
          }
          this.$set(lib2, 'amount', Math.round(aqf * this.contractInfo.dSafeGuaranteeRate))
        }
      }
    },
    handleSave() {
      this.$refs.elForm.validate((valid) => {
        if (!valid) return;
        let changeStatusData = {
          approved: true,
          businessKey: this.row.id,
          comment: this.curShowForm.comment,
          taskId: this.row.taskId,
        };
        if (this.status === 1) {
          changeStatusData.constructionRegister = {
            calcDesc: this.curShowForm.calcDesc,
            conId: this.row.id,
            registerDesc: this.curShowForm.comment,
            fileId: this.fileId,
            registerMethodList: this.constructionDetailList,
          };
        } else if (this.status === 2) {
          if (this.row.status !== 6 && !this.fromEvent) {
            if (!this.curShowForm.calcDesc) {
              this.$message.warning("计算式说明不能为空");
              return;
            }
            changeStatusData.constructionFinished = {
              calculationDesc: this.curShowForm.calcDesc,
              conId: this.row.id,
              remark: this.curShowForm.comment,
              fileId: this.fileId,
              finishedMethodList: this.constructionDetailList,
              isGuarantee: this.curShowForm.isGuarantee,
              isProduction: this.curShowForm.isProduction,
              endTime: this.curShowForm.endTime,
            };
          } else {
            changeStatusData = {
              id: this.curShowForm.id,
              calculationDesc: this.curShowForm.calcDesc,
              conId: this.curShowForm.conId,
              remark: this.curShowForm.comment,
              fileId: this.fileId,
              finishedMethodList: this.constructionDetailList,
              isGuarantee: this.curShowForm.isGuarantee,
              isProduction: this.curShowForm.isProduction,
              endTime: this.curShowForm.endTime,
            };
          }
        } else {
          const constructionFile = {
            fileId: this.fileId,
            inId: this.row.id,
          };
          if (this.fileId) {
            if (this.status === 3) {
              constructionFile.registerType = 3;
            }
            if (this.status === 4) {
              constructionFile.registerType = 4;
            }
          }
          if (this.status === 5) {
            const visaBy = this.$refs.visaRef
              .getCheckedNodes()
              .map((item) => item.data.id);
            const visaByName = this.$refs.visaRef
              .getCheckedNodes()
              .map((item) => item.data.label);
            const visaCheckBy = this.$refs.visaCheckRef
              .getCheckedNodes()
              .map((item) => item.data.id);
            const visaCheckByName = this.$refs.visaCheckRef
              .getCheckedNodes()
              .map((item) => item.data.label);
            changeStatusData.visaBy = visaBy.join(",");
            changeStatusData.visaName = visaByName.join(",");
            changeStatusData.visaCheckBy = visaCheckBy[0];
            changeStatusData.visaCheckName = visaCheckByName[0];
            changeStatusData.feedback = this.curShowForm.feedback;
            if (this.fileId) {
              constructionFile.registerType = 5;
            }

            localStorage.setItem('visaByArr', JSON.stringify(this.curShowForm.visaBy))
            localStorage.setItem('visaCheckBy', JSON.stringify(this.curShowForm.visaCheckBy))

          }
          if (this.fileId) {
            changeStatusData.constructionFile = constructionFile;
          }
        }
        this.changeStatus(changeStatusData);
      });
    },
    changeStatus(data) {
      if (this.status === 2 && (this.row.status === 6 || this.fromEvent) ) {
        const submitData = {
          ...data
        }
        if (this.fromEvent) {
          submitData.flag = 1
        }
        updateFinishedDetail(submitData)
          .then((res) => {
            this.$message.success("操作成功");
            this.close();
          })
          .finally(() => {
            this.loading = false;
          });
      } else {
        changeConstructionStatus(data)
          .then((res) => {
            this.$message.success("操作成功");
            this.close();
          })
          .finally(() => {
            this.loading = false;
          });
      }
    },
    overrule() {
      if(!this.curShowForm.comment) {
        this.$message.warning(`请填写${this.remarkTxtEnum[this.status]}登记备注`);
        return;
      }
      const changeStatusData = {
        approved: false,
        businessKey: this.row.id,
        comment: this.curShowForm.comment,
        taskId: this.row.taskId,
      };
      if (this.fileId) {
        const constructionFile = {
          fileId: this.fileId,
          inId: this.row.id,
        };
        if (this.status === 4) {
          constructionFile.registerType = 4;
        }
        if (this.status === 5) {
          changeStatusData.feedback = this.curShowForm.feedback;
          constructionFile.registerType = 5;
        }
        changeStatusData.constructionFile = constructionFile;
      }
      this.changeStatus(changeStatusData);
    },
    close() {
      this.$emit("close");
    },
    // 生成计算式说明
    generateInstructions() {
      let calculationDesc = "";
      this.constructionDetailList.forEach((item) => {
        if (item.schemeName != '安全生产费' && item.schemeName != '安全保通费')
          calculationDesc += `${item.schemeName || ""}:${item.calcDesc || ""}=${
            item.amount || 0
          }${item.unit || ""}\n`;
      });
      this.$set(this.curShowForm, "calcDesc", calculationDesc);
    },
    addScheme() {
      if (!this.row.checkConId) {
        this.$message.error("请先选择合同");
        return;
      }
      this.$refs.methodsTree.openLibModel();
    },
  },
};
</script>
<style lang="scss" scoped>
.card_title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  text-align: left;
  margin-bottom: 15px;
  font-weight: bold;
}
</style>
<style lang="scss" scoped>
@import "@/assets/styles/business.scss";
</style>
