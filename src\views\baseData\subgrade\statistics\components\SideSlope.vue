<template>
	<div style="width: 100%; height: 70vh">
		<el-input
			style="width: 170px"
			v-model="queryParams.slopeCode"
			placeholder="边坡编码"
			clearable
		/>
		<el-input
			style="margin: 0 20px; width: 170px"
			v-model="queryParams.slopeName"
			placeholder="边坡名称"
			clearable
		/>

		<el-button type="primary" icon="el-icon-search" @click="handleQuery">搜索</el-button>
		<el-button icon="el-icon-refresh" @click="resetQuery">重置</el-button>
		<el-button style="margin-bottom: 10px" type="primary" @click="exportList">导出列表</el-button>
		<el-table
			ref="table"
			v-loading="loading"
			height="calc(100% - 88px)"
			border
			:data="tableData"
			:header-cell-style="{ height: '36px' }"
		>
			<el-table-column fixed label="序号" type="index" width="50" align="center">
				<template v-slot="scope">
					{{ scope.$index + (queryParams.pageNum - 1) * queryParams.pageSize + 1 }}
				</template>
			</el-table-column>
			<el-table-column
				fixed
				label="边坡编码"
				align="center"
				prop="slopeCode"
				min-width="120"
				show-overflow-tooltip
			/>
			<el-table-column
				label="边坡名称"
				align="center"
				prop="slopeName"
				min-width="120"
				show-overflow-tooltip
			/>
			<el-table-column
				label="管理处"
				align="center"
				prop="managementMaintenanceName"
				min-width="120"
				show-overflow-tooltip
			/>
			<el-table-column
				label="管养分处"
				align="center"
				prop="managementMaintenanceBranchName"
				min-width="120"
				show-overflow-tooltip
			/>
			<el-table-column
				label="养护路段"
				align="center"
				prop="maintenanceSectionName"
				min-width="120"
				show-overflow-tooltip
			/>
			<el-table-column
				label="路线编码"
				align="center"
				prop="routeCode"
				min-width="120"
				show-overflow-tooltip
			/>
			<el-table-column label="桩号范围" align="center" min-width="180" show-overflow-tooltip>
				<template #default="{ row }">
					<span>{{ formatPile(row.startStake) }}</span>
					<span v-if="row.startStake">~</span>
					<span>{{ formatPile(row.endStake) }}</span>
				</template>
			</el-table-column>
			<el-table-column align="center" prop="operationState" min-width="140" label="运营状态">
				<template slot-scope="{ row }">
					<el-link :underline="false" type="success">
						<DictTag :value="row.operationState" :options="dict.type.sys_operation_state" />
					</el-link>
				</template>
			</el-table-column>
			<el-table-column label="数据状态" align="center" min-width="140">
				<template #default="{ row }">
					<el-link :type="{ 1: 'info', 2: 'success' }[row.status]" :underline="false">
						<DictTag :value="row.status" :options="dict.type.base_data_state" />
					</el-link>
				</template>
			</el-table-column>
			<el-table-column align="center" prop="direction" width="140" label="方向">
				<template slot-scope="{ row }">
					<DictTag :value="row.direction" :options="dict.type.sys_route_direction" />
				</template>
			</el-table-column>
			<el-table-column label="位置" align="center" prop="lane" min-width="140">
				<template slot-scope="{ row }">
					<DictTag :value="row.lane" :options="dict.type.lane" />
				</template>
			</el-table-column>
			<el-table-column
				label="经度"
				align="center"
				prop="longitude"
				min-width="140"
				show-overflow-tooltip
			/>
			<el-table-column
				label="纬度"
				align="center"
				prop="latitude"
				min-width="140"
				show-overflow-tooltip
			/>
			<el-table-column label="施工里程桩号" align="center" min-width="140" show-overflow-tooltip>
				<template #default="{ row }">
					<span>{{ formatPile(row.constructionStake) }}</span>
				</template>
			</el-table-column>
			<el-table-column label="统一里程桩号" align="center" min-width="140" show-overflow-tooltip>
				<template #default="{ row }">
					<span>{{ formatPile(row.unifiedMileageStake) }}</span>
				</template>
			</el-table-column>
			<el-table-column
				label="路基类型"
				align="center"
				prop="roadbedType"
				min-width="140"
				show-overflow-tooltip
			/>
			<el-table-column
				label="土质"
				align="center"
				prop="soil"
				min-width="140"
				show-overflow-tooltip
			/>
			<el-table-column
				label="检查梯"
				align="center"
				prop="checkLadder"
				min-width="140"
				show-overflow-tooltip
			/>
			<el-table-column
				label="坡脚长度(m)"
				align="center"
				prop="slopingFootLength"
				min-width="140"
				show-overflow-tooltip
			/>
			<el-table-column
				label="落台宽度(m)"
				align="center"
				prop="dropWidth"
				min-width="140"
				show-overflow-tooltip
			/>
			<el-table-column
				label="边坡类型"
				align="center"
				prop="slopeType"
				min-width="120"
				show-overflow-tooltip
			>
				<template slot-scope="scope">
					<DictTag :value="scope.row.slopeType" :options="dict.type.side_slope_type" />
				</template>
			</el-table-column>
			<el-table-column
				label="长度(m)"
				align="center"
				prop="length"
				min-width="120"
				show-overflow-tooltip
			/>
			<el-table-column
				label="总高度(m)"
				align="center"
				prop="totalHeight"
				min-width="120"
				show-overflow-tooltip
			/>
			<el-table-column
				label="边坡总台数"
				align="center"
				prop="slopePlatformNumber"
				min-width="120"
				show-overflow-tooltip
			/>
			<el-table-column
				label="防护形式总数"
				align="center"
				prop="protectionFormNumber"
				min-width="120"
				show-overflow-tooltip
			/>
			<el-table-column
				label="备注"
				align="center"
				prop="remark"
				min-width="140"
				show-overflow-tooltip
			/>
		</el-table>
		<pagination
			:total="total"
			:page.sync="queryParams.pageNum"
			:limit.sync="queryParams.pageSize"
			:pageSizes="[10, 20, 30, 50, 100, 1000]"
			@pagination="getList"
		/>
	</div>
</template>

<script>
import { getListPage } from '@/api/baseData/subgrade/sideSlope/index.js'

export default {
	name: 'detail-table-sideSlope',
	props: {
		row: { type: undefined, default: undefined },
	},
	dicts: ['side_slope_type', 'sys_operation_state', 'base_data_state', 'sys_route_direction'],
	components: {},
	data() {
		return {
			loading: false,
			tableData: [],
			queryParams: {
				pageNum: 1,
				pageSize: 20,
			},
			rowParams: {},
			total: 0,
		}
	},
	created() {
		this.getParams()
		this.getList()
	},
	methods: {
		getParams() {
			this.rowParams = {
				...this.queryParams,
				routeCodes: this.row.routeCode ? [this.row.routeCode] : this.row.routeCodes,
				maintenanceSectionIds: this.row.maintenanceSectionIds
					? this.row.maintenanceSectionIds
					: undefined,
				maintenanceSectionId: this.row.maintenanceSectionId
					? this.row.maintenanceSectionId
					: undefined,
				managementMaintenanceBranchId: this.row.managementMaintenanceBranchId
					? this.row.managementMaintenanceBranchId
					: undefined,
				managementMaintenanceId: this.row.managementMaintenanceId
					? this.row.managementMaintenanceId
					: undefined,
			}
		},
		getList() {
			if (!this.row) return
			this.loading = true
			getListPage({
				...this.rowParams,
				...this.queryParams,
			})
				.then((res) => {
					if (res.code === 200) {
						this.tableData = res.rows || []
						this.total = res.total || 0
					}
				})
				.finally(() => {
					this.loading = false
				})
		},
		// 搜索按钮
		handleQuery() {
			this.queryParams.pageNum = 1
			this.getList()
		},
		// 重置按钮
		resetQuery() {
			this.queryParams = {
				pageNum: 1,
				pageSize: 20,
			}
			this.handleQuery()
		},
		exportList() {
			if (this.tableData.length === 0) return

			this.$modal
				.confirm('导出所有表格数据，是否继续？')
				.then(() => {
					this.download(
						'/baseData/sideslope/basic/exportPageTable',
						{
							...this.queryParams,
							...this.rowParams,
						},
						`sideslope_interflow_${new Date().getTime()}.xlsx`,
						{
							headers: { 'Content-Type': 'application/json;' },
							parameterType: 'body',
						}
					)
				})
				.catch(() => {})
		},
	},
	computed: {},
	watch: {},
}
</script>

<style scoped></style>
