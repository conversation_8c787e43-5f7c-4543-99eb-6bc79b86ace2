<template>
  <el-container>
    <el-header>
      <el-form
          :model="form"
          :inline="true"
          label-position="right"
          label-suffix="："
          ref="tigaForm"
          label-width="85px">
        <el-form-item label="监测类型" prop="monitorType">
          <el-select
              v-model="form.monitorType"
              placeholder="请选择监测类型"
              @change="changeMonitorType"
              no-data-text="无数据"
              value-key="content"
              size="small"
          >
            <el-option
                v-for="item in monitorTypeList"
                :key="item.code"
                :label="item.content"
                :value="item">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="监测内容" prop="monitorContent">
          <el-select
              v-model="form.monitorContent"
              default-first-option
              placeholder="请选择监测内容"
              @change="changeMonitorContent"
              no-data-text="无数据"
              value-key="content"
              size="small"
          >
            <el-option
                v-for="item in monitorContentList"
                :key="item.code"
                :label="item.content"
                :value="item">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="监测位置" prop="monitorLocation">
          <el-select
              v-model="form.monitorLocation"
              default-first-option
              placeholder="请选择监测位置"
              @change="changeMonitorLocation"
              no-data-text="无数据"
              value-key="content"
              size="small"
          >
            <el-option
                v-for="item in monitorLocationList"
                :key="item.code"
                :label="item.content"
                :value="item">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="传感器" prop="sensorId">
          <el-select
              v-model="form.sensorId"
              placeholder="请选择传感器"
              @change="changeSensor"
              no-data-text="无数据"
              value-key="sensorId"
              size="small"
          >
            <el-option
                v-for="item in sensorInfoList"
                :key="item.code"
                :label="item.sensorInstallCode"
                :value="item">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="具体类型" prop="specificMonitorType">
          <el-select
              class="selects"
              v-model="specificMonitorContentIds"
              multiple
              filterable
              collapse-tags
              no-data-text="无数据"
              style="width: 200px"
              placeholder="请选择"
              size="small"
              value-key="specificMonitorTypeId"
              :disabled="isDraw"
          >
            <el-option
                v-for="item in specificMonitorTypeList"
                :label="item.specificMonitorTypeName"
                :key="item.specificMonitorTypeId"
                :value="item"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="时间范围" prop="timerange">
          <el-date-picker
              size="medium"
              style="width:200px"
              type="datetimerange"
              v-model="form.timerange"
              value-format="yyyy-MM-dd HH:mm:ss"
              format="yyyy-MM-dd HH:mm:ss"
              @on-change="statusDateChange"
              :transfer="true"
          ></el-date-picker>
        </el-form-item>
        <el-form-item>
          <el-button
              type="primary"
              @click="draw"
              size="small"
              :loading="loading">
            <span>绘制</span>
          </el-button>
        </el-form-item>
        <el-form-item>
          当前选择结构物为：{{currentStructure.label}}
        </el-form-item>
      </el-form>
    </el-header>
    <el-main>
      <el-form
          :inline="true"
          label-position="right"
          label-suffix="："
          label-width="85px"
      >
        <el-form-item label="统计周期">
          <el-select
              v-model="granularityType"
              value-key="index"
              style="width: 70px"
              placeholder="请选择"
              size="small"
              @change="granularityTypeChange"
          >
            <el-option
                v-for="item in granularityTypeList"
                :label="item.label"
                :key="item.index"
                :value="item"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="统计方式">
          <el-select
              class="selects"
              v-model="analysisType"
              multiple
              filterable
              collapse-tags
              value-key="index"
              style="width: 200px"
              no-data-text="无数据"
              placeholder="请选择"
              size="small"
              :disabled="isDraw"
          >
            <el-option
                v-for="item in analysisTypeList"
                :label="item.label"
                :key="item.index"
                :value="item.index"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-checkbox v-model="isShowLimit" label="预警线" border size="small"></el-checkbox>
        </el-form-item>
      </el-form>
      <div v-loading="loading" id="myChart7" style="width: 100%; height: 400px; overflow:hidden"></div>
    </el-main>
  </el-container>
</template>

<script>
import {
  getSensorByDataCode, getDataForSpecificTypeAndAnalysisType, getMax, getMin
} from "@/api/jgjc/dataAnalysis/customize/index.js";
import * as echarts from "echarts";
export default {
  name: "Eigenvalue",
  props: ['monitorTypeList', 'currentStructure', 'isDraw'],
  emits: ['changeDraw'],
  data() {
    return {
      granularityNum: 1,
      granularityType: {index: 5, label: '天'},
      granularityTypeList: [{index: 3, label: '10分'}, {index: 4, label: '时'},{index: 5, label: '天'}, {index: 6, label: '周'}, {index: 7, label: '月'}, {index: 8, label: '年'}],
      analysisType: [ 2 ],
      analysisTypeList: [{index: 0, label: '最大值'}, {index: 1, label:'最小值'}, {index: 2, label: '均值'}, {index: 3, label: '中值'}
        , {index: 4, label: '均方差'}, {index: 5, label: '均方根'}, {index: 6, label: '第一条'}, {index: 7, label: '最后一条'}, {index: 8, label: '求和'}
        , {index: 9, label: '计数'}, {index: 10, label: '绝对最大值'}, {index: 11, label: '最大差'}, {index: 12, label: '绝对值累积量'}, {index: 13, label: '梯度'}],
      monitorContentList: [],
      monitorLocationList: [],
      sensorInfoList: [],
      selectLeafCode: '',
      form: {
        monitorType: '',
        monitorContent: '',
        monitorLocation: '',
        sensorId: '',
        timerange: ''
      },
      sensorInfo: {},
      specificMonitorContentIds: [],
      specificMonitorTypeList: [],
      formatterNum: 12,
      isShowLimit: false,
      loading: false
    };
  },
  methods: {
    // “绘制”按钮的方法
    async draw() {
      if(this.form.monitorType === '' ||
          this.form.monitorContent === '' ||
          this.form.monitorLocation === '' ||
          this.form.sensorId === '' ||
          this.form.timerange.length === 0 ||
          this.form.timerange[0] === ''
      ){
        this.$message({
          type: 'warning',
          message: "查询数据前请注意查询条件的完整性"
        });
        return;
      }
      if(this.analysisType.length === 0){
        this.$message({
          type: 'warning',
          message: "统计方式不能为空"
        });
        return;
      }
      // 如果具体监测类型没选则自动全选
      if(this.specificMonitorContentIds.length === 0){
        this.specificMonitorTypeList.forEach((item)=>{
          this.specificMonitorContentIds.push(item)
        })
      }
      this.loading = true
      const sensorCode = this.specificMonitorContentIds.map(item => item.specificMonitorTypeId)
      await getDataForSpecificTypeAndAnalysisType({
        dataCode: this.form.sensorId,
        startTime: this.form.timerange[0],
        structureCode: this.currentStructure.structureCode,
        endTime: this.form.timerange[1],
        granularityNum: this.granularityNum,
        granularityType: this.granularityType.index,
        analysisTypes: this.analysisType ? this.analysisType.join(',') : null,
        sensorCode: sensorCode? sensorCode.join(',') : null,
      }).then(async (res) => {
        this.loading = false
        if (res.data.length === 0 || this.isNone(res.data)) {
          this.setEmptyChart("没有数据");
        } else {
          this.setRealChart(res.data)
        }
      }).catch((err) => {
        console.log(err)
        this.loading = false
      });
      this.loading = false
    },
    // 判断数据是否全部为空
    isNone(data) {
      let flag = false;
      data.forEach((datai) => {
        datai.forEach((dataj)=>{
          if(dataj.values.length !==0){
            flag = true;
          }
        })
      });
      return !flag;
    },
    //绘制实时数据 带预警值线
    setRealChart(data) {
      let myChart = echarts.getInstanceByDom(
          document.getElementById("myChart7")
      );
      if (myChart !== undefined) {
        myChart.dispose();
      }
      myChart = echarts.init(document.getElementById("myChart7"));
      // 修改数据格式 维度转换
      let drawData = []
      for(let i = 0 ; i < this.specificMonitorContentIds.length; i++){
        drawData.push([])
      }
      data.forEach((datai, analysisTypeIndex) => {
        datai.forEach((dataj, specificTypeIndex) => {
          let tmpDotList = [];
          dataj.times.forEach((value, index) => {
            tmpDotList.push(
                [dataj.times[index], dataj.values[index]]
            )
          })
          dataj.name = dataj.name + this.analysisTypeList[this.analysisType[analysisTypeIndex]].label
          dataj.dot = tmpDotList
          drawData[specificTypeIndex].push(dataj)
        })
      });
      let formatterNum = this.tabNum === "1" ? 4 : this.formatterNum
      let option = {
        toolbox: {
          right: 0,
          top: 0,
          feature: {
            saveAsImage: {
              title: '保存'
            },
          },
        },
        tooltip: {
          textStyle:{
            align:'left'
          },
          trigger: "axis",
          confine: true
        },
        xAxis: {
          type: 'category',
          name: "时间",
          nameLocation: "middle",
          nameGap: 32,
          axisLabel: {
            formatter: function (value, index) {
              return value.substring(0, value.length - formatterNum);
            },
            fontSize: 11,
          },
        },
        yAxis: [],
        dataZoom: [
          {
            type: "inside",
          },
          {
            type: 'slider',
            xAxisIndex: 0,
            minSpan: 5,
            height: 20,
            bottom: 5,
          }
        ],
        grid: [
          {
            top: "10%",
            left: "2%",
            right: 7 * (drawData.length > 2 ? drawData.length - 2 : 1) + "%",
            bottom: "10%",
            containLabel: true,
          },
        ],
        series: [],
        legend: {
          type: 'scroll',
          data: [],
          selected: {},
        },
      };
      //加入多行数据
      for(let i = 0 ; i < drawData.length; i++){
        let finalMax = 0;
        let finalMin = 0;
        for(let j = 0 ; j < drawData[i].length; j++){
          option.legend.data.push(drawData[i][j].name);
          option.series.push({
            type: "line",
            name: drawData[i][j].name,
            showSymbol: false,
            data: drawData[i][j].dot,
            yAxisIndex: i,
            markPoint: {
              data: [
                { type: 'max', name: 'Max' },
                { type: 'min', name: 'Min' }
              ]
            },
          });
          //计算最大值与最小值
          let minValue = getMin(drawData[i][j].values); // 输出最小值
          let maxValue = getMax(drawData[i][j].values); // 输出最大值
          // 一些特殊情况处理
          if (maxValue === 0 && minValue === 0) {
            maxValue = 1
            minValue = -1
          } else {
            let delta = Math.ceil((maxValue - minValue) * 0.2 * (10 ** drawData[i][j].accuracy))/(10 ** drawData[i][j].accuracy)
            if(delta === 0){
              delta = 1
            }
            let midValue = Math.ceil((maxValue + minValue) / 2 * (10 ** drawData[i][j].accuracy))/(10 ** drawData[i][j].accuracy)
            maxValue = midValue + 3 * delta
            minValue = midValue - 3 * delta
          }
          // 外层循环最大最小值更新
          if(j === 0){
            finalMax = maxValue
            finalMin = minValue
          }else {
            if(finalMax < maxValue){
              finalMax = maxValue
            }
            if(finalMin > minValue){
              finalMin = minValue
            }
          }
        }
        // 一些预警线
        // 画预警线
        let limitInfoList = [];
        let markLineDataList= []
        if (drawData[i][0].limitInfo && this.isShowLimit) {
          option.legend.data.push("预警线")
          option.series.push({
            type: "line",
            name: "预警线",
            showSymbol: false,
            yAxisIndex: i,
            animation: false,
            markLine:{
              symbol: 'none',
              data: markLineDataList,
              lineStyle:{
                color: "red"
              }
            },
            itemStyle:{
              color: "red"
            }
          });
          Object.keys(drawData[i][0].limitInfo).forEach((key) => {
            markLineDataList.push({
              yAxis: drawData[i][0].limitInfo[key],
              lineStyle: {
                type: 'solid'
              },
              emphasis: {
                label: {
                  position: 'outside',
                  formatter: drawData[i][0].name + key + drawData[i][0].limitInfo[key],
                  show: true,
                },
              },
              label: {
                show: false,
              },
            })
            limitInfoList.push(drawData[i][0].limitInfo[key])
          })
        }
        limitInfoList.push(finalMin)
        limitInfoList.push(finalMax)
        finalMin = getMin(limitInfoList)
        finalMax = getMax(limitInfoList)

        // 一些特殊情况处理
        if (finalMax === 0 && finalMin === 0) {
          finalMax = 1
          finalMin = -1
        } else {
          let delta = Math.ceil((finalMax - finalMin) * 0.2 * (10 ** drawData[i][0].accuracy))/(10 ** drawData[i][0].accuracy)
          if(delta === 0){
            delta = 1
          }
          let midValue = Math.ceil((finalMax + finalMin) / 2 * (10 ** drawData[i][0].accuracy))/(10 ** drawData[i][0].accuracy)
          finalMax = midValue + 3 * delta
          finalMin = midValue - 3 * delta
        }

        // 如果超过3列数据，则多余的y轴需要偏移
        if (i > 1) {
          option.yAxis.push({
            name: this.specificMonitorContentIds[i].specificMonitorTypeName + "/" + drawData[i][0].unit,
            nameTextStyle: {
              fontWeight: "bold",
              fontSize: 12,
            },
            axisLine: {
              show: true,
              lineStyle: {
                width: '1'//坐标线的宽度
              },
              onZero: false,
            },
            axisLabel: {
              formatter: function (value) {
                return value.toFixed(drawData[i][0].accuracy >= 0  ? drawData[i][0].accuracy : 4);
              },
            },
            splitNumber: 6,
            min: finalMin,
            max: finalMax,
            interval: (finalMax - finalMin) / 6, // 标轴分割间隔
            offset: 120 * (i - 1)
          });
        } else {
          option.yAxis.push({
            name: this.specificMonitorContentIds[i].specificMonitorTypeName + "/" + drawData[i][0].unit,
            nameTextStyle: {
              fontWeight: "bold",
              fontSize: 12
            },
            axisLabel: {
              formatter: function (value) {
                return value.toFixed(drawData[i][0].accuracy >= 0  ? drawData[i][0].accuracy : 4); // 2表示小数为2位
              },
            },
            splitNumber: 6,
            min: finalMin,
            max: finalMax,
            interval: (finalMax - finalMin) / 6, // 标轴分割间隔
          })
        }
      }
      option && myChart.setOption(option);
      window.onresize = function () {
        myChart.resize();
      };
    },
    //绘制空表 提示信息
    setEmptyChart(msg) {
      let myChart = echarts.getInstanceByDom(
          document.getElementById("myChart7")
      );
      if (myChart !== undefined) {
        myChart.dispose();
      }
      myChart = echarts.init(document.getElementById("myChart7"));
      let option = {
        title: {
          text: msg,
          left:'center',
        },
      };
      option && myChart.setOption(option);
      //自适应大小
      window.onresize = function () {
        myChart.resize();
      };
    },
    // 下面是一系列下拉框的change方法
    async changeMonitorType(e) {
      this.form.monitorType = e.content
      this.monitorContentList.splice(0)
      this.monitorLocationList.splice(0)
      this.sensorInfoList.splice(0)
      this.form.monitorContent = '';
      this.form.monitorLocation = '';
      this.form.sensorId = '';
      this.specificMonitorContentIds.splice(0)
      this.specificMonitorTypeList.splice(0)
      const monitorContentList = e.children;
      for (const monitorContent of monitorContentList) {
        this.monitorContentList.push(monitorContent)
      }
    },

    async changeMonitorContent(e) {
      this.form.monitorContent = e.content
      this.monitorLocationList.splice(0)
      this.sensorInfoList.splice(0)
      this.form.monitorLocation = '';
      this.form.sensorId = '';
      this.specificMonitorContentIds.splice(0)
      this.specificMonitorTypeList.splice(0)
      const monitorLocationList = e.children
      for (const monitorLocation of monitorLocationList) {
        this.monitorLocationList.push(monitorLocation)
      }
    },

    async changeMonitorLocation(e) {
      this.form.monitorLocation = e.content
      this.sensorInfoList.splice(0)
      this.form.sensorId = '';
      this.specificMonitorContentIds.splice(0)
      this.specificMonitorTypeList.splice(0)
      const ref = await getSensorByDataCode({ dataCode: e.code })
      const sensorInfoList = ref.data
      // const sensorInfoList = await this.procRequest(getSensorByDataCode, { dataCode: e.code })
      for (const sensorInfo of sensorInfoList) {
        this.sensorInfoList.push({
          code: sensorInfo.code,
          sensorId: sensorInfo.sensorId,
          sensorInstallCode: sensorInfo.dataCode,
          specificMonitorTypeId: sensorInfo.children.map(item => item.sensorCode),
          specificMonitorTypeName: sensorInfo.children.map(item => item.typeName),
        })
      }
    },

    async changeSensor(e) {
      this.form.sensorId = e.sensorInstallCode
      this.sensorInfo = e

      // 更新具体监测类型列表
      this.specificMonitorContentIds.splice(0)
      this.specificMonitorTypeList.splice(0)
      e.specificMonitorTypeId.forEach((value, index)=>{
        this.specificMonitorTypeList.push({
          specificMonitorTypeName: e.specificMonitorTypeName[index],
          specificMonitorTypeId: e.specificMonitorTypeId[index],
        })
      })
    },
    // 时间范围下拉框的change事件
    statusDateChange(e){
      this.form.timerange = e;
    },

    // 改变统计周期时，变更绘图formatter
    granularityTypeChange(granularityType){
      if(granularityType.index < 5){
        this.formatterNum = 7;
      }else if(granularityType.index < 8){
        this.formatterNum = 12;
      }else{
        this.formatterNum = 12;
      }
      if(granularityType.index === 3){
        this.granularityNum = 10;
      }else {
        this.granularityNum = 1;
      }
      // console.log(this.formatter)
    },
    // 清空表单
    resetThisForm(){
      this.monitorContentList.splice(0)
      this.monitorLocationList.splice(0)
      this.sensorInfoList.splice(0)
      this.form.monitorType = '';
      this.form.monitorContent = '';
      this.form.monitorLocation = '';
      this.form.sensorId = '';
      this.specificMonitorContentIds.splice(0)
      this.specificMonitorTypeList.splice(0)
    }
  }
}

</script>

<style scoped>
.el-header{
  height: auto !important;
  padding: 0;
  color: #333;
  border-bottom: 1px solid #eee;
}
.el-form-item {
  display: inline-block;
  height: 40px;
  margin: 5px 0 5px 5px;
}
.el-form {
  text-align: left;
}
.el-button {
  margin-left: 5px;
}
/deep/.ivu-input {
  font-size: 14px !important;
}
/deep/.el-input__inner {
  padding: 0 0 0 10px !important;
}
.el-main{
  padding: 0;
  text-align: center;
}
/deep/.tags-select-input {
  .el-select__tags {
    white-space: nowrap;
    overflow: hidden;
    flex-wrap: nowrap !important;
  }
}

/deep/.el-select__tags-text {
  display: inline-block;
  max-width: 75px;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

</style>
