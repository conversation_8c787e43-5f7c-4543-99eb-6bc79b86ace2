<template>
  <div class="app-container">

    <!-- 查询 -->
    <div class="searchBox" :style="{ 'height': queryShow ? '86px' : '48px' }">
      <el-row :gutter="12">
        <el-col :span="24" style="display: flex; align-items: center">
          <CascadeSelection
            style="min-width: 192px;"
            :form-data="queryParams"
            v-model="queryParams"
            @update:fromData="queryParamsUpdate"
            types="201"
            multiple
          />
          <RangeInput
            :clearData="clearData"
            @startValue="(v) => {queryParams.pileStartNum = v}"
            @endValue="(v) => {queryParams.pileEndNum = v}"
          />
          <el-button type="primary" icon="el-icon-search" size="mini" @click="queryhandle">搜索</el-button>
          <el-button icon="el-icon-refresh" size="mini" @click="queryReset">重置</el-button>
          <el-button
            :icon="queryShow ? 'el-icon-arrow-up' : 'el-icon-arrow-down'"
            style="color: #1890ff;border-color: #badeff;background-color: #e8f4ff;"
            circle
            @click="queryShow = !queryShow"
          />
        </el-col>
      </el-row>
      <transition name="search">
        <div class="searchMoreBox" v-if="queryShow">
          <el-row :gutter="20" style="width: 852px;">
            <el-col :span="6" :offset="0">
              <el-select v-model="queryParams.disasterType" placeholder="灾毁类型" clearable>
                <el-option v-for="dict in dict.type.disaster_risk_type" :key="dict.value" :label="dict.label"
                           :value="dict.label"/>
              </el-select>
            </el-col>
            <el-col :span="6" :offset="0">
              <el-input v-model="queryParams.disasterSectionId" placeholder="灾毁段编号" clearable
                        @keyup.enter.native="queryhandle"/>
            </el-col>
            <el-col :span="12" :offset="0">
              <el-date-picker
                v-model="queryTime"
                @change="() => {queryParams.startTime = queryTime[0] + ' 00:00:00'; queryParams.endTime = queryTime[1] + ' 23:59:59'}"
                type="daterange"
                style="width: 100%;"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                format="yyyy-MM-dd"
                value-format="yyyy-MM-dd"
              >
              </el-date-picker>
            </el-col>
          </el-row>
          <el-row :gutter="20" style="width: 426px; margin-left: 10px">
            <el-col :span="12" :offset="0">
              <el-select v-model="queryParams.status" placeholder="请选择审核状态" clearable>
                <el-option label="待提交" value="0"></el-option>
                <el-option label="审核中" value="1"></el-option>
                <el-option label="已审核" value="2"></el-option>
                <el-option label="已撤销" value="3"></el-option>
                <el-option label="已驳回" value="4"></el-option>
              </el-select>
            </el-col>
          </el-row>
        </div>
      </transition>
    </div>

    <!-- 主表数据 -->
    <div class="tableDiv" :style="{ 'height': queryShow ? 'calc(100% - 96px)' : 'calc(100% - 58px)' }">
      <!-- 功能按钮 -->
      <div class="btnBox">
        <el-row :gutter="10">
          <el-col :span="1.5" v-hasPermi="['disaster:submitted:add']">
            <el-button type="primary" icon="el-icon-plus" size="mini" @click="formInit('add')">新增
            </el-button>
          </el-col>
          <el-col :span="1.5" v-hasPermi="['disaster:submitted:edit']">
            <el-button type="success" icon="el-icon-edit" size="mini" :disabled="tableEditDisabled"
                       @click="formEdit('select')">修改
            </el-button>
          </el-col>
          <el-col :span="1.5" v-hasPermi="['disaster:submitted:remove']">
            <el-button type="danger" icon="el-icon-delete" size="mini" :disabled="tableDelDisabled"
                       @click="formDelete('batch')">删除
            </el-button>
          </el-col>
          <el-col :span="1.5" v-if="$store.getters.name === 'admin'">
            <el-button type="primary" icon="el-icon-refresh-left" size="mini" @click="formRevoke('batch')">
              撤销
            </el-button>
          </el-col>
          <el-col :span="1.5" v-hasPermi="['disaster:submitted:export']">
            <el-button type="warning" icon="el-icon-download" size="mini" @click="formExport">导出
            </el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="warning" icon="el-icon-download" v-if="$store.getters.name === 'admin'" size="mini"
                       @click="expCard">导出卡片
            </el-button>
          </el-col>
        </el-row>
      </div>

      <!-- 数据表格 -->
      <el-table v-adjust-table size="mini" height="calc(100% - 98px)" border ref="tableRef"
                v-loading="tableLoading" :data="tableData" @selection-change="tableSelectionChange"
                @row-click="tableRowClick">
        <el-table-column type="selection" width="55" align="center"/>
        <el-table-column label="序号" fixed align="center" type="index" width="50"></el-table-column>
        <el-table-column label="灾毁类型" fixed :show-overflow-tooltip="true" width="120" align="center"
                         prop="disasterType"/>
        <el-table-column label="灾毁段编号" :show-overflow-tooltip="true" width="250" align="center"
                         prop="disasterSectionId"/>
        <el-table-column label="管养单位" :show-overflow-tooltip="true" width="120" align="center"
                         prop="maintenanceUnitName"/>
        <el-table-column label="路段名称" :show-overflow-tooltip="true" width="120" align="center"
                         prop="roadSectionName"/>
        <el-table-column label="路线名称" :show-overflow-tooltip="true" width="220" align="center" prop="routerName"/>
        <el-table-column label="技术等级" :show-overflow-tooltip="true" width="150" align="center"
                         prop="technicalGrade">
          <template slot-scope="scope">
            <dict-tag :options="dict.type.sys_route_grade" :value="scope.row.technicalGrade"/>
          </template>
        </el-table-column>
        <el-table-column label="填报人" :show-overflow-tooltip="true" width="120" align="center" prop="createName"/>
        <el-table-column label="填报单位" :show-overflow-tooltip="true" width="250" align="center" prop="createUnit"/>
        <el-table-column label="填报时间" :show-overflow-tooltip="true" width="180" align="center" prop="createTime"/>
        <el-table-column label="状态" :show-overflow-tooltip="true" width="120" align="center" prop="status">
          <template slot-scope="scope">
            <el-tag v-if="scope.row.status === '0'" type="info">待提交</el-tag>
            <el-tag v-if="scope.row.status === '1'" type="warning">审核中</el-tag>
            <el-tag v-if="scope.row.status === '2'" type="success">已审核</el-tag>
            <el-tag v-if="scope.row.status === '3'">已撤销</el-tag>
            <el-tag v-if="scope.row.status === '4'" type="danger">已驳回</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="250" align="center" class-name="small-padding fixed-width" fixed="right">
          <template slot-scope="scope">
            <el-button v-if="scope.row.status !== '1' && scope.row.status !== '2' && scope.row.status !== '4'" size="mini" type="text"
                       icon="el-icon-edit" @click="formEdit('click', scope.row)" v-hasPermi="['disaster:highway:edit']">
              修改
            </el-button>
            <el-button v-if="scope.row.status === '0' || scope.row.status === '3'" size="mini" type="text"
                       icon="el-icon-delete" @click="formDelete('single', scope.row)"
                       v-hasPermi="['disaster:highway:edit']">删除
            </el-button>
            <el-button size="mini" type="text" icon="el-icon-view" @click="formView(scope.row)">查看</el-button>
            <el-button v-if="scope.row.status === '1' || scope.row.status === '4'" key="submit" size="mini" type="text"
                       icon="el-icon-share"
                       :disabled="isButtonDisabled(scope.row.id)"
                       @click="formAudit(scope.row)">复核
            </el-button>
            <el-button v-if="scope.row.status === '1' || scope.row.status === '2'" size="mini" type="text"
                       icon="el-icon-view"
                       @click="formAuditInfo(scope.row)">审核信息
            </el-button>
            <el-button v-if="scope.row.status === '1' && $store.getters.name === 'admin'"
                       v-hasPermi="['disaster:disaster:edit']" size="mini" type="text"
                       icon="el-icon-refresh-left" @click="formRevoke('single', scope.row)">撤销
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <pagination :total="queryTotal" :page.sync="queryParams.pageNum"
                  :limit.sync="queryParams.pageSize" @pagination="queryList" style="margin-right: 10px;"/>

    </div>

    <!-- 表单 -->
    <el-dialog :visible.sync="formDialog" width="70%" max-height="50%" :close-on-press-escape="false"
               :close-on-click-modal="false" append-to-body class="formDialog">
      <template slot="title">
        <div class="titleBox">
          <div class="title">{{ formTitle }}</div>
          <div class="subTitle" v-if="formParams.id">ID：{{ formParams.id }}</div>
        </div>
      </template>
      <el-form ref="formRef" :model="formParams" :rules="formRules" :disabled="formType === 'view'"
               label-width="140px" label-position="right">
        <div class="infoBox" style="margin-bottom: 20px;" v-loading="formLoading">
          <div class="infoTitle">
            基础信息
          </div>

          <el-row :gutter="12">
            <el-col :span="12">
              <el-form-item label="灾毁类型" prop="disasterType">
                <el-select v-model="formParams.disasterType" placeholder="请选择灾毁类型"
                           @change="disasterTypeSelectHandle" style="width: 100%;">
                  <el-option v-for="dict in dict.type.disaster_risk_type" :label="dict.label" :value="dict.label"
                             :key="dict.value"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="关联风险点" prop="disasterRiskId">
                <div style="width: 100%; display: flex; flex-direction: row; flex-wrap: nowrap;">
                  <el-input v-model="formParams.disasterRiskId" disabled
                            placeholder="点击右侧查询数据进行关联"></el-input>
                  <el-button type="primary" style="margin-left: 10px;" @click="openRiskSearchDialog">查询</el-button>
                </div>
              </el-form-item>
            </el-col>
          </el-row>

        </div>

        <div class="infoBox" style="margin-bottom: 20px;" v-loading="riskFormLoading">
          <div class="infoTitle">
            关联风险点基础信息
          </div>

          <el-row :gutter="12">
            <el-col :span="12">
              <el-form-item label="灾害名称" prop="disasterName">
                <el-input v-model="riskFormParams.disasterName" disabled placeholder="关联风险点后显示"/>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="灾害编号" prop="disasterNum">
                <el-input v-model="riskFormParams.disasterNum" disabled placeholder="关联风险点后显示"/>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="12">
            <el-col :span="12">
              <el-form-item label="管养单位名称" prop="maintenanceUnitId">
                <el-select v-model="riskFormParams.maintenanceUnitId" disabled placeholder="关联风险点后显示"
                           style="width: 100%;">
                  <el-option v-for="item in formMaintenanceList" :label="item.label" :value="item.id"
                             :key="item.id"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="技术等级" prop="technicalGrade">
                <el-select disabled v-model="riskFormParams.technicalGrade" disabled placeholder="关联风险点后显示"
                           style="width: 100%;">
                  <el-option v-for="dict in dict.type.sys_route_grade" :label="dict.label" :value="dict.value"
                             :key="dict.value"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="12">
            <el-col :span="12">
              <el-form-item label="路线名称" prop="routerName">
                <el-input v-model="riskFormParams.routerName" disabled placeholder="关联风险点后显示"/>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="路线编号" prop="routerNum">
                <el-input v-model="riskFormParams.routerNum" disabled placeholder="关联风险点后显示"/>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="12">
            <el-col :span="12">
              <el-form-item label="起点经纬度" prop="startLatitudeAndLongitude">
                <div style="width: 100%; display: flex; flex-direction: row; flex-wrap: nowrap;">
                  <el-input v-model="riskFormParams.startLatitudeAndLongitude" disabled
                            placeholder="请输入起点经纬度，以英文逗号分隔"></el-input>
                  <el-button slot="append" icon="el-icon-location" @click="openCoordinateDialog('start')">查看坐标
                  </el-button>
                </div>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="止点经纬度" prop="endLatitudeAndLongitude">
                <div style="width: 100%; display: flex; flex-direction: row; flex-wrap: nowrap;">
                  <el-input v-model="riskFormParams.endLatitudeAndLongitude" disabled
                            placeholder="请输入止点经纬度，以英文逗号分隔"></el-input>
                  <el-button icon="el-icon-location" @click="openCoordinateDialog('end')">查看坐标</el-button>
                </div>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="12">
            <el-col :span="12">
              <el-form-item label="起点桩号" prop="pileStartNum">
                <el-input v-model="riskFormParams.pileStartNum" disabled placeholder="关联风险点后显示"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="终点桩号" prop="pileEndNum">
                <el-input v-model="riskFormParams.pileEndNum" disabled placeholder="关联风险点后显示"></el-input>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="12">
            <el-col :span="12">
              <el-form-item label="风险评分" prop="riskScore">
                <el-input v-model="riskFormParams.riskScore" disabled placeholder="关联风险点后显示"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="风险等级" prop="riskGrade">
                <el-input v-model="riskFormParams.riskGrade" disabled placeholder="关联风险点后显示"></el-input>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="12">
            <el-col :span="12">
              <el-form-item label="采集人" prop="createName">
                <el-input v-model="riskFormParams.createName" disabled placeholder="关联风险点后显示"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="采集时间" prop="createTime">
                <el-input v-model="riskFormParams.createTime" disabled placeholder="关联风险点后显示"></el-input>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="12">
            <el-col :span="24">
              <el-form-item label="">
                <el-button type="primary" @click="queryRiskDetail">查看关联风险点完整信息</el-button>
              </el-form-item>
            </el-col>
          </el-row>

        </div>

        <div class="infoBox" style="margin-bottom: 20px;" v-loading="formLoading">
          <div class="infoTitle">
            灾毁基础信息
          </div>

          <el-row :gutter="12">
            <el-col :span="12">
              <el-form-item prop="disasterSectionId">
                <span slot="label">
                  <el-tooltip :content="`灾毁段编号，按'选择的灾毁编号-填报年月日'自动生成`" placement="top">
                    <i class="el-icon-question"></i>
                  </el-tooltip>
                  灾毁段编号
                </span>
                <el-input v-model="formParams.disasterSectionId" disabled placeholder="保存后自动生成"/>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="12">
            <el-col :span="12">
              <el-form-item label="起点经纬度" prop="startLatitudeAndLongitude">
                <el-input v-model="formParams.startLatitudeAndLongitude" disabled placeholder="关联风险点后自动生成"/>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="止点经纬度" prop="endLatitudeAndLongitude">
                <el-input v-model="formParams.endLatitudeAndLongitude" disabled placeholder="关联风险点后自动生成"/>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="12">
            <el-col :span="12">
              <el-form-item label="起点桩号" prop="pileStartNum">
                <el-input v-model="formParams.pileStartNum" disabled placeholder="关联风险点后自动生成"/>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="止点桩号" prop="pileEndNum">
                <el-input v-model="formParams.pileEndNum" disabled placeholder="关联风险点后自动生成"/>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="12">
            <el-col :span="12">
              <el-form-item label="路面宽度" prop="roadbedWidth">
                <div style="width: 100%; display: flex; align-items: center; flex-direction: row; flex-wrap: nowrap;">
                  <el-input-number v-model="formParams.roadbedWidth" controls-position="right" :min="0" :precision="2"
                                   style="width: 100%;" placeholder="请输入路面宽度"/>
                  <el-tag size="small" type="info" effect="light">（米）</el-tag>
                </div>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="路面类型" prop="roadbedType">
                <el-select v-model="formParams.roadbedType" placeholder="请选择路面类型" style="width: 100%;">
                  <el-option v-for="dict in dict.type.damage_surface_type" :label="dict.label" :value="dict.value"
                             :key="dict.value"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>

        </div>

        <div class="infoBox" style="margin-bottom: 20px;" v-loading="formLoading">
          <div class="infoTitle">
            灾毁损失情况
          </div>

          <el-row :gutter="12">
            <el-col :span="12">
              <el-form-item label="是否中断" prop="interrupt">
                <el-radio-group v-model="formParams.interrupt" ref="subFormRadio1">
                  <el-radio :label="'0'">是</el-radio>
                  <el-radio :label="'1'">否</el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="灾毁发生时间" prop="disasterStartTime">
                <el-date-picker v-model="formParams.disasterStartTime" style="width:100%" type="datetime"
                                placeholder="灾毁发生时间" clearable value-format="yyyy-MM-dd HH:mm"
                                format="yyyy-MM-dd HH:mm"/>
              </el-form-item>
            </el-col>
          </el-row>

          <div class="infoBox" style="margin-bottom: 20px;" v-loading="formLoading">
            <div class="infoTitle">
              灾毁设施类型
            </div>

            <el-row :gutter="12">
              <el-col :span="24">
                <el-form-item label="灾毁设施类型" prop="disasterFacilitiesType">
                  <el-checkbox-group v-model="formParams.disasterFacilitiesType" @change="formFacilitiesChange">
                    <el-checkbox v-for="dict in dict.type.damage_facility_type" :label="dict.value" :key="dict.value">
                      {{ dict.label }}
                    </el-checkbox>
                  </el-checkbox-group>
                </el-form-item>
              </el-col>
            </el-row>

            <div class="infoBox" style="margin-bottom: 20px;" v-loading="formLoading"
                 v-show="formParams.disasterFacilitiesType.includes('1')">
              <div class="infoTitle">
                路基
              </div>

              <el-row :gutter="12">
                <el-col :span="12">
                  <el-form-item label="损失路基方量" prop="damagedRoadbedVolume">
                    <div
                      style="width: 100%; display: flex; align-items: center; flex-direction: row; flex-wrap: nowrap;">
                      <el-input-number v-model="formParams.damagedRoadbedVolume" controls-position="right" :min="0"
                                       :precision="0" style="width: 100%;" placeholder="请输入损失路基方量"/>
                      <el-tag size="small" type="info" effect="light">（立方米）</el-tag>
                    </div>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="损毁路基长度" prop="damagedRoadbedLength">
                    <div
                      style="width: 100%; display: flex; align-items: center; flex-direction: row; flex-wrap: nowrap;">
                      <el-input-number v-model="formParams.damagedRoadbedLength" controls-position="right" :min="0"
                                       :precision="0" style="width: 100%;" placeholder="请输入损毁路基长度"/>
                      <el-tag size="small" type="info" effect="light">（米）</el-tag>
                    </div>
                  </el-form-item>
                </el-col>
              </el-row>

              <el-row :gutter="12">
                <el-col :span="12">
                  <el-form-item label="损失全额" prop="damagedFullAmount">
                    <div
                      style="width: 100%; display: flex; align-items: center; flex-direction: row; flex-wrap: nowrap;">
                      <el-input-number v-model="formParams.damagedFullAmount" controls-position="right"
                                       @change="formtotalCompute" :min="0" :precision="2" style="width: 100%;"
                                       placeholder="请输入损失路基方量"/>
                      <el-tag size="small" type="info" effect="light">（万元）</el-tag>
                    </div>
                  </el-form-item>
                </el-col>
              </el-row>

            </div>

            <div class="infoBox" style="margin-bottom: 20px;" v-loading="formLoading"
                 v-show="formParams.disasterFacilitiesType.includes('2')">
              <div class="infoTitle">
                路面
              </div>

              <el-row :gutter="12">
                <el-col :span="24">
                  <el-form-item label-width="0">
                    <el-checkbox v-model="asphaltSelect" style="pointer-events: none;">沥青路面</el-checkbox>
                    <el-checkbox v-model="cementSelect" style="pointer-events: none;">水泥路面</el-checkbox>
                    <el-checkbox v-model="dinasSelect" style="pointer-events: none;">砂石路面</el-checkbox>
                  </el-form-item>
                </el-col>
              </el-row>

              <el-divider></el-divider>

              <el-row :gutter="12">
                <el-col :span="2">
                  <el-form-item label-width="0">
                    <p style="font-weight: bold; margin: 0;">沥青路面 ></p>
                  </el-form-item>
                </el-col>
                <el-col :span="7">
                  <el-form-item label="损毁路面面积" prop="asphaltDamagedRoadbedArea">
                    <div
                      style="width: 100%; display: flex; align-items: center; flex-direction: row; flex-wrap: nowrap;">
                      <el-input-number v-model="formParams.asphaltDamagedRoadbedArea" controls-position="right" :min="0"
                                       :precision="0" style="width: 100%;" placeholder="请输入损毁路面面积"/>
                      <el-tag size="small" type="info" effect="light">（平方米）</el-tag>
                    </div>
                  </el-form-item>
                </el-col>
                <el-col :span="7">
                  <el-form-item label="损毁路面长度" prop="asphaltDamagedRoadbedLength">
                    <div
                      style="width: 100%; display: flex; align-items: center; flex-direction: row; flex-wrap: nowrap;">
                      <el-input-number v-model="formParams.asphaltDamagedRoadbedLength" controls-position="right"
                                       :min="0" :precision="0" style="width: 100%;" placeholder="请输入损毁路面长度"/>
                      <el-tag size="small" type="info" effect="light">（米）</el-tag>
                    </div>
                  </el-form-item>
                </el-col>
                <el-col :span="7">
                  <el-form-item label="损失全额" prop="asphaltDamagedFullAmount">
                    <div
                      style="width: 100%; display: flex; align-items: center; flex-direction: row; flex-wrap: nowrap;">
                      <el-input-number v-model="formParams.asphaltDamagedFullAmount" controls-position="right"
                                       @change="formtotalCompute" :min="0" :precision="2" style="width: 100%;"
                                       placeholder="请输入损失全额"/>
                      <el-tag size="small" type="info" effect="light">（万元）</el-tag>
                    </div>
                  </el-form-item>
                </el-col>
              </el-row>

              <el-divider></el-divider>

              <el-row :gutter="12">
                <el-col :span="2">
                  <el-form-item label-width="0">
                    <p style="font-weight: bold; margin: 0;">水泥路面 ></p>
                  </el-form-item>
                </el-col>
                <el-col :span="7">
                  <el-form-item label="损毁路面面积" prop="cementDamagedRoadbedArea">
                    <div
                      style="width: 100%; display: flex; align-items: center; flex-direction: row; flex-wrap: nowrap;">
                      <el-input-number v-model="formParams.cementDamagedRoadbedArea" controls-position="right" :min="0"
                                       :precision="0" style="width: 100%;" placeholder="请输入损毁路面面积"/>
                      <el-tag size="small" type="info" effect="light">（平方米）</el-tag>
                    </div>
                  </el-form-item>
                </el-col>
                <el-col :span="7">
                  <el-form-item label="损毁路面长度" prop="cementDamagedRoadbedLength">
                    <div
                      style="width: 100%; display: flex; align-items: center; flex-direction: row; flex-wrap: nowrap;">
                      <el-input-number v-model="formParams.cementDamagedRoadbedLength" controls-position="right"
                                       :min="0" :precision="0" style="width: 100%;" placeholder="请输入损毁路面长度"/>
                      <el-tag size="small" type="info" effect="light">（米）</el-tag>
                    </div>
                  </el-form-item>
                </el-col>
                <el-col :span="7">
                  <el-form-item label="损失全额" prop="cementDamagedFullAmount">
                    <div
                      style="width: 100%; display: flex; align-items: center; flex-direction: row; flex-wrap: nowrap;">
                      <el-input-number v-model="formParams.cementDamagedFullAmount" controls-position="right"
                                       @change="formtotalCompute" :min="0" :precision="2" style="width: 100%;"
                                       placeholder="请输入损失全额"/>
                      <el-tag size="small" type="info" effect="light">（万元）</el-tag>
                    </div>
                  </el-form-item>
                </el-col>
              </el-row>

              <el-divider></el-divider>

              <el-row :gutter="12">
                <el-col :span="2">
                  <el-form-item label-width="0">
                    <p style="font-weight: bold; margin: 0;">砂石路面 ></p>
                  </el-form-item>
                </el-col>
                <el-col :span="7">
                  <el-form-item label="损毁路面面积" prop="dinasDamagedRoadbedArea">
                    <div
                      style="width: 100%; display: flex; align-items: center; flex-direction: row; flex-wrap: nowrap;">
                      <el-input-number v-model="formParams.dinasDamagedRoadbedArea" controls-position="right" :min="0"
                                       :precision="0" style="width: 100%;" placeholder="请输入损毁路面面积"/>
                      <el-tag size="small" type="info" effect="light">（平方米）</el-tag>
                    </div>
                  </el-form-item>
                </el-col>
                <el-col :span="7">
                  <el-form-item label="损毁路面长度" prop="dinasDamagedRoadbedLength">
                    <div
                      style="width: 100%; display: flex; align-items: center; flex-direction: row; flex-wrap: nowrap;">
                      <el-input-number v-model="formParams.dinasDamagedRoadbedLength" controls-position="right" :min="0"
                                       :precision="0" style="width: 100%;" placeholder="请输入损毁路面长度"/>
                      <el-tag size="small" type="info" effect="light">（米）</el-tag>
                    </div>
                  </el-form-item>
                </el-col>
                <el-col :span="7">
                  <el-form-item label="损失全额" prop="dinasDamagedFullAmount">
                    <div
                      style="width: 100%; display: flex; align-items: center; flex-direction: row; flex-wrap: nowrap;">
                      <el-input-number v-model="formParams.dinasDamagedFullAmount" controls-position="right"
                                       @change="formtotalCompute" :min="0" :precision="2" style="width: 100%;"
                                       placeholder="请输入损失全额"/>
                      <el-tag size="small" type="info" effect="light">（万元）</el-tag>
                    </div>
                  </el-form-item>
                </el-col>
              </el-row>

            </div>

            <div class="infoBox" style="margin-bottom: 20px;" v-loading="formLoading"
                 v-show="formParams.disasterFacilitiesType.includes('3')">
              <div class="infoTitle">
                桥梁
              </div>

              <el-row :gutter="12">
                <el-col :span="24">
                  <el-form-item label="毁坏类型" prop="bridgeDamagedType">
                    <el-radio-group v-model="formParams.bridgeDamagedType" ref="subFormRadio2">
                      <el-radio v-for="dict in dict.type.bridge_damage_type" :label="dict.value" :key="dict.value">
                        {{ dict.label }}
                      </el-radio>
                    </el-radio-group>
                  </el-form-item>
                </el-col>
              </el-row>

              <el-divider v-if="formParams.bridgeDamagedType"></el-divider>

              <el-row :gutter="12" v-show="formParams.bridgeDamagedType">
                <el-col :span="8">
                  <el-form-item label="损毁长度" prop="bridgeDamagedLength">
                    <div
                      style="width: 100%; display: flex; align-items: center; flex-direction: row; flex-wrap: nowrap;">
                      <el-input-number v-model="formParams.bridgeDamagedLength" controls-position="right" :min="0"
                                       :precision="2" style="width: 100%;" placeholder="请输入损毁长度"/>
                      <el-tag size="small" type="info" effect="light">（延米）</el-tag>
                    </div>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="损毁数量" prop="bridgeDamagedNumber">
                    <div
                      style="width: 100%; display: flex; align-items: center; flex-direction: row; flex-wrap: nowrap;">
                      <el-input-number v-model="formParams.bridgeDamagedNumber" controls-position="right" :min="0"
                                       :precision="0" style="width: 100%;" placeholder="请输入损毁数量"/>
                      <el-tag size="small" type="info" effect="light">（座）</el-tag>
                    </div>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="损毁全额" prop="bridgeDamagedFullAmount">
                    <div
                      style="width: 100%; display: flex; align-items: center; flex-direction: row; flex-wrap: nowrap;">
                      <el-input-number v-model="formParams.bridgeDamagedFullAmount" controls-position="right"
                                       @change="formtotalCompute" :min="0" :precision="2" style="width: 100%;"
                                       placeholder="请输入损毁全额"/>
                      <el-tag size="small" type="info" effect="light">（万元）</el-tag>
                    </div>
                  </el-form-item>
                </el-col>
              </el-row>

            </div>

            <div class="infoBox" style="margin-bottom: 20px;" v-loading="formLoading"
                 v-show="formParams.disasterFacilitiesType.includes('4')">
              <div class="infoTitle">
                隧道
              </div>

              <el-row :gutter="12">
                <el-col :span="8">
                  <el-form-item label="损毁长度" prop="tunnelDamagedLength">
                    <div
                      style="width: 100%; display: flex; align-items: center; flex-direction: row; flex-wrap: nowrap;">
                      <el-input-number v-model="formParams.tunnelDamagedLength" controls-position="right" :min="0"
                                       :precision="0" style="width: 100%;" placeholder="请输入损毁长度"/>
                      <el-tag size="small" type="info" effect="light">（延米）</el-tag>
                    </div>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="损毁数量" prop="tunnelDamagedNumber">
                    <div
                      style="width: 100%; display: flex; align-items: center; flex-direction: row; flex-wrap: nowrap;">
                      <el-input-number v-model="formParams.tunnelDamagedNumber" controls-position="right" :min="0"
                                       :precision="0" style="width: 100%;" placeholder="请输入损毁数量"/>
                      <el-tag size="small" type="info" effect="light">（道）</el-tag>
                    </div>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="损毁全额" prop="tunnelDamagedFullAmount">
                    <div
                      style="width: 100%; display: flex; align-items: center; flex-direction: row; flex-wrap: nowrap;">
                      <el-input-number v-model="formParams.tunnelDamagedFullAmount" controls-position="right" :min="0"
                                       @change="formtotalCompute" :precision="2" style="width: 100%;"
                                       placeholder="请输入损毁全额"/>
                      <el-tag size="small" type="info" effect="light">（万元）</el-tag>
                    </div>
                  </el-form-item>
                </el-col>
              </el-row>
            </div>

            <div class="infoBox" style="margin-bottom: 20px;" v-loading="formLoading"
                 v-show="formParams.disasterFacilitiesType.includes('5')">
              <div class="infoTitle">
                涵洞
              </div>

              <el-row :gutter="12">
                <el-col :span="12">
                  <el-form-item label="损毁数量" prop="culvertDamagedNumber">
                    <div
                      style="width: 100%; display: flex; align-items: center; flex-direction: row; flex-wrap: nowrap;">
                      <el-input-number v-model="formParams.culvertDamagedNumber" controls-position="right" :min="0"
                                       :precision="0" style="width: 100%;" placeholder="请输入损毁数量"/>
                      <el-tag size="small" type="info" effect="light">（道）</el-tag>
                    </div>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="损毁全额" prop="culvertDamagedFullAmount">
                    <div
                      style="width: 100%; display: flex; align-items: center; flex-direction: row; flex-wrap: nowrap;">
                      <el-input-number v-model="formParams.culvertDamagedFullAmount" controls-position="right"
                                       @change="formtotalCompute" :min="0" :precision="2" style="width: 100%;"
                                       placeholder="请输入损毁全额"/>
                      <el-tag size="small" type="info" effect="light">（万元）</el-tag>
                    </div>
                  </el-form-item>
                </el-col>
              </el-row>
            </div>

            <div class="infoBox" style="margin-bottom: 20px;" v-loading="formLoading"
                 v-show="formParams.disasterFacilitiesType.includes('6')">
              <div class="infoTitle">
                防护工程
              </div>

              <el-row :gutter="12">
                <el-col :span="24">
                  <el-form-item label-width="0">
                    <el-checkbox v-model="revetmentSelect" style="pointer-events: none;">护坡</el-checkbox>
                    <el-checkbox v-model="barricadeSelect" style="pointer-events: none;">驳岸、挡墙</el-checkbox>
                  </el-form-item>
                </el-col>
              </el-row>

              <el-divider></el-divider>

              <el-row :gutter="12">
                <el-col :span="2">
                  <el-form-item label-width="0">
                    <p style="font-weight: bold; margin: 0;">护坡 ></p>
                  </el-form-item>
                </el-col>
                <el-col :span="7">
                  <el-form-item label="损毁方量" prop="revetmentDamagedVolume">
                    <div
                      style="width: 100%; display: flex; align-items: center; flex-direction: row; flex-wrap: nowrap;">
                      <el-input-number v-model="formParams.revetmentDamagedVolume" controls-position="right" :min="0"
                                       :precision="0" style="width: 100%;" placeholder="请输入损毁方量"/>
                      <el-tag size="small" type="info" effect="light">（立方米）</el-tag>
                    </div>
                  </el-form-item>
                </el-col>
                <el-col :span="7">
                  <el-form-item label="损毁数量" prop="revetmentDamagedNumber">
                    <div
                      style="width: 100%; display: flex; align-items: center; flex-direction: row; flex-wrap: nowrap;">
                      <el-input-number v-model="formParams.revetmentDamagedNumber" controls-position="right" :min="0"
                                       :precision="0" style="width: 100%;" placeholder="请输入损毁数量"/>
                      <el-tag size="small" type="info" effect="light">（处）</el-tag>
                    </div>
                  </el-form-item>
                </el-col>
                <el-col :span="7">
                  <el-form-item label="损毁全额" prop="revetmentDamagedFullAmount">
                    <div
                      style="width: 100%; display: flex; align-items: center; flex-direction: row; flex-wrap: nowrap;">
                      <el-input-number v-model="formParams.revetmentDamagedFullAmount" controls-position="right"
                                       @change="formtotalCompute" :min="0" :precision="2" style="width: 100%;"
                                       placeholder="请输入损毁全额"/>
                      <el-tag size="small" type="info" effect="light">（万元）</el-tag>
                    </div>
                  </el-form-item>
                </el-col>
              </el-row>

              <el-divider></el-divider>

              <el-row :gutter="12">
                <el-col :span="2">
                  <el-form-item label-width="0">
                    <p style="font-weight: bold; margin: 0;">驳岸、挡墙 ></p>
                  </el-form-item>
                </el-col>
                <el-col :span="7">
                  <el-form-item label="损毁方量" prop="barricadeDamagedVolume">
                    <div
                      style="width: 100%; display: flex; align-items: center; flex-direction: row; flex-wrap: nowrap;">
                      <el-input-number v-model="formParams.barricadeDamagedVolume" controls-position="right" :min="0"
                                       :precision="0" style="width: 100%;" placeholder="请输入损毁方量"/>
                      <el-tag size="small" type="info" effect="light">（立方米）</el-tag>
                    </div>
                  </el-form-item>
                </el-col>
                <el-col :span="7">
                  <el-form-item label="损毁数量" prop="barricadeDamagedNumber">
                    <div
                      style="width: 100%; display: flex; align-items: center; flex-direction: row; flex-wrap: nowrap;">
                      <el-input-number v-model="formParams.barricadeDamagedNumber" controls-position="right" :min="0"
                                       :precision="0" style="width: 100%;" placeholder="请输入损毁数量"/>
                      <el-tag size="small" type="info" effect="light">（处）</el-tag>
                    </div>
                  </el-form-item>
                </el-col>
                <el-col :span="7">
                  <el-form-item label="损毁全额" prop="barricadeDamagedFullAmount">
                    <div
                      style="width: 100%; display: flex; align-items: center; flex-direction: row; flex-wrap: nowrap;">
                      <el-input-number v-model="formParams.barricadeDamagedFullAmount" controls-position="right"
                                       @change="formtotalCompute" :min="0" :precision="2" style="width: 100%;"
                                       placeholder="请输入损毁全额"/>
                      <el-tag size="small" type="info" effect="light">（万元）</el-tag>
                    </div>
                  </el-form-item>
                </el-col>
              </el-row>

            </div>

            <div class="infoBox" style="margin-bottom: 20px;" v-loading="formLoading"
                 v-show="formParams.disasterFacilitiesType.includes('7')">
              <div class="infoTitle">
                坍塌方
              </div>

              <el-row :gutter="12">
                <el-col :span="8">
                  <el-form-item label="损毁方量" prop="collapseDamagedVolume">
                    <div
                      style="width: 100%; display: flex; align-items: center; flex-direction: row; flex-wrap: nowrap;">
                      <el-input-number v-model="formParams.collapseDamagedVolume" controls-position="right" :min="0"
                                       :precision="0" style="width: 100%;" placeholder="请输入损毁方量"/>
                      <el-tag size="small" type="info" effect="light">（立方米）</el-tag>
                    </div>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="损毁数量" prop="collapseDamagedNumber">
                    <div
                      style="width: 100%; display: flex; align-items: center; flex-direction: row; flex-wrap: nowrap;">
                      <el-input-number v-model="formParams.collapseDamagedNumber" controls-position="right" :min="0"
                                       :precision="0" style="width: 100%;" placeholder="请输入损毁数量"/>
                      <el-tag size="small" type="info" effect="light">（处）</el-tag>
                    </div>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="损毁全额" prop="collapseDamagedFullAmount">
                    <div
                      style="width: 100%; display: flex; align-items: center; flex-direction: row; flex-wrap: nowrap;">
                      <el-input-number v-model="formParams.collapseDamagedFullAmount" controls-position="right"
                                       @change="formtotalCompute" :min="0" :precision="2" style="width: 100%;"
                                       placeholder="请输入损毁全额"/>
                      <el-tag size="small" type="info" effect="light">（万元）</el-tag>
                    </div>
                  </el-form-item>
                </el-col>
              </el-row>
            </div>

            <div class="infoBox" style="margin-bottom: 20px;" v-loading="formLoading"
                 v-show="formParams.disasterFacilitiesType.includes('99')">
              <div class="infoTitle">
                其它
              </div>

              <el-row :gutter="12">
                <el-col :span="24">
                  <el-form-item label="灾毁设施名称" prop="otherRemarks">
                    <el-input v-model="formParams.otherRemarks" clearable placeholder="请输入灾毁设施名称"/>
                  </el-form-item>
                </el-col>
              </el-row>

              <el-row :gutter="12">
                <el-col :span="12">
                  <el-form-item label="损失全额" prop="otherDamagedAmount">
                    <div
                      style="width: 100%; display: flex; align-items: center; flex-direction: row; flex-wrap: nowrap;">
                      <el-input-number v-model="formParams.otherDamagedAmount" controls-position="right"
                                       @change="formtotalCompute" :min="0" :precision="2" style="width: 100%;"
                                       placeholder="请输入损失全额"/>
                      <el-tag size="small" type="info" effect="light">（万元）</el-tag>
                    </div>
                  </el-form-item>
                </el-col>
              </el-row>
            </div>

          </div>

          <div class="infoBox" style="margin-bottom: 20px;" v-loading="formLoading">
            <div class="infoTitle">
              损失合计
            </div>

            <el-row :gutter="12">
              <el-col :span="12">
                <el-form-item label-width="0" prop="totalLoss">
                  <div style="width: 100%; display: flex; align-items: center; flex-direction: row; flex-wrap: nowrap;">
                    <el-input-number v-model="formParams.totalLoss" controls-position="right" disabled :min="0"
                                     :precision="2" style="width: 100%;" placeholder="自动计算损失合计"/>
                    <el-tag size="small" type="info" effect="light">（万元）</el-tag>
                  </div>
                </el-form-item>
              </el-col>
            </el-row>
          </div>

        </div>

        <div class="infoBox" style="margin-bottom: 20px;" v-loading="formLoading">
          <div class="infoTitle">
            灾毁照片
          </div>
          <el-row :gutter="12">
            <el-col :span="24">
              <el-form-item label-width="0" prop="riskDescription">
                <div class="imgBox" v-if="formImgSrcList1.length > 0">
                  <el-timeline>
                    <el-timeline-item v-for="item in formImgDateLine1" :key="item.time" :timestamp="item.time"
                                      placement="top" color="#5cbb7a">
                      <el-card class="imgBoxCard">
                        <div class="cardMain" v-for="itemC in item.data">
                          <el-button class="imgDeleteBtn" type="danger" icon="el-icon-delete" circle
                                     @click="formDeleteImg1(itemC.id)"></el-button>
                          <div class="imgTitle">
                            <el-tooltip class="item" effect="dark" :content="itemC.name" placement="top">
                              <i class="el-icon-info"></i>
                            </el-tooltip>
                            {{ itemC.name }}
                          </div>
                          <el-image
                            fit="cover"
                            class="img"
                            @click="formImgPreview1(itemC.imgUrl)"
                            :src="itemC.url"
                            :preview-src-list="formImgUrlList1"
                          ></el-image>
                          <div class="footer">
                            {{ `由 ${itemC.createBy} 上传于 ${itemC.createTime}` }}
                          </div>
                        </div>
                      </el-card>
                    </el-timeline-item>
                  </el-timeline>
                </div>
                <div class="noneBox" v-else>
                  暂无内容
                </div>
              </el-form-item>
            </el-col>
          </el-row>
        </div>

        <div class="infoBox" v-loading="formLoading" v-if="formType !== 'view'" style="padding: 15px; margin-bottom: 20px;">
          <div class="infoTitle">
            <el-tooltip content="上传提示" placement="top">
              <i class="el-icon-question"></i>
            </el-tooltip>
            上传灾毁照片
          </div>
          <ImageUpload
            v-if="formDialog"
            v-model="formUploadList1"
            :ownerId="formImgOwnerId1"
            :needRemark="true"
            :can-sort="true"
            storage-path="/disaster/risk/damage/"
            platform="fykj"
          />
        </div>

        <div class="infoBox" style="margin-bottom: 20px;" v-loading="formLoading">
          <div class="infoTitle">
            应急抢通信息
          </div>

          <el-row :gutter="12">
            <el-col :span="12">
              <el-form-item label="是否已抢通" prop="alreadySnatchedThrough">
                <el-radio-group v-model="formParams.alreadySnatchedThrough" ref="subFormRadio3">
                  <el-radio :label="'0'">是</el-radio>
                  <el-radio :label="'1'">否</el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="预计抢通时间" prop="actualRushTimeExpected">
                <el-date-picker v-model="formParams.actualRushTimeExpected" style="width:100%" type="datetime"
                                placeholder="请选择预计抢通时间" clearable value-format="yyyy-MM-dd HH:mm"
                                format="yyyy-MM-dd HH:mm"/>
              </el-form-item>
            </el-col>
          </el-row>

          <el-divider></el-divider>

          <el-row :gutter="12">
            <el-col :span="24">
              <el-form-item label="灾毁等级" prop="disasterLevel">
                <el-radio-group v-model="formParams.disasterLevel" ref="subFormRadio4">
                  <el-radio v-for="dict in dict.type.hazard_degree" :label="dict.value" :key="dict.value">{{
                      dict.label
                    }}
                  </el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>
          </el-row>

          <el-divider></el-divider>

          <el-row :gutter="12">
            <el-col :span="24">
              <el-form-item label="备注" prop="remarks">
                <el-input v-model="formParams.remarks" clearable
                          placeholder="例：造成上行行车道2和行车道3断交，现场行车道1通行(仅一个车道通行)。"/>
              </el-form-item>
            </el-col>
          </el-row>

        </div>

        <div class="infoBox" style="margin-bottom: 20px;" v-loading="formLoading">
          <div class="infoTitle">
            应急抢通情况
          </div>

          <el-row :gutter="12">
            <el-col :span="12">
              <el-form-item label="实际抢通时间" prop="actualRushTime">
                <el-date-picker v-model="formParams.actualRushTime" style="width:100%" type="datetime"
                                placeholder="请选择实际抢通时间" clearable value-format="yyyy-MM-dd HH:mm"
                                format="yyyy-MM-dd HH:mm"/>
              </el-form-item>
            </el-col>
          </el-row>

          <el-divider></el-divider>

          <el-row :gutter="12">
            <el-col :span="12">
              <el-form-item label="是否已处置" prop="handled">
                <el-radio-group v-model="formParams.handled" ref="subFormRadio5">
                  <el-radio :label="'0'">是</el-radio>
                  <el-radio :label="'1'">否</el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="（预计）处置时间" prop="disposalTimeExpected">
                <el-date-picker v-model="formParams.disposalTimeExpected" style="width:100%" type="datetime"
                                placeholder="请选择（预计）处置时间" clearable value-format="yyyy-MM-dd HH:mm"
                                format="yyyy-MM-dd HH:mm"/>
              </el-form-item>
            </el-col>
          </el-row>

          <el-divider></el-divider>

          <el-row :gutter="12">
            <el-col :span="8">
              <el-form-item label="（预计）投入人工" prop="inputLaborExpected">
                <div style="width: 100%; display: flex; align-items: center; flex-direction: row; flex-wrap: nowrap;">
                  <el-input-number v-model="formParams.inputLaborExpected" controls-position="right" :min="0"
                                   :precision="2" style="width: 100%;" placeholder="请输入（预计）投入人工"/>
                  <el-tag size="small" type="info" effect="light">（万元）</el-tag>
                </div>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="（预计）投入机械" prop="inputMachineryExpected">
                <div style="width: 100%; display: flex; align-items: center; flex-direction: row; flex-wrap: nowrap;">
                  <el-input-number v-model="formParams.inputMachineryExpected" controls-position="right" :min="0"
                                   :precision="2" style="width: 100%;" placeholder="请输入（预计）投入机械"/>
                  <el-tag size="small" type="info" effect="light">（万元）</el-tag>
                </div>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="（预计）投入资金" prop="inputFundExpected">
                <div style="width: 100%; display: flex; align-items: center; flex-direction: row; flex-wrap: nowrap;">
                  <el-input-number v-model="formParams.inputFundExpected" controls-position="right" :min="0"
                                   :precision="2" style="width: 100%;" placeholder="请输入（预计）投入资金"/>
                  <el-tag size="small" type="info" effect="light">（万元）</el-tag>
                </div>
              </el-form-item>
            </el-col>
          </el-row>

          <el-divider></el-divider>

          <div class="infoBox" style="margin-bottom: 20px;" v-loading="formLoading">
            <div class="infoTitle">
              抢通后照片
            </div>
            <el-row :gutter="12">
              <el-col :span="24">
                <el-form-item label-width="0" prop="riskDescription">
                  <div class="imgBox" v-if="formImgSrcList2.length > 0">
                    <el-timeline>
                      <el-timeline-item v-for="item in formImgDateLine2" :key="item.time" :timestamp="item.time"
                                        placement="top" color="#5cbb7a">
                        <el-card class="imgBoxCard">
                          <div class="cardMain" v-for="itemC in item.data">
                            <el-button class="imgDeleteBtn" type="danger" icon="el-icon-delete" circle
                                       @click="formDeleteImg2(itemC.id)"></el-button>
                            <div class="imgTitle">
                              <el-tooltip class="item" effect="dark" :content="itemC.name" placement="top">
                                <i class="el-icon-info"></i>
                              </el-tooltip>
                              {{ itemC.name }}
                            </div>
                            <el-image
                              fit="cover"
                              class="img"
                              @click="formImgPreview2(itemC.imgUrl)"
                              :src="itemC.url"
                              :preview-src-list="formImgUrlList2"
                            ></el-image>
                            <div class="footer">
                              {{ `由 ${itemC.createBy} 上传于 ${itemC.createTime}` }}
                            </div>
                          </div>
                        </el-card>
                      </el-timeline-item>
                    </el-timeline>
                  </div>
                  <div class="noneBox" v-else>
                    暂无内容
                  </div>
                </el-form-item>
              </el-col>
            </el-row>
          </div>

          <div class="infoBox" v-loading="formLoading" v-if="formType !== 'view'" style="padding: 15px; margin-bottom: 20px;">
            <div class="infoTitle">
              <el-tooltip content="请补充上传灾毁抢通后现场照片" placement="top">
                <i class="el-icon-question"></i>
              </el-tooltip>
              上传抢通后照片
            </div>
            <ImageUpload
              v-if="formDialog"
              v-model="formUploadList2"
              :ownerId="formImgOwnerId2"
              :needRemark="true"
              :can-sort="true"
              storage-path="/disaster/risk/damageScoopOut/"
              platform="fykj"
            />
          </div>
        </div>

        <div class="infoBox" style="margin-bottom: 20px;" v-loading="formLoading">
          <div class="infoTitle">
            公函
          </div>
          <el-row :gutter="12">
            <el-col :span="24">
              <el-form-item label-width="0" prop="riskDescription">
                <div class="fileBox" v-if="formFileSrcList.length > 0" v-for="item in formFileSrcList">
                  <div class="fileItemBox">
                    <div class="fileItem">
                      <div class="boxLeft">
                        <i class="el-icon-document"></i>
                      </div>
                      <div class="boxmiddle">
                        {{ item.name }}
                      </div>
                      <div class="boxRight">
                        <el-button type="primary" icon="el-icon-download"
                                   @click="formDownloadFile(item.url, item.name)">下载
                        </el-button>
                        <el-button type="danger" icon="el-icon-delete" @click="formDeleteFile(item.id)">删除</el-button>
                      </div>
                    </div>
                  </div>
                </div>
                <div class="noneBox" v-if="formFileSrcList.length === 0">暂无内容</div>
              </el-form-item>
            </el-col>
          </el-row>
        </div>

        <div class="infoBox" v-loading="formLoading" v-if="formType !== 'view'" style="padding: 15px; margin-bottom: 20px;">
          <div class="infoTitle">
            <el-tooltip content="灾毁损失全额超过1000万元，需要上传公函" placement="top">
              <i class="el-icon-question"></i>
            </el-tooltip>
            上传公函
          </div>
          <FileUpload
            v-if="formDialog"
            v-model="formUploadList3"
            :ownerId="formFileOwnerId"
            storage-path="/disaster/risk/officialLetter/"
          />
        </div>

      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-row class="footerTip" v-if="formParams.createName">
          <el-col :span="12" style="display: flex; justify-content: flex-start;">
            填报人：{{ formParams.createName }}
          </el-col>
          <el-col :span="12" style="display: flex; justify-content: flex-end;">
            填报时间：{{ formParams.createTime }}
          </el-col>
        </el-row>
        <el-row v-if="formType !== 'view'">
          <el-col :span="24" style="display: flex; justify-content: center;">
            <el-button type="primary" @click="formSubmit('formRef', '0')" :loading="formBtnLoading">保 存</el-button>
            <el-button type="success" @click="formSubmit('formRef', '1')" :loading="formBtnLoading">提交审核</el-button>
          </el-col>
        </el-row>
      </div>
    </el-dialog>

    <!-- 关联风险点查询dialog -->
    <el-dialog title="风险点查询" class="riskSearchDialog" :visible.sync="riskSearchDialog" @close="riskDialogClose"
               width="70%" :close-on-press-escape="false" :close-on-click-modal="false" append-to-body>
      <div class="searchDialogMain">
        <div class="searchBox" style="padding: 0">
          <el-row style="margin-bottom: 10px;">
            <el-col :span="24">
              <el-input v-model="riskQueuryParams.disasterNum" style="width: 193px; margin-right: 20px;"
                        placeholder="请输入灾害编号" clearable @keyup.enter.native="riskList"></el-input>
              <el-input v-model="riskQueuryParams.disasterName" style="width: 193px; margin-right: 20px;"
                        placeholder="请输入灾害名称" clearable @keyup.enter.native="riskList"></el-input>
              <el-select v-model="riskQueuryParams.disasterType" style="margin-right: 20px;"
                         placeholder="请选择灾害类型" clearable>
                <el-option v-for="dict in dict.type.disaster_risk_type" :key="dict.value" :label="dict.label"
                           :value="dict.label"/>
              </el-select>
              <el-button type="primary" icon="el-icon-search" size="mini" @click="riskList">搜索</el-button>
              <el-button icon="el-icon-refresh" size="mini" @click="riskQueryReset">重置</el-button>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="24">
              <CascadeSelection
                style="min-width: 192px;"
                :form-data="riskQueuryParams"
                v-model="riskQueuryParams"
                types="201"
                multiple
              />
            </el-col>
          </el-row>
        </div>
        <div class="tableBox">
          <el-table size="mini" height="calc(100% - 50px)" border ref="tableRef" v-loading="riskTableLoading"
                    :data="riskTableData">
            <el-table-column label="灾害编号" fixed :show-overflow-tooltip="true" align="center" prop="disasterNum"
                             width="180"/>
            <el-table-column label="灾害名称" fixed :show-overflow-tooltip="true" align="center" prop="disasterName"/>
            <el-table-column label="灾害类型" fixed :show-overflow-tooltip="true" align="center" prop="disasterType">
              <template slot-scope="scope">
                <p v-if="!scope.row.disasterType.includes('其它')">{{ scope.row.disasterType }}</p>
                <dict-tag
                  v-if="scope.row.disasterType.includes('其它') && scope.row.disasterType.split(':')[1] !== '99'"
                  :options="dict.type.disaster_risk_other_type" :value="scope.row.disasterType.split(':')[1]"/>
                <p v-if="scope.row.disasterType.includes('其它') && scope.row.disasterType.split(':')[1] === '99'">
                  {{ scope.row.disasterType.split(':')[2] }}</p>
              </template>
            </el-table-column>
            <el-table-column label="管养单位" fixed :show-overflow-tooltip="true" align="center"
                             prop="maintenanceUnitName"/>
            <el-table-column label="路段名称" fixed :show-overflow-tooltip="true" align="center"
                             prop="roadSectionName"/>
            <el-table-column label="路线名称" fixed :show-overflow-tooltip="true" align="center" prop="routerName"
                             width="180"/>
            <el-table-column label="采集人" fixed :show-overflow-tooltip="true" align="center" prop="createName"/>
            <el-table-column label="采集单位" fixed :show-overflow-tooltip="true" align="center" prop="createUnit"/>
            <el-table-column label="状态" fixed :show-overflow-tooltip="true" align="center" prop="status" width="80">
              <template slot-scope="scope">
                <el-tag v-if="scope.row.status === '0'" type="info">待提交</el-tag>
                <el-tag v-if="scope.row.status === '1'" type="warning">审核中</el-tag>
                <el-tag v-if="scope.row.status === '2'" type="success">已审核</el-tag>
                <el-tag v-if="scope.row.status === '3'">已撤销</el-tag>
                <el-tag v-if="scope.row.status === '4'" type="danger">已驳回</el-tag>
              </template>
            </el-table-column>
            <el-table-column label="操作" align="center" class-name="small-padding fixed-width" fixed="right"
                             width="80">
              <template slot-scope="scope">
                <el-button size="mini" type="text" @click="riskSelect(scope.row)">选择
                </el-button>
              </template>
            </el-table-column>
          </el-table>

          <pagination :total="riskTableTotal" :page.sync="riskQueuryParams.pageNum" :pager-count="2"
                      :limit.sync="riskQueuryParams.pageSize" @pagination="riskList" style="margin-right: 10px;"/>
        </div>
      </div>
    </el-dialog>

    <!-- 风险点详情dialog -->
    <el-dialog :visible.sync="riskFormDialog" width="70%" max-height="50%" :close-on-press-escape="false"
               :close-on-click-modal="false" append-to-body class="formDialog">
      <template slot="title">
        <div class="titleBox">
          <div class="title">风险点详情表单</div>
          <div class="subTitle" v-if="riskFormParams.id">ID：{{ riskFormParams.id }}</div>
          <div class="riskLevel" v-if="!riskFormParams.disasterType.includes('其它')">
            <p class="title">风险等级：</p>
            <p class="main" v-if="riskFormParams.riskGrade" :style="{ 'background':
              riskFormParams.riskGrade.includes('一') ? 'rgb(254, 0, 0)' :
              riskFormParams.riskGrade.includes('二') ? 'rgb(255, 192, 1)' :
              riskFormParams.riskGrade.includes('三') ? 'rgb(255, 255, 3)' : 'rgb(1, 176, 241)' }"
            >{{ riskFormParams.riskGrade }}</p>
            <p class="main" v-else style="background: rgb(1, 176, 241)">
              暂无
            </p>
            <el-tag type="info" effect="plain" style="margin-left: 5px;">
              {{ `${riskFormParams.riskScore ? riskFormParams.riskScore : '0'}分` }}
            </el-tag>
          </div>
        </div>
      </template>
      <el-form ref="formRef" :model="riskFormParams" label-width="125px" label-position="right">
        <div class="infoBox" style="margin-bottom: 20px;" v-loading="riskFormDetailLoading">
          <div class="infoTitle">
            基础信息
          </div>
          <el-row :gutter="12">
            <el-col :span="12">
              <el-form-item label="管养单位名称" prop="maintenanceUnitId">
                <el-select v-model="riskFormParams.maintenanceUnitId" disabled placeholder="请选择管养单位"
                           style="width: 100%;">
                  <el-option v-for="item in formMaintenanceList" :label="item.label" :value="item.id"
                             :key="item.id"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="路段名称" prop="roadSectionId">
                <el-select v-model="riskFormParams.roadSectionId" disabled placeholder="请选择路段"
                           style="width: 100%;">
                  <el-option v-for="item in formRoadSectionList" :label="item.maintenanceSectionName"
                             :value="item.maintenanceSectionId" :key="item.maintenanceSectionId"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="12">
            <el-col :span="12">
              <el-form-item label="路线名称" prop="routerNum">
                <el-select v-model="riskFormParams.routerNum" disabled placeholder="请选择路线" style="width: 100%;">
                  <el-option v-for="item in formRouteList" :label="`${item.routeName}（${item.routeCode}）`"
                             :value="item.routeCode" :key="item.routeCode"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="技术等级" prop="technicalGrade">
                <!-- <el-input readonly v-model="formParams.technicalGrade" placeholder="请输入技术等级" /> -->
                <el-select v-model="riskFormParams.technicalGrade" disabled placeholder="请选择技术等级"
                           style="width: 100%;">
                  <el-option v-for="dict in dict.type.sys_route_grade" :label="dict.label" :value="dict.value"
                             :key="dict.value"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="12">
            <el-col :span="12">
              <el-form-item label="灾害名称" prop="disasterName">
                <el-input v-model="riskFormParams.disasterName" disabled placeholder="请输入灾害名称"/>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="灾害编号" prop="disasterNum">
                <el-input v-model="riskFormParams.disasterNum" disabled placeholder="请输入灾害编号"/>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="12">
            <el-col :span="12">
              <el-form-item label="起点经纬度" prop="startLatitudeAndLongitude">
                <div style="width: 100%; display: flex; flex-direction: row; flex-wrap: nowrap;">
                  <el-input v-model="riskFormParams.startLatitudeAndLongitude" disabled
                            placeholder="请输入起点经纬度，以英文逗号分隔"></el-input>
                  <el-button slot="append" icon="el-icon-location" @click="openCoordinateDialog('start')">查看坐标
                  </el-button>
                </div>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="止点经纬度" prop="endLatitudeAndLongitude">
                <div style="width: 100%; display: flex; flex-direction: row; flex-wrap: nowrap;">
                  <el-input v-model="riskFormParams.endLatitudeAndLongitude" disabled
                            placeholder="请输入止点经纬度，以英文逗号分隔"></el-input>
                  <el-button icon="el-icon-location" @click="openCoordinateDialog('end')">查看坐标</el-button>
                </div>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="12">
            <el-col :span="12">
              <el-form-item label="起点桩号" prop="pileStartNum">
                <el-input v-model="riskFormParams.pileStartNum" disabled placeholder="请输入起点桩号"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="终点桩号" prop="pileEndNum">
                <el-input v-model="riskFormParams.pileEndNum" disabled placeholder="请输入终点桩号"></el-input>
              </el-form-item>
            </el-col>

          </el-row>

        </div>

        <Crumble v-if="riskFormParams.disasterType === '崩塌'" ref="formSubRef"/>

        <LandSlide v-if="riskFormParams.disasterType === '滑坡'" ref="formSubRef"/>

        <DebrisFlow v-if="riskFormParams.disasterType === '泥石流'" ref="formSubRef"/>

        <SubSide v-if="riskFormParams.disasterType === '沉陷与塌陷'" ref="formSubRef"/>

        <WaterDestruction v-if="riskFormParams.disasterType === '水毁'" ref="formSubRef"/>

        <Other v-if="riskFormParams.disasterType.includes('其它')" ref="formSubRef"/>

      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-row class="footerTip" v-if="riskFormParams.createName">
          <el-col :span="12" style="display: flex; justify-content: flex-start;">
            采集人：{{ riskFormParams.createName }}
          </el-col>
          <el-col :span="12" style="display: flex; justify-content: flex-end;">
            采集时间：{{ riskFormParams.createTime }}
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24" style="display: flex; justify-content: center;">
            <!-- <el-button type="primary" @click="riskFormDialog = false" :loading="formBtnLoading">关 闭</el-button> -->
          </el-col>
        </el-row>
      </div>
    </el-dialog>

    <!-- 用户导入对话框 -->
    <el-dialog :title="upload.title" :visible.sync="upload.open" width="400px" :close-on-press-escape="false"
               :close-on-click-modal="false" append-to-body>
      <el-upload
        ref="upload"
        :limit="1"
        accept=".xlsx, .xls"
        :headers="upload.headers"
        :action="upload.url + '?disasterType=' + upload.disasterType"
        :disabled="upload.isUploading"
        :on-progress="handleFileUploadProgress"
        :on-success="handleFileSuccess"
        :auto-upload="false"
        drag
      >
        <i class="el-icon-upload"></i>
        <div class="el-upload__text">
          将文件拖到此处，或
          <em>点击上传</em>
        </div>
        <div class="el-upload__tip" slot="tip">
          <!-- <el-checkbox v-model="upload.updateSupport" />是否更新已经存在的灾害数据 -->
          <el-link type="primary" style="font-size:12px" @click="importTemplate">下载模板</el-link>
        </div>
        <div class="el-upload__tip" style="color:red" slot="tip">提示：仅允许导入“xls”或“xlsx”格式文件！</div>
      </el-upload>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitFileForm">确 定</el-button>
        <!-- <el-button @click="upload.open = false">取 消</el-button> -->
      </div>
    </el-dialog>

    <!-- 坐标拾取dialog -->
    <el-dialog title="查看坐标" class="coordinateDialog" :visible.sync="coordinateDialog" @close="coordinateClose"
               :close-on-press-escape="false" :close-on-click-modal="false" append-to-body>
      <div class="coordinateMap">
        <div id="coordinateBox" v-if="coordinateDialog"></div>
        <!-- <div class="coordinateSearch">
          <el-input v-model="coordinateSearch" clearable placeholder="请输入地名"  @keyup.enter.native="coordinateList" />
          <el-button type="primary" icon="el-icon-search" style="margin-left: 5px;" @click="coordinateList">查询</el-button>
        </div> -->
        <div class="coordinateTip">
          <el-row>
            <el-col :span="24" style="padding: 5px 0px;">
              当前经纬度：
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="24">
              <el-input v-model="coordinatePosition" readonly/>
            </el-col>
          </el-row>
        </div>
      </div>
      <!-- <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="coordinateSave">保 存</el-button>
      </div> -->
    </el-dialog>

    <!-- 审核信息 -->
    <el-dialog title="审核信息" :visible.sync="auditInfoDialog" :close-on-press-escape="false"
               :close-on-click-modal="false" append-to-body>
      <div class="hisMainBox">
        <el-timeline reverse>
          <el-timeline-item
            v-for="item in formIdeaList"
            :key="item.id"
            :timestamp="`${item.startTime}`"
            color="#5cbb7a"
            placement="top"
          >
            <el-card>
              <div slot="header" class="clearfix">
                <span>节点：{{ item.nodeName }}</span>
                <el-button icon="el-icon-edit" type="text" size="mini" style="float: right; padding: 3px 0"
                           @click="updateNodeTime(item)"></el-button>
              </div>
              <div class="hisIdeaBox">
                <div class="boxMain">
                  <h3 v-if="item.assignee">{{ item.content.comment }}</h3>
                  <div class="changeBox" v-if="item.content.operatingRecord && item.content.operatingRecord.length > 0">
                    <div class="title">表单修改记录</div>
                    <div class="list" v-for="val in item.content.operatingRecord">
                      <el-tag type="info" effect="plain">{{ val.name }}
                      </el-tag>
                      <p style="margin: 0 10px;">由</p>
                      <p style="color: #E6A23C">{{ `"${val.oldVal}"` }}</p>
                      <p style="margin: 0 10px;">改为</p>
                      <p style="color: #F56C6C">{{ `"${val.newVal}"` }}</p>
                    </div>
                  </div>
                </div>
                <div class="boxBottom">
                  <p v-if="item.assignee">提交人：{{ item.assignee }}</p>
                  <p v-if="item.assignee">提交时间：{{ item.endTime }}</p>
                  <p v-if="!item.assignee">流程结束</p>
                  <p v-if="!item.assignee">结束时间：{{ item.endTime }}</p>
                </div>
              </div>
            </el-card>
          </el-timeline-item>
        </el-timeline>
      </div>
    </el-dialog>

    <!-- 更新节点时间 -->
    <el-dialog title="更新节点信息" :visible.sync="updateTimeDialog" width="30%" :close-on-press-escape="false"
               :close-on-click-modal="false" append-to-body>
      <el-form ref="form" :model="updateTimeForm" label-width="120px">
        <el-form-item label="节点名称">
          <el-input v-model="updateTimeForm.nodeName" disabled></el-input>
        </el-form-item>
        <el-form-item label="审核人(旧)" v-if="updateTimeForm.nodeName === '审核'">
          <el-input v-model="updateTimeForm.oldAssignee" disabled></el-input>
        </el-form-item>
        <el-form-item label="审核人(新)" v-if="updateTimeForm.nodeName === '审核'">
          <div style="width: 100%; display: flex; justify-content: space-between">
            <el-select v-model="updateTimeForm.assignee" clearable placeholder="请选择创建人" style="width: 80%">
              <el-option
                v-for="item in userSelectData"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              >
              </el-option>
            </el-select>
            <el-button icon="el-icon-search" type="primary" @click="userDialogOpen" style="width: 18%"></el-button>
          </div>
        </el-form-item>
        <el-form-item label="节点时间（旧）">
          <el-col :span="11">
            <el-date-picker
              v-model="updateTimeForm.hisStartTime"
              disabled
              type="datetime"
              style="width: 100%;"
              placeholder="选择日期时间">
            </el-date-picker>
          </el-col>
          <el-col style="text-align: center;" :span="2">-</el-col>
          <el-col :span="11">
            <el-date-picker
              v-model="updateTimeForm.hisEndTime"
              disabled
              type="datetime"
              style="width: 100%;"
              placeholder="选择日期时间">
            </el-date-picker>
          </el-col>
        </el-form-item>
        <el-form-item label="节点时间（新）">
          <el-col :span="11">
            <el-date-picker
              v-model="updateTimeForm.startTime"
              type="datetime"
              format="yyyy-MM-dd HH:mm:ss"
              value-format="yyyy-MM-dd HH:mm:ss"
              style="width: 100%;"
              placeholder="选择日期时间">
            </el-date-picker>
          </el-col>
          <el-col style="text-align: center;" :span="2">-</el-col>
          <el-col :span="11">
            <el-date-picker
              v-model="updateTimeForm.endTime"
              type="datetime"
              format="yyyy-MM-dd HH:mm:ss"
              value-format="yyyy-MM-dd HH:mm:ss"
              style="width: 100%;"
              placeholder="选择日期时间">
            </el-date-picker>
          </el-col>
        </el-form-item>
        <el-form-item label="审核变量(旧)" v-if="updateTimeForm.nodeName === '审核'">
          <el-input  type="textarea" :rows="2" v-model="updateTimeForm.oldContent" disabled></el-input>
        </el-form-item>
        <el-form-item label="审核变量(新)" v-if="updateTimeForm.nodeName === '审核'">
          <el-input  type="textarea" :rows="2" v-model="updateTimeForm.content"></el-input>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="updateTimeDialog = false">取 消</el-button>
        <el-button type="primary" @click="subUpdateTime" :loading="updateTimeFormBtnLoading">保 存</el-button>
      </span>
    </el-dialog>

    <el-dialog :visible.sync="userDialog" title="用户选择" width="40%" :close-on-press-escape="false" :close-on-click-modal="false" append-to-body class="userDialogBox">
      <div class="userBoxMain">
        <div class="searchBox">
          <el-row>
            <el-col :span="20">
              <el-input v-model="userParams.nickName" placeholder="请输入用户昵称" clearable @keyup.enter.native="userList"></el-input>
            </el-col>
            <el-col :span="4" style="display: flex; justify-content: flex-end;">
              <el-button type="primary" icon="el-icon-search" size="mini" @click="userList">搜索</el-button>
            </el-col>
          </el-row>
        </div>
        <div class="dataBox">
          <el-table size="mini" height="calc(100% - 50px)" border ref="tableRef" v-loading="userTableLoading" :data="userTableData">
            <el-table-column label="用户名称" fixed :show-overflow-tooltip="true" align="center" prop="userName" />
            <el-table-column label="用户昵称" fixed :show-overflow-tooltip="true" align="center" prop="nickName" />
            <el-table-column label="用户编码" fixed :show-overflow-tooltip="true" align="center" prop="userId" />
            <el-table-column label="操作" align="center" class-name="small-padding fixed-width" fixed="right">
              <template slot-scope="scope">
                <el-button size="mini" type="text" @click="userSelect(scope.row)">选择
                </el-button>
              </template>
            </el-table-column>
          </el-table>

          <pagination :total="userTableTotal" :page.sync="userParams.pageNum" :pager-count="2"
                      :limit.sync="userParams.pageSize" @pagination="userList" style="margin-right: 10px;" />
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
// -------------------- 引入 --------------------
// 组件
// import PileInput from '@/components/PileInput/index2.vue' // 桩号（表单）
import CascadeSelection from '@/components/CascadeSelection/index.vue' // 管养处/路段/路线
import RangeInput from '@/views/baseData/components/rangeInput/index.vue' // 桩号（查询）
import ImageUpload from '@/views/disaster/ImageUpload.vue' // 图片上传组件
import FileUpload from '@/views/disaster/FileUpload.vue' // 文件上传组件
import AMapLoader from '@amap/amap-jsapi-loader'; // 高德地图

// 子表
import Crumble from './crumble/index.vue' // 崩塌
import LandSlide from './landslide/index.vue' // 滑坡
import DebrisFlow from './debrisFlow/index.vue' // 泥石流
import SubSide from './subside/index.vue' // 沉陷与塌陷
import WaterDestruction from './waterDestruction/index.vue' // 水毁
import Other from './other/index.vue' // 其它

// 导入相关
import {getToken} from "@/utils/auth";

// API
import {
  queryPageDamage,
  createDamage,
  updateDamage,
  deleteDamage,
  queryPageRiskById,
  queryPageRisk,
  revokeProcess
} from '@/api/disaster/damage/damage' // 灾毁类型接口
import {deptTreeSelect} from '@/api/tmpl' // 管养单位
import {
  listByMaintenanceSectionId,
  getMaintenanceSectionListAll
} from '@/api/baseData/common/routeLine' // 路段数据
import {listMaintenanceSectionAll} from '@/api/system/maintenanceSection' // 养护路段接口
import {getTreeByEntity} from '@/api/system/geography' // 区划接口
import {removeFile} from '@/api/system/fileUpload.js' // 删除图片接口
import {findFiles} from '@/api/file/index.js'
import {
  queryAuditInfo, queryTodoTaskByBusinessKey,
  queryTodoTaskByCategory,
  updateNodeInfo,
  updateNodeTime,
  updateTime
} from "@/api/disaster/process/process";
import {listUser} from "@/api/system/user"; // 流程接口
import moment from 'moment';

export default {
  name: "Damage",
  // 数据字典
  dicts: [
    'disaster_risk_type',  // 灾害类型
    'disaster_risk_other_type', // 灾害类型（其它）
    'sys_route_grade', // 技术等级
    'damage_surface_type', // 灾毁路面类型
    'damage_facility_type', // 灾毁设施类型
    'bridge_damage_type', // 桥梁损毁类型
    'hazard_degree', // 危害程度/灾毁等级
  ],
  // 组件
  components: {
    // PileInput,
    CascadeSelection,
    RangeInput,
    ImageUpload,
    FileUpload,
    Crumble,
    LandSlide,
    DebrisFlow,
    SubSide,
    WaterDestruction,
    Other,
  },

  // -------------------- 变量 --------------------
  data() {
    return {

      /**
       * 查询相关
       */
      queryShow: false, // 隐藏筛选显隐
      queryParams: { // 查询参数
        pageNum: 1, // 页码
        pageSize: 10, // 每页条数
        managementMaintenanceIds: [], // 管养处
        maintenanceSectionId: '', // 路段编号
        routeCodes: [], // 路线编号
        disasterSectionId: '', // 灾毁段编号
        disasterType: '', // 灾毁类型
        pileStartNum: '', // 起始桩号
        pileEndNum: '', // 结束桩号
        startTime: '', // 开始时间
        endTime: '', // 结束时间
        status: '' // 状态
      },
      queryTime: [], // 查询时间
      queryTotal: 0, // 总条数

      /**
       * 表格相关
       */
      tableData: [], // 表格数据
      tableSelection: [], // 表格选中数据
      tableLoading: false, // 表格加载
      businessKeyArr: [], // 业务主键

      /**
       * 表单相关
       */
      formParams: { // 表单参数
        id: '', // 主表ID
        disasterType: '', // 灾毁类型
        disasterRiskId: '', // 风险点ID
        disasterSectionId: '', // 灾毁段编号
        startLatitudeAndLongitude: '', // 经纬度（起）
        endLatitudeAndLongitude: '', // 经纬度（止）
        pileStartNum: '', // 桩号（起）
        pileEndNum: '', // 桩号（终）
        roadbedWidth: '', // 路面宽度
        roadbedType: '', // 路面类型
        disasterStartTime: '', // 灾毁发生时间
        interrupt: '', // 是否中断
        disasterFacilitiesType: [], // 灾毁设施类型
        // 路基
        damagedRoadbedVolume: 0, // 损毁路基方量
        damagedRoadbedLength: 0, // 损毁路基长度
        damagedFullAmount: 0, // 损失全额
        // 路面
        asphaltDamagedRoadbedArea: 0, // 损毁路面面积-沥青路面
        asphaltDamagedRoadbedLength: 0, // 损毁路面长度-沥青路面
        asphaltDamagedFullAmount: 0, // 损失全额-沥青路面

        cementDamagedRoadbedArea: 0, // 损毁路面面积-水泥路面
        cementDamagedRoadbedLength: 0, // 损毁路面长度-水泥路面
        cementDamagedFullAmount: 0, // 损失全额-水泥路面

        dinasDamagedRoadbedArea: 0, // 损毁路面面积-砂石路面
        dinasDamagedRoadbedLength: 0, // 损毁路面长度-砂石路面
        dinasDamagedFullAmount: 0, // 损失全额-砂石路面
        // 桥梁
        bridgeDamagedType: '', // 桥梁毁坏类型
        bridgeDamagedLength: 0, // 损毁长度-桥梁
        bridgeDamagedNumber: 0, // 损毁数量-桥梁
        bridgeDamagedFullAmount: 0, // 损毁全额-桥梁
        // 隧道
        tunnelDamagedLength: 0, // 损毁长度-隧道
        tunnelDamagedNumber: 0, // 损毁数量-隧道
        tunnelDamagedFullAmount: 0, // 损毁全额-隧道
        // 涵洞
        culvertDamagedNumber: 0, // 损毁数量-涵洞
        culvertDamagedFullAmount: 0, // 损毁全额-涵洞
        // 防护工程
        revetmentDamagedVolume: 0, // 损毁方量-坡护
        revetmentDamagedNumber: 0, // 损毁数量-坡护
        revetmentDamagedFullAmount: 0, // 损毁全额-坡护

        barricadeDamagedVolume: 0, // 损毁方量-驳岸、挡墙
        barricadeDamagedNumber: 0, // 损毁数量-驳岸、挡墙
        barricadeDamagedFullAmount: 0, // 损毁全额-驳岸、挡墙
        // 坍塌方
        collapseDamagedVolume: 0, // 损毁方量-坍塌方
        collapseDamagedNumber: 0, // 损毁数量-坍塌方
        collapseDamagedFullAmount: 0, // 损毁全额-坍塌方
        // 其它
        otherRemarks: '', // 其它备注
        otherDamagedAmount: 0, // 莫他灾毁损失
        // 合计
        totalLoss: 0, // 损失合计
        disasterImgIds: '', // 灾毁照片
        alreadySnatchedThrough: '', // 是否已抢通
        actualRushTimeExpected: '', // 预计抢通时间
        disasterLevel: '', // 灾毁等级
        remarks: '', // 备注
        // 应急抢通情况
        actualRushTime: '', // 实际抢通时间
        handled: '', // 是否已处置
        disposalTimeExpected: '', // 处置时间
        inputLaborExpected: 0, // 投入人工
        inputMachineryExpected: 0, // 投入机械
        inputFundExpected: 0, // 投入资金
        snatchedThroughImgIds: '', // 抢通后照片
        // 公函
        officialLetter: '', // 公函
        // 其它字段
        processId: '', // 流程ID
        dataSources: 'PC', // 数据来源
        status: '', // 状态
        createName: '', // 填报人名称
        createUnit: '', // 填报人单位
        createTime: '', // 填报时间
      },
      formTitle: '灾毁填报', // 表单dialog标题
      formDialog: false, // 表单dialog显隐
      formLoading: false, // 表单加载
      formBtnLoading: false, // 表单按钮加载
      // formRiskType: '', // 表单灾害类型
      // formRiskId: '', // 表单主表ID
      formType: 'add', // 表单类型
      formRules: { // 表单校验规则
        disasterType: [
          {required: true, message: "灾毁类型不能为空", trigger: "blur"}
        ],
        disasterRiskId: [
          {required: true, message: "需要关联风险点信息", trigger: "blur"}
        ],
      },

      formMaintenanceList: [], // 管养单位列表
      formRoadSectionList: [], // 路段列表
      formRouteList: [], // 路线列表
      // 图片上传相关
      // 灾毁照片
      formImgSrcList1: [], // 图片渲染列表
      formImgUrlList1: [], // 图片预览列表
      formUploadList1: '', // 图片上传列表
      formImgOwnerId1: '', // 图片上传ownerId1
      // 抢通后照片
      formImgSrcList2: [], // 图片渲染列表
      formImgUrlList2: [], // 图片预览列表
      formUploadList2: '', // 图片上传列表
      formImgOwnerId2: '', // 图片上传ownerId2
      // 公函
      formFileSrcList: [], // 图片渲染列表
      formUploadList3: '', // 文件上传列表
      formFileOwnerId: '', // 文件上传ownerId

      formImgNum: [], // 图片数量（图片集用）

      // 关联风险点相关
      riskSearchDialog: false, // 关联风险点查询dialog
      riskQueuryParams: { // 关联风险点查询参数
        pageNum: 1,
        pageSize: 10,
        disasterRiskId: '', // 灾害ID
        disasterNum: '', // 灾害编号
        disasterName: '', // 灾害名称
        disasterType: '', // 灾害类型
        managementMaintenanceIds: [], // 管养处
        maintenanceSectionId: '', // 路段编号
        routeCodes: [], // 路线编号
      },
      riskTableTotal: 0, // 风险点数据总数
      riskTableData: [], // 关联风险点数据
      riskTableLoading: false, // 关联风险点数据加载

      riskFormDialog: false, // 关联风险点表单dialog
      riskFormParams: { // 关联风险点表单（主表）
        id: '', // 主表ID
        areaCode: '', // 县级区划编码
        disasterNum: '', // 灾害编号
        disasterName: '', // 灾害名称
        disasterType: '', // 灾害类型
        routerNum: '', // 路线编号
        routerName: '', // 路线名称
        startLatitudeAndLongitude: '', // 经纬度（起）
        endLatitudeAndLongitude: '', // 经纬度（止）
        technicalGrade: '', // 技术等级
        pileStartNum: '', // 桩号（起）
        pileEndNum: '', // 桩号（终）
        maintenanceUnitId: '', // 管养单位ID
        maintenanceUnitName: '', // 管养单位名称
        roadSectionId: '', // 路段ID
        roadSectionName: '', // 路段名称
        status: '', // 流程状态
        createName: '', // 采集人
        createTime: '', // 采集时间
        updateBy: '', // 更新人
        updateTime: '', // 更新时间
      },
      riskFormSubParams: {}, // 关联风险点表单（子表）
      riskFormLoading: false, // 表单加载（主表基础信息）
      riskFormDetailLoading: false, // 表单加载（风险点详细信息）

      /**
       * 桩号相关
       */
      pileStartK: '', // 起点桩号K
      pileStartAdd: '', // 起点桩号+
      pileEndK: '', // 终点桩号K
      pileEndAdd: '', // 终点桩号+

      /**
       * 灾毁设施类型相关
       */
      disasterFacilitiesData: [], // 灾毁设施类型字典数据

      /**
       * 区划相关
       */
      divisionsProvincial: '53', // 省级区划
      divisionsProvincialList: [ // 省级区划列表
        {
          disabled: true,
          id: "53",
          label: "云南省",
          level: 1
        }
      ],
      divisionsMunicipal: '', // 市级区划
      divisionsMunicipalList: [], // 市级区划列表
      areaCodeList: [], // 县级区划列表


      /**
       * 导入相关
       */
      // 用户导入参数
      upload: {
        // 是否显示弹出层（高边坡数据导入）
        open: false,
        // 弹出层标题（高边坡数据导入）
        title: "",
        // 是否禁用上传
        isUploading: false,
        // 是否更新已经存在的高边坡数据
        // updateSupport: 0,
        // 崩塌数据类型
        disasterType: '',
        // 设置上传的请求头部
        headers: {Authorization: "Bearer " + getToken()},
        // 上传的地址
        url: process.env.VUE_APP_BASE_API + "/disaster/highway/importData"
      },

      /**
       * 坐标相关
       */
      coordinateDialog: false, // 坐标dialog
      coordinateMap: null, // 地图实例
      coordinateMarker: null, // 地图标记
      coordinateType: 'start', // 坐标类型
      coordinatePosition: '', // 坐标经纬度
      coordinateSearch: '', // 坐标查询
      formIdeaList: [], // 表单流转意见数据
      updateTimeForm: {
        id: '',
        nodeName: '',
        hisStartTime: '',
        hisEndTime: '',
        startTime: '',
        endTime: '',
        oldAssignee: '',
        assignee: '',
        oldContent: '',
        content: '',
        taskId: ''
      },
      processId: '',
      updateTimeFormBtnLoading: false, // 表单按钮加载
      auditInfoDialog: false,
      updateTimeDialog: false,
      formProcessType: [], // 表单字典数据
      userDialog: false, // 用户dialog
      userParams: { // 用户查询参数
        pageNum: 1, // 页码
        pageSize: 10, // 每页条数
        nickName: '', // 用户昵称
      },
      userTableLoading: false, // 用户数据加载
      userTableData: [], // 用户数据
      userTableTotal: 0, // 用户数据总数
      userSelectData: [], // 用户选中数据
      clearData: false,
    }
  },
  // -------------------- 计算属性 --------------------
  computed: {
    // 沥青路面勾选状态
    asphaltSelect() {
      if (this.formParams.asphaltDamagedRoadbedArea > 0) {
        return true
      }
      if (this.formParams.asphaltDamagedRoadbedLength > 0) {
        return true
      }
      if (this.formParams.asphaltDamagedFullAmount > 0) {
        return true
      }
      return false
    },
    // 水泥路面勾选状态
    cementSelect() {
      if (this.formParams.cementDamagedRoadbedArea > 0) {
        return true
      }
      if (this.formParams.cementDamagedRoadbedLength > 0) {
        return true
      }
      if (this.formParams.cementDamagedFullAmount > 0) {
        return true
      }
      return false
    },
    // 砂石路面勾选状态
    dinasSelect() {
      if (this.formParams.dinasDamagedRoadbedArea > 0) {
        return true
      }
      if (this.formParams.dinasDamagedRoadbedLength > 0) {
        return true
      }
      if (this.formParams.dinasDamagedFullAmount > 0) {
        return true
      }
      return false
    },
    // 护坡勾选状态
    revetmentSelect() {
      if (this.formParams.revetmentDamagedVolume > 0) {
        return true
      }
      if (this.formParams.revetmentDamagedNumber > 0) {
        return true
      }
      if (this.formParams.revetmentDamagedFullAmount > 0) {
        return true
      }
      return false
    },
    // 驳岸、挡墙勾选状态
    barricadeSelect() {
      if (this.formParams.barricadeDamagedVolume > 0) {
        return true
      }
      if (this.formParams.barricadeDamagedNumber > 0) {
        return true
      }
      if (this.formParams.barricadeDamagedFullAmount > 0) {
        return true
      }
      return false
    },

    // 灾毁图片时间线
    formImgDateLine1() {
      let dateLine = []
      if (this.formImgSrcList1.length > 0) {
        this.formImgSrcList1.forEach(item => {
          let date = item.createTime.split('T')[0]
          if (dateLine.length === 0) {
            dateLine.push({
              time: date,
              data: [item]
            })
          } else {
            let index = dateLine.findIndex(item2 => item2.time === date)
            if (index !== -1) {
              dateLine[index].data.push(item)
            } else {
              dateLine.push({
                time: date,
                data: [item]
              })
            }
          }
        })
        // 时间线排序
        dateLine.sort((a, b) => {
          // 将日期字符串分割并转换为日期对象
          let dateA = new Date(a.time);
          let dateB = new Date(b.time);

          // 比较日期
          return dateA - dateB;
        })
      }
      console.log('组装的时间线图片1：', dateLine)

      return dateLine
    },

    // 抢通后图片时间线
    formImgDateLine2() {
      let dateLine = []
      if (this.formImgSrcList2.length > 0) {
        this.formImgSrcList2.forEach(item => {
          let date = item.createTime.split('T')[0]
          if (dateLine.length === 0) {
            dateLine.push({
              time: date,
              data: [item]
            })
          } else {
            let index = dateLine.findIndex(item2 => item2.time === date)
            if (index !== -1) {
              dateLine[index].data.push(item)
            } else {
              dateLine.push({
                time: date,
                data: [item]
              })
            }
          }
        })
        // 时间线排序
        dateLine.sort((a, b) => {
          // 将日期字符串分割并转换为日期对象
          let dateA = new Date(a.time);
          let dateB = new Date(b.time);

          // 比较日期
          return dateA - dateB;
        })
      }
      console.log('组装的时间线图片2：', dateLine)

      return dateLine
    },
    //控制表格头部修改按钮
    tableEditDisabled() {
      if (this.tableSelection.length === 1) {
        if (this.tableSelection[0].status !== '0') {
          return true
        } else {
          return false
        }
      }
      return true
    },
    //控制表头部删除按钮
    tableDelDisabled() {
      let reviewIndex = this.tableSelection.findIndex((item) => item.status === '1');
      let finishIndex = this.tableSelection.findIndex((item) => item.status === '2');
      if (this.tableSelection.length > 0 && reviewIndex === -1 && finishIndex === -1) {
        return false
      }
      return true
    },
    isButtonDisabled() {
      return (id) => {
        if (this.businessKeyArr.length === 0) {
          return true
        }
        return !this.businessKeyArr.includes(id);
      };
    }
  },
  // -------------------- 生命周期 --------------------
  mounted() {
    this.initPage()
  },
  // -------------------- 方法 --------------------
  methods: {

    /**
     * 页面相关
     */
    // 初始化页面
    initPage() {
      // 获取数据
      this.queryList()
      // 获取待办任务业务ID集合
      this.queryTodoTaskByCategory()
      // 查询字典类型数据
      this.queryType()
      // 获取管养单位数据
      this.queryMaintenanceList()
      // 获取区划数据（市级）
      this.queryDivisionsTree(2)
      // 获取灾毁设施类型字典
      this.queryDamageFacilityDict()
    },

    queryType() {
      this.getDicts("process_type").then((res) => {
        if (res.code === 200) {
          this.formProcessType = res.data
        }
      })
    },

    /**
     * 查询相关
     */
    // 点击查询按钮
    queryhandle() {
      this.queryList()
    },

    // 重置查询条件
    queryReset() {
      this.clearData = true
      this.queryParams = { // 查询参数
        pageNum: 1, // 页码
        pageSize: 10, // 每页条数
        managementMaintenanceIds: [], // 管养处
        maintenanceSectionId: '', // 路段编号
        routeCodes: [], // 路线编号
        disasterSectionId: '', // 灾毁段编号
        disasterType: '', // 关联风险点类型/灾毁类型
        pileStartNum: '', // 起始桩号
        pileEndNum: '', // 结束桩号
        startTime: '', // 开始时间
        endTime: '', // 结束时间
        status: '' // 状态
      }
      this.queryhandle();
    },

    // 查询条件改变
    queryParamsUpdate(val) {
      console.log('查询条件变更：', val)
      console.log('当前查询条件：', this.queryParams)
    },

    // 获取灾毁数据
    async queryList() {
      this.tableLoading = true
      await queryPageDamage(this.queryParams).then((res) => {
        console.log('获取灾毁数据：', res)
        if (res.code === 200) {
          this.tableData = res.rows
          this.queryTotal = res.total
          this.clearData = false
        }
        this.tableLoading = false
      }).catch((err) => {
        this.tableLoading = false
        this.$message.error(err)
      })
    },

    // 获取关联风险点数据
    async queryRiskData() {
      this.riskFormLoading = true
      this.riskFormDetailLoading = true
      console.log('关联风险点ID：', this.formParams.disasterRiskId)
      await queryPageRiskById(this.formParams.disasterRiskId).then((res) => {
        if (res.code == 200) {
          this.riskFormParams = res.data.disasterRisk
          this.riskFormSubParams = res.data.data
        }
        this.riskFormLoading = false
        this.riskFormDetailLoading = false
      }).catch(() => {
        this.riskFormLoading = false
        this.riskFormDetailLoading = false
      })
    },

    // 获取风险点详情并渲染
    queryRiskDetail() {
      if (!this.formParams.disasterRiskId) {
        this.$message.warning('请先选择关联风险点')
        return
      }
      this.queryRiskData()
      this.riskFormDialog = true
      this.$nextTick(() => {
        this.$refs.formSubRef.initPage(this.riskFormSubParams, this.riskFormParams.disasterType)
      })
    },

    // 获取灾毁数据
    async queryTodoTaskByCategory() {
      await queryTodoTaskByCategory("灾毁上报").then((res) => {
        if (res.code === 200) {
          this.businessKeyArr = res.data
        }
      }).catch((err) => {
        this.$message.error(err)
      })
    },

    // 获取管养单位
    queryMaintenanceList() {
      let vo = {
        deptTypeList: [1, 3, 4],
        types: 100
      }
      deptTreeSelect(vo).then((res) => {
        console.log('养护路段数据：', res)
        if (res.code === 200) {
          this.handleMainRender(res.data)
        }
      }).catch((err) => {
        this.$message.error(err)
        this.formMaintenanceList = []
      })
    },

    // 管养单位渲染列表处理
    handleMainRender(data) {
      data.forEach(item => {
        this.formMaintenanceList.push(item)
        if (item.children) {
          this.handleMainRender(item.children)
        }
      })
    },

    // 获取路段信息
    queryMaintenanceSectionList(val) {
      listMaintenanceSectionAll({departmentIdList: val}).then((res) => {
        console.log('获取路段数据：', res)
        if (res.code === 200) {
          this.formRoadSectionList = res.data
        }
      }).catch((err) => {
        this.$message.error(err)
        this.formRoadSectionList = []
      })
    },

    // 获取路线信息
    queryRouterList(val) {
      listByMaintenanceSectionId({maintenanceSectionId: val}).then(res => {
        console.log('获取路线信息：', res)
        if (res.code == 200) {
          this.formRouteList = res.data
        }
      }).catch((err) => {
        this.$message.error(err)
        this.formRouteList = []
      })
    },

    // 获取区划树
    queryDivisionsTree(level) {
      let vo = {}
      switch (level) {
        case 2:
          vo = {
            supCode: this.divisionsProvincial
          }
          break;

        case 3:
          vo = {
            supCode: this.divisionsMunicipal
          }
          break;
      }

      getTreeByEntity(vo).then((res) => {
        console.log('区划数据：', res)
        if (res) {
          switch (level) {
            case 2:
              this.divisionsMunicipalList = res.data
              break;

            case 3:
              this.areaCodeList = res.data
              break;
          }
        }
      })
    },

    // 获取图片数据（灾毁图片）
    async queryImg1(id) {
      await findFiles({ownerId: id}).then((res) => {
        if (res.code === 200 && res.data.length > 0) {

          this.formImgSrcList1 = res.data.map(item => ({
            id: item.ownerId + '-' + item.id,
            name: item.originalFilename,
            url: item.thumbUrl,
            imgUrl: item.url,
            remark: item.remark,
            createTime: item.createTime,
            createBy: item.createBy
          }))
          this.formImgUrlList1 = res.data.map(item => item.url)

        }
      }).catch(() => {
        this.$message.error('图片查询失败，请重新打开表单尝试')
      })
    },

    // 获取图片数据（抢通后图片）
    async queryImg2(id) {
      await findFiles({ownerId: id}).then((res) => {
        if (res.code === 200 && res.data.length > 0) {

          this.formImgSrcList2 = res.data.map(item => ({
            id: item.ownerId + '-' + item.id,
            name: item.originalFilename,
            url: item.thumbUrl,
            imgUrl: item.url,
            remark: item.remark,
            createTime: item.createTime,
            createBy: item.createBy
          }))
          this.formImgUrlList2 = res.data.map(item => item.url)

        }
      }).catch(() => {
        this.$message.error('图片查询失败，请重新打开表单尝试')
      })
    },

    // 获取文件数据（公函）
    async queryFile(id) {
      await findFiles({ownerId: id}).then((res) => {
        if (res.code === 200 && res.data.length > 0) {

          this.formFileSrcList = res.data.map(item => ({
            id: item.ownerId + '-' + item.id,
            name: item.originalFilename,
            url: item.thumbUrl,
            imgUrl: item.url,
            remark: item.remark,
            createTime: item.createTime,
            createBy: item.createBy
          }))
          this.formImgUrlList = res.data.map(item => item.url)

        }
      }).catch(() => {
        this.$message.error('图片查询失败，请重新打开表单尝试')
      })
    },

    // 获取灾毁设施类型字典
    queryDamageFacilityDict() {
      return new Promise((resolve, reject) => {
        this.disasterFacilitiesData = []
        this.getDicts("damage_facility_type").then((res) => {
          console.log('获取的灾毁类型字典：', res)
          if (res.code === 200) {
            res.data.forEach((item) => {
              this.disasterFacilitiesData.push(item.dictValue)
            })
            console.log('填充的字典值：', this.disasterFacilitiesData)
            resolve(true)
          } else {
            reject(false)
          }
        }).catch(() => {
          reject(false)
        })
      })
    },

    /**
     * 表格相关
     */
    // 勾选表格项改变时
    tableSelectionChange(val) {
      this.tableSelection = val
    },

    // 点击一行时
    tableRowClick(val) {
      this.$refs.tableRef.toggleRowSelection(val)
    },

    /**
     * 表单相关
     */


    // 点击修改时
    formEdit(type, item) {
      let val = null
      switch (type) {
        case 'select':
          val = this.tableSelection[0]
          break;
        case 'click':
          val = item
          break;
      }
      this.formDialog = true
      this.formRiskId = val.id
      this.formInit('edit', val)
    },

    formView(item) {
      this.formRiskId = item.id
      this.formInit('view', item)
      this.formDialog = true
    },

    // 初始化表单
    async formInit(type, item, riskType) {
      console.log('主表数据：', item)
      this.formType = type
      switch (type) {
        case 'add':
          // 重置表单
          this.formParams = {
            id: '', // 主表ID
            disasterType: '', // 灾毁类型
            disasterRiskId: '', // 风险点ID
            disasterSectionId: '', // 灾毁段编号
            startLatitudeAndLongitude: '', // 经纬度（起）
            endLatitudeAndLongitude: '', // 经纬度（止）
            pileStartNum: '', // 桩号（起）
            pileEndNum: '', // 桩号（终）
            roadbedWidth: '', // 路面宽度
            roadbedType: '', // 路面类型
            disasterStartTime: '', // 灾毁发生时间
            interrupt: '', // 是否中断
            disasterFacilitiesType: [], // 灾毁设施类型
            // 路基
            damagedRoadbedVolume: 0, // 损毁路基方量
            damagedRoadbedLength: 0, // 损毁路基长度
            damagedFullAmount: 0, // 损失全额
            // 路面
            asphaltDamagedRoadbedArea: 0, // 损毁路面面积-沥青路面
            asphaltDamagedRoadbedLength: 0, // 损毁路面长度-沥青路面
            asphaltDamagedFullAmount: 0, // 损失全额-沥青路面

            cementDamagedRoadbedArea: 0, // 损毁路面面积-水泥路面
            cementDamagedRoadbedLength: 0, // 损毁路面长度-水泥路面
            cementDamagedFullAmount: 0, // 损失全额-水泥路面

            dinasDamagedRoadbedArea: 0, // 损毁路面面积-砂石路面
            dinasDamagedRoadbedLength: 0, // 损毁路面长度-砂石路面
            dinasDamagedFullAmount: 0, // 损失全额-砂石路面
            // 桥梁
            bridgeDamagedType: '', // 桥梁毁坏类型
            bridgeDamagedLength: 0, // 损毁长度-桥梁
            bridgeDamagedNumber: 0, // 损毁数量-桥梁
            bridgeDamagedFullAmount: 0, // 损毁全额-桥梁
            // 隧道
            tunnelDamagedLength: 0, // 损毁长度-隧道
            tunnelDamagedNumber: 0, // 损毁数量-隧道
            tunnelDamagedFullAmount: 0, // 损毁全额-隧道
            // 涵洞
            culvertDamagedNumber: 0, // 损毁数量-涵洞
            culvertDamagedFullAmount: 0, // 损毁全额-涵洞
            // 防护工程
            revetmentDamagedVolume: 0, // 损毁方量-坡护
            revetmentDamagedNumber: 0, // 损毁数量-坡护
            revetmentDamagedFullAmount: 0, // 损毁全额-坡护

            barricadeDamagedVolume: 0, // 损毁方量-驳岸、挡墙
            barricadeDamagedNumber: 0, // 损毁数量-驳岸、挡墙
            barricadeDamagedFullAmount: 0, // 损毁全额-驳岸、挡墙
            // 坍塌方
            collapseDamagedVolume: 0, // 损毁方量-坍塌方
            collapseDamagedNumber: 0, // 损毁数量-坍塌方
            collapseDamagedFullAmount: 0, // 损毁全额-坍塌方
            // 其它
            otherRemarks: '', // 其它备注
            otherDamagedAmount: 0, // 莫他灾毁损失
            // 合计
            totalLoss: 0, // 损失合计
            disasterImgIds: '', // 灾毁照片
            alreadySnatchedThrough: '', // 是否已抢通
            actualRushTimeExpected: '', // 预计抢通时间
            disasterLevel: '', // 灾毁等级
            remarks: '', // 备注
            // 应急抢通情况
            actualRushTime: '', // 实际抢通时间
            handled: '', // 是否已处置
            disposalTimeExpected: '', // 处置时间
            inputLaborExpected: 0, // 投入人工
            inputMachineryExpected: 0, // 投入机械
            inputFundExpected: 0, // 投入资金
            snatchedThroughImgIds: '', // 抢通后照片
            // 公函
            officialLetter: '', // 公函
            // 其它字段
            processId: '', // 流程ID
            dataSources: 'PC', // 数据来源
            status: '', // 状态
            createName: '', // 填报人名称
            createUnit: '', // 填报人单位
            createTime: '', // 填报时间
          }
          // 桩号重置
          this.pileStartK = ''
          this.pileStartAdd = ''
          this.pileEndK = ''
          this.pileEndAdd = ''
          // 重置市级区划下拉
          this.divisionsMunicipal = ''
          this.areaCodeList = []
          // 重置路段/路线下拉
          this.formRoadSectionList = []
          this.formRouteList = []
          // 重置图片
          // 灾毁照片
          this.formImgSrcList1 = [] // 图片缩略图列表
          this.formImgUrlList1 = [] // 图标预览图列表
          this.formUploadList1 = '' // 图片上传列表
          this.formImgOwnerId1 = 'ZH' + new Date().getTime().toString() // 图片上传ownerId1
          this.disasterImgIds = this.formImgOwnerId1

          // 抢通后照片
          this.formImgSrcList2 = [] // 图片缩略图列表
          this.formImgUrlList2 = [] // 图标预览图列表
          this.formUploadList2 = '' // 图片上传列表
          this.formImgOwnerId2 = 'QT' + new Date().getTime().toString() // 图片上传ownerId2
          this.snatchedThroughImgIds = this.formImgOwnerId2

          // 公函
          this.formFileSrcList = [] // 文件渲染列表
          this.formUploadList3 = '' // 文件上传列表
          this.formFileOwnerId = 'GH' + new Date().getTime().toString() // 文件上传ownerId
          this.officialLetter = this.formFileOwnerId

          this.formImgNum = [], // 图片数量（图片集用）
            // 打开表单dialog
            this.formDialog = true
          this.$nextTick(() => {
            // 清除主表表单验证
            this.$refs.formRef.clearValidate()
          })
          break;

        case 'edit':
        case 'view':
          // 表单赋值
          this.formParams = {
            id: item.id, // 主表ID
            disasterType: item.disasterType, // 灾毁类型
            disasterRiskId: item.disasterRiskId, // 风险点ID
            disasterSectionId: item.disasterSectionId, // 灾毁段编号
            startLatitudeAndLongitude: item.startLatitudeAndLongitude, // 经纬度（起）
            endLatitudeAndLongitude: item.endLatitudeAndLongitude, // 经纬度（止）
            pileStartNum: item.pileStartNum, // 桩号（起）
            pileEndNum: item.pileEndNum, // 桩号（终）
            roadbedWidth: item.roadbedWidth, // 路面宽度
            roadbedType: item.roadbedType, // 路面类型
            disasterStartTime: item.disasterStartTime, // 灾毁发生时间
            interrupt: item.interrupt, // 是否中断
            disasterFacilitiesType: this.formArrayChange(item.disasterFacilitiesType), // 灾毁设施类型
            // 路基
            damagedRoadbedVolume: item.damagedRoadbedVolume, // 损毁路基方量
            damagedRoadbedLength: item.damagedRoadbedLength, // 损毁路基长度
            damagedFullAmount: item.damagedFullAmount, // 损失全额
            // 路面
            asphaltDamagedRoadbedArea: item.asphaltDamagedRoadbedArea, // 损毁路面面积-沥青路面
            asphaltDamagedRoadbedLength: item.asphaltDamagedRoadbedLength, // 损毁路面长度-沥青路面
            asphaltDamagedFullAmount: item.asphaltDamagedFullAmount, // 损失全额-沥青路面

            cementDamagedRoadbedArea: item.cementDamagedRoadbedArea, // 损毁路面面积-水泥路面
            cementDamagedRoadbedLength: item.cementDamagedRoadbedLength, // 损毁路面长度-水泥路面
            cementDamagedFullAmount: item.cementDamagedFullAmount, // 损失全额-水泥路面

            dinasDamagedRoadbedArea: item.dinasDamagedRoadbedArea, // 损毁路面面积-砂石路面
            dinasDamagedRoadbedLength: item.dinasDamagedRoadbedLength, // 损毁路面长度-砂石路面
            dinasDamagedFullAmount: item.dinasDamagedFullAmount, // 损失全额-砂石路面
            // 桥梁
            bridgeDamagedType: item.bridgeDamagedType, // 桥梁毁坏类型
            bridgeDamagedLength: item.bridgeDamagedLength, // 损毁长度-桥梁
            bridgeDamagedNumber: item.bridgeDamagedNumber, // 损毁数量-桥梁
            bridgeDamagedFullAmount: item.bridgeDamagedFullAmount, // 损毁全额-桥梁
            // 隧道
            tunnelDamagedLength: item.tunnelDamagedLength, // 损毁长度-隧道
            tunnelDamagedNumber: item.tunnelDamagedNumber, // 损毁数量-隧道
            tunnelDamagedFullAmount: item.tunnelDamagedFullAmount, // 损毁全额-隧道
            // 涵洞
            culvertDamagedNumber: item.culvertDamagedNumber, // 损毁数量-涵洞
            culvertDamagedFullAmount: item.culvertDamagedFullAmount, // 损毁全额-涵洞
            // 防护工程
            revetmentDamagedVolume: item.revetmentDamagedVolume, // 损毁方量-坡护
            revetmentDamagedNumber: item.revetmentDamagedNumber, // 损毁数量-坡护
            revetmentDamagedFullAmount: item.revetmentDamagedFullAmount, // 损毁全额-坡护

            barricadeDamagedVolume: item.barricadeDamagedVolume, // 损毁方量-驳岸、挡墙
            barricadeDamagedNumber: item.barricadeDamagedNumber, // 损毁数量-驳岸、挡墙
            barricadeDamagedFullAmount: item.barricadeDamagedFullAmount, // 损毁全额-驳岸、挡墙
            // 坍塌方
            collapseDamagedVolume: item.collapseDamagedVolume, // 损毁方量-坍塌方
            collapseDamagedNumber: item.collapseDamagedNumber, // 损毁数量-坍塌方
            collapseDamagedFullAmount: item.collapseDamagedFullAmount, // 损毁全额-坍塌方
            // 其它
            otherRemarks: item.otherRemarks, // 其它备注
            otherDamagedAmount: item.otherDamagedAmount, // 莫他灾毁损失
            // 合计
            totalLoss: item.totalLoss, // 损失合计
            disasterImgIds: item.disasterImgIds, // 灾毁照片
            alreadySnatchedThrough: item.alreadySnatchedThrough, // 是否已抢通
            actualRushTimeExpected: item.actualRushTimeExpected, // 预计抢通时间
            disasterLevel: item.disasterLevel, // 灾毁等级
            remarks: item.remarks, // 备注
            // 应急抢通情况
            actualRushTime: item.actualRushTime, // 实际抢通时间
            handled: item.handled, // 是否已处置
            disposalTimeExpected: item.disposalTimeExpected, // 处置时间
            inputLaborExpected: item.inputLaborExpected, // 投入人工
            inputMachineryExpected: item.inputMachineryExpected, // 投入机械
            inputFundExpected: item.inputFundExpected, // 投入资金
            snatchedThroughImgIds: item.snatchedThroughImgIds, // 抢通后照片
            // 公函
            officialLetter: item.officialLetter, // 公函
            // 其它字段
            processId: item.processId, // 流程ID
            dataSources: 'PC', // 数据来源
            status: item.status, // 状态
            createName: item.createName, // 填报人名称
            createUnit: item.createUnit, // 填报人单位
            createTime: item.createTime, // 填报时间
          }
          //修改时只显示返回的灾毁类型数据
          this.riskQueuryParams.disasterType = item.disasterType

          // 根据关联风险点ID查询数据
          await this.queryRiskData()

          if (!this.formParams.disasterSectionId) {
            this.formParams.disasterSectionId = this.riskFormParams.disasterNum + '-' + moment(this.formParams.createTime).format('YYYYMMDD')
          }
          this.formImgNum = [] // 图片集数量
          // 重置图片渲染及上传接收列表（灾毁图片）
          this.formImgSrcList1 = []
          this.formImgUrlList1 = []
          this.formUploadList1 = '' // 图片上传列表

          if (this.formParams.disasterImgIds) {
            await this.queryImg1(this.formParams.disasterImgIds)
            this.formImgOwnerId1 = this.formParams.disasterImgIds
          } else {
            this.formImgOwnerId1 = 'ZH' + new Date().getTime().toString()
            this.formParams.disasterImgIds = this.formImgOwnerId1
          }

          // 重置图片渲染及上传接收列表（抢通后图片）
          this.formImgSrcList2 = []
          this.formImgUrlList2 = []
          this.formUploadList2 = '' // 图片上传列表

          if (this.formParams.snatchedThroughImgIds) {
            await this.queryImg2(this.formParams.snatchedThroughImgIds)
            this.formImgOwnerId2 = this.formParams.snatchedThroughImgIds
          } else {
            this.formImgOwnerId2 = 'QT' + new Date().getTime().toString()
            this.formParams.snatchedThroughImgIds = this.formImgOwnerId2
          }

          // 重置公函渲染及上传接收列表
          this.formFileSrcList = [] // 文件渲染列表
          this.formUploadList3 = '' // 文件上传列表

          if (this.formParams.officialLetter) {
            await this.queryFile(this.formParams.officialLetter)
            this.formFileOwnerId = this.formParams.officialLetter
          } else {
            this.formFileOwnerId = 'GH' + new Date().getTime().toString()
            this.formParams.officialLetter = this.formFileOwnerId
          }

          // 打开dialog
          this.formDialog = true
          this.$nextTick(() => {
            // 清除主表表单验证
            this.$refs.formRef.clearValidate()
          })
          break;
      }

      // 解决单选框报错问题（谷歌浏览器升级导致）
      await this.$nextTick(() => {
        for (let radio in this.$refs) {
          let refName = 'formRadio'
          let check = radio.includes(refName)
          if (check) {
            this.$refs[radio].$children.forEach((item) => {
              item.$refs.radio.removeAttribute("aria-hidden");
            });
          }
        }
      })
    },
    // 提交表单
    async formSubmit(formName, statusNum) {
      // 主表流程状态
      this.formParams.status = statusNum
      this.formBtnLoading = true

      // 创建提交参数
      let vo = JSON.parse(JSON.stringify(this.formParams))

      // 转换数组对象
      vo.disasterFacilitiesType = Array.isArray(vo.disasterFacilitiesType) ? vo.disasterFacilitiesType.join(",") : ''
      switch (statusNum) {
        case '0':
          switch (this.formType) {
            case 'add':
              await createDamage(vo).then((res) => {
                console.log('数据新增结果：', res)
                if (res.code === 200) {
                  this.$message.success('操作成功！')
                  this.formBtnLoading = false
                  this.formDialog = false
                  this.queryList()
                }
              }).catch((err) => {
                this.$message.error(err)
                this.formBtnLoading = false
              })
              break;

            case 'edit':
              await updateDamage(vo).then((res) => {
                console.log('数据修改结果：', res)
                if (res.code === 200) {
                  this.$message.success('操作成功！')
                  this.formBtnLoading = false
                  this.formDialog = false
                  this.queryList()
                }
              }).catch((err) => {
                this.$message.error(err)
                this.formBtnLoading = false
              })
              break;
          }
          break;

        case '1':
          this.$refs[formName].validate(async (valid) => {
            if (valid) {
              switch (this.formType) {
                case 'add':
                  await createDamage(vo).then((res) => {
                    console.log('数据新增结果：', res)
                    if (res.code === 200) {
                      this.$message.success('操作成功！')
                      this.formBtnLoading = false
                      this.formDialog = false
                      this.queryList()
                    }
                  }).catch((err) => {
                    this.$message.error(err)
                    this.formBtnLoading = false
                  })
                  break;

                case 'edit':
                  await updateDamage(vo).then((res) => {
                    console.log('数据修改结果：', res)
                    if (res.code === 200) {
                      this.$message.success('操作成功！')
                      this.formBtnLoading = false
                      this.formDialog = false
                      this.queryList()
                    }
                  }).catch((err) => {
                    this.$message.error(err)
                    this.formBtnLoading = false
                  })
                  break;
              }
            } else {
              this.$message.warning('表单必填字段未填写！')
              return false
            }
          })
          break;
      }

    },

    // 删除表单项
    formDelete(type, item) {
      let deleteId = ''
      switch (type) {
        case 'batch':
          let ids = []
          this.tableSelection.forEach((row) => {
            ids.push(row.id)
          })
          deleteId = ids.join(',')
          break;
        case 'single':
          deleteId = item.id
          break;
      }
      this.$modal
        .confirm('确认删除？')
        .then(async () => {
          this.tableLoading = true
          console.log('删除参数：', deleteId)
          await deleteDamage(deleteId).then(res => {
            if (res && res.code == '200') {
              this.queryList()
              this.$message.success('操作成功！')
            }
            this.tableLoading = false
          }).catch((err) => {
            this.$message.error(err)
            this.tableLoading = false
          })
        })
    },

    async formAudit(row) {
      try {
        // 查询待办任务
        const res = await queryTodoTaskByBusinessKey(row.id);

        // 检查响应结果
        if (res.code !== 200) {
          this.$message.error('查询待办任务失败，请稍后重试');
          return;
        }

        // 判断是否有任务数据
        if (!res.data) {
          this.$message.warning('未查询到待办任务信息');
          return;
        }

        // 动态解析流程表单路径
        const routePath = await this.formCheckTyoe(res.data.processDefinitionKey);
        if (!routePath) {
          this.$message.warning('未查询到流程表单信息');
          return;
        }

        // 构建路由并打开新窗口
        this.openAuditForm(res.data, routePath);
      } catch (error) {
        // 捕获并处理异常
        console.error('formAudit error:', error);
        this.$message.error('执行操作时发生异常，请稍后重试');
      }
    },

    /**
     * 打开流程表单页面
     * @param {Object} taskData - 待办任务数据
     * @param {string} routePath - 动态路由名称
     */
    openAuditForm(taskData, routePath) {
      const routeUrl = this.$router.resolve({
        name: routePath,
        query: {
          taskId: taskData.taskId,
          businessKey: taskData.businessKey,
          processInstanceId: taskData.processInstanceId,
          taskKey: taskData.taskKey,
          route: routePath,
          name: taskData.category,
          title: taskData.title,
          isApprove: '0',
        },
      });

      // 在新标签页打开
      window.open(routeUrl.href, '_blank');
    },

    // 确认流程类型
    formCheckTyoe(type) {
      let check = this.formProcessType.findIndex(item => item.dictValue === type)
      if (check !== -1) {
        return this.formProcessType[check].remark
      } else {
        return null
      }
    },

    async formAuditInfo(item) {
      this.processId = item.processId
      const auditData = await this.fetchAuditInfo(item.processId);
      if (auditData) {
        const data = await this.formIdeaChange(auditData);
        this.formIdeaList = data;
      }
      this.auditInfoDialog = true;
    },

    async fetchAuditInfo(processId) {
      try {
        const res = await queryAuditInfo(processId);
        if (res.code === 200) {
          return res.data;
        } else {
          throw new Error(res.message || '获取审核信息失败');
        }
      } catch (err) {
        this.$message.error(err.message || err);
        return null;
      }
    },

    updateNodeTime(item) {
      this.updateTimeForm = {
        id: '',
        nodeName: '',
        hisStartTime: '',
        hisEndTime: '',
        startTime: '',
        endTime: '',
        oldAssignee: '',
        assignee: '',
        oldContent: '',
        content: '',
        taskId: ''
      }
      this.updateTimeForm.id = item.id
      this.updateTimeForm.nodeName = item.nodeName
      this.updateTimeForm.hisStartTime = item.startTime
      this.updateTimeForm.hisEndTime = item.endTime
      this.updateTimeForm.oldAssignee = item.assignee
      // 直接访问 comment 字段
      if (item.content && typeof item.content === 'object') {
        this.updateTimeForm.oldContent = item.content.comment || '';
      } else {
        console.error("item.content 不是一个有效的对象:", item.content);
        this.updateTimeForm.oldContent = '';
      }
      this.updateTimeForm.taskId = item.taskId
      this.updateTimeDialog = true
    },

    userDialogOpen() {
      this.userList()
      this.userDialog = true
    },

    userList() {
      this.userTableLoading = true
      listUser(this.userParams).then((res) => {
        if(res.code === 200) {
          this.userTableData = res.rows
          this.userTableTotal = res.total
        }
        this.userTableLoading = false
      }).catch(() => {
        this.userTableLoading = false
      })
    },

    userSelect(item) {
      this.userSelectData = []
      this.userSelectData.push ({ value: item.userId, label: item.nickName })
      this.updateTimeForm.assignee = item.nickName + '@' + item.userId
      this.userDialog = false
    },

    async subUpdateTime() {
        try {
          this.updateTimeFormBtnLoading = true;

          const res = await updateNodeInfo(this.updateTimeForm);
          if (res.code === 200) {
            this.$message.success("更新节点信息成功");
          } else {
            throw new Error(res.msg || '更新节点信息失败');
          }

          // 获取审核信息并更新表单
          const auditData = await this.fetchAuditInfo(this.processId);
          if (auditData) {
            const data = await this.formIdeaChange(auditData);
            this.formIdeaList = data;
          }

          // 关闭弹窗
          this.updateTimeDialog = false;

        } catch (err) {
          this.$message.error(err.message || err);
        } finally {
          // 确保加载状态在任何情况下都会被重置
          this.updateTimeFormBtnLoading = false;
        }
    },

    // 处理意见字段
    formIdeaChange(data) {
      return new Promise((resolve) => {
        for (let i = 0; i < data.length; i++) {
          if (data[i].content && data[i].content !== '无内容') {
            data[i].content = JSON.parse(data[i].content)
            if (data[i].content.operatingRecord) {
              data[i].content.operatingRecord = JSON.parse(data[i].content.operatingRecord)
            }
          }
          if (i === data.length - 1) {
            resolve(data)
          }
        }
      })
    },

    // 导出表单
    formExport() {
      // 深拷贝 queryParams
      let vo = {...this.queryParams};

      // 检查是否有选中的数据行
      const isAllData = this.tableSelection.length === 0;

      // 根据是否选择数据行来设置提示消息和导出参数
      const message = isAllData
        ? '即将根据查询条件导出所有灾毁填报数据，此过程可能花费时间较长，是否继续？'
        : `已选择${this.tableSelection.length}条灾毁填报数据，确认导出数据？`;

      if (!isAllData) {
        // 仅导出选中项的情况，将选中项的 id 加入参数
        vo.ids = this.tableSelection.map(row => row.id);
      }

      // 显示确认提示，并在确认后调用下载方法
      this.$modal
        .confirm(message)
        .then(() => {
          const filename = `灾毁填报_${new Date().getTime()}.xlsx`;
          this.download(
            '/disaster/submitted/export',
            vo,
            filename
          );
        })
    },

    // 关闭表单
    formClose() {
      this.formParams.disasterType = ''
      this.formDialog = false
    },

    // 当灾毁设施类型改变时
    formFacilitiesChange() {
      console.log('灾毁类型改变：', this.formParams.disasterFacilitiesType)
      if (this.disasterFacilitiesData === 0) {
        this.queryDamageFacilityDict().then(() => {
          this.formWwitchFacilities()
        }).catch(() => {
          return
        })
      } else {
        this.formWwitchFacilities()
      }
    },

    // 校对灾毁设施类型数据并重置未勾选的选项内的值
    formWwitchFacilities() {
      let facilitiesOptions = new Set(this.formParams.disasterFacilitiesType)

      let facilitiesUnCheck = this.disasterFacilitiesData.filter(x => !facilitiesOptions.has(x))
      console.log('未勾选的值：', facilitiesUnCheck)
      for (let i of facilitiesUnCheck) {
        switch (i) {
          case '1':
            // 路基
            this.formParams.damagedRoadbedVolume = 0
            this.formParams.damagedRoadbedLength = 0
            this.formParams.damagedFullAmount = 0
            break;

          case '2':
            // 沥青路面
            this.formParams.asphaltDamagedRoadbedArea = 0
            this.formParams.asphaltDamagedRoadbedLength = 0
            this.formParams.asphaltDamagedFullAmount = 0
            // 水泥路面
            this.formParams.cementDamagedRoadbedArea = 0
            this.formParams.cementDamagedRoadbedLength = 0
            this.formParams.cementDamagedFullAmount = 0
            // 砂石路面
            this.formParams.dinasDamagedRoadbedArea = 0
            this.formParams.dinasDamagedRoadbedLength = 0
            this.formParams.dinasDamagedFullAmount = 0
            break;

          case '3':
            // 桥梁
            this.formParams.bridgeDamagedType = ''
            this.formParams.bridgeDamagedLength = 0
            this.formParams.bridgeDamagedNumber = 0
            this.formParams.bridgeDamagedFullAmount = 0
            break;

          case '4':
            // 隧道
            this.formParams.tunnelDamagedLength = 0
            this.formParams.tunnelDamagedNumber = 0
            this.formParams.tunnelDamagedFullAmount = 0
            break;

          case '5':
            // 涵洞
            this.formParams.culvertDamagedNumber = 0
            this.formParams.culvertDamagedFullAmount = 0
            break;

          case '6':
            // 坡护
            this.formParams.revetmentDamagedVolume = 0
            this.formParams.revetmentDamagedNumber = 0
            this.formParams.revetmentDamagedFullAmount = 0
            // 驳岸、挡墙
            this.formParams.barricadeDamagedVolume = 0
            this.formParams.barricadeDamagedNumber = 0
            this.formParams.barricadeDamagedFullAmount = 0
            break;

          case '7':
            // 坍塌方
            this.formParams.collapseDamagedVolume = 0
            this.formParams.collapseDamagedNumber = 0
            this.formParams.collapseDamagedFullAmount = 0
            break;

          case '99':
            // 其它
            this.formParams.otherRemarks = ''
            this.formParams.otherDamagedAmount = 0
            break;
        }
      }
      this.formtotalCompute()
    },

    // 表单损失合计计算
    formtotalCompute() {
      let total = 0
      total = this.formParams.damagedFullAmount + this.formParams.asphaltDamagedFullAmount +
        this.formParams.cementDamagedFullAmount + this.formParams.dinasDamagedFullAmount +
        this.formParams.bridgeDamagedFullAmount + this.formParams.tunnelDamagedFullAmount +
        this.formParams.culvertDamagedFullAmount + this.formParams.revetmentDamagedFullAmount +
        this.formParams.barricadeDamagedFullAmount + this.formParams.collapseDamagedFullAmount +
        this.formParams.otherDamagedAmount
      this.formParams.totalLoss = total
    },

    // 管养单位/路段改变时
    formMaintenanceChange(type) {
      switch (type) {
        case '1':
          // 获取管养单位名称
          this.formGetName('1', this.formParams.maintenanceUnitId)
          // 重置路段及路线数据
          this.formParams.roadSectionId = ''
          this.formParams.roadSectionName = ''
          this.formRoadSectionList = []
          this.formParams.routerNum = ''
          this.formParams.routerName = ''
          this.formRouteList = []
          // 重置路线等级
          this.formParams.technicalGrade = ''
          // 重新获取路段数据列表
          this.queryMaintenanceSectionList(this.formParams.maintenanceUnitId)
          break;

        case '2':
          // 获取路段单位名称
          this.formGetName('2', this.formParams.roadSectionId)
          // 重置路线数据
          this.formParams.routerNum = ''
          this.formParams.routerName = ''
          this.formRouteList = []
          // 重置路线等级
          this.formGetTechnicalGrade(this.formParams.roadSectionId)
          // 重新获取路线数据列表
          this.queryRouterList(this.formParams.roadSectionId)
          break;

        case '3':
          // 获取路段单位名称
          this.formGetName('3', this.formParams.routerNum)
          break;
      }
    },

    // 获取管养单位/路段/路线名称
    formGetName(type, val) {
      switch (type) {
        case '1':
          for (let i = 0; i < this.formMaintenanceList.length; i++) {
            if (this.formMaintenanceList[i].id == val) {
              this.formParams.maintenanceUnitName = this.formMaintenanceList[i].label
              break
            }
          }
          break;

        case '2':
          for (let i = 0; i < this.formRoadSectionList.length; i++) {
            if (this.formRoadSectionList[i].maintenanceSectionId == val) {
              this.formParams.roadSectionName = this.formRoadSectionList[i].maintenanceSectionName
              break
            }
          }
          break;

        case '3':
          for (let i = 0; i < this.formRouteList.length; i++) {
            if (this.formRouteList[i].routeCode == val) {
              this.formParams.routerName = this.formRouteList[i].routeName
              break
            }
          }
          break;
      }
    },

    // 获取技术等级
    formGetTechnicalGrade(val) {
      for (let i = 0; i < this.formRoadSectionList.length; i++) {
        if (this.formRoadSectionList[i].maintenanceSectionId == val) {
          this.formParams.technicalGrade = this.formRoadSectionList[i].routeGrade
          break
        }
      }
    },

    // 市级区划改变时
    // formChangeAreaCode(val) {
    //   console.log('市区区划改变：', val)
    //   this.formParams.areaCode = ''
    //   if(val) {
    //     let index = this.divisionsMunicipalList.findIndex(item => item.id === val)
    //     if(index !== -1) {
    //       this.areaCodeList = this.divisionsMunicipalList[index].children
    //     }
    //   } else {
    //     this.areaCodeList = []
    //   }
    // },

    // 表单数组转换
    formArrayChange(item) {
      if (item) {
        let check = item.includes(',')
        if (check) {
          return item.split(',')
        } else {
          return [item]
        }
      } else {
        return []
      }
    },

    // 文件下载
    async formDownloadFile(url, fileName) {
      try {
        // 使用fetch请求文件
        const response = await fetch(url);
        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }
        const blob = await response.blob(); // 将响应体转换为Blob对象

        // 创建下载链接
        const link = document.createElement('a');
        link.href = window.URL.createObjectURL(blob);
        link.setAttribute('download', fileName); // 设置下载的文件名
        link.style.display = 'none';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
      } catch (error) {
        this.$message.error('下载文件错误');
      }
    },

    // 表单是否临河改变时
    formRiverChange() {
      // 清除临河地形
      if (this.formParams.embankmentByARiver === '1') {
        this.formParams.embankmentRiversideTerrain = null
      }
    },

    // 表单其他控制
    // formRadioChange(type, remark) {
    //   if(this.formParams[type] !== '99') {
    //     this.formParams[remark] = ''
    //   }
    // },

    // 表单单选选择无时处理
    // formRadioClear(target, typeArray) {
    //   if (this.formParams[target] === '1') {
    //     typeArray.forEach(type => {
    //       // 移除表单验证
    //       delete this.formRules[type]
    //       if(Array.isArray(this.formParams[type])) {
    //         this.formParams[type] = []
    //       } else {
    //         this.formParams[type] = ''
    //       }
    //     })
    //     switch (target) {
    //       case 'hisDisasterHappened':
    //         this.formParams.hisHappenedFrequency = 0
    //         break;

    //       case 'curCracked':
    //         break;

    //       case 'curDamaged':
    //         this.formParams.curDamagedOther = ''
    //         break;
    //     }
    //   } else {
    //     // 添加表单验证
    //     for(let i=0; i<typeArray.length; i++) {
    //       if(typeArray[i].includes("Remark")) {
    //         continue
    //       }
    //       this.formRules[typeArray[i]] = [
    //         { required: true, message: "该数据为必填/必选", trigger: "blur" }
    //       ]
    //     }
    //   }
    // },

    // 点击预览图片时（灾毁照片）
    formImgPreview1(url) {
      let index = this.formImgUrlList1.findIndex(item => item === url)
      if (index !== -1) {
        let moveUrl = this.formImgUrlList1.splice(index, this.formImgUrlList1.length - index)
        this.formImgUrlList1.unshift(...moveUrl)
      }
    },

    // 点击预览图片时（抢通后照片）
    formImgPreview2(url) {
      let index = this.formImgUrlList2.findIndex(item => item === url)
      if (index !== -1) {
        let moveUrl = this.formImgUrlList2.splice(index, this.formImgUrlList2.length - index)
        this.formImgUrlList2.unshift(...moveUrl)
      }
    },

    // 删除图片（灾毁照片）
    formDeleteImg1(id) {
      this.$modal.confirm('是否确认删除该图片？').then(async () => {
        this.$modal.loading('正在删除图片，请稍候...')
        removeFile(id.split('-')[1]).then((res) => {
          if (res.code === 200) {
            this.$message.success('删除图片成功！')
            // 移除预览列表图片
            let index = this.formImgSrcList1.findIndex(item => item.id === id)
            if (index !== -1) {
              let imgUrl = this.formImgSrcList1[index].imgUrl
              let imgIndex = this.formImgUrlList1.indexOf(imgUrl)
              console.log('预览索引：', imgIndex)
              if (imgIndex !== -1) {
                this.formImgUrlList1.splice(imgIndex, 1)
              }
            }
            // 移除渲染列表图片
            this.formImgSrcList1 = this.formImgSrcList1.filter(item => item.id !== id)
          }
          this.$modal.closeLoading()
        }).catch(() => {
          this.$message.error('删除图片失败')
          this.$modal.closeLoading()
        })
      })
    },

    // 删除图片（抢通后照片）
    formDeleteImg2(id) {
      this.$modal.confirm('是否确认删除该图片？').then(async () => {
        this.$modal.loading('正在删除图片，请稍候...')
        removeFile(id.split('-')[1]).then((res) => {
          if (res.code === 200) {
            this.$message.success('删除图片成功！')
            // 移除预览列表图片
            let index = this.formImgSrcList2.findIndex(item => item.id === id)
            console.log('查找预览索引：', index)
            if (index !== -1) {
              let imgUrl = this.formImgSrcList2[index].imgUrl
              console.log('查找预览对应值：', this.formImgSrcList2[index])
              let imgIndex = this.formImgUrlList2.indexOf(imgUrl)
              console.log('查找预览索引2：', imgIndex)
              if (imgIndex !== -1) {
                this.formImgUrlList2.splice(imgIndex, 1)
              }
            }
            // 移除渲染列表图片
            this.formImgSrcList2 = this.formImgSrcList2.filter(item => item.id !== id)
          } else {
            this.$message.error('删除图片失败')
          }
          this.$modal.closeLoading()
        }).catch(() => {
          this.$message.error('删除图片失败')
          this.$modal.closeLoading()
        })
      })
    },

    // 删除文件（公函）
    formDeleteFile(id) {
      this.$modal.confirm('是否确认删除？').then(async () => {
        this.$modal.loading('正在删除文件，请稍候...')
        removeFile(id.split('-')[1]).then((res) => {
          if (res.code === 200) {
            this.$message.success('删除文件成功！')
            // 移除渲染列表文件
            this.formFileSrcList = this.formFileSrcList.filter(item => item.id !== id)
          } else {
            this.$message.error('删除文件失败')
          }
          this.$modal.closeLoading()
        }).catch(() => {
          this.$message.error('删除文件失败')
          this.$modal.closeLoading()
        })
      })
    },

    /**
     * 关联风险点相关
     */

    // 打开查询风险点dialog
    openRiskSearchDialog() {
      this.riskList()
      this.riskSearchDialog = true
    },

    // 当风险点dialog关闭时
    riskDialogClose() {
      this.riskSearchDialog = false
    },

    // 查询风险点数据
    riskList() {
      this.riskTableLoading = true
      console.log('提交参数：', this.riskQueuryParams)
      queryPageRisk(this.riskQueuryParams).then((res) => {
        console.log('获取的风险点数据：', res)
        if (res.code === 200) {
          this.riskTableData = res.rows
          this.riskTableTotal = res.total
        }
        this.riskTableLoading = false
      }).catch(() => {
        this.riskTableLoading = false
      })
    },

    // 重置风险点查询
    riskQueryReset() {
      this.riskQueuryParams = {
        pageNum: 1,
        pageSize: 10,
        disasterRiskId: '', // 灾害ID
        disasterNum: '', // 灾害编号
        disasterName: '', // 灾害名称
        disasterType: '', // 灾害类型
        managementMaintenanceIds: [], // 管养处
        maintenanceSectionId: '', // 路段编号
        routeCodes: [], // 路线编号
      }
      this.riskList()
    },

    // 风险点选择
    riskSelect(item) {
      console.log('选择的风险点数据：', item)
      // 灾毁表单赋值
      this.formParams.disasterRiskId = item.id
      this.formParams.pileStartNum = item.pileStartNum
      this.formParams.pileEndNum = item.pileEndNum
      this.formParams.startLatitudeAndLongitude = item.startLatitudeAndLongitude
      this.formParams.endLatitudeAndLongitude = item.endLatitudeAndLongitude
      this.formParams.disasterSectionId = item.disasterNum + '-' + moment().format('YYYYMMDD')
      // 关联风险赋值
      this.riskFormParams = item
      this.riskSearchDialog = false
    },

    /**
     * 导入相关
     */
    /** 导入按钮操作 */
    handleImport() {
      this.upload.title = "高边坡数据导入";
      // this.formRiskType = val
      // this.upload.disasterType = val
      this.upload.open = true;
    },

    /** 下载模板操作 */
    importTemplate() {
      // let vo = {
      //   disasterType: this.formRiskType
      // }
      this.download('/disaster/highway/importTemplate',
        {},
        `高边坡数据模板_${new Date().getTime()}.xlsx`)
    },

    // 文件上传中处理
    handleFileUploadProgress(event, file, fileList) {
      this.upload.isUploading = true;
    },

    // 文件上传成功处理
    handleFileSuccess(response, file, fileList) {
      this.upload.open = false;
      this.upload.isUploading = false;
      this.$refs.upload.clearFiles();
      // this.$alert(response.msg, "导入结果", { dangerouslyUseHTMLString: true });
      this.$alert("<div style='overflow: auto;overflow-x: hidden;max-height: 70vh;padding: 10px 20px 0;'>" + response.msg + "</div>", "导入结果", {dangerouslyUseHTMLString: true});
      this.queryList();
    },

    // 提交上传文件
    submitFileForm() {
      this.$refs.upload.submit();
    },

    /**
     * 坐标相关
     */
    // 打开地图dialog
    openCoordinateDialog(type) {
      this.coordinateDialog = true
      this.coordinateType = type
      this.$nextTick(() => {
        this.coordinateInit()
      })
    },

    // 地图初始化
    coordinateInit() {
      const _this = this
      // 重置筛选结果
      this.coordinateSearch = ''

      window._AMapSecurityConfig = {
        securityJsCode: "8ba5a60100192adc21a2044b9582e26e", // 安全密钥
      };
      AMapLoader.load({
        key: "38ce82094eecafcb00a7dd5b323cc4d0", // 申请好的Web端开发者Key，首次调用 load 时必填
        version: "2.0", // 指定要加载的 JSAPI 的版本，缺省时默认为 1.4.15
        // plugins: ["AMap.ToolBar", "AMap.Scale"], //需要使用的的插件列表，如比例尺'AMap.Scale'，支持添加多个如：['...','...']
      })
        .then((AMap) => {
          // 变量初始化
          let positionInit = [102.8207599, 24.8885797] // 默认坐标昆明市
          switch (_this.coordinateType) {
            case 'start':
              if (_this.formParams.startLatitudeAndLongitude && _this.formParams.startLatitudeAndLongitude.includes(",")) {
                let start = _this.formParams.startLatitudeAndLongitude.split(",")
                start.forEach(item => {
                  item = Number(item)
                })
                positionInit = start
              }
              break;

            case 'end':
              if (_this.formParams.endLatitudeAndLongitude && _this.formParams.endLatitudeAndLongitude.includes(",")) {
                let end = _this.formParams.endLatitudeAndLongitude.split(",")
                end.forEach(item => {
                  item = Number(item)
                })
                positionInit = end
              }
              break;
          }
          _this.coordinatePosition = positionInit.join(",")

          console.log('初始化位置的值：', positionInit)

          // 地图初始化
          let satellite = new AMap.TileLayer.Satellite(); //创建卫星图层
          let roadNet = new AMap.TileLayer.RoadNet(); //创建路网图层
          _this.coordinateMap = new AMap.Map("coordinateBox", {
            // 设置地图容器id
            viewMode: "3D", // 是否为3D地图模式
            zoom: 11, // 初始化地图级别
            center: positionInit, // 初始化地图中心点位置
            layers: [satellite, roadNet], // 地图图层的数组
          })
          AMap.plugin('AMap.ToolBar', function () {
            let toolbar = new AMap.ToolBar(); // 缩放工具条实例化
            _this.coordinateMap.addControl(toolbar); //添加控件
          })
          AMap.plugin('AMap.Scale', function () {
            let scale = new AMap.Scale(); // 比例尺工具条实例化
            _this.coordinateMap.addControl(scale); //添加控件
          })

          // 初始化坐标点
          if (_this.coordinateMarker) {
            _this.coordinateMarker = null
          }
          _this.coordinateMarker = new AMap.Marker({
            position: positionInit
          })
          _this.coordinateMap.add(_this.coordinateMarker)

          // 地图绑定点击事件
          // _this.coordinateMap.on("click", _this.coordinateClick);
        })
        .catch((e) => {
          console.log(e);
        });
    },

    // 添加地图点击事件
    // coordinateClick(e) {
    //   // 清除原有标记
    //   if(this.coordinateMarker) {
    //     this.coordinateMap.remove(this.coordinateMarker)
    //   }
    //   this.coordinateMarker = new AMap.Marker({
    //     position: [e.lnglat.getLng(), e.lnglat.getLat()]
    //   })
    //   this.coordinateMap.add(this.coordinateMarker)
    //   // 坐标点赋值
    //   this.coordinatePosition = `${e.lnglat.getLng()},${e.lnglat.getLat()}`
    // },

    // 添加查询事件
    coordinateList() {
      const _this = this
      AMap.plugin('AMap.PlaceSearch', function () {
        let autoOptions = {
          city: ''
        }
        let placeSearch = new AMap.PlaceSearch(autoOptions);
        placeSearch.search(_this.coordinateSearch, function (status, result) {
          console.log('搜索状态：', status)
          console.log('搜索结果：', result)
          // 搜索成功时，result即是对应的匹配数据
          if (status === 'complete') {
            if (result.info === 'OK' && result.poiList.count > 0) {
              let lng = result.poiList.pois[0].location.lng
              let lat = result.poiList.pois[0].location.lat
              // 重置中心点
              _this.coordinateMap.setCenter([lng, lat])
              // 清除原有标记
              if (_this.coordinateMarker) {
                _this.coordinateMap.remove(_this.coordinateMarker)
              }
              _this.coordinateMarker = new AMap.Marker({
                position: [lng, lat]
              })
              _this.coordinateMap.add(_this.coordinateMarker)
              // 坐标点赋值
              _this.coordinatePosition = `${lng},${lat}`
            } else {
              _this.$message.warning('暂无数据！')
            }
          } else {
            _this.$message.warning('查询失败！')
          }
        })
      })
    },

    // 保存当前坐标
    coordinateSave() {
      if (!this.coordinatePosition) {
        this.$message.warning('请选择经纬度！')
        return
      }
      switch (this.coordinateType) {
        case 'start':
          this.formParams.startLatitudeAndLongitude = this.coordinatePosition
          break;

        case 'end':
          this.formParams.endLatitudeAndLongitude = this.coordinatePosition
          break;
      }
      this.coordinateDialog = false
    },

    // 窗口关闭时销毁地图
    coordinateClose() {
      if (this.coordinateMap) {
        console.log('触发销毁')
        this.coordinateMap.off("click", this.coordinateClick);
        this.coordinateMap.destroy()
        this.coordinateMap = null
      }
    },
    // 选择灾毁类型默认在新增时显示选中数据
    disasterTypeSelectHandle(val) {
      this.riskQueuryParams.disasterType = val
    },
    formRevoke(type, item) {
      let ids = [];
      switch (type) {
        case 'batch':
          this.tableSelection.forEach((row) => {
            if (row.status === '1') {
              ids.push(row.id)
            }
          })
          break;
        case 'single':
          ids.push(item.id);
          break;
      }
      if (ids.length === 0) {
        this.$alert('请至少选择一条审核中业务！', '提示信息', {
          confirmButtonText: '确定'
        });
        return;
      }
      let idsStr = ids.join(",");
      this.$modal
        .confirm('是否确认撤销所选业务？')
        .then(async () => {
            this.$prompt('请输入撤销原因', '提示信息', {
              confirmButtonText: '确定',
              cancelButtonText: '取消',
              inputValidator: (value) => { //非空验证
                if (!value) {
                  return '撤销原因不能为空!'
                }
              }
            }).then(async ({value}) => {
                this.tableLoading = true
                await revokeProcess({type: '2', ids: idsStr, reason: value}).then(res => {
                  if (res && res.code == '200') {
                    if (res.data.msg) {
                      this.$message.warning('已成功撤销：' + res.data.success + ' 条数据，失败：' + res.data.failure + " 条，信息：" + res.data.msg);
                    } else {
                      this.$message.success('操作成功！');
                    }
                    this.queryList();
                    this.tableLoading = false;
                  }
                }).catch((err) => {
                  this.$message.error(err);
                  this.tableLoading = false;
                })
              }
            ).catch()
          }
        )
    },
    expCard() {
      var userId = this.$store.getters.userId;
      var params = {...this.queryParams, reportId: '1036089104949125120', loginUserId: userId};
      if (params.managementMaintenanceIds && params.managementMaintenanceIds.length > 0) {
        params.managementMaintenanceIds = params.managementMaintenanceIds.join(",");
      }
      if (params.routeCodes && params.routeCodes.length > 0) {
        params.routeCodes = params.routeCodes.join(",");
      }
      if (params.startTime) {
        params.startTime = params.startTime.substring(0, 10);
      }
      if (params.endTime) {
        params.endTime = params.endTime.substring(0, 10);
      }
      this.$tab.openPage('灾毁信息导出', '/disaster/exp_card/disaster_submitted', params);
    }
  }
}

</script>

<style lang="scss" scoped>
.app-container form:first-child .el-select,
.app-container form:nth-child(2) .el-select,
.app-container form:nth-child(2) ::v-deep .el-form-item__content,
.app-container form:first-child ::v-deep .el-form-item__content {
  width: 240px;
}

.app-container form:first-child .el-form-item:last-child ::v-deep .el-form-item__content {
  width: auto;
}

.app-container {
  padding: 10px;
  background-color: #c0c0c0;
  box-sizing: border-box;
}

.formDialog {
  ::v-deep .el-dialog__body {
    height: 600px;
    overflow-y: auto;
  }

  .dialog-footer {
    width: 100%;

    .footerTip {
      color: #888888;
      font-size: 14px;
    }
  }

  .titleBox {
    height: 22px;
    display: flex;
    flex-direction: row;
    align-items: center;

    .title {
      font-size: 16px;
      color: black;
      margin: 0;
    }

    .subTitle {
      margin-left: 15px;
      font-size: 12px;
      color: #888888;
    }

    .riskLevel {
      user-select: none;
      position: absolute;
      // top: 0;
      right: 5%;
      display: flex;
      align-items: center;
      flex-direction: row;

      .title {
        font-size: 16px;
        font-weight: bold;
      }

      .main {
        font-size: 16px;
        font-weight: bold;
        padding: 5px 10px 5px 10px;
        color: white;
        box-sizing: border-box;
        border-radius: 5px;
      }

      .score {
        color: #888888;
        font-size: 14px;
      }
    }
  }
}

.searchBox {
  padding: 10px;
  background: #fff;
  border-radius: 10px;
  transition: all .1s linear;
  display: flex;
  flex-direction: column;

  .searchMoreBox {
    min-width: 192px;
    margin-top: 10px;
    display: flex;
    align-items: center;
    flex-direction: row;
  }
}

.tableDiv {
  margin-top: 10px;
  background-color: white;
  padding-bottom: 10px;
  border-radius: 10px;
  transition: all .1s linear;
  display: flex;
  flex-direction: column;

  .btnBox {
    padding: 10px;
  }
}

.infoBox {
  padding: 15px 15px 0 15px;
  box-sizing: border-box;
  border-radius: 6px;
  border: 1px solid #C4C4C4;
  position: relative;

  .infoTitle {
    user-select: none;
    position: absolute;
    top: 0;
    left: 0;
    padding: 0 10px;
    font-size: 14px;
    line-height: 14px;
    font-weight: bold;
    transform: translateX(15px) translateY(-50%);
    background-color: white;
  }

  .imgBox {
    height: auto;
    width: 100%;
    // display: flex;
    // flex-wrap: wrap;
    // align-content: flex-start;

    .imgItemBox {
      height: 240px;
      width: calc(100% / 3);
      box-sizing: border-box;
      padding: 10px;
      overflow-y: auto;
      display: flex;
      align-items: center;
      justify-content: center;
      position: relative;

      .imgDeleteBtn {
        position: absolute;
        z-index: 1;
        top: 10%;
        right: 10%;
      }
    }

    ::v-deep .el-card__body {
      width: 100%;
      display: flex;
      flex-direction: row;
      flex-wrap: wrap;
      align-content: flex-start;
    }

    .imgBoxCard {
      width: 100%;

      .cardMain {
        height: 260px;
        width: 33%;
        box-sizing: border-box;
        padding: 0 10px;
        display: flex;
        flex-direction: column;
        position: relative;

        .imgDeleteBtn {
          position: absolute;
          z-index: 1;
          top: 20%;
          right: 5%;
        }

        .imgTitle {
          height: 28px;
          width: 100%;
          font-size: 16px;
          font-weight: bold;
          white-space: nowrap;
          text-overflow: ellipsis;
          overflow: hidden;
        }

        .img {
          height: calc(100% - (28px + 28px));
          width: 100%;
          padding: 10px 0;
          position: relative;
          z-index: 0;
        }

        .footer {
          height: 28px;
          color: #888888;
          font-size: 14px;
        }
      }
    }
  }

  .fileBox {
    width: 100%;
    display: flex;
    flex-direction: column;

    .fileItemBox {
      width: 100%;
      padding: 10px 0;

      .fileItem {
        width: 100%;
        padding: 10px;
        border: 1px solid #DCDFE6;
        display: flex;
        flex-direction: row;

        .boxLeft {
          height: auto;
          width: 48px;
          font-size: 24px;
          display: flex;
          justify-content: center;
          align-items: center;
        }

        .boxmiddle {
          height: auto;
          width: calc(100% - (48px + 200px));
          color: #888888;
          font-size: 14px;
          display: flex;
          align-items: center;
        }

        .boxRight {
          height: auto;
          width: 200px;
          display: flex;
          justify-content: center;
          align-items: center;
        }
      }


    }
  }

  .noneBox {
    user-select: none;
    height: 200px;
    width: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    color: #888888;
  }
}

.coordinateDialog {

  .coordinateMap {
    height: 600px;
    width: 100%;
    position: relative;

    #coordinateBox {
      height: 100%;
      width: 100%;
      border-radius: 5px;
      position: relative;
      z-index: 0;
    }

    .coordinateSearch {
      position: absolute;
      z-index: 1;
      top: 10px;
      left: 10px;
      width: 50%;
      padding: 10px;
      box-sizing: border-box;
      background-color: #fff;
      border-radius: 5px;
      border: 1px solid #DCDFE6;
      box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.12), 0 0 6px 0 rgba(0, 0, 0, 0.04);
      display: flex;
      flex-direction: row;
    }

    .coordinateTip {
      position: absolute;
      z-index: 1;
      top: 10px;
      right: 10px;
      padding: 10px;
      box-sizing: border-box;
      background-color: #fff;
      border-radius: 5px;
      border: 1px solid #DCDFE6;
      box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.12), 0 0 6px 0 rgba(0, 0, 0, 0.04);
    }
  }
}

.riskSearchDialog {

  .searchDialogMain {
    height: 600px;
    width: 100%;
    display: flex;
    flex-direction: column;

    .searchBox {
      height: 86px;
      width: 100%;
    }

    .tableBox {
      height: calc(100% - 86px);
      width: 100%;
    }

  }
}

.hisMainBox {
  width: 100%;

  .hisIdeaBox {
    width: 100%;
    display: flex;
    flex-direction: column;

    .boxMain {
      width: 100%;
      display: flex;
      flex-direction: column;

      .changeBox {
        width: 50%;
        box-sizing: border-box;
        padding: 10px;
        border-radius: 5px;
        border: 1px solid #DCDFE6;
        box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.12), 0 0 6px 0 rgba(0, 0, 0, 0.04);
        display: flex;
        flex-direction: column;

        .title {
          padding: 10px;
          user-select: none;
          color: #888888;
          font-size: 14px;
          font-weight: bold;
          border-bottom: 1px solid #DCDFE6;
        }

        .list {
          padding: 10px;
          user-select: none;
          color: #888888;
          font-size: 14px;
          border-bottom: 1px solid #DCDFE6;
          display: flex;
          flex-direction: row;
          flex-wrap: wrap;
          align-items: center;

          p {
            margin: 0;
            line-height: 20px;
          }
        }

        .none {
          padding: 10px;
          user-select: none;
          color: #888888;
          font-size: 14px;
        }
      }
    }

    .boxBottom {
      width: 100%;
      font-size: 14px;
      color: #888888;
      display: flex;
      justify-content: space-between;
    }
  }
}

::v-deep .el-divider--horizontal {
  margin: 0 0 18px 0;
}

// v-if过渡动画
// 查询框
.search-enter-active {
  transition: all .1s linear;
}

.search-enter {
  opacity: 0;
}

.search-leave-active {
  transition: all .1s linear;
}

.search-leave-to {
  opacity: 0;
}
</style>
