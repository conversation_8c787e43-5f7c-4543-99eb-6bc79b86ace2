<template>
  <Cards :customTitle="true" w="100%" h="27vh" :isDtl="false">
    <template slot="title">
      <el-dropdown trigger="click" v-if="titleOptions.length > 1" @command="handleTitle">
        <span class="el-dropdown-link">
          {{ title }}<i class="el-icon-caret-bottom"></i>
        </span>
        <el-dropdown-menu slot="dropdown">
          <el-dropdown-item v-for="(item, index) in titleOptions" :key="index" :command="item">{{ item
            }}</el-dropdown-item>
        </el-dropdown-menu>
      </el-dropdown>
      <span v-else>{{ title }}</span>
    </template>
    <template slot="more">
      <el-cascader v-if="title === '挠度'" class="mycascader" :options="deflectionSensorList"
        :popper-append-to-body="false" v-model="defaultValue" :props="props" @change="handleChange"
        :show-all-levels="false" empty="暂无" ref="cascader" />
      <el-select v-else v-model="defaultValue2" @change="handleChangeOther">
        <el-option v-for="item in otherSensorList" :key="item.sensorId" :label="item.installLocation"
          :value="item.sensorId"></el-option>
      </el-select>
    </template>
    <div class="box" v-loading="loading" element-loading-text="加载中" element-loading-spinner="el-icon-loading"
      element-loading-background="rgba(0, 0, 0, 0.3)">
      <Echarts v-if="option" :option="option" :height="'100%'" :key="optionKey" />
    </div>
  </Cards>
</template>

<script>
import { isBigScreen } from '../../../utils/utils.js';
import Echarts from "../../components/echarts.vue"
import Cards from "../../components/cards.vue"
import { fetchGet } from '../../../utils/api.js'

export default {
  name: 'Mid',
  inject: ['iThis'],
  props: {
    // title: {
    //   type: String,
    //   default: "挠度"
    // }
  },
  components: { Echarts, Cards },
  data() {
    return {
      title: '挠度',
      isBig: isBigScreen(),
      option: null,
      loading: false,
      optionKey: 'optionKey',
      props: {
        value: "sensorId",
        label: "installLocation",
        multiple: true,
      },
      defaultValue: [],
      deflectionSensorList: [],
      WSDSensorList: [],
      YLSensorList: [],
      JLSensorList: [],
      YBSensorList: [],
      YYLSensorList: [],
      SLSensorList: [],
      nameList: [],
      chartData: [],
      timeList: [],
      RHTempData: [],
      titleOptions: [],
      otherSensorList: [],
      defaultValue2: '',
    }
  },
  created() {
    this.init()
  },
  methods: {
    init() {
      let sensorList = this.iThis.sensorList;
      this.deflectionSensorList = sensorList.filter((item) => item.specificMonitorTypeName?.includes("挠度"));
      this.WSDSensorList = sensorList.filter((item) => item.specificMonitorTypeName?.includes("湿度"));
      this.JLSensorList = sensorList.filter((item) => item.specificMonitorTypeName?.includes("距离变化值"));
      this.YLSensorList = sensorList.filter(item => {
        return item.specificMonitorTypeName.some(name => {
          return name.includes('雨量') || name.includes('水量');
        });
      });
      this.YBSensorList = sensorList.filter(item => {
        return item.specificMonitorTypeName.some(name => {
          return name.includes('应变') || name.includes('光纤');
        });
      });
      this.YYLSensorList = sensorList.filter((item) => /预应力/.test(item.specificMonitorTypeName[0]));
      this.SLSensorList = sensorList.filter((item) => /索力/.test(item.specificMonitorTypeName[0]));

      if (this.deflectionSensorList?.length != 0) this.titleOptions.push("挠度");

      if (this.WSDSensorList?.length != 0) this.titleOptions.push("温湿度");

      if (this.YLSensorList?.length != 0) this.titleOptions.push("雨量");

      if (this.JLSensorList?.length != 0) this.titleOptions.push("距离变化值");

      if (this.YBSensorList?.length != 0) this.titleOptions.push("应变");

      if (this.YYLSensorList?.length != 0) this.titleOptions.push("预应力");

      if (this.SLSensorList?.length != 0) this.titleOptions.push("索力");

      if (this.titleOptions.length == 0) {
        this.title = "挠度";
        this.option = {
          title: {
            text: '没有数据',
            left: 'center',
            top: 'center',
            textStyle: {
              fontSize: this.isBig ? 32 : 14,
              color: '#606266',
              fontWeight: 700
            },
          },
        }
        this.optionKey = new Date().getTime();
      } else {
        this.title = this.titleOptions[0];
        this.handleTitle(this.title);
      }
    },
    handleTitle(v) {
      this.title = v;
      this.defaultValue = [];
      this.defaultValue2 = '';
      this.otherSensorList = []
      switch (v) {
        case "挠度":
          this.defaultValue = [this.deflectionSensorList[0].sensorId];
          this.getChartData(
            this.deflectionSensorList[0].code,
            this.deflectionSensorList[0].sensorId
          );
          this.nameList.push(this.deflectionSensorList[0].installLocation);
          break;
        case "温湿度":
          this.otherSensorList = this.WSDSensorList
          this.defaultValue2 = this.otherSensorList[0].sensorId;
          this.getChartData(
            this.WSDSensorList[0].code,
            this.WSDSensorList[0].sensorId
          );
          break;
        case "雨量":
          this.otherSensorList = this.YLSensorList
          this.defaultValue2 = this.otherSensorList[0].sensorId;
          this.getChartData(
            this.YLSensorList[0].code,
            this.YLSensorList[0].sensorId
          );
          break;
        case "距离变化值":
          this.otherSensorList = this.JLSensorList
          this.defaultValue2 = this.otherSensorList[0].sensorId;
          this.getChartData(
            this.JLSensorList[0].code,
            this.JLSensorList[0].sensorId
          );
          break;
        case "应变":
          this.otherSensorList = this.YBSensorList
          this.defaultValue2 = this.otherSensorList[0].sensorId;
          this.getChartData(
            this.YBSensorList[0].code,
            this.YBSensorList[0].sensorId
          );
          break;
        case "预应力":
          this.otherSensorList = this.YYLSensorList
          this.defaultValue2 = this.otherSensorList[0].sensorId;
          this.getChartData(
            this.YYLSensorList[0].code,
            this.YYLSensorList[0].sensorId
          );
          break;
        case "索力":
          this.otherSensorList = this.SLSensorList
          this.defaultValue2 = this.otherSensorList[0].sensorId;
          this.getChartData(
            this.SLSensorList[0].code,
            this.SLSensorList[0].sensorId
          );
          break;
      }
    },
    handleChangeOther() {
      const obj = this.otherSensorList.find(item => item.sensorId === this.defaultValue2);
      this.getChartData(
        obj.code,
        obj.sensorId
      );
    },
    async handleChange(sensorList) {
      if (sensorList.length == 0) {
        this.option = null;
        this.optionKey = new Date().getTime();
        return
      }
      let filterSensors = [];
      await sensorList.forEach((item) => {
        this.deflectionSensorList.forEach((element) => {
          if (element.sensorId == item) {
            filterSensors.push(element);
          }
        });
      });
      this.nameList = [];
      this.chartData = [];
      this.timeList = [];
      if (filterSensors.length != 0) {
        filterSensors.forEach((element) => {
          this.getChartData(element.code, element.sensorId);
          this.nameList.push(element.installLocation);
        });
      }
    },
    getChartData(nodeCode, sensorId) {
      this.loading = true;
      let params = {
        structureNodeCode: this.iThis.params.code,
        nodeCode: nodeCode,
        sensorId: sensorId,
      };
      const url = 'https://jkjc.yciccloud.com:8000/xboot/displayScreen/default/getCalibratedRealTimeData'
      fetchGet(url, params).then((res) => {
        if (res.code == 200) {
          if (this.title == "挠度") {
            // 取出挠度数据
            res.result.forEach((item) => {
              if (item.name === "挠度") {
                this.chartData.push(item);
                this.timeList = this.chartData[0].times;
                this.setOption()
              }
            });
          } else if (this.title == "温湿度") {
            this.RHTempData = res.result;
            this.initSingleChart()
          } else {
            this.RHTempData = res.result;
            this.initSingleChart()
          }
        } else {
          console.log("getCalibratedRealTimeData返回数据失败");
        }
      }).finally(() => {
        this.loading = false;
      });
    },
    async setOption() {
      if (this.chartData.length == 0 || this.chartData[0].values?.length === 0) {
        this.option = {
          title: {
            text: '没有数据',
            left: 'center',
            top: 'center',
            textStyle: {
              fontSize: this.isBig ? 32 : 14,
              color: '#606266',
              fontWeight: 700
            }
          }
        }
        this.optionKey = new Date().getTime();
        return;
      }
      await this.chartData.forEach((obj, index) => {
        obj.name = this.nameList[index];
      });
      var newTimeList = this.timeList.map((item) => {
        item = item.slice(0, 19)
        return item
      })
      let myseries = this.chartData.map((item) => ({
        name: item.name,
        type: "line",
        data: item.values,
        smooth: false,
        symbolSize: 7
      }));

      this.option = {
        tooltip: {
          trigger: "axis",
        },
        legend: {
          data: this.nameList,
          type: 'scroll',
          top: "5%",
          left: "2%",
          itemGap: this.isBig ? 20 : 10, // 每个图例间隔
          pageButtonItemGap: 0,// 页码和箭头之间间隔
          pageIconColor: "#ffffff",// 箭头颜色
          pageTextStyle: {
            color: '#ffffff'// 页码文字颜色
          },
          textStyle: {
            color: "#ffffff",
            fontSize: this.isBig ? 24 : 12,
          },
          // itemWidth: 20, // 设置图例标记的宽度
          // itemHeight: 9, // 设置图例标记的高度
        },
        grid: {
          left: "4%",
          right: "4%",
          top: "20%",
          bottom: "6%",
          containLabel: true,
        },
        xAxis: {
          type: "category",
          boundaryGap: true,
          data: newTimeList,
          axisLabel: {
            // 添加或修改 axisLabel 配置项
            textStyle: {
              // 设置文本样式
              color: "#ffffff",
              fontSize: this.isBig ? 24 : 10,
            },
          },
          axisLine: {
            show: true,
            lineStyle: {
              color: 'rgba(110, 112, 121, 0.70)'
            }
          },
          axisTick: {
            alignWithLabel: true, // 刻度线与标签对齐
            // inside: true // 刻度线朝上
          }
        },
        yAxis: {
          type: "value",
          boundaryGap: [0.1, 0.1],
          axisLabel: {
            // 添加或修改 axisLabel 配置项
            textStyle: {
              // 设置文本样式
              color: "rgba(153, 153, 153, 1)",
              fontSize: this.isBig ? 24 : 10,
            },
          },
          splitArea: {
            show: false,
          },
          splitLine: {
            lineStyle: {
              color: 'rgba(110, 112, 121, 0.70)' // 修改分割线颜色
            }
          },
        },
        series: myseries,
      }
      this.optionKey = new Date().getTime()
    },
    initSingleChart() {
      let data = this.RHTempData;
      if (data?.length && data[0].values?.length === 0) {
        this.option = {
          title: {
            text: '没有数据',
            left: 'center',
            top: 'center',
            textStyle: {
              fontSize: this.isBig ? 32 : 14,
              color: '#606266',
              fontWeight: 700
            }
          }
        }
        this.optionKey = new Date().getTime();
        return
      }
      let myseries = [];
      let mylegend = [];
      let myXdata = [];
      data.forEach((item) => {
        myseries.push({
          name: item.name,
          type: "line",
          data: item.values,
          smooth: false,
          symbolSize: 7
        });
        mylegend.push(item.name);
        myXdata = item.times;
      });
      let option = {
        tooltip: {
          trigger: "axis",
        },
        legend: {
          data: mylegend,
          top: "5%",
          left: "center",
          textStyle: {
            color: "#ffffff",
            fontSize: this.isBig ? 24 : 12,
          },
          // itemWidth: 20, // 设置图例标记的宽度
          // itemHeight: 9, // 设置图例标记的高度
        },
        grid: {
          left: "4%",
          right: "4%",
          bottom: "6%",
          top: "20%",
          containLabel: true,
        },
        xAxis: {
          type: "category",
          boundaryGap: true,
          data: myXdata,
          axisLabel: {
            // 添加或修改 axisLabel 配置项
            textStyle: {
              // 设置文本样式
              color: "#ffffff",
              fontSize: this.isBig ? 24 : 10,
            },
          },
          axisLine: {
            show: true,
            lineStyle: {
              color: 'rgba(110, 112, 121, 0.70)'
            }
          },
          axisTick: {
            alignWithLabel: true, // 刻度线与标签对齐
            // inside: true // 刻度线朝上
          }
        },
        yAxis: {
          type: "value",
          boundaryGap: [0.1, 0.1],
          axisLabel: {
            // 添加或修改 axisLabel 配置项
            textStyle: {
              // 设置文本样式
              color: "rgba(153, 153, 153, 1)",
              fontSize: this.isBig ? 24 : 10,
            },
          },
          splitArea: {
            show: false,
          },
          splitLine: {
            lineStyle: {
              color: 'rgba(110, 112, 121, 0.70)' // 修改分割线颜色
            }
          },
        },
        series: myseries,
      };

      this.option = option
      this.optionKey = new Date().getTime()
    },
  },
  computed: {},
  watch: {},
}
</script>

<style lang="scss" scoped>
@import "@/assets/styles/utils.scss";

.box {
  height: 100%;
  width: 100%;

  .mycascader {
    position: absolute;
    right: 2%;
    top: 10%;
    z-index: 100;
    color: #fff;
    overflow: hidden;
  }
}

.el-dropdown-link {
  cursor: pointer;

  i {
    font-size: vwpx(28px);
    margin-left: vwpx(10px);
    color: #606266;
  }
}

::v-deep {
  .el-dropdown {
    font-size: inherit !important;
    color: #fff;
  }

  .el-dropdown-menu__item {
    padding: 0 vwpx(20px);
    font-size: vwpx(24px);
  }

  .el-input__inner {
    width: vwpx(400px);
    height: vwpx(58px);
    font-size: vwpx(24px);
    color: rgb(255, 255, 255);
    background-color: rgba(4, 17, 48, 0.4);
    border: 1px solid rgba(6, 135, 255, 1);
    padding: 0 vwpx(60px) 0 vwpx(30px);
  }

  // 下拉框
  .el-input__suffix {
    right: vwpx(5px);
  }

  // 下拉框的箭头
  // .el-select .el-input .el-select__caret {
  //   font-size: vwpx(30px);
  //   line-height: vwpx(58px);
  // }

  .el-cascader__tags {
    flex-wrap: nowrap;
    width: vwpx(340px);
    overflow-x: auto;
  }

  .el-select__tags {
    flex-wrap: nowrap !important;
  }

  .el-select__tags-text {
    max-width: vwpx(100px);
    font-size: vwpx(24px);
  }

  .el-tag--info {
    max-width: vwpx(160px);
  }
}
</style>