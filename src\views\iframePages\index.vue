<template>
	<div class="ifr_box">
		<!-- <iframe id="ifr" src="https://zhyhpt.yciccloud.com:9000/webApp/safeDoc/"></iframe> -->
		<!-- <iframe id="ifr" src="https://example.com"></iframe> -->
		<iframe id="ifr" :src="targetUrl"></iframe>
	</div>
</template>
<script>
export default {
	name: 'iframePages',
	data() {
		return {
			targetUrl: '',
		}
	},
	beforeRouteEnter(to, from, next) {
		next((vm) => {
			let url = ''
			let userId = vm.$store.state.user.id.toString()
			if (Object.keys(to.query).length !== 0) {
				url = `https://jkjc.yciccloud.com:8000/xboot/yhptUser/redirectFromYmgsPlatform2?pageName=${to.query.name}&userId=${userId}`
				// url = `http://*************:8000/xboot/yhptUser/redirectFromYmgsPlatform2?pageName=${to.query.name}`
			}
			// 可以通过 vm 访问组件实例
			if (url) {
				vm.targetUrl = url
			} else {
				vm.$router.push({ path: '/404' })
			}
		})
	},
}
</script>
<style lang="scss" scoped>
.ifr_box {
	width: 100%;
	height: 100%;

	#ifr {
		width: 100%;
		height: 100%;
		border: none;
	}
}
</style>