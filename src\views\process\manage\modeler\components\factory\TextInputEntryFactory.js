'use strict';

var escapeHTML = require('bpmn-js-properties-panel/lib/Utils').escapeHTML;

var domify = require('min-dom').domify,
    domQuery = require('min-dom').query;

var entryFieldDescription = require('bpmn-js-properties-panel/lib/factory/EntryFieldDescription');


var textField = function(translate, options, defaultParameters) {

  // Default action for the button next to the input-field
  var defaultButtonAction = function(element, inputNode) {
    var input = domQuery('input[name="' + options.modelProperty + '"]', inputNode);
    input.value = '';

    return true;
  };

  // default method to determine if the button should be visible
  var defaultButtonShow = function(element, inputNode) {
    var input = domQuery('input[name="' + options.modelProperty + '"]', inputNode);

    return input.value !== '';
  };


  var resource = defaultParameters,
      label = options.label || resource.id,
      dataValueLabel = options.dataValueLabel,
      buttonLabel = (options.buttonLabel || 'X'),
      actionName = (typeof options.buttonAction != 'undefined') ? options.buttonAction.name : 'clear',
      actionMethod = (typeof options.buttonAction != 'undefined') ? options.buttonAction.method : defaultButtonAction,
      showName = (typeof options.buttonShow != 'undefined') ? options.buttonShow.name : 'canClear',
      showMethod = (typeof options.buttonShow != 'undefined') ? options.buttonShow.method : defaultButtonShow,
      canBeDisabled = !!options.disabled && typeof options.disabled === 'function',
      canBeHidden = !!options.hidden && typeof options.hidden === 'function',
      description = options.description;

  switch (resource.id) {
    case 'activiti:assignee':
      resource.html =
        domify(
          '<label for="camunda-' + resource.id + '" ' +
          (canBeDisabled ? 'data-disable="isDisabled" ' : '') +
          (canBeHidden ? 'data-show="isHidden" ' : '') +
          (dataValueLabel ? 'data-value="' + dataValueLabel + '"' : '') + '>'+ label +'</label>' +
          '<div class="bpp-field-wrapper" ' +
          (canBeDisabled ? 'data-disable="isDisabled"' : '') +
          (canBeHidden ? 'data-show="isHidden"' : '') +
          '>' +
          '<div class="left-input-disabled">' +
          '<input id="camunda-' + resource.id + '" type="text" name="' + options.modelProperty+'" ' +
          (canBeDisabled ? 'data-disable="isDisabled"' : '') +
          (canBeHidden ? 'data-show="isHidden"' : '') +
          ' />' +
          '</div>' +
          '<input type="button" class="btn-select" value="选择" onclick="bpmnAssigneeDialog(this)"/>' +  //点击方法
          '</div>'
        );
      break;

    case 'activiti:candidateUsers':
      resource.html =
        domify(
          '<label for="camunda-' + resource.id + '" ' +
          (canBeDisabled ? 'data-disable="isDisabled" ' : '') +
          (canBeHidden ? 'data-show="isHidden" ' : '') +
          (dataValueLabel ? 'data-value="' + dataValueLabel + '"' : '') + '>'+ label +'</label>' +
          '<div class="bpp-field-wrapper" ' +
          (canBeDisabled ? 'data-disable="isDisabled"' : '') +
          (canBeHidden ? 'data-show="isHidden"' : '') +
          '>' +
          '<div class="left-input-disabled">' +
          '<input id="camunda-' + resource.id + '" type="text" name="' + options.modelProperty+'" ' +
          (canBeDisabled ? 'data-disable="isDisabled"' : '') +
          (canBeHidden ? 'data-show="isHidden"' : '') +
          ' />' +
          '</div>' +
          '<input type="button" class="btn-select" value="选择" onclick="bpmnCandidateUsersDialog(this)"/>' +  //点击方法
          '</div>'
        );
      break;

    case 'activiti:candidateGroups':
      resource.html =
        domify(
          '<label for="camunda-' + resource.id + '" ' +
          (canBeDisabled ? 'data-disable="isDisabled" ' : '') +
          (canBeHidden ? 'data-show="isHidden" ' : '') +
          (dataValueLabel ? 'data-value="' + dataValueLabel + '"' : '') + '>'+ label +'</label>' +
          '<div class="bpp-field-wrapper" ' +
          (canBeDisabled ? 'data-disable="isDisabled"' : '') +
          (canBeHidden ? 'data-show="isHidden"' : '') +
          '>' +
          '<div class="left-input-disabled">' +
          '<input id="camunda-' + resource.id + '" type="text" name="' + options.modelProperty+'" ' +
          (canBeDisabled ? 'data-disable="isDisabled"' : '') +
          (canBeHidden ? 'data-show="isHidden"' : '') +
          ' />' +
          '</div>' +
          '<input type="button" class="btn-select" value="选择" onclick="bpmnCandidateGroupsDialog(this)"/>' +  //点击方法
          '</div>'
        );
      break;
  }

  // add description below text input entry field
  if (description) {
    resource.html.appendChild(entryFieldDescription(translate, description, { show: canBeHidden && 'isHidden' }));
  }

  resource[actionName] = actionMethod;
  resource[showName] = showMethod;

  if (canBeDisabled) {
    resource.isDisabled = function() {
      return options.disabled.apply(resource, arguments);
    };
  }

  if (canBeHidden) {
    resource.isHidden = function() {
      return !options.hidden.apply(resource, arguments);
    };
  }

  resource.cssClasses = ['bpp-textfield'];

  return resource;
};

module.exports = textField;
