// 行政识别数据
const baseInfoData = [
  {
    type: 'select',
    deptType: 201,
    label:'管理处',
    prop: 'managementMaintenanceId',
    disabled: true,
    rules: [{ required: true, message: '管理处不能为空，选择桥梁带出。', trigger: 'change' }]
  },
  {

    prop: 'managementMaintenanceBranchId',
    placeholder: '请输入管养分处',
    type: 'select',
    deptType: 202,
    label:'管养分处',

    disabled: true,
    rules: [{ required: true, message: '管养分处不能为空，选择桥梁带出。', trigger: 'change' }]
  },
  {
    label: '路线编码',
    prop: 'routeCode',
    type: 'input',
    disabled: true,

    rules: [{ required: true, message: '路线编码不能为空，选择桥梁带出。', trigger: 'change' }]
  },

  {
    label:'路段名称',
    prop: 'routeName',
    type: 'input',

    disabled: true,
    rules: [{ required: true, message: '路段名称不能为空，选择桥梁带出。', trigger: 'change' }]
  },

  {
    label:'中心桩号',
    prop: 'centerStake',
    type: 'input',

    disabled: true,
    rules: [{ required: true, message: '中心桩号不能为空，选择桥梁带出。', trigger: 'change' }]
  },
  {
    label:'桥梁编码',
    prop: 'bridgeCode',
    type: 'input',
    rules: [{ required: true, message: '桥梁编码不能为空，选择桥梁带出。', trigger: 'change' }],
  },
  {
    label:'桥梁名称',
    prop: 'bridgeName',
    type: 'input',

    disabled: true,
  },
  {
    label:'被跨越道路名称',
    prop: 'acrossName',
    type: 'input',

    disabled: true,
  },
  {
    label:'桥梁全长(m)',
    prop: 'totalLength',
    type: 'input',

    disabled: true,
  },
  {
    label: '主桥上部构造结构形式',
    prop: 'mainSuperstructureType',
    placeholder: '请选择主桥上部构造结构形式',
    type: 'select',
    dict: 'bridge_main_superstructure_type',

    disabled: true,
  },

  {
    label:'修建年度',
    prop: 'constructionYear',
    type: 'input',

    disabled: true,
  },
  {
    label:'上次检测时间',
    prop: 'lastCheckDate',
    type: 'date',

  },
  {
    label:'上次特殊检查项目',
    prop: 'lastCheckItem',
    type: 'input',


  },
  {
    label:'本次特殊检查时间',
    prop: 'checkDate',
    type: 'date',
    rules: [{ required: true, message: '请选择检查时间', trigger: 'blur' }],


  },

  {
    label:'本次特殊检查类型	',
    prop: 'checkType',
    type: 'input',
  },
  {
    label:'检查时的气候及环境温度	',
    prop: 'weather',
    type: 'input',
  },
  {
    label:'处置对策	',
    prop: 'treatMeasure',
    type: 'input',
  },
]

const specialExamData = [
  {

    prop: 'items',
    type: 'addinput',
    span:21,
    children:[[
      {
        label:'检测项目',
        prop: 'item',
        placeholder:'请输入检测项目',
        type: 'input',


      },
      {
        label:'检测结果',
        prop: 'result',
        placeholder:'请输入检测结果',
        type: 'input',

      },
    ]]
  },


  {
    label:'特殊检查结论',
    prop: 'conclusion',
    type: 'input',
    span:10,

  },
  {
    label:'记录人',
    prop: 'recordUser',
    type: 'inputUser',
    span:10,

  },
  {
    label:'负责人',
    prop: 'principal',
    type: 'input',
    span:10,

  },

  {
    label:'特殊检查完成机构',
    prop: 'organization',
    type: 'input',
    span:10,
  },
]



export default {
  baseInfoData,
  specialExamData
}
