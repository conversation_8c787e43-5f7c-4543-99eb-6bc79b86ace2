export default class IndexedDBStorage {
  constructor(dbName, storeName, version = 1) {
    this.dbName = dbName;
    this.storeName = storeName;
    this.version = version;
    this.db = null;
  }

  // 打开数据库
  open() {
    return new Promise((resolve, reject) => {
      const request = indexedDB.open(this.dbName, this.version);

      request.onupgradeneeded = (event) => {
        this.db = event.target.result;
        if (!this.db.objectStoreNames.contains(this.storeName)) {
          const store = this.db.createObjectStore(this.storeName, { keyPath: 'id', autoIncrement: true });
          // 添加索引，假设数据中有一个常用查询字段 'keyField'，可根据实际情况修改
          store.createIndex('keyFieldIndex', 'id', { unique: false });
        }
      };

      request.onsuccess = (event) => {
        this.db = event.target.result;
        resolve();
      };

      request.onerror = (event) => {
        reject(event.target.error);
      };
    });
  }

  // 获取数据，添加使用索引优化的方法
  getByIndex(indexName, key) {
    return new Promise((resolve, reject) => {
      if (!this.db) {
        reject(new Error('数据库未打开'));
        return;
      }

      const transaction = this.db.transaction([this.storeName], 'readonly');
      const store = transaction.objectStore(this.storeName);
      const index = store.index(indexName);
      const request = index.get(key);

      request.onsuccess = (event) => {
        resolve(event.target.result);
      };

      request.onerror = (event) => {
        reject(event.target.error);
      };
    });
  }

  get(id) {
    return new Promise((resolve, reject) => {
      if (!this.db) {
        reject(new Error('数据库未打开'));
        return;
      }

      const transaction = this.db.transaction([this.storeName], 'readonly');
      const store = transaction.objectStore(this.storeName);
      const request = store.get(id);

      request.onsuccess = (event) => {
        resolve(event.target.result);
      };
    });
  }

  // 添加数据
  add(data) {
    return new Promise((resolve, reject) => {
      if (!this.db) {
        reject(new Error('数据库未打开'));
        return;
      }

      const transaction = this.db.transaction([this.storeName], 'readwrite');
      const store = transaction.objectStore(this.storeName);
      const request = store.add(data);

      request.onsuccess = (event) => {
        resolve(event.target.result);
      };

      request.onerror = (event) => {
        reject(event.target.error);
      };
    });
  }

  // 删除数据
  delete(id) {
    return new Promise((resolve, reject) => {
      if (!this.db) {
        reject(new Error('数据库未打开'));
        return;
      }

      const transaction = this.db.transaction([this.storeName], 'readwrite');
      const store = transaction.objectStore(this.storeName);
      const request = store.delete(id);

      request.onsuccess = (event) => {
        resolve();
      };

      request.onerror = (event) => {
        reject(event.target.error);
      };
    });
  }

  // 更新数据
  update(data) {
    return new Promise((resolve, reject) => {
      if (!this.db) {
        reject(new Error('数据库未打开'));
        return;
      }

      const transaction = this.db.transaction([this.storeName], 'readwrite');
      const store = transaction.objectStore(this.storeName);
      const request = store.put(data);

      request.onsuccess = (event) => {
        resolve();
      };

      request.onerror = (event) => {
        reject(event.target.error);
      };
    });
  }

  // 关闭数据库
  close() {
    if (this.db) {
      this.db.close();
      this.db = null;
    }
  }
}

// 移除数据库
export function removeIndexedDB(dbName) {
  if ('indexedDB' in window) {
    const request = window.indexedDB.deleteDatabase(dbName);
    request.onsuccess = function (event) {
      // console.log('指定的 IndexedDB 已成功移除');
    };
    request.onerror = function (event) {
      console.error('移除指定的 IndexedDB 时出错:', event.target.error);
    };
    request.onblocked = function (event) {
      console.warn('移除指定的 IndexedDB 被阻止:', event);
    };
  } else {
    console.warn('当前浏览器不支持 IndexedDB');
  }
}

// 使用示例
async function exampleUsage() {
  const db = new IndexedDBStorage('myDatabase', 'myStore');
  try {
    await db.open();

    // 添加数据
    const newItem = { name: '示例数据', value: 123 };
    const id = await db.add(newItem);
    console.log('添加数据成功，ID:', id);

    // 更新数据
    const updatedItem = { id: id, name: '更新后的数据', value: 456 };
    await db.update(updatedItem);
    console.log('更新数据成功');

    // 删除数据
    await db.delete(id);
    console.log('删除数据成功');

    // 通过索引读取数据示例
    try {
      // 假设已有数据包含 'keyField' 字段，这里设置查询值
      const keyFieldValue = '示例查询值';
      const result = await db.getByIndex('keyFieldIndex', keyFieldValue);
      console.log('通过索引查询数据成功:', result);
    } catch (error) {
      console.error('通过索引查询数据出错:', error);
    }

  } catch (error) {
    console.error('操作出错:', error);
  } finally {
    db.close();
  }
}
