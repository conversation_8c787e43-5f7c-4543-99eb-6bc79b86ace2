<template>
  <div class="nature-of-route" v-loading="loading" element-loading-background="rgba(0, 0, 0, 0.4)">
    <Echarts :option="option" v-if="option" height="100%" key="routeKey" />
  </div>
</template>

<script>
import Echarts from '../../echarts/echarts.vue';
import { isBigScreen } from "@/views/cockpit/util/utils";
// api
import { getRouteTypeCount } from "@/api/cockpit/route";

export default {
  name: 'NatureOfRoute',
  components: {
    Echarts,
  },
  data() {
    return {
      isBig: isBigScreen(),
      loading: false,
      option: null,
      rData: [{
        name: '国道',
        value: 1627,
      }, {
        name: '省道',
        value: 987,
      },],
      rColor: ['#3C59FD', '#01FBEF', '#C1A133'],
    }
  },
  async created() {
    this.loading = true;
    await this.getTypeCount();
    this.loading = false;
    this.option = this.initPieCharts(this.rData, this.rColor);
  },
  methods: {
    // 获取 路网专题-路线性质统计 数据
    getTypeCount() {
      return new Promise((resolve, reject) => {
        getRouteTypeCount().then((res) => {
          if (res.code === 200 && res.data) {
            this.rData = res.data.map(v => {
              return {
                name: v.routeType || '',
                value: v.length ? v.length.toFixed(2) - 0 : v.length - 0 || 0,
              }
            })
            resolve(res.data)
          } else {
            reject(res)
          }
        }).catch((err) => {
          reject(err)
        })
      })
    },
    // 初始化饼图
    initPieCharts(dataArr = [], colorArr = []) {
      var trafficWay = dataArr || [];
      var data = [];
      let sum = dataArr.reduce((acc, curr) => acc + curr.value, 0);
      let avg = sum / (dataArr.length * 10);
      let total = avg;
      var color = colorArr || ['#00ffff', '#00cfff', '#006ced', '#ffe000', '#ffa800', '#ff5b00', '#ff3000']
      for (var i = 0; i < trafficWay.length; i++) {
        data.push({
          value: trafficWay[i].value,
          name: trafficWay[i].name,
          itemStyle: {
            normal: {
              borderWidth: this.isBig ? 16 : 8,
              shadowBlur: 2,
              borderColor: color[i],
              shadowColor: color[i]
            }
          }
        }, {
          value: total || 3,
          name: '',
          itemStyle: {
            normal: {
              label: {
                show: false
              },
              labelLine: {
                show: false
              },
              color: 'rgba(0, 0, 0, 0)',
              borderColor: 'rgba(0, 0, 0, 0)',
              borderWidth: 0
            }
          }
        });
      }
      var seriesOption = [
        {
          type: "pie",
          zlevel: 0,
          silent: true,
          radius: ["92%", "96%"],
          center: ['25%', '50%'],
          hoverAnimation: false,
          color: "rgba(0,62,122,1)",
          label: {
            normal: {
              show: false,
            },
          },
          labelLine: {
            normal: {
              show: false,
            },
          },
          data: [1],
        },
        {
          name: '',
          type: 'pie',
          clockWise: false,
          radius: ['78%', '78%'],
          center: ['25%', '50%'],
          hoverAnimation: false,
          label: {
            normal: {
              show: false,
            },
          },
          labelLine: {
            normal: {
              show: false,
            },
          },
          data: data
        }];
      let option = {
        backgroundColor: 'rgba(0,0,0,0)',
        color: color,
        title: {
          text: `路线性质`,
          x: 'center',
          y: 'center',
          left: '24%',
          textAlign: "center",
          textStyle: {
            color: "#fff",
            fontSize: this.isBig ? 42 : 16,
            fontWeight: "700",
            shadowColor: "rgba(27,126,242,0.8)",
            shadowBlur: 10,
            shadowOffsetX: 5,
            shadowOffsetY: 5,
          },
        },
        tooltip: {
          show: false
        },
        toolbox: {
          show: false
        },
        series: seriesOption,
        legend: {
          show: true,
          y: 'center',
          left: '56%',
          icon: 'circle',
          itemWidth: this.isBig ? 30 : 10, // 设置宽度
          itemHeight: this.isBig ? 30 : 10, // 设置高度
          itemGap: this.isBig ? 20 : 10,
          textStyle: {
            color: '#fff',
            rich: {
              title: {
                color: 'rgba(255,255,255,0.8)',
                fontSize: this.isBig ? 24 : 12,
                padding: [3, 0],
              },
              value: {
                color: '#fff',
                fontSize: this.isBig ? 32 : 16,
                fontWeight: 'bold',
              },
              unit: {
                color: 'rgba(255,255,255,0.8)',
                fontSize: this.isBig ? 24 : 12,
              }
            }
          },
          formatter: (name) => {
            let d = dataArr.filter(v => v.name == name);
            let value = d ? d[0].value : null;
            return `{title|${name}：}` + `{value|${value}} ` + `{unit|km}`;
          },
        },
      }
      return option;
    },
  }
}
</script>

<style lang="scss" scoped>
.nature-of-route {
  width: 100%;
  height: 100%;
  padding: 5px 10px;
}
</style>