<template>
  <div class='page-container'>
    <!-- 左侧管理处及路段树形结构 -->
    <div class='left-panel' :class="{ 'collapsed': !relaNav }">
      <div class='panel-content'>
        <h3>管理处及路段</h3>
        <el-tree
          ref='tree'
          :data='filteredTreeData'
          :default-expanded-keys='[1]'
          :expand-on-click-node='false'
          :props='defaultProps'
          highlight-current
          node-key='code'
          @node-click='handleNodeClick'
        ></el-tree>
      </div>
      <!--      <div class="collapse-btn" @click='relaNav = !relaNav'>-->
      <!--        <el-icon><ArrowLeft v-if="relaNav" /><ArrowRight v-else /></el-icon>-->
      <!--      </div>-->
    </div>

    <!-- 中间分析类型列表 -->
    <div class='middle-panel'>
      <div class='panel-content'>
        <h3>分析类型</h3>
        <!-- 分析类型列表 -->
        <div
          v-for='item in mainConfig'
          :key='item.type'
          class='analysis-type-item'
          :class='{ active: mainIndex === item.type }'
          @click='mainIndex = item.type'
        >
          <div class='analysis-type-item-header'>
            <span class='analysis-type-item-title'>{{ item.label }}</span>
          </div>
          <div class='analysis-type-item-description'>{{ item.chart.length }}种分析图表</div>
        </div>
      </div>
    </div>

    <!-- 右侧主内容区域 -->
    <div class='right-panel'>
      <div class='analysis-card'>
        <div class='analysis-card-header'>
          <div class='analysis-card-title'>{{ getCurrentTabLabel }}</div>
          <div class='analysis-card-description'>请选择配置或添加新配置</div>
        </div>
        <div class='analysis-card-content'>
          <div class='tab-actions'>
            <el-button
              type='primary'
              icon='el-icon-plus'
              v-for='chart in getCurrentTab.chart'
              @click='handleAddConfig(chart)'
              size='small'
              class='mb10'
            >
              {{ chart.label }}
            </el-button>
          </div>

          <el-table
            v-loading='loading'
            :data='tableData'
            border
            size='mini'
            height='calc(100vh - 320px)'
            class='mt10'
          >
            <el-table-column label='序号' width='50' type='index' align='center' />
            <el-table-column label='配置名称' width='500' prop='optionName' align='center'></el-table-column>
            <el-table-column label='分析类别' prop='analysisType' align='center'></el-table-column>
            <el-table-column label='绘图类别' prop='drawType' align='center'></el-table-column>
            <el-table-column label='操作' prop='name' align='center'>
              <template slot-scope='scope'>
                <el-button
                  type='text'
                  size='mini'
                  icon='el-icon-edit'
                  @click='handleEditConfig(scope.row)'
                >
                  编辑
                </el-button>
                <el-button
                  type='text'
                  size='mini'
                  icon='el-icon-delete'
                  @click='handleDeleteConfig(scope.row)'
                >
                  删除
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>
    </div>

    <!-- 配置对话框 -->
    <el-dialog :title='dialogTitle' :visible.sync='dialogVisible' append-to-body width='500px'>
      <el-form :model='formData' label-width='120px'>
        <el-form-item
          v-for='(item, index) in formConfig'
          :key='index'
          :label='item.label'
          :prop='item.prop'
          :required="item.isRequired === 'true'"
          :error="item.label + '不能为空'"
          :show-message='false'
        >
          <!-- 文本输入框 -->
          <el-input
            style='width: 100%'
            v-if="item.type === 'text'"
            v-model='formData[item.prop]'
            :placeholder='`请输入${item.label}`'
          ></el-input>

          <!-- 数字输入框 -->
          <el-input-number
            style='width: 100%'
            v-else-if="item.type === 'number'"
            v-model='formData[item.prop]'
            :placeholder='`请输入${item.label}`'
          ></el-input-number>

          <!-- 自定义组件 -->
          <component
            style='width: 100%'
            v-else-if="item.type === 'component'"
            :is='item.component'
            v-model='formData[item.prop]'
          ></component>
        </el-form-item>
        <div style='text-align: right'>
          <el-button type='primary' @click='handleSubmit'>提交</el-button>
          <el-button @click='dialogVisible = false'>取消</el-button>
        </div>
      </el-form>
    </el-dialog>
  </div>
</template>

<script>
import { getJCDomainTree } from '@/api/jgjc/baseInfo/alarmLog'
// import { ArrowLeft, ArrowRight } from '@element-plus/icons-vue'
import SensorSelect from './components/sensorSelect.vue'
import {
  addOneConfig,
  deleteConfigById,
  getConfigUnderStructureCode,
  updateOneConfigById,
} from '@/api/jgjc/dataAnalysis/presets'
import { formConfig, mainConfig } from '@/views/jgjc/dataAnalysis/comm/chartConfig'

export default {
  name: 'Presets',
  components: {
    SensorSelect,
  },
  provide() {
    return {
      getStructureCode: () => this.structureCode,
    }
  },
  data() {
    return {
      // 遮罩层
      loading: false,
      // 左侧组织树
      relaNav: true,
      relaOptions: [],
      filteredTreeData: [],
      defaultProps: {
        children: 'children',
        label: 'label',
      },
      mainConfig: mainConfig,
      mainIndex: 'WSD',
      tableData: [],
      formConfig: [],
      dialogVisible: false,
      dialogTitle: '',
      formData: {},
      chart: {},
      structureCode: '',
    }
  },
  computed: {
    getCurrentTab: function() {
      return this.mainConfig.find(item => item.type === this.mainIndex) || {}
    },
    getCurrentTabLabel: function() {
      return this.getCurrentTab.label || ''
    },
  },
  created() {
    this.getDeptTree()
  },
  methods: {
    // 查询部门下拉树结构
    getDeptTree() {
      getJCDomainTree({}).then(response => {
        this.relaOptions = response.data
        this.filteredTreeData = [...this.relaOptions]
      })
    },
    // 树节点点击事件
    handleNodeClick(nodeData, node) {
      if (node.level == 4) {
        console.log(node)
        this.structureId = nodeData.structureId
        this.structureCode = nodeData.structureCode
        this.handleQuery()
      }
    },
    handleQuery() {
      this.loading = true
      getConfigUnderStructureCode({ structureCode: this.structureCode }).then(res => {
        this.loading = false
        this.tableData = res.result
      })
    },
    handleAddConfig(chart) {
      this.chart = chart || {}
      if (!this.structureCode) {
        this.$message.warning('请选择结构物！')
        return
      }
      this.formConfig = formConfig[chart.formType]
      this.formData = {}
      this.dialogVisible = true
      this.dialogTitle = `配置${chart.label}`
    },
    handleEditConfig(row) {
      this.chart = this.mainConfig.find(item => item.type === row.analysisType).chart.find(item => item.type === row.drawType)
      this.formConfig = formConfig[this.chart.formType]
      this.dialogVisible = true
      this.formData = row
      const detail = JSON.parse(row.detail)
      this.formData.sensorDatail = []
      for (let i = 0; i < detail.length; i++) {
        const item = detail[i]
        if (item.sensorList) {
          const detail = JSON.parse(JSON.stringify(item))
          delete detail.detail
          delete this.formData.sensorDatail
          this.formData = { ...this.formData, ...detail }
          this.formData.sensorList = item.sensorList
          break
        } else {
          this.formData.sensorDatail.push(item)
        }
      }
      console.log(this.formData)
      this.dialogTitle = `修改`
    },
    handleSubmit() {
      const params = {
        id: this.formData.id,
        structureCode: this.structureCode,
        analysisType: this.mainIndex,
        drawType: this.chart.type,
        optionName: this.formData.optionName,
        pictureOrder: this.formData.pictureOrder,
      }
      if (this.formData.sensorDatail) {
        params.detail = this.formData.sensorDatail.map(item => {
          return {
            dataCode: item.dataCode,
            label: item.label,
            sensorCode: item.sensorCode,
            structureCode: this.structureCode,
          }
        })
      }
      if(this.formData.sensorList){
        params.detail = [{
          sensorList: this.formData.sensorList.map(item => {
            return {
              dataCode: item.dataCode,
              label: item.label,
              sensorCode: item.sensorCode,
              structureCode: this.structureCode,
            }
          })
        }]
        this.formConfig.forEach(item => {
          if (item.isDetail) {
            params.detail[0][item.prop] = this.formData[item.prop]
          }
        })
      }
      params.detail = JSON.stringify(params.detail)
      if (this.formData.id) {
        updateOneConfigById(params).then(res => {
          this.$message.success('保存成功！')
          this.handleQuery()
          this.dialogVisible = false
        })
      } else {
        addOneConfig(params).then(res => {
          this.$message.success('保存成功！')
          this.handleQuery()
          this.dialogVisible = false
        })
      }
    },
    handleDeleteConfig(row) {
      this.$modal.confirm('是否确认删除？').then(() => {
        deleteConfigById({ id: row.id }).then(res => {
          this.$message.success('删除成功！')
          this.handleQuery()
        })
      })
    },
  },
}
</script>

<style scoped>
.page-container {
  display: flex;
  height: 100%;
  overflow: hidden;
  background-color: #f5f5f5;
}

/* 左侧树形结构样式 */
.left-panel {
  width: 280px;
  background-color: #ffffff;
  border-right: 1px solid #e6e6e6;
  position: relative;
  transition: all 0.3s;
  display: flex;
}

.left-panel.collapsed {
  width: 0;
  overflow: hidden;
}

/* 中间分析类型面板 */
.middle-panel {
  width: 300px;
  background-color: #ffffff;
  border-right: 1px solid #e6e6e6;
  position: relative;
  transition: all 0.3s;
}

.panel-content {
  padding: 20px;
  overflow-y: auto;
  height: 100%;
}

.collapse-btn {
  width: 20px;
  height: 40px;
  background-color: #ffffff;
  border: 1px solid #e6e6e6;
  border-left: none;
  border-radius: 0 4px 4px 0;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  position: absolute;
  right: -20px;
  top: 50%;
  transform: translateY(-50%);
  z-index: 1;
}

.collapse-btn:hover {
  background-color: #f0f7ff;
}

/* 分析类型列表项样式 */
.analysis-type-item {
  padding: 12px;
  margin-bottom: 10px;
  border-radius: 4px;
  border: 1px solid #e6e6e6;
  cursor: pointer;
  transition: all 0.3s;
}

.analysis-type-item:hover {
  background-color: #f0f7ff;
}

.analysis-type-item.active {
  background-color: #e6f4ff;
  border-color: #1890ff;
}

.analysis-type-item-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.analysis-type-item-title {
  font-size: 14px;
  font-weight: bold;
  color: #333333;
}

.analysis-type-item-description {
  font-size: 12px;
  color: #666666;
}

/* 右侧主内容区域 */
.right-panel {
  flex: 1;
  padding: 20px;
  overflow-y: auto;
  background-color: #ffffff;
}

/* 分析卡片样式 */
.analysis-card {
  margin-bottom: 20px;
  border-radius: 4px;
  background-color: #ffffff;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border: 1px solid #e6e6e6;
}

.analysis-card-header {
  padding: 15px;
  border-bottom: 1px solid #e6e6e6;
}

.analysis-card-title {
  font-size: 16px;
  font-weight: bold;
  color: #333333;
  margin-bottom: 8px;
}

.analysis-card-description {
  font-size: 14px;
  color: #666666;
}

.analysis-card-content {
  padding: 20px;
}

.tab-actions {
  margin-bottom: 15px;
}

.el-table {
  width: 100%;
}

.mb10 {
  margin-bottom: 10px;
}

.mt10 {
  margin-top: 10px;
}

/* 响应式调整 */
@media (max-width: 1200px) {
  .middle-panel {
    width: 250px;
  }
}

@media (max-width: 992px) {
  .left-panel {
    width: 220px;
  }

  .middle-panel {
    width: 200px;
  }
}

@media (max-width: 768px) {
  .page-container {
    flex-direction: column;
  }

  .left-panel,
  .middle-panel {
    width: 100%;
    height: auto;
    max-height: 300px;
  }

  .left-panel.collapsed,
  .middle-panel.collapsed {
    height: 0;
    max-height: 0;
    overflow: hidden;
  }

  .collapse-btn {
    top: auto;
    bottom: -20px;
    left: 50%;
    transform: translateX(-50%) rotate(90deg);
    right: auto;
  }
}
</style>
