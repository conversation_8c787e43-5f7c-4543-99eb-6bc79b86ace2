<template>
  <div class="weather-detail" v-loading="loading" element-loading-background="rgba(0, 0, 0, 0.5)">
    <el-descriptions :column="1" border v-if="!loading">
      <el-descriptions-item>
        <template slot="label">天气</template>
        <el-tag size="small" v-if="datailData.weather">{{ datailData.weather }}</el-tag>
      </el-descriptions-item>
      <el-descriptions-item>
        <template slot="label">预报时间</template>
        {{ datailData.preTime | tiemFormate }}
      </el-descriptions-item>
      <el-descriptions-item>
        <template slot="label">管理处</template>
        {{ datailData.mOffice }}
      </el-descriptions-item>
      <el-descriptions-item>
        <template slot="label">面积</template>
        {{ datailData.area }} ㎡
      </el-descriptions-item>
      <el-descriptions-item>
        <template slot="label">降雨量</template>
        <span>{{ datailData.valueMin ? datailData.valueMin.toFixed(3) : '0' }} mm</span>
        ~
        <span>{{ datailData.valueMax ? datailData.valueMax.toFixed(3) : '0' }} mm</span>
      </el-descriptions-item>
      <el-descriptions-item>
        <template slot="label">影响的桥梁</template>
        {{ datailData.bridgeNum }} 座
      </el-descriptions-item>
      <el-descriptions-item>
        <template slot="label">影响的隧道</template>
        {{ datailData.tunnelNum }} 座
      </el-descriptions-item>
      <el-descriptions-item>
        <template slot="label">影响的涵洞</template>
        {{ datailData.culvertNum }} 个
      </el-descriptions-item>
      <el-descriptions-item>
        <template slot="label">涉灾隐患点</template>
        {{ datailData.xqHazardsNum }} 个
      </el-descriptions-item>
    </el-descriptions>

    <el-table :data="tableData" border style="width: 100%" v-if="tableData.length" height="60vh">
      <el-table-column prop="managementOffice" label="管理处"> </el-table-column>
      <el-table-column prop="roadNum" label="路段编码" width="180"></el-table-column>
      <el-table-column prop="roadName" label="路段名称"></el-table-column>
      <el-table-column prop="maintenanceSection" label="养护路段名称"></el-table-column>
      <el-table-column prop="pileStart" label="起点桩号"></el-table-column>
      <el-table-column prop="pileEnd" label="终点桩号"></el-table-column>
      <el-table-column prop="sectionLength" label="长度">
        <template slot-scope="{row}">
          {{ row.sectionLength ? (row.sectionLength / 1000).toFixed(2) + ' km' : '-' }}
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>

<script>
import { getDetail } from '@/api/oneMap/weather';

export default {
  name: 'WeatherDetail',
  props: {
    id: {
      type: [String, Number],
      default: ''
    },
    formData: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      datailData: {},
      //type  0:无雨 1:小雨 2:中雨 3:大雨 4:暴雨 5:大暴雨 6:特大暴雨 11: 小雪 12: 中雪 13: 大雪 14: 暴雪
      typeList: [
        {
          name: '无雨',
          type: 0,
          color: 'rgba(0,0,0,0)',
          icon: require('@/assets/map/weather/rainstorm.png'),
        },
        {
          name: '小雨',
          type: 1,
          color: 'rgba(143,248,136, 0.6)',
          icon: require('@/assets/map/weather/rain_is_light.png'),
          gifUrl: require('@/assets/map/weather/gif_xy.gif'),
        },
        {
          name: '中雨',
          type: 2,
          color: 'rgba(36,164,31,0.6)',
          icon: require('@/assets/map/weather/moderate_rain.png'),

          gifUrl: require('@/assets/map/weather/gif_zy.gif'),
        },
        {
          name: '大雨',
          type: 3,
          color: 'rgba(86,173,251,0.6)',
          icon: require('@/assets/map/weather/a_heavy_rain.png'),
          gifUrl: require('@/assets/map/weather/gif_dy.gif'),
        },
        {
          name: '暴雨',
          type: 4,
          color: 'rgba(43,0,247,0.6)',
          icon: require('@/assets/map/weather/rainstorm.png'),
          gifUrl: require('@/assets/map/weather/gif_by.gif'),
        },
        {
          name: '大暴雨',
          type: 5,
          color: 'rgba(254,6,1,0.5)',
          icon: require('@/assets/map/weather/rainstorm.png'),
          gifUrl: require('@/assets/map/weather/gif_dby.gif'),
        },
        {
          name: '特大暴雨',
          type: 6,
          color: 'rgba(254,6,1,0.6)',
          icon: require('@/assets/map/weather/rainstorm.png'),
        },
        {
          name: '小雪',
          type: 11,
          color: '#000000',
          icon: require('@/assets/map/weather/rainstorm.png'),
        },
        {
          name: '中雪',
          type: 12,
          color: '#000000',
          icon: require('@/assets/map/weather/rainstorm.png'),
        },
        {
          name: '大雪',
          type: 13,
          color: '#000000',
          icon: require('@/assets/map/weather/rainstorm.png'),
        },
        {
          name: '暴雪',
          type: 14,
          color: '#000000',
          icon: require('@/assets/map/weather/rainstorm.png'),
        },
      ],
      loading: false,
      tableData: []
    }
  },
  watch: {
    id: {
      handler(newVal, oldVal) {
        if (newVal) {
          this.getDetailData();
        }
      },
      deep: true,
      immediate: true
    }
  },
  filters: {
    tiemFormate(value) {
      if (!value) return '';
      if (typeof value === 'string') {
        const date = new Date(value);
        if (!isNaN(date.getTime())) {
          const year = date.getFullYear();
          const month = String(date.getMonth() + 1).padStart(2, '0');
          const day = String(date.getDate()).padStart(2, '0');
          const hours = String(date.getHours()).padStart(2, '0');
          const minutes = String(date.getMinutes()).padStart(2, '0');
          const seconds = String(date.getSeconds()).padStart(2, '0');
          return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
        }
      }
      return '';
    }
  },
  mounted() { },
  methods: {
    safeDivide(a, b, decimalPlaces) {
      const factor = Math.pow(10, decimalPlaces);
      return (a * factor) / (b * factor);
    },
    safeMultiply(a, b) {
      const decimalA = (a.toString().split('.')[1] || '').length;
      const decimalB = (b.toString().split('.')[1] || '').length;
      const factor = Math.pow(10, decimalA + decimalB); // 放大倍数为两数小数位数之和
      let num = (a * factor) * (b * factor) / (factor * factor)
      num = num.toString().split('.')[1]?.length > 3 ? Number(num.toFixed(3)) : num;
      return num;
    },
    stakeFormat(value) {
      if (!value) return "";
      let prefix = parseInt(value / 1000);
      let val = "";
      if (prefix < 1) {
        val = "k0+" + value;
      } else {
        let str = this.safeDivide(value, 1000, 3) + "";

        const parts = str.split(".");
        const integerPart = parseInt(parts[0]);

        const decimalPart = parts[1] ? parseFloat("0." + parts[1]) : 0;

        val = "k" + integerPart + "+" + this.safeMultiply(decimalPart, 1000);
      }
      return val;
    },
    getDetailData() {
      let { params } = this.formData;
      params.id = this.id;
      this.loading = true;
      getDetail(params).then(res => {
        if (res && res.code === 200 && res.data) {
          let data = res.data || {};
          let { relationList } = data;
          this.datailData = data;
          this.datailData.weather = this.typeList.find(item => item.type == data.type).name || '';
          if (relationList && relationList.length) {
            this.datailData.mOffice = [...new Set(relationList.map(v => v.managementOffice))].join(',')
            this.tableData = relationList.map(v => {
              v.pileStart = this.stakeFormat(v.pileStart)
              v.pileEnd = this.stakeFormat(v.pileEnd)
              return v;
            })
          }
        }
      }).finally(() => {
        this.loading = false;
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.weather-detail {
  width: 100%;
  height: 100%;
  overflow: hidden;
  padding-bottom: 1vh;
}

::v-deep .el-descriptions {
  .el-descriptions__body {
    background-color: rgba(4, 17, 48, 0.6) !important;
    color: #ffffff;
  }

  .is-bordered .el-descriptions-item__cell {
    border: 1px solid rgba(1, 102, 254, 0.4);
  }

  .el-descriptions-item__label.is-bordered-label {
    background-color: rgba(4, 17, 48, 0.6) !important;
    color: #ffffff;
    font-size: 1.4vh;
    min-width: 100px
  }

  .el-descriptions-item__content {
    font-size: 1.3vh;
  }
}

::v-deep .el-table {
  background: unset;
  border: unset;
  min-width: unset;

  &::before {
    background-color: unset;
  }

  &::after {
    background-color: unset;
  }

  tr {
    background-color: unset;
  }

  tr:nth-child(even) {
    background: rgba(86, 145, 255, 0);
    color: #ffffff;
  }

  td {
    color: #ffffff;
  }

  td,
  th.is-leaf {
    border: 1px solid rgba(1, 102, 254, 0.4);
  }

  .el-table__header-wrapper tr th {
    background-color: rgba(1, 102, 254, 0.2);
    color: #ffffff !important;
    font-size: vwpx(28px);
  }

  tbody {
    background-color: unset;
    border: none;
    cursor: pointer;
  }

  .el-table__body tr:hover>td {
    background-color: unset;
  }

  .el-table__body tr.current-row>td {
    background-color: rgba(1, 102, 254, 0.2) !important;
  }

  .el-table__body tr.current-row:hover {
    background-color: rgba(1, 102, 254, 0.2);
  }

  .el-table__row {
    height: vwpx(76px) !important;
    line-height: vwpx(76px) !important;
  }

  .cell {
    line-height: unset !important;
  }
}
</style>
