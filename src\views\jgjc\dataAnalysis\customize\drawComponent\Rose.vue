<template>
  <el-container>
    <el-header>
      <div class="data2">
        <el-form
            :model="form2"
            :inline="true"
            label-position="right"
            label-suffix="："
            ref="tigaForm2"
            label-width="85px">
          <el-form-item label="监测类型" prop="monitorType">
            <el-select
                v-model="form2.monitorType"
                placeholder="请选择监测类型"
                @change="changeMonitorType2"
                no-data-text="无数据"
                value-key="content"
                size="small"
            >
              <el-option
                  v-for="item in (monitorTypeList.filter(item => item.content.indexOf('风') >= 0))"
                  :key="item.code"
                  :label="item.content"
                  :value="item">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="监测内容" prop="monitorContent">
            <el-select
                v-model="form2.monitorContent"
                default-first-option
                placeholder="请选择监测内容"
                @change="changeMonitorContent2"
                no-data-text="无数据"
                value-key="content"
                size="small"
            >
              <el-option
                  v-for="item in monitorContentList2"
                  :key="item.code"
                  :label="item.content"
                  :value="item">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="监测位置" prop="monitorLocation">
            <el-select
                v-model="form2.monitorLocation"
                default-first-option
                placeholder="请选择监测位置"
                @change="changeMonitorLocation2"
                no-data-text="无数据"
                value-key="content"
                size="small"
            >
              <el-option
                  v-for="item in monitorLocationList2"
                  :key="item.code"
                  :label="item.content"
                  :value="item">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="传感器" prop="sensorId">
            <el-select
                v-model="form2.sensorId"
                placeholder="请选择传感器"
                @change="changeSensor2"
                no-data-text="无数据"
                value-key="sensorId"
                size="small"
            >
              <el-option
                  v-for="item in sensorInfoList2"
                  :key="item.code"
                  :label="item.sensorInstallCode"
                  :value="item">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="具体类型" prop="specificMonitorType">
            <el-select
                v-model="form2.specificMonitorType"
                filterable
                collapse-tags
                :placeholder="tips1"
                no-data-text="无数据"
                size="small"
                value-key="specificMonitorTypeId"
                :disabled="isDraw"
            >
              <el-option
                  v-for="item in specificMonitorTypeList2"
                  :label="item.specificMonitorTypeName"
                  :key="item.specificMonitorTypeId"
                  :value="item.specificMonitorTypeId"
              >
              </el-option>
            </el-select>
          </el-form-item>
        </el-form>
      </div>
      <div class="data1">
        <el-form
            :model="form"
            :inline="true"
            label-position="right"
            label-suffix="："
            ref="tigaForm3"
            label-width="85px">
          <el-form-item label="传感器" prop="sensorId">
            <el-select
                v-model="form.sensorId"
                placeholder="请选择传感器"
                @change="changeSensor"
                no-data-text="无数据"
                value-key="sensorId"
                size="small"
            >
              <el-option
                  v-for="item in sensorInfoList2"
                  :key="item.code"
                  :label="item.sensorInstallCode"
                  :value="item">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="具体类型" prop="specificMonitorType">
            <el-select
                v-model="form.specificMonitorType"
                filterable
                collapse-tags
                :placeholder="tips2"
                no-data-text="无数据"
                size="small"
                value-key="specificMonitorTypeId"
                :disabled="isDraw"
            >
              <el-option
                  v-for="item in specificMonitorTypeList"
                  :label="item.specificMonitorTypeName"
                  :key="item.specificMonitorTypeId"
                  :value="item.specificMonitorTypeId"
              >
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="时间范围" prop="timerange">
            <el-date-picker
                size="medium"
                style="width:200px"
                type="datetimerange"
                v-model="form.timerange"
                value-format="yyyy-MM-dd HH:mm:ss"
                format="yyyy-MM-dd HH:mm:ss"
                @on-change="statusDateChange"
                :transfer="true"
            ></el-date-picker>
          </el-form-item>
          <el-form-item>
            <el-button
                type="primary"
                @click="draw"
                :loading="loading"
                size="small"
            >
              <span>绘制</span>
            </el-button>
          </el-form-item>
          <el-form-item>
            当前选择结构物为：{{currentStructure.label}}
          </el-form-item>
        </el-form>
      </div>
    </el-header>
    <el-main>
      <div id="myChart4" v-loading="loading" style="display: inline-block; width: 50%; height: 400px; border-right: 1px solid #eee"></div>
      <div id="myChart5" v-loading="loading" style="display: inline-block; width: 50%; height: 400px"></div>
    </el-main>
  </el-container>
</template>

<script>
import {
  getSensorByDataCode,roseAnalysis
} from "@/api/jgjc/dataAnalysis/customize/index.js";
import * as echarts from "echarts";
export default {
  name: "Rose",
  props: ['monitorTypeList', 'currentStructure', 'isDraw'],
  emits: ['changeDraw'],
  data() {
    return {
      isZD: false,
      tips1: "请选择风速",
      tips2: "请选择风向",
      processMethod: "风速风向玫瑰图",
      monitorContentList2: [],
      monitorLocationList2: [],
      sensorInfoList2: [],
      specificMonitorTypeList: [],
      specificMonitorTypeList2: [],
      form: {
        sensorId: '',
        specificMonitorType: '',
        timerange: ''
      },
      form2: {
        monitorType: '',
        monitorContent: '',
        monitorLocation: '',
        sensorId: '',
        specificMonitorType: '',
      },
      sensorInfo: {},
      sensorInfo2: {},
      loading: false
    };
  },
  methods: {
    // 判断是否为长时查询
    async isLongCheck(isZD, isPreprocess) {
      // 计算时间差值
      let startTime = new Date(this.form.timerange[0]);
      let endTime = new Date(this.form.timerange[1]);
      let deltaTime = endTime.getTime() - startTime.getTime()
      let flag = false

      if(isZD){
        // 振动数据 1天+原始值 或 任意时间+预处理
        if(isPreprocess){
          flag = true
        }else{
          if(deltaTime > 86400000){
            flag = true
          }
        }
      }else {
        // 普通数据 6月+原始值 或 2月+预处理
        if(isPreprocess){
          if(deltaTime > 86400000 * 60){
            flag = true
          }
        }else{
          if(deltaTime > 86400000 * 180){
            flag = true
          }
        }
      }

      if(flag){
        const res = await this.$confirm('当前条件可能造成查询时间过长, 是否继续?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).catch(err => err)
        return res === 'confirm';
      }
      return true
    },
    // “绘制”按钮的方法
    async draw() {
      if(
          this.form.sensorId === '' ||
          this.form.specificMonitorType === '' ||
          this.form2.monitorType === '' ||
          this.form2.monitorContent === '' ||
          this.form2.monitorLocation === '' ||
          this.form2.sensorId === '' ||
          this.form2.specificMonitorType === '' ||
          this.form.timerange.length === 0 ||
          this.form.timerange[0] === ''
      ){
        this.$message({
          type: 'warning',
          message: "查询数据前请注意查询条件的完整性"
        });
        return;
      }
      if(await this.isLongCheck(this.isZD, true)){
        this.loading = true
        const {code, msg, data} = await roseAnalysis({
          structureCode: this.currentStructure.structureCode,
          windDirectionDataCode: this.form.sensorId,
          windDirectionSensorCode: this.form.specificMonitorType,
          windSpeedDataCode: this.form2.sensorId,
          windSpeedSensorCode: this.form2.specificMonitorType,
          startTime: this.form.timerange[0],
          endTime: this.form.timerange[1],
          granularityNum: 10,
          granularityType: 3,
          analysisType: 2,
        })
        if(code){
          if (data.processedData?.errMsg) {
            this.$message.error(data.processedData?.errMsg);
          } else {
            this.setRoseChart("myChart4", "风速玫瑰图", data.processedData.windSpeedFreq, data.unit)
            this.setRoseChart("myChart5", "风向玫瑰图", data.processedData.windDirectionFreq.map(item => item*100), "%")
          }
        }else{
          this.$message.error("算法调用失败");
          console.log(msg)
        }
        this.loading = false
      }else {
        // 用户取消了本次查询
      }
    },
    // 绘制风速风向玫瑰图
    setRoseChart(chartName, title, data, unit) {
      let myChart = echarts.getInstanceByDom(document.getElementById(chartName))
      if(myChart !== undefined){
        myChart.dispose()
      }
      myChart = echarts.init(document.getElementById(chartName))
      //风向频率数组，按顺时针方向，上北下南左西右东，16方位。若要使线重合，请将下标为0的数值添加到末尾。
      let percentList = data;
      //风向 16方位
      let namelist = ['北', '', '东北', '', '东', '', '东南',
        '', '南', '', '西南', '', '西', '', '西北', ''];
      //数据
      const seriesData = [];
      for (let i = 0; i <= namelist.length; i++) {
        var jiaodu = (360 / namelist.length) * i;
        seriesData.push([Number(percentList[i]).toFixed(3), jiaodu]); //[径向，角度]
      }
      let option = {
        toolbox: {
          feature: {
            saveAsImage: {
              title:'保存'
            },
          },
        },
        backgroundColor: '#fff',
        title: {
          text: title,
          textStyle: {
            color: '#666'
          },
          left: 0,
        },
        angleAxis: {
          type: 'value',
          startAngle: 90,
          boundaryGap: false,
          min: 0, //最小刻度
          max: 360, //最大刻度
          interval: 360 / namelist.length, //间隔刻度 16方位间隔22.5，可改8方位
          axisLabel: {
            show: true,
            color: '#1c2383',
            fontWeight: 'bold',
            formatter: function (value, index) {
              return namelist[index];
            }
          },
          splitLine: {
            show: true,
            lineStyle: {
              color: '#9a9a9a',
              width: 1
            }
          },
          axisLine: {
            show: true,
            lineStyle: {
              color: '#333',
              width: 3
            }
          },
          axisTick: {
            alignWithLabel: true
          },
          z: 10
          //data: namelist,
        },
        radiusAxis: {
          z: 200,
          axisLabel: {
            show: true,
            color: '#000',
            fontWeight: 'bold',
            formatter: '{value}'+unit
          },
          max: Math.max(...percentList).toFixed(2),
          splitLine: {
            show: true,
            lineStyle: {
              color: '#000',
              width: 1
            }
          },
          axisLine: {
            show: true,
            lineStyle: {
              color: '#000000',
              width: 1
            }
          }
        },
        polar: {},
        tooltip: {
          trigger: 'item',
          borderColor: '#12510e',
          borderWidth: 1,
          textStyle: {
            color: '#000000',
            fontWeight: 'bold',
            align:'left'
          },
          axisPointer: {
            z: 300,
            type: 'cross',
            label: {
              color: 'white',
              backgroundColor: 'black'
            }
          },
          formatter: function(params){
            //第一个和最后一个点保持一致，防止第一个点时找不见数据
            const dataIndex = params.dataIndex >= namelist.length ? 0 : params.dataIndex;
            return namelist[dataIndex] + params.data[0];
          }
        },
        series: [
          {
            type: 'line',
            data: seriesData,
            coordinateSystem: 'polar',
            showBackground: true,
            backgroundStyle: {
              color: '#fff'
            },
            // color: ['#2e53ec'],
            color: ['#054b07'],
            itemStyle: {
              normal: {
                shadowBlur: 10,
                shadowOffsetX: 0,
                shadowColor: 'rgba(189, 203, 203, 0.5)'
              },
              emphasis: {
                shadowBlur: 10,
                shadowOffsetX: 0,
                shadowColor: 'rgba(105, 113, 113, 0.5)',
                opacity: 0.9
              }
            },
            areaStyle: {
              color: "green",
              opacity: 0.5
            }
          }
        ]
      };


      option && myChart.setOption(option);
      //自适应大小
      window.onresize = function () {
        myChart.resize();
      };
    },
    // 下面是一系列下拉框的change方法
    async changeSensor(e) {
      this.specificMonitorTypeList.splice(0)
      this.form.specificMonitorType = '';
      this.form.sensorId = e.sensorInstallCode
      this.sensorInfo = e
      e.specificMonitorTypeId.forEach((value, index)=>{
        this.specificMonitorTypeList.push({
          specificMonitorTypeName: e.specificMonitorTypeName[index],
          specificMonitorTypeId: e.specificMonitorTypeId[index],
        })
      })
    },

    async changeMonitorType2(e) {
      this.form2.monitorType = e.content
      this.isZD = (
          e.content === "振动" ||
          e.content === "地震" ||
          e.content === "索力"
      )
      this.monitorContentList2.splice(0)
      this.monitorLocationList2.splice(0)
      this.sensorInfoList2.splice(0)
      this.specificMonitorTypeList2.splice(0)
      this.form2.monitorContent = '';
      this.form2.monitorLocation = '';
      this.form2.sensorId = '';
      this.form2.specificMonitorType = '';
      this.form.sensorId = '';
      this.form.specificMonitorType = '';
      const monitorContentList = e.children;
      for (const monitorContent of monitorContentList) {
        this.monitorContentList2.push(monitorContent)
      }
    },

    async changeMonitorContent2(e) {
      this.form2.monitorContent = e.content
      this.monitorLocationList2.splice(0)
      this.sensorInfoList2.splice(0)
      this.specificMonitorTypeList2.splice(0)
      this.form2.monitorLocation = '';
      this.form2.sensorId = '';
      this.form2.specificMonitorType = '';
      this.form.sensorId = '';
      this.form.specificMonitorType = '';
      const monitorLocationList = e.children;
      for (const monitorLocation of monitorLocationList) {
        this.monitorLocationList2.push(monitorLocation)
      }
    },

    async changeMonitorLocation2(e) {
      this.form2.monitorLocation = e.content
      this.sensorInfoList2.splice(0)
      this.specificMonitorTypeList2.splice(0)
      this.form2.sensorId = '';
      this.form2.specificMonitorType = '';
      this.form.sensorId = '';
      this.form.specificMonitorType = '';
      const ref = await getSensorByDataCode({dataCode: e.code})
      const sensorInfoList = ref.data
      for (const sensorInfo of sensorInfoList) {
        this.sensorInfoList2.push({
          code: sensorInfo.code,
          sensorId: sensorInfo.sensorId,
          sensorInstallCode: sensorInfo.dataCode,
          specificMonitorTypeId: sensorInfo.children.map(item => item.sensorCode),
          specificMonitorTypeName: sensorInfo.children.map(item => item.typeName),
        })
      }
    },

    async changeSensor2(e) {
      this.specificMonitorTypeList2.splice(0)
      this.form2.specificMonitorType = '';
      this.form2.sensorId = e.sensorInstallCode
      this.sensorInfo2 = e
      e.specificMonitorTypeId.forEach((value, index)=>{
        this.specificMonitorTypeList2.push({
          specificMonitorTypeName: e.specificMonitorTypeName[index],
          specificMonitorTypeId: e.specificMonitorTypeId[index],
        })
      })
    },

    // 时间范围下拉框的change事件
    statusDateChange(e){
      this.form.timerange = e;
    },

    // 清空表单
    resetThisForm(){
      this.monitorContentList2.splice(0)
      this.monitorLocationList2.splice(0)
      this.sensorInfoList2.splice(0)
      this.specificMonitorTypeList2.splice(0)
      this.form2.monitorContent = '';
      this.form2.monitorLocation = '';
      this.form2.sensorId = '';
      this.form2.specificMonitorType = '';
      this.form2.monitorType = '';
      this.form.sensorId = '';
      this.form.specificMonitorType = '';
    },

    //自动选择风速风向
    async autoSelectWind() {
        if(this.currentStructure?.label === undefined){
          return;
        }
        // this.$message({type: 'warning', message: "请分别选择风速和风向", duration: 4000});
        this.monitorContentList2.splice(0)
        this.monitorLocationList2.splice(0)
        this.sensorInfoList2.splice(0)
        this.specificMonitorTypeList2.splice(0)
        this.form2.monitorType = '';
        this.form2.monitorContent = '';
        this.form2.monitorLocation = '';
        this.form2.sensorId = '';
        this.form2.specificMonitorType = '';
        // 以“风”字过滤监测类型列表
        this.monitorTypeListFilter = this.monitorTypeList.filter(item => item.content.indexOf('风') >= 0)
        // 风速风向自动选 选监测类型
        this.form2.monitorType = this.monitorTypeListFilter[0]
        if(this.form2.monitorType?.code === undefined){
          return;
        }
        // 选监测内容
        const monitorContentList = this.form2.monitorType.children
        for (const monitorContent of monitorContentList) {
          this.monitorContentList2.push(monitorContent)
        }
        this.form2.monitorContent = this.monitorContentList2[0]
        // 选监测位置
        const monitorLocationList = this.form2.monitorContent.children;
        for (const monitorLocation of monitorLocationList) {
          this.monitorLocationList2.push(monitorLocation)
        }
        this.form2.monitorLocation = this.monitorLocationList2[0]
        // 选传感器
      const ref = await getSensorByDataCode({dataCode: e.code})
      const sensorInfoList = ref.data
        for (const sensorInfo of sensorInfoList) {
          this.sensorInfoList2.push({
            code: sensorInfo.code,
            sensorId: sensorInfo.sensorId,
            sensorInstallCode: sensorInfo.dataCode,
            specificMonitorTypeId: sensorInfo.children.map(item => item.sensorCode),
            specificMonitorTypeName: sensorInfo.children.map(item => item.typeName),
          })
        }
        this.form2.sensorId = this.sensorInfoList2[0]
        this.form.sensorId = this.sensorInfoList2[0]
        this.sensorInfo2 = this.sensorInfoList2[0]
        this.sensorInfo = this.sensorInfoList2[0]
        // 选具体监测类型
        this.sensorInfoList2[0].specificMonitorTypeId.forEach((value, index)=>{
          this.specificMonitorTypeList2.push({
            specificMonitorTypeName: this.sensorInfoList2[0].specificMonitorTypeName[index],
            specificMonitorTypeId: this.sensorInfoList2[0].specificMonitorTypeId[index],
          })
          this.specificMonitorTypeList.push({
            specificMonitorTypeName: this.sensorInfoList2[0].specificMonitorTypeName[index],
            specificMonitorTypeId: this.sensorInfoList2[0].specificMonitorTypeId[index],
          })
        })
        if(this.specificMonitorTypeList[0].specificMonitorTypeName === '风向'){
          this.form2.specificMonitorType = this.specificMonitorTypeList[1].specificMonitorTypeId
          this.form.specificMonitorType = this.specificMonitorTypeList[0].specificMonitorTypeId
        }else {
          this.form2.specificMonitorType = this.specificMonitorTypeList[0].specificMonitorTypeId
          this.form.specificMonitorType = this.specificMonitorTypeList[1].specificMonitorTypeId
        }
      },
    },
}

</script>

<style scoped>
.el-header{
  height: auto !important;
  padding: 0;
  color: #333;
  border-bottom: 1px solid #eee;
}
.el-form-item {
  display: inline-block;
  height: 40px;
  margin: 5px 0 5px 5px;
}
.el-form {
  text-align: left;
}
.el-button {
  margin-left: 5px;
}
/deep/.ivu-input {
  font-size: 14px !important;
}
/deep/.el-input__inner {
  padding: 0 0 0 10px !important;
}
.el-main{
  padding: 0;
  text-align: center;
  overflow: hidden;
}
.data1 {
  width: 100%;
  font-family: "Microsoft YaHei";
  display: flex;
  justify-content: left;
}
.data2 {
  width: 100%;
  font-family: "Microsoft YaHei";
  display: flex;
  justify-content: left;
  border-bottom: 1px solid #eee;
}
</style>
