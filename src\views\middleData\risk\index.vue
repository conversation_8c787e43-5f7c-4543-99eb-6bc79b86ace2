<template>
  <PageContainer>
    <template slot="search">
      <el-form :model="queryParams" ref="queryForm" :inline="true">
        <div style="display: flex; align-items: center; margin: 0">
          <el-form-item label="" prop="roadType" style="margin: 5px 10px 0 0">
            <CascadeSelection style="min-width: 192px" :form-data="queryParams" v-model="queryParams" types="201"
              multiple />
          </el-form-item>
          <!-- <RangeInput @startValue="
          (v) => {
            queryParams.startStake
              = v;
          }
        " @endValue="
          (v) => {
            queryParams.endStake = v;
          }
        " /> -->
          <el-form-item label="" prop="roadType" style="margin: 5px 10px 0 0">
            <el-select v-model="queryParams.roadType" placeholder="请选择公路类型" clearable @keyup.enter.native="handleQuery">
              <el-option v-for="dict in dict.type.sys_road_type" :key="dict.value" :label="dict.label"
                :value="dict.value" />
            </el-select>
          </el-form-item>
          <el-form-item label="" prop="assetName" style="margin: 5px 10px 0 0">
            <el-input v-model="queryParams.assetName" placeholder="请输入结构物名称" clearable style="width: 240px"
              @keyup.enter.native="handleQuery" />
          </el-form-item>
          <el-form-item label="" prop="roadType" style="margin: 5px 10px 0 0">
            <div style="min-width: 240px">
              <el-button type="primary" icon="el-icon-search" @click="handleQuery">搜索</el-button>
              <el-button icon="el-icon-refresh" @click="resetQuery">重置</el-button>
              <el-button v-show="!showSearch" icon="el-icon-arrow-down" circle @click="showSearch = true" />
              <el-button v-show="showSearch" icon="el-icon-arrow-up" style="
              color: #1890ff;
              border-color: #badeff;
              background-color: #e8f4ff;
            " circle @click="showSearch = false" />
            </div>
          </el-form-item>
        </div>
        <template v-if="showSearch">
          <el-form-item label="" prop="assetType" style="margin: 5px 10px 0 0">
            <el-select v-model="queryParams.assetType" placeholder="请选择结构物类型" clearable
              @keyup.enter.native="handleQuery">
              <el-option v-for="dict in dict.type.sys_hazard_asset_type" :key="dict.value" :label="dict.label"
                :value="dict.value" />
            </el-select>
          </el-form-item>
          <el-form-item label="" prop="hazardLocation" style="margin: 5px 10px 0 0">
            <el-input v-model="queryParams.hazardLocation" placeholder="请输入灾害风险隐患部位" clearable
              prefix-icon="el-icon-user" style="width: 240px" @keyup.enter.native="handleQuery" />
          </el-form-item>
          <el-form-item label="" prop="hazardLevel" style="margin: 5px 10px 0 0">
            <el-select v-model="queryParams.hazardLevel" placeholder="请选择隐患等级" style="width: 100%;" clearable
              @keyup.enter.native="handleQuery">
              <el-option v-for="dict in dict.type.sys_hazard_level" :key="dict.value" :label="dict.label"
                :value="parseInt(dict.value)"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="" prop="rectificationCompleted" style="margin: 5px 10px 0 0">
            <el-select v-model="queryParams.rectificationCompleted" placeholder="请选择是否完成整治" style="width: 100%;"
              clearable @keyup.enter.native="handleQuery">
              <el-option label="是" value="是"></el-option>
              <el-option label="否" value="否"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="" prop="isNaturalDisaster" style="margin: 5px 10px 0 0">
            <el-select v-model="queryParams.isNaturalDisaster" placeholder="请选择是否属于5类自然灾害" style="width: 100%;"
              clearable @keyup.enter.native="handleQuery">
              <el-option label="是" value="是"></el-option>
              <el-option label="否" value="否"></el-option>
            </el-select>
          </el-form-item>
        </template>
      </el-form>
    </template>
<!--    <template slot="header">-->
<!--      &lt;!&ndash;操作按钮区开始&ndash;&gt;-->
<!--      <el-row :gutter="10" class="mb8">-->
<!--        <el-col :span="1.5">-->
<!--          <el-button type="primary" icon="el-icon-plus" size="mini" @click="handleAdd"-->
<!--            v-hasPermi="['middleData:risk:add']">新增</el-button>-->
<!--        </el-col>-->
<!--        <el-col :span="1.5">-->
<!--          <el-button type="success" icon="el-icon-edit" size="mini" :disabled="single" @click="handleUpdate"-->
<!--            v-hasPermi="['middleData:risk:edit']">修改</el-button>-->
<!--        </el-col>-->
<!--        <el-col :span="1.5">-->
<!--          <el-button type="danger" icon="el-icon-delete" size="mini" :disabled="multiple" @click="handleDelete"-->
<!--            v-hasPermi="['middleData:risk:remove']">删除</el-button>-->
<!--        </el-col>-->
<!--        <el-col :span="1.5">-->
<!--          <el-button type="info" plain icon="el-icon-upload2" size="mini" @click="handleImport"-->
<!--            v-hasPermi="['middleData:risk:import']">导入</el-button>-->
<!--        </el-col>-->
<!--        <el-col :span="1.5">-->
<!--          <el-button type="warning" icon="el-icon-download" size="mini" @click="handleExport"-->
<!--            v-hasPermi="['middleData:risk:export']">导出</el-button>-->
<!--        </el-col>-->
<!--        <right-toolbar :showSearch.sync="showSearch" @queryTable="getList" :columns="columns"></right-toolbar>-->
<!--      </el-row>-->
<!--      &lt;!&ndash;操作按钮区结束&ndash;&gt;-->
<!--    </template>-->
    <template slot="body">
      <!--数据表格开始-->
      <el-table v-adjust-table ref="table" size="mini"
        :height="showSearch ? 'calc(100vh - 320px)' : 'calc(100vh - 260px)'" style="width: 100%" v-loading="loading"
        border :data="riskList" @selection-change="handleSelectionChange" :row-style="rowStyle"
        @row-click="handleRowClick">
        <el-table-column type="selection" width="50" align="center" />
        <el-table-column fixed label="序号" type="index" width="50">
          <template v-slot="scope">
            {{ (scope.$index + (queryParams.pageNum - 1) * queryParams.pageSize) + 1 }}
          </template>
        </el-table-column>
        <el-table-column label="省份" align="center" prop="provinceName" v-if="cmpColumns('省份')" />
        <el-table-column label="市/州" align="center" prop="cityName" v-if="cmpColumns('市/州')" />
        <el-table-column label="管理处" align="center" prop="managementOffice" v-if="cmpColumns('管理处')" />
        <el-table-column label="养护路段" align="center" prop="maintenanceSection" v-if="cmpColumns('养护路段')" />
        <el-table-column label="公路类型" align="center" prop="roadType" v-if="cmpColumns('公路类型')">
          <template v-slot="scope">
            <dict-tag :options="dict.type.sys_road_type" :value="scope.row.roadType" />
          </template>
        </el-table-column>
        <el-table-column label="公路编号" align="center" prop="roadNumber" v-if="cmpColumns('公路编号')" />
        <el-table-column label="中心桩号/位置桩号" align="center" prop="centerStake" v-if="cmpColumns('中心桩号/位置桩号')" />
        <el-table-column label="起点桩号" align="center" prop="startStake" v-if="cmpColumns('起点桩号')" />
        <el-table-column label="止点桩号" align="center" prop="endStake" v-if="cmpColumns('止点桩号')" />
        <el-table-column label="灾害风险隐患类型" align="center" prop="hazardType" v-if="cmpColumns('灾害风险隐患类型')" />
        <el-table-column label="结构物名称" align="center" prop="assetName" show-overflow-tooltip
          v-if="cmpColumns('结构物名称')" />
        <el-table-column label="结构物类型" align="center" prop="assetType" v-if="cmpColumns('结构物类型')">
          <template v-slot="scope">
            <dict-tag :options="dict.type.sys_hazard_asset_type" :value="scope.row.assetType" />
          </template>
        </el-table-column>
        <el-table-column label="灾害风险隐患部位" align="center" prop="hazardLocation" v-if="cmpColumns('灾害风险隐患部位')" />
        <el-table-column label="隐患等级" align="center" prop="hazardLevel" v-if="cmpColumns('隐患等级')">
          <template v-slot="scope">
            <dict-tag :options="dict.type.sys_hazard_level" :value="scope.row.hazardLevel" />
          </template>
        </el-table-column>
        <!-- <el-table-column label="灾害风险隐患描述" align="center" prop="hazardDescription" /> -->
        <!-- <el-table-column label="照片" align="center" prop="photo" /> -->
        <el-table-column label="是否完成整治" align="center" prop="rectificationCompleted" v-if="cmpColumns('是否完成整治')" />
        <!-- <el-table-column label="已(拟)采取的措施" align="center" prop="measuresDetail" /> -->
        <el-table-column label="整治完成时限" align="center" prop="completionDeadline" v-if="cmpColumns('整治完成时限')" />
        <el-table-column label="省级责任单位及人员" align="center" prop="provincialResponsible" show-overflow-tooltip
          v-if="cmpColumns('省级责任单位及人员')" />
        <el-table-column label="复核责任单位及人员" align="center" prop="reviewResponsible" show-overflow-tooltip
          v-if="cmpColumns('复核责任单位及人员')" />
        <el-table-column label="排查责任单位及人员" align="center" prop="inspectionResponsible" show-overflow-tooltip
          v-if="cmpColumns('排查责任单位及人员')" />
        <el-table-column label="是否属于5类自然灾害" align="center" prop="isNaturalDisaster" v-if="cmpColumns('是否属于5类自然灾害')" />
        <!-- <el-table-column label="备注" align="center" prop="remarks" /> -->
<!--        <el-table-column label="操作" fixed="right" align="center" width="160" class-name="small-padding fixed-width">-->
<!--          <template slot-scope="scope" v-if="scope.row.userId !== 1">-->
<!--            <el-button size="mini" type="text" icon="el-icon-edit" @click="handleUpdate(scope.row)"-->
<!--              v-hasPermi="['middleData:risk:edit']">修改</el-button>-->
<!--            <el-button size="mini" type="text" icon="el-icon-delete" @click="handleDelete(scope.row)"-->
<!--              v-hasPermi="['middleData:risk:remove']">删除</el-button>-->
<!--          </template>-->
<!--        </el-table-column>-->
      </el-table>
      <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize"
        @pagination="getList" />
      <!--数据表格结束-->
    </template>
    <!-- 添加或修改公路灾害风险信息对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="70%" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="160px">
        <div class="infoBox" style="margin-bottom: 20px;">
          <div class="infoTitle">
            基础信息
          </div>
          <el-row>
            <el-col :span="12">
              <el-form-item label="省份名称" prop="provinceName">
                <el-input v-model="form.provinceName" placeholder="请输入省份名称" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="市/州名称" prop="cityName">
                <el-select v-model="form.cityName" placeholder="请选择市/州" style="width: 100%;">
                  <el-option v-for="item in cityList" :key="item" :label="item" :value="item">
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="12">
              <el-form-item label="管理处" prop="managementOfficeId">
                <selectTree :key="'field2'" v-model="form.managementOfficeId" :deptType="100" :deptTypeList="[1, 3, 4]"
                  placeholder="请选择管理处" clearable filterable @node-selected="handleNodeSelected" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="养护路段" prop="maintenanceSectionId">
                <RoadSection v-model="form.maintenanceSectionId" :deptId="form.managementOfficeId"
                  @change="maintenanceSectionIdChange" placeholder="请选择养护路段" style="width: 100%;" />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="12">
              <el-form-item label="公路类型" prop="roadType">
                <!-- <el-select v-model="form.roadType" placeholder="请选择公路类型" style="width: 100%;">
                  <el-option v-for="dict in dict.type.sys_road_type" :key="dict.value" :label="dict.label"
                    :value="parseInt(dict.value)"></el-option>
                </el-select> -->
                <el-radio-group v-model="form.roadType">
                  <el-radio v-for="dict in dict.type.sys_road_type" :label="parseInt(dict.value)">{{
                    dict.label }}</el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="公路编号" prop="roadNumber">
                <el-select v-model="form.roadNumber" placeholder="请选择公路编号" clearable filterable style="width: 100%"
                  collapse-tags @change="routeCodeChange">
                  <el-option v-for="item in routeList" :key="item.routeId" :label="item.routeName"
                    :value="item.routeCode">
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="12">
              <el-tooltip effect="dark" content="起点桩号(KXXX+XXX格式)" placement="top">
                <el-form-item label="起点桩号" prop="startStake">
                  <div class="input-row">
                    <span class="dwClass">K</span>
                    <el-input type="number" :min="0" v-model="form.beginMile1" class="input-box" />
                    <span class="dwClass">-</span>
                    <el-input type="number" :min="0" v-model="form.beginMile2" class="input-box" />
                  </div>
                </el-form-item>
              </el-tooltip>
            </el-col>
            <el-col :span="12">
              <el-tooltip effect="dark" content="终点桩号(KXXX+XXX格式)" placement="top">
                <el-form-item label="终点桩号" prop="endStake">
                  <div class="input-row">
                    <span class="dwClass">K</span>
                    <el-input type="number" :min="0" v-model="form.endMile1" class="input-box" />
                    <span class="dwClass">-</span>
                    <el-input type="number" :min="0" v-model="form.endMile2" class="input-box" />
                  </div>
                </el-form-item>
              </el-tooltip>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="12">
              <el-tooltip effect="dark" content="中心桩号/位置桩号(KXXX+XXX格式)" placement="top">
                <el-form-item label="中心桩号/位置桩号" prop="centerStake">
                  <div class="input-row">
                    <span class="dwClass">K</span>
                    <el-input type="number" :min="0" v-model="form.centerStakeBeginMile1" />
                    <span class="dwClass">-</span>
                    <el-input type="number" :min="0" v-model="form.centerStakeBeginMile2" />
                  </div>
                </el-form-item>
              </el-tooltip>
            </el-col>
            <el-col :span="12">
              <el-form-item label="结构物名称" prop="assetName">
                <el-input v-model="form.assetName" placeholder="请输入结构物名称" />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="12">
              <el-form-item label="结构物类型" prop="assetType">
                <el-select v-model="form.assetType" placeholder="请选择结构物类型" style="width: 100%;">
                  <el-option v-for="dict in dict.type.sys_hazard_asset_type" :key="dict.value" :label="dict.label"
                    :value="parseInt(dict.value)"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
        </div>
        <div class="infoBox" style="margin-bottom: 20px;">
          <div class="infoTitle">
            风险评估
          </div>
          <el-row>
            <el-col :span="12">
              <el-form-item label="灾害风险隐患部位" prop="hazardLocation">
                <el-input v-model="form.hazardLocation" placeholder="请输入灾害风险隐患部位" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="隐患等级" prop="hazardLevel">
                <el-select v-model="form.hazardLevel" placeholder="请选择隐患等级" style="width: 100%;">
                  <el-option v-for="dict in dict.type.sys_hazard_level" :key="dict.value" :label="dict.label"
                    :value="parseInt(dict.value)"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="12">
              <el-form-item label="灾害风险隐患类型" prop="hazardType">
                <el-select v-model="form.hazardType" filterable placeholder="请选择灾害风险隐患类型" style="width: 100%">
                  <el-option label="滑坡" value="滑坡"></el-option>
                  <el-option label="崩塌" value="崩塌"></el-option>
                  <el-option label="泥石流" value="泥石流"></el-option>
                  <el-option label="沉陷与塌陷" value="沉陷与塌陷"></el-option>
                  <el-option label="水毁" value="水毁"></el-option>
                  <el-option label="其它" value="其它"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="灾害风险隐患描述" prop="hazardDescription">
                <el-input v-model="form.hazardDescription" type="textarea" placeholder="请输入内容" />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="12">
              <el-form-item label="是否属于5类自然灾害" prop="isNaturalDisaster">
                <!-- <el-select v-model="form.isNaturalDisaster" placeholder="请选择是否属于5类自然灾害" style="width: 100%;">
                  <el-option label="是" value="是"></el-option>
                  <el-option label="否" value="否"></el-option>
                </el-select> -->
                <el-radio-group v-model="form.isNaturalDisaster">
                  <el-radio label="是">{{ '是' }}</el-radio>
                  <el-radio label="否">{{ '否' }}</el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="是否完成整治" prop="rectificationCompleted">
                <!-- <el-select v-model="form.rectificationCompleted" placeholder="请选择是否完成整治" style="width: 100%;">
                  <el-option label="是" value="是"></el-option>
                  <el-option label="否" value="否"></el-option>
                </el-select> -->
                <el-radio-group v-model="form.rectificationCompleted">
                  <el-radio label="是">{{ '是' }}</el-radio>
                  <el-radio label="否">{{ '否' }}</el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="12">
              <el-form-item label="整治完成时限" prop="completionDeadline">
                <el-date-picker clearable v-model="form.completionDeadline" type="date" value-format="yyyy-MM-dd"
                  format="yyyy-MM-dd" placeholder="请选择整治完成时限" style="width: 100%;">
                </el-date-picker>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="已(拟)采取的措施" prop="measuresDetail">
                <el-input v-model="form.measuresDetail" type="textarea" placeholder="请输入内容" />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="12">
              <el-form-item label="省级责任单位及人员" prop="provincialResponsible">
                <el-input v-model="form.provincialResponsible" placeholder="请输入省级责任单位及人员" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="复核责任单位及人员" prop="reviewResponsible">
                <el-input v-model="form.reviewResponsible" placeholder="请输入复核责任单位及人员" />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="12">
              <el-form-item label="排查责任单位及人员" prop="inspectionResponsible">
                <el-input v-model="form.inspectionResponsible" placeholder="请输入排查责任单位及人员" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="备注" prop="remarks">
                <el-input v-model="form.remarks" type="textarea" placeholder="请输入内容" />
              </el-form-item>
            </el-col>
          </el-row>
        </div>
        <div class="infoBox" style="margin-bottom: 20px;" v-if="this.form.id">
          <div class="infoTitle">
            照片预览
          </div>
          <el-row :gutter="12">
            <el-col :span="24">
              <el-form-item label-width="0" prop="riskDescription">
                <div class="imgBox" v-if="formImgSrcList.length > 0">
                  <el-timeline>
                    <el-timeline-item v-for="item in formDataLine" :key="item.time" :timestamp="item.time"
                      placement="top" color="#5cbb7a">
                      <el-card class="imgBoxCard">
                        <div class="cardMain" v-for="itemC in item.data">
                          <el-button class="imgDeleteBtn" type="danger" icon="el-icon-delete" circle
                            @click="formDeleteImg(itemC.id)"></el-button>
                          <div class="imgTitle">
                            <el-tooltip class="item" effect="dark" :content="itemC.name" placement="top">
                              <i class="el-icon-info"></i>
                            </el-tooltip>
                            {{ itemC.name }}
                          </div>
                          <el-image fit="cover" class="img" :src="itemC.url" @click="formImgPreview(itemC.imgUrl)"
                            :preview-src-list="formImgUrlList"></el-image>
                          <div class="footer">
                            {{ `由 ${itemC.createBy} 上传于 ${itemC.createTime}` }}
                          </div>
                          <div class="footer">
                            {{ itemC.remark ? `图片描述：${itemC.remark}` : '' }}
                          </div>
                        </div>
                      </el-card>
                    </el-timeline-item>
                  </el-timeline>
                </div>
                <div class="noneBox" v-else>
                  暂无内容
                </div>
              </el-form-item>
            </el-col>
          </el-row>
        </div>
        <div class="infoBox" style="padding: 15px;">
          <div class="infoTitle">
            <!-- <el-tooltip content="上传填报高边坡的全貌照片及局部照片（防护设施、排水设施），不少于 3 张" placement="top">
              <i class="el-icon-question"></i>
            </el-tooltip> -->
            上传照片
          </div>
          <ImageUpload v-model="formUploadList" :ownerId="formImgOwnerId" :needRemark="true" :can-sort="true"
            storage-path="/middleData/risk/" platform="fykj" @input="queryImg(formImgOwnerId)" ref="refImageUpload"
            :fileType="['png', 'jpg', 'jpeg', 'PNG', 'JPG', 'JPEG']" />
        </div>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 用户导入对话框 -->
    <el-dialog :title="upload.title" :visible.sync="upload.open" width="400px" append-to-body>
      <el-upload ref="upload" :limit="1" accept=".xlsx, .xls" :headers="upload.headers"
        :action="upload.url + '?updateSupport=' + upload.updateSupport" :disabled="upload.isUploading"
        :on-progress="handleFileUploadProgress" :on-success="handleFileSuccess" :auto-upload="false" drag>
        <i class="el-icon-upload"></i>
        <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
        <div class="el-upload__tip text-center" slot="tip">
          <div class="el-upload__tip" slot="tip">
            <el-checkbox v-model="upload.updateSupport" /> 是否更新已经存在的用户数据
          </div>
          <span>仅允许导入xls、xlsx格式文件。</span>
          <el-link type="primary" :underline="false" style="font-size:12px;vertical-align: baseline;"
            @click="importTemplate">下载模板</el-link>
        </div>
      </el-upload>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitFileForm">确 定</el-button>
        <el-button @click="upload.open = false">取 消</el-button>
      </div>
    </el-dialog>
  </PageContainer>
</template>

<script>
import { listRisk, getRisk, delRisk, delRiskBatch, addRisk, updateRisk } from "@/api/middleData/risk";
import { getToken } from "@/utils/auth";
import Treeselect from "@riophae/vue-treeselect";
import "@riophae/vue-treeselect/dist/vue-treeselect.css";
import selectTree from "@/components/DeptTmpl/selectTree.vue";
import RoadSection from "./components/index.vue"
import CascadeSelection from '@/components/CascadeSelectionManagementOffice/index.vue'; // 管养处/路段/路线
import RangeInput from "@/views/baseData/components/rangeInput/index.vue";

import { removeFile } from '@/api/system/fileUpload.js'
import { findFiles } from '@/api/file/index.js'
import ImageUpload from '@/views/disaster/ImageUpload.vue' // 图片上传组件

import { listByMaintenanceSectionId } from '@/api/baseData/common/routeLine'
import { listAllRoute } from '@/api/system/route'
export default {
  name: "Risk",
  dicts: ['sys_road_type', 'sys_hazard_asset_type', 'sys_hazard_level'],
  components: {
    selectTree,
    RoadSection,
    ImageUpload,
    CascadeSelection,
    RangeInput,
  },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: false,
      dictType: [],
      // 日期范围
      dateRange: [],
      // 总条数
      total: 0,
      // 公路灾害风险信息表格数据
      riskList: null,
      // 弹出层标题
      title: "",
      // 部门树选项
      deptOptions: undefined,
      // 是否显示弹出层
      open: false,

      // 表单参数
      form: {
        provinceName: '云南省'
      },
      defaultProps: {
        children: "children",
        label: "label"
      },
      // 用户导入参数
      upload: {
        // 是否显示弹出层（用户导入）
        open: false,
        // 弹出层标题（用户导入）
        title: "",
        // 是否禁用上传
        isUploading: false,
        // 是否更新已经存在的用户数据
        updateSupport: 0,
        // 设置上传的请求头部
        headers: { Authorization: "Bearer " + getToken() },
        // 上传的地址
        url: process.env.VUE_APP_BASE_API + "/system/user/importData"
      },
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 50,
        provinceName: null,
        cityName: null,
        managementOffice: null,
        managementOfficeId: null,
        maintenanceSectionId: null,
        maintenanceSection: null,
        roadType: null,
        roadNumber: null,
        centerStake: null,
        startStake: null,
        endStake: null,
        hazardType: null,
        assetName: null,
        assetType: null,
        hazardLocation: null,
        hazardLevel: null,
        hazardDescription: null,
        photo: null,
        rectificationCompleted: null,
        measuresDetail: null,
        completionDeadline: null,
        provincialResponsible: null,
        reviewResponsible: null,
        inspectionResponsible: null,
        isNaturalDisaster: null,
        riskDatabaseId: null,
        remarks: null,
        shape: null
      },
      // 列信息
      columns: [
        { key: 0, label: `省份`, visible: true },
        { key: 1, label: `市/州`, visible: true },
        { key: 2, label: `管理处`, visible: true },
        { key: 3, label: `养护路段`, visible: true },
        { key: 4, label: `公路类型`, visible: true },
        { key: 5, label: `公路编号`, visible: true },
        { key: 6, label: `中心桩号/位置桩号`, visible: true },
        { key: 7, label: `起点桩号`, visible: true },
        { key: 8, label: `止点桩号`, visible: true },
        { key: 9, label: `灾害风险隐患类型`, visible: true },
        { key: 10, label: `结构物名称`, visible: true },
        { key: 11, label: `结构物类型`, visible: true },
        { key: 12, label: `灾害风险隐患部位`, visible: true },
        { key: 13, label: `隐患等级`, visible: true },
        { key: 14, label: `是否完成整治`, visible: true },
        { key: 15, label: `整治完成时限`, visible: true },
        { key: 16, label: `省级责任单位及人员`, visible: true },
        { key: 17, label: `复核责任单位及人员`, visible: true },
        { key: 18, label: `排查责任单位及人员`, visible: true },
        { key: 19, label: `是否属于5类自然灾害`, visible: true },
      ],
      // 表单校验
      rules: {


      },
      cityList: [
        '昆明市', '曲靖市', '玉溪市', '保山市', '昭通市', '丽江市', '普洱市', '临沧市',
        '楚雄彝族自治州', '红河哈尼族彝族自治州', '文山壮族苗族自治州',
        '西双版纳傣族自治州', '大理白族自治州', '德宏傣族景颇族自治州',
        '怒江傈僳族自治州', '迪庆藏族自治州'
      ],
      routeList: [],
      // 图片上传相关
      formImgSrcList: [], // 图片渲染列表
      formImgUrlList: [], // 图片预览列表
      formUploadList: '', // 图片上传列表
      formImgOwnerId: '', // 图片上传ownerId
      formImgNum: [], // 图片数量（图片集用）
    };
  },
  watch: {
    // 根据名称筛选部门树
  },
  created() {
    this.getList();
    // this.getDeptTree();
    // this.getConfigKey("sys.user.initPassword").then(response => {
    //   this.initPassword = response.msg;
    // });
  },
  computed: {
    cmpColumns() {
      return (val) => {
        let el = this.columns.find(item => item.label === val)
        if (el) {
          return el.visible
        } else {
          return true
        }
      }
    }
  },
  methods: {
    /** 查询用户列表 */
    getList() {
      this.loading = true;
      // this.queryParams.createTimee = this.dateRange[0]
      // this.queryParams.createTimes = this.dateRange[1]
      listRisk(this.queryParams).then(response => {
        this.riskList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    restImg() {
      // 重置图片
      this.formDataLine = []
      this.formImgSrcList = [] // 图片缩略图列表
      this.formImgUrlList = [] // 图片预览图列表
      this.formUploadList = '' // 图片上传列表
    },
    // 表单重置
    reset() {
      this.form = {
        provinceName: '云南省',
        cityName: null,
        managementOffice: null,
        managementOfficeId: null,
        maintenanceSectionId: null,
        maintenanceSection: null,
        roadType: null,
        roadNumber: null,
        centerStake: null,
        startStake: null,
        endStake: null,
        hazardType: null,
        assetName: null,
        assetType: null,
        hazardLocation: null,
        hazardLevel: null,
        hazardDescription: null,
        photo: null,
        rectificationCompleted: null,
        measuresDetail: null,
        completionDeadline: null,
        provincialResponsible: null,
        reviewResponsible: null,
        inspectionResponsible: null,
        isNaturalDisaster: null,
        riskDatabaseId: null,
        remarks: null,
        shape: null
      };
      this.resetForm("form");
      this.restImg();
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dateRange = [];
      this.resetForm("queryForm");
      this.queryParams.managementMaintenanceBranchId = null
      this.queryParams.managementMaintenanceBranchIds = null
      this.queryParams.managementMaintenanceIds = null
      this.queryParams.routeCodes = null
      this.queryParams.maintenanceSectionId = null
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id);
      this.single = selection.length != 1;
      this.multiple = !selection.length;
    },
    // 表格点击勾选
    handleRowClick(row) {
      row.isSelected = !row.isSelected;
      this.$refs.table.toggleRowSelection(row);
    },
    // 勾选高亮
    rowStyle({ row, rowIndex }) {
      if (this.ids.includes(row.id)) {
        return { 'background-color': '#E1F0FF', color: '#333' }
      } else {
        return { 'background-color': '#fff', color: '#333' }
      }
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加公路灾害风险信息";
      this.formImgOwnerId = new Date().getTime().toString()
      this.form.photo = this.formImgOwnerId
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids;
      getRisk(id).then(async (response) => {
        this.form = response.data;
        this.open = true;
        this.title = "修改公路灾害风险信息";

        /* 中心桩号回显相关 */
        const centerStakeBeginMile1 = Math.floor(Number(this.form.centerStake) / 1000);
        const centerStakeBeginMile2 = Number(this.form.centerStake) % 1000;
        this.$set(this.form, 'centerStakeBeginMile1', centerStakeBeginMile1);
        this.$set(this.form, 'centerStakeBeginMile2', centerStakeBeginMile2);

        /* 起始桩号回显相关 */
        const beginMile1 = Math.floor(Number(this.form.startStake) / 1000);
        const beginMile2 = Number(this.form.startStake) % 1000;
        this.$set(this.form, 'beginMile1', beginMile1);
        this.$set(this.form, 'beginMile2', beginMile2);

        const endMile1 = Math.floor(Number(this.form.endStake) / 1000);
        const endMile2 = Number(this.form.endStake) % 1000;
        this.$set(this.form, 'endMile1', endMile1);
        this.$set(this.form, 'endMile2', endMile2);

        /* 获取图片回显 */
        if (this.form.photo) {
          this.formImgOwnerId = this.form.photo
          await this.queryImg(this.formImgOwnerId)
        } else {
          this.formImgOwnerId = new Date().getTime().toString()
          this.form.photo = this.formImgOwnerId
        }
        this.formUploadList = this.form.photo
      });

    },
    /** 提交按钮 */
    submitForm: function () {
      /* 中心桩号上传相关 */
      this.form.centerStake = Number(this.form.centerStakeBeginMile1) * 1000 + Number(this.form.centerStakeBeginMile2) *
        1;
      /* 起始桩号上传相关 */
      this.form.startStake = Number(this.form.beginMile1) * 1000 + Number(this.form.beginMile2) *
        1; // 起始里程
      this.form.endStake = Number(this.form.endMile1) * 1000 + Number(this.form.endMile2) *
        1; // 结束里程
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.id != null) {
            updateRisk(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addRisk(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const id = row.id ? [row.id] : this.ids;
      this.$modal.confirm('是否确认删除公路灾害风险信息的数据项？').then(function () {
        return delRiskBatch(id);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => { });
    },

    /** 导出按钮操作 */
    handleExport() {

      this.download('middleData/roadHazardRisk/export', {
        ...this.queryParams
      }, `risk_${new Date().getTime()}.xlsx`)

    },
    /** 导入按钮操作 */
    handleImport() {
      this.upload.title = "用户导入";
      this.upload.open = true;
    },
    /** 下载模板操作 */
    importTemplate() {
      this.download('system/user/importTemplate', {
      }, `user_template.xlsx`)
    },
    // 文件上传中处理
    handleFileUploadProgress(event, file, fileList) {
      this.upload.isUploading = true;
    },
    // 文件上传成功处理
    handleFileSuccess(response, file, fileList) {
      this.upload.open = false;
      this.upload.isUploading = false;
      this.$refs.upload.clearFiles();
      this.$alert("<div style='overflow: auto;overflow-x: hidden;max-height: 70vh;padding: 10px 20px 0;'>" + response.msg + "</div>", "导入结果", { dangerouslyUseHTMLString: true });
      this.getList();
    },
    // 提交上传文件
    submitFileForm() {
      this.$refs.upload.submit();
    },
    /* 公路编号相关 */
    routeCodeChange(val) {
      if (val) {
        let el = this.routeList.find(item => val == item.routeCode)
        this.form.routeName = el?.routeName
      } else {
        this.form.routeName = null
      }
    },
    async maintenanceSectionIdChange(val) {
      if (this.form.maintenanceSectionId) {
        await this.managementChange(this.form.maintenanceSectionId)
      } else {
        this.listAllRoute()
      }
      this.form.maintenanceSection = val?.maintenanceSectionName
    },
    listAllRoute() {
      listAllRoute().then((res) => {
        if (res.code == 200) {
          this.routeList = res.data || [];
        }
      })
    },
    //路段下拉选改变事件
    managementChange(routeSectionId) {
      return new Promise((resolve, reject) => {
        this.queryParams.roadNumber = null;
        this.form.roadNumber = null;
        listByMaintenanceSectionId({ maintenanceSectionId: routeSectionId }).then((res) => {
          if (res.code == 200) {
            res.data.forEach((item) => {
              item.routeName = item.routeName + '(' + item.routeCode + ')';
            });
            this.routeList = res.data || [];
            resolve(this.routeList)
          } else {
            reject(res)
          }
        });
      })
    },
    /** 图片预览 */
    previewImage(url) {
      this.imagePreview.url = url;
      this.imagePreview.visible = true;
    },
    // 获取图片数据
    async queryImg(id) {
      await findFiles({ ownerId: id }).then(res => {
        if (res.code === 200) {
          this.formImgSrcList = res.data.map(item => ({
            id: item.ownerId + '-' + item.id,
            name: item.originalFilename,
            url: item.thumbUrl,
            imgUrl: item.url,
            remark: item.remark,
            createTime: item.createTime,
            createBy: item.createBy
          }))
          this.formImgUrlList = res.data.map(item => item.url)
          this.formDataLine = this.formImgDateLine()
        }
      }).catch(() => {
        this.$message.error('图片查询失败，请重新打开表单尝试')
      })
    },
    formImgDateLine() {
      let dateLine = []
      if (this.formImgSrcList.length > 0) {
        this.formImgSrcList.forEach(item => {
          let date = item.createTime.split('T')[0]
          if (dateLine.length === 0) {
            dateLine.push({
              time: date,
              data: [item]
            })
          } else {
            let index = dateLine.findIndex(item2 => item2.time === date)
            if (index !== -1) {
              dateLine[index].data.push(item)
            } else {
              dateLine.push({
                time: date,
                data: [item]
              })
            }
          }
        })
        // 时间线排序
        dateLine.sort((a, b) => {
          // 将日期字符串分割并转换为日期对象
          let dateA = new Date(a.time);
          let dateB = new Date(b.time);
          // 比较日期
          return dateA - dateB;
        })
      }
      return dateLine
    },
    // 删除图片
    formDeleteImg(id) {
      this.$modal.confirm('是否确认删除该图片？').then(async () => {
        this.$modal.loading('正在删除图片，请稍候...')
        removeFile(id.split('-')[1]).then(async (res) => {
          if (res.code === 200) {
            this.$message.success('删除图片成功！')
            /* // 移除预览列表图片
            let index = this.formImgSrcList.findIndex(item => item.id === id)
            if (index !== -1) {
              let imgUrl = this.formImgSrcList[index].imgUrl
              let imgIndex = this.formImgUrlList.indexOf(imgUrl)
              if (imgIndex !== -1) {
                this.formImgUrlList.splice(imgIndex, 1)
              }
            }
            // 移除渲染列表图片
            this.formImgSrcList = this.formImgSrcList.filter(item => item.id !== id) */
            await this.queryImg(id.split('-')[0])
            this.$refs.refImageUpload.getImg(this.formUploadList)
          }
          this.$modal.closeLoading()
        }).catch(() => {
          this.$message.error('删除图片失败')
          this.$modal.closeLoading()
        })
      })
    },
    // 点击预览图片时
    formImgPreview(url) {
      let index = this.formImgUrlList.findIndex(item => item === url)
      if (index !== -1) {
        let moveUrl = this.formImgUrlList.splice(index, this.formImgUrlList.length - index)
        this.formImgUrlList.unshift(...moveUrl)
      }
    },
    handleNodeSelected(node) {
      this.form.managementOffice = node.label; // 获取对应的 name
    },
  }
};
</script>
<style>
.hasTagsView .app-main[data-v-078753dd] {
  background: #f5f7fa;
}

.tableDiv {
  background-color: white;
  padding-bottom: 10px;
}

.infoBox {
  padding: 15px 15px 0 15px;
  box-sizing: border-box;
  border-radius: 6px;
  border: 1px solid #C4C4C4;
  position: relative;

  .infoTitle {
    user-select: none;
    position: absolute;
    top: 0;
    left: 0;
    padding: 0 10px;
    font-size: 14px;
    line-height: 14px;
    font-weight: bold;
    transform: translateX(15px) translateY(-50%);
    background-color: white;
  }

  .imgBox {
    height: auto;
    width: 100%;

    ::v-deep .el-card__body {
      width: 100%;
      display: flex;
      flex-direction: row;
      flex-wrap: wrap;
      align-content: flex-start;
    }

    .imgBoxCard {
      width: 100%;

      .cardMain {
        height: 260px;
        width: 33%;
        box-sizing: border-box;
        padding: 0 10px;
        display: flex;
        flex-direction: column;
        position: relative;

        .imgDeleteBtn {
          position: absolute;
          z-index: 1;
          top: 20%;
          right: 5%;
        }

        .imgTitle {
          height: 28px;
          width: 100%;
          font-size: 16px;
          font-weight: bold;
          white-space: nowrap;
          text-overflow: ellipsis;
          overflow: hidden;
        }

        .img {
          height: calc(100% - (28px + 28px));
          width: 100%;
          padding: 10px 0;
          position: relative;
          z-index: 0;
        }

        .footer {
          height: 28px;
          color: #888888;
          font-size: 14px;
        }
      }
    }
  }

  .noneBox {
    user-select: none;
    height: 200px;
    width: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    color: #888888;
  }
}

.dwClass {
  font-size: 12px;
  line-height: 3;
  color: #007aff;
  margin-left: 5px;
}

.input-row {
  display: flex;
  align-items: center;
}
</style>
