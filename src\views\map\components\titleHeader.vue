<template>
  <div class="title-header">
    <span class="title-text">{{ title }}</span>
    <div class="slot">
      <slot />
    </div>
  </div>
</template>

<script>
export default {
  name: "TitleHeader",
  props: {
    title: {
      type: String,
      default: '标题内容'
    }
  },
  data() {
    return {
      // 在此添加数据属性
    };
  },
  methods: {
    // 在此添加方法
  },
  mounted() {
    // 组件挂载后逻辑
  }
};
</script>

<style lang="scss" scoped>
.title-header {
  width: 100%;
  background-image: url('~@/assets/map/title-bg-2.png');
  background-repeat: no-repeat;
  background-size: 100% 100%;
  padding: 0.3vh 1.5vh;

  display: flex;
  align-items: center;

  .title-text {
    font-size: 1.7vh;
    color: #ffffff;
    font-family: YouSheBiaoTiHei, YouSheBiaoTiHei;
    font-weight: 400;
    text-shadow: 0px 0px 16px #0088FF;
    text-align: left;
    font-style: normal;
    text-transform: none;
  }

  .slot {
    margin-left: 3vh;
    line-height: 3vh;
  }
}
</style>
