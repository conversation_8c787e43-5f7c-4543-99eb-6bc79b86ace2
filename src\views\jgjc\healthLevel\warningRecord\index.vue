<template>
  <div class="app-container">
    <el-row :gutter="20">
      <!-- 筛选区开始 -->
      <el-col :span="24" :xs="24">
        <el-row>
          <el-tabs v-model='isDeal' @tab-click="handleQuery">
            <el-tab-pane label="未处理" name="0">
            </el-tab-pane>
            <el-tab-pane label="已处理" name="1">
            </el-tab-pane>
          </el-tabs>
          <el-col :span="24">
            <el-form ref="queryForm" :inline="true" :model="queryParams" label-width="68px" size="small">
              <el-form-item label="" prop="alertLevel">
                <el-select v-model="queryParams.alertLevel" placeholder="请选择预警等级">
                  <el-option v-for="item in alertLevelList" :key="item.value" :label="item.label" :value="item.value">
                  </el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="" prop="alertLevel">
                <el-input v-model='queryParams.structureName' placeholder="请输入结构物名称"></el-input>
              </el-form-item>
              <el-form-item>
                <el-button icon="el-icon-search" size="mini" type="primary" @click="handleQuery">搜索</el-button>
                <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
                <el-button icon="el-icon-delete" size="mini" type="danger" @click="handelDelete">删除</el-button>
              </el-form-item>
            </el-form>
          </el-col>
        </el-row>
        <!-- 筛选区结束 -->
        <!-- 数据表格开始 -->
        <div class="tableDiv">
          <el-table v-loading="loading" :data="dataList"
                    :height="pageType == 'edit' ? 'calc(100vh - 260px)' : 'calc(70vh - 260px)'" border size="mini"
                    style="width: 100%" @selection-change="handleSelectionChange">
            <el-table-column
              type="selection"
              width="50">
            </el-table-column>
            <el-table-column align="center" fixed label="序号" type="index" width="100"></el-table-column>
            <template v-for="(column, index) in columns">
              <el-table-column v-if="column.visible" :key="index" :label="column.label" :prop="column.field"
                               align="center" show-overflow-tooltip>
                <template slot-scope="scope">
                  <span>{{ scope.row[column.field] }}</span>
                </template>
              </el-table-column>
            </template>
            <el-table-column align="center" class-name="small-padding fixed-width" fixed="right" label="操作" width="200">
              <template slot-scope="scope">
                <el-button size="mini" v-if="!isViewAll" type="text" @click="handleViewAll(scope.row)">查看所有</el-button>
                <el-button size="mini" v-else type="text" @click="handleQuery(scope.row)">展开筛选</el-button>
                <el-button size="mini" type="text" @click="handleOpenProcess(scope.row)">{{ isDeal == 0 ? '处理' : '查看' }}</el-button>
              </template>
            </el-table-column>
          </el-table>
          <pagination
            v-show="total > 0"
            :limit.sync="queryParams.pageSize"
            :page.sync="queryParams.pageNumber"
            :total="total"
            @pagination="handleQuery"
          />
        </div>
        <!-- 数据表格结束 -->
      </el-col>
    </el-row>
    <el-dialog :title="'预警处理记录'" :visible.sync="dialogVisible" width="70%" @close="dialogVisible = false; fileList = []">
      <div v-loading="loading">
        <el-descriptions border size="medium">
          <el-descriptions-item label="结构物名称">{{ checkRow.structureName }}</el-descriptions-item>
          <el-descriptions-item label="传感器名称">{{ checkRow.sensorCode }}</el-descriptions-item>
          <el-descriptions-item label="监测值名称">{{ checkRow.monitorCode }}</el-descriptions-item>
          <el-descriptions-item label="预警类型">{{ checkRow.alertType }}</el-descriptions-item>
          <el-descriptions-item label="预警级别">{{ checkRow.alertLevel }}</el-descriptions-item>
          <el-descriptions-item label="报警值">{{ checkRow.sensorValue }}</el-descriptions-item>
          <el-descriptions-item label="阈值上限">{{ checkRow.threshold }}</el-descriptions-item>
          <el-descriptions-item label="阈值下限"></el-descriptions-item>
          <el-descriptions-item label=""></el-descriptions-item>
          <el-descriptions-item label="预警内容">{{ checkRow.alertContent }}</el-descriptions-item>

        </el-descriptions>
        <el-form :model="formData" ref="elForm" :rules="rules" label-width="86px" class="mt20">
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="处理结果" prop="dealResult">
                <el-select v-model="formData.dealResult" placeholder="请选择处理结果" style="width: 100%">
                  <el-option label="正常" value="正常"></el-option>
                  <el-option label="异常" value="异常"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12" v-if="formData.dealResult == '异常' && flashModes.length > 0">
              <el-form-item label="预案模式" prop="flashMode">
                <el-select v-model="formData.flashMode" placeholder="请选择预案模式" style="width: 100%">
                  <el-option v-for="item in flashModes" :key="item.value" :label="item.label"
                             :value="item.value"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="处理内容" prop="dealContent">
                <el-input v-model="formData.dealContent" type="textarea" :rows="2" style="width: 100%" placeholder="请输入处理内容" clearable></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <div style="text-align: right">
            <el-button type="primary" @click="handleProcess" v-if='isDeal == 0'>提交</el-button>
          </div>
        </el-form>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import RoadSection from "@/views/baseData/components/roadSection/index.vue";
import SelectTree from "@/components/DeptTmpl/selectTree.vue";
import {getAlertRecordPage, getAlertRecordStructDetail, getLatestProcessedByPage, deleteAlertRecord, updateAlertRecord} from "@/api/jgjc/healthLevel/warningRecord";

import {
  getCommonConfigPage,
  getSsConfigPage,
  getWdmConfigPage,
  getZjgtConfigPage,
} from '@/api/jgjc/flashingModeConfig'
import {sansiModes, wdmModes} from "@/views/jgjc/earlyWarning/defaultModes";

export default {
  name: "WarningRecord",
  components: {SelectTree, RoadSection},
  props: {
    pageType: {
      type: String,
      default: 'edit'
    },
    maiSecId: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      loading: false,
      showSearch: false,
      total: 0,
      dataList: [],
      fullDataList: [],
      alertLevelList: [
        {label: '一级', value: '一级'},
        {label: '二级', value: '二级'},
        {label: '三级', value: '三级'},
      ],
      // 查询参数
      queryParams: {
        pageNumber: 1,
        pageSize: 50,
      },
      dialogVisible: false,
      checkRow: {},
      formData: {
        planName: '一级预警'
      },
      // 列信息
      columns: [
        {key: 0, field: 'structureName', label: '结构物名称', visible: true},
        {key: 1, field: 'sensorCode', label: '传感器名称', visible: true},
        {key: 2, field: 'monitorCode', label: '监测值名称', visible: true},
        {key: 0, field: 'flashType', label: '厂商', visible: true},
        {key: 3, field: 'alertType', label: '预警类型', visible: true},
        {key: 4, field: 'alertLevel', label: '预警级别', visible: true},
        {key: 5, field: 'alertContent', label: '预警内容', visible: true},
        {key: 6, field: 'sensorValue', label: '报警值', visible: true},
        {key: 7, field: 'threshold', label: '阈值线', visible: true},
        {key: 8, field: 'sensorValueTime', label: '预警时间', visible: true}
      ],
      flashModes: [],
      rules: {
        dealResult: [
          {required: true, message: '请选择处理结果', trigger: 'blur'}
        ],
        dealContent: [
          {required: true, message: '请输入处理内容', trigger: 'blur'}
        ],
      },
      isDeal: 0,
      selectDatas: [],
      isViewAll: false
    };
  },
  created() {
    this.handleQuery();
  },
  methods: {
    handleQuery() {
      this.loading = true
      this.queryParams.isDeal = this.isDeal
      getLatestProcessedByPage(this.queryParams).then(res => {
        this.dataList = res.rows
        this.total = res.total
        this.loading = false
        this.isViewAll = false
      })
    },
    resetQuery() {
      this.queryParams = {
        pageNumber: 1,
        pageSize: 50,
      };
      this.handleQuery();
    },
    handleViewAll(row) {
      this.loading = true
      this.queryParams.isDeal = this.isDeal

      getAlertRecordPage({
        ...this.queryParams,
        structureCode: row.structureCode,
        sensorCode: row.sensorCode,
        monitorCode: row.monitorCode
      }).then(res => {
        this.dataList = res.rows
        this.total = res.total
        this.loading = false
        this.isViewAll = true
      })
    },
    handelDelete() {
      if (this.selectDatas.length == 0) {
        this.$message.warning('请选择需要删除的数据');
        return
      }
      this.$confirm('是否确认删除选中的数据？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        const ids = this.selectDatas.map(item => item.id)
        deleteAlertRecord({
          ids: ids
        }).then(res => {
          this.$message.success('删除成功！');
          this.handleQuery()
        })
      })
    },
    handleOpenProcess(row) {
      const flashType = row.flashType
      this.flashModes = []
      if (flashType == '中交国通') {
        getZjgtConfigPage({
          pageNum: 1,
          pageSize: 100,
        }).then(res => {
          for (let i = 0; i < res.rows.length; i++) {
            const item = res.rows[i]
            const temp = {}
            temp.zjgtConfigEntity = item
            temp.isCustom = item.isCustom
            temp.label = item.modeName
            temp.value = item.id
            this.flashModes.push(temp)
          }
        })
      }
      if (flashType == '三思') {
        getSsConfigPage({
          pageNum: 1,
          pageSize: 100,
        }).then(res => {
          for (let i = 0; i < res.rows.length; i++) {
            const item = res.rows[i]
            const temp = {}
            temp.sansiConfigEntity = item
            temp.isCustom = item.isCustom
            temp.label = item.name
            temp.value = item.id
            this.flashModes.push(temp)
          }
        })
      }
      if (flashType == '维的美') {
        getWdmConfigPage({
          pageNum: 1,
          pageSize: 100,
        }).then(res => {
          for (let i = 0; i < res.rows.length; i++) {
            const item = res.rows[i]
            const temp = {}
            temp.wdmConfigEntity = item
            temp.isCustom = item.isCustom
            temp.label = item.modeName
            temp.value = item.id
            this.flashModes.push(temp)
          }
        })
      }
      if (flashType == '第三方') {
        getCommonConfigPage({
          pageNum: 1,
          pageSize: 100,
        }).then(res => {
          for (let i = 0; i < res.rows.length; i++) {
            const item = res.rows[i]
            const temp = {}
            temp.flashCommonModeConfig = item
            temp.isCustom = item.isCustom
            temp.label = item.modeName
            temp.value = item.id
            this.flashModes.push(temp)
          }
        })
      }
      this.checkRow = row
      this.dialogVisible = true
      this.formData = row
    },
    handleProcess() {
      this.$refs.elForm.validate(valid => {
        if (!valid) return
        this.loading = true
        const flashData = this.flashModes.find(item => item.value == this.formData.flashMode)
        let params = {
          ...this.formData,
          structureCode: this.checkRow.structureCode,
          isCustom: flashData?.isCustom || '0',
          zjgtConfigEntity: flashData?.zjgtConfigEntity || {},
          sansiConfigEntity: flashData?.sansiConfigEntity || {},
          wdmConfigEntity: flashData?.wdmConfigEntity || {},
          flashCommonModeConfig: flashData?.flashCommonModeConfig || {},
        }
        updateAlertRecord(params).then(res => {
          this.loading = false
          this.$message.success('操作成功！');
          this.dialogVisible = false
          this.handleQuery()
        })
      })
    },
    // 选中
    handleSelectionChange(e) {
      this.selectDatas = e
    },
  }
};
</script>

<style scoped lang="scss">
.tableDiv {
  margin-top: 20px;
}
</style>
