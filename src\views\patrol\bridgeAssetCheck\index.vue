<template>
  <div class="app-container">
    <!--查询条件开始-->
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" label-width="68px">
      <el-form-item prop="manageMaintainUnitId">
        <tree-select v-model="queryParams.manageMaintainUnitId" :options="deptOptions" :show-count="true"
                     style="width: 240px"
                     placeholder="请选择管养单位"/>
      </el-form-item>
      <el-form-item prop="maintenanceSection">
        <el-select v-model="queryParams.maintenanceSection" placeholder="养护路段" clearable style="width: 240px">
          <el-option
            v-for="v in maintenanceSectionList" :key="v.maintenanceSectionId"
            :label="v.maintenanceSectionName"
            :value="v.maintenanceSectionId"
          />
        </el-select>
      </el-form-item>
      <el-form-item prop="routeCode">
        <el-select v-model="queryParams.routeCode" filterable clearable placeholder="请选择路线编码"
                   style="width: 240px">
          <el-option
            v-for="item in routeList"
            :key="item.routeId"
            :label="item.routeName"
            :value="item.routeCode">
          </el-option>
        </el-select>
      </el-form-item>

      <el-form-item label="" prop="assetName">
        <el-input
          v-model="queryParams.assetName"
          placeholder="请输入资产名称"
          clearable
          style="width: 240px"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item style="width: 200px">
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
        <el-button v-show="!showSearch" @click="showSearch=true" icon="el-icon-arrow-down" circle></el-button>
        <el-button v-show="showSearch" @click="showSearch=false" icon="el-icon-arrow-up" circle></el-button>
      </el-form-item>
      <!--默认折叠-->
      <el-form-item v-show="showSearch" label="" prop="checkTime">
        <el-date-picker clearable
                        v-model="queryParams.checkTime"
                        type="daterange"
                        value-format="yyyy-MM-dd"
                        start-placeholder="检查开始时间"
                        end-placeholder="检查结束时间"
                        style="width: 240px">
        </el-date-picker>
      </el-form-item>

      <el-form-item v-show="showSearch" label="" prop="status">
        <DictSelect v-model="queryParams.status" type="patrol_inspection_status" placeholder="请选择状态"
                    style="width: 240px"></DictSelect>
      </el-form-item>
    </el-form>
    <!--查询条件结束-->


    <!--操作按钮区开始-->
    <el-row :gutter="10" style="margin-bottom: 8px">
      <el-col :span="1.5">
        <el-button
          type="primary"
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['system:assetCheck:add']"
        >新增
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['system:assetCheck:edit']"
        >修改
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['system:assetCheck:remove']"
        >删除
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="info"
          plain
          icon="el-icon-upload2"
          size="mini"
          @click="handleImport"
          v-hasPermi="['system:assetCheck:export']"
        >导入
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['system:assetCheck:export']"
        >导出
        </el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList" :columns="columns"></right-toolbar>
    </el-row>
    <!--操作按钮区结束-->

    <!--数据表格开始-->
    <div class="tableDiv">
      <el-table size="mini" :height="showSearch ? 'calc(100vh - 320px)' : 'calc(100vh - 260px)'" style="width: 100%"
                v-loading="loading" border :data="assetCheckList" @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="50" align="center"/>
        <el-table-column fixed label="序号" type="index" width="50">
          <template v-slot="scope">
            {{ (scope.$index + (queryParams.pageNum - 1) * queryParams.pageSize)+1 }}
          </template>
        </el-table-column>
        <el-table-column label="检查类型" align="center" prop="type" min-width="140" show-overflow-tooltip>
          <template slot-scope="scope">
            <dict-tag :options="dict.type.patrol_inspection_category" :value="scope.row.type"/>
          </template>
        </el-table-column>
        <el-table-column label="养护路段" align="center" prop="routeName" min-width="140" show-overflow-tooltip/>
        <el-table-column label="路线编码" align="center" prop="routeCode" min-width="140" show-overflow-tooltip/>
        <el-table-column label="桥梁编码" align="center" prop="assetCode" min-width="140" show-overflow-tooltip/>
        <el-table-column label="桥梁名称" align="center" prop="assetName" min-width="140" show-overflow-tooltip/>
        <el-table-column label="桥位桩号" align="center" prop="mile"/>
        <el-table-column label="权属单位" align="center" prop="propertyUnitName" min-width="140" show-overflow-tooltip/>
        <el-table-column label="养护单位" align="center" prop="maintainUnitName" min-width="140" show-overflow-tooltip/>
        <el-table-column label="天气" align="center" prop="weather"/>
        <el-table-column label="负责人" align="center" prop="kahuna"/>
        <el-table-column label="记录人" align="center" prop="oprUser"/>
        <el-table-column label="巡查类别 " align="center" prop="category">
          <template slot-scope="scope">
            <dict-tag :options="dict.type.patrol_inspection_type" :value="scope.row.category"/>
          </template>
        </el-table-column>
        <el-table-column label="检查日期" align="center" prop="checkTime" width="180" min-width="140"
                         show-overflow-tooltip>
          <template slot-scope="scope">
            <span>{{ parseTime(scope.row.checkTime, '{y}-{m}-{d}') }}</span>
          </template>
        </el-table-column>
        <el-table-column label="状态" align="center" prop="status">
          <template slot-scope="scope">
            <dict-tag :options="dict.type.patrol_inspection_status" :value="scope.row.status"/>
          </template>
        </el-table-column>
        <el-table-column label="备注" align="center" prop="remark"/>

        <el-table-column
          label="操作"
          fixed="right"
          align="center"
          width="160"
          class-name="small-padding fixed-width"
        >
          <template slot-scope="scope" v-if="scope.row.userId !== 1">
            <el-button
              size="mini"
              type="text"
              icon="el-icon-edit"
              @click="handleUpdate(scope.row)"
              v-hasPermi="['patrol:assetCheck:edit']"
            >修改
            </el-button>
            <el-button
              size="mini"
              type="text"
              icon="el-icon-delete"
              @click="handleDelete(scope.row)"
              v-hasPermi="['patrol:assetCheck:remove']"
            >删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <pagination
        v-show="total>0"
        :total="total"
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
        @pagination="getList"
      />
    </div>
    <!--数据表格结束-->


    <!-- 添加或修改资产寻检查主对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="80%" append-to-body :close-on-click-modal="false">

      <AssetCheckInsertOrUpdate ref="assetCheckInsertOrUpdate" :check-entity="form"></AssetCheckInsertOrUpdate>

      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 用户导入对话框 -->
    <el-dialog :title="upload.title" :visible.sync="upload.open" width="400px" append-to-body>
      <el-upload
        ref="upload"
        :limit="1"
        accept=".xlsx, .xls"
        :headers="upload.headers"
        :action="upload.url + '?updateSupport=' + upload.updateSupport"
        :disabled="upload.isUploading"
        :on-progress="handleFileUploadProgress"
        :on-success="handleFileSuccess"
        :auto-upload="false"
        drag
      >
        <i class="el-icon-upload"></i>
        <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
        <div class="el-upload__tip text-center" slot="tip">
          <div class="el-upload__tip" slot="tip">
            <el-checkbox v-model="upload.updateSupport"/>
            是否更新已经存在的用户数据
          </div>
          <span>仅允许导入xls、xlsx格式文件。</span>
          <el-link type="primary" :underline="false" style="font-size:12px;vertical-align: baseline;"
                   @click="importTemplate">下载模板
          </el-link>
        </div>
      </el-upload>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitFileForm">确 定</el-button>
        <el-button @click="upload.open = false">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {listAssetCheck, getAssetCheck, delAssetCheck, addAssetCheck, updateAssetCheck} from "@/api/patrol/assetCheck";
import {getToken} from "@/utils/auth";

import TreeSelect from "@riophae/vue-treeselect";
import "@riophae/vue-treeselect/dist/vue-treeselect.css";

import AssetCheckInsertOrUpdate from "./insertOrUpdate.vue";
import {deptTreeSelect} from "@/api/system/user";
import {listMaintenanceSectionAll} from "@/api/system/maintenanceSection";
import {listAllRoute} from "@/api/system/route";


export default {
  name: "AssetCheck",
  dicts: ['patrol_inspection_type', 'patrol_inspection_category', 'patrol_inspection_status'],
  components: {TreeSelect, AssetCheckInsertOrUpdate},
  props: {
    /**
     * 巡查类型；桥梁日常巡查、桥梁经常检查、隧道日常巡查等
     */
    type: {
      type: String,
      default: '1'
    },
    /**
     * menuType 菜单类型 ps: 1:隧道日常巡查   2:隧道日常巡查审核  3:隧道日常巡查结果
     */
    menuType: {
      type: Number,
      // require: true
      default: 1
    }
  },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: false,
      // 总条数
      total: 0,
      // 资产寻检查主表格数据
      assetCheckList: null,
      // 弹出层标题
      title: "",
      partsType: {
        '1': '桥梁日常巡查',
        '2': '桥梁经常检查',
        '3': '涵洞定期检查',
        '4': '涵洞经常检查',
        '5': '隧道日常巡查',
        '6': '隧道经常检查',
      },
      deptOptions: [],
      maintenanceSectionList: null,
      routeList: null,
      // 是否显示弹出层
      open: false,
      // 表单参数
      form: {
        type: this.type,
        partsType: this.type,
        details: null
      },
      // 用户导入参数
      upload: {
        // 是否显示弹出层（用户导入）
        open: false,
        // 弹出层标题（用户导入）
        title: "",
        // 是否禁用上传
        isUploading: false,
        // 是否更新已经存在的用户数据
        updateSupport: 0,
        // 设置上传的请求头部
        headers: {Authorization: "Bearer " + getToken()},
        // 上传的地址
        url: process.env.VUE_APP_BASE_API + "/system/user/importData"
      },
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 50,
        type: this.type,
        category: null,
        kahuna: null,
        oprUser: null,
        checkTime: null,
        checkStartTime: null,
        checkEndTime: null,
        status: null,
        image: null,
        assetId: null,
        assetName: null,
        assetCode: null,
        routeName: null,
        routeCode: null,
        propertyUnitName: null,
        maintainUnitName: null,
        mile: null,
        weather: null
      },
      // 列信息
      columns: [
        {key: 0, label: `检查类型`, visible: true},
        {key: 1, label: `巡查类别 `, visible: true},
        {key: 2, label: `负责人`, visible: true},
        {key: 3, label: `记录人`, visible: true},
        {key: 4, label: `检查时间`, visible: true},
        {key: 5, label: `状态 审核状态`, visible: true},
        {key: 6, label: `图像`, visible: true},
        {key: 7, label: `备注`, visible: true},
        {key: 8, label: `删除标识`, visible: true},
        {key: 9, label: `资产id`, visible: true},
        {key: 10, label: `资产名称`, visible: true},
        {key: 11, label: `资产编码`, visible: true},
        {key: 12, label: `路线名称`, visible: true},
        {key: 13, label: `路线编码`, visible: true},
        {key: 14, label: `权属单位`, visible: true},
        {key: 15, label: `养护单位`, visible: true},
        {key: 16, label: `桩号`, visible: true},
        {key: 17, label: `天气`, visible: true}
      ],

    };
  },
  watch: {
    'queryParams.checkTime': function (val) {
      if (Array.isArray(val)) {
        this.queryParams.checkStartTime = val[0]
        this.queryParams.checkEndTime = val[1]
      }
    }
  },
  created() {
    this.getList();
    this.getDeptTree()
    this.getRouteList()
    this.getMaintenanceSection()
  },
  methods: {
    /** 查询用户列表 */
    getList() {
      this.loading = true;
      listAssetCheck(this.queryParams).then(response => {
        this.assetCheckList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    /** 查询部门下拉树结构 */
    getDeptTree() {
      return deptTreeSelect().then(response => {
        this.deptOptions = response.data;
      });
    },
    /** 查询养护路段下拉列表 */
    getMaintenanceSection() {
      listMaintenanceSectionAll().then(res => {
        this.maintenanceSectionList = res.data
      })
    },
    /** 查询路线列表 */
    getRouteList() {
      listAllRoute().then(res => {
        this.routeList = res.data
      })
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        type: this.type,
        partsType: this.type,
        category: null,
        kahuna: null,
        oprUser: null,
        checkTime: null,
        status: null,
        image: null,
        remark: null,
        delFlag: null,
        assetId: null,
        assetName: null,
        assetCode: null,
        routeName: null,
        routeCode: null,
        propertyUnitName: null,
        maintainUnitName: null,
        mile: null,
        weather: null,
        details: null
      };
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dateRange = [];
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id);
      this.single = selection.length != 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = `添加${this.partsType[this.type]}`;
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      // this.reset();
      // const id = row.id || this.ids;
      // getAssetCheck(id).then(response => {
      //   this.form = {...response.data, details: null};
      //   this.open = true;
      //   this.title = "修改资产寻检查主";
      // });
      this.form = {...row, type: this.type, partsType: this.type, details: null}
      this.open = true;
      this.title = `修改${this.partsType[this.type]}`;
    },
    /** 提交按钮 */
    submitForm: function () {
      this.$refs.assetCheckInsertOrUpdate.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.id != null) {
            updateAssetCheck(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addAssetCheck(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const id = row.id || this.ids;
      this.$modal.confirm('是否确认删除资产寻检查主编号为"' + id + '"的数据项？').then(function () {
        return delAssetCheck(id);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {
      });
    },

    /** 导出按钮操作 */
    handleExport() {

      this.download('patrol/assetCheck/export', {
        ...this.queryParams
      }, `assetCheck_${new Date().getTime()}.xlsx`)

    },
    /** 导入按钮操作 */
    handleImport() {
      this.upload.title = "用户导入";
      this.upload.open = true;
    },
    /** 下载模板操作 */
    importTemplate() {
      this.download('system/user/importTemplate', {}, `user_template.xlsx`)
    },
    // 文件上传中处理
    handleFileUploadProgress(event, file, fileList) {
      this.upload.isUploading = true;
    },
    // 文件上传成功处理
    handleFileSuccess(response, file, fileList) {
      this.upload.open = false;
      this.upload.isUploading = false;
      this.$refs.upload.clearFiles();
      this.$alert("<div style='overflow: auto;overflow-x: hidden;max-height: 70vh;padding: 10px 20px 0;'>" + response.msg + "</div>", "导入结果", {dangerouslyUseHTMLString: true});
      this.getList();
    },
    // 提交上传文件
    submitFileForm() {
      this.$refs.upload.submit();
    }
  }
};
</script>
<style scoped>
.tableDiv {
  background-color: white;
  padding-bottom: 10px;
}

::v-deep .vue-treeselect__control {
  height: auto;
}

::v-deep .el-dialog__body {
  padding: 10px 20px;
  height: 75vh;
  overflow-y: auto;
  scrollbar-width: none
}
</style>
