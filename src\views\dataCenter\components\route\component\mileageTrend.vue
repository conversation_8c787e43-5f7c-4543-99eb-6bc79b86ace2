<template>
  <div class="mileage-trend" v-loading="loading" element-loading-background="rgba(0, 0, 0, 0.4)">
    <Echarts :option="option" v-if="option" height="100%" key="trendKey" />
  </div>
</template>

<script>
import * as echarts from "echarts";
import Echarts from '../../echarts/echarts.vue';
// api
import { getRouteMileageTrend } from "@/api/cockpit/route";

export default {
  components: {
    Echarts
  },
  data() {
    return {
      option: null,
      loading: false,
      // 通车里程趋势数据
      mileageTrend: [],
    }
  },
  async mounted() {
    this.loading = true;
    let data = await this.getMileageTrend();
    let arr = [];
    // 遍历每一年的数据，计算该年之前所有数据的 length 总和
    for (let i = 0; i < data.length; i++) {
      let sum = 0;
      for (let j = 0; j <= i; j++) {
        sum += data[j].length;
      }
      arr.push({
        YEAR: data[i].YEAR || data[i].year,
        length: sum
      });
    }
    this.mileageTrend = arr; // 赋值给mileageTrend，用于渲染char
    this.loading = false;
    this.initCharts();
  },
  methods: {
    // 获取 通车里程趋势 数据
    getMileageTrend() {
      return new Promise((resolve, reject) => {
        getRouteMileageTrend().then((res) => {
          if (res.code === 200 && res.data) {
            resolve(res.data)
          } else {
            reject(res.message)
          }
        }).catch((err) => {
          reject(err)
        });
      })
    },
    initCharts() {
      var fontColor = '#FFFFFF';
      let xAxisData = this.mileageTrend && this.mileageTrend.length ? this.mileageTrend.map(v => v.YEAR || v.year) : [];
      let seriesData = this.mileageTrend && this.mileageTrend.length ? this.mileageTrend.map(v => v.length) : [];
      this.option = {
        backgroundColor: 'rgba(0,0,0,0)',
        grid: {
          left: '1%',
          right: '5%',
          top: '16%',
          bottom: '2%',
          containLabel: true
        },
        tooltip: {
          show: false,
        },
        legend: {
          show: false,
        },
        xAxis: [
          {
            type: 'category',
            boundaryGap: false,
            axisLabel: {
              color: fontColor,
              fontSize: 24,
            },
            axisLine: {
              show: true,
              lineStyle: {
                color: 'transparent'
              }
            },
            axisTick: {
              show: false,
            },
            splitLine: {
              show: false,
            },
            data: xAxisData || ['2016', '2017', '2018', '2019', '2020', '2021', '2022', '2023', '2025']
          }
        ],
        yAxis: [
          {
            type: 'value',
            name: '个',
            min: 0,
            axisLabel: {
              formatter: '{value}',
              textStyle: {
                color: '#999999',
                fontSize: 24,
              }
            },
            nameTextStyle: {
              color: '#999999',
              fontSize: 24,
              padding: [0, 40, 10, 0]
            },
            axisLine: {
              show: false,
              lineStyle: {
                color: '#6E7079'
              }
            },
            axisTick: {
              show: false,
            },
            splitLine: {
              show: true,
              lineStyle: {
                color: 'rgba(110,112,121,0.3)'
              },
            },
            splitArea: false,
          },
        ],
        series: [
          {
            name: '',
            type: 'line',
            stack: '总量',
            symbol: 'circle',
            symbolSize: 8,
            itemStyle: {
              normal: {
                color: '#ffffff',
                borderColor: '#00FFB7',
                lineStyle: {
                  color: "#1CFFBC",
                  width: 1
                },
                areaStyle: {
                  color: new echarts.graphic.LinearGradient(0, 1, 0, 0, [{
                    offset: 0,
                    color: 'rgba(28,255,188,0.1)'
                  }, {
                    offset: 1,
                    color: 'rgba(28,255,188,0.6)'
                  }]),
                }
              }
            },
            data: seriesData || [9000, 10000, 8000, 9300, 7600, 8100, 5700, 6000, 7000, 7000]
          },
        ]
      };
    }
  },
}
</script>

<style lang="scss" scoped>
.mileage-trend {
  width: 100%;
  height: 100%;
}
</style>