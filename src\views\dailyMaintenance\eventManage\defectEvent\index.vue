<template>
  <div class="app-container maindiv">
    <el-row :gutter="20">
      <!--部门数据-->
<!--      <el-col :span="relaNav ? 5 : 0" :xs="24" class="leftDiv">-->
<!--        &lt;!&ndash;折叠图标&ndash;&gt;-->
<!--        <div class="leftIcon" @click="relaNav = false">-->
<!--          <span class="el-icon-caret-left"></span>-->
<!--        </div>-->
<!--        <div class="head-container">-->
<!--          <el-input-->
<!--            v-model="keyword"-->
<!--            placeholder="输入关键词检索"-->
<!--            @change="handleSearch"-->
<!--            clearable-->
<!--            size="small"-->
<!--            prefix-icon="el-icon-search"-->
<!--            style="margin-bottom: 20px"-->
<!--          />-->
<!--        </div>-->
<!--        <div class="left-total">共 {{ leftTotal }} 条</div>-->
<!--        <div class="head-container" style="width: 300px">-->
<!--          <el-tree-->
<!--            :data="filteredTreeData"-->
<!--            :expand-on-click-node="false"-->
<!--            :filter-node-method="filterNode"-->
<!--            :default-expanded-keys="[0]"-->
<!--            ref="tree"-->
<!--            node-key="id"-->
<!--            highlight-current-->
<!--            @node-click="handleNodeClick"-->
<!--          >-->
<!--          </el-tree>-->
<!--        </div>-->
<!--      </el-col>-->
      <!--角色数据-->
      <el-col :span="24" :xs="24">
        <!--展开图标-->
        <div class="rightIcon" @click="relaNav = true" v-show="!relaNav">
          <span class="el-icon-caret-right"></span>
        </div>
        <el-row>
          <el-col :span="24" :xs="24">
            <el-row>
              <el-form
                ref="queryForm"
                :model="queryParams"
                size="mini"
                :inline="true"
                label-width="68px"
              >
                <el-form-item label="" prop="domainId">
                  <el-date-picker
                    v-model="queryParams.yearStr"
                    style="width: 240px"
                    type="year"
                    value-format="yyyy"
                    placeholder="年份">
                  </el-date-picker>
                </el-form-item>
                <el-form-item label="" prop="domainId">
                  <selectTree
                    :key="'field2'"
                    style="width: 240px"
                    v-model="queryParams.domainIdStr"
                    :deptType="100" :deptTypeList="[1, 3, 4]"
                    placeholder="管养单位"
                    clearable
                    filterable
                  />
                </el-form-item>
                <el-form-item label="" prop="maiSecId">
                  <RoadSection v-model="queryParams.maiSecId" :deptId="queryParams.domainIdStr" placeholder="路段名称"/>
                </el-form-item>
                <el-form-item>
                  <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
                  <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
                  <el-button v-show="!showSearch" @click="showSearch=true" icon="el-icon-arrow-down" circle></el-button>
                  <el-button v-show="showSearch" @click="showSearch=false" icon="el-icon-arrow-up" circle></el-button>
                </el-form-item>
              </el-form>
              <el-col :span="24">
                <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch"
                         label-width="68px">
                  <el-form-item label="" prop="routeCode">
                    <el-input
                      v-model="queryParams.routeCode"
                      placeholder="请输入路线编码"
                      clearable
                      style="width: 240px"
                    >
                    </el-input>
                  </el-form-item>
                  <el-form-item label="" prop="direction">
                    <dict-select type="route_direction" clearable
                                 v-model="queryParams.direction" placeholder="请选择路线方向"
                                 style="width: 240px"></dict-select>
                  </el-form-item>
                  <el-form-item label="" prop="lane">
                    <dict-select type="lane" clearable
                                 v-model="queryParams.lane" placeholder="请选择位置"
                                 style="width: 240px"></dict-select>
                  </el-form-item>
                  <el-form-item label="" prop="beginMile">
                    <el-input
                      v-model="queryParams.beginMileStr"
                      placeholder="起点桩号"
                      clearable
                      style="width: 115px"
                    >
                    </el-input>
                    <span style="width: 10px;display: inline-block;text-align: center">~</span>
                    <el-input
                      v-model="queryParams.endMileStr"
                      placeholder="终点桩号"
                      clearable
                      style="width: 115px"
                    >
                    </el-input>
                  </el-form-item>
                  <el-form-item label="" prop="assetMainType">
                    <dict-select type="sys_asset_type" clearable v-model="queryParams.assetTypeStr" placeholder="请选择资产类型"
                                 style="width: 240px" level="1"></dict-select>
                  </el-form-item>
                  <el-form-item label="" prop="diseaseType">
                    <el-select v-model="queryParams.diseaseType" style="width: 100%" placeholder="请选择事件类型" filterable>
                      <el-option v-for="dict in disType" :key="dict.dictValue"
                                 :label="dict.dictLabel" :value="dict.dictValue">
                      </el-option>
                    </el-select>
                  </el-form-item>
                  <el-form-item label="" prop="disStatus">
                    <dict-select type="dis_status_name" clearable v-model="queryParams.disStatusStr" placeholder="请输入状态"
                                 style="width: 240px"></dict-select>
                  </el-form-item>
                  <el-form-item label="" prop="stage">
                    <dict-select type="dis_stage_name" clearable v-model="queryParams.stageStr" placeholder="请输入阶段"
                                 style="width: 240px"></dict-select>
                  </el-form-item>
                  <el-form-item label="" prop="code">
                    <el-input
                      v-model="queryParams.code"
                      placeholder="请输入施工单编码"
                      clearable
                      style="width: 240px"
                    >
                    </el-input>
                  </el-form-item>
                  <el-form-item label="" prop="collectDates">
                    <el-date-picker
                      v-model="queryParams.collectDates"
                      format="yyyy-MM-dd"
                      value-format="yyyy-MM-dd HH:mm:ss"
                      style="width: 240px"
                      placeholder="采集开始时间"
                      clearable
                    ></el-date-picker>
                  </el-form-item>
                  <el-form-item label="" prop="collectDatee">
                    <el-date-picker
                      v-model="queryParams.collectDatee"
                      format="yyyy-MM-dd"
                      value-format="yyyy-MM-dd HH:mm:ss"
                      style="width: 240px"
                      placeholder="采集结束时间"
                      clearable
                    ></el-date-picker>
                  </el-form-item>
                </el-form>
              </el-col>
            </el-row>
          </el-col>
        </el-row>
        <!--操作按钮区开始-->
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button
              type="warning"
              icon="el-icon-download"
              size="mini"
              v-has-menu-permi="['disease:disease:export']"
              @click="exportList"
            >导出清单
            </el-button
            >
          </el-col>
          <el-col :span="1.5">
            <el-button
              type="success"
              icon="el-icon-view"
              size="mini"
              @click="openDisInfo"
            >事件信息
            </el-button
            >
          </el-col>
          <right-toolbar
            :showSearch.sync="showSearch"
            @queryTable="handleQuery"
            :columns="columns"
          ></right-toolbar>
        </el-row>
        <el-row>
          <div class="draggable">
            <el-table v-adjust-table
              size="mini"
              style="width: 100%"
              v-loading="loading"
              border
              :data="tableData"
              row-key="id"
              ref="dataTable"
              stripe
              highlight-current-row
              @row-click="handleClickRow"
              @selection-change="handleSelectionChange"
              :height="
                showSearch ? 'calc(100vh - 330px)' : 'calc(100vh - 270px)'
              "
            >
              <el-table-column type="selection" width="50" align="center"/>
              <el-table-column
                label="序号"
                align="center"
                type="index"
                width="50"
              />
              <template v-for="(column,index) in columns">
                <el-table-column :label="column.label" show-overflow-tooltip
                                 v-if="column.visible"
                                 align="center"
                                 :prop="column.field"
                                 :width="column.width">
                  <template slot-scope="scope">
                    <dict-tag v-if="column.dict" :options="dict.type[column.dict]" :value="scope.row[column.field]"/>
                    <template v-else-if="column.slots">
                      <RenderDom :row="scope.row" :index="index" :render="column.render"/>
                    </template>
                    <span v-else-if="column.isTime">{{ parseTime(scope.row[column.field], '{y}-{m}-{d}') }}</span>
                    <span v-else>{{ scope.row[column.field] }}</span>
                  </template>
                </el-table-column>
              </template>
            </el-table>
            <pagination
              v-show="total>0"
              :total="total"
              :page.sync="queryParams.pageNum"
              :limit.sync="queryParams.pageSize"
              @pagination="handleQuery"
            />
          </div>
        </el-row>
      </el-col>
    </el-row>
    <el-dialog title="事件信息" destroy-on-close :visible.sync="dialogVisible" width="80%" v-if="dialogVisible">
      <event-detail :dis-id="clickRow.disId" :daliy-id="clickRow.daliyId"/>
<!--      <event-info :dis-id="clickRow.id" :daliy-id="clickRow.daliyId"></event-info>-->
    </el-dialog>
    <el-dialog title="附件列表" destroy-on-close :visible.sync="openFile" width="80%">
      <file-upload v-model="disFilePath" :forView="true"></file-upload>
    </el-dialog>
  </div>
</template>

<script>
import {findUserDeptMaintenanceList2} from '@/api/system/maintenanceSection'
import {
  findDiseaseDataList,
  batchDeleteDiseaseData,
  deleteDiseaseData, findRoaddiseaseList
} from '@/api/dailyMaintenance/eventManage/eventData.js'
import EventInfo from "../../component/eventTreeInfo.vue";
import selectTree from "@/components/DeptTmpl/selectTree.vue";
import RoadSection from "@/views/baseData/components/roadSection/index.vue";
import moment from "moment";
import EventDetail from "@/views/dailyMaintenance/component/eventDetail.vue";
import {formatPile} from "@/utils/ruoyi";
import { listAllDiseases } from '@/api/patrol/diseases'

export default {
  name: 'DefectEvent',
  components: {
    EventDetail,
    RoadSection, selectTree,
    EventInfo,
    RenderDom: {
      functional: true,
      props: {
        row: Object,
        index: Number,
        render: Function
      },
      render(createElement, ctx) {
        const {row, index} = ctx.props
        return ctx.props.render(row, index)
      }
    }
  },
  dicts: ['route_direction', 'disposal_type', 'event_type', 'sys_asset_type'],
  props: [],
  data() {
    return {
      leftTotal: 1,
      showSearch: false,
      queryParams: {
        pageNum: 1,
        pageSize: 50
      },
      total: 0,
      loading: false,
      columns: [
      {key: 0, width: 100, field: 'maiSecId', label: `路段名称`, visible: true},
      {key: 1, width: 100, field: 'routeCode', label: `路线编码`, visible: true},
      {key: 23, width: 200, field: 'subRouteCode', label: `养护子段`, visible: true},
      {key: 22, width: 200, field: 'disCode', label: `事件编码`, visible: true},
      {key: 2, width: 100, field: 'beginMileShow', label: `起点桩号`, visible: true},
      {key: 3, width: 100, field: 'endMileShow', label: `终点桩号`, visible: true},
      {key: 4, width: 100, field: 'domainName', label: `管养单位`, visible: true},
      {key: 5, width: 100, field: 'domainName', label: `管理处`, visible: true},
      {key: 6, width: 200, field: 'diseaseName', label: `事件类型`, visible: true, dict: 'sys_asset_type'},
      {key: 7, width: 200, field: 'diseaseDesc', label: `事件描述`, visible: true},
      {key: 8, width: 100, field: 'dealType', label: `处置类型`, visible: true, dict: 'disposal_type'},
      {key: 9, width: 100, field: 'handleStatusName', label: `事件状态`, visible: true},
      {key: 10, width: 100, field: 'reportName', label: `上报人`, visible: true},
      {key: 11, width: 100, field: 'disFrom', label: `事件来源`, visible: true},
      {key: 12, width: 100, field: 'createReasonName', label: `事件分类`, visible: true},
      {key: 13, width: 100, field: 'collectDate', label: `采集时间`, visible: true, isTime: true},
      {
        key: 14, width: 100, field: 'disPicPath', label: `附件`, visible: true, slots: true, render: (row, index) => {
          return (
            <el-button
              size="mini"
              disabled = {!row.disPicPath}
              type="text" onClick={e => this.handleOpenFile(e, row)}>查看</el-button>
          )
        }
      }
    ],
      tableData: [],
      rowData: {},
      drawerTitle: '新增事件',
      drawer: false,
      openFile: false,
      openOperate: false,
      dialogVisible: false,
      selectIds: [],
      clickRow: {},
      // 左侧组织树
      relaNav: true,
      disFilePath: '',
      keyword: '',
      relaName: '',
      relaOptions: [],
      filteredTreeData: [],
      disType: []
    }
  },
  computed: {},
  watch: {},
  created() {
    this.getDisType()
    this.getDeptTree()
    this.handleQuery()
  },
  mounted() {
  },
  methods: {
    // 关键词检索
    handleSearch() {
      const keyword = this.keyword.toLowerCase();
      this.filteredTreeData = this.relaOptions.filter(node => this.filterNode(node, keyword));
    },
    // 筛选节点
    filterNode(node, keyword) {
      if (node.label.indexOf(keyword) != -1) {
        return true;
      }
      if (node.children) {
        return node.children.some(childNode => this.filterNode(childNode, keyword));
      }
      return false;
    },
    // 查询部门下拉树结构
    getDeptTree() {
      findUserDeptMaintenanceList2().then(response => {
        const treeData = response.data
        treeData.forEach(item => {
          getChild(item)
        })

        function getChild(node) {
          node.label = node.deptName || node.maintenanceSectionName
          node.id = node.deptId || node.maintenanceSectionId
          if (node.children) {
            node.children.forEach(item => {
              getChild(item)
            })
          }
        }

        // 增加一个最顶级
        const tree = [
          {
            label: '云南省交通投资建设集团有限公司',
            deptId: '1',
            id: '1',
            children: [...treeData]
          }
        ]
        this.relaOptions = tree
        this.filteredTreeData = [...this.relaOptions]
      });
    },
    handleNodeClick(e) {
      this.queryParams.domainId = ''
      this.queryParams.domainName = ''
      this.queryParams.maiSecName = ''
      this.queryParams.maiSecId = ''
      this.queryParams.domainIdStr = ''
      if (e.deptId) {
        this.queryParams.domainIdStr = String(e.id)
        this.queryParams.domainId = parseInt(e.id)
        this.queryParams.domainName = e.label
      } else {
        this.queryParams.domainIdStr = String(e.departmentId)
        this.queryParams.domainId = parseInt(e.departmentId)
        this.queryParams.domainName = e.departmentName
        this.queryParams.maiSecName = e.label
        this.queryParams.maiSecId = e.id
      }
      this.handleQuery()
    },
    // 选中
    handleSelectionChange(e) {
      this.selectIds = e.map(obj => obj.id)
    },
    // 查询
    handleQuery() {
      this.loading = true
      this.queryParams.year = parseInt(this.queryParams.yearStr) || null
      this.queryParams.domainId = parseInt(this.queryParams.domainIdStr) || null
      this.queryParams.endMile = parseInt(this.queryParams.endMileStr) || null
      this.queryParams.beginMile = parseInt(this.queryParams.beginMileStr) || null
      this.queryParams.assetMainType = parseInt(this.queryParams.assetTypeStr) || null
      this.queryParams.disType = parseInt(this.queryParams.disTypeStr) || null
      this.queryParams.disStatus = parseInt(this.queryParams.disStatusStr) || null
      this.queryParams.stage = parseInt(this.queryParams.stageStr) || null
      if (this.queryParams.domainId == 1) {
        this.queryParams.domainId = null
      }
      const endTime = this.queryParams.collectTimee ? moment(this.queryParams.collectTimee).endOf("day").format('YYYY-MM-DD HH:mm:ss') : null
      this.$set(this.queryParams, 'collectTimee', endTime)
      this.queryParams.sjType = 2
      this.queryParams.flag = 1
      findRoaddiseaseList(this.queryParams).then(res => {
        if (res.code == 200) {
          this.loading = false
          res.rows.forEach(item => {
            item.beginMileShow = formatPile(item.beginMile)
            item.endMileShow = formatPile(item.endMile)
          })
          this.tableData = res.rows
          this.total = res.total
        }
      })
    },
    getDisType() {
      listAllDiseases().then(res => {
        this.disType = res.data.map(item => {
          return {
            dictLabel: item.diseaseName,
            dictValue: item.id
          }
        })
      })
    },
    // 修改
    handleUpdate(e) {
      this.drawerTitle = '修改事件'
      this.rowData = e
      this.drawer = true
    },
    // 新增
    handleAdd() {
      this.drawerTitle = '新增事件'
      this.rowData = {}
      this.drawer = true
    },
    // 删除
    handleDelete(e) {
      this.$modal.confirm('是否确认删除').then(() => {
        deleteDiseaseData(e.id).then(res => {
          if (res.code == 200) {
            this.$modal.msgSuccess('删除成功')
            this.handleQuery()
          }
        })
      })
    },
    checkSelectable(row) {
      return row.disStatus == 1
    },
    handleBatchDelete() {
      if (this.selectIds.length > 0)
        this.$modal.confirm('是否确认删除所选的数据').then(() => {
          batchDeleteDiseaseData(this.selectIds).then(res => {
            this.handleQuery();
            this.$modal.msgSuccess("删除成功");
          })
        })
    },
    resetQuery() {
      this.queryParams = {
        pageNum: 1,
        pageSize: 50
      }
      this.handleQuery()
    },
    handleCloseDetail() {
      this.rowData = {}
      this.drawer = false
      this.handleQuery()
    },
    handleOpenFile(e, row) {
      this.disFilePath = ''
      this.openFile = true
      this.disFilePath = row.disFilePath
    },
    // 导出清单按钮
    exportList() {
      this.download(
        'manager/roaddisease/export',
        {...this.queryParams},
        `disease_${new Date().getTime()}.xlsx`,
        {
          headers: { 'Content-Type': 'application/json;' },
          parameterType: 'body'
        }
      )
    },
    handleClickRow(e) {
      this.clickRow = e
      e.isSelected = !e.isSelected;
      this.$refs.dataTable.toggleRowSelection(e);
    },
    openDisInfo(row) {
      if (!this.clickRow.id) {
        this.$message.warning('请选择一条事件')
        return
      }
      this.dialogVisible = true
      this.disId = row.id
    },
    handleSubmit(e, status) {
      if (status === 1) {
        this.$modal.confirm('是否将该事件作为被损工程提交到被损被盗项目中?').then(() => {
        })
      } else {
        this.$modal.confirm('是否将该事件作为被盗工程提交到被损被盗项目中?').then(() => {
        })
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.leftDiv {
  border-right: 1px solid #d8dce5;
  min-height: calc(100vh - 100px);
  overflow-y: auto;
  height: calc(80vh - 150px);
  position: relative;
  top: -20px;
  padding-top: 10px;
  background-color: white;
}

.leftIcon {
  border: 1px solid #dcdfe6;
  border-radius: 8px;
  width: 16px;
  height: 50px;
  line-height: 50px;
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
  position: absolute;
  right: 0;
  top: 300px;
  z-index: 2;
}

.leftIcon:hover {
  background-color: #dcdfe6;
}

.rightIcon {
  border: 1px solid #dcdfe6;
  border-radius: 8px;
  width: 16px;
  height: 50px;
  line-height: 50px;
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
  position: absolute;
  left: -10px;
  top: 280px;
  z-index: 10;
  background: white;
}

.rightIcon:hover {
  background-color: #dcdfe6;
}

.left-total {
  font-size: 13px;
  line-height: 28px;
  color: #606266;
  position: absolute;
  bottom: 10px;
  right: 10px;
}
</style>
<style lang="scss" scoped>
@import "@/assets/styles/business.scss";
</style>
