<template>
	<div class="component-upload-image">
		<el-upload
			multiple
			ref="imageUpload"
			:action="uploadImgUrl"
			list-type="picture-card"
			:on-success="handleUploadSuccess"
			:before-upload="handleBeforeUpload"
			:limit="limit"
			:on-error="handleUploadError"
			:on-exceed="handleExceed"
			:on-remove="handleDelete"
			:show-file-list="true"
			:headers="headers"
			:file-list="fileList"
			:on-preview="handlePictureCardPreview"
			:class="{ hide: this.fileList.length >= this.limit || hideUpload, read: disabled }"
		>
			<i class="el-icon-plus"></i>
		</el-upload>

		<!-- 上传提示 -->
		<div class="el-upload__tip" slot="tip" v-if="showTip">
			请上传
			<template v-if="fileSize">
				大小不超过
				<b style="color: #f56c6c">{{ fileSize }}MB</b>
			</template>
			<template v-if="fileType">
				格式为
				<b style="color: #f56c6c">{{ fileType.join('/') }}</b>
			</template>
			的文件
		</div>

		<el-dialog :visible.sync="dialogVisible" title="预览" width="50%" append-to-body>
			<img :src="dialogImageUrl" style="display: block; max-width: 100%; margin: 0 auto" />
		</el-dialog>
	</div>
</template>

<script>
import { getToken } from '@/utils/auth'
import axios from 'axios'

export default {
	name: 'ImageUpload',
	props: {
		value: [String, Array],
		// 图片数量限制
		limit: {
			type: Number,
			default: 100,
		},
		// 大小限制(MB)
		fileSize: {
			type: Number,
			default: 100,
		},
		// 文件类型, 例如['png', 'jpg', 'jpeg']
		fileType: {
			type: Array,
			default: () => ['png', 'jpg', 'jpeg'],
		},
		// 是否显示提示
		isShowTip: {
			type: Boolean,
			default: true,
		},
		// 上传平台标识
		platform: {
			type: String,
			default: 'ylzx',
		},
		// 上传地址
		uploadUrl: {
			type: String,
			default: '/ruoyi-file/upload',
		},
		disabled: {
			type: Boolean,
			delete: false,
		},
		/**
		 * 当编辑时，存在 确认、取消 两个按钮，此时应该为false(即手动模式)，手动提交和取消文件操作， 如果是true(自动模式)，那么对文件新增、删除后没法回滚
		 * 但是目前只支持自动模式，后面空了再写
		 * 如果limit=1,那么删除是逻辑删除,limit=1,automatic属性不起作用
		 */
		automatic: {
			type: Boolean,
			default: true,
		},
		// TODO 暂时用不上
		LogicalDeletion: {
			type: Boolean,
			default: false,
		},
		mode: {
			type: Number,
			default: 1,
		},
	},
	data() {
		return {
			uploadList: [],
			removeList: [],
			dialogImageUrl: '',
			dialogVisible: false,
			hideUpload: false,
			headers: {
				Authorization: 'Bearer ' + getToken(),
			},
			fileList: [],
			flag: false,
		}
	},
	watch: {
		value: {
			handler(val) {
				if (val) {
					const list = Array.isArray(val) ? val : this.value.toString().split(',')
					list.forEach((item) => {
						if (typeof item === 'string') {
							axios
								.get(process.env.VUE_APP_BASE_API + '/ruoyi-file/findFiles?ownerId=' + item, {
									headers: {
										Authorization: 'Bearer ' + getToken(),
									},
								})
								.then((res) => {
									let imageList = res.data.data
									imageList.forEach((item) => {
										this.fileList.push({ name: item.fileName, url: item.url, id: item.id })
									})
								})
						} else {
							this.fileList.push(item)
						}
					})
				} else {
					this.fileList = []
					this.$emit('input', this.generateSnowflakeId())
				}
			},
			immediate: true,
		},
		disabled: {
			handler(val) {
				this.hideUpload = val
			},
			immediate: true,
		},
	},
	computed: {
		// 是否显示提示
		showTip() {
			return this.isShowTip && (this.fileType || this.fileSize)
		},
		uploadImgUrl() {
			return (
				process.env.VUE_APP_BASE_API +
				`${
					this.uploadUrl.startsWith('/')
						? `${this.uploadUrl.endsWith('?') ? `${this.uploadUrl}` : `${this.uploadUrl}?`}`
						: `/${this.uploadUrl.endsWith('?') ? `${this.uploadUrl}` : `${this.uploadUrl}?`}`
				}platform=${this.platform}&ownerId=${this.value}`
			)
		},
	},
	methods: {
		generateSnowflakeId() {
			return Date.now().toString()
		},
		// 文件个数超出
		handleExceed() {
			this.$modal.msgError(`上传文件数量不能超过 ${this.limit} 个!`)
		},
		// 上传前loading加载
		handleBeforeUpload(file) {
			let isImg = false
			if (this.fileType.length) {
				let fileExtension = ''
				if (file.name.lastIndexOf('.') > -1) {
					fileExtension = file.name.slice(file.name.lastIndexOf('.') + 1)
				}
				isImg = this.fileType.some((type) => {
					if (file.type.indexOf(type) > -1) return true
					if (fileExtension && fileExtension.indexOf(type) > -1) return true
					return false
				})
			} else {
				isImg = file.type.indexOf('image') > -1
			}

			if (!isImg) {
				this.$modal.msgError(`文件格式不正确, 请上传${this.fileType.join('/')}图片格式文件!`)
				return false
			}
			if (this.fileSize) {
				const isLt = file.size / 1024 / 1024 < this.fileSize
				if (!isLt) {
					this.$modal.msgError(`上传头像图片大小不能超过 ${this.fileSize} MB!`)
					return false
				}
			}
			// this.$modal.loading("正在上传图片，请稍候...");
			this.hideUpload = true
		},

		// 上传成功回调
		handleUploadSuccess(res, file) {
			if (res.code === 200) {
				this.uploadList.push({ name: res.data.fileName, ...res.data })
				this.fileList.push({ name: res.data.fileName, ...res.data })
				// this.$modal.closeLoading();
				this.hideUpload = false
				// this.execMode()
			} else {
				this.hideUpload = false
				// this.$modal.closeLoading();
				this.$modal.msgError(res.msg)
				this.$refs.imageUpload.handleRemove(file)
			}
		},
		// 删除图片
		handleDelete(file) {
			const findex = this.fileList.map((f) => f.name).indexOf(file.name)
			if (findex > -1) {
				this.fileList.splice(findex, 1)
				this.removeList.push(file)

				if (this.limit === 1) {
					this.$emit('input', this.generateSnowflakeId())
				} else if (this.automatic) {
					axios.get(process.env.VUE_APP_BASE_API + '/ruoyi-file/removeFile?fileId=' + file.id, {
						headers: {
							Authorization: 'Bearer ' + getToken(),
						},
					})
				}
				// this.execMode()
			}
		},
		// 上传失败
		handleUploadError() {
			this.$modal.msgError('上传图片失败，请重试')
			// this.$modal.closeLoading();
			this.hideUpload = false
		},
		// 预览
		handlePictureCardPreview(file) {
			this.dialogImageUrl = file.url
			this.dialogVisible = true
		},
		execMode(force = false) {
			if (this.mode === 1) {
				this.$emit('input', this.generateSnowflakeId())
			}
			if (force) {
				this.$emit('input', this.generateSnowflakeId())
			}
		},
	},
}
</script>
<style scoped lang="scss">
// .el-upload--picture-card 控制加号部分
::v-deep.hide .el-upload--picture-card {
	display: none;
}
::v-deep .read .el-upload-list__item-status-label,
::v-deep .read .el-upload-list__item-actions .el-upload-list__item-delete {
	display: none;
}

// 去掉动画效果
::v-deep .el-list-enter-active,
::v-deep .el-list-leave-active {
	transition: all 0s;
}

::v-deep .el-list-enter,
.el-list-leave-active {
	opacity: 0;
	transform: translateY(0);
}

.component-upload-image > div:first-child {
	display: flex;
	justify-content: left;
	align-content: center;
	align-items: center;
}

::v-deep .el-upload--picture-card {
	display: flex;
	justify-content: center;
	align-items: center;
}

//::v-deep .el-upload-list--picture-card,
::v-deep .el-upload-list__item,
::v-deep .el-upload--picture-card {
	/*  width: 58px;
    height: 58px;*/
}

::v-deep .el-upload-list__item-actions {
	font-size: 1rem;
}

::v-deep .el-upload-list__item-actions span + span {
	margin-left: 5px !important;
}
</style>
