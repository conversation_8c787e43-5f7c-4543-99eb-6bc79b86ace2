//湿度概率直方图
import { getMax, getMin } from '@/views/jgjc/dataAnalysis/dataView/drawMethods/util'
import * as echarts from 'echarts'
import myTheme from './myTheme'
export const WIND = {
//风级概率分布柱状图
  setLEVELChart(chartName, data) {
    let myChart = echarts.getInstanceByDom(
      document.getElementById(chartName),
    )
    if (myChart !== undefined) {
      myChart.dispose()
    }
    let optionName = data.optionName
    data = data.dataList
    myChart = echarts.init(document.getElementById(chartName), myTheme.theme)
    // 去除头尾0 确定有效范围索引
    let startIndex = 0
    let endIndex = 18
    for (let i = 0; i < data.length; i++) {
      let tmpStartIndex = 0
      let startFlag = 0
      for (let j = 0; j < 19; j++) {
        if (data[i].distribution[j] !== 0 && startFlag === 0) {
          tmpStartIndex = j
          startFlag = 1
        }
      }
      if (i === 0) {
        startIndex = tmpStartIndex
      } else if (startIndex > tmpStartIndex) {
        startIndex = tmpStartIndex
      }
    }
    for (let i = 0; i < data.length; i++) {
      let tmpEndIndex = 18
      let endFlag = 0
      for (let j = 0; j < 19; j++) {
        if (data[i].distribution[18 - j] !== 0 && endFlag === 0) {
          tmpEndIndex = 18 - j
          endFlag = 1
        }
      }
      if (i === 0) {
        endIndex = tmpEndIndex
      } else if (endIndex < tmpEndIndex) {
        endIndex = tmpEndIndex
      }
    }
    // 去除头尾0 去除无效数据
    for (let i = 0; i < data.length; i++) {
      data[i].distribution = data[i].distribution.slice(startIndex, endIndex + 1)
    }

    const xLabel = []
    for (let i = startIndex; i <= endIndex; i++) {
      xLabel.push(i + '级')
    }
    let option = {
      title: {
        left: 'center',
        text: optionName,
      },
      toolbox: {
        right: 0,
        top: 25,
        feature: {
          saveAsImage: {
            title: '保存',
          },
        },
      },
      tooltip: {
        textStyle: {
          align: 'left',
        },
        trigger: 'axis',
        confine: true,
      },
      xAxis: {
        type: 'category',
        name: '风级',
        nameLocation: 'middle',
        nameGap: 22,
        data: xLabel,
      },
      yAxis: {
        name: '概率',
        type: 'value',
        axisLabel: {
          formatter: function(value, index) {
            return value.toFixed(1) + '%'
          },
        },
      },
      dataZoom: [
        {
          type: 'inside',
        },
      ],
      grid: [
        {
          top: '17%',
          left: '2%',
          right: '10%',
          bottom: '12%',
          containLabel: true,
        },
      ],
      series: [],
      legend: {
        type: 'scroll',
        data: [],
        x: 'center',
        y: 'bottom',
      },
    }
    //加入多行数据
    for (let i = 0; i < data.length; i++) {
      let barData = data[i].distribution.map(item => item.toFixed(1))
      option.legend.data.push(data[i].label)
      option.series.push({
        type: 'bar',
        barGap: 0,
        barCategoryGap: 0,
        name: data[i].label,
        data: barData,
      })
    }
    option && myChart.setOption(option)
    window.onresize = function() {
      myChart.resize()
    }
  },
  //风玫瑰图
  setROSEChart(chartName, data, unit) {
    let title = data.optionName
    data = data.windDirectionFreq.map(item => item * 100)
    let myChart = echarts.getInstanceByDom(
      document.getElementById(chartName),
    )
    if (myChart !== undefined) {
      myChart.dispose()
    }
    myChart = echarts.init(document.getElementById(chartName), myTheme.theme)
    //风向频率数组，按顺时针方向，上北下南左西右东，16方位。若要使线重合，请将下标为0的数值添加到末尾。
    let percentList = data
    //风向 16方位
    let namelist = ['北', '', '东北', '', '东', '', '东南',
      '', '南', '', '西南', '', '西', '', '西北', '']
    //数据
    const seriesData = []
    for (let i = 0; i <= namelist.length; i++) {
      var jiaodu = (360 / namelist.length) * i
      seriesData.push([Number(percentList[i]).toFixed(3), jiaodu]) //[径向，角度]
    }
    let option = {
      toolbox: {
        feature: {
          saveAsImage: {
            title: '保存',
          },
        },
      },
      backgroundColor: '#fff',
      title: {
        text: title,
        textStyle: {
          color: '#666',
        },
        left: 0,
      },
      angleAxis: {
        type: 'value',
        startAngle: 90,
        boundaryGap: false,
        min: 0, //最小刻度
        max: 360, //最大刻度
        interval: 360 / namelist.length, //间隔刻度 16方位间隔22.5，可改8方位
        axisLabel: {
          show: true,
          color: '#1c2383',
          fontWeight: 'bold',
          formatter: function(value, index) {
            return namelist[index]
          },
        },
        splitLine: {
          show: true,
          lineStyle: {
            color: '#9a9a9a',
            width: 1,
          },
        },
        axisLine: {
          show: true,
          lineStyle: {
            color: '#333',
            width: 3,
          },
        },
        axisTick: {
          alignWithLabel: true,
        },
        z: 10,
        //data: namelist,
      },
      radiusAxis: {
        z: 200,
        axisLabel: {
          show: true,
          color: '#000',
          fontWeight: 'bold',
          formatter: '{value}' + unit,
        },
        max: Math.max(...percentList).toFixed(2) + 5,
        splitLine: {
          show: true,
          lineStyle: {
            color: '#000',
            width: 1,
          },
        },
        axisLine: {
          show: true,
          lineStyle: {
            color: '#000000',
            width: 1,
          },
        },
      },
      polar: {},
      tooltip: {
        trigger: 'item',
        borderColor: '#12510e',
        borderWidth: 1,
        textStyle: {
          color: '#000000',
          fontWeight: 'bold',
          align: 'left',
        },
        axisPointer: {
          z: 300,
          type: 'cross',
          label: {
            color: 'white',
            backgroundColor: 'black',
          },
        },
        formatter: function(params) {
          //第一个和最后一个点保持一致，防止第一个点时找不见数据
          const dataIndex = params.dataIndex >= namelist.length ? 0 : params.dataIndex
          return namelist[dataIndex] + params.data[0]
        },
      },
      series: [
        {
          type: 'line',
          data: seriesData,
          coordinateSystem: 'polar',
          showBackground: true,
          backgroundStyle: {
            color: '#fff',
          },
          // color: ['#2e53ec'],
          color: ['#054b07'],
          itemStyle: {
            normal: {
              shadowBlur: 10,
              shadowOffsetX: 0,
              shadowColor: 'rgba(189, 203, 203, 0.5)',
            },
            emphasis: {
              shadowBlur: 10,
              shadowOffsetX: 0,
              shadowColor: 'rgba(105, 113, 113, 0.5)',
              opacity: 0.9,
            },
          },
          areaStyle: {
            color: 'green',
            opacity: 0.5,
          },
        },
      ],
    }


    option && myChart.setOption(option)
    //自适应大小
    window.onresize = function() {
      myChart.resize()
    }
  },
  //10分钟风速折线图
  setWINDSPEEDChart(chartName, data) {
    let myChart = echarts.getInstanceByDom(
      document.getElementById(chartName),
    )
    if (myChart !== undefined) {
      myChart.dispose()
    }
    myChart = echarts.init(document.getElementById(chartName), myTheme.theme)
    let optionName = data.optionName
    data = data.dataList
    // 数据处理 给值增加时间戳
    data.forEach((item, index) => {
      let tmpDotList0 = []
      for (let i = 0; i < item.result.originData.times.length; i++) {
        tmpDotList0.push([item.result.originData.times[i], item.result.originData.values[i].toFixed(item.result.accuracy >= 0 ? item.result.accuracy : 4)])
      }
      item.result.originData.dotList = tmpDotList0
    })
    let option = {
      title: {
        left: 'center',
        text: optionName,
      },
      toolbox: {
        right: 0,
        top: 25,
        feature: {
          saveAsImage: {
            title: '保存',
          },
        },
      },
      tooltip: {
        textStyle: {
          align: 'left',
        },
        trigger: 'axis',
        confine: true,
      },
      xAxis: {
        type: 'category',
        name: '时间',
        nameLocation: 'middle',
        nameGap: 22,
        axisLabel: {
          formatter: function(value, index) {
            return value.substring(0, value.length - 12)
          },
          fontSize: 11,
        },
      },
      yAxis: [],
      dataZoom: [
        {
          type: 'inside',
        },
      ],
      grid: [
        {
          top: '17%',
          left: '2%',
          right: '10%',
          bottom: '12%',
          containLabel: true,
        },
      ],
      series: [],
      legend: {
        type: 'scroll',
        data: [],
        x: 'center',
        y: 'bottom',
        selected: {},
      },
    }
    //加入多行数据
    let finalMax = 0
    let finalMin = 0
    for (let i = 0; i < data.length; i++) {
      option.legend.data.push(data[i].label)
      option.series.push({
        type: 'line',
        name: data[i].label,
        showSymbol: false,
        data: data[i].result.originData.dotList,
      })

      //计算最大值与最小值
      let minValue = getMin(data[i].result.originData.values) // 输出最小值
      let maxValue = getMax(data[i].result.originData.values) // 输出最大值
      // 一些特殊情况处理
      if (maxValue === 0 && minValue === 0) {
        maxValue = 1
        minValue = -1
      } else {
        let delta = Math.ceil((maxValue - minValue) * 0.2 * (10 ** data[i].result.accuracy)) / (10 ** data[i].result.accuracy)
        if (delta === 0) {
          delta = 1
        }
        let midValue = Math.ceil((maxValue + minValue) / 2 * (10 ** data[i].result.accuracy)) / (10 ** data[i].result.accuracy)
        maxValue = midValue + 3 * delta
        minValue = midValue - 3 * delta
      }
      // 外层循环最大最小值更新
      if (i === 0) {
        finalMax = maxValue
        finalMin = minValue
      } else {
        if (finalMax < maxValue) {
          finalMax = maxValue
        }
        if (finalMin > minValue) {
          finalMin = minValue
        }
      }
      // 一些预警线
      if (data[i].result.limitInfo) {
        Object.keys(data[i].result.limitInfo).forEach((key) => {
          option.series.push({
            type: 'line',
            name: data[i].label + key,
            showSymbol: false,
            animation: false,
            markLine: {
              symbol: 'none',
              data: [
                {
                  yAxis: data[i].result.limitInfo[key],
                  lineStyle: {
                    type: 'solid',
                  },
                  label: {
                    show: true,
                    position: 'insideEndTop',
                  },
                },
              ],
            },
          })
          option.legend.data.push(data[i].label + key)
        })
      }
    }
    // 一些特殊情况处理
    if (finalMax === 0 && finalMin === 0) {
      finalMax = 1
      finalMin = -1
    } else {
      let delta = Math.ceil((finalMax - finalMin) * 0.2 * (10 ** data[0].result.accuracy)) / (10 ** data[0].result.accuracy)
      if (delta === 0) {
        delta = 1
      }
      let midValue = Math.ceil((finalMax + finalMin) / 2 * (10 ** data[0].result.accuracy)) / (10 ** data[0].result.accuracy)
      finalMax = midValue + 3 * delta
      finalMin = midValue - 3 * delta
    }
    if (finalMin < 0) {
      finalMin = 0
      finalMax = Math.ceil(finalMax / 6) * 6
    }
    option.yAxis.push({
      name: '风速/(m/s)',
      nameTextStyle: {
        fontWeight: 'bold',
        fontSize: 12,
      },
      axisLabel: {
        formatter: function(value) {
          return value.toFixed(data[0].result.accuracy >= 0 ? data[0].result.accuracy : 4) // 2表示小数为2位
        },
      },
      splitNumber: 6,
      min: finalMin,
      max: finalMax,
      interval: (finalMax - finalMin) / 6, // 标轴分割间隔
    })
    option && myChart.setOption(option)
    window.onresize = function() {
      myChart.resize()
    }
  },
}
