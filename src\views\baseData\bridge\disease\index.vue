<template>
  <PageContainer>
    <template slot="search">
      <div style="display: flex; align-items: center; margin: 0">
        <CascadeSelection
          :form-data="queryParams"
          v-model="queryParams"
          types="201"
          multiple
        />
        <div style="margin-left: 20px">
          <el-input
            v-model="queryParams.bridgeName"
            style="width:100%;"
            placeholder="桥梁名称"
            clearable
          />
        </div>
        <div style="margin-left: 20px">
          <el-input
            v-model="queryParams.bridgeCode"
            style="width:100%"
            placeholder="桥梁编码"
            clearable
          />
        </div>
        <div style="margin: 0 20px">
          <el-date-picker
            style="width: 100%"
            v-model="queryParams.checkYear"
            type="year"
            placeholder="年份"
            value-format="yyyy"
          />
        </div>
        <div style="min-width:240px;">
          <el-button
            v-hasPermi="['baseData:bridgeDiseases:listPage']"
            type="primary"
            icon="el-icon-search"
            @click="handleQuery"
          >搜索</el-button>
          <el-button
            icon="el-icon-refresh"
            @click="resetQuery"
          >重置</el-button>
        </div>
      </div>
    </template>
    <template slot="header">
      <div class="button-list">
        <el-button
          v-hasPermi="['baseData:import:execute']"
          class="mb8"
          type="primary"
          @click="importUpdate"
        >导入更新</el-button>
        <el-button
          v-hasPermi="['baseData:import:execute']"
          class="mb8"
          type="primary"
          @click="importAdd"
        >导入新增</el-button>
        <el-button
          v-hasPermi="['baseData:bridgeDiseases:export']"
          type="primary"
          @click="handleExport"
        >数据导出</el-button>
      </div>
    </template>
    <template slot="body">
      <el-table
        v-adjust-table
        ref="table"
        v-loading="loading"
        height="99%"
        border
        :data="tableData"
        :header-cell-style="{'height': '36px'}"
        :row-style="rowStyle"
        @selection-change="handleSelectionChange"
        @row-click="handleRowClick"
      >
        <el-table-column
          fixed
          type="selection"
          width="50"
          align="center"
        />
        <el-table-column
          fixed
          label="序号"
          type="index"
          width="50"
          align="center"
        >
          <template v-slot="scope">
            {{ (scope.$index + (queryParams.pageNum - 1) * queryParams.pageSize)+1 }}
          </template>
        </el-table-column>
        <el-table-column
          fixed
          label="桥梁名称"
          align="center"
          prop="bridgeName"
          min-width="140"
          show-overflow-tooltip
        />
        <el-table-column
          label="桥梁编码"
          align="center"
          prop="bridgeCode"
          min-width="140"
          show-overflow-tooltip
        />
        <el-table-column
          label="年度"
          align="center"
          prop="checkYear"
          min-width="140"
          show-overflow-tooltip
        />

        <el-table-column
          label="管养处名称"
          align="center"
          prop="managementMaintenanceName"
          min-width="140"
          show-overflow-tooltip
        />
        <el-table-column
          label="管养分处名称"
          align="center"
          prop="managementMaintenanceBranchName"
          min-width="140"
          show-overflow-tooltip
        />
        <el-table-column
          label="养护路段名称"
          align="center"
          prop="maintenanceSectionName"
          min-width="140"
          show-overflow-tooltip
        />
        <el-table-column
          label="路线编码"
          align="center"
          prop="routeCode"
          min-width="140"
          show-overflow-tooltip
        />
        <el-table-column
          label="路线名称"
          align="center"
          prop="routeName"
          min-width="140"
          show-overflow-tooltip
        />

        <!-- <el-table-column
          label="桥梁桩号"
          align="center"
          prop="centerStake"
          min-width="120"
          show-overflow-tooltip
        >
          <template slot-scope="scope">
            {{ formatPile(scope.row.centerStake) }}
          </template>
        </el-table-column> -->

        <el-table-column
          label="定期检查日期"
          align="center"
          prop="inspectDate"
          min-width="120"
          show-overflow-tooltip
        />



        <el-table-column
          label="部件名称"
          align="center"
          prop="componentName"
          min-width="120"
          show-overflow-tooltip
        />
        <el-table-column
          label="构件编码"
          align="center"
          prop="componentCode"
          min-width="120"
          show-overflow-tooltip
        />
        <el-table-column
          label="病害主类"
          align="center"
          prop="mainCategoriesDiseases"
          min-width="120"
          show-overflow-tooltip
        />
        <el-table-column
          label="病害类型"
          align="center"
          prop="diseaseType"
          min-width="120"
          show-overflow-tooltip
        />
        <el-table-column
          label="病害描述"
          align="center"
          prop="diseaseDescription"
          min-width="300"
          show-overflow-tooltip
        />
        <el-table-column
          label="长度(m)"
          align="center"
          prop="length"
          min-width="120"
          show-overflow-tooltip
        />
        <el-table-column
          label="面积(㎡)"
          align="center"
          prop="area"
          min-width="120"
          show-overflow-tooltip
        />
        <el-table-column
          label="裂缝宽度(mm)"
          align="center"
          prop="crackWidth"
          min-width="120"
          show-overflow-tooltip
        />
        <el-table-column
          label="病害标度"
          align="center"
          prop="diseaseScale"
          min-width="120"
          show-overflow-tooltip
        />
        <el-table-column
          label="备注"
          align="center"
          prop="remark"
          min-width="200"
          show-overflow-tooltip
        />
      </el-table>
      <pagination
        :total="total"
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
        :pageSizes="[10, 20, 30, 50, 100, 1000]"
        @pagination="getList"
      />
    </template>
    <ImportData
      v-if="showImportAdd"
      :is-update="isUpdate"
      :dialog-visible="showImportAdd"
      :import-base-type="importBaseType"
      :import-type="importType"
      @close="closeImportAdd"
    />
  </PageContainer>
</template>

<script>
import CascadeSelection from '@/components/CascadeSelection/index.vue'
import ImportData from '@/views/baseData/components/importData/index.vue'
import { getListPage } from '@/api/baseData/bridge/disease/index'

export default {
  name: 'Disease',
  props: {},
  components: { CascadeSelection, ImportData },
  data() {
    return {
      loading: false,
      queryParams: { pageNum: 1, pageSize: 20, checkYear:new Date().getFullYear()+''},
      total: 0,
      tableData: [],
      showImport: false,
      ids: [],
      showImportAdd: false,
      isUpdate: false,
      importType: 1,
      importBaseType: '12'
    }
  },
  created() {
    this.getList()
  },
  methods: {
    getList() {
      this.loading = true
      getListPage(this.queryParams)
        .then(res => {
          if (res.rows) {
            res.rows.map(item => {
              if(item.inspectDate){
                let dateString = item.inspectDate;
                const year = dateString.substring(0, 4);
                const month = dateString.substring(4, 6);
                const day = dateString.substring(6, 8);
                item.inspectDate=year+'-'+month+'-'+day
              }
            })
            this.tableData = res.rows
            this.total = res.total
          }
        })
        .finally(() => {
          this.loading = false
        })
    },
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    resetQuery() {
      this.queryParams = {
        pageNum: 1,
        pageSize: 20,
        checkYear:new Date().getFullYear()+''
      }
      this.getList()
    },
    handleExport() {
      if (this.tableData.length === 0) return
      if (this.ids.length === 0) {
        this.$modal
          .confirm('即将导出所有表格数据，此过程可能花费时间较长，是否继续？')
          .then(() => {
            this.download(
              '/baseData/bridge/inspectDiseases/export',
              this.queryParams,
              `road_interflow_${new Date().getTime()}.xlsx`,
              {
                headers: { 'Content-Type': 'application/json;' },
                parameterType: 'body'
              }
            )
          })
          .catch(() => {})
      } else {
        this.$modal
          .confirm(`已选择${this.ids.length}条桥梁病害数据，确认导出？`)
          .then(() => {
            this.download(
              '/baseData/bridge/inspectDiseases/export',
              { ids: this.ids },
              `static_${new Date().getTime()}.xlsx`,
              {
                headers: { 'Content-Type': 'application/json;' },
                parameterType: 'body'
              }
            )
          })
          .catch(() => {})
      }
    },
    // 导入更新按钮
    importUpdate() {
      this.isUpdate = true
      this.showImportAdd = true
      this.importType = 1
    },
    // 导入新增按钮
    importAdd() {
      this.isUpdate = false
      this.showImportAdd = true
      this.importType = 2
    },
    closeImportAdd(v) {
      this.showImportAdd = false
      if (v) this.getList()
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
    },
    // 表格点击勾选
    handleRowClick(row) {
      row.isSelected = !row.isSelected
      this.$refs.table.toggleRowSelection(row)
    },
    // 勾选高亮
    rowStyle({ row, rowIndex }) {
      if (this.ids.includes(row.id)) {
        return { 'background-color': '#b7daff', color: '#333' }
      } else {
        return { 'background-color': '#fff', color: '#333' }
      }
    }
  },
  computed: {},
  watch: {}
}
</script>

<style lang="scss" scoped>
.button-list {
  border-radius: 4px;
  width: 100%;
  .el-button {
    margin-bottom: 10px;
    margin-right: 10px;
    margin-left: 0;
  }
}
</style>
