<template>
  <div>
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button icon="el-icon-plus" size="mini" type="primary" @click="handleAdd">
          新增
        </el-button>
        <el-button icon="el-icon-delete" size="mini" type="danger" @click="handleDelete">
          删除
        </el-button>
      </el-col>
    </el-row>
    <!-- 数据表格开始 -->
    <div class="tableDiv">
      <el-table
        v-adjust-table
        v-loading="loading"
        :data="dataList"
        :height="'calc(100vh - 260px)'"
        border
        size="mini"
        style="width: 100%"
        @selection-change="handleSelectionChange"
      >
        <el-table-column align="center" fixed="left" type="selection" width="55" :selectable="checkSelectable"></el-table-column>
        <el-table-column
          align="center"
          fixed
          label="序号"
          type="index"
          width="100"
        ></el-table-column>
        <template v-for="(column, index) in columns">
          <el-table-column
            v-if="column.visible"
            :key="index"
            :label="column.label"
            :prop="column.field"
            align="center"
            show-overflow-tooltip
          >
            <template slot-scope="scope">
              <dict-tag
                v-if="column.dict"
                :options="dict.type[column.dict]"
                :value="scope.row[column.field]"
              />
              <template v-else-if="column.slots">
                <RenderDom :index="index" :render="column.render" :row="scope.row" />
              </template>
              <span v-else-if="column.isTime">
								{{ parseTime(scope.row[column.field], '{y}-{m}-{d}') }}
							</span>
              <span v-else>{{ scope.row[column.field] }}</span>
            </template>
          </el-table-column>
        </template>
        <el-table-column align="center" fixed="right" label="操作" width="100">
          <template slot-scope="scope">
            <el-button icon="el-icon-edit" size="mini" type="text" :disabled="scope.row.isCustom == 0" @click="handleUpdate(scope.row)">修改
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      <pagination
        v-show="total > 0"
        :limit.sync="queryParams.pageSize"
        :page.sync="queryParams.pageNum"
        :total="total"
        @pagination="handleQuery"
      />
    </div>
    <!-- 数据表格结束 -->
    <el-dialog
      :title="dialogTitle"
      :visible.sync="dialogVisible"
      append-to-body
      destroy-on-close
      width="50%"
    >
      <el-form ref="elForm" :model="formData" :rules="rules" label-width="120px" size="medium">
        <el-row>
          <el-col :span="12">
            <el-form-item label="模式名称" prop="modeName">
              <el-input
                v-model="formData.modeName"
                placeholder="请输入模式名称"
                clearable
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="爆闪开关" prop="flashSw">
              <dict-select
                type="flash_open_type"
                style="width: 100%"
                placeholder="请选择爆闪开关"
                v-model="formData.flashSw"
              ></dict-select>
            </el-form-item>
          </el-col>
<!--          <el-col :span="12">-->
<!--            <el-form-item label="模式编码" prop="modeCode">-->
<!--              <el-input-->
<!--                v-model="formData.modeCode"-->
<!--                placeholder="请输入模式编码"-->
<!--                clearable-->
<!--                style="width: 100%"-->
<!--              />-->
<!--            </el-form-item>-->
<!--          </el-col>-->
        </el-row>

        <el-row>
          <el-col :span="12">
            <el-form-item label="亮度" prop="bright">
              <el-slider
                v-model="formData.bright"
                :min="0"
                :max="15"
                show-input
                style="width: 100%"
              />
            </el-form-item>
          </el-col>

          <el-col :span="12">
            <el-form-item label="显示文本" prop="textMode">
              <el-select
                v-model="formData.textMode"
                placeholder="请选择显示文本"
                clearable
                @change='handleChangeTextMode'
                style="width: 100%"
              >
                <el-option v-for="item in textModeList" :key="item.value" :label="item.label" :value="item.value">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="12" v-if="formData.textMode == '10'">
            <el-form-item label="自定义显示内容" prop="textContent">
              <el-input
                v-model="formData.textContent"
                placeholder="请输入自定义显示内容(最多4个字)"
                clearable
                :maxlength="4"
                show-word-limit
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="文字颜色" prop="textColor">
              <el-select
                v-model="formData.textColor"
                placeholder="请选择文字颜色"
                clearable
                style="width: 100%"
              >
                <el-option label="红色" :value="0" />
                <el-option label="绿色" :value="1" />
                <el-option label="黄色" :value="2" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="12">
            <el-form-item label="语音开关" prop="voiceSw">
              <dict-select
                type="flash_open_type"
                style="width: 100%"
                placeholder="请选择语音开关"
                v-model="formData.voiceSw"
              ></dict-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="播放内容" prop="voiceMode">
              <el-select placeholder="请选择交通标志" v-model="formData.voiceMode" style="width: 100%">
                <el-option v-for="item in textList" :key="item.value" :label="item.label" :value="item.value">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="12">
            <el-form-item label="语音音量" prop="voiceVolume">
              <el-slider
                v-model="formData.voiceVolume"
                :min="0"
                :max="15"
                show-input
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="语音次数" prop="voiceCount">
              <el-input-number
                v-model="formData.voiceCount"
                :min="0"
                placeholder="0表示循环播报"
                controls-position="right"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <div style="text-align: right; margin-top: 20px">
          <el-button size="mini" type="primary" @click="handleSave">保存</el-button>
          <el-button size="mini" @click="dialogVisible = false">退出</el-button>
        </div>
      </el-form>
    </el-dialog>
  </div>
</template>

<script>
import {
  deleteWdmConfig,
  getWdmConfigPage,
  saveWdmConfig,
  updateWdmConfig,
} from '@/api/jgjc/flashingModeConfig'

export default {
  dicts: ['flash_open_type'],
  data() {
    return {
      // 遮罩层
      loading: false,
      dataList: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
      },
      // 列信息
      columns: [
        { key: 0, width: 100, field: 'modeName', label: `模式名称`, visible: true },
        { key: 1, width: 100, field: 'modeCode', label: `模式编码`, visible: true },
        { key: 2, width: 100, field: 'flashSw', label: `爆闪开关`, visible: true, dict: 'flash_open_type' },
        { key: 3, width: 100, field: 'bright', label: `亮度`, visible: true },
        { key: 4, width: 100, field: 'textContent', label: `显示文本`, visible: true },
        { key: 5, width: 100, field: 'textColorShow', label: `文字颜色`, visible: true },
        { key: 6, width: 100, field: 'voiceSw', label: `语音开关`, visible: true, dict: 'flash_open_type' },
        { key: 7, width: 100, field: 'voiceContent', label: `播放内容`, visible: true },
        { key: 8, width: 100, field: 'voiceVolume', label: `语音音量`, visible: true },
        { key: 9, width: 100, field: 'voiceCountShow', label: `语音次数`, visible: true }
      ],
      textModeList: [
        { label: "关", value: 0 },
        { label: "停", value: 1 },
        { label: "慢", value: 2 },
        { label: "雾", value: 3 },
        { label: "雨", value: 4 },
        { label: "冰", value: 5 },
        { label: "雪", value: 6 },
        { label: "风", value: 7 },
        { label: "堵", value: 8 },
        { label: "检", value: 9 },
        { label: "自定义", value: 10 }
      ],
      total: 0,
      ids: [],
      dialogVisible: false,
      dialogTitle: '新增',
      formData: {},
      textList: [
        { value: 1, label: '前方坍塌，禁止通行' },
        { value: 2, label: '前方事故，禁止通行' },
        { value: 3, label: '前方事故，减速慢行' },
        { value: 4, label: '前方施工，减速慢行' },
        { value: 5, label: '雨天路滑，谨慎驾驶' },
        { value: 6, label: '雨雪天气，谨慎驾驶' },
        { value: 7, label: '大雾天气，谨慎驾驶' },
        { value: 8, label: '请减速慢行' }
      ]
    }
  },
  computed: {
    rules() {
      return {
        modeName: [
          { required: true, message: '模式名称不能为空', trigger: 'blur' }
        ],
        modeCode: [
          { required: true, message: '模式编码不能为空', trigger: 'blur' }
        ],
        textMode: [
          { required: true, message: '请选择显示文本', trigger: 'change' }
        ],
        flashSw: [
          { required: true, message: '请选择爆闪开关状态', trigger: 'change' }
        ],
        bright: [
          { required: true, message: '请设置亮度值', trigger: 'change' }
        ],
        textContent: [
          { required: true, message: '显示文本不能为空', trigger: 'blur' },
          { max: 4, message: '最多只能输入4个字', trigger: 'blur' }
        ],
        textColor: [
          { required: true, message: '请选择文字颜色', trigger: 'change' }
        ],
        voiceSw: [
          { required: true, message: '请选择语音开关状态', trigger: 'change' }
        ],
      }
    },
  },
  mounted() {
    this.handleQuery()
  },
  methods: {
    handleQuery() {
      this.loading = true
      getWdmConfigPage(this.queryParams)
        .then((res) => {
          this.dataList = res.rows
          this.dataList.forEach(item => {
            item.textColorShow = item.textColor == 0 ? '红色' : item.textColor == 1 ? '绿色' : '黄色'
            item.voiceCountShow = item.voiceCount == 0 ? '循环播放' : item.voiceCount
            if (item.voiceMode == 0) {
              item.voiceMode = null
            }
          })
          this.total = res.total
          this.loading = false
        })
        .catch((err) => {
          this.loading = false
          console.error(err)
        })
    },
    handleSelectionChange(e) {
      this.ids = e.map((item) => item.id)
    },

    handleDelete() {
      if (this.ids.length === 0) {
        this.$message({
          message: '请选择需要删除的数据',
          type: 'warning',
        })
        return
      }
      this.$confirm('是否确认删除选中数据?', '警告', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }).then(() => {
        deleteWdmConfig({ ids: this.ids }).then((res) => {
          this.$message({
            message: '删除成功',
            type: 'success',
          })
          this.handleQuery()
        })
      })
    },
    handleAdd() {
      this.dialogVisible = true
      this.dialogTitle = '新增'
      this.formData = {}
    },
    handleUpdate(e) {
      this.dialogVisible = true
      this.dialogTitle = '修改'
      this.formData = JSON.parse(JSON.stringify(e))
    },
    handleSave() {
      this.$refs.elForm.validate((valid) => {
        if (!valid) return
        if (this.formData.voiceSw != '1') {
          this.formData.voiceMode = 0
          this.formData.voiceContent = ''
        }
        if (this.formData.voiceMode) {
          this.formData.voiceContent = this.textList.find(item => item.value == this.formData.voiceMode).label
        }
        if (this.dialogTitle === '新增') {
          saveWdmConfig(this.formData).then((res) => {
            this.$message({
              message: '新增成功',
              type: 'success',
            })
            this.handleQuery()
            this.closeDialog()
          })
        } else {
          updateWdmConfig(this.formData).then((res) => {
            this.$message({
              message: '修改成功',
              type: 'success',
            })
            this.handleQuery()
            this.closeDialog()
          })
        }
      })
    },
    checkSelectable(row) {
      return row.isCustom != 0
    },
    handleChangeTextMode(e) {
      const textMode = this.textModeList.find((item) => item.value == e)
      if (textMode.value != 10) {
        this.formData.textContent = textMode.label
      }
      if (textMode.value == 0) {
        this.formData.textContent = ''
      }
    },
    closeDialog() {
      this.dialogVisible = false
      this.$refs.elForm.resetFields()
    },
  },
}
</script>

<style lang="scss" scoped></style>
