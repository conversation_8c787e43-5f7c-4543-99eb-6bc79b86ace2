<template>
  <div class="tableDiv">
    <el-table v-adjust-table v-loading="loading" :data="dataList" height="100%" size="mini" style="width: 100%"
      :row-key="getRowKeys">
      <el-table-column type="expand">
        <template slot-scope="scope">
          <el-table v-adjust-table :data="scope.row.childDevices" :row-key="getRowKeys">
            <el-table-column label="子设备编码" prop="name" width="150" />
            <el-table-column label="子设备状态" prop="status">
              <template slot-scope="scope">
                <el-tag :type="scope.row.status === 1 ? 'success' : 'danger'">{{ scope.row.status === 1 ? '在线' : '离线'
                }}</el-tag>
              </template>
            </el-table-column>
            <el-table-column fixed="right" label="操作" width="150" align="center">
              <template slot-scope="scope">
                <el-button size="mini" type="text" @click="handleRestart(scope.row)">重启</el-button>
              </template>
            </el-table-column>
          </el-table>
        </template>
      </el-table-column>
      <template v-for="(column, index) in columns">
        <el-table-column v-if="column.visible" :key="index" :label="column.label" :prop="column.field" align="center"
          show-overflow-tooltip>
          <template slot-scope="scope">
            <dict-tag v-if="column.dict" :options="dict.type[column.dict]" :value="scope.row[column.field]" />
            <template v-else-if="column.slots">
              <RenderDom :index="index" :render="column.render" :row="scope.row" />
            </template>
            <span v-else-if="column.isTime">
              {{ parseTime(scope.row[column.field], '{y}-{m}-{d}') }}
            </span>
            <span v-else>{{ scope.row[column.field] }}</span>
          </template>
        </el-table-column>
      </template>
      <el-table-column fixed="right" label="操作" width="150" align="center">
        <template slot-scope="scope">
          <el-button size="mini" type="text" @click="handleEnd(scope.row)">结束</el-button>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>

<script>
import {
  getZJGTStructDeviceStatus,
  getSansiStructDeviceStatus,
  getWdmStructDeviceStatus
} from "@/api/jgjc/earlyWarning/deviceModel";

import {
  startWarnPlan,
  endWarnPlan,
  getWdmStructFlashDeviceStatus,
  offSiteFlashWarning,
  setSingleFlashWarnDev
} from '@/api/Tibet/index'

export default {
  name: 'equipmentonlineStatus',
  components: {
    RenderDom: {
      functional: true,
      props: {
        row: Object,
        index: Number,
        render: Function,
      },
      render(createElement, ctx) {
        const { row, index } = ctx.props;
        return ctx.props.render(row, index);
      },
    }
  },
  props: {
    checkData: {
      type: Object,
      default: () => { }
    }
  },
  data() {
    return {
      loading: false,
      dataList: [],
      columns: [
        { key: 0, width: 100, field: 'deviceName', label: '名称', visible: true },
        { key: 1, width: 100, field: 'deviceCode', label: '设备编码', visible: true },
        {
          key: 4, width: 100, field: 'status', label: '设备状态', visible: true, slots: true, render: (row, index) => {
            if (row.status === '在线') {
              return <div style='color: #32da49'>{row.status}</div>
            } else {
              return <div style='color: #fb4545'>{row.status}</div>
            }
          }
        },
        {
          key: 5, width: 100, field: 'onlines', label: '子设备数', visible: true, slots: true, render: (row, index) => {
            return <div>{row.warnStatus?.onlines || '0'}</div>
          }
        },
        {
          key: 6, width: 100, field: 'warning', label: '预警设备号', visible: true, slots: true, render: (row, index) => {
            return <div>{row.warnStatus?.warning || '无预警'}</div>
          }
        },
        // {
        //   key: 6, width: 100, field: 'vno', label: '情报板', visible: true, slots: true, render: (row, index) => {
        //     const isActive = row.warnStatus?.warnState?.vno != '关闭';
        //     return (
        //       <div class={isActive ? 'blinking' : ''}>
        //         {row.warnStatus?.warnState?.vno || '-'}
        //       </div>
        //     )
        //   }
        // },
        // {
        //   key: 7, width: 100, field: 'vflash', label: '爆闪灯', visible: true, slots: true, render: (row, index) => {
        //     const isActive = row.warnStatus?.warnState?.vflash != '关闭';
        //     return (
        //       <div class={isActive ? 'blinking' : ''}>
        //         {row.warnStatus?.warnState?.vflash || '-'}
        //       </div>
        //     )
        //   }
        // },
        {
          key: 7, width: 100, field: 'vflash', label: '预警状态', visible: true, slots: true, render: (row, index) => {
            const isActive = row.warnStatus?.warnState?.vflash == '开';
            return (
              <div class={isActive ? 'blinking' : ''}>
                {row.warnStatus?.warnState?.vflash || '-'}
              </div>
            )
          }
        },
      ],
      timer: null, // 轮询定时器
      pollingInterval: 5000, // 5秒轮询一次
    }
  },
  mounted() {
    this.$nextTick(() => {
      this.initPolling();
    })
  },
  beforeDestroy() {
    this.clearPolling();
  },
  methods: {
    // 初始化轮询
    initPolling() {
      this.fetchData(); // 立即获取一次数据
      this.timer = setInterval(() => {
        this.fetchData();
      }, this.pollingInterval);
    },

    // 清除轮询
    clearPolling() {
      if (this.timer) {
        clearInterval(this.timer);
        this.timer = null;
      }
    },

    // 获取数据
    fetchData() {
      let data = JSON.parse(localStorage.getItem('mapData'));
      this.getEquipmentonlineStatus(data);
    },

    // 重启轮询（如果需要）
    restartPolling() {
      this.clearPolling();
      this.initPolling();
    },

    handleStart(row) {
      startWarnPlan({}).then(res => {
        this.$message.success('启动成功');
        this.fetchData(); // 操作成功后立即刷新数据
      }).catch(err => {
        this.$message.error('启动失败');
      });
    },

    handleEnd(row) {
      offSiteFlashWarning({ siteTriggerCommonCode: row.deviceCode }).then(res => {
        this.$message.success('结束成功');
        this.fetchData(); // 操作成功后立即刷新数据
      }).catch(err => {
        this.$message.error('结束失败');
      });
    },

    handleOffSite(row) {
      offSiteFlashWarning({}).then(res => {
        this.$message.success('解除成功');
        this.fetchData(); // 操作成功后立即刷新数据
      }).catch(err => {
        this.$message.error('解除失败');
      });
    },

    async getEquipmentonlineStatus(data) {
      if (!data) return;
      // this.loading = true;
      try {
        if (data.flashType == '维的美') {
          const res = await getWdmStructFlashDeviceStatus({ structId: data.id });
          this.dataList = res.data;
          if (this.dataList.length) {
            this.dataList.forEach(element => {
              element.childDevices = []
              /* 数组第一个开始，位数1-3代表停字牌，8代表断路器，4 5 6 7 9 10 11代表爆闪灯；数值0代表离线，数组1代表在线。 */
              if (element?.warnStatus?.sonOnline?.length > 0) {
                element.warnStatus.sonOnline.forEach((item, index) => {
                  if ([0, 1, 2].includes(index)) {
                    element.childDevices.push({
                      name: `${index + 1}停字牌`,
                      deviceCode: index,
                      status: item,
                      centralControlCode: element.deviceCode,
                    })
                  }
                  if ([7].includes(index)) {
                    element.childDevices.push({
                      name: `${index + 1}断路器`,
                      deviceCode: index,
                      status: item,
                      centralControlCode: element.deviceCode,
                    })
                  }
                  if ([3, 4, 5, 6, 8, 9, 10].includes(index)) {
                    element.childDevices.push({
                      name: `${index + 1}爆闪灯`,
                      deviceCode: index,
                      status: item,
                      centralControlCode: element.deviceCode,
                    })
                  }
                })
              }
            });
          }
        }
      } catch (error) {
        console.error('获取设备状态失败:', error);
        this.$message.error('获取设备状态失败');
      } finally {
        // this.loading = false;
      }
    },
    handleRestart(row) {
      setSingleFlashWarnDev({
        centralControlCode: row.centralControlCode,
        deviceCode: row.deviceCode,
        mode: 2, // mode:0关闭| mode:1开启|mode:2重启
      }).then(res => {
        this.$message.success('重启成功');
        this.fetchData(); // 操作成功后立即刷新数据
      }).catch(err => {
        this.$message.error('重启失败');
      });
    },
    getRowKeys(row) {
      return row.deviceCode;
    },
  }
}
</script>

<style lang="scss" scoped>
@import "./index.scss";

/* 闪烁动画效果 */
@keyframes blink {

  /* 开始状态：红色背景，白色文字，完全不透明 */
  0% {
    opacity: 1;
    background-color: #ffc107;
    color: white;
  }

  /* 中间状态：半透明 */
  50% {
    opacity: 0.5;
    background-color: #ff0000;
    color: white;
  }

  /* 结束状态：恢复完全不透明 */
  100% {
    opacity: 1;
    background-color: #ff0000;
    color: white;
  }
}

.blinking {
  animation: blink 1s linear infinite;
  padding: 2px 8px;
  border-radius: 4px;
  display: inline-block;
}

.tableDiv {
  width: 100%;
}

/* 可根据需要调整动画速度或颜色 */
.fast-blink {
  animation-duration: 0.5s;
}

.slow-blink {
  animation-duration: 2s;
}

.blue-blink {
  background-color: #0066ff;
}
</style>