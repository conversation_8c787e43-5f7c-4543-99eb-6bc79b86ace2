<template>
  <el-table
    ref="table"
    size="mini" v-bind="$attrs"
    style="width: 100%"
    @selection-change="getSelect"
    :height="'calc(100vh - 342px)'"
    border
    :row-style="rowStyle"
    @row-click="handleRowClick"
  >
    <el-table-column type="selection" width="50" align="center"/>
    <el-table-column label="序号" type="index" width="50"/>
    <el-table-column :label="assetName" align="center" prop="assetName"/>
    <el-table-column :label="assetCode" align="center" prop="assetCode"/>
    <el-table-column label="日常巡查频率" align="center" prop="dayFrequency">
      <template v-slot="scope">
        <span>{{scope.row.dayFrequency?scope.row.dayFrequency+" 天/次":"未设置" }}</span>
      </template>
    </el-table-column>
    <el-table-column label="经常检查频率" align="center" prop="monthFrequency">
      <template v-slot="scope">
        <span>{{scope.row.monthFrequency?scope.row.monthFrequency+" 月/次":"未设置" }}</span>
      </template>
    </el-table-column>
    <el-table-column label="管养单位" align="center" prop="managementMaintenanceName"/>
    <el-table-column label="养护路段" align="center" prop="maintenanceSectionName"/>
    <el-table-column label="修改时间" align="center" prop="updateTime" width="180">
      <template v-slot="scope">
        <span>{{ parseTime(scope.row.updateTime, '{y}-{m}-{d} {h}:{m}:{s}') }}</span>
      </template>
    </el-table-column>
    <el-table-column
      label="操作"
      align="center"
      width="100"
      class-name="small-padding fixed-width"
    >
      <template v-slot="scope">
        <el-button
          size="mini"
          type="text"
          icon="el-icon-edit"
          @click="getUpdate(scope.row)"
          v-hasPermi="['patrol:frequencySettings:edit']"
        >修改
        </el-button>
      </template>
    </el-table-column>
  </el-table>
</template>

<script>
export default {
  name: "Tables",
  props: ['activeName','selectIds'],
  methods: {
    // 表格点击勾选
    handleRowClick(row) {
      row.isSelected = !row.isSelected
      this.$refs.table.toggleRowSelection(row)
    },
    // 勾选高亮
    rowStyle({ row, rowIndex }) {
      if (this.selectIds.includes(row.assetId)) {
        return { 'background-color': '#b7daff', color: '#333' }
      } else {
        return { 'background-color': '#fff', color: '#333' }
      }
    },
    getUpdate(row){
      this.$emit("getUpdate", row)
    },
    getSelect(selection){
      this.$emit("getSelect", selection)
    }
  },
  computed: {
    assetName() {
      if (this.activeName === "隧道机电") {
        return "隧道名称";
      } else {
        return this.activeName + "名称";
      }
    },
    assetCode() {
      if (this.activeName === "隧道机电") {
        return "隧道编码";
      } else {
        return this.activeName + "编码";
      }
    }
  }
}
</script>

<style scoped>

</style>
