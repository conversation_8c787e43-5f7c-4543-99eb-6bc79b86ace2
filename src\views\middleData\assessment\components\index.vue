<template>
  <div class="route-road">
    <el-select filterable v-model="selectValue" ref="elSelect" style="width: 100%" clearable v-bind="$attrs"
      @change="changeSelect">
      <el-option v-for="item in routeOptions" :key="item.maintenanceSectionId" :label="item.maintenanceSectionName"
        :value="item.maintenanceSectionId" />
    </el-select>
  </div>
</template>

<script>
import { listMaintenanceSectionAll } from "@/api/system/maintenanceSection";

export default {
  props: {
    value: {
      type: [String, Number],
      default: ''
    },
    deptId: {
      type: [Number, String],
      default: null
    },
    departmentIdList: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      routeOptions: [],
    };
  },
  computed: {
    selectValue: {
      get() {
        return this.value;
      },
      set(val) {
        this.$emit('input', val);
      }
    }
  },
  watch: {
    deptId: 'handleDependencyChange',
    departmentIdList: {
      handler: 'handleDependencyChange',
      deep: true
    }
  },
  async created() {
    await this.getOptions();
    await this.validateInitialValue();
  },
  methods: {
    // 获取选项数据
    async getOptions() {
      try {
        const res = await listMaintenanceSectionAll({
          departmentId: this.deptId,
          departmentIdList: this.departmentIdList
        });
        this.routeOptions = res.data;
      } finally {
      }
    },

    // 处理依赖项变化
    async handleDependencyChange() {
      await this.getOptions();
      await this.validateCurrentValue();
    },

    // 验证初始值
    async validateInitialValue() {
      if (!this.value) return;

      const isValid = this.routeOptions.some(
        item => item.maintenanceSectionId === this.value
      );

      if (!isValid) {
        console.warn('初始值无效，已自动清空');
        this.$emit('input', '');
      }
    },

    // 验证当前值
    async validateCurrentValue() {
      if (!this.value) return;

      const isValid = this.routeOptions.some(
        item => item.maintenanceSectionId === this.value
      );

      if (!isValid) {
        console.warn('检测到无效值，已自动清空');
        this.$emit('input', '');
      }
    },

    // 选择变更事件
    changeSelect(e) {
      let el = this.routeOptions.find(element => element.maintenanceSectionId == e);
      this.$emit('change', el);
    },

    // 获取选项标签
    getLabel(id) {
      const item = this.routeOptions.find(
        item => item.maintenanceSectionId === id
      );
      return item ? item.maintenanceSectionName : '';
    }
  }
};
</script>
