<template>
  <div class="stake-mark">
    <el-form :model="form" ref="form" :rules="rules" label-width="80px" :inline="false">
      <el-form-item label="管理处">
        <el-select v-model="form.managementMaintenanceIds" @change="handleDeptChange" @clear="handleDeptClear"
          placeholder="请选择" clearable filterable style="width: 100%;" multiple>
          <el-option v-for="item in deptOptions" :key="item.id" :label="item.label" :value="item.id" />
        </el-select>
      </el-form-item>
      <el-form-item label="养护路段">
        <el-select v-model="form.maintenanceSectionId" placeholder="请选择" clearable filterable style="width: 100%;">
          <el-option v-for="item in routeOptions" :key="item.maintenanceSectionId" :label="item.maintenanceSectionName"
            :value="item.maintenanceSectionId" />
        </el-select>
      </el-form-item>
      <el-form-item label="总重(吨)">
        <el-input v-model="totalWeight" placeholder="小于等于" clearable type="number" min="0" />
      </el-form-item>
      <el-form-item label="轴重(吨)">
        <el-input v-model="axleLoad" placeholder="小于等于" clearable type="number" min="0" />
      </el-form-item>
      <el-form-item label="轴距(米)">
        <el-input v-model="wheelbase" placeholder="大于等于" clearable type="number" min="0" />
      </el-form-item>
    </el-form>
    <div class="btn-list">
      <el-button type="primary" @click="onView" size="mini">查看</el-button>
      <el-button type="primary" @click="onQuit" size="mini" class="hollow-out">退出</el-button>
    </div>
  </div>
</template>

<script>
import { deptTreeSelect } from '@/api/tmpl'
import {
  getMaintenanceSectionListAll
} from '@/api/baseData/common/routeLine'
import { getListPage } from '@/api/baseData/facility/baseInfo/index'

import { addWidthFeature, removeLayer } from '@/views/map/components/common/mapFun';

import TollStationIcon from '@/assets/map/toll-station-icon.png'

export default {
  data() {
    return {
      form: {
        managementMaintenanceIds: [],
        maintenanceSectionId: '',
        pageNum: 1,
        pageSize: 10000,
        mainTypeId: 8,
        typeId: 26,
        paramsDTO: {
          precisionParams: {
            customParams: [],
          }
        },
      },
      totalWeight: null, // 总重
      wheelbase: null, // 轴距
      axleLoad: null, //轴重
      rules: {},
      deptOptions: [],
      routeOptions: [],
    }
  },
  created() {
    this.getDept();
    this.getRoutes();
  },
  methods: {
    // 获取管理处
    getDept() {
      deptTreeSelect({ types: 201 }).then(res => {
        if (res.code === 200) {
          this.deptOptions = res.data || [];
        }
      })
    },
    // 获取养护路段数据
    getRoutes(e) {
      getMaintenanceSectionListAll({ departmentIdList: e }).then(res => {
        if (res.code == 200) {
          this.routeOptions = res.data || []
        }
      })
    },
    // 管养出监听选择变化
    handleDeptChange(e) {
      if (e) {
        this.getRoutes(e);
      }
    },
    // 清空管养出
    handleDeptClear(e) {
      this.form.maintenanceSectionId = '';
      this.getRoutes();
    },
    // 查看
    onView() {
      // 移除上次
      removeLayer(window.mapLayer, 'tollStationLayer', 'name')
      removeLayer(window.mapLayer, 'tollStationLayer')
      let totalWeight = this.totalWeight - 0
      let wheelbase = this.wheelbase - 0
      let axleLoad = this.axleLoad - 0
      // 组装请求条件
      let params = {
        ...this.form,
        paramsDTO: {
          precisionParams: {
            customParams: [
              {
                "col": "base.total_weight",
                "compareSymbol": "&lt;=",
                "colValue": totalWeight ? totalWeight : null,
              },
              {
                "col": "base.wheelbase",
                "compareSymbol": "&gt;=",
                "colValue": wheelbase ? wheelbase : null,
              },
              {
                "col": "base.axle_load",
                "compareSymbol": "&lt;=",
                "colValue": axleLoad ? axleLoad : null,
              }
            ],
          }
        },
      }
      this.$modal.loading();
      getListPage(params).then(res => {
        if (res.code === 200 && res.rows) {
          let data = res.rows.map(row => {
            row.typeName = row.name || row.typeName || "收费站";
            row.listQueryConfig = {
              detlCondf: {
                path: './baseData/ancillary/baseInfo/commonForm.vue',
                type: 1,
              },
            }
            row.mainTypeId = 8
            row.items = {}
            return row;
          })
          addWidthFeature(window.mapLayer, data, 'tollStationLayer', TollStationIcon);
        }
      }).finally(() => {
        this.$modal.closeLoading();
      });
    },
    // 退出
    onQuit() {
      this.$emit('quit');
      removeLayer(window.mapLayer, 'tollStationLayer', 'name')
    },
  }
}
</script>

<style lang="scss" scoped>
.stake-mark {
  z-index: 9999;
  margin: 10px 0;
  color: #ffffff;

  ::v-deep .el-form {
    .el-form-item__label {
      color: #ffffff;
    }
  }

  ::v-deep .el-input {
    .el-input__inner {
      background-color: rgba(1, 102, 254, 0.2);
      border: 1px solid #0166FE;
      color: #ffffff;
    }

    .el-input__inner::placeholder {
      color: #BBBBBB;
    }

    .el-input-group__append {
      background-color: rgba(1, 102, 254, 0.2);
      border: 1px solid #0166FE;
      color: #ffffff;
      border-left: none;
      padding: 0 10px;
      cursor: pointer;
    }


  }

  .btn-list {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    margin-top: 30px;

    .hollow-out {
      background-color: rgba(1, 102, 254, 0.2);
      color: #ffffff;
    }
  }

  ::v-deep .el-select__tags {
    flex-wrap: nowrap;
    overflow: auto;
  }
}

input[type="number"] {
  background-color: rgba(1, 102, 254, 0.2);
}
</style>
