<template>
  <div class="app-container maindiv">
    <el-row :gutter="20">
      <el-col :span="24">
        <el-form ref="queryForm" :inline="true" :model="queryParams" size="mini" label-width="100px">
          <el-form-item label="路线编码" prop="routeCode">
            <el-input v-model="queryParams.routeCode" style="width: 230px;"/>
          </el-form-item>
          <el-form-item label="编号" prop="yearStr">
            <el-input v-model="queryParams.code" style="width: 230px;"/>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" size="mini" icon="el-icon-search" @click="handleQuery">查询</el-button>
            <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
          </el-form-item>
        </el-form>
      </el-col>
      <el-col :span="24">
        <el-table size="mini"
                  style="width: 100%"
                  v-loading="loading"
                  border
                  :data="tableData"
                  row-key="id"
                  ref="dataTable"
                  stripe
                  highlight-current-row
                  @row-click="handleClickRow"
                  @selection-change="handleSelectionChange"
                  :height="300">
          <el-table-column
              v-if="multipleChoice"
              type="selection"
              width="50">
          </el-table-column>
          <el-table-column
              label="序号"
              align="center"
              type="index"
              width="50"
          >
            <template v-slot="scope">
              {{ (scope.$index + (queryParams.pageNum - 1) * queryParams.pageSize)+1 }}
            </template>
          </el-table-column>
          <template v-for="(column,index) in columns">
            <el-table-column :label="column.label"
                             v-if="column.visible"
                             align="center"
                             :prop="column.field">
              <template slot-scope="scope">
                <dict-tag v-if="column.dict" :options="dict.type[column.dict]" :value="scope.row[column.field]"/>
                <template v-else-if="column.slots">
                  <RenderDom :row="scope.row" :index="index" :render="column.render"/>
                </template>
                <span v-else-if="column.isTime">{{ parseTime(scope.row[column.field], '{y}-{m}-{d}') }}</span>
                <span v-else>{{ scope.row[column.field] }}</span>
              </template>
            </el-table-column>
          </template>
        </el-table>
        <pagination
            v-show="total>0"
            :total="total"
            :page.sync="queryParams.pageNum"
            :limit.sync="queryParams.pageSize"
            @pagination="handleQuery"
        />
        <div v-if="multipleChoice" class="mt10" style="text-align: right">
          <el-button type="primary" @click="saveData">保存</el-button>
        </div>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import {listStatic as bridgeQuery} from "@/api/baseData/bridge/baseInfo";
import {listStatic as tunnelQuery} from "@/api/baseData/tunnel/baseInfo";

import {formatPile} from "@/utils/ruoyi";
import {culvertList} from "@/api/baseData/culvert/culvertInfo";
import {listMaintenanceSection, listMaintenanceSectionAll} from "@/api/system/maintenanceSection";
export default {
  data() {
    return {
      loading: false,
      queryParams: {
        pageNum: 1,
        pageSize: 50
      },
      total: 0,
      tableData: [],
      columns: [],
      selectDatas: []
    }
  },
  dicts: ['sys_culvert_type'],
  props: {
    assetType: {
      type: String,
      default: '31'
    },
    maiSecId: {
      type: String,
      default: ''
    },
    multipleChoice: {
      type: Boolean,
      default: false
    }
  },
  components: {
    RenderDom: {
      functional: true,
      props: {
        row: Object,
        index: Number,
        render: Function
      },
      render(createElement, ctx) {
        const {row, index} = ctx.props
        return ctx.props.render(row, index)
      }
    }
  },
  watch: {
    maiSecId: {
      handler(val) {
        if (val) {
          this.queryParams.maintenanceSectionId = val
          this.handleQuery()
        }
      },
      immediate: true
    }
  },
  mounted() {
    if (this.assetType == '31') {
      this.columns = [
        {key: 1, width: 100, field: 'maintenanceSectionName', label: `路段名称`, visible: true},
        {key: 2, width: 100, field: 'managementMaintenanceName', label: `管养单位`, visible: true},
        {key: 3, width: 100, field: 'routeCode', label: `路线编码`, visible: true},
        {key: 4, width: 100, field: 'centerStake', label: `中心桩号`, visible: true, slots: true, render: (row, index) => {
            return (
                <span>
                  {formatPile(row.centerStake)}</span>
            )
          }},
        {key: 5, width: 100, field: 'bridgeCode', label: `桥梁编码`, visible: true},
        {key: 6, width: 100, field: 'bridgeName', label: `桥梁名称`, visible: true},
      ]
    } else if (this.assetType == '32') {
      this.columns = [
        {key: 1, width: 100, field: 'maintenanceSectionName', label: `路段名称`, visible: true},
        {key: 2, width: 100, field: 'managementMaintenanceName', label: `管养单位`, visible: true},
        {key: 3, width: 100, field: 'routeCode', label: `路线编码`, visible: true},
        {key: 4, width: 100, field: 'centerStake', label: `中心桩号`, visible: true, slots: true, render: (row, index) => {
            return (
                <span>
                  {formatPile(row.centerStake)}</span>
            )
          }},
        {key: 5, width: 100, field: 'tunnelCode', label: `隧道编码`, visible: true},
        {key: 6, width: 100, field: 'tunnelName', label: `隧道名称`, visible: true},
      ]
    } else if (this.assetType == '33') {
      this.columns = [
        {key: 1, width: 100, field: 'maintenanceSectionName', label: `路段名称`, visible: true},
        {key: 2, width: 100, field: 'managementMaintenanceName', label: `管养单位`, visible: true},
        {key: 3, width: 100, field: 'routeCode', label: `路线编码`, visible: true},
        {key: 4, width: 100, field: 'centerStake', label: `中心桩号`, visible: true, slots: true, render: (row, index) => {
            return (
                <span>
                  {formatPile(row.centerStake)}</span>
            )
          }},
        {key: 5, width: 100, field: 'rampId', label: `匝道编码`, visible: true},
        {key: 6, width: 100, field: 'culvertCode', label: `涵洞编号`, visible: true},
        {key: 7, width: 100, field: 'culvertType', label: `涵洞类型`, visible: true, dict: 'sys_culvert_type'},
      ]
    } else {
      this.columns = [
        {key: 1, width: 100, field: 'maintenanceSectionName', label: `路段名称`, visible: true},
        {key: 2, width: 100, field: 'maintenanceSectionId', label: `路线编码`, visible: true},
        {key: 3, width: 100, field: 'unifiedMileagePileStart', label: `起点桩号`, visible: true, slots: true, render: (row, index) => {
            return (
              <span>
                  {formatPile(row.unifiedMileagePileStart)}</span>
            )
          }},
        {key: 4, width: 100, field: 'unifiedMileagePileEnd', label: `终点桩号`, visible: true, slots: true, render: (row, index) => {
            return (
              <span>
                  {formatPile(row.unifiedMileagePileEnd)}</span>
            )
          }},
        {key: 5, width: 100, field: 'lanes', label: `车道数`, visible: true},
      ]
    }
  },
  methods: {
    handleQuery() {
      this.loading = true
      if (this.assetType == '31') {
        this.queryParams.bridgeCode = this.queryParams.code
        bridgeQuery(this.queryParams).then(res => {
          this.tableData = res.rows
          this.total = res.total
          this.loading = false
        })
      } else if (this.assetType == '32') {
        this.queryParams.tunnelCode = this.queryParams.code
        tunnelQuery(this.queryParams).then(res => {
          this.tableData = res.rows
          this.total = res.total
          this.loading = false
        })
      } else if (this.assetType == '33') {
        this.queryParams.culvertCode = this.queryParams.code
        culvertList(this.queryParams).then(res => {
          this.tableData = res.rows
          this.total = res.total
          this.loading = false
        })
      } else {
        this.queryParams.maintenanceSectionId = this.queryParams.routeCode
        listMaintenanceSection(this.queryParams).then(res => {
          this.tableData = res.rows
          this.total = res.total
          this.loading = false
        })
      }
    },
    resetQuery() {
      this.queryParams = {
        pageNum: 1,
        pageSize: 50
      }
    },
    // 选中
    handleSelectionChange(e) {
      this.selectDatas = e
    },
    saveData() {
      const assetList = []
      for (let i = 0; i < this.selectDatas.length; i++) {
        const row = this.selectDatas[i]
        let asset = {}
        if (this.assetType == '31') {
          asset = {
            id: row.id,
            routeCode: row.routeCode,
            direction: row.direction,
            name: row.bridgeName,
            beginMile: row.startStake,
            endMile: row.endStake
          }
        } else if (this.assetType == '32') {
          asset = {
            id: row.id,
            routeCode: row.routeCode,
            direction: row.direction,
            name: row.tunnelName,
            beginMile: row.startStake,
            endMile: row.endStake
          }
        } else if (this.assetType == '33') {
          asset = {
            id: row.id,
            routeCode: row.routeCode,
            direction: row.direction,
            name: row.culvertCode
          }
        } else {
          asset = {
            id: row.id,
            routeCode: row.maintenanceSectionId,
            name: row.maintenanceSectionName,
            beginMile: row.unifiedMileagePileStart,
            endMile: row.unifiedMileagePileEnd
          }
        }
        assetList.push(asset)
      }
      this.$emit('checkAsset', assetList)
    },
    handleClickRow(row) {
      if (this.multipleChoice) {
        this.$nextTick(() => {
          row.isSelected = !row.isSelected;
          this.$refs.dataTable.toggleRowSelection(e);
        })
        return
      }
      let asset = {}
      if (this.assetType == '31') {
        asset = {
          id: row.id,
          routeCode: row.routeCode,
          name: row.bridgeName,
          startStake: row.startStake,
          endStake: row.endStake
        }
      } else if (this.assetType == '32') {
        asset = {
          id: row.id,
          routeCode: row.routeCode,
          name: row.tunnelName,
          startStake: row.startStake,
          endStake: row.endStake
        }
      } else if (this.assetType == '33') {
        asset = {
          id: row.id,
          routeCode: row.routeCode,
          name: row.culvertCode,
          startStake: row.startStake,
          endStake: row.endStake
        }
      } else {
        asset = {
          id: row.id,
          routeCode: row.maintenanceSectionId,
          name: row.maintenanceSectionName,
          beginMile: row.unifiedMileagePileStart,
          endMile: row.unifiedMileagePileEnd
        }
      }
      this.$emit('checkAsset', asset)
    }
  }
}
</script>

<style scoped lang="scss">

</style>
