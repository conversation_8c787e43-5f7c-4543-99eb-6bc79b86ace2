<template>
  <div class="container">
    <template v-if="isFormat">
      <el-input key="1" v-model.number="prefix" @input="decodePile(true)">
        <template slot="prepend">k</template>
      </el-input>
      <el-input key="2" v-model="suffix" @input="decodePile(true)">
        <template slot="prepend">+</template>
      </el-input>
    </template>
    <template v-else>
      <el-input key="3" v-bind="$attrs" v-on="$listeners"></el-input>
    </template>
    <i class="el-icon-sort" @click="isFormat=!isFormat"></i>

  </div>
</template>
<script>

export default {
  name: "PileInput",
  props: {
    format: {
      type: Boolean,
      default: false,
    }
  },
  data() {
    return {
      prefix: null,
      suffix: null,
      isFormat: null,
    }
  },
  watch: {
    suffix(n, o) {
      // 限制后缀输入位数
      if (n > 1000) {
        this.suffix = o
        return
      }
      this.decodePile()
    },
    '$attrs.value': {
      deep: true,
      immediate: true,
      handler(n, o) {

        console.log(n)
        if (n == null) {
          this.suffix = this.prefix = null
          return
        }

        if (n.toString().substr(-1) == '.') {
          return
        }

        const regex = /^\d+(\.\d+)?$/;
        if (!regex.test(n)) {
          console.log('不是数字')
          this.$attrs.value = o
          return
        }



        this.prefix = parseInt(this.$attrs.value / 1000)
        if (this.prefix < 1) {
          this.suffix = this.$attrs.value
        } else {
          this.suffix = this.$attrs.value.toString().substr(parseInt(this.$attrs.value / 1000).toString().length)
        }
      }
    },
  },
  mounted() {
    this.isFormat = this.format
  },
  methods: {
    decodePile(flag) {

      if (flag){
        if (this.suffix > 1000) {
          return
        }
        this.$attrs.value = this.prefix * 1000 + Number(this.suffix)
        this.$emit('input', this.$attrs.value)
      }

    }
  },
};
</script>
<style scoped>
.container {
  position: relative;
  display: flex;
  align-items: center;
}

i {
  position: absolute;
  right: 0;
  padding: 0 5px;
  cursor: pointer;
  opacity: 0.6;
}

i:hover {
  color: rgb(92, 182, 255);
}

i:active {
  color: unset;
}

::v-deep .el-input-group__prepend {
  background: rgba(245, 247, 250, 0.5);
  font-weight: 600;
  font-size: 1.25rem;
}
</style>
