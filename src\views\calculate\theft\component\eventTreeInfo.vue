<template>
  <div class="record-main">
    <el-descriptions :column="1" border>
      <el-descriptions-item
        :label="item.label"
        v-for="(item, index) in myTableData"
        :key="'myTable' + index"
      >{{ taskData[item.key] || "" }}</el-descriptions-item
      >
    </el-descriptions>

    <div class="right-steps">
      <el-steps v-if="stepsData && stepsData.length > 0" direction="vertical" :active="stepsData.length">
        <el-step v-for="(item, index) in stepsData" :key="'steps' + index">
          <template #icon>{{ stepsData.length - index }}</template>
          <template #title>
            <div :style="{color: item.direction ? 'none' : 'red'}">{{ item.nodeName }}
              <i v-if="item.direction" class="el-icon-circle-check"/>
              <i v-else class="el-icon-circle-close"/>
              &nbsp&nbsp&nbsp{{ item.endTime }}
            </div>
          </template>
          <template #description>
            <el-descriptions :column="1" :colon="false">
              <el-descriptions-item v-if="item.isProduction || item.isProduction  == 0" label="是否计算安全生产费">{{ item.isProduction == 0 ? '否' : '是'}}</el-descriptions-item>
              <el-descriptions-item v-if="item.isGuarantee || item.isGuarantee  == 0" label="是否计算安全保通费">{{ item.isGuarantee == 0 ? '否' : '是'}}</el-descriptions-item>
              <el-descriptions-item label="操作人">{{ item.assigneeName }}</el-descriptions-item>
              <el-descriptions-item label="备注">{{ item.comment }}</el-descriptions-item>
              <template v-if="item.annex && item.annex.length > 0">
                <el-descriptions-item label="附件">
                </el-descriptions-item>
                <el-descriptions-item>
                  <file-upload v-for="file in item.annex" v-model="file.fileId" :forView="true"></file-upload>
                </el-descriptions-item>
              </template>
              <template v-if="item.djList && item.djList.length > 0">
                <el-descriptions-item label="审核照片">
                </el-descriptions-item>
                <el-descriptions-item>
                  <file-upload v-for="file in item.djList" v-model="file.fileId" :forView="true"></file-upload>
                </el-descriptions-item>
              </template>
              <template v-if="item.sgqList && item.sgqList.length > 0">
                <el-descriptions-item label="施工前照片">
                </el-descriptions-item>
                <el-descriptions-item>
                  <file-upload v-for="file in item.sgqList" v-model="file.fileId" :forView="true"></file-upload>
                </el-descriptions-item>
              </template>
              <template v-if="item.sghList && item.sghList.length > 0">
                <el-descriptions-item label="施工后照片">
                </el-descriptions-item>
                <el-descriptions-item>
                  <file-upload v-for="file in item.sghList" v-model="file.fileId" :forView="true"></file-upload>
                </el-descriptions-item>
              </template>
              <template v-if="item.djzp && item.djzp.length > 0">
                <el-descriptions-item label="登记照片">
                </el-descriptions-item>
                <el-descriptions-item>
                  <file-upload v-for="file in item.djzp" v-model="file.fileId" :forView="true"></file-upload>
                </el-descriptions-item>
              </template>
              <el-descriptions-item v-if="item.methodList && item.methodList.length > 0" label="方法清单">
              </el-descriptions-item>
              <el-descriptions-item v-if="item.methodList && item.methodList.length > 0">
                <el-table v-adjust-table :data="item.methodList" border size="mini">
                  <el-table-column prop="schemeCode" label="子目号" align="center"/>
                  <el-table-column prop="schemeName" label="方法名" align="center"/>
                  <el-table-column prop="calcDesc" label="计算式" align="center"/>
                  <el-table-column prop="num" label="数量" align="center"/>
                  <el-table-column prop="remark" label="备注" align="center"/>
                </el-table>
              </el-descriptions-item>
            </el-descriptions>
          </template>
        </el-step>
      </el-steps>
      <el-empty style="width: 500px" v-else></el-empty>
    </div>
  </div>
</template>
<script>
import { EventBus } from '@/utils/eventBus';
import {getRecord} from "@/api/theft/construction";

export default {
  data() {
    return {
      myTableData: [
        {
          label: "项目名称",
          key: "projectName",
        },
        {
          label: "任务单名称",
          key: "constructionName",
        },
        {
          label: "任务单编码",
          key: "constructionCode",
        },
        {
          label: "路段名称",
          key: "maiSecName",
        },
        {
          label: "位置",
          key: "mileRange",
        },
        {
          label: "施工单位",
          key: "conDomainName",
        },
        {
          label: "施工合同",
          key: "conConName",
        },
        {
          label: "监理单位",
          key: "supDomainName",
        },
        {
          label: "监理合同",
          key: "supConName",
        },
        {
          label: "验收人员",
          key: "acceptancePerson",
        },
        {
          label: "工作内容",
          key: "settleContent",
        },
      ],
      stepsData: [],
      taskData: {}
    };
  },
  props: {
    row: {
      type: Object,
      default: {},
    },
  },
  watch: {
    row: {
      handler(val) {
        if (val.finishedId) {
          getRecord({ businessKey: val.finishedId }).then((res) => {
            this.stepsData = res.data || [];
            this.stepsData.forEach(item => {
              item.annex = item.fileList?.filter(item => item.isAnnex == 1) || []
              item.sgqList = item.fileList?.filter(item => item.registerType == 1) || []
              item.sghList = item.fileList?.filter(item => item.registerType == 2) || []
              item.djList = item.fileList?.filter(item => item.registerType == 5) || []
              item.djzp = item.fileList?.filter(item => item.registerType == 0) || []
            })
            this.taskData = this.row
          });
        }
      },
      immediate: true,
      deep: true,
    },
  },
  methods: {},
};
</script>
<style lang="scss" scoped>
.record-main {
  display: flex;
  .el-descriptions {
    width: 300px;
    margin-right: 20px;
  }
}
::v-deep .el-descriptions-item__container {
  width: 38vw;
  overflow-x: auto;
}
</style>
<style lang="scss" scoped>
@import "@/assets/styles/business.scss";
</style>
