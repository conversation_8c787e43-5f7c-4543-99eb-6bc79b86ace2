<template>

  <el-drawer v-bind="$attrs" v-on="$listeners">
    <div class="container">
      <el-descriptions title="病害简要信息" size="mini" border>
        <el-descriptions-item label="资产类型">
          <dict-tag :options="dict.type.sys_asset_type" :value="data.assetType"/>
        </el-descriptions-item>
        <el-descriptions-item label="资产名称">
          <dict-tag :options="dict.type.sys_asset_type" :value="data.assetName"/>
        </el-descriptions-item>
        <el-descriptions-item label="病害类型">
          <el-tag size="small">{{ data.diseaseName }}</el-tag>
        </el-descriptions-item>
<!--        <el-descriptions-item label="病害编码">{{ data.diseaseCode }}</el-descriptions-item>-->
      </el-descriptions>
      <div style="margin: 0 10%">
        <el-divider>病害处治措施</el-divider>
      </div>
      <!--操作按钮区开始-->
      <el-row :gutter="10" class="mb8">
        <el-col :span="1.5">
          <el-button
            type="primary"
            icon="el-icon-plus"
            size="mini"
            @click="handleAdd"
            v-hasPermi="['system:diseaseAdvices:add']"
          >新增
          </el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button
            type="success"
            icon="el-icon-edit"
            size="mini"
            :disabled="single"
            @click="handleUpdate"
            v-hasPermi="['system:diseaseAdvices:edit']"
          >修改
          </el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button
            type="danger"
            icon="el-icon-delete"
            size="mini"
            :disabled="multiple"
            @click="handleDelete"
            v-hasPermi="['system:diseaseAdvices:remove']"
          >删除
          </el-button>
        </el-col>
      </el-row>
      <!--操作按钮区结束-->
      <!--数据表格开始-->
      <el-table size="mini" height="calc(100vh - 310px)" style="width: 100%" v-loading="loading" border
                :data="diseaseAdvicesList" @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="50" align="center"/>
        <el-table-column fixed label="序号" type="index" width="50">
          <template v-slot="scope">
            {{ (scope.$index + (queryParams.pageNum - 1) * queryParams.pageSize)+1 }}
          </template>
        </el-table-column>
        <el-table-column label="措施编码" align="center" prop="code"/>
        <el-table-column label="措施名称" align="center" prop="name"/>
        <el-table-column label="排序" align="center" prop="sort" width="80"/>
        <el-table-column
          label="操作"
          fixed="right"
          align="center"
          width="160"
          class-name="small-padding fixed-width"
        >
          <template slot-scope="scope" v-if="scope.row.userId !== 1">
            <el-button
              size="mini"
              type="text"
              icon="el-icon-edit"
              @click="handleUpdate(scope.row)"
              v-hasPermi="['system:diseaseAdvices:edit']"
            >修改
            </el-button>
            <el-button
              size="mini"
              type="text"
              icon="el-icon-delete"
              @click="handleDelete(scope.row)"
              v-hasPermi="['system:diseaseAdvices:remove']"
            >删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <pagination
        v-show="total>0"
        :total="total"
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
        @pagination="getList"
      />
    </div>

    <!-- 添加或修改病害处治措施对话框 -->
<!--    <el-dialog :title="title" :visible.sync="open" width="400px" append-to-body :modal="false" v-dialog-drag>-->
    <el-dialog :title="title" :visible.sync="open" width="30%" append-to-body  >
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
          <el-form-item label="编码" prop="code">
            <el-input v-model="form.code" placeholder="请输入编码"/>
          </el-form-item>
          <el-form-item label="名称" prop="name">
            <el-input v-model="form.name" placeholder="请输入名称"/>
          </el-form-item>
          <el-form-item label="排序" prop="sort">
            <el-input v-model="form.sort" placeholder="请输入排序"/>
          </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </el-drawer>
</template>

<script>
import {getToken} from "@/utils/auth";
import {
  addDiseaseAdvices, delDiseaseAdvices,
  getDiseaseAdvices,
  listDiseaseAdvices,
  updateDiseaseAdvices
} from "@/api/patrol/diseaseAdvices";
import {delDiseases} from "@/api/patrol/diseases";

export default {
  name: "AdviceDetail",
  dicts: ['sys_asset_type'],
  props: {
    data: {
      type: Object,
      default: {}
    }
  },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: false,
      dictType: [],
      // 总条数
      total: 0,
      // 病害处治措施表格数据
      diseaseAdvicesList: null,
      // 弹出层标题
      title: "",
      // 部门树选项
      deptOptions: undefined,
      // 是否显示弹出层
      open: false,

      // 表单参数
      form: {},
      defaultProps: {
        children: "children",
        label: "label"
      },
      // 用户导入参数
      upload: {
        // 是否显示弹出层（用户导入）
        open: false,
        // 弹出层标题（用户导入）
        title: "",
        // 是否禁用上传
        isUploading: false,
        // 是否更新已经存在的用户数据
        updateSupport: 0,
        // 设置上传的请求头部
        headers: {Authorization: "Bearer " + getToken()},
        // 上传的地址
        url: process.env.VUE_APP_BASE_API + "/system/user/importData"
      },
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 50,
        diseaseId: null,
        code: null,
        name: null,
        sort: null
      },
      // 列信息
      columns: [
        {key: 0, label: `编码`, visible: true},
        {key: 1, label: `名称`, visible: true},
        {key: 2, label: `排序`, visible: true}
      ],
      // 表单校验
      rules: {
        code: [
          {required: true, message: "编码不能为空", trigger: "blur"}
        ],
        name: [
          {required: true, message: "名称不能为空", trigger: "blur"}
        ],


      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询用户列表 */
    getList() {
      this.loading = true;
      listDiseaseAdvices(this.queryParams).then(response => {
        this.diseaseAdvicesList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        diseaseId: this.data.id,
        code: null,
        name: null,
        sort: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dateRange = [];
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id);
      this.single = selection.length != 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加病害处治措施";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids;
      getDiseaseAdvices(id).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改病害处治措施";
      });

    },
    /** 提交按钮 */
    submitForm: function () {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.id != null) {
            updateDiseaseAdvices(this.form).then(response => {
              this.$message({message: '"修改成功"', customClass: 'margin-left-25'})
              this.open = false;
              this.getList();
            });
          } else {
            addDiseaseAdvices(this.form).then(response => {
              this.$message({message: '"新增成功"', customClass: 'margin-left-25'})
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const id = row.id || this.ids;

      if (row.id) {
        this.$confirm('是否确认删除病害处治措施"' +row.name + '"的数据项？', "系统提示", {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: "warning",
          customClass:'margin-left-50'
        }).then(function () {
          return delDiseaseAdvices(id);
        }).then(() => {
          this.getList();
          this.$message({message: '"删除成功"', customClass: 'margin-left-25'})
        }).catch(() => {
        })
      } else {
        if (this.ids.length > 0) {
          this.$modal.confirm('是否确认批量删除选中数据？').then( () => {
            let delArray = []
            this.ids.forEach(item => {
              delArray.push(delDiseaseAdvices(item))
            })
            Promise.all(delArray).then(() => {this.$modal.msgSuccess("删除成功");this.getList()})
          })
        }
      }


    },
  },
  watch: {
    data() {
      this.queryParams.diseaseId = this.data.id
      this.form.diseaseId = this.data.id
      this.getList()
    }
  },
};
</script>
<style scoped>
.container {
  margin-top: -20px;
  padding: 20px;
}
/*::v-deep .el-dialog{
  position: absolute;
  margin-left: 20%;
}*/

</style>

<style >
.margin-left-50{
  margin-left: 50%;
}
.margin-left-25{
  margin-left: 25%;
}
</style>
