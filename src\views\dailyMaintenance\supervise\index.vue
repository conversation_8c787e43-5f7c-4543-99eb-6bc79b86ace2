<template>
  <div class="app-container">
    <el-row :gutter="20">
      <!--筛选区开始-->
      <el-col :span="24" :xs="24">
        <el-row>
          <el-col :span="24">
            <el-form ref="queryForm" :inline="true" :model="queryParams" label-width="68px" size="small">
              <el-form-item>
                <selectTree
                    :key="'domainId'"
                    v-model="queryParams.domainId"
                    :deptType="100"
                    :deptTypeList="[1, 3, 4]" clearable
                    filterable
                    placeholder="督办单位"
                    style="width: 240px"
                />
              </el-form-item>
              <el-form-item>
                <selectTree
                    :key="'constructionUnit'"
                    v-model="queryParams.acceptDomainId"
                    :data-rule="false"
                    :dept-type="100"
                    placeholder="接收单位"
                    :expand-all="false"
                    clearable
                    filterable
                    style="width: 240px"
                />
              </el-form-item>
              <el-form-item>
                <RoadSection v-model="queryParams.maiSecId" :deptId="queryParams.domainId" placeholder="养护路段"
                             style="width: 240px"/>
              </el-form-item>
              <el-form-item>
                <dict-select v-model="queryParams.emgGrade" :type="'urgent_degree'" placeholder="紧急程度"
                             style="width: 240px"/>
              </el-form-item>
              <el-form-item>
                <el-input v-model="queryParams.name" placeholder="督办名称" style="width: 240px"></el-input>
              </el-form-item>
              <el-form-item>
                <el-button icon="el-icon-search" size="mini" type="primary" @click="handleQuery">搜索</el-button>
                <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
              </el-form-item>
            </el-form>
          </el-col>
        </el-row>
        <!--筛选区结束-->

        <!--操作按钮区开始-->
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button
                icon="el-icon-plus"
                size="mini"
                type="primary"
                v-has-menu-permi="['noticeSupervision:supervision:add']"
                v-if="type == 1"
                @click="handleOpenAdd"
            >新增
            </el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button
                icon="el-icon-download"
                size="mini"
                v-has-menu-permi="['noticeSupervision:construction:export', 'noticeSupervision:construction:exportViewList']"
                type="success"
                @click="exportList"
            >导出
            </el-button>
          </el-col>
          <right-toolbar :columns="columns" :showSearch.sync="showSearch" @queryTable="handleQuery"></right-toolbar>
        </el-row>
        <!--操作按钮区结束-->
        <!--数据表格开始-->
        <div class="tableDiv">
          <el-table v-adjust-table
              ref="dataTable"
              v-loading="loading"
              :data="tableData"
              :height="
                showSearch ? 'calc(100vh - 330px)' : 'calc(100vh - 270px)'
              "
              border
              highlight-current-row
              @row-click="handleClickRow"
              row-key="id"
              size="mini"
              stripe
              style="width: 100%"
          >
            <el-table-column
                align="center"
                label="序号"
                type="index"
                width="50"
            />
            <template v-for="(column,index) in columns">
              <el-table-column v-if="column.visible" show-overflow-tooltip
                               :label="column.label"
                               :prop="column.field"
                               align="center">
                <template slot-scope="scope">
                  <dict-tag v-if="column.dict" :options="dict.type[column.dict]" :value="scope.row[column.field]"/>
                  <template v-else-if="column.slots">
                    <RenderDom :index="index" :render="column.render" :row="scope.row"/>
                  </template>
                  <span v-else-if="column.isTime">{{ parseTime(scope.row[column.field], '{y}-{m}-{d}') }}</span>
                  <span v-else>{{ scope.row[column.field] }}</span>
                </template>
              </el-table-column>
            </template>
            <el-table-column
                align="center"
                class-name="small-padding fixed-width"
                fixed="right"
                label="操作"
                width="250"
            >
              <template slot-scope="scope">
                <template>
                  <el-button
                      icon="el-icon-edit"
                      size="mini"
                      type="text"
                      v-has-menu-permi="['noticeSupervision:supervision:edit']"
                      v-if="type == 1"
                      @click="handleEdit(scope.row)"
                  >编辑
                  </el-button>
                  <el-button
                      icon="el-icon-delete"
                      size="mini"
                      type="text"
                      v-has-menu-permi="['noticeSupervision:supervision:delete']"
                      v-if="type == 1"
                      @click="handleDelete(scope.row)"
                  >删除
                  </el-button>
                  <el-button
                      icon="el-icon-download"
                      size="mini"
                      type="text"
                      v-has-menu-permi="['noticeSupervision:construction:review']"
                      v-if="type == 1"
                      @click="handleReceive(scope.row)"
                  >下发
                  </el-button>
                  <el-button
                      icon="el-icon-download"
                      size="mini"
                      type="text"
                      v-has-menu-permi="['noticeSupervision:construction:review']"
                      v-if="type == 2"
                      @click="handleReceive(scope.row)"
                  >接收
                  </el-button>
                  <el-button
                      icon="el-icon-view"
                      size="mini"
                      type="text"
                      @click="handleView(scope.row)"
                  >查看
                  </el-button>
                  <el-button
                      icon="el-icon-view"
                      size="mini"
                      type="text"
                      v-has-menu-permi="['noticeSupervision:construction:reject']"
                      v-if="type == 2"
                      @click="handleReject(scope.row)"
                  >驳回
                  </el-button>
                </template>
              </template>
            </el-table-column>
          </el-table>
          <pagination
              v-show="total>0"
              :limit.sync="queryParams.pageSize"
              :page.sync="queryParams.pageNum"
              :total="total"
              @pagination="handleQuery"
          />
        </div>
        <!--数据表格结束-->
      </el-col>
    </el-row>
    <el-drawer :wrapperClosable="false" :title="detailTitle" :visible.sync="openDetail" size="70%" destroy-on-close>
      <detail @close="handleClose" :type="type" :read-only="readOnly" :row="row"/>
    </el-drawer>
  </div>
</template>

<script>
import RoadSection from "@/views/baseData/components/roadSection/index.vue";
import selectTree from "@/components/DeptTmpl/selectTree.vue";
import Detail from "./detail.vue";
import {
  findSupervisionIssuanceList,
  findSupervisionList,
  issuanceMethod, rejectSupervision,
  removeSupervision
} from "@/api/dailyMaintenance/supervise";

export default {
  name: 'SuperviseView',
  components: {Detail, selectTree, RoadSection},
  dicts: ['urgent_degree', 'supervision_status'],
  data() {
    return {
      showSearch: false,
      queryParams: {
        pageNum: 1,
        pageSize: 50
      },
      total: 0,
      loading: false,
      columns: [
        {key: 0, width: 100, field: 'stage', label: `状态`, visible: true, dict: 'supervision_status'},
        {key: 1, width: 100, field: 'maiSecName', label: `养护路段`, visible: true},
        {key: 2, width: 200, field: 'name', label: `督办名称`, visible: true},
        {key: 3, width: 300, field: 'content', label: `督办事项`, visible: true},
        {key: 4, width: 200, field: 'domainName', label: `督办单位`, visible: true},
        {key: 5, width: 200, field: 'acceptDomainName', label: `接收单位`, visible: true},
        {key: 6, width: 100, field: 'emgGrade', label: `紧急程度`, visible: true, dict: 'urgent_degree'},
      ],
      tableData: [],
      openDetail: false,
      detailTitle: '新增督办信息',
      row: {},
      clickRow: {},
      readOnly: false,
      type: 1,
    }
  },
  created() {
    this.type = this.$route.query.type
    if (this.type == '2') {
      this.columns.push({key: 7, width: 200, field: 'issuePersonName', label: `督办人`, visible: true},
                        {key: 8, width: 100, field: 'issueDate', label: `下发日期`, visible: true})
    }
    if (this.type == '3') {
      this.columns.push({key: 7, width: 200, field: 'issuePersonName', label: `督办人`, visible: true},
                        {key: 8, width: 100, field: 'issueDate', label: `下发日期`, visible: true},
                        {key: 9, width: 200, field: 'acceptPersonName', label: `接收人`, visible: true},
                        {key: 10, width: 100, field: 'acceptDate', label: `接收日期`, visible: true})
    }
    this.handleQuery()
  },
  methods: {
    handleQuery() {
      this.loading = true
      if (this.type != '3') {
        this.queryParams.stage = parseInt(this.type)
        findSupervisionIssuanceList(this.queryParams).then(response => {
          if (response.code == 200) {
            this.loading = false
            this.tableData = response.rows
            this.total = response.total
          }
        })
      } else {
        findSupervisionList(this.queryParams).then(response => {
          if (response.code == 200) {
            this.loading = false
            this.tableData = response.rows
            this.total = response.total
          }
        })
      }
    },
    handleEdit(row) {
      this.row = row
      this.detailTitle = '修改督办信息'
      this.openDetail = true
      this.readOnly = false
    },
    handleDelete(row) {
      this.$confirm('是否确认删除督办信息?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        removeSupervision(row.id).then(response => {
          if (response.code == 200) {
            this.$message.success('删除成功')
            this.handleQuery()
          }
        })
      })
    },
    handleClickRow(e) {
      this.clickRow = e
    },
    handleOpenAdd() {
      this.row = {}
      this.detailTitle = '新增督办信息'
      this.openDetail = true
      this.readOnly = false
    },
    handleView(row) {
      this.row = row
      this.detailTitle = '督办详情'
      this.openDetail = true
      this.readOnly = true
    },
    handleReceive(row) {
      this.row = row
      this.detailTitle = '接收督办信息'
      this.openDetail = true
      this.readOnly = true
    },
    handleReject(row) {
      this.loading = true
      rejectSupervision(row).then(res => {
        if (res.code == 200) {
          this.$message.success('驳回成功')
          this.handleQuery()
        }
      })
    },
    handleIssuance(row) {
      issuanceMethod(row).then(res => {
        if (res.code == 200) {
          this.$message.success('下发成功')
          this.handleQuery()
        }
      })
    },
    handleClose() {
      this.openDetail = false
      this.handleQuery()
    },
    resetQuery() {
      this.queryParams = {
        pageNum: 1,
        pageSize: 50
      }
      this.handleQuery()
    },
    // 导出清单按钮
    exportList() {
      this.queryParams.stage = parseInt(this.type)
      let url = 'manager/noticeSupervision/export'
      if (this.type == '3') {
        url = 'manager/noticeSupervision/exportViewList'
      }
      this.download(
          url,
          {...this.queryParams},
          `督办管理导出清单_${new Date().getTime()}.xlsx`,
          {
            headers: {"Content-Type": "application/json;"},
            parameterType: "body",
          }
      );
    },
  }
}
</script>

<style lang="scss" scoped>

</style>
<style lang="scss" scoped>
@import "@/assets/styles/business.scss";
</style>
