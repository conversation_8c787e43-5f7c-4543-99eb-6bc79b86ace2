<template>
  <el-form ref="form" :model="ruleForm" :rules="rules" label-width="110px">
    <el-row :gutter="20" :style="{ pointerEvents: readonly ? 'none' : '' }">
      <el-col :span="12" :offset="0">
        <el-form-item label="字段名称" prop="columnName">
          <el-input v-model="ruleForm.columnName" placeholder="请输入字段名称" />
        </el-form-item>
      </el-col>
      <el-col :span="12" :offset="0">
        <el-form-item label="中文名称" prop="columnNameZh">
          <el-input v-model="ruleForm.columnNameZh" placeholder="请输入名称" />
        </el-form-item>
      </el-col>

      <el-col :span="12" :offset="0">
        <el-form-item label="分组名称" prop="groupName">
          <el-input v-model="ruleForm.groupName" placeholder="请输入分组名称" />
        </el-form-item>
      </el-col>
      <el-col :span="12" :offset="0">
        <el-form-item label="排序" prop="showIndex">
          <el-input-number v-model="ruleForm.showIndex" :step="1" step-strictly style="width: 100%;" />
        </el-form-item>
      </el-col>

      <el-col :span="12" :offset="0">
        <el-form-item label="数据类型" prop="columnType">
          <el-select v-model="ruleForm.columnType" placeholder="请选择" clearable style="width: 100%;">
            <el-option v-for="dict in dict.type.data_type" :key="dict.value" :label="dict.label" :value="dict.value" />
          </el-select>
        </el-form-item>
      </el-col>
      <el-col :span="12" :offset="0">
        <el-form-item label="输入类型" prop="inputType">
          <el-select v-model="ruleForm.inputType" placeholder="请选择" clearable style="width: 100%;">
            <el-option v-for="item in optionsObj[ruleForm.columnType]" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
      </el-col>

      <el-col :span="12" :offset="0">
        <el-form-item label="选择项" prop="optionName">
          <el-input v-model="ruleForm.optionName" placeholder="请输入选择项,英文逗号隔开" />
        </el-form-item>
      </el-col>
    </el-row>
    <el-form-item style="margin: auto;display: flex;justify-content: center;">
      <el-button type="primary" @click="onSubmit" v-loading="loading" v-if="!readonly">确定</el-button>
      <el-button @click="onCancel">取消</el-button>
    </el-form-item>
  </el-form>
</template>

<script>
import { addMenuSubQuery, updateMenuSubQuery } from "@/api/oneMap/menuSubQuery";
import { createIdWorker } from '@/api/baseData/common'

export default {
  dicts: ['data_type', 'input_type'],
  props: {
    form: {
      type: Object,
      default: {},
    },
    id: {
      type: [String, Number],
      default: ''
    },
    readonly: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      ruleForm: {
        oneMapShow: 1,
      },
      rules: {
        columnName: [
          { required: true, message: '请输入字段名称', trigger: 'blur' },
        ],
      },
      loading: false,
      enterOwnerId: '',
      optionsObj: {
        1: [],
        2: [
          {
            label: '输入框',
            value: 1
          },
          {
            label: '选择框',
            value: 2
          },
          {
            label: '区间',
            value: 3
          },
          {
            label: '单选框',
            value: 4
          },
          {
            label: '多选框',
            value: 5
          },
        ],
        3: [
          {
            label: '输入框',
            value: 1
          },
          {
            label: '区间',
            value: 3
          },
        ],
        4: [
          {
            label: '输入框',
            value: 1
          },
          {
            label: '区间',
            value: 3
          },
        ],
        5: [
          {
            label: '输入框',
            value: 1
          },
          {
            label: '区间',
            value: 3
          },
          // {
          //   label: '日期',
          //   value: ''
          // },
        ],
      },
    }
  },
  created() {
    this.ruleForm = { ...this.form }
    createIdWorker().then(res => {
      if (res.code === 200) this.enterOwnerId = res.data
    })
  },
  methods: {
    // 提交
    onSubmit() {
      this.$refs.form.validate(vali => {
        if (!vali) return
        this.ruleForm.iconId = Object.prototype.toString.call(this.ruleForm.iconId) === "[object Array]" ? this.ruleForm.iconId[0] : this.ruleForm.iconId
        let request = this.ruleForm.id ? updateMenuSubQuery(this.ruleForm) : addMenuSubQuery(this.ruleForm)
        let msg = this.ruleForm.id ? '编辑成功' : '新增成功';
        this.loading = true;
        request.then(res => {
          if (res.code == 200) {
            this.$modal.msgSuccess(msg);
            this.$emit('refresh')
            this.onCancel();
          }
        }).finally(() => {
          this.loading = false;
        })
      })
    },
    // 取消关闭
    onCancel() {
      this.$emit('close', false)
    },
  },
};
</script>

<style lang="scss" scoped></style>
