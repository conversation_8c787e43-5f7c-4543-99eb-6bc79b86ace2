<template>
  <div :class="theme">
    <el-dialog title="视频播放" :visible.sync="dialogFormVisible" custom-class="aass" @close="closeM3u8">
      <div :style="{ height: isBig ? '50vh' : '45vh' }" v-loading="load">
        <div v-if="url" style="height: 100%">
          <newVideo width="100%" height="100%" :url="url"></newVideo>
        </div>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="close" size="small">关 闭</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
// import commonMin from "@/api/commonMin";
// import min from "@/views/equipManage/mins/mins";
import newVideo from "../Video/newVideo.vue";
import { fetchGet } from '../../api.js'
import { isBigScreen } from '../../../utils/utils';
// import { getVideo } from "@/api/realtime";
export default {
  // mixins: [commonMin, min],
  components: { newVideo },
  props: {
    theme: {
      type: String,
      default: "dark",
    },
  },
  data() {
    return {
      load: false,
      url: "",
      // 用来测试的url
      testUrl:
        "https://yunqivedio.alicdn.com/2017yq/v2/0x0/96d79d3f5400514a6883869399708e11/96d79d3f5400514a6883869399708e11.m3u8",
      dialogFormVisible: false,
      id: "",
      task: "",
      timer: '',
      test: "http://playertest.longtailvideo.com/adaptive/bipbop/gear4/prog_index.m3u8",
      box: [],
      // 用于判断是哪个地方的视频
      videoId: "",
      isBig: isBigScreen(),
    };
  },
  methods: {
    getBox(a) {
      let url = "/structurevideo/getByStructureCode";
      this.requestGet(url, { ...a, currentPage: -1, pageSize: -1 }).then(
        (e) => {
          if (e.code == "200") {
            this.box = e.result.records;
          }
        }
      );
    },
    openM3U8(a, showLoading = false) {
      if (showLoading) {
        this.load = true;
        this.url = "";
      }
      const url = "https://jkjc.yciccloud.com:8000/xboot/structurevideo/getByStructureCode"
      fetchGet(url, a)
        .then((e) => {
          if (e.code == 200) {
            let urlList = e.result.records;
            // console.log("urlList", urlList)
            urlList.forEach((item) => {
              if (item.id == this.videoId) {
                this.url = String(item.videoIp + item.videoUrl);
                // console.log("视频地址:", this.url);
              }
            });
          }
        })
        .finally(() => {
          this.load = false;
        });
    },
    inits(a) {
      this.videoId = a.id;
      if (!a.id) {
        return;
      }
      if (this.task) {
        clearInterval(this.task);
        this.task = "";
      }
      this.$nextTick(() => {
        this.openM3U8(a, true);
      });
    },
    open(a) {
      this.timer = setTimeout(() => {
        this.inits(a);
        this.dialogFormVisible = true;
      }, 500);
    },
    closeM3u8() {
      clearInterval(this.task);
      clearTimeout(this.timer)
      this.task = "";
      this.id = "";
      this.url = "";
      console.log("关闭");
    },
    close() {
      this.dialogFormVisible = false;
      this.$nextTick(() => {
        this.dialogTitle = "";
        setTimeout(() => {
          this.url = "";
        }, 1000);
      });
    },
    beforeDestroy() {
      this.closeM3u8();
    },
  },
};
</script>
<style lang="scss" scoped>
@import "@/assets/styles/utils.scss";

.aass {
  width: 800px;
}

::v-deep {
  .el-dialog__header {
    color: #000 !important;
    font-size: vwpx(34px);
  }

  .el-dialog__title {
    color: #fff;
    font-size: vwpx(34px);
  }
}

.dark {
  ::v-deep {
    .el-dialog {
      background: rgba(12, 42, 86, 0.9);
      color: #fff;
      border: 1px solid #0687FF
    }

    .el-dialog__header {
      border-bottom: 1px #0687FF solid !important;
      padding: vwpx(40px) vwpx(60px) !important;
    }

    .el-dialog__title {
      color: #fff;
    }

    .el-dialog__body {
      padding: vwpx(40px) vwpx(60px) !important;
    }
  }
}
</style>