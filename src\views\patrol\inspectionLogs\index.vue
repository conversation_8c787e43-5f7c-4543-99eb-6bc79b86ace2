<template>
    <div class="app-container">
        <el-row :gutter="20">
            <!--筛选区开始-->
            <el-col :span="24" :xs="24">
                <el-row>
                    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" label-width="68px">
                        <el-col :span="24">
                            <el-form-item label="" prop="maintenanceUnitId">
                                <selectTree
                                    :key="'domainId'"
                                    v-model="queryParams.maintenanceUnitId"
                                    :deptType="101"
                                    :deptTypeList="[3, 4]"
                                    clearable
                                    @change="deptChange(queryParams.maintenanceUnitId)"
                                    filterable
                                    placeholder="管养单位"
                                    style="width: 240px"
                                />
                            </el-form-item>
                            <el-form-item label="" prop="maintenanceSectionId">
                                <el-select
                                    v-model="queryParams.maintenanceSectionId"
                                    filterable
                                    placeholder="请选养护路段"
                                    style="width: 240px"
                                >
                                    <el-option
                                        v-for="item in maintenanceSectionList"
                                        :key="item.maintenanceSectionId"
                                        :label="item.maintenanceSectionName"
                                        :value="item.maintenanceSectionId"
                                    ></el-option>
                                </el-select>
                            </el-form-item>
                            <el-form-item label="" prop="userNameLike">
                                <el-input
                                    v-model="queryParams.userNameLike"
                                    placeholder="请输入巡线员"
                                    clearable
                                    prefix-icon="el-icon-user"
                                    style="width: 240px"
                                    @keyup.enter.native="handleQuery"
                                />
                            </el-form-item>
                            <el-form-item label="" prop="patrolType">
                                <el-select
                                    v-model="queryParams.patrolType"
                                    placeholder="请选择巡查类型"
                                    clearable
                                    style="width: 240px"
                                >
                                    <el-option
                                        v-for="dict in dict.type.patrol_inspection_type"
                                        :key="dict.value"
                                        :label="dict.label"
                                        :value="dict.value"
                                    />
                                </el-select>
                            </el-form-item>
                            <el-form-item label="" prop="isEvent">
                                <el-select
                                    v-model="queryParams.isEvent"
                                    placeholder="有无事件"
                                    clearable
                                    style="width: 240px"
                                >
                                    <el-option label="有" value="1"></el-option>
                                    <el-option label="无" value="2"></el-option>
                                </el-select>
                            </el-form-item>

                            <el-form-item label="" prop="carNum">
                                <el-input
                                    v-model="queryParams.carNum"
                                    placeholder="请输入车牌号"
                                    clearable
                                    prefix-icon="el-icon-user"
                                    style="width: 240px"
                                    @keyup.enter.native="handleQuery"
                                />
                            </el-form-item>

                            <el-form-item>
                                <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">
                                    搜索
                                </el-button>
                                <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
                                <el-button
                                    v-show="!showSearch"
                                    @click="showSearch = true"
                                    icon="el-icon-arrow-down"
                                    circle
                                ></el-button>
                                <el-button
                                    v-show="showSearch"
                                    @click="showSearch = false"
                                    icon="el-icon-arrow-up"
                                    circle
                                ></el-button>
                            </el-form-item>
                            <el-form-item v-show="showSearch" label="巡查时间" prop="collectTime">
                                <el-date-picker
                                    v-model="dateRange"
                                    style="width: 240px"
                                    value-format="yyyy-MM-dd"
                                    type="daterange"
                                    range-separator="-"
                                    start-placeholder="开始日期"
                                    end-placeholder="结束日期"
                                ></el-date-picker>
                            </el-form-item>
                            <el-form-item v-show="showSearch" label="巡查机构" prop="patrolUnitId">
                                <selectTree
                                    v-model="queryParams.patrolUnitId"
                                    :deptType="100"
                                    filterable
                                    placeholder="巡查机构"
                                    style="width: 240px"
                                />
                            </el-form-item>
                            <!--默认折叠-->
                        </el-col>
                    </el-form>
                </el-row>
                <!--筛选区结束-->

                <!--操作按钮区开始-->
                <el-row :gutter="10" class="mb8">
                    <el-col :span="1.5">
                        <el-button
                            type="primary"
                            icon="el-icon-plus"
                            size="mini"
                            @click="handleAdd"
                            v-hasPermi="['patrol:inspectionLogs:add']"
                        >
                            新增
                        </el-button>
                    </el-col>
                    <el-col :span="1.5">
                        <el-button
                            type="success"
                            icon="el-icon-edit"
                            size="mini"
                            :disabled="single"
                            @click="handleUpdate"
                            v-hasPermi="['patrol:inspectionLogs:edit']"
                        >
                            修改
                        </el-button>
                    </el-col>
                    <el-col :span="1.5">
                        <el-button
                            type="danger"
                            icon="el-icon-delete"
                            size="mini"
                            :disabled="multiple"
                            @click="handleDelete"
                            v-hasPermi="['patrol:inspectionLogs:remove']"
                        >
                            删除
                        </el-button>
                    </el-col>
                    <el-col :span="1.5">
                        <el-button
                            type="success"
                            icon="el-icon-edit"
                            size="mini"
                            :disabled="single"
                            @click="syncDailyPatrol(1)"
                            v-hasPermi="['patrol:inspectionLogs:bridgeDaily']"
                        >
                            生成桥梁日常
                        </el-button>
                    </el-col>
                    <el-col :span="1.5">
                        <el-button
                            type="success"
                            icon="el-icon-edit"
                            size="mini"
                            :disabled="single"
                            @click="syncDailyPatrol(5)"
                            v-hasPermi="['patrol:inspectionLogs:tunnelDaily']"
                        >
                            生成隧道日常
                        </el-button>
                    </el-col>
                    <el-col :span="1.5">
                        <el-button type="primary" icon="el-icon-download" size="mini" @click="handleExportCard">
                            导出报告
                        </el-button>
                    </el-col>
                    <el-col :span="1.5">
                        <el-button type="warning" icon="el-icon-download" size="mini" @click="handleExport">
                            导出
                        </el-button>
                    </el-col>
                    <right-toolbar
                        :showSearch.sync="showSearch"
                        @queryTable="getList"
                        :columns="columns"
                    ></right-toolbar>
                </el-row>
                <!--操作按钮区结束-->

                <!--数据表格开始-->
                <div class="tableDiv">
                    <el-table
                        v-adjust-table
                        ref="table"
                        size="mini"
                        :height="showSearch ? 'calc(100vh - 320px)' : 'calc(100vh - 260px)'"
                        style="width: 100%"
                        v-loading="loading"
                        border
                        :data="inspectionLogsList"
                        @selection-change="handleSelectionChange"
                        :row-style="rowStyle"
                        @row-click="handleRowClick"
                    >
                        <el-table-column type="selection" width="50" align="center" fixed="left" />
                        <el-table-column label="序号" type="index" width="50">
                            <template v-slot="scope">
                                {{ scope.$index + (queryParams.pageNum - 1) * queryParams.pageSize + 1 }}
                            </template>
                        </el-table-column>
                        <el-table-column label="巡查类型" align="center" prop="patrolType" v-if="columns[0].visible">
                            <template slot-scope="scope">
                                <dict-tag :options="dict.type.patrol_inspection_type" :value="scope.row.patrolType" />
                            </template>
                        </el-table-column>
                        <el-table-column
                            label="巡查路段"
                            align="center"
                            show-overflow-tooltip
                            prop="maintenanceSectionName"
                            v-if="columns[1].visible"
                        />
                        <el-table-column
                            align="center"
                            prop="direction"
                            label="方向"
                            width="50"
                            v-if="columns[2].visible"
                        >
                            <template slot-scope="scope">
                                <dict-tag
                                    :options="dict.type.patrol_inspection_direction"
                                    :value="scope.row.direction"
                                />
                            </template>
                        </el-table-column>
                        <el-table-column
                            label="巡查人员"
                            align="center"
                            prop="userNames"
                            show-overflow-tooltip
                            width="110"
                            v-if="columns[3].visible"
                        />
                        <el-table-column label="车辆" align="center" prop="carNum" v-if="columns[4].visible" />
                        <el-table-column
                            label="天气"
                            align="center"
                            prop="weather"
                            show-overflow-tooltip
                            v-if="columns[5].visible"
                        />
                        <el-table-column
                            label="巡查内容"
                            align="center"
                            prop="content"
                            width="280"
                            v-if="columns[6].visible"
                        />
                        <el-table-column
                            label="巡查机构"
                            align="center"
                            prop="patrolUnitName"
                            width="90"
                            v-if="columns[7].visible"
                        />
                        <el-table-column
                            label="管养单位"
                            align="center"
                            prop="maintenanceUnitName"
                            width="90"
                            v-if="columns[8].visible"
                        />
                        <!--              <el-table-column label="管理处" align="center" prop="content" />-->
                        <el-table-column
                            label="开始时间"
                            align="center"
                            prop="startTime"
                            width="90"
                            v-if="columns[9].visible"
                        />
                        <el-table-column
                            label="结束时间"
                            align="center"
                            prop="endTime"
                            width="90"
                            v-if="columns[10].visible"
                        />
<!--                        <el-table-column-->
<!--                            label="上报时间"-->
<!--                            align="center"-->
<!--                            prop="reportedTime"-->
<!--                            width="90"-->
<!--                            v-if="columns[11].visible"-->
<!--                        />-->
<!--                        <el-table-column-->
<!--                            label="采集时间"-->
<!--                            align="center"-->
<!--                            prop="collectTime"-->
<!--                            width="90"-->
<!--                            v-if="columns[12].visible"-->
<!--                        />-->
                        <el-table-column
                            label="巡查里程(km)"
                            align="center"
                            prop="patrolMileage"
                            width="120"
                            v-if="columns[11].visible"
                        />
                        <el-table-column
                            label="巡查病害数"
                            align="center"
                            prop="diseaseNum"
                            width="100"
                            v-if="columns[12].visible"
                        />
                        <el-table-column
                            label="操作"
                            fixed="right"
                            align="center"
                            width="340"
                            class-name="small-padding"
                        >
                            <template slot-scope="scope">
                                <el-button
                                    size="mini"
                                    type="text"
                                    icon="el-icon-edit"
                                    @click="handleUpdate(scope.row)"
                                    v-hasPermi="['patrol:inspectionLogs:edit']"
                                >
                                    修改
                                </el-button>
                                <el-button
                                    size="mini"
                                    type="text"
                                    icon="el-icon-delete"
                                    @click="handleDelete(scope.row)"
                                    v-hasPermi="['patrol:inspectionLogs:remove']"
                                >
                                    删除
                                </el-button>
                                <el-button
                                    size="mini"
                                    type="text"
                                    icon="el-icon-edit"
                                    @click="onDetail(scope.row)"
                                    v-hasPermi="['patrol:inspectionLogs:query']"
                                >
                                    事件
                                </el-button>
                                <el-button
                                    size="mini"
                                    type="text"
                                    icon="el-icon-document"
                                    @click="handleViewReport(scope.row)"
                                    v-hasPermi="['patrol:inspectionLogs:query']"
                                >
                                    报告
                                </el-button>
                                <el-button
                                    size="mini"
                                    type="text"
                                    icon="el-icon-map-location"
                                    @click="traceView(scope.row)"
                                >
                                    轨迹查看
                                </el-button>
                            </template>
                        </el-table-column>
                    </el-table>

                    <pagination
                        v-show="total > 0"
                        :total="total"
                        :page.sync="queryParams.pageNum"
                        :limit.sync="queryParams.pageSize"
                        @pagination="getList"
                    />
                </div>
                <!--数据表格结束-->
            </el-col>
        </el-row>

        <!-- 添加或修改巡查日志对话框 -->
        <el-dialog
            :title="title"
            :visible.sync="open"
            width="45%"
            max-height="50%"
            :close-on-press-escape="false"
            :close-on-click-modal="false"
            append-to-body
            class="formDialog"
        >
            <el-form ref="form" :model="form" :rules="rules" label-width="120px" label-position="right">
                <div class="infoBox" style="margin-bottom: 20px" v-loading="loading">
                    <div class="infoTitle">基础信息</div>
                    <el-row :gutter="12">
                        <el-col :span="24">
                            <el-form-item label="添加模式">
                                <el-switch
                                    v-model="batchMode"
                                    active-text="批量添加"
                                    inactive-text="单条添加"
                                    @change="handleModeChange"
                                >
                                </el-switch>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row :gutter="12" v-if="batchMode">
                        <el-col :span="24">
                            <el-form-item label="日期范围" prop="dateRange">
                                <el-date-picker
                                    v-model="form.dateRange"
                                    type="daterange"
                                    range-separator="至"
                                    start-placeholder="开始日期"
                                    end-placeholder="结束日期"
                                    value-format="yyyy-MM-dd"
                                    style="width: 100%"
                                ></el-date-picker>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row :gutter="12">
                        <el-col :span="24">
                            <el-form-item label="巡查类型" prop="patrolType">
                                <el-select
                                    v-model="form.patrolType"
                                    filterable
                                    placeholder="请选巡查类型"
                                    style="width: 100%"
                                >
                                    <el-option
                                        v-for="dict in dict.type.patrol_inspection_type"
                                        :key="dict.value"
                                        :label="dict.label"
                                        :value="parseInt(dict.value)"
                                    ></el-option>
                                </el-select>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row :gutter="12">
                        <el-col :span="24">
                            <el-form-item label="巡查方向" prop="direction">
                                <el-select
                                    v-model="form.direction"
                                    filterable
                                    placeholder="请选巡查方向"
                                    style="width: 100%"
                                >
                                    <el-option
                                        v-for="dict in dict.type.patrol_inspection_direction"
                                        :key="dict.value"
                                        :label="dict.label"
                                        :value="parseInt(dict.value)"
                                    ></el-option>
                                </el-select>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row :gutter="12">
                        <el-col :span="24">
                            <el-form-item label="管养单位" prop="maintenanceUnitId">
                                <selectTree
                                  :key="'domainId'"
                                  v-model="form.maintenanceUnitId"
                                  :deptType="101"
                                  :deptTypeList="[3, 4]"
                                  clearable
                                  @change="deptChange(form.maintenanceUnitId)"
                                  filterable
                                  onlySelectChild
                                  placeholder="管养单位"
                                  style="width: 100%"
                                />
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row :gutter="12">
                        <el-col :span="24">
                            <el-form-item label="养护路段" prop="maintenanceSectionId">
                                <el-select
                                    v-model="form.maintenanceSectionId"
                                    filterable
                                    placeholder="请选养护路段"
                                    style="width: 100%"
                                >
                                    <el-option
                                        v-for="item in maintenanceSectionList"
                                        :key="item.maintenanceSectionId"
                                        :label="item.maintenanceSectionName"
                                        :value="item.maintenanceSectionId"
                                    ></el-option>
                                </el-select>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row :gutter="12">
                        <el-col :span="24">
                            <el-form-item
                                :label="batchMode ? '起始时间(时分秒)' : '起始时间'"
                                :prop="batchMode ? 'startTimeHMS' : 'startTime'"
                            >
                                <el-date-picker
                                    v-if="!batchMode"
                                    v-model="form.startTime"
                                    value-format="yyyy-MM-dd HH:mm:ss"
                                    type="datetime"
                                    placeholder="年-月-日 时:分:秒"
                                    style="width: 100%"
                                ></el-date-picker>
                                <el-time-picker
                                    v-else
                                    v-model="form.startTimeHMS"
                                    value-format="HH:mm:ss"
                                    placeholder="时:分:秒"
                                    style="width: 100%"
                                ></el-time-picker>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row :gutter="12">
                        <el-col :span="24">
                            <el-form-item
                                :label="batchMode ? '终止时间(时分秒)' : '终止时间'"
                                :prop="batchMode ? 'endTimeHMS' : 'endTime'"
                            >
                                <el-date-picker
                                    v-if="!batchMode"
                                    v-model="form.endTime"
                                    value-format="yyyy-MM-dd HH:mm:ss"
                                    type="datetime"
                                    placeholder="年-月-日 时:分:秒"
                                    style="width: 100%"
                                ></el-date-picker>
                                <el-time-picker
                                    v-else
                                    v-model="form.endTimeHMS"
                                    value-format="HH:mm:ss"
                                    placeholder="时:分:秒"
                                    style="width: 100%"
                                ></el-time-picker>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <!--       25.7.22  隐藏（有歧义）           -->
<!--                    <el-row :gutter="12">-->
<!--                        <el-col :span="24">-->
<!--                            <el-form-item-->
<!--                                :label="batchMode ? '上报时间(时分秒)' : '上报时间'"-->
<!--                                :prop="batchMode ? 'reportedTimeHMS' : 'reportedTime'"-->
<!--                            >-->
<!--                                <el-date-picker-->
<!--                                    v-if="!batchMode"-->
<!--                                    v-model="form.reportedTime"-->
<!--                                    value-format="yyyy-MM-dd HH:mm:ss"-->
<!--                                    type="datetime"-->
<!--                                    placeholder="年-月-日 时:分:秒"-->
<!--                                    style="width: 100%"-->
<!--                                ></el-date-picker>-->
<!--                                <el-time-picker-->
<!--                                    v-else-->
<!--                                    v-model="form.reportedTimeHMS"-->
<!--                                    value-format="HH:mm:ss"-->
<!--                                    placeholder="时:分:秒"-->
<!--                                    style="width: 100%"-->
<!--                                ></el-time-picker>-->
<!--                            </el-form-item>-->
<!--                        </el-col>-->
<!--                    </el-row>-->
<!--                    <el-row :gutter="12">-->
<!--                        <el-col :span="24">-->
<!--                            <el-form-item-->
<!--                                :label="batchMode ? '采集时间(时分秒)' : '采集时间'"-->
<!--                                :prop="batchMode ? 'collectTimeHMS' : 'collectTime'"-->
<!--                            >-->
<!--                                <el-date-picker-->
<!--                                    v-if="!batchMode"-->
<!--                                    v-model="form.collectTime"-->
<!--                                    value-format="yyyy-MM-dd HH:mm:ss"-->
<!--                                    type="datetime"-->
<!--                                    placeholder="年-月-日 时:分:秒"-->
<!--                                    style="width: 100%"-->
<!--                                ></el-date-picker>-->
<!--                                <el-time-picker-->
<!--                                    v-else-->
<!--                                    v-model="form.collectTimeHMS"-->
<!--                                    value-format="HH:mm:ss"-->
<!--                                    placeholder="时:分:秒"-->
<!--                                    style="width: 100%"-->
<!--                                ></el-time-picker>-->
<!--                            </el-form-item>-->
<!--                        </el-col>-->
<!--                    </el-row>-->
                    <el-row :gutter="12">
                        <el-col :span="24">
                            <el-form-item label="巡查里程(km)" prop="patrolMileage">
                                <el-input v-model="form.patrolMileage" placeholder="请输巡查里程(km)" />
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row :gutter="12">
                        <el-col :span="24">
                            <el-form-item label="车牌号" prop="carNum">
                                <el-input v-model="form.carNum" placeholder="请输入车牌号" />
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row :gutter="12">
                        <el-col :span="24">
                            <el-form-item label="天气" prop="weather">
                                <el-input v-model="form.weather" placeholder="请输入天气" />
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row :gutter="12">
                        <el-col :span="24">
                            <el-form-item label="巡查人员" prop="userIdList">
                                <el-cascader
                                    v-model="form.userIdList"
                                    :options="deptUserOptions"
                                    :props="props"
                                    :show-all-levels="false"
                                    ref="deptUser"
                                    filterable
                                    clearable
                                    style="width: 100%"
                                ></el-cascader>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row :gutter="12">
                        <el-col :span="24">
                            <el-form-item label="巡查内容" prop="content">
                                <el-input type="textarea" v-model="form.content" placeholder="请输入巡查内容" />
                            </el-form-item>
                        </el-col>
                    </el-row>
                </div>
            </el-form>
            <div slot="footer" class="dialog-footer">
                <el-button type="primary" @click="submitForm">确 定</el-button>
                <el-button @click="cancel">取 消</el-button>
            </div>
        </el-dialog>

        <eventDetail
            :visible.sync="showDetail"
            :data="currentRow"
            title="事件详情"
            :append-to-body="true"
            size="60%"
        ></eventDetail>
        <TraceDialog
            :visible.sync="traceVisible"
            :rowData.sync="currentRow"
            @visibleChange="() => (currentRow = {})"
        ></TraceDialog>
    </div>
</template>

<script>
    import {
        batchAddInspectionLogs,
        listInspectionLogs,
        getInspectionLogs,
        delInspectionLogs,
        batchDeleteInspectionLogs,
        addInspectionLogs,
        updateInspectionLogs,
    } from '@/api/patrol/inspectionLogs';
    import Treeselect from '@riophae/vue-treeselect';
    import '@riophae/vue-treeselect/dist/vue-treeselect.css';
    import { listMaintenanceSectionAll } from '@/api/system/maintenanceSection';
    import { getTreeStruct } from '@/api/tmpl';
    import { deptTreeSelect } from '@/api/system/user';
    import eventDetail from './event.vue';
    import { getToken } from '@/utils/auth';
    import selectTree from '@/components/DeptTmpl/selectTree.vue';
    import { setGenerateCheck } from '@/api/patrol/assetCheck';
    const TraceDialog = () => import('@/views/patrol/inspectionLogs/traceView/index.vue');

    export default {
        name: 'InspectionLogs',
        dicts: ['patrol_inspection_type', 'patrol_inspection_direction'],
        components: { selectTree, Treeselect, eventDetail, TraceDialog },
        data() {
            return {
                // 遮罩层
                loading: true,
                // 选中数组
                ids: [],
                // 非单个禁用
                single: true,
                // 非多个禁用
                multiple: true,
                // 显示搜索条件
                showSearch: false,
                dictType: [],
                maintenanceSectionList: [],
                // 日期范围
                dateRange: [],
                // 总条数
                total: 0,
                // 巡查日志表格数据
                inspectionLogsList: null,
                // 弹出层标题
                title: '',
                // 部门-用户树选项
                deptUserOptions: [],
                // 是否显示弹出层
                open: false,
                showDetail: false,
                currentRow: {},
                // 表单参数
                form: {},
                props: {
                    multiple: true, //是否多选
                    value: 'id',
                },
                // 查询参数
                queryParams: {
                    pageNum: 1,
                    pageSize: 10,
                    patrolType: null,
                    userNameLike: null,
                    isEvent: null,
                    maintenanceSectionId: null,
                    maintenanceUnitId: null,
                    collectTime: null,
                    patrolUnitId: null,
                    carNum: null,
                    collectTimee: null,
                    collectTimes: null,
                },
                // 表单校验
                rules: {
                    patrolType: [{ required: true, message: '巡查类型不能为空', trigger: 'change' }],
                    maintenanceUnitId: [{ required: true, message: '管养单位不能为空', trigger: 'change' }],
                    maintenanceSectionId: [{ required: true, message: '养护路段不能为空', trigger: 'change' }],
                    direction: [{ required: true, message: '巡查方向不能为空', trigger: 'change' }],
                    carNum: [{ required: true, message: '车牌号不能为空', trigger: 'blur' }],
                    startTime: [{ type: 'string', required: true, message: '请选择起始时间', trigger: 'change' }],
                    endTime: [{ type: 'string', required: true, message: '请选择终止时间', trigger: 'change' }],
                    reportedTime: [{ type: 'string', required: true, message: '请选择上报时间', trigger: 'change' }],
                    collectTime: [{ type: 'string', required: true, message: '请选择采集时间', trigger: 'change' }],
                    weather: [{ type: 'string', required: true, message: '请输入天气', trigger: 'blur' }],
                    userIdList: [{ required: true, message: '请选择巡查人员', trigger: 'change' }],
                    // 批量模式下的校验规则
                    dateRange: [{ type: 'array', required: true, message: '请选择日期范围', trigger: 'change' }],
                    startTimeHMS: [{ type: 'string', required: true, message: '请选择起始时间', trigger: 'change' }],
                    endTimeHMS: [{ type: 'string', required: true, message: '请选择终止时间', trigger: 'change' }],
                    reportedTimeHMS: [{ type: 'string', required: true, message: '请选择上报时间', trigger: 'change' }],
                    collectTimeHMS: [{ type: 'string', required: true, message: '请选择采集时间', trigger: 'change' }],
                },
                dialogVisible: false,
                reportSrc: '',
                exportingSelectedIds: false,
                exportingByQuery: false,
                traceVisible: false,
                isViewSingleReport: false, // 添加标志位，表示是否正在查看单个报告
                isNoSign: false,
                // 列显示控制
                columns: [
                    { key: 0, label: '巡查类型', visible: true },
                    { key: 1, label: '巡查路段', visible: true },
                    { key: 2, label: '方向', visible: true },
                    { key: 3, label: '巡查人员', visible: true },
                    { key: 4, label: '车辆', visible: true },
                    { key: 5, label: '天气', visible: true },
                    { key: 6, label: '巡查内容', visible: true },
                    { key: 7, label: '巡查机构', visible: true },
                    { key: 8, label: '管养单位', visible: true },
                    { key: 9, label: '开始时间', visible: true },
                    { key: 10, label: '结束时间', visible: true },
                    // { key: 11, label: '上报时间', visible: true },
                    // { key: 12, label: '采集时间', visible: true },
                    { key: 13, label: '巡查里程', visible: true },
                    { key: 14, label: '巡查病害数', visible: true },
                ],
                // 批量模式
                batchMode: false,
            };
        },
        watch: {
            // 根据名称筛选部门树
        },
        computed: {
        },
        created() {
            this.getList();
            // this.getDeptList()
            this.getMaintenanceSection();
            this.getDeptTreeDef();
        },
        methods: {
            onDetail(row) {
                this.currentRow = row;
                this.showDetail = true;
            },

            /** 查询巡查日志列表 */
            getList() {
                this.loading = true;
                this.queryParams.collectTimee = this.dateRange[0];
                this.queryParams.collectTimes = this.dateRange[1];
                listInspectionLogs(this.queryParams).then((response) => {
                    this.inspectionLogsList = response.rows;
                    this.total = response.total;
                    this.loading = false;
                });
            },
            /** 查询养护路段下拉列表 */
            getMaintenanceSection() {
                listMaintenanceSectionAll().then((res) => {
                    this.maintenanceSectionList = res.data;
                });
            },
            /** 查询部门-用户下拉树结构 */
            getDeptTreeDef() {
                getTreeStruct({ types: 111 }).then((response) => {
                    this.deptUserOptions = response.data;
                });
            },
            //管理处下拉选点击事件
            deptChange(departmentId) {
                listMaintenanceSectionAll({ departmentId: departmentId }).then((res) => {
                    this.maintenanceSectionList = res.data;
                });
            },
            // 取消按钮
            cancel() {
                this.open = false;
                this.reset();
            },
            // 表单重置
            reset() {
                this.form = {
                    patrolType: null,
                    maintenanceSectionId: null,
                    direction: null,
                    carNum: null,
                    weather: null,
                    content: null,
                    startTime: null,
                    endTime: null,
                    reportedTime: null,
                    collectTime: null,
                    patrolMileage: null,
                    userIdList: [],
                    // 批量模式字段
                    dateRange: [],
                    startTimeHMS: null,
                    endTimeHMS: null,
                    reportedTimeHMS: null,
                    collectTimeHMS: null,
                };
                this.resetForm('form');
                // 重置为单条添加模式
                this.batchMode = false;
            },
            /** 搜索按钮操作 */
            handleQuery() {
                this.queryParams.pageNum = 1;
                this.getList();
            },
            /** 重置按钮操作 */
            resetQuery() {
                this.dateRange = [];
                this.resetForm('queryForm');
                // 显式重置日期相关查询参数
                this.queryParams.collectTimee = null;
                this.queryParams.collectTimes = null;
                this.getMaintenanceSection();
                this.handleQuery();
            },
            // 多选框选中数
            handleSelectionChange(selection) {
                this.ids = selection.map((item) => item.id);
                this.single = selection.length != 1;
                this.multiple = !selection.length;
            },
            // 表格点击勾选
            handleRowClick(row) {
                row.isSelected = !row.isSelected;
                this.$refs.table.toggleRowSelection(row);
            },
            // 勾选高亮
            rowStyle({ row, rowIndex }) {
                if (this.ids.includes(row.id)) {
                    return { 'background-color': '#b7daff', color: '#333' };
                } else {
                    return { 'background-color': '#fff', color: '#333' };
                }
            },
            /** 新增按钮操作 */
            handleAdd() {
                this.reset();
                this.open = true;
                this.title = '添加巡查日志';
            },
            /** 修改按钮操作 */
            async handleUpdate(row) {
                this.reset();
                const id = row.id || this.ids;
                await getInspectionLogs(id).then((response) => {
                    this.$nextTick(() => {
                        this.form = response.data;
                        this.form.patrolMileage = (this.form.patrolMileage / 1000).toFixed(3);
                        let list = [];
                        if (this.deptUserOptions.length <= 0) {
                            getTreeStruct({ types: 111 }).then((datas) => {
                                this.deptUserOptions = datas.data;
                            });
                        }
                        response.data.userIds.forEach((item) => {
                            list.push(this.changeCascader(item, this.deptUserOptions));
                        });
                        this.form.userIdList = list;
                        this.open = true;
                        this.title = '修改巡查日志';
                    });
                });
            },
            /** 提交按钮 */
            submitForm: function () {
                // 根据当前模式动态设置校验规则
                if (this.batchMode) {
                    // 批量模式下，移除对完整时间的验证，改为验证时分秒和日期范围
                    this.$refs['form'].clearValidate(['startTime', 'endTime', 'reportedTime', 'collectTime']);
                } else {
                    // 单条模式下，移除对时分秒和日期范围的验证
                    this.$refs['form'].clearValidate([
                        'startTimeHMS',
                        'endTimeHMS',
                        'reportedTimeHMS',
                        'collectTimeHMS',
                        'dateRange',
                    ]);
                }

                this.$refs['form'].validate((valid) => {
                    let userIds = [];
                    this.$refs.deptUser.getCheckedNodes().forEach((value) => {
                        console.log(value);
                        if (value.children.length <= 0) {
                            userIds.push(value.value);
                        }
                    });
                    this.form.userIds = userIds;
                    if (valid) {
                        this.form.patrolMileage = this.form.patrolMileage * 1000;
                        const selectedItem = this.maintenanceSectionList.find(
                            (item) => item.maintenanceSectionId === this.form.maintenanceSectionId
                        );
                        if (selectedItem) this.form.maintenanceSectionName = selectedItem.maintenanceSectionName;

                        if (this.batchMode) {
                            // 批量添加模式
                            if (!this.form.dateRange || this.form.dateRange.length !== 2) {
                                this.$modal.msgError('请选择日期范围');
                                return;
                            }

                            if (
                                !this.form.startTimeHMS ||
                                !this.form.endTimeHMS ||
                                !this.form.reportedTimeHMS ||
                                !this.form.collectTimeHMS
                            ) {
                                this.$modal.msgError('请完善时间信息');
                                return;
                            }

                            // 生成日期列表
                            const startDate = new Date(this.form.dateRange[0]);
                            const endDate = new Date(this.form.dateRange[1]);
                            const dateList = [];

                            const currentDate = new Date(startDate);
                            while (currentDate <= endDate) {
                                dateList.push(this.formatDate(currentDate));
                                currentDate.setDate(currentDate.getDate() + 1);
                            }

                            // 准备批量数据
                            const batchData = [];
                            dateList.forEach((date) => {
                                const newItem = { ...this.form };
                                // 删除不需要的日期范围
                                delete newItem.dateRange;

                                // 构建完整的时间
                                newItem.startTime = `${date} ${this.form.startTimeHMS}`;
                                newItem.endTime = `${date} ${this.form.endTimeHMS}`;
                                newItem.reportedTime = `${date} ${this.form.reportedTimeHMS}`;
                                newItem.collectTime = `${date} ${this.form.collectTimeHMS}`;

                                // 删除时分秒字段
                                delete newItem.startTimeHMS;
                                delete newItem.endTimeHMS;
                                delete newItem.reportedTimeHMS;
                                delete newItem.collectTimeHMS;

                                batchData.push(newItem);
                            });

                            // 调用批量添加API
                            batchAddInspectionLogs(batchData).then((response) => {
                                this.$modal.msgSuccess('批量添加成功');
                                this.open = false;
                                this.getList();
                            });
                        } else {
                            // 单条添加模式
                            if (this.form.id != null) {
                                updateInspectionLogs(this.form).then((response) => {
                                    this.$modal.msgSuccess('修改成功');
                                    this.open = false;
                                    this.getList();
                                });
                            } else {
                                addInspectionLogs(this.form).then((response) => {
                                    this.$modal.msgSuccess('新增成功');
                                    this.open = false;
                                    this.getList();
                                });
                            }
                        }
                    }
                });
            },
            handleNodeSelected(node) {
                this.form.maintenanceUnitId = node.id;
                this.form.maintenanceUnitName = node.label; // 获取对应的 name
            },
            /** 删除按钮操作 */
            handleDelete(row) {
                const ids = row.id || this.ids;
                const isMultiple = Array.isArray(ids);
                const message = isMultiple
                    ? `是否确认删除选中的${ids.length}条巡查日志数据项?`
                    : `是否确认删除巡查日志编号为"${ids}"的数据项？`;

                this.$modal
                    .confirm(message)
                    .then(function () {
                        return isMultiple ? batchDeleteInspectionLogs(ids) : delInspectionLogs(ids);
                    })
                    .then(() => {
                        this.getList();
                        this.$modal.msgSuccess('删除成功');
                    })
                    .catch(() => {});
            },
            /** 生成日常巡查按钮操作 */
            syncDailyPatrol(type) {
                const id = this.ids[0];
                const info = type === 1 ? '桥梁日常巡查？' : '隧道日常巡查？';
                this.$modal
                    .confirm('是否确认生成该巡查日志的' + info)
                    .then(function () {
                        return setGenerateCheck({ logId: id, type: type });
                    })
                    .then(() => {
                        this.getList();
                        this.$modal.msgSuccess('操作成功');
                    })
                    .catch(() => {});
            },
            // 在对话框的关闭事件中
            dialogClosed() {
                this.currentRow = null;
                this.exportingSelectedIds = false;
                this.exportingByQuery = false;
                this.isViewSingleReport = false; // 重置单个报告查看标志
                this.isNoSign = false; // 重置无签名标志
            },

            /** 导出报告按钮操作 */
            handleExportCard() {
                // 如果有选中项，直接使用选中项的ID
                if (this.ids.length > 0) {
                    this.$confirm(`根据选中条件，本次导出共有 ${this.ids.length} 条数据，是否确认导出？`, '提示', {
                        confirmButtonText: '确定',
                        cancelButtonText: '取消',
                        type: 'warning',
                    })
                        .then(() => {
                            const now = new Date();
                            const timeStr = `${now.getFullYear()}年${
                                now.getMonth() + 1
                            }月${now.getDate()}日${now.getHours()}时${now.getMinutes()}分${now.getSeconds()}秒`;
                            const fileName = `巡查日志报告_${timeStr}.pdf`;

                            this.download(
                                'patrol/inspectionLogs/exportDailyPatrolReport',
                                {
                                    ids: this.ids,
                                },
                                fileName,
                                {
                                    parameterType: 'body', // 设置参数类型为 body
                                    headers: {
                                        'Content-Type': 'application/json', // 设置请求头为 JSON
                                    },
                                }
                            );
                        })
                        .catch(() => {
                            this.$message({
                                type: 'info',
                                message: '已取消导出',
                            });
                        });
                    return;
                }

                // 如果没有选中项，则使用查询条件获取数量
                let queryParams = {
                    ...this.queryParams,
                    dataRule: true,
                };

                // 处理日期范围
                if (this.dateRange && this.dateRange.length === 2) {
                    queryParams.collectTimee = this.dateRange[0];
                    queryParams.collectTimes = this.dateRange[1];
                }

                // 获取数据总数
                listInspectionLogs({
                    ...queryParams,
                    pageNum: 1,
                    pageSize: 1,
                }).then((res) => {
                    const export_count = res.total;

                    this.$confirm(`根据搜索条件，本次导出共有 ${export_count} 条数据，是否确认导出？`, '提示', {
                        confirmButtonText: '确定',
                        cancelButtonText: '取消',
                        type: 'warning',
                    })
                        .then(() => {
                            const now = new Date();
                            const timeStr = `${now.getFullYear()}年${
                                now.getMonth() + 1
                            }月${now.getDate()}日${now.getHours()}时${now.getMinutes()}分${now.getSeconds()}秒`;
                            const fileName = `巡查日志报告_${timeStr}.pdf`;

                            this.download(
                                'patrol/inspectionLogs/exportDailyPatrolReport',
                                {
                                    ...queryParams,
                                },
                                fileName,
                                {
                                    parameterType: 'body', // 设置参数类型为 body
                                    headers: {
                                        'Content-Type': 'application/json', // 设置请求头为 JSON
                                    },
                                }
                            );
                        })
                        .catch(() => {
                            this.$message({
                                type: 'info',
                                message: '已取消导出',
                            });
                        });
                });
            },

            /** 导出Excel按钮操作 */
            handleExport() {
                // 如果有选中项，直接使用选中项的ID
                if (this.ids.length > 0) {
                    this.$confirm(`根据选中条件，本次导出共有 ${this.ids.length} 条数据，是否确认导出？`, '提示', {
                        confirmButtonText: '确定',
                        cancelButtonText: '取消',
                        type: 'warning',
                    })
                        .then(() => {
                            const now = new Date();
                            const timeStr = `${now.getFullYear()}年${
                                now.getMonth() + 1
                            }月${now.getDate()}日${now.getHours()}时${now.getMinutes()}分${now.getSeconds()}秒`;
                            const fileName = `巡查日志_${timeStr}.xlsx`;

                            this.download(
                                'patrol/inspectionLogs/exportByCondition',
                                {
                                    ids: this.ids,
                                },
                                fileName,
                                {
                                    parameterType: 'body', // 设置参数类型为 body
                                    headers: {
                                        'Content-Type': 'application/json', // 设置请求头为 JSON
                                    },
                                }
                            );
                        })
                        .catch(() => {
                            this.$message({
                                type: 'info',
                                message: '已取消导出',
                            });
                        });
                    return;
                }

                // 如果没有选中项，则使用查询条件获取数量
                let queryParams = {
                    ...this.queryParams,
                    dataRule: true,
                };

                // 处理日期范围
                if (this.dateRange && this.dateRange.length === 2) {
                    queryParams.collectTimee = this.dateRange[0];
                    queryParams.collectTimes = this.dateRange[1];
                }

                // 获取数据总数
                listInspectionLogs({
                    ...queryParams,
                    pageNum: 1,
                    pageSize: 1,
                }).then((res) => {
                    const export_count = res.total;

                    this.$confirm(`根据搜索条件，本次导出共有 ${export_count} 条数据，是否确认导出？`, '提示', {
                        confirmButtonText: '确定',
                        cancelButtonText: '取消',
                        type: 'warning',
                    })
                        .then(() => {
                            const now = new Date();
                            const timeStr = `${now.getFullYear()}年${
                                now.getMonth() + 1
                            }月${now.getDate()}日${now.getHours()}时${now.getMinutes()}分${now.getSeconds()}秒`;
                            const fileName = `巡查日志_${timeStr}.xlsx`;

                            this.download(
                                'patrol/inspectionLogs/exportByCondition',
                                {
                                    ...queryParams,
                                },
                                fileName,
                                {
                                    parameterType: 'body', // 设置参数类型为 body
                                    headers: {
                                        'Content-Type': 'application/json', // 设置请求头为 JSON
                                    },
                                }
                            );
                        })
                        .catch(() => {
                            this.$message({
                                type: 'info',
                                message: '已取消导出',
                            });
                        });
                });
            },

            //把返回的一维数组转成多维数组 key：对比的id ， arrData：原始数组
            changeCascader(key, arrData) {
                let arr = [];
                let returnArr = []; // 存放结果的数组
                let depth = 0; // 定义全局层级
                // 定义递归函数
                function childrenEach(childrenData, depthN) {
                    for (var j = 0; j < childrenData.length; j++) {
                        depth = depthN; // 将执行的层级赋值 到 全局层级
                        arr[depthN] = childrenData[j].id;
                        if (childrenData[j].id == key) {
                            returnArr = arr.slice(0, depthN + 1); //将目前匹配的数组，截断并保存到结果数组，
                            break;
                        } else {
                            if (childrenData[j].children) {
                                depth++;
                                childrenEach(childrenData[j].children, depth);
                            }
                        }
                    }
                    return returnArr;
                }
                return childrenEach(arrData, depth);
            },

            handleViewReport(row) {
                const now = new Date();
                const timeStr = `${now.getFullYear()}年${
                    now.getMonth() + 1
                }月${now.getDate()}日${now.getHours()}时${now.getMinutes()}分${now.getSeconds()}秒`;
                const fileName = `巡查日志报告_${timeStr}.pdf`;

                this.download(
                    'patrol/inspectionLogs/exportDailyPatrolReport',
                    {
                        ids: [row.id]
                    },
                    fileName,
                    {
                        parameterType: 'body', // 设置参数类型为 body
                        headers: {
                            'Content-Type': 'application/json', // 设置请求头为 JSON
                        },
                    }
                );
            },

            traceView(row) {
                this.currentRow = row || null;
                this.traceVisible = true;
            },

            handleModeChange() {
                // 处理模式切换逻辑
                if (this.batchMode) {
                    // 切换到批量模式
                    this.form.dateRange = [];
                    this.form.startTimeHMS = '';
                    this.form.endTimeHMS = '';
                    this.form.reportedTimeHMS = '';
                    this.form.collectTimeHMS = '';

                    // 如果已经有单条模式的时间，转换为时分秒
                    if (this.form.startTime) {
                        const time = this.form.startTime.split(' ')[1];
                        this.form.startTimeHMS = time;
                    }
                    if (this.form.endTime) {
                        const time = this.form.endTime.split(' ')[1];
                        this.form.endTimeHMS = time;
                    }
                    if (this.form.reportedTime) {
                        const time = this.form.reportedTime.split(' ')[1];
                        this.form.reportedTimeHMS = time;
                    }
                    if (this.form.collectTime) {
                        const time = this.form.collectTime.split(' ')[1];
                        this.form.collectTimeHMS = time;
                    }

                    // 清除单条模式的校验状态
                    this.$nextTick(() => {
                        this.$refs['form'].clearValidate(['startTime', 'endTime', 'reportedTime', 'collectTime']);
                    });
                } else {
                    // 切换到单条模式
                    // 清除批量模式相关字段
                    delete this.form.dateRange;
                    delete this.form.startTimeHMS;
                    delete this.form.endTimeHMS;
                    delete this.form.reportedTimeHMS;
                    delete this.form.collectTimeHMS;

                    // 清除批量模式的校验状态
                    this.$nextTick(() => {
                        this.$refs['form'].clearValidate([
                            'dateRange',
                            'startTimeHMS',
                            'endTimeHMS',
                            'reportedTimeHMS',
                            'collectTimeHMS',
                        ]);
                    });
                }
            },

            // 格式化日期为 yyyy-MM-dd
            formatDate(date) {
                const year = date.getFullYear();
                const month = String(date.getMonth() + 1).padStart(2, '0');
                const day = String(date.getDate()).padStart(2, '0');
                return `${year}-${month}-${day}`;
            },
        },
    };
</script>
<style lang="scss" scoped>
    .app-container form:first-child .el-select,
    .app-container form:nth-child(2) .el-select,
    .app-container form:nth-child(2) ::v-deep .el-form-item__content,
    .app-container form:first-child ::v-deep .el-form-item__content {
        width: 240px;
    }
    .app-container form:first-child .el-form-item:last-child ::v-deep .el-form-item__content {
        width: auto;
    }
    .app-container form:nth-child(1) ::v-deep .vue-treeselect__control,
    .app-container form:nth-child(2) ::v-deep .vue-treeselect__control {
        height: auto;
        line-height: 30px;
    }
    .el-dialog .pile-input,
    .el-dialog .el-select,
    .el-dialog .el-date-editor,
    .el-dialog .vue-treeselect {
        width: 208px;
    }
    .tableDiv {
        background-color: white;
        padding-bottom: 10px;
    }

    .infoBox {
        padding: 15px 15px 0 15px;
        box-sizing: border-box;
        border-radius: 6px;
        border: 1px solid #c4c4c4;
        position: relative;

        .infoTitle {
            user-select: none;
            position: absolute;
            top: 0;
            left: 0;
            padding: 0 10px;
            font-size: 14px;
            line-height: 14px;
            font-weight: bold;
            transform: translateX(15px) translateY(-50%);
            background-color: white;
        }

        .imgBox {
            height: auto;
            width: 100%;
            display: flex;
            flex-wrap: wrap;
            align-content: flex-start;

            .imgItemBox {
                height: 240px;
                width: calc(100% / 3);
                box-sizing: border-box;
                padding: 10px;
                overflow-y: auto;
                display: flex;
                align-items: center;
                justify-content: center;
                position: relative;

                .imgDeleteBtn {
                    position: absolute;
                    z-index: 1;
                    top: 10%;
                    right: 10%;
                }
            }
        }
    }

    .formDialog {
        ::v-deep .el-dialog__body {
            height: 600px;
            overflow-y: auto;
        }

        .titleBox {
            height: 22px;
            position: relative;
            display: flex;
            flex-direction: row;
            align-items: center;

            .title {
                font-size: 16px;
                color: black;
                margin: 0;
            }

            .subTitle {
                margin-left: 15px;
                font-size: 12px;
                color: #888888;
            }

            .riskLevel {
                user-select: none;
                position: absolute;
                // top: 0;
                right: 5%;
                display: flex;
                align-items: center;
                flex-direction: row;

                .title {
                    font-size: 16px;
                    font-weight: bold;
                }

                .main {
                    font-size: 16px;
                    font-weight: bold;
                    padding: 5px 10px 5px 10px;
                    color: white;
                    box-sizing: border-box;
                    border-radius: 5px;
                }
            }
        }
    }

    .report-dialog {
        ::v-deep .el-dialog {
            margin-top: 5vh !important;
            height: 90vh;

            .el-dialog__body {
                padding: 10px;
                height: calc(90vh - 100px);
                overflow-y: auto;
                scrollbar-width: none;
            }
        }
    }
</style>
