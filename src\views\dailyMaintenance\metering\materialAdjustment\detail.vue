<template>
  <div class="road-interflow-edit">
    <el-row :gutter="15">
      <el-form ref="elForm" :model="formData" :rules="rules" size="medium" label-width="100px">
        <el-row>
          <el-col :span="12">
            <el-form-item label="调差项目" prop="name">
              <el-input v-model="formData.name" placeholder="请输入调差项目" clearable>
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="代号" prop="code">
              <el-input v-model="formData.code" placeholder="请输入代号" clearable>
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="基础单价" prop="basePrice">
              <el-input v-model="formData.basePrice" placeholder="请输入基础单价" clearable>
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="年份" prop="year">
              <el-input v-model="formData.year" placeholder="请输入年份" clearable>
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="单位" prop="unit">
              <el-input v-model="formData.unit" placeholder="请输入单位" clearable>
              </el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <div style="margin-bottom:10px"><span style="color: red">*&nbsp;</span>结算期单价</div>
          <el-col :span="6" v-for="(item, index) in adjustDetailList">
            <el-form-item :label="item.month + '月'" prop="month">
              <el-input v-model="adjustDetailList[index].price" type="number" min="0" placeholder="请输入" clearable>
              </el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-col :span="12">
          <el-form-item label="涉及合同" prop="conId">
            <contract-section v-model="formData.conId" valueType="object" :params="contractParams" placeholder="请选择合同" @change="addContract"></contract-section>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-tabs v-model="editableTabsValue" closable @tab-remove="removeTab">
            <el-tab-pane
                v-for="(item, index) in contractList"
                :key="item.conId"
                :label="item.conName"
                :name="item.conId"
            >
              <el-button type="primary" @click="addScheme(item.conId, index)" style="margin-bottom:10px;float:right">新增</el-button>
              <el-table v-adjust-table
                  :data="item.schemeList"
                  border
                  height="200px"
                  ref="tableRef"
                  style="width: 100%">
                <el-table-column
                    label="序号"
                    align="center"
                    type="index"
                    width="50"
                />
                <el-table-column
                    prop="schemeCode"
                    align="center"
                    label="子目号">
                </el-table-column>
                <el-table-column
                    prop="schemeName"
                    align="center"
                    label="子目名称">
                </el-table-column>
                <el-table-column
                    prop="unit"
                    align="center"
                    label="单位">
                </el-table-column>
                <el-table-column
                    prop="price"
                    align="center"
                    label="单价">
                </el-table-column>
                <el-table-column
                    prop="priceRate"
                    align="center"
                    label="单价占比">
                  <template slot-scope="scope">
                    <el-input v-model="scope.row.priceRate">
                    </el-input>
                  </template>
                </el-table-column>
                <el-table-column
                    prop="field101"
                    align="center"
                    label="操作">
                  <template slot-scope="scope">
                    <el-button
                        size="mini"
                        type="text"
                        @click="handleDelete(scope.row)"
                    >移除
                    </el-button
                    >
                  </template>
                </el-table-column>
              </el-table>
            </el-tab-pane>
          </el-tabs>
        </el-col>
        <el-col :span="24"  style="text-align: right;padding-right: 7.5px;margin-top: 18px">
          <el-button type="primary" @click="onSave">保 存</el-button>
          <el-button @click="onClose">退 出</el-button>
        </el-col>
      </el-form>
    </el-row>
    <el-dialog title="选择报价" destroy-on-close :visible.sync="libModel" width="500px" append-to-body>
      <el-input
          placeholder="输入关键字进行过滤"
          v-model="filterText">
      </el-input>

      <el-tree
          class="filter-tree"
          :data="libData"
          style="height: 300px;overflow-y: scroll"
          show-checkbox
          :props="defaultProps"
          default-expand-all
          :filter-node-method="filterNode"
          ref="tree">
      </el-tree>
      <div style="text-align: right;padding-right: 7.5px">
        <el-button type="primary" @click="checkLib">保 存</el-button>
        <el-button @click="libModel = false">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import {addMaterialAdjust, updateMaterialAdjust} from '@/api/dailyMaintenance/metering/materialAdjustment'
import ContractSection from "@/components/ContractSection/index.vue";
import {getTreeData} from "@/api/contract/quotationSystem";
export default {
  name: "index",
  components: {ContractSection},
  data() {
    return {
      formData: {},
      loading: false,
      adjustDetailList: [],
      contractList: [],
      contractParams: {},
      rules: {
        name: [
          {
            required: true,
            message: '请输入调差项目',
            trigger: 'blur'
          }
        ],
        code: [
          {
            required: true,
            message: '请输入代号',
            trigger: 'blur'
          }
        ],
        basePrice: [
          {
            required: true,
            message: '请输入基础单价',
            trigger: 'blur'
          }
        ],
        year: [
          {
            required: true,
            message: '请输入年份',
            trigger: 'blur'
          }
        ]
      },
      editableTabsValue: '',
      libModel: false,
      filterText: '',
      libData: [],
      treeIndex: 0,
      defaultProps: {
        children: 'children',
        label: 'schemeName'
      },
    }
  },
  props: {
    rowData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  watch: {
    rowData: {
      handler(val) {
        if (val.id) {
          this.formData = {
            id: val.id,
            name: val.name,
            code: val.code,
            basePrice: val.basePrice,
            year: val.year,
            unit: val.unit,
          }
          if (val.adjustSchemeList) {
            val.adjustSchemeList.forEach(item => {
              this.contractList.push({
                conId: item.conId,
                conName: item.conName,
                schemeList: item.schemeList
              })
            })
          }
          this.editableTabsValue = this.contractList[0]?.conId
          this.adjustDetailList = val.adjustDetailList
        } else {
          this.initData()
        }
      },
      immediate: true
    },
  },
  mounted() {
  },
  methods: {
    getDetail(id) {
    },
    onSave() {
      this.loading = true
      this.$refs.elForm.validate(valid => {
        if (!valid) return
        this.formData.adjustDetailList = this.adjustDetailList
        this.formData.adjustSchemeList = []
        for (let i = 0; i < this.contractList.length; i++) {
          this.formData.adjustSchemeList.push(...this.contractList[i].schemeList)
        }
        for (let i = 0; i < this.formData.adjustSchemeList.length; i++) {
          delete this.formData.adjustSchemeList[i].conName
        }
        if (this.formData.id) {
          updateMaterialAdjust(this.formData).then(() => {
            this.$modal.msgSuccess("保存成功")
            this.onClose()
          })
        } else {
          addMaterialAdjust(this.formData).then(() => {
            this.$modal.msgSuccess("新增成功")
            this.onClose()
          })
        }
      })
    },
    addContract(e) {
      if (e) {
        this.formData.conId = ''
        this.editableTabsValue = e.value
        const exists = this.contractList.some(contract => contract.conId === e.value);
        if (!exists) {
          // 如果不存在，则添加到 contractList
          this.contractList.push({
            conId: e.value,
            conName: e.name,
            schemeList: []
          });
        }
      }
    },
    onSubmit() {
      this.loading = true
    },
    onClose() {
      this.$emit("close")
    },
    initData() {
      const months = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12]
      months.forEach(month => {
        this.adjustDetailList.push({
          month,
          price: 0
        })
      })
    },
    removeTab(targetName) {
      let tabs = this.contractList;
      let activeName = this.editableTabsValue;
      if (activeName === targetName) {
        tabs.forEach((tab, index) => {
          if (tab.conId === targetName) {
            let nextTab = tabs[index + 1] || tabs[index - 1];
            if (nextTab) {
              activeName = nextTab.conId;
            }
          }
        });
      }
      this.editableTabsValue = activeName;
      this.contractList = tabs.filter(tab => tab.conId !== targetName);
    },
    addScheme(e, index) {
      this.libData = []
      this.libModel = true
      this.treeIndex = index
      // 获取报价体系
      getTreeData({
        conId: e,
        isConstruction: '是',
        domain: ''
      }).then(res => {
        this.libData = res.rows || []
        // 过滤libData中children为空的
        this.libData = this.libData.filter(item => {
          return item.children && item.children.length > 0
        })
      })
    },
    checkLib() {
      let checkDatas = this.$refs.tree.getCheckedNodes()
      const schemeList = []
      checkDatas = checkDatas.filter(item => {
        return item.nodeType == 2
      })

      checkDatas.forEach(item => {
        schemeList.push({
          conId: this.contractList[this.treeIndex].conId,
          schemeId: item.id,
          schemeCode: item.schemeCode,
          schemeName: item.schemeName,
          unit: item.unit,
          price: item.price,
          priceRate: item.priceRate
        })
      })
      this.contractList[this.treeIndex].schemeList = schemeList
      this.libModel = false
    },
    handleDelete(row) {
      this.contractList[this.treeIndex].schemeList = this.contractList[this.treeIndex].schemeList.filter(item => item.id !== row.id);
    },
  }
}
</script>
<style scoped lang="scss">
.card_title {
  width: 200px;
  text-align: left;
  margin-bottom: 15px;
  font-weight: bold;
}
</style>
<style lang="scss" scoped>
@import "@/assets/styles/business.scss";
</style>
