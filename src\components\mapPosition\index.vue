<template>
  <div class="map-position">
    <el-dialog title="地图定位" :visible.sync="dialogVisible" width="70%" :before-close="handleClose">
      <div class="body">
        <i class="el-icon-close body-close" @click="handleClose"></i>
        <map-view ref="mapRef" :forPosition="true" :padding="[0, 0, 0, 0]"/>
        <!-- <div id="map"></div> -->
        <div class="optional-menu">
          <div v-for="(item, index) in menuList" :key="index" class="menu-list" @click="handleMenuClick(item, index)">
            <img :src="item.icon" alt="" :style="{ border: item.act ? '1px dashed #dddddd' : '' }" />
            <span>{{ item.name }}</span>
          </div>
        </div>
      </div>
    </el-dialog>


  </div>
</template>

<script>
import MapView from '@/views/map/components/mapView.vue'
import { mapState, mapActions } from 'vuex'
import { getListPage } from '@/api/oneMap/layer'
import { addClickFeature, onMeasure, removeLayer, removeAllLayer, getLonLat, gcj02towgs84 } from '@/views/map/components/common/mapFun'
import { WKT } from 'ol/format'
import { Point } from "ol/geom";
import { transform as projTransform } from "ol/proj";

export default {
  name: 'map-position',
  props: {
    dialogVisible: {
      type: Boolean,
      default: false
    },
    data: {
      type: undefined,
      default: () => { }
    }
  },
  components: { MapView },
  data() {
    return {
      open: false,
      menuList: [
        {
          name: '测距',
          icon: require('@/assets/map/ranging.png'),
          value: 4,
          act: false,
        },
        {
          name: '测面',
          icon: require('@/assets/map/surface.png'),
          value: 5,
          act: false,
        },
        // {
        //   name: '绘面',
        //   icon: require('@/assets/map/omei.png'),
        //   value: 6,
        //   act: false,
        // },
        {
          name: '完成',
          icon: require('@/assets/map/complete.png'),
          value: 7,
        },
        {
          name: '清空',
          icon: require('@/assets/map/clear.png'),
          value: 8,
        }
      ],
    }
  },
  async created() {
    await this.getMapLayerList()
    this.addPosition()
  },
  methods: {
    ...mapActions({
      getMapLayer: 'map/getMapLayer'
    }),
    async getMapLayerList() {
      const res = await getListPage({ pageNum: 1, pageSize: 99 })
      if (res.code === 200) {
        let arr = res.rows.filter(
          v => (v.ifShow == 1 || v.ifShow) && v.layerName !== '天地图注记'
        )
        this.getMapLayer(arr)
        this.open = true
      }
    },
    addPosition() {
      setTimeout(() => {
        let shape = this.data.shape
        // gcj02 转 wgs84
        let { lon, lat } = getLonLat(shape);
        let wgs84Coordinates = gcj02towgs84(lon, lat);
        const projectedCoords = projTransform(
          wgs84Coordinates,
          "EPSG:4326",
          "EPSG:3857"
        );
        let point = new Point(projectedCoords);
        let newShape = new WKT().writeGeometry(point);
        let feature = new WKT().readFeature(newShape);
        // let feature = new WKT().readFeature(point, {
        //   dataProjection: 'EPSG:4326',
        //   featureProjection: 'EPSG:3857'
        // })
        let data = {
          ...this.data
        }
        addClickFeature(
          feature,
          data,
          null,
          true,
          true,
          'clickLayer',
          2,
          this.data.name
        )
        window.mapLayer.getView().fit(feature.getGeometry().getExtent(), {
          duration: 300,
          maxZoom: 18
        })
      }, 1000)
    },
    handleClose() {
      this.open = false
      this.$emit('close')
    },
    handleMenuClick(row, index) {
      switch (row.value) {
        case 4: // 测距
          row.act = !row.act;
          // 修改测面、绘面选中状态
          if (row.act) {
            this.menuList[index + 1].act = false;
            this.menuList[index + 2].act = false;
            onMeasure();
          } else {
            window.drawSource.clear()
            window.draw.finishDrawing() // 绘制完成
            window.mapLayer.removeInteraction(window.draw)
            window.draw = null;
          }
          break;
        case 5: // 测面
          row.act = !row.act;
          // 修改测距、绘面选中状态
          if (row.act) {
            this.menuList[index - 1].act = false;
            this.menuList[index + 1].act = false;
            onMeasure('Polygon');
          } else {
            window.drawSource.clear()
            window.draw.finishDrawing() // 绘制完成
            window.mapLayer.removeInteraction(window.draw)
            window.draw = null;
          }
          break;
        case 6: // 绘面
          row.act = !row.act;
          // 修改测距、测面选中状态
          if (row.act) {
            this.menuList[index - 1].act = false;
            this.menuList[index - 2].act = false;
            onMeasure('Polygon', true);
          } else {
            window.drawSource.clear()
            window.draw.finishDrawing() // 绘制完成
            window.mapLayer.removeInteraction(window.draw)
            window.draw = null;
          }
          break;
        case 7: // 完成
          if (window.draw) {
            window.draw.finishDrawing(); // 绘制完成
          }
          break;
        case 8: // 清空
          if (window.draw) {
            window.drawSource.clear()
            window.draw.finishDrawing() // 绘制完成
            // window.mapLayer.removeInteraction(window.draw)
            // window.draw = null;
          }
          break;
      }
    },
  },
  computed: {
    ...mapState({
      layerList: state => state.map.layerList
    })
  }
}
</script>

<style lang="scss" scoped>
@import "@/assets/styles/utils.scss";

.map-position {
  position: relative;
}

::v-deep {
  .el-dialog .el-dialog__header {
    display: none !important;
  }

  .el-dialog .el-dialog__body {
    padding: 0 !important;
  }
}

.body {
  height: 90vh;
  width: 100%;
  position: relative;

  .body-close {
    position: absolute;
    top: 5px;
    right: 5px;
    height: 23px;
    width: 23px;
    z-index: 9999;
    font-size: 21px;
    color: #fff;
    margin: 5px;
    border: 1px solid #909399;
    border-radius: 4px;
    background: #909399;
    cursor: pointer;
  }

  .mar-container {
    position: absolute;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;

    .map-view {
      width: 100%;
      height: 100%;
      cursor: pointer;
    }
  }


  .optional-menu {
    position: absolute;
    top: 10px;
    left: 5px;

    .menu-list {
      width: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
      flex-direction: column;
      margin-bottom: 6px;
      cursor: pointer;

      img {
        width: vwpx(45px);
        height: vwpx(45px);
        margin-bottom: 3px;
      }

      span {
        font-family: Microsoft YaHei, Microsoft YaHei;
        font-weight: 400;
        font-size: vw(13);
        color: #FFFFFF;
      }

      &:active {
        animation: click 2s ease;
      }
    }
  }


}
</style>