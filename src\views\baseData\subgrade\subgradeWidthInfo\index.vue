<template>
  <PageContainer>
    <template slot="search">
      <div style="display: flex; align-items: center; margin: 0">
        <CascadeSelection
          :form-data="queryParams"
          v-model="queryParams"
          types="201"
          multiple
        />
        <rangeInput
          :clearData="clearData"
          :startPlaceholder="'起点桩号'"
          :endPlaceholder="'终点桩号'"
          @startValue="
            (v) => {
              queryParams.startStake = v;
            }
          "
          @endValue="
            (v) => {
              queryParams.endStake = v;
            }
          "
        />

        <div style="min-width: 220px">
          <el-button
            v-hasPermi="['baseData:roadbed:getListPage']"
            type="primary"
            icon="el-icon-search"
            @click="handleQuery"
            >搜索</el-button
          >
          <el-button icon="el-icon-refresh" @click="resetQuery">重置</el-button>
          <el-button
            v-show="!showSearch"
            icon="el-icon-arrow-down"
            circle
            @click="showSearch = true"
          />
          <el-button
            v-show="showSearch"
            icon="el-icon-arrow-up"
            style="
              color: #1890ff;
              border-color: #badeff;
              background-color: #e8f4ff;
            "
            circle
            @click="showSearch = false"
          />
        </div>
      </div>
      <div v-if="showSearch" style="margin-top: 5px">
        <el-select
          v-model="queryParams.operationState"
          placeholder="运营状态"
          clearable
          collapse-tags
          style="width: 170px"
        >
          <el-option
            v-for="dict in dict.type.sys_operation_state"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
        <el-select
          style="margin-left: 20px; width: 170px"
          v-model="queryParams.status"
          placeholder="数据状态"
          clearable
        >
          <el-option
            v-for="dict in dict.type.base_data_state"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>

        <el-input
          style="margin-left: 20px; width: 170px"
          v-model="queryParams.roadbedCode"
          placeholder="路基编码"
          clearable
        />
      </div>
    </template>
    <template slot="header">
      <div class="button-list">
        <el-button
          v-hasPermi="['baseData:roadbed:add']"
          type="primary"
          @click="handleAdd"
          >新增</el-button
        >
        <el-button
          v-hasPermi="['baseData:roadbed:edit']"
          type="primary"
          @click="handleUpdate"
          >编辑</el-button
        >
        <el-button
          v-hasPermi="['baseData:roadbed:delete']"
          type="primary"
          @click="handleDelete"
          >删除</el-button
        >
        <el-button
          v-hasPermi="['baseData:roadbed:getInfoById']"
          type="primary"
          @click="handleView('')"
          >查看</el-button
        >
        <el-button
          v-hasPermi="['baseData:businessStatusRecord:getListPage']"
          type="primary"
          @click="onStatusChange('')"
          >运营状态变更
        </el-button>
        <el-button
          v-hasPermi="['baseData:import:execute']"
          type="primary"
          @click="importAdd"
          >导入新增</el-button
        >
        <el-button
          v-hasPermi="['baseData:import:execute']"
          type="primary"
          @click="importUpdate"
          >导入更新</el-button
        >
        <el-button
          v-hasPermi="['baseData:roadbed:export']"
          type="primary"
          @click="exportList"
          >导出清单</el-button
        >
        <el-button
          v-hasPermi="['baseData:roadbed:genQrCode']"
          type="primary"
          @click="downloadQrcode"
          >二维码下载</el-button
        >
      </div>
    </template>
    <template slot="body">
      <el-table
        v-adjust-table
        ref="table"
        height="100%"
        style="width: 100%"
        v-loading="loading"
        border
        :data="tableData"
        :header-cell-style="{ height: '36px' }"
        :row-style="rowStyle"
        @selection-change="handleSelectionChange"
        @row-click="handleRowClick"
      >
        <el-table-column type="selection" width="50" align="center" />
        <el-table-column
          fixed
          label="序号"
          type="index"
          width="50"
          align="center"
        >
          <template v-slot="scope">
            {{
              scope.$index +
              (queryParams.pageNum - 1) * queryParams.pageSize +
              1
            }}
          </template>
        </el-table-column>
        <el-table-column
          fixed
          label="操作"
          align="center"
          width="50"
        >
          <template slot-scope="scope">
            <el-link
              type="primary"
              :disabled="!scope.row.shape"
              @click.stop="handleLocation(scope.row)"
            >定位</el-link>
          </template>
        </el-table-column>
        <el-table-column
          fixed
          label="路基编码"
          align="center"
          prop="roadbedCode"
          min-width="140"
          show-overflow-tooltip
        />
        <el-table-column
          label="管理处"
          align="center"
          prop="managementMaintenanceName"
          min-width="140"
        />
        <el-table-column
          label="管养分处"
          align="center"
          prop="managementMaintenanceBranchName"
          min-width="140"
        />
        <el-table-column
          label="养护路段"
          align="center"
          prop="maintenanceSectionName"
          min-width="140"
          show-overflow-tooltip
        />
        <el-table-column
          label="路线编码"
          align="center"
          prop="routeCode"
          min-width="140"
          show-overflow-tooltip
        />

        <el-table-column
          label="桩号范围"
          align="center"
          min-width="180"
          show-overflow-tooltip
        >
          <template #default="{ row }">
            <span>{{ formatPile(row.startStake) }}</span>
            <span v-if="row.startStake">~</span>
            <span>{{ formatPile(row.endStake) }}</span>
          </template>
        </el-table-column>
        <el-table-column
          align="center"
          prop="operationState"
          min-width="140"
          label="运营状态"
        >
          <template slot-scope="{ row }">
            <el-link
              :underline="false"
              :type="
                { 1: 'info', 2: 'success', 3: 'danger', 4: 'primary' }[
                  row.operationState
                ]
              "
              @click="handleOperational($event, row)"
            >
              <DictTag
                :value="row.operationState"
                :options="dict.type.sys_operation_state"
              />
            </el-link>
          </template>
        </el-table-column>
        <el-table-column label="数据状态" align="center" min-width="140">
          <template #default="{ row }">
            <el-link
              :type="{ 1: 'info', 2: 'success' }[row.status]"
              :underline="false"
              > 
              <DictTag
                :value="row.status"
                :options="dict.type.base_data_state"
              />
              </el-link
            > 
           
          </template>
        </el-table-column>
        <el-table-column
          align="center"
          prop="direction"
          width="140"
          label="方向"
        >
          <template slot-scope="{ row }">
            <DictTag
              :value="row.direction"
              :options="dict.type.sys_route_direction"
            />
          </template>
        </el-table-column>
        <el-table-column
          label="位置"
          align="center"
          prop="lane"
          min-width="140"
          show-overflow-tooltip
        >
          <template slot-scope="{ row }">
            <DictTag :value="row.lane" :options="dict.type.lane" />
          </template>
        </el-table-column>
        <el-table-column
          label="经度"
          align="center"
          prop="longitude"
          min-width="140"
          show-overflow-tooltip
        />
        <el-table-column
          label="纬度"
          align="center"
          prop="latitude"
          min-width="140"
          show-overflow-tooltip
        />
        <el-table-column
          label="施工里程桩号"
          align="center"
          min-width="140"
          show-overflow-tooltip
        >
          <template #default="{ row }">
            <span>{{ formatPile(row.constructionStake) }}</span>
          </template>
        </el-table-column>
        <el-table-column
          label="统一里程桩号"
          align="center"
          min-width="140"
          show-overflow-tooltip
        >
          <template #default="{ row }">
            <span>{{ formatPile(row.unifiedMileageStake) }}</span>
          </template>
        </el-table-column>
        <el-table-column label="设计宽度" align="center">
          <el-table-column
            label="左幅(m)"
            align="center"
            prop="designWidthLeftPanel"
            min-width="140"
          />
          <el-table-column
            label="右幅(m)"
            align="center"
            prop="designWidthRightPanel"
            min-width="140"
          />
          <el-table-column
            label="整体式(m)"
            align="center"
            prop="designWidthMonolithic"
            min-width="140"
          />
        </el-table-column>
        <el-table-column label="实际宽度" align="center">
          <el-table-column
            label="左幅(m)"
            align="center"
            prop="actualWidthLeftPanel"
            min-width="140"
          />
          <el-table-column
            label="右幅(m)"
            align="center"
            prop="actualWidthRightPanel"
            min-width="140"
          />
          <el-table-column
            label="整体式(m)"
            align="center"
            prop="actualWidthMonolithic"
            min-width="140"
          />
        </el-table-column>

        <el-table-column
          label="坡长(m)"
          align="center"
          prop="slopeLength"
          min-width="140"
          show-overflow-tooltip
        />
        <el-table-column
          label="高度"
          align="center"
          prop="height"
          min-width="140"
          show-overflow-tooltip
        />
        <el-table-column
          label="路基类型"
          align="center"
          prop="roadbedType"
          min-width="140"
          show-overflow-tooltip
        />
        <el-table-column
          label="平台数量"
          align="center"
          prop="numPlatform"
          min-width="140"
          show-overflow-tooltip
        />
        <el-table-column
          label="检查梯"
          align="center"
          prop="ladder"
          min-width="140"
          show-overflow-tooltip
        />
        <el-table-column
          label="防护形式"
          align="center"
          prop="protectType"
          min-width="140"
          show-overflow-tooltip
        />
        <el-table-column
          label="土质"
          align="center"
          prop="soil"
          min-width="140"
          show-overflow-tooltip
        />
        <el-table-column
          label="坡脚长度"
          align="center"
          prop="footLength"
          min-width="140"
          show-overflow-tooltip
        />
        <el-table-column
          label="落台宽度"
          align="center"
          prop="fallingWidth"
          min-width="140"
          show-overflow-tooltip
        />

        <el-table-column
          label="备注"
          align="center"
          prop="remark"
          min-width="140"
          show-overflow-tooltip
        />
        <el-table-column label="图片" align="center">
          <template #default="{ row }">
            <el-link
              :underline="false"
              type="primary"
              :disabled="row.samplePictureId ? false : true"
              @click.stop="previewImg(row)"
              >查看</el-link
            >
          </template>
        </el-table-column>
        <el-table-column
          label="详情"
          fixed="right"
          align="center"
          prop="samplePictureId"
          width="100"
        >
          <template slot-scope="{ row }">
            <el-link
              v-hasPermi="['baseData:roadbed:getInfoById']"
              :underline="false"
              type="primary"
              @click.stop="handleView(row)"
              >查看</el-link
            >
          </template>
        </el-table-column>
      </el-table>
      <pagination
        :total="total"
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
        :pageSizes="[10, 20, 30, 50, 100, 1000]"
        @pagination="getList"
      />
    </template>
    <add-and-edit
      v-if="showAddEdit"
      :forView="forView"
      :formData="formData"
      :title="title"
      :showAddEdit="showAddEdit"
      @close="
        () => {
          showAddEdit = false;
          formData = {};
        }
      "
      @refresh="
        () => {
          showAddEdit = false;
          getList();
        }
      "
    />
    <ImportData
      v-if="showImportAdd"
      :is-update="isUpdate"
      :dialog-visible="showImportAdd"
      :import-base-type="importBaseType"
      :import-type="importType"
      @close="closeImportAdd"
    />
    <MapPosition
      v-if="showMapPosition"
      :dialogVisible="showMapPosition"
      :data="mapPositionData"
      @close="showMapPosition = false"
    />
    <Dialog title="查看图片" width="500px" :show.sync="imgShow">
      <ImagePreview :owner-id="imageUrl" width="100%" height="100%" />
    </Dialog>
  </PageContainer>
</template>

<script>
import {
  getListPage,
  getSubgradeById,
  subgradeDelete,
  importStatic,
  changeLockedStatus,
} from "@/api/baseData/subgrade/baseInfo/index";
import { findFiles } from "@/api/file/index.js";
import { statusDialog } from "@/views/baseData/components/statusDialog/index.js";
import { statusListDialog } from "@/views/baseData/components/statusDialog/list.js";
import Dialog from "@/components/Dialog/index.vue";
import rangeInput from "@/views/baseData/components/rangeInput/index.vue";
import AddAndEdit from "./components/addAndEdit.vue";
import ImportData from "@/views/baseData/components/importData/index.vue";
import CascadeSelection from "@/components/CascadeSelection/index.vue";
import MapPosition from '@/components/mapPosition/index.vue'


export default {
  name: "SubgradeWidthInfo",
  components: {
    rangeInput,
    AddAndEdit,
    ImportData,
    CascadeSelection,
    Dialog,
    MapPosition,
  },
  dicts: [
    "sys_route_direction",
    "sys_operation_state",
    "lane",
    "base_data_state",
  ],
  data() {
    return {
      loading: true,
      showAddEdit: false,
      forView: false,
      title: "",
      formData: {},
      clearData: false,
      showUpload: false,
      importBaseType: "5",
      isUpdate: false,
      importType: 2,
      ids: [],
      single: true,
      multiple: true,
      showSearch: false,
      showImportAdd: false,
      total: 0,
      tableData: null,
      showMapPosition: false,
      mapPositionData: undefined,
      queryParams: {
        pageNum: 1,
        pageSize: 20,
        operationState: "2",
      },
      imgShow: false, // 显示图片
      imageUrl: "", // 图片地址
      selectItem: {},
    };
  },
  watch: {},
  created() {
    this.getList();
  },
  methods: {
    // 获取表格数据
    getList() {
      this.loading = true;
      getListPage(this.queryParams)
        .then((res) => {
          if (res.code === 200) {
            this.tableData = res.rows || [];
            this.total = res.total || 0;
            this.clearData = false;
          }
        })
        .finally(() => {
          this.loading = false;
        });
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id);
      this.selectItem = selection;
    },
    // 表格点击勾选
    handleRowClick(row) {
      row.isSelected = !row.isSelected;
      this.$refs.table.toggleRowSelection(row);
    },
    // 勾选高亮
    rowStyle({ row, rowIndex }) {
      if (this.ids.includes(row.id)) {
        return { "background-color": "#b7daff", color: "#333" };
      } else {
        return { "background-color": "#fff", color: "#333" };
      }
    },
    // 搜索按钮
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    // 重置按钮
    resetQuery() {
      this.clearData = true;
      this.queryParams = {
        pageNum: 1,
        pageSize: 20,
        operationState: "2",
      };
      this.handleQuery();
    },
    // 新增按钮操作
    handleAdd() {
      this.forView = false;
      this.showAddEdit = true;
      this.formData = {};
      this.title = "新增路基数据";
    },
    closeImportAdd(v) {
      this.showImportAdd = false;
      if (v) this.getList();
    },
    // 编辑按钮
    handleUpdate() {
      if (this.ids.length != 1) {
        this.$message.warning("请选择一条数据进行编辑！");
        return;
      } else {
        getSubgradeById(this.ids[0])
          .then((res) => {
            if (res && res.data) {
              this.formData.baseInfoForm = res.data;
              this.showAddEdit = true;
              this.forView = false;
              this.title = "编辑路基数据";
            }
          })
          .finally(() => {
            this.$modal.closeLoading();
          });
      }
    },
    // 表格操作-定位
    handleLocation(row) {
      this.mapPositionData = row
      this.showMapPosition = true
    },
    // 是否锁定按钮
    handleLocked() {
      if (this.ids.length === 0) {
        this.$message.warning("请选择至少一条数据！");
        return;
      }
      this.$modal
        .confirm("确认锁定？")
        .then(() => {
          changeLockedStatus({ ids: this.ids.join() }).then((res) => {
            if (res && res.data) {
              this.getList();
              this.$modal.msgSuccess("操作成功");
            }
          });
        })
        .catch(() => {});
    },
    // 删除按钮
    handleDelete() {
      if (this.ids.length == 0) {
        this.$message.warning("请选择至少一条数据进行删除！");
        return;
      }
      this.$modal
        .confirm("确认删除？")
        .then(() => {
          subgradeDelete(this.ids).then((res) => {
            if (res && res.code == "200") {
              this.getList();
              this.$modal.msgSuccess("删除成功");
            }
          });
        })
        .catch(() => {});
    },
    // 导出清单按钮
    exportList() {
      if (this.ids.length === 0) {
        this.$modal
          .confirm("即将导出所有表格数据，此过程可能花费时间较长，是否继续？")
          .then(() => {
            this.download("/baseData/roadbed/export", this.queryParams, ``, {
              headers: { "Content-Type": "application/json;" },
              parameterType: "body",
            });
          })
          .catch(() => {});
      } else {
        this.$modal
          .confirm(`已选择${this.ids.length}条路基数据，确认导出清单？`)
          .then(() => {
            this.download("/baseData/roadbed/export", { ids: this.ids }, ``, {
              headers: { "Content-Type": "application/json;" },
              parameterType: "body",
            });
          })
          .catch(() => {});
      }
    },
    // 运营状态变更
    onStatusChange() {
      if (this.ids.length !== 1) {
        this.$modal.msgWarning("请选择一条数据！");
        return;
      }
      statusDialog({
        dataId: this.selectItem[0].assetId,
        baseDataType: 5,
      }).then((res) => {
        if (res) {
          this.getList();
        }
      });
    },
    // 表格操作-运营状态
    handleOperational(event, row) {
      event.stopPropagation();
      statusListDialog({ dataId: row.assetId, baseDataType: 5 });
    },
    // 二维码下载
    downloadQrcode() {
      if (this.ids.length === 0) {
        this.$modal
          .confirm(
            "即将下载所有表格的二维码数据，此过程可能花费时间较长，是否继续？"
          )
          .then(() => {
            this.download(
              "/baseData/roadbed/genQrCode",
              this.queryParams,
              `QrCode_${new Date().getTime()}`,
              {
                headers: { "Content-Type": "application/json;" },
                parameterType: "body",
              }
            );
          })
          .catch(() => {});
      } else {
        this.$modal
          .confirm(`已选择${this.ids.length}条路基宽度数据，是否下载二维码？`)
          .then(() => {
            let data = {
              ...this.queryParams,
              ids: this.ids,
            };
            this.download(
              "/baseData/roadbed/genQrCode",
              data,
              `QrCode_${new Date().getTime()}`,
              {
                headers: { "Content-Type": "application/json;" },
                parameterType: "body",
              }
            );
          })
          .catch(() => {});
      }
    },
    // 查看图片
    previewImg(row) {
      this.imgShow = true;
      this.imageUrl = row.samplePictureId;
    },
    // 查看按钮
    handleView(row) {
      if (row) {
        this.ids = [row.id];
      }
      this.title = "查看路基数据";
      if (this.ids.length != 1) {
        this.$message.warning("请选择一条数据！");
        return;
      } else {
        this.forView = true;
        this.$modal.loading();
        getSubgradeById(this.ids[0])
          .then((res) => {
            if (res && res.data) {
              this.formData.baseInfoForm = res.data;
              this.formData.informationForm = res.data;
              this.showAddEdit = true;
            }
          })
          .finally(() => {
            this.$modal.closeLoading();
          });
      }
    },
    // 运营状态变更按钮
    changeStatus() {
      if (this.ids.length != 1) {
        this.$message.warning("请选择一条数据！");
        return;
      } else {
        statusDialog({ dataId: this.ids[0], baseDataType: 5 });
      }
    },
    // 数据导出按钮
    handleExport() {
      if (this.ids.length == 0) {
        this.$message.warning("请选择至少一条数据！");
        return;
      }
      this.download(
        "baseData/roadbed/static/exportById",
        { ids: this.ids.join() },
        `static_${new Date().getTime()}.xlsx`
      );
    },
    // 导入新增按钮
    importAdd() {
      this.isUpdate = false;
      this.showImportAdd = true;
      this.importType = 2;
    },
    //导入更新按钮
    importUpdate() {
      this.isUpdate = true;
      this.showImportAdd = true;
      this.importType = 1;
    },
  },
};
</script>

<style lang="scss" scoped>
::v-deep .el-table .el-table__fixed-body-wrapper{
    top: 72px !important;
}
.button-list {
  border-radius: 4px;
  width: 100%;
  .el-button {
    margin-bottom: 10px;
    margin-right: 10px;
    margin-left: 0;
  }
}
</style>
