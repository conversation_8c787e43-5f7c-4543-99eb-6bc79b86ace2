<template>
  <el-drawer
    :title="formTitle"
    :visible.sync="showForm"
    :direction="'rtl'"
    size="80%"
    :before-close="handleClose">
  <div class="app-container pageBox">
    <div class="btnBox">
      <div class="rightBox" v-if="type=='edit'">
        <el-button type="primary" size="medium" class="submitBtn" @click="formSubmit(true)">提交</el-button>
      </div>
    </div>
    <el-tabs v-model="formTab" >
      <el-tab-pane label="流程表单" name="form">
        <div class="formBox" >
            <component
              :is="listType"
              :form-data="modelData"
              :componentId="componentId"
              :key="listType + componentId"
              style="height: 48vh;"
              v-if="this.formData.length<=1"
            />
            <el-row :gutter="15">
              <el-col :span="12">
                <div class="hisBox" style="margin-bottom: 20px;padding-top: 50px;" v-if="type=='edit'">
                  <el-form
                    ref="subform"
                    :model="formParmas.processVariables"
                    label-width="100px"
                    label-position="right"
                    :rules="rules"
                  >
                    <el-form-item label="审核结果:" prop="approved">
                      <el-select
                        v-model="formParmas.processVariables.approved"
                        placeholder="请选择审核结果"
                        clearable
                        style="width: 90%;"
                      >
                        <el-option
                          v-for="item in [{label: '通过', value: true}, {label: '不通过', value: false}]"
                          :key="item.value"
                          :label="item.label"
                          :value="item.value"
                        >
                        </el-option>
                      </el-select>
                    </el-form-item>
                    <el-form-item label="审批意见:" prop="comment">
                      <el-input
                        type="textarea"
                        placeholder="请输入审批意见"
                        :rows="2"
                        v-model="formParmas.processVariables.comment"
                        maxlength="200"
                        style="width: 90%;"
                        show-word-limit
                      ></el-input>
                    </el-form-item>
                  </el-form>

                </div>
              </el-col>
              <el-col :span="12">
                <div class="hisBox" >
                  <el-tabs v-model="formHisTab" type="border-card">
                    <el-tab-pane label="流转意见" name="his" >
                      <div class="hisMainBox" style="overflow-y: auto;height: 22vh;">
                        <el-timeline reverse>
                          <el-timeline-item
                            v-for="(item,i) in formIdeaList"
                            :key="i"
                            :timestamp="`${ item.startTime}`"
                            color="#5cbb7a"
                            placement="top"
                          >
                            <p>节点：{{item.nodeName}}</p>
                            <p v-if="item.assignee">审核意见：{{ typeof item.content === 'object' ? item.content.comment : ''}}</p>
                            <p v-if="item.assignee">提交人：{{ item.assignee }}</p>
                            <p v-if="item.assignee">提交时间：{{ item.endTime }}</p>
                            <p v-if="!item.assignee">流程结束</p>
                            <p v-if="!item.assignee">结束时间：{{ item.endTime }}</p>
                            <el-card v-if="typeof item.content === 'object' && item.content.operatingRecord">
                              <div class="hisIdeaBox" >
                                  <div class="changeBox" >
                                    <div class="title">表单修改记录</div>
                                    <div class="list" v-if="item.content.operatingRecord.length > 0" v-for="val in item.content.operatingRecord">
                                      <el-tag
                                        type="info"
                                        effect="plain">
                                        {{ val.name }}
                                      </el-tag>
                                      <!-- <p style="font-weight: bold;">{{ val.name }}</p> -->
                                      <p style="margin: 0 10px;">由</p>
                                      <p style="color: #E6A23C">{{ `"${val.oldVal}"` }}</p>
                                      <p style="margin: 0 10px;">改为</p>
                                      <p style="color: #F56C6C">{{ `"${val.newVal}"` }}</p>
                                    </div>
                                    <!-- <div class="none" v-if="item.content.operatingRecord.length === 0">
                                      暂无记录
                                    </div> -->
                                  </div>
                              </div>
                            </el-card>
                          </el-timeline-item>
                        </el-timeline>
                      </div>
                    </el-tab-pane>
                  </el-tabs>
                </div>
              </el-col>
            </el-row>



        </div>
      </el-tab-pane>
      <el-tab-pane label="流程图" name="bpmn">
        <div class="formBox">
          <el-image
            style="width: 100%; height: 100%"
            :src="formBpmnUrl"
            fit="scale-down"
          >
          </el-image>
          <!-- <img :src="formBpmnUrl" style="height: 100%; width: 100%" /> -->
        </div>
      </el-tab-pane>
    </el-tabs>
  </div>
  </el-drawer>
</template>

<script>
// -------------------- 引入 --------------------

// 组件

// API
import { queryIdeaTask, queryBpmnTask } from '@/api/process/task/task' // 流程相关
import { listUser } from "@/api/system/user"; // 用户管理模块
import BridgeForm from './bridgeForm.vue'
import TunnelForm from './tunnelForm.vue'
import CulvertForm from '@/views/baseData/culvert/culvertInfo/detail.vue'
import { auditProcessBaseDataTasks,getAssetInfo } from '@/api/task/index' // 流程相关
import {

  getInfoById,
} from "@/api/baseData/culvert/culvertInfo/index.js";

export default {
  name: "ProcessForm1",
  // 数据字典
  dicts: [],
  // 组件
  components: {BridgeForm,TunnelForm,CulvertForm},

  // -------------------- 变量 --------------------
  props: {
    formData: {
      type:Array,
      default:()=>[]
    },
    showForm: {
      type:Boolean,
      default:false
    },
    type: {
      type: String,
      default: 'edit'
    }
  },

  data() {
    return {
      /**
       * 表单相关
       */
      formTab: 'form', // 表单标签页
      formHisTab: 'his', // 流转意见标签页
      formIdeaTab: 'idea', // 签字意见标签页
      dataList: [], // 表单数据
      formTitle: '', // 表单标题
      formRoute: '', // 表单路由
      modelData: {}, // 表单数据
      formIdeaList: [], // 表单流转意见数据
      listType: 'BridgeForm', // 表单类型
      rules: {
        comment: [
          { required: true, message: '请输入审批意见', trigger: 'blur' }
        ],
        approved: [
          { required: true, message: '请选择审核结果', trigger: 'blur' }
        ]
      },
      componentsList: [
        { key: 'BridgeForm', label: '桥梁信息'},
        { key: 'TunnelForm', label: '隧道信息'},
        { key: 'CulvertForm',label:'涵洞信息'},
      ], // 组件列表
      componentId: '', // 组件数据ID
      formBpmnUrl: '', // 表单流程图
      processParmas:{
        processVariables: {
          comment: '', // 审批意见
          approved: '', // 是否通过
          operatingRecord: '', // 表单修改记录
          variables: {}, // 表单数据
        },
        toTaskDTO:[],
      }, // 流程参数
      formParmas: { // 表单提交参数
        processVariables: {
          comment: '', // 审批意见
          approved: '', // 是否通过
        },
        toTaskDTO:{},
      },

    }
  },
  // -------------------- 方法 --------------------
  mounted () {
    this.initPage()
  },
  methods: {
    /**
     * 页面相关
     */
    // 初始化页面
    async initPage() {
      let obj = this.formData && this.formData[0] ? this.formData[0] : {};
      // let data = await this.getListType(obj.businessKey);
      // let { typeId } = data || { type: '8' };
      // let typeObj = {
      //   8: 'bridge', // 桥梁
      //   10: 'culvert', // 涵洞
      //   194: 'tunnel', // 隧道
      // }
      this.componentId = obj.businessKey

      this.formTitle = `${this.formData[0].title}`
      if(obj.type == '194'){
        this.listType = 'TunnelForm'
      }else if(obj.type == '10'){
        this.listType = 'CulvertForm'
        getInfoById(this.componentId).then((res) => {
          if (res.code == 200) {
            this.modelData = res.data || {};
          }
        })
      }

      // 查询流转意见
      this.queryIdea(this.formData[0].processInstanceId)
      // 查询流程图
      this.queryBpmn(this.formData[0].processInstanceId)
    },

    // 获取数据类型
    getListType(assetId) {
      return new Promise((resolve, reject) => {
        getAssetInfo(assetId).then((res) => {
          if(res.code === 200) {
            resolve(res.data);
          }else {
            resolve(null);
          }
        }).catch(err=>{
          reject(err);
        })
      })
    },

    /**
     * 查询相关
     */
    // 查询流转意见
    queryIdea(id) {
      let vo = {
        processInstanceId: id
      }
      queryIdeaTask(vo).then((res) => {
        if(res.code === 200) {
          this.formIdeaChange(res.data).then((data) => {
            this.formIdeaList = data
          })
        }
      })
    },

    // 查询流程图
    queryBpmn(id) {
      let vo = {
        instanceId: id
      }
      queryBpmnTask(vo).then((res) => {
        if(res) {
          let blob = new Blob([res], { type: 'image/jpg' })
          let url = URL.createObjectURL(blob)
          this.formBpmnUrl = url
        }
      })
    },

    handleClose() {
      this.$emit('close')
    },

    // 查询用户
    async queryUser(id) {
      let vo = {
        pageNum: 1,
        pageSize: 10,
        userId: id
      }
      await listUser(vo).then((res) => {
        if(res.code === 200) {
          return res.rows[0].nickName
        } else {
          return id
        }
      }).catch(() => {
        return id
      })
    },

    /**
     * 表单相关
     */

    // 提交表单
    formSubmit(type) {

      let pass = false
      this.$refs.subform.validate((valid) => {
        if (valid) {
          pass = true
        }
      })

      if (!pass) return

      // if(!this.formParmas.processVariables.comment||!this.formParmas.processVariables.approved) {
      //   this.$message.warning('请输入审批意见和结果')
      //   return
      // }
      // 调用表单方法
      this.$modal.confirm('确认提交？').then(async () => {

        this.$modal.loading("正在提交，请稍候...")
        // 赋予本地参数

        this.processParmas.processVariables.comment = this.formParmas.processVariables.comment
        this.processParmas.processVariables.approved = this.formParmas.processVariables.approved
        this.processParmas.toTaskDTO = this.formData

        // 表单字段修改对比校验

       if(this.processParmas.toTaskDTO.length>0){
          auditProcessBaseDataTasks(this.processParmas).then((res) => {

          if(res.code === 200) {
            this.$message.success(res.msg)
            this.$emit('refresh')
            this.$emit('close')
          }
          this.$modal.closeLoading()
        }).catch(() => {
          this.$modal.closeLoading()
        })

       }
      })
    },



    // 处理意见字段
    formIdeaChange(data) {
      return new Promise((resolve) => {
        for(let i=0; i<data.length; i++) {
          if(data[i].content && data[i].content !== '无内容') {
            data[i].content = JSON.parse(data[i].content)
            if(data[i].content.operatingRecord) {
              data[i].content.operatingRecord = JSON.parse(data[i].content.operatingRecord)
            }
          }
          if(i === data.length - 1) {
            resolve(data)
          }
        }
      })
    },
  },
}
</script>

<style lang="scss" scoped>

.pageBox {
  display: flex;
  flex-direction: column;

  ::v-deep .el-tabs--border-card > .el-tabs__header .el-tabs__item.is-active{
    background-color: #f5f7fa;
    border-right-color: #f5f7fa;
    border-left-color: #f5f7fa;
  }

  .btnBox {
    height: 40px;
    width: 100%;
    display: flex;
    flex-direction: row;

    .leftBox {
      height: 100%;
      width: 50%;
      display: flex;
      flex-direction: row;
      align-items: center;

      .btn {
        margin-right: 15px;
      }

      .title {
        height: 100%;
        font-size: 14px;
        display: flex;
        align-items: center;
      }
    }

    .rightBox {
      height: 100%;
      width: 100%;
      display: flex;
      flex-direction: row;
      justify-content: flex-end;
      align-items: center;

      .submitBtn {
        margin-left: 15px;
      }
    }
  }

  .formBox {
    height: 100%;
    width: 100%;
    box-sizing: border-box;
    padding: 10px;
    display: flex;
    flex-direction: column;

    .mainBox {
      width: 100%;
      box-sizing: border-box;
      padding: 20px;
      margin-bottom: 20px;
      border: 1px solid #DCDFE6;
      box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.12), 0 0 6px 0 rgba(0, 0, 0, 0.04);
    }

    .hisBox {
      width: 100%;
      background: #f5f7fa;
      border-radius: 8px;
      // padding: 15px 5px 0px 5px;;
      height: 28vh !important;
      ::v-deep .el-tabs .el-tabs__header {
        height: 37px;
      }

      ::v-deep .el-tabs--border-card > .el-tabs__header .el-tabs__item.is-active{
        color: black;
      }

      .hisMainBox {
        width: 100%;

        .hisIdeaBox {
          width: 100%;
          display: flex;
          flex-direction: column;

          .boxMain {
            width: 100%;
            display: flex;
            flex-direction: column;

            .changeBox {
              width: 50%;
              box-sizing: border-box;
              padding: 10px;
              border-radius: 5px;
              border: 1px solid #DCDFE6;
              box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.12), 0 0 6px 0 rgba(0, 0, 0, 0.04);
              display: flex;
              flex-direction: column;

              .title {
                padding: 10px;
                user-select: none;
                color: #888888;
                font-size: 14px;
                font-weight: bold;
                border-bottom: 1px solid #DCDFE6;
              }

              .list {
                padding: 10px;
                user-select: none;
                color: #888888;
                font-size: 14px;
                border-bottom: 1px solid #DCDFE6;
                display: flex;
                flex-direction: row;
                flex-wrap: wrap;
                align-items: center;

                p {
                  margin: 0;
                  line-height: 20px;
                }
              }

              .none {
                padding: 10px;
                user-select: none;
                color: #888888;
                font-size: 14px;
              }
            }
          }

          .boxBottom {
            width: 100%;
            font-size: 14px;
            color: #888888;
            display: flex;
            justify-content: space-between;
          }
        }
      }

      ::v-deep .el-tabs {
        height: 100% !important;
      }
    }
  }
}

::v-deep .app-container {
  padding: 0 20px;
}

::v-deep .el-tabs{

}

::v-deep .el-drawer__header {
  margin-bottom: 10px;
}

::v-deep .culvert-info-edit .el-tabs {
  height: auto;
}

::v-deep .el-tabs {
    height: calc(100% - 20px);

    .el-tabs__header {
      height: 40px;
      margin: 0 0 0px;
    }

    .el-tabs__content {
      height: calc(100% - 55px);


      .el-tab-pane {
        height: 100%;
      }
    }
  }


</style>
