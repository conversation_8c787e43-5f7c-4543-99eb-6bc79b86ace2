<template>
  <div class="range-input">
    <!-- <el-input
      v-model="StartValueComputed"
      :placeholder="startPlaceholder"
      :min="0"
      clearable
      style="width:calc(50% - 10px)"
      type="number"
    />  -->
    <el-input-number v-model="StartValueComputed" controls-position="right" :placeholder="startPlaceholder" clearable
      :min="0" style="width:calc(50% - 10px)" :key="timeKey1" />
    -
    <!-- <el-input
      v-model="EndValueComputed"
      :placeholder="endPlaceholder"
      :min="0"
      clearable
      style="width:calc(50% - 10px)"
      type="number"
    /> -->
    <el-input-number v-model="EndValueComputed" controls-position="right" :placeholder="endPlaceholder" clearable
      :min="0" style="width:calc(50% - 10px)" :key="timeKey2" />
  </div>
</template>

<script>
import { tempStructureAddStatic } from '@/api/baseData/tunnel/baseInfo/index';

export default {
  name: 'rangeInput',
  props: {
    startPlaceholder: { type: String, default: '起始桩号' },
    endPlaceholder: { type: String, default: '结束桩号' },
    clearData: { type: Boolean, default: false }
  },
  data() {
    return {
      internalStartValue: '',
      internalEndValue: '',
      timeKey1: 'timeKey1',
      timeKey2: 'timeKey2',
    }
  },
  created() { },
  mounted() {

  },
  methods: {},
  computed: {
    StartValueComputed: {
      get() {
        return !this.internalStartValue ? undefined : this.internalStartValue;
      },
      set(value) {
        this.internalStartValue = value
        this.$emit('startValue', value === "" ? undefined : value)
      }
    },
    EndValueComputed: {
      get() {
        return !this.internalEndValue ? undefined : this.internalEndValue;
      },
      set(value) {
        this.internalEndValue = value
        this.$emit('endValue', value === "" ? undefined : value)
      }
    }
  },
  watch: {
    clearData: {
      handler(val) {
        if (val) {
          this.internalStartValue = ""
          this.internalEndValue = ""

          // 设置一个定时器，解决输入0时，组件不刷新的问题
          setTimeout(() => {
            this.timeKey1 = new Date().getTime() + 'timeKey1'
            this.timeKey2 = new Date().getTime() + 'timeKey2'
          }, 100)
        }
      },
      immediate: true
    }
  }
}
</script>

<style lang="scss" scoped>
.range-input {
  // font-weight: 400;
  // font-size: 14px;
  min-width: 210px;
  margin: 0 20px;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
}

::v-deep {
  .el-input__inner {
    border: 0;
    // height: 26px;
  }

  .el-input-number__increase,
  .el-input-number__decrease {
    border-left: 0 !important;
  }
}
</style>