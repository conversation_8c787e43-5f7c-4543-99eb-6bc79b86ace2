<template>
  <div class="road-interflow-edit">
    <el-row :gutter="15">
      <el-form ref="elForm" :inline="true" :model="formData" :rules="rules" label-width="120px">
        <el-col :span="24">
          <el-form-item label="扣款类型" prop="mtype">
            <dict-select v-model="formData.mtype" type="deduction_type" style="width: 280px;"/>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="扣款金额" prop="fund">
            <el-input v-model="formData.fund" type="number" style="width: 280px;"/>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="是否计算监理费" prop="isSupFund">
            <el-radio-group v-model="formData.isSupFund" size="medium" style="width: 280px;">
              <el-radio v-for="(item, index) in urgentDegreeOptions" :key="index" :disabled="item.disabled"
                        :label="item.value">{{ item.label }}
              </el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
        <el-col :span="24" v-if="formData.isSupFund === 1">
          <el-form-item label="费用类型" prop="dataType">
            <cost-select :type="6" v-model="formData.dataType" style="width: 230px;"/>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="扣款说明" prop="remark">
            <el-input v-model="formData.remark" :rows="3" type="textarea" style="width: 280px;"/>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="附件" prop="fileId">
            <file-upload v-model="formData.fileId" :owner-id="formData.fileId"></file-upload>
          </el-form-item>
        </el-col>
        <el-col :span="24" style="text-align: right;padding-right: 7.5px;margin-top: 18px">
          <el-button type="primary" @click="onSave">保 存</el-button>
          <el-button @click="onClose">退 出</el-button>
        </el-col>
      </el-form>
    </el-row>
  </div>
</template>

<script>
import {addDeduction, editDeduction} from "@/api/regularTesting/metering/settlementApplication"
import CostSelect from "@/components/CostSelect/index.vue";
import {v4 as uuidv4} from "uuid";
export default {
  components: {CostSelect},
  data() {
    return {
      formData: {},
      rules: {
        mtype: [
          {required: true, message: '请选择扣款类型', trigger: 'blur'}
        ],
        dataType: [
          {required: true, message: '请选择费用类型', trigger: 'blur'}
        ],
        fund: [
          {required: true, message: '请输入扣款金额', trigger: 'blur'}
        ],
        isSupFund: [
          {required: true, message: '请选择是否计算监理费', trigger: 'blur'}
        ],
      },
      urgentDegreeOptions: [{
        "label": "是",
        "value": 1
      }, {
        "label": "否",
        "value": 0
      }],
    }
  },
  props: {
    settleId: {
      type: String,
      default: ''
    },
    rowData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  watch: {
    rowData: {
      handler(val) {
        if (val.id) {
          this.formData = {
            id: val.id,
            mtype: val.mtype,
            fund: val.fund,
            isSupFund: val.isSupFund,
            remark: val.remark,
            fileId: val.fileId || uuidv4().replace(/-/g, '').slice(0, 20)
          }
        }
      },
      immediate: true,
      deep: true
    }
  },
  methods: {
    onSave() {
      this.$refs.elForm.validate(valid => {
        if (!valid) return
        this.formData.settleId = this.settleId
        if (
          this.formData.fileId &&
          Array.isArray(this.formData.fileId) &&
          this.formData.fileId.length > 0
        ) {
          this.formData.fileId = this.formData.fileId[0];
        } else if (Array.isArray(this.formData.fileId) &&
          this.formData.fileId.length == 0){
          this.formData.fileId = null
        }
        if (this.formData.isSupFund === 0 && this.formData.dataType) {
          delete this.formData.dataType
        }
        if (this.formData.id) {
          editDeduction(this.formData).then(() => {
            this.$modal.msgSuccess('保存成功')
            this.onClose()
          })
        } else {
          addDeduction(this.formData).then(() => {
            this.$modal.msgSuccess('保存成功')
            this.onClose()
          })
        }
      })
    },
    onClose() {
      this.$emit('close', '4')
    }
  }
}
</script>

<style lang="scss" scoped>

</style>
<style lang="scss" scoped>
@import "@/assets/styles/business.scss";
</style>
