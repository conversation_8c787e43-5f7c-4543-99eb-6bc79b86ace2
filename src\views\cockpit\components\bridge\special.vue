<template>
  <div class="bridge-special">
    <div class="special-c">
      <!-- <MapView :padding="[50, 50, 50, -250]" ref="mpRef"></MapView> -->
      <AMap ref="ampRef" v-on="$listeners"></AMap>
    </div>
    <section class="special-l">
      <CockpitCard title="运营桥梁信息" :w="isBig ? '10vw' : '20vw'" :h="isBig ? 'calc(40vh - 20px)' : 'calc(40vh - 5px)'"
        :class="isBig ? 'mb-2' : 'mb-3'" :isDtl="false">
        <div class="bridge-info">
          <span>桥梁总数量</span>
          <span v-for="(item, index) in bridgeNum" :key="index" class="info-num">
            {{ item }}
          </span>
          <span>座</span>
        </div>
        <Echarts :option="btOption" v-if="btOption" height="16vh" key="btKey" />
        <div class="divider"></div>
        <Echarts :option="sOption" v-if="sOption" height="16vh" key="sKey" />
      </CockpitCard>
      <CockpitCard title="技术状况等级" :w="isBig ? '10vw' : '20vw'" h="calc(19vh - 5px)" :class="isBig ? 'mb-2' : 'mb-3'"
        :isDtl="false">
        <Echarts :option="lvOption" v-if="lvOption" height="18vh" key="lKey" />
      </CockpitCard>
      <CockpitCard title="养护数量趋势" :w="isBig ? '10vw' : '20vw'" h="calc(19vh - 5px)" :isDtl="false">
        <Echarts :option="tcOption" v-if="tcOption" height="18vh" key="tcKey" />
      </CockpitCard>
    </section>
    <section class="special-r">
      <div class="info">
        <div>
          <CockpitCard title="管理处桥梁分布情况" :w="isBig ? '9vw' : '18vw'" :isDtl="false"
            :h="isBig ? 'calc(57vh - 12px)' : 'calc(57vh - 6px)'" :class="isBig ? 'mb-2' : 'mb-3'">
            <Tables :columns="columns" :data="tableData" :isScroll="false" @row-click="onTableClick"></Tables>
          </CockpitCard>
          <CockpitCard title="桥梁养护经费" :w="isBig ? '9vw' : '18vw'" h="calc(26vh - 5px)" :class="isBig ? 'mb-2' : 'mb-3'"
            :isDtl="false">
            <div class="ed-chart">
              <div class="ed-title" :style="{ top: isBig ? '0px' : '2px' }">
                <div class="ed-title-box">
                  日常养护：
                  <span
                    style="display:block;width: 10px;height: 10px;background-color: rgba(118,255,49,0.5);border-radius: 2px; margin-right: 4px;"></span>
                  预算
                  <span
                    style="display:block;width: 10px;height: 10px;background-color: rgba(118,255,49,1);border-radius: 2px;margin: 0 5px;"></span>
                  完成
                </div>
                <div class="ed-title-box" :style="{ paddingTop: isBig ? '4px' : '0px' }">
                  养护工程：
                  <span
                    style="display:block;width: 10px;height: 10px;background-color: rgba(33,155,255,0.5);border-radius: 2px; margin-right: 4px;"></span>
                  预算
                  <span
                    style="display:block;width: 10px;height: 10px;background-color: rgba(33,155,255,1);border-radius: 2px;margin: 0 5px;"></span>
                  完成
                </div>
              </div>
              <Echarts :option="edOption" v-if="edOption" height="25.5vh" :key="edKey" />
            </div>
          </CockpitCard>
        </div>
        <div class="ml-2">
          <CockpitCard title="健康监测" :w="isBig ? '9vw' : '18vw'" h="calc(26vh - 5px)" :class="isBig ? 'mb-2' : 'mb-3'"
            :isDtl="false">
            <Echarts :option="jkOption" v-if="jkOption" height="25.5vh" key="jkKey" />
          </CockpitCard>
          <CockpitCard title="长大桥目录" :w="isBig ? '9vw' : '18vw'" h="calc(26vh - 5px)" :class="isBig ? 'mb-2' : 'mb-3'"
            :isDtl="false">
            <Echarts :option="mlOption" v-if="mlOption" height="25.5vh" key="jkKey" />
          </CockpitCard>
          <CockpitCard title="独柱墩" :w="isBig ? '9vw' : '18vw'" h="calc(26vh - 5px)" :isDtl="false">
            <Echarts :option="dzOption" v-if="dzOption" height="25.5vh" key="jkKey" />
          </CockpitCard>
        </div>
      </div>
    </section>
  </div>
</template>

<script>
import * as echarts from 'echarts'
import { calcFontSize, isBigScreen } from '../../util/utils';
import CockpitCard from '../cockpitCard.vue';
import Echarts from '../echarts/echarts.vue';
import Tables from "../tables.vue";
import MapView from '../mapView.vue';
// import AMap from '../aMap.vue';
import AMap from '@/components/Map/aMap.vue'
import { getBridgeList, getMaintenanceBridgeInfo, getBridgeSumFundInfo, getMaintenanceQuantity, getAllStructureInfoGroupByDomain } from "@/api/cockpit/index";
import cache from "@/plugins/cache";

export default {
  name: 'Special',
  components: {
    CockpitCard,
    Echarts,
    Tables,
    MapView,
    AMap
  },
  props: {
    statisticsData: {
      type: Object,
      default: () => { }
    }
  },
  data() {
    return {
      map: null,
      isBig: isBigScreen(),
      bridgeNum: [],
      btOption: null,
      sOption: null,
      lvOption: null,
      tcOption: null, // 通车桥梁趋势
      columns: [
        {
          label: "序号",
          prop: "index",
          width: 40,
        },
        {
          label: "管理处",
          prop: "managementMaintenanceName",
        },
        {
          label: "特大桥",
          prop: "superMajorBridge",
          width: 50,
        },
        {
          label: "大桥",
          prop: "greatBridge",
          width: 40,
        },
        {
          label: "中桥",
          prop: "mediumBridge",
          width: 40,
        },
        {
          label: "小桥",
          prop: "smallBridge",
          width: 40,
        },
      ],
      tableData: [],
      edOption: null,
      jkOption: null,
      mlOption: null,
      dzOption: null,
      obj: {
        "deptIds": [],
        "id": "1815935561620328449",
        icon: require('@/assets/cockpit/bridge.png'),
        path: './baseData/bridge/baseInfo/archives/index.vue',
        type: 2, // 1：组件 2：路由 3：接口
        "paramsDTO": {
          "precisionParams": {
            "base.span_classify_type": "1", // 跨径分类 ：1、特大桥 2、大桥 3、中桥 4、小桥
          }
        }
      },
      year: new Date().getFullYear(),
      edKey: "edKey"
    }
  },
  async created() {
    window.$Bus.$on('pageSelectYear', async (data) => {
      this.year = data;
      this.initEdEcharts();
    })
  },
  destroyed() {
    window.$Bus.$off('aMapClick')
    window.$Bus.$off('pageSelectYear')
  },
  mounted() {
    this.$nextTick(() => {
      this.initMap();
    })
    // 初始化桥梁类型图表
    this.initBtECharts();
    this.initSECharts();
    // 技术等级
    this.initLvECharts();
    // 通车桥梁趋势-折线图
    this.initTcEcharts();
    // 桥梁养护经费-柱状图
    this.initEdEcharts();
    this.initBarsCharts();
    // 桥梁分布情况-表格数据
    this.initTableData();
  },
  destroyed() {
    if (this.$refs.mpRef) {
      this.$refs.ampRef.removeVector('桥梁');
    }
  },
  methods: {
    // 管理处桥梁分布情况 - 表格点击
    onTableClick(row) {
      if (!row) return;
      let { managementMaintenanceId } = row;
      let localData = cache.session.getJSON("rangeData");
      if (localData && localData.length) {
        let deptObj = localData.find((item) => item.sys_dept_id == managementMaintenanceId);
        if (this.$refs.ampRef) {
          this.$refs.ampRef.setDistrictHight(deptObj);
        }
      }
    },
    initMap() {
      this.$nextTick(() => {
        if (this.$refs.ampRef) {
          this.$refs.ampRef.setVector('桥梁', this.obj)
        }
      })
    },
    // 桥梁类型-饼图
    initBtECharts() {
      var outr = this.isBig ? "83%" : "79%";
      var inr = this.isBig ? '74%' : '74%';
      var size = 13;
      var numberdata = []
      var titledata = []
      var color = []
      let colorObj = {
        特大桥: '#01FBEF',
        大桥: '#00AEFF',
        中桥: '#0154FB',
        小桥: '#518BFF'
      }
      for (let index = 0; index < this.statisticsData.spanClassifyTypes.length; index++) {
        const el = this.statisticsData.spanClassifyTypes[index];
        numberdata.push(el.typeCount)
        titledata.push(el.typeLabel)
        color.push(colorObj[el.typeLabel])
      }

      let sum = numberdata.reduce((a, b) => a + b, 0);
      sum = sum.toString();
      let sumArray = sum ? sum.split('') : null;
      this.bridgeNum = sumArray ? sumArray : ['0'];
      let title = '桥梁类型';
      this.btOption = this.initPieCharts(outr, inr, size, numberdata, titledata, color, title);
    },
    // 主桥上部结构形式-饼图
    initSECharts() {
      var outr = this.isBig ? "83%" : "79%";
      var inr = this.isBig ? '74%' : '74%';
      var size = 13;
      var numberdata = [];
      var titledata = [];
      let color = [];
      let colorObj = {
        空心板梁: '#7C8EFF',
        T梁: '#DD47FF',
        箱形梁: '#8B34FF',
        连续钢构: '#FFE000',
        悬索桥: '#FFA800',
        斜拉桥: '#FF5B00'
      }
      for (let index = 0; index < this.statisticsData.mainSuperstructureTypes.length; index++) {
        const el = this.statisticsData.mainSuperstructureTypes[index];
        if (colorObj[el.typeLabel]) {
          numberdata.push(el.typeCount)
          titledata.push(el.typeLabel)
          color.push(colorObj[el.typeLabel])
        }
      }
      let wrap = this.isBig ? '\n\n\n\n\n' : '\n\n\n';
      let title = `主桥上部${wrap}结构形式`;
      this.sOption = this.initPieCharts(outr, inr, size, numberdata, titledata, color, title);
    },
    initLvECharts() {
      var outr = this.isBig ? "83%" : "79%";
      var inr = this.isBig ? '74%' : '74%';
      var size = 13;
      var numberdata = [];
      var titledata = [];
      let color = [];
      let colorObj = {
        '1类': '#FF9803',
        '2类': '#00DFFF',
        '3类': '#008CFF',
        '4类': '#0154FB',
        '5类': '#518BFF',
        '未评定': '#9151FF'
      }
      for (let index = 0; index < this.statisticsData.techAssessTypes.length; index++) {
        const el = this.statisticsData.techAssessTypes[index];
        if (colorObj[el.typeLabel]) {
          numberdata.push(el.typeCount)
          titledata.push(el.typeLabel)
          color.push(colorObj[el.typeLabel])
        }
      }
      let title = '最新技术状况';

      this.lvOption = this.initPieCharts(outr, inr, size, numberdata, titledata, color, title);
    },
    // 公共的饼图创建
    initPieCharts(outr = 135, inr = 125, size = 13, numberdata = [], titledata = [], color = [], title = '') {
      var total = 0;
      //计算总和
      for (var i = 0; i < numberdata.length; i++) {
        total += Number(numberdata[i]);
      }
      let placeHolderStyle = {
        normal: {
          label: {
            show: false,
          },
          labelLine: {
            show: false,
          },
          color: 'rgba(0, 0, 0, 0)',
          borderColor: 'rgba(0, 0, 0, 0)',
          borderWidth: 0,
        },
      };

      var data = [];
      for (var i = 0; i < numberdata.length; i++) {
        data.push(
          {
            value: numberdata[i],
            name: titledata[i],
            itemStyle: {
              normal: {
                borderWidth: 5,
                shadowBlur: 2,
                borderColor: color[i],
                shadowColor: color[i],
                color: color[i],
              },
            },
          },
          {
            value: total / 30,
            name: '',
            itemStyle: placeHolderStyle,
          }
        );
      }

      var seriesObj = [
        {
          type: "pie",
          zlevel: 0,
          silent: true,
          center: ['30%', '50%'], //此处调整圆环的位置
          radius: ["92%", "95%"],
          hoverAnimation: false,
          color: "rgba(0,62,122,0.9)",
          label: {
            normal: {
              show: false,
            },
          },
          labelLine: {
            normal: {
              show: false,
            },
          },
          data: [1],
        },
        {
          name: '',
          type: 'pie',
          clockWise: false,
          startAngle: '90',
          center: ['30%', '50%'], //此处调整圆环的位置
          radius: [outr, inr], //此处可以调整圆环的大小
          hoverAnimation: false,
          itemStyle: {
            normal: {
              label: {
                show: false,
              },
              labelLine: {
                show: false,
              },
            },
          },
          data: data,
          animationType: 'scale',
          animationEasing: 'elasticOut',
          animationDelay: function (idx) {
            return idx * 50;
          },
        },
        {
          name: '',
          type: 'pie',
          center: ['30%', '50%'],
          radius: ['49%', '49%'],
          itemStyle: {
            color: 'transparant',
          },
          startAngle: '90',
          data: [
            {
              value: total,
              name: '',
              label: {
                normal: {
                  show: true,
                  formatter: '{b| ' + title + '}',
                  rich: {
                    b: {
                      color: 'rgb(255,255,255)',
                      fontSize: this.isBig ? size * 3 : size,
                      lineHeight: this.isBig ? 12 : 5,
                      fontWeight: 'bold',
                    },
                  },
                  textStyle: {
                    fontSize: 28,
                    fontWeight: 'bold',
                  },
                  position: 'center',
                },
              },
            },
          ],
        },
      ];

      let option = {
        backgroundColor: 'rgba(0,0,0,0)',
        tooltip: {
          show: false,
        },
        legend: {
          show: true,
          // right: this.isBig ? '10%' : '5%',
          left: '60%',
          y: 'center',
          icon: "circle",
          itemWidth: this.isBig ? 6 : 3, // 设置宽度
          itemHeight: this.isBig ? 6 : 3, // 设置高度
          itemGap: this.isBig ? 3 : 0,
          formatter: (name) => {
            let d = data.filter(v => v.name == name);
            let num = d[0].value;
            return " {title|" + name + "：}" + "{num|" + num + "} " + "{unit|座}";
          },
          textStyle: {
            color: '#fff',
            fontSize: this.isBig ? size * 2 : size,
            rich: {
              title: {
                color: 'rgba(255,255,255,0.8)',
                fontSize: this.isBig ? size * 2 : size - 1,
                padding: [3, 0],
              },
              num: {
                color: '#fff',
                fontSize: this.isBig ? size * 2 : size,
              },
              unit: {
                color: 'rgba(182,182,182,0.8)',
                fontSize: this.isBig ? size * 2 : size - 1,
              }
            }
          },
          itemWidth: 10,
          itemHeight: 10,
          itemGap: 10,
        },
        toolbox: {
          show: false,
        },
        series: seriesObj,
      };
      return option;
    },
    async initBarsCharts() {
      let arr = [];
      let { data } = await getMaintenanceBridgeInfo()
      arr = data?.childs || []
      let jkArr = arr.filter(v => v.managementMaintenanceName == '健康监测');
      let mlArr = arr.filter(v => v.managementMaintenanceName == '长大桥目录');
      let dzArr = arr.filter(v => v.managementMaintenanceName == '独柱墩');

      // let jkXData = jkArr[0].childs.map(v => v.managementMaintenanceName.replace("管理处", ""));
      // let jkData = jkArr[0].childs.map(v => v.whetherHealthMonitorSystemType);
      let jkXData = [];
      let jkData = [];
      let jkSColor = '#00B9FE';
      let jkEColor = 'rgba(0,185,254,0.1)';

      let jkX = await getAllStructureInfoGroupByDomain();
      if (jkX.code == 200) {
        jkX.result.forEach((item, index) => {
          jkXData.push(item.domainName.replace("管理处", ""))
          jkData.push(item.structureTypeInfoList?.[0]?.structureTypeCount)
        })
      }

      this.jkOption = this.initBarCharts(jkXData, jkData, jkSColor, jkEColor, []);

      let mlXData = mlArr[0].childs.map(v => v.managementMaintenanceName.replace("管理处", ""));
      let mlData = mlArr[0].childs.map(v => v.whetherInLongSpanCatalogType);
      let mlSColor = '#76FF30';
      let mlEColor = 'rgba(118,255,48,0.1)';
      this.mlOption = this.initBarCharts(mlXData, mlData, mlSColor, mlEColor, []);

      let dzXData = dzArr[0].childs.map(v => v.managementMaintenanceName.replace("管理处", ""));
      let dzData = dzArr[0].childs.map(v => v.whetherSingleColumnPierType);
      let dzSColor = '#FEB247';
      let dzEColor = 'rgba(254,178,71,0.1)';
      this.dzOption = this.initBarCharts(dzXData, dzData, dzSColor, dzEColor, []);
    },
    initBarCharts(xData = [], data = [], startColor = '', endColor = '', legend = []) {
      let option = {
        backgroundColor: "rgba(0,0,0,0)",
        grid: {
          left: "3%",
          right: "4%",
          top: "15%",
          bottom: "1%",
          containLabel: true,
        },
        // 添加 tooltip 配置
        tooltip: {
          show: true, // 显示 tooltip
          trigger: 'axis', // 触发类型为坐标轴，当鼠标悬停在柱子上时显示信息
          axisPointer: {
            type: 'shadow' // 坐标轴指示器类型为阴影
          },
          formatter: function (params) {
            let result = `${params[0].name}<br/>`;
            params.forEach(item => {
              result += `${item.seriesName}: ${item.value}k<br/>`;
            });
            return result;
          }
        },
        legend: {
          show: legend.length > 0 ? true : false,
          data: legend,
          x: 'center',
          y: '2%',
          itemWidth: this.isBig ? 20 : 10,
          itemHeight: this.isBig ? 20 : 10,
          itemGap: this.isBig ? 20 : 10,
          textStyle: {
            color: '#ffffff',
            fontSize: this.isBig ? 25 : 14,
          },
        },
       xAxis: [
            {
              type: "category",
              name: "",
              nameTextStyle: {
                color: "#999999",
                algin: "right",
                fontSize: this.isBig ? 25 : calcFontSize(14),
                padding: [6, 0, 0, 0],
                verticalAlign: "top",
              },
              data: xData,
              axisPointer: {
                type: "shadow",
              },
              axisLine: {
                show: true,
                lineStyle: {
                  color: "rgba(153,153,153,0.3)",
                },
              },
              axisLabel: {
                rotate: 50,
                textStyle: {
                  color: "#8B8C8C", //更改坐标轴文字颜色
                  fontSize: this.isBig ? 26 : calcFontSize(12), //更改坐标轴文字大小
                },
                interval: false, //
              },
            },
          ],
        yAxis: {
          name: "",
          nameTextStyle: {
            color: "#999999",
            fontSize: this.isBig ? 25 : 14,
          },
          axisLabel: {
            color: "#999999",
            fontSize: this.isBig ? 25 : 14,
            formatter: '{value}k',
          },
          splitLine: {
            show: true,
            lineStyle: {
              color: "rgba(110,112,121,0.5)",
            },
          },
          splitArea: {
            show: false,
          },
        },
        series: [
          {
            name: legend.length ? legend[0] : "",
            type: "bar",
            barWidth: 10,
            itemStyle: {
              normal: {
                color: new echarts.graphic.LinearGradient(
                  0,
                  0,
                  0,
                  1,
                  [
                    {
                      offset: 0,
                      color: startColor,
                    },
                    {
                      offset: 0.8,
                      color: endColor,
                    },
                  ],
                  false
                ),
              },
            },
            data,
          },
        ],
      };
      return option;
    },
    initTcEcharts() {

      getMaintenanceQuantity({ dataType: 8 }).then(res => {
        let list = [];
        if (res.data.length > 10) {
          list = res.data.slice(-10)
        } else {
          list = res.data
        }

        let xData = list.map(v => v.year);
        let yData = list.map(v => v.quantity);

        var fontColor = '#8B8C8C';
        this.tcOption = {
          backgroundColor: 'rgba(0,0,0,0)',
          grid: {
            left: '5%',
            right: '5%',
            top: '16%',
            bottom: '3%',
            containLabel: true
          },
          tooltip: {
            show: true,
            trigger: 'item'
          },
          legend: {
            show: false
          },
          xAxis: [
            {
              type: 'category',
              name: '',
              nameTextStyle: {
                color: "#999999",
                algin: 'right',
                fontSize: this.isBig ? 25 : 14,
                padding: [6, 0, 0, -12],
                verticalAlign: 'top',
              },
              boundaryGap: false,
              axisLabel: {
                color: fontColor,
                rotate: 45,
                interval: false,
                textStyle: {
                  fontSize: this.isBig ? 25 : calcFontSize(12),
                },
              },
              axisLine: {
                show: true,
                lineStyle: {
                  color: 'rgba(28,255,188,0.04)'
                }
              },
              axisTick: {
                show: false,
              },
              splitLine: {
                show: false,
                lineStyle: {
                  color: '#195384'
                }
              },
              data: xData
            }
          ],
          yAxis: [
            {
              type: 'value',
              name: '',
              axisLabel: {
                formatter: '{value}座',
                textStyle: {
                  color: '#999999',
                  fontSize: this.isBig ? 25 : 14,
                }
              },
              axisLine: {
                lineStyle: {
                  color: '#27b4c2'
                }
              },
              axisTick: {
                show: false,
              },
              splitLine: {
                show: true,
                lineStyle: {
                  color: 'rgba(110,112,121,0.5)',
                }
              },
              splitArea: {
                show: false,
              }
            },
          ],
          series: [
            {
              name: '',
              type: 'line',
              smooth: true,
              symbol: 'emptyCircle',
              symbolSize: 8,
              emphasis: {
                focus: 'series'
              },
              itemStyle: {
                normal: {
                  color: '#1CFFBC',
                  lineStyle: {
                    width: 1
                  },
                  areaStyle: {
                    color: new echarts.graphic.LinearGradient(0, 1, 0, 0, [{
                      offset: 0,
                      color: 'rgba(28,255,188,0.2)'
                    }, {
                      offset: 1,
                      color: 'rgba(28,255,188,0.9)'
                    }]),
                  }
                }
              },
              markPoint: {
                itemStyle: {
                  normal: {
                    color: 'red'
                  }
                }
              },
              data: yData
            },
          ]
        };

      })


    },
    // 桥梁养护经费
    initEdEcharts() {
      let xData = [
        "保山",
        "大理",
        "昆明东",
        "昆明西",
        "丽江",
        "临沧",
        "普洱",
        "曲靖",
        "文山",
        "西双版纳",
        "昭通",
        "红河",
      ]
      let rcyhData1 = Array(xData.length).fill(0) // 日常养护预算
      let rcyhData2 = Array(xData.length).fill(0) // 日常养护完成
      let yhgcData1 = Array(xData.length).fill(0) // 专项养护预算
      let yhgcData2 = Array(xData.length).fill(0) // 专项养护完成
      let index = 0;
      getBridgeSumFundInfo({ year: this.year }).then((res) => {
        if (res.code === 200 && res.rows) {
          res.rows.map(el => {
            el.domainName = el.domainName.replace("管理处", "")
          })
          res.rows.forEach(item => {
            const regionIndex = xData.indexOf(item.domainName);
            if (regionIndex !== -1) { // 如果找到了对应的地区
              switch (item.type) {
                case "日常养护预算额度":
                  rcyhData1[regionIndex] = Number((item.sumFund / 1000).toFixed(2)) // 填充日常养护完成
                  break
                case "日常养护完成额度":
                  rcyhData2[regionIndex] = Number((item.sumFund / 1000).toFixed(2)) // 填充日常养护完成
                  break
                case "专项养护预算额度":
                  yhgcData1[regionIndex] = Number((item.sumFund / 1000).toFixed(2)) // 填充专项养护预算
                  break
                case "专项养护完成额度":
                  yhgcData2[regionIndex] = Number((item.sumFund / 1000).toFixed(2)) // 填充专项养护完成
                  break
                default:
                  break
              }
            }
          });
        }
        this.edOption = {
          tooltip: {
            trigger: "axis",
            axisPointer: {
              // 坐标轴指示器，坐标轴触发有效
              type: "shadow", // 默认为直线，可选为：'line' | 'shadow'
            },
          },
          legend: [
            {
              show: false,
              x: '50%',
              y: '0%',
              itemWidth: this.isBig ? 30 : 20,
              itemHeight: this.isBig ? 15 : 10,
              textStyle: {
                color: '#ffffff',
                fontSize: this.isBig ? 20 : 12,
              },
              formatter: (name) => {
                index++
                return name
              },
              data: ["完成", "预算 "],
            },
            {
              show: false,
              x: '50%',
              y: '7%',
              itemWidth: this.isBig ? 30 : 20,
              itemHeight: this.isBig ? 15 : 10,
              textStyle: {
                color: '#ffffff',
                fontSize: this.isBig ? 20 : 12,
              },
              formatter: (name) => {
                index++
                return name
              },
              data: ["预算", "完成 "],
            },
          ],
          grid: {
            top: '15%',
            left: "3%",
            right: "4%",
            bottom: "2%",
            containLabel: true,
          },
          xAxis: [
            {
              type: "category",
              data: xData,
              splitLine: {
                show: false
              },
              axisLine: {
                lineStyle: {
                  color: "#3f7fb2",
                },
              },
              axisLabel: {
                rotate: 50,
                interval: false, // 强制显示所有标签
                textStyle: {
                  color: "#8B8C8C",
                  fontSize: this.isBig ? 25 : calcFontSize(12),
                },
              },
            },
          ],
          yAxis: [
            {
              type: "value",
              name: "",
              nameTextStyle: {
                color: "#999999",
                fontSize: this.isBig ? 25 : 14,
              },
              min: 0,
              axisTick: {
                show: false,
              },
              axisLine: {
                lineStyle: {
                  color: "rgba(110,112,121,0.5)",
                },
              },
              axisLabel: {
                textStyle: {
                  color: "#999999",
                  fontSize: this.isBig ? 25 : 14,
                },
                formatter: '{value}k'
              },
              splitLine: {
                show: true,
                lineStyle: {
                  color: "rgba(110,112,121,0.5)",
                }
              },
              splitArea: {
                show: false,
              }
            },
          ],
          series: [
            {
              name: "完成",
              type: "bar",
              stack: "日常养护",
              data: [...rcyhData2],
              itemStyle: {
                normal: {
                  color: "#76FF31",
                },
              },
            },
            {
              name: "预算 ",
              type: "bar",
              stack: "日常养护",
              data: rcyhData1,
              itemStyle: {
                normal: {
                  color: "rgba(118,255,49,0.5)",
                },
              },
            },

            {
              name: "完成 ",
              type: "bar",
              stack: "养护工程",
              data: yhgcData2,
              itemStyle: {
                normal: {
                  color: "#219BFF",
                },
              },
            },
            {
              name: "预算",
              type: "bar",
              stack: "养护工程",
              data: yhgcData1,
              itemStyle: {
                normal: {
                  color: "rgba(33,155,255,0.5)",
                },
              },
            },
          ],
        };
        this.edKey = new Date().getTime()
      })
    },
    // 桥梁分布情况-表格
    initTableData() {
      getBridgeList().then((res) => {
        this.tableData = res.data || []
      })
    }
  }
}
</script>

<style lang="scss" scoped>
@import "@/assets/styles/utils.scss";

.bridge-special {
  width: 100%;
  height: 100%;
  position: relative;
  margin-top: vwpx(15px);

  .special-c {
    width: 100%;
    height: 91vh;
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
  }

  .mb-2 {
    margin-bottom: vwpx(20px);
  }

  .mb-3 {
    margin-bottom: vwpx(30px);
  }

  .ml-2 {
    margin-left: vwpx(20px);
  }

  .special-l {
    position: absolute;
    left: vwpx(30px);
    top: 0;


    .bridge-info {
      width: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
      margin: vwpx(20px) 0;
      padding-top: vwpx(16px);

      span {
        font-family: Source Han Sans, Source Han Sans;
        font-weight: 400;
        font-size: vwpx(32px);
        color: #01FBEF;
        text-align: left;
        font-style: normal;
        text-transform: none;
        margin: 0 vwpx(10px);
      }

      .info-num {
        background: rgba(4, 17, 48, 0.4);
        box-shadow: inset 0px 0px 6px 0px #0154FB;
        border: 1px solid #009DFF;
        margin: 0 vwpx(5px);
        padding: vwpx(6px) vwpx(12px);
        color: #FFFFFF;
        font-size: vwpx(34px);
      }
    }

    .divider {
      width: 90%;
      margin: vwpx(16px) 5%;
      border: 1px dotted rgba(156, 189, 255, 0.5);
    }
  }

  .special-r {
    position: absolute;
    right: vwpx(30px);
    top: 0;

    .info {
      display: flex;

      .ed-chart {
        position: relative;

        .ed-title {
          width: 100%;
          font-size: 1.3vh;
          color: #FFFFFF;
          position: absolute;
          flex-shrink: 0;

          display: flex;
          align-items: center;
          justify-content: center;
          flex-direction: column;

          span {
            margin: vwpx(4px) 0;
            font-size: 1.1vh;
          }

          .ed-title-box {
            display: flex;
            align-items: center;
            flex-shrink: 0;
          }
        }
      }
    }
  }
}
</style>