<template>
  <div class="road-interflow-edit" style="padding: 20px" v-loading="loading">
    <el-row :gutter="20">
      <el-form
        ref="elForm"
        :model="formData"
        :rules="rules"
        :disabled="readonly"
        size="medium"
        label-width="130px"
      >
        <el-col :span="24">
          <div class="card_title">1.基本信息</div>
        </el-col>
        <el-col :span="8">
          <el-form-item label="管养单位" prop="domainId">
            <selectTree
              :key="'domainId'"
              v-model="formData.domainId"
              :deptType="100"
              :deptTypeList="[1, 3, 4]"
              clearable
              filterable
              placeholder="管养单位"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="年度" prop="year">
            <el-date-picker
              v-model="formData.year"
              placeholder="年度"
              style="width: 100%"
              type="year"
              value-format="yyyy"
            >
            </el-date-picker>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="路段名称" prop="maiSecId">
            <RoadSection
              ref="roadSection"
              v-model="formData.maiSecId"
              :deptId="formData.domainId"
              placeholder="路段"
              style="width: 100%"
              @change="changeMaiSecId"
            />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="选择预算" prop="xzys">
            <el-input style="width: 100%" placeholder="选择预算"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="项目名称" prop="name">
            <el-input
              style="width: 100%"
              v-model="formData.name"
              placeholder="项目名称"
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="项目编码" prop="code">
            <el-input
              style="width: 100%"
              v-model="formData.code"
              placeholder="项目编码"
            ></el-input>
          </el-form-item>
        </el-col>

        <el-col :span="8">
          <el-form-item label="计划资金(元)" prop="sumFund">
            <el-input
              style="width: 100%"
              type="number"
              v-model="formData.sumFund"
              placeholder="计划资金(元)"
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="工程类型" prop="projectType">
            <cost-select :type="6"
              v-model="formData.projectType"
              placeholder="工程类型"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="合同" prop="conId">
            <contract-section
              v-model="formData.conId"
              valueType="value"
              :params="contractFilterMap"
              placeholder="请选择合同"
              @change="contractChange"
              ref="contractSectionRef"
            ></contract-section>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="构造物类型">
            <dict-select
              v-model="formData.structureType"
              type="structure_type"
              placeholder="构造物类型"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="立项理由" prop="reason">
            <el-input
              type="textarea"
              rows="2"
              v-model="formData.reason"
              :style="{ width: '100%' }"
              placeholder="立项理由"
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="备注" prop="remark">
            <el-input
              type="textarea"
              rows="2"
              v-model="formData.remark"
              :style="{ width: '100%' }"
              placeholder="备注"
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <div class="card_title">2.合同清单</div>
        </el-col>
        <el-col :span="24">
          <el-button
            type="primary"
            style="margin-bottom: 10px; float: right"
            @click="addContractItem"
            >新增</el-button
          >
          <methods-list
            :value.sync="contractObj[formData.conId]"
          ></methods-list>
        </el-col>
        <el-col :span="24">
          <div class="card_title" style="margin-top: 15px">3.构造物</div>
        </el-col>
        <el-col :span="24">
          <el-button
            type="primary"
            style="margin-bottom: 10px; float: right"
            @click="addStructure"
            >新增</el-button
          >
          <el-table v-adjust-table :data="gzList" border height="200px" style="width: 100%">
            <el-table-column prop="routeCode" align="center" label="路线编码">
            </el-table-column>

            <el-table-column prop="name" align="center" label="构造物名称">
              <template slot-scope="scope">
                {{ scope.row.name }}
              </template>
            </el-table-column>
            <el-table-column prop="direction" align="center" label="上下行">
              <template slot-scope="scope">
                <dict-select
                  type="route_direction"
                  clearable
                  v-model="scope.row.direction"
                  style="width: 100%"
                ></dict-select>
              </template>
            </el-table-column>
            <el-table-column prop="beginMile" align="center" label="起点桩号">
              <template slot-scope="scope">
                k
                <el-input-number
                  v-model="scope.row.beginMile1"
                  :min="0"
                  :controls="false"
                  :precision="0"
                  style="width:40%"
                  @change="changeMile(scope.row)"
                ></el-input-number>
                <div
                  style="width: 10%; text-align: center; display: inline-block"
                >
                  +
                </div>
                <el-input-number
                  v-model="scope.row.beginMile2"
                  :min="0"
                  :controls="false"
                  :precision="3"
                  style="width:40%"
                  @change="changeMile(scope.row)"
                ></el-input-number>
              </template>
            </el-table-column>
            <el-table-column prop="endMile" align="center" label="终点桩号">
              <template slot-scope="scope">
                k
                <el-input-number
                  v-model="scope.row.endMile1"
                  :min="0"
                  :controls="false"
                  :precision="0"
                  style="width:40%"
                  @change="changeMile(scope.row)"
                ></el-input-number>
                <div
                  style="width: 10%; text-align: center; display: inline-block"
                >
                  +
                </div>
                <el-input-number
                  v-model="scope.row.endMile2"
                  :min="0"
                  :controls="false"
                  :precision="3"
                  style="width:40%"
                  @change="changeMile(scope.row)"
                ></el-input-number>
              </template>
            </el-table-column>
            <el-table-column
              prop="roadSectionLength"
              align="center"
              label="长度"
            >
              <template slot-scope="scope">
                {{
                  Math.abs(
                    (
                      (scope.row.endMile || 0) - (scope.row.beginMile || 0)
                    ).toFixed(2)
                  )
                }}
              </template>
            </el-table-column>
            <el-table-column prop="field101" align="center" label="操作">
              <template slot-scope="scope">
                <el-button
                  size="mini"
                  type="text"
                  @click="handleDeleteGzw(scope)"
                  >移除
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-col>
        <el-col :span="24" class="mt10">
          <div style="text-align: right">
            <el-button type="primary" @click="handleSave">保存</el-button>
            <el-button @click="close">取消</el-button>
          </div>
        </el-col>
      </el-form>
    </el-row>

    <methods-tree
      ref="methodsTree"
      :domain-id="formData.domainId"
      :con-id="formData.conId"
      v-model="methodsTreeSelectData"
      :loading="loading"
    ></methods-tree>

    <el-dialog
      title="选择构造物"
      destroy-on-close
      :visible.sync="structureModel"
      width="65%"
      append-to-body
      v-if="structureModel"
    >
      <asset-select
        :assetType="assetType"
        @checkAsset="checkAsset"
        :mai-sec-id="formData.maiSecId"
      ></asset-select>
    </el-dialog>
  </div>
</template>

<script>
import selectTree from "@/components/DeptTmpl/selectTree.vue";
import RoadSection from "@/views/baseData/components/roadSection/index.vue";
import ContractSection from "@/components/ContractSection/index.vue";
import assetSelect from "@/components/AssetSelect/index.vue";
import MethodsList from "@/components/MethodsList/index.vue";
import MethodsTree from "@/components/MethodsTree/index.vue";
import {
  addProject,
  editProject,
} from "@/api/regularTesting/projectManage/projectInfo";
import CostSelect from "@/components/CostSelect/index.vue";

export default {
  components: {
    CostSelect,
    assetSelect,
    ContractSection,
    RoadSection,
    selectTree,
    MethodsList,
    MethodsTree,
  },
  data() {
    return {
      loading: false,
      formData: {
        year: String(new Date().getFullYear()),
      },
      rules: {
        domainId: [
          { required: true, message: "管养单位不能为空", trigger: "change" },
        ],
        year: [{ required: true, message: "年度不能为空", trigger: "change" }],
        maiSecId: [
          { required: true, message: "路段名称不能为空", trigger: "change" },
        ],
        name: [
          { required: true, message: "项目名称不能为空", trigger: "blur" },
        ],
        code: [
          { required: true, message: "项目编码不能为空", trigger: "blur" },
        ],
        sumFund: [
          { required: true, message: "计划资金不能为空", trigger: "blur" },
        ],
        projectType: [
          { required: true, message: "工程类型不能为空", trigger: "change" },
        ],
        conId: [{ required: true, message: "请选择合同", trigger: "change" }],
        reason: [
          { required: true, message: "立项理由不能为空", trigger: "blur" },
        ],
      },
      contractFilterMap: {
      },
      defaultProps: {
        children: "children",
        label: "schemeName",
      },
      structureModel: false,
      assetType: "34",
      gzList: [],
      isFirst: false,
      contractObj: {},
      methodsTreeSelectData: [],
    };
  },
  props: {
    rowData: {
      type: Object,
      default: {},
    },
    readonly: {
      type: Boolean,
      default: false,
    },
  },
  watch: {
    rowData: {
      handler(val) {
        if (val.id) {
          this.isFirst = true;
          this.formData = JSON.parse(JSON.stringify(val));
          if (
            this.formData.projectStructureList &&
            this.formData.projectStructureList.length > 0
          ) {
            this.gzList = [...this.formData.projectStructureList];
          }
          this.formData.year = String(this.formData.year);
          this.formData.domainId = String(this.formData.domainId);
          this.gzList.forEach((item) => {
            item.laneArr = item.lane?.split(",") || [];
            item.beginMile1 = parseInt((item.beginMile || 0) / 1000);
            item.beginMile2 = parseInt((item.beginMile || 0) % 1000);
            item.endMile1 = parseInt((item.endMile || 0) / 1000);
            item.endMile2 = parseInt((item.endMile || 0) % 1000);
          });
            this.contractObj[this.formData.conId] =
              this.formData.projectDetailList || [];
        }

      },
      immediate: true,
      deep: true,
    },
    "formData.structureType": {
      handler(val) {
        if (val == "0") {
          this.assetType = "31";
        } else if (val == "1") {
          this.assetType = "32";
        } else {
          this.assetType = "34";
        }
        if (!this.isFirst) {
          this.gzList = [];
        }
      },
    },
    methodsTreeSelectData: {
      async handler(val) {
        if (val.length > 0) {
          for (let index = 0; index < val.length; index++) {
            const item = val[index];
            const hasFlag = this.contractObj[this.formData.conId].some(
              (value) => {
                return value.schemeId === item.id;
              }
            );
            if (hasFlag) {
              try {
                await this.$confirm(
                  `存在与子目号${item.schemeCode}相同的数据，是否确认加入?`,
                  "确认",
                  {
                    confirmButtonText: "确定",
                    cancelButtonText: "取消",
                    type: "warning",
                    distinguishCancelAndClose: true,
                  }
                );
                this.addScheme(item);
              } catch (error) {
                continue;
              }
            } else {
              this.addScheme(item);
            }
          }
        }
      },
      immediate: true,
      deep: true,
    },
  },
  methods: {
    addScheme(item) {
      const curContractLength = this.contractObj[this.formData.conId].length;
      this.contractObj[this.formData.conId].push({
        schemeId: item.id,
        schemeCode: item.schemeCode,
        schemeName: item.schemeName,
        price: item.price,
        unit: item.unit,
        isProduction: item.safetyFeeFlag,
        order: curContractLength + 1,
      });
    },
    contractChange() {
      if (
        this.formData.conId &&
        !this.contractObj.hasOwnProperty(this.formData.conId)
      ) {
        this.contractObj[this.formData.conId] = [];
      }
    },
    addContractItem() {
      if (!this.formData.conId || !this.formData.domainId) {
        this.$message.error("请先选择合同和管养单位");
        return;
      }
      this.$refs.methodsTree.openLibModel();
    },

    addStructure() {
      if (!this.formData.maiSecId || !this.formData.structureType) {
        this.$message.error("请先选择路段和造物类型");
        return;
      }
      this.structureModel = true;
    },
    checkAsset(data) {
      // 如果data.id 再gzList中不存在 则插入到gzList
      const exists = this.gzList.some((item) => item.id === data.id);
      if (!exists) {
        const gzListLength = this.gzList.length;
        if (data.startStake) {
          this.$set(data, 'beginMile1', parseInt(data.startStake / 1000) || 0)
          let index1 = parseInt(data.startStake / 1000) == 0 ? 0 : parseInt(data.startStake / 1000).toString().length
          this.$set(data, 'beginMile2',  data.startStake.toString().substring(index1))
        }
        if (data.endStake) {
          this.$set(data, 'endMile1', parseInt(data.endStake / 1000) || 0)
          let index2 = parseInt(data.endStake / 1000) == 0 ? 0 : parseInt(data.endStake / 1000).toString().length
          this.$set(data, 'endMile2',  data.endStake.toString().substring(index2))
        }
        data.direction = 0
        data.order = gzListLength + 1;
        delete data.id
        this.gzList.push(data);
      }
      this.structureModel = false;
    },
    changeMile(row) {
        this.$set(row, 'beginMile', (row.beginMile1 || 0) * 1000 + parseInt(row.beginMile2 || 0))
        this.$set(row, 'endMile', (row.endMile1 || 0) * 1000 + parseInt(row.endMile2 || 0))
    },
    handleDeleteGzw(e) {
      this.gzList.splice(e.$index, 1)
      this.gzList.forEach((item, index) => {
        item.order = index + 1;
      });
      this.$forceUpdate();
    },
    handleSave() {
      this.$refs.elForm.validate((valid) => {
        if (!valid) return;
        if (
          this.contractObj[this.formData.conId] &&
          this.contractObj[this.formData.conId].length > 0
        ) {
          this.formData.projectDetailList =
            this.contractObj[this.formData.conId];
        } else {
          delete this.formData.projectDetailList;
        }
        if (this.gzList && this.gzList.length > 0) {
          this.formData.projectStructureList = this.gzList;
          this.formData.projectStructureList.forEach((item) => {
            if (item.lane) item.lane = item.laneArr.join(",");
          });
        } else {
          delete this.formData.projectStructureList;
        }

        this.loading = true;
        if (this.formData.id) {
          editProject(this.formData).then((res) => {
            this.loading = false;
            this.$message.success("保存成功");
            this.close();
          });
        } else {
          addProject(this.formData).then((res) => {
            this.loading = false;
            this.$message.success("保存成功");
            this.close();
          });
        }
      });
    },
    changeMaiSecId() {
      if (!this.isFirst) {
        this.$refs.contractSectionRef.selectValue = ''
      }
      this.isFirst = false;
      const sectionName = this.$refs.roadSection.getLabel(
        this.formData.maiSecId
      );
      this.contractFilterMap = {
        sectionName,
        type: "3",
      };
    },
    close() {
      this.$emit("close");
    },
  },
};
</script>
<style lang="scss" scoped>
.card_title {
  width: 200px;
  text-align: left;
  margin-bottom: 15px;
  font-weight: bold;
}
::v-deep {
  .el-tabs__header {
    padding-left: 20px;
    border: 0;
  }

  .el-tabs__content {
    border: 0;
  }

  .el-form-item__label {
    font-family: Microsoft YaHei, Microsoft YaHei;
    font-weight: 400;
    font-size: 14px;
    color: #333333;
  }

  .el-input.is-disabled .el-input__inner {
    background-color: white;
    border-color: #dfe4ed;
    color: #1d2129;
  }

  .el-textarea.is-disabled .el-textarea__inner {
    background-color: white;
    border-color: #dfe4ed;
    color: #1d2129;
  }
  .el-range-editor.is-disabled {
    background-color: white;
    border-color: #dfe4ed;
    color: #1d2129;
  }
  .el-range-editor.is-disabled input {
    background-color: white;
    border-color: #dfe4ed;
    color: #1d2129;
  }
}
</style>
<style lang="scss" scoped>
@import "@/assets/styles/business.scss";
</style>
