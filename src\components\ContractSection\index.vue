<template>
  <div v-loading="loading" class="contract-road">
    <el-select
        v-model="selectValue"
        clearable
        filterable
        style="width: 100%"
        v-bind="$attrs"
        @change="changeSelect"
    >
      <template v-if="valueType === 'value'">
        <el-option
            v-for="item in contractOptions"
            :key="item.id"
            :label="item.name"
            :value="item.id"
        />
      </template>
      <template v-else>
        <el-option
            v-for="item in contractOptions"
            :key="item.id"
            :label="item.name"
            :value="{name: item.name, value: item.id}"
        />
      </template>
    </el-select>
  </div>
</template>

<script>
import {getListAll} from "@/api/contract/info/index"

export default {
  props: {
    value: {
      type: Object,
      default: ''
    },
    valueType: {
      type: String,
      default: 'value'
    },
    params: {
      type: Object,
      default: () => {}
    }
  },
  watch: {
    params: {
      handler(val) {
        if (val) {
          this.getOptions(val)
        }
      },
      immediate: true,
      deep: true
    },
    "params.conDomainId": {
      handler(newVal, oldVal) {
        if (oldVal && oldVal != newVal) {
          this.$emit('input', '')
        }
      },
      immediate: true,
      deep: true
    }
  },
  data() {
    return {
      contractOptions: [], // 路段数据
      loading: false,
    };
  },
  computed: {
    selectValue: {
      get: function () {
        return this.value
      },
      set: function (val) {
        this.$emit('input', val)
      }
    }
  },
  created() {
  },
  mounted() {
    // this.getOptions({})
  },
  methods: {
    getOptions(params) {
      params.isEnable = 0
      if (params.conDomainId) delete params.type
      getListAll(params).then((res) => {
        if (res.code == 200) {
          this.contractOptions = res.rows || []
        }
      });
    },
    changeSelect(e) {
      this.$emit('update:value', e)
      this.$emit('change', e)
    }
  },
};
</script>

<style lang="scss" scoped></style>
