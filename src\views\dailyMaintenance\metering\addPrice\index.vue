<template>
  <div class="app-container maindiv">
    <el-row :gutter="20">
      <!--部门数据-->
      <el-col :span="relaNav ? 5 : 0" :xs="24" class="leftDiv">
        <!--折叠图标-->
        <div class="leftIcon" @click="relaNav = false">
          <span class="el-icon-caret-left"></span>
        </div>
        <div class="head-container">
          <el-input
              v-model="keyword"
              clearable
              placeholder="输入关键词检索"
              prefix-icon="el-icon-search"
              size="small"
              style="margin-bottom: 20px"
              @change="handleSearch"
          />
        </div>
        <div class="head-container" style="width: 300px">
          <el-tree
              ref="tree"
              :data="filteredTreeData"
              :default-expanded-keys="[0]"
              :expand-on-click-node="false"
              :filter-node-method="filterNode"
              highlight-current
              node-key="id"
              @node-click="handleNodeClick"
          >
          </el-tree>
        </div>
      </el-col>
      <!--角色数据-->
      <el-col :span="relaNav ? 19 : 24" :xs="24">
        <!--展开图标-->
        <div v-show="!relaNav" class="rightIcon" @click="relaNav = true">
          <span class="el-icon-caret-right"></span>
        </div>
        <el-row>
          <el-col :span="24" :xs="24">
            <el-row>
              <el-form
                  ref="queryForm"
                  :inline="true"
                  :model="queryParams"
                  label-width="68px"
                  size="mini"
              >
                <el-form-item label="" prop="year">
                  <el-date-picker
                      v-model="queryParams.year"
                      placeholder="年份"
                      style="width: 240px"
                      type="year"
                      value-format="yyyy"
                  >
                  </el-date-picker>
                </el-form-item>
                <el-form-item>
                  <selectTree
                      :key="'domainId'"
                      v-model="queryParams.domainId"
                      :deptType="100" :deptTypeList="[1, 3, 4]"
                      clearable
                      filterable
                      placeholder="管养单位"
                      style="width: 240px"
                  />
                </el-form-item>
                <el-form-item>
                  <selectTree
                      :key="'conDomainId'"
                      v-model="queryParams.conDomainId" :data-rule="false"
                      :dept-type="100"
                      placeholder="施工单位"
                      :filter-keys="['云南省交通投资建设集团有限公司', '云南交投投资有限公司']"
                      :expand-all="false"
                      clearable
                      filterable
                      style="width: 240px"
                  />
                </el-form-item>
                <el-form-item>
                  <RoadSection v-model="queryParams.maiSecId" :deptId="queryParams.domainId" placeholder="路段"/>
                </el-form-item>
                <el-form-item>
                  <RouteCodeSection v-model="queryParams.routeCode" :maintenanceSectionId="queryParams.maiSecId"
                                    placeholder="路线编码" style="width: 240px;"/>
                </el-form-item>
                <el-form-item>
                  <dict-select v-model="queryParams.assetMainType" clearable :level="1"
                               placeholder="请选择资产类型"
                               style="width: 240px" type="sys_asset_type"></dict-select>
                </el-form-item>
                <el-form-item>
                  <el-select v-model="queryParams.disType" filterable placeholder="请选择事件类型" clearable style="width: 240px;">
                    <el-option v-for="item in advicesList"
                               :key="item.value"
                               :label="item.label"
                               :value="item.value">
                    </el-option>
                  </el-select>
                </el-form-item>
                <el-form-item>
                  <dict-select v-model="queryParams.calcStatus" clearable placeholder="请选择计量状态"
                               style="width: 240px"
                               type="testing_calc_status"></dict-select>
                </el-form-item>
                <el-form-item>
                  <el-input v-model="queryParams.stageName" placeholder="事件阶段" style="width: 240px"></el-input>
                </el-form-item>
                <el-form-item>
                  <el-button icon="el-icon-search" size="mini" type="primary" @click="handleQuery">搜索</el-button>
                  <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
                  <el-button v-show="!showSearch" circle icon="el-icon-arrow-down" @click="showSearch=true"></el-button>
                  <el-button v-show="showSearch" circle icon="el-icon-arrow-up" @click="showSearch=false"></el-button>
                </el-form-item>
              </el-form>
              <!--默认折叠-->
              <el-col :span="24">
                <el-form v-show="showSearch" ref="queryForm" :inline="true" :model="queryParams" label-width="68px"
                         size="small">
                  <el-form-item>
                    <el-input v-model="queryParams.code" placeholder="施工单编码" style="width: 240px"></el-input>
                  </el-form-item>
                  <el-form-item>
                    <el-input v-model="queryParams.schemeCode" placeholder="子目号" style="width: 240px"></el-input>
                  </el-form-item>
                  <el-form-item>
                    <cost-select :type="9" v-model="queryParams.costType" clearable
                                 placeholder="请选择费用类型" style="width: 240px"
                                 ></cost-select>
                  </el-form-item>
                  <el-form-item>
                    <el-date-picker
                        v-model="queryParams.realEDate"
                        end-placeholder="完工日期"
                        range-separator="至"
                        start-placeholder="完工日期"
                        style="width: 240px"
                        type="daterange"
                        value-format="yyyy-MM-dd">
                    </el-date-picker>
                  </el-form-item>
                  <el-form-item>
                    <dict-select v-model="queryParams.isGuarantee" clearable
                                 placeholder="请选择是否计算安全保通费" style="width: 240px"
                                 type="bridge_simple_bool"></dict-select>
                  </el-form-item>
                  <el-form-item>
                    <dict-select v-model="queryParams.isProduction" clearable
                                 placeholder="请选择是否计算安全生产费" style="width: 240px"
                                 type="bridge_simple_bool"></dict-select>
                  </el-form-item>
                  <el-form-item>
                    <el-input v-model="queryParams.intermediateCode" placeholder="中间计量单编码"
                              style="width: 240px"></el-input>
                  </el-form-item>
                  <el-form-item>
                    <el-input v-model="queryParams.settleCode" placeholder="结算计量单编码"
                              style="width: 240px"></el-input>
                  </el-form-item>
                  <el-form-item>
                    <dict-select v-model="queryParams.isCalc" clearable
                                 placeholder="请选择能否计量" style="width: 240px"
                                 type="bridge_simple_bool"></dict-select>
                  </el-form-item>
                </el-form>
              </el-col>
            </el-row>
          </el-col>
        </el-row>
        <!--操作按钮区开始-->
        <el-row :gutter="10" class="mb8">
<!--          <el-col :span="1.5">-->
<!--            <el-button-->
<!--                icon="el-icon-delete"-->
<!--                size="mini"-->
<!--                type="danger"-->
<!--            >撤回到验收登记-->
<!--            </el-button-->
<!--            >-->
<!--          </el-col>-->
          <el-col :span="1.5">
            <el-button
                icon="el-icon-view"
                size="mini"
                type="success"
                @click="handleOpenEvent"
            >事件信息
            </el-button
            >
          </el-col>
          <el-col :span="1.5">
            <el-button
                icon="el-icon-download"
                size="mini"
                type="warning"
                v-has-menu-permi="['settlement:repository:export']"
                @click="exportList"
            >导出清单
            </el-button
            >
          </el-col>
          <el-col :span="1.5">
            <el-button
                icon="el-icon-view"
                size="mini"
                type="primary"
                v-has-menu-permi="['settlement:repository:visapreview']"
                @click="previewVisa"
            >签证单预览
            </el-button
            >
          </el-col>
          <el-col :span="1.5">
            <el-button
                icon="el-icon-view"
                size="mini"
                type="success"
                v-has-menu-permi="['settlement:repository:maintenancepreview']"
                @click="previewMaintenance"
            >维修档案预览
            </el-button
            >
          </el-col>
          <el-col :span="1.5">
            <el-button
              icon="el-icon-download"
              size="mini"
              type="primary"
              v-has-menu-permi="['settlement:repository:constructiondownload']"
              @click="archivesDownload"
            >施工档案下载
            </el-button
            >
          </el-col>
          <el-col :span="1.5">
            <el-button type="primary" v-has-menu-permi="['calcdaliy:settle:updatevisacheck']" icon="el-icon-edit" size="mini" @click="openEditUserInfoDialog"
            >人员信息修改
            </el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button
              type="warning"
              icon="el-icon-view"
              size="mini"
              v-has-permi="['settlement:repository:regenerate']"
              @click="handleRegenerate"
            >重新生成报表
            </el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button
              type="danger"
              icon="el-icon-refresh-left"
              size="mini"
              v-has-permi="['calcdaliy:settle:withdraw']"
              @click="handleWithdraw"
            >撤回
            </el-button>
          </el-col>
          <el-col :span="1.5" class="mb10">
            <el-button
              type="success"
              icon="el-icon-edit"
              size="mini"
              v-has-permi="['settlement:update:costType']"
              @click="updataCostType"
            >修改费用类型
            </el-button>
          </el-col>
          <right-toolbar
              :columns="columns"
              :showSearch.sync="showSearch"
              @queryTable="handleQuery"
          ></right-toolbar>
        </el-row>
        <el-row>
          <div class="draggable">
            <el-table v-adjust-table
                ref="dataTable"
                v-loading="loading"
                :data="tableData"
                :height="
                showSearch ? 'calc(90vh - 330px)' : 'calc(90vh - 270px)'
              "
                border
                highlight-current-row
                :expand-row-keys="expandedRowKeys"
                row-key="settleId"
                size="mini"
                stripe
                style="width: 100%"
                @row-click="handleClickRow"
                @selection-change="handleSelectionChange"
                @expand-change="loadData"
            >
              <el-table-column type="expand">
                <template slot-scope="props">
                  <el-table v-adjust-table :data="props.row.methodList" style="width: 100%">
                    <el-table-column
                        align="center"
                        label=""
                        width="100"
                    />
                    <el-table-column
                        align="center"
                        label="序号"
                        type="index"
                        width="50"
                    />
                    <el-table-column
                        align="center"
                        label="子目号"
                        prop="schemeCode">
                    </el-table-column>
                    <el-table-column
                        align="center"
                        label="养护方法"
                        prop="schemeName">
                    </el-table-column>
                    <el-table-column
                        align="center"
                        label="计算式"
                        prop="calcDesc">
                    </el-table-column>
                    <el-table-column
                        align="center"
                        label="方法数量"
                        prop="num">
                    </el-table-column>
                    <el-table-column
                        align="center"
                        label="方法单位"
                        prop="unit">
                    </el-table-column>
                    <el-table-column
                        align="center"
                        label="单价"
                        prop="price">
                    </el-table-column>
                    <el-table-column
                        align="center"
                        label="金额"
                        prop="amount">
                    </el-table-column>
                  </el-table>
                </template>
              </el-table-column>
              <el-table-column align="center" type="selection" width="50"/>
              <el-table-column
                  align="center"
                  label="序号"
                  type="index"
                  width="50"
              />
              <template v-for="(column,index) in columns">
                <el-table-column v-if="column.visible"
                                 :label="column.label"
                                 :prop="column.field"
                                 :width="column.width"
                                 align="center">
                  <template slot-scope="scope">
                    <dict-tag v-if="column.dict" :options="dict.type[column.dict]" :value="scope.row[column.field]"/>
                    <template v-else-if="column.slots">
                      <RenderDom :index="index" :render="column.render" :row="scope.row"/>
                    </template>
                    <span v-else-if="column.isTime">{{ parseTime(scope.row[column.field], '{y}-{m}-{d}') }}</span>
                    <span v-else>{{ scope.row[column.field] }}</span>
                  </template>
                </el-table-column>
              </template>
              <el-table-column
                  align="center"
                  class-name="small-padding fixed-width"
                  fixed="right"
                  label="操作"
                  width="250"
              >
                <template slot-scope="scope">
<!--                  <el-button-->
<!--                      icon="el-icon-edit"-->
<!--                      size="mini"-->
<!--                      type="text"-->
<!--                      @click="handleEdit (scope.row)"-->
<!--                  >修改-->
<!--                  </el-button>-->
                  <el-button
                      icon="el-icon-s-promotion"
                      size="mini"
                      type="text"
                      v-has-menu-permi="['settlement:safetyFee:edit']"
                      @click="handleUpdate (scope.row)"
                  >更新是否计算安全费
                  </el-button>
                  <el-button
                      icon="el-icon-edit"
                      size="mini"
                      type="text"
                      v-has-menu-permi="['settlement:repository:edit']"
                      @click="handleUpdateList (scope.row)"
                  >编辑清单
                  </el-button>
                </template>
              </el-table-column>
            </el-table>
            <pagination
                v-show="total>0"
                :limit.sync="queryParams.pageSize"
                :page.sync="queryParams.pageNum"
                :total="total"
                @pagination="handleQuery"
            />
          </div>
        </el-row>
      </el-col>
    </el-row>
    <el-dialog :close-on-click-modal="false" :title="drawerTitle" :visible.sync="drawer" destroy-on-close width="80%">
      <detail :row-data="rowData" @close="handleCloseDetail"></detail>
    </el-dialog>
    <el-dialog
        v-if="dialogVisible"
        :close-on-click-modal="false"
        :close-on-press-escape="false"
        :destroy-on-close="true"
        :visible.sync="dialogVisible"
        title="事件信息"
        width="90%">
      <event-detail :daliy-id="checkRow.constructionId" :dis-id="checkRow.disId"/>
    </el-dialog>
    <el-dialog :close-on-click-modal="false" :visible.sync="updateDialog" destroy-on-close title="修改计算类型"
               width="500px">
      <div class="road-interflow-edit">
        <el-row :gutter="15">
          <el-form
            ref="updateForm"
            :model="rowData"
            size="medium"
            label-width="180px"
            :inline="true"
          >
            <el-col :span="24">
              <el-form-item label="是否计算安全保通费" prop="isGuaranteeTemp">
                <dict-select
                  type="bridge_simple_bool"
                  v-model="rowData.isGuaranteeTemp"
                  placeholder="请选择是否计算安全保通费"
                  style="width: 125%"
                ></dict-select>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="是否计算安全生产费" prop="isProductionTemp">
                <dict-select
                  type="bridge_simple_bool"
                  v-model="rowData.isProductionTemp"
                  placeholder="请选择是否计算安全生产费"
                  style="width: 125%"
                ></dict-select>
              </el-form-item>
            </el-col>
          </el-form>
          <div style="text-align: right">
            <el-button type="primary" size="mini" @click="handleUpdateSafetyFee">保存</el-button>
          </div>
        </el-row>
      </div>
    </el-dialog>

    <el-dialog :close-on-click-modal="false" :visible.sync="updateListDialog" destroy-on-close title="编辑清单"
               width="80%">
      <el-row :gutter="15">
        <el-col :span="24">
          <el-table v-adjust-table
              ref="tableRef"
              v-loading="loading"
              :data="methodList"
              border
              height="400px"
              style="width: 100%">
            <el-table-column
                align="center"
                label="序号"
                type="index"
                width="50"
            />
            <el-table-column
                align="center"
                label="子目号"
                prop="schemeCode"
                width="100">
            </el-table-column>
            <el-table-column
                align="center"
                label="子目名称"
                prop="schemeName"
                width="200">
              <template slot-scope="scope">
                <el-input v-if="scope.row.status != 1" v-model="scope.row.schemeName">
                </el-input>
                <span v-else>{{ scope.row.schemeName }}</span>
              </template>
            </el-table-column>
            <el-table-column
                align="center"
                label="单位"
                prop="unit"
                width="100">
              <template slot-scope="scope">
                <el-input v-if="scope.row.status != 1" v-model="scope.row.unit">
                </el-input>
                <span v-else>{{ scope.row.unit }}</span>
              </template>
            </el-table-column>
            <el-table-column
                align="center"
                label="单价"
                prop="price"
                width="100">
              <template slot-scope="scope">
                <el-input v-if="scope.row.status != 1" v-model="scope.row.price">
                </el-input>
                <span v-else>{{ scope.row.price }}</span>
              </template>
            </el-table-column>
            <el-table-column
                align="center"
                label="计算式"
                prop="calcDesc"
                width="200">
              <template slot-scope="scope">
                <el-input v-if="scope.row.status != 1" v-model="scope.row.calcDesc" @change="changeCalculation(scope.row)">
                </el-input>
                <span v-else>{{ scope.row.calcDesc }}</span>
              </template>
            </el-table-column>
            <el-table-column
                align="center"
                label="方法数量"
                prop="num"
                width="100">
              <template slot-scope="scope">
                <el-input v-if="scope.row.status != 1" v-model="scope.row.num" @change="changeSchemeNum(scope.row)">
                </el-input>
                <span v-else>{{ scope.row.num }}</span>
              </template>
            </el-table-column>
            <el-table-column
                align="center"
                label="资金"
                prop="amount"
                width="100">
            </el-table-column>
            <el-table-column
                align="center"
                label="备注"
                prop="remark"
                width="200">
              <template slot-scope="scope">
                <el-input v-if="scope.row.status != 1" v-model="scope.row.remark">
                </el-input>
                <span v-else>{{ scope.row.remark }}</span>
              </template>
            </el-table-column>
            <el-table-column
                align="center"
                label="操作"
                prop="field101">
              <template slot-scope="scope">
                <el-button
                    size="mini"
                    type="text"
                    v-has-menu-permi="['settlement:detail:edit']"
                    :disabled="scope.row.status == 1"
                    @click="handleDelete(scope.row)"
                >移除
                </el-button>
                <el-button
                  size="mini"
                  type="text"
                  v-has-menu-permi="['settlement:detail:delete']"
                  :disabled="scope.row.status == 1"
                  @click="handleCheckLib(scope.row)"
                >选择
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-col>
        <el-col :span="24">
          <div style="text-align: right;margin-top: 10px; ">
            <el-button size="mini" type="primary" @click="handleSave">保存</el-button>
            <el-button size="mini" type="danger" @click="updateListDialog = false">退出</el-button>
          </div>
        </el-col>
      </el-row>
    </el-dialog>
    <el-dialog
      title="编辑人员信息"
      destroy-on-close
      :visible.sync="userInfoDialog"
      :close-on-click-modal="false"
      size="50%"
    >
      <el-form  ref="userInfoRef" :model="userInfo" :rules="userInfoRules">
        <el-form-item label="验收人员" prop="visaBy" label-width="100px">
          <el-cascader
            v-model="userInfo.visaBy"
            :options="deptUserOptions"
            :props="visaProps"
            :show-all-levels="false"
            ref="visaRef"
            filterable
            clearable
            collapse-tags
            style="width: 100%"
          ></el-cascader>
        </el-form-item>
        <el-form-item label="审核人" prop="visaCheckBy" label-width="100px">
          <el-cascader
            ref="visaCheckRef"
            v-model="userInfo.visaCheckBy"
            :options="deptUserOptions"
            :props="visaCheckProps"
            :show-all-levels="false"
            filterable
            clearable
            collapse-tags
            style="width: 100%"
          ></el-cascader>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button size="small" @click="userInfoDialog = false">取 消</el-button>
        <el-button size="small" type="primary" @click="saveUserInfo">确 定</el-button>
      </span>
    </el-dialog>
    <IFramePreview ref="iframeRef" :srcdoc="preview.html" :down-url="preview.url" :file-name="preview.fileName"></IFramePreview>
    <methods-tree scheme-type="日常养护" isSingle :con-id="rowData.conConId" ref="methodsRef" @input="checkLib" :domain-id="rowData.domainId"></methods-tree>
    <el-dialog :close-on-click-modal="false" :visible.sync="costTypeVisible" title="修改费用类型" width="500px">
      <el-form ref="costTypeRef" :model="costTypeForm" label-width="80px">
        <el-form-item label="费用类型">
          <cost-select :type="9" v-model="costTypeForm.costType" style="width: 80%"/>
        </el-form-item>
        <el-form-item style="text-align: right">
          <el-button type="primary" @click="saveCostType">保存</el-button>
        </el-form-item>
      </el-form>
    </el-dialog>
  </div>
</template>

<script>
import Detail from "./detail.vue";
import EventInfo from "../../component/eventTreeInfo.vue";
import selectTree from "@/components/DeptTmpl/selectTree.vue";
import {
  deleteMethodList, downloadConstruction,
  fetchMethodList, updateSafetyFee,
  fetchRepositoryList, preViewMaintenance, previewVisa,
  updateMethodList, getVisaCheck, updateVisaCheck, regenerateReport, withdraw, updateCostType
} from "@/api/dailyMaintenance/metering/addPrice"
import {listAllDiseases} from "@/api/patrol/diseases";
import {findUserDeptMaintenanceList2} from "@/api/system/maintenanceSection";
import RoadSection from "@/views/baseData/components/roadSection/index.vue";
import RouteCodeSection from "@/components/RouteCodeSection/index.vue";
import Luckysheet from "@/components/Luckysheet/index.vue";
import axios from "axios";
import {getToken} from "@/utils/auth";
import IFramePreview from "@/components/IFramePreview/index.vue";
import EventDetail from "@/views/dailyMaintenance/component/eventDetail.vue";
import MethodsTree from "@/components/MethodsTree/index.vue";
import {getTreeStruct} from "@/api/tmpl";
import CostSelect from "@/components/CostSelect/index.vue";
import { Decimal } from 'decimal.js';

export default {
  name: 'AddPrice',
  components: {
    CostSelect,
    EventDetail,
    IFramePreview,
    Luckysheet,
    RouteCodeSection,
    RoadSection,
    selectTree,
    EventInfo,
    Detail,
    MethodsTree,
    RenderDom: {
      functional: true,
      props: {
        row: Object,
        index: Number,
        render: Function
      },
      render(createElement, ctx) {
        const {row, index} = ctx.props
        return ctx.props.render(row, index)
      }
    }
  },
  dicts: ['calc_type', 'cost_name', 'sys_asset_type', 'testing_calc_status', 'settlement_status', 'bridge_simple_bool'],
  props: [],
  data() {
    return {
      leftTotal: 1,
      showSearch: false,
      queryParams: {},
      total: 0,
      loading: false,
      columns: [
        {key: 0, width: 150, field: 'status', label: `状态`, visible: true, dict: 'settlement_status'},
        {key: 1, width: 100, field: 'costType', label: `费用类型`, visible: true, dict: 'cost_name'},
        {key: 3, width: 100, field: 'maiSecName', label: `路段`, visible: true},
        {key: 3, width: 100, field: 'calcStatus', label: `计量状态`, visible: true, dict: 'testing_calc_status'},
        {key: 2, width: 100, field: 'assetMainTypeName', label: `资产类型`, visible: true, dict: 'sys_asset_type'},
        {key: 3, width: 100, field: 'sumFund', label: `总费用`, visible: true, slots: true, render: (row) => {
            return (
                <span>{row.sumFund?.toFixed(0)}</span>
            );
          }},
        {key: 4, width: 100, field: 'unsettledFund', label: `未结算费用`, visible: true, slots: true, render: (row) => {
            return (
                <span>{row.unsettledFund?.toFixed(0)}</span>
            );
          }},
        {key: 5, width: 100, field: 'supFund', label: `安全监理费`, visible: true, slots: true, render: (row) => {
            return (
                <span>{row.supFund?.toFixed(0)}</span>
            );
          }},
        {key: 6, width: 100, field: 'productionFund', label: `安全生产费`, visible: true, slots: true, render: (row) => {
            return (
                <span>{row.productionFund?.toFixed(0)}</span>
            );
          }},
        {key: 7, width: 100, field: 'guaranteeFund', label: `安全保通费`, visible: true, slots: true, render: (row) => {
            return (
                <span>{row.guaranteeFund?.toFixed(0)}</span>
            );
          }},
        {key: 8, width: 150, field: 'disTypeName', label: `事件类型`, visible: true},
        {key: 9, width: 200, field: 'name', label: `施工单名称`, visible: true},
        {key: 10, width: 200, field: 'code', label: `施工单编码`, visible: true,
          slots: true,
          render: (row, index) => {
            if (row.code) {
              return (
                <el-link type="primary" style="font-size: 12px;" onClick={()=> this.routerChange(row)}>{row.code}</el-link>
              )
            } else {
              return ''
            }
          }},
        {key: 11, width: 200, field: 'conDomainName', label: `施工单位`, visible: true},
        {key: 12, width: 100, field: 'isCalc', label: `是否计量`, visible: true, dict: 'bridge_simple_bool'},
      ],
      tableData: [],
      methodList: [],
      selectIds: [],
      rowData: {},
      expandedRowKeys: [],
      drawerTitle: '修改',
      drawer: false,
      updateDialog: false,
      dialogVisible: false,
      updateListDialog: false,
      // 左侧组织树
      relaNav: true,
      keyword: '',
      relaOptions: [],
      filteredTreeData: [],
      deleteIds: [],
      preview: {
        html: '',
        url: '',
        fileName: ''
      },
      settleId: '',
      checkRow: {},
      advicesList: [],
      checkIndex: 0,
      deptUserOptions: [],
      visaProps: {
        multiple: true, //是否多选
        value: "id",
        emitPath: false,
      },
      visaCheckProps: {
        multiple: false, //是否多选
        value: "id",
        emitPath: false,
      },
      userInfoDialog: false,
      userInfo: {
        id: '',
        visaBy: '',
        visaCheckBy: '',
        visaCheckName: '',
        visaName: ''
      },
      userInfoRules: {
        visaBy: [
          {required: true, type: 'array', message: '请选择验收人员'}
        ],
        visaCheckBy: [
          {required: true, message: '请选择审核人'}
        ]
      },
      costTypeVisible: false,
      costTypeForm: {}
    }
  },
  computed: {},
  watch: {},
  created() {
    this.getDisList()
    this.getDeptTree()
    this.handleQuery()
    this.getDeptTreeDef();
  },
  mounted() {
  },
  methods: {
    /** 查询部门-用户下拉树结构 */
    getDeptTreeDef() {
      getTreeStruct({types:111}).then(response => {
        this.deptUserOptions = response.data;
      });
    },
    // 关键词检索
    handleSearch() {
      const keyword = this.keyword.toLowerCase();
      this.filteredTreeData = this.relaOptions.filter(node => this.filterNode(node, keyword));
    },
    // 筛选节点
    filterNode(node, keyword) {
      if (node.label.indexOf(keyword) != -1) {
        return true;
      }
      if (node.children) {
        return node.children.some(childNode => this.filterNode(childNode, keyword));
      }
      return false;
    },
    // 查询部门下拉树结构
    getDeptTree() {
      findUserDeptMaintenanceList2().then(response => {
        const treeData = response.data
        treeData.forEach(item => {
          getChild(item)
        })

        function getChild(node) {
          node.label = node.deptName || node.maintenanceSectionName
          node.id = node.deptId || node.maintenanceSectionId
          if (node.children) {
            node.children.forEach(item => {
              getChild(item)
            })
          }
        }

        // 增加一个最顶级
        const tree = [
          {
            label: '云南省交通投资建设集团有限公司',
            id: '1',
            children: [...treeData]
          }
        ]
        this.relaOptions = tree
        this.filteredTreeData = [...this.relaOptions]
      });
    },
    handleNodeClick(e) {
      this.queryParams.domainId = ''
      this.queryParams.maiSecId = ''
      if (e.deptId) {
        this.queryParams.domainId = String(e.id)
      } else if (e.departmentId) {
        this.queryParams.domainId = String(e.departmentId)
        this.queryParams.maiSecId = e.id
      }
      this.handleQuery()
    },
    handleQuery() {
      this.loading = true
      if (this.queryParams.realEDate && this.queryParams.realEDate.length == 2) {
        this.queryParams.startRealEDate = this.queryParams.realEDate[0]
        this.queryParams.endRealEDate = this.queryParams.realEDate[1]
      }
      fetchRepositoryList(this.queryParams).then(res => {
        this.tableData = res.rows
        this.total = res.total
        this.expandedRowKeys = []
        this.loading = false
      })
    },
    resetQuery() {
      this.queryParams = {
        pageNum: 1,
        pageSize: 50
      }
      this.handleQuery()
    },
    handleCloseDetail() {
      this.rowData = {}
      this.drawer = false
      this.handleQuery()
    },
    handleEdit(row) {
      this.rowData = row
      this.drawer = true
    },
    handleUpdate(row) {
      this.rowData = row
      this.$set(this.rowData, "isProductionTemp", this.rowData.isProduction)
      this.$set(this.rowData, "isGuaranteeTemp", this.rowData.isGuarantee)
      this.updateDialog = true;
    },
    handleUpdateSafetyFee() {
      const params = {
        id: this.rowData.settleId,
        isProduction: this.rowData.isProductionTemp,
        isGuarantee: this.rowData.isGuaranteeTemp
      }
      updateSafetyFee(params).then(res => {
        this.$message.success('修改成功')
        this.updateDialog = false
        this.handleQuery()
      })
    },
    handleUpdateList(row) {
      this.updateListDialog = true
      this.loading = true
      this.methodList = []
      this.deleteIds = []
      this.rowData = row
      fetchMethodList(row.settleId, 1).then(res => {
        this.methodList.push(...res.data)
        // 根据id去重
        this.methodList = this.methodList.reduce((acc, curr) => {
          if (!acc.some(item => item.id === curr.id)) {
            acc.push(curr);
          }
          return acc;
        }, []);
        this.loading = false
      })

    },
    openEditUserInfoDialog() {
      if (!this.checkRow.settleId) {
        this.$message.warning("请先选择一条数据！");
        return;
      }
      const params = {
        constructionId: this.checkRow.constructionId,
        disId: this.checkRow.disId,
      }
      getVisaCheck(params).then(res=> {
        this.userInfo = res.data
        this.userInfo.visaBy =  this.userInfo.visaBy.split(',')
        this.userInfoDialog = true
      })
    },
    saveUserInfo() {
      this.$refs.userInfoRef.validate((valid)=> {
        if (valid) {
          const formData = JSON.parse(JSON.stringify(this.userInfo))
          const visaNode = this.$refs.visaRef.getCheckedNodes()
          const visaCheckNode = this.$refs.visaCheckRef.getCheckedNodes()
          formData.visaName = visaNode.map(item=> {
            return item.label
          }).join()
          formData.visaBy = formData.visaBy.join()
          formData.visaCheckName = visaCheckNode[0].label
          updateVisaCheck(formData).then(res=> {
            this.$message.success("修改成功！");
            this.userInfoDialog = false
          })
        }
      })
    },
    // 导出清单按钮
    exportList() {
      if (this.selectIds.length > 0) this.queryParams.settleIds = this.selectIds
      this.download(
          'manager/settlement/repository/export',
          {...this.queryParams},
          `settlement_repository_${new Date().getTime()}.xlsx`,
          {
            headers: {'Content-Type': 'application/json;'},
            parameterType: 'body'
          }
      )
    },
    async changeCalculation(row) {
      if (!this.isValidMathFormula(row.calcDesc)) {
        this.$modal.msgError('计算式错误，请检查')
        return
      }
      let num =  this.math.evaluate(row.calcDesc) || 0
      this.$set(row, 'num', this.ceilToTwo(num, row.decimalPlaces))
      await this.changeSchemeNum(row)
    },
    async changeSchemeNum(row) {
      let money = new Decimal(row.num || 0).times(row.price || 0).toNumber()
      this.$set(row, 'amount', Math.round(money))
      this.total = this.methodList.reduce((acc, curr) => acc + curr.amount, 0)
    },
    handleClickRow(e) {
      this.checkRow = e
      e.isSelected = !e.isSelected;
      this.settleId = e.settleId
      this.$refs.dataTable.toggleRowSelection(e);
    },
    // 选中
    handleSelectionChange(e) {
      this.selectIds = e.map(item => item.settleId)
    },
    loadData(row) {
      fetchMethodList(row.settleId, 0).then(res => {
        this.$set(row, 'methodList', res.data)
      })
    },
    handleDelete(row) {
      this.deleteIds.push(row.id)
      this.methodList.splice(this.methodList.findIndex(item => item.id === row.id), 1)
    },
    handleSave() {
      this.loading = true
      updateMethodList(this.methodList).then(() => {
        if (this.deleteIds.length > 0) {
          deleteMethodList(this.deleteIds.join(',')).then(() => {
            this.deleteIds = []
            this.$message.success('保存成功')
            this.updateListDialog = false
            this.loading = false
          })
        } else {
          this.$message.success('保存成功')
          this.updateListDialog = false
          this.loading = false
        }
      })
    },
    previewVisa() {
      if (!this.settleId) {
        this.$message.warning('请选择结算单')
        return
      }
      this.loading = true
      previewVisa(this.settleId).then(res => {
        if (res.code == 200){
          this.preview.html = res.data.html
          this.preview.url = res.data.downUrl
          this.preview.fileName = res.data.fileName
          this.$refs.iframeRef.visible = true
        }
        this.loading = false
      })
    },
    previewMaintenance() {
      if (!this.settleId) {
        this.$message.warning('请选择结算单')
        return
      }
      this.loading = true
      preViewMaintenance(this.settleId).then(res => {
        if (res.code == 200){
          this.preview.html = res.data.html
          this.preview.url = res.data.downUrl
          this.preview.fileName = res.data.fileName
          this.$refs.iframeRef.visible = true
        }
        this.loading = false
      })
    },
    handleOpenEvent() {
      if (!this.checkRow.constructionId) {
        this.$message.warning('请选择结算单')
        return
      }
      this.dialogVisible = true
    },
    archivesDownload() {
      if (!this.settleId) {
        this.$message.error('请选择结算单')
        return
      }
      this.loading = true
      if (res.data.fileName.endsWith('.zip')) {
        let link = document.createElement('a')
        link.download = res.data.fileName
        link.style.display = 'none'
        link.href = res.data.downUrl
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)
        this.loading = false
      } else {
        axios({
          method: "get",
          responseType: 'arraybuffer',
          url: res.data.downUrl,
          headers: {}
        }).then((res) => {
          const arrayBuffer = res.data;
          // 创建一个Blob对象
          const blob = new Blob([arrayBuffer], {type: 'application/zip'}); // 对于.xls文件
          saveAs(blob, res.data.fileName)
        }).finally(() => {
          this.loading = false
        })
      }
    },
    handleCheckLib(row) {
      this.checkIndex = this.methodList.findIndex(item => item === row);
      this.$refs.methodsRef.openLibModel()
    },
    checkLib(checkData) {
      let methodData = this.methodList[this.checkIndex]
      methodData.schemeCode = checkData.schemeCode
      methodData.schemeName = checkData.schemeName;
      methodData.unit = checkData.unit;
      methodData.price = checkData.price;
      methodData.amount = Math.round((methodData.price || 0) * (methodData.num || 0))
      methodData.isProduction = checkData.safetyFeeFlag;
      methodData.schemeId = checkData.id;
      this.$set(this.methodList, this.checkIndex, methodData)
    },
    handleRegenerate() {
      if (this.selectIds.length == 0) {
        this.$modal.msgError("请勾选至少一条数据")
        return
      }
      const params = {
        idList: this.selectIds,
        type: 2
      }
      regenerateReport(params).then(res => {
        this.$modal.msgSuccess("操作成功")
      })
    },
    handleWithdraw() {
      if (!this.checkRow.finishedId) {
        this.$message.warning('请先选择一条记录！')
        return
      }
      if(this.checkRow.status != 0) {
        this.$message.error('只能撤回状态为结算库的签证单!')
        return
      }
      this.$confirm('确定撤回吗？', '警告', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        const params = {
          businessKey: this.checkRow.finishedId
        }
        withdraw(params).then(res => {
          this.$message.success('撤回成功')
          this.handleQuery()
        })
      })
    },
    updataCostType() {
      if (!this.checkRow.finishedId) {
        this.$message.warning('请先选择一条记录！')
        return
      }
      this.costTypeForm = {
        finishedId: this.checkRow.finishedId,
        costType: this.checkRow.costType
      }
      this.costTypeVisible = true
    },
    saveCostType() {
      updateCostType(this.costTypeForm).then(res => {
        this.$message.success('修改成功')
        this.costTypeVisible = false
        this.handleQuery()
      })
    },
    // 获取事件列表
    getDisList() {
      listAllDiseases().then(res => {
        res.data.forEach(item => {
          this.advicesList.push({
            label: item.diseaseName,
            value: item.id
          })
        })
      })
    },
    routerChange(row) {
      let routeInfo = {
        path: '/dailyMaintenance/construction/constructionSituation',
        query: {code: row.code}
      }
      this.$router.push(routeInfo)
    }
  }
}
</script>

<style lang="scss" scoped>
.leftDiv {
  border-right: 1px solid #d8dce5;
  min-height: calc(100vh - 100px);
  overflow-y: auto;
  height: calc(80vh - 150px);
  position: relative;
  top: -20px;
  padding-top: 10px;
  background-color: white;
}

.leftIcon {
  border: 1px solid #dcdfe6;
  border-radius: 8px;
  width: 16px;
  height: 50px;
  line-height: 50px;
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
  position: absolute;
  right: 0;
  top: 300px;
  z-index: 2;
}

.leftIcon:hover {
  background-color: #dcdfe6;
}

.rightIcon {
  border: 1px solid #dcdfe6;
  border-radius: 8px;
  width: 16px;
  height: 50px;
  line-height: 50px;
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
  position: absolute;
  left: -10px;
  top: 280px;
  z-index: 10;
  background: white;
}

.rightIcon:hover {
  background-color: #dcdfe6;
}

.left-total {
  font-size: 13px;
  line-height: 28px;
  color: #606266;
  position: absolute;
  bottom: 10px;
  right: 10px;
}
</style>
<style lang="scss" scoped>
@import "@/assets/styles/business.scss";
</style>
