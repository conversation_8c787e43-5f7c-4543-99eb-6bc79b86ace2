<template>
  <el-collapse v-model="localActiveNames" @change="handleChange" accordion>
    <el-collapse-item
      v-for="(item, index) in list"
      :key="index"
      :title="item.categoryName"
      :name="'' + index"
    >
      <component
        :is="componentName"
        :form="item"
        :index="index"
        v-bind="getComponentProps(item)"
      ></component>
    </el-collapse-item>
  </el-collapse>
</template>

<script>
import formFile from "@/components/ModuleFile/formFile.vue";
import FileUpload from "@/components/ModuleFile/index.vue";
import { getListByEntity } from "@/api/system/fileUpload.js";
import { mapState, mapActions, mapMutations } from "vuex";
export default {
  name: "collapse",
  components: {
    formFile,
    FileUpload,
  },
  props: {
    fileUploadProps: {
      type: Object,
      default: () => ({}),
    },
    list: {
      type: Array,
      default: () => [],
    },
    activeNames: {
      type: String,
      default: "",
    },
  },
  inject: ["cardId", "isUpload", "bizId"],
  data() {
    return {
      previousIndex: null, // 用于跟踪上一个打开的索引
      ownerIdLists: {},
    };
  },
  computed: {
    componentName() {
      return this.isUpload ? "FileUpload" : "formFile";
    },
    localActiveNames: {
      get() {
        // Getter 返回 prop 的当前值
        return this.activeNames;
      },
      set(newValue) {
        // Setter 发出一个事件来更新父组件的 prop
        this.$emit("update:activeNames", newValue);
      },
    },
  },
  methods: {
    ...mapMutations("file", {
      changeList: "CHANGE_LIST", // 映射 Vuex mutation
    }),
    handleChange(val) {
      this.$emit("update:activeNames", "" + val);
      this.changeList({ cardId: this.cardId });
    },
    async fetchOwnerIdList(fileSubId) {
      try {
        getListByEntity({ fileSubId, bizId: this.bizId }).then((res) => {
          const ownerIdList = res.data.map((item) => item.ownerId);
          this.ownerIdLists[fileSubId] = ownerIdList;
        });
      } catch (error) {
        console.error("Error fetching ownerIdList:", error);
      }
    },
    getComponentProps(item) {
      if (this.isUpload) {
        const fileSubId = item.id;
        if (!this.ownerIdLists[fileSubId]) {
          this.fetchOwnerIdList(fileSubId);
        }
        return {
          ...this.fileUploadProps,
          limit: item.list ? (item.limit === 0 ? undefined : item.limit) : undefined,
          fileSize: item.fileSize?(item.fileSize === 0 ? undefined : item.fileSize):undefined,
          //fileType为''或者null时，不限制文件类型,如果不为空，按照逗号分隔转换成list
          fileType:
            item.fileType === "" || item.fileType === null
              ? undefined
              : item.fileType.split(","),
          fileSubId: fileSubId,
          bizId: this.bizId,
          ownerIdList: this.ownerIdLists[fileSubId] || [],
        };
      }
      return {};
    },
  },
  created() {},
};
</script>

<style scoped>
.el-collapse-item:hover {
  background-color: #f5f5f5;
  transition: background-color 0.3s;
}
</style>
