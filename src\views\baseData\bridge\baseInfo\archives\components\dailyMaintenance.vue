<template>
  <div class="right-box" :class="oneMap ? 'one-map' : ''">
    <div class="right-box-head">
      <h5>日常养护</h5>
    </div>
    <div
      style="height: calc(100% - 53px); width: 100%"
      class="container-view-list"
    >
      <el-table
        ref="table"
        height="100%"
        :header-cell-style="{ height: '36px' }"
        border
        v-loading="loading"
        :data="staticList"
      >
        <el-table-column label="序号" type="index" width="50" align="center">
          <template v-slot="scope">
            {{
              scope.$index +
              (queryParams.pageNum - 1) * queryParams.pageSize +
              1
            }}
          </template>
        </el-table-column>
        <el-table-column
          label="管养单位"
          align="center"
          prop="domainName"
          min-width="140"
          show-overflow-tooltip
        />
        <el-table-column
          label="施工单位"
          align="center"
          prop="conDomainName"
          min-width="140"
          show-overflow-tooltip
        />

        <el-table-column
          label="路段名称"
          align="center"
          prop="maiSecName"
          min-width="140"
          show-overflow-tooltip
        />
        <el-table-column
          label="路线编码"
          align="center"
          prop="routeCode"
          min-width="140"
          show-overflow-tooltip
        />
        <el-table-column
          label="上下行"
          align="center"
          prop="direction"
          min-width="140"
          show-overflow-tooltip
        >
          <template slot-scope="scope">
            <dict-tag
              :options="dict.type.sys_route_direction"
              :value="scope.row.direction"
            />
          </template>
        </el-table-column>
        <el-table-column
          label="位置"
          align="center"
          prop="lane"
          min-width="140"
          show-overflow-tooltip
        >
          <template slot-scope="scope">
            <dict-tag :options="dict.type.lane" :value="scope.row.lane" />
          </template>
        </el-table-column>

        <el-table-column
          label="起点桩号"
          align="center"
          prop="beginMile"
          min-width="140"
          show-overflow-tooltip
        />
        <el-table-column
          label="终点桩号"
          align="center"
          prop="endMile"
          min-width="140"
          show-overflow-tooltip
        />

        <el-table-column
          label="费用类型"
          align="center"
          prop="costType"
          min-width="140"
          show-overflow-tooltip
        />

        <el-table-column
          label="事件类型"
          align="center"
          prop="disTypeName"
          min-width="140"
          show-overflow-tooltip
        />
        <el-table-column
          label="事件描述"
          align="center"
          prop="disDesc"
          min-width="140"
          show-overflow-tooltip
        />
        <el-table-column
          label="施工单"
          align="center"
          prop="name"
          min-width="140"
          show-overflow-tooltip
        >
        </el-table-column>
        <el-table-column
          label="施工单编号"
          align="center"
          prop="code"
          min-width="140"
          show-overflow-tooltip
        >
        </el-table-column>
        <el-table-column
          label="完工时间"
          align="center"
          prop="updateTime"
          min-width="140"
          show-overflow-tooltip
        >
        </el-table-column>

        <el-table-column
          label="总金额"
          align="center"
          prop="sumFund"
          min-width="140"
          show-overflow-tooltip
        />
      </el-table>

      <pagination
        :total="total"
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
        :pageSizes="[10, 20, 30, 50, 100, 1000]"
        @pagination="getList"
      />
    </div>
  </div>
</template>
  
  <script>
import { getgetBridgeAssetDiseaseInfoCard } from "@/api/baseData/bridge/baseInfo/index";

import { getFile } from "@/api/file";

import ImportData from "@/views/baseData/components/importData/index.vue";
import { listAllDiseases } from "@/api/patrol/diseases";

export default {
  name: "periodic-baseInfo",
  components: { ImportData },
  inject: ["oneMap"],
  dicts: ["base_data_yes_no", "sys_route_direction", "lane"],
  props: {
    id: {
      type: String || Number,
      default: "",
    },
    assetId: {
      type: String || Number,
      default: "",
    },
  },
  data() {
    return {
      loading: true,
      ids: [],
      single: true,
      multiple: true,

      showImportAdd: false,
      total: 0,
      staticList: null,
      routeCode: [],
      queryParams: {
        pageNum: 1,
        pageSize: 10,
      },
      dateRange: [],
      ownerId: "",
      disType: [],
    };
  },
  watch: {
    assetId: {
      handler: function (val) {
        if(val){
          this.getDisType();
        }
      },
      immediate: true,
    },
  },
  created() {
    
  },
  methods: {
    // 获取表格数据
    getList() {
      this.loading = true;

      getgetBridgeAssetDiseaseInfoCard({
        assetId: this.assetId,
        // assetId: 916,
      }).then(async (response) => {
        this.staticList = response.rows;
        this.total = response.total;

        
        this.loading = false;

        let dataList = JSON.parse(JSON.stringify(response.rows));

        this.staticList = dataList.map((item) => {
          this.disType.forEach((resItem) => {
            if (resItem.dictValue === item.disType)
              item.disTypeName =resItem.dictLabel;
          });

          return item;
        });



      });
    },
    async handlePreview(row) {
      this.ownerId = row.reportPath;
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id);
      this.single = selection.length != 1;
      this.multiple = !selection.length;
    },
    // 勾选高亮
    rowStyle({ row, rowIndex }) {
      if (this.ids.includes(row.id)) {
        return { "background-color": "#b7daff", color: "#333" };
      } else {
        return { "background-color": "#fff", color: "#333" };
      }
    },
    // 搜索按钮
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    // 重置按钮
    resetQuery() {
      this.routeCode = [];

      this.dateRange = [];
      this.queryParams = {};
      this.handleQuery();
    },

    handleDateChange() {
      this.queryParams.constructionStartDate = this.dateRange[0];
      this.queryParams.constructionEndDate = this.dateRange[1];
    },
    // 表格点击勾选
    handleRowClick(row) {
      row.isSelected = !row.isSelected;
      this.$refs.table.toggleRowSelection(row);
    },
    getDisType() {
      listAllDiseases().then((res) => {
        this.disType = res.data.map((item) => {
          return {
            dictLabel: item.diseaseName,
            dictValue: item.id,
          };
        });
        this.getList();
      });
    },
  },
};
</script>
  
  <style lang="scss" scoped>
@import "@/assets/styles/common.scss";
::v-deep {
  .el-table__row:hover td {
    background: #b7daff !important;
  }
}
.container-view-list {
  // padding: 0;
}
.right-box {
  width: 100%;
  height: 100%;
  background-color: #fff;
  border-radius: 10px;
  box-shadow: 0px 0px 10px 0px rgba(0, 0, 0, 0.2);
  position: relative;
  .right-box-head {
    width: 100%;
    height: 52px;
    background: #409eff;
    border-radius: 10px 10px 0 0;
    display: flex;
    justify-content: center;
    align-items: center;
    h5 {
      font-family: Microsoft YaHei, Microsoft YaHei;
      font-weight: 700;
      font-size: 14px;
      color: #ffffff;
    }
  }
}
</style>
  