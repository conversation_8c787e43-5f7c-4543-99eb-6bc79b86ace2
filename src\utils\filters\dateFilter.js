import Vue from 'vue';
import moment from 'moment';
// Vue.filter('formatDate', function (value) {
//     if (!value) return '';

//     // 使用 Vue 原型链上的 moment
//     let date;

//     // 尝试处理不同格式的日期
//     if (Vue.prototype.$moment(value, moment.ISO_8601, true).isValid()) {
//         date = Vue.prototype.$moment(value, moment.ISO_8601, true);
//     } else if (Vue.prototype.$moment(value, 'YYYY-MM-DD', true).isValid()) {
//         date = Vue.prototype.$moment(value, 'YYYY-MM-DD', true);
//     } else {
//         return value; // 如果日期无效，返回原始值
//     }

//     // 格式化日期为 "年-月-日T时:分:秒"
//     return date.format('YYYY-MM-DDTHH:mm:ss');
// });
Vue.filter('formatDate', function (value, format = 'YYYY-MM-DD') {
    if (!value) return '';
    return moment(value).format(format);
});
