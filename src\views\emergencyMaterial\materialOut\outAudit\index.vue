<template>
  <div class="app-container maindiv">
    <el-row>
      <el-col :span="24" :xs="24">
        <el-row>
          <el-form
            ref="queryForm"
            :model="queryParams"
            size="mini"
            :inline="true"
            label-width="68px"
          >
            <el-form-item>
              <el-input v-model="queryParams.code" placeholder="编号" />
            </el-form-item>
            <el-form-item>
              <el-input v-model="queryParams.materialName" placeholder="物资库名称" />
            </el-form-item>
            <el-form-item>
              <selectTree
                :key="'domainId'"
                style="width: 240px"
                v-model="queryParams.domainId"
                :dept-type="100"
                :expand-all="false"
                :dataRule="false"
                placeholder="领用单位"
                clearable
              />
            </el-form-item>
            <el-form-item>
              <el-date-picker
                v-model="outTime"
                type="daterange"
                range-separator="至"
                start-placeholder="发出起始时间"
                end-placeholder="发出截止时间"
                style="width: 240px"
                value-format="yyyy-MM-dd"
              >
              </el-date-picker>
            </el-form-item>
            <el-form-item>
              <el-button
                type="primary"
                icon="el-icon-search"
                size="mini"
                @click="handleQuery"
                >搜索</el-button
              >
              <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
                >重置</el-button
              >
            </el-form-item>
          </el-form>
        </el-row>
      </el-col>
    </el-row>
    <!--操作按钮区开始-->
    <el-row :gutter="10" class="mb8">
<!--      <el-col :span="1.5">-->
<!--        <el-button type="warning" icon="el-icon-download" size="mini"-->
<!--          >导出报表-->
<!--        </el-button>-->
<!--      </el-col>-->
      <el-col :span="1.5">
        <el-button
          type="success"
          icon="el-icon-view"
          size="mini"
          @click="handleOpenOperate"
          >审核意见
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          icon="el-icon-download"
          size="mini"
          @click="exportList"
          >导出
        </el-button>
      </el-col>
      <right-toolbar
        :showSearch.sync="showSearch"
        @queryTable="handleQuery"
        :columns="columns"
      ></right-toolbar>
    </el-row>
    <el-row>
      <div class="draggable">
        <el-table v-adjust-table
          size="mini"
          style="width: 100%"
          v-loading="loading"
          border
          :data="tableData"
          row-key="id"
          ref="dataTable"
          stripe
          highlight-current-row
          @row-click="handleClickRow"
          :height="showSearch ? 'calc(100vh - 330px)' : 'calc(100vh - 270px)'"
        >
          <el-table-column
            label="序号"
            align="center"
            type="index"
            width="50"
          />
          <template v-for="(column, index) in columns">
            <el-table-column
              :label="column.label"
              v-if="column.visible"
              align="center"
              :prop="column.field"
              :width="column.width"
            >
              <template slot-scope="scope">
                <dict-tag
                  v-if="column.dict"
                  :options="dict.type[column.dict]"
                  :value="scope.row[column.field]"
                />
                <template v-else-if="column.slots">
                  <RenderDom
                    :row="scope.row"
                    :index="index"
                    :render="column.render"
                  />
                </template>
                <span v-else-if="column.isTime">{{
                  parseTime(scope.row[column.field], "{y}-{m}-{d}")
                }}</span>
                <span v-else>{{ scope.row[column.field] }}</span>
              </template>
            </el-table-column>
          </template>
          <el-table-column
            label="操作"
            fixed="right"
            align="center"
            width="250"
            class-name="small-padding fixed-width"
          >
            <template slot-scope="scope">
              <el-button
                type="text"
                icon="el-icon-view"
                @click="openSubmitDialog(scope.row)"
                >审核</el-button
              >
              <el-button
                type="text"
                @click="showInfo(scope.row)"
                icon="el-icon-view"
                >查看</el-button
              >
            </template>
          </el-table-column>
        </el-table>
        <pagination
          v-show="total > 0"
          :total="total"
          :page.sync="queryParams.pageNum"
          :limit.sync="queryParams.pageSize"
          @pagination="handleQuery"
        />
      </div>
    </el-row>
    <!-- 操作记录 -->
    <el-dialog
      title="操作记录"
      :visible.sync="openOperateInfo"
      width="70%"
      destroy-on-close
      v-if="openOperateInfo"
    >
      <operateInfo
        @close="modelClose"
        :businessKey="rowData.id"
        :getNodeInfo="getNodeInfo"
      ></operateInfo>
    </el-dialog>
    <!-- 查看 -->
    <el-dialog
      title="出库物资清单"
      append-to-body
      modal-append-to-body
      :visible.sync="infoDialog"
      width="70%"
      v-if="infoDialog"
    >
      <info @close="modelClose" :rowData="rowData"></info>
    </el-dialog>
    <!-- 审核 -->
    <el-dialog
      title="审核"
      append-to-body
      modal-append-to-body
      :visible.sync="submitDialog"
      width="400px"
      v-if="submitDialog"
    >
      <el-input type="textarea" v-model="submitRemark" />

      <span slot="footer" class="dialog-footer">
        <el-button size="mini" type="primary" @click="confirmSubmit(true)"
          >通过</el-button
        >
        <el-button size="mini" type="danger" @click="confirmSubmit(false)"
          >驳回</el-button
        >
        <el-button size="mini" @click="modelClose">退 出</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import selectTree from "@/components/DeptTmpl/selectTree.vue";
import operateInfo from "@/views/dailyMaintenance/component/operateInfo.vue";
import info from "./info.vue";
import { getOutAuditList, getNodeInfo, delMaterialOut, submitOutbound } from "@/api/emergencyMaterial/materialOut";
export default {
  components: {
    operateInfo,
    selectTree,
    info,
    RenderDom: {
      functional: true,
      props: {
        row: Object,
        index: Number,
        render: Function,
      },
      render(createElement, ctx) {
        const { row, index } = ctx.props;
        return ctx.props.render(row, index);
      },
    },
  },
  props: [],
  dicts: [],
  data() {
    return {
      showSearch: false,
      queryParams: {
        pageNum: 1,
        pageSize: 10,
      },
      outTime: [],
      total: 0,
      loading: false,
      columns: [
        {
          key: 0,
          width: 100,
          field: "code",
          label: `编号`,
          visible: true,
        },
        {
          key: 1,
          field: "materialCode",
          label: `物资库编码`,
          visible: true,
        },
        {
          key: 2,
          field: "materialName",
          label: `物资库名称`,
          visible: true,
        },
        {
          key: 3,
          field: "manager",
          label: `管理人员`,
          visible: true,
        },
        {
          key: 4,
          field: "domainName",
          label: `领用单位`,
          visible: true,
        },

        {
          key: 5,
          field: "person",
          label: `联系人`,
          visible: true,
        },
        {
          key: 6,
          field: "personTel",
          label: `联系方式`,
          visible: true,
        },
        {
          key: 7,
          field: "outTime",
          label: `出库日期`,
          visible: true,
        },
      ],
      tableData: [],
      rowData: {},
      infoDialog: false,
      openOperateInfo: false,
      submitDialog: false,
      submitRemark: "",
    };
  },
  computed: {},
  watch: {},
  created() {
    this.handleQuery();
  },
  mounted() {},
  methods: {
    getNodeInfo,
    handleQuery() {
      this.loading = true;
      if (this.outTime && this.outTime.length > 1) {
        this.queryParams.startOutTime = this.outTime[0]
        this.queryParams.endOutTime = this.outTime[1]
      } else {
        this.queryParams.startOutTime = ''
        this.queryParams.endOutTime = ''
      }
      getOutAuditList(this.queryParams).then((res) => {
        this.loading = false;
        this.tableData = res.rows;
        this.total = res.total;
      });
    },
    resetQuery() {
      this.queryParams = {
        pageNum: 1,
        pageSize: 50,
      };
      this.outTime = []
      this.handleQuery()
    },

    handleClickRow(row) {
      this.rowData = row;
    },
    handleOpenOperate() {
      if (!this.rowData.id) {
        this.$modal.msgError("请选择一条数据");
        return;
      }
      this.openOperateInfo = true;
    },
    showInfo(row) {
      this.rowData = row;
      this.infoDialog = true;
    },
    // 导出清单按钮
    exportList() {
      this.download(
        "manager/emergency/material/outbound/pending/export",
        { ...this.queryParams },
        `出库审核单列表_${new Date().getTime()}.xlsx`,
        {
          headers: { "Content-Type": "application/json;" },
          parameterType: "body",
        }
      );
    },
    openSubmitDialog(row) {
      this.rowData = row;
      this.submitDialog = true
    },
    confirmSubmit(approved) {
      if(!this.submitRemark) {
        this.$message.warning('请输入审核意见')
        return
      }
      const data = {
        businessKey: this.rowData.id,
        approved,
        comment: this.submitRemark,
        taskId: this.rowData.taskId
      }
      submitOutbound(data).then(()=> {
        this.$message.success('提交成功')
        this.modelClose()
        this.handleQuery();
      })
    },
    modelClose() {
      this.openOperateInfo = false;
      this.infoDialog = false;
      this.submitDialog = false
    },
  },
};
</script>

<style lang="scss" scoped>
::v-deep .dialog-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;

  .el-button {
    margin: 0 !important;
  }
}
</style>
<style lang="scss" scoped>
@import "@/assets/styles/business.scss";
</style>
