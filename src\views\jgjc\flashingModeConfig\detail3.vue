<template>
  <div>
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button icon="el-icon-plus" size="mini" type="primary" @click="handleAdd">
          新增
        </el-button>
        <el-button icon="el-icon-delete" size="mini" type="danger" @click="handleDelete">
          删除
        </el-button>
      </el-col>
    </el-row>
    <!-- 数据表格开始 -->
    <div class="tableDiv">
      <el-table
        v-adjust-table
        v-loading="loading"
        :data="dataList"
        :height="'calc(100vh - 260px)'"
        border
        size="mini"
        style="width: 100%"
        @selection-change="handleSelectionChange"
      >
        <el-table-column align="center" fixed="left" type="selection" width="55" :selectable="checkSelectable"></el-table-column>
        <el-table-column
          align="center"
          fixed
          label="序号"
          type="index"
          width="100"
        ></el-table-column>
        <template v-for="(column, index) in columns">
          <el-table-column
            v-if="column.visible"
            :key="index"
            :label="column.label"
            :prop="column.field"
            align="center"
            show-overflow-tooltip
          >
            <template slot-scope="scope">
              <dict-tag
                v-if="column.dict"
                :options="dict.type[column.dict]"
                :value="scope.row[column.field]"
              />
              <template v-else-if="column.slots">
                <RenderDom :index="index" :render="column.render" :row="scope.row" />
              </template>
              <span v-else-if="column.isTime">
								{{ parseTime(scope.row[column.field], '{y}-{m}-{d}') }}
							</span>
              <span v-else>{{ scope.row[column.field] }}</span>
            </template>
          </el-table-column>
        </template>
<!--        <el-table-column align="center" fixed="right" label="操作" width="100">-->
<!--          <template slot-scope="scope">-->
<!--            <el-button icon="el-icon-edit" size="mini" type="text" :disabled="scope.row.isCustom == 0" @click="handleUpdate(scope.row)">修改-->
<!--            </el-button>-->
<!--          </template>-->
<!--        </el-table-column>-->
      </el-table>
      <pagination
        v-show="total > 0"
        :limit.sync="queryParams.pageSize"
        :page.sync="queryParams.pageNum"
        :total="total"
        @pagination="handleQuery"
      />
    </div>
    <!-- 数据表格结束 -->
    <el-dialog
      :title="dialogTitle"
      :visible.sync="dialogVisible"
      append-to-body
      destroy-on-close
      width="50%"
    >
      <el-form ref="elForm" :model="formData" :rules="rules" label-width="120px" size="medium">
        <el-row>
          <el-col :span="12">
            <el-form-item label="模式名称" prop="name">
              <el-input
                v-model="formData.name"
                placeholder="请输入模式名称"
                clearable
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="爆闪开关" prop="lampSwitch">
              <dict-select
                type="flash_open_type"
                style="width: 100%"
                placeholder="请选择爆闪开关"
                v-model="formData.lampSwitch"
              ></dict-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="12">
            <el-form-item label="显示内容" prop="screenWord">
              <el-input
                v-model="formData.screenWord"
                placeholder="请输入显示内容"
                clearable
                maxlength='1'
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="文字颜色" prop="color">
              <el-select
                v-model="formData.color"
                placeholder="请选择文字颜色"
                clearable
                style="width: 100%"
              >
                <el-option label="黄色" :value="0" />
                <el-option label="红色" :value="1" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="12">
            <el-form-item label="闪烁频率" prop="frequency">
              <el-select
                v-model="formData.frequency"
                placeholder="请选择闪烁频率"
                clearable
                style="width: 100%"
              >
                <el-option label="常亮" :value="0" />
                <el-option label="慢速" :value="30" />
                <el-option label="中速" :value="60" />
                <el-option label="快速" :value="120" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="自定义音频" prop="soundContent">
              <el-input
                v-model="formData.soundContent"
                placeholder="请输入自定义音频"
                clearable
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="12">
            <el-form-item label="音频间隔" prop="soundInterval">
              <el-input-number
                v-model="formData.soundInterval"
                :min="0"
                placeholder="请输入音频间隔"
                controls-position="right"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <div style="text-align: right; margin-top: 20px">
          <el-button size="mini" type="primary" @click="handleSave">保存</el-button>
          <el-button size="mini" @click="dialogVisible = false">退出</el-button>
        </div>
      </el-form>
    </el-dialog>
  </div>
</template>

<script>
import {
  deleteSsConfig,
  getSsConfigPage,
  saveSsConfig,
} from '@/api/jgjc/flashingModeConfig'

export default {
  dicts: ['flash_open_type'],
  data() {
    return {
      // 遮罩层
      loading: false,
      dataList: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
      },
      // 列信息
      columns: [
        { key: 0, width: 100, field: 'name', label: `模式名称`, visible: true },
        { key: 1, width: 100, field: 'modeCode', label: `模式编码`, visible: true },
        { key: 2, width: 100, field: 'lampSwitch', label: `爆闪开关`, visible: true, dict: 'flash_open_type' },
        { key: 4, width: 100, field: 'screenWord', label: `显示内容`, visible: true },
        { key: 5, width: 100, field: 'textColorShow', label: `文字颜色`, visible: true },
        { key: 7, width: 100, field: 'frequencyShow', label: `闪烁频率`, visible: true },
        { key: 8, width: 100, field: 'soundContent', label: `自定义音频`, visible: true },
        { key: 9, width: 100, field: 'soundInterval', label: `音频间隔`, visible: true }
      ],
      total: 0,
      ids: [],
      dialogVisible: false,
      dialogTitle: '新增',
      formData: {},
    }
  },
  computed: {
    rules() {
      return {
        name: [
          { required: true, message: '请输入模式名称', trigger: 'blur' },
          { min: 1, max: 50, message: '长度在1到50个字符', trigger: 'blur' }
        ],
        lampSwitch: [
          { required: true, message: '请选择爆闪开关', trigger: 'change' }
        ],
        screenWord: [
          { required: true, message: '请输入显示内容', trigger: 'blur' },
          { min: 1, max: 100, message: '长度在1到100个字符', trigger: 'blur' }
        ],
        color: [
          { required: true, message: '请选择文字颜色', trigger: 'change' }
        ],
        frequency: [
          { required: true, message: '请输入闪烁频率', trigger: 'blur' },
          { type: 'number', min: 0, message: '必须大于等于0', trigger: 'blur' }
        ],
      }
    },
  },
  mounted() {
    this.handleQuery()
  },
  methods: {
    handleQuery() {
      this.loading = true
      getSsConfigPage(this.queryParams)
        .then((res) => {
          this.dataList = res.rows
          this.dataList.forEach(item => {
            item.textColorShow = item.textColor == 0 ? '黄色' : '红色'
            item.frequencyShow = item.frequency == 0 ? '常亮' : item.frequency == 30 ? '慢速' : item.frequency == 60 ? '中速' : '快速'
          })
          this.total = res.total
          this.loading = false
        })
        .catch((err) => {
          this.loading = false
          console.error(err)
        })
    },
    handleSelectionChange(e) {
      this.ids = e.map((item) => item.id)
    },

    handleDelete() {
      if (this.ids.length === 0) {
        this.$message({
          message: '请选择需要删除的数据',
          type: 'warning',
        })
        return
      }
      this.$confirm('是否确认删除选中数据?', '警告', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }).then(() => {
        deleteSsConfig({ ids: this.ids }).then((res) => {
          this.$message({
            message: '删除成功',
            type: 'success',
          })
          this.handleQuery()
        })
      })
    },
    handleAdd() {
      this.dialogVisible = true
      this.dialogTitle = '新增'
      this.formData = {}
    },
    handleUpdate(e) {
      this.dialogVisible = true
      this.dialogTitle = '修改'
      this.formData = JSON.parse(JSON.stringify(e))
    },
    handleSave() {
      this.$refs.elForm.validate((valid) => {
        if (!valid) return
        saveSsConfig(this.formData).then((res) => {
          this.$message({
            message: '新增成功',
            type: 'success',
          })
          this.handleQuery()
          this.closeDialog()
        })
      })
    },
    checkSelectable(row) {
      return row.isCustom != 0
    },
    closeDialog() {
      this.dialogVisible = false
      this.$refs.elForm.resetFields()
    },
  },
}
</script>

<style lang="scss" scoped></style>
