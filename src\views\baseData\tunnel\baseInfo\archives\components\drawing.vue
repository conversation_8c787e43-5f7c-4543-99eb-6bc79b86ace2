<template>
  <div class="right-box" :class="oneMap?'one-map':''" :style="oneMap?'background-color: unset;':'background-color: #fff;'">
    <div class="right-box-head">
      <h5>隧道图纸文件</h5>
    </div>
    <div style="height: calc(100% - 53px); width: 100%;">
      <CardBox
        style=" padding: 10px; border-radius: 0 0 10px 10px;"
        :style="oneMap ? 'height: calc(100vh - 171px);' : 'height: calc(100vh - 155px);'"
        typeCode="BS130"
        v-for="item in [assetId]"
        :bizId="item"
        :key="item"
        :assetType="194"
      />
    </div>
  </div>
</template>

<script>
import CardBox from '@/components/Drawing/cardBox.vue'

export default {
  name: 'tunnel-archives-drawing',
  inject: {
    oneMap: {
      default: false,
    },
  },
  props: {
    assetId: {
      type: undefined,
      default: ''
    }
  },
  components: { CardBox },
  data() {
    return {}
  },
  created() {},
  methods: {},
  computed: {},
  watch: {}
}
</script>

<style lang="scss" scoped>
@import "@/assets/styles/common.scss";
.right-box {
  width: 100%;
  height: 100%;
  border-radius: 10px;
  box-shadow: 0px 0px 10px 0px rgba(0, 0, 0, 0.2);
  position: relative;
  .right-box-head {
    width: 100%;
    height: 52px;
    background: #409eff;
    border-radius: 10px 10px 0 0;
    display: flex;
    justify-content: center;
    align-items: center;
    h5 {
      font-weight: 700;
      font-size: 14px;
      color: #ffffff;
    }
  }
}
</style>