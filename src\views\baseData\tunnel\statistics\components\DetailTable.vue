<template>
  <div class="detail-table">
    <el-dialog :title="title" :visible.sync="showTable" width="70%" :before-close="handleClose"
      :close-on-click-modal="false">
      <div style="width: 100%; height: 70vh">
        <el-input style=" width: 170px" v-model="queryParams.tunnelName" placeholder="隧道名称" clearable />
        <el-input style="margin: 0 20px; width: 170px" v-model="queryParams.tunnelCode" placeholder="隧道编码" clearable />

        <el-button type="primary" icon="el-icon-search" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" @click="resetQuery">重置</el-button>
        <el-button style="margin-bottom: 10px" type="primary" @click="exportList">导出列表</el-button>
        <el-table ref="table" v-loading="loading" height="calc(100% - 88px)" border :data="tableData"
          :header-cell-style="{ height: '36px' }">
          <el-table-column fixed label="序号" type="index" width="50" align="center" />
          <el-table-column fixed label="隧道名称" align="center" prop="tunnelName" min-width="140" show-overflow-tooltip />
          <el-table-column label="隧道编码" align="center" prop="tunnelCode" min-width="140" show-overflow-tooltip />
          <el-table-column label="路线名称" align="center" prop="routeName" min-width="140" show-overflow-tooltip />
          <el-table-column label="路线编码" align="center" prop="routeCode" min-width="140" show-overflow-tooltip />
          <el-table-column label="管理处" align="center" prop="managementMaintenanceName" min-width="140" />
          <el-table-column label="养护路段" align="center" prop="maintenanceSectionName" min-width="140" />
          <el-table-column label="管养分处" align="center" prop="managementMaintenanceBranchName" min-width="140" />
          <el-table-column label="养护等级" align="center" prop="maintenanceGrade" min-width="140" show-overflow-tooltip>
            <template slot-scope="scope">
              <dict-tag :options="dict.type.tunnel_maintenance_grade" :value="scope.row.maintenanceGrade" />
            </template>
          </el-table-column>
          <el-table-column label="方向" align="center" prop="direction" min-width="140" show-overflow-tooltip>
            <template slot-scope="scope">
              <dict-tag :options="dict.type.sys_route_direction" :value="scope.row.direction" />
            </template>
          </el-table-column>
          <el-table-column label="统一里程桩号" align="center" prop="unifiedMileageStake" min-width="140"
            show-overflow-tooltip>
            <template slot-scope="scope">
              {{ formatPile(scope.row.unifiedMileageStake) }}
            </template>
          </el-table-column>
          <el-table-column label="隧道长度(m)" align="center" prop="tunnelLength" min-width="140" show-overflow-tooltip />
          <el-table-column label="按隧道长度分类" align="center" prop="lengthClassification" min-width="140"
            show-overflow-tooltip>
            <template slot-scope="scope">
              <dict-tag :options="dict.type.tunnel_length_classification" :value="scope.row.lengthClassification" />
            </template>
          </el-table-column>
          <el-table-column label="隧道评定等级" align="center" prop="assessmentGrade" min-width="140" show-overflow-tooltip>
            <template slot-scope="scope">
              <dict-tag :options="dict.type.tunnel_assess_grade" :value="scope.row.assessmentGrade" />
            </template>
          </el-table-column>
          <el-table-column label="建成通车时间" align="center" prop="operationDate" min-width="140" show-overflow-tooltip />
          <el-table-column label="建成通车时间" align="center" prop="operationDate" min-width="140" show-overflow-tooltip />
          <el-table-column label="是否水下隧道" align="center" prop="underwaterTunnel" min-width="140" show-overflow-tooltip>
            <template slot-scope="scope">
              <dict-tag :options="dict.type.base_data_yes_no" :value="scope.row.underwaterTunnel" />
            </template>
          </el-table-column>
          <el-table-column label="运营状态" align="center" prop="operationState" min-width="140" show-overflow-tooltip>
            <template slot-scope="{ row }">
              <el-link :underline="false" :type="{ 1: 'info', 2: 'success', 3: 'danger', 4: 'primary' }[
                row.operationState
              ]
                " @click="handleOperational($event, row)">
                <DictTag :value="row.operationState" :options="dict.type.sys_operation_state" />
              </el-link>
            </template>
          </el-table-column>
          <el-table-column label="所在政区编码" align="center" prop="areaCode" min-width="140" show-overflow-tooltip />
          <el-table-column label="是否跨省隧道" align="center" prop="crossProvinceTunnel" min-width="140"
            show-overflow-tooltip>
            <template slot-scope="scope">
              <dict-tag :options="dict.type.base_data_yes_no" :value="scope.row.crossProvinceTunnel" />
            </template>
          </el-table-column>
          <el-table-column label="是否锁定" align="center" prop="isLocked" min-width="140" show-overflow-tooltip>
            <template slot-scope="{ row }">
              <el-link :underline="false" :type="row.isLocked ? 'danger' : 'info'">
                {{ row.isLocked ? "是" : "否" }}
              </el-link>
            </template>
          </el-table-column>
          <el-table-column label="数据状态" align="center" prop="status" min-width="140" show-overflow-tooltip>
            <template slot-scope="scope">
              {{ scope.row.status == 1 ? "暂存" : "启用" }}
            </template>
          </el-table-column>
          <el-table-column label="是否在长大隧道目录" align="center" prop="inLongTunnelDirectory" min-width="150"
            show-overflow-tooltip>
            <template slot-scope="scope">
              <dict-tag :options="dict.type.base_data_yes_no" :value="scope.row.inLongTunnelDirectory" />
            </template>
          </el-table-column>
          <el-table-column label="设计时速(km/h)" align="center" prop="designSpeed" min-width="140" show-overflow-tooltip />
          <el-table-column label="衬砌类型" align="center" prop="liningType" min-width="140" show-overflow-tooltip>
            <template slot-scope="scope">
              <dict-tag :options="dict.type.tunnel_lining_type" :value="scope.row.liningType" />
            </template>
          </el-table-column>
          <el-table-column label="建成时间" align="center" prop="buildDate" min-width="140" show-overflow-tooltip />
          <el-table-column label="交竣工图纸" align="center" prop="finishPapersPath" min-width="140" show-overflow-tooltip />
        </el-table>
        <pagination :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize"
          :pageSizes="[10, 20, 30, 50, 100, 1000]" @pagination="getList" />
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listStatic } from "@/api/baseData/tunnel/baseInfo/index";

export default {
  name: "tunnel-statistics-detail-table",
  props: {
    showTable: { type: Boolean, default: false },
    row: { type: undefined, default: "" },
    params: { type: undefined, default: "" },
  },
  dicts: [
    "tunnel_maintenance_grade",
    "sys_route_direction",
    "tunnel_length_classification",
    "tunnel_assess_grade",
    "base_data_yes_no",
    "sys_operation_state",
    "tunnel_lining_type",
  ],
  components: {},
  data() {
    return {
      title: "",
      loading: false,
      queryParams: {
        pageNum: 1,
        pageSize: 20,
        operationState: '2',
      },
      total: 0,
      tableData: [],
      paramsObj: {},
    };
  },
  created() {
    this.getTitle();
    this.getParams();
    this.getList();
  },
  methods: {
    // 搜索按钮
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    // 重置按钮
    resetQuery() {
      this.queryParams = {
        pageNum: 1,
        pageSize: 20,
        operationState: '2',
      };
      this.handleQuery();
    },
    getTitle() {
      if (this.row) {
        this.title =
          (this.row.managementMaintenanceName
            ? this.row.managementMaintenanceName + "-"
            : "") +
          this.row.maintenanceSectionName +
          "-" +
          this.row.clickName +
          "列表";
      }
    },
    getParams() {
      if (this.row.clickName == "未评定") {
        this.params.notInIds = this.params.ids;
        delete this.params.ids;
      }

      if (this.params) {
        let ids = this.row.managementMaintenanceIds
          ? this.row.managementMaintenanceIds
          : this.row.managementMaintenanceId
            ? [this.row.managementMaintenanceId]
            : undefined;
        this.paramsObj = {
          ...this.queryParams,
          managementMaintenanceIds: ids,
          maintenanceSectionId: this.row.maintenanceSectionId || undefined,
          maintenanceSectionIds: this.row.maintenanceSectionIds || undefined,
          routeCodes: this.row.routeCodes || undefined,
          operationState: this.row.operationState || undefined,
          years: this.row.years || undefined,
          ...this.params,
        };
      }
    },
    getList() {
      this.loading = true;
      let paramsObj = {
        ...this.paramsObj,
        ...this.queryParams,
      };
      listStatic(paramsObj)
        .then((res) => {
          if (res.code === 200) {
            this.tableData = res.rows;
            this.total = res.total;
          }
        })
        .finally(() => {
          this.loading = false;
        });
    },
    exportList() {

      if (this.tableData.length === 0) return;
      this.$modal
        .confirm("导出所有表格数据，是否继续？")
        .then(() => {
          let paramsObj = {
            ...this.paramsObj,
            ...this.queryParams,
          };
          this.download(
            "/baseData/tunnel/export",
            paramsObj,
            `road_interflow_${new Date().getTime()}.xlsx`,
            {
              headers: { "Content-Type": "application/json;" },
              parameterType: "body",
            }
          );
        })
        .catch(() => { });
    },
    handleClose() {
      this.$emit("close");
    },
  },
  computed: {},
  watch: {},
};
</script>

<style scoped lang="scss">
.detail-table {
  ::v-deep .el-table .el-table__fixed-body-wrapper {
    top: 36px !important;
  }
}
</style>