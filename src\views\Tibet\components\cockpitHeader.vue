<template>
  <!-- <div class="header" ref="hRef" :style="{ position: !isInline ? fixed : 'relative' }"> -->
  <div class="header" ref="hRef"
    :style="{ position: fixed, top: !isInline ? '0' : isFullscreen ? '0' : '81px', left: isFullscreen ? '0' : isInline && !isCollapse ? '245px' : isInline && isCollapse ? '50px' : '0' }">
    <div class="header-main" :class="bigBool ? 'h-sub-bg' : 'h-bg'">
      <div class="current-time" :style="{ fontSize: bigBool ? '56px' : '14px' }" v-if="!bigBool">{{ time }}</div>
      <div class="cockpit-title" :class="bigBool ? '' : 'set-family'"
        :style="{ justifyContent: bigBool ? '' : 'center', marginLeft: bigBool ? '30px' : '', fontSize: !isInline && bigBool ? '2.8vh' : '1.8vw' }">
        <span class="title-n" :style="{ maxWidth: isSub && !bigBool ?  (path == '/cockpit' ? '22vw' : '19vw') : '' }" :title="title">{{ title }}</span>
        <slot></slot>
      </div>
      <div class="btn">
        <slot name="btn">
          <img :src="isFullscreen ? quitIcon : fullIcon" @click="fullClick"
            :style="{ width: bigBool ? '80px' : '30px', height: bigBool ? '80px' : '30px' }" />

          <img src="@/assets/map/quite.png" @click="handleQuit" v-if="!isInline"
            :style="{ width: bigBool ? '150px' : '60px', height: bigBool ? '150px' : '60px' }" />
        </slot>
      </div>
    </div>
    <div class="year-selector" v-if="time && isSub && !excludeList.includes(title)">
      <el-select v-model="year" placeholder="请选择年份" :popper-append-to-body="false" popper-class="select-popper"
        @change="onChangeYear">
        <el-option v-for="item in years" :key="item" :label="item + '年'" :value="item"></el-option>
      </el-select>
      <span v-if="jumpType == 2" class="ml-1 mt-1" style="cursor: pointer;"
        :style="{ width: bigBool ? '140px' : '60px', height: bigBool ? '64px' : '29px' }">
        <!-- <img src="@/assets/cockpit/back.png" @click="handleBack" style="width:100%;height:100%"/> -->
      </span>
    </div>
  </div>
</template>

<script>
import screenfull from 'screenfull';
import FullImg from '@/assets/map/full.png';
import QuitFullImg from '@/assets/map/quit-full.png';
import { isBigScreen } from '../util/utils';
import { mapGetters } from "vuex";

export default {
  name: 'Header',
  inject: ['sub'],
  props: {
    title: {
      type: String,
      default: '云南交投集团养护数据中心'
    },
    fixed: {
      type: String,
      default: 'fixed',
    },
    fontSize: {
      type: String,
      default: ''
    },
    // 是否是子页面
    isSub: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      headerH: 70,
      time: '',
      bigBool: false,
      isInline: false,
      year: new Date().getFullYear(),
      timer: null,
      fullIcon: FullImg,
      quitIcon: QuitFullImg,
      isFullscreen: false,
      jumpType: 0,
      years: [],
      excludeList: ['桥梁养护专题', '隧道养护专题', '工程集群健康监测', '云南交投集团养护数据中心','巡检查专题','日常养护专题','专项养护专题','路网养护专题'],
      path: '',
    }
  },

  created() {
    this.getYear();
    this.bigBool = isBigScreen();
    const query = this.$route?.query
    if (query && !query?.jumpType && Object.keys(query).length !== 0 && query?.type == 1) {
      this.isInline = true;
    }
    this.jumpType = query?.jumpType;
    if (!this.jumpType) {
      this.jumpType = this.sub.jumpType;
    }
    this.path = this.$route.path;
    // 获取当前是星期几
    const date = new Date();
    const week = ['星期日', '星期一', '星期二', '星期三', '星期四', '星期五', '星期六'];
    const dayOfWeek = week[date.getDay()];
    // 时时获取当前时间 年月日时分秒
    this.timer = setInterval(() => {
      let date = new Date();
      const year = date.getFullYear();
      const month = date.getMonth() + 1;
      const day = date.getDate();

      const hours = date.getHours();
      let minutes = date.getMinutes();
      let seconds = date.getSeconds();
      // 判断分 秒是否大于10
      if (minutes < 10) {
        minutes = '0' + minutes;
      }
      if (seconds < 10) {
        seconds = '0' + seconds;
      }
      this.time = `${year}年${month}月${day}日 ${dayOfWeek} ${hours}:${minutes}:${seconds}`;
    }, 1000);
  },
  computed: {
    ...mapGetters(["sidebar"]),
    isCollapse() {
      return !this.sidebar.opened;
    }
  },
  mounted() {
    this.headerH = this.$refs.hRef.clientHeight || this.$refs.hRef.offsetHeight;
    screenfull.on("change", this.change);
  },
  methods: {
    getYear() {
      let date = new Date();
      let year = date.getFullYear();
      this.year = year;
      for (let i = year - 10; i < year + 1; i++) {
        this.years.push(i);
      }
    },
    getHeaderHeight() {
      return this.headerH;
    },
    onChangeYear() {
      window.$Bus.$emit("pageSelectYear", this.year);
    },
    handleQuit() {
      this.$router.push({ path: '/home' })
      // 返回上一个页面
      // this.$router.back();
    },
    fullClick() {
      this.isFullscreen = !this.isFullscreen;
      this.$emit('fullClick', this.isFullscreen);
    },
    change() {
      this.isFullscreen = screenfull.isFullscreen;
    },
    handleBack() {
      this.$router.back();
      if (this.jumpType == 2) {
        window.close();
      }
    }
  },

  unmounted() {
    screenfull.off("change", this.change);
    if (this.timer) {
      clearInterval(this.timer)
      this.timer = null;
    }
  },
}
</script>

<style lang="scss" scoped>
@import "@/assets/styles/utils.scss";

@font-face {
  font-family: 'YouSheBiaoTiHei';
  /* 自定义的字体名称 */
  src: url('~@/assets/home/<USER>') format('truetype');
  /* 字体文件路径和格式 */
  /* 可选属性，根据需要设置 */
  font-weight: normal;
  font-style: normal;
}

.header {
  height: vwpx(160px);
  top: 0;
  left: 0;
  right: 0;
  background-image: url('~@/assets/cockpit/cockpit-bg.png');
  background-repeat: no-repeat;
  background-size: cover;
  z-index: 2;
  // position: fixed;

  .h-bg {
    background-image: url('~@/assets/cockpit/header-bg.png');
    background-repeat: no-repeat;
    background-size: 100% 100%;
  }

  .h-sub-bg {
    // background-image: url('~@/assets/cockpit/top-bg.png');
    background-repeat: no-repeat;
    background-size: 100% 100%;
  }

  .year-selector {
    z-index: 99;
    height: 0vh;
    position: relative;
    top: -4vmin;
    float: right;
    right: 1.8518518519vmin;
    display: flex;
    align-items: center;

    ::v-deep .el-input {
      .el-input__inner {
        width: 9vw;
        height: vwpx(70px);
        background-color: rgba(1, 102, 254, 0.2);
        border: 1px solid #0166fe;
        color: #ffffff;
        font-size: vwpx(30px);
      }

      .el-input__inner::placeholder {
        color: #bbbbbb;
      }

      .el-input-group__append {
        background-color: rgba(1, 102, 254, 0.2);
        border: 1px solid #0166fe;
        color: #ffffff;
        border-left: none;
        padding: 0 10px;
        cursor: pointer;
      }
    }

    ::v-deep .select-popper {
      background-color: rgba(1, 102, 254, 0.2);
      border: 1px solid #0166fe;
      color: #ffffff !important;
      font-size: vwpx(30px);
      margin: vwpx(10px) 0;
    }

    ::v-deep .el-select-dropdown__item {
      color: #ffffff !important;
      font-size: vwpx(30px);
      margin: vwpx(15px) 0;
    }

    ::v-deep .el-select-dropdown__item.selected {
      color: #42abff !important;
    }
  }

  .header-main {
    height: 100%;
    width: 100%;
    position: relative;

    .current-time {
      position: absolute;
      left: vwpx(40px);
      top: 40%;
      transform: translateY(-50%);
      color: #fff;
    }

    .set-family {
      font-family: YouSheBiaoTiHei;
      background: linear-gradient(180deg, #FFFFFF 0%, #FFFFFF 60%, #20A9FF 100%);
      background-clip: text;
      -webkit-background-clip: text;
      color: transparent;
    }



    .cockpit-title {
      font-family: YouSheBiaoTiHei;
      font-weight: 400;
      min-width: vwpx(550px);
      letter-spacing: vwpx(8px);
      text-align: center;
      line-height: vwpx(160px);
      color: #fff;

      display: flex;
      align-items: center;
      justify-content: flex-start;

      .title-n {
        display: block;
        text-align: left;
        color: #fff;
        font-size: min(26px, 1.45vw);
        // 超出部分隐藏
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }

    }

    .b-font {
      // font-size: vwpx(96px);
      font-size: 1.3vw;
    }

    .n-font {
      // font-size: vwpx(70px);
      // font-size: clamp(1rem, 0.682rem + 1.59vw, 1.575rem);
      font-size: 1.8vw;
    }

    .btn {
      position: absolute;
      right: vwpx(10px);
      top: 50%;
      transform: translateY(-50%);
      cursor: pointer;

      display: flex;
      align-items: center;
    }
  }

  .ml-1 {
    margin-left: vwpx(10px);
  }

  .mt-1 {
    margin-top: vwpx(10px);
  }
}
</style>