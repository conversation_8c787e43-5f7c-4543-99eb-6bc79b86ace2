<template>
  <div class="app-container">
    <el-row :gutter="20">

      <!--筛选区开始-->
      <el-col :span="24" :xs="24">
        <el-row>
          <el-col :span="24">
            <el-form ref="queryForm" :inline="true" :model="queryParams" label-width="68px" size="small">
              <el-form-item label="" prop="year">
                <el-date-picker
                    v-model="queryParams.year"
                    style="width: 240px"
                    type="year"
                    format="yyyy"
                    value-format="yyyy"
                    placeholder="年份">
                </el-date-picker>
              </el-form-item>
              <el-form-item label="" prop="code">
                <el-select v-model="queryParams.budgetVer" placeholder="请选择类型版本" clearable :style="{width: '100%'}">
                  <el-option v-for="(item, index) in versionList" :key="index" :label="item.label"
                            :value="item.value" :disabled="item.disabled">
                  </el-option>
                </el-select>
              </el-form-item>
              <el-form-item>
                <el-button icon="el-icon-search" size="mini" type="primary" @click="handleQuery">搜索</el-button>
                <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
              </el-form-item>
            </el-form>
            <!--默认折叠-->
          </el-col>
        </el-row>
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button
                icon="el-icon-plus"
                size="mini"
                type="primary"
                v-has-menu-permi="['budget:yearconfig:add']"
                @click="handleAdd"
            >新增
            </el-button>
          </el-col>
          <right-toolbar
              :columns="columns"
              :showSearch.sync="showSearch"
              @queryTable="handleQuery"
          ></right-toolbar>
        </el-row>
        <!--筛选区结束-->
        <!--数据表格开始-->
        <div class="tableDiv">
          <el-table v-adjust-table v-loading="loading" :data="dataList"
                    :height="showSearch ? 'calc(100vh - 320px)' : 'calc(100vh - 260px)'"
                    border size="mini" style="width: 100%">
            <el-table-column fixed label="序号" align="center" type="index" width="100"></el-table-column>
            <template v-for="(column,index) in columns">
              <el-table-column :label="column.label"
                               v-if="column.visible"
                               align="center"
                               :prop="column.field">
                <template slot-scope="scope">
                  <dict-tag v-if="column.dict" :options="dict.type[column.dict]" :value="scope.row[column.field]"/>
                  <template v-else-if="column.slots">
                    <RenderDom :row="scope.row" :index="index" :render="column.render"/>
                  </template>
                  <span v-else>{{ scope.row[column.field] }}</span>
                </template>
              </el-table-column>
            </template>
            <el-table-column
                label="操作"
                fixed="right"
                align="center"
                width="300"
                class-name="small-padding fixed-width"
            >
              <template slot-scope="scope">
                <el-button
                    size="mini"
                    type="text"
                    icon="el-icon-delete"
                    v-has-menu-permi="['budget:yearconfig:remove']"
                    @click="handleDelete(scope.row)"
                >删除
                </el-button>
                <el-button
                    size="mini"
                    type="text"
                    icon="el-icon-turn-off"
                    v-if="scope.row.isReport == 0"
                    v-has-menu-permi="['budget:yearconfig:edit']"
                    @click="handleChange(scope.row, 'isReport')"
                >开启上报
                </el-button>
                <el-button
                    size="mini"
                    type="text"
                    icon="el-icon-open"
                    v-else
                    v-has-menu-permi="['budget:yearconfig:edit']"
                    @click="handleChange(scope.row, 'isReport')"
                >关闭上报
                </el-button>
                <el-button
                    size="mini"
                    type="text"
                    icon="el-icon-turn-off"
                    v-if="scope.row.isAdjust == 0"
                    v-has-menu-permi="['budget:yearconfig:edit']"
                    @click="handleChange(scope.row, 'isAdjust')"
                >开启调整
                </el-button>
                <el-button
                    size="mini"
                    type="text"
                    icon="el-icon-open"
                    v-else
                    v-has-menu-permi="['budget:yearconfig:edit']"
                    @click="handleChange(scope.row, 'isAdjust')"
                >关闭调整
                </el-button>
              </template>
            </el-table-column>
          </el-table>

          <pagination
              v-show="total>0"
              :limit.sync="queryParams.pageSize"
              :page.sync="queryParams.pageNum"
              :total="total"
              @pagination="handleQuery"
          />
        </div>
        <!--数据表格结束-->
      </el-col>
    </el-row>
    <el-dialog title="新增" :visible.sync="open" destroy-on-close append-to-body width="500px">
      <el-form ref="elForm" :model="formData" :rules="rules" size="medium" label-width="100px">
        <el-form-item label="年份" prop="year">
          <el-date-picker
              v-model="formData.year"
              :style="{width: '100%'}"
              format="yyyy"
              value-format="yyyy"
              type="year"
              placeholder="年份">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="上报开关" prop="isReport">
          <el-select v-model="formData.isReport" placeholder="请输入上报开关" clearable :style="{width: '100%'}">
            <el-option v-for="(item, index) in options" :key="index" :label="item.label"
                       :value="item.value" :disabled="item.disabled"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="调整开关" prop="isAdjust">
          <el-select v-model="formData.isAdjust" placeholder="请输入调整开关" clearable :style="{width: '100%'}">
            <el-option v-for="(item, index) in options" :key="index" :label="item.label"
                       :value="item.value" :disabled="item.disabled"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="关联版本" prop="budgetVer">
          <el-select v-model="formData.budgetVer" placeholder="请选择类型版本" clearable :style="{width: '100%'}">
            <el-option v-for="(item, index) in versionList" :key="index" :label="item.label"
                      :value="item.value" :disabled="item.disabled">
            </el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <div style="text-align: right">
        <el-button @click="close">取消</el-button>
        <el-button type="primary" @click="handleConfirm">确定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { GetYearconfigList , AddYearconfigList, EditYearconfigList, DeleteYearconfig } from "@/api/budgetManage/yearControl";
import { listAllBudgetVersions} from "@/api/budgetManage/version";

export default {
  name: "YearControl",
  components: {
    RenderDom: {
      functional: true,
      props: {
        row: Object,
        index: Number,
        render: Function
      },
      render(createElement, ctx) {
        const {row, index} = ctx.props
        return ctx.props.render(row, index)
      }
    }
  },
  data() {
    return {
      // 遮罩层
      loading: false,
      // 显示搜索条件
      showSearch: false,
      // 总条数
      total: 0,
      dataList: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 50,
      },
      open: false,
      // 列信息
      columns: [
        {key: 0, field: 'year', label: `年份`, visible: true},
        {key: 1, field: 'verName', label: `版本号`, visible: true},
        {key: 2, field: 'isReport', label: `预算上报`, visible: true, slots: true, render: (row, index) => {
            if (row.isReport === 0) {
              return (
                <el-tag type="danger">关闭</el-tag>
              )
            } else {
              return (
                <el-tag type="success">开启</el-tag>
              )
            }
          }
        },
        {key: 3, field: 'isAdjust', label: `预算调整`, visible: true, slots: true, render: (row, index) => {
            if (row.isAdjust === 0) {
              return (
                  <el-tag type="danger">关闭</el-tag>
              )
            } else {
              return (
                  <el-tag type="success">开启</el-tag>
              )
            }
          }
        },
      ],
      formData: {
        isAdjust: 0,
        isReport: 0,
      },
      rules: {
        year: [{
          required: true,
          message: '请输入年份',
          trigger: 'change'
        }],
        isReport: [{
          required: true,
          message: '请输入上报开关',
          trigger: 'change'
        }],
        isAdjust: [{
          required: true,
          message: '请输入调整开关',
          trigger: 'change'
        }],
        budgetVer: [{
          required: true,
          message: '请输入版本号',
          trigger: 'change'
        }],
      },
      options: [{
        "label": "关闭",
        "value": 0
      }, {
        "label": "开启",
        "value": 1
      }],
      versionList: [],
    };
  },
  watch: {
    // 根据名称筛选部门树
  },
  created() {
    this.handleQuery()
    this.getAllVersions()
  },
  methods: {
    // 查询所有版本
    getAllVersions() {
      listAllBudgetVersions().then(res => {
        this.versionList = res.data.map(el=>{
          return {value:el.id,label:el.name}
        })
      })
    },

    /** 重置按钮操作 */
    resetQuery() {
      this.queryParams = {
        pageNum: 1,
        pageSize: 50,
      }
    },
    handleQuery() {
      this.loading = true
      if(this.queryParams.year){
        this.queryParams.year = +this.queryParams.year
      }
      GetYearconfigList(this.queryParams).then(res => {
        this.dataList = res.rows
        this.total = res.total
        this.loading = false
      })
    },
    handleAdd() {
      this.formData = {
        isAdjust: 0,
        isReport: 0
      }
      this.open = true
    },
    handleConfirm() {
      this.$refs['elForm'].validate(valid => {
        if (!valid) return
        AddYearconfigList(this.formData).then(res => {
          if (res.code === 200) {
            this.$modal.msgSuccess("新增成功")
            this.handleQuery()
          } else {
            this.loading = false
            this.$modal.msgError(res.msg)
          }
          this.close()
        })
      })
    },
    handleDelete(row) {
      this.$modal.confirm("是否确认删除").then(() => {
        this.loading = true
        DeleteYearconfig(row.id).then(res => {
          this.$modal.msgSuccess("删除成功")
          this.handleQuery()
        })
      });
    },
    handleChange(row, column) {
      row[column] = row[column] === 1 ? 0 : 1
      this.loading = true
      EditYearconfigList(row).then(res => {
        if (res.code === 200) {
          this.$modal.msgSuccess("更新成功")
          this.handleQuery()
        } else {
          this.loading = false
          this.$modal.msgError(res.msg)
        }
      })
    },
    close() {
      this.open = false
    },
  }
};
</script>
<style>
</style>
<style lang="scss" scoped>
@import "@/assets/styles/business.scss";
</style>
