<template>
  <div class="budget-and-actual" v-loading="loading" element-loading-background="rgba(0, 0, 0, 0.4)">
    <Echarts :option="option" v-if="option" height="100%" :key="bdKey" />
  </div>
</template>

<script>
import * as echarts from "echarts";
import Echarts from '../../echarts/echarts.vue';
import { isBigScreen } from "@/views/cockpit/util/utils";
// api
import { getDiseaseEventCompare, getSpecialFundStatistics } from '@/api/cockpit/maintain';

export default {
  name: 'budgetAndActual',
  components: {
    Echarts,
  },
  props: {
    dataObj: {
      type: Object,
      default: () => ({
        unit: '个',
        xData: ["道路工程", "桥梁工程", "隧道工程", "机电工程", "房建工程", "供电专线维护", "三大系统"],
        budget: [220, 420, 210, 230, 450, 280, 220],
        actual: [500, 900, 890, 520, 910, 570, 510],
      })
    },
    year: {
      type: [String, Number],
      default: '',
    },
    type: {
      type: [String, Number],
      default: 1, // 1、预算与实际完成对比分析，2、专项养护资金使用情况
    }
  },
  data() {
    return {
      isBig: isBigScreen(),
      option: null,
      loading: false,
      bdKey: new Date().getTime() + ''
    }
  },
  created() {
    // this.option = this.initCharts();
  },
  watch: {
    year: {
      async handler(newVal) {
        if (newVal) {
          this.bdKey = new Date().getTime() + '';
          this.loading = true;
          let data = await this.getData();
          this.loading = false;
          let obj = {};
          if(this.type === 1) {
            // 获取数据并初始化图表
            let ysData = data.filter(v => v.name.includes('预算'))
            let sjData = data.filter(v => v.name.includes('实际完成'))
            let unit = '个'
            // 确定单位：如果数据中存在count值则为'个'，否则为'万元'
            if (data.length > 0) {
              unit = data[0].hasOwnProperty('count') && data[0].count ? '个' : '万元';
            }
            let xData = [];
            if (ysData && ysData.length) {
              ysData.forEach(v => xData.push(v.name.replace("预算", "")));
            } else {
              sjData.forEach(v => xData.push(v.name.replace("实际完成", "")));
            }
            obj = {
              unit,
              xData,
              budget: ysData.map(v => v.count || (v.sumFund ? v.sumFund / 10000 : 0)),
              actual: sjData.map(v => v.count || (v.sumFund ? v.sumFund / 10000 : 0)) || [],
            }
          } else {
            let ysData = data.map(v => v.budgetFund)
            let sjData = data.map(v => v.actualCompleteFund)
            let xData = data.map(v => v.typeName)
            let unit = 'k'
            obj = {
              unit,
              xData,
              budget: ysData|| [],
              actual: sjData || [],
            }
          }
         
          this.option = this.initCharts(obj);
        }
      },
      immediate: true,
      deep: true,
    }
  },
  methods: {
    // 获取数据
    getData() {
      return new Promise((resolve, reject) => {
        let obj = {
          1: getDiseaseEventCompare(this.year),
          2: getSpecialFundStatistics(this.year),
        };
        let resonsponse = obj[this.type]
        resonsponse.then((res) => {
          let data = res.rows || res.data;
          if (res.code === 200 && data) {
            resolve(data)
          } else {
            resolve([])
          }
        }).catch(err => {
          reject(err)
        })
      })
    },
    // 初始化图表
    initCharts(obj) {
      let option = {
        backgroundColor: 'rgba(255, 255, 255,0)',
        grid: {
          left: '1%',
          right: '5%',
          top: '15%',
          bottom: '2%',
          containLabel: true
        },
        tooltip: {},
        legend: {
          show: true,
          itemWidth: this.isBig ? 30 : 10, // 设置宽度
          itemHeight: this.isBig ? 30 : 10, // 设置高度
          textStyle: {
            color: '#fff',
            fontSize: this.isBig ? 24 : 12,
          },
          data: ['预算', '实际完成']
        },
        xAxis: {
          axisLine: {
            lineStyle: {
              color: 'transparent'
            }
          },
          axisLabel: {
            color: '#fff',
            interval: 0,
            rotate: obj.xData?.length > 4 ? 35 : 0,
            fontSize: this.isBig ? 24 : 12,
          },
          data: obj.xData,
        },
        yAxis: {
          type: 'value',
          name: obj.unit,
          min: 0,
          nameTextStyle: {
            padding: [0, 0, 0, -30],
            fontSize: this.isBig ? 24 : 12,
          },
          axisLabel: {
            formatter: '{value}',
            fontSize: this.isBig ? 24 : 12,
            textStyle: {
              color: '#999999'
            }
          },
          axisLine: {
            show: false,
            lineStyle: {
              color: '#6E7079'
            }
          },
          axisTick: {
            show: false,
          },
          splitLine: {
            show: true,
            lineStyle: {
              color: 'rgba(110,112,121,0.3)'
            },
          },
          splitArea: false,
        },
        series: [{
          name: '预算',
          type: 'bar',
          data: obj.budget,
          itemStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [{
              offset: 0,
              color: '#73F431'
            }, {
              offset: 1,
              color: 'rgba(115,244,49,0.2)'
            }])
          },
          barWidth: this.isBig ? 16 : 8,
        }, {
          name: '实际完成',
          type: 'bar',
          data: obj.actual,
          itemStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [{
              offset: 0,
              color: '#039EFE'
            }, {
              offset: 1,
              color: 'rgba(3,158,254,0.2)'
            }])
          },
          barWidth: this.isBig ? 16 : 8,
          barGap: "20%",
        }]
      };
      return option;
    }
  }
}
</script>

<style lang="scss" scoped>
.budget-and-actual {
  width: 100%;
  height: 100%;
  padding: 5px 10px;
}
</style>