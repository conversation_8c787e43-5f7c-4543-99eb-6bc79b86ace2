<template>
  <div class="road-interflow-edit">
    <el-tabs>
      <el-tab-pane label="中间计量表">
        <el-row :gutter="15">
          <el-col :span="24">
            <el-row :gutter="10" class="mb8">
              <el-col :span="1.5">
                <el-button
                    icon="el-icon-download"
                    size="mini"
                    type="primary"
                    @click="exportList1"
                >导出清单
                </el-button
                >
              </el-col>
            </el-row>
          </el-col>
          <el-col :span="24">
            <div class="draggable">
              <el-table v-adjust-table
                  ref="dataTable"
                  v-loading="loading"
                  :data="tableData1"
                  border
                  height="600"
                  highlight-current-row
                  row-key="id"
                  size="mini"
                  stripe
                  style="width: 100%"
              >
                <el-table-column
                    align="center"
                    label="序号"
                    type="index"
                    width="50"
                />
                <template v-for="(column,index) in columns1">
                  <el-table-column v-if="column.visible"
                                   :label="column.label"
                                   :prop="column.field"
                                   align="center">
                    <template slot-scope="scope">
                      <dict-tag v-if="column.dict" :options="dict.type[column.dict]" :value="scope.row[column.field]"/>
                      <template v-else-if="column.slots">
                        <RenderDom :index="index" :render="column.render" :row="scope.row"/>
                      </template>
                      <span v-else-if="column.isTime">{{ parseTime(scope.row[column.field], '{y}-{m}-{d}') }}</span>
                    <span v-else>{{ scope.row[column.field] }}</span>
                    </template>
                  </el-table-column>
                </template>
              </el-table>
              <pagination
                  v-show="total1>0"
                  :limit.sync="queryParams.pageSize"
                  :page.sync="queryParams.pageNum"
                  :total="total1"
                  @pagination="handleQuery1"
              />
            </div>
          </el-col>
        </el-row>
      </el-tab-pane>
      <el-tab-pane label="事件信息">
        <el-row>
          <el-row>
            <el-col :span="24">
              <el-form
                  ref="queryForm"
                  :inline="true"
                  :model="queryParams"
                  label-width="68px"
                  size="mini"
              >
                <el-form-item prop="disType">
                  <el-select v-model="queryParams.disType" filterable placeholder="请选择事件类型" clearable style="width: 190px;">
                    <el-option v-for="item in advicesList"
                               :key="item.value"
                               :label="item.label"
                               :value="item.value">
                    </el-option>
                  </el-select>
                </el-form-item>
                <el-form-item prop="maiSecName">
                  <RoadSection style="width: 190px" v-model="queryParams.maiSecName" placeholder="路段名称"/>
                </el-form-item>
                <el-form-item>
                  <RouteCodeSection style="width: 190px;" v-model="queryParams.routeCode"
                                    :maintenanceSectionId="queryParams.maiSecName" placeholder="路线编码"/>
                </el-form-item>
                <el-form-item>
                  <el-input
                      v-model="queryParams.beginMile"
                      placeholder="起止桩号"
                      clearable
                      style="width: 110px"
                  >
                  </el-input>
                  <div style="width: 20px;display: inline-block;text-align: center">~</div>
                  <el-input
                      v-model="queryParams.endMile"
                      placeholder="起止桩号"
                      clearable
                      style="width: 110px"
                  >
                  </el-input>
                </el-form-item>
                <el-form-item>
                  <el-input style="width: 190px" placeholder="子目号" v-model="queryParams.schemeCode"></el-input>
                </el-form-item>
                <el-form-item>
                  <el-input style="width: 190px" placeholder="方法名" v-model="queryParams.schemeName"></el-input>
                </el-form-item>
                <el-form-item>
                  <el-input style="width: 190px" placeholder="施工单编号" v-model="queryParams.code"></el-input>
                </el-form-item>
                <el-form-item>
                  <el-button icon="el-icon-search" size="mini" type="primary" @click="handleQuery2">搜索</el-button>
                  <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
                </el-form-item>
              </el-form>
            </el-col>
          </el-row>
          <el-row :gutter="10" class="mb8">
            <el-col :span="1.5">
              <el-button
                  icon="el-icon-view"
                  size="mini"
                  @click="openEventInfo"
                  type="primary"
              >事件信息
              </el-button
              >
            </el-col>
            <el-col :span="1.5">
              <el-button
                  icon="el-icon-download"
                  size="mini"
                  type="warning"
                  @click="exportList2"
              >导出清单
              </el-button
              >
            </el-col>
          </el-row>
          <el-col :span="24">
            <div class="draggable">
              <el-table v-adjust-table
                  ref="dataTable"
                  v-loading="loading"
                  :data="tableData2"
                  border
                  height="600"
                  highlight-current-row
                  row-key="detailId"
                  size="mini"
                  stripe
                  style="width: 100%"
                  @row-click="handleClickRow"
                  @selection-change="handleSelectionChange"
                  @expand-change="loadData"
              >
                <el-table-column type="expand">
                  <template slot-scope="props">
                    <el-table v-adjust-table v-loading="methodLoading" :data="props.row.methodList" style="width: 100%">
                      <el-table-column
                          align="center"
                          label=""
                          prop="">
                      </el-table-column>
                      <el-table-column
                          align="center"
                          label="子目号"
                          prop="schemeCode">
                      </el-table-column>
                      <el-table-column
                          align="center"
                          label="养护方法"
                          prop="schemeName">
                      </el-table-column>
                      <el-table-column
                          align="center"
                          label="计算式"
                          prop="calcDesc">
                      </el-table-column>
                      <el-table-column
                          align="center"
                          label="方法数量"
                          prop="num">
                      </el-table-column>
                      <el-table-column
                          align="center"
                          label="方法单位"
                          prop="unit">
                      </el-table-column>
                      <el-table-column
                          align="center"
                          label="单价"
                          prop="price">
                      </el-table-column>
                      <el-table-column
                          align="center"
                          label="金额"
                          prop="amount">
                      </el-table-column>
                    </el-table>
                  </template>
                </el-table-column>
                <el-table-column
                    align="center"
                    label="序号"
                    type="index"
                    width="50"
                />
                <template v-for="(column,index) in columns2">
                  <el-table-column v-if="column.visible" show-overflow-tooltip
                                   :label="column.label"
                                   :prop="column.field"
                                   :width="column.width"
                                   align="center">
                    <template slot-scope="scope">
                      <dict-tag v-if="column.dict" :options="dict.type[column.dict]" :value="scope.row[column.field]"/>
                      <template v-else-if="column.slots">
                        <RenderDom :index="index" :render="column.render" :row="scope.row"/>
                      </template>
                      <span v-else-if="column.isTime">{{ parseTime(scope.row[column.field], '{y}-{m}-{d}') }}</span>
                    <span v-else>{{ scope.row[column.field] }}</span>
                    </template>
                  </el-table-column>
                </template>
              </el-table>
              <pagination
                  v-show="total2>0"
                  :limit.sync="queryParams.pageSize"
                  :page.sync="queryParams.pageNum"
                  :total="total2"
                  @pagination="handleQuery2"
              />
            </div>
          </el-col>
        </el-row>
      </el-tab-pane>
      <el-tab-pane label="计量表明细">
        <el-row :gutter="15">
          <el-col :span="24">
            <el-row :gutter="10" class="mb8">
              <el-col :span="1.5">
                <el-button
                    icon="el-icon-download"
                    size="mini"
                    type="primary"
                    @click="exportList3"
                >导出清单
                </el-button
                >
              </el-col>
            </el-row>
          </el-col>
          <el-col :span="24">
            <div class="draggable">
              <el-table v-adjust-table
                  ref="dataTable"
                  v-loading="loading"
                  :data="tableData3"
                  border
                  height="600"
                  highlight-current-row
                  row-key="id"
                  size="mini"
                  stripe
                  style="width: 100%"
              >
                <el-table-column
                    align="center"
                    label="序号"
                    type="index"
                    width="50"
                />
                <template v-for="(column,index) in columns3">
                  <el-table-column v-if="column.visible"
                                   :label="column.label"
                                   :prop="column.field"
                                   align="center">
                    <template slot-scope="scope">
                      <dict-tag v-if="column.dict" :options="dict.type[column.dict]" :value="scope.row[column.field]"/>
                      <template v-else-if="column.slots">
                        <RenderDom :index="index" :render="column.render" :row="scope.row"/>
                      </template>
                      <span v-else-if="column.isTime">{{ parseTime(scope.row[column.field], '{y}-{m}-{d}') }}</span>
                    <span v-else>{{ scope.row[column.field] }}</span>
                    </template>
                  </el-table-column>
                </template>
              </el-table>
              <pagination
                  v-show="total3>0"
                  :limit.sync="queryParams.pageSize"
                  :page.sync="queryParams.pageNum"
                  :total="total3"
                  @pagination="handleQuery1"
              />
            </div>
          </el-col>
        </el-row>
      </el-tab-pane>
      <el-tab-pane label="扣款清单">
        <el-row :gutter="15">
          <el-col :span="24" v-if="type == 'view'">
            <el-row :gutter="10" class="mb8">
              <el-col :span="1.5">
                <el-button
                    icon="el-icon-plus"
                    size="mini"
                    type="primary"
                    v-has-menu-permi="['calcdaliy:deduction:add']"
                    @click="openDeduction = true; deduction = {}"
                >新增
                </el-button
                >
              </el-col>
            </el-row>
          </el-col>
          <el-col :span="24">
            <div class="draggable">
              <el-table v-adjust-table
                  ref="dataTable"
                  v-loading="loading"
                  :data="tableData4"
                  border
                  height="600"
                  highlight-current-row
                  row-key="id"
                  size="mini"
                  stripe
                  style="width: 100%"
              >
                <el-table-column
                    align="center"
                    label="序号"
                    type="index"
                    width="50"
                />
                <template v-for="(column,index) in columns4">
                  <el-table-column v-if="column.visible"
                                   :label="column.label"
                                   :prop="column.field"
                                   align="center">
                    <template slot-scope="scope">
                      <dict-tag v-if="column.dict" :options="dict.type[column.dict]" :value="scope.row[column.field]"/>
                      <template v-else-if="column.slots">
                        <RenderDom :index="index" :render="column.render" :row="scope.row"/>
                      </template>
                      <span v-else-if="column.isTime">{{ parseTime(scope.row[column.field], '{y}-{m}-{d}') }}</span>
                    <span v-else>{{ scope.row[column.field] }}</span>
                    </template>
                  </el-table-column>
                </template>
              </el-table>
              <pagination
                  v-show="total4>0"
                  :limit.sync="queryParams.pageSize"
                  :page.sync="queryParams.pageNum"
                  :total="total4"
                  @pagination="handleQuery4"
              />
            </div>
          </el-col>
        </el-row>
      </el-tab-pane>
      <el-tab-pane label="计量调整">
        <el-row :gutter="15">
          <el-col :span="24">
            <el-form
                ref="queryForm"
                :inline="true"
                :model="queryParams"
                label-width="68px"
                size="mini"
            >
              <el-form-item>
                <cost-select :type="9" v-model="queryParams.dataType" placeholder="费用类型" style="width: 190px"></cost-select>
              </el-form-item>
              <el-form-item>
                <el-input v-model="queryParams.schemeCode" placeholder="子目号" style="width: 190px"></el-input>
              </el-form-item>
              <el-form-item>
                <el-input v-model="queryParams.schemeName" placeholder="子目名称" style="width: 190px"></el-input>
              </el-form-item>
              <el-form-item>
                <el-button icon="el-icon-search" size="mini" type="primary" @click="handleQuery5">搜索</el-button>
                <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
              </el-form-item>
            </el-form>
          </el-col>
          <el-col :span="24" v-if="type == 'view'">
            <el-row :gutter="10" class="mb8">
              <el-col :span="1.5">
                <el-button
                    icon="el-icon-plus"
                    size="mini"
                    type="primary"
                    v-has-menu-permi="['calcdaliy:fundadjust:add']"
                    @click="openCost = true; cost = {}"
                >新增
                </el-button
                >
              </el-col>
            </el-row>
          </el-col>
          <el-col :span="24">
            <div class="draggable">
              <el-table v-adjust-table
                  ref="dataTable"
                  v-loading="loading"
                  :data="tableData5"
                  border
                  height="600"
                  highlight-current-row
                  row-key="id"
                  size="mini"
                  stripe
                  style="width: 100%"
              >
                <el-table-column
                    align="center"
                    label="序号"
                    type="index"
                    width="50"
                />
                <template v-for="(column,index) in columns5">
                  <el-table-column v-if="column.visible"
                                   :fixed="column.fixed"
                                   :label="column.label"
                                   :prop="column.field"
                                   align="center">
                    <template slot-scope="scope">
                      <dict-tag v-if="column.dict" :options="dict.type[column.dict]" :value="scope.row[column.field]"/>
                      <template v-else-if="column.slots">
                        <RenderDom :index="index" :render="column.render" :row="scope.row"/>
                      </template>
                      <span v-else-if="column.isTime">{{ parseTime(scope.row[column.field], '{y}-{m}-{d}') }}</span>
                    <span v-else>{{ scope.row[column.field] }}</span>
                    </template>
                  </el-table-column>
                </template>
              </el-table>
              <pagination
                  v-show="total5>0"
                  :limit.sync="queryParams.pageSize"
                  :page.sync="queryParams.pageNum"
                  :total="total5"
                  @pagination="handleQuery1"
              />
            </div>
          </el-col>
        </el-row>
      </el-tab-pane>
      <el-tab-pane label="材料调差">
        <el-row :gutter="15">
          <el-col :span="24">
            <el-form
                ref="queryForm"
                :inline="true"
                :model="queryParams"
                label-width="68px"
                size="mini"
            >
              <el-form-item>
                <cost-select :type="9" v-model="queryParams.dataType" placeholder="费用类型" style="width: 190px"></cost-select>
              </el-form-item>
              <el-form-item>
                <el-input v-model="queryParams.schemeCode" placeholder="子目号" style="width: 190px"></el-input>
              </el-form-item>
              <el-form-item>
                <el-input v-model="queryParams.schemeName" placeholder="子目名称" style="width: 190px"></el-input>
              </el-form-item>
              <el-form-item>
                <el-button icon="el-icon-search" size="mini" type="primary" @click="handleQuery6">搜索</el-button>
                <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
              </el-form-item>
            </el-form>
          </el-col>
          <el-col :span="24" v-if="type == 'view'">
            <el-row :gutter="10" class="mb8">
              <el-col :span="1.5">
                <el-button
                    icon="el-icon-plus"
                    size="mini"
                    type="primary"
                    v-has-menu-permi="['calcdaliy:materialadjust:add']"
                    @click="openMaterial = true, material = {}"
                >新增
                </el-button
                >
              </el-col>
            </el-row>
          </el-col>
          <el-col :span="24">
            <div class="draggable">
              <el-table v-adjust-table
                  ref="dataTable"
                  v-loading="loading"
                  :data="tableData6"
                  border
                  height="600"
                  highlight-current-row
                  row-key="id"
                  size="mini"
                  stripe
                  style="width: 100%"
              >
                <el-table-column
                    align="center"
                    label="序号"
                    type="index"
                    width="50"
                />
                <template v-for="(column,index) in columns6">
                  <el-table-column v-if="column.visible"
                                   :fixed="column.fixed"
                                   :label="column.label"
                                   :prop="column.field"
                                   align="center">
                    <template slot-scope="scope">
                      <dict-tag v-if="column.dict" :options="dict.type[column.dict]" :value="scope.row[column.field]"/>
                      <template v-else-if="column.slots">
                        <RenderDom :index="index" :render="column.render" :row="scope.row"/>
                      </template>
                      <span v-else-if="column.isTime">{{ parseTime(scope.row[column.field], '{y}-{m}-{d}') }}</span>
                    <span v-else>{{ scope.row[column.field] }}</span>
                    </template>
                  </el-table-column>
                </template>
              </el-table>
              <pagination
                  v-show="total6>0"
                  :limit.sync="queryParams.pageSize"
                  :page.sync="queryParams.pageNum"
                  :total="total6"
                  @pagination="handleQuery1"
              />
            </div>
          </el-col>
        </el-row>
      </el-tab-pane>
    </el-tabs>
    <el-dialog v-if="openDeduction" :visible.sync="openDeduction" append-to-body modal-append-to-body
               title="扣款信息"
               width="80%">
      <add-deduction :row-data="deduction" :settleId="rowData.id" @close="modelClose"></add-deduction>
    </el-dialog>
    <el-dialog v-if="openCost" :visible.sync="openCost" append-to-body modal-append-to-body title="费用调整"
               width="80%">
      <add-cost :settle-data="rowData" :row-data="cost" :settleId="rowData.id" @close="modelClose" :con-id="rowData.conId"></add-cost>
    </el-dialog>
    <el-dialog v-if="openMaterial" :visible.sync="openMaterial" append-to-body modal-append-to-body title="材料调差"
               width="80%">
      <add-material :row-data="material" :prnt-data="rowData" @close="modelClose"></add-material>
    </el-dialog>
    <el-dialog title="附件列表" append-to-body modal-append-to-body :visible.sync="openFile" width="80%" v-if="openFile">
      <file-upload v-model="disFilePath" :forView="true"></file-upload>
    </el-dialog>
    <el-dialog
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :destroy-on-close="true"
      append-to-body modal-append-to-body
      :visible.sync="eventInfoVisible"
      v-if="eventInfoVisible"
      title="事件信息"
      width="80%">
      <event-detail :dis-id="disId"/>
    </el-dialog>
  </div>
</template>
<script>
import {
  deleteMiddle,
  middleListBySid,
  listScheme,
  fetchDeductionList, deleteDeduction,
  fetchFundAdjustList, deleteFundAdjust,
  fetchMaterialAdjustList, deleteMaterialAdjust, listEvent
} from '@/api/dailyMaintenance/metering/settlementApplication'
import {listAllDiseases} from "@/api/patrol/diseases";
import {listEventBySettleId, listMethodBySettleId} from "@/api/dailyMaintenance/metering/middleApplication";
import AddDeduction from "@/views/dailyMaintenance/metering/settlementApplication/addDeduction.vue";
import AddCost from "@/views/dailyMaintenance/metering/settlementApplication/addCost.vue";
import AddMaterial from "@/views/dailyMaintenance/metering/settlementApplication/addMaterial.vue";
import RoadSection from "@/views/baseData/components/roadSection/index.vue";
import RouteCodeSection from "@/components/RouteCodeSection/index.vue";
import CostSelect from "@/components/CostSelect/index.vue";
import EventDetail from "@/views/dailyMaintenance/component/eventDetail.vue";
import {multiply, round} from "lodash";

export default {
  components: {
    EventDetail,
    CostSelect,
    RouteCodeSection, RoadSection,
    AddDeduction, AddCost, AddMaterial,
    RenderDom: {
      functional: true,
      props: {
        row: Object,
        index: Number,
        render: Function
      },
      render(createElement, ctx) {
        const {row, index} = ctx.props
        return ctx.props.render(row, index)
      }
    }
  },
  dicts: ['route_direction', 'lane', 'sys_asset_type', 'deduction_type', 'adjustment_type', 'cost_name'],
  data() {
    return {
      queryParams: {
        pageNum: 1,
        pageSize: 10,
      },
      loading: false,
      tableData1: [],
      total1: 0,
      openFile: false,
      disFilePath: '',
      eventInfoVisible: false,
      disId: '',
      columns1: [
        {key: 0, width: 100, field: 'name', label: `中间计量名称`, visible: true},
        {key: 1, width: 100, field: 'code', label: `中间计量编号`, visible: true},
        {key: 2, width: 100, field: 'domainName', label: `管养单位`, visible: true},
        {key: 3, width: 100, field: 'calcDomainName', label: `申请计量单位`, visible: true},
        {key: 4, width: 100, field: 'maiSecId', label: `路段名称`, visible: true},
        {key: 5, width: 100, field: 'conName', label: `合同名称`, visible: true},
        {key: 6, width: 100, field: 'calcFund', label: `计量资金(元)`, visible: true},
        {key: 7, width: 100, field: 'productionFund', label: `安全生产费(元)`, visible: true, slots: true, render: (row) => {
            return (
                <span>{row.productionFund?.toFixed(0)}</span>
            );
          }},
        {key: 8, width: 100, field: 'guaranteeFund', label: `安全保通费(元)`, visible: true, slots: true, render: (row) => {
            return (
                <span>{row.guaranteeFund?.toFixed(0)}</span>
            );
          }},
        {key: 9, width: 100, field: 'supFund', label: `监理费(元)`, visible: true, slots: true, render: (row) => {
            return (
              <span>{row.supFund?.toFixed(0)}</span>
            );
          }},
        {key: 10, width: 100, field: 'calcDate', label: `计量日期`, visible: true},
        {key: 11, width: 100, field: 'createTime', label: `操作日期`, visible: true},
        // {key: 12, width: 100, field: 'field12', label: `操作意见`, visible: true},
        {key: 13, width: 100, field: 'remark', label: `备注`, visible: true},
        // {key: 14, width: 100, field: 'status', label: `状态`, visible: true},
      ],
      tableData2: [],
      total2: 0,
      columns2: [
        {key: 0, width: 100, field: 'code', label: `施工单编号`, visible: true},
        {key: 1, width: 100, field: 'assetMainTypeName', label: `资产类型`, visible: true, dict: 'sys_asset_type'},
        {key: 2, width: 100, field: 'maiSecName', label: `路段名称`, visible: true},
        {key: 3, width: 100, field: 'routeCode', label: `路线编码`, visible: true},
        {key: 4, width: 100, field: 'direction', label: `上下行`, visible: true, dict: 'route_direction'},
        {key: 5, width: 100, field: 'lane', label: `位置`, visible: true, dict: 'lane'},
        {key: 6, width: 100, field: 'beginMile', label: `起点桩号`, visible: true, slots: true, render: (row, index) => {
            return (
                <span>{this.formatPile(row.beginMile)}</span>
            )
          }},
        {key: 7, width: 100, field: 'endMile', label: `终点桩号`, visible: true, slots: true, render: (row, index) => {
            return (
                <span>{this.formatPile(row.endMile)}</span>
            )
          }},
        {key: 8, width: 100, field: 'disTypeName', label: `事件类型`, visible: true, dict: 'sys_asset_type'},
        {key: 9, width: 100, field: 'disDesc', label: `事件描述`, visible: true},
        {key: 10, width: 100, field: 'sumFund', label: `费用(元)`, visible: true, slots: true, render: (row) => {
            return (
              <span>{row.sumFund?.toFixed(0)}</span>
            );
          }},
        {key: 11, width: 100, field: 'productionFund', label: `安全生产费`, visible: true, slots: true, render: (row) => {
            return (
                <span>{row.productionFund?.toFixed(0)}</span>
            );
          }},
        {key: 12, width: 100, field: 'guaranteeFund', label: `安全保通费`, visible: true, slots: true, render: (row) => {
            return (
                <span>{row.guaranteeFund?.toFixed(0)}</span>
            );
          }},
        {key: 13, width: 100, field: 'supFund', label: `监理费`, visible: true, slots: true, render: (row) => {
            return (
                <span>{row.supFund?.toFixed(0)}</span>
            );
          }},
        {key: 14, width: 100, field: 'field14', label: `图片`, visible: true},
        {key: 15, width: 100, field: 'supDomainName', label: `监理单位`, visible: true},
        {key: 16, width: 100, field: 'operator', label: `操作人`, visible: true},
        {key: 17, width: 100, field: 'operatorTime', label: `操作时间`, visible: true},
      ],

      tableData3: [],
      total3: 0,
      columns3: [
        {key: 0, width: 100, field: 'schemeName', label: `费用名称`, visible: true},
        {key: 1, width: 100, field: 'schemeCode', label: `子目号`, visible: true},
        {key: 2, width: 100, field: 'schemeName', label: `子目名称`, visible: true},
        {key: 3, width: 100, field: 'unit', label: `单位`, visible: true},
        {key: 4, width: 100, field: 'price', label: `单价`, visible: true},
        {key: 5, width: 100, field: 'num', label: `数量`, visible: true},
        {key: 6, width: 100, field: 'amount', label: `金额`, visible: true},
      ],

      tableData4: [],
      total4: 0,
      columns4: [
        {key: 1, width: 100, field: 'mtype', label: `扣款类型`, visible: true, dict: 'deduction_type'},
        {key: 2, width: 100, field: 'fund', label: `扣款金额`, visible: true},
        {
          key: 3,
          width: 100,
          field: 'isSupFund',
          label: `是否计算监理费`,
          visible: true,
          slots: true,
          render: (row, index) => {
            if (row.isSupFund == 0) {
              return <div>不计算</div>
            } else {
              return <div>计算</div>
            }
          }
        },
        {key: 4, width: 100, field: 'remark', label: `扣款描述`, visible: true},
        {key: 6, width: 100, field: 'fileId', label: `附件`, visible: true, slots: true, render: (row, index) => {
            return (
              <el-button
                size="mini"
                disabled={!row.fileId}
                type="text" onClick={e => this.handleOpenFile(e, row)}>查看</el-button>
            )
          }},
        {
          key: 5,
          width: 100,
          field: 'fileId',
          label: `操作`,
          visible: this.type == 'view',
          fixed: "right",
          slots: true,
          render: (row, index) => {
            return (
                <div>
                  <el-button
                      size="mini"
                      type="text" v-has-menu-permi={['calcdaliy:deduction:edit']} onClick={e => this.handleOpenEdit(row, '4')}>编辑
                  </el-button>
                  <el-button
                      size="mini"
                      type="text" v-has-menu-permi={['calcdaliy:deduction:remove']} onClick={e => this.handleDeleteOther(row, '4')}>删除
                  </el-button>
                </div>
            )
          }
        },
      ],
      tableData5: [],
      total5: 0,
      columns5: [
        {key: 0, width: 100, field: 'adjustmentType', label: `调整类型`, visible: true, dict: 'adjustment_type'},
        {key: 1, width: 100, field: 'dataType', label: `费用类型`, visible: true, dict: 'cost_name'},
        {key: 2, width: 100, field: 'schemeCode', label: `子目号`, visible: true},
        {key: 3, width: 100, field: 'schemeName', label: `子目名称`, visible: true},
        {key: 4, width: 100, field: 'price', label: `单价`, visible: true},
        {key: 5, width: 100, field: 'unit', label: `单位`, visible: true},
        {key: 6, width: 100, field: 'num', label: `数量`, visible: true},
        {key: 7, width: 100, field: 'amount', label: `金额`, visible: true},
        {
          key: 8,
          width: 100,
          field: 'isSupFund',
          label: `是否计量监理费`,
          visible: true,
          slots: true,
          render: (row, index) => {
            if (row.isSupFund == 0) {
              return <div>不计算</div>
            } else {
              return <div>计算</div>
            }
          }
        },
        {key: 9, width: 100, field: 'supDomainName', label: `监理单位`, visible: true},
        {key: 10, width: 100, field: 'remark', label: `备注`, visible: true},
        {key: 11, width: 100, field: 'createuser', label: `操作人`, visible: true},
        {key: 12, width: 100, field: 'createTime', label: `操作时间`, visible: true},
        {
          key: 13,
          width: 150,
          field: 'fileId',
          label: `操作`,
          visible: this.type == 'view',
          fixed: "right",
          slots: true,
          render: (row, index) => {
            return (
                <div>
                  <el-button
                      size="mini"
                      type="text" v-has-menu-permi={['calcdaliy:fundadjust:edit']} onClick={e => this.handleOpenEdit(row, '5')}>编辑
                  </el-button>
                  <el-button
                      size="mini"
                      type="text" v-has-menu-permi={['calcdaliy:fundadjust:remove']} onClick={e => this.handleDeleteOther(row, '5')}>删除
                  </el-button>
                </div>
            )
          }
        },
      ],
      tableData6: [],
      total6: 0,
      columns6: [
        {key: 0, width: 100, field: 'dataType', label: `费用类型`, visible: true, dict: 'cost_name'},
        {key: 1, width: 100, field: 'projName', label: `调差项目`, visible: true},
        {key: 2, width: 100, field: 'schemeCode', label: `子目号`, visible: true},
        {key: 3, width: 100, field: 'schemeName', label: `子目名称`, visible: true},
        {key: 4, width: 100, field: 'price', label: `单价`, visible: true},
        {key: 5, width: 100, field: 'unit', label: `单位`, visible: true},
        {key: 6, width: 100, field: 'calcDesc', label: `计算式`, visible: true},
        {key: 7, width: 100, field: 'num', label: `数量`, visible: true},
        {key: 8, width: 100, field: 'amount', label: `金额`, visible: true},
        {key: 9, width: 100, field: 'remark', label: `备注`, visible: true},
        {
          key: 10,
          width: 150,
          field: 'fileId',
          label: `操作`,
          visible: this.type == 'view',
          fixed: "right",
          slots: true,
          render: (row, index) => {
            return (
                <div>
                  <el-button
                      size="mini"
                      type="text" v-has-menu-permi={['calcdaliy:materialadjust:edit']} onClick={e => this.handleOpenEdit(row, '6')}>编辑
                  </el-button>
                  <el-button
                      size="mini"
                      type="text" v-has-menu-permi={['calcdaliy:materialadjust:remove']} onClick={e => this.handleDeleteOther(row, '6')}>删除
                  </el-button>
                </div>
            )
          }
        },
      ],
      openDeduction: false,
      deduction: {},
      openCost: false,
      cost: {},
      openMaterial: false,
      material: {},
      advicesList: [],
    }
  },
  props: {
    rowData: {
      type: Object,
      default: () => {
      }
    },
    type: {
      type: String,
      default: ''
    }
  },
  created() {
    this.getDisList()
    this.handleQuery1()
    this.handleQuery2()
    this.handleQuery3()
    this.handleQuery4()
    this.handleQuery5()
    this.handleQuery6()
  },
  methods: {
    handleQuery1() {
      this.loading = true
      this.queryParams.id = this.rowData.id
      middleListBySid(this.queryParams).then(res => {
        this.tableData1 = res.rows
        this.total1 = res.total
        this.loading = false
      })
    },
    handleQuery2() {
      this.loading = true
      this.queryParams.settlecalcId = this.rowData.id
      this.queryParams.settleId = this.rowData.id
      this.queryParams.maiSecId = this.maiSecId
      listEvent(this.queryParams).then(res => {
        this.tableData2 = res.rows
        this.total2 = res.total
        this.loading = false
      })
    },
    handleQuery3() {
      this.loading = true
      this.queryParams.settlecalcId = this.rowData.id
      listScheme(this.queryParams).then(res => {
        this.tableData3 = res.rows
        this.total3 = res.total
        this.loading = false
      })
    },
    handleQuery4() {
      this.loading = true
      this.queryParams.settleId = this.rowData.id
      fetchDeductionList(this.queryParams).then(res => {
        this.tableData4 = res.rows
        this.total4 = res.total
        this.loading = false
      })
    },
    handleQuery5() {
      this.loading = true
      this.queryParams.settleId = this.rowData.id
      fetchFundAdjustList(this.queryParams).then(res => {
        this.tableData5 = res.rows
        this.total5 = res.total
        this.loading = false
      })
    },
    handleQuery6() {
      this.loading = true
      this.queryParams.settleId = this.rowData.id
      fetchMaterialAdjustList(this.queryParams).then(res => {
        this.tableData6 = res.rows
        this.tableData6.forEach(item => {
          item.amount = round(multiply(Number(item.num) || 0, Number(item.price) || 0),2)
        })
        this.total6 = res.total
        this.loading = false
      })
    },
    handleDelete(row) {
      this.$modal.confirm('是否确认删除').then(() => {
        deleteMiddle(row.detailId).then(() => {
          this.$modal.msgSuccess('删除成功')
          this.handleQuery1()
          this.$emit('pageUpdate')
        })
      })

    },
    handleOpenFile(e, row) {
      this.disFilePath = row.fileId
      this.openFile = true
    },
    exportList1() {
      this.download(
          'manager/settlecalc/detail/export',
          {settleId: this.rowData.id},
          `settlecalc_detail_${new Date().getTime()}.xlsx`,
          {
            headers: {'Content-Type': 'application/json;'},
            parameterType: 'body'
          }
      )
    },
    exportList2() {
      this.download(
          'manager/settlecalc/detail/event/export',
          {...this.queryParams},
          `intermediate_detail_${new Date().getTime()}.xlsx`,
          {
            headers: {'Content-Type': 'application/json;'},
            parameterType: 'body'
          }
      )
    },
    exportList3() {
      this.download(
          'manager/method/settlecalc/export',
          {...this.queryParams},
          `method_settlecalc_${new Date().getTime()}.xlsx`,
          {
            headers: {'Content-Type': 'application/json;'},
            parameterType: 'body'
          }
      )
    },
    // 选中
    handleSelectionChange(e) {
      this.selectIds = e.map(item => item.detailId)
    },
    loadData(row) {
      this.methodLoading = true
      listEventBySettleId({settleId: row.settleId, detailId: row.detailId}).then(res => {
        this.$set(row, 'methodList', res.data)
        this.methodLoading = false
      })
    },
    resetQuery() {
      this.queryParams = {
        pageNum: 1,
        pageSize: 10,
      }
    },
    modelClose(index) {
      this.openDeduction = false
      this.openCost = false
      this.openMaterial = false
      switch (index) {
        case "4":
          this.handleQuery4();
          break;
        case "5":
          this.handleQuery5();
          break;
        case "6":
          this.handleQuery6();
          break;
      }
      this.$emit('pageUpdate')
    },
    handleDeleteOther(row, index) {
      this.$modal.confirm('是否确认删除').then(() => {
        switch (index) {
          case "4":
            deleteDeduction(row.id).then(() => {
              this.$modal.msgSuccess('删除成功')
              this.handleQuery4()
            })
            break;
          case "5":
            deleteFundAdjust(row.id).then(() => {
              this.$modal.msgSuccess('删除成功')
              this.handleQuery5()
            })
            break;
          case "6":
            deleteMaterialAdjust(row.id).then(() => {
              this.$modal.msgSuccess('删除成功')
              this.handleQuery6()
            })
            break;
        }
        this.$emit('pageUpdate')
      })
    },
    handleOpenEdit(row, index) {
      switch (index) {
        case "4":
          this.deduction = row
          this.openDeduction = true
          break;
        case "5":
          this.cost = row
          this.openCost = true
          this.handleQuery5();
          break;
        case "6":
          this.material = row
          this.openMaterial = true
          this.handleQuery6();
          break;
      }
    },
    handleClickRow(e) {
      this.disId = e.disId
    },
    openEventInfo() {
      if (!this.disId) {
        this.$modal.msgWarning("请先选择数据")
        return
      }
      this.eventInfoVisible = true
    },
    // 获取事件列表
    getDisList() {
      listAllDiseases().then(res => {
        res.data.forEach(item => {
          this.advicesList.push({
            label: item.diseaseName,
            value: item.id
          })
        })
      })
    },
  }
}
</script>
<style lang="scss" scoped>
</style>
<style lang="scss" scoped>
@import "@/assets/styles/business.scss";
</style>
