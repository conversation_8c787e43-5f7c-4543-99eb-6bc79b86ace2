<template>
  <div class="bim" :class="theme">
    <el-tabs tab-position="top" v-model="activeName" type="border-card">
      <!-- <el-tab-pane v-if="hasBim" label="BIM模型" name="1"></el-tab-pane> -->
      <!-- <el-tab-pane v-if="panoramic720view" label="全景图" name="2"></el-tab-pane> -->
      <el-tab-pane label="地图" name="0"></el-tab-pane>
      <el-tab-pane v-if="videoInfo.length > 0" label="监测点视频" name="3"></el-tab-pane>
      <el-tab-pane label="监测点图片" name="4"></el-tab-pane>
      <el-tab-pane label="设备在线状态" name="5"></el-tab-pane>
    </el-tabs>

    <!-- <div class="body" v-if="hasBim" v-show="activeName === '1'">
      <Bim :bimToRealTime="bimToRealTime" />
    </div> -->
    <!-- <div class="body" v-show="activeName === '2'">
      <iframe :src="panoramic720view" width="100%" height="100%" frameborder="0" allowfullscreen>
      </iframe>
    </div> -->

    <div class="body" v-show="activeName === '0'">
      <map-view @mapClick="mapClickeve" />
    </div>

    <div class="body video" v-show="activeName === '3' && videoInfo.length > 0">
      <el-tabs class="original" tab-position="top" v-model="videoName" @tab-click="tabClick">
        <el-tab-pane v-for="(item, index) in videoInfo" :key="index" :label="item.label"
          :name="item.label"></el-tab-pane>
      </el-tabs>
      <div class="video-box" v-if="videoUrl">
        <newVideo width="100%" height="100%" :url="videoUrl"></newVideo>
      </div>
    </div>

    <div class="body" style="overflow: auto; display: flex; align-items: center;" v-show="activeName === '4'">



      <el-image :src="structureImages[0]" :preview-src-list="srcList" style="width: 100%;"></el-image>
      <!-- <el-carousel trigger="click" style="width: 100%; height: 100%;">
        <el-carousel-item v-for="(item, index) in structureImages" :key="index"
          style="display: grid; place-items: center;">
          <el-image :src="item" :preview-src-list="srcList" style="height: 100%;"></el-image>
        </el-carousel-item>
      </el-carousel> -->
    </div>
    <div class="body" v-show="activeName === '5'">
      <equipmentonlineStatus />
    </div>
    <div class="btns">
      <div class="btn" @click="openWarningRecord" style="color: red;">超限警告记录</div>
      <div class="btn" @click="openWarningExecute">预警执行记录</div>
      <div class="btn" @click="handleEnd" v-if="checkData.flashType != '默认' && checkData.planStatus == 1">结束预案</div>
      <div class="btn" @click="handleBegin"
        v-if="checkData.flashType != '默认' && checkData.planStatus == 0 && checkData.isHasOffFlashDev == '0'">启动预案
      </div>
    </div>
    <div style="position:fixed; z-index: 2000; width: 90vw;height: 90vh;left: 5%;top: 5%" class="model-box"
      v-if="recordVisible">
      <div class="title" style="z-index: 999">预警记录
        <i class="el-icon-close" @click="recordVisible = false"></i>
      </div>
      <div class="content" style="padding: 0">
        <warning-record :check-data='checkData' :installLocation="installLocation" style="width: 100%;height: 100%"
          frameborder="0" @query='$emit("query")'></warning-record>
      </div>
    </div>
    <div style="position:fixed; z-index: 2000; width: 90vw;height: 90vh;left: 5%;top: 5%" class="model-box"
      v-if="executeVisible">
      <div class="title" style="z-index: 999">预警执行记录
        <i class="el-icon-close" @click="executeVisible = false"></i>
      </div>
      <div class="content" style="padding: 0">
        <warning-execute :check-data='checkData' style="width: 100%;height: 100%" frameborder="0"></warning-execute>
      </div>
    </div>
    <div class="model-box" v-if="modelVisible" title="启动预案" ref="dialog2" @mousedown="bringToFront('dialog2')">
      <div class="title" @mousedown="startDrag($event, 'dialog2')">启动预案
        <i class="el-icon-close" @click="modelVisible = false"></i>
      </div>
      <div class="content" style="color: white;">
        <el-form ref="elForm" :model="formData" :rules="rules" :label-width="'100px'">
          <el-row>
            <el-col :span="16">
              <!--              <el-form-item label="是否自定义" prop="isCustom">-->
              <!--                <el-select v-model="formData.isCustom" @change="changeIsCustom" popper-class="type-popper"-->
              <!--                           :popper-append-to-body="false">-->
              <!--                  <el-option label="否" value="0"></el-option>-->
              <!--                  <el-option label="是" value="1"></el-option>-->
              <!--                </el-select>-->
              <!--              </el-form-item>-->
              <el-form-item label="预案模式" prop="flashMode">
                <el-select v-model="formData.flashMode" @change="changeFlashMode" popper-class="type-popper"
                  :popper-append-to-body="false">
                  <el-option v-for="(item, index) in flashModes" :key="index" :label="item.label"
                    :value="item.value"></el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="爆闪">
                {{ flashData.strobe ? '开' : '关' }}
              </el-form-item>
              <el-form-item label="显示">
                {{ flashData.displayName }}
              </el-form-item>
              <el-form-item label="闪烁">
                {{ flashData.flicker ? '常规' : '常亮' }}
              </el-form-item>
              <el-form-item label="音频">
                {{ flashData.sound }}
              </el-form-item>
            </el-col>
            <el-col :span="8" class="tips">
              <img v-if="flashData.strobe" src="@/assets/earlyWarning/ssfwd.gif">
              <img v-else src="@/assets/earlyWarning/ssfwd.png">
              <div v-if="flashData.displayName != 'ball'" class="mode-dispaly" :style="{ 'color': flashData.display }"
                :class="flashData.flicker ? 'blink' : ''">{{ flashData.displayName }}
              </div>
              <div v-else class="display-ball" :style="{ 'background-color': flashData.display }"
                :class="flashData.flicker ? 'blink' : ''">
              </div>
            </el-col>
          </el-row>
          <div style="text-align: right; margin-top: 20px">
            <el-button class="model-btn" type="primary" @click="handleSubmit">发布</el-button>
          </div>
        </el-form>
      </div>
    </div>
  </div>
</template>

<script>
import Bim from './components/Bim.vue'
import newVideo from './components/Video/newVideo.vue'
const equipmentonlineStatus = () => import('./components/equipmentonlineStatus.vue')
const warningRecord = () => import('./components/warningRecord.vue')
const warningExecute = () => import('./components/warningExecute.vue')
const mapView = () => import('./components/map.vue')
import { fetchGet } from './api.js'
import {
  getZjgtConfigPage,
  getSsConfigPage,
  getWdmConfigPage,
  getCommonConfigPage,
} from '@/api/jgjc/flashingModeConfig'
import { zjgtModes, wdmModes, sansiModes } from "./components/defaultModes.js"

export default {
  name: 'BimAndPictures',
  props: {
    theme: {
      type: String,
      default: 'dark'
    }
  },
  components: { Bim, newVideo, equipmentonlineStatus, warningRecord, warningExecute, mapView },
  data() {
    return {
      installLocation: "",
      activeName: "0",
      hasBim: false,
      structureImages: ["https://jkjc.yciccloud.com:8002/ynjt-structure-file/7c28243f509445fb86fafaa6a0d0fccd-治租河.jpeg"],
      srcList: ["https://jkjc.yciccloud.com:8002/ynjt-structure-file/7c28243f509445fb86fafaa6a0d0fccd-治租河.jpeg",],
      videoInfo: [],
      videoName: "",
      videoUrl: "",
      panoramic720view: "",
      checkData: {
        planStatus: 0, // 预案状态 0：未启动 1：已启动
      },  // 详情数据
      recordVisible: false, // 预警操作记录
      executeVisible: false, // 预警执行记录
      modelVisible: false, // 启动预案
      formData: {}, // 启动预案表单数据
      rules: { // 启动预案表单校验规则
        flashMode: [{ required: true, message: '请选择爆闪模式', trigger: 'change' }]
      },
      flashModes: zjgtModes, // 爆闪模式
      flashData: { // 爆闪数据
        strobe: false, // 爆闪
        displayName: '', // 显示
        display: '', // 显示颜色
        flicker: false, // 闪烁
        sound: '', // 音频
      },
    }
  },
  mounted() {
    // 监听Tree组件那里传过来的obj，属性分别有：fileId、videoInfo、structureImage
    window.$Bus.$on('treeToBim', (obj) => {
      this.hasBim = obj.fileId ? true : false
      if (obj.code === "1593262524063682560" || obj.code === "1593262527993745408") {
        obj.panoramic720view = "https://www.720yun.com/vr/4dbj5Oyyuf7"
      }
      // this.activeName = obj.fileId ? '1' : obj.panoramic720view ? '2' : obj.videoInfo ? '3' : '4'
      // this.activeName = obj.videoInfo ? '3' : '4'
      if (obj.structureImage) {
        this.structureImages = [obj.structureImage]
        this.srcList = [obj.structureImage]
      }
      if (obj.videoInfo) {
        this.videoInfo = obj.videoInfo
        this.videoName = this.videoInfo[0].label
        this.tabClick()
      }
      if (obj.panoramic720view) {
        this.panoramic720view = obj.panoramic720view
      }
    })
    const data = localStorage.getItem('mapData');
    const parsedData = JSON.parse(data);
    this.checkData = Array.isArray(parsedData) ? parsedData[0] : parsedData
  },
  methods: {
    // 点击BIM传感器节点后 绘制图表
    async bimToRealTime(sensorInfoList) {
      if (sensorInfoList === undefined || sensorInfoList.length === 0) {
        this.$message({ type: 'warning', message: "该构件没有对应传感器信息", duration: 2000 });
        return;
      }
      window.$Bus.$emit('bimToCharts', sensorInfoList)
    },
    tabClick() {
      this.videoUrl = ""
      const url = "https://jkjc.yciccloud.com:8000/xboot/structurevideo/getByStructureCode"
      const data = this.videoInfo.find(item => item.label === this.videoName)
      fetchGet(url, data).then(res => {
        if (res.code === 200) {
          let urlList = res.result.records;
          urlList.forEach((item) => {
            if (item.id == data.id) {
              // this.videoUrl = String('https://jkjc.glyhgl.com:22585' + item.videoUrl);
              this.videoUrl = item.videoUrl
            }
          });
        }
      })
    },
    openWarningRecord() {
      this.recordVisible = true
    },
    openWarningExecute() {
      this.executeVisible = true
    },
    handleBegin() {
      this.formData = {
        isCustom: '0'
      }
      this.modelVisible = true
      this.flashModes = []
      if (this.checkData.flashType == '中交国通') {
        getZjgtConfigPage({
          pageNum: 1,
          pageSize: 100,
        }).then(res => {
          for (let i = 0; i < res.rows.length; i++) {
            const item = res.rows[i]
            const temp = {}
            temp.zjgtConfigEntity = item
            temp.isCustom = item.isCustom
            temp.label = item.modeName
            temp.value = item.id
            temp.strobe = item.rbSw == 1
            temp.flicker = false
            if (item.textSw == 1) {
              temp.display = this.getColor(item.textColorA)
            } else {
              temp.display = this.getColor(item.textColorB)
            }
            if (item.textSw == 1) {
              temp.displayName = item.textA
            } else if (item.textSw == 2) {
              temp.displayName = item.textB
            } else if (item.textSw == 3) {
              temp.displayName = item.textA + item.textB
            }
            if (item.voiceTrack == 0) {
              temp.sound = item.voiceContent
            } else {
              temp.sound = this.dict.translate('voice_track', item.voiceTrack)
            }
            this.flashModes.push(temp)
          }
          console.log(this.flashModes)
        })

      }
      if (this.checkData.flashType == '三思') {
        // this.flashModes = sansiModes
        getSsConfigPage({
          pageNum: 1,
          pageSize: 100,
        }).then(res => {
          for (let i = 0; i < res.rows.length; i++) {
            const item = res.rows[i]
            const temp = {}
            temp.sansiConfigEntity = item
            temp.isCustom = item.isCustom
            temp.label = item.name
            temp.value = item.id
            temp.strobe = item.lampSwitch == 1
            temp.flicker = item.frequency != 0
            temp.display = item.textColor == 0 ? 'yellow' : 'red'
            temp.displayName = item.screenWord
            temp.sound = item.soundContent
            this.flashModes.push(temp)
          }
        })
      }
      if (this.checkData.flashType == '维的美') {
        getWdmConfigPage({
          pageNum: 1,
          pageSize: 100,
        }).then(res => {
          for (let i = 0; i < res.rows.length; i++) {
            const item = res.rows[i]
            const temp = {}
            temp.wdmConfigEntity = item
            temp.isCustom = item.isCustom
            temp.label = item.modeName
            temp.value = item.id
            temp.strobe = item.flashSw == 1
            temp.flicker = false
            temp.display = item.textColor == 0 ? 'red' : item.textColor == 1 ? 'green' : 'yellow'
            temp.displayName = item.textContent
            temp.sound = item.voiceContent
            this.flashModes.push(temp)
          }
        })
        // this.flashModes = wdmModes
      }
      if (this.checkData.flashType == '第三方') {
        getCommonConfigPage({
          pageNum: 1,
          pageSize: 100,
        }).then(res => {
          for (let i = 0; i < res.rows.length; i++) {
            const item = res.rows[i]
            const temp = {}
            temp.flashCommonModeConfig = item
            temp.isCustom = item.isCustom
            temp.label = item.modeName
            temp.value = item.id
            temp.strobe = item.flashSw == 1
            temp.flicker = false
            temp.display = item.textColor == '红色' ? 'red' : item.textColor == '绿色' ? 'green' : 'yellow'
            temp.displayName = item.textContent
            temp.sound = item.voiceContent
            this.flashModes.push(temp)
          }
        })
        // this.flashModes = wdmModes
      }
    },
    handleEnd() {
      this.$modal.confirm("是否结束预案?").then(() => {
        const params = {
          structId: this.checkData.id,
          flashType: this.checkData.flashType
        }
        endPlan(params).then(res => {
          if (res.code == 200) {
            this.$message.success("操作成功")
            this.modelVisible = false
            this.$emit('query')
            this.checkData.planStatus = 0
            this.checkData.isHasOffFlashDev = '0'
          }
        })
      })
    },
    getColor(value) {
      if (value == 1) {
        return 'red'
      } else if (value == 2) {
        return 'green'
      } else if (value == 3) {
        return 'none'
      } else if (value == 4) {
        return 'yellow'
      } else {
        return 'none'
      }
    },
    // 点击时置顶
    bringToFront(dialogRef) {
      const dialog = this.$refs[dialogRef];
      const currentZIndex = parseInt(dialog.style.zIndex || this.zIndexBase);
      const maxZIndex = Math.max(
        ...Array.from(document.querySelectorAll('.model-box'))
          .map(el => parseInt(el.style.zIndex || 0))
      );

      if (currentZIndex <= maxZIndex) {
        dialog.style.zIndex = maxZIndex + 1;
      }
    },
    // 开始拖动
    startDrag(e, dialogRef) {
      if (e.button !== 0) return;

      const dialog = this.$refs[dialogRef];
      this.dragState = {
        isDragging: true,
        currentDialog: dialog,
        startX: e.clientX - dialog.offsetLeft,
        startY: e.clientY - dialog.offsetTop
      };

      document.addEventListener('mousemove', this.handleDrag);
      document.addEventListener('mouseup', this.stopDrag);
      e.preventDefault();
    },

    // 处理拖动
    handleDrag(e) {
      if (!this.dragState.isDragging) return;

      const { currentDialog, startX, startY } = this.dragState;
      let left = e.clientX - startX;
      let top = e.clientY - startY;

      // 限制在可视区域内
      const maxLeft = window.innerWidth - currentDialog.offsetWidth;
      const maxTop = window.innerHeight - currentDialog.offsetHeight;

      currentDialog.style.left = `${Math.max(0, Math.min(left, maxLeft))}px`;
      currentDialog.style.top = `${Math.max(0, Math.min(top, maxTop))}px`;
    },

    // 停止拖动
    stopDrag() {
      this.dragState.isDragging = false;
      document.removeEventListener('mousemove', this.handleDrag);
      document.removeEventListener('mouseup', this.stopDrag);
    },
    // 提交 启动预案
    handleSubmit() {
      this.$refs.elForm.validate(valid => {
        if (!valid) return
        this.formData.structId = this.checkData.id
        this.formData.structureCode = this.checkData.code
        this.formData.flashType = this.checkData.flashType
        this.formData.planName = '一级预警'
        this.formData.lampSwitch = ''
        this.formData.screenWord = ''
        this.formData.flicker = ''
        this.formData.soundContent = ''
        this.formData.isCustom = this.flashData.isCustom || '0'
        this.formData.zjgtConfigEntity = this.flashData.zjgtConfigEntity || {}
        this.formData.sansiConfigEntity = this.flashData.sansiConfigEntity || {}
        this.formData.wdmConfigEntity = this.flashData.wdmConfigEntity || {}
        this.formData.flashCommonModeConfig = this.flashData.flashCommonModeConfig || {}
        console.log(this.formData)
        startPlan(this.formData).then(res => {
          if (res.code == 200) {
            this.$message.success("操作成功")
            this.modelVisible = false
            this.$emit('query')
            this.checkData.planStatus == 1
          }
        })
      })
    },
    changeFlashMode() {
      this.flashData = this.flashModes.find((item) => item.value == this.formData.flashMode)
    },
    mapClickeve(res) {
      this.installLocation = res.installLocation
      this.openWarningRecord()
    },
  },
  computed: {},
  watch: {},
  beforeDestroy() {
    window.$Bus.$off('treeToBim')
  }
}
</script>

<style lang="scss" scoped>
@import "@/assets/styles/utils.scss";
@import "./components//index.scss";

::-webkit-scrollbar {
  width: vwpx(14px);
  height: vwpx(14px);
}

.bim {
  width: 100%;
  height: 100%;

  .body {
    height: calc(100% - #{vwpx(80px)}) !important;
    width: 100%;
    border: 1px solid #dcdfe6;
    border-top: none !important;
    padding: vwpx(10px);

    .video-box {
      height: calc(100% - #{vwpx(60px)});
      width: 100%;
    }
  }

  ::v-deep {
    .el-tabs--border-card {
      box-shadow: none !important;
      border-bottom: none !important;
    }

    .el-tabs__item {
      width: vwpx(270px);
      height: vwpx(80px);
      padding: 0 !important;
      display: flex;
      justify-content: center;
      align-items: center;
      font-size: vwpx(30px);
      color: #303133;
      transition: 0.3s;
      flex-shrink: 0;
    }


    .el-tabs__item.is-active {
      color: #1890ff !important;
    }

    .el-tabs__header {
      background: #EFF5FF;
    }

    .original {
      .el-tabs__item {
        width: vwpx(260px);
        height: vwpx(50px);
        line-height: vwpx(50px);
        padding: 0;
        font-size: vwpx(33px);
        color: #303133;
        transition: 0.3s;
      }

      .el-tabs__item.is-active {
        color: #1D77FF;
      }

      .el-tabs__header {
        background: transparent;
        margin-bottom: vwpx(10px);
      }
    }

    .el-tabs__nav {
      display: flex;
      /* 防止子元素换行 */
      flex-wrap: nowrap;
    }

    .el-tabs__nav-wrap::after {
      display: none;
    }

    .el-tabs__active-bar {
      display: none;
    }

    // 这个会继承 main-tabs 的高度
    .el-tabs__content {
      height: 0 !important;
      padding: 0;
    }

    .el-carousel__container {
      background: transparent;
      height: 100%;
    }

  }

  .btns {
    display: flex;
    flex-wrap: nowrap;
    justify-content: flex-end;
    position: absolute;
    bottom: 4%;
    right: 2%;

    .btn {
      width: 11.25vmin;
      height: vwpx(68px);
      margin: 0px vwpx(20px);
      background: #005f9a;
      color: white;
      font-size: vwpx(32px);
      display: flex;
      justify-content: center;
      align-items: center;
    }
  }
}

.dark {

  ::-webkit-scrollbar-thumb {
    background-color: rgba(1, 102, 254, 0.4);
  }

  ::-webkit-scrollbar-track {
    background-color: rgba(0, 35, 94, .6);
  }

  .body {
    border: 1px solid #0166FE !important;
    border-top: none !important;
  }

  ::v-deep {

    .el-tabs__item {
      color: #fff !important;
    }

    .el-tabs__item.is-active {
      color: #0166FE !important;
      background: rgba(0, 35, 94, 0.6);
      border-right-color: transparent;
      border-left-color: transparent;
      box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.3);
    }

    .el-tabs--border-card {
      background: rgba(1, 102, 254, 0.15) !important;
      border: 1px solid #0166fe;
      border-bottom: none !important;
    }

    .el-tabs__header {
      background: rgba(1, 102, 254, 0.15) !important;
      border: none !important;
    }

    .original {
      .el-tabs__item.is-active {
        color: #1D77FF !important;
      }

      .el-tabs__header {
        background: transparent !important;
        border: none !important;
      }

      .el-tabs__item.is-active {
        color: #0166FE !important;
        background: transparent;
        border-right-color: transparent;
        border-left-color: transparent;
        box-shadow: none;
      }
    }

    .el-tabs__nav-prev,
    .el-tabs__nav-next {
      line-height: 33px;
    }
  }
}

.model-box {
  width: 133.722222vmin;
  height: 84.944444vmin;
  background: #0b1c3a;
  border: 1px solid #1467ad;
  position: absolute;
  left: 15%;
  top: 10%;
  z-index: 9;
  cursor: default;

  /* 默认光标 */
  .title {
    cursor: move;
    /* 标题拖动光标 */
    user-select: none;
    /* 防止文本选中 */
    height: vwpx(70px);
    background: #003d5a;
    font-size: vwpx(40px);
    color: white;
    display: flex;
    align-items: center;
    padding-left: vwpx(40px);
    padding-right: vwpx(40px);
    justify-content: space-between;
    margin: 0px;

    i {
      cursor: pointer;
    }
  }

  .content {
    padding: 2%;
    width: 100%;
    height: 94.5%;
    position: relative;

    .img {
      width: 100%;
      height: 100%;
    }

    .textwrap {
      margin-top: vwpx(130px);
      height: 76%;
      background-color: #003d5a;
      padding: 1%;
      color: white;
      overflow-y: auto;
    }

    .select {
      position: absolute;
      right: 5%;
      top: 5%;
      width: vwpx(300px);
      z-index: 999;

      ::v-deep .select-popper {
        background-color: rgba(1, 102, 254, 0.2);
        border: 1px solid #0166fe;
        color: #ffffff !important;
        font-size: vwpx(32px);
        margin: vwpx(20px) 0;
      }

      ::v-deep .el-pager li {
        background-color: #42abff !important;
        /* 设置背景色 */
        color: white !important;
      }

      ::v-deep .el-pager li.active {
        background-color: #0166fe !important;
        /* 设置活动按钮的背景色 */
        color: #fff !important;
        /* 活动按钮文字颜色 */
      }

      ::v-deep .el-select-dropdown {
        background: rgba(7, 40, 87, 0.8) !important;
        border-color: #0166FE;
      }
    }

    .steering-wheel {
      position: absolute;
      left: 5%;
      bottom: 5%;
      width: vwpx(250px);
      height: vwpx(250px);
      background: url("~@/assets/earlyWarning/steering-wheel.png") no-repeat;
      background-size: 100% 100%;
    }

    .operate {
      position: absolute;
      right: 5%;
      top: 5%;
      width: 10%;
      color: white;

      div {
        display: flex;
        justify-content: space-between;
        margin-bottom: vwpx(20px);
      }

      img {
        cursor: pointer;
      }
    }

    .top-btns {
      display: flex;
      flex-wrap: nowrap;
      width: 54vmin;
      justify-content: flex-start;
      position: absolute;
      top: 2%;
      left: 2%;
      z-index: 9;

      .checked {
        background: #009a9a !important;
      }

      .btn {
        width: 11.25vmin;
        height: vwpx(68px);
        margin: 0px vwpx(20px);
        background: #005f9a;
        color: white;
        font-size: vwpx(32px);
        display: flex;
        justify-content: center;
        align-items: center;
      }
    }

    .btns {
      display: flex;
      flex-wrap: nowrap;
      width: 50vmin;
      justify-content: flex-end;
      position: absolute;
      bottom: 4%;
      right: 2%;

      .btn {
        width: 11.25vmin;
        height: vwpx(68px);
        margin: 0px vwpx(20px);
        background: #005f9a;
        color: white;
        font-size: vwpx(32px);
        display: flex;
        justify-content: center;
        align-items: center;
      }
    }
  }
}

.tips {
  position: relative;

  img {
    width: 100%;
  }

  .mode-dispaly {
    font-size: 11vmin;
    line-height: 100%;
    position: absolute;
    width: 100%;
    height: 21%;
    position: absolute;
    margin-top: -57%;
    text-align: center
  }

  .display-ball {
    width: 11vmin;
    height: 11vmin;
    border-radius: 50%;
    position: absolute;
    margin-top: -57%;
    margin-left: calc(50% - 5.5vmin);
  }

  ;

  /* 定义关键帧动画 */
  @keyframes blink {

    0%,
    100% {
      opacity: 1;
    }

    50% {
      opacity: 0;
    }
  }

  /* 应用动画 */
  .blink {
    animation: blink 0.5s infinite;
    /* 持续时间1秒，无限次播放 */
  }
}
</style>