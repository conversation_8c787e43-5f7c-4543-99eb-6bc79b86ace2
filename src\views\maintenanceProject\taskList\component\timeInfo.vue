<template>
  <div class="app-container maindiv">
    <el-row :gutter="15">
      <el-form ref="elForm" :model="formData" :rules="rules" size="medium" label-width="100px">
        <el-col :span="12">
          <el-form-item label="计划开始" prop="begnDate">
            <el-date-picker type="datetime" v-model="formData.begnDate" format="yyyy-MM-dd HH:mm:ss"
                            value-format="yyyy-MM-dd HH:mm:ss" :style="{width: '100%'}" placeholder="请选择计划开始" clearable>
            </el-date-picker>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="计划完成" prop="field104">
            <el-date-picker type="datetime" v-model="formData.field104" format="yyyy-MM-dd HH:mm:ss"
                            value-format="yyyy-MM-dd HH:mm:ss" :style="{width: '100%'}" placeholder="请选择计划完成" clearable>
            </el-date-picker>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="任务单新增" prop="field105">
            <el-date-picker type="datetime" v-model="formData.field105" format="yyyy-MM-dd HH:mm:ss"
                            value-format="yyyy-MM-dd HH:mm:ss" :style="{width: '100%'}" placeholder="请选择任务单新增" clearable>
            </el-date-picker>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="任务单审核" prop="field106">
            <el-date-picker type="datetime" v-model="formData.field106" format="yyyy-MM-dd HH:mm:ss"
                            value-format="yyyy-MM-dd HH:mm:ss" :style="{width: '100%'}" placeholder="请选择任务单审核" clearable>
            </el-date-picker>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="任务单下发" prop="field107">
            <el-date-picker type="datetime" v-model="formData.field107" format="yyyy-MM-dd HH:mm:ss"
                            value-format="yyyy-MM-dd HH:mm:ss" :style="{width: '100%'}" placeholder="请选择任务单下发" clearable>
            </el-date-picker>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item size="large" style="text-align: right">
            <el-button type="primary" @click="submitForm">保存</el-button>
          </el-form-item>
        </el-col>
      </el-form>
    </el-row>
  </div>
</template>
<script>
export default {
  components: {},
  props: [],
  data() {
    return {
      formData: {
        begnDate: null,
        field104: null,
        field105: null,
        field106: null,
        field107: null,
      },
      rules: {
        begnDate: [{
          required: true,
          message: '请选择计划开始',
          trigger: 'change'
        }],
        field104: [{
          required: true,
          message: '请选择计划完成',
          trigger: 'change'
        }],
        field105: [{
          required: true,
          message: '请选择任务单新增',
          trigger: 'change'
        }],
        field106: [{
          required: true,
          message: '请选择任务单审核',
          trigger: 'change'
        }],
        field107: [{
          required: true,
          message: '请选择任务单下发',
          trigger: 'change'
        }],
      },
    }
  },
  computed: {},
  watch: {},
  created() {},
  mounted() {},
  methods: {
    submitForm() {
      this.$refs['elForm'].validate(valid => {
        if (!valid) return
      })
    },
    resetForm() {
      this.$refs['elForm'].resetFields()
    },
  }
}

</script>
<style>
</style>
<style lang="scss" scoped>
@import "@/assets/styles/business.scss";
</style>
