<template>
  <div class="management-office-statistics" v-loading="loading" element-loading-background="rgba(0, 0, 0, 0.4)">
    <Tables :columns="columns" :data="list" @row-click="onListClick" />
  </div>
</template>

<script>
import Tables from '../../tables.vue';
// api
import { getManageStatistics } from "@/api/cockpit/route";

export default {
  components: {
    Tables,
  },
  data() {
    return {
      loading: false,
      columns: [
        {
          prop: 'index',
          label: '序号',
          width: 50,
        },
        {
          prop: 'deptName',
          label: '管理处',
          width: '100%',
        },
        {
          prop: 'mainRouteMileage',
          label: '主线里程（km）',
          width: 100,
        },
        {
          prop: 'connectRouteMileage',
          label: '连接线里程(km)',
          width: 100,
        },
        {
          prop: 'rampRouteMileage',
          label: '匝道里程(km)',
          width: 100,
        },
        {
          prop: 'highwayNum',
          label: '公路路段(条)',
          width: 100,
        },
        {
          prop: 'maintenanceNum',
          label: '养护路段(条)',
          width: 100,
        }
      ],
      list: []
    }
  },
  async mounted() {
    this.loading = true;
    this.list = await this.getList();
    this.loading = false;
  },
  methods: {
    // 获取 管理处路段统计 数据
    getList() {
      return new Promise((resolve, reject) => {
        getManageStatistics().then((res) => {
          if (res.code === 200 && res.data) {
            resolve(res.data)
          } else {
            reject(res.msg)
          }
        }).catch((err) => {
          reject(err)
        })
      })
    },
    onListClick(row) {
      console.log(row);
    }
  }
}
</script>

<style lang="scss" scoped>
@import "@/assets/styles/utils.scss";

.management-office-statistics {
  width: 100%;
  height: 100%;
  padding: vwpx(10px) vwpx(20px);
}
</style>