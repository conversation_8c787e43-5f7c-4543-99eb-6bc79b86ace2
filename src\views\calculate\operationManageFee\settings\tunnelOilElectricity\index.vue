<template>
  <div class="app-container">
    <el-row :gutter="20">
      <!-- 筛选区开始 -->
      <el-col :span="24" :xs="24">
        <el-row>
          <el-col :span="24">
            <el-form ref="queryForm" :inline="true" :model="queryParams" label-width="68px" size="small">
              <el-form-item label="" prop="maintenanceRoad">
                <RoadSection ref="roadSection" v-model="queryParams.maiSecId" placeholder="路段" :readonly="maiSecId"
                             style="width: 100%"/>
              </el-form-item>
              <el-form-item label="" prop="siteName">
                <el-input v-model="queryParams.siteName" placeholder="请输入站点"/>
              </el-form-item>
              <el-form-item>
                <el-button icon="el-icon-search" size="mini" type="primary" @click="handleQuery">搜索</el-button>
                <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
              </el-form-item>
            </el-form>
          </el-col>
        </el-row>
        <el-row :gutter="10" class="mb8" v-if="pageType == 'edit'">
          <el-col :span="1.5">
            <el-button
              icon="el-icon-plus"
              size="mini"
              type="primary"
              v-has-menu-permi="['operate:oilCost:add']"
              @click="handleAdd"
            >新增
            </el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button
              icon="el-icon-download"
              size="mini"
              type="success"
              v-has-menu-permi="['operate:oilCost:export']"
              @click="exportList"
            >导出
            </el-button>
          </el-col>
        </el-row>
        <!-- 筛选区结束 -->
        <!-- 数据表格开始 -->
        <div class="tableDiv">
          <el-table v-adjust-table v-loading="loading" :data="dataList"
                    :height="pageType == 'edit' ? 'calc(100vh - 260px)' : 'calc(70vh - 260px)'" border size="mini"
                    style="width: 100%" @selection-change="handleSelectionChange">
            <el-table-column
              type="selection"
              width="50">
            </el-table-column>
            <el-table-column align="center" fixed label="序号" type="index" width="100"></el-table-column>
            <template v-for="(column, index) in columns">
              <el-table-column v-if="column.visible" :key="index" :label="column.label" :prop="column.field"
                               align="center" show-overflow-tooltip>
                <template slot-scope="scope">
                  <span>{{ scope.row[column.field] }}</span>
                </template>
              </el-table-column>
            </template>
            <el-table-column align="center" class-name="small-padding fixed-width" fixed="right" label="操作" v-if="pageType == 'edit'" width="300">
            <template slot-scope="scope">
                <el-button icon="el-icon-edit" v-has-menu-permi="['operate:oilCost:edit']" size="mini" type="text" @click="handleEdit(scope.row)">编辑</el-button>
                <el-button icon="el-icon-delete" v-has-menu-permi="['operate:oilCost:remove']" size="mini" type="text" @click="handleDelete(scope.row)">删除
                </el-button>
              </template>
            </el-table-column>
          </el-table>
          <pagination
            v-show="total > 0"
            :limit.sync="queryParams.pageSize"
            :page.sync="queryParams.pageNum"
            :total="total"
            @pagination="handleQuery"
          />
        </div>
        <!-- 数据表格结束 -->
      </el-col>
    </el-row>
    <el-dialog :visible.sync="dialogVisible" append-to-body destroy-on-close :title="dialogTitle" width="90%">
      <el-form ref="elForm" :model="formData" :rules="rules" label-width="100px" size="medium">
        <el-form-item label="费用类型" prop="feeType">
          <el-input readonly value="隧道发电油费"></el-input>
        </el-form-item>
        <el-form-item label="管养单位" prop="domainId">
          <select-tree
            v-model="formData.domainId"
            :deptType="100"
            :deptTypeList="[1, 3, 4]"
            clearable
            onlySelectChild
            placeholder="请选择管养单位"
          ></select-tree>
        </el-form-item>
        <el-form-item label="养护路段" prop="maiSecId">
          <RoadSection ref="roadSection" v-model="formData.maiSecId" :deptId="formData.domainId" placeholder="路段"
                       style="width: 100%"/>
        </el-form-item>
        <el-form-item label="站点" prop="siteName">
          <el-input v-model="formData.siteName" placeholder="请输入站点"/>
        </el-form-item>
        <el-form-item label="使用范围" prop="useScope">
          <el-input v-model="formData.useScope" placeholder="请输入使用范围"/>
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input :rows="3" type="textarea" v-model="formData.remark" />
        </el-form-item>
      </el-form>
      <div style="text-align: right">
        <el-button @click="closeDialog">取消</el-button>
        <el-button type="primary" @click="handleConfirm">确定</el-button>
      </div>
    </el-dialog>
    <div class="mt20" style="text-align: right">
      <el-button v-if="pageType == 'view'" type="primary" @click="handleCheckData">保存</el-button>
    </div>
  </div>
</template>

<script>
import {
  addTunnelOilcost,
  deleteTunnelOilcost,
  editTunnelOilcost,
  listTunnelOilcostPage
} from '@/api/calculate/operationManageFee/tunnelOilElectricity';
import RoadSection from "@/views/baseData/components/roadSection/index.vue";
import SelectTree from "@/components/DeptTmpl/selectTree.vue";

export default {
  name: "YourComponentName",
  components: {SelectTree, RoadSection},
  props: {
    pageType: {
      type: String,
      default: 'edit'
    },
    maiSecId: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      // 遮罩层
      loading: false,
      // 显示搜索条件
      showSearch: false,
      // 总条数
      total: 0,
      dataList: [],
      selectDatas: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
      },
      // 列信息
      columns: [
        {key: 0, field: 'domainName', label: '管养单位', visible: true},
        {key: 1, field: 'maiSecName', label: '养护路段', visible: true},
        {key: 2, field: 'siteName', label: '站点', visible: true},
        {key: 3, field: 'useScope', label: '使用范围', visible: true}
      ],
      // 对话框标题
      dialogTitle: '',
      dialogVisible: false,
      formData: {},
      rules: {
        domainId: [{required: true, message: '请输入管养单位', trigger: 'blur'}],
        maiSecId: [{required: true, message: '请输入养护路段', trigger: 'blur'}],
        siteName: [{required: true, message: '请输入站点', trigger: 'blur'}],
        useScope: [{required: true, message: '请输入使用范围', trigger: 'blur'}]
      }
    };
  },
  created() {
    this.handleQuery();
  },
  methods: {
    handleQuery() {
      this.loading = true;
      if (this.maiSecId) this.queryParams.maiSecId = this.maiSecId
      listTunnelOilcostPage(this.queryParams).then(res => {
        this.dataList = res.rows;
        this.total = res.total;
        this.loading = false;
      }).catch(err => {
        this.loading = false;
        console.error(err);
      });
    },
    resetQuery() {
      this.queryParams = {
        pageNum: 1,
        pageSize: 10,
        maintenanceRoad: '',
        site: ''
      };
      this.handleQuery();
    },
    handleAdd() {
      this.dialogTitle = '新增';
      this.formData = {};
      this.dialogVisible = true;
    },
    handleEdit(row) {
      this.dialogTitle = '编辑';
      this.formData = {...row};
      this.dialogVisible = true;
    },
    handleConfirm() {
      this.$refs.elForm.validate(valid => {
        if (valid) {
          this.loading = true;
          if (this.formData.id) {
            editTunnelOilcost(this.formData).then(res => {
              if (res.code === 200) {
                this.$message.success('编辑成功');
                this.handleQuery();
              } else {
                this.$message.error(res.msg);
              }
              this.loading = false;
              this.closeDialog();
            }).catch(err => {
              this.loading = false;
              console.error(err);
            });
          } else {
            addTunnelOilcost(this.formData).then(res => {
              if (res.code === 200) {
                this.$message.success('新增成功');
                this.handleQuery();
              } else {
                this.$message.error(res.msg);
              }
              this.loading = false;
              this.closeDialog();
            }).catch(err => {
              this.loading = false;
              console.error(err);
            });
          }
        }
      });
    },
    closeDialog() {
      this.dialogVisible = false;
      this.$refs.elForm.resetFields();
    },
    handleDelete(row) {
      this.$confirm('是否确认删除？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.loading = true;
        deleteTunnelOilcost(row.id).then(res => {
          if (res.code === 200) {
            this.$message.success('删除成功');
            this.handleQuery();
          } else {
            this.$message.error(res.msg);
          }
          this.loading = false;
        }).catch(err => {
          this.loading = false;
          console.error(err);
        });
      })
    },
    // 选中
    handleSelectionChange(e) {
      this.selectDatas = e
    },
    handleCheckData() {
      this.$emit('check', this.selectDatas)
    },
    // 导出清单按钮
    exportList() {
      this.download(
        "manager/operate/tunnel/oilcost/export",
        {...this.queryParams},
        `electricity_${new Date().getTime()}.xlsx`,
        {
          headers: {"Content-Type": "application/json;"},
          parameterType: "body",
        }
      );
    },
  }
};
</script>

<style scoped>
.tableDiv {
  margin-top: 20px;
}
</style>
<style lang="scss" scoped>
@import "@/assets/styles/business.scss";
</style>
