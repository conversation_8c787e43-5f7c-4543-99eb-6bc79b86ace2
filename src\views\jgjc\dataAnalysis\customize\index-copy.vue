<!--<template>-->
<!--  <div class='page-container'>-->
<!--    &lt;!&ndash; 左侧管理处及路段树形结构 &ndash;&gt;-->
<!--    <div class='left-panel' :class="{ 'collapsed': leftCollapsed }">-->
<!--      &lt;!&ndash;      <div class="collapse-btn" @click="toggleLeftCollapse">&ndash;&gt;-->
<!--      &lt;!&ndash;        <el-icon><ArrowLeft v-if="!leftCollapsed" /><ArrowRight v-else /></el-icon>&ndash;&gt;-->
<!--      &lt;!&ndash;      </div>&ndash;&gt;-->
<!--      <div class='panel-content'>-->
<!--        <h3>管理处及路段</h3>-->
<!--        <el-tree-->
<!--          ref='tree'-->
<!--          :data='filteredTreeData'-->
<!--          :default-expanded-keys='[1]'-->
<!--          :expand-on-click-node='false'-->
<!--          :props='defaultProps'-->
<!--          highlight-current-->
<!--          node-key='code'-->
<!--          @node-click='handleNodeClick'-->
<!--        ></el-tree>-->
<!--      </div>-->
<!--    </div>-->

<!--    &lt;!&ndash; 中间分析类型列表 -&ndash;&gt;-->
<!--    <div class='middle-pan-el' :class="{ 'collapsed': middleCollapsed }">-->
<!--      &lt;!&ndash;      <d-iv class="collapse-btn" @click="toggleMiddleCollapse">&ndash;&gt;-->
<!--      &lt;!&ndash;        -<el-icon><ArrowLeft v-if="!middleCollapsed" /><ArrowRight v-else /></el-icon>&ndash;&gt;-->
<!--      &lt;!&ndash;      </-div>&ndash;&gt;-->
<!--      <div class='panel-co-ntent'>-->
<!--        <h3>分析类型</h3>-->-
<!--        &lt;!&ndash; 分析类型列-表 &ndash;&gt;-->
<!--        <div-->-
<!--          v-for='tab in ta-bConfig'-->
<!--          :key='tab.name'--->
<!--          class='analysis--type-item'-->
<!--          :class='{ active-: activeTab === tab.name }'-->
<!--          @click='selectAn-alysisType(tab)'-->
<!--        >-->-
<!--          <div class='anal-ysis-type-item-header'>-->
<!--            <span class='a-nalysis-type-item-title'>{{ tab.label }}</span>-->
<!--          </div>-->-
<!--          <div class='analysis-type-item-description'>{{ tab.description }}</div>-->
<!--        </div>-->
<!--      </div>-->
<!--    </div>-->

<!--    &lt;!&ndash; 右侧图表展示区域 &ndash;&gt;-->
<!--    <div class='right-panel'>-->
<!--      <template v-for='tab in tabConfig'>-->
<!--        &lt;!&ndash; 当前选中的分析类型 &ndash;&gt;-->
<!--        <div v-if='tab.name === activeTab' class='analysis-card'>-->
<!--          <div class='analysis-card-header'>-->
<!--            <div class='analysis-card-title'>{{ getCurrentTabLabel() }}</div>-->
<!--            <div class='analysis-card-description'>{{ getTabDescription(activeTab) }}</div>-->
<!--          </div>-->
<!--          <div class='analysis-card-content'>-->
<!--            <correlation ref='correlation' v-show="activeTab === 'feature'" :currentStructure='currentNode'-->
<!--                         :monitorTypeList='monitorTypeList' />-->
<!--          </div>-->
<!--        </div>-->
<!--      </template>-->
<!--    </div>-->
<!--  </div>-->
<!--</template>-->

<!--<script>-->
<!--import { getJCDomainTree } from '@/api/jgjc/baseInfo/alarmLog'-->
<!--import Correlation from './drawComponent/Correlation.vue'-->
<!--import { getSensorTreeNodeByCode } from '@/api/jgjc/dataAnalysis/customize'-->
<!--// import MaxMin from './drawComponent/MaxMin.vue';-->
<!--// import Mse from './drawComponent/Mse.vue';-->
<!--// import Rose from './drawComponent/Rose.vue';-->
<!--// import Spectrum from './drawComponent/Spectrum.vue';-->
<!--// import Trend from './drawComponent/Trend.vue';-->
<!--// import Eigenvalue from "./drawComponent/Eigenvalue.vue";-->
<!--// import VehicleLoad from "./drawComponent/VehicleLoad.vue";-->
<!--export default {-->
<!--  name: 'MonitorAnalysis',-->
<!--  components: { Correlation },-->
<!--  data() {-->
<!--    return {-->
<!--      // 折叠状态-->
<!--      leftCollapsed: false,-->
<!--      middleCollapsed: false,-->

<!--      loading: false,-->
<!--      // 左侧树相关-->
<!--      relaNav: true,-->
<!--      relaOptions: [],-->
<!--      filteredTreeData: [],-->
<!--      defaultProps: {-->
<!--        children: 'children',-->
<!--        label: 'label',-->
<!--      },-->

<!--      // Tab相关-->
<!--      activeTab: 'feature',-->
<!--      tabConfig: [-->
<!--        {-->
<!--          name: 'feature',-->
<!--          label: '特征值分析',-->
<!--          description: '监测桥梁结构的特征值变化，分析结构健康状态',-->
<!--          chartType: 'line',-->
<!--          queryParams: {},-->
<!--          data: {},-->
<!--        },-->
<!--        {-->
<!--          name: 'correlation',-->
<!--          label: '数据序列相关性分析',-->
<!--          description: '分析不同监测点数据之间的相关性，评估结构整体性能',-->
<!--          chartType: 'scatter',-->
<!--          queryParams: {},-->
<!--          data: {},-->
<!--        },-->
<!--        {-->
<!--          name: 'spectrum',-->
<!--          label: '振动信号频谱分析',-->
<!--          description: '分析桥梁振动信号的频谱特征，评估结构动力特性',-->
<!--          chartType: 'bar',-->
<!--          queryParams: {},-->
<!--          data: {},-->
<!--        },-->
<!--        {-->
<!--          name: 'windRose',-->
<!--          label: '风速风向玫瑰图',-->
<!--          description: '展示桥梁所处环境的风速风向分布情况，评估风荷载影响',-->
<!--          chartType: 'pie',-->
<!--          queryParams: {},-->
<!--          data: {},-->
<!--        },-->
<!--        {-->
<!--          name: 'trend',-->
<!--          label: '数据趋势性分析',-->
<!--          description: '分析监测数据的长期趋势变化，预测结构性能演变',-->
<!--          chartType: 'line',-->
<!--          queryParams: {},-->
<!--          data: {},-->
<!--        },-->
<!--        {-->
<!--          name: 'vehicle',-->
<!--          label: '车辆荷载分析',-->
<!--          description: '分析通过桥梁的车辆荷载分布情况，评估结构承载能力',-->
<!--          chartType: 'bar',-->
<!--          queryParams: {},-->
<!--          data: {},-->
<!--        },-->
<!--        {-->
<!--          name: 'crack',-->
<!--          label: '裂缝日变化分析',-->
<!--          description: '监测桥梁裂缝的日变化情况，评估结构耐久性',-->
<!--          chartType: 'line',-->
<!--          queryParams: {},-->
<!--          data: {},-->
<!--        },-->
<!--        {-->
<!--          name: 'exceed',-->
<!--          label: '超限统计分析',-->
<!--          description: '统计监测数据的超限情况，评估结构安全状态',-->
<!--          chartType: 'bar',-->
<!--          queryParams: {},-->
<!--          data: {},-->
<!--        },-->
<!--      ],-->
<!--      currentNode: {},-->
<!--      monitorTypeList: [],-->
<!--    }-->
<!--  },-->
<!--  created() {-->
<!--    this.getDeptTree()-->
<!--  },-->
<!--  mounted() {-->
<!--  },-->
<!--  beforeDestroy() {-->
<!--  },-->
<!--  methods: {-->
<!--    // 切换左侧面板折叠状态-->
<!--    toggleLeftCollapse() {-->
<!--      this.leftCollapsed = !this.leftCollapsed-->
<!--    },-->

<!--    // 切换中间面板折叠状态-->
<!--    toggleMiddleCollapse() {-->
<!--      this.middleCollapsed = !this.middleCollapsed-->
<!--    },-->


<!--    // 获取部门树-->
<!--    getDeptTree() {-->
<!--      getJCDomainTree({}).then(response => {-->
<!--        this.relaOptions = response.data-->
<!--        this.filteredTreeData = [...this.relaOptions]-->
<!--      })-->
<!--    },-->

<!--    // 树节点点击事件-->
<!--    handleNodeClick(nodeData, node) {-->
<!--      if (node.level == 4) {-->
<!--        this.structureId = nodeData.structureId-->
<!--        this.currentNode = nodeData-->
<!--        this.refreshList()-->
<!--      }-->
<!--    },-->
<!--    // 点击叶子结点后更新“监测类型”列表-->
<!--    async refreshList() {-->
<!--      const ref = await getSensorTreeNodeByCode({ code: this.currentNode.structureCode })-->
<!--      const monitorTypeList = this.transformData(ref.data)-->
<!--      this.monitorTypeList.splice(0)-->
<!--      for (const monitorType of monitorTypeList) {-->
<!--        this.monitorTypeList.push(monitorType)-->
<!--      }-->
<!--      // 清空所有子组件的表单以及下拉框选项-->
<!--      // this.$refs.eigenvalue.resetThisForm();-->
<!--      // this.$refs.correlation.resetThisForm();-->
<!--      // this.$refs.spectrum.resetThisForm();-->
<!--      // this.$refs.rose.resetThisForm();-->
<!--      // this.$refs.trend.resetThisForm();-->
<!--      // this.$refs.vehicleLoad.resetThisForm();-->
<!--      // this.$refs.dailyVariation.resetThisForm();-->
<!--      // this.$refs.limitStatistic.resetThisForm();-->
<!--      // await this.$refs.rose.autoSelectWind();-->
<!--    },-->

<!--    transformData(data) {-->
<!--      return data.map(item => {-->
<!--        const newItem = {-->
<!--          content: item.label,-->
<!--          code: item.monitoringTypeId,-->
<!--          monitoringTypeName: item.monitoringTypeName,-->
<!--          monitoringTypeOrders: item.monitoringTypeOrders,-->
<!--        }-->

<!--        if (item.children) {-->
<!--          newItem.children = item.children.map(child => {-->
<!--            const newChild = {-->
<!--              content: child.label,-->
<!--              code: child.monitoringContentId,-->
<!--              monitoringContentName: child.monitoringContentName,-->
<!--              monitoringContentOrders: child.monitoringContentOrders,-->
<!--            }-->

<!--            if (child.children) {-->
<!--              newChild.children = child.children.map(sensor => ({-->
<!--                content: sensor.label,-->
<!--                code: sensor.dataCode,-->
<!--                position: sensor.position,-->
<!--                sensorCode: sensor.sensorCode,-->
<!--                sensorOrders: sensor.sensorOrders,-->
<!--              }))-->
<!--            }-->

<!--            return newChild-->
<!--          })-->
<!--        }-->

<!--        return newItem-->
<!--      })-->
<!--    },-->

<!--    // 获取当前Tab的label-->
<!--    getCurrentTabLabel() {-->
<!--      const tab = this.tabConfig.find(tab => tab.name === this.activeTab)-->
<!--      return tab ? tab.label : ''-->
<!--    },-->

<!--    // 获取当前Tab的描述-->
<!--    getTabDescription(tabName) {-->
<!--      const tab = this.tabConfig.find(tab => tab.name === tabName)-->
<!--      return tab ? tab.description : ''-->
<!--    },-->
<!--    // 选择分析类型-->
<!--    selectAnalysisType(tab) {-->
<!--      this.activeTab = tab.name-->
<!--    },-->
<!--  },-->
<!--}-->
<!--</script>-->

<!--<style scoped>-->
<!--.page-container {-->
<!--  display: flex;-->
<!--  height: 100%;-->
<!--  overflow: hidden;-->
<!--}-->

<!--/* 左侧树形结构样式 */-->
<!--.left-panel {-->
<!--  width: 250px;-->
<!--  background-color: #ffffff;-->
<!--  border-right: 1px solid #e6e6e6;-->
<!--  position: relative;-->
<!--  transition: all 0.3s;-->
<!--}-->

<!--.left-panel.collapsed {-->
<!--  width: 0;-->
<!--  overflow: hidden;-->
<!--}-->

<!--.middle-panel {-->
<!--  width: 300px;-->
<!--  background-color: #ffffff;-->
<!--  border-right: 1px solid #e6e6e6;-->
<!--  position: relative;-->
<!--  transition: all 0.3s;-->
<!--}-->

<!--.middle-panel.collapsed {-->
<!--  width: 0;-->
<!--  overflow: hidden;-->
<!--}-->

<!--.panel-content {-->
<!--  padding: 20px;-->
<!--  overflow-y: auto;-->
<!--  height: 100%;-->
<!--}-->

<!--.collapse-btn {-->
<!--  position: absolute;-->
<!--  top: 50%;-->
<!--  right: -20px;-->
<!--  transform: translateY(-50%);-->
<!--  width: 20px;-->
<!--  height: 40px;-->
<!--  background-color: #ffffff;-->
<!--  border: 1px solid #e6e6e6;-->
<!--  border-left: none;-->
<!--  border-radius: 0 4px 4px 0;-->
<!--  display: flex;-->
<!--  align-items: center;-->
<!--  justify-content: center;-->
<!--  cursor: pointer;-->
<!--  z-index: 1;-->
<!--}-->

<!--.left-panel .collapse-btn {-->
<!--  right: -20px;-->
<!--}-->

<!--.middle-panel .collapse-btn {-->
<!--  right: -20px;-->
<!--}-->

<!--/* 右侧图表展示区域样式 */-->
<!--.right-panel {-->
<!--  flex: 1;-->
<!--  padding: 20px;-->
<!--  overflow-y: auto;-->
<!--  background-color: #ffffff;-->
<!--}-->

<!--/* 分析类型列表项样式 */-->
<!--.analysis-type-item {-->
<!--  padding: 12px;-->
<!--  margin-bottom: 10px;-->
<!--  border-radius: 4px;-->
<!--  border: 1px solid #e6e6e6;-->
<!--  cursor: pointer;-->
<!--  transition: all 0.3s;-->
<!--}-->

<!--.analysis-type-item:hover {-->
<!--  background-color: #f0f7ff;-->
<!--}-->

<!--.analysis-type-item.active {-->
<!--  background-color: #e6f4ff;-->
<!--  border-color: #1890ff;-->
<!--}-->

<!--.analysis-type-item-header {-->
<!--  display: flex;-->
<!--  justify-content: space-between;-->
<!--  align-items: center;-->
<!--  margin-bottom: 8px;-->
<!--}-->

<!--.analysis-type-item-title {-->
<!--  font-size: 14px;-->
<!--  font-weight: bold;-->
<!--  color: #333333;-->
<!--}-->

<!--.analysis-type-item-description {-->
<!--  font-size: 12px;-->
<!--  color: #666666;-->
<!--  margin-top: 4px;-->
<!--  display: -webkit-box;-->
<!--  -webkit-line-clamp: 2;-->
<!--  -webkit-box-orient: vertical;-->
<!--  overflow: hidden;-->
<!--  text-overflow: ellipsis;-->
<!--}-->

<!--/* 分析类型卡片样式 */-->
<!--.analysis-card {-->
<!--  margin-bottom: 20px;-->
<!--  border-radius: 4px;-->
<!--  background-color: #ffffff;-->
<!--  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);-->
<!--  border: 1px solid #e6e6e6;-->
<!--}-->

<!--.analysis-card-header {-->
<!--  padding: 15px;-->
<!--  border-bottom: 1px solid #e6e6e6;-->
<!--}-->

<!--.analysis-card-title {-->
<!--  font-size: 16px;-->
<!--  font-weight: bold;-->
<!--  color: #333333;-->
<!--  margin-bottom: 8px;-->
<!--}-->

<!--.analysis-card-description {-->
<!--  font-size: 14px;-->
<!--  color: #666666;-->
<!--}-->

<!--.analysis-card-content {-->
<!--  padding: 20px;-->
<!--}-->

<!--/* 图表容器样式 */-->
<!--.chart-container {-->
<!--  margin-bottom: 20px;-->
<!--  border: 1px solid #e6e6e6;-->
<!--  border-radius: 4px;-->
<!--  padding: 20px;-->
<!--  background-color: #ffffff;-->
<!--}-->

<!--.chart-header {-->
<!--  display: flex;-->
<!--  justify-content: space-between;-->
<!--  align-items: center;-->
<!--  margin-bottom: 15px;-->
<!--}-->

<!--.chart-title {-->
<!--  font-size: 14px;-->
<!--  color: #333333;-->
<!--}-->

<!--.chart-content {-->
<!--  height: 500px;-->
<!--}-->

<!--/* 工具栏样式 */-->
<!--.toolbar {-->
<!--  margin-bottom: 20px;-->
<!--  padding: 10px;-->
<!--  background-color: #f5f5f5;-->
<!--  border-radius: 4px;-->
<!--  display: flex;-->
<!--  gap: 10px;-->
<!--  border: 1px solid #e6e6e6;-->
<!--}-->

<!--/* 当前选择信息样式 */-->
<!--.current-info {-->
<!--  margin-bottom: 15px;-->
<!--  padding: 8px 12px;-->
<!--  background-color: #f5f5f5;-->
<!--  border-radius: 4px;-->
<!--  border: 1px solid #e6e6e6;-->
<!--  text-align: center;-->
<!--}-->

<!--.info-value {-->
<!--  color: #333333;-->
<!--  font-size: 14px;-->
<!--  font-weight: 500;-->
<!--}-->

<!--/* 查询表单样式 */-->
<!--.query-form {-->
<!--  margin-bottom: 20px;-->
<!--  padding: 20px;-->
<!--  background: #f5f5f5;-->
<!--  border-radius: 4px;-->
<!--}-->

<!--::v-deep .el-divider&#45;&#45;horizontal {-->
<!--  margin: 13px 0;-->
<!--}-->
<!--</style>-->
