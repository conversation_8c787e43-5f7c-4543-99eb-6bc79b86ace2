<template>
  <div class="app-container maindiv">
    <el-row>
      <el-col :span="24" :xs="24">
        <el-row>
          <el-form
            ref="queryForm"
            :model="queryParams"
            size="mini"
            :inline="true"
            label-width="100px"
          >
            <el-form-item label="管养单位">
              <selectTree
                :key="'domainId'"
                style="width: 240px"
                v-model="queryParams.domainId"
                :dept-type="101"
                :expand-all="false"
                :dataRule="false"
                placeholder="请选择管养单位"
                clearable
              />
            </el-form-item>
            <el-form-item label="站点名称">
              <el-input v-model="queryParams.siteName" placeholder="请输入站点名称" />
            </el-form-item>
            <el-form-item label="物资名称">
              <el-input v-model="queryParams.wname" placeholder="请输入物资名称" />
            </el-form-item>
<!--            <el-form-item label="起始处理时间">-->
<!--              <el-date-picker-->
<!--                v-model="queryParams.startHandleTime"-->
<!--                type="date"-->
<!--                placeholder="选择日期"-->
<!--                value-format="yyyy-MM-dd"-->
<!--              />-->
<!--            </el-form-item>-->
<!--            <el-form-item label="截止处理时间">-->
<!--              <el-date-picker-->
<!--                v-model="queryParams.endHandleTime"-->
<!--                type="date"-->
<!--                placeholder="选择日期"-->
<!--                value-format="yyyy-MM-dd"-->
<!--              />-->
<!--            </el-form-item>-->
            <el-form-item>
              <el-button
                type="primary"
                icon="el-icon-search"
                size="mini"
                @click="handleQuery"
              >搜索</el-button>
              <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
            </el-form-item>
          </el-form>
        </el-row>
      </el-col>
    </el-row>

    <!--操作按钮区开始-->
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="warning"
          icon="el-icon-download"
          size="mini"
          @click="exportList"
          v-has-menu-permi="['waste:detail:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar
        :showSearch.sync="showSearch"
        @queryTable="handleQuery"
        :columns="columns"
      ></right-toolbar>
    </el-row>

    <el-row>
      <div class="draggable">
        <el-table
          v-adjust-table
          size="mini"
          style="width: 100%"
          v-loading="loading"
          border
          :data="tableData"
          row-key="id"
          ref="dataTable"
          stripe
          highlight-current-row
          @row-click="handleClickRow"
          :height="showSearch ? 'calc(100vh - 330px)' : 'calc(100vh - 270px)'"
        >
          <el-table-column
            label="序号"
            align="center"
            type="index"
            width="50"
          />
          <template v-for="(column, index) in columns">
            <el-table-column
              :label="column.label"
              v-if="column.visible"
              align="center"
              :prop="column.field"
              :width="column.width"
            >
              <template slot-scope="scope">
                <template v-if="column.field === 'domainId'">
                  {{ scope.row.domainName || scope.row.domainId }}
                </template>
                <template v-else-if="column.field === 'stock' || column.field === 'storeQuantity' || column.field === 'outQuantity' || column.field === 'handleAmount'">
                  {{ scope.row[column.field] | numberFormat }}
                </template>
                <span v-else>{{ scope.row[column.field] }}</span>
              </template>
            </el-table-column>
          </template>
        </el-table>
        <pagination
          v-show="total > 0"
          :total="total"
          :page.sync="queryParams.pageNum"
          :limit.sync="queryParams.pageSize"
          @pagination="handleQuery"
        />
      </div>
    </el-row>
  </div>
</template>

<script>
import selectTree from "@/components/DeptTmpl/selectTree.vue";
import { listWasteDetail, exportWasteDetail } from '@/api/materialWaste/inventoryList'

export default {
  name: 'WasteStockManagement',
  components: {
    selectTree
  },
  filters: {
    numberFormat(value) {
      if (value === null || value === undefined) return '0.00';
      return parseFloat(value).toFixed(2);
    }
  },
  data() {
    return {
      showSearch: false,
      queryParams: {
        pageNum: 1,
        pageSize: 50,
        domainId: null,
        siteName: '',
        wtype: '',
        wname: '',
        startTime: ''
      },
      total: 0,
      loading: false,
      columns: [
        {
          key: 0,
          field: "siteName",
          label: "站点名称",
          visible: true
        },
        {
          key: 1,
          field: "domainId",
          label: "管养单位",
          visible: true
        },
        {
          key: 2,
          field: "wtype",
          label: "物资类型",
          visible: true
        },
        {
          key: 3,
          field: "wname",
          label: "物资名称",
          visible: true
        },
        {
          key: 4,
          field: "ownerUnit",
          label: "产权单位",
          visible: true
        },
        {
          key: 5,
          field: "specs",
          label: "规格",
          visible: true
        },
        {
          key: 6,
          field: "unit",
          label: "单位",
          visible: true
        },
        {
          key: 7,
          field: "stock",
          label: "库存数量",
          visible: true
        },
        {
          key: 8,
          field: "storeQuantity",
          label: "累计入库数量",
          visible: true
        },
        {
          key: 9,
          field: "outQuantity",
          label: "累计出库数量",
          visible: true
        },
        {
          key: 10,
          field: "handleAmount",
          label: "累计处置金额",
          visible: true
        }
      ],
      tableData: [],
      rowData: {}
    };
  },
  created() {
    this.handleQuery();
  },
  methods: {
    handleQuery() {
      this.loading = true;
      listWasteDetail(this.queryParams).then(res => {
        this.loading = false;
        this.tableData = res.rows;
        this.total = res.total;
      }).catch(() => {
        this.loading = false;
      });
    },
    resetQuery() {
      this.queryParams = {
        pageNum: 1,
        pageSize: 50,
        domainId: null,
        siteName: '',
        wtype: '',
        wname: '',
        startTime: ''
      };
      this.handleQuery();
    },
    handleClickRow(row) {
      this.rowData = row;
    },
    exportList() {
      this.download('manager/waste/detail/export', {
        ...this.queryParams
      }, `库存列表数据_${new Date().getTime()}.xlsx`);
    }
  }
};
</script>

<style lang="scss" scoped>
@import "@/assets/styles/business.scss";
</style>
