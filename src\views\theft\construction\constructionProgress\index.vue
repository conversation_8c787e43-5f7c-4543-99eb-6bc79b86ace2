<template>
  <div>
    <reg-list ref="reg" :status="-1">
      <template #operate="scope">
        <el-button
          size="mini"
          type="text"
          icon="el-icon-s-promotion"
          v-has-menu-permi="['theft:construction:isEnd']"
          @click="handleUpdate(scope.scope.row)"
        >更新完结状态
        </el-button>
      </template>
    </reg-list>
  </div>
</template>
<script>

import RegList from "../component/regList.vue";
import {updateIsEndConstruction} from "@/api/theft/taskList";

export default {
  name: 'ConstructionProgress',
  components: {RegList},
  methods: {
    handleUpdate(row) {
      if (row.isEnd == 0) {
        this.$confirm('该任务单未完结，是否更新为已完结？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          updateIsEndConstruction({id: row.id, isEnd: 1}).then(res => {
            this.$message.success('操作成功')
            this.$refs.reg.handleQuery()
          })
        })
      } else {
        this.$confirm('该任务单已完结，是否更新为未完结？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          updateIsEndConstruction({id: row.id, isEnd: 0}).then(res => {
            this.$message.success('操作成功')
            this.$refs.reg.handleQuery()
          })
        })
      }
    }
  }
}
</script>

<style scoped lang="scss">

</style>
<style lang="scss" scoped>
@import "@/assets/styles/business.scss";
</style>
