<template>
  <div class="road-maintenance-expenditure" v-loading="loading" element-loading-background="rgba(0, 0, 0, 0.4)">
    <Echarts :option="option" v-if="option" height="100%" key="levelKey" />
  </div>
</template>

<script>
import * as echarts from "echarts";
import Echarts from '../../echarts/echarts.vue';
// api
import { getRoadMaintenanceExpenditure } from '@/api/cockpit/route';

export default {
  name: 'RoadMaintenanceExpenditure',
  components: {
    Echarts
  },
  data() {
    return {
      option: null,
      loading: false,
    }
  },
  created() { },
  async mounted() {
    this.loading = true;
    const data = await this.getData();
    this.loading = false;
    if (!data) return;
    let ysData = data.filter(x => x.name === '预算')
    let yjData = data.filter(x => x.name === '实际完成')
    let xAxisData = [];
    let data1 = [];
    let data2 = [];
    data1 = ysData.map(x => x.sumFund)
    data2 = yjData.map(x => x.sumFund)
    if (data1.length > data2.length) {
      xAxisData = ysData.map(v => v.year)
    } else {
      xAxisData = yjData.map(v => v.year)
    }
    // 将数据除以10000并保留2位小数
    data1 = data1.map(value => parseFloat((value / 10000).toFixed(2)));
    data2 = data2.map(value => parseFloat((value / 10000).toFixed(2)));
    this.option = this.initCharts(xAxisData, data1, data2);
  },
  methods: {
    getData() {
      return new Promise((resolve, reject) => {
        getRoadMaintenanceExpenditure().then(res => {
          if (res.code === 200 && res.rows) {
            resolve(res.rows);
          } else {
            reject(res);
          }
        }).catch(err => {
          reject(err);
        });
      })
    },
    initCharts(xAxisData = [], data1 = [], data2 = []) {
      let option = {
        backgroundColor: 'rgba(255,255,255,0)',
        grid: {
          left: '1%',
          right: '4%',
          top: '12%',
          bottom: '2%',
          containLabel: true
        },
        tooltip: {
          show: true,
          trigger: 'item'
        },
        legend: {
          show: true,
          x: 'center',
          y: '5',
          icon: 'circle',
          itemWidth: 30,
          itemHeight: 30,
          textStyle: {
            color: '#fff',
            fontSize: 24,
          },
          data: ['预算', '实际完成']
        },
        xAxis: [
          {
            type: 'category',
            boundaryGap: false,
            axisLabel: {
              color: '#fff',
              fontSize: 24
            },
            axisLine: {
              show: true,
              lineStyle: {
                color: 'transparent'
              }
            },
            axisTick: {
              show: false,
            },
            splitLine: {
              show: false,
            },
            data: xAxisData || ['2016', '2017', '2018', '2019', '2020', '2021', '2022', '2023', '2024', '2025']
          }
        ],
        yAxis: [
          {
            type: 'value',
            name: '万元',
            nameTextStyle: {
              color: '#999999',
              fontSize: 24,
              padding: [0, 40, 20, 0]
            },
            min: 0,
            axisLabel: {
              formatter: '{value}',
              textStyle: {
                color: '#999999',
                fontSize: 24
              }
            },
            axisLine: {
              lineStyle: {
                color: '#27b4c2'
              }
            },
            axisTick: {
              show: false,
            },
            splitLine: {
              show: true,
              lineStyle: {
                color: 'rgba(110,112,121,0.5)'
              }
            },
            splitArea: false
          },
        ],
        series: [
          {
            name: '预算',
            type: 'line',
            stack: '总量',
            symbol: 'circle',
            symbolSize: 8,
            smooth: false, // 平滑曲线
            itemStyle: {
              normal: {
                color: '#1CFFBC',
                lineStyle: {
                  color: "#1CFFBC",
                  width: 1
                },
                areaStyle: {
                  color: new echarts.graphic.LinearGradient(0, 1, 0, 0, [{
                    offset: 0,
                    color: 'rgba(28,255,188,0.1)'
                  }, {
                    offset: 1,
                    color: 'rgba(28,255,188,0.6)'
                  }]),
                }
              }
            },
            markPoint: {
              itemStyle: {
                normal: {
                  color: 'red'
                }
              }
            },
            data: data1 || [7800, 9000, 7000, 10000, 12000, 12800, 8200, 9600, 9900, 10000]
          },
          {
            name: '实际完成',
            type: 'line',
            stack: '总量',
            symbol: 'circle',
            smooth: false,
            symbolSize: 8,
            itemStyle: {
              normal: {
                color: '#0154FB',
                lineStyle: {
                  color: "#0154FB",
                  width: 1
                },
                areaStyle: {
                  color: new echarts.graphic.LinearGradient(0, 1, 0, 0, [{
                    offset: 0,
                    color: 'rgba(1,84,251,0.1)'
                  }, {
                    offset: 1,
                    color: 'rgba(1,84,251,0.6)'
                  }]),
                }
              }
            },
            data: data2 || [5000, 7100, 5100, 8400, 7800, 7900, 5000, 5800, 6000, 6200]
          },
        ]
      };
      return option;
    }
  }
}
</script>

<style lang="scss" scoped>
.road-maintenance-expenditure {
  width: 100%;
  height: 100%;
}
</style>