  
<template>
    <div v-loading="loading" class="app-container">
      <div class="tableDiv" :style="{height:'100%',paddingTop:'10px',marginTop:'0px',maginBottom:'-10px'}">
        <iframe :src="src" id="reportView" frameborder="no" style="width: 100%;height: 100%;" scrolling="auto" />
      </div>
    </div>
  </template>
  
  <script>
  // -------------------- 引入 --------------------
  // 导入相关
import { createDamage } from "@/api/disaster/damage/damage";
import {getToken} from "@/utils/auth";
  
  export default {
    name: "JimuAdapter4HighSlope",
    // 组件
    components: {
    },
    computed: {
    },
    created(){
        this.reportId = this.$route.query.reportId
        var token=this.getTokenForReport();
        var params=this.getQryStringParams(this.$route.query);
        this.src=this.reportViewerUrl+this.reportId+"?token="+token+"&tenantId=1"+params;
    },
    data() {
      return {
        src: "",
        loading: true,
        reportViewerUrl:process.env.VUE_APP_REPORT_VIEWER_URL,
        reportId: "",
      }
    },
    mounted() {
      this.initPage()
      setTimeout(() => {
        this.loading = false;
      }, 230);
    },
    methods: {
      initPage() {
      },
      getTokenForReport(){
        // return getToken();
        return "eyJhbGciOiJIUzUxMiJ9.eyJ1c2VyX2lkIjoxLCJ1c2VyX2tleSI6IjUxYmMwNjdlLWE2YmMtNDNmYS1hYmJkLWI2YjUxODBmNWU4ZiIsInVzZXJuYW1lIjoiYWRtaW4ifQ.R2KL_yMa0gLxmobbna3AW1i1Ynro6Yvbqc4OmzC6fUrQnv9nilNGqy-9O4vhPp8_cwvuWzC6FTKw1qJhKwDC0Q";
      },
      getQryStringParams(qryObj){
        var params='';
        for(var p in qryObj){
          var value=qryObj[p];
          if(value && value.length>0){
            value=value.replace("[","【");
            value=value.replace("]","】");
            params+="&"+p+"="+value;
          }
        }
        return params;
      },
      urlEncode(param, key, encode) {
        if (param == null) return '';
        var paramStr = '';
        var t = typeof (param);
        if (t == 'string' || t == 'number' || t == 'boolean') {
          paramStr += '&' + key + '=' + ((encode == null || encode) ? encodeURIComponent(param) : param);
        } else {
          for (var i in param) {
            var k = key == null ? i : key + (param instanceof Array ? '[' + i + ']' : '.' + i);
            paramStr += this.urlEncode(param[i], k, encode);
          }
        }
        return paramStr;
      },
  
    },
  }
  
  </script>
  
  <style lang="scss" scoped>
  .app-container form:first-child .el-select,
  .app-container form:nth-child(2) .el-select,
  .app-container form:nth-child(2) ::v-deep .el-form-item__content,
  .app-container form:first-child ::v-deep .el-form-item__content {
    width: 240px;
  }
  
  .app-container form:first-child .el-form-item:last-child ::v-deep .el-form-item__content {
    width: auto;
  }
  
  .app-container {
    padding: 10px;
    background-color: #c0c0c0;
    box-sizing: border-box;
  }
  
  .formDialog {
    ::v-deep .el-dialog__body {
      height: 600px;
      overflow-y: auto;
    }
  
    .dialog-footer {
      width: 100%;
  
      .footerTip {
        color: #888888;
        font-size: 14px;
      }
    }
  
    .titleBox {
      height: 22px;
      position: relative;
      display: flex;
      flex-direction: row;
      align-items: center;
  
      .title {
        font-size: 16px;
        color: black;
        margin: 0;
      }
  
      .subTitle {
        margin-left: 15px;
        font-size: 12px;
        color: #888888;
      }
  
      .riskLevel {
        user-select: none;
        position: absolute;
        // top: 0;
        right: 5%;
        display: flex;
        align-items: center;
        flex-direction: row;
  
        .title {
          font-size: 16px;
          font-weight: bold;
        }
  
        .main {
          font-size: 16px;
          font-weight: bold;
          padding: 5px 10px 5px 10px;
          color: white;
          box-sizing: border-box;
          border-radius: 5px;
        }
  
        .score {
          color: #888888;
          font-size: 14px;
        }
  
      }
    }
  }
  
  .searchBox {
    padding: 10px;
    background: #fff;
    border-radius: 10px;
    transition: all .1s linear;
    display: flex;
    flex-direction: column;
  
    .searchMoreBox {
      min-width: 192px;
      margin-top: 10px;
      display: flex;
      align-items: center;
      flex-direction: row;
    }
  }
  
  .tableDiv {
    margin-top: 10px;
    background-color: white;
    padding-bottom: 10px;
    border-radius: 10px;
    transition: all .1s linear;
    display: flex;
    flex-direction: column;
  
    .btnBox {
      padding: 10px;
    }
  }
  
  .infoBox {
    padding: 15px 15px 0 15px;
    box-sizing: border-box;
    border-radius: 6px;
    border: 1px solid #C4C4C4;
    position: relative;
  
    .infoTitle {
      user-select: none;
      position: absolute;
      top: 0;
      left: 0;
      padding: 0 10px;
      font-size: 14px;
      line-height: 14px;
      font-weight: bold;
      transform: translateX(15px) translateY(-50%);
      background-color: white;
    }
  
    .imgBox {
      height: auto;
      width: 100%;
      display: flex;
      flex-wrap: wrap;
      align-content: flex-start;
  
      .imgItemBox {
        height: 240px;
        width: calc(100% / 3);
        box-sizing: border-box;
        padding: 10px;
        overflow-y: auto;
        display: flex;
        align-items: center;
        justify-content: center;
        position: relative;
  
        .imgDeleteBtn {
          position: absolute;
          z-index: 1;
          top: 10%;
          right: 10%;
        }
      }
    }
  }
  
  .coordinateDialog {
  
    .coordinateMap {
      height: 600px;
      width: 100%;
      position: relative;
  
      #coordinateBox {
        height: 100%;
        width: 100%;
        border-radius: 5px;
        position: relative;
        z-index: 0;
      }
  
      .coordinateSearch {
        position: absolute;
        z-index: 1;
        top: 10px;
        left: 10px;
        width: 50%;
        padding: 10px;
        box-sizing: border-box;
        background-color: #fff;
        border-radius: 5px;
        border: 1px solid #DCDFE6;
        box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.12), 0 0 6px 0 rgba(0, 0, 0, 0.04);
        display: flex;
        flex-direction: row;
      }
  
      .coordinateTip {
        position: absolute;
        z-index: 1;
        top: 10px;
        right: 10px;
        padding: 10px;
        box-sizing: border-box;
        background-color: #fff;
        border-radius: 5px;
        border: 1px solid #DCDFE6;
        box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.12), 0 0 6px 0 rgba(0, 0, 0, 0.04);
      }
    }
  }
  
  // v-if过渡动画
  // 查询框
  .search-enter-active {
    transition: all .1s linear;
  }
  
  .search-enter {
    opacity: 0;
  }
  
  .search-leave-active {
    transition: all .1s linear;
  }
  
  .search-leave-to {
    opacity: 0;
  }
  </style>
  