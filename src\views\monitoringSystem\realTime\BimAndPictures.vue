<template>
  <div class="bim" :class="theme">
    <el-tabs tab-position="top" v-model="activeName" type="border-card">
      <el-tab-pane v-if="hasBim" label="BIM模型" name="1"></el-tab-pane>
      <el-tab-pane v-if="panoramic720view" label="全景图" name="2"></el-tab-pane>
      <el-tab-pane v-if="videoInfo.length > 0" label="视频" name="3"></el-tab-pane>
      <el-tab-pane label="图片" name="4"></el-tab-pane>
    </el-tabs>

    <div class="body" v-if="hasBim" v-show="activeName === '1'">
      <Bim :bimToRealTime="bimToRealTime" />
    </div>
    <div class="body" v-show="activeName === '2'">
      <iframe :src="panoramic720view" width="100%" height="100%" frameborder="0" allowfullscreen>
      </iframe>
    </div>
    <div class="body video" v-show="activeName === '3' && videoInfo.length > 0">
      <el-tabs class="original" tab-position="top" v-model="videoName" @tab-click="tabClick">
        <el-tab-pane v-for="(item, index) in videoInfo" :key="index" :label="item.label"
          :name="item.label"></el-tab-pane>
      </el-tabs>
      <div class="video-box" v-if="videoUrl">
        <newVideo width="100%" height="100%" :url="videoUrl"></newVideo>
      </div>
    </div>

    <div class="body" style="overflow: auto; display: flex; align-items: center;" v-show="activeName === '4'">
      <el-image :src="structureImages[0]" :preview-src-list="srcList" style="width: 100%;"></el-image>
      <!-- <el-carousel trigger="click" style="width: 100%; height: 100%;">
        <el-carousel-item v-for="(item, index) in structureImages" :key="index"
          style="display: grid; place-items: center;">
          <el-image :src="item" :preview-src-list="srcList" style="height: 100%;"></el-image>
        </el-carousel-item>
      </el-carousel> -->
    </div>
  </div>
</template>

<script>
import Bim from './components/Bim.vue'
import newVideo from './components/Video/newVideo.vue'
import { fetchGet } from './api.js'

export default {
  name: 'BimAndPictures',
  props: {
    theme: {
      type: String,
      default: 'dark'
    }
  },
  components: { Bim, newVideo },
  data() {
    return {
      activeName: "1",
      hasBim: false,
      structureImages: ["https://jkjc.yciccloud.com:8002/ynjt-structure-file/7c28243f509445fb86fafaa6a0d0fccd-治租河.jpeg"],
      srcList: ["https://jkjc.yciccloud.com:8002/ynjt-structure-file/7c28243f509445fb86fafaa6a0d0fccd-治租河.jpeg",],
      videoInfo: [],
      videoName: "",
      videoUrl: "",
      panoramic720view: ""
    }
  },
  mounted() {
    // 监听Tree组件那里传过来的obj，属性分别有：fileId、videoInfo、structureImage
    window.$Bus.$on('treeToBim', (obj) => {
      this.hasBim = obj.fileId ? true : false
      if (obj.code === "1593262524063682560" || obj.code === "1593262527993745408") {
        obj.panoramic720view = "https://www.720yun.com/vr/4dbj5Oyyuf7"
      }
      this.activeName = obj.fileId ? '1' : obj.panoramic720view ? '2' : obj.videoInfo ? '3' : '4'
      if (obj.structureImage) {
        this.structureImages = [obj.structureImage]
        this.srcList = [obj.structureImage]
      }
      if (obj.videoInfo) {
        this.videoInfo = obj.videoInfo
        this.videoName = this.videoInfo[0].label
        this.tabClick()
      }
      if (obj.panoramic720view) {
        this.panoramic720view = obj.panoramic720view
      }
    })
  },
  methods: {
    // 点击BIM传感器节点后 绘制图表
    async bimToRealTime(sensorInfoList) {
      if (sensorInfoList === undefined || sensorInfoList.length === 0) {
        this.$message({ type: 'warning', message: "该构件没有对应传感器信息", duration: 2000 });
        return;
      }
      window.$Bus.$emit('bimToCharts', sensorInfoList)
    },
    tabClick() {
      this.videoUrl = ""
      const url = "https://jkjc.yciccloud.com:8000/xboot/structurevideo/getByStructureCode"
      const data = this.videoInfo.find(item => item.label === this.videoName)
      fetchGet(url, data).then(res => {
        if (res.code === 200) {
          let urlList = res.result.records;
          urlList.forEach((item) => {
            if (item.id == data.id) {
              this.videoUrl = String('https://jkjc.glyhgl.com:22585' + item.videoUrl);
              // this.videoUrl = String(item.videoIp + item.videoUrl);
            }
          });
        }
      })
    }
  },
  computed: {},
  watch: {},
  beforeDestroy() {
    window.$Bus.$off('treeToBim')
  }
}
</script>

<style lang="scss" scoped>
@import "@/assets/styles/utils.scss";

::-webkit-scrollbar {
  width: vwpx(14px);
  height: vwpx(14px);
}

.bim {
  width: 100%;
  height: 100%;

  .body {
    height: calc(100% - #{vwpx(80px)}) !important;
    width: 100%;
    border: 1px solid #dcdfe6;
    border-top: none !important;
    padding: vwpx(10px);

    .video-box {
      height: calc(100% - #{vwpx(60px)});
      width: 100%;
    }
  }

  ::v-deep {
    .el-tabs--border-card {
      box-shadow: none !important;
      border-bottom: none !important;
    }

    .el-tabs__item {
      width: vwpx(270px);
      height: vwpx(80px);
      padding: 0 !important;
      display: flex;
      justify-content: center;
      align-items: center;
      font-size: vwpx(30px);
      color: #303133;
      transition: 0.3s;
      flex-shrink: 0;
    }


    .el-tabs__item.is-active {
      color: #1890ff !important;
    }

    .el-tabs__header {
      background: #EFF5FF;
    }

    .original {
      .el-tabs__item {
        width: vwpx(260px);
        height: vwpx(50px);
        line-height: vwpx(50px);
        padding: 0;
        font-size: vwpx(33px);
        color: #303133;
        transition: 0.3s;
      }

      .el-tabs__item.is-active {
        color: #1D77FF;
      }

      .el-tabs__header {
        background: transparent;
        margin-bottom: vwpx(10px);
      }
    }

    .el-tabs__nav {
      display: flex;
      /* 防止子元素换行 */
      flex-wrap: nowrap;
    }

    .el-tabs__nav-wrap::after {
      display: none;
    }

    .el-tabs__active-bar {
      display: none;
    }

    // 这个会继承 main-tabs 的高度
    .el-tabs__content {
      height: 0 !important;
      padding: 0;
    }

    .el-carousel__container {
      background: transparent;
      height: 100%;
    }

  }
}

.dark {

  ::-webkit-scrollbar-thumb {
    background-color: rgba(1, 102, 254, 0.4);
  }

  ::-webkit-scrollbar-track {
    background-color: rgba(0, 35, 94, .6);
  }

  .body {
    border: 1px solid #0166FE !important;
    border-top: none !important;
  }

  ::v-deep {

    .el-tabs__item {
      color: #fff !important;
    }

    .el-tabs__item.is-active {
      color: #0166FE !important;
      background: rgba(0, 35, 94, 0.6);
      border-right-color: transparent;
      border-left-color: transparent;
      box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.3);
    }

    .el-tabs--border-card {
      background: rgba(1, 102, 254, 0.15) !important;
      border: 1px solid #0166fe;
      border-bottom: none !important;
    }

    .el-tabs__header {
      background: rgba(1, 102, 254, 0.15) !important;
      border: none !important;
    }

    .original {
      .el-tabs__item.is-active {
        color: #1D77FF !important;
      }

      .el-tabs__header {
        background: transparent !important;
        border: none !important;
      }

      .el-tabs__item.is-active {
        color: #0166FE !important;
        background: transparent;
        border-right-color: transparent;
        border-left-color: transparent;
        box-shadow: none;
      }
    }
  }
}
</style>