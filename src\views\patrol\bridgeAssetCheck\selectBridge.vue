<template>
  <el-dialog title="选择桥梁" :visible.sync="visible" width="80%" append-to-body v-dialog-drag>
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true">
      <el-form-item label="" prop="deptId" >
        <el-select v-model="queryParams.deptId" filterable placeholder="请选择管养单位"
                   style="width: 240px" @change="deptChange">
          <el-option
            v-for="item in deptOptions"
            :key="item.id"
            :label="item.label"
            :value="item.id">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="" prop="maintenanceSectionId">
        <el-select v-model="queryParams.maintenanceSectionId" filterable placeholder="请选养护路段" style="width: 240px">
          <el-option
            v-for="item in maintenanceSectionList"
            :key="item.maintenanceSectionId"
            :label="item.maintenanceSectionName"
            :value="item.maintenanceSectionId">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="" prop="assetName">
        <el-input
          v-model="queryParams.assetName"
          placeholder="请输入桥梁名称"
          clearable
          style="width: 240px"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="" prop="assetCode">
        <el-input
          v-model="queryParams.assetCode"
          placeholder="请输入桥梁编码"
          clearable
          style="width: 240px"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="" prop="isInspect">
        <el-select v-model="queryParams.isInspect" placeholder="是否已检查" clearable style="width: 240px">
          <el-option label="是" value="1"></el-option>
          <el-option label="否" value="0"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item style="width: 236px;">
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>
    <el-row>
      <el-table size="mini" style="width: 100%" v-loading="loading" border
        @cell-dblclick="cellClickBridge"
        highlight-current-row
        @current-change="handleCurrentChange"
        ref="table"
        :data="bridgeList"
        height="396px"
      >
        <el-table-column type="index" width="55">
          <template v-slot="scope">
            {{ (scope.$index + (queryParams.pageNum - 1) * queryParams.pageSize)+1 }}
          </template>
        </el-table-column>
        <el-table-column label="路线编码" align="center" prop="routeCode"/>
        <el-table-column label="中心桩号" align="center" prop="centerStake"/>
        <el-table-column label="桥梁编码" align="center" prop="assetCode"/>
        <el-table-column label="桥梁名称" align="center" prop="assetName"/>
        <el-table-column label="是否已检查" align="center" prop="isInspect"/>
      </el-table>
      <pagination
        v-show="total>0"
        :total="total"
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
        @pagination="getList"
      />
    </el-row>
    <div slot="footer" class="dialog-footer">
      <el-button type="primary" @click="handleSelectBridge">确 定</el-button>
      <el-button @click="visible = false">取 消</el-button>
    </div>
  </el-dialog>
</template>

<script>
import {listStatic} from "@/api/patrol/assetInfo";
import {getTreeStruct} from "@/api/tmpl";
import {listMaintenanceSectionAll} from "@/api/system/maintenanceSection";
export default {
  data() {
    return {
      // 遮罩层
      visible: false,
      loading: false,
      // 选中数组值
      maintenanceSectionIds: [],
      // 总条数
      total: 0,
      // 未关联的养护路段数据
      bridgeList: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        deptId: null,
        maintenanceSectionId: null,
        assetName: null,
        assetCode: null,
        isInspect: null,
        patrolTime: null,//检查时间
      },
      //提交参数
      params: {},
      maintenanceSectionList: [],
      // 部门树选项
      deptOptions: [],
      currentRow: null
    };
  },
  methods: {
    // 显示弹框
    show() {
      this.getList();
      this.getDeptTree();
      this.getMaintenanceSection();
      this.visible = true;
    },
    cellClickBridge(row) {
      this.currentRow = row;
      this.handleSelectBridge()
    },
    handleCurrentChange(row) {
      this.currentRow = row;
    },
    // 查询表数据
    getList() {
      this.loading = true;
      listStatic(this.queryParams).then(res => {
        this.bridgeList = res.rows;
        this.total = res.total;
        this.loading = false;
      });
    },
    /** 查询部门下拉树结构 */
    getDeptTree() {
      return getTreeStruct({types:201}).then(response => {
        this.deptOptions = response.data;
      });
    },
    //管理处下拉选点击事件
    deptChange() {
      listMaintenanceSectionAll({departmentId: this.queryParams.deptId}).then(res => {
        this.maintenanceSectionList = res.data
      })
    },
    /** 查询养护路段下拉列表 */
    getMaintenanceSection() {
      listMaintenanceSectionAll().then(res => {
        this.maintenanceSectionList = res.data
      })
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    /** 提交选择养护路段操作 */
    handleSelectBridge() {
      if (!this.currentRow) {
        this.$modal.msgError("请选择桥梁");
      }
      this.$emit('update:checkEntity', this.currentRow);
      this.visible=false;
    }
  }
};
</script>
