//湿度概率直方图
import { getMax, getMin } from '@/views/jgjc/dataAnalysis/dataView/drawMethods/util'
import * as echarts from "echarts";
import myTheme from './myTheme'
export const PW = {
  //位移温度相关性图
  setMEANSCATTERChart(chartName, data) {
    let myChart = echarts.getInstanceByDom(
      document.getElementById(chartName)
    );
    if (myChart !== undefined) {
      myChart.dispose();
    }
    myChart = echarts.init(document.getElementById(chartName), myTheme.theme);
    let optionName = data.optionName;
    let xMin = getMin(data.xRawData.dataList)
    let xMax = getMax(data.xRawData.dataList)
    let yMax = getMax(data.yRawData.dataList)
    let yMin = getMin(data.yRawData.dataList)
    // 一些特殊情况处理
    if (xMax === 0 && xMin === 0) {
      xMax = 1
      xMin = -1
    } else {
      let delta = (xMax - xMin) * 0.2
      if (delta === 0) {
        delta = 1
      }
      xMax = xMax + delta
      xMin = xMin - delta
    }
    if (yMax === 0 && yMin === 0) {
      yMax = 1
      yMin = -1
    } else {
      let delta = (yMax - yMin) * 0.2
      if (delta === 0) {
        delta = 1
      }
      yMax = yMax + delta
      yMin = yMin - delta
    }

    let dataset = [];
    for (let i in data.xRawData.dataList) {
      let tmpLine = [];
      tmpLine.push(data.xRawData.dataList[i]);
      tmpLine.push(data.yRawData.dataList[i]);
      dataset.push(tmpLine);
    }
    let option = {
      title: {
        left: 'center',
        text: optionName,
      },
      tooltip: {
        textStyle:{
          align:'left'
        },
        trigger: "axis",
      },
      grid: {
        show: false,
        left: "3%",
        right: "3",
        bottom: "5%",
        top: "13%",
        containLabel: true,
      },
      toolbox: {
        feature: {
          saveAsImage: {
            title: "保存",
          },
        },
      },
      xAxis: {
        name: data.xRawData.name + "/" + data.xRawData.unit,
        nameLocation: "middle",
        type: "value",
        scale: true,
        nameGap: 20,
        axisLabel: {
          formatter: function (value) {
            return value.toFixed(data.xRawData.accuracy >= 0  ? data.xRawData.accuracy : 4); // 2表示小数为2位
          },
        },
        boundaryGap: ['20%', '20%'],
        axisLine:{
          show: true,
          onZero: false,
        },
        axisTick: {
          show: false
        },
      },
      yAxis: {
        name:
          data.yRawData.name + "/" + data.yRawData.unit,
        type: "value",
        scale: true,
        axisLabel: {
          formatter: function (value) {
            return value.toFixed(data.yRawData.accuracy >= 0  ? data.yRawData.accuracy : 4); // 2表示小数为2位
          },
        },
        boundaryGap: ['20%', '20%'],
        axisLine:{
          show: true,
          onZero: false,
        },
        axisTick: {
          show: false
        },
      },
      dataZoom: [
        {
          type: "inside", //详细配置可见echarts官网
        },
      ],
      series: [
        {
          // name: "",
          type: "scatter",
          data: dataset,
        },
      ],
    };
    option && myChart.setOption(option);
    //自适应大小
    window.onresize = function () {
      myChart.resize();
    };
  },

}
