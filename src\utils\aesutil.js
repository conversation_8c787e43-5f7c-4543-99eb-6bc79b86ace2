import CryptoJS from 'crypto-js';


function getKey() {
  // 定义前端Key秘钥-需要注意 跟后端解密秘钥保持一直
  let key = 'vEjLXJ/VMOFJyS6lP6s3hw==';
  return CryptoJS.enc.Utf8.parse(key);
}

//加密
export function encrypt(data) {
  const key = getKey();
  return CryptoJS.AES.encrypt(data, key,{mode: CryptoJS.mode.ECB, padding: CryptoJS.pad.Pkcs7}).toString();
}

//解密
export function decrypt(data) {
  const key = getKey();
  const decodedBytes = CryptoJS.enc.Base64.parse(data);
  const decrypted = CryptoJS.AES.decrypt({
    ciphertext: decodedBytes
  }, key, {
    mode: CryptoJS.mode.ECB,
    padding: CryptoJS.pad.Pkcs7
  });
  return decrypted.toString(CryptoJS.enc.Utf8);
}
