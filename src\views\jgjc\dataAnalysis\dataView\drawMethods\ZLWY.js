//湿度概率直方图
import { getMax, getMin } from '@/views/jgjc/dataAnalysis/dataView/drawMethods/util'
import * as echarts from "echarts";
import myTheme from './myTheme'
export const ZLWY = {

  //位移温度相关性图
  setWYCORChart(chartName, data) {
    let myChart = echarts.getInstanceByDom(
      document.getElementById(chartName)
    );
    if (myChart !== undefined) {
      myChart.dispose();
    }
    myChart = echarts.init(document.getElementById(chartName), myTheme.theme);
    let optionName = data.optionName;
    let xMin = getMin(data.xData.dataList)
    let xMax = getMax(data.xData.dataList)
    let yMax = getMax(data.yData.dataList)
    let yMin = getMin(data.yData.dataList)

    let minPoint = [
      xMin,
      (
        xMin *
        data.k +
        data.b
      ).toFixed(2),
    ];
    let maxPoint = [
      xMax,
      (
        xMax *
        data.k +
        data.b
      ).toFixed(2),
    ];
    // 一些特殊情况处理
    if (xMax === 0 && xMin === 0) {
      xMax = 1
      xMin = -1
    } else {
      let delta = (xMax - xMin) * 0.2
      if (delta === 0) {
        delta = 1
      }
      xMax = xMax + delta
      xMin = xMin - delta
    }
    if (yMax === 0 && yMin === 0) {
      yMax = 1
      yMin = -1
    } else {
      let delta = (yMax - yMin) * 0.2
      if (delta === 0) {
        delta = 1
      }
      yMax = yMax + delta
      yMin = yMin - delta
    }

    let dataset = [];
    for (let i in data.xData.dataList) {
      let tmpLine = [];
      tmpLine.push(data.xData.dataList[i]);
      tmpLine.push(data.yData.dataList[i]);
      dataset.push(tmpLine);
    }
    let markLineOpt = {
      animation: false,
      label: {
        formatter:
          "y = " +
          Number(data.k).toFixed(3) +
          " * x + " +
          Number(data.b).toFixed(3) +
          "  相关系数为" +
          Number(data.corrGust).toFixed(3) +
          "  ",
        // formatter: "y = 0.5 * x + 3",
        align: "right",
        fontSize: 18,
        fontWeight: "bold",
        distance: 90
      },
      lineStyle: {
        width: 2,
        type: "solid",
        color: 'red',
      },
      data: [
        [
          {
            coord: minPoint,
            symbol: "none",
          },
          {
            coord: maxPoint,
            symbol: "none",
          },
        ],
      ],
    };

    let option = {
      title: {
        left: 'center',
        text: optionName,
      },
      tooltip: {
        textStyle:{
          align:'left'
        },
        trigger: "axis",
      },
      grid: {
        left: "3%",
        right: "3",
        bottom: "5%",
        top: "13%",
        containLabel: true,
      },
      toolbox: {
        feature: {
          saveAsImage: {
            title: "保存",
          },
        },
      },
      xAxis: {
        name: data.xData.name + "/" + data.xData.unit,
        nameLocation: "middle",
        type: "value",
        scale: true,
        min: xMin,
        max: xMax,
        nameGap: 20,
        axisLabel: {
          formatter: function (value) {
            return value.toFixed(data.xData.accuracy >= 0  ? data.xData.accuracy : 4); // 2表示小数为2位
          },
        },
        axisLine:{
          show: true,
          onZero: false,
        },
        axisTick: {
          show: false
        },
      },
      yAxis: {
        name: this.splitBySpace(data.yData.name) + "/" + data.yData.unit,
        type: "value",
        scale: true,
        min: yMin,
        max: yMax,
        axisLabel: {
          formatter: function (value) {
            return value.toFixed(data.yData.accuracy >= 0  ? data.yData.accuracy : 4); // 2表示小数为2位
          },
        },
        axisLine:{
          show: true,
          onZero: false,
        },
        axisTick: {
          show: false
        },
      },
      dataZoom: [
        {
          type: "inside", //详细配置可见echarts官网
        },
      ],
      series: [
        {
          // name: "",
          type: "scatter",
          data: dataset,
          markLine: markLineOpt,
        },
      ],
    };
    option && myChart.setOption(option);
    //自适应大小
    window.onresize = function () {
      myChart.resize();
    };
  },
  //竖向位移频率分布直方图
  setZLWYDISChart(chartName, data) {
    let myChart = echarts.getInstanceByDom(
      document.getElementById(chartName)
    );
    if (myChart !== undefined) {
      myChart.dispose();
    }
    myChart = echarts.init(document.getElementById(chartName), myTheme.theme);

    let optionName = data.optionName;
    let xMinValue = data.minValue;
    let xMaxValue = data.maxValue;
    let xInterval = data.interval;
    let xLabel = []
    let loopTimes = (xMaxValue-xMinValue)/xInterval
    xLabel.push('<'+xMinValue);
    for(let i = 0; i < loopTimes; i++){
      xLabel.push(xMinValue+"~"+(xMinValue+xInterval));
      xMinValue += xInterval
    }
    xLabel.push('>'+xMaxValue);
    data = data.dataList
    let option = {
      title: {
        left: 'center',
        text: optionName,
      },
      toolbox: {
        right: 0,
        top: 25,
        feature: {
          saveAsImage: {
            title: '保存'
          },
        },
      },
      tooltip: {
        textStyle:{
          align:'left'
        },
        trigger: "axis",
        confine: true
      },
      xAxis: {
        type: 'category',
        name: "竖向位移直方图",
        nameLocation: "middle",
        nameGap: 22,
        data: xLabel
      },
      yAxis: {
        name: "比率",
        type: 'value',
        nameTextStyle: {
          fontWeight: "bold",
          fontSize: 12
        },
        axisLabel: {
          formatter: function (value, index) {
            return value.toFixed(0) + '%';
          }
        }
      },
      dataZoom: [
        {
          type: "inside",
        },
      ],
      grid: [
        {
          top: "17%",
          left: "2%",
          right: "10%",
          bottom: "14%",
          containLabel: true,
        },
      ],
      series: [],
      legend: {
        type: 'scroll',
        x: 'center',
        y: 'bottom',
        data: [],
      },
    };
    //加入多行数据
    for(let i = 0 ; i < data.length; i++){
      option.legend.data.push(data[i].label);
      option.series.push({
        type: "bar",
        barGap: 0,
        barCategoryGap: 0,
        name: data[i].label,
        data: data[i].distribution.map(item => item.toFixed(1)),
      });
    }
    option && myChart.setOption(option);
    window.onresize = function () {
      myChart.resize();
    };
  },
  //绘制空表 提示信息
  setEmptyChart(chartName, msg) {
    let myChart = echarts.getInstanceByDom(
      document.getElementById(chartName)
    );
    if (myChart !== undefined) {
      myChart.dispose();
    }
    myChart = echarts.init(document.getElementById(chartName), myTheme.theme);
    let option = {
      title: {
        text: msg,
        left:'center',
      },
    };
    option && myChart.setOption(option);
    //自适应大小
    window.onresize = function () {
      myChart.resize();
    };
  },

}
