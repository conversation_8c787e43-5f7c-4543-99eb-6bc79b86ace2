import axios from "axios";

const loadIcons = async () => {
  try {
    const response = await axios.get("/assets/AppIcons/iconfont.json");
    const { css_prefix_text, glyphs } = response.data;
    return glyphs.map((glyph) => ({
      name: glyph.name,
      class: css_prefix_text + glyph.font_class,
    }));
  } catch (error) {
    console.error("Error loading iconfont.json:", error);
    return [];
  }
};

export default loadIcons;
