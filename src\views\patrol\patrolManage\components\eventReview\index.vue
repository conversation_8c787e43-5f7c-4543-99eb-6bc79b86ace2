<template>
  <div class=" maindiv">
    <el-row :gutter="20">
      <!--部门数据-->
<!--      <el-col :span="relaNav ? 5 : 0" :xs="24" class="leftDiv">-->
<!--        &lt;!&ndash;折叠图标&ndash;&gt;-->
<!--        <div class="leftIcon" @click="relaNav = false">-->
<!--          <span class="el-icon-caret-left"></span>-->
<!--        </div>-->
<!--        <div class="head-container">-->
<!--          <el-input-->
<!--            v-model="keyword"-->
<!--            placeholder="输入关键词检索"-->
<!--            @change="handleSearch"-->
<!--            clearable-->
<!--            size="small"-->
<!--            prefix-icon="el-icon-search"-->
<!--            style="margin-bottom: 20px"-->
<!--          />-->
<!--        </div>-->
<!--        <div class="left-total">共 {{ leftTotal }} 条</div>-->
<!--        <div class="head-container" style="width: 300px">-->
<!--          <el-tree-->
<!--            :data="filteredTreeData"-->
<!--            :expand-on-click-node="false"-->
<!--            :filter-node-method="filterNode"-->
<!--            :default-expanded-keys="[0]"-->
<!--            ref="tree"-->
<!--            node-key="id"-->
<!--            highlight-current-->
<!--            @node-click="handleNodeClick"-->
<!--          >-->
<!--          </el-tree>-->
<!--        </div>-->
<!--      </el-col>-->
      <!--角色数据-->
      <el-col :span="24" :xs="24">
        <!--展开图标-->
        <div class="rightIcon" @click="relaNav = true" v-show="!relaNav">
          <span class="el-icon-caret-right"></span>
        </div>
        <el-row>
          <el-col :span="24" :xs="24">
            <el-row>
              <el-form
                ref="queryForm"
                :model="queryParams"
                size="small"
                :inline="true"
                label-width="68px"
              >
                <el-form-item label="" prop="disCode">
                  <el-input
                    v-model="queryParams.disCode"
                    placeholder="请输入事件编码"
                    clearable
                    style="width: 240px"
                  >
                  </el-input>
                </el-form-item>
<!--                <el-form-item label="" prop="domainId">-->
<!--                  <selectTree-->
<!--                    :key="'field2'"-->
<!--                    style="width: 240px"-->
<!--                    v-model="queryParams.domainIdStr"-->
<!--                    :deptType="100" :deptTypeList="[1, 3, 4]"-->
<!--                    placeholder="管养单位"-->
<!--                    clearable-->
<!--                    filterable-->
<!--                  />-->
<!--                </el-form-item>-->
<!--                <el-form-item label="" prop="maiSecId">-->
<!--                  <RoadSection v-model="queryParams.maiSecId" :deptId="queryParams.domainIdStr" placeholder="路段名称" style="width: 240px"/>-->
<!--                </el-form-item>-->
                <el-form-item label="" prop="dealType">
                  <dict-select type="disposal_type" clearable
                               v-model="queryParams.dealType" placeholder="请选择处理类型"
                               style="width: 240px"></dict-select>
                </el-form-item>
                <el-form-item label="" prop="handleStatusArr">
                  <el-select v-model="queryParams.handleStatusArr" multiple placeholder="请选择审核状态" style="width: 240px">
                    <el-option
                        v-for="item in handleStatusOptions"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value">
                    </el-option>
                  </el-select>
                </el-form-item>
                <el-form-item label="" prop="diseaseType">
                  <el-select v-model="queryParams.diseaseType" style="width: 100%" placeholder="请选择事件类型">
                    <el-option v-for="dict in disType" :key="dict.dictValue"
                               :label="dict.dictLabel" :value="dict.dictValue">
                    </el-option>
                  </el-select>
                </el-form-item>
                <el-form-item>
                  <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
                  <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
<!--                  <el-button v-show="!showSearch" @click="showSearch=true" icon="el-icon-arrow-down" circle></el-button>-->
<!--                  <el-button v-show="showSearch" @click="showSearch=false" icon="el-icon-arrow-up" circle></el-button>-->
                </el-form-item>
              </el-form>
              <el-col :span="24">
                <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" v-if="false"
                         label-width="68px">
                  <el-form-item label="" prop="reportNo">
                    <el-cascader
                        v-model="queryParams.reportNo"
                        :options="deptUserOptions"
                        :props="props"
                        :show-all-levels="false"
                        ref="deptUser"
                        placeholder="上报人" clearable style="width: 240px"
                        filterable clearable></el-cascader>
                  </el-form-item>
                  <el-form-item label="" prop="collectDates">
                    <el-date-picker
                      v-model="queryParams.collectDates"
                      format="yyyy-MM-dd"
                      value-format="yyyy-MM-dd HH:mm:ss"
                      style="width: 240px"
                      placeholder="上报开始时间"
                      clearable
                    ></el-date-picker>
                  </el-form-item>
                  <el-form-item label="" prop="collectDatee">
                    <el-date-picker
                      v-model="queryParams.collectDatee"
                      format="yyyy-MM-dd"
                      value-format="yyyy-MM-dd HH:mm:ss"
                      style="width: 240px"
                      placeholder="上报结束时间"
                      clearable
                    ></el-date-picker>
                  </el-form-item>

                  <el-form-item label="" prop="diseaseDesc">
                    <el-input
                      v-model="queryParams.diseaseDesc"
                      placeholder="请输入事件描述"
                      clearable
                      style="width: 240px"
                    >
                    </el-input>
                  </el-form-item>
                  <el-form-item label="" prop="createReason">
                    <dict-select type="event_type" clearable
                                 v-model="queryParams.createReason" placeholder="请选择事件分类"
                                 style="width: 240px"></dict-select>
                  </el-form-item>
                  <el-form-item label="" prop="disFrom">
                    <el-select v-model="queryParams.disFrom" placeholder="请选择事件来源" clearable style="width: 240px">
                        <el-option
                          key="直接新增"
                          label="直接新增"
                          value="直接新增"
                        ></el-option>
                    </el-select>
                  </el-form-item>
                </el-form>
              </el-col>
            </el-row>
          </el-col>
        </el-row>
        <!--操作按钮区开始-->
        <el-row :gutter="10" class="mb8">
<!--          <el-col :span="1.5">-->
<!--            <el-button-->
<!--              type="primary"-->
<!--              icon="el-icon-plus"-->
<!--              size="mini"-->
<!--              v-has-menu-permi="['roadDisease:roadDisease:add']"-->
<!--              @click="handleAdd"-->
<!--            >新增-->
<!--            </el-button-->
<!--            >-->
<!--          </el-col>-->
<!--          <el-col :span="1.5">-->
<!--            <el-button-->
<!--              type="danger"-->
<!--              icon="el-icon-delete"-->
<!--              size="mini"-->
<!--              v-has-menu-permi="['roadDisease:roadDisease:removeBatchByIds']"-->
<!--              @click="handleBatchDelete"-->
<!--            >批量删除-->
<!--            </el-button>-->
<!--          </el-col>-->
          <el-col :span="1.5">
            <el-button
              type="primary"
              icon="el-icon-download"
              size="mini"
              v-has-menu-permi="['roadDisease:roadDisease:export']"
              @click="exportList"
            >导出清单
            </el-button
            >
          </el-col>
<!--          <el-col :span="1.5">-->
<!--            <el-button-->
<!--              type="success"-->
<!--              icon="el-icon-view"-->
<!--              size="mini"-->
<!--              v-has-menu-permi="['roadDisease:roadDisease:reviewBatch']"-->
<!--              @click="handleOpenReview"-->
<!--            >批量审核-->
<!--            </el-button-->
<!--            >-->
<!--          </el-col>-->
          <right-toolbar
            :showSearch.sync="showSearch"
            @queryTable="handleQuery"
            :columns="columns"
          ></right-toolbar>
        </el-row>
        <el-row>
          <div class="draggable">
            <el-table v-adjust-table
              size="mini"
              style="width: 100%"
              v-loading="loading"
              border
              :data="tableData"
              row-key="id"
              ref="dataTable"
              stripe
              @selection-change="handleSelectionChange"
              @row-click="handleRowClick"
             height="22vh"
            >
              <el-table-column type="selection" width="50" align="center" :selectable="checkSelectable"/>
              <el-table-column
                label="序号"
                align="center"
                type="index"
                width="50"
              />
              <template v-for="(column,index) in columns">
                <el-table-column :label="column.label"
                                 v-if="column.visible"
                                 align="center"
                                 :prop="column.field"
                                 show-overflow-tooltip
                                 :width="column.width">
                  <template slot-scope="scope">
                    <dict-tag v-if="column.dict" :options="dict.type[column.dict]" :value="scope.row[column.field]"/>
                    <template v-else-if="column.slots">
                      <RenderDom :row="scope.row" :index="index" :render="column.render"/>
                    </template>
                    <span v-else-if="column.isTime">{{ parseTime(scope.row[column.field], '{y}-{m}-{d}') }}</span>
                    <span v-else>{{ scope.row[column.field] }}</span>
                  </template>
                </el-table-column>
              </template>
<!--              <el-table-column-->
<!--                label="操作"-->
<!--                fixed="right"-->
<!--                align="center"-->
<!--                width="160"-->
<!--                class-name="small-padding fixed-width"-->
<!--              >-->
<!--                <template slot-scope="scope">-->
<!--                  <el-button-->
<!--                    size="mini"-->
<!--                    type="text"-->
<!--                    icon="el-icon-view"-->
<!--                    @click="handleView(scope.row)"-->
<!--                  >查看-->
<!--                  </el-button-->
<!--                  >-->
<!--                  <el-button-->
<!--                    size="mini"-->
<!--                    type="text"-->
<!--                    v-has-menu-permi="['roadDisease:roadDisease:review']"-->
<!--                    icon="el-icon-check"-->
<!--                    :disabled="(scope.row.dealType == '2') || (scope.row.handleStatus == 2)"-->
<!--                    @click="handleCheck(scope.row)"-->
<!--                  >审核-->
<!--                  </el-button-->
<!--                  >-->
<!--                  <el-button-->
<!--                    size="mini"-->
<!--                    type="text"-->
<!--                    v-has-menu-permi="['roadDisease:roadDisease:delete']"-->
<!--                    icon="el-icon-delete"-->
<!--                    :disabled="scope.row.handleStatus == 2"-->
<!--                    @click="handleDelete(scope.row)"-->
<!--                  >删除-->
<!--                  </el-button-->
<!--                  >-->
<!--                </template>-->
<!--              </el-table-column>-->
            </el-table>
            <pagination
              v-show="total>0"
              :total="total"
              :page.sync="queryParams.pageNum"
              :limit.sync="queryParams.pageSize"
              @pagination="handleQuery"
            />
          </div>
        </el-row>
      </el-col>
    </el-row>
    <el-dialog :title="detailTitle" v-if="openDetail" destroy-on-close :visible.sync="openDetail" :close-on-click-modal="false" width="80%">
      <Detail @close="handleCloseDetail" :type="type" :row-data="rowData" :select-data="queryParams"></Detail>
    </el-dialog>

    <el-dialog title="批量审核" destroy-on-close :visible.sync="openCheck" width="80%">
      <el-form ref="form" :model="formData" :rules="rules" size="medium" label-width="100px">
        <el-form-item label="审核意见" prop="receiveComment">
          <el-input
            v-model="formData.receiveComment"
            type="textarea"
            :rows="4"
            placeholder="请输入审核意见"
          />
        </el-form-item>
        <div style="text-align: right">
          <el-button type="primary" @click="onSubmit(1)">有 效</el-button>
          <el-button type="primary" @click="onSubmit(0)">无 效</el-button>
        </div>
      </el-form>
    </el-dialog>
    <el-dialog title="附件列表" destroy-on-close :visible.sync="openFile" width="80%">
      <file-upload v-model="disPicPath" :forView="true"></file-upload>
    </el-dialog>
  </div>
</template>

<script>
import Detail from "./detail.vue";
import fileUpload from "@/components/FileUpload/index.vue";
import selectTree from "@/components/DeptTmpl/selectTree.vue";
import RoadSection from "@/views/baseData/components/roadSection/index.vue";
import {
  findAuditDataListNoLoading ,
  deleteAuditData,
  batchDeleteAuditData,
  batchReviewAuditData,
  batchReviewInvalidAuditData
} from '@/api/dailyMaintenance/eventManage/eventReview'
import {getTreeStruct} from "@/api/tmpl";
import {findUserDeptMaintenanceList2} from '@/api/system/maintenanceSection'
import moment from "moment";
import DeptUserTreeSelect from "@/components/DeptUserTreeSelect/index.vue";
import {formatPile} from "@/utils/ruoyi";
import {listAllDiseases} from "@/api/patrol/diseases";

export default {
  name: 'EventReview',
  components: {
    DeptUserTreeSelect,
    RoadSection, selectTree,
    Detail, fileUpload,
    RenderDom: {
      functional: true,
      props: {
        row: Object,
        index: Number,
        render: Function
      },
      render(createElement, ctx) {
        const {row, index} = ctx.props
        return ctx.props.render(row, index)
      }
    }
  },
  dicts: ['disposal_type', 'event_type', 'contract_status', 'sys_asset_type'],
  props: [],
  data() {
    return {
      leftTotal: 1,
      showSearch: false,
      queryParams: {
        domainId: '',
        pageNum: 1,
        pageSize: 50,
        handleStatusArr: []
      },
      total: 0,
      columns: [
        {key: 0, width: 100, field: 'maiSecName', label: `路段名称`, visible: true},
        {key: 1, width: 100, field: 'routeCode', label: `路线编码`, visible: true},
        {key: 23, width: 200, field: 'subRouteCode', label: `养护子段`, visible: true},
        {key: 22, width: 200, field: 'disCode', label: `事件编码`, visible: true},
        {key: 2, width: 100, field: 'beginMileShow', label: `起点桩号`, visible: true},
        {key: 3, width: 100, field: 'endMileShow', label: `终点桩号`, visible: true},
        {key: 4, width: 100, field: 'domainName', label: `管养单位`, visible: true},
        {key: 5, width: 100, field: 'domainName', label: `管理处`, visible: true},
        {key: 6, width: 200, field: 'diseaseName', label: `事件类型`, visible: true, dict: 'sys_asset_type'},
        {key: 7, width: 200, field: 'diseaseDesc', label: `事件描述`, visible: true},
        {key: 8, width: 100, field: 'dealType', label: `处置类型`, visible: true, dict: 'disposal_type'},
        {key: 9, width: 100, field: 'handleStatusName', label: `事件状态`, visible: true},
        {key: 10, width: 100, field: 'reportName', label: `上报人`, visible: true},
        {key: 11, width: 100, field: 'disFrom', label: `事件来源`, visible: true},
        {key: 12, width: 100, field: 'createReasonName', label: `事件分类`, visible: true},
        {key: 13, width: 100, field: 'collectDate', label: `采集时间`, visible: true, isTime: true},
        {
          key: 14, width: 100, field: 'disPicPath', label: `附件`, visible: true, slots: true, render: (row, index) => {
            return (
              <el-button
                size="mini"
                disabled = {!row.disPicPath}
                type="text" onClick={e => this.handleOpenFile(e, row)}>查看</el-button>
            )
          }
        }
      ],
      tableData: [],
      detailTitle: '新增事件',
      openDetail: false,
      rowData: {},
      openCheck: false,
      openFile: false,
      disPicPath: '',
      formData: {},
      loading: false,
      selectIds: [],
      rules: {
        receiveComment: [{
          required: true,
          message: '请输入审核意见',
          trigger: 'blur'
        }],
      },
      type: '',
      // 左侧组织树
      relaNav: true,
      keyword: '',
      relaName: '',
      relaOptions: [],
      filteredTreeData: [],
      handleStatusOptions: [{
          value: '1',
          label: '待审核'
        }, {
          value: '2',
          label: '审核通过'
        }, {
          value: '3',
          label: '审核无效'
        },
      ],
      // 部门-用户树选项
      deptUserOptions: [],
      disType: [],
      props: {
        multiple: false,//是否多选
        value: "id",
        emitPath: false
      },
    }
  },
  computed: {},
  watch: {

    '$attrs.current': function () {
      if (this.$attrs.current.ids){

        // this.queryParams.maintenanceSectionName = this.$attrs.current.maintenanceSectionName
        // this.queryParams.propertyUnitName = this.$attrs.current.maintenanceSectionName
        // this.queryParams.maintainUnitName = this.$attrs.current.maintenanceSectionName
        // this.queryParams.checkTime = this.$attrs.current.maintenanceSectionName

        // this.queryParams.idList = this.$attrs.current.ids.split(',')
        // this.queryParams.recordId = this.$attrs.current.ids
        this.handleQuery()
      }

    }

  },
  created() {
		this.getDisType()
    this.getDeptTreeDef()
    this.getDeptTree()
    // this.handleQuery()
  },
  mounted() {
  },
  methods: {
    /** 查询部门-用户下拉树结构 */
    getDeptTreeDef() {
      getTreeStruct({types:111}).then(response => {
        this.deptUserOptions = response.data;
      });
    },
    // 关键词检索
    handleSearch() {
      const keyword = this.keyword.toLowerCase();
      this.filteredTreeData = this.relaOptions.filter(node => this.filterNode(node, keyword));
    },
    // 筛选节点
    filterNode(node, keyword) {
      if (node.label.indexOf(keyword) != -1) {
        return true;
      }
      if (node.children) {
        return node.children.some(childNode => this.filterNode(childNode, keyword));
      }
      return false;
    },

    //  查询
    async handleQuery() {


      this.loading = true
      this.queryParams.domainId = parseInt(this.queryParams.domainIdStr) || null
      if (this.queryParams.domainId == 1) {
        this.queryParams.domainId = null
      }
      if (this.queryParams.handleStatusArr && this.queryParams.handleStatusArr.length > 0) {
        this.queryParams.handleStatusName = this.queryParams.handleStatusArr.join(',')
      } else {
        this.queryParams.handleStatusName = '1,2,3'
      }
      const endTime = this.queryParams.collectDatee ? moment(this.queryParams.collectDatee).endOf("day").format('YYYY-MM-DD HH:mm:ss') : null
      this.$set(this.queryParams, 'collectDatee', endTime)
      this.queryParams.sjType = 2

      if (this.$attrs.current.ids){
        let result = []
        for (let id of this.$attrs.current.ids.split(',')){
          console.log("发送请求")
        let temp =   findAuditDataListNoLoading({...this.queryParams, recordId: id}).then(res => {
            res.rows.forEach(item => {
              item.beginMileShow = formatPile(item.beginMile)
              item.endMileShow = formatPile(item.endMile)
            })
            this.tableData = res.rows
            this.total = res.total
            return res
          })
          result.push(temp)
        }

        Promise.all(result).then(res => {
          let total = 0
          let dataArray = []


          for (let temp of res){

            total += temp.total

            if (temp.rows){
              dataArray.push(...temp.rows)
            }

          }
          this.total = total
          this.tableData = dataArray
          this.loading = false

        }).finally(()=>{
          this.loading = false
        })
      }else
        this.loading = false






    },
    // 查询部门下拉树结构
    getDeptTree() {
      findUserDeptMaintenanceList2().then(response => {
        const treeData = response.data
        treeData.forEach(item => {
          getChild(item)
        })
        function getChild(node) {
          node.label = node.deptName || node.maintenanceSectionName
          node.id = node.deptId || node.maintenanceSectionId
          if (node.children) {
            node.children.forEach(item => {
              getChild(item)
            })
          }
        }
        // 增加一个最顶级
        const tree = [
            {
              label: '云南省交通投资建设集团有限公司',
              id: '1',
              deptId: '1',
              children: [...treeData]
            }
        ]
        this.relaOptions = tree
        this.filteredTreeData = [...this.relaOptions]
      });
    },
    // 修改
    handleUpdate(e) {
    },
    // 新增
    handleAdd() {
      this.type = 'add'
      this.detailTitle = '新增普通事件'
      this.rowData = {}
      this.openDetail = true
    },
    // 审核
    handleCheck(e) {
      this.type = 'examine'
      this.detailTitle = '审核上报事件'
      this.rowData = e
      this.openDetail = true
    },
    // 查看
    handleView(e) {
      this.type = 'view'
      this.detailTitle = '查看上报事件'
      this.rowData = e
      this.openDetail = true
    },
    // 删除
    handleDelete(e) {
      console.log(e)
      this.$modal.confirm('是否确认删除').then(() => {
        deleteAuditData(e.id).then(res => {
          this.handleQuery();
          this.$modal.msgSuccess("删除成功");
        })
      })
    },
    // 选中
    handleSelectionChange(e) {
      this.selectIds = e.map(obj => obj.id)
    },
    // 打开批量审核弹窗
    handleOpenReview() {
      if (this.selectIds.length <= 0) {
        this.$modal.warning('请至少勾选一条数据')
        return
      }
      this.openCheck = true
    },
    // 批量删除
    handleBatchDelete() {
      if (this.selectIds.length <= 0) {
        this.$modal.warning('请至少勾选一条数据')
        return
      }
      this.$modal.confirm('是否确认删除所选的数据').then(() => {
        batchDeleteAuditData(this.selectIds).then(res => {
          this.handleQuery();
          this.$modal.msgSuccess("删除成功");
        })
      })
    },
    handleCloseDetail() {
      this.openDetail = false
      this.handleQuery()
    },
    // 导出清单按钮
    exportList() {
      this.queryParams.fileName = '事件审核导出清单'


      this.download(
        'manager/roaddisease/export',
        {...this.queryParams, idList: this.selectIds.length < 1 ? this.tableData.map(item => item.id) :this.selectIds},
        `roaddisease_${new Date().getTime()}.xlsx`,
        {
          headers: { 'Content-Type': 'application/json;' },
          parameterType: 'body'
        }
      )
    },
    handleNodeClick(e) {
      this.queryParams.domainId = ''
      this.queryParams.domainName = ''
      this.queryParams.maiSecName = ''
      this.queryParams.maiSecId = ''
      this.queryParams.domainIdStr = ''
      if (e.deptId) {
        this.queryParams.domainIdStr = String(e.id)
        this.queryParams.domainId = parseInt(e.id)
        this.queryParams.domainName = e.label
      } else {
        this.queryParams.domainIdStr = String(e.departmentId)
        this.queryParams.domainId = parseInt(e.departmentId)
        this.queryParams.domainName = e.departmentName
        this.queryParams.maiSecName = e.label
        this.queryParams.maiSecId = e.id
      }
      this.handleQuery()
    },
    handleOpenFile(e, row) {
      this.disPicPath = ''
      this.openFile = true
      this.disPicPath = row.disPicPath
    },
    resetQuery() {
      this.queryParams = {
        pageNum: 1,
        pageSize: 50,

      }
      this.handleQuery()
    },
    onSubmit(status) {
      this.$refs.form.validate(valid => {
        if (!valid) return
        // 根据selectIds 获取选中的数据
        const selectData = this.tableData.filter(item => this.selectIds.includes(item.id))
        // 循环selectData，赋值receiveComment
        selectData.forEach(item => {
          item.receiveComment = this.formData.receiveComment
          item.handleStatus = status == 1 ? 2 : 3
        })
        // 根据status 判断审核状态
        if (status == 1) {
          // 审核通过
          batchReviewAuditData(selectData).then(res => {
            if (res.code == 200) {
              this.$modal.msgSuccess('操作成功')
              this.openCheck = false
              this.handleQuery()
            }
          })
        } else {
          // 审核不通过
          batchReviewInvalidAuditData(selectData).then(res => {
            if (res.code == 200) {
              this.$modal.msgSuccess('操作成功')
              this.openCheck = false
              this.handleQuery()
            }
          })
        }
      })
    },
    handleRowClick(row) {
      if (row.handleStatus != 2) {
        row.isSelected = !row.isSelected;
        this.$refs.dataTable.toggleRowSelection(row);
      }
    },
	  getDisType() {
		  listAllDiseases().then(res => {
			  this.disType = res.data.map(item => {
				  return {
					  dictLabel: item.diseaseName,
					  dictValue: item.id
				  }
			  })
		  })
	  },
    checkSelectable(row) {
      return true
      // return row.handleStatus != 2
    }
  }
}
</script>

<style lang="scss" scoped>
.leftDiv {
  border-right: 1px solid #d8dce5;
  min-height: calc(100vh - 100px);
  overflow-y: auto;
  height: calc(80vh - 150px);
  position: relative;
  top: -20px;
  padding-top: 10px;
  background-color: white;
}

.leftIcon {
  border: 1px solid #dcdfe6;
  border-radius: 8px;
  width: 16px;
  height: 50px;
  line-height: 50px;
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
  position: absolute;
  right: 0;
  top: 300px;
  z-index: 2;
}

.leftIcon:hover {
  background-color: #dcdfe6;
}

.rightIcon {
  border: 1px solid #dcdfe6;
  border-radius: 8px;
  width: 16px;
  height: 50px;
  line-height: 50px;
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
  position: absolute;
  left: -10px;
  top: 280px;
  z-index: 10;
  background: white;
}

.rightIcon:hover {
  background-color: #dcdfe6;
}

.left-total {
  font-size: 13px;
  line-height: 28px;
  color: #606266;
  position: absolute;
  bottom: 10px;
  right: 10px;
}
</style>
<style lang="scss" scoped>
@import "@/assets/styles/business.scss";
</style>
