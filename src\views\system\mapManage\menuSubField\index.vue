<template>
  <PageContainer>
    <template slot="search">
      <el-row :gutter="20">
        <el-col :span="3" :offset="0">
          <el-input v-model="queryParams.columnNameZh" placeholder="请输入中文名称" clearable size="mini"
            @keyup.enter.native="handleQuery" />
        </el-col>
        <!-- <el-col :span="3" :offset="0">
          <el-select v-model="queryParams.shapeType" placeholder="请选择数据类型" clearable size="mini" style="width: 100%;">
            <el-option v-for="dict in dict.type.shape_type" :key="dict.value" :label="dict.label" :value="dict.value" />
          </el-select>
        </el-col> -->
        <el-col :span="6" :offset="0">
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">
            搜索
          </el-button>
          <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">
            重置
          </el-button>
        </el-col>
      </el-row>
    </template>
    <template slot="header">
      <el-row :gutter="20">
        <el-col :span="1.5" :offset="0" class="mb8">
          <el-button v-hasPermi="['baseData:culvert:add']" type="primary" size="mini" @click="handleAdd">
            新增
          </el-button>
        </el-col>
        <el-col :span="1.5" :offset="0" class="mb8">
          <el-button v-hasPermi="['baseData:culvert:edit']" :disabled="!(tableSelects.length === 1)" type="primary"
            size="mini" @click="handleUpdate">
            编辑
          </el-button>
        </el-col>
        <el-col :span="1.5" :offset="0" class="mb8">
          <el-button v-hasPermi="['baseData:culvert:delete']" :disabled="!(tableSelects.length > 0)" type="primary"
            size="mini" @click="handleDelete">
            删除
          </el-button>
        </el-col>
        <el-col :span="1.5" :offset="0" class="mb8">
          <el-button v-hasPermi="['baseData:culvert:getInfoById']" :disabled="!(tableSelects.length === 1)" type="primary"
            size="mini" @click="handleDetail">
            查看
          </el-button>
        </el-col>
      </el-row>
    </template>
    <template slot="body">
      <el-table :key="$route.query.id" v-loading="loading" :data="menuSubFieldList" @selection-change="handleSelectionChange"
        @row-click="handleRowClick" border height="100%" :row-key="(row) => row.id" ref="tableRef">
        <el-table-column type="selection" width="55" align="center" :reserve-selection="true" />
        <el-table-column label="主键id" min-width="150" prop="id" show-overflow-tooltip/>
        <el-table-column label="中文名称" min-width="150" align="center" prop="columnNameZh" show-overflow-tooltip />
        <el-table-column label="字段名称" align="center" prop="columnName" show-overflow-tooltip />
        <el-table-column label="字段数据类型" align="center" prop="columnType">
          <template slot-scope="{row}">
            <dict-tag :options="dict.type.data_type" :value="row.columnType" />
          </template>
        </el-table-column>
        <el-table-column label="输入框类型" width="100" align="center" prop="inputBoxType">
          <template slot-scope="{row}">
            <dict-tag :options="dict.type.input_type" :value="row.inputBoxType" />
          </template>
        </el-table-column>
        <el-table-column label="是否显示在列表上" align="center" prop="ifTableShow">
          <template slot-scope="{row}">
            <el-link type="primary" :underline="false">{{ row.ifTableShow == 0 ? '否' : '是' }}</el-link>
          </template>
        </el-table-column>
        <el-table-column label="显示字段是否模糊搜索" align="center" prop="ifLikeSearch">
          <template slot-scope="{row}">
            <el-link type="primary" :underline="false">{{ row.ifLikeSearch == 0 ? '否' : '是' }}</el-link>
          </template>
        </el-table-column>
        <el-table-column label="数据表头字段名" min-width="150" align="center" prop="tableHeadName" show-overflow-tooltip />
        <el-table-column label="排序" align="center" prop="showIndex" />
        <!-- <el-table-column label="是否用于排序" width="120"  align="center" prop="ifSort">
          <template slot-scope="{row}">
            <el-link type="primary" :underline="false">{{ row.ifShow == 0 ? '是' : '否' }}</el-link>
          </template>
        </el-table-column> -->
        <!-- <el-table-column label="转数字排序" width="100" align="center" prop="ifSortSigned" /> -->

        <!-- <el-table-column label="shape 数据类型"  width="130" align="center" prop="shapeType">
          <template slot-scope="{row}">
            <dict-tag :options="dict.type.shape_type" :value="row.shapeType" />
          </template>
        </el-table-column> -->
        <!-- <el-table-column label="字段数据正则表达式" min-width="150" align="center" prop="regEx" />
        <el-table-column label="选择项" align="center" prop="optionName">
          <template slot-scope="{row}">
            {{ row.inputBoxType }}
          </template>
        </el-table-column> -->
        <!-- <el-table-column label="是否必传" align="center" prop="ifRequired">
          <template slot-scope="{row}">
            {{ row.inputBoxType == 0 ? '否' : '是' }}
          </template>
        </el-table-column> -->
      </el-table>

      <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize" @pagination="getList" />
    </template>


    <!-- 新增编辑查看 -->
    <Dialog :title="title" :show.sync="open">
      <AddOrEdit :form="form" @close="handleClose" @refresh="getList" :readonly="readonly" />
    </Dialog>
  </PageContainer>
</template>

<script>
import { getListPage, getMenuSubField, delMenuSubField } from "@/api/oneMap/menuSubField";
import AddOrEdit from "./components/addOrEdit.vue";
import Dialog from "@/components/Dialog/index.vue";

export default {
  name: "MenuSubField",
  dicts: ['shape_type','data_type', 'input_type'],
  components: {
    AddOrEdit,
    Dialog,
  },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 查询列头显示字段表格数据
      menuSubFieldList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        menuSubId: null,
        columnNameZh: null,
        columnName: null,
        columnType: null,
        ifShow: null,
        showIndex: null,
        ifSort: null,
        ifSortSigned: null,
        inputBoxType: null,
        shapeType: null,
        regEx: null,
        optionName: null,
        ifRequired: null
      },
      // 表单参数
      form: {},
      tableSelects: [],
      readonly: false,
    };
  },
  created() {
    this.queryParams.menuSubId = this.$route.query.id
    this.getList();
  },
  watch: {
    '$route.query.id': function(newId) {
      this.queryParams.menuSubId = newId;
      this.getList();
      this.tableSelects = []
    }
  },
  methods: {
    getList() {
      this.loading = true;
      getListPage(this.queryParams).then(response => {
        this.menuSubFieldList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        menuSubId: null,
        columnNameZh: null,
        columnName: null,
        columnType: null,
        ifShow: null,
        showIndex: null,
        ifSort: null,
        ifSortSigned: null,
        inputBoxType: null,
        shapeType: null,
        regEx: null,
        optionName: null,
        ifRequired: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.queryParams.columnNameZh=null
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.tableSelects = selection;
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.form.menuSubId = this.$route.query.id
      this.open = true;
      this.title = "添加查询列头显示字段";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = this.tableSelects[0].id
      getMenuSubField(id).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改查询列头显示字段";
      });
    },
    handleClose(e) {
      this.open = e;
      this.readonly = false;
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = this.tableSelects.map(v=>v.id);
      this.$modal.confirm('是否确认删除查询列头显示字段的数据项？').then(function () {
        return delMenuSubField(ids);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => { });
    },
    handleRowClick(row) {
      let arr = this.tableSelects.filter((v) => v.id == row.id);
      // 点击行 选中复选框
      if (arr && arr.length > 0) {
        this.$refs.tableRef.toggleRowSelection(row, false);
        this.tableSelects = this.tableSelects.filter((v) => v.id != row.id);
      } else {
        this.tableSelects = [...this.tableSelects, ...[row]];
        this.$refs.tableRef.toggleRowSelection(row, true);
      }
    },
    // 查看详情
    handleDetail() {
      this.reset();
      const id = this.tableSelects[0].id
      this.readonly = true;
      getMenuSubField(id).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "查看查询列头显示字段";
      });
    },
  }
};
</script>

<style lang="scss" scoped></style>
