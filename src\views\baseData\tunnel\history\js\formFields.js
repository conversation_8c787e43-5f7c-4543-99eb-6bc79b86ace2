// 基础信息
const baseInfo = [
  {
    label: '管理处类别',
    prop: 'maintenanceUnitCategory',
    placeholder: '请选择管理处类别',
    type: 'dictSelect',
    dict: 'tunnel_maintenance_unit_category',
    rules: [{ required: true, message: '请选择管理处类别', trigger: 'change' }]
  },
  {
    label: '管理处',
    prop: 'managementMaintenanceId',
    placeholder: '请选择按管理处',
    type: 'selectTree',
    rules: [{ required: true, message: '请选择管理处', trigger: 'change' }],
    deptType: 201
  },
  {
    label: '管养分处',
    prop: 'managementMaintenanceBranchId',
    placeholder: '请选择管养分处',
    type: 'selectTree',
    rules: [{ required: true, message: '请选择管养分处', trigger: 'change' }],
    deptType: 202
  },
  {
    label: '养护路段',
    prop: 'maintenanceSectionId',
    placeholder: '请选择养护路段',
    type: 'select',
    rules: [{ required: true, message: '请选择养护路段', trigger: 'change' }],
    options: [],
    disabled: true,
    api: 'listMaintenanceSectionAll',
    optionLabel: 'maintenanceSectionName',
    optionValue: 'maintenanceSectionId',
    disabledFieds: 'managementMaintenanceId'
  },
  {
    label: '路线编码',
    prop: 'routeCode',
    placeholder: '请选择路线编码',
    type: 'select',
    rules: [{ required: true, message: '请选择路线编码', trigger: 'change' }],
    options: [],
    disabled: true,
    api: 'routeListAll',
    optionLabel: 'routeCode',
    optionValue: 'routeCode',
    disabledFieds: 'maintenanceSectionId'
  },
  {
    label: '路线名称',
    prop: 'routeName',
    placeholder: '请输入路线名称',
    type: 'select',
    api: 'routeListAll',
    optionLabel: 'routeName',
    optionValue: 'routeName',
    disabledFieds: 'maintenanceSectionId'
  },
  {
    label: '路段类型',
    prop: 'sectionType',
    placeholder: '请选择路段类型',
    type: 'roadType',
    rules: [{ required: true, message: '请选择路段类型', trigger: 'change' }],
  },
  {
    label: '隧道名称',
    prop: 'tunnelName',
    placeholder: '请输入隧道名称',
    rules: [{ required: true, message: '请输入隧道名称', trigger: 'blur' }],
    type: 'input',
  },
  {
    label: '隧道编码',
    prop: 'tunnelCode',
    placeholder: '请输入隧道编码',
    rules: [{ required: true, message: '请输入隧道编码', trigger: 'blur' }],
    type: 'input',
  },
  {
    label: '按隧道长度分类',
    prop: 'lengthClassification',
    placeholder: '请选择按隧道长度分类',
    type: 'dictSelect',
    rules: [{ required: true, message: '请选择按隧道长度分类', trigger: 'change' }],
    dict: 'tunnel_length_classification',
  },
  {
    label: '方向',
    prop: 'direction',
    placeholder: '请选择方向',
    type: 'dictSelect',
    dict: 'sys_route_direction',
    rules: [{ required: true, message: '请选择方向', trigger: 'change' }]
  },
  {
    label: '隧道评定等级',
    prop: 'assessmentGrade',
    placeholder: '请选择隧道评定等级',
    type: 'dictSelect',
    dict: 'tunnel_assess_grade',
    rules: [{ required: true, message: '请选择隧道评定等级', trigger: 'change' }]
  },
  {
    label: '隧道孔类型',
    prop: 'holeTypeCode',
    placeholder: '请选择隧道孔类型',
    type: 'dictSelect',
    dict: 'tunnel_hole_type_code',
    rules: [{ required: true, message: '请选择隧道孔类型', trigger: 'change' }]
  },
  {
    label: '建成时间',
    prop: 'buildDate',
    placeholder: '请选择建成时间',
    type: 'date',
    rules: [{ required: true, message: '请输入建成时间', trigger: 'blur' }]
  },
  {
    label: '建成通车时间',
    prop: 'operationDate',
    placeholder: '请选择建成通车时间',
    type: 'date',
    rules: [{ required: true, message: '请选择建成通车时间', trigger: 'change' }]
  },
  {
    label: '是否水下隧道',
    prop: 'underwaterTunnel',
    placeholder: '请选择是否水下隧道',
    type: 'dictSelect',
    dict: 'base_data_yes_no',
  },
  {
    label: '隧道入口桩号',
    prop: 'startStake',
    placeholder: '请输入隧道入口桩号',
    type: 'pileInput',
    precision: 3,
    rules: [{ required: true, message: '请输入隧道入口桩号', trigger: 'blur' }]
  },
  {
    label: '终点桩号',
    prop: 'endStake',
    placeholder: '请输入终点桩号',
    type: 'pileInput',
    precision: 3,
    rules: [{ required: true, message: '请输入终点桩号', trigger: 'blur' }]
  },
  {
    label: '施工桩号',
    prop: 'constructionStake',
    placeholder: '请输入施工桩号',
    type: 'pileInput',
    precision: 3,
  },
  {
    label: '国高网桩号',
    prop: 'nationalNetworkStake',
    placeholder: '请输入国高网桩号',
    type: 'pileInput',
    precision: 3,
  },
  {
    label: '统一里程桩号',
    prop: 'unifiedMileageStake',
    placeholder: '统一里程桩号',
    type: 'pileInput',
    precision: 3,
  },
  {
    label: '地理位置经度',
    prop: 'longitude',
    placeholder: '请输入地理位置经度',
    type: 'input',

  },
  {
    label: '地理位置纬度',
    prop: 'latitude',
    placeholder: '请输入地理位置纬度',
    type: 'input',
  },
  {
    label: '地理位置高程',
    prop: 'elevation',
    placeholder: '请输入地理位置高程',
    type: 'input',
  },
  {
    label: '运营状态',
    prop: 'operationState',
    placeholder: '请选择运营状态',
    type: 'dictSelect',
    dict: 'sys_operation_state',

  },

  {
    label: '所在政区编码',
    prop: 'areaCode',
    placeholder: '请输入所在政区编码',
    type: 'input',
    rules: [
      { required: true, message: '所在政区编码', trigger: 'blur' },
      { max: 32, message: '长度最大为32个字符', trigger: 'blur' }
    ]
  },

  {
    label: '是否跨省隧道',
    prop: 'crossProvinceTunnel',
    placeholder: '请选择是否跨省隧道',
    type: 'dictSelect',
    dict: 'base_data_yes_no',
  },

  {
    label: '是否在长大隧道目录',
    prop: 'inLongTunnelDirectory',
    placeholder: '请选择是否在长大隧道目录',
    type: 'dictSelect',
    dict: 'base_data_yes_no'
  },

  {
    label: '设计时速(km/h)',
    prop: 'designSpeed',
    placeholder: '请输入设计时速',
    type: 'inputNumber',
  },
  {
    label: '设计批复文件',
    prop: 'designFilesPath',
    placeholder: '请输入设计批复文件',
    type: 'input',

  },
  {
    label: '养护等级',
    prop: 'maintenanceGrade',
    placeholder: '请选择养护等级',
    type: 'dictSelect',
    rules: [{ required: true, message: '请选择养护等级', trigger: 'change' }],
    dict: 'tunnel_maintenance_grade',
  },
  {
    label: '围岩等级',
    prop: 'surroundingRockGrade',
    placeholder: '请输入围岩等级',
    type: 'input'
  },
  {
    label: '交竣工图纸',
    prop: 'finishPapersPath',
    placeholder: '请输入交竣工图纸',
    type: 'input'
  },
  {
    label: '隧道出口照片',
    prop: 'outHoleImage',
    placeholder: '请选择文件',
    type: 'uploadImg'
  },
  {
    label: '隧道进口照片',
    prop: 'inHoleImage',
    placeholder: '请选择文件',
    type: 'uploadImg'
  },
  {
    label: '隧道洞内典型照片',
    prop: 'innerImage',
    placeholder: '请选择文件',
    type: 'uploadImg'
  },
]
// 结构技术数据
const technology = [
  {
    label: '隧道长度(m)',
    prop: 'tunnelLength',
    placeholder: '请输入桥长',
    type: 'inputNumber',
    precision: 2,
  },
  {
    label: '隧道净宽(m)',
    prop: 'tunnelWidth',
    placeholder: '请输入隧道净宽',
    type: 'inputNumber',
    precision: 2,
  },
  {
    label: '隧道净高(m)',
    prop: 'tunnelHeight',
    placeholder: '请输入隧道净高',
    type: 'inputNumber',
    precision: 2,
  },

  {
    label: '拱腰半径(m)',
    prop: 'archRadius',
    placeholder: '请输入拱腰半径',
    type: 'inputNumber',
    precision: 2,
  },

  {
    label: '洞门形式',
    prop: 'holeStyle',
    placeholder: '请选择洞门形式',
    type: 'dictSelect',
    dict: 'tunnel_hole_style'
  },
  {
    label: '衬砌类型',
    prop: 'liningType',
    placeholder: '请选择衬砌类型',
    type: 'dictSelect',
    dict: 'tunnel_lining_type'
  },
  {
    label: '进洞门类型',
    prop: 'tunnelDoorTypeIn',
    placeholder: '请输入进洞门类型',
    type: 'input',

  },
  {
    label: '出洞门类型',
    prop: 'tunnelDoorTypeOut',
    placeholder: '请输入出洞门类型',
    type: 'input',

  },
  {
    label: '是否危隧',
    prop: 'isDangerous',
    placeholder: '请否选择是否危隧',
    type: 'dictSelect',
    dict: 'base_data_yes_no'

  },
  {
    label: '单车道宽度(m)',
    prop: 'singleLaneWidth',
    placeholder: '请输入单车道宽度(m)',
    type: 'inputNumber',
    precision: 2,
  },
  {
    label: '车道总宽度(m)',
    prop: 'totalLaneWidth',
    placeholder: '请输入车道总宽度',
    type: 'inputNumber',
    precision: 2,
  },
  {
    label: '检修道',
    prop: 'accessRoad',
    placeholder: '请输入检修道',
    type: 'input',

  },
  {
    label: '检修道宽度(m)',
    prop: 'accessRoadWidth',
    placeholder: '请输入检修道宽度',
    type: 'inputNumber',
    precision: 2,
  },
  {
    label: '最大纵坡(%)',
    prop: 'maxLongitudinalSlope',
    placeholder: '请输入最大纵坡',
    type: 'inputNumber',
    min: 1,
    max: 100,
  },

  {
    label: '衬砌材料',
    prop: 'liningMaterial',
    placeholder: '请输入衬砌材料',
    type: 'input'
  },
  {
    label: '断面形式',
    prop: 'sectionForm',
    placeholder: '请输入断面形式',
    type: 'input'
  },
  {
    label: '人行横洞数量',
    prop: 'personGalleryNum',
    placeholder: '请输入人行横洞数量',
    type: 'inputNumber',
    min: 0,
  },
  {
    label: '车行横洞数量',
    prop: 'carGalleryNum',
    placeholder: '请输入最大纵坡',
    type: 'inputNumber',
    min: 0,
  },
  {
    label: '紧急停车带数量',
    prop: 'emergencyParkingStripNum',
    placeholder: '请输入最大纵坡',
    type: 'inputNumber',
    min: 0,
  },
  {
    label: '是否有逃生通道',
    prop: 'escapeRoute',
    placeholder: '请选择是否有逃生通道',
    type: 'dictSelect',
    dict: 'base_data_yes_no'

  },
  {
    label: '配置等级',
    prop: 'allocationGrade',
    placeholder: '请输入配置等级',
    type: 'dictSelect',
    dict: 'tunnel_allocation_grade',


  },
  {
    label: '土建评定等级',
    prop: 'civilGrade',
    placeholder: '请选择土建评定等级',
    type: 'dictSelect',
    dict: 'tunnel_assess_grade'

  },
  {
    label: '机电评定等级',
    prop: 'electricalGrade',
    placeholder: '请选择机电评定等级',
    type: 'dictSelect',
    dict: 'tunnel_assess_grade'

  },
  {
    label: '其他评定等级',
    prop: 'otherGrade',
    placeholder: '请选择其他评定等级',
    type: 'dictSelect',
    dict: 'tunnel_assess_grade'

  },
  {
    label: '机电设施类别',
    prop: 'facilityType',
    placeholder: '请输入机电设施类别',
    type: 'input',
  },
  {
    label: '共用机电设施的隧道编码',
    prop: 'facilityCode',
    placeholder: '请输入共用机电设施的隧道编码',
    type: 'input',
  },
  {
    label: '通风控制方式',
    prop: 'ventilationWay',
    placeholder: '请输入通风控制方式',
    type: 'input',
  },
  {
    label: '照明设施控制方式',
    prop: 'lightingWay',
    placeholder: '请输入通风控制方式',
    type: 'input',
  },
  {
    label: '进洞口防护和过渡',
    prop: 'protectionTransitionIn',
    placeholder: '请输入进洞口防护和过渡',
    type: 'input',
  },
  {
    label: '出洞口防护和过渡',
    prop: 'protectionTransitionOut',
    placeholder: '出洞口防护和过渡',
    type: 'input',
  },
]
// 隧道管理信息数据
const information = [
  {
    label: '管理单位编码',
    prop: 'mgrUnitCode',
    placeholder: '请输入管理单位编码',
    type: 'input',
  },
  {
    label: '管理单位名称',
    prop: 'mgrUnitName',
    placeholder: '请输入管理单位名称',
    type: 'input',
  },
  {
    label: '所属单位名称',
    prop: 'propertyUnitName',
    placeholder: '请输入所属单位名称',
    type: 'input',
  },
  {
    label: '监管单位名称',
    prop: 'superUnitName',
    placeholder: '请输入监管单位名称',
    type: 'input',
  },
  {
    label: '建设单位名称',
    prop: 'buildUnitName',
    placeholder: '请输入建设单位名称',
    type: 'input',
  },
  {
    label: '设计单位名称',
    prop: 'designUnitName',
    placeholder: '请输入设计单位名称',
    type: 'input',
  },
  {
    label: '监理单位名称',
    prop: 'supervisionUnitName',
    placeholder: '请输入监理单位名称',
    type: 'input',
  },
  {
    label: '施工单位名称',
    prop: 'constructUnitName',
    placeholder: '请输入施工单位名称',
    type: 'input',
  },
]

export default {
  baseInfo,
  technology,
  information,
}
