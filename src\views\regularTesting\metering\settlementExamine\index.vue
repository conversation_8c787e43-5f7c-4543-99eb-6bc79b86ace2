<template>
  <div class="app-container maindiv">
    <el-row :gutter="20" style="display: flex">
      <!--部门数据-->
      <maintenance-tree
        @rowClick="handleNodeClick"
        :realNav="realNav"
        @closeNav="realNav = false"
      ></maintenance-tree>
      <!--角色数据-->
      <el-col :span="realNav ? 19 : 24" :xs="24">
        <!--展开图标-->
        <div class="rightIcon" @click="realNav = true" v-show="!realNav">
          <span class="el-icon-caret-right"></span>
        </div>
        <el-row>
          <el-col :span="24" :xs="24">
            <el-row>
              <el-form
                ref="queryForm"
                :model="queryParams"
                size="mini"
                :inline="true"
                label-width="68px"
              >
                <el-form-item label="">
                  <el-date-picker
                    style="width: 240px"
                    v-model="queryParams.year"
                    type="year"
                    @change="getNumberList"
                    value-format="yyyy"
                    placeholder="年份"
                  >
                  </el-date-picker>
                </el-form-item>
                <el-form-item>
                  <el-select
                    v-model="queryParams.number"
                    placeholder="期数"
                    style="width: 240px"
                    clearable
                  >
                    <el-option
                      v-for="item in numberList"
                      :key="item.value"
                      :value="item.value"
                      :label="item.label"
                    ></el-option>
                  </el-select>
                </el-form-item>
                <el-form-item>
                  <selectTree
                    :key="'domainId'"
                    style="width: 240px"
                    v-model="queryParams.domainId"
                    :deptType="100"
                    :deptTypeList="[1, 3, 4]"
                    placeholder="管养单位"
                    clearable
                    filterable
                  />
                </el-form-item>
                <el-form-item>
                  <selectTree
                    :key="'constructionUnit'"
                    v-model="queryParams.calcDomainId" :data-rule="false"
                    clearable
                    filterable
                    :dept-type="100"
                    :filter-keys="['云南省交通投资建设集团有限公司', '云南交投投资有限公司']"
                    :expand-all="false"
                    placeholder="施工单位"
                    style="width: 240px"
                  />
                </el-form-item>
                <el-form-item>
                  <RoadSection
                    v-model="queryParams.maiSecId"
                    :deptId="queryParams.domainId"
                    placeholder="路段"
                  />
                </el-form-item>
                <el-form-item>
                  <el-button
                    type="primary"
                    icon="el-icon-search"
                    size="mini"
                    @click="handleQuery"
                    >搜索</el-button
                  >
                  <el-button
                    icon="el-icon-refresh"
                    size="mini"
                    @click="resetQuery"
                    >重置</el-button
                  >
                  <el-button
                    v-show="!showSearch"
                    @click="showSearch = true"
                    icon="el-icon-arrow-down"
                    circle
                  ></el-button>
                  <el-button
                    v-show="showSearch"
                    @click="showSearch = false"
                    icon="el-icon-arrow-up"
                    circle
                  ></el-button>
                </el-form-item>
              </el-form>
              <!--默认折叠-->
              <el-col :span="24">
                <el-form
                  :model="queryParams"
                  ref="queryForm"
                  size="small"
                  :inline="true"
                  v-show="showSearch"
                  label-width="68px"
                >
                  <el-form-item>
                    <el-input
                      style="width: 240px"
                      placeholder="结算计量单名称"
                      v-model="queryParams.name"
                    ></el-input>
                  </el-form-item>
                  <el-form-item>
                    <el-input
                      style="width: 240px"
                      placeholder="结算计量单编码"
                      v-model="queryParams.code"
                    ></el-input>
                  </el-form-item>
                  <el-form-item>
                    <el-input
                      style="width: 240px"
                      placeholder="任务单编号"
                      v-model="queryParams.constructionCode"
                    ></el-input>
                  </el-form-item>
                  <el-form-item>
                    <el-input
                      style="width: 240px"
                      placeholder="中间计量单编码"
                      v-model="queryParams.intermediateCode"
                    ></el-input>
                  </el-form-item>
                </el-form>
              </el-col>
            </el-row>
          </el-col>
        </el-row>
        <!--操作按钮区开始-->
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button
              type="success"
              icon="el-icon-view"
              size="mini"
              @click="handleOpenOperate"
              >审核意见
            </el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button
              type="warning"
              icon="el-icon-download"
              size="mini"
              v-has-menu-permi="['calccheck:settlecalc:pendingexport']"
              @click="exportList"
              >导出清单
            </el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button
              type="success"
              @click="handlePreview"
              icon="el-icon-view"
              >报表预览</el-button
            >
          </el-col>
          <el-col :span="1.5">
            <el-button
              type="warning"
              @click="handleDownload"
              icon="el-icon-download"
              >报表下载</el-button
            >
          </el-col>
          <el-col :span="1.5">
            <el-button
              type="warning"
              icon="el-icon-view"
              size="mini"
              v-has-permi="['settlement:repository:regenerate']"
              @click="handleRegenerate"
            >重新生成报表
            </el-button>
          </el-col>
          <right-toolbar
            :showSearch.sync="showSearch"
            @queryTable="handleQuery"
            :columns="columns"
          ></right-toolbar>
        </el-row>
        <el-row>
          <div class="draggable">
            <el-table v-adjust-table
              size="mini"
              style="width: 100%"
              v-loading="loading"
              border
              :data="tableData"
              row-key="id"
              ref="dataTable"
              stripe
              highlight-current-row
              @row-click="handleClickRow"
              @selection-change="handleSelectionChange"
              :height="
                showSearch ? 'calc(100vh - 330px)' : 'calc(100vh - 270px)'
              "
            >
              <el-table-column type="selection" width="50" align="center"/>
              <el-table-column
                label="序号"
                align="center"
                type="index"
                width="50"
              />
              <template v-for="(column, index) in columns">
                <el-table-column
                  :label="column.label"
                  v-if="column.visible"
                  align="center"
                  :prop="column.field"
                  :width="column.width"
                  show-overflow-tooltip
                >
                  <template slot-scope="scope">
                    <dict-tag
                      v-if="column.dict"
                      :options="dict.type[column.dict]"
                      :value="scope.row[column.field]"
                    />
                    <template v-else-if="column.slots">
                      <RenderDom
                        :row="scope.row"
                        :index="index"
                        :render="column.render"
                      />
                    </template>
                    <span v-else-if="column.isTime">{{
                      parseTime(scope.row[column.field], "{y}-{m}-{d}")
                    }}</span>
                    <span v-else>{{ scope.row[column.field] }}</span>
                  </template>
                </el-table-column>
              </template>
              <el-table-column
                label="操作"
                fixed="right"
                align="center"
                width="250"
                class-name="small-padding fixed-width"
              >
                <template slot-scope="scope">
                  <el-button
                    type="text"
                    v-has-menu-permi="['calccheck:settlecalc:process']"
                    @click="handleOpenSubmit(scope.row)"
                    icon="el-icon-check"
                    >审核</el-button
                  >
                  <el-button
                    type="text"
                    @click="showInfo(scope.row)"
                    icon="el-icon-view"
                    >查看明细</el-button
                  >

                </template>
              </el-table-column>
            </el-table>
            <pagination
              v-show="total > 0"
              :total="total"
              :page.sync="queryParams.pageNum"
              :limit.sync="queryParams.pageSize"
              @pagination="handleQuery"
            />
          </div>
        </el-row>
      </el-col>
    </el-row>
    <el-dialog
      title="审核意见"
      :visible.sync="openOperateInfo"
      width="80%"
      destroy-on-close
      v-if="openOperateInfo"
    >
      <operateInfo
        @close="modelClose"
        :businessKey="row.id"
        :getNodeInfo="getNodeInfo"
      ></operateInfo>
    </el-dialog>
    <el-dialog
      title="明细查看"
      append-to-body
      modal-append-to-body
      :visible.sync="infoDialog"
      width="80%"
      v-if="infoDialog"
    >
      <info @close="modelClose" :maiSecId="maiSecId" :rowData="rowData"></info>
    </el-dialog>
    <el-dialog
      :title="drawerTitle"
      destroy-on-close
      :visible.sync="drawer"
      :close-on-click-modal="false"
      size="50%"
    >
      <detail @close="handleCloseDetail" :row-data="rowData"></detail>
    </el-dialog>
    <el-dialog
      title="附件列表"
      :visible.sync="openFile"
      width="500px"
      v-if="openFile"
    >
      <file-upload
        @close="modelClose"
        v-model="disFilePath"
        :forView="true"
      ></file-upload>
    </el-dialog>
    <IFramePreview ref="iframeRef" :srcdoc="preview.html" :down-url="preview.url" :file-name="preview.fileName"></IFramePreview>

    <el-dialog
      title="确认"
      :visible.sync="confirmDialog"
      width="30%"
      v-if="confirmDialog"
    >
      <el-form
        v-model="formData"
        size="small"
        :inline="true"
        label-width="120px"
      >
        <el-form-item label="核定计量金额">
          <el-input v-model="formData.calcFund" style="width: 230px" readonly />
        </el-form-item>
        <el-form-item label="监理费">
          <el-input v-model="formData.supFund" style="width: 230px" readonly />
        </el-form-item>
        <el-form-item label="操作意见">
          <el-input
            v-model="formData.comment"
            type="textarea"
            style="width: 230px"
          />
        </el-form-item>
      </el-form>
      <div style="text-align: right">
        <el-button
          v-if="formData.status < 8"
          type="danger"
          @click="handleSubmit(false, 1)"
          >驳回至申请</el-button
        >
        <el-button
          v-if="formData.status < 8"
          type="danger"
          @click="handleSubmit(false, 2)"
          >驳回至上一步</el-button
        >
        <el-button size="mini" @click="confirmDialog = false">取 消</el-button>
        <el-button size="mini" type="primary" @click="handleSubmit(true)"
          >确 定</el-button
        >
      </div>
    </el-dialog>
  </div>
</template>

<script>
import Detail from "./detail.vue";
import EventInfo from "../../component/eventTreeInfo.vue";
import selectTree from "@/components/DeptTmpl/selectTree.vue";
import MaintenanceTree from "@/components/MaintenanceTree/index.vue";
import operateInfo from "@/views/dailyMaintenance/component/operateInfo.vue";
import info from "./info.vue";
import {
  getNodeInfo,
  processSettle,
  getNumberList,
  getPreviewInfo,
  downloadReport,
} from "@/api/regularTesting/metering/settlementApplication";
import { listSettlecalc } from "@/api/regularTesting/metering/settlementExamine";
import RoadSection from "@/views/baseData/components/roadSection/index.vue";
import IFramePreview from "@/components/IFramePreview/index.vue";
import axios from 'axios'
import { saveAs } from 'file-saver'
import {regenerateReport} from "@/api/dailyMaintenance/metering/addPrice";
import { getNumbersByYear } from '@/api/dailyMaintenance/metering/settlementApplication'
export default {
  name: 'SettlementExamine',
  components: {
    MaintenanceTree,
    IFramePreview,
    RoadSection,
    operateInfo,
    selectTree,
    EventInfo,
    Detail,
    info,
    RenderDom: {
      functional: true,
      props: {
        row: Object,
        index: Number,
        render: Function,
      },
      render(createElement, ctx) {
        const { row, index } = ctx.props;
        return ctx.props.render(row, index);
      },
    },
  },
  props: [],
  dicts: ["testing_settlement_status"],
  data() {
    return {
      leftTotal: 1,
      showSearch: false,
      queryParams: {
        pageNum: 1,
        pageSize: 10,
      },
      total: 0,
      loading: false,
      settleId: "",
      maiSecId: "",
      preview: {
        html: '',
        url: '',
        fileName: ''
      },
      columns: [
        {
          key: 0,
          width: 100,
          field: "status",
          label: `状态`,
          visible: true,
          dict: "testing_settlement_status",
        },
        {
          key: 1,
          width: 80,
          field: "numberName",
          label: `期数`,
          visible: true,
        },
        {
          key: 2,
          width: 110,
          field: "name",
          label: `结算计量名称`,
          visible: true,
        },
        {
          key: 3,
          width: 110,
          field: "code",
          label: `结算计量编号`,
          visible: true,
        },
        {
          key: 4,
          width: 100,
          field: "domainName",
          label: `管养单位`,
          visible: true,
        },
        {
          key: 5,
          width: 110,
          field: "calcDomainName",
          label: `申请计量单位`,
          visible: true,
        },
        {
          key: 6,
          width: 100,
          field: "maiSecId",
          label: `路段名称`,
          visible: true,
        },
        {
          key: 7,
          width: 100,
          field: "conName",
          label: `合同名称`,
          visible: true,
        },
        {
          key: 7,
          width: 110,
          field: "calcFund",
          label: `核定计量金额`,
          visible: true,
        },
        {
          key: 8,
          width: 100,
          field: "sumFund",
          label: `基本费用`,
          visible: true,
        },
        {
          key: 9,
          width: 100,
          field: "productionFund",
          label: `安全生产费`,
          visible: true,
        },
        {
          key: 10,
          width: 100,
          field: "guaranteeFund",
          label: `安全保通费`,
          visible: true,
        },
        {
          key: 11,
          width: 100,
          field: "adjustFund",
          label: `调整费用`,
          visible: true,
        },
        {
          key: 12,
          width: 80,
          field: "supFund",
          label: `监理费`,
          visible: true,
        },
        {
          key: 13,
          width: 110,
          field: "materialAdjustFund",
          label: `材料调差费用`,
          visible: true,
        },
        {
          key: 14,
          width: 100,
          field: "eductionFund",
          label: `扣款金额`,
          visible: true,
        },
        {
          key: 15,
          width: 100,
          field: "preCalcName",
          label: `上一期名称`,
          visible: true,
        },
        {
          key: 16,
          width: 100,
          field: "preCalcCode",
          label: `上一期编号`,
          visible: true,
        },
        {
          key: 17,
          width: 100,
          field: "calcDate",
          label: `计量日期`,
          visible: true,
        },
        {
          key: 18,
          width: 100,
          field: "fileId",
          label: `附件`,
          visible: true,
          slots: true,
          render: (row, index) => {
            return (
              <el-button
                size="mini"
                disabled={!row.fileId}
                type="text"
                onClick={(e) => this.handleOpenFile(e, row)}
              >
                查看
              </el-button>
            );
          },
        },
      ],
      tableData: [],
      rowData: {},
      drawerTitle: "结算计量申请",
      drawer: false,
      openFile: false,
      openOperateInfo: false,
      infoDialog: false,
      disFilePath: "",
      // 左侧组织树
      realNav: true,
      confirmDialog: false,
      formData: {},
      row: {},
      numberList: [],
      ids: []
    };
  },
  computed: {},
  watch: {},
  created() {
    this.getNumberList()
    this.handleQuery();
  },
  mounted() {},
  methods: {
    getNodeInfo,
    getNumberList() {
      const parmas = {
        year: this.queryParams.year || null,
        pageNum: 1,
        pageSize: 1000,
      };
      this.numberList = []
      getNumbersByYear(parmas).then((res) => {
        res.rows.forEach((item) => {
          this.numberList.push({
            label: item.name,
            value: item.id,
          });
        });
      });
    },
    handleNodeClick(e) {
      this.queryParams.domainId = e.domainId || "";
      this.queryParams.maiSecId = e.maiSecId || "";
      this.handleQuery();
    },
    handleQuery() {
      this.loading = true;
      listSettlecalc(this.queryParams).then((res) => {
        this.loading = false;
        this.tableData = res.rows;
        this.total = res.total;
      });
    },
    resetQuery() {
      this.queryParams = {
        pageNum: 1,
        pageSize: 50,
      };
      this.handleQuery()
    },
    handleCloseDetail() {
      this.rowData = {};
      this.drawer = false;
      this.handleQuery();
    },
    handleClickRow(row) {
      this.row = row;
    },
    handleOpenSubmit(row) {
      this.formData = {
        businessKey: row.id,
        taskId: row.taskId,
        supFund: row.supFund,
        status: row.status,
        calcFund: row.calcFund,
      };
      this.confirmDialog = true;
    },
    // 提交
    handleSubmit(approved, reject) {
      if (!approved && !this.formData.comment) {
        this.$modal.msgWarning('请填写操作意见')
        return
      }
      this.formData.approved = approved;
      this.formData.reject = reject;
      processSettle(this.formData).then((res) => {
        this.$modal.msgSuccess("提交成功");
        this.confirmDialog = false;
        this.handleQuery();
      });
    },
    handleOpenOperate() {
      if (!this.row.id) {
        this.$modal.msgError("请选择一条数据");
        return;
      }
      this.openOperateInfo = true;
    },
    handleOpenFile(e, row) {
      this.disFilePath = "";
      this.disFilePath = row.fileId;
      this.openFile = true;
    },
    showInfo(row) {
      this.infoDialog = true;
      this.rowData = row;
    },

    // 报表预览
    handlePreview() {
      if (!this.row.id) {
        this.$modal.msgError("请选择一条数据");
        return;
      }
      const params = {id: this.row.id}
      this.loading = true
      getPreviewInfo(params).then(res => {
        this.loading = false
        this.preview.html = res.data.html
        this.preview.url = res.data.downUrl
        this.preview.fileName = res.data.fileName
        this.$refs.iframeRef.visible = true
      })
    },
    // 报表下载
    handleDownload() {
      if (!this.row.id) {
        this.$modal.msgError("请选择一条数据");
        return;
      }
      const params = {id: this.row.id}
      this.loading = true
      downloadReport(params).then(res=> {
        if (res.code == 200){
          if (!res.data) {
            this.$message.warning(res.msg)
            this.loading = false
            return
          }
          if (res.data.fileName.endsWith('.zip')) {
            let link = document.createElement('a')
            link.download = res.data.fileName
            link.style.display = 'none'
            link.href = res.data.downUrl
            document.body.appendChild(link)
            link.click()
            document.body.removeChild(link)
            this.loading = false
          } else {
            axios({
              method: "get",
              responseType: 'arraybuffer',
              url: res.data.downUrl,
              headers: {}
            }).then((res) => {
              const arrayBuffer = res.data;
              // 创建一个Blob对象
              const blob = new Blob([arrayBuffer], {type: 'application/zip'}); // 对于.xls文件
              saveAs(blob, res.data.fileName)
            }).finally(() => {
              this.loading = false
            })
          }
        }
      })
    },
    // 导出清单按钮
    exportList() {
      this.download(
        "manager/calc/check/settlecalc/pending/export",
        { ...this.queryParams },
        `settlecalc_${new Date().getTime()}.xlsx`,
        {
          headers: { "Content-Type": "application/json;" },
          parameterType: "body",
        }
      );
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection
    },
    handleRegenerate() {
      if (this.ids.length == 0) {
        this.$modal.msgError("请勾选至少一条数据")
        return
      }
      const params = {
        idList: this.ids.map(item => item.id),
        type: 6
      }
      regenerateReport(params).then(res => {
        this.$modal.msgSuccess("操作成功")
      })
    },
    modelClose() {
      this.drawer = false;
      this.openFile = false;
      this.openOperateInfo = false;
      this.infoDialog = false;
    },
  },
};
</script>

<style lang="scss" scoped>
.rightIcon {
  border: 1px solid #dcdfe6;
  border-radius: 8px;
  width: 16px;
  height: 50px;
  line-height: 50px;
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
  position: absolute;
  left: -10px;
  top: 280px;
  z-index: 10;
  background: white;
}

.rightIcon:hover {
  background-color: #dcdfe6;
}

.left-total {
  font-size: 13px;
  line-height: 28px;
  color: #606266;
  position: absolute;
  bottom: 10px;
  right: 10px;
}
</style>
<style lang="scss" scoped>
@import "@/assets/styles/business.scss";
</style>
