<template>
  <div>
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button icon="el-icon-plus" size="mini" type="primary" @click="handleAdd">
          新增
        </el-button>
        <el-button icon="el-icon-delete" size="mini" type="danger" @click="handleDelete">
          删除
        </el-button>
      </el-col>
    </el-row>
    <!-- 数据表格开始 -->
    <div class="tableDiv">
      <el-table
        v-adjust-table
        v-loading="loading"
        :data="dataList"
        :height="'calc(100vh - 260px)'"
        border
        size="mini"
        style="width: 100%"
        @selection-change="handleSelectionChange"
      >
        <el-table-column align="center" fixed="left" type="selection" width="55" :selectable="checkSelectable"></el-table-column>
        <el-table-column
          align="center"
          fixed
          label="序号"
          type="index"
          width="100"
        ></el-table-column>
        <template v-for="(column, index) in columns">
          <el-table-column
            v-if="column.visible"
            :key="index"
            :label="column.label"
            :prop="column.field"
            align="center"
            show-overflow-tooltip
          >
            <template slot-scope="scope">
              <dict-tag
                v-if="column.dict"
                :options="dict.type[column.dict]"
                :value="scope.row[column.field]"
              />
              <template v-else-if="column.slots">
                <RenderDom :index="index" :render="column.render" :row="scope.row" />
              </template>
              <span v-else-if="column.isTime">
								{{ parseTime(scope.row[column.field], '{y}-{m}-{d}') }}
							</span>
              <span v-else>{{ scope.row[column.field] }}</span>
            </template>
          </el-table-column>
        </template>
        <el-table-column align="center" fixed="right" label="操作" width="100">
          <template slot-scope="scope">
            <el-button icon="el-icon-edit" size="mini" type="text" :disabled="scope.row.isCustom == 0" @click="handleUpdate(scope.row)">修改
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      <pagination
        v-show="total > 0"
        :limit.sync="queryParams.pageSize"
        :page.sync="queryParams.pageNum"
        :total="total"
        @pagination="handleQuery"
      />
    </div>
    <!-- 数据表格结束 -->
    <el-dialog
      :title="dialogTitle"
      :visible.sync="dialogVisible"
      append-to-body
      destroy-on-close
      width="50%"
    >
      <el-form ref="elForm" :model="formData" :rules="rules" label-width="120px" size="medium">
        <el-row>
          <el-col :span="12">
            <el-form-item label="模式名称" prop="modeName">
              <el-input v-model="formData.modeName" placeholder="请输入模式名称" clearable />
            </el-form-item>
          </el-col>
<!--          <el-col :span="12">-->
<!--            &lt;!&ndash; 模式编码 &ndash;&gt;-->
<!--            <el-form-item label="模式编码" prop="modeCode">-->
<!--              <el-input v-model="formData.modeCode" placeholder="请输入模式编码" clearable />-->
<!--            </el-form-item>-->
<!--          </el-col>-->
          <el-col :span="12">
            <!-- 爆闪灯 -->
            <el-form-item label="爆闪灯" prop="rbSw">
              <dict-select
                type="flash_open_type"
                style="width: 100%"
                placeholder="请选择爆闪灯"
                v-model="formData.rbSw"
              ></dict-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <!-- 语音 -->
            <el-form-item label="语音" prop="voiceSw">
              <dict-select
                type="flash_open_type"
                style="width: 100%"
                placeholder="请选择语音"
                v-model="formData.voiceSw"
              ></dict-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <!-- 语音曲目 -->
            <el-form-item label="语音曲目" prop="voiceTrack">
              <dict-select
                type="voice_track"
                style="width: 100%"
                placeholder="请选择语音曲目"
                v-model="formData.voiceTrack"
              ></dict-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <!-- 播放内容 -->
            <el-form-item label="播放内容" prop="voiceContent">
              <el-input v-model="formData.voiceContent" placeholder="请输入播放内容" clearable />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <!-- 文字 -->
            <el-form-item label="文字" prop="textSw">
              <dict-select
                type="flash_text"
                style="width: 100%"
                placeholder="请选择文字"
                v-model="formData.textSw"
              ></dict-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <!-- 文字颜色1 -->
            <el-form-item label="文字颜色1" prop="textColorA">
              <dict-select
                type="flash_text_color"
                style="width: 100%"
                placeholder="请选择文字颜色1"
                v-model="formData.textColorA"
              ></dict-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <!-- 文字颜色2 -->
            <el-form-item label="文字颜色2" prop="textColorB">
              <dict-select
                type="flash_text_color"
                style="width: 100%"
                placeholder="请选择文字颜色2"
                v-model="formData.textColorB"
              ></dict-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <!-- 文字内容1 -->
            <el-form-item label="文字内容1" prop="textA">
              <el-input
                v-model="formData.textA"
                placeholder="请输入文字内容1"
                clearable
                :maxlength="1"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <!-- 文字内容2 -->
            <el-form-item label="文字内容2" prop="textB">
              <el-input
                v-model="formData.textB"
                placeholder="请输入文字内容2"
                clearable
                :maxlength="1"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <div style="text-align: right; margin-top: 20px">
          <el-button size="mini" type="primary" @click="handleSave">保存</el-button>
          <el-button size="mini" @click="dialogVisible = false">退出</el-button>
        </div>
      </el-form>
    </el-dialog>
  </div>
</template>

<script>
import {
  deleteZjgtConfig,
  getZjgtConfigPage,
  saveZjgtConfig,
  updateZjgtConfig,
} from '@/api/jgjc/flashingModeConfig'

export default {
  dicts: ['flash_open_type', 'voice_track', 'flash_text', 'flash_text_color'],
  data() {
    return {
      // 遮罩层
      loading: false,
      dataList: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
      },
      // 列信息
      columns: [
        { key: 0, width: 100, field: 'modeName', label: `模式名称`, visible: true },
        { key: 1, width: 100, field: 'modeCode', label: `模式编码`, visible: true },
        {
          key: 2,
          width: 100,
          field: 'rbSw',
          label: `爆闪灯`,
          visible: true,
          dict: 'flash_open_type',
        },
        {
          key: 3,
          width: 100,
          field: 'voiceSw',
          label: `语音`,
          visible: true,
          dict: 'flash_open_type',
        },
        {
          key: 4,
          width: 100,
          field: 'voiceTrack',
          label: `语音曲目`,
          visible: true,
          dict: 'voice_track',
        },
        { key: 5, width: 100, field: 'voiceContent', label: `播放内容`, visible: true },
        { key: 6, width: 100, field: 'textSw', label: `文字`, visible: true, dict: 'flash_text' },
        {
          key: 7,
          width: 100,
          field: 'textColorA',
          label: `文字颜色1`,
          visible: true,
          dict: 'flash_text_color',
        },
        {
          key: 8,
          width: 100,
          field: 'textColorB',
          label: `文字颜色2`,
          visible: true,
          dict: 'flash_text_color',
        },
        { key: 9, width: 100, field: 'textA', label: `文字内容1`, visible: true },
        { key: 10, width: 100, field: 'textB', label: `文字内容2`, visible: true },
      ],
      total: 0,
      ids: [],
      dialogVisible: false,
      dialogTitle: '新增',
      formData: {},
    }
  },
  computed: {
    rules() {
      return {
        modeName: [{ required: true, message: '模式名称不能为空', trigger: 'blur' }],
        rbSw: [{ required: true, message: '爆闪灯不能为空', trigger: 'change' }],
        voiceSw: [{ required: true, message: '语音不能为空', trigger: 'change' }],
        voiceTrack: [{ required: true, message: '语音曲目不能为空', trigger: 'change' }],
        voiceContent: [
          {
            required: this.isVoiceContentRequired, // 动态校验，通过方法判断
            message: '播放内容不能为空',
            trigger: 'blur',
          },
        ],
        textColorA: [
          {
            required: this.isTextColorRequired, // 动态校验，通过方法判断
            message: '文字颜色1不能为空',
            trigger: 'change',
          },
        ],
        textColorB: [
          {
            required: this.isTextColorRequired,
            message: '文字颜色2不能为空',
            trigger: 'change',
          },
        ],
        textA: [
          {
            required: this.isTextContentRequired,
            message: '文字内容1不能为空',
            trigger: 'blur',
          },
        ],
        textB: [
          {
            required: this.isTextContentRequired,
            message: '文字内容2不能为空',
            trigger: 'blur',
          },
        ],
      }
    },
    isVoiceContentRequired() {
      return this.formData.voiceTrack == 0
    },
    isTextColorRequired() {
      return parseInt(this.formData.textSw) > 0
    },
    isTextContentRequired() {
      return parseInt(this.formData.textSw) > 0
    },
  },
  mounted() {
    this.handleQuery()
  },
  methods: {
    handleQuery() {
      this.loading = true
      getZjgtConfigPage(this.queryParams)
        .then((res) => {
          this.dataList = res.rows
          this.total = res.total
          this.loading = false
        })
        .catch((err) => {
          this.loading = false
          console.error(err)
        })
    },
    handleSelectionChange(e) {
      this.ids = e.map((item) => item.id)
    },

    handleDelete() {
      if (this.ids.length === 0) {
        this.$message({
          message: '请选择需要删除的数据',
          type: 'warning',
        })
        return
      }
      this.$confirm('是否确认删除选中数据?', '警告', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }).then(() => {
        deleteZjgtConfig({ ids: this.ids }).then((res) => {
          this.$message({
            message: '删除成功',
            type: 'success',
          })
          this.handleQuery()
        })
      })
    },
    handleAdd() {
      this.dialogVisible = true
      this.dialogTitle = '新增'
      this.formData = {}
    },
    handleUpdate(e) {
      this.dialogVisible = true
      this.dialogTitle = '修改'
      this.formData = JSON.parse(JSON.stringify(e))
    },
    handleSave() {
      this.$refs.elForm.validate((valid) => {
        if (!valid) return
        if (this.dialogTitle === '新增') {
          saveZjgtConfig(this.formData).then((res) => {
            this.$message({
              message: '新增成功',
              type: 'success',
            })
            this.handleQuery()
            this.closeDialog()
          })
        } else {
          updateZjgtConfig(this.formData).then((res) => {
            this.$message({
              message: '修改成功',
              type: 'success',
            })
            this.handleQuery()
            this.closeDialog()
          })
        }
      })
    },
    checkSelectable(row) {
      return row.isCustom != 0
    },
    closeDialog() {
      this.dialogVisible = false
      this.$refs.elForm.resetFields()
    },
  },
}
</script>

<style lang="scss" scoped></style>
