<template>
  <div class="mar-container">
    <div id="map" ref="mapRef" class="map-view"></div>

    <!-- 经纬度、缩放 -->
    <div class="base-info" :style="{ fontSize: isBig ? '0.36vw' : '0.8vw' }">
      <div ref="mapContral" class="mouse-position"></div>
      <div class="zoom-class">图层：{{ currentZoom }}</div>
    </div>

    <!-- 提示信息 -->
    <div id="popup" class="ol-popup" ref="containerRef">
      <span id="popup-closer" class="ol-popup-closer" ref="closerRef" @click="handleClose"></span>
      <div id="popup-content" ref="popupRef"></div>
    </div>
  </div>
</template>

<script>
import "ol/ol.css";
import { Map, View, Feature } from "ol";
import TileLayer from "ol/layer/Tile";
import XYZ from "ol/source/XYZ";
import {
  defaults as defaultsControl,
  MousePosition,
  ScaleLine,
} from "ol/control";
import { createStringXY } from "ol/coordinate";
import { fromLonLat, toLonLat } from "ol/proj"; // 导入方法
import { WKT } from "ol/format";
import VectorLayer from "ol/layer/Vector";
import VectorSource from "ol/source/Vector";
import { Fill, Icon, Style, Text, Stroke } from "ol/style";
import {
  MultiPolygon as GeomMultiPolygon,
  LineString as GeomLineString,
} from "ol/geom";
import { fromExtent } from "ol/geom/Polygon";
import { toFeature } from "ol/render/Feature";
import { defaults as defaultInteractions } from 'ol/interaction';
import cache from "@/plugins/cache";
// api
import { getShapeList } from "@/api/oneMap/deptInfo";
import { mapState, mapActions, mapMutations } from "vuex";
import { Point } from 'ol/geom';
import {
  addMapMask,
  addWidthFeature,
  removeAllLayer,
  addVectorTile,
  removeLayer,
  getLineConfig,
  onMatching,
  getLayersByName,
  featureToWkt,
  wktToFeature,
  getAllLayers,
  addClickFeature,
  buildLayer,
} from "./common/mapFun";
import bridgeIcon from '@/assets/map/bridge-icon.png'
import locationIcon from '@/assets/map/address.png'
import { isBigScreen } from "@/views/cockpit/util/utils";

var key = "cde0b56cf882626889981701109a7536";
//实例化比例尺控件（ScaleLine）
var scaleLineControl = new ScaleLine({
  className: isBigScreen() ? "custom-scale-line" : "custom-scale-line-small", // 自定义样式类名
  //设置比例尺单位，degrees、imperial、us、nautical、metric（度量单位）
  units: "metric",
});

export default {
  props: {
    forPosition: {
      type: Boolean,
      default: false,
    },
    padding: {
      type: Array,
      default: () => null,
    }
  },
  data() {
    return {
      shapeList: [],
      vectorsLayer: null,
      currentZoom: 7,
      rangeData: [], // 接收范围线数据
      isBig: isBigScreen(),
      isAdmin: this.$store.getters.roles.includes("admin"),
    };
  },
  created() {
    let availH = window.innerHeight || window.screen.height || window.screen.availHeight || 1080;
    // 大屏 1100 以上
    if (availH > 1100) {
      this.currentZoom = 8.5;
    }
    this.getRangeShape();
  },
  async mounted() {
    await this.initMap();
    getLineConfig(this.authDeptIds);
    // 加载大桥
    // this.loadBridge();
  },
  unmounted() {
    window.mapLayer = null;
    window.layers = [];
  },
  computed: {
    ...mapState({
      layerList: (state) => state.map.layerList,
      deptId: (state) => state.user.deptId,
      showLevel: (state) => state.map.showLevel,
      menuShowType: (state) => state.map.menuShowType,
      legendList: (state) => state.map.legendList,
      authDeptIds: (state) => state.map.authDeptIds,
      lineClick: (state) => state.map.lineClick,
      isRecord: (state) => state.map.isRecord,
      recordList: (state) => state.map.recordList,
      isLocal: (state) => state.map.isLocal,
    }),
  },
  methods: {
    ...mapActions({
      getMapZoom: "map/getMapZoom",
      getTreeList: "map/getTreeList",
      getTableShow: "map/getTableShow",
      getRecordList: "map/getRecordList",
    }),
    ...mapMutations({
      setRecordBool: "map/setRecordBool",
      setRecordIndex: "map/setRecordIndex",
    }),
    async initMap() {
      window.layers = [];
      if (this.layerList && this.layerList.length) {
        window.layers = buildLayer(this.layerList);
        // // 添加成 XYZ 类型
        // let xyzArr = this.layerList.filter(v=> v.serviceType === 'XYZ');
        // xyzArr.forEach((item, index) => {
        //   window.layers[index] = new TileLayer({
        //     name: item.layerName,
        //     zIndex: item.showIndex,
        //     source: new XYZ({
        //       url:
        //         item.layerUrl ||
        //         `http://t{0-7}.tianditu.gov.cn/DataServer?T=img_w&x={x}&y={y}&l={z}&tk=${key}`,
        //       attributions: item.layerName,
        //       wrapX: false, // 是否水平包裹
        //       crossOrigin: "anonymous", // 常见的设置为 'anonymous' 或 'use-credentials'
        //       maxZoom: 18,
        //     }),
        //     preload: Infinity,
        //     visible: item.layerUrl ? !!item.ifShow : true,
        //   });
        // });
        // // TMS
        // let tmsArr = this.layerList.filter(v=> v.serviceType === 'TMS');
        // // WMS
        // let wmsArr = this.layerList.filter(v=> v.serviceType === 'WMS');
      } else {
        let layer1 = new TileLayer({
          name: "天地图矢量图层",
          source: new XYZ({
            url: `http://t{0-7}.tianditu.gov.cn/DataServer?T=img_w&x={x}&y={y}&l={z}&tk=${key}`,
            attributions: "天地图的属性描述",
            wrapX: false,
            maxZoom: 18,
          }),
          preload: Infinity,
          visible: true,
        });
        let layer2 = new TileLayer({
          name: "天地图矢量图层注记",
          source: new XYZ({
            url: `http://t{0-7}.tianditu.gov.cn/DataServer?T=cta_w&x={x}&y={y}&l={z}&tk=${key}`,
            attributions: "天地图的属性描述",
            wrapX: false,
          }),
          preload: Infinity,
        });
        window.layers = [layer1, layer2];
      }
      let padding = this.padding || [50, 0, 0, this.isBig ? 1320 : this.isAdmin ? 660 : 350];
      window.mapLayer = new Map({
        target: "map",
        view: new View({
          projection: "EPSG:3857",
          //地图初始中心点
          center: fromLonLat([102.75530900000001, 24.95423900000002]),
          //地图初始显示级别
          minZoom: 6,
          zoom: this.currentZoom,
          maxZoom: 20,
          padding,
          rotation: 0,
          enableRotation: true,
        }),
        layers: window.layers,
        controls: defaultsControl({
          attribution: false,
          zoom: false,
          rotate: false,
        }).extend([scaleLineControl]),
        interactions: defaultInteractions({
          doubleClickZoom: false
        }),
      });
      window.mapLayer.getView().on("change:resolution", () => {
        const zoomLevel = Math.round(window.mapLayer.getView().getZoom());
        this.getMapZoom(zoomLevel);
        // let layer = getLayersByName('name','pbfLayer')
        // if (zoomLevel >= 19 && layer) {
        //   layer.set('declutter',false);
        //   layer.getSource().changed();
        //   layer.getSource().refresh();
        //   layer.changed();
        //   window.mapLayer.updateSize();
        // } else if(zoomLevel < 19 && zoomLevel > 6 && layer) {
        //   layer.set('declutter',true);
        //   layer.getSource().changed();
        //   layer.getSource().refresh();
        //   layer.changed();
        //   window.mapLayer.updateSize();
        // }
        this.currentZoom = zoomLevel;
        let mapEl = this.$refs.mapRef
        // 定义节流函数
        let gifUpdateTimer = null;
        const updateGifVisibility = () => {
          if (gifUpdateTimer) {
            clearTimeout(gifUpdateTimer);
          }
          gifUpdateTimer = setTimeout(() => {
            // 获取子 dom id 是 'gif-' 开头的元素
            const gifElements = mapEl ? mapEl.querySelectorAll('[id^="gif-"]') : [];
            if (gifElements) {
              if ((zoomLevel >= 9 && zoomLevel < 10) || (zoomLevel >= 10 && zoomLevel < 11)) {
                gifElements.forEach((element) => {
                  element.style.display = zoomLevel >= 10 ? 'block' : 'none';
                });
              }
            }
          }, 0);
        };
        updateGifVisibility();
      });

      // 鼠标坐标
      const mousePositionControl = new MousePosition({
        coordinateFormat: createStringXY(6),
        projection: "EPSG:4326",
        className: "custom-mouse-position",
        target: this.$refs.mapContral,
      });
      window.mapLayer.addControl(mousePositionControl);

      this.mouseEvent();
      this.moveEvent();
    },
    // 地图事件
    mouseEvent() {
      if (this.forPosition) return;
      // 加载点击事件
      window.mapLayer.on("singleclick", (evt) => {
        if (window.draw) return;
        // 获取点击的坐标
        const coordinate = evt.coordinate;
        // const pixel = window.mapLayer.getPixelFromCoordinate(coordinate);
        // var pixel = window.mapLayer.getEventPixel(evt.originalEvent);
        let feature = window.mapLayer.forEachFeatureAtPixel(
          evt.pixel,
          (feature) => feature,
          {
            layerFilter: (e) => {
              let layerName = this.isLocal ? ["maskLayer", "clickLayer", 'pbfLayer'] : !this.lineClick ? ["maskLayer", "路网信息", "treeHighwayLayer"] : ["maskLayer", "dataLayer", "clickLayer", 'pbfLayer'];
              return e.get("name") && !layerName.includes(e.get("name"));
            },
            hitTolerance: this.isBig ? 40 : 20, //像素容差率 默认0
          }
        );
        if (this.isLocal) {
          const geometry = feature.getGeometry();
          const featureType = geometry.getType();
          const properties = feature.getProperties();
          const lonLat = toLonLat(coordinate);
          // 路线添加 选中状态
          if (featureType == 'LineString' && feature) {
            let fea;
            if (feature instanceof Feature) {
              fea = feature;
            } else {
              fea = toFeature(feature, feature.getType());
            }
            addClickFeature(fea, properties, null, true, true, 'clickLayer', 1);
          }
          // 添加定位图标
          const clickFeature = new Feature(new Point(evt.coordinate));
          addClickFeature(clickFeature, properties, locationIcon, false, true, 'clickLayer', 2, '');
          let obj = {
            featureType,
            properties,
            lonLat,
            feature,
          }
          window.$Bus.$emit("onLocation", obj);
          return;
        }
        let features = feature.get("features");
        if (features && features.length) {
          feature = features[0];
        }
        if (feature) {
          if (feature instanceof Feature) {
            const properties = feature.getProperties();
            let data = {
              ...properties.data,
            };
            data.coordinate = coordinate || '';
            let deptId;
            if (data && data.sys_dept_id) {
              deptId = data.sys_dept_id;
            } else if (data && data.dept_id) {
              deptId = data.dept_id;
            }
            // 如果加载了切片数据等 并且 是点击了行政区面则不跳转管理处
            let isM = data.type && data.type == 'mgeom'
            let layer = getLayersByName('name', 'pbfLayer') || null;
            let allLayer = getAllLayers() || [];
            let layers = allLayer.find((l) => l.get('treeLayerName'));
            // 加载地图弹层
            // 如果有部门id 则显示部门区域
            if ((data && deptId) && !this.lineClick) {
              // 存储deptId 到 vuex中
              // this.$store.commit("map/setDeptId", { deptId });
              // 如果是点击了地图上的行政区域 并且加载了切片图层 则不跳转
              if ((layers || layer) && isM) return;
              removeAllLayer(window.mapLayer);
              removeLayer(window.mapLayer, "maskLayer", "name");
              removeLayer(window.mapLayer, "dataLayer");
              removeLayer(window.mapLayer, "dataLayer", "name");
              if (data.total) {
                addMapMask(window.mapLayer, [data]);
                addWidthFeature(
                  window.mapLayer,
                  [data],
                  "dataLayer",
                  null,
                  true,
                  this.$store.state.map.deptMinZoom,
                );
                let feature = wktToFeature(data.mgeom)
                window.mapLayer
                  .getView()
                  .fit(feature.getGeometry().getExtent(), {
                    duration: 500,
                  });
              } else {
                let deptArr = onMatching(deptId);
                if (deptArr) {
                  removeLayer(window.mapLayer, "dataLayer");
                  addMapMask(window.mapLayer, deptArr);
                  addWidthFeature(
                    window.mapLayer,
                    deptArr,
                    "dataLayer",
                    null,
                    true,
                    this.$store.state.map.deptMinZoom
                  );
                  let feature = wktToFeature(deptArr[0].mgeom)
                  if (deptArr && deptArr.length == 1) {
                    window.mapLayer
                      .getView()
                      .fit(feature.getGeometry().getExtent(), {
                        duration: 500,
                      });
                  }
                } else {
                  this.getRangeShape(deptId, true);
                }
              }
              this.$store.commit("SET_DEPTID", deptId);
              if (this.legendList && this.legendList.length) {
                // 重新加载图层
                // 移除矢量图层
                removeLayer(window.mapLayer, "pbfLayer");
                let obj = {
                  managementMaintenanceIds: deptId ? [deptId] : [],
                  id: this.legendList[0].id,
                  menuType: this.legendList[0].menuType,
                };
                addVectorTile(
                  window.mapLayer,
                  "name",
                  "pbfLayer",
                  JSON.stringify(obj),
                  null,
                  false,
                  9001
                );
              }
              // 根据部门Id从新获取树 数据
              let deptArr = [deptId];
              // 重新加载左侧树数据
              this.getTreeList({ deptIds: deptArr });
              // 触发左侧数据更新
              window.$Bus.$emit("mapClick", data);
              // 重新加载路线
              // removeLayer(window.mapLayer, 'pbfLayer', 'lineLayer')
              // 根据部门Id重新加载路线矢量数据
              // getLineConfig(deptArr);
              this.getTableShow(false);
            } else {
              // 返回用于详情
              this.$emit("click", data);
            }
          } else {
            // RenderFeature 转 Feature
            let fea = toFeature(feature, feature.getType());
            // 获取图斑信息
            let fesData = {
              ...fea.getProperties()
            };
            // 获取点击的坐标
            fesData.coordinate = coordinate;
            // 返回用于详情
            this.$emit("click", fesData);
          }
        }
      });
    },
    // 获取范围线
    getRangeShape(deptId = null, click = false) {
      let deptIds;
      let isAdmin = this.$store.getters.roles.includes("admin");
      if (deptId) {
        deptIds = [deptId || this.deptId];
      } else {
        let ids = isAdmin ? [] : deptId ? [deptId] : []
        deptIds = ids || this.authDeptIds;
      }
      getShapeList({ sysDeptIds: deptIds })
        .then((res) => {
          if (res.code == 200) {
            this.rangeData = res.data || [];
            // 保存范围数据
            if (!click) {
              cache.session.setJSON("rangeData", this.rangeData);
            }
            removeLayer(window.mapLayer, "dataLayer");
            addMapMask(window.mapLayer, res.data);
            addWidthFeature(
              window.mapLayer,
              res.data,
              "dataLayer",
              null,
              true,
              this.$store.state.map.deptMinZoom
            );
            // 地图移动到当前范围
            if (res.data && res.data.length == 1) {
              let feature = wktToFeature(res.data[0].mgeom)
              window.mapLayer.getView().fit(feature.getGeometry().getExtent(), {
                duration: 500,
              });
            }
            // 处理点击后移除不了图层数据问题
            if (click) {
              this.removeLayer("dataLayer");
              // removeLayer(window.mapLayer, 'dataLayer', 'name');
            }
          }
        });
    },

    // 地图移动事件
    moveEvent() {
      window.mapLayer.on("moveend", (evt) => {
        let view = evt.target.getView();
        let zoom = view.getZoom();
        // 获取当前窗口范围
        const currentExtent = view.calculateExtent(evt.target.getSize());
        // 获取当前中心点
        const currentCenter = view.getCenter();
        let arr = [{
          extent: currentExtent,
          center: currentCenter,
          zoom: zoom,
        }]
        // 把地图移动范围数据保存到状态管理中
        if (this.isRecord) {
          this.getRecordList(arr);
          // 如果地图记录更新则更新索引为最后一个
          this.setRecordIndex({ index: this.recordList.length - 1 });
        }
        // 重新设置可记录地图移动记录
        setTimeout(() => {
          this.setRecordBool({ bool: true });
        }, 300);
      });
    },

    removeLayer(layerName = "shapeLayer") {
      let allLayer = window.mapLayer?.getLayers()?.getArray();
      if (!allLayer) return;
      let layer = allLayer.find((l) => l.get("name") == layerName);
      if (layer) {
        window.mapLayer.removeLayer(layer);
      }
    },
    handleClose() {
      // containerRef.value.style.display = 'none';
      // // 移除选中
      // this.removeLayer(mapLayer.value, 'clickLayer');
      // this.removeLayer(mapLayer.value, 'shapeLayer');
      // emits('removeSelect');
      return;
    },
    // 加载大桥
    loadBridge() {
      let bridgeLonLat = [98.67289, 24.838556];
      let bridgeFeature = new Feature({
        geometry: new Point(bridgeLonLat),
      });
      // 转化为wkt
      let shape = featureToWkt(bridgeFeature)
      let arr = [{
        shape,
        type: 'bridge',
        typeName: '龙江大桥'
      }]
      // 添加矢量图层
      removeLayer(window.mapLayer, 'bridgeLayer');
      addWidthFeature(window.mapLayer, arr, 'bridgeLayer', bridgeIcon, false)
    },
  },
};
</script>

<style lang="scss" scoped>
@import "@/assets/styles/utils.scss";

.mar-container {
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;

  .map-view {
    width: 100%;
    height: 100%;
    cursor: pointer;
  }

  .base-info {
    position: absolute;
    bottom: 0;
    right: vwpx(20px);
    color: #ffffff;

    display: flex;
    align-items: center;

    .mouse-position {
      margin-right: vwpx(10px);
    }

    .zoom-class {
      margin-left: vwpx(10px);
      margin-bottom: vwpx(10px);
    }
  }
}
</style>

<style>
.custom-scale-line {
  border-radius: 4px;
  padding: 2px;
  bottom: 5px;
  right: 6vw;
  z-index: 2;
  position: absolute;
}

.custom-scale-line-small {
  border-radius: 4px;
  padding: 2px;
  bottom: 5px;
  right: 250px;
  z-index: 2;
  position: absolute;
}

.custom-scale-line-inner {
  border: 1px solid #fff;
  border-top: none;
  color: #fff;
  font-size: 0.32vw;
  text-align: center;
  margin: 1px 2px;
  will-change: contents, width;
  transition: all 0.25s;
  border-radius: 0;
  font-family: Source Han Sans CN;
  font-weight: bold;
}

.custom-scale-line-small-inner {
  border: 1px solid #fff;
  border-top: none;
  color: #fff;
  font-size: 12px;
  text-align: center;
  margin: 1px 2px;
  will-change: contents, width;
  transition: all 0.25s;
  border-radius: 0;
  font-family: Source Han Sans CN;
  font-weight: bold;
}

/**
* 提示框的样式信息
*/
.tooltip {
  position: relative;
  background: rgba(0, 0, 0, 0.5);
  border-radius: 4px;
  color: white;
  padding: 4px 8px;
  opacity: 0.7;
  white-space: nowrap;
}

.tooltip-measure {
  opacity: 1;
  font-weight: bold;
}

.tooltip-static {
  background-color: #ffcc33;
  color: black;
  border: 1px solid white;
}

.tooltip-measure:before,
.tooltip-static:before {
  border-top: 6px solid rgba(0, 0, 0, 0.5);
  border-right: 6px solid transparent;
  border-left: 6px solid transparent;
  content: "";
  position: absolute;
  bottom: -6px;
  margin-left: -7px;
  left: 50%;
}

.tooltip-static:before {
  border-top-color: #ffcc33;
}
</style>
