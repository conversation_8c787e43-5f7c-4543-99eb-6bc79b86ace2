<template>
  <div class="app-container">
    <el-row :gutter="20">
      <!--筛选区开始-->
      <el-col :span="24" :xs="24">
        <el-row>
          <el-col :span="24">
            <el-form
              :model="queryParams"
              ref="queryForm"
              size="small"
              :inline="true"
              label-width="68px"
            >
              <el-form-item label="" prop="appid">
                <el-input
                  v-model="queryParams.appid"
                  placeholder="请输入应用ID"
                  clearable
                  prefix-icon="el-icon-user"
                  style="width: 240px"
                  @keyup.enter.native="handleQuery"
                />
              </el-form-item>
              <el-form-item label="" prop="name">
                <el-input
                  v-model="queryParams.name"
                  placeholder="请输入应用名称"
                  clearable
                  prefix-icon="el-icon-user"
                  style="width: 240px"
                  @keyup.enter.native="handleQuery"
                />
              </el-form-item>
              <el-form-item label="" prop="title">
                <el-input
                  v-model="queryParams.title"
                  placeholder="请输入更新标题"
                  clearable
                  prefix-icon="el-icon-user"
                  style="width: 240px"
                  @keyup.enter.native="handleQuery"
                />
              </el-form-item>
              <el-form-item label="" prop="url">
                <el-input
                  v-model="queryParams.url"
                  placeholder="请输入安装包下载地址"
                  clearable
                  prefix-icon="el-icon-user"
                  style="width: 240px"
                  @keyup.enter.native="handleQuery"
                />
              </el-form-item>
              <el-form-item label="" prop="platform">
                <el-input
                  v-model="queryParams.platform"
                  placeholder="请输入0：Android | 1：iOS"
                  clearable
                  prefix-icon="el-icon-user"
                  style="width: 240px"
                  @keyup.enter.native="handleQuery"
                />
              </el-form-item>
              <el-form-item label="" prop="version">
                <el-input
                  v-model="queryParams.version"
                  placeholder="请输入版本号"
                  clearable
                  prefix-icon="el-icon-user"
                  style="width: 240px"
                  @keyup.enter.native="handleQuery"
                />
              </el-form-item>
              <el-form-item label="" prop="stablePublish">
                <el-input
                  v-model="queryParams.stablePublish"
                  placeholder="请输入是否是稳定版"
                  clearable
                  prefix-icon="el-icon-user"
                  style="width: 240px"
                  @keyup.enter.native="handleQuery"
                />
              </el-form-item>
              <el-form-item label="" prop="isMandatory">
                <el-input
                  v-model="queryParams.isMandatory"
                  placeholder="请输入是否强制更新"
                  clearable
                  prefix-icon="el-icon-user"
                  style="width: 240px"
                  @keyup.enter.native="handleQuery"
                />
              </el-form-item>
              <el-form-item label="" prop="isSilently">
                <el-input
                  v-model="queryParams.isSilently"
                  placeholder="请输入是否静默更新"
                  clearable
                  prefix-icon="el-icon-user"
                  style="width: 240px"
                  @keyup.enter.native="handleQuery"
                />
              </el-form-item>
              <el-form-item label="" prop="createEnv">
                <el-input
                  v-model="queryParams.createEnv"
                  placeholder="请输入创建环境"
                  clearable
                  prefix-icon="el-icon-user"
                  style="width: 240px"
                  @keyup.enter.native="handleQuery"
                />
              </el-form-item>
              <el-form-item label="" prop="createDate">
                <el-date-picker
                  clearable
                  v-model="queryParams.createDate"
                  type="date"
                  value-format="yyyy-MM-dd"
                  placeholder="请选择创建日期"
                >
                </el-date-picker>
              </el-form-item>
              <el-form-item label="" prop="type">
                <el-select
                  v-model="queryParams.type"
                  placeholder="请选择0:native_app|1:wgt"
                  clearable
                >
                  <el-option
                    v-for="dict in dict.type.app_update_type"
                    :key="dict.value"
                    :label="dict.label"
                    :value="dict.value"
                  />
                </el-select>
              </el-form-item>
              <el-form-item label="" prop="minUniVersion">
                <el-input
                  v-model="queryParams.minUniVersion"
                  placeholder="请输入最低uni-app版本"
                  clearable
                  prefix-icon="el-icon-user"
                  style="width: 240px"
                  @keyup.enter.native="handleQuery"
                />
              </el-form-item>
              <el-form-item label="" prop="storeUrl">
                <el-input
                  v-model="queryParams.storeUrl"
                  placeholder="请输入商店链接"
                  clearable
                  prefix-icon="el-icon-user"
                  style="width: 240px"
                  @keyup.enter.native="handleQuery"
                />
              </el-form-item>
              <el-form-item>
                <el-button
                  type="primary"
                  icon="el-icon-search"
                  size="mini"
                  @click="handleQuery"
                  >搜索</el-button
                >
                <el-button
                  icon="el-icon-refresh"
                  size="mini"
                  @click="resetQuery"
                  >重置</el-button
                >
                <el-button
                  v-show="!showSearch"
                  @click="showSearch = true"
                  icon="el-icon-arrow-down"
                  circle
                ></el-button>
                <el-button
                  v-show="showSearch"
                  @click="showSearch = false"
                  icon="el-icon-arrow-up"
                  circle
                ></el-button>
              </el-form-item>
            </el-form>
            <!--默认折叠-->
          </el-col>

          <!--默认折叠 ,此处仅作为示例-->
          <el-col :span="24">
            <el-form
              :model="queryParams"
              ref="queryForm"
              size="small"
              :inline="true"
              v-show="showSearch"
              label-width="68px"
            >
              <el-form-item label="" prop="status">
                <el-select
                  v-model="queryParams.status"
                  placeholder="示例状态"
                  clearable
                  style="width: 240px"
                >
                  <el-option
                    v-for="dict in dictType"
                    :key="dict.value"
                    :label="dict.label"
                    :value="dict.value"
                  />
                </el-select>
              </el-form-item>
              <el-form-item label="" prop="status">
                <el-select
                  v-model="queryParams.status"
                  placeholder="示例状态"
                  clearable
                  style="width: 240px"
                >
                  <el-option
                    v-for="dict in dictType"
                    :key="dict.value"
                    :label="dict.label"
                    :value="dict.value"
                  />
                </el-select>
              </el-form-item>
            </el-form>
          </el-col>
        </el-row>
        <!--筛选区结束-->

        <!--操作按钮区开始-->
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button
              type="primary"
              icon="el-icon-plus"
              size="mini"
              @click="handleAdd"
              v-hasPermi="['system:updates:add']"
              >新增</el-button
            >
          </el-col>
          <el-col :span="1.5">
            <el-button
              type="success"
              icon="el-icon-edit"
              size="mini"
              :disabled="single"
              @click="handleUpdate"
              v-hasPermi="['system:updates:edit']"
              >修改</el-button
            >
          </el-col>
          <el-col :span="1.5">
            <el-button
              type="danger"
              icon="el-icon-delete"
              size="mini"
              :disabled="multiple"
              @click="handleDelete"
              v-hasPermi="['system:updates:remove']"
              >删除</el-button
            >
          </el-col>
          <el-col :span="1.5">
            <el-button
              type="info"
              plain
              icon="el-icon-upload2"
              size="mini"
              @click="handleImport"
              v-hasPermi="['system:updates:export']"
              >导入</el-button
            >
          </el-col>
          <el-col :span="1.5">
            <el-button
              type="warning"
              icon="el-icon-download"
              size="mini"
              @click="handleExport"
              v-hasPermi="['system:user:export']"
              >导出</el-button
            >
          </el-col>
          <right-toolbar
            :showSearch.sync="showSearch"
            @queryTable="getList"
            :columns="columns"
          ></right-toolbar>
        </el-row>
        <!--操作按钮区结束-->

        <!--数据表格开始-->
        <div class="tableDiv">
          <el-table
            v-adjust-table
            ref="table"
            size="mini"
            :height="showSearch ? 'calc(100vh - 320px)' : 'calc(100vh - 260px)'"
            style="width: 100%"
            v-loading="loading"
            border
            :data="updatesList"
            @selection-change="handleSelectionChange"
            :row-style="rowStyle"
            @row-click="handleRowClick"
          >
            <el-table-column type="selection" width="50" align="center" />
            <el-table-column fixed label="序号" type="index" width="50">
              <template v-slot="scope">
                {{ (scope.$index + (queryParams.pageNum - 1) * queryParams.pageSize)+1 }}
              </template>
            </el-table-column>
            <el-table-column label="应用ID" align="center" prop="appid" />
            <el-table-column label="应用名称" align="center" prop="name" />
            <el-table-column label="更新标题" align="center" prop="title" />
            <el-table-column label="更新内容" align="center" prop="contents" />
            <el-table-column label="安装包" align="center">
              <template slot-scope="scope">
                <el-button
                  type="text"
                  :disabled="!scope.row.url"
                  @click="openDownloadLink(scope.row.url, scope.row.version)"
                >
                  {{ scope.row.version }}
                </el-button>
              </template>
            </el-table-column>
            <el-table-column label="平台" align="center">
              <template slot-scope="scope">
                {{ getPlatformNames(scope.row.platform) }}
              </template>
            </el-table-column>
            <el-table-column
              label="uni-app平台"
              align="center"
              prop="uniPlatform"
            />
            <el-table-column label="是否是稳定版" align="center">
              <template slot-scope="scope">
                {{ scope.row.stablePublish ? '是' : '否' }}
              </template>
            </el-table-column>
            <el-table-column label="是否强制更新" align="center">
              <template slot-scope="scope">
                {{ scope.row.isMandatory ? '是' : '否' }}
              </template>
            </el-table-column>
            <el-table-column label="是否静默更新" align="center">
              <template slot-scope="scope">
                {{ scope.row.isSilently ? '是' : '否' }}
              </template>
            </el-table-column>
            <el-table-column label="创建环境" align="center" prop="createEnv" />
            <el-table-column
              label="创建日期"
              align="center"
              prop="createDate"
              width="180"
            >
              <template slot-scope="scope">
                <span>{{
                  parseTime(scope.row.createDate, '{y}-{m}-{d}')
                }}</span>
              </template>
            </el-table-column>
            <el-table-column label="消息" align="center" prop="message" />
            <el-table-column
              label="0:native_app|1:wgt"
              align="center"
              prop="type"
            >
              <template slot-scope="scope">
                <dict-tag
                  :options="dict.type.app_update_type"
                  :value="scope.row.type"
                />
              </template>
            </el-table-column>
            <el-table-column
              label="最低uni-app版本"
              align="center"
              prop="minUniVersion"
            />
            <el-table-column label="商店链接" align="center" prop="storeUrl" />
            <el-table-column
              label="操作"
              fixed="right"
              align="center"
              width="160"
              class-name="small-padding fixed-width"
            >
              <template slot-scope="scope" v-if="scope.row.userId !== 1">
                <el-button
                  size="mini"
                  type="text"
                  icon="el-icon-edit"
                  @click.stop="handleUpdate(scope.row)"
                  v-hasPermi="['system:updates:edit']"
                  >修改</el-button
                >
                <el-button
                  size="mini"
                  type="text"
                  icon="el-icon-delete"
                  @click.stop="handleDelete(scope.row)"
                  v-hasPermi="['system:updates:remove']"
                  >删除</el-button
                >
              </template>
            </el-table-column>
          </el-table>

          <pagination
            v-show="total > 0"
            :total="total"
            :page.sync="queryParams.pageNum"
            :limit.sync="queryParams.pageSize"
            @pagination="getList"
          />
        </div>
        <!--数据表格结束-->
      </el-col>
    </el-row>

    <!-- 添加或修改应用更新信息对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="600px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-col :span="12">
          <el-form-item label="应用ID" prop="appid">
            <el-input v-model="form.appid" placeholder="请输入应用ID" />
          </el-form-item>
        </el-col>

        <el-col :span="12">
          <el-form-item label="应用名称" prop="name">
            <el-input v-model="form.name" placeholder="请输入应用名称" />
          </el-form-item>
        </el-col>

        <el-col :span="12">
          <el-form-item label="更新标题" prop="title">
            <el-input v-model="form.title" placeholder="请输入更新标题" />
          </el-form-item>
        </el-col>

        <el-col :span="12">
          <el-form-item label="更新内容" prop="contents">
            <el-input
              v-model="form.contents"
              type="textarea"
              placeholder="请输入内容"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="安装包下载地址" prop="url">
            <FileUpload
              :owner-id="Date.now()"
              previewWidth="80%"
              ref=""
              :limit="1"
              :fileType="['apk','wgt']"
              is-show-tip
              is-static
              :storage-path="'AppUpdateFiles'"
              :success-hook="handleFileUpload"
            ></FileUpload>
          </el-form-item>
        </el-col>

        <el-col :span="12">
          <el-form-item label="平台" prop="platformList">
            <el-select
              v-model="form.platformList"
              multiple
              placeholder="请选择平台"
            >
              <el-option
                v-for="(label, value) in dictAppPlatform"
                :key="value"
                :label="label"
                :value="value"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-col>

        <el-col :span="12">
          <el-form-item label="版本号" prop="version">
            <el-input v-model="form.version" placeholder="请输入版本号" />
          </el-form-item>
        </el-col>

        <el-col :span="12">
          <el-form-item label="是否是稳定版" prop="stablePublish">
            <el-select v-model="form.stablePublish" placeholder="请选择">
              <el-option label="是" :value="1"></el-option>
              <el-option label="否" :value="0"></el-option>
            </el-select>
          </el-form-item>
        </el-col>

        <el-col :span="12">
          <el-form-item label="是否强制更新" prop="isMandatory">
            <el-select v-model="form.isMandatory" placeholder="请选择">
              <el-option label="是" :value="1"></el-option>
              <el-option label="否" :value="0"></el-option>
            </el-select>
          </el-form-item>
        </el-col>

        <el-col :span="12">
          <el-form-item label="是否静默更新" prop="isSilently">
            <el-select v-model="form.isSilently" placeholder="请选择">
              <el-option label="是" :value="1"></el-option>
              <el-option label="否" :value="0"></el-option>
            </el-select>
          </el-form-item>
        </el-col>

        <el-col :span="12">
          <el-form-item label="创建环境" prop="createEnv">
            <el-input v-model="form.createEnv" placeholder="请输入创建环境" />
          </el-form-item>
        </el-col>

        <el-col :span="12">
          <el-form-item label="创建日期" prop="createDate">
            <el-date-picker
              v-model="form.createDate"
              type="datetime"
              value-format="yyyy-MM-dd HH:mm:ss"
              placeholder="请选择创建日期"
            >
            </el-date-picker>
          </el-form-item>
        </el-col>

        <el-col :span="12">
          <el-form-item label="消息" prop="message">
            <el-input
              v-model="form.message"
              type="textarea"
              placeholder="请输入内容"
            />
          </el-form-item>
        </el-col>

        <el-col :span="12">
          <el-form-item label="0:native_app|1:wgt" prop="type">
            <el-select
              v-model="form.type"
              placeholder="请选择0:native_app|1:wgt"
            >
              <el-option
                v-for="dict in dict.type.app_update_type"
                :key="dict.value"
                :label="dict.label"
                :value="parseInt(dict.value)"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-col>

        <el-col :span="12">
          <el-form-item label="最低uni-app版本" prop="minUniVersion">
            <el-input
              v-model="form.minUniVersion"
              placeholder="请输入最低uni-app版本"
            />
          </el-form-item>
        </el-col>

        <el-col :span="12">
          <el-form-item label="商店链接" prop="storeUrl">
            <el-input v-model="form.storeUrl" placeholder="请输入商店链接" />
          </el-form-item>
        </el-col>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 用户导入对话框 -->
    <el-dialog
      :title="upload.title"
      :visible.sync="upload.open"
      width="400px"
      append-to-body
    >
      <el-upload
        ref="upload"
        :limit="1"
        accept=".xlsx, .xls"
        :headers="upload.headers"
        :action="upload.url + '?updateSupport=' + upload.updateSupport"
        :disabled="upload.isUploading"
        :on-progress="handleFileUploadProgress"
        :on-success="handleFileSuccess"
        :auto-upload="false"
        drag
      >
        <i class="el-icon-upload"></i>
        <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
        <div class="el-upload__tip text-center" slot="tip">
          <div class="el-upload__tip" slot="tip">
            <el-checkbox v-model="upload.updateSupport" />
            是否更新已经存在的用户数据
          </div>
          <span>仅允许导入xls、xlsx格式文件。</span>
          <el-link
            type="primary"
            :underline="false"
            style="font-size: 12px; vertical-align: baseline"
            @click="importTemplate"
            >下载模板</el-link
          >
        </div>
      </el-upload>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitFileForm">确 定</el-button>
        <el-button @click="upload.open = false">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  listUpdates,
  getUpdates,
  delUpdates,
  addUpdates,
  updateUpdates,
} from '@/api/system/updates';
import { findFiles } from '@/api/system/fileUpload';
import { getDicts } from '@/api/system/dict/data';
import { getToken } from '@/utils/auth';
import Treeselect from '@riophae/vue-treeselect';
import '@riophae/vue-treeselect/dist/vue-treeselect.css';

export default {
  name: 'Updates',
  dicts: ['app_update_type'],
  components: {},
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: false,
      dictType: [],
      dictAppPlatform: {},
      // 总条数
      total: 0,
      // 应用更新信息表格数据
      updatesList: null,
      // 弹出层标题
      title: '',
      // 部门树选项
      deptOptions: undefined,
      // 是否显示弹出层
      open: false,

      // 表单参数
      form: {},
      defaultProps: {
        children: 'children',
        label: 'label',
      },
      // 用户导入参数
      upload: {
        // 是否显示弹出层（用户导入）
        open: false,
        // 弹出层标题（用户导入）
        title: '',
        // 是否禁用上传
        isUploading: false,
        // 是否更新已经存在的用户数据
        updateSupport: 0,
        // 设置上传的请求头部
        headers: { Authorization: 'Bearer ' + getToken() },
        // 上传的地址
        url: process.env.VUE_APP_BASE_API + '/system/user/importData',
      },
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 50,
        appid: null,
        name: null,
        title: null,
        contents: null,
        url: null,
        platform: null,
        version: null,
        uniPlatform: null,
        stablePublish: null,
        isMandatory: null,
        isSilently: null,
        createEnv: null,
        createDate: null,
        message: null,
        type: null,
        minUniVersion: null,
        storeUrl: null,
      },
      // 列信息
      columns: [
        { key: 0, label: `应用ID`, visible: true },
        { key: 1, label: `应用名称`, visible: true },
        { key: 2, label: `更新标题`, visible: true },
        { key: 3, label: `更新内容`, visible: true },
        { key: 4, label: `安装包下载地址`, visible: true },
        { key: 5, label: `0：Android | 1：iOS`, visible: true },
        { key: 6, label: `版本号`, visible: true },
        { key: 7, label: `uni-app平台`, visible: true },
        { key: 8, label: `是否是稳定版`, visible: true },
        { key: 9, label: `是否强制更新`, visible: true },
        { key: 10, label: `是否静默更新`, visible: true },
        { key: 11, label: `创建环境`, visible: true },
        { key: 12, label: `创建日期`, visible: true },
        { key: 13, label: `消息`, visible: true },
        { key: 14, label: `0:native_app|1:wgt`, visible: true },
        { key: 15, label: `最低uni-app版本`, visible: true },
        { key: 16, label: `商店链接`, visible: true },
      ],
      // 表单校验
      rules: {},
    };
  },
  watch: {
    // 根据名称筛选部门树
  },
  created() {
    this.getList();
    getDicts('app_platform').then((response) => {
      //dictValue为字典值，dictLabel为字典标签
      response.data.forEach((item) => {
        this.dictAppPlatform[item.dictValue] = item.dictLabel;
      });
    });
    // this.getDeptTree();
    // this.getConfigKey("sys.user.initPassword").then(response => {
    //   this.initPassword = response.msg;
    // });
  },
  methods: {
    /** 查询用户列表 */
    getList() {
      this.loading = true;
      listUpdates(this.queryParams).then((response) => {
        this.updatesList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    async handleFileUpload(data) {
      try {
        this.form.url = data.url;
      } catch (error) {
        console.error('获取错误:', error);
      }
    },
    openDownloadLink(url, version) {
      if (!url) {
        this.$message.error('文件无法下载，未找到文件的URL');
        return;
      }

      // 创建一个隐藏的 <a> 元素
      const link = document.createElement('a');
      link.href = url;
      link.target = '_blank'; // 在新标签页中打开

      // 可选：设置下载文件名
      link.download = `智慧养护v${version}.apk`;

      // 将链接添加到文档中，触发点击，然后移除
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    },
    getPlatformNames(platformString) {
      const platforms = platformString.split(',');
      return platforms.map((p) => this.dictAppPlatform[p]).join(', ');
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        appid: null,
        name: null,
        title: null,
        contents: null,
        url: null,
        platform: null,
        version: null,
        uniPlatform: null,
        stablePublish: null,
        isMandatory: null,
        isSilently: null,
        createEnv: null,
        createDate: null,
        message: null,
        type: null,
        minUniVersion: null,
        storeUrl: null,
      };
      this.resetForm('form');
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dateRange = [];
      this.resetForm('queryForm');
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id);
      this.single = selection.length != 1;
      this.multiple = !selection.length;
    },
    // 表格点击勾选
    handleRowClick(row) {
      row.isSelected = !row.isSelected;
      this.$refs.table.toggleRowSelection(row);
    },
    // 勾选高亮
    rowStyle({ row, rowIndex }) {
      if (this.ids.includes(row.id)) {
        return { 'background-color': '#b7daff', color: '#333' };
      } else {
        return { 'background-color': '#fff', color: '#333' };
      }
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = '添加应用更新信息';
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids;
      getUpdates(id).then((response) => {
        this.form = response.data;
        this.open = true;
        this.title = '修改应用更新信息';
      });
    },
    /** 提交按钮 */
    submitForm: function () {
      this.$refs['form'].validate((valid) => {
        if (valid) {
          if (this.form.id != null) {
            updateUpdates(this.form).then((response) => {
              this.$modal.msgSuccess('修改成功');
              this.open = false;
              this.getList();
            });
          } else {
            addUpdates(this.form).then((response) => {
              this.$modal.msgSuccess('新增成功');
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const id = row.id || this.ids;
      this.$modal
        .confirm('是否确认删除应用更新信息编号为"' + id + '"的数据项？')
        .then(function () {
          return delUpdates(id);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess('删除成功');
        })
        .catch(() => {});
    },

    /** 导出按钮操作 */
    handleExport() {
      this.download(
        'system/updates/export',
        {
          ...this.queryParams,
        },
        `updates_${new Date().getTime()}.xlsx`
      );
    },
    /** 导入按钮操作 */
    handleImport() {
      this.upload.title = '用户导入';
      this.upload.open = true;
    },
    /** 下载模板操作 */
    importTemplate() {
      this.download('system/user/importTemplate', {}, `user_template.xlsx`);
    },
    // 文件上传中处理
    handleFileUploadProgress(event, file, fileList) {
      this.upload.isUploading = true;
    },
    // 文件上传成功处理
    handleFileSuccess(response, file, fileList) {
      this.upload.open = false;
      this.upload.isUploading = false;
      this.$refs.upload.clearFiles();
      this.$alert(
        "<div style='overflow: auto;overflow-x: hidden;max-height: 70vh;padding: 10px 20px 0;'>" +
          response.msg +
          '</div>',
        '导入结果',
        { dangerouslyUseHTMLString: true }
      );
      this.getList();
    },
    // 提交上传文件
    submitFileForm() {
      this.$refs.upload.submit();
    },
  },
};
</script>
<style>
.hasTagsView .app-main[data-v-078753dd] {
  background: #f5f7fa;
}

.tableDiv {
  background-color: white;
  padding-bottom: 10px;
}
</style>
