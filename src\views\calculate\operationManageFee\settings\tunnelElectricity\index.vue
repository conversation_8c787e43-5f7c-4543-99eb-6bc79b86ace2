<template>
  <div class="app-container">
    <el-row :gutter="20">
      <!-- 筛选区开始 -->
      <el-col :span="24" :xs="24">
        <el-row>
          <el-col :span="24">
            <el-form ref="queryForm" :inline="true" :model="queryParams" label-width="68px" size="small">
              <el-form-item label="" prop="maintenanceRoad">
                <RoadSection ref="roadSection" v-model="queryParams.maiSecId" placeholder="路段" :readonly="maiSecId"
                             style="width: 100%"/>
              </el-form-item>
              <el-form-item label="" prop="houseName">
                <el-input v-model="queryParams.houseName" placeholder="请输入户名"/>
              </el-form-item>
              <el-form-item label="" prop="houseNum">
                <el-input v-model="queryParams.houseNum" placeholder="请输入户号"/>
              </el-form-item>
              <el-form-item>
                <el-button icon="el-icon-search" size="mini" type="primary" @click="handleQuery">搜索</el-button>
                <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
              </el-form-item>
            </el-form>
          </el-col>
        </el-row>
        <el-row :gutter="10" class="mb8" v-if="pageType == 'edit'">
          <el-col :span="1.5">
            <el-button
              icon="el-icon-plus"
              size="mini"
              type="primary"
              v-has-menu-permi="['operate:electricity:add']"
              @click="handleAdd"
            >新增
            </el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button
              icon="el-icon-download"
              size="mini"
              type="success"
              v-has-menu-permi="['operate:electricity:export']"
              @click="exportList"
            >导出
            </el-button>
          </el-col>
        </el-row>
        <!-- 筛选区结束 -->
        <!-- 数据表格开始 -->
        <div class="tableDiv">
          <el-table v-adjust-table v-loading="loading" :data="dataList"
                    :height="pageType == 'edit' ? 'calc(100vh - 260px)' : 'calc(70vh - 260px)'" border size="mini"
                    style="width: 100%" @selection-change="handleSelectionChange">
            <el-table-column
              type="selection"
              width="50">
            </el-table-column>
            <el-table-column align="center" fixed label="序号" type="index" width="100"></el-table-column>
            <template v-for="(column, index) in columns">
              <el-table-column
                :label="column.label"
                v-if="column.visible"
                align="center"
                width="150"
                :prop="column.field" show-overflow-tooltip
              >
                <template slot-scope="scope">
                  <dict-tag
                    v-if="column.dict"
                    :options="dict.type[column.dict]"
                    :value="scope.row[column.field]"
                  />
                  <template v-else-if="column.slots">
                    <RenderDom
                      :row="scope.row"
                      :index="index"
                      :render="column.render"
                    />
                  </template>
                  <span v-else-if="column.isTime">{{
                      parseTime(scope.row[column.field], "{y}-{m}-{d}")
                    }}</span>
                  <span v-else>{{ scope.row[column.field] }}</span>
                </template>
              </el-table-column>
            </template>
            <el-table-column align="center" class-name="small-padding fixed-width" fixed="right" label="操作" v-if="pageType == 'edit'" width="300">
            <template slot-scope="scope">
                <el-button icon="el-icon-edit" v-has-menu-permi="['operate:electricity:edit']" size="mini" type="text" @click="handleEdit(scope.row)">编辑</el-button>
                <el-button icon="el-icon-delete" v-has-menu-permi="['operate:electricity:remove']" size="mini" type="text" @click="handleDelete(scope.row)">删除
                </el-button>
              </template>
            </el-table-column>
          </el-table>
          <pagination
            v-show="total > 0"
            :limit.sync="queryParams.pageSize"
            :page.sync="queryParams.pageNum"
            :total="total"
            @pagination="handleQuery"
          />
        </div>
        <!-- 数据表格结束 -->
      </el-col>
    </el-row>
    <!-- 新增对话框 -->
    <el-dialog v-if="dialogVisible" :visible.sync="dialogVisible" append-to-body destroy-on-close :title="dialogTitle"
               width="90%">
      <el-form ref="elForm" :model="formData" :rules="rules" label-width="150px" size="medium">
        <el-row :gutter="15">
          <el-col :span="8">
            <el-form-item label="费用类型" prop="feeType">
              <el-input readonly value="隧道电费"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="管养单位" prop="domainId">
              <select-tree
                v-model="formData.domainId"
                :deptType="100"
                :deptTypeList="[1, 3, 4]"
                clearable
                onlySelectChild
                placeholder="请选择管养单位"
              ></select-tree>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="养护路段" prop="maiSecId">
              <RoadSection ref="roadSection" v-model="formData.maiSecId" :deptId="formData.domainId" placeholder="路段"
                           style="width: 100%"/>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="路线编码" prop="routeCode">
              <route-code-section ref="routeRef" v-model="formData.routeCode" :maintenanceSectionId="formData.maiSecId"
                                  placeholder="路线编码" style="width: 100%"></route-code-section>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="通车时间" prop="openTime">
              <el-date-picker
                v-model="formData.openTime"
                placeholder="通车时间"
                style="width: 100%"
                type="date"
                value-format="yyyy-MM-dd"
              >
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="产权单位" prop="companyType">
              <dict-select
                v-model="formData.companyType"
                clearable
                placeholder="产权单位"
                style="width: 100%"
                type="company_type"
              ></dict-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="户号" prop="houseNum">
              <el-input v-model="formData.houseNum" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="户名称" prop="houseName">
              <el-input v-model="formData.houseName" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="计量点编号" prop="measureNum">
              <el-input v-model="formData.measureNum" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="变压器容量" prop="kva">
              <el-input v-model="formData.kva" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="变压器等级" prop="kv">
              <el-input v-model="formData.kv" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="用电地址" prop="electricityAddr">
              <el-input v-model="formData.electricityAddr" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="供电单位" prop="powerUnit">
              <el-input v-model="formData.powerUnit" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="市场化属性分类" prop="attribute">
              <el-input v-model="formData.attribute" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="电费缴纳单位" prop="payUnit">
              <el-input v-model="formData.payUnit" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="用电范围" prop="powerRange">
              <el-input v-model="formData.powerRange" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="用电性质" prop="electricalProperties">
              <el-input v-model="formData.electricalProperties" />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="备注" prop="remark">
              <el-input :rows="3" type="textarea" v-model="formData.remark" />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div style="text-align: right">
        <el-button @click="closeAddDialog">取消</el-button>
        <el-button type="primary" @click="handleConfirm">确定</el-button>
      </div>
    </el-dialog>
    <div class="mt20" style="text-align: right">
      <el-button v-if="pageType == 'view'" type="primary" @click="handleCheckData">保存</el-button>
    </div>
  </div>
</template>

<script>
import {
  listTunnelElectricityPage,
  addTunnelElectricity,
  editTunnelElectricity,
  deleteTunnelElectricity
} from '@/api/calculate/operationManageFee/tunnelElectricity'
import SelectTree from "@/components/DeptTmpl/selectTree.vue";
import RoadSection from "@/views/baseData/components/roadSection/index.vue";
import RouteCodeSection from "@/components/RouteCodeSection/index.vue";

export default {
  name: "YourComponentName",
  components: {RouteCodeSection, RoadSection, SelectTree},
  dicts: ['company_type'],
  props: {
    pageType: {
      type: String,
      default: 'edit'
    },
    maiSecId: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      // 遮罩层
      loading: false,
      // 显示搜索条件
      showSearch: false,
      // 总条数
      total: 0,
      dataList: [],
      selectDatas: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
      },
      // 列信息
      columns: [
        {key: 0, field: 'domainName', label: '管养单位', visible: true},
        {key: 1, field: 'routeCode', label: '路线编码', visible: true},
        {key: 2, field: 'maiSecName', label: '养护路段', visible: true},
        {key: 3, field: 'openTime', label: '通车时间', visible: true},
        {key: 4, field: 'companyType', label: '产权单位', visible: true, dict: 'company_type'},
        {key: 5, field: 'houseNum', label: '用电户号', visible: true},
        {key: 6, field: 'houseName', label: '用电户名', visible: true},
        {key: 7, field: 'measureNum', label: '计量点编号', visible: true},
        {key: 8, field: 'kva', label: '变压器容量(KVA)', visible: true},
        {key: 9, field: 'kv', label: '电压等级(KV)', visible: true},
        {key: 10, field: 'electricalProperties', label: '用电性质', visible: true},
        {key: 11, field: 'electricityAddr', label: '用电地址', visible: true},
        {key: 12, field: 'powerUnit', label: '供电单位', visible: true},
        {key: 13, field: 'attribute', label: '市场化属性分类', visible: true},
        {key: 14, field: 'powerRange', label: '用电范围', visible: true},
        {key: 15, field: 'payUnit', label: '电费缴纳单位', visible: true}
      ],
      // 对话框标题
      dialogTitle: '',
      dialogVisible: false,
      formData: {},
      rules: {
        domainId: [
          { required: true, message: '请选择管养单位', trigger: 'change' }
        ],
        maiSecId: [
          { required: true, message: '请输入养护路段', trigger: 'change' }
        ],
        routeCode: [
          { required: true, message: '请输入路线编码', trigger: 'change' }
        ],
        openTime: [
          { required: true, message: '请选择通车时间', trigger: 'change' }
        ],
        companyType: [
          { required: true, message: '请选择产权单位', trigger: 'change' }
        ],
        houseNum: [
          { required: true, message: '请输入户号', trigger: 'blur' }
        ],
        houseName: [
          { required: true, message: '请输入户名称', trigger: 'blur' }
        ],
        measureNum: [
          { required: true, message: '请输入计量点编号', trigger: 'blur' }
        ]
      }
    };
  },
  created() {
    this.handleQuery();
  },
  methods: {
    handleQuery() {
      this.loading = true;
      if (this.maiSecId) this.queryParams.maiSecId = this.maiSecId
      listTunnelElectricityPage(this.queryParams).then(res => {
        this.dataList = res.rows;
        this.total = res.total;
        this.loading = false;
      }).catch(err => {
        this.loading = false;
        console.error(err);
      });
    },
    resetQuery() {
      this.queryParams = {
        pageNum: 1,
        pageSize: 10,
        maintenanceRoad: '',
        username: '',
        accountNumber: ''
      };
      this.handleQuery();
    },
    handleAdd() {
      this.dialogTitle = '新增';
      this.formData = {};
      this.dialogVisible = true;
    },
    handleConfirm() {
      this.$refs.elForm.validate(valid => {
        if (valid) {
          this.loading = true;
          if (this.formData.id) {
            editTunnelElectricity(this.formData).then(res => {
              if (res.code === 200) {
                this.$message.success('编辑成功');
                this.handleQuery();
              } else {
                this.$message.error(res.msg);
              }
              this.loading = false;
              this.closeEditDialog();
            }).catch(err => {
              this.loading = false;
              console.error(err);
            });
          } else {
            addTunnelElectricity(this.formData).then(res => {
              if (res.code === 200) {
                this.$message.success('新增成功');
                this.handleQuery();
              } else {
                this.$message.error(res.msg);
              }
              this.loading = false;
              this.closeAddDialog();
            }).catch(err => {
              this.loading = false;
              console.error(err);
            });
          }
        }
      });
    },
    closeAddDialog() {
      this.dialogVisible = false;
      this.$refs.elForm.resetFields();
    },
    handleEdit(row) {
      this.dialogTitle = '编辑';
      this.formData = {...row};
      this.dialogVisible = true;
    },
    closeEditDialog() {
      this.dialogVisible = false;
      this.$refs.elForm.resetFields();
    },
    handleDelete(row) {
      this.$confirm('是否确认删除?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.loading = true;
        deleteTunnelElectricity(row.id).then(res => {
          if (res.code === 200) {
            this.$message.success('删除成功');
            this.handleQuery();
          } else {
            this.$message.error(res.msg);
          }
          this.loading = false;
        }).catch(err => {
          this.loading = false;
          console.error(err);
        });
      })
    },
    // 选中
    handleSelectionChange(e) {
      this.selectDatas = e
    },
    handleCheckData() {
      this.$emit('check', this.selectDatas)
    },
    // 导出清单按钮
    exportList() {
      this.download(
        "manager/operate/tunnel/electricity/export",
        { ...this.queryParams },
        `electricity_${new Date().getTime()}.xlsx`,
        {
          headers: { "Content-Type": "application/json;" },
          parameterType: "body",
        }
      );
    },
  }
};
</script>

<style scoped>
.tableDiv {
  margin-top: 20px;
}
</style>
<style lang="scss" scoped>
@import "@/assets/styles/business.scss";
</style>
