<template>
  <div class="cascade-selection">
    <el-row :gutter="gutter">
      <el-col :span="showCodes?8:12" :offset="0">
        <el-select
          v-model="form.managementOfficeId"
          clearable
          filterable
          ref="stlectTree"
          class="custom-select"
          placeholder="管理处"
          @clear="selectClear"
        >
          <el-option
            v-for="item in deptOptions"
            :key="item.id"
            :value="item.id"
            :label="item.label"
          >
          </el-option>
        </el-select>
      </el-col>
      <el-col :span="showCodes?8:12" :offset="0">
        <el-select
          v-model="form.maintenanceSectionId"
          placeholder="路段名称"
          clearable
          filterable
          @change="maintenanceSectionChange"

        >
          <el-option
            v-for="item in routeOptions"
            :key="item.maintenanceSectionId"
            :label="item.maintenanceSectionName"
            :value="item.maintenanceSectionId"
          >
          </el-option>
        </el-select>
      </el-col>
      <el-col :span="showCodes?8:0" :offset="0">
        <el-select
          v-model="form.roadNumber"
          placeholder="路线编码"
          clearable
          filterable
          @change="routeCodesChange"

          collapse-tags
        >
          <el-option
            v-for="item in routeList"
            :key="item.routeId"
            :label="item.routeName"
            :value="item.routeCode"
          >
          </el-option>
        </el-select>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import {deptTreeSelect} from '@/api/tmpl';
import {
  listByMaintenanceSectionId,
  getMaintenanceSectionListAll,
} from '@/api/baseData/common/routeLine';
import {listAllRoute} from '@/api/system/route.js';
import {listMaintenanceSectionAll} from "@/api/system/maintenanceSection";

/**
 * 管理处下拉选择组件
 */
export default {
  components: {
  },
  props: {
    /**
     * 管养处-数据类型
     * 一、最项层级 只是当前级别：200、包括下级：100
     * 二、管理处 只是当前级别：201、包括下级：101
     * 三、分处 只是当前级别：202、包括下级：102
     * 四、隧管站 只是当前级别：203 、包括下级：103
     * 五、施工单位 、只是当前级别：204 、包括下级：104
     * 六、只要监理单位（包括下级） 只是当前级别：205 、包括下级：105
     * 七、设计单位 、只是当前级别：206 、包括下级：106
     */
    types: {
      type: [String, Number],
      default: 101,
    },
    // 绑定数据
    formData: {
      type: Object,
      default: () => ({
        managementOfficeId: null,
        maintenanceSectionId: null,
        roadNumber: null
      }),
    },
    gutter: {
      default: 20,
    },
    showCodes: {
      default: true,
    }
  },
  data() {
    return {
      deptOptions: [], // 管养处数据
      routeOptions: [], //
      routeList: [], //

      obj: {}, // 返回数据
      form: {
        managementOfficeId: null,
        maintenanceSectionId: null,
        roadNumber: null
      }, // 为了操作数据
      defaultProps: {
        children: 'children',
        label: 'label',
      },
      treeValue: {},
    };
  },
  created() {
    this.getDeptList();
    this.getRoad()
    this.getRoute()
  },
  mounted() {

  },
  watch: {
    formData: {
      handler(val) {
        if (val && Object.keys(val).length) {
          this.form = val;
        }
      },
      deep: true,
      immediate: true,
    },
    'form.managementOfficeId': {
      handler(val) {
        this.$nextTick(() => {
          if (!val) {
            this.selectClear();
            this.getDeptList();
            this.getRoad()
            this.getRoute()
          } else if (val) {
            this.deptChange(val);
          }
        });
      },
      deep: true,
    },
  },
  methods: {
    getDeptList() {
      deptTreeSelect({types: this.types}).then((response) => {
        this.deptOptions = response.data;
      });
    },

    getRoad(){
      getMaintenanceSectionListAll().then((res) => {
          this.routeOptions = res.data || [];
      });
    },


    getRoute(){
      listAllRoute().then((res) => {
          this.routeList = res.data || [];
      });
    },

    // 监听选中
    deptChange(e) {
      this.form.maintenanceSectionId = null;
      this.form.roadNumber = null;
      listMaintenanceSectionAll({departmentIdList: e}).then((res) => {
        this.routeOptions = res.data
      })

      this.updateFrom()
    },
    // 监听路段 变化
    maintenanceSectionChange(e) {
      this.form.roadNumber = null;
      if (e)
      listByMaintenanceSectionId({maintenanceSectionId: e}).then((res) => {
        if (res.code == 200) {
          res.data.forEach((item) => {
            item.routeName = item.routeName + '(' + item.routeCode + ')';
          });
          this.routeList = res.data || [];
        }
      });

      this.updateFrom()
    },

    routeCodesChange(e) {
      this.updateFrom()
    },


    // 监听清空事件
    selectClear() {
      // 清空表单数据
      this.form.managementOfficeId = null;
      this.form.maintenanceSectionId = null;
      this.form.roadNumber = null;

      // 更新对象数据
      this.obj = {
        managementOfficeId: null,
        maintenanceSectionId: null,
        roadNumber: null,
      };
      this.$emit('update:fromData', {...this.formData, ...this.obj});
    },
    updateFrom(){
      this.obj = {
        managementOfficeId: this.formData.managementOfficeId,
        maintenanceSectionId: this.formData.maintenanceSectionId,
        roadNumber: this.formData.roadNumber,
      };
      this.$forceUpdate();
      this.$emit('update:fromData', {...this.formData, ...this.obj});
    }
  },
};
</script>

<style lang="scss" scoped>
.cascade-selection {
  display: flex;
  align-items: center;

  .tree-select-mini {
    font-size: 13px;
    font-weight: unset;

    ::v-deep .vue-treeselect__control {
      height: 26px;
      line-height: 26px;

      .vue-treeselect__placeholder,
      .vue-treeselect__single-value {
        line-height: 26px;
      }
    }

    ::v-deep .vue-treeselect__menu {
      .vue-treeselect__label {
        font-weight: unset;
        color: #606266;
      }
    }
  }

  .tree-select-small {
    font-size: 13px;

    ::v-deep .vue-treeselect__control {
      height: 30px;
      line-height: 30px;
      font-weight: 200;

      .vue-treeselect__placeholder,
      .vue-treeselect__single-value {
        line-height: 30px;
      }
    }

    ::v-deep .vue-treeselect__menu {
      .vue-treeselect__label {
        font-weight: unset;
        color: #606266;
      }
    }
  }

  /* 输入框超出隐藏，不换行*/
  ::v-deep .el-select__tags {
    flex-wrap: nowrap;
    overflow: auto;
  }

  ::v-deep .el-scrollbar {
    .el-select-dropdown__item {
      padding: 0;
    }
  }

  .custom-select {
    .el-select-dropdown__item {
      padding: 0;
      background-color: #0f0;
      /* 修改背景颜色为绿色 */
    }
  }
}
</style>
