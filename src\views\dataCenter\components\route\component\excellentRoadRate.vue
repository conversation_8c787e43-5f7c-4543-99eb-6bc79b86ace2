<template>
  <div class="excellent-road-tate" v-loading="loading" element-loading-background="rgba(0, 0, 0, 0.4)">
    <Echarts :option="option" v-if="option" height="100%" key="routeKey" />
  </div>
</template>

<script>
import Echarts from '../../echarts/echarts.vue';
//api
import { getExcellentRoadRate } from '@/api/cockpit/route';

export default {
  name: 'NatureOfRoute',
  components: {
    Echarts,
  },
  data() {
    return {
      option: null,
      rData: [{
        name: '优',
        value: 1627,
      }, {
        name: '良',
        value: 910,
      }, {
        name: '中',
        value: 604,
      }, {
        name: '次',
        value: 453,
      }, {
        name: '差',
        value: 204,
      },],
      rColor: ['#01FBEF', '#FFA800', '#FFE000', '#8B34FF', '#3C59FD'],
      loading: false,
    }
  },
  async created() {
    this.loading = true;
    let data = await this.getData();
    this.loading = false;
    if (data) {
      this.rData = data.map(v => {
        return {
          name: v.level || v.name,
          value: (v.sum - 0) || 0,
        }
      });
    }
    // Convert values by dividing by 1000
    this.rData = this.rData.map(item => {
      return {
        name: item.name,
        value: item.value / 1000
      };
    });
    this.option = this.initPieCharts(this.rData, this.rColor);
  },
  methods: {
    // 获取数据
    getData() {
      return new Promise((resolve, reject) => {
        getExcellentRoadRate().then(res => {
          if (res.code === 200) {
            resolve(res.data);
          } else {
            reject(res.msg);
          }
        }).catch(err => {
          reject(err);
        })
      })
    },
    initPieCharts(dataArr = [], colorArr = []) {
      var trafficWay = dataArr || [];
      var data = [];

      let sum = dataArr.reduce((acc, curr) => acc + curr.value, 0);
      let avg = sum / (dataArr.length * 10);
      let total = avg;

      var color = colorArr || ['#00ffff', '#00cfff', '#006ced', '#ffe000', '#ffa800', '#ff5b00', '#ff3000']
      for (var i = 0; i < trafficWay.length; i++) {
        data.push({
          value: trafficWay[i].value,
          name: trafficWay[i].name,
          itemStyle: {
            normal: {
              borderWidth: 8,
              shadowBlur: 2,
              borderColor: color[i],
              shadowColor: color[i]
            }
          }
        }, {
          value: total || 3,
          name: '',
          itemStyle: {
            normal: {
              label: {
                show: false
              },
              labelLine: {
                show: false
              },
              color: 'rgba(0, 0, 0, 0)',
              borderColor: 'rgba(0, 0, 0, 0)',
              borderWidth: 0
            }
          }
        });
      }
      var seriesOption = [
        {
          type: "pie",
          zlevel: 0,
          silent: true,
          radius: ["85%", "89%"],
          center: ['26%', '50%'],
          hoverAnimation: false,
          color: "rgba(0,62,122,1)",
          label: {
            normal: {
              show: false,
            },
          },
          labelLine: {
            normal: {
              show: false,
            },
          },
          data: [1],
        },
        {
          name: '',
          type: 'pie',
          clockWise: false,
          radius: ['70%', '75%'],
          center: ['26%', '50%'],
          hoverAnimation: false,
          label: {
            normal: {
              show: false,
            },
          },
          labelLine: {
            normal: {
              show: false,
            },
          },
          data: data
        }];
      let option = {
        backgroundColor: 'rgba(0,0,0,0)',
        color: color,
        title: {
          text: `优等路率`,
          x: 'center',
          y: 'center',
          left: '25%',
          textAlign: "center",
          textStyle: {
            color: "#fff",
            fontSize: 16 * 2.5,
            fontWeight: "700",
            shadowColor: "rgba(27,126,242,0.8)",
            shadowBlur: 10,
            shadowOffsetX: 5,
            shadowOffsetY: 5,
          },
        },
        tooltip: {
          show: false
        },
        toolbox: {
          show: false
        },
        series: seriesOption,
        legend: {
          show: true,
          y: 'center',
          right: '2%',
          icon: 'circle',
          itemWidth: 30, // 设置宽度
          itemHeight: 30, // 设置高度
          itemGap: 35,
          textStyle: {
            color: '#fff',
            rich: {
              title: {
                color: 'rgba(255,255,255,0.8)',
                fontSize: 12 * 2.2,
                padding: [3, 0],
              },
              value: {
                color: '#fff',
                fontSize: 14 * 2.5,
                fontWeight: 'bold',
              },
              unit: {
                color: 'rgba(182,182,182,0.8)',
                fontSize: 12 * 2.2,
              }
            }
          },
          formatter: (name) => {
            let d = dataArr.filter(v => v.name == name);
            let value = d ? d[0].value : null;
            let total = dataArr.reduce((sum, item) => sum + item.value, 0);
            let percent = ((value / total) * 100).toFixed(2);
            return `{title|${name}：}` + `{value|${value}} ` + `{unit|km} ` + ` {title|${percent}%}`;
          },
        },
      }
      return option;
    },
  }
}
</script>

<style lang="scss" scoped>
.excellent-road-tate {
  width: 100%;
  height: 100%;
  padding: 5px 10px;
}
</style>