<template>
  <div class="formBox">

    <!-- 表单 -->
      <el-form ref="subFormRef" :model="subFormParams" :disabled="formIsApprove === '1'" :rules="formRules" label-width="125px" label-position="right">

        <div class="infoBox" style="margin-bottom: 20px;" v-loading="formLoading">
          <div class="infoTitle">
            灾害历史特征
          </div>
          <el-row :gutter="12">
            <el-col :span="24">
              <el-form-item label="是否发生灾害" prop="hisDisasterHappened">
                <el-radio-group
                  @input="formRadioClear('hisDisasterHappened', [
                  'hisLastHappenedTime',
                  'hisHappenedFrequency',
                  'hisScopeLength',
                  'hisScopeWidth',
                  'hisTrigger',
                  'hisRouteStatus',
                  'hisHazardForm',
                  'hisHazardTarget',
                  'hisHazardTargetRemark',
                  'hisHazardDegree',
                  'hisDisasterDisposal'
                  ])"
                  v-model="subFormParams.hisDisasterHappened" ref="subFormRadio1">
                  <el-radio :label="'0'">是</el-radio>
                  <el-radio :label="'1'">否</el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>
          </el-row>

          <div v-show="subFormParams.hisDisasterHappened === '0'">

            <el-divider></el-divider>

            <el-row :gutter="12">
              <el-col :span="12">
                <el-form-item label="灾害最近发生时间" prop="hisLastHappenedTime">
                  <el-date-picker v-model="subFormParams.hisLastHappenedTime" style="width:100%" type="date"
                    placeholder="灾害最近发生时间" clearable value-format="yyyy-MM-dd" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="灾害发生频率" prop="hisHappenedFrequency">
                  <el-input-number v-model="subFormParams.hisHappenedFrequency" controls-position="right" :min="0" :precision="0" style="width: 100%;" placeholder="近十年发生次数" />
                </el-form-item>
              </el-col>
            </el-row>

            <el-divider></el-divider>

            <el-row :gutter="12">
              <el-col :span="12">
                <el-form-item label="规模长度(米)" prop="hisScopeLength">
                  <el-input-number v-model="subFormParams.hisScopeLength" controls-position="right" :min="0" :precision="2" style="width: 100%;" placeholder="请输入规模长度" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="规模宽度(米)" prop="hisScopeWidth">
                  <el-input-number v-model="subFormParams.hisScopeWidth" controls-position="right" :min="0" :precision="2" style="width: 100%;" placeholder="请输入规模宽度" />
                </el-form-item>
              </el-col>
            </el-row>

            <el-divider></el-divider>

            <el-row :gutter="12">
              <el-col :span="24">
                <el-form-item label="触发因素" prop="hisTrigger">
                  <el-radio-group v-model="subFormParams.hisTrigger" @input="formRadioChange('hisTrigger', 'hisTriggerRemark')"
                    ref="subFormRadio2">
                    <el-radio v-for="dict in dict.type.trigger_factor" :label="dict.value"
                      style="margin-right: 30px; margin-top: 5px; margin-bottom: 5px;">{{ dict.label }}</el-radio>
                      <el-radio label="99" style="margin-top: 5px; margin-bottom: 5px;">其它</el-radio>
                    </el-radio-group>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="12" v-show="subFormParams.hisTrigger === '99'">
              <el-col :span="24">
                <el-form-item label="" prop="hisTrigger">
                  <el-input v-model="subFormParams.hisTriggerRemark" placeholder="其它" />
                </el-form-item>
              </el-col>
            </el-row>

            <el-divider></el-divider>

            <el-row :gutter="12">
              <el-col :span="24">
                <el-form-item label="断通情况" prop="hisRouteStatus">
                  <el-radio-group v-model="subFormParams.hisRouteStatus" ref="subFormRadio3">
                    <el-radio v-for="dict in dict.type.router_status" :label="dict.value">{{ dict.label }}</el-radio>
                  </el-radio-group>
                </el-form-item>
              </el-col>
            </el-row>

            <el-divider></el-divider>

            <el-row :gutter="12">
              <el-col :span="24">
                <el-form-item label="危害形式" prop="hisHazardForm">
                  <el-radio-group v-model="subFormParams.hisHazardForm" ref="subFormRadio4">
                    <el-radio v-for="dict in dict.type.hazard_form" :label="dict.value">{{ dict.label }}</el-radio>
                  </el-radio-group>
                </el-form-item>
              </el-col>
            </el-row>

            <el-divider></el-divider>

            <el-row :gutter="12">
              <el-col :span="24">
                <el-form-item label="危害对象" prop="hisHazardTarget">
                  <el-checkbox-group v-model="subFormParams.hisHazardTarget">
                    <el-checkbox  v-for="dict in dict.type.hazard_target" :label="dict.value">{{ dict.label }}</el-checkbox>
                  </el-checkbox-group>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="12">
              <el-col :span="24">
                <el-form-item label="" prop="hisHazardTarget">
                  <el-input v-model="subFormParams.hisHazardTargetRemark" placeholder="其它" />
                </el-form-item>
              </el-col>
            </el-row>

            <el-divider></el-divider>

            <el-row :gutter="12">
              <el-col :span="24">
                <el-form-item prop="hisHazardDegree">
                  <span slot="label">
                    <el-tooltip content="
                      危害程度级别：①严重：有下列情形之一的为严重：
                      1、公路及其构筑物完全破坏或功能完全丧失，交通毁坏或中断；
                      高速公路、普通国道抢修、处置时间 48 小时以上；
                      县道、乡道抢修、处置时间 48 小时以上，且短期内难以修复。
                      2、直接经济损失大于 1000 万元。②较严重：有下列情形之一的为较严重：
                      1、公路及其构筑物遭到较大破坏或功能受到较大影响，需要进行专门加固
                      治理后才能投入正常运用；
                      国道、省道抢修、处置时间 24 小时以上；
                      县道、乡道抢修、处置时间 48 小时以上。
                      2、直接经济损失为 500 万至 1000 万元。③一般：有下列情形之一者为一般。
                      1、公路及其构筑物遭到一些破坏或功能受到一些影响，及时修复后仍能使
                      用；
                      国道、省道抢修、处置时间 6 小时以上；
                      县道、乡道抢修、处置时间 24 小时以上。
                      2、直接经济损失为 100 万至 500 万元。④轻微：有下列情形之一者为轻微。
                      1、国道、省道及其构筑物仅受很小的影响，公路及构筑物基本功能影响不
                      大，未造成交通中断；
                      县道、乡道抢修、处置时间 12 小时以上。
                      2、直接经济损失小于 100 万。"
                      placement="top"
                    >
                      <i class="el-icon-question"></i>
                    </el-tooltip>
                    危害程度
                  </span>
                  <el-radio-group v-model="subFormParams.hisHazardDegree" ref="subFormRadio6">
                    <el-radio v-for="dict in dict.type.hazard_degree" :label="dict.value">{{ dict.label }}</el-radio>
                  </el-radio-group>
                </el-form-item>
              </el-col>
            </el-row>

            <el-divider></el-divider>

            <el-row :gutter="12">
              <el-col :span="24">
                <el-form-item label="灾害处治情况" prop="hisDisasterDisposal">
                  <el-radio-group v-model="subFormParams.hisDisasterDisposal" ref="subFormRadio7">
                    <el-radio v-for="dict in dict.type.disaster_disposal" :label="dict.value">{{ dict.label }}</el-radio>
                  </el-radio-group>
                </el-form-item>
              </el-col>
            </el-row>
          </div>
        </div>

        <div class="infoBox" style="margin-bottom: 20px;" v-loading="formLoading">
          <div class="infoTitle">
            风险点现状特征
          </div>
          <el-row :gutter="12">
            <el-col :span="24">
              <el-form-item label="裂缝" prop="curCracked">
                <el-radio-group v-model="subFormParams.curCracked"
                  @input="formRadioClear('curCracked', ['curCrackLocation', 'curCrackLocationRemark'])" ref="subFormRadio8">
                  <el-radio :label="'0'">有</el-radio>
                  <el-radio :label="'1'">无</el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>
          </el-row>

          <el-divider v-if="subFormParams.curCracked === '0'"></el-divider>

          <el-row :gutter="12" v-show="subFormParams.curCracked === '0'">
            <el-col :span="24">
              <el-form-item label="裂缝位置" prop="curCrackLocation">
                <el-checkbox-group v-model="subFormParams.curCrackLocation">
                  <el-checkbox  v-for="dict in dict.type.subside_crack_location" :label="dict.value">{{ dict.label }}</el-checkbox>
                </el-checkbox-group>
                <!-- <el-radio-group v-model="subFormParams.curCrackLocation" @input="formRadioChange('curCrackLocation', 'curCrackLocationRemark')"
                  ref="subFormRadio9">
                  <el-radio v-for="dict in dict.type.crack_location" :label="dict.value"
                    style="margin-right: 30px; margin-top: 5px; margin-bottom: 5px;">{{ dict.label }}</el-radio>
                    <el-radio label="99" style="margin-top: 5px; margin-bottom: 5px;">其它</el-radio>
                </el-radio-group> -->
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="12" v-show="subFormParams.curCracked === '0'">
            <el-col :span="24">
              <el-form-item label="" prop="curCrackLocation">
                <el-input v-model="subFormParams.curCrackLocationRemark" placeholder="其它" />
              </el-form-item>
            </el-col>
          </el-row>

          <el-divider></el-divider>

          <el-row :gutter="12">
            <el-col :span="24">
              <el-form-item label="结构物破损情况" prop="curDamaged">
                <el-radio-group v-model="subFormParams.curDamaged" @input="formRadioClear('curDamaged', [
                  'curDamagedDrainage',
                  'curDamagedDrainageRemark',
                  'curDamagedSlopeSurface',
                  'curDamagedSlopeSurfaceRemark',
                  'curDamagedRiverBank',
                  'curDamagedRiverBankRemark',
                  'curDamagedSupportFacility',
                  'curDamagedSupportFacilityRemark',
                  'curDamagedBridge',
                  'curDamagedTunnel',
                  'curDamagedOther'
                ])" ref="subFormRadio10">
                  <el-radio :label="'0'">有</el-radio>
                  <el-radio :label="'1'">无</el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>
          </el-row>

          <div v-show="subFormParams.curDamaged === '0'">

            <el-divider></el-divider>

            <el-row :gutter="12">
              <el-col :span="24">
                <el-form-item label="排水" prop="curDamagedDrainage">
                  <el-checkbox-group v-model="subFormParams.curDamagedDrainage">
                    <el-checkbox  v-for="dict in dict.type.side_slope_surface_drainage_facilities" :label="dict.value">{{ dict.label }}</el-checkbox>
                  </el-checkbox-group>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="12">
              <el-col :span="24">
                <el-form-item label="" prop="curDamagedDrainage">
                  <el-input v-model="subFormParams.curDamagedDrainageRemark" placeholder="其它" />
                </el-form-item>
              </el-col>
            </el-row>

            <el-divider></el-divider>

            <el-row :gutter="12">
              <el-col :span="24">
                <el-form-item label="坡面防护" prop="curDamagedSlopeSurface">
                  <el-checkbox-group v-model="subFormParams.curDamagedSlopeSurface">
                    <el-checkbox  v-for="dict in dict.type.side_slope_protection" :label="dict.value">{{ dict.label }}</el-checkbox>
                  </el-checkbox-group>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="12">
              <el-col :span="24">
                <el-form-item label="" prop="curDamagedSlopeSurface">
                  <el-input v-model="subFormParams.curDamagedSlopeSurfaceRemark" placeholder="其它" />
                </el-form-item>
              </el-col>
            </el-row>

            <el-divider></el-divider>

            <el-row :gutter="12">
              <el-col :span="24">
                <el-form-item label="沿河防护" prop="curDamagedRiverBank">
                  <el-checkbox-group v-model="subFormParams.curDamagedRiverBank">
                    <el-checkbox  v-for="dict in dict.type.protection_along_the_river" :label="dict.value">{{ dict.label }}</el-checkbox>
                  </el-checkbox-group>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="12">
              <el-col :span="24">
                <el-form-item label="" prop="curDamagedRiverBank">
                  <el-input v-model="subFormParams.curDamagedRiverBankRemark" placeholder="其它" />
                </el-form-item>
              </el-col>
            </el-row>

            <el-divider></el-divider>

            <el-row :gutter="12">
              <el-col :span="24">
                <el-form-item label="支挡设施" prop="curDamagedSupportFacility">
                  <el-checkbox-group v-model="subFormParams.curDamagedSupportFacility">
                    <el-checkbox  v-for="dict in dict.type.side_slope_support_facilities" :label="dict.value">{{ dict.label }}</el-checkbox>
                  </el-checkbox-group>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="12">
              <el-col :span="24">
                <el-form-item label="" prop="curDamagedSupportFacility">
                  <el-input v-model="subFormParams.curDamagedSupportFacilityRemark" placeholder="其它" />
                </el-form-item>
              </el-col>
            </el-row>

            <el-divider></el-divider>

            <el-row :gutter="12">
              <el-col :span="24">
                <el-form-item label="桥梁" prop="curDamagedBridge">
                  <el-checkbox-group v-model="subFormParams.curDamagedBridge">
                    <el-checkbox  v-for="dict in dict.type.damaged_bridge" :label="dict.value">{{ dict.label }}</el-checkbox>
                  </el-checkbox-group>
                </el-form-item>
              </el-col>
            </el-row>

            <el-divider></el-divider>

            <el-row :gutter="12">
              <el-col :span="24">
                <el-form-item label="隧道" prop="curDamagedTunnel">
                  <el-checkbox-group v-model="subFormParams.curDamagedTunnel">
                    <el-checkbox  v-for="dict in dict.type.damaged_tunnel" :label="dict.value">{{ dict.label }}</el-checkbox>
                  </el-checkbox-group>
                </el-form-item>
              </el-col>
            </el-row>

            <el-divider></el-divider>

            <el-row :gutter="12">
              <el-col :span="24">
                <el-form-item label="其他结构物" prop="curDamagedOther">
                  <el-input v-model="subFormParams.curDamagedOther" placeholder="其他结构物" />
                </el-form-item>
              </el-col>
            </el-row>

            <el-divider></el-divider>

          </div>

          <el-divider></el-divider>

          <el-row :gutter="12">
            <el-col :span="24">
              <el-form-item prop="curDisasterDevelopment">
                <span slot="label">
                  <el-tooltip content="
                    危害程度级别：①严重：满足下述 3 项及以上者为严重：
                    1、公路附近地面多处下陷、开裂，塌陷严重。
                    2、路面、路基、构筑物开裂明显，破损严重。
                    3、道路沉陷深度大于 30cm。
                    4、路基沉陷长度大于 50m。②中等：满足下述 3 项及以上者为中等：
                    1、公路附近存在采空区或岩溶等造成沉陷塌陷的条件，地面塌陷、开裂
                    明显。
                    2、路面、路基、构筑物有开裂现象，构筑物局部破损，裂缝中等发育。
                    3、路基沉陷深度 10~30cm。
                    4、路基沉陷长度 30~50m。③轻微：满足下述 3 项及其以上者为轻微：
                    1、公路附近地面塌陷、开裂不明显。
                    2、路基、构筑物少量变形、开裂，路面少量裂缝。
                    3、路基沉陷深度小于 10cm。
                    4、路基沉陷长度 10~30m。④无：公路附近地面塌陷、开裂不明显，路基、构筑物无变形、无裂缝，构筑
                    物完好。"
                    placement="top"
                  >
                    <i class="el-icon-question"></i>
                  </el-tooltip>
                  灾害发育程度
                </span>
                <el-radio-group v-model="subFormParams.curDisasterDevelopment" ref="subFormRadio17">
                  <el-radio v-for="dict in dict.type.disaster_development" :label="dict.value">{{ dict.label
                    }}</el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>
          </el-row>
        </div>

        <div class="infoBox" style="margin-bottom: 20px;" v-loading="formLoading">
          <div class="infoTitle">
            风险点特征描述
          </div>
          <el-row :gutter="12">
            <el-col :span="24">
              <el-form-item label-width="0" prop="riskDescription">
                <el-input
                  :autosize="{ minRows: 4 }"
                  type="textarea"
                  placeholder="主要从灾害历史特征、风险点现状特征及对公路可能造成危害进行描述。"
                  v-model="subFormParams.riskDescription"
                  maxlength="500"
                  show-word-limit
                ></el-input>
              </el-form-item>
            </el-col>
          </el-row>
        </div>

        <div class="infoBox" style="margin-bottom: 20px;" v-loading="formLoading">
          <div class="infoTitle">
            示意图及照片
          </div>
          <el-row :gutter="12">
            <el-col :span="24">
              <el-form-item label-width="0" prop="riskDescription">
                <div class="imgBox" v-if="formImgSrcList.length > 0">
                  <el-timeline>
                    <el-timeline-item v-for="item in formImgDateLine" :timestamp="item.time" placement="top" color="#5cbb7a">
                      <el-card class="imgBoxCard">
                        <div class="cardMain" v-for="itemC in item.data">
                          <el-button class="imgDeleteBtn" type="danger" icon="el-icon-delete" circle @click="formDeleteImg(itemC.id)"></el-button>
                          <div class="imgTitle">
                            <el-tooltip class="item" effect="dark" :content="itemC.name" placement="top">
                              <i class="el-icon-info"></i>
                            </el-tooltip>
                            {{ itemC.name }}
                          </div>
                          <el-image
                            fit="cover"
                            class="img"
                            @click="formImgPreview(itemC.imgUrl)"
                            :src="itemC.url"
                            :preview-src-list="formImgUrlList"
                          ></el-image>
                          <div class="footer">
                            {{`由 ${itemC.createBy} 上传于 ${itemC.createTime}` }}
                          </div>
                          <div class="footer">
                            {{`图片描述：${itemC.remark ? itemC.remark : ''}` }}
                          </div>
                        </div>
                      </el-card>
                    </el-timeline-item>
                  </el-timeline>
                </div>
                <div class="noneBox" v-else>
                  暂无内容
                </div>
              </el-form-item>
            </el-col>
          </el-row>
        </div>

        <div class="infoBox" v-loading="formLoading" style="padding: 15px;" v-if="formIsApprove === '0'">
          <div class="infoTitle">
            <el-tooltip content="①如发生灾害需上传灾害照片。②如灾害已处治需上传处治后照片。照片规范：平面图或全貌照片（反映沉陷与塌陷特征及范围，与危害对象关系），剖面图或局部照片（反映构筑物破损情况、裂缝分布情况等） " placement="top">
              <i class="el-icon-question"></i>
            </el-tooltip>
            上传示意图及照片
          </div>
          <ImageUpload
            v-model="formUploadList"
            :ownerId="formImgOwnerId"
            :needRemark="true"
            :can-sort="true"
            storage-path="/disaster/risk/subside/"
            platform="fykj"
          />
        </div>

      </el-form>

  </div>
</template>

<script>
// -------------------- 引入 --------------------
// 组件
import PileInput from '@/components/PileInput/index.vue'
import ImageUpload from '@/views/disaster/ImageUpload.vue'

// API
import { queryPageRiskSub } from "@/api/disaster/risk/risk"
import { removeFile } from '@/api/system/fileUpload.js'
import { findFiles } from '@/api/file/index.js'

export default {
  name: "SubSide",
  // 数据字典
  dicts: [
    'trigger_factor', // 触发因素
    'router_status', // 断通情况
    'hazard_form', // 危害形式
    'hazard_target', // 危害对象
    'hazard_degree', // 危害程度
    'disaster_disposal', // 灾害处治情况
    // 'crack_location', // 裂缝位置
    'subside_crack_location', // 裂缝位置
    'side_slope_surface_drainage_facilities', // 排水
    'side_slope_protection', // 坡面防护
    'protection_along_the_river', // 沿河防护
    'side_slope_support_facilities', // 支挡设施
    'damaged_bridge', // 桥梁受损部位
    'damaged_tunnel', // 隧道受损部位
    'disaster_development', // 灾害发育情况
  ],
  // 组件
  components: {
    PileInput,
    ImageUpload
  },

  // -------------------- 变量 --------------------
  data() {
    return {

      /**
       * 表单相关
       */
      subFormParams: { // 表单参数
        // 子表参数
        hisDisasterHappened: '1', // 是否发生灾害（0是，1否）
        hisLastHappenedTime: '', // 灾害最近发生时间
        hisHappenedFrequency: 0, // 灾害发生频率（近十年发生次数）
        hisScopeLength: '', // 规模（长度）
        hisScopeWidth: '', // 规模（长度）
        hisTrigger: '', // 触发因素
        hisTriggerRemark: '', // 触发因素备注
        hisRouteStatus: '', // 断通情况
        hisHazardForm: '', // 危害形式
        hisHazardTarget: [], // 危害对象
        hisHazardTargetRemark: '', // 危害对象其他
        hisHazardDegree: '', // 危害程度
        hisDisasterDisposal: '', // 灾害处治情况
        curCracked: '1', // 是否有裂缝（0是，1否）
        curCrackLocation: [], // 裂缝位置
        curCrackLocationRemark: '', // 裂缝位置其他
        curDamaged: '1', // 是否破损（0是，1否）
        curDamagedDrainage: [], // 排水
        curDamagedDrainageRemark: '', // 排水其他
        curDamagedSlopeSurface: [], // 坡面防护
        curDamagedSlopeSurfaceRemark: '', // 坡面防护其他
        curDamagedRiverBank: [], // 沿河防护
        curDamagedRiverBankRemark: '', // 沿河防护其他
        curDamagedSupportFacility: [], // 支挡设施
        curDamagedSupportFacilityRemark: '', // 支挡设施其他
        curDamagedBridge: [], // 桥梁
        curDamagedTunnel: [], // 隧道
        curDamagedOther: '', // 其它
        curDisasterDevelopment: '', // 灾害发育情况
        riskDescription: '', // 风险点特征描述
        imgIds: '', // 示意图及图片
      },
      formTitle: '崩塌风险点信息调查表', // 表单dialog标题
      formLoading: false, // 表单加载
      formRules: { // 表单校验规则
        hisDisasterHappened: [
          { required: true, message: "请选择是否发生灾害", trigger: "blur" }
        ],
        curCracked: [
          { required: true, message: "请选择是否有裂缝", trigger: "blur" }
        ],
        curDamaged: [
          { required: true, message: "请选择是否有破损情况", trigger: "blur" }
        ],
        curDisasterDevelopment: [
          { required: true, message: "请选择灾害发育程度", trigger: "blur" }
        ],
      },
      formImgSrcList: [], // 图片渲染列表
      formImgUrlList: [], // 图片预览列表
      formUploadList: '', // 图片上传列表
      formImgOwnerId: '', // 图片上传ownerId
      formImgNum: [], // 图片数量（图片集用）

      formIsApprove: '0', // 表单审批状态
    }
  },
  // -------------------- 计算属性 --------------------
  computed: {
    formImgDateLine() {
      let dateLine = []
      if(this.formImgSrcList.length > 0) {
        this.formImgSrcList.forEach(item => {
          let date = item.createTime.split('T')[0]
          if(dateLine.length === 0) {
            dateLine.push({
              time: date,
              data: [item]
            })
          } else {
            let index = dateLine.findIndex(item2 => item2.time === date)
            if(index !== -1) {
              dateLine[index].data.push(item)
            } else {
              dateLine.push({
                time: date,
                data: [item]
              })
            }
          }
        })
        // 时间线排序
        dateLine.sort((a, b) => {
          // 将日期字符串分割并转换为日期对象
          let dateA = new Date(a.time);
          let dateB = new Date(b.time);

          // 比较日期
          return dateA - dateB;
        })
      }
      console.log('组装的时间线图片：', dateLine)
      return dateLine
    }
  },
  mounted() {
    // this.initPage()
  },
  // -------------------- 方法 --------------------
  methods: {

    /**
     * 页面相关
     */
    // 初始化页面
    async initPage(type, id) {
      console.log('初始化子表：', type, id)
      switch (type) {
        case 'add':
          await this.formInit(type)
          break;
        case 'edit':
          await this.querySubList(id)
          break;
      }
      // 解决单选框报错问题（谷歌浏览器升级导致）
      this.$nextTick(() => {
        for (let radio in this.$refs) {
          let refName = 'subFormRadio'
          let check = radio.includes(refName)
          if (check) {
            this.$refs[radio].$children.forEach((item) => {
              item.$refs.radio.removeAttribute("aria-hidden");
            });
          }
        }
      })
    },

    // 获取子表数据
    async querySubList(id) {
      this.formLoading = true
      await queryPageRiskSub(id).then((res) => {
        console.log('获取崩塌子表数据：', res)
        if(res.code === 200) {
          this.formInit('edit', res.data)
        }
        this.formLoading = false
      }).catch((err) => {
        this.formLoading = false
        this.$message.error(err)
      })
    },

    // 获取图片数据
    async queryImg(id) {
      await findFiles({ ownerId: id }).then((res) => {
        if(res.code === 200 && res.data.length > 0) {
          this.formImgSrcList = res.data.map(item => ({
            id: item.ownerId + '-' + item.id,
            name: item.originalFilename,
            url: item.thumbUrl,
            imgUrl: item.url,
            remark: item.remark,
            createTime: item.createTime,
            createBy: item.createBy
          }))
          this.formImgUrlList = res.data.map(item => item.url)
        }
      }).catch(() => {
        this.$message.error('图片查询失败，请重新打开表单尝试')
      })
    },

    /**
     * 表单相关
     */

    // 点击修改时
    formEdit(type, item) {
      let val = null
      switch (type) {
        case 'select':
          val = this.tableSelection[0]
          break;

        case 'click':
          val = item
          break;
      }
      this.querySubList(val)
    },

    // 初始化表单
    formInit(type, item, isApprove) {
      this.formIsApprove = isApprove
      switch (type) {
        case 'add':
          // 重置表单
          this.subFormParams = {
            // 子表参数
            hisDisasterHappened: '1', // 是否发生灾害（0是，1否）
            hisLastHappenedTime: '', // 灾害最近发生时间
            hisHappenedFrequency: 0, // 灾害发生频率（近十年发生次数）
            hisScopeLength: '', // 规模（长度）
            hisScopeWidth: '', // 规模（长度）
            hisTrigger: '', // 触发因素
            hisTriggerRemark: '', // 触发因素备注
            hisRouteStatus: '', // 断通情况
            hisHazardForm: '', // 危害形式
            hisHazardTarget: [], // 危害对象
            hisHazardTargetRemark: '', // 危害对象其他
            hisHazardDegree: '', // 危害程度
            hisDisasterDisposal: '', // 灾害处治情况
            curCracked: '1', // 是否有裂缝（0是，1否）
            curCrackLocation: [], // 裂缝位置
            curCrackLocationRemark: '', // 裂缝位置其他
            curDamaged: '1', // 是否破损（0是，1否）
            curDamagedDrainage: [], // 排水
            curDamagedDrainageRemark: '', // 排水其他
            curDamagedSlopeSurface: [], // 坡面防护
            curDamagedSlopeSurfaceRemark: '', // 坡面防护其他
            curDamagedRiverBank: [], // 沿河防护
            curDamagedRiverBankRemark: '', // 沿河防护其他
            curDamagedSupportFacility: [], // 支挡设施
            curDamagedSupportFacilityRemark: '', // 支挡设施其他
            curDamagedBridge: [], // 桥梁
            curDamagedTunnel: [], // 隧道
            curDamagedOther: '', // 其它
            curDisasterDevelopment: '', // 灾害发育情况
            riskDescription: '', // 风险点特征描述
            imgIds: '', // 示意图及图片
          }
          // 重置图片渲染及上传接收列表
          this.formImgSrcList = [] // 图片缩略图列表
          this.formImgUrlList = [] // 图片预览图列表
          this.formUploadList = '' // 图片上传列表
          this.formImgOwnerId = new Date().getTime().toString() // 图片上传ownerId
          this.subFormParams.imgIds = this.formImgOwnerId
          this.formImgNum = [] // 图片集数量
          break;

        case 'edit':
          // 表单赋值
          this.subFormParams = {
            // 子表参数
            hisDisasterHappened: item.hisDisasterHappened, // 是否发生灾害（0是，1否）
            hisLastHappenedTime: item.hisLastHappenedTime, // 灾害最近发生时间
            hisHappenedFrequency: item.hisHappenedFrequency, // 灾害发生频率（近十年发生次数）
            hisScopeLength: item.hisScopeLength, // 规模（长度）
            hisScopeWidth: item.hisScopeWidth, // 规模（长度）
            hisTrigger: item.hisTrigger, // 触发因素
            hisTriggerRemark: item.hisTriggerRemark, // 触发因素备注
            hisRouteStatus: item.hisRouteStatus, // 断通情况
            hisHazardForm: item.hisHazardForm, // 危害形式
            hisHazardTarget: this.formArrayChange(item.hisHazardTarget), // 危害对象
            hisHazardTargetRemark: item.hisHazardTargetRemark, // 危害对象其他
            hisHazardDegree: item.hisHazardDegree, // 危害程度
            hisDisasterDisposal: item.hisDisasterDisposal, // 灾害处治情况
            curCracked: item.curCracked, // 是否有裂缝（0是，1否）
            curCrackLocation: this.formArrayChange(item.curCrackLocation), // 裂缝位置
            curCrackLocationRemark: item.curCrackLocationRemark, // 裂缝位置其他
            curDamaged: item.curDamaged, // 是否破损（0是，1否）
            curDamagedDrainage: this.formArrayChange(item.curDamagedDrainage), // 排水
            curDamagedDrainageRemark: item.curDamagedDrainageRemark, // 排水其他
            curDamagedSlopeSurface: this.formArrayChange(item.curDamagedSlopeSurface), // 坡面防护
            curDamagedSlopeSurfaceRemark: item.curDamagedSlopeSurfaceRemark, // 坡面防护其他
            curDamagedRiverBank: this.formArrayChange(item.curDamagedRiverBank), // 沿河防护
            curDamagedRiverBankRemark: item.curDamagedRiverBankRemark, // 沿河防护其他
            curDamagedSupportFacility: this.formArrayChange(item.curDamagedSupportFacility), // 支挡设施
            curDamagedSupportFacilityRemark: item.curDamagedSupportFacilityRemark, // 支挡设施其他
            curDamagedBridge: this.formArrayChange(item.curDamagedBridge), // 桥梁
            curDamagedTunnel: this.formArrayChange(item.curDamagedTunnel), // 隧道
            curDamagedOther: item.curDamagedOther, // 其它
            curDisasterDevelopment: item.curDisasterDevelopment, // 灾害发育情况
            riskDescription: item.riskDescription, // 风险点特征描述
            imgIds: item.imgIds, // 示意图及图片
          }
          // 重置图片渲染及上传接收列表
          this.formImgSrcList = []
          this.formImgUrlList = []
          this.formUploadList = '' // 图片上传列表

          this.formImgNum = [] // 图片集数量
          if(this.subFormParams.imgIds) {
            this.queryImg(this.subFormParams.imgIds)
            this.formImgOwnerId = this.subFormParams.imgIds
          }else {
            this.formImgOwnerId = new Date().getTime().toString()
            this.subFormParams.imgIds = this.formImgOwnerId
          }
          break;
      }
    },

    // 表单数组转换
    formArrayChange(item) {
      if(item) {
        let check = item.includes(',')
        if(check) {
          return item.split(',')
        } else {
          return [item]
        }
      } else {
        return []
      }
    },

    // 表单其他控制
    formRadioChange(type, remark) {
      if(this.subFormParams[type] !== '99') {
        this.subFormParams[remark] = ''
      }
    },

    // 表单单选选择无时处理
    formRadioClear(target, typeArray) {
      if (this.subFormParams[target] === '1') {
        typeArray.forEach(type => {
          // 移除表单验证
          delete this.formRules[type]
          if(Array.isArray(this.subFormParams[type])) {
            this.subFormParams[type] = []
          } else {
            this.subFormParams[type] = ''
          }
        })
        switch (target) {
          case 'hisDisasterHappened':
            this.subFormParams.hisHappenedFrequency = 0
            break;

          case 'curCracked':
            break;

          case 'curDamaged':
            this.subFormParams.curDamagedOther = ''
            break;
        }
      } else {
        // 添加表单验证
        for(let i=0; i<typeArray.length; i++) {
          if(typeArray[i].includes("Remark")) {
            continue
          }
          this.formRules[typeArray[i]] = [
            { required: true, message: "该数据为必填/必选", trigger: "blur" }
          ]
        }
      }
    },

    // 点击预览图片时
    formImgPreview(url) {
      let index = this.formImgUrlList.findIndex(item => item === url)
      if(index !== -1) {
        let moveUrl = this.formImgUrlList.splice(index, this.formImgUrlList.length - index)
        this.formImgUrlList.unshift(...moveUrl)
      }
    },

    // 删除图片
    formDeleteImg(id) {
      this.$modal.confirm('是否确认删除该图片？').then(async () => {
        this.$modal.loading('正在删除图片，请稍候...')
        removeFile(id.split('-')[1]).then((res) => {
          if (res.code === 200) {
            this.$message.success('删除图片成功！')
            // 移除预览列表图片
            let index = this.formImgSrcList.findIndex(item => item.id === id)
            if(index !== -1) {
              let imgUrl = this.formImgSrcList[index].imgUrl
              let imgIndex = this.formImgUrlList.indexOf(imgUrl)
              console.log('预览索引：', imgIndex)
              if(imgIndex !== -1) {
                this.formImgUrlList.splice(imgIndex, 1)
              }
            }
            // 移除渲染列表图片
            this.formImgSrcList = this.formImgSrcList.filter(item => item.id !== id)
          }
          this.$modal.closeLoading()
        }).catch(() => {
          this.$message.error('删除图片失败')
          this.$modal.closeLoading()
        })
      })
    },
  },
}

</script>

<style lang="scss" scoped>
.app-container form:first-child .el-select,
.app-container form:nth-child(2) .el-select,
.app-container form:nth-child(2) ::v-deep .el-form-item__content,
.app-container form:first-child ::v-deep .el-form-item__content {
  width: 240px;
}

.app-container form:first-child .el-form-item:last-child ::v-deep .el-form-item__content {
  width: auto;
}

::v-deep .el-dialog__body {
  height: 600px;
  overflow-y: auto;
}

.tableDiv {
  background-color: white;
  padding-bottom: 10px;
}

.infoBox {
  padding: 15px 15px 0 15px;
  box-sizing: border-box;
  border-radius: 6px;
  border: 1px solid #C4C4C4;
  position: relative;

  .infoTitle {
    user-select: none;
    position: absolute;
    top: 0;
    left: 0;
    padding: 0 10px;
    font-size: 14px;
    line-height: 14px;
    font-weight: bold;
    transform: translateX(15px) translateY(-50%);
    background-color: white;
  }

  .imgBox {
    height: auto;
    width: 100%;
    // display: flex;
    // flex-wrap: wrap;
    // align-content: flex-start;

    // .imgItemBox {
    //   height: 240px;
    //   width: calc(100% / 3);
    //   box-sizing: border-box;
    //   padding: 10px;
    //   overflow-y: auto;
    //   display: flex;
    //   align-items: center;
    //   justify-content: center;
    //   position: relative;

    //   .imgDeleteBtn {
    //     position: absolute;
    //     z-index: 1;
    //     top: 10%;
    //     right: 10%;
    //   }
    // }

    ::v-deep .el-card__body {
      width: 100%;
      display: flex;
      flex-direction: row;
      flex-wrap: wrap;
      align-content: flex-start;
    }

    .imgBoxCard {
      width: 100%;

      .cardMain {
        height: 260px;
        width: 33%;
        box-sizing: border-box;
        padding: 0 10px;
        display: flex;
        flex-direction: column;
        position: relative;

        .imgDeleteBtn {
          position: absolute;
          z-index: 1;
          top: 20%;
          right: 5%;
        }

        .imgTitle {
          height: 28px;
          width: 100%;
          font-size: 16px;
          font-weight: bold;
          white-space: nowrap;
          text-overflow: ellipsis;
          overflow: hidden;
        }

        .img {
          height: calc(100% - (28px + 28px));
          width: 100% ;
          padding: 10px 0;
          position: relative;
          z-index: 0;
        }

        .footer {
          height: 28px;
          color: #888888;
          font-size: 14px;
        }
      }
    }
  }

  .noneBox {
    user-select: none;
    height: 200px;
    width: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    color: #888888;
  }
}

::v-deep .el-divider--horizontal {
  margin: 0 0 18px 0;
}
</style>
