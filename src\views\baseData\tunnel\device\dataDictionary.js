// 行政识别数据
const baseInfoData = [
  {
    type: 'select',
    deptType: 201,
    label:'管理处',
    prop: 'managementMaintenanceId',
    span:12,
    disabled: true,
    rules: [{ required: true, message: '请选择隧道', trigger: 'change' }],
  },

  {
    label: '路线编码',
    prop: 'routeCode',
    type: 'input',
    disabled: true,
    span:12,
    rules: [{ required: true, message: '请选择隧道', trigger: 'change' }],
  },

  {
    label:'养护路段',
    prop: 'maintenanceSectionId',
    type: 'input',
    span:12,
    disabled: true,
    rules: [{ required: true, message: '请选择隧道', trigger: 'change' }],
  },
  {
    label:'隧道编码',
    prop: 'tunnelCode',
    type: 'input',
    rules: [{ required: true, message: '请选择隧道', trigger: 'change' }],
    span:12,
    disabled: true,
  },
  {
    label:'隧道名称',
    prop: 'tunnelName',
    type: 'input',
    span:12,
    disabled: true,
    rules: [{ required: true, message: '请选择隧道', trigger: 'change' }],
  },
  {
    label:"设施编码",
    prop:"deviceCode",
    type:"input",
    span:12,
  },
  {
    label:"设施名称",
    prop:"deviceName",
    type:"input",
    span:12,
    rules: [{ required: true, message: '请输入设施名称', trigger: 'change' }],
  },
  {
    label:"设施类型",
    prop:"deviceType",
    type:"select",
    span:12,
    dict: 'tunnel_elect_device_type',
    rules: [{ required: true, message: '请输入设施类型', trigger: 'change' }],
  },
  {
    label:"设施型号",
    prop:"deviceModel",
    type:"input",
    span:12,

  },
  {
    label:"入口桩号",
    prop:"startStake",
    type:"pileInput",
    span:12,
  },
  {
    label:"出口桩号",
    prop:"endStake",
    type:"pileInput",
    span:12,
  },

  {
    label:"光源类别",
    prop:"lightType",
    type:"input",
    span:12,
  },
  {
    label:"数量",
    prop:"number",
    type:"inputNumber",
    span:12,
  },
  {
    label:"功率",
    prop:"power",
    type:"inputNumber",
    span:12,
  },
  {
    label:"单位",
    prop:"unit",
    type:"input",
    span:12,
  },
  {
    label:"记录者",
    prop:"recorder",
    type:"input",
    span:12,

  },
  {
    label:"是否关键设备",
    prop:"isKeyDevice",
    type:"select",
    span:12,
    dict: 'base_data_yes_no',
  },
  
  {
    label:"记录时间",
    prop:"recorderTime",
    type:"date",
    span:12,
  },
  {
    label:"描述",
    prop:"remark",
    type:"input",
    span:12,
  },

  

]





export default {
  baseInfoData,
  
}
