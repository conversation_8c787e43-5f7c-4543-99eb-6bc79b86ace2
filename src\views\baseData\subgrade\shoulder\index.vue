<template>
  <PageContainer>
    <template slot="search">
      <div style="display: flex; align-items: center; margin: 0">
        <CascadeSelection
          style="min-width: 192px"
          :form-data="queryParams"
          v-model="queryParams"
          types="201"
          multiple
        />
        <RangeInput
          :clearData="clearData"
          @startValue="
            (v) => {
              queryParams.startStake = v;
            }
          "
          @endValue="
            (v) => {
              queryParams.endStake = v;
            }
          "
        />
        <div style="min-width: 220px">
          <el-button
            v-hasPermi="['baseData:shoulder:getListPage']"
            type="primary"
            @click="handleQuery"
            >搜索</el-button
          >
          <el-button @click="resetQuery">重置</el-button>
          <el-button
            v-show="!showSearch"
            icon="el-icon-arrow-down"
            circle
            @click="showSearch = true"
          />
          <el-button
            v-show="showSearch"
            icon="el-icon-arrow-up"
            style="
              color: #1890ff;
              border-color: #badeff;
              background-color: #e8f4ff;
            "
            circle
            @click="showSearch = false"
          />
        </div>
      </div>
      <div v-if="showSearch" style="margin-top: 5px">
        <el-select
          v-model="queryParams.operationState"
          placeholder="运营状态"
          clearable
          collapse-tags
          style="width: 170px"
        >
          <el-option
            v-for="dict in dict.type.sys_operation_state"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
        <el-select
          style="margin-left: 20px; width: 170px"
          v-model="queryParams.status"
          placeholder="数据状态"
          clearable
        >
          <el-option
            v-for="dict in dict.type.base_data_state"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
        <el-input
          style="margin-left: 20px; width: 170px"
          v-model="queryParams.roadbedCode"
          placeholder="路肩编码"
          clearable
        />
      </div>
    </template>
    <template slot="header">
      <div class="button-list">
        <el-button
          type="primary"
          v-hasPermi="['baseData:shoulder:add']"
          @click="handleAdd"
          >新增</el-button
        >
        <el-button
          type="primary"
          v-hasPermi="['baseData:shoulder:edit']"
          @click="handleUpdate"
          >编辑</el-button
        >
        <el-button
          type="primary"
          v-hasPermi="['baseData:shoulder:getInfoById']"
          @click="handleView()"
          >查看</el-button
        >
        <el-button
          type="primary"
          v-hasPermi="['baseData:shoulder:delete']"
          @click="handleDelete"
          >删除</el-button
        >
        <el-button
          v-hasPermi="['baseData:import:execute']"
          type="primary"
          @click="importAdd"
          >导入新增</el-button
        >
        <el-button
          v-hasPermi="['baseData:import:execute']"
          type="primary"
          @click="importUpdate"
          >导入更新</el-button
        >
        <el-button
          v-hasPermi="['baseData:shoulder:export']"
          type="primary"
          @click="exportList"
          >导出清单</el-button
        >
        <el-button
          v-hasPermi="['baseData:shoulder:genQrCode']"
          type="primary"
          @click="downloadQrcode"
          >二维码下载</el-button
        >
        <el-button
          v-hasPermi="['baseData:businessStatusRecord:getListPage']"
          type="primary"
          @click="onStatusChange"
          >运营状态变更
        </el-button>
      </div>
    </template>
    <template slot="body">
      <el-table
        v-adjust-table
        ref="table"
        height="100%"
        style="width: 100%"
        :header-cell-style="{ height: '36px' }"
        :row-style="rowStyle"
        v-loading="loading"
        border
        :data="staticList"
        @selection-change="handleSelectionChange"
        @row-click="handleRowClick"
      >
        <el-table-column type="selection" width="50" align="center" />
        <el-table-column
          fixed
          label="序号"
          type="index"
          width="50"
          align="center"
        >
          <template v-slot="scope">
            {{
              scope.$index +
              (queryParams.pageNum - 1) * queryParams.pageSize +
              1
            }}
          </template>
        </el-table-column>
        <el-table-column fixed label="操作" align="center" width="50">
          <template slot-scope="scope">
            <el-link
              type="primary"
              :disabled="!scope.row.shape"
              @click.stop="handleLocation(scope.row)"
              >定位</el-link
            >
          </template>
        </el-table-column>
        <el-table-column
          fixed
          label="路肩编码"
          align="center"
          prop="roadbedCode"
          width="140"
        />
        <el-table-column
          label="管理处"
          align="center"
          prop="managementMaintenanceName"
          min-width="140"
          show-overflow-tooltip
        />
        <el-table-column
          label="管养分处"
          align="center"
          prop="managementMaintenanceBranchName"
          min-width="140"
          show-overflow-tooltip
        />
        <el-table-column
          label="养护路段"
          align="center"
          prop="maintenanceSectionName"
          min-width="140"
          show-overflow-tooltip
        />
        <el-table-column
          label="路线编码"
          align="center"
          prop="routeCode"
          min-width="140"
          show-overflow-tooltip
        />

        <el-table-column
          label="桩号范围"
          align="center"
          min-width="200"
          show-overflow-tooltip
        >
          <template #default="{ row }">
            <span>{{ formatPile(row.startStake) }}</span>
            <span v-if="row.startStake">~</span>
            <span>{{ formatPile(row.endStake) }}</span>
          </template>
        </el-table-column>
        <el-table-column
          align="center"
          prop="operationState"
          min-width="140"
          label="运营状态"
        >
          <template slot-scope="{ row }">
            <el-link
              :underline="false"
              :type="
                { 1: 'info', 2: 'success', 3: 'danger', 4: 'primary' }[
                  row.operationState
                ]
              "
              @click="handleOperational($event, row)"
            >
              <DictTag
                :value="row.operationState"
                :options="dict.type.sys_operation_state"
              />
            </el-link>
          </template>
        </el-table-column>
        <el-table-column label="数据状态" align="center" min-width="140">
          <template #default="{ row }">
            <el-link
              :type="{ 1: 'info', 2: 'success' }[row.status]"
              :underline="false"
            >
              <DictTag
                :value="row.status"
                :options="dict.type.base_data_state"
              />
            </el-link>
          </template>
        </el-table-column>
        <el-table-column
          label="方向"
          align="center"
          prop="direction"
          min-width="140"
          show-overflow-tooltip
        >
          <template slot-scope="{ row }">
            <DictTag
              :value="row.direction"
              :options="dict.type.sys_route_direction"
            />
          </template>
        </el-table-column>
        <el-table-column
          label="位置"
          align="center"
          prop="lane"
          min-width="140"
          show-overflow-tooltip
        >
          <template slot-scope="{ row }">
            <DictTag :value="row.lane" :options="dict.type.lane" />
          </template>
        </el-table-column>
        <el-table-column
          label="经度"
          align="center"
          prop="longitude"
          min-width="140"
          show-overflow-tooltip
        />
        <el-table-column
          label="纬度"
          align="center"
          prop="latitude"
          min-width="140"
          show-overflow-tooltip
        />
        <el-table-column
          label="施工里程桩号"
          align="center"
          prop="constructionStake"
          min-width="140"
          show-overflow-tooltip
        >
          <template #default="{ row }">
            <span>{{ formatPile(row.constructionStake) }}</span>
          </template>
        </el-table-column>
        <el-table-column
          label="统一里程桩号"
          align="center"
          prop="unifiedMileageStake"
          min-width="140"
          show-overflow-tooltip
        >
          <template #default="{ row }">
            <span>{{ formatPile(row.unifiedMileageStake) }}</span>
          </template>
        </el-table-column>
        <el-table-column
          label="坡长(m)"
          align="center"
          prop="slopeLength"
          min-width="140"
          show-overflow-tooltip
        />
        <el-table-column
          label="路肩类型"
          align="center"
          prop="shoulderType"
          min-width="140"
          show-overflow-tooltip
        >
          <template slot-scope="{ row }">
            <DictTag
              :value="row.shoulderType"
              :options="dict.type.shoulder_type"
            />
          </template>
        </el-table-column>
        <el-table-column
          label="土质"
          align="center"
          prop="soil"
          min-width="140"
          show-overflow-tooltip
        />
        <el-table-column
          label="高度(m)"
          align="center"
          prop="height"
          min-width="140"
          show-overflow-tooltip
        />
        <el-table-column
          label="平台数量"
          align="center"
          prop="numPlatform"
          min-width="140"
          show-overflow-tooltip
        />
        <el-table-column
          label="检查梯"
          align="center"
          prop="ladder"
          min-width="140"
          show-overflow-tooltip
        />
        <el-table-column
          label="防护方式"
          align="center"
          prop="protectType"
          min-width="140"
          show-overflow-tooltip
        />
        <el-table-column
          label="坡脚长度(m)"
          align="center"
          prop="footLength"
          min-width="140"
          show-overflow-tooltip
        />
        <el-table-column
          label="落台宽度(m)"
          align="center"
          prop="fallingWidth"
          min-width="140"
          show-overflow-tooltip
        />

        <el-table-column
          label="备注"
          align="center"
          prop="remark"
          min-width="140"
          show-overflow-tooltip
        />
        <el-table-column label="图片" align="center">
          <template #default="{ row }">
            <el-link
              v-hasPermi="['baseData:shoulder:getInfoById']"
              :underline="false"
              type="primary"
              :disabled="row.samplePictureId ? false : true"
              @click.stop="previewImg(row)"
              >查看</el-link
            >
          </template>
        </el-table-column>
        <el-table-column label="详情" align="center" width="100" fixed="right">
          <template
            slot-scope="scope"
            style="display: flex; justify-content: space-around"
          >
            <el-button type="text" @click="handleView(scope.row.id)"
              >查看</el-button
            >
          </template>
        </el-table-column>
      </el-table>
      <pagination
        :total="total"
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
        :pageSizes="[10, 20, 30, 50, 100, 1000]"
        @pagination="getList"
      />
    </template>
    <Form
      v-if="showAddEdit"
      :forView="forView"
      :formData="formData"
      :title="title"
      :showAddEdit="showAddEdit"
      @close="
        () => {
          showAddEdit = false;
          formData = {};
        }
      "
      @refresh="
        () => {
          showAddEdit = false;
          formData = {};
          getList();
        }
      "
    />
    <ImportData
      v-if="showImportAdd"
      :is-update="isUpdate"
      :dialog-visible="showImportAdd"
      :import-base-type="importBaseType"
      :import-type="importType"
      @close="closeImportAdd"
    />
    <Dialog title="查看图片" width="500px" :show.sync="imgShow">
      <ImagePreview :owner-id="imageUrl" width="100%" height="100%" />
    </Dialog>
    <MapPosition
      v-if="showMapPosition"
      :dialogVisible="showMapPosition"
      :data="mapPositionData"
      @close="showMapPosition = false"
    />
  </PageContainer>
</template>

<script>
import {
  listShoulderRecords,
  getShoulder,
  delShoulder,
} from "@/api/baseData/subgrade/shoulder/index";
import Form from "./form.vue";
import ImportData from "@/views/baseData/components/importData/index.vue";
import CascadeSelection from "@/components/CascadeSelection/index.vue";
import DictTag from "@/components/DictTag";
import Dialog from "@/components/Dialog/index.vue";
import RangeInput from "@/views/baseData/components/rangeInput/index.vue";
import { statusDialog } from "@/views/baseData/components/statusDialog/index.js";
import { statusListDialog } from "@/views/baseData/components/statusDialog/list.js";
import MapPosition from "@/components/mapPosition/index.vue";

export default {
  name: "Shoulder",
  components: {
    Form,
    ImportData,
    CascadeSelection,
    DictTag,
    RangeInput,
    Dialog,
    MapPosition,
  },
  dicts: [
    "lane",
    "sys_operation_state",
    "sys_route_direction",
    "shoulder_type",
    "base_data_state",
  ],
  data() {
    return {
      loading: true,
      showAddEdit: false,
      forView: false,
      title: "",
      formData: {},
      showUpload: false,
      importBaseType: "19",
      clearData: false,
      isUpdate: false,
      importType: 0,
      ids: [],
      showImportAdd: false,
      total: 0,
      staticList: [],
      showSearch: false,
      queryParams: {
        pageNum: 1,
        pageSize: 20,
        operationState: "2",
      },
      selectdTables: [],
      imgShow: false,
      imageUrl: "",
      showMapPosition: false,
      mapPositionData: undefined,
    };
  },
  watch: {},
  created() {
    this.getList();
  },
  methods: {
    getList() {
      this.loading = true;
      listShoulderRecords(this.queryParams)
        .then((res) => {
          if (res.code === 200) {
            this.staticList = res.rows;
            this.total = res.total;
            this.clearData = false;
          }
        })
        .finally(() => {
          this.loading = false;
        });
    },
    handleSelectionChange(selection) {
      this.selectdTables = selection;
      this.ids = selection.map((item) => item.id);
    },
    rowStyle({ row, rowIndex }) {
      if (this.ids.includes(row.id)) {
        return { "background-color": "#b7daff", color: "#333" };
      } else {
        return { "background-color": "#fff", color: "#333" };
      }
    },
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    resetQuery() {
      this.clearData = true;
      this.queryParams = { pageNum: 1, pageSize: 20, operationState: "2", };
      this.handleQuery();
    },
    handleAdd() {
      this.forView = false;
      this.showAddEdit = true;
      this.formData = {};
      this.title = "新增路肩数据";
    },
    closeImportAdd(v) {
      this.showImportAdd = false;
      if (v) this.getList();
    },
     // 表格操作-运营状态
     handleOperational(event, row) {
      event.stopPropagation();
      statusListDialog({ dataId: row.assetId, baseDataType: 19 });
    },
    // 二维码下载
    downloadQrcode() {
      if (this.staticList.length === 0) return;
      let msg =
        this.ids && this.ids.length > 0
          ? `已选择${this.ids.length}条路肩数据，是否下载二维码？`
          : "即将下载所有表格的二维码数据，此过程可能花费时间较长，是否继续？";
      this.$modal
        .confirm(msg)
        .then(() => {
          this.queryParams.ids = this.ids;
          this.download(
            "/baseData/shoulder/genQrCode",
            this.queryParams,
            `二维码_${new Date().getTime()}.xlsx`,
            {
              headers: { "Content-Type": "application/json;" },
              parameterType: "body",
            }
          );
        })
        .catch(() => {});
    },
    handleRowClick(row) {
      row.isSelected = !row.isSelected;
      this.$refs.table.toggleRowSelection(row);
    },
    importUpdate() {
      this.isUpdate = true;
      this.showImportAdd = true;
      this.importType = 1;
    },
    importAdd() {
      this.isUpdate = false;
      this.showImportAdd = true;
      this.importType = 2;
    },
    handleUpdate() {
      if (this.ids.length != 1) {
        this.$message.warning("请选择一条数据进行编辑！");
        return;
      } else {
        getShoulder(this.ids[0]).then((res) => {
          if (res && res.data) {
            this.formData = res.data;
            this.forView = false;
            this.showAddEdit = true;
            this.title = "修改路肩数据";
          }
        });
      }
    },
    // 删除按钮
    handleDelete() {
      if (this.ids.length == 0) {
        this.$message.warning("请选择至少一条数据进行删除！");
        return;
      }
      this.$modal
        .confirm("确认删除？")
        .then(() => {
          delShoulder(this.ids).then((res) => {
            if (res && res.code == "200") {
              this.getList();
              this.$modal.msgSuccess("删除成功");
            }
          });
        })
        .catch(() => {});
    },
    // 导出清单按钮
    exportList() {
      if (this.staticList.length === 0) return;
      if (this.ids.length === 0) {
        this.$modal
          .confirm("即将导出所有表格数据，此过程可能花费时间较长，是否继续？")
          .then(() => {
            this.download(
              "/baseData/shoulder/export",
              this.queryParams,
              `road_interflow_${new Date().getTime()}.xlsx`,
              {
                headers: { "Content-Type": "application/json;" },
                parameterType: "body",
              }
            );
          })
          .catch(() => {});
      } else {
        this.$modal
          .confirm(`已选择${this.ids.length}条路肩数据，确认导出清单？`)
          .then(() => {
            this.download(
              "/baseData/shoulder/export",
              { ids: this.ids },
              `static_${new Date().getTime()}.xlsx`,
              {
                headers: { "Content-Type": "application/json;" },
                parameterType: "body",
              }
            );
          })
          .catch(() => {});
      }
    },
    // 查看图片
    previewImg(row) {
      this.imgShow = true;
      this.imageUrl = row.samplePictureId;
    },
    handleView(id) {
      let data = "";
      if (id) {
        data = id;
      } else {
        if (this.ids.length !== 1) {
          this.$message.warning("请选择一条数据！");
          return;
        } else {
          data = this.ids[0];
        }
      }
      getShoulder(data).then((res) => {
        if (res && res.data) {
          this.formData = res.data;
          this.forView = true;
          this.showAddEdit = true;
          this.title = "查看路肩数据";
        }
      });
    },
    // 运营状态变更
    onStatusChange() {
      if (this.ids.length != 1) {
        this.$message.warning("请选择一条数据！");
        return;
      } else {
        let row = this.selectdTables[0];
        statusDialog({ dataId: row.assetId, baseDataType: 19 }).then((res) => {
          if (res) {
            this.getList();
          }
        });
      }
    },
    // 查看运营变更记录
    onStatusList(row) {
      statusListDialog({ dataId: row.assetId, baseDataType: 19 });
    },
    // 表格操作-定位
    handleLocation(row) {
      this.mapPositionData = row;
      this.showMapPosition = true;
    },
  },
};
</script>

<style lang="scss" scoped>
.button-list {
  border-radius: 4px;
  width: 100%;
  .el-button {
    margin-bottom: 10px;
    margin-right: 10px;
    margin-left: 0;
  }
}
</style>
