<template>
  <el-dialog title="选择隧道" :visible.sync="visible" width="80%" append-to-body v-dialog-drag>
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-if="!isAddAll">
      <el-form-item prop="routeCode">
        <el-select v-model="queryParams.routeCode" filterable clearable placeholder="请选择路线编码"
                   style="width: 240px">
          <el-option
            v-for="item in routeList"
            :key="item.routeId"
            :label="item.routeName"
            :value="item.routeCode">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="" prop="tunnelName">
        <el-input
          v-model="queryParams.tunnelName"
          placeholder="请输入隧道名称"
          clearable
          style="width: 240px"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="" prop="tunnelCode">
        <el-input
          v-model="queryParams.tunnelCode"
          placeholder="请输入隧道编码"
          clearable
          style="width: 240px"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item style="width: 236px;">
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>
    <el-row>
      <el-table size="mini" style="width: 100%" v-loading="loading" border
                highlight-current-row
                @row-click="clickRow" @select="selectChange"
                @select-all="handleSelectionChange"
                ref="table"
                :data="tableDataList"
                height="396px"
      >
        <el-table-column v-if="isAddAll" type="selection" width="50" align="center"/>
        <el-table-column fixed label="序号" type="index" width="50" align="center">
          <template v-slot="scope">
            {{ (scope.$index + (queryParams.pageNum - 1) * queryParams.pageSize)+1 }}
          </template>
        </el-table-column>
        <el-table-column label="路线编号" align="center" prop="routeCode" min-width="140" show-overflow-tooltip/>
        <el-table-column fixed label="隧道名称" align="center" prop="tunnelName" min-width="140" show-overflow-tooltip/>
        <el-table-column fixed label="隧道代码" align="center" prop="tunnelCode" min-width="140" show-overflow-tooltip/>
        <el-table-column label="养护路段" align="center" prop="maintenanceSectionName"
                         min-width="140"></el-table-column>
        <el-table-column label="管养单位" align="center" prop="managementMaintenanceName" min-width="140"/>
        <el-table-column label="管养分处" align="center" prop="managementMaintenanceBranchName"
                         min-width="140"></el-table-column>
        <el-table-column label="统一里程桩号" align="center" prop="unifiedMileageStake" min-width="140"
                         show-overflow-tooltip>
          <template slot-scope="scope">
            {{ formatPile(scope.row.unifiedMileageStake) }}
          </template>
        </el-table-column>
      </el-table>
      <pagination
        v-show="total>0"
        :total="total"
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
        @pagination="getList"
      />
    </el-row>
    <div slot="footer" class="dialog-footer">
      <el-button type="primary" @click="handleSelectBridge">确 定</el-button>
      <el-button @click="visible = false">取 消</el-button>
    </div>
  </el-dialog>
</template>

<script>
import {listStatic} from '@/api/baseData/tunnel/baseInfo'
import {listAllRoute} from "@/api/system/route";
export default {
  props: {
    /** * 类型： tunnel ， 5， 6 * */
    // 三者区别在于检查时间，日常:天 ; 经常：月
    type: {
      type: String,
      default: 'tunnel',
    },
    maintenanceSectionId: {
      type: String,
      default: '',
    },
    isAddAll: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      // 遮罩层
      visible: false,
      loading: false,
      // 选中数组值
      assetIds: [],
      // 总条数
      total: 0,
      // 表数据
      tableDataList: [],
      routeList: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        tunnelName: null,
        tunnelCode: null,
        routeCode: null
      },
      //提交参数
      params: {},
      maintenanceSectionList: [],
      // 部门树选项
      deptOptions: [],
      currentRow: null
    };
  },
  methods: {
    // 显示弹框
    show() {
      console.log("this.isAddAll:", this.isAddAll)
      this.getList();
      if (!this.isAddAll) this.getRouteList();
      this.visible = true;
    },
    clickRow(row) {
      this.$refs.table.toggleRowSelection(row);
      this.setChecked(row);
    },
    selectChange(arr, row) {
      //退选和选中处理
      this.setChecked(row);
    },
    //退选和选中处理
    setChecked(row){
      if (this.assetIds.includes(row.assetId)) {
        this.assetIds = this.assetIds.filter(i => i !== row.assetId);
      } else {
        this.assetIds.push(row.assetId)
      }
      if (!this.isAddAll) this.currentRow = row;
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      if (selection.length > 0) {
        selection.forEach(item => {
          // this.setChecked(item);
          if (!this.assetIds || !this.assetIds.includes(item.assetId)) {
            this.assetIds.push(item.assetId)
          }
        })
      } else {
        this.tableDataList.forEach(item => {
          this.assetIds = this.assetIds.filter(i => i !== item.assetId);
        })
      }
    },
    /** 查询路线列表 */
    getRouteList() {
      listAllRoute().then(res => {
        this.routeList = res.data
      })
    },
    // 查询表数据
    getList() {
      this.loading = true;
      if (this.isAddAll) {
        this.queryParams.maintenanceSectionId = this.maintenanceSectionId
      }
      this.queryParams.ruleIdsFlag = true;
      listStatic(this.queryParams).then(response => {
        this.tableDataList = response.rows
        this.total = response.total
        this.loading = false
      })
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    /** 提交选择养护路段操作 */
    handleSelectBridge() {
      let data;
      if (this.isAddAll) {
        if (!this.assetIds) {
          this.$modal.msgError("请选择隧道");
          return;
        }
        data = this.assetIds;
      } else {
        if (!this.currentRow) {
          this.$modal.msgError("请选择隧道");
          return;
        }
        data = this.currentRow;
      }
      this.$emit('update:checkEntity', data);
      this.assetIds = [];
      this.visible=false;
    }
  }
};
</script>
