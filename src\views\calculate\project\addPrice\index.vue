<template>
  <div class="app-container maindiv">
    <el-row :gutter="20" style="display: flex">
      <!--部门数据-->
      <maintenance-tree @rowClick="handleNodeClick" :realNav="realNav" @closeNav="realNav=false"></maintenance-tree>
      <!--角色数据-->
      <el-col :span="realNav ? 19 : 24" :xs="24">
        <!--展开图标-->
        <div class="rightIcon" @click="realNav = true" v-show="!realNav">
          <span class="el-icon-caret-right"></span>
        </div>
        <el-row>
          <el-col :span="24" :xs="24">
            <el-row>
              <el-form
                  ref="queryForm"
                  :inline="true"
                  :model="queryParams"
                  label-width="68px"
                  size="mini"
              >
                <el-form-item label="" prop="year">
                  <el-date-picker
                      v-model="queryParams.year"
                      placeholder="年份"
                      style="width: 240px"
                      type="year"
                      value-format="yyyy"
                  >
                  </el-date-picker>
                </el-form-item>
                <el-form-item>
                  <selectTree
                      :key="'domainId'"
                      v-model="queryParams.domainId"
                      :deptType="100"
                      :deptTypeList="[1, 3, 4]"
                      clearable
                      filterable
                      placeholder="管养单位"
                      style="width: 240px"
                  />
                </el-form-item>
                <el-form-item>
                  <selectTree
                      :key="'conDomainId'"
                      v-model="queryParams.conDomainId" :data-rule="false"
                      :dept-type="100"
                      placeholder="施工单位"
                      :filter-keys="['云南省交通投资建设集团有限公司', '云南交投投资有限公司']"
                      :expand-all="false"
                      clearable
                      filterable
                      style="width: 240px"
                  />
                </el-form-item>
                <el-form-item>
                  <RoadSection
                      v-model="queryParams.maiSecId"
                      :deptId="queryParams.domainId"
                      placeholder="路段"
                  />
                </el-form-item>
                <el-form-item>
                  <el-input
                      v-model="queryParams.projectName"
                      placeholder="项目名称"
                      style="width: 240px"
                  />
                </el-form-item>
                <el-form-item>
                  <el-input
                      v-model="queryParams.constructionName"
                      placeholder="任务单名称"
                      style="width: 240px"
                  />
                </el-form-item>
                <el-form-item>
                  <el-input
                      v-model="queryParams.constructionCode"
                      placeholder="任务单编号"
                      style="width: 240px"
                  />
                </el-form-item>
                <el-form-item>
                  <el-input
                      v-model="queryParams.schemeCode"
                      placeholder="子目号"
                      style="width: 240px"
                  />
                </el-form-item>
                <el-form-item>
                  <dict-select
                      v-model="queryParams.calcStatus"
                      clearable
                      placeholder="计量情况"
                      style="width: 240px"
                      type="testing_calc_status"
                  ></dict-select>
                </el-form-item>

                <el-form-item>
                  <el-button
                      icon="el-icon-search"
                      size="mini"
                      type="primary"
                      @click="handleQuery"
                  >搜索
                  </el-button
                  >
                  <el-button
                      icon="el-icon-refresh"
                      size="mini"
                      @click="resetQuery"
                  >重置
                  </el-button
                  >
                  <el-button
                      v-show="!showSearch"
                      circle
                      icon="el-icon-arrow-down"
                      @click="showSearch = true"
                  ></el-button>
                  <el-button
                      v-show="showSearch"
                      circle
                      icon="el-icon-arrow-up"
                      @click="showSearch = false"
                  ></el-button>
                </el-form-item>
              </el-form>
              <!--默认折叠-->
              <el-col :span="24">
                <el-form
                    v-show="showSearch"
                    ref="queryForm"
                    :inline="true"
                    :model="queryParams"
                    label-width="68px"
                    size="small"
                >
                  <el-form-item>
                    <dict-select
                        v-model="queryParams.isGuarantee"
                        clearable
                        placeholder="请选择是否计算安全保通费"
                        style="width: 240px"
                        type="bridge_simple_bool"
                    ></dict-select>
                  </el-form-item>
                  <el-form-item>
                    <dict-select
                        v-model="queryParams.isProduction"
                        clearable
                        placeholder="请选择是否计算安全生产费"
                        style="width: 240px"
                        type="bridge_simple_bool"
                    ></dict-select>
                  </el-form-item>
                  <el-form-item>
                    <el-date-picker
                        v-model="queryParams.realEDate"
                        end-placeholder="完工日期"
                        range-separator="至"
                        start-placeholder="完工日期"
                        style="width: 240px"
                        type="daterange"
                        value-format="yyyy-MM-dd"
                    >
                    </el-date-picker>
                  </el-form-item>
                  <el-form-item>
                    <dict-select
                        v-model="queryParams.status"
                        clearable
                        placeholder="任务单状态"
                        style="width: 240px"
                        type="settlement_status"
                    ></dict-select>
                  </el-form-item>
                  <el-form-item>
                    <el-input
                        v-model="queryParams.intermediateCode"
                        placeholder="中间计量单编码"
                        style="width: 240px"
                    ></el-input>
                  </el-form-item>
                  <el-form-item>
                    <el-input
                        v-model="queryParams.settleCode"
                        placeholder="结算计量单编码"
                        style="width: 240px"
                    ></el-input>
                  </el-form-item>
                </el-form>
              </el-col>
            </el-row>
          </el-col>
        </el-row>
        <!--操作按钮区开始-->
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button
                type="primary"
                icon="el-icon-view"
                size="mini"
                v-has-menu-permi="['calcproject:settle:visapreview']"
                @click="previewVisa"
            >签证单预览
            </el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button
                type="success"
                icon="el-icon-view"
                size="mini"
                v-has-menu-permi="['calcproject:settle:maintainpreview']"
                @click="previewMaintenance"
            >维修档案预览
            </el-button>
          </el-col>
          <el-col :span="1.5">

          </el-col>
          <el-col :span="1.5">
            <el-button
                type="warning"
                icon="el-icon-view"
                size="mini"
                v-has-menu-permi="['calcproject:settle:repairpreview']"
                @click="previewRepair"
            >修复反馈预览
            </el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="primary" v-has-menu-permi="['calcproject:settle:updatevisacheck']" icon="el-icon-edit" size="mini" @click="openEditUserInfoDialog"
            >人员信息修改
            </el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button
              type="warning"
              icon="el-icon-view"
              size="mini"
              v-has-permi="['settlement:repository:regenerate']"
              @click="handleRegenerate"
            >重新生成报表
            </el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button
              type="danger"
              icon="el-icon-refresh-left"
              size="mini"
              v-has-permi="['projConstructionFinished:finished:withdraw']"
              @click="handleWithdraw"
            >撤回
            </el-button>
          </el-col>
          <right-toolbar
              :columns="columns"
              :showSearch.sync="showSearch"
              @queryTable="handleQuery"
          ></right-toolbar>
        </el-row>
        <el-row>
          <div class="draggable">
            <el-table v-adjust-table
                size="mini"
                style="width: 100%"
                v-loading="loading"
                border
                :data="tableData"
                row-key="settleId"
                ref="dataTable"
                @row-click="handleRowClick"
                @selection-change="handleSelectionChange2"
                stripe
                highlight-current-row
                @expand-change="loadData"
                :height="
                showSearch ? 'calc(100vh - 430px)' : 'calc(100vh - 370px)'
              "
            >
              <el-table-column type="selection" width="50" align="center"/>
              <el-table-column type="expand">
                <template slot-scope="props">
                  <el-table v-adjust-table :data="props.row.methodList" style="width: 100%">
                    <el-table-column
                        align="center"
                        label=""
                        width="50"
                    />
                    <el-table-column
                        align="center"
                        label="序号"
                        type="index"
                        width="50"
                    />
                    <el-table-column
                        align="center"
                        label="子目号"
                        prop="schemeCode"
                    >
                    </el-table-column>
                    <el-table-column
                        align="center"
                        label="子目名称"
                        prop="schemeName"
                    >
                    </el-table-column>
                    <el-table-column align="center" label="单价" prop="price">
                    </el-table-column>
                    <el-table-column
                        align="center"
                        label="计算式"
                        prop="calcDesc"
                    >
                    </el-table-column>
                    <el-table-column align="center" label="数量" prop="num">
                    </el-table-column>
                    <el-table-column align="center" label="金额" prop="amount">
                    </el-table-column>
                    <el-table-column align="center" label="备注" prop="remark">
                    </el-table-column>
                  </el-table>
                  <pagination
                      v-show="props.row.totalNum > 0"
                      :limit.sync="props.row.queryData.pageSize"
                      :page.sync="props.row.queryData.pageNum"
                      :total="props.row.totalNum"
                      @pagination="getRowDetailList(props.row)"
                  />
                </template>
              </el-table-column>
              <el-table-column
                  label="序号"
                  align="center"
                  type="index"
                  width="50"
              />
              <template v-for="(column, index) in columns">
                <el-table-column
                    :label="column.label"
                    v-if="column.visible"
                    align="center"
                    :prop="column.field"
                    :width="column.width"
                >
                  <template slot-scope="scope">
                    <dict-tag
                        v-if="column.dict"
                        :options="dict.type[column.dict]"
                        :value="scope.row[column.field]"
                    />
                    <template v-else-if="column.slots">
                      <RenderDom
                          :row="scope.row"
                          :index="index"
                          :render="column.render"
                      />
                    </template>
                    <span v-else-if="column.isTime">{{
                        parseTime(scope.row[column.field], "{y}-{m}-{d}")
                      }}</span>
                    <span v-else>{{ scope.row[column.field] }}</span>
                  </template>
                </el-table-column>
              </template>
              <el-table-column
                  label="操作"
                  fixed="right"
                  align="center"
                  width="250"
                  class-name="small-padding fixed-width"
              >
                <template slot-scope="scope">
                  <el-button
                      type="text"
                      size="small"
                      v-has-menu-permi="['calcproject:method:edit']"
                      @click="openEditListDialog(scope.row)"
                  >编辑清单
                  </el-button
                  >
                  <el-button
                      type="text"
                      size="small"
                      v-has-menu-permi="['calcproject:method:remove']"
                      @click="openDelItemDialog(scope.row)"
                  >删除新增单价子目
                  </el-button
                  >
                </template>
              </el-table-column>
            </el-table>
            <pagination
                v-show="total > 0"
                :total="total"
                :page.sync="queryParams.pageNum"
                :limit.sync="queryParams.pageSize"
                @pagination="handleQuery"
            />
          </div>
        </el-row>
      </el-col>
    </el-row>
    <el-dialog title="编辑清单" :visible.sync="editListDialog" width="90%">
      <el-table v-adjust-table
          :data="settleMethodList"
          border
          height="200px"
          ref="tableRef"
          style="width: 100%"
      >
        <el-table-column label="序号" align="center" type="index" width="50"/>
        <el-table-column
            prop="schemeCode"
            align="center"
            label="子目号"
        >
        </el-table-column>
        <el-table-column
            prop="schemeName"
            align="center"
            label="子目名称"
        >
          <template slot-scope="scope">
            <el-input v-model="scope.row.schemeName">
            </el-input>
          </template>
        </el-table-column>
        <el-table-column
            prop="unit"
            align="center"
            label="单位"
            width="100">
          <template slot-scope="scope">
            <el-input v-model="scope.row.unit">
            </el-input>
          </template>
        </el-table-column>
        <el-table-column prop="price" align="center" label="单价" width="100">
        </el-table-column>
        <el-table-column
            prop="calcDesc"
            align="center"
            label="计算式"
        >
          <template slot-scope="scope">
            <el-input
                v-model="scope.row.calcDesc"
                @change="changeCalculation(scope.row)"
            >
            </el-input>
          </template>
        </el-table-column>
        <el-table-column prop="num" align="center" label="方法数量" width="100">
          <template slot-scope="scope">
            <el-input
                v-model="scope.row.num"
                @change="changeSchemeNum(scope.row)"
            >
            </el-input>
          </template>
        </el-table-column>
        <el-table-column prop="amount" align="center" label="资金" width="100">
        </el-table-column>
        <el-table-column prop="calcStatus" label="是否计量">
          <template slot-scope="scope">
            <dict-tag
                :options="dict.type.project_clac_middle_status"
                :value="scope.row.calcStatus"
            />
          </template>
        </el-table-column>
        <el-table-column prop="remark" align="center" label="备注">
          <template slot-scope="scope">
            <el-input v-model="scope.row.remark"></el-input>
          </template>
        </el-table-column>
        <el-table-column
            label="操作"
            fixed="right"
            align="center"
            width="150"
            class-name="small-padding fixed-width"
        >
          <template slot-scope="scope">
            <el-button
                type="text"
                size="small"
                @click="openLib(scope.row)"
            >选择
            </el-button
            >
          </template>
        </el-table-column>
      </el-table>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitEditList">保存</el-button>
        <el-button @click="editListDialog = false">退出</el-button>
      </span>
    </el-dialog>
    <el-dialog
        title="删除新增单价子目"
        :visible.sync="delItemDialog"
        width="90%"
    >
      <el-table v-adjust-table
          :data="settleMethodList"
          border
          height="200px"
          ref="delTableRef"
          @selection-change="handleSelectionChange"
          style="width: 100%"
      >
        <el-table-column type="selection" width="55"></el-table-column>
        <el-table-column label="序号" align="center" type="index" width="50"/>
        <el-table-column
            prop="schemeCode"
            align="center"
            label="子目号"
            width="100"
        >
        </el-table-column>
        <el-table-column
            prop="schemeName"
            align="center"
            label="子目名称"
            width="100"
        >
        </el-table-column>
        <el-table-column prop="unit" align="center" label="单位" width="100">
        </el-table-column>
        <el-table-column prop="price" align="center" label="单价" width="100">
        </el-table-column>
        <el-table-column
            prop="calcDesc"
            align="center"
            label="计算式"
            width="100"
        />
        <el-table-column
            prop="num"
            align="center"
            label="方法数量"
            width="100"
        />
        <el-table-column prop="amount" align="center" label="资金" width="100">
        </el-table-column>
        <el-table-column prop="calcStatus" label="是否计量"/>
        <el-table-column
            prop="remark"
            align="center"
            label="备注"
            width="100"
        />
      </el-table>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitDelItem">删除</el-button>
        <el-button @click="delItemDialog = false">退出</el-button>
      </span>
    </el-dialog>
    <methods-tree schemeType="专项养护" :con-id="checkRow.conConId" is-single ref="methodsRef" @input="checkLib" ></methods-tree>
    <el-dialog
      title="编辑人员信息"
      destroy-on-close
      :visible.sync="userInfoDialog"
      :close-on-click-modal="false"
      size="50%"
    >
      <el-form  ref="userInfoRef" :model="userInfo" :rules="userInfoRules">
        <el-form-item label="验收人员" prop="visaBy" label-width="100px">
          <el-cascader
            v-model="userInfo.visaBy"
            :options="deptUserOptions"
            :props="visaProps"
            :show-all-levels="false"
            ref="visaRef"
            filterable
            clearable
            collapse-tags
            style="width: 100%"
          ></el-cascader>
        </el-form-item>
        <el-form-item label="审核人" prop="visaCheckBy" label-width="100px">
          <el-cascader
            ref="visaCheckRef"
            v-model="userInfo.visaCheckBy"
            :options="deptUserOptions"
            :props="visaCheckProps"
            :show-all-levels="false"
            filterable
            clearable
            collapse-tags
            style="width: 100%"
          ></el-cascader>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button size="small" @click="userInfoDialog = false">取 消</el-button>
        <el-button size="small" type="primary" @click="saveUserInfo">确 定</el-button>
      </span>
    </el-dialog>
    <IFramePreview ref="iframeRef" :srcdoc="preview.html" :down-url="preview.url" :file-name="preview.fileName"></IFramePreview>

  </div>
</template>

<script>
import selectTree from "@/components/DeptTmpl/selectTree.vue";
import MaintenanceTree from "@/components/MaintenanceTree/index.vue"
import { Decimal } from 'decimal.js';
import {
  fetchMethodList,
  fetchMethodListAll, getVisaCheck, preViewMaintenance, previewRepair, previewVisa, updateVisaCheck, withdraw,
} from "@/api/calculate/project/settlement";
import {
  editSettleMethodList,
  delSettleMethod,
  otherRepositoryList
} from "@/api/calculate/project/addPrice";
import RoadSection from "@/views/baseData/components/roadSection/index.vue";
import MethodsTree from "@/components/MethodsTree/index.vue";
import IFramePreview from "@/components/IFramePreview/index.vue";
import {getTreeStruct} from "@/api/tmpl";
import {regenerateReport} from "@/api/dailyMaintenance/metering/addPrice";
export default {
  name: 'AddPrice',
  components: {
    IFramePreview,
    RoadSection,
    selectTree,
    MethodsTree,
    RenderDom: {
      functional: true,
      props: {
        row: Object,
        index: Number,
        render: Function,
      },
      render(createElement, ctx) {
        const {row, index} = ctx.props;
        return ctx.props.render(row, index);
      },
    },
    MaintenanceTree
  },
  dicts: [
    "project_clac_middle_status", "settlement_status", "cost_name", "sys_asset_type", "testing_calc_status",
  ],
  props: [],
  data() {
    return {
      leftTotal: 1,
      showSearch: false,
      queryParams: {
        pageNum: 1,
        pageSize: 50,
        addUnitPrice: 1,
      },
      total: 0,
      loading: false,
      columns: [
        {
          key: 0,
          width: 150,
          field: "status",
          label: `状态`,
          visible: true,
          dict: "settlement_status",
        },
        {
          key: 1,
          width: 100,
          field: "calcStatus",
          label: `计量情况`,
          visible: true,
          dict: "testing_calc_status",
        },
        {
          key: 2,
          width: 200,
          field: "constructionCode",
          label: `任务单编号`,
          visible: true,
        },
        {
          key: 3,
          width: 200,
          field: "constructionName",
          label: `任务单名称`,
          visible: true,
        },
        {
          key: 4,
          width: 100,
          field: "sumFund",
          label: `金额`,
          visible: true,
        },
        {
          key: 5,
          width: 100,
          field: "residueFund",
          label: `剩余金额`,
          visible: true,
        },
        {
          key: 6,
          width: 200,
          field: "projectName",
          label: `项目名称`,
          visible: true,
        },
        {
          key: 7,
          width: 100,
          field: "maiSecName",
          label: `路段名称`,
          visible: true,
        },
        {
          key: 8,
          width: 100,
          field: "domainName",
          label: `管养单位`,
          visible: true,
        },
        {
          key: 9,
          width: 100,
          field: "productionFund",
          label: `安全生产费`,
          visible: true,
          slots: true,
          render: (row) => {
            return (
                <span>{row.productionFund?.toFixed(0)}</span>
            );
          }
        },
        {
          key: 10,
          width: 100,
          field: "guaranteeFund",
          label: `安全保通费`,
          visible: true,
          slots: true,
          render: (row) => {
            return (
                <span>{row.guaranteeFund?.toFixed(0)}</span>
            );
          }
        },
        {
          key: 11,
          width: 100,
          field: "supFund",
          label: `监理费`,
          visible: true,
          slots: true,
          render: (row) => {
            return (
                <span>{row.supFund?.toFixed(0)}</span>
            );
          }
        },
        {
          key: 12,
          width: 200,
          field: "conDomainName",
          label: `施工单位`,
          visible: true,
        },
        {
          key: 13,
          width: 200,
          field: "conConName",
          label: `施工合同`,
          visible: true,
        },
        {
          key: 12,
          width: 200,
          field: "supDomainName",
          label: `监理单位`,
          visible: true,
        },
        {
          key: 12,
          width: 200,
          field: "designDomainName",
          label: `设计单位`,
          visible: true,
        },
        {
          key: 14,
          width: 100,
          field: "endTime",
          label: `完工日期`,
          visible: true,
        },
        {
          key: 12,
          width: 100,
          field: "defLiaPer",
          label: `缺陷责任期(月)`,
          visible: true,
        },
        {
          key: 14,
          width: 200,
          field: "acceptancePerson",
          label: `验收人员`,
          visible: true,
        },
      ],
      tableData: [],
      rowData: {},
      // 左侧组织树
      realNav: true,
      keyword: "",
      relaOptions: [],
      filteredTreeData: [],
      editListDialog: false,
      delItemDialog: false,
      settleMethodList: [],
      delIds: [],
      checkRow: {},
      methodIndex: 0,
      settleId: '',
      preview: {
        html: '',
        url: '',
        fileName: ''
      },
      checkData: {},
      deptUserOptions: [],
      visaProps: {
        multiple: true, //是否多选
        value: "id",
        emitPath: false,
      },
      visaCheckProps: {
        multiple: false, //是否多选
        value: "id",
        emitPath: false,
      },
      userInfoDialog: false,
      userInfo: {
        id: '',
        visaBy: '',
        visaCheckBy: '',
        visaCheckName: '',
        visaName: ''
      },
      userInfoRules: {
        visaBy: [
          {required: true, type: 'array', message: '请选择验收人员'}
        ],
        visaCheckBy: [
          {required: true, message: '请选择审核人'}
        ]
      },
      ids: []
    };
  },
  computed: {},
  watch: {},
  created() {
    this.handleQuery();
    this.getDeptTreeDef();
  },
  mounted() {
  },
  methods: {
    /** 查询部门-用户下拉树结构 */
    getDeptTreeDef() {
      getTreeStruct({types:111}).then(response => {
        this.deptUserOptions = response.data;
      });
    },
    handleNodeClick(e) {
      this.queryParams.domainId = e.domainId || "";
      this.queryParams.maiSecId = e.maiSecId || "";
      this.handleQuery();
    },
    handleQuery() {
      this.loading = true;
      otherRepositoryList(this.queryParams).then((res) => {
        this.tableData = res.rows;
        this.total = res.total;
        this.loading = false;
        this.tableData.forEach((item) => {
          item.queryData = {
            settleId: item.settleId,
            pageNum: 1,
            pageSize: 10
          };
          item.totalNum = 0;
        });
      });
    },
    resetQuery() {
      this.queryParams = {
        pageNum: 1,
        pageSize: 50,
      };
      this.handleQuery()
    },
    loadData(row, expandedRows) {
      const isExpand = expandedRows.some(
          (item) => row.settleId === item.settleId
      );
      if (isExpand) {
        this.getRowDetailList(row);
      }
    },
    getRowDetailList(row) {
      fetchMethodList(row.queryData).then((res) => {
        this.$set(row, "totalNum", res.total || 0);
        this.$set(row, "methodList", res.rows);
      });
    },
    // 打开 编辑清单弹窗
    openEditListDialog(row) {
      this.checkRow = row
      const params = {
        settleId: row.settleId,
        flag: 1,
      };
      fetchMethodListAll(params).then((res) => {
        this.settleMethodList = res.data;
        this.editListDialog = true;
      });
    },
    // 保存编辑清单
    submitEditList() {
      editSettleMethodList(this.settleMethodList).then((res) => {
        this.$message.success("保存成功");
        this.editListDialog = false;
      });
    },
    // 打开新增单价子目弹窗
    openDelItemDialog(row) {
      const params = {
        settleId: row.settleId,
        flag: 1,
      };
      fetchMethodListAll(params).then((res) => {
        this.settleMethodList = res.data;
        this.delItemDialog = true;
      });
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.delIds = selection.map(item => item.id)
    },
    // 删除选中新增单价子目
    submitDelItem() {
      delSettleMethod(this.delIds.join(",")).then(res => {
        this.$message.success("保存成功");
        this.delItemDialog = false
      })
    },
    handleRowClick(row) {
      this.settleId = row.settleId;
      this.checkData = row
    },
    openEditUserInfoDialog() {
      if (!this.checkData.settleId) {
        this.$message.warning("请先选择一条数据！");
        return;
      }
      getVisaCheck(this.checkData.finishedId).then(res=> {
        this.userInfo = res.data
        this.userInfo.visaBy =  this.userInfo.visaBy.split(',')
        this.userInfoDialog = true
      })
    },
    saveUserInfo() {
      this.$refs.userInfoRef.validate((valid)=> {
        if (valid) {
          const formData = JSON.parse(JSON.stringify(this.userInfo))
          const visaNode = this.$refs.visaRef.getCheckedNodes()
          const visaCheckNode = this.$refs.visaCheckRef.getCheckedNodes()
          formData.visaName = visaNode.map(item=> {
            return item.label
          }).join()
          formData.visaBy = formData.visaBy.join()
          formData.visaCheckName = visaCheckNode[0].label
          updateVisaCheck(formData).then(res=> {
            this.$message.success("修改成功！");
            this.userInfoDialog = false
          })
        }
      })
    },
    previewVisa() {
      if (!this.settleId) {
        this.$message.error('请选择结算单')
        return
      }
      this.loading = true
      previewVisa(this.settleId).then(res => {
        if (res.code == 200){
          this.preview.html = res.data.html
          this.preview.url = res.data.downUrl
          this.preview.fileName = res.data.fileName
          this.$refs.iframeRef.visible = true
        }
        this.loading = false
      })
    },

    previewMaintenance() {
      if (!this.settleId) {
        this.$message.error('请选择结算单')
        return
      }
      this.loading = true
      preViewMaintenance(this.settleId).then(res => {
        if (res.code == 200){
          this.preview.html = res.data.html
          this.preview.url = res.data.downUrl
          this.preview.fileName = res.data.fileName
          this.$refs.iframeRef.visible = true
        }
        this.loading = false
      })
    },

    previewRepair() {
      if (!this.settleId) {
        this.$message.error('请选择结算单')
        return
      }
      this.loading = true
      previewRepair(this.settleId).then(res => {
        if (res.code == 200){
          this.preview.html = res.data.html
          this.preview.url = res.data.downUrl
          this.preview.fileName = res.data.fileName
          this.$refs.iframeRef.visible = true
        }
        this.loading = false
      })
    },
    changeCalculation(row) {
      if (!this.isValidMathFormula(row.calcDesc)) {
        this.$modal.msgError('计算式错误，请检查')
        return
      }
      this.$set(row, 'num', this.ceilToTwo(this.math.evaluate(row.calcDesc), row.decimalPlaces))
      this.$set(row, "amount", Math.round(new Decimal(row.num || 0).times(row.price || 0).toNumber()));
      this.total = this.formData.settleMethodList.reduce(
          (acc, curr) => acc + curr.amount,
          0
      );
    },
    changeSchemeNum(row) {
      this.$set(row, "amount", Math.round(new Decimal(row.num || 0).times(row.price || 0).toNumber()));
      this.total = this.formData.settleMethodList.reduce(
          (acc, curr) => acc + curr.amount,
          0
      );
    },
    openLib(row) {
      this.methodIndex = this.settleMethodList.indexOf(row)
      this.$refs.methodsRef.openLibModel()
    },
    // 多选框选中数据
    handleSelectionChange2(selection) {
      this.ids = selection
    },
    handleRegenerate() {
      if (this.ids.length == 0) {
        this.$modal.msgError("请勾选至少一条数据")
        return
      }
      const params = {
        idList: this.ids.map(item => item.settleId),
        type: 4
      }
      regenerateReport(params).then(res => {
        this.$modal.msgSuccess("操作成功")
      })
    },
    handleWithdraw() {
      if (!this.checkData.finishedId) {
        this.$message.warning('请先选择一条记录！')
        return
      }
      this.$confirm('确定撤回吗？', '警告', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        const params = {
          businessKey: this.checkData.finishedId
        }
        withdraw(params).then(res => {
          this.$message.success('撤回成功')
          this.handleQuery()
        })
      })
    },
    checkLib(checkData) {
      let methodData = this.settleMethodList[this.methodIndex]
      methodData.schemeCode = checkData.schemeCode
      methodData.schemeName = checkData.schemeName;
      methodData.unit = checkData.unit;
      methodData.price = checkData.price;
      methodData.amount = (methodData.price || 0) * (methodData.num || 0)
      methodData.isProduction = checkData.safetyFeeFlag;
      methodData.schemeId = checkData.id;
      methodData.calcStatus = checkData.calcStatus;
      this.$set(this.settleMethodList, this.checkIndex, methodData)
      // e.id = this.settleMethodList[this.methodIndex].id
      // this.$set(this.settleMethodList, this.methodIndex, e);
    }
  },
};
</script>

<style lang="scss" scoped>


.rightIcon {
  border: 1px solid #dcdfe6;
  border-radius: 8px;
  width: 16px;
  height: 50px;
  line-height: 50px;
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
  position: absolute;
  left: -10px;
  top: 280px;
  z-index: 10;
  background: white;
}

.rightIcon:hover {
  background-color: #dcdfe6;
}

.left-total {
  font-size: 13px;
  line-height: 28px;
  color: #606266;
  position: absolute;
  bottom: 10px;
  right: 10px;
}
</style>
<style lang="scss" scoped>
@import "@/assets/styles/business.scss";
</style>
