<template>
  <div class="app-container">
    <!-- 查询 -->
    <div class="searchBox" :style="{ 'height': queryShow ? '86px' : '48px' }">
      <el-row :gutter="20" style="width: 1000px;">
        <el-col :span="5" :offset="0">
          <el-select
            v-model="queryParams.selectedDeptIds"
            multiple
            clearable
            ref="stlectTree"
            class="custom-select"
            placeholder="请选择检查部门"
            @clear="selectClear"
            @remove-tag="removeTag"
            collapse-tags
            style="width: 100%"
          >
            <el-option
              hidden
              v-for="item in deptOptions4Qry"
              :key="item.id"
              :label="item.label"
              :value="item.id"
            >
            </el-option>
            <el-tree
              style="font-weight: 400"
              :data="deptOptions4Qry"
              :props="defaultProps"
              @check="handleNodeCheck"
              @node-click="nodeClick"
              node-key="id"
              check-on-click-node
              ref="treeRef"
              show-checkbox
            ></el-tree>
          </el-select>
        </el-col>
        <el-col :span="5" :offset="0">
          <el-input
            v-model="queryParams.examineProjectName"
            placeholder="请输入考核项目名称"
            clearable
            @keyup.enter.native="handleQuery"
          />
        </el-col>
        <el-col :span="5" :offset="0">
          <el-select v-model="queryParams.examineType" placeholder="请选择检查类型" clearable>
            <el-option v-for="dict in dict.type.examine_type" :key="dict.value" :label="dict.label"
                       :value="dict.value"/>
          </el-select>
        </el-col>
        <el-col :span="5" :offset="0">
          <el-select
            v-model="queryParams.projectStatus"
            placeholder="请选择项目状态"
            clearable
          >
            <el-option label="未下发" value="1"/>
            <el-option label="已下发" value="2"/>
          </el-select>
        </el-col>
        <el-col :span="4" :offset="0" style="display: flex; align-items: center">
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
          <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
          <el-button
            :icon="queryShow ? 'el-icon-arrow-up' : 'el-icon-arrow-down'"
            style="color: #1890ff;border-color: #badeff;background-color: #e8f4ff;"
            circle
            @click="queryShow = !queryShow"
          />
        </el-col>
      </el-row>
      <transition name="search">
        <div class="searchMoreBox" v-if="queryShow">
          <el-row :gutter="20" style="width:1000px;">
            <el-col :span="10" :offset="0">
              <el-date-picker
                v-model="queryTime"
                @change="() => {queryParams.startTime = queryTime[0] + ' 00:00:00'; queryParams.endTime = queryTime[1] + ' 23:59:59'}"
                type="daterange"
                style="width: 100%;"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                format="yyyy-MM-dd"
                value-format="yyyy-MM-dd"
              >
              </el-date-picker>
            </el-col>
          </el-row>
        </div>
      </transition>
    </div>

    <!--数据表格开始-->
    <div class="tableDiv" :style="{ 'height': queryShow ? 'calc(100% - 96px)' : 'calc(100% - 58px)' }">
      <!-- 功能按钮 -->
      <div class="btnBox">
        <el-row :gutter="10">
          <el-col :span="1.5">
            <el-button type="primary" icon="el-icon-plus" size="mini" @click="handleAdd">新增</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="success" icon="el-icon-edit" size="mini" :disabled="forEditDelDisabled" @click="handleUpdate('select')">修改
            </el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="danger" icon="el-icon-delete" size="mini" :disabled="forDeleteDelDisabled" @click="handleDelete">删除
            </el-button>
          </el-col>
        </el-row>
      </div>

      <!-- 数据表格 -->
      <el-table v-adjust-table :data="examineProjectList" :row-key="row => row.id" height="calc(100% - 98px)"
                style="width: 100%" v-loading="loading" border @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="50" align="center"/>
        <el-table-column fixed label="序号" type="index" width="50"></el-table-column>
        <el-table-column label="项目名称" align="center" prop="examineProjectName" min-width="120" show-overflow-tooltip/>
        <el-table-column label="检查类型" align="center" prop="examineType" width="120">
          <template v-slot="scope">
            <dict-tag :options="dict.type.examine_type" :value="scope.row.examineType"/>
          </template>
        </el-table-column>
        <el-table-column label="要求完成时间" align="center" prop="deadline" width="120"/>
        <el-table-column label="考核情况" align="center" width="120">
          <template v-slot="scope">
            <el-tooltip class="item" effect="dark" content="已考核管理处" placement="left">
              <span style="color: green">
                {{ scope.row.finishedTasks }}
              </span>
            </el-tooltip>
            /
            <el-tooltip class="item" effect="dark" content="全部管理处" placement="right">
              <span style="color: blue">
                {{ scope.row.totalTasks }}
              </span>
            </el-tooltip>
          </template>
        </el-table-column>
        <el-table-column label="创建人" align="center" prop="createBy" width="120"/>
        <el-table-column label="创建单位" align="center" prop="createUnit" min-width="140" show-overflow-tooltip/>
        <el-table-column label="创建时间" align="center" prop="createTime" width="140"/>
        <el-table-column label="项目状态" align="center" prop="status" width="120">
          <template v-slot="scope">
            <el-tag :type="scope.row.projectStatus === '2' ? 'success' : ''">
              {{ scope.row.projectStatus === '1' ? '未下发' : '已下发' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" fixed="right" align="center" width="250" class-name="small-padding fixed-width">
          <template v-slot="scope">
            <el-button v-if="scope.row.projectStatus === '1' || $store.getters.name === 'admin'" size="mini" type="text" icon="el-icon-edit"
                       @click="handleUpdate('click', scope.row)">修改
            </el-button>
            <el-button v-if="scope.row.projectStatus === '1' || $store.getters.name === 'admin'" size="mini" type="text" icon="el-icon-delete"
                       @click="handleDelete(scope.row)">删除
            </el-button>
            <el-button v-if="scope.row.projectStatus === '1'" size="mini" type="text" icon="el-icon-position"
                       @click="handleIssued(scope.row)">下发
            </el-button>
            <el-button size="mini" type="text" icon="el-icon-view"
                       @click="handleView(scope.row)">查看
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize"
                  @pagination="getList"/>
    </div>

    <!--数据表格结束-->

    <!-- 添加或修改考核项目对话框 -->
    <el-dialog :title="examineProjectTitle" :visible.sync="examineProjectDialog" width="45%" destroy-on-close
               :close-on-click-modal="false" append-to-body>
      <el-form ref="examineProjectForm" :model="examineProjectForm" :rules="rules" label-width="140px">
        <el-form-item label="考核项目名称" prop="examineProjectName">
          <el-input v-model="examineProjectForm.examineProjectName" :disabled="!isEdit"
                    placeholder="请输入考核项目名称"/>
        </el-form-item>
        <el-form-item label="检查类型" prop="examineType">
          <el-select v-model="examineProjectForm.examineType" :disabled="!isEdit" placeholder="请选择检查类型"
                     @change="selectedType" clearable style="width: 100%">
            <el-option v-for="dict in dict.type.examine_type" :key="dict.value" :label="dict.label"
                       :value="dict.value"/>
          </el-select>
          <!-- <el-radio-group v-model="examineProjectForm.examineType" @change="selectedType">
            <el-radio v-for="dict in dict.type.examine_type" :label="dict.value">{{ dict.label }}</el-radio>
          </el-radio-group> -->
        </el-form-item>
        <el-form-item label="检查模板" prop="examineTemplateId">
          <el-select v-model="examineProjectForm.examineTemplateId" :disabled="!isEdit" multiple
                     placeholder="请选择检查模板" style="width: 100%">
            <el-option
              v-for="item in optionsTemplate"
              :key="item.id"
              :label="item.typeName"
              :value="item.id"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="检查部门" prop="examineDepartmentsArr">
          <el-select
            style="width: 100%"
            v-model="examineProjectForm.examineDepartmentsArr"
            :disabled="!isEdit"
            multiple
            filterable
            placeholder="请选择检查部门"
            @change="onSelectedDept"
          >
            <el-option
              v-for="item in optionsDept"
              :key="item.deptId"
              :label="item.deptName"
              :value="item.deptId"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="要求完成时间" prop="deadline">
          <el-date-picker
            v-model="examineProjectForm.deadline"
            type="date"
            style="width: 100%;"
            placeholder="选择日期"
            :picker-options="pickerOptions"
            value-format="yyyy-MM-dd">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="填报人岗位" prop="selectedPosts">
          <el-select
            style="width: 100%"
            v-model="examineProjectForm.selectedPosts"
            :disabled="!isEdit"
            multiple
            filterable
            @change="handleChange"
            placeholder="请选择填报人岗位"
          >
            <el-option
              v-for="item in optionsPost"
              :key="item.postId"
              :label="item.postName"
              :value="item.postId"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="填报人" prop="selectedUsers">
          <el-select
            style="width: 100%"
            v-model="examineProjectForm.selectedUsers"
            :disabled="!isEdit"
            multiple
            filterable
            placeholder="请选择填报人"
          >
            <el-option
              v-for="item in userList"
              :key="item.userId"
              :label="item.nickName"
              :value="item.userId+'@'+item.deptId"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="考核文件" prop="url">
          <FileUpload
            previewWidth="80%"
            v-model="examineProjectForm.fileId"
            :forView="!isEdit"
            :limit="1"
            :fileType="['docx', 'pdf']"
            :showTitle="true"
            :owner-id="examineProjectForm.fileId"
            storage-path="/examine/project/"
            platform="fykj"
            @input="(value) => (this.examineProjectForm.fileId = value[0])"
          ></FileUpload>
        </el-form-item>
        <el-form-item label="任务状态">
          <el-radio-group v-model="examineProjectForm.projectStatus" :disabled="!isEdit">
            <el-radio label="1">未下发</el-radio>
            <el-radio label="2">已下发</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="备注">
          <el-input type="textarea" v-model="examineProjectForm.remarks" :disabled="!isEdit"></el-input>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button v-if="isEdit" @click="examineProjectDialog = false">取 消</el-button>
        <el-button v-if="isEdit" type="primary" @click="submitForm">保 存</el-button>
      </span>
    </el-dialog>


    <!-- 添加或修改考核项目对话框 -->
    <el-dialog title="查看考核项目" :visible.sync="examineProjectViewDialog" width="80%" destroy-on-close
               :close-on-click-modal="false" append-to-body>
      <el-row>
        <el-tabs value="project">
          <el-tab-pane label="考核项目" name="project"></el-tab-pane>
        </el-tabs>
        <el-descriptions class="margin-top" :column="4" size="medium" border :label-style="labelStyle">
          <el-descriptions-item>
            <template slot="label">
              考核项目名称
            </template>
            {{ projectWithSectTaskStatus.projectName }}
          </el-descriptions-item>
          <el-descriptions-item>
            <template slot="label">
              检查类型
            </template>
            {{ switchExamineTypeName(projectWithSectTaskStatus.examineType) }}
          </el-descriptions-item>
          <el-descriptions-item>
            <template slot="label">
              要求完成时间
            </template>
            {{ projectWithSectTaskStatus.deadline }}
          </el-descriptions-item>
          <el-descriptions-item>
            <template slot="label">
              检查模板
            </template>
            {{ projectWithSectTaskStatus.examineTplNames }}
          </el-descriptions-item>
          <el-descriptions-item>
            <template slot="label">
              项目状态
            </template>
            <el-tag :type="projectWithSectTaskStatus.projectStatus === '已下发' ? 'success' : ''">
              {{ projectWithSectTaskStatus.projectStatus }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item>
            <template slot="label">
              创建时间
            </template>
            {{ projectWithSectTaskStatus.createTime }}
          </el-descriptions-item>
          <el-descriptions-item>
            <template slot="label">
              创建人
            </template>
            {{ projectWithSectTaskStatus.createBy }}
          </el-descriptions-item>
          <el-descriptions-item>
            <template slot="label">
              创建单位
            </template>
            {{ projectWithSectTaskStatus.createUnit }}
          </el-descriptions-item>

          <el-descriptions-item>
            <template slot="label">
              考核文件
            </template>
            <FileUpload
              previewWidth="80%"
              v-model="projectWithSectTaskStatus.fileId"
              :forView="projectWithSectTaskStatus.projectStatus==='已下发'"
              :showTitle="true"
              :isShowTip="false"
              storage-path="/examine/project/"
              :owner-id="projectWithSectTaskStatus.fileId"
              v-if="projectWithSectTaskStatus.fileId"
              @input="(value) => (projectWithSectTaskStatus.fileId = value[0])"
            >
            </FileUpload>
          </el-descriptions-item>

          <el-descriptions-item>
            <template slot="label">
              备注信息
            </template>
            {{ projectWithSectTaskStatus.remarks }}
          </el-descriptions-item>
        </el-descriptions>
      </el-row>
      <el-tabs value="tasks">
        <el-tab-pane label="完成情况" name="tasks"></el-tab-pane>
      </el-tabs>
      <el-row>
        <el-table
          :data="projectWithSectTaskStatus.taskStatusList"
          style="width: 100%;margin-bottom: 20px;"
          row-key="id"
          border
          default-expand-all
          :height="'calc(100vh - 400px)'">
          <el-table-column prop="subject" label="管理处" width="400">
            <template v-slot="scope">
              <span style="fontWeight:bold">{{ scope.row.deptName }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="status" label="状态">
            <template v-slot="scope">
              <span style="fontWeight:bold">{{ scope.row.status }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="completeTime" label="要求完成时间">
            <template v-slot="scope">
              <span style="fontWeight:bold">{{ scope.row.deadline }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="completeTime" label="任务最后更新时间">
            <template v-slot="scope">
              <span style="fontWeight:bold">{{ scope.row.completeTime }}</span>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="100" align="center">
            <template v-slot="scope">
              <el-button size="mini" type="text" icon="el-icon-view"
                         v-if="scope.row.status !== '未下发'" @click="handleViewTask(scope.row)">查看
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-row>
    </el-dialog>
    <EvaluationDialog :resultData="taskResult" :visible="examineDialogVisible" :isView="true"
                      :onDialogClose="onEvaludateDialogClose"/>
  </div>
</template>

<script>
import Treeselect from '@riophae/vue-treeselect';
import selectTreeCheckbox from '@/components/DeptTmpl/selectTreeCheckbox.vue';
import deptTreeCheckbox from '@/components/DeptTmpl/deptTreeCheckbox.vue';
import {deptTreeSelect} from '@/api/tmpl';
import {getMaintenanceSectionListAll} from '@/api/baseData/common/routeLine';

import {findFileUser, findFillDept, findFillPost} from '@/api/system/user';
import {getExamineTaskResultByTaskId} from "@/api/examine/task/examineTask";
import EvaluationDialog from '../evaluation_dialog.vue';
import {
  addExamineProject,
  deleteExamineProject,
  editExamineProject,
  getExamineTypeByType,
  getExamineTypeList,
  getSectTaskStatusList,
  issuedProject,
  queryExamineProjectList
} from '@/api/examine/project/examineProject';

export default {
  name: 'examine_project',
  components: {
    Treeselect,
    selectTreeCheckbox,
    deptTreeCheckbox,
    EvaluationDialog
  },
  dicts: [
    'examine_type',  // 考核类型
  ],
  data() {
    return {
      queryShow: false, // 隐藏筛选显隐
      deptOptions4Qry: [],
      routeOptions4Qry: [],
      //部门的选项
      optionsDept: [],
      //岗位选项
      selectedPosts: [],
      //人员选项
      selectedUsers: [],
      //路段的选项
      routeOptions: [],
      //模板的选项
      optionsTemplate: [],
      // 遮罩层
      loading: false,
      // 选中数组
      ids: [],
      tableSelection: [], // 表格选中数据
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: false,
      dictType: [],
      // 总条数
      total: 0,
      // 填报任务表格数据
      examineProjectList: [],
      // 是否显示弹出层
      examineProjectDialog: false,
      // 查看对话框
      examineProjectViewDialog: false,
      labelStyle: {
        'background-color': '#f5f7fa',
        width: '120px',
        color: '#606266 !important',
        fontWeight: '500',
        'text-align': 'center'
      },
      // 弹出层标题
      examineProjectTitle: '',
      //是否编辑
      isEdit: true,
      roadSectionsMap: {},
      examineDialogVisible: false,
      taskResult: {task: {}, detailList: []},
      queryTime: [], // 查询时间
      //角色的选项
      optionsPost: [],
      pickerOptions: { //禁用当前日期之前的日期
        disabledDate(time) {
          return time.getTime() < Date.now() - 8.64e7;
        },
      },
      //用户列表
      userList: [],
      defaultProps: {
        children: 'children',
        label: 'label',
      },
      projectWithSectTaskStatus: {taskStatusList: []},
      // 表单参数
      examineProjectForm: {
        id: '',
        examineProjectName: '',
        examineType: '',
        examineTemplateId: [],
        examineDepartments: '',
        examineDepartmentsArr: [],
        examineRoadSectionIds: [],
        selectedPosts: [],
        selectedUsers: [],
        projectStatus: '1',
        fileId: new Date().getTime().toString(),
        status: '1',
        remarks: ''
      },
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        selectedDeptIds: [],
        selectedSectIds: [],
        examineProjectName: '',
        examineType: '',
        projectStatus: '',
      },
      // 表单校验
      rules: {
        examineProjectName: [
          {required: true, message: '考核项目名称不能为空', trigger: 'blue'},
        ],
        examineType: [
          {required: true, message: '检查类型不能为空', trigger: 'blue'},
        ],
        examineTemplateId: [
          {required: true, message: '未选择考核模板', trigger: 'blue'},
        ],
        examineDepartmentsArr: [
          {required: true, message: '未选择检查模板', trigger: 'blue'},
        ],
        examineRoadSectionIds: [
          {required: true, message: '未选择检查路段', trigger: 'blue'},
        ],
        deadline: [
          {required: true, message: '未填写要求完成时间', trigger: 'blue'},
        ],
        selectedPosts: [
          {required: true, message: '未选择填报人岗位', trigger: 'blue'},
        ],
        selectedUsers: [
          {required: true, message: '未选择填报人', trigger: 'blue'},
        ],
      },
    };
  },
  watch: {
    'queryParams.selectedDeptIds': {
      handler(val) {
        this.$nextTick(() => {
          if (!val) {
            this.selectClear();
            this.deptChange([]);
          } else if (val && !val.length) {
            this.deptChange(val);
          }
        });
      },
      deep: true,
    },
  },
  computed: {
    forEditDelDisabled() {
      if (this.tableSelection.length !== 1) {
        return true;
      }
      const selectedItem = this.tableSelection[0];
      if (selectedItem.projectStatus !== '1') {
        return true;
      }
      return false;
    },
    forDeleteDelDisabled() {
      if (this.tableSelection.length === 0) {
        return true;
      }
      const allStatusAreOne = this.tableSelection.every(item => item.projectStatus === '1');
      return !allStatusAreOne;
    },
    formatDeptName() {
      return (row) => {
        const dept = this.optionsDept.find(option => option.deptId === parseInt(row.maintenanceUnitId));
        let deptName = dept ? dept.deptName : '';
        row.deptName = deptName;
        return deptName;
      };
    }
  },
  created() {
    this.getList();
    this.getDeptList()
  },
  methods: {
    handleNodeCheck(node, nodes) {
      this.queryParams.selectedDeptIds =
        nodes && nodes.checkedNodes ? nodes.checkedNodes.map((v) => v.id) : [];
      this.deptChange(this.queryParams.selectedDeptIds);
      this.$forceUpdate();
    },
    nodeClick(item) {
      const {id, label} = item;
      this.treeValue.id = id;
      this.treeValue.label = label;
    },
    selectClear() {
      if (this.$refs.treeRef) {
        // 清空树选中数据
        this.$refs.treeRef.setCheckedKeys([]);
        // 清空表单数据
        this.queryParams.selectedDeptIds = [];
        this.queryParams.selectedSectIds = '';
      }
    },
    removeTag(tag) {
      let checkedNodes = this.$refs.treeRef.getCheckedNodes();
      // 删除节点
      for (let i = 0; i < checkedNodes.length; i++) {
        if (checkedNodes[i].id == tag) {
          checkedNodes.splice(i, 1);
          break;
        }
      }
      // 设置 tree 选中的节点
      this.$refs.treeRef.setCheckedNodes(checkedNodes);
      // 更新表单数据并触发搜索
      this.queryParams.selectedDeptIds = checkedNodes.map((node) => node.id);
      this.deptChange(this.queryParams.selectedDeptIds);
    },
    deptChange(e) {
      this.queryParams.selectedSectIds = [];
      getMaintenanceSectionListAll({departmentIdList: e}).then((res) => {
        if (res.code == 200) {
          this.routeOptions4Qry = res.data || [];
        }
      });

    },

    getList() {
      this.loading = true;
      queryExamineProjectList(this.queryParams).then((response) => {
        this.examineProjectList = response.rows
        this.total = response.total;
        this.loading = false;
      });
    },
    getDeptList() {
      findFillDept().then((res) => {
        if (res.code === 200) {
          this.optionsDept = res.data;
        }
      });
      deptTreeSelect({types: '201'}).then((response) => {
        this.deptOptions4Qry = response.data;
      });
    },
    /** 搜索按钮操作 */
    handleQuery() {
      let qparams = this.queryParams;
      this.queryParams.pageNum = 1;
      this.queryParams.deptIds = (qparams.selectedDeptIds && qparams.selectedDeptIds.length) ? qparams.selectedDeptIds.join(",") : '';
      this.queryParams.sectIds = (qparams.selectedSectIds && qparams.selectedSectIds.length) ? qparams.selectedSectIds.join(",") : '';
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.queryParams = {};
      this.queryTime = []
      this.getList()
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id);
      this.tableSelection = selection
      this.single = selection.length != 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    async handleAdd() {
      this.isEdit = true;
      this.resetForm();
      this.examineProjectTitle = '添加考核项目';
      let postsRes = await findFillPost();
      this.optionsPost = postsRes.data;
      let etLstRet = await getExamineTypeList();
      this.optionsTemplate = etLstRet.data;
      this.examineProjectDialog = true;
    },
    handleChange(e) {
      findFileUser({
        selectedPosts: this.examineProjectForm.selectedPosts,
        selectedDepts: this.examineProjectForm.examineDepartmentsArr,
      }).then((res) => {
        if (res.code === 200) {
          this.userList = res.data;
          this.examineProjectForm.selectedUsers = this.userList.map((user) => user.userId + '@' + user.deptId);
        }
      });
    },
    selectedType() {
      let type = this.examineProjectForm.examineType;
      this.examineProjectForm.examineTemplateId = [];
      getExamineTypeByType({type}).then((res) => {
        if (res.code === 200) {
          this.optionsTemplate = res.data;
        }
      });
    },
    async onSelectedDept() {
      /*      // 构建请求参数，包含选中的部门ID列表
            let params0 = { departmentIdList: this.examineProjectForm.examineDepartmentsArr }
            // 构建请求参数，包含选中的岗位和部门ID列表
            let params1 = { selectedPosts: this.examineProjectForm.selectedPosts, selectedDepts: this.examineProjectForm.examineDepartmentsArr }

            // 并发请求获取路段列表和用户列表
            const [sectRes, userRes] = await Promise.all([
              getMaintenanceSectionListAll(params0),
              findFileUser(params1)
            ]);

            // 更新路段选项列表
            this.routeOptions = sectRes.data;
            // 更新用户列表
            this.userList = userRes.data;
            // 更新选中的用户列表，格式为 userId@deptId
            this.examineProjectForm.selectedUsers = this.userList.map((user) => user.userId + '@' + user.deptId);*/

      findFileUser({
        selectedPosts: this.examineProjectForm.selectedPosts,
        selectedDepts: this.examineProjectForm.examineDepartmentsArr
      }).then((res) => {
        if (res.code === 200) {
          this.userList = res.data;
          this.examineProjectForm.selectedUsers = this.userList.map((user) => user.userId + '@' + user.deptId);
        }
      })
    },
    /** 修改按钮操作 */
    async handleUpdate(type, row) {
      this.isEdit = true;
      this.examineProjectTitle = '修改考核项目';
      if (type === 'click') {
        await this.loadForm(row);
      }else {
        await this.loadForm(this.tableSelection[0]);
      }
      this.examineProjectDialog = true;
    },
    async handleView(row) {
      this.isEdit = false;
      let stsLstRes = await getSectTaskStatusList(row.id);
      this.projectWithSectTaskStatus = stsLstRes.data;
      this.examineProjectViewDialog = true;
    },
    async loadForm(row) {
      this.examineProjectForm = {...row};
      if (!this.examineProjectForm.fileId) {
        this.examineProjectForm.fileId = new Date().getTime().toString()
      }
      if (row.examineDepartments) {
        this.examineProjectForm.examineDepartmentsArr = row.examineDepartments.split(',').map(id => parseInt(id.trim(), 10));
      } else {
        this.examineProjectForm.examineDepartmentsArr = [];
      }
      let type = this.examineProjectForm.examineType;
      let etLstRet = await getExamineTypeByType({type});
      this.optionsTemplate = etLstRet.data;
      if (row.examineTemplateId) {
        this.examineProjectForm.examineTemplateId = row.examineTemplateId.split(',');
      } else {
        this.examineProjectForm.examineTemplateId = [];
      }

      let postsRes = await findFillPost();
      this.optionsPost = postsRes.data;

      if (row.fillPosts) {
        this.examineProjectForm.selectedPosts = row.fillPosts.split(',').map(id => parseInt(id));
      } else {
        this.examineProjectForm.selectedPosts = [];
      }
      this.onSelectedDept()

      if (row.fillUsers) {
        this.examineProjectForm.selectedUsers = row.fillUsers.split(',');
      } else {
        this.examineProjectForm.selectedUsers = [];
      }

      if (row.roadSectionIds) {
        this.examineProjectForm.examineRoadSectionIds = row.roadSectionIds.split(',');
      } else {
        this.examineProjectForm.examineRoadSectionIds = [];
      }
    },
    handleIssued(row) {
      if (row.examineDepartments) {
        issuedProject(row.id).then((res) => {
          if (res.code === 200) {
            this.$message.success('操作成功！');
            this.getList();
          } else {
            this.$message.error(res.msg);
          }
        })
      } else {
        this.$message.error('请选择检查部门！');
        return;
      }
    },
    /** 提交按钮 */
    submitForm: function () {
      this.$refs['examineProjectForm'].validate(async (valid) => {
        if (!valid) return;
        try {
          let response;
          if (this.examineProjectForm.examineTemplateId && this.examineProjectForm.examineTemplateId.length > 0) {
            const tplIds = this.examineProjectForm.examineTemplateId.join(',');
            this.examineProjectForm.examineTemplateIds = tplIds;
          }
          if (this.examineProjectForm.examineDepartmentsArr && this.examineProjectForm.examineDepartmentsArr.length > 0) {
            const selectedDepartments = this.examineProjectForm.examineDepartmentsArr.join(',');
            this.examineProjectForm.examineDepartments = selectedDepartments;
          }
          // if(this.examineProjectForm.examineRoadSectionIds && this.examineProjectForm.examineRoadSectionIds.length > 0){
          //   const selectedRoadSectionIds = this.examineProjectForm.examineRoadSectionIds.join(',');
          //   this.examineProjectForm.roadSectionIds = selectedRoadSectionIds;
          // }
          if (this.examineProjectForm.selectedPosts && this.examineProjectForm.selectedPosts.length > 0) {
            const fillPosts = this.examineProjectForm.selectedPosts.join(',');
            this.examineProjectForm.fillPosts = fillPosts;
          }
          if (this.examineProjectForm.selectedUsers && this.examineProjectForm.selectedUsers.length > 0) {
            const fillUsers = this.examineProjectForm.selectedUsers.join(',');
            this.examineProjectForm.fillUsers = fillUsers;
          }
          // 判断是更新还是新增
          if (this.examineProjectForm.id) {
            response = await editExamineProject(this.examineProjectForm);
            console.log('数据更新结果：', response);
          } else {
            response = await addExamineProject(this.examineProjectForm);
            console.log('数据新增结果：', response);
          }
          // 检查返回结果
          if (response.code === 200) {
            this.$message.success('操作成功！');
            this.examineProjectDialog = false;
            await this.getList(); // 刷新
          }
        } catch (error) {
          this.$message.error('操作失败，请重试！');
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      if (row.id) {
        const id = row.id;
        this.$modal
          .confirm('是否确认删除考核项目名称为' + row.examineProjectName + '的数据项？')
          .then(function () {
            return deleteExamineProject(id);
          })
          .then(() => {
            this.getList();
            this.$modal.msgSuccess('删除成功');
          });
      } else {
        if (this.ids.length > 0) {
          this.$modal
            .confirm('是否确认删除选中的' + this.ids.length + '条数据项？')
            .then(() => {
              let delArray = [];
              this.ids.forEach((item) => {
                delArray.push(deleteExamineProject(item));
              });
              Promise.all(delArray).then(
                () => this.$modal.msgSuccess('删除成功') || this.getList()
              );
            });
        }
      }
    },
    resetForm() {
      this.examineProjectForm = {
        id: '',
        examineProjectName: '',
        examineType: '',
        examineTemplateId: [],
        examineDepartments: '',
        examineRoadSectionIds: [],
        selectedPosts: [],
        selectedUsers: [],
        fileId: new Date().getTime().toString(),
        projectStatus: '1',
        status: '1',
        remarks: ''
      };
    },
    handleViewTask(task) {
      getExamineTaskResultByTaskId(task.projectId, task.taskId).then((res) => {
        let resData = res.data;
        // resData.task.deptName=task.deptName;
        this.taskResult = resData
        this.examineDialogVisible = true
        this.isView = true;
      });
    },
    onEvaludateDialogClose() {
      this.examineDialogVisible = false;
    },
    switchExamineTypeName(value) {
      const examineTypeDict = this.dict.type.examine_type;
      const item = examineTypeDict.find(dictItem => dictItem.value === value);
      return item ? item.label : value;
    }
  },
};
</script>
<style scoped lang="scss">
.app-container form:first-child .el-select,
.app-container form:nth-child(2) .el-select,
.app-container form:nth-child(2) ::v-deep .el-form-item__content,
.app-container form:first-child ::v-deep .el-form-item__content {
  width: 240px;
}

.app-container form:first-child .el-form-item:last-child ::v-deep .el-form-item__content {
  width: auto;
}

.app-container {
  padding: 10px;
  background-color: #c0c0c0;
  box-sizing: border-box;
}

.searchBox {
  padding: 10px;
  background: #fff;
  border-radius: 10px;
  transition: all .1s linear;
  display: flex;
  flex-direction: column;

  .searchMoreBox {
    min-width: 192px;
    margin-top: 10px;
    display: flex;
    align-items: center;
    flex-direction: row;
  }
}

.dialog-content {
  max-height: 170vh;
  overflow-y: auto;
}

.dialog-footer {
  text-align: right;
}

.tableDiv {
  margin-top: 10px;
  background-color: white;
  padding-bottom: 10px;
  border-radius: 10px;
  transition: all .1s linear;
  display: flex;
  flex-direction: column;

  .btnBox {
    padding: 10px;
  }

  ::v-deep .el-upload-list__item {
    width: 42px;
    height: 42px;
    margin: 0px 5px -6px 0 !important;
  }

  ::v-deep .el-upload--picture-card {
    width: 42px;
    height: 42px;
    margin: 4px 0;
  }

  ::v-deep .el-upload-list__item-actions {
    font-size: 1rem;
  }

  ::v-deep .el-upload-list__item-actions span + span {
    margin-left: 5px !important;
  }
}

::v-deep .el-dialog {
  display: flex;
  flex-direction: column;
  margin: 0 !important;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  max-height: calc(100% - 30px);
  max-width: calc(100% - 30px);
}

::v-deep .el-dialog .el-dialog__body {
  flex: 1;
  overflow: auto;
}

.cascade-selection {
  display: flex;
  align-items: center;

  .tree-select-mini {
    font-size: 13px;
    font-weight: unset;

    ::v-deep .vue-treeselect__control {
      height: 26px;
      line-height: 26px;

      .vue-treeselect__placeholder,
      .vue-treeselect__single-value {
        line-height: 26px;
      }
    }

    ::v-deep .vue-treeselect__menu {
      .vue-treeselect__label {
        font-weight: unset;
        color: #606266;
      }
    }
  }

  .tree-select-small {
    font-size: 13px;

    ::v-deep .vue-treeselect__control {
      height: 30px;
      line-height: 30px;
      font-weight: 200;

      .vue-treeselect__placeholder,
      .vue-treeselect__single-value {
        line-height: 30px;
      }
    }

    ::v-deep .vue-treeselect__menu {
      .vue-treeselect__label {
        font-weight: unset;
        color: #606266;
      }
    }
  }

  /* 输入框超出隐藏，不换行*/
  ::v-deep .el-select__tags {
    flex-wrap: nowrap;
    overflow: auto;
  }

  ::v-deep .el-scrollbar {
    .el-select-dropdown__item {
      padding: 0;
    }
  }

  .custom-select {
    .el-select-dropdown__item {
      padding: 0;
      background-color: #0f0;
      /* 修改背景颜色为绿色 */
    }
  }
}
</style>
