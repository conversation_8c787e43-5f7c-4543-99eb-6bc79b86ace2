<template>
  <div v-loading="loading" class="culvert-info-edit" style="overflow-y: auto;overflow-x: hidden;margin-bottom: 15px;" >
    <el-tabs v-model="activeName" type="card">
      <el-tab-pane label="基本信息" name="0" />
      <el-tab-pane label="结构技术" name="1" />
      <el-tab-pane label="档案资料" name="2" />
    </el-tabs>
    <div class="culvert-info-edit-base" :style="{ height: formH + 'px' }">
      <el-form
        ref="ruleFormEl"
        :model="ruleForm"
        :rules="rules"
        label-width="100px"
      >
        <template v-if="activeName == '0'">
          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item label="管理处" prop="managementMaintenanceId">
                <treeselect
                  :options="deptOptions"
                  :show-count="true"
                  :readonly="true"
                  placeholder="管理处"
                  v-model="ruleForm.managementMaintenanceId"
                  style="pointer-events: none"
                />
              </el-form-item>
              <el-form-item label="路线等级" prop="routeLevel">
                <el-select
                  v-model="ruleForm.routeLevel"
                  clearable
                  filterable
                  style="width: 100%; pointer-events: none"
                >
                  <el-option
                    v-for="dict in dict.type.bridge_route_level"
                    :key="dict.value"
                    :label="dict.label"
                    :value="dict.value"
                  >
                  </el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="涵洞编码" prop="culvertCode">
                <el-input
                  v-model="ruleForm.culvertCode"
                  placeholder="请输入涵洞编码"
                  readonly
                />
              </el-form-item>
              <el-form-item label="匝道编码" prop="rampId">
                <el-input
                  v-model="ruleForm.rampId"
                  placeholder="请输入匝道编码"
                  readonly
                />
              </el-form-item>
              <el-form-item label="中心桩号">
                <PileInput
                  v-model="ruleForm.centerStake"
                  placeholder="请输入中心桩号"
                  readonly
                />
              </el-form-item>
              <el-form-item label="进洞口图片">
                <ImageUpload
                  :limit="1"
                  :isShowTip="false"
                  v-model="ruleForm.entranceHoleImageId"
                  storage-path="/base/culvert/culvertInfo"
                />
              </el-form-item>
            </el-col>

            <el-col :span="8">
              <el-form-item label="养护路段" prop="maintenanceSectionId">
                <el-select
                  :disabled="false"
                  remote
                  v-model="ruleForm.maintenanceSectionId"
                  placeholder="养护路段"
                  style="width: 100%; pointer-events: none"
                >
                  <el-option
                    v-for="item in routeOptions"
                    :key="item.maintenanceSectionId"
                    :label="item.maintenanceSectionName"
                    :value="item.maintenanceSectionId"
                  >
                  </el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="路段类型" prop="sectionType">
                <el-select
                  v-model="ruleForm.sectionType"
                  clearable
                  filterable
                  style="width: 100%; pointer-events: none"
                >
                  <el-option
                    v-for="dict in dict.type.sys_route_type"
                    :key="dict.value"
                    :label="dict.label"
                    :value="dict.value"
                  >
                  </el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="涵洞类型" prop="culvertType">
                <el-select
                  v-model="ruleForm.culvertType"
                  clearable
                  filterable
                  style="width: 100%; pointer-events: none"
                >
                  <el-option
                    v-for="dict in dict.type.sys_culvert_type"
                    :key="dict.value"
                    :label="dict.label"
                    :value="dict.value"
                  >
                  </el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="设计荷载" prop="designLoad">
                <el-select
                  v-model="ruleForm.designLoad"
                  clearable
                  filterable
                  style="width: 100%; pointer-events: none"
                >
                  <el-option
                    v-for="dict in dict.type.bridge_design_load"
                    :key="dict.value"
                    :label="dict.label"
                    :value="dict.value"
                  >
                  </el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="施工桩号">
                <PileInput
                  v-model="ruleForm.constructionStake"
                  placeholder="请输入施工桩号"
                  readonly
                />
              </el-form-item>
              <el-form-item label="出洞口图片">
                <!-- <ImagePreview
                  :owner-id="ruleForm.exitHoleImageId"
                  v-if="ruleForm.exitHoleImageId"
                /> -->
                <ImageUpload
                  :limit="1"
                  :isShowTip="false"
                  :disabled="true"
                  v-model="ruleForm.exitHoleImageId"
                  storage-path="/base/culvert/culvertInfo"
                  style="pointer-events: none"
                />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="路线编码" prop="routeCode">
                <el-select
                  v-model="ruleForm.routeCode"
                  placeholder="路线编码"
                  style="width: 100%; pointer-events: none"
                  @change="onRouteCodeChange"
                >
                  <el-option
                    v-for="item in routeList"
                    :key="item.routeId"
                    :label="item.routeName"
                    :value="item.routeId"
                  >
                  </el-option>
                </el-select>
              </el-form-item>
              <el-form-item
                label="管养分处"
                prop="managementMaintenanceBranchId"
              >
                <treeselect
                  v-model="ruleForm.managementMaintenanceBranchId"
                  :options="deptOptions"
                  :show-count="true"
                  placeholder="请选择管养分处"
                  style="pointer-events: none"
                />
              </el-form-item>
              <el-form-item label="建成时间">
                <el-date-picker
                  v-model="ruleForm.buildDate"
                  type="date"
                  placeholder="选择建成时间"
                  value-format="yyyy-MM-dd"
                  style="width: 100%; pointer-events: none"
                />
              </el-form-item>
              <el-form-item label="涵洞跨径(m)" prop="culvertSpan">
                <el-input-number
                  v-model="ruleForm.culvertSpan"
                  style="width: 100%; pointer-events: none"
                  controls-position="right"
                  placeholder="请输入涵洞跨径"
                />
              </el-form-item>
            </el-col>
          </el-row>
        </template>

        <template v-if="activeName == '1'">
          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item label="涵身长度(米)" prop="culvertLength">
                <el-input-number
                  v-model="ruleForm.culvertLength"
                  style="width: 100%; pointer-events: none"
                  controls-position="right"
                  placeholder="请输入涵身长度"
                />
              </el-form-item>
              <el-form-item label="进口形式" prop="inletPortForm">
                <el-input
                  v-model="ruleForm.inletPortForm"
                  placeholder="请输入进口形式"
                  readonly
                />
              </el-form-item>
              <el-form-item
                label="涵底纵坡"
                prop="culvertBottomLongitudinalRamp"
              >
                <el-input
                  v-model="ruleForm.culvertBottomLongitudinalRamp"
                  placeholder="请输入涵底纵坡"
                  readonly
                />
              </el-form-item>
              <el-form-item label="路面宽度(米)" prop="pavementWidth">
                <el-input-number
                  v-model="ruleForm.pavementWidth"
                  style="width: 100%; pointer-events: none"
                  controls-position="right"
                  placeholder="请输入路面宽度"
                  readonly
                />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="孔径(米)" prop="aperture">
                <el-input-number
                  v-model="ruleForm.aperture"
                  style="width: 100%; pointer-events: none"
                  controls-position="right"
                  placeholder="请输入涵身长度"
                  readonly
                />
              </el-form-item>
              <el-form-item label="出口形式" prop="exitForm">
                <el-input
                  v-model="ruleForm.exitForm"
                  placeholder="请输入出口形式"
                  readonly
                />
              </el-form-item>
              <el-form-item label="涵底铺砌" prop="culvertBottomPave">
                <el-input
                  v-model="ruleForm.culvertBottomPave"
                  placeholder="请输入涵底铺砌"
                  readonly
                />
              </el-form-item>
              <el-form-item label="路基宽度(米)" prop="subgradeWidth">
                <el-input-number
                  v-model="ruleForm.subgradeWidth"
                  style="width: 100%; pointer-events: none"
                  controls-position="right"
                  placeholder="请输入路基宽度"
                  readonly
                />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="净高(米)" prop="culvertHeight">
                <el-input-number
                  v-model="ruleForm.culvertHeight"
                  style="width: 100%; pointer-events: none"
                  controls-position="right"
                  placeholder="请输入净高"
                  readonly
                />
              </el-form-item>
              <el-form-item label="基础形式" prop="basicsForm">
                <el-input
                  v-model="ruleForm.basicsForm"
                  placeholder="请输入基础形式"
                  readonly
                />
              </el-form-item>
              <el-form-item label="填土高度(米)" prop="fillHeight">
                <el-input-number
                  v-model="ruleForm.fillHeight"
                  style="width: 100%; pointer-events: none"
                  controls-position="right"
                  placeholder="请输入填土高度"
                  readonly
                />
              </el-form-item>
              <el-form-item label="路面类型" prop="pavementType">
                <el-input
                  v-model="ruleForm.pavementType"
                  placeholder="请输入路面类型"
                  readonly
                />
              </el-form-item>
            </el-col>
          </el-row>
        </template>

        <template v-if="activeName == '2'">
          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item label="设计图纸" prop="designDrawingComplete">
                <el-select
                  v-model="ruleForm.designDrawingComplete"
                  clearable
                  filterable
                  placeholder="请选择设计图纸"
                  style="pointer-events: none"
                >
                  <el-option
                    v-for="dict in dict.type.base_archives_type"
                    :key="dict.value"
                    :label="dict.label"
                    :value="dict.value"
                  >
                  </el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="竣工文件" prop="completionDocumentComplete">
                <el-select
                  v-model="ruleForm.completionDocumentComplete"
                  clearable
                  filterable
                  placeholder="请选择竣工文件"
                  style="pointer-events: none"
                >
                  <el-option
                    v-for="dict in dict.type.base_archives_type"
                    :key="dict.value"
                    :label="dict.label"
                    :value="dict.value"
                  >
                  </el-option>
                </el-select>
              </el-form-item>
              <el-form-item
                label="特别检查报告"
                prop="particularlyReportComplete"
              >
                <el-select
                  v-model="ruleForm.particularlyReportComplete"
                  clearable
                  filterable
                  placeholder="请选择特别检查报告"
                  style="pointer-events: none"
                >
                  <el-option
                    v-for="dict in dict.type.base_archives_type"
                    :key="dict.value"
                    :label="dict.label"
                    :value="dict.value"
                  >
                  </el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="档案号" prop="fileNumberComplete">
                <el-select
                  v-model="ruleForm.fileNumberComplete"
                  clearable
                  filterable
                  placeholder="请选择档案号"
                  style="pointer-events: none"
                >
                  <el-option
                    v-for="dict in dict.type.base_archives_type"
                    :key="dict.value"
                    :label="dict.label"
                    :value="dict.value"
                  >
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="设计文件" prop="designDocumentComplete">
                <el-select
                  v-model="ruleForm.designDocumentComplete"
                  clearable
                  filterable
                  placeholder="请选择设计文件"
                  style="pointer-events: none"
                >
                  <el-option
                    v-for="dict in dict.type.base_archives_type"
                    :key="dict.value"
                    :label="dict.label"
                    :value="dict.value"
                  >
                  </el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="验收文件" prop="acceptanceDocumentComplete">
                <el-select
                  v-model="ruleForm.acceptanceDocumentComplete"
                  clearable
                  filterable
                  placeholder="请选择验收文件"
                  style="pointer-events: none"
                >
                  <el-option
                    v-for="dict in dict.type.base_archives_type"
                    :key="dict.value"
                    :label="dict.label"
                    :value="dict.value"
                  >
                  </el-option>
                </el-select>
              </el-form-item>
              <el-form-item
                label="专项检查报告"
                prop="specialInspectionReportComplete"
              >
                <el-select
                  v-model="ruleForm.specialInspectionReportComplete"
                  clearable
                  filterable
                  placeholder="请选择专项检查报告"
                  style="pointer-events: none"
                >
                  <el-option
                    v-for="dict in dict.type.base_archives_type"
                    :key="dict.value"
                    :label="dict.label"
                    :value="dict.value"
                  >
                  </el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="存档案" prop="saveArchivesComplete">
                <el-select
                  v-model="ruleForm.saveArchivesComplete"
                  clearable
                  filterable
                  placeholder="请选择存档案"
                  style="pointer-events: none"
                >
                  <el-option
                    v-for="dict in dict.type.base_archives_type"
                    :key="dict.value"
                    :label="dict.label"
                    :value="dict.value"
                  >
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item
                label="施工文件"
                prop="constructionDocumentsComplete"
              >
                <el-select
                  v-model="ruleForm.constructionDocumentsComplete"
                  clearable
                  filterable
                  placeholder="请选择施工文件"
                  style="pointer-events: none"
                >
                  <el-option
                    v-for="dict in dict.type.base_archives_type"
                    :key="dict.value"
                    :label="dict.label"
                    :value="dict.value"
                  >
                  </el-option>
                </el-select>
              </el-form-item>
              <el-form-item
                label="定期检查报告"
                prop="periodicInspectionReportComplete"
              >
                <el-select
                  v-model="ruleForm.periodicInspectionReportComplete"
                  clearable
                  filterable
                  placeholder="请选择定期检查报告"
                  style="pointer-events: none"
                >
                  <el-option
                    v-for="dict in dict.type.base_archives_type"
                    :key="dict.value"
                    :label="dict.label"
                    :value="dict.value"
                  >
                  </el-option>
                </el-select>
              </el-form-item>
              <el-form-item
                label="历次维修资料"
                prop="previousMaintenanceComplete"
              >
                <el-select
                  v-model="ruleForm.previousMaintenanceComplete"
                  clearable
                  filterable
                  placeholder="请选择历次维修资料"
                  style="pointer-events: none"
                >
                  <el-option
                    v-for="dict in dict.type.base_archives_type"
                    :key="dict.value"
                    :label="dict.label"
                    :value="dict.value"
                  >
                  </el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="建档时间" prop="filingTimeComplete">
                <el-select
                  v-model="ruleForm.filingTimeComplete"
                  clearable
                  filterable
                  placeholder="请选择建档时间"
                  style="pointer-events: none"
                >
                  <el-option
                    v-for="dict in dict.type.base_archives_type"
                    :key="dict.value"
                    :label="dict.label"
                    :value="dict.value"
                  >
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
        </template>
      </el-form>
    </div>

    <div class="text-center">
      <!-- <el-button @click="onClose">关 闭</el-button> -->
    </div>
  </div>
</template>

<script>
import {
  culvertAdd,
  culvertEdit,
  tempAdd,
  getInfoById,
} from "@/api/baseData/culvert/culvertInfo/index.js";
import Base from "./Base";
import Structure from "./Structure";
import Archives from "./Archives";
import { deptTreeSelect } from "@/api/tmpl";
import "@riophae/vue-treeselect/dist/vue-treeselect.css";
import Treeselect from "@riophae/vue-treeselect";
import { listMaintenanceSectionAll } from "@/api/system/maintenanceSection";
import { listByMaintenanceSectionId } from "@/api/baseData/common/routeLine";
import { listAllRoute } from "@/api/system/route.js";
import PileInput from "@/components/PileInput/index.vue";

export default {
  name: "CulvertInfoEdit",
  dicts: [
    "bridge_route_level",
    "sys_route_type",
    "sys_culvert_type",
    "bridge_design_load",
    "base_archives_type",
  ],
  components: { Base, Structure, Archives, Treeselect, PileInput },

  props: {
   
    componentId: { type: String, default: '' },
    
  },
  data() {
    return {
      loading: false,
      activeName: "0",
      ruleForm: {},
      rules: {},
      enterOwnerId: "", // 进洞口图片id
      leaveOwnerId: "", // 出洞口图片id
      formH: 600, // form 表单最大高度
      deptOptions: [], // 部门树数据
      types: 101, // 编码规划
      routeOptions: [], // 路段数据
      routeList: [], // 路线编码数据
    };
  },
  computed: {},
  watch: {
   
  },
  created() {
    this.init();
  },
  mounted() {
    this.formH = window.innerHeight - 360;
    window.addEventListener("resize", () => {
      this.formH = window.innerHeight - 360;
    });
  },
  methods: {
    init() {
     
      this.getInfo();
      this.getDeptTree();
      this.getOptions();
    },
    getInfo(val) {
     
        this.$modal.loading();
        getInfoById(this.componentId).then((res) => {
          if (res.code == 200) {
            this.ruleForm = res.data || {};
          }
        }).finally(()=>{
          this.$modal.closeLoading();
        });
     
    },
    onClose() {
      this.$emit("close");
    },
    /** 查询部门下拉树结构 */
    getDeptTree() {
      return deptTreeSelect({ types: this.types }).then((response) => {
        this.deptOptions = response.data;
      });
    },
    getOptions() {
      listMaintenanceSectionAll({ departmentId: null }).then((res) => {
        if (res.code == 200) {
          this.routeOptions = res.data;
        }
      });
      listAllRoute().then((res) => {
        if (res.code == 200) {
          this.routeList = res.data || [];
        }
      });
    },
     // 路线编码
    onRouteCodeChange(e) {
      let findObj = this.routeList.find((item) => item.routeCode == e);
      if(findObj) {
        this.form.routeId = findObj.routeId;
        this.$forceUpdate();
      }
    },
  },
};
</script>

<style lang="scss" scoped>
@import "@/assets/styles/common.scss";
.culvert-info-edit {
  /* max-height: 75vh;
    padding: 10px;
    overflow: auto; */
    height: 28vh !important;
    border: 1px solid rgb(232, 241, 255);

  ::v-deep .el-input__suffix {
    display: none;
  }

  ::v-deep .el-tabs{
    height: 67px;
  }

  ::v-deep .el-tabs .el-tabs__content{
    height:0;
  }
}
</style>
