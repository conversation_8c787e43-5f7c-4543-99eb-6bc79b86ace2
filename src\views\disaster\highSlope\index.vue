<template>
  <div class="app-container">

    <!-- 查询 -->
    <div class="searchBox" :style="{ 'height': queryShow ? '86px' : '48px' }">
      <el-row :gutter="12">
        <el-col :span="24" style="display: flex; align-items: center">
          <CascadeSelection
            style="min-width: 192px;"
            :form-data="queryParams"
            v-model="queryParams"
            @update:fromData="queryParamsUpdate"
            types="201"
            multiple
          />
          <RangeInput
            :clearData="clearData"
            @startValue="(v) => {queryParams.pileStartNum = v}"
            @endValue="(v) => {queryParams.pileEndNum = v}"
          />
          <el-button type="primary" icon="el-icon-search" size="mini" @click="queryhandle">搜索</el-button>
          <el-button icon="el-icon-refresh" size="mini" @click="queryReset">重置</el-button>
          <el-button
            :icon="queryShow ? 'el-icon-arrow-up' : 'el-icon-arrow-down'"
            style="color: #1890ff;border-color: #badeff;background-color: #e8f4ff;"
            circle
            @click="queryShow = !queryShow"
          />
        </el-col>
      </el-row>
      <transition name="search">
        <div class="searchMoreBox" v-if="queryShow">
          <el-row :gutter="20" style="width: 426px;">
            <el-col :span="12" :offset="0">
              <el-input v-model="queryParams.highSlopeNum" placeholder="请输入高边坡编号" clearable
                        @keyup.enter.native="queryhandle"/>
            </el-col>
            <el-col :span="12" :offset="0">
              <el-input v-model="queryParams.highSlopeName" placeholder="请输入高边坡名称" clearable
                        @keyup.enter.native="queryhandle"/>
            </el-col>
          </el-row>
          <el-row :gutter="20" style="width: 416.45px; min-width: 210px; margin-left: 10px">
            <el-col :span="24" :offset="0">
              <el-date-picker
                v-model="queryTime"
                @change="() => {queryParams.startTime = queryTime[0] + ' 00:00:00'; queryParams.endTime = queryTime[1] + ' 23:59:59'}"
                type="daterange"
                style="width: 100%;"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                format="yyyy-MM-dd"
                value-format="yyyy-MM-dd"
              >
              </el-date-picker>
            </el-col>
          </el-row>
          <el-row :gutter="20" style="width: 426px; margin-left: 10px">
            <el-col :span="12" :offset="0">
              <el-select v-model="queryParams.status" placeholder="请选择审核状态" clearable>
                <el-option label="待提交" value="0"></el-option>
                <el-option label="审核中" value="1"></el-option>
                <el-option label="已审核" value="2"></el-option>
                <el-option label="已撤销" value="3"></el-option>
                <el-option label="已驳回" value="4"></el-option>
              </el-select>
            </el-col>
          </el-row>
        </div>
      </transition>
    </div>

    <!-- 主表数据 -->
    <div class="tableDiv" :style="{ 'height': queryShow ? 'calc(100% - 96px)' : 'calc(100% - 58px)' }">
      <!-- 功能按钮 -->
      <div class="btnBox">
        <el-row :gutter="10">
          <el-col :span="1.5" v-hasPermi="['disaster:highway:add']">
            <el-button type="primary" icon="el-icon-plus" size="mini" @click="formInit('add')">新增
            </el-button>
          </el-col>
          <el-col :span="1.5" v-hasPermi="['disaster:highway:edit']">
            <el-button type="success" icon="el-icon-edit" size="mini" :disabled="forEditDelDisabled"
                       @click="formEdit('select')">修改
            </el-button>
          </el-col>
          <el-col :span="1.5" v-hasPermi="['disaster:highway:remove']">
            <el-button type="danger" icon="el-icon-delete" size="mini" :disabled="forDeleteDelDisabled"
                       @click="formDelete('batch')">删除
            </el-button>
          </el-col>
          <el-col :span="1.5" v-hasPermi="['disaster:highway:import']">
            <el-button type="primary" icon="el-icon-document-add" size="mini" @click="handleImportAdd">导入(新增)
            </el-button>
          </el-col>
          <el-col :span="1.5" v-hasPermi="['disaster:highway:import']">
            <el-button type="primary" icon="el-icon-document-add" size="mini" @click="handleImportUpdate">导入(更新)
            </el-button>
          </el-col>
          <el-col :span="1.5" v-hasPermi="['disaster:highway:export']">
            <el-button type="warning" icon="el-icon-download" size="mini" @click="formExport">导出
            </el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="warning" icon="el-icon-download" v-if="$store.getters.name === 'admin'" size="mini"
                       @click="expCard">导出卡片
            </el-button>
          </el-col>
          <el-col :span="1.5" v-if="$store.getters.name === 'admin'">
            <el-button type="primary" icon="el-icon-refresh" size="mini"
                       @click="excDataSync">
              数据同步
            </el-button>
          </el-col>
          <el-col :span="1.5" v-if="$store.getters.name === 'admin'">
            <el-button type="primary" icon="el-icon-refresh-left" size="mini" @click="formRevoke('batch')">
              撤销
            </el-button>
          </el-col>
          <el-col :span="1.5" v-if="$store.getters.name === 'admin'">
            <el-button type="primary" icon="el-icon-s-operation" size="mini" @click="batchStart()">
              批量启动
            </el-button>
          </el-col>
        </el-row>
      </div>

      <!-- 数据表格 -->
      <el-table v-adjust-table size="mini" height="calc(100% - 98px)" border ref="tableRef"
                v-loading="tableLoading" :data="tableData" @selection-change="tableSelectionChange"
                @row-click="tableRowClick">
        <el-table-column type="selection" width="55" align="center"/>
        <el-table-column label="序号" fixed align="center" type="index" width="50"></el-table-column>
        <el-table-column label="高边坡编号" :show-overflow-tooltip="true" width="180" align="center"
                         prop="highSlopeNum"/>
        <el-table-column label="高边坡名称" :show-overflow-tooltip="true" width="250" align="center"
                         prop="highSlopeName"/>
        <el-table-column label="管养单位" :show-overflow-tooltip="true" width="120" align="center"
                         prop="maintenanceUnitName"/>
        <el-table-column label="路段名称" :show-overflow-tooltip="true" width="150" align="center"
                         prop="roadSectionName"/>
        <el-table-column label="路线名称" :show-overflow-tooltip="true" width="180" align="center" prop="routerName"/>
        <el-table-column label="技术等级" :show-overflow-tooltip="true" width="120" align="center"
                         prop="technicalGrade">
          <template slot-scope="scope">
            <dict-tag :options="dict.type.sys_route_grade" :value="scope.row.technicalGrade"/>
          </template>
        </el-table-column>
        <el-table-column label="采集人" :show-overflow-tooltip="true" width="120" align="center" prop="createName"/>
        <el-table-column label="采集单位" :show-overflow-tooltip="true" width="220" align="center" prop="createUnit"/>
        <el-table-column label="采集时间" :show-overflow-tooltip="true" width="180" align="center" prop="createTime"/>
        <el-table-column label="修改时间" :show-overflow-tooltip="true" width="180" align="center" prop="updateTime"/>
        <el-table-column label="状态" :show-overflow-tooltip="true" width="120" align="center" prop="status">
          <template slot-scope="scope">
            <el-tag v-if="scope.row.status === '0'" type="info">待提交</el-tag>
            <el-tag v-if="scope.row.status === '1'" type="warning">审核中</el-tag>
            <el-tag v-if="scope.row.status === '2'" type="success">已审核</el-tag>
            <el-tag v-if="scope.row.status === '3'">已撤销</el-tag>
            <el-tag v-if="scope.row.status === '4'" type="danger">已驳回</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" width="250" class-name="small-padding fixed-width" fixed="right">
          <template slot-scope="scope">
            <el-button v-if="scope.row.status !== '1' && scope.row.status !== '2' && scope.row.status !== '4'" size="mini" type="text" icon="el-icon-edit"
                       @click="formEdit('click', scope.row)"
                       v-hasPermi="['disaster:highway:edit']">修改
            </el-button>
            <el-button v-if="scope.row.status === '0' || scope.row.status === '3'" size="mini" type="text"
                       icon="el-icon-delete"
                       @click="formDelete('single', scope.row)"
                       v-hasPermi="['disaster:highway:edit']">删除
            </el-button>
            <el-button size="mini" type="text" icon="el-icon-view" @click="formView(scope.row)">查看</el-button>
            <el-button v-if="scope.row.status === '1' || scope.row.status === '4'" key="submit" size="mini" type="text"
                       icon="el-icon-share"
                       :disabled="isButtonDisabled(scope.row.id)"
                       @click="formAudit(scope.row)">复核
            </el-button>
            <el-button v-if="scope.row.status === '1' || scope.row.status === '2' || scope.row.status === '4'" size="mini" type="text"
                       icon="el-icon-view"
                       @click="formAuditInfo(scope.row)">审核信息
            </el-button>
            <el-button v-if="scope.row.status === '1' && $store.getters.name === 'admin'"
                       v-hasPermi="['disaster:disaster:edit']" size="mini" type="text"
                       icon="el-icon-refresh-left" @click="formRevoke('single', scope.row)">撤销
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <pagination :total="queryTotal" :page.sync="queryParams.pageNum"
                  :limit.sync="queryParams.pageSize" @pagination="queryList" style="margin-right: 10px;"/>

    </div>

    <!-- 表单 -->
    <el-dialog :visible.sync="formDialog" width="70%" max-height="50%" :close-on-press-escape="false"
               :close-on-click-modal="false" append-to-body class="formDialog">
      <template slot="title">
        <div class="titleBox">
          <div class="title">{{ formTitle }}</div>
          <div class="subTitle" v-if="formParams.id">ID：{{ formParams.id }}</div>
        </div>
      </template>
      <el-form ref="formRef" :model="formParams" :rules="formRules" :disabled="this.formType === 'view'"
               label-width="140px" label-position="right">
        <div class="infoBox" style="margin-bottom: 20px;" v-loading="formLoading">
          <div class="infoTitle">
            基础信息
          </div>
          <el-row :gutter="12">
            <el-col :span="12">
              <el-form-item label="管养单位名称" prop="maintenanceUnitId">
                <el-select ref="formMainRef" v-model="formParams.maintenanceUnitId" placeholder="请选择管养单位"
                           style="width: 100%;" @change="formMaintenanceChange('1')">
                  <el-option v-for="item in formMaintenanceRenderList" :label="item.label" :value="item.id"
                             :key="item.id" style="display: none;"></el-option>
                  <el-tree
                    :data="formMaintenanceList"
                    :props="{ children: 'children', label: 'label', value: 'id' }"
                    :expand-on-click-node="false"
                    highlight-current
                    default-expand-all
                    @node-click="formMainItemClick"
                  >
                  </el-tree>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item prop="roadSectionId">
                <span slot="label">
                  <el-tooltip content="选择管养单位后带出" placement="top">
                    <i class="el-icon-question"></i>
                  </el-tooltip>
                  路段名称
                </span>
                <el-select v-model="formParams.roadSectionId" placeholder="请选择路段" style="width: 100%;"
                           @change="formMaintenanceChange('2')">
                  <el-option v-for="item in formRoadSectionList" :label="item.maintenanceSectionName"
                             :value="item.maintenanceSectionId" :key="item.maintenanceSectionId"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="12">
            <el-col :span="12">
              <el-form-item prop="routerNum">
                <span slot="label">
                  <el-tooltip content="选择路段后带出" placement="top">
                    <i class="el-icon-question"></i>
                  </el-tooltip>
                  路线名称
                </span>
                <el-select v-model="formParams.routerNum" placeholder="请选择路线" style="width: 100%;"
                           @change="formMaintenanceChange('3')">
                  <el-option v-for="item in formRouteList" :label="`${item.routeName}（${item.routeCode}）`"
                             :value="item.routeCode" :key="item.routeCode"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item prop="technicalGrade">
                <span slot="label">
                  <el-tooltip content="选择路段后带出" placement="top">
                    <i class="el-icon-question"></i>
                  </el-tooltip>
                  技术等级
                </span>
                <!-- <el-input readonly v-model="formParams.technicalGrade" placeholder="请输入技术等级" /> -->
                <el-select disabled v-model="formParams.technicalGrade" placeholder="请选择技术等级"
                           style="width: 100%;">
                  <el-option v-for="dict in dict.type.sys_route_grade" :label="dict.label" :value="dict.value"
                             :key="dict.value"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="12">
            <el-col :span="8">
              <el-form-item prop="">
                <span slot="label">
                  <el-tooltip content="省级区划代码" placement="top">
                    <i class="el-icon-question"></i>
                  </el-tooltip>
                  省级区划
                </span>
                <el-select v-model="divisionsProvincial" disabled style="width: 100%;">
                  <el-option v-for="item in divisionsProvincialList" :label="`${item.label}（${item.id}）`"
                             :value="item.id" :key="item.id"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item prop="">
                <span slot="label">
                  <el-tooltip content="市级区划代码" placement="top">
                    <i class="el-icon-question"></i>
                  </el-tooltip>
                  市级区划
                </span>
                <el-select v-model="divisionsMunicipal" clearable style="width: 100%;" @change="formChangeAreaCode">
                  <el-option v-for="item in divisionsMunicipalList" :label="`${item.label}（${item.id}）`"
                             :value="item.id" :key="item.id"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item prop="areaCode">
                <span slot="label">
                  <el-tooltip content="县级区划代码，用于生成灾害编码" placement="top">
                    <i class="el-icon-question"></i>
                  </el-tooltip>
                  县级区划
                </span>
                <el-select v-model="formParams.areaCode" clearable style="width: 100%;">
                  <el-option v-for="item in areaCodeList" :label="`${item.label}（${item.id}）`" :value="item.id"
                             :key="item.id"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="12">
            <el-col :span="12">
              <el-form-item prop="highSlopeName">
                <span slot="label">
                  <el-tooltip content="按“里程桩号+段高边坡”保存后自动生成（例如“K0+000 至K0+100 段高边坡”）"
                              placement="top">
                    <i class="el-icon-question"></i>
                  </el-tooltip>
                  高边坡名称
                </span>
                <el-input v-model="formParams.highSlopeName" disabled placeholder="请输入高边坡名称"/>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item prop="highSlopeNum">
                <span slot="label">
                  <el-tooltip
                    content="按“路线编号+县级行政区划代码+B+四位编号+L（R）”进行编号（边坡四位编码后加 L 或 R 代表左侧或右侧，例如G75520303B0002L）,保存后自动生成"
                    placement="top">
                    <i class="el-icon-question"></i>
                  </el-tooltip>
                  高边坡编号
                </span>
                <el-input v-model="formParams.highSlopeNum" disabled placeholder="请输入高边坡编号"/>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="12">
            <el-col :span="12">
              <el-form-item prop="pileStartNum">
                <span slot="label">
                  <el-tooltip
                    content="起止点桩号，根据现场调查填写起点桩号和止点桩号（例如起点桩号 K0+100，止点桩号 K0+200）"
                    placement="top">
                    <i class="el-icon-question"></i>
                  </el-tooltip>
                  边坡起点桩号
                </span>
                <!-- <PileInput v-model="formParams.pileStartNum" placeholder="请输入起点桩号"></PileInput> -->
                <div style="width: 100%; display: flex; flex-direction: row; flex-wrap: nowrap;">
                  <el-tag
                    type="info"
                    effect="plain"
                    size="medium"
                  >
                    K
                  </el-tag>
                  <el-input v-model="pileStartK" controls-position="right" style="width: 50%;"
                            placeholder="请输入起点桩号"/>
                  <el-tag
                    type="info"
                    effect="plain"
                    size="medium"
                  >
                    +
                  </el-tag>
                  <el-input v-model="pileStartAdd" controls-position="right" style="width: 50%;"
                            placeholder="请输入起点桩号"/>
                </div>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item prop="pileEndNum">
                <span slot="label">
                  <el-tooltip
                    content="起止点桩号，根据现场调查填写起点桩号和止点桩号（例如起点桩号 K0+100，止点桩号 K0+200）"
                    placement="top">
                    <i class="el-icon-question"></i>
                  </el-tooltip>
                  边坡终点桩号
                </span>
                <!-- <PileInput v-model="formParams.pileEndNum" placeholder="请输入终点桩号"></PileInput> -->
                <div style="width: 100%; display: flex; flex-direction: row; flex-wrap: nowrap;">
                  <el-tag
                    type="info"
                    effect="plain"
                    size="medium"
                  >
                    K
                  </el-tag>
                  <el-input v-model="pileEndK" controls-position="right" style="width: 50%;"
                            placeholder="请输入终点桩号"/>
                  <el-tag
                    type="info"
                    effect="plain"
                    size="medium"
                  >
                    +
                  </el-tag>
                  <el-input v-model="pileEndAdd" controls-position="right" style="width: 50%;"
                            placeholder="请输入终点桩号"/>
                </div>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="12">
            <el-col :span="12">
              <el-form-item prop="startLatitudeAndLongitude">
                <span slot="label">
                  <el-tooltip content="格式为两个浮点数，中间用英文逗号（,）分隔，经度在前，纬度在后。" placement="top">
                    <i class="el-icon-question"></i>
                  </el-tooltip>
                  边坡起点经纬度
                </span>
                <el-input v-model="formParams.startLatitudeAndLongitude" placeholder="请输入起点经纬度，以英文逗号分隔">
                  <el-button slot="append" icon="el-icon-location" @click="openCoordinateDialog('start')">坐标拾取
                  </el-button>
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item prop="endLatitudeAndLongitude">
                <span slot="label">
                  <el-tooltip content="格式为两个浮点数，中间用英文逗号（,）分隔，经度在前，纬度在后。" placement="top">
                    <i class="el-icon-question"></i>
                  </el-tooltip>
                  边坡止点经纬度
                </span>
                <el-input v-model="formParams.endLatitudeAndLongitude" placeholder="请输入止点经纬度，以英文逗号分隔">
                  <el-button slot="append" icon="el-icon-location" @click="openCoordinateDialog('end')">坐标拾取
                  </el-button>
                </el-input>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="12">
            <el-col :span="12">
              <el-form-item label="建造年度" prop="constructionYear">
                <el-date-picker
                  style="width: 100%;"
                  v-model="formParams.constructionYear"
                  type="year"
                  value-format="yyyy"
                  placeholder="建造年度"
                >
                </el-date-picker>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="改建年度" prop="renovationYear">
                <el-date-picker
                  style="width: 100%;"
                  v-model="formParams.renovationYear"
                  type="year"
                  value-format="yyyy"
                  placeholder="改建年度"
                >
                </el-date-picker>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="12">
            <el-col :span="12">
              <el-form-item prop="slopeLength">
                <span slot="label">
                  <el-tooltip content="根据起止点桩号，保存后自动计算" placement="top">
                    <i class="el-icon-question"></i>
                  </el-tooltip>
                  边坡长度(米)
                </span>
                <el-input v-model="formParams.slopeLength" disabled style="width: 100%;"
                          placeholder="请输入边坡长度(米)"/>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="车道数量(个)" prop="laneNum">
                <el-input-number v-model="formParams.laneNum" controls-position="right" :min="0" :max="8" :precision="0"
                                 style="width: 100%;" placeholder="请输入车道数量(个)"/>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="12">
            <el-col :span="12">
              <el-form-item label="路基宽度(米)" prop="roadbedWidth">
                <el-input-number v-model="formParams.roadbedWidth" controls-position="right" :min="0" :precision="2"
                                 style="width: 100%;" placeholder="请输入路基宽度(米)"/>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="路面宽度(米)" prop="roadWidth">
                <el-input-number v-model="formParams.roadWidth" controls-position="right" :min="0" :precision="2"
                                 style="width: 100%;" placeholder="请输入路面宽度(米)"/>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="12">
            <!-- <el-col :span="12">
              <el-form-item label="区划代码" prop="areaCode">
                <el-input v-model="formParams.areaCode" type="number" placeholder="请输入区划代码" />
              </el-form-item>
            </el-col> -->
            <el-col :span="12">
              <el-form-item label="相对位置" prop="relativePosition">
                <el-radio-group v-model="formParams.relativePosition" ref="formRadio1">
                  <el-radio v-for="dict in dict.type.side_slope_relative_position" :key="dict.value"
                            :label="dict.value">{{
                      dict.label
                    }}
                  </el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="12">
            <el-col :span="24">
              <el-form-item label="面层类型" prop="surfaceLayerType">
                <el-radio-group v-model="formParams.surfaceLayerType" ref="formRadio2">
                  <el-radio v-for="dict in dict.type.roadbed_surface_type" :key="dict.value" :label="dict.value">{{
                      dict.label
                    }}
                  </el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>
          </el-row>

        </div>

        <div class="infoBox" style="margin-bottom: 20px;" v-loading="formLoading">
          <div class="infoTitle">
            边坡类型
          </div>
          <el-row :gutter="12">
            <el-col :span="24">
              <el-form-item label="边坡类型">
                <el-radio-group v-model="slopeType" @change="slopeTypeChange" ref="formRadio8">
                  <el-radio label="0">路堤边坡</el-radio>
                  <el-radio label="1">路堑边坡</el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>
          </el-row>
        </div>

        <div class="infoBox" style="margin-bottom: 20px;" v-loading="formLoading" v-show="slopeType === '0'">
          <div class="infoTitle">
            边坡类型（路堤边坡）
          </div>
          <el-row :gutter="12">
            <el-col :span="8">
              <el-form-item label="坡高(米)" prop="embankmentSlopeHeight">
                <el-input-number v-model="formParams.embankmentSlopeHeight" controls-position="right" :min="0"
                                 :precision="2" style="width: 100%;" placeholder="请输入坡高(米)"/>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="坡度(度)" prop="embankmentSlope">
                <el-input-number v-model="formParams.embankmentSlope" controls-position="right" :min="0" :precision="2"
                                 style="width: 100%;" placeholder="请输入坡度(度)"/>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="分级(级)" prop="embankmentGrade">
                <el-input-number v-model="formParams.embankmentGrade" controls-position="right" :min="0" :precision="2"
                                 style="width: 100%;" placeholder="请输入分级(级)"/>
              </el-form-item>
            </el-col>
          </el-row>

          <el-divider></el-divider>

          <el-row :gutter="12">
            <el-col :span="24">
              <el-form-item label="是否临河" prop="embankmentByARiver">
                <el-radio-group v-model="formParams.embankmentByARiver" ref="formRadio3" @change="formRiverChange">
                  <el-radio :label="'0'">是</el-radio>
                  <el-radio :label="'1'">否</el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>
          </el-row>

          <el-divider></el-divider>

          <el-row :gutter="12" v-show="formParams.embankmentByARiver === '0'">
            <el-col :span="24">
              <el-form-item label="临河地形" prop="embankmentRiversideTerrain">
                <el-radio-group v-model="formParams.embankmentRiversideTerrain" ref="formRadio4">
                  <el-radio v-for="dict in dict.type.side_slope_river_terrain" :key="dict.value" :label="dict.value">{{
                      dict.label
                    }}
                  </el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>
          </el-row>

        </div>

        <div class="infoBox" style="margin-bottom: 20px;" v-loading="formLoading" v-show="slopeType === '1'">
          <div class="infoTitle">
            边坡类型（路堑边坡）
          </div>
          <el-row :gutter="12">
            <el-col :span="8">
              <el-form-item label="坡高(米)" prop="cuttingSlopeHeight">
                <el-input-number v-model="formParams.cuttingSlopeHeight" controls-position="right" :min="0"
                                 :precision="2" style="width: 100%;" placeholder="请输入坡高(米)"/>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="坡度(度)" prop="cuttingSlope">
                <el-input-number v-model="formParams.cuttingSlope" controls-position="right" :min="0" :precision="2"
                                 style="width: 100%;" placeholder="请输入坡度(度)"/>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="分级(级)" prop="cuttingGrade">
                <el-input-number v-model="formParams.cuttingGrade" controls-position="right" :min="0" :precision="0"
                                 style="width: 100%;" placeholder="请输入分级(级)"/>
              </el-form-item>
            </el-col>
          </el-row>

          <el-divider></el-divider>

          <el-row :gutter="12">
            <el-col :span="24">
              <el-form-item label="岩性" prop="cuttingLithology">
                <el-radio-group v-model="formParams.cuttingLithology" ref="formRadio5">
                  <el-radio v-for="dict in dict.type.side_slope_lithology" :key="dict.value" :label="dict.value">{{
                      dict.label
                    }}
                  </el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>
          </el-row>

        </div>

        <div class="infoBox" style="margin-bottom: 20px;" v-loading="formLoading">
          <div class="infoTitle">
            排水设施类型
          </div>
          <el-row :gutter="12">
            <el-col :span="24">
              <el-form-item label="地表排水设施" prop="surfaceDrainageFacilities">
                <el-checkbox-group v-model="formParams.surfaceDrainageFacilities">
                  <el-checkbox v-for="dict in dict.type.surface_drainage_facilities" :key="dict.value"
                               :label="dict.value">
                    {{ dict.label }}
                  </el-checkbox>
                </el-checkbox-group>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="12">
            <el-col :span="24">
              <el-form-item label="" prop="surfaceDrainageFacilitiesRemark">
                <el-input v-model="formParams.surfaceDrainageFacilitiesRemark" placeholder="其它"/>
              </el-form-item>
            </el-col>
          </el-row>

          <el-divider></el-divider>

          <el-row :gutter="12">
            <el-col :span="24">
              <el-form-item label="地下排水设施" prop="undergroundDrainageFacilities">
                <el-checkbox-group v-model="formParams.undergroundDrainageFacilities">
                  <el-checkbox v-for="dict in dict.type.side_slope_underground_drainage_facilities" :key="dict.value"
                               :label="dict.value">
                    {{ dict.label }}
                  </el-checkbox>
                </el-checkbox-group>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="12">
            <el-col :span="24">
              <el-form-item label="" prop="undergroundDrainageFacilitiesRemark">
                <el-input v-model="formParams.undergroundDrainageFacilitiesRemark" placeholder="其它"/>
              </el-form-item>
            </el-col>
          </el-row>

        </div>

        <div class="infoBox" style="margin-bottom: 20px;" v-loading="formLoading">
          <div class="infoTitle">
            防护设施类型
          </div>

          <el-row :gutter="12">
            <el-col :span="24">
              <el-form-item label="坡面防护" prop="curDamagedSlopeSurface">
                <el-checkbox-group v-model="formParams.curDamagedSlopeSurface">
                  <el-checkbox v-for="dict in dict.type.side_slope_protection" :key="dict.value" :label="dict.value">{{
                      dict.label
                    }}
                  </el-checkbox>
                </el-checkbox-group>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="12">
            <el-col :span="24">
              <el-form-item label="" prop="curDamagedSlopeSurfaceRemark">
                <el-input v-model="formParams.curDamagedSlopeSurfaceRemark" placeholder="其它"/>
              </el-form-item>
            </el-col>
          </el-row>

          <el-divider></el-divider>

          <el-row :gutter="12">
            <el-col :span="24">
              <el-form-item label="沿河防护" prop="curDamagedRiverBank">
                <el-checkbox-group v-model="formParams.curDamagedRiverBank">
                  <el-checkbox v-for="dict in dict.type.protection_along_the_river" :key="dict.value"
                               :label="dict.value">{{
                      dict.label
                    }}
                  </el-checkbox>
                </el-checkbox-group>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="12">
            <el-col :span="24">
              <el-form-item label="" prop="curDamagedRiverBankRemark">
                <el-input v-model="formParams.curDamagedRiverBankRemark" placeholder="其它"/>
              </el-form-item>
            </el-col>
          </el-row>

          <el-divider></el-divider>

          <el-row :gutter="12">
            <el-col :span="24">
              <el-form-item label="支挡设施" prop="curDamagedSupportFacility">
                <el-checkbox-group v-model="formParams.curDamagedSupportFacility">
                  <el-checkbox v-for="dict in dict.type.side_slope_support_facilities" :key="dict.value"
                               :label="dict.value">{{
                      dict.label
                    }}
                  </el-checkbox>
                </el-checkbox-group>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="12">
            <el-col :span="24">
              <el-form-item label="" prop="curDamagedSupportFacilityRemark">
                <el-input v-model="formParams.curDamagedSupportFacilityRemark" placeholder="其它"/>
              </el-form-item>
            </el-col>
          </el-row>

        </div>

        <div class="infoBox" style="margin-bottom: 20px;" v-loading="formLoading">
          <div class="infoTitle">
            抗震设防等级
          </div>
          <el-row :gutter="12">
            <el-col :span="24">
              <el-form-item label-width="0" prop="seismicFortificationLevel">
                <el-radio-group v-model="formParams.seismicFortificationLevel" ref="formRadio6">
                  <el-radio v-for="dict in dict.type.seismic_fortification_level" :key="dict.value" :label="dict.value">
                    {{
                      dict.label
                    }}
                  </el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>
          </el-row>
        </div>

        <div class="infoBox" style="margin-bottom: 20px;" v-loading="formLoading">
          <div class="infoTitle">
            防洪标准
          </div>
          <el-row :gutter="12">
            <el-col :span="24">
              <el-form-item label-width="0" prop="floodControlStandard">
                <el-radio-group v-model="formParams.floodControlStandard"
                                @input="formRadioChange('floodControlStandard', 'floodControlStandardRemark')"
                                ref="subFormRadio8">
                  <el-radio v-for="dict in dict.type.side_slop_flood_control" :key="dict.value" :label="dict.value"
                            style="margin-right: 30px; margin-top: 5px; margin-bottom: 5px;">{{ dict.label }}
                  </el-radio>
                  <el-radio label="99" style="margin-top: 5px; margin-bottom: 5px;">其它</el-radio>
                </el-radio-group>
                <!-- <el-radio-group v-model="formParams.floodControlStandard" ref="formRadio7">
                  <el-radio v-for="dict in dict.type.side_slop_flood_control" :label="dict.value">{{ dict.label }}</el-radio>
                </el-radio-group> -->
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="12" v-show="formParams.floodControlStandard === '99'">
            <el-col :span="24">
              <el-form-item label-width="0" prop="floodControlStandardRemark">
                <el-input v-model="formParams.floodControlStandardRemark" placeholder="其它"/>
              </el-form-item>
            </el-col>
          </el-row>
        </div>

        <div class="infoBox" style="margin-bottom: 20px;" v-loading="formLoading">
          <div class="infoTitle">
            其他需要说明的信息
          </div>
          <el-row :gutter="12">
            <el-col :span="24">
              <el-form-item label-width="0" prop="supplementaryInformation">
                <el-input
                  :autosize="{ minRows: 4 }"
                  type="textarea"
                  placeholder="主要填写表格中未明确提出的其他需要上报的信息。"
                  v-model="formParams.supplementaryInformation"
                  maxlength="500"
                  show-word-limit
                ></el-input>
              </el-form-item>
            </el-col>
          </el-row>
        </div>

        <div class="infoBox" style="margin-bottom: 20px;" v-loading="formLoading">
          <div class="infoTitle">
            照片（边坡典型照片）
          </div>
          <el-row :gutter="12">
            <el-col :span="24">
              <el-form-item label-width="0" prop="riskDescription">
                <div class="imgBox" v-if="formImgSrcList.length > 0">
                  <el-timeline>
                    <el-timeline-item v-for="item in formImgDateLine" :key="item.time" :timestamp="item.time"
                                      placement="top"
                                      color="#5cbb7a">
                      <el-card class="imgBoxCard">
                        <div class="cardMain" v-for="itemC in item.data">
                          <el-button class="imgDeleteBtn" type="danger" icon="el-icon-delete" circle
                                     @click="formDeleteImg(itemC.id)"></el-button>
                          <div class="imgTitle">
                            <el-tooltip class="item" effect="dark" :content="itemC.name" placement="top">
                              <i class="el-icon-info"></i>
                            </el-tooltip>
                            {{ itemC.name }}
                          </div>
                          <el-image fit="cover" class="img" :src="itemC.url" @click="formImgPreview(itemC.imgUrl)" :preview-src-list="formImgUrlList"></el-image>
                          <div class="footer">
                            {{`由 ${itemC.createBy} 上传于 ${itemC.createTime}` }}
                          </div>
                          <div class="footer">
                            {{`图片描述：${itemC.remark ? itemC.remark : ''}` }}
                          </div>
                        </div>
                      </el-card>
                    </el-timeline-item>
                  </el-timeline>
                </div>
                <div class="noneBox" v-else>
                  暂无内容
                </div>
              </el-form-item>
            </el-col>
          </el-row>
        </div>

        <div class="infoBox" v-loading="formLoading" style="padding: 15px;" v-if="this.formType !== 'view'">
          <div class="infoTitle">
            <el-tooltip content="上传填报高边坡的全貌照片及局部照片（防护设施、排水设施），不少于 3 张" placement="top">
              <i class="el-icon-question"></i>
            </el-tooltip>
            上传照片
          </div>
          <ImageUpload
            v-if="formDialog"
            v-model="formUploadList"
            :ownerId="formImgOwnerId"
            :needRemark="true"
            :can-sort="true"
            storage-path="/disaster/risk/highSlope/"
            platform="fykj"
          />
        </div>

      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-row class="footerTip" v-if="formParams.createName">
          <el-col :span="12" style="display: flex; justify-content: flex-start;">
            采集人：{{ formParams.createName }}
          </el-col>
          <el-col :span="12" style="display: flex; justify-content: flex-end;">
            采集时间：{{ formParams.createTime }}
          </el-col>
        </el-row>
        <el-row v-if="formType !== 'view'">
          <el-col :span="24" style="display: flex; justify-content: center;">
            <el-button type="primary" @click="formSubmit('formRef', '0')" :loading="formBtnLoading">保 存</el-button>
            <el-button type="success" @click="formSubmit('formRef', '1')" :loading="formBtnLoading">提交审核</el-button>
          </el-col>
        </el-row>
      </div>
    </el-dialog>

    <!-- 用户导入对话框 -->
    <el-dialog :title="upload.title" :visible.sync="upload.open" width="400px" :close-on-press-escape="false"
               :close-on-click-modal="false" append-to-body>
      <el-upload
        ref="upload"
        :limit="1"
        accept=".xlsx, .xls"
        :headers="upload.headers"
        :action="upload.submitType == 'add' ? upload.urlAdd : upload.urlUpdate"
        :disabled="upload.isUploading"
        :on-progress="handleFileUploadProgress"
        :on-success="handleFileSuccess"
        :auto-upload="false"
        drag
      >
        <i class="el-icon-upload"></i>
        <div class="el-upload__text">
          将文件拖到此处，或
          <em>点击上传</em>
        </div>
        <div class="el-upload__tip" slot="tip" v-if="upload.submitType == 'add'">
          <el-link type="primary" style="font-size:12px" @click="importTemplate">下载模板</el-link>
        </div>
        <div class="el-upload__tip" style="color:red" slot="tip">提示：仅允许导入“xls”或“xlsx”格式文件！</div>
      </el-upload>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitFileForm">确 定</el-button>
        <!-- <el-button @click="upload.open = false">取 消</el-button> -->
      </div>
    </el-dialog>

    <el-dialog title="坐标拾取" class="coordinateDialog" :visible.sync="coordinateDialog" @close="coordinateClose"
               :close-on-press-escape="false" :close-on-click-modal="false" append-to-body>
      <div class="coordinateMap">
        <div id="coordinateBox" v-if="coordinateDialog"></div>
        <div class="coordinateSearch">
          <el-input v-model="coordinateSearch" clearable placeholder="请输入地名" @keyup.enter.native="coordinateList"/>
          <el-button type="primary" icon="el-icon-search" style="margin-left: 5px;" @click="coordinateList">查询
          </el-button>
        </div>
        <div class="coordinateTip">
          <el-row>
            <el-col :span="24" style="padding: 5px 0px;">
              当前经纬度：
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="24">
              <el-input v-model="coordinatePosition" readonly/>
            </el-col>
          </el-row>
        </div>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="coordinateSave">保 存</el-button>
      </div>
    </el-dialog>

    <!-- 审核信息 -->
    <el-dialog title="审核信息" :visible.sync="auditInfoDialog" :close-on-press-escape="false"
               :close-on-click-modal="false" append-to-body>
      <div class="hisMainBox">
        <el-timeline reverse>
          <el-timeline-item
            v-for="item in formIdeaList"
            :key="item.id"
            :timestamp="`${item.startTime}`"
            color="#5cbb7a"
            placement="top"
          >
            <el-card>
              <div slot="header" class="clearfix">
                <span>节点：{{ item.nodeName }}</span>
                <el-button icon="el-icon-edit" type="text" size="mini" style="float: right; padding: 3px 0"
                           @click="updateNodeTime(item)"></el-button>
              </div>
              <div class="hisIdeaBox">
                <div class="boxMain">
                  <h3 v-if="item.assignee">{{ item.content.comment }}</h3>
                  <div class="changeBox" v-if="item.content.operatingRecord && item.content.operatingRecord.length > 0">
                    <div class="title">表单修改记录</div>
                    <div class="list" v-for="val in item.content.operatingRecord">
                      <el-tag type="info" effect="plain">{{ val.name }}</el-tag>
                      <p style="margin: 0 10px;">由</p>
                      <p style="color: #E6A23C">{{ `"${val.oldVal}"` }}</p>
                      <p style="margin: 0 10px;">改为</p>
                      <p style="color: #F56C6C">{{ `"${val.newVal}"` }}</p>
                    </div>
                  </div>
                </div>
                <div class="boxBottom">
                  <p v-if="item.assignee">提交人：{{ item.assignee }}</p>
                  <p v-if="item.assignee">提交时间：{{ item.endTime }}</p>
                  <p v-if="!item.assignee">流程结束</p>
                  <p v-if="!item.assignee">结束时间：{{ item.endTime }}</p>
                </div>
              </div>
            </el-card>
          </el-timeline-item>
        </el-timeline>
      </div>
    </el-dialog>

    <!-- 更新节点时间 -->
    <el-dialog title="更新节点信息" :visible.sync="updateTimeDialog" width="30%" :close-on-press-escape="false"
               :close-on-click-modal="false" append-to-body>
      <el-form ref="form" :model="updateTimeForm" label-width="120px">
        <el-form-item label="节点名称">
          <el-input v-model="updateTimeForm.nodeName" disabled></el-input>
        </el-form-item>
        <el-form-item label="审核人(旧)" v-if="updateTimeForm.nodeName === '审核'">
          <el-input v-model="updateTimeForm.oldAssignee" disabled></el-input>
        </el-form-item>
        <el-form-item label="审核人(新)" v-if="updateTimeForm.nodeName === '审核'">
          <div style="width: 100%; display: flex; justify-content: space-between">
            <el-select v-model="updateTimeForm.assignee" clearable placeholder="请选择创建人" style="width: 80%">
              <el-option
                v-for="item in userSelectData"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              >
              </el-option>
            </el-select>
            <el-button icon="el-icon-search" type="primary" @click="userDialogOpen" style="width: 18%"></el-button>
          </div>
        </el-form-item>
        <el-form-item label="节点时间(旧)">
          <el-col :span="11">
            <el-date-picker
              v-model="updateTimeForm.hisStartTime"
              disabled
              type="datetime"
              style="width: 100%;"
              placeholder="选择日期时间">
            </el-date-picker>
          </el-col>
          <el-col style="text-align: center;" :span="2">-</el-col>
          <el-col :span="11">
            <el-date-picker
              v-model="updateTimeForm.hisEndTime"
              disabled
              type="datetime"
              style="width: 100%;"
              placeholder="选择日期时间">
            </el-date-picker>
          </el-col>
        </el-form-item>
        <el-form-item label="节点时间(新)">
          <el-col :span="11">
            <el-date-picker
              v-model="updateTimeForm.startTime"
              type="datetime"
              format="yyyy-MM-dd HH:mm:ss"
              value-format="yyyy-MM-dd HH:mm:ss"
              style="width: 100%;"
              placeholder="选择日期时间">
            </el-date-picker>
          </el-col>
          <el-col style="text-align: center;" :span="2">-</el-col>
          <el-col :span="11">
            <el-date-picker
              v-model="updateTimeForm.endTime"
              type="datetime"
              format="yyyy-MM-dd HH:mm:ss"
              value-format="yyyy-MM-dd HH:mm:ss"
              style="width: 100%;"
              placeholder="选择日期时间">
            </el-date-picker>
          </el-col>
        </el-form-item>
        <el-form-item label="审核变量(旧)" v-if="updateTimeForm.nodeName === '审核'">
          <el-input  type="textarea" :rows="2" v-model="updateTimeForm.oldContent" disabled></el-input>
        </el-form-item>
        <el-form-item label="审核变量(新)" v-if="updateTimeForm.nodeName === '审核'">
          <el-input  type="textarea" :rows="2" v-model="updateTimeForm.content"></el-input>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="updateTimeDialog = false">取 消</el-button>
        <el-button type="primary" @click="subUpdateInfo" :loading="updateTimeFormBtnLoading">保 存</el-button>
      </span>
    </el-dialog>

    <el-dialog :visible.sync="userDialog" title="创建人选择" width="40%" :close-on-press-escape="false" :close-on-click-modal="false" append-to-body class="userDialogBox">
      <div class="userBoxMain">
        <div class="searchBox">
          <el-row>
            <el-col :span="20">
              <el-input v-model="userParams.nickName" placeholder="请输入用户昵称" clearable @keyup.enter.native="userList"></el-input>
            </el-col>
            <el-col :span="4" style="display: flex; justify-content: flex-end;">
              <el-button type="primary" icon="el-icon-search" size="mini" @click="userList">搜索</el-button>
            </el-col>
          </el-row>
        </div>
        <div class="dataBox">
          <el-table size="mini" height="calc(100% - 50px)" border ref="tableRef" v-loading="userTableLoading" :data="userTableData">
            <el-table-column label="用户名称" fixed :show-overflow-tooltip="true" align="center" prop="userName" />
            <el-table-column label="用户昵称" fixed :show-overflow-tooltip="true" align="center" prop="nickName" />
            <el-table-column label="用户编码" fixed :show-overflow-tooltip="true" align="center" prop="userId" />
            <el-table-column label="操作" align="center" class-name="small-padding fixed-width" fixed="right">
              <template slot-scope="scope">
                <el-button size="mini" type="text" @click="userSelect(scope.row)">选择
                </el-button>
              </template>
            </el-table-column>
          </el-table>

          <pagination :total="userTableTotal" :page.sync="userParams.pageNum" :pager-count="2"
                      :limit.sync="userParams.pageSize" @pagination="userList" style="margin-right: 10px;" />
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
// -------------------- 引入 --------------------
// 组件
// import PileInput from '@/components/PileInput/index.vue' // 桩号（表单）
import CascadeSelection from '@/components/CascadeSelection/index.vue' // 管养处/路段/路线
import RangeInput from '@/views/baseData/components/rangeInput/index.vue' // 桩号（查询）
import ImageUpload from '@/views/disaster/ImageUpload.vue' // 图片上传组件
import AMapLoader from '@amap/amap-jsapi-loader'; // 高德地图
// 导入相关
import {getToken} from "@/utils/auth";

// API
import {
  batchStartProcess,
  createHighSlope,
  dataSync,
  deleteHighSlope,
  queryPageHighSlope,
  revokeProcess,
  updateHighSlope
} from '@/api/disaster/highSlope/highSlope'
import {deptTreeSelect} from '@/api/tmpl' // 管养单位
import {listByMaintenanceSectionId} from '@/api/baseData/common/routeLine' // 路段数据
import {listMaintenanceSectionAll} from '@/api/system/maintenanceSection' // 养护路段接口
import {getTreeByEntity} from '@/api/system/geography' // 区划接口
import {removeFile} from '@/api/system/fileUpload.js'
import {findFiles} from '@/api/file/index.js'
import {
  queryAuditInfo,
  queryTodoTaskByBusinessKey,
  queryTodoTaskByCategory,
  updateNodeInfo,
  updateNodeTime
} from "@/api/disaster/process/process";
import {listUser} from "@/api/system/user";
import sortFileUpload from "@/components/SortFileUpload/index.vue";

export default {
  name: "HighSlope",
  // 数据字典
  dicts: [
    'side_slope_relative_position', // 相对位置
    'roadbed_surface_type',  // 面层类型
    'side_slope_river_terrain', // 临河地形
    'side_slope_lithology', // 岩性
    'surface_drainage_facilities', // 地表排水设施
    'side_slope_underground_drainage_facilities', // 地下排水设施
    'side_slope_protection', // 坡面防护
    'protection_along_the_river', // 沿河防护
    'side_slope_support_facilities', // 支挡设施
    'seismic_fortification_level', // 抗震设防等级
    'side_slop_flood_control', // 防洪标准
    'sys_route_grade', // 路线等级
    'process_type'
  ],
  // 组件
  components: {
    // PileInput,
    CascadeSelection,
    RangeInput,
    ImageUpload,
    sortFileUpload
  },

  // -------------------- 变量 --------------------
  data() {
    return {

      /**
       * 查询相关
       */
      queryShow: false, // 隐藏筛选显隐
      queryParams: { // 查询参数
        pageNum: 1, // 页码
        pageSize: 10, // 每页条数
        managementMaintenanceIds: [], // 管养处
        maintenanceSectionId: '', // 路段编号
        routeCodes: [], // 路线编号
        highSlopeNum: '', // 高边坡编号
        highSlopeName: '', // 高边坡名称
        pileStartNum: '', // 起始桩号
        pileEndNum: '', // 结束桩号
        startTime: '', // 开始时间
        endTime: '', // 结束时间
        status: '' // 状态
      },
      queryTime: [], // 查询时间
      queryTotal: 0, // 总条数

      /**
       * 表格相关
       */
      tableData: [], // 表格数据
      tableSelection: [], // 表格选中数据
      tableLoading: false, // 表格加载
      businessKeyArr: [], // 业务主键

      /**
       * 表单相关
       */
      formParams: { // 表单参数
        id: '', // 主表ID
        highSlopeNum: '', // 高边坡编号
        highSlopeName: '', // 高边坡名称
        routerNum: '', // 路线编号
        routerName: '', // 路线名称
        startLatitudeAndLongitude: '', // 经纬度（起）
        endLatitudeAndLongitude: '', // 经纬度（止）
        technicalGrade: '', // 技术等级
        pileStartNum: '', // 桩号（起）
        pileEndNum: '', // 桩号（终）
        maintenanceUnitId: '', // 管养单位ID
        maintenanceUnitName: '', // 管养单位名称
        roadSectionId: '', // 路段ID
        roadSectionName: '', // 路段名称
        constructionYear: '', // 建造年度
        renovationYear: '', // 改建年度
        areaCode: '', // 区划代码
        relativePosition: '', // 相对位置
        slopeLength: '', // 边坡长度
        laneNum: '', // 车道数量
        roadbedWidth: '', // 路基宽度
        roadWidth: '', // 路面宽度
        surfaceLayerType: '', // 面层类型
        embankmentSlopeHeight: '', // 路堤边坡（高）
        embankmentSlope: '', // 路堤边坡（坡度）
        embankmentGrade: '', // 路堤边坡（分级）
        embankmentByARiver: '', // 是否临河
        embankmentRiversideTerrain: '', // 临河地形
        cuttingSlopeHeight: '', // 路堑边坡（坡高）
        cuttingSlope: '', // 路堑边坡（坡度）
        cuttingGrade: '', // 路堑边坡（分级）
        cuttingLithology: '', // 岩性
        surfaceDrainageFacilities: [], // 地表排水设施
        surfaceDrainageFacilitiesRemark: '', // 地表排水设施（其它）
        undergroundDrainageFacilities: [], // 地下排水设施
        undergroundDrainageFacilitiesRemark: '', // 地下排水设施（其它）
        curDamagedSlopeSurface: [], // 坡面防护
        curDamagedSlopeSurfaceRemark: '', // 坡面防护（其它）
        curDamagedRiverBank: [], // 沿河防护
        curDamagedRiverBankRemark: '', // 沿河防护备注
        curDamagedSupportFacility: [], // 支挡设施
        curDamagedSupportFacilityRemark: '', // 支挡设施（其它）
        seismicFortificationLevel: '', // 抗震设防等级
        floodControlStandard: '', // 防洪标准
        floodControlStandardRemark: '', // 防洪标准（其它）
        supplementaryInformation: '', // 其他需要说明的信息
        imgIds: '', // 照片
        dataSources: 'PC', // 数据来源
        processId: '', // 流程ID
        status: '', // 流程状态
        createName: '', // 采集人
        createTime: '', // 采集时间
      },
      formTitle: '高边坡信息调查表', // 表单dialog标题
      formDialog: false, // 表单dialog显隐
      formLoading: false, // 表单加载
      formBtnLoading: false, // 表单按钮加载
      formRiskType: '', // 表单灾害类型
      formRiskId: '', // 表单主表ID
      formType: 'add', // 表单类型
      formRules: { // 表单校验规则
        maintenanceUnitId: [
          {required: true, message: "管养单位不能为空", trigger: "blur"}
        ],
        roadSectionId: [
          {required: true, message: "路段信息不能为空", trigger: "blur"}
        ],
        routerNum: [
          {required: true, message: "路段信息不能为空", trigger: "blur"}
        ],
        technicalGrade: [
          {required: true, message: "技术等级不能为空", trigger: "blur"}
        ],
        routerName: [
          {required: true, message: "路线名称不能为空", trigger: "blur"}
        ],
        areaCode: [
          {required: true, message: "区划代码不能为空", trigger: "blur"}
        ],
        relativePosition: [
          {required: true, message: "相对位置不能为空", trigger: "blur"}
        ],
        surfaceDrainageFacilities: [
          {required: true, message: "地表排水设施不能为空", trigger: "blur"}
        ],
        undergroundDrainageFacilities: [
          {required: true, message: "地下排水设施不能为空", trigger: "blur"}
        ],
        curDamagedSlopeSurface: [
          {required: true, message: "坡面防护不能为空", trigger: "blur"}
        ],
        curDamagedRiverBank: [
          {required: true, message: "沿河防护不能为空", trigger: "blur"}
        ],
        curDamagedSupportFacility: [
          {required: true, message: "支挡设施不能为空", trigger: "blur"}
        ],
      },

      formMaintenanceList: [], // 管养单位列表
      formMaintenanceRenderList: [], // 管养单位渲染列表
      formRoadSectionList: [], // 路段列表
      formRouteList: [], // 路线列表
      // 图片上传相关
      formImgSrcList: [], // 图片渲染列表
      formImgUrlList: [], // 图片预览列表
      formUploadList: '', // 图片上传列表
      formImgOwnerId: '', // 图片上传ownerId
      formImgNum: [], // 图片数量（图片集用）

      /**
       * 桩号相关
       */
      pileStartK: '', // 起点桩号K
      pileStartAdd: '', // 起点桩号+
      pileEndK: '', // 终点桩号K
      pileEndAdd: '', // 终点桩号+

      /**
       * 边坡相关
       */
      slopeType: '0', // 边坡类型

      /**
       * 区划相关
       */
      divisionsProvincial: '53', // 省级区划
      divisionsProvincialList: [ // 省级区划列表
        {
          disabled: true,
          id: "53",
          label: "云南省",
          level: 1
        }
      ],
      divisionsMunicipal: '', // 市级区划
      divisionsMunicipalList: [], // 市级区划列表
      areaCodeList: [], // 县级区划列表


      /**
       * 导入相关
       */
      // 用户导入参数
      upload: {
        // 是否显示弹出层（高边坡数据导入）
        open: false,
        // 弹出层标题（高边坡数据导入）
        title: "",
        // 是否禁用上传
        isUploading: false,
        // 是否更新已经存在的高边坡数据
        // updateSupport: 0,
        // 提交类型
        submitType: '',
        // 设置上传的请求头部
        headers: {Authorization: "Bearer " + getToken()},
        // 上传的地址(新增)
        urlAdd: process.env.VUE_APP_BASE_API + "/disaster/high-slope/importDataAdd",
        // 上传的地址(更新)
        urlUpdate: process.env.VUE_APP_BASE_API + "/disaster/high-slope/importDataUpdate"
      },

      /**
       * 坐标相关
       */
      coordinateDialog: false, // 坐标dialog
      coordinateMap: null, // 地图实例
      coordinateMarker: null, // 地图标记
      coordinateType: 'start', // 坐标类型
      coordinatePosition: '', // 坐标经纬度
      coordinateSearch: '', // 坐标查询
      formIdeaList: [], // 表单流转意见数据
      updateTimeForm: {
        id: '',
        nodeName: '',
        hisStartTime: '',
        hisEndTime: '',
        startTime: '',
        endTime: '',
        oldAssignee: '',
        assignee: '',
        oldContent: '',
        content: '',
        taskId: ''
      },
      processId: '',
      updateTimeFormBtnLoading: false, // 表单按钮加载
      auditInfoDialog: false,
      updateTimeDialog: false,
      formProcessType: [], // 表单字典数据
      userDialog: false, // 用户dialog
      userParams: { // 用户查询参数
        pageNum: 1, // 页码
        pageSize: 10, // 每页条数
        nickName: '', // 用户昵称
      },
      userTableLoading: false, // 用户数据加载
      userTableData: [], // 用户数据
      userTableTotal: 0, // 用户数据总数
      userSelectData: [], // 用户选中数据
      clearData: false,
    }
  },
  // -------------------- 计算属性 --------------------
  computed: {
    formImgDateLine() {
      let dateLine = []
      if (this.formImgSrcList.length > 0) {
        this.formImgSrcList.forEach(item => {
          let date = item.createTime.split('T')[0]
          if (dateLine.length === 0) {
            dateLine.push({
              time: date,
              data: [item]
            })
          } else {
            let index = dateLine.findIndex(item2 => item2.time === date)
            if (index !== -1) {
              dateLine[index].data.push(item)
            } else {
              dateLine.push({
                time: date,
                data: [item]
              })
            }
          }
        })
        // 时间线排序
        dateLine.sort((a, b) => {
          // 将日期字符串分割并转换为日期对象
          let dateA = new Date(a.time);
          let dateB = new Date(b.time);

          // 比较日期
          return dateA - dateB;
        })
      }
      return dateLine
    },
    forEditDelDisabled() {
      if (this.tableSelection.length !== 1) {
        return true;
      }
      const selectedItem = this.tableSelection[0];
      if (selectedItem.status === '1' || selectedItem.status === '2') {
        return true;
      }
      return false;
    },
    forDeleteDelDisabled() {
      let reviewIndex = this.tableSelection.findIndex((item) => item.status === '1');
      let finishIndex = this.tableSelection.findIndex((item) => item.status === '2');
      if (this.tableSelection.length > 0 && reviewIndex === -1 && finishIndex === -1) {
        return false
      }
      return true
    },
    isButtonDisabled() {
      return (id) => {
        if (this.businessKeyArr.length === 0) {
          return true
        }
        return !this.businessKeyArr.includes(id);
      };
    }
  },
  mounted() {
    this.initPage()
  },
  // -------------------- 方法 --------------------
  methods: {

    /**
     * 页面相关
     */
    // 初始化页面
    initPage() {
      // 获取数据
      this.queryList()
      // 获取待办任务业务ID集合
      this.queryTodoTaskByCategory()
      // 查询字典类型数据
      this.queryType()
      // 获取管养单位数据
      this.queryMaintenanceList()
      // 获取区划数据（市级）
      this.queryDivisionsTree(2)
    },

    queryType() {
      this.getDicts("process_type").then((res) => {
        if (res.code === 200) {
          this.formProcessType = res.data
        }
      })
    },

    /**
     * 查询相关
     */
    // 点击查询按钮
    queryhandle() {
      this.queryList()
    },

    // 重置查询条件
    queryReset() {
      this.clearData = true
      this.queryParams = { // 查询参数
        pageNum: 1, // 页码
        pageSize: 10, // 每页条数
        managementMaintenanceIds: [], // 管养处
        maintenanceSectionId: '', // 路段编号
        routeCodes: [], // 路线编号
        highSlopeNum: '', // 高边坡编号
        highSlopeName: '', // 高边坡名称
        pileStartNum: '', // 起始桩号
        pileEndNum: '', // 结束桩号
        startTime: '', // 开始时间
        endTime: '', // 结束时间
      }
      this.queryhandle();
    },

    // 查询条件改变
    queryParamsUpdate(val) {
    },

    // 获取高边坡数据
    async queryList() {
      this.tableLoading = true
      await queryPageHighSlope(this.queryParams).then((res) => {
        if (res.code === 200) {
          this.tableData = res.rows
          this.queryTotal = res.total
          this.clearData = false
        }
        this.tableLoading = false
      }).catch((err) => {
        this.tableLoading = false
        this.$message.error(err)
      })
    },

    // 获取高边坡数据
    async queryTodoTaskByCategory() {
      await queryTodoTaskByCategory("高边坡采集").then((res) => {
        if (res.code === 200) {
          this.businessKeyArr = res.data
        }
      }).catch((err) => {
        this.$message.error(err)
      })
    },

    // 获取管养单位
    queryMaintenanceList() {
      let vo = {
        deptTypeList: [1, 3, 4],
        types: 100
      }
      deptTreeSelect(vo).then((res) => {
        if (res.code === 200) {
          this.formMaintenanceList = res.data
          this.handleMainRender(this.formMaintenanceList)
        }
      }).catch((err) => {
        this.$message.error(err)
        this.formMaintenanceList = []
        this.formMaintenanceRenderList = []
      })
    },

    // 管养单位渲染列表处理
    handleMainRender(data) {
      data.forEach(item => {
        this.formMaintenanceRenderList.push(item)
        if (item.children) {
          this.handleMainRender(item.children)
        }
      })
    },

    // 获取路段信息
    queryMaintenanceSectionList(val) {
      // getMaintenanceSectionListAll({ departmentIdList: val }).then((res) => {
      listMaintenanceSectionAll({departmentIdList: val}).then((res) => {
        if (res.code === 200) {
          this.formRoadSectionList = res.data
        }
      }).catch((err) => {
        this.$message.error(err)
        this.formRoadSectionList = []
      })
    },

    // 获取路线信息
    queryRouterList(val) {
      listByMaintenanceSectionId({maintenanceSectionId: val}).then(res => {
        if (res.code == 200) {
          this.formRouteList = res.data
        }
      }).catch((err) => {
        this.$message.error(err)
        this.formRouteList = []
      })
    },

    // 获取区划树
    queryDivisionsTree(level) {
      let vo = {}
      switch (level) {
        case 2:
          vo = {
            supCode: this.divisionsProvincial
          }
          break;

        case 3:
          vo = {
            supCode: this.divisionsMunicipal
          }
          break;
      }

      getTreeByEntity(vo).then((res) => {
        if (res) {
          switch (level) {
            case 2:
              this.divisionsMunicipalList = res.data
              break;

            case 3:
              this.areaCodeList = res.data
              break;
          }
        }
      })
    },

    // 获取图片数据
    async queryImg(id) {
      await findFiles({ ownerId: id }).then(res => {
        if (res.code === 200 && res.data.length > 0) {
          this.formImgSrcList = res.data.map(item => ({
            id: item.ownerId + '-' + item.id,
            name: item.originalFilename,
            url: item.thumbUrl,
            imgUrl: item.url,
            remark: item.remark,
            createTime: item.createTime,
            createBy: item.createBy
          }))
          this.formImgUrlList = res.data.map(item => item.url)
        }
      }).catch(() => {
        this.$message.error('图片查询失败，请重新打开表单尝试')
      })
    },

    /**
     * 表格相关
     */
    // 勾选表格项改变时
    tableSelectionChange(val) {
      this.tableSelection = val
    },

    // 点击一行时
    tableRowClick(val) {
      if (val.status === '0') {
        this.$refs.tableRef.toggleRowSelection(val)
      }
    },

    /**
     * 表单相关
     */

    // 点击新增时
    formDialogShow(val) {
      this.formRiskId = ''
      this.formInit('add', null, val)
    },

    // 点击修改时
    formEdit(type, item) {
      let val = null
      switch (type) {
        case 'select':
          val = this.tableSelection[0]
          break;
        case 'click':
          val = item
          break;
      }
      this.formDialog = true
      this.formRiskId = val.id
      this.formInit('edit', val)
    },

    formView(item) {
      this.formRiskId = item.id
      this.formInit('view', item)
      this.formDialog = true
    },

    // 初始化表单
    async formInit(type, item, riskType) {
      this.formType = type
      switch (type) {
        case 'add':
          // 重置表单
          this.formParams = {
            id: '', // 主表ID
            highSlopeNum: '', // 高边坡编号
            highSlopeName: '', // 高边坡名称
            routerNum: '', // 路线编号
            routerName: '', // 路线名称
            startLatitudeAndLongitude: '', // 经纬度（起）
            endLatitudeAndLongitude: '', // 经纬度（止）
            technicalGrade: '', // 技术等级
            pileStartNum: '', // 桩号（起）
            pileEndNum: '', // 桩号（终）
            maintenanceUnitId: '', // 管养单位ID
            maintenanceUnitName: '', // 管养单位名称
            roadSectionId: '', // 路段ID
            roadSectionName: '', // 路段名称
            constructionYear: '', // 建造年度
            renovationYear: '', // 改建年度
            areaCode: '', // 区划代码
            relativePosition: '', // 相对位置
            slopeLength: '', // 边坡长度
            laneNum: '', // 车道数量
            roadbedWidth: '', // 路基宽度
            roadWidth: '', // 路面宽度
            surfaceLayerType: '', // 面层类型
            embankmentSlopeHeight: '', // 路堤边坡（高）
            embankmentSlope: '', // 路堤边坡（坡度）
            embankmentGrade: '', // 路堤边坡（分级）
            embankmentByARiver: '', // 是否临河
            embankmentRiversideTerrain: '', // 临河地形
            cuttingSlopeHeight: '', // 路堑边坡（坡高）
            cuttingSlope: '', // 路堑边坡（坡度）
            cuttingGrade: '', // 路堑边坡（分级）
            cuttingLithology: '', // 岩性
            surfaceDrainageFacilities: [], // 地表排水设施
            surfaceDrainageFacilitiesRemark: '', // 地表排水设施（其它）
            undergroundDrainageFacilities: [], // 地下排水设施
            undergroundDrainageFacilitiesRemark: '', // 地下排水设施（其它）
            curDamagedSlopeSurface: [], // 坡面防护
            curDamagedSlopeSurfaceRemark: '', // 坡面防护（其它）
            curDamagedRiverBank: [], // 沿河防护
            curDamagedRiverBankRemark: '', // 沿河防护备注
            curDamagedSupportFacility: [], // 支挡设施
            curDamagedSupportFacilityRemark: '', // 支挡设施（其它）
            seismicFortificationLevel: '', // 抗震设防等级
            floodControlStandard: '', // 防洪标准
            floodControlStandardRemark: '', // 防洪标准（其它）
            supplementaryInformation: '', // 其他需要说明的信息
            imgIds: '', // 照片
            dataSources: 'PC', // 数据来源
            processId: '', // 流程ID
            status: '', // 流程状态
            createName: '', // 采集人
            createTime: '', // 采集时间
          }
          // 桩号重置
          this.pileStartK = ''
          this.pileStartAdd = ''
          this.pileEndK = ''
          this.pileEndAdd = ''
          // 边坡类型重置
          this.slopeType = '0'
          // 重置市级区划下拉
          this.divisionsMunicipal = ''
          this.areaCodeList = []
          // 重置路段/路线下拉
          this.formRoadSectionList = []
          this.formRouteList = []
          // 重置图片
          this.formImgSrcList = [] // 图片缩略图列表
          this.formImgUrlList = [] // 图片预览图列表
          this.formUploadList = '' // 图片上传列表
          this.formImgOwnerId = new Date().getTime().toString() // 图片上传ownerId
          // 打开表单dialog
          this.formDialog = true
          this.$nextTick(() => {
            // 清除主表表单验证
            this.$refs.formRef.clearValidate()
          })
          break;

        case 'edit':
        case 'view':
          // 表单赋值
          this.formParams = {
            id: item.id, // 主表ID
            highSlopeNum: item.highSlopeNum, // 高边坡编号
            highSlopeName: item.highSlopeName, // 高边坡名称
            routerNum: item.routerNum, // 路线编号
            routerName: item.routerName, // 路线名称
            startLatitudeAndLongitude: item.startLatitudeAndLongitude, // 经纬度（起）
            endLatitudeAndLongitude: item.endLatitudeAndLongitude, // 经纬度（止）
            technicalGrade: item.technicalGrade, // 技术等级
            pileStartNum: item.pileStartNum, // 桩号（起）
            pileEndNum: item.pileEndNum, // 桩号（终）
            maintenanceUnitId: item.maintenanceUnitId, // 管养单位ID
            maintenanceUnitName: item.maintenanceUnitName, // 管养单位名称
            roadSectionId: item.roadSectionId, // 路段ID
            roadSectionName: item.roadSectionName, // 路段名称
            constructionYear: item.constructionYear, // 建造年度
            renovationYear: item.renovationYear, // 改建年度
            areaCode: item.areaCode, // 区划代码
            relativePosition: item.relativePosition, // 相对位置
            slopeLength: item.slopeLength, // 边坡长度
            laneNum: item.laneNum, // 车道数量
            roadbedWidth: item.roadbedWidth, // 路基宽度
            roadWidth: item.roadWidth, // 路面宽度
            surfaceLayerType: item.surfaceLayerType, // 面层类型
            embankmentSlopeHeight: item.embankmentSlopeHeight, // 路堤边坡（高）
            embankmentSlope: item.embankmentSlope, // 路堤边坡（坡度）
            embankmentGrade: item.embankmentGrade, // 路堤边坡（分级）
            embankmentByARiver: item.embankmentByARiver, // 是否临河
            embankmentRiversideTerrain: item.embankmentRiversideTerrain, // 临河地形
            cuttingSlopeHeight: item.cuttingSlopeHeight, // 路堑边坡（坡高）
            cuttingSlope: item.cuttingSlope, // 路堑边坡（坡度）
            cuttingGrade: item.cuttingGrade, // 路堑边坡（分级）
            cuttingLithology: item.cuttingLithology, // 岩性
            surfaceDrainageFacilities: this.formArrayChange(item.surfaceDrainageFacilities), // 地表排水设施
            surfaceDrainageFacilitiesRemark: item.surfaceDrainageFacilitiesRemark, // 地表排水设施（其它）
            undergroundDrainageFacilities: this.formArrayChange(item.undergroundDrainageFacilities), // 地下排水设施
            undergroundDrainageFacilitiesRemark: item.undergroundDrainageFacilitiesRemark, // 地下排水设施（其它）
            curDamagedSlopeSurface: this.formArrayChange(item.curDamagedSlopeSurface), // 坡面防护
            curDamagedSlopeSurfaceRemark: item.curDamagedSlopeSurfaceRemark, // 坡面防护（其它）
            curDamagedRiverBank: this.formArrayChange(item.curDamagedRiverBank), // 沿河防护
            curDamagedRiverBankRemark: item.curDamagedRiverBankRemark, // 沿河防护备注
            curDamagedSupportFacility: this.formArrayChange(item.curDamagedSupportFacility), // 支挡设施
            curDamagedSupportFacilityRemark: item.curDamagedSupportFacilityRemark, // 支挡设施（其它）
            seismicFortificationLevel: item.seismicFortificationLevel, // 抗震设防等级
            floodControlStandard: item.floodControlStandard, // 防洪标准
            floodControlStandardRemark: item.floodControlStandardRemark, // 防洪标准（其它）
            supplementaryInformation: item.supplementaryInformation, // 其他需要说明的信息
            imgIds: item.imgIds, // 照片
            dataSources: item.dataSources, // 数据来源
            processId: item.processId, // 流程ID
            status: item.status, // 流程状态
            createName: item.createName, // 采集人
            createTime: item.createTime, // 采集时间
          }
          // 根据县级获取市级编码
          let check = false
          for (let i = 0; i < this.divisionsMunicipalList.length; i++) {
            if (check) break
            for (let j = 0; j < this.divisionsMunicipalList[i].children.length; j++) {
              if (this.divisionsMunicipalList[i].children[j].id === this.formParams.areaCode) {
                this.divisionsMunicipal = this.divisionsMunicipalList[i].id
                this.areaCodeList = this.divisionsMunicipalList[i].children
                check = true
                break
              }
            }
          }
          // 桩号赋值
          if (item.pileStartNum) {
            this.pileStartK = item.pileStartNum.split('+')[0].split('K')[1]
            this.pileStartAdd = item.pileStartNum.split('+')[1]
          }
          if (item.pileEndNum) {
            this.pileEndK = item.pileEndNum.split('+')[0].split('K')[1]
            this.pileEndAdd = item.pileEndNum.split('+')[1]
          }
          // 边坡类型赋值
          if (item.embankmentSlopeHeight) {
            this.slopeType = '0'
          } else {
            this.slopeType = '1'
          }
          // 重置路段/路线下拉
          this.formRoadSectionList = []
          this.formRoadSectionList.push({
            maintenanceSectionName: item.roadSectionName,
            maintenanceSectionId: item.roadSectionId
          })
          this.formRouteList = []
          this.formRouteList.push({routeName: item.routerName, routeCode: item.routerNum})
          // 重置图片渲染及上传接收列表
          this.formImgSrcList = []
          this.formImgUrlList = []
          this.formUploadList = '' // 图片上传列表

          this.formImgNum = [] // 图片集数量
          if (this.formParams.imgIds) {
            await this.queryImg(item.imgIds)
            this.formImgOwnerId = item.imgIds
          } else {
            this.formImgOwnerId = new Date().getTime().toString()
          }
          // 打开dialog
          this.formDialog = true
          this.$nextTick(() => {
            // 清除主表表单验证
            this.$refs.formRef.clearValidate()
          })
          break;
      }

      // 解决单选框报错问题（谷歌浏览器升级导致）
      await this.$nextTick(() => {
        for (let radio in this.$refs) {
          let refName = 'formRadio'
          let check = radio.includes(refName)
          if (check) {
            this.$refs[radio].$children.forEach((item) => {
              item.$refs.radio.removeAttribute("aria-hidden");
            });
          }
        }
      })
    },

    // 提交表单
    async formSubmit(formName, statusNum) {
      this.formParams.status = statusNum
      this.formParams.imgIds = this.formImgOwnerId
      this.$refs[formName].validate(async (valid) => {
        if (valid) {
          this.formBtnLoading = true
          // 判断图片数量
          // if(!this.formParams.imgIds) {
          //   this.formBtnLoading = false
          //   this.$message.warning('请上传示意图及照片（不少于3张）！')
          //   return
          // }
          // if(!this.formParams.imgIds.includes(",")) {
          //   this.formBtnLoading = false
          //   this.$message.warning('请上传示意图及照片（不少于3张）！')
          //   return
          // }
          // if(this.formParams.imgIds.split(",").length < 3) {
          //   this.formBtnLoading = false
          //   this.$message.warning('请上传示意图及照片（不少于3张）！')
          //   return
          // }
          // 桩号验证
          if (!this.pileStartK || !this.pileStartAdd) {
            this.$message.warning('请输入起点桩号！')
            this.formBtnLoading = false
            return
          }
          if (!this.pileEndK || !this.pileEndAdd) {
            this.$message.warning('请输入终点桩号！')
            this.formBtnLoading = false
            return
          }
          if (isNaN(this.pileStartK) || isNaN(this.pileEndK) || isNaN(this.pileStartAdd) || isNaN(this.pileEndAdd)) {
            this.$message.warning('桩号字段必须是数字！')
            this.formBtnLoading = false
            return;
          }

          // 起止点经纬度验证
          if (!this.formParams.startLatitudeAndLongitude) {
            this.$message.warning('起点经纬度未填写/拾取！')
            this.formBtnLoading = false
            return
          }
          if (!this.formParams.endLatitudeAndLongitude) {
            this.$message.warning('止点经纬度未填写/拾取！')
            this.formBtnLoading = false
            return
          }
          // 桩号组装
          this.formParams.pileStartNum = `K${this.pileStartK}+${this.pileStartAdd}`
          this.formParams.pileEndNum = `K${this.pileEndK}+${this.pileEndAdd}`
          // 边坡长度计算
          this.formParams.slopeLength = Math.abs(parseFloat(this.pileEndK + this.pileEndAdd) - parseFloat(this.pileStartK + this.pileStartAdd));
          // 路基路面验证
          // if (this.formParams.roadbedWidth < this.formParams.roadWidth) {
          //   this.$message.warning('路面宽度不得大于路基宽度！')
          //   this.formBtnLoading = false
          //   return
          // }
          // 创建提交参数
          let vo = JSON.parse(JSON.stringify(this.formParams))

          // 转换数组对象
          vo.surfaceDrainageFacilities = Array.isArray(vo.surfaceDrainageFacilities) ? vo.surfaceDrainageFacilities.join(",") : ''
          vo.undergroundDrainageFacilities = Array.isArray(vo.undergroundDrainageFacilities) ? vo.undergroundDrainageFacilities.join(",") : ''
          vo.curDamagedSlopeSurface = Array.isArray(vo.curDamagedSlopeSurface) ? vo.curDamagedSlopeSurface.join(",") : ''
          vo.curDamagedRiverBank = Array.isArray(vo.curDamagedRiverBank) ? vo.curDamagedRiverBank.join(",") : ''
          vo.curDamagedSupportFacility = Array.isArray(vo.curDamagedSupportFacility) ? vo.curDamagedSupportFacility.join(",") : ''

          switch (this.formType) {
            case 'add':
              await createHighSlope(vo).then((res) => {
                if (res.code === 200) {
                  this.$message.success('操作成功！')
                  this.formBtnLoading = false
                  this.formDialog = false
                  this.queryList()
                }
              }).catch((err) => {
                this.$message.error(err)
                this.formBtnLoading = false
              })
              break;

            case 'edit':
              await updateHighSlope(vo).then((res) => {
                if (res.code === 200) {
                  this.$message.success('操作成功！')
                  this.formBtnLoading = false
                  this.formDialog = false
                  this.queryList()
                }
              }).catch((err) => {
                this.$message.error(err)
                this.formBtnLoading = false
              })
              break;
          }
        } else {
          this.$message.warning('表单必填字段未填写！')
          this.formBtnLoading = false
        }
      })

    },

    // 删除表单项
    formDelete(type, item) {
      let deleteId = ''
      switch (type) {
        case 'batch':
          let ids = []
          this.tableSelection.forEach((row) => {
            ids.push(row.id)
          })
          deleteId = ids.join(',')
          break;
        case 'single':
          deleteId = item.id
          break;
      }
      this.$modal
        .confirm('确认删除？')
        .then(async () => {
          this.tableLoading = true
          await deleteHighSlope(deleteId).then(res => {
            if (res && res.code == '200') {
              this.queryList()
              this.$message.success('操作成功！')
            }
            this.tableLoading = false
          }).catch((err) => {
            this.$message.error(err)
            this.tableLoading = false
          })
        })
    },

    async formAudit(row) {
      try {
        // 查询待办任务
        const res = await queryTodoTaskByBusinessKey(row.id);

        // 检查响应结果
        if (res.code !== 200) {
          this.$message.error('查询待办任务失败，请稍后重试');
          return;
        }

        // 判断是否有任务数据
        if (!res.data) {
          this.$message.warning('未查询到待办任务信息');
          return;
        }

        // 动态解析流程表单路径
        const routePath = await this.formCheckTyoe(res.data.processDefinitionKey);
        if (!routePath) {
          this.$message.warning('未查询到流程表单信息');
          return;
        }

        // 构建路由并打开新窗口
        this.openAuditForm(res.data, routePath);
      } catch (error) {
        // 捕获并处理异常
        console.error('formAudit error:', error);
        this.$message.error('执行操作时发生异常，请稍后重试');
      }
    },

    /**
     * 打开流程表单页面
     * @param {Object} taskData - 待办任务数据
     * @param {string} routePath - 动态路由名称
     */
    openAuditForm(taskData, routePath) {
      const routeUrl = this.$router.resolve({
        name: routePath,
        query: {
          taskId: taskData.taskId,
          businessKey: taskData.businessKey,
          processInstanceId: taskData.processInstanceId,
          taskKey: taskData.taskKey,
          route: routePath,
          name: taskData.category,
          title: taskData.title,
          isApprove: '0',
        },
      });

      // 在新标签页打开
      window.open(routeUrl.href, '_blank');
    },

    // 确认流程类型
    formCheckTyoe(type) {
      let check = this.formProcessType.findIndex(item => item.dictValue === type)
      if (check !== -1) {
        return this.formProcessType[check].remark
      } else {
        return null
      }
    },

    async formAuditInfo(item) {
      this.processId = item.processId
      const auditData = await this.fetchAuditInfo(item.processId);
      if (auditData) {
        const data = await this.formIdeaChange(auditData);
        this.formIdeaList = data;
      }
      this.auditInfoDialog = true;
    },

    async fetchAuditInfo(processId) {
      try {
        const res = await queryAuditInfo(processId);
        if (res.code === 200) {
          return res.data;
        } else {
          throw new Error(res.message || '获取审核信息失败');
        }
      } catch (err) {
        this.$message.error(err.message || err);
        return null;
      }
    },

    updateNodeTime(item) {
      this.updateTimeForm = {
        id: '',
        nodeName: '',
        hisStartTime: '',
        hisEndTime: '',
        startTime: '',
        endTime: '',
        oldAssignee: '',
        assignee: '',
        oldContent: '',
        content: '',
        taskId: ''
      }
      this.updateTimeForm.id = item.id
      this.updateTimeForm.nodeName = item.nodeName
      this.updateTimeForm.hisStartTime = item.startTime
      this.updateTimeForm.hisEndTime = item.endTime
      this.updateTimeForm.oldAssignee = item.assignee
      // 直接访问 comment 字段
      if (item.content && typeof item.content === 'object') {
        this.updateTimeForm.oldContent = item.content.comment || '';
      } else {
        console.error("item.content 不是一个有效的对象:", item.content);
        this.updateTimeForm.oldContent = '';
      }
      this.updateTimeForm.taskId = item.taskId
      this.updateTimeDialog = true
    },

    userDialogOpen() {
      this.userList()
      this.userDialog = true
    },

    userList() {
      this.userTableLoading = true
      listUser(this.userParams).then((res) => {
        if(res.code === 200) {
          this.userTableData = res.rows
          this.userTableTotal = res.total
        }
        this.userTableLoading = false
      }).catch(() => {
        this.userTableLoading = false
      })
    },

    userSelect(item) {
      this.userSelectData = []
      this.userSelectData.push ({ value: item.userId, label: item.nickName })
      this.updateTimeForm.assignee = item.nickName + '@' + item.userId
      this.userDialog = false
    },

    async subUpdateInfo() {
      try {
        this.updateTimeFormBtnLoading = true;

        const res = await updateNodeInfo(this.updateTimeForm);
        if (res.code === 200) {
          this.$message.success("更新节点信息成功");
        } else {
          throw new Error(res.msg || '更新节点信息失败');
        }

        // 获取审核信息并更新表单
        const auditData = await this.fetchAuditInfo(this.processId);
        if (auditData) {
          const data = await this.formIdeaChange(auditData);
          this.formIdeaList = data;
        }

        // 关闭弹窗
        this.updateTimeDialog = false;

      } catch (err) {
        this.$message.error(err.message || err);
      } finally {
        // 确保加载状态在任何情况下都会被重置
        this.updateTimeFormBtnLoading = false;
      }
    },

    // 处理意见字段
    formIdeaChange(data) {
      for (let i = 0; i < data.length; i++) {
        if (data[i].content && data[i].content !== '无内容') {
          data[i].content = JSON.parse(data[i].content);
          if (data[i].content.operatingRecord) {
            data[i].content.operatingRecord = JSON.parse(data[i].content.operatingRecord);
          }
        }
      }
      return data;
    },

    // 导出表单
    formExport() {
      // 深拷贝 queryParams
      let vo = {...this.queryParams};

      // 检查是否有选中的数据行
      const isAllData = this.tableSelection.length === 0;

      // 根据是否选择数据行来设置提示消息和导出参数
      const message = isAllData
        ? '即将根据查询条件导出所有高边坡数据，此过程可能花费时间较长，是否继续？'
        : `已选择${this.tableSelection.length}条高边坡数据，确认导出数据？`;

      if (!isAllData) {
        // 仅导出选中项的情况，将选中项的 id 加入参数
        vo.highSlopeIds = this.tableSelection.map(row => row.id);
      }

      // 显示确认提示，并在确认后调用下载方法
      this.$modal
        .confirm(message)
        .then(() => {
          const filename = `高边坡信息_${new Date().getTime()}.xlsx`;
          this.download(
            '/disaster/high-slope/export',
            vo,
            filename
          );
        })
    },

    // 关闭表单
    formClose() {
      this.formParams.disasterType = ''
      this.formDialog = false
    },

    // 点击预览图片时
    formImgPreview(url) {
      let index = this.formImgUrlList.findIndex(item => item === url)
      if (index !== -1) {
        let moveUrl = this.formImgUrlList.splice(index, this.formImgUrlList.length - index)
        this.formImgUrlList.unshift(...moveUrl)
      }
    },

    // 点击管养单位时
    formMainItemClick(data) {
      if (data.label.includes('分处') || data.label.includes('红河管理处')) {
        this.formParams.maintenanceUnitId = data.id
        this.$refs.formMainRef.blur()
        this.formMaintenanceChange('1')
      } else {
        this.$message.warning('仅可选择分处！')
      }
    },

    // 管养单位/路段改变时
    formMaintenanceChange(type) {
      switch (type) {
        case '1':
          // 获取管养单位名称
          this.formGetName('1', this.formParams.maintenanceUnitId)
          // 重置路段及路线数据
          this.formParams.roadSectionId = ''
          this.formParams.roadSectionName = ''
          this.formRoadSectionList = []
          this.formParams.routerNum = ''
          this.formParams.routerName = ''
          this.formRouteList = []
          // 重置路线等级
          this.formParams.technicalGrade = ''
          // 重新获取路段数据列表
          this.queryMaintenanceSectionList(this.formParams.maintenanceUnitId)
          break;

        case '2':
          // 获取路段单位名称
          this.formGetName('2', this.formParams.roadSectionId)
          // 重置路线数据
          this.formParams.routerNum = ''
          this.formParams.routerName = ''
          this.formRouteList = []
          // 重置路线等级
          this.formGetTechnicalGrade(this.formParams.roadSectionId)
          // 重新获取路线数据列表
          this.queryRouterList(this.formParams.roadSectionId)
          break;

        case '3':
          // 获取路段单位名称
          this.formGetName('3', this.formParams.routerNum)
          break;
      }
    },

    // 获取管养单位/路段/路线名称
    formGetName(type, val) {
      switch (type) {
        case '1':
          for (let i = 0; i < this.formMaintenanceRenderList.length; i++) {
            if (this.formMaintenanceRenderList[i].id == val) {
              this.formParams.maintenanceUnitName = this.formMaintenanceRenderList[i].label
              break
            }
          }
          break;

        case '2':
          for (let i = 0; i < this.formRoadSectionList.length; i++) {
            if (this.formRoadSectionList[i].maintenanceSectionId == val) {
              this.formParams.roadSectionName = this.formRoadSectionList[i].maintenanceSectionName
              break
            }
          }
          break;

        case '3':
          for (let i = 0; i < this.formRouteList.length; i++) {
            if (this.formRouteList[i].routeCode == val) {
              this.formParams.routerName = this.formRouteList[i].routeName
              break
            }
          }
          break;
      }
    },

    // 获取技术等级
    formGetTechnicalGrade(val) {
      for (let i = 0; i < this.formRoadSectionList.length; i++) {
        if (this.formRoadSectionList[i].maintenanceSectionId == val) {
          this.formParams.technicalGrade = this.formRoadSectionList[i].routeGrade
          break
        }
      }
    },

    // 市级区划改变时
    formChangeAreaCode(val) {
      this.formParams.areaCode = ''
      if (val) {
        let index = this.divisionsMunicipalList.findIndex(item => item.id === val)
        if (index !== -1) {
          this.areaCodeList = this.divisionsMunicipalList[index].children
        }
      } else {
        this.areaCodeList = []
      }
    },

    // 表单数组转换
    formArrayChange(item) {
      if (item) {
        let check = item.includes(',')
        if (check) {
          return item.split(',')
        } else {
          return [item]
        }
      } else {
        return []
      }
    },

    // 表单是否临河改变时
    formRiverChange() {
      // 清除临河地形
      if (this.formParams.embankmentByARiver === '1') {
        this.formParams.embankmentRiversideTerrain = null
      }
    },

    // 表单其他控制
    formRadioChange(type, remark) {
      if (this.formParams[type] !== '99') {
        this.formParams[remark] = ''
      }
    },

    // 表单单选选择无时处理
    formRadioClear(target, typeArray) {
      if (this.formParams[target] === '1') {
        typeArray.forEach(type => {
          // 移除表单验证
          delete this.formRules[type]
          if (Array.isArray(this.formParams[type])) {
            this.formParams[type] = []
          } else {
            this.formParams[type] = ''
          }
        })
        switch (target) {
          case 'hisDisasterHappened':
            this.formParams.hisHappenedFrequency = 0
            break;

          case 'curCracked':
            break;

          case 'curDamaged':
            this.formParams.curDamagedOther = ''
            break;
        }
      } else {
        // 添加表单验证
        for (let i = 0; i < typeArray.length; i++) {
          if (typeArray[i].includes("Remark")) {
            continue
          }
          this.formRules[typeArray[i]] = [
            {required: true, message: "该数据为必填/必选", trigger: "blur"}
          ]
        }
      }
    },

    // 删除图片
    formDeleteImg(id) {
      this.$modal.confirm('是否确认删除该图片？').then(async () => {
        this.$modal.loading('正在删除图片，请稍候...')
        removeFile(id.split('-')[1]).then((res) => {
          if (res.code === 200) {
            this.$message.success('删除图片成功！')
            // 移除预览列表图片
            let index = this.formImgSrcList.findIndex(item => item.id === id)
            if (index !== -1) {
              let imgUrl = this.formImgSrcList[index].imgUrl
              let imgIndex = this.formImgUrlList.indexOf(imgUrl)
              if (imgIndex !== -1) {
                this.formImgUrlList.splice(imgIndex, 1)
              }
            }
            // 移除渲染列表图片
            this.formImgSrcList = this.formImgSrcList.filter(item => item.id !== id)
          }
          this.$modal.closeLoading()
        }).catch(() => {
          this.$message.error('删除图片失败')
          this.$modal.closeLoading()
        })
      })
    },
    /**
     * 边坡相关
     */
    // 当边坡类型改变时
    slopeTypeChange() {
      switch (this.slopeType) {
        case '0':
          // 重置路堑数据
          this.formParams.cuttingSlopeHeight = null
          this.formParams.cuttingSlope = null
          this.formParams.cuttingGrade = null
          this.formParams.cuttingLithology = null
          break;

        case '1':
          // 重置路堤数据
          this.formParams.embankmentSlopeHeight = null
          this.formParams.embankmentSlope = null
          this.formParams.embankmentGrade = null
          this.formParams.embankmentByARiver = null
          this.formParams.embankmentRiversideTerrain = null
          break;
      }
    },

    /**
     * 导入相关
     */
    /** 导入按钮操作 */
    handleImportAdd() {
      this.upload.title = "高边坡数据导入(新增)";
      this.upload.submitType = 'add'
      this.upload.open = true;
    },

    handleImportUpdate() {
      this.upload.title = "高边坡数据导入(更新)";
      this.upload.submitType = 'update'
      this.upload.open = true;
    },

    /** 下载模板操作 */
    importTemplate() {
      // let vo = {
      //   disasterType: this.formRiskType
      // }
      this.download('/disaster/high-slope/importTemplate',
        {},
        `高边坡数据模板_${new Date().getTime()}.xlsx`)
    },

    // 文件上传中处理
    handleFileUploadProgress(event, file, fileList) {
      this.upload.isUploading = true;
    },

    // 文件上传成功处理
    handleFileSuccess(response, file, fileList) {
      this.upload.open = false;
      this.upload.isUploading = false;
      this.$refs.upload.clearFiles();
      // this.$alert(response.msg, "导入结果", { dangerouslyUseHTMLString: true });
      this.$alert("<div style='overflow: auto;overflow-x: hidden;max-height: 70vh;padding: 10px 20px 0;'>" + response.msg + "</div>", "导入结果", {dangerouslyUseHTMLString: true});
      this.queryList();
    },

    // 提交上传文件
    submitFileForm() {
      this.$refs.upload.submit();
    },

    /**
     * 坐标相关
     */
    // 打开地图dialog
    openCoordinateDialog(type) {
      this.coordinateDialog = true
      this.coordinateType = type
      this.$nextTick(() => {
        this.coordinateInit()
      })
    },

    // 地图初始化
    coordinateInit() {
      const _this = this
      // 重置筛选结果
      this.coordinateSearch = ''

      window._AMapSecurityConfig = {
        securityJsCode: "8ba5a60100192adc21a2044b9582e26e", // 安全密钥
      };
      AMapLoader.load({
        key: "38ce82094eecafcb00a7dd5b323cc4d0", // 申请好的Web端开发者Key，首次调用 load 时必填
        version: "2.0", // 指定要加载的 JSAPI 的版本，缺省时默认为 1.4.15
        // plugins: ["AMap.ToolBar", "AMap.Scale"], //需要使用的的插件列表，如比例尺'AMap.Scale'，支持添加多个如：['...','...']
      })
        .then((AMap) => {
          // 变量初始化
          let positionInit = [102.8207599, 24.8885797] // 默认坐标昆明市
          switch (_this.coordinateType) {
            case 'start':
              if (_this.formParams.startLatitudeAndLongitude && _this.formParams.startLatitudeAndLongitude.includes(",")) {
                let start = _this.formParams.startLatitudeAndLongitude.split(",")
                start.forEach(item => {
                  item = Number(item)
                })
                positionInit = start
              }
              break;

            case 'end':
              if (_this.formParams.endLatitudeAndLongitude && _this.formParams.endLatitudeAndLongitude.includes(",")) {
                let end = _this.formParams.endLatitudeAndLongitude.split(",")
                end.forEach(item => {
                  item = Number(item)
                })
                positionInit = end
              }
              break;
          }
          _this.coordinatePosition = positionInit.join(",")


          // 地图初始化
          let satellite = new AMap.TileLayer.Satellite(); //创建卫星图层
          let roadNet = new AMap.TileLayer.RoadNet(); //创建路网图层
          _this.coordinateMap = new AMap.Map("coordinateBox", {
            // 设置地图容器id
            viewMode: "3D", // 是否为3D地图模式
            zoom: 11, // 初始化地图级别
            center: positionInit, // 初始化地图中心点位置
            layers: [satellite, roadNet], // 地图图层的数组
          })
          AMap.plugin('AMap.ToolBar', function () {
            let toolbar = new AMap.ToolBar(); // 缩放工具条实例化
            _this.coordinateMap.addControl(toolbar); //添加控件
          })
          AMap.plugin('AMap.Scale', function () {
            let scale = new AMap.Scale(); // 比例尺工具条实例化
            _this.coordinateMap.addControl(scale); //添加控件
          })

          // 初始化坐标点
          if (_this.coordinateMarker) {
            _this.coordinateMarker = null
          }
          _this.coordinateMarker = new AMap.Marker({
            position: positionInit
          })
          _this.coordinateMap.add(_this.coordinateMarker)

          // 地图绑定点击事件
          _this.coordinateMap.on("click", _this.coordinateClick);
        })
        .catch((e) => {
          console.log(e);
        });
    },

    // 添加地图点击事件
    coordinateClick(e) {
      // 清除原有标记
      if (this.coordinateMarker) {
        this.coordinateMap.remove(this.coordinateMarker)
      }
      this.coordinateMarker = new AMap.Marker({
        position: [e.lnglat.getLng(), e.lnglat.getLat()]
      })
      this.coordinateMap.add(this.coordinateMarker)
      // 坐标点赋值
      this.coordinatePosition = `${e.lnglat.getLng()},${e.lnglat.getLat()}`
    },

    // 添加查询事件
    coordinateList() {
      const _this = this
      AMap.plugin('AMap.PlaceSearch', function () {
        let autoOptions = {
          city: ''
        }
        let placeSearch = new AMap.PlaceSearch(autoOptions);
        placeSearch.search(_this.coordinateSearch, function (status, result) {
          // 搜索成功时，result即是对应的匹配数据
          if (status === 'complete') {
            if (result.info === 'OK' && result.poiList.count > 0) {
              let lng = result.poiList.pois[0].location.lng
              let lat = result.poiList.pois[0].location.lat
              // 重置中心点
              _this.coordinateMap.setCenter([lng, lat])
              // 清除原有标记
              if (_this.coordinateMarker) {
                _this.coordinateMap.remove(_this.coordinateMarker)
              }
              _this.coordinateMarker = new AMap.Marker({
                position: [lng, lat]
              })
              _this.coordinateMap.add(_this.coordinateMarker)
              // 坐标点赋值
              _this.coordinatePosition = `${lng},${lat}`
            } else {
              _this.$message.warning('暂无数据！')
            }
          } else {
            _this.$message.warning('查询失败！')
          }
        })
      })
    },

    // 保存当前坐标
    coordinateSave() {
      if (!this.coordinatePosition) {
        this.$message.warning('请选择经纬度！')
        return
      }
      switch (this.coordinateType) {
        case 'start':
          this.formParams.startLatitudeAndLongitude = this.coordinatePosition
          break;

        case 'end':
          this.formParams.endLatitudeAndLongitude = this.coordinatePosition
          break;
      }
      this.coordinateDialog = false
    },

    // 窗口关闭时销毁地图
    coordinateClose() {
      if (this.coordinateMap) {
        this.coordinateMap.off("click", this.coordinateClick);
        this.coordinateMap.destroy()
        this.coordinateMap = null
      }
    },
    excDataSync() {
      this.$modal
        .confirm('是否确认同步数据？')
        .then(async () => {
          this.tableLoading = true;
          await dataSync().then(res => {
            if (res && res.code == '200') {
              this.$message.success(res.msg);
              this.tableLoading = false;
            }
          }).catch((err) => {
            this.$message.error(err);
            this.tableLoading = false;
          })
        })
    },
    expCard() {
      var userId = this.$store.getters.userId;
      var params = {...this.queryParams, reportId: '1015773521003847680', loginUserId: userId};
      if (params.managementMaintenanceIds && params.managementMaintenanceIds.length > 0) {
        params.managementMaintenanceIds = params.managementMaintenanceIds.join(",");
      }
      if (params.routeCodes && params.routeCodes.length > 0) {
        params.routeCodes = params.routeCodes.join(",");
      }
      if (params.startTime) {
        params.startTime = params.startTime.substring(0, 10);
      }
      if (params.endTime) {
        params.endTime = params.endTime.substring(0, 10);
      }
      this.$tab.openPage('公路高边坡信息导出', '/disaster/exp_card/highslope', params);
    },
    formRevoke(type, item) {
      let ids = [];
      switch (type) {
        case 'batch':
          this.tableSelection.forEach((row) => {
            if (row.status === '1' || row.status==='4') {
              ids.push(row.id)
            }
          })
          break;
        case 'single':
          ids.push(item.id);
          break;
      }
      if (ids.length === 0) {
        this.$alert('请至少选择一条审核中业务！', '提示信息', {
          confirmButtonText: '确定'
        });
        return;
      }
      let idsStr = ids.join(",");
      this.$modal
        .confirm('是否确认撤销所选业务？')
        .then(async () => {
            this.$prompt('请输入撤销原因', '提示信息', {
              confirmButtonText: '确定',
              cancelButtonText: '取消',
              inputValidator: (value) => { //非空验证
                if (!value) {
                  return '撤销原因不能为空!'
                }
              }
            }).then(async ({value}) => {
                this.tableLoading = true
                await revokeProcess({type: '1', ids: idsStr, reason: value}).then(res => {
                  if (res && res.code == '200') {
                    if (res.data.msg) {
                      this.$message.warning('已成功撤销：' + res.data.success + ' 条数据，失败：' + res.data.failure + " 条，信息：" + res.data.msg);
                    } else {
                      this.$message.success('操作成功！');
                    }
                    this.queryList();
                    this.tableLoading = false;
                  }
                }).catch((err) => {
                  this.$message.error(err);
                  this.tableLoading = false;
                })
              }
            ).catch()
          }
        )
    },
    batchStart() {
      let ids = [];
      this.tableSelection.forEach((row) => {
        if (row.status==='3') {
          ids.push(row.id)
        }
      })
      if (ids.length === 0) {
        this.$alert('请至少选择一条已撤销业务！', '提示信息', {
          confirmButtonText: '确定'
        });
        return;
      }
      let idsStr = ids.join(",");
      this.$modal
        .confirm('确认启动所选流程？')
        .then(async () => {
          this.tableLoading = true
          await batchStartProcess(idsStr).then(res => {
            if (res && res.code == '200') {
              this.queryList()
              this.$message.success('操作成功！')
            }
            this.tableLoading = false
          }).catch((err) => {
            this.$message.error(err)
            this.tableLoading = false
          })
        })
    }
  },
}

</script>

<style lang="scss" scoped>
.app-container form:first-child .el-select,
.app-container form:nth-child(2) .el-select,
.app-container form:nth-child(2) ::v-deep .el-form-item__content,
.app-container form:first-child ::v-deep .el-form-item__content {
  width: 240px;
}

.app-container form:first-child .el-form-item:last-child ::v-deep .el-form-item__content {
  width: auto;
}

.app-container {
  padding: 10px;
  background-color: #c0c0c0;
  box-sizing: border-box;
}

.formDialog {
  ::v-deep .el-dialog__body {
    height: 600px;
    overflow-y: auto;
  }

  .dialog-footer {
    width: 100%;

    .footerTip {
      color: #888888;
      font-size: 14px;
    }
  }

  .titleBox {
    height: 22px;
    display: flex;
    flex-direction: row;
    align-items: center;

    .title {
      font-size: 16px;
      color: black;
      margin: 0;
    }

    .subTitle {
      margin-left: 15px;
      font-size: 12px;
      color: #888888;
    }
  }
}

.searchBox {
  padding: 10px;
  background: #fff;
  border-radius: 10px;
  transition: all .1s linear;
  display: flex;
  flex-direction: column;

  .searchMoreBox {
    min-width: 192px;
    margin-top: 10px;
    display: flex;
    align-items: center;
    flex-direction: row;
  }
}

.tableDiv {
  margin-top: 10px;
  background-color: white;
  padding-bottom: 10px;
  border-radius: 10px;
  transition: all .1s linear;
  display: flex;
  flex-direction: column;

  .btnBox {
    padding: 10px;
  }
}

.infoBox {
  padding: 15px 15px 0 15px;
  box-sizing: border-box;
  border-radius: 6px;
  border: 1px solid #C4C4C4;
  position: relative;

  .infoTitle {
    user-select: none;
    position: absolute;
    top: 0;
    left: 0;
    padding: 0 10px;
    font-size: 14px;
    line-height: 14px;
    font-weight: bold;
    transform: translateX(15px) translateY(-50%);
    background-color: white;
  }

  .imgBox {
    height: auto;
    width: 100%;
    // display: flex;
    // flex-wrap: wrap;
    // align-content: flex-start;

    // .imgItemBox {
    //   height: 240px;
    //   width: calc(100% / 3);
    //   box-sizing: border-box;
    //   padding: 10px;
    //   overflow-y: auto;
    //   display: flex;
    //   align-items: center;
    //   justify-content: center;
    //   position: relative;

    //   .imgDeleteBtn {
    //     position: absolute;
    //     z-index: 1;
    //     top: 10%;
    //     right: 10%;
    //   }
    // }

    ::v-deep .el-card__body {
      width: 100%;
      display: flex;
      flex-direction: row;
      flex-wrap: wrap;
      align-content: flex-start;
    }

    .imgBoxCard {
      width: 100%;

      .cardMain {
        height: 260px;
        width: 33%;
        box-sizing: border-box;
        padding: 0 10px;
        display: flex;
        flex-direction: column;
        position: relative;

        .imgDeleteBtn {
          position: absolute;
          z-index: 1;
          top: 20%;
          right: 5%;
        }

        .imgTitle {
          height: 28px;
          width: 100%;
          font-size: 16px;
          font-weight: bold;
          white-space: nowrap;
          text-overflow: ellipsis;
          overflow: hidden;
        }

        .img {
          height: calc(100% - (28px + 28px));
          width: 100%;
          padding: 10px 0;
          position: relative;
          z-index: 0;
        }

        .footer {
          height: 28px;
          color: #888888;
          font-size: 14px;
        }
      }
    }
  }

  .noneBox {
    user-select: none;
    height: 200px;
    width: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    color: #888888;
  }
}

.coordinateDialog {

  .coordinateMap {
    height: 600px;
    width: 100%;
    position: relative;

    #coordinateBox {
      height: 100%;
      width: 100%;
      border-radius: 5px;
      position: relative;
      z-index: 0;
    }

    .coordinateSearch {
      position: absolute;
      z-index: 1;
      top: 10px;
      left: 10px;
      width: 50%;
      padding: 10px;
      box-sizing: border-box;
      background-color: #fff;
      border-radius: 5px;
      border: 1px solid #DCDFE6;
      box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.12), 0 0 6px 0 rgba(0, 0, 0, 0.04);
      display: flex;
      flex-direction: row;
    }

    .coordinateTip {
      position: absolute;
      z-index: 1;
      top: 10px;
      right: 10px;
      padding: 10px;
      box-sizing: border-box;
      background-color: #fff;
      border-radius: 5px;
      border: 1px solid #DCDFE6;
      box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.12), 0 0 6px 0 rgba(0, 0, 0, 0.04);
    }
  }
}

.hisMainBox {
  width: 100%;

  .hisIdeaBox {
    width: 100%;
    display: flex;
    flex-direction: column;

    .boxMain {
      width: 100%;
      display: flex;
      flex-direction: column;

      .changeBox {
        width: 50%;
        box-sizing: border-box;
        padding: 10px;
        border-radius: 5px;
        border: 1px solid #DCDFE6;
        box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.12), 0 0 6px 0 rgba(0, 0, 0, 0.04);
        display: flex;
        flex-direction: column;

        .title {
          padding: 10px;
          user-select: none;
          color: #888888;
          font-size: 14px;
          font-weight: bold;
          border-bottom: 1px solid #DCDFE6;
        }

        .list {
          padding: 10px;
          user-select: none;
          color: #888888;
          font-size: 14px;
          border-bottom: 1px solid #DCDFE6;
          display: flex;
          flex-direction: row;
          flex-wrap: wrap;
          align-items: center;

          p {
            margin: 0;
            line-height: 20px;
          }
        }

        .none {
          padding: 10px;
          user-select: none;
          color: #888888;
          font-size: 14px;
        }
      }
    }

    .boxBottom {
      width: 100%;
      font-size: 14px;
      color: #888888;
      display: flex;
      justify-content: space-between;
    }
  }
}

.userDialogBox {
  ::v-deep .el-dialog__body {
    height: 600px;
    overflow-y: auto;
  }

  .userBoxMain {
    height: 100%;
    width: 100%;
    display: flex;
    flex-direction: column;

    .searchBox {
      height: 48px;
      width: 100%;
    }

    .dataBox {
      height: calc(100% - 48px);
      width: 100%;
      overflow-y: auto;
    }
  }
}

::v-deep .el-divider--horizontal {
  margin: 0 0 18px 0;
}

// v-if过渡动画
// 查询框
.search-enter-active {
  transition: all .1s linear;
}

.search-enter {
  opacity: 0;
}

.search-leave-active {
  transition: all .1s linear;
}

.search-leave-to {
  opacity: 0;
}
</style>
