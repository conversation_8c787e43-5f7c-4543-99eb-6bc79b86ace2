<template>
	<div class="login">
		<div class="logo">
			<img :src="logoInfo.topLogUrl" alt="" />
			<!-- <img src="@/assets/login/logo.png" alt="" /> -->
			<div class="logo-name">
				{{ logoInfo.title }}
				<span style="color: red; font-size: 15px">{{ logoInfo.version }}</span>
				<!-- <div>云南高速公路智慧养护管理平台</div>
        <div>云南交投集团</div> -->
			</div>
			<template v-if="logoInfo.homePath !== '/xizangindex'">
				<div class="warming">本平台为互联网非涉密平台，严禁处理、传输国家秘密</div>
			</template>
		</div>
		<el-form ref="loginForm" :model="loginForm" :rules="loginRules" class="login-form" v-if="!showForgetPassword">
			<!-- <h3 class="title">云南高速公路智慧养护管理平台</h3> -->
			<img :src="logoInfo.middleLogUrl" alt="" class="login-img" />
			<!-- <img src="@/assets/login/logo-1.png" alt="" class="login-img" /> -->
			<div class="divider"></div>

			<!-- 登录方式切换按钮 -->
			<div class="login-type-switch">
				<span :class="{ active: loginType === 'account' }" @click="switchLoginType('account')">
					账号密码登录
				</span>
				<span :class="{ active: loginType === 'phone' }" @click="switchLoginType('phone')">
					手机号登录
				</span>
			</div>

			<!-- 账号密码登录表单 -->
			<template v-if="loginType === 'account'">
				<el-form-item prop="username">
					<el-input v-model="loginForm.username" type="text" auto-complete="off" placeholder="账号">
						<img slot="prefix" src="@/assets/login/user.png" alt=""
							style="width: 15px; height: 15px; margin-top: 12px" />
					</el-input>
				</el-form-item>
				<el-form-item prop="password">
					<el-input v-model="loginForm.password" type="password" auto-complete="off" placeholder="密码"
						@keyup.enter.native="handleLogin">
						<img slot="prefix" src="@/assets/login/pwd.png" alt=""
							style="width: 15px; height: 15px; margin-top: 12px" />
					</el-input>
				</el-form-item>
				<el-form-item prop="code" v-if="captchaEnabled">
					<el-input v-model="loginForm.code" auto-complete="off" placeholder="验证码" style="width: 63%"
						@keyup.enter.native="handleLogin">
						<img slot="prefix" src="@/assets/login/code.png" alt=""
							style="width: 15px; height: 15px; margin-top: 12px" />
					</el-input>
					<div class="login-code">
						<img :src="codeUrl" @click="getCode" class="login-code-img" />
					</div>
				</el-form-item>

				<el-form-item style="width: 100%">
					<el-button :loading="loading" size="medium" type="primary" style="width: 100%; opacity: 0.7"
						@click.native.prevent="handleLogin">
						<span v-if="!loading">登 录</span>
						<span v-else>登 录 中...</span>
					</el-button>
					<div style="float: right" v-if="register">
						<router-link class="link-type" :to="'/register'">立即注册</router-link>
					</div>
				</el-form-item>
				<div style="margin: 0px 0px 25px 0px; display: flex; align-items: center; padding: 0 25px">
					<el-checkbox v-model="loginForm.rememberMe">记住密码</el-checkbox>
					<el-button type="text" class="forget-pwd" @click="handleForgetPwd">忘记密码？</el-button>
				</div>
			</template>

			<!-- 手机号登录表单 -->
			<phone-login v-else @login-success="handleLoginSuccess" />
		</el-form>

		<!-- 忘记密码组件 -->
		<div v-else class="login-form">
			<forget-password @back="showForgetPassword = false" @reset-success="handleLoginSuccess" />
		</div>

		<!--  底部  -->
		<template v-if="logoInfo.homePath !== '/xizangindex'">
			<!-- 下载APP -->
			<div class="download-app">
				<el-popover placement="top" width="350" trigger="hover">
					<el-row>
						<el-col :span="12">
							<div class="download-app-title">安卓APP下载</div>
							<div class="download-app-qrcode">
								<img src="../assets/qrcode-android.png" />
							</div>
						</el-col>
						<el-col :span="12">
							<div class="download-app-title">苹果APP下载</div>
							<div class="download-app-qrcode">
								<img src="../assets/qrcode-ios.png" />
							</div>
						</el-col>
					</el-row>
					<el-button class="download-app-btn" slot="reference">移动APP下载</el-button>
				</el-popover>
			</div>

			<div class="el-login-footer">
				<span>
					{{ `2025 云南云岭高速公路工程咨询有限公司 | 版本 ${logoInfo.version} | 更新日志 | ICP备案/许可证号:滇ICP备17011551号-9` }}
				</span>
				<img src="@/assets/login/dgwa.png" alt="" />
				<span>滇公网安备 53011202001170号</span>
			</div>
		</template>
	</div>
</template>

<script>
import { getCodeImg, getLoginLog } from '@/api/login'
import Cookies from 'js-cookie'
import { decrypt as AesDecrypt } from '@/utils/aesutil'
import { encrypt, decrypt } from '@/utils/jsencrypt'
import { getHomeUrl } from '@/utils/auth'
import PhoneLogin from '@/components/login/PhoneLogin.vue'
import ForgetPassword from '@/components/login/ForgetPassword'
import { getMergeShape } from "@/api/oneMap/deptInfo";
import { getVectorTile, getDataCockpitGeoJson } from "@/api/cockpit/index";
import IndexedDBStorage, { removeIndexedDB } from '@/utils/IndexedDb.js'

export default {
	name: 'Login',
	components: {
		PhoneLogin,
		ForgetPassword,
	},
	data() {
		return {
			isTest: '',
			codeUrl: '',
			loginForm: {
				username: '',
				password: '',
				rememberMe: false,
				code: '',
				uuid: '',
			},
			loginRules: {
				username: [{ required: true, trigger: 'blur', message: '请输入您的账号' }],
				password: [{ required: true, trigger: 'blur', message: '请输入您的密码' }],
				code: [{ required: true, trigger: 'change', message: '请输入验证码' }],
			},
			loading: false,
			// 验证码开关
			captchaEnabled: true,
			// 注册开关
			register: false,
			redirect: undefined,
			loginType: 'account', // 登录方式：account-账号密码，phone-手机号
			showForgetPassword: false, // 是否显示忘记密码组件
			logoInfo: {
				title: '',
				version: '',
				topLogUrl: '',
				middleLogUrl: '',
				backgroundImage: '',
			},
		}
	},
	watch: {
		$route: {
			handler: function (route) {
				this.redirect = route.query && route.query.redirect
			},
			immediate: true,
		},
	},
	created() {
		this.getCode()
		this.getCookie()

		let redirect = this.$route.query.redirect
		console.log('redirect: ', redirect)
		let url, userid, adminuser
		if (redirect) {
			//外部跳转免登陆
			url = redirect.substring(0, redirect.indexOf('?'))
			userid = decodeURIComponent(this.getParam(redirect, 'userid='))
		} else {
			//内部超级管理员指定登录
			//获取url上参数
			const params = new URLSearchParams(window.location.search)
			userid = params.get('userid')
			adminuser = params.get('adminuser')
		}
		let loginUser = this.$route.query.loginUser ?? userid
		const referrer = document.referrer
		console.log('referrer: ', referrer)
		if (
			// referrer.includes('182.245.124.110') ||
			// referrer.includes('116.249.27.37') ||
			// referrer.includes('yhgl.yciccloud.com') ||
			adminuser === 'ylzx'
		) {
			if (loginUser) {
				Cookies.remove('username')
				Cookies.remove('password')
				Cookies.remove('rememberMe')
				this.loginForm.username = loginUser
				this.$store
					.dispatch('LoginByUser', this.loginForm)
					.then(() => {
						url = url ?? getHomeUrl() ?? '/'
						console.log('url: ', url)
						this.$router.push({ path: url }).catch(() => { })
					})
					.catch(() => {
						this.loading = false
						if (this.captchaEnabled) {
							this.getCode()
						}
					})
			}
		}

		let now_href = window.location.href
		console.log(now_href)
		// if(now_href.indexOf("zhyhpt.yciccloud.com")!=-1 || now_href.indexOf("122.1.14")!=-1){
		//   this.isTest="测试版v2.0";
		// }
	},
	methods: {
		getCode() {
			getCodeImg().then((res) => {
				this.captchaEnabled = res.captchaEnabled === undefined ? true : res.captchaEnabled
				if (this.captchaEnabled) {
					let img = AesDecrypt(res.img)
					this.codeUrl = 'data:image/gif;base64,' + img
					this.loginForm.uuid = res.uuid
				}
			})
		},
		getCookie() {
			const username = Cookies.get('username')
			const password = Cookies.get('password')
			const rememberMe = Cookies.get('rememberMe')
			this.loginForm = {
				username: username === undefined ? this.loginForm.username : username,
				password: password === undefined ? this.loginForm.password : decrypt(password),
				rememberMe: rememberMe === undefined ? false : Boolean(rememberMe),
			}
		},
		handleLogin() {
			this.$refs.loginForm.validate((valid) => {
				if (valid) {
					this.loading = true
					if (this.loginForm.rememberMe) {
						Cookies.set('username', this.loginForm.username, { expires: 30 })
						Cookies.set('password', encrypt(this.loginForm.password), { expires: 30 })
						Cookies.set('rememberMe', this.loginForm.rememberMe, { expires: 30 })
					} else {
						Cookies.remove('username')
						Cookies.remove('password')
						Cookies.remove('rememberMe')
					}
					this.$store
						.dispatch('Login', this.loginForm)
						.then(async () => {
							// const home_url = await getHomeUrl()
							// this.$router.push({ path: home_url ?? this.redirect ?? '/' }).catch(() => { })
							this.handleLoginSuccess();
							// 登录成功后 预请求获取 - 驾驶舱 地图 3d范围 路线 geoJson 数据 存储到 本地 ， 退出登录后 清除
							this.preRequestData()
						})
						.catch(() => {
							if (this.captchaEnabled) {
								this.getCode()
							}
						}).finally(() => {
							setTimeout(() => {
								this.loading = false
							}, 1000)
						})
				}
			})
		},
		getParam(str, subStr) {
			const index = str.indexOf(subStr)
			if (index !== -1) {
				const index2 = str.indexOf('&')
				if (index2 !== -1) {
					return str.substring(index + subStr.length, index2)
				}
				return str.substring(index + subStr.length)
			}
			return ''
		},
		// 切换登录方式
		switchLoginType(type) {
			this.loginType = type
			this.$refs.loginForm?.clearValidate()
		},
		// 登录成功处理
		async handleLoginSuccess() {
			const home_url = await getHomeUrl()
			let path = home_url || this.redirect || '/'
			console.log('path：', path)
			this.$router.push({ path }).catch(() => { })
		},
		// 忘记密码
		handleForgetPwd() {
			this.showForgetPassword = true
		},
		async getLoginLog() {
			let res = await getLoginLog()
			if (res.code == 200) {
				localStorage.setItem('logoInfo', JSON.stringify(res.data))
				this.logoInfo = res.data
			}
		},
		// 登录成功后 预请求获取 - 驾驶舱 地图 3d范围 路线 geoJson 数据 存储到 本地 ， 退出登录后 清除
		async preRequestData() {
			let indexedDB = new IndexedDBStorage('mapData', 'indexedDB');
			await indexedDB.open();
			// 删除旧数据 - 路线 存在没点退出登录 未清除数据
			removeIndexedDB('mapData')
			getMergeShape({ tolerance: 0.0005 }).then(res => {
				if(res.code === 200) {
					 try {
						// 存储到 indexedDB
						const newItem = { id: '1', name: 'merge', value: res.data };
						indexedDB.add(newItem).then(() => {
						}).catch((error) => {
							console.error('存储数据到IndexedDB失败:', error);
						});
					} catch (err) {
						console.log(err);
					}
				}
			});
			let lineId = process.env.VUE_APP_LINE_ID
			// 合并部门数据 tolerance: 0.0001能压缩到1M左右，数值越大压缩的比例也越大
			// getVectorTile({ id: lineId, tolerance: 0.005 })
			// getDataCockpitGeoJson({ id: lineId, tolerance: 0.005 })
			getVectorTile({ id: lineId, tolerance: 0.005 }).then(async (res) => {
				if(res) {
					 try {
						// 存储到 indexedDB
						const newItem = { id: '2', name: 'geoJson', value: res };
						indexedDB.add(newItem).then(() => {
						}).catch((error) => {
							console.error('存储数据到IndexedDB失败:', error);
						});
					} catch (err) {
						console.log(err);
					}
				}
			}).finally(()=>{
				setTimeout(()=>{
					indexedDB.close();
    			indexedDB = null;
				},1500)
			});
		},
	},
	async mounted() {
		await this.getLoginLog()
		this.$nextTick(() => {
			/* 背景图片修改 */
			const Element = document.querySelector('.login');
			Element.style.backgroundImage = `url("${this.logoInfo.backgroundImage}")`;
			if (this.logoInfo?.homePath === '/xizangindex') {
				document.title = '西藏高等级公路风险点位监测预警系统'
			} else {
				document.title = '智慧养护管理平台'
			}
		});
	},
}
</script>

<style rel="stylesheet/scss" lang="scss">
.login {
	display: flex;
	justify-content: center;
	align-items: center;
	min-width: 500px;
	height: 100%;
	// background-image: url('../assets/login/bg.png');
	background-repeat: no-repeat;
	background-position: center;
	background-size: 100% 100%;

	position: relative;

	.logo {
		width: 100%;
		height: 80px;
		position: absolute;
		top: 0;
		left: 0;
		right: 0;
		background: linear-gradient(90deg, rgba(255, 255, 255, 0.6) 0%, rgba(255, 255, 255, 0) 90%);
		padding: 0 30px;

		display: flex;
		align-items: center;

		img {
			width: 56px;
			height: 56px;
		}

		.logo-name {
			font-family: Microsoft YaHei, Microsoft YaHei;
			font-weight: 700;
			font-size: 38px;
			color: #333333;
			letter-spacing: 1px;
			text-align: left;
			font-style: normal;
			margin-left: 10px;
		}

		.warming {
			position: absolute;
			right: 0;
			margin-right: 30px;
			font-size: 20px;
			color: #ff0000;
			font-weight: 700;
		}
	}
}

.title {
	margin: 0px auto 30px auto;
	text-align: center;
	color: #707070;
}

.login-form {
	border-radius: 16px;
	width: 400px;
	min-height: 420px;
	background: rgba(255, 255, 255, 0.6);
	border: 2px solid #ffffff;
	display: flex;
	flex-direction: column;

	.login-img {
		width: 40%;
		height: 40%;
		margin-left: 30%;
		margin-top: 10px;
		flex-shrink: 0;
	}

	.divider {
		width: 100%;
		height: 1px;
		margin: 10px 0 20px 0;
		background-color: #ffffff;
		flex-shrink: 0;
	}

	.login-type-switch {
		flex-shrink: 0;
	}

	.el-form-item {
		padding: 0px 25px;
	}

	.el-checkbox {
		padding-left: 50%;
		transform: translate(-15%);
	}

	.el-input {
		height: 38px;

		input {
			height: 38px;
		}
	}

	.input-icon {
		height: 39px;
		width: 14px;
		margin-left: 2px;
	}
}

.login-tip {
	font-size: 13px;
	text-align: center;
	color: #bfbfbf;
}

.login-code {
	width: 33%;
	height: 38px;
	float: right;

	img {
		cursor: pointer;
		vertical-align: middle;
	}
}

.download-app {
	height: 25px;
	border-radius: 5px;
	position: fixed;
	bottom: 35px;
	text-align: center;
	color: #fff;
	letter-spacing: 1px;
	display: flex;
	align-items: center;
	justify-content: center;
	flex-wrap: nowrap;

	.download-app-btn {
		border: 0;
		background-color: rgba(83, 163, 242, 0.5);
		color: white;
		font-size: 12px;
	}
}

.download-app-title {
	text-align: center;
	color: #0a720d;
}

.download-app-qrcode {
	img {
		width: 150px;
		height: 150px;
	}
}

.el-login-footer {
	background: rgba(255, 255, 255, 0.1);
	height: 32px;
	line-height: 32px;
	position: fixed;
	bottom: 0;
	width: 100%;
	min-width: 900px;
	text-align: center;
	color: #fff;
	font-family: Arial;
	font-size: 12px;
	letter-spacing: 1px;

	display: flex;
	align-items: center;
	justify-content: center;
	flex-wrap: nowrap;
	overflow-x: hidden;

	img {
		margin: 0 6px;
	}
}

.login-code-img {
	height: 38px;
}

.login-type-switch {
	margin: 0 25px 20px;
	text-align: center;
	background: rgba(255, 255, 255, 0.6);
	border-radius: 20px;
	padding: 8px 0;

	span {
		display: inline-block;
		padding: 0 20px;
		cursor: pointer;
		color: #606266;
		font-size: 14px;

		&.active {
			color: #409eff;
			font-weight: bold;
		}

		&:first-child {
			border-right: 1px solid #dcdfe6;
		}

		&:hover {
			color: #409eff;
		}
	}
}

.forget-pwd {
	margin-left: auto;
	padding: 0;
	color: #409eff;

	&:hover {
		color: #66b1ff;
	}
}
</style>
