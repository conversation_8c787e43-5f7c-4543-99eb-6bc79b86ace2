<template>
  <div class="inspection-index" v-loading="loading" element-loading-background="rgba(0, 0, 0, 0.4)">
    <div class="list" v-for="(item, index) in list" :key="index" :style="{ height: isBig ? '95px' : '35px' }">
      <img class="img" :src="item.icon" :alt="item.name" />
      <span class="name" :style="{ fontSize: isBig ? '0.45vw' : '0.7vw' }">{{ item.name }}</span>
      <div class="list-n">
        <span>{{ item.number }} <small>{{ item.unit }}</small></span>
      </div>
    </div>
  </div>
</template>

<script>
import { isBigScreen } from '../../util/utils';
import {
  getPatrolYearDetail,
} from "@/api/cockpit/index";

export default {
  created() {
    this.getSelectAssetCheckData();
    window.$Bus.$on("onChangeYear", (y) => {
      this.year = y;
      this.getSelectAssetCheckData();
    });
  },
  beforeDestroy() {
    window.$Bus.$off("onChangeYear");
  },
  methods: {
    async getSelectAssetCheckData() {
      this.loading = true;
      await getPatrolYearDetail({ year: this.year }).then(res => {
        if (res.code === 200 && res.data) {
          let data = res.data;
          // 此注释用于说明将米转换为万公里的逻辑 data.roadbedMileage 单位 是米？
          // 1 万公里 = 10000 * 1000 米，所以将米数除以 10000000 即可得到万公里数
          this.list[0].number = data.roadbedMileage ? (((data.roadbedMileage - 0) / 1000) / 10000).toFixed(2) : 0;
          this.list[1].number = data.bridgeCount;
          this.list[2].number = data.tunnelCount;
          this.list[3].number = data.culvertCount;
        } else {
          this.list[0].number = 0;
          this.list[1].number = 0;
          this.list[2].number = 0;
          this.list[3].number = 0;
        }
      }).finally(() => {
        this.loading = false;
      });
    },
  },
  data() {
    return {
      isBig: isBigScreen(),
      year: new Date().getFullYear(),
      loading: false,
      list: [
        {
          icon: require('@/assets/cockpit/way.png'),
          name: '道路日常检查',
          number: 0, // 8623,
          unit: '万公里',
        },
        {
          icon: require('@/assets/cockpit/bridge-patrol.png'),
          name: '桥梁经常检查',
          number: 0 , // 13980,
          unit: '座',
        },
        {
          icon: require('@/assets/cockpit/tunnel-i.png'),
          name: '隧道经常检查',
          number: 0 ,// 1379,
          unit: '座',
        },
        {
          icon: require('@/assets/cockpit/culvert.png'),
          name: '涵洞经常检查',
          number: 0, // 1379,
          unit: '个',
        },
      ],
    }
  },
  mounted() {
  }
}
</script>

<style lang="scss" scoped>
@import "@/assets/styles/utils.scss";

.inspection-index {
  width: 100%;
  padding: vwpx(14px) vwpx(16px);
  height: 100%;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;

  .list {
    width: 100%;
    display: flex;
    align-items: center;
    flex: 1;
    background: rgba(23, 116, 255, 0.1);
    padding: 0 vwpx(15px);
    margin: vwpx(10px) 0;
    border-radius: vwpx(5px);

    .img {
      width: vwpx(52px);
      height: vwpx(52px);
    }

    .name {
      font-family: Microsoft YaHei UI, Source Han Sans;
      color: #FFFFFF;
      text-align: center;
      margin-left: vwpx(20px);
      letter-spacing: 0.1vw;
    }

    .list-n {
      margin-left: auto;

      span:first-child {
        font-family: Microsoft YaHei UI, Microsoft YaHei UI;
        font-weight: 700;
        font-size: vwpx(36px);
        color: #42ABFF;
        text-align: right;
        font-style: normal;
        text-transform: none;

        small {
          font-family: Microsoft YaHei UI, Source Han Sans;
          font-weight: 400;
          font-size: vwpx(24px);
          color: rgba(255, 255, 255, 0.8);
          text-shadow: 0px 0px 10px rgba(27, 126, 242, 0.8);
          text-align: right;
        }
      }
    }
  }
}
</style>