// 行政识别数据
const baseInfoData = [
  {
    label: '路线编码',
    prop: 'routeCode',
    type: 'input',
    disabled: true,
    span:12,
  },

  {
    label:'路线名称',
    prop: 'routeName',
    type: 'input',
    span:12,
    disabled: true,
  },

//   direction	string
// 调查方向(字典：sys_route_direction)

// endStake	number
// minimum: 0
// exclusiveMinimum: false
// 终点桩号(格式为纯数字，保留三位小数)

// id	string
// 主键

// maintenanceSectionId	string
// 养护路段Id

// managementMaintenanceId	string
// 管理处id

// pavType	string
// 路面类型

// routeId	string
// 路线id

// specificDirection	string
// 具体方向

// startStake	number
// minimum: 0
// exclusiveMinimum: false
// 起点桩号(格式为纯数字，保留三位小数)
  {
    label: '养护路段',
    prop: 'maintenanceSectionId',
    placeholder: '请选择养护路段',
    type: 'select',
    rules: [{ required: true, message: '请选择养护路段', trigger: 'change' }],
    options: [],
    api: 'maintenanceSectionList',
    optionLabel: 'maintenanceSectionName',
    optionValue: 'maintenanceSectionId',
    disabledFieds: 'managementMaintenanceBranchId'
  },
  {
    label: '路线编码',
    prop: 'routeCode',
    placeholder: '请选择路线编码',
    type: 'select',
    rules: [{ required: true, message: '请选择路线编码', trigger: 'change' }],
    options: [],
    disabled: true,
    api: 'routeListAll',
    optionLabel: 'routeCode',
    optionValue: 'routeCode',
    disabledFieds: 'maintenanceSectionId'
  },
  {
    label: '路线名称',
    prop: 'routeName',
    placeholder: '请输入路线名称',
    type: 'select',
    api: 'routeListAll',
    optionLabel: 'routeName',
    optionValue: 'routeName',
    disabledFieds: '',
    rules: [{ required: true, message: '路线名称不能为空，选择路线编码自动带出。', trigger: 'change' }],
  },
  {
    label: '路线技术等级',
    prop: 'routeLevel',
    placeholder: '请选择路线技术等级',
    // type: 'dictSelect',
    multiple: true,
    dict: 'sys_route_grade',
    disabledFieds:'',
    type: 'multiDictSelect',
    rules: [{required: true, message: '路线技术等级不能为空，选择养护路段自动带出。', trigger: 'change'}],
  },
 
  {
    label:"调查方向",
    prop:"direction",
    type:"select",
    span:12,
    rules: [{ required: true, message: '请选择调查方向', trigger: 'change' }],
    dict: 'sys_route_direction',
  },

  {
    label:"具体方向",
    prop:"specificDirection",
    type:"input",
    rules: [{ required: true, message: '请输入具体方向', trigger: 'change' }],
    span:12,
  },
  {
    label:"路面类型",
    prop:"pavType",
    type:"select",
    span:12,
    rules: [{ required: true, message: '请选择路面类型', trigger: 'change' }],
    dict:'sys_surface_type',
  },
  {
    label:"评定长度(m)",
    prop:"ratingLength",
    type:"input",
    span:12,
    disabled:true,
    placeholder:'输入【起点桩号】和【终点桩号】自动计算',
    rules: [{required: true, message: '评定长度为空', trigger: 'change'}],
  },
  {
    label:"起点桩号",
    prop:"startStake",
    type:"pileInput",
    span:12,
    rules: [{required: true, message: '起点桩号为空', trigger: 'change'}],
  },
  {
    label:"终点桩号",
    prop:"endStake",
    type:"pileInput",
    span:12,
    rules: [{required: true, message: '终点桩号为空', trigger: 'change'}],
  },
  {
    label: '起点经纬度',
    propLon: 'longitudeStart',
    propLat: 'latitudeStart',
    placeholder: '请输入起点经纬度',
    type: 'coordinate',
    prepend: 'lonlat',
  },
  {
    label: '终点经纬度',
    propLon: 'longitudeEnd',
    propLat: 'latitudeEnd',
    placeholder: '请输入终点经纬度',
    type: 'coordinate',
    prepend: 'lonlat',
  },
]





export default {
  baseInfoData,
  
}
