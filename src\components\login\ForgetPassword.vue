<template>
  <div class="forget-password">
    <img src="@/assets/login/logo-1.png" alt="" class="login-img"/>
    <div class="divider"></div>

    <!-- 与登录页面相同的样式 -->
    <div class="login-type-switch">
      <span class="active">重置密码</span>
    </div>

    <el-form ref="resetForm" :model="resetForm" :rules="resetRules">
      <!-- 用于欺骗 Chrome 浏览器的隐藏输入框 -->
      <el-input type="text" style="display: none" autocomplete="off"/>
      <el-input type="password" style="display: none" autocomplete="off"/>

      <el-form-item prop="phone">
        <el-input
          v-model="resetForm.phone"
          type="text"
          autocomplete="new-password"
          placeholder="请输入手机号"
          prefix-icon="el-icon-phone"
        >
        </el-input>
      </el-form-item>

      <el-form-item prop="smsCode">
        <el-input
          v-model="resetForm.smsCode"
          autocomplete="new-password"
          placeholder="请输入短信验证码"
          style="width: 63%"
        >
          <img slot="prefix" src="@/assets/login/code.png" alt="" style="width: 15px;height: 15px;margin-top: 12px;"/>
        </el-input>
        <el-button
          class="sms-code-btn"
          :disabled="smsCodeTimer > 0"
          @click="sendSmsCode">
          {{ smsCodeTimer > 0 ? `${smsCodeTimer}秒后重新获取` : '获取验证码' }}
        </el-button>
      </el-form-item>

      <el-form-item prop="newPassword">
        <el-input
          v-model="resetForm.newPassword"
          type="password"
          autocomplete="new-password"
          placeholder="请输入新密码"
          show-password
        >
          <img slot="prefix" src="@/assets/login/pwd.png" alt="" style="width: 15px;height: 15px;margin-top: 12px;"/>
        </el-input>
      </el-form-item>

      <el-form-item prop="confirmPassword">
        <el-input
          v-model="resetForm.confirmPassword"
          type="password"
          autocomplete="new-password"
          placeholder="请确认新密码"
          show-password
        >
          <img slot="prefix" src="@/assets/login/pwd.png" alt="" style="width: 15px;height: 15px;margin-top: 12px;"/>
        </el-input>
      </el-form-item>
      <div class="rules-item">密码长度为6-20位，需包含大小写字母和数字</div>
      <el-form-item style="width:100%;">
        <el-button
          :loading="loading"
          size="medium"
          type="primary"
          style="width:100%;opacity: 0.7;"
          @click.native.prevent="handleReset"
        >
          <span v-if="!loading">修改并登录</span>
          <span v-else>修改中...</span>
        </el-button>
      </el-form-item>

      <div style="margin:0 0 25px 0; display: flex; align-items: center; padding: 0 25px;">
        <el-button type="text" style="padding: 0; color: #337ecc;" @click="$emit('back')">返回登录</el-button>
      </div>
    </el-form>

		<!-- 添加账号选择对话框 -->
		<el-dialog
			title="请选择要修改的账号"
			:visible.sync="dialogVisible"
			width="400px"
			:show-close="false"
		>
			<el-radio-group v-model="selectedAccount" class="account-list">
				<el-radio
					v-for="account in accountList"
					:key="account.userName"
					:label="account.userName"
					class="account-item"
				>
					{{ account.userName }} - {{ account.nickName }}
				</el-radio>
			</el-radio-group>
			<span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="handleAccountSelect" :disabled="!selectedAccount">确 定</el-button>
      </span>
		</el-dialog>
  </div>
</template>

<script>
import { sendCode } from '@/api/login'

export default {
  name: 'ForgetPassword',
  data() {
    // 密码验证规则
    const validatePassword = (rule, value, callback) => {
      if (value.length < 6 || value.length > 20) {
        callback(new Error('密码长度必须在6-20位之间'));
      } else if (!/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)[a-zA-Z\d]{6,20}$/.test(value)) {
        callback(new Error('密码必须包含大小写字母和数字'));
      } else {
        callback();
      }
    };
    // 确认密码验证
    const validateConfirmPassword = (rule, value, callback) => {
      if (value !== this.resetForm.newPassword) {
        callback(new Error('两次输入的密码不一致'));
      } else {
        callback();
      }
    };
    // 手机号验证
    const validatePhone = (rule, value, callback) => {
      if (!value) {
        callback(new Error('请输入手机号'));
      } else if (!/^1[3-9]\d{9}$/.test(value)) {
        callback(new Error('请输入正确的手机号'));
      } else {
        callback();
      }
    };

    return {
      loading: false,
      smsCodeTimer: 0,
      timer: null,
      resetForm: {
        phone: '',
        newPassword: '',
        confirmPassword: '',
        smsCode: ''
      },
      resetRules: {
        phone: [
          { required: true, trigger: "blur", validator: validatePhone }
        ],
        newPassword: [
          { required: true, trigger: "blur", message: "请输入新密码" },
          { validator: validatePassword, trigger: "blur" }
        ],
        confirmPassword: [
          { required: true, trigger: "blur", message: "请确认新密码" },
          { validator: validateConfirmPassword, trigger: "blur" }
        ],
        smsCode: [
          { required: true, trigger: "blur", message: "请输入短信验证码" }
        ]
      },
      dialogVisible: false,
      accountList: [],
      selectedAccount: '',
    }
  },
  methods: {
    // 发送验证码
    sendSmsCode() {
      this.$refs.resetForm.validateField('phone', (errorMsg) => {
        if (!errorMsg) {
          sendCode(this.resetForm.phone).then(() => {
            this.startSmsCodeTimer();
            this.$message.success('验证码已发送');
          }).catch(() => {
            // 发送失败的处理
          });
        }
      });
    },

    // 开始倒计时
    startSmsCodeTimer() {
      this.smsCodeTimer = 60;
      if (this.timer) {
        clearInterval(this.timer);
      }
      this.timer = setInterval(() => {
        this.smsCodeTimer--;
        if (this.smsCodeTimer <= 0) {
          clearInterval(this.timer);
          this.timer = null;
        }
      }, 1000);
    },

    // 提交重置
    handleReset() {
      this.$refs.resetForm.validate(valid => {
        if (valid) {
          this.loading = true;
          // 先调用手机号登录接口检查是否有多个账号
          this.$store.dispatch('LoginByPhone', {
            phone: this.resetForm.phone,
            smsCode: this.resetForm.smsCode
          }).then((res) => {
            if (Array.isArray(res) && res.length > 0) {
              // 有多个账号，显示选择对话框
              this.accountList = res;
              this.selectedAccount = '';
              this.dialogVisible = true;
              this.loading = false;
            } else {
              // 单个账号，直接重置密码
              this.resetPassword();
            }
          }).catch(() => {
            this.loading = false;
          });
        }
      });
    },

    // 处理账号选择
    handleAccountSelect() {
      if (!this.selectedAccount) return;
      this.loading = true;
      this.resetPassword(this.selectedAccount);
    },

    // 重置密码
    async resetPassword(userName) {
      const params = {
        phonenumber: this.resetForm.phone,
        password: this.resetForm.newPassword,
        userName: userName
      };
      try {
        if (userName) {
          // 选择账号走用户名登录
          this.loading = true;
          await this.$store.dispatch('LoginByUser', { username: userName });
          this.dialogVisible = false;
        }
        // 执行重置密码操作
        this.loading = true;
        await this.$store.dispatch('ResetPassword', params);
        this.$message.success('密码重置成功');
        this.$emit('reset-success');
      } catch (error) {
        // 捕获异常并处理
        this.loading = false;
        console.error('操作失败:', error);
      }
    }
  },
  beforeDestroy() {
    if (this.timer) {
      clearInterval(this.timer);
    }
  }
}
</script>

<style lang="scss" scoped>
.forget-password {
  width: 100%;
  display: flex;
  flex-direction: column;

  .login-img {
    width: 40%;
    margin: 10px auto;
  }

  .divider {
    width: 100%;
    height: 1px;
    margin: 10px 0;
    background-color: #ffffff;
  }

  .login-type-switch {
    margin: 0 25px 20px;
    text-align: center;
    background: rgba(255, 255, 255, 0.6);
    border-radius: 20px;
    padding: 8px 0;

    span {
      display: inline-block;
      padding: 0 20px;
      color: #337ecc;
      font-size: 14px;
      font-weight: bold;
    }
  }

  .el-form {
    flex: 1;

    .el-form-item {
      padding: 0 25px;
      margin-bottom: 18px;

      &:last-of-type {
        margin-bottom: 10px;
      }
    }

    // 调整手机图标的样式
    :deep(.el-icon-phone) {
      font-size: 16px;
      margin-left: 2px;
    }
  }

  .sms-code-btn {
    width: 35%;
    height: 38px;
    margin-left: 2%;
    padding: 0;
    font-size: 12px;
  }
}

:deep(.el-form-item__error) {
  padding-top: 2px;
}

.account-list {
  display: flex;
  flex-direction: column;
  width: 100%;

  .account-item {
    margin: 10px 0;
    padding: 10px;
    border-radius: 4px;

    &:hover {
      background-color: #f5f7fa;
    }
  }
}

.el-dialog__body {
  padding: 20px;
}

.rules-item {
  line-height: 1.8;
  padding: 0 0 5px 26px;
  font-size: x-small;
  margin-top: -5px;
  color: #5f5d5d;
}
</style>
