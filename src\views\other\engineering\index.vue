<template>
  <div class="app-container">
    <el-row :gutter="20">

      <!--筛选区开始-->
      <el-col :span="24" :xs="24">
        <el-row>
          <el-col :span="24" >
            <el-form :model="queryParams" ref="queryForm" size="small" :inline="true"  label-width="68px">

                          <el-form-item label="" prop="documentReceived">
                            <el-input
                                v-model="queryParams.documentReceived"
                                placeholder="请输入来文名称"
                                clearable
                                prefix-icon="el-icon-user"
                                style="width: 240px"
                                @keyup.enter.native="handleQuery"
                            />

                          </el-form-item>
                          <el-form-item>
                            <selectTree
                              :key="'domainId'"
                              v-model="queryParams.managementUnitId"
                              :deptType="100"
                              :deptTypeList="[1, 3, 4, 10]" clearable
                              filterable
                              placeholder="管养单位"
                              style="width: 240px"
                            />
                          </el-form-item>
                          <el-form-item>
                            <RoadSection v-model="queryParams.maintenanceSectionId" :deptId="queryParams.managementUnitId" placeholder="路段"  style="width: 240px"/>
                          </el-form-item>
                          <el-form-item label="" prop="projectStatus">
                            <el-select v-model="queryParams.projectStatus" placeholder="请选择项目状态" clearable>
                              <el-option
                                  v-for="dict in dict.type.engineering_project_status"
                                  :key="dict.value"
                                  :label="dict.label"
                                  :value="dict.value"
                              />
                            </el-select>
                          </el-form-item>
                          <el-form-item label="" prop="compensationSituation">
                            <el-select v-model="queryParams.compensationSituation" placeholder="请选择赔付情况" clearable>
                              <el-option
                                  v-for="dict in dict.type.engineering_compensation_situation"
                                  :key="dict.value"
                                  :label="dict.label"
                                  :value="dict.value"
                              />
                            </el-select>
                          </el-form-item>
              <el-form-item>
                <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
                <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
                <!-- <el-button v-show="!showSearch" @click="showSearch=true" icon="el-icon-arrow-down" circle></el-button>
                <el-button  v-show="showSearch" @click="showSearch=false"  icon="el-icon-arrow-up" circle></el-button> -->
              </el-form-item>
            </el-form>
            <!--默认折叠-->
          </el-col>

          <!--默认折叠 ,此处仅作为示例-->
          <!-- <el-col :span="24" >
            <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">

              <el-form-item label="" prop="status">
                <el-select
                    v-model="queryParams.status"
                    placeholder="示例状态"
                    clearable
                    style="width: 240px"
                >
                  <el-option
                      v-for="dict in dictType"
                      :key="dict.value"
                      :label="dict.label"
                      :value="dict.value"
                  />
                </el-select>
              </el-form-item>
              <el-form-item label="" prop="status">
                <el-select
                    v-model="queryParams.status"
                    placeholder="示例状态"
                    clearable
                    style="width: 240px"
                >
                  <el-option
                      v-for="dict in dictType"
                      :key="dict.value"
                      :label="dict.label"
                      :value="dict.value"
                  />
                </el-select>
              </el-form-item>

            </el-form>
          </el-col> -->
        </el-row>
        <!--筛选区结束-->


        <!--操作按钮区开始-->
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button
                type="primary"
                icon="el-icon-plus"
                size="mini"
                @click="handleAdd"
                v-hasPermi="['repote:engineering:add']"
            >新增</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button
                type="success"
                icon="el-icon-edit"
                size="mini"
                :disabled="single"
                @click="handleUpdate"
                v-hasPermi="['repote:engineering:edit']"
            >修改</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button
                type="danger"
                icon="el-icon-delete"
                size="mini"
                :disabled="multiple"
                @click="handleDelete"
                v-hasPermi="['repote:engineering:remove']"
            >删除</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button
                type="info"
                plain
                icon="el-icon-upload2"
                size="mini"
                @click="handleImport"
                v-hasPermi="['repote:engineering:export']"
            >导入</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button
                type="warning"
                icon="el-icon-download"
                size="mini"
                @click="handleExport"
                v-hasPermi="['system:user:export']"
            >导出</el-button>
          </el-col>
          <right-toolbar :showSearch.sync="showSearch" @queryTable="findUserDeptRoadList" :columns="columns"></right-toolbar>
        </el-row>
        <!--操作按钮区结束-->

        <!--数据表格开始-->
        <div class="tableDiv">
          <el-table
              v-adjust-table
              ref="table"
              size="mini"
              :height="showSearch ? 'calc(100vh - 320px)' : 'calc(100vh - 260px)'"
              style="width: 100%"
              v-loading="loading"
              border
              :data="engineeringList"
              @selection-change="handleSelectionChange"
              :row-style="rowStyle"
              @row-click="handleRowClick"
          >
            <el-table-column type="selection" width="50" align="center" />
            <el-table-column fixed label="序号" type="index" width="50" :index="indexMethod">
            </el-table-column>
                    <el-table-column label="来文名称" align="center" prop="documentReceived"width="300" />
                    <el-table-column label="项目状态" align="center" prop="projectStatus">
                      <template slot-scope="scope">
                            <dict-tag :options="dict.type.engineering_project_status" :value="scope.row.projectStatus"/>
                      </template>
                    </el-table-column>
                    <el-table-column label="赔付情况" align="center" prop="compensationSituation">
                      <template slot-scope="scope">
                            <dict-tag :options="dict.type.engineering_compensation_situation" :value="scope.row.compensationSituation"/>
                      </template>
                    </el-table-column>
                    <el-table-column label="管养单位" align="center" prop="managementUnit" width="180"/>
                    <el-table-column label="养护路段" align="center" prop="maintenanceSection" width="180"/>
                    <el-table-column label="来文时间" align="center" prop="submissionTime" width="180">
                      <template slot-scope="scope">
                        <span>{{ parseTime(scope.row.submissionTime, '{y}-{m}-{d}') }}</span>
                      </template>
                    </el-table-column>
                    <el-table-column label="来文单位" align="center" prop="reportingUnit" width="180" />

                    <el-table-column label="起点桩号" align="center" prop="startPoint" >
                      <template slot-scope="scope">
                        {{ formatPile(scope.row.startPoint) }}
                      </template>
                    </el-table-column>
                    <el-table-column label="终点桩号" align="center" prop="endPoint" >
                      <template slot-scope="scope">
                        {{ formatPile(scope.row.endPoint) }}
                      </template>
                    </el-table-column>
                    <el-table-column label="申请内容" align="center" prop="implementationContent" width="300"/>
                    <el-table-column label="拟开工日期" align="center" prop="plannedStartDate" width="180">
                      <template slot-scope="scope">
                        <span>{{ parseTime(scope.row.plannedStartDate, '{y}-{m}-{d}') }}</span>
                      </template>
                    </el-table-column>
                    <el-table-column label="拟竣工日期" align="center" prop="plannedCompletionDate" width="180">
                      <template slot-scope="scope">
                        <span>{{ parseTime(scope.row.plannedCompletionDate, '{y}-{m}-{d}') }}</span>
                      </template>
                    </el-table-column>
                    <el-table-column label="是否交通管制" align="center" prop="isTrafficControl" width="150">
                      <template slot-scope="scope">
                            <dict-tag :options="dict.type.engineering_is_regulated" :value="scope.row.isTrafficControl"/>
                      </template>
                    </el-table-column>
                    <el-table-column label="联系人" align="center" prop="contactPerson" />
                    <el-table-column label="电话" align="center" prop="phoneNumber" width="180"/>
                    <el-table-column label="备注" align="center" prop="remarks" width="300" />
                    <el-table-column label="复函文件编码" align="center" prop="replyDocumentCode" width="180"/>
                    <el-table-column label="复函时间" align="center" prop="replyTime" width="180">
                      <template slot-scope="scope">
                        <span>{{ parseTime(scope.row.replyTime, '{y}-{m}-{d}') }}</span>
                      </template>
                    </el-table-column>
                    <el-table-column label="复函意见" align="center" prop="replyOpinions" />
                    <el-table-column label="涉路施工协议编号" align="center" prop="agreementNumber" width="230"/>
                    <el-table-column label="路产协议编号" align="center" prop="roadPropertyAgreementNumber" width="230"/>
                    <el-table-column label="协议金额" align="center" prop="agreementAmount" />
                    <el-table-column label="方向" align="center" prop="direction" >
                      <template slot-scope="scope">
                        <dict-tag :options="dict.type.engineering_direction" :value="scope.row.direction"/>
                      </template>
                    </el-table-column>
                    <el-table-column label="建设单位" align="center" prop="constructionUnit" />
                    <el-table-column label="设计单位" align="center" prop="designUnit" />
                    <el-table-column label="施工单位" align="center" prop="constructionCompany" />
                    <el-table-column label="监理单位" align="center" prop="supervisionUnit" />
            <el-table-column
                label="操作"
                fixed="right"
                align="center"
                width="230"
                class-name="small-padding fixed-width"
            >
              <template slot-scope="scope" v-if="scope.row.userId !== 1">
                <el-button
                    size="mini"
                    type="text"
                    icon="el-icon-edit"
                    @click="handleUpdate(scope.row)"
                    v-hasPermi="['repote:engineering:edit']"
                >修改</el-button>
                <el-button
                    size="mini"
                    type="text"
                    icon="el-icon-delete"
                    @click="handleDelete(scope.row)"
                    v-hasPermi="['repote:engineering:remove']"
                >删除</el-button>
                <el-button
                  size="mini"
                  type="text"
                  icon="el-icon-upload2"
                  @click="handleUploadClick(scope.row)">
                  上传附件
                </el-button>
              </template>
            </el-table-column>
          </el-table>

          <pagination
              v-show="total>0"
              :total="total"
              :page.sync="queryParams.pageNum"
              :limit.sync="queryParams.pageSize"
              @pagination="findUserDeptRoadList"
          />
        </div>
        <!--数据表格结束-->
      </el-col>
    </el-row>

    <!-- 添加或修改涉路工程对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="900px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">

                      <el-col :span="12">
                        <el-form-item label="来文名称" prop="documentReceived">
                          <el-input v-model="form.documentReceived" placeholder="请输入来文名称" />
                        </el-form-item>
                      </el-col>


                      <el-col :span="12">
                        <el-form-item label="项目状态" prop="projectStatus" style="width: 100%">
                          <el-select v-model="form.projectStatus" placeholder="请选择项目状态" style="width: 100%">
                            <el-option
                                v-for="dict in dict.type.engineering_project_status"
                                :key="dict.value"
                                :label="dict.label"
                                :value="dict.value"
                            ></el-option>
                          </el-select>
                        </el-form-item>
                      </el-col>


                      <el-col :span="12">
                        <el-form-item label="赔付情况" prop="compensationSituation" style="width: 100%">
                          <el-select v-model="form.compensationSituation" placeholder="请选择赔付情况" style="width: 100%">
                            <el-option
                                v-for="dict in dict.type.engineering_compensation_situation"
                                :key="dict.value"
                                :label="dict.label"
                                :value="dict.value"
                            ></el-option>
                          </el-select>
                        </el-form-item>
                      </el-col>


                      <el-col :span="12">
                        <el-form-item label="管养单位" prop="managementUnit">
                          <el-input v-model="form.managementUnit" placeholder="请输入管养单位" />
                        </el-form-item>
                      </el-col>


                      <el-col :span="12">
                        <el-form-item label="养护路段" prop="maintenanceSection">
                          <el-input v-model="form.maintenanceSection" placeholder="请输入养护路段" />
                        </el-form-item>
                      </el-col>


                      <el-col :span="12">
                        <el-form-item label="路线编码" prop="roadCode" style="width: 100%">
                          <el-input v-model="form.roadCode" placeholder="请输入路线编码" style="width: 100%" />
                        </el-form-item>
                      </el-col>


                      <el-col :span="12">
                        <el-form-item label="来文时间" prop="submissionTime" style="width: 100%">
                          <el-date-picker clearable
                                          v-model="form.submissionTime"
                                          type="date"
                                          value-format="yyyy-MM-dd"
                                          style="width: 100%"
                                          placeholder="请选择来文时间">
                          </el-date-picker>
                        </el-form-item>
                      </el-col>


                      <el-col :span="12">
                        <el-form-item label="来文单位" prop="reportingUnit">
                          <el-input v-model="form.reportingUnit" placeholder="请输入来文单位" />
                        </el-form-item>
                      </el-col>


                      <el-col :span="12">
                        <el-form-item label="起点桩号" prop="startPoint">
                          <el-input v-model="form.startPoint" placeholder="请输入起点桩号" />
                        </el-form-item>
                      </el-col>


                      <el-col :span="12">
                        <el-form-item label="终点桩号" prop="endPoint">
                          <el-input v-model="form.endPoint" placeholder="请输入终点桩号" />
                        </el-form-item>
                      </el-col>


                      <el-col :span="12">
                        <el-form-item label="申请内容" prop="applicationContent">
                          <el-input v-model="form.applicationContent" type="textarea" placeholder="请输入内容" />
                        </el-form-item>
                      </el-col>


                      <el-col :span="12">
                        <el-form-item label="拟开工日期" prop="plannedStartDate" style="width: 100%">
                          <el-date-picker clearable
                                          v-model="form.plannedStartDate"
                                          type="date"
                                          value-format="yyyy-MM-dd"
                                          style="width: 100%"
                                          placeholder="请选择拟开工日期">
                          </el-date-picker>
                        </el-form-item>
                      </el-col>


                      <el-col :span="12">
                        <el-form-item label="拟竣工日期" prop="plannedCompletionDate" style="width: 100%">
                          <el-date-picker clearable
                                          v-model="form.plannedCompletionDate"
                                          type="date"
                                          value-format="yyyy-MM-dd"
                                          style="width: 100%"
                                          placeholder="请选择拟竣工日期">
                          </el-date-picker>
                        </el-form-item>
                      </el-col>


                      <el-col :span="12">
                        <el-form-item label="是否交通管制" prop="isTrafficControl" style="width: 100%">
                          <el-select v-model="form.isTrafficControl" placeholder="请选择是否交通管制" style="width: 100%">
                            <el-option
                                v-for="dict in dict.type.engineering_is_regulated"
                                :key="dict.value"
                                :label="dict.label"
                                :value="dict.value"
                            ></el-option>
                          </el-select>
                        </el-form-item>
                      </el-col>


                      <el-col :span="12">
                        <el-form-item label="联系人" prop="contactPerson">
                          <el-input v-model="form.contactPerson" placeholder="请输入联系人" />
                        </el-form-item>
                      </el-col>


                      <el-col :span="12">
                        <el-form-item label="电话" prop="phoneNumber">
                          <el-input v-model="form.phoneNumber" placeholder="请输入电话" />
                        </el-form-item>
                      </el-col>


                      <el-col :span="12">
                        <el-form-item label="备注" prop="remarks">
                          <el-input v-model="form.remarks" placeholder="请输入备注" />
                        </el-form-item>
                      </el-col>


                      <el-col :span="12">
                        <el-form-item label="复函文件编码" prop="replyDocumentCode">
                          <el-input v-model="form.replyDocumentCode" placeholder="请输入复函文件编码" />
                        </el-form-item>
                      </el-col>


                      <el-col :span="12">
                        <el-form-item label="复函时间" prop="replyTime"style="width: 100%">
                          <el-date-picker clearable
                                          v-model="form.replyTime"
                                          type="date"
                                          style="width: 100%"
                                          value-format="yyyy-MM-dd"
                                          placeholder="请选择复函时间">
                          </el-date-picker>
                        </el-form-item>
                      </el-col>


                      <el-col :span="12">
                        <el-form-item label="复函意见" prop="replyOpinions">
                          <el-input v-model="form.replyOpinions" placeholder="请输入复函意见" />
                        </el-form-item>
                      </el-col>


                      <el-col :span="12">
                        <el-form-item label="涉路施工协议编号" prop="agreementNumber">
                          <el-input v-model="form.agreementNumber" placeholder="请输入涉路施工协议编号" />
                        </el-form-item>
                      </el-col>


                      <el-col :span="12">
                        <el-form-item label="路产协议编号" prop="roadPropertyAgreementNumber">
                          <el-input v-model="form.roadPropertyAgreementNumber" placeholder="请输入路产协议编号" />
                        </el-form-item>
                      </el-col>


                      <el-col :span="12">
                        <el-form-item label="协议金额" prop="agreementAmount">
                          <el-input v-model="form.agreementAmount" placeholder="请输入协议金额" />
                        </el-form-item>
                      </el-col>


                      <el-col :span="12">
                        <el-form-item label="车道" prop="lane" style="width: 100%">
                          <el-select v-model="form.roadLane" placeholder="请选择车道" style="width: 100%">
                            <el-option
                              v-for="dict in dict.type.engineering_lane"
                              :key="dict.value"
                              :label="dict.label"
                              :value="dict.value"
                            ></el-option>
                          </el-select>
                        </el-form-item>
                      </el-col>


                      <el-col :span="12">
                        <el-form-item label="建设单位" prop="constructionUnit">
                          <el-input v-model="form.constructionUnit" placeholder="请输入建设单位" />
                        </el-form-item>
                      </el-col>


                      <el-col :span="12">
                        <el-form-item label="设计单位" prop="designUnit">
                          <el-input v-model="form.designUnit" placeholder="请输入设计单位" />
                        </el-form-item>
                      </el-col>


                      <el-col :span="12">
                        <el-form-item label="施工单位" prop="constructionCompany">
                          <el-input v-model="form.constructionCompany" placeholder="请输入施工单位" />
                        </el-form-item>
                      </el-col>


                      <el-col :span="12">
                        <el-form-item label="监理单位" prop="supervisionUnit">
                          <el-input v-model="form.supervisionUnit" placeholder="请输入监理单位" />
                        </el-form-item>
                      </el-col>

<!--            <el-divider content-position="center">涉路工程车道信息</el-divider>-->
<!--            <el-row :gutter="10" class="mb8">-->
<!--              <el-col :span="1.5">-->
<!--                <el-button type="primary" icon="el-icon-plus" size="mini" @click="handleAddRoadEngineeringLane">添加</el-button>-->
<!--              </el-col>-->
<!--              <el-col :span="1.5">-->
<!--                <el-button type="danger" icon="el-icon-delete" size="mini" @click="handleDeleteRoadEngineeringLane">删除</el-button>-->
<!--              </el-col>-->
<!--            </el-row>-->
<!--            <el-table :data="roadEngineeringLaneList" :row-class-name="rowRoadEngineeringLaneIndex" @selection-change="handleRoadEngineeringLaneSelectionChange" ref="roadEngineeringLane">-->
<!--              <el-table-column type="selection" width="50" align="center" />-->
<!--              <el-table-column label="序号" align="center" prop="index" width="50"/>-->
<!--                      <el-table-column label="车道信息" prop="lane" width="150">-->
<!--                        <template slot-scope="scope">-->
<!--                          <el-input v-model="scope.row.lane" placeholder="请输入车道信息" />-->
<!--                        </template>-->
<!--                      </el-table-column>-->
<!--            </el-table>-->


      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 用户导入对话框 -->
    <el-dialog :title="upload.title" :visible.sync="upload.open" width="400px" append-to-body>
      <el-upload
          ref="upload"
          :limit="1"
          accept=".xlsx, .xls"
          :headers="upload.headers"
          :action="upload.url + '?updateSupport=' + upload.updateSupport"
          :disabled="upload.isUploading"
          :on-progress="handleFileUploadProgress"
          :on-success="handleFileSuccess"
          :auto-upload="false"
          drag
      >
        <i class="el-icon-upload"></i>
        <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
        <div class="el-upload__tip text-center" slot="tip">
          <div class="el-upload__tip" slot="tip">
            <el-checkbox v-model="upload.updateSupport" /> 是否更新已经存在的用户数据
          </div>
          <span>仅允许导入xls、xlsx格式文件。</span>
          <el-link type="primary" :underline="false" style="font-size:12px;vertical-align: baseline;" @click="importTemplate">下载模板</el-link>
        </div>
      </el-upload>
      <div slot="footer" class="dialog-footer">
        <div class="footer-buttons">
          <el-button type="primary" @click="submitFileForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </div>
    </el-dialog>
    <el-drawer :wrapperClosable="false" :title="detailTitle" :visible.sync="openEdit" size="70%" destroy-on-close v-if="openEdit">
      <edit :row-data="form" @close="handleClose" :readonly="readonly"></edit>
    </el-drawer>
<!--    <el-drawer :wrapperClosable="false" title="新增任务单" :visible.sync="openTask" size="70%" destroy-on-close v-if="openTask">-->
<!--      <edit-detail :type="taskType" @close="handleClose" :project="checkRow"></edit-detail>-->
<!--    </el-drawer>-->
    <MissionDetail
      :visible.sync="showDetail"
      :data="currentRow"
      title="上传附件管理"
      :append-to-body="true"
      size="55%"
    />
  </div>
</template>

<script>
import {
  listEngineering,
  getEngineering,
  delEngineering,
  addEngineering,
  updateEngineering,
  getUserPermissions,
  getEngineeringLane
} from "@/api/engineering/engineering";
  import { getToken } from "@/utils/auth";
  import Treeselect from "@riophae/vue-treeselect";
  import "@riophae/vue-treeselect/dist/vue-treeselect.css";
  import selectTree from "@/components/DeptTmpl/selectTree.vue";
  import RoadSection from "@/views/baseData/components/roadSection/index.vue";
  import MissionDetail from "@/views/other/engineering/detail.vue";
  import TaskDetail from "@/views/maintenanceProject/taskList/component/taskDetail.vue";
  import Edit from "@/views/other/engineering/edit.vue";
  import {formatPile} from "@/utils/ruoyi";
  import Detail from "@/views/maintenanceProject/projectManage/detail.vue";
import {findUserMaintenanceIds, findUserDept} from "@/api/engineering/engineering";

  export default {
    name: "Engineering",
    dicts: ['engineering_is_regulated', 'engineering_direction', 'engineering_project_status', 'engineering_compensation_situation', 'engineering_lane', 'engineering_is_regulated'],
    components: {Edit, TaskDetail, MissionDetail, RoadSection, selectTree },
    data() {
      return {
        detailTitle: '涉路工程信息',
        //是否展示弹出
        showDetail: false,
        currentRow: {},
        // 遮罩层
        loading: true,
        // 选中数组
        ids: [],
        // 子表选中数据
         checkedRoadEngineeringLane: [],
        // 非单个禁用
        single: true,
        // 非多个禁用
        multiple: true,
        // 显示搜索条件
        showSearch: false,
        dictType:[],
        // 总条数
        total: 0,
        // 涉路工程表格数据
        engineeringList: null,
        // 涉路工程车道表格数据
        roadEngineeringLaneList: [],
        //用户所拥有的路段列表
        userMaintenanceIds: [],
        //用户所拥有的部门列表
        userDeptList:[],
        //根据ID查询出来的车道
        roadEngineeringLaneDetails:[],
        // 弹出层标题
        title: "",
        // 部门树选项
        deptOptions: undefined,
        // 是否显示弹出层
        open: false,
        openEdit: false,
        // 表单参数
        form: {},
        defaultProps: {
          children: "children",
          label: "label"
        },
        // 用户导入参数
        upload: {
          // 是否显示弹出层（用户导入）
          open: false,
          // 弹出层标题（用户导入）
          title: "",
          // 是否禁用上传
          isUploading: false,
          // 是否更新已经存在的用户数据
          updateSupport: 0,
          // 设置上传的请求头部
          headers: { Authorization: "Bearer " + getToken() },
          // 上传的地址
          url: process.env.VUE_APP_BASE_API + "/system/user/importData"
        },
        // 查询参数
        queryParams: {
          pageNum: 1,
          pageSize: 10,
          documentReceived: null,
          projectStatus: null,
          compensationSituation: null,
          managementUnitId: null,
          maintenanceSectionId: null
        },
        // 列信息
        columns: [
        { key: 0, label: `来文名称`, visible: true },
        { key: 1, label: `项目状态`, visible: true },
        { key: 2, label: `赔付情况`, visible: true },
        { key: 3, label: `管养单位`, visible: true },
        { key: 4, label: `管养单位Id`, visible: true },
        { key: 5, label: `养护路段`, visible: true },
        { key: 6, label: `养护路段Id`, visible: true },
        { key: 7, label: `路线编码`, visible: true },
        { key: 8, label: `来文时间`, visible: true },
        { key: 9, label: `来文单位`, visible: true },
        { key: 10, label: `起点桩号`, visible: true },
        { key: 11, label: `终点桩号`, visible: true },
        { key: 12, label: `申请内容`, visible: true },
        { key: 13, label: `拟开工日期`, visible: true },
        { key: 14, label: `拟竣工日期`, visible: true },
        { key: 15, label: `是否交通管制`, visible: true },
        { key: 16, label: `联系人`, visible: true },
        { key: 17, label: `电话`, visible: true },
        { key: 18, label: `备注`, visible: true },
        { key: 19, label: `复函文件编码`, visible: true },
        { key: 20, label: `复函时间`, visible: true },
        { key: 21, label: `复函意见`, visible: true },
        { key: 22, label: `涉路施工协议编号`, visible: true },
        { key: 23, label: `路产协议编号`, visible: true },
        { key: 24, label: `协议金额`, visible: true },
        { key: 25, label: `方向`, visible: true },
        { key: 26, label: `建设单位`, visible: true },
        { key: 27, label: `设计单位`, visible: true },
        { key: 28, label: `施工单位`, visible: true },
        { key: 29, label: `监理单位`, visible: true }
        ],
        // 表单校验
        rules: {
    documentReceived: [
        { required: true, message: "来文名称不能为空", trigger: "change" }
    ],
    projectStatus: [
        { required: true, message: "项目状态不能为空", trigger: "change" }
    ],
    compensationSituation: [
        { required: true, message: "赔付情况不能为空", trigger: "change" }
    ],


        }
      };
    },
    watch: {
      // 根据名称筛选部门树
                      },
    created() {
      this.findUserDeptRoadList();
      // this.getList();
      // this.getDeptTree();
    },
    methods: {
      indexMethod(index) {
        return (this.queryParams.pageNum - 1) * this.queryParams.pageSize + index + 1;
      },
      /** 查询用户列表 */
      getList() {
        this.loading = true;
        console.log("Query Params: ", this.queryParams);
        listEngineering(this.queryParams).then(response => {
          console.log("res"+response);
          this.engineeringList = response.rows;
          this.total = response.total;
          this.loading = false;
        });
      },
      // 取消按钮
      cancel() {
        this.open = false;
        this.reset();
      },
      // 表单重置
      reset() {
        this.form = {
            documentReceived: null,
            projectStatus: null,
            compensationSituation: null,
            managementUnit: null,
            managementUnitId: null,
            maintenanceSection: null,
            maintenanceSectionId: null,
            roadCode: null,
            submissionTime: null,
            reportingUnit: null,
            startPoint: null,
            endPoint: null,
            applicationContent: null,
            plannedStartDate: null,
            plannedCompletionDate: null,
            isTrafficControl: null,
            contactPerson: null,
            phoneNumber: null,
            remarks: null,
            replyDocumentCode: null,
            replyTime: null,
            replyOpinions: null,
            agreementNumber: null,
            roadPropertyAgreementNumber: null,
            agreementAmount: null,
            direction: null,
            constructionUnit: null,
            designUnit: null,
            constructionCompany: null,
            supervisionUnit: null,
            roadLane: null
        };
        this.roadEngineeringLaneList = [];
        this.resetForm("form");
      },
      /** 搜索按钮操作 */
      handleQuery() {
        this.queryParams.pageNum = 1;
        console.log(this.queryParams);
        this.findUserDeptRoadList();
      },
      /** 新增按钮操作 */
      handleAdd() {
        this.reset();
        this.title = "添加涉路工程";
        this.readonly = false
        this.openEdit = true
      },
      /** 重置按钮操作 */
      resetQuery() {
        this.dateRange = [];
        this.resetForm("queryForm");
        this.handleQuery();
      },
      // 多选框选中数据
      handleSelectionChange(selection) {
        this.ids = selection.map(item => item.id);
        this.single = selection.length != 1;
        this.multiple = !selection.length;
      },
      //上传附件
      handleUploadClick(row) {
        this.showDetail = true;
        this.currentRow = row;
        console.log('当前行数据:', this.currentRow);
      },
      // 表格点击勾选
      handleRowClick(row) {
        row.isSelected = !row.isSelected;
        this.$refs.table.toggleRowSelection(row);
      },
      // 勾选高亮
      rowStyle({ row, rowIndex }) {
        if (this.ids.includes(row.id)) {
          return { 'background-color': '#b7daff', color: '#333' }
        } else {
          return { 'background-color': '#fff', color: '#333' }
        }
      },
      handleClose() {
        this.openEdit = false;
        this.reset();
      },
      /** 修改按钮操作 */
      /** 修改按钮操作 */
      async handleUpdate(row) {
        this.reset();
        const id = row.id || this.ids;
        try {
          // 使用 Promise.all 并行执行 getEngineering 和 getEngineeringLane 方法
          const [engineeringResponse, laneResponse] = await Promise.all([
            getEngineering(id),     // 获取涉路工程数据
            getEngineeringLane(id)  // 获取车道数据
          ]);

          // 合并数据
          this.form = engineeringResponse.data;
          this.form.laneList = laneResponse.data;  // 添加 laneList 到 form

          // 打印更新后的 form 数据，确保 laneList 已包含
          console.log('更新后的 form:', JSON.stringify(this.form));

          // 打开编辑页面
          this.openEdit = true;
          this.title = "修改涉路工程";

          // 传递数据到编辑页面
          this.updateEditPage(this.form);  // 传递数据到编辑页面

        } catch (error) {
          console.error("获取数据失败:", error);
        }
      },


      /** 获取用户权限 */
      findUserDeptRoadList() {
        this.loading = true;
        // 使用 Promise.all 等待两个异步请求完成
        Promise.all([findUserDept(), findUserMaintenanceIds()])
          .then(([userDeptData, userMaintenanceData]) => {
            // 在这里可以确保两个请求都已经完成
            this.userDeptList = userDeptData.data;
            this.userMaintenanceIds = userMaintenanceData.data;

            this.queryParams.userDeptList = userDeptData.data;
            this.queryParams.userMaintenanceIds = userMaintenanceData.data;

            // 发送请求获取数据
            getUserPermissions(this.queryParams).then(res => {
              console.log("res"+JSON.stringify(res));
              this.engineeringList = res.rows;
              this.total = res.total;
              this.loading = false;
            });
          })
          .catch(error => {
            console.error("请求失败: ", error);
            this.loading = false;
          });
      },

      /** 提交按钮 */
      submitForm: function() {
        this.$refs["form"].validate(valid => {
          if (valid) {
            this.form.roadEngineeringLaneList = this.roadEngineeringLaneList;
            if (this.form.id != null) {
              updateEngineering(this.form).then(response => {
                this.$modal.msgSuccess("修改成功");
                this.open = false;
                this.findUserDeptRoadList();
              });
            } else {
              addEngineering(this.form).then(response => {
                this.$modal.msgSuccess("新增成功");
                this.open = false;
                this.findUserDeptRoadList();
              });
            }
          }
        });
      },
      /** 删除按钮操作 */
      handleDelete(row) {
        const id = row.id || this.ids;
        this.$modal.confirm('是否确认删除涉路工程编号为"' + id + '"的数据项？').then(function() {
          return delEngineering(id);
        }).then(() => {
          this.findUserDeptRoadList();
          this.$modal.msgSuccess("删除成功");
        }).catch(() => {});
      },
  /** 涉路工程车道序号 */
  rowRoadEngineeringLaneIndex({ row, rowIndex }) {
    row.index = rowIndex + 1;
  },
  /** 涉路工程车道添加按钮操作 */
  handleAddRoadEngineeringLane() {
    let obj = {};
    obj.lane = "";
    this.roadEngineeringLaneList.push(obj);
  },
  /** 涉路工程车道删除按钮操作 */
  handleDeleteRoadEngineeringLane() {
    if (this.checkedRoadEngineeringLane.length == 0) {
      this.$modal.msgError("请先选择要删除的涉路工程车道数据");
    } else {
      const roadEngineeringLaneList = this.roadEngineeringLaneList;
      const checkedRoadEngineeringLane = this.checkedRoadEngineeringLane;
      this.roadEngineeringLaneList = roadEngineeringLaneList.filter(function(item) {
        return checkedRoadEngineeringLane.indexOf(item.index) == -1
      });
    }
  },
  /** 复选框选中数据 */
  handleRoadEngineeringLaneSelectionChange(selection) {
    this.checkedRoadEngineeringLane = selection.map(item => item.index)
  },

      /** 导出按钮操作 */
      handleExport() {

    this.download('repote/engineering/export', {
      ...this.queryParams
    }, `engineering_${new Date().getTime()}.xlsx`)

      },
      /** 导入按钮操作 */
      handleImport() {
        this.upload.title = "用户导入";
        this.upload.open = true;
      },
      /** 下载模板操作 */
      importTemplate() {
        this.download('system/user/importTemplate', {
        }, `user_template.xlsx`)
      },
      // 文件上传中处理
      handleFileUploadProgress(event, file, fileList) {
        this.upload.isUploading = true;
      },
      // 文件上传成功处理
      handleFileSuccess(response, file, fileList) {
        this.upload.open = false;
        this.upload.isUploading = false;
        this.$refs.upload.clearFiles();
        this.$alert("<div style='overflow: auto;overflow-x: hidden;max-height: 70vh;padding: 10px 20px 0;'>" + response.msg + "</div>", "导入结果", { dangerouslyUseHTMLString: true });
        this.findUserDeptRoadList();
      },
      // 提交上传文件
      submitFileForm() {
        this.$refs.upload.submit();
      }
  }
  };
</script>
<style>
  .hasTagsView .app-main[data-v-078753dd]{
    background: #f5f7fa;
  }

  .tableDiv{
    background-color: white;
    padding-bottom: 10px;
  }

  .dialog-footer {
    display: flex;
    justify-content: center; /* 水平居中 */
    padding: 16px; /* 添加一些内边距 */
  }
  .footer-buttons {
    display: flex;
    gap: 10px; /* 按钮之间的间距 */
  }
</style>
