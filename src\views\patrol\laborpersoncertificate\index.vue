<template>
  <div class="app-container">
    <el-row :gutter="20">
      <!--部门数据-->
      <el-col :span="deptNav ? 5:0 " :xs="24" class="leftDiv">
        <!--折叠图标-->
        <div class="leftIcon"  @click="deptNav=false">
          <span class="el-icon-caret-left"></span>
        </div>
        <div class="head-container">
          <el-input
            v-model="deptName"
            placeholder="单位名称"
            clearable
            size="small"
            prefix-icon="el-icon-search"
            style="margin-bottom: 20px"
          />
        </div>
        <div class="head-container" style="width: 300px">
          <el-tree
              :data="deptOptions"
              :props="defaultProps"
              :expand-on-click-node="false"
              :filter-node-method="filterNode"
              ref="tree"
              :render-after-expand="true"
              node-key="id"
              highlight-current
              @node-click="handleNodeClick"
            >
              <template #default="{ node, data }">
                  <div class="custom-tree-node" >
                      <div :class="{ 'activeNode': node.data.domainType === 999 }"  :title="data.label"
                        style="width:270px;white-space:nowrap;overflow:hidden;text-overflow:ellipsis;">
                        {{ node.label }}
                      </div>
                  </div>
              </template>
            </el-tree>
        </div>
      </el-col>

      <el-col :span="deptNav ? 19:24" :xs="24">
        <!--展开图标-->
        <div class="rightIcon" @click="deptNav=true" v-show="!deptNav">
          <span class="el-icon-caret-right"></span>
        </div>
        <!-- 暂时屏蔽，后期需要就放开 -->
        <el-row v-if="false">
          <el-col :span="24" >
            <el-form :model="queryParams" ref="queryForm" size="small" :inline="true"  label-width="68px">
                <el-form-item >
                  <el-button type="primary"  @click="deptNav=!deptNav"   :icon="deptNavIcon" size="mini">
                    <span v-show="deptNav">折叠</span>
                    <span v-show="!deptNav">展开</span>
                  </el-button>
                </el-form-item>
            </el-form>
            <!--默认折叠-->
          </el-col>
        </el-row>
        
        <el-col >
            <el-tabs type="border-card" v-model="activeTab">
              <el-tab-pane label="劳务管理人员" name="manager">
                  <LaborTabData ref="laborManager" v-if="activeTab === 'manager'" :personType="1" :domainId="domainId" :grade="grade"
                      :labDomainId="labDomainId" :domainIdStr="domainIdStr" :domainName="curDomainName">
                  </LaborTabData>
              </el-tab-pane>
              <el-tab-pane label="劳务作业人员" name="worker">
                  <LaborTabData ref="laborWorker" v-if="activeTab === 'worker'" :personType="2" :domainId="domainId" :grade="grade"
                      :labDomainId="labDomainId" :domainIdStr="domainIdStr" :domainName="curDomainName">
                  </LaborTabData>
              </el-tab-pane>
            </el-tabs>
        </el-col>

      </el-col>
    </el-row>
    
  </div>
</template>

<script>
  //import { listLaborpersoncertificate, getLaborpersoncertificate, delLaborpersoncertificate, addLaborpersoncertificate, updateLaborpersoncertificate } from "@/api/patrol/laborpersoncertificate";
  import { getLaborUnitTree } from "@/api/patrol/laborunit";
  import { getToken } from "@/utils/auth";
  import LaborTabData from "@/views/patrol/laborpersoncertificate/laborTabData.vue"
  //import Treeselect from "@riophae/vue-treeselect";
  import "@riophae/vue-treeselect/dist/vue-treeselect.css";

  export default {
    name: "Laborpersoncertificate",
    components: { LaborTabData },
    data() {
      return {
        // 遮罩层
        loading: false,
        // 选中数组
        ids: [],
        // 非单个禁用
        single: true,
        // 非多个禁用
        multiple: true,
        // 显示搜索条件
        showSearch: false,
        dictType:[],
        // 总条数
        total: 0,
        // 劳务人员持证情况表格数据
        laborpersoncertificateList: null,
        // 弹出层标题
        title: "",
       // 左侧组织树
        deptNav: true,
        // 部门树选项
        deptOptions: undefined,
        // 部门名称
        deptName: undefined,
        //当前点击的树节点
        currentOptionData:undefined,
        // 是否显示弹出层
        open: false,
        activeTab: "manager",
        // 表单参数
        form: {},
        defaultProps: {
          children: "children",
          label: "label"
        },
        domainId: "-1",
        grade: -1,
        domainIdStr: "",
        labDomainId: "0",
        //当前所属单位名称
        curDomainName: "",
        // 用户导入参数
        upload: {
          // 是否显示弹出层（用户导入）
          open: false,
          // 弹出层标题（用户导入）
          title: "",
          // 是否禁用上传
          isUploading: false,
          // 是否更新已经存在的用户数据
          updateSupport: 0,
          // 设置上传的请求头部
          headers: { Authorization: "Bearer " + getToken() },
          // 上传的地址
          url: process.env.VUE_APP_BASE_API + "/system/user/importData"
        },
        // 查询参数
        queryParams: {
          pageNum: 1,
          pageSize: 50,
            domainId: null,
            cerType: null,
            cerName: null,
            cerCode: null,
            cerAuthority: null,
            issueDate: null,
            validDate: null,
            cerFile: null,
            userId: null,
            status: null
        },
        // 列信息
        columns: [
        { key: 0, label: `所属单位id`, visible: true },
        { key: 1, label: `证书类型`, visible: true },
        { key: 2, label: `证书名称`, visible: true },
        { key: 3, label: `证书编号`, visible: true },
        { key: 4, label: `颁发机构`, visible: true },
        { key: 5, label: `颁发日期`, visible: true },
        { key: 6, label: `有效期至`, visible: true },
        { key: 7, label: `附件`, visible: true },
        { key: 8, label: `备注`, visible: true },
        { key: 9, label: `删除标志（0代表存在，1代表删除）`, visible: true },
        { key: 10, label: `用户id`, visible: true },
        { key: 11, label: `状态（0-正常，1-停用）`, visible: true }
        ],
        // 表单校验
        rules: {


        }
      };
    },
    watch: {
      // 根据名称筛选部门树
      deptName(val) {
        this.$refs.tree.filter(val);
      }
    },
    created() {
      this.getDeptTree();
      // this.getConfigKey("sys.user.initPassword").then(response => {
      //   this.initPassword = response.msg;
      // });
    },
    computed: {
      deptNavIcon() {
          return this.deptNav ? 'el-icon-arrow-left' : 'el-icon-arrow-right';
      },
    },
    methods: {
      // /** 查询用户列表 */
      // getList() {
      //   this.loading = true;
      //   listLaborpersoncertificate(this.queryParams).then(response => {
      //     this.laborpersoncertificateList = response.rows;
      //     this.total = response.total;
      //     this.loading = false;
      //   });
      // },
      /** 查询部门下拉树结构 */
      getDeptTree() {
        getLaborUnitTree().then(response => {
          this.deptOptions = response.data;
        });
      },
      // 筛选节点
      filterNode(value, data) {
        if (!value) return true;
        return data.label.indexOf(value) !== -1;
      },
      // 节点单击事件
      handleNodeClick(val) {
        if (val.domainType === 999) {
          this.domainId = val.parentId;
          this.grade = 3
          this.labDomainId = val.id
          this.domainIdStr = val.id
          this.curDomainName = val.domainShortName
        } else {
          let labDomainIdList = this.getChilds(val);
          this.domainId = val.id;
          this.grade = 1;
          this.labDomainId = "0";
          this.domainIdStr = labDomainIdList.length > 0 ? labDomainIdList.join(",") : ''
        }
        
        this.$nextTick(() => {
          if (this.activeTab === 'manager'){
            this.$refs.laborManager.handleQuery();
          }else {
            this.$refs.laborWorker.handleQuery();
          }
        })
        
      },
      getChilds(node){
        let result = []
        if (node.domainType === 999) {
            result.push(node.id)
        }
        if (node.children && node.children.length > 0) {
            for (let i = 0; i < node.children.length; i++) {
                result = result.concat(this.getChilds(node.children[i]));
            }
        }
        return result
      },
      // 取消按钮
      cancel() {
        this.open = false;
        this.reset();
      },
      // 表单重置
      reset() {
        this.form = {
            domainId: null,
            cerType: null,
            cerName: null,
            cerCode: null,
            cerAuthority: null,
            issueDate: null,
            validDate: null,
            cerFile: null,
            remark: null,
            delFlag: null,
            userId: null,
            status: null
        };
        this.resetForm("form");
      },
  }
  };
</script>
<style scoped>
  .hasTagsView .app-main[data-v-078753dd]{
    background: #f5f7fa;
  }

  .tableDiv{
    background-color: white;
    padding-bottom: 10px;
  }

  .custom-tree-node{
      display: flex;
      align-items: center;
      width: 100%;
      justify-content: space-between;
      font-size:14px;
  }

  .leftDiv{
    border-right: 1px solid #d8dce5;
    min-height: calc(100vh - 110px);
    overflow-y: auto;
    height: calc(80vh - 110px);
    position: relative;
    top: -20px;
    padding-top: 10px;
    background-color: white;
  }

  .leftIcon{
    border: 1px solid #DCDFE6;
    border-radius: 8px;
    width: 16px;
    height: 50px;
    line-height: 50px;
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
    position: absolute;
    right: 0;
    top: 300px;
    z-index: 2;
  }
  .leftIcon:hover{
    background-color: #DCDFE6;
  }

  .rightIcon{
    border: 1px solid #DCDFE6;
    border-radius: 8px;
    width: 16px;
    height: 50px;
    line-height: 50px;
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
    position: absolute;
    left: -10px;
    top: 280px;
    z-index: 10;
    background: white;
  }
  .rightIcon:hover{
    background-color: #DCDFE6;
  }
</style>
