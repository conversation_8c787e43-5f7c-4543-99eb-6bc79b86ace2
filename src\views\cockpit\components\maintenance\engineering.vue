<template>
  <div>
    <Echarts :key="key" :option="option" v-if="option" :height="isBig ? '26.5vh' : '26vh'" />
  </div>
</template>

<script>
import { calcFontSize, isBigScreen } from "../../util/utils";
import Echarts from "../echarts/echarts.vue";
import { getProjCountInfo } from "@/api/cockpit/index";

export default {
  components: {
    Echarts,
  },
  props: {
    year: {
      default: new Date().getFullYear(),
    },
  },
  data() {
    return {
      option: null,
      isBig: isBigScreen(),
      chartList: [],
      key: "",
    };
  },
  async mounted() {
    await this.getChartList();
    this.key = new Date().getTime();
  },
  watch: {
    async year() {
      await this.getChartList();
      this.key = new Date().getTime();
    },
  },
  methods: {
    async getChartList() {
      const res = await getProjCountInfo({ year: this.year });
      if (res.code === 200 && res.rows) {
        this.chartList = res.rows || [];
        this.option = this.initPieCharts();
      }
    },
    initPieCharts() {
      let color = [
        "#016DFF",
        "#00FDFD",
        "#FF5B00",
        "#FFDD21",
        "#1DA7FF",
        "#2967EA",
      ];
      let titledata = this.chartList.map((el) => el.type);
      let numberdata = this.chartList.map((el) => el.count);
      let outr = this.isBig ? "67%" : "70%";
      let inr = this.isBig ? "62%" : "65%";
      var total = 0;
      let lengData = [];
      let index = -1;
      //计算总和
      for (var i = 0; i < numberdata.length; i++) {
        total += Number(numberdata[i]);
      }
      let placeHolderStyle = {
        normal: {
          label: {
            show: false,
          },
          labelLine: {
            show: false,
          },
          color: "rgba(0, 0, 0, 0)",
          borderColor: "rgba(0, 0, 0, 0)",
          borderWidth: 0,
        },
      };

      var data = [];
      for (var i = 0; i < numberdata.length; i++) {
        data.push(
          {
            value: numberdata[i],
            name: titledata[i],
            itemStyle: {
              normal: {
                borderWidth: 3,
                shadowBlur: 2,
                borderColor: color[i],
                shadowColor: color[i],
                color: color[i],
              },
            },
          },
          {
            value: total / 100,
            name: "",
            itemStyle: placeHolderStyle,
          }
        );
        lengData.push({
          name: titledata[i],
          color: color[i],
          value: numberdata[i],
        });
      }
      let center = this.isBig ? ["32%", "50%"] : ["28%", "50%"];
      var seriesObj = [
        {
          type: "pie",
          zlevel: 0,
          silent: true,
          radius: this.isBig ? ["76%", "73%"] : ["80%", "77%"],
          center, //此处调整圆环的位置
          hoverAnimation: false,
          color: "rgba(0,62,122,0.9)",
          label: {
            normal: {
              show: false,
            },
          },
          labelLine: {
            normal: {
              show: false,
            },
          },
          data: [1],
        },
        {
          name: "",
          type: "pie",
          clockWise: false,
          startAngle: "90",
          center, //此处调整圆环的位置
          radius: [outr, inr], //此处可以调整圆环的大小
          hoverAnimation: false,
          itemStyle: {
            normal: {
              label: {
                show: false,
                position: "outside",
                color: "#ddd",
                formatter: function (params) {
                  var percent = 0;
                  var total = 1;
                  for (var i = 0; i < data.length; i++) {
                    total += data[i].value;
                  }
                  percent = ((params.value / total) * 100).toFixed(2);
                  if (params.name !== "") {
                    return percent + "%";
                  } else {
                    return "";
                  }
                },
                fontSize: this.isBig ? 30 : calcFontSize(12),
              },
              labelLine: {
                length: this.isBig ? 30 : 15,
                length2: this.isBig ? 60 : 30,
                show: false,
                color: "#00ffff",
              },
            },
          },
          data: data,
          animationType: "scale",
          animationEasing: "elasticOut",
          animationDelay: function (idx) {
            return idx * 50;
          },
        },
      ];

      let option = {
        backgroundColor: "rgba(0,0,0,0)",
        tooltip: {
          show: false,
        },
        title: {
          text: "{a|" + total + "}{c| 个}",
          subtext: "养护工程",
          top: "38%",
          textAlign: "center",
          left: this.isBig ? "31%" : "27%",
          textStyle: {
            color: "#fff",
            shadowColor: "rgba(27,126,242,0.8)",
            shadowBlur: 10,
            shadowOffsetX: 5,
            shadowOffsetY: 5,
            rich: {
              a: {
                fontSize: this.isBig ? 60 : calcFontSize(22),
                color: "#fff",
                fontWeight: "700",
              },
              c: {
                fontSize: this.isBig ? 42 : calcFontSize(13),
                color: "rgba(255,255,255,0.9)",
                padding: [2, 2],
              },
            },
          },
          subtextStyle: {
            fontSize: this.isBig ? 48 : calcFontSize(16),
            align: "center",
            color: "#fff",
          },
        },
        toolbox: {
          show: false,
        },
        legend: {
          y: "center",
          right: "8%",
          icon: "circle", //改变legend小图标形状
          itemGap: this.isBig ? 20 : 10, // 设置legend的间距
          itemWidth: this.isBig ? 20 : 10, // 设置宽度
          itemGap: this.isBig ? 35 : 16,
          orient: 'vertical',
          formatter: (name) => {
            let str = name;
            let arr = lengData.filter((v) => v.name == name);
            if (arr.length > 0) {
              str = `{title|${name}：}{num|${arr[0].value}} {unit|个}`;
            }
            return str;
          },
          textStyle: {
            fontSize: this.isBig ? 30 : calcFontSize(15),
            color: "#fff",
            rich: {
              title: {
                color: 'rgba(255,255,255,0.8)',
                fontSize: this.isBig ? 30 : calcFontSize(12),
              },
              num: {
                color: '#fff',
                fontSize: this.isBig ? 30 : calcFontSize(13),
              },
              unit: {
                color: 'rgba(182,182,182,0.8)',
                fontSize: this.isBig ? 30 : calcFontSize(12),
              },
            }
          },
          itemStyle: {
            borderColor: "transparent",
          },
          data: lengData,
        },
        series: seriesObj,
      };
      return option;
    },
  },
};
</script>

<style lang="scss" scoped></style>
