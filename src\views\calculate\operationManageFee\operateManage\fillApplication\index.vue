<template>
  <div class="app-container">
    <el-row :gutter="20">
      <el-col :span="24" :xs="24">
        <!--操作按钮区开始-->
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5" v-if="type == 1">
            <el-button
                icon="el-icon-plus"
                size="mini"
                type="primary"
                v-has-menu-permi="['operate:application:add']"
                @click="handleOpenAdd"
            >新增申请单
            </el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button
                icon="el-icon-download"
                size="mini"
                type="success"
                v-has-menu-permi="['operate:application:export', 'operate:application:exportView']"
                @click="exportList"
            >导出
            </el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button
                icon="el-icon-view"
                size="mini"
                type="warning"
                @click="handleOpenOperate"
            >审核意见
            </el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button
                icon="el-icon-search"
                size="mini"
                type="primary"
                @click="handleQuery"
            >查询
            </el-button>
          </el-col>
          <right-toolbar :columns="columns" @queryTable="handleQuery"></right-toolbar>
        </el-row>
        <!--操作按钮区结束-->
        <!--数据表格开始-->
        <div class="tableDiv">
          <el-table v-adjust-table
              ref="dataTable"
              v-loading="loading"
              :data="tableData"
              :height="`calc(100vh - 270px)`"
              border
              @row-click="handleClickRow"
              highlight-current-row
              row-key="id"
              size="mini"
              stripe
              style="width: 100%"
          >
            <el-table-column
                align="center"
                label="序号"
                type="index"
                width="50"
            />
            <template v-for="(column,index) in columns">
              <el-table-column v-if="column.visible" show-overflow-tooltip
                               :label="column.label"
                               :prop="column.field"
                               :width="column.width"
                               align="center">
                <template slot-scope="scope">
                  <dict-tag v-if="column.dict" :options="dict.type[column.dict]" :value="scope.row[column.field]"/>
                  <template v-else-if="column.slots">
                    <RenderDom :index="index" :render="column.render" :row="scope.row"/>
                  </template>
                  <span v-else-if="column.isTime">{{ parseTime(scope.row[column.field], '{y}-{m}-{d}') }}</span>
                  <span v-else>{{ scope.row[column.field] }}</span>
                </template>
              </el-table-column>
            </template>
            <el-table-column
                align="center"
                class-name="small-padding fixed-width"
                fixed="right"
                label="操作"
                width="250"
            >
              <template slot-scope="scope">
                <template>
                  <el-button
                      icon="el-icon-edit"
                      size="mini"
                      type="text"
                      v-if="type == 1 || type == 0"
                      v-has-menu-permi="['operate:application:edit']"
                      @click="handleEdit(scope.row)"
                  >编辑
                  </el-button>
                  <el-button
                      icon="el-icon-view"
                      size="mini"
                      type="text"
                      v-if="type == 1 || type == 0"
                      @click="handleView(scope.row)"
                  >查看
                  </el-button>
                  <el-button
                      icon="el-icon-delete"
                      size="mini"
                      type="text"
                      v-if="type == 1 || type == 0"
                      v-has-menu-permi="['operate:application:delete']"
                      @click="handleDelete(scope.row)"
                  >删除
                  </el-button>
                  <el-button
                    icon="el-icon-check"
                    size="mini"
                    type="text"
                    v-if="type > 0"
                    v-has-menu-permi="['operate:application:submit']"
                    @click="handleSubmit(scope.row)"
                  >{{ btnText }}
                  </el-button>
<!--                  <el-button-->
<!--                      icon="el-icon-close"-->
<!--                      size="mini"-->
<!--                      type="text"-->
<!--                      v-if="type == 2"-->
<!--                      v-has-menu-permi="['operate:application:reject']"-->
<!--                      @click="handleReject(scope.row)"-->
<!--                  >驳回-->
<!--                  </el-button>-->
                </template>
              </template>
            </el-table-column>
          </el-table>
          <pagination
              v-show="total>0"
              :limit.sync="queryParams.pageSize"
              :page.sync="queryParams.pageNum"
              :total="total"
              @pagination="handleQuery"
          />
        </div>
        <!--数据表格结束-->
      </el-col>
    </el-row>
    <el-drawer :wrapperClosable="false" :title="detailTitle" :visible.sync="openDetail" size="70%" destroy-on-close>
      <detail :type="type" :row="row" :read-only="readOnly" @close="handleClose"/>
    </el-drawer>
    <el-dialog :visible.sync="openFile" destroy-on-close title="附件列表" width="90%">
      <file-upload v-model="fileId" :forView="true"></file-upload>
    </el-dialog>
    <el-dialog v-if="openOperateInfo" :visible.sync="openOperateInfo" destroy-on-close title="审核意见" width="90%">
      <operateInfo :business-key="clickRow.id" :get-node-info="nodeInfo" param-name="id"></operateInfo>
    </el-dialog>
  </div>
</template>

<script>
import Detail from "./detail.vue";
import {
  deleteApplication,
  listApplications, nodeInfo, rejectApplication,
  submitApplication, viewListApplications
} from "@/api/calculate/operationManageFee/fillApplication";
import operateInfo from "./component/operateInfo.vue";

export default {
  name: 'FillApplication',
  components: {
    operateInfo, Detail,
    RenderDom: {
      functional: true,
      props: {
        row: Object,
        index: Number,
        render: Function
      },
      render(createElement, ctx) {
        const {row, index} = ctx.props
        return ctx.props.render(row, index)
      }
    }},
  dicts: ['urgent_degree'],
  data() {
    return {
      showSearch: false,
      queryParams: {
        pageNum: 1,
        pageSize: 50
      },
      total: 0,
      loading: false,
      columns: [
        {key: 0, field: 'domainName', label: `管理处`, visible: true},
        {key: 1, field: 'applyReason', label: `申请事由`, visible: true},
        {key: 2, field: 'reservationTime', label: `预约开始时间`, visible: true},
        {key: 5, field: 'reservationTimeEnd', label: `预约结束时间`, visible: true},
        {key: 3, field: 'handleNo', label: `办理人`, visible: true},
        {key: 4, field: 'fileId', label: `授权附件`, visible: true, slots: true, render: (row, index) => {
            return (
                <el-button
                    size="mini"
                    disabled={!row.fileId}
                    type="text" onClick={e => this.handleOpenFile(e, row)}>查看</el-button>
            )
          }
        },
      ],
      tableData: [],
      openDetail: false,
      detailTitle: '新增申请单',
      btnText: '提交',
      row: {},
      type: 1,
      readOnly: false,
      fileId: '',
      openFile: false,
      openOperateInfo: false,
      clickRow: {}
    }
  },
  created() {
    this.type = Number(this.$route.query.type)
    switch (this.type) {
      case 1:
        this.btnText = '提交'
        break
      case 2:
        this.btnText = '审核'
        break
      case 3:
        this.btnText = '领出登记'
        break
      case 4:
        this.btnText = '办理登记'
        break
      case 5:
        this.btnText = '归还登记'
        break
    }
    this.handleQuery()
  },
  methods: {
    nodeInfo,
    handleQuery() {
      this.loading = true
      if (this.type != 0) {
        this.queryParams.stage = parseInt(this.type)
        listApplications(this.queryParams).then(res => {
          if (res.code == 200) {
            this.tableData = res.rows
            this.loading = false
            this.total = res.total
          }
        })
      } else {
        viewListApplications(this.queryParams).then(res => {
          if (res.code == 200) {
            this.tableData = res.rows
            this.loading = false
            this.total = res.total
          }
        })
      }
    },
    handleDelete(row) {
      this.$confirm('是否确认删除?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        deleteApplication(row.id).then(res => {
          if (res.code == 200) {
            this.$message.success('删除成功')
            this.handleQuery()
          }
        })
      })
    },
    handleClickRow(e) {
      this.clickRow = e
    },
    handleOpenOperate() {
      if (!this.clickRow.id) {
        this.$message.warning('请先选择一条记录！')
        return
      }
      this.openOperateInfo = true
    },
    handleOpenAdd() {
      this.row = {}
      this.detailTitle = '新增申请单'
      this.readOnly = false
      this.openDetail = true
    },
    handleEdit(row) {
      this.row = row
      this.detailTitle = '编辑申请单'
      this.readOnly = false
      this.openDetail = true
    },
    handleView(row) {
      this.row = row
      this.detailTitle = '查看申请单'
      this.readOnly = true
      this.openDetail = true
    },
    handleSubmit(row) {
      const title = this.type > 1 ? '审核' : '提交'
      if (this.type >= 2) {
        this.row = row
        switch (this.type) {
          case 2:
            this.detailTitle = '业务申请单审核'
            break
          case 3:
            this.detailTitle = '领出登记'
            break
          case 4:
            this.detailTitle = '办理登记'
            break
          case 5:
            this.detailTitle = '归还登记'
            break
        }
        this.readOnly = false
        this.openDetail = true
      } else {
        this.$confirm(`是否确认${title}?`, '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          submitApplication(row).then(res => {
            if (res.code == 200) {
              this.$message.success(`${title}成功`)
              this.handleQuery()
            }
          })
        })
      }
    },
    handleReject(row) {
      this.$confirm('是否确认驳回?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        rejectApplication(row).then(res => {
          if (res.code == 200) {
            this.$message.success('驳回成功')
            this.handleQuery()
          }
        })
      })
    },
    handleOpenFile(e, row) {
      this.fileId = ''
      this.openFile = true
      this.fileId = row.fileId
    },
    handleClose() {
      this.openDetail = false
      this.handleQuery()
    },
    exportList() {
      if (this.type == 0) {
        this.download(
            "manager/operate/application/exportView",
            {...this.queryParams},
            `用电户号套餐办理导出清单_${new Date().getTime()}.xlsx`,
            {
              headers: {"Content-Type": "application/json;"},
              parameterType: "body",
            }
        );
      } else {
        this.queryParams.stage = parseInt(this.type)
        this.download(
            "manager/operate/application/export",
            {...this.queryParams},
            `用电户号套餐办理导出清单_${new Date().getTime()}.xlsx`,
            {
              headers: {"Content-Type": "application/json;"},
              parameterType: "body",
            }
        );
      }
    }
  }
}
</script>

<style scoped lang="scss">

</style>
<style lang="scss" scoped>
@import "@/assets/styles/business.scss";
</style>
