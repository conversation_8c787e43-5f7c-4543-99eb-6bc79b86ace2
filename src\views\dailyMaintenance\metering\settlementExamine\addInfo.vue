<template>
  <div class="road-interflow-edit">
    <el-row :gutter="15">
      <el-col :span="24">
        <el-form
            ref="queryForm"
            :model="queryParams"
            size="mini"
            :inline="true"
            label-width="68px"
        >
          <el-form-item>
            <el-input style="width: 190px" placeholder="施工单编号" v-model="queryParams.constructionCode"></el-input>
          </el-form-item>
          <el-form-item>
            <el-button icon="el-icon-search" size="mini" type="primary" @click="handleQuery">搜索</el-button>
            <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
          </el-form-item>
        </el-form>
      </el-col>
      <el-col :span="24">
        <div class="draggable">
          <el-table v-adjust-table
              size="mini"
              style="width: 100%"
              v-loading="loading"
              border
              :data="tableData"
              row-key="id"
              height="600"
              ref="dataTable"
              stripe
              highlight-current-row
              @row-click="handleClickRow"
              @selection-change="handleSelectionChange"

          >
            <el-table-column type="selection" width="50" align="center"/>
            <el-table-column
                label="序号"
                align="center"
                type="index"
                width="50"
            />
            <template v-for="(column,index) in columns">
              <el-table-column :label="column.label"
                               v-if="column.visible"
                               align="center"
                               :prop="column.field">
                <template slot-scope="scope">
                  <dict-tag v-if="column.dict" :options="dict.type[column.dict]" :value="scope.row[column.field]"/>
                  <template v-else-if="column.slots">
                    <RenderDom :row="scope.row" :index="index" :render="column.render"/>
                  </template>
                  <span v-else-if="column.isTime">{{ parseTime(scope.row[column.field], '{y}-{m}-{d}') }}</span>
                    <span v-else>{{ scope.row[column.field] }}</span>
                </template>
              </el-table-column>
            </template>
          </el-table>
          <pagination
              v-show="total>0"
              :total="total"
              :page.sync="queryParams.pageNum"
              :limit.sync="queryParams.pageSize"
              @pagination="handleQuery"
          />
        </div>
      </el-col>
      <el-col :span="24"  style="text-align: right;padding-right: 7.5px;margin-top: 18px">
        <el-button type="primary" @click="onSave">保 存</el-button>
        <el-button @click="onClose">退 出</el-button>
      </el-col>
    </el-row>
  </div>
</template>
<script>
import {addMiddle, listBySid} from "@/api/dailyMaintenance/metering/settlementApplication";
export default {
  data() {
    return {
      columns: [
        {key: 0, width: 100, field: 'name', label: `中间计量名称`, visible: true},
        {key: 1, width: 100, field: 'code', label: `中间计量编号`, visible: true},
        {key: 2, width: 100, field: 'domainName', label: `管养单位`, visible: true},
        {key: 3, width: 100, field: 'calcDomainName', label: `申请计量单位`, visible: true},
        {key: 4, width: 100, field: 'maiSecId', label: `路段名称`, visible: true},
        {key: 5, width: 100, field: 'conName', label: `合同名称`, visible: true},
        {key: 6, width: 100, field: 'constructionFund', label: `计量资金(元)`, visible: true},
      ],
      queryParams: {
        pageNum: 1,
        pageSize: 50,
      },
      total: 0,
      selectIds: []
    }
  },
  props: {
    settleId: {
      type: String,
      default: ''
    }
  },
  mounted() {
    this.handleQuery()
  },
  created() {
  },
  methods: {
    handleQuery() {
      this.loading = true
      this.queryParams.settleId = this.settleId
      listBySid(this.queryParams).then(res => {
        this.tableData = res.rows
        this.total = res.total
        this.loading = false
      })
    },
    // 选中
    handleSelectionChange(e) {
      this.selectIds = e
    },
    handleClickRow(e) {
      e.isSelected = !e.isSelected;
      this.$refs.dataTable.toggleRowSelection(e);
    },
    onSave() {
      for (let i = 0; i < this.selectIds.length; i++) {
        this.selectIds[i].calcId = this.selectIds[i].id
        this.selectIds[i].settleId = this.settleId
      }
      addMiddle(this.selectIds).then(() => {
        this.$modal.msgSuccess('保存成功')
        this.handleQuery()
      })
    },
    resetQuery() {
      this.queryParams = {
        pageNum: 1,
        pageSize: 50,
      }
    },
    onClose() {
      this.$emit('close')
    }
  }
}
</script>
<style scoped lang="scss">

</style>
<style lang="scss" scoped>
@import "@/assets/styles/business.scss";
</style>
