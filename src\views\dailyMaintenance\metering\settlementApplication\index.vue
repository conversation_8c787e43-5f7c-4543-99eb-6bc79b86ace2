<template>
  <div class="app-container maindiv">
    <el-row :gutter="20">
      <!--部门数据-->
      <el-col :span="relaNav ? 5 : 0" :xs="24" class="leftDiv">
        <!--折叠图标-->
        <div class="leftIcon" @click="relaNav = false">
          <span class="el-icon-caret-left"></span>
        </div>
        <div class="head-container">
          <el-input
              v-model="keyword"
              placeholder="输入关键词检索"
              @change="handleSearch"
              clearable
              size="small"
              prefix-icon="el-icon-search"
              style="margin-bottom: 20px"
          />
        </div>
        <div class="head-container" style="width: 300px">
          <el-tree
              :data="filteredTreeData"
              :expand-on-click-node="false"
              :filter-node-method="filterNode"
              :default-expanded-keys="[1]"
              ref="tree"
              node-key="id"
              highlight-current
              @node-click="handleNodeClick"
          >
          </el-tree>
        </div>
      </el-col>
      <!--角色数据-->
      <el-col :span="relaNav ? 19 : 24" :xs="24">
        <!--展开图标-->
        <div class="rightIcon" @click="relaNav = true" v-show="!relaNav">
          <span class="el-icon-caret-right"></span>
        </div>
        <el-row>
          <el-col :span="24" :xs="24">
            <el-row>
              <el-form
                  ref="queryForm"
                  :model="queryParams"
                  size="mini"
                  :inline="true"
                  label-width="68px"
              >
                <el-form-item label="" prop="year">
                  <el-date-picker
                      style="width: 240px"
                      @change="getNumbers"
                      v-model="queryParams.year"
                      type="year"
                     value-format="yyyy"
                  placeholder="年份"
                  >
                  </el-date-picker>
                </el-form-item>
                <el-form-item label="" prop="number">
                  <el-select
                    v-model="queryParams.number"
                    placeholder="期数"
                    clearable
                    style="width: 240px"
                  >
                    <el-option
                      v-for="item in numbers"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                    >
                    </el-option>
                  </el-select>
                </el-form-item>
                <el-form-item>
                  <selectTree
                      :key="'domainId'"
                      style="width: 240px"
                      v-model="queryParams.domainId"
                      :deptType="100" :deptTypeList="[1, 3, 4]"
                      placeholder="管养单位"
                      clearable
                      filterable
                  />
                </el-form-item>
                <el-form-item>
                  <selectTree
                      :key="'constructionUnit'"
                      style="width: 240px"
                      v-model="queryParams.calcDomainId"
                      :dept-type="100"
                      placeholder="施工单位"
                      :filter-keys="['云南省交通投资建设集团有限公司', '云南交投投资有限公司']"
                      :expand-all="false"
                      clearable
                      filterable
                  />
                </el-form-item>
                <el-form-item>
                  <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
                  <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
                  <el-button v-show="!showSearch" @click="showSearch=true" icon="el-icon-arrow-down" circle></el-button>
                  <el-button v-show="showSearch" @click="showSearch=false" icon="el-icon-arrow-up" circle></el-button>
                </el-form-item>
              </el-form>
              <!--默认折叠-->
              <el-col :span="24">
                <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch"
                         label-width="68px">
                  <el-form-item>
                    <RoadSection v-model="queryParams.maiSecId" :deptId="queryParams.domainId" placeholder="路段"/>
                  </el-form-item>
                  <el-form-item>
                    <el-input style="width: 240px" placeholder="计量单名称" v-model="queryParams.name"></el-input>
                  </el-form-item>
                  <el-form-item>
                    <el-input style="width: 240px" placeholder="计量单编码" v-model="queryParams.code"></el-input>
                  </el-form-item>
                  <el-form-item>
                    <el-input style="width: 240px" placeholder="施工单编码" v-model="queryParams.constructionCode"></el-input>
                  </el-form-item>
                  <el-form-item>
                    <el-input style="width: 240px" placeholder="操作人" v-model="queryParams.operator"></el-input>
                  </el-form-item>
                  <el-form-item>
                    <el-date-picker
                        v-model="queryParams.calcDate"
                        style="width: 240px"
                        type="daterange"
                        value-format="yyyy-MM-dd"
                        range-separator="至"
                        start-placeholder="计量时间"
                        end-placeholder="计量时间">
                    </el-date-picker>
                  </el-form-item>
                  <el-form-item>
                    <el-input style="width: 240px" placeholder="中间计量单编码" v-model="queryParams.intermediateCode"></el-input>
                  </el-form-item>
                </el-form>
              </el-col>
            </el-row>
          </el-col>
        </el-row>
        <!--操作按钮区开始-->
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button
                type="primary"
                icon="el-icon-plus"
                size="mini"
                v-has-menu-permi="['calcdaliy:settlecalc:add']"
                @click="handleAdd"
            >新增
            </el-button
            >
          </el-col>
          <el-col :span="1.5">
            <el-button
                type="success"
                icon="el-icon-view"
                size="mini"
                @click="handleOpenOperate"
            >操作意见
            </el-button
            >
          </el-col>
          <el-col :span="1.5"><el-button type="primary" v-has-menu-permi="['calcdaliy:settlecalc:reportpreview']" @click="handlePreview" icon="el-icon-view">报表预览</el-button></el-col>
          <el-col :span="1.5"><el-button type="success" v-has-menu-permi="['calcdaliy:settlecalc:reportpreview']" @click="handleDownload" icon="el-icon-download">报表下载</el-button></el-col>
          <el-col :span="1.5">
            <el-button
                type="warning"
                icon="el-icon-download"
                size="mini"
                v-has-menu-permi="['calcdaliy:settlecalc:export']"
                @click="exportList"
            >导出清单
            </el-button
            >
          </el-col>
          <el-col :span="1.5">
            <el-button
              type="warning"
              icon="el-icon-view"
              size="mini"
              v-has-permi="['settlement:repository:regenerate']"
              @click="handleRegenerate"
            >重新生成报表
            </el-button>
          </el-col>
          <right-toolbar
              :showSearch.sync="showSearch"
              @queryTable="handleQuery"
              :columns="columns"
          ></right-toolbar>
        </el-row>
        <el-row>
          <div class="draggable">
            <el-table v-adjust-table
                size="mini"
                style="width: 100%"
                v-loading="loading"
                border
                :data="tableData"
                row-key="id"
                ref="dataTable"
                stripe
                highlight-current-row
                @row-click="handleClickRow"
                @selection-change="handleSelectionChange"
                :height="
                showSearch ? 'calc(100vh - 330px)' : 'calc(100vh - 270px)'
              "
            >

              <el-table-column type="selection" width="50" align="center"/>
              <el-table-column
                  label="序号"
                  align="center"
                  type="index"
                  width="50"
              />
              <template v-for="(column,index) in columns">
                <el-table-column :label="column.label"
                                 v-if="column.visible"
                                 align="center"
                                 :prop="column.field"
                                 :width="column.width">
                  <template slot-scope="scope">
                    <dict-tag v-if="column.dict" :options="dict.type[column.dict]" :value="scope.row[column.field]"/>
                    <template v-else-if="column.slots">
                      <RenderDom :row="scope.row" :index="index" :render="column.render"/>
                    </template>
                    <span v-else-if="column.isTime">{{ parseTime(scope.row[column.field], '{y}-{m}-{d}') }}</span>
                    <span v-else>{{ scope.row[column.field] }}</span>
                  </template>
                </el-table-column>
              </template>
              <el-table-column
                label="操作"
                fixed="right"
                align="center"
                width="250"
                class-name="small-padding fixed-width"
              >
                <template slot-scope="scope">
                  <el-button
                    icon="el-icon-view"
                    size="mini"
                    type="text" @click="showInfo(scope.row)">查看明细
                  </el-button>
                  <el-button
                      size="mini"
                      type="text"
                      icon="el-icon-delete"
                      v-has-menu-permi="['calcdaliy:settlecalc:remove']"
                      @click="handleDelete(scope.row)"
                      :disabled="scope.row.status != 0"
                  >删除
                  </el-button>
                  <el-button
                      size="mini"
                      type="text"
                      icon="el-icon-edit"
                      v-has-menu-permi="['calcdaliy:settlecalc:edit']"
                      @click="handleEdit (scope.row)"
                      :disabled="scope.row.status != 0"
                  >修改
                  </el-button>
                  <el-dropdown size="mini">
                    <el-button size="mini" type="text" icon="el-icon-d-arrow-right">更多</el-button>
                    <el-dropdown-menu slot="dropdown">
                      <el-dropdown-item><el-button type="text" v-has-menu-permi="['settlecalc:detail:add']" @click="openAddInfo(scope.row)" icon="el-icon-plus" :disabled="scope.row.status != 0">添加明细</el-button></el-dropdown-item>
                      <el-dropdown-item><el-button type="text" v-has-menu-permi="['calcdaliy:settlecalc:process']" @click="handleOpenSubmit(scope.row)" icon="el-icon-check" :disabled="scope.row.status != 0">提交</el-button></el-dropdown-item>
                      <el-dropdown-item><el-button type="text" v-has-menu-permi="['calcdaliy:settlecalc:updateFund']" @click="updateFund(scope.row)" icon="el-icon-refresh-left" :disabled="scope.row.status != 0">更新费用</el-button></el-dropdown-item>
                    </el-dropdown-menu>
                  </el-dropdown>
                </template>
              </el-table-column>
            </el-table>
            <pagination
                v-show="total>0"
                :total="total"
                :page.sync="queryParams.pageNum"
                :limit.sync="queryParams.pageSize"
                @pagination="handleQuery"
            />
          </div>
        </el-row>
      </el-col>
    </el-row>
    <el-dialog title="操作意见" :visible.sync="openOperateInfo" width="80%" destroy-on-close v-if="openOperateInfo">
      <operateInfo @close="modelClose" :businessKey="row.id" :getNodeInfo="getNodeInfo"></operateInfo>
    </el-dialog>
    <el-dialog title="明细查看" :before-close="modelClose" append-to-body modal-append-to-body :visible.sync="infoDialog" width="90%" v-if="infoDialog">
      <info  @close="modelClose" :maiSecId="maiSecId" :rowData="rowData" @pageUpdate="handleQuery"></info>
    </el-dialog>
    <el-dialog title="新增明细" :visible.sync="addInfoDialog" width="80%" v-if="addInfoDialog">
      <add-info  @close="modelClose" :settleId="settleId"></add-info>
    </el-dialog>
    <el-dialog :title="drawerTitle" destroy-on-close :visible.sync="drawer" v-if="drawer" :close-on-click-modal="false" width="80%">
      <detail @close="handleCloseDetail" :row-data="rowData"></detail>
    </el-dialog>
    <el-dialog title="附件列表" :visible.sync="openFile" width="80%" v-if="openFile">
      <file-upload  @close="modelClose" v-model="disFilePath" :forView="true"></file-upload>
    </el-dialog>
    <IFramePreview ref="iframeRef" :srcdoc="preview.html" :down-url="preview.url" :file-name="preview.fileName"></IFramePreview>
    <el-dialog title="确认" :visible.sync="confirmDialog" width="30%" v-if="confirmDialog">
      <el-form v-model="formData" size="small" :inline="true" label-width="120px">
        <el-form-item label="预计结算总费用">
          <el-input v-model="formData.constructionFund" style="width: 230px" readonly/>
        </el-form-item>
        <el-form-item label="监理费">
          <el-input v-model="formData.supFund" style="width: 230px" readonly/>
        </el-form-item>
        <el-form-item label="提交意见">
          <el-input v-model="formData.comment" type="textarea" style="width: 230px"/>
        </el-form-item>
      </el-form>
      <div style="text-align: right">
        <el-button size="mini" @click="confirmDialog = false">取 消</el-button>
        <el-button size="mini" type="primary" @click="handleSubmit">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import Detail from "./detail.vue";
import EventInfo from "../../component/eventTreeInfo.vue";
import selectTree from "@/components/DeptTmpl/selectTree.vue";
import operateInfo from "@/views/dailyMaintenance/component/operateInfo.vue";
import info from "./info.vue"
import addInfo from "./addInfo.vue"
import {
  listSettlecalc,
  deleteSettlecalc,
  submit,
  getNodeInfo,
  processSettle,
  previewSettlecalcReport,
  downloadSettlecalcReport,
  getNumbers,
  updateFund,
  getNumbersByYear
} from "@/api/dailyMaintenance/metering/settlementApplication"
import {findUserDeptMaintenanceList2} from "@/api/system/maintenanceSection";
import RoadSection from "@/views/baseData/components/roadSection/index.vue";
import Luckysheet from "@/components/Luckysheet/index.vue";
import axios from "axios";
import {getToken} from "@/utils/auth";
import IFramePreview from "@/components/IFramePreview/index.vue";
import {regenerateReport} from "@/api/dailyMaintenance/metering/addPrice";
export default {
  name: 'SettlementApplication',
  components: {
    IFramePreview,
    Luckysheet,
    RoadSection,
    operateInfo,
    selectTree,
    EventInfo,
    Detail,
    info,
    addInfo,
    RenderDom: {
      functional: true,
      props: {
        row: Object,
        index: Number,
        render: Function
      },
      render(createElement, ctx) {
        const {row, index} = ctx.props
        return ctx.props.render(row, index)
      }
    }
  },
  props: [],
  dicts: ['calc_daliy_settlement_status'],
  data() {
    return {
      leftTotal: 1,
      showSearch: false,
      queryParams: {},
      total: 0,
      loading: false,
      settleId: '',
      maiSecId: '',
      preview: {
        html: '',
        url: '',
        fileName: ''
      },
      columns: [
        {key: 0, width: 150, field: 'status', label: `状态`, visible: true, dict: 'calc_daliy_settlement_status'},
        {key: 0, width: 100, field: 'numberName', label: `期数`, visible: true},
        {key: 1, width: 200, field: 'name', label: `计量结算单名称`, visible: true},
        {key: 2, width: 200, field: 'code', label: `计量结算单编号`, visible: true},
        {key: 3, width: 200, field: 'preCalcName', label: `上一期名称`, visible: true},
        {key: 4, width: 200, field: 'preCalcCode', label: `上一期编号`, visible: true},
        {key: 5, width: 100, field: 'domainName', label: `管养单位`, visible: true},
        {key: 6, width: 200, field: 'calcDomainName', label: `申请计量单位`, visible: true},
        {key: 7, width: 100, field: 'maiSecId', label: `路段名称`, visible: true},
        {key: 8, width: 200, field: 'conName', label: `合同名称`, visible: true},
        {key: 9, width: 100, field: 'constructionFund', label: `施工总费用(元)`, visible: true},
        {key: 10, width: 100, field: 'sumFund', label: `工程量费用(元)`, visible: true, slots: true, render: (row) => {
            return (
              <span>{row.sumFund?.toFixed(0)}</span>
            );
          }},
        {key: 11, width: 100, field: 'productionFund', label: `安全生产费(元)`, visible: true, slots: true, render: (row) => {
            return (
                <span>{row.productionFund?.toFixed(0)}</span>
            );
          }},
        {key: 12, width: 100, field: 'guaranteeFund', label: `安全保通费(元)`, visible: true, slots: true, render: (row) => {
            return (
                <span>{row.guaranteeFund?.toFixed(0)}</span>
            );
          }},
        {key: 13, width: 100, field: 'adjustFund', label: `调整费用(元)`, visible: true},
        {key: 14, width: 100, field: 'supFund', label: `监理费(元)`, visible: true},
        {key: 15, width: 100, field: 'eductionFund', label: `扣款金额(元)`, visible: true},
        {key: 16, width: 100, field: 'materialAdjustFund', label: `材料调差费用(元)`, visible: true},
        {key: 17, width: 100, field: 'calcDate', label: `计量日期`, visible: true},
        {key: 18, width: 100, field: 'updateTime', label: `操作日期`, visible: true},
        {key: 20, width: 100, field: 'createuser', label: `操作人`, visible: true},
        {key: 21, width: 100, field: 'remark', label: `备注`, visible: true},
        {key: 17, width: 100, field: 'fileId', label: `附件`, visible: true, slots: true, render: (row, index) => {
            return (
                <el-button
                    size="mini"
                    disabled={!row.fileId}
                    type="text" onClick={e => this.handleOpenFile(e, row)}>查看</el-button>
            )
          }},
      ],
      tableData: [],
      rowData: {},
      drawerTitle: '结算计量申请',
      drawer: false,
      openFile: false,
      openOperateInfo: false,
      infoDialog: false,
      addInfoDialog: false,
      disFilePath: '',
      // 左侧组织树
      relaNav: true,
      keyword: '',
      relaOptions: [],
      filteredTreeData: [],
      confirmDialog: false,
      formData: {},
      row: {},
      numbers: [],
      ids: []
    }
  },
  computed: {},
  watch: {},
  created() {
    this.getDeptTree()
    this.handleQuery()
    this.getNumbers()
  },
  mounted() {},
  methods: {
    getNodeInfo,
    // 关键词检索
    handleSearch() {
      const keyword = this.keyword.toLowerCase();
      this.filteredTreeData = this.relaOptions.filter(node => this.filterNode(node, keyword));
    },
    // 筛选节点
    filterNode(node, keyword) {
      if (node.label.indexOf(keyword) != -1) {
        return true;
      }
      if (node.children) {
        return node.children.some(childNode => this.filterNode(childNode, keyword));
      }
      return false;
    },
    // 查询部门下拉树结构
    getDeptTree() {
      findUserDeptMaintenanceList2().then(response => {
        const treeData = response.data
        treeData.forEach(item => {
          getChild(item)
        })

        function getChild(node) {
          node.label = node.deptName || node.maintenanceSectionName
          node.id = node.deptId || node.maintenanceSectionId
          if (node.children) {
            node.children.forEach(item => {
              getChild(item)
            })
          }
        }

        // 增加一个最顶级
        const tree = [
          {
            label: '云南省交通投资建设集团有限公司',
            id: '1',
            children: [...treeData]
          }
        ]
        this.relaOptions = tree
        this.filteredTreeData = [...this.relaOptions]
      });
    },
    handleNodeClick(e) {
      this.queryParams.domainId = ''
      this.queryParams.maiSecId = ''
      if (e.deptId) {
        this.queryParams.domainId = String(e.id)
      } else if (e.departmentId) {
        this.queryParams.domainId = String(e.departmentId)
        this.queryParams.maiSecId = e.id
      }
      this.handleQuery()
    },
    handleQuery() {
      this.loading = true
      if (this.queryParams.calcDate) {
        this.queryParams.startCalcDate = this.queryParams.calcDate[0]
        this.queryParams.endCalcDate = this.queryParams.calcDate[1]
      }
      listSettlecalc(this.queryParams).then(res => {
        this.loading = false
        this.tableData = res.rows
        this.total = res.total
      })
    },
    resetQuery() {
      this.queryParams = {
        pageNum: 1,
        pageSize: 50
      }
      this.handleQuery()
    },
    handleCloseDetail() {
      this.rowData = {}
      this.drawer = false
      this.handleQuery()
    },
    handleEdit(row) {
      this.rowData = row
      this.drawer = true
    },
    handleDelete(row) {
      this.$modal.confirm('是否确认删除').then(() => {
        this.loading = true
        deleteSettlecalc(row.id).then(() => {
          this.$modal.msgSuccess('删除成功')
          this.handleQuery()
        })
      })
    },
    handleClickRow(row) {
      this.row = row
    },
    handleOpenSubmit(row) {
      this.formData = {
        businessKey: row.id,
        taskId: row.taskId,
        approved: true,
        supFund: row.supFund,
        constructionFund: row.constructionFund
      }
      this.confirmDialog = true
    },
    // 提交
    handleSubmit() {
      processSettle(this.formData).then(res => {
        this.$modal.msgSuccess('提交成功')
        this.confirmDialog = false
        this.handleQuery()
      })
    },
    handleOpenOperate() {
      if (!this.row.id) {
        this.$modal.msgError('请选择一条数据')
        return
      }
      this.openOperateInfo = true
    },
    handleOpenFile(e, row) {
      this.disFilePath = row.fileId
      this.openFile = true
    },
    handleAdd() {
      this.drawer = true
      this.rowData = {}
    },
    showInfo(row) {
      this.infoDialog = true
      this.rowData = row
    },
    openAddInfo(row) {
      this.addInfoDialog = true;
      this.maiSecId = row.maiSecId;
      this.settleId = row.id;
    },
    // 报表预览
    handlePreview() {
      if (!this.row.id) {
        this.$modal.msgWarning('请选择一条数据')
        return
      }
      this.loading = true
      previewSettlecalcReport(this.row.id).then(res => {
        if (res.code == 200){
          this.preview.html = res.data.html
          this.preview.url = res.data.downUrl
          this.preview.fileName = res.data.fileName
          this.$refs.iframeRef.visible = true
        }
        this.loading = false
      })
    },
    // 报表下载
    handleDownload() {
      if (!this.row.id) {
        this.$modal.msgWarning('请选择一条数据')
        return
      }
      this.loading = true
      downloadSettlecalcReport(this.row.id).then(res => {
        const url = res.data.downUrl
        const fileName = res.data.fileName
        if (res.code == 200) {
          if (!res.data) {
            this.$message.warning(res.msg)
            this.loading = false
            return
          }
          if (res.data.fileName.endsWith('.zip')) {
            let link = document.createElement('a')
            link.download = res.data.fileName
            link.style.display = 'none'
            link.href = res.data.downUrl
            document.body.appendChild(link)
            link.click()
            document.body.removeChild(link)
            this.loading = false
          } else {
            axios({
              method: "get",
              responseType: 'arraybuffer',
              url: res.data.downUrl,
              headers: {}
            }).then((res) => {
              const arrayBuffer = res.data;
              // 创建一个Blob对象
              const blob = new Blob([arrayBuffer], {type: 'application/zip'}); // 对于.xls文件
              saveAs(blob, res.data.fileName)
            }).finally(() => {
              this.loading = false
            })
          }
        }
      })
    },
    // 导出清单按钮
    exportList() {
      const params = {
        ...this.queryParams,
        settleIds: this.ids.map(item => item.id),
      }
      this.download(
          'manager/settlecalc/export',
          params,
          `settlecalc_${new Date().getTime()}.xlsx`,
        {
          headers: { 'Content-Type': 'application/json;' },
          parameterType: 'body'
        }
      )
    },
    updateFund(row) {
      this.$modal.confirm('是否确定更新费用').then(() => {
        this.loading = true
        updateFund(row.id).then(res => {
          this.handleQuery()
        })
      })
    },
    modelClose() {
      this.drawer = false
      this.openFile = false
      this.openOperateInfo = false
      this.infoDialog = false
      this.addInfoDialog = false
      this.handleQuery()
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection
    },
    handleRegenerate() {
      if (this.ids.length == 0) {
        this.$modal.msgError("请勾选至少一条数据")
        return
      }
      const params = {
        idList: this.ids.map(item => item.id),
        type: 2
      }
      regenerateReport(params).then(res => {
        this.$modal.msgSuccess("操作成功")
      })
    },
    getNumbers() {
      this.numbers = []
      getNumbersByYear({year: this.queryParams.year || null}).then((res) => {
        res.rows.forEach((item) => {
          this.numbers.push({
            label: item.name,
            value: item.id,
          });
        });
      });
    },
  }
}
</script>

<style lang="scss" scoped>
.leftDiv {
  border-right: 1px solid #d8dce5;
  min-height: calc(100vh - 100px);
  overflow-y: auto;
  height: calc(80vh - 150px);
  position: relative;
  top: -20px;
  padding-top: 10px;
  background-color: white;
}

.leftIcon {
  border: 1px solid #dcdfe6;
  border-radius: 8px;
  width: 16px;
  height: 50px;
  line-height: 50px;
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
  position: absolute;
  right: 0;
  top: 300px;
  z-index: 2;
}

.leftIcon:hover {
  background-color: #dcdfe6;
}

.rightIcon {
  border: 1px solid #dcdfe6;
  border-radius: 8px;
  width: 16px;
  height: 50px;
  line-height: 50px;
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
  position: absolute;
  left: -10px;
  top: 280px;
  z-index: 10;
  background: white;
}

.rightIcon:hover {
  background-color: #dcdfe6;
}

.left-total {
  font-size: 13px;
  line-height: 28px;
  color: #606266;
  position: absolute;
  bottom: 10px;
  right: 10px;
}
</style>
<style lang="scss" scoped>
@import "@/assets/styles/business.scss";
</style>
