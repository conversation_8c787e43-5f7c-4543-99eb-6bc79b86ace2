<template>
  <div class="container-view-list">

    <section class="page-direction">
      <div class="page-l">
        <slot name="left" />
      </div>
      <div class="page-r">
        <section class="container-header">
          <slot name="search" />
        </section>

        <section class="container-body">
          <div class="table-header">
            <slot name="header" />
          </div>

          <div class="divider"></div>

          <div class="table-body">
            <slot name="body"></slot>
          </div>
        </section>
      </div>
    </section>

    <slot></slot>
  </div>
</template>

<script>
// 插槽body 里面的table height设为 100%
export default {
  props: {
    layout: {
      type: String,
      default: 'row', // row ,column
    },
  },
};
</script>

<style lang="scss" scoped>
.container-view-list {

  .page-direction {
    display: flex;
    flex: 1;

    .page-l {
      flex: 1;
      background: #ffffff;
      box-shadow: 0px 0px 10px 0px rgba(0, 0, 0, 0.2);
      border-radius: 10px;
      margin-right: 10px;
      flex-shrink: 0;
      min-width: 200px;
      padding: 10px 5px;
    }

    .page-r {
      flex: 5;
      display: flex;
      flex-direction: column;
      overflow-x: auto;
      .container-header {
        background: #ffffff;
        // box-shadow: 0px 0px 10px 0px rgba(0, 0, 0, 0.2);
        border-radius: 10px;
        display: flex;
        flex-direction: column;
        padding: 10px;
      }

      .container-body {
        background: #ffffff;
        box-shadow: 0px 0px 10px -10px rgba(0, 0, 0, 0.2);
        border-radius: 10px;
        margin-top: 10px;
        overflow: hidden;
        flex: 1;
        .table-header {
          padding: 10px 10px 0px 10px;
        }

        .divider {
          width: calc(100% - 20px);
          height: 1px;
          background-color: #e5e6eb;
          margin: 0 auto;
        }

        .table-body {
          margin: 10px;
          height: calc(100% - 120px);

          ::v-deep .el-table {
            flex: 1;
          }
        }
      }
    }
  }
}
</style>
