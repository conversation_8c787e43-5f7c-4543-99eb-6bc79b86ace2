<template>
  <div class="app-container">
    <el-row :gutter="20">

      <el-col :span="24">
        <!--操作按钮区开始-->
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button
                v-if="grade > 2"
                type="primary"
                icon="el-icon-plus"
                size="mini"
                @click="handleAdd"
                v-hasPermi="['patrol:laborpersoninsure:add']"
            >新增</el-button>
          </el-col>
          <el-col :span="1.5" v-if="false">
            <el-button
                v-if="grade > 2"
                type="danger"
                icon="el-icon-delete"
                size="mini"
                :disabled="multiple"
                @click="handleDelete"
                v-hasPermi="['patrol:laborpersoninsure:remove']"
            >删除</el-button>
          </el-col>
          <!-- <right-toolbar :showSearch.sync="showSearch" @queryTable="getList" :columns="columns"></right-toolbar> -->
        </el-row>
        <!--操作按钮区结束-->

        <!--数据表格开始-->
        <div class="tableDiv">
          <el-table v-adjust-table size="mini" style="width: 100%" height="590"
            v-loading="loading" border :data="laborpersoninsureList" @selection-change="handleSelectionChange">
            <el-table-column type="selection" width="50" align="center" />
            <el-table-column fixed label="序号" type="index" width="50">
              <template v-slot="scope">
                {{ (scope.$index + (queryParams.pageNum - 1) * queryParams.pageSize)+1 }}
              </template>
            </el-table-column>
            <el-table-column label="姓名" align="center" prop="userName" />
            <el-table-column label="所属劳务单位" align="center" prop="domainName" />
            <el-table-column label="保险类型" align="center" prop="insureType" />
            <el-table-column label="保险起始时间" align="center" prop="insureStartDate" width="180">
                <template slot-scope="scope">
                    <span>{{ parseTime(scope.row.insureStartDate, '{y}-{m}-{d}') }}</span>
                </template>
            </el-table-column>
            <el-table-column label="保险结束时间" align="center" prop="insureEndDate" width="180">
                <template slot-scope="scope">
                    <span>{{ parseTime(scope.row.insureEndDate, '{y}-{m}-{d}') }}</span>
                </template>
            </el-table-column>
            <el-table-column label="保险单号" align="center" prop="insureCode" />
            <el-table-column label="附件" align="center" prop="attFile">
                <template slot-scope="scope">
                    <div @click="downloadAttFile(scope.row.attFile)" v-if="scope.row.attFile">
                        <el-link type="primary" :underline="false" title="查看">查看</el-link>
                    </div>
                </template>
            </el-table-column>
            <el-table-column
                label="操作"
                fixed="right"
                align="center"
                width="160"
                class-name="small-padding fixed-width"
                v-if="grade > 2"
            >
              <template slot-scope="scope" v-if="scope.row.userId !== 1">
                <el-button
                    size="mini"
                    type="text"
                    icon="el-icon-edit"
                    @click="handleUpdate(scope.row)"
                    v-hasPermi="['patrol:laborpersoninsure:edit']"
                >修改</el-button>
                <el-button
                    size="mini"
                    type="text"
                    icon="el-icon-delete"
                    @click="handleDelete(scope.row)"
                    v-hasPermi="['patrol:laborpersoninsure:remove']"
                >删除</el-button>
              </template>
            </el-table-column>
          </el-table>

          <pagination
              v-show="total>0"
              :total="total"
              :page.sync="queryParams.pageNum"
              :limit.sync="queryParams.pageSize"
              @pagination="getList"
          />
        </div>
        <!--数据表格结束-->
      </el-col>
    </el-row>

    <!-- 新增编辑弹窗 -->
    <el-dialog :title="title" :visible.sync="open" width="700px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="120px">
        <el-row>
            <el-col :span="12">
              <el-form-item label="姓名" :required="true">
                  <el-select v-model="form.userId" filterable placeholder="请选择"
                      :multiple="labisureType === 1" @change="onChangeLabor">
                      <el-option v-for="item in laborPeopleList" :key="item.id" :label="item.name" :value="item.id" />
                  </el-select>
              </el-form-item>
          </el-col>
          <el-col :span="12">
              <el-form-item label="保险类型" prop="insureType">
                  <el-input v-model="form.insureType" placeholder="请输入" />
              </el-form-item>
          </el-col>
        </el-row>
        <el-row>
            <el-col :span="12">
              <el-form-item label="保险起始时间" prop="insureStartDate">
                  <el-date-picker clearable
                                  v-model="form.insureStartDate"
                                  type="date"
                                  value-format="yyyy-MM-dd"
                                  placeholder="请选择保险起始时间">
                  </el-date-picker>
              </el-form-item>
          </el-col>
          <el-col :span="12">
              <el-form-item label="保险结束时间" prop="insureEndDate">
                  <el-date-picker clearable
                                  v-model="form.insureEndDate"
                                  type="date"
                                  value-format="yyyy-MM-dd"
                                  placeholder="请选择保险结束时间">
                  </el-date-picker>
              </el-form-item>
          </el-col>
        </el-row>
        <el-row>
            <el-col :span="12">
              <el-form-item label="保险单号" prop="insureCode">
                  <el-input v-model="form.insureCode" placeholder="请输入保险单号" />
              </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="附件" prop="attFile">
                <file-upload ref="fileUpload" v-model="form.attFile" :limit=1 :ownerId="attFileOwnerId"
                    @input="onAttUploadCompelte"/>
            </el-form-item>
          </el-col>
        </el-row>

      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 用户导入对话框 -->
    <el-dialog :title="upload.title" :visible.sync="upload.open" width="400px" append-to-body>
      <el-upload
          ref="upload"
          :limit="1"
          accept=".xlsx, .xls"
          :headers="upload.headers"
          :action="upload.url + '?updateSupport=' + upload.updateSupport"
          :disabled="upload.isUploading"
          :on-progress="handleFileUploadProgress"
          :on-success="handleFileSuccess"
          :auto-upload="false"
          drag
      >
        <i class="el-icon-upload"></i>
        <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
        <div class="el-upload__tip text-center" slot="tip">
          <div class="el-upload__tip" slot="tip">
            <el-checkbox v-model="upload.updateSupport" /> 是否更新已经存在的用户数据
          </div>
          <span>仅允许导入xls、xlsx格式文件。</span>
          <el-link type="primary" :underline="false" style="font-size:12px;vertical-align: baseline;" @click="importTemplate">下载模板</el-link>
        </div>
      </el-upload>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitFileForm">确 定</el-button>
        <el-button @click="upload.open = false">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
  import { getInsureByPage, listLaborpersoninsure, getLaborpersoninsure, delLaborpersoninsure, addLaborpersoninsure, updateLaborpersoninsure } from "@/api/patrol/laborpersoninsure";
  import { getLaborPersonList } from "@/api/patrol/laborperson";
  import { getToken } from "@/utils/auth";
  import { findFiles } from '@/api/file/index.js'
  import Treeselect from "@riophae/vue-treeselect";
  import "@riophae/vue-treeselect/dist/vue-treeselect.css";

  export default {
    name: "Laborpersoninsure",
    components: { Treeselect },
    props:{
        //类型(0:个人保险，1：团体保险)
        labisureType: {
            type: Number,
            default: 0
        },
        domainId:{
            type:String,
            default:"0"
        },
        grade:{
            type:Number,
            default:-1
        },
        domainIdStr:{
            type:String,
            default:''
        }
    },
    data() {
      return {
        // 遮罩层
        loading: false,
        // 选中数组
        ids: [],
        // 非单个禁用
        single: true,
        // 非多个禁用
        multiple: true,
        // 显示搜索条件
        showSearch: false,
        dictType:[],
        // 总条数
        total: 0,
        // 劳务人员参保情况表格数据
        laborpersoninsureList: null,
        // 弹出层标题
        title: "",
        // 左侧组织树
        deptNav: true,
        // 部门树选项
        deptOptions: undefined,
        // 部门名称
        deptName: undefined,
        //当前点击的树节点
        currentOptionData:undefined,
        // 是否显示弹出层
        open: false,
        //附件的唯一标识
        attFileOwnerId: 0,
        //劳务人员列表
        laborPeopleList: [],
        // 表单参数
        form: {},
        defaultProps: {
          children: "children",
          label: "label"
        },
        //统计数据
        statisticsData:{
          expireCount: 0,
          groupCount: 0,
          notCount: 0,
          personalCount: 0,
          personalShare: 0
        },
        // 用户导入参数
        upload: {
          // 是否显示弹出层（用户导入）
          open: false,
          // 弹出层标题（用户导入）
          title: "",
          // 是否禁用上传
          isUploading: false,
          // 是否更新已经存在的用户数据
          updateSupport: 0,
          // 设置上传的请求头部
          headers: { Authorization: "Bearer " + getToken() },
          // 上传的地址
          url: process.env.VUE_APP_BASE_API + "/system/user/importData"
        },
        // 查询参数
        queryParams: {
          pageNum: 1,
          pageSize: 50,
          labisureType: 0,
          domainIdStr: ""
            // userId: null,
            // labisureType: null,
            // domainId: null,
            // insureType: null,
            // insureStartDate: null,
            // insureEndDate: null,
            // insureCode: null,
            // attFile: null,
            // status: null
        },
        // 列信息
        columns: [
        { key: 0, label: `姓名`, visible: true },
        { key: 1, label: `类型(0:个人保险，1：团体保险)`, visible: true },
        { key: 2, label: `所属单位id`, visible: true },
        { key: 3, label: `保险类型`, visible: true },
        { key: 4, label: `保险起始时间`, visible: true },
        { key: 5, label: `保险结束时间`, visible: true },
        { key: 6, label: `保险单号`, visible: true },
        { key: 7, label: `附件`, visible: true }
        ],
        // 表单校验
        rules: {
          userId: [{ required: true, message: '请选择人员', trigger: 'change' }],
          insureStartDate: [{ required: true, message: '请选择保险起始时间', trigger: 'change' },{ validator: this.validStartEndDate, trigger: 'change' }],
          insureEndDate: [{ required: true, message: '请选择保险结束时间', trigger: 'change' },{ validator: this.validStartEndDate, trigger: 'change' }],
          insureCode: [{ required: true, message: '请输入保险单号', trigger: 'blur' }],
          insureType: [{ required: true, message: '请输入保险类型', trigger: 'blur' }],
          attFile:[{ required: true, message: '请上传附件', trigger: 'change' }],
        }
      };
    },
    watch: {
      domainIdStr: {
        immediate: true,
        handler(newVal){
          this.queryParams.domainIdStr = newVal;
          // console.log("wath-domainIdStr111",newVal);
          // this.handleQuery()
        }
      },
      labisureType:{
        immediate: true,
        handler(newVal) {
          this.queryParams.labisureType = newVal;
          this.form.labisureType = newVal;
          // console.log("wath-labisureType2222",newVal);
          // this.handleQuery()
        }
      }
    },
    created() {
      this.handleQuery();
    },
    computed: {

    },
    methods: {
      /** 查询用户列表 */
      getList() {
        this.loading = true;
        if (this.queryParams.domainIdStr != null && this.queryParams.domainIdStr != '') {
          this.queryParams.domainIds = this.queryParams.domainIdStr.split(',');
        }else {
          this.queryParams.domainIds = [];
        }
        getInsureByPage(this.queryParams, { pageNum: this.queryParams.pageNum, pageSize: this.queryParams.pageSize}).then(response => {
          this.laborpersoninsureList = response.rows;
          this.total = response.total;
          this.loading = false;
        });

      },
      // 取消按钮
      cancel() {
        this.open = false;
        this.reset();
      },
      // 表单重置
      reset() {
        this.form = {
            userId: null,
            domainId: null,
            insureType: null,
            insureStartDate: null,
            insureEndDate: null,
            insureCode: null,
            attFile: null,
            remark: null,
            delFlag: null,
            status: null
        };
        this.resetForm("form");
      },
      /** 搜索按钮操作 */
      handleQuery() {
        this.queryParams.pageNum = 1;
        this.getList();
      },
      /** 重置按钮操作 */
      resetQuery() {
        this.dateRange = [];
        this.resetForm("queryForm");
        this.handleQuery();
      },
      // 多选框选中数据
      handleSelectionChange(selection) {
        this.ids = selection.map(item => item.id);
        this.single = selection.length != 1;
        this.multiple = !selection.length;
      },
      //获取附件的唯一id
      getAttfileOwnerId(){
        this.attFileOwnerId = new Date().getTime();
      },
      /** 新增按钮操作 */
      handleAdd() {
        this.getAttfileOwnerId();
        this.reset();
        this.open = true;
        this.title = "新增";
        getLaborPersonList({domainIds: this.queryParams.domainIdStr.split(',')}).then((res) => {
            this.laborPeopleList = res.data;
        })
      },
      /** 修改按钮操作 */
      handleUpdate(row) {
        this.getAttfileOwnerId();
        this.reset();
        const id = row.id || this.ids;
        getLaborpersoninsure(id).then(response => {
            this.form = response.data;
            if (this.$props.labisureType == 1) {
                this.form.userId = this.form.userId.split(",");
            }
            // console.log("getLaborpersoninsure,",this.form)
            this.open = true;
            this.title = "修改";
            getLaborPersonList({domainIds: this.queryParams.domainIdStr.split(',')}).then((res) => {
                this.laborPeopleList = res.data
            })
        });
      },
      onChangeLabor(val){
        if (this.$props.labisureType === 0) {
            let findLabor = this.laborPeopleList.find(t => t.id === val)
            if (findLabor) {
                this.form.name = findLabor.name
            }
        }
      },
      validStartEndDate(rule, value, callback){
        if (new Date(this.form.insureStartDate).getTime() > new Date(this.form.insureEndDate).getTime()) {
            callback(new Error("保险结束时间应该大于开始日期"))
        }else {
            callback()
        }
      },
      /** 提交按钮 */
      submitForm: function() {
        this.$refs["form"].validate(valid => {
          if (valid) {
            this.form.labisureType = this.$props.labisureType;
            if (this.form.labisureType == 1) {
              this.form.userId = this.form.userId.join(",");
            }
            if (this.form.id != null) {
                updateLaborpersoninsure(this.form).then(response => {
                  this.$modal.msgSuccess("修改成功");
                  this.open = false;
                  this.getList();
              });
            } else {
                this.form.domainId = this.$props.domainIdStr
                addLaborpersoninsure(this.form).then(response => {
                  this.$modal.msgSuccess("新增成功");
                  this.open = false;
                  this.getList();
              });
            }

            this.$emit("getStatistics");
          }
        });
      },
      /** 删除按钮操作 */
      handleDelete(row) {
        const id = row.id || this.ids;
        this.$modal.confirm('是否确认删除劳务人员参保情况编号为"' + id + '"的数据项？').then(function() {
          return delLaborpersoninsure(id);
        }).then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        }).catch(() => {});
      },

      /** 导出按钮操作 */
      handleExport() {
        this.download('manager/laborpersoninsure/export', {
        ...this.queryParams
        }, `laborpersoninsure_${new Date().getTime()}.xlsx`)
      },
      /** 导入按钮操作 */
      handleImport() {
        this.upload.title = "用户导入";
        this.upload.open = true;
      },
      /** 下载模板操作 */
      importTemplate() {
        this.download('manager/laborpersoninsure/importTemplate', {
        }, `劳务人员保险模板.xlsx`)
      },
      // 文件上传中处理
      handleFileUploadProgress(event, file, fileList) {
        this.upload.isUploading = true;
      },
      // 文件上传成功处理
      handleFileSuccess(response, file, fileList) {
        this.upload.open = false;
        this.upload.isUploading = false;
        this.$refs.upload.clearFiles();
        this.$alert("<div style='overflow: auto;overflow-x: hidden;max-height: 70vh;padding: 10px 20px 0;'>" + response.msg + "</div>", "导入结果", { dangerouslyUseHTMLString: true });
        this.getList();
      },
      // 提交上传文件
      submitFileForm() {
        this.$refs.upload.submit();
      },
      //下载附件
      downloadAttFile(ownerId){
        findFiles({ ownerId }).then(res => {
          let file = res.data[0];
          const url = file.url || (file.response && file.response.url)
          if (url) {
            fetch(url)
              .then((response) => response.blob())
              .then((blob) => {
                const link = document.createElement('a')
                link.href = URL.createObjectURL(blob)
                link.download = file.originalFilename || 'download'
                document.body.appendChild(link)
                link.click()
                document.body.removeChild(link)
              })
              .catch(() => {
                this.$message.error('文件无法下载，未找到文件的URL')
              })
          } else {
            this.$message.error('文件无法下载，未找到文件的URL')
          }
        });
      },
      //附件上传完成
      onAttUploadCompelte(val){
        //返回的是一个数组
        //附件存储的是一个数字的字符串，下载需要调findFiles接口获取信息
        this.form.attFile = val[0];
      }
  }
  };
</script>
<style scoped>
  .hasTagsView .app-main[data-v-078753dd]{
    background: #f5f7fa;
  }

  .tableDiv{
    background-color: white;
    padding-bottom: 10px;
  }

  .custom-tree-node{
      display: flex;
      align-items: center;
      width: 100%;
      justify-content: space-between;
      font-size:14px;
  }

  .leftDiv{
    border-right: 1px solid #d8dce5;
    min-height: calc(100vh - 110px);
    overflow-y: auto;
    height: calc(80vh - 110px);
    position: relative;
    top: -20px;
    padding-top: 10px;
    background-color: white;
  }

  .leftIcon{
    border: 1px solid #DCDFE6;
    border-radius: 8px;
    width: 16px;
    height: 50px;
    line-height: 50px;
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
    position: absolute;
    right: 0;
    top: 300px;
    z-index: 2;
  }
  .leftIcon:hover{
    background-color: #DCDFE6;
  }

  .rightIcon{
    border: 1px solid #DCDFE6;
    border-radius: 8px;
    width: 16px;
    height: 50px;
    line-height: 50px;
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
    position: absolute;
    left: -10px;
    top: 280px;
    z-index: 10;
    background: white;
  }
  .rightIcon:hover{
    background-color: #DCDFE6;
  }

  .txt_height{
    height:30px
  }

</style>
