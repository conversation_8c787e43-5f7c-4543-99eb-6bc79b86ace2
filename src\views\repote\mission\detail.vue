<template>
  <el-drawer v-bind="$attrs" v-on="$listeners">
    <div class="container">
      <el-descriptions title="" size="small" :column="4">
        <el-descriptions-item label="任务名称">{{
          data.name
        }}</el-descriptions-item>
        <el-descriptions-item label="发布人">{{
          data.userName
        }}</el-descriptions-item>
        <el-descriptions-item label="发布部门">{{
          data.deptName
        }}</el-descriptions-item>
      </el-descriptions>
      <div style="margin: 20px 8%">
        <el-divider v-if="false">填报内容模板</el-divider>
      </div>

      <el-form :model="queryParams" ref="queryForm" size="small" :inline="true">
      </el-form>
      <!--默认折叠-->

      <!--默认折叠 ,此处仅作为示例-->
      <!--筛选区结束-->

      <!--操作按钮区开始-->
      <el-row :gutter="10" class="mb8">
        <el-col :span="1.5">
          <el-button
            type="primary"
            icon="el-icon-plus"
            size="mini"
            @click="handleAdd"
            >新增
          </el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button
            type="success"
            icon="el-icon-edit"
            size="mini"
            :disabled="single"
            @click="handleUpdate"
            >修改
          </el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button
            type="danger"
            icon="el-icon-delete"
            size="mini"
            :disabled="multiple"
            @click="handleDelete"
            >删除
          </el-button>
        </el-col>
      </el-row>
      <!--操作按钮区结束-->

      <!--数据表格开始-->
      <div class="tableDiv">
        <el-table
          size="mini"
          :height="showSearch ? 'calc(100vh - 320px)' : 'calc(100vh - 240px)'"
          style="width: 100%"
          v-loading="loading"
          border
          :data="detailList"
          @selection-change="handleSelectionChange"
        >
          <el-table-column type="selection" width="50" align="center" />
          <el-table-column
            label="序号"
            type="index"
            width="50"
          ></el-table-column>
          <el-table-column
            label="填报表名称"
            width="220"
            :show-overflow-tooltip="true"
            align="center"
            prop="name"
          />
          <el-table-column
            label="填报要求"
            :show-overflow-tooltip="true"
            align="center"
            prop="request"
          />
          <el-table-column label="模板" align="center" width="200">
            <template slot-scope="scope">
              <!--              <image-preview :owner-id="scope.row.url" :width="40" :height="40" :key="scope.row.id"/>-->
              <FileUpload
                previewWidth="80%"
                for-view
                v-model="scope.row.url"
                :key="scope.row.id"
              />
            </template>
          </el-table-column>
          <el-table-column
            label="过期时间"
            align="center"
            prop="expiry"
            width="150"
          >
            <template slot-scope="scope">
              {{ scope.row.expiry | formatDate('YYYY年MM月DD日') }}
            </template>
          </el-table-column>
          <el-table-column
            label="表头行"
            :show-overflow-tooltip="true"
            align="center"
            prop="headRow"
          />
          <el-table-column
            label="备注"
            align="center"
            prop="status"
          ></el-table-column>
        </el-table>

        <pagination
          v-show="total > 0"
          :total="total"
          :page.sync="queryParams.pageNum"
          :limit.sync="queryParams.pageSize"
          @pagination="getList"
        />
      </div>
      <!--数据表格结束-->

      <!-- 添加或修改督查详情对话框 -->
      <el-dialog
        :title="title"
        :visible.sync="open"
        v-if="open"
        width="50%"
        append-to-body
        destroy-on-close
        :close-on-click-modal="false"
      >
        <el-form ref="form" :model="form" :rules="rules" label-width="120px">
          <el-form-item label="填报表名称" prop="name">
            <el-input v-model="form.name" placeholder="请输入填报表名称" />
          </el-form-item>

          <el-form-item label="填报要求" prop="request">
            <el-input
              v-model="form.request"
              type="textarea"
              :rows="4"
              placeholder="请输入填报要求"
            />
          </el-form-item>

          <el-form-item label="备注" prop="remark">
            <el-input
              v-model="form.remark"
              type="textarea"
              :rows="3"
              placeholder="请输入备注"
            />
          </el-form-item>
          <el-form-item label="填报模板" prop="url">
            <FileUpload
              platform="system"
              previewWidth="80%"
              v-model="form.url"
              :limit="1"
              :fileType="['xlsx']"
              :owner-id="+new Date()"
              @input="(value) => (this.form.url = value[0])"
              :success-hook="tableHeadCheck"
              :excel-options="excelOptions"
            ></FileUpload>
          </el-form-item>
          <el-form-item label="表头所在行" prop="headRow">
            <el-input
              v-model="form.headRow"
              type="number"
              placeholder="表头所在行"
            />
          </el-form-item>
          <el-form-item label="到期时间" prop="expiry">
            <el-date-picker
              v-model="form.expiry"
              align="right"
              type="date"
              placeholder="选择日期"
              :clearable="false"
              :picker-options="pickerOptions"
              value-format="yyyy-MM-dd"
            >
            </el-date-picker>
          </el-form-item>
          <div class="headRow-tip">
            1、上传的“填报模板”表，表头所在行中各表头标识名称不允许重复。
          </div>
          <div class="headRow-tip">
            2、上传完成“填报模板”后，请点击预览，确认表格表头所在行和填写的表头所在行号一致，默认从1开始。
          </div>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </el-dialog>
    </div>
  </el-drawer>
</template>

<script>
import { getToken } from '@/utils/auth';
import {
  addDetail,
  delDetail,
  getDetail,
  listDetail,
  updateDetail,
} from '@/api/supervise/detail';
import ImageUpload from '@/views/patrol/diseases/ImageUpload.vue';
import ImagePreview from '@/views/patrol/diseases/ImagePreview.vue';
import {
  addRepoteForm,
  delRepoteForm,
  getRepoteForm,
  listRepoteForm,
  updateRepoteForm,
  getLastRow,
} from '@/api/repote/repoteForm';
import { delRepoteMission } from '@/api/repote/repoteMission';

export default {
  name: 'MissionDetail',
  components: { ImageUpload, ImagePreview },
  props: {
    data: {
      type: Object,
      default: {},
    },
  },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: false,
      dictType: [],
      // 总条数
      total: 0,
      // 督查详情表格数据
      detailList: null,
      // 弹出层标题
      title: '',
      // 部门树选项
      deptOptions: undefined,
      // 是否显示弹出层
      open: false,

      // 表单参数
      form: {},
      defaultProps: {
        children: 'children',
        label: 'label',
      },
      // 用户导入参数
      upload: {
        // 是否显示弹出层（用户导入）
        open: false,
        // 弹出层标题（用户导入）
        title: '',
        // 是否禁用上传
        isUploading: false,
        // 是否更新已经存在的用户数据
        updateSupport: 0,
        // 设置上传的请求头部
        headers: { Authorization: 'Bearer ' + getToken() },
        // 上传的地址
        url: process.env.VUE_APP_BASE_API + '/system/user/importData',
      },
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 50,
        missionId: null,
        name: null,
        request: null,
        url: null,
        headRow: null,
      },
      // 列信息
      columns: [
        { key: 0, label: `任务ID`, visible: true },
        { key: 1, label: `填报表格名称`, visible: true },
        { key: 2, label: `填报表格要求`, visible: true },
        { key: 3, label: `填报表格备注`, visible: true },
        { key: 4, label: `填报模板地址`, visible: true },
        { key: 5, label: `表头所在行数`, visible: true },
      ],
      // 表单校验
      // 表单校验
      rules: {
        missionId: [
          { required: true, message: '任务ID不能为空', trigger: 'blur' },
        ],
        name: [
          { required: true, message: '任务名称不能为空', trigger: 'blur' },
        ],
        headRow: [
          { required: true, message: '表头行不能为空', trigger: 'blur' },
        ],
      },
      pickerOptions: {
        disabledDate(time) {
          const today = new Date();
          today.setUTCHours(23, 59, 59); // 将今天的时间部分设为0
          return time.getTime() < today.getTime();
        },
        shortcuts: [
          {
            text: '今天',
            onClick(picker) {
              picker.$emit('pick', new Date());
            },
          },
          {
            text: '三天后',
            onClick(picker) {
              const date = new Date();
              date.setTime(date.getTime() + 3 * 3600 * 1000 * 24);
              picker.$emit('pick', date);
            },
          },
          {
            text: '一周后',
            onClick(picker) {
              const date = new Date();
              date.setTime(date.getTime() + 3600 * 1000 * 24 * 7);
              picker.$emit('pick', date);
            },
          },
          {
            text: '一月后',
            onClick(picker) {
              const date = new Date();
              //使用 this.$moment() 需要自然月
              date.setMonth(date.getMonth() + 1);
              picker.$emit('pick', date);
            },
          },
        ],
      },
    };
  },
  computed: {
    /**
     * excelOptions: {
        rowSelect: true,
        rangeSelect: [
          {
            row: [this.form.headRow - 1, this.form.headRow - 1],
            column: [0, 0],
          },
        ],
      },
     */
    excelOptions() {
      return {
        rowSelect: true,
        allowEdit: false,
        rangeSelect: [
          {
            row: [this.form.headRow - 1, this.form.headRow - 1],
            column: [0, 0],
          },
        ],
      };
    },
  },
  watch: {
    data() {
      this.queryParams.missionId = this.data.id;
      this.form.missionId = this.data.id;
      this.getList();
    },
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询用户列表 */
    getList() {
      this.loading = true;
      listRepoteForm(this.queryParams).then((response) => {
        this.detailList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        missionId: this.data.id,
        name: null,
        request: null,
        remark: null,
        url: null,
        headRow: 1,
        expiry: new Date(),
      };
      this.resetForm('form');
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dateRange = [];
      this.resetForm('queryForm');
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id);
      this.single = selection.length != 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = '添加填报表格规范';
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids;
      getRepoteForm(id).then((response) => {
        this.form = response.data;
        this.open = true;
        this.title = '修改添填报表格规范';
      });
    },
    async tableHeadCheck(data) {
      this.form.headRow = 1;

      // 只有当 this.form.name 为空时才设置值
      if (!this.form.name) {
        this.form.name = data.originalFilename.endsWith('.xlsx')
          ? data.originalFilename.slice(0, -5)
          : data.originalFilename;
      }

      if (!data.url) return;

      try {
        const response = await getLastRow(data.url);
        if (response.code === 200) {
          this.form.headRow = response.data === -1 ? 1 : response.data + 1;
        }
      } catch (error) {
        console.error(error);
        this.$modal.msgError('请求失败');
      }
    },
    /** 提交按钮 */
    submitForm: function () {
      this.$refs['form'].validate((valid) => {
        if (valid) {
          if (this.form.expiry) {
            const date = new Date(this.form.expiry);
            date.setUTCHours(23, 59, 59);
            this.form.expiry = date;
          }
          if (this.form.id != null) {
            updateRepoteForm(this.form).then((response) => {
              this.$modal.msgSuccess('修改成功');
              this.open = false;
              this.getList();
            });
          } else {
            addRepoteForm(this.form).then((response) => {
              this.$modal.msgSuccess('新增成功');
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      if (row.id) {
        const id = row.id;
        this.$modal
          .confirm('是否确认删除填报格规范名称为' + row.name + '的数据项？')
          .then(function () {
            return delRepoteForm(id);
          })
          .then(() => {
            this.getList();
            this.$modal.msgSuccess('删除成功');
          });
      } else {
        if (this.ids.length > 0) {
          this.$modal
            .confirm('是否确认删除选中的' + this.ids.length + '条数据项？')
            .then(() => {
              let delArray = [];
              this.ids.forEach((item) => {
                delArray.push(delRepoteForm(item));
              });
              Promise.all(delArray).then(
                () => this.$modal.msgSuccess('删除成功') || this.getList()
              );
            });
        }
      }
    },

    /** 导出按钮操作 */
    handleExport() {
      this.download(
        'supervise/detail/export',
        {
          ...this.queryParams,
        },
        `detail_${new Date().getTime()}.xlsx`
      );
    },
    /** 导入按钮操作 */
    handleImport() {
      this.upload.title = '用户导入';
      this.upload.open = true;
    },
    /** 下载模板操作 */
    importTemplate() {
      this.download('system/user/importTemplate', {}, `user_template.xlsx`);
    },
    // 文件上传中处理
    handleFileUploadProgress(event, file, fileList) {
      this.upload.isUploading = true;
    },
    // 文件上传成功处理
    handleFileSuccess(response, file, fileList) {
      this.upload.open = false;
      this.upload.isUploading = false;
      this.$refs.upload.clearFiles();
      this.$alert(
        "<div style='overflow: auto;overflow-x: hidden;max-height: 70vh;padding: 10px 20px 0;'>" +
          response.msg +
          '</div>',
        '导入结果',
        { dangerouslyUseHTMLString: true }
      );
      this.getList();
    },
    // 提交上传文件
    submitFileForm() {
      this.$refs.upload.submit();
    },
  },
};
</script>
<style scoped lang="scss">
.container {
  margin-top: -20px;
  padding: 20px;
}

::v-deep .el-list-enter-active,
::v-deep .el-list-leave-active {
  transition: all 0s;
}

::v-deep .el-list-enter,
.el-list-leave-active {
  opacity: 0;
  transform: translateY(0);
}

.tableDiv {
  ::v-deep .el-upload-list__item {
    width: 42px;
    height: 42px;
    margin: 0px 5px -6px 0 !important;
  }

  ::v-deep .el-upload--picture-card {
    width: 42px;
    height: 42px;
    margin: 4px 0;
  }

  ::v-deep .el-icon-plus {
    font-size: 1rem;
  }

  ::v-deep .el-upload-list__item-actions {
    font-size: 1rem;
  }

  ::v-deep .el-upload-list__item-actions span + span {
    margin-left: 5px !important;
  }
}

.headRow-tip {
  color: red;
  text-align: left;
  width: 90%;
  margin: 0 auto;
}
</style>
