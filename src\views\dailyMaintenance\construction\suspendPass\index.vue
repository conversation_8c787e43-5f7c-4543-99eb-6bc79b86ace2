<template>
  <div class="app-container maindiv">
    <registration-list ref="regis" :page-list="pageList" :stage="-2">
      <template #btn>
        <el-col :span="1.5">
          <el-button
              icon="el-icon-download"
              size="mini"
              type="warning"
              v-has-menu-permi="['dispose:construction:export']"
              @click="exportList"
          >导出清单
          </el-button
          >
        </el-col>
      </template>
      <template #operate="scope" >
        <el-button
          size="mini"
          type="text"
          icon="el-icon-check"
          v-has-menu-permi="['dispose:construction:againReview']"
          @click="handleAgainReview (scope.scope.row)"
        >重新施工
        </el-button>
      </template>
    </registration-list>
  </div>
</template>
<script>
import registrationList from '../component/registrationList.vue'
import {
  pageList,
  againReview,
} from "@/api/dailyMaintenance/construction/suspend";
export default {
  name: "SuspendPass",
  components: {registrationList},
  data() {
    return {
      drawerTitle: '验收申请',
      drawer: false,
      rowData: {}
    }
  },
  methods: {
    pageList,
    handleRegis(rows) {
      this.rowData = rows
      this.drawer = true
    },
    // 暂不处理
    handleAgainReview(rows) {
      this.$prompt('', '重新施工', {
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        inputPlaceholder: '请输入',
        inputType: 'textarea'
      }).then(({ value }) => {
        rows.remark = value
        againReview(rows).then(res => {
          this.$message.success('操作成功！');
          this.$refs.regis.handleQuery()
        })
      })
    },
    handleCloseDetail() {
      this.rowData = {}
      this.drawer = false
      this.$refs.regis.handleQuery()
    },
    // 导出清单按钮
    exportList() {
      this.$refs.regis.queryParams.year = this.$refs.regis.queryParams.yearStr ? parseInt(this.$refs.regis.queryParams.yearStr) : null

      this.download(
          'manager/dispose/export',
          {...this.$refs.regis.queryParams},
          `dispose_${new Date().getTime()}.xlsx`,
          {
            headers: {'Content-Type': 'application/json;'},
            parameterType: 'body'
          }
      )
    },
  }
}
</script>
