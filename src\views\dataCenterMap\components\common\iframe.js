import Vue from "vue";
import Dialog from "./iframeDialog.vue";

export function iframeDialog({
  iframeUrl = null, // iframeUrl
  componentName = "", // 组件名称
  params = {},
  isBack = true,
  el = null, // 挂载的元素
  title = "",
  msg = "",
} = {}) {
  return new Promise((resolve) => {
    const instance = new Vue({
      el: document.createElement("div"),
      data: {
        visible: true,
        componentName,
      },
      methods: {
        close() {
          this.visible = false;
          document.body.removeChild(this.$el);
        },
        cancel() {
          resolve();
          this.close();
        },
        ok() {
          // const { componentName } = this;
          // if (msg) {
          //   if (!componentName) {
          //     this.$message.error(msg);
          //     return;
          //   }
          // }
          resolve({ componentName });
          this.close();
        },
      },
      render(h) {
        const { visible, componentName } = this;
        return h(Dialog, {
          props: {
            title,
            visible,
            componentName,
            iframeUrl,
            params,
            isBack,
          },
          on: {
            "update:componentName": (v) => (this.componentName = v),
            cancel: this.cancel,
            ok: this.ok,
          },
        });
      },
    });

    document.body.appendChild(instance.$el);
  });
}
