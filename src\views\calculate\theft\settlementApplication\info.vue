<template>
  <div class="road-interflow-edit">
    <el-tabs>
      <el-tab-pane label="中间计量清单">
        <el-row :gutter="15">
          <el-col :span="24">
            <el-form
                ref="queryForm"
                :inline="true"
                :model="queryParams"
                label-width="68px"
                size="mini"
            >
              <el-form-item>
                <el-input
                    v-model="queryParams.intermediateName"
                    placeholder="中间计量单名称"
                    style="width: 190px"
                ></el-input>
              </el-form-item>
              <el-form-item>
                <el-input
                    v-model="queryParams.intermediateCode"
                    placeholder="中间计量单编码"
                    style="width: 190px"
                ></el-input>
              </el-form-item>
              <el-form-item>
                <el-button
                    icon="el-icon-search"
                    size="mini"
                    type="primary"
                    @click="handleQuery1"
                >搜索
                </el-button
                >
                <el-button
                    icon="el-icon-refresh"
                    size="mini"
                    @click="resetQuery"
                >重置
                </el-button
                >
              </el-form-item>
            </el-form>
            <el-row :gutter="10" class="mb8">
              <el-col :span="1.5">
                <el-button
                    icon="el-icon-download"
                    size="mini"
                    type="primary"
                    @click="exportList1"
                >导出清单
                </el-button>
              </el-col>
            </el-row>
          </el-col>
          <el-col :span="24">
            <div class="draggable">
              <el-table v-adjust-table
                  ref="dataTable"
                  v-loading="loading"
                  :data="tableData1"
                  border
                  height="600"
                  highlight-current-row
                  row-key="id"
                  size="mini"
                  stripe
                  style="width: 100%"
              >
                <el-table-column
                    align="center"
                    label="序号"
                    type="index"
                    width="50"
                />
                <template v-for="(column, index) in columns1">
                  <el-table-column
                      v-if="column.visible"
                      :label="column.label"
                      :prop="column.field"
                      align="center"
                      show-overflow-tooltip
                      :width="column.width"
                  >
                    <template slot-scope="scope">
                      <dict-tag
                          v-if="column.dict"
                          :options="dict.type[column.dict]"
                          :value="scope.row[column.field]"
                      />
                      <template v-else-if="column.slots">
                        <RenderDom
                            :index="index"
                            :render="column.render"
                            :row="scope.row"
                        />
                      </template>
                      <span v-else-if="column.isTime">{{
                          parseTime(scope.row[column.field], "{y}-{m}-{d}")
                        }}</span>
                      <span v-else>{{ scope.row[column.field] }}</span>
                    </template>
                  </el-table-column>
                </template>
                <el-table-column
                    align="center"
                    class-name="small-padding fixed-width"
                    fixed="right"
                    label="操作"
                    width="200"
                >
                  <template slot-scope="scope">
                    <el-button
                        icon="el-icon-delete"
                        size="mini"
                        type="text"
                        v-has-menu-permi="['calctheft:detail:remove']"
                        @click="handleDelete(scope.row)"
                    >删除
                    </el-button>
                  </template>
                </el-table-column>
              </el-table>
              <pagination
                  v-show="total1 > 0"
                  :limit.sync="queryParams.pageSize"
                  :page.sync="queryParams.pageNum"
                  :total="total1"
                  @pagination="handleQuery1"
              />
            </div>
          </el-col>
        </el-row>
      </el-tab-pane>
      <el-tab-pane label="任务清单">
        <el-row>
          <el-row>
            <el-col :span="24">
              <el-form
                  ref="queryForm"
                  :inline="true"
                  :model="queryParams"
                  label-width="68px"
                  size="mini"
              >
                <el-form-item>
                  <el-input
                      style="width: 190px"
                      placeholder="任务单名称"
                      v-model="queryParams.constructionName"
                  ></el-input>
                </el-form-item>
                <el-form-item>
                  <el-input
                      style="width: 190px"
                      placeholder="任务单编码"
                      v-model="queryParams.constructionCode"
                  ></el-input>
                </el-form-item>
                <el-form-item>
                  <el-button
                      icon="el-icon-search"
                      size="mini"
                      type="primary"
                      @click="handleQuery2"
                  >搜索
                  </el-button
                  >
                  <el-button
                      icon="el-icon-refresh"
                      size="mini"
                      @click="resetQuery"
                  >重置
                  </el-button
                  >
                </el-form-item>
              </el-form>
            </el-col>
          </el-row>
          <el-row :gutter="10" class="mb8">
            <el-col :span="1.5">
              <el-button icon="el-icon-view" size="mini" type="primary" @click="handleOpenRecord"
              >操作记录
              </el-button>
            </el-col>
            <el-col :span="1.5">
              <el-button
                  icon="el-icon-download"
                  size="mini"
                  type="warning"
                  @click="exportList2"
              >导出清单
              </el-button>
            </el-col>
          </el-row>
          <el-col :span="24">
            <div class="draggable">
              <el-table v-adjust-table
                  ref="dataTable"
                  v-loading="loading"
                  :data="tableData2"
                  border
                  height="600"
                  highlight-current-row
                  row-key="detailId"
                  size="mini"
                  stripe
                  style="width: 100%"
                  @row-click="handleClickRow"
                  @selection-change="handleSelectionChange"
                  @expand-change="loadData"
              >
                <el-table-column type="index" label="序号"></el-table-column>
                <el-table-column type="expand" label="报价清单">
                  <template slot-scope="props">
                    <el-table v-adjust-table
                        :data="props.row.methodList"
                        style="width: 100%"
                        v-loading="methodLoading"
                    >
                      <el-table-column prop="" align="center" label="">
                      </el-table-column>
                      <el-table-column
                          prop="schemeCode"
                          align="center"
                          label="子目号"
                      >
                      </el-table-column>
                      <el-table-column
                          prop="schemeName"
                          align="center"
                          label="养护方法"
                      >
                      </el-table-column>
                      <el-table-column
                          prop="calcDesc"
                          align="center"
                          label="计算式"
                      >
                      </el-table-column>
                      <el-table-column
                          prop="num"
                          align="center"
                          label="方法数量"
                      >
                      </el-table-column>
                      <el-table-column
                          prop="unit"
                          align="center"
                          label="方法单位"
                      >
                      </el-table-column>
                      <el-table-column prop="price" align="center" label="单价">
                      </el-table-column>
                      <el-table-column
                          prop="amount"
                          align="center"
                          label="金额"
                      >
                      </el-table-column>
                    </el-table>
                  </template>
                </el-table-column>
                <el-table-column
                    align="center"
                    label="序号"
                    type="index"
                    width="50"
                />
                <template v-for="(column, index) in columns2">
                  <el-table-column
                      v-if="column.visible"
                      :label="column.label"
                      :prop="column.field"
                      :width="column.width"
                      align="center"
                  >
                    <template slot-scope="scope">
                      <dict-tag
                          v-if="column.dict"
                          :options="dict.type[column.dict]"
                          :value="scope.row[column.field]"
                      />
                      <template v-else-if="column.slots">
                        <RenderDom
                            :index="index"
                            :render="column.render"
                            :row="scope.row"
                        />
                      </template>
                      <span v-else-if="column.isTime">{{
                          parseTime(scope.row[column.field], "{y}-{m}-{d}")
                        }}</span>
                      <span v-else>{{ scope.row[column.field] }}</span>
                    </template>
                  </el-table-column>
                </template>
              </el-table>
              <pagination
                  v-show="total2 > 0"
                  :limit.sync="queryParams.pageSize"
                  :page.sync="queryParams.pageNum"
                  :total="total2"
                  @pagination="handleQuery2"
              />
            </div>
          </el-col>
        </el-row>
      </el-tab-pane>
      <el-tab-pane label="报价清单">
        <el-row :gutter="15">
          <el-col :span="24">
            <el-row :gutter="10" class="mb8">
              <el-col :span="1.5">
                <el-button
                    icon="el-icon-download"
                    size="mini"
                    type="primary"
                    @click="exportList3"
                >导出清单
                </el-button>
              </el-col>
            </el-row>
          </el-col>
          <el-col :span="24">
            <div class="draggable">
              <el-table v-adjust-table
                  ref="dataTable"
                  v-loading="loading"
                  :data="tableData3"
                  border
                  height="600"
                  highlight-current-row
                  row-key="id"
                  size="mini"
                  stripe
                  style="width: 100%"
              >
                <el-table-column
                    align="center"
                    label="序号"
                    type="index"
                    width="50"
                />
                <template v-for="(column, index) in columns3">
                  <el-table-column
                      v-if="column.visible"
                      :label="column.label"
                      :prop="column.field"
                      align="center"
                  >
                    <template slot-scope="scope">
                      <dict-tag
                          v-if="column.dict"
                          :options="dict.type[column.dict]"
                          :value="scope.row[column.field]"
                      />
                      <template v-else-if="column.slots">
                        <RenderDom
                            :index="index"
                            :render="column.render"
                            :row="scope.row"
                        />
                      </template>
                      <span v-else-if="column.isTime">{{
                          parseTime(scope.row[column.field], "{y}-{m}-{d}")
                        }}</span>
                      <span v-else>{{ scope.row[column.field] }}</span>
                    </template>
                  </el-table-column>
                </template>
              </el-table>
              <pagination
                  v-show="total3 > 0"
                  :limit.sync="queryParams.pageSize"
                  :page.sync="queryParams.pageNum"
                  :total="total3"
                  @pagination="handleQuery3"
              />
            </div>
          </el-col>
        </el-row>
      </el-tab-pane>
      <el-tab-pane label="扣款清单">
        <el-row :gutter="15">
          <el-col :span="24">
            <el-row :gutter="10" class="mb8">
              <el-col :span="1.5">
                <el-button
                    icon="el-icon-plus"
                    size="mini"
                    type="primary"
                    v-has-menu-permi="['calctheft:deduction:add']"
                    @click="
                    openDeduction = true;
                    deduction = {};
                  "
                >新增
                </el-button>
              </el-col>
              <el-col :span="1.5">
                <el-button
                    icon="el-icon-download"
                    size="mini"
                    type="primary"
                    @click="exportList4"
                >导出清单
                </el-button>
              </el-col>
            </el-row>
          </el-col>
          <el-col :span="24">
            <div class="draggable">
              <el-table v-adjust-table
                  ref="dataTable"
                  v-loading="loading"
                  :data="tableData4"
                  border
                  height="600"
                  highlight-current-row
                  row-key="id"
                  size="mini"
                  stripe
                  style="width: 100%"
              >
                <el-table-column
                    align="center"
                    label="序号"
                    type="index"
                    width="50"
                />
                <template v-for="(column, index) in columns4">
                  <el-table-column
                      v-if="column.visible"
                      :label="column.label"
                      :prop="column.field"
                      align="center"
                  >
                    <template slot-scope="scope">
                      <dict-tag
                          v-if="column.dict"
                          :options="dict.type[column.dict]"
                          :value="scope.row[column.field]"
                      />
                      <template v-else-if="column.slots">
                        <RenderDom
                            :index="index"
                            :render="column.render"
                            :row="scope.row"
                        />
                      </template>
                      <span v-else-if="column.isTime">{{
                          parseTime(scope.row[column.field], "{y}-{m}-{d}")
                        }}</span>
                      <span v-else>{{ scope.row[column.field] }}</span>
                    </template>
                  </el-table-column>
                </template>
              </el-table>
              <pagination
                  v-show="total4 > 0"
                  :limit.sync="queryParams.pageSize"
                  :page.sync="queryParams.pageNum"
                  :total="total4"
                  @pagination="handleQuery4"
              />
            </div>
          </el-col>
        </el-row>
      </el-tab-pane>
      <el-tab-pane label="计量调整">
        <el-row :gutter="15">
          <el-col :span="24">
            <el-form
                ref="queryForm"
                :inline="true"
                :model="queryParams"
                label-width="68px"
                size="mini"
            >
              <el-form-item>
                <cost-select :type="35"
                    v-model="queryParams.dataType"
                    placeholder="费用类型"
                    style="width: 190px"
                ></cost-select>
              </el-form-item>
              <el-form-item>
                <el-input
                    v-model="queryParams.schemeCode"
                    placeholder="子目号"
                    style="width: 190px"
                ></el-input>
              </el-form-item>
              <el-form-item>
                <el-input
                    v-model="queryParams.schemeName"
                    placeholder="子目名称"
                    style="width: 190px"
                ></el-input>
              </el-form-item>
              <el-form-item>
                <el-button
                    icon="el-icon-search"
                    size="mini"
                    type="primary"
                    @click="handleQuery5"
                >搜索
                </el-button
                >
                <el-button
                    icon="el-icon-refresh"
                    size="mini"
                    @click="resetQuery"
                >重置
                </el-button
                >
              </el-form-item>
            </el-form>
          </el-col>
          <el-col :span="24">
            <el-row :gutter="10" class="mb8">
              <el-col :span="1.5">
                <el-button
                    icon="el-icon-plus"
                    size="mini"
                    type="primary"
                    v-has-menu-permi="['calctheft:fundadjust:add']"
                    @click="
                    openCost = true;
                    cost = {};
                  "
                >新增
                </el-button>
              </el-col>
            </el-row>
          </el-col>
          <el-col :span="24">
            <div class="draggable">
              <el-table v-adjust-table
                  ref="dataTable"
                  v-loading="loading"
                  :data="tableData5"
                  border
                  height="600"
                  highlight-current-row
                  row-key="id"
                  size="mini"
                  stripe
                  style="width: 100%"
              >
                <el-table-column
                    align="center"
                    label="序号"
                    type="index"
                    width="50"
                />
                <template v-for="(column, index) in columns5">
                  <el-table-column
                      v-if="column.visible"
                      :fixed="column.fixed"
                      :label="column.label"
                      :prop="column.field"
                      :width="column.width"
                      align="center"
                  >
                    <template slot-scope="scope">
                      <dict-tag
                          v-if="column.dict"
                          :options="dict.type[column.dict]"
                          :value="scope.row[column.field]"
                      />
                      <template v-else-if="column.slots">
                        <RenderDom
                            :index="index"
                            :render="column.render"
                            :row="scope.row"
                        />
                      </template>
                      <span v-else-if="column.isTime">{{
                          parseTime(scope.row[column.field], "{y}-{m}-{d}")
                        }}</span>
                      <span v-else>{{ scope.row[column.field] }}</span>
                    </template>
                  </el-table-column>
                </template>
              </el-table>
              <pagination
                  v-show="total5 > 0"
                  :limit.sync="queryParams.pageSize"
                  :page.sync="queryParams.pageNum"
                  :total="total5"
                  @pagination="handleQuery5"
              />
            </div>
          </el-col>
        </el-row>
      </el-tab-pane>
      <el-tab-pane label="材料调差">
        <el-row :gutter="15">
          <el-col :span="24">
            <el-form
                ref="queryForm"
                :inline="true"
                :model="queryParams"
                label-width="68px"
                size="mini"
            >
              <el-form-item>
                <cost-select :type="35"
                    v-model="queryParams.dataType"
                    placeholder="费用类型"
                    style="width: 190px"
                ></cost-select>
              </el-form-item>
              <el-form-item>
                <el-input
                    v-model="queryParams.schemeCode"
                    placeholder="子目号"
                    style="width: 190px"
                ></el-input>
              </el-form-item>
              <el-form-item>
                <el-input
                    v-model="queryParams.schemeName"
                    placeholder="子目名称"
                    style="width: 190px"
                ></el-input>
              </el-form-item>
              <el-form-item>
                <el-button
                    icon="el-icon-search"
                    size="mini"
                    type="primary"
                    @click="handleQuery6"
                >搜索
                </el-button
                >
                <el-button
                    icon="el-icon-refresh"
                    size="mini"
                    @click="resetQuery"
                >重置
                </el-button
                >
              </el-form-item>
            </el-form>
          </el-col>
          <el-col :span="24">
            <el-row :gutter="10" class="mb8">
              <el-col :span="1.5">
                <el-button
                    icon="el-icon-plus"
                    size="mini"
                    type="primary"
                    v-has-menu-permi="['calctheft:materialadjust:add']"
                    @click="(openMaterial = true), (material = {})"
                >新增
                </el-button>
              </el-col>
            </el-row>
          </el-col>
          <el-col :span="24">
            <div class="draggable">
              <el-table v-adjust-table
                  ref="dataTable"
                  v-loading="loading"
                  :data="tableData6"
                  border
                  height="600"
                  highlight-current-row
                  row-key="id"
                  size="mini"
                  stripe
                  style="width: 100%"
              >
                <el-table-column
                    align="center"
                    label="序号"
                    type="index"
                    width="50"
                />
                <template v-for="(column, index) in columns6">
                  <el-table-column
                      v-if="column.visible"
                      :fixed="column.fixed"
                      :label="column.label"
                      :prop="column.field"
                      :width="column.width"
                      align="center"
                  >
                    <template slot-scope="scope">
                      <dict-tag
                          v-if="column.dict"
                          :options="dict.type[column.dict]"
                          :value="scope.row[column.field]"
                      />
                      <template v-else-if="column.slots">
                        <RenderDom
                            :index="index"
                            :render="column.render"
                            :row="scope.row"
                        />
                      </template>
                      <span v-else-if="column.isTime">{{
                          parseTime(scope.row[column.field], "{y}-{m}-{d}")
                        }}</span>
                      <span v-else>{{ scope.row[column.field] }}</span>
                    </template>
                  </el-table-column>
                </template>
              </el-table>
              <pagination
                  v-show="total6 > 0"
                  :limit.sync="queryParams.pageSize"
                  :page.sync="queryParams.pageNum"
                  :total="total6"
                  @pagination="handleQuery6"
              />
            </div>
          </el-col>
        </el-row>
      </el-tab-pane>
    </el-tabs>
    <el-dialog
        v-if="openDeduction"
        :visible.sync="openDeduction"
        append-to-body
        modal-append-to-body
        title="扣款信息"
        width="500px"
    >
      <add-deduction
          :row-data="deduction"
          :settleId="rowData.id"
          @close="modelClose"
      ></add-deduction>
    </el-dialog>
    <el-dialog
      v-if="openCost"
      :visible.sync="openCost"
      append-to-body
      modal-append-to-body
      title="计量调整"
      width="800px"
    >
      <add-cost
        :settle-data="rowData"
        :row-data="cost"
        :settleId="rowData.id"
        @close="modelClose"
        :con-id="rowData.conId"
      ></add-cost>
    </el-dialog>
    <el-dialog
        v-if="openMaterial"
        :visible.sync="openMaterial"
        append-to-body
        modal-append-to-body
        title="材料调整"
        width="500px"
    >
      <add-material
          :row-data="material"
          :prnt-data="rowData"
          @close="modelClose"
      ></add-material>
    </el-dialog>
    <el-dialog
      title="操作记录"
      destroy-on-close
      :visible.sync="dialogVisible"
      v-if="dialogVisible"
      modal-append-to-body
      append-to-body
      width="90%"
    >
      <reg-record :id="clickRow.finishedId"/>
    </el-dialog>
  </div>
</template>
<script>
import {
  middleListBySid,
  deleteMiddle,
  listEvent,
  listScheme,
  fetchDeductionList,
  deleteDeduction,
  fetchFundAdjustList,
  deleteFundAdjust,
  fetchMaterialAdjustList,
  deleteMaterialAdjust
} from "@/api/calculate/theft/settlementApplication";
import {listEventBySettleId} from "@/api/calculate/theft/middleApplication";
import AddDeduction from "./addDeduction.vue";
import AddCost from "./addCost.vue";
import AddMaterial from "./addMaterial.vue";
import RoadSection from "@/views/baseData/components/roadSection/index.vue";
import RouteCodeSection from "@/components/RouteCodeSection/index.vue";
import CostSelect from "@/components/CostSelect/index.vue";
import RegRecord from "@/views/calculate/theft/component/regRecord.vue";

export default {
  components: {
    RegRecord,
    CostSelect,
    RouteCodeSection,
    RoadSection,
    AddDeduction,
    AddCost,
    AddMaterial,
    RenderDom: {
      functional: true,
      props: {
        row: Object,
        index: Number,
        render: Function,
      },
      render(createElement, ctx) {
        const {row, index} = ctx.props;
        return ctx.props.render(row, index);
      },
    },
  },
  dicts: [
    "route_direction",
    "lane",
    "sys_asset_type",
    "deduction_type",
    "adjustment_type",
    "theft_m_type",
    'theft_m_type',
    "bridge_simple_bool", "project_clac_middle_status", "testing_calc_status", 'settlement_status'
  ],
  data() {
    return {
      queryParams: {
        pageNum: 1,
        pageSize: 10,
      },
      loading: false,
      tableData1: [],
      total1: 0,
      columns1: [
        {
          key: 0,
          width: 120,
          field: "name",
          label: `中间计量名称`,
          visible: true,
        },
        {
          key: 1,
          width: 120,
          field: "code",
          label: `中间计量编号`,
          visible: true,
        },
        {
          key: 2,
          width: 100,
          field: "domainName",
          label: `管养单位`,
          visible: true,
        },
        {
          key: 3,
          width: 120,
          field: "calcDomainName",
          label: `申请计量单位`,
          visible: true,
        },
        {
          key: 4,
          width: 100,
          field: "maiSecId",
          label: `路段名称`,
          visible: true,
        },
        {
          key: 5,
          width: 100,
          field: "conName",
          label: `合同名称`,
          visible: true,
        },
        {
          key: 6,
          width: 100,
          field: "sumFund",
          label: `基本费用`,
          visible: true,
        },
        {
          key: 7,
          width: 120,
          field: "productionFund",
          label: `安全生产费(元)`,
          visible: true,
        },
        {
          key: 8,
          width: 120,
          field: "guaranteeFund",
          label: `安全保通费(元)`,
          visible: true,
        },
        {
          key: 9,
          width: 100,
          field: "supFund",
          label: `监理费(元)`,
          visible: true,
        },
        {
          key: 31,
          width: 100,
          field: "designFund",
          label: `设计费(元)`,
          visible: true,
        },
        {
          key: 10,
          width: 100,
          field: "calcDate",
          label: `计量日期`,
          visible: true,
        },
        {
          key: 14,
          width: 100,
          field: "status",
          label: `状态`,
          visible: true,
          dict: "project_clac_middle_status",
        },
      ],
      tableData2: [],
      total2: 0,
      columns2: [
        {
          key: 0,
          width: 100,
          field: "status",
          label: `状态`,
          visible: true,
          dict: "settlement_status",
        },
        {
          key: 1,
          width: 100,
          field: "calcStatus",
          label: `计量情况`,
          visible: true,
          dict: "testing_calc_status",
        },
        {
          key: 9,
          width: 100,
          field: "sumFund",
          label: `金额`,
          visible: true,
        },
        {
          key: 5,
          width: 100,
          field: "residueFund",
          label: `剩余金额`,
          visible: true,
        },
        {
          key: 9,
          width: 100,
          field: "productionFund",
          label: `安全生产费`,
          visible: true,
        },
        {
          key: 10,
          width: 100,
          field: "guaranteeFund",
          label: `安全保通费`,
          visible: true,
        },
        {
          key: 11,
          width: 100,
          field: "supFund",
          label: `监理费`,
          visible: true,
        },

        {
          key: 2,
          field: "projectName",
          label: `项目名称`,
          visible: true,
        },
        {
          key: 1,
          width: 100,
          field: "constructionName",
          label: `任务单名称`,
          visible: true,
        },
        {
          key: 2,
          width: 100,
          field: "constructionCode",
          label: `任务单编号`,
          visible: true,
        },
        {
          key: 4,
          field: "domainName",
          label: `管养单位`,
          visible: true,
        },
        {
          key: 10,
          width: 100,
          field: "isGuarantee",
          label: `是否计算安全保通费`,
          visible: true,
          dict: 'bridge_simple_bool'
        },
        {
          key: 11,
          width: 100,
          field: "isProduction",
          label: `是否计算安全生产费`,
          visible: true,
          dict: 'bridge_simple_bool'
        },
        {
          key: 12,
          width: 100,
          field: "conDomainName",
          label: `施工单位`,
          visible: true,
        },
        {
          key: 12,
          width: 100,
          field: "supDomainName",
          label: `监理单位`,
          visible: true,
        },
        {
          key: 14,
          width: 100,
          field: "endTime",
          label: `完工日期`,
          visible: true,
        },
        {
          key: 12,
          width: 100,
          field: "defLiaPer",
          label: `缺陷责任期(月)`,
          visible: true,
        },
      ],
      tableData3: [],
      total3: 0,
      columns3: [
        {key: 0, field: "schemeCode", label: `子目号`, visible: true},
        {key: 1, field: "schemeName", label: `子目名称`, visible: true},
        {key: 2, field: "price", label: `单价`, visible: true},
        {key: 4, field: "num", label: `数量`, visible: true},
        {key: 5, field: "amount", label: `金额`, visible: true},
        {key: 5, field: "remark", label: `备注`, visible: true},
      ],
      tableData4: [],
      total4: 0,
      columns4: [
        {
          key: 1,
          width: 100,
          field: "mtype",
          label: `扣款类型`,
          visible: true,
          dict: "deduction_type",
        },
        {key: 2, width: 100, field: "fund", label: `扣款金额`, visible: true},
        {
          key: 3,
          width: 100,
          field: "isSupFund",
          label: `是否计算监理费`,
          visible: true,
          slots: true,
          render: (row, index) => {
            if (row.isSupFund == 0) {
              return <div>不计算</div>;
            } else {
              return <div>计算</div>;
            }
          },
        },
        {
          key: 4,
          width: 100,
          field: "remark",
          label: `扣款描述`,
          visible: true,
        },
        {
          key: 5,
          width: 100,
          field: "fileId",
          label: `操作`,
          visible: true,
          fixed: "right",
          slots: true,
          render: (row, index) => {
            return (
                <div>
                  <el-button
                      size="mini"
                      type="text"
                      v-has-menu-permi={['calctheft:deduction:edit']}
                      onClick={(e) => this.handleOpenEdit(row, "4")}
                  >
                    编辑
                  </el-button>
                  <el-button
                      size="mini"
                      type="text"
                      v-has-menu-permi={['calctheft:deduction:remove']}
                      onClick={(e) => this.handleDeleteOther(row, "4")}
                  >
                    删除
                  </el-button>
                </div>
            );
          },
        },
      ],
      tableData5: [],
      total5: 0,
      columns5: [
        {
          key: 0,
          width: 100,
          field: "adjustmentType",
          label: `调整类型`,
          visible: true,
          dict: "adjustment_type",
        },
        {
          key: 1,
          width: 100,
          field: "dataType",
          label: `费用类型`,
          visible: true,
          dict: "theft_m_type",
        },
        {
          key: 2,
          width: 100,
          field: "schemeCode",
          label: `子目号`,
          visible: true,
        },
        {
          key: 3,
          width: 100,
          field: "schemeName",
          label: `子目名称`,
          visible: true,
        },
        {key: 4, width: 100, field: "price", label: `单价`, visible: true},
        {key: 5, width: 100, field: "unit", label: `单位`, visible: true},
        {key: 6, width: 100, field: "num", label: `数量`, visible: true},
        {key: 7, width: 100, field: "amount", label: `金额`, visible: true},
        {
          key: 8,
          width: 100,
          field: 'isSupFund',
          label: `是否计量监理费`,
          visible: true,
          slots: true,
          render: (row, index) => {
            if (row.isSupFund == 0) {
              return <div>不计算</div>
            } else {
              return <div>计算</div>
            }
          }
        },
        {key: 9, width: 100, field: 'supDomainName', label: `监理单位`, visible: true},
        {key: 10, width: 100, field: "remark", label: `备注`, visible: true},
        {
          key: 11,
          width: 100,
          field: "createuser",
          label: `操作人`,
          visible: true,
        },
        {
          key: 12,
          width: 100,
          field: "createTime",
          label: `操作时间`,
          visible: true,
        },
        {
          key: 13,
          width: 150,
          field: "fileId",
          label: `操作`,
          visible: true,
          fixed: "right",
          slots: true,
          render: (row, index) => {
            return (
                <div>
                  <el-button
                      size="mini"
                      type="text"
                      v-has-menu-permi={['calctheft:fundadjust:edit']}
                      onClick={(e) => this.handleOpenEdit(row, "5")}
                  >
                    编辑
                  </el-button>
                  <el-button
                      size="mini"
                      type="text"
                      v-has-menu-permi={['calctheft:fundadjust:remove']}
                      onClick={(e) => this.handleDeleteOther(row, "5")}
                  >
                    删除
                  </el-button>
                </div>
            );
          },
        },
      ],
      tableData6: [],
      total6: 0,
      columns6: [
        {
          key: 0,
          width: 100,
          field: "dataType",
          label: `费用类型`,
          visible: true,
          dict: "theft_m_type",
        },
        {
          key: 1,
          width: 100,
          field: "projName",
          label: `调差项目`,
          visible: true,
        },
        {
          key: 2,
          width: 100,
          field: "schemeCode",
          label: `子目号`,
          visible: true,
        },
        {
          key: 3,
          width: 100,
          field: "schemeName",
          label: `子目名称`,
          visible: true,
        },
        {key: 4, width: 100, field: "price", label: `单价`, visible: true},
        {key: 5, width: 100, field: "unit", label: `单位`, visible: true},
        {
          key: 6,
          width: 100,
          field: "calcDesc",
          label: `计算式`,
          visible: true,
        },
        {key: 7, width: 100, field: "num", label: `数量`, visible: true},
        {key: 8, width: 100, field: "amount", label: `金额`, visible: true},
        {key: 9, width: 100, field: "remark", label: `备注`, visible: true},
        {
          key: 10,
          width: 150,
          field: "fileId",
          label: `操作`,
          visible: true,
          fixed: "right",
          slots: true,
          render: (row, index) => {
            return (
                <div>
                  <el-button
                      size="mini"
                      type="text"
                      v-has-menu-permi={['calctheft:materialadjust:edit']}
                      onClick={(e) => this.handleOpenEdit(row, "6")}
                  >
                    编辑
                  </el-button>
                  <el-button
                      size="mini"
                      type="text"
                      v-has-menu-permi={['calctheft:materialadjust:remove']}
                      onClick={(e) => this.handleDeleteOther(row, "6")}
                  >
                    删除
                  </el-button>
                </div>
            );
          },
        },
      ],
      openDeduction: false,
      deduction: {},
      openCost: false,
      cost: {},
      openMaterial: false,
      material: {},
      dialogVisible: false,
      clickRow: {}
    };
  },
  props: {
    rowData: {
      type: Object,
      default: () => {
      },
    },
  },
  created() {
    this.handleQuery1();
    this.handleQuery2();
    this.handleQuery3();
    this.handleQuery4();
    this.handleQuery5();
    this.handleQuery6();
  },
  methods: {
    handleQuery1() {
      this.loading = true;
      middleListBySid({settleId: this.rowData.id}).then((res) => {
        this.tableData1 = res.rows;
        this.total1 = res.total;
        this.loading = false;
      });
    },
    handleQuery2() {
      this.loading = true;
      this.queryParams.settleCalcId = this.rowData.id;
      listEvent(this.queryParams).then((res) => {
        this.tableData2 = res.rows;
        this.total2 = res.total;
        this.loading = false;
      });
    },
    handleQuery3() {
      this.loading = true;
      this.queryParams.settleCalcId = this.rowData.id;
      listScheme(this.queryParams).then((res) => {
        this.tableData3 = res.rows;
        this.total3 = res.total;
        this.loading = false;
      });
    },
    handleQuery4() {
      this.loading = true;
      this.queryParams.settleId = this.rowData.id;
      fetchDeductionList(this.queryParams).then((res) => {
        this.tableData4 = res.rows;
        this.total4 = res.total;
        this.loading = false;
      });
    },
    handleQuery5() {
      this.loading = true;
      this.queryParams.settleId = this.rowData.id;
      fetchFundAdjustList(this.queryParams).then((res) => {
        this.tableData5 = res.rows;
        this.total5 = res.total;
        this.loading = false;
      });
    },
    handleQuery6() {
      this.loading = true;
      this.queryParams.settleCalcId = this.rowData.id;
      fetchMaterialAdjustList(this.queryParams).then((res) => {
        this.tableData6 = res.rows;
        this.total6 = res.total;
        this.loading = false;
      });
    },
    handleDelete(row) {
      this.$modal.confirm("是否确认删除").then(() => {
        deleteMiddle(row.detailId).then(() => {
          this.$modal.msgSuccess("删除成功");
          this.handleQuery1();
          this.$emit('pageUpdate')
        });
      });
    },
    handleClickRow(e) {
      this.clickRow = e
      e.isSelected = !e.isSelected;
      this.$refs.dataTable.toggleRowSelection(e);
    },
    handleOpenRecord() {
      if (!this.clickRow.detailId) {
        this.$message.error("请选择一条数据");
        return;
      }
      this.dialogVisible = true
    },
    exportList1() {
      this.download(
          "manager/calc/theft/settlecalc/detail/export",
          {settleId: this.rowData.id},
          `settlecalc_detail_${new Date().getTime()}.xlsx`,
          {
            headers: {"Content-Type": "application/json;"},
            parameterType: "body",
          }
      );
    },
    exportList2() {
      this.download(
          "manager/calc/theft/settlecalc/detail/export/visa",
          {...this.queryParams},
          `intermediate_detail_${new Date().getTime()}.xlsx`,
          {
            headers: {"Content-Type": "application/json;"},
            parameterType: "body",
          }
      );
    },
    exportList3() {
      this.download(
          "manager/calc/theft/settle/method/export/data",
          {...this.queryParams},
          `method_settlecalc_${new Date().getTime()}.xlsx`,
          {
            headers: {"Content-Type": "application/json;"},
            parameterType: "body",
          }
      );
    },
    exportList4() {
      this.download(
          "manager/calc/theft/deduction/export",
          {...this.queryParams},
          `method_deduction_${new Date().getTime()}.xlsx`,
          {
            headers: {"Content-Type": "application/json;"},
            parameterType: "body",
          }
      );
    },
    // 选中
    handleSelectionChange(e) {
      this.selectIds = e.map((item) => item.detailId);
    },
    loadData(row) {
      this.methodLoading = true;
      listEventBySettleId({
        settleId: row.settleId,
        detailId: row.detailId,
      }).then((res) => {
        this.$set(row, "methodList", res.rows);
        this.methodLoading = false;
      });
    },
    resetQuery() {
      this.queryParams = {
        pageNum: 1,
        pageSize: 10,
      };
    },
    modelClose(index) {
      this.openDeduction = false;
      this.openCost = false;
      this.openMaterial = false;
      switch (index) {
        case "4":
          this.handleQuery4();
          break;
        case "5":
          this.handleQuery5();
          break;
        case "6":
          this.handleQuery6();
          break;
      }
      this.$emit('pageUpdate')
    },
    handleDeleteOther(row, index) {
      this.$modal.confirm("是否确认删除").then(() => {
        switch (index) {
          case "4":
            deleteDeduction(row.id).then(() => {
              this.$modal.msgSuccess("删除成功");
              this.handleQuery4();
            });
            break;
          case "5":
            deleteFundAdjust(row.id).then(() => {
              this.$modal.msgSuccess("删除成功");
              this.handleQuery5();
            });
            break;
          case "6":
            deleteMaterialAdjust(row.id).then(() => {
              this.$modal.msgSuccess("删除成功");
              this.handleQuery6();
            });
            break;
        }
        this.$emit('pageUpdate')
      });
    },
    handleOpenEdit(row, index) {
      switch (index) {
        case "4":
          this.deduction = row;
          this.openDeduction = true;
          break;
        case "5":
          this.cost = row;
          this.openCost = true;
          this.handleQuery5();
          break;
        case "6":
          this.material = row;
          this.openMaterial = true;
          this.handleQuery6();
          break;
      }
    },
  },
};
</script>
<style lang="scss" scoped></style>
<style lang="scss" scoped>
@import "@/assets/styles/business.scss";
</style>
