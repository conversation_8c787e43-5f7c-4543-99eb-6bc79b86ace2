<template>
  <div class="app-container maindiv">
    <el-row :gutter="20" style="display: flex;">
      <!--部门数据-->
      <maintenance-tree
        @rowClick="handleNodeClick"
        :realNav="realNav"
        @closeNav="realNav = false"
      ></maintenance-tree>
      <!--角色数据-->
      <el-col :span="realNav ? 19 : 24" :xs="24">
        <!--展开图标-->
        <div v-show="!realNav" class="rightIcon" @click="realNav = true">
          <span class="el-icon-caret-right"></span>
        </div>
        <el-row>
          <el-col :span="24" :xs="24">
            <el-row>
              <el-form
                ref="queryForm"
                :model="queryParams"
                size="mini"
                :inline="true"
                label-width="68px"
              >
                <el-form-item label="" prop="disType">
                  <el-date-picker
                    style="width: 240px"
                    v-model="queryParams.year"
                    type="year"
                    value-format="yyyy"
                    placeholder="年份"
                  >
                  </el-date-picker>
                </el-form-item>
                <el-form-item>
                  <dict-select type="testing_intermediate_status" style="width: 240px" placeholder="计量状态" v-model="queryParams.status"></dict-select>
                </el-form-item>
                <el-form-item>
                  <selectTree
                    :key="'domainId'"
                    style="width: 240px"
                    v-model="queryParams.domainId"
                    :deptType="100"
                    :deptTypeList="[1, 3, 4]"
                    placeholder="管养单位"
                    clearable
                    filterable
                  />
                </el-form-item>
                <el-form-item>
                  <selectTree
                    :key="'constructionUnit'"
                    v-model="queryParams.calcDomainId" :data-rule="false"
                    clearable
                    filterable
                    :dept-type="100"
                    :filter-keys="['云南省交通投资建设集团有限公司', '云南交投投资有限公司']"
                    :expand-all="false"
                    placeholder="施工单位"
                    style="width: 240px"
                  />
                </el-form-item>
                <el-form-item>
                  <RoadSection
                    v-model="queryParams.maiSecId"
                    :deptId="queryParams.domainId"
                    placeholder="路段"
                  />
                </el-form-item>
                <el-form-item>
                  <el-button
                    type="primary"
                    icon="el-icon-search"
                    size="mini"
                    @click="handleQuery"
                    >搜索</el-button
                  >
                  <el-button
                    icon="el-icon-refresh"
                    size="mini"
                    @click="resetQuery"
                    >重置</el-button
                  >
                  <el-button
                    v-show="!showSearch"
                    @click="showSearch = true"
                    icon="el-icon-arrow-down"
                    circle
                  ></el-button>
                  <el-button
                    v-show="showSearch"
                    @click="showSearch = false"
                    icon="el-icon-arrow-up"
                    circle
                  ></el-button>
                </el-form-item>
              </el-form>
              <!--默认折叠-->
              <el-col :span="24">
                <el-form
                  :model="queryParams"
                  ref="queryForm"
                  size="small"
                  :inline="true"
                  v-show="showSearch"
                  label-width="68px"
                >
                  <el-form-item>
                    <el-input
                      style="width: 240px"
                      placeholder="中间计量名称"
                      v-model="queryParams.name"
                    ></el-input>
                  </el-form-item>
                  <el-form-item>
                    <el-input
                      style="width: 240px"
                      placeholder="中间计量编号"
                      v-model="queryParams.code"
                    ></el-input>
                  </el-form-item>
                  <el-form-item>
                    <el-input
                      style="width: 240px"
                      placeholder="任务单编号"
                      v-model="queryParams.constructionCode"
                    ></el-input>
                  </el-form-item>
                  <el-form-item>
                    <el-input
                      style="width: 240px"
                      placeholder="结算计量单编码"
                      v-model="queryParams.settleCode"
                    ></el-input>
                  </el-form-item>
                </el-form>
              </el-col>
            </el-row>
          </el-col>
        </el-row>
        <!--操作按钮区开始-->
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button
              icon="el-icon-view"
              size="mini"
              type="warning"
              @click="handleOpenOperate"
              >审核意见
            </el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button icon="el-icon-view" size="mini" type="success" v-has-menu-permi="['calccheck:intermediate:reportpreview']" @click="handlePreview"
              >报表预览
            </el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button
              icon="el-icon-download"
              size="mini"
              type="warning"
              v-has-menu-permi="['calccheck:intermediate:pendingexport']"
              @click="exportList"
              >导出清单
            </el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button icon="el-icon-download" size="mini" type="primary" v-has-menu-permi="['calccheck:intermediate:reportdownload']" @click="handleDownload"
              >报表下载
            </el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button
              type="warning"
              icon="el-icon-view"
              size="mini"
              v-has-permi="['settlement:repository:regenerate']"
              @click="handleRegenerate"
            >重新生成报表
            </el-button>
          </el-col>
          <right-toolbar
            :columns="columns"
            :showSearch.sync="showSearch"
            @queryTable="handleQuery"
          ></right-toolbar>
        </el-row>
        <el-row>
          <div class="draggable">
            <el-table v-adjust-table
              ref="dataTable"
              v-loading="loading"
              :data="tableData"
              :height="
                showSearch ? 'calc(100vh - 330px)' : 'calc(100vh - 270px)'
              "
              border
              highlight-current-row
              row-key="id"
              size="mini"
              @row-click="handleClickRow"
              @selection-change="handleSelectionChange"
              stripe
              style="width: 100%"
            >
              <el-table-column type="selection" width="50" align="center"/>
              <el-table-column
                align="center"
                label="序号"
                type="index"
                width="50"
              />
              <template v-for="(column, index) in columns">
                <el-table-column
                  v-if="column.visible"
                  :label="column.label"
                  :prop="column.field"
                  :width="column.width"
                  align="center"
                  show-overflow-tooltip
                >
                  <template slot-scope="scope">
                    <dict-tag
                      v-if="column.dict"
                      :options="dict.type[column.dict]"
                      :value="scope.row[column.field]"
                    />
                    <template v-else-if="column.slots">
                      <RenderDom
                        :index="index"
                        :render="column.render"
                        :row="scope.row"
                      />
                    </template>
                    <span v-else-if="column.isTime">{{
                      parseTime(scope.row[column.field], "{y}-{m}-{d}")
                    }}</span>
                    <span v-else>{{ scope.row[column.field] }}</span>
                  </template>
                </el-table-column>
              </template>
              <el-table-column
                align="center"
                class-name="small-padding fixed-width"
                fixed="right"
                label="操作"
                width="250"
              >
                <template slot-scope="scope">
                  <el-button
                    icon="el-icon-check"
                    size="mini"
                    type="text"
                    v-has-menu-permi="['calccheck:intermediate:process']"
                    @click="handleCheck(scope.row)"
                    >审核
                  </el-button>
                  <el-button
                    icon="el-icon-view"
                    size="mini"
                    type="text"
                    @click="showInfo(scope.row)"
                    >查看明细
                  </el-button>
                </template>
              </el-table-column>
            </el-table>
            <pagination
              v-show="total > 0"
              :limit.sync="queryParams.pageSize"
              :page.sync="queryParams.pageNum"
              :total="total"
              @pagination="handleQuery"
            />
          </div>
        </el-row>
      </el-col>
    </el-row>
    <el-dialog
      :visible.sync="openOperateInfo"
      destroy-on-close
      width="80%"
      title="操作意见"
      v-if="openOperateInfo"
    >
      <operateInfo
        @close="modelClose"
        :businessKey="row.id"
        :getNodeInfo="getNodeInfo"
      ></operateInfo>
    </el-dialog>
    <el-dialog :visible.sync="infoDialog" width="80%" title="明细查看">
      <info :calcId="calcId" :maiSecId="maiSecId" @close="modelClose"></info>
    </el-dialog>
    <el-dialog
      :visible.sync="openFile"
      destroy-on-close
      title="附件列表"
      width="500px"
    >
      <file-upload
        v-model="fileId"
        :forView="true"
        @close="modelClose"
      ></file-upload>
    </el-dialog>
    <el-dialog
      :visible.sync="openOperate"
      destroy-on-close
      title="审核意见"
      width="500px"
    >
      <div class="app-container maindiv">
        <el-row :gutter="20">
          <el-col :span="24" :xs="24">
            <el-form
              ref="elForm"
              :inline="true"
              :model="formData"
              label-width="120px"
              size="small"
            >
              <el-form-item label="预计结算总费用">
                <el-input
                  v-model="formData.calcFund"
                  readonly
                  placeholder="核定审核金额"
                  style="width: 260px"
                />
              </el-form-item>
              <el-form-item label="预计监理费用">
                <el-input
                  v-model="formData.supFund"
                  readonly
                  placeholder="请输入预计监理费用"
                  style="width: 260px"
                />
              </el-form-item>
              <el-form-item label="审核意见">
                <el-input
                  v-model="formData.comment"
                  placeholder="请输入审核意见"
                  style="width: 260px"
                  type="textarea"
                />
              </el-form-item>
            </el-form>
            <div style="width: 95%; text-align: right">
              <el-button
                v-if="formData.status > 0"
                type="danger"
                @click="handleSubmit(false, 1)"
                >驳回至申请</el-button
              >
              <el-button
                v-if="formData.status > 1"
                type="danger"
                @click="handleSubmit(false, 2)"
                >驳回至上一步</el-button
              >
              <el-button type="primary" @click="handleSubmit(true)"
                >通 过</el-button
              >
            </div>
          </el-col>
        </el-row>
      </div>
    </el-dialog>
    <IFramePreview ref="iframeRef" :srcdoc="preview.html" :down-url="preview.url" :file-name="preview.fileName"></IFramePreview>
  </div>
</template>

<script>
import selectTree from "@/components/DeptTmpl/selectTree.vue";
import MaintenanceTree from "@/components/MaintenanceTree/index.vue";
import operateInfo from "@/views/dailyMaintenance/component/operateInfo.vue";
import {
  listPendingDaliyIntermediate,
  middleProcess,
  getNodeInfo,
  getPreviewInfo,
  downloadReport,
} from "@/api/regularTesting/metering/middleApplication";
import info from "./info.vue";
import RoadSection from "@/views/baseData/components/roadSection/index.vue";
import IFramePreview from "@/components/IFramePreview/index.vue";
import axios from 'axios'
import { saveAs } from 'file-saver'
import {regenerateReport} from "@/api/dailyMaintenance/metering/addPrice";
export default {
  name: 'MiddleExamine',
  components: {
    IFramePreview,
    MaintenanceTree,
    RoadSection,
    operateInfo,
    selectTree,
    info,
    RenderDom: {
      functional: true,
      props: {
        row: Object,
        index: Number,
        render: Function,
      },
      render(createElement, ctx) {
        const { row, index } = ctx.props;
        return ctx.props.render(row, index);
      },
    },
  },
  props: [],
  dicts: ["testing_intermediate_status"],
  data() {
    return {
      leftTotal: 1,
      showSearch: false,
      queryParams: {
        pageNum: 1,
        pageSize: 50,
      },
      total: 0,
      loading: false,
      columns: [
        {
          key: 0,
          width: 100,
          field: "status",
          label: `状态`,
          visible: true,
          dict: "testing_intermediate_status",
        },
        {
          key: 2,
          width: 110,
          field: "name",
          label: `中间计量名称`,
          visible: true,
        },
        {
          key: 3,
          width: 110,
          field: "code",
          label: `中间计量编号`,
          visible: true,
        },
        {
          key: 4,
          width: 100,
          field: "domainName",
          label: `管养单位`,
          visible: true,
        },
        {
          key: 5,
          width: 110,
          field: "calcDomainName",
          label: `申请计量单位`,
          visible: true,
        },
        {
          key: 6,
          width: 100,
          field: "maiSecId",
          label: `路段名称`,
          visible: true,
        },
        {
          key: 7,
          width: 100,
          field: "conName",
          label: `合同名称`,
          visible: true,
        },
        {
          key: 7,
          width: 110,
          field: "calcFund",
          label: `核定计量金额`,
          visible: true,
        },
        {
          key: 8,
          width: 100,
          field: "sumFund",
          label: `基本费用`,
          visible: true,
        },
        {
          key: 9,
          width: 100,
          field: "productionFund",
          label: `安全生产费`,
          visible: true,
        },
        {
          key: 10,
          width: 100,
          field: "guaranteeFund",
          label: `安全保通费`,
          visible: true,
        },
        {
          key: 11,
          width: 80,
          field: "supFund",
          label: `监理费`,
          visible: true,
        },
        {
          key: 12,
          width: 100,
          field: "calcDate",
          label: `计量日期`,
          visible: true,
        },
        {
          key: 17,
          width: 100,
          field: "fileId",
          label: `附件`,
          visible: true,
          slots: true,
          render: (row, index) => {
            return (
              <el-button
                size="mini"
                disabled={!row.fileId}
                type="text"
                onClick={(e) => this.handleOpenFile(e, row)}
              >
                查看
              </el-button>
            );
          },
        },
      ],
      tableData: [],
      rowData: {},
      drawerTitle: "中间计量申请",
      fileId: "",
      drawer: false,
      openFile: false,
      openOperateInfo: false,
      infoDialog: false,
      openOperate: false,
      formData: {},
      maiSecId: "",
      calcId: "",
      // 左侧组织树
      realNav: true,
      row: {},
      preview: {
        html: '',
        url: '',
        fileName: ''
      },
      ids: []
    };
  },
  computed: {},
  watch: {},
  created() {
    this.handleQuery();
  },
  mounted() {},
  methods: {
    getNodeInfo,
    handleNodeClick(e) {
      this.queryParams.domainId = e.domainId || "";
      this.queryParams.maiSecId = e.maiSecId || "";
      this.handleQuery();
    },
    handleQuery() {
      this.loading = true;
      if (this.queryParams.calcDate) {
        this.queryParams.startCalcDate = this.queryParams.calcDate[0];
        this.queryParams.endCalcDate = this.queryParams.calcDate[1];
      }
      listPendingDaliyIntermediate(this.queryParams).then((res) => {
        this.loading = false;
        this.tableData = res.rows;
        this.total = res.total;
      });
    },
    resetQuery() {
      this.queryParams = {
        pageNum: 1,
        pageSize: 50,
      };
      this.handleQuery()
    },

    handleOpenFile(e, row) {
      this.fileId = "";
      this.openFile = true;
      this.fileId = row.fileId;
    },
    handleClickRow(row) {
      this.row = row;
    },
    handleCheck(row) {
      this.formData = {
        businessKey: row.id,
        taskId: row.taskId,
        supFund: row.supFund,
        status: row.status,
        calcFund: row.calcFund,
      };
      this.openOperate = true;
    },
    handleSubmit(approved, reject) {
      if (!approved && !this.formData.comment) {
        this.$modal.msgWarning('请填写审核意见')
        return
      }

      this.formData.approved = approved;
      this.formData.reject = reject;
      middleProcess(this.formData).then((res) => {
        this.$modal.msgSuccess("操作成功");
        this.openOperate = false;
        this.handleQuery();
      });
    },
    showInfo(row) {
      this.infoDialog = true;
      this.maiSecId = row.maiSecId;
      this.calcId = row.id;
    },
    handleOpenOperate() {
      if (!this.row.id) {
        this.$modal.msgError("请选择一条数据");
        return;
      }
      this.openOperateInfo = true;
    },
    // 导出清单按钮
    exportList() {
      this.download(
        "manager/calc/check/intermediate/pending/export",
        { ...this.queryParams },
        `intermediate_${new Date().getTime()}.xlsx`,
        {
          headers: { "Content-Type": "application/json;" },
          parameterType: "body",
        }
      );
    },
    // 报表预览
    handlePreview() {
      if (!this.row.id) {
        this.$modal.msgError("请选择一条数据");
        return;
      }
      const params = {id: this.row.id}
      this.loading = true
      getPreviewInfo(params).then(res => {
        this.loading = false
        this.preview.html = res.data.html
        this.preview.url = res.data.downUrl
        this.preview.fileName = res.data.fileName
        this.$refs.iframeRef.visible = true
      })
    },
    // 报表下载
    handleDownload() {
      if (!this.row.id) {
        this.$modal.msgError("请选择一条数据");
        return;
      }
      const params = {id: this.row.id}
      this.loading = true
      downloadReport(params).then(res=> {
        if (res.code == 200){
          if (res.data.fileName.endsWith('.zip')) {
            let link = document.createElement('a')
            link.download = res.data.fileName
            link.style.display = 'none'
            link.href = res.data.downUrl
            document.body.appendChild(link)
            link.click()
            document.body.removeChild(link)
            this.loading = false
          } else {
            axios({
              method: "get",
              responseType: 'arraybuffer',
              url: res.data.downUrl,
              headers: {}
            }).then((blobRes) => {
              const arrayBuffer = blobRes.data;
              // 创建一个Blob对象
              const blob = new Blob([arrayBuffer], {type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'}); // 对于.xls文件
              saveAs(blob, res.data.fileName)
            }).finally(() => {
              this.loading = false
            })
          }
        }
      })
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection
    },
    handleRegenerate() {
      if (this.ids.length == 0) {
        this.$modal.msgError("请勾选至少一条数据")
        return
      }
      const params = {
        idList: this.ids.map(item => item.id),
        type: 6
      }
      regenerateReport(params).then(res => {
        this.$modal.msgSuccess("操作成功")
      })
    },
    modelClose() {
      this.openFile = false;
      this.openOperateInfo = false;
      this.infoDialog = false;
    },
  },
};
</script>

<style lang="scss" scoped>
.rightIcon {
  border: 1px solid #dcdfe6;
  border-radius: 8px;
  width: 16px;
  height: 50px;
  line-height: 50px;
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
  position: absolute;
  left: -10px;
  top: 280px;
  z-index: 10;
  background: white;
}

.rightIcon:hover {
  background-color: #dcdfe6;
}

.left-total {
  font-size: 13px;
  line-height: 28px;
  color: #606266;
  position: absolute;
  bottom: 10px;
  right: 10px;
}
</style>
<style lang="scss" scoped>
@import "@/assets/styles/business.scss";
</style>
