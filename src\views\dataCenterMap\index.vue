<template>
  <div class="map" id="mapfull">
    <map-view ref="mapRef" v-if="!loading && isFinish" @click="onMapClick" />
    <!-- <arcgis-map ref="mapRef"/> -->
    <!-- <GeosceneMap ref="mapRef" /> -->
    <!-- 全屏操作 -->
    <div class="full-screen" title="退出" :style="{ top: !isFull ? '5px' : '-2px' }">
      <!-- <svg-icon :icon-class="isFullscreen ? 'exit-fullscreen' : 'fullscreen'" @click="fullClick" v-if="!isFull" /> -->
      <div class="icon-img">
        <img :src="isFullscreen ? QuitFullImg : FullImg" @click="fullClick" class="click-full" v-if="!isBig" />
        <img src="@/assets/map/quite.png" v-if="$route.path != '/static/centerDataMap'" @click="handleQuit" />
      </div>
    </div>
    <!-- 管理处树 -->
    <dp-tree @click="treeShow = $event" ref="tRef" v-if="deptShow"></dp-tree>
    <!-- 操作目录 -->
    <Tree @check="handleTreeChecked" @finish="isFinish = true" ref="treeRef" v-show="treeShow || !deptShow" />
    <!-- 图例 -->
    <Legend />
    <!-- 图层-右下 -->
    <layer-controll />
    <!-- 左侧操作按钮 -->
    <left-menu v-if="!loading" @click="menuClick" />
    <!-- 右侧操作按钮 -->
    <!-- <right-menu @openTable="(val, flag, title) => { tableData = val; showRouteTable = flag; tableTitle = title; }" /> -->
    <route-table v-if="showRouteTable" :data="tableData" :tableTitle="tableTitle" @close="onCloseTable" />
    <!-- 右侧表格 -->
    <right-menu @openTable="onClickTable" />

    <!-- 详情弹窗 -->
    <div v-if="showDVisible" :class="detailType == 3 ? 'dialog-map' : 'dialog-body'" class="dialog-detail">
      <Dialog :title="title" :show.sync="showDVisible" top="0" :append="false" width="70%" :modal="true">
        <div class="dialog-content" :style="dynamicStyle">
          <component :is="componentName" :form-data="formData" :id="formData.id" :oneMap="isOneMap" />
        </div>
      </Dialog>
    </div>
    <!-- 点击地图弹出表格或者Echart图表详情 - 接口类 -->
    <TableInfo :show.sync="tableShow" :url="infoUrl" :method="method" :key="tableKey" :data-obj="dataObj"
      :healthMonitoring="healthMonitoring" :ifChart="ifChart" :isBig="isBig" />
    <!-- 顶部标题 -->
    <div class="map-title">
      <span v-if="pageType">工程集群健康监测智慧化管理平台</span>
      <span v-else>公路智慧养护一张图</span>
    </div>
    <!-- 路线信息弹窗 -->
    <LineDialog v-if="showLVisible" :show.sync="showLVisible" :id="lineId" :coordinate="coordinate"
      @showAnimation="onShowAnimation" />
    <RoadAnimation v-if="animationVisible" :roadData="roadData" @showInfo="onShowInfo"
      @close="animationVisible = false" />
    <div v-if="openInfoFromAnimation" :class="detailType == 3 ? 'dialog-map' : 'dialog-body'" class="dialog-detail">
      <Dialog :title="title" :show.sync="openInfoFromAnimation" top="0" :append="false" width="70%" :modal="true">
        <div class="dialog-content" :style="dynamicStyle">
          <component :is="componentName" :form-data="formData" :id="formData.id" :oneMap="isOneMap" />
        </div>
      </Dialog>
    </div>
  </div>
</template>

<script>
import Vue from "vue";
// api
import { getListPage } from "@/api/oneMap/layer";

import screenfull from "screenfull";
// 组件
import DpTree from "./components/dpTree.vue";
import Tree from "./components/tree.vue";
import Legend from "./components/legend.vue";
import LayerControll from "./components/layer.vue";
import MapView from "./components/mapView.vue";
import ArcgisMap from "./components/arcgisMap.vue";
import GeosceneMap from "./components/geosceneMap.vue";
import LeftMenu from "./components/left/leftMenu.vue";
import RightMenu from "./components/right/menu.vue";
import RouteTable from "./components/bottom/routeTable.vue";
import Dialog from "@/components/Dialog/index.vue";
import TableInfo from "@/components/table/tableInfo.vue";
import LineDialog from "./components/lineDialog.vue";
import RoadAnimation from "./components/roadAnimation/index.vue";
// vuex
import { mapState, mapActions, mapMutations } from "vuex";
import {
  addWidthFeature,
  removeLayer,
  addClickFeature,
  isValidWKT,
  lineId,
  featureToWkt,
} from "./components/common/mapFun";
import { WKT } from "ol/format";
import FullImg from "@/assets/map/full.png";
import QuitFullImg from "@/assets/map/quit-full.png";
import { getDataList } from "@/api/oneMap/layerData";
import cache from "@/plugins/cache";
import { isBigScreen } from "./components/common/util";
import { iframeDialog } from "./components/common/iframe";

const list = require.context("@/views", true, /\.vue$/);

export default {
  name: 'Map',
  components: {
    DpTree,
    Tree,
    Legend,
    LayerControll,
    MapView,
    ArcgisMap,
    LeftMenu,
    RightMenu,
    GeosceneMap,
    Dialog,
    TableInfo,
    RouteTable,
    LineDialog,
    RoadAnimation
  },
  provide() {
    return {
      oneMap: this.isOneMap,
      instance: this,
    };
  },
  data() {
    return {
      mapLayer: null,
      isFullscreen: false,
      loading: false,
      isFinish: false, // 用户树组件 部门权限数据请求完成
      showVisible: false,
      componentName: "",
      formData: {}, // 详情参数
      tableShow: false, // 接口详情数据
      infoUrl: "", // 地图详情接口
      method: "", // 接口类型
      tableKey: "",
      dataObj: {}, // 请求参数
      detailType: null, // 弹窗详情类型
      isFull: false, //
      FullImg,
      QuitFullImg,
      showRouteTable: false,
      tableData: [],
      isOneMap: true,
      tableTitle: "",
      title: "详情",
      isTap: false,
      lineId: null,
      coordinate: null,
      treeShow: false,
      pageType: false,
      healthMonitoring: null,
      routeType: '',
      animationVisible: false,
      roadData: null,
      isBig: isBigScreen(),
      openInfoFromAnimation: false,
      ifChart: false,
      SubgradeUrlList: {
        '路基': "./baseData/subgrade/subgradeWidthInfo/components/detail.vue",
        "边坡": "./baseData/subgrade/sideSlope/detail.vue",
        "挡墙": "./baseData/subgrade/retainingWallInfo/detail.vue",
        "中央集水沟": "./baseData/subgrade/collectionDitch/detail.vue",
        "路肩": "./baseData/subgrade/shoulder/detail.vue"
      },
      iframeUrl: null, // 地址栏地址
    };
  },
  computed: {
    ...mapState({
      detlCondf: (state) => state.map.detlCondf,
      legendList: (state) => state.map.legendList,
      tableHeader: (state) => state.map.tableHeader,
      lineClick: (state) => state.map.lineClick,
      deptShow: (state) => state.map.deptShow,
      menuShowType: (state) => state.map.menuShowType,
    }),
    dynamicStyle() {
      let strStyle = "";
      if (this.isFull) {
        if (this.detailType === 3) {
          strStyle = "height: calc(100vh - 170px);";
        } else {
          strStyle = "height: calc(100vh - 80px);";
        }
      } else {
        if (this.detailType === 3) {
          strStyle = "height: calc(100vh - 210px);";
        } else {
          strStyle = "height: calc(100vh - 100px);";
        }
      }
      return strStyle;
    },
    // 是否显示详情弹窗
    showDVisible: {
      get() {
        return this.showVisible && !this.isTap;
      },
      set(val) {
        this.showVisible = false;
      },
    },
    // 是否显示路线弹窗
    showLVisible: {
      get() {
        return this.showVisible && this.isTap;
      },
      set(val) {
        this.showVisible = false;
      },
    },
  },
  created() {
    this.routeType = this.$route.query.type || ''
    if (this.routeType == "1") {
      this.pageType = true;
    }
    this.getMapLayerList();
    // const objString = decodeURIComponent(this.$route.query.data);
    // const query = JSON.parse(objString)
    const query = this.$route.query;
    if (query.shape) {
      setTimeout(() => {
        let shape = query.shape;
        let feature = new WKT().readFeature(shape, {
          dataProjection: "EPSG:4326",
          featureProjection: "EPSG:3857",
        });
        let data = {
          ...query,
        };
        addClickFeature(
          feature,
          data,
          null,
          true,
          true,
          "clickLayer",
          2,
          query.name
        );
        window.mapLayer.getView().fit(feature.getGeometry().getExtent(), {
          duration: 300,
        });
        this.$router.replace({
          path: this.$route.path,
          query: {}, // 移除所有查询参数
        });
      }, 1000);
    }
    if (this.$route.name == "oneMap") {
      this.isFull = true;
    }
  },
  mounted() {
    screenfull.on("change", this.change);
  },
  unmounted() {
    screenfull.off("change", this.change);
    window.mapLayer = null;
  },
  watch: {
    showVisible(val) {
      if (!val) {
        let { path } = this.$route;
        this.$router.push({ path });
      }
    },
    showLVisible(val) {
      if (!val) {
        this.animationVisible = val;
      }
    },
    openInfoFromAnimation(val) {
      if (!val) window.$Bus.$emit("closeInfoForAnimation");
    }
  },

  methods: {
    ...mapActions({
      getMapLayer: "map/getMapLayer",
      getDetlCondf: "map/getDetlCondf",
      setSpread: "map/setSpread",
    }),
    ...mapMutations({
      setTableHeader: "map/setTableHeader",
      setLayer: "map/setLayer",
    }),
    // 获取地图图层
    getMapLayerList() {
      this.loading = true;
      getListPage({ pageNum: 1, pageSize: 99 })
        .then((res) => {
          if (res.code == 200 && res.rows) {
            let arr = res.rows.filter((v) => v.defaultMap == 1 || v.defaultMap);
            arr = arr.map((v) => {
              v.ifShow = !!v.ifShow;
              v.opacity = 100;
              v.name = v.layerName;
              return v;
            });
            this.getMapLayer(arr);
            this.setLayer({ layer: arr });
          }
        })
        .finally(() => {
          this.loading = false;
        });
    },
    change() {
      this.isFullscreen = screenfull.isFullscreen;
    },
    // 点击全屏
    fullClick() {
      // if (screenfull.isFullscreen) {
      //   screenfull.exit();
      // } else {
      //   screenfull.toggle();
      // }
      // return;
      if (!this.isFull) {
        window.$Bus.$off();
        this.$router.push(`/onemap?type=2`);
        if (!screenfull.isFullscreen) screenfull.toggle();
      } else {
        if (this.routeType == "2") {
          this.$router.go(-1);
          if (screenfull.isFullscreen) screenfull.exit();
        } else {
          if (screenfull.isFullscreen) {
            screenfull.exit();
          } else {
            screenfull.toggle();
          }
        }
      }
    },
    // 选中树
    handleTreeChecked(data) {
      data = data ? data : [];
      if (this.$refs.mapRef) {
        // 将数据加载到地图
        // this.$refs.mapRef.setMapShape(data)
      }
    },
    // 地图点击事件
    async onMapClick(data) {
      console.log("地图-点击", data)
      // 如果 存在面数据 并且 是点选则阻止
      if (data && data.mgeom && this.lineClick) return;
      this.isTap = false;
      this.title = data.typeName || "详情";
      let legendRusult
      let detlCondf;
      // 和图列数据匹配
      if (data && data.layer) {
        let arr = this.legendList.filter((v) => v.layerMenuSubId == data.layer);
        let listQueryConfig = arr && arr.length ? arr[0].listQueryConfig : null;
        if (listQueryConfig && typeof listQueryConfig == "string") {
          listQueryConfig = JSON.parse(listQueryConfig);
        } else {
          listQueryConfig = listQueryConfig ? listQueryConfig : null;
        }
        legendRusult = arr
        if (listQueryConfig && typeof listQueryConfig?.detlCondf == "string") {
          detlCondf = JSON.parse(listQueryConfig.detlCondf);
        } else {
          detlCondf = listQueryConfig ? listQueryConfig.detlCondf : null;
        }
        this.setTableHeader({
          header:
            listQueryConfig && listQueryConfig.tableHeader
              ? listQueryConfig.tableHeader
              : this.tableHeader,
        });
      }
      if (!detlCondf) {
        let listQueryConfig = data ? data.listQueryConfig : null;
        if (listQueryConfig && typeof listQueryConfig.detlCondf == "string") {
          detlCondf = JSON.parse(listQueryConfig.detlCondf);
        } else {
          detlCondf = listQueryConfig ? listQueryConfig.detlCondf : null;
        }
        legendRusult = data
      }

      // 线图层
      if (data.layer == lineId && !detlCondf) {
        this.title = "路线信息";
        this.isTap = true;
        this.lineId = data.id;
        this.coordinate = data.coordinate;
        // 线图层数据 缓存
        let lineObj = cache.session.getJSON("lineObj");
        if (lineObj) {
          let listQueryConfig = lineObj.listQueryConfig;
          listQueryConfig =
            typeof listQueryConfig == "string"
              ? JSON.parse(listQueryConfig)
              : listQueryConfig;
          if (listQueryConfig && typeof listQueryConfig.detlCondf == "string") {
            detlCondf = JSON.parse(listQueryConfig.detlCondf);
          } else {
            detlCondf = listQueryConfig ? listQueryConfig.detlCondf : null;
          }
        }

        let shape = data.LineString
          ? new WKT().writeGeometry(data.LineString)
          : null;
        if (shape) {
          let feature = new WKT().readFeature(shape);
          // 移除
          removeLayer(window.mapLayer, "clickLayer");
          // 添加到地图
          // addClickFeature(feature, data, null, true, true, 'clickLayer', 1, data.typename);
          // 移动到地图
          // window.mapLayer.getView().fit(feature.getGeometry().getExtent(), {
          //   duration: 500
          // })
        }
      }
      // 如果存在
      if (detlCondf) {
        if (data.writeable || data.writeUrl) {
          detlCondf.writeable = data.writeable;
          detlCondf.writeUrl = data.writeUrl;
        }
        if (detlCondf.isMultiPage) {
          detlCondf = detlCondf[data.type] || detlCondf['default'];
        }
        this.getDetlCondf(detlCondf);
      }
      if (!data || !this.detlCondf) return;
      if (this.detlCondf.type && data.id) {
        let isHiddenDanger = false;
        let isSubgrade = false
        if (Array.isArray(legendRusult) || legendRusult.listQueryConfig) {
          let arr, listQueryConfig
          if (Array.isArray(legendRusult)) {
            arr = legendRusult[0]
          } else {
            let result = this.legendList.filter((v) => v.id == legendRusult.treeId);
            arr = result[0]
          }

          listQueryConfig = arr?.listQueryConfig || data?.listQueryConfig
          if (listQueryConfig && typeof listQueryConfig == "string") {
            listQueryConfig = JSON.parse(listQueryConfig);
          } else {
            listQueryConfig = listQueryConfig ? listQueryConfig : null;
          }

          if (
            listQueryConfig &&
            listQueryConfig.healthMonitoring &&
            typeof listQueryConfig.healthMonitoring == "string"
          ) {
            this.healthMonitoring = JSON.parse(
              listQueryConfig.healthMonitoring
            );
          } else {
            this.healthMonitoring = listQueryConfig?.healthMonitoring || null;
          }

          if (this.menuShowType == 1 && data?.healthMonitoring) {
            this.healthMonitoring = data.healthMonitoring
          }
          if (listQueryConfig?.hiddenDanger) {
            isHiddenDanger = true;
          }
          if (listQueryConfig?.subgrade) {
            isSubgrade = true
          }
        }
        this.formData = data || {};
        let componentUrl = this.detlCondf.path;
        //如果是自然灾害点图标
        if (isHiddenDanger && data.listQueryConfig) {
          if (data.listQueryConfig.detlCondf.type != 3) {
            this.detlCondf.type = 2
            if (data?.type == "高边坡" || data?.hazard_type == "高边坡") {
              componentUrl = "./disaster/highSlope/view/highSlopeFormDialog.vue";
            } else {
              componentUrl = "./disaster/risk/view/riskFormDialog.vue";
            }
          }
        }
        // 如果是路基图标
        if (isSubgrade) {
          this.detlCondf.type = 2
          componentUrl = this.SubgradeUrlList[data.typeName]
        }
        const array = componentUrl.split("/");
        const [firstItem, firstButOne, thirdName] = [...array];
        let { path, query, matched } = this.$route;

        // 是否是正确的组件
        let componentBool = false;
        if (thirdName == "hidden") {
          let ids = [
            "885463252157333504",
            "885468706916794368",
            "885471019043651584",
          ];
          let id = ids[Math.floor(Math.random() * ids.length)];
          this.formData.id = id;
        }
        try {
          const component = list(componentUrl).default;
          // 把组件加载到components中
          if (component) {
            Vue.component(thirdName, {
              name: thirdName,
              ...component,
            });
          }
          componentBool = true;
        } catch (error) {
          // this.$modal.msgError("组件加载失败");
          componentBool = false;
        }
        // 每次点击重置数值避免点击后数据一直不变
        this.detailType = null;
        // type: 2 路由
        if (
          this.detlCondf.type == 2 &&
          this.detlCondf.navigate &&
          this.detlCondf.navigate == "redirect"
        ) {
          delete data.shape;
          let serviceUrl = window.location.origin;
          if (serviceUrl) {
            let queryParams = new URLSearchParams();
            Object.keys(data).forEach(key => {
              queryParams.append(key, data[key]);
            });
            let url = serviceUrl + '/cockpit'
            let param = {
              ...data,
              componentR: "BridgeDetail",
              jumpType: 2,
            };
            url += `?${queryParams.toString()}` + '&componentR=BridgeDetail' + '&jumpType=2';
            this.iframeUrl = url;
            iframeDialog({ iframeUrl: null, componentName: 'subpage', params: param }).then(res => {
            });
            // if (!this.isFullscreen) {
            //   // 打开新窗口
            //   window.open(url, '_blank');
            // }
          } else {
            this.$router.push({
              path: "/cockpit",
              query: {
                ...data,
                componentR: "BridgeDetail",
                jumpType: 2,
              },
            });
          }

        } else if (this.detlCondf.type == 2) {
          let query = {};
          // 组织路由地址参数
          if (this.detlCondf.params && Array.isArray(this.detlCondf.params)) {
            this.detlCondf.params.forEach((v) => {
              query[v.paramColName] = data[v.dataColName];
            });
          } else {
            query = {
              id: data.id,
              bridgeName: data.typeName,
              whetherHealthMonitorSystemType: data.type,
            };
          }
          this.$router.push({
            path,
            query,
          });
        } else if (this.detlCondf.type == 3) {
          let arr
          if (Array.isArray(legendRusult)) {
            arr = legendRusult[0]
          } else {
            let result = this.legendList.filter((v) => v.id == legendRusult.treeId);
            arr = result[0]
          }
          // type：3 接口请求
          this.detailType = 3;
          this.tableKey = new Date().getTime();
          this.dataObj = {
            dataId: data.id,
            id: this.menuShowType == 1 ? (data?.treeId || arr?.id) : this.legendList[0]?.id,
            ...this.detlCondf.params,
          };
          // 设计回溯单独处理
          if (data.sjhsId) {
            this.dataObj.id = data.sjhsId;
            this.dataObj.menuSubClassifyId = data.menuSubClassifyId;
          }
          this.infoUrl = this.detlCondf.path || "";
          this.method = this.detlCondf.method || "post";
          if (this.detlCondf.ifChart) {
            this.ifChart = true;
          } else {
            this.ifChart = false;
          }
          this.tableShow = true;
          this.$forceUpdate();
        }

        if (componentBool) {
          // 接收动态组件名称
          this.componentName = thirdName;
          // 显示详情弹窗
          this.showVisible = true;
        }
        // removeLayer(window.mapLayer, 'dataLayer')
      } else {
        // 先移除
        removeLayer(window.mapLayer, "clickLayer");
        // 添加到地图
        // addWidthFeature(window.mapLayer, [data], 'clickLayer', true)
        if (data.type == "bridge") {
          // 打开第三方地址
          window.open(`https://jgjc-szls.glyhgl.com/video.html`, "_blank");
        }
      }
    },
    firstToUpperCase(str) {
      return str.charAt(0).toUpperCase() + str.slice(1);
    },
    // 退出、返回上个路由
    handleQuit() {
      // this.$router.back();
      this.$router.push({ path: "homeHB" });
      this.setSpread(0);
    },
    // 点击表格
    onClickTable(val, flag, title) {
      // (val, flag, title) => { tableData = val; showRouteTable = flag; tableTitle = title; }
      this.tableData = val;
      // this.showRouteTable = flag;
      this.tableTitle = title;

      // 不显示table表格直接加载 ？
      removeLayer(window.mapLayer, "clickLayer", "name");
      removeLayer(window.mapLayer, "clickLayer");
      let obj = {
        ...val,
        pageNum: 1,
        pageSize: 10000,
      };
      getDataList(obj)
        .then((res) => {
          if (res.code === 200 && res.rows) {
            let arr = res.rows.map((row) => {
              row.name = row.lxmc || row.zdmc || row.qdmc || "";
              return row;
            });
            let features = [];
            // addWidthFeature(window.mapLayer, arr, 'clickLayer', null, true)
            arr.forEach((row) => {
              let shape = row.shape;
              if (shape) {
                if (!isValidWKT(shape)) return;
                let feature = new WKT().readFeature(shape, {
                  dataProjection: "EPSG:4326",
                  featureProjection: "EPSG:3857",
                });
                feature.set("data", {
                  ...row,
                  listQueryConfig: { detlCondf: this.detlCondf },
                });
                features = [...features, ...[feature]];
                // addClickFeature(feature, row, null, false, false, 'clickLayer', 1)
              }
            });
            if (features && features.length) {
              addClickFeature(features, null, null, true, false, "clickLayer", 1);
              window.mapLayer.getView().fit(features[0]?.getGeometry()?.getExtent(), {
                duration: 300,
              });
            }
          }
        });
    },
    // 关闭表格弹窗
    onCloseTable() {
      this.showRouteTable = false;
      this.tableData = [];
      this.tableTitle = "";
    },
    menuClick(data) {
      if (data.value === 1 && this.menuShowType == 1) {
        this.treeShow = false;
      }
    },
    onShowAnimation(info, flag) {
      this.roadData = info;
      this.animationVisible = flag;
    },
    onShowInfo(obj) {
      this.title = "详情";
      const component = list(obj.componentUrl).default
      if (component) {
        Vue.component(obj.componentName, {
          name: obj.componentName,
          ...component,
        })
        this.formData = obj.data
        this.componentName = obj.componentName
        this.showVisible = true
        this.openInfoFromAnimation = true
        let { path } = this.$route
        let query = { ...obj.query }
        this.$router.push({
          path,
          query,
        })
      }
    }
  },
};
</script>

<style lang="scss" scoped>
@import "@/assets/styles/utils.scss";

@font-face {
  font-family: "YouSheBiaoTiHei";
  /* 自定义的字体名称 */
  src: url("~@/assets/home/<USER>") format("truetype");
  /* 字体文件路径和格式 */
  /* 可选属性，根据需要设置 */
  font-weight: normal;
  font-style: normal;
}

.map {
  width: 100%;
  height: 100%;
  position: relative;
  background-color: rgba(8, 5, 66, 0.65);
  touch-action: none;
  /* 禁用触摸操作 */
  user-select: none;
  /* 防止文本被选中 */

  .map-view {
    width: 100%;
    height: 100%;
  }

  .full-screen {
    position: absolute;
    right: vwpx(10px);
    cursor: pointer;
    color: #ffffff;
    z-index: 10000;

    .icon-img {
      display: flex;
      align-items: center;

      .click-full {
        width: vwpx(52px);
        height: vwpx(52px);
      }

      img {
        width: vwpx(100px);
        height: vwpx(100px);
      }
    }
  }

  .map-title {
    position: absolute;
    top: 3%;
    left: 60%;
    width: vwpx(900px);
    height: vwpx(120px);
    transform: translate(-50%, -50%);
    background-image: url("~@/assets/map/map-title-bg.png");
    background-repeat: no-repeat;
    background-size: 100% 100%;

    display: flex;
    align-items: center;
    justify-content: center;

    span {
      font-family: YouSheBiaoTiHei;
      font-weight: 400;
      font-size: vwpx(60px);
      letter-spacing: 2px;
      text-align: center;
      font-style: normal;
      text-transform: none;
      background: linear-gradient(180deg,
          #ffffff 0%,
          #ffffff 60%,
          #20a9ff 100%);
      background-clip: text;
      -webkit-background-clip: text;
      color: transparent;
      white-space: nowrap;
    }
  }
}

.dialog-map {
  .dialog-content {
    // max-height: calc(100vh - 110px);
    // height: calc(100vh - 210px);
    overflow-y: auto;
  }
}

.dialog-body {
  ::v-deep .el-dialog {
    margin: 0 auto 0;

    &:not(.is-fullscreen) {
      margin-top: 0vh !important;
    }
  }

  .dialog-content {
    // height: calc(100vh - 110px);
    overflow-y: auto;
  }
}

.dialog-detail {
  ::v-deep .el-dialog {
    margin: 0 auto 0;
    background: rgba(4, 17, 48, 0.8);
    box-shadow: inset 0px 0px 10px 0px #3662ec;
    border-radius: 10px 10px 10px 10px;
    border: 1px solid #0687ff;
    color: #ffffff !important;

    .el-dialog__header {
      border-bottom: none;
      padding: 10px 15px !important;

      .el-dialog__title {
        color: #ffffff;
      }

      .el-dialog__headerbtn {
        color: #ffffff;
        top: 10px;
      }
    }

    .el-dialog__body {
      padding: 10px;
      color: #ffffff !important;
    }
  }
}
</style>
