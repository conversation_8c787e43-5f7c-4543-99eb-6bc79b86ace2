<template>
  <el-dialog :append-to-body="append" destroy-on-close :title="title" :visible.sync="show" :width="width"
    :before-close="close" v-bind="$attrs">
    <div slot="title" v-if="ifHeader" style="color: #fff; font-size: 1.5vh; position: relative;;">
      {{ title }}
      <slot name="header"></slot>
    </div>
    <slot v-if="show" :show="show" :close="close" />
  </el-dialog>
</template>

<script>
export default {
  name: 'Dialog',
  components: {},
  props: {
    title: {
      type: String,
      default: ''
    },
    show: { // 通过 :show.sync 来进行绑定
      type: Boolean,
      default: false
    },
    width: {
      type: String,
      default: '60%'
    },
    isConfirm: {
      type: Boolean,
      default: false
    },
    // 自身是否插入至 body 元素上
    append: {
      type: Boolean,
      default: true,
    },
    ifHeader: {
      type: Boolean,
      default: false,
    }
  },
  data() {
    return {}
  },
  computed: {},
  watch: {},
  created() { },
  mounted() { },
  methods: {
    close() {
      if (this.isConfirm) {
        this.$confirm('未保存关闭将会丢失已填写内容，是否关闭？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.$emit('update:show', false)
        })
      } else {
        this.$emit('update:show', false)
      }
    }
  }
}
</script>

<style lang="scss" scoped>
@import "@/assets/styles/utils.scss";

::v-deep .el-dialog {
  .el-dialog__header {
    border-bottom: none;
    padding: 10px 15px !important;

    .el-dialog__title {
      font-size: 1.4vh;
    }

    .el-dialog__headerbtn {
      top: vwpx(20px);
      font-size: vwpx(38px);
    }
  }

  .el-dialog__body {
    padding: 10px;
  }
}
</style>