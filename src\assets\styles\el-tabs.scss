::v-deep {
  .el-tabs__header {
    height: 54px;
    background: #eff5ff;
    box-shadow: inset 0px 1px 0px 0px rgba(255, 255, 255, 0.5954);
    border-radius: 10px 10px 0px 0px;
    border: 1px solid #c0d2e2;
    border-bottom: 0;
    margin: 0;

    .el-tabs__nav {
      height: 54px;
      border: 0;
      display: flex;
      align-items: flex-end;
    }

    .el-tabs__item {
      border: 0;
      height: 43px;
      color: #333333;
      font-weight: 700;
      font-family: Microsoft YaHei, Microsoft YaHei;
      font-size: 14px;
    }

    .el-tabs__item.is-active {
      color: #409EFF;
      border-bottom: 3px solid #409EFF;
    }
  }

  .el-tabs__content {
    border-radius: 0px 0px 10px 10px;
    border: 1px solid #c0d2e2;
    border-top: 0;
    padding-top: 10px;
    background-color: #fff;
  }
}