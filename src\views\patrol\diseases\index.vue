<template>
  <div class="app-container">
    <el-row :gutter="20">
      <!--筛选区开始-->
      <el-col :span="24" :xs="24">
        <el-row>
          <el-col :span="24">
            <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" label-width="68px">
              <el-form-item label="" prop="assetName">
                <dict-select type="sys_asset_type" clearable ref="assetNameQuerySelect" only-tail-node
                             v-model="queryParams.assetName" placeholder="请选择资产名称"
                             style="width: 240px"></dict-select>
              </el-form-item>
              <!--              <el-form-item label="" prop="assetType" >
                              <dict-select type="sys_asset_type" clearable :level="1"
                                           v-model="queryParams.assetType" placeholder="请选择资产类型"  style="width: 240px"></dict-select>
                            </el-form-item>-->
              <el-form-item label="" prop="diseaseName">
                <el-input
                  v-model="queryParams.diseaseName"
                  placeholder="请输入病害类型"
                  clearable
                  style="width: 240px"
                  @keyup.enter.native="handleQuery"
                />
              </el-form-item>
              <el-form-item label="" prop="diseaseCode">
                <el-input
                  v-model="queryParams.diseaseCode"
                  placeholder="请输入病害编码"
                  clearable
                  style="width: 240px"
                  @keyup.enter.native="handleQuery"
                />
              </el-form-item>
              <el-form-item label="" prop="pileMode">
                <el-select v-model="queryParams.pileMode" placeholder="请选择桩号计算方式" clearable
                           style="width: 240px">
                  <el-option
                    v-for="dict in dict.type.sys_pile_mode"
                    :key="dict.value"
                    :label="dict.label"
                    :value="dict.value"
                  />
                </el-select>
              </el-form-item>
              <el-form-item>
                <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
                <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
                <!--                <el-button v-show="!showSearch" @click="showSearch=true" icon="el-icon-arrow-down" circle></el-button>-->
                <!--                <el-button  v-show="showSearch" @click="showSearch=false"  icon="el-icon-arrow-up" circle></el-button>-->
              </el-form-item>
            </el-form>
            <!--默认折叠-->
          </el-col>
          <!--默认折叠 ,此处仅作为示例-->
          <el-col :span="24">

          </el-col>
        </el-row>
        <!--筛选区结束-->

        <!--操作按钮区开始-->
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button
              type="primary"
              icon="el-icon-plus"
              size="mini"
              @click="handleAdd"
              v-hasPermi="['system:diseases:add']"
            >新增
            </el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button
              type="success"
              icon="el-icon-edit"
              size="mini"
              :disabled="single"
              @click="handleUpdate"
              v-hasPermi="['system:diseases:edit']"
            >修改
            </el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button
              type="danger"
              icon="el-icon-delete"
              size="mini"
              :disabled="multiple"
              @click="handleDelete"
              v-hasPermi="['system:diseases:remove']"
            >删除
            </el-button>
          </el-col>
          <!--          <el-col :span="1.5">
                      <el-button
                        type="info"
                        plain
                        icon="el-icon-upload2"
                        size="mini"
                        @click="handleImport"
                        v-hasPermi="['system:diseases:export']"
                      >导入
                      </el-button>
                    </el-col>
                    <el-col :span="1.5">
                      <el-button
                        type="warning"
                        icon="el-icon-download"
                        size="mini"
                        @click="handleExport"
                        v-hasPermi="['system:user:export']"
                      >导出
                      </el-button>
                    </el-col>-->
          <right-toolbar :showSearch.sync="showSearch" @queryTable="getList" :columns="columns"></right-toolbar>
        </el-row>
        <!--操作按钮区结束-->

        <!--数据表格开始-->
        <div class="tableDiv">
          <el-table size="mini" :height="showSearch ? 'calc(100vh - 320px)' : 'calc(100vh - 260px)'" style="width: 100%"
                    v-loading="loading" border :data="diseasesList" @selection-change="handleSelectionChange">
            <el-table-column type="selection" width="50" align="center"/>
            <el-table-column fixed label="序号" type="index" align="center" width="50">
              <template v-slot="scope">
                {{ (scope.$index + (queryParams.pageNum - 1) * queryParams.pageSize)+1 }}
              </template>
            </el-table-column>
            <el-table-column label="资产类型" align="center" prop="assetType" v-if="computedColumns('资产类型')"
                             :formatter="(...arg)=> selectDictLabel(dict.type.sys_asset_type, arg[2])"/>
            <el-table-column label="资产名称" align="center" prop="assetName"  v-if="computedColumns('资产名称')">

              <template slot-scope="scope">
                <dict-tag :options="dict.type.sys_asset_type" :value="scope.row.assetName"/>
              </template>

            </el-table-column>
            <el-table-column label="病害编码" align="center" prop="diseaseCode"  v-if="computedColumns('病害编码')"/>
            <el-table-column label="病害类型" align="center" prop="diseaseName"  v-if="computedColumns('病害类型')"/>
            <el-table-column label="示例图像" align="center" prop="exampleImage" width="100"  v-if="computedColumns('示例图像')">
              <template slot-scope="scope">
                <!--                <image-preview :src="scope.row.exampleImage" :width="40" :height="40"/>-->
                <image-preview :owner-id="scope.row.exampleImage" :width="40" :height="40" :key="scope.row.id"/>
              </template>
            </el-table-column>
            <el-table-column label="桩号计算方式" align="center" prop="pileMode" v-if="computedColumns('桩号计算方式')">
              <template slot-scope="scope">
                <dict-tag :options="dict.type.sys_pile_mode" :value="scope.row.pileMode"/>
              </template>
            </el-table-column>
            <el-table-column label="排序" align="center" prop="sort"  v-if="computedColumns('排序')"/>
            <el-table-column label="标记颜色" align="center" prop="color" v-if="computedColumns('标记颜色')">
              <template slot-scope="scope">
                <el-color-picker v-model="scope.row.color" size="mini" disabled></el-color-picker>
                <!--                <div :style="`width: 40px;height: 40px;border-radius: 5px;background: ${scope.row.color};margin: auto`"></div>-->
              </template>
            </el-table-column>
            <el-table-column
              label="操作"
              fixed="right"
              align="center"
              width="180"
              class-name="small-padding fixed-width"
            >
              <template slot-scope="scope" v-if="scope.row.userId !== 1">
                <el-button
                  size="mini"
                  type="text"
                  icon="el-icon-edit"
                  @click="handleUpdate(scope.row)"
                  v-hasPermi="['system:diseases:edit']"
                >修改
                </el-button>
                <el-button
                  size="mini"
                  type="text"
                  icon="el-icon-delete"
                  @click="handleDelete(scope.row)"
                  v-hasPermi="['system:diseases:remove']"
                >删除
                </el-button>
                <el-button
                  size="mini"
                  type="text"
                  icon="el-icon-setting"
                  @click="onDetail(scope.row)"
                  v-hasPermi="['system:diseaseAdvices:query']"
                >措施
                </el-button>
              </template>
            </el-table-column>
          </el-table>

          <pagination
            v-show="total>0"
            :total="total"
            :page.sync="queryParams.pageNum"
            :limit.sync="queryParams.pageSize"
            @pagination="getList"
          />
        </div>
        <!--数据表格结束-->
      </el-col>
    </el-row>

    <!-- 添加或修改资产病害信息对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="60%" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="96px" style="min-height: 400px">
        <el-col :span="12">
          <el-form-item label="资产名称" prop="assetName">
            <!--                          <el-input v-model="form.assetName" placeholder="请输入资产名称" />-->
            <dict-select type="sys_asset_type" ref="assetNameEditSelect" only-tail-node v-model="form.assetName"
                         placeholder="请选择资产名称"></dict-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="资产类型" prop="assetType">
            <dict-select type="sys_asset_type" disabled v-model="form.assetType"
                         placeholder="请选择资产类型"></dict-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="病害类型" prop="diseaseName">
            <el-input v-model="form.diseaseName" placeholder="请输入病害类型"/>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="病害编码" prop="diseaseCode">
            <el-input v-model="form.diseaseCode" placeholder="请输入病害编码"/>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="示例图像" prop="exampleImage">
            <!--            <image-upload v-model="form.exampleImage" :limit="1" :isShowTip="false"/>-->
            <image-upload v-model="form.exampleImage" :limit="1"/>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="桩号计算方式" prop="pileMode">
            <el-select v-model="form.pileMode" placeholder="请选择桩号计算方式" style="width: 100%">
              <el-option
                v-for="dict in dict.type.sys_pile_mode"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="排序" prop="sort">
            <el-input v-model="form.sort" placeholder="请输入排序"/>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="标记颜色" prop="color">
            <el-color-picker v-model="form.color"></el-color-picker>
          </el-form-item>
        </el-col>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 用户导入对话框 -->
    <el-dialog :title="upload.title" :visible.sync="upload.open" width="400px" append-to-body>
      <el-upload
        ref="upload"
        :limit="1"
        accept=".xlsx, .xls"
        :headers="upload.headers"
        :action="upload.url + '?updateSupport=' + upload.updateSupport"
        :disabled="upload.isUploading"
        :on-progress="handleFileUploadProgress"
        :on-success="handleFileSuccess"
        :auto-upload="false"
        drag
      >
        <i class="el-icon-upload"></i>
        <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
        <div class="el-upload__tip text-center" slot="tip">
          <div class="el-upload__tip" slot="tip">
            <el-checkbox v-model="upload.updateSupport"/>
            是否更新已经存在的用户数据
          </div>
          <span>仅允许导入xls、xlsx格式文件。</span>
          <el-link type="primary" :underline="false" style="font-size:12px;vertical-align: baseline;"
                   @click="importTemplate">下载模板
          </el-link>
        </div>
      </el-upload>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitFileForm">确 定</el-button>
        <el-button @click="upload.open = false">取 消</el-button>
      </div>
    </el-dialog>
    <DiseaseDetail :visible.sync="showDetail" :data="currentRow" title="详情" :append-to-body="true"
                   size="40%"></DiseaseDetail>
  </div>
</template>

<script>
import {listDiseases, getDiseases, delDiseases, addDiseases, updateDiseases} from "@/api/patrol/diseases";
import {getToken} from "@/utils/auth";
import DiseaseDetail from "./detail.vue";
import MaintenanceDetail from "@/views/system/maintenanceSection/detail.vue";
// import ImageUpload from "./TempImageUpload.vue";
import ImageUpload from "./ImageUpload.vue";
// import ImagePreview from "./TempImagePreview.vue";
import ImagePreview from "./ImagePreview.vue";
import {delParts} from "@/api/patrol/parts";

export default {
  name: "Diseases",
  dicts: ['sys_pile_mode', 'sys_asset_type'],
  components: {MaintenanceDetail, DiseaseDetail, ImageUpload, ImagePreview},
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: false,
      dictType: [],
      // 总条数
      total: 0,
      currentRow: {},
      showDetail: false,
      // 资产病害信息表格数据
      diseasesList: null,
      // 弹出层标题
      title: "",
      // 部门树选项
      deptOptions: undefined,
      // 是否显示弹出层
      open: false,

      // 表单参数
      form: {},
      defaultProps: {
        children: "children",
        label: "label"
      },
      // 用户导入参数
      upload: {
        // 是否显示弹出层（用户导入）
        open: false,
        // 弹出层标题（用户导入）
        title: "",
        // 是否禁用上传
        isUploading: false,
        // 是否更新已经存在的用户数据
        updateSupport: 0,
        // 设置上传的请求头部
        headers: {Authorization: "Bearer " + getToken()},
        // 上传的地址
        url: process.env.VUE_APP_BASE_API + "/system/user/importData"
      },
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        assetType: null,
        assetName: null,
        diseaseCode: null,
        diseaseName: null,
        exampleImage: null,
        pileMode: null,
        sort: null,
        color: null
      },
      // 列信息
      columns: [
        {key: 0, label: `资产类型`, visible: true},
        {key: 1, label: `资产名称`, visible: true},
        {key: 2, label: `病害编码`, visible: true},
        {key: 3, label: `病害类型`, visible: true},
        {key: 4, label: `示例图像`, visible: true},
        {key: 5, label: `桩号计算方式`, visible: true},
        {key: 6, label: `排序`, visible: true},
        {key: 7, label: `标记颜色`, visible: true}
      ],
      // 表单校验
      rules: {
        assetType: [
          {required: true, message: "资产类型不能为空", trigger: "blur"}
        ],
        assetName: [
          {required: true, message: "资产名称不能为空", trigger: "blur"}
        ],
        diseaseCode: [
          {required: true, message: "病害编码不能为空", trigger: "blur"}
        ],
        diseaseName: [
          {required: true, message: "病害类型不能为空", trigger: "blur"}
        ],


      }
    };
  },
  computed: {
    computedColumns() {
      return (label) => {
        return this.columns.filter(item => item.label === label)[0].visible;
      }
    }
  },
  watch: {
    'form.assetName': {
      handler: function (value) {
        this.$nextTick(() => {
          if (!value) return
          let {dictValue: assetType} = {...this.$refs.assetNameEditSelect?.getParentByValue()}
          this.form.assetType = assetType
          // TODO 后面改用wait
          if (!this.form.assetType) {
            setTimeout(() => {
              let {dictValue: assetType} = {...this.$refs.assetNameEditSelect?.getParentByValue()}
              this.form.assetType = assetType
            }, 1000)
          }
        })
      },
    }
  },
  created() {
    this.getList();
  },
  methods: {
    onDetail(row) {
      this.currentRow = row
      this.showDetail = true
    },
    /** 查询用户列表 */
    getList() {
      this.loading = true;
      listDiseases(this.queryParams).then(response => {
        this.diseasesList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        assetType: null,
        assetName: null,
        diseaseCode: null,
        diseaseName: null,
        exampleImage: null,
        pileMode: null,
        sort: null,
        color: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dateRange = [];
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id);
      this.single = selection.length != 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加资产病害信息";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids;
      getDiseases(id).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改资产病害信息";
      });

    },
    /** 提交按钮 */
    submitForm: function () {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.id != null) {
            updateDiseases(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addDiseases(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {

      console.log(row, 'dd')

      if (row.id) {
        const id = row.id
        this.$modal.confirm('是否确认删除数据项？').then(function () {
          return delDiseases(id);
        }).then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
      } else {
        if (this.ids.length > 0) {
          this.$modal.confirm('是否确认批量删除选中数据？').then( () => {
            let delArray = []
            this.ids.forEach(item => {
              delArray.push(delDiseases(item))
            })
            Promise.all(delArray).then(() => {this.$modal.msgSuccess("删除成功");this.getList()})
          })
        }
      }


      // const id = row.id || this.ids;
      // this.$modal.confirm('是否确认删除选中的数据项？').then(function () {
      //   return delDiseases(id);
      // }).then(() => {
      //   this.getList();
      //   this.$modal.msgSuccess("删除成功");
      // }).catch(() => {
      // });
    },

    /** 导出按钮操作 */
    handleExport() {

      this.download('system/diseases/export', {
        ...this.queryParams
      }, `diseases_${new Date().getTime()}.xlsx`)

    },
    /** 导入按钮操作 */
    handleImport() {
      this.upload.title = "用户导入";
      this.upload.open = true;
    },
    /** 下载模板操作 */
    importTemplate() {
      this.download('system/user/importTemplate', {}, `user_template.xlsx`)
    },
    // 文件上传中处理
    handleFileUploadProgress(event, file, fileList) {
      this.upload.isUploading = true;
    },
    // 文件上传成功处理
    handleFileSuccess(response, file, fileList) {
      this.upload.open = false;
      this.upload.isUploading = false;
      this.$refs.upload.clearFiles();
      this.$alert("<div style='overflow: auto;overflow-x: hidden;max-height: 70vh;padding: 10px 20px 0;'>" + response.msg + "</div>", "导入结果", {dangerouslyUseHTMLString: true});
      this.getList();
    },
    // 提交上传文件
    submitFileForm() {
      this.$refs.upload.submit();
    }
  }
};
</script>
<style>
.hasTagsView .app-main[data-v-078753dd] {
  background: #f5f7fa;
}

.tableDiv {
  background-color: white;
  padding-bottom: 10px;
}
</style>
