{"name": "ruoyi", "version": "3.6.4", "description": "养护系统", "author": "若依", "license": "MIT", "scripts": {"dev": "node --max_old_space_size=4096 node_modules/@vue/cli-service/bin/vue-cli-service.js serve", "build:prod": "vue-cli-service build", "build:stage": "vue-cli-service build --mode staging", "preview": "node build/index.js --preview", "lint": "eslint --ext .js,.vue src"}, "husky": {"hooks": {"pre-commit": "lint-staged"}}, "lint-staged": {"src/**/*.{js,vue}": ["eslint --fix", "git add"]}, "keywords": ["vue", "admin", "dashboard", "element-ui", "boilerplate", "admin-template", "management-system"], "repository": {"type": "git", "url": "https://gitee.com/y_project/RuoYi-Cloud.git"}, "dependencies": {"@achrinza/node-ipc": "^10.1.11", "@amap/amap-jsapi-loader": "^1.0.1", "@fortawesome/fontawesome-svg-core": "^6.7.2", "@fortawesome/free-solid-svg-icons": "^6.7.2", "@fortawesome/vue-fontawesome": "^2.0.10", "@geoscene/core": "4.23.14", "@microsoft/signalr": "^7.0.7", "@riophae/vue-treeselect": "0.4.0", "@turf/turf": "^7.1.0", "@vue-office/docx": "^1.6.2", "@vue-office/excel": "^1.7.8", "@vue-office/pdf": "^2.0.2", "@vue/composition-api": "^1.7.2", "axios": "0.24.0", "bimfacesdkloader": "^0.1.0", "bpmn-js": "^7.3.1", "bpmn-js-bpmnlint": "^0.15.0", "bpmn-js-properties-panel": "^0.37.2", "bpmn-moddle": "^7.1.3", "bpmnlint": "^6.4.0", "bpmnlint-loader": "^0.1.4", "camunda-bpmn-moddle": "^7.0.1", "clipboard": "2.0.8", "crypto-js": "^4.2.0", "decimal.js": "^10.5.0", "echarts": "5.4.0", "element-ui": "2.15.14", "esri-loader": "^3.7.0", "ezuikit-js": "^0.7.2", "file-drops": "^0.4.0", "file-saver": "^2.0.5", "fuse.js": "6.4.3", "gsap": "^3.12.2", "highlight.js": "9.18.5", "inherits": "^2.0.4", "js-beautify": "1.13.0", "js-cookie": "3.0.1", "js-pinyin": "^0.2.7", "jsencrypt": "3.0.0-rc.1", "lodash": "^4.0.0", "mathjs": "^14.4.0", "min-dom": "^3.2.1", "moment": "^2.30.1", "nprogress": "0.2.0", "ol": "^6.15.1", "ol-ext": "^4.0.24", "pkcs7": "^1.0.4", "quill": "1.3.7", "screenfull": "5.0.2", "sortablejs": "1.10.2", "uuid": "^10.0.0", "v-scale-screen": "^1.0.3", "video.js": "^8.16.1", "videojs-contrib-hls": "^5.15.0", "vue": "2.6.12", "vue-count-to": "1.0.13", "vue-cropper": "0.5.5", "vue-demi": "^0.14.6", "vue-json-editor": "^1.4.3", "vue-meta": "2.4.0", "vue-router": "3.4.9", "vue-seamless-scroll": "^1.1.23", "vuedraggable": "^2.24.3", "vuex": "3.6.0", "xlsx": "^0.18.5", "xlsx-style": "^0.8.13"}, "devDependencies": {"@vue/cli-plugin-babel": "4.4.6", "@vue/cli-plugin-eslint": "4.4.6", "@vue/cli-service": "4.4.6", "babel-eslint": "10.1.0", "babel-plugin-dynamic-import-node": "2.3.3", "chalk": "4.1.0", "compression-webpack-plugin": "6.1.2", "connect": "3.6.6", "eslint": "7.15.0", "eslint-plugin-vue": "7.2.0", "lint-staged": "10.5.3", "luckyexcel": "^1.0.1", "raw-loader": "^4.0.2", "runjs": "4.4.2", "sass": "1.32.13", "sass-loader": "10.1.1", "script-ext-html-webpack-plugin": "2.1.5", "svg-sprite-loader": "5.1.1", "vue-template-compiler": "2.6.12"}, "engines": {"node": ">=8.9", "npm": ">= 3.0.0"}, "browserslist": ["> 1%", "last 2 versions"]}