<template>
  <div class="card-container">
    <el-card class="tableClass">
      <el-collapse-transition>
        <div v-show="currentPage">
          <div slot="header" class="clearfix">
            <el-tooltip
              class="item"
              effect="dark"
              :content="form.remark"
              placement="top-start"
            >
              <!-- 当鼠标悬停时显示按钮 -->
              <span
                class="hover-area"
                @mouseenter="hovering = true"
                @mouseleave="hovering = false"
              >
                <el-button
                  class="edit-button"
                  type="primary"
                  icon="el-icon-edit"
                  size="mini"
                  v-show="hovering && !isUpload"
                  @click="editForm"
                ></el-button>
                <span
                  class="module-name"
                  v-show="!hovering || isUpload"
                  style="line-height: 28px"
                  >{{ form.moduleName }}</span
                >
              </span>
            </el-tooltip>

            <el-button
              v-if="!isUpload"
              style="float: right; padding: 3px 0"
              type="text"
              @click="createNewItem"
              >新建</el-button
            >
          </div>
          <el-divider></el-divider>
          <div class="text item collapse-container">
            <fileSub
              :list="listFromVuex"
              :active-names.sync="activeNames"
              @updateProcess="updateProcess"
            >
            </fileSub>
          </div>
          <div class="bottom clearfix">
            <el-progress
              :text-inside="true"
              :stroke-width="26"
              :percentage="process"
              :status="progressStatus"
            >
            </el-progress>
            <el-tag type="success"
              >进度为:{{ fileMapLength }} / {{ listLength }}</el-tag
            >
            <el-tag>文件数量: {{ selfFileNum }}</el-tag>
            <el-tag type="info"
              >文件大小: {{ selfTotalFileSize | formatFileSize }}</el-tag
            >
            <!-- <el-button style="float: right" type="text">查看更多</el-button> -->
          </div>
        </div>
      </el-collapse-transition>
      <el-collapse-transition>
        <div v-show="!currentPage">
          <!-- 编辑页面的内容 -->
          <el-page-header @back="editForm" content="编辑页面"></el-page-header>
          <el-form ref="form" :model="form" label-width="80px">
            <el-form-item label="模块名称">
              <el-input v-model="localForm.moduleName"></el-input>
            </el-form-item>
            <el-form-item label="备注">
              <el-input v-model="localForm.remark"></el-input>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="toggleSave" :disabled="false">{{
                "保存"
              }}</el-button>
              <el-button :disabled="false" @click="toggleDelete">{{
                "删除"
              }}</el-button>
            </el-form-item>
          </el-form>
        </div>
      </el-collapse-transition>
    </el-card>
  </div>
</template>

<script>
import collapse from "@/components/ModuleFile/collapse.vue";
import fileSub from "@/components/ModuleFile/fileSub.vue";
import { mapState, mapActions, mapMutations, mapGetters } from "vuex";
import { deleteModule } from "@/api/system/moduleFile.js";
export default {
  name: "card",
  components: {
    collapse,
    fileSub,
  },
  props: {
    form: {
      type: Object,
      required: true,
    },
    cardId: {
      type: Number,
      required: true,
    },
  },
  provide() {
    return {
      cardId: this.cardId,
      initCard: this.initCard,
    };
  },
  inject: ["isUpload"],
  data() {
    return {
      currentPage: true, // 控制显示哪个页面
      list: [],
      hovering: false,
      localForm: JSON.parse(JSON.stringify(this.form)), // 深拷贝 prop
      ModuleFileSub: {
        id: null,
        categoryName: null,
        fileId: null,
        fileType: null,
        sort: null,
      },
      activeNames: "",
      originalForm: {},
      listLength: 0,
      fileMapLength: 0,
      process: 0.0,
      selfTotalFileSize: 0.0,
      selfFileNum: 0,
    };
  },
  created() {
    this.initCard();
  },
  methods: {
    ...mapActions("file", {
      initCardState: "initCardState",
      addToList: "addToList",
      fetchList: "fetchList",
      deleteList: "deleteList",
    }),
    ...mapMutations("file", {
      changeList: "CHANGE_LIST", // 映射 Vuex mutation
    }),
    initCard() {
      this.initCardState(this.cardId); // 初始化状态
      if (this.form && this.form.id) {
        this.fetchList({
          cardId: this.cardId,
          entity: { moduleId: this.form.id },
        });
      }
      this.originalForm = { ...this.form };
    },
    createNewItem() {
      // 创建一个新的 item 对象
      const newItem = {
        categoryName: "新分类",
        moduleId: this.cardId,
        fileType: "",
        sort: 10,
      };
      this.changeList({ cardId: this.cardId });
      // 调用 Vuex action 添加到 list
      this.addToList({ cardId: this.cardId, newItem: newItem });
      this.activeNames = `${this.listFromVuex.length - 1}`;
    },
    editForm() {
      this.currentPage = !this.currentPage;
      this.localForm = { ...this.originalForm };
    },
    toggleSave() {
      this.saveForm().then(() => {
        this.currentPage = !this.currentPage;
        this.originalForm = { ...this.localForm };
        this.$message.success("保存成功");
      });
    },
    toggleDelete() {
      this.$confirm("此操作将永久删除该文件, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          this.deleteForm();
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消删除",
          });
        });
    },
    deleteForm() {
      // 调用删除接口
      deleteModule(this.form.id).then(() => {
        this.deleteList({ cardId: this.cardId });
        this.$emit("refresh");
        this.$message.success("删除成功");
      });
    },
    updateProcess() {
      this.listLength = this.listFromVuex.length;
      this.fileMapLength = Object.keys(this.listFileMap).length;
      this.process =
        this.listLength > 0 ? (this.fileMapLength / this.listLength) * 100 : 0;
      this.selfTotalFileSize = this.getSelfTotalFileSize();
      this.selfFileNum = this.getSelfFileNum();
      this.$emit("updateBox");
    },
    getSelfTotalFileSize() {
      return this.getTotalFileSize(this.cardId);
    },
    getSelfFileNum() {
      return this.getFileNum(this.cardId);
    },
  },
  computed: {
    ...mapState("file", {
      listFromVuex(state) {
        return (
          (state.cards[this.cardId] && state.cards[this.cardId].list) || []
        );
      },
      listFileMap(state) {
        return (
          (state.cards[this.cardId] && state.cards[this.cardId].fileMap) || {}
        );
      },
    }),
    ...mapGetters("file", {
      getTotalFileSize: "getTotalFileSize",
      getFileNum: "getFileNum",
    }),
    progressStatus() {
      if (this.process === 100) {
        return "success";
      } else if (this.process >= 50) {
        return null;
      } else if (this.process < 50) {
        return "warning";
      } else if (this.process === 0) {
        return "exception";
      } else {
        return "exception";
      }
    },
  },
};
</script>

<style scope>
.text {
  font-size: 14px;
}

.item {
  margin-bottom: 18px;
}

.clearfix:before,
.clearfix:after {
  display: table;
  content: "";
}
.clearfix:after {
  clear: both;
}

.bottom {
  margin-top: 13px;
  line-height: 12px;
}
.el-card {
  height: 100%;
}
.collapse-container {
  height: 300px; /* 设置固定高度 */
  overflow-y: auto; /* 显示垂直滚动条 */
}
</style>
