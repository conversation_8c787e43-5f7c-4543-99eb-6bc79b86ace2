<template>
  <div class="app-container">
    <el-row :gutter="20">

      <!--筛选区开始-->
      <el-col :span="24" :xs="24">
        <el-row>
          <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" label-width="68px">
            <el-col :span="24">
              <el-form-item label="" prop="managementMaintenanceId">
                <selectTree v-model="queryParams.managementMaintenanceId" :deptType="201" clearable
                  @change="deptChange(queryParams.managementMaintenanceId)" filterable placeholder="管理处"
                  style="width: 240px" />
              </el-form-item>
              <el-form-item label="" prop="routeSectionId">
                <el-select v-model="queryParams.routeSectionId" filterable placeholder="请选择养护路段" style="width: 240px"
                  @change="managementChange(queryParams.routeSectionId)">
                  <el-option v-for="item in maintenanceSectionList" :key="item.maintenanceSectionId"
                    :label="item.maintenanceSectionName" :value="item.maintenanceSectionId"></el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="" prop="routeCode">
                <el-select v-model="queryParams.routeCode" placeholder="请选择路线编码" clearable filterable
                  style="width: 240px">
                  <el-option v-for="item in routeList" :key="item.routeId" :label="item.routeName"
                    :value="item.routeCode">
                  </el-option>
                </el-select>
              </el-form-item>
              <!-- <el-form-item label="创建时间" prop="createTime">
                <el-date-picker
                    v-model="dateRange"
                    style="width: 240px"
                    value-format="yyyy-MM-dd"
                    type="daterange"
                    range-separator="-"
                    start-placeholder="开始日期"
                    end-placeholder="结束日期"
                ></el-date-picker>
              </el-form-item> -->
              <el-form-item>
                <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
                <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
                <!-- <el-button v-show="!showSearch" @click="showSearch=true" icon="el-icon-arrow-down" circle></el-button>
                <el-button v-show="showSearch" @click="showSearch=false" icon="el-icon-arrow-up" circle></el-button> -->
              </el-form-item>
            </el-col>
          </el-form>
        </el-row>
        <!--筛选区结束-->


        <!--操作按钮区开始-->
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button type="primary" icon="el-icon-plus" size="mini" @click="handleAdd"
              v-hasPermi="['middleData:riskSections:add']">新增</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="success" icon="el-icon-edit" size="mini" :disabled="single" @click="handleUpdate"
              v-hasPermi="['middleData:riskSections:edit']">修改</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="danger" icon="el-icon-delete" size="mini" :disabled="multiple" @click="handleDelete"
              v-hasPermi="['middleData:riskSections:remove']">删除</el-button>
          </el-col>
          <!--          <el-col :span="1.5">-->
          <!--            <el-button-->
          <!--                type="info"-->
          <!--                plain-->
          <!--                icon="el-icon-upload2"-->
          <!--                size="mini"-->
          <!--                @click="handleImport"-->
          <!--                v-hasPermi="['middleData:riskSections:export']"-->
          <!--            >导入</el-button>-->
          <!--          </el-col>-->
          <el-col :span="1.5">
            <el-button type="warning" icon="el-icon-download" size="mini" @click="handleExport"
              v-hasPermi="['middleData:riskSections:export']">导出清单</el-button>
          </el-col>
          <right-toolbar :showSearch.sync="showSearch" @queryTable="getList" :columns="columns"></right-toolbar>
        </el-row>
        <!--操作按钮区结束-->

        <!--数据表格开始-->
        <div class="tableDiv">
          <el-table v-adjust-table ref="table" size="mini"
            :height="showSearch ? 'calc(100vh - 320px)' : 'calc(100vh - 260px)'" style="width: 100%" v-loading="loading"
            border :data="riskSectionsList" @selection-change="handleSelectionChange" :row-style="rowStyle"
            @row-click="handleRowClick">
            <el-table-column type="selection" width="50" align="center" />
            <el-table-column fixed label="序号" type="index" width="50">
              <template v-slot="scope">
                {{ (scope.$index + (queryParams.pageNum - 1) * queryParams.pageSize) + 1 }}
              </template>
            </el-table-column>
            <el-table-column label="省份" align="center" prop="province" v-if="cmpColumns('省份')" />
            <el-table-column label="市/州" align="center" prop="cityName" width="80" show-overflow-tooltip
              v-if="cmpColumns('市/州')" />
            <el-table-column label="路线编码" align="center" prop="routeCode" v-if="cmpColumns('路线编码')" />
            <el-table-column label="路线名称" align="center" prop="routeName" v-if="cmpColumns('路线名称')" />
            <el-table-column label="疑似风险路段" align="center" prop="potentialRiskSegment" width="160"
              v-if="cmpColumns('疑似风险路段')" />
            <el-table-column label="起点桩号" align="center" prop="startStake" v-if="cmpColumns('起点桩号')">
              <template slot-scope="scope">
                {{ formatPile(scope.row.startStake) }}
              </template>
            </el-table-column>
            <el-table-column label="终点桩号" align="center" prop="endStake" v-if="cmpColumns('终点桩号')">
              <template slot-scope="scope">
                {{ formatPile(scope.row.endStake) }}
              </template>
            </el-table-column>
            <el-table-column label="灾害点数量" align="center" prop="disasterCount" width="100" v-if="cmpColumns('灾害点数量')">
              <template slot-scope="scope">
                {{ scope.row.disasterCount || 0 }}
              </template>
            </el-table-column>
            <el-table-column label="路段长度(km)" align="center" prop="length" width="120" v-if="cmpColumns('路段长度(km)')" />
            <el-table-column label="路段" align="center" prop="routeSectionName" v-if="cmpColumns('路段')" />
            <el-table-column label="管理处" align="center" prop="managementMaintenanceName" v-if="cmpColumns('管理处')" />
            <el-table-column label="权属单位" align="center" prop="propertyUnitType" v-if="cmpColumns('权属单位')" />
            <el-table-column label="中心点经度" align="center" prop="centerLon" width="120" v-if="cmpColumns('中心点经度')" />
            <el-table-column label="中心点纬度" align="center" prop="centerLat" width="120" v-if="cmpColumns('中心点纬度')" />
            <!--            <el-table-column label="空间信息" align="center" prop="shape" />-->
            <el-table-column label="类型" align="center" prop="type" v-if="cmpColumns('类型')">
              <template slot-scope="scope">
                {{ scope.row.type || '/' }}
              </template>
            </el-table-column>
            <el-table-column label="排查状态" align="center" prop="status" v-if="cmpColumns('排查状态')">
              <template slot-scope="scope">
                <span v-if="scope.row.status === 0">待排查</span>
                <span v-if="scope.row.status === 1">已排查</span>
                <span v-if="scope.row.status === 2">排查中</span>
              </template>
            </el-table-column>
            <el-table-column label="风险等级" align="center" prop="riskLevel" width="100" show-overflow-tooltip
              v-if="cmpColumns('风险等级')">
              <template slot-scope="scope">
                <span>{{ formatRiskLevel(scope.row.riskLevel) }}</span>
              </template>
            </el-table-column>
            <el-table-column label="路段风险描述" align="center" prop="riskDescription" show-overflow-tooltip width="150"
              v-if="cmpColumns('路段风险描述')" />
            <el-table-column label="是否已采取措施" align="center" prop="measuresTaken" width="120" show-overflow-tooltip
              v-if="cmpColumns('是否已采取措施')">
              <template slot-scope="scope">
                <span>{{ formatMeasures(scope.row.measuresTaken) }}</span>
              </template>
            </el-table-column>
            <el-table-column label="已(拟)采取的措施" align="center" prop="measuresDetail" show-overflow-tooltip width="150"
              v-if="cmpColumns('已(拟)采取的措施')" />
            <el-table-column label="省级责任单位及人员" align="center" prop="provincialResponsible" width="150"
              show-overflow-tooltip v-if="cmpColumns('省级责任单位及人员')" />
            <el-table-column label="复核责任单位及人员" align="center" prop="reviewResponsible" width="150" show-overflow-tooltip
              v-if="cmpColumns('复核责任单位及人员')" />
            <el-table-column label="排查责任单位及人员" align="center" prop="inspectionResponsible" width="150"
              show-overflow-tooltip v-if="cmpColumns('排查责任单位及人员')" />
            <el-table-column label="创建时间" align="center" prop="createTime" width="150" show-overflow-tooltip
              v-if="cmpColumns('创建时间')" />
            <el-table-column label="操作" fixed="right" align="center" width="120" class-name="small-padding fixed-width">
              <template slot-scope="scope" v-if="scope.row.userId !== 1">
                <el-button size="mini" type="text" icon="el-icon-edit" @click="handleUpdate(scope.row)"
                  v-hasPermi="['middleData:riskSections:edit']">修改</el-button>
                <el-button size="mini" type="text" icon="el-icon-delete" @click="handleDelete(scope.row)"
                  v-hasPermi="['middleData:riskSections:remove']">删除</el-button>
              </template>
            </el-table-column>
          </el-table>

          <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum"
            :limit.sync="queryParams.pageSize" @pagination="getList" />
        </div>
        <!--数据表格结束-->
      </el-col>
    </el-row>

    <!-- 添加或修改疑似风险路段清单对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="80%" max-height="50%" :close-on-press-escape="false"
      :close-on-click-modal="false" append-to-body class="formDialog">
      <el-form ref="form" :model="form" :rules="rules" label-width="125px" label-position="right">
        <div class="infoBox" style="margin-bottom: 20px;" v-loading="loading">
          <div class="infoTitle">基础信息</div>
          <el-form-item label="省份" prop="province" class="inline-form-item">
            <el-input v-model="form.province" disabled />
          </el-form-item>
          <el-form-item label="市/州" prop="cityName" class="inline-form-item">
            <el-select v-model="form.cityName" placeholder="请选择市/州" style="width: 100%;">
              <el-option v-for="item in cityList" :key="item" :label="item" :value="item">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="管理处" prop="managementMaintenanceId" class="inline-form-item">
            <selectTree v-model="form.managementMaintenanceId" :deptType="201" clearable
              @change="deptChange(form.managementMaintenanceId)" filterable placeholder="请选择管理处" style="width: 100%"
              @node-selected="handleNodeSelected" />
          </el-form-item>
          <el-form-item label="路段名称" prop="routeSectionId" class="inline-form-item">
            <el-select v-model="form.routeSectionId" filterable placeholder="请选择养护路段"
              @change="managementChange(form.routeSectionId)" style="width: 100%">
              <el-option v-for="item in maintenanceSectionList" :key="item.maintenanceSectionId"
                :label="item.maintenanceSectionName" :value="item.maintenanceSectionId"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="路线编码" prop="routeCode" class="inline-form-item">
            <el-select v-model="form.routeCode" placeholder="路线编码" clearable filterable style="width: 100%"
              collapse-tags>
              <el-option v-for="item in routeList" :key="item.routeId" :label="item.routeName" :value="item.routeCode">
              </el-option>
            </el-select>
          </el-form-item>
        </div>
        <div class="infoBox" style="margin-bottom: 20px;" v-loading="loading">
          <div class="infoTitle">风险信息</div>
          <el-form-item label="起点桩号" prop="startStake" class="inline-form-item">
            <PileInput v-model="form.startStake" placeholder="" />
          </el-form-item>
          <el-form-item label="终点桩号" prop="endStake" class="inline-form-item">
            <PileInput v-model="form.endStake" placeholder="" />
          </el-form-item>
          <el-form-item label="路段长度(km)" prop="length" class="inline-form-item">
            <el-input v-model="form.length" placeholder="请输入路段长度(km)" />
          </el-form-item>
          <!--          <el-form-item label="灾害点数量" prop="disasterCount" class="inline-form-item">-->
          <!--            <el-input v-model="form.disasterCount" type="number" placeholder="请输入灾害点数量" />-->
          <!--          </el-form-item>-->
          <el-form-item label="中心点经纬度" prop="centerLon" class="inline-form-item">
            <lon-lat type="lonlat" :lon.sync="form.centerLon" :lat.sync="form.centerLat" />
          </el-form-item>
          <el-form-item label="中心点经纬度" prop="centerLat" style="display: none">
            <el-input v-model="form.centerLat" style="display: none" />
          </el-form-item>
          <el-form-item label="风险等级" prop="riskLevel" class="inline-form-item">
            <el-select v-model="form.riskLevel" placeholder="请选择风险等级" clearable style="width: 100%">
              <el-option v-for="dict in riskLevelOptions" :key="dict.dictValue" :label="dict.dictLabel"
                :value="dict.dictValue" />
            </el-select>
          </el-form-item>
          <el-form-item label="是否已采取措施" prop="measuresTaken" class="inline-form-item">
            <el-select v-model="form.measuresTaken" placeholder="请选择是否已采取措施" clearable style="width: 100%">
              <el-option v-for="dict in measuresOptions" :key="dict.dictValue" :label="dict.dictLabel"
                :value="dict.dictValue" />
            </el-select>
          </el-form-item>
          <el-form-item label="路段风险描述" prop="riskDescription" class="inline-form-item">
            <el-input v-model="form.riskDescription" type="textarea" :rows="3" placeholder="请输入内容" />
          </el-form-item>
          <el-form-item label="已(拟)采取的措施" prop="measuresDetail" class="inline-form-item">
            <el-input v-model="form.measuresDetail" type="textarea" :rows="3" placeholder="请输入内容" />
          </el-form-item>
          <!--          <el-form-item label="空间信息" prop="shape" class="inline-form-item">-->
          <!--            <el-input v-model="form.shape" type="textarea" placeholder="请输入内容" />-->
          <!--          </el-form-item>-->
        </div>
        <div class="infoBox" style="margin-bottom: 20px;" v-loading="loading">
          <div class="infoTitle">责任信息</div>
          <el-form-item label="省级责任单位及人员" prop="provincialResponsible" class="inline-form-item">
            <el-input v-model="form.provincialResponsible" placeholder="请输入省级责任单位及人员" />
          </el-form-item>
          <el-form-item label="复核责任单位及人员" prop="reviewResponsible" class="inline-form-item">
            <el-input v-model="form.reviewResponsible" placeholder="请输入复核责任单位及人员" />
          </el-form-item>
          <el-form-item label="排查责任单位及人员" prop="inspectionResponsible" class="inline-form-item">
            <el-input v-model="form.inspectionResponsible" placeholder="请输入排查责任单位及人员" />
          </el-form-item>
        </div>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 用户导入对话框 -->
    <el-dialog :title="upload.title" :visible.sync="upload.open" width="400px" append-to-body>
      <el-upload ref="upload" :limit="1" accept=".xlsx, .xls" :headers="upload.headers"
        :action="upload.url + '?updateSupport=' + upload.updateSupport" :disabled="upload.isUploading"
        :on-progress="handleFileUploadProgress" :on-success="handleFileSuccess" :auto-upload="false" drag>
        <i class="el-icon-upload"></i>
        <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
        <div class="el-upload__tip text-center" slot="tip">
          <div class="el-upload__tip" slot="tip">
            <el-checkbox v-model="upload.updateSupport" /> 是否更新已经存在的用户数据
          </div>
          <span>仅允许导入xls、xlsx格式文件。</span>
          <el-link type="primary" :underline="false" style="font-size:12px;vertical-align: baseline;"
            @click="importTemplate">下载模板</el-link>
        </div>
      </el-upload>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitFileForm">确 定</el-button>
        <el-button @click="upload.open = false">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listRiskSections, getRiskSections, delData, addRiskSections, updateRiskSections } from "@/api/middleData/riskSections";
import { getToken } from "@/utils/auth";
import { listMaintenanceSectionAll } from '@/api/system/maintenanceSection'
import selectTree from '@/components/DeptTmpl/selectTree.vue'
import PileInput from '@/views/system/route/pileinput.vue'
import lonLat from '@/components/mapPosition/lonLat.vue'
import { listByMaintenanceSectionId } from '@/api/baseData/common/routeLine'
import { listAllRoute } from '@/api/system/route'

export default {
  name: "RiskSections",
  components: { lonLat, PileInput, selectTree },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: false,
      dictType: [],
      // 日期范围
      dateRange: [],
      // 总条数
      total: 0,
      // 疑似风险路段清单表格数据
      riskSectionsList: null,
      maintenanceSectionList: [],
      routeList: [],
      // 弹出层标题
      title: "",
      // 部门树选项
      deptOptions: undefined,
      // 是否显示弹出层
      open: false,

      // 表单参数
      form: {},
      defaultProps: {
        children: "children",
        label: "label"
      },
      // 用户导入参数
      upload: {
        // 是否显示弹出层（用户导入）
        open: false,
        // 弹出层标题（用户导入）
        title: "",
        // 是否禁用上传
        isUploading: false,
        // 是否更新已经存在的用户数据
        updateSupport: 0,
        // 设置上传的请求头部
        headers: { Authorization: "Bearer " + getToken() },
        // 上传的地址
        url: process.env.VUE_APP_BASE_API + "/system/user/importData"
      },
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 50,
        province: null,
        routeCode: null,
        routeName: null,
        startStake: null,
        endStake: null,
        disasterCount: null,
        length: null,
        routeSectionName: null,
        managementMaintenanceName: null,
        propertyUnitType: null,
        centerLon: null,
        centerLat: null,
        shape: null,
        managementMaintenanceId: null,
        routeSectionId: null,
        type: null,
        potentialRiskSegment: null,
        status: null
      },
      // 列信息
      columns: [
        { key: 0, label: `省份`, visible: true },
        { key: 1, label: `市/州`, visible: true },
        { key: 2, label: `路线编码`, visible: true },
        { key: 3, label: `路线名称`, visible: true },
        { key: 4, label: `疑似风险路段`, visible: true },
        { key: 5, label: `起点桩号`, visible: true },
        { key: 6, label: `终点桩号`, visible: true },
        { key: 7, label: `灾害点数量`, visible: true },
        { key: 8, label: `路段长度(km)`, visible: true },
        { key: 9, label: `路段`, visible: true },
        { key: 10, label: `管理处`, visible: true },
        { key: 11, label: `权属单位`, visible: true },
        { key: 12, label: `中心点经度`, visible: true },
        { key: 13, label: `中心点纬度`, visible: true },
        { key: 14, label: `类型`, visible: true },
        { key: 15, label: `排查状态`, visible: true },
        { key: 16, label: `风险等级`, visible: true },
        { key: 17, label: `路段风险描述`, visible: true },
        { key: 18, label: `是否已采取措施`, visible: true },
        { key: 19, label: `已(拟)采取的措施`, visible: true },
        { key: 20, label: `省级责任单位及人员`, visible: true },
        { key: 21, label: `复核责任单位及人员`, visible: true },
        { key: 22, label: `排查责任单位及人员`, visible: true },
        { key: 23, label: `创建时间`, visible: true },
      ],
      // 表单校验
      rules: {
        cityName: [
          { required: true, message: "州/市不能为空", trigger: ["blur", "change"] }
        ],
        managementMaintenanceId: [
          { required: true, message: "管理处不能为空", trigger: ["blur", "change"] }
        ],
        routeSectionId: [
          { required: true, message: "路段名称不能为空", trigger: ["blur", "change"] }
        ],
        routeCode: [
          { required: true, message: "路线编码不能为空", trigger: ["blur", "change"] }
        ],
        startStake: [
          { required: true, message: "起点桩号不能为空", trigger: "blur" }
        ],
        endStake: [
          { required: true, message: "终点桩号不能为空", trigger: "blur" }
        ],
        length: [
          { required: true, message: "路段长度不能为空", trigger: "blur" }
        ],
        disasterCount: [
          { required: true, message: "灾害点数量不能为空", trigger: "blur" }
        ],
        centerLon: [
          { required: true, message: "中心点经纬度不能为空", trigger: "blur" }
        ],
        centerLat: [
          { required: true, message: "中心点经纬度不能为空", trigger: "blur" }
        ],
        riskLevel: [
          { required: true, message: "风险等级不能为空", trigger: ["blur", "change"] }
        ],
        measuresTaken: [
          { required: true, message: "是否已采取措施不能为空", trigger: ["blur", "change"] }
        ],
      },
      // 风险等级字典
      riskLevelOptions: [
        { dictLabel: '无', dictValue: 5 },
        { dictLabel: '低风险', dictValue: 4 },
        { dictLabel: '中风险', dictValue: 3 },
        { dictLabel: '较高风险', dictValue: 2 },
        { dictLabel: '高风险', dictValue: 1 },
      ],
      // 是否采取措施字典
      measuresOptions: [
        { dictLabel: '是', dictValue: 1 },
        { dictLabel: '否', dictValue: 0 },
      ],
      cityList: [
        '昆明市', '曲靖市', '玉溪市', '保山市', '昭通市', '丽江市', '普洱市', '临沧市',
        '楚雄彝族自治州', '红河哈尼族彝族自治州', '文山壮族苗族自治州',
        '西双版纳傣族自治州', '大理白族自治州', '德宏傣族景颇族自治州',
        '怒江傈僳族自治州', '迪庆藏族自治州'
      ],
    };
  },
  watch: {
    // 根据名称筛选部门树
  },
  created() {
    this.getList();
    this.getMaintenanceSection();
    this.listAllRoute();
    // this.getDeptTree();
    // this.getConfigKey("sys.user.initPassword").then(response => {
    //   this.initPassword = response.msg;
    // });
  },
  computed: {
    cmpColumns() {
      return (val) => {
        let el = this.columns.find(item => item.label === val)
        if (el) {
          return el.visible
        } else {
          return true
        }
      }
    }
  },
  methods: {
    /** 查询用户列表 */
    getList() {
      this.loading = true;
      // this.queryParams.createTimee = this.dateRange[0]
      // this.queryParams.createTimes = this.dateRange[1]
      listRiskSections(this.queryParams).then(response => {
        this.riskSectionsList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    /** 查询养护路段下拉列表 */
    getMaintenanceSection() {
      listMaintenanceSectionAll().then((res) => {
        this.maintenanceSectionList = res.data;
      });
    },
    //管理处下拉选点击事件
    deptChange(departmentId) {
      this.queryParams.routeSectionId = null;
      this.queryParams.routeCode = null;
      this.form.routeSectionId = null;
      this.form.routeCode = null;
      listMaintenanceSectionAll({ departmentId: departmentId }).then((res) => {
        this.maintenanceSectionList = res.data;
      });
    },
    listAllRoute() {
      listAllRoute().then((res) => {
        if (res.code == 200) {
          this.routeList = res.data || [];
        }
      })
    },
    //路段下拉选改变事件
    managementChange(routeSectionId) {
      this.queryParams.routeCode = null;
      this.form.routeCode = null;
      listByMaintenanceSectionId({ maintenanceSectionId: routeSectionId }).then((res) => {
        if (res.code == 200) {
          res.data.forEach((item) => {
            item.routeName = item.routeName + '(' + item.routeCode + ')';
          });
          this.routeList = res.data || [];
        }
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        province: null,
        routeCode: null,
        routeName: null,
        startStake: null,
        endStake: null,
        disasterCount: null,
        length: null,
        routeSectionName: null,
        managementMaintenanceName: null,
        propertyUnitType: null,
        centerLon: null,
        centerLat: null,
        shape: null,
        managementMaintenanceId: null,
        routeSectionId: null,
        type: null,
        potentialRiskSegment: null,
        status: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dateRange = [];
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id);
      this.single = selection.length != 1;
      this.multiple = !selection.length;
    },
    // 表格点击勾选
    handleRowClick(row) {
      row.isSelected = !row.isSelected;
      this.$refs.table.toggleRowSelection(row);
    },
    // 勾选高亮
    rowStyle({ row, rowIndex }) {
      if (this.ids.includes(row.id)) {
        return { 'background-color': '#E1F0FF', color: '#333' }
      } else {
        return { 'background-color': '#fff', color: '#333' }
      }
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.form.province = "云南省";
      this.open = true;
      this.title = "添加疑似风险路段清单";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids;
      getRiskSections(id).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改疑似风险路段清单";
      });

    },
    handleNodeSelected(node) {
      this.form.managementMaintenanceId = node.id;
      this.form.managementMaintenanceName = node.label; // 获取对应的 name
    },
    // 格式化风险等级显示
    formatRiskLevel(riskLevel) {
      const dict = this.riskLevelOptions.find((item) => item.dictValue === riskLevel);
      return dict ? dict.dictLabel : riskLevel;
    },
    // 格式化是否采取措施显示
    formatMeasures(status) {
      const dict = this.measuresOptions.find((item) => item.dictValue === status);
      return dict ? dict.dictLabel : status;
    },
    /** 提交按钮 */
    submitForm: function () {
      this.$refs["form"].validate(valid => {
        if (valid) {
          // 获取路线名称
          const route = this.routeList.find(item => item.routeCode === this.form.routeCode);
          this.form.routeName = route.routeName;
          // 获取路段名称
          const maintenanceSection = this.maintenanceSectionList.find(item => item.maintenanceSectionId === this.form.routeSectionId);
          this.form.routeSectionName = maintenanceSection.maintenanceSectionName;
          console.log("from: ", this.form);
          if (this.form.id != null) {
            updateRiskSections(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addRiskSections(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const id = row.id || this.ids;
      if (row.id) { // 单个删除
        this.$modal.confirm('是否确认删除改数据？').then(function () {
          let data = { idList: [row.id] };
          return delData(data);
        }).then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        }).catch(() => { });
      } else { // 批量删除
        this.$modal.confirm(`是否确认删除选中的 ${id.length} 条数据项？`).then(function () {
          let data = { idList: id };
          console.log(data)
          return delData(data);
        }).then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        }).catch(() => { });
      }
    },

    /** 导出按钮操作 */
    handleExport() {
      this.download('middleData/riskSections/export', {
        ...this.queryParams,
      }, `疑似风险点路段清单_${new Date().getTime()}.xlsx`)
    },
    /** 导入按钮操作 */
    handleImport() {
      this.upload.title = "用户导入";
      this.upload.open = true;
    },
    /** 下载模板操作 */
    importTemplate() {
      this.download('system/user/importTemplate', {
      }, `user_template.xlsx`)
    },
    // 文件上传中处理
    handleFileUploadProgress(event, file, fileList) {
      this.upload.isUploading = true;
    },
    // 文件上传成功处理
    handleFileSuccess(response, file, fileList) {
      this.upload.open = false;
      this.upload.isUploading = false;
      this.$refs.upload.clearFiles();
      this.$alert("<div style='overflow: auto;overflow-x: hidden;max-height: 70vh;padding: 10px 20px 0;'>" + response.msg + "</div>", "导入结果", { dangerouslyUseHTMLString: true });
      this.getList();
    },
    // 提交上传文件
    submitFileForm() {
      this.$refs.upload.submit();
    }
  }
};
</script>
<style lang="scss" scoped>
.hasTagsView .app-main[data-v-078753dd] {
  background: #f5f7fa;
}

.tableDiv {
  background-color: white;
  padding-bottom: 10px;
}

.formDialog {
  ::v-deep .el-dialog__body {
    height: 600px;
    overflow-y: auto;
  }

  .titleBox {
    height: 22px;
    position: relative;
    display: flex;
    flex-direction: row;
    align-items: center;

    .title {
      font-size: 16px;
      color: black;
      margin: 0;
    }

    .subTitle {
      margin-left: 15px;
      font-size: 12px;
      color: #888888;
    }

    .riskLevel {
      user-select: none;
      position: absolute;
      // top: 0;
      right: 5%;
      display: flex;
      align-items: center;
      flex-direction: row;

      .title {
        font-size: 16px;
        font-weight: bold;
      }

      .main {
        font-size: 16px;
        font-weight: bold;
        padding: 5px 10px 5px 10px;
        color: white;
        box-sizing: border-box;
        border-radius: 5px;
      }

    }
  }
}

.infoBox {
  padding: 15px 15px 0 15px;
  box-sizing: border-box;
  border-radius: 6px;
  border: 1px solid #C4C4C4;
  position: relative;

  .infoTitle {
    user-select: none;
    position: absolute;
    top: 0;
    left: 0;
    padding: 0 10px;
    font-size: 14px;
    line-height: 14px;
    font-weight: bold;
    transform: translateX(15px) translateY(-50%);
    background-color: white;
  }
}

.inline-form-item {
  display: inline-block;
  box-sizing: border-box;
  width: 48%;
  margin-right: 2%;
  /* 设置项之间的间距 */
}
</style>
