<template>
  <div style="padding: 10px;">
    <el-button type="primary" @click="onClick">坐标系转换</el-button>
    <ImportData v-if="showImport" :is-update="isUpdate" :dialog-visible="showImport" :import-base-type="importBaseType"
      :import-type="importType" @close="closeImport" />
  </div>
</template>

<script>
import ImportData from "@/views/baseData/components/importData/index.vue";

export default {
  name: 'OtherTools',
  props: {},
  components: { ImportData },
  data() {
    return {
      showImport: false,
      isUpdate: false,
      importBaseType: "38",
    }
  },
  created() { },
  methods: {
    onClick() {
      this.isUpdate = true;
      this.showImport = true;
      this.importType = 1;
    },
    closeImport() {
      this.showImport = false;
    }
  },
  computed: {},
  watch: {},
}
</script>

<style scoped></style>