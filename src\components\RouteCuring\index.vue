<template>
  <div class="route-load" v-loading="loading">
    <el-select :multiple="multiple" :disabled="disabled" filterable style="width: 100%;" :size="size" clearable v-model="selectValue" :placeholder="placeholder">
      <el-option v-for="item in routeOptions" :key="item.maintenanceSectionId" :label="item.maintenanceSectionName"
                 :value="item.maintenanceSectionId"></el-option>
    </el-select>
  </div>
</template>

<script>
import { listMaintenanceSectionAll } from '@/api/system/maintenanceSection.js'
export default {
  name: 'RouteCuring',
  props: {
    value: {
      type: [String, Array],
      default: ''
    },
    placeholder: {
      type: String,
      default: '请选择路段'
    },
    size: {
      type: String,
      default: 'medium'
    },
    disabled: {
      type: Boolean,
      default: false
    },
    multiple: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      loading: false,
      routeOptions: []
    }
  },
  computed: {
    selectValue: {
      get() {
        return this.value
      },
      set(val) {
        this.$emit('input', val)
      }
    }
  },
  created() {
    this.getListAllRoute()
  },
  methods: {
    getListAllRoute() {
      this.loading = true
      listMaintenanceSectionAll().then(res => {
        if (res.code === 200) {
          this.routeOptions = res.data.filter(el => el.roadSectionId)
        }
      }).finally(() => {
        this.loading = false
      })
    },
  },
}
</script>

<style lang="scss" scoped>
.route-load {
}
</style>
