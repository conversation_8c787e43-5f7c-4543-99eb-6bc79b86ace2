<template>
    <div>
      <el-dialog
        title="隧道定期检查明细"
        :visible.sync="showDetail"
        width="55%"
        append-to-body
        :before-close="handleClose"
        :close-on-click-modal="true"
      >
      <!-- <el-button
        class="mb8"
          type="primary"
          @click="importUpdate"
        >导入更新</el-button>
        <el-button
        class="mb8"
          type="primary"
          @click="importAdd"
        >导入新增</el-button> -->
        <el-button
        
          type="primary"
          @click="handleRemove"
        >删除</el-button>
        
        <el-table
          ref="table"
          v-loading="loading"
          height="250px"
          border
          :data="tableData"
          :header-cell-style="{'background':'#F2F3F5','color': '#212529','height': '36px','font-weight': '700','font-size': '14px'}"
          :cell-style="{'height': '36px'}"
          @selection-change="handleSelectionChange"
          @row-click="handleRowClick"
        >
          <el-table-column
            fixed
            type="selection"
            width="50"
            align="center"
          />
          <el-table-column
            label="序号"
            type="index"
            width="50"
            align="center"
          />
          <!-- <el-table-column
            label="检查类型"
            align="center"
            prop="checkType"
            min-width="120"
            show-overflow-tooltip
          >
            <template slot-scope="scope">
              <span v-if="scope.row && scope.row.checkType">
                <DictTag
                  :value="scope.row.checkType"
                  :options="dict.type.tunnel_periodic_detection_type"
                />
              </span>
            </template>
          </el-table-column> -->
          <el-table-column
            label="检查日期"
            align="center"
            prop="checkDate"
            min-width="120"
            show-overflow-tooltip
          />
          <el-table-column
            label="检查项目名称"
            align="center"
            prop="itemName"
            min-width="120"
            show-overflow-tooltip
          />
          <el-table-column
            label="状况值"
            align="center"
            prop="state"
            min-width="120"
            show-overflow-tooltip
          />
          <el-table-column
            label="部件权重"
            align="center"
            prop="weight"
            min-width="120"
            show-overflow-tooltip
          />
          <el-table-column
            label="完好率"
            align="center"
            prop="intactRate"
            min-width="120"
            show-overflow-tooltip
          />
        </el-table>
        <pagination
          :total="total"
          :page.sync="pageNum"
          :limit.sync="pageSize"
          @pagination="getList"
        />
        <div slot="footer">
          <el-button @click="handleClose">取 消</el-button>
        </div>
      </el-dialog>
     
      <ImportData
        v-if="showImportAdd"
        :is-update="isUpdate"
        :dialog-visible="showImportAdd"
        :import-base-type="'29'"
        :import-type="importType"
        @close="closeImportAdd"
      />
      </div>
  </template>
  
  <script>
  import {
    getTunnelDetectionListPage,
    deleteTunnelDetectionByIds
  } from '@/api/baseData/tunnel/periodic/index'
  import DictTag from "@/components/DictTag";

  import ImportData from '@/views/baseData/components/importData/index.vue'
  
  export default {
    name: 'sideSlope-protections',
    components: {ImportData},
    props: {
      showDetail: { type: Boolean, default: false },
      choseId: { type: undefined, default: '' },
      formData: { type: Object, default: () => {} }
    },
    dicts: ['bridge_tec_condition_level'],
    data() {
      return {
        loading: false,
        pageNum: 1,
        pageSize: 20,
        total: 0,
        tableData: [],
        showComDetail:false,
        componId:null,
        showImportAdd: false,
        importType: 1,
        isUpdate: false,
        ids: []
        
      }
    },
    created() {
      this.getList()
    },
    methods: {
      getList() {
        if (!this.choseId) return
        this.loading = true
        let obj = {
          periodicDetectionId:this.choseId,
          pageNum: this.pageNum,
          pageSize: this.pageSize
        }
        getTunnelDetectionListPage(obj)
          .then(res => {
            if (res.code === 200) {
              this.tableData = res.rows
              this.total = res.total
            }
            this.loading = false
          })
          .catch(() => {
            this.loading = false
          })
      },
      handleClose() {
        this.$emit('close')
      },
      // 导入更新按钮
      importUpdate() {
        this.isUpdate = true
        this.showImportAdd = true
        this.importType = 1
      },
      // 导入新增按钮
      importAdd() {
        this.isUpdate = false
        this.showImportAdd = true
        this.importType = 2
      },
      handleView(row) {
        this.showComDetail = true
        this.componId = row.id
      },
      closeImportAdd(v) {
        this.showImportAdd = false
        if (v) this.getList()
      },
      handleRemove() {
        if (this.ids.length === 0) {
          this.$message.warning('请选择至少一条数据进行删除！')
          return
        }
        this.$modal
          .confirm('确认删除？')
          .then(() => {
            deleteTunnelDetectionByIds(this.ids).then(res => {
              if (res.code === 200) {
                this.getList()
                this.$modal.msgSuccess('删除成功')
              }
            })
          })
          .catch(() => {})
      },
      // 表格点击勾选
      handleRowClick(row) {
        row.isSelected = !row.isSelected
        this.$refs.table.toggleRowSelection(row)
      },
      // 多选框选中数据
      handleSelectionChange(selection) {
        this.ids = selection.map(item => item.id)
      },
    },
    watch: {
      // slopeId(newVal, oldVal) {
      //   this.tableData = []
      //   this.getList(newVal)
      // }
    }
  }
  </script>
  
  <style lang="scss" scoped>
  ::v-deep .el-form-item {
    margin-bottom: 10px;
    color: #1d2129;
  }
  </style>