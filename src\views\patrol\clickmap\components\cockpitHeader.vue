<template>
  <div
    class="header"
    ref="hRef"
    :style="{ position: !isInline ? fixed : 'relative' }"
  >
    <div class="header-main">
      <div
        class="current-time"
        :style="{ fontSize: bigBool ? '56px' : '14px' }"
      >
        {{ time }}
      </div>
      <div
        class="cockpit-title"
        :class="!isInline && bigBool ? 'b-font' : 'n-font'"
        :style="{ fontSize: fontSize }"
      >
        {{ title }}
        <slot></slot>
      </div>
      <div class="btn">
        <slot name="btn">
          <img
            :src="isFullscreen ? quitIcon : fullIcon"
            @click="fullClick"
            :style="{
              width: bigBool ? '80px' : '30px',
              height: bigBool ? '80px' : '30px',
            }"
          />

          <img
            src="@/assets/map/quite.png"
            @click="handleQuit"
            v-if="!isInline"
            :style="{
              width: bigBool ? '150px' : '60px',
              height: bigBool ? '150px' : '60px',
            }"
          />
        </slot>
      </div>
    </div>
  </div>
</template>

<script>
import screenfull from 'screenfull';
import FullImg from '@/assets/map/full.png';
import QuitFullImg from '@/assets/map/quit-full.png';
import { isBigScreen } from '../util/utils';

export default {
  name: 'Header',
  props: {
    title: {
      type: String,
      default: '物资材料管理系统',
    },
    fixed: {
      type: String,
      default: 'fixed',
    },
    fontSize: {
      type: String,
      default: '',
    },
  },
  data() {
    return {
      headerH: 70,
      time: '',
      bigBool: false,
      isInline: false,
      timer: null,
      fullIcon: FullImg,
      quitIcon: QuitFullImg,
      isFullscreen: false,
    };
  },

  created() {
    this.bigBool = isBigScreen();
    const query = this.$route.query;
    if (Object.keys(query).length !== 0) {
      this.isInline = true;
    }
    // 获取当前是星期几
    const date = new Date();
    const week = [
      '星期日',
      '星期一',
      '星期二',
      '星期三',
      '星期四',
      '星期五',
      '星期六',
    ];
    const dayOfWeek = week[date.getDay()];
    // 时时获取当前时间 年月日时分秒
    this.timer = setInterval(() => {
      let date = new Date();
      const year = date.getFullYear();
      const month = date.getMonth() + 1;
      const day = date.getDate();

      const hours = date.getHours();
      let minutes = date.getMinutes();
      let seconds = date.getSeconds();
      // 判断分 秒是否大于10
      if (minutes < 10) {
        minutes = '0' + minutes;
      }
      if (seconds < 10) {
        seconds = '0' + seconds;
      }
      this.time = `${year}年${month}月${day}日 ${dayOfWeek} ${hours}:${minutes}:${seconds}`;
    }, 1000);
  },
  mounted() {
    this.headerH = this.$refs.hRef.clientHeight || this.$refs.hRef.offsetHeight;
    screenfull.on('change', this.change);
  },
  methods: {
    getHeaderHeight() {
      return this.headerH;
    },
    handleQuit() {
      this.$router.push({ path: 'home' });
    },
    fullClick() {
      this.isFullscreen = !this.isFullscreen;
      this.$emit('fullClick', this.isFullscreen);
    },
    change() {
      this.isFullscreen = screenfull.isFullscreen;
    },
  },

  unmounted() {
    screenfull.off('change', this.change);
    if (this.timer) {
      clearInterval(this.timer);
      this.timer = null;
    }
  },
};
</script>

<style lang="scss" scoped>
@import '@/assets/styles/utils.scss';

@font-face {
  font-family: 'YouSheBiaoTiHei';
  /* 自定义的字体名称 */
  src: url('~@/assets/home/<USER>') format('truetype');
  /* 字体文件路径和格式 */
  /* 可选属性，根据需要设置 */
  font-weight: normal;
  font-style: normal;
}

.header {
  height: vwpx(160px);
  top: 0;
  left: 0;
  right: 0;
  background-image: url('~@/assets/cockpit/cockpit-bg.png');
  background-repeat: no-repeat;
  background-size: cover;
  z-index: 2;

  .header-main {
    height: 100%;
    width: 100%;
    background-image: url('~@/assets/cockpit/header-bg.png');
    background-repeat: no-repeat;
    background-size: 100% 100%;
    position: relative;

    .current-time {
      position: absolute;
      left: vwpx(40px);
      top: 40%;
      transform: translateY(-50%);
      color: #fff;
    }

    .cockpit-title {
      font-family: YouSheBiaoTiHei;
      font-weight: 400;

      letter-spacing: vwpx(12px);
      text-align: center;
      font-style: normal;
      text-transform: none;
      background: linear-gradient(
        180deg,
        #ffffff 0%,
        #ffffff 60%,
        #20a9ff 100%
      );
      background-clip: text;
      -webkit-background-clip: text;
      color: transparent;
      line-height: vwpx(160px);

      display: flex;
      align-items: center;
      justify-content: center;
    }

    .b-font {
      // font-size: vwpx(96px);
      font-size: 1.4vw;
    }

    .n-font {
      // font-size: vwpx(70px);
      // font-size: clamp(1rem, 0.682rem + 1.59vw, 1.575rem);
      font-size: 1.8vw;
    }

    .btn {
      position: absolute;
      right: vwpx(-5px);
      top: 50%;
      transform: translateY(-50%);
      cursor: pointer;

      display: flex;
      align-items: center;
    }
  }
}
</style>
