<template>
  <div class='app-container'>
    <el-row :gutter='20'>
      <!--部门数据-->
      <el-col :span='relaNav ? 4 : 0' :xs='24' class='leftDiv'>
        <!--折叠图标-->
        <div class='leftIcon' @click='relaNav = false'>
          <span class='el-icon-caret-left'></span>
        </div>
        <div class='head-container' style='width: 300px'>
          <el-tree
            ref='tree'
            :data='filteredTreeData'
            :default-expanded-keys='[1]'
            :expand-on-click-node='false'
            :props='defaultProps'
            highlight-current
            node-key='code'
            @node-click='handleNodeClick'
          >
          </el-tree>
        </div>
      </el-col>
      <!--角色数据-->
      <el-col :span='relaNav ? 20 : 24' :xs='24'>
        <!--展开图标-->
        <div v-show='!relaNav' class='rightIcon' @click='relaNav = true'>
          <span class='el-icon-caret-right'></span>
        </div>
        <el-row :gutter='10' class='mb8'>
          <el-form
            ref="queryForm"
            :inline="true"
            :model="queryParams"
            label-width="68px"
            size="mini"
          >
            <el-form-item
              label=""
              prop="reportName"
            >
              <el-input
                v-model='queryParams.reportName'
                placeholder='请输入报告名称'
                clearable
                size="mini"
              ></el-input>
            </el-form-item>
            <el-form-item
              label=""
              prop="reportType"
            >
              <dict-select
                v-model='queryParams.reportType'
                type='report_type'
                placeholder='请选择报告类型'
                clearable
                size='mini'
              ></dict-select>
            </el-form-item>
            <el-form-item
              label=""
              prop="reportYear"
            >
              <el-date-picker
                v-model='queryParams.reportYear'
                type='year'
                placeholder='请选择年份'
                clearable
                size='mini'
                format='yyyy'
                value-format='yyyy'
              >
              </el-date-picker>
            </el-form-item>
            <el-form-item>
              <el-button
                icon='el-icon-search'
                size='mini'
                type='primary'
                @click='handleQuery'
              >查询
              </el-button>
            </el-form-item>
            <el-button
              icon='el-icon-refresh'
              size='mini'
              type='success'
              @click='handleUpload'
            >生成报告
            </el-button>
            <el-button
              icon='el-icon-upload'
              size='mini'
              type='warning'
              @click='handleUpload'
            >模板选择
            </el-button>
          </el-form>
        </el-row>
        <!-- 筛选区结束 -->
        <!-- 数据表格开始 -->
        <div class='tableDiv'>
          <el-table v-adjust-table v-loading='loading' :data='dataList'
                    :height="'calc(100vh - 260px)'" border size='mini'
                    @selection-change='handleSelectionChange'>
            <el-table-column align='center' fixed label='序号' type='index' width='100'></el-table-column>
            <el-table-column align='left' label='报告名称' prop='reportName'>
              <template slot-scope='scope'>
                <el-link
                  type="primary"
                  class='link-type'
                  @click='handleRowClick(scope.row)'
                >{{ scope.row.reportName }}
                </el-link>
              </template>
            </el-table-column>
            <el-table-column align='center' label='报告类型' prop='reportType'>
              <template slot-scope='scope'>
                <dict-tag :options='dict.type.report_type' :value='scope.row.reportType'></dict-tag>
              </template>
            </el-table-column>
            <el-table-column align='center' label='年份' prop='reportYear' />
            <el-table-column align='center' label='具体时间' prop='specificTime' />
            <el-table-column align='center' label='头模板' prop='specificTime' />
            <el-table-column align='center' label='主体模板' prop='specificTime' />
            <el-table-column align='center' label='生成状态' prop='specificTime' />
            <el-table-column align='center' label='任务开始时间' prop='specificTime' />
            <el-table-column align='center' label='更新时间' prop='specificTime' />

            <el-table-column align='center' fixed='right' label='操作' width='150'>
              <template slot-scope='scope'>
                <el-button
                  icon='el-icon-edit'
                  size='mini'
                  type='text'
                  @click='handleEdit(scope.row)'
                >编辑
                </el-button>
                <el-button
                  icon='el-icon-delete'
                  size='mini'
                  type='text'
                  @click='handleDelete(scope.row)'
                >删除
                </el-button>
              </template>
            </el-table-column>
          </el-table>
          <pagination
            v-show='total > 0'
            :limit.sync='queryParams.pageSize'
            :page.sync='queryParams.pageNum'
            :total='total'
            @pagination='handleQuery'
          />
        </div>
        <!-- 数据表格结束 -->
      </el-col>
    </el-row>
    <el-dialog dialogTitle='上传文件' v-if='fileVisible' :visible.sync='fileVisible' width='500px' append-to-body>
      <el-form ref='elForm' :model='formData' :rules='rules' label-width='80px'>
        <el-form-item label='当前桥梁'>
          <el-input v-model='structureName' readonly />
        </el-form-item>
        <el-form-item label='报告类型' prop='reportType' v-if='!formData.id'>
          <dict-select
            v-model='formData.reportType'
            type='report_type'
            placeholder='请选择模板类型'
            @change='changeReportType'
          ></dict-select>
        </el-form-item>
        <el-form-item label='年份' prop='reportYear' v-if='!formData.id'>
          <el-date-picker
            style='width: 100%'
            :readonly='formData.reportType == 3'
            v-model='formData.reportYear'
            type='year'
            format='yyyy'
            value-format='yyyy'
            placeholder='请选择年份'
          >
          </el-date-picker>
        </el-form-item>
        <template v-if='!formData.id'>
          <el-form-item label='具体时间' prop='specificTime' v-if='formData.reportType == 1 || formData.reportType == 2'>
            <el-date-picker
              style='width: 100%'
              v-if='formData.reportType == 2'
              v-model='formData.specificTime'
              type="month"
              format='M'
              value-format='M'
              placeholder='请选择'
            >
            </el-date-picker>
            <el-select v-else v-model='formData.specificTime' style='width: 100%'>
              <el-option label='第一季度' value='1'></el-option>
              <el-option label='第二季度' value='2'></el-option>
              <el-option label='第三季度' value='3'></el-option>
              <el-option label='第四季度' value='4'></el-option>
            </el-select>
          </el-form-item>
        </template>
        <el-form-item label='模板名称' prop='reportName'>
          <el-input v-model='formData.reportName' placeholder='请输入名称'></el-input>
        </el-form-item>
        <el-form-item label='' prop='reportUrl' v-if='!formData.id'>
          <file-upload platform='jgjc' ref='file' v-model="formData.reportUrl" :owner-id='formData.reportUrl' :file-type='["doc", "docx", "pdf"]' :limit='1'></file-upload>
        </el-form-item>
      </el-form>
      <div style="text-align: right">
        <el-button type='primary' @click='handleSaveFile'>确 定</el-button>
        <el-button @click='fileVisible = false'>取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getJCDomainTree } from '@/api/jgjc/baseInfo/alarmLog'
import { getFile } from '@/api/file'
import axios from 'axios'
import {
  deleteReportManageForCustomer,
  getReportManageForCustomerPage,
  saveReportManageForCustomer, updateReportManageForCustomer,
} from '@/api/jgjc/assessmentReport/reportManage'

export default {
  name: 'ContentManagement',
  dicts: ['report_type'],
  data() {
    return {
      // 遮罩层
      loading: false,
      // 左侧组织树
      relaNav: true,
      relaOptions: [],
      filteredTreeData: [],
      defaultProps: {
        children: 'children',
        label: 'label',
      },
      // 总条数
      total: 0,
      dataList: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
      },
      structureId: '',
      structureName: '',
      formData: {},
      fileVisible: false,
      rules: {
        reportType: [
          { required: true, message: '请选择报告类型', trigger: 'change' }
        ],
        reportYear: [
          { required: true, message: '请选择年份', trigger: 'change' }
        ],
        specificTime: [
          {
            required: true,
            message: '请选择具体时间',
            trigger: 'change',
            validator: (rule, value, callback) => {
              // 只有当报告类型是1或2时才需要校验
              if ((this.formData.reportType == 1 || this.formData.reportType == 2) && !value) {
                callback(new Error('请选择具体时间'));
              } else {
                callback();
              }
            }
          }
        ],
        reportName: [
          { required: true, message: '请输入模板名称', trigger: 'blur' }
        ]
      }
    }
  },
  created() {
    this.getDeptTree()
  },
  methods: {
    // 查询部门下拉树结构
    getDeptTree() {
      getJCDomainTree({}).then(response => {
        this.relaOptions = response.data
        this.filteredTreeData = [...this.relaOptions]
      })
    },
    // 树节点点击事件
    handleNodeClick(nodeData, node) {
      if (node.level == 4) {
        console.log(node)
        this.queryParams = {
          pageNum: 1,
          pageSize: 10,
          structureId: nodeData.structureId,
          level: node.level,
        }
        this.structureId = nodeData.structureId
        this.structureName = nodeData.label
        this.handleQuery()
      }
    },
    handleQuery() {
      if (!this.structureId) {
        this.$message.warning('请选择结构物')
        return
      }
      this.queryParams.structureId = this.structureId
      this.loading = true
      getReportManageForCustomerPage(this.queryParams).then(res => {
        this.dataList = res.rows
        this.total = res.total
        this.loading = false
      }).catch(err => {
        this.loading = false
        console.error(err)
      })
    },
    handleRowClick(row) {
      this.loading = true
      getFile({ ownerId: row.reportUrl }).then(async res => {
        if (res.code == 200) {
          await this.handleDownload(res.data, row.reportName)
        }
      }).finally(() => {
        this.loading = false
      })
    },
    handleEdit(row) {
      this.formData = Object.assign({}, row)
      this.formData.reportYear = String(row.reportYear)
      this.fileVisible = true
    },
    handleDelete(row) {
      this.$confirm('是否确认删除？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }).then(() => {
        this.loading = true
        deleteReportManageForCustomer({ ids: [row.id] }).then(res => {
          if (res.code === 200) {
            this.$message.success('删除成功')
            this.handleQuery()
          } else {
            this.$message.error(res.msg)
          }
          this.loading = false
        }).catch(err => {
          this.loading = false
          console.error(err)
        })
      })
    },
    handleUpload() {
      if (!this.structureId) {
        this.$message.warning('请选择结构物')
        return
      }
      this.formData = {}
      this.formData.structureId = this.structureId
      this.fileVisible = true
    },
    handleSaveFile() {
      this.$refs.elForm.validate(valid => {
        if (!valid) return
        if (this.formData.id) {
          updateReportManageForCustomer(this.formData).then(res => {
            this.$modal.msgSuccess('保存成功')
            this.fileVisible = false
            this.handleQuery()
          })
        } else {
          const fileList = this.$refs.file.fileList
          if (fileList.length != 1) {
            this.$message.warning('请上传文件')
            return
          }
          this.formData.reportUrl = fileList[0].ownerId
          saveReportManageForCustomer(this.formData).then(res => {
            this.$modal.msgSuccess('新增成功')
            this.fileVisible = false
            this.handleQuery()
          })
        }
      })
    },
    async handleDownload(file, name) {
      const url = file.url || (file.response && file.response.url)
      if (!url) {
        this.$message.error('文件无法下载，未找到文件的URL')
        return
      }

      try {
        // 创建一个不包含 Authorization 的请求头
        const downloadHeaders = {
          ...this.headers,
          'Content-Disposition': 'attachment', // 添加强制下载头
          'Content-Type': 'application/octet-stream', // 使用通用的二进制流类型
        }
        delete downloadHeaders.Authorization

        // 使用 axios 进行下载，因为它可以自动处理跨域认证
        const response = await axios({
          url: url,
          method: 'GET',
          responseType: 'blob',
          headers: downloadHeaders,
        })

        // 从响应头中获取文件类型
        const contentType = response.headers['content-type']
        const blob = new Blob([response.data], { type: contentType || 'application/octet-stream' })

        // 创建一个隐藏的 iframe 来处理下载
        const iframe = document.createElement('iframe')
        iframe.style.display = 'none'
        document.body.appendChild(iframe)
        const finalFileName = name

        // 在 iframe 中创建 blob URL 并触发下载
        const iframeWindow = iframe.contentWindow
        const blobUrl = iframeWindow.URL.createObjectURL(blob)
        const link = iframeWindow.document.createElement('a')
        link.href = blobUrl
        link.download = finalFileName
        link.click()

        // 清理资源
        setTimeout(() => {
          iframeWindow.URL.revokeObjectURL(blobUrl)
          document.body.removeChild(iframe)
        }, 1000)
      } catch (error) {
        console.error('下载文件时发生错误:', error)
        this.$message.error('下载文件失败，请重试')
      }
    },
    changeReportType() {
      this.$set(this.formData, 'specificTime', null)
    }
  },
}
</script>

<style scoped>
.leftDiv {
  border-right: 1px solid #d8dce5;
  min-height: calc(100vh - 100px);
  overflow-y: auto;
  height: calc(80vh - 150px);
  position: relative;
  top: -20px;
  padding-top: 10px;
  background-color: white;
}

.leftIcon {
  border: 1px solid #dcdfe6;
  border-radius: 8px;
  width: 16px;
  height: 50px;
  line-height: 50px;
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
  position: absolute;
  right: 0;
  top: 300px;
  z-index: 2;
}

.leftIcon:hover {
  background-color: #dcdfe6;
}

.rightIcon {
  border: 1px solid #dcdfe6;
  border-radius: 8px;
  width: 16px;
  height: 50px;
  line-height: 50px;
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
  position: absolute;
  left: -10px;
  top: 280px;
  z-index: 10;
  background: white;
}

.rightIcon:hover {
  background-color: #dcdfe6;
}

.tableDiv {
  margin-top: 20px;
}
</style>
<style lang='scss' scoped>
@import "@/assets/styles/business.scss";
</style>
