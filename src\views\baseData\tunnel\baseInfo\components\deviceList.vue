<template>
  <div class="route-road">
    <el-dialog
      :title="'机电设备'"
      :visible="deviceForView"
      width="80%"
      style="height: 70vh;"
      append-to-body
      :before-close="handleClose"
      :close-on-click-modal="false"
    >
    <el-table
        ref="table"
        v-loading="loading"
        height="350px"
        border
        :data="tableData"
        :header-cell-style="{'height': '36px'}"
        :row-style="rowStyle"
      >
        <el-table-column
          fixed
          type="selection"
          width="50"
          align="center"
        />
        <el-table-column
          label="序号"
          type="index"
          width="50"
          align="center"
        >
          <template v-slot="scope">
            {{ (scope.$index + (queryParams.pageNum - 1) * queryParams.pageSize)+1 }}
          </template>
        </el-table-column>
        <el-table-column
          label="设施名称"
          align="center"
          prop="deviceName"
          min-width="120"
          show-overflow-tooltip
        />
        
        <el-table-column
          label="设施类型"
          align="center"
          prop="deviceType"
          min-width="120"
          show-overflow-tooltip
        >
          <template slot-scope="scope">
            <dict-tag
              :options="dict.type.tunnel_elect_device_type"
              :value="scope.row.deviceType"
            />
          </template>
        </el-table-column>
        <el-table-column
          label="设施型号"
          align="center"
          prop="deviceModel"
          min-width="120"
          show-overflow-tooltip
        />
       
        <el-table-column
          label="设施编码"
          align="center"
          prop="deviceCode"
          min-width="120"
          show-overflow-tooltip
        />
        <el-table-column
          label="隧道名称"
          align="center"
          prop="tunnelName"
          min-width="120"
          show-overflow-tooltip
        />
        <el-table-column
          label="隧道编码"
          align="center"
          prop="tunnelCode"
          min-width="120"
          show-overflow-tooltip
        />
        <el-table-column
          label="起点桩号"
          align="center"
          prop="startStake"
          min-width="120"
          show-overflow-tooltip
        />
        <el-table-column
          label="终点桩号"
          align="center"
          prop="endStake"
          min-width="120"
          show-overflow-tooltip
        />
        <el-table-column
          label="数量"
          align="center"
          prop="number"
          min-width="120"
          show-overflow-tooltip
        />
        <el-table-column
          label="单位"
          align="center"
          prop="unit"
          min-width="120"
          show-overflow-tooltip
        />
        <el-table-column
          label="光源类别"
          align="center"
          prop="lightType"
          min-width="120"
          show-overflow-tooltip
        />
       
        <el-table-column
          label="功率"
          align="center"
          prop="power"
          min-width="120"
          show-overflow-tooltip
        />
        <el-table-column
          label="是否关键设备"
          align="center"
          prop="isKeyDevice"
          min-width="120"
          show-overflow-tooltip
        >
          <template slot-scope="scope">
            <dict-tag
              :options="dict.type.base_data_yes_no"
              :value="scope.row.isKeyDevice"
            />
          </template>
        </el-table-column>
       
        <el-table-column
          label="记录者"
          align="center"
          prop="recorder"
          min-width="120"
          show-overflow-tooltip
        />
        <el-table-column
          label="记录时间"
          align="center"
          prop="recorderTime"
          min-width="120"
          show-overflow-tooltip
        />
      </el-table>
      <pagination
        :total="total"
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
        :pageSizes="[10, 20, 30, 50, 100, 1000]"
        @pagination="getList"
      />
     
    </el-dialog>
  </div>
</template>

<script>
import { getDicts } from '@/api/system/dict/data'
import { segmentsList } from '@/api/baseData/tunnel/baseInfo/getSections'

import { getListPage } from '@/api/baseData/tunnel/device/index'

export default {
  // 路段选择器
  name: 'RouteRoad',
  components: {},
  props: {
    tunnelId: {
      type: String,
      default: 0
    },
    deviceForView: {
      type: Boolean,
      default: true
    }
  },
  dicts: [
    'base_data_yes_no',
    'tunnel_length_classification',
    'tunnel_assess_grade',
    'tunnel_maintenance_grade',
    'sys_route_grade',
    'tunnel_elect_device_type'
  ],
  data() {
    return {
      loading: false,
      dialogTitle: '新增',
      showAddEdit: false,
      staticList: [],
      tableData: [],
      manageForm: {
        engineeringProperties: '',
        lastReformTime: '',
        majorDisease: '',
        reformSite: ''
      },
      queryParams: {
        pageNum: 1,
        pageSize: 10
      },
      total: 0,
      id: 0
    }
  },
  watch: {},
  computed: {},
  created() {
    this.getList()
  },
  mounted() {},
  methods: {
    getList() {
      if (!this.tunnelId) return
      this.loading = true
      getListPage({
        tunnelIds: [this.tunnelId],
        ...this.queryParams
      })
        .then(res => {
          if (res.code === 200) {
            this.tableData = res.rows
            this.total = res.total
          }
        })
        .finally(() => {
          this.loading = false
        })
    },
    handleClose(done) {
      this.$emit('close', false)
    }
  },
  watch: {}
}
</script>



<style lang="scss" scoped>
.route-road {
  padding: 0 10px;
  height: 100%;
  .el-button {
    // margin-left: 10px;
    margin-bottom: 10px;
  }
}
</style>