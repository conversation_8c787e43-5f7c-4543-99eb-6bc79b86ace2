<template>
  <div class="app-container">
    <el-row :gutter="20">

      <!--部门数据-->
      <el-col :span="deptNav ? 5:0 " :xs="24" class="leftDiv">
        <!--折叠图标-->
        <div class="leftIcon"  @click="deptNav=false">
          <span class="el-icon-caret-left"></span>
        </div>
        <div class="head-container">
          <el-input
            v-model="deptName"
            placeholder="部门名称"
            clearable
            size="small"
            prefix-icon="el-icon-search"
            style="margin-bottom: 20px"
          />
        </div>
        <div class="head-container" style="width: 300px">
          <span style="margin-left:0px;">单位：{{curSelectDept.label}}</span>
          <el-tree
            :data="deptOptions"
            :props="defaultProps"
            :expand-on-click-node="false"
            :filter-node-method="filterNode"
            ref="tree"
            :default-expanded-keys="[1]"
            node-key="id"
            highlight-current
            @node-click="handleNodeClick"
          />
        </div>
      </el-col>

      <!--筛选区开始-->
      <el-col :span="deptNav ? 19:24" :xs="24">
        <!--展开图标-->
        <div class="rightIcon" @click="deptNav=true" v-show="!deptNav">
          <span class="el-icon-caret-right"></span>
        </div>

        <el-row>
          <el-col :span="24" >
            <el-form :model="queryParams" ref="queryForm" size="small" :inline="true"  label-width="68px">
                <el-form-item >
                  <el-button type="primary"  @click="deptNav=!deptNav"   :icon="deptNavIcon" size="mini">
                    <span v-show="deptNav">折叠</span>
                    <span v-show="!deptNav">展开</span>
                  </el-button>
                </el-form-item>
                          <el-form-item label="" prop="cerName">
                            <el-input
                                v-model="queryParams.cerName"
                                placeholder="请输入证书名称"
                                clearable
                                prefix-icon="el-icon-user"
                                style="width: 240px"
                                @keyup.enter.native="handleQuery"
                            />
                          </el-form-item>
                          <el-form-item label="" prop="cerCode">
                            <el-input
                                v-model="queryParams.cerCode"
                                placeholder="请输入证书编号"
                                clearable
                                prefix-icon="el-icon-user"
                                style="width: 240px"
                                @keyup.enter.native="handleQuery"
                            />
                          </el-form-item>
                          <el-form-item label="" prop="cerAuthority">
                            <el-input
                                v-model="queryParams.cerAuthority"
                                placeholder="请输入颁发机构"
                                clearable
                                prefix-icon="el-icon-user"
                                style="width: 240px"
                                @keyup.enter.native="handleQuery"
                            />
                          </el-form-item>
                          <el-form-item label="" prop="issueDate">
                            <el-date-picker clearable
                                            v-model="queryParams.issueDate"
                                            type="date"
                                            value-format="yyyy-MM-dd"
                                            placeholder="请选择颁发日期">
                            </el-date-picker>
                          </el-form-item>
                          <el-form-item label="" prop="validDate">
                            <el-date-picker clearable
                                            v-model="queryParams.validDate"
                                            type="date"
                                            value-format="yyyy-MM-dd"
                                            placeholder="请选择有效期至">
                            </el-date-picker>
                          </el-form-item>
                          <el-form-item label="" prop="userId">
                            <el-input
                                v-model="queryParams.userId"
                                placeholder="请输入用户id"
                                clearable
                                prefix-icon="el-icon-user"
                                style="width: 240px"
                                @keyup.enter.native="handleQuery"
                            />
                          </el-form-item>
              <el-form-item>
                <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
                <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
                <el-button v-show="!showSearch" @click="showSearch=true" icon="el-icon-arrow-down" circle></el-button>
                <el-button  v-show="showSearch" @click="showSearch=false"  icon="el-icon-arrow-up" circle></el-button>
              </el-form-item>
            </el-form>
            <!--默认折叠-->
          </el-col>

          <!--默认折叠 ,此处仅作为示例-->
          <el-col :span="24" >
            <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">

              <el-form-item label="" prop="status">
                <el-select
                    v-model="queryParams.status"
                    placeholder="示例状态"
                    clearable
                    style="width: 240px"
                >
                  <el-option
                      v-for="dict in dictType"
                      :key="dict.value"
                      :label="dict.label"
                      :value="dict.value"
                  />
                </el-select>
              </el-form-item>
              <el-form-item label="" prop="status">
                <el-select
                    v-model="queryParams.status"
                    placeholder="示例状态"
                    clearable
                    style="width: 240px"
                >
                  <el-option
                      v-for="dict in dictType"
                      :key="dict.value"
                      :label="dict.label"
                      :value="dict.value"
                  />
                </el-select>
              </el-form-item>

            </el-form>
          </el-col>
        </el-row>
        <!--筛选区结束-->


        <!--操作按钮区开始-->
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button
                type="primary"
                icon="el-icon-plus"
                size="mini"
                @click="handleAdd"
                v-hasPermi="['patrol:laborcertificate:add']"
            >新增</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button
                type="success"
                icon="el-icon-edit"
                size="mini"
                :disabled="single"
                @click="handleUpdate"
                v-hasPermi="['patrol:laborcertificate:edit']"
            >修改</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button
                type="danger"
                icon="el-icon-delete"
                size="mini"
                :disabled="multiple"
                @click="handleDelete"
                v-hasPermi="['patrol:laborcertificate:remove']"
            >删除</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button
                type="info"
                plain
                icon="el-icon-upload2"
                size="mini"
                @click="handleImport"
                v-hasPermi="['patrol:laborcertificate:export']"
            >导入</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button
                type="warning"
                icon="el-icon-download"
                size="mini"
                @click="handleExport"
                v-hasPermi="['system:user:export']"
            >导出</el-button>
          </el-col>
          <right-toolbar :showSearch.sync="showSearch" @queryTable="getList" :columns="columns"></right-toolbar>
        </el-row>
        <!--操作按钮区结束-->

        <!--数据表格开始-->
        <div class="tableDiv">
          <el-table  size="mini" :height="showSearch ? 'calc(100vh - 320px)' : 'calc(100vh - 260px)'"   style="width: 100%" v-loading="loading" border :data="laborcertificateList" @selection-change="handleSelectionChange">
            <el-table-column type="selection" width="50" align="center" />
            <el-table-column fixed label="序号" type="index" width="50">
              <template v-slot="scope">
                {{ (scope.$index + (queryParams.pageNum - 1) * queryParams.pageSize)+1 }}
              </template>
            </el-table-column>
                    <el-table-column label="所属单位" align="center" prop="domainId" />
                    <el-table-column label="证书类型" align="center" prop="cerType" />
                    <el-table-column label="证书名称" align="center" prop="cerName" />
                    <el-table-column label="证书编号" align="center" prop="cerCode" />
                    <el-table-column label="颁发机构" align="center" prop="cerAuthority" />
                    <el-table-column label="颁发日期" align="center" prop="issueDate" width="180">
                      <template slot-scope="scope">
                        <span>{{ parseTime(scope.row.issueDate, '{y}-{m}-{d}') }}</span>
                      </template>
                    </el-table-column>
                    <el-table-column label="有效期至" align="center" prop="validDate" width="180">
                      <template slot-scope="scope">
                        <span>{{ parseTime(scope.row.validDate, '{y}-{m}-{d}') }}</span>
                      </template>
                    </el-table-column>
                    <el-table-column label="附件" align="center" prop="cerFile" />
                    <el-table-column label="备注" align="center" prop="remark" />
                    <el-table-column label="用户" align="center" prop="userId" />
                    <el-table-column label="状态" align="center" prop="status" />
            <el-table-column
                label="操作"
                fixed="right"
                align="center"
                width="160"
                class-name="small-padding fixed-width"
            >
              <template slot-scope="scope" v-if="scope.row.userId !== 1">
                <el-button
                    size="mini"
                    type="text"
                    icon="el-icon-edit"
                    @click="handleUpdate(scope.row)"
                    v-hasPermi="['patrol:laborcertificate:edit']"
                >修改</el-button>
                <el-button
                    size="mini"
                    type="text"
                    icon="el-icon-delete"
                    @click="handleDelete(scope.row)"
                    v-hasPermi="['patrol:laborcertificate:remove']"
                >删除</el-button>
              </template>
            </el-table-column>
          </el-table>

          <pagination
              v-show="total>0"
              :total="total"
              :page.sync="queryParams.pageNum"
              :limit.sync="queryParams.pageSize"
              @pagination="getList"
          />
        </div>
        <!--数据表格结束-->
      </el-col>
    </el-row>

    <!-- 添加或修改单位人员持证情况对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="600px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
          <el-row>
            <el-col :span="12">
              <el-form-item label="所属单位" prop="domainId">
                <treeselect v-model="form.domainId" :options="deptOptions" :show-count="false"
                          placeholder="请选择所属单位"/>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="证书名称" prop="cerName">
                <el-input v-model="form.cerName" placeholder="请输入证书名称" />
              </el-form-item>
            </el-col>
          </el-row>

                      <el-col :span="12">
                        <el-form-item label="证书编号" prop="cerCode">
                          <el-input v-model="form.cerCode" placeholder="请输入证书编号" />
                        </el-form-item>
                      </el-col>
                      <el-col :span="12">
                        <el-form-item label="颁发机构" prop="cerAuthority">
                          <el-input v-model="form.cerAuthority" placeholder="请输入颁发机构" />
                        </el-form-item>
                      </el-col>
                      <el-col :span="12">
                        <el-form-item label="颁发日期" prop="issueDate">
                          <el-date-picker clearable
                                          v-model="form.issueDate"
                                          type="date"
                                          value-format="yyyy-MM-dd"
                                          placeholder="请选择颁发日期">
                          </el-date-picker>
                        </el-form-item>
                      </el-col>
                      <el-col :span="12">
                        <el-form-item label="有效期至" prop="validDate">
                          <el-date-picker clearable
                                          v-model="form.validDate"
                                          type="date"
                                          value-format="yyyy-MM-dd"
                                          placeholder="请选择有效期至">
                          </el-date-picker>
                        </el-form-item>
                      </el-col>
                      <el-col :span="12">
                        <el-form-item label="附件" prop="cerFile">
                          <file-upload v-model="form.cerFile"/>
                        </el-form-item>
                      </el-col>
                      <el-col :span="12">
                        <el-form-item label="备注" prop="remark">
                          <el-input v-model="form.remark" placeholder="请输入备注" />
                        </el-form-item>
                      </el-col>
                      <el-col :span="12">
                        <el-form-item label="用户" prop="userId">
                          <el-input v-model="form.userId" placeholder="请输入用户" />
                        </el-form-item>
                      </el-col>
                      <!-- <el-col :span="12">
                        <el-form-item label="删除标志" prop="delFlag">
                          <el-input v-model="form.delFlag" placeholder="请输入删除标志" />
                        </el-form-item>
                      </el-col> -->
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 用户导入对话框 -->
    <el-dialog :title="upload.title" :visible.sync="upload.open" width="400px" append-to-body>
      <el-upload
          ref="upload"
          :limit="1"
          accept=".xlsx, .xls"
          :headers="upload.headers"
          :action="upload.url + '?updateSupport=' + upload.updateSupport"
          :disabled="upload.isUploading"
          :on-progress="handleFileUploadProgress"
          :on-success="handleFileSuccess"
          :auto-upload="false"
          drag
      >
        <i class="el-icon-upload"></i>
        <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
        <div class="el-upload__tip text-center" slot="tip">
          <div class="el-upload__tip" slot="tip">
            <el-checkbox v-model="upload.updateSupport" /> 是否更新已经存在的用户数据
          </div>
          <span>仅允许导入xls、xlsx格式文件。</span>
          <el-link type="primary" :underline="false" style="font-size:12px;vertical-align: baseline;" @click="importTemplate">下载模板</el-link>
        </div>
      </el-upload>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitFileForm">确 定</el-button>
        <el-button @click="upload.open = false">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
  import { listLaborcertificate, getLaborcertificate, delLaborcertificate, addLaborcertificate, updateLaborcertificate } from "@/api/patrol/laborcertificate";
  import { deptTreeSelect } from "@/api/system/user";
  import { getToken } from "@/utils/auth";
  import Treeselect from "@riophae/vue-treeselect";
  import "@riophae/vue-treeselect/dist/vue-treeselect.css";

  export default {
    name: "Laborcertificate",
    components: { Treeselect },
    data() {
      return {
        // 遮罩层
        loading: true,
        // 选中数组
        ids: [],
        // 非单个禁用
        single: true,
        // 非多个禁用
        multiple: true,
        // 显示搜索条件
        showSearch: false,
        dictType:[],
        // 总条数
        total: 0,
        // 单位人员持证情况表格数据
        laborcertificateList: null,
        // 弹出层标题
        title: "",
        // 左侧组织树
        deptNav: true,
        // 部门树选项
        deptOptions: undefined,
        // 部门名称
        deptName: undefined,
        //当前选中的部门
        curSelectDept: {
          label:"无"
        },
        // 是否显示弹出层
        open: false,

        // 表单参数
        form: {},
        defaultProps: {
          children: "children",
          label: "label"
        },
        // 用户导入参数
        upload: {
          // 是否显示弹出层（用户导入）
          open: false,
          // 弹出层标题（用户导入）
          title: "",
          // 是否禁用上传
          isUploading: false,
          // 是否更新已经存在的用户数据
          updateSupport: 0,
          // 设置上传的请求头部
          headers: { Authorization: "Bearer " + getToken() },
          // 上传的地址
          url: process.env.VUE_APP_BASE_API + "/system/user/importData"
        },
        // 查询参数
        queryParams: {
          pageNum: 1,
          pageSize: 50,
            domainId: null,
            cerType: null,
            cerName: null,
            cerCode: null,
            cerAuthority: null,
            issueDate: null,
            validDate: null,
            cerFile: null,
            userId: null,
            status: null,
        },
        // 列信息
        columns: [
        { key: 0, label: `所属单位id`, visible: true },
        { key: 1, label: `证书类型`, visible: true },
        { key: 2, label: `证书名称`, visible: true },
        { key: 3, label: `证书编号`, visible: true },
        { key: 4, label: `颁发机构`, visible: true },
        { key: 5, label: `颁发日期`, visible: true },
        { key: 6, label: `有效期至`, visible: true },
        { key: 7, label: `附件`, visible: true },
        { key: 8, label: `备注`, visible: true },
        { key: 9, label: `用户id`, visible: true },
        { key: 10, label: `状态（0-正常，1-停用）`, visible: true },
        { key: 11, label: `删除标志（0代表存在，1代表删除）`, visible: true }
        ],
        // 表单校验
        rules: {


        }
      };
    },
    watch: {
      // 根据名称筛选部门树
                      },
    created() {
      this.getList();
      this.getDeptTree();
      // this.getConfigKey("sys.user.initPassword").then(response => {
      //   this.initPassword = response.msg;
      // });
    },
    computed: {
      deptNavIcon() {
          return this.deptNav ? 'el-icon-arrow-left' : 'el-icon-arrow-right';
      },
    },
    methods: {
      /** 查询用户列表 */
      getList() {
        this.loading = true;
        listLaborcertificate(this.queryParams).then(response => {
          this.laborcertificateList = response.rows;
          this.total = response.total;
          this.loading = false;
        });
      },
       /** 查询部门下拉树结构 */
      getDeptTree() {
        deptTreeSelect().then(response => {
          this.deptOptions = response.data;
        });
      },
      // 筛选节点
      filterNode(value, data) {
        if (!value) return true;
        return data.label.indexOf(value) !== -1;
      },
      // 节点单击事件
      handleNodeClick(data) {
        this.curSelectDept = data;
        this.queryParams.domainId = data.id;
        this.handleQuery();
      },
      // 取消按钮
      cancel() {
        this.open = false;
        this.reset();
      },
      // 表单重置
      reset() {
        this.form = {
            domainId: null,
            cerType: null,
            cerName: null,
            cerCode: null,
            cerAuthority: null,
            issueDate: null,
            validDate: null,
            cerFile: null,
            remark: null,
            userId: null,
            status: null,
            delFlag: null
        };
        this.resetForm("form");
      },
      /** 搜索按钮操作 */
      handleQuery() {
        this.queryParams.pageNum = 1;
        this.getList();
      },
      /** 重置按钮操作 */
      resetQuery() {
        this.dateRange = [];
        this.resetForm("queryForm");
        this.handleQuery();
      },
      // 多选框选中数据
      handleSelectionChange(selection) {
        this.ids = selection.map(item => item.id);
        this.single = selection.length != 1;
        this.multiple = !selection.length;
      },
      /** 新增按钮操作 */
      handleAdd() {
        this.reset();
        this.open = true;
        this.title = "添加单位人员持证情况";
      },
      /** 修改按钮操作 */
      handleUpdate(row) {
        this.reset();
        const id = row.id || this.ids;
        getLaborcertificate(id).then(response => {
          this.form = response.data;
          this.open = true;
          this.title = "修改单位人员持证情况";
        });

      },
      /** 提交按钮 */
      submitForm: function() {
        this.$refs["form"].validate(valid => {
          if (valid) {
            if (this.form.id != null) {
              updateLaborcertificate(this.form).then(response => {
                this.$modal.msgSuccess("修改成功");
                this.open = false;
                this.getList();
              });
            } else {
              addLaborcertificate(this.form).then(response => {
                this.$modal.msgSuccess("新增成功");
                this.open = false;
                this.getList();
              });
            }
          }
        });
      },
      /** 删除按钮操作 */
      handleDelete(row) {
        const id = row.id || this.ids;
        this.$modal.confirm('是否确认删除单位人员持证情况编号为"' + id + '"的数据项？').then(function() {
          return delLaborcertificate(id);
        }).then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        }).catch(() => {});
      },

      /** 导出按钮操作 */
      handleExport() {

    this.download('patrol/laborcertificate/export', {
      ...this.queryParams
    }, `laborcertificate_${new Date().getTime()}.xlsx`)

      },
      /** 导入按钮操作 */
      handleImport() {
        this.upload.title = "用户导入";
        this.upload.open = true;
      },
      /** 下载模板操作 */
      importTemplate() {
        this.download('system/user/importTemplate', {
        }, `user_template.xlsx`)
      },
      // 文件上传中处理
      handleFileUploadProgress(event, file, fileList) {
        this.upload.isUploading = true;
      },
      // 文件上传成功处理
      handleFileSuccess(response, file, fileList) {
        this.upload.open = false;
        this.upload.isUploading = false;
        this.$refs.upload.clearFiles();
        this.$alert("<div style='overflow: auto;overflow-x: hidden;max-height: 70vh;padding: 10px 20px 0;'>" + response.msg + "</div>", "导入结果", { dangerouslyUseHTMLString: true });
        this.getList();
      },
      // 提交上传文件
      submitFileForm() {
        this.$refs.upload.submit();
      }
  }
  };
</script>
<style>
  .hasTagsView .app-main[data-v-078753dd]{
    background: #f5f7fa;
  }

  .leftDiv{
    border-right: 1px solid #d8dce5;
    min-height: calc(100vh - 110px);
    overflow-y: auto;
    height: calc(80vh - 110px);
    position: relative;
    top: -20px;
    padding-top: 10px;
    background-color: white;
  }

  .leftIcon{
    border: 1px solid #DCDFE6;
    border-radius: 8px;
    width: 16px;
    height: 50px;
    line-height: 50px;
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
    position: absolute;
    right: 0;
    top: 300px;
    z-index: 2;
  }
  .leftIcon:hover{
    background-color: #DCDFE6;
  }

  .rightIcon{
    border: 1px solid #DCDFE6;
    border-radius: 8px;
    width: 16px;
    height: 50px;
    line-height: 50px;
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
    position: absolute;
    left: -10px;
    top: 280px;
    z-index: 10;
    background: white;
  }
  .rightIcon:hover{
    background-color: #DCDFE6;
  }


  .tableDiv{
    background-color: white;
    padding-bottom: 10px;
  }
</style>
