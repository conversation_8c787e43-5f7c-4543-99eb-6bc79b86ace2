<template>
  <div class="app-container">
    <el-row :gutter="20">
      <!-- 筛选区开始 -->
      <el-col :span="24" :xs="24">
        <el-row>
          <el-col :span="24">
            <el-form ref="queryForm" :inline="true" :model="queryParams" label-width="68px" size="small">
              <el-form-item label="" prop="maintenanceRoad">
                <RoadSection ref="roadSection" v-model="queryParams.maiSecId" placeholder="路段" :readonly="maiSecId"
                             style="width: 100%"/>
              </el-form-item>
              <el-form-item label="" prop="houseName">
                <el-input v-model="queryParams.houseName" placeholder="请输入户名"/>
              </el-form-item>
              <el-form-item label="" prop="houseNum">
                <el-input v-model="queryParams.houseNum" placeholder="请输入户号"/>
              </el-form-item>
              <el-form-item>
                <el-button icon="el-icon-search" size="mini" type="primary" @click="handleQuery">搜索</el-button>
                <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
              </el-form-item>
            </el-form>
          </el-col>
        </el-row>
        <el-row :gutter="10" class="mb8" v-if="pageType == 'edit'">
          <el-col :span="1.5">
            <el-button
              v-has-menu-permi="['operate:systemElectricity:add']"
              icon="el-icon-plus"
              size="mini"
              type="primary"
              @click="handleAdd"
            >新增
            </el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button
              v-has-menu-permi="['operate:systemElectricity:export']"
              icon="el-icon-download"
              size="mini"
              type="success"
              @click="exportList"
            >导出
            </el-button>
          </el-col>
        </el-row>
        <!-- 筛选区结束 -->
        <!-- 数据表格开始 -->
        <div class="tableDiv">
          <el-table v-adjust-table v-loading="loading" :data="dataList"
                    :height="pageType == 'edit' ? 'calc(100vh - 260px)' : 'calc(70vh - 260px)'" border size="mini"
                    style="width: 100%" @selection-change="handleSelectionChange">>
            <el-table-column
              type="selection"
              width="50">
            </el-table-column>
            <el-table-column align="center" fixed label="序号" type="index" width="100"></el-table-column>
            <template v-for="(column, index) in columns">
              <el-table-column
                v-if="column.visible"
                :label="column.label"
                :prop="column.field"
                width="150"
                align="center" show-overflow-tooltip
              >
                <template slot-scope="scope">
                  <dict-tag
                    v-if="column.dict"
                    :options="dict.type[column.dict]"
                    :value="scope.row[column.field]"
                  />
                  <template v-else-if="column.slots">
                    <RenderDom
                      :index="index"
                      :render="column.render"
                      :row="scope.row"
                    />
                  </template>
                  <span v-else-if="column.isTime">{{
                      parseTime(scope.row[column.field], "{y}-{m}-{d}")
                    }}</span>
                  <span v-else>{{ scope.row[column.field] }}</span>
                </template>
              </el-table-column>
            </template>
            <el-table-column align="center" class-name="small-padding fixed-width" fixed="right" label="操作" v-if="pageType == 'edit'"
                             width="300">
              <template slot-scope="scope">
                <el-button icon="el-icon-edit" v-has-menu-permi="['operate:systemElectricity:edit']" size="mini" type="text" @click="handleEdit(scope.row)">编辑</el-button>
                <el-button icon="el-icon-delete" v-has-menu-permi="['operate:systemElectricity:remove']" size="mini" type="text" @click="handleDelete(scope.row)">删除
                </el-button>
              </template>
            </el-table-column>
          </el-table>
          <pagination
            v-show="total > 0"
            :limit.sync="queryParams.pageSize"
            :page.sync="queryParams.pageNum"
            :total="total"
            @pagination="handleQuery"
          />
        </div>
        <!-- 数据表格结束 -->
      </el-col>
    </el-row>
    <!-- 新增/编辑对话框 -->
    <el-dialog v-if="dialogVisible" :title="dialogTitle" :visible.sync="dialogVisible" append-to-body destroy-on-close
               width="90%">
      <el-form ref="elForm" :model="formData" :rules="rules" label-width="150px" size="medium">
        <el-row :gutter="15">
          <el-col :span="8">
            <el-form-item label="费用类型" prop="feeType">
              <el-input readonly value="三大系统电费"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="管养单位" prop="domainId">
              <select-tree
                v-model="formData.domainId"
                :deptType="100"
                :deptTypeList="[1, 3, 4]"
                clearable
                onlySelectChild
                placeholder="请选择管养单位"
              ></select-tree>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="养护路段" prop="maiSecId">
              <RoadSection ref="roadSection" v-model="formData.maiSecId" :deptId="formData.domainId" placeholder="路段"
                           style="width: 100%"/>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="路线编码" prop="routeCode">
              <route-code-section ref="routeRef" v-model="formData.routeCode" :maintenanceSectionId="formData.maiSecId"
                                  placeholder="路线编码" style="width: 100%"></route-code-section>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="通车时间" prop="openTime">
              <el-date-picker
                v-model="formData.openTime"
                placeholder="通车时间"
                style="width: 100%"
                type="date"
                value-format="yyyy-MM-dd"
              >
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="产权单位" prop="companyType">
              <dict-select
                v-model="formData.companyType"
                clearable
                placeholder="产权单位"
                style="width: 100%"
                type="company_type"
              ></dict-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="户号" prop="houseNum">
              <el-input v-model="formData.houseNum"/>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="户名称" prop="houseName">
              <el-input v-model="formData.houseName"/>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="计量点编号" prop="measureNum">
              <el-input v-model="formData.measureNum"/>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="变压器容量" prop="kva">
              <el-input v-model="formData.kva"/>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="变压器等级" prop="kv">
              <el-input v-model="formData.kv"/>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="用电地址" prop="electricityAddr">
              <el-input v-model="formData.electricityAddr"/>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="供电单位" prop="powerUnit">
              <el-input v-model="formData.powerUnit"/>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="市场化属性分类" prop="attribute">
              <el-input v-model="formData.attribute"/>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="电费缴纳单位" prop="payUnit">
              <el-input v-model="formData.payUnit"/>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="用电范围" prop="powerRange">
              <el-input v-model="formData.powerRange"/>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="用电性质" prop="electricalProperties">
              <el-input v-model="formData.electricalProperties"/>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="备注" prop="remark">
              <el-input v-model="formData.remark" :rows="3" type="textarea"/>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div style="text-align: right">
        <el-button @click="closeDialog">取消</el-button>
        <el-button type="primary" @click="handleSubmit">确定</el-button>
      </div>
    </el-dialog>
    <div class="mt20" style="text-align: right">
      <el-button v-if="pageType == 'view'" type="primary" @click="handleCheckData">保存</el-button>
    </div>
  </div>
</template>

<script>
import {
  addElectricity,
  deleteElectricity,
  editElectricity,
  listElectricityPage
} from '@/api/calculate/operationManageFee/threeElectricity';
import RoadSection from "@/views/baseData/components/roadSection/index.vue";
import RouteCodeSection from "@/components/RouteCodeSection/index.vue";
import SelectTree from "@/components/DeptTmpl/selectTree.vue";

export default {
  name: "YourComponentName",
  components: {SelectTree, RouteCodeSection, RoadSection},
  dicts: ['company_type'],
  props: {
    pageType: {
      type: String,
      default: 'edit'
    },
    maiSecId: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      // 遮罩层
      loading: false,
      // 显示搜索条件
      showSearch: false,
      // 总条数
      total: 0,
      dataList: [],
      selectDatas: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        maintenanceRoad: '',
        username: '',
        accountNumber: ''
      },
      // 表格列配置
      columns: [
        {key: 0, field: 'domainName', label: '管养单位', visible: true},
        {key: 1, field: 'routeCode', label: '路线编码', visible: true},
        {key: 2, field: 'maiSecName', label: '养护路段', visible: true},
        {key: 3, field: 'openTime', label: '通车时间', visible: true},
        {key: 4, field: 'companyType', label: '产权单位', visible: true, dict: 'company_type'},
        {key: 5, field: 'houseNum', label: '用电户号', visible: true},
        {key: 6, field: 'houseName', label: '用电户名', visible: true},
        {key: 7, field: 'measureNum', label: '计量点编号', visible: true},
        {key: 8, field: 'kva', label: '变压器容量(KVA)', visible: true},
        {key: 9, field: 'kv', label: '电压等级(KV)', visible: true},
        {key: 10, field: 'electricalProperties', label: '用电性质', visible: true},
        {key: 11, field: 'electricityAddr', label: '用电地址', visible: true},
        {key: 12, field: 'powerUnit', label: '供电单位', visible: true},
        {key: 13, field: 'attribute', label: '市场化属性分类', visible: true},
        {key: 14, field: 'powerRange', label: '用电范围', visible: true},
        {key: 15, field: 'payUnit', label: '电费缴纳单位', visible: true}
      ],
      // 对话框标题
      dialogTitle: '',
      // 对话框是否显示
      dialogVisible: false,
      // 表单数据
      formData: {},
      // 表单验证规则
      rules: {
        domainId: [
          {required: true, message: '请选择管养单位', trigger: 'change'}
        ],
        maiSecId: [
          {required: true, message: '请输入养护路段', trigger: 'change'}
        ],
        routeCode: [
          {required: true, message: '请输入路线编码', trigger: 'change'}
        ],
        openTime: [
          {required: true, message: '请选择通车时间', trigger: 'change'}
        ],
        companyType: [
          {required: true, message: '请选择产权单位', trigger: 'change'}
        ],
        houseNum: [
          {required: true, message: '请输入户号', trigger: 'blur'}
        ],
        houseName: [
          {required: true, message: '请输入户名称', trigger: 'blur'}
        ],
        measureNum: [
          {required: true, message: '请输入计量点编号', trigger: 'blur'}
        ]
      }
    };
  },
  created() {
    this.handleQuery();
  },
  methods: {
    handleQuery() {
      this.loading = true;
      if (this.maiSecId) this.queryParams.maiSecId = this.maiSecId
      listElectricityPage(this.queryParams).then(response => {
        this.dataList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    handleAdd() {
      this.dialogTitle = '新增';
      this.formData = {};
      this.dialogVisible = true;
    },
    handleEdit(row) {
      this.dialogTitle = '编辑';
      this.formData = {...row};
      this.dialogVisible = true;
    },
    closeDialog() {
      this.dialogVisible = false;
      this.resetForm("form");
    },
    handleSubmit() {
      this.$refs.elForm.validate(valid => {
        if (valid) {
          if (!this.formData.id) {
            addElectricity(this.formData).then(response => {
              this.$message.success('新增成功');
              this.closeDialog();
              this.handleQuery();
            });
          } else {
            editElectricity(this.formData).then(response => {
              this.$message.success('修改成功');
              this.closeDialog();
              this.handleQuery();
            });
          }
        }
      })
    },
    handleDelete(row) {
      this.$confirm('是否确认删除？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.loading = true;
        deleteElectricity(row.id).then(res => {
          if (res.code === 200) {
            this.$message.success('删除成功');
            this.handleQuery();
          } else {
            this.$message.error(res.msg);
          }
          this.loading = false;
        }).catch(err => {
          this.loading = false;
          console.error(err);
        });
      })
    },
    // 选中
    handleSelectionChange(e) {
      this.selectDatas = e
    },
    handleCheckData() {
      this.$emit('check', this.selectDatas)
    },
    // 导出清单按钮
    exportList() {
      this.download(
        "manager/operate/electricity/export",
        {...this.queryParams},
        `electricity_${new Date().getTime()}.xlsx`,
        {
          headers: {"Content-Type": "application/json;"},
          parameterType: "body",
        }
      );
    },
  }
}
</script>
<style scoped>
.tableDiv {
  margin-top: 20px;
}
</style>
<style lang="scss" scoped>
@import "@/assets/styles/business.scss";
</style>
