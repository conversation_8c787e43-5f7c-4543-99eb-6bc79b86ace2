<template>
  <div class="road-interflow-edit">
    <el-row :gutter="15">
      <el-form
          ref="elForm"
          :model="formData"
          :rules="rules"
          size="medium"
          label-width="140px"
      >
        <el-col :span="12">
          <el-form-item label="期数" prop="calcNumberId">
            <el-select v-model="formData.calcNumberId"  placeholder="期数" @change="numberChange" clearable style="width: 100%;" >
              <el-option v-for="item in numbers"
                         :key="item.value"
                         :label="item.label"
                         :value="item.value">
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="项目名称" prop="calcName">
            <el-input
                v-model="formData.calcName"
                placeholder="请输入项目名称"
                clearable
            >
            </el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="计量编号" prop="calcCode">
            <el-input
                v-model="formData.calcCode"
                placeholder="请输入计量编号"
                clearable
            >
            </el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="管养单位" prop="domainId">
            <select-tree
                v-model="formData.domainId"
                placeholder="请选择管养单位"
                :deptType="100"
                :deptTypeList="[1, 3, 4]"
                clearable
            ></select-tree>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="路段" prop="maiSecId">
            <road-section
                ref="roadSection"
                v-model="formData.maiSecId"
                :deptId="formData.domainId"
                placeholder="请选择路段"
                @change="maiSecChange"
            ></road-section>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="申请计量单位" prop="conDomainName">
            <el-input v-model="formData.conDomainName" placeholder="请输入申请计量单位" style="width: 100%"/>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="计量日期" prop="calcDate">
            <el-date-picker
                clearable
                v-model="formData.calcDate"
                type="date"
                value-format="yyyy-MM-dd"
                style="width: 100%"
                placeholder="请选择计量日期"
            >
            </el-date-picker>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="上期计量单" prop="preCalcId">
            <el-select
                v-model="formData.preCalcId"
                placeholder="请输入上期计量单"
                @change="preCalcChange"
                style="width: 100%"
                clearable
            >
              <el-option v-for="item in preCalcList" :key="item.id" :value="item.id" :label="item.calcName + item.calcCode"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="备注" prop="remark">
            <el-input
                type="textarea"
                v-model="formData.remark"
                placeholder="请输入备注"
                clearable
                :style="{ width: '100%' }"
            >
            </el-input>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="附件">
            <file-upload v-model="formData.fileId" :owner-id="formData.fileId"></file-upload>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <div class="mb10" style="text-align: right">
            <el-button type="primary" @click="onAddMethod">添加</el-button>
          </div>
          <el-table v-adjust-table
              :data="methodList"
              border
              height="300px"
              style="width: 100%"
          >
            <el-table-column width="50" align="center" label="序号" type="index">
            </el-table-column>
            <el-table-column
                prop="mtype"
                align="center"

                label="养护类型"
            >
              <template slot-scope="scope">
                <dict-select @change="changeMtype(scope.row)" style="width: 100%;" v-model="scope.row.mtype" type="maintenance_type"/>
              </template>
            </el-table-column>
            <el-table-column
                prop="projType"
                align="center"

                label="所属工程类型"
            >
              <template slot-scope="scope">
                <dict-select v-if="scope.row.mtype == 2" :disabled="scope.row.mtype!=2" style="width: 100%;" v-model="scope.row.projType" type="affiliation_project_type"/>
              </template>
            </el-table-column>
            <el-table-column
                prop="fundType"
                align="center"

                label="费用类型"
            >
              <template slot-scope="scope">
                <dict-select v-if="scope.row.mtype == 2" style="width: 100%;" v-model="scope.row.fundType" type="base_project_type"/>
                <cost-select v-else :type="getFundDict(scope.row.mtype)"
                             :key="scope.row.mtype + '2'"
                             v-model="scope.row.fundType"
                             style="width: 100%;"
                />
              </template>
            </el-table-column>
            <el-table-column
                prop="schemeCode"
                align="center"

                label="子目号"
            >
              <template slot-scope="scope">
                <el-input style="width: 100%;" v-model="scope.row.schemeCode" />
              </template>
            </el-table-column>
            <el-table-column
                prop="schemeName"
                align="center"

                label="子目名称"
            >
              <template slot-scope="scope">
                <el-input style="width: 100%;" v-model="scope.row.schemeName" />
              </template>
            </el-table-column>
            <el-table-column
                prop="price"
                align="center"

                label="单价"
            >
              <template slot-scope="scope">
                <el-input @change="changePrice(scope.row)" style="width: 100%;" v-model="scope.row.price" />
              </template>
            </el-table-column>
            <el-table-column
                prop="unit"
                align="center"

                label="单位"
            >
              <template slot-scope="scope">
                <el-input style="width: 100%;" v-model="scope.row.unit" />
              </template>
            </el-table-column>
            <el-table-column
                prop="schemeNum"
                align="center"

                label="数量"
            >
              <template slot-scope="scope">
                <el-input @change="changePrice(scope.row)" style="width: 100%;" v-model="scope.row.schemeNum" />
              </template>
            </el-table-column>
            <el-table-column
                prop="schemeFund"
                align="center"

                label="金额"
            >
              <template slot-scope="scope">
                <el-input style="width: 100%;" v-model="scope.row.schemeFund" />
              </template>
            </el-table-column>
            <el-table-column
                prop="remark"
                align="center"

                label="备注"
            >
              <template slot-scope="scope">
                <el-input style="width: 100%;" v-model="scope.row.remark" />
              </template>
            </el-table-column>
            <el-table-column
                prop="remark"
                align="center"

                label="操作"
            >
              <template slot-scope="scope">
                <el-button type="text" @click="onDeleteMethod(scope.row)">移除</el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-col>
        <el-col
            :span="24"
            style="text-align: right; padding-right: 7.5px; margin-top: 18px"
        >
          <el-button type="primary" @click="onSave">保 存</el-button>
          <el-button @click="onClose">退 出</el-button>
        </el-col>
      </el-form>
    </el-row>
  </div>
</template>
<script>
import SelectTree from "@/components/DeptTmpl/selectTree.vue";
import RoadSection from "@/views/baseData/components/roadSection/index.vue";
import {
  addContractOtherFundCalc,
  editContractOtherFundCalc, getContractOtherFundCalcDetail, getPreCalc
} from "@/api/calculate/contractCalc/contractCalc";
import ContractSection from "@/components/ContractSection/index.vue";
import ConstructionSelect from "@/components/ConstructionSelect/index.vue";
import moment from "moment/moment";
import {getNumbers} from "@/api/dailyMaintenance/metering/settlementApplication";
import CostSelect from "@/components/CostSelect/index.vue";
import {v4 as uuidv4} from "uuid";

export default {
  name: "index",
  components: {CostSelect, ConstructionSelect, ContractSection, RoadSection, SelectTree },
  data() {
    return {
      formData: {
        calcDate:  moment().format('YYYY-MM-DD')
      },
      loading: false,
      methodList: [],
      filterMap: {},
      numbers: [],
      rules: {
        calcNumberId: [{ required: true, message: "请输入期数", trigger: "blur" }],
        calcName: [{ required: true, message: "请输入项目名称", trigger: "blur" }],
        calcCode: [{ required: true, message: "请输入计量编号", trigger: "blur" }],
        domainId: [
          { required: true, message: "请输入管养单位", trigger: "blur" },
        ],
        maiSecId: [{ required: true, message: "请输入路段", trigger: "blur" }],
        conDomainName: [
          { required: true, message: "请输入申请计量单位", trigger: "blur" },
        ],
        calcDate: [
          { required: true, message: "请选择计量日期", trigger: "change" },
        ],
      },
      preCalcList: [],
      fundDict: '25'
    };
  },
  props: {
    rowData: {
      type: Object,
      default: () => {
        return {};
      },
    },
  },
  watch: {
    rowData: {
      handler(val) {
        if (val.id) {
          this.filterMap = {
            sectionName: val.maiSecName,
          }
          getContractOtherFundCalcDetail(val.id).then(res => {
            this.formData = res.data
            this.methodList = this.formData.detailList
            this.formData.domainId = String(this.formData.domainId);
            if(!this.formData.fileId) {
              this.formData.fileId = uuidv4().replace(/-/g, '').slice(0, 20)
            }
            this.getPreCalcList()
          })
        }
        if(!this.formData.fileId) {
          this.formData.fileId = uuidv4().replace(/-/g, '').slice(0, 20)
        }
      },
      immediate: true,
    },
  },
  mounted() {},
  created() {
    this.getNumbers()
  },
  methods: {
    getFundDict(mtype) {
      switch (mtype) {
        case '1':
          return 9
        case '2':
          return 25
        case '3':
          return 35
        case '4':
          return 6
      }
    },
    onSave() {
      this.$refs.elForm.validate((valid) => {
        if (!valid) return;
        if (
            this.formData.fileId &&
            Array.isArray(this.formData.fileId) &&
            this.formData.fileId.length > 0
        ) {
          this.formData.fileId = this.formData.fileId[0];
        } else if (Array.isArray(this.formData.fileId) &&
          this.formData.fileId.length == 0){
          this.formData.fileId = null
        }
        for (let i = 0; i < this.methodList.length; i++) {
          let item = this.methodList[i]
          if (!item.mtype || !item.fundType || !item.schemeCode || !item.schemeName || !item.schemeFund || !item.price || !item.unit || !item.schemeNum) {
            this.$modal.msgError("请完整填写费用明细");
            return
          }
        }
        this.formData.detailList = this.methodList
        if (this.formData.id) {
          editContractOtherFundCalc(this.formData).then(() => {
            this.$modal.msgSuccess("保存成功");
            this.onClose();
          });
        } else {
          addContractOtherFundCalc(this.formData).then(() => {
            this.$modal.msgSuccess("保存成功");
            this.onClose();
          });
        }
      });
    },
    onAddMethod() {
      this.methodList.push({})
    },
    onDeleteMethod(row) {
      this.methodList.splice(this.methodList.indexOf(row), 1);
    },
    getNumbers() {
      getNumbers().then(res => {
        res.data.forEach(item => {
          this.numbers.push({
            label: item.name,
            value: item.id
          })
        })
      })
    },
    getFilterMap(e) {
      const sectionName = this.$refs.roadSection.getLabel(this.formData.maiSecId)
      this.filterMap = {
        sectionName,
        conDomainId: e
      }
    },
    numberChange() {
      this.preCalcList = []
      this.getPreCalcList()
    },
    maiSecChange() {
      this.preCalcList = []
      this.getPreCalcList()
    },
    preCalcChange() {
      this.$forceUpdate()
    },
    getPreCalcList() {
      if (this.formData.calcNumberId && this.formData.maiSecId) {
        const subData = {
          calcNumberId: this.formData.calcNumberId,
          maiSecId: this.formData.maiSecId,
        }
        if (this.rowData && this.rowData.id) {
          subData.id = this.rowData.id
        }
        getPreCalc(subData).then(res => {
          console.log(res);
          this.preCalcList = res.data
        })
      }
    },
    changeMtype(row) {
      this.$set(row, 'projType', '')
      this.$set(row, 'fundType', '')
    },
    changePrice(row) {
      let price =  row.price || 0
      let num = row.schemeNum || 0
      this.$set(row, 'schemeFund', price * num)
    },
    onClose() {
      this.$emit("close");
    },
  },
};
</script>
<style scoped lang="scss">
.card_title {
  width: 200px;
  text-align: left;
  margin-bottom: 15px;
  font-weight: bold;
}
</style>
<style lang="scss" scoped>
@import "@/assets/styles/business.scss";
</style>
