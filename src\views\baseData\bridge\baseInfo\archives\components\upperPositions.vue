<template>
    <PageContainer :ifSearch="false" :ifHeader="false" :class="oneMap ? 'oneMap-dark' : ''"
      :style="oneMap ? 'padding: 0' : ''" >
      <template slot="body">
        <el-row style="height: 100%;">
          <el-col :span="6" style="height: 100%;overflow-y: auto;">
            <el-menu
              style="height: 100%;"
              :class="oneMap ? 'dark' : ''"
              :default-openeds="['1','2','3']"
              class="el-menu-vertical-demo"
              @select="handleSelect"
              >
  
              <el-submenu index="1">
                <template slot="title" >
                  <span style="font-weight: 800;font-size: 16px;">
                    <i class="el-icon-s-unfold"></i>
                    <span>上部结构</span>
                  </span>
                </template>
               
                <el-menu-item-group >
                  <el-menu-item :index="item1.id" v-for="(item1,index1) in upperPositions" :key="index1">
                    <template slot="default">
                      <div class="titleStyle">{{item1.positionName}}</div>
                      <div class="numberStyle">
                        <i class="el-icon-s-unfold"></i><span >构件数： {{item1.componentCount}}</span>
                      </div>
                    </template>
                  </el-menu-item>
                </el-menu-item-group>
              </el-submenu>
              <el-submenu index="2">
                <template slot="title" >
                  <span style="font-weight: 800;font-size: 16px;">
                    <i class="el-icon-s-unfold"></i>
                    <span>下部结构</span>
                  </span>
                </template>
               
                <el-menu-item-group >
                  <el-menu-item :index="item2.id" v-for="(item2,index2) in downPositions" :key="index2">
                    <template slot="default">
                      <div class="titleStyle">{{item2.positionName}}</div>
                      <div class="numberStyle">
                        <i class="el-icon-s-unfold"></i><span >构件数： {{item2.componentCount}}</span>
                      </div>
                    </template>
                  </el-menu-item>
                </el-menu-item-group>
  
              </el-submenu>

              <el-submenu index="3">
                <template slot="title" >
                  <span style="font-weight: 800;font-size: 16px;">
                    <i class="el-icon-s-unfold"></i>
                    <span>桥面系</span>
                  </span>
                </template>
              
                <el-menu-item-group >
                  <el-menu-item :index="item3.id" v-for="(item3,index3) in deckPositions" :key="index3">
                    <template slot="default">
                      <div class="titleStyle">{{item3.positionName}}</div>
                      <div class="numberStyle">
                        <i class="el-icon-s-unfold"></i><span >构件数： {{item3.componentCount}}</span>
                      </div>
                    </template>
                  </el-menu-item>
                </el-menu-item-group>

              </el-submenu>
  
            </el-menu>
          </el-col>
          <el-col :span="18" style="height: calc(100% - 20px)" :class="oneMap ? 'one-map' : ''">
            <el-table
              ref="table"
              height="calc(100% - 30px)"
              style="width: 100%;"
              :cell-style="{'height': '36px'}"
              :row-style="rowStyle"
              border
              v-loading="loading"
              :data="dataList"
              @selection-change="handleSelectionChange"
              @row-click="handleRowClick"
              :element-loading-background="oneMap ? 'rgba(2, 10, 30, 0.5)' : ''"
            >
              <el-table-column
                type="selection"
                width="50"
                align="center"
              />
             
              <el-table-column
                label="构件名称"
                align="center"
                prop="componentName"
                min-width="140"
                show-overflow-tooltip
              />
              <el-table-column
                
                label="构件编码"
                align="center"
                prop="componentCode"
                min-width="140"
                show-overflow-tooltip
              />
            
              <el-table-column
                label="部件id"
                align="center"
                prop="positionId"
                min-width="140"
                show-overflow-tooltip
              />
              <el-table-column
                label="操作"
                align="center"
                width="80"
              >
                <template slot-scope="scope">
                  <el-button
                    type="text"
                    @click="deleteData(scope.row)"
                  >删除</el-button>
                </template>
              </el-table-column>
            </el-table>
            <pagination
              :total="total"
              :page.sync="queryParams.pageNum"
              :limit.sync="queryParams.pageSize"
              :pageSizes="[10, 20, 30, 50, 100, 1000]"
              @pagination="getList"
            />
          </el-col>
        </el-row>
      
    </template>
     
    </PageContainer>
  </template>
  
  <script>
  import {
    getMemberPosition,
    getMemberComponent,
    delMember
  } from '@/api/baseData/bridge/baseInfo/index'
  

  
  
  export default {
    name: 'periodic-baseInfo',
    components: {},
    dicts: [],
    props: {
        id: {
          type: String || Number,
          default: ''
        }
    },
    inject: {
      oneMap: {
        default: false
      }
    },
    data() {
      return {
        loading: true,
        ids: [],
        single: true,
        multiple: true,
        setNumber:{},
        total: 0,
        upperPositions: [],
        deckPositions: [],
        downPositions: [],
        dataList: [],
        queryParams: {
          pageNum: 1,
          pageSize: 20
        },

      }
    },
    watch: {},
    created() {
      this.getList()
     
    },
    methods: {
    
      // 获取表格数据
      getList() {
        this.loading = true
        getMemberPosition(this.id).then(response => {
          this.upperPositions = response.upperPositions
          this.deckPositions = response.deckPositions
          this.downPositions = response.downPositions
          this.loading = false
          if (response.upperPositions.length > 0) {
            getMemberComponent({bridgeId:this.id,positionId:response.upperPositions[0].id,...this.queryParams}).then(res => {
              this.dataList = res.rows
              this.total = res.total
            })
          }
        })
      },
      // 多选框选中数据
      handleSelectionChange(selection) {
        this.ids = selection.map(item => item.id)
        this.single = selection.length != 1
        this.multiple = !selection.length
      },
      // 勾选高亮
      rowStyle({ row, rowIndex }) {
        if (this.ids.includes(row.id)) {
          return { 'background-color': '#b7daff', color: '#333' }
        } else {
          return { 'background-color': '#fff', color: '#333' }
        }
      },
      // 搜索按钮
      handleQuery() {
        this.queryParams.pageNum = 1
        this.getList()
      },
      // 重置按钮
      resetQuery() {
   
  
   
        this.queryParams = {}
        this.handleQuery()
      },
      // 表格点击勾选
      handleRowClick(row) {
        row.isSelected = !row.isSelected
        this.$refs.table.toggleRowSelection(row)
      },
      handleSelect(index) {
        this.loading = true
        getMemberComponent({bridgeId:this.id,positionId:index,...this.queryParams}).then(res => {
          this.dataList = res.rows
          this.total = res.total
          this.loading = false
        })
      },
  
  
      // 删除
      deleteData(row) {
        this.$confirm('是否删除该数据？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          delMember([row.id]).then(() => {
            this.$message({
              type: 'success',
              message: '删除成功'
            })
            this.getList()
          })
        })
      },
  
    }
  }
  </script>
  
  <style lang="scss" scoped>
  @import "@/assets/styles/common.scss";
  
  .numberStyle{
    font-size: 12px;
    font-weight: 300;
    margin-left: 13%;
    color: #858888;
  }
  
  .titleStyle{
    
    font-size: 16px;
    font-weight: 900;
  }
  
  ::v-deep .el-menu-item{
    height: 100px !important;
    margin: 0% 4%;
  }
  
  ::v-deep .el-menu-item.is-active{
    color: black;
  }
  
  .el-submenu{
    margin: 10% 0;
  }

  .dark {
    background: #0166fe33;
    border-right: none !important;
    ::v-deep{
      .el-menu{
        background: #0166fe33;
      }
      .el-submenu__title {
        color: #fff
      }
      .el-menu-item-group__title {
        padding: 0;
      }
    }
  }

  </style>

<style>
.oneMap-dark {
  .container-body {
    background: unset !important;
    border: 1px solid #0166fe !important;
  }
  .container-view-list {
    padding: 0 !important;
  }
}
</style>
  