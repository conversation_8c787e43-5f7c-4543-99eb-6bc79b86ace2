<template>
  <div class="road-interflow-edit">
    <el-row :gutter="15">
      <el-form
        ref="elForm"
        :model="formData"
        :rules="rules"
        size="medium"
        label-width="140px"
      >
        <el-col :span="12">
          <el-form-item label="期数" prop="number">
            <el-select
              v-model="formData.number"
              placeholder="期数"
              clearable
              @change="numberChange"
              style="width: 100%"
            >
              <el-option
                v-for="item in numbers"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              >
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="计量名称" prop="name">
            <el-input
              v-model="formData.name"
              placeholder="请输入计量名称"
              clearable
            >
            </el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="计量编号" prop="code">
            <el-input
              v-model="formData.code"
              placeholder="请输入计量编号"
              clearable
            >
            </el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="管养单位" prop="domainId">
            <select-tree
              v-model="formData.domainId"
              placeholder="请选择管养单位"
              :deptType="100"
              :deptTypeList="[1, 3, 4]"
              clearable
              ></select-tree
            >
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="路段" prop="maiSecId">
            <road-section
              ref="roadSection"
              v-model="formData.maiSecId"
              :deptId="formData.domainId"
              placeholder="请选择路段"
              @change="maiSecChange"
            ></road-section>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="申请计量单位" prop="calcDomainId">
            <construction-select
              :type="0"
              @change="getFilterMap"
              v-model="formData.calcDomainId"
              :mai-sec-id="formData.maiSecId"
              :mai-domain-id="formData.domainId"
              add-user-id
              placeholder="申请计量单位"
            ></construction-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="合同" prop="conId">
            <contract-section
              ref="contractSectionRef"
              v-model="formData.conId"
              :params="filterMap"
              @change="conChange"
              placeholder="请选择合同"
            ></contract-section>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="计量日期" prop="calcDate">
            <el-date-picker
              clearable
              v-model="formData.calcDate"
              type="date"
              value-format="yyyy-MM-dd"
              style="width: 100%"
              placeholder="请选择计量日期"
            >
            </el-date-picker>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="上期计量单" prop="preCalcid">
            <el-select
              v-model="formData.preCalcid"
              placeholder="请输入上期计量单"
              @change="preCalcChange"
              clearable
            >
              <el-option
                v-for="item in preCalcList"
                :key="item.id"
                :value="item.id"
                :label="item.name + item.code"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="备注" prop="remark">
            <el-input
              type="textarea"
              v-model="formData.remark"
              placeholder="请输入备注"
              clearable
              :style="{ width: '100%' }"
            >
            </el-input>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="附件" prop="remark">
            <file-upload
              v-model="formData.fileId"
              :owner-id="formData.fileId"
            ></file-upload>
          </el-form-item>
        </el-col>
        <el-col
          :span="24"
          style="text-align: right; padding-right: 7.5px; margin-top: 18px"
        >
          <el-button type="primary" @click="onSave">保 存</el-button>
          <el-button @click="onClose">退 出</el-button>
        </el-col>
      </el-form>
    </el-row>
  </div>
</template>
<script>
import SelectTree from "@/components/DeptTmpl/selectTree.vue";
import RoadSection from "@/views/baseData/components/roadSection/index.vue";
import {
  addSettlecalc,
  editSettlecalc,
  getNumbers,
  getPreCalc
} from "@/api/dailyMaintenance/metering/settlementApplication";
import ContractSection from "@/components/ContractSection/index.vue";
import ConstructionSelect from "@/components/ConstructionSelect/index.vue";
import moment from "moment/moment";
import {v4 as uuidv4} from "uuid";

export default {
  name: "index",
  components: { ConstructionSelect, ContractSection, RoadSection, SelectTree },
  data() {
    return {
      formData: {},
      loading: false,
      methodList: [],
      filterMap: {},
      numbers: [],
      rules: {
        number: [{ required: true, message: "请输入期数", trigger: "blur" }],
        name: [{ required: true, message: "请输入计量名称", trigger: "blur" }],
        code: [{ required: true, message: "请输入计量编号", trigger: "blur" }],
        domainId: [
          { required: true, message: "请输入管养单位", trigger: "blur" },
        ],
        maiSecId: [{ required: true, message: "请输入路段", trigger: "blur" }],
        calcDomainId: [
          { required: true, message: "请输入申请计量单位", trigger: "blur" },
        ],
        conId: [{ required: true, message: "请输入合同", trigger: "blur" }],
        calcDate: [
          { required: true, message: "请选择计量日期", trigger: "change" },
        ],
      },
      preCalcList: [],
    };
  },
  props: {
    rowData: {
      type: Object,
      default: () => {
        return {};
      },
    },
  },
  watch: {
    rowData: {
      handler(val) {
        if (val.id) {
          this.filterMap = {
            sectionName: val.maiSecName,
            conDomainId: val.calcDomainId,
          };
          this.formData = JSON.parse(JSON.stringify(val));
          this.formData.calcDomainId = String(this.formData.calcDomainId);
          this.formData.domainId = String(this.formData.domainId);
          this.getPreCalcList();
        } else {
          this.$set(this.formData, 'calcDate', moment().format("YYYY-MM-DD"))
        }
        if(!this.formData.fileId) {
          this.formData.fileId = uuidv4().replace(/-/g, '').slice(0, 20)
        }
      },
      immediate: true,
    },
  },
  created() {
    this.getNumbers();
  },
  mounted() {},
  methods: {
    onSave() {
      this.$refs.elForm.validate((valid) => {
        if (!valid) return;
        if (
          this.formData.fileId &&
          Array.isArray(this.formData.fileId) &&
          this.formData.fileId.length > 0
        ) {
          this.formData.fileId = this.formData.fileId[0];
        } else if (Array.isArray(this.formData.fileId) &&
          this.formData.fileId.length == 0){
          this.formData.fileId = null
        }
        if (this.formData.id) {
          editSettlecalc(this.formData).then(() => {
            this.$modal.msgSuccess("保存成功");
            this.onClose();
          });
        } else {
          addSettlecalc(this.formData).then(() => {
            this.$modal.msgSuccess("保存成功");
            this.onClose();
          });
        }
      });
    },
    getFilterMap(e) {
      const sectionName = this.$refs.roadSection.getLabel(
        this.formData.maiSecId
      );
      this.filterMap = {
        sectionName,
        conDomainId: e,
      };
    },
    getNumbers() {
      getNumbers().then((res) => {
        res.data.forEach((item) => {
          this.numbers.push({
            label: item.name,
            value: item.id,
          });
        });
      });
    },
    numberChange() {
      this.formData.preCalcid = "";
      this.preCalcList = [];
      this.getPreCalcList();
    },
    maiSecChange() {
      this.preCalcList = [];
      this.getPreCalcList();
    },
    conChange() {
      this.formData.preCalcid = "";
      this.preCalcList = [];
      this.getPreCalcList();
    },
    preCalcChange() {
      this.$forceUpdate();
    },
    getPreCalcList() {
      if (this.formData.number && this.formData.maiSecId && this.formData.conId) {
        const subData = {
          number: this.formData.number,
          maiSecId: this.formData.maiSecId,
          conId: this.formData.conId,
        };
        if (this.rowData && this.rowData.id) {
          subData.settleId = this.rowData.id;
        }
        getPreCalc(subData).then((res) => {
          console.log(res);
          this.preCalcList = res.data;
        });
      }
    },
    onClose() {
      this.$emit("close");
    },
  },
};
</script>
<style scoped lang="scss">
.card_title {
  width: 200px;
  text-align: left;
  margin-bottom: 15px;
  font-weight: bold;
}
</style>
<style lang="scss" scoped>
@import "@/assets/styles/business.scss";
</style>
