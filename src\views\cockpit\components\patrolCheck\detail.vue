<template>
  <div class="patrol-check-detail">
    <el-row :gutter="10" style="margin-top: 5px;">
      <el-col :xs="6" :sm="6" :md="6" :lg="6" :xl="6">
        <CockpitCard title="日常巡查" :isDtl="false" w="100%" h="calc(24vh)">
          <DailyInspection />
        </CockpitCard>
        <CockpitCard title="巡查病害上报数" :isDtl="false" w="100%" h="calc(28vh)" class="mt-1">
          <DiseaseReport />
        </CockpitCard>
      </el-col>
      <el-col :xs="6" :sm="6" :md="6" :lg="6" :xl="6">
        <CockpitCard title="本月桥梁经常检查" :isDtl="false" w="100%" h="calc(24vh)">
          <RegularInspection v-if="qlObj.data[1].value" :progress="qlObj.progress" color="#00FFB7" :data="qlObj.data" key="qlKey" />
        </CockpitCard>
        <CockpitCard title="各管理处桥梁经常检查情况" :isDtl="false" w="100%" h="calc(28vh)" class="mt-1">
          <Tables :columns="columns" :data="qlTableData" />
        </CockpitCard>
      </el-col>
      <el-col :xs="6" :sm="6" :md="6" :lg="6" :xl="6">
        <CockpitCard title="本月隧道经常检查" :isDtl="false" w="100%" h="calc(24vh)">
          <RegularInspection v-if="sdObj.data[1].value" :progress="sdObj.progress" color="#E333B7" :data="sdObj.data" key="sdKey" />
        </CockpitCard>
        <CockpitCard title="各管理处隧道经常检查情况" :isDtl="false" w="100%" h="calc(28vh)" class="mt-1">
          <Tables :columns="columns" :data="sdTableData" />
        </CockpitCard>
      </el-col>
      <el-col :xs="6" :sm="6" :md="6" :lg="6" :xl="6">
        <CockpitCard title="本月涵洞经常检查" :isDtl="false" w="100%" h="calc(24vh)">
          <RegularInspection v-if="holeObj.data[1].value" :progress="holeObj.progress" color="#7335FC" :data="holeObj.data" key="hdKey" />
        </CockpitCard>
        <CockpitCard title="各管理处涵洞经常检查情况" :isDtl="false" w="100%" h="calc(28vh)" class="mt-1">
          <Tables :columns="columns" :data="hdTableData" />
        </CockpitCard>
      </el-col>
    </el-row>

    <el-row :gutter="10" class="mt-1">
      <el-col :xs="12" :sm="12" :md="12" :lg="12" :xl="12">
        <CockpitCard title="巡查病害趋势" :isDtl="false" w="100%" h="calc(26vh)" hBg="l">
          <template slot="more">
            <el-select v-model="tType" placeholder="请选择" clearable style="width: 100px;" :popper-append-to-body="false">
              <el-option label="年" :value="1" />
              <el-option label="月" :value="2" />
            </el-select>
          </template>
          <TrendChart :type="tType" />
        </CockpitCard>
      </el-col>
      <el-col :xs="12" :sm="12" :md="12" :lg="12" :xl="12">
        <CockpitCard title="各管理处巡查里程统计" :isDtl="false" w="100%" h="calc(26vh)" hBg="l">
          <template slot="more">
            <el-select v-model="mType" placeholder="请选择" clearable style="width: 100px;" :popper-append-to-body="false">
              <el-option label="年" :value="1" />
              <el-option label="月" :value="2" />
            </el-select>
          </template>
          <MileageStatistics :type="mType" :year="year" />
        </CockpitCard>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import CockpitCard from '../cockpitCard.vue';
import DailyInspection from './component/dailyInspection.vue';
import DiseaseReport from './component/diseaseReport.vue';
import Tables from '../tables.vue';
import RegularInspection from './component/regularInspection.vue';
import TrendChart from './component/TrendChart.vue';
import MileageStatistics from './component/mileageStatistics.vue';
// api
import { getBridgePatrol, getTunnelPatrol, getHolePatrol, getBridgeOftenCountByManager, getTunnelOftenCountByManager, getHoleOftenCountByManager } from '@/api/cockpit/inspection';


export default {
  components: {
    CockpitCard,
    DailyInspection, // 日常巡查
    DiseaseReport, // 巡查病害上报数
    Tables, // 表格
    RegularInspection, // 经常检查
    TrendChart, // 巡查病害趋势
    MileageStatistics, // 各管理处��查里程统计
  },
  data() {
    return {
      columns: [
        { prop: 'index', label: '序号', width: 50 },
        { prop: 'deptName', label: '管理处' },
        { prop: 'toBeCheckedNum', label: '待检查(座)', width: 80 },
        { prop: 'checkedNum', label: '已查(座)', width: 70 },
        { prop: 'checkedAccountFor', label: '已检占比(%)', width: 100, af: true },
      ],
      qlTableData: [],
      sdTableData: [],
      hdTableData: [],
      qlObj: {
        title: '桥梁经常检查',
        data: [
          {
            name: '累计检查次数',
            value: 0,
            unit: '次'
          },
          {
            name: '待检查桥梁',
            value: 0,
            unit: '座'
          },
          {
            name: '已检查桥梁',
            value: 0,
            unit: '座'
          },
        ],
        progress: 0,
      },
      sdObj: {
        title: '隧道经常检查',
        data: [
          {
            name: '累计检查次数',
            value: 0,
            unit: '次'
          },
          {
            name: '待检查隧道',
            value: 0,
            unit: '座'
          },
          {
            name: '已检查隧道',
            value: 0,
            unit: '座'
          }
        ],
        progress: 0,
      },
      holeObj: {
        title: '涵洞经常检查',
        data: [
          {
            name: '累计检查次数',
            value: 0,
            unit: '次'
          },
          {
            name: '待检查涵洞',
            value: 0,
            unit: '座'
          },
          {
            name: '已检查涵洞',
            value: 0,
            unit: '座'
          }
        ],
        progress: 0,
      },
      year: '',
      tYear: '',
      years: [],
      tType: 1,
      mType: 1,
      dateKey: 'dateKey' + new Date().getTime(), // 重置datePicker key，避免首次选择日期时，无法重新选择的bug
      pickerOptions: {
        shortcuts: [{
          text: '最近一周',
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
            picker.$emit('pick', [start, end]);
          }
        }, {
          text: '最近一个月',
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
            picker.$emit('pick', [start, end]);
          }
        }, {
          text: '最近三个月',
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
            picker.$emit('pick', [start, end]);
          }
        }]
      },
    }
  },
  created() {
    this.getYear();
    this.getPieData();
    this.getTableData();
  },
  mounted() { },
  methods: {
    getYear() {
      let date = new Date();
      let year = date.getFullYear();
      this.year = year;
      this.tYear = year + '';
      for (let i = year - 10; i < year + 1; i++) {
        this.years.push(i);
      }
    },
    getPieData() {
      // 获取桥梁数据
      getBridgePatrol().then(res => {
        if (res.code === 200 && res.data) {
          this.qlObj.data[0].value = res.data.diseaseNum || 0;
          this.qlObj.data[1].value = res.data.toBeCheckedNum || 0;
          this.qlObj.data[2].value = res.data.checkedNum || 0;
          // 累加 value 
          let sum = 1;
          sum = this.qlObj.data?.reduce((acc, curr) => acc + curr.value, 0) || 1;
          let progress = 0;
          progress = res.data.checkedNum ? (res.data.checkedNum / sum * 100).toFixed(2) : 0;
          this.qlObj.progress = res.data.checkedAccountFor ? (res.data.checkedAccountFor * 100).toFixed(2) : progress;
        }
      })

      // 获取隧道数据
      getTunnelPatrol().then(res => {
        if (res.code === 200 && res.data) {
          // 隧道数据暂时不做处理
          this.sdObj.data[0].value = res.data.diseaseNum || 0;
          this.sdObj.data[1].value = res.data.toBeCheckedNum || 0;
          this.sdObj.data[2].value = res.data.checkedNum || 0;
          // 累加 value 
          let sum = 1;
          sum = this.sdObj.data?.reduce((acc, curr) => acc + curr.value, 0) || 1;
          let progress = 0;
          progress = res.data.checkedNum ? (res.data.checkedNum / sum * 100).toFixed(2) : 0;
          this.sdObj.progress = res.data.checkedAccountFor ? (res.data.checkedAccountFor * 100).toFixed(2) : progress;
        }
      })

      // 获取孔道数据
      getHolePatrol().then(res => {
        if (res.code === 200 && res.data) {
          // 孔道数据暂时不做处理
          this.holeObj.data[0].value = res.data.diseaseNum || 0;
          this.holeObj.data[1].value = res.data.toBeCheckedNum || 0;
          this.holeObj.data[2].value = res.data.checkedNum || 0;
          // 累加 value 
          let sum = 1;
          sum = this.holeObj.data.reduce((acc, curr) => acc + curr.value, 0) || 1;
          let progress = 0;
          progress = res.data.checkedNum ? (res.data.checkedNum / sum * 100).toFixed(2) : 0;
          this.holeObj.progress = res.data.checkedAccountFor ? (res.data.checkedAccountFor * 100).toFixed(2) : progress;
        }
      })
    },
    getTableData() {
      getBridgeOftenCountByManager().then(res => {
        if (res.code === 200 && res.data) {
          this.qlTableData = res.data || [];
        }
      })
      getTunnelOftenCountByManager().then(res => {
        // console.log('隧道数据', res)
        if (res.code === 200 && res.data) {
          this.sdTableData = res.data || [];
        }
      })

      getHoleOftenCountByManager().then(res => {
        // console.log('涵洞数据', res)
        if (res.code === 200 && res.data) {
          this.hdTableData = res.data || [];
        }
      })
    },
  },
}
</script>

<style lang="scss" scoped>
@import "@/assets/styles/utils.scss";

.patrol-check-detail {
  width: 100%;
  padding: 0 10px;

  .mt-1 {
    margin-top: 8px;
  }

  ::v-deep .el-input {
    .el-input__inner {
      height: vwpx(300px);
      height: vwpx(60px);
      background-color: rgba(1, 102, 254, 0.2);
      border: 1px solid #0166fe;
      color: #ffffff;
      font-size: vwpx(30px);
    }

    .el-input__inner::placeholder {
      color: #bbbbbb;
    }

    .el-input-group__append {
      background-color: rgba(1, 102, 254, 0.2);
      border: 1px solid #0166fe;
      color: #ffffff;
      border-left: none;
      padding: 0 10px;
      cursor: pointer;
    }
  }
}
</style>