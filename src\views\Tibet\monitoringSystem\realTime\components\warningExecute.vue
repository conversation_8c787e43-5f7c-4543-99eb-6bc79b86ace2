<template>
  <div class="app-container">
    <div class="target-list">
      <div v-for="item in targetList" class="targetBox">
        <div class="title">{{ item.title }}</div>
        <div class="num"><img :src="item.url" alt="" />{{ item.num }}</div>
      </div>
    </div>
    <el-row :gutter="20">
      <!-- 筛选区开始 -->
      <el-col :span="24" :xs="24">
        <el-row>
          <el-col :span="24">
            <el-form ref="queryForm" :inline="true" :model="queryParams" label-width="68px" size="small">
              <el-form-item label="" prop="planName">
                <el-select v-model="queryParams.planName" placeholder="请选择预案模式" clearable popper-class="type-popper"
                  :popper-append-to-body="false">
                  <el-option v-for="item in alertLevelList" :key="item.value" :label="item.label" :value="item.value" />
                </el-select>
              </el-form-item>
              <el-form-item label="" prop="planStatus">
                <el-select v-model="queryParams.planStatus" placeholder="请选择处理状态" clearable popper-class="type-popper"
                  :popper-append-to-body="false">
                  <el-option label="成功" value="成功" />
                  <el-option label="失败" value="失败" />
                </el-select>
              </el-form-item>
              <el-form-item>
                <el-button icon="el-icon-search" size="mini" type="primary" @click="handleQuery">搜索</el-button>
                <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
              </el-form-item>
            </el-form>
          </el-col>
        </el-row>
        <!-- 筛选区结束 -->
        <!-- 数据表格开始 -->
        <div class="tableDiv">
          <el-table v-loading="loading" :data="dataList"
            :height="pageType == 'edit' ? 'calc(100vh - 260px)' : 'calc(70vh - 260px)'" border size="mini"
            style="width: 100%" @selection-change="handleSelectionChange">
            <el-table-column type="selection" width="50">
            </el-table-column>
            <el-table-column align="center" fixed label="序号" type="index" width="100"></el-table-column>
            <template v-for="(column, index) in columns">
              <el-table-column v-if="column.visible" :key="index" :label="column.label" :prop="column.field"
                :width="column.width" align="center" show-overflow-tooltip>
                <template slot-scope="scope">
                  <span>{{ scope.row[column.field] }}</span>
                </template>
              </el-table-column>
            </template>
            <el-table-column align="center" class-name="small-padding fixed-width" fixed="right" label="操作" width="200">
              <template slot-scope="scope">
                <el-button size="mini" type="text" @click="handleDelete(scope.row)"
                  v-has-menu-permi="['jgjc:earlyWarning:execute:delete']">删除</el-button>
              </template>
            </el-table-column>
          </el-table>
          <pagination v-show="total > 0" :limit.sync="queryParams.pageSize" :page.sync="queryParams.pageNum"
            :total="total" @pagination="handleQuery" />
        </div>
        <!-- 数据表格结束 -->
      </el-col>
    </el-row>
  </div>
</template>

<script>
import RoadSection from "@/views/baseData/components/roadSection/index.vue";
import SelectTree from "@/components/DeptTmpl/selectTree.vue";
import { deleteFlashGenericPlan, listFlashGenericPlan, statistics } from "@/api/jgjc/earlyWarning/deviceModel";

export default {
  name: "WarningRecord",
  components: { SelectTree, RoadSection },
  props: {
    pageType: {
      type: String,
      default: 'edit'
    },
    maiSecId: {
      type: String,
      default: ''
    },
    checkData: {
      type: Object,
      default: () => { }
    }
  },
  data() {
    return {
      loading: false,
      showSearch: false,
      total: 0,
      dataList: [],
      targetList: [
        { title: '边坡塌陷', num: 0, url: require('@/assets/earlyWarning/bptx.png') },
        { title: '桥梁塌陷', num: 0, url: require('@/assets/earlyWarning/qltx.png') },
        { title: '特重大交通事故', num: 0, url: require('@/assets/earlyWarning/tzdjtsg.png') },
        { title: '交通事故', num: 0, url: require('@/assets/earlyWarning/jtsg.png') },
        { title: '封路', num: 0, url: require('@/assets/earlyWarning/fl.png') },
        { title: '减速慢行', num: 0, url: require('@/assets/earlyWarning/jsmx.png') },
        { title: '主动诱导', num: 0, url: require('@/assets/earlyWarning/zdyd.png') },
        { title: '道路轮廓强化', num: 0, url: require('@/assets/earlyWarning/dllkqh.png') },
        { title: '正常', num: 0, url: require('@/assets/earlyWarning/zc.png') },
      ],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
      },
      // 列信息
      columns: [
        { key: 0, width: 150, field: 'structName', label: '结构物名称', visible: true },
        { key: 1, width: 150, field: 'structCode', label: '结构物编码', visible: true },
        { key: 10, width: 150, field: 'flashType', label: '厂商', visible: true },
        { key: 2, width: 150, field: 'planName', label: '预案模式', visible: true },
        { key: 3, width: 100, field: 'flashMode', label: '爆闪模式', visible: true },
        { key: 4, width: 100, field: 'screenWord', label: '显示', visible: true },
        { key: 5, width: 100, field: 'flicker', label: '闪烁', visible: true },
        { key: 6, width: 100, field: 'soundContent', label: '音频', visible: true },
        { key: 11, width: 100, field: 'soundVolume', label: '音频音量', visible: true },
        { key: 7, width: 100, field: 'planStatus', label: '预案状态', visible: true },
        { key: 8, width: 250, field: 'planExeTime', label: '预警开始时间', visible: true },
        { key: 9, width: 250, field: 'planOverTime', label: '预警结束时间', visible: true },
        { key: 12, width: 100, field: 'createUser', label: '操作人', visible: true },
      ],
      alertLevelList: [
        { label: '一级预警', value: '一级预警' },
        { label: '二级预警', value: '二级预警' },
        { label: '三级预警', value: '三级预警' },
      ],
    };
  },
  mounted() {
    this.queryParams.structureCode = this.checkData.code
    this.queryParams.structId = this.checkData.id
    this.handleQuery();
    this.getStatistics()
  },
  methods: {
    getStatistics() {
      const params = {
        structId: this.checkData.id
      }
      statistics(params).then(res => {
        if (res.code == 200) {
          res.data.forEach(item => {
            switch (item.flashMode) {
              case '8':
                this.targetList[0].num = item.modeCount
                break;
              case '7':
                this.targetList[1].num = item.modeCount
                break;
              case '6':
                this.targetList[2].num = item.modeCount
                break;
              case '5':
                this.targetList[3].num = item.modeCount
                break;
              case '4':
                this.targetList[4].num = item.modeCount
                break;
              case '3':
                this.targetList[5].num = item.modeCount
                break;
              case '2':
                this.targetList[6].num = item.modeCount
                break;
              case '1':
                this.targetList[7].num = item.modeCount
                break;
              case '0':
                this.targetList[8].num = item.modeCount
                break;
            }
          })
        }
      })
    },
    handleQuery() {
      this.loading = true
      listFlashGenericPlan(this.queryParams).then(res => {
        this.dataList = res.rows
        this.total = res.total
        this.loading = false
      })
    },
    resetQuery() {
      this.queryParams = {
        pageNum: 1,
        pageSize: 10,
      };
      this.queryParams.structureCode = this.checkData.code
      this.queryParams.structId = this.checkData.id
      this.handleQuery();
    },
    handleDelete(row) {
      this.$confirm('是否确认删除选中数据项?', '警告', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.loading = true
        const ids = [row.id]
        deleteFlashGenericPlan({ ids: ids }).then(res => {
          if (res.code == 200) {
            this.handleQuery()
            this.$modal.msgSuccess("删除成功");
          }
        })
      });
    },
    // 选中
    handleSelectionChange(e) {
      this.selectDatas = e
    },
  }
};
</script>

<style lang='scss' scoped>
@import "./index.scss";

.tableDiv {
  margin-top: 20px;
}

.target-list {
  display: flex;
  flex-wrap: nowrap;
  justify-content: space-between;
  margin-bottom: 10px;

  .targetBox {
    padding: 5px 10px;
    background-color: #034fc3;
    border-radius: 5px;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
    width: 160px;

    .title {
      font-size: 14px;
      color: #fff;
      margin-bottom: 10px;
    }

    .num {
      color: #fff;
      font-size: 28px;
      font-weight: bold;
      display: flex;
      justify-content: start;
      align-items: center;

      img {
        margin-right: 20px;
      }
    }
  }
}
</style>
