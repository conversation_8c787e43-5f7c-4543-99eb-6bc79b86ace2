<template>
  <el-form
    ref="form"
    :model="form"
    label-width="80px"
    :disabled="!localIsEditing"
  >
    <el-form-item label="类型名称">
      <el-input v-model="form.categoryName"></el-input>
    </el-form-item>
    <el-form-item label="文件类型">
      <el-select
        v-model="fileTypeArray"
        multiple
        filterable
        allow-create
        default-first-option
        placeholder="不填写则不限制"
      >
        <el-option
          v-for="item in options"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        >
        </el-option>
      </el-select>
    </el-form-item>
    <el-form-item label="排序">
      <el-input-number
        v-model="form.sort"
        controls-position="right"
        :min="1"
        :max="10"
      ></el-input-number>
    </el-form-item>
    <el-form-item>
      <el-button type="primary" @click="toggleEdit" :disabled="false">{{
        localIsEditing ? "保存" : "编辑"
      }}</el-button>
      <el-button :disabled="false" @click="toggleCancel">{{
        localIsEditing ? "取消" : "删除"
      }}</el-button>
    </el-form-item>
  </el-form>
</template>

<script>
import { mapState, mapActions, mapMutations } from "vuex";
import { saveModule, deleteFileSubModule } from "@/api/system/moduleFileSub.js";
export default {
  name: "formFile",
  data() {
    return {
      localIsEditing: false,
      options: [
        {
          value: "jpg",
          label: "jpg",
        },
        {
          value: "png",
          label: "png",
        },
        {
          value: "jpeg",
          label: "jpeg",
        },

        {
          value: "doc",
          label: "doc",
        },
        {
          value: "docx",
          label: "docx",
        },
        {
          value: "xls",
          label: "xls",
        },
        {
          value: "xlsx",
          label: "xlsx",
        },
        {
          value: "pdf",
          label: "pdf",
        },
        {
          value: "ppt",
          label: "ppt",
        },
      ],
    };
  },
  props: {
    form: {
      type: Object,
      default: () => ({}),
    },
    index: {
      type: Number,
      default: 0,
    },
  },
  inject: ["cardId", "initCard"],
  methods: {
    ...mapActions("file", {
      setEditingStatus: "setEditingStatus", // 映射 Vuex action
      fetchList: "fetchList", // 映射 Vuex action
    }),
    ...mapMutations("file", {
      changeList: "CHANGE_LIST", // 映射 Vuex mutation
    }),
    toggleEdit() {
      // 使用本地的 localIsEditing 状态来切换编辑状态
      this.setEditing(!this.localIsEditing);
      if (!this.localIsEditing) {
        this.saveForm();
      }
      // 其他逻辑...
    },
    saveForm() {
      // 调用save方法保存表单数据
      saveModule(this.form).then(() => {
        this.initCard();
        this.$message.success("保存成功");
      });
    },
    toggleCancel() {
      if (this.localIsEditing) {
        this.cancelForm();
        return;
      } else {
        this.deleteForm();
      }
    },
    changeForm() {
      if (this.localIsEditing) {
        this.cancelForm();
      }
    },
    setEditing(status) {
      this.localIsEditing = status;
      this.setEditingStatus({
        cardId: this.cardId,
        index: this.index,
        status: this.localIsEditing,
      });
    },
    cancelForm() {
      this.setEditing(false);
      this.changeList({ cardId: this.cardId });
    },
    deleteForm() {
      // 弹出确认框，用户点击确认后删除数据
      this.$confirm("此操作将永久删除该文件, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          // 调用删除接口
          console.log(this.form.id);
          deleteFileSubModule(this.form.id).then(() => {
            this.initCard();
            this.$message.success("删除成功");
          });
        })
        .catch(() => {
          this.$message.info("已取消删除");
        });
    },
  },
  watch: {
    // 监听 editingStatus 的变化
    editingStatus: {
      deep: true,
      handler(newStatus) {
        // 当编辑状态变化时，更新本地的 isEditing 状态
        this.localIsEditing = newStatus[this.index] || false;
      },
    },
  },
  mounted() {
    // 初始化时，将当前的编辑状态保存到 Vuex
    this.localIsEditing = this.editingStatus[this.index] || false;
  },
  computed: {
    // 用于将数组转换为字符串
    fileTypeArray: {
      get() {
        // 当读取 fileTypeArray 时，将 form.fileType 字符串转换为数组
        if (!this.form.fileType || this.form.fileType === "") {
          return [];
        }
        return this.form.fileType.split(",");
      },
      set(value) {
        // 当 fileTypeArray 发生变化时（用户选择或取消选择），将数组转换为字符串，并更新 form.fileType
        this.form.fileType = value
          .map((item) => {
            // 去除每个元素开头的点
            return item.startsWith(".") ? item.slice(1) : item;
          })
          .join(",");
      },
    },
    ...mapState("file", {
      editingStatus(state) {
        return (
          (state.cards[this.cardId] &&
            state.cards[this.cardId].editingStatus) ||
          []
        );
      },
    }),
  },
};
</script>