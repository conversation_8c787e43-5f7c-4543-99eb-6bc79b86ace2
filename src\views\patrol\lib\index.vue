<template>
  <div class="app-container">
    <el-row :gutter="20">

      <!--筛选区开始-->
      <el-col :span="24" :xs="24">
        <el-row>
          <el-col :span="24" >
            <el-form :model="queryParams" ref="queryForm" size="small" :inline="true"  label-width="68px">

                          <el-form-item label="" prop="libName">
                            <el-input
                                v-model="queryParams.libName"
                                placeholder="请输入名称"
                                clearable
                                prefix-icon="el-icon-user"
                                style="width: 240px"
                                @keyup.enter.native="handleQuery"
                            />
                          </el-form-item>
                          <el-form-item label="" prop="beginTime">
                            <el-date-picker clearable
                                            v-model="queryParams.beginTime"
                                            type="date"
                                            value-format="yyyy-MM-dd"
                                            placeholder="请选择起始时间">
                            </el-date-picker>
                          </el-form-item>
                          <el-form-item label="" prop="endTime">
                            <el-date-picker clearable
                                            v-model="queryParams.endTime"
                                            type="date"
                                            value-format="yyyy-MM-dd"
                                            placeholder="请选择终止时间">
                            </el-date-picker>
                          </el-form-item>
                          <el-form-item label="" prop="producerNo">
                            <el-input
                                v-model="queryParams.producerNo"
                                placeholder="请输入创建人编号"
                                clearable
                                prefix-icon="el-icon-user"
                                style="width: 240px"
                                @keyup.enter.native="handleQuery"
                            />
                          </el-form-item>
                          <el-form-item label="" prop="defaultLib">
                            <el-input
                                v-model="queryParams.defaultLib"
                                placeholder="请输入是否默认报价"
                                clearable
                                prefix-icon="el-icon-user"
                                style="width: 240px"
                                @keyup.enter.native="handleQuery"
                            />
                          </el-form-item>
                          <el-form-item label="" prop="isLock">
                            <el-input
                                v-model="queryParams.isLock"
                                placeholder="请输入是否锁定"
                                clearable
                                prefix-icon="el-icon-user"
                                style="width: 240px"
                                @keyup.enter.native="handleQuery"
                            />
                          </el-form-item>
                          <el-form-item label="" prop="domainId">
                            <el-input
                                v-model="queryParams.domainId"
                                placeholder="请输入权属单位id"
                                clearable
                                prefix-icon="el-icon-user"
                                style="width: 240px"
                                @keyup.enter.native="handleQuery"
                            />
                          </el-form-item>
              <el-form-item>
                <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
                <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
                <el-button v-show="!showSearch" @click="showSearch=true" icon="el-icon-arrow-down" circle></el-button>
                <el-button  v-show="showSearch" @click="showSearch=false"  icon="el-icon-arrow-up" circle></el-button>
              </el-form-item>
            </el-form>
            <!--默认折叠-->
          </el-col>

          <!--默认折叠 ,此处仅作为示例-->
          <el-col :span="24" >
            <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">

              <el-form-item label="" prop="status">
                <el-select
                    v-model="queryParams.status"
                    placeholder="示例状态"
                    clearable
                    style="width: 240px"
                >
                  <el-option
                      v-for="dict in dictType"
                      :key="dict.value"
                      :label="dict.label"
                      :value="dict.value"
                  />
                </el-select>
              </el-form-item>
              <el-form-item label="" prop="status">
                <el-select
                    v-model="queryParams.status"
                    placeholder="示例状态"
                    clearable
                    style="width: 240px"
                >
                  <el-option
                      v-for="dict in dictType"
                      :key="dict.value"
                      :label="dict.label"
                      :value="dict.value"
                  />
                </el-select>
              </el-form-item>

            </el-form>
          </el-col>
        </el-row>
        <!--筛选区结束-->


        <!--操作按钮区开始-->
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button
                type="primary"
                icon="el-icon-plus"
                size="mini"
                @click="handleAdd"
                v-hasPermi="['patrol:lib:add']"
            >新增</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button
                type="success"
                icon="el-icon-edit"
                size="mini"
                :disabled="single"
                @click="handleUpdate"
                v-hasPermi="['patrol:lib:edit']"
            >修改</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button
                type="danger"
                icon="el-icon-delete"
                size="mini"
                :disabled="multiple"
                @click="handleDelete"
                v-hasPermi="['patrol:lib:remove']"
            >删除</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button
                type="info"
                plain
                icon="el-icon-upload2"
                size="mini"
                @click="handleImport"
                v-hasPermi="['patrol:lib:export']"
            >导入</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button
                type="warning"
                icon="el-icon-download"
                size="mini"
                @click="handleExport"
                v-hasPermi="['system:user:export']"
            >导出</el-button>
          </el-col>
          <right-toolbar :showSearch.sync="showSearch" @queryTable="getList" :columns="columns"></right-toolbar>
        </el-row>
        <!--操作按钮区结束-->

        <!--数据表格开始-->
        <div class="tableDiv">
          <el-table  size="mini" :height="showSearch ? 'calc(100vh - 320px)' : 'calc(100vh - 260px)'"   style="width: 100%" v-loading="loading" border :data="libList" @selection-change="handleSelectionChange">
            <el-table-column type="selection" width="50" align="center" />
            <el-table-column fixed label="序号" type="index" width="50">
              <template v-slot="scope">
                {{ (scope.$index + (queryParams.pageNum - 1) * queryParams.pageSize)+1 }}
              </template>
            </el-table-column>
                    <el-table-column label="名称" align="center" prop="libName" />
                    <el-table-column label="描述" align="center" prop="libDesc" />
                    <el-table-column label="起始时间" align="center" prop="beginTime" width="180">
                      <template slot-scope="scope">
                        <span>{{ parseTime(scope.row.beginTime, '{y}-{m}-{d}') }}</span>
                      </template>
                    </el-table-column>
                    <el-table-column label="终止时间" align="center" prop="endTime" width="180">
                      <template slot-scope="scope">
                        <span>{{ parseTime(scope.row.endTime, '{y}-{m}-{d}') }}</span>
                      </template>
                    </el-table-column>
                    <el-table-column label="创建人编号" align="center" prop="producerNo" />
                    <el-table-column label="备注" align="center" prop="remark" />
                    <el-table-column label="类型" align="center" prop="schemeType" />
                    <el-table-column label="是否默认报价" align="center" prop="defaultLib" />
                    <el-table-column label="是否锁定" align="center" prop="isLock" />
                    <el-table-column label="权属单位id" align="center" prop="domainId" />
                    <el-table-column label="状态" align="center" prop="status" />
            <el-table-column
                label="操作"
                fixed="right"
                align="center"
                width="160"
                class-name="small-padding fixed-width"
            >
              <template slot-scope="scope" v-if="scope.row.userId !== 1">
                <el-button
                    size="mini"
                    type="text"
                    icon="el-icon-edit"
                    @click="handleUpdate(scope.row)"
                    v-hasPermi="['patrol:lib:edit']"
                >修改</el-button>
                <el-button
                    size="mini"
                    type="text"
                    icon="el-icon-delete"
                    @click="handleDelete(scope.row)"
                    v-hasPermi="['patrol:lib:remove']"
                >删除</el-button>
              </template>
            </el-table-column>
          </el-table>

          <pagination
              v-show="total>0"
              :total="total"
              :page.sync="queryParams.pageNum"
              :limit.sync="queryParams.pageSize"
              @pagination="getList"
          />
        </div>
        <!--数据表格结束-->
      </el-col>
    </el-row>

    <!-- 添加或修改报价体系基本信息对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="600px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">

                      <el-col :span="12">
                        <el-form-item label="名称" prop="libName">
                          <el-input v-model="form.libName" placeholder="请输入名称" />
                        </el-form-item>
                      </el-col>


                      <el-col :span="12">
                        <el-form-item label="描述" prop="libDesc">
                          <el-input v-model="form.libDesc" type="textarea" placeholder="请输入内容" />
                        </el-form-item>
                      </el-col>


                      <el-col :span="12">
                        <el-form-item label="起始时间" prop="beginTime">
                          <el-date-picker clearable
                                          v-model="form.beginTime"
                                          type="date"
                                          value-format="yyyy-MM-dd"
                                          placeholder="请选择起始时间">
                          </el-date-picker>
                        </el-form-item>
                      </el-col>


                      <el-col :span="12">
                        <el-form-item label="终止时间" prop="endTime">
                          <el-date-picker clearable
                                          v-model="form.endTime"
                                          type="date"
                                          value-format="yyyy-MM-dd"
                                          placeholder="请选择终止时间">
                          </el-date-picker>
                        </el-form-item>
                      </el-col>


                      <el-col :span="12">
                        <el-form-item label="创建人编号" prop="producerNo">
                          <el-input v-model="form.producerNo" placeholder="请输入创建人编号" />
                        </el-form-item>
                      </el-col>


                      <el-col :span="12">
                        <el-form-item label="备注" prop="remark">
                          <el-input v-model="form.remark" type="textarea" placeholder="请输入内容" />
                        </el-form-item>
                      </el-col>




                      <el-col :span="12">
                        <el-form-item label="是否默认报价" prop="defaultLib">
                          <el-input v-model="form.defaultLib" placeholder="请输入是否默认报价" />
                        </el-form-item>
                      </el-col>


                      <el-col :span="12">
                        <el-form-item label="是否锁定" prop="isLock">
                          <el-input v-model="form.isLock" placeholder="请输入是否锁定" />
                        </el-form-item>
                      </el-col>


                      <el-col :span="12">
                        <el-form-item label="权属单位id" prop="domainId">
                          <el-input v-model="form.domainId" placeholder="请输入权属单位id" />
                        </el-form-item>
                      </el-col>




                      <el-col :span="12">
                        <el-form-item label="删除标志" prop="delFlag">
                          <el-input v-model="form.delFlag" placeholder="请输入删除标志" />
                        </el-form-item>
                      </el-col>



      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 用户导入对话框 -->
    <el-dialog :title="upload.title" :visible.sync="upload.open" width="400px" append-to-body>
      <el-upload
          ref="upload"
          :limit="1"
          accept=".xlsx, .xls"
          :headers="upload.headers"
          :action="upload.url + '?updateSupport=' + upload.updateSupport"
          :disabled="upload.isUploading"
          :on-progress="handleFileUploadProgress"
          :on-success="handleFileSuccess"
          :auto-upload="false"
          drag
      >
        <i class="el-icon-upload"></i>
        <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
        <div class="el-upload__tip text-center" slot="tip">
          <div class="el-upload__tip" slot="tip">
            <el-checkbox v-model="upload.updateSupport" /> 是否更新已经存在的用户数据
          </div>
          <span>仅允许导入xls、xlsx格式文件。</span>
          <el-link type="primary" :underline="false" style="font-size:12px;vertical-align: baseline;" @click="importTemplate">下载模板</el-link>
        </div>
      </el-upload>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitFileForm">确 定</el-button>
        <el-button @click="upload.open = false">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
  import { listLib, getLib, delLib, addLib, updateLib } from "@/api/patrol/lib";
  import { getToken } from "@/utils/auth";
  import Treeselect from "@riophae/vue-treeselect";
  import "@riophae/vue-treeselect/dist/vue-treeselect.css";

  export default {
    name: "Lib",
    components: {  },
    data() {
      return {
        // 遮罩层
        loading: true,
        // 选中数组
        ids: [],
        // 非单个禁用
        single: true,
        // 非多个禁用
        multiple: true,
        // 显示搜索条件
        showSearch: false,
        dictType:[],
        // 总条数
        total: 0,
        // 报价体系基本信息表格数据
        libList: null,
        // 弹出层标题
        title: "",
        // 部门树选项
        deptOptions: undefined,
        // 是否显示弹出层
        open: false,

        // 表单参数
        form: {},
        defaultProps: {
          children: "children",
          label: "label"
        },
        // 用户导入参数
        upload: {
          // 是否显示弹出层（用户导入）
          open: false,
          // 弹出层标题（用户导入）
          title: "",
          // 是否禁用上传
          isUploading: false,
          // 是否更新已经存在的用户数据
          updateSupport: 0,
          // 设置上传的请求头部
          headers: { Authorization: "Bearer " + getToken() },
          // 上传的地址
          url: process.env.VUE_APP_BASE_API + "/system/user/importData"
        },
        // 查询参数
        queryParams: {
          pageNum: 1,
          pageSize: 50,
            libName: null,
            libDesc: null,
            beginTime: null,
            endTime: null,
            producerNo: null,
            schemeType: null,
            defaultLib: null,
            isLock: null,
            domainId: null,
            status: null,
        },
        // 列信息
        columns: [
        { key: 0, label: `名称`, visible: true },
        { key: 1, label: `描述`, visible: true },
        { key: 2, label: `起始时间`, visible: true },
        { key: 3, label: `终止时间`, visible: true },
        { key: 4, label: `创建人编号`, visible: true },
        { key: 5, label: `备注`, visible: true },
        { key: 6, label: `类型`, visible: true },
        { key: 7, label: `是否默认报价（1默认 0 非默认）`, visible: true },
        { key: 8, label: `是否锁定（1锁定 0 未锁定）`, visible: true },
        { key: 9, label: `权属单位id`, visible: true },
        { key: 10, label: `状态（0-正常，1-停用）`, visible: true },
        { key: 11, label: `删除标志（0代表存在，1代表删除）`, visible: true }
        ],
        // 表单校验
        rules: {


        }
      };
    },
    watch: {
      // 根据名称筛选部门树
                      },
    created() {
      this.getList();
      // this.getDeptTree();
      // this.getConfigKey("sys.user.initPassword").then(response => {
      //   this.initPassword = response.msg;
      // });
    },
    methods: {
      /** 查询用户列表 */
      getList() {
        this.loading = true;
        listLib(this.queryParams).then(response => {
          this.libList = response.rows;
          this.total = response.total;
          this.loading = false;
        });
      },
      // 取消按钮
      cancel() {
        this.open = false;
        this.reset();
      },
      // 表单重置
      reset() {
        this.form = {
            libName: null,
            libDesc: null,
            beginTime: null,
            endTime: null,
            producerNo: null,
            remark: null,
            schemeType: null,
            defaultLib: null,
            isLock: null,
            domainId: null,
            status: null,
            delFlag: null
        };
        this.resetForm("form");
      },
      /** 搜索按钮操作 */
      handleQuery() {
        this.queryParams.pageNum = 1;
        this.getList();
      },
      /** 重置按钮操作 */
      resetQuery() {
        this.dateRange = [];
        this.resetForm("queryForm");
        this.handleQuery();
      },
      // 多选框选中数据
      handleSelectionChange(selection) {
        this.ids = selection.map(item => item.id);
        this.single = selection.length != 1;
        this.multiple = !selection.length;
      },
      /** 新增按钮操作 */
      handleAdd() {
        this.reset();
        this.open = true;
        this.title = "添加报价体系基本信息";
      },
      /** 修改按钮操作 */
      handleUpdate(row) {
        this.reset();
        const id = row.id || this.ids;
        getLib(id).then(response => {
          this.form = response.data;
          this.open = true;
          this.title = "修改报价体系基本信息";
        });

      },
      /** 提交按钮 */
      submitForm: function() {
        this.$refs["form"].validate(valid => {
          if (valid) {
            if (this.form.id != null) {
              updateLib(this.form).then(response => {
                this.$modal.msgSuccess("修改成功");
                this.open = false;
                this.getList();
              });
            } else {
              addLib(this.form).then(response => {
                this.$modal.msgSuccess("新增成功");
                this.open = false;
                this.getList();
              });
            }
          }
        });
      },
      /** 删除按钮操作 */
      handleDelete(row) {
        const id = row.id || this.ids;
        this.$modal.confirm('是否确认删除报价体系基本信息编号为"' + id + '"的数据项？').then(function() {
          return delLib(id);
        }).then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        }).catch(() => {});
      },

      /** 导出按钮操作 */
      handleExport() {

    this.download('patrol/lib/export', {
      ...this.queryParams
    }, `lib_${new Date().getTime()}.xlsx`)

      },
      /** 导入按钮操作 */
      handleImport() {
        this.upload.title = "用户导入";
        this.upload.open = true;
      },
      /** 下载模板操作 */
      importTemplate() {
        this.download('system/user/importTemplate', {
        }, `user_template.xlsx`)
      },
      // 文件上传中处理
      handleFileUploadProgress(event, file, fileList) {
        this.upload.isUploading = true;
      },
      // 文件上传成功处理
      handleFileSuccess(response, file, fileList) {
        this.upload.open = false;
        this.upload.isUploading = false;
        this.$refs.upload.clearFiles();
        this.$alert("<div style='overflow: auto;overflow-x: hidden;max-height: 70vh;padding: 10px 20px 0;'>" + response.msg + "</div>", "导入结果", { dangerouslyUseHTMLString: true });
        this.getList();
      },
      // 提交上传文件
      submitFileForm() {
        this.$refs.upload.submit();
      }
  }
  };
</script>
<style>
  .hasTagsView .app-main[data-v-078753dd]{
    background: #f5f7fa;
  }

  .tableDiv{
    background-color: white;
    padding-bottom: 10px;
  }
</style>
