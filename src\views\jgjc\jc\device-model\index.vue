<template>
  <div class="app-container">
    <el-row :gutter="20">
      <!--筛选区开始-->
      <el-col :span="24" :xs="24">
        <el-row>
          <el-col :span="24" >
            <el-form :model="queryParams" ref="queryForm" size="small" :inline="true"  label-width="90px">
              <el-form-item label="厂家：" prop="manufacturerIdDisplay">
                <el-input
                  v-model="queryParams.manufacturerIdDisplay"
                  placeholder="请输入厂家"
                  clearable
                  :readonly="true"
                  prefix-icon="el-icon-edit"
                  style="width: 240px"
                  @keyup.enter.native="handleQuery"
                >
                  <el-button slot="append" icon="el-icon-search" @click="onSelectManufacturerId"></el-button>
                </el-input>
              </el-form-item>
              <el-form-item label="设备类型：" prop="deviceTypeId">
                <el-select
                  v-model="queryParams.deviceTypeId"
                  placeholder="请输入设备类型"
                  clearable
                >
                  <el-option
                    v-for="dict in deviceTypeList"
                    :key="dict.id"
                    :label="dict.name"
                    :value="dict.id"
                  />
                </el-select>
              </el-form-item>
              <el-form-item label="名称：" prop="name">
                <el-input
                  v-model="queryParams.name"
                  placeholder="请输入名称"
                  clearable
                  prefix-icon="el-icon-edit"
                  style="width: 240px"
                  maxlength="200" show-word-limit
                  @keyup.enter.native="handleQuery"
                />
              </el-form-item>
              <el-form-item>
                <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
                <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
              </el-form-item>
            </el-form>
            <!--默认折叠-->
          </el-col>
        </el-row>
        <!--筛选区结束-->
        <!--操作按钮区开始-->
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button
              type="primary"
              icon="el-icon-plus"
              size="mini"
              @click="handleAdd"
              v-hasPermi="['system:model:add']"
            >新增</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button
              type="success"
              icon="el-icon-edit"
              size="mini"
              :disabled="single"
              @click="handleUpdate"
              v-hasPermi="['system:model:edit']"
            >修改</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button
              type="danger"
              icon="el-icon-delete"
              size="mini"
              :disabled="multiple"
              @click="handleDelete"
              v-hasPermi="['system:model:remove']"
            >删除</el-button>
          </el-col>
          <right-toolbar :showSearch.sync="showSearch" @queryTable="getList" :columns="columns"></right-toolbar>
        </el-row>
        <!--操作按钮区结束-->

        <!--数据表格开始-->
        <div class="tableDiv">
          <el-table
            ref="table"
            size="mini"
            :height="showSearch ? 'calc(100vh - 320px)' : 'calc(100vh - 260px)'"
            style="width: 100%"
            v-loading="loading"
            border
            :data="modelList"
            @selection-change="handleSelectionChange"
            :row-style="rowStyle"
            @row-click="handleRowClick"
          >
            <el-table-column type="selection" width="50" align="center" />
            <el-table-column fixed type="index" align="right" width="50">
            </el-table-column>
            <el-table-column label="名称" align="left" header-align="center" prop="name" width="250" :show-overflow-tooltip="true" />
            <el-table-column label="厂家" align="left" header-align="center" prop="manufacturerIdDisplay" width="250" :show-overflow-tooltip="true"/>
            <el-table-column label="设备类型" align="left" header-align="center" prop="deviceTypeIdDisplay" width="100" :show-overflow-tooltip="true"/>
            <el-table-column label="说明" align="left" header-align="center" prop="description" :show-overflow-tooltip="true"/>
            <el-table-column label="序号" align="right" header-align="center" prop="orders" width="50"/>
            <el-table-column
              label="操作"
              fixed="right"
              align="center"
              width="160"
              class-name="small-padding fixed-width"
            >
              <template slot-scope="scope" v-if="scope.row.userId !== 1">
                <el-button
                  size="mini"
                  type="text"
                  icon="el-icon-edit"
                  @click="handleUpdate(scope.row)"
                  v-hasPermi="['system:model:edit']"
                >修改</el-button>
                <el-button
                  size="mini"
                  type="text"
                  icon="el-icon-delete"
                  @click="handleDelete(scope.row)"
                  v-hasPermi="['system:model:remove']"
                >删除</el-button>
              </template>
            </el-table-column>
          </el-table>

          <pagination
            v-show="total>0"
            :total="total"
            :page.sync="queryParams.pageNum"
            :limit.sync="queryParams.pageSize"
            @pagination="getList"
          />
        </div>
        <!--数据表格结束-->
      </el-col>
    </el-row>
    <!-- 添加或修改设备型号对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="600px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-col :span="24">
          <el-form-item label="厂家" prop="manufacturerIdDisplay">
            <el-input
              v-model="form.manufacturerIdDisplay"
              placeholder="请输入厂家"
              :readonly="true"
            >
              <el-button slot="append" icon="el-icon-search" @click="onSelectManufacturerIdEdit"></el-button>
            </el-input>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="设备类型" prop="deviceTypeId">
            <el-select
              v-model="form.deviceTypeId"
              placeholder="请输入设备类型"
              style="width: 100%;"
            >
              <el-option
                v-for="dict in deviceTypeList"
                :key="dict.id"
                :label="dict.name"
                :value="dict.id"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="名称" prop="name">
            <el-input v-model="form.name" placeholder="请输入名称" maxlength="200" show-word-limit/>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="序号" prop="orders">
            <el-input-number v-model="form.orders" placeholder="请输入序号" style="width:100%;" controls-position="right" :min="1" :step="1" :max="1000000"></el-input-number>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="说明" prop="description">
            <el-input v-model="form.description" type="textarea" placeholder="请输入内容" :rows="5" maxlength="10000" show-word-limit/>
          </el-form-item>
        </el-col>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
    <SelectManufacturer ref="selectManufacturerRef" @select="selectManufacturer"></SelectManufacturer>
    <SelectManufacturer ref="selectManufacturerEditRef" @select="selectManufacturerEdit"></SelectManufacturer>
  </div>
</template>

<script>
import { listDeviceModel, getDeviceModel, delDeviceModel, addDeviceModel, updateDeviceModel,delDeviceModelBatch } from "@/api/jgjc/jc/deviceModel";
import { findAllDeviceType } from "@/api/jgjc/jc/deviceType";
import { getToken } from "@/utils/auth";
import Treeselect from "@riophae/vue-treeselect";
import "@riophae/vue-treeselect/dist/vue-treeselect.css";
//import SelectTunnel from "@/views/system/dept/selectTunnel";
import SelectManufacturer from '@/views/jgjc/jc/manufacturer/selectManufacturer.vue'
export default {
  name: "JcDeviceModel",
  components: { SelectManufacturer },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      delText:[],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: false,
      dictType:[],
      // 总条数
      total: 0,
      // 设备型号表格数据
      modelList: null,
      // 设备类型列表
      deviceTypeList:[],
      // 弹出层标题
      title: "",
      // 部门树选项
      deptOptions: undefined,
      // 是否显示弹出层
      open: false,

      // 表单参数
      form: {},
      defaultProps: {
        children: "children",
        label: "label"
      },
      // 用户导入参数
      upload: {
        // 是否显示弹出层（用户导入）
        open: false,
        // 弹出层标题（用户导入）
        title: "",
        // 是否禁用上传
        isUploading: false,
        // 是否更新已经存在的用户数据
        updateSupport: 0,
        // 设置上传的请求头部
        headers: { Authorization: "Bearer " + getToken() },
        // 上传的地址
        url: process.env.VUE_APP_BASE_API + "/system/user/importData"
      },
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 50,
        manufacturerId: null,
        manufacturerIdDisplay: null,
        deviceTypeId: null,
        deviceTypeIdDisplay:null,
        name: null,
        orderDirection: 'ASC'
      },
      // 列信息
      columns: [
        { key: 0, label: `名称`, visible: true },
        { key: 1, label: `厂家`, visible: true },
        { key: 2, label: `设备类型`, visible: true },
        { key: 3, label: `说明`, visible: true },
        { key: 4, label: `序号`, visible: true }
      ],
      // 表单校验
      rules: {
        manufacturerIdDisplay: [
          { required: true, message: "厂家不能为空", trigger: "blur" }
        ],
        deviceTypeId: [
          { required: true, message: "设备类型不能为空", trigger: "blur" }
        ],
        name: [
          { required: true, message: "名称不能为空", trigger: "blur" }
        ],
        orders: [
          { required: true, message: "序号不能为空", trigger: "blur" }
        ],
        description: [
          { required: true, message: "说明不能为空", trigger: "blur" }
        ]
      }
    };
  },
  watch: {
    // 根据名称筛选部门树
  },
  created() {
    this.getList();
    this.getDeviceTypeList();
    // this.getDeptTree();
    // this.getConfigKey("sys.user.initPassword").then(response => {
    //   this.initPassword = response.msg;
    // });
  },
  methods: {
    getDeviceTypeList() {
      this.loading = true
      findAllDeviceType().then(response => {
        this.deviceTypeList = response.data;
        this.loading = false;
      });
    },
    /** 查询用户列表 */
    getList() {
      this.loading = true;
      listDeviceModel(this.queryParams).then(response => {
        this.modelList = response.data;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        manufacturerId: null,
        manufacturerIdDisplay:null,
        deviceTypeId: null,
        deviceTypeIdDisplay:null,
        name: null,
        orders: null,
        description: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dateRange = [];
      this.resetForm("queryForm");
      this.queryParams.manufacturerId=null
      this.queryParams.manufacturerIdDisplay=null
      this.queryParams.deviceTypeId=null
      this.queryParams.deviceTypeIdDisplay=null
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id);
      this.delText = selection.map(item => item.name);
      this.single = selection.length != 1;
      this.multiple = !selection.length;
    },
    // 表格点击勾选
    handleRowClick(row) {
      row.isSelected = !row.isSelected;
      this.$refs.table.toggleRowSelection(row);
    },
    // 勾选高亮
    rowStyle({ row, rowIndex }) {
      if (this.ids.includes(row.id)) {
        return { 'background-color': '#E1F0FF', color: '#333' }
      } else {
        return { 'background-color': '#fff', color: '#333' }
      }
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加设备型号";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids;
      getDeviceModel(id).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改设备型号";
      });

    },
    /** 提交按钮 */
    submitForm: function() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.id != null) {
            updateDeviceModel(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addDeviceModel(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      if(row.id) {
        this.$modal.confirm('是否确认删除设备型号为"' + row.name + '"的数据项？').then(function() {
          return delDeviceModel(row.id);
        }).then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        }).catch(() => {
        });
      }else{
        var ids = this.ids
        this.$modal.confirm('是否确认删除设备型号为"' + this.delText + '"的数据项？').then(function() {
          return delDeviceModelBatch(ids);
        }).then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        }).catch(() => {
        });
      }
    },
    onSelectManufacturerId(){
      this.$refs.selectManufacturerRef.show()
    },
    selectManufacturer(res){
      this.queryParams.manufacturerId = res.id
      this.queryParams.manufacturerIdDisplay=res.name
    },
    onSelectManufacturerIdEdit() {
      this.$refs.selectManufacturerEditRef.show()
    },
    selectManufacturerEdit(res) {
      this.form.manufacturerId = res.id
      this.form.manufacturerIdDisplay = res.name
    }
  }
};
</script>
<style>
.hasTagsView .app-main[data-v-078753dd]{
  background: #f5f7fa;
}

.tableDiv{
  background-color: white;
  padding-bottom: 10px;
}
</style>
