<template>
  <div class="app-container">
    <el-row :gutter="20">

      <el-col :span="24">
        <!--操作按钮区开始-->
        <el-row :gutter="10" class="mb8" v-if="grade > 2">
          <el-col :span="1.5">
            <el-button
                type="primary"
                icon="el-icon-plus"
                size="mini"
                @click="handleAdd"
                v-hasPermi="['patrol:laborperson:add']"
            >新增</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button
                type="danger"
                icon="el-icon-delete"
                size="mini"
                :disabled="multiple"
                @click="handleDelete"
                v-hasPermi="['patrol:laborperson:remove']"
            >删除</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button
                type="info"
                plain
                icon="el-icon-upload2"
                size="mini"
                @click="handleImport"
                v-hasPermi="['patrol:laborperson:export']"
            >导入</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button
                type="warning"
                icon="el-icon-download"
                size="mini"
                @click="handleExport"
                v-hasPermi="['patrol:laborperson:export']"
            >导出</el-button>
          </el-col>
          <right-toolbar v-if="false" :showSearch.sync="showSearch" @queryTable="getList" :columns="columns"></right-toolbar>
        </el-row>
        <!--操作按钮区结束-->

        <!--数据表格开始-->
        <div class="tableDiv">
          <el-table v-adjust-table size="mini" :height="showSearch ? 'calc(100vh - 320px)' : 'calc(100vh - 260px)'"
            style="width: 100%" v-loading="loading" border :data="laborpersonList" @selection-change="handleSelectionChange">
            <el-table-column type="selection" width="50" align="center" />
            <el-table-column fixed label="序号" type="index" width="50">
              <template v-slot="scope">
                {{ (scope.$index + (queryParams.pageNum - 1) * queryParams.pageSize)+1 }}
              </template>
            </el-table-column>
            <el-table-column label="姓名" align="center" prop="name" />
            <el-table-column label="电话" align="center" prop="phone" />
            <el-table-column label="身份证" align="center" prop="idCard" />
            <el-table-column label="人员类型" align="center" prop="personType" :formatter="getPersonTypeName"/>
            <el-table-column label="性别" align="center" prop="sex" :formatter="getSexName"/>
            <el-table-column label="职位" align="center" prop="job" />
            <el-table-column label="所属项目部" align="center" prop="projectName" :show-overflow-tooltip="true"/>
            <el-table-column label="所属养护所" align="center" prop="maintainName" :show-overflow-tooltip="true"/>
            <el-table-column
                label="操作"
                fixed="right"
                align="center"
                width="160"
                class-name="small-padding fixed-width"
                v-if="grade > 2"
            >
              <template slot-scope="scope" v-if="scope.row.userId !== 1">
                <el-button
                    size="mini"
                    type="text"
                    icon="el-icon-edit"
                    @click="handleUpdate(scope.row)"
                    v-hasPermi="['patrol:laborperson:edit']"
                >修改</el-button>
                <el-button
                    size="mini"
                    type="text"
                    icon="el-icon-delete"
                    @click="handleDelete(scope.row, true)"
                    v-hasPermi="['patrol:laborperson:remove']"
                >删除</el-button>
              </template>
            </el-table-column>
          </el-table>

          <pagination
              v-show="total>0"
              :total="total"
              :page.sync="queryParams.pageNum"
              :limit.sync="queryParams.pageSize"
              @pagination="getList"
          />
        </div>
        <!--数据表格结束-->
      </el-col>
    </el-row>

    <!-- 新增编辑弹窗 -->
    <el-dialog :title="title" :visible.sync="open" width="1100px" append-to-body :close-on-click-modal="false">
        <el-tabs type="border-card" v-model="activeTab" class="tabscss" @tab-click="tabClick">
            <el-tab-pane label="基本信息" :name="tabNames[0]" class="tabpanecss">
                <el-form ref="form" :model="form" label-width="120px" :rules="rules" :inline="true">
                    <el-col :span="12">
                        <el-form-item label="姓名" prop="name" :required="true">
                            <el-input v-model="form.name" placeholder="请输入" />
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                         <el-form-item label="电话" prop="phone" :required="true">
                            <el-input v-model="form.phone" placeholder="请输入" />
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="身份证" prop="idCard">
                            <el-input v-model="form.idCard" placeholder="请输入" />
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="人员类型" prop="personType" :required="true">
                            <el-select v-model="form.personType" placeholder="请选择" style="width: 198px">
                                <el-option v-for="dict in dict.type.labor_person_type"
                                    :key="dict.value" :label="dict.label" :value="dict.value" v-if="showPersonType(dict.value)"
                                />
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="性别" prop="sex" :required="true">
                            <el-select v-model="form.sex" placeholder="请选择" style="width: 198px">
                                <el-option v-for="dict in dict.type.sys_user_sex"
                                    :key="dict.value" :label="dict.label" :value="dict.value"
                                />
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="职位" prop="job" :required="true">
                            <el-input v-model="form.job" placeholder="请输入" />
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="所属养护所" prop="maintainId" :required="true">
                            <!-- <deptTree :deptType="103" /> -->
                            <el-select v-model="form.maintainId" placeholder="请选择">
                                <el-option v-for="item in maintainList" :key="item.id" :label="item.label" :value="item.id"/>
                            </el-select>
                        </el-form-item>
                    </el-col>
                </el-form>
                <div class="form-footer">
                    <el-button type="primary" @click="submitForm">确 定</el-button>
                    <el-button @click="cancel">取 消</el-button>
                </div>
            </el-tab-pane>
            <el-tab-pane label="参保信息" :name="tabNames[1]" class="tabpanecss">
                <!-- 表格 -->
                <el-table v-loading="insurLoading" :data="insuranceTableData" highlight-current-row style="width: 100%" :height="500"
                    >
                    <el-table-column label="序号" align="center" width="50" type="index" >
                      <template v-slot="scope">
                        {{ (scope.$index + (queryParams.pageNum - 1) * queryParams.pageSize)+1 }}
                      </template>
                    </el-table-column>
                    <el-table-column label="姓名" align="center" width="120" prop="userName" />
                    <el-table-column label="所属劳务单位" align="center" prop="domainName" />
                    <el-table-column label="保险类型" align="center" width="120" prop="insureType" />
                    <el-table-column label="保险起始时间" align="center" width="160" prop="insureStartDate" />
                    <el-table-column label="保险结束时间" align="center" width="160" prop="insureEndDate" />
                    <el-table-column label="保险单号" align="center"  prop="insureCode" />
                </el-table>
            </el-tab-pane>
            <el-tab-pane label="持证信息" :name="tabNames[2]" class="tabpanecss">
                <CertificateInfo ref="cerInfo" :domainName="domainName" :domainId="domainId"
                    :userName="form.name" :userId="form.id"/>
            </el-tab-pane>
        </el-tabs>

    </el-dialog>

    <!-- 用户导入对话框 -->
    <el-dialog :title="upload.title" :visible.sync="upload.open" width="400px" append-to-body>
      <el-upload
          ref="upload"
          :limit="1"
          accept=".xlsx, .xls"
          :headers="upload.headers"
          :action="upload.url + '?updateSupport=' + upload.updateSupport"
          :disabled="upload.isUploading"
          :on-progress="handleFileUploadProgress"
          :on-success="handleFileSuccess"
          :auto-upload="false"
          drag
      >
        <i class="el-icon-upload"></i>
        <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
        <div class="el-upload__tip text-center" slot="tip">
          <div class="el-upload__tip" slot="tip">
            <el-checkbox v-model="upload.updateSupport" /> 是否更新已经存在的用户数据
          </div>
          <span>仅允许导入xls、xlsx格式文件。</span>
          <el-link type="primary" :underline="false" style="font-size:12px;vertical-align: baseline;" @click="importTemplate">下载模板</el-link>
        </div>
      </el-upload>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitFileForm">确 定</el-button>
        <el-button @click="upload.open = false">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
  import { getInsureAllList } from "@/api/patrol/laborpersoninsure";
  import { listbyParam, getLaborperson, delLaborperson, batchDelLaborperson, addLaborperson, updateLaborperson } from "@/api/patrol/laborperson";
  import { getTreeStruct } from "@/api/tmpl";
  import { getToken } from "@/utils/auth";
  import { findFiles } from '@/api/file/index.js'
  import Treeselect from "@riophae/vue-treeselect";
  import CertificateInfo from "@/views/patrol/laborpersoncertificate/certificateInfo.vue"
//   import deptTree from '@/components/DeptTmpl/deptTree.vue'
  import "@riophae/vue-treeselect/dist/vue-treeselect.css";

  export default {
    name: "LaborTabData",
    dicts: ['sys_user_sex', 'labor_person_type'],
    components: { Treeselect, CertificateInfo },
    props:{
        personType: {
            type: Number,
            default: 1
        },
        domainId:{
            type:String,
            default:"-1"
        },
        grade:{
            type:Number,
            default:-1
        },
        labDomainId:{
            type:String,
            default:0
        },
        domainIdStr:{
            type:String,
            default:''
        },
        domainName: {
            type: String,
            default:''
        }
    },
    data() {
      return {
        // 遮罩层
        loading: false,
        //参保信息遮罩层
        insurLoading: false,
        // 选中数组
        ids: [],
        // 非单个禁用
        single: true,
        // 非多个禁用
        multiple: true,
        // 显示搜索条件
        showSearch: false,
        //是否正在查询
        isQuerying: false,
        dictType:[],
        // 总条数
        total: 0,
        // 劳务人员参保情况表格数据
        laborpersoninsureList: null,
        laborpersonList: null,
        // 弹出层标题
        title: "",
        // 左侧组织树
        deptNav: true,
        // 部门树选项
        deptOptions: undefined,
        // 部门名称
        deptName: undefined,
        //当前点击的树节点
        currentOptionData:undefined,
        // 是否显示弹出层
        open: false,
        //附件的唯一标识
        attFileOwnerId: 0,
        //劳务人员列表
        laborPeopleList: [],
        // 表单参数
        form: {},
        defaultProps: {
          children: "children",
          label: "label"
        },
        //养护所集合
        maintainList:[],
        //获取参保信息列表
        insuranceTableData:[],
        tabNames:['base','insurance','certificate'],
        activeTab: "base",
        showFooter:true,
        // 用户导入参数
        upload: {
          // 是否显示弹出层（用户导入）
          open: false,
          // 弹出层标题（用户导入）
          title: "",
          // 是否禁用上传
          isUploading: false,
          // 是否更新已经存在的用户数据
          updateSupport: false,
          // 设置上传的请求头部
          headers: { Authorization: "Bearer " + getToken() },
          // 上传的地址
          url: process.env.VUE_APP_BASE_API + "/manager/laborperson/import"
        },
        // 查询参数
        queryParams: {
          pageNum: 1,
          pageSize: 50,
          personType:"3,4",
          domainIdStr: ""
        },
        // 列信息
        columns: [
        { key: 0, label: `姓名`, visible: true },
        { key: 1, label: `类型(0:个人保险，1：团体保险)`, visible: true },
        { key: 2, label: `所属单位id`, visible: true },
        { key: 3, label: `保险类型`, visible: true },
        { key: 4, label: `保险起始时间`, visible: true },
        { key: 5, label: `保险结束时间`, visible: true },
        { key: 6, label: `保险单号`, visible: true },
        { key: 7, label: `附件`, visible: true }
        ],
        // 表单校验
        rules: {
          userId: [
              { required: true, message: "用户id，关联劳务人员表ID字段不能为空", trigger: "blur" }
          ]
        }
      };
    },
    watch: {
        personType: {
            immediate: true,
            handler(val) {
                this.queryParams.personType = val === 1 ? "3,4" : "1,2";
            }
        },
        domainId:{
            immediate: true,
            handler(val){
                // this.queryParams.domainId = val;
                // console.log("wath-domainId2222",val, "fdsfsd,", this.$props);
            }
        },
        domainIdStr: {
            immediate: true,
            handler(val){
                this.queryParams.domainIdStr = val;
                if (val == null || val == "")
                    this.queryParams.labDomainIds = [];
                else
                    this.queryParams.labDomainIds = val.split(',');

                // console.log("wath-domainIdStr",val, "fdsfsd,", this.$props);
            }
        }
    },
    created() {
        this.handleQuery();
    },
    computed: {

    },
    methods: {
      /** 查询用户列表 */
      getList() {
        this.loading = true;
        listbyParam(this.queryParams).then(response => {
          this.laborpersonList = response.rows;
          this.total = response.total;
          this.loading = false;
        });
      },
      getInsureList(){
        this.insurLoading = true;
        let insureQuery = {
            labisureType: 0,
            userId: this.form.id,
            domainIds: this.queryParams.labDomainIds
        }

        getInsureAllList(insureQuery).then(response => {
            this.insuranceTableData = response.data;
            this.insurLoading = false;
        });
      },
      tabClick(tab, val){

        if(this.activeTab == this.tabNames[1] && this.title != "新增") {
            //参保信息
            this.getInsureList();
        }
      },
      //获取性别名称
      getSexName(row, column, cellValue) {
        if (this.dict.type.sys_user_sex) {
          let filterVals = this.dict.type.sys_user_sex.filter(item => Number.parseInt(item.value) == cellValue);
          if (filterVals && filterVals.length > 0) {
            return filterVals[0].label;
          }
        }
        return cellValue;
      },
      //获取人员类型名称
      getPersonTypeName(row, column, cellValue) {
        if (this.dict.type.labor_person_type) {
          let filterVals = this.dict.type.labor_person_type.filter(item => Number.parseInt(item.value) == cellValue);
          if (filterVals && filterVals.length > 0) {
            return filterVals[0].label;
          }
        }
        return cellValue;
      },
      //显示人员类型
      showPersonType(val) {
        if (val == 1 || val == 2){
            return this.$props.personType === 2;
        } else if (val == 3 || val == 4) {
            return this.$props.personType === 1;
        }
        return false;
      },
      // 取消按钮
      cancel() {
        this.open = false;
        this.reset();
      },
      // 表单重置
      reset() {
        this.form = {
            userId: null,
            domainId: null,
            insureType: null,
            insureStartDate: null,
            insureEndDate: null,
            insureCode: null,
            attFile: null,
            remark: null,
            delFlag: null,
            status: null
        };
        this.resetForm("form");
        this.insuranceTableData = [];
        this.activeTab = "base";
      },
      /** 搜索按钮操作 */
      handleQuery() {
        this.queryParams.pageNum = 1;
        this.getList();
      },
      /** 重置按钮操作 */
      resetQuery() {
        this.dateRange = [];
        this.resetForm("queryForm");
        this.handleQuery();
      },
      // 多选框选中数据
      handleSelectionChange(selection) {
        this.ids = selection.map(item => item.id);
        this.single = selection.length != 1;
        this.multiple = !selection.length;
      },
      //获取附件的唯一id
      getAttfileOwnerId(){
        this.attFileOwnerId = new Date().getTime();
      },
      //获取养护所数据
      async getTreeStruct(){
        return await getTreeStruct({types:103}).then(response => {
            this.maintainList = response.data;
        });
      },
      /** 新增按钮操作 */
      handleAdd() {
        this.reset();
        this.form.labDomainId = this.$props.labDomainId;
        this.open = true;
        this.title = "新增";
        this.getTreeStruct();
      },
      /** 修改按钮操作 */
      async handleUpdate(row) {
        console.log("handleUpdate1")
        await this.getTreeStruct();
        this.reset();
        const id = row.id || this.ids;
        this.$nextTick(() => {
            getLaborperson(id).then(response => {
                this.form = response.data;
                //需要转化下form.maintainId，树的id是字符串，
                this.form.maintainId = this.form.maintainId.toString();
                this.open = true;
                this.title = "修改";
            });
        })

      },
      /** 提交按钮 */
      submitForm: function() {
        this.$refs["form"].validate(valid => {
          if (valid) {
            if (this.form.id != null) {
                updateLaborperson(this.form).then(response => {
                    this.$modal.msgSuccess("修改成功");
                    this.open = false;
                    this.handleQuery();
              });
            } else {
                //所属管养单位
                this.form.labDomainId = this.$props.labDomainId
                //所属项目部
                this.form.projectId = this.$props.domainId
                addLaborperson(this.form).then(response => {
                    this.$modal.msgSuccess("新增成功");
                    this.open = false;
                    this.handleQuery();
              });
            }
          }
        });
      },
      /** 删除按钮操作 */
      handleDelete(row, isSingle) {
        let selectIds = []
        if (isSingle){
            selectIds.push(row.id);
        }else {
            selectIds = this.ids;
        }

        this.$modal.confirm('是否确认删除劳务人员参保情况编号为"' + selectIds.join(",") + '"的数据项？').then(function() {
          return batchDelLaborperson(selectIds);
        }).then(() => {
          this.handleQuery();
          this.$modal.msgSuccess("删除成功");
        }).catch(() => {});
      },

      /** 导出按钮操作 */
      handleExport() {
        let exportParam = this.queryParams;
        exportParam.labDomainIdStr = this.queryParams.labDomainIds.join(",");
        this.download('manager/laborperson/export', exportParam, `laborperson_${new Date().getTime()}.xlsx`)
      },
      /** 导入按钮操作 */
      handleImport() {
        this.upload.title = "用户导入";
        this.upload.open = true;
      },
      /** 导出操作 */
      importTemplate() {
        this.download('manager/laborperson/importTemplate', {
        }, `劳务人员管理模板.xlsx`)
      },
      // 文件上传中处理
      handleFileUploadProgress(event, file, fileList) {
        this.upload.isUploading = true;
      },
      // 文件上传成功处理
      handleFileSuccess(response, file, fileList) {
        this.upload.open = false;
        this.upload.isUploading = false;
        this.$refs.upload.clearFiles();
        this.$alert("<div style='overflow: auto;overflow-x: hidden;max-height: 70vh;padding: 10px 20px 0;'>" + response.msg + "</div>", "导入结果", { dangerouslyUseHTMLString: true });
        this.getList();
      },
      // 提交上传文件
      submitFileForm() {
        this.$refs.upload.submit();
      },
      //下载附件
      downloadAttFile(ownerId){
        findFiles({ ownerId }).then(res => {
          let file = res.data[0];
          const url = file.url || (file.response && file.response.url)
          if (url) {
            fetch(url)
              .then((response) => response.blob())
              .then((blob) => {
                const link = document.createElement('a')
                link.href = URL.createObjectURL(blob)
                link.download = file.originalFilename || 'download'
                document.body.appendChild(link)
                link.click()
                document.body.removeChild(link)
              })
              .catch(() => {
                this.$message.error('文件无法下载，未找到文件的URL')
              })
          } else {
            this.$message.error('文件无法下载，未找到文件的URL')
          }
        });
      },
      //附件上传完成
      onAttUploadCompelte(val){
        //返回的是一个数组
        //附件存储的是一个数字的字符串，下载需要调findFiles接口获取信息
        this.form.attFile = val[0];
      },
  }
  };
</script>
<style scoped>
  .hasTagsView .app-main[data-v-078753dd]{
    background: #f5f7fa;
  }

  .tableDiv{
    background-color: white;
    padding-bottom: 10px;
  }

  .custom-tree-node{
      display: flex;
      align-items: center;
      width: 100%;
      justify-content: space-between;
      font-size:14px;
  }

  .leftDiv{
    border-right: 1px solid #d8dce5;
    min-height: calc(100vh - 110px);
    overflow-y: auto;
    height: calc(80vh - 110px);
    position: relative;
    top: -20px;
    padding-top: 10px;
    background-color: white;
  }

  .leftIcon{
    border: 1px solid #DCDFE6;
    border-radius: 8px;
    width: 16px;
    height: 50px;
    line-height: 50px;
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
    position: absolute;
    right: 0;
    top: 300px;
    z-index: 2;
  }
  .leftIcon:hover{
    background-color: #DCDFE6;
  }

  .rightIcon{
    border: 1px solid #DCDFE6;
    border-radius: 8px;
    width: 16px;
    height: 50px;
    line-height: 50px;
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
    position: absolute;
    left: -10px;
    top: 280px;
    z-index: 10;
    background: white;
  }
  .rightIcon:hover{
    background-color: #DCDFE6;
  }

  .form-footer {
    position: absolute;
    right: 20px;
    bottom: 20px;
  }

  .txt_height{
    height:30px
  }

    .tabscss {
        height:600px;
    }
  .tabpanecss {
    height: 530px;
  }

</style>
