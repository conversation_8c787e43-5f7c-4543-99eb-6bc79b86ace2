<template>
  <div class="container-view-list">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="120px">
      <el-form-item label="年份" prop="year">
        <el-date-picker
          v-model="queryParams.year"
          type="year"
          placeholder="选择年"
          style="width: 215px;"
          @keyup.enter.native="handleQuery"
        >
        </el-date-picker>
      </el-form-item>
      <el-form-item label="管理处">
        <SelectTreeCheckbox></SelectTreeCheckbox>
      </el-form-item>
      <el-form-item label="施工单位">
        <SelectTreeCheckbox></SelectTreeCheckbox>
      </el-form-item>
      <el-form-item label="养护路段">
        <SelectTreeCheckbox></SelectTreeCheckbox>
      </el-form-item>
      <el-form-item label="项目名称">
        <el-input
          v-model="queryParams.startDate"
          placeholder="请输入项目名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="项目编码">
        <el-input
          v-model="queryParams.startDate"
          placeholder="请输入项目编码"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="工程分类">
        <el-select v-model="queryParams.startDate" placeholder="请选择工程分类">
          <el-option label="全部" :value="0" />
          <el-option label="预防性养护" :value="1" />
          <el-option label="修复性养护" :value="2" />
          <el-option label="专项性养护" :value="3" />
          <el-option label="应急性养护" :value="4" />
        </el-select>
      </el-form-item>
      <el-form-item label="所属工程分类">
        <el-select v-model="queryParams.startDate" placeholder="请选择所属工程分类">
          <el-option label="全部" :value="0" />
          <el-option label="桥梁工程" :value="1" />
          <el-option label="路基工程" :value="2" />
          <el-option label="路面工程" :value="3" />
          <el-option label="隧道工程" :value="4" />
          <el-option label="机电工程" :value="5" />
          <el-option label="交安工程" :value="6 " />
          <el-option label="绿化工程" :value="7 " />
          <el-option label="房建工程" :value="8 " />
        </el-select>
      </el-form-item>
      <el-form-item label="任务单编码">
        <el-input
          v-model="queryParams.startDate"
          placeholder="请输入项目编码"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="项目状态">
        <el-select v-model="queryParams.startDate" placeholder="请选择项目状态">
          <el-option label="全部" :value="0" />
          <el-option label="未开始" :value="1" />
          <el-option label="进行中" :value="2" />
          <el-option label="已完成" :value="3" />
        </el-select>
      </el-form-item>
      <el-form-item label="操作人">
        <el-input
          v-model="queryParams.startDate"
          placeholder="请输入操作人"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label=" ">
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['manager:bridge:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table height="100%" v-loading="loading" :data="bridgeList">
      <el-table-column label="序号" fixed="left" type="index" width="55" align="center" />
      <el-table-column label="状态" width="100" align="center" prop="状态" />
      <el-table-column label="管理处" width="100" align="center" prop="管理处" />
      <el-table-column label="年度" width="80" align="center" prop="年度" />
      <el-table-column label="项目名称" width="180" show-overflow-tooltip align="center" prop="项目名称" />
      <el-table-column label="项目编码" width="180" align="center" prop="项目编码" />
      <el-table-column label="养护路段" width="150" align="center" prop="养护路段" />
      <el-table-column label="计划资金" width="150" align="center" prop="计划资金" />
      <el-table-column label="施工任务单" width="120" align="center" prop="施工任务单" />
      <el-table-column label="设计任务单" width="120" align="center" prop="设计任务单" />
      <el-table-column label="检测任务单" width="120" align="center" prop="检测任务单" />
      <el-table-column label="桩号范围" width="200" show-overflow-tooltip align="center" prop="桩号范围" />
      <el-table-column label="工程分类" width="120" align="center" prop="工程分类" />
      <el-table-column label="所属工程类别" width="140" align="center" prop="所属工程类别" />
      <el-table-column label="构造物名称" width="200" show-overflow-tooltip align="center" prop="构造物名称" />
      <el-table-column label="缺陷责任期" width="120" align="center" prop="缺陷责任期" />
      <el-table-column label="预计工期" width="80" align="center" prop="预计工期" />
      <el-table-column label="预计开始时间" width="120" align="center" prop="预计开始时间" />
      <el-table-column label="预计结束时间" width="120" align="center" prop="预计结束时间" />
      <el-table-column label="操作人" align="center" prop="操作人" />
      <el-table-column label="上报人" align="center" prop="上报人" />
      <el-table-column label="备注" min-width="200" align="center" prop="备注" />
      <el-table-column label="操作" align="center" class-name="small-padding fixed-right" fixed="right">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-search"
            @click="onDetail(scope.row)"
            v-hasPermi="['manager:bridge:query']"
          >查看</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      :pageSizes="[10, 20, 30, 50, 100, 1000]"
      @pagination="getList"
    />

    <Dialog
      title="查看项目信息"
      width="1200px"
      :show.sync="showDetail"
    >
      <Detail></Detail>
    </Dialog>
  </div>
</template>

<script>
import { listBridgeMaintenanceRecords, getBridge, delBridge, addBridge, updateBridge } from "@/api/baseData/bridge/maintenanceRecord";
import SelectTreeCheckbox from '@/components/DeptTmpl/selectTreeCheckbox'
import SelectTree from '@/components/DeptTmpl/selectTree'
import Dialog from '@/components/Dialog/index.vue'
import Detail from './detail/index.vue'
export default {
  name: "Maintenance",
  components: {
    SelectTreeCheckbox,
    SelectTree,
    Dialog,
    Detail
  },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 桥梁养护处治记录表格数据
      bridgeList: [],
      // 是否显示弹出层
      showDetail: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        bridgeCode: null,
        startDate: null,
        endDate: null,
        buildType: null,
        buildReason: null,
        scopeOfWork: null,
        cost: null,
        sourceOfIncome: null,
        qualityAssessment: null,
        buildUnit: null,
        designUnit: null,
        constructionUnit: null,
        constructionControlUnit: null,
        birId: null,
        bridgeId: null,
        valid: null,
        packetId: null
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询桥梁养护处治记录列表 */
    getList() {
      this.loading = true;
      listBridgeMaintenanceRecords(this.queryParams).then(response => {
        this.bridgeList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 查看
    onDetail(row) {
      this.showDetail = true
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('manager/bridge/export', {
        ...this.queryParams
      }, `bridge_${new Date().getTime()}.xlsx`)
    }
  }
};
</script>

<style lang="scss" scoped>
.container-view-list {
  v-deep .el-table {
    flex: 1;
  }
}
</style>
