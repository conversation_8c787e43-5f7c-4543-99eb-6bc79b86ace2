<template>
	<div>
		<div class="tunnel-table-container">
			<el-form
				ref="queryForm"
				:model="queryParams"
				size="small"
				:inline="true"
				class="form-container"
			>
				<div class="search-container">
					<div class="search-row justify-end">
						<el-form-item prop="assetName">
							<el-input
								v-model="queryParams.assetName"
								placeholder="请输入隧道名称"
								clearable
								style="width: 240px"
								@input="debouncedSearch"
								@clear="debouncedSearch"
								@keyup.enter.native="handleQuery"
							/>
						</el-form-item>
						<el-form-item prop="assetCode">
							<el-input
								v-model="queryParams.assetCode"
								placeholder="请输入隧道编码"
								clearable
								style="width: 240px"
								@input="debouncedSearch"
								@clear="debouncedSearch"
								@keyup.enter.native="handleQuery"
							/>
						</el-form-item>
						<el-form-item prop="checkTimeHas">
							<el-date-picker
								v-model="queryParams.checkTimeHas"
								:clearable="false"
								:type="['1', '3', '5'].includes(type) ? 'date' : 'month'"
								format="yyyy-MM-dd"
								value-format="yyyy-MM-dd HH:mm:ss"
								placeholder="请选择检查时间"
								style="width: 240px"
								@change="handleDateChange"
							></el-date-picker>
						</el-form-item>
						<el-form-item prop="isCheck">
							<el-select
								v-model="queryParams.isCheck"
								placeholder="是否已检查"
								clearable
								style="width: 240px"
								@change="debouncedSearch"
								@clear="debouncedSearch"
							>
								<el-option label="已检查" value="true"></el-option>
								<el-option label="未检查" value="false"></el-option>
							</el-select>
						</el-form-item>
					</div>

					<div class="search-row justify-end">
						<CascadeSelection
							style="min-width: 192px"
							:form-data="queryParams"
							v-model="queryParams"
							types="201"
							multiple
							@update:fromData="handleCascadeChange"
						/>
						<el-form-item style="width: 200px; margin-left: 10px">
							<div style="display: flex; align-items: center">
								<el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">
									搜索
								</el-button>
								<el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
								<el-checkbox
									v-if="generate"
									v-model="disableTableClick"
									style="margin-left: 10px; white-space: nowrap"
									@change="handleQuery"
								>
									选择上述条件的全部
								</el-checkbox>
							</div>
						</el-form-item>
					</div>
				</div>

				<!--      隐藏的查询条件-->
			</el-form>
			<!-- 表格-->
			<div class="table-wrapper" :class="{ 'table-disabled': disableTableClick }">
				<el-table
					ref="dataTable"
					size="mini"
					v-loading="loading"
					element-loading-text="正在获取数据..."
					element-loading-spinner="el-icon-loading"
					element-loading-background="rgba(255, 255, 255, 0.8)"
					border
					:data="tableDataList"
					row-key="id"
					@row-click="
						(row) => {
							if (!disableTableClick) {
								single && $refs.dataTable.clearSelection()
								$refs.dataTable.toggleRowSelection(row)
							}
						}
					"
					style="min-height: 300px; margin-top: 10px"
					@selection-change="handleSelectionChange"
					@select="handleSelect"
					@select-all="handleSelectAll"
				>
					<el-table-column type="selection" width="50" align="center" />
					<el-table-column fixed label="序号" type="index" width="50" align="center">
						<template v-slot="scope">
							{{ scope.$index + (queryParams.pageNum - 1) * queryParams.pageSize + 1 }}
						</template>
					</el-table-column>
					<el-table-column
						fixed
						label="隧道名称"
						align="center"
						prop="baseData.assetName"
						min-width="140"
						show-overflow-tooltip
					/>
					<el-table-column
						fixed
						label="隧道代码"
						align="center"
						prop="baseData.assetCode"
						min-width="140"
						show-overflow-tooltip
					/>
					<el-table-column
						label="养护路段"
						align="center"
						prop="baseData.maintenanceSectionName"
						min-width="140"
					></el-table-column>
					<el-table-column
						label="路线编号"
						align="center"
						prop="baseData.routeCode"
						min-width="140"
						show-overflow-tooltip
					/>
					<el-table-column
						label="管养单位"
						align="center"
						prop="baseData.managementMaintenanceName"
						min-width="140"
					/>
					<el-table-column
						label="管养分处"
						align="center"
						prop="baseData.managementMaintenanceBranchName"
						min-width="140"
					></el-table-column>
					<el-table-column
						label="统一里程桩号"
						align="center"
						prop="baseData.unifiedMileageStake"
						min-width="140"
						show-overflow-tooltip
					>
						<template slot-scope="scope">
							{{ formatPile(scope.row.baseData.unifiedMileageStake) }}
						</template>
					</el-table-column>
					<el-table-column label="是否已检查" align="center" prop="isCheck">
						<template slot-scope="scope">
							{{ scope.row.isCheck === true ? '已检查' : '未检查' }}
						</template>
					</el-table-column>
				</el-table>
				<pagination
					:total="total"
					:page.sync="queryParams.pageNum"
					:limit.sync="queryParams.pageSize"
					@pagination="getList"
				/>
			</div>
		</div>
	</div>
</template>
<script>
import TreeSelect from '@riophae/vue-treeselect'
import '@riophae/vue-treeselect/dist/vue-treeselect.css'

import { listStatic } from '@/api/baseData/tunnel/baseInfo'
import { listByDistance } from '@/api/patrol/assetCheck'
import { deptTreeSelect } from '@/api/system/user'
import { listMaintenanceSectionAll } from '@/api/system/maintenanceSection'
import { listAllRoute } from '@/api/system/route'
import AssetCheckInsertOrUpdate from '@/views/patrol/assetCheck/insertOrUpdate.vue'
import CascadeSelection from '@/components/CascadeSelectionManagementOffice/index.vue'
import moment from 'moment'

export default {
	name: 'TunnelTableDialog',
	components: { AssetCheckInsertOrUpdate, TreeSelect, CascadeSelection },
	dicts: [
		'tunnel_maintenance_grade',
		'sys_route_direction',
		'tunnel_length_classification',
		'tunnel_assess_grade',
		'sys_yes_no',
		'sys_operation_state',
		'tunnel_lining_type',
	],
	props: {
		/** * 选中的数据 */
		value: {
			type: Array,
			default: () => [],
		},
		/** * 是否单选； */
		single: {
			type: Boolean,
			default: false,
		},
		/** * 类型： tunnel ， 5， 6 * */
		// 三者区别在于检查时间，日常:天 ; 经常：月
		type: {
			type: String,
			default: 'tunnel',
		},
		generate: {
			type: Boolean,
			default: false,
		},
	},
	watch: {
		value(val) {
			if (!val) {
				this.$refs.dataTable.clearSelection()
			}
		},
	},
	data() {
		return {
			deptOptions: [],
			maintenanceSectionList: null,
			routeList: null,
			tableDataList: [],
			total: 0,
			loading: false,
			queryParams: {
				pageNum: 1,
				pageSize: 50,
				manageMaintainUnitId: null,
				maintenanceSection: null,
				bridgeCode: null,
				bridgeName: null,
				routeCode: null,
				mainSuperstructureType: null,
				checkTimeHas: moment().format('YYYY-MM-DD HH:mm:ss'), // 使用 moment 格式化当前时间
				isCheck: 'false',
				type: this.type,
			},
			showSearch: false,
			disableTableClick: false,
			searchTimer: null,
		}
	},

	methods: {
		// 多选框选中数据
		handleSelectionChange(selection) {
			// console.log(selection)
			// this.ids = selection.map(item => item.id)
			this.$emit('input', selection)
		},
		handleClearSelection() {
			this.$refs.dataTable.clearSelection()
		},
		handleSelect(selection, row) {
			if (this.single) {
				this.$refs.dataTable.clearSelection()
				this.$nextTick(() => {
					if (selection.includes(row)) this.$refs.dataTable.toggleRowSelection(row)
				})
			}
		},
		handleSelectAll(selection) {
			if (this.single) {
				let temp = this.value[0]
				this.$refs.dataTable.clearSelection()
				this.$refs.dataTable.toggleRowSelection(temp)
			}
		},
		/** 重置按钮操作 */
		resetQuery() {
			this.resetForm('queryForm')
			this.handleQuery()
		},
		/** 获取表格数据*/
		getList() {
			this.loading = true
			// 处理managementMaintenanceBranchId，确保不传递数组给后端
			const params = { ...this.queryParams }

			// 如果managementMaintenanceBranchId是数组，取第一个值或者设置为null
			if (Array.isArray(params.managementMaintenanceBranchId)) {
				params.managementMaintenanceBranchId =
					params.managementMaintenanceBranchId.length > 0
						? params.managementMaintenanceBranchId[0]
						: null
			}

			// 确保其他可能的数组字段也被正确处理
			if (
				params.managementMaintenanceIds &&
				Array.isArray(params.managementMaintenanceIds) &&
				params.managementMaintenanceIds.length === 0
			) {
				params.managementMaintenanceIds = null
			}

			listByDistance(params)
				.then((response) => {
					this.tableDataList = response.rows
					this.total = response.total
				})
				.catch(() => {
					this.tableDataList = []
					this.total = 0
				})
				.finally(() => {
					this.loading = false
				})
		},
		/** 查询部门下拉树结构 */
		getDeptTree() {
			return deptTreeSelect().then((response) => {
				this.deptOptions = response.data
			})
		},
		/** 查询养护路段下拉列表 */
		getMaintenanceSection() {
			listMaintenanceSectionAll().then((res) => {
				this.maintenanceSectionList = res.data
			})
		},
		/** 查询路线列表 */
		getRouteList() {
			listAllRoute().then((res) => {
				this.routeList = res.data
			})
		},
		// 获取当前查询参数的方法
		getCurrentQueryParams() {
			const ids =
				this.$refs.dataTable?.selection
					.filter((row) => row?.baseData?.assetId)
					.map((row) => row.baseData.assetId) || []

			const finalIds = this.disableTableClick ? null : ids
			return {
				assetCode: this.queryParams.assetCode || null,
				assetName: this.queryParams.assetName || null,
				checkTimeHas: this.queryParams.checkTimeHas || null,
				isCheck: this.queryParams.isCheck || null,
				maintenanceSectionId: this.queryParams.maintenanceSectionId || null,
				managementMaintenanceIds: this.queryParams.managementMaintenanceIds || null,
				routeCodes: this.queryParams.routeCodes || null,
				type: this.type,
				ids: finalIds,
				total: this.disableTableClick ? this.total : finalIds ? finalIds.length : null,
			}
		},
		// 处理 CascadeSelection 的变化
		handleCascadeChange(val) {
			// 更新查询参数
			Object.assign(this.queryParams, val)
			// 触发防抖搜索
			this.debouncedSearch()
		},
		handleDateChange(date) {
			// 调用原有的防抖搜索
			this.debouncedSearch()
			// 如果日期存在，则格式化日期，只保留年月日
			const formattedDate = date ? date.split(' ')[0] : date
			// 触发日期变更事件
			this.$emit('check-time-change', formattedDate)
		},

		// 添加防抖搜索方法
		debouncedSearch() {
			if (this.searchTimer) {
				clearTimeout(this.searchTimer)
			}
			this.searchTimer = setTimeout(() => {
				this.handleQuery()
			}, 300)
		},

		// 原有的立即搜索方法
		handleQuery() {
			// 如果有定时器先清除
			if (this.searchTimer) {
				clearTimeout(this.searchTimer)
			}
			this.queryParams.pageNum = 1
			this.getList()
		},
	},
	created() {
		this.getList()
		this.getDeptTree()
		this.getRouteList()
		this.getMaintenanceSection()
	},
}
</script>
<style lang="scss" scoped>
.tunnel-table-container {
	display: flex;
	flex-direction: column;
	align-items: flex-end;
	height: 100%;

	.el-form-item {
		width: 15%;
		margin: 10px 10px 0 0;
	}

	.el-form-item__content,
	.el-autocomplete,
	.el-date-editor,
	.el-select {
		width: 100%;
	}

	.vue-treeselect__control {
		height: auto;
	}

	.form-container {
		width: 100%;
	}

	.search-container {
		width: 100%;
		display: flex;
		flex-direction: column;
		gap: 10px;

		.search-row {
			display: flex;
			flex-direction: row;
			gap: 10px;
			align-items: center;
			justify-content: flex-start;
			padding-right: 0;
			width: 100%;

			&.justify-end {
				justify-content: flex-start;
				padding-right: 0;
			}
		}

		.el-form-item {
			margin: 0;
			width: auto;
		}
	}
}

// .el-dialog__body {
//   padding: 10px 30px 0;
//   height: 65vh;
//   overflow-y: auto;
//   scrollbar-width: none;
// }

.table-wrapper {
	position: relative;
	width: 100%;
	display: flex;
	flex-direction: column;
	gap: 10px;

	.el-pagination {
		margin-top: 10px;
	}

	&.table-disabled {
		&::after {
			content: '';
			position: absolute;
			top: 0;
			left: 0;
			right: 0;
			bottom: 0;
			background: rgba(255, 255, 255, 0.4);
			pointer-events: none;
			z-index: 1;
		}

		.el-table {
			opacity: 0.7;
			cursor: not-allowed;
		}

		.el-table th,
		.el-table td {
			cursor: not-allowed;
		}
	}
}
</style>
