import Vue from "vue";

export default {
  inserted(el) {
    initAdjust(el)
  },
  update(el) {
    Vue.nextTick(() => triggerAdjust(el))
  },
  unbind(el) {
    destroyListener(el)
  }
}

// 初始化监听
function initAdjust(el) {
  // 1. 创建观察者
  el.__headerObserver__ = new ResizeObserver(() => {
    triggerAdjust(el)
  })
  el.__headerObserver__.observe(el.querySelector('.el-table__header-wrapper'))

  // 2. 创建MutationObserver
  el.__mutationObserver__ = new MutationObserver(() => {
    triggerAdjust(el)
  })
  el.__mutationObserver__.observe(
    el.querySelector('.el-table__header-wrapper'),
    { attributes: true, childList: true, subtree: true }
  )

  // 3. 绑定组件激活事件
  el.__activatedHandler__ = () => triggerAdjust(el)
  el.closest('keep-alive')?.__vue__.$on('activated', el.__activatedHandler__)
}

// 触发调整逻辑
function triggerAdjust(el) {
  Vue.nextTick(() => {
    // 核心调整逻辑
    const headerHeight = el.querySelector('.el-table__header-wrapper')?.offsetHeight
    const fixedBodies = el.querySelectorAll('.el-table__fixed-body-wrapper')
    fixedBodies.forEach(body => {
      body.style.setProperty('top', `${headerHeight}px`, 'important')
    })
    // 强制刷新表格布局
    el.__vue__?.$refs?.table?.doLayout?.()
  })
}

// 销毁监听
function destroyListener(el) {
  el.__headerObserver__?.disconnect()
  el.__mutationObserver__?.disconnect()
  el.closest('keep-alive')?.__vue__.$off('activated', el.__activatedHandler__)
}