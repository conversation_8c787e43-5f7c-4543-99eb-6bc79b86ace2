<template>
  <div class="home-container">
    <div class="home-header">
      <cockpit-header v-on="$listeners"></cockpit-header>
    </div>
    <div class="home-main" :style="mainStyle">
      <div class="page-l">
        <page-l @click="onLClick"></page-l>
      </div>
      <!-- <div class="page-r">
        <page-r></page-r>
      </div> -->
    </div>
  </div>
</template>

<script>
// 组件
import { isBigScreen } from '../util/utils';
import CockpitHeader from './cockpitHeader.vue';
import PageR from './pageR.vue';
import PageL from './pageL.vue';

export default {
  name: 'Home',
  components: {
    CockpitHeader,
    PageL,
    PageR,
  },
  data() {
    return {
      isBig: isBigScreen(),
      height:
        window.innerHeight ||
        window.screen.height ||
        window.screen.availHeight ||
        1080,
    };
  },
  computed: {
    mainStyle() {
      let style = {};
      if (this.isBig) {
        style = {
          // height: this.height - 150 + 'px',
          height: 'calc(100vh - 150px)',
          overflow: 'hidden',
        };
      } else {
        style = {
          // height: this.height * 2 - 135 + 'px',
          height: 'calc(200vh - 135px)',
          overflowY: 'auto',
          flexDirection: 'column',
        };
      }
      return style;
    },
  },
  methods: {
    onLClick(obj) {
      this.$emit('clickL', obj);
    },
  },
  unmounted() {},
};
</script>

<style lang="scss" scoped>
@import '@/assets/styles/utils.scss';

.home-container {
  width: 100%;
  height: 100%;
  overflow-y: auto;

  .home-header {
    width: 100%;
    height: vwpx(156px);
    z-index: 9;
  }

  .home-main {
    width: 100%;
    // display: flex;
    overflow: hidden;
    background-color: rgba(9, 25, 45, 1);

    .page-l {
      //   flex: 1;
      background-image: url('~@/assets/cockpit/cockpit-bg.png');
      background-repeat: no-repeat;
      background-size: cover;
      padding: 0 vwpx(20px);
    }
  }
}
</style>
