<template>
  <div class="app-container">

    <!-- 查询 -->
    <div class="searchBox" style="height: 48px;">
      <el-row :gutter="12">
        <el-col :span="4">
          <el-input v-model="queryParams.title" clearable placeholder="请输入流程标题"></el-input>
        </el-col>
        <el-col :span="4">
          <el-select v-model="queryParams.category" placeholder="请选择流程分类" style="width: 100%;" clearable>
            <el-option v-for="dict in dict.type.process_type" :key="dict.value" :label="dict.label" :value="dict.label" />
          </el-select>
        </el-col>
        <el-col :span="4">
          <div style="width: 100%; display: flex; flex-direction: row; flex-wrap: nowrap;">
            <el-select v-model="queryParams.applyUserName" clearable placeholder="请选择创建人" style="width: 80%">
              <el-option
                v-for="item in userSelectData"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              >
              </el-option>
            </el-select>
            <el-button icon="el-icon-search" type="primary" @click="userDialogOpen" style="width: 20%"></el-button>
          </div>
        </el-col>
        <el-col :span="8">
          <el-date-picker
            v-model="queryTime"
            @change="() => {queryParams.startTime = queryTime[0] + ' 00:00:00'; queryParams.endTime = queryTime[1] + ' 23:59:59'}"
            type="daterange"
            style="width: 100%;"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            format="yyyy-MM-dd"
            value-format="yyyy-MM-dd"
          >
          </el-date-picker>
        </el-col>
        <el-col :span="4" style="display: flex; align-items: center">

          <el-button type="primary" icon="el-icon-search" size="mini" @click="queryList">搜索</el-button>
          <el-button icon="el-icon-refresh" size="mini" @click="queryReset">重置</el-button>

        </el-col>
      </el-row>
    </div>

    <!-- 主表数据 -->
    <div class="tableDiv" style="height: calc(100% - 58px)">
      <!-- 功能按钮 -->
      <!-- <div class="btnBox">
        <el-row :gutter="10">
          <el-col :span="1.5">
            <el-button type="primary" icon="el-icon-plus" size="mini" @click="formInit('add')">新增
            </el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="success" icon="el-icon-edit" size="mini" :disabled="tableSelection.length !== 1"
              @click="formEdit('select')">修改
            </el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="danger" icon="el-icon-delete" size="mini" :disabled="tableSelection.length === 0"
              @click="formDelete('batch')">删除
            </el-button>
          </el-col>
        </el-row>
      </div> -->

      <!-- 数据表格 -->
      <el-table size="mini" height="calc(100% - 98px)" border ref="tableRef" style="border-radius: 10px;"
        v-loading="tableLoading" :data="tableData" @selection-change="tableSelectionChange" @row-click="tableRowClick">
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="序号" align="center" width="60px" type="index"></el-table-column>
        <el-table-column label="流程标题" :show-overflow-tooltip="true" align="center" prop="title" />
        <el-table-column label="流程类型" :show-overflow-tooltip="true" align="center">
          <template slot-scope="scope">
            <dict-tag :options="dict.type.process_type" :value="scope.row.category"/>
          </template>
        </el-table-column>
        <el-table-column label="流程实例ID" :show-overflow-tooltip="true" align="center" prop="processInstanceId" />
        <el-table-column label="创建日期" :show-overflow-tooltip="true" align="center" prop="createTime" />
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width" fixed="right">
          <template slot-scope="scope">
            <!-- <el-button size="mini" type="text" icon="el-icon-finished" @click="formInit('examine', scope.row)">审批
            </el-button> -->
            <el-button size="mini" type="text" icon="el-icon-view" @click="formInit('check', scope.row)">查看
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <pagination :total="queryTotal" :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize" @pagination="queryList" style="margin-right: 10px;" />

    </div>

    <!-- 表单 -->
    <el-dialog :visible.sync="formDialog" width="70%" max-height="50%" :close-on-press-escape="false" :close-on-click-modal="false" append-to-body class="formDialog">
      <template slot="title">
        <div class="titleBox">
          <div class="title">{{ formType === 'examine' ? '审批任务' : '查看任务' }}</div>
          <div class="subTitle" v-if="formParams.id">ID：</div>
        </div>
      </template>
    </el-dialog>

    <el-dialog :visible.sync="userDialog" title="创建人选择" width="40%" :close-on-press-escape="false" :close-on-click-modal="false" append-to-body class="userDialogBox">
      <div class="userBoxMain">
        <div class="searchBox">
          <el-row>
            <el-col :span="20">
              <el-input v-model="userParams.nickName" placeholder="请输入用户昵称" clearable @keyup.enter.native="userList"></el-input>
            </el-col>
            <el-col :span="4" style="display: flex; justify-content: flex-end;">
              <el-button type="primary" icon="el-icon-search" size="mini" @click="userList">搜索</el-button>
            </el-col>
          </el-row>
        </div>
        <div class="dataBox">
          <el-table size="mini" height="calc(100% - 50px)" border ref="tableRef" v-loading="userTableLoading" :data="userTableData">
            <el-table-column label="用户名称" fixed :show-overflow-tooltip="true" align="center" prop="userName" />
            <el-table-column label="用户昵称" fixed :show-overflow-tooltip="true" align="center" prop="nickName" />
            <el-table-column label="用户编号" fixed :show-overflow-tooltip="true" align="center" prop="userId" />
            <el-table-column label="操作" align="center" class-name="small-padding fixed-width" fixed="right">
              <template slot-scope="scope">
                <el-button size="mini" type="text" @click="userSelect(scope.row)">选择
                </el-button>
              </template>
            </el-table-column>
          </el-table>

          <pagination :total="userTableTotal" :page.sync="userParams.pageNum" :pager-count="2"
            :limit.sync="userParams.pageSize" @pagination="userList" style="margin-right: 10px;" />
        </div>
        <!-- <div class="selectBox">
          <el-tag
            v-if="userSelectData.userId"
            :key="userSelectData.userId"
            @close="userSelctCancel"
            closable
          >
            {{userSelectData.nickName}}
          </el-tag>
        </div> -->
      </div>
      <!-- <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="userSelectDown">保 存</el-button>
      </div> -->
    </el-dialog>

  </div>
</template>

<script>
// -------------------- 引入 --------------------

// API
import { queryPageCompletedTask } from '@/api/process/task/task' // 流程任务模块
import { listUser } from "@/api/system/user"; // 用户管理模块

export default {
  name: "Await",
  // 数据字典
  dicts: [
    'process_type'
  ],
  // 组件
  components: {},

  // -------------------- 变量 --------------------
  data() {
    return {

      /**
       * 查询相关
       */
      queryShow: false, // 查询更多参数显隐
      queryParams: { // 查询参数
        title: '', // 流程标题
        category: '', // 流程类型
        applyUserName: '', // 创建人
        startTime: '', // 开始时间
        endTime: '', // 结束时间
        pageNum: 1, // 页码
        pageSize: 10, // 每页条数
      },
      queryTime: '', // 时间范围
      queryTotal: 0, // 查询总数

      /**
       * 表格相关
       */
      tableData: [], // 表格数据
      tableSelection: [], // 表格选中数据
      tableLoading: false, // 表格加载

      /**
       * 表单相关
       */
      formDialog: false, // 表单弹窗
      formParams: { // 表单参数
        id: '', // 流程ID
      },
      formType: '', // 表单类型
      formProcessType: [], // 表单字典数据

      /**
       * 用户相关
       */
      userDialog: false, // 用户dialog
      userParams: { // 用户查询参数
        pageNum: 1, // 页码
        pageSize: 10, // 每页条数
        nickName: '', // 用户昵称
      },
      userTableLoading: false, // 用户数据加载
      userTableData: [], // 用户数据
      userTableTotal: 0, // 用户数据总数
      userSelectData: [], // 用户选中数据
    }
  },
  mounted() {
    this.initPage()
    // 增加标签页监听
    document.addEventListener('visibilitychange', this.pageVisibilityChange);
  },
  beforeDestroy () {
    // 移除标签页监听
    document.removeEventListener('visibilitychange', this.pageVisibilityChange);
  },
  // -------------------- 方法 --------------------
  methods: {

    /**
     * 页面相关
     */
    // 初始化页面
    initPage() {
      // 查询待办数据
      this.queryList()
      // 查询字典类型数据
      this.queryType()
    },

    // 当进入标签页时
    pageVisibilityChange() {
      if (document.visibilityState === 'visible') {
        this.queryList()
      }
    },

    /**
     * 查询相关
     */
    // 查询数据
    queryList() {
      this.tableLoading = true
      queryPageCompletedTask(this.queryParams).then((res) => {
        console.log('已办数据：', res)
        if(res.code === 200) {
          this.tableData = res.data.records
          this.queryTotal = res.data.total
        }
        this.tableLoading = false
      }).catch(() => {
        this.tableLoading = false
      })
    },

    // 查询重置
    queryReset() {
      this.queryParams = { // 查询参数
        title: '', // 流程标题
        category: '', // 流程类型
        applyUserName: '', // 创建人
        startTime: '', // 开始时间
        endTime: '', // 结束时间
        pageNum: 1, // 页码
        pageSize: 10, // 每页条数
      }
      this.queryList()
    },

    // 查询流程应对路由表单
    queryType() {
      this.getDicts("process_type").then((res) => {
        console.log('流程类型：', res)
        if(res.code === 200) {
          this.formProcessType = res.data
        }
      })
    },

    /**
     * 表格相关
     */
    // 勾选表格项改变时
    tableSelectionChange(val) {
      this.tableSelection = val
    },

    // 点击一行时
    tableRowClick(val) {
      this.$refs.tableRef.toggleRowSelection(val)
    },

    /**
     * 表单相关
     */
    // 初始化表单
    async formInit(type, data) {
      console.log('获取的参数：', data)
      this.formType = type
      switch (type) {
        case 'examine':

          break;

        case 'check':
          let routePath = await this.formCheckTyoe(data.processDefinitionKey)
          if(!routePath) {
            this.$message.warning('未查询到流程表单信息')
            return
          }
          const routeUrl = this.$router.resolve({
            name: routePath,
            query: {
              taskId: data.taskId,
              businessKey: data.businessKey,
              processInstanceId: data.processInstanceId,
              taskKey: data.taskKey,
              route: routePath,
              name: data.category,
              title: data.title,
              isApprove: '1'
            },
          })
          window.open(routeUrl.href, '_blank')
          break;
      }
    },

    // 确认流程类型
    formCheckTyoe(type) {
      let check = this.formProcessType.findIndex(item => item.dictValue === type)
      if(check !== -1) {
        return this.formProcessType[check].remark
      } else {
        return null
      }
    },

    /**
     * 用户相关
     */

    // 打开用户查询框
    userDialogOpen() {
      this.userList()
      this.userDialog = true
    },

    // 查询用户信息
    userList() {
      this.userTableLoading = true
      listUser(this.userParams).then((res) => {
        console.log('获取的用户信息：', res)
        if(res.code === 200) {
          this.userTableData = res.rows
          this.userTableTotal = res.total
        }
        this.userTableLoading = false
      }).catch(() => {
        this.userTableLoading = false
      })
    },

    // 确认选择用户
    userSelect(item) {
      this.userSelectData = []
      this.userSelectData.push ({ value: item.userId, label: item.nickName })
      this.queryParams.applyUserName = item.userId
      this.userDialog = false
    },

    // 取消选择用户
    userSelctCancel() {
      this.userSelectData = []
      this.queryParams.applyUserName = ''
    }
  },
}

</script>

<style lang="scss" scoped>
.app-container form:first-child .el-select,
.app-container form:nth-child(2) .el-select,
.app-container form:nth-child(2) ::v-deep .el-form-item__content,
.app-container form:first-child ::v-deep .el-form-item__content {
  width: 240px;
}

.app-container form:first-child .el-form-item:last-child ::v-deep .el-form-item__content {
  width: auto;
}

.app-container {
  padding: 10px;
  background-color: #c0c0c0;
  box-sizing: border-box;
}

.formDialog {
  ::v-deep .el-dialog__body {
    height: 600px;
    overflow-y: auto;
  }

  .titleBox {
    height: 22px;
    position: relative;
    display: flex;
    flex-direction: row;
    align-items: center;

    .title {
      font-size: 16px;
      color: black;
      margin: 0;
    }

    .subTitle {
      margin-left: 15px;
      font-size: 12px;
      color: #888888;
    }

    .riskLevel {
      user-select: none;
      position: absolute;
      // top: 0;
      right: 5%;
      display: flex;
      align-items: center;
      flex-direction: row;

      .title {
        font-size: 16px;
        font-weight: bold;
      }

      .main {
        font-size: 16px;
        font-weight: bold;
        padding: 5px 10px 5px 10px;
        color: white;
        box-sizing: border-box;
        border-radius: 5px;
      }

    }
  }

  .formMain {
    height: 100%;
    width: 100%;
    box-sizing: border-box;
    border: 1px solid #888888;
    position: relative;

    .mainCanvas {
      height: 100%;
      width: 100%;
    }

    .mainPanel {
      position: absolute;
      top: 0;
      right: 0;
      height: 100%;
      width: 300px;
      border-left: 1px solid #888888;
      background-color: rgb(248, 248, 248);
      overflow-y: auto;
    }
  }
}

.searchBox {
  padding: 10px;
  background: #fff;
  border-radius: 10px;
  transition: all .1s linear;
  display: flex;
  flex-direction: column;

  .searchMoreBox {
    min-width: 192px;
    margin-top: 10px;
    display: flex;
    align-items: center;
    flex-direction: row;
  }
}

.tableDiv {
  margin-top: 10px;
  background-color: white;
  padding-bottom: 10px;
  border-radius: 10px;
  transition: all .1s linear;
  display: flex;
  flex-direction: column;

  .btnBox {
    padding: 10px;
  }
}

.infoBox {
  padding: 15px 15px 0 15px;
  box-sizing: border-box;
  border-radius: 6px;
  border: 1px solid #C4C4C4;
  position: relative;

  .infoTitle {
    user-select: none;
    position: absolute;
    top: 0;
    left: 0;
    padding: 0 10px;
    font-size: 14px;
    line-height: 14px;
    font-weight: bold;
    transform: translateX(15px) translateY(-50%);
    background-color: white;
  }

  .imgBox {
    height: auto;
    width: 100%;
    display: flex;
    flex-wrap: wrap;
    align-content: flex-start;

    .imgItemBox {
      height: 240px;
      width: calc(100% / 3);
      box-sizing: border-box;
      padding: 10px;
      overflow-y: auto;
      display: flex;
      align-items: center;
      justify-content: center;
      position: relative;

      .imgDeleteBtn {
        position: absolute;
        z-index: 1;
        top: 10%;
        right: 10%;
      }
    }
  }
}

// 流程图相关
.userDialogBox {
  ::v-deep .el-dialog__body {
    height: 600px;
    overflow-y: auto;
  }

  .userBoxMain {
    height: 100%;
    width: 100%;
    display: flex;
    flex-direction: column;

    .searchBox {
      height: 48px;
      width: 100%;
    }

    .dataBox {
      height: calc(100% - 48px);
      width: 100%;
      overflow-y: auto;
    }
  }
}

::v-deep .bjs-powered-by {
  opacity: 0;
  user-select: none;
  pointer-events: none;
}

// v-if过渡动画
// 查询框
.search-enter-active {
  transition: all .1s linear;
}
.search-enter {
  opacity: 0;
}
.search-leave-active {
  transition: all .1s linear;
}
.search-leave-to {
  opacity: 0;
}
</style>
