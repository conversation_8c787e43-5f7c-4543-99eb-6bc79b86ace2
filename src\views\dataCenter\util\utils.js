// 判断是否是大屏
export function isBigScreen() {
  let availW = window.innerWidth || window.screen.width || window.screen.availWidth || 1920;
  let availH = window.innerHeight || window.screen.height || window.screen.availHeight || 1080;
  let isBigScreen = false;
  if (availW > 2560 && availH > 1440) {
    isBigScreen = true;
  }
  return isBigScreen;
}

// 监听是否全屏
export function nowSize(val) {
  let availW = window.innerWidth || window.screen.width || window.screen.availWidth || 1920;
  let availH = window.innerHeight || window.screen.height || window.screen.availHeight || 1080;
  let nowClientWidth = availW;
  let initWidth = 1920;
  if (availW > 2560 && availH > 1440) {
    nowClientWidth = availW;
    initWidth = 3840;
  }
  return val * (nowClientWidth/initWidth);
}

export function calcFontSize(minSize = 14) {
  let baseWidth = 1920 || window.innerWidth || window.screen.width || window.screen.availWidth || 1920;
  return Math.max((window.innerWidth / baseWidth) * 14, minSize)
}