<template>
  <div class="app-container pageBox">
    <div class="btnBox">
      <div class="leftBox">
        <el-button type="primary" icon="el-icon-tickets" size="medium" class="btn" circle></el-button>
        <p class="title">流程：{{ formTitle }}</p>
      </div>
      <div class="rightBox" v-if="formIsApprove === '0'">
        <el-button type="warning" size="medium" class="submitBtn" @click="formSubmit(false)">退回</el-button>
        <el-button type="primary" size="medium" class="submitBtn" @click="formSubmit(true)">复核</el-button>
      </div>
    </div>
    <el-tabs v-model="formTab">
      <el-tab-pane label="流程表单" name="form">
        <div class="formBox">
          <div class="mainBox">
            <router-view ref="formRouteRef" />
          </div>
          <div class="hisBox" style="margin-bottom: 20px;" v-if="formIsApprove === '0'">
            <el-tabs v-model="formIdeaTab" type="border-card">
              <el-tab-pane label="签字意见" name="idea">
                <el-input
                  type="textarea"
                  placeholder="请输入审批意见"
                  :rows="4"
                  v-model="formParmas.variables.comment"
                  maxlength="200"
                  show-word-limit
                ></el-input>
              </el-tab-pane>
            </el-tabs>
          </div>
          <div class="hisBox">
            <el-tabs v-model="formHisTab" type="border-card">
              <el-tab-pane label="流转意见" name="his">
                <div class="hisMainBox">
                  <el-timeline reverse>
                    <el-timeline-item
                      v-for="item in formIdeaList"
                      :timestamp="`${item.startTime}`"
                      color="#5cbb7a"
                      placement="top"
                    >
                      <el-card>
                        <div slot="header" class="clearfix">
                          <span>节点：{{ item.nodeName }}</span>
                        </div>
                        <div class="hisIdeaBox">
                          <div class="boxMain">
                            <h3 v-if="item.assignee">{{ item.content.comment }}</h3>
                            <div class="changeBox" v-if="item.content.operatingRecord && item.content.operatingRecord.length > 0">
                              <div class="title">表单修改记录</div>
                              <div class="list" v-for="val in item.content.operatingRecord">
                                <el-tag type="info" effect="plain">{{ val.name }}</el-tag>
                                <!-- <p style="font-weight: bold;">{{ val.name }}</p> -->
                                <p style="margin: 0 10px;">由</p>
                                <p style="color: #E6A23C">{{ `"${val.oldVal}"` }}</p>
                                <p style="margin: 0 10px;">改为</p>
                                <p style="color: #F56C6C">{{ `"${val.newVal}"` }}</p>
                              </div>
                              <!-- <div class="none" v-if="item.content.operatingRecord.length === 0">
                                暂无记录
                              </div> -->
                            </div>
                          </div>
                          <div class="boxBottom">
                            <p v-if="item.assignee">提交人：{{ item.assignee }}</p>
                            <p v-if="item.assignee">提交时间：{{ item.endTime }}</p>
                            <p v-if="!item.assignee">流程结束</p>
                            <p v-if="!item.assignee">结束时间：{{ item.endTime }}</p>
                          </div>
                        </div>
                      </el-card>
                    </el-timeline-item>
                  </el-timeline>
                </div>
              </el-tab-pane>
            </el-tabs>
          </div>
        </div>
      </el-tab-pane>
      <el-tab-pane label="流程图" name="bpmn">
        <div class="formBox">
          <el-image
            style="width: 100%; height: 100%"
            :src="formBpmnUrl"
            fit="scale-down"
          >
          </el-image>
          <!-- <img :src="formBpmnUrl" style="height: 100%; width: 100%" /> -->
        </div>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
// -------------------- 引入 --------------------

// 组件

// API
import { queryIdeaTask, queryBpmnTask } from '@/api/process/task/task' // 流程相关
import { listUser } from "@/api/system/user"; // 用户管理模块


export default {
  name: "ProcessForm",
  // 数据字典
  dicts: [],
  // 组件
  components: {},

  // -------------------- 变量 --------------------
  data() {
    return {
      /**
       * 表单相关
       */
      formTab: 'form', // 表单标签页
      formHisTab: 'his', // 流转意见标签页
      formIdeaTab: 'idea', // 签字意见标签页
      formTitle: '', // 表单标题
      formRoute: '', // 表单路由
      formIdeaList: [], // 表单流转意见数据
      formBpmnUrl: '', // 表单流程图
      formParmas: { // 表单提交参数
        taskId: '', // 流程ID
        variables: {
          comment: '', // 审批意见
          operatingRecord: '', // 操作记录
          approved: true, // 是否通过
          variablesJson: [], // 附加变量
        }
      },
      formIsApprove: '0', // 是否为审批
    }
  },
  // -------------------- 方法 --------------------
  mounted () {
    this.initPage()
  },
  methods: {
    /**
     * 页面相关
     */
    // 初始化页面
    initPage() {
      this.formTitle = `${this.$route.query.name}：${this.$route.query.title}`
      // 是否为审批
      this.formIsApprove = this.$route.query.isApprove
      // 查询流转意见
      this.queryIdea(this.$route.query.processInstanceId)
      // 查询流程图
      this.queryBpmn(this.$route.query.processInstanceId)
    },

    /**
     * 查询相关
     */
    // 查询流转意见
    queryIdea(id) {
      let vo = {
        processInstanceId: id
      }
      queryIdeaTask(vo).then((res) => {
        if(res.code === 200) {
          this.formIdeaChange(res.data).then((data) => {
            this.formIdeaList = data
          })
        }
      })
    },

    // 查询流程图
    queryBpmn(id) {
      let vo = {
        instanceId: id
      }
      queryBpmnTask(vo).then((res) => {
        if(res) {
          let blob = new Blob([res], { type: 'image/jpg' })
          let url = URL.createObjectURL(blob)
          this.formBpmnUrl = url
        }
      })
    },

    // 查询用户
    async queryUser(id) {
      let vo = {
        pageNum: 1,
        pageSize: 10,
        userId: id
      }
      await listUser(vo).then((res) => {
        if(res.code === 200) {
          return res.rows[0].nickName
        } else {
          return id
        }
      }).catch(() => {
        return id
      })
    },

    /**
     * 表单相关
     */

    // 提交表单
    formSubmit(type) {
      if(!this.formParmas.variables.comment) {
        this.$message.warning('请输入审批意见')
        return
      }
      // 调用表单方法
      this.$modal.confirm('确认提交？').then(async () => {
        // 流程参数
        let vo = {
          approved: type, // 通过
          comment: this.formParmas.variables.comment, // 流转意见
        }
        this.$refs.formRouteRef.formProcessSubmit(vo)
      })
    },

    // 处理意见字段
    formIdeaChange(data) {
      return new Promise((resolve) => {
        for(let i=0; i<data.length; i++) {
          if(data[i].content && data[i].content !== '无内容') {
            data[i].content = JSON.parse(data[i].content)
            if(data[i].content.operatingRecord) {
              data[i].content.operatingRecord = JSON.parse(data[i].content.operatingRecord)
            }
          }
          if(i === data.length - 1) {
            resolve(data)
          }
        }
      })
    },
  },
}
</script>

<style lang="scss" scoped>

.pageBox {
  display: flex;
  flex-direction: column;

  .btnBox {
    height: 40px;
    width: 100%;
    display: flex;
    flex-direction: row;

    .leftBox {
      height: 100%;
      width: 50%;
      display: flex;
      flex-direction: row;
      align-items: center;

      .btn {
        margin-right: 15px;
      }

      .title {
        height: 100%;
        font-size: 14px;
        display: flex;
        align-items: center;
      }
    }

    .rightBox {
      height: 100%;
      width: 50%;
      display: flex;
      flex-direction: row;
      justify-content: flex-end;
      align-items: center;

      .submitBtn {
        margin-left: 15px;
      }
    }
  }

  .formBox {
    height: 100%;
    width: 100%;
    box-sizing: border-box;
    padding: 10px;
    display: flex;
    flex-direction: column;

    .mainBox {
      width: 100%;
      box-sizing: border-box;
      padding: 20px;
      margin-bottom: 20px;
      border: 1px solid #DCDFE6;
      box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.12), 0 0 6px 0 rgba(0, 0, 0, 0.04);
    }

    .hisBox {
      width: 100%;

      .hisMainBox {
        width: 100%;

        .hisIdeaBox {
          width: 100%;
          display: flex;
          flex-direction: column;

          .boxMain {
            width: 100%;
            display: flex;
            flex-direction: column;

            .changeBox {
              width: 50%;
              box-sizing: border-box;
              padding: 10px;
              border-radius: 5px;
              border: 1px solid #DCDFE6;
              box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.12), 0 0 6px 0 rgba(0, 0, 0, 0.04);
              display: flex;
              flex-direction: column;

              .title {
                padding: 10px;
                user-select: none;
                color: #888888;
                font-size: 14px;
                font-weight: bold;
                border-bottom: 1px solid #DCDFE6;
              }

              .list {
                padding: 10px;
                user-select: none;
                color: #888888;
                font-size: 14px;
                border-bottom: 1px solid #DCDFE6;
                display: flex;
                flex-direction: row;
                flex-wrap: wrap;
                align-items: center;

                p {
                  margin: 0;
                  line-height: 20px;
                }
              }

              .none {
                padding: 10px;
                user-select: none;
                color: #888888;
                font-size: 14px;
              }
            }
          }

          .boxBottom {
            width: 100%;
            font-size: 14px;
            color: #888888;
            display: flex;
            justify-content: space-between;
          }
        }
      }

      ::v-deep .el-tabs {
        height: 100% !important;
      }
    }
  }
}

::v-deep .el-tabs {
    height: calc(100% - 40px);

    .el-tabs__header {
      height: 40px;
    }

    .el-tabs__content {
      height: calc(100% - 55px);
      overflow-y: auto;

      .el-tab-pane {
        height: 100%;
      }
    }
  }


</style>
