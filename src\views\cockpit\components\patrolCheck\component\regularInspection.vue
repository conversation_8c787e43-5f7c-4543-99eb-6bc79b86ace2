<template>
  <div class="regular-inspection">
    <div class="chart-pie">
      <Echarts :option="option" v-if="option" height="100%" key="regularKey" />
    </div>
    <div class="chart-legend">
      <div v-for="(item, index) in data" :key="index" class="legend-list"
        :class="index < data.length - 1 ? 'border-b' : ''">
        <span>{{ item.name }}</span>
        <span>{{ item.value }}</span>
        <span>{{ item.unit }}</span>
      </div>
    </div>
  </div>
</template>

<script>
import * as echarts from "echarts";
import Echarts from '../../echarts/echarts.vue';
import { isBigScreen } from '../../../util/utils';

export default {
  name: 'regularInspection',
  components: {
    Echarts,
  },
  props: {
    progress: {
      type: [String, Number],
      default: () => 0,
    },
    color: {
      type: String,
      default: () => '#38a700',
    },
    data: {
      type: Array,
      default: () => [
        {
          name: '累计检查次数',
          value: 1627,
          unit: '次'
        },
        {
          name: '待检查桥梁',
          value: 1627,
          unit: '座'
        },
        {
          name: '已检查桥梁',
          value: 230,
          unit: '座'
        },
      ],
    }
  },
  data() {
    return {
      isBig: isBigScreen(),
      option: null,
    }
  },
  mounted() {
    this.initChart();
  },
  methods: {
    initChart() {
      let center = ['50%', '50%'];
      this.option = {
        backgroundColor: 'rgba(0,0,0,0)',
        title: [{
          text: '已检占比',
          left: '35%',
          top: this.isBig ? '55%' : '50%',
          textStyle: {
            color: '#ffffff',
            fontSize: this.isBig ? 26 : 13,
            fontWeight: '400',
          }
        }, {
          text: this.progress + '%',
          left: '35%',
          top: '35%',
          textStyle: {
            fontSize: this.isBig ? 44 : '22',
            color: '#ffffff',
            fontFamily: 'Lato',
            fontWeight: '700',
          },
        }],
        polar: {
          radius: ['80%', '88%'],
          center,
        },
        angleAxis: {
          max: 100,
          show: false,
        },
        radiusAxis: {
          type: 'category',
          show: true,
          axisLabel: {
            show: false,
          },
          axisLine: {
            show: false,

          },
          axisTick: {
            show: false
          },
        },
        series: [
          {
            type: "pie",
            zlevel: 0,
            silent: true,
            radius: ["94%", "98%"],
            center,
            hoverAnimation: false,
            color: "rgba(0,62,122,1)",
            label: {
              normal: {
                show: false,
              },
            },
            labelLine: {
              normal: {
                show: false,
              },
            },
            data: [1],
          },
          {
            name: '',
            type: 'bar',
            roundCap: true,
            barWidth: this.isBig ? 20 : 10,
            showBackground: true,
            backgroundStyle: {
              color: 'rgba(66, 66, 66, .3)',
            },
            data: [this.progress],
            coordinateSystem: 'polar',
            itemStyle: {
              normal: {
                color: new echarts.graphic.LinearGradient(0, 1, 0, 0, [{
                  offset: 0,
                  color: this.color,
                }, {
                  offset: 1,
                  color: this.color,
                }]),
              }
            }
          },
        ]
      };
    }
  },
}
</script>

<style lang="scss" scoped>
@import "@/assets/styles/utils.scss";

.regular-inspection {
  width: 100%;
  height: 100%;
  padding: vwpx(20px) vwpx(10px);
  display: flex;
  align-items: center;

  .chart-pie {
    flex: 1;
    width: 100%;
    height: 100%;
  }

  .chart-legend {
    flex: 1;
    margin-right: vwpx(40px);
    margin-left: vwpx(20px);

    .legend-list {
      display: flex;
      align-items: center;
      height: vwpx(100px);
      line-height: vwpx(100px);

      span:first-child {
        font-family: Microsoft YaHei UI, Microsoft YaHei UI;
        font-weight: 400;
        font-size: vwpx(28px);
        color: #FFFFFF;
        margin-left: vwpx(20px);
        flex-shrink: 0;
      }

      span:nth-child(2) {
        font-family: Microsoft YaHei UI, Microsoft YaHei UI;
        font-weight: 700;
        font-size: vwpx(36px);
        color: #409DFF;
        margin: 0 vwpx(20px);
      }

      span:last-child {
        font-family: Microsoft YaHei UI, Microsoft YaHei UI;
        font-weight: 400;
        font-size: vwpx(24px);
        color: #B6B6B6;
      }
    }

    .border-b {
      border-bottom: 1px dotted rgba(156, 189, 255, 0.5);
    }
  }
}
</style>