import inherits from 'inherits';

import PropertiesActivator from 'bpmn-js-properties-panel/lib/PropertiesActivator';


import idProps from 'bpmn-js-properties-panel/lib/provider/bpmn/parts/IdProps';
import nameProps from 'bpmn-js-properties-panel/lib/provider/bpmn/parts/NameProps';
// import processProps from 'bpmn-js-properties-panel/lib/provider/bpmn/parts/ProcessProps';
// import linkProps from 'bpmn-js-properties-panel/lib/provider/bpmn/parts/LinkProps';
// import eventProps from 'bpmn-js-properties-panel/lib/provider/bpmn/parts/EventProps';
// import documentationProps from 'bpmn-js-properties-panel/lib/provider/bpmn/parts/DocumentationProps';

import ConditionalProps from 'bpmn-js-properties-panel/lib/provider/camunda/parts/ConditionalProps' // 判断条件

import AssigneeProps from './parts/assigneeProps'; // 代理人
import CandidateUsers from './parts/candidateUsers'; // 用户组
import CandidateGroups from './parts/candidateGroups'; // 用户组

// function createGeneralTabGroups(element, bpmnFactory, canvas, elementRegistry, translate) {

//     var generalGroup = {
//         id: 'general',
//         label: 'General',
//         entries: []
//     };
//     idProps(generalGroup, element, translate);
//     nameProps(generalGroup, element, bpmnFactory, canvas, translate);
//     processProps(generalGroup, element, translate);

//     var detailsGroup = {
//         id: 'details',
//         label: 'Details',
//         entries: []
//     };
//     linkProps(detailsGroup, element, translate);
//     eventProps(detailsGroup, element, bpmnFactory, elementRegistry, translate);

//     var documentationGroup = {
//         id: 'documentation',
//         label: 'Documentation',
//         entries: []
//     };

//     documentationProps(documentationGroup, element, bpmnFactory, translate);

//     return [
//         generalGroup,
//         detailsGroup,
//         documentationGroup
//     ];
// }

function createCustomTabGroups(element, bpmnFactory, canvas, elementRegistry, translate) {
    var customGroup = {
        id: 'info-custom',
        label: '基础信息',
        entries: []
    }
    // id字段
    idProps(customGroup, element, translate);
    // name字段
    nameProps(customGroup, element, bpmnFactory, canvas, translate);
    // 单用户字段
    AssigneeProps(customGroup, element, translate);
    // 多用户字段
    CandidateUsers(customGroup, element, translate);
    // 组字段
    CandidateGroups(customGroup, element, translate);
    // 判断条件
    ConditionalProps(customGroup, element, bpmnFactory, translate)
    
    return [
        customGroup
    ];
}

export default function CustomPropertiesProvider(
    bpmnFactory,
    canvas,
    elementRegistry,
    elementTemplates,
    eventBus,
    modeling,
    replace,
    selection,
    translate
) {
    PropertiesActivator.call(this, eventBus);

    this.getTabs = function(element) {
        // var generalTab = {
        //     id: 'general',
        //     label: 'General',
        //     groups: createGeneralTabGroups(element, bpmnFactory, canvas, elementRegistry, translate)
        // };

        var authorityTab = {
            id: 'custom',
            label: '属性',
            groups: createCustomTabGroups(element, bpmnFactory, canvas, elementRegistry, translate)
        };
        return [
            // generalTab,
            authorityTab
        ];
    }
}

CustomPropertiesProvider.$inject = [
    'bpmnFactory',
    'canvas',
    'elementRegistry',
    'elementTemplates',
    'eventBus',
    'modeling',
    'replace',
    'selection',
    'translate'
];

inherits(CustomPropertiesProvider, PropertiesActivator);