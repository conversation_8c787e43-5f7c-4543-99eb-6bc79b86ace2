<template>
  <div class="route-index">
    <section class="route-info-t">
      <div class="total-mileage">
        <span class="color-1">{{ statistic.wholeNum.toFixed(2) }}<small>km</small></span>
        <span>养护总里程</span>
      </div>
      <div class="dotted-divider-vertical"></div>
      <div class="total-mileage">
        <span class="color-2">
          {{ statistic.wholeNumRoad }}<small>条</small>
        </span>
        <!-- <span>集团路段</span> -->
        <span>公路路段</span>
      </div>
      <div class="dotted-divider-vertical"></div>
      <div class="total-mileage">
        <span class="color-3">
          {{ statistic.wholecompanyRoad }}<small>条</small>
        </span>
        <!-- <span>项目公司路段</span> -->
        <span>养护路段</span>
      </div>
    </section>
    <div class="divider"></div>
    <section class="route-info-b mt-2">
      <div
        class="info-list"
        :class="isBig ? 'mb-3' : 'mb-1'"
        v-for="(item, index) in infoList"
        :key="index"
      >
        <div class="info-b-title">
          {{ item.deptType == "1" ? "权属集团" : "权属项目公司" }}
        </div>

        <div class="list-item">
          <div class="list-item-col">
            <div class="divider-vertical"></div>
            <div class="info-item">
              <div class="title-name">养护里程</div>
              <div class="val color-1">
                {{ item.mainLength }} <small>km</small>
              </div>
            </div>
          </div>

          <div class="list-item-col j-center">
            <div class="divider-vertical"></div>
            <div class="info-item">
              <div class="title-name">公路路段</div>
              <div class="val color-2">
                {{ item.highwaySection }} <small>条</small>
              </div>
            </div>
          </div>

          <div class="list-item-col j-end">
            <div class="divider-vertical"></div>
            <div class="info-item">
              <div class="title-name">养护路段</div>
              <div class="val color-3">
                {{ item.maintenanceSection }} <small>条</small>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  </div>
</template>

<script>
import { isBigScreen } from "../../util/utils";
import { getBaseCount } from "@/api/cockpit/index";

export default {
  name: "RouteIndex",
  created() {
    this.getBaseCount();
  },
  methods: {
    async getBaseCount() {
      const res = await getBaseCount();
      if (res.code === 200) {
        this.infoList = res.data;
        res.data.forEach((item) => {
          item.mainLength = item.mainLength || 0
          this.statistic.wholeNum += item.mainLength;
          // 公路路段
          item.highwaySection = item.highwaySection || 0
          this.statistic.wholeNumRoad += item.highwaySection;
          // 养护路段
          item.maintenanceSection = item.maintenanceSection || 0
          this.statistic.wholecompanyRoad  += item.maintenanceSection;
        });
        return;
        // 临时修改数据 2025-1-3
        this.statistic.wholeNum = 6002.519;
        this.statistic.wholeNumRoad = 76;
        this.infoList =  this.infoList.map(v =>{
          v.mainLength = v.deptType == 1 ? 3381.446 : 2621.073;
          v.highwaySection = v.deptType == 1 ? 42 : 34;
          return v;
        })
      }
    },
  },
  data() {
    return {
      isBig: isBigScreen(),
      statistic: {
        wholeNum: 0,
        wholeNumRoad: 0,
        wholecompanyRoad: 0,
      },

      infoList: [
        {
          title: "权属集团",
          mainLength: "3380.419",
          highwaySection: "51",
          maintenanceSection: "51",
        },
        {
          title: "权属项目公司",
          mainLength: "3380.419",
          highwaySection: "51",
          maintenanceSection: "51",
        },
      ],
    };
  },
};
</script>

<style lang="scss" scoped>
@import "@/assets/styles/utils.scss";

.route-index {
  font-family: Microsoft YaHei UI, Microsoft YaHei UI;
  padding: vwpx(20px) vwpx(34px);
  color: white;

  .divider {
    flex-shrink: 0;
    width: 100%;
    height: vwpx(2px);
    background: repeating-linear-gradient(
      to right,
      rgba(156, 189, 255, 0.5),
      rgba(156, 189, 255, 0.5) vwpx(2px),
      transparent vwpx(3px),
      transparent vwpx(8px)
    );
    margin: vwpx(6px) 0;
  }
  .route-info-t {
    height: 28%;
    display: flex;
    align-items: center;
    justify-content: space-between;

    .total-mileage {
      span:first-child {
        font-weight: 700;
        font-size: vwpx(32px);
      }
      span:last-child {
        font-size: vwpx(26px);
        color: #ffffff;
        text-align: left;
        margin-left: vwpx(15px);
      }
    }
    .dotted-divider-vertical {
      flex-shrink: 0;
      width: vwpx(2px);
      height: vwpx(70px);
      background: repeating-linear-gradient(
        to bottom,
        rgba(156, 189, 255, 0.5),
        rgba(156, 189, 255, 0.5) vwpx(2px),
        transparent vwpx(3px),
        transparent vwpx(8px)
      ); /* 创建一条虚线背景 */
    }
  }
  .route-info-b {
    // height: 68%;

    .info-list {
      display: flex;
      flex-direction: column;
      // flex: 1;

      .info-b-title {
        font-family: Microsoft YaHei UI, Microsoft YaHei UI;
        // font-weight: 700;
        font-size: vwpx(30px);
        color: #ffffff;
      }

      .list-item {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-top: vwpx(10px);

        .list-item-col {
          display: flex;
          align-items: center;
          // flex: 1;

          .divider-vertical {
            flex-shrink: 0;
            width: vwpx(4px);
            height: vwpx(50px);
            background-color: #014473;
            margin-right: vwpx(10px);
          }

          .info-item {
            max-height: vwpx(80px);
            
            .title-name {
              font-size: vwpx(24px);
              color: rgba(255,255,255,1);
            }
            .val {
              font-family: Microsoft YaHei UI, Microsoft YaHei UI;
              font-weight: 700;
              font-size: vwpx(32px);

              small {
                font-size: vwpx(24px);
              }
            }
          }
        }
      }
    }
  }

  .mb-1 {
    margin-bottom: vwpx(10px);
  }
  .mb-2 {
    margin-bottom: vwpx(20px);
  }
  .mb-3 {
    margin-bottom: vwpx(30px);
  }
  .mb-5 {
    margin-bottom: vwpx(50px);
  }

  .mt-1 {
    margin-top: vwpx(10px);
  }

  .mt-2 {
    margin-top: vwpx(20px);
  }

  .mt-3 {
    margin-top: vwpx(30px);
  }

  .ml-auto {
    margin-left: auto;
  }

  .j-center {
    justify-content: center;
  }

  .j-end {
    justify-content: flex-end;
  }

  .color-1 {
    color: #ffba00;
  }
  .color-2 {
    color: #2ddb44;
  }
  .color-3 {
    color: #017bfd;
  }
  .color-4 {
    color: #42e4ee;
  }
  .color-5 {
    color: #fd4cdb;
  }
}
</style>
