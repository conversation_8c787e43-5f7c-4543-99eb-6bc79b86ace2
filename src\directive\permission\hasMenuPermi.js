 /**
 * v-hasMenuPermi 操作菜单权限处理
 * Copyright (c) 2019 ruoyi
 */

import store from '@/store'
export default {
  inserted(el, binding, vnode) {
    const { value } = binding
    const all_permission = "*:*:*";
    const menuPerms = store.getters && store.getters.menuPerms
    const allpermissions = store.getters && store.getters.permissions
    // 获取当前路由信息
    const vm = vnode.context;
    const route = vm.$route;
    if (value && value instanceof Array && value.length > 0) {
      // 管理员直接放行
      if (allpermissions[0] === all_permission) {
        return
      }
      const permissionFlag = value
      const hasPermissions = menuPerms.some(permission => {
        return permissionFlag.includes(permission.perms) && route.path === ('/' + permission.path)
      })

      if (!hasPermissions) {
        el.parentNode && el.parentNode.removeChild(el)
      }
    } else {
      throw new Error(`请设置操作权限标签值`)
    }
  }
}
