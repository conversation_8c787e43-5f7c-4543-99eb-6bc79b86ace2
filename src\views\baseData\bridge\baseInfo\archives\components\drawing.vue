<template>
  <div
    class="right-box"
    :style="oneMap?'background-color: unset;':'background-color: #fff;'"
    :class="oneMap?'one-map':''"
  >
    <div class="right-box-head">
      <h5>桥梁图纸文件</h5>
    </div>
    <div style="height: calc(100% - 53px); width: 100%;">
      <CardBox
        style="padding: 10px; border-radius: 0 0 10px 10px;"
        :style="oneMap ? 'height: calc(100vh - 171px);' : 'height: calc(100vh - 155px);'"
        typeCode="BS129"
        v-for="item in [assetId]"
        :bizId="item"
        :key="item"
        :assetType="8"
      />
    </div>
  </div>
</template>
  
<script>
import CardBox from '@/components/Drawing/cardBox.vue'
export default {
  name: 'drawing-baseInfo',
  components: { CardBox },
  inject: {
    oneMap: {
      default: false, 
    }
  },
  dicts: [],
  props: {
    assetId: {
      type: String || Number,
      default: ''
    }
  },
  data() {
    return {}
  },
  watch: {},
  created() {},
  methods: {}
}
</script>
  
<style lang="scss" scoped>
@import '@/assets/styles/common.scss';
.right-box {
  width: 100%;
  height: 100%;
  border-radius: 10px;
  box-shadow: 0px 0px 10px 0px rgba(0, 0, 0, 0.2);
  position: relative;
  .right-box-head {
    width: 100%;
    height: 52px;
    background: #409eff;
    border-radius: 10px 10px 0 0;
    display: flex;
    justify-content: center;
    align-items: center;
    h5 {
      font-weight: 700;
      font-size: 14px;
      color: #ffffff;
    }
  }
}
</style>
  