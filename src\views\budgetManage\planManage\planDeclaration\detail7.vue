<template>
  <div class="maindiv">
    <el-row>
      <el-col :span="9">
        <el-form
          :model="formData"
          ref="queryForm"
          :inline="true"
          :rules="rules"
          label-width="80px"
        >
          <el-form-item label="所属类别">
            <el-input v-model="showTypeName" style="width: 220px" disabled>
            </el-input>
          </el-form-item>
          <el-form-item label="项目名称" prop="projectShowName">
            <el-input
              v-model="formData.projectShowName"
              style="width: 220px"
              readonly
              disabled
              @focus="projOpenFlag = true"
              placeholder="请输入项目名称"
              clearable
            >
            </el-input>
          </el-form-item>
          <el-form-item label="工程桩号" prop="mileageStr">
            <el-input
              v-model="formData.mileageStr"
              style="width: 220px"
              placeholder="请输入工程桩号"
              clearable
            >
            </el-input>
          </el-form-item>
          <el-form-item label="专项名称" prop="projectName">
            <el-input
              v-model="formData.projectName"
              style="width: 220px"
              placeholder="请输入专项名称"
              clearable
            >
            </el-input>
          </el-form-item>
          <el-form-item label="工程类别" prop="projType">
            <dict-select
              type="project_type"
              onlyTailNode
              clearable
              v-model="formData.projType"
              placeholder="请选择工程类别"
              style="width: 220px"
              :disabled="optionModel == 'read'"
            ></dict-select>
          </el-form-item>
          <el-form-item label="专项金额" prop="sumFund">
            <el-input
              v-model="formData.sumFund"
              style="width: 220px"
              placeholder="请输入专项预计划金额"
              disabled
            >
            </el-input>
          </el-form-item>
          <el-form-item label="合同" prop="conIds">
            <el-select
              multiple
              v-model="formData.conIds"
              filterable
              placeholder="请选择合同"
              clearable
              @change="handleCheckCon"
              :disabled="optionModel == 'read'"
              style="width: 220px"
            >
              <el-option
                v-for="item in contractList"
                :key="item.id"
                :label="item.name"
                :value="`${item.id}_${item.name}`"
              >
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="工程数量" prop="projNum	">
            <el-input
              type="textarea"
              v-model="formData.projNum"
              style="width: 450px"
              placeholder="请输入主要工程数量"
              clearable
            >
            </el-input>
          </el-form-item>
          <el-form-item label="立项理由" prop="projectReason	">
            <el-input
              type="textarea"
              v-model="formData.projectReason"
              style="width: 450px"
              placeholder="请输入立项理由"
              clearable
            >
            </el-input>
          </el-form-item>
          <el-form-item label="工程内容" prop="enContent">
            <el-input
              type="textarea"
              v-model="formData.enContent"
              style="width: 450px"
              placeholder="请输入主要工程内容"
              clearable
            >
            </el-input>
          </el-form-item>
          <el-form-item label="备注" prop="remark">
            <el-input
              type="textarea"
              v-model="formData.remark"
              style="width: 450px"
              placeholder="请输入备注"
              clearable
            >
            </el-input>
          </el-form-item>
        </el-form>
      </el-col>
      <el-col :span="15">
        <el-form
          :rules="rules"
          size="medium"
          label-width="70px"
          style="height: 58px"
        >
          <el-col :span="6" v-if="optionModel != 'read'">
            <el-form-item label="养护方法">
              <el-button
                icon="el-icon-plus"
                circle
                @click="openLibModel"
              ></el-button>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="总费用">
              <el-input disabled :value="total" :style="{ width: '100%' }">
              </el-input>
            </el-form-item>
          </el-col>
        </el-form>
        <div class="tab-box">
          <el-tabs v-model="activeName" type="card" @tab-click="handleClick">
            <template v-for="tab in tabs">
              <el-tab-pane
                :label="tab.split('_')[1]"
                :name="tab.split('_')[0]"
              ></el-tab-pane>
            </template>
          </el-tabs>
        </div>
        <el-table v-adjust-table
          :data="schemeMap[activeName]"
          border
          height="620px"
          ref="tableRef"
          style="width: 100%"
        >
          <el-table-column
            label="序号"
            align="center"
            type="index"
            width="50"
          />
          <el-table-column
            prop="schemeCode"
            align="center"
            label="子目号"
            width="100"
          >
          </el-table-column>
          <el-table-column
            prop="schemeName"
            align="center"
            label="子目名称"
            width="150"
          >
            <template slot-scope="scope">
              <el-input
                v-if="scope.row.schemeCode.indexOf('10000') != -1"
                v-model="scope.row.schemeName"
              >
              </el-input>
              <span v-else>{{scope.row.schemeName}}</span>
            </template>
          </el-table-column>
          <el-table-column prop="unit" align="center" label="单位" width="80">
            <template slot-scope="scope">
              <el-input
                v-if="scope.row.schemeCode.indexOf('10000') != -1"
                v-model="scope.row.unit"
              >
              </el-input>
              <span v-else>{{scope.row.schemeName}}</span>
            </template>
          </el-table-column>
          <el-table-column prop="price" align="center" label="单价" width="80">
            <template slot-scope="scope">
              <el-input
                v-if="scope.row.schemeCode.indexOf('10000') != -1"
                v-model="scope.row.price"
                @change="changePrice(scope.row)"
              >
              </el-input>
              <span v-else>{{scope.row.price}}</span>
            </template>
          </el-table-column>
          <el-table-column
            prop="calcDesc"
            align="center"
            label="计算式"
            width="150"
          >
            <template slot-scope="scope">
              <el-input
                v-model="scope.row.calcDesc"
                @change="changeCalculation(scope.row)"
              >
              </el-input>
            </template>
          </el-table-column>
          <el-table-column
            prop="num"
            align="center"
            label="方法数量"
            width="80"
          >
            <template slot-scope="scope">
              <el-input
                v-model="scope.row.num"
                @change="changeSchemeNum(scope.row)"
              >
              </el-input>
            </template>
          </el-table-column>
          <el-table-column
            prop="amount"
            align="center"
            label="资金"
            width="100"
          >
          </el-table-column>
          <el-table-column
            prop="remark"
            align="center"
            label="备注"
            width="150"
          >
            <template slot-scope="scope">
              <el-input v-model="scope.row.remark"> </el-input>
            </template>
          </el-table-column>
          <el-table-column prop="field101" align="center" label="移除" v-if="optionModel != 'read'">
            <template slot-scope="scope">
              <el-button size="mini" type="text" @click="handleDelete(scope)"
                >移除
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-col>
    </el-row>
    <span
      slot="footer"
      class="dialog-footer"
      style="display: flex; justify-content: flex-end; margin-top: 10px"
    >
      <el-button
        type="primary"
        @click="onSubmit"
        v-if="optionModel != 'read'"
        :loading="submitLoading"
        >确定</el-button
      >
      <el-button @click="closeDialog()">取消</el-button>
    </span>
    <methods-tree
      ref="methods"
      :con-id="methodsConId"
      v-model="methods"
      :domain-id="domainId"
    ></methods-tree>
    <el-dialog :visible.sync="projOpenFlag" v-if="projOpenFlag" title="项目选择" width="70%" append-to-body modal-append-to-body >
      <project-select @checkProj="checkProj" />
    </el-dialog>
  </div>
</template>

<script>
import {
  AddPlanDetail,
  EditPlanDetail,
  AddCondetail,
  GetCondetail,
  EditCondetail,
} from "@/api/budgetManage/planManage";
import { getListAll } from "@/api/contract/info/index";
import MethodsTree from "@/components/MethodsTree/index.vue";
import ProjectSelect from "./projectSelect.vue";
export default {
  components: {ProjectSelect, MethodsTree },
  props: {
    planId: {
      type: String,
      default: "",
    },
    typeId: {
      type: [String, Number],
      default: "",
    },
    companyType: {
      type: Number,
      default: 0,
    },
    currentRow: {
      type: Object,
      default: null,
    },
    maiSecId: {
      type: String,
      default: "",
    },
    optionModel: {
      type: String,
      default: "add",
    },
    typeLabel: {
      type: String,
      default: "",
    },
    domainId: {
      type: [String, Number],
      default: "",
    },
    treeData: {
      type: Array,
      default: []
    }
  },
  watch: {
    methods: {
      async handler(val) {
        if (val.length > 0) {
          // schemeMap 存入映射tab的表格数据
          const cloneData = JSON.parse(JSON.stringify(val))
          cloneData.forEach((item) => {
            item.conId = this.activeName;
            item.schemeId = item.id;
            delete item.id;
          });
          if (this.schemeMap[this.activeName]) {
            const tempList = []
            for (let index = 0; index < cloneData.length; index++) {
              const item = cloneData[index];
              const hasFlag = this.schemeMap[this.activeName].some((value) => {
                return value.schemeId === item.schemeId;
              });
              if (hasFlag) {
                try {
                  await this.$confirm(
                    `存在与子目号${item.schemeCode}相同的数据，是否确认加入?`,
                    "确认",
                    {
                      confirmButtonText: "确定",
                      cancelButtonText: "取消",
                      type: "warning",
                      distinguishCancelAndClose: true,
                    }
                  );
                  tempList.push(item);
                } catch (error) {
                  continue;
                }
              } else {
                tempList.push(item);
              }
            }
            this.schemeMap[this.activeName] = [
              ...this.schemeMap[this.activeName],
              ...tempList,
            ];
          } else {
            this.schemeMap[this.activeName] = cloneData;
          }
          this.schemeList = Object.values(this.schemeMap).reduce(
            (previousValue, currentValue) => [
              ...previousValue,
              ...currentValue,
            ],
            []
          );
          console.log("schemeList", this.schemeList);
        }
      },
      immediate: true,
      deep: true,
    },
    currentRow: {
      async handler(val) {
        if (val) {
          this.typeId = val.typeId;
          this.companyType = val.companyType;
          this.formData = { ...val };
          this.formData.conIds = val.conIds.split(",");
          this.total = val.sumFund;
          this.getTypeLabel()
          if (this.formData.conIds) {
            const res = await GetCondetail({ detailId: val.id, pageSize: 999 });
            const rows = res.rows;
            this.formData.conIds.forEach((item) => {
              this.$set(
                this.schemeMap,
                item,
                rows.filter((value) => value.conId == item)
              );
            });
            this.schemeList = Object.values(this.schemeMap).reduce(
              (previousValue, currentValue) => [
                ...previousValue,
                ...currentValue,
              ],
              []
            );
          }
          this.getContractList();
        }
      },
      immediate: true,
      deep: true,
    },
    typeLabel: {
      handler(val) {
        if(val) {
          this.showTypeName = val
        }
      },
      immediate: true,
      deep: true,
    }
  },
  data() {
    return {
      formData: {
        projectName: "",
        projectReason: "",
        conIds: "",
        remark: "",
      },
      rules: {
        projectName: [
          { required: true, message: "请输入项目名称", trigger: "blur" },
        ],
        projectReason: [
          { required: true, message: "请输入立项理由", trigger: "blur" },
        ],
        conIds: [{ required: true, message: "请选择合同", trigger: "change" }],
      },
      contractList: [],
      libData: [],
      methods: [],
      schemeList: [],
      total: 0,
      activeName: "",
      submitLoading: false,
      tabs: [],
      methodsConId: "",
      schemeMap: {},
      showTypeName: '',
      projOpenFlag: false,
    };
  },
  methods: {
    getTypeLabel() {
      function findLabelById(tree, id) {
        for (let node of tree) {
          if (node.id === id) {
            return node.label;
          }
          if (node.children) {
            const foundLabel = findLabelById(node.children, id);
            if (foundLabel) {
              return foundLabel;
            }
          }
        }
        return null;
      }
      this.$nextTick(()=> {
        this.showTypeName = findLabelById(this.treeData, this.currentRow.typeId)
      })
    },
    handleClick(tab, event) {
      this.methodsConId = tab.name;
    },
    onSubmit() {
      this.$refs["queryForm"].validate(async (valid) => {
        if (valid) {
          if (this.schemeList.length <= 0) {
            this.$message.warning(`合同下的子目不能为空`)
            return
          }
          for (const key in this.schemeMap) {
            if (this.schemeMap[key].length <= 0) {
              const label = this.contractList.find(item=> item.id == key)?.name || ''
              this.$message.warning(`${label}合同下的子目不能为空`)
                return
            }
          }
          this.submitLoading = true;
          // 提交接口
          let obj = { ...this.formData };
          obj.conIds = obj.conIds.map((el) => el.split("_")[0]).join(",");
          if (this.currentRow) {
            // 修改
            const params = {
              companyType: this.companyType,
              planId: this.planId,
              typeId: this.typeId,
              ...obj,
              sumFund: this.total,
              id: this.currentRow.id,
            };
            // 先改表单
            const res = await EditPlanDetail(params);
            this.submitLoading = false;
            if (res.code == 200) {
              this.schemeList.forEach((element, index) => {
                element.detailId = this.currentRow.id;
                element.orderIndex = index;
              });
              console.log("修改", this.schemeList);
              // 再改合同明细
              const result = await AddCondetail([...this.schemeList]);
              if (result.code == 200) {
                this.$modal.msgSuccess('保存成功')
                this.closeDialog();
              }
            }
          } else {
            // 新增
            const params = {
              companyType: this.companyType,
              planId: this.planId,
              typeId: this.typeId,
              ...obj,
              sumFund: this.total,
            };
            const res = await AddPlanDetail(params);
            this.submitLoading = false;
            if (res.code == 200) {
              console.log("新增", this.schemeList);
              // 先传传左边的表单数据得到id再传右边的表格数组
              this.schemeList.forEach((element, index) => {
                element.detailId = res.msg;
                element.orderIndex = index;
              });
              console.log("新增", this.schemeList);
              const result = await AddCondetail([...this.schemeList]);
              if (result.code == 200) {
                this.closeDialog();
              }
            }
          }
        }
      });
    },
    closeDialog() {
      this.$refs["queryForm"].resetFields();
      this.$emit("closeDialog");
    },
    // 合同列表
    async getContractList() {
      const res = await getListAll({ sectionName: this.maiSecId });
      this.contractList = res.rows || [];
      this.contractList.forEach((el) => {
        this.formData.conIds.forEach((conid, index) => {
          if (el.id == conid) {
            const newConid = conid + "_" + el.name;
            this.$set(this.formData.conIds, index, newConid);
          }
        });
      });
      if (this.currentRow) {
        this.handleCheckCon(this.formData.conIds);
      }
    },
    handleCheckCon(e) {
      // 对比 e 和this.tabs 找出多出或者少出的那条
      const setTabs = new Set(this.tabs);
      const setE = new Set(e);
      const diffE = e.filter(x => !setTabs.has(x));
      const diffTabs = this.tabs.filter(x => !setE.has(x));
      const differences = [...diffE, ...diffTabs];
      if (e.length > this.tabs.length) {  // 新增
        this.activeName = differences[0].split("_")[0];
      } else {  // 移除
        this.activeName = this.tabs[0].split("_")[0];
        this.removeName = differences[0].split("_")[0];
        delete this.schemeMap[this.removeName]
        this.schemeList = Object.values(this.schemeMap).reduce(
          (previousValue, currentValue) => [...previousValue, ...currentValue],
          []
        );
      }
      this.tabs = e;
      this.methodsConId = this.activeName;
      this.total = this.schemeList.reduce((acc, curr) => acc + curr.amount, 0);
      this.formData.sumFund = this.total;
    },
    openLibModel() {
      if (this.tabs && this.tabs.length > 0) {
        this.$refs.methods.openLibModel();
      } else {
        this.$message.info("请选择合同");
      }
    },
    changePrice(row) {
      this.$set(row, "amount", Math.round((row.num || 0) * (row.price || 0)));
      this.schemeList = Object.values(this.schemeMap).reduce(
        (previousValue, currentValue) => [...previousValue, ...currentValue],
        []
      );
      this.total = this.schemeList.reduce((acc, curr) => acc + curr.amount, 0);
      this.formData.sumFund = this.total;
    },

    changeCalculation(row) {
      if (!this.isValidMathFormula(row.calcDesc)) {
        this.$modal.msgError('计算式错误，请检查')
        return
      }
      this.$set(row, "num", Math.round(this.math.evaluate(row.calcDesc), 3));
      this.$set(row, "amount", Math.round((row.num || 0) * (row.price || 0)));
      this.schemeList = Object.values(this.schemeMap).reduce(
        (previousValue, currentValue) => [...previousValue, ...currentValue],
        []
      );
      this.total = this.schemeList.reduce((acc, curr) => acc + curr.amount, 0);
      this.formData.sumFund = this.total;
    },
    changeSchemeNum(row) {
      this.$set(row, "amount", Math.round((row.num || 0) * (row.price || 0)));
      this.schemeList = Object.values(this.schemeMap).reduce(
        (previousValue, currentValue) => [...previousValue, ...currentValue],
        []
      );
      this.total = this.schemeList.reduce((acc, curr) => acc + curr.amount, 0);
      this.formData.sumFund = this.total;
    },
    handleDelete(scope) {
      this.schemeMap[this.activeName].splice(scope.$index, 1);
      this.schemeList = Object.values(this.schemeMap).reduce(
        (previousValue, currentValue) => [...previousValue, ...currentValue],
        []
      );
      this.total = this.schemeList.reduce((acc, curr) => acc + curr.amount, 0);
      this.formData.sumFund = this.total;
    },
    checkProj(e) {
      this.formData.projectShowName = e.projName
      this.formData.projectId = e.projId
      this.projOpenFlag = false
    },
  },
  created() {
    if (!this.currentRow) {
      this.getContractList();
    } else {


    }
  },
};
</script>

<style scoped lang="scss">
.maindiv {
}
</style>
<style lang="scss" scoped>
@import "@/assets/styles/business.scss";
</style>
