<template>
  <div>
    <div class="draggable">
      <el-table v-adjust-table
        size="mini"
        style="width: 100%"
        v-loading="loading"
        border
        :data="tableData"
        row-key="id"
        ref="dataTable"
        stripe
        highlight-current-row
        @row-click="handleClickRow"
        @selection-change="handleSelectionChange"
        :height="'calc(100vh - 200px)'"

      >
        <el-table-column type="expand">
          <template slot-scope="props">
            <el-table v-adjust-table :data="props.row.methodList" style="width: 100%">
              <el-table-column
                prop=""
                align="center"
                label="">
              </el-table-column>
              <el-table-column
                prop="schemeCode"
                align="center"
                label="子目号">
              </el-table-column>
              <el-table-column
                prop="schemeName"
                align="center"
                label="养护方法">
              </el-table-column>
              <el-table-column
                prop="calcDesc"
                align="center"
                label="计算式">
              </el-table-column>
              <el-table-column
                prop="num"
                align="center"
                label="方法数量">
              </el-table-column>
              <el-table-column
                prop="unit"
                align="center"
                label="方法单位">
              </el-table-column>
              <el-table-column
                prop="price"
                align="center"
                label="单价">
              </el-table-column>
              <el-table-column
                prop="amount"
                align="center"
                label="金额">
              </el-table-column>
              <el-table-column
                prop="remark"
                align="center"
                label="备注">
              </el-table-column>
            </el-table>
          </template>
        </el-table-column>
        <el-table-column
          label="序号"
          align="center"
          type="index"
          width="50"
        />
        <template v-for="(column,index) in columns">
          <el-table-column :label="column.label"
                           v-if="column.visible"
                           align="center"
                           :prop="column.field"
                           :width="column.width">
            <template slot-scope="scope">
              <dict-tag v-if="column.dict" :options="dict.type[column.dict]" :value="scope.row.disease[column.field]"/>
              <template v-else-if="column.slots">
                <RenderDom :row="scope.row" :index="index" :render="column.render"/>
              </template>
              <span v-else>{{ scope.row.disease[column.field] }}</span>
            </template>
          </el-table-column>
        </template>
        <el-table-column
          label="操作"
          fixed="right"
          align="center"
          width="100"
          class-name="small-padding fixed-width"
        >
          <template slot-scope="scope">
            <el-dropdown size="mini">
              <el-button size="mini" type="text" icon="el-icon-d-arrow-right">操作</el-button>
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item>
                  <el-button type="text" @click="handleRegis(scope.row)" icon="el-icon-edit">完工登记</el-button>
                </el-dropdown-item>
                <el-dropdown-item>
                  <el-button type="text" icon="el-icon-delete" @click="handleNotYetDispose(scope.row)">暂不处理
                  </el-button>
                </el-dropdown-item>
                <el-dropdown-item>
                  <el-button type="text" icon="el-icon-delete" @click="handleRejectToRegister(scope.row)">撤回待施工
                  </el-button>
                </el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
          </template>
        </el-table-column>
      </el-table>
      <pagination
        :total="total"
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
        @pagination="handleQuery"
      />
    </div>
    <el-drawer :wrapperClosable="false" :title="drawerTitle" destroy-on-close :visible.sync="drawer" size="70%">
      <detail @close="handleCloseDetail" :row-data="rowData"></detail>
    </el-drawer>
    <el-dialog title="事件信息" destroy-on-close :visible.sync="dialogVisible" width="80%">
      <event-info></event-info>
    </el-dialog>
    <el-dialog title="附件列表" destroy-on-close :visible.sync="openFile" width="80%">
      <file-upload v-model="disFilePath" :forView="true"></file-upload>
    </el-dialog>
  </div>
</template>

<script>
import Detail from "./detail.vue";
import EventInfo from "../../component/eventTreeInfo.vue";
import {notYetDispose} from "@/api/dailyMaintenance/construction/registration"
import {getUserTreeStructure, list, rejectToRegister} from "@/api/dailyMaintenance/construction/complete"
import RouteCodeSection from "@/components/RouteCodeSection/index.vue";

export default {
  components: {
    RouteCodeSection,
    EventInfo,
    Detail,
    RenderDom: {
      functional: true,
      props: {
        row: Object,
        index: Number,
        render: Function
      },
      render(createElement, ctx) {
        const {row, index} = ctx.props
        return ctx.props.render(row, index)
      }
    }
  },
  dicts: ['route_direction', 'lane', 'event_type', 'sys_asset_type'],
  data() {
    return {
      leftTotal: 1,
      showSearch: false,
      queryParams: {
        pageNum: 1,
        pageSize: 50,
      },
      total: 0,
      loading: false,
      columns: [
        {
          key: 0, width: 100, field: 'field1', label: `操作`, visible: true, slots: true, render: (row, index) => {
            return (
              <el-button
                size="mini"
                type="text">业务流程</el-button>
            )
          }
        },
        {key: 0, width: 100, field: 'assetMainType', label: `资产类型`, visible: true, dict: 'sys_asset_type'},
        {key: 2, width: 100, field: 'maiSecName', label: `路段名称`, visible: true},
        {key: 3, width: 100, field: 'routeCode', label: `路线编码`, visible: true},
        {key: 3, width: 100, field: 'direction', label: `上下行`, visible: true, dict: 'route_direction'},
        {key: 4, width: 100, field: 'lane', label: `位置`, visible: true, dict: 'lane'},
        {key: 5, width: 100, field: 'beginMileShow', label: `起点桩号`, visible: true},
        {key: 6, width: 100, field: 'endMileShow', label: `终点桩号`, visible: true},
        {key: 8, width: 100, field: 'disTypeName', label: `事件类型`, visible: true},
        {key: 9, width: 100, field: 'disDesc', label: `事件描述`, visible: true},
        {
          key: 10,
          width: 100,
          field: 'disFilePath',
          label: `图片`,
          visible: true,
          slots: true,
          render: (row, index) => {
            return (
              <el-button
                size="mini"
                disabled={!row.disease.disFilePath}
                type="text" onClick={e => this.handleOpenFile(e, row)}>查看</el-button>
            )
          }
        },
        {key: 11, width: 100, field: 'stageName', label: `阶段`, visible: true},
      ],
      tableData: [],
      rowData: {},
      drawerTitle: '完工登记',
      drawer: false,
      openFile: false,
      dialogVisible: false,
      selectIds: [],
      clickRow: {},
      // 左侧组织树
      relaNav: true,
      disFilePath: '',
      relaName: '',
      conId: '',
    }
  },
  props: {
    conId: {
      type: String,
      default: ''
    }
  },
  computed: {},
  watch: {},
  created() {
  },
  mounted() {
    if (this.conId) {
      this.handleQuery()
    }
    else {
      this.tableData = []
    }
  },
  methods: {
    handleQuery() {
      this.loading = true
      this.queryParams.conId = this.conId
      list(this.queryParams).then(res => {
        this.tableData = res.rows
        this.total = res.total
        this.loading = false
      })
    },
    // 选中
    handleSelectionChange(e) {
      this.selectIds = e.map(obj => obj.id)
    },
    resetQuery() {
      this.queryParams = {
        pageNum: 1,
        pageSize: 50
      }
      this.handleQuery()
    },
    handleCloseDetail() {
      this.rowData = {}
      this.drawer = false
      this.handleQuery()
    },
    handleOpenFile(e, row) {
      this.disFilePath = ''
      this.openFile = true
      this.disFilePath = row.disease.disFilePath
    },
    // 导出清单按钮
    exportList() {
      this.download(
        'manager/disease/export',
        {...this.queryParams},
        `disease_${new Date().getTime()}.xlsx`,
        {
          headers: {'Content-Type': 'application/json;'},
          parameterType: 'body'
        }
      )
    },
    handleRegis(rows) {
      this.rowData = rows
      this.drawer = true
    },
    handleClickRow(e) {
      this.clickRow = e
      e.isSelected = !e.isSelected;
      this.$refs.dataTable.toggleRowSelection(e);
    },
    // 暂不处理
    handleNotYetDispose(rows) {
      rows.daliyId = rows.conId
      notYetDispose(rows).then(res => {
        this.$message.success('暂不处理成功')
        this.handleQuery()
      })
    },
    handleRejectToRegister(rows) {
      rows.daliyId = rows.conId
      rejectToRegister(rows).then(res => {
        this.$message.success('撤回成功')
        this.handleQuery()
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.leftDiv {
  border-right: 1px solid #d8dce5;
  min-height: calc(100vh - 100px);
  overflow-y: auto;
  height: calc(80vh - 150px);
  position: relative;
  top: -20px;
  padding-top: 10px;
  background-color: white;
}

.leftIcon {
  border: 1px solid #dcdfe6;
  border-radius: 8px;
  width: 16px;
  height: 50px;
  line-height: 50px;
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
  position: absolute;
  right: 0;
  top: 300px;
  z-index: 2;
}

.leftIcon:hover {
  background-color: #dcdfe6;
}

.rightIcon {
  border: 1px solid #dcdfe6;
  border-radius: 8px;
  width: 16px;
  height: 50px;
  line-height: 50px;
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
  position: absolute;
  left: -10px;
  top: 280px;
  z-index: 10;
  background: white;
}

.rightIcon:hover {
  background-color: #dcdfe6;
}

.left-total {
  font-size: 13px;
  line-height: 28px;
  color: #606266;
  position: absolute;
  bottom: 10px;
  right: 10px;
}
</style>
<style lang="scss" scoped>
@import "@/assets/styles/business.scss";
</style>
