<template>
  <div class="ifr_box">
    <iframe id="ifr" :src="targetUrl" allow="microphone"></iframe>
  </div>
</template>
<script>
export default {
  name: 'iframePages',
  data() {
    return {
      targetUrl: '',
    }
  },
  beforeRouteEnter(to, from, next) {
    next((vm) => {
      let url = ''
      let usertoken = document.cookie.replace(/(?:(?:^|.*;\s*)Admin-Token\s*\=\s*([^;]*).*$)|^.*$/, "$1");
      if (usertoken) {
        url = 'https://ai.glyhgl.com/ui/chat/4e06dce1f64e73e5?usertoken=' + usertoken;
      }
      // 可以通过 vm 访问组件实例
      if (url) {
        vm.targetUrl = url
      } else {
        vm.$router.push({ path: '/404' })
      }
    })
  },
}
</script>
<style lang="scss" scoped>
.ifr_box {
  width: 100%;
  height: 100%;

  #ifr {
    width: 100%;
    height: 100%;
    border: none;
  }
}
</style>