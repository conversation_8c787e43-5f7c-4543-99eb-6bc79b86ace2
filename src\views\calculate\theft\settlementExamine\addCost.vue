<template>
  <div class="road-interflow-edit">
    <el-row :gutter="15">
      <el-form ref="elForm" size="medium" :inline="true" :model="formData" :rules="rules" label-width="120px">
        <el-col :span="12">
          <el-form-item label="调整类型" prop="adjustmentType">
            <dict-select v-model="formData.adjustmentType" type="adjustment_type" style="width: 230px;"/>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="费用类型" prop="dataType">
            <cost-select :type="35" v-model="formData.dataType" style="width: 230px;"/>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="子目号" prop="schemeCode">
            <el-input v-model="formData.schemeCode" style="width: 230px;" @focus="openLib" readonly/>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="子目名称" prop="schemeName">
            <el-input v-model="formData.schemeName" style="width: 230px;" :readonly='formData.rateFlag != 1'/>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="单价" prop="price">
            <el-input v-model="formData.price" type="number" style="width: 230px;" :readonly='formData.rateFlag != 1'/>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="单位" prop="unit">
            <el-input v-model="formData.unit" style="width: 230px;" :readonly='formData.rateFlag != 1'/>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="数量" prop="num">
            <el-input v-model="formData.num" type="number" style="width: 230px;"/>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="金额" prop="amount">
            <el-input v-model="formData.amount" type="number" style="width: 230px;"/>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="是否计算监理费" prop="isSupFund">
            <el-radio-group v-model="formData.isSupFund" size="medium">
              <el-radio v-for="(item, index) in urgentDegreeOptions" :key="index" :disabled="item.disabled"
                        :label="item.value">{{ item.label }}
              </el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
        <el-col v-if="formData.isSupFund == 1" :span="12">
          <el-form-item label="监理单位" prop="supDomainId">
            <construction-select style="width: 230px;" v-model="formData.supDomainId" :mai-sec-id="settleData.maiSecId" :mai-domain-id="settleData.domainId" :type="2" placeholder="监理单位"></construction-select>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="备注" prop="remark">
            <el-input v-model="formData.remark" :rows="3" type="textarea" style="width: 610px;"/>
          </el-form-item>
        </el-col>
        <el-col :span="24" style="text-align: right;padding-right: 7.5px;margin-top: 18px">
          <el-button type="primary" @click="onSave">保 存</el-button>
          <el-button @click="onClose">退 出</el-button>
        </el-col>
      </el-form>
    </el-row>
    <methods-tree ref="methods" :con-id="conId" v-model="methods" :domain-id="formData.domainId"></methods-tree>
  </div>
</template>

<script>
import {addFundAdjust, editFundAdjust} from "@/api/calculate/theft/settlementApplication";
import MethodsTree from "@/components/MethodsTree/index.vue";
import {multiply, round} from "lodash"
import ConstructionSelect from "@/components/ConstructionSelect/index.vue";
import CostSelect from "@/components/CostSelect/index.vue";
export default {
  components: {CostSelect, ConstructionSelect, MethodsTree},
  data() {
    return {
      formData: {
        adjustmentType: '',
        dataType: '',
        schemeCode: '',
        schemeName: '',
        price: '',
        unit: '',
        num: '',
        amount: '',
        remark: ''
      },
      rules: {
        adjustmentType: [
          {required: true, message: '请选择调整类型', trigger: 'blur'}
        ],
        dataType: [
          {required: true, message: '请选择费用类型', trigger: 'blur'}
        ],
        schemeCode: [
          {required: true, message: '请选择子目', trigger: 'change'}
        ],
        num: [
          {required: true, message: '请输入数量', trigger: 'blur'}
        ],
        isSupFund: [
          {required: true, message: '请选择是否计算监理费', trigger: 'blur'}
        ],
        supDomainId: [
          {required: true, message: '请选择监理单位', trigger: 'blur'}
        ],
      },
      urgentDegreeOptions: [{
        "label": "是",
        "value": 1
      }, {
        "label": "否",
        "value": 0
      }],
      methods: []
    }
  },
  props: {
    settleId: {
      type: String,
      default: ''
    },
    conId: {
      type: String,
      default: ''
    },
    rowData: {
      type: Object,
      default: () => {
        return {}
      }
    },
    settleData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  watch: {
    rowData: {
      handler(val) {
        if (val.id) {
          this.formData = JSON.parse(JSON.stringify(val))
        }
      },
      immediate: true,
      deep: true
    },
    methods: {
      handler(val) {
        console.log("methods",val)
        if (val.length > 0) {
          this.formData.schemeId = val[0].id
          this.formData.schemeCode = val[0].schemeCode
          this.formData.schemeName = val[0].schemeName
          this.formData.price = val[0].price
          this.formData.unit = val[0].unit
          this.formData.rateFlag = val[0].rateFlag
        }
      },
      immediate: true,
      deep: true
    },
    formPrice: {
      handler(val) {
        this.formData.amount = Number(val) || 0
      },
      immediate: true,
      deep: true,
    }
  },
  computed: {
    formPrice() {
      return round(multiply(Number(this.formData.num) || 0, Number(this.formData.price) || 0))
    },
  },
  methods: {
    onSave() {
      this.$refs.elForm.validate(valid => {
        if (!valid) return
        this.formData.settleId = this.settleId
        if (this.formData.id) {
          editFundAdjust(this.formData).then(() => {
            this.$modal.msgSuccess('保存成功')
            this.onClose()
          })
        } else {
          addFundAdjust(this.formData).then(() => {
            this.$modal.msgSuccess('保存成功')
            this.onClose()
          })
        }
      })
    },
    openLib() {
      this.$refs.methods.openLibModel()
    },
    onClose() {
      this.$emit('close', '5')
    }
  }
}
</script>

<style lang="scss" scoped>

</style>
<style lang="scss" scoped>
@import "@/assets/styles/business.scss";
</style>
