<template>
  <div class="app-container maindiv" v-loading="loading">
    <el-form ref="elForm" :model="formData" :rules="rules" size="medium" label-width="100px" :disabled="readOnly">
      <el-row>
        <div class="card_title">1.基本信息</div>
        <el-row :gutter="20" style="flex-wrap: wrap; display: flex;">
          <el-col :span="12">
            <el-form-item label="路线类型" prop="routeType">
              <DictSelect
                  v-model="formData.routeType"
                  :type="'sys_route_type'"
                  :placeholder="'路线类型'"
                  clearable
              ></DictSelect>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="管养单位" prop="domainId">
              <selectTree
                  :style="{width: '100%'}"
                  v-model="formData.domainId"
                  :deptType="100" :deptTypeList="[1, 3, 4]"
                  :onlySelectChild="true"
                  placeholder="管养单位"
                  clearable
                  filterable
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="路段名称" prop="maiSecId">
              <el-select v-model="formData.maiSecId" placeholder="养护路段" clearable style="width: 100%;"
                         @change="handleChangeRoute">
                <el-option v-for="item in routeOptions"
                           :key="item.maintenanceSectionId"
                           :label="item.maintenanceSectionName"
                           :value="item.maintenanceSectionId">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="路线编码" prop="routeCode">
              <route-code-section v-model="formData.routeCode" @change="getCode" placeholder="路线编码"
                                  style="width: 100%" :route-type="formData.routeType" ref="routeRef"
                                  :maintenanceSectionId="formData.maiSecId"></route-code-section>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="事件编号" prop="disCode">
              <el-input v-model="formData.disCode" placeholder="请输入事件编号" readonly :style="{width: '100%'}">
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="资产类型" prop="assetMainType">
              <el-select v-model="formData.assetMainType" style="width: 100%">
                <el-option v-for="dict in assetMainType" :key="dict.dictValue"
                           :label="dict.dictLabel" :value="dict.dictValue">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="资产名称" prop="assetType">
              <el-select v-model="formData.assetType" style="width: 100%" @change="changeAsset">
                <el-option v-for="dict in  assetType" :key="dict.dictValue"
                           :label="dict.dictLabel" :value="dict.dictValue">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="事件类型" prop="disType">
              <el-select v-model="formData.disType" style="width: 100%">
                <el-option v-for="dict in disType" :key="dict.dictValue"
                           :label="dict.dictLabel" :value="dict.dictValue">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="是否风险事件" prop="whetherRiskEvent">
              <el-select v-model="formData.whetherRiskEvent" style="width: 100%">
                <el-option label="是" value="是"></el-option>
                <el-option label="否" value="否"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12" v-if="formData.assetMainType == '4'">
            <el-form-item label="资产" prop="assetName">
              <el-input v-model="formData.assetName" readonly @focus="handleOpenAsset"/>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="上下行" prop="direction">
              <dict-select type="route_direction" clearable
                           v-model="formData.direction" placeholder="请选择路线方向"
                           style="width: 100%"></dict-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="处置类型" prop="dealType">
              <dict-select type="disposal_type" clearable
                           v-model="formData.dealType" placeholder="请选择处置类型"
                           style="width: 100%"></dict-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="起点桩号" prop="beginMile">
              <el-input v-model="formData.beginMile1" :style="{width: '45%'}">
                <template slot="prepend">K</template>
              </el-input>
              <div style="width: 10%;text-align: center;display: inline-block;">+</div>
              <el-input maxlength="3" v-model="formData.beginMile2" :style="{width: '45%'}">
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="终点桩号" prop="endMile">
              <el-input v-model="formData.endMile1" :style="{width: '45%'}">
                <template slot="prepend">K</template>
              </el-input>
              <div style="width: 10%;text-align: center;display: inline-block;">+</div>
              <el-input maxlength="3" v-model="formData.endMile2" :style="{width: '45%'}">
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="位置" prop="lane">
              <dict-select type="lane" clearable multiple
                           v-model="formData.laneArr" placeholder="请选择路线车道"
                           style="width: 100%"></dict-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="位置坐标" prop="point">
              <el-input v-model="formData.point" placeholder="x:0,y:0" clearable :style="{width: '100%'}">
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="采集时间" prop="collectTime">
              <el-date-picker v-model="formData.collectTime" type="datetime" format="yyyy-MM-dd HH:mm:ss"
                              value-format="yyyy-MM-dd HH:mm:ss"
                              :style="{width: '100%'}" placeholder="请选择采集时间" clearable></el-date-picker>
            </el-form-item>
          </el-col>
        </el-row>
        <el-col :span="24">
          <el-form-item label="事件描述" prop="disDesc">
            <el-input v-model="formData.disDesc" type="textarea" placeholder="请输入事件描述"
                      :autosize="{minRows: 4, maxRows: 4}" :style="{width: '100%'}"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="养护措施列表" prop="advicesList" v-if="formData.disType">
            <el-select v-model="formData.advicesList" style="width: 100%" @change="handleAdvicesChange">
              <el-option v-for="dict in advicesList" :key="dict.name"
                         :label="dict.name" :value="dict.name">
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="养护建议" prop="maintainOpinion">
            <el-input v-model="formData.maintainOpinion" type="textarea" placeholder="请输入养护建议"
                      :autosize="{minRows: 4, maxRows: 4}" :style="{width: '100%'}"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row style="margin-top: 15px;">
        <div class="card_title">2.养护方法</div>
        <el-form ref="elForm" :model="formData" :rules="rules" size="medium" label-width="100px" :disabled="readOnly">
          <el-col :span="8">
            <el-form-item label="合同">
              <el-select
                  v-model="formData.conId" filterable
                  placeholder="请输入关键词"
                  clearable
                  @change="handleCheckCon"
                  :style="{width: '100%'}">
                <el-option
                    v-for="item in contractList"
                    :key="item.id"
                    :label="item.name"
                    :value="item.id"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="养护方法">
              <el-button icon="el-icon-plus" circle @click="openLibModel"></el-button>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="总费用">
              <el-input
                  disabled
                  :value="total"
                  :style="{width: '100%'}">
              </el-input>
            </el-form-item>
          </el-col>
        </el-form>
        <el-table
            :data="schemeList"
            border
            height="200px"
            ref="tableRef"
            style="width: 100%">
          <el-table-column
              label="序号"
              align="center"
              type="index"
              width="50"
          />
          <el-table-column
              prop="schemeCode"
              align="center"
              label="子目号"
              width="100">
          </el-table-column>
          <el-table-column
              prop="schemeName"
              align="center"
              label="子目名称"
              width="100">
          </el-table-column>
          <el-table-column
              prop="unit"
              align="center"
              label="单位"
              width="100">
          </el-table-column>
          <el-table-column
              prop="price"
              align="center"
              label="单价"
              width="100">
          </el-table-column>
          <el-table-column
              prop="calculation"
              align="center"
              label="计算式"
              width="100">
            <template slot-scope="scope">
              <el-input v-model="scope.row.calculation" @change="changeCalculation(scope.row)">
              </el-input>
            </template>
          </el-table-column>
          <el-table-column
              prop="schemeNum"
              align="center"
              label="方法数量"
              width="100">
            <template slot-scope="scope">
              <el-input v-model="scope.row.schemeNum" @change="changeSchemeNum(scope.row)">
              </el-input>
            </template>
          </el-table-column>
          <el-table-column
              prop="amount"
              align="center"
              label="资金"
              width="100">
          </el-table-column>
          <el-table-column
              prop="remark"
              align="center"
              label="备注"
              width="100">
            <template slot-scope="scope">
              <el-input v-model="scope.row.remark">
              </el-input>
            </template>
          </el-table-column>
          <el-table-column
              prop="field101"
              align="center"
              label="移除">
            <template slot-scope="scope">
              <el-button
                  size="mini"
                  type="text"
                  @click="handleDelete(scope.row)"
              >移除
              </el-button
              >
            </template>
          </el-table-column>
        </el-table>
      </el-row>
      <el-row style="margin-top: 15px;">
        <div class="card_title">3.采集照片</div>
        <el-col :span="24">
          <el-form-item label="" prop="disPicPath">
            <file-upload :for-view="readOnly" v-model="formData.disPicPath" :owner-id="formData.disPicPath"></file-upload>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row style="margin-top: 15px;">
        <div class="card_title">4.事件附件</div>
        <el-col :span="24">
          <el-form-item label="" prop="disFilePath">
            <file-upload :for-view="readOnly" v-model="formData.disFilePath" :owner-id="formData.disFilePath"></file-upload>
          </el-form-item>
        </el-col>
      </el-row>
      <div style="text-align: right;padding-right: 7.5px" v-if="!readOnly">
        <el-button type="primary" @click="onSubmit">保 存</el-button>
        <el-button @click="onClose">取 消</el-button>
      </div>
    </el-form>
    <methods-tree :key="formData.conId" scheme-type="日常养护" :con-id="formData.conId" ref="methodsRef" :domain-id="formData.domainId"
                  @input="checkLib"></methods-tree>
    <el-dialog title="选择资产" destroy-on-close :visible.sync="openAsset" width="65%" append-to-body v-if="openAsset">
      <asset-select @checkAsset="checkAsset"
                    :asset-type="formData.assetType == 107 ? 31 : formData.assetType == 145 ? 32 : 33"
                    :mai-sec-id="formData.maiSecId"></asset-select>
    </el-dialog>
  </div>
</template>
<script>
import fileUpload from "@/components/FileUpload/index.vue";
import {listMaintenanceSectionAll} from "@/api/system/maintenanceSection";
import {getListAll} from "@/api/contract/info/index"
import {getTreeData} from "@/api/contract/quotationSystem"
import {
  addDiseaseData,
  updateDiseaseData,
  getDiseaseDataById
} from "@/api/dailyMaintenance/eventManage/eventData";
import selectTree from "@/components/DeptTmpl/selectTree.vue";
import {listAllData} from "@/api/system/dict/data";
import {listByMaintenanceSectionId} from "@/api/baseData/common/routeLine";
import RouteCodeSection from "@/components/RouteCodeSection/index.vue"
import assetSelect from "@/components/AssetSelect/index"
import moment from "moment";
import {listAllDiseases} from "@/api/patrol/diseases";
import {getCode} from "@/api/system/reportcode";
import MethodsTree from "@/components/MethodsTree/index.vue";
import {listDiseaseAdvices} from "@/api/patrol/diseaseAdvices";

export default {
  components: {selectTree, fileUpload, RouteCodeSection, assetSelect, MethodsTree},
  props: {
    readOnly: {
      type: Boolean,
      default: false
    },
    rowData: {
      type: Object,
      default: () => {
        return {}
      }
    },
    selectData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  data() {
    return {
      formData: {
        domainId: '',
        disPicPath: '',
        disFilePath: '',
        collectTime: moment().format('YYYY-MM-DD HH:mm:ss')
      },
      routeOptions: [], // 路段数据
      routeList: [], // 路线编码数据
      loading: false,
      rules: {
        routeType: [{required: true, message: '请选择路线类型', trigger: 'change'}],
        domainId: [{required: true, message: '请选择管养单位', trigger: 'change'}],
        maiSecId: [{required: true, message: '请选择路段名称', trigger: 'blue'}],
        routeCode: [{required: true, message: '请选择路线编码', trigger: 'change'}],
        disCode: [{required: true, message: '请输入事件编号', trigger: 'blue'}],
        assetMainType: [{required: true, message: '请选择资产类型', trigger: 'change'}],
        assetType: [{required: true, message: '请选择资产名称', trigger: 'change'}],
        disType: [{required: true, message: '请选择事件类型', trigger: 'change'}],
        assetName: [{required: true, message: '请选择资产', trigger: 'change'}],
        whetherRiskEvent: [{required: true, message: '请选择是否风险事件', trigger: 'change'}],
        direction: [{required: true, message: '请选择上下行', trigger: 'change'}],
        dealType: [{required: true, message: '请选择处置类型', trigger: 'change'}],
        beginMile: [{
          required: true, validator: (rule, value, callback) => {
            if (this.formData.beginMile1 != null && this.formData.beginMile2 != null) {
              if (isNaN(Number(this.formData.beginMile1)) || isNaN(Number(this.formData.beginMile2))) {
                  callback(new Error('请输入合法的桩号'))
              }
              const route = this.$refs.routeRef.getRouteInfo()
              if (route) {
                if ((this.formData.beginMile1 >= parseInt(route.pileEnd / 1000) || 0 ) || (this.formData.beginMile1 < parseInt(route.pileStart / 1000) || 0 )) {
                  callback(new Error('起点桩号不在桩号范围内'))
                }
              }
              callback()
            } else {
              callback(new Error('请完整填写起点桩号'))
            }
          }, trigger: 'blur'
        }],
        endMile: [{
          required: true, validator: (rule, value, callback) => {
            if (this.formData.endMile1 != null && this.formData.endMile2 != null) {
              if (isNaN(Number(this.formData.endMile1)) || isNaN(Number(this.formData.endMile2))) {
                callback(new Error('请输入合法的桩号'))
              }
              const route = this.$refs.routeRef.getRouteInfo()
              if (route) {
                if ((this.formData.endMile1 >= parseInt(route.pileEnd / 1000) || 0 ) || (this.formData.endMile1 < parseInt(route.pileStart / 1000) || 0 )) {
                  callback(new Error('起点桩号不在桩号范围内'))
                }
              }
              callback()
            } else {
              callback(new Error('请完整填写终点桩号'))
            }
          }, trigger: 'blur'
        }],
        laneArr: [{required: true, type: 'array', message: '请选择位置', trigger: 'change'}],
        collectTime: [{required: true, message: '请选择采集时间', trigger: 'change'}]
      },
      contractList: [],
      schemeList: [],
      total: 0,
      defaultProps: {
        children: 'children',
        label: 'schemeName'
      },
      assetMainType: [],
      openAsset: false,
      disType: [],
      advicesList: []
    }
  },
  computed: {
    assetType: function () {
      const nameList = this.assetMainType.find(item => item.dictValue === this.formData.assetMainType)
      return nameList ? nameList.children : []
    },
  },
  watch: {
    rowData: {
      handler: function (val, oldVal) {
        if (val) {
          this.formData = JSON.parse(JSON.stringify(val))
          this.$set(this.formData, 'beginMile1', parseInt(this.formData.beginMile / 1000) || 0)
          this.$set(this.formData, 'beginMile2', this.formData.beginMile % 1000 || 0)
          this.$set(this.formData, 'endMile1', parseInt(this.formData.endMile / 1000) || 0)
          this.$set(this.formData, 'endMile2', this.formData.endMile % 1000 || 0)
          if (val.id) {
            this.loading = true
            // 查询详情
            getDiseaseDataById(val.id).then(res => {
              this.schemeList = res.data.schemeList || []
              // 计算资金
              this.total = 0
              this.schemeList.forEach(item => {
                item.amount = item.schemeNum * item.price
                this.total += item.amount
              })
            })
            // 获取合同信息
            getListAll({
              sectionName: val.maiSecId
            }).then(res => {
              this.contractList = res.rows || []
            })
          } else {
            const date = moment().format('YYYY-MM-DD HH:mm:ss')
            this.$set(this.formData, 'collectTime', date)
          }
          if (this.formData.lane) {
            this.$set(this.formData, 'laneArr', this.formData.lane.split(','))
          }
          if (this.formData.assetMainType) {
            this.formData.assetMainType = String(this.formData.assetMainType)
          }
          if (this.formData.assetType) {
            this.formData.assetType = String(this.formData.assetType)
          }
          if (this.formData.disType) {
            this.formData.disType = String(this.formData.disType)
          }
          if (this.formData.domainId) {
            this.formData.domainId = String(this.formData.domainId)
          }
          const pointArr = []
          if (this.formData.lon) pointArr.push(`x:${this.formData.lon}`)
          if (this.formData.lat) pointArr.push(`y:${this.formData.lat}`)
          this.$set(this.formData, 'point', pointArr.join(','))
          if (this.formData.maiSecId) {
            this.handleChangeRoute(this.formData.maiSecId)
          }
        }
      },
      immediate: true
    },
    "formData.domainId": {
      handler: function (val) {
        if (val) {
          this.formData.maiSecName = ''
          this.$set(this.formData, 'maiSecId', '')
          this.$set(this.formData, 'routeCode', '')
          listMaintenanceSectionAll({departmentId: val}).then(res => {
            if (res.code == 200) {
              this.routeOptions = res.data
            }
          })
        }
      },
      immediate: false
    },
    "formData.assetMainType": {
      handler: function (val) {
        this.$set(this.formData, 'assetType', '')
        this.$set(this.formData, 'disType', '')
      },
      immediate: false
    },
    "formData.disType": {
      handler: function (val) {
        if (val)
          listDiseaseAdvices(val).then(res => {
            this.advicesList = res.rows
          })
      },
      immediate: true
    },
    "formData.maiSecId": {
      handler: function (val) {
        this.$set(this.formData, 'routeCode', '')
      },
      immediate: false
    },
    "formData.beginMile1": {
      handler(val) {
        this.$set(this.formData, 'endMile1', val)
      }
    },
    "formData.beginMile2": {
      handler(val) {
        this.$set(this.formData, 'endMile2', val)
      }
    }
  },
  created() {
    this.getAssetType()
  },
  mounted() {
    if (!this.formData.disFilePath) this.formData.disFilePath = this.generateUUID()
    if (!this.formData.disPicPath) this.formData.disPicPath = this.generateUUID()
    listMaintenanceSectionAll({departmentId: this.formData.domainId}).then(res => {
      if (res.code == 200) {
        this.routeOptions = res.data
      }
    })
    if (this.formData.assetType) {
      listAllDiseases({assetName: this.formData.assetType}).then(res => {
        this.disType = res.data.map(item => {
          return {
            dictLabel: item.diseaseName,
            dictValue: item.diseaseCode
          }
        })
      })
    }
  },
  methods: {
    onSubmit() {
      this.$refs['elForm'].validate(valid => {
        if (!valid) return
        this.formData.lane = this.formData.laneArr.join(',');
        this.formData.beginMile = this.formData.beginMile1 * 1000 + parseInt(this.formData.beginMile2)
        this.formData.endMile = this.formData.endMile1 * 1000 + parseInt(this.formData.endMile2)
        if (this.formData.disPicPath && Array.isArray(this.formData.disPicPath) && this.formData.disPicPath.length > 0) {
          this.formData.disPicPath = this.formData.disPicPath[0]
        }
        if (this.formData.disFilePath && Array.isArray(this.formData.disFilePath) && this.formData.disFilePath.length > 0) {
          this.formData.disFilePath = this.formData.disFilePath[0]
        }
        this.schemeList.forEach(item => {
          item.id = undefined
        })

        if (this.formData.point) {
          // 使用正则表达式匹配并提取 x 和 y 的值
          const regex = /(\d+\.\d+)/g;
          const matches = this.formData.point.match(regex);
          if (matches) {
            // 将匹配到的值转换为浮点数，并赋值给 formData
            const numbers = matches.map(Number);
            console.log(numbers); // 输出: [123.45, 678.9]

            this.formData.lon = parseFloat(numbers[0]);
            this.formData.lat = parseFloat(numbers[1]);
          }
        }
        this.formData.schemeList = this.schemeList
        if (this.formData.id) {
          updateDiseaseData(this.formData).then(res => {
            if (res.code == 200) {
              this.$modal.msgSuccess('保存成功')
              this.$emit("close");
            }
          })
        } else {
          addDiseaseData(this.formData).then(res => {
            if (res.code == 200) {
              this.$modal.msgSuccess('保存成功')
              this.$emit("close");
            }
          })
        }
      })
    },
    // 选中养护路段
    handleChangeRoute(e) {
      if (e) {
        listByMaintenanceSectionId({maintenanceSectionId: e}).then((res) => {
          if (res.code == 200) {
            let routeList = res.data || [];
            routeList = routeList.filter(item => item != null)
            this.routeList = routeList
          }
        });
        // 获取合同信息
        getListAll({
          sectionName: this.formData.maiSecId
        }).then(res => {
          this.contractList = res.rows || []
        })
      }
    },
    handleCheckCon(e) {
      this.schemeList = []
    },
    openLibModel() {
      this.$refs.methodsRef.openLibModel()
    },
    // 选中方法
    checkLib(checkDatas) {
      checkDatas = checkDatas.filter(item => {
        return item.nodeType == 2
      })
      checkDatas.forEach(item => {
        this.schemeList.push({
          schemeId: item.id,
          schemeCode: item.schemeCode,
          schemeName: item.schemeName,
          unit: item.unit,
          price: item.price,
          priceRate: item.priceRate,
          safetyFeeFlag: item.safetyFeeFlag,
          decimalPlaces: item.decimalPlaces
        })
      })
      this.schemeList = this.schemeList.reduce((acc, curr) => {
        const exists = acc.some(item => item.schemeId === curr.schemeId);
        return exists ? acc : [...acc, curr];
      }, []);
    },
    async changeCalculation(row) {
      let num = eval(row.calculation) || 0
      this.$set(row, 'schemeNum', this.ceilToTwo(num, row.decimalPlaces))
      await this.changeSchemeNum(row)
    },
    async changeSchemeNum(row) {
      let amount = (row.schemeNum * row.price) || 0
      this.$set(row, 'amount', Math.round(amount))
      this.total = await this.schemeList.reduce((acc, curr) => Number(acc) + Number(curr.amount), 0)
    },
    handleDelete(row) {
      this.schemeList = this.schemeList.filter(item => item.schemeId !== row.schemeId);
      this.total = Number(this.schemeList.reduce((acc, curr) => acc + Number(curr.amount || 0), 0)).toFixed(2)
    },
    getAssetType() {
      listAllData({dictType: 'sys_asset_type'}).then(res => {
        this.assetMainType = this.handleTree(res.data, "dictCode", "dictParentCode");
      }).finally(() => {
        this.loading = false
      })
    },
    changeAsset(e) {
      this.$set(this.formData, 'disType', '')
      this.$set(this.formData, 'assetId', '')
      this.$set(this.formData, 'assetName', '')
      this.disType = []
      if (e) {
        listAllDiseases({assetName: e}).then(res => {
          this.disType = res.data.map(item => {
            return {
              dictLabel: item.diseaseName,
              dictValue: item.diseaseCode
            }
          })
        })
      }
    },
    handleAdvicesChange(value) {
      // 将选择的养护措施值推送到养护建议
      if (this.formData.maintainOpinion) {
        this.formData.maintainOpinion += `,${value}`;
      } else {
        this.formData.maintainOpinion = value;
      }
      // 清空养护措施的值
      this.formData.advicesList = '';
    },
    onClose() {
      this.$emit("close");
    },
    getCode() {
      this.$set(this.formData, 'disCode', '')
      const params = {
        reportType: 'MP_DISEASE_CODE',
        domainId: this.formData.domainId,
        routeCode: this.formData.routeCode,
        sectionName: this.formData.maiSecId,
        year: new Date().getFullYear()
      }
      getCode(params).then(res => {
        if (res.code == 200) {
          this.$set(this.formData, 'disCode', res.msg)
        }
      })
    },
    generateUUID() {
      return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {
        var r = crypto.getRandomValues(new Uint8Array(1))[0] % 16 | 0;
        var v = c === 'x' ? r : (r & 0x3 | 0x8);
        return v.toString(16);
      }).slice(0, 20); // 截取前 20 位
    },
    handleOpenAsset() {
      if (!this.formData.assetType) {
        this.$modal.msgWarning('请选择资产子类型')
        return
      }
      this.openAsset = true
    },
    checkAsset(asset) {
      this.formData.assetName = asset.name
      this.formData.assetId = asset.id
      this.openAsset = false
    }
  }
}

</script>
<style scoped lang="scss">
.card_title {
  width: 100px;
  text-align: right;
  margin-bottom: 15px;
  font-weight: bold;
}
::v-deep {
  .el-tabs__header {
    padding-left: 20px;
    border: 0;
  }
  .el-tabs__content {
    border: 0;
  }
  .el-form-item__label {
    font-family: Microsoft YaHei, Microsoft YaHei;
    font-weight: 400;
    font-size: 14px;
    color: #333333;
  }
  .el-input.is-disabled .el-input__inner {
    background-color: white;
    border-color: #dfe4ed;
    color: #1d2129;
  }
  .el-textarea.is-disabled .el-textarea__inner {
    background-color: white;
    border-color: #dfe4ed;
    color: #1d2129;
  }
}
</style>
