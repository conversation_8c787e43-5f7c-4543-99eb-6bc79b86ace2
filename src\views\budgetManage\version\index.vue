<template>
  <div class="app-container">
    <el-row :gutter="20">

      <!--筛选区开始-->
      <el-col :span="24" :xs="24">
        <el-row>
          <el-col :span="24">
            <el-form ref="queryForm" :inline="true" :model="queryParams" label-width="68px" size="small">
              <el-form-item label="" prop="year">
                <el-input
                    v-model="queryParams.code"
                    clearable
                    placeholder="版本编码"
                    style="width: 240px"
                    @keyup.enter.native="handleQuery"
                />
              </el-form-item>
              <el-form-item label="" prop="budgetVer">
                <el-input
                    v-model="queryParams.name"
                    clearable
                    placeholder="版本名称"
                    style="width: 240px"
                    @keyup.enter.native="handleQuery"
                />
              </el-form-item>
              <el-form-item>
                <el-button icon="el-icon-search" size="mini" type="primary" @click="handleQuery">搜索</el-button>
                <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
              </el-form-item>
            </el-form>
            <!--默认折叠-->
          </el-col>
        </el-row>
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button
                icon="el-icon-plus"
                size="mini"
                type="primary"
                v-has-menu-permi="['budget:version:add']"
                @click="handleAdd"
            >新增
            </el-button>
          </el-col>
          <right-toolbar
              :columns="columns"
              :showSearch.sync="showSearch"
              @queryTable="handleQuery"
          ></right-toolbar>
        </el-row>
        <!--筛选区结束-->
        <!--数据表格开始-->
        <div class="tableDiv">
          <el-table v-adjust-table v-loading="loading" :data="dataList"
                    :height="showSearch ? 'calc(100vh - 320px)' : 'calc(100vh - 260px)'"
                    border size="mini" style="width: 100%">
            <el-table-column fixed label="序号" align="center" type="index" width="100"></el-table-column>
            <template v-for="(column,index) in columns">
              <el-table-column :label="column.label"
                               v-if="column.visible"
                               align="center"
                               :prop="column.field">
                <template slot-scope="scope">
                  <dict-tag v-if="column.dict" :options="dict.type[column.dict]" :value="scope.row[column.field]"/>
                  <template v-else-if="column.slots">
                    <RenderDom :row="scope.row" :index="index" :render="column.render"/>
                  </template>
                  <span v-else>{{ scope.row[column.field] }}</span>
                </template>
              </el-table-column>
            </template>
            <el-table-column
                label="操作"
                fixed="right"
                align="center"
                width="300"
                class-name="small-padding fixed-width"
            >
              <template slot-scope="scope">
                <el-button
                    size="mini"
                    type="text"
                    icon="el-icon-edit"
                    v-has-menu-permi="['budget:version:edit']"
                    @click="handleEdit(scope.row)"
                >编辑
                </el-button>
                <el-button
                    size="mini"
                    type="text"
                    icon="el-icon-delete"
                    v-has-menu-permi="['budget:version:remove']"
                    @click="handleDelete(scope.row)"
                >删除
                </el-button>
              </template>
            </el-table-column>
          </el-table>

          <pagination
              v-show="total>0"
              :limit.sync="queryParams.pageSize"
              :page.sync="queryParams.pageNum"
              :total="total"
              @pagination="handleQuery"
          />
        </div>
        <!--数据表格结束-->
      </el-col>
    </el-row>
    <el-dialog :title="dialogTitle" :visible.sync="open" destroy-on-close append-to-body width="500px">
      <el-form ref="elForm" :model="formData" :rules="rules" size="medium" label-width="100px">
        <el-form-item label="版本编码" prop="code">
          <el-input v-model="formData.code" placeholder="请输入版本编号" :maxlength="100" clearable
                    :style="{width: '100%'}"></el-input>
        </el-form-item>
        <el-form-item label="版本名称" prop="name">
          <el-input v-model="formData.name" placeholder="请输入版本名称" :maxlength="100" clearable
                    :style="{width: '100%'}"></el-input>
        </el-form-item>
        <el-form-item label="克隆版本" prop="copyid">
          <el-select v-model="formData.copyid" placeholder="请选择克隆版本" clearable :style="{width: '100%'}">
            <el-option v-for="(item, index) in options" :key="index" :label="item.label"
                       :value="item.value" :disabled="item.disabled"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="formData.remark" placeholder="请输入备注" :maxlength="100" clearable
                    :style="{width: '100%'}"></el-input>
        </el-form-item>
      </el-form>
      <div style="text-align: right">
        <el-button @click="close">取消</el-button>
        <el-button type="primary" @click="handleConfirm">确定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { formatDate } from '@/utils/index'
import {listBudgetVersions, addBudgetVersion, editBudgetVersion, deleteBudgetVersion, listAllBudgetVersions} from "@/api/budgetManage/version";
export default {
  name: "Version",
  components: {
    RenderDom: {
      functional: true,
      props: {
        row: Object,
        index: Number,
        render: Function
      },
      render(createElement, ctx) {
        const {row, index} = ctx.props
        return ctx.props.render(row, index)
      }
    }
  },
  data() {
    return {
      // 遮罩层
      loading: false,
      // 显示搜索条件
      showSearch: false,
      // 总条数
      total: 0,
      dataList: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 50,
      },
      open: false,
      // 列信息
      columns: [
        {key: 0, field: 'code', label: `版本编码`, visible: true},
        {key: 1, field: 'name', label: `版本名称`, visible: true},
        {key: 2, field: 'createBy', label: `编制人`, visible: true},
        {key: 3, field: 'createTime', label: `编制时间`, visible: true, slots: true, render: (row, index) => {
            const time = row.createTime
            return (<span>{ formatDate(time) }</span>)
        }},
      ],
      formData: {
        code:"",
        name:"",
        copyid:null,
        remark:"",
      },
      rules: {
        code: [{
          required: true,
          message: '请输入版本编码',
          trigger: 'blur'
        }],
        name: [{
          required: true,
          message: '请输入版本名称',
          trigger: 'blur'
        }],
      },
      options: [],
      dialogTitle: "新增",
      dialogStatus:"add",
      currentRow :null
    };
  },
  watch: {
    // 根据名称筛选部门树
  },
  created() {
    this.handleQuery()
  },
  methods: {
    /** 重置按钮操作 */
    resetQuery() {
      this.queryParams = {
        pageNum: 1,
        pageSize: 50,
      }
    },
    handleQuery() {
      this.loading = true
      listBudgetVersions(this.queryParams).then(res => {
        this.dataList = res.rows
        this.total = res.total
        this.loading = false
      })
    },
    handleAdd() {
      this.getAllVersions()
      this.dialogTitle = "新增"
      this.dialogStatus = "add"
      this.formData= {
        code:"",
        name:"",
        copyid:null,
        remark:"",
      }
      this.open = true
    },
    handleEdit(row){
      this.getAllVersions()
      this.currentRow = row
      this.formData= {
        code:row.code,
        name:row.name,
        copyid:row.copyid,
        remark:row.remark,
      }
      this.open = true
      this.dialogTitle = "编辑"
      this.dialogStatus = "edit"

    },
    handleConfirm() {
      this.$refs['elForm'].validate(valid => {
        if (!valid) return
        if(this.dialogStatus === "add"){
          addBudgetVersion(this.formData).then(res => {
            if (res.code === 200) {
              this.$modal.msgSuccess("新增成功")
              this.handleQuery()
              this.open = false
            } else {
              this.$modal.msgError(res.msg)
            }
            this.close()
          })
        }else{
          this.formData.id = this.currentRow.id
          editBudgetVersion(this.formData).then(res => {
            if (res.code === 200) {
              this.$modal.msgSuccess("更新成功")
              this.open = false
              this.handleQuery()
            } else {
              this.$modal.msgError(res.msg)
            }
          })
        }
      })
    },
    handleDelete(row) {
      this.$modal.confirm("是否确认删除").then(() => {
        this.loading = true
        deleteBudgetVersion(row.id).then(res => {
          this.$modal.msgSuccess("删除成功")
          this.handleQuery()
        })
      });
    },
    close() {
      this.open = false
    },
    getAllVersions() {
      listAllBudgetVersions().then(res => {
        this.options = res.data.map(el=>{
          return {value:el.id,label:el.name}
        })
      })
    },
  }
};
</script>
<style>
</style>
<style lang="scss" scoped>
@import "@/assets/styles/business.scss";
</style>
