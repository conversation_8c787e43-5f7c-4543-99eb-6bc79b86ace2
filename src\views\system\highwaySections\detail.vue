<template>

  <el-drawer v-bind="$attrs" v-on="$listeners">
    <div class="container">
      <el-descriptions title="路段简要信息" size="mini" border>
        <el-descriptions-item label="路段名称">{{ data.roadSectionName }}</el-descriptions-item>
        <el-descriptions-item label="路段编号">{{ data.roadSectionCode }}</el-descriptions-item>
<!--        <el-descriptions-item label="通车时间">{{ parseTime(data.openingTime, '{y}-{m}-{d}') }}</el-descriptions-item>-->
        <el-descriptions-item label="车道数">{{ data.lanes }}</el-descriptions-item>
        <el-descriptions-item label="起点名称">{{ data.placeStartName }}</el-descriptions-item>
        <el-descriptions-item label="终点名称">{{ data.placeEndName }}</el-descriptions-item>
        <el-descriptions-item label="产权单位">{{ data.propertyUnitName }}</el-descriptions-item>
      </el-descriptions>

      <div style="margin: 0 10%"><el-divider>养护路段信息</el-divider></div>

      <!--数据表格开始-->
      <el-table size="mini" height="calc(100vh - 310px)" border :data="maintenanceSectionList">
        <el-table-column label="序号" fixed align="center" type="index" width="50">
          <template v-slot="scope">
            {{ (scope.$index + (queryParams.pageNum - 1) * queryParams.pageSize)+1 }}
          </template>
        </el-table-column>

        <el-table-column v-if="false" label="公路路段" :show-overflow-tooltip="true" align="center" prop="roadSectionName"/>
        <el-table-column label="管养单位" :show-overflow-tooltip="true" align="center" width="240"
                         prop="departmentName"/>
        <el-table-column label="养护路段名称" fixed :show-overflow-tooltip="true" align="center" width="120"
                         prop="maintenanceSectionName"/>
        <el-table-column label="起点桩号" :show-overflow-tooltip="true" align="center" prop="pileStart"
                         :formatter="(...arg)=>formatPile(arg[2])"/>
        <el-table-column label="终点桩号" :show-overflow-tooltip="true" align="center" prop="pileEnd"
                         :formatter="(...arg)=>formatPile(arg[2])"/>
        <el-table-column label="起点名称" :show-overflow-tooltip="true" align="center" prop="placeStartName"/>
        <el-table-column label="终点名称" :show-overflow-tooltip="true" align="center" prop="placeEndName"/>
        <el-table-column label="主线里程" :show-overflow-tooltip="true" align="center" prop="mainLength"
                         :formatter="(...arg)=>{if(arg[2]) return arg[2].toLocaleString()}"/>
        <el-table-column label="路线等级" :show-overflow-tooltip="true" align="center" prop="routeGrade">
          <template slot-scope="scope">
            <dict-tag :options="dict.type.sys_route_grade" :value="scope.row.routeGrade"/>
          </template>
        </el-table-column>
        <el-table-column label="统一里程起点桩号" :show-overflow-tooltip="true" align="center" width="140"
                         prop="unifiedMileagePileStart"
                         :formatter="(...arg)=>formatPile(arg[2])"/>
        <el-table-column label="统一里程终点桩号" :show-overflow-tooltip="true" align="center" width="140"
                         prop="unifiedMileagePileEnd"
                         :formatter="(...arg)=>formatPile(arg[2])"/>
        <el-table-column label="通车时间" :show-overflow-tooltip="true" align="center" prop="openingTime" width="180">
          <template slot-scope="scope">
            <span>{{ parseTime(scope.row.openingTime, '{y}-{m}-{d}') }}</span>
          </template>
        </el-table-column>
        <el-table-column label="统计里程" :show-overflow-tooltip="true" align="center" prop="roadSectionLength"
                         :formatter="(...arg)=>{if(arg[2]) return arg[2].toLocaleString()}"/>
      </el-table>
      <pagination
        v-show="total>0"
        :total="total"
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
        @pagination="getList"
      />
    </div>
  </el-drawer>
</template>

<script>

import {listMaintenanceSection} from "@/api/system/maintenanceSection";

export default {
  name: "RoadDetail",
  dicts: ['sys_route_grade'],
  props: {
    data: {
      type: Object,
      default: {}
    }
  },
  data() {
    return {
      // 养护路段管理表格数据
      maintenanceSectionList: [],
      queryParams: {
        pageNum: 1,
        pageSize: 50,
        roadSectionId: null,
      },
      // 总条数
      total: 0,

    }
  },
  watch: {
    data() {
      this.queryParams.roadSectionId = this.data.roadSectionId
      this.getList()
    }
  },
  methods: {
    /** 查询养护路段管理列表 */
    getList() {
      listMaintenanceSection(this.queryParams).then(response => {
        this.maintenanceSectionList = response.rows;
        this.total = response.total;
      });
    },
  },
};
</script>
<style scoped>
.container {
  margin-top: -20px;
  padding: 20px;
}
</style>
