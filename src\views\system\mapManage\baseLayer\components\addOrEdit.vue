<template>
  <el-form ref="form" :model="ruleForm" :rules="rules" label-width="100px">
    <el-row :gutter="20" :style="{pointerEvents: readonly?'none':''}">
      <el-col :span="12" :offset="0">
        <el-form-item label="图层名称" prop="layerName">
          <el-input v-model="ruleForm.layerName" placeholder="请输入图层名称" clearable></el-input>
        </el-form-item>
      </el-col>
      <el-col :span="12" :offset="0">
        <el-form-item label="中心点" prop="centerPoint">
          <el-input v-model="ruleForm.centerPoint" placeholder="请输入中心点：x,y" />
        </el-form-item>
      </el-col>
    </el-row>
    
    <el-row :gutter="20" :style="{pointerEvents: readonly?'none':''}">
      <el-col :span="12" :offset="0">
        <el-form-item label="服务地址" prop="layerUrl">
          <el-input v-model="ruleForm.layerUrl" placeholder="请输入服务地址" />
        </el-form-item>
      </el-col>
      <el-col :span="12" :offset="0">
      
        <el-form-item label="服务类型" prop="serviceType">
          <el-select v-model="ruleForm.serviceType" placeholder="请选择服务类型" style="width: 100%;">
            <el-option label="XYZ" value="XYZ"> </el-option>
            <el-option label="TMS" value="TMS"> </el-option>
            <el-option label="WMS" value="WMS"> </el-option>
          </el-select>
        </el-form-item>
      </el-col>
    </el-row>

    <el-row :gutter="20" :style="{pointerEvents: readonly?'none':''}">
      <el-col :span="12" :offset="0">
        <el-form-item label="地图范围" prop="layerRange">
          <el-input v-model="ruleForm.layerRange" placeholder="请输入图层范围" clearable></el-input>
        </el-form-item>
      </el-col>
      <el-col :span="12" :offset="0">
        <el-form-item label="是否显示" prop="ifShow">
         <el-select v-model="ruleForm.ifShow" placeholder="请选择" clearable style="width: 100%;">
          <el-option label="是" :value="1"></el-option>
          <el-option label="否" :value="0"></el-option>
         </el-select>
        </el-form-item>
      </el-col>
    </el-row>

    <el-row :gutter="20" :style="{pointerEvents: readonly?'none':''}">
      <el-col :span="12" :offset="0">
        <el-form-item label="最小层级" prop="minZoom">
          <el-input-number v-model="ruleForm.minZoom" :min="1" :max="20" controls-position="right" style="width: 100%" />
        </el-form-item>
      </el-col>
      <el-col :span="12" :offset="0">
        <el-form-item label="最大层级" prop="maxZoom">
          <el-input-number v-model="ruleForm.maxZoom" :min="1" :max="20" controls-position="right" style="width: 100%" />
        </el-form-item>
      </el-col>
    </el-row>
    
    <el-row :gutter="20" :style="{pointerEvents: readonly?'none':''}">
      <el-col :span="12" :offset="0">
        <el-form-item label="排序" prop="showIndex">
          <el-input-number v-model="ruleForm.showIndex" :min="0" :max="9999" controls-position="right" style="width: 100%" />
        </el-form-item>
      </el-col>
      <el-col :span="12" :offset="0">
        <el-form-item label="是否默认底图" prop="defaultMap">
          <el-select v-model="ruleForm.defaultMap" placeholder="请选择" clearable style="width: 100%;">
            <el-option label="是" :value="1"></el-option>
            <el-option label="否" :value="0"></el-option>
          </el-select>
        </el-form-item>
      </el-col>
    </el-row>
    <el-row :gutter="20">
      <el-col :span="12" :offset="0">
        <el-form-item label="描述" prop="description">
          <el-input v-model="form.description"  type="textarea" :rows="2" placeholder="请输入描述" />
        </el-form-item>
      </el-col>
      <el-col :span="12" :offset="0"></el-col>
    </el-row>
    
    <el-form-item style="margin: auto;display: flex;justify-content: center;">
      <el-button type="primary" @click="onSubmit" v-loading="loading" v-if="!readonly">确定</el-button>
      <el-button @click="onCancel">取消</el-button>
    </el-form-item>
  </el-form>
</template>

<script>
import { addBaseLayer, updateBaseLayer } from "@/api/oneMap/layer";
export default {
  props: {
    form: {
      type: Object,
      default: {
        layerUrl: ''
      },
    },
    id: {
      type: [String, Number],
      default: ''
    },
    readonly: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    let validateMinZoom = (rule,value,callback)=> {
      if (value > this.ruleForm.maxZoom) {
        this.$nextTick(() => {
          this.ruleForm.minZoom = this.ruleForm.maxZoom;
          callback(new Error('最小层级不能大于最大层级'));
        });
      } else {
        callback();
      }
    }
    let validateMaxZoom = (rule,value,callback)=> {
      if (value < this.ruleForm.minZoom) {
        this.$nextTick(() => {
          this.ruleForm.maxZoom = this.ruleForm.minZoom;
        });
        callback(new Error('最大层级不能小于最小层级'));
      } else {
        callback();
      }
    }
    return {
      ruleForm: {
        ifShow: 1,
      },
      rules: {
        layerName: [
          { required: true, message: '请输入图层名称', trigger: 'blur' },
        ],
        layerUrl:[
          { required: true, message: '请输入服务地址', trigger: 'blur' },
          {
            validator:(rule, value, callback)=>{
              const regex = /^(http:\/\/|https:\/\/).+$/;
              if (regex.test(value)) {
                callback();
              } else {
                callback(new Error('请输入有效的http或https格式URL'));
              }
            },
            trigger: 'blur'
          }
        ],
        serviceType: [
          { required: true, message: '请选择服务类型', trigger: ['change','blur'] },
        ],
        minZoom: [
          {validator: validateMinZoom, trigger: ['blur', 'change']},
        ],
        maxZoom: [
          {validator: validateMaxZoom, trigger: ['blur', 'change']},
        ],
      },
      loading: false,
    }
  },
  created () {
    this.ruleForm = {...this.form}
  },
  methods: {
    // 提交
    onSubmit() {
      this.$refs.form.validate(vali=>{
        if(!vali) return
        let request = this.ruleForm.id?updateBaseLayer(this.ruleForm):addBaseLayer(this.ruleForm)
        let msg = this.ruleForm.id ? '编辑成功' : '新增成功';
        this.loading = true;
        request.then(res=>{
          if(res.code ==200) {
            this.$modal.msgSuccess(msg);
            this.$emit('refresh')
            this.onCancel();
          }
        }).finally(()=>{
          this.loading = false;
        })
      })
    },
    // 取消关闭
    onCancel(){
      this.$emit('close',false)
    },
  },
};
</script>

<style lang="scss" scoped></style>
