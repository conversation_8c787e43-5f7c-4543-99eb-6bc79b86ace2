<template>
  <div>
    <Echarts :option="option" v-if="option" height="24vh" />
  </div>
</template>

<script>
import { isBigScreen } from '../../util/utils';
import Echarts from '../echarts/echarts.vue';

export default {
  components: {
    Echarts,
  },
  data() {
    return {
      option: null,
      isBig: isBigScreen(),
    }
  },
  mounted() {
    this.option = this.initPieCharts();
  },
  methods: {
    initPieCharts() {
      let color = ['#016DFF', '#00FDFD', '#FF5B00', '#FFDD21', '#1DA7FF', '#2967EA']
      let titledata = ['修复性养护', '应急性养护', '预防性养护', '专项性养护'];
      let numberdata = [180, 38, 78, 113];
      let outr = '60%';
      let inr = '55%';
      var total = 0;
      let lengData = [];
      let index = -1;
      //计算总和
      for (var i = 0; i < numberdata.length; i++) {
        total += Number(numberdata[i]);
      }
      let placeHolderStyle = {
        normal: {
          label: {
            show: false,
          },
          labelLine: {
            show: false,
          },
          color: 'rgba(0, 0, 0, 0)',
          borderColor: 'rgba(0, 0, 0, 0)',
          borderWidth: 0,
        },
      };

      var data = [];
      for (var i = 0; i < numberdata.length; i++) {
        data.push(
          {
            value: numberdata[i],
            name: titledata[i],
            itemStyle: {
              normal: {
                borderWidth: 3,
                shadowBlur: 10,
                borderColor: color[i],
                shadowColor: color[i],
                color: color[i],
              },
            },
          },
          {
            value: total / 30,
            name: '',
            itemStyle: placeHolderStyle,
          }
        );
        lengData.push({
          name: titledata[i],
          color: color[i],
          value: numberdata[i],
        })
      }
      var seriesObj = [
        {
          name: '',
          type: 'pie',
          clockWise: false,
          startAngle: '90',
          center: ['50%', '35%'], //此处调整圆环的位置
          radius: [outr, inr], //此处可以调整圆环的大小
          hoverAnimation: false,
          itemStyle: {
            normal: {
              label: {
                show: true,
                position: "outside",
                color: "#ddd",
                formatter: function (params) {
                  var percent = 0;
                  var total = 1;
                  for (var i = 0; i < data.length; i++) {
                    total += data[i].value;
                  }
                  percent = ((params.value / total) * 100).toFixed(2);
                  if (params.name !== "") {
                    return (
                      percent + "%"
                    );
                  } else {
                    return "";
                  }
                },
                fontSize: this.isBig ? 25 : 12,
              },
              labelLine: {
                length: this.isBig ? 30 : 15,
                length2: this.isBig ? 80 : 40,
                show: true,
                color: "#00ffff",
              },
            },
          },
          data: data,
          animationType: 'scale',
          animationEasing: 'elasticOut',
          animationDelay: function (idx) {
            return idx * 50;
          },
        },
      ];

      let option = {
        backgroundColor: 'rgba(0,0,0,0)',
        tooltip: {
          show: false,
        },
        title: {
          text: "{a|" + total + "}{c| 个}",
          subtext: '养护工程',
          top: "25%",
          textAlign: "center",
          left: "49%",
          textStyle: {
            color: "#fff",
            shadowColor: 'rgba(27,126,242,0.8)',
            shadowBlur: 10,
            shadowOffsetX: 5,
            shadowOffsetY: 5,
            rich: {
              a: {
                fontSize: this.isBig ? 32 : 16,
                color: "#fff",
                fontWeight: "700",
              },
              c: {
                fontSize: this.isBig ? 26 : 12,
                color: "rgba(255,255,255,0.8)",
                padding: [2, 2],
              },
            },
          },
          subtextStyle: {
            fontSize: this.isBig ? 28 : 14,
            align: "center",
            color: '#fff'
          },
        },
        toolbox: {
          show: false,
        },
        legend: {
          bottom: '5%',
          left: 'center',
          icon: "circle", //改变legend小图标形状
          itemGap: 10, // 设置legend的间距
          itemWidth: 5, // 设置宽度
          itemHeight: 5, // 设置高度
          formatter: (name) => {
            let str = name;
            let arr = lengData.filter(v=>v.name == name)
            if(arr.length > 0) {
              str = name + ' ' + arr[0].value;
            }
            return str;
          },
          textStyle: {
            fontSize: this.isBig ? 22 : 12,
            color: "#fff",
          },
          itemStyle: {
            borderColor: "transparent",
          },
          data: lengData,
        },
        series: seriesObj,
      };
      return option;
    },
  }
}
</script>

<style lang="scss" scoped></style>