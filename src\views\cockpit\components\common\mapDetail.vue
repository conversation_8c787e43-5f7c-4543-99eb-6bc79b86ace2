<template>
  <div class="map-detail" :class="form['catType'] == 1 ? 'normal-width' : 'cat-width'"
    :style="{ top: top + 'px', left: left + 'px' }" @click.stop="">
    <section class="header">
      <div class="header-title">
        <span></span>
        <span>{{ title }}</span>
      </div>
      <i class="el-icon-close close" @click="onClose"></i>
    </section>
    <section class="body">
      <template v-if="form['catType'] == 1">
        <div v-for="(item, index) in formList" :key="'d' + index" class="list">
          <span class="list-name" v-if="(item.type === 3 && form[item.prop]) || item.type !== 3">{{ item.name }}</span>
          <span class="list-value" v-if="item.type === 1">{{ form[item.prop] }} <span v-if="item.unit">{{ item.unit}}</span></span>
          <span class="list-value" v-else-if="item.type === 2 && item.prop === 'catType'">
            {{ form[item.prop] === 2 ? 'AI巡查车' : '普通巡查车' }}
          </span>
          <span class="list-value" v-else-if="item.type === 2 && item.prop === 'status'">
            <el-link :type="form[item.prop] === 1 ? '' : 'success'" :underline="false">
              {{ form[item.prop] === 1 ? '完成巡查' : '正在巡查' }}
            </el-link>
          </span>
          <span class="list-value" v-else-if="item.type === 3 && form[item.prop]">
            <el-button type="primary" size="mini" @click="playVideo">查看</el-button>
          </span>
          <span class="list-value" v-else-if="item.type === 4">
            <el-button type="primary" size="mini" @click="onListClick(form)">查询轨迹</el-button>
          </span>
        </div>
      </template>
      <template v-else>
        <div class="cat-type-2">
          <div class="video-content">
            <video width="100%" height="100%" id="video" autoplay loop :src="form['videoUrl']"
              v-if="form['videoUrl']"></video>
          </div>
          <div class="info">
            <el-row :gutter="20" class="mb5">
              <el-col :span="12" :offset="0" class="info-item">
                <span>管理处：</span> <span>{{ form['deptName'] }}</span>
              </el-col>
              <el-col :span="12" :offset="0" class="info-item">
                <span>路段：</span> <span>{{ form['maintenanceSectionName'] }}</span>
              </el-col>
              <el-col :span="12" :offset="0" class="info-item">
                <span>巡查车：</span> <span>{{ form['carNum'] }}</span>
              </el-col>
              <el-col :span="12" :offset="0" class="info-item">
                <span>巡查人：</span> <span>{{ form['personnelName'] }}</span>
              </el-col>
            </el-row>

            <el-table :data="form.list" style="width: 100%" height="17vh">
              <el-table-column prop="diseaseName" label="事件名称" width="80" show-overflow-tooltip />
              <el-table-column prop="dealTypeName" label="事件类型" width="80" show-overflow-tooltip />
              <el-table-column prop="diseaseDesc" label="描述" min-width="120" show-overflow-tooltip />
            </el-table>
          </div>
        </div>
      </template>
    </section>

    <VideoPlay v-if="showVideo" :left="vLeft" :top="vTop" @close="showVideo = false" :videoUrl="form['videoUrl']">
    </VideoPlay>
  </div>
</template>

<script>
import VideoPlay from './videoPlay.vue';
import { isBigScreen } from '../../util/utils';

export default {
  name: 'MapDetail',
  components: {
    VideoPlay,
  },
  props: {
    top: {
      type: [Number, String],
      default: 100
    },
    left: {
      type: [Number, String],
      default: 600
    },
    title: {
      type: String,
      default: '详情'
    },
    form: {
      type: Object,
      default: () => { }
    }
  },
  data() {
    return {
      isBig: isBigScreen(),
      formList: [
        {
          name: '巡查车辆车牌号：',
          prop: 'carNum',
          type: 1,
        },
        {
          name: '开始巡查时间：',
          prop: 'startTime',
          type: 1,
        },
        {
          name: '巡查状态：',
          prop: 'status',
          type: 2,
        },
        {
          name: '巡查人：',
          prop: 'personnelName',
          type: 1,
        },
        {
          name: '巡查里程：',
          prop: 'patrolMileage',
          type: 1,
          unit: 'km'
        },
        {
          name: '本次巡查发现的养护事件数量：',
          prop: 'diseaseNum',
          type: 1,
        },
        {
          name: '巡查车类型：',
          prop: 'catType',
          type: 2,
        },
        {
          name: '巡查人电话：',
          prop: 'phoneNumber',
          type: 1,
        },
        {
          name: '视屏：',
          prop: 'videoUrl',
          type: 3,
        },
        {
          name: '查询轨迹：',
          prop: '',
          type: 4,
        },
      ],
      showVideo: false,
    }
  },
  computed: {
    // 计算属性 
    vTop() {
      let vHeight = window.innerHeight || document.documentElement.clientHeight;
      if (vHeight > this.top + 500) {
        return this.top + 250;
      }
      return this.top;
    },
    vLeft() {
      let vWidth = window.innerWidth || document.documentElement.clientWidth;
      let vHeight = window.innerHeight || document.documentElement.clientHeight;
      let left = this.left;
      if (vWidth > this.left + 500) {
        left = this.left + 350;
      }
      if (vHeight > this.top + 500) {
        left = this.left + 300;
      }
      return left;
    }
  },
  methods: {
    onClose() {
      this.$emit('close');
    },
    playVideo() {
      this.showVideo = !this.showVideo;
    },
    onListClick() {
      this.$emit('click', this.form);
    },
  }
}
</script>

<style lang="scss" scoped>
@import "@/assets/styles/utils.scss";

.normal-width {
  width: vwpx(640px);
  min-height: vwpx(560px);
  max-height: vwpx(640px);
}

.cat-width {
  width: 80vh;
  height: auto;
  min-height: vwpx(600px);
  max-height: vwpx(680px);
}

.map-detail {
  // width: vwpx(640px);
  // min-height: vwpx(560px);
  // max-height: vwpx(640px);
  background: rgba(4, 17, 48, 0.8);
  box-shadow: inset 0px 0px 10px 0px #3662EC;
  border-radius: vwpx(20px);
  border: vwpx(2px) solid #0687FF;
  position: fixed;
  z-index: 100;
  overflow: hidden;

  .header {
    width: 100%;
    height: vwpx(84px);
    background: linear-gradient(90deg, rgba(6, 97, 255, 0.312) 0%, rgba(6, 97, 255, 0) 100%);
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 vwpx(20px);

    .header-title {
      display: flex;
      align-items: center;
      color: #ffffff;
      font-size: vwpx(32px);

      span:first-child {
        margin-right: vwpx(20px);
        width: vwpx(6px);
        height: vwpx(40px);
        background: #F2AF4A;
      }
    }

    .close {
      margin-left: auto;
      font-size: vwpx(36px);
      color: #ffffff;
      cursor: pointer;
    }
  }

  .body {
    width: 100%;
    // height: calc(100% - 42px);
    height: 100%;
    padding: vwpx(10px) vwpx(20px);
    box-sizing: border-box;
    overflow: hidden;

    .list {
      display: flex;
      align-items: center;
      margin-bottom: vwpx(10px);

      .list-name {
        font-family: Microsoft YaHei, Microsoft YaHei;
        font-weight: 400;
        font-size: vwpx(28px);
        color: #FFFFFF;
      }

      .list-value {
        font-family: Microsoft YaHei, Microsoft YaHei;
        font-weight: 400;
        font-size: vwpx(28px);
        color: #409DFF;
      }
    }

    .cat-type-2 {
      width: 100%;
      height: 100%;
      display: flex;

      .video-content {
        flex: 1;
        height: 100%;

        /* 隐藏当前时间和剩余时间 */
        video::-webkit-media-controls-current-time-display,
        video::-webkit-media-controls-time-remaining-display {
          display: none;
        }

        /* 隐藏三个点菜单按钮 */
        video::-webkit-media-controls-overflow-button {
          display: none !important;
        }
      }

      .info {
        flex: 1;
        height: 100%;
        padding: 0 vwpx(20px);

        .info-item {
          font-family: Microsoft YaHei, Microsoft YaHei;
          font-weight: 400;
          font-size: vwpx(28px);
          color: #FFFFFF;
        }

        ::v-deep .el-table {
          background-color: unset;
          border: unset;
          overflow-x: hidden;

          &::-webkit-scrollbar {
            width: vwpx(12px);
            height: vwpx(12px);
          }

          &::-webkit-scrollbar-thumb {
            background-color: rgba(1, 102, 254, 0.3);
            border-radius: 2px;
          }

          &::-webkit-scrollbar-track {
            background-color: rgba(1, 102, 254, 0.2);
          }

          &::before {
            background-color: unset;
          }

          tr {
            background-color: unset;
          }

          td {
            color: #ffffff;
          }

          th.is-leaf {
            border-bottom: none;
          }

          thead {
            background-color: unset;
            color: #018cb3;
          }

          .cell {
            line-height: unset !important;
          }

          .el-table__header-wrapper tr th {
            background-color: rgba(2, 43, 91, 1);
            color: #018cb3 !important;
          }

          tbody {
            background-color: unset;
            border: none;

            .el-table__row {
              border-bottom: none !important;
              cursor: pointer;

              .el-table__cell {
                border-bottom: 1px solid rgba(2, 57, 128, 1);
                padding: 0;
                cursor: pointer;
              }
            }
          }

          .el-table__body tr:hover>td {
            background-color: unset;
          }

          .el-table__body tr.current-row>td.el-table__cell {
            background-color: rgba(0, 115, 232, 0);

          }

          .el-table__inner-wrapper::before {
            background-color: unset;
          }

          th.el-table__cell {
            background-color: unset;
            color: #ffffff;
          }

          .el-table__body-wrapper {
            &::-webkit-scrollbar {
              // 整个滚动条
              width: 0; // 纵向滚动条的宽度
              background: rgba(213, 215, 220, 0.3);
              border: none;
            }

            &::-webkit-scrollbar-track {
              // 滚动条轨道
              border: none;
            }
          }
        }
      }
    }
  }
}
</style>