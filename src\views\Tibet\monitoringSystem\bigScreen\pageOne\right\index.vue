<template>
  <div class="body-right">
    <Top />
    <Mid />
    <Bottom />
  </div>
</template>

<script>
import Top from '../right/Top.vue'
import Mid from '../right/Mid.vue'
import Bottom from '../right/Bottom.vue'

export default {
  name: 'Right-index',
  props: {},
  components: { Top, Mid, Bottom },
  data() {
    return {}
  },
  created() { },
  methods: {},
  computed: {},
  watch: {},
}
</script>

<style lang="scss" scoped>
.body-right {
  position: relative;
  z-index: 999;
  height: 100%;
  width: 24.5%;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}
</style>