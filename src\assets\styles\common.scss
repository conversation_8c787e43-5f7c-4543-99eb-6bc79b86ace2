@import "@/assets/styles/utils.scss";

$border-color: #0166FE;
$bg-color: rgba(1, 102, 254, 0.2);

.one-map {
  ::-webkit-scrollbar-thumb {
    background-color: rgba(1, 102, 254, 0.4);
  }

  ::-webkit-scrollbar-track {
    background-color: rgba(0, 35, 94, .6);
  }
  
  color: #ffffff;
  background-color: unset !important;

  ::v-deep .el-form {
    .el-form-item__label {
      color: #ffffff;
    }
  }

  ::v-deep .el-input {
    .el-input__inner {
      background-color: $bg-color;
      border: 1px solid $border-color;
      color: #ffffff;
    }

    .el-input__inner::placeholder {
      color: #BBBBBB;
    }

    .el-input-group__append {
      background-color: $bg-color;
      border: 1px solid $border-color;
      color: #ffffff;
      border-left: none;
      padding: 0 10px;
      cursor: pointer;
    }
  }

  ::v-deep .el-input-number {

    .el-input-number__increase,
    .el-input-number__decrease {
      background-color: $bg-color;
      color: #ffffff;
    }
  }

  ::v-deep .el-tabs {
    .el-tabs__nav {
      border: 1px solid $border-color;
    }

    .el-tabs__header {
      border-bottom: 1px solid $border-color;
      // border:none;
      background-color: unset;
      box-shadow: unset;
      -webkit-box-shadow: unset;
      padding-left: 0px;
    }

    .el-tabs__content {
      border: none;
      background-color: unset;
    }

    .el-tabs__item.is-active {
      border-bottom: 1px solid $border-color;
      /* 选中状态下的底部边框样式 */
    }

    .el-tabs__item {
      border-left: 1px solid $border-color;
      color: #ffffff;
    }

    .is-active {
      color: #1890ff !important;
    }
  }

  ::v-deep .vue-treeselect {
    .vue-treeselect__control {
      background-color: rgba(17, 44, 96, 0.6);
      color: #ffffff;
      border: 1px solid $border-color;
    }

    .vue-treeselect__single-value {
      color: #ffffff;
    }
  }

  ::v-deep .el-textarea.is-disabled .el-textarea__inner {
    background-color: rgba(1, 102, 254, 0.2);
    border-color: #0166fe;
  }

  ::v-deep .el-upload,
  .el-upload--picture-card {
    background-color: $bg-color;
    color: #ffffff;

    .el-icon-plus {
      color: #ffffff;
    }
  }

  ::v-deep .route-road {

    .inputStyle {
      background-color: $bg-color !important;
      border: 1px solid $border-color;
      color: #ffffff;
    }
  }


  ::v-deep .el-table {
    background: unset;
    border: unset;

    &::before {
      background-color: unset;
    }

    &::after {
      background-color: unset;
    }

    tr {
      background-color: unset;
    }

    tr:nth-child(even) {
      background: rgba(86, 145, 255, 0);
      color: #ffffff;
    }

    td {
      color: #ffffff;
    }

    td,
    th.is-leaf {
      border: 1px solid rgba(1, 102, 254, 0.4);
    }

    .el-table__header-wrapper tr th {
      background-color: rgba(1, 102, 254, 0.2);
      color: #ffffff !important;
      font-size: 14px;
    }

    tbody {
      background-color: unset;
      border: none;
      cursor: pointer;
    }

    .el-table__body tr:hover>td {
      background-color: unset;
    }

    .el-table__body tr.current-row>td {
      background-color: rgba(1, 102, 254, 0.2) !important;
    }

    .el-table__body tr.current-row:hover {
      background-color: rgba(1, 102, 254, 0.2);
    }
  }

  ::v-deep .el-pagination {
    .el-pagination__total {
      color: #ffffff;
    }

    .btn-prev,
    .btn-next {
      background-color: rgba(255, 255, 255, 0.1);
      border: 1px solid rgba(1, 102, 254, 0.6);
      color: #ffffff;
    }

    .el-pager {
      li {
        background-color: rgba(255, 255, 255, 0.1);
        border: 1px solid rgba(1, 102, 254, 0.6);
        color: #ffffff;
      }

      li:not(.disabled).active {
        background-color: rgba(1, 102, 254, 0.8);
        border: 1px solid rgba(1, 102, 254, 0.6);
        color: #ffffff;
      }
    }
  }

  ::v-deep .pagination-container {
    background-color: unset;
  }


  @media (min-width: 2560px) and (min-height: 1600px) {
    ::v-deep .el-form-item--mini.el-form-item {
      margin-bottom: vwpx(36px);

       .el-form-item__label {
        font-size: vwpx(28px);
        width: vwpx(400px) !important;
      }
  
       .el-form-item__content {
        font-size: vwpx(28px);
        margin-left:vwpx(400px) !important;
      }


      

      .el-radio {
        .el-radio__inner {
          width: vwpx(28px);
          height: vwpx(28px);
        }

        .el-radio__label {
          font-size: vwpx(28px);
          padding-left: vwpx(20px);
        }
      }

      .el-form-item__label {
        line-height: vwpx(56px);
      }

      .el-input--mini {
        font-size: vwpx(24px);
  
        .el-input__inner {
          height: vwpx(56px);
          line-height: vwpx(56px);
        }
  
        .el-input__icon {
          line-height: vwpx(56px);
        }
  
        .el-icon-sort {
          line-height: vwpx(56px) !important;
        }
      }
    }

    .onemap-radio-col{
      ::v-deep .el-form-item--mini.el-form-item{
        .el-form-item__label {
          width: vwpx(80px) !important;
        }
    
         .el-form-item__content {
          margin-left:vwpx(80px) !important;
        }
      }
    
    }


    .archives .archives-left .archives-left-head h5{

      // vwpx(80px)
      font-size: vwpx(28px)
    }

    .el-divider--horizontal {
      margin: vwpx(48px) 0 !important;

      .el-divider__text {
        font-size: vwpx(28px);
      }

    }

    ::v-deep .el-input-number--mini .el-input-number__increase,
    ::v-deep .el-input-number--mini .el-input-number__decrease {
      width: vwpx(56px);
      font-size: vwpx(24px);
      line-height: vwpx(52px)
    }

    
   
    ::v-deep .el-tabs__item{
      font-size: vwpx(28px);
      height: vwpx(80px);
      line-height: vwpx(80px);
    }


    ::v-deep .el-select .el-input .el-select__caret {
      font-size: vwpx(28px);
    }


    ::v-deep .el-upload--picture-card {
      width: vwpx(296px);
      height: vwpx(296px);
      line-height: vwpx(296px);
    }

    ::v-deep .el-upload__tip {
      font-size: vwpx(24px);
    }

    ::v-deep .el-upload-list--picture-card .el-upload-list__item{
      width: vwpx(296px);
    height: vwpx(296px);
    }
  }
}