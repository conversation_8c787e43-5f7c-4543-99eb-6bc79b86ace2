<template>
	<div>
		<el-dialog
			:title="title"
			:visible.sync="showTable"
			width="70%"
			:before-close="handleClose"
			:close-on-click-modal="false"
		>
			<component :is="componentName" :row="row" />
		</el-dialog>
	</div>
</template>

<script>
import { listStatic } from '@/api/baseData/bridge/statistics/index'
import Subgrade from './Subgrade.vue'
import SideSlope from './SideSlope.vue'
import Ditch from './Ditch.vue'
import Shoulder from './Shoulder.vue'
import Wall from './Wall.vue'

export default {
	name: 'subgrade-statistics-detail-table',
	props: {
		showTable: { type: Boolean, default: false },
		row: { type: undefined, default: '' },
	},
	dicts: [],
	components: { Subgrade, SideSlope, Ditch, Shoulder, Wall },
	data() {
		return {
			title: '',
			loading: false,
			queryParams: {
				pageNum: 1,
				pageSize: 20,
			},
			total: 0,
			tableData: [],
			componentName: '',
		}
	},
	created() {
		this.getTitle()
		this.getComponentName()
	},
	methods: {
		getTitle() {
			if (!this.row) return
			if (this.row.maintenanceSectionName == '合计') {
				this.title = '合计'
			} else {
				this.title =
					(this.row.routeCode ? '路线编码(' + this.row.routeCode + ')-' : '') +
					(this.row.typeName || this.row.assetTypeName) +
					'列表'
			}
		},
		getComponentName() {
			if (!this.row) return
			switch (this.row.typeId) {
				case '2':
					this.componentName = 'SideSlope'
					break
				case '3':
					this.componentName = 'Subgrade'
					break
				case '4':
					this.componentName = 'Ditch'
					break
				case '5':
					this.componentName = 'Shoulder'
					break
				case '183':
					this.componentName = 'Wall'
					break
				// case '185':
				//   this.componentName = 'Shoulder'
				//   break
			}
		},
		handleClose() {
			this.$emit('close')
		},
	},
	computed: {},
	watch: {},
}
</script>

<style lang="scss" scoped></style>
