<template>
  <div class="bridge-special">
    <div class="special-c" v-loading="loading" element-loading-background="rgba(0, 0, 0, 0.3)">
      <!-- <MapView :padding="[50, 50, 50, -250]" ref="mpRef"></MapView> -->
      <AMap ref="ampRef" v-if="!loading" @click="onMapClick"></AMap>
    </div>
    <section class="special-l">
      <CockpitCard title="监测结构物统计" :w="isBig ? '10vw' : '20vw'" h="calc(10vh - 5px)" :class="isBig ? 'mb-2' : 'mb-3'"
        :isDtl="false">
        <fitness-index type="2" @click="onFClick" top="3vh" :isAct="true"></fitness-index>
      </CockpitCard>
      <CockpitCard title="分布情况" :w="isBig ? '10vw' : '20vw'" h="calc(40vh)" :class="isBig ? 'mb-2' : 'mb-3'"
        :isDtl="false">
        <Echarts :option="dbOption" v-if="dbOption" height="40vh" key="jkKey" v-loading="fLoading"
          element-loading-background="rgba(0, 0, 0, 0.4)" @click="onEChartsClick"/>
      </CockpitCard>
      <!-- <CockpitCard title="结构物基本信息" :w="isBig ? '10vw' : '20vw'" h="calc(19vh - 5px)" :isDtl="false">
        <div class="base-info">
          <vue-seamless-scroll :data="structureData" :class-option="optionHover" class="seamless-warp">
            <div v-for="item in structureData" :key="item.id">
              <div class="info-item">
                <img :src="item.structureImage" class="img" />
                <div class="info-data">
                  <span>{{ item.structureName }}</span>
                  <span>主桥上部结构类型：{{ item.ustructType }}</span>
                  <span>技术状况：{{ item.level }}</span>
                  <span>按跨径分类：{{ item.structureType }}</span>
                </div>
                <img src="@/assets/cockpit/addres-info.png" class="img-icon" @click="setPosition(item)">
              </div>
              <div class="divider"></div>
            </div>
          </vue-seamless-scroll>
        </div>
      </CockpitCard> -->
      <CockpitCard title="结构物清单" :w="isBig ? '10vw' : '20vw'" :h="isBig ? '28vh' : 'calc(28vh - 5px)'" :class="isBig ? 'mb-2' : 'mb-3'"
        :isDtl="false">
        <!-- <Echarts :option="levelOption" v-if="levelOption" height="25vh" key="jkKey" /> -->
         <StructureList ref="struRef" @row-click="onTableClick"/>
          <template slot="more">
          <div class="stru-search">
            <el-input v-model="keywords" clearable class="stru-input" placeholder="请输入名称" :size="isBig ? 'large' : 'small'" />
            <el-button class="search-btn" icon="el-icon-search" @click="onStruSearch"></el-button>
          </div>
         </template>
      </CockpitCard>
    </section>
    <section class="special-r">
      <div class="info">
        <!-- <div>
          <CockpitCard title="健康度等级" :w="isBig ? '9vw' : '18vw'" h="calc(26vh - 5px)" :class="isBig ? 'mb-2' : 'mb-3'"
            :isDtl="false">
            <Echarts :option="levelOption" v-if="levelOption" height="25vh" key="jkKey" />
          </CockpitCard>
          <CockpitCard title="报警状态" :w="isBig ? '9vw' : '18vw'" h="calc(26vh - 5px)" :class="isBig ? 'mb-2' : 'mb-3'"
            :isDtl="false">
            <div class="warning-status">
              <div class="warning-status-list" v-for="(item, index) in warningList" :key="'warning' + index">
                <img :src="item.img" class="img" />
                <span>{{ item.name }}</span>
              </div>
            </div>
            <div class="warning-content">
              <vue-seamless-scroll :data="warningData" :class-option="optionHover" class="seamless-warp">
                <div class="warning-content-list" v-for="(item, index) in warningData" :key="index">
                  <span class="warning-content-id">{{ index + 1 }}</span>
                  <span class="warning-content-val">{{ item.alertContent }}</span>
                </div>
              </vue-seamless-scroll>
            </div>
          </CockpitCard>
          <CockpitCard title="监测类别" :w="isBig ? '9vw' : '18vw'" h="calc(26vh - 5px)" :isDtl="false">
            <div class="monitoring-category">
              <div class="monitoring-category-list" v-for="(item, index) in categoryList" :key="'category' + index">
                <img :src="item.img" class="img" />
                <span class="name" :style="{ fontSize: isBig ? '0.4vw' : '0.8vw' }">{{ item.name }}</span>
                <span class="val" :style="{ fontSize: isBig ? '0.4vw' : '0.8vw' }">({{ item.val }})</span>
              </div>
            </div>
          </CockpitCard>
        </div> -->
        <div class="ml-2">
          <CockpitCard title="监测类别" :w="isBig ? '9vw' : '18vw'" h="calc(26vh - 5px)" :isDtl="false">
            <div class="monitoring-category">
              <div class="monitoring-category-list" v-for="(item, index) in categoryList" :key="'category' + index">
                <img :src="item.img" class="img" />
                <span class="name" :style="{ fontSize: isBig ? '0.4vw' : '0.8vw' }">{{ item.name }}</span>
                <span class="val" :style="{ fontSize: isBig ? '0.4vw' : '0.8vw' }">({{ item.val }})</span>
              </div>
            </div>
          </CockpitCard>
          <CockpitCard title="传感器运营状态" :w="isBig ? '9vw' : '18vw'" h="calc(26vh - 5px)" :class="isBig ? 'mb-2' : 'mb-3'"
            :isDtl="false">
            <Echarts :option="cgOption" v-if="cgOption" height="25vh" key="jkKey" />
          </CockpitCard>
          <CockpitCard title="报警状态" :w="isBig ? '9vw' : '18vw'" :h="isBig ? 'calc(27vh + 5px)' : 'calc(28vh - 5px)'" :class="isBig ? 'mb-2' : 'mb-3'"
            :isDtl="false">
            <div class="warning-status">
              <div class="warning-status-list" v-for="(item, index) in warningList" :key="'warning' + index" @click="onLevelClick(item, index)">
                <img :src="item.img" class="img" />
                <span :style="{ color: warningIndex === index ? '#42ABFF' : '' }">{{ item.name }}</span>
              </div>
            </div>
            <div class="warning-content">
              <vue-seamless-scroll :data="warningData" :class-option="optionHover" class="seamless-warp">
                <div class="warning-content-list" v-for="(item, index) in warningData" :key="index" @click="onWaringClick(item)">
                  <span class="warning-content-id">
                    <!-- {{ index + 1 }} -->
                    <img src="@/assets/cockpit/health/level-1.png" class="img" v-if="item.alertLevel == '一级'" />
                    <img src="@/assets/cockpit/health/level-2.png" class="img" v-if="item.alertLevel == '二级'" />
                    <img src="@/assets/cockpit/health/level-3.png" class="img" v-if="item.alertLevel == '三级'" />
                  </span>
                  <span class="warning-content-val">{{ item.alertContent }}</span>
                </div>
              </vue-seamless-scroll>
            </div>
          </CockpitCard>
          <!-- <CockpitCard title="技术状况" :w="isBig ? '9vw' : '18vw'" h="calc(26vh - 5px)" :class="isBig ? 'mb-2' : 'mb-3'"
            :isDtl="false">
            <Echarts :option="jsOption" v-if="jsOption" height="25vh" key="jkKey" />
          </CockpitCard>
          <CockpitCard title="传感器运营状态" :w="isBig ? '9vw' : '18vw'" h="calc(26vh - 5px)" :class="isBig ? 'mb-2' : 'mb-3'"
            :isDtl="false">
            <Echarts :option="cgOption" v-if="cgOption" height="25vh" key="jkKey" />
          </CockpitCard>
          <CockpitCard title="监测数据统计" :w="isBig ? '9vw' : '18vw'" h="calc(26vh - 5px)" :isDtl="false">
            <Echarts :option="mlOption" v-if="mlOption" height="25vh" key="jkKey" />
            <div class="jc-data-statistics">
              <div class="statistics-info">
                <span class="info-today"></span>
                <span class="info-val">今日数据: <strong>{{ todayCount }}</strong></span>
                <span class="info-unit">条</span>
              </div>

              <div class="statistics-info">
                <span class="info-yesterday"></span>
                <span class="info-val">昨日数据: <strong>{{ ytodayCount }}</strong></span>
                <span class="info-unit">条</span>
              </div>
            </div>
          </CockpitCard> -->
        </div>
      </div>
    </section>
  </div>
</template>

<script>
import * as echarts from 'echarts'
import { isBigScreen } from '../../util/utils';
import CockpitCard from '../cockpitCard.vue';
import Echarts from '../echarts/echarts.vue';
import Tables from "../tables.vue";
import MapView from '../mapView.vue';
import AMap from '@/components/Map/aMap.vue'
import FitnessIndex from '../fitness/index.vue';
import StructureList from './component/structureList.vue'
import { getAllStructureInfoGroupByDomain, getDataCount, getSensorCountKindByMonitorType, getNotDealRecordInformation } from "@/api/cockpit/index";
import { getWarningList } from '@/api/cockpit/health'
import { Loading } from 'element-ui';
import cache from "@/plugins/cache";

export default {
  name: 'Special',
  components: {
    CockpitCard,
    Echarts,
    Tables,
    MapView,
    FitnessIndex,
    AMap,
    StructureList
  },
  data() {
    return {
      map: null,
      isBig: isBigScreen(),
      jsOption: null,
      mlOption: null,
      dbOption: null,
      levelOption: null, // 桥梁技术状况评定
      cgOption: null, // 传感器运营状态
      todayCount: 0,
      structureData: [],
      ytodayCount: 0,
      warningData: [],
      levelList: [],
      warningIndex: null,
      warningList: [
        {
          name: '一级',
          color: '#FA5151',
          img: require('@/assets/cockpit/health/level-1.png'),
        },
        {
          name: '二级',
          color: '#FF9803',
          img: require('@/assets/cockpit/health/level-2.png'),
        },
        {
          name: '三级',
          color: '#F9E21F',
          img: require('@/assets/cockpit/health/level-3.png'),
        },
      ],
      categoryList: [
        {
          name: '温湿度',
          val: 0,
          img: require('@/assets/cockpit/health/humiture.png'),
        },
        {
          name: '衬砌变形',
          val: 0,
          img: require('@/assets/cockpit/health/lining.png'),
        },
        {
          name: '裂缝',
          val: 0,
          img: require('@/assets/cockpit/health/crack.png'),
        },
        {
          name: '应变',
          val: 0,
          img: require('@/assets/cockpit/health/strain.png'),
        },
        {
          name: '倾斜',
          val: 0,
          img: require('@/assets/cockpit/health/tilt.png'),
        },
        {
          name: '体外预应力',
          val: 280,
          img: require('@/assets/cockpit/health/prestress.png'),
        },
        {
          name: '位移',
          val: 0,
          img: require('@/assets/cockpit/health/displacement.png'),
        },
        {
          name: '振动',
          val: 0,
          img: require('@/assets/cockpit/health/vibrate.png'),
        },
        {
          name: '雨量',
          val: 0,
          img: require('@/assets/cockpit/health/hyetal.png'),
        },
        {
          name: '结构温度',
          val: 0,
          img: require('@/assets/cockpit/health/temperature.png'),
        },
        {
          name: '索力',
          val: 0,
          img: require('@/assets/cockpit/health/cable-force.png'),
        },
        {
          name: '转角',
          val: 0,
          img: require('@/assets/cockpit/health/corner.png'),
        },
        {
          name: '地震',
          val: 0,
          img: require('@/assets/cockpit/health/earthquake.png'),
        },
        {
          name: '车辆荷载',
          val: 0,
          img: require('@/assets/cockpit/health/vehicle-load.png'),
        },
        {
          name: '风速、风向',
          val: 0,
          img: require('@/assets/cockpit/health/wind.png'),
        },
      ],
      name: '桥梁',
      obj: {
        "deptIds": [],
        "id": "1821371445660356609",
        icon: require('@/assets/cockpit/bridge.png'),
        paramsDTO: {
          "precisionParams": {
            "type": "桥梁",
            "customParams": [
              {
                "col": "code",
                "compareSymbol": "=",
                "type": "2",
                "colValue": ""
              },
              {
                "col": "content",
                "compareSymbol": "like",
                "type": "2",
                "colValue": ""
              },
              {
                "col": "management_maintenance_name",
                "compareSymbol": "like",
                "type": "2",
                "colValue": ""
              },
              {
                "col": "maintenance_section_name",
                "compareSymbol": "like",
                "type": "2",
                "colValue": ""
              },
              {
                "col": "route_code",
                "compareSymbol": "=",
                "type": "2",
                "colValue": ""
              },
              {
                "col": "direction",
                "compareSymbol": "like",
                "type": "2",
                "colValue": ""
              },
              {
                "col": "bridge_length",
                "compareSymbol": "=",
                "dataType": "2",
                "type": "2",
                "colValue": ""
              },
              {
                "col": "zhuang_hao",
                "compareSymbol": "=",
                "dataType": "2",
                "type": "2",
                "colValue": ""
              }
            ]
          },
          "ks": ""
        }
      },
      queryList: [
        {
          "deptIds": [],
          "id": "1821371445660356609",
          icon: require('@/assets/cockpit/bridge.png'),
          paramsDTO: {
            "precisionParams": {
              "type": "桥梁",
              "customParams": [
                {
                  "col": "code",
                  "compareSymbol": "=",
                  "type": "2",
                  "colValue": ""
                },
                {
                  "col": "content",
                  "compareSymbol": "like",
                  "type": "2",
                  "colValue": ""
                },
                {
                  "col": "management_maintenance_name",
                  "compareSymbol": "like",
                  "type": "2",
                  "colValue": ""
                },
                {
                  "col": "maintenance_section_name",
                  "compareSymbol": "like",
                  "type": "2",
                  "colValue": ""
                },
                {
                  "col": "route_code",
                  "compareSymbol": "=",
                  "type": "2",
                  "colValue": ""
                },
                {
                  "col": "direction",
                  "compareSymbol": "like",
                  "type": "2",
                  "colValue": ""
                },
                {
                  "col": "bridge_length",
                  "compareSymbol": "=",
                  "dataType": "2",
                  "type": "2",
                  "colValue": ""
                },
                {
                  "col": "zhuang_hao",
                  "compareSymbol": "=",
                  "dataType": "2",
                  "type": "2",
                  "colValue": ""
                }
              ]
            },
            "ks": ""
          }
        },
        {
          "deptIds": [],
          "id": "1821371445660356609",
          icon: require('@/assets/cockpit/slope.png'),
          paramsDTO: {
            "precisionParams": {
              "type": "边坡",
              "customParams": [
                {
                  "col": "code",
                  "compareSymbol": "=",
                  "type": "2",
                  "colValue": ""
                },
                {
                  "col": "content",
                  "compareSymbol": "like",
                  "type": "2",
                  "colValue": ""
                },
                {
                  "col": "management_maintenance_name",
                  "compareSymbol": "like",
                  "type": "2",
                  "colValue": ""
                },
                {
                  "col": "maintenance_section_name",
                  "compareSymbol": "like",
                  "type": "2",
                  "colValue": ""
                },
                {
                  "col": "route_code",
                  "compareSymbol": "=",
                  "type": "2",
                  "colValue": ""
                },
                {
                  "col": "direction",
                  "compareSymbol": "like",
                  "type": "2",
                  "colValue": ""
                },
                {
                  "col": "bridge_length",
                  "compareSymbol": "=",
                  "dataType": "2",
                  "type": "2",
                  "colValue": ""
                },
                {
                  "col": "zhuang_hao",
                  "compareSymbol": "=",
                  "dataType": "2",
                  "type": "2",
                  "colValue": ""
                }
              ]
            },
            "ks": ""
          }
        },
        {
          "deptIds": [],
          "id": "1821371445660356609",
          icon: require('@/assets/cockpit/tunnel-icon.png'),
          url: '/oneMap/layerData/getDataInfo',
          paramsDTO: {
            "precisionParams": {
              "type": "隧道",
              "customParams": [
                {
                  "col": "code",
                  "compareSymbol": "=",
                  "type": "2",
                  "colValue": ""
                },
                {
                  "col": "content",
                  "compareSymbol": "like",
                  "type": "2",
                  "colValue": ""
                },
                {
                  "col": "management_maintenance_name",
                  "compareSymbol": "like",
                  "type": "2",
                  "colValue": ""
                },
                {
                  "col": "maintenance_section_name",
                  "compareSymbol": "like",
                  "type": "2",
                  "colValue": ""
                },
                {
                  "col": "route_code",
                  "compareSymbol": "=",
                  "type": "2",
                  "colValue": ""
                },
                {
                  "col": "direction",
                  "compareSymbol": "like",
                  "type": "2",
                  "colValue": ""
                },
                {
                  "col": "bridge_length",
                  "compareSymbol": "=",
                  "dataType": "2",
                  "type": "2",
                  "colValue": ""
                },
                {
                  "col": "zhuang_hao",
                  "compareSymbol": "=",
                  "dataType": "2",
                  "type": "2",
                  "colValue": ""
                }
              ]
            },
            "ks": ""
          }
        },
      ],
      loading: true,
      fLoading: false,
      keywords: ''
    }
  },
  computed: {
    // 滚动设置
    optionHover() {
      return {
        hoverStop: true,
        step: this.isBig ? 1.0 : 0.2, // 数值越大速度滚动越快
        limitMoveNum: 2, // 开始无缝滚动的数据量 this.dataList.length
        direction: 1, // 0向下 1向上 2向左 3向右
        openWatch: true, // 开启数据实时监控刷新dom
        singleHeight: 0, // 单步运动停止的高度(默认值0是无缝不停止的滚动) direction => 0/1
        singleWidth: 0, // 单步运动停止的宽度(默认值0是无缝不停止的滚动) direction => 2/3
        waitTime: 1000, // 单步运动停止的时间(默认值1000ms)
      };
    },
  },
  async mounted() {
    setTimeout(() => {
      this.$nextTick(() => {
        this.loading = false;
        this.initMap();
      })
    }, 500)
    this.initBarsCharts();
    this.initDbBarECharts();
    // 获取健康度等级 图标数据
    let leveData = [99, 99, 99];
    let levelXData = ["等级Ⅰ", "等级Ⅱ", "等级Ⅲ"];
    let levelColorObj = {
      barColor0: 'rgba(0,185,254,0.1)',
      barColor6: 'rgba(0,185,254,0.6)',
      barColor: 'rgba(0,185,254,1)',
      barTColor: 'rgba(132,222,255,1)',
    }
    this.levelOption = this.initBEcharts(levelXData, leveData, levelColorObj);
    // 获取技术状况 图标数据
    let jsData = [99, 99, 99];
    let jsXData = ["1类", "2类", "3类"];
    let jsColorObj = {
      barColor0: 'rgba(2,255,131,0.1)',
      barColor6: 'rgba(2,255,131,0.6)',
      barColor: 'rgba(2,255,131,1)',
      barTColor: 'rgba(143,255,201,1)',
    }
    this.jsOption = this.initBEcharts(jsXData, jsData, jsColorObj);
    let data = await this.getDataInfo();
    this.initCGEcharts(data);
    this.monitoringCategory();
    this.getWarningList();
  },
  destroyed() {
    if (this.$refs.mpRef) {
      this.$refs.mpRef.removeVector('桥梁');
    }
  },
  methods: {
    onMapClick(data) {
      this.onEChartsClick(data);
    },
    onWaringClick(item) {
      let { longitude, latitude } = item;
      if(this.$refs.ampRef && longitude && latitude) {
        let shape = `POINT(${longitude} ${latitude})`;
        this.$refs.ampRef?.setCenter(shape, 12.2);
      }
    },
    onLevelClick(item, index) {
      if(this.warningIndex == index) {
        this.warningIndex = null;
        this.warningData = this.levelList
      } else {
        this.warningIndex = index
        this.warningData = this.levelList.filter(v=> v.alertLevel == item.name)
      }
    },
    async onFClick(data) {
      if (!data) return;
      this.name = data.name;
      let index = this.queryList.findIndex(v => v.paramsDTO.precisionParams.type == data.name);
      if (index != -1) {
        this.obj = this.queryList[index] || {};
        let loadingInstance = Loading.service({ fullscreen: true, background: 'rgba(0, 0, 0, 0.4)', spinner: 'el-icon-loading', });
        await this.$refs.ampRef?.removeMarker();
        setTimeout(() => {
          this.$nextTick(async () => {
            await this.$refs.ampRef?.setVector('健康监测', this.obj);
            loadingInstance.close();
          });
        }, 500)
      }
      this.initDbBarECharts(data.name);
      if (this.$refs.mpRef) {
        this.$refs.mpRef.removeVector('桥梁');
      }
    },
    getWarningList() {
      getNotDealRecordInformation().then(res => {
        if (res.code == 200 && res.result) {
          console.log('报警状态数据：', res.result);
          this.warningData = [];
          // this.warningData = [...res.result.三级预警, ...res.result.二级预警, ...res.result.一级预警]
          for (let key in res.result) {
            this.warningData = [...this.warningData, ...res.result[key]]
          }
          this.levelList = this.warningData || [];
        }
      });
    },
    setPosition(item) {
      window.mapConfig.setZoomAndCenter(14, [item.longitude, item.latitude]);
    },
    initMap() {
      this.$nextTick(() => {
        if (this.$refs.ampRef) {
          this.$refs.ampRef.setVector('健康监测', this.obj)
        }
      })
    },
    monitoringCategory() {
      getSensorCountKindByMonitorType().then(res => {
        if (res.code == 200) {
          for (let key in res.result) {
            this.categoryList.forEach(item => {
              if (item.name == key) {
                item.val = res.result[key]
              }
            })
          }
        }
      })
    },
    initBarsCharts() {
      getDataCount().then(res => {
        if (res.code == 200) {
          let xData = ["四月", "五月", "六月", "七月", "八月", "九月"];
          let data = JSON.parse(res.result.eachMonthCount)

          this.todayCount = (res.result.realTimeData / 100000000).toFixed(2) + ' 亿'
          this.ytodayCount = (res.result.yesterdayData / 100000000).toFixed(2) + ' 亿'

          let mlSColor = '#00FDFD';
          let mlEColor = 'rgba(0,253,253,0.1)';
          this.mlOption = this.initBarCharts(xData, data, mlSColor, mlEColor);
        }
      })
    },
    initBarCharts(xData = [], data = [], startColor = '', endColor = '', legend = []) {

      let option = {
        backgroundColor: "rgba(0,0,0,0)",
        grid: {
          left: "3%",
          right: "4%",
          top: "25%",
          bottom: "1%",
          containLabel: true,
        },
        legend: {
          show: legend.length > 0 ? true : false,
          data: legend,
          x: 'center',
          y: '2%',
          itemWidth: 10,
          itemHeight: 10,
          itemGap: 10,
          textStyle: {
            color: '#ffffff',
          },
        },
        xAxis: {
          data: xData,
          axisLine: {
            lineStyle: {
              color: "rgba(110,112,121,0.5)",
            },
          },
          axisLabel: {
            color: "#8B8C8C",
            fontSize: this.isBig ? 22 : 14,
            // rotate: 40,
            interval: false,
          },
        },
        yAxis: {
          name: "",
          nameTextStyle: {
            color: "#999999",
            fontSize: this.isBig ? 22 : 13,
          },
          axisLabel: {
            color: "#999999",
            fontSize: this.isBig ? 22 : 13,
            formatter: function (value) {
              return (value / 100000000) + ' 亿';
            }
          },
          splitLine: {
            show: true,
            lineStyle: {
              color: "rgba(110,112,121,0.5)",
            },
          },
          splitArea: {
            show: false,
          },
        },
        series: [
          {
            name: legend.length ? legend[0] : "",
            type: "bar",
            barWidth: this.isBig ? 15 : 10,
            itemStyle: {
              normal: {
                color: new echarts.graphic.LinearGradient(
                  0,
                  0,
                  0,
                  1,
                  [
                    {
                      offset: 0,
                      color: startColor,
                    },
                    {
                      offset: 0.8,
                      color: endColor,
                    },
                  ],
                  false
                ),
              },
            },
            data,
          },
        ],
      };
      return option;
    },
    initDbBarECharts(name = '桥梁') {
      this.dbOption = null;
      this.fLoading = true;
      // 初始化
      getAllStructureInfoGroupByDomain().then(res => {
        if (res.code == 200 && res.result) {
          let xData = []
          let bridgeData = []
          let tunnelData = []
          let slopeData = []
          let list = [];
          res.result.forEach(item => {
            xData.push(item.domainName);
            let structureTypeInfoList = item.structureTypeInfoList;
            let bridgeObj = structureTypeInfoList.find(v => v.structureType === '桥梁')
            bridgeData.push(bridgeObj?.structureTypeCount);
            let tunnelObj = structureTypeInfoList.find(v => v.structureType === '隧道')
            tunnelData.push(tunnelObj?.structureTypeCount);
            let slopeObj = structureTypeInfoList.find(v => v.structureType === '边坡')
            slopeData.push(slopeObj?.structureTypeCount);
            list = list.concat(item.structureNormalDataManageList);
          })
          this.structureData = list;
          let bridgeSeries = [{
            name: "桥梁数量",
            type: "bar",
            barWidth: this.isBig ? 20 : 10,
            itemStyle: {
              normal: {
                show: true,
                color: "#00B8FA",
                barBorderRadius: 0,
                borderWidth: 0,
                borderColor: "#333",
                color: new echarts.graphic.LinearGradient(1, 0, 0, 0, [
                  {
                    offset: 0,
                    color: "rgba(0,184,250,1)",
                  },
                  {
                    offset: 1,
                    color: "rgba(0,184,250,0)",
                  },
                ]),
              },
            },
            barGap: 0.5,
            barCategoryGap: "40%",
            data: bridgeData,
            events: {
              click: (params) => {
                console.log('121', params)
              }
            }
          }]
          let tunnelSeries = [{
            name: "隧道数量",
            type: "bar",
            barWidth: this.isBig ? 20 : 10,
            itemStyle: {
              normal: {
                show: true,
                color: "#9749FF",
                barBorderRadius: 0,
                borderWidth: 0,
                borderColor: "#333",
                color: new echarts.graphic.LinearGradient(1, 0, 0, 0, [
                  {
                    offset: 0,
                    color: "rgba(151,73,255,1)",
                  },
                  {
                    offset: 1,
                    color: "rgba(151,73,255,0)",
                  },
                ]),
              },
            },
            barGap: 0.5,
            barCategoryGap: "40%",
            data: tunnelData,
          }]
          let slopeSeries = [{
            name: "边坡数量",
            type: "bar",
            barWidth: this.isBig ? 20 : 10,
            itemStyle: {
              normal: {
                show: true,
                color: "#EE9433",
                barBorderRadius: 0,
                borderWidth: 0,
                borderColor: "#333",

                color: new echarts.graphic.LinearGradient(1, 0, 0, 0, [
                  {
                    offset: 0,
                    color: "rgba(238,148,51,1)",
                  },
                  {
                    offset: 1,
                    color: "rgba(238,148,51,0)",
                  },
                ]),
              },
            },
            barGap: 0.5,
            barCategoryGap: "40%",
            data: slopeData,
          }]
          let series = name.includes('桥梁') ? bridgeSeries : name.includes('边坡') ? slopeSeries : tunnelSeries;

          this.dbOption = {
            backgroundColor: "rgba(255,255,255,0)",
            legend: {
              show: true,
              x: "center",
              y: "2%",
              textStyle: {
                color: "#fff",
                fontSize: this.isBig ? 22 : 12,
              },
              data: ["桥梁数量", "隧道数量", "边坡数量"],
            },
            grid: {
              left: "3%",
              right: "7%",
              top: "10%",
              bottom: "3%",
              containLabel: true,
            },

            tooltip: {
              show: "true",
              trigger: "axis",
              axisPointer: {
                // 坐标轴指示器，坐标轴触发有效
                type: "shadow", // 默认为直线，可选为：'line' | 'shadow'
              },
            },
            xAxis: {
              type: "value",
              name: '个',
              nameTextStyle: {
                color: "#999999",
                algin: 'right',
                fontSize: this.isBig ? 25 : 14,
                padding: this.isBig ? [10, 0, 0, 2] : [6, 0, 0, -4],
                verticalAlign: 'top',
              },
              axisTick: {
                show: false,
              },
              axisLine: {
                show: true,
                lineStyle: {
                  color: "rgba(110,112,121,0.5)",
                },
              },
              axisLabel: {
                color: "#8B8C8C",
                fontSize: this.isBig ? 25 : 12,
              },
              splitLine: {
                show: true,
                lineStyle: {
                  color: "rgba(110,112,121,0.5)",
                },
              },
              splitArea: {
                show: false,
              },
            },
            yAxis: [
              {
                type: "category",
                axisTick: {
                  show: true,
                  length: -5,
                },
                axisLine: {
                  show: true,
                  lineStyle: {
                    color: "rgba(110,112,121,0.5)",
                  },
                },
                axisLabel: {
                  color: "#fff",
                  fontSize: this.isBig ? 22 : 12,
                },
                splitArea: {
                  show: false,
                },
                data: xData,
              },
              {
                type: "category",
                axisLine: {
                  show: false,
                },
                axisTick: {
                  show: false,
                },
                axisLabel: {
                  show: false,
                },
                splitArea: {
                  show: false,
                },
                splitLine: {
                  show: false,
                },
                data: xData,
              },
            ],
            series,
          };
        }
      }).finally(() => {
        this.fLoading = false;
      })
    },
    initBEcharts(xData, data, colorObj) {
      const sideData = data.map((item) => item + 1);

      let option = {
        backgroundColor: "rgba(0,0,0,0)",
        grid: {
          top: "15%",
          left: "3%",
          right: "3%",
          bottom: "2%",
          containLabel: true,
        },
        xAxis: {
          data: xData,
          //坐标轴
          axisLine: {
            lineStyle: {
              color: "rgba(110,112,121,0.5)",
            },
          },
          //坐标值标注
          axisLabel: {
            show: true,
            textStyle: {
              color: "#8B8C8C",
              fontSize: this.isBig ? 22 : 12,
            },
          },
        },
        yAxis: {
          type: "value",
          name: '个',
          nameTextStyle: {
            color: "#999999",
            algin: 'right',
            fontSize: this.isBig ? 25 : 14,
            padding: [0, 30, 0, 0],
          },
          //坐标轴
          axisLine: {
            show: false,
          },
          //坐标值标注
          axisLabel: {
            show: true,
            textStyle: {
              color: "#999999",
              fontSize: this.isBig ? 22 : 12,
            },
          },
          //分格线
          splitLine: {
            lineStyle: {
              color: "rgba(110,112,121,0.5)",
            },
          },
          splitArea: {
            show: false,
          },
          interval: 10,
        },
        series: [
          {
            name: "a",
            tooltip: {
              show: false,
            },
            label: {
              normal: {
                show: true,
                position: 'top',
                fontSize: this.isBig ? 24 : 16,
                color: '#fff',
                offset: [0, -10],
              },
            },
            type: "bar",
            barWidth: 24.5,
            itemStyle: {
              normal: {
                color: new echarts.graphic.LinearGradient(
                  0,
                  1,
                  0,
                  0,
                  [
                    {
                      offset: 0,
                      color: colorObj.barColor0, // 0% 处的颜色
                    },
                    {
                      offset: 0.6,
                      color: colorObj.barColor6, // 60% 处的颜色
                    },
                    {
                      offset: 1,
                      color: colorObj.barColor, // 100% 处的颜色
                    },
                  ],
                  false
                ),
              },
            },
            data: data,
            barGap: 0,
          },
          {
            type: "bar",
            barWidth: 8,
            itemStyle: {
              normal: {
                color: new echarts.graphic.LinearGradient(
                  0,
                  1,
                  0,
                  0,
                  [
                    {
                      offset: 0,
                      color: colorObj.barColor0, // 0% 处的颜色
                    },
                    {
                      offset: 0.6,
                      color: colorObj.barColor6, // 60% 处的颜色
                    },
                    {
                      offset: 1,
                      color: colorObj.barColor, // 100% 处的颜色
                    },
                  ],
                  false
                ),
              },
            },
            barGap: 0,
            data: sideData,
          },
          {
            name: "b",
            tooltip: {
              show: false,
            },
            type: "pictorialBar",
            itemStyle: {
              borderWidth: 1,
              borderColor: colorObj.barTColor,
              color: colorObj.barTColor,
            },

            symbol: "path://M 0,0 l 120,0 l -30,60 l -120,0 z",
            symbolSize: ["30", "12"],
            symbolOffset: ["0", "-8"],
            symbolRotate: -15,
            symbolPosition: "end",
            data: data,
            z: 3,
          },
        ],
      };
      return option;
    },
    initCGEcharts(data) {
      console.log('dddd', data)
      var dataStyle = {
        normal: {
          label: {
            show: false
          },
          labelLine: {
            show: false
          },
          shadowBlur: 0,
          shadowColor: '#203665'
        }
      };
      let data1 =  data.filter(v => v.disPlayName == '设备在线率');
      let data2 =  data.filter(v => v.disPlayName == '传感器报警');
      let data3 =  data.filter(v => v.disPlayName == '数据完整率');
      let circleRadius1 = data1 ? (data1[0]?.disPlayNumber * 100).toFixed(0) - 0 : 0;
      let circleRadius2 = data2 ? (data2[0]?.disPlayNumber * 100).toFixed(0) - 0 : 0;
      let circleRadius3 = data2 ? (data3[0]?.disPlayNumber * 100).toFixed(0) - 0 : 0;
      let radius = ['35%', '40%'];
      this.cgOption = {
        backgroundColor: "rgba(0,0,0,0)",
        series: [{
          name: '第一个圆环',
          type: 'pie',
          clockWise: false,
          radius: radius || [40, 50],
          itemStyle: dataStyle,
          hoverAnimation: false,
          center: ['18%', '45%'],
          data: [{
            value: circleRadius1,
            label: {
              normal: {
                rich: {
                  a: {
                    color: '#FFFFFF',
                    align: 'center',
                    fontSize: this.isBig ? 40 : 20,
                    fontWeight: "bold",
                    padding: this.isBig ? [140, 0, 0, 0] : [70, 0, 0, 0],
                  },
                  b: {
                    color: '#fff',
                    align: 'center',
                    fontSize: this.isBig ? 32 : 16,
                    padding: this.isBig ? [100, 0, 0, 0] : [50, 0, 0, 0]
                  }
                },
                formatter: (params) => {
                  return "{a|" + params.value + "%}" + "\n{b|设备在线率}";
                },
                position: 'center',
                show: true,
                textStyle: {
                  fontSize: this.isBig ? 24 : '14',
                  fontWeight: 'normal',
                  color: '#fff',
                }
              }
            },
            itemStyle: {
              normal: {
                color: '#90EF7F',
                shadowColor: '#90EF7F',
                shadowBlur: 0,
                borderRadius: 10,
              }
            }
          }, {
            value: 100 - circleRadius1,
            name: 'invisible',
            itemStyle: {
              normal: {
                color: 'rgba(23,116,255,0.2)'
              },
              emphasis: {
                color: 'rgba(23,116,255,0.2)'
              }
            }
          }]
        }, {
          name: '第二个圆环',
          type: 'pie',
          clockWise: false,
          radius: radius || [40, 50],
          itemStyle: dataStyle,
          hoverAnimation: false,
          center: ['50%', '45%'],
          data: [{
            value: circleRadius2,
            label: {
              normal: {
                rich: {
                  a: {
                    color: '#FFFFFF',
                    align: 'center',
                    fontSize: this.isBig ? 40 : 20,
                    fontWeight: "bold",
                    padding: this.isBig ? [140, 0, 0, 0] : [70, 0, 0, 0],
                  },
                  b: {
                    color: '#fff',
                    align: 'center',
                    fontSize: this.isBig ? 32 : 16,
                    padding: this.isBig ? [100, 0, 0, 0] : [50, 0, 0, 0]
                  }
                },
                formatter: function (params) {
                  return "{a|" + params.value + "%}" + "\n{b|数据完整率}";
                },
                position: 'center',
                show: true,
                textStyle: {
                  fontSize: '14',
                  fontWeight: 'normal',
                  color: '#fff'
                }
              }
            },
            itemStyle: {
              normal: {
                color: '#2196F3',
                shadowColor: '#2196F3',
                shadowBlur: 0,
                borderRadius: 10,
              }
            }
          }, {
            value: 100 - circleRadius2,
            name: 'invisible',
            itemStyle: {
              normal: {
                color: 'rgba(23,116,255,0.2)'
              },
              emphasis: {
                color: 'rgba(23,116,255,0.2)'
              }
            }
          }]
        }, {
          name: '第三个圆环',
          type: 'pie',
          clockWise: false,
          radius: radius || [40, 50],
          itemStyle: dataStyle,
          hoverAnimation: false,
          center: ['82%', '45%'],
          data: [{
            value: circleRadius3,
            label: {
              normal: {
                rich: {
                  a: {
                    color: '#FFFFFF',
                    align: 'center',
                    fontSize: this.isBig ? 40 : 20,
                    fontWeight: "bold",
                    padding: this.isBig ? [140, 0, 0, 0] : [70, 0, 0, 0],
                  },
                  b: {
                    color: '#fff',
                    align: 'center',
                    fontSize: this.isBig ? 32 : 16,
                    padding: this.isBig ? [100, 0, 0, 0] : [50, 0, 0, 0]
                  }
                },
                formatter: function (params) {
                  return "{a|" + params.value + "%}" + "\n\{b|传感器报警}";
                },
                position: 'center',
                show: true,
                textStyle: {
                  fontSize: '14',
                  fontWeight: 'normal',
                  color: '#fff'
                }
              }
            },
            itemStyle: {
              normal: {
                color: '#A569FF',
                shadowColor: '#A569FF',
                shadowBlur: 0,
                borderRadius: 10,
              }
            }
          }, {
            value: 100 - circleRadius3,
            name: 'invisible',
            itemStyle: {
              normal: {
                color: 'rgba(23,116,255,0.2)'
              },
              emphasis: {
                color: 'rgba(23,116,255,0.2)'
              }
            }
          }]
        }]
      }
    },
    onStruSearch () {
      if(this.$refs.struRef) {
        this.$refs.struRef.onSearch(this.keywords);
      }
    },
    // 分布情况 图表点击事件
    async onEChartsClick(data) {
      if(!data) return;
      let { name } = data;
      if(this.$refs.struRef) {
        this.$refs.struRef.onFilter(name);
      }
      // 地图行政区
      let rangeData = cache.session.getJSON("rangeData");
      if(rangeData && rangeData.length) {
        let d = rangeData.find(v => v.name == name);
        if(d && this.$refs.ampRef) {
          this.$refs.ampRef.setDistrictHight(d);
          let index = this.queryList.findIndex(v => v.paramsDTO.precisionParams.type == this.name);
          if (index != -1) {
            this.obj = JSON.parse(JSON.stringify(this.queryList[index])) || {};
            this.obj.managementMaintenanceIds = [d.sys_dept_id]
            let loadingInstance = Loading.service({ fullscreen: true, background: 'rgba(0, 0, 0, 0.4)', spinner: 'el-icon-loading' });
            await this.$refs.ampRef?.removeMarker();
            setTimeout(() => {
              this.$nextTick(async () => {
                await this.$refs.ampRef?.setVector('健康监测', this.obj);
                loadingInstance.close();
              });
            }, 500)
            if (this.$refs.mpRef) {
              this.$refs.mpRef.removeVector('桥梁');
            }
          }
        }
      }
    },
    // 结构物清单列表点击事件
    onTableClick(data) {
      if(!data) return;
      let { longitude, latitude } = data;
      let shape = `POINT(${longitude} ${latitude})`;
      this.$refs.ampRef?.setCenter(shape);
    },
    // 获取传感器运营状态数据
    getDataInfo(nodeCode = null) {
      return new Promise((resolve, reject) => {
        getWarningList({ nodeCode }).then(res=>{
          if(res.code === 200 && res.result) {
            resolve(res.result)
          } else {
            resolve(null)
          }
        }).catch(err=>{
          reject(err)
        });
      });
    },
  }
}
</script>

<style lang="scss" scoped>
@import "@/assets/styles/utils.scss";

.bridge-special {
  width: 100%;
  height: 100%;
  position: relative;
  margin-top: vwpx(15px);

  .special-c {
    width: 100%;
    height: 91vh;
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
  }

  .mb-2 {
    margin-bottom: vwpx(20px);
  }

  .mb-3 {
    margin-bottom: vwpx(30px);
  }

  .ml-2 {
    margin-left: vwpx(20px);
  }

  .special-l {
    position: absolute;
    left: vwpx(30px);
    top: 0;

    .base-info {
      padding: vwpx(10px) vwpx(20px);
      height: 100%;
      overflow-y: auto;

      &::-webkit-scrollbar {
        width: vwpx(12px);
        height: vwpx(12px);
      }

      &::-webkit-scrollbar-thumb {
        background-color: rgba(1, 102, 254, 0.3);
        border-radius: 2px;
      }

      &::-webkit-scrollbar-track {
        background-color: rgba(1, 102, 254, 0.2);
      }

      .info-item {
        display: flex;
        align-items: center;

        .img {
          width: vwpx(120px);
          height: vwpx(140px);
        }

        .img-icon {
          width: vwpx(68px);
          height: vwpx(68px);
          margin-left: auto;
          cursor: pointer;
        }

        .info-data {
          font-family: Source Han Sans, Source Han Sans;
          // font-weight: 400;
          font-size: vwpx(28px);
          color: #FFFFFF;
          text-align: left;
          // font-style: normal;
          // text-transform: none;
          margin-left: vwpx(30px);

          display: flex;
          flex-direction: column;

          span:first-child {
            font-family: Source Han Sans, Source Han Sans;
            // font-weight: 500;
            font-size: vwpx(30px);
            color: #00B9FE;
            line-height: vwpx(40px);
            text-align: left;
            // font-style: normal;
            // text-transform: none;
            margin-bottom: vwpx(12px);
            cursor: pointer;
          }
        }
      }

      .divider {
        border: 1px dotted rgba(156, 189, 255, 0.5);
        margin: vwpx(20px) 0;
      }
    }
    .stru-search {
      display: flex;
      align-items: center;
      .stru-input {
        width: vwpx(300px);
        ::v-deep input {
          height: vwpx(50px);
          line-height: vwpx(50px);
          background-color: rgba(1, 102, 254, 0.2);
          border: 1px solid #0166fe;
          color: #ffffff !important;
        }
      }

      ::v-deep .el-input__suffix {
        height: vwpx(50px);
        line-height: vwpx(50px);
        display: flex;
        align-items: center;
      }

      .search-btn {
        width: vwpx(40px);
        height: vwpx(50px);
        margin-left: vwpx(10px);
        background-color: rgba(1,102,254, 0.5);
        color: #ffffff;
        border: none;

        display: flex;
        align-items: center;
        justify-content: center;
        
      }
    }
  }

  .special-r {
    position: absolute;
    right: vwpx(30px);
    top: 0;

    .info {
      display: flex;

      .warning-content {
        height: 60%;
        overflow-y: auto;

        &::-webkit-scrollbar {
          width: vwpx(12px);
          height: vwpx(12px);
        }

        &::-webkit-scrollbar-thumb {
          background-color: rgba(1, 102, 254, 0.3);
          border-radius: 2px;
        }

        &::-webkit-scrollbar-track {
          background-color: rgba(1, 102, 254, 0.2);
        }

        .warning-content-list {
          height: 45%;
          color: white;
          margin-bottom: vwpx(20px);
          font-size: vwpx(28px);
          padding: 0 13px;
          display: flex;
          align-items: center;
          cursor: pointer;

          .warning-content-id {
            width: vwpx(80px);
            height: vwpx(80px);
            display: inline-flex;
            /* text-align: center; */
            justify-content: center;
            align-items: center;
            // background: #024d98;
            border: 1px solid gray;

            .img {
              width: 100%;
              height: 100%;
            }
          }

          .warning-content-val {
            height: 100%;
            display: inline-flex;
            width: 94%;
            margin-left: 1%;
          }
        }
      }

      .warning-status {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: vwpx(20px);
        flex-wrap: wrap;

        .warning-status-list {
          margin: 0 vwpx(10px) vwpx(20px) vwpx(10px);
          width: 25%;
          height: 25%;
          display: flex;
          align-items: center;
          flex-direction: column;
          cursor: pointer;

          .img {
            width: 60%;
            height: 60%;
          }

          span {
            font-family: Source Han Sans, Source Han Sans;
            font-weight: 500;
            font-size: vwpx(28px);
            color: #FFFFFF;
            text-align: center;
            font-style: normal;
            margin-top: vwpx(10px);
          }
        }
      }
    }

    .jc-data-statistics {
      position: absolute;
      top: vwpx(5px);
      left: 0;
      right: 0;
      width: 100%;

      display: flex;
      flex-direction: column;

      .statistics-info {
        margin-bottom: vwpx(10px);
        display: flex;
        align-items: center;
        justify-content: flex-start;
        margin-left: 50%;
        transform: translateX(-40%);

        .info-today {
          display: block;
          width: vwpx(25px);
          height: vwpx(25px);
          background: #02FF83;
        }

        .info-yesterday {
          display: block;
          width: vwpx(25px);
          height: vwpx(25px);
          background: #FFBE27;
        }

        .info-val {
          font-weight: 400;
          font-size: 1.3vh;
          color: #FFFFFF;
          margin: 0 vwpx(20px);
        }

        .info-unit {
          font-weight: 400;
          font-size: 1.25vh;
          color: rgba(255, 255, 255, 0.6);
        }
      }
    }

    .monitoring-category {
      width: 100%;
      display: flex;
      align-items: center;
      justify-content: space-between;
      flex-wrap: wrap;
      padding: vwpx(20px);

      .monitoring-category-list {
        width: 50%;
        margin-bottom: vwpx(15px);
        display: flex;
        align-items: center;

        .img {
          width: vwpx(50px);
          height: vwpx(50px);
        }

        .name {
          font-family: Source Han Sans, Source Han Sans;
          font-weight: 400;
          // font-size: vwpx(30px);
          // font-size: '0.6vw';
          color: #FFFFFF;
          text-align: left;
          font-style: normal;
          text-transform: none;
          margin: 0 vwpx(20px);
        }

        .val {
          font-family: Source Han Sans, Source Han Sans;
          font-weight: 400;
          font-size: vwpx(30px);
          color: #F2AF4A;
          text-align: left;
          font-style: normal;
          text-transform: none;
        }
      }
    }
  }

  .seamless-warp {
    widows: 100%;
    height: 100%;
    // overflow: hidden;
  }
}
</style>