<template>
  <el-row :gutter="20">
    <div style="display: flex; flex-wrap: wrap;">
      <div v-if="isMangerUnit" style="width: 100%">
        <ManageSelectTree placeholder="请选择" :formObject="formData"/>
      </div>
      <el-col
        v-for="(item,index) in fields"
        :key="index"
        :span="item.span?item.span:12">
        <el-form-item :label="item.label" :prop="item.prop" :rules="item.rules">
          <span v-if="item.type === 'input'">
            <el-input
              v-model="formData[item.prop]"
              :placeholder="item.placeholder"
              clearable/>
          </span>
          <span v-else-if="item.type === 'inputSelectList'">
            <el-input
              v-model="formData[item.prop]"
              readonly
              :placeholder="item.placeholder"
              clearable/>
          </span>
          <span v-else-if="item.type === 'inputTextarea'">
            <el-input
              v-model="formData[item.prop]"
              autosize
              :placeholder="item.placeholder"
              type="textarea"
              clearable/>
          </span>
          <span v-else-if="item.type === 'pileInput'">
            <PileInput v-model="formData[item.prop]"/>
          </span>
          <span v-else-if="item.type === 'inputNumber'">
            <el-input-number
              v-model="formData[item.prop]"
              style="width: 100%;"
              :precision="item.precision"
              clearable/>
          </span>
          <div v-else-if="item.type ==='uploadImg'">
            <ImageUpload
              :key="item.ownerId"
              v-model="formData[item.prop]"
              :limit="1"
              :owner-id="item.ownerId"
              :storage-path="item.storagePath?item.storagePath:'/base/data/commonFile'"/>
          </div>
          <span v-else-if="item.type === 'select'">
            <el-select
              v-model="formData[item.prop]"
              style="width: 100%;"
              :placeholder="item.placeholder"
              clearable
              filterable
              :disabled="!formData[item.disabledFieds]"
              @change="(val) => {handleSelect(val, item)}">
              <el-option
                v-for="v in item.options"
                :key="v[item.optionValue]"
                :label="v[item.optionLabel]"
                :value="v[item.optionValue]"/>
            </el-select>
          </span>
          <span v-else-if="item.type === 'selectTree'">
            <SelectTree
              v-model="formData[item.prop]"
              :dept-type="item.deptType"
              placeholder="请选择"/>
          </span>
          <span v-else-if="item.type === 'CascaderRegion'">
            <CascaderRegion
              v-model="formData[item.prop]"
              :deep="1"
              @input="(value)=>{formData[item.prop]=value}"/>
          </span>
          <span v-else-if="item.type === 'dictSelect'">
            <el-select
              v-model="formData[item.prop]"
              style="width: 100%;"
              :multiple="item.multiple"
              placeholder="请选择"
              :disabled="item.disabledFieds"
              clearable>
              <el-option
                v-for="i in dict.type[item.dict]"
                :key="i.value"
                :label="i.label"
                :value="i.value"/>
            </el-select>
          </span>

          <span v-else-if="item.type === 'multiDictSelect'">
            <MultiDictSelect
              v-model="formData[item.prop]"
              :disabled="item.disabledFieds"
              :multiple="item.multiple"
              :options="dict.type[item.dict]"/>
          </span>
          <span v-else-if="item.type === 'date' || item.type === 'year'">
            <el-date-picker
              v-model="formData[item.prop]"
              style="width:100%"
              :type="item.type"
              :placeholder="item.placeholder"
              :picker-options="pickerOptions"
              clearable
              :value-format="item.type === 'year' ? 'yyyy' : 'yyyy-MM-dd'"/>
          </span>
          <span v-else-if="item.type === 'tree'">
            <select-tree
              style="width: 100%;"
              v-model="formData[item.prop]"
              clearable/>
          </span>
          <span v-else-if="item.type === 'coordinate'">
            <lon-lat
              :type="item.prepend"
              :lon.sync="formData[item.propLon]"
              :lat.sync="formData[item.propLat]"/>
          </span>
          <span v-else-if="item.type === 'roadType'">
            <SectionSelect
              v-model="formData[item.prop]"
              :formObject="formData"
              :sectionId="formData.maintenanceSectionId"
              :disabled="!formData.maintenanceSectionId"
              clearable/>
          </span>
        </el-form-item>
      </el-col>
    </div>
  </el-row>
</template>
<script>


import SelectTree from "@/components/DeptTmpl/selectTree.vue";
import SectionSelect from "@/components/SectionSelect/index.vue";
import lonLat from "@/components/mapPosition/lonLat.vue";
import CascaderRegion from "@/views/baseData/components/CascaderRegion/index.vue";
import PileInput from "@/components/PileInput/index.vue";
import ManageSelectTree from "@/components/manageSelectTree/index.vue";
import {listMaintenanceSectionAll} from '@/api/system/maintenanceSection'
import {listByMaintenanceSectionId} from '@/api/baseData/common/routeLine'
import {createIdWorker} from '@/api/baseData/common'
import MultiDictSelect from "@/views/baseData/components/MultiDictSelect/index.vue";

export default {
  components: {
    MultiDictSelect,
    ManageSelectTree,
    PileInput,
    CascaderRegion,
    lonLat,
    SectionSelect,
    SelectTree
  },
  props: {
    isMangerUnit: {
      type: Boolean,
      default: false
    },
    fields: {
      type: Array,
      default: []
    },

    from: {
      type: Object,
      default: () => ({
        routeLevel: '',
        routeCode: '',
        routeName: '',
        maintenanceSectionId: '',
        managementMaintenanceId: ''
      })
    },
  },
  data() {
    return {
      pickerOptions: {
        disabledDate(v) {
          return v.getTime() > new Date().getTime() 
        }
      },
    }
  },

  computed: {
    formData: {
      get: function (val) {
        return this.from
      },
      set: function (val) {

        this.$emit('input', val)
      }

    }
  },
  created() {


  },
  mounted() {
    this.intImage()
  },
  dicts: [
    "bridge_route_level",
    "sys_route_type",
    "sys_culvert_type",
    "sys_design_load",
    "base_archives_type",
    'sys_design_load',
    'base_archival_data',
    'sys_route_grade',
    'sys_route_direction',
    'left_right',
    'lane',
    'sys_surface_type'
  ],
  watch: {

    'formData': {
      handler: function (val) {
        // this.$emit('input', val)
      },
      deep: true
    },


    'formData.managementMaintenanceId'(newVal, oldVal) {
      // if (oldVal) {
      //   if (this.formData.maintenanceSectionId) {
      //     this.formData.maintenanceSectionId = "";
      //   } else if (this.formData.routeCode) {
      //     this.formData.routeCode = "";
      //   }
      // }
    },
    'formData.managementMaintenanceBranchId'(newVal, oldVal) {
      if (newVal) {
        this.deptChange(newVal)
      }
      if (oldVal ) {
        if(this.formData.maintenanceSectionId)this.formData.maintenanceSectionId=''
        if(this.formData.routeCode)this.formData.routeCode=''
      }
    },
    'formData.maintenanceSectionId'(newVal, oldVal) {

      if (newVal) {
        this.maintenanceSectionChange(newVal)
      }
      if (oldVal) {
        if (this.formData.routeCode) {
          this.formData.routeCode = "";
        }
        if (this.formData.routeName) {
          this.formData.routeName = "";
        }
      }
    },
    'formData.routeLevel'(newVal, oldVal) {
      if (!Array.isArray(newVal)) {
        this.formData.routeLevel = Array.isArray(newVal) ? '' : newVal.split(',')
      }

    },


  },
  methods: {

    intImage() {
      this.fields.forEach(async el => {
        if (el.type === 'uploadImg') {
          let res = await createIdWorker();

          if (res.code == 200) {
            el.ownerId = Number(res.data);

            this.$forceUpdate();
          }
        }
      })
    },

    // 选择框事件
    handleSelect(e, i) {
      switch (i.prop) {
        case 'routeCode':
          if (e) {
            this.fields.forEach(el => {
              if (el.prop === 'routeName') {
                const option = el.options.find(i => i.routeCode === e)
                if (option) {
                  this.formData.routeId = option.routeId
                  this.formData.routeName = option.routeName
                }
              }
            })
          } else {
            this.formData.routeId = ''
            this.formData.routeName = ''
          }
          break
        case 'routeName':
          if (e) {
            this.fields.forEach(el => {
              if (el.prop === 'routeCode') {
                const option = el.options.find(i => i.routeName === e)
                if (option) {
                  this.formData.routeId = option.routeId
                  this.formData.routeCode = option.routeCode
                }
              }
            })
          } else {
            this.formData.routeId = ''
            this.formData.routeCode = ''
          }
          break
      }
    },
    // 监听选中管理处
    deptChange(e) {
      if (!e) return
      listMaintenanceSectionAll({departmentIdList: [this.formData.managementMaintenanceBranchId]}).then(res => {
        if (res.code == 200) {
          this.fields.forEach(el => {
            if (el.prop === 'maintenanceSectionId') {
              el.options = res.data
            }
          })
        }
      })
    },
    // 监听选中养护路段
    maintenanceSectionChange(e) {
      if (!e) return

      this.fields.forEach(el => {
        if (el.prop === 'maintenanceSectionId') {
          //el.options中maintenanceSectionName等于e的routeGrade

          const option = el.options.find(i => i.maintenanceSectionId === e)
          if (option && option.routeGrade) {

            if (this.fields.some(item => item.prop === 'routeLevel')) {
              this.formData.routeLevel = option.routeGrade.split(',')
            }
          }
        }
      })

      listByMaintenanceSectionId({maintenanceSectionId: e}).then(res => {
        if (res.code == 200) {
          this.fields.forEach(el => {
            if (el.prop === 'routeCode' || el.prop === 'routeName') {
              el.options = res.data
            }
          })
        }
      })
    },

  }
}
</script>

<style lang="scss" scoped>
@import '~@/assets/styles/el-tabs.scss';

::v-deep {
  // .el-input.is-disabled .el-input__inner {
  //   background-color: white;
  //   border-color: #dfe4ed;
  //   color: #1d2129;
  // }

  .el-textarea.is-disabled .el-textarea__inner {
    background-color: white;
    border-color: #dfe4ed;
    color: #1d2129;
  }
}
</style>
