<template>
  <div class="app-container">
    <el-row :gutter="20">
      <!-- 筛选区开始 -->
      <el-tabs v-model='activeTab' @tab-click="handleTabClick">
        <el-tab-pane label="未处理" name="0">
        </el-tab-pane>
        <el-tab-pane label="已处理" name="1">
        </el-tab-pane>
      </el-tabs>
      <el-col :span="24" :xs="24" v-if="activeTab == '0'">
        <el-row>
          <el-col :span="24">
            <el-form ref="queryForm" :inline="true" :model="queryParams" label-width="68px" size="small">
              <el-form-item label="" prop="alertLevel">
                <el-select v-model="queryParams.alertLevel" placeholder="请选择预警等级" popper-class="type-popper"
                           :popper-append-to-body="false">
                  <el-option v-for="item in alertLevelList" :key="item.value" :label="item.label" :value="item.value">
                  </el-option>
                </el-select>
              </el-form-item>
<!--              <el-form-item label="" prop="siteName">-->
<!--                <dict-select dict="warning_level" v-model="queryParams.siteName" placeholder="请选择处理状态"/>-->
<!--              </el-form-item>-->
              <el-form-item>
                <el-button icon="el-icon-search" size="mini" type="primary" @click="handleQuery">搜索</el-button>
                <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
              </el-form-item>
            </el-form>
          </el-col>
        </el-row>
        <!-- 筛选区结束 -->
        <!-- 数据表格开始 -->
        <div class="tableDiv">
          <el-table v-loading="loading" :data="dataList"
                    height="500px" stripe size="mini"
                    style="width: 100%" @selection-change="handleSelectionChange">
            <el-table-column
              type="selection"
              width="50">
            </el-table-column>
            <el-table-column align="center" label="序号" type="index" width="100"></el-table-column>
            <template v-for="(column, index) in columns">
              <el-table-column v-if="column.visible" :key="index" :label="column.label" :prop="column.field"
                               align="center" show-overflow-tooltip>
                <template slot-scope="scope">
                  <span>{{ scope.row[column.field] }}</span>
                </template>
              </el-table-column>
            </template>
            <el-table-column align="center" label="操作" width="200">
              <template slot-scope="scope">
                <el-button size="mini" type="text" @click="handleFullClick(scope.row)">查看所有</el-button>
                <el-button size="mini" type="text" @click="handleOpenProcess(scope.row)" v-has-menu-permi="['jgjc:earlyWarning:record:dispose']">处理</el-button>
              </template>
            </el-table-column>
          </el-table>
          <pagination
            v-show="total > 0"
            :limit.sync="queryParams.pageSize"
            :page.sync="queryParams.pageNumber"
            :total="total"
            @pagination="handleQuery"
          />
        </div>
        <!-- 数据表格结束 -->
      </el-col>
      <el-col :span='24' v-else>
        <el-row>
          <el-col :span="24">
            <el-form ref="queryForm" :inline="true" :model="queryParams2" label-width="68px" size="small">
              <el-form-item label="" prop="alertLevel">
                <el-select v-model="queryParams2.alertLevel" placeholder="请选择预警等级" popper-class="type-popper"
                           :popper-append-to-body="false">
                  <el-option v-for="item in alertLevelList" :key="item.value" :label="item.label" :value="item.value">
                  </el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="" prop="sensorCode">
                <el-input v-model="queryParams2.sensorCode" placeholder="请输入传感器编码"/>
              </el-form-item>
              <el-form-item label="" prop="monitorCode">
                <el-input v-model="queryParams2.monitorCode" placeholder="请输入监测值编码"/>
              </el-form-item>
              <el-form-item>
                <el-button icon="el-icon-search" size="mini" type="primary" @click="handleQuery2">搜索</el-button>
                <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
              </el-form-item>
            </el-form>
          </el-col>
        </el-row>
        <!-- 筛选区结束 -->
        <!-- 数据表格开始 -->
        <div class="tableDiv">
          <el-table v-loading="loading" :data="dataList2"
                    height="500px" stripe size="mini"
                    style="width: 100%" @selection-change="handleSelectionChange">
            <el-table-column
              type="selection"
              width="50">
            </el-table-column>
            <el-table-column align="center" label="序号" type="index" width="100"></el-table-column>
            <template v-for="(column, index) in columns2">
              <el-table-column v-if="column.visible" :key="index" :label="column.label" :prop="column.field"
                               align="center" show-overflow-tooltip>
                <template slot-scope="scope">
                  <span>{{ scope.row[column.field] }}</span>
                </template>
              </el-table-column>
            </template>
            <el-table-column align="center" label="操作" width="200">
              <template slot-scope="scope">
                <el-button size="mini" type="text" @click="handleDelete(scope.row)">删除</el-button>
              </template>
            </el-table-column>
          </el-table>
          <pagination
            v-show="total2 > 0"
            :limit.sync="queryParams2.pageSize"
            :page.sync="queryParams2.pageNumber"
            :total="total2"
            @pagination="handleQuery2"
          />
        </div>
        <!-- 数据表格结束 -->
      </el-col>
    </el-row>
    <el-dialog :title="'预警处理记录'" :visible.sync="dialogVisible" width="70%" @close="dialogVisible = false; fileList = []" modal-append-to-body :modal='false'>
      <div v-loading="loading">
        <el-descriptions border size="medium">
          <el-descriptions-item label="结构物名称">{{ checkRow.structureName }}</el-descriptions-item>
          <el-descriptions-item label="传感器名称">{{ checkRow.sensorName }}</el-descriptions-item>
          <el-descriptions-item label="监测值名称">{{ checkRow.specificMonitorTypeName }}</el-descriptions-item>
          <el-descriptions-item label="预警类型">{{ checkRow.alertType }}</el-descriptions-item>
          <el-descriptions-item label="预警级别">{{ checkRow.alertLevel }}</el-descriptions-item>
          <el-descriptions-item label="报警值">{{ checkRow.sensorValue }}</el-descriptions-item>
          <el-descriptions-item label="阈值上限">{{ checkRow.threshold }}</el-descriptions-item>
          <el-descriptions-item label="阈值下限"></el-descriptions-item>
          <el-descriptions-item label=""></el-descriptions-item>
          <el-descriptions-item label="预警内容">{{ checkRow.alertContent }}</el-descriptions-item>

        </el-descriptions>
        <el-form :model="formData" ref="elForm" :rules="rules" label-width="120px" class="mt20">
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="处理结果" prop="isNormal">
                <el-select v-model="formData.isNormal" placeholder="请选择处理结果" style="width: 100%" :popper-append-to-body='false'>
                  <el-option label="正常" value="正常"></el-option>
                  <el-option label="异常" value="异常"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12" v-if="formData.isNormal == '异常'">
              <el-form-item label="预案模式" prop="flashMode">
                <el-select v-model="formData.flashMode" placeholder="请选择预案模式" style="width: 100%" :popper-append-to-body='false'>
                  <el-option v-for="item in flashModes" :key="item.value" :label="item.label"
                             :value="item.value"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="处理内容" prop="description">
                <el-input v-model="formData.description" type="textarea" :rows="2" style="width: 100%" placeholder="请输入处理内容" clearable></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="图片" prop="imgPath">
                <file-upload platform='jgjc' ref="fileUpload" v-model="formData.imgPath" :limit=1 :ownerId="formData.imgPath"/>
              </el-form-item>
            </el-col>
          </el-row>
          <div style="text-align: right">
            <el-button type="primary" @click="handleProcess">提交</el-button>
          </div>
        </el-form>
      </div>
    </el-dialog>
    <el-dialog :title="'查看所有'" :visible.sync="fullDialogVisible" width="70%" @close="fullDialogVisible = false" modal-append-to-body :modal='false'>
      <div class="tableDiv">
        <el-table v-loading="loading" :data="fullDataList"
                  :height="'calc(100vh - 260px)' " border size="mini"
                  style="width: 100%" @selection-change="handleSelectionChange">
          <el-table-column align="center" fixed label="序号" type="index" width="100"></el-table-column>
          <template v-for="(column, index) in columns">
            <el-table-column v-if="column.visible" :key="index" :label="column.label" :prop="column.field"
                             align="center" show-overflow-tooltip>
              <template slot-scope="scope">
                <span>{{ scope.row[column.field] }}</span>
              </template>
            </el-table-column>
          </template>
        </el-table>
        <pagination
          v-show="fullParamsTotal > 0"
          :limit.sync="fullParams.pageSize"
          :page.sync="fullParams.pageNumber"
          :total="fullParamsTotal"
          @pagination="handleFullQuery"
        />
      </div>
    </el-dialog>
  </div>
</template>

<script>
import RoadSection from "@/views/baseData/components/roadSection/index.vue";
import SelectTree from "@/components/DeptTmpl/selectTree.vue";
import {
  deleteAlertrecord,
  processFlash,
  sensorWarningRecordPage,
  warningRecordPage,
  warningRecordPage2,
} from '@/api/jgjc/earlyWarning/deviceModel'
import {
  getCommonConfigPage,
  getSsConfigPage,
  getWdmConfigPage,
  getZjgtConfigPage,
} from '@/api/jgjc/flashingModeConfig'
import {sansiModes, wdmModes} from "@/views/jgjc/earlyWarning/defaultModes";

export default {
  name: "WarningRecord",
  components: {SelectTree, RoadSection},
  props: {
    checkData: {
      type: Object,
      default: () => {}
    },
    structureCode: {
      type: String,
      default: ''
    },
    installLocation: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      loading: false,
      showSearch: false,
      total: 0,
      fullParamsTotal: 0,
      dataList: [],
      fullDataList: [],
      alertLevelList: [
        {label: '一级预警', value: '一级预警'},
        {label: '二级预警', value: '二级预警'},
        {label: '三级预警', value: '三级预警'},
      ],
      // 查询参数
      queryParams: {
        pageNumber: 1,
        pageSize: 50,
      },
      fullParams: {
        pageNumber: 1,
        pageSize: 50,
      },
      dialogVisible: false,
      fullDialogVisible: false,
      checkRow: {},
	    formData: {
		    planName: '一级预警'
      },
      // 列信息
      columns: [
        {key: 0, field: 'structureName', label: '结构物名称', visible: true},
        {key: 1, field: 'sensorName', label: '传感器名称', visible: true},
        {key: 2, field: 'specificMonitorTypeName', label: '监测值名称', visible: true},
        {key: 3, field: 'alertType', label: '预警类型', visible: true},
        {key: 4, field: 'alertLevel', label: '预警级别', visible: true},
        {key: 5, field: 'alertContent', label: '预警内容', visible: true},
        {key: 6, field: 'sensorValue', label: '报警值', visible: true},
        {key: 7, field: 'threshold', label: '阈值线', visible: true},
        {key: 8, field: 'sensorValueTime', label: '预警时间', visible: true}
      ],
      flashModes: [
        {label: '特重大交通事故', value: '0'},
        {label: '交通事故', value: '1'},
        {label: '封路', value: '2'},
        {label: '减速慢行', value: '3'},
        {label: '主动诱导', value: '4'},
        {label: '道路轮廓强化', value: '5'},
        {label: '正常', value: '6'},
      ],
      rules: {
        isNormal: [
          {required: true, message: '请选择处理结果', trigger: 'blur'}
        ],
        description: [
          {required: true, message: '请输入处理内容', trigger: 'blur'}
        ],
        flashMode: [
          {required: true, message: '请选择预案模式', trigger: 'blur'}
        ]
      },
      activeTab: 0,
      queryParams2: {
        pageNumber: 1,
        pageSize: 50,
      },
      dataList2: [],
      total2: [],
      columns2: [
        {key: 0, field: 'structureName', label: '结构物名称', visible: true},
        {key: 1, field: 'sensorCode', label: '传感器编码', visible: true},
        {key: 2, field: 'monitorCode', label: '监测值编码', visible: true},
        {key: 3, field: 'alertType', label: '预警类型', visible: true},
        {key: 4, field: 'alertLevel', label: '预警级别', visible: true},
        {key: 5, field: 'alertContent', label: '预警内容', visible: true},
        {key: 6, field: 'sensorValue', label: '报警值', visible: true},
        {key: 7, field: 'threshold', label: '阈值线', visible: true},
        {key: 8, field: 'warningTime', label: '预警时间', visible: true}
      ],
    };
  },
  mounted() {
    this.queryParams.structureCode = this.checkData.code
    this.queryParams.structId = this.checkData.id
    this.queryParams2 = {
      ...this.queryParams
    }
    const flashType = this.checkData.flashType
    this.flashModes = []
    if (flashType == '中交国通') {
      getZjgtConfigPage({
        pageNum: 1,
        pageSize: 100,
      }).then(res => {
        for (let i = 0; i < res.rows.length; i++) {
          const item = res.rows[i]
          const temp = {}
          temp.zjgtConfigEntity = item
          temp.isCustom = item.isCustom
          temp.label = item.modeName
          temp.value = item.id
          this.flashModes.push(temp)
        }
        console.log(this.flashModes)
      })

    }
    if (flashType == '三思') {
      getSsConfigPage({
        pageNum: 1,
        pageSize: 100,
      }).then(res => {
        for (let i = 0; i < res.rows.length; i++) {
          const item = res.rows[i]
          const temp = {}
          temp.sansiConfigEntity = item
          temp.isCustom = item.isCustom
          temp.label = item.name
          temp.value = item.id
          this.flashModes.push(temp)
        }
      })
    }
    if (flashType == '维的美') {
      getWdmConfigPage({
        pageNum: 1,
        pageSize: 100,
      }).then(res => {
        for (let i = 0; i < res.rows.length; i++) {
          const item = res.rows[i]
          const temp = {}
          temp.wdmConfigEntity = item
          temp.isCustom = item.isCustom
          temp.label = item.modeName
          temp.value = item.id
          this.flashModes.push(temp)
        }
      })
    }
    if (flashType == '第三方') {
      getCommonConfigPage({
        pageNum: 1,
        pageSize: 100,
      }).then(res => {
        for (let i = 0; i < res.rows.length; i++) {
          const item = res.rows[i]
          const temp = {}
          temp.flashCommonModeConfig = item
          temp.isCustom = item.isCustom
          temp.label = item.modeName
          temp.value = item.id
          this.flashModes.push(temp)
        }
      })
    }
    this.handleQuery();
  },
  methods: {
    handleQuery() {
      this.loading = true
      if (this.structureCode) this.queryParams.structureCode = this.structureCode
      if (this.installLocation) this.queryParams.installLocation = this.installLocation

      warningRecordPage(this.queryParams).then(res => {
        this.dataList = res.rows
        this.dataList.forEach(item => {
          item.warningTime = this.formatDateTime(item.warningTime)
        })
        this.total = res.total
        this.loading = false
      })
    },
    handleQuery2() {
      this.loading = true
      if (this.structureCode) this.queryParams.structureCode = this.structureCode
      if (this.installLocation) this.queryParams.installLocation = this.installLocation
      warningRecordPage2(this.queryParams2).then(res => {
        this.dataList2 = res.rows
        this.dataList2.forEach(item => {
          item.warningTime = this.formatDateTime(item.warningTime)
        })
        this.total2 = res.total
        this.loading = false
      })
    },
    handleTabClick() {
      if (this.activeTab == '0') {
        this.handleQuery()
      } else {
        this.handleQuery2()
      }
    },
    formatDateTime(isoString) {
      if (!isoString || isoString == null || isoString == '') {
        return ''
      }
      try {
        // 创建 Date 对象
        const date = new Date(isoString);
        // 获取年、月、日、时、分、秒
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const day = String(date.getDate()).padStart(2, '0');
        const hours = String(date.getHours()).padStart(2, '0');
        const minutes = String(date.getMinutes()).padStart(2, '0');
        const seconds = String(date.getSeconds()).padStart(2, '0');
        // 拼接成需要的格式
        return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
      } catch (e) {
        return ''
      }
    },
    resetQuery() {
      this.queryParams = {
        pageNumber: 1,
        pageSize: 50,
      };
      this.queryParams.structureCode = this.checkData.code
      this.queryParams.structId = this.checkData.id

      this.queryParams2 = {
        ...this.queryParams
      }
      this.handleTabClick()
    },
    handleFullClick(row) {
      this.fullDialogVisible = true
      this.fullParams.structId = this.checkData.id
      this.fullParams.sensorId = row.sensorId
      this.handleFullQuery()
    },
    handleFullQuery() {
      this.loading = true
      sensorWarningRecordPage(this.fullParams).then(res => {
        this.fullDataList = res.rows
        this.fullParamsTotal = res.total
        this.loading = false
      })
    },
    handleOpenProcess(row) {
      this.checkRow = row
      this.dialogVisible = true
    },
    handleProcess() {
      this.$refs.elForm.validate(valid => {
        if (!valid) return
        this.loading = true
        const flashData = this.flashModes.find(item => item.value == this.formData.flashMode)
        let params = {
          ...this.formData,
          structureCode: this.checkRow.structureCode,
          structId: this.checkData.id,
          flashType: this.checkRow.flashType,
          isCustom: flashData?.isCustom || '0',
          zjgtConfigEntity: flashData?.zjgtConfigEntity || {},
          sansiConfigEntity: flashData?.sansiConfigEntity || {},
          wdmConfigEntity: flashData?.wdmConfigEntity || {},
          flashCommonModeConfig: flashData?.flashCommonModeConfig || {},
          planName: '一级预警',
          lampSwitch: '',
          screenWord: '',
          flicker: '',
          soundContent: '',
        }
        if (params.imgPath && Array.isArray(params.imgPath)) {
          params.imgPath = params.imgPath[0]
        }
        processFlash(params).then(res => {
          this.loading = false
          this.$message.success('操作成功！');
          this.dialogVisible = false
          this.handleQuery()
          this.$emit('query')
        })
      })
    },
    handleDelete(row) {
      this.$confirm('是否确认删除?', '警告', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        const ids = [row.id]
        deleteAlertrecord({ids: ids}).then(res => {
          this.handleQuery2()
          this.$modal.msgSuccess("删除成功");
        })
      });
    },
    // 选中
    handleSelectionChange(e) {
      this.selectDatas = e
    },
  }
};
</script>

<style scoped lang="scss">
@import "./index.scss";

.tableDiv {
  margin-top: 20px;
}
</style>
