<template>
  <div class="app-container">
    <el-row :gutter="20">

      <!--筛选区开始-->
      <el-col :span="24" :xs="24">
        <el-row>
          <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" label-width="68px">
            <el-col :span="24">
                          <el-form-item label="" prop="routeCode">
                            <el-input
                                v-model="queryParams.routeCode"
                                placeholder="请输入路线编码"
                                clearable
                                prefix-icon="el-icon-user"
                                style="width: 240px"
                                @keyup.enter.native="handleQuery"
                            />
                          </el-form-item>
                          <el-form-item label="" prop="routeName">
                            <el-input
                                v-model="queryParams.routeName"
                                placeholder="请输入路线名称"
                                clearable
                                prefix-icon="el-icon-user"
                                style="width: 240px"
                                @keyup.enter.native="handleQuery"
                            />
                          </el-form-item>
                          <el-form-item label="" prop="province">
                            <el-input
                                v-model="queryParams.province"
                                placeholder="请输入省份"
                                clearable
                                prefix-icon="el-icon-user"
                                style="width: 240px"
                                @keyup.enter.native="handleQuery"
                            />
                          </el-form-item>
                          <el-form-item label="" prop="period">
                            <el-input
                                v-model="queryParams.period"
                                placeholder="请输入洪水年限"
                                clearable
                                prefix-icon="el-icon-user"
                                style="width: 240px"
                                @keyup.enter.native="handleQuery"
                            />
                          </el-form-item>
                          <el-form-item label="" prop="startStake">
                            <el-input
                                v-model="queryParams.startStake"
                                placeholder="请输入起点桩号"
                                clearable
                                prefix-icon="el-icon-user"
                                style="width: 240px"
                                @keyup.enter.native="handleQuery"
                            />
                          </el-form-item>
                          <el-form-item label="" prop="endStake">
                            <el-input
                                v-model="queryParams.endStake"
                                placeholder="请输入终点桩号"
                                clearable
                                prefix-icon="el-icon-user"
                                style="width: 240px"
                                @keyup.enter.native="handleQuery"
                            />
                          </el-form-item>
                          <el-form-item label="" prop="length">
                            <el-input
                                v-model="queryParams.length"
                                placeholder="请输入长度"
                                clearable
                                prefix-icon="el-icon-user"
                                style="width: 240px"
                                @keyup.enter.native="handleQuery"
                            />
                          </el-form-item>
                          <el-form-item label="" prop="routeSectionName">
                            <el-input
                                v-model="queryParams.routeSectionName"
                                placeholder="请输入路段名称"
                                clearable
                                prefix-icon="el-icon-user"
                                style="width: 240px"
                                @keyup.enter.native="handleQuery"
                            />
                          </el-form-item>
                          <el-form-item label="" prop="managementMaintenanceName">
                            <el-input
                                v-model="queryParams.managementMaintenanceName"
                                placeholder="请输入管理处名称"
                                clearable
                                prefix-icon="el-icon-user"
                                style="width: 240px"
                                @keyup.enter.native="handleQuery"
                            />
                          </el-form-item>
                          <el-form-item label="" prop="centerLon">
                            <el-input
                                v-model="queryParams.centerLon"
                                placeholder="请输入中心点经度"
                                clearable
                                prefix-icon="el-icon-user"
                                style="width: 240px"
                                @keyup.enter.native="handleQuery"
                            />
                          </el-form-item>
                          <el-form-item label="" prop="centerLat">
                            <el-input
                                v-model="queryParams.centerLat"
                                placeholder="请输入中心点纬度"
                                clearable
                                prefix-icon="el-icon-user"
                                style="width: 240px"
                                @keyup.enter.native="handleQuery"
                            />
                          </el-form-item>
                          <el-form-item label="" prop="managementMaintenanceId">
                            <el-input
                                v-model="queryParams.managementMaintenanceId"
                                placeholder="请输入管理处id"
                                clearable
                                prefix-icon="el-icon-user"
                                style="width: 240px"
                                @keyup.enter.native="handleQuery"
                            />
                          </el-form-item>
                          <el-form-item label="" prop="routeSectionId">
                            <el-input
                                v-model="queryParams.routeSectionId"
                                placeholder="请输入路段id"
                                clearable
                                prefix-icon="el-icon-user"
                                style="width: 240px"
                                @keyup.enter.native="handleQuery"
                            />
                          </el-form-item>
                          <el-form-item label="" prop="affects">
                            <el-input
                                v-model="queryParams.affects"
                                placeholder="请输入是否影响道路安全(0:不影响;1:影响)"
                                clearable
                                prefix-icon="el-icon-user"
                                style="width: 240px"
                                @keyup.enter.native="handleQuery"
                            />
                          </el-form-item>
                          <el-form-item label="" prop="photos">
                            <el-input
                                v-model="queryParams.photos"
                                placeholder="请输入图片的ownerId"
                                clearable
                                prefix-icon="el-icon-user"
                                style="width: 240px"
                                @keyup.enter.native="handleQuery"
                            />
                          </el-form-item>
                          <el-form-item label="" prop="checkerId">
                            <el-input
                                v-model="queryParams.checkerId"
                                placeholder="请输入巡查人ID"
                                clearable
                                prefix-icon="el-icon-user"
                                style="width: 240px"
                                @keyup.enter.native="handleQuery"
                            />
                          </el-form-item>
                          <el-form-item label="" prop="checkerName">
                            <el-input
                                v-model="queryParams.checkerName"
                                placeholder="请输入巡查人"
                                clearable
                                prefix-icon="el-icon-user"
                                style="width: 240px"
                                @keyup.enter.native="handleQuery"
                            />
                          </el-form-item>
                          <el-form-item label="" prop="auditId">
                            <el-input
                                v-model="queryParams.auditId"
                                placeholder="请输入审核人ID"
                                clearable
                                prefix-icon="el-icon-user"
                                style="width: 240px"
                                @keyup.enter.native="handleQuery"
                            />
                          </el-form-item>
                          <el-form-item label="" prop="auditName">
                            <el-input
                                v-model="queryParams.auditName"
                                placeholder="请输入审核人"
                                clearable
                                prefix-icon="el-icon-user"
                                style="width: 240px"
                                @keyup.enter.native="handleQuery"
                            />
                          </el-form-item>
                          <el-form-item label="" prop="checkTime">
                            <el-date-picker clearable
                                            v-model="queryParams.checkTime"
                                            type="date"
                                            value-format="yyyy-MM-dd"
                                            placeholder="请选择巡查时间">
                            </el-date-picker>
                          </el-form-item>
                          <el-form-item label="" prop="auditTime">
                            <el-date-picker clearable
                                            v-model="queryParams.auditTime"
                                            type="date"
                                            value-format="yyyy-MM-dd"
                                            placeholder="请选择审核时间">
                            </el-date-picker>
                          </el-form-item>
              <el-form-item>
                <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
                <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
                <el-button v-show="!showSearch" @click="showSearch=true" icon="el-icon-arrow-down" circle></el-button>
                <el-button v-show="showSearch" @click="showSearch=false" icon="el-icon-arrow-up" circle></el-button>
              </el-form-item>
              <!--默认折叠 ,此处仅作为示例-->
              <el-form-item v-show="showSearch" label="创建时间" prop="createTime">
                <el-date-picker
                    v-model="dateRange"
                    style="width: 240px"
                    value-format="yyyy-MM-dd"
                    type="daterange"
                    range-separator="-"
                    start-placeholder="开始日期"
                    end-placeholder="结束日期"
                ></el-date-picker>
              </el-form-item>
            </el-col>
          </el-form>
        </el-row>
        <!--筛选区结束-->


        <!--操作按钮区开始-->
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button
                type="primary"
                icon="el-icon-plus"
                size="mini"
                @click="handleAdd"
                v-hasPermi="['middleData:hazardSections:add']"
            >新增</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button
                type="success"
                icon="el-icon-edit"
                size="mini"
                :disabled="single"
                @click="handleUpdate"
                v-hasPermi="['middleData:hazardSections:edit']"
            >修改</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button
                type="danger"
                icon="el-icon-delete"
                size="mini"
                :disabled="multiple"
                @click="handleDelete"
                v-hasPermi="['middleData:hazardSections:remove']"
            >删除</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button
                type="info"
                plain
                icon="el-icon-upload2"
                size="mini"
                @click="handleImport"
                v-hasPermi="['middleData:hazardSections:export']"
            >导入</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button
                type="warning"
                icon="el-icon-download"
                size="mini"
                @click="handleExport"
                v-hasPermi="['system:user:export']"
            >导出</el-button>
          </el-col>
          <right-toolbar :showSearch.sync="showSearch" @queryTable="getList" :columns="columns"></right-toolbar>
        </el-row>
        <!--操作按钮区结束-->

        <!--数据表格开始-->
        <div class="tableDiv">
          <el-table
              v-adjust-table
              ref="table"
              size="mini"
              :height="showSearch ? 'calc(100vh - 320px)' : 'calc(100vh - 260px)'"
              style="width: 100%"
              v-loading="loading"
              border
              :data="hazardSectionsList"
              @selection-change="handleSelectionChange"
              :row-style="rowStyle"
              @row-click="handleRowClick"
          >
            <el-table-column type="selection" width="50" align="center" />
            <el-table-column fixed label="序号" type="index" width="50">
            </el-table-column>
                    <el-table-column label="路线编码" align="center" prop="routeCode" />
                    <el-table-column label="路线名称" align="center" prop="routeName" />
                    <el-table-column label="省份" align="center" prop="province" />
                    <el-table-column label="洪水年限" align="center" prop="period" />
                    <el-table-column label="起点桩号" align="center" prop="startStake" />
                    <el-table-column label="终点桩号" align="center" prop="endStake" />
                    <el-table-column label="长度" align="center" prop="length" />
                    <el-table-column label="路段名称" align="center" prop="routeSectionName" />
                    <el-table-column label="管理处名称" align="center" prop="managementMaintenanceName" />
                    <el-table-column label="产权单位类型" align="center" prop="propertyUnitType" />
                    <el-table-column label="中心点经度" align="center" prop="centerLon" />
                    <el-table-column label="中心点纬度" align="center" prop="centerLat" />
                    <el-table-column label="空间信息" align="center" prop="shape" />
                    <el-table-column label="管理处id" align="center" prop="managementMaintenanceId" />
                    <el-table-column label="路段id" align="center" prop="routeSectionId" />
                    <el-table-column label="是否影响道路安全(0:不影响;1:影响)" align="center" prop="affects" />
                    <el-table-column label="备注" align="center" prop="remarks" />
                    <el-table-column label="图片的ownerId" align="center" prop="photos" />
                    <el-table-column label="巡查人ID" align="center" prop="checkerId" />
                    <el-table-column label="巡查人" align="center" prop="checkerName" />
                    <el-table-column label="审核人ID" align="center" prop="auditId" />
                    <el-table-column label="审核人" align="center" prop="auditName" />
                    <el-table-column label="审核状态" align="center" prop="auditStatus" />
                    <el-table-column label="巡查时间" align="center" prop="checkTime" width="180">
                      <template v-slot="scope">
                        <span>{{ parseTime(scope.row.checkTime, '{y}-{m}-{d}') }}</span>
                      </template>
                    </el-table-column>
                    <el-table-column label="审核时间" align="center" prop="auditTime" width="180">
                      <template v-slot="scope">
                        <span>{{ parseTime(scope.row.auditTime, '{y}-{m}-{d}') }}</span>
                      </template>
                    </el-table-column>
            <el-table-column
                label="操作"
                fixed="right"
                align="center"
                width="160"
                class-name="small-padding fixed-width"
            >
              <template slot-scope="scope" v-if="scope.row.userId !== 1">
                <el-button
                    size="mini"
                    type="text"
                    icon="el-icon-edit"
                    @click="handleUpdate(scope.row)"
                    v-hasPermi="['middleData:hazardSections:edit']"
                >修改</el-button>
                <el-button
                    size="mini"
                    type="text"
                    icon="el-icon-delete"
                    @click="handleDelete(scope.row)"
                    v-hasPermi="['middleData:hazardSections:remove']"
                >删除</el-button>
              </template>
            </el-table-column>
          </el-table>

          <pagination
              v-show="total>0"
              :total="total"
              :page.sync="queryParams.pageNum"
              :limit.sync="queryParams.pageSize"
              @pagination="getList"
          />
        </div>
        <!--数据表格结束-->
      </el-col>
    </el-row>

    <!-- 添加或修改山洪淹没区路段清单对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="600px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">

                      <el-col :span="12">
                        <el-form-item label="路线编码" prop="routeCode">
                          <el-input v-model="form.routeCode" placeholder="请输入路线编码" />
                        </el-form-item>
                      </el-col>


                      <el-col :span="12">
                        <el-form-item label="路线名称" prop="routeName">
                          <el-input v-model="form.routeName" placeholder="请输入路线名称" />
                        </el-form-item>
                      </el-col>


                      <el-col :span="12">
                        <el-form-item label="省份" prop="province">
                          <el-input v-model="form.province" placeholder="请输入省份" />
                        </el-form-item>
                      </el-col>


                      <el-col :span="12">
                        <el-form-item label="洪水年限" prop="period">
                          <el-input v-model="form.period" placeholder="请输入洪水年限" />
                        </el-form-item>
                      </el-col>


                      <el-col :span="12">
                        <el-form-item label="起点桩号" prop="startStake">
                          <el-input v-model="form.startStake" placeholder="请输入起点桩号" />
                        </el-form-item>
                      </el-col>


                      <el-col :span="12">
                        <el-form-item label="终点桩号" prop="endStake">
                          <el-input v-model="form.endStake" placeholder="请输入终点桩号" />
                        </el-form-item>
                      </el-col>


                      <el-col :span="12">
                        <el-form-item label="长度" prop="length">
                          <el-input v-model="form.length" placeholder="请输入长度" />
                        </el-form-item>
                      </el-col>


                      <el-col :span="12">
                        <el-form-item label="路段名称" prop="routeSectionName">
                          <el-input v-model="form.routeSectionName" placeholder="请输入路段名称" />
                        </el-form-item>
                      </el-col>


                      <el-col :span="12">
                        <el-form-item label="管理处名称" prop="managementMaintenanceName">
                          <el-input v-model="form.managementMaintenanceName" placeholder="请输入管理处名称" />
                        </el-form-item>
                      </el-col>




                      <el-col :span="12">
                        <el-form-item label="中心点经度" prop="centerLon">
                          <el-input v-model="form.centerLon" placeholder="请输入中心点经度" />
                        </el-form-item>
                      </el-col>


                      <el-col :span="12">
                        <el-form-item label="中心点纬度" prop="centerLat">
                          <el-input v-model="form.centerLat" placeholder="请输入中心点纬度" />
                        </el-form-item>
                      </el-col>


                      <el-col :span="12">
                        <el-form-item label="空间信息" prop="shape">
                          <el-input v-model="form.shape" type="textarea" placeholder="请输入内容" />
                        </el-form-item>
                      </el-col>


                      <el-col :span="12">
                        <el-form-item label="管理处id" prop="managementMaintenanceId">
                          <el-input v-model="form.managementMaintenanceId" placeholder="请输入管理处id" />
                        </el-form-item>
                      </el-col>


                      <el-col :span="12">
                        <el-form-item label="路段id" prop="routeSectionId">
                          <el-input v-model="form.routeSectionId" placeholder="请输入路段id" />
                        </el-form-item>
                      </el-col>


                      <el-col :span="12">
                        <el-form-item label="是否影响道路安全(0:不影响;1:影响)" prop="affects">
                          <el-input v-model="form.affects" placeholder="请输入是否影响道路安全(0:不影响;1:影响)" />
                        </el-form-item>
                      </el-col>


                      <el-col :span="12">
                        <el-form-item label="备注" prop="remarks">
                          <el-input v-model="form.remarks" type="textarea" placeholder="请输入内容" />
                        </el-form-item>
                      </el-col>


                      <el-col :span="12">
                        <el-form-item label="图片的ownerId" prop="photos">
                          <el-input v-model="form.photos" placeholder="请输入图片的ownerId" />
                        </el-form-item>
                      </el-col>


                      <el-col :span="12">
                        <el-form-item label="巡查人ID" prop="checkerId">
                          <el-input v-model="form.checkerId" placeholder="请输入巡查人ID" />
                        </el-form-item>
                      </el-col>


                      <el-col :span="12">
                        <el-form-item label="巡查人" prop="checkerName">
                          <el-input v-model="form.checkerName" placeholder="请输入巡查人" />
                        </el-form-item>
                      </el-col>


                      <el-col :span="12">
                        <el-form-item label="审核人ID" prop="auditId">
                          <el-input v-model="form.auditId" placeholder="请输入审核人ID" />
                        </el-form-item>
                      </el-col>


                      <el-col :span="12">
                        <el-form-item label="审核人" prop="auditName">
                          <el-input v-model="form.auditName" placeholder="请输入审核人" />
                        </el-form-item>
                      </el-col>




                      <el-col :span="12">
                        <el-form-item label="巡查时间" prop="checkTime">
                          <el-date-picker clearable
                                          v-model="form.checkTime"
                                          type="date"
                                          value-format="yyyy-MM-dd"
                                          placeholder="请选择巡查时间">
                          </el-date-picker>
                        </el-form-item>
                      </el-col>


                      <el-col :span="12">
                        <el-form-item label="审核时间" prop="auditTime">
                          <el-date-picker clearable
                                          v-model="form.auditTime"
                                          type="date"
                                          value-format="yyyy-MM-dd"
                                          placeholder="请选择审核时间">
                          </el-date-picker>
                        </el-form-item>
                      </el-col>



      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 用户导入对话框 -->
    <el-dialog :title="upload.title" :visible.sync="upload.open" width="400px" append-to-body>
      <el-upload
          ref="upload"
          :limit="1"
          accept=".xlsx, .xls"
          :headers="upload.headers"
          :action="upload.url + '?updateSupport=' + upload.updateSupport"
          :disabled="upload.isUploading"
          :on-progress="handleFileUploadProgress"
          :on-success="handleFileSuccess"
          :auto-upload="false"
          drag
      >
        <i class="el-icon-upload"></i>
        <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
        <div class="el-upload__tip text-center" slot="tip">
          <div class="el-upload__tip" slot="tip">
            <el-checkbox v-model="upload.updateSupport" /> 是否更新已经存在的用户数据
          </div>
          <span>仅允许导入xls、xlsx格式文件。</span>
          <el-link type="primary" :underline="false" style="font-size:12px;vertical-align: baseline;" @click="importTemplate">下载模板</el-link>
        </div>
      </el-upload>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitFileForm">确 定</el-button>
        <el-button @click="upload.open = false">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
  import { listHazardSections, getHazardSections, delData, addHazardSections, updateHazardSections } from "@/api/middleData/hazardSections";
  import { getToken } from "@/utils/auth";
  import Treeselect from "@riophae/vue-treeselect";
  import "@riophae/vue-treeselect/dist/vue-treeselect.css";

  export default {
    name: "HazardSections",
    components: {  },
    data() {
      return {
        // 遮罩层
        loading: true,
        // 选中数组
        ids: [],
        // 非单个禁用
        single: true,
        // 非多个禁用
        multiple: true,
        // 显示搜索条件
        showSearch: false,
        dictType:[],
        // 日期范围
        dateRange: [],
        // 总条数
        total: 0,
        // 山洪淹没区路段清单表格数据
        hazardSectionsList: null,
        // 弹出层标题
        title: "",
        // 部门树选项
        deptOptions: undefined,
        // 是否显示弹出层
        open: false,

        // 表单参数
        form: {},
        defaultProps: {
          children: "children",
          label: "label"
        },
        // 用户导入参数
        upload: {
          // 是否显示弹出层（用户导入）
          open: false,
          // 弹出层标题（用户导入）
          title: "",
          // 是否禁用上传
          isUploading: false,
          // 是否更新已经存在的用户数据
          updateSupport: 0,
          // 设置上传的请求头部
          headers: { Authorization: "Bearer " + getToken() },
          // 上传的地址
          url: process.env.VUE_APP_BASE_API + "/system/user/importData"
        },
        // 查询参数
        queryParams: {
          pageNum: 1,
          pageSize: 50,
            routeCode: null,
            routeName: null,
            province: null,
            period: null,
            startStake: null,
            endStake: null,
            length: null,
            routeSectionName: null,
            managementMaintenanceName: null,
            propertyUnitType: null,
            centerLon: null,
            centerLat: null,
            shape: null,
            managementMaintenanceId: null,
            routeSectionId: null,
            affects: null,
            remarks: null,
            photos: null,
            checkerId: null,
            checkerName: null,
            auditId: null,
            auditName: null,
            auditStatus: null,
            checkTime: null,
            auditTime: null
        },
        // 列信息
        columns: [
        { key: 0, label: `路线编码`, visible: true },
        { key: 1, label: `路线名称`, visible: true },
        { key: 2, label: `省份`, visible: true },
        { key: 3, label: `洪水年限`, visible: true },
        { key: 4, label: `起点桩号`, visible: true },
        { key: 5, label: `终点桩号`, visible: true },
        { key: 6, label: `长度（km）`, visible: true },
        { key: 7, label: `路段名称`, visible: true },
        { key: 8, label: `管理处名称`, visible: true },
        { key: 9, label: `产权单位类型`, visible: true },
        { key: 10, label: `中心点经度`, visible: true },
        { key: 11, label: `中心点纬度`, visible: true },
        { key: 12, label: `空间信息`, visible: true },
        { key: 13, label: `管理处id`, visible: true },
        { key: 14, label: `路段id`, visible: true },
        { key: 15, label: `是否影响道路安全(0:不影响;1:影响)`, visible: true },
        { key: 16, label: `备注`, visible: true },
        { key: 17, label: `图片的ownerId`, visible: true },
        { key: 18, label: `巡查人ID`, visible: true },
        { key: 19, label: `巡查人`, visible: true },
        { key: 20, label: `审核人ID`, visible: true },
        { key: 21, label: `审核人`, visible: true },
        { key: 22, label: `审核状态（0-未审核，1-审核通过，2-审核不通过）`, visible: true },
        { key: 23, label: `巡查时间`, visible: true },
        { key: 24, label: `审核时间`, visible: true }
        ],
        // 表单校验
        rules: {


        }
      };
    },
    watch: {
      // 根据名称筛选部门树
                      },
    created() {
      this.getList();
      // this.getDeptTree();
      // this.getConfigKey("sys.user.initPassword").then(response => {
      //   this.initPassword = response.msg;
      // });
    },
    methods: {
      /** 查询用户列表 */
      getList() {
        this.loading = true;
        // this.queryParams.createTimee = this.dateRange[0]
        // this.queryParams.createTimes = this.dateRange[1]
        listHazardSections(this.queryParams).then(response => {
          this.hazardSectionsList = response.rows;
          this.total = response.total;
          this.loading = false;
        });
      },
      // 取消按钮
      cancel() {
        this.open = false;
        this.reset();
      },
      // 表单重置
      reset() {
        this.form = {
            routeCode: null,
            routeName: null,
            province: null,
            period: null,
            startStake: null,
            endStake: null,
            length: null,
            routeSectionName: null,
            managementMaintenanceName: null,
            propertyUnitType: null,
            centerLon: null,
            centerLat: null,
            shape: null,
            managementMaintenanceId: null,
            routeSectionId: null,
            affects: null,
            remarks: null,
            photos: null,
            checkerId: null,
            checkerName: null,
            auditId: null,
            auditName: null,
            auditStatus: null,
            checkTime: null,
            auditTime: null
        };
        this.resetForm("form");
      },
      /** 搜索按钮操作 */
      handleQuery() {
        this.queryParams.pageNum = 1;
        this.getList();
      },
      /** 重置按钮操作 */
      resetQuery() {
        this.dateRange = [];
        this.resetForm("queryForm");
        this.handleQuery();
      },
      // 多选框选中数据
      handleSelectionChange(selection) {
        this.ids = selection.map(item => item.id);
        this.single = selection.length != 1;
        this.multiple = !selection.length;
      },
      // 表格点击勾选
      handleRowClick(row) {
        row.isSelected = !row.isSelected;
        this.$refs.table.toggleRowSelection(row);
      },
      // 勾选高亮
      rowStyle({ row, rowIndex }) {
        if (this.ids.includes(row.id)) {
          return { 'background-color': '#E1F0FF', color: '#333' }
        } else {
          return { 'background-color': '#fff', color: '#333' }
        }
      },
      /** 新增按钮操作 */
      handleAdd() {
        this.reset();
        this.open = true;
        this.title = "添加山洪淹没区路段清单";
      },
      /** 修改按钮操作 */
      handleUpdate(row) {
        this.reset();
        const id = row.id || this.ids;
        getHazardSections(id).then(response => {
          this.form = response.data;
          this.open = true;
          this.title = "修改山洪淹没区路段清单";
        });

      },
      /** 提交按钮 */
      submitForm: function() {
        this.$refs["form"].validate(valid => {
          if (valid) {
            if (this.form.id != null) {
              updateHazardSections(this.form).then(response => {
                this.$modal.msgSuccess("修改成功");
                this.open = false;
                this.getList();
              });
            } else {
              addHazardSections(this.form).then(response => {
                this.$modal.msgSuccess("新增成功");
                this.open = false;
                this.getList();
              });
            }
          }
        });
      },
      /** 删除按钮操作 */
      handleDelete(row) {
        const id = row.id || this.ids;
        if (row.id) { // 单个删除
          this.$modal.confirm('是否确认删除改数据？').then(function() {
            let data = { idList: [row.id]};
            return delData(data);
          }).then(() => {
            this.getList();
            this.$modal.msgSuccess("删除成功");
          }).catch(() => {});
        } else { // 批量删除
          this.$modal.confirm(`是否确认删除选中的 ${id.length} 条数据项？`).then(function () {
            let data = { idList: id};
            console.log(data)
            return delData(data);
          }).then(() => {
            this.getList();
            this.$modal.msgSuccess("删除成功");
          }).catch(() => {});
        }
      },

      /** 导出按钮操作 */
      handleExport() {

    this.download('middleData/hazardSections/export', {
      ...this.queryParams
    }, `hazardSections_${new Date().getTime()}.xlsx`)

      },
      /** 导入按钮操作 */
      handleImport() {
        this.upload.title = "用户导入";
        this.upload.open = true;
      },
      /** 下载模板操作 */
      importTemplate() {
        this.download('system/user/importTemplate', {
        }, `user_template.xlsx`)
      },
      // 文件上传中处理
      handleFileUploadProgress(event, file, fileList) {
        this.upload.isUploading = true;
      },
      // 文件上传成功处理
      handleFileSuccess(response, file, fileList) {
        this.upload.open = false;
        this.upload.isUploading = false;
        this.$refs.upload.clearFiles();
        this.$alert("<div style='overflow: auto;overflow-x: hidden;max-height: 70vh;padding: 10px 20px 0;'>" + response.msg + "</div>", "导入结果", { dangerouslyUseHTMLString: true });
        this.getList();
      },
      // 提交上传文件
      submitFileForm() {
        this.$refs.upload.submit();
      }
  }
  };
</script>
<style>
  .hasTagsView .app-main[data-v-078753dd]{
    background: #f5f7fa;
  }

  .tableDiv{
    background-color: white;
    padding-bottom: 10px;
  }
</style>
