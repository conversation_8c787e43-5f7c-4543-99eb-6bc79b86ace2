<template>
    <div>
        <el-dialog
            :title="title"
            :visible.sync="open"
            width="480px"
            append-to-body
            :close-on-click-modal="false"
            custom-class="audit-dialog"
        >
            <el-form ref="form" :model="form" :rules="rules" label-width="90px">
                <el-form-item label="审核时间" prop="auditTime" class="form-item">
                    <el-date-picker
                        clearable
                        v-model="form.auditTime"
                        type="date"
                        value-format="yyyy-MM-dd"
                        placeholder="请选择审核时间"
                        class="date-picker"
                    >
                    </el-date-picker>
                </el-form-item>
                <el-form-item label="审核人" prop="auditName" class="form-item">
                    <el-input
                        v-model="form.auditName"
                        placeholder="请输入审核人"
                        class="input-field"
                    ></el-input>
                </el-form-item>
                <el-form-item label="审核意见" prop="auditContent" class="form-item">
                    <el-input
                        type="textarea"
                        v-model="form.auditContent"
                        placeholder="请输入审核意见"
                        :rows="4"
                        class="textarea-input"
                    ></el-input>
                </el-form-item>
            </el-form>
            <div slot="footer" class="dialog-footer">
                <el-button type="primary" @click="handleAudit(1)" class="approve-btn">审核通过</el-button>
                <el-button type="danger" @click="handleAudit(2)" class="reject-btn">审核不通过</el-button>
            </div>
        </el-dialog>
    </div>
</template>

<script>
    import { parseTime } from "@/utils/ruoyi";

    export default {
        name: 'AuditDialog',
        props: {
            // 弹窗标题
            title: {
                type: String,
                default: '批量审核'
            },
            // 是否显示弹窗
            visible: {
                type: Boolean,
                default: false
            },
            // 选中的ID数组
            ids: {
                type: Array,
                default: () => []
            }
        },
        data() {
            return {
                // 是否显示弹出层
                open: false,
                // 表单参数
                form: {
                    ids: [],
                    auditContent: '',
                    auditTime: parseTime(new Date(), '{y}-{m}-{d}'),
                    auditStatus: null
                },
                // 表单校验
                rules: {

                }
            };
        },
        watch: {
            visible(val) {
                this.open = val;
            },
            open(val) {
                if (!val) {
                    this.$emit('update:visible', false);
                }
            },
            ids: {
                handler(newVal) {
                    this.form.ids = newVal;
                },
                immediate: true
            }
        },
        created() {
            // 设置默认审核时间为当前日期
            this.form.auditTime = parseTime(new Date(), '{y}-{m}-{d}');
        },
        methods: {
            // 取消按钮
            cancel() {
                this.open = false;
                this.reset();
            },
            // 表单重置
            reset() {
                this.form = {
                    ids: this.ids,
                    auditContent: '',
                    auditTime: parseTime(new Date(), '{y}-{m}-{d}'),
                    auditStatus: null
                };
                this.resetForm('form');
            },
            // 处理审核
            handleAudit(status) {
                this.$refs['form'].validate((valid) => {
                    if (valid) {
                        this.form.auditStatus = status;

                        // 通过事件向父组件提交审核信息
                        this.$emit('submit', {...this.form});
                        this.cancel();
                    }
                });
            },
            // 重置表单
            resetForm(formName) {
                if (this.$refs[formName]) {
                    this.$refs[formName].resetFields();
                }
            }
        }
    }
</script>

<style lang="scss" scoped>
.audit-dialog {
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);

    ::v-deep .el-dialog__header {
        padding: 20px 24px;
        border-bottom: 1px solid #f0f0f0;

        .el-dialog__title {
            font-size: 18px;
            font-weight: 500;
            color: #333;
        }

        .el-dialog__headerbtn {
            top: 20px;
            right: 20px;

            .el-dialog__close {
                font-size: 18px;
                color: #999;
                transition: all 0.3s;

                &:hover {
                    transform: rotate(90deg);
                    color: #666;
                }
            }
        }
    }

    ::v-deep .el-dialog__body {
        padding: 24px 30px;
    }

    ::v-deep .el-form-item {
        margin-bottom: 24px;

        .el-form-item__label {
            font-size: 14px;
            color: #333;
            font-weight: 500;
        }
    }

    .date-picker {
        width: 100%;

        ::v-deep .el-input__inner {
            height: 40px;
            line-height: 40px;
            border-radius: 8px;
            border: 1px solid #e4e7ed;
            transition: all 0.3s;

            &:hover, &:focus {
                border-color: #409EFF;
                box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.1);
            }
        }
    }

    .textarea-input {
        ::v-deep .el-textarea__inner {
            padding: 12px;
            border-radius: 8px;
            border: 1px solid #e4e7ed;
            transition: all 0.3s;
            resize: none;
            font-family: system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;

            &:hover, &:focus {
                border-color: #409EFF;
                box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.1);
            }

            &::placeholder {
                color: #c0c4cc;
            }
        }
    }

    .dialog-footer {
        display: flex;
        justify-content: center;
        gap: 16px;
        padding-top: 10px;

        .approve-btn, .reject-btn {
            min-width: 120px;
            height: 40px;
            border-radius: 20px;
            font-weight: 500;
            letter-spacing: 1px;
            transition: all 0.3s;
            border: none;
        }

        .approve-btn {
            background: linear-gradient(135deg, #3498db, #2980b9);

            &:hover {
                background: linear-gradient(135deg, #2980b9, #2573a7);
                transform: translateY(-1px);
                box-shadow: 0 4px 10px rgba(41, 128, 185, 0.3);
            }
        }

        .reject-btn {
            background: linear-gradient(135deg, #ff6b6b, #ee5253);

            &:hover {
                background: linear-gradient(135deg, #ee5253, #e74c3c);
                transform: translateY(-1px);
                box-shadow: 0 4px 10px rgba(231, 76, 60, 0.3);
            }
        }
    }

    ::v-deep .el-dialog__footer {
        padding: 10px 24px 24px;
        border-top: none;
    }
}
</style>
