<template>
  <div class="map-view" ref="mapRef"></div>
</template>

<script>
import { Feature, Map, View } from 'ol';
import { OSM, Vector as VectorSource, XYZ } from "ol/source";
import VectorLayer from 'ol/layer/Vector';
import TileLayer from 'ol/layer/Tile';
import { Fill, Stroke, Text, Style } from "ol/style";
import { GeometryCollection, LineString, MultiPolygon, Point, Polygon } from "ol/geom";
import Mask from "ol-ext/filter/Mask";
import Crop from "ol-ext/filter/Crop.js";
import axios from "axios";
import { GeoJSON, WKT } from 'ol/format';
import { defaults } from 'ol/control';
import { fromLonLat } from 'ol/proj';
import * as turf from '@turf/turf';

import { getShapeList } from "@/api/oneMap/deptInfo";
import { getMenuSub } from "@/api/oneMap/menuSub";
import cache from "@/plugins/cache";
import { addDataToMap, addVectorTile, removeLayer } from '../util/mapUtils';

var key = "cde0b56cf882626889981701109a7536";
const lineId = "1824259894327382017";

export default {
  props: {
    padding: {
      type: Array,
      default: () => [50, 0, 50, 30],
    }
  },
  data() {
    return {
      vectorSource: null,
      vectorLayer: [],
      feature: null,
      map: null,
      dataList: [], // 部门数据
    }
  },
  mounted() {
    this.__init();
  },
  unmounted() {
    this.map = null;
    removeLayer(this.map, 'dataLayer');
  },
  destroyed() {
    this.map = null;
  },
  methods: {
    async __init() {
      this.vectorSource = await this.getAreaList();
      this.vectorLayer = await this.addRegionLayer(this.vectorSource);
      this.initMap();
    },
    initMap() {
      this.map = new Map({
        target: this.$refs.mapRef,
        view: new View({
          projection: "EPSG:3857",
          //地图初始中心点
          center: fromLonLat([102.75530900000001, 24.95423900000002]),
          //地图初始显示级别
          minZoom: 6,
          zoom: 7,
          maxZoom: 20,
          padding: this.padding,
          enableRotation: true,
        }),
        layers: this.vectorLayer,
        controls: defaults({
          attribution: false,
          zoom: false,
          rotate: false,
          drag: false,
        }),
      });
      this.addShadowByExt(this.vectorSource);
      this.addLine();
      this.setDataToMap();


      // Set map view with rotation and tilt
      // this.map.setView(new View({
      //   center: fromLonLat([102.8207599, 24.8885797]), // Center coordinates
      //   zoom: 11,
      //   rotation: Math.PI / 10, // 30 degree rotation
      //   constrainRotation: false // Allow free rotation
      // }));

      // Add tilt effect by setting camera parameters
      // const view = this.map.getView();
      // view.animate({
      //   rotation: Math.PI / 10,
      //   duration: 2000,
      //   easing: function (t) {
      //     return Math.pow(t, 2);
      //   }
      // });
    },
    // 获取 行政区域
    async getAreaList(deptId = null) {
      return new Promise((resolve, reject) => {
        let localData = cache.session.getJSON('rangeData')
        if (!localData || (localData && !localData.length)) {
          this.$modal.loading();
          getShapeList({ sysDeptIds: deptId ? [deptId] : [] }).then((res) => {
            if (res.code == 200 && res.data) {
              cache.session.setJSON("rangeData", res.data);
              this.dataList = res.data
              let source = this.getSource(res.data)
              resolve(source);
            } else {
              resolve(null);
            }
          }).catch(err => {
            reject(err);
          }).finally(() => {
            this.$modal.closeLoading();
          });
        } else {
          let source = this.getSource(localData)
          this.dataList = localData;
          resolve(source);
        }
      });

    },
    // 获取source 数据
    getSource(data = []) {
      let arr = []
      for (let index = 0; index < data.length; index++) {
        const mgeom = data[index].mgeom;
        // // 解析WKT字符串
        const geometry = new WKT().readGeometry(mgeom);
        // 获取所有点的数组
        const coordinates = geometry.getCoordinates();
        arr[index] = turf.polygon(coordinates[0], { combine: 'yes' });
        // arr[index] = turf.multiPolygon(coordinates, { combine: 'yes' });
      }
      var union = turf.union(turf.featureCollection(arr));
      let mul = new MultiPolygon([union.geometry.coordinates]).transform('EPSG:4326', 'EPSG:3857');
      let mulFeature = new Feature(mul);
      this.feature = mulFeature;
      let vectorSource = new VectorSource();
      vectorSource.addFeature(mulFeature)
      return vectorSource;
    },
    // 添加数据到地图
    setDataToMap() {
      removeLayer(this.map, 'dataLayer');
      let vectorsLayer = addDataToMap(
        this.dataList,
        "dataLayer",
        null,
        true,
      );
      if (vectorsLayer) {
        this.map.addLayer(vectorsLayer);
      }

      // 移动到行政区域
      this.map.getView().fit(this.feature.getGeometry().getExtent(), {
        duration: 500,
      });
    },

    // 添加行政区域
    async addRegionLayer(data) {
      // return new Promise((resolve) => {
      let _this = this;
      let source = data
      let layer = new VectorLayer({
        source: source,
        style: new Style({
          renderer(coordinate, state) {
            let arr = coordinate[0][0];
            const ctx = state.context;
            _this.addOutlineShadow(ctx, {
              fillStyle: "rgba(3,42,65,0.8)",
              shadowOffsetY: 30,
              shadowOffsetX: 2,
              shadowColor: "rgba(3,42,65,0.8)",
              strokeStyle: "rgba(3,42,65,0.8)",
              coodArr: arr,
            });
            _this.addOutlineShadow(ctx, {
              fillStyle: "rgba(25,70,101,0.7)",
              shadowOffsetY: 25,
              shadowOffsetX: 5,
              shadowColor: "rgba(25,70,101,1)",
              strokeStyle: "rgba(30, 60, 95,0.5)",
              coodArr: arr,
            });
            _this.addOutlineShadow(ctx, {
              fillStyle: "rgba(25,70,101,0.5)",
              shadowOffsetY: 18,
              shadowOffsetX: 2,
              shadowColor: "rgba(25,70,101,0.7)",
              strokeStyle: "rgba(30, 60, 95,0.5)",
              coodArr: arr,
            });
            _this.addOutlineShadow(ctx, {
              fillStyle: "rgba(255,255,255,0.8)",
              shadowOffsetY: 14,
              shadowOffsetX: 2,
              shadowColor: "rgba(3,42,65,0.3)",
              strokeStyle: "rgba(3,42,65,1)",
              shadowBlur: 10,
              coodArr: arr,
            });
            _this.addOutlineShadow(ctx, {
              fillStyle: "transparent",
              shadowOffsetY: 10,
              shadowOffsetX: 2,
              shadowColor: "rgba(255,255,255,0.8)",
              strokeStyle: "rgba(255,255,255,0)",
              coodArr: arr,
            });
            _this.addOutlineShadow(ctx, {
              fillStyle: "rgba(255,255,255,0.8)",
              shadowOffsetY: 8,
              shadowOffsetX: 2,
              shadowColor: "rgba(255,255,255,0.6)",
              strokeStyle: "rgba(255,255,255,0)",
              shadowBlur: 10,
              coodArr: arr,
            });
            _this.addOutlineShadow(ctx, {
              fillStyle: "transparent",
              shadowOffsetY: 5,
              shadowOffsetX: 2,
              shadowColor: "rgba(70, 133, 171,1)",
              strokeStyle: "rgba(255,255,255,1)",
              shadowBlur: 10,
              coodArr: arr,
            });
            //白色
            _this.addOutlineShadow(ctx, {
              fillStyle: "rgba(1,49,93,1)",
              shadowOffsetY: 5,
              shadowOffsetX: 10,
              shadowColor: "rgba(255,255,255,0.1)",
              strokeStyle: "rgba(255,255,255,1)",
              shadowBlur: 10,
              coodArr: arr,
              lineWidth: 6,
            });
          },
        }),
      });

      return [layer];
    },
    //添加Mask
    addMask(options) {
      return new Mask({
        feature: options.feature,
        wrapX: false,
        inner: options.inner || false,
        fill: new Fill({ color: options.fillColor }),
        shadowColor: options.shadowColor || "rgba(0,0,0,0.5)",
        shadowWidth: options.shadowWidth || 10,
        // shadowMapUnits: true,
      });      
    },
    setLayerFilterCrop(layer, feature) {
      /**
       * 设置图层裁切
       */
      const crop = new Crop({
        feature: feature,
        inner: true,
        active: true,
        wrapX: true,
        shadowWidth: 2,
        shadowColor: "rgba(255,255,255,0.7)",
      });
      layer.addFilter(crop);
    },
    //添加外发光
    addShadowByExt(source) {
      const features = source.getFeatures();
      let layer = new VectorLayer({
        source: source,
      });
      this.map.addLayer(layer);
      //内发光
      let mask = this.addMask({
        fillColor: "rgba(1,49,93,1)",
        feature: features[0],
        inner: true,
        shadowColor: "rgba(255,255,255,0.8)",
        shadowWidth: 5,
      });
      //设置裁切
      this.setLayerFilterCrop(layer, features[0]);
      layer.addFilter(mask);
    },
    //添加外发光
    addOutlineShadow(ctx, option) {
      // 设置属性控制图形的外观
      ctx.fillStyle = option.fillStyle || "transparent";
      ctx.strokeStyle = option.strokeStyle || "transparent";
      ctx.lineWidth = option.lineWidth || 1;
      //  设置Y轴偏移量
      ctx.shadowOffsetY = option.shadowOffsetY || 20;
      //  设置X轴偏移量
      ctx.shadowOffsetX = option.shadowOffsetX || 2;
      //  设置模糊度
      ctx.shadowBlur = option.shadowBlur || 2;
      //  设置阴影颜色
      ctx.shadowColor = option.shadowColor || "#000";
      ctx.beginPath();
      let arr = option.coodArr || [];
      for (let i = 0; i < arr.length; i++) {
        const data = arr[i];
        if (i === 0) {
          ctx.moveTo(data[0], data[1]);
        } else {
          ctx.lineTo(data[0], data[1]);
        }
      }
      ctx.closePath();
      ctx.fill();
      ctx.stroke();
    },

    // 添加路线
    async addLine() {
      let lineObj = cache.session.getJSON("lineObj")
      let layerName = "路网信息";
      if (!lineObj) {
        this.$modal.loading();
        await getMenuSub(lineId)
          .then((res) => {
            lineObj = res.data;
            lineObj.borderColour = 'rgba(0,191,45,0.8)';
            let layer = addVectorTile(this.map, "name", layerName, JSON.stringify({ id: lineId }), lineObj, true, 10, 6, 256, false);
            this.map.addLayer(layer);
          })
          .finally(() => {
            this.$modal.closeLoading();
          });
      } else {
        lineObj.borderColour = 'rgba(0,191,45,0.8)';
        let layer = addVectorTile(this.map, "name", layerName, JSON.stringify({ id: lineId }), lineObj, true, 10, 6, 256, false);
        this.map.addLayer(layer);
      }
    },

    setVector(name = '', obj = {}) {
      setTimeout(() => {
        let layer = addVectorTile(this.map, "name", name, JSON.stringify(obj), obj, true, 10, 6, 256, false);
        this.map.addLayer(layer);
      }, 600)
    },
    removeVector(name = 'dataLayer') {
      removeLayer(this.map, name);
    },
  },

}
</script>

<style lang="scss" scoped>
.map-view {
  // position: fixed;
  // top: 100px;
  // left: 0;
  // right: 0;
  // bottom: 0;

  width: 100%;
  // height: 100%;

  transform: rotateX(45deg) translateY(20px) scale3d(0.9, 1.0, 1.0);
  height: 140vh;
  margin-top: -30vh;
}
</style>

