<template>
  <div class="el-table-body" :class="isBig ? 'big-table' : 'normal-table'">
    <el-table
      :data="data"
      height="100%"
      ref="rolltable"
      @mouseenter.native="stopScroll"
      @mouseleave.native="startScroll"
       @row-click="onClick"
    >
      <el-table-column
        v-for="(col, index) in columns"
        :prop="col.prop"
        :key="col.id"
        :label="col.label"
        :min-width="col.width"
        :align="index == 0 ? '' : 'center'"
        show-overflow-tooltip
      >
        <template slot-scope="{ row, $index }">
          <div v-if="col.prop === 'index'" :style="indexStyle($index)">
            {{ $index + 1 }}
          </div>
          <span :style="{ fontSize: isBig ? '0.36vw' : '1.4vh' }" v-else>
            {{ row[col.prop] }}
          </span>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>

<script>
import { isBigScreen } from "../util/utils";

export default {
  name: "tables",
  props: {
    columns: {
      type: Array,
      default: () => [],
    },
    data: {
      type: [Array,Object],
      default: () => [],
    },
    isScroll: {
      type: Boolean,
      default: true,
    },
  },
  data() {
    return {
      isBig: isBigScreen(),
      rolltimer: null,
    };
  },
  mounted() {
    this.$nextTick(() => {
      setTimeout(() => {
        this.tableScroll(false);
      }, 2500)
    });
  },
  destroyed() {
    this.tableScroll(true);
  },
  methods: {
    tableScroll(stop) {
      if (this.data && this.data.length <= 6) return; // 如果数据小于等于6行，则不进行滚动
      if (!this.isScroll) return; // 如果isScroll为false，则不进行滚动
      if (stop) {
        clearInterval(this.rolltimer);
        return;
      }
      const table = this.$refs.rolltable;
      const divData = table.bodyWrapper;
      if(!divData) return;
      this.rolltimer = setInterval(() => {
        divData.scrollTop += 1;
        if (
          divData.clientHeight + divData.scrollTop + 1 >
          divData.scrollHeight
        ) {
          if (table.tableData.length > 5) {
            divData.scrollTop = 0;
          }
        }
      }, 35);
      if (divData.scrollTop + divData.clientHeight >= divData.scrollHeight) {
        divData.scrollTop = 0;
      }
    },
    startScroll() {
      this.tableScroll(false);
    },

    stopScroll() {
      this.tableScroll(true);
    },
    indexStyle(index) {
      let bs = "";
      let border = "";
      let color = "";
      if (index === 0) {
        bs = "inset 0px 0px 15px 0px rgba(175,0,0,0.6)";
        border = "1px solid #AF0000";
        color = "#FF4646";
      } else if (index === 1) {
        bs = "inset 0px 0px 15px 0px rgba(175,107,0,0.6)";
        border = "1px solid #AF6B00";
        color = "#FD9500";
      } else if (index === 2) {
        bs = "inset 0px 0px 15px 0px rgba(175,156,0,0.6)";
        border = "1px solid #AF9C00";
        color = "#FCFF00";
      } else {
        bs = "inset 0px 0px 15px 0px rgba(0,115,175,0.6)";
        border = "1px solid #0073AF";
        color = "#00EAFF";
      }

      let obj = {
        textAlign: "center",
        width: this.isBig ? "50px" : "22px",
        height: this.isBig ? "50px" : "22px",
        lineHeight: this.isBig ? "50px" : "22px",
        background: "rgba(4,17,48,0.4)",
        boxShadow: bs,
        fontSize: this.isBig ? "0.45vw" : "",
        border,
        color,
      };
      return obj;
    },
    onClick(row) {
      this.$emit("row-click", row);
    },
  },
};
</script>

<style lang="scss" scoped>
@import "@/assets/styles/utils.scss";

.el-table-body {
  width: 100%;
  height: 100%;
  padding: 10px 3px;

  ::v-deep .el-table {
    background-color: unset;
    border: unset;
    overflow-x: hidden;

    &::-webkit-scrollbar {
      width: vwpx(12px);
      height: vwpx(12px);
    }

    &::-webkit-scrollbar-thumb {
      background-color: rgba(1, 102, 254, 0.3);
      border-radius: 2px;
    }

    &::-webkit-scrollbar-track {
      background-color: rgba(1, 102, 254, 0.2);
    }

    &::before {
      background-color: unset;
    }

    tr {
      background-color: unset;
    }

    td {
      color: #ffffff;
    }

    th.is-leaf {
      border-bottom: none;
    }

    thead {
      background-color: unset;
      color: #018cb3;
    }

    .cell {
      line-height: unset !important;
    }

    .el-table__header-wrapper tr th {
      background-color: rgba(2, 43, 91, 1);
      color: #018cb3 !important;
    }

    tbody {
      background-color: unset;
      border: none;

      .el-table__row {
        border-bottom: none !important;
        cursor: pointer;

        .el-table__cell {
          border-bottom: 1px solid rgba(2, 57, 128, 1);
          padding: 0;
          cursor: pointer;
        }
      }
    }

    .el-table__body tr:hover > td {
      background-color: unset;
    }

    .el-table__body tr.current-row > td.el-table__cell {
      background-color: rgba(0, 115, 232, 0);
      
    }

    .el-table__inner-wrapper::before {
      background-color: unset;
    }

    th.el-table__cell {
      background-color: unset;
      color: #ffffff;
    }

    .el-table__body-wrapper {
      &::-webkit-scrollbar {
        // 整个滚动条
        width: 0; // 纵向滚动条的宽度
        background: rgba(213, 215, 220, 0.3);
        border: none;
      }

      &::-webkit-scrollbar-track {
        // 滚动条轨道
        border: none;
      }
    }
  }
}

.normal-table {
  ::v-deep .el-table {
    .el-table__row {
      height: vwpx(90px) !important;
    }
    .el-table__header-wrapper tr th {
      font-size: 1.35vh;
    }
  }
}

.big-table {
  ::v-deep .el-table {
    .el-table__row {
      height: vwpx(97px) !important;
    }
    .el-table__header-wrapper tr th {
      font-size: 1.4vh;
    }
  }
}

.seamless-warp {
  height: 100%;
}
</style>
