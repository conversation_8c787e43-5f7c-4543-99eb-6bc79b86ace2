<template>
  <!-- 授权用户 -->
  <el-dialog title="选择养护路段" :visible.sync="visible" width="800px" top="5vh" append-to-body>
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true">
      <el-form-item label="养护路段名称" prop="maintenanceSectionName">
        <el-input
          v-model="queryParams.maintenanceSectionName"
          placeholder="请输入养护路段名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="" prop="departmentId" >
        <el-select v-model="queryParams.departmentId" filterable placeholder="请选择管养单位"
                   style="width: 240px">
          <el-option
            v-for="item in deptOptions"
            :key="item.id"
            :label="item.label"
            :value="item.id">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>
    <el-row>
      <el-table @row-click="clickRow" ref="table" :data="maintenanceSectionList" @selection-change="handleSelectionChange" height="260px">
        <el-table-column type="selection" width="55"></el-table-column>
        <el-table-column label="养护路段ID" align="center" width="120" prop="maintenanceSectionId"/>
        <el-table-column label="养护路段名称" align="center" prop="maintenanceSectionName"/>
      </el-table>
      <pagination
        v-show="total>0"
        :total="total"
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
        @pagination="getList"
      />
    </el-row>
    <div slot="footer" class="dialog-footer">
      <el-button type="primary" @click="handleSelectUser">确 定</el-button>
      <el-button @click="visible = false;resetQuery()">取 消</el-button>
    </div>
  </el-dialog>
</template>

<script>
import {queryDeptMaintenanceList} from "@/api/system/maintenanceSection";
import {addDeptMaintenance} from "@/api/system/maintenance";
import {getTreeStruct} from "@/api/tmpl";
export default {
  dicts: ['sys_normal_disable'],
  props: {
    // 部门ID
    deptId: {
      type: [Number, String]
    }
  },
  data() {
    return {
      // 遮罩层
      visible: false,
      // 选中数组值
      maintenanceSectionIdStr: [],
      // 总条数
      total: 0,
      // 未关联的养护路段数据
      maintenanceSectionList: [],
      deptOptions: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        departmentId: null,
        maintenanceSectionId: undefined,
        maintenanceSectionName: undefined,
      },
      //提交参数
      params: {
        deptId : null,
        maintenanceSectionIdStr : null
      }
    };
  },
  methods: {
    // 显示弹框
    show() {
      this.queryParams.deptId = this.deptId;
      this.getList();
      this.visible = true;
    },
    clickRow(row) {
      this.$refs.table.toggleRowSelection(row);
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.maintenanceSectionIdStr = selection.map(item => item.maintenanceSectionId);
    },
    // 查询表数据
    getList() {
      queryDeptMaintenanceList(this.queryParams).then(res => {
        this.maintenanceSectionList = res.rows;
        this.total = res.total;
      });
    },
    /** 查询管理处下拉树结构 */
    getDeptList() {
      return getTreeStruct({types:201}).then(response => {
        this.deptOptions = response.data;
      });
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    /** 提交选择养护路段操作 */
    handleSelectUser() {
      const deptId = this.queryParams.deptId;
      const maintenanceSectionIdStr = this.maintenanceSectionIdStr.join(",");
      if (maintenanceSectionIdStr == "") {
        this.$modal.msgError("请选择要分配的养护路段");
        return;
      }
      this.params.deptId = deptId;
      this.params.maintenanceSectionIdStr = maintenanceSectionIdStr;
      console.log(this.params)
      addDeptMaintenance(this.params).then(response => {
        this.$modal.msgSuccess("修改成功");
        this.$parent.handleDeptAsset(deptId);
        this.visible=false;
      });
    }
  },
  created() {
    this.getDeptList()
  }
};
</script>
