<template>
  <el-dialog title="选择值类型" :visible.sync="visible" width="1000px" top="5vh" append-to-body>
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true">
      <el-form-item label="编码" prop="code">
        <el-input
          v-model="queryParams.code"
          placeholder="请输入编码"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="名称" prop="name">
        <el-input
          v-model="queryParams.name"
          placeholder="请输入名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>
    <el-row>
      <el-table ref="table" :data="list" height="260px" stripe border highlight-current-row @current-change="handleCurrentChange" >
        <el-table-column label="采样方案" align="left" header-align="center" prop="sampleProjectIdDisplay" width="130" :show-overflow-tooltip="true"/>
        <el-table-column label="编码" align="left" header-align="center" prop="code" width="200" :show-overflow-tooltip="true"/>
        <el-table-column label="名称" align="left" header-align="center" prop="name" width="200" :show-overflow-tooltip="true"/>
        <el-table-column label="单位" align="left" header-align="center" prop="unit" width="80" :show-overflow-tooltip="true"/>
        <el-table-column label="小数位数" align="right" header-align="center" prop="numericScale"  width="80" />
        <el-table-column label="默认显示" align="center" header-align="center" prop="defaultShow"  width="80">
          <template slot-scope="scope">
            <el-tag v-if="scope.row.defaultShow" type="success">是</el-tag>
            <el-tag v-if="!scope.row.defaultShow" type="danger">否</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="显示顺序" align="right" header-align="center" prop="showOrders"  width="80" />
        <el-table-column label="存储类型" align="left" header-align="center" prop="storageType" width="80" :show-overflow-tooltip="true"/>
        <el-table-column label="默认采样类型名称" align="left" header-align="center" prop="defaultSampleTypeName" width="140" :show-overflow-tooltip="true"/>
        <el-table-column label="说明" align="left" header-align="center" prop="description" :show-overflow-tooltip="true"/>
      </el-table>
      <pagination
        v-show="total>0"
        :total="total"
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
        @pagination="getList"
      />
    </el-row>
    <div slot="footer" class="dialog-footer">
      <el-button type="primary" @click="handleSelect">确 定</el-button>
      <el-button @click="visible = false">取 消</el-button>
    </div>
  </el-dialog>
</template>
<script>
import { listValueType } from '@/api/jgjc/jc/valueType'
export default {
  data() {
    return {
      // 遮罩层
      visible: false,
      selectData:null,
      // 总条数
      total: 0,
      list: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        code:null,
        name: null,
        orderDirection: 'ASC'
      },
      //提交参数
      params: {}
    };
  },
  methods: {
    // 显示弹框
    show() {
      this.getList();
      this.visible = true;
    },
    handleCurrentChange(row) {
      this.selectData = row
    },
    // 查询表数据
    getList() {
      listValueType(this.queryParams).then(res => {
        this.list = res.data;
        this.total = res.total;
      });
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    /** 提交选择隧道操作 */
    handleSelect() {
      if(this.selectData!=null){
        this.$emit('select',this.selectData)
        this.visible = false;
        return
      }
      this.$modal.msgWarning("请选择一条值类型记录");
    }
  }
};
</script>
