<template>
  <div >
    <el-select :disabled="disabled" class="main-select-tree" ref="selectTree" v-model="selectValue" style="width: 100%;" :size="size" :placeholder="placeholder" :clearable="clearable" filterable :filter-method="filterMethod" @clear="handleClear" @visible-change="clearFilter">
      <el-option v-for="item in formatData(datas)" :key="item.value" :label="item.label" :value="item.value" style="display: none;" />
      <el-tree class="main-select-el-tree" ref="selecteltree"
               :data="datas"
               node-key="id"
               highlight-current
               :props="defaultProps"
               @node-click="handleNodeClick"
               :filter-node-method="filterNode"
               :current-node-key="selectValue"
               :expand-on-click-node="expandOnClickNode"
               :default-expand-all="expandAll"
               :render-after-expand="false"
      />
    </el-select>
  </div>
</template>

<script>
import { deptTreeSelect } from "@/api/tmpl";

export default {
  props: {
    value: {
      type: String,
      default: ''
    },
    deptType: { // 部门类型types
      type: Number,
      default: '',
      require: true
    },
    deptTypeList: {
      type: Array,
      default: null
    },
    size: {
      type: String,
      default: ''
    },
    placeholder: {
      type: String,
      default: ''
    },
    disabled: {
      type: Boolean,
      default: false
    },
    clearable: {
      type: Boolean,
      default: false
    },
    expandAll: {
      type: Boolean,
      default: true
    },
    onlySelectChild: {
      type: Boolean,
      default: false
    },
    filterKeys: {
      type: Array,
      default: () => []
    },
    dataRule: {
      type: Boolean,
      default: true
    }
  },
  computed: {
    selectValue: {
      get: function () {
        return String(this.value || '')
      },
      set: function(val) {
        this.$emit('input', val)
      }
    }
  },
  data() {
    return {
      // value: 6,
      expandOnClickNode: true,
      options:[],
      datas: [{
        id: 1,
        label: '云南',
        children: [{
          id: 2,
          label: '昆明',
          children: [
            {id: 3,label: '五华区',children:[{id: 8,label: '北辰小区'}]},
            {id: 4,label: '盘龙区'}
          ]
        }]
      }, {
        id: 5,
        label: '湖南',
        children: [
          {id: 6,label: '长沙'},
          {id: 7,label: '永州'}
        ]
      }],
      defaultProps: {
        children: 'children',
        label: 'label'
      }
    }
  },
  created() {
    this.getDept();
  },
  methods: {
    changeDomain(e) {
      console.log(e)
      this.$emit('change', e)
    },
    getDept() {
      var deptType ={types:this.deptType, deptTypeList: this.deptTypeList, dataRule: this.dataRule};
      console.log(deptType)
      deptTreeSelect(deptType).then(response => {
        if (this.filterKeys.length > 0) {
          response.data = response.data.filter(item => this.filterKeys.indexOf(item.label) == -1)
        }
        this.datas = response.data;
      });
    },
    handleClear() {
      this.$emit('input', '')
    },
    // 四级菜单
    formatData(data){
      let options = [];
      data.forEach((item,key) => {
        options.push({label:item.label,value:item.id});
        if(item.children){
          item.children.forEach((items,keys) => {
            options.push({label:items.label,value:items.id});
            if(items.children){
              items.children.forEach((itemss,keyss) => {
                options.push({label:itemss.label,value:itemss.id});
                if(itemss.children){
                  itemss.children.forEach((itemsss,keysss) => {
                    options.push({label:itemsss.label,value:itemsss.id});
                  });
                }
              });
            }
          });
        }
      });
      return options;
    },
    filterMethod(e) {
      this.$refs.selecteltree.filter(e);
      return true
    },
    filterNode(value, data) {
      if (!value) return true;
      return data.label.indexOf(value) !== -1;
    },
    clearFilter() {
      this.$refs.selecteltree.filter('');
    },
    getLabel() {
      const option = this.formatData(this.datas)
      return option.find(item => item.value == this.value).label
    },
    handleNodeClick(node){
      if (this.onlySelectChild) {
        if (!node.children ||node.children.length == 0) {
          this.$emit('input', node.id)
          this.$refs.selectTree.blur();
          this.$emit('node-selected', node);
        }
      } else {
        this.$emit('input', node.id)
        this.$refs.selectTree.blur();
        this.$emit('node-selected', node);
      }
      this.changeDomain(node.id)
    }
  }
}
</script>
<style>
.main-select-el-tree .el-tree-node .is-current > .el-tree-node__content{font-weight: bold; color: #409eff;}
.main-select-el-tree .el-tree-node.is-current > .el-tree-node__content{font-weight: bold; color: #409eff;}
</style>
