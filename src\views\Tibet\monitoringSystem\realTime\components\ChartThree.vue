<template>
  <div id="chart3" class="chart"></div>
</template>

<script>
import Echarts from '../../components/echarts.vue';
import { isBigScreen } from '../../utils/utils.js';
import * as echarts from "echarts";
import RealTimeWebSocket from "./realTimeWebSocket.js";

export default {
  name: 'ChartThree',
  props: {
    chartBoxHeight: {
      type: String,
      default: '260px',
    },
    theme: {
      type: String,
      default: 'dark',
    },
    activeName: {
      type: String,
      default: '1',
    }
  },
  components: { Echarts },
  data() {
    return {
      isBig: isBigScreen(),
      realTimeWebSocket: null,
      realTimePushData: {},
    }
  },
  created() {
    this.realTimeWebSocket = new RealTimeWebSocket(this.setRealPushChartInfoInit, this.setRealPushChart, this)
  },
  methods: {
    async initChart3(sensorInfo) {
      await this.connectRealTime()
      await this.getSensorInfo(sensorInfo)
      await this.startSendData()
      return true
    },
    //绘制实时数据（推送）
    setRealPushChartInfoInit(currentSensorInfo) {
      let myChart = echarts.getInstanceByDom(
        document.getElementById("chart3")
      );
      if (myChart !== undefined) {
        myChart.dispose();
      }
      myChart = echarts.init(document.getElementById("chart3"));
      if (currentSensorInfo === null) {
        this.setEmptyChart('无实时推送数据')
        return;
      }
      if (!currentSensorInfo.success) {
        this.setEmptyChart('无实时推送数据')
        return;
      }
      this.realTimePushData = {
        stampTime: [],
        seriesData: [],
        yAxis: [],
      }
      let valueTypeList = currentSensorInfo.data.valueTypeList
      let option = {
        toolbox: {
          right: 0,
          top: 0,
          itemSize: this.isBig ? 30 : 12,
          feature: {
            saveAsImage: {
              title: '保存',
              type: 'png',
              backgroundColor: this.theme === 'dark' ? 'rgba(4, 37, 72, 0.8)' : '#fff',
            },
          },
        },
        tooltip: {
          textStyle: {
            align: 'left',
            fontSize: this.isBig ? 24 : 14,
          },
          trigger: "axis",
          confine: true
        },
        xAxis: {
          type: 'category',
          name: "时间",
          nameLocation: "middle",
          nameTextStyle: {
            fontSize: this.isBig ? 24 : 12,
            color: this.theme === 'dark' ? '#fff' : '#000',
          },
          nameGap: this.isBig ? 40 : 20,
          axisLabel: {
            fontSize: this.isBig ? 24 : 11,
            color: this.theme === 'dark' ? '#fff' : '#000',
          },
          data: this.realTimePushData.stampTime
        },
        yAxis: [],
        dataZoom: [
          {
            type: "inside",
          },
        ],
        grid: [
          {
            top: this.isBig ? "30%" : '25%',
            left: "2%",
            right: 5 * (valueTypeList.length > 2 ? valueTypeList.length - 2 : 1) + "%",
            bottom: this.isBig ? "20%" : "5%",
            containLabel: true,
          },
        ],
        series: [],
        legend: {
          type: 'scroll',
          top: this.isBig ? "10%" : '0',
          data: [],
          selected: {},
          textStyle: {
            color: this.theme === 'dark' ? '#fff' : '#000',
            fontSize: this.isBig ? 25 : 14,
          },
        },
      };
      //加入多行数据
      for (let i = 0; i < valueTypeList.length; i++) {
        option.legend.data.push(valueTypeList[i].name);
        option.series.push({
          type: "line",
          name: valueTypeList[i].name,
          showSymbol: false,
          data: [],
          yAxisIndex: i,
        });
        // 如果超过3列数据，则多余的y轴需要偏移
        let name = ''
        if (valueTypeList[i].unit === '') {
          name = valueTypeList[i].name
        } else {
          name = valueTypeList[i].name + "/" + valueTypeList[i].unit
        }
        if (i > 1) {
          option.yAxis.push({
            name: name,
            nameTextStyle: {
              fontWeight: "bold",
              fontSize: this.isBig ? 24 : 12,
              color: this.theme === 'dark' ? '#fff' : '#000',
            },
            axisLine: {
              show: true,
              lineStyle: {
                width: '1'//坐标线的宽度
              },
              onZero: false,
            },
            axisLabel: {
              fontSize: this.isBig ? 22 : 11,
              color: this.theme === 'dark' ? '#fff' : '#000',
              formatter: function (value) {
                return value.toFixed(valueTypeList[i].precision >= 0 ? valueTypeList[i].precision : 4);
              },
            },
            min: 0,
            max: 0,
            interval: 0,
            splitNumber: 6,
            offset: 120 * (i - 1),
            splitLine: {
              show: true,
              lineStyle: {
                color: this.theme === 'dark' ? 'rgba(110, 112, 121, 0.50)' : '#eee',
              }
            },
            splitArea: {
              show: false,
            },
          });
        } else {
          option.yAxis.push({
            name: name,
            nameTextStyle: {
              fontWeight: "bold",
              fontSize: this.isBig ? 24 : 12,
              color: this.theme === 'dark' ? '#fff' : '#000',
            },
            axisLabel: {
              fontSize: this.isBig ? 22 : 11,
              color: this.theme === 'dark' ? '#fff' : '#000',
              formatter: function (value) {
                return value.toFixed(valueTypeList[i].precision >= 0 ? valueTypeList[i].precision : 4);
              },
            },
            min: 0,
            max: 0,
            interval: 0,
            splitNumber: 6,
            splitLine: {
              show: true,
              lineStyle: {
                color: this.theme === 'dark' ? 'rgba(110, 112, 121, 0.50)' : '#eee',
              }
            },
            splitArea: {
              show: false,
            },
          })
        }
      }
      this.realTimePushData.seriesData = option.series
      this.realTimePushData.yAxis = option.yAxis

      option && myChart.setOption(option);
    },
    //绘制实时数据（推送）
    setRealPushChart(newData) {
      let myChart = echarts.getInstanceByDom(
        document.getElementById("chart3")
      );
      // 传入时间戳
      let len = this.realTimePushData.stampTime.length;
      if (len > 400) {
        this.realTimePushData.stampTime.shift();
      }
      this.realTimePushData.stampTime.push(newData.stampTime)
      // 传入数据
      let list = newData?.data;
      for (let i = 0; i < list.length; i++) {
        if (len > 400) {
          this.realTimePushData.seriesData[i].data.shift();
        }
        this.realTimePushData.seriesData[i].data.push(list[i]);
        // y轴自适应
        let data = this.realTimePushData.seriesData[i].data
        //计算最大值与最小值
        let minValue = this.getMin(data); // 输出最小值
        let maxValue = this.getMax(data); // 输出最大值
        // 一些特殊情况处理
        if (maxValue === 0 && minValue === 0) {
          maxValue = 1
          minValue = -1
        } else {
          let delta = (maxValue - minValue) * 0.2
          if (delta === 0) {
            delta = 1
          }
          maxValue = maxValue + delta
          minValue = minValue - delta
        }
        // 如果超过3列数据，则多余的y轴需要偏移
        this.realTimePushData.yAxis[i].min = minValue
        this.realTimePushData.yAxis[i].max = maxValue
        this.realTimePushData.yAxis[i].interval = (maxValue - minValue) / 6
      }
      myChart.setOption({
        xAxis: {
          data: this.realTimePushData.stampTime
        },
        series: this.realTimePushData.seriesData,
        yAxis: this.realTimePushData.yAxis,
      });
    },
    // 暂时关闭推送
    async stopDrawRealTimePushData() {
      await this.disConnectRealTime()
    },
    //实时数据推送 断开hub
    async disConnectRealTime() {
      await this.realTimeWebSocket.stop()
    },
    //实时数据推送 连接hub
    async connectRealTime() {
      await this.realTimeWebSocket.start()
    },
    //实时数据推送 设置传感器信息
    async getSensorInfo(sensorInfo) {
      if (sensorInfo.sensorId === "") {
        this.$message({
          type: "warning",
          message: "请先选择传感器",
        });
        return;
      }
      let currSensorInfoCmd = {
        constructionCode: sensorInfo.structureCode,
        sensorId: sensorInfo.sensorId,
      }
      await this.realTimeWebSocket.getSensorInfo(currSensorInfoCmd)
    },
    //实时数据推送 开始接收数据
    async startSendData() {
      await this.realTimeWebSocket.startSendData()
    },
    //绘制空表 提示信息
    setEmptyChart(msg) {
      this.$emit('noData')
      let myChart = echarts.getInstanceByDom(
        document.getElementById("chart3")
      );
      if (myChart !== undefined) {
        myChart.dispose();
      }
      myChart = echarts.init(document.getElementById("chart3"));
      let option = {
        title: {
          text: msg,
          left: 'center',
          top: 'center',
          textStyle: {
            fontSize: this.isBig ? 32 : 14,
            color: '#606266',
            fontWeight: 700
          },
        },
      };
      option && myChart.setOption(option);
      //自适应大小
      window.onresize = function () {
        myChart.resize();
      };
    },
    // 公共方法-获取最小值
    getMin(arr) {
      if (arr.length === 0) {
        return 0;
      }
      let tmpValue = arr[0];
      arr.forEach((value, index) => {
        if (tmpValue > value) {
          tmpValue = value
        }
      })
      return tmpValue
    },
    // 公共方法-获取最大值
    getMax(arr) {
      if (arr.length === 0) {
        return 0;
      }
      let tmpValue = arr[0];
      arr.forEach((value, index) => {
        if (tmpValue < value) {
          tmpValue = value
        }
      })
      return tmpValue
    }
  },
}
</script>

<style lang="scss" scoped>
@import "@/assets/styles/utils.scss";

.chart {
  width: 100%;
  height: 100%;
}
</style>