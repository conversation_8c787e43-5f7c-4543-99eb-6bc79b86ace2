<template>
  <div style="position: relative">
    <div v-loading="loading" ref="canvas" id="luckysheet" class="luckysheet-content">
    </div>
    <el-button class="download-btn" type="primary" v-show="showDownload" @click="handleDownload">下载</el-button>
  </div>
</template>

<script>
import LuckyExcel from "luckyexcel";
import axios from "axios";
import {getToken} from "@/utils/auth";
export default {
  name: "Luckysheet",
  props: {
    /* Excel文件的URL */
    fileUrl: {
      type: String,
      required: true,
    },
    /* 编辑器的内容 */
    // value: {
    //   type: String,
    // },
    /* excel名称 */
    title: {
      type: String,
      default: "",
    },
    /* 只读 */
    allowEdit: {
      type: Boolean,
      default: false,
    },
    /* 行选择模式 */
    rowSelect: {
      type: Boolean,
      default: false,
    },
    /* 选取范围，列表 */
    rangeSelect: {
      type: Array,
      default: null,
    },
    excelOptions: {
      type: Object,
      default: () => {}
    },
    hasToken: {
      type: <PERSON>olean,
      default: false,
    },
    showDownload: {
      type: Boolean,
      default: false,
    },
    fileName: {
      type: String,
      default: '',
    }
  },
  data() {
    return {
      luckysheet: null,
      loading: true,
      options: {
        container: "luckysheet", //容器id名
        title: this.title, // 表 头名
        lang: "zh", // 中文
        showtoolbar: false, // 是否显示工具栏
        showinfobar: false, // 是否显示顶部信息栏
        rowHeaderWidth: 0, // 隐藏列号
        columnHeaderHeight: 0,    // 隐藏行号
        sheetFormulaBar: false, // 是否显示公式栏
        showsheetbar: true, // 是否显示底部sheet按钮
        showstatisticBarConfig:{
          count: false, // 计数栏
          zoom: false, // 缩放
        },
        showsheetbarConfig:{
			    add: false,
			    menu: false,
		    },
        forceCalculation: true, //强制刷新公式
        allowEdit: this.allowEdit,
        enableAddRow:false,
        enableAddBackTop:false,
        showtoolbarConfig: {
          chart: false, // '图表'（图标隐藏，但是如果配置了chart插件，右击仍然可以新建图表）
        },
        hook: {
          //   sheetMouseup: this.handleSheetMouseup,
          //   sheetMousemove: this.handleSheetMousemove,
          //   rangeSelect: this.handleRangeSelect,
          //   rangeSelect: function (index, sheet) {
          //     console.info("index", index);
          //     console.info("sheet", sheet);
          //   },
        },
      },
      blob: null
    };
  },
  created() {},
  watch: {
    fileUrl: {
      handler(val) {
        Object.assign(this.options, this.excelOptions)
        this.getExcel();
      },
      immediate: true,
    },
  },
  mounted() {
    // this.getExcel();
  },
  methods: {
    async getExcel() {
      try {
        this.loading = true;

        // 判断 Luckysheet 是否已经存在实例
        if (window.luckysheet && window.luckysheet.destroy) {
          window.luckysheet.destroy();
        }

        const res = await axios({
          method: "get",
          url: this.fileUrl,
          responseType: "arraybuffer",
            headers: this.hasToken ? {
              'Authorization': 'Bearer ' + getToken(),
            } : {}
        }).then((res) => {
          const data = res.data;
          this.blob = new Blob([data], {
            type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
          });
          //   console.log("====blob====", blob);
          const file = new window.File(
            [this.blob], // blob
            "Filename.xlsx",
            {
              type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
            }
          );
          //   console.log("====file====", file);
          LuckyExcel.transformExcelToLucky(
            file,
            (exportJson, luckysheetfile) => {
              //   console.log("transformExcelToLucky", file, exportJson);
              if (exportJson.sheets == null || exportJson.sheets.length == 0) {
                ElMessage.warning("读取excel文件内容失败");
                return;
              }
              // 调用初始化方法
              this.initExcel(exportJson);
            }
          );
        });
      } catch (err) {
        console.error(err);
      }
    },
    handleDownload() {
      saveAs(this.blob, this.fileName)
    },

    // 初始化对象
    initExcel(json) {
      //   this.$nextTick(() => {
      var _this = this; //注意这里要重新指定下this对象。
      // 先销毁 避免反复创建

      window.luckysheet.destroy();

      this.options.data = json.sheets;
      if (this.rowSelect) {
        (this.options.columnHeaderHeight = 0), //行头宽度
          (this.options.showsheetbar = false), //是否显示底部sheet按钮
          // hook是字典，增加一个键值对
          (this.options.hook = {
            sheetMouseup: this.handleSheetMouseup,
          });
      }

      // 创建表单
      window.luckysheet.create(this.options);
      if (this.rangeSelect) {
        const range = this.rangeSelect;
        if (this.rowSelect) {
          range.map((item) => {
            item.column = [0, window.luckysheet.getSheetData()[0].length - 1];
          });
        }
        window.luckysheet.setRangeShow(this.rangeSelect);
      }
      this.loading = false
      //   this.listenRowChange();
      // var that = this;
      // this.$refs.canvas.addEventListener("click", async function () {
      //   try {
      //     const xml = await window.luckysheet.getluckysheetfile();
      //     that.$emit("input", JSON.stringify(xml));
      //   } catch (error) {
      //     that.$modal.msgError("实时保存失败" + error);
      //   }
      // });
      //   });
    },
    // 监听选中区域
    handleSheetMousemove(cell, postion, sheetFile, moveState, ctx) {
      if (moveState.selectStatus) {
        let row = [0, postion.r];
        let column = [0, window.luckysheet.getSheetData()[0].length - 1];
        window.luckysheet.setRangeShow([{ row: row, column: column }]);
      }
    },
    handleSheetMouseup(cell, postion, sheetFile, moveState, ctx) {
      if (
        moveState.selectStatus ||
        moveState.rowsSelectedStatus ||
        moveState.cellRightClick
      ) {
        let row = [postion.r, postion.r];
        let column = [0, window.luckysheet.getSheetData()[0].length - 1];
        window.luckysheet.setRangeShow([{ row: row, column: column }]);
      }
    },
    handleRangeSelect(index, sheet) {
      const range = window.luckysheet.getRange()[0];
      let { row, column } = range;
      row = [0, row[1]];
      column = [0, window.luckysheet.getSheetData()[0].length - 1];
      //   window.luckysheet.setRangeShow([{ row: row, column: column }]);
    },
    // 监听按行统计
    listenRowChange() {
      const _this = this;
      console.log(
        "====luckysheetEventHandler====",
        window.luckysheet.luckysheetEventHandler
      );
      window.luckysheet.luckysheetEventHandler = function (eventType, data) {
        console.log(eventType);
        if (eventType === "cellSelect") {
          const { r, c } = data;
          if (r && r.length > 0) {
            const r1 = r[0].row[0];
            const r2 = r[0].row[1];
            // 只选择行
            window.luckysheet.setRange([
              r1,
              0,
              r2,
              window.luckysheet.getSheetData()[0].length - 1,
            ]);
          }
        }
      };
    },
  },
};
</script>
<style lang="css" scoped>
.luckysheet-content {
  margin: 0px;
  padding: 0px;
  width: 100%;
  height: calc(100vh - 230px);
  left: 0px;
  top: 40px;
  bottom: 0px;
  z-index: 99999;
}
.download-btn {
  position: absolute;
  bottom: 20px;
  right: 20px;
}
</style>
