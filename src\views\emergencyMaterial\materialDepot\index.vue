<template>
  <div class="app-container maindiv">
    <el-row>
      <el-col :span="24" :xs="24">
        <el-row>
          <el-form
            ref="queryForm"
            :model="queryParams"
            size="mini"
            :inline="true"
            label-width="68px"
          >
            <el-form-item>
              <selectTree
                :key="'domainId'"
                style="width: 240px"
                v-model="queryParams.domainId"
                :dept-type="100"
                :expand-all="false"
                placeholder="管养单位"
                clearable
                filterable
              />
            </el-form-item>
            <el-form-item>
              <el-input v-model="queryParams.name" placeholder="物资库名称" />
            </el-form-item>
            <el-form-item>
              <el-input v-model="queryParams.code" placeholder="物资库编码" />
            </el-form-item>
            <el-form-item>
              <el-input v-model="queryParams.person" placeholder="管理人员" />
            </el-form-item>
            <el-form-item>
              <el-button
                type="primary"
                icon="el-icon-search"
                size="mini"
                @click="handleQuery"
                >搜索</el-button
              >
              <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
                >重置</el-button
              >
            </el-form-item>
          </el-form>
        </el-row>
      </el-col>
    </el-row>
    <!--操作按钮区开始-->
    <el-row :gutter="10" class="mb8" v-if="hasHandle">
      <el-col :span="1.5">
        <el-button
          type="primary"
          icon="el-icon-plus"
          size="mini"
          @click="openDetailDialog(null)"
          >新增
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          icon="el-icon-view"
          size="mini"
          @click="handleOpenOperate"
          >操作记录
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          icon="el-icon-download"
          size="mini"
          @click="exportList"
          >导出
        </el-button>
      </el-col>
      <right-toolbar
        :showSearch.sync="showSearch"
        @queryTable="handleQuery"
        :columns="columns"
      ></right-toolbar>
    </el-row>
    <el-row>
      <div class="draggable">
        <el-table v-adjust-table
          size="mini"
          style="width: 100%"
          v-loading="loading"
          border
          :data="tableData"
          row-key="id"
          ref="dataTable"
          stripe
          highlight-current-row
          @row-click="handleClickRow"
          :height="!hasHandle?tableHeight:showSearch ? 'calc(100vh - 330px)' : 'calc(100vh - 270px)'"
        >
          <el-table-column
            label="序号"
            align="center"
            type="index"
            width="50"
          />
          <template v-for="(column, index) in columns">
            <el-table-column
              :label="column.label"
              v-if="column.visible"
              align="center"
              :prop="column.field"
              :width="column.width"
              show-overflow-tooltip
            >
              <template slot-scope="scope">
                <dict-tag
                  v-if="column.dict"
                  :options="dict.type[column.dict]"
                  :value="scope.row[column.field]"
                />
                <template v-else-if="column.slots">
                  <RenderDom
                    :row="scope.row"
                    :index="index"
                    :render="column.render"
                  />
                </template>
                <span v-else-if="column.isTime">{{
                  parseTime(scope.row[column.field], "{y}-{m}-{d}")
                }}</span>
                <span v-else>{{ scope.row[column.field] }}</span>
              </template>
            </el-table-column>
          </template>
          <el-table-column
            label="操作"
            fixed="right"
            align="center"
            width="250"
            class-name="small-padding fixed-width"
            v-if="hasHandle"
          >
            <template slot-scope="scope">
              <el-button
                type="text"
                @click="showInfo(scope.row)"
                icon="el-icon-view"
                >查看</el-button
              >
              <el-button
                type="text"
                @click="openDetailDialog(scope.row)"
                icon="el-icon-edit"
                >编辑</el-button
              >
              <el-button
                type="text"
                @click="handleDelete(scope.row)"
                icon="el-icon-delete"
                >删除</el-button
              >
            </template>
          </el-table-column>
        </el-table>
        <pagination
          v-show="total > 0"
          :total="total"
          :page.sync="queryParams.pageNum"
          :limit.sync="queryParams.pageSize"
          @pagination="handleQuery"
        />
      </div>
    </el-row>
    <el-dialog
      title="操作记录"
      :visible.sync="openOperateInfo"
      size="50%"
      destroy-on-close
      v-if="openOperateInfo"
    >
      <el-timeline style='height: 80vh; overflow-y: scroll' v-loading='loading'>
        <el-timeline-item
          v-for="(activity, index) in handleList"
          :key="index"
          color='#0bbd87'
          hide-timestamp
        >
          <div class="time-line-item">
            <div class="time-line-title">
              {{ activity.typeName }}
            </div>
            <div class="time-line-content">
              操作人：{{ activity.operator }}
            </div>
            <div class="time-line-content">
              附件：<el-link type='primary' :underline="false" @click="handleViewFile(activity)">查看</el-link>
            </div>
            <div class="time-line-content">
              详情：<el-link type='primary' :underline="false" @click="handleViewReport(activity)">查看</el-link>
            </div>
          </div>
        </el-timeline-item>
      </el-timeline>
    </el-dialog>
    <el-dialog
      title="站点物资"
      append-to-body
      modal-append-to-body
      :visible.sync="infoDialog"
      width="70%"
      v-if="infoDialog"
    >
      <info @close="modelClose" :rowData="rowData"></info>
    </el-dialog>

    <el-dialog
      :title="drawerTitle"
      destroy-on-close
      :visible.sync="drawer"
      :close-on-click-modal="false"
      width="70%"
      v-if="drawer"
    >
      <detail @close="handleCloseDetail" :row-data="rowData"></detail>
    </el-dialog>
    <el-dialog
      title="附件查看"
      append-to-body
      modal-append-to-body
      :visible.sync="fileDialog"
      width="70%"
      v-if="fileDialog"
    >
      <file-upload v-model="fileId" :owner-id="fileId" :forView="true"></file-upload>
    </el-dialog>
    <IFramePreview ref="iframeRef" :srcdoc="preview.html" :down-url="preview.url" :file-name="preview.fileName"></IFramePreview>
  </div>
</template>

<script>
import Detail from "./detail.vue";
import selectTree from "@/components/DeptTmpl/selectTree.vue";
import EventTreeInfo from "../component/eventTreeInfo.vue"
import info from "./info.vue";
import {
  getMaterialDepotList,
  delMaterialDepot,
  getNodeInfo,
  getOperationRecord,
} from '@/api/emergencyMaterial/materialDepot'
import { previewReport as previewReport0 } from '@/api/emergencyMaterial/materialOut'
import { previewReport as previewReport1 } from '@/api/emergencyMaterial/materialWriteOff'
import { previewReport as previewReport2 } from '@/api/emergencyMaterial/materialIn'
import { previewReport as previewReport3 } from '@/api/emergencyMaterial/materialReplenish'
import { previewReport as previewReport4 } from '@/api/emergencyMaterial/materialWriteOffReplenish'
import { previewReport as previewReport5 } from '@/api/emergencyMaterial/materialMaintain'
import { previewReport as previewReport6 } from '@/api/emergencyMaterial/materialStock'
import IFramePreview from '@/components/IFramePreview/index.vue'

export default {
  components: {
    IFramePreview,
    selectTree,
    Detail,
    EventTreeInfo,
    info,
    RenderDom: {
      functional: true,
      props: {
        row: Object,
        index: Number,
        render: Function,
      },
      render(createElement, ctx) {
        const { row, index } = ctx.props;
        return ctx.props.render(row, index);
      },
    },
  },
  dicts: [],

  data() {
    return {
      showSearch: false,
      queryParams: {
        pageNum: 1,
        pageSize: 10,
      },
      total: 0,
      loading: false,
      columns: [
        {
          key: 0,
          width: 100,
          field: "code",
          label: `物资库编码`,
          visible: true,
        },
        {
          key: 1,
          width: 100,
          field: "name",
          label: `物资库名称`,
          visible: true,
        },
        {
          key: 2,
          field: "maiSecName",
          label: `线路名称`,
          visible: true,
        },
        {
          key: 3,
          field: "domainName",
          label: `管养单位`,
          visible: true,
        },
        {
          key: 4,
          width: 100,
          field: "coverMaiSecName",
          label: `覆盖路段`,
          visible: true,
        },
        {
          key: 5,
          field: "milestr",
          label: `桩号位置`,
          visible: true,
        },
        {
          key: 6,
          width: 100,
          field: "struct",
          label: `建筑结构`,
          visible: true,
        },
        {
          key: 7,
          width: 100,
          field: "isNew",
          label: `原有或新建`,
          visible: true,
          slots: true,
          render: (row, index)=> {
            return (
              <span>{row.isNew == 1 ? '新建':'原有'}</span>
            )
          }
        },
        {
          key: 7,
          width: 120,
          field: "describe",
          label: `物资库介绍`,
          visible: true,
        },
        {
          key: 8,
          width: 100,
          field: "area",
          label: `面积(㎡)`,
          visible: true,
        },
        {
          key: 9,
          width: 100,
          field: "person",
          label: `管理人员`,
          visible: true,
        },
        {
          key: 10,
          width: 100,
          field: "telNumber",
          label: `联系电话`,
          visible: true,
        },
        {
          key: 11,
          width: 100,
          field: "remark",
          label: `备注`,
          visible: true,
        },
      ],
      tableData: [],
      rowData: null,
      drawerTitle: "新增物资库",
      drawer: false,
      infoDialog: false,
      openOperateInfo: false,
      formData: {},
      routeList: [],
      handleList: [],
      fileDialog: false,
      fileId: '',
      preview: {
        html: '',
        url: '',
        fileName: ''
      },
    };
  },
  props: {
    hasHandle: {
      type: Boolean,
      default: true,
    },
    filterColumn: {
      type: Array,
      default: ()=> [],
      required: false
    },
    tableHeight: {
      type: String,
      default: 'auto'
    }
  },
  computed: {},
  watch: {
    filterColumn: {
      handler(val) {
        if (val && val.length > 0) {
          let tempColumns = JSON.parse(JSON.stringify(this.columns))
          val.forEach((item) => {
            tempColumns = tempColumns.filter((val) => val.field != item);
          });
          this.columns = tempColumns
        }
      },
      immediate: true,
      deep: true,
    },
  },
  created() {
    this.handleQuery();
  },
  mounted() {},
  methods: {
    handleQuery() {
      this.loading = true;
      getMaterialDepotList(this.queryParams).then((res) => {
        this.loading = false;
        this.tableData = res.rows;
        this.total = res.total;
      });
    },
    resetQuery() {
      this.queryParams = {
        pageNum: 1,
        pageSize: 50,
      };
      this.handleQuery()
    },
    handleCloseDetail() {
      this.rowData = null;
      this.drawer = false;
      this.handleQuery();
    },
    openDetailDialog(row) {
      if (row) {
        this.drawerTitle = '编辑物资库'
      } else {
        this.drawerTitle = '新增物资库'
      }
      this.rowData = row
      this.drawer = true;
    },
    handleDelete(row) {
      this.$modal.confirm("是否删除").then(() => {
        delMaterialDepot(row.id).then(res=> {
          this.$message.success('删除成功')
          this.handleQuery()
        })
      });
    },
    handleClickRow(row) {
      this.rowData = row;
      if (!this.hasHandle) {
        this.$emit('depotSelect', row)
      }
    },
    handleOpenOperate() {
      if (!this.rowData || !this.rowData.id) {
        this.$modal.msgError("请选择一条数据");
        return;
      }
      getOperationRecord(this.rowData.id).then(res=> {
        this.handleList = res.data || []
        this.openOperateInfo = true;
      })
    },
    showInfo(row) {
      this.rowData = row;
      this.infoDialog = true;
    },
    // 导出清单按钮
    exportList() {
      this.download(
        "manager/emergency/material/export",
        { ...this.queryParams },
        `物资库_${new Date().getTime()}.xlsx`,
        {
          headers: { "Content-Type": "application/json;" },
          parameterType: "body",
        }
      );
    },
    modelClose() {
      this.drawer = false;
      this.openOperateInfo = false;
      this.infoDialog = false;
    },
    handleViewFile(row) {
      this.fileId = row.fileId
      this.fileDialog = true
    },
    handleViewReport(row) {
      let reportApi = null
      this.loading = true
      switch (row.type) {
        case 0:
          reportApi = previewReport0
          break;
        case 1:
          reportApi = previewReport1
          break;
        case 2:
          reportApi = previewReport2
          break;
        case 3:
          reportApi = previewReport3
          break;
        case 4:
          reportApi = previewReport4
          break;
        case 5:
          reportApi = previewReport5
          break;
        case 6:
          reportApi = previewReport6
          break;
        default: return
      }
      reportApi(row.id).then(res => {
        this.preview.html = res.data.html
        this.preview.url = res.data.downUrl
        this.preview.fileName = res.data.fileName
        this.$refs.iframeRef.visible = true
      }).finally(() => {
        this.loading = false
      })
    }

  },
};
</script>

<style lang="scss" scoped>
.time-line-item {
  .time-line-title {
    font-size: 20px;
    font-weight: bold;
  }
  .time-line-content {
    line-height: 38px;
  }
}
::v-deep .el-timeline-item__tail {
  border-color: #409EFF;
}
</style>
<style lang="scss" scoped>
@import "@/assets/styles/business.scss";
</style>
