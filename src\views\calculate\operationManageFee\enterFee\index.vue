<template>
  <div class="app-container maindiv">
    <el-row :gutter="20" style="height: 100%;">
      <!--部门数据-->
      <el-col :span="relaNav ? 5 : 0" :xs="24" class="leftDiv">
        <!--折叠图标-->
        <div class="leftIcon" @click="relaNav = false">
          <span class="el-icon-caret-left"></span>
        </div>
        <left-tree @query="leftTreeQuery"></left-tree>
      </el-col>
      <!--角色数据-->
      <el-col :span="relaNav ? 19 : 24" :xs="24" style="height: 100%;">
        <!--展开图标-->
        <div v-show="!relaNav" class="rightIcon" @click="relaNav = true">
          <span class="el-icon-caret-right"></span>
        </div>
        <div class="right-container">
          <el-row>
            <el-col :span="24" :xs="24" class="mb8">
              <el-button icon="el-icon-plus"
                         size="mini"
                         type="primary"
                         v-if="type == 'edit'"
                         v-has-menu-permi="['operate:metering:add']"
                         @click="openAddModel = true">新增计量单
              </el-button>
            </el-col>
            <el-col :span="24">
              <el-table v-adjust-table
                v-if="topColumn.length > 0"
                ref="dataTable"
                v-loading="loading"
                :data="tableDataTop"
                :height="200"
                border
                highlight-current-row
                row-key="id"
                size="mini"
                stripe
                style="width: 100%"
              >
                <el-table-column
                  align="center"
                  label="公司类型"
                  prop="type">
                </el-table-column>
                <el-table-column
                  align="center"
                  label="总费用"
                  prop="sum">
                  <template slot-scope="scope">
                    <span v-if="scope.row.sum">{{ scope.row.sum.toFixed(2) }}</span>
                  </template>
                </el-table-column>
                <el-table-column v-for="(item,index) in topColumn" :label="item.numberName" align="center"
                                 prop="numberName">
                  <el-table-column :label="item.name" :prop="'column' + index" align="center">
                    <template slot-scope="scope">
                      <span v-if="scope.row['column' + index]">{{ scope.row['column' + index].toFixed(2) }}</span>
                    </template>
                  </el-table-column>
                </el-table-column>
              </el-table>
              <el-empty v-else image-size="100" style="height: 200px;background-color: white"/>
            </el-col>
          </el-row>
          <!--操作按钮区开始-->
          <el-row :gutter="15" style="margin-top: 10px;height: calc(100% - 236px)">
            <el-col :span="relaNavRight ? 5 : 0" :xs="24" style="padding: 0; height: 100%;">
              <div class="RightDiv">
                <div class="leftIcon" style="top: 210px" @click="relaNavRight = false">
                  <span class="el-icon-caret-left"></span>
                </div>
                <right-tree ref="rightTree" @query="rightTreeQuery"></right-tree>
              </div>
            </el-col>
            <el-col :span="relaNavRight ? 19 : 24" :xs="24" style="height: 100%;">
              <div v-show="!relaNavRight" class="rightIcon" style="top: 210px" @click="relaNavRight = true">
                <span class="el-icon-caret-right"></span>
              </div>
              <div class="mb10" v-if="columns.length > 0">
                <el-button v-if="detailParams.status == 0 && detailEdit && type == 'edit'" type="primary" @click="handleAddDetail" v-has-menu-permi="['operate:meteringDetail:add']">新增</el-button>
                <el-button v-if="detailParams.status == 0 && detailParams.code && type == 'edit'" type="primary" @click="handleEdit" v-has-menu-permi="['operate:metering:edit']">编辑计量单</el-button>
                <el-button v-if="detailParams.status == 0 && detailParams.code && type == 'edit'" type="primary" @click="handleRemove" v-has-menu-permi="['operate:metering:remove']">删除计量单</el-button>
                <el-button v-if="detailParams.status == 0 && detailParams.code && type == 'edit'" type="primary" v-has-menu-permi="['operate:metering:process']" @click="handleSubmit">提交计量单</el-button>
                <el-button v-if="detailParams.status != 0 && detailParams.code" type="primary" v-has-menu-permi="['operate:metering:withdraw']" @click="handleWithdraw">撤回计量单</el-button>
                <el-button v-if="detailParams.children" type="primary" @click="handleViewOperate">审核意见</el-button>
                <el-button v-if="detailParams.children" type="primary" @click="exportList">导出清单</el-button>
                <el-button v-if="detailParams.children.length == 0" type="primary" @click="exportDetailList">导出明细清单</el-button>
              </div>
              <div class="draggable" style="height: calc(100% - 38px);">
                <template v-if="columns.length > 0">
                  <el-table v-adjust-table
                    ref="btnTable"
                    v-loading="loading"
                    :data="tableData"
                    border
                    height="90%"
                    highlight-current-row
                    row-key="id"
                    size="mini"
                    stripe
                    style="width: 100%"
                    @row-click="handleRowClick"
                    @expand-change="loadData"
                  >
                    <el-table-column type="expand">
                      <template slot-scope="props">
                        <el-table v-adjust-table ref="expandTable" :data="props.row.children" border size="mini">
                          <el-table-column
                            align="center"
                            label="序号"
                            type="index"
                            width="50"
                          />
                          <template v-for="(column,index) in expandColumns">
                            <el-table-column v-if="column.visible"
                                             :label="column.label"
                                             :prop="column.field"
                                             align="center">
                              <template slot-scope="scope">
                                <dict-tag v-if="column.dict" :options="dict.type[column.dict]" :value="scope.row[column.field]"/>
                                <template v-else-if="column.slots">
                                  <RenderDom :index="index" :render="column.render" :row="scope.row"/>
                                </template>
                                <span v-else>{{ scope.row[column.field] }}</span>
                              </template>
                            </el-table-column>
                          </template>
                        </el-table>
                        <pagination
                          :total="loadParams.total"
                          :page.sync="loadParams.pageNum"
                          :limit.sync="loadParams.pageSize"
                          @pagination="loadData(props.row, [])"
                        />
                      </template>
                    </el-table-column>
                    <el-table-column
                      align="center"
                      label="序号"
                      type="index"
                      width="50"
                    />
                    <template v-for="(column,index) in columns">
                      <el-table-column v-if="column.visible"
                                       :label="column.label"
                                       :prop="column.field"
                                       type=""
                                       align="center">
                        <template slot-scope="scope">
                          <dict-tag v-if="column.dict" :options="dict.type[column.dict]" :value="scope.row[column.field]"/>
                          <template v-else-if="column.slots">
                            <RenderDom :index="index" :render="column.render" :row="scope.row"/>
                          </template>
                          <span v-else>{{ scope.row[column.field] }}</span>
                        </template>
                      </el-table-column>
                    </template>
                    <el-table-column
                      align="center"
                      v-if="detailParams.status == 0 && detailEdit && type == 'edit'"
                      class-name="small-padding fixed-width"
                      fixed="right"
                      type=""
                      label="操作"
                      width="250"
                    >
                      <template slot-scope="scope">
                        <el-button
                          icon="el-icon-edit"
                          size="mini"
                          type="text"
                          v-has-menu-permi="['operate:meteringDetail:edit']"
                          @click="handleEditDetail(scope.row)"
                        >编辑
                        </el-button>
                        <el-button
                          icon="el-icon-delete"
                          size="mini"
                          type="text"
                          v-has-menu-permi="['operate:meteringDetail:remove']"
                          @click="handleRemoveDetail(scope.row)"
                        >删除
                        </el-button>
                      </template>
                    </el-table-column>
                  </el-table>
                  <pagination
                    :total="total"
                    :page.sync="queryParams.pageNum"
                    :limit.sync="queryParams.pageSize"
                    @pagination="rightTreeQuery"
                  />
                </template>
                <el-empty v-else style="height: 100%;background-color: white"/>
              </div>
            </el-col>
          </el-row>
        </div>
      </el-col>
    </el-row>
    <el-dialog :visible.sync="openAddModel" v-if="openAddModel" append-to-body title="新增计量单" width="50%">
      <el-form :model="formData" label-position="right" label-width="80px" ref="elForm" size="mini"
               :rules="rules">
        <el-form-item label="期数" prop="number">
          <el-select v-model="formData.number" style="width: 100%">
            <el-option v-for="(item,index) in numberList" :key="index" :label="item.label" :value="item.value">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="计量编码" prop="code">
          <el-input v-model="formData.code"/>
        </el-form-item>
        <el-form-item label="计量名称" prop="name">
          <el-input v-model="formData.name"/>
        </el-form-item>
        <div style="text-align: right">
          <el-button type="primary" @click="submitForm">保存</el-button>
        </div>
      </el-form>
    </el-dialog>
    <el-dialog :visible.sync="reviewFlag" append-to-body title="审核意见" width="50%">
      <reviewComments></reviewComments>
    </el-dialog>
    <el-dialog :visible.sync="infoFlag" v-if="infoFlag" append-to-body title="查看明细" width="90%">
      <detail :detail-row="detailRow" :params="detailParams" :add-type="addType" :maiSecId="rightParams.maiSecId" @close="closeDetail"></detail>
    </el-dialog>
    <el-dialog v-if="openOperateInfo" :visible.sync="openOperateInfo" destroy-on-close title="操作记录" width="90%">
      <operateInfo :business-key="detailParams.id" :get-node-info="nodeInfo"></operateInfo>
    </el-dialog>
    <el-dialog title="附件列表" destroy-on-close :visible.sync="openFile" width="90%">
      <file-upload v-model="filePath" :forView="true"></file-upload>
    </el-dialog>
  </div>
</template>

<script>
import reviewComments from "./reviewComments.vue";
import Detail from "./detail.vue";
import LeftTree from "./leftTree.vue";
import RightTree from "./rightTree.vue";
import {
  addMetering, deleteMetering, deleteMeteringDetail, editMetering,
  getNumber,
  listDetail,
  listMetering,
  listMeteringDetailFund, nodeInfo, process, withdraw
} from "@/api/calculate/operationManageFee/enterFee";
import OperateInfo from "@/views/dailyMaintenance/component/operateInfo.vue";

export default {
  name: 'EnterFee',
  components: {
    OperateInfo,
    Detail,
    reviewComments,
    LeftTree,
    RightTree,
    RenderDom: {
      functional: true,
      props: {
        row: Object,
        index: Number,
        render: Function
      },
      render(createElement, ctx) {
        const {row, index} = ctx.props
        return ctx.props.render(row, index)
      }
    }
  },
  props: [],
  dicts: ['company_type', 'operate_metering_type'],
  data() {
    return {
      type: 'edit',
      total: 0,
      loading: false,
      topColumn: [],
      columns: [],
      expandColumns: [],
      tableDataTop: [],
      tableData: [],
      rightParams: {},
      detailParams: {},
      reviewFlag: false,
      openAddModel: false,
      infoFlag: false,
      detailEdit: false,
      openOperateInfo: false,
      openFile: false,
      filePath: '',
      detailRow: {},
      addType: '隧道电费',
      numberList: [],
      formData: {},
      rules: {
        number: [
          { required: true, message: '请选择期数', trigger: 'change' }
        ],
        code: [
          { required: true, message: '请输入计量编码', trigger: 'blur' }
        ],
        name: [
          { required: true, message: '请输入计量名称', trigger: 'blur' }
        ]
      },
      // 左侧组织树
      relaNav: true,
      relaNavRight: true,
      currentExpandedRow: null,
      columns1: [
        {key: 0, width: 100, field: 'companyType', label: `养护公司类型`, visible: true, dict: 'company_type'},
        {key: 1, width: 100, field: 'typeId', label: `费用类型`, visible: true, dict: 'operate_metering_type'},
        {key: 2, width: 100, field: 'month', label: `月份`, visible: true},
        {key: 3, width: 100, field: 'mileStr', label: `里程桩号`, visible: true},
        {key: 4, width: 100, field: 'fund', label: `总费`, visible: true},
        {key: 5, width: 100, field: 'remark', label: `备注`, visible: true},
        {key: 6, width: 100, field: 'fileId', label: `附件`, visible: true,slots: true,
          render: (row, index) => {
            return (
              <el-button
                size="mini"
                disabled={!row.fileId}
                type="text" onClick={e => this.handleOpenFile(e, row)}>查看</el-button>
            )
          }},
      ],
      columns2: [
        {key: 0, width: 100, field: 'month', label: `月份`, visible: true},
        {key: 1, width: 100, field: 'mileStr', label: `里程桩号`, visible: true},
        {key: 2, width: 100, field: 'fund', label: `总费`, visible: true},
        {key: 3, width: 100, field: 'remark', label: `备注`, visible: true},
        {key: 4, width: 100, field: 'fileId', label: `附件`, visible: true,slots: true,
          render: (row, index) => {
            return (
              <el-button
                size="mini"
                disabled={!row.fileId}
                type="text" onClick={e => this.handleOpenFile(e, row)}>查看</el-button>
            )
          }},
      ],
      expandColumns1: [
        {key: 0, width: 100, field: 'houseNum', label: `户号`, visible: true},
        {key: 1, width: 100, field: 'houseName', label: `用户名`, visible: true},
        {key: 2, width: 100, field: 'powerRange', label: `用电范围`, visible: true},
        {key: 3, width: 100, field: 'num', label: `本期电量`, visible: true},
        {key: 4, width: 100, field: 'bqFund', label: `本期金额`, visible: true},
        {key: 5, width: 100, field: 'price', label: `本期单价`, visible: true},
        {key: 6, width: 100, field: 'avgPrice', label: `电网代购平均电价`, visible: true},
        {key: 7, width: 100, field: 'isMarketization', label: `是否市场化用电`, visible: true},
        {key: 8, width: 100, field: 'waterFund', label: `本期水利基金`, visible: true},
        {key: 9, width: 100, field: 'fund', label: `本期总额`, visible: true},
      ],
      expandColumns2: [
        {key: 0, width: 100, field: 'siteName', label: `站点`, visible: true},
        {key: 1, width: 100, field: 'useScope', label: `使用范围`, visible: true},
        {key: 2, width: 100, field: 'fund', label: `本期金额`, visible: true},
      ],
      expandColumns3: [
        {key: 0, width: 100, field: 'siteName', label: `站点`, visible: true},
        {key: 1, width: 100, field: 'carNum', label: `车牌号`, visible: true},
        {key: 2, width: 100, field: 'carType', label: `车辆类型`, visible: true},
        {key: 3, width: 100, field: 'fund', label: `本期金额`, visible: true},
      ],
      queryParams: {
        pageNum: 1,
        pageSize: 10,
      },
      loadParams: {
        total: 0,
        pageNum: 1,
        pageSize: 10,
      },
      clickRow: {}
    }
  },
  computed: {},
  watch: {},
  created() {
    const type = this.$route.query.type
    if (type) this.type = type
  },
  mounted() {
  },
  methods: {
    nodeInfo,
    leftTreeQuery(params) {
      this.rightParams = params
      this.$refs.rightTree.getDeptTree(params)
      this.columns = []
      this.tableData = []
      this.getNumbers(params.year)
      this.handleQuery(params)
    },
    rightTreeQuery(params) {
      if (params.children) {
        this.queryParams = params
        this.queryParams.pageNum = 1
        this.queryParams.pageSize = 10
      } else {
        params = this.queryParams
      }
      if (params.id) params.meterId = params.id
      this.detailParams = params
      console.log(this.detailParams)
      this.columns = []
      if (params.children?.length > 0) {
        this.columns = this.columns1
        this.detailEdit = false
      } else {
        this.columns = this.columns2
        this.detailEdit = true
      }
      this.addType = params.name
      this.tableData = []
      listDetail(params).then(res => {
        this.tableData = res.rows
        this.total = res.total
        this.$refs.btnTable.doLayout();
        this.clickRow = {}
      })
    },
    handleQuery(params) {
      this.tableDataTop = []
      listMetering(params).then(res => {
        this.topColumn = res.data
        this.tableDataTop.push(...[
          {
            type: '总计',
            sum: 0,
          },
          {
            type: '集团公司',
            sum: 0,
          },
          {
            type: '项目公司',
            sum: 0,
          }
        ])
        for (let i = 0; i < res.data.length; i++) {
          const item = res.data[i];

          const data1 = this.tableDataTop[1];
          data1['column' + i] = item.companyFund;
          data1.sum += data1['column' + i];

          const data2 = this.tableDataTop[2];
          data2['column' + i] = item.projectFund;
          data2.sum += data2['column' + i]

          const data0 = this.tableDataTop[0];
          data0['column' + i] = data1['column' + i]  + data2['column' + i]
          data0.sum += data0['column' + i]
        }
      })
    },
    handleEdit() {
      this.formData = this.detailParams
      this.openAddModel = true
    },
    handleRemove() {
      this.$confirm('确定删除吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        deleteMetering(this.detailParams.id).then(res => {
          this.$message.success('删除成功')
          this.handleQuery(this.rightParams)
          this.$refs.rightTree.getDeptTree(this.rightParams)
        })
      })
    },
    handleSubmit() {
      this.$prompt('', '提交计量单', {
        confirmButtonText: '提交',
        cancelButtonText: '取消',
        inputPlaceholder: '请输入',
        inputType: 'textarea'
      }).then(({ value }) => {
        const params = {
          businessKey: this.detailParams.id,
          approved: true,
          comment: value,
          taskId: this.detailParams.taskId
        }
        process(params).then(res => {
          this.$message.success('提交成功')
          this.leftTreeQuery(this.rightParams)
        })
      })

    },
    handleWithdraw() {
      this.$confirm('确定撤回吗？', '警告', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        const params = {
          businessKey: this.detailParams.id,
        }
        withdraw(params).then(res => {
          this.$message.success('撤回成功')
          this.leftTreeQuery(this.rightParams)
        })
      })
    },
    handleViewOperate() {
      this.openOperateInfo = true
    },
    handleOpenFile(e, row) {
      this.filePath = ''
      this.openFile = true
      this.filePath = row.fileId
    },
    exportList() {
      this.download(
        'manager/operate/metering/detail/export',
        {...this.detailParams},
        `运营管理费计量单明细_${new Date().getTime()}.xlsx`,
        {
          headers: {'Content-Type': 'application/json;'},
          parameterType: 'body'
        }
      )
    },
    exportDetailList() {
      if (!this.clickRow.id) {
        this.$message.error('请选择一条数据')
        return
      }
      const params = {
        detailId: this.clickRow.id,
        typeId: this.detailParams.typeId || '1'
      }
      this.download(
        'manager/operate/metering/detail/fund/export',
        {...params},
        `运营管理费计量单明细金额_${new Date().getTime()}.xlsx`,
        {
          headers: {'Content-Type': 'application/json;'},
          parameterType: 'body'
        }
      )
    },

    loadData(row, expandedRows) {
      if (expandedRows.length >= 2) {
        // 关闭当前展开的行，保留最后一个展开的行
        this.currentExpandedRow &&
        this.$refs.btnTable.toggleRowExpansion(
          this.currentExpandedRow,
          false
        );
        // 更新当前展开的行
        this.currentExpandedRow = row;
        this.loadParams.total = 0
        this.loadParams.pageNum = 1
      } else {
        // 只有一个展开的行时，更新当前展开的行
        this.currentExpandedRow = row;
      }
      if (row.typeId == '1' || row.typeId == '3') {
        this.expandColumns = this.expandColumns1
      }
      if (row.typeId == '2' || row.typeId == '4') {
        this.expandColumns = this.expandColumns2
      }
      if (row.typeId == '5') {
        this.expandColumns = this.expandColumns3
      }
      listMeteringDetailFund({
        ...this.loadParams,
        detailId: row.id,
        typeId: row.typeId
      }).then(res => {
        this.$set(row, "children", res.rows);
        this.loadParams.total = res.total
      })
    },
    submitForm() {
      this.$refs.elForm.validate(valid => {
        if (!valid) return
        this.formData.numberName = this.numberList.find(item => item.value == this.formData.number)?.label
        this.formData.year = this.rightParams.year
        this.formData.maiSecId = this.rightParams.maiSecId
        this.formData.domainId = this.rightParams.domainId
        if(this.formData.id) {
          editMetering(this.formData).then(res => {
            this.openAddModel = false
            this.$message.success('保存成功')
            this.handleQuery(this.rightParams)
            this.$refs.rightTree.getDeptTree(this.rightParams)
            this.formData = {}
          })
        } else {
          addMetering(this.formData).then(res => {
            this.openAddModel = false
            this.$message.success('保存成功')
            this.handleQuery(this.rightParams)
            this.$refs.rightTree.getDeptTree(this.rightParams)
            this.formData = {}
          })
        }
      })
    },
    handleAddDetail() {
      this.detailRow = {}
      this.infoFlag = true
    },
    handleEditDetail(row) {
      this.detailRow = row
      this.infoFlag = true
    },
    handleRemoveDetail(row) {
      this.$confirm('确定删除吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        deleteMeteringDetail(row.id).then(res => {
          this.$message.success('删除成功')
          this.handleQuery(this.rightParams)
          this.rightTreeQuery(this.detailParams)
        })
      })
    },
    closeDetail() {
      this.infoFlag = false
      this.handleQuery(this.rightParams)
      this.rightTreeQuery(this.detailParams)
    },
    getNumbers(year) {
      this.numberList = []
      getNumber(year).then(res => {
        res.data.forEach(item => {
          this.numberList.push({
            label: item.name,
            value: item.id
          })
        })
      })
    },
    handleRowClick(e) {
      this.clickRow = e
    }
  }
}
</script>

<style lang="scss" scoped>
.leftDiv {
  border-right: 1px solid #d8dce5;
  overflow-y: auto;
  height: calc(100% + 30px);
  position: relative;
  top: -20px;
  padding-top: 10px;
  background-color: white;
}

.right-container {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.RightDiv {
  border-right: 1px solid #d8dce5;
  overflow-y: auto;
  height: 100%;
  position: relative;
  padding-top: 10px;
  background-color: white;
  margin-left: 10px;
}

.leftIcon {
  border: 1px solid #dcdfe6;
  border-radius: 8px;
  width: 16px;
  height: 50px;
  line-height: 50px;
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
  position: absolute;
  right: 0;
  top: 300px;
  z-index: 2;
}

.leftIcon:hover {
  background-color: #dcdfe6;
}

.rightIcon {
  border: 1px solid #dcdfe6;
  border-radius: 8px;
  width: 16px;
  height: 50px;
  line-height: 50px;
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
  position: absolute;
  left: -10px;
  top: 280px;
  z-index: 10;
  background: white;
}

.rightIcon:hover {
  background-color: #dcdfe6;
}
</style>
<style lang="scss" scoped>
@import "@/assets/styles/business.scss";
</style>
