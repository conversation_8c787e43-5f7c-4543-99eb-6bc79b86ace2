<template>
  <div class="stake-mark" v-loading="loading" element-loading-background="rgba(0, 0, 0, 0.3)">
    <el-form :model="form" ref="formRef" :rules="rules" :label-width="isBig ? '160px' : '80px'" :inline="false">
      <el-form-item label="路线编码" prop="routeCode">
        <el-select v-model="form.routeCode" placeholder="请选择" clearable filterable style="width: 100%;"
          @change="onRouteCodeChange" popper-class="select-popper" :popper-append-to-body="false">
          <el-option v-for="item in routeOptions" :key="item.routeId" :label="item.routeName" :value="item.routeCode" />
        </el-select>
      </el-form-item>
      <el-form-item label="定位桩号" prop="stakeNumber" :rules="stateRules">
        <el-row :gutter="1">
          <el-col :span="1" :offset="0" class="l-k">K </el-col>
          <el-col :span="10" :offset="1">
            <el-input v-model="form.k" placeholder="请输入" style="width: 100%;" />
          </el-col>
          <el-col :span="1" :offset="0"><span class="ml5 mr5">+</span></el-col>
          <el-col :span="10" :offset="1">
            <el-input v-model="form.b" placeholder="请输入" style="width: 100%;" />
          </el-col>
        </el-row>
      </el-form-item>
      <el-form-item label="坐标X">
        <el-input v-model="form.longitude" placeholder="请输入经度..." clearable />
      </el-form-item>
      <el-form-item label="坐标Y">
        <el-input v-model="form.latitude" placeholder="请输入纬度..." clearable />
      </el-form-item>
    </el-form>
    <div class="btn-list">
      <el-button type="primary" @click="onSearch" :size="isBig ? 'medium' : ''">查坐标</el-button>
      <el-button type="primary" @click="onGetPile" :size="isBig ? 'medium' : ''">查桩号</el-button>
      <el-button type="primary" @click="setLocation" :size="isBig ? 'medium' : ''" :class="localBol ? 'location' : ''">
        定位
      </el-button>
      <el-button type="primary" @click="onClear" :size="isBig ? 'medium' : ''">清除</el-button>
      <!-- <el-button type="primary" @click="onQuit" :size="isBig ? 'medium' : 'mini'" class="hollow-out">退出</el-button> -->
    </div>
  </div>
</template>

<script>
import { listAllRoute } from '@/api/system/route'
import { Point } from 'ol/geom';
import { Feature } from 'ol';
import { fromLonLat, transform as projTransform } from 'ol/proj';
import { Style, Fill, Circle as CircleStyle, Stroke, Icon } from 'ol/style';
import VectorSource from 'ol/source/Vector';
import VectorLayer from 'ol/layer/Vector';
import { mapMutations } from 'vuex';

import { addClickFeature, removeLayer, getLonLat, isValidWKT, gcj02towgs84 } from '@/views/map/components/common/mapFun'

import { publicRequest } from '@/api/oneMap/tableInfo'
import { WKT } from 'ol/format';

import PileIcon from '@/assets/map/pile.png'
import { isBigScreen } from '../common/util';
import { getRoute } from '@/api/system/route';
import { getRouteInfo } from '@/api/baseData/common/routeLine';


export default {
  data() {
    return {
      form: {
        routeCode: '',
        stakeNumber: null,
        longitude: '',
        latitude: '',
        k: '',
        b: '',
      },
      rules: {
        routeCode: [
          { required: true, message: '请选择路线编码', trigger: 'change' }
        ],
      },
      routeOptions: [],
      isBig: isBigScreen(),
      localBol: false,
      loading: false,
    }
  },
  computed: {
    stateRules() {
      return [{ required: false, type: 'number', message: '请输入数字', trigger: ['change', 'blur'] }]
    },
  },
  created() {
    this.getRouteList();
    window.$Bus.$on("onLocation", (val) => {
      if (val) {
        if (val.lonLat) {
          this.form.longitude = val.lonLat[0];
          this.form.latitude = val.lonLat[1];
        }
        let properties = val.properties;
        if (properties && (properties.id || properties.data.id)) {
          let id = properties.id || properties.data.id;
          this.getRouteInfo(id);
        }
        this.$forceUpdate();
      }
    })
  },
  methods: {
    ...mapMutations({
      setLoaclBol: "map/setLoaclBol",
    }),
    // 获取路线编码数据
    getRouteList() {
      listAllRoute().then(res => {
        if (res.code === 200) {
          this.routeOptions = res.data || [];
        }
      })
    },
    // 获取路线信息
    getRouteInfo(routeId) {
      this.loading = true;
      getRouteInfo(routeId).then(async res => {
        if (res.code === 200) {
          let params = {};
          if (this.form.longitude && this.form.latitude) {
            let wkt
            // 将经纬度转为wkt
            const point = new Point([this.form.longitude - 0, this.form.latitude - 0]);
            wkt = new WKT().writeGeometry(point);
            params = {
              wkt,
              // routeCode: this.form.routeCode || null,
            }
          }
          let info = await this.onGetInfo(params);
          this.form.routeCode = res.data?.lxbh;
          this.form.stakeNumber = info && info.stakeNumber ? info.stakeNumber/1000 + '' : res.data?.qdzh || res.data?.zdzh;
          let state = this.form.stakeNumber.split('.');
          this.form.k = state[0] || 0;
          this.form.b = state.length > 1 ? state[1] : '000';
          // 如果b长度超过三位截取前3位
          this.form.b = this.form.b.length >= 3 ? this.form.b.substring(0, 3) : this.form.b.length === 2 ? this.form.b + '0' : this.form.b + '00';
          // 转为数字
          this.form.k = this.form.k - 0;
          this.form.b = this.form.b - 0;
          
          if(this.$refs.formRef) {
            this.$refs.formRef?.clearValidate();
          }
          this.$forceUpdate();
        }
      }).finally(() => {
        this.loading = false;
      });
    },
    // 查询
    onSearch() {
      // 数据组装
      if (this.form.k || this.form.b) {
        this.form.stakeNumber = (this.form.k - 0) * 1000 + (this.form.b - 0)
      }
      this.$refs.formRef.validate((vali) => {
        if (!vali) return;
        let params = {}
        // if (this.form.longitude && this.form.latitude && (!this.form.k || !this.form.b)) {
        //   let wkt
        //   // 将经纬度转为wkt
        //   const point = new Point([this.form.longitude - 0, this.form.latitude - 0]);
        //   wkt = new WKT().writeGeometry(point);
        //   params = {
        //     wkt,
        //     routeCode: this.form.routeCode,
        //   }
        // } else {
        //   params = {
        //     stakeNumber: this.form.stakeNumber,
        //     routeCode: this.form.routeCode,
        //   }
        // }
        params = {
          stakeNumber: this.form.stakeNumber,
          routeCode: this.form.routeCode,
        }
        this.$modal.loading("请求中，请稍后...");
        publicRequest('/system/stationLine/getRouteData', 'post', params).then(res => {
          if (res.code == 200 && res.data) {
            let data = res.data || {};
            if (data.wkt) {
              let isBool = isValidWKT(data.wkt);
              if (isBool) {
                // 解析WKT字符串
                const { lon, lat } = getLonLat(data.wkt)
                this.form.longitude = lon;
                this.form.latitude = lat;
              } else {
                this.$modal.msgError(`数据:${data.wkt} 格式不正确`);
                this.form.longitude = ''
                this.form.latitude = ''
              }
              this.onLocation();
            } else {
              this.form.longitude = ''
              this.form.latitude = ''
            }
            if (data.stakeNumber) {
              let number = data.stakeNumber - 0
              let stateNum = number / 1000
              let str = stateNum + ''
              let arr = str.split('.')
              //arr[1]不足三位数，补0
              if (arr && arr[1] && arr[1].length < 3) {
                arr[1] = arr[1].padEnd(3, '0')
              }
              this.form.k = arr[0] || 0
              this.form.b = arr[1] || 0
              this.form.stakeNumber = data.stakeNumber
            }
          }
        }).finally(() => {
          this.$modal.closeLoading();
        })
      })
    },
    // 定位
    onLocation() {
      if (!this.form.longitude || !this.form.latitude) {
        this.$modal.msgError("请输入经纬度");
        return;
      }
      let lonLat = [this.form.longitude - 0, this.form.latitude - 0]
      let [longitude, latitude] = lonLat
      // 校验经纬度
      let pointLen = 0; // 小数点后最少长度
      // 经度范围：0-180，小数部分至少3位
      const lngRegExp = new RegExp(`^([0-9]|[1-9][0-9]|1[0-7][0-9]|180)(\\.\\d{${pointLen},})?$`);
      // 纬度范围：0-90，小数部分至少3位
      const latRegExp = new RegExp(`^([0-9]|[1-8][0-9]|90)(\\.\\d{${pointLen},})?$`);
      // 验证纬度  
      if (!lngRegExp.test(longitude) || !latRegExp.test(latitude)) {
        this.$modal.msgError("请输入正确的经纬度");
        return;
      }
      let wgs84Coordinates = gcj02towgs84(longitude, latitude)
      removeLayer(window.mapLayer, 'clickLayer');
      // 定位到 该经纬度
      const feature = new Feature({
        geometry: new Point(fromLonLat(wgs84Coordinates))
      })
      const style = new Style({
        // image: new CircleStyle({
        //   radius: 7,
        //   fill: new Fill({ color: '#D91810' }),
        //   stroke: new Stroke({ color: '#ffffff', width: 2 }),
        // }),
        image: new Icon({
          anchor: [0.5, 1], // 图标的锚点，相对于图标图片的比例
          src: PileIcon || "",
          scale: 0.85,
        }),
      });

      // 将特征的样式设置为刚才创建的样式
      feature.setStyle(style);
      // 创建矢量数据源并将特征添加进去
      const vectorSource = new VectorSource({
        features: [feature],
      });
      // 创建矢量图层并将数据源与之关联
      const vectorLayer = new VectorLayer({
        source: vectorSource,
      });
      vectorLayer.set('name', 'clickLayer')
      vectorLayer.setZIndex(9002)
      window.mapLayer.addLayer(vectorLayer);
      window.mapLayer.getView().animate({
        center: feature?.getGeometry()?.getCoordinates(),
        zoom: 10,
      });
    },
    setLocation() {
      this.localBol = !this.localBol;
      this.setLoaclBol({ bol: this.localBol });
    },
    // 查询桩号
    onGetPile() {
      if(!this.form.longitude || !this.form.latitude) {
        this.$modal.msgError("请输入经纬度");
        return;
      }
      let params = {};
      if (this.form.longitude && this.form.latitude) {
        let wkt
        // 将经纬度转为wkt
        const point = new Point([this.form.longitude - 0, this.form.latitude - 0]);
        wkt = new WKT().writeGeometry(point);
        params = {
          wkt,
          // routeCode: this.form.routeCode || null,
        }
      }
      this.$modal.loading("请求中，请稍后...");
      publicRequest('/system/stationLine/getRouteData', 'post', params).then(res => {
        if (res.code == 200 && res.data) {
          // 路线编码
          this.form.routeCode = res.data.routeCode ? res.data.routeCode : this.form.routeCode || '';
          // 桩号
          let stakeNumber = res.data.stakeNumber;
          this.form.stakeNumber = stakeNumber;
          if (stakeNumber && res.data.routeCode) {
            let number = stakeNumber - 0;
            let stateNum = number / 1000;
            let str = stateNum + '';
            let arr = str.split('.');
            //arr[1]不足三位数，补0
            if (arr && arr[1] && arr[1].length < 3) {
              arr[1] = arr[1].padEnd(3, '0');
            }
            this.form.k = arr[0] || 0;
            this.form.b = arr[1] || 0;
          } else {
            this.form.k = 0;
            this.form.b = 0;
          }
          this.onLocation();
        }
      }).finally(() => {
        this.$modal.closeLoading();
      });
    },
    // 根据参数查询桩号或经纬度
    onGetInfo(params) {
      return new Promise((resolve, reject) => {
        publicRequest('/system/stationLine/getRouteData', 'post', params).then(res => {
          if(res.code == 200 && res.data) {
            resolve(res.data);
          } else {
            resolve(null);
          }
        }).catch(err=>{
          reject(err);
        });
      });
    },
    // 清除
    onClear() {
      this.$modal.confirm('确认清除所填信息？').then(() => {
        Object.keys(this.form).forEach(key => {
          this.form[key] = ''
        });
        removeLayer(window.mapLayer, 'clickLayer');
        // 清除 element ui 验证信息
        this.$refs.formRef.clearValidate();
      }).then(() => {

      }).catch(() => { });
    },
    // 退出
    onQuit() {
      this.$emit('quit')
      removeLayer(window.mapLayer, 'clickLayer');
    },
    // 监听路段编码改变
    onRouteCodeChange(e) {
      let arr = this.routeOptions.filter(v => v.routeCode == e);
      this.form.longitude = '';
      this.form.latitude = '';
      if (arr && arr.length) {
        let pileStart = arr[0].pileStart || arr[0].pileEnd || 0;
        this.form.stakeNumber = pileStart;
        if (pileStart) {
          let number = pileStart - 0;
          let str = number / 1000 + '';
          let ar = str.split('.');
          if (ar && ar.length >= 2) {
            ar[1] = ar[1] + '';
            ar[1] = ar[1].length == 1 ? ar[1] + '00' : ar[1].length == 2 ? ar[1] + 0 : ar[1];
            ar[1] = ar[1] - 0;
            if (ar[1] < 100) {
              ar[1] = '0' + ar[1];
            } else {
              ar[1] = ar[1] + '';
            }
          } else {
            ar[1] = '000';
          }
          this.form.k = ar[0] || 0;
          this.form.b = ar[1] || 0;
        }
      }
    },
  }
}
</script>

<style lang="scss" scoped>
@import "@/assets/styles/utils.scss";

.stake-mark {
  z-index: 9999;
  margin: vwpx(20px) 0;
  color: #ffffff;
  overflow: hidden;

  ::v-deep .el-form {
    .el-form-item__label {
      color: #ffffff;
      font-size: vwpx(30px);
    }

    .el-input--mini .el-input__inner {
      height: vwpx(68px);
      min-height: vwpx(66px);
    }
  }

  ::v-deep .el-input {
    .el-input__inner {
      background-color: rgba(1, 102, 254, 0.2);
      border: 1px solid #0166FE;
      color: #ffffff;
      font-size: vwpx(28px);
    }

    .el-input__inner::placeholder {
      color: #BBBBBB;
    }

    .el-input-group__append {
      background-color: rgba(1, 102, 254, 0.2);
      border: 1px solid #0166FE;
      color: #ffffff;
      border-left: none;
      padding: 0 vwpx(20px);
      cursor: pointer;
    }
  }

  .l-k {
    line-height: vwpx(72px);
    margin-left: vwpx(-10px);
    font-size: vwpx(30px);
  }

  .btn-list {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    margin-top: vwpx(60px);

    button {
      height: vwpx(72px);
      min-height: vwpx(66px);
      width: vwpx(150px);
      height: 3vh;
      font-size: 1.4vh;
    }

    .hollow-out {
      background-color: rgba(1, 102, 254, 0.2);
      color: #ffffff;
    }

    .location {
      border: 2px dotted rgba(255, 0, 0, 0.6);
    }
  }

  ::v-deep .el-select-dropdown__item {
    font-size: vwpx(30px);
    margin: vwpx(15px) 0;
  }

  ::v-deep .el-button--mini {
    padding: vwpx(20px) 0;
  }
}
</style>