<template>
  <div class="app-container">
    <el-row :gutter="20">

      <!--筛选区开始-->
      <el-col :span="24" :xs="24">
        <el-row>
          <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" label-width="68px">
            <el-col :span="24">
              <el-form-item label="" prop="name">
                <el-input v-model="queryParams.name" placeholder="请输入灾点名称" clearable prefix-icon="el-icon-user"
                  style="width: 240px" @keyup.enter.native="handleQuery" />
              </el-form-item>
              <el-form-item label="" prop="managementMaintenanceName">
                <el-input v-model="queryParams.managementMaintenanceName" placeholder="请输入管理处名称" clearable
                  prefix-icon="el-icon-user" style="width: 240px" @keyup.enter.native="handleQuery" />
              </el-form-item>
              <el-form-item label="" prop="affects">
                <el-select v-model="queryParams.affects" placeholder="是否影响道路安全" style="width: 240px" clearable>
                  <el-option v-for="dict in dict.type.is_affects_road_safety" :key="dict.value" :label="dict.label"
                    :value="parseInt(dict.value)"></el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="" prop="isSiteAudit">
                <el-select v-model="queryParams.isSiteAudit" placeholder="是否为现场复核" clearable style="width: 240px">
                  <el-option label="是" value="1"></el-option>
                  <el-option label="否" value="0"></el-option>
                </el-select>
              </el-form-item>
              <!--              <el-form-item label="" prop="checkerId">-->
              <!--                <el-input-->
              <!--                    v-model="queryParams.checkerId"-->
              <!--                    placeholder="请输入巡查人ID"-->
              <!--                    clearable-->
              <!--                    prefix-icon="el-icon-user"-->
              <!--                    style="width: 240px"-->
              <!--                    @keyup.enter.native="handleQuery"-->
              <!--                />-->
              <!--              </el-form-item>-->
              <el-form-item>
                <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
                <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
                <!-- <el-button v-show="!showSearch" @click="showSearch=true" icon="el-icon-arrow-down" circle></el-button>
                <el-button v-show="showSearch" @click="showSearch=false" icon="el-icon-arrow-up" circle></el-button> -->
              </el-form-item>
              <!--默认折叠 ,此处仅作为示例-->
              <!-- <el-form-item v-show="showSearch" label="创建时间" prop="createTime">
                <el-date-picker
                    v-model="dateRange"
                    style="width: 240px"
                    value-format="yyyy-MM-dd"
                    type="daterange"
                    range-separator="-"
                    start-placeholder="开始日期"
                    end-placeholder="结束日期"
                ></el-date-picker>
              </el-form-item> -->
            </el-col>
          </el-form>
        </el-row>
        <!--筛选区结束-->


        <!--操作按钮区开始-->
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button type="primary" icon="el-icon-plus" size="mini" @click="handleAdd"
              v-hasPermi="['middleData:geologicalPoints:add']">新增</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="success" icon="el-icon-edit" size="mini" :disabled="single" @click="handleUpdate"
              v-hasPermi="['middleData:geologicalPoints:edit']">修改</el-button>
          </el-col>
          <!--          <el-col :span="1.5">-->
          <!--            <el-button  type="info" plain icon="el-icon-upload2" size="mini" @click="handleImport" v-hasPermi="['middleData:geologicalPoints:export']">导入</el-button>-->
          <!--          </el-col>-->
          <el-col :span="1.5">
            <el-button type="danger" icon="el-icon-delete" size="mini" :disabled="multiple" @click="handleDelete"
              v-hasPermi="['middleData:geologicalPoints:remove']">删除</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="warning" icon="el-icon-download" size="mini" @click="handleExport"
              v-hasPermi="['middleData:geologicalPoints:export']">导出清单</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="primary" plain icon="el-icon-check" size="mini" :disabled="multiple"
              @click="handleBatchAudit" v-hasPermi="['middleData:geologicalPoints:edit']">批量审核</el-button>
          </el-col>
          <right-toolbar :showSearch.sync="showSearch" @queryTable="getList" :columns="columns"></right-toolbar>
        </el-row>
        <!--操作按钮区结束-->

        <!--数据表格开始-->
        <div class="tableDiv">
          <el-table v-adjust-table ref="table" size="mini"
            :height="showSearch ? 'calc(100vh - 320px)' : 'calc(100vh - 260px)'" style="width: 100%" v-loading="loading"
            border :data="geologicalPointsList" @selection-change="handleSelectionChange" :row-style="rowStyle"
            @row-click="handleRowClick">
            <el-table-column type="selection" width="50" align="center" />
            <el-table-column fixed label="序号" type="index" width="50">
              <template v-slot="scope">
                {{ (scope.$index + (queryParams.pageNum - 1) * queryParams.pageSize) + 1 }}
              </template>
            </el-table-column>
            <el-table-column label="省份" align="center" prop="province" v-if="cmpColumns('省份')" />
            <el-table-column label="灾点名称" align="center" prop="name" v-if="cmpColumns('灾点名称')" />
            <el-table-column label="灾点类别" align="center" prop="type" v-if="cmpColumns('灾点类别')" />
            <el-table-column label="规模等级" align="center" prop="scale" v-if="cmpColumns('规模等级')" />
            <el-table-column label="路线编码" align="center" prop="routeCode" v-if="cmpColumns('路线编码')" />
            <el-table-column label="路线名称" align="center" prop="routeName" v-if="cmpColumns('路线名称')" />
            <el-table-column label="垂点桩号" align="center" prop="centerStakeNumber" v-if="cmpColumns('垂点桩号')">
              <template slot-scope="scope">
                <span>{{ formatStakeNumber(scope.row.centerStakeNumber) }}</span>
              </template>
            </el-table-column>
            <el-table-column label="灾点经度" align="center" prop="longitude" v-if="cmpColumns('灾点经度')" />
            <el-table-column label="灾点纬度" align="center" prop="latitude" v-if="cmpColumns('灾点纬度')" />
            <el-table-column label="与路段垂直距离(m)" align="center" prop="perpendicularDistance" width="180"
              v-if="cmpColumns('与路段垂直距离(m)')" />
            <el-table-column label="方向角(°)" align="center" prop="directionAngle" v-if="cmpColumns('方向角(°)')" />
            <el-table-column label="疑似风险路段" align="center" prop="potentialRiskSegment" width="180"
              v-if="cmpColumns('疑似风险路段')" />
            <el-table-column label="路段名称" align="center" prop="routeSectionName" v-if="cmpColumns('路段名称')" />
            <el-table-column label="管理处" align="center" prop="managementMaintenanceName" v-if="cmpColumns('管理处')" />
            <el-table-column label="权属单位" align="center" prop="propertyUnitType" v-if="cmpColumns('权属单位')" />
            <!--            <el-table-column label="空间信息" align="center" prop="shape"/>-->
            <!--            <el-table-column label="备注" align="center" prop="remarks" />-->
            <!--            <el-table-column label="图片的ownerId" align="center" prop="photos" />-->
            <el-table-column label="是否影响道路安全" align="center" prop="affects" width="180" v-if="cmpColumns('是否影响道路安全')">
              <template slot-scope="scope">
                <dict-tag :options="dict.type.is_affects_road_safety" :value="scope.row.affects" />
              </template>
            </el-table-column>
            <el-table-column label="是否为现场复核" align="center" prop="isSiteAudit" v-if="cmpColumns('是否为现场复核')">
              <template slot-scope="scope">
                <span v-if="scope.row.isSiteAudit === 0">否</span>
                <span v-if="scope.row.isSiteAudit === 1">是</span>
              </template>
            </el-table-column>
            <el-table-column label="巡查人" align="center" prop="checkerName" v-if="cmpColumns('巡查人')" />
            <el-table-column label="审核人" align="center" prop="auditName" v-if="cmpColumns('审核人')" />
            <el-table-column label="审核状态" align="center" prop="auditStatus" v-if="cmpColumns('审核状态')">
              <template slot-scope="scope">
                <span v-if="scope.row.auditStatus === 0">未审核</span>
                <span v-if="scope.row.auditStatus === 1">审核通过</span>
                <span v-if="scope.row.auditStatus === 2">审核不通过</span>
              </template>
            </el-table-column>
            <el-table-column label="巡查时间" align="center" prop="checkTime" width="180" v-if="cmpColumns('巡查时间')" />
            <el-table-column label="风险等级" align="center" prop="riskGrade" width="180" v-if="cmpColumns('风险等级')">
              <template slot-scope="scope">
                <dict-tag :options="dict.type.geological_points_risk_grade" :value="scope.row.riskGrade" />
              </template>
            </el-table-column>
            <el-table-column label="操作" fixed="right" align="center" width="160" class-name="small-padding fixed-width">
              <template slot-scope="scope" v-if="scope.row.userId !== 1">
                <!--                <el-button-->
                <!--                    size="mini"-->
                <!--                    type="text"-->
                <!--                    icon="el-icon-edit"-->
                <!--                    @click="handleUpdate(scope.row)"-->
                <!--                    v-hasPermi="['middleData:geologicalPoints:edit']"-->
                <!--                >修改</el-button>-->
                <el-button size="mini" type="text" icon="el-icon-check" @click="handleAudit(scope.row)"
                  v-hasPermi="['middleData:geologicalPoints:edit']">审核</el-button>
                <el-button size="mini" type="text" icon="el-icon-delete" @click="handleDelete(scope.row)"
                  v-hasPermi="['middleData:geologicalPoints:remove']">删除</el-button>
              </template>
            </el-table-column>
          </el-table>

          <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum"
            :limit.sync="queryParams.pageSize" @pagination="getList" />
        </div>
        <!--数据表格结束-->
      </el-col>
    </el-row>

    <!-- 添加或修改路域外地质灾害隐患点清单对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="70%" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="160px">
        <div class="infoBox" style="margin-bottom: 20px;">
          <div class="infoTitle">
            基础信息
          </div>
          <el-row>
            <el-col :span="12">
              <el-form-item label="省份" prop="province">
                <el-input v-model="form.province" placeholder="请输入省份" disabled />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="管理处" prop="managementMaintenanceId" class="inline-form-item">
                <selectTree v-model="form.managementMaintenanceId" :deptType="201" clearable
                  @change="deptChange(form.managementMaintenanceId)" filterable placeholder="请选择管理处" style="width: 100%"
                  @node-selected="handleNodeSelected" />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="12">
              <el-form-item label="路段名称" prop="routeSectionId" class="inline-form-item">
                <el-select v-model="form.routeSectionId" filterable placeholder="请选择养护路段"
                  @change="managementChange(form.routeSectionId)" style="width: 100%">
                  <el-option v-for="item in maintenanceSectionList" :key="item.maintenanceSectionId"
                    :label="item.maintenanceSectionName" :value="item.maintenanceSectionId"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="路线编码" prop="routeCode" class="inline-form-item">
                <el-select v-model="form.routeCode" placeholder="路线编码" clearable filterable style="width: 100%"
                  collapse-tags @change="routeCodeChange">
                  <el-option v-for="item in routeList" :key="item.routeId" :label="item.routeName"
                    :value="item.routeCode">
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="12">
              <el-form-item label="路线名称" prop="routeName">
                <el-input v-model="form.routeName" placeholder="请输入路线名称" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="权属单位" prop="propertyUnitType">
                <el-radio-group v-model="form.propertyUnitType">
                  <el-radio label="集团公司">集团公司</el-radio>
                  <el-radio label="项目公司">项目公司</el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>
          </el-row>
        </div>
        <div class="infoBox" style="margin-bottom: 20px;">
          <div class="infoTitle">
            风险评估
          </div>
          <el-row>
            <el-col :span="12">
              <el-form-item label="灾点名称" prop="name">
                <el-input v-model="form.name" placeholder="请输入灾点名称" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="灾点类别" prop="type">
                <el-select v-model="form.type" filterable placeholder="请选择灾点类别" style="width: 100%">
                  <el-option label="滑坡" value="滑坡"></el-option>
                  <el-option label="崩塌" value="崩塌"></el-option>
                  <el-option label="泥石流" value="泥石流"></el-option>
                  <el-option label="沉陷与塌陷" value="沉陷与塌陷"></el-option>
                  <el-option label="水毁" value="水毁"></el-option>
                  <el-option label="其它" value="其它"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="12">
              <el-form-item label="规模等级" prop="scale">
                <el-select v-model="form.scale" filterable placeholder="请选择规模等级" style="width: 100%">
                  <el-option label="一级" value="一级"></el-option>
                  <el-option label="二级" value="二级"></el-option>
                  <el-option label="三级" value="三级"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-tooltip effect="dark" content="垂点桩号(KXXX+XXX格式)" placement="top">
                <el-form-item label="垂点桩号" prop="centerStakeNumber">
                  <div class="input-row">
                    <span class="dwClass">K</span>
                    <el-input type="number" :min="0" v-model="form.beginMile1" />
                    <span class="dwClass">-</span>
                    <el-input type="number" :min="0" v-model="form.beginMile2" />
                  </div>
                </el-form-item>
              </el-tooltip>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="12">
              <el-form-item label="灾点经度" prop="longitude">
                <el-input v-model="form.longitude" placeholder="请输入灾点经度"
                  @input="(v) => (form.longitude = v.replace(/[^\d.]/g, ''))">
                  <el-button slot="append" icon="el-icon-location"
                    @click="showCoordinatePicker('longitude')">坐标拾取</el-button>
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="灾点纬度" prop="latitude">
                <el-input v-model="form.latitude" placeholder="请输入灾点纬度"
                  @input="(v) => (form.latitude = v.replace(/[^\d.]/g, ''))">
                  <el-button slot="append" icon="el-icon-location"
                    @click="showCoordinatePicker('latitude')">坐标拾取</el-button>
                </el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="12">
              <el-form-item label="与路段垂直距离(m)" prop="perpendicularDistance">
                <el-input v-model="form.perpendicularDistance"
                  @input="(v) => (form.perpendicularDistance = v.replace(/[^\d.]/g, ''))" placeholder="请输入与路段垂直距离(m)" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="方向角(°)" prop="directionAngle">
                <el-input v-model="form.directionAngle" @input="(v) => (form.directionAngle = v.replace(/[^\d.]/g, ''))"
                  placeholder="请输入方向角(°)" />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="12">
              <el-form-item label="疑似风险路段" prop="riskId">
                <el-select v-model="form.riskId" placeholder="请选择疑似风险路段" clearable filterable style="width: 100%"
                  collapse-tags @change="riskChange" @focus="listRiskSectionsAll">
                  <el-option v-for="item in riskList" :key="item.id" :label="item.potentialRiskSegment"
                    :value="item.id">
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="风险等级" prop="riskGrade">
                <el-select v-model="form.riskGrade" placeholder="请选择风险等级" style="width: 100%;">
                  <el-option v-for="dict in dict.type.geological_points_risk_grade" :key="dict.value"
                    :label="dict.label" :value="dict.value"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="12">
              <el-form-item label="是否影响道路安全" prop="affects">
                <el-radio-group v-model="form.affects">
                  <el-radio v-for="dict in dict.type.is_affects_road_safety" :label="dict.value">{{ dict.label
                  }}</el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="是否为现场复核" prop="isSiteAudit">
                <el-radio-group v-model="form.isSiteAudit">
                  <el-radio :label="1">是</el-radio>
                  <el-radio :label="0">否</el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="12">
              <el-form-item label="巡查人" prop="checkerName">
                <el-input v-model="form.checkerName" placeholder="请输入巡查人" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="审核人" prop="auditName">
                <el-input v-model="form.auditName" placeholder="请输入审核人" />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="12">
              <el-form-item label="巡查时间" prop="checkTime">
                <el-date-picker key="checkTime" clearable v-model="form.checkTime" type="datetime"
                  value-format="yyyy-MM-dd HH:mm:ss" placeholder="请选择巡查时间" style="width: 100%;">
                </el-date-picker>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="审核时间" prop="auditTime">
                <el-date-picker key="auditTime" clearable v-model="form.auditTime" type="datetime"
                  value-format="yyyy-MM-dd HH:mm:ss" placeholder="请选择审核时间" style="width: 100%;">
                </el-date-picker>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="12">
              <el-form-item label="备注" prop="remarks">
                <el-input v-model="form.remarks" type="textarea" placeholder="请输入内容" />
              </el-form-item>
            </el-col>
          </el-row>
        </div>
        <div class="infoBox" style="margin-bottom: 20px;" v-if="this.form.id">
          <div class="infoTitle">
            照片预览
          </div>
          <el-row :gutter="12">
            <el-col :span="24">
              <el-form-item label-width="0" prop="riskDescription">
                <div class="imgBox" v-if="formImgSrcList.length > 0">
                  <el-timeline>
                    <el-timeline-item v-for="item in formDataLine" :key="item.time" :timestamp="item.time"
                      placement="top" color="#5cbb7a">
                      <el-card class="imgBoxCard">
                        <div class="cardMain" v-for="itemC in item.data">
                          <el-button class="imgDeleteBtn" type="danger" icon="el-icon-delete" circle
                            @click="formDeleteImg(itemC.id)"></el-button>
                          <div class="imgTitle">
                            <el-tooltip class="item" effect="dark" :content="itemC.name" placement="top">
                              <i class="el-icon-info"></i>
                            </el-tooltip>
                            {{ itemC.name }}
                          </div>
                          <el-image fit="cover" class="img" :src="itemC.url" @click="formImgPreview(itemC.imgUrl)"
                            :preview-src-list="formImgUrlList"></el-image>
                          <div class="footer">
                            {{ `由 ${itemC.createBy} 上传于 ${itemC.createTime}` }}
                          </div>
                          <div class="footer">
                            {{ itemC.remark ? `图片描述：${itemC.remark}` : '' }}
                          </div>
                        </div>
                      </el-card>
                    </el-timeline-item>
                  </el-timeline>
                </div>
                <div class="noneBox" v-else>
                  暂无内容
                </div>
              </el-form-item>
            </el-col>
          </el-row>
        </div>
        <div class="infoBox" style="padding: 15px;">
          <div class="infoTitle">
            <!-- <el-tooltip content="上传填报高边坡的全貌照片及局部照片（防护设施、排水设施），不少于 3 张" placement="top">
              <i class="el-icon-question"></i>
            </el-tooltip> -->
            上传照片
          </div>
          <ImageUpload v-model="formUploadList" :ownerId="formImgOwnerId" :needRemark="true" :can-sort="true"
            storage-path="/middleData/geologicalPoints/" platform="fykj" @input="queryImg(formImgOwnerId)"
            ref="refImageUpload" :fileType="['png', 'jpg', 'jpeg', 'PNG', 'JPG', 'JPEG']" />
        </div>
        <!-- <el-col :span="12">
          <el-form-item label="路段名称" prop="routeSectionName">
            <el-input v-model="form.routeSectionName" placeholder="请输入路段名称" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="管理处名称" prop="managementMaintenanceName">
            <el-input v-model="form.managementMaintenanceName" placeholder="请输入管理处名称" />
          </el-form-item>
        </el-col> -->
        <!-- <el-col :span="12">
          <el-form-item label="疑似风险路段" prop="potentialRiskSegment">
            <el-input v-model="form.potentialRiskSegment" placeholder="请输入疑似风险路段" />
          </el-form-item>
        </el-col> -->
        <!-- <el-col :span="12">
          <el-form-item label="空间信息" prop="shape">
            <el-input v-model="form.shape" type="textarea" placeholder="请输入内容" />
          </el-form-item>
        </el-col> -->
        <!-- <el-col :span="12">
          <el-form-item label="管理处id" prop="managementMaintenanceId">
            <el-input v-model="form.managementMaintenanceId" placeholder="请输入管理处id" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="路段id" prop="routeSectionId">
            <el-input v-model="form.routeSectionId" placeholder="请输入路段id" />
          </el-form-item>
        </el-col> -->
        <!-- <el-col :span="12">
          <el-form-item label="巡查人ID" prop="checkerId">
            <el-input v-model="form.checkerId" placeholder="请输入巡查人ID" />
          </el-form-item>
        </el-col> -->
        <!-- <el-col :span="12">
          <el-form-item label="审核人ID" prop="auditId">
            <el-input v-model="form.auditId" placeholder="请输入审核人ID" />
          </el-form-item>
        </el-col> -->
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 批量审核对话框 -->
    <audit-dialog :visible.sync="auditDialog.open" :title="auditDialog.title" :ids="ids" @submit="submitAudit" />

    <!-- 用户导入对话框 -->
    <el-dialog :title="upload.title" :visible.sync="upload.open" width="400px" append-to-body>
      <el-upload ref="upload" :limit="1" accept=".xlsx, .xls" :headers="upload.headers"
        :action="upload.url + '?updateSupport=' + upload.updateSupport" :disabled="upload.isUploading"
        :on-progress="handleFileUploadProgress" :on-success="handleFileSuccess" :auto-upload="false" drag>
        <i class="el-icon-upload"></i>
        <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
        <div class="el-upload__tip text-center" slot="tip">
          <div class="el-upload__tip" slot="tip">
            <el-checkbox v-model="upload.updateSupport" /> 是否更新已经存在的用户数据
          </div>
          <span>仅允许导入xls、xlsx格式文件。</span>
          <el-link type="primary" :underline="false" style="font-size:12px;vertical-align: baseline;"
            @click="importTemplate">下载模板</el-link>
        </div>
      </el-upload>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitFileForm">确 定</el-button>
        <el-button @click="upload.open = false">取 消</el-button>
      </div>
    </el-dialog>
    <!-- 坐标拾取组件 -->
    <coordinate-picker :visible.sync="coordinatePickerVisible" :initialPosition="coordinatePickerInitPosition"
      @save="handleCoordinateSave" />
  </div>
</template>

<script>
import { listGeologicalPoints, getGeologicalPoints, delData, addGeologicalPoints, updateGeologicalPoints, batchAudit } from "@/api/middleData/geologicalPoints";
import { getToken } from "@/utils/auth";
import AuditDialog from '@/views/middleData/xq/audit/index.vue'
import selectTree from '@/components/DeptTmpl/selectTree.vue'
import { listMaintenanceSectionAll } from '@/api/system/maintenanceSection'
import { listAllRoute } from '@/api/system/route'
import { listByMaintenanceSectionId } from '@/api/baseData/common/routeLine'
import { listRiskSectionsAll } from '@/api/middleData/riskSections'
import ImageUpload from '@/views/disaster/ImageUpload.vue' // 图片上传组件
import CoordinatePicker from '@/components/CoordinatePicker'; // 引入坐标拾取组件
import { findFiles } from '@/api/file/index.js'
import moment from "moment";
import { formatStake } from "@/utils/common";
export default {
  name: "GeologicalPoints",
  dicts: ['is_affects_road_safety', 'geological_points_risk_grade', 'sys_no_yes'],
  components: { AuditDialog, selectTree, ImageUpload, CoordinatePicker },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: false,
      dictType: [],
      // 日期范围
      dateRange: [],
      // 总条数
      total: 0,
      // 路域外地质灾害隐患点清单表格数据
      geologicalPointsList: null,
      // 弹出层标题
      title: "",
      // 部门树选项
      deptOptions: undefined,
      // 是否显示弹出层
      open: false,

      // 表单参数
      form: {
        province: '云南省',
      },
      defaultProps: {
        children: "children",
        label: "label"
      },
      // 用户导入参数
      upload: {
        // 是否显示弹出层（用户导入）
        open: false,
        // 弹出层标题（用户导入）
        title: "",
        // 是否禁用上传
        isUploading: false,
        // 是否更新已经存在的用户数据
        updateSupport: 0,
        // 设置上传的请求头部
        headers: { Authorization: "Bearer " + getToken() },
        // 上传的地址
        url: process.env.VUE_APP_BASE_API + "/system/user/importData"
      },
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 50,
        province: null,
        name: null,
        type: null,
        scale: null,
        routeCode: null,
        routeName: null,
        centerStakeNumber: null,
        longitude: null,
        latitude: null,
        perpendicularDistance: null,
        directionAngle: null,
        potentialRiskSegment: null,
        routeSectionName: null,
        managementMaintenanceName: null,
        propertyUnitType: null,
        riskId: null,
        riskGrade: null,
        shape: null,
        managementMaintenanceId: null,
        routeSectionId: null,
        remarks: null,
        photos: null,
        affects: null,
        checkerId: null,
        checkerName: null,
        auditId: null,
        auditName: null,
        auditStatus: null,
        checkTime: null,
        auditTime: null
      },
      // 列信息
      columns: [
        { key: 0, label: `省份`, visible: true },
        { key: 1, label: `灾点名称`, visible: true },
        { key: 2, label: `灾点类别`, visible: true },
        { key: 3, label: `规模等级`, visible: true },
        { key: 4, label: `路线编码`, visible: true },
        { key: 5, label: `路线名称`, visible: true },
        { key: 6, label: `垂点桩号`, visible: true },
        { key: 7, label: `灾点经度`, visible: true },
        { key: 8, label: `灾点纬度`, visible: true },
        { key: 9, label: `与路段垂直距离(m)`, visible: true },
        { key: 10, label: `方向角(°)`, visible: true },
        { key: 11, label: `疑似风险路段`, visible: true },
        { key: 12, label: `路段名称`, visible: true },
        { key: 13, label: `管理处`, visible: true },
        { key: 14, label: `权属单位`, visible: true },
        { key: 15, label: `是否影响道路安全`, visible: true },
        { key: 16, label: `是否为现场复核`, visible: true },
        { key: 17, label: `巡查人`, visible: true },
        { key: 18, label: `审核人`, visible: true },
        { key: 19, label: `审核状态`, visible: true },
        { key: 20, label: `巡查时间`, visible: true },
        { key: 21, label: `风险等级`, visible: true }
      ],
      // 表单校验
      rules: {


      },
      // 批量审核对话框
      auditDialog: {
        open: false,
        title: '批量审核'
      },
      maintenanceSectionList: [],
      routeList: [],
      riskList: [],
      // 图片上传相关
      formImgSrcList: [], // 图片渲染列表
      formImgUrlList: [], // 图片预览列表
      formUploadList: '', // 图片上传列表
      formImgOwnerId: '', // 图片上传ownerId
      formImgNum: [], // 图片数量（图片集用）
      // 坐标相关
      coordinatePickerVisible: false, // 坐标拾取对话框可见性
      coordinatePickerInitPosition: "102.8207599,24.8885797", // 初始坐标
      currentCoordinateType: '', // 当前坐标类型，longitude表示经度，latitude表示纬度
    };
  },
  watch: {
    // 根据名称筛选部门树
  },
  created() {
    this.getList();
    this.getMaintenanceSection();
    this.listAllRoute();
    this.listRiskSectionsAll();
    // this.getDeptTree();
    // this.getConfigKey("sys.user.initPassword").then(response => {
    //   this.initPassword = response.msg;
    // });
  },
  computed: {
    cmpColumns() {
      return (val) => {
        let el = this.columns.find(item => item.label === val)
        if (el) {
          return el.visible
        } else {
          return true
        }
      }
    }
  },
  methods: {
    // 格式化桩号显示
    formatStakeNumber(value) {
      return formatStake(value);
    },
    /** 查询用户列表 */
    getList() {
      this.loading = true;
      // this.queryParams.createTimee = this.dateRange[0]
      // this.queryParams.createTimes = this.dateRange[1]
      listGeologicalPoints(this.queryParams).then(response => {
        this.geologicalPointsList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    restImg() {
      // 重置图片
      this.formDataLine = []
      this.formImgSrcList = [] // 图片缩略图列表
      this.formImgUrlList = [] // 图片预览图列表
      this.formUploadList = '' // 图片上传列表
    },
    // 表单重置
    reset() {
      this.form = {
        province: '云南省',
        name: null,
        type: null,
        scale: null,
        routeCode: null,
        routeName: null,
        centerStakeNumber: null,
        longitude: null,
        latitude: null,
        perpendicularDistance: null,
        directionAngle: null,
        potentialRiskSegment: null,
        routeSectionName: null,
        managementMaintenanceName: null,
        propertyUnitType: null,
        riskId: null,
        riskGrade: null,
        shape: null,
        managementMaintenanceId: null,
        routeSectionId: null,
        remarks: null,
        photos: null,
        affects: null,
        checkerId: null,
        checkerName: null,
        auditId: null,
        auditName: null,
        auditStatus: null,
        checkTime: null,
        auditTime: null
      };
      this.resetForm("form");
      this.restImg();
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dateRange = [];
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id);
      this.single = selection.length != 1;
      this.multiple = !selection.length;
    },
    // 表格点击勾选
    handleRowClick(row) {
      row.isSelected = !row.isSelected;
      this.$refs.table.toggleRowSelection(row);
    },
    // 勾选高亮
    rowStyle({ row, rowIndex }) {
      if (this.ids.includes(row.id)) {
        return { 'background-color': '#E1F0FF', color: '#333' }
      } else {
        return { 'background-color': '#fff', color: '#333' }
      }
    },

    /** 单条记录审核按钮操作 */
    handleAudit(row) {
      this.ids = [row.id]; // 设置当前选中的记录ID
      this.auditDialog.open = true; // 打开审核对话框
      this.auditDialog.title = '审核记录';
    },
    /** 批量审核按钮操作 */
    handleBatchAudit() {
      if (this.ids.length === 0) {
        this.$message.warning('请至少选择一条记录进行审核');
        return;
      }
      this.auditDialog.open = true;
    },
    /** 提交审核 */
    submitAudit(formData) {
      batchAudit(formData).then(response => {
        this.$modal.msgSuccess('审核操作成功');
        this.getList();
      });
    },

    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加路域外地质灾害隐患点清单";
      this.formImgOwnerId = new Date().getTime().toString()
      this.form.photos = this.formImgOwnerId
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids;
      getGeologicalPoints(id).then(async (response) => {
        this.form = response.data;
        this.open = true;
        this.title = "修改路域外地质灾害隐患点清单";
        this.form.checkTime = this.form.checkTime ? new Date(this.form.checkTime) : '';
        this.form.auditTime = this.form.auditTime ? new Date(this.form.auditTime) : '';
        /* 桩号回显相关 */
        const beginMile1 = Math.floor(Number(this.form.centerStakeNumber) / 1000);
        const beginMile2 = Number(this.form.centerStakeNumber) % 1000;
        this.$set(this.form, 'beginMile1', beginMile1);
        this.$set(this.form, 'beginMile2', beginMile2);
        /* 获取图片回显 */
        if (this.form.photos) {
          this.formImgOwnerId = this.form.photos
          await this.queryImg(this.formImgOwnerId)
        } else {
          this.formImgOwnerId = new Date().getTime().toString()
          this.form.photos = this.formImgOwnerId
        }
        this.formUploadList = this.form.photos
      });

    },
    /** 提交按钮 */
    submitForm: function () {
      this.$refs["form"].validate(valid => {
        if (valid) {
          // shape拼接
          if (this.form.longitude && this.form.latitude) {
            this.form.shape = `POINT(${this.form.longitude} ${this.form.latitude})`;
          }
          // 事件格式化
          this.form.checkTime = this.form.checkTime ? moment(this.form.checkTime).format('YYYY-MM-DD HH:mm:ss') : null;
          this.form.auditTime = this.form.auditTime ? moment(this.form.auditTime).format('YYYY-MM-DD HH:mm:ss') : null;
          /* 桩号上传相关 */
          this.form.centerStakeNumber = Number(this.form.beginMile1) * 1000 + Number(this.form.beginMile2) * 1
          if (this.form.id != null) {
            updateGeologicalPoints(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addGeologicalPoints(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const id = row.id || this.ids;
      if (row.id) { // 单个删除
        this.$modal.confirm('是否确认删除改数据？').then(function () {
          let data = { idList: [row.id] };
          return delData(data);
        }).then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        }).catch(() => { });
      } else { // 批量删除
        this.$modal.confirm(`是否确认删除选中的 ${id.length} 条数据项？`).then(function () {
          let data = { idList: id };
          console.log(data)
          return delData(data);
        }).then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        }).catch(() => { });
      }
    },

    /** 导出按钮操作 */
    handleExport() {
      this.download('middleData/geologicalPoints/export', {
        ...this.queryParams
      }, `路域外地址灾害隐患点清单_${new Date().getTime()}.xlsx`)
    },
    /** 导入按钮操作 */
    handleImport() {
      this.upload.title = "用户导入";
      this.upload.open = true;
    },
    /** 下载模板操作 */
    importTemplate() {
      this.download('system/user/importTemplate', {
      }, `user_template.xlsx`)
    },
    // 文件上传中处理
    handleFileUploadProgress(event, file, fileList) {
      this.upload.isUploading = true;
    },
    // 文件上传成功处理
    handleFileSuccess(response, file, fileList) {
      this.upload.open = false;
      this.upload.isUploading = false;
      this.$refs.upload.clearFiles();
      this.$alert("<div style='overflow: auto;overflow-x: hidden;max-height: 70vh;padding: 10px 20px 0;'>" + response.msg + "</div>", "导入结果", { dangerouslyUseHTMLString: true });
      this.getList();
    },
    // 提交上传文件
    submitFileForm() {
      this.$refs.upload.submit();
    },
    listRiskSectionsAll() {
      let params = {
        routeCode: this.form.routeCode,
        routeSectionId: this.form.routeSectionId,
        managementMaintenanceId: this.form.managementMaintenanceId,
      }
      listRiskSectionsAll(params).then((res) => {
        if (res.code == 200) {
          this.riskList = res.data || [];
        }
      })
    },
    /** 查询养护路段下拉列表 */
    getMaintenanceSection() {
      listMaintenanceSectionAll().then((res) => {
        this.maintenanceSectionList = res.data;
      });
    },
    //管理处下拉选点击事件
    deptChange(departmentId) {
      this.queryParams.routeSectionId = null;
      this.queryParams.routeCode = null;
      this.form.routeSectionId = null;
      this.form.routeCode = null;
      listMaintenanceSectionAll({ departmentId: departmentId }).then((res) => {
        this.maintenanceSectionList = res.data;
      });
    },
    listAllRoute() {
      listAllRoute().then((res) => {
        if (res.code == 200) {
          this.routeList = res.data || [];
        }
      })
    },
    //路段下拉选改变事件
    managementChange(routeSectionId) {
      this.queryParams.routeCode = null;
      this.form.routeCode = null;
      listByMaintenanceSectionId({ maintenanceSectionId: routeSectionId }).then((res) => {
        if (res.code == 200) {
          res.data.forEach((item) => {
            item.routeName = item.routeName + '(' + item.routeCode + ')';
          });
          this.routeList = res.data || [];
        }
      });
      if (this.form.routeSectionId) {
        // 获取路段名称
        const maintenanceSection = this.maintenanceSectionList.find(item => item.maintenanceSectionId === this.form.routeSectionId);
        this.form.routeSectionName = maintenanceSection.maintenanceSectionName;
      } else {
        this.form.routeSectionName = ''
      }
    },
    handleNodeSelected(node) {
      this.form.managementMaintenanceId = node.id;
      this.form.managementMaintenanceName = node.label; // 获取对应的 name
    },
    routeCodeChange() {
      // 获取路线名称
      if (this.form.routeCode) {
        const route = this.routeList.find(item => item.routeCode === this.form.routeCode);
        this.form.routeName = route.routeName;
      } else {
        this.form.routeName = ''
      }
    },
    riskChange() {
      // 获取疑是风险路线名称
      if (this.form.riskId) {
        const risk = this.riskList.find(item => item.id === this.form.riskId);
        this.form.potentialRiskSegment = risk.potentialRiskSegment;
      } else {
        this.form.potentialRiskSegment = ''
      }
    },
    /** 图片预览 */
    previewImage(url) {
      this.imagePreview.url = url;
      this.imagePreview.visible = true;
    },

    // 获取图片数据
    async queryImg(id) {
      await findFiles({ ownerId: id }).then(res => {
        if (res.code === 200) {
          this.formImgSrcList = res.data.map(item => ({
            id: item.ownerId + '-' + item.id,
            name: item.originalFilename,
            url: item.thumbUrl,
            imgUrl: item.url,
            remark: item.remark,
            createTime: item.createTime,
            createBy: item.createBy
          }))
          this.formImgUrlList = res.data.map(item => item.url)
          this.formDataLine = this.formImgDateLine()
        }
      }).catch(() => {
        this.$message.error('图片查询失败，请重新打开表单尝试')
      })
    },
    formImgDateLine() {
      let dateLine = []
      if (this.formImgSrcList.length > 0) {
        this.formImgSrcList.forEach(item => {
          let date = item.createTime.split('T')[0]
          if (dateLine.length === 0) {
            dateLine.push({
              time: date,
              data: [item]
            })
          } else {
            let index = dateLine.findIndex(item2 => item2.time === date)
            if (index !== -1) {
              dateLine[index].data.push(item)
            } else {
              dateLine.push({
                time: date,
                data: [item]
              })
            }
          }
        })
        // 时间线排序
        dateLine.sort((a, b) => {
          // 将日期字符串分割并转换为日期对象
          let dateA = new Date(a.time);
          let dateB = new Date(b.time);
          // 比较日期
          return dateA - dateB;
        })
      }
      return dateLine
    },
    // 删除图片
    formDeleteImg(id) {
      this.$modal.confirm('是否确认删除该图片？').then(async () => {
        this.$modal.loading('正在删除图片，请稍候...')
        removeFile(id.split('-')[1]).then(async (res) => {
          if (res.code === 200) {
            this.$message.success('删除图片成功！')
            /* // 移除预览列表图片
            let index = this.formImgSrcList.findIndex(item => item.id === id)
            if (index !== -1) {
              let imgUrl = this.formImgSrcList[index].imgUrl
              let imgIndex = this.formImgUrlList.indexOf(imgUrl)
              if (imgIndex !== -1) {
                this.formImgUrlList.splice(imgIndex, 1)
              }
            }
            // 移除渲染列表图片
            this.formImgSrcList = this.formImgSrcList.filter(item => item.id !== id) */
            await this.queryImg(id.split('-')[0])
            this.$refs.refImageUpload.getImg(this.formUploadList)
          }
          this.$modal.closeLoading()
        }).catch(() => {
          this.$message.error('删除图片失败')
          this.$modal.closeLoading()
        })
      })
    },
    // 点击预览图片时
    formImgPreview(url) {
      let index = this.formImgUrlList.findIndex(item => item === url)
      if (index !== -1) {
        let moveUrl = this.formImgUrlList.splice(index, this.formImgUrlList.length - index)
        this.formImgUrlList.unshift(...moveUrl)
      }
    },
    /**
       * 坐标相关方法
       */
    // 显示坐标拾取组件
    showCoordinatePicker(type) {
      this.currentCoordinateType = type;
      // 如果已有经纬度，设置初始值
      if (this.form.longitude && this.form.latitude) {
        this.coordinatePickerInitPosition = `${this.form.longitude},${this.form.latitude}`;
      } else {
        // 默认昆明市坐标
        this.coordinatePickerInitPosition = "102.8207599,24.8885797";
      }
      this.coordinatePickerVisible = true;
    },
    // 处理坐标保存
    handleCoordinateSave(position) {
      if (!position) return;
      const coordinates = position.split(',');
      if (coordinates.length === 2) {
        const lng = coordinates[0];
        const lat = coordinates[1];
        // 根据坐标类型设置表单字段
        if (this.currentCoordinateType === 'longitude' || this.currentCoordinateType === 'latitude') {
          this.form.longitude = lng;
          this.form.latitude = lng;
        }
      }
    },
  }
};
</script>
<style lang="scss" scoped>
.hasTagsView .app-main[data-v-078753dd] {
  background: #f5f7fa;
}

.tableDiv {
  background-color: white;
  padding-bottom: 10px;
}

.infoBox {
  padding: 15px 15px 0 15px;
  box-sizing: border-box;
  border-radius: 6px;
  border: 1px solid #C4C4C4;
  position: relative;

  .infoTitle {
    user-select: none;
    position: absolute;
    top: 0;
    left: 0;
    padding: 0 10px;
    font-size: 14px;
    line-height: 14px;
    font-weight: bold;
    transform: translateX(15px) translateY(-50%);
    background-color: white;
  }

  .imgBox {
    height: auto;
    width: 100%;

    ::v-deep .el-card__body {
      width: 100%;
      display: flex;
      flex-direction: row;
      flex-wrap: wrap;
      align-content: flex-start;
    }

    .imgBoxCard {
      width: 100%;

      .cardMain {
        height: 260px;
        width: 33%;
        box-sizing: border-box;
        padding: 0 10px;
        display: flex;
        flex-direction: column;
        position: relative;

        .imgDeleteBtn {
          position: absolute;
          z-index: 1;
          top: 20%;
          right: 5%;
        }

        .imgTitle {
          height: 28px;
          width: 100%;
          font-size: 16px;
          font-weight: bold;
          white-space: nowrap;
          text-overflow: ellipsis;
          overflow: hidden;
        }

        .img {
          height: calc(100% - (28px + 28px));
          width: 100%;
          padding: 10px 0;
          position: relative;
          z-index: 0;
        }

        .footer {
          height: 28px;
          color: #888888;
          font-size: 14px;
        }
      }
    }
  }

  .noneBox {
    user-select: none;
    height: 200px;
    width: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    color: #888888;
  }
}

.dwClass {
  font-size: 12px;
  line-height: 3;
  color: #007aff;
  margin-left: 5px;
}

.input-row {
  display: flex;
  align-items: center;
}
</style>
