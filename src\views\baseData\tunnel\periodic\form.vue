<template>
  <div>
    <el-dialog
      :title="title"
      :visible.sync="showDialog"
      width="60%"
      append-to-body
      :before-close="handleClose"
      :close-on-click-modal="false"
    >
      <div
        v-loading="loading"
        style="height: 60vh; overflow-y: auto; padding: 0 10px 0 5px"
      >
        <el-form ref="form" :model="form" :rules="rules" label-width="120px">
          <div style="display: flex; flex-wrap: wrap; margin-bottom: 20px">
            <el-col :span="12">
              <el-form-item prop="tunnelName">
                <template slot="label">
                  <span style="color: #ff4949; margin-right: 4px;">*</span>隧道名称
                </template>
                <div class="click-input" @click="handleInput">
                  <el-input
                    v-model="form.tunnelName"
                    placeholder="请选择"
                    clearable
                    readonly
                  />
                </div>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="养护单位" prop="managementMaintenanceId">
                <SelectTree
                  v-model="form.managementMaintenanceId"
                  :dept-type="201"
                  placeholder="请选择"
                  clearable
                  :disabled="true"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="评定等级" prop="assessmentGrade">
                <el-select
                  v-model="form.assessmentGrade"
                  style="width: 100%"
                  placeholder="请选择"
                  clearable
                >
                  <el-option
                    v-for="item in dict.type.tunnel_assess_grade"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="检查类型" prop="checkType">
                <el-select
                  v-model="form.checkType"
                  style="width: 100%"
                  placeholder="请选择"
                  clearable
                >
                  <el-option
                    v-for="item in dict.type.tunnel_periodic_detection_type"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="检查名称" prop="checkName">
                <el-input
                  v-model="form.checkName"
                  placeholder="请输入"
                  clearable
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="检查单位" prop="checkUnit">
                <el-input
                  v-model="form.checkUnit"
                  placeholder="请输入"
                  clearable
                />
              </el-form-item>
            </el-col>
            <!-- <el-col :span="12">
              <el-form-item
                label="检查描述"
                prop="narrative"
              >
                <el-input
                  v-model="form.narrative"
                  placeholder="请输入"
                  clearable
                />
              </el-form-item>
            </el-col> -->
            <el-col :span="12">
              <el-form-item label="检查日期" prop="checkDate">
                <el-date-picker
                  v-model="form.checkDate"
                  style="width: 100%"
                  type="date"
                  placeholder="请选择"
                  clearable
                  value-format="yyyy-MM-dd"
                />
              </el-form-item>
            </el-col>
            <!-- <el-col :span="12">
              <el-form-item label="结束时间" prop="endDate">
                <el-date-picker
                  v-model="form.endDate"
                  style="width: 100%"
                  type="date"
                  placeholder="请选择"
                  clearable
                  value-format="yyyy-MM-dd"
                />
              </el-form-item>
            </el-col> -->
            <el-col :span="12">
              <el-form-item label="检查评分" prop="checkResult">
                <el-input-number
                  v-model="form.checkResult"
                  style="width: 100%"
                  placeholder="请输入"
                  clearable
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="检查描述" prop="remark">
                <el-input
                  v-model="form.remark"
                  placeholder="请输入"
                  clearable
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="养护建议" prop="suggestions">
                <el-input
                  v-model="form.suggestions"
                  placeholder="请输入"
                  clearable
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="评定单位" prop="assessmentUnit">
                <el-input
                  v-model="form.assessmentUnit"
                  placeholder="请输入"
                  clearable
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="负责人" prop="personInCharge">
                <el-input
                  v-model="form.personInCharge"
                  placeholder="请输入"
                  clearable
                />
              </el-form-item>
            </el-col>
            <el-col :span="12" v-if="this.form.id">
              <el-form-item label="记录人" prop="createBy">
                <!-- <el-cascader
                  v-model="form.createBy"
                  :disabled="true"
                  :options="deptUserOptions"
                  :props="{ multiple: false, value: 'id', emitPath: false}"
                  :show-all-levels="false"
                  style="width: 100%"
                  filterable
                  clearable
                  collapse-tags
                /> -->
                <el-input
                  v-model="form.createBy"
                  placeholder="请输入"
                  clearable
                  disabled
                />
              </el-form-item>
            </el-col>
            <el-col :span="12" v-if="this.form.id">
              <el-form-item label="记录时间" prop="createTime">
                <!-- <el-date-picker
                  :disabled="true"
                  v-model="form.createTime"
                  style="width:100%"

                  placeholder="请选择"
                  clearable
                  value-format="yyyy-MM-dd HH:mm:ss"
                /> -->
                <el-input
                  v-model="form.createTime"
                  placeholder="请输入"
                  clearable
                  disabled
                />
              </el-form-item>
            </el-col>
            <!-- <el-col :span="24">
              <el-form-item
                label="备注"
                prop="remark"
              >
                <el-input
                  v-model="form.remark"
                  type="textarea"
                  placeholder="输入"
                />
              </el-form-item>
            </el-col> -->

            <el-col :span="12">
              <el-form-item label="报告编写人" prop="reportWriter">
                <el-input
                  v-model="form.reportWriter"
                  placeholder="请输入"
                  clearable
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="报告编制日期" prop="reportWriteDate">
                <el-date-picker
                  v-model="form.reportWriteDate"
                  style="width: 100%"
                  type="date"
                  placeholder="请选择"
                  clearable
                  value-format="yyyy-MM-dd"
                />
              </el-form-item>
            </el-col>

            <!-- <el-col :span="12">
              <el-form-item label="备注" prop="remark">
                <el-input
                  type="textarea"
                  v-model="form.remark"
                  placeholder="请输入备注"
                  clearable
                  autosize
                />
              </el-form-item>
            </el-col> -->
            <el-col :span="12">
              <el-form-item label="检查报告" prop="reportPath">
                <FileUpload
                  :key="ownerId"
                  v-model="form.reportPath"
                  :limit="1"
                  :owner-id="ownerId"
                  storage-path="/base/tunnel/periodic/"
                  platform="mpkj"
                />
              </el-form-item>
            </el-col>
          </div>
        </el-form>
      </div>
      <div slot="footer">
        <el-button type="primary" :loading="loading" @click="handleSubmit"
          >提 交</el-button
        >
        <el-button @click="handleClose">取 消</el-button>
      </div>
    </el-dialog>
    <TunnelSelector :show="showSelector" @close="closeSelector" />
  </div>
</template>

<script>
import SelectTree from "@/components/DeptTmpl/selectTree";
import TunnelSelector from "./components/tunnelSelector.vue";
import { getTreeStruct } from "@/api/tmpl";
import { add, edit } from "@/api/baseData/tunnel/periodic/index";
import { createIdWorker } from "@/api/baseData/common";
import { getUser } from "@/api/system/user";

export default {
  name: "periodic-form",
  props: {
    title: { type: String, default: "新增隧道定期数据" },
    showDialog: { type: Boolean, default: false },
    forEdit: { type: Boolean, default: false },
    formData: { default: {} },
  },
  components: { SelectTree, TunnelSelector },
  dicts: ["tunnel_periodic_detection_type", "tunnel_assess_grade"],
  data() {
    return {
      loading: false,
      form: { createBy1: ["2973"] },
      rules: {
        assessmentUnit: [
          { required: true, message: "请选择评定单位", trigger: "change" },
        ],
        //负责人
        personInCharge: [
          { required: true, message: "请输入负责人", trigger: "change" },
        ],
        //检查日期
        checkDate: [
          { required: true, message: "请选择检查日期", trigger: "change" },
        ],
        //结束时间
        // endDate: [
        //   { required: true, message: "请选择结束时间", trigger: "change" },
        // ],
        //检查类型
        checkType: [
          { required: true, message: "请选择检查类型", trigger: "change" },
        ],
        //评定等级
        assessmentGrade: [
          { required: true, message: "请选择评定等级", trigger: "change" },
        ],
        // 检查评分
        checkResult: [
          { required: true, message: "请输入检查评分", trigger: "change" },
        ],
      },
      deptUserOptions: [],
      showSelector: false,
      ownerId: undefined,
    };
  },
  created() {
    if (this.formData.id) {
      this.form = { ...this.formData };
      getUser(this.form.createBy).then((res) => {
        if (res.code === 200) {
          this.form.createBy = res.data.nickName;
        }
      });
    }
    this.init();
  },
  methods: {
    init() {
      createIdWorker().then((res) => {
        if (res.code === 200) {
          this.ownerId = Number(res.data);
        }
      });
      getTreeStruct({ types: 111 }).then((res) => {
        if (res.code === 200) {
          //将deptUserOptions中最底层的children取出组成一个数组

          this.deptUserOptions = res.data;
        }
      });
    },

    handleClose() {
      this.$modal
        .confirm("确认退出？")
        .then(() => {
          this.form = {};
          this.$emit("close", false);
        })
        .catch(() => {});
    },
    handleInput() {
      if (this.forEdit) {
        this.$message.warning("编辑不允许更改所选隧道！");
        return;
      }
      this.showSelector = true;
    },
    handleSubmit() {
      this.$refs.form.validate((valid) => {
        if (!valid) return;
        if (!this.form.tunnelName) {
          this.$message.warning("未选择隧道信息！");
          return;
        }
        this.loading = true;
        for (const key in this.form) {
          if (Array.isArray(this.form[key])) {
            this.form[key] = this.form[key].join(",");
          }
        }
        let api = this.form.id ? edit : add;
        api(this.form).then((res) => {
          if (res.code === 200) {
            this.$message.success("操作成功！");
            this.$emit("close", true);
          }
        })
        .finally(() => {
          this.loading = false;
        });
      });
    },
    closeSelector(obj) {
      if (obj) {
        const { tunnelName, id, managementMaintenanceId, assessmentGrade } =
          obj;
        this.form.tunnelName = tunnelName;
        this.form.tunnelId = id;
        this.form.managementMaintenanceId = managementMaintenanceId;
      }
      this.showSelector = false;
    },
  },
  computed: {},
  watch: {},
};
</script>

<style lang="scss" scoped>
.click-input ::v-deep .el-input .el-input__inner {
  cursor: pointer;
}
::v-deep .el-input.is-disabled .el-input__inner {
  background: #fff;
  border: 1px solid #dcdfe6;
  color: #1d2129;
}
::v-deep .el-input.is-disabled .el-input__inner::-webkit-input-placeholder {
  color: #666666;
}
</style>
