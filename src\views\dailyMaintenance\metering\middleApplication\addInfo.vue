<template>
  <div class="road-interflow-edit">
    <el-row :gutter="15">
      <el-row>
        <el-col :span="24">
          <el-form
              ref="queryForm"
              :inline="true"
              :model="queryParams"
              label-width="68px"
              size="mini"
          >
            <el-form-item prop="disType">
              <el-select v-model="queryParams.disType" filterable placeholder="请选择事件类型" clearable style="width: 240px;">
                <el-option v-for="item in advicesList"
                           :key="item.value"
                           :label="item.label"
                           :value="item.value">
                </el-option>
              </el-select>
            </el-form-item>
<!--            <el-form-item prop="maiSecName">-->
<!--              <RoadSection v-model="queryParams.maiSecName" placeholder="路段名称" style="width: 190px"/>-->
<!--            </el-form-item>-->
<!--            <el-form-item>-->
<!--              <RouteCodeSection v-model="queryParams.routeCode" :maintenanceSectionId="queryParams.maiSecName"-->
<!--                                placeholder="路线编码" style="width: 190px;"/>-->
<!--            </el-form-item>-->
            <el-form-item>
              <el-input
                  v-model="queryParams.beginMile"
                  clearable
                  placeholder="起止桩号"
                  style="width: 110px"
              >
              </el-input>
              <div style="width: 20px;display: inline-block;text-align: center">~</div>
              <el-input
                  v-model="queryParams.endMile"
                  clearable
                  placeholder="起止桩号"
                  style="width: 110px"
              >
              </el-input>
            </el-form-item>
            <el-form-item>
              <el-date-picker
                v-model="queryParams.dateArr"
                type="daterange"
                value-format="yyyy-MM-dd"
                :style="{width: '100%'}"
                range-separator="至"
                start-placeholder="完工日期"
                end-placeholder="完工日期">
              </el-date-picker>
            </el-form-item>
<!--            <el-form-item>-->
<!--              <el-input v-model="queryParams.schemeCode" placeholder="子目号" style="width: 190px"></el-input>-->
<!--            </el-form-item>-->
<!--            <el-form-item>-->
<!--              <el-input v-model="queryParams.schemeName" placeholder="方法名" style="width: 190px"></el-input>-->
<!--            </el-form-item>-->
            <el-form-item>
              <el-input v-model="queryParams.code" placeholder="施工单编号" style="width: 190px"></el-input>
            </el-form-item>
            <el-form-item>
              <el-button icon="el-icon-search" size="mini" type="primary" @click="handleQuery">搜索</el-button>
              <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
            </el-form-item>
          </el-form>
        </el-col>
      </el-row>
      <el-row :gutter="10" class="mb8">
        <el-col :span="1.5">
          <el-button
              icon="el-icon-view"
              size="mini"
              type="primary"
              @click="dialogVisible = true"
          >事件信息
          </el-button
          >
        </el-col>
      </el-row>
      <el-row>
        <div class="draggable">
          <el-table v-adjust-table
              ref="dataTable"
              v-loading="loading"
              :data="tableData"
              border
              height="600"
              highlight-current-row
              row-key="settleId"
              size="mini"
              stripe
              style="width: 100%"
              @row-click="handleClickRow"
              @selection-change="handleSelectionChange"
              @expand-change="loadData"
          >
            <el-table-column type="expand">
              <template slot-scope="props">
                <el-table v-adjust-table :data="props.row.methodList" style="width: 100%" v-loading="methodLoading">
                  <el-table-column
                      prop=""
                      align="center"
                      label="">
                  </el-table-column>
                  <el-table-column
                      prop="schemeCode"
                      align="center"
                      label="子目号">
                  </el-table-column>
                  <el-table-column
                      prop="schemeName"
                      align="center"
                      label="养护方法">
                  </el-table-column>
                  <el-table-column
                      prop="calcDesc"
                      align="center"
                      label="计算式">
                  </el-table-column>
                  <el-table-column
                      prop="num"
                      align="center"
                      label="方法数量">
                  </el-table-column>
                  <el-table-column
                      prop="unit"
                      align="center"
                      label="方法单位">
                  </el-table-column>
                  <el-table-column
                      prop="price"
                      align="center"
                      label="单价">
                  </el-table-column>
                  <el-table-column
                      prop="amount"
                      align="center"
                      label="金额">
                  </el-table-column>
                  <el-table-column
                      prop="remark"
                      align="center"
                      label="备注">
                  </el-table-column>
                  <el-table-column
                      prop="status"
                      align="center"
                      label="状态">
                    <template slot-scope="scope">
                      <dict-tag :options="dict.type['testing_calc_status']" :value="scope.row.status"/>
                    </template>
                  </el-table-column>
                </el-table>
              </template>
            </el-table-column>
            <el-table-column align="center" type="selection" width="50"/>
            <el-table-column
                align="center"
                label="序号"
                type="index"
                width="50"
            />
            <template v-for="(column,index) in columns">
              <el-table-column v-if="column.visible"
                               :label="column.label"
                               :prop="column.field"
                               :width="column.width"
                               show-overflow-tooltip
                               align="center">
                <template slot-scope="scope">
                  <dict-tag v-if="column.dict" :options="dict.type[column.dict]" :value="scope.row[column.field]"/>
                  <template v-else-if="column.slots">
                    <RenderDom :index="index" :render="column.render" :row="scope.row"/>
                  </template>
                  <span v-else-if="column.isTime">{{ parseTime(scope.row[column.field], '{y}-{m}-{d}') }}</span>
                  <span v-else>{{ scope.row[column.field] }}</span>
                </template>
              </el-table-column>
            </template>
          </el-table>
          <pagination
              v-show="total>0"
              :limit.sync="queryParams.pageSize"
              :page.sync="queryParams.pageNum"
              :total="total"
              @pagination="handleQuery"
          />
        </div>
      </el-row>
      <el-col :span="24" style="text-align: right;padding-right: 7.5px;margin-top: 18px">
        <el-button type="primary" @click="onSave">保 存</el-button>
        <el-button @click="onClose">退 出</el-button>
      </el-col>
    </el-row>
    <el-dialog :visible.sync="dialogVisible" append-to-body destroy-on-close modal-append-to-body title="事件信息"
               width="80%">
        <event-detail :dis-id="rowData.disId" :daliy-id="rowData.daliyId"/>
    </el-dialog>
  </div>
</template>
<script>
import {addDetail, listSettle, listMethodBySettleId} from "@/api/dailyMaintenance/metering/middleApplication"
import EventTreeInfo from "@/views/dailyMaintenance/component/eventTreeInfo.vue";
import RoadSection from "@/views/baseData/components/roadSection/index.vue";
import RouteCodeSection from "@/components/RouteCodeSection/index.vue";
import {listAllDiseases} from "@/api/patrol/diseases";
import EventDetail from "@/views/dailyMaintenance/component/eventDetail.vue";

export default {
  dicts: ['route_direction', 'lane', 'sys_asset_type', 'system_have', 'calc_status', 'testing_calc_status'],
  components: {
    EventDetail, RoadSection, EventTreeInfo, RouteCodeSection,
    RenderDom: {
      functional: true,
      props: {
        row: Object,
        index: Number,
        render: Function
      },
      render(createElement, ctx) {
        const {row, index} = ctx.props
        return ctx.props.render(row, index)
      }
    }},
  data() {
    return {
      columns: [
        {key: 0, width: 100, field: 'name', label: `施工单`, visible: true},
        {key: 1, width: 100, field: 'code', label: `施工单编号`, visible: true},
        {key: 2, width: 100, field: 'domainName', label: `管养单位`, visible: true},
        {key: 1, width: 100, field: 'assetMainTypeName', label: `资产类型`, visible: true, dict: 'sys_asset_type'},
        {key: 4, width: 100, field: 'maiSecId', label: `路段名称`, visible: true},
        {key: 5, width: 100, field: 'routeCode', label: `路线编码`, visible: true},
        {key: 4, width: 100, field: 'direction', label: `上下行`, visible: true, dict: 'route_direction'},
        {key: 5, width: 100, field: 'lane', label: `位置`, visible: true, dict: 'lane'},
        {key: 8, width: 100, field: 'beginMile', label: `起点桩号`, visible: true, slots: true, render: (row, index) => {
            return (
                <span>{this.formatPile(row.beginMile)}</span>
            )
          }},
        {key: 9, width: 100, field: 'endMile', label: `终点桩号`, visible: true, slots: true, render: (row, index) => {
            return (
                <span>{this.formatPile(row.endMile)}</span>
            )
          }},
        {key: 8, width: 100, field: 'disTypeName', label: `事件类型`, visible: true, dict: 'sys_asset_type'},
        {key: 11, width: 100, field: 'disDesc', label: `事件内容`, visible: true},
        {key: 12, width: 100, field: 'realEDate', label: `完工时间`, visible: true},
        {key: 13, width: 100, field: 'sumFund', label: `总金额`, visible: true},
        {key: 14, width: 100, field: 'unsettledAmount', label: `未结算金额`, visible: true},
        {key: 15, width: 100, field: 'calcStatus', label: `结算状态`, visible: true, dict: 'calc_status'},
        {key: 16, width: 100, field: 'addUnitPrice', label: `有无新增单价`, visible: true, dict: 'system_have'},
      ],
      queryParams: {
        pageNum: 1,
        pageSize: 50,
      },
      tableData: [],
      selectIds: [],
      advicesList: [],
      total: 0,
      dialogVisible: false,
      loading: true,
      methodLoading: true,
      rowData: {}
    }
  },
  props: {
    maiSecId: {
      type: String,
      default: ''
    },
    calcId: {
      type: String,
      default: ''
    }
  },
  watch: {
    calcId: {
      handler(val) {
        if (val) {
          this.handleQuery()
        }
      }
    }
  },
  created() {
    this.getDisList()
    this.handleQuery()
  },
  methods: {
    handleQuery() {
      this.loading = true
      this.queryParams.calcId = this.calcId
      if (this.queryParams.dateArr) {
        this.queryParams.startRealEDate = this.queryParams.dateArr[0]
        this.queryParams.endRealEDate = this.queryParams.dateArr[1]
      }
      listSettle(this.queryParams).then(res => {
        this.tableData = res.rows
        this.total = res.total
        this.loading = false
      })
    },
    resetQuery() {
      this.queryParams = {
        pageNum: 1,
        pageSize: 50,
      }
    },
    // 选中
    handleSelectionChange(e) {
      this.selectIds = e
    },
    // 获取事件列表
    getDisList() {
      listAllDiseases().then(res => {
        res.data.forEach(item => {
          this.advicesList.push({
            label: item.diseaseName,
            value: item.id
          })
        })
      })
    },
    handleClickRow(e) {
      e.isSelected = !e.isSelected;
      this.rowData = e
      this.$refs.dataTable.toggleRowSelection(e);
    },
    loadData(row) {
      this.methodLoading = true
      listMethodBySettleId({settleId: row.settleId}).then(res => {
        this.$set(row, 'methodList', res.data)
        this.methodLoading = false
      })
    },

    onSave() {
      const params = []
      if (this.selectIds.length === 0) {
        this.$modal.msgWarning('没有选中的数据')
        return
      }
      for (let i = 0; i < this.selectIds.length; i++) {
        params.push({
          calcId: this.calcId,
          settleId: this.selectIds[i].settleId
        })
      }
      addDetail(params).then(res => {
        this.$modal.msgSuccess('保存成功')
        this.onClose()
      })
    },
    onClose() {
      this.$emit('close')
    }
  }
}
</script>
<style lang="scss" scoped>

</style>
<style lang="scss" scoped>
@import "@/assets/styles/business.scss";
</style>
