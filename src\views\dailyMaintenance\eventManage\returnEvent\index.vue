<template>
  <div class="app-container maindiv">
    <el-row :gutter="20">
      <!--角色数据-->
      <el-row>
        <el-col :span="24" :xs="24">
          <el-row>
            <el-form
                ref="queryForm"
                :model="queryParams"
                size="mini"
                :inline="true"
                label-width="68px"
            >
              <el-form-item label="" prop="domainId">
                <selectTree
                  :key="'field2'"
                  style="width: 240px"
                  v-model="queryParams.domainIdStr"
                  :deptType="100" :deptTypeList="[1, 3, 4]"
                  placeholder="管养单位"
                  clearable
                  filterable
                />
              </el-form-item>
              <el-form-item label="" prop="maiSecId">
                <RoadSection v-model="queryParams.maiSecId" :deptId="queryParams.domainIdStr" placeholder="路段名称"/>
              </el-form-item>
              <el-form-item label="" prop="dealType">
                <dict-select type="disposal_type" clearable
                             v-model="queryParams.dealType" placeholder="请选择处理类型"
                             style="width: 240px"></dict-select>
              </el-form-item>
              <el-form-item label="" prop="reportName">
                <el-input
                    v-model="queryParams.reportName"
                    placeholder="请输入上报人"
                    clearable
                    style="width: 240px"
                >
                </el-input>
              </el-form-item>
              <el-form-item>
                <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
                <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
                <el-button v-show="!showSearch" @click="showSearch=true" icon="el-icon-arrow-down" circle></el-button>
                <el-button v-show="showSearch" @click="showSearch=false" icon="el-icon-arrow-up" circle></el-button>
              </el-form-item>
            </el-form>
            <el-col :span="24">
              <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch"
                       label-width="68px">
                <el-form-item label="" prop="beginTime">
                  <el-date-picker
                      v-model="queryParams.beginTime"
                      format="yyyy-MM-dd"
                      value-format="yyyy-MM-dd HH:mm:ss"
                      style="width: 240px"
                      placeholder="上报开始时间"
                      clearable
                  ></el-date-picker>
                </el-form-item>
                <el-form-item label="" prop="endTime">
                  <el-date-picker
                      v-model="queryParams.endTime"
                      format="yyyy-MM-dd"
                      value-format="yyyy-MM-dd HH:mm:ss"
                      style="width: 240px"
                      placeholder="上报结束时间"
                      clearable
                  ></el-date-picker>
                </el-form-item>
                <el-form-item label="" prop="diseaseType">
                  <el-select v-model="queryParams.diseaseType" style="width: 100%" placeholder="请选择事件类型" filterable>
                    <el-option v-for="dict in disType" :key="dict.dictValue"
                               :label="dict.dictLabel" :value="dict.dictValue">
                    </el-option>
                  </el-select>
                </el-form-item>
                <el-form-item label="" prop="diseaseDesc">
                  <el-input
                      v-model="queryParams.diseaseDesc"
                      placeholder="请输入事件描述"
                      clearable
                      style="width: 240px"
                  >
                  </el-input>
                </el-form-item>
                <el-form-item label="" prop="createReason">
                  <dict-select type="event_type" clearable
                               v-model="queryParams.createReason" placeholder="请选择事件分类"
                               style="width: 240px"></dict-select>
                </el-form-item>
                <el-form-item label="" prop="disFrom">
                  <el-select v-model="queryParams.disFrom" placeholder="请选择事件来源" clearable style="width: 240px">
                    <el-option
                      key="直接新增"
                      label="直接新增"
                      value="直接新增"
                    ></el-option>
                    <el-option
                      key="巡查发现"
                      label="巡查发现"
                      value="巡查发现"
                    ></el-option>
                  </el-select>
                </el-form-item>
              </el-form>
            </el-col>
          </el-row>
        </el-col>
      </el-row>
      <!--操作按钮区开始-->
      <el-row :gutter="10" class="mb8">
<!--        <el-col :span="1.5">-->
<!--          <el-button-->
<!--              type="primary"-->
<!--              icon="el-icon-plus"-->
<!--              size="mini"-->
<!--              @click="handleReturn"-->
<!--          >批量恢复-->
<!--          </el-button-->
<!--          >-->
<!--        </el-col>-->
        <el-col :span="1.5">
          <el-button
              type="danger"
              icon="el-icon-delete"
              size="mini"
              @click="handleBatchDelete"
              v-has-menu-permi="['roadDisease:roadDisease:removeBatchByIds']"
          >批量删除
          </el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button
              type="warning"
              icon="el-icon-download"
              size="mini"
              v-has-menu-permi="['roadDisease:roadDisease:export']"
              @click="exportList"
          >导出清单
          </el-button
          >
        </el-col>
<!--        <el-col :span="1.5">-->
<!--          <el-button-->
<!--              type="success"-->
<!--              icon="el-icon-view"-->
<!--              size="mini"-->
<!--              @click="handleOpenReview"-->
<!--          >批量审核-->
<!--          </el-button-->
<!--          >-->
<!--        </el-col>-->
        <right-toolbar
            :showSearch.sync="showSearch"
            @queryTable="handleQuery"
            :columns="columns"
        ></right-toolbar>
      </el-row>
      <el-row>
        <div class="draggable">
          <el-table v-adjust-table
              size="mini"
              style="width: 100%"
              v-loading="loading"
              border
              :data="tableData"
              row-key="id"
              ref="dataTable"
              stripe
              @row-click="handleClickRow"
              @selection-change="handleSelectionChange"
              :height="
                showSearch ? 'calc(100vh - 330px)' : 'calc(100vh - 270px)'
              "
          >
            <el-table-column type="selection" width="50" align="center"/>
            <el-table-column
                label="序号"
                align="center"
                type="index"
                width="50"
            />
            <template v-for="(column,index) in columns">
              <el-table-column :label="column.label"
                               v-if="column.visible"
                               align="center"
                               :prop="column.field"
                               show-overflow-tooltip
                               :width="column.width">
                <template slot-scope="scope">
                  <dict-tag v-if="column.dict" :options="dict.type[column.dict]" :value="scope.row[column.field]"/>
                  <template v-else-if="column.slots">
                    <RenderDom :row="scope.row" :index="index" :render="column.render"/>
                  </template>
                  <span v-else-if="column.isTime">{{ parseTime(scope.row[column.field], '{y}-{m}-{d}') }}</span>
                    <span v-else>{{ scope.row[column.field] }}</span>
                </template>
              </el-table-column>
            </template>
            <el-table-column
                label="操作"
                fixed="right"
                align="center"
                width="250"
                class-name="small-padding fixed-width"
            >
              <template slot-scope="scope">
                <el-button
                    size="mini"
                    type="text"
                    icon="el-icon-view"
                    @click="handleView(scope.row)"
                >查看
                </el-button
                >
<!--                <el-button-->
<!--                    size="mini"-->
<!--                    type="text"-->
<!--                    icon="el-icon-check"-->
<!--                    @click="handleCheck(scope.row)"-->
<!--                >审核-->
<!--                </el-button-->
<!--                >-->
                <el-button
                    size="mini"
                    type="text"
                    icon="el-icon-delete"
                    v-has-menu-permi="['roadDisease:roadDisease:delete\n']"
                    @click="handleDelete(scope.row)"
                >删除
                </el-button
                >
                <el-button
                    size="mini"
                    type="text"
                    icon="el-icon-plus"
                    v-has-menu-permi="['roadDisease:roadDisease:edit']"
                    @click="handleSubmit(scope.row)"
                >提交
                </el-button
                >
              </template>
            </el-table-column>
          </el-table>
          <pagination
              v-show="total>0"
              :total="total"
              :page.sync="queryParams.pageNum"
              :limit.sync="queryParams.pageSize"
              @pagination="handleQuery"
          />
        </div>
      </el-row>
    </el-row>
    <!-- 详情信息弹框 -->
    <el-dialog :title="detailTitle" v-if="openDetail" destroy-on-close :visible.sync="openDetail" width="80%">
      <Detail @close="handleCloseDetail" :type="type" :row-data="rowData" :domainId="queryParams.domainId"></Detail>
    </el-dialog>
    <el-dialog title="批量审核" destroy-on-close :visible.sync="openCheck" width="80%">
      <el-form ref="form" :model="formData" :rules="rules" size="medium" label-width="100px">
        <el-form-item label="审核意见" prop="receiveComment">
          <el-input
              v-model="formData.receiveComment"
              type="textarea"
              :rows="4"
              placeholder="请输入审核意见"
          />
        </el-form-item>
        <div style="text-align: right">
          <el-button type="primary" @click="onSubmit(1)">有 效</el-button>
          <el-button type="primary" @click="onSubmit(0)">无 效</el-button>
        </div>
      </el-form>
    </el-dialog>
    <el-dialog title="附件列表" destroy-on-close :visible.sync="openFile" width="80%">
      <file-upload v-model="disPicPath" :forView="true"></file-upload>
    </el-dialog>
  </div>
</template>

<script>
import Detail from "./detail.vue";
import fileUpload from "@/components/FileUpload/index.vue";

import {
  findAuditDataList,
  deleteAuditData,
  editAuditData,
  batchDeleteAuditData,
  batchReviewAuditData,
  batchReviewInvalidAuditData,
  editBatch
} from '@/api/dailyMaintenance/eventManage/eventReview'
import selectTree from "@/components/DeptTmpl/selectTree.vue";
import RoadSection from "@/views/baseData/components/roadSection/index.vue";
import {formatPile} from "@/utils/ruoyi";
import { listAllDiseases } from '@/api/patrol/diseases'

export default {
  name: 'ReturnEvent',
  components: {
    RoadSection, selectTree,
    Detail, fileUpload,
    RenderDom: {
      functional: true,
      props: {
        row: Object,
        index: Number,
        render: Function
      },
      render(createElement, ctx) {
        const {row, index} = ctx.props
        return ctx.props.render(row, index)
      }
    }
  },
  dicts: ['disposal_type', 'event_type', 'contract_status', 'sys_asset_type'],
  props: [],
  data() {
    return {
      leftTotal: 1,
      showSearch: false,
      queryParams: {
        domainId: '',
        pageNum: 1,
        pageSize: 50,
        handleStatusName: 3
      },
      total: 0,
      columns: [
        {key: 0, width: 100, field: 'maiSecId', label: `路段名称`, visible: true},
        {key: 1, width: 100, field: 'routeCode', label: `路线编码`, visible: true},
        {key: 2, width: 100, field: 'beginMileShow', label: `起点桩号`, visible: true},
        {key: 3, width: 100, field: 'endMileShow', label: `终点桩号`, visible: true},
        {key: 4, width: 100, field: 'domainName', label: `管养单位`, visible: true},
        {key: 5, width: 100, field: 'domainName', label: `管理处`, visible: true},
        {key: 6, width: 150, field: 'diseaseName', label: `事件类型`, visible: true, dict: 'sys_asset_type'},
        {key: 7, width: 200, field: 'diseaseDesc', label: `事件描述`, visible: true},
        {key: 8, width: 100, field: 'dealType', label: `处置类型`, visible: true, dict: 'disposal_type'},
        {key: 9, width: 100, field: 'handleStatusName', label: `事件状态`, visible: true},
        {key: 10, width: 100, field: 'reportName', label: `上报人`, visible: true},
        {key: 11, width: 100, field: 'disFrom', label: `事件来源`, visible: true},
        {key: 12, width: 100, field: 'createReason', label: `事件分类`, visible: true, dict: 'event_type'},
        {key: 13, width: 150, field: 'collectDate', label: `采集时间`, visible: true},
        {
          key: 14, width: 100, field: 'disPicPath', label: `附件`, visible: true, slots: true, render: (row, index) => {
            return (
              <el-button
                size="mini"
                disabled = {!row.disPicPath}
                type="text" onClick={e => this.handleOpenFile(e, row)}>查看</el-button>
            )
          }
        }
      ],
      tableData: [],
      detailTitle: '新增事件',
      openDetail: false,
      rowData: {},
      openCheck: false,
      openFile: false,
      disPicPath: '',
      formData: {},
      loading: false,
      selectIds: [],
      rules: {
        receiveComment: [{
          required: true,
          message: '请输入审核意见',
          trigger: 'blur'
        }],
      },
      type: '',
      disType: []
    }
  },
  computed: {},
  watch: {},
  created() {
    this.getDisType()
    this.handleQuery()
  },
  mounted() {
  },
  methods: {
    //  查询
    handleQuery() {
      this.loading = true
      this.queryParams.domainId = parseInt(this.queryParams.domainIdStr) || null
      findAuditDataList(this.queryParams).then(res => {
        if (res.code == 200) {
          this.loading = false
          res.rows.forEach(item => {
            item.beginMileShow = formatPile(item.beginMile)
            item.endMileShow = formatPile(item.endMile)
          })
          this.tableData = res.rows
          this.total = res.total
        }
      })
    },
    // 修改
    handleUpdate(e) {
    },
    // 审核
    handleCheck(e) {
      this.type = 'examine'
      this.detailTitle = '审核上报事件'
      this.rowData = e
      this.openDetail = true
    },
    // 查看
    handleView(e) {
      this.type = 'view'
      this.detailTitle = '查看上报事件'
      this.rowData = e
      this.openDetail = true
    },
    // 删除
    handleDelete(e) {
      console.log(e)
      this.$modal.confirm('是否确认删除').then(() => {
        deleteAuditData(e.id).then(res => {
          this.handleQuery();
          this.$modal.msgSuccess("删除成功");
        })
      })
    },
    // 选中
    handleSelectionChange(e) {
      this.selectIds = e.map(obj => obj.id)
    },
    // 打开批量审核弹窗
    handleOpenReview() {
      if (this.selectIds.length > 0)
        this.openCheck = true
    },
    // 批量删除
    handleBatchDelete() {
      if (this.selectIds.length > 0)
        this.$modal.confirm('是否确认删除所选的数据').then(() => {
          batchDeleteAuditData(this.selectIds).then(res => {
            this.handleQuery();
            this.$modal.msgSuccess("删除成功");
          })
        })
    },
    handleCloseDetail() {
      this.openDetail = false
      this.handleQuery()
    },
    // 导出清单按钮
    exportList() {
      this.queryParams.fileName = '退回事件导出清单'
      this.download(
          'manager/roaddisease/export',
          {...this.queryParams},
          `roaddisease_${new Date().getTime()}.xlsx`,
        {
          headers: { 'Content-Type': 'application/json;' },
          parameterType: 'body'
        }
      )
    },
    handleOpenFile(e, row) {
      this.disPicPath = ''
      this.openFile = true
      this.disPicPath = row.disPicPath
    },
    resetQuery() {
      this.queryParams = {
        pageNum: 1,
        pageSize: 50,
        handleStatusName: 3
      }
      this.handleQuery()
    },
    onSubmit(status) {
      this.$refs.form.validate(valid => {
        if (!valid) return
        // 根据selectIds 获取选中的数据
        const selectData = this.tableData.filter(item => this.selectIds.includes(item.id))
        // 循环selectData，赋值receiveComment
        selectData.forEach(item => {
          item.receiveComment = this.formData.receiveComment
        })
        // 根据status 判断审核状态
        if (status == 1) {
          // 审核通过
          batchReviewAuditData(selectData).then(res => {
            if (res.code == 200) {
              this.$modal.msgSuccess('操作成功')
              this.openCheck = false
              this.handleQuery()
            }
          })
        } else {
          // 审核不通过
          batchReviewInvalidAuditData(selectData).then(res => {
            if (res.code == 200) {
              this.$modal.msgSuccess('操作成功')
              this.openCheck = false
              this.handleQuery()
            }
          })
        }
      })
    },
    getDisType() {
      listAllDiseases().then(res => {
        this.disType = res.data.map(item => {
          return {
            dictLabel: item.diseaseName,
            dictValue: item.id
          }
        })
      })
    },
    // 恢复
    handleReturn(rows) {
      console.log(rows)
      if (rows.id) {
        editBatch([...rows]).then(res => {
          this.handleQuery();
          this.$modal.msgSuccess("恢复成功");
        })
      } else if (this.selectIds.length > 0)
        this.$modal.confirm('是否确认恢复所选的数据').then(() => {
          // 根据selectIds 获取选中的数据
          const selectData = this.tableData.filter(item => this.selectIds.includes(item.id))
          editBatch(selectData).then(res => {
            this.handleQuery();
            this.$modal.msgSuccess("恢复成功");
          })
        })
    },
    handleSubmit(row) {
      this.type = 'add'
      this.detailTitle = '查看上报事件'
      this.rowData = row
      this.openDetail = true
      // row.handleStatus = 1
      // editAuditData(row).then(res => {
      //   this.handleQuery();
      //   this.$modal.msgSuccess("提交成功");
      // })
    },
    handleClickRow(e) {
      e.isSelected = !e.isSelected;
      this.$refs.dataTable.toggleRowSelection(e);
    },
  }
}
</script>

<style lang="scss" scoped>
.leftDiv {
  border-right: 1px solid #d8dce5;
  min-height: calc(100vh - 100px);
  overflow-y: auto;
  height: calc(80vh - 150px);
  position: relative;
  top: -20px;
  padding-top: 10px;
  background-color: white;
}

.leftIcon {
  border: 1px solid #dcdfe6;
  border-radius: 8px;
  width: 16px;
  height: 50px;
  line-height: 50px;
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
  position: absolute;
  right: 0;
  top: 300px;
  z-index: 2;
}

.leftIcon:hover {
  background-color: #dcdfe6;
}

.rightIcon {
  border: 1px solid #dcdfe6;
  border-radius: 8px;
  width: 16px;
  height: 50px;
  line-height: 50px;
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
  position: absolute;
  left: -10px;
  top: 280px;
  z-index: 10;
  background: white;
}

.rightIcon:hover {
  background-color: #dcdfe6;
}

.left-total {
  font-size: 13px;
  line-height: 28px;
  color: #606266;
  position: absolute;
  bottom: 10px;
  right: 10px;
}
</style>
<style lang="scss" scoped>
@import "@/assets/styles/business.scss";
</style>
