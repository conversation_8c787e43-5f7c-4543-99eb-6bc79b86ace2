<template>
  <div class="app-container maindiv">
    <el-row :gutter="20">
      <el-col :span="24" :xs="24">
        <!--数据表格开始-->
        <div class="tableDiv">
          <el-table v-adjust-table stripe size="mini" height="500px"
                    style="width: 100%" v-loading="loading" border :data="dataList"
          >
            <el-table-column fixed label="序号" type="index" width="50"></el-table-column>
            <template v-for="(column,index) in columns">
              <el-table-column v-if="column.visible"
                               :label="column.label"
                               :prop="column.field"
                               :width="column.width"
                               align="center">
                <template slot-scope="scope">
                  <dict-tag v-if="column.dict" :options="dict.type[column.dict]" :value="scope.row[column.field]"/>
                  <template v-else-if="column.slots">
                    <RenderDom :index="index" :render="column.render" :row="scope.row"/>
                  </template>
                  <span v-else-if="column.isTime">{{ parseTime(scope.row[column.field], '{y}-{m}-{d}') }}</span>
                  <span v-else>{{ scope.row[column.field] }}</span>
                </template>
              </el-table-column>
            </template>

            <el-table-column label="操作">
              <template slot-scope="scope">
                <el-link type="primary"  v-hasPermi="['comm:node:opinion:edit']" @click="openEditDialog(scope.row)">编辑</el-link>
              </template>
            </el-table-column>
          </el-table>
        </div>
        <!--数据表格结束-->
      </el-col>
    </el-row>

    <el-dialog
      append-to-body
      :visible.sync="editDialog"
      width="650px"
    >
      <el-form :model="editForm">
        <el-form-item label="操作人" label-width="100px">
          <el-cascader
              v-model="editForm.assignee"
              :options="deptUserOptions"
              :props="assigneeProps"
              :show-all-levels="false"
              ref="assigneeRef"
              filterable
              clearable
              @change="assigneeChange"
              style="width: 460px"
            >
          </el-cascader>
        </el-form-item>
        <el-form-item label="操作意见" label-width="100px">
          <el-input v-model="editForm.content" style="width: 460px;"/>
        </el-form-item>
        <el-form-item label="操作时间" label-width="100px">
          <el-date-picker
            v-model="editForm.endTime"
            type="datetime"
            format="yyyy-MM-dd HH:mm:ss"
            value-format="yyyy-MM-dd HH:mm:ss"
            placeholder="结束时间">
          </el-date-picker>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="editDialog = false">取 消</el-button>
        <el-button type="primary" @click="saveEdit">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { getTreeStruct,editNodeInfo } from "@/api/tmpl";
import {updateIssuanceTime} from "@/api/dailyMaintenance/constructionOrder/noticeView";
import {updateTheftIssueDate} from "@/api/theft/taskList";
import {updateIssueDate} from "@/api/maintenanceProject/taskList";
export default {
  name: "operateInfo",
  components: {
    RenderDom: {
      functional: true,
      props: {
        row: Object,
        index: Number,
        render: Function
      },
      render(createElement, ctx) {
        const {row, index} = ctx.props
        return ctx.props.render(row, index)
      }
    }
  },
  props: {
    businessKey: {
      type: String,
      default: ''
    },
    getNodeInfo: {
      type: Function
    },
    type: {
      type: String,
      default: ''
    },
    paramName: {
        type: String,
        default: 'businessKey'
    }

  },
  data() {
    return {
      loading: false,
      queryParams: {
        pageNum: 1,
        pageSize: 3,
      },
      editDialog: false,
      editForm: {
        id: '',
        assignee: '',
        content: '',
        startTime: '',
        endTime: '',
        taskId: ''
      },
      // 列信息
      columns: [
        {key: 0, field: 'nodeName', label: `操作节点`, visible: true},
        {key: 1, field: 'nodeName', label: `操作类型`, visible: true, slots: true, render: (row, index) => {
            return row.direction ? (<span>通过</span>) : (<span>驳回</span>)
          }},
        {key: 2, field: 'assigneeName', label: `操作人`, visible: true},
        {key: 3, field: 'endTime', label: `操作时间`, visible: true},
        {key: 4, field: 'comment', label: `操作意见`, visible: true},
        {key: 5, field: 'direction', label: `方向`, visible: true, slots: true, render: (row, index) => {
          return row.direction ? (<span>通过</span>) : (<span>驳回</span>)
          }}
      ],
      // 表格数据
      dataList: [],
      deptUserOptions: [],
      assigneeProps: {
        multiple: false, //是否多选
        value: 'id',
        emitPath: false,
      },
      curUser: null
    };
  },
  watch: {},
  created() {
    this.getDeptTreeDef();
  },
  mounted() {
    if (this.businessKey) {
      this.queryList()
    }
  },
  methods: {
    queryList() {
      this.loading = true
      const params = {}
      params[this.paramName] = this.businessKey
      this.getNodeInfo(params).then(res => {
        this.dataList = res.data
        this.loading = false
      })
    },
    openEditDialog(row) {
      this.editForm.id = row.id
      this.editForm.nodeName = row.nodeName
      this.editForm.taskId = row.taskId
      this.editForm.assignee = row.userId
      this.editForm.content = row.comment
      this.editForm.startTime = row.startTime
      this.editForm.endTime = row.endTime
      this.curUser = {
        label: row.assignee,
        value: row.userId
      }
      this.editDialog = true
    },
    assigneeChange(value) {
       this.$nextTick(() => {
        this.curUser = this.$refs.assigneeRef.getCheckedNodes()[0]
      })
      console.log(this.curUser);
    },
    saveEdit() {
      if (this.editForm.nodeName === '施工单下发') {
        const params = {
          id: this.businessKey,
          issuanceTime: this.editForm.endTime
        }
        updateIssuanceTime(params)
      }
      if (this.editForm.nodeName == '签发' && this.type == 'proj') {
        const params = {
          id: this.businessKey,
          issueDate: this.editForm.endTime
        }
        updateIssueDate(params)
      }
      if (this.editForm.nodeName == '签发' && this.type == 'theft') {
        const params = {
          id: this.businessKey,
          issueDate: this.editForm.endTime
        }
        updateTheftIssueDate(params)
      }
      const submitData = {
        ...this.editForm
      }
      submitData.assignee = `${this.curUser.label}@${this.curUser.value}`
      submitData.startTime = submitData.endTime
      editNodeInfo(submitData).then(()=> {
        this.queryList()
        this.editDialog = false
      })
    },
    getDeptTreeDef() {
      getTreeStruct({ types: 111, dataRule: false }).then((response) => {
        this.deptUserOptions = response.data;
      });
    },
  }
};
</script>
<style>
</style>
<style lang="scss" scoped>
@import "@/assets/styles/business.scss";
</style>
