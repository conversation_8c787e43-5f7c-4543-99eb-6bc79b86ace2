<template>
  <div class="sub-page">
    <!-- <div class="sub-page-header">
      <cockpit-header :title="title">
        <img src="@/assets/cockpit/back.png" @click="handleBack"
          :style="{ width: isBig ? '140px' : '60px', height: isBig ? '64px' : '28px' }" />
      </cockpit-header>
    </div> -->
    <section class="sub-page-main" :style="mainStyle">
      <div class="sub-page-l" v-if="isBig || (!isBig && jumpType != 2)">
        <cockpit-header :title="title" fixed="static" :isSub="true">
          <template #btn>
            <img src="@/assets/cockpit/back.png" @click="handleBack"
              :style="{ width: isBig ? '140px' : '60px', height: isBig ? '64px' : '28px' }" />
          </template>
        </cockpit-header>
        <div class="page-l">
          <component :is="singlePage ? componentR : componentL" :statisticsData="data"></component>
        </div>
      </div>
      <div class="sub-page-r" v-if="!singlePage" :style="{ overflow: componentR ? '' : 'hidden' }">
        <cockpit-header :title="jumpType == 2 ? titleName : subTitle1" fixed="static" v-if="componentR" :isSub="true">
          <static-info v-if="subTitle1 != '巡检查专题'"></static-info>
          <template #btn>
            <span></span>
          </template>
        </cockpit-header>
        <div class="page-r"
          :style="{ height: componentR ? 'calc(100% - 100px)' : 'calc(100%)', overflow: componentR ? '' : 'hidden' }">
          <component :is="componentR" v-if="componentR" :dTitle.sync="subTitle1"></component>
          <template v-else>
            <!-- <iframe src="/video/数字孪生.mp4" width="100%" height="100%" frameborder="0" @load="onLoad"
              @error="onError"></iframe> -->
            <!-- <object type="text/html" :data="thirdparty" width="100%" height="100%" @load="onLoad" @error="onError">
              <p>您的浏览器不支持此对象嵌入.</p>
            </object> -->
            <video width="100%" height="100%" id="video" ref="videoRef" autoplay loop preload muted>
              <source src="@/assets/cockpit/数字孪生.mp4" type="video/mp4">
              <p>您的浏览器不支持此视频.</p>
            </video>
          </template>
        </div>
      </div>
    </section>

    <TableInfo :show.sync="tableShow" :url="infoUrl" :method="method" :key="tableKey" :data-obj="dataObj"
      :healthMonitoring="healthMonitoring" :ifChart="false" :isBig="isBig" v-if="tableShow" />
  </div>
</template>

<script>
import { isBigScreen } from '../util/utils';
import CockpitHeader from './cockpitHeader.vue';
import StaticInfo from './common/static.vue';
import BridgeSpecial from './bridge/special.vue';
import BridgeDetail from './bridge/detail.vue';
import TunnelSpecial from './tunnel/special.vue';
import TunnelDetail from './tunnel/detail.vue';
import HealthIndex from './health/index.vue';
import PatrolCheck from './patrolCheck/index.vue';
import PatrolDetail from './patrolCheck/detail.vue';
import RouteDetail from './route/route.vue'; // 路网
import Maintenance from './maintenance/index.vue'; // 日常养护
import SpecialIndex from './maintenance/specialIndex.vue';
import TableInfo from "@/components/table/tableInfo.vue";

import { mapState, mapMutations } from 'vuex';

import { getMenuSub } from '@/api/oneMap/menuSub';
import { getListPage } from '@/api/oneMap/menuSubField';

export default {
  name: 'subpage',
  provide() {
    return {
      sub: this,
    };
  },
  components: {
    CockpitHeader,
    StaticInfo,
    BridgeSpecial,
    BridgeDetail,
    TunnelSpecial,
    TunnelDetail,
    HealthIndex,
    PatrolCheck,
    PatrolDetail,
    RouteDetail,
    Maintenance,
    SpecialIndex,
    TableInfo,
  },
  props: {
    title: {
      type: String,
      default: ''
    },
    subTitle: {
      type: String,
      default: ''
    },
    pageLId: {
      type: String,
      default: ''
    },
    pageRId: {
      type: String,
      default: ''
    },
    type: {
      type: [String, Number],
      default: ''
    },
    id: {
      type: [String, Number],
      default: ''
    },
    data: {
      type: [Object, Array],
      default: () => {
        return {}
      }
    },
    // 是否单独页面
    singlePage: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      isBig: isBigScreen(),
      height: window.innerHeight || window.screen.height || window.screen.availHeight || 1080,
      componentL: this.pageLId,
      componentR: this.pageRId,
      thirdparty: 'https://jgjc-szls.glyhgl.com/video.html',
      subTitle1: this.subTitle || "",
      titleName: this.subTitle || "",
      jumpType: this.type || 0,
      tableShow: false,
      infoUrl: "/oneMap/layerData/getDataInfo",
      method: "post",
      tableKey: new Date().getTime(),
      dataObj: {},
      healthMonitoring: {},
    }
  },
  computed: {
    ...mapState({
      legendList: (state) => state.map.legendList,
      menuShowType: (state) => state.map.menuShowType,
    }),
    mainStyle() {
      let style = {};
      if (this.isBig) {
        style = {
          height: '100vh',
          overflow: 'hidden',
        }
      } else {
        style = {
          // height: this.height * 2 + 'px',
          height: this.jumpType == 2 ? '100vh' : this.singlePage ? '100vh' : '200vh',
          overflowY: 'auto',
          flexDirection: 'column',
        }
      }
      return style;
    },
  },
  methods: {
    ...mapMutations({
      setTableHeader: "map/setTableHeader",
    }),
    handleBack() {
      if (this.$route?.query?.jumpType && this.$route?.query?.jumpType == '1') {
        this.$router.go(-1)
        return
      }
      this.$emit('back');
    },
    onLoad() {
      console.log('网页加载成功')
      this.$modal.closeLoading();
    },
    onError() {
      console.log('网页加载失败')
    },
    async getMapLayer(id) {
      return new Promise((resolve, reject) => {
        getMenuSub(id).then(res => {
          if (res.code == 200 && res.data) {
            resolve(res.data)
          } else {
            resolve(null)
          }
        }).catch(err => {
          reject(err)
        })
      });
    },
    // 获取显示 头部
    async getTableHeader(params) {
      return new Promise((resolve, reject) => {
        getListPage(params).then(res => {
          if (res.code == 200 && res.rows) {
            let data = res.rows.map(v => {
              v.name = v.columnNameZh || v.name
              v.col = v.columnName || v.col
              v.ifTableShow = 1;
              return v;
            })
            resolve(data)
          } else {
            resolve(null)
          }
        }).catch(err => {
          reject(err)
        });
      });
    },
  },
  mounted() {
    this.jumpType = this.$route?.query?.jumpType;
    this.subTitle1 = this.$route?.query?.qlmc || this.$route?.query?.bridge_name || this.subTitle
    if (!this.jumpType) {
      this.jumpType = this.type;
      if (this.jumpType == 2) {
        this.titleName = this.subTitle
      }
    }
    // if(this.$refs.videoRef) {
    //   this.$refs.videoRef.play()
    // }
    const myVideo = document.getElementById('video');
    if (myVideo) {
      this.$nextTick(() => {
        setTimeout(() => {
          myVideo.play()
        }, 1000)
      })
    }
    window.$Bus.$on('aMapClick', async (data) => {
      if (data.sys_dept_id) return
      this.subTitle1 = data.name
      if (data.path) return;
      this.dataObj = {
        dataId: data.id,
        id: data?.layerId || data?.id,
      }
      this.$modal.loading();
      let res = await this.getMapLayer(data?.layerId);
      let listQueryConfig = null;
      if (res) {
        listQueryConfig = res.listQueryConfig ? JSON.parse(res.listQueryConfig) : null;
      }
      if (listQueryConfig) {
        this.healthMonitoring = listQueryConfig.healthMonitoring;
      }
      let header = await this.getTableHeader({ pageNum: 1, pageSize: 100, menuSubId: data?.layerId })
      if (header) {
        this.setTableHeader({ header });
      }
      this.tableShow = true;
      setTimeout(() => {
        this.$modal.closeLoading();
      }, 500);
    })
    this.$forceUpdate();
  },
  unmounted() { },
  destroyed() {
    window.$Bus.$off('aMapClick')
  },
}
</script>

<style lang="scss" scoped>
@import "@/assets/styles/utils.scss";

.sub-page {
  width: calc(100% - 0px);
  min-height: 100vh;
  overflow-x: hidden;
  overflow-y: auto;

  .sub-page-header {
    width: 100%;
    height: vwpx(156px);
    z-index: 9;
  }

  .sub-page-main {
    width: 100%;
    display: flex;
    overflow: hidden;

    .sub-page-l {
      flex: 1;
      background-image: url('~@/assets/cockpit/cockpit-bg.png');
      background-repeat: no-repeat;
      background-size: 100% 100%;
      flex-shrink: 0;
      // background-image: radial-gradient(ellipse at center, rgba(12,87,177, 1) 0%,rgba(12,87,177, 0.8) 10%, rgba(9, 25, 45, 0.8) 20%, rgba(9, 25, 45, 1) 100%);

      // background-color: rgba(9, 25, 45, 1);
      .page-l {
        width: 100%;
      }
    }

    .sub-page-r {
      flex: 1;
      // background-image: url('~@/assets/cockpit/cockpit-bg.png');
      // background-repeat: no-repeat;
      // background-size: 100% 100%;
      background-color: rgba(9, 25, 45, 1);

      .page-r {
        width: 100%;
        // height: 100%;
      }
    }
  }
}
</style>
