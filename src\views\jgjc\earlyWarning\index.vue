<template>
    <div ref="container" class="screen-large_wrap" :class="{'full': isFullscreen}">
      <div class="home-header">
        <div class="current-time">{{ time }}</div>
        <div class="cockpit-title set-family">
          <span :title="title" class="title-n">{{ title }}</span>
        </div>
        <div class="btn">
          <img :src="isFullscreen ? quitIcon : fullIcon" ref="full" @click="fullClick"/>
          <img src="@/assets/map/quite.png" @click="handleQuit" v-if="isFull == 1"/>
        </div>
      </div>
      <div class="home-main" v-loading='loading'>
        <left :isBig="isBig" class="page-l" :style="isBig ? '-webkit-box-flex: 1' : ''" :structureList="structureList" @query="handleQuery" @box-click="handleClickBox" @fileterData='fileterData' ref='left'></left>
        <right @point-click='pointClick' :isBig="isBig" class="page-r" :style="isBig ? '-webkit-box-flex: 2' : ''" ref="right"></right>
      </div>
    </div>
</template>

<script>

import FullImg from "@/assets/map/full.png";
import QuitFullImg from "@/assets/map/quit-full.png";
import screenfull from "screenfull";
import Left from "@/views/jgjc/earlyWarning/left.vue";
import Right from "@/views/jgjc/earlyWarning/right.vue";
import {listStructure} from "@/api/jgjc/earlyWarning/deviceModel";
import {fromLonLat} from "ol/proj";
import { isBigScreen } from "@/views/cockpit/util/utils";
import { getFile } from '@/api/file'

export default {
  components: {
    Left, Right
  },
  data() {
    return {
      title: '自然灾害监测预警系统',
      time: '',
      timer: null,
      refreshTimer: null,
      userActive: true,
      idleTimer: null,
      fullIcon: FullImg,
      quitIcon: QuitFullImg,
      isFullscreen: false,
      structureList: [],
      checkData: {},
      isFull: 0,
      isBig: false,
      loading: false
    }
  },
  created() {
    this.isBig = isBigScreen()
    // 获取当前是星期几
    const date = new Date();
    const week = ['星期日', '星期一', '星期二', '星期三', '星期四', '星期五', '星期六'];
    const dayOfWeek = week[date.getDay()];
    // 时时获取当前时间 年月日时分秒
    this.timer = setInterval(() => {
      let date = new Date();
      const year = date.getFullYear();
      const month = date.getMonth() + 1;
      const day = date.getDate();

      const hours = date.getHours();
      let minutes = date.getMinutes();
      let seconds = date.getSeconds();
      // 判断分 秒是否大于10
      if (minutes < 10) {
        minutes = '0' + minutes;
      }
      if (seconds < 10) {
        seconds = '0' + seconds;
      }
      this.time = `${year}年${month}月${day}日 ${dayOfWeek} ${hours}:${minutes}:${seconds}`;
    }, 1000);
  },
  mounted() {
    this.handleQuery()
    this.initRefresh();
    this.setupActivityListeners();
    this.isFull = this.$route.query.isFull || 0
    if (this.isFull == 1) {
      this.$refs.container.classList.add('full-screen');
      this.$refs.right.initMap()
    }
  },
  methods: {
    fullClick() {
      this.isFullscreen = !this.isFullscreen;
      let fullDom = document.body;
      if (fullDom) screenfull.toggle(fullDom);
      if (!document.fullscreenElement) {
        screenfull.request();
      } else {
        screenfull.exit();
      }
    },
    // 退出、返回上个路由
    handleQuit() {
      // this.$router.back();
      this.$router.push({ path: "/home" });
      this.setSpread(0);
    },
    handleQuery() {
      this.loading = true
      listStructure({}).then(res => {
        this.structureList = res.data
        // 排序 status = 1 的放在最前面，其次是planStatus = 1 的，最后是status = 0 的
        this.structureList.sort((a, b) => {
          if (a.status == 1) {
            return -1;
          } else if (b.status == 1) {
            return 1;
          } else if (a.planStatus == 1) {
            return -1;
          } else if (b.planStatus == 1) {
            return 1;
          }
          return 0;
        });
        this.$refs.right.clearAllPoints()
        this.structureList.forEach(item => {
          item.isHasOffFlashDev = '0'
          let type = 1
          if (item.planStatus == 1) {
            type = 0
          } else if (item.status == 1) {
            type = 2
          }
          if (this.checkData.id == item.id) {
            this.$refs.right.addPoint(type, fromLonLat([item.lon, item.lat]), item.name, item.id)
          } else {
            this.$refs.right.addPoint(type, fromLonLat([item.lon, item.lat]), null, item.id)
          }
          if (item.imgPath) {
            getFile({ ownerId: item.imgPath }).then(async res => {
              if (res.code == 200) {
                this.$set(item, 'imgUrl', res.data.url)
                this.$set(item, 'thumbUrl', res.data.thumbUrl)

              }
            }).finally(() => {
            })
          }
        })
      }).finally(() => {
        this.loading = false
      })
    },
    initRefresh() {
      this.refreshTimer = setInterval(() => {
        if (!this.userActive) {
          this.handleQuery();
        }
      }, 15000);
    },
    setupActivityListeners() {
      const events = ['mousemove', 'keydown', 'click'];
      const resetIdleTimer = () => {
        this.userActive = true
        clearTimeout(this.idleTimer);
        // 设置15秒无操作后才允许刷新（与刷新周期一致）
        this.idleTimer = setTimeout(() => {
          this.userActive = false;
        }, 15000);
      };

      events.forEach(event => {
        window.addEventListener(event, resetIdleTimer);
      });
      resetIdleTimer(); // 初始化
    },
    handleClickBox(e) {
      this.checkData = e
      let type = 1
      if (e.planStatus == 1) {
        type = 0
      } else if (e.status == 1) {
        type = 2
      }
      this.$refs.right.clearAllPoints()
      this.structureList.forEach(item => {
        let type = 1
        if (item.planStatus == 1) {
          type = 0
        } else if (item.status == 1) {
          type = 2
        }
        if (this.checkData.id == item.id) {
          this.$refs.right.addPoint(2, fromLonLat([item.lon, item.lat]), item.name, item.id)
        } else {
          this.$refs.right.addPoint(type, fromLonLat([item.lon, item.lat]), null, item.id)
        }
      })
      this.$refs.right.focusOnLocation([e.lon, e.lat])
    },
    pointClick(id) {
      console.log(id)
      const box = this.structureList.find(item => item.id == id)
      this.$refs.left.onBoxClick(box)
    },
    fileterData(list) {
      if (list.length > 0) {
        this.$refs.right.clearAllPoints()
        list.forEach(item => {
          let type = 1
          if (item.planStatus == 1) {
            type = 0
          } else if (item.status == 1) {
            type = 2
          }
          this.$refs.right.addPoint(type, fromLonLat([item.lon, item.lat]), null, item.id)
        })
        // this.$refs.right.focusOnLocation([102.0007599, 23.7085797], 8)
      }
    },
  },
  beforeDestroy() {
    screenfull.off("change", this.change);
    if (this.timer) {
      clearInterval(this.timer)
      this.timer = null;
    }
    if (this.refreshTimer) {
      clearInterval(this.refreshTimer)
      this.refreshTimer = null;
    }
    if (this.idleTimer) {
      clearTimeout(this.idleTimer);
    }
  },
}
</script>

<style lang="scss" scoped>
@import "@/assets/styles/utils.scss";
.full-screen {
  position: fixed;
  width: 100vw !important;
  height: 100vh !important;
  left: 0%;
  top: 0%;
  z-index: 9999;
}
.screen-large_wrap {
  /* 铺满整个屏幕 */
  width: 100%;
  height: 100%;
  /* 背景覆盖整个屏幕（包括留白区域） */
  background-image: url('~@/assets/cockpit/cockpit-bg.png');
  background-repeat: no-repeat;
  background-size: cover; /* 让背景完全覆盖，可能被裁剪 */
  background-position: center center;
  overflow-y: auto;
  /* 使用 Flex 布局让内容居中
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  align-items: center;
  overflow: hidden;
  */
  .home-header {
    background-image: url('~@/assets/cockpit/header-bg.png');
    background-repeat: no-repeat;
    background-size: 100% 100%;
    width: 100%;
    height: vwpx(156px);
    //z-index: 99;
    position: relative;

    .current-time {
      position: absolute;
      left: vwpx(40px);
      font-size: vwpx(40px);
      top: 35%;
      transform: translateY(-50%);
      color: #fff;
    }

    .set-family {
      font-family: YouSheBiaoTiHei;
      background: linear-gradient(180deg, #FFFFFF 0%, #FFFFFF 60%, #20A9FF 100%);
      background-clip: text;
      -webkit-background-clip: text;
      color: transparent;
    }

    .cockpit-title {
      width: 100%;
      font-family: YouSheBiaoTiHei;
      font-weight: 400;
      min-width: vwpx(1100px);
      letter-spacing: vwpx(16px);
      text-align: center;
      line-height: vwpx(160px);
      color: #fff;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: vwpx(68px);

      .title-n {
        display: block;
        text-align: left;
        color: #fff;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }

    .btn {
      position: absolute;
      right: vwpx(20px);
      top: 35%;
      transform: translateY(-50%);
      cursor: pointer;
      display: flex;
      align-items: center;
    }
  }

  .home-main {
    /*
    aspect-ratio: 16/9;
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center;
    margin: 0 auto;
    */
    width: 100%;
    height: 92.5%;
    display: -webkit-box;
    .page-l {
      flex: 1;
      height: 100%;
      padding: vwpx(40px);
      width: 50%;
    }

    .page-r {
      flex: 1;
      height: 100%;
      padding: vwpx(40px);
      width: 50%;
    }
  }

}

.full {
  position: fixed !important;
  z-index: 2000;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  width: 100%;
  height: 100%;
}
</style>
