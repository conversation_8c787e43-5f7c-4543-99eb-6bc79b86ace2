// 行政识别数据

let baseInfoData = [
  {
    label: '管理处',
    prop: 'managementMaintenanceId',
    placeholder: '请选择按管理处',
    type: 'select',
    rules: [{ required: true, message: '请选择管理处', trigger: 'blur' }],
    deptType:201
  },
  {
    label: '管养分处',
    prop: 'managementMaintenanceBranchId',
    placeholder: '请选择管养分处',
    type: 'select',
    rules: [{ required: true, message: '请选择管养分处', trigger: 'blur' }],
    deptType:202
  },
  {
    label: '养护路段',
    prop: 'maintenanceSectionId',
    placeholder: '请选择养护路段',
    type: 'select',
    rules: [{ required: true, message: '请选择养护路段', trigger: 'blur' }],
    options: [],
    disabled: true,
    api: 'listMaintenanceSectionAll',
    optionLabel: 'maintenanceSectionName',
    optionValue: 'maintenanceSectionId'
  },
  {
    label: '路线编码',
    prop: 'routeCode',
    placeholder: '请选择路线编码',
    type: 'select',
    rules: [{ required: true, message: '请选择路线编码', trigger: 'blur' }],
    options: [],
    api: 'listAllRoute',
    optionLabel: 'routeCode',
    optionValue: 'routeCode',
  },
  {
    label: '移交管理单位',
    prop: 'transferManagementUnit',
    placeholder: '请输入移交管理单位',

    type: 'input',
  },

  {
    label: '路段类型',
    prop: 'sectionType',
    placeholder: '请选择路段类型',
    type: 'sectionSelect',

    disabled: true,
  },
  {
    label: '路基编码',
    prop: 'roadbedCode',
    placeholder: '请输入路基编码',
    type: 'input',
  },


  {
    label: '路线等级',
    prop: 'routeLevel',
    placeholder: '请选择路线等级',
    type: 'select',
    dict: 'sys_route_grade'
  },
  // {
  //   label: '路段技术等级',
  //   prop: 'sectionTechLevelType',
  //   placeholder: '请选择路段技术等级',
  //   type: 'select',
  //   dict: 'roadbed_section_tech_level',
  // },
  {
    label: '面层类型',
    prop: 'pavementType',
    placeholder: '请选择路面类型',
    type: 'select',

    dict: 'sys_surface_type',
  },
  {
    label: '方向',
    prop: 'direction',
    placeholder: '请选择方向',
    type: 'select',


    dict: 'sys_route_direction',
  },
  {
    label: '左右',
    prop: 'leftOrRight',
    placeholder: '请选择左右',
    type: 'select',


    dict: 'left_right',
  },
  {
    label: '位置',
    prop: 'lane',
    placeholder: '请选择位置',
    type: 'select',

    dict: 'lane',
  },
  {
    label: '车道数',
    prop: 'laneNumber',
    placeholder: '请输入车道数',
    type: 'inputNumber',

  },



  // {
  //   label: '匝道名称',
  //   prop: 'belongRampName',
  //   placeholder: '请输入匝道名称',
  //   type: 'input',
  // },
  // {
  //   label: '连接线名称',
  //   prop: 'belongConnectingLineName',
  //   placeholder: '请输入连接线名称',
  //   type: 'input',
  // },
  {
    label: '起点桩号',
    prop: 'startStake',
    placeholder: '请输入起点桩号',
    type: 'inputNumber',
    precision: 3,
    rules: [{ required: true, message: '请输入起点桩号', trigger: 'blur' }],
  },
  {
    label: '经纬度',
    prop: 'longitude',
    placeholder: '请输入起点经度',
    type: 'startcoordinate',
    precision: 3,
  },

  {
    label: '终点桩号',
    prop: 'endStake',
    placeholder: '请输入终点桩号',
    type: 'inputNumber',
    precision: 3,
    rules: [{ required: true, message: '请输入终点桩号', trigger: 'blur' }],
  },

  {
    label: '路基位置',
    prop: 'position',
    placeholder: '请输入路基位置',
    type: 'input',
  },
  {
    label: '施工里程桩号',
    prop: 'constructionStake',
    placeholder: '请输入施工里程桩号',
    type: 'inputNumber',
    precision: 3,
  },
  {
    label: '施工里程起点桩号',
    prop: 'constructionStartStake',
    placeholder: '请输入施工里程起点桩号',
    type: 'inputNumber',
    precision: 3,
  },
  {
    label: '施工里程终点桩号',
    prop: 'constructionEndStake',
    placeholder: '请输入施工里程终点桩号',
    type: 'inputNumber',
    precision: 3,
  },
  {
    label: '施工里程(km)',
    prop: 'constructionMileage',
    placeholder: '请输入施工里程',
    type: 'inputNumber',

  },
  {
    label: '统一里程桩号',
    prop: 'unifiedMileageStake',
    placeholder: '请输入统一里程桩号',
    type: 'inputNumber',
    precision: 3,
  },
  {
    label: '统一里程起点桩号',
    prop: 'unifiedMileageStartStake',
    placeholder: '请输入统一里程起点桩号',
    type: 'inputNumber',
    precision: 3,
  },
  {
    label: '统一里程终点桩号',
    prop: 'unifiedMileageEndStake',
    placeholder: '请输入统一里程终点桩号',
    type: 'inputNumber',
    precision: 3,
  },
  {
    label: '管养里程(km)',
    prop: 'maintenanceMileage',
    placeholder: '请输入管养里程',
    type: 'inputNumber',
  },
  {
    label: '国高网桩号',
    prop: 'nationalNetworkStake',
    placeholder: '请输入国高网桩号',
    type: 'inputNumber',
    precision: 3,
  },
  {
    label: '国高网起点桩号',
    prop: 'nationalNetworkStartStake',
    placeholder: '请输入国高网起点桩号',
    type: 'inputNumber',
    precision: 3,
  },
  {
    label: '国高网终点桩号',
    prop: 'nationalNetworkEndStake',
    placeholder: '请输入国高网终点桩号',
    type: 'inputNumber',
    precision: 3,
  },
  {
    label: '国高网里程(km)',
    prop: 'nationalNetworkMileage',
    placeholder: '请输入国高网里程',
    type: 'inputNumber',
  },
  {
    label: '设计宽度左幅(m)',
    prop: 'designWidthLeftPanel',
    placeholder: '请输入设计宽度左幅',
    type: 'inputNumber',
    precision: 2,
  },
  {
    label: '设计宽度右幅(m)',
    prop: 'designWidthRightPanel',
    placeholder: '请输入设计宽度右幅',
    type: 'inputNumber',
    precision: 2,
  },
  {
    label: '设计宽度整体式(m)',
    prop: 'designWidthMonolithic',
    placeholder: '请输入设计宽度整体式',
    type: 'inputNumber',
    precision: 2,
  },
  {
    label: '实际宽度左幅(m)',
    prop: 'actualWidthLeftPanel',
    placeholder: '请输入实际宽度左幅',
    type: 'inputNumber',
    precision: 2,
  },
  {
    label: '实际宽度右幅(m)',
    prop: 'actualWidthRightPanel',
    placeholder: '请输入实际宽度右幅',
    type: 'inputNumber',
    precision: 2,
  },
  {
    label: '实际宽度整体式(m)',
    prop: 'actualWidthMonolithic',
    placeholder: '请输入实际宽度整体式',
    type: 'inputNumber',
    precision: 2,
  },
  {
    label: '坡长(m)',
    prop: 'slopeLength',
    placeholder: '请输入坡长',
    type: 'inputNumber',
    precision: 2,
  },
  {
    label: '高度',
    prop: 'height',
    placeholder: '请输入高度',
    type: 'inputNumber',
    precision: 2,
  },
  {
    label: '路基类型',
    prop: 'roadbedType',
    placeholder: '请输入路基类型',
    type: 'input',
  },
  {
    label: '平台数量',
    prop: 'numPlatform',
    placeholder: '请输入平台数量',
    type: 'inputNumber',
  },
  {
    label: '检查梯',
    prop: 'ladder',
    placeholder: '请输入检查梯',
    type: 'input',
  },
  {
    label: '防护形式',
    prop: 'protectType',
    placeholder: '请输入防护形式',
    type: 'input',
  },
  {
    label: '土质',
    prop: 'soil',
    placeholder: '请输入土质',
    type: 'input',
  },
  {
    label: '坡脚长度',
    prop: 'footLength',
    placeholder: '请输入坡脚长度',
    type: 'inputNumber',
    precision: 2,
  },
  {
    label: '落台宽度',
    prop: 'fallingWidth',
    placeholder: '请输入落台宽度',
    type: 'inputNumber',
    precision: 2,
  },
  {
    label: '建成时间',
    prop: 'buildDate',
    placeholder: '请选择建成时间',
    type: 'date',
  },
  {
    label: '备注',
    prop: 'remark',
    placeholder: '请输入备注',
    type: 'input',
  },
  {
    label: '图片',
    prop: 'samplePictureId',
    placeholder: '请选择文件',
    type: 'uploadImg',
  },
];







export default {
  baseInfoData,

}
