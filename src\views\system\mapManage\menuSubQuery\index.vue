<template>
  <PageContainer>
    <template slot="search">
      <el-row :gutter="20">
        <el-col :span="3" :offset="0">
          <el-input v-model="queryParams.columnNameZh" placeholder="请输入中文名称" clearable size="mini"
            @keyup.enter.native="handleQuery" />
        </el-col>
        <el-col :span="3" :offset="0">
          <el-input v-model="queryParams.columnName" placeholder="请输入字段名称" clearable size="mini"
            @keyup.enter.native="handleQuery" />
        </el-col>
        <!-- <el-col :span="3" :offset="0">
          <el-input v-model="queryParams.groupName" placeholder="请输入分组名称" clearable size="mini"
            @keyup.enter.native="handleQuery" />
        </el-col> -->
        <el-col :span="6" :offset="0">
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">
            搜索
          </el-button>
          <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">
            重置
          </el-button>
        </el-col>
      </el-row>
    </template>
    <template slot="header">
      <el-row :gutter="20">
        <el-col :span="1.5" :offset="0" class="mb8">
          <el-button v-hasPermi="['baseData:culvert:add']" type="primary" size="mini" @click="handleAdd">
            新增
          </el-button>
        </el-col>
        <el-col :span="1.5" :offset="0" class="mb8">
          <el-button v-hasPermi="['baseData:culvert:edit']" :disabled="!(tableSelects.length === 1)" type="primary"
            size="mini" @click="handleUpdate">
            编辑
          </el-button>
        </el-col>
        <el-col :span="1.5" :offset="0" class="mb8">
          <el-button v-hasPermi="['baseData:culvert:delete']" :disabled="!(tableSelects.length > 0)" type="primary"
            size="mini" @click="handleDelete">
            删除
          </el-button>
        </el-col>
        <el-col :span="1.5" :offset="0" class="mb8">
          <el-button v-hasPermi="['baseData:culvert:getInfoById']" :disabled="!(tableSelects.length === 1)" type="primary"
            size="mini" @click="handleDetail">
            查看
          </el-button>
        </el-col>
      </el-row>
    </template>
    <template slot="body">
      <el-table v-loading="loading" :data="menuSubQueryList" @selection-change="handleSelectionChange"
        @row-click="handleRowClick" border height="100%" :row-key="(row) => row.id" ref="tableRef">
        <el-table-column type="selection" width="55" align="center" fixed="left" :reserve-selection="true" />
        <el-table-column label="名称" min-width="100" align="center" prop="columnNameZh" show-overflow-tooltip/>
        <el-table-column label="字段名称" align="center" prop="columnName" />
        <el-table-column label="数据类型" align="center" prop="columnType">
          <template slot-scope="{row}">
            <dict-tag :options="dict.type.data_type" :value="row.columnType"/>
          </template>
        </el-table-column>
        <el-table-column label="输入类型" align="center" prop="inputType">
          <template slot-scope="{row}">
            <dict-tag :options="dict.type.input_type" :value="row.inputType"/>
          </template>
        </el-table-column>
        <!-- <el-table-column label="选择项" align="center" prop="optionName">
          <template slot-scope="{row}">
            {{ row.optionName }}
          </template>
        </el-table-column> -->
        <el-table-column label="排序" align="center" prop="showIndex" />
        <el-table-column label="分组名称" align="center" prop="groupName" />
      </el-table>

      <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize" @pagination="getList" />
    </template>

    <!-- 新增编辑查看 -->
    <Dialog :title="title" :show.sync="open">
      <AddOrEdit :form="form" @close="handleClose" @refresh="getList" :readonly="readonly" />
    </Dialog>
  </PageContainer>
</template>
 
<script>
import { getListPage, getMenuSubQuery, delMenuSubQuery } from "@/api/oneMap/menuSubQuery";
import AddOrEdit from "./components/addOrEdit.vue";
import Dialog from "@/components/Dialog/index.vue";

export default {
  name: "MenuSubQuery",
  dicts: ['data_type', 'input_type'],
  components: {
    AddOrEdit,
    Dialog,
  },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 查询项设置名存储的子表格数据
      menuSubQueryList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        menuSubId: null,
        columnNameZh: null,
        columnName: null,
        columnType: null,
        inputType: null,
        dictType: null,
        optionName: null,
        showIndex: null,
        groupName: null
      },
      // 表单参数
      form: {},
      tableSelects: [],
      readonly: false
    };
  },
  created() {
    this.queryParams.menuSubId = this.$route.query.id
    this.getList();
  },
  methods: {
    /** 查询查询项设置名存储的子列表 */
    getList() {
      this.loading = true;
      getListPage(this.queryParams).then(response => {
        this.menuSubQueryList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        menuSubId: null,
        columnNameZh: null,
        columnName: null,
        columnType: null,
        inputType: null,
        dictType: null,
        optionName: null,
        showIndex: null,
        groupName: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.tableSelects = selection || [];
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.form.menuSubId = this.$route.query.id
      this.open = true;
      this.title = "添加查询项设置名存储";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = this.tableSelects[0].id
      getMenuSubQuery(id).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改查询项设置名存储";
      });
    },
    handleClose(e) {
      this.open = e;
      this.readonly = false;
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = this.tableSelects.map(v=>v.id)
      this.$modal.confirm('是否确认删除查询项设置名存储的数据项？').then(function () {
        return delMenuSubQuery(ids);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => { });
    },
    handleDetail() {
      this.reset();
      this.readonly = true;
      const id = this.tableSelects[0].id
      getMenuSubQuery(id).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "查看查询项设置名存储的子";
      });
    },
    handleRowClick(row) {
      let arr = this.tableSelects.filter((v) => v.id == row.id);
      // 点击行 选中复选框
      if (arr && arr.length > 0) {
        this.$refs.tableRef.toggleRowSelection(row, false);
        this.tableSelects = this.tableSelects.filter((v) => v.id != row.id);
      } else {
        this.tableSelects = [...this.tableSelects, ...[row]];
        this.$refs.tableRef.toggleRowSelection(row, true);
      }
    },
  }
};
</script>
 
<style lang="scss" scoped></style>