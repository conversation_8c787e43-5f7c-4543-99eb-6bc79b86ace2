<template>
  <div class="culvert-statistics">
    <el-button
      v-hasPermi="['baseData:culvertStatistics:export']"
      style="margin-bottom: 10px;"
      type="primary"
      @click="exportList"
    >导出报表</el-button>
    <el-table
      v-adjust-table
      ref="table"
      v-loading="loading"
      height="calc(100% - 90px)"
      border
      :data="tableData"
      :header-cell-style="{'height': '36px'}"
    >
      <el-table-column
        label="序号"
        type="index"
        width="120"
        align="center"
        >
          <template v-slot="scope">
            {{
              scope.$index +
              (queryParams.pageNum - 1) * queryParams.pageSize +
              1
            }}
          </template>
      </el-table-column>
      <el-table-column
        label="涵洞类型"
        align="center"
        prop="culvertType"
        min-width="120"
        show-overflow-tooltip
      >
        <template slot-scope="scope">
          <span v-if="scope.row && scope.row.culvertType">
            <DictTag
              :value="scope.row.culvertType"
              :options="dict.type.sys_culvert_type"
            />
          </span>
        </template>
      </el-table-column>
      <el-table-column
        label="总数量(个)"
        align="center"
        prop="statisticsTotal"
        min-width="120"
      >
        <template slot-scope="{ row }">
          <el-link
            v-hasPermi="['baseData:culvert:query']"
            type="primary"
            :underline="false"
            @click="handleTable(row, [row.culvertType], row.statisticsTotal)"
          > {{row.statisticsTotal}} </el-link>
        </template>
      </el-table-column>
    </el-table>
    <pagination
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      :pageSizes="[10, 20, 30, 50, 100, 1000]"
      @pagination="getList"
    />
    <DetailTable
      v-if="showTable"
      :showTable="showTable"
      :row="row"
      :params="params"
      @close="closeTable"
    />
  </div>
</template>

<script>
import CascadeSelection from '@/components/CascadeSelection/index.vue'
import { getStatistics } from '@/api/baseData/culvert/statistics/index'
import DetailTable from './components/DetailTable.vue'

export default {
  name: 'Statistics',
  props: {},
  dicts: ['sys_culvert_type'],
  components: { CascadeSelection, DetailTable },
  data() {
    return {
      queryParams: {
        pageNum: 1,
        pageSize: 20
      },
      tableData: [],
      loading: false,
      showTable: false,
      row: {},
      params: {},
      total: 0
    }
  },
  created() {
    this.getList()
  },
  methods: {
    getList() {
      this.loading = true
      this.tableData = []
      this.cellList = []
      this.count = null
      getStatistics(this.queryParams)
        .then(res => {
          if (res) {
            this.total = res.total
            this.tableData = res.rows
          }
        })
        .finally(() => {
          this.loading = false
        })
    },
    handleTable(row, list, Num) {
      if (Num == 0) return
      this.showTable = true
      this.row = row
      let arr = ['1', '2', '3', '4', '5', '6']
      this.params = { culvertTypes: list[0] == '合计' ? arr : list }
    },
    closeTable() {
      this.showTable = false
      this.row = {}
      this.params = {}
    },
    exportList() {
      this.$modal
        .confirm('导出所有表格数据，是否继续？')
        .then(() => {
          this.download(
            '/baseData/culvert/statistics/export',
            this.queryParams,
            `桥梁统计_${new Date().getTime()}.xlsx`,
            {
              headers: { 'Content-Type': 'application/json;' },
              parameterType: 'body'
            }
          )
        })
        .catch(() => {})
    },
  },
  computed: {},
  watch: {}
}
</script>

<style lang="scss" scoped>
.culvert-statistics {
  width: calc(100% - 20px);
  height: calc(100% - 20px);
  padding: 10px;
  background: #ffffff;
  box-shadow: 0px 0px 10px 0px rgba(0, 0, 0, 0.2);
  border-radius: 10px;
  margin: 10px;
}
</style>