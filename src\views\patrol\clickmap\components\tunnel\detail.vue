<template>
  <div class="bridge-detail">
    <section class="flex-1 ml-3">
      <CockpitCard title="隧道基础信息" :w="isBig ? '12vw' : '24vw'" h="calc(57vh)" :class="isBig ? 'mb-2' : 'mb-3'"
        :isDtl="false">
        <div class="bridge-info">
          <div v-for="(item, index) in infoList" :key="index" class="info-list">
            <span class="name">{{ item.name }}</span>
            <span class="value">{{ item.value }}</span>
          </div>
        </div>
      </CockpitCard>
      <div class="flex">
        <CockpitCard title="隧道出口照片" :w="isBig ? 'calc(6vw - 12px)' : 'calc(12vw - 6px)'" :isDtl="false"
          :h="isBig ? 'calc(25vh + 20px)' : 'calc(25vh)'" :class="isBig ? 'mb-2' : 'mb-3'">
          <!-- <img src="@/assets/cockpit/elevation.png" class="img" /> -->
          <div class="img-empty">暂无照片</div>
        </CockpitCard>
        <CockpitCard title="隧道进口照片" :w="isBig ? 'calc(6vw - 12px)' : 'calc(12vw - 6px)'" :isDtl="false"
          :h="isBig ? 'calc(25vh + 20px)' : 'calc(25vh)'" :class="isBig ? 'mb-2' : 'mb-3'" class="ml-3">
          <!-- <img src="@/assets/cockpit/info.png" class="img" /> -->
          <div class="img-empty">暂无照片</div>
        </CockpitCard>
      </div>
    </section>

    <section class="flex-2">
      <div class="flex mb-3">
        <CockpitCard title="巡查情况" :w="isBig ? '12vw' : '24vw'" :h="isBig ? 'calc(41vh + 3px)' : '41vh'" :isDtl="false">
          <Tables :columns="pColumns" :data="pData"></Tables>
        </CockpitCard>
        <CockpitCard title="隧道技术状况评定" :w="isBig ? '12vw' : '24vw'" :h="isBig ? 'calc(41vh + 3px)' : '41vh'" class="ml-3"
          :isDtl="false">
          <Echarts :option="pdOption" v-if="pdOption" height="40vh" key="pdKey" />
        </CockpitCard>
      </div>

      <div class="inspect-situation">
        <div class="header">
          <span>隧道经常检查情况</span>
        </div>
        <div class="main">
          <div class="tunnel-civil mr-2">
            <span class="upper-left"></span>
            <span class="upper-right"></span>
            <span class="lower-left"></span>
            <span class="lower-right"></span>

            <div class="civil-title">
              <span></span>
              <span>隧道土建</span>
            </div>

            <div class="inspect-info">
              <div v-for="(item, index) in list1" :key="'in' + index" class="list">
                <span class="name">{{ item.name }}</span>
                <div :class="item.value == 1 ? 'normal' : 'error'"><span></span> {{ item.value == 1 ? '未见异常' : '出现异常' }}
                </div>
              </div>
            </div>
          </div>
          <div class="tunnel-civil ml-2">
            <span class="upper-left"></span>
            <span class="upper-right"></span>
            <span class="lower-left"></span>
            <span class="lower-right"></span>

            <div class="civil-title">
              <span></span>
              <span>隧道机电</span>
            </div>

            <div class="inspect-info">
              <div v-for="(item, index) in list2" :key="'in' + index" class="list">
                <span class="name">{{ item.name }}</span>
                <div :class="item.value == 1 ? 'normal' : 'error'"><span></span> {{ item.value == 1 ? '未见异常' : '出现异常' }}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <section class="flex-1">
      <CockpitCard title="最新技术评定结果" :w="isBig ? '12vw' : '24vw'" :h="isBig ? 'calc(87vh + 12px)' : 'calc(87vh + 3px)'"
        :isDtl="false">
        <div class="pd-level">
          <div class="level">
            <span>总体评定等级</span>
            <span>3 <small>级</small></span>
          </div>
          <div class="divider"></div>
          <div class="level">
            <span>养护等级</span>
            <span>2 <small>级</small></span>
          </div>
        </div>

        <div class="pd-result">
          <div class="result-list" v-for="(item, index) in rList" :key="'r' + index">
            <div class="list-index" :class="'list-index' + (index + 1)">{{ index + 1 }}</div>
            <span class="value">{{ item.name }}</span>
            <span class="name">{{ item.type }}</span>
            <span class="value">{{ item.value }}</span>
          </div>
        </div>
        <div class="tab-list">
          <div class="tab-item" :class="tabIndex == index ? 'tab-act' : ''" v-for="(item, index) in tabList"
            :key="'tab' + index" @click="onTabClick(index)">
            {{ item.name }}
          </div>
        </div>
        <div class="pr-table">
          <Tables :columns="prColumns" :data="prData"></Tables>
        </div>
        <div class="mt-advice">
          <div class="advice-name">
            <img src="@/assets/cockpit/mt-advice.png" />
            <span>养护意见</span>
          </div>
          <div class="advice-cont">
            隧道整体状况较为良好，应进行“日常养护、预防养护”针对较为严重的病害现象建议立即进行处置。
          </div>
        </div>
      </CockpitCard>
    </section>
  </div>
</template>

<script>
import * as echarts from 'echarts'
import { isBigScreen } from '../../util/utils';
import CockpitCard from '../cockpitCard.vue';
import Tables from '../tables.vue';
import Echarts from '../echarts/echarts.vue';

export default {
  components: {
    CockpitCard,
    Tables,
    Echarts,
  },
  data() {
    return {
      isBig: isBigScreen(),
      info: {},
      infoList: [
        {
          name: '管养单位：',
          value: '大理管理处',
        },
        {
          name: '路线编码：',
          value: 'G5612',
        },
        {
          name: '路段名称：',
          value: '大理-临沧高速公路',
        },
        {
          name: '隧道名称：',
          value: '白塔村隧道左幅',
        },
        {
          name: '隧道编码：',
          value: 'H5612532901U0030',
        },
        {
          name: '统一里程桩号：',
          value: 'K4+510',
        },
        {
          name: '隧道长度：',
          value: '5400.50m',
        },
        {
          name: '衬砌类型：',
          value: '整体式衬砌',
        },
        {
          name: '洞门形式：',
          value: '环框式洞门',
        },
        {
          name: '机电设施类别：',
          value: '供配电设施，照明设施，消防设施，监控与通信设施',
        },
        {
          name: '隧道养护工程师：',
          value: '余正利',
        },
        {
          name: '下一次评定日期：',
          value: '2024年',
        },
        {
          name: '养护工程处置次数：',
          value: '1次',
        },
        {
          name: '日常养护次数：',
          value: '1次',
        },
        {
          name: '本月日常巡查次数：',
          value: '4次',
        },
        {
          name: '本月经常检查次数：',
          value: '4次',
        },
      ],
      pColumns: [
        {
          label: "巡查时间",
          prop: "time",
        },
        {
          label: "巡查人",
          prop: "name",
        },
        {
          label: "天气状况",
          prop: "weather",
        },
        {
          label: "是否异常",
          prop: "abnormal",
        },
      ],
      pData: [

        {
          time: "2024-08-29",
          name: "余伟",
          weather: "晴",
          abnormal: "否",
        },
        {
          time: "2024-08-29",
          name: "余伟",
          weather: "晴",
          abnormal: "否",
        },
        {
          time: "2024-08-29",
          name: "余伟",
          weather: "晴",
          abnormal: "否",
        },
        {
          time: "2024-08-29",
          name: "余伟",
          weather: "晴",
          abnormal: "否",
        },
        {
          time: "2024-08-29",
          name: "余伟",
          weather: "晴",
          abnormal: "否",
        },
        {
          time: "2024-08-29",
          name: "余伟",
          weather: "晴",
          abnormal: "否",
        },
        {
          time: "2024-08-29",
          name: "余伟",
          weather: "晴",
          abnormal: "否",
        },
        {
          time: "2024-08-29",
          name: "余伟",
          weather: "晴",
          abnormal: "否",
        },
        {
          time: "2024-08-29",
          name: "余伟",
          weather: "晴",
          abnormal: "否",
        },
        {
          time: "2024-08-29",
          name: "余伟",
          weather: "晴",
          abnormal: "否",
        },
        {
          time: "2024-08-29",
          name: "余伟",
          weather: "晴",
          abnormal: "否",
        },

        {
          time: "2024-08-29",
          name: "余伟",
          weather: "晴",
          abnormal: "否",
        },
        {
          time: "2024-08-29",
          name: "余伟",
          weather: "晴",
          abnormal: "否",
        },
      ],
      pdOption: null,
      list1: [
        {
          name: '洞口',
          value: 1,
        },
        {
          name: '洞门',
          value: 1,
        },
        {
          name: '衬砌',
          value: 0,
        },
        {
          name: '路面',
          value: 1,
        },
        {
          name: '检修道',
          value: 1,
        },
        {
          name: '排水设施',
          value: 0,
        },
        {
          name: '吊顶及各种预埋件',
          value: 1,
        },
        {
          name: '标志、标线、轮廓标',
          value: 1,
        },
        {
          name: '车行横道',
          value: 1,
        },
      ],
      list2: [
        {
          name: '供配电设施',
          value: 1,
        },
        {
          name: '通风设施',
          value: 1,
        },
        {
          name: '照明设施',
          value: 0,
        },
        {
          name: '消防设施',
          value: 1,
        },
        {
          name: '监控与通讯设施',
          value: 1,
        },
        {
          name: '标志',
          value: 0,
        },
        {
          name: '标线',
          value: 1,
        },
        {
          name: '其他特有设施',
          value: 1,
        },
        {
          name: '车行横道',
          value: 1,
        },
      ],
      prColumns: [{
        label: "结构名称",
        prop: "name",
      }, {
        label: "状况值",
        prop: "score",
      }],
      prData: [
        {
          name: '洞口',
          score: '1',
        },
        {
          name: '洞门',
          score: '2',
        },
        {
          name: '衬砌',
          score: '1',
        },
        {
          name: '路面',
          score: '2',
        },
        {
          name: '检修道',
          score: '2',
        },
        {
          name: '排水设施',
          score: '0',
        },
        {
          name: '吊顶及各种预埋件',
          score: '0',
        },
        {
          name: '标志、标线、轮廓标',
          score: '0',
        },
        {
          name: '车行横道',
          score: '2',
        },
      ],
      rList: [
        {
          name: '土建评定结果',
          type: '技术状况类别',
          value: '1类',
        },
        {
          name: '机电设施评定结果',
          type: '技术状况类别',
          value: '2类',
        },
        {
          name: '其他工程设施评定结果',
          type: '技术状况类别',
          value: '3类',
        },
      ],
      tabIndex: 0,
      tabList: [
        {
          name: '隧道土建',
        },
        {
          name: '隧道机电',
        },
        {
          name: '隧道其他工程设施',
        },
      ],
    }
  },
  mounted() {
    this.initPdEcharts();
  },
  methods: {
    initPdEcharts() {
      const data = [40, 66, 57, 48, 68, 76, 50, 62, 54];
      const sideData = data.map((item) => item + 1);

      this.pdOption = {
        backgroundColor: "rgba(0,0,0,0)",
        title: [
          {
            text: "2022年9月评定结果：2类隧道(89.07分)",
            left: "center",
            top: "2%",
            textStyle: {
              color: "#42ABFF",
            },
          },
        ],
        grid: {
          top: "15%",
          left: "3%",
          right: "3%",
          bottom: "2%",
          containLabel: true,
        },
        xAxis: {
          data: ["2016", "2017", "2018", "2019", "2020", "2021", "2022", "2023", "2024"],
          //坐标轴
          axisLine: {
            lineStyle: {
              color: "rgba(110,112,121,0.5)",
            },
          },
          //坐标值标注
          axisLabel: {
            show: true,
            textStyle: {
              color: "#fff",
            },
          },
        },
        yAxis: {
          //坐标轴
          axisLine: {
            show: false,
          },
          //坐标值标注
          axisLabel: {
            show: true,
            textStyle: {
              color: "#999999",
            },
          },
          //分格线
          splitLine: {
            lineStyle: {
              color: "rgba(110,112,121,0.5)",
            },
          },
          splitArea: {
            show: false,
          },
          interval: 10,
        },
        series: [
          {
            name: "a",
            tooltip: {
              show: false,
            },
            label: {
              normal: {
                show: true,
                position: 'top',
                fontSize: 16,
                color: '#fff',
                offset: [0, -10],
              },
            },
            type: "bar",
            barWidth: 24.5,
            itemStyle: {
              normal: {
                color: new echarts.graphic.LinearGradient(
                  0,
                  1,
                  0,
                  0,
                  [
                    {
                      offset: 0,
                      color: "rgba(0,255,251,0.1)", // 0% 处的颜色
                    },
                    {
                      offset: 0.6,
                      color: "rgba(0,255,251,0.6)", // 60% 处的颜色
                    },
                    {
                      offset: 1,
                      color: "rgba(0,255,251,1)", // 100% 处的颜色
                    },
                  ],
                  false
                ),
              },
            },
            data: data,
            barGap: 0,
          },
          {
            type: "bar",
            barWidth: 8,
            itemStyle: {
              normal: {
                color: new echarts.graphic.LinearGradient(
                  0,
                  1,
                  0,
                  0,
                  [
                    {
                      offset: 0,
                      color: "rgba(0,255,251,0.1)", // 0% 处的颜色
                    },
                    {
                      offset: 0.6,
                      color: "rgba(0,255,251,0.4)", // 60% 处的颜色
                    },
                    {
                      offset: 1,
                      color: "rgba(0,255,251,0.6)", // 100% 处的颜色
                    },
                  ],
                  false
                ),
              },
            },
            barGap: 0,
            data: sideData,
          },
          {
            name: "b",
            tooltip: {
              show: false,
            },
            type: "pictorialBar",
            itemStyle: {
              borderWidth: 1,
              borderColor: "rgba(125,255,253,0.7)",
              color: "#7DFFFD",
            },

            symbol: "path://M 0,0 l 120,0 l -30,60 l -120,0 z",
            symbolSize: ["30", "12"],
            symbolOffset: ["0", "-8"],
            symbolRotate: -15,
            symbolPosition: "end",
            data: data,
            z: 3,
          },
        ],
      };

    },
    onTabClick(index) {
      this.tabIndex = index;
    }
  },
}
</script>

<style lang="scss" scoped>
@import "@/assets/styles/utils.scss";

.bridge-detail {
  width: 100%;
  display: flex;

  .bridge-info {
    padding: vwpx(20px);

    .info-list {
      width: 100%;
      height: vwpx(70px);
      background-color: rgba(23, 116, 255, 0.1);
      margin: vwpx(4px) 0;
      padding: 0 vwpx(10px);

      display: flex;
      align-items: center;

      .name {
        font-family: Microsoft YaHei, Microsoft YaHei;
        font-weight: 400;
        font-size: vwpx(26px);
        color: #42ABFF;
      }

      .value {
        font-family: Microsoft YaHei, Microsoft YaHei;
        font-weight: 400;
        font-size: vwpx(26px);
        color: #FFFFFF;
        margin-left: vwpx(20px);
      }
    }
  }

  .inspect-info {
    padding: vwpx(20px);

    .list {
      height: vwpx(64px);
      background-color: rgba(23, 116, 255, 0.1);

      display: flex;
      align-items: center;
      justify-content: space-between;
      margin: vwpx(6px) 0;
      padding: 0 vwpx(10px);

      .name {
        font-family: Microsoft YaHei, Microsoft YaHei;
        font-weight: 400;
        font-size: vwpx(26px);
        color: #00CBFF;
      }

      .normal {
        font-family: Microsoft YaHei, Microsoft YaHei;
        font-weight: 400;
        font-size: vwpx(26px);
        color: #1CFFBC;
        display: flex;
        align-items: center;

        span {
          width: vwpx(15px);
          height: vwpx(15px);
          display: block;
          border-radius: 50%;
          background-color: #1CFFBC;
          margin-right: vwpx(10px);
        }
      }

      .error {
        font-family: Microsoft YaHei, Microsoft YaHei;
        font-weight: 400;
        font-size: vwpx(26px);
        color: #F53F3F;
        display: flex;
        align-items: center;

        span {
          width: vwpx(15px);
          height: vwpx(15px);
          display: block;
          border-radius: 50%;
          background-color: #F53F3F;
          margin-right: vwpx(10px);
        }
      }
    }
  }

  .pd-level {
    margin: vwpx(30px) vwpx(20px);
    height: vwpx(184px);
    background: #002449;
    box-shadow: inset 0px 0px 10px 0px #0065FF;
    border-radius: 6px;
    border: 1px solid #20A9FF;

    display: flex;
    align-items: center;

    .level {
      flex: 1;

      display: flex;
      align-items: center;
      justify-content: center;
      flex-direction: column;

      span:first-child {
        font-family: Microsoft YaHei UI, Microsoft YaHei UI;
        font-weight: 400;
        font-size: vwpx(28px);
        color: #FFFFFF;
        text-align: center;
        font-style: normal;
        text-transform: none;
      }

      span:last-child {
        font-family: Microsoft YaHei UI, Microsoft YaHei UI;
        font-weight: 700;
        font-size: vwpx(48px);
        color: #F2AF4A;
        text-align: center;
        font-style: normal;
        text-transform: none;
        margin-top: vwpx(10px);

        small {
          font-family: Microsoft YaHei UI, Microsoft YaHei UI;
          font-weight: 400;
          font-size: vwpx(24px);
          color: #B6B6B6;
          text-shadow: 0px 0px 10px rgba(27, 126, 242, 0.8);
          text-align: center;
          font-style: normal;
          text-transform: none;
        }
      }
    }

    .divider {
      width: 0;
      height: vwpx(104px);
      border: 1px solid rgba(156, 189, 255, 0.5);
    }
  }

  .pd-result {
    margin: vwpx(30px) vwpx(20px);

    .result-list {
      height: vwpx(80px);
      background: #01315D;
      margin-bottom: vwpx(10px);
      color: #ffffff;
      font-size: vwpx(26px);

      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 0 vwpx(30px);

      .list-index {
        width: vwpx(50px);
        height: vwpx(50px);
        background: rgba(4, 17, 48, 0.4);


        display: flex;
        align-items: center;
        justify-content: center;
      }

      .list-index1 {
        box-shadow: inset 0px 0px 6px 0px #F53F3F;
        border: 1px solid #F53F3F;
      }

      .list-index2 {
        box-shadow: inset 0px 0px 6px 0px #FF8F1F;
        border: 1px solid #FF8000;
      }

      .list-index3 {
        box-shadow: inset 0px 0px 6px 0px #FDAF34;
        border: 1px solid #FDAF34;
      }
    }
  }

  .tab-list {
    margin: 0 vwpx(20px) vwpx(10px) vwpx(20px);
    display: flex;
    align-items: center;
    height: vwpx(76px);

    .tab-item {
      flex: 1;
      height: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
      background: rgba(4, 17, 48, 0.4);
      box-shadow: inset 0px 0px 6px 0px #0154FB;

      font-family: Microsoft YaHei UI, Microsoft YaHei UI;
      font-weight: 700;
      font-size: vwpx(28px);
      color: #FFFFFF;
      text-shadow: 0px 0px 10px rgba(27, 126, 242, 0.8);
      text-align: center;
      font-style: normal;
      text-transform: none;
      cursor: pointer;
    }

    .tab-act {
      background: #42ABFF;
      border: 1px solid #42ABFF;
    }
  }

  .pr-table {
    border: 1px solid #2CB5FF;
    margin: 0 vwpx(20px);
    // height: 40vh;
  }

  .mt-advice {
    border: 1px solid #2CB5FF;
    height: vwpx(320px);
    background-color: rgba(23, 116, 255, 0.1);
    margin: vwpx(40px) vwpx(20px);

    display: flex;
    align-items: center;

    .advice-name {
      display: flex;
      align-items: center;
      justify-content: center;
      flex-direction: column;
      margin: 0 vwpx(20px);

      img {
        width: vwpx(130px);
        height: vwpx(130px);
      }

      span {
        font-family: Source Han Sans, Source Han Sans;
        font-weight: 500;
        font-size: vwpx(28px);
        color: #42ABFF;
        margin-top: vwpx(20px);
      }
    }

    .advice-cont {
      flex: 1;
      height: 100%;
      background: rgba(23, 116, 255, 0.1);
      font-family: Microsoft YaHei UI, Microsoft YaHei UI;
      font-weight: 400;
      font-size: vwpx(26px);
      color: #FFFFFF;

      display: flex;
      align-items: center;
      padding: vwpx(30px);

      text-align: left;
      font-style: normal;
      text-transform: none;
    }
  }

  .img-empty {
    height: calc(100% - 15px);
    margin: vwpx(20px);
    background: #01315D;
    font-family: Microsoft YaHei, Microsoft YaHei;
    font-weight: 400;
    font-size: vwpx(24px);
    color: rgba(255, 255, 255, 0.6);
    border-radius: vwpx(5px);

    display: flex;
    align-items: center;
    justify-content: center;
  }

  .inspect-situation {
    width: calc(100% - 15px);

    .header {
      width: 100%;
      height: vwpx(76px);
      background-image: url('~@/assets/cockpit/card-header-bg-l.png');
      background-repeat: no-repeat;
      background-size: auto 100%;
      display: flex;
      align-items: center;
      object-fit: fill;

      span {
        font-size: vwpx(34px);
        margin-left: vwpx(45px);
        font-family: YouSheBiaoTiHei, YouSheBiaoTiHei;
        font-weight: 400;
        color: #FFFFFF;
        text-shadow: 0px 0px 16px #0088FF;
        text-align: left;
        font-style: normal;
        text-transform: none;
        margin-top: vwpx(7px);
      }
    }

    .main {
      display: flex;
      align-items: center;
      justify-content: space-evenly;

      .tunnel-civil {
        position: relative;
        flex: 1;
        height: 41.3vh;
        border: 1px solid rgba(0, 166, 255, 0.5);
        padding: vwpx(20px);

        .civil-title {
          display: flex;
          align-items: center;

          span:first-child {
            display: block;
            width: vwpx(8px);
            height: vwpx(32px);
            background: #42ABFF;
          }

          span:last-child {
            font-family: Microsoft YaHei, Microsoft YaHei;
            font-weight: 700;
            font-size: vwpx(32px);
            color: #FFFFFF;
            text-shadow: 0px 0px 10px rgba(27, 126, 242, 0.8);
            text-align: center;
            font-style: normal;
            text-transform: none;
            margin-left: vwpx(15px);
          }
        }

        .upper-left {
          position: absolute;
          top: -1px;
          left: -1px;
          width: vwpx(30px);
          height: vwpx(30px);
          background-image: url('~@/assets/cockpit/card-box.png');
          background-repeat: no-repeat;
          background-size: 100% 100%;
          transform: rotate(180deg);
        }

        .upper-right {
          position: absolute;
          top: -1px;
          right: -1px;
          width: vwpx(30px);
          height: vwpx(30px);
          background-image: url('~@/assets/cockpit/card-box.png');
          background-repeat: no-repeat;
          background-size: 100% 100%;
          transform: rotate(270deg);
        }

        .lower-left {
          position: absolute;
          bottom: -1px;
          left: -1px;
          width: vwpx(30px);
          height: vwpx(30px);
          background-image: url('~@/assets/cockpit/card-box.png');
          background-repeat: no-repeat;
          background-size: 100% 100%;
          transform: rotate(90deg);
        }

        .lower-right {
          position: absolute;
          bottom: -1px;
          right: -1px;
          width: vwpx(30px);
          height: vwpx(30px);
          background-image: url('~@/assets/cockpit/card-box.png');
          background-repeat: no-repeat;
          background-size: 100% 100%;
          transform: rotate(0deg);
        }
      }
    }
  }

  .flex {
    display: flex;
  }

  .flex-1 {
    flex: 1;
  }

  .flex-2 {
    flex: 2;
  }

  .mb-2 {
    margin-bottom: vwpx(20px);
  }

  .mb-3 {
    margin-bottom: vwpx(30px);
  }

  .ml-3 {
    margin-left: vwpx(30px);
  }

  .ml-2 {
    margin-left: vwpx(20px);
  }

  .mr-2 {
    margin-right: vwpx(20px);
  }

  .img {
    width: 100%;
    height: 100%;
    padding: 5px;
    cursor: pointer;
    object-fit: fill;
  }
}
</style>