<template>
  <div class="app-container maindiv">
    <el-steps :active="timeList.length + 1">
      <el-step v-for="item in timeList" :title="item.title" :description="item.time"></el-step>
    </el-steps>
  </div>
</template>

<script>
export default {
  data() {
    return {
      timeList: [
        {title: '任务单新增', time: '2024年9月19日09:11:19'},
        {title: '任务单审核', time: '2024年9月19日09:11:19'},
        {title: '任务单下发', time: '2024年9月19日09:11:19'}
      ]
    }
  }
}
</script>
<style scoped lang="scss">

</style>
<style lang="scss" scoped>
@import "@/assets/styles/business.scss";
</style>
