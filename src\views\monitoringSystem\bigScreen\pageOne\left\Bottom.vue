<template>
  <!-- <ItemCard :title="'桥墩倾斜'" :wide="false">
    <template>
      <div class="box">
        <div class="left">
          <Echarts :option="option1" v-if="option1" :height="'92%'" key="option2" />
          <div class="installLocationContent">玉溪侧倾角</div>
        </div>
        <div class="right">
          <Echarts :option="option2" v-if="option2" :height="'92%'" key="option3" />
          <div class="installLocationContent">元江侧倾角</div>
        </div>
      </div>
    </template>
</ItemCard> -->
  <Cards :title="title" w="100%" h="28vh" :isDtl="false">
    <div class="box" v-if="!isSingle">
      <div class="left">
        <Echarts :option="option1" v-if="option1" :height="'92%'" :key="option1Key" />
        <div class="installLocationContent">{{ installLocationList[0] }}</div>
      </div>
      <div class="right">
        <Echarts :option="option2" v-if="option2" :height="'92%'" :key="option2Key" />
        <div class="installLocationContent">{{ installLocationList[1] }}</div>
      </div>
    </div>
    <div v-else class="box" style="justify-content: center; align-items: center;">
      <Echarts :option="option3" v-if="option3" :height="'92%'" :key="option3Key" />
    </div>
  </Cards>
</template>

<script>
import { isBigScreen } from '../../../utils/utils.js';
import ItemCard from "../../components/itemCard.vue"
import Echarts from "../../components/echarts.vue"
import Cards from "../../components/cards.vue"
import { fetchGet } from '../../../utils/api.js'

export default {
  name: 'Bottom',
  inject: ['iThis'],
  props: {
    // title: {
    //   type: String,
    //   default: '桥墩倾斜'
    // }
  },
  components: { ItemCard, Echarts, Cards },
  data() {
    return {
      title: '桥墩倾斜',
      isBig: isBigScreen(),
      option1: null,
      option2: null,
      option3: null,
      option1Key: 'option1Key',
      option2Key: 'option1Key',
      option3Key: 'option1Key',
      sensorList: [],
      backupSensorList: [],
      singleChartData: [],
      installLocationList: [],
      chartData1: [],
      chartData2: [],
      url: 'https://jkjc.yciccloud.com:8000/xboot/displayScreen/default/getCalibratedRealTimeData',
      isSingle: false,
    }
  },
  created() {
    this.init()
  },
  methods: {
    async init() {
      let result = this.iThis.sensorList;
      // 选择其中的转角传感器
      const keywords = [
        "X方向转角变化值",
        "X方向倾角变化值",
        "X方向倾角",
        "X方向边坡倾角变化值",
      ];
      this.sensorList = keywords
        .map(keyword => result.filter(item => item.specificMonitorTypeName.includes(keyword)).slice(0, 2))
        .find(list => list.length > 0) || [];

      const keywords2 = [
        // "压强",
        // "微应变",
        "高程坐标变化",
      ]
      this.backupSensorList = keywords2
        .map(keyword => result.filter(item => item.specificMonitorTypeName.includes(keyword)).slice(0, 1))
        .find(list => list.length > 0) || [];

      if (this.sensorList.length != 0) {
        await this.sensorList.forEach((element, index) => {
          this.installLocationList[index] = element.installLocation
          this.getChartData(element.code, element.sensorId, index);
        });
        setTimeout(() => {
          this.initChart(this.chartData1, this.chartData2);
        }, 1000);
      } else {
        this.getRHTempData()
      }
    },
    getChartData(nodeCode, sensorId, index) {
      let params = {
        nodeCode: nodeCode,
        sensorId: sensorId,
        structureNodeCode: this.iThis.params.code,
      };
      fetchGet(this.url, params).then((res) => {
        if (res.code == 200) {
          this.title = this.iThis.params.type + "倾斜";
          if (res.result.length < 2) return;
          let xData = res.result[0]?.values || [];
          let yData = res.result[1]?.values || [];
          this.title = this.iThis.params.type + "倾斜";
          let newData = [];
          if (xData.length > 0) {
            for (let i = 0; i < xData.length; i++) {
              newData.push([xData[i], yData[i]]);
            }
          }
          if (index == 0) {
            this.chartData1 = newData;
          } else if (index == 1) {
            this.chartData2 = newData;
          }
        } else {
          console.log("getCalibratedRealTimeData返回数据失败");
        }
      });
    },
    getRHTempData() {
      this.title = this.iThis.params.type + "倾斜";
      this.isSingle = true
      if (this.iThis.params.type == '边坡' || this.backupSensorList.length == 0) {
        this.option3 = {
          title: {
            text: '没有数据',
            left: 'center',
            top: 'center',
            textStyle: {
              fontSize: this.isBig ? 32 : 14,
              color: '#606266',
              fontWeight: 700
            },
          },
        };
        this.option3Key = new Date().getTime();
      } else {
        this.title = this.backupSensorList[0].specificMonitorTypeName[0];
        fetchGet(this.url, {
          nodeCode: this.backupSensorList[0].code,
          sensorId: this.backupSensorList[0].sensorId,
          structureNodeCode: this.iThis.params.code,
        }).then((res) => {
          if (res.code == 200) {
            this.isSingle = true
            this.singleChartData = res.result;
            this.initSingleChart(this.singleChartData);
          } else {
            console.log("getCalibratedRealTimeData返回数据失败");
          }
        });
      }
    },
    initChart(data1, data2) {
      var option1;
      var option2;
      if (data1.length == 0) {
        option1 = {
          title: {
            text: '没有数据',
            left: 'center',
            top: 'center',
            textStyle: {
              fontSize: this.isBig ? 32 : 14,
              color: '#606266',
              fontWeight: 700
            }
          }
        }
      } else {
        option1 = {
          tooltip: {
            trigger: "item", // 设置 tooltip 触发类型为 item
            formatter: function (params) {
              // 自定义 tooltip 显示内容
              return `${params.data[0]}, ${params.data[1]}`;
            },
          },
          xAxis: {
            axisLabel: {
              color: "rgba(156, 189, 255, 1)", // 设置x轴标签颜色为白色
              fontSize: this.isBig ? 24 : 10,
            },
            splitLine: {
              show: true,
              lineStyle: {
                color: 'rgba(255, 255, 255, 0.50)' // 这里设置了分割线颜色
              }
            },
            axisLine: {
              show: false,
            },
            splitArea: {
              show: false,
            },
            axisTick: {
              show: false,
            },
          },
          yAxis: {
            axisLabel: {
              color: "rgba(156, 189, 255, 1)", // 设置y轴标签颜色为白色
              fontSize: this.isBig ? 24 : 10,
            },
            splitArea: {
              show: false,
            },
            splitLine: {
              lineStyle: {
                color: 'rgba(255, 255, 255, 0.50)' // 修改分割线颜色
              }
            },
            axisLine: {
              show: false,
            },
            axisTick: {
              show: false,
            },
          },
          grid: {
            left: "1%",
            right: "10%",
            top: "10%",
            bottom: "4",
            containLabel: true,
          },
          series: [
            {
              // 散点大小
              symbolSize: this.isBig ? 10 : 5,
              data: data1,
              type: "scatter",
              itemStyle: {
                color: "#00BFFF", // 设置散点颜色
              },
            },
          ],
        };
      }

      if (data2.length == 0) {
        option2 = {
          title: {
            text: '没有数据',
            left: 'center',
            top: 'center',
            textStyle: {
              fontSize: this.isBig ? 32 : 14,
              color: '#606266',
              fontWeight: 700
            }
          }
        }
      } else {
        option2 = {
          tooltip: {
            trigger: "item", // 设置 tooltip 触发类型为 item
            formatter: function (params) {
              // 自定义 tooltip 显示内容
              return `${params.data[0]}, ${params.data[1]}`;
            },
          },
          xAxis: {
            axisLabel: {
              color: "rgba(156, 189, 255, 1)", // 设置x轴标签颜色为白色
              fontSize: this.isBig ? 24 : 10,
            },
            splitLine: {
              show: true,
              lineStyle: {
                color: 'rgba(255, 255, 255, 0.50)' // 这里设置了分割线颜色
              }
            },
            axisLine: {
              show: false,
            },
            splitArea: {
              show: false,
            },
            axisTick: {
              show: false,
            },
          },
          yAxis: {
            axisLabel: {
              color: "rgba(156, 189, 255, 1)", // 设置x轴标签颜色为白色
              fontSize: this.isBig ? 24 : 10,
            },
            splitArea: {
              show: false,
            },
            splitLine: {
              lineStyle: {
                color: 'rgba(255, 255, 255, 0.50)' // 修改分割线颜色
              }
            },
            axisLine: {
              show: false,
            },
            axisTick: {
              show: false,
            },
          },
          grid: {
            left: "1%",
            right: "10%",
            top: "10%",
            bottom: "4",
            containLabel: true,
          },
          series: [
            {
              // 散点大小
              symbolSize: this.isBig ? 10 : 5,
              data: data2,
              type: "scatter",
              itemStyle: {
                color: "#00BFFF", // 设置散点颜色
              },
            },
          ],
        };
      }




      this.option1 = option1
      this.option2 = option2
      this.option1Key = new Date().getTime();
      this.option2Key = new Date().getTime();
    },
    initSingleChart(data) {
      var option;

      let myseries = [];
      let mylegend = [];
      let myXdata = [];
      data.forEach((item) => {
        myseries.push({
          name: item.name,
          type: "line",
          data: item.values,
        });
        mylegend.push(item.name);
        myXdata = item.times;
      });
      option = {
        tooltip: {
          trigger: "axis",
        },
        legend: {
          data: mylegend,
          top: "2%",
          itemGap: this.isBig ? 20 : 10, // 每个图例间隔
          textStyle: {
            color: "#ffffff",
            fontSize: this.isBig ? 24 : 12,
          },
        },
        grid: {
          left: "2%",
          right: "4%",
          bottom: "5%",
          top: "16%",
          containLabel: true,
        },
        xAxis: {
          type: "category",
          boundaryGap: false,
          data: myXdata,
          axisLabel: {
            // 添加或修改 axisLabel 配置项
            textStyle: {
              // 设置文本样式
              color: "#ffffff",
              fontSize: this.isBig ? 24 : 10,
            },
          },
          axisLine: {
            show: true,
            lineStyle: {
              color: 'rgba(110, 112, 121, 0.70)'
            }
          },
          axisTick: {
            alignWithLabel: true, // 刻度线与标签对齐
            inside: true // 刻度线朝上
          }
        },
        yAxis: {
          type: "value",
          axisLabel: {
            // 添加或修改 axisLabel 配置项
            textStyle: {
              // 设置文本样式
              color: "rgba(153, 153, 153, 1)",
              fontSize: this.isBig ? 24 : 10,
            },
          },
          splitArea: {
            show: false,
          },
          splitLine: {
            lineStyle: {
              color: 'rgba(110, 112, 121, 0.70)' // 修改分割线颜色
            }
          },
        },
        series: myseries,
      };

      this.option3 = option;
      this.option3Key = new Date().getTime();
    },
  },
  computed: {},
  watch: {},
}
</script>

<style lang="scss" scoped>
.box {
  display: flex;
  height: 100%;
  width: 100%;

  .left {
    position: relative;
    top: 3%;
    left: 2%;
    height: 90%;
    width: 48%;
  }

  .right {
    position: relative;
    top: 3%;
    left: 2%;
    height: 90%;
    width: 48%;
  }

  .installLocationContent {
    width: 90%;
    height: 8%;
    text-align: center;
    color: white;
    font-size: 1vh;
    letter-spacing: 1px;
  }
}
</style>