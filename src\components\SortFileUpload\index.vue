<template>
  <div class="upload-file">
    <el-upload
      ref="fileUpload"
      multiple
      :action="uploadFileUrl"
      :data="uploadParams"
      :before-upload="handleBeforeUpload"
      :file-list="fileList"
      :limit="limit"
      :on-error="handleUploadError"
      :on-exceed="handleExceed"
      :on-success="handleUploadSuccess"
      :class="'upload-file-uploader' + ' ' + (forView ? 'uploadBox-hide' : '')"
      :headers="headers"
      :drag="true"
      list-type="picture-card"
    >
      <i class="el-icon-upload"></i>
      <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
      <div slot="file" slot-scope="{ file }" class="upload-file-item">
        <img
          class="el-upload-list__item-thumbnail"
          :src="file.thumbUrl"
          alt=""
        />
        <el-tooltip :content="file.name" placement="top">
          <div v-if="showFileName" class="file-name-overlay">{{ file.name }}</div>
          <span class="el-upload-list__item-actions">
            <span
              class="el-upload-list__item-preview"
              @click="handlePictureCardPreview(file)"
            >
              <i class="el-icon-zoom-in" />
            </span>
            <span
              v-if="!disabled"
              class="el-upload-list__item-delete"
              @click="handleDownload(file)"
            >
              <i class="el-icon-download" />
            </span>
            <span
              v-if="!disabled && !forView"
              class="el-upload-list__item-delete"
              @click="handleRemove(file)"
            >
              <i class="el-icon-delete" />
            </span>
          </span>
        </el-tooltip>
      </div>
      <!-- 上传提示 -->
      <div v-if="showTip" slot="tip" class="el-upload__tip">
        请上传
        <template v-if="fileSize">
          大小不超过 <b style="color: #f56c6c">{{ fileSize }}MB</b>
        </template>
        <template v-if="fileType">
          格式为 <b style="color: #f56c6c">{{ fileType.join("/") }}</b>
        </template>
        的文件
      </div>
    </el-upload>
    <div style="color: red;font-size: 12px;height: 35px;line-height: normal;" v-if="!forView">严禁在本互联网非涉密平台处理、传输国家秘密，请确认扫描、传输的文件资料不涉及国家秘密</div>
    <el-dialog
      :width="previewWidth"
      :visible.sync="dialogVisible"
      custom-class="fixed-dialog"
      append-to-body
    >
      <template v-if="showFileName" #title>
        <span v-html="dialogTitle" class="dialog-title"></span>
      </template>
      <OfficePreview
        :excel-options="excelOptions"
        :dialog-url="dialogUrl"
        :content-type="contentType"
        v-if="dialogVisible"
      />
    </el-dialog>
  </div>
</template>

<script>
import { getToken } from "@/utils/auth";
import { findFiles } from "@/api/file/index.js";
import {
  removeFile,
  addUploadRecord,
  removeUploadRecord,
} from "@/api/system/fileUpload.js";
import OfficePreview from "@/components/OfficePreview/index.vue";
import Sortable from "sortablejs";
import { v4 as uuidv4 } from 'uuid';

export default {
  name: "FileUpload",
  components: {
    OfficePreview,
  },
  props: {
    value: {
      // 绑定值传ownerId，可以是id数组
      type: undefined,
      default: () => [],
    },
    // 数量限制
    limit: {
      type: Number,
      default: null,
    },
    // 大小限制(MB)
    fileSize: {
      type: Number,
      default: null,
    },
    // 文件类型, 例如['png', 'jpg', 'jpeg']
    fileType: {
      type: Array,
      //   default: () => ["doc", "xls", "ppt", "txt", "pdf", "png", "jpg", "jpeg"],
      default: null,
    },
    // 是否显示提示
    isShowTip: {
      type: Boolean,
      default: true,
    },
    fileSubId: {
      type: Number,
      default: 0,
    },
    bizId: {
      type: String,
      default: "",
    },
    ownerId: {
      // 文件唯一标识id
      default: +new Date(),
    },
    storagePath: {
      // 存储路径用于分类
      type: String,
      default: "",
    },
    forView: {
      // 当查看时传true
      type: Boolean,
      default: false,
    },
    showFileName: {
      // 是否显示文件名
      type: Boolean,
      default: false,
    },
    previewWidth: {
      type: String,
      default: "",
    },
    downloadName: {
      // 自定义下载文件名
      type: String,
      default: null,
    },
    needTime: {
      // 是否需要给下载文件名加年月日时分秒 例如：文件名_2021-01-01T12_00_00
      type: Boolean,
      default: false,
    },
    successHook: {
      // 上传成功后的回调
      type: Function,
      default: () => {},
    },
    excelOptions: {
      type: Object,
      default: () => ({}),
    },
    canSort: {
      // 是否开启拖拽排序
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      number: 0,
      uploadList: [],
      uploadFileUrl:
        process.env.VUE_APP_BASE_API +
        `/file/upload`,
      uploadParams: {
        platform: 'ylzx',
        ownerId: this.ownerId,
        storagePath: this.storagePath
      },
      headers: {
        Authorization: "Bearer " + getToken(),
      },
      fileList: [],
      dialogUrl: "",
      dialogTitle: "",
      contentType: "",
      dialogVisible: false,
      disabled: false
    };
  },
  computed: {
    // 是否显示提示
    showTip() {
      return this.isShowTip && (this.fileType || this.fileSize);
    },
  },
  watch: {
    value: {
      async handler(val) {
        if (val) {
          // 首先将值转为数组
          const list = Array.from(
            new Set(Array.isArray(val) ? val : this.value.split(","))
          );
          // 然后将数组转为对象数组
          const tempArr = [];
          for (let i = 0; i < list.length; i++) {
            let ownerId = list[i]
            const {data} = await findFiles({ownerId});
            tempArr.push(
              ...data.map((file) => {
                return {
                  ...file,
                  name: file.originalFilename,
                  url: file.url,
                  ownerId: file.ownerId,
                  thumbUrl: this.getFileThumbnailUrl(
                    file.contentType,
                    file.thumbUrl
                  ),
                };
              })
            );
          }
          this.fileList = tempArr;
          console.log(this.fileList)
        } else {
          this.fileList = [];
        }
      },
      deep: true,
      immediate: true,
    },
    ownerId: {
      handler(val) {
        if (val) {
          //如果ownerId有变化，这里需要监听，否则url地址的ownerId不会变化
          this.uploadParams.ownerId = uuidv4().substring(0, 20)
        }
      },
    },
  },
  mounted() {
    if (this.canSort) {
      // 开启拖拽排序，开启后每上传文件都会生成一个新的ownerId
      this.initDragSort()
      this.uploadParams.ownerId = uuidv4().substring(0, 20)
    }
  },
  methods: {
    // 上传前校检格式和大小
    handleBeforeUpload(file) {
      // 校检文件类型
      if (this.fileType) {
        const fileName = file.name.split(".");
        const fileExt = fileName[fileName.length - 1].toLowerCase();
        const isTypeOk = this.fileType.indexOf(fileExt) >= 0;
        if (!isTypeOk) {
          this.$modal.msgError(
            `文件格式不正确, 请上传${this.fileType.join("/")}格式文件!`
          );
          return false;
        }
      }
      // 校检文件大小
      if (this.fileSize) {
        const isLt = file.size / 1024 / 1024 < this.fileSize;
        if (!isLt) {
          this.$modal.msgError(`上传文件大小不能超过 ${this.fileSize} MB!`);
          return false;
        }
      }
      if (this.canSort) {
        // 开启拖拽排序，开启后每上传文件都会生成一个新的ownerId
        this.uploadParams.ownerId = uuidv4().substring(0, 20)
        console.log(this.uploadParams.ownerId)
      }
      this.$modal.loading("正在上传文件，请稍候...");
      this.number++;
      return true;
    },
    // 文件个数超出
    handleExceed() {
      this.$modal.msgError(`上传文件数量不能超过 ${this.limit} 个!`);
    },
    // 上传失败
    handleUploadError(err) {
      this.$modal.msgError("上传文件失败，请重试");
      this.$modal.closeLoading();
    },
    // 上传成功回调
    handleUploadSuccess(res, file) {
      if (res.code === 200) {
        // 根据文件类型设置缩略图
        const thumbUrl = this.getFileThumbnailUrl(
          res.data.contentType,
          res.data.thumbUrl
        );
        const temp = {
          ext: res.data.ext,
          thumbUrl: thumbUrl,
          id: res.data.id,
          remoteUrl: res.data.url,
          contentType: res.data.contentType,
          name: res.data.originalFilename,
          url: res.data.url,
          uid: file.uid,
          ownerId: res.data.ownerId,
        };
        this.uploadList.push(temp);
        addUploadRecord({
          fileSubId: this.fileSubId,
          bizId: this.bizId,
          fileName: res.data.originalFilename,
          fileId: res.data.id,
          fileSize: res.data.fileSize,
          ownerId: res.data.ownerId,
        }).then();
        this.uploadedSuccessfully();
        if (this.successHook) {
          this.successHook(res.data);
        }
      } else {
        this.number--;
        // this.$modal.closeLoading();
        this.$modal.msgError(res.msg);
        this.fileList.splice(findex, 1);
        // this.$emit("input", this.formatValue(this.fileList));
        // this.$refs.fileUpload.handleRemove(file);
        this.uploadedSuccessfully();
      }
    },
    // 上传结束处理
    uploadedSuccessfully() {
      if (this.number > 0 && this.uploadList.length === this.number) {
        this.fileList = this.fileList.concat(this.uploadList);
        this.uploadList = [];
        this.number = 0;
        this.$modal.closeLoading();
      }
    },
    // 获取文件名称
    getFileName(name) {
      // 如果是url那么取最后的名字 如果不是直接返回
      if (name.lastIndexOf("/") > -1) {
        return name.slice(name.lastIndexOf("/") + 1);
      } else {
        return name;
      }
    },
    // 格式化返回对象
    formatValue(list) {
      return list.filter((item) => item.ownerId).map((el) => el.ownerId);
    },
    handleRemove(file) {
      this.$confirm('确定删除该文件吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }).then(() => {
        if (!file.id) return
        const findex = this.fileList.map((f) => f.id).indexOf(file.id)
        if (findex > -1) {
          if (this.bizId === '' && this.fileSubId === 0) {
            removeFile(file.id).then((res) => {
              if (res.code === 200) {
                this.fileList.splice(findex, 1)
                this.$emit('input', this.formatValue(this.fileList))
                this.$message({
                  type: 'success',
                  message: '删除成功!',
                })
              }
            })
          } else {
            removeUploadRecord({
              fileId: file.id,
              assetType: this.assetType,
              fileSubId: this.fileSubId,
            }).then(() => {
              removeFile(file.id).then((res) => {
                if (res.code === 200) {
                  this.fileList.splice(findex, 1)
                  this.$emit('input', this.formatValue(this.fileList))
                  this.$message({
                    type: 'success',
                    message: '删除成功!',
                  })
                }
              })
            })
          }
        }
      })
    },
    handlePictureCardPreview(file) {
      this.contentType = file.contentType;
      this.dialogUrl = file.url;
      this.dialogTitle = file.name;
      this.dialogVisible = true;
    },
    async handleDownload(file) {
      const url = file.url || (file.response && file.response.url);
      if (!url) {
        this.$message.error("文件无法下载，未找到文件的URL");
        return;
      }
      try {
        const response = await fetch(url);
        if (!response.ok) {
          throw new Error("网络响应不是 2xx");
        }
        const blob = await response.blob();
        const link = document.createElement("a");
        link.href = URL.createObjectURL(blob);

        const originalFileName = this.getFileName(file.name) || "download";
        const fileExtension = originalFileName.split(".").pop();
        let fileName = this.downloadName || originalFileName;

        // 如果 fileName 已经有后缀，去掉后缀
        if (fileName.includes(".")) {
          fileName = fileName.substring(0, fileName.lastIndexOf("."));
        }

        const timestamp = this.needTime
          ? `_${this.$moment().format("YYYY-MM-DDTHH_mm_ss")}`
          : "";

        // 生成最终的文件名，确保后缀在最后
        const finalFileName = `${fileName}${timestamp}.${fileExtension}`;

        link.download = finalFileName;

        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);

        // 释放 URL 对象
        URL.revokeObjectURL(link.href);
      } catch (error) {
        this.$message.error("文件无法下载，未找到文件的URL");
      }
    },
    reset() {
      this.fileList = [];
    },
    // 根据文件类型获取缩略图
    getFileThumbnailUrl(contentType, thumbUrl) {
      if (contentType.startsWith("image/")) {
        // 如果是图片类型，直接返回图片 URL
        return thumbUrl;
      } else {
        switch (contentType) {
          case "application/pdf":
            return require("@/assets/images/Ipdf.png");
          case "application/msword":
          case "application/vnd.openxmlformats-officedocument.wordprocessingml.document":
            return require("@/assets/images/Iword.png");
          case "application/vnd.ms-excel":
          case "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet":
            return require("@/assets/images/Iexcel.png");
          case "application/vnd.ms-powerpoint":
          case "application/vnd.openxmlformats-officedocument.presentationml.presentation":
            return require("@/assets/images/Ippt.png");
          case "application/zip":
          case "application/x-zip-compressed":
          case "application/x-rar-compressed":
          case "application/x-7z-compressed":
          case "application/x-tar":
          case "application/gzip":
            return require("@/assets/images/Izip.png");
          default:
            return require("@/assets/images/Iother.png"); // 默认文件图标
        }
      }
    },
    // 支持拖拽排序
    initDragSort() {
      const el = this.$refs.fileUpload.$el.querySelectorAll('.el-upload-list')[0];
      Sortable.create(el, {
        onEnd: ({ oldIndex, newIndex }) => {
          // 交换位置
          const arr = this.fileList;
          const page = arr[oldIndex];
          arr.splice(oldIndex, 1);
          arr.splice(newIndex, 0, page);
        }
      });
    },
    save(){
      this.$emit("input", this.formatValue(this.fileList));
    }
  },
};
</script>

<style scoped lang="scss">
.upload-file-uploader {
  max-height: 390px;
  overflow-y: auto;
  margin-bottom: 5px;
}
.fixed-dialog {
  position: fixed !important;
  top: 50% !important;
  left: 50% !important;
  transform: translate(-50%, -50%) !important;
  z-index: 999 !important; /* 确保弹窗在所有元素之上 */
}
::v-deep .uploadBox-hide .el-upload--picture-card {
  display: none;
}

.file-name-overlay {
  position: absolute;
  bottom: 0; /* 或者根据需求调整位置 */
  left: 0;
  width: 100%;
  background-color: rgba(0, 0, 0, 0.5); /* 半透明背景 */
  color: white; /* 文本颜色 */
  padding: 2px 5px;
  text-align: center; /* 文本居中 */
  z-index: 1; /* 确保在最上层 */
  box-sizing: border-box; /* 确保 padding 不影响宽度 */
}
.el-upload-list__item-actions {
  z-index: 2;
}

.el-upload-list__item-actions > span {
  cursor: pointer;
}

.dialog-title {
  font-size: 20px; /* 调整字体大小 */
  font-weight: bold; /* 加粗 */
  color: #708090;
}

// 新增样式以限制拖拽区域大小
::v-deep .el-upload-dragger {
  width: 147px;
  height: 147px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

::v-deep .el-upload--picture-card {
  width: 148px;
  height: 148px;
  line-height: normal;
}

::v-deep .el-upload-dragger .el-icon-plus {
  font-size: 28px;
  color: #8c939d;
  margin-bottom: 8px;
}

::v-deep .el-upload-dragger .el-upload__text {
  font-size: 12px;
  color: #606266;
  text-align: center;
  line-height: 1.2;
  padding: 0 5px;
}
</style>
