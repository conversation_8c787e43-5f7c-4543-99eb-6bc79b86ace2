import { createStringXY } from 'ol/coordinate';
import { fromLonLat } from 'ol/proj'; // 导入方法
import { WKT, MVT, GeoJSON, JSONFeature } from 'ol/format';
import VectorLayer from 'ol/layer/Vector';
import VectorSource from 'ol/source/Vector';
import Cluster from 'ol/source/Cluster';
import {
  Circle as CircleStyle,
  Fill,
  Icon,
  Style,
  Text,
  Stroke,
  RegularShape,
} from 'ol/style';
import {
  MultiPolygon as GeomMultiPolygon,
  LineString as GeomLineString,
} from 'ol/geom';
import { fromExtent } from 'ol/geom/Polygon';
import { Point } from 'ol/geom';
import {
  Vector as sourceVector,
  VectorTile as VectorTileSource,
  XYZ,
} from 'ol/source';
import { Feature } from 'ol';
import { Draw, Modify, Select, DoubleClickZoom } from 'ol/interaction';
import VectorTileLayer from 'ol/layer/VectorTile';
import { getArea, getLength } from 'ol/sphere';
import { toFeature } from 'ol/render/Feature';
import { createXYZ } from 'ol/tilegrid';
import { get as getProj } from 'ol/proj';

import { getToken } from '@/utils/auth';
import { findFiles } from '@/api/file/index.js';
import store from '@/store';
import { getShapeList } from '@/api/oneMap/deptInfo';
import cache from '@/plugins/cache';
import { getMenuSub } from '@/api/oneMap/menuSub';

import spotImg from '@/assets/map/spot.png';
import pointImg from '@/assets/map/point.png';

export const lineId = '1824259894327382017';

// 从vuex 中获取 当前 地图相关数据
let mapObj = store.state.map || {};
// 是否是超级管理员
let isAdmin = store.getters.roles.includes('admin');
var timer = null;
// 创建一个解析 WKT 的格式对象
const format = new WKT();
const baseStyle = new Style({
  fill: new Fill({
    color: 'rgba(255, 255, 255, 0.8)',
  }),
  stroke: new Stroke({
    color: '#33cc33',
    width: 2,
  }),
  image: new CircleStyle({
    anchor: [15, 15],
    radius: 7,
    stroke: new Stroke({
      color: 'rgba(255,255,255,0.8)',
      width: 1,
    }),
    fill: new Fill({
      color: 'rgb(64,158,255)',
    }),
  }),
  text: new Text({
    font: '13px Microsoft YaHei',
    fill: new Fill({
      color: '#000',
    }),
    stroke: new Stroke({
      color: '#fff',
      width: 0,
    }),
    text: '',
    offsetY: -25,
    padding: [5, 5, 0, 10], // 文本与背景之间的间距
    overflow: true,
  }),
  // zIndex: 9999,
});

export function addDataToMap(
  data = null,
  layerKey = 'dataLayer',
  icon = null,
  flashing = false
) {
  if (!data) return;
  if (!Array.isArray(data)) {
    console.error('addDataToMap: data must be an array');
    return;
  }

  let features = [];
  let icons = icon;
  let vectorsLayer = null;

  data.forEach((item) => {
    if (!item.lon || !item.lat) {
      console.warn('Item missing coordinates:', item);
      return;
    }

    const coordinates = fromLonLat([Number(item.lon), Number(item.lat)]);
    const point = new Point(coordinates);
    const feature = new Feature({
      geometry: point,
    });

    feature.setStyle(
      new Style({
        text: new Text({
          text: item.名称 || '',
          fill: new Fill({
            color: '#fff',
          }),
          stroke: new Stroke({
            color: '#333',
            width: 2,
          }),
          offsetX: 0,
          offsetY: -20,
          textAlign: 'center',
          textBaseline: 'middle',
          font: '12px Arial',
          backgroundFill: new Fill({
            color: 'rgba(13,27,43,0.6)',
          }),
          padding: [2, 5, 2, 5],
        }),
        image: new Icon({
          anchor: [0.5, 0.5],
          anchorXUnits: 'fraction',
          anchorYUnits: 'pixels',
          src: icons
            ? typeof icons === 'boolean'
              ? require('@/assets/map/piont.png')
              : icons
            : spotImg,
          scale: 0.8,
        }),
      })
    );

    feature.set('data', item);
    features.push(feature);
  });

  vectorsLayer = new VectorLayer({
    source: new VectorSource({
      features: features,
    }),
  });
  vectorsLayer.setZIndex(9002);
  vectorsLayer.set('name', layerKey);

  if (flashing) {
    let flag = false;
    const fun = () => {
      if (!flag) {
        vectorsLayer.setOpacity(0.8);
        vectorsLayer.getSource().forEachFeature(function (feature) {
          var style = feature.getStyle();
          let textStyle = style.getText();
          let feaObj = feature.getProperties().data || {};

          let text = '';
          if (feaObj.名称) {
            text = `${feaObj.名称}`;
            // text = `${feaObj.名称}\n`;
            // if (feaObj.负责人) {
            //   text += `负责人: ${feaObj.负责人}\n`;
            // }
            // if (feaObj.area) {
            //   text += `面积: ${feaObj.area}m²\n`;
            // }
            // if (feaObj.覆盖路段) {
            //   text += `覆盖路段: ${feaObj.覆盖路段}`;
            // }
          }

          textStyle.setText(text);

          let newStyle = new Style({
            text: textStyle,
            image: new Icon({
              anchor: [0.5, 1],
              anchorXUnits: 'fraction',
              anchorYUnits: 'pixels',
              src:
                feaObj.dept_id == 1 || feaObj.sys_dept_id == 1
                  ? pointImg
                  : spotImg,
              scale: 0.8,
              opacity: 1,
            }),
          });
          feature.setStyle(newStyle);
        });
        flag = false;
      } else {
        vectorsLayer.setOpacity(1);
        vectorsLayer.getSource().forEachFeature(function (feature) {
          var style = feature.getStyle();
          let textStyle = style.getText();
          let feaObj = feature.getProperties().data || {};

          let newStyle = new Style({
            text: textStyle,
            image: new Icon({
              anchor: [0.5, 0.5],
              anchorXUnits: 'fraction',
              anchorYUnits: 'pixels',
              src:
                feaObj.dept_id == 1 || feaObj.sys_dept_id == 1
                  ? pointImg
                  : spotImg,
              scale: 0.8,
              opacity: 1,
            }),
          });
          feature.setStyle(newStyle);
        });
        flag = true;
      }
    };
    timer = setInterval(fun, 1000);
  }

  return vectorsLayer;
}

export function addSourceToMap(source) {}

export function removeLayer(
  mapLayer,
  layerName = 'shapeLayer',
  layerKey = 'name'
) {
  // 移除图层
  let allLayer = mapLayer?.getLayers()?.getArray();
  if (!allLayer) return;
  let layer = allLayer.find((l) => l.get(layerKey) == layerName);
  if (layer) {
    mapLayer.removeLayer(layer);
  }
  if (timer) {
    clearInterval(timer);
    timer = null;
  }
}
/**
 * 添加适量切片
 * @param {*} mapLayer 地图对象
 * @param {*} layerKey 图层名称
 * @param {*} layerValue 图层名称值
 * @param {*} dataDto 切片请求参数
 * @param {*} row 点击的数据
 * @param {*} lineBase 是否是地图路线
 * @param {*} minZoom 最小矢量图层加载层级
 * @param {*} tileSize 图层加载大小
 * @returns
 */
export function addVectorTile(
  mapLayer,
  layerKey,
  layerValue,
  dataDto,
  row = null,
  lineBase = false,
  zIndex = null,
  minZoom = 6,
  tileSize = 256,
  showText = true
) {
  let url = `${process.env.VUE_APP_BASE_API}/oneMap/layerData/3857/${tileSize}/{z}/{x}/{y}.pbf`;
  const style = baseStyle.clone();

  let vectorTileSource = new VectorTileSource({
    tileSize,
    format: new MVT(),
    url: url,
    tileGrid: createXYZ({
      extent: getProj('EPSG:3857').getExtent(),
      maxZoom: 20,
    }),
    wrapX: false,
    tilePixelRatio: 2,
    projection: 'EPSG:3857',
    tileLoadFunction: function (tile, url) {
      tile.setLoader(function (extent, resolution, projection) {
        fetch(url, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            Authorization: 'Bearer ' + getToken(),
          },
          body: dataDto,
        }).then(function (response) {
          response.arrayBuffer().then(function (data) {
            const format = tile.getFormat(); // ol/format/MVT configured as source format
            const features = format.readFeatures(data, {
              extent: extent,
              featureProjection: projection,
            });
            // console.log("切片数据", features);
            if (!lineBase) {
            }
            tile.setFeatures(features);
          });
        });
      });
    },
    tileUrlFunction: function (tileCoord) {
      console.log(tileCoord);
    },
  });
  minZoom =
    mapObj.legendList && mapObj.legendList.length
      ? mapObj.showLevel
      : row && row.showLevel
      ? row.showLevel
      : minZoom;
  let vLayer = new VectorTileLayer({
    maxZoom: 25,
    minZoom,
    source: vectorTileSource,
    declutter: lineBase ? false : true,
    style: (feature, resolution) => {
      const zoom = Math.floor(
        mapLayer.getView().getZoomForResolution(resolution)
      );
      const featureData = feature.getProperties();
      const geometry = feature.getGeometry();
      const featureType = geometry.getType();
      style.getText().setBackgroundFill(
        new Fill({
          color: 'rgba(0,0,0,0)',
        })
      );
      // 如果是面或者是线
      if (
        featureData &&
        (featureData.shapeType == 2 ||
          featureData.shapeType == 3 ||
          (featureType != 'Point' && featureType != 'MultiPoint'))
      ) {
        // 边框颜色
        style.getStroke().setColor(row.borderColour);
        // 填充颜色
        style.getFill().setColor(row.interiorColour);
        // 设置边框粗细
        let borderW = row.borderWidth || 2;
        style.getStroke().setWidth(borderW);
        let isRandom = false;
        // 路线
        if (
          (featureData.shapeType == 2 ||
            (featureType != 'Point' && featureType != 'MultiPoint')) &&
          lineBase
        ) {
          style.getText().setFill(
            new Fill({
              color: 'rgba(255,255,255, 1)', // 文本颜色
            })
          );
          style.getText().setStroke(
            new Stroke({
              color: 'rgba(0,135,46, 1)', // 本颜色
              width: 6,
            })
          );
          style.getText().setPlacement('line');
          style.getText().setRepeat(8000);
          style.getText().setOffsetY(0);
          style.getText().setRotateWithView(true);
          style.setZIndex(0);
          isRandom = true;
        }
        let typeName = featureData.typeName || featureData.typename || '';
        const text = zoom >= row.textShowLevel || 6 ? `${typeName}` : '';

        if (isRandom && zoom < 10 && zoom >= 7) {
          style.getText().setOverflow(true);
          if (zoom < 9) {
            style.getText().setPlacement('point');
          }
          let randomArr = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12];
          for (let index = 0; index < 150; index++) {
            randomArr[index] = index + 1;
          }
          const randomIndex =
            randomArr[Math.floor(Math.random() * randomArr.length)]; // 生成一个随机索引
          if (randomIndex == 10 || randomIndex == 60 || randomIndex == 100) {
            if (showText) {
              style.getText().setText(text);
            }
          } else {
            style.getText().setText('');
          }
        } else {
          style.getText().setOverflow(false);
          style.getText().setPlacement('line');
          if (showText) {
            style.getText().setText(text);
          }
        }
      } else {
        let scale = 1;
        if (zoom > mapObj.showLevel + 1) scale = 0.7;
        else scale = 0.65;
        let typeName = featureData.typeName || featureData.typename || '';
        if (typeName) {
          let textShowLevel =
            mapObj.legendList && mapObj.legendList.length
              ? mapObj.legendList[0].textShowLevel
              : minZoom;
          const text = zoom >= textShowLevel ? `${typeName}` : '';
          style.getText().setText(text);
        } else {
          style.getText().setText('');
        }
        let icon = '';
        if (row && row.icon) {
          icon = row.icon;
        } else if (mapObj.legendList) {
          icon = mapObj.legendList[0]?.icon;
        }
        if (icon) {
          // 聚合样式
          if (!isNaN(Number(typeName)) && featureData.ifAgg) {
            style.getText().setOffsetY(0);
            style.getText().setFill(
              new Fill({
                color: 'rgba(255,255,255, 1)', // 文本颜色
              })
            );
            style.setImage(
              new CircleStyle({
                anchor: [15, 15],
                radius: typeName - 0 > 99 ? 18 : 14,
                stroke: new Stroke({
                  color: 'rgba(255,255,255,0.4)',
                  width: 1,
                }),
                fill: new Fill({
                  color: 'rgb(64,158,255)',
                }),
              })
            );
          } else {
            style.getText().setOffsetY(-25);
            style.getText().setFill(
              new Fill({
                color: 'rgba(255,255,255, 0.8)', // 文本颜色
              })
            );
            style.getText().setStroke(
              new Stroke({
                color: 'rgba(255,255,255, 0)', // 描边
              })
            );
            // style.getText().setBackgroundFill(
            //   new Fill({
            //     color: "rgba(13,27,43,0.6)",
            //   })
            // );
            style.getText().setText('');
            let index = 8;
            let newStr = typeName;
            if (typeName.length > index) {
              style.getText().setOffsetY(-35);
              newStr = typeName.slice(0, index) + '\n' + typeName.slice(index);
            }
            let newIndex = 20;
            if (newStr.length > newIndex) {
              style.getText().setOffsetY(-45);
              newStr =
                newStr.slice(0, newIndex) + '\n' + newStr.slice(newIndex);
            }
            style.getText().setText(newStr);
            //统一50*54图标 或自定义
            style.setImage(
              new Icon({
                anchor: [25, 27], //中心点
                anchorXUnits: 'pixels',
                anchorYUnits: 'pixels',
                src: icon || '',
                size: [50, 54], //实际尺寸 50*54
                scale: scale, // scale
                crossOrigin: 'anonymous',
              })
            );
          }
        } else {
          style.setImage(
            new CircleStyle({
              anchor: [15, 15],
              radius: 7,
              stroke: new Stroke({
                color: row.borderColour,
                width: row.borderWidth || 1,
              }),
              fill: new Fill({
                color: row.interiorColour,
              }),
            })
          );
        }
      }
      if (zIndex) {
        style.setZIndex(9);
      } else {
        style.setZIndex(999);
      }
      return style;
    },
  });
  vLayer.set('name', layerValue);
  vLayer.set(layerKey, layerValue);
  if (zIndex) {
    vLayer.setZIndex(zIndex);
  } else {
    vLayer.setZIndex(9003);
  }

  return vLayer;
}
