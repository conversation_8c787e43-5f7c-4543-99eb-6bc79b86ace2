<template>
  <div class="unit-funds" v-loading="loading" element-loading-background="rgba(0, 0, 0, 0.4)">
    <el-row :gutter="20" style="height: 100%;">
      <el-col :span="12" :offset="0" style="height: 100%;">
        <div class="unit-funds-i">
          <Echarts :option="wgOption" v-if="wgOption" height="100%" :key="'wgKey' + loading + year" />
          <span>完工数量(件)</span>
        </div>
      </el-col>
      <el-col :span="12" :offset="0" style="height: 100%;">
        <div class="unit-funds-i">
          <Echarts :option="yhOption" v-if="yhOption" height="100%" :key="'yhKey' + loading + year" />
          <span>养护费用(万元)</span>
        </div>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import * as echarts from "echarts";
import Echarts from '../../echarts/echarts.vue';
import { isBigScreen } from "@/views/cockpit/util/utils";
// api
import { getDiseaseEventFunds, getDomainFundStatistics } from '@/api/cockpit/maintain';

export default {
  name: 'unitFunds',
  components: {
    Echarts
  },
  props: {
    year: {
      type: [String, Number],
      default: ''
    },
    type: {
      type: [String, Number],
      default: 1, // 1、日常养护专题-各管养单位资金使用情况，2、专题养护-各管养单位资金使用情况
    }
  },
  data() {
    return {
      isBig: isBigScreen(),
      loading: false,
      wgOption: null,
      yhOption: null,
    }
  },
  created() { },
  mounted() { },
  watch: {
    year: {
      async handler(newVal) {
        if (newVal) {
          this.loading = true;
          let data = await this.getData();
          let wgYData = data.map(v => v.domainName) || ['昆明东', '昆明西', '大理', '保山', '丽江', '曲靖', '昭通', '普洱', '版纳'];
          // 完成数量
          let wgData = data.map(v => v.count) || [1239, 1081, 851, 1044, 1044, 1044, 1044, 1044, 1044];
          wgData = wgData.map(v => v - 0);
          let wgSColor = 'rgb(0,164,253,0.2)';
          let wgEColor = 'rgb(0,164,253,1)';
          this.wgOption = this.initCharts(wgYData, wgData, wgSColor, wgEColor);
          
          // 养护费用
          let denominator = this.type === 1 ? 10000 : 1;
          let yhYData = data.map(v => '') || ['', '', '', '', '', '', '', '', ''];
          let yhData = data.map(v => v.sumFund ? v.sumFund / denominator : 0) || [1239, 1081, 851, 1044, 1044, 1044, 1044, 1044, 1044];
          yhData = yhData.map(v => v.toFixed(2) - 0);
          let yhSColor = 'rgb(254,234,0,0.2)';
          let yhEColor = 'rgb(254,234,0,1)';
          this.yhOption = this.initCharts(yhYData, yhData, yhSColor, yhEColor);
          this.loading = false;
        }
      },
      immediate: true,
      deep: true,
    }
  },
  methods: {
    // 获取数据
    getData() {
      return new Promise((resolve, reject) => {
        let obj = {
          1: getDiseaseEventFunds(this.year),
          2: getDomainFundStatistics(this.year),
        };
        let resonponse = obj[this.type];
        resonponse.then((res) => {
          let data = res.rows ||　res.data;
          data = data.map(item => {
            item.count = item.count || item.completedCount || 0;
            item.sumFund = item.sumFund || item.fund || 0;
            return item;
          })
          if (res.code === 200 && data) {
            resolve(data)
          } else {
            reject('获取数据失败');
          }
        }).catch((e) => {
          reject(e)
        });
      })
    },
    initCharts(yData = [], data = [], sColor = '', eColor = '') {
      var className = yData;
      // 默认背景最大值？
      let sum = data.reduce((acc, curr) => acc + curr, 0);
      var defaultData = data.map(v => sum) || [1500, 1500, 1500, 1500, 1500, 1500, 1500, 1500, 1500, 1500, 1500];
      let option = {
        backgroundColor: 'rgba(255, 255, 255,0)',
        grid: {
          left: '1%',
          right: '2%',
          bottom: '5%',
          top: '10%',
          containLabel: true
        },
        tooltip: {
          show: false,
          trigger: 'axis',
          axisPointer: {
            type: 'none',
          },
          formatter: (params) => {
            return params[0].name + '<br/>' +
              params[0].seriesName + ' : ' + params[0].value + ' '
          }
        },
        xAxis: {
          show: false,
          type: 'value'
        },
        yAxis: [{
          type: 'category',
          inverse: true,
          axisLabel: {
            show: true,
            textStyle: {
              color: '#fff'
            },
          },
          splitLine: {
            show: false
          },
          axisTick: {
            show: false
          },
          axisLine: {
            show: false
          },
          data: className
        }, {
          type: 'category',
          inverse: true,
          axisTick: 'none',
          axisLine: 'none',
          show: true,
          axisLabel: {
            textStyle: {
              color: '#ffffff',
              fontSize: this.isBig ? 24 : '12'
            },
            formatter: (value) => {
              return value;
            },
          },
          data: data
        }],
        series: [{
          name: '占比',
          type: 'bar',
          zlevel: 1,
          itemStyle: {
            normal: {
              barBorderRadius: 30,
              color: new echarts.graphic.LinearGradient(0, 0, 1, 0, [{
                offset: 0,
                color: sColor || 'rgb(57,89,255,1)'
              }, {
                offset: 1,
                color: eColor || 'rgb(46,200,207,1)'
              }]),
              // color: (params) => {
              //   return colorList[params.dataIndex]
              // }
            },
          },
          barWidth: this.isBig ? 16 : 8,
          data: data
        },
        {
          name: '背景',
          type: 'bar',
          barWidth: this.isBig ? 16 : 8,
          barGap: '-100%',
          data: defaultData,
          itemStyle: {
            normal: {
              color: '#2C3767',
              barBorderRadius: 30,
            }
          },
        },
        ]
      };
      return option;
    }
  }
}
</script>

<style lang="scss" scoped>
@import "@/assets/styles/utils.scss";

.unit-funds {
  width: 100%;
  height: 100%;
  padding: vwpx(10px) vwpx(20px);

  .unit-funds-i {
    height: 100%;
    position: relative;

    span {
      position: absolute;
      top: vwpx(10px);
      right: vwpx(20px);

      font-family: Microsoft YaHei, Microsoft YaHei;
      font-weight: 400;
      font-size: vwpx(24px);
      color: rgba(255, 255, 255, 0.8);
    }
  }
}
</style>