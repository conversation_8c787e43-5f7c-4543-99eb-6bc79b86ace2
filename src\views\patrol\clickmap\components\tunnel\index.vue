<template>
  <div class="tunnel-index">
    <!-- <div class="type">
      <el-select v-model="bridgeType" value-key="" placeholder="请选择" clearable style="width: 120px;"
        :popper-append-to-body="false">
        <el-option v-for="item in typeOptions" :key="item.value" :label="item.label" :value="item.value" />
      </el-select>
    </div> -->
    <!-- <pie-echarts name="隧道" :data="tnnelData" :color="color" height="17vh"></pie-echarts> -->
    <Echarts :option="option" v-if="option" height="17vh" key="tunnelKey" />
  </div>
</template>

<script>
import { isBigScreen } from '@/views/map/components/common/util';
import PieEcharts from '../echarts/pieEcharts.vue';
import Echarts from '../echarts/echarts.vue';
export default {
  components: {
    PieEcharts,
    Echarts,
  },
  data() {
    return {
      option: null,
      bridgeType: '',
      typeOptions: [
        {
          value: 1,
          label: '特长隧道'
        },
        {
          value: 2,
          label: '长隧道'
        },
        {
          value: 3,
          label: '中隧道'
        },
        {
          value: 4,
          label: '短隧道'
        }
      ],
      tnnelData: [
        {
          name: "长隧道",
          value: 3000,
        },
        {
          name: "特长隧道",
          value: 870,
        },
        {
          name: "中隧道",
          value: 5502,
        },
        {
          name: "短隧道",
          value: 6789,
        },
      ],
      color: [
        "#8B34FF",
        "#FFE000",
        "#FFA800",
        "#FF5B00",
        "#ffa800",
      ],
      isBig: isBigScreen(),
    }
  },
  created () {
    var outr = '80%';
    var inr = '77%';
    var size = 15;
    var numberdata = [3000, 870, 5502, 6789];
    var titledata = ['特长隧道', '长隧道', '中隧道', '短隧道'];
    let color = this.color || [
      "#00ffff",
      "#00cfff",
      "#006ced",
      "#518BFF",
      "#ffa800",
      "#ff5b00",
      "#ff3000",
    ];
    let title = '16170';
    this.option = this.initPieCharts(outr, inr, size, numberdata, titledata, color, title);
  },
  methods: {
    initPieCharts(outr = 135, inr = 125, size = 13, numberdata = [], titledata = [], color = [], title = '') {
      var total = 0;
      //计算总和
      for (var i = 0; i < numberdata.length; i++) {
        total += Number(numberdata[i]);
      }
      let placeHolderStyle = {
        normal: {
          label: {
            show: false,
          },
          labelLine: {
            show: false,
          },
          color: 'rgba(0, 0, 0, 0)',
          borderColor: 'rgba(0, 0, 0, 0)',
          borderWidth: 0,
        },
      };

      var data = [];
      for (var i = 0; i < numberdata.length; i++) {
        data.push(
          {
            value: numberdata[i],
            name: titledata[i],
            itemStyle: {
              normal: {
                borderWidth: 5,
                shadowBlur: 10,
                borderColor: color[i],
                shadowColor: color[i],
                color: color[i],
              },
            },
          },
          {
            value: total / 30,
            name: '',
            itemStyle: placeHolderStyle,
          }
        );
      }

      var seriesObj = [
        {
          name: '',
          type: 'pie',
          clockWise: false,
          startAngle: '90',
          center: ['50%', '50%'], //此处调整圆环的位置
          radius: [outr, inr], //此处可以调整圆环的大小
          hoverAnimation: false,
          itemStyle: {
            normal: {
              label: {
                show: true,
                position: "outside",
                color: "#ddd",
                formatter: function (params) {
                  var percent = 0;
                  var total = 1;
                  for (var i = 0; i < data.length; i++) {
                    total += data[i].value;
                  }
                  percent = ((params.value / total) * 100).toFixed(2);
                  if (params.name !== "") {
                    return (
                      params.name + `：${params.value}` +
                      "\n" +
                      "占比：" +
                      percent +
                      "%"
                    );
                  } else {
                    return "";
                  }
                },
                fontSize: this.isBig ? 25 : 12,
              },
              labelLine: {
                length: this.isBig ? 30 : 15,
                length2: this.isBig ? 80 : 30,
                show: true,
                color: "#00ffff",
              },
            },
          },
          data: data,
          animationType: 'scale',
          animationEasing: 'elasticOut',
          animationDelay: function (idx) {
            return idx * 50;
          },
        },
      ];

      let option = {
        backgroundColor: 'rgba(0,0,0,0)',
        tooltip: {
          show: false,
        },
        title: {
          text: 16170 || `${total}`,
          subtext: '隧道',
          top: "40%",
          textAlign: "center",
          left: "49%",
          textStyle: {
            color: "#fff",
            fontSize: this.isBig ? 32 : 16,
            fontWeight: "700",
            shadowColor: 'rgba(27,126,242,0.8)',
            shadowBlur: 10,
            shadowOffsetX: 5,
            shadowOffsetY: 5,
          },
          subtextStyle: {
            fontSize: this.isBig ? 32 : 16,
            align: "center",
            color: '#fff'
          },
        },
        toolbox: {
          show: false,
        },
        series: seriesObj,
      };
      return option;
    },
  },
}
</script>

<style lang="scss" scoped>
.tunnel-index {
  padding: 8px;
  color: white;
  position: relative;

  .type {
    position: absolute;
    top: 10px;
    right: 10px;
    z-index: 99;
  }

  ::v-deep .el-input {
    .el-input__inner {
      background-color: rgba(1, 102, 254, 0.2);
      border: 1px solid #0166FE;
      color: #ffffff;
      // border-right: none;
    }

    .el-input__inner::placeholder {
      color: #BBBBBB;
    }

    .el-input-group__append {
      background-color: rgba(1, 102, 254, 0.2);
      border: 1px solid #0166FE;
      color: #ffffff;
      border-left: none;
      padding: 0 10px;
      cursor: pointer;
    }
  }
}
</style>