<template>
  <div class="container">
    <el-form :model="form" ref="form" :rules="rules" label-width="80px" :inline="false">
      <el-form-item label="管理处">
        <el-select v-model="form.managementMaintenanceId" @change="handleDeptChange" @clear="handleDeptClear"
          placeholder="请选择" clearable filterable style="width: 100%;" multiple>
          <el-option v-for="item in deptOptions" :key="item.id" :label="item.label" :value="item.id" />
        </el-select>
      </el-form-item>
      <el-form-item label="养护路段">
        <el-select v-model="form.maintenanceSectionName" @change="handleRouteChange" @clear="handleRouteClear"
          placeholder="请选择" clearable filterable style="width: 100%;">
          <el-option v-for="item in routeOptions" :key="item.maintenanceSectionId" :label="item.maintenanceSectionName"
            :value="item.maintenanceSectionId" />
        </el-select>
      </el-form-item>
      <el-form-item label="路线编码">
        <el-select v-model="form.lxbh" placeholder="请选择" clearable filterable style="width: 100%;" multiple>
          <el-option v-for="(item, index) in codeOptions" :key="'route' + index" :label="item.routeName"
            :value="item.routeCode" />
        </el-select>
      </el-form-item>
    </el-form>
    <div class="btn-list">
      <el-button type="primary" @click="onReveal" size="mini">显示</el-button>
      <el-button type="primary" @click="onClear" size="mini">清除</el-button>
      <el-button type="primary" @click="onQuit" size="mini" class="hollow-out">退出</el-button>
    </div>
  </div>
</template>

<script>
import { deptTreeSelect } from '@/api/tmpl'
import {
  listByMaintenanceSectionId,
  getMaintenanceSectionListAll
} from '@/api/baseData/common/routeLine'
import { listAllRoute } from '@/api/system/route'
import { getDictData, getDataList } from '@/api/oneMap/layerData'
import { removeLayer } from '@/views/map/components/common/mapFun';
import { mapState } from 'vuex'

export default {
  inject: ['oneMap', 'instance'],
  data() {
    return {
      form: {
        managementMaintenanceId: [],
        maintenanceSectionName: '',
        lxbh: []
      },
      rules: {},
      routeOptions: [],
      deptOptions: [],
      codeOptions: [],
    }
  },
  computed: {
    ...mapState({
      authDeptIds: state => state.map.authDeptIds,
    }),
  },
  created() {
    this.getDept();
    this.getRoutes();
    this.getCodes();
  },
  methods: {
    // 获取管理处
    getDept() {
      deptTreeSelect({ types: 201 }).then(res => {
        if (res.code === 200) {
          this.deptOptions = res.data || [];
        }
      })
    },
    // 获取养护路段数据
    getRoutes(e) {
      this.$modal.loading();
      getMaintenanceSectionListAll({ departmentIdList: e }).then(res => {
        if (res.code == 200) {
          this.routeOptions = res.data || []
        }
      }).finally(() => {
        this.$modal.closeLoading();
      });
    },
    // 获取路线编码
    getCodes(e) {
      if (!e) {
        this.$modal.loading();
        listAllRoute().then(res => {
          if (res.code == 200) {
            this.codeOptions = res.data || []
          }
        }).finally(() => {
          this.$modal.closeLoading();
        });
        return
      }
      this.$modal.loading();
      listByMaintenanceSectionId({ maintenanceSectionId: e }).then(res => {
        if (res.code == 200) {
          let arr = res.data.filter(v => v)
          this.codeOptions = arr || [];
        }
      }).finally(() => {
        this.$modal.closeLoading();
      });
    },
    // 管养出监听选择变化
    handleDeptChange(e) {
      if (e) {
        this.form.maintenanceSectionName = ''
        this.form.lxbh = '';
        this.getRoutes(e);
      }
    },
    // 清空管养出
    handleDeptClear(e) {
      this.form.maintenanceSectionName = '';
      this.getRoutes();
      this.form.lxbh = '';
      this.getCodes();
    },
    // 监听路线选择变换
    handleRouteChange(e) {
      if (e) {
        this.form.lxbh = '';
        this.getCodes(e)
      }
    },
    // 清空 路线
    handleRouteClear() {
      this.form.lxbh = '';
      this.getCodes();
    },
    // 显示
    onReveal() {
      // if (!this.form.managementMaintenanceId) {
      //   this.$modal.msgWarning("请选择管理处");
      //   return;
      // } else if (!this.form.maintenanceSectionName) {
      //   this.$modal.msgWarning("请选择养护路段");
      //   return;
      // } else 
      // if (!this.form.lxbh) {
      //   this.$modal.msgWarning("请选择路线编码");
      //   return;
      // }
      // 路线表格
      let obj = {
        id: '1824259894327382017',
        managementMaintenanceIds: this.authDeptIds || [],
        paramsDTO: {
          precisionParams: {
            lxbh: this.form.lxbh,
            management_maintenance_id: this.form.managementMaintenanceId,
            maintenance_section_name: this.form.maintenanceSectionName,
          }
        },
        pageNum: 1,
        pageSize: 10
      }
      this.instance.tableData = obj
      this.instance.tableTitle = this.form.maintenanceSectionName || ''
      this.instance.showRouteTable = true

      return
      this.$modal.loading();
      getDataList(obj).then(res => {
        if (res.code === 200) {
          this.instance.tableData = res.rows
          this.instance.tableTitle = this.form.maintenanceSectionName || '养护路段'
          this.instance.showRouteTable = true
        }
        if (!res.rows || (res.rows && !res.rows.length)) {
          this.$modal.msgError("暂无数据");
        }
      }).finally(() => {
        this.$modal.closeLoading();
      })
    },
    // 清除
    onClear() {
      this.$modal.confirm('确认要清除吗？').then(() => {
        // 1、清空条件
        Object.keys(this.form).forEach(key => {
          this.form[key] = ''
        })
        // 2、 关闭表格弹窗
        this.instance.showRouteTable = false;
        // 3、清除地图渲染数据
        removeLayer(window.mapLayer, 'layerId')
        removeLayer(window.mapLayer, 'clickLayer')
      }).then(() => {

      }).catch(() => { });
    },
    // 退出
    onQuit() {
      // 1、 关闭表格弹窗
      this.instance.showRouteTable = false;
      // 2、清除地图渲染数据
      removeLayer(window.mapLayer, 'layerId')
      removeLayer(window.mapLayer, 'clickLayer')
      this.$emit('quit')
    },
  }
}
</script>

<style lang="scss" scoped>
.container {
  z-index: 9999;
  margin: 10px 0;
  color: #ffffff;

  ::v-deep .el-form {
    .el-form-item__label {
      color: #ffffff;
    }
  }

  ::v-deep .el-input {
    .el-input__inner {
      background-color: rgba(1, 102, 254, 0.2);
      border: 1px solid #0166FE;
      color: #ffffff;
    }

    .el-input__inner::placeholder {
      color: #BBBBBB;
    }

    .el-input-group__append {
      background-color: rgba(1, 102, 254, 0.2);
      border: 1px solid #0166FE;
      color: #ffffff;
      border-left: none;
      padding: 0 10px;
      cursor: pointer;
    }
  }

  .btn-list {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    margin-top: 30px;

    .hollow-out {
      background-color: rgba(1, 102, 254, 0.2);
      color: #ffffff;
    }
  }

  ::v-deep .el-select__tags {
    flex-wrap: nowrap;
    overflow: auto;
  }
}
</style>
