<template>
  <div class="header" ref="hRef" :style="{ position: fixed, top: '0', left: '0' }">
    <div class="header-main" :class="theme === 'dark' ? 'h-bg-dark' : 'h-bg-light'">
      <div class="current-time" v-if="showT" :style="{ fontSize: isBig ? '0.44vw' : '0.85vw' }">{{ time }}</div>
      <div class="cockpit-title" :class="'set-family'"
        :style="{ justifyContent: 'center', marginLeft: isBig ? '30px' : '', fontSize: isBig ? '1vw' : '1.7vw' }">
        <span class="title-n" :style="{ maxWidth: isBig ? '20vw' : '' }" :title="title">{{ title }}</span>
        <slot></slot>
      </div>
      <div class="btn">
        <slot name="btn">
        </slot>
      </div>
    </div>
  </div>
</template>

<script>
import { isBigScreen } from '../utils/utils.js';

export default {
  name: 'Header',
  props: {
    title: {
      type: String,
      default: '健康监测大屏'
    },
    fixed: {
      type: String,
      default: 'fixed',
    },
    theme: {
      type: String,
      default: 'light',
    },
    showT: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      time: '',
      isBig: isBigScreen(),
      year: new Date().getFullYear(),
      timer: null,
      years: [],
      params:{},
    }
  },
  created() {
    this.getYear();
    // 获取当前是星期几
    const date = new Date();
    const week = ['星期日', '星期一', '星期二', '星期三', '星期四', '星期五', '星期六'];
    const dayOfWeek = week[date.getDay()];
    // 时时获取当前时间 年月日时分秒
    this.timer = setInterval(() => {
      let date = new Date();
      const year = date.getFullYear();
      const month = date.getMonth() + 1;
      const day = date.getDate();

      const hours = date.getHours();
      let minutes = date.getMinutes();
      let seconds = date.getSeconds();
      // 判断分 秒是否大于10
      if (minutes < 10) {
        minutes = '0' + minutes;
      }
      if (seconds < 10) {
        seconds = '0' + seconds;
      }
      this.time = `${year}年${month}月${day}日 ${dayOfWeek} ${hours}:${minutes}:${seconds}`;
    }, 1000);
  },
  computed: {},
  mounted() {
  },
  methods: {
    getYear() {
      let date = new Date();
      let year = date.getFullYear();
      this.year = year;
      for (let i = year - 10; i < year + 1; i++) {
        this.years.push(i);
      }
    },
  },
  unmounted() {
    if (this.timer) {
      clearInterval(this.timer)
      this.timer = null;
    }
  },
}
</script>

<style lang="scss" scoped>
@import "@/assets/styles/utils.scss";

@font-face {
  font-family: 'YouSheBiaoTiHei';
  /* 自定义的字体名称 */
  src: url('~@/assets/home/<USER>') format('truetype');
  /* 字体文件路径和格式 */
  /* 可选属性，根据需要设置 */
  font-weight: normal;
  font-style: normal;
}

.header {
  height: vwpx(160px);
  top: 0;
  left: 0;
  right: 0;
  z-index: 2;

  .h-bg-dark {
    background-image: url('~@/assets/monitoringSystem/header-bg-dark.png');
    background-repeat: no-repeat;
    background-size: 100% 100%;
  }

  .h-bg-light {
    background-image: url('~@/assets/monitoringSystem/header-bg-light.png');
    background-repeat: no-repeat;
    background-size: 100% 100%;
  }

  .header-main {
    height: 100%;
    width: 100%;
    position: relative;

    .current-time {
      position: absolute;
      left: vwpx(40px);
      top: 60%;
      transform: translateY(-50%);
      color: #fff;
    }

    .set-family {
      font-family: YouSheBiaoTiHei;
      background: linear-gradient(180deg, #FFFFFF 0%, #FFFFFF 60%, #20A9FF 100%);
      background-clip: text;
      -webkit-background-clip: text;
      color: transparent;
    }

    .cockpit-title {
      font-family: YouSheBiaoTiHei;
      font-weight: 400;
      min-width: vwpx(550px);
      letter-spacing: vwpx(8px);
      text-align: center;
      line-height: vwpx(160px);
      color: #fff;

      display: flex;
      align-items: center;
      justify-content: flex-start;

      .title-n {
        display: block;
        text-align: left;
        color: #fff;
        // 超出部分隐藏
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }

    .btn {
      position: absolute;
      right: vwpx(10px);
      top: 60%;
      transform: translateY(-50%);
      cursor: pointer;

      display: flex;
      align-items: center;
    }
  }

}
</style>