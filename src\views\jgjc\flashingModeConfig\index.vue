<template>
  <div class='app-container maindiv'>
    <el-row :gutter='20'>
      <el-tabs v-model='activeIndex'>
        <el-tab-pane :name='1' label='中交国通'></el-tab-pane>
        <el-tab-pane :name='2' label='维的美'></el-tab-pane>
        <el-tab-pane :name='3' label='三思'></el-tab-pane>
        <el-tab-pane :name='4' label='第三方'></el-tab-pane>
      </el-tabs>
    </el-row>
    <detail1 v-if='activeIndex == 1'/>
    <detail2 v-if='activeIndex == 2'/>
    <detail3 v-if='activeIndex == 3'/>
    <detail4 v-if='activeIndex == 4'/>

  </div>
</template>

<script>
import Detail1 from './detail1.vue'
import Detail2 from './detail2.vue'
import Detail3 from './detail3.vue'
import Detail4 from './detail4.vue'

export default {
  components: { Detail1, Detail2, Detail3, Detail4 },
  data() {
    return {
      activeIndex: 1,
    }
  },
}
</script>

<style lang='scss' scoped></style>
