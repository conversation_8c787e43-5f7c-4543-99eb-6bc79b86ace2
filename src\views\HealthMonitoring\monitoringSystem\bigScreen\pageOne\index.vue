<template>
  <div class="page-one" :class="isBig ? 'no-margin' : 'has-margin'">
    <Left />
    <Mid />
    <Right />
  </div>
</template>

<script>
import { isBigScreen } from '../../utils/utils.js';
import Left from "./left/index.vue"
import Mid from "./mid/index.vue"
import Right from "./right/index.vue"

export default {
  name: 'pageOne',
  props: {},
  components: { Left, Mid, Right },
  data() {
    return {
      isBig: isBigScreen(),
    }
  },
  created() { },
  methods: {
  },
  computed: {},
  watch: {},
}
</script>

<style lang="scss" scoped>
@import "@/assets/styles/utils.scss";

.page-one {
  touch-action: none;
  user-select: none;
  display: flex;
  justify-content: space-around;
  width: 100%;
  height: 92vh;
  position: relative;
  background: url("~@/assets/monitoringSystem/background.png") no-repeat;
  background-size: 100% 100%;
}

.has-margin {
  margin-top: vwpx(160px);
}

.no-margin {
  margin-top: 0;
}
</style>