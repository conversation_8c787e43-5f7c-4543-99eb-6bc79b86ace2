<template>
  <div class="health-monitoring">
    <div v-for="(item, index) in list" :key="index" class="list">
      <div class="list-item">
        <span class="name">{{ item.name }}</span>
        <span class="value">{{ item.value }} <small>{{ item.unit }}</small></span>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'HealthMonitoring',
  data() {
    return {
      list: [
        {
          name: '特大桥',
          value: 63,
          unit: '座'
        },
        {
          name: '大桥',
          value: 45,
          unit: '座'
        },
        {
          name: '特长隧道',
          value: 2,
          unit: '座'
        },
        {
          name: '长隧道',
          value: 1,
          unit: '座'
        },
        {
          name: '边坡',
          value: 7,
          unit: '座'
        },
      ]
    }
  },
  methods: {
  },
  components: {
  },
  mounted() {
  }
}
</script>

<style lang="scss" scoped>
@import "@/assets/styles/utils.scss";

.health-monitoring {
  padding: 8px;
  color: #ffffff;
  height: 100%;
  overflow-y: auto;

  display: flex;
  flex-wrap: wrap;

  .list {
    width: 31%;
    height: vwpx(135px);
    background: rgba(0,0,0,0.1);
    box-shadow: inset 0px 0px 10px 0px #0065FF;
    border-radius: 6px;
    border: 1px solid #20A9FF;
    margin: 2% 1.1%;



    .list-item {
      width: 100%;
      height: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
      flex-direction: column;

      .name {
        font-family: Microsoft YaHei UI, Source Han Sans;
        font-weight: 700;
        font-size: vwpx(30px);
        color: #FFFFFF;
        text-align: center;
        font-style: normal;
        text-transform: none;
      }

      .value {
        font-family: Microsoft YaHei UI, Microsoft YaHei UI;
        font-weight: 700;
        font-size: vwpx(40px);
        color: #42ABFF;
        text-align: center;
        font-style: normal;
        text-transform: none;

        small {
          font-weight: 400;
          font-size: vwpx(24px);
          color: rgba(255, 255, 255, 0.5);
          text-shadow: 0px 0px 10px rgba(27, 126, 242, 0.8);
          text-align: center;
          font-style: normal;
          text-transform: none;
        }
      }
    }
  }
}
</style>