<template>
  <el-drawer :wrapperClosable="false" title="考核任务" :visible.sync="thisVisible" size="90%" destroy-on-close
             @close="onDialogClose" :before-close="handleClose">
    <div class="examine-task-container">
      <el-collapse v-model="activeNames">
        <el-collapse-item title="考核任务信息" name="1">
          <el-row>
            <el-descriptions class="margin-top" :column="3" size="medium" border :label-style="labelStyle">
              <el-descriptions-item>
                <template slot="label">
                  任务名称
                </template>
                <div class="uniform-width">
                  {{ resultData.task.taskName }}
                </div>
              </el-descriptions-item>
              <el-descriptions-item>
                <template slot="label">
                  管养单位
                </template>
                <div class="uniform-width">
                  {{ resultData.task.deptName }}
                </div>
              </el-descriptions-item>
              <el-descriptions-item>
                <template slot="label">
                  任务状态
                </template>
                <div class="uniform-width">
                  {{
                    resultData.task.taskStatus === '3' ? '已评分' : (resultData.task.taskStatus === '2' ? '附件已上传' : '附件未上传')
                  }}
                </div>
              </el-descriptions-item>
              <el-descriptions-item>
                <template slot="label">
                  下发单位
                </template>
                {{ resultData.task.createUnit }}
              </el-descriptions-item>
              <el-descriptions-item>
                <template slot="label">
                  下发时间
                </template>
                {{ resultData.task.createTime }}
              </el-descriptions-item>
              <el-descriptions-item>
                <template slot="label">
                  要求完成时间
                </template>
                {{ resultData.task.deadline }}
              </el-descriptions-item>
              <el-descriptions-item>
                <template slot="label">
                  考核文件
                </template>
                <FileUpload
                  previewWidth="80%"
                  v-model="resultData.task.fileId"
                  :limit="1"
                  :forView="true"
                  :showTitle="true"
                  :isShowTip="false"
                  storage-path="/examine/project/"
                  platform="fykj"
                  :owner-id="resultData.task.fileId"
                  v-if="resultData.task.fileId"
                  @input="(value) => (resultData.task.fileId = value[0])"
                >
                </FileUpload>
              </el-descriptions-item>
              <el-descriptions-item>
                <template slot="label">
                  备注信息
                </template>
                {{ resultData.task.remarks }}
              </el-descriptions-item>
            </el-descriptions>
          </el-row>
        </el-collapse-item>
        <el-collapse-item title="考核内容" name="2">
          <el-row>
            <el-tabs type="border-card" v-model="activeTab">
              <el-tab-pane v-for="schema in resultData.schemaList" :label="schema.schemaName" :name="schema.schemaName"
                           :key="schema.schemaId">
                <el-table ref="table" v-if="schema.detailList && schema.detailList.length > 0" :data="schema.detailList"
                          style="width: 100%;"
                          :height="isView ? 'calc(100vh - 280px)' : 'calc(100vh - 330px)'" class="examine-detail"
                          :span-method="spanMethod">
                  <el-table-column prop="l2Name" label="二级指标" width="100" :group-by="'secondName'"
                                   show-overflow-tooltip></el-table-column>
                  <el-table-column prop="l2Weight" label="权值" width="60" align="center"></el-table-column>
                  <el-table-column prop="l3Name" v-if="schema.hasL3" label="三级指标" width="100"
                                   show-overflow-tooltip></el-table-column>
                  <el-table-column prop="l3Weight" v-if="schema.hasL3" label="权值" width="60"
                                   align="center"></el-table-column>
                  <el-table-column prop="evaluationItems" label="评价项目" width="150" align="left"
                                   show-overflow-tooltip></el-table-column>
                  <el-table-column prop="standard" label="评分标准" width="150" align="left"
                                   show-overflow-tooltip></el-table-column>
                  <el-table-column prop="score" label="分值" width="60" align="center"></el-table-column>
                  <el-table-column label="评分细化" align="left" width="2000">
                    <template slot-scope="scope">
                      <span v-html="scope.row.detailedEvaluation"></span>
                    </template>
                  </el-table-column>

                  <el-table-column label="检查情况" align="center" :width="checkSituationWidth" fixed="right"
                                   header-align="center">
                    <el-table-column
                      prop="imgId"
                      label="扣分文件"
                      align="center"
                      width="150"
                      :show-overflow-tooltip="false"
                      v-if="resultData.task.taskStatus!='1'">
                      <template slot-scope="scope">
                        <FileUpload4Examine
                          v-model="scope.row.imgId"
                          previewWidth="80%"
                          :forView="isView || resultData.task.taskStatus==='1'"
                          :fileType="['docx', 'pdf', 'xlsx', 'png', 'jpg', 'jpeg']"
                          :showTitle="true"
                          storage-path="/examine/task/result/"
                          platform="fykj"
                          :owner-id="scope.row.imgId"
                          v-if="scope.row.detailId"
                          @input="(value) => (scope.row.imgId = value[0])"
                          @update:ownerId="handleOwnerIdImgUpdate"
                        >
                        </FileUpload4Examine>
                      </template>
                    </el-table-column>
                    <el-table-column
                      prop="deductProject"
                      label="扣分项"
                      width="150"
                      show-overflow-tooltip
                      v-if="resultData.task.taskStatus!='1'">
                      <template slot-scope="scope">
                        <span v-if="isView">{{ scope.row.deductProject }}</span>
                        <el-input type="textarea" v-if="!isView && resultData.task.taskStatus==='2' && scope.row.detailId"
                                  v-model="scope.row.deductProject" :rows="2" placeholder="请输入扣分项"></el-input>
                      </template>
                    </el-table-column>
                    <el-table-column
                      prop="deductScore"
                      label="扣分值"
                      align="center"
                      width="80"
                      v-if="resultData.task.taskStatus!='1'">
                      <template slot-scope="scope">
                        <span v-if="isView">{{ scope.row.deductScore }}</span>
                        <el-input placeholder="请输入扣分值"
                                  v-if="!isView && resultData.task.taskStatus==='2' && scope.row.detailId"
                                  v-model="scope.row.deductScore" @keyup.native.prevent="handleDeductSoreInput(scope.row)"
                                  @change="calcScore(scope.row)"></el-input>
                      </template>
                    </el-table-column>
                    <el-table-column
                      prop="fileId"
                      label="附件"
                      align="center"
                      width="150"
                      :show-overflow-tooltip="false">
                      <template slot-scope="scope">
                        <FileUpload4Examine
                          v-model="scope.row.fileId"
                          previewWidth="80%"
                          :forView="isView || resultData.task.taskStatus==='2'"
                          :fileType="['docx', 'pdf', 'zip', 'rar']"
                          :showTitle="true"
                          storage-path="/examine/task/result/"
                          platform="fykj"
                          :owner-id="scope.row.fileId"
                          v-if="scope.row.detailId"
                          @input="(value) => (scope.row.fileId = value[0])"
                          @update:ownerId="handleOwnerIdUpdate"
                        >
                        </FileUpload4Examine>
                      </template>
                    </el-table-column>
                  </el-table-column>

                  <el-table-column prop="standardScore" label="评分标准得分" width="60" align="center" fixed="right"
                                   v-if="resultData.task.taskStatus!='1'"></el-table-column>

                  <el-table-column prop="l2Score" label="得分" width="60" align="center" fixed="right"
                                   v-if="resultData.task.taskStatus!='1'"></el-table-column>
                </el-table>
              </el-tab-pane>
            </el-tabs>
          </el-row>
        </el-collapse-item>
      </el-collapse>

      <div slot="footer" style="text-align: right; padding-top: 20px" v-if="!isView">
        <el-button @click="visible=false">取 消</el-button>
        <el-button type="primary" @click="handleSave(false)">保 存</el-button>
        <el-button type="success" @click="handleSave(true)">保存并提交</el-button>
      </div>
    </div>
  </el-drawer>
</template>

<script>
import FileUpload4Examine from './upload/FileUpload4Examine.vue';

export default {
  name: 'evaluation_dialog',
  components: {
    FileUpload4Examine,
  },
  props: {
    resultData: {
      type: Object,
      default: {task: {}, schemaList: []}
    },
    visible: {
      type: Boolean,
      default: false
    },
    onSaveBtnClick: {
      type: Function,
      default: (resultData) => {
      }
    },
    onDialogClose: {
      type: Function,
      default: () => {
        alert("对话框关闭回调函数必须由调用方提供！")
      }
    },
    isView: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      labelStyle: {
        'background-color': '#f5f7fa',
        width: '120px',
        color: '#606266 !important',
        fontWeight: '500',
        'text-align': 'center'
      },
      dsGroups: {},   // 存储每个 schemaName 对应的合并单元格信息
      schemaMap: {},   // 存储每个 schemaName 对应的 schema 详细信息
      dsMap: {},  // 存储每个 schemaId 对应的 detailList
      activeTab: '',
      thisVisible: false,
      activeNames: ['2']
    }
  },
  computed: {
    showDeductColumns() {
      return this.resultData.task.taskStatus !== '1'
    },
    showScoreColumns() {
      return this.resultData.task.taskStatus !== '1'
    },
    checkSituationWidth() {
      let width = 150 // 附件列
      if (this.showDeductColumns) {
        width += 150 // 扣分文件
        width += 150 // 扣分项
        width += 80  // 扣分值
      }
      return width
    }
  },
  watch: {
    resultData(newVal, oldVal) {
      // 添加安全检查，确保 newVal 存在且包含有效的 schemaList
      if (!newVal || !newVal.schemaList || newVal.schemaList.length === 0) {
        return;
      }
      // 设置默认激活的标签页为 schemaList 中的第一个 schema 的名称
      this.activeTab = newVal.schemaList[0]?.schemaName || '';
      this.calcScore();
    },
    visible(newVal, oldVal) {
      this.thisVisible = newVal;
    }
  },
  created() {
  },
  methods: {
    calcScore() {
      const currentSchema = this.resultData.schemaList.find(
        (schema) => schema.schemaName === this.activeTab
      );
      if (!currentSchema) return;

      const data = currentSchema.detailList;
      let itemScores = {};  // 存储每个 evaluationItems 的总分
      let totalScore = 0;   // 用于计算所有 standardScore 的总和

      data.forEach((row) => {

        // 计算 standardScore (分值 - 扣分值，且不小于 0)
        row.standardScore = parseFloat(row.score) - parseFloat(row.deductScore);

        // 累加所有 standardScore
        totalScore += row.standardScore;

        // 计算 evaluationItemsScore
        if (!itemScores[row.evaluationItems]) {
          itemScores[row.evaluationItems] = 0;
        }
        itemScores[row.evaluationItems] += row.standardScore;
      });

      // 更新 evaluationItemsScore
      data.forEach((row) => {
        row.evaluationItemsScore = itemScores[row.evaluationItems] || 0;
        row.l2Score = totalScore;
      });
    },
    handleDeductSoreInput(row) {
      row.deductScore = row.deductScore.replace(/[^\d.]/g, '');
      if (parseFloat(row.deductScore) > parseFloat(row.score)) {
        row.deductScore = row.score
      }
    },
    handleClose(done) {
      if (!this.isView) {
        this.$props.onSaveBtnClick(this.$props.resultData, false);
      }
      done();
    },
    handleSave(submit) {
      this.$props.onSaveBtnClick(this.$props.resultData, submit);
    },
    spanMethod({row, column, rowIndex, columnIndex}) {
      // 需要合并的列
      const mergeColumns = [
        'l2Name', 'l2Weight',
        'l3Name', 'l3Weight',
        'evaluationItems', 'standard',
        'l2Score',
        'evaluationItemsScore'
      ];

      // 判断当前列是否需要合并
      if (!mergeColumns.includes(column.property)) return [1, 1];

      // 获取当前激活的 schema
      const currentSchema = this.resultData.schemaList.find(
        (schema) => schema.schemaName === this.activeTab
      );

      // 如果没有找到对应 schema 或没有 detailList 数据，则正常显示
      if (!currentSchema || !currentSchema.detailList) return [1, 1];

      const {detailList: data, hasL3} = currentSchema;

      // 如果是 l3Name 或 l3Weight 列，但当前 schema 没有 L3，则正常显示
      if ((column.property === 'l3Name' || column.property === 'l3Weight') && !hasL3) return [1, 1];

      // 获取当前列的参考列，evaluationItemsScore 参考 evaluationItems
      const referenceColumn = column.property === 'evaluationItemsScore' ? 'evaluationItems' : column.property;

      // 计算合并的行数
      let startIndex = rowIndex;
      while (startIndex > 0 && data[startIndex - 1][referenceColumn] === row[referenceColumn]) {
        startIndex--;
      }

      const spanCount = data.slice(startIndex).findIndex(
        (item) => item[referenceColumn] !== row[referenceColumn]
      );

      const span = spanCount === -1 ? data.length - startIndex : spanCount;

      // l2Score 列的特殊合并逻辑：合并所有单元格
      if (column.property === 'l2Score') {
        return rowIndex === 0 ? [data.length, 1] : [0, 0];
      }

      // 合并逻辑
      return startIndex === rowIndex ? [span, 1] : [0, 0];
    },

    handleOwnerIdUpdate(ownerId) {
      this.$set(this.scope.row, 'fileId', ownerId);
    },
    handleOwnerIdImgUpdate(ownerId) {
      this.$set(this.scope.row, 'imgId', ownerId);
    }
  },
};
</script>
<style>
.custom-dialog .el-dialog__body {
  height: 600px;
  padding: 10px;
  overflow: hidden;
}

.custom-tree-node {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 14px;
  padding-right: 8px;
}
</style>
<style scoped lang="scss">
.dialog-content {
  max-height: 170vh;
  overflow-y: auto;
}

.dialog-footer {
  text-align: right;
}

::v-deep .el-table__fixed-header-wrapper {
  z-index: 9 !important;
}

::v-deep .el-table .el-table__fixed-body-wrapper {
  top: 72px !important;
}


.examine-detail {
  ::v-deep p {
    margin: 0;
    padding: 0;
  }
}

.tableDiv {
  ::v-deep .el-upload-list__item {
    width: 42px;
    height: 42px;
    margin: 0px 5px -6px 0 !important;
  }

  ::v-deep .el-upload--picture-card {
    width: 42px;
    height: 42px;
    margin: 4px 0;
  }

  ::v-deep .el-icon-plus {
    font-size: 1rem;
  }

  ::v-deep .el-upload-list__item-actions {
    font-size: 1rem;
  }

  ::v-deep .el-upload-list__item-actions span + span {
    margin-left: 5px !important;
  }
}

::v-deep .el-dialog {
  display: flex;
  flex-direction: column;
  margin: 0 !important;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  max-height: calc(100% - 30px);
  max-width: calc(100% - 30px);
}

::v-deep .el-dialog .el-dialog__body {
  flex: 1;
  overflow: auto;
}

::v-deep .el-drawer__header {
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  color: #72767b;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  margin-bottom: 0px;
  padding: 20px;
  padding-bottom: 0;
}

.examine-task-container {
  padding: 20px;
}

.uniform-width {
  width: 250px;
  min-width: 200px;
  max-width: 300px;
  display: inline-block;
  word-break: break-word;
}
</style>
