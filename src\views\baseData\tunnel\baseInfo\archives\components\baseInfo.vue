<template>
  <div
    v-loading="loading"
    style="border-radius: 10px; box-shadow: 0px 0px 10px 0px rgba(0,0,0,0.2);"
    :class="oneMap ? 'one-map' : 'default'"
  >
    <el-tabs
      v-model="activeName"
      type="card"
    >
      <el-tab-pane
        label="基础信息"
        name="baseInfo"
      >
        <div style="height: calc(100vh - 167px);overflow-y: auto;padding: 0 10px 0 5px;">
          <el-form
            ref="baseInfo"
            :model="form"
            label-width="160px"
            :disabled="true"
          >
            <div style="display: flex; flex-wrap: wrap;">
              <ManageSelectTree
                placeholder="请选择"
                :formObject="form"
                v-if="!loading"
              />
              <el-col
                  :span="item.span?item.span:12"
                v-for="(item,index) in baseInfoFields"
                :key="index"
                  v-if="!item.forView||forView"
              >
                <el-form-item
                  :label="item.label"
                  :prop="item.prop"

                >
                  <span v-if="item.type === 'input'">
                    <el-input
                      v-model="form[item.prop]"
                      :placeholder="item.placeholder"
                      clearable
                    />
                  </span>
                  <span v-if="item.type === 'pileInput'">
                    <PileInput
                      v-model="form[item.prop]"
                      :placeholder="item.placeholder"
                    />
                  </span>
                  <span v-else-if="item.type === 'inputNumber'">
                    <el-input-number
                      style="width: 100%"
                      v-model="form[item.prop]"
                      :precision="item.precision"
                      clearable
                    ></el-input-number>
                  </span>
                  <span v-else-if="item.type === 'select'">
                    <el-select
                      v-model="form[item.prop]"
                      style="width: 100%;"
                      :placeholder="item.placeholder"
                      clearable
                      filterable
                      :disabled="!form[item.disabledFieds]"
                    >
                      <el-option
                        v-for="v in item.options"
                        :key="v[item.optionValue]"
                        :label="v[item.optionLabel]"
                        :value="v[item.optionValue]"
                      />
                    </el-select>
                  </span>

                  <span v-else-if="item.type === 'CascaderRegion'">
                      <CascaderRegion
                          v-model="form[item.prop]"
                          :deep=1
                          @input="(value)=>{form[item.prop]=value}"
                      />
                    </span>
                  <span v-else-if="item.type === 'selectTree'">
                    <SelectTree
                      v-model="form[item.prop]"
                      :dept-type="item.deptType"
                      placeholder="请选择"
                    />
                  </span>
                  <span v-else-if="item.type === 'dictSelect'">
                    <el-select
                      v-model="form[item.prop]"
                      style="width: 100%;"
                      placeholder="请选择"
                      clearable
                    >
                      <el-option
                        v-for="i in dict.type[item.dict]"
                        :key="i.value"
                        :label="i.label"
                        :value="i.value"
                      />
                    </el-select>
                  </span>
                  <span v-else-if="item.type === 'multiDictSelect'">
                      <MultiDictSelect
                        v-model="form[item.prop]"
                        :disabled="item.disabledFieds"
                        :multiple="item.multiple"
                        :options="dict.type[item.dict]"/>
                    </span>
                  <div v-else-if="item.type =='uploadImg'">
                    <ImageUpload
                      :key="item.ownerId"
                      v-model="form[item.prop]"
                      :limit="1"
                      :owner-id="item.ownerId"
                      storage-path="/base/tunnel/baseInfo"
                    />
                  </div>
                  <span v-else-if="item.type === 'date'">
                    <el-date-picker
                      v-model="form[item.prop]"
                      style="width:100%"
                      type="date"
                      :placeholder="item.placeholder"
                      clearable
                      value-format="yyyy-MM-dd"
                    />
                  </span>
                  <span v-else-if="item.type === 'year'">
                      <el-date-picker
                        v-model="form[item.prop]"
                        style="width: 100%"
                        type="year"
                        :placeholder="item.placeholder"
                         :picker-options="pickerOptions"
                        clearable
                        value-format="yyyy"
                      />
                    </span>
                  <span v-else-if="item.type === 'tree'">
                    <select-tree
                      style="width: 100%;"
                      v-model="form[item.prop]"
                      clearable
                    />
                  </span>
                  <span v-else-if="item.type === 'roadType'">
                    <SectionSelect
                      style="pointer-events: none"
                      v-model="form[item.prop]"
                      :formObject="form"
                      :sectionId="form.maintenanceSectionId"
                      :disabled="!form.maintenanceSectionId"
                      clearable
                    />
                  </span>
                  <span v-else-if="item.type === 'coordinate'">
                      <lon-lat :type="item.prepend" :lon.sync="form[item.propLon]" :lat.sync="form[item.propLat]"/>
                    </span>
                </el-form-item>
              </el-col>
            </div>
          </el-form>
        </div>
      </el-tab-pane>
      <el-tab-pane
        label="结构技术数据"
        name="technology"
      >
        <div style="height: calc(100vh - 167px);overflow-y: auto;padding: 0 10px 0 5px;">
          <el-form
            ref="technology"
            :model="form"
            label-width="190px"
            :disabled="true"
          >
            <div style="display: flex; flex-wrap: wrap;">
              <el-col
                :span="12"
                v-for="(item,index) in technologyFields"
                :key="index"
              >
                <el-form-item
                  :label="item.label"
                  :prop="item.prop"
                >
                  <span v-if="item.type === 'dictSelect'">
                    <el-select
                      v-model="form[item.prop]"
                      style="width: 100%;"
                      placeholder="请选择"
                      clearable
                    >
                      <el-option
                        v-for="i in dict.type[item.dict]"
                        :key="i.value"
                        :label="i.label"
                        :value="i.value"
                      />
                    </el-select>
                  </span>
                  <span v-else-if="item.type === 'inputNumber'">
                    <el-input-number
                      style="width: 100%"
                      v-model="form[item.prop]"
                      :precision="item.precision"
                      :min="item.min"
                      :max="item.max"
                      clearable
                    ></el-input-number>
                  </span>
                  <span v-else-if="item.type === 'input'">
                    <el-input
                      v-model="form[item.prop]"
                      :placeholder="item.placeholder"
                    />
                  </span>
                </el-form-item>
              </el-col>
            </div>
          </el-form>
        </div>
      </el-tab-pane>
      <el-tab-pane
        label="隧道管理信息"
        name="information"
      >
        <div style="height: calc(100vh - 167px);overflow-y: auto;padding: 0 10px 0 5px;">
          <el-form
            ref="information"
            :model="form"
            label-width="160px"
            :disabled="true"
          >
            <div style="display: flex; flex-wrap: wrap;">
              <el-col
                :span="item.span?item.span:12"
                v-for="(item,index) in informationFields"
                :key="index"
              >
                <el-form-item
                  :label="item.label"
                  :prop="item.prop"
                >
                  <span v-if="item.type==='input'">
                       <el-input
                         v-model="form[item.prop]"
                         :placeholder="item.placeholder"
                       />
                    </span>


                  <span v-else-if="item.type === 'inputTextarea'">
                    <el-input
                      v-model="form[item.prop]"
                      autosize
                      :placeholder="item.placeholder"
                      type="textarea"
                      clearable
                    />
                  </span>
                </el-form-item>
              </el-col>
            </div>
          </el-form>
        </div>
      </el-tab-pane>
      <el-tab-pane
          label="隧道档案"
          name="fileInfo"
        >
          <div style="height: 60vh;overflow-y: auto;padding: 0 10px 0 5px;">
            <el-form
              ref="fileInfo"
              :model="form"
              label-width="160px"
              :disabled="true"
            >
              <div style="display: flex; flex-wrap: wrap;">
                <el-col
                  :span="12"
                  v-for="(item,index) in fileInfoFields"
                  :key="index"
                >
                  <el-form-item
                    :label="item.label"
                    :prop="item.prop"
                  >
                  <span v-if="item.type === 'dict'">
                      <el-select
                        v-model="form[item.prop]"
                        style="width: 100%;"
                        placeholder="请选择"
                        clearable
                      >
                        <el-option
                          v-for="i in dict.type[item.dict]"
                          :key="i.value"
                          :label="i.label"
                          :value="i.value"
                        />
                      </el-select>
                    </span>
                    <span v-else-if="item.type === 'input'">
                      <el-input
                        style="width: 100%"
                        v-model="form[item.prop]"
                        :placeholder="item.placeholder"
                        clearable
                      ></el-input>
                    </span>
                    <span v-else-if="item.type === 'date'">
                      <el-date-picker
                        v-model="form[item.prop]"
                        style="width:100%"
                        type="date"
                        :placeholder="item.placeholder"
                        clearable
                        value-format="yyyy-MM-dd"
                      />
                    </span>

                  </el-form-item>
                </el-col>
              </div>
            </el-form>
          </div>
        </el-tab-pane>
<!--      <el-tab-pane-->
<!--        label="改造数据"-->
<!--        name="transform"-->
<!--      >-->
<!--        <div style="height:calc(100vh - 167px)">-->
<!--          <ManageList-->
<!--            v-if="activeName === 'transform'"-->
<!--            :canEdit="false"-->
<!--            :tunnelId="form.id"-->
<!--          />-->
<!--        </div>-->
<!--      </el-tab-pane>-->
    </el-tabs>
  </div>
</template>

<script>
import formFields from '../../js/formFields'
import dicts from '../../js/dicts'
import { getStatic } from '@/api/baseData/tunnel/baseInfo/index'
import { getHisStatic } from '@/api/baseData/tunnel/history/index'
import SelectTree from '@/components/DeptTmpl/selectTree'
import ManageList from '../../components/manageList.vue'
import SectionSelect from '@/components/SectionSelect'
import PileInput from '@/components/PileInput/index.vue'
import { listMaintenanceSectionAll } from '@/api/system/maintenanceSection'
import { listByMaintenanceSectionId } from '@/api/baseData/common/routeLine'
import CascaderRegion from "@/views/baseData/components/CascaderRegion/index.vue";
import lonLat from "@/components/mapPosition/lonLat.vue";
import ManageSelectTree from '@/components/manageSelectTree/index.vue'
import MultiDictSelect from "@/views/baseData/components/MultiDictSelect/index.vue";

export default {
  name: 'tunnel-archives-baseInfo',
  components: {
    MultiDictSelect,
    lonLat, CascaderRegion, SelectTree, SectionSelect, ManageList, PileInput,ManageSelectTree },
  inject: {
    oneMap: {
      default: false, 
    }
  },
  props: {
    id: {
      default: ''
    }
  },
  dicts: dicts,
  data() {
    return {
      loading: false,
      activeName: 'baseInfo',
      form: {},
      baseInfoFields: [],
      technologyFields: [],
      informationFields: [],
      manageListData: [],
      fileInfoFields: [],

    }
  },
  created() {
    this.init()
  },
  methods: {
    init() {
      this.baseInfoFields = JSON.parse(JSON.stringify(formFields.baseInfo))
      this.technologyFields = JSON.parse(JSON.stringify(formFields.technology))
      this.informationFields = JSON.parse(
        JSON.stringify(formFields.information)
      )
      this.fileInfoFields= JSON.parse(
        JSON.stringify(formFields.fileInfo)
      )
      this.getFormData()
    },
    // 获取表单数据
    getFormData() {
      this.loading = true
      let api = this.$route.query.type == 'history' ? getHisStatic : getStatic
      api(this.id)
        .then(res => {
          if (res.code === 200) {
            this.form = res.data
            this.$emit('getBaseInfo', this.form)
          }
        })
        .finally(() => {
          this.loading = false
        })
    },
    // 监听选中管理处
    deptChange(e) {
      if (!e) return
      listMaintenanceSectionAll({ departmentId: e }).then(res => {
        if (res.code == 200) {
          this.baseInfoFields.forEach(el => {
            if (el.prop === 'maintenanceSectionId') {
              el.options = res.data
            }
          })
        }
      })
    },
    // 监听选中养护路段
    maintenanceSectionChange(e) {
      if (!e) return
      listByMaintenanceSectionId({ maintenanceSectionId: e }).then(res => {
        if (res.code == 200) {
          this.baseInfoFields.forEach(el => {
            if (el.prop === 'routeCode' || el.prop === 'routeName') {
              el.options = res.data
            }
          })
        }
      })
    }
  },
  watch: {
    'form.managementMaintenanceId'(newVal, oldVal) {
      if (newVal) {
        this.deptChange(newVal)
      }
    },
    'form.maintenanceSectionId'(newVal, oldVal) {
      if (newVal) {
        this.maintenanceSectionChange(newVal)
      }
      if (!newVal&&this.form.routeCode) {
        this.form.routeCode = ''
        this.form.routeName = ''
      }
    }
  }
}
</script>

<style lang="scss" scoped>
@import '@/assets/styles/common.scss';

.default {
  @import '~@/assets/styles/el-tabs.scss';
  ::v-deep {
    .el-tabs__header {
      padding-left: 20px;
      border: 0;
    }
    .el-tabs__content {
      border: 0;
    }
    .el-form-item__label {
      font-family: Microsoft YaHei, Microsoft YaHei;
      font-weight: 400;
      font-size: 14px;
      color: #333333;
    }
    .el-input.is-disabled .el-input__inner {
      background-color: white;
      border-color: #dfe4ed;
      color: #1d2129;
    }
    .el-textarea.is-disabled .el-textarea__inner {
      background-color: white;
      border-color: #dfe4ed;
      color: #1d2129;
    }
  }
}
</style>
