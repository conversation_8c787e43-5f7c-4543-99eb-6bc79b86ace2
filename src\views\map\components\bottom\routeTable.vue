<template>
  <div class="bottom-table">
    <div class="body-title">
      <el-button type="primary" style="height: 20px;line-height: 6px;" @click="onExport">导出</el-button>
      {{ tableTitle + '养护路段列表' }}
    </div>
    <i
      class="el-icon-close body-close"
      @click="handleClose"
    ></i>
    <div>
      <el-table
        ref="table"
        v-loading="loading"
        :data="tableData"
        border
        height="250px"
        @row-click="handleRowClick"
        :highlight-current-row="highlight"
      >
        <el-table-column
          type="index"
          width="50"
        />
        <el-table-column
          v-for="(item,index) in tableHeader"
          :key="index"
          :prop="item.columnName"
          :label="item.columnNameZh"
          min-width="180"
          show-overflow-tooltip
        >
          <template slot-scope="scope">
            <span v-if="item.columnName === 'ldjsdj'">
              <dict-tag
                :options="dict.type.bridge_route_level"
                :value="scope.row[item.columnName]"
              />
            </span>
            <span v-else>
              {{scope.row[item.columnName]}}
            </span>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        style="float: right;"
        :current-page.sync="params.pageNum"
        :page-size="params.pageSize"
        layout="total, prev, pager, next"
        :total="total"
        background
        :pager-count="3"
      >
      </el-pagination>
    </div>
  </div>
</template>

<script>
import { getTableHeader, getDataList } from '@/api/oneMap/layerData'
import { isValidWKT, addClickFeature } from '../common/mapFun'
import { WKT } from 'ol/format'

export default {
  name: 'route-table',
  inject: {
    map: {
      default: false,
    },
  },
  props: {
    headerId: {
      type: undefined,
      default: '1824259894327382017'
    },
    data: {
      type: undefined,
      default: []
    },
    tableTitle: {
      type: String,
      default: ''
    }
  },
  components: {},
  dicts: ['bridge_route_level'],
  data() {
    return {
      loading: false,
      tableData: [],
      tableHeader: [],
      highlight: true,
      params: {
        pageNum: 1,
        pageSize: 10
      },
      total: 0
    }
  },
  async created() {
    await this.getHeader()
    await this.getList()
    this.$nextTick(() => {
      this.$refs.table.doLayout()
    })
  },
  methods: {
    async getHeader() {
      this.loading = true
      const res = await getTableHeader({ menuSubId: this.headerId })
      if (res.code === 200) {
        // 处理路线表格（非通用）
        res.rows.map(el => {
          switch (el.columnName) {
            case 'lxmc':
              el.showIndex = 0
              break
          }
        })
        const sortedData = res.rows.sort((a, b) => a.showIndex - b.showIndex)
        this.tableHeader = sortedData.map(el => {
          return {
            columnNameZh: el.columnNameZh,
            columnName: el.columnName
          }
        })
        this.tableHeader = this.tableHeader.filter(
          item => item.columnName !== 'id' && item.columnName !== 'shape'
        )
      }
      this.loading = false
    },
    async getList() {
      this.loading = true
      let obj = {
        ...this.data,
        ...this.params
      }
      const res = await getDataList(obj)
      if (res.code === 200) {
        this.tableData = res.rows
        this.total = res.total
      }
      this.loading = false
    },
    handleClose() {
      this.removeMapLayer('layerId')
      this.$emit('close')
    },
    handleSizeChange(val) {
      this.params.pageSize = val
      this.getList()
    },
    handleCurrentChange(val) {
      this.params.pageNum = val
      this.getList()
    },
    handleRowClick(row) {
      let shape = row.shape
      if (!shape) return
      const list = window.mapLayer.getLayers().getArray()
      const arr = []
      list.map(layer => {
        if (layer.get('layerId') === row.id) {
          arr.push(layer)
        }
      })
      if (arr.length > 0) {
        arr.map(l => {
          window.mapLayer.removeLayer(l)
        })
        this.highlight = false
        return
      } else {
        this.highlight = true
      }
      if (!isValidWKT(shape)) return
      let feature = undefined
      if (!this.coordinateSystem || this.coordinateSystem == '') {
        this.coordinateSystem =
          this.legendList && this.legendList.length
            ? this.legendList[0].coordinateSystem
            : ''
      }
      feature = new WKT().readFeature(shape, {
        dataProjection: 'EPSG:4326',
        featureProjection: 'EPSG:3857'
      })
      addClickFeature(feature, row, null, true, true, 'clickLayer', 1)
      // 跳转到地图
      window.mapLayer.getView().fit(feature.getGeometry().getExtent(), {
        duration: 300,
        maxZoom: 18
      })
    },
    removeMapLayer(key, value) {
      const list = window.mapLayer.getLayers().getArray()
      const arr = []
      list.map(layer => {
        if (value ? layer.get(key) === value : layer.get(key)) {
          arr.push(layer)
        }
      })
      if (arr.length > 0) {
        arr.map(l => {
          window.mapLayer.removeLayer(l)
        })
      }
    },
    // 导出数据
    onExport() {
      this.$modal.confirm('确认导出该数据列表？').then(()=> {
        this.download(
        "/oneMap/layerData/exportDataList",
        this.data,
        this.tableTitle ? this.tableTitle + '养护路段列表' : '养护路段列表',
        {
          headers: { "Content-Type": "application/json;" },
          parameterType: "body",
        }
      );
      }).then(() => {
        
      }).catch(() => {});
    },
  },
  computed: {},
  watch: {
    data: {
      handler(newValue, oldValue) {
        this.removeMapLayer('layerId')
        this.params.pageNum = 1
        this.params.pageSize = 10
        this.getList()
        this.$nextTick(() => {
          this.$refs.table.doLayout()
        })
      },
      deep: true
    }
  }
}
</script>

<style lang="scss" scoped>
.bottom-table {
  width: 100%;
  position: absolute;
  bottom: 0;
  left: 0;
  z-index: 99;
  background: linear-gradient(
    90deg,
    rgba(2, 10, 30, 0.8) 0%,
    rgba(2, 10, 30, 0.8) 100%
  );
  box-shadow: inset 0px 0px 10px 0px rgba(35, 134, 255, 0.5);
  border-radius: 10px 10px 0 0;
  color: #ffffff;
  border: 1px solid rgb(6, 135, 255);
  padding: 30px 10px 10px 10px;
  .body-title {
    position: absolute;
    top: 6px;
    left: 10px;
  }
  .body-close {
    position: absolute;
    top: 5px;
    right: 5px;
    z-index: 100;
    font-size: 18px;
    cursor: pointer;
  }
  ::v-deep .el-table {
    background: unset;
    border: unset;

    &::before {
      background-color: unset;
    }

    &::after {
      background-color: unset;
    }

    tr {
      background-color: unset;
    }

    tr:nth-child(even) {
      background: rgba(86, 145, 255, 0);
      color: #ffffff;
    }

    td {
      color: #ffffff;
    }

    td,
    th.is-leaf {
      border: 1px solid rgba(1, 102, 254, 0.4);
    }

    .el-table__header-wrapper tr th {
      background-color: rgba(1, 102, 254, 0.2);
      color: #ffffff !important;
      font-size: 14px;
    }

    tbody {
      background-color: unset;
      border: none;
      cursor: pointer;
    }

    .el-table__body tr:hover > td {
      background-color: unset;
    }

    .el-table__body tr.current-row > td {
      background-color: rgba(1, 102, 254, 0.2) !important;
    }

    .el-table__body tr.current-row:hover {
      background-color: rgba(1, 102, 254, 0.2);
    }
    .el-table__body-wrapper::-webkit-scrollbar-thumb {
      // border-radius: 2px !important; /* 设置滑块的圆角 */
      background-color: rgba(
        1,
        102,
        254,
        0.3
      ) !important; /* 设置滑块的背景色 */
    }
    .el-table__body-wrapper::-webkit-scrollbar-track {
      background-color: rgba(
        1,
        102,
        254,
        0.2
      ) !important; /* 设置轨道的背景色 */
    }
  }
}
::v-deep .el-pagination {
  .el-pagination__total {
    color: #ffffff;
  }

  .btn-prev,
  .btn-next {
    background-color: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(1, 102, 254, 0.6);
    color: #ffffff;
  }

  .el-pager {
    li {
      background-color: rgba(255, 255, 255, 0.1);
      border: 1px solid rgba(1, 102, 254, 0.6);
      color: #ffffff;
    }

    li:not(.disabled).active {
      background-color: rgba(1, 102, 254, 0.8);
      border: 1px solid rgba(1, 102, 254, 0.6);
      color: #ffffff;
    }
  }
}
</style>