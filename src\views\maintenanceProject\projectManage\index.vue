<template>
  <div class="app-container">
    <el-row :gutter="20">
      <!--筛选区开始-->
      <el-col :span="24" :xs="24">
        <el-row>
          <el-col :span="24">
            <el-form ref="queryForm" :inline="true" :model="queryParams" label-width="68px" size="small">
              <el-form-item label="" prop="disType">
                <el-date-picker
                    v-model="queryParams.year"
                    placeholder="年份"
                    style="width: 240px"
                    type="year"
                    value-format="yyyy"
                >
                </el-date-picker>
              </el-form-item>
              <el-form-item>
                <selectTree
                    :key="'domainId'"
                    v-model="queryParams.domainId"
                    :deptType="100"
                    :deptTypeList="[1, 3, 4]" clearable
                    filterable
                    placeholder="管养单位"
                    style="width: 240px"
                />
              </el-form-item>
              <el-form-item>
                <selectTree
                    :key="'constructionUnit'"
                    v-model="queryParams.calcDomainId" :data-rule="false"
                    :dept-type="100"
                    :expand-all="false"
                    :filter-keys="['云南省交通投资建设集团有限公司', '云南交投投资有限公司']"
                    clearable
                    filterable
                    placeholder="施工单位"
                    style="width: 240px"
                />
              </el-form-item>
              <el-form-item>
                <RoadSection v-model="queryParams.maiSecId" :deptId="queryParams.domainId" placeholder="路段"
                             style="width: 240px"/>
              </el-form-item>
              <el-form-item>
                <el-input v-model="queryParams.name" placeholder="项目名称" style="width: 240px"></el-input>
              </el-form-item>
              <el-form-item>
                <el-input v-model="queryParams.code" placeholder="项目编码" style="width: 240px"></el-input>
              </el-form-item>
              <el-form-item>
                <el-input v-model="queryParams.constructionCode" placeholder="任务单编码" style="width: 240px"></el-input>
              </el-form-item>
              <el-form-item>
                <dict-select
                    v-model="queryParams.projectType"
                    placeholder="工程分类"
                    style="width: 240px"
                    type="project_type"
                />
              </el-form-item>
              <el-form-item>
                <cost-select :type="25"
                             v-model="queryParams.mtype"
                    placeholder="所属工程类别"
                    style="width: 240px"
                />
              </el-form-item>
              <el-form-item>
                <dict-select
                    v-model="queryParams.status"
                    placeholder="项目状态"
                    style="width: 240px"
                    type="project_status_type"
                />
              </el-form-item>
              <el-form-item>
                <el-input v-model="queryParams.createuser" placeholder="操作人" style="width: 240px"></el-input>
              </el-form-item>
              <el-form-item>
                <el-button icon="el-icon-search" size="mini" type="primary" @click="handleQuery">搜索</el-button>
                <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
              </el-form-item>
            </el-form>
          </el-col>
        </el-row>
        <!--筛选区结束-->

        <!--操作按钮区开始-->
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button
                icon="el-icon-edit"
                size="mini"
                type="primary"
                v-has-menu-permi="['project:project:add']"
                @click="handleAdd"
            >新增
            </el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button
                icon="el-icon-download"
                size="mini"
                type="success"
                v-has-menu-permi="['project:project:export']"
                @click="exportList"
            >导出
            </el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button
                size="mini"
                type="warning"
                v-has-menu-permi="['project:project:updateStatus']"
                @click="updProject(1)"
            >项目开始
            </el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button
                size="mini"
                type="danger"
                v-has-menu-permi="['project:project:updateStatus']"
                @click="updProject(2)"
            >项目结束
            </el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button
                size="mini"
                type="primary"
                v-has-menu-permi="['project:project:updateStatus']"
                @click="updProject(0)"
            >恢复到未开始
            </el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button
              size="mini"
              type="success"
              v-has-menu-permi="['theft:project:updateStatus']"
              @click="updProject(1)"
            >恢复到进行中
            </el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button
                size="mini"
                type="success"
                v-has-menu-permi="['projConstruction:construction:add']"
                @click="addTask('2')"
            >生成检测任务单
            </el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button
                size="mini"
                type="warning"
                v-has-menu-permi="['projConstruction:construction:add']"
                @click="addTask('1')"
            >生成施工任务单
            </el-button>
          </el-col>
          <!--          <el-col :span="1.5">-->
          <!--            <el-button-->
          <!--                size="mini"-->
          <!--                type="primary"-->
          <!--                @click="addTask('3')"-->
          <!--            >生成设计任务单-->
          <!--            </el-button>-->
          <!--          </el-col>-->
          <right-toolbar :columns="columns" :showSearch.sync="showSearch" @queryTable="handleQuery"></right-toolbar>
        </el-row>
        <!--操作按钮区结束-->
        <!--数据表格开始-->
        <div class="tableDiv">
          <el-table v-adjust-table
              ref="dataTable"
              v-loading="loading"
              :data="tableData"
              :height="
                showSearch ? 'calc(100vh - 330px)' : 'calc(100vh - 270px)'
              "
              border
              highlight-current-row
              row-key="id"
              size="mini"
              stripe
              style="width: 100%"
              @row-click="handleClickRow"
          >
            <el-table-column
                align="center"
                label="序号"
                type="index"
                width="50"
            />
            <template v-for="(column,index) in columns">
              <el-table-column v-if="column.visible"
                               show-overflow-tooltip
                               :label="column.label"
                               :prop="column.field"
                               :width="column.width"
                               align="center">
                <template slot-scope="scope">
                  <dict-tag v-if="column.dict" :options="dict.type[column.dict]" :value="scope.row[column.field]"/>
                  <template v-else-if="column.slots">
                    <RenderDom :index="index" :render="column.render" :row="scope.row"/>
                  </template>
                  <span v-else-if="column.isTime">{{ parseTime(scope.row[column.field], '{y}-{m}-{d}') }}</span>
                  <span v-else>{{ scope.row[column.field] }}</span>
                </template>
              </el-table-column>
            </template>
            <el-table-column
                align="center"
                class-name="small-padding fixed-width"
                fixed="right"
                label="操作"
                width="250"
            >
              <template slot-scope="scope">
                <el-button
                    :disabled="scope.row.status != 0"
                    icon="el-icon-delete"
                    size="mini"
                    v-has-menu-permi="['project:project:remove']"
                    type="text"
                    @click="handleDelete(scope.row)"
                >删除
                </el-button>
                <el-button
                    icon="el-icon-edit"
                    size="mini"
                    type="text"
                    v-has-menu-permi="['project:project:edit']"
                    @click="handleEdit (scope.row)"
                >修改
                </el-button>
                <el-button
                    icon="el-icon-view"
                    size="mini"
                    type="text"
                    @click="handleView (scope.row)"
                >查看
                </el-button>
                <el-button
                    icon="el-icon-tickets"
                    size="mini"
                    type="text"
                    @click="handleFileManage(scope.row)"
                >资料管理
                </el-button>
              </template>
            </el-table-column>
          </el-table>
          <pagination
              v-show="total>0"
              :limit.sync="queryParams.pageSize"
              :page.sync="queryParams.pageNum"
              :total="total"
              @pagination="handleQuery"
          />
        </div>
        <!--数据表格结束-->
      </el-col>
    </el-row>
    <el-drawer v-if="openDetail" :title="detailTitle" :visible.sync="openDetail" :wrapperClosable="false"
               destroy-on-close
               size="75%">
      <detail :readonly="readonly" :row-data="row" @close="handleClose"></detail>
    </el-drawer>
    <el-drawer v-if="openTask" :visible.sync="openTask" :wrapperClosable="false" destroy-on-close size="70%"
               title="新增任务单">
      <task-detail :project="checkRow" :type="taskType" @close="handleClose"></task-detail>
    </el-drawer>
    <el-drawer v-if="openData" :visible.sync="openData" :wrapperClosable="false" destroy-on-close size="70%"
               title="资料管理">
      <data-manage :row="row" @close="openData=false"></data-manage>
    </el-drawer>
  </div>
</template>

<script>
import Tables from "@/views/patrol/frequencySettings/tables.vue";
import RoadSection from "@/views/baseData/components/roadSection/index.vue";
import selectTree from "@/components/DeptTmpl/selectTree.vue";
import Detail from "./detail.vue";
import TaskDetail from "@/views/maintenanceProject/taskList/component/taskDetail.vue";
import {deleteProject, isAdd, queryList, updateProjectStatus} from "@/api/maintenanceProject/projectManage";
import DataManage from "./component/dataManage.vue";
import CostSelect from "@/components/CostSelect/index.vue";

export default {
  name: 'ProjectManage',
  dicts: ['affiliation_project_type', 'project_type', 'project_status_type'],
  components: {
    CostSelect,
    DataManage, Detail, selectTree, RoadSection, Tables, TaskDetail,
    RenderDom: {
      functional: true,
      props: {
        row: Object,
        index: Number,
        render: Function
      },
      render(createElement, ctx) {
        const {row, index} = ctx.props
        return ctx.props.render(row, index)
      }
    }
  },
  data() {
    return {
      showSearch: false,
      queryParams: {
        pageNum: 1,
        pageSize: 50
      },
      total: 0,
      loading: false,
      columns: [
        {key: 0, width: 100, field: 'status', label: `状态`, visible: true, dict: 'project_status_type'},
        {key: 1, width: 100, field: 'domainName', label: `管养单位`, visible: true},
        {key: 2, width: 100, field: 'year', label: `年度`, visible: true},
        {key: 3, width: 200, field: 'name', label: `项目名称`, visible: true},
        {key: 4, width: 200, field: 'code', label: `项目编码`, visible: true},
        {key: 5, width: 100, field: 'maiSecName', label: `路段名称`, visible: true},
        {key: 6, width: 100, field: 'sumFund', label: `计划资金`, visible: true},
        {
          key: 7,
          width: 100,
          field: 'constructionTaskList',
          label: `施工任务单`,
          visible: true,
          slots: true,
          render: (row) => {
            return (
                <el-link disabled={!row.constructionTaskList} type="primary" underline
                         onClick={e => this.handleOpenDetail(row.code, 1)}>{row.constructionTaskList}</el-link>
            )
          }
        },
        {
          key: 8, width: 100, field: 'checkTaskList', label: `检测任务单`, visible: true,
          slots: true,
          render: (row) => {
            return (
                <el-link disabled={!row.checkTaskList} type="primary" underline
                         onClick={e => this.handleOpenDetail(row.code, 2)}>{row.checkTaskList}</el-link>
            )
          }
        },
        {
          key: 9, width: 100, field: 'designTaskList', label: `设计任务单`, visible: true,
          slots: true,
          render: (row) => {
            return (
                <el-link disabled={!row.designTaskList} type="primary" underline
                         onClick={e => this.handleOpenDetail(row.code, 3)}>{row.designTaskList}</el-link>
            )
          }
        },
        {
          key: 10, width: 200, field: 'mileRange', label: `桩号范围`, visible: true
        },
        {key: 11, width: 100, field: 'projectType', label: `工程分类`, visible: true, dict: 'project_type'},
        {key: 12, width: 130, field: 'mtype', label: `所属工程类别`, visible: true, dict: 'affiliation_project_type'},
        {
          key: 13, width: 200, field: 'structureName', label: `构造物名称`, visible: true
        },
        {key: 14, width: 100, field: 'defLiaPer', label: `缺陷责任期`, visible: true},
        {key: 15, width: 100, field: 'expectDuration', label: `预计工期`, visible: true},
        {key: 16, width: 130, field: 'beginDate', label: `预计开始时间`, visible: true},
        {key: 17, width: 130, field: 'endDate', label: `预计结束时间`, visible: true},
        {key: 18, width: 100, field: 'createuser', label: `操作人`, visible: true},
        {key: 19, width: 100, field: 'updateuser', label: `上报人`, visible: true},
        {key: 20, width: 100, field: 'remark', label: `备注`, visible: true},
      ],
      tableData: [],
      detailTitle: '新增项目信息',
      openDetail: false,
      row: {},
      checkRow: null,
      openTask: false,
      openData: false,
      taskType: '1',
      readonly: false,
    }
  },
  mounted() {
    this.handleQuery()
  },
  methods: {
    handleQuery() {
      this.loading = true
      queryList(this.queryParams).then(res => {
        this.tableData = res.rows
        this.total = res.total
        this.loading = false
      })
    },
    resetQuery() {
      this.queryParams = {
        pageNum: 1,
        pageSize: 50
      }
      this.handleQuery()
    },
    handleAdd() {
      this.row = {}
      this.detailTitle = '新增项目信息'
      this.readonly = false
      this.openDetail = true
    },
    handleEdit(row) {
      this.row = row
      this.detailTitle = '修改项目信息'
      this.readonly = false
      this.openDetail = true
    },
    handleDelete(row) {
      this.$modal.confirm("是否确认删除").then(() => {
        this.loading = true
        deleteProject(row.id).then(res => {
          this.$modal.msgSuccess("删除成功")
          this.handleQuery()
        })
      });
    },
    handleView(row) {
      this.row = row
      this.detailTitle = '项目信息'
      this.readonly = true
      this.openDetail = true
    },
    handleClose() {
      this.openDetail = false
      this.openTask = false
      this.handleQuery()
    },
    // 导出清单按钮
    exportList() {
      this.queryParams.fileName = '养护工程项目管理'
      this.download(
          'manager/project/export',
          {...this.queryParams},
          `project_${new Date().getTime()}.xlsx`,
          {
            headers: {'Content-Type': 'application/json;'},
            parameterType: 'body'
          }
      )
    },
    handleClickRow(row) {
      this.checkRow = row
    },
    updProject(status) {
      if (!this.checkRow) {
        this.$modal.msgError("请选择一条数据")
        return
      } else {
        if (status == 0 && (this.checkRow.constructionTaskList != 0 || this.checkRow.checkTaskList != 0 || this.checkRow.designTaskList != 0)) {
          this.$modal.msgError("当前项目存在未完成的任务单，无法更新为未开始")
          return
        }
        if (status == 1 && (this.checkRow.status != 0 && this.checkRow.status != 2)) {
          this.$modal.msgError("当前项目已开始，无法再次更新为已开始")
          return;
        }
        if (status == 2 && this.checkRow.status != 1) {
          this.$modal.msgError("当前项目未开始，无法更新为已结束")
          return
        }
        this.$modal.confirm("是否确认更新？").then(() => {
          this.loading = true
          updateProjectStatus({id: this.checkRow.id, status: status}).then(res => {
            this.$modal.msgSuccess("更新成功")
            this.handleQuery()
          })
        })
      }
    },
    handleOpenDetail(code, type) {
      this.$router.push(`/maintenanceProject/taskList/taskListPrep?projCode=${code}&taskType=${type}`)
    },
    addTask(type) {
      if (!this.checkRow) {
        this.$modal.msgError("请选择一条数据")
        return
      }
      isAdd(this.checkRow.id).then(res => {
        if (res.code == 200) {
          this.taskType = type
          this.openTask = true
        }
      })
    },
    handleFileManage(row) {
      this.row = row
      this.openData = true
    },
  }
}
</script>

<style lang="scss" scoped>

</style>
<style lang="scss" scoped>
@import "@/assets/styles/business.scss";
</style>
