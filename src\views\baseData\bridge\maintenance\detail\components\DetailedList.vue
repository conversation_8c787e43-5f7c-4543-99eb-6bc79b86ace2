<template>
  <div class="repair-detailed-list">
    <el-descriptions title="2.合同清单" />
    <el-tabs v-model="activeName">
        <el-tab-pane label="交科院2020合同" name="0"></el-tab-pane>
        <el-tab-pane label="设计院设计合同2021" name="1"></el-tab-pane>
        <el-tab-pane label="云路检测2020合同" name="2"></el-tab-pane>
        <el-tab-pane label="云岭咨询监理合同2021" name="3"></el-tab-pane>
    </el-tabs>

    <el-table
      :data="tableData"
      style="width: 100%"
      show-summary
      :summary-method="getSummary"
    >
      <el-table-column
        prop="item1"
        label="子目号"
      >
      </el-table-column>
      <el-table-column
        prop="item2"
        label="子目名称"
      >
      </el-table-column>
      <el-table-column
        prop="item3"
        label="单位"
      >
      </el-table-column>
      <el-table-column
        prop="item4"
        label="单价"
      >
      </el-table-column>
      <el-table-column
        prop="item5"
        label="计算式"
      >
      </el-table-column>
      <el-table-column
        prop="item6"
        label="数量"
      >
      </el-table-column>
      <el-table-column
        prop="item7"
        label="资金"
      >
      </el-table-column>
      <el-table-column
        prop="item8"
        label="备注"
      >
      </el-table-column>
    </el-table>
  </div>
</template>

<script>
export default {
  name: 'RepairDetailedList',
  components:{},
  data(){
      return {
        activeName: '0',
        tableData: []
    }
  },
  watch:{},
  computed:{},
  created(){},
  mounted(){
    this.tableData.length = 0
    for (let i = 0; i < 5; i++) {
        this.tableData.push({
            item1: '192-8',
            item2: '安全保通费',
            item3: '总额',
            item4: 8,
            item5: 2514,
            item6: 1890,
            item7: 36298,
            item8: '备注信息'
        })    
    }
  },
    methods: {
    getSummary(param) {
        const { columns, data } = param;
        const sums = [];
        columns.forEach((column, index) => {
          if (index === 0) {
            sums[index] = '合同资金';
            return;
          }
          const values = data.map(item => Number(item[column.property]));
          if (!values.every(value => isNaN(value)) && index === 4) {
            sums[index] = values.reduce((prev, curr) => {
              const value = Number(curr);
              if (!isNaN(value)) {
                return prev + curr;
              } else {
                return prev;
              }
            }, 0);
            sums[index] += ' 元';
          } else {
            sums[index] = '';
          }
        });

        return sums;
      }
  },
}
</script>

<style lang="scss" scoped>
.repair-detailed-list {}
</style>