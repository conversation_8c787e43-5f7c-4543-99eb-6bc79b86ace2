<template>
  <div class="app-container maindiv">
    <el-timeline>
      <el-timeline-item
        v-for="(activity, index) in handleList"
        :key="index"
        hide-timestamp
      >
        <div class="time-line-item">
          <div class="time-line-timestamp">
            {{ activity.timestamp }}
          </div>
          <div class="time-line-content">
            {{ activity.content }}
          </div>
        </div>

      </el-timeline-item>
    </el-timeline>
  </div>
</template>

<script>
export default {
  name: "eventInfo",
  data() {
    return {
      activities: [
        {
          content: "活动按期开始",
          timestamp: "2018-04-15",
        },
        {
          content: "通过审核",
          timestamp: "2018-04-13",
        },
        {
          content: "创建成功",
          timestamp: "2018-04-11",
        },
      ],
    };
  },
  props: {
    handleList: {
      type: Array,
      default: ()=> []
    }
  },
  created() {

  },
  methods: {

  }
};
</script>

<style lang="scss" scoped>
.maindiv {
  height: 600px;
  overflow-y: scroll;

  ::v-deep .el-timeline {
    padding-left: 150px;
  }

  .time-line-item {
    display: flex;

    .time-line-timestamp {
      position: absolute;
      left: -150px;
      font-size: 18px;
      width: 105px;
      word-break: break-all;
    }
  }
}
</style>
<style lang="scss" scoped>
@import "@/assets/styles/business.scss";
</style>
