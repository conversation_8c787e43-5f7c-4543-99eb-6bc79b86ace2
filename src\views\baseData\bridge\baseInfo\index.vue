<template>
  <PageContainer>
    <template slot="search">
      <div style="display: flex; align-items: center; margin: 0">
        <CascadeSelection
          style="min-width: 192px"
          :form-data="queryParams"
          v-model="queryParams"
          types="201"
          multiple
        />
        <RangeInput
          :clearData="clearData"
          @startValue="
            (v) => {
              queryParams.centerStakeStart = v;
            }
          "
          @endValue="
            (v) => {
              queryParams.centerStakeEnd = v;
            }
          "
        />
        <div style="min-width: 240px">
          <el-button
            v-hasPermi="['baseData:bridge:selectPage']"
            type="primary"
            icon="el-icon-search"
            @click="handleQuery"
            >搜索</el-button
          >
          <el-button icon="el-icon-refresh" @click="resetQuery">重置</el-button>
          <el-button
            v-show="!showSearch"
            icon="el-icon-arrow-down"
            circle
            @click="showSearch = true"
          />
          <el-button
            v-show="showSearch"
            icon="el-icon-arrow-up"
            style="
              color: #1890ff;
              border-color: #badeff;
              background-color: #e8f4ff;
            "
            circle
            @click="showSearch = false"
          />
        </div>
      </div>
      <el-form
        v-show="showSearch"
        ref="queryForm"
        :model="queryParams"
        :inline="true"
      >
        <div class="first-divider" />
        <el-form-item style="margin: 5px 10px 0 0">
          <el-select
            v-model="queryParams.mainSuperstructureType"
            placeholder="结构形式"
            clearable
            multiple
            collapse-tags
          >
            <el-option
              v-for="dict in dict.type.bridge_main_superstructure_type"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item style="margin: 5px 10px 0 0">
          <el-input
            v-model="queryParams.bridgeCode"
            style="width: 100%"
            placeholder="桥梁编码"
            clearable
          />
        </el-form-item>
        <el-form-item style="margin: 5px 10px 0 0">
          <el-input
            v-model="queryParams.bridgeName"
            style="width: 100%"
            placeholder="桥梁名称"
            clearable
          />
        </el-form-item>
        <el-form-item style="margin: 5px 10px 0 0">
          <el-select
            v-model="queryParams.operationState"
            placeholder="运营状态"
            clearable
            collapse-tags
          >
            <el-option
              v-for="dict in dict.type.sys_operation_state"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item style="margin: 5px 10px 0 0">
          <el-select
            v-model="queryParams.whetherSingleColumnPierType"
            placeholder="是否是独柱墩"
            clearable
          >
            <el-option
              v-for="dict in dict.type.base_data_yes_no"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item style="margin: 5px 10px 0 0">
          <el-select
            v-model="queryParams.spanClassifyType"
            placeholder="跨径分类"
            clearable
            multiple
            collapse-tags
          >
            <el-option
              v-for="dict in dict.type.bridge_span_classify"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item style="margin: 5px 10px 0 0">
          <el-select
            v-model="queryParams.bridgeTechAssessType"
            placeholder="评定等级"
            clearable
            multiple
            collapse-tags
          >
            <el-option
              v-for="dict in dict.type.bridge_tec_condition_level"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item style="margin: 5px 10px 0 0">
          <el-select
            v-model="queryParams.whetherInLongSpanCatalogType"
            placeholder="是否长大桥目录"
            clearable
          >
            <el-option
              v-for="dict in dict.type.base_data_yes_no"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item style="margin: 5px 10px 0 0">
          <el-select
            v-model="queryParams.status"
            placeholder="数据状态"
            clearable
          >
            <el-option
              v-for="dict in dict.type.base_data_state"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item style="margin: 5px 10px 0 0">
          <el-select
            v-model="queryParams.whetherHealthMonitorSystemType"
            placeholder="是否有健康监测系统"
            clearable
          >
            <el-option
              v-for="dict in dict.type.base_data_yes_no"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item style="margin: 5px 10px 0 0">
          <el-select
            v-model="queryParams.ifAdditionalOverpassBridge"
            placeholder="是否为新增上垮桥"
            clearable
          >
            <el-option
              v-for="dict in dict.type.base_data_yes_no"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item style="margin: 5px 10px 0 0">
          <el-select
            v-model="queryParams.ifRebuildAfterDemolition"
            placeholder="是否为拆除还建桥"
            clearable
          >
            <el-option
              v-for="dict in dict.type.base_data_yes_no"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item style="margin: 5px 10px 0 0">
          <el-select
            v-model="queryParams.overpassBridgeType"
            placeholder="跨线桥类型"
            clearable
          >
            <el-option
              v-for="dict in dict.type.overpass_bridge_type"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <!-- <el-form-item label="修建年度" style="margin: 5px 10px 0 0">
          <el-date-picker
            v-model="dateRange"
            type="daterange"
            range-separator="-"
            start-placeholder="年-月-日"
            end-placeholder="年-月-日"
            value-format="yyyy-MM-dd"
          />
        </el-form-item> -->
        <el-form-item label="修建年度" style="margin: 5px 10px 0 0">
          <el-date-picker
            v-model="queryParams.constructionYear"
            type="year"
            value-format="yyyy"
            placeholder="选择年份"
          />
        </el-form-item>
      </el-form>
    </template>
    <template slot="header">
      <div class="button-list">
        <el-button
          v-hasPermi="['baseData:bridge:add']"
          type="primary"
          @click="handleAdd"
          >新增</el-button
        >
        <el-button
          v-hasPermi="['baseData:bridge:edit']"
          type="primary"
          @click="handleUpdate"
          >编辑</el-button
        >
        <el-button
          v-hasPermi="['baseData:bridge:delete']"
          type="primary"
          @click="handleDelete"
          >删除</el-button
        >
        <el-button
          v-hasPermi="['baseData:bridge:info']"
          type="primary"
          @click="handleView"
          >查看</el-button
        >
        <el-button
          v-hasPermi="['baseData:bridge:updateLocked']"
          type="primary"
          @click="handleLocked"
          >是否锁定</el-button
        >
        <el-button
          v-hasPermi="['baseData:businessStatusRecord:add']"
          type="primary"
          @click="changeStatus"
          >运营状态变更</el-button
        >
        <el-button
          v-hasPermi="['baseData:bridge:drawing']"
          type="primary"
          @click="handleViewDrawings"
          >查看图纸</el-button
        >
        <el-button
          v-hasPermi="['baseData:bridge:annalsExport']"
          type="primary"
          @click="exportYearReport"
          >导出年报</el-button
        >
        <el-button
          v-hasPermi="['baseData:bridge:getCard']"
          type="primary"
          @click="handleViewCard"
          >查看卡片</el-button
        >
        <!-- <el-button
          v-hasPermi="['baseData:bridge:selectCard']"
          type="info"
          plain
          @click="handleEditCard"
          disabled
        >编辑卡片</el-button> -->
        <el-button
          v-hasPermi="['baseData:bridge:cardExport']"
          type="primary"
          @click="handleExportCard"
          >导出卡片</el-button
        >
        <!-- <el-button
          v-hasPermi="['baseData:bridge:exportPageTable']"
          type="primary"
          @click="exportList"
        >导出清单</el-button> -->
        <el-button
          v-hasPermi="['baseData:bridge:export']"
          type="primary"
          @click="handleExport"
          >数据导出</el-button
        >
        <el-button
          v-hasPermi="['baseData:import:execute']"
          type="primary"
          @click="importUpdate"
          >导入更新</el-button
        >
        <el-button
          v-hasPermi="['baseData:import:execute']"
          type="primary"
          @click="importAdd"
          >导入新增</el-button
        >
        <!-- <el-button
          type="info"
          plain
          size="mini"
          disabled
          @click="importReport"
        >年报导入</el-button> -->
        <el-button
          v-hasPermi="['baseData:bridge:genQrCode']"
          type="primary"
          @click="downloadQrcode"
          >二维码下载</el-button
        >
        <el-button
          v-hasPermi="['baseData:bridge:submitAudit']"
          type="primary"
          @click="taskBridge"
          >提交审核</el-button
        >
      </div>
    </template>
    <template slot="body">
      <el-table
        v-adjust-table
        ref="table"
        v-loading="loading"
        height="99%"
        border
        :data="staticList"
        :header-cell-style="{ height: '36px' }"
        :row-style="rowStyle"
        @selection-change="handleSelectionChange"
        @row-click="handleRowClick"
      >
        <el-table-column type="selection" width="50" align="center" />
        <el-table-column
          fixed
          label="序号"
          type="index"
          width="50"
          align="center"
        >
          <template v-slot="scope">
            {{
              scope.$index +
              (queryParams.pageNum - 1) * queryParams.pageSize +
              1
            }}
          </template>
        </el-table-column>
        <el-table-column fixed label="操作" align="center" width="80">
          <template slot-scope="scope">
            <el-link
              :disabled="!scope.row.shape"
              type="primary"
              :underline="false"
              @click.stop="handleLocation(scope.row)"
              >定位</el-link
            >
          </template>
        </el-table-column>
        <el-table-column
          fixed
          label="桥梁名称"
          align="center"
          prop="bridgeName"
          min-width="130"
          show-overflow-tooltip
        />
        <el-table-column
          label="桥梁编码"
          align="center"
          prop="bridgeCode"
          min-width="130"
          show-overflow-tooltip
        />
        <el-table-column
          label="养护路段"
          align="center"
          prop="maintenanceSectionName"
          min-width="130"
          show-overflow-tooltip
        />
        <el-table-column
          label="路线编码"
          align="center"
          prop="routeCode"
          min-width="130"
          show-overflow-tooltip
        />
        <el-table-column
          label="数据状态"
          align="center"
          prop="status"
          min-width="130"
        >
          <template slot-scope="{ row }">
            <el-link
              :underline="false"
              :type="
                {
                  1: 'success',
                  2: 'primary',
                  3: 'info',
                  4: 'danger',
                  5: 'success',
                }[row.status]
              "
            >
              <DictTag
                :value="row.status"
                :options="dict.type.base_data_state"
              />
            </el-link>
          </template>
        </el-table-column>
        <el-table-column
          label="管理处"
          align="center"
          prop="managementMaintenanceName"
          min-width="130"
          show-overflow-tooltip
        />
        <el-table-column
          label="管养分处"
          align="center"
          prop="managementMaintenanceBranchName"
          min-width="130"
          show-overflow-tooltip
        />
        <el-table-column
          label="结构形式"
          align="center"
          prop="mainSuperstructureTypeName"
          min-width="130"
          show-overflow-tooltip
        />
        <el-table-column
          label="中心桩号"
          align="center"
          prop="centerStake"
          min-width="130"
          show-overflow-tooltip
        >
          <template slot-scope="scope">
            {{ formatPile(scope.row.centerStake) }}
          </template>
        </el-table-column>
        <el-table-column
          label="桥长"
          align="center"
          prop="totalLength"
          min-width="130"
          show-overflow-tooltip
        />
        <el-table-column
          label="桥梁评定等级"
          align="center"
          prop="bridgeTechAssessTypeName"
          min-width="130"
          show-overflow-tooltip
        />
        <el-table-column
          label="跨径分类"
          align="center"
          prop="spanClassifyTypeName"
          min-width="130"
          show-overflow-tooltip
        />
        <el-table-column
          label="跨径组合"
          align="center"
          prop="spanGroups"
          min-width="130"
          show-overflow-tooltip
        />
        <el-table-column
          label="固定编码"
          align="center"
          prop="fixedCode"
          min-width="130"
          show-overflow-tooltip
        />
        <el-table-column
          label="是否是独柱墩"
          align="center"
          prop="whetherSingleColumnPierTypeName"
          min-width="130"
          show-overflow-tooltip
        >
          <template slot-scope="scope">
            <dict-tag
              :options="dict.type.base_data_yes_no"
              :value="scope.row.whetherSingleColumnPierType"
            />
          </template>
        </el-table-column>
        <el-table-column
          label="运营状态"
          align="center"
          prop="operationStateName"
          min-width="130"
          show-overflow-tooltip
        >
          <template slot-scope="{ row }">
            <el-link
              :underline="false"
              :type="
                { 1: 'info', 2: 'success', 3: 'danger', 4: 'primary' }[
                  row.operationState
                ]
              "
              @click="handleOperational($event, row)"
            >
              <DictTag
                :value="row.operationState"
                :options="dict.type.sys_operation_state"
              />
            </el-link>
          </template>
        </el-table-column>
        <el-table-column
          label="是否锁定"
          align="center"
          prop="isLocked"
          min-width="130"
          show-overflow-tooltip
        >
          <template slot-scope="{ row }">
            <el-link
              :underline="false"
              :type="row.isLocked ? 'danger' : 'info'"
              @click.stop="handleLocked(row)"
            >
              {{ row.isLocked ? "是" : "否" }}
            </el-link>
          </template>
        </el-table-column>
        <el-table-column
          label="是否长大桥梁目录"
          align="center"
          prop="whetherInLongSpanCatalogTypeName"
          min-width="140"
          show-overflow-tooltip
        >
          <template slot-scope="scope">
            <dict-tag
              :options="dict.type.base_data_yes_no"
              :value="scope.row.whetherInLongSpanCatalogType"
            />
          </template>
        </el-table-column>
        <el-table-column
          label="修建年度"
          align="center"
          prop="constructionYear"
          min-width="130"
          show-overflow-tooltip
        />
        <el-table-column label="图片" align="center" width="220">
          <template slot-scope="scope">
            <div style="display: flex; justify-content: space-around">
              <el-button
                :disabled="!scope.row.frontPhotoId"
                type="text"
                @click="handlePhotos(scope.row, 1)"
                >正面照</el-button
              >
              <el-button
                :disabled="!scope.row.facadePhotoId"
                type="text"
                @click="handlePhotos(scope.row, 2)"
                >立面照</el-button
              >
              <el-button
                :disabled="!scope.row.typicalPhotoId"
                type="text"
                @click="handlePhotos(scope.row, 3)"
                >典型照</el-button
              >
            </div>
          </template>
        </el-table-column>
        <el-table-column fixed="right" label="档案" align="center" width="80">
          <template slot-scope="scope">
            <el-button
              v-hasPermi="['baseData:bridge:info']"
              type="text"
              @click.stop="goArchivesPage(scope.row)"
              >查看</el-button
            >
          </template>
        </el-table-column>
      </el-table>
      <pagination
        :total="total"
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
        :pageSizes="[10, 20, 30, 50, 100, 1000]"
        @pagination="getList"
      />
    </template>
    <Form
      v-if="showAddEdit"
      :for-view="forView"
      :form-data="formData"
      :title="title"
      :show-form="showAddEdit"
      @close="
        () => {
          showAddEdit = false;
          formData = {};
        }
      "
      @refresh="
        () => {
          showAddEdit = false;
          getList();
          formData = {};
        }
      "
    />
    <ImportData
      v-if="showImportAdd"
      :is-update="isUpdate"
      :dialog-visible="showImportAdd"
      :import-base-type="importBaseType"
      :import-type="importType"
      @close="closeImportAdd"
    />
    <el-dialog
      :title="imageTitle"
      :visible.sync="showImage"
      width="500px"
      append-to-body
    >
      <ImagePreview :owner-id="imageUrl" width="100%" height="100%" />
    </el-dialog>
    <Drawings
      v-if="showDrawing"
      :showDrawing="showDrawing"
      :typeCode="'BS129'"
      :id="assetIds[0]"
      @close="showDrawing = false"
    />
    <Card
      v-if="showCard"
      :forView="forView"
      :formData="formData"
      :dialogVisible="showCard"
      :bridge-title="formData.areaCode"
      @close="showCard = false"
    ></Card>
    <MapPosition
      v-if="showMapPosition"
      :dialogVisible="showMapPosition"
      :data="mapPositionData"
      @close="showMapPosition = false"
    />
    <drawer-panel
      :show.sync="panelShow"
      :title="panelTitle"
      size="87%"
      @close="onPanelClose"
    >
      <ArchiveIndex :key="archiveKey" />
    </drawer-panel>
  </PageContainer>
</template>

<script>
import {
  listStatic,
  getStatic,
  delStatic,
  changeLockedStatus,
  audit,
  getCard,
} from "@/api/baseData/bridge/baseInfo/index";
import Form from "./form.vue";
import SelectTreeCheckbox from "@/components/DeptTmpl/selectTreeCheckbox";
import ImportData from "@/views/baseData/components/importData/index.vue";
import CascadeSelection from "@/components/CascadeSelection/index.vue";
import lonLat from "@/components/mapPosition/lonLat.vue";
import { statusDialog } from "@/views/baseData/components/statusDialog/index.js";
import { statusListDialog } from "@/views/baseData/components/statusDialog/list.js";
import RangeInput from "@/views/baseData/components/rangeInput/index.vue";
import Drawings from "@/views/baseData/components/drawings/index.vue";
import Card from "./components/card/index.vue";
import MapPosition from "@/components/mapPosition/index.vue";
import DrawerPanel from "@/components/RightPanel/drawer.vue";
import ArchiveIndex from "@/views/baseData/bridge/baseInfo/archives/index.vue";

export default {
  name: "BaseInfo",
  components: {
    ImportData,
    CascadeSelection,
    SelectTreeCheckbox,
    RangeInput,
    Form,
    Drawings,
    Card,
    MapPosition,
    DrawerPanel,
    ArchiveIndex,
    lonLat,
  },
  dicts: [
    "bridge_tec_condition_level",
    "bridge_main_superstructure_type",
    "base_data_yes_no",
    "bridge_span_classify",
    "sys_operation_state",
    "base_data_state",
    "overpass_bridge_type",
  ],
  data() {
    return {
      loading: true,
      showAddEdit: false,
      forView: false,
      clearData: false,
      title: "",
      importBaseType: "1",
      importType: null,
      formData: "",
      ids: [],
      showSearch: false,
      total: 0,
      staticList: null,
      queryParams: {
        pageNum: 1,
        pageSize: 20,
        operationState: "2",
      },
      dateRange: [],
      maintenanceOptions: [], // 养护路段options
      routeCodeOptions: [], // 路线编码options
      imageUrl: "",
      imageTitle: "",
      showImage: false,
      showImportAdd: false,
      showCard: false,
      isUpdate: false,
      selectdTables: [],
      showDrawing: false,
      showMapPosition: false,
      mapPositionData: undefined,
      assetIds: [],
      panelShow: false,
      panelTitle: "桥梁档案",
      archiveKey: new Date().getTime(),
    };
  },
  watch: {},
  created() {
    this.getList();
  },
  methods: {
    // 获取表格数据
    getList() {
      this.loading = true;
      if (this.dateRange?.length > 0) {
        this.queryParams.buildDateStart = this.dateRange[0];
        this.queryParams.buildDateEnd = this.dateRange[1];
      } else {
        this.queryParams.buildDateStart = "";
        this.queryParams.buildDateEnd = "";
      }
      listStatic(this.queryParams)
        .then((response) => {
          this.staticList = response.rows;
          this.total = response.total;
          this.loading = false;
          this.clearData = false;
        })
        .catch(() => {
          this.loading = false;
        });
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.selectdTables = selection;
      this.ids = selection.map((item) => item.id);
      this.assetIds = selection.map((item) => item.assetId);
    },
    // 表格点击勾选
    handleRowClick(row) {
      row.isSelected = !row.isSelected;
      this.$refs.table.toggleRowSelection(row);
    },
    // 勾选高亮
    rowStyle({ row, rowIndex }) {
      if (this.ids.includes(row.id)) {
        return { "background-color": "#b7daff", color: "#333" };
      } else {
        return { "background-color": "#fff", color: "#333" };
      }
    },
    // 搜索按钮
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    // 重置按钮
    resetQuery() {
      this.dateRange = [];
      this.clearData = true;
      this.queryParams = {
        pageNum: 1,
        pageSize: 20,
        operationState: "2",
      };
      this.handleQuery();
    },
    // 新增按钮操作
    handleAdd() {
      this.forView = false;
      this.showAddEdit = true;
      this.title = "新增桥梁静态数据";
    },
    // 编辑按钮
    handleUpdate() {
      if (this.ids.length !== 1) {
        this.$message.warning("请选择一条数据进行编辑！");
        return;
      } else {
        this.forView = false;
        let canEdit = true;

        let e = this.selectdTables[0];

        if (e.isLocked || e.status == 3) {
          canEdit = false;
          this.$message.error(
            "桥梁名称：" + e.bridgeName + "已锁定或正在审核，不允许编辑！"
          );
        }

        if (canEdit) {
          getStatic(this.ids[0]).then((res) => {
            if (res && res.data) {
              this.formData = res.data;
              this.showAddEdit = true;
              this.title = "修改桥梁静态数据";
            }
          });
        }
      }
    },
    // 删除按钮
    handleDelete() {
      if (this.ids.length === 0) {
        this.$message.warning("请选择至少一条数据进行删除！");
        return;
      }
      let canDel = true;
      this.ids.map((el) => {
        if (!canDel) {
          return;
        }

        let e = this.selectdTables[0];

        if (e.isLocked || e.status == 3) {
          canDel = false;
          this.$message.error(
            "桥梁名称：" + e.bridgeName + "已锁定或正在审核，不允许删除！"
          );
        }
      });
      if (canDel) {
        this.$modal
          .confirm("确认删除？")
          .then(() => {
            delStatic(this.ids).then((res) => {
              if (res.code === 200) {
                this.getList();
                this.$modal.msgSuccess("删除成功");
              }
            });
          })
          .catch(() => {});
      }
    },
    exportYearReport() {
      if (this.ids.length === 0) {
        this.$modal
          .confirm(
            "即将导出所有表格年报数据，此过程可能花费时间较长，是否继续？"
          )
          .then(() => {
            this.download(
              "/baseData/bridge/annalsExport",
              {
                ...this.queryParams,
              },
              `bridge_report_${new Date().getTime()}.xlsx`,
              {
                headers: {
                  "Content-Type": "application/json;",
                  "Is-Annals-Export": true,
                },
                parameterType: "body",
              }
            );
          })
          .catch(() => {});
      } else {
        this.$modal
          .confirm(`已选择${this.ids.length}条桥梁数据，确认导出年报？`)
          .then(() => {
            this.download(
              "/baseData/bridge/annalsExport",
              {
                ids: this.ids,
              },
              `bridge_report_${new Date().getTime()}.xlsx`,
              {
                headers: {
                  "Content-Type": "application/json;",
                  "Is-Annals-Export": true,
                },
                parameterType: "body",
              }
            );
          })
          .catch(() => {});
      }
    },
    // 导出清单按钮
    exportList() {
      if (this.ids.length === 0) {
        this.$modal
          .confirm("即将导出所有表格数据，此过程可能花费时间较长，是否继续？")
          .then(() => {
            this.download(
              "/baseData/bridge/exportPageTable",
              this.queryParams,
              `road_interflow_${new Date().getTime()}.xlsx`,
              {
                headers: { "Content-Type": "application/json;" },
                parameterType: "body",
              }
            );
          })
          .catch(() => {});
      } else {
        this.$modal
          .confirm(`已选择${this.ids.length}条桥梁数据，确认导出清单？`)
          .then(() => {
            this.download(
              "/baseData/bridge/exportPageTable",
              { ids: this.ids },
              `static_${new Date().getTime()}.xlsx`,
              {
                headers: { "Content-Type": "application/json;" },
                parameterType: "body",
              }
            );
          })
          .catch(() => {});
      }
    },
    // 导入年报按钮
    importReport() {},
    // 二维码下载
    downloadQrcode() {
      if (this.ids.length === 0) {
        this.$modal
          .confirm(
            "即将下载所有表格的二维码数据，此过程可能花费时间较长，是否继续？"
          )
          .then(() => {
            this.download(
              "/baseData/bridge/genQrCode",
              this.queryParams,
              `QrCode_${new Date().getTime()}`,
              {
                headers: { "Content-Type": "application/json;" },
                parameterType: "body",
              }
            );
          })
          .catch(() => {});
      } else {
        this.$modal
          .confirm(`已选择${this.ids.length}条桥梁数据，是否下载二维码？`)
          .then(() => {
            let data = {
              ...this.queryParams,
              ids: this.ids,
            };
            this.download(
              "/baseData/bridge/genQrCode",
              data,
              `QrCode_${new Date().getTime()}`,
              {
                headers: { "Content-Type": "application/json;" },
                parameterType: "body",
              }
            );
          })
          .catch(() => {});
      }
    },
    // 查看按钮
    handleView() {
      if (this.ids.length !== 1) {
        this.$message.warning("请选择一条数据！");
        return;
      } else {
        getStatic(this.ids[0])
          .then((res) => {
            if (res && res.data) {
              this.formData = res.data;
              this.forView = true;
              this.showAddEdit = true;
              this.title = "查看桥梁静态数据";
            }
          })
          .catch(() => {});
      }
    },
    taskBridge() {
      //this.selectdTables数组中的status，只要有一个不是2或者4.就不能提交审核
      let flag = false;
      this.selectdTables.forEach((item, index) => {
        if (item.status != 2 && item.status != 4 && item.isLocked) {
          flag = true;
        }
      });
      if (this.ids.length === 0) {
        this.$message.warning("请选择至少一条数据进行审核！");
        return;
      } else if (flag) {
        this.$message.warning("此数据无法提交审核！");
        return;
      }
      this.$modal
        .confirm("确认审核？")
        .then(() => {
          let list = [];

          this.selectdTables.forEach((item, index) => {
            list.push({
              businessKey: item.id,
              variables: {
                managementMaintenanceId:item.managementMaintenanceId,
                managementMaintenanceBranchId:item.managementMaintenanceBranchId,
                maintenanceSectionId:item.maintenanceSectionId,
                assetId: item.assetId,
                type: "8",
                assetCode: item.bridgeCode,
                assetName: item.bridgeName,
                routeCode: item.routeCode,
                routeName: item.routeName,
                maintenanceSectionName: item.maintenanceSectionName,
                managementMaintenanceName: item.managementMaintenanceName,
                managementMaintenanceBranchName:
                  item.managementMaintenanceBranchName,
              },
            });
          });

          audit({
            businessDTOS: list,
            processDefinitionKey: "base_data_audit",
          }).then((res) => {
            if (res.code === 200) {
              this.getList();
              this.$modal.msgSuccess("审核成功");
            }
          });
        })
        .catch(() => {});
    },
    // 查看卡片按钮
    handleViewCard() {
      if (this.ids.length !== 1) {
        this.$message.warning("请选择一条数据！");
        return;
      } else {
        // getStatic(this.ids[0]).then(res => {
        //   if (res && res.data) {
        //     this.formData = res.data
        //     this.forView = true
        //     this.showCard = true
        //   }
        // })

        getCard({ ids: [this.ids[0]] }).then((res) => {
          if (res.length > 0) {
            this.formData = res[0];
            this.forView = true;
            this.showCard = true;
          }
        });
      }
    },
    // 编辑卡片按钮
    handleEditCard() {
      if (this.ids.length !== 1) {
        this.$message.warning("请选择一条数据！");
        return;
      } else {
        this.forView = false;
        this.showCard = true;
      }
    },
    // 导出卡片按钮
    handleExportCard() {
      if (this.ids.length === 0) {
        this.$message.warning("请选择一条数据！");
      } else {
        this.$modal
          .confirm(`已选择${this.ids.length}条桥梁数据，确认导出数据？`)
          .then(() => {
            this.download(
              "/baseData/bridge/cardExport",
              { ids: this.ids },
              `static_${new Date().getTime()}.xlsx`,
              {
                headers: { "Content-Type": "application/json;" },
                parameterType: "body",
              }
            );
          })
          .catch(() => {});
      }
    },
    // 查看图纸按钮
    handleViewDrawings() {
      if (this.ids.length !== 1) {
        this.$message.warning("请选择一条数据！");
        return;
      } else {
        this.showDrawing = true;
      }
    },
    // 运营状态变更按钮
    changeStatus() {
      if (this.ids.length !== 1) {
        this.$message.warning("请选择一条数据！");
        return;
      } else {
        // baseDataType 基础数据类型 1桥梁 2隧道 3涵洞 4互通
        statusDialog({ dataId: this.assetIds[0], baseDataType: 1 }).then(() => {
          this.getList();
        });
      }
    },
    // 数据导出按钮
    handleExport() {
      if (this.ids.length === 0) {
        this.$modal
          .confirm("即将导出所有表格数据，此过程可能花费时间较长，是否继续？")
          .then(() => {
            this.download(
              "/baseData/bridge/export",
              this.queryParams,
              `road_interflow_${new Date().getTime()}.xlsx`,
              {
                headers: { "Content-Type": "application/json;" },
                parameterType: "body",
              }
            );
          })
          .catch(() => {});
      } else {
        this.$modal
          .confirm(`已选择${this.ids.length}条桥梁数据，确认导出数据？`)
          .then(() => {
            this.download(
              "/baseData/bridge/export",
              { ids: this.ids },
              `static_${new Date().getTime()}.xlsx`,
              {
                headers: { "Content-Type": "application/json;" },
                parameterType: "body",
              }
            );
          })
          .catch(() => {});
      }
    },
    // 导入更新按钮
    importUpdate() {
      this.isUpdate = true;
      this.showImportAdd = true;
      this.importType = 1;
    },
    // 导入新增按钮
    importAdd() {
      this.isUpdate = false;
      this.showImportAdd = true;
      this.importType = 2;
    },
    closeImportAdd(v) {
      this.showImportAdd = false;
      if (v) this.getList();
    },
    // 是否锁定按钮
    handleLocked(row) {
      if (this.ids.length === 0 && !row.id) {
        this.$message.warning("请选择至少一条数据！");
        return;
      }
      let arr = [];
      let ids = [];
      if (row.id) {
        arr = [row];
        ids = [row.id];
      } else {
        arr = JSON.parse(JSON.stringify(this.selectdTables));
        ids = this.ids;
      }
      const allTrue = arr.every((table) => table.isLocked === true);
      const allFalse = arr.every((table) => table.isLocked === false);
      if (allTrue || allFalse) {
        this.$modal
          .confirm(allFalse ? "确认锁定？" : "确认解锁？")
          .then(() => {
            changeLockedStatus(ids).then((res) => {
              if (res.code === 200) {
                this.getList();
                this.$modal.msgSuccess("操作成功");
              }
            });
          })
          .catch(() => {});
      } else {
        this.$message.warning("请选择锁定状态相同的数据进行操作！");
      }
    },
    // 表格操作-定位
    handleLocation(row) {
      // const objString = JSON.stringify(row);
      // this.$router.push({ path: `/static/map/?data=${encodeURIComponent(objString)}` });
      // this.$router.push(
      //   `/static/map?id=${row.id}&shape=${row.shape}&name=${row.bridgeName}`
      // )
      this.mapPositionData = row;
      this.showMapPosition = true;
    },
    // 表格操作-运营状态
    handleOperational(event, row) {
      event.stopPropagation();
      statusListDialog({ dataId: row.assetId, baseDataType: 1 });
    },
    // 表格操作-正面照、立面照、典型照
    handlePhotos(row, type) {
      this.showImage = true;
      switch (type) {
        case 1:
          this.imageUrl = row.frontPhotoId;
          this.imageTitle = "桥梁正面照";
          break;
        case 2:
          this.imageUrl = row.facadePhotoId;
          this.imageTitle = "桥梁立面照";
          break;
        case 3:
          this.imageUrl = row.typicalPhotoId;
          this.imageTitle = "桥梁典型照片";
          break;
      }
    },
    // 表格操作-档案查看
    goArchivesPage(row) {
      let { path } = this.$route;

      let query = {
        bridgeName: row.bridgeName,
        id: row.id,
        assetId: row.assetId,
        assetCode:row.assetCode,
        assetName:row.assetName,
        whetherHealthMonitorSystemType: row.whetherHealthMonitorSystemType,
      };
      this.$router.push({
        path,
        query,
      });
      // 刷新组件
      this.archiveKey = new Date().getTime();
      this.panelShow = true;
      return;
      this.$router.push(
        `baseInfo/archives?bridgeName=${row.bridgeName}&id=${row.id}&whetherHealthMonitorSystemType=${row.whetherHealthMonitorSystemType}`
      );
    },
    onPanelClose() {
      this.panelShow = false;
      let { path } = this.$route;
      this.$router.push({
        path,
        query: {},
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.first-divider {
  width: 100%;
  height: 1px;
  //border-bottom: 1px solid #dcdfe6;
  //margin: 0 0 5px 0 !important;
}

// 滚动条的宽度
::v-deep .el-table__body-wrapper::-webkit-scrollbar {
  height: 10px; // 纵向滚动条 必写
}

::v-deep .el-table__fixed {
  height: auto !important;
  bottom: 10px !important;
}

::v-deep .el-table__fixed-right {
  height: auto !important;
  bottom: 10px !important;
}

::v-deep .el-table__body-wrapper {
  z-index: 2;
}

.button-list {
  border-radius: 4px;
  width: 100%;
  .el-button {
    margin-bottom: 10px;
    margin-right: 10px;
    margin-left: 0;
  }
}
</style>
