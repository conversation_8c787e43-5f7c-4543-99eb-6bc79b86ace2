<template>
  <PageContainer>
    <template slot="search">
      <div style="display: flex; align-items: center; margin: 0">
        <CascadeSelectionManagementOffice
          style="min-width:192px;margin-right: 10px;"
          :form-data="queryParams"
          v-model="queryParams"
          types="201"
          multiple
        />

        <el-button
          v-hasPermi="['baseData:roadsectionStatistics:getStatistics']"
          type="primary"
          icon="el-icon-search"
          @click="handleQuery"
        >搜索</el-button>
        <el-button
          icon="el-icon-refresh"
          @click="resetQuery"
        >重置</el-button>
      </div>
    </template>
    <template slot="header">
      <el-button
        v-hasPermi="['baseData:roadsectionStatistics:export']"
        style="margin-bottom: 10px;"
        type="primary"
        @click="exportList"
      >导出报表</el-button>
    </template>
    <template slot="body">
      <!-- <div class="body">
      </div> -->
      <el-table
        v-adjust-table
        ref="table"
        v-loading="loading"
        height="100%"
        border
        :span-method="objectSpanMethod"
        :data="tableData"
        :header-cell-style="{'height': '36px'}"
        :row-class-name="tableRowClassName"
      >

        <el-table-column
          fixed
          label="序号"
          type="index"
          width="50"
          align="center"
        />

        <el-table-column
          fixed
          label="养护路段"
          align="center"
          prop="maintenanceSectionName"
          min-width="120"
          show-overflow-tooltip
        />
        <el-table-column
          label="产权单位"
          align="center"
          prop="unit"
          min-width="120"
          show-overflow-tooltip
        />
        <el-table-column
          label="管理处名称"
          align="center"
          prop="managementMaintenanceName"
          min-width="120"
          show-overflow-tooltip
        />
        <el-table-column
          label="通车时间"
          align="center"
          prop="buildOpenedDate"
          min-width="120"
          show-overflow-tooltip
        />
        <el-table-column
          label="路龄(年)"
          align="center"
          prop="roadYears"
          min-width="120"
          show-overflow-tooltip
        />
        <el-table-column
          label="路线编码"
          align="center"
          prop="routeCode"
          min-width="120"
          show-overflow-tooltip
        />
        <el-table-column
          label="路线全称"
          align="center"
          prop="routeFullName"
          min-width="120"
          show-overflow-tooltip
        />
        <el-table-column
          label="路线简称"
          align="center"
          prop="routeName"
          min-width="120"
          show-overflow-tooltip
        />
        <el-table-column
          label="行政等级"
          align="center"
          prop="administrativeLevel"
          min-width="120"
          show-overflow-tooltip
        >
          <template slot-scope="scope">
            <dict-tag
              :options="dict.type.sys_route_nature"
              :value="scope.row.administrativeLevel"
            />
          </template>
        </el-table-column>
        <el-table-column
          label="技术等级"
          align="center"
          prop="routeLevel"
          min-width="120"
          show-overflow-tooltip
        >
          <template slot-scope="scope">
            <dict-tag
              :options="dict.type.sys_route_grade"
              :value="scope.row.routeLevel"
            />
          </template>
        </el-table-column>
        <el-table-column
          label="车道数量"
          align="center"
          prop="lanes"
          min-width="120"
          show-overflow-tooltip
        >
          <!-- <template slot-scope="scope">
            <dict-tag
              :options="dict.type.lane"
              :value="scope.row.lane"
            />
          </template> -->
        </el-table-column>
        <el-table-column
          label="起止点地名"
          align="center"
          prop="startEndNames"
          min-width="120"
          show-overflow-tooltip
        />
        <el-table-column
          label="实际管养"
          align="center"
        >
          <el-table-column
            label="起点桩号"
            align="center"
            prop="startStake"
            min-width="120"
          >
            <template slot-scope="scope">
              {{ formatPile(scope.row.startStake) }}
            </template>
          </el-table-column>
          <el-table-column
            label="止点桩号"
            align="center"
            prop="endStake"
            min-width="120"
          >
            <template slot-scope="scope">
              {{ formatPile(scope.row.endStake) }}
            </template>
          </el-table-column>
          <el-table-column
            label="管养里程(km)"
            align="center"
            prop="maintenanceMileage"
            min-width="120"
          />
        </el-table-column>
        <el-table-column
          label="年报数据"
          align="center"
        >
          <el-table-column
            label="起点桩号"
            align="center"
            prop="yearStartStake"
            min-width="120"
          >
            <template slot-scope="scope">
              {{ formatPile(scope.row.yearStartStake) }}
            </template>
          </el-table-column>

          <el-table-column
            label="止点桩号"
            align="center"
            prop="yearEndStake"
            min-width="120"
          >
            <template slot-scope="scope">
              {{ formatPile(scope.row.yearEndStake) }}
            </template>
          </el-table-column>
          <el-table-column
            label="年报里程(km)"
            align="center"
            prop="yearMaintenanceMileage"
            min-width="120"
          />
        </el-table-column>
        <el-table-column
          label="桥梁"
          align="center"
        >
          <el-table-column
            label="座数"
            align="center"
            prop="bridgeTotalCount"
            min-width="120"
          >
            <template slot-scope="{ row }">
              <el-link

                type="primary"
                :underline="false"
                @click="handleConditionLevel({...row, clickName: '座数'}, [], row.bridgeTotalCount,'DetailTableBridge')"
              > {{row.bridgeTotalCount}} </el-link>
            </template>
          </el-table-column>
          <el-table-column
            label="长度(m)"
            align="center"
            prop="bridgeTotalLength"
            min-width="120"
          />
          <el-table-column
            label="特大桥座数"
            align="center"
            prop="superMajorBridge"
            min-width="120"
          >
            <template slot-scope="{ row }">
              <el-link

                type="primary"
                :underline="false"
                @click="handleConditionLevel({...row, clickName: '特大桥'}, ['1'], row.superMajorBridge,'DetailTableBridge')"
              > {{row.superMajorBridge}} </el-link>
            </template>
          </el-table-column>
          <el-table-column
            label="特大桥长度(m)"
            align="center"
            prop="superMajorBridgeLength"
            min-width="160"
          />
          <el-table-column
            label="大桥座数"
            align="center"
            prop="greatBridge"
            min-width="120"
          >
            <template slot-scope="{ row }">
              <el-link

                type="primary"
                :underline="false"
                @click="handleConditionLevel({...row, clickName: '大桥'}, ['2'], row.superMajorBridge,'DetailTableBridge')"
              > {{row.greatBridge}} </el-link>
            </template>
          </el-table-column>
          <el-table-column
            label="大桥长度(m)"
            align="center"
            prop="greatBridgeLength"
            min-width="120"
          />
          <el-table-column
            label="中桥座数"
            align="center"
            prop="mediumBridge"
            min-width="120"
          >
            <template slot-scope="{ row }">
              <el-link

                type="primary"
                :underline="false"
                @click="handleConditionLevel({...row, clickName: '中桥'}, ['3'], row.mediumBridge,'DetailTableBridge')"
              > {{row.mediumBridge}} </el-link>
            </template>
          </el-table-column>
          <el-table-column
            label="中桥长度(m)"
            align="center"
            prop="mediumBridgeLength"
            min-width="120"
          />
          <el-table-column
            label="小桥座数"
            align="center"
            prop="smallBridge"
            min-width="120"
          >
            <template slot-scope="{ row }">
              <el-link

                type="primary"
                :underline="false"
                @click="handleConditionLevel({...row, clickName: '小桥'}, ['4'], row.smallBridge,'DetailTableBridge')"
              > {{row.smallBridge}} </el-link>
            </template>
          </el-table-column>
          <el-table-column
            label="小桥长度(m)"
            align="center"
            prop="smallBridgeLength"
            min-width="120"
          />
        </el-table-column>
        <el-table-column
          label="隧道"
          align="center"
        >
          <el-table-column
            label="座数"
            align="center"
            prop="tunnelTotalCount"
            min-width="120"
          >
            <template slot-scope="{ row }">
              <el-link
                type="primary"
                :underline="false"
                @click="handleConditionLevel({...row, clickName: '座数'}, '1,2,3,4', row.tunnelTotalCount,'DetailTable')"
              > {{row.tunnelTotalCount}} </el-link>
            </template>
          </el-table-column>
          <el-table-column
            label="长度(m)"
            align="center"
            prop="tunnelTotalLength"
            min-width="120"
          >

          </el-table-column>
          <el-table-column
            label="特长隧道座数"
            align="center"
            prop="superMajorTunnel"
            min-width="120"
          >
            <template slot-scope="{ row }">
              <el-link
                type="primary"
                :underline="false"
                @click="handleConditionLevel({...row, clickName: '特长隧道'}, '1', row.superMajorTunnel,'DetailTable')"
              > {{row.superMajorTunnel}} </el-link>
            </template>
          </el-table-column>
          <el-table-column
            label="特长隧道长度(m)"
            align="center"
            prop="superMajorTunnelLength"
            min-width="160"
          />
          <el-table-column
            label="长隧道座数"
            align="center"
            prop="greatTunnel"
            min-width="160"
          >
            <template slot-scope="{ row }">
              <el-link
                type="primary"
                :underline="false"
                @click="handleConditionLevel({...row, clickName: '长隧道'}, '2', row.greatTunnel,'DetailTable')"
              > {{row.greatTunnel}} </el-link>
            </template>
          </el-table-column>
          <el-table-column
            label="长隧道长度(m)"
            align="center"
            prop="greatTunnelLength"
            min-width="160"
          />
          <el-table-column
            label="中隧道座数"
            align="center"
            prop="mediumTunnel"
            min-width="160"
          >
            <template slot-scope="{ row }">
              <el-link
                type="primary"
                :underline="false"
                @click="handleConditionLevel({...row, clickName: '中隧道'}, '3', row.mediumTunnel,'DetailTable')"
              > {{row.mediumTunnel}} </el-link>
            </template>
          </el-table-column>
          <el-table-column
            label="中隧道长度(m)"
            align="center"
            prop="mediumTunnelLength"
            min-width="160"
          />
          <el-table-column
            label="短隧道座数"
            align="center"
            prop="smallTunnel"
            min-width="120"
          >
            <template slot-scope="{ row }">
              <el-link
                type="primary"
                :underline="false"
                @click="handleConditionLevel({...row, clickName: '短隧道'}, '4', row.smallTunnel,'DetailTable')"
              > {{row.smallTunnel}} </el-link>
            </template>
          </el-table-column>
          <el-table-column
            label="短隧道长度(m)"
            align="center"
            prop="smallTunnelLength"
            min-width="160"
          />
        </el-table-column>
        <el-table-column
          label="桥隧管理站、所(个)"
          align="center"
          prop="bridgeTunnelManager"
          min-width="160"
        >
          <template slot-scope="{ row }">
            <el-link

              type="primary"
              :underline="false"
              @click="handleConditionLevel({...row, clickName: '桥隧管理站、所(个)'}, '195', row.bridgeTunnelManager,'StatisticDetailTable')"
            > {{row.bridgeTunnelManager}} </el-link>
          </template>
        </el-table-column>
        <el-table-column
          label="应急物资仓库(个)"
          align="center"
          prop="emergencyWarehouse"
          min-width="160"
        >
        <template slot-scope="{ row }">
            <!-- <el-link
              type="primary"
              :underline="false"
              @click="handleConditionLevel({...row, clickName: '应急物资仓库(个)'}, '195', row.emergencyWarehouse,'StatisticDetailTable')"
            >
              {{row.emergencyWarehouse}}
            </el-link> -->
            <span>{{row.emergencyWarehouse}}</span>
          </template>

        </el-table-column>
        <el-table-column
          label="隧道变电站"
          align="center"
        >
          <el-table-column
            label="数量(个)"
            align="center"
            prop="transformer"
            min-width="120"
          >
            <template slot-scope="{ row }">
              <el-link

                type="primary"
                :underline="false"
                @click="handleConditionLevel({...row, clickName: '隧道变电站'}, '197', row.transformer,'StatisticDetailTable')"
              > {{row.transformer}} </el-link>
            </template>
          </el-table-column>
          <el-table-column
            label="有人值守(个)"
            align="center"
            prop="someoneDuty"
            min-width="120"
          />
          <el-table-column
            label="无人值守(个)"
            align="center"
            prop="nobodyDuty"
            min-width="120"
          />
        </el-table-column>
        <el-table-column
          label="收费站(个)"
          align="center"
          prop="tollStations"
          min-width="120"
        >
          <template slot-scope="{ row }">
            <el-link

              type="primary"
              :underline="false"
              @click="handleConditionLevel({...row, clickName: '收费站(个)'}, '26', row.tollStations,'StatisticDetailTable')"
            > {{row.tollStations}} </el-link>
          </template>
        </el-table-column>
        <el-table-column
          label="服务区(座)"
          align="center"
          prop="serviceArea"
          min-width="120"
        >
          <template slot-scope="{ row }">
            <el-link

              type="primary"
              :underline="false"
              @click="handleConditionLevel({...row, clickName: '服务区(座)'}, '27', row.serviceArea,'StatisticDetailTable')"
            > {{row.serviceArea}} </el-link>
          </template>
        </el-table-column>
        <el-table-column
          label="备注"
          align="center"
          prop="remark"
          min-width="120"
        />
      </el-table>
      <!-- <pagination
        :total="total"
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
        :pageSizes="[10, 20, 30, 50, 100, 1000]"
        @pagination="getList"
      /> -->
    </template>

    <component
      :is="listType"
      v-if="showTable"
      :showTable="showTable"
      :row="row"
      :params="params"
      @close="closeTable"
    />
    <!-- <DetailTable
      v-if="showTable"
      :showTable="showTable"
      :row="row"
      :params="params"
      @close="closeTable"
    /> -->
  </PageContainer>
</template>

<script>
import CascadeSelectionManagementOffice from '@/components/CascadeSelectionManagementOffice/index.vue'
import { getStatistics } from '@/api/baseData/road/statistics/index'
import DetailTable from './components/DetailTable.vue'
import DetailTableBridge from './components/DetailTableBridge.vue'
import StatisticDetailTable from './components/StatisticDetailTable.vue'

export default {
  name: 'Statistics',
  props: {},
  dicts: ['sys_operation_state','sys_route_nature','sys_route_grade'],
  components: { CascadeSelectionManagementOffice, DetailTable,DetailTableBridge,StatisticDetailTable },
  data() {
    return {

      listType: 'DetailTable',
      tableData: [],
      loading: false,
      cellList: [],
      queryParams:{
        pageNum: 1,
        pageSize: 20,
      },
      count: null,
      showTable: false,
      row: {},
      params: {}
    }
  },
  created() {
    this.getList()
  },
  methods: {
    handleQuery() {
      this.getList()
    },
    // 重置按钮
    resetQuery() {
      this.queryParams = {
        pageNum: 1,
        pageSize: 20
      }
      this.handleQuery()
    },
    getList() {
      this.loading = true
      this.tableData = []
      this.cellList = []
      this.count = null
      getStatistics(this.queryParams)
        .then(res => {
          if (res) {
            this.tableData = res
            this.computeCell(this.tableData)
            this.$nextTick(() => {
              this.$refs.table.doLayout(); // yourTableRef 是 el-table 的 ref 名称
            });
          }
        })
        .finally(() => {
          this.loading = false
        })
    },
    computeCell(table) {
      for (let index = 0; index < table.length; index++) {
        if (index == 0) {
          this.cellList.push(1)
          this.count = 0
        } else {
          if (
            table[index].maintenanceSectionName ==
            table[index - 1].maintenanceSectionName
          ) {
            this.cellList[this.count] += 1
            this.cellList.push(0)
          } else {
            this.cellList.push(1)
            this.count = index
          }
        }
      }
    },
    objectSpanMethod({ row, column, rowIndex, columnIndex }) {
      if (columnIndex === 1) {
        const rowCell = this.cellList[rowIndex]
        if (rowCell > 0) {
          const colCell = 1.
          return {
            rowspan: rowCell,
            colspan: colCell
          }
        } else {
          return {
            rowspan: 0,
            colspan: 0
          }
        }
      }
    },
    exportList() {
      this.$modal
        .confirm('导出所有表格数据，是否继续？')
        .then(() => {
          this.download(
            '/baseData/roadsection/statistics/export',
            this.queryParams,
            `路段信息统计_${new Date().getTime()}.xlsx`,
            {
              headers: { 'Content-Type': 'application/json;' },
              parameterType: 'body'
            }
          )
        })
        .catch(() => {})
    },

    handleConditionLevel(row, list, Num,type) {

      this.listType=type
      this.showTable = true
      this.row = row


      if(type=='StatisticDetailTable'){
      this.params = {  typeId: list, segmentStakeStart:row.startStake,
        segmentStakeEnd:row.endStake,  maintenanceSectionIds:row.sectionIds,maintenanceSectionId:row.sectionIds?.[0],...this.queryParams}
      }else if(type=='DetailTableBridge'){
        this.params = {  spanClassifyType: list, centerStakeStart:row.startStake,
          centerStakeEnd:row.endStake,...this.queryParams }
      }else{
        if (list === '1,2,3,4' && this.row.maintenanceSectionName !== "总计") {
            this.params = {  lengthClassifications: ['1','2','3','4'], segmentStakeStart:row.startStake,
              segmentStakeEnd:row.endStake,...this.queryParams }
        } else {
            this.params = {  lengthClassification: list, segmentStakeStart:row.startStake,
              segmentStakeEnd:row.endStake,...this.queryParams }
        }
      }

    },
    closeTable() {
      this.showTable = false
      this.row = {}
    },
    tableRowClassName(row_params) {
      let { row } = row_params
      if (row.maintenanceSectionName == '总计') {
        return `tr-fixed fixed-row`
      } else {
        return ``
      }
    }
  },
  computed: {},
  watch: {},
}
</script>

<style lang="scss" scoped>
.body {
  height: calc(100vh - 230px);
  overflow-y: auto;
}

::v-deep .el-table .el-table__fixed-body-wrapper{
    top: 72px !important;
}

::v-deep .el-table {
  .tr-fixed {
    display: table-row;
    position: sticky;
    bottom: 0;
    width: 100%;
    td {
      border-top: 1px solid #f3f5fa;
      background: #fff;
    }
  }

  .fixed-row {
    bottom: 0;
  }
}
</style>
