<template>
  <div class='app-container'>
    <el-row :gutter='20'>
      <!--角色数据-->
      <el-col :span='24' :xs='24'>
        <el-row :gutter='10' class='mb8'>
          <el-col :span='1.5'>
            <el-button
              icon='el-icon-upload'
              size='mini'
              type='success'
              @click='handleUpload'
            >上传文件
            </el-button>
          </el-col>
        </el-row>
        <!-- 筛选区结束 -->
        <!-- 数据表格开始 -->
        <div class='tableDiv'>
          <el-table v-adjust-table v-loading='loading' :data='dataList'
                    :height="'calc(100vh - 260px)'" border size='mini'
                    @selection-change='handleSelectionChange'>
            <el-table-column align='center' fixed label='序号' type='index' width='100'></el-table-column>
            <el-table-column align='left' label='模板名称' prop='templateName'>
              <template slot-scope='scope'>
                <el-link
                  type="primary"
                  class='link-type'
                  @click='handleRowClick(scope.row)'
                >{{ scope.row.templateName }}
                </el-link>
              </template>
            </el-table-column>
            <el-table-column align='center' fixed='right' label='操作' width='150'>
              <template slot-scope='scope'>
                <el-button
                  icon='el-icon-edit'
                  size='mini'
                  type='text'
                  @click='handleEdit(scope.row)'
                >编辑
                </el-button>
                <el-button
                  icon='el-icon-delete'
                  size='mini'
                  type='text'
                  @click='handleDelete(scope.row)'
                >删除
                </el-button>
              </template>
            </el-table-column>
          </el-table>
          <pagination
            v-show='total > 0'
            :limit.sync='queryParams.pageSize'
            :page.sync='queryParams.pageNum'
            :total='total'
            @pagination='handleQuery'
          />
        </div>
        <!-- 数据表格结束 -->
      </el-col>
    </el-row>
    <el-dialog dialogTitle='上传文件' v-if='fileVisible' :visible.sync='fileVisible' width='500px' append-to-body>
      <el-form ref='form' :model='formData' label-width='80px'>
        <el-form-item label='模板名称' prop='templateName'>
          <el-input v-model='formData.templateName' placeholder='请输入名称'></el-input>
        </el-form-item>
        <el-form-item label='' prop='templatePath' v-if='!formData.id'>
          <file-upload platform='jgjc' ref='file' v-model="formData.templatePath" :owner-id='formData.templatePath' :file-type='["zip"]' :is-show-tip='false' :limit='1'></file-upload>
          <div  class="el-upload__tip">
            <div>请上传zip压缩文件，压缩包内需包含以下三个文件：</div>
            <div>1.template.docx</div>
            <div>2.document.xml</div>
            <div>3.document.xml.rels</div>
            <div>且文件名和路径中不能包括中文字符。</div>
          </div>
        </el-form-item>
      </el-form>
      <div style="text-align: right">
        <el-button type='primary' @click='handleSaveFile'>确 定</el-button>
        <el-button @click='fileVisible = false'>取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getFile } from '@/api/file'
import axios from 'axios'
import {
  deleteReportBodyTemplate,
  getReportBodyTemplatePage,
  saveReportBodyTemplate, updateReportBodyTemplate,
} from '@/api/jgjc/assessmentReport/mainTemplate'

export default {
  name: 'ContentManagement',
  data() {
    return {
      // 遮罩层
      loading: false,
      // 左侧组织树
      relaOptions: [],
      filteredTreeData: [],
      defaultProps: {
        children: 'children',
        label: 'label',
      },
      // 总条数
      total: 0,
      dataList: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
      },
      structureId: '',
      formData: {},
      fileVisible: false
    }
  },
  created() {
    this.handleQuery()
  },
  methods: {
    handleQuery() {
      this.loading = true
      getReportBodyTemplatePage(this.queryParams).then(res => {
        this.dataList = res.rows
        this.total = res.total
        this.loading = false
      }).catch(err => {
        this.loading = false
        console.error(err)
      })
    },
    handleRowClick(row) {
      this.loading = true
      getFile({ ownerId: row.templatePath }).then(async res => {
        if (res.code == 200) {
          await this.handleDownload(res.data, row.templateName)
        }
      }).finally(() => {
        this.loading = false
      })
    },
    handleEdit(row) {
      this.formData = Object.assign({}, row)
      this.fileVisible = true
    },
    handleDelete(row) {
      this.$confirm('是否确认删除？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }).then(() => {
        this.loading = true
        deleteReportBodyTemplate({ ids: [row.id] }).then(res => {
          if (res.code === 200) {
            this.$message.success('删除成功')
            this.handleQuery()
          } else {
            this.$message.error(res.msg)
          }
          this.loading = false
        }).catch(err => {
          this.loading = false
          console.error(err)
        })
      })
    },
    handleUpload() {
      this.formData = {}
      this.formData.structureId = this.structureId
      this.fileVisible = true
    },
    handleSaveFile() {
      if (!this.formData.templateName) {
        this.$message.warning('请输入模板名称')
        return
      }
      this.formData.filename = this.formData.templateName
      if (this.formData.id) {
        updateReportBodyTemplate(this.formData).then(res => {
          this.$modal.msgSuccess('保存成功')
          this.fileVisible = false
          this.handleQuery()
        })
      } else {
        const fileList = this.$refs.file.fileList
        if (fileList.length != 1) {
          this.$message.warning('请上传文件')
          return
        }
        this.formData.templatePath = fileList[0].ownerId
        saveReportBodyTemplate(this.formData).then(res => {
          this.$modal.msgSuccess('新增成功')
          this.fileVisible = false
          this.handleQuery()
        })
      }
    },
    async handleDownload(file, name) {
      const url = file.url || (file.response && file.response.url)
      if (!url) {
        this.$message.error('文件无法下载，未找到文件的URL')
        return
      }

      try {
        // 创建一个不包含 Authorization 的请求头
        const downloadHeaders = {
          ...this.headers,
          'Content-Disposition': 'attachment', // 添加强制下载头
          'Content-Type': 'application/octet-stream', // 使用通用的二进制流类型
        }
        delete downloadHeaders.Authorization

        // 使用 axios 进行下载，因为它可以自动处理跨域认证
        const response = await axios({
          url: url,
          method: 'GET',
          responseType: 'blob',
          headers: downloadHeaders,
        })

        // 从响应头中获取文件类型
        const contentType = response.headers['content-type']
        const blob = new Blob([response.data], { type: contentType || 'application/octet-stream' })

        // 创建一个隐藏的 iframe 来处理下载
        const iframe = document.createElement('iframe')
        iframe.style.display = 'none'
        document.body.appendChild(iframe)
        const finalFileName = name

        // 在 iframe 中创建 blob URL 并触发下载
        const iframeWindow = iframe.contentWindow
        const blobUrl = iframeWindow.URL.createObjectURL(blob)
        const link = iframeWindow.document.createElement('a')
        link.href = blobUrl
        link.download = finalFileName
        link.click()

        // 清理资源
        setTimeout(() => {
          iframeWindow.URL.revokeObjectURL(blobUrl)
          document.body.removeChild(iframe)
        }, 1000)
      } catch (error) {
        console.error('下载文件时发生错误:', error)
        this.$message.error('下载文件失败，请重试')
      }
    },
  },
}
</script>

<style scoped>
</style>
<style lang='scss' scoped>
@import "@/assets/styles/business.scss";
</style>
