//湿度概率直方图
import { getMax, getMin } from '@/views/jgjc/dataAnalysis/dataView/drawMethods/util'
import * as echarts from "echarts";
import myTheme from './myTheme'
export const YB = {

  //小时结构温度极值时程图
  setJGWDChart(chartName, data) {
    let myChart = echarts.getInstanceByDom(
      document.getElementById(chartName)
    );
    if (myChart !== undefined) {
      myChart.dispose();
    }
    myChart = echarts.init(document.getElementById(chartName), myTheme.theme);
    let optionName = data.optionName;
    data = data.dataList
    // 数据处理 给值增加时间戳
    data.forEach((item,index) =>{
      let tmpDotList0 = []
      let tmpDotList1 = []
      for(let i = 0; i < item.result.processedData.maxValues.length; i++) {
        tmpDotList0.push([item.result.processedData.times[i], item.result.processedData.maxValues[i].toFixed(item.result.accuracy >= 0  ? item.result.accuracy : 4)])
        tmpDotList1.push([item.result.processedData.times[i], item.result.processedData.minValues[i].toFixed(item.result.accuracy >= 0  ? item.result.accuracy : 4)])
      }
      item.result.processedData.maxDotList = tmpDotList0
      item.result.processedData.minDotList = tmpDotList1
    })
    let option = {
      title: {
        left: 'center',
        text: optionName,
      },
      toolbox: {
        right: 0,
        top: 25,
        feature: {
          saveAsImage: {
            title: '保存'
          },
        },
      },
      tooltip: {
        textStyle:{
          align:'left'
        },
        trigger: "axis",
        confine: true
      },
      xAxis: {
        type: 'category',
        name: "时间",
        nameLocation: "middle",
        nameGap: 22,
        axisLabel: {
          formatter: function (value, index) {
            return value.substring(0, value.length - 7);
          },
          fontSize: 11,
        },
      },
      yAxis: [],
      dataZoom: [
        {
          type: "inside",
        },
      ],
      grid: [
        {
          top: "17%",
          left: "2%",
          right: "10%",
          bottom: "12%",
          containLabel: true,
        },
      ],
      series: [],
      legend: {
        type: 'scroll',
        data: [],
        x: 'center',
        y: 'bottom',
        selected: {},
      },
    };
    //加入多行数据
    let finalMax = 0;
    let finalMin = 0;
    for(let i = 0 ; i < data.length; i++){
      option.legend.data.push(data[i].label+"小时最大值");
      option.series.push({
        type: "line",
        name: data[i].label+"小时最大值",
        showSymbol: false,
        data: data[i].result.processedData.maxDotList,
      });
      option.legend.data.push(data[i].label+"小时最小值");
      option.series.push({
        type: "line",
        name: data[i].label+"小时最小值",
        showSymbol: false,
        data: data[i].result.processedData.minDotList,
      });

      //计算最大值与最小值
      let minValue = getMin(data[i].result.processedData.minValues); // 输出最小值
      let maxValue = getMax(data[i].result.processedData.maxValues); // 输出最大值
      // 一些特殊情况处理
      if (maxValue === 0 && minValue === 0) {
        maxValue = 1
        minValue = -1
      } else {
        let delta = Math.ceil((maxValue - minValue) * 0.2 * (10 ** data[i].result.accuracy))/(10 ** data[i].result.accuracy)
        if(delta === 0){
          delta = 1
        }
        let midValue = Math.ceil((maxValue + minValue) / 2 * (10 ** data[i].result.accuracy))/(10 ** data[i].result.accuracy)
        maxValue = midValue + 3 * delta
        minValue = midValue - 3 * delta
      }
      // 外层循环最大最小值更新
      if(i === 0){
        finalMax = maxValue
        finalMin = minValue
      }else {
        if(finalMax < maxValue){
          finalMax = maxValue
        }
        if(finalMin > minValue){
          finalMin = minValue
        }
      }
      // 一些预警线
      if (data[i].result.limitInfo) {
        Object.keys(data[i].result.limitInfo).forEach((key) => {
          option.series.push({
            type: "line",
            name: data[i].label + key,
            showSymbol: false,
            animation: false,
            markLine: {
              symbol: 'none',
              data: [
                {
                  yAxis: data[i].result.limitInfo[key],
                  lineStyle: {
                    type: 'solid'
                  },
                  label: {
                    show: true,
                    position: 'insideEndTop',
                  },
                }
              ]
            }
          });
          option.legend.data.push(data[i].label + key)
        })
      }
    }
    // 一些特殊情况处理
    if (finalMax === 0 && finalMin === 0) {
      finalMax = 1
      finalMin = -1
    } else {
      let delta = Math.ceil((finalMax - finalMin) * 0.2 * (10 ** data[0].result.accuracy))/(10 ** data[0].result.accuracy)
      if(delta === 0){
        delta = 1
      }
      let midValue = Math.ceil((finalMax + finalMin) / 2 * (10 ** data[0].result.accuracy))/(10 ** data[0].result.accuracy)
      finalMax = midValue + 3 * delta
      finalMin = midValue - 3 * delta
    }
    option.yAxis.push({
      name: '温度/℃',
      nameTextStyle: {
        fontWeight: "bold",
        fontSize: 12
      },
      axisLabel: {
        formatter: function (value) {
          return value.toFixed(data[0].result.accuracy >= 0  ? data[0].result.accuracy : 4); // 2表示小数为2位
        },
      },
      splitNumber: 6,
      min: finalMin,
      max: finalMax,
      interval: (finalMax - finalMin) / 6, // 标轴分割间隔
    })
    option && myChart.setOption(option);
    window.onresize = function () {
      myChart.resize();
    };
  },
  //小时结构应变极值时程图
  setJGYBChart(chartName, data) {
    let myChart = echarts.getInstanceByDom(
      document.getElementById(chartName)
    );
    if (myChart !== undefined) {
      myChart.dispose();
    }
    myChart = echarts.init(document.getElementById(chartName), myTheme.theme);
    let optionName = data.optionName;
    data = data.dataList
    // 数据处理 给值增加时间戳
    data.forEach((item,index) =>{
      let tmpDotList0 = []
      let tmpDotList1 = []
      for(let i = 0; i < item.result.processedData.maxValues.length; i++) {
        tmpDotList0.push([item.result.processedData.times[i], item.result.processedData.maxValues[i].toFixed(item.result.accuracy >= 0  ? item.result.accuracy : 4)])
        tmpDotList1.push([item.result.processedData.times[i], item.result.processedData.minValues[i].toFixed(item.result.accuracy >= 0  ? item.result.accuracy : 4)])
      }
      item.result.processedData.maxDotList = tmpDotList0
      item.result.processedData.minDotList = tmpDotList1
    })
    let option = {
      title: {
        left: 'center',
        text: optionName,
      },
      toolbox: {
        right: 0,
        top: 25,
        feature: {
          saveAsImage: {
            title: '保存'
          },
        },
      },
      tooltip: {
        textStyle:{
          align:'left'
        },
        trigger: "axis",
        confine: true
      },
      xAxis: {
        type: 'category',
        name: "时间",
        nameLocation: "middle",
        nameGap: 22,
        axisLabel: {
          formatter: function (value, index) {
            return value.substring(0, value.length - 7);
          },
          fontSize: 11,
        },
      },
      yAxis: [],
      dataZoom: [
        {
          type: "inside",
        },
      ],
      grid: [
        {
          top: "17%",
          left: "2%",
          right: "10%",
          bottom: "12%",
          containLabel: true,
        },
      ],
      series: [],
      legend: {
        type: 'scroll',
        data: [],
        x: 'center',
        y: 'bottom',
        selected: {},
      },
    };
    //加入多行数据
    let finalMax = 0;
    let finalMin = 0;
    for(let i = 0 ; i < data.length; i++){
      option.legend.data.push(data[i].label+"小时最大值");
      option.series.push({
        type: "line",
        name: data[i].label+"小时最大值",
        showSymbol: false,
        data: data[i].result.processedData.maxDotList,
      });
      option.legend.data.push(data[i].label+"小时最小值");
      option.series.push({
        type: "line",
        name: data[i].label+"小时最小值",
        showSymbol: false,
        data: data[i].result.processedData.minDotList,
      });

      //计算最大值与最小值
      let minValue = getMin(data[i].result.processedData.minValues); // 输出最小值
      let maxValue = getMax(data[i].result.processedData.maxValues); // 输出最大值
      // 一些特殊情况处理
      if (maxValue === 0 && minValue === 0) {
        maxValue = 1
        minValue = -1
      } else {
        let delta = Math.ceil((maxValue - minValue) * 0.2 * (10 ** data[i].result.accuracy))/(10 ** data[i].result.accuracy)
        if(delta === 0){
          delta = 1
        }
        let midValue = Math.ceil((maxValue + minValue) / 2 * (10 ** data[i].result.accuracy))/(10 ** data[i].result.accuracy)
        maxValue = midValue + 3 * delta
        minValue = midValue - 3 * delta
      }
      // 外层循环最大最小值更新
      if(i === 0){
        finalMax = maxValue
        finalMin = minValue
      }else {
        if(finalMax < maxValue){
          finalMax = maxValue
        }
        if(finalMin > minValue){
          finalMin = minValue
        }
      }
      // 一些预警线
      if (data[i].result.limitInfo) {
        Object.keys(data[i].result.limitInfo).forEach((key) => {
          option.series.push({
            type: "line",
            name: data[i].label + key,
            showSymbol: false,
            animation: false,
            markLine: {
              symbol: 'none',
              data: [
                {
                  yAxis: data[i].result.limitInfo[key],
                  lineStyle: {
                    type: 'solid'
                  },
                  label: {
                    show: true,
                    position: 'insideEndTop',
                  },
                }
              ]
            }
          });
          option.legend.data.push(data[i].label + key)
        })
      }
    }
    // 一些特殊情况处理
    if (finalMax === 0 && finalMin === 0) {
      finalMax = 1
      finalMin = -1
    } else {
      let delta = Math.ceil((finalMax - finalMin) * 0.2 * (10 ** data[0].result.accuracy))/(10 ** data[0].result.accuracy)
      if(delta === 0){
        delta = 1
      }
      let midValue = Math.ceil((finalMax + finalMin) / 2 * (10 ** data[0].result.accuracy))/(10 ** data[0].result.accuracy)
      finalMax = midValue + 3 * delta
      finalMin = midValue - 3 * delta
    }
    option.yAxis.push({
      name: data[0].result.processedData.name + "/" +data[0].result.processedData.unit,
      nameTextStyle: {
        fontWeight: "bold",
        fontSize: 12
      },
      axisLabel: {
        formatter: function (value) {
          return value.toFixed(data[0].result.accuracy >= 0  ? data[0].result.accuracy : 4); // 2表示小数为2位
        },
      },
      splitNumber: 6,
      min: finalMin,
      max: finalMax,
      interval: (finalMax - finalMin) / 6, // 标轴分割间隔
    })
    option && myChart.setOption(option);
    window.onresize = function () {
      myChart.resize();
    };
  },
  //应变分布直方图
  setYBDISChart(chartName, data) {
    let myChart = echarts.getInstanceByDom(
      document.getElementById(chartName)
    );
    if (myChart !== undefined) {
      myChart.dispose();
    }
    myChart = echarts.init(document.getElementById(chartName), myTheme.theme);

    let optionName = data.optionName;
    let xMinValue = data.minValue;
    let xMaxValue = data.maxValue;
    let xInterval = data.interval;
    let xLabel = []
    let loopTimes = (xMaxValue-xMinValue)/xInterval
    xLabel.push('<'+xMinValue);
    for(let i = 0; i < loopTimes; i++){
      xLabel.push(xMinValue+"~"+(xMinValue+xInterval));
      xMinValue += xInterval
    }
    xLabel.push('>'+xMaxValue);
    data = data.dataList
    let option = {
      title: {
        left: 'center',
        text: optionName,
      },
      toolbox: {
        right: 0,
        top: 25,
        feature: {
          saveAsImage: {
            title: '保存'
          },
        },
      },
      tooltip: {
        textStyle:{
          align:'left'
        },
        trigger: "axis",
        confine: true
      },
      xAxis: {
        type: 'category',
        name: "应变直方图",
        nameLocation: "middle",
        nameGap: 22,
        data: xLabel
      },
      yAxis: {
        name: "比率",
        type: 'value',
        nameTextStyle: {
          fontWeight: "bold",
          fontSize: 12
        },
        axisLabel: {
          formatter: function (value, index) {
            return value.toFixed(0) + '%';
          }
        }
      },
      dataZoom: [
        {
          type: "inside",
        },
      ],
      grid: [
        {
          top: "17%",
          left: "2%",
          right: "10%",
          bottom: "14%",
          containLabel: true,
        },
      ],
      series: [],
      legend: {
        type: 'scroll',
        x: 'center',
        y: 'bottom',
        data: [],
      },
    };
    //加入多行数据
    for(let i = 0 ; i < data.length; i++){
      option.legend.data.push(data[i].label);
      option.series.push({
        type: "bar",
        barGap: 0,
        barCategoryGap: 0,
        name: data[i].label,
        data: data[i].distribution.map(item => item.toFixed(1)),
      });
    }
    option && myChart.setOption(option);
    window.onresize = function () {
      myChart.resize();
    };
  },

}
