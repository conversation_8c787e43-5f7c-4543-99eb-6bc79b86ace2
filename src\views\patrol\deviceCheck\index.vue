<template>
  <div class="app-container">
    <!--筛选区开始-->
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" label-width="68px">
      <div style="display: flex; align-items: center; flex-wrap: nowrap">
        <CascadeSelection
          style="min-width: 192px; margin-right: 10px"
          :form-data="queryParams"
          v-model="queryParams"
          types="201"
          multiple
        />
        <el-form-item label="" prop="assetCode" style="margin-bottom: 0; margin-right: 10px">
          <el-input
            v-model="queryParams.assetCode"
            placeholder="请输入隧道编码"
            clearable
            prefix-icon="el-icon-user"
            style="width: 240px"
            @keyup.enter.native="handleQuery"
          />
        </el-form-item>

        <el-form-item label="" prop="assetName" style="margin-bottom: 0; margin-right: 10px">
          <el-input
            v-model="queryParams.assetNameLike"
            placeholder="请输入隧道名称"
            clearable
            prefix-icon="el-icon-user"
            style="width: 240px"
            @keyup.enter.native="handleQuery"
          />
        </el-form-item>
        <!--      <el-form-item label="隧道">-->
        <!--          <el-input v-model="queryParams.assetName" readonly @click.native="selectTunnel" placeholder="点击选择..."/>-->
        <!--      </el-form-item>-->
        <el-form-item label="" style="margin-bottom: 0; margin-right: 10px">
          <el-date-picker
            v-model="dateRange"
            style="width: 240px"
            value-format="yyyy-MM-dd"
            type="daterange"
            range-separator="-"
            start-placeholder="检查开始日期"
            end-placeholder="检查结束日期"
          ></el-date-picker>
        </el-form-item>
<!--        <el-form-item prop="departmentIdList">-->
<!--          <el-select v-model="queryParams.departmentIdList"-->
<!--                     filterable-->
<!--                     placeholder="请选择所属单位"-->
<!--                     multiple collapse-tags>-->
<!--            <el-option-->
<!--              v-for="item in deptOptions"-->
<!--              :key="item.id"-->
<!--              :label="item.label"-->
<!--              :value="item.id">-->
<!--            </el-option>-->
<!--          </el-select>-->
<!--        </el-form-item>-->
      </div>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <!--操作按钮区开始-->
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd(1)"
          v-hasPermi="['patrol:deviceCheck:add']"
        >新增
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['patrol:deviceCheck:edit']"
        >修改
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['patrol:deviceCheck:remove']"
        >删除
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="primary"
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd(2)"
          v-hasPermi="['patrol:deviceCheck:addAll']"
          v-if="recordType === 2"
        >批量新增
        </el-button>
      </el-col>
<!--      <el-col :span="1.5">-->
<!--        <el-button type="info" plain icon="el-icon-upload2" size="mini"-->
<!--          @click="handleImport" v-hasPermi="['patrol:deviceCheck:export']">-->
<!--          导入-->
<!--        </el-button>-->
<!--      </el-col>-->
      <el-col :span="1.5">
        <el-button type="warning" icon="el-icon-download" size="mini" @click="handleExport"
                   v-hasPermi="['patrol:deviceCheck:export']">
          导出
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="primary" icon="el-icon-download" size="mini" @click="handleExportCard">
          导出卡片
        </el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>
    <!--操作按钮区结束-->

    <!--数据表格开始-->
    <div class="tableDiv">
      <el-table
        v-adjust-table
        ref="table"
        size="mini"
        :height="showSearch ? 'calc(100vh - 320px)' : 'calc(96vh - 260px)'"
        style="width: 100%"
        v-loading="loading"
        border
        :data="deviceCheckList"
        @selection-change="handleSelectionChange"
        :row-style="rowStyle"
        @row-click="handleRowClick"
      >
        <el-table-column type="selection" width="40" align="center"/>
        <el-table-column fixed label="序号" type="index" width="50">
          <template v-slot="scope">
            {{ (scope.$index + (queryParams.pageNum - 1) * queryParams.pageSize)+1 }}
          </template>
        </el-table-column>
        <el-table-column label="路线编码" align="center" prop="routeCode"/>
        <el-table-column label="养护路段" align="center" prop="routeName" width="120"/>
        <el-table-column label="隧道编码" align="center" prop="assetCode" width="120" show-overflow-tooltip/>
        <el-table-column label="隧道名称" align="center" prop="assetName" width="140" show-overflow-tooltip/>
        <el-table-column label="管养单位" align="center" prop="deptName"/>
        <el-table-column :label="recordType===3?'故障日期':'检查日期'" align="center" prop="checkTime"/>
        <el-table-column label="天气" align="center" prop="weather" width="50"/>
        <el-table-column label="负责人" align="center" prop="checker" show-overflow-tooltip/>
        <el-table-column label="记录人" align="center" prop="recorder" show-overflow-tooltip/>
        <el-table-column label="设备名称" align="center" prop="devName" width="120" show-overflow-tooltip/>
        <el-table-column label="检查位置" align="center" prop="location" width="120" show-overflow-tooltip/>
        <el-table-column label="故障部位" align="center" prop="faultLocation" v-if="recordType === 3"/>
        <el-table-column label="检查内容" align="center" prop="content" width="180" v-if="recordType !== 3" show-overflow-tooltip/>
        <!-- 结果（1正常，2异常，3异常且严重）-->
        <el-table-column label="检查结果" align="center" prop="result" v-if="recordType !== 3">
          <template v-slot="scope">
            <span v-if="scope.row.result === 1">正常</span>
            <span v-if="scope.row.result === 2">异常</span>
            <span v-if="scope.row.result === 3">异常且严重</span>
          </template>
        </el-table-column>
        <el-table-column label="车牌号" align="center" prop="carLicense" v-if="recordType === 2"/>
        <el-table-column label="车辆台数" align="center" prop="nums" v-if="recordType === 2"/>
        <el-table-column :label="recordType===2?'注意事项':(recordType===3?'故障原因及内容':'异常描述')" align="center" prop="describe" width="180" show-overflow-tooltip/>
        <el-table-column :label="recordType===1?'养护措施':'应急措施'" align="center" prop="measures" width="180" v-if="recordType !== 2" show-overflow-tooltip/>
        <el-table-column
          label="操作"
          fixed="right"
          align="center"
          width="110"
          class-name="small-padding fixed-width"
        >
          <template slot-scope="scope" v-if="scope.row.userId !== 1">
            <el-button
              size="mini"
              type="text"
              icon="el-icon-edit"
              @click="handleUpdate(scope.row)"
              v-hasPermi="['patrol:deviceCheck:edit']"
            >修改
            </el-button>
            <el-button
              size="mini"
              type="text"
              icon="el-icon-delete"
              @click="handleDelete(scope.row)"
              v-hasPermi="['patrol:deviceCheck:remove']"
            >删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <pagination
        v-show="total>0"
        :total="total"
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
        @pagination="getList"
      />
    </div>
    <!--数据表格结束-->

    <!-- 添加修改 -->
    <el-dialog :title="title" :visible.sync="open" width="80%" max-height="50%"
               :close-on-press-escape="false" :close-on-click-modal="false" append-to-body
               class="formDialog">
      <el-form ref="form" :model="deviceForm" :rules="rules" label-width="125px" label-position="right">
        <!--      <div class="check-edit-title">基础信息</div>-->
        <div class="infoBox" style="margin-bottom: 20px;" v-loading="loading">
          <div class="infoTitle">基础信息</div>
          <el-row :gutter="12">
            <el-col :span="12">
              <el-form-item label="路段名称" prop="routeName" v-if="isAddAll">
                <el-select
                  v-model="deviceForm.routeName"
                  placeholder="输入搜索..."
                  clearable
                  @clear="clearMaintenanceSectionId"
                  filterable
                  @change="maintenanceSectionChange"
                  style="width: 100%"
                >
                  <el-option
                    v-for="item in routeOptions"
                    :key="item.maintenanceSectionId"
                    :label="item.maintenanceSectionName"
                    :value="item"
                  >
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="隧道名称" prop="assetName">
                <el-input v-model="deviceForm.assetName" readonly @click.native="selectTunnel" :placeholder="assetNameStr"/>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="隧道编码" prop="assetCode" v-if="!isAddAll">
                <el-input v-model="deviceForm.assetCode" disabled placeholder="选择隧道自动填充"/>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="12">
            <el-col :span="12">
              <el-form-item label="路线编码" prop="routeCode" v-if="!isAddAll">
                <el-input v-model="deviceForm.routeCode" disabled placeholder="选择隧道自动填充"/>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="养护路段" prop="routeName" v-if="!isAddAll">
                <el-input v-model="deviceForm.routeName" disabled placeholder="选择隧道自动填充"/>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="12">
<!--            <el-col :span="12">-->
<!--              <el-form-item label="中心桩号" prop="centerStake">-->
<!--                <PileInput v-model="deviceForm.centerStake" :is-format="true" :is-disabled="true"/>-->
<!--              </el-form-item>-->
<!--            </el-col>-->
            <el-col :span="12">
              <el-form-item label="养护单位" prop="deptName" v-if="!isAddAll">
                <el-input v-model="deviceForm.deptName" disabled placeholder="选择隧道自动填充"/>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="12">
            <el-col :span="12">
              <el-form-item label="检查时间" prop="checkTime" v-if="!isAddAll">
                <el-date-picker clearable
                                v-model="deviceForm.checkTime"
                                type="date"
                                value-format="yyyy-MM-dd"
                                placeholder="请选择检查时间">
                </el-date-picker>
              </el-form-item>
              <el-form-item label="检查时间" prop="dateRange" v-if="isAddAll">
                <el-date-picker
                  v-model="dateRange"
                  style="width: 240px"
                  value-format="yyyy-MM-dd"
                  type="daterange"
                  range-separator="-"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                ></el-date-picker>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="天气" prop="weather">
                <el-autocomplete v-model="deviceForm.weather"
                          :fetch-suggestions="weatherSuggestions"
                          placeholder="请输入天气"/>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="12">
            <el-col :span="12">
              <el-form-item label="负责人" prop="checkerId">
                <el-cascader
                  ref="deptUser"
                  @change="handleChange"
                  v-model="deviceForm.checkerId"
                  :options="deptUserOptions"
                  :props="{value: 'id', emitPath: false }"
                  :show-all-levels="false"
                  filterable
                  clearable
                  style="width: 100%"
                />
<!--                <el-input v-model="deviceForm.checker" placeholder="请输入检查人"/>-->
              </el-form-item>
            </el-col>
            <el-col :span="12">
<!--              <el-form-item label="记录人" prop="recorder" v-if="!isAddAll">-->
<!--                <el-input v-model="deviceForm.recorder" placeholder="请输入记录人"/>-->
<!--              </el-form-item>-->
              <el-form-item label="记录人" prop="recorderId" v-if="!isAddAll">
                <el-cascader
                  ref="deptUser2"
                  @change="handleChange2"
                  v-model="deviceForm.recorderId"
                  :options="deptUserOptions"
                  :props="{value: 'id', emitPath: false }"
                  :show-all-levels="false"
                  filterable
                  clearable
                  style="width: 100%"
                />
              </el-form-item>
            </el-col>
          </el-row>
        </div>
        <!--    检查项详情-->
        <div class="check-edit-title">设备检查详情</div>
        <div v-for="domain in deviceForm.domains" :key="domain.id" class="infoBox" style="margin-bottom: 10px;" v-loading="loading">
          <el-row :gutter="12">
            <el-col :span="12">
              <el-form-item label="设备名称" prop="devName">
                <el-input v-model="domain.devName" placeholder="请输入设备名称"/>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="检查位置" prop="location">
                <el-input v-model="domain.location" placeholder="请输入检查位置"/>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="12" v-if="recordType === 2">
            <el-col :span="12">
              <el-form-item label="车牌号" prop="carLicense">
                <el-input v-model="domain.carLicense" placeholder="请输入车牌号"/>
              </el-form-item>
            </el-col>
            <el-col :span="12" v-if="recordType === 2">
              <el-form-item label="车辆台数" prop="nums">
                <el-input v-model="domain.nums" type="number" placeholder="请输入车辆台数"/>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="12">
            <el-col :span="12" v-if="recordType !== 3">
              <el-form-item label="检查结果" prop="result">
                <el-radio-group v-model="domain.result">
                  <el-radio :label="1">正常</el-radio>
                  <el-radio :label="2">异常</el-radio>
                  <el-radio :label="3">异常且严重</el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>
            <el-col :span="12"  v-if="recordType === 3">
              <el-form-item label="故障部位" prop="faultLocation">
                <el-input v-model="domain.faultLocation" placeholder="请输入检查内容"/>
              </el-form-item>
            </el-col>
            <el-col :span="12"  v-if="recordType !== 2">
              <el-form-item :label="recordType===1?'养护措施':'应急措施'" prop="measures">
                <el-input type="textarea" v-model="domain.measures" placeholder="请输入检查内容"/>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="12">
            <el-col :span="12" v-if="recordType !== 3">
              <el-form-item label="检查内容" prop="content">
                <el-input type="textarea" v-model="domain.content" placeholder="请输入检查内容"/>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item :label="recordType===2?'注意事项':(recordType===3?'故障原因及内容':'异常描述')" prop="describe">
                <el-input type="textarea" v-model="domain.describe" placeholder="请输入"/>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="12" v-if="recordType === 1">
            <el-form-item label="上传附件" prop="picPaths">
              <FileUpload
                :value="domain.picPaths"
                key="domain.id"
                previewWidth="80%"
                :owner-id="domain.picPaths?domain.picPaths:+new Date()"
                can-sort
                @input="(value) => (domain.picPaths = value[0])"
              ></FileUpload>
            </el-form-item>
          </el-row>
          <div v-if="!deviceForm.id" class="button-container">
            <el-button @click.prevent="removeDomain(domain)" size="mini" icon="el-icon-delete"></el-button>
          </div>
        </div>
        <el-button v-if="!deviceForm.id" class="add_details" @click="addDomain"><i class="el-icon-plus"></i></el-button>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="addCancel">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 选择隧道 -->
    <select-tunnel ref="select"
                   :type="recordType===1?'8':'7'"
                   :isAddAll="isAddAll"
                   :maintenanceSectionId="maintenanceSectionId"
                   @update:checkEntity="handleCheckEntityUpdate"
    />

    <!-- 导出卡片iframe -->
<!--    <el-dialog-->
<!--      title="导出预览"-->
<!--      :visible.sync="showIframe"-->
<!--      width="90%"-->
<!--      :close-on-click-modal="false"-->
<!--      append-to-body-->
<!--      class="export-preview-dialog"-->
<!--    >-->
<!--      <iframe-->
<!--        :src="src"-->
<!--        id="reportView"-->
<!--        frameborder="no"-->
<!--        style="width: 100%; height: 75vh"-->
<!--        scrolling="auto"-->
<!--      />-->
<!--    </el-dialog>-->

  </div>
</template>

<script>
import {
  listDeviceCheck,
  getDeviceCheck,
  delDeviceCheck,
  addDeviceCheck,
  updateDeviceCheck,
  getAssetTotalCount,
  addAllDeviceCheck,
} from '@/api/patrol/deviceCheck'
import {getToken} from "@/utils/auth";
import "@riophae/vue-treeselect/dist/vue-treeselect.css";
import {getTreeStruct} from "@/api/tmpl";
import SelectTunnel from "./TunnelTableDialog.vue";
import {mapGetters} from "vuex";
import {delDeviceFaultRecord, getDeviceFaultRecord} from "@/api/patrol/deviceFaultRecord";
import PileInput from "@/components/PileInput/index.vue";
import CascadeSelection from './selectOffice/index.vue'
import { getMaintenanceSectionListAll } from '@/api/baseData/common/routeLine'

export default {
  components: { CascadeSelection, PileInput, SelectTunnel},
  props: {
    /**
     * 记录类型（1经常检查，2日常巡查，3故障记录，4故障月报）
     */
    recordType: {
      type: Number,
      // require: true
      default: 1
    }
  },

  data() {
    return {
      // 遮罩层
      loading: true,
      // 是否显示导出卡片iframe
      showIframe: false,
      // 选中数组
      ids: [],
      // 是否批量新增
      isAddAll: false,
      maintenanceSectionId: '',
      routeOptions: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: false,
      // 部门-用户树选项
      deptUserOptions: [],
      dictType: [],
      // 总条数
      total: 0,
      // 隧道机电日常巡查表格数据
      deviceCheckList: null,
      // 弹出层标题
      title: "",
      // 部门树选项
      deptOptions: undefined,
      // 是否显示弹出层
      open: false,
      // 日期范围
      dateRange: [],
      // 表单参数
      deviceForm: {},
      assetNameStr: "点击选择...",
      defaultProps: {
        children: "children",
        label: "label"
      },
      // 用户导入参数
      upload: {
        // 是否显示弹出层（用户导入）
        open: false,
        // 弹出层标题（用户导入）
        title: "",
        // 是否禁用上传
        isUploading: false,
        // 是否更新已经存在的用户数据
        updateSupport: 0,
        // 设置上传的请求头部
        headers: {Authorization: "Bearer " + getToken()},
        // 上传的地址
        url: process.env.VUE_APP_BASE_API + "/system/user/importData"
      },
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 50,
        recordType: null,
        assetId: null,
        assetNameLike: null,
        routeName: null,
        departmentIdList: [],
        routeCodeList: []
      },
      // 表单校验
      rules: {
        assetName: [
          {required: true, message: "资产名称不能为空", trigger: "change"}
        ],
        assetCode: [
          {required: true, message: "资产编码不能为空", trigger: "change"}
        ],
        routeCode: [
          {required: true, message: "路线编码不能为空", trigger: "change"}
        ],
        routeName: [
          {required: true, message: "养护路段不能为空", trigger: "change"}
        ],
        checkTime: [
          {required: true, message: "检查时间不能为空", trigger: "change"}
        ],
        // dateRange: [
        //   {type: 'array',required: true,message: '请选择起止时间',trigger: 'change',},
        // ],
        checkerId: [
          {required: true, message: "负责人不能为空", trigger: "change"}
        ],
        recorderId: [
          {required: true, message: "记录人不能为空", trigger: "change"}
        ],
      }
    };
  },
  watch: {
    // 根据名称筛选部门树
  },
  computed: {
    ...mapGetters(['nickName','userId']),
  },
  created() {
    this.getList();
    // this.getDeptTree();
    // this.getConfigKey("sys.user.initPassword").then(response => {
    //   this.initPassword = response.msg;
    // });
  },
  methods: {
    /** 选择桥梁 */
    selectTunnel() {
      this.$refs.select.show();
    },
    handleCheckEntityUpdate(newValue) {
      console.log("newValue : ", newValue)
      // 在这里更新父组件中的值
      if (this.isAddAll) {
        if (newValue.length <= 0) {
          this.$modal.msgError("请选择隧道");
        }
        this.deviceForm.assetIds = newValue;
        this.assetNameStr = "已选择：" + this.deviceForm.assetIds.length + "条记录";
        this.deviceForm.assetName = this.assetNameStr;//填充资产名称以防必填校验
      } else {
        this.deviceForm.assetId = newValue.assetId;
        this.deviceForm.assetName = newValue.tunnelName;
        this.deviceForm.assetCode = newValue.tunnelCode;
        this.deviceForm.routeCode = newValue.routeCode;
        this.deviceForm.routeName = newValue.maintenanceSectionName;
        this.deviceForm.deptName = newValue.managementMaintenanceName;
        this.deviceForm.domainId = newValue.managementMaintenanceId;
      }
    },

    /** 查询用户列表 */
    getList() {
      this.loading = true;
      this.queryParams.recordType = this.recordType;
      this.queryParams.oprTimes = this.dateRange[0];
      this.queryParams.oprTimee = this.dateRange[1];
      this.queryParams.domainIds = this.queryParams.departmentIdList.join(",");
      this.queryParams.routeCodes = this.queryParams.routeCodeList.map(item => `"${item}"`).join(",");
      listDeviceCheck(this.queryParams).then(response => {
        this.deviceCheckList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    addCancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.deviceForm = {
        assetId : null,
        assetName : null,
        assetCode : null,
        routeCode : null,
        routeName : null,
        checkerId : null,
        checker : null,
        centerStake : null,
        deptName : null,
        recorder : this.isAddAll?null:this.nickName,
        recorderId : this.isAddAll?null:this.userId,
        recordType : this.recordType,
        // 选中的隧道ID
        assetIds: [],
        domains : [{
          checkId : null,
          devName : null,
          location : null,
          content : null,
          result : 1,
          describe : null,
          measures : null,
          picPaths : null,
          carLicense : null,
          nums : null
        }]
      };
      this.resetForm("form");
    },
    /** 查询部门下拉树结构 */
    // getDeptTree() {
    //   return getTreeStruct({types: 201}).then(response => {
    //     this.deptOptions = response.data;
    //   });
    // },
    /** 查询部门-用户下拉树结构 */
    getDeptUserTree() {
      return getTreeStruct({types:111}).then(response => {
        this.deptUserOptions = response.data;
      });
    },
    /** 查询养护路段列表*/
    getMaintenanceSectionListAll() {
      getMaintenanceSectionListAll().then((res) => {
        if (res.code === 200) {
          this.routeOptions = res.data || [];
        }
      });
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dateRange = [];
      this.queryParams = {
        pageNum: 1,
        pageSize: 50,
        recordType: null,
        assetCode: null,
        assetName: null,
        routeName: null,
        departmentIdList: [],
        routeCodeList: []
      };
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id);
      this.single = selection.length != 1;
      this.multiple = !selection.length;
    },
    // 表格点击勾选
    handleRowClick(row) {
      row.isSelected = !row.isSelected;
      this.$refs.table.toggleRowSelection(row);
    },
    // 勾选高亮
    rowStyle({row, rowIndex}) {
      if (this.ids.includes(row.id)) {
        return {'background-color': '#b7daff', color: '#333'}
      } else {
        return {'background-color': '#fff', color: '#333'}
      }
    },
    /** 新增按钮操作 */
    handleAdd(type) {
      this.reset();
      this.isAddAll = type === 2;
      this.open = true;
      this.title = this.recordTypeMap()[this.recordType] || '添加隧道机电巡查记录';
      this.getDeptUserTree();
      if (this.isAddAll) {
        this.maintenanceSectionId = '';
        this.getMaintenanceSectionListAll();
      }
    },
    maintenanceSectionChange(selectedItem) {
      if (selectedItem) {
        this.maintenanceSectionId = selectedItem.maintenanceSectionId;
        this.deviceForm.routeName = selectedItem.maintenanceSectionName;
      }
    },
    clearMaintenanceSectionId() {
      this.maintenanceSectionId = "";
    },

    recordTypeMap() {
      return {
        1: "添加隧道机电经常巡查",
        2: "添加隧道机电日常巡查",
        3: "添加隧道机电故障上报"
      };
    },

    /** 修改按钮操作 */
    handleUpdate(row) {
      this.isAddAll = false;
      const id = row.id || this.ids;
      if (this.recordType === 3) {
        getDeviceFaultRecord(id).then(response => {this.setForm(response);});
      } else {
        getDeviceCheck(id).then(response => {this.setForm(response);});
      }
      this.getDeptUserTree();
    },
    setForm(response) {
      this.$nextTick(() => {
        this.deviceForm = response.data;
        this.deviceForm.checkerId = response.data.checkerId.toString();
        // if (!this.isAddAll) {
        //   this.deviceForm.recorder = this.nickName;
        //   this.deviceForm.recorderId = this.userId;
        // }
        if (this.recordType === 3) {this.deviceForm.domains = response.data.faultRecordList;}
        this.open = true;
        this.title=this.recordType===3?"修改隧道机电故障上报":(this.recordType===1?"修改隧道机电经常巡查":"修改隧道机电日常巡查");
      })
    },
    handleChange(value) {
      // 处理级联选择器的选择变化
      let userList = this.$refs.deptUser.getCheckedNodes();
      this.deviceForm.checkerId = userList.length>0?userList[0].value:null;
      this.deviceForm.checker = userList.length>0?userList[0].label:null;
    },
    handleChange2(value) {
      // 处理级联选择器的选择变化
      let userList = this.$refs.deptUser2.getCheckedNodes();
      this.deviceForm.recorderId = userList.length>0?userList[0].value:null;
      this.deviceForm.recorder = userList.length>0?userList[0].label:null;
    },
    removeDomain(item) {
      let index = this.deviceForm.domains.indexOf(item);
      if (index !== -1) {
        this.deviceForm.domains.splice(index, 1)
      }
    },
    addDomain() {
      this.deviceForm.domains.push({
        devName: null,
        location: null,
        carLicense: null,
        nums: null,
        content: null,
        describe: null,
        result: 1,
      });
    },
    weatherSuggestions(queryString, cb) {
      const suggestions = [
        { value: '晴' },
        { value: '多云' },
        { value: '小雨' },
        { value: '中雨' },
      ];
      cb(suggestions);
    },
    /** 新增修改提交按钮 */
    submitForm: function() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.recordType === 3) {
            this.deviceForm.faultRecordList = this.deviceForm.domains;
            this.deviceForm.domains = [];
          }else {
            this.deviceForm.faultRecordList = []
          }
          console.log("deviceForm: ", this.deviceForm)
          if (this.deviceForm.id != null) {
            updateDeviceCheck(this.deviceForm).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            if (this.isAddAll && this.recordType === 2) {
              //批量新增
              if (this.dateRange.length <= 0) {
                this.$modal.msgError("请选择检查起止时间");
              }
              this.deviceForm.oprTimes = this.dateRange[0];
              this.deviceForm.oprTimee = this.dateRange[1];
              addAllDeviceCheck(this.deviceForm).then(response => {
                this.$modal.msgSuccess("新增成功");
                this.open = false;
                this.getList();
              });
            } else {
              addDeviceCheck(this.deviceForm).then(response => {
                this.$modal.msgSuccess("新增成功");
                this.open = false;
                this.getList();
              });
            }
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const id = row.id || this.ids;
      const recordType = this.recordType;
      if (row.id) {
        this.$modal.confirm('是否确认删除隧道机电日常巡查编号为"' + row.id + '"的数据项？').then(function () {
            let data = { idList: [row.id]};
            return recordType === 3?delDeviceFaultRecord(data):delDeviceCheck(data);
          }).then(() => {
            this.getList();
            this.$modal.msgSuccess('删除成功');
          });
      } else {
        this.$modal.confirm(`是否确认删除选中的 ${id.length} 条数据项？`).then(function () {
            let data = { idList: id};
            return recordType === 3?delDeviceFaultRecord(data):delDeviceCheck(data);
          }).then(() => {
            this.getList();
            this.$modal.msgSuccess('删除成功');
          });
      }
    },

    /** 报表导出按钮操作 */
    handleExportCard() {
      let params = {...this.queryParams};
      params.recordType = this.recordType;
      if (this.ids.length === 0) {
        params.oprTimes = this.dateRange[0];
        params.oprTimee = this.dateRange[1];
        params.domainIds = this.queryParams.departmentIdList.join(",");
        params.routeCodes =this.queryParams.routeCodeList.map(item => `"${item}"`).join(",");
      } else {
        params.ids = this.ids.join(',');
      }
      // console.log("queryParams: ", this.queryParams)
      // 先获取数据总数，等待结果后再显示确认框
      getAssetTotalCount(params).then((res) => {
        this.$confirm(`本次导出共有 ${res.data} 条数据，是否确认导出？`, '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        })
        .then(() => {
          const fileName = this.getFileNameBB();
          params.fileName = fileName;
          this.download('patrol/deviceCheck/exportAssetReportCard', params, fileName);
        })
        .catch((e) => {
          console.log(e)
          this.$message({
            type: 'info',
            message: '已取消导出',
          })
        })
      })
    },
    getFileNameBB(){
      // 辅助函数：为个位数添加前导零
      function padZero(num) {
        return num < 10 ? `0${num}` : num;
      }
      const now = new Date();
      const timeStr = `${now.getFullYear()}-${padZero(now.getMonth() + 1)}-${padZero(now.getDate())}_${padZero(now.getHours())}-${padZero(now.getMinutes())}-${padZero(now.getSeconds())}`;
      const recordTypeMap = {
        1: "隧道机电设施经常性（定期）检修记录表",
        2: "隧道机电设施日常巡查记录表",
        3: "隧道机电设施故障记录表"
      };
      const text = recordTypeMap[this.recordType] || "隧道机电巡查记录表";
      return `${text}_${timeStr}.xlsx`;
    },



    /** 导出Excel按钮操作 */
    handleExport() {
      let params = {...this.queryParams};
      params.recordType = this.recordType;
      if (this.ids.length === 0) {
        params.oprTimes = this.dateRange[0];
        params.oprTimee = this.dateRange[1];
        params.domainIds = this.queryParams.departmentIdList.join(",");
        params.routeCodes =this.queryParams.routeCodeList.map(item => `"${item}"`).join(",");
      } else {
        params.ids = this.ids.join(',');
      }
      // 先获取数据总数，等待结果后再显示确认框
      getAssetTotalCount(params).then((res) => {
        this.$confirm(`本次导出共有 ${res.data || 0} 条数据，是否确认导出？`, '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        }).then(() => {
          const fileName = this.getFileName();
          this.download('patrol/deviceCheck/export', params, fileName);
        }).catch(() => {
          this.$message({
            type: 'info',
            message: '已取消导出',
          });
        });
      });
    },
    getFileName(){
      // 辅助函数：为个位数添加前导零
      function padZero(num) {
        return num < 10 ? `0${num}` : num;
      }
      const now = new Date();
      const timeStr = `${now.getFullYear()}-${padZero(now.getMonth() + 1)}-${padZero(now.getDate())}_${padZero(now.getHours())}-${padZero(now.getMinutes())}-${padZero(now.getSeconds())}`;
      const recordTypeMap = {
        1: "隧道机电经常检查清单",
        2: "隧道机电日常巡查清单",
        3: "隧道机电故障上报清单"
      };
      const text = recordTypeMap[this.recordType] || "隧道机电巡查清单";
      return `${text}_${timeStr}.xlsx`;
    },

    /** 导入按钮操作 */
    handleImport() {
      this.upload.title = "用户导入";
      this.upload.open = true;
    },
    /** 下载模板操作 */
    importTemplate() {
      this.download('system/user/importTemplate', {}, `user_template.xlsx`)
    },
    // 文件上传中处理
    handleFileUploadProgress(event, file, fileList) {
      this.upload.isUploading = true;
    },
    // 文件上传成功处理
    handleFileSuccess(response, file, fileList) {
      this.upload.open = false;
      this.upload.isUploading = false;
      this.$refs.upload.clearFiles();
      this.$alert("<div style='overflow: auto;overflow-x: hidden;max-height: 70vh;padding: 10px 20px 0;'>" + response.msg + "</div>", "导入结果", {dangerouslyUseHTMLString: true});
      this.getList();
    },
    // 提交上传文件
    submitFileForm() {
      this.$refs.upload.submit();
    }
  }
};
</script>
<style lang="scss" scoped>
.infoBox {
  padding: 15px 15px 0 15px;
  box-sizing: border-box;
  border-radius: 6px;
  border: 1px solid #C4C4C4;
  position: relative;

  .infoTitle {
    user-select: none;
    position: absolute;
    top: 0;
    left: 0;
    padding: 0 10px;
    font-size: 14px;
    line-height: 14px;
    font-weight: bold;
    transform: translateX(15px) translateY(-50%);
    background-color: white;
  }

  .imgBox {
    height: auto;
    width: 100%;
    display: flex;
    flex-wrap: wrap;
    align-content: flex-start;

    .imgItemBox {
      height: 240px;
      width: calc(100% / 3);
      box-sizing: border-box;
      padding: 10px;
      overflow-y: auto;
      display: flex;
      align-items: center;
      justify-content: center;
      position: relative;

      .imgDeleteBtn {
        position: absolute;
        z-index: 1;
        top: 10%;
        right: 10%;
      }
    }
  }
}

.check-edit-title {
  font-weight: bold;
  font-size: 1.15rem;
  padding: 10px 0;
  /* color: #333333;*/
  /* color: #72767b;*/
  &:before {
    content: '';
    display: inline-block;
    width: 5px;
    height: 1.5rem;
    vertical-align: bottom;
    margin-right: 0.8rem;
    background: #3797EB;
  }
}

.add_details {
  width: 100%;
  border-radius: 6px;
  border: 1px solid #C4C4C4;
}

.button-container {
  display: flex;
  justify-content: center;
  align-items: center;
}

.hasTagsView .app-main[data-v-078753dd] {
  background: #f5f7fa;
}

.tableDiv {
  background-color: white;
  padding-bottom: 10px;
}

.formDialog {
  ::v-deep .el-dialog__body {
    height: 850px;
    overflow-y: auto;
  }

  .titleBox {
    height: 22px;
    position: relative;
    display: flex;
    flex-direction: row;
    align-items: center;

    .title {
      font-size: 16px;
      color: black;
      margin: 0;
    }

    .subTitle {
      margin-left: 15px;
      font-size: 12px;
      color: #888888;
    }

    .riskLevel {
      user-select: none;
      position: absolute;
      // top: 0;
      right: 5%;
      display: flex;
      align-items: center;
      flex-direction: row;

      .title {
        font-size: 16px;
        font-weight: bold;
      }

      .main {
        font-size: 16px;
        font-weight: bold;
        padding: 5px 10px 5px 10px;
        color: white;
        box-sizing: border-box;
        border-radius: 5px;
      }

    }
  }
}
.export-preview-dialog ::v-deep .el-dialog__body {
  padding: 10px;
}
</style>
