::v-deep {
  font-family: <PERSON> Ya<PERSON>ei, Microsoft YaHei;

  // 弹框样式
  // .el-dialog {
  //   border-radius: 10px;

  //   .el-dialog__header {
  //     padding: 20px 30px 0 30px;

  //     .el-dialog__title {
  //       font-weight: 500;
  //       font-size: 16px;
  //       color: #1d2129;
  //     }
  //   }

  //   .el-dialog__body {
  //     padding: 20px 30px;
  //   }

  //   .el-dialog__footer {
  //     display: flex;
  //     justify-content: center;
  //     padding: 10px 30px 30px;

  //     .el-button {
  //       margin: 0 10px !important;
  //       height: 38px !important;
  //       font-size: 14px !important;
  //       font-weight: 700 !important;
  //       padding: 10px 37px !important;
  //     }
  //   }
  // }

  // tabs样式
  .el-tabs__header {
    height: 54px;
    background: #eff5ff;
    box-shadow: inset 0px 1px 0px 0px rgba(255, 255, 255, 0.5954);
    border-radius: 10px 10px 0px 0px;
    border: 1px solid #c0d2e2;
    border-bottom: 0;
    margin: 0;

    .el-tabs__nav {
      height: 54px;
      border: 0;
      display: flex;
      align-items: flex-end;
    }

    .el-tabs__item {
      border: 0;
      height: 43px;
      color: #333333;
      font-weight: 700;
      font-family: Microsoft YaHei, Microsoft YaHei;
      // font-size: 14px;
    }

    .el-tabs__item.is-active {
      color: #409EFF;
      border-bottom: 3px solid #409EFF;
    }
  }

  .el-tabs__content {
    border-radius: 0px 0px 10px 10px;
    border: 1px solid #c0d2e2;
    border-top: 0;
    padding-top: 10px;
    background-color: #fff;
  }

  // 按钮样式
  // .el-button {
  //   margin: 0 8px 10px 0;
  //   height: 32px;
  //   font-size: 12px;
  //   font-weight: 400;
  //   padding: 8px 12px;
  // }

  // .el-button--text {
  //   // font-size: 14px;
  //   font-weight: 400;
  //   padding: 0;
  //   margin: 0 8px;
  // }

  // .el-link {
  //   // font-size: 14px;
  //   font-weight: 400;
  // }

  // .el-button.is-circle {
  //   height: 32px !important;
  //   width: 32px !important;

  //   i {
  //     display: flex;
  //     justify-content: center;
  //     align-items: center;
  //   }
  // }

  // 表格样式
  // .el-table .el-table__cell {
  //   padding: 0;
  // }

  // .el-table__body .el-table__row.hover-row td {
  //   background-color: #e1f0ff !important;
  // }

  // 输入框相关样式
  .el-range-input {
    color: #1d2129;
    // font-size: 14px;
  }

  .el-range-editor.el-input__inner {
    // height: 32px;
  }

  .el-range-separator {
    color: #666666 !important;
  }

  .el-range-editor .el-range-input::-webkit-input-placeholder {
    color: #666666;
  }

  .el-tag {
    background: #f2f3f5;
    border-radius: 4px 4px 4px 4px;
    color: #1d2129;
  }

  .el-range-separator {
    color: #c9cdd4;
  }

  .el-input__inner::-webkit-input-placeholder {
    font-weight: 400;
    // font-size: 14px;
    color: #666666;
  }

  .el-input__inner {
    font-family: Microsoft YaHei, Microsoft YaHei !important;
    font-weight: 400;
    // font-size: 14px;
    color: #1d2129;
    // height: 32px;
  }

  // .el-input-number__decrease,
  // .el-input-number__increase {
  //   display: none;
  // }

  // .el-form-item__label {
  //   font-weight: 400;
  //   // font-size: 14px;
  //   color: #333333;
  // }
}