<template>
    <div class="up">
        <div class="table-container">
          <!-- 查询表单 -->

          <div class="opt-container">
            <el-form
              ref="queryForm"
              :inline="true"
              :model="queryParams"
              label-width="68px"
              size="mini"
            >
            <el-form-item label="" prop="budgetYear">
              <el-select
              v-model="queryParams.budgetYear"
              placeholder="年份"
              style="width:120px">
                <el-option v-for="item in yearList" :key="item" :label="item" :value="item">
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item>
                  <selectTree
                    :key="'domainId'"
                    v-model="queryParams.domainId"
                    :deptType="100"
                    :deptTypeList="[1, 3]"
                    clearable
                    filterable
                    placeholder="管养单位"
                    style="width: 240px"
                  />
                </el-form-item>
            <el-form-item>
                  <RoadSection
                    v-model="queryParams.maiSecId"
                    :deptId="queryParams.domainId"
                    placeholder="路段"
                  />
                </el-form-item>
                <el-form-item>
                  <el-button
                    icon="el-icon-search"
                    size="mini"
                    type="primary"
                    @click="getPlan"
                    >搜索</el-button
                  >
                  <el-button
                    icon="el-icon-refresh"
                    size="mini"
                    @click="resetQuery"
                    >重置</el-button
                  >
                </el-form-item>

            </el-form>
            <el-form
              :inline="true"
              label-width="68px"
              size="mini"
            >
            <el-form-item v-if="isnew">
              <el-button
                icon="el-icon-plus"
                size="mini"
                type="primary"
                v-has-menu-permi="['budget:plan:add']"
                @click="addPlan"
                >新增</el-button>
              </el-form-item>
          </el-form>
          </div>
          <!-- 表格数据 -->
          <el-table v-adjust-table
              size="mini"
              style="width: 100%"
              v-loading="loading"
              border
              :data="tableDataTop"
              row-key="id"
              ref="dataTable"
              highlight-current-row
              :height="'calc(100vh - 230px)'"
              :show-summary="true"
              :summary-method="getSummaries"
            >
              <el-table-column
                label="序号"
                align="center"
                type="index"
                width="50"
              />
              <template v-for="(column,index) in columnTop">
                <el-table-column :label="column.label"
                                 v-if="column.visible"
                                 align="center"
                                 :prop="column.field"
                                 :width="column.width">
                  <template slot-scope="scope">
                    <dict-tag v-if="column.dict" :options="dict.type[column.dict]" :value="scope.row[column.field]"/>
                    <template v-else-if="column.slots">
                      <RenderDom :row="scope.row" :index="index" :render="column.render"/>
                    </template>
                    <span v-else>{{ scope.row[column.field] }}</span>
                  </template>
                </el-table-column>
              </template>
              <el-table-column
                  align="center"
                  class-name="small-padding fixed-width"
                  fixed="right"
                  label="操作"
                  width="200"
              >
                <template slot-scope="scope">
                  <el-button
                      icon="el-icon-edit"
                      size="mini"
                      type="text"
                      @click="handleEdit(scope.row)"
                  >{{ handleEditStatus(scope.row) }}
                  </el-button>
                  <el-button
                    icon="el-icon-delete"
                    size="mini"
                    type="text"
                    v-if="isnew === 'true'"
                    v-has-menu-permi="['budget:plan:remove']"
                    @click="handleDelete(scope.row)"
                  >删除
                  </el-button>
                  <el-button
                      icon="el-icon-view"
                      size="mini"
                      type="text"
                      @click="openReviewDialog(scope.row)"
                  >审核意见
                  </el-button>
                </template>
              </el-table-column>
            </el-table>
            <pagination
                v-show="total>0"
                :limit.sync="queryParams.pageSize"
                :page.sync="queryParams.pageNum"
                :pageSizes="pageSizes"
                :total="total"
                @pagination="getPlan"
            />
        </div>
      <el-drawer :wrapperClosable="false" title="编辑" :visible.sync="detailFlag" size="85%" destroy-on-close v-if="detailFlag">
        <div class="btn-container" v-if="!isshow && (selectPlan.status === 0 && isnew === 'true')">
          <el-button type="primary" v-has-menu-permi="['budget:plan:process', 'budget:plan:processadjust']" @click="submitplan()">提交审核</el-button>
        </div>
        <div class="btn-container" v-if="!isshow && (selectPlan.status === 1  && isnew === 'true')">
          <el-button type="primary" v-has-menu-permi="['budget:plan:process', 'budget:plan:processadjust']" @click="submit()">提交审核</el-button>
        </div>
        <div class="btn-container" v-if="!isshow && (selectPlan.status > 1 && selectPlan.status < 6)">
          <el-button type="primary" v-has-menu-permi="['budget:plan:process', 'budget:plan:processadjust']" @click="submit()">审核</el-button>
        </div>
        <div class="app-container maindiv">
          <el-row :gutter="15" style="margin-top: 10px">
            <el-col :span="relaNavRight ? 3 : 0" :xs="24" style="padding: 0">
              <div class="RightDiv">
                <div class="leftIcon" @click="relaNavRight = false" style="top: 210px">
                  <span class="el-icon-caret-left"></span>
                </div>
                <right-tree ref="rightTree"></right-tree>
              </div>
            </el-col>
            <el-col :span="relaNavRight ? 21 : 24" :xs="24">
              <div class="rightIcon" @click="relaNavRight = true" v-show="!relaNavRight" style="top: 210px">
                <span class="el-icon-caret-right"></span>
              </div>
              <!-- 计划申报下 -->
              <template>
                <PlanDeclarationDown />
              </template>
            </el-col>
          </el-row>
        </div>
      </el-drawer>
      <el-dialog title="审核意见" :visible.sync="reviewFlag" width="80%" custom-class="detail-dialog">
        <div class="detail-dialog-content">
          <el-table v-adjust-table :data="reviewDialogData" max-height="870" border>
            <template v-for="(column,index) in reviewDialogColumns">
                  <el-table-column
                    v-if="column.visible"
                    :label="column.label"
                    align="center"
                    :prop="column.field"
                    :width="column.width"
                  >
                    <template slot-scope="scope">
                      <dict-tag v-if="column.dict" :options="dict.type[column.dict]" :value="scope.row[column.field]"/>
                      <template v-else-if="column.slots">
                        <RenderDom :row="scope.row" :index="index" :render="column.render"/>
                      </template>
                      <span v-else>{{ scope.row[column.field] }}</span>
                    </template>
                  </el-table-column>
                </template>
                <el-table-column label="操作" width="100">
                  <template slot-scope="scope">
                    <el-link type="primary"  v-hasPermi="['comm:node:opinion:edit']" @click="openEditNodeDialog(scope.row)">编辑</el-link>
                  </template>
                </el-table-column>
          </el-table>
        </div>
      </el-dialog>
      <el-dialog title="确认" :visible.sync="confirmDialog" width="30%" v-if="confirmDialog">
      <el-form v-model="formData" size="small" :inline="true" label-width="120px">
        <el-form-item label="审核意见">
          <el-input v-model="formData.comment" type="textarea" style="width: 230px"/>
        </el-form-item>
      </el-form>
      <div style="text-align: right">
        <el-button type="danger" v-if="formData.status > 0" @click="handleSubmit(false, formData.status)">驳回</el-button>
        <el-button type="primary" v-if="formData.status > 0" @click="handleSubmit(true,formData.status)">通 过</el-button>
        <el-button type="primary" v-if="formData.status === 0" @click="handleSubmit(true,formData.status)">提交</el-button>
      </div>
    </el-dialog>
    <el-dialog title="新增" :visible.sync="addDialog" width="30%" v-if="addDialog">
      <el-form v-model="addformData" size="small" :inline="true" label-width="150px">
        <el-form-item label="选择年份" prop="year">
          <el-select
              v-model="addformData.year"
              placeholder="年份"
              style="width:120px">
                <el-option v-for="item in yearList" :key="item" :label="item" :value="item">
                </el-option>
              </el-select>
        </el-form-item>
        <el-form-item label="选择管理处">
          <el-select
              v-model="addformData.domainId" filterable
              placeholder="选择管理处"
              style="width:220px" @change="domainSelect">
              <el-option v-for="item in addformData.domainList" :key="item.deptId" :label="item.deptName" :value="item.deptId">
              </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="选择路段">
          <el-select
                v-model="addformData.maiSecId" filterable
                placeholder="选择路段"
                style="width:220px">
            <el-option v-for="item in addformData.maiSecIds" :key="item.maintenanceSectionId" :label="item.maintenanceSectionName" :value="item.maintenanceSectionId">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="是否拷贝上一年度">
          <el-radio-group v-model="addformData.copyLastYear">
            <el-radio-button :label="true">是</el-radio-button>
            <el-radio-button :label="false">否</el-radio-button>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="备注">
          <el-input v-model="addformData.remark" type="textarea" style="width: 230px"/>
        </el-form-item>
      </el-form>
      <div style="text-align: right">
        <el-button type="primary" @click="addPlanSubmit()">确定</el-button>
      </div>
    </el-dialog>
    <el-dialog
      append-to-body
      :visible.sync="editNodeDialog"
      width="650px"
    >
      <el-form :model="editNodeForm">
        <el-form-item label="操作人" label-width="100px">
          <el-cascader
              v-model="editNodeForm.assignee"
              :options="deptUserOptions"
              :props="assigneeProps"
              :show-all-levels="false"
              ref="assigneeRef"
              filterable
              clearable
              collapse-tags
              @change="assigneeChange"
              style="width: 460px"
            >
          </el-cascader>
        </el-form-item>
        <el-form-item label="操作意见" label-width="100px">
          <el-input v-model="editNodeForm.content" style="width: 460px;"/>
        </el-form-item>
        <el-form-item label="操作时间" label-width="100px">
          <el-date-picker
            v-model="editNodeForm.endTime"
            type="datetime"
            format="yyyy-MM-dd HH:mm:ss"
            value-format="yyyy-MM-dd HH:mm:ss"
          >
          </el-date-picker>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="editNodeDialog = false">取 消</el-button>
        <el-button type="primary" @click="saveEditNode">确 定</el-button>
      </span>
    </el-dialog>
    </div>
</template>
<script>
import {
  GetPlanlist,
  submitplan,
  GetReviewList,
  process,
  findByYearList,
  addPlanData,
  DeletePlan
} from "@/api/budgetManage/planManage";
import { findUserDeptMaintenanceList2 } from "@/api/system/maintenanceSection";
import { formatDateNoTime } from '@/utils/index'
import { getTreeStruct,editNodeInfo } from "@/api/tmpl";
import PlanDeclarationDown from "./planDeclarationDown.vue";
import RightTree from "../rightTree.vue";
import selectTree from "@/components/DeptTmpl/selectTree.vue";
import RoadSection from "@/views/baseData/components/roadSection/index.vue";
import RouteCodeSection from "@/components/RouteCodeSection/index.vue";
import { addPlan } from "@/api/budget/plan";

export default {
  components: {
    RoadSection,
    RouteCodeSection,
    selectTree,
    PlanDeclarationDown,
    RightTree,
    RenderDom: {
      functional: true,
      props: {
        row: Object,
        index: Number,
        render: Function
      },
      render(createElement, ctx) {
        const {row, index} = ctx.props
        return ctx.props.render(row, index)
      }
    }
  },
  data() {
      return {
          columnTop: [
              {key: 11, width: 100, field: 'statusName', label: `状态`, visible: true},
              {key: 0, width: 200, field: 'code', label: `计划编码`, visible: true},
              {key: 2,width: 50, field: 'budgetYear', label: `年份`, visible: true},
              {key: 3,width: 120,  field: 'maiSecName', label: `路段名称`, visible: true},
              {key: 4,width: 120,  field: 'operatefund', label: `运营费用`, visible: true},
              {key: 5, width: 120, field: 'testfund', label: `养护检测费`, visible: true},
              {key: 6, width: 120, field: 'dailyfund', label: `日常养护费`, visible: true},
              {key: 7, width: 120, field: 'specialfund', label: `专项工程费`, visible: true},
              {key: 8, width: 120, field: 'damagedfund', label: `被损被盗`, visible: true},
              {key: 9, width: 120, field: 'sumFund', label: `总费用`, visible: true},
              {key: 10, width: 100, field: 'createTime', label: `编制日期`, visible: true,slots: true, render: (row, index) => {
                return (
                  <span>{formatDateNoTime(row.createTime)}</span>
                )
              }},
          ],
          loading:false,
          tableDataTop: [],
          reviewFlag: false,
          reviewDialogData: [],
          infoFlag: false,
          relaNavRight: true,
          detailFlag:false,
          total: 0,
          confirmDialog: false,
          formData: {comment:"",status:1},
          addDialog: false,
          isnew:0,
          isshow: false,
          addformData: {year:new Date().getFullYear(),domainId:"",maiSecId:"",copyLastYear:true,remark:"",domainList:[],maiSecIds:[]},
          yearList:[],
          treeData:[],
          queryParams: {
            pageNum: 1,
            pageSize: 15,
            budgetYear: new Date().getFullYear(),
            budgetType: 1
          },
          pageSizes:[10,15, 20,25, 30,35, 50,100],
          selectPlan:{status:0},
          reviewDialogColumns: [
              {key: 1, width: 150, field: 'nodeName', label: `名称`, visible: true},
              {key: 2, width: 100, field: 'approved', label: `状态`, visible: true, slots: true,  render: (row, index) => {
                return (
                  <span>{row.approved ? '通过' : '驳回'}</span>
                )
              }},
              {key: 3,width: 100, field: 'assigneeName', label: `审核人`, visible: true},
              {key: 4, width: 180,  field: 'endTime', label: `审核时间`, visible: true},
              {key: 5,  field: 'content', label: `内容`, visible: true},
          ],
          editNodeDialog: false,
          editNodeForm: {
            id: '',
            assignee: '',
            content: '',
            startTime: '',
            endTime: '',
            taskId: ''
          },
          nodeCurUser: null,
          deptUserOptions: [],
          assigneeProps: {
            multiple: false, //是否多选
            value: 'id',
            emitPath: false,
          },
          curRow: null
      }
  },
  methods:{
    async getPlan() {
      const res = await GetPlanlist(this.queryParams)
      this.tableDataTop = res.rows
      this.total = res.total
    },
    async GetYearList(){
     const res = await findByYearList();
     this.yearList = res.data || [];
     this.addformData.year = this.yearList[0];
     this.queryParams.budgetYear = this.yearList[0];
    },
    getSummaries(param) {
      const { columns, data } = param;
      const sums = [];
      columns.forEach((column, index) => {
          if (index === 0) {
            sums[index] = '合计';
            return;
          }
          const canCalc = ["operatefund","testfund","dailyfund","specialfund","damagedfund","sumFund",]
          if(canCalc.includes(column.property)){
            const values = data.map(item => Number(item[column.property]));
            if (!values.every(value => isNaN(value))) {
              sums[index] = values.reduce((prev, curr) => {
                const value = Number(curr);
                if (!isNaN(value)) {
                  return prev + curr;
                } else {
                  return prev;
                }
              }, 0);
              sums[index] += ' 元';
            } else {
              sums[index] = 'N/A';
            }
          }
      })
      return sums;
    },
    //新增方案
    async addPlan(){
      let res = await findUserDeptMaintenanceList2();
      this.treeData = res.data || [];
      this.addformData.domainId = "";
      this.addformData.maiSecId="";
      this.addformData.copyLastYear=true;
      this.addformData.remark="";
      this.addformData.domainList=[]
      this.addformData.maiSecIds=[]
      //赋值所有得管理处和路段
      this.addformData.domainList = this.treeData;
      this.addDialog = true;
    },
    //新增方案
    async addPlanSubmit(){
      let param ={
        budgetYear:this.addformData.year,
        domainId:this.addformData.domainId,
        maiSecId:this.addformData.maiSecId,
        copyLastYear:this.addformData.copyLastYear,
        remark:  this.addformData.copyLastYear.remark
      }
      let res = await addPlanData(param)
      if(res.code == 200){
        this.$message.success('提交成功');
      }
      else{
        this.$message.error(res.msg);
      }
      this.addDialog = false;
      await this.getPlan();
    },
    //管理处选择
    domainSelect(value){
      this.addformData.maiSecIds = [];
      var item = this.treeData.find(item => item.deptId === value) || {};
      if(item.children){
          item.children.forEach(sec=>{
            if(!this.addformData.maiSecIds.find(item => item.maintenanceSectionId === sec.maintenanceSectionId))
              this.addformData.maiSecIds.push(sec);
          });
        }
    },
    //提交预算计划
    submitplan(){
      this.formData.comment = '';
      this.formData.status=0;
      this.confirmDialog = true;
    },
    //审核
    async handleSubmit(isapproved,status){
      if(this.formData.comment.length == 0){
        this.$message.error('请输入内容！');
        return;
      }

      if(status === 0){
        const res = await submitplan({businessKey:this.selectPlan.id,status:this.selectPlan.status,comment:this.formData.comment});
        if(res.code == 200){
          this.$message.success('提交成功');
        }
        else{
          this.$message.error(res.msg);
        }
      }
      else{
        const res = await process({businessKey:this.selectPlan.id,status:this.selectPlan.status,comment:this.formData.comment,approved:isapproved,reject:2});
        if(res.code == 200){
          this.$message.success('提交成功');
        }
        else{
          this.$message.error(res.msg);
        }
      }
      this.formData.comment = '';
      this.formData.status=0;
      this.confirmDialog = false;
      this.detailFlag = false;
      this.getPlan()
    },
    //审核
    submit(){
      this.formData.comment = '';
      this.formData.status=1;
      this.confirmDialog = true;
    },
    //判断按钮状态
    handleEditStatus(row){
        if(this.$route.query.isshow)
          return "查看"
        return row.status > 1 ? "审核" : "查看"
    },
    handleEdit(row) {
      this.selectPlan = row;
      this.detailFlag = true
      this.$nextTick(() => {
        window.$Bus.$emit("getPlanId", row.id)
        window.$Bus.$on("domainId", row.domainId);
        if(!this.$route.query.isshow)
          this.$refs.rightTree.getDeptTree(row.budgetYear)
        else
          this.$refs.rightTree.getShowDeptTree(row.id)
      })
    },
    queryDetailTree(data) {
      this.queryParams.domainId = ''
      this.queryParams.maiSecId = ''
      if (data && data.domainId) {
        this.queryParams.domainId = data.domainId
        if (data.hasOwnProperty('maiSecId')) {
          this.queryParams.maiSecId = data.maiSecId
        }
      }
      this.getPlan()
    },
    resetQuery() {
      this.queryParams = {
        pageNum: 1,
        pageSize: 15,
      };
    },
    handleDelete(row) {
      this.$confirm('确定删除吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        DeletePlan(row.id).then(res => {
          if (res.code == 200) {
            this.$message.success('删除成功');
            this.getPlan();
          } else {
            this.$message.error(res.msg);
          }
        })
      })
    },
    openReviewDialog(row) {
      this.curRow = row
      this.getReviewData()
    },
    getReviewData() {
      GetReviewList(this.curRow.id).then(res=> {
        this.reviewDialogData = res.data || []
        this.reviewFlag = true
      })
    },
    openEditNodeDialog(row) {
      this.editNodeForm.id = row.id
      this.editNodeForm.taskId = row.taskid
      this.editNodeForm.assignee = row.userId
      this.editNodeForm.content = row.comment
      this.editNodeForm.startTime = row.startTime
      this.editNodeForm.endTime = row.endTime
      this.nodeCurUser = {
        label: row.assignee,
        value: row.userId
      }
      this.editNodeDialog = true
    },
    assigneeChange() {
      this.$nextTick(() => {
        this.nodeCurUser = this.$refs.assigneeRef.getCheckedNodes()[0]
      })
    },
    saveEditNode() {
      const submitData = {
        ...this.editNodeForm
      }
      submitData.assignee = `${this.nodeCurUser.label}@${this.nodeCurUser.value}`
      submitData.startTime = submitData.endTime
      editNodeInfo(submitData).then(()=> {
        this.getReviewData()
        this.editNodeDialog = false
      })
    },
    getDeptTreeDef() {
      getTreeStruct({ types: 111 }).then((response) => {
        this.deptUserOptions = response.data;
      });
    },
  }
  ,
  created() {
    if(this.$route.query.status)
      this.queryParams.statusstr =this.$route.query.status
    if(this.$route.query.isnew)
      this.isnew =this.$route.query.isnew
    if(this.$route.query.isshow)
      this.isshow =this.$route.query.isshow
    this.GetYearList();
    // 查询计划列表
    this.getPlan()
    this.getDeptTreeDef()
  },
  updated() {
    this.$nextTick(() => {
      this.$refs["dataTable"].doLayout();
    });
  },

}
</script>
<style  scoped lang="scss">
  .up{
    background-color: #fff;
    .table-container{
      padding: 10px;
      ::v-deep .el-table__footer-wrapper tr{
        height: 36px;
      }
      .opt-container{
        margin-bottom: 5px;
      }
    }
  }
  .RightDiv {
    border-right: 1px solid #d8dce5;
    height: 46rem;
    position: relative;
    padding-top: 10px;
    background-color: white;
    margin-left: 10px;
  }

  .leftIcon {
    border: 1px solid #dcdfe6;
    border-radius: 8px;
    width: 16px;
    height: 50px;
    line-height: 50px;
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
    position: absolute;
    right: 0;
    top: 300px;
    z-index: 2;
  }
  .btn-container{
    margin-left: 25px;
  }
  .app-container{
    margin: 5px;
    padding: 5px;
  }
  .leftIcon:hover {
    background-color: #dcdfe6;
  }
  .rightIcon {
    border: 1px solid #dcdfe6;
    border-radius: 8px;
    width: 16px;
    height: 50px;
    line-height: 50px;
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
    position: absolute;
    left: -10px;
    top: 280px;
    z-index: 10;
    background: white;
  }

  .rightIcon:hover {
    background-color: #dcdfe6;
  }

  ::v-deep .detail-dialog {
    height: 600px;
    overflow-y: auto;
  }
  .detail-dialog-content {
    // padding: 20px;
  }
</style>
<style lang="scss" scoped>
@import "@/assets/styles/business.scss";
</style>
