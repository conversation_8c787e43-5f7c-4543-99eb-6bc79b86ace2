<template>
  <div class="lon-lat-input">
    <template v-if="inputChange && (type == 'lonlat' || type == 'lon' || type === 'lat')">
      <el-input placeholder="请输入经度" v-model="longitude" v-if="type == 'lonlat' || type == 'lon'">
        <template slot="prepend">
          <el-button type="info">lon</el-button>
        </template>
      </el-input>
      <el-input placeholder="请输入纬度" v-model="latitude" v-if="type == 'lonlat' || type == 'lat'"
        style="margin-left: 5px;">
        <template slot="prepend">
          <el-button type="info">lat</el-button>
        </template>
      </el-input>
      <div class="el-icon-sort" slot="suffix" style="line-height: 30px;cursor: pointer;" @click="onChange(false, true)">
      </div>
    </template>
    <template v-else>
      <el-input :placeholder="placeholder" v-model="lonlat">
        <div class="el-icon-sort" slot="suffix" style="line-height: 30px;cursor: pointer;margin-right: 5px;"
          @click="onChange(true, true)" v-if="type !== 'lon' && type !== 'lat'"></div>
        <template slot="append">
          <el-button type="info" @click="onChooseLonLat" icon="el-icon-location">{{ btnText }}</el-button>
        </template>
      </el-input>
    </template>
  </div>
</template>

<script>
import { chooseLonLat } from './choose';

export default {
  name: 'lonLat',
  model: {
    prop: 'value',
    event: 'change',
  },
  props: {
    // 类型 lonlat || '' 经纬度，lon 经度，lat 纬度 -- 返回对应的经纬度、经度、纬度
    type: {
      type: String,
      default: 'lonlat'
    },
    value: {
      type: String,
      default: ''
    },
    lon: {

    },
    lat: {

    },
  },
  data() {
    return {
      lonlat: '',
      inputChange: false,
    }
  },
  mounted() {

    this.$forceUpdate()
  },
  computed: {

    longitude: {
      get() {

        return this.lon
      },
      set(val) {
        this.$emit('update:lon', val)
      }
    },
    latitude: {
      get() {
        return this.lat
      },
      set(val) {
        this.$emit('update:lat', val)
      }
    },

    btnText() {
      let str = ''
      if (this.type === 'lonlat' || this.type === '') {
        str = '选择经纬度'
      } else if (this.type === 'lon') {
        str = '选择经度'
      } else {
        str = '选择纬度'
      }
      return str;
    },
    placeholder() {
      let str = ''
      if (this.type === 'lonlat' || this.type === '') {
        str = '经纬度，英文逗号分隔'
      } else if (this.type === 'lon') {
        str = '请输入经度'
      } else {
        str = '请输入纬度'
      }
      return str;
    },
  },
  watch: {
    lonlat(val) {
      if (val) {
        this.onChange(true, false);
        this.$emit('change', val);
      }
    },
    longitude: {
      handler(val) {
        if (val) {
          this.onChange(false, false);
          this.$emit('update:lon', val)
        } else {
          this.lonlat = this.latitude
        }
      },
      deep: true,
      immediate: true,
    },
    latitude: {
      handler(val) {
        if (val) {
          this.onChange(false, false);
          this.$emit('update:lat', val)
        } else {
          this.lonlat = this.longitude
        }
      },
      deep: true,
      immediate: true,
    },
  },
  methods: {
    onChooseLonLat() {
      chooseLonLat().then(res => {
        let arr = [];
        if (res) {
          arr = res.split(',');
          if (this.type === 'lon') {
            this.lonlat = arr[0];
          } else if (this.type === 'lat') {
            this.lonlat = arr[1];
          } else {
            this.lonlat = res
          }
        }
        this.$emit('change', this.lonlat);
      })
    },
    onChange(bool, bools) {
      if (bools) {
        this.inputChange = bool;
      }
      // 数据转化
      if (bool) {
        if (this.type === 'lonlat' && this.lonlat) {
          let arr = this.lonlat.split(',');
          this.longitude = arr[0];
          this.latitude = arr[1];
        } else {
          this.longitude = '';
          this.latitude = '';
        }
        if (this.type === 'lon') {
          this.longitude = this.lonlat;
        }
        if (this.type === 'lat') {
          this.latitude = this.lonlat;
        }
      } else {
        if (this.type === 'lonlat' && this.longitude && this.latitude) {
          this.lonlat = this.longitude + "," + this.latitude
        }
        if (this.type === 'lon') {
          this.lonlat = this.longitude
        }
        if (this.type === 'lat') {
          this.lonlat = this.latitude
        }
      }
    },
  }
}
</script>

<style lang="scss" scoped>
.lon-lat-input {
  width: 100%;

  display: flex;
  align-items: center;
  color: #0a0909;
}
::v-deep .el-input-group__prepend {
  padding: 0 10px !important;
}
::v-deep .el-input-group__append {
  padding: 0 10px !important;
  overflow: hidden;
}
</style>
