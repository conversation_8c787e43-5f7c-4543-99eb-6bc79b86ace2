<template>
  <PageContainer>
    <template slot="search">
      <div style="display: flex; align-items: center; margin: 0">
        <CascadeSelection
            :form-data="searchForm"
            v-model="searchForm"
            types="201"
            multiple
        />
        <el-select v-model="searchForm.culvertTypes" multiple clearable filterable  placeholder="涵洞类型" style="margin:0 20px">
          <el-option  v-for="dict in dict.type.sys_culvert_type" :key="'sys_culvert_type' + dict.value" :label="dict.label"
                      :value="dict.value">
          </el-option>
        </el-select>

        <div style="min-width:220px;">
          <el-button v-hasPermi="['baseData:culvert:query']" type="primary" icon="el-icon-search" @click="onSearch">搜索</el-button>
          <el-button icon="el-icon-refresh" @click="resetQuery">
            重置
          </el-button>
          <el-button
              v-show="!showSearch"
              icon="el-icon-arrow-down"
              circle
              @click="showSearch=true"
          />
          <el-button
              v-show="showSearch"
              icon="el-icon-arrow-up"
              style="color: #1890ff;border-color: #badeff;background-color: #e8f4ff;"
              circle
              @click="showSearch=false"
          />
        </div>

      </div>
      <div v-if="showSearch" style="margin-top: 5px">
        <el-input v-model="searchForm.culvertCode" placeholder="涵洞编码" @keyup.enter.native="onSearch" style="margin-right:20px;width: 170px" clearable/>
        <el-select
            v-model="searchForm.operationState"
            placeholder="运营状态"
            clearable
            collapse-tags
            style="width: 170px"
        >
          <el-option
              v-for="dict in dict.type.sys_operation_state"
              :key="'sys_operation_state' + dict.value"
              :label="dict.label"
              :value="dict.value"
          />
        </el-select>
        <el-select
            style="margin-left:20px;width: 170px"
            v-model="searchForm.status"
            placeholder="数据状态"
            clearable
        >
          <el-option
              v-for="dict in dict.type.base_data_state"
              :key="'base_data_state' + dict.value"
              :label="dict.label"
              :value="dict.value"
          />
        </el-select>
      </div>
    </template>
    <template slot="header">
      <el-row :gutter="10">
        <el-button v-hasPermi="['baseData:culvert:add']" type="primary" @click="onAdd" class="mb8">新增</el-button>
        <el-button v-hasPermi="['baseData:culvert:edit']" type="primary" @click="onEdit" class="mb8">编辑</el-button>
        <el-button v-hasPermi="['baseData:culvert:delete']" type="primary" @click="onRemove" class="mb8">删除</el-button>
        <el-button v-hasPermi="['baseData:culvert:getInfoById']" type="primary" @click="onDetail"
          class="mb8">查看</el-button>
        <el-button v-hasPermi="['baseData:culvert:updateLocked']" type="primary" @click="onLocked"
          class="mb8">是否锁定</el-button>
        <el-button v-hasPermi="['baseData:culvert:status']" type="primary" @click="onStatusChange" class="mb8">
          运营状态变更
        </el-button>
        <el-button v-hasPermi="['baseData:culvert:drawing']" type="primary" @click="onDrawing" class="mb8">查看图纸</el-button>
        <el-button v-hasPermi="['baseData:culvert:getCard']" type="primary" @click="onCard(1)" class="mb8">查看卡片</el-button>
        <el-button v-hasPermi="['baseData:culvert:cardExport']" type="primary" @click="onCardExport" class="mb8">导出卡片</el-button>
        <!-- <el-button v-hasPermi="['baseData:culvert:card']" type="primary" @click="onCard(2)" class="mb8">编辑卡片</el-button> -->
        <el-button v-hasPermi="['baseData:culvert:exportPageTable']" type="primary" @click="onExport" class="mb8">导出清单</el-button>
        <el-button v-hasPermi="['baseData:culvert:export']" type="primary" @click="dataExport"
          class="mb8">数据导出</el-button>
        <el-button v-hasPermi="['baseData:culvert:importAdd']" type="primary" @click="onImport(2)"
          class="mb8">导入新增</el-button>
        <el-button v-hasPermi="['baseData:import:execute']" type="primary" @click="onImport(1)"
          class="mb8">导入更新</el-button>
        <el-button v-hasPermi="['baseData:import:execute']" type="primary" @click="onQrCode" class="mb8">
          二维码下载
        </el-button>
        <el-button
          v-hasPermi="['baseData:culvert:submitAudit']"
          type="primary"
          @click="taskBridge"
        >提交审核</el-button>
      </el-row>
    </template>
    <template slot="body">
      <el-table v-adjust-table v-loading="loading" :data="tableData" height="100%" border @selection-change="onSelectTable"
        @row-click="clickTable" ref="tableRef" :row-key="(row) => row.id">
        <el-table-column fixed="left" type="selection" width="50" align="center" :selectable="checkSelectable"
          :reserve-selection="true" />
        <el-table-column label="序号" fixed="left" type="index" width="55" align="center" >
          <template v-slot="scope">
            {{ (scope.$index + (searchForm.pageNum - 1) * searchForm.pageSize)+1 }}
          </template>
        </el-table-column>
        <el-table-column label="操作" fixed width="50" align="center" class-name="small-padding fixed-right">
          <template slot-scope="{ row }">
            <el-link  type="primary"  :disabled="!row.shape"  @click.stop="onToMap(row)">定位</el-link>
          </template>
        </el-table-column>
        <el-table-column label="涵洞编码" fixed="left" min-width="200" show-overflow-tooltip align="center" prop="culvertCode" />
        <el-table-column label="管理处" width="150" align="center" prop="managementMaintenanceName" />
        <el-table-column label="管辖分处" width="150" align="center" prop="managementMaintenanceBranchName" />
        <el-table-column label="养护路段" width="150" align="center" prop="maintenanceSectionName" />
        <el-table-column label="路线编码" width="80" align="center" prop="routeCode" />
        <el-table-column label="中心桩号" width="180" align="center">
          <template slot-scope="scope">
            <span v-if="scope.row && scope.row.centerStake">{{
              formatPile(scope.row.centerStake)
            }}</span>
          </template>
        </el-table-column>

        <el-table-column label="涵洞类型" width="150" align="center" prop="startPointName">
          <template slot-scope="scope">
            <span v-if="scope.row && scope.row.culvertType">
              <DictTag :value="scope.row.culvertType" :options="dict.type.sys_culvert_type" />
            </span>
          </template>
        </el-table-column>

        <el-table-column align="center" prop="operationState" width="110" label="运营状态">
          <template slot-scope="{ row }">
            <el-link :underline="false" :type="{ 1: 'info', 2: 'success', 3: 'danger', 4: 'primary' }[
              row.operationState
            ]
              "  @click.stop="onStatusList(row)">
              <DictTag :value="row.operationState" :options="dict.type.sys_operation_state" />
            </el-link>
          </template>
        </el-table-column>
        <el-table-column align="center" prop="operationState" width="110" label="是否锁定">
          <template slot-scope="{ row }">
            <el-link :underline="false" :type="row.isLocked ? 'danger' : 'info'"
               @click.stop="onLocked(row)">
              {{ row.isLocked ? "是" : "否" }}
            </el-link>
          </template>
        </el-table-column>
        <el-table-column align="center" prop="operationState" width="110" label="数据状态">
          <template slot-scope="{ row }">
            <el-link

              :underline="false"
              :type="{ 1: 'success', 2: 'primary', 3: 'info', 4: 'danger',5:'success'}[row.status]"

            >
              <DictTag
                :value="row.status"
                :options="dict.type.base_data_state"
              />
            </el-link>
          </template>
        </el-table-column>
        <el-table-column label="涵洞跨径(米)" width="150" align="center" prop="culvertSpan" />
        <el-table-column label="涵洞长度(米)" width="150" align="center" prop="culvertLength" />
        <el-table-column label="涵洞净高(米)" width="150" align="center" prop="culvertHeight" />
        <el-table-column label="查看" fixed="right" align="center">
          <template #default="{ row }">
            <el-link type="primary" :underline="false" v-hasPermi="['baseData:culvert:getInfoById']"
              @click.stop="onDetail(row)">查看</el-link>
          </template>
        </el-table-column>
      </el-table>
      <pagination v-show="pages.total > 0" :total="pages.total" :page.sync="pages.pageNum" :limit.sync="pages.pageSize"
        @pagination="getList" />
    </template>

    <Dialog :title="Object.keys(modelData).length > 0 ? '编辑涵洞信息' : '新增涵洞信息'
      " :show.sync="showEdit" :width="'60%'">
      <Edit v-if="showEdit" :form-data="modelData" @close="() => {
        showEdit = false;
        modelData = {};
        getList();
      }
        " />
    </Dialog>

    <!-- <Dialog title="查看涵洞信息" :show.sync="showCard">
      <ViewCard v-if="showCard" :form-data="modelData" />
    </Dialog> -->
    <Dialog title="查看涵洞信息" :show.sync="showDetail" :width="'70%'">
      <Detail v-if="showDetail" :form-data="modelData " @close="() => {
        showDetail = false;
      }
        " />
    </Dialog>
    <ImportData v-if="showImportAdd" :is-update="isUpdate" :dialog-visible="showImportAdd"
      :import-base-type="importBaseType" :import-type="importType" @close="closeImportAdd" />
    <!-- 查看图纸、二维码下载、照片弹窗 -->
    <Dialog :title="showTitle" :show.sync="showVisible">
      <div v-if="showType == 1">暂无数据</div>
      <div v-else-if="showType == 2"></div>
    </Dialog>
    <Drawings
      v-if="showDrawing"
      :showDrawing="showDrawing"
      :typeCode="'BS131'"
      :id="tableSelects[0].assetId"
      title="涵洞图纸文件"
      @close="showDrawing = false"
    />
    <Card
      :dialogVisible="showCard"
      :forView="forView"
      :title="title"
      :cardId="cardId"
      @close="() => {showCard = false; forView = false; title = ''}"
    />
    <MapPosition
      v-if="showMapPosition"
      :dialogVisible="showMapPosition"
      :data="mapPositionData"
      @close="showMapPosition = false"
    />
  </PageContainer>
</template>

<script>
import {
  culvertList,
  culvertDelete,
  updateLocked,
} from "@/api/baseData/culvert/culvertInfo/index.js";

import {
  audit,
} from '@/api/baseData/bridge/baseInfo/index'

import SelectTreeCheckbox from "@/components/DeptTmpl/selectTreeCheckbox";
import selectTree from "@/components/DeptTmpl/selectTree";
import RouteLine from "@/components/RouteLine";
import RouteRoad from "@/components/RouteRoad";
import DictTag from "@/components/DictTag";
import Dialog from "@/components/Dialog/index.vue";
import Edit from "./edit.vue";
// import ViewCard from "./viewCard.vue";
import Card from "./components/Card.vue";
import Detail from "./detail.vue";
import ImportData from "@/views/baseData/components/importData/index.vue";
import Drawings from '@/views/baseData/components/drawings/index.vue'
import CascadeSelection from "@/components/CascadeSelection/index.vue";
import { listMaintenanceSectionAll } from "@/api/system/maintenanceSection";
import { listByMaintenanceSectionId } from "@/api/baseData/common/routeLine";
import { statusDialog } from "@/views/baseData/components/statusDialog/index.js";
import { statusListDialog } from "@/views/baseData/components/statusDialog/list.js";
import MapPosition from '@/components/mapPosition/index.vue'

export default {
  name: "CulvertInfo",
  dicts: ["sys_culvert_type", "sys_operation_state",'base_data_state'],
  components: {
    SelectTreeCheckbox,
    selectTree,
    RouteLine,
    RouteRoad,
    Dialog,
    DictTag,
    // ViewCard,
    Card,
    Edit,
    ImportData,
    Detail,
    CascadeSelection,
    Drawings,
    MapPosition
  },
  data() {
    return {
      loading: false,
      showMore: false, // 更多搜索条件
      manageIds: "", // 选中数组
      tableSelects: [], // table选中数组
      tableData: [],
      showEdit: false, // 添加/编辑
      showCard: false, // 查看卡片
      showDetail: false, // 查看
      modelData: {}, // 模态框数据
      searchForm: {
        operationState: "2", // 默认2 运营中
      },
      pages: {
        pageNum: 1,
        pageSize: 20,
        total: 0,
      },
      showImportAdd: false, // 导入弹窗
      isUpdate: false,
      importBaseType: "3", // 导入基础数据类型 1桥梁 2涵洞 3涵洞 4互通
      importType: 2, // 导入类型 1更新 2新增
      routeOptions: [], // 路段数据
      routeList: [], // 路线编码数据
      showVisible: false,
      showTitle: "",
      showType: 1, // 1、显示查看图纸、二维码下载,2、查看图片
      tableH: "92%", // 表格高度
      showDrawing: false,
      forView: false,
      title: '',
      cardId: '',
      showSearch: false,
      showMapPosition: false,
      mapPositionData: undefined
    };
  },
  created() {
    this.init();
    // 改变表格高度
    this.tableH = window.innerHeight - 300 + "px";
    window.addEventListener("resize", () => {
      this.tableH = window.innerHeight - 300 + "px";
    });
  },
  methods: {
    init() {
      this.getList();
    },
    /** 查询列表 */
    getList() {
      this.loading = true;
      this.$refs?.tableRef?.clearSelection();
      // this.searchForm.manageMaintainUnitId = this.manageIds.join(',')
      // 2024年6月21日
      // this.searchForm.managementMaintenanceId = '';
      // 路段
      // this.searchForm.routeIds = this.searchForm.maintenanceSectionId?[this.searchForm.maintenanceSectionId]:null
      this.searchForm = Object.assign(this.searchForm, this.pages);
      culvertList(this.searchForm)
        .then((res) => {
          if (res.code === 200) {
            this.tableData = res.rows || [];
            this.pages.total = res.total;
          }
        })
        .finally(() => {
          this.loading = false;
        });
    },
    /** 搜索按钮操作 */
    onSearch() {
      this.pages.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.searchForm = {
        pageNum: 1,
        pageSize: 20,
        operationState: "2"
      }
      this.onSearch();
    },
    onAdd() {
      this.modelData = {};
      this.showEdit = true;
    },
    onDetail(row) {
      let data;
      if (row && !row.id) {
        if (this.tableSelects && this.tableSelects.length != 1) {
          this.$modal.msgWarning("请选择一条数据");
          return;
        }
        data = this.tableSelects[0];
      } else {
        data = row;
      }
      this.modelData = data;
      this.showDetail = true;
    },
    onCard(type) {
      if (this.tableSelects && this.tableSelects.length != 1) {
        this.$modal.msgWarning("请选择一条数据");
        return;
      }
      if (type == 1) {
        this.cardId = this.tableSelects[0].id
        this.forView = true
        this.showCard = true
        this.title = '查看涵洞卡片'
      } else {
        this.cardId = this.tableSelects[0].id
        this.forView = false
        this.showCard = true
        this.title = '编辑涵洞卡片'
      }
    },
    // 运营状态变更
    onStatusChange() {
      if (this.tableSelects && this.tableSelects.length != 1) {
        this.$modal.msgWarning("请选择一条数据");
        return;
      }
      let row = this.tableSelects[0];
      // baseDataType 基础数据类型 1桥梁 2涵洞 3涵洞 4互通 5 路基宽度
      statusDialog({ dataId: row.assetId, baseDataType: 3 }).then((res) => {
        if (res) {
          this.getList();
        }
      });
    },
    // 查看运营变更记录
    onStatusList(row) {
      statusListDialog({ dataId: row.assetId, baseDataType: 3 });
    },
    // 查看图纸
    onDrawing(row) {
      if (this.tableSelects.length !== 1) {
        this.$message.warning('请选择一条数据！')
        return
      } else {
        this.showDrawing = true
      }
    },
    // 二维码下载
    onQrCode() {
      // this.showTitle = '涵洞二维码'
      // this.showVisible = true
      // tableSelects[0]
      let msg =
        this.tableSelects && this.tableSelects.length > 0
          ? "确定下载选中数据相关二维码？"
          : "确定下载二维码？";
      this.$modal
        .confirm("确定下载二维码？")
        .then(() => {
          if (this.tableSelects && this.tableSelects.length) {
            this.searchForm.ids = this.tableSelects.map((v) => v.id);
          }
          this.download(
            "/baseData/culvert/genQrCode",
            this.searchForm,
            `二维码_${new Date().getTime()}.xlsx`,
            {
              headers: { "Content-Type": "application/json;" },
              parameterType: "body",
            }
          );
        })
        .then(() => {
          this.searchForm.ids = [];
        })
        .catch(() => { });
    },
    // 导出卡片
    onCardExport() {
      if (this.tableSelects && this.tableSelects.length === 0) {
        this.$message.warning("请选择一条数据！");
      } else {
        let params = {
          ...this.searchForm,
          ids: this.tableSelects.map(v=> v.id)
        }
        this.$modal
          .confirm(`已选择${this.tableSelects.length}条涵洞数据，确认导出数据？`)
          .then(() => {
            this.download(
              "/baseData/culvert/cardExport",
              params,
              `卡片_${new Date().getTime()}.xlsx`,
              {
                headers: { "Content-Type": "application/json;" },
                parameterType: "body",
              }
            );
          })
          .catch(() => {});
      }
    },
    // 是否锁定
    onLocked(row) {
      let ids = [];
      let msg = "";
      if(row && !row.id) {
        row = this.tableSelects
      }
      // 判断接收参数是 对象还是 数组
      if (Object.prototype.toString.call(row) == "[object Array]") {
        if(row.length == 0) {
          this.$modal.msgWarning("请选择至少一条数据！");
          return;
        }
        ids = row.map((v) => v.id);
        msg = "确定要更新锁定状态？";
      } else {
        ids = [row.id];
        msg = row.isLocked ? "确定要解除锁定？" : "确定锁定？";
      }

      this.$modal
        .confirm(msg, "温馨提示")
        .then(() => {
          this.$modal.loading("提交中，请稍后...");
          updateLocked(ids)
            .then((res) => {
              if (res.code == 200) {
                this.$modal.msgSuccess("更新成功");
                this.getList();
              }
            })
            .finally(() => {
              this.$modal.closeLoading();
            });
        })
        .then(() => { })
        .catch(() => { });
    },
    onImport(type = 2) {
      this.isUpdate = false;
      this.showImportAdd = true;
      this.importType = type;
      return;
      const inputEl = document.createElement("input");
      inputEl.style.display = "none";
      inputEl.type = "file";
      inputEl.accept = ".xls,.xlsx,.xlsm";
      document.body.appendChild(inputEl);
      inputEl.click();
      inputEl.onchange = (e) => {
        for (const key in e.target.files) {
          if (!isNaN(Number(key))) {
            this.loading = true;
            const formData = new FormData();
            formData.append("file", e.target.files[key]);
            /* importData(formData).then(res => {
              if (res.code === 200) {
                this.$message.success('导入成功！')
                this.getList()
              }
            }).finally(() => {
              this.loading = false
              document.body.removeChild(inputEl)
            }) */
          }
        }
      };
    },
    onExport() {
      let ids = this.tableSelects.map(item => item.id)
      if (ids.length === 0) {
        this.$modal
          .confirm('即将导出所有表格数据，此过程可能花费时间较长，是否继续？')
          .then(() => {
            this.download(
              '/baseData/culvert/exportPageTable',
              this.searchForm,
              `road_interflow_${new Date().getTime()}.xlsx`,
              {
                headers: { 'Content-Type': 'application/json;' },
                parameterType: 'body'
              }
            )
          })
          .catch(() => {})
      } else {
        this.$modal
          .confirm(`已选择${ids.length}条涵洞数据，确认导出清单？`)
          .then(() => {
            this.download(
              '/baseData/culvert/exportPageTable',
              { ids: ids },
              `static_${new Date().getTime()}.xlsx`,
              {
                headers: { 'Content-Type': 'application/json;' },
                parameterType: 'body'
              }
            )
          })
          .catch(() => {})
      }
    },

    taskBridge() {
      //this.selectdTables数组中的status，只要有一个不是2或者4.就不能提交审核
      let flag = false
      this.tableSelects.forEach((item, index) => {
        if (item.status != 2 && item.status != 4 && item.isLocked) {
          flag = true
        }
      })
      if (this.tableSelects.length === 0) {
        this.$message.warning('请选择至少一条数据进行审核！')
        return
      } else if (flag) {
        this.$message.warning('此数据无法提交审核！')
        return
      }
      this.$modal
        .confirm('确认审核？')
        .then(() => {
          let list = []
          this.tableSelects.forEach((item, index) => {
            list.push({
              businessKey: item.id,
              variables: {
                managementMaintenanceId:item.managementMaintenanceId,
                managementMaintenanceBranchId:item.managementMaintenanceBranchId,
                maintenanceSectionId:item.maintenanceSectionId,
                assetId: item.assetId,
                type:'10',
                assetCode: item.culvertCode,
                assetName: item.culvertName,
                routeCode: item.routeCode,
                routeName: item.routeName,
                maintenanceSectionName: item.maintenanceSectionName,
                managementMaintenanceName: item.managementMaintenanceName,
                managementMaintenanceBranchName:
                  item.managementMaintenanceBranchName
              }
            })
          })

          audit({
            businessDTOS: list,
            processDefinitionKey: 'base_data_audit'
          }).then(res => {
            if (res.code === 200) {
              this.getList()
              this.$modal.msgSuccess('审核成功')
            }
          })
        })
        .catch(() => {})
    },

    // 数据导出
    dataExport() {
      let ids = this.tableSelects.map(item => item.id)
      if (ids.length === 0) {
        this.$modal
          .confirm('即将导出所有表格数据，此过程可能花费时间较长，是否继续？')
          .then(() => {
            this.download(
              '/baseData/culvert/export',
              this.searchForm,
              `road_interflow_${new Date().getTime()}.xlsx`,
              {
                headers: { 'Content-Type': 'application/json;' },
                parameterType: 'body'
              }
            )
          })
          .catch(() => {})
      } else {
        this.$modal
          .confirm(`已选择${ids.length}条涵洞数据，确认导出数据？`)
          .then(() => {
            this.download(
              '/baseData/culvert/export',
              { ids: ids },
              `static_${new Date().getTime()}.xlsx`,
              {
                headers: { 'Content-Type': 'application/json;' },
                parameterType: 'body'
              }
            )
          })
          .catch(() => {})
      }
    },
    onSelectTable(list) {
      this.tableSelects = list;
    },
    // 禁止复选框勾选
    checkSelectable(row) {
      return true;
      // if(row.isLocked) {
      //   return false;
      // }else {
      //   return true;
      // }
    },
    // 点击行
    clickTable(row) {
      // if(row.isLocked) return;
      let arr = this.tableSelects.filter((v) => v.id == row.id);
      // 点击行 选中复选框
      if (arr && arr.length > 0) {
        this.$refs.tableRef.toggleRowSelection(row, false);
        this.tableSelects = this.tableSelects.filter((v) => v.id != row.id);
      } else {
        this.tableSelects = [...this.tableSelects, ...[row]];
        this.$refs.tableRef.toggleRowSelection(row, true);
      }
    },
    onEdit() {
      if (this.tableSelects && this.tableSelects.length !== 1) {
        this.$modal.msgWarning("请选择一条数据进行编辑！");
        return;
      }

      let e = this.tableSelects[0]
      let canEdit = true

      if (e.isLocked || e.status == 3) {
        canEdit = false
        this.$message.error(
          '桥梁名称：' + e.culvertCode + '已锁定或正在审核，不允许编辑！'
        )
      }

      if (canEdit) {
        this.modelData = this.tableSelects[0];
        this.showEdit = true;
      }
    },
    // 跳转
    onToMap(row) {
      this.mapPositionData = row
      this.showMapPosition = true
    },
    onRemove() {
      if (this.tableSelects && this.tableSelects.length == 0) {
        this.$modal.msgWarning("请选择至少一条数据进行删除！");
        return;
      }
      this.$confirm("确定删除所有选中数据吗?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        this.loading = true;
        let ids = this.tableSelects.map((v) => v.id);
        culvertDelete(ids)
          .then((res) => {
            if (res.code == 200) {
              this.$message.success("删除成功");
              this.getList();
            }
          })
          .finally(() => {
            this.loading = false;
          });
      });
    },
    closeImportAdd(v) {
      this.showImportAdd = false;
      if (v) this.getList();
    },
    // 监听 管理处选择
    handleTreeChange(e) {
      if (!e) return;
      listMaintenanceSectionAll({ departmentId: e }).then((res) => {
        if (res.code == 200) {
          this.routeOptions = res.data || [];
          // 置空选中路段
          this.searchForm.maintenanceSectionId = "";
          // 置空选中路线编码
          this.searchForm.routeCodes = [];
          // 置空线路编码数据
          this.routeList = [];
        }
      });
    },
    // 监听路段选择变化
    maintenanceSectionChange(e) {
      if (!e) return;
      listByMaintenanceSectionId({ maintenanceSectionId: e }).then((res) => {
        if (res.code == 200) {
          this.routeList = res.data || [];
          // 置空选中路线编码
          this.searchForm.routeCodes = [];
        }
      });
    },

    // 查看图片
    onImgPreview(row) {
      this.showVisible = true;
      this.showTitle = "照片";
      this.showType = 2;
    },
  },
};
</script>

<style lang="scss" scoped>
/* 输入框超出隐藏，不换行*/
::v-deep .el-select__tags {
    flex-wrap: nowrap;
    overflow: auto;
  }

::v-deep .el-scrollbar {
  .el-select-dropdown__item {
    padding: 0;
  }
}
.custom-select {
    .el-select-dropdown__item {
      padding: 0;
      background-color: #0f0;
      /* 修改背景颜色为绿色 */
    }
  }
</style>
