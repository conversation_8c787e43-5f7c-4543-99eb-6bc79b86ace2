<template>
  <el-tabs
    v-model="editableTabsValue"
    type="card"
    tab-position="left"
    style="height: 100vh"
    @tab-remove="removeTab"
    :before-leave="beforeLeave"
    class="my-tab-pane"
  >
    <el-tab-pane
      v-for="item in editableTabs"
      :key="item.id"
      :label="item.name"
      :name="item.name"
      cloasable
    >
      <template v-slot:label>
        <div class="tab-label-container">
          <span
            v-if="!item.isEditing"
            @dblclick="startEditing(item)"
            :ref="'span-' + item.id"
          >
            {{ item.name }}
          </span>
          <el-input
            v-else
            v-model="item.editingName"
            class="tab-input"
            size="mini"
            :style="{
              width: item.editWidth + 'px',
            }"
            @blur="cancelEdit(item)"
            @keyup.enter="confirmEdit(item)"
          ></el-input>
          <el-button
            v-if="!item.isEditing"
            type="text"
            icon="el-icon-edit"
            @click.stop="startEditing(item)"
          ></el-button>
          <el-button
            v-if="!item.isEditing"
            type="text"
            icon="el-icon-delete"
            @click.stop="deleteItem(item)"
          ></el-button>
          <el-button
            v-if="item.isEditing"
            type="text"
            icon="el-icon-check"
            @click.stop="confirmEdit(item)"
          ></el-button>
          <el-button
            v-if="item.isEditing"
            type="text"
            icon="el-icon-close"
            @click.stop="cancelEdit(item)"
          ></el-button>
        </div>
      </template>
      <cardBox :type-id="item.id"></cardBox>
    </el-tab-pane>
    <el-tab-pane>
      <template v-slot:label>
        <div class="tab-plus-label">
          <i class="el-icon-plus"></i>
        </div>
      </template>
    </el-tab-pane>
  </el-tabs>
</template>

<script>
import cardBox from "@/components/ModuleFile/cardBox.vue";
import { getList } from "@/api/system/moduleType.js";

export default {
  name: "moduleFileTest",
  components: {
    cardBox,
  },
  data() {
    return {
      list: [],
      editableTabsValue: "1",
      currentIndex: 1,
      editableTabs: [
        {
          title: "活动1",
          name: "1",
        },
      ],
      tabIndex: 1,
      addIndex: 1,
    };
  },
  props: {
    isUpload: {
      type: Boolean,
      default: false,
    },
  },
  provide() {
    return {
      isUpload: this.isUpload,
    };
  },
  created() {
    this.fetchList();
  },
  methods: {
    fetchList() {
      getList()
        .then((response) => {
          this.list = response.data;
        })
        .catch((error) => {
          console.error("请求列表数据失败:", error);
        });
    },
    startEditing(item) {
      this.list = this.list.map((i) => {
        if (i.id === item.id) {
          const span = this.$refs["span-" + item.id]; // 获取对应的span元素
          if (span) {
            const width = span[0].offsetWidth; // 计算span元素的宽度
            return {
              ...i,
              isEditing: true,
              editingName: i.name,
              editWidth: width,
            };
          }
        }
        return { ...i, isEditing: false };
      });
    },
    confirmEdit(item) {
      // 处理确认编辑逻辑
      item.name = item.editingName;
      item.isEditing = false;
      // 发送更新请求...
    },
    cancelEdit(item) {
      item.isEditing = false;
    },
    deleteItem(item) {
      // 处理删除逻辑
      // 发送删除请求...
    },
    addTab() {
      let newTabIndex = ++this.tabIndex + "";
      this.editableTabs.push({
        title: "活动" + ++this.addIndex,
        name: newTabIndex,
      });
      this.editableTabsValue = newTabIndex;
      this.currentIndex = newTabIndex;
    },
    removeTab(targetName) {
      if (this.editableTabs.length <= 1) {
        return false;
      }
      var self = this;
      let tabs = self.editableTabs;
      let activeName = self.editableTabsValue;
      if (activeName === targetName) {
        tabs.forEach((tab, index) => {
          if (tab.name === targetName) {
            let nextTab = tabs[index + 1] || tabs[index - 1];
            if (nextTab) {
              activeName = nextTab.name;
            }
          }
        });
      }
      self.editableTabsValue = activeName;

      self.editableTabs = tabs.filter((tab) => tab.name !== targetName);

      self.editableTabs.map((tab, index) => {
        tab.title = "活动" + (index + 1);
        self.addIndex = index + 1;
      });
      self.currentIndex = self.editableTabsValue;
      self.$message({
        type: "success",
        message: "删除成功!",
      });
    },
    /* 活动标签切换时触发 */
    beforeLeave(currentName, oldName) {
      var self = this;
      //重点，如果name是add，则什么都不触发
      if (currentName == "add") {
        this.addTab();
        return false;
      } else {
        this.currentIndex = currentName;
      }
    },
  },
};
</script>
<style scoped>
.app-container home {
  min-height: 100vh;
}
</style>

<style>
.tab-input .el-input__inner {
  padding-left: 5px;
  padding-right: 5px;
  box-sizing: border-box;
}

.tab-plus-label {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 5px;
  border: 1px solid transparent;
  border-radius: 50%;
  transition: border-color 0.3s ease;
}

.tab-plus-label:hover {
  border-color: #409eff;
  cursor: pointer;
}
</style>
