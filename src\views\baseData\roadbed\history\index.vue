<template>
  <PageContainer>
    <template slot="search">
      <div style="display: flex; align-items: center; margin: 0">
        <CascadeSelection
          :form-data="queryParams"
          v-model="queryParams"
          types="201"
          multiple
        />
        <rangeInput
          :startPlaceholder="'起始桩号111'"
          :endPlaceholder="'终点桩号'"
          @startValue="(v) => {queryParams.unifiedMileageStartStake = v}"
          @endValue="(v) => {queryParams.unifiedMileageEndStake = v}"
        />
        <el-select
          style="margin-right:20px"
          v-model="queryParams.typeId"
          placeholder="资产名称"
          clearable
          @change="getDynamicSelect"
        >
          <el-option
            v-for="dict in assetSubclassList"
            :key="dict.id"
            :label="dict.typeName"
            :value="dict.id"
          />
        </el-select>
        <div style="min-width:220px;height:32px">
          <el-button
            type="primary"
            icon="el-icon-search"
            @click="handleQuery"
          >搜索</el-button>
          <el-button
            icon="el-icon-refresh"
            @click="resetQuery"
          >重置</el-button>
        </div>
      </div>
    </template>

    <template slot="header">



    </template>
    <template slot="body">
      <el-table
        v-loading="loading"
        ref="table"
        height="100%"
        style="width: 100%"
        border
        :data="tableData"
        :row-style="rowStyle"
        @selection-change="handleSelectionChange"
        @row-click="handleRowClick"
      >
        <el-table-column

          type="selection"
          width="50"
          align="center"
        />
        <el-table-column
          label="序号"
          type="index"
          width="50"
          align="center"
        >
          <template v-slot="scope">
            {{ (scope.$index + (queryParams.pageNum - 1) * queryParams.pageSize)+1 }}
          </template>
        </el-table-column>
        <el-table-column
          label="资产子类"
          align="center"
          prop="typeName"
          min-width="140"
          show-overflow-tooltip
        >

        </el-table-column>
        <el-table-column
          label="资产编码"
          align="center"
          prop="assetCode"
          min-width="140"
          show-overflow-tooltip
        />
        <el-table-column
          label="管理处"
          align="center"
          prop="managementMaintenanceName"
          min-width="140"
          show-overflow-tooltip
        />
        <el-table-column
          label="管养分处"
          align="center"
          prop="managementMaintenanceBranchName"
          min-width="140"
          show-overflow-tooltip
        />
        <el-table-column
          label="养护路段名称"
          align="center"
          prop="maintenanceSectionName"
          min-width="140"
          show-overflow-tooltip
        />
        <el-table-column
          label="路线编码"
          align="center"
          prop="routeCode"
          min-width="140"
          show-overflow-tooltip
        />
        <el-table-column
          label="运营状态"
          align="center"
          prop="operationStateName"
          min-width="130"
          show-overflow-tooltip
        >
          <template slot-scope="{ row }">
            <el-link
              :underline="false"
              :type="{ 1: 'info', 2: 'success', 3: 'danger', 4: 'primary' }[row.operationState]"

              @click="handleOperational($event,row)"
            >
              <DictTag
                :value="row.operationState"
                :options="dict.type.sys_operation_state"
              />
            </el-link>
          </template>
        </el-table-column>
        <el-table-column
          label="桩号范围"
          align="center"
          min-width="200"
          show-overflow-tooltip
        >
          <template slot-scope="scope">
            {{ formatPile(scope.row.startStake) }} ~ {{ formatPile(scope.row.endStake) }}
          </template>
        </el-table-column>
        <el-table-column
          label="方向"
          align="center"
          prop="direction"
          min-width="140"
          show-overflow-tooltip
        >
          <template slot-scope="scope">
            <dict-tag
              :options="dict.type.sys_route_direction"
              :value="scope.row.direction"
            />
          </template>
        </el-table-column>
        <el-table-column
          label="位置"
          align="center"
          prop="lane"
          min-width="140"
          show-overflow-tooltip
        >
        <template slot-scope="scope">
            <dict-tag
              :options="dict.type.lane"
              :value="scope.row.lane"
            />
          </template>
        </el-table-column>
        <el-table-column
          label="经度"
          align="center"
          min-width="140"
          show-overflow-tooltip
          prop="longitude"
        >

        </el-table-column>
        <el-table-column
          label="纬度"
          align="center"
          min-width="140"
          show-overflow-tooltip
          prop="latitude"
        >
        </el-table-column>
        <el-table-column
          label="施工里程桩号"
          align="center"
          prop="constructionStake"
          min-width="140"
          show-overflow-tooltip
        >
          <template slot-scope="scope">
            {{ formatPile(scope.row.constructionStake) }}
          </template>
        </el-table-column>
        <el-table-column
          label="统一里程桩号"
          align="center"
          prop="unifiedMileageStake"
          min-width="140"
          show-overflow-tooltip
        >
          <template slot-scope="scope">
            {{ formatPile(scope.row.unifiedMileageStake) }}
          </template>
        </el-table-column>
        <el-table-column
          v-for="item in tableHead"
          :key="item.id"
          :label="item.alias"
          align="center"
          :prop="item.columnName"
          min-width="140"
          show-overflow-tooltip
        />
        <el-table-column
          label="图片"
          align="center"
        >
          <template #default="{ row }">
            <el-link
              :underline="false"
              type="primary"
              :disabled="row.picPath ? false : true"
              @click.stop="previewImg(row)"
            >查看</el-link>
          </template>
        </el-table-column>
      </el-table>
      <pagination
        :total="total"
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
        :pageSizes="[10, 20, 30, 50, 100, 1000]"
        @pagination="getList"
      />
      <Dialog
      title="查看图片"
      width="500px"
      :show.sync="imgShow"
    >
      <ImagePreview
        :owner-id="imageUrl"
        width="100%"
        height="100%"
      />
    </Dialog>
    </template>



  </PageContainer>
</template>

<script>
import {
  getFacilityHisListPage,
  getFacility,
  getAssetSubclass,
  getDynamicData
} from '@/api/baseData/facility/baseInfo/index'
import CascadeSelection from '@/components/CascadeSelection/index.vue'
import rangeInput from '@/views/baseData/components/rangeInput/index.vue'
import Dialog from '@/components/Dialog/index.vue'
import { statusDialog } from '@/views/baseData/components/statusDialog/index.js'
import { statusListDialog } from '@/views/baseData/components/statusDialog/list.js'

export default {
  name: 'History',
  components: {

    CascadeSelection,
    rangeInput,

    Dialog
  },
  dicts: ['sys_route_type', 'sys_operation_state','sys_route_direction','lane'],
  data() {
    return {
      loading: true,

      forView: false,
      title: '',
      formData: {},
      assetSubclassList: [],
      tableHead: [],
      ids: [],
      total: 0,
      tableData: [],
      queryParams: {
        pageNum: 1,
        pageSize: 20,
        mainTypeId:3
      },
      imgShow: false,
      imageUrl: '',
      assetIds: []

    }
  },
  watch: {},
  created() {

    this.getList()
    this.getAssetSubclassList()
  },
  methods: {
    // 获取表格数据
    getList() {
      this.loading = true

      getFacilityHisListPage(this.queryParams)
        .then(response => {
          this.tableData = response.rows
          this.total = response.total
          this.loading = false
        })
        .catch(() => {
          this.loading = false
        })
    },
    //查询字长子类
    getAssetSubclassList() {
      getAssetSubclass({mainTypeId:3}).then(res => {
          this.assetSubclassList = res
      })
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.assetIds = selection.map(item => item.assetId)
    },
    // 表格点击勾选
    handleRowClick(row) {
      row.isSelected = !row.isSelected
      this.$refs.table.toggleRowSelection(row)
    },
    // 勾选高亮
    rowStyle({ row, rowIndex }) {
      if (this.ids.includes(row.id)) {
        return { 'background-color': '#b7daff', color: '#333' }
      } else {
        return { 'background-color': '#fff', color: '#333' }
      }
    },

    // 搜索按钮
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
      this.getDynamicList()
    },

    getDynamicList() {
     if(this.queryParams.typeId){
        getDynamicData({mainTypeId:this.queryParams.mainTypeId,typeId:this.queryParams.typeId}).then(res => {
        if(res.code==200){
            this.tableHead = res.data
        }
        })
     }

    },
    // 重置按钮
    resetQuery() {
      this.queryParams = {
        pageNum: 1,
        pageSize: 20
      }
      this.handleQuery()
    },

    // 查看图片
    previewImg(row) {
      this.imgShow = true
      this.imageUrl = row.picPath
    },

    getDynamicSelect(val) {
     let data=this.assetSubclassList.find(item=>item.id==this.queryParams.typeId)
     this.queryParams.mainTypeId=data.mainType
    },


    // 查看按钮
    handleView() {
      if (this.ids.length != 1) {
        this.$message.warning('请选择一条数据！')
        return
      } else {
        getFacility(this.ids[0]).then(res => {
          if (res.code === 200) {
            this.formData = res.data
            this.forView = true
            this.showAddEdit = true
            this.title = '查看附属设施数据'
          }
        })
      }
    },

    // 运营状态变更按钮
    changeStatus() {
      if (this.ids.length !== 1) {
        this.$message.warning('请选择一条数据！')
        return
      } else {
        // baseDataType 基础数据类型 ？附属设施
        statusDialog({ dataId: this.ids[0], baseDataType: 22 }).then(() => {
          this.getList()
        })
      }
    },
    // 表格操作-运营状态
    handleOperational(event, row) {
      event.stopPropagation()
      statusListDialog({ dataId: row.id, baseDataType: 22 })
    },

  }
}
</script>

<style lang="scss" scoped>
::v-deep .el-table .el-table__fixed-body-wrapper{
    top: 72px !important;
}
</style>
