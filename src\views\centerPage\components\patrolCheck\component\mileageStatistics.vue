<template>
  <div class="mileage-statistics" v-loading="loading" element-loading-background="rgba(0, 0, 0, 0.3)">
    <Echarts :option="option" v-if="option" height="100%" :key="'mileage' + type" />
  </div>
</template>

<script>
import * as echarts from "echarts";
import Echarts from '../../echarts/echarts.vue';
// api
import { getMileageData, getPatrolMileageCount } from '@/api/cockpit/inspection';

export default {
  components: {
    Echarts
  },
  props: {
    type: {
      type: Number,
      default: 1
    },
    year: {
      type: [Number, String],
      default: new Date().getFullYear()
    }
  },
  data() {
    return {
      option: null,
      loading: false,
      xAxisData: [],
      dSeriesData: [],
      mSeriesData: [],
    }
  },
  mounted() { },
  watch: {
    type: {
      async handler(val) {
        if (val) {
          this.loading = true;
          this.xAxisData = [];
          this.dSeriesData = [];
          this.mSeriesData = [];
          this.option = null;
          let disease = await this.getDisease();
          let mileage = await this.getPatrolMileage();
          this.loading = false;
          this.xAxisData = disease.map(item => item.domainName);
          // 去除xAxisData中每个值的后面3个字节
          this.xAxisData = this.xAxisData.map(item => {
            if (item && typeof item === 'string') {
              return item.slice(0, -3);
            }
            return item;
          });
          this.dSeriesData = disease.map(item => item.count);
          // 处理数据和管理处对应

          if (disease && disease.length) {
            mileage.forEach(item => {
              let findIndex = disease.findIndex(i => i.domainName === item.deptName)
              this.mSeriesData[findIndex] = item.roadbedMileage || 0;
            })
          } else {
            this.mSeriesData = mileage.map(item => item.roadbedMileage || 0);
          }
          this.initCharts();
        }
      },
      immediate: true,
      deep: true
    }
  },
  methods: {
    // 获取病害数据
    getDisease() {
      return new Promise((resolve, reject) => {
        getMileageData(this.type).then(res => {
          if (res.code === 200 && res.rows) {
            resolve(res.rows);
          } else {
            reject(new Error('获取数据失败'));
          }
        }).catch(err => {
          reject(err);
        })
      });
    },
    // 获取里程数据
    getPatrolMileage() {
      return new Promise((resolve, reject) => {
        getPatrolMileageCount(this.type).then(res => {
          if (res.code === 200 && res.data) {
            resolve(res.data);
          } else {
            reject(new Error('获取数据失败'));
          }
        }).catch(err => {
          reject(err);
        })
      })
    },
    initCharts() {
      this.option = {
        backgroundColor: 'rgba(255,255,255,0)',
        color: ['#2adecf'],
        textStyle: {
          color: 'rgb(222,222,222)',
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          }
        },
        legend: {
          data: ['巡查里程', '病害数'],
          textStyle: {
            color: '#FFFFFF'
          }
        },
        grid: {
          left: '2%',
          right: '1%',
          bottom: '3%',
          top: '16%',
          containLabel: true
        },
        xAxis: [{
          data: this.xAxisData || ["保山", "大理", "红河", "昆明东", "昆明西", "丽江", "临沧", "普洱", "曲靖", "文山"],
          axisLine: {
            show: true,
            lineStyle: {
              color: 'transparent'
            }
          },
          splitLine: {
            show: false
          },
          axisTick: {
            show: false
          },
          axisLabel: {
            interval: 0,
            rotate: 0
          },
        }],
        yAxis: [{
          type: 'value',
          name: '巡查里程（km）',
          nameTextStyle: {
            color: '#999999',
            padding: [0, 0, 0, !this.mSeriesData.length ? 80 : 0]
          },
          axisLine: {
            show: true,
            lineStyle: {
              color: 'transparent'
            }
          },
          axisTick: {
            show: false
          },
          splitLine: {
            show: false
          },
          splitArea: false,
          axisLabel: {
            formatter: '{value}',
            textStyle: {
              color: '#999999'
            }
          },
        },
        {
          type: 'value',
          name: '病害数（个）',
          nameTextStyle: {
            color: '#999999',
            padding: [0, !this.dSeriesData.length ? 100 : -20, 0, 0]
          },
          axisLabel: {
            formatter: '{value}',
            textStyle: {
              color: '#999999'
            }
          },
          axisLine: {
            show: false
          },
          axisTick: {
            show: false
          },
          splitLine: {
            show: true,
            lineStyle: {
              color: 'rgba(110,112,121,0.3)'
            },
          },
          splitArea: false,
        }
        ],
        series: [{
          name: '巡查里程',
          type: 'bar',
          barWidth: '13%',
          itemStyle: {
            normal: {
              barBorderRadius: [30, 30, 0, 0],
              color: new echarts.graphic.LinearGradient(
                0, 1, 0, 0, [{
                  offset: 0,
                  color: '#0057AC'
                }, {
                  offset: 1,
                  color: '#03F2FE'
                }]
              )
            }
          },
          data: this.mSeriesData || [],
        }, {
          name: '病害数',
          type: 'line',
          yAxisIndex: 1,
          symbolSize: 8,
          itemStyle: {
            normal: {
              color: '#1CD9FF',
              barBorderRadius: [30, 30, 0, 0],
              lineStyle: {
                color: '#1CD9FF'
              },
              areaStyle: {
                color: new echarts.graphic.LinearGradient(0, 1, 0, 0, [{
                  offset: 0,
                  color: 'rgba(28,217,255,0.1)'
                }, {
                  offset: 1,
                  color: 'rgba(28,217,255,0.6)'
                }]),
              }
            }
          },
          data: this.dSeriesData || [],
          markLine: {
            symbol: 'none',
            itemStyle: {
              normal: {
                lineStyle: {
                  type: 'dashed',
                  color: 'red'
                },
                label: {
                  show: true,
                  position: 'left'
                }
              }
            },
            large: true,
            effect: {
              show: false,
              loop: true,
              period: 0,
              scaleSize: 2,
              color: null,
              shadowColor: null,
              shadowBlur: null
            },
            data: [
              [{
                name: '',
                xAxis: '',
                yAxis: ''
              },
              {
                name: '',
                xAxis: '',
                yAxis: ''
              }
              ]
            ]
          }
        }]
      }
    }
  },
}
</script>

<style lang="scss" scoped>
.mileage-statistics {
  width: 100%;
  height: 100%;
}
</style>