<template>
  <div class="weather" ref="weatherRef" :style="{ maxHeight: oneMap ? '94vh' : '82vh' }">
    <TitleHeader title="长期预报">
      <el-checkbox v-model="checked"><span style="color: #fff;">逐小时</span></el-checkbox>
    </TitleHeader>
    <section class="yb-content">
      <div v-for="(item, index) in cqList" :key="'cq' + index" class="list" @click="onCqClick(index, 1)"
        :class="cqIndex == index ? 'cq-active' : 'cp-normal'">
        <span>{{ item.name }}</span>
      </div>
    </section>

    <TimeLine v-if="checked" :timeList="cqTimeList" v-model="cqTime" />

    <TitleHeader title="短期预报"></TitleHeader>
    <section class="yb-content">
      <template v-if="dqList.length">
        <div v-for="(item, index) in dqList" :key="'dq' + index" class="list" @click="onCqClick(index, 2)"
          :class="dqIndex == index ? 'cq-active' : 'cp-normal'">
          <span>{{ item.name }}</span>
        </div>
      </template>
      <div v-else class="no-thing">
        暂无数据
      </div>
    </section>

    <TimeLine :timeList="dqTimeList" v-model="dqTime" />

    <TitleHeader title="雨量情况"></TitleHeader>
    <section class="list">
      <template v-if="ylList.length">
        <div v-for="(item, index) in ylList" :key="'yl' + index" class="list-item"
          :class="item.checked ? 'list-item-act' : 'list-item-normal'" @click="onQcClick(item, index, 'yl')">
          <div class="item-title">
            <img :src="item.icon" alt="" />
            <span>{{ item.name }}</span>
          </div>
          <span class="column-divider"></span>
          <div class="dept-count">
            <span>{{ item.deptName }}</span>
            <div class="number">
              <div class="number-item">
                <span :style="{ color: item.textColor }">{{ item.managementOffice || 0 }}</span>
                <small>个</small>
              </div>
              <div class="number-item mx-2">
                <span :style="{ color: item.textColor }">{{ item.maintenanceSection || 0 }}</span>
                <small>段</small>
              </div>
              <div class="number-item">
                <span :style="{ color: item.textColor }">{{ item.sectionLength ? (item.sectionLength / 1000).toFixed(2) : 0 }}</span>
                <small>km</small>
              </div>
            </div>
          </div>
        </div>
      </template>
      <div v-else class="no-thing">
        暂无数据
      </div>
    </section>
    <TitleHeader title="暴雨预警情况"></TitleHeader>
    <!-- <div class="checkbox-group">
      <el-checkbox-group v-model="checkList">
        <el-checkbox label="雨量"></el-checkbox>
        <el-checkbox label="雷电"></el-checkbox>
        <el-checkbox label="冰雹"></el-checkbox>
      </el-checkbox-group>
    </div> -->
    <section class="list">
      <template v-if="byList.length">
        <div v-for="(item, index) in byList" :key="'by' + index" class="list-item"
          :class="item.checked ? 'list-item-act' : 'list-item-normal'" @click="onQcClick(item, index, 'by')">
          <div class="item-title">
            <img :src="item.icon" alt="" />
            <span :style="{ color: item.color }">{{ item.name }}</span>
          </div>
          <span class="column-divider"></span>
          <div class="dept-count">
            <span>{{ item.deptName }}</span>
            <div class="number">
              <div class="number-item">
                <span :style="{ color: item.color }">{{ item.maintenanceSection }}</span>
                <small>个</small>
              </div>
              <div class="number-item mx-2">
                <span :style="{ color: item.color }">{{ item.managementOffice }}</span>
                <small>段</small>
              </div>
              <div class="number-item">
                <span :style="{ color: item.color }">{{ item.sectionLength }}</span>
                <small>km</small>
              </div>
            </div>
          </div>
        </div>
      </template>
      <div v-else class="no-thing">
        暂无数据
      </div>
    </section>
    <div class="divider"></div>

    <div class="btn" @click="onCreate">生成气象快报</div>

    <el-dialog :visible.sync="showDialog" width="50%" append-to-body :before-close="close">
      <template slot="title">
        <el-button type="primary" @click="onDownLoad" v-loading="loading">下载</el-button>
      </template>
      <div v-loading="bodyLoading" class="dialog-content" element-loading-background="rgba(0, 0, 0, 0.5)">
        <OfficePreview v-show="fileInfo.url && !bodyLoading" :content-type="fileInfo.contentType"
          :dialog-url="fileInfo.url" />
      </div>
    </el-dialog>
  </div>
</template>

<script>
import TitleHeader from './titleHeader.vue';
import TimeLine from './timeLine.vue';
import OfficePreview from "@/components/OfficePreview";
import { addGifLayer, addWeatherLayer, removeLayer, removeOverlay } from '@/views/map/components/common/mapFun.js';
import imgGif from '@/assets/map/point.gif'
// api
import { getWeatherList, createReport, getRainfallStatistics, getWarningLevel, getWarningLevelRegion } from '@/api/oneMap/weather';
import { findFiles } from '@/api/file/index.js'

import { mapState, mapActions, mapMutations } from "vuex";
import { Loading } from 'element-ui';

export default {
  name: "Weather",
  components: {
    TitleHeader,
    TimeLine,
    OfficePreview
  },
  props: {
    initObj: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      checkList: ['雨量'],
      checked: false,
      cqIndex: 0,
      cqList: [
        {
          name: '24 小时',
          value: 24,
        },
        {
          name: '48 小时',
          value: 48,
        },
        {
          name: '72 小时',
          value: 72,
        }
      ],
      dqIndex: null,
      dqList: [
        {
          name: '2 小时',
          value: 2
        }
      ],
      ylList: [],
      byList: [],
      typeList2: [
        {
          name: '红色',
          deptName: '管理处',
          icon: require('@/assets/map/weather/warning-red.png'),
          color: '#FD1100',
          maintenanceSection: 0,
          managementOffice: 0,
          sectionLength: 0
        },
        {
          name: '橙色',
          deptName: '管理处',
          icon: require('@/assets/map/weather/warning-orange.png'),
          color: '#FD7200',
          maintenanceSection: 0,
          managementOffice: 0,
          sectionLength: 0
        },
        {
          name: '黄色',
          deptName: '管理处',
          icon: require('@/assets/map/weather/warning-yellow.png'),
          color: '#FDD300',
          maintenanceSection: 0,
          managementOffice: 0,
          sectionLength: 0
        },
        {
          name: '蓝色',
          deptName: '管理处',
          icon: require('@/assets/map/weather/warning-blue.png'),
          color: '#06B8FF',
          maintenanceSection: 0,
          managementOffice: 0,
          sectionLength: 0
        },
      ],
      showDialog: false,
      cqTime: '2025-06-27 17:24:00',
      dqTime: '',
      params: {
        preTime: '', // 预报时间
        type: null, // 降雨类型 - 0:无雨 1:小雨 2:中雨 3:大雨 4:暴雨 5:大暴雨 6:特大暴雨 11: 小雪 12: 中雪 13: 大雪 14: 暴雪"
        managementOfficeIds: [], // 管理单位ID 
        maintenanceSectionIds: [], // 养护路段ID
        dataType: 2, // 2 小时 ， 24 小时，48 小时， 72 小时
        summaryType: 0, // 取值范围: 0:获取24小时、48小时、72小时的总体雨量; 1:获取单个小时的雨量预告"
      },
      //type  0:无雨 1:小雨 2:中雨 3:大雨 4:暴雨 5:大暴雨 6:特大暴雨 11: 小雪 12: 中雪 13: 大雪 14: 暴雪
      typeList: [
        {
          name: '无雨',
          type: 0,
          color: 'rgba(0,0,0,0)',
          icon: require('@/assets/map/weather/rainstorm.png'),
        },
        {
          name: '小雨',
          type: 1,
          color: 'rgba(143,248,136, 0.6)',
          textColor: '#8ff888',
          icon: require('@/assets/map/weather/rain_is_light.png'),
          gifUrl: require('@/assets/map/weather/gif_xy.gif'),
        },
        {
          name: '中雨',
          type: 2,
          color: 'rgba(36,164,31,0.6)',
          textColor: '#24a41f',
          icon: require('@/assets/map/weather/moderate_rain.png'),

          gifUrl: require('@/assets/map/weather/gif_zy.gif'),
        },
        {
          name: '大雨',
          type: 3,
          color: 'rgba(86,173,251,0.6)',
          textColor: '#56adfb',
          icon: require('@/assets/map/weather/a_heavy_rain.png'),
          gifUrl: require('@/assets/map/weather/gif_dy.gif'),
        },
        {
          name: '暴雨',
          type: 4,
          color: 'rgba(43,0,247,0.6)',
          textColor: '#2b00f7',
          icon: require('@/assets/map/weather/rainstorm.png'),
          gifUrl: require('@/assets/map/weather/gif_by.gif'),
        },
        {
          name: '大暴雨',
          type: 5,
          color: 'rgba(254,6,1,0.6)',
          textColor: '#fd1100',
          icon: require('@/assets/map/weather/super_rainstorm.png'),
          gifUrl: require('@/assets/map/weather/gif_dby.gif'),
        },
        {
          name: '特大暴雨',
          type: 6,
          color: 'rgba(254,6,1,0.6)',
          icon: require('@/assets/map/weather/rainstorm.png'),
        },
        {
          name: '小雪',
          type: 11,
          color: '#000000',
          icon: require('@/assets/map/weather/rainstorm.png'),
        },
        {
          name: '中雪',
          type: 12,
          color: '#000000',
          icon: require('@/assets/map/weather/rainstorm.png'),
        },
        {
          name: '大雪',
          type: 13,
          color: '#000000',
          icon: require('@/assets/map/weather/rainstorm.png'),
        },
        {
          name: '暴雪',
          type: 14,
          color: '#000000',
          icon: require('@/assets/map/weather/rainstorm.png'),
        },
      ],
      fileInfo: {},
      fileBlob: null,
      loading: false,
      loadingInstance: null,
      bodyLoading: false,
      cqTimeList: [],
      dqTimeList: [],
      deptIds: [],
      oneMap: false,
    };
  },
  methods: {
    // 点击选中
    onCqClick(index, type) {
      let val = ''
      this.params.type = [];
      if (type === 1) {
        this.params.dataType = this.cqList[index].value
        val = this.cqTimeList[0]
        this.cqIndex = (this.cqIndex >= 0 && this.cqIndex === index) ? null : index;
        this.dqIndex = null;
        this.createCqTime();
      } else if (type === 2) {
        this.params.dataType = this.dqList[index].value
        val = this.dqTimeList[0]
        this.dqIndex = (this.dqIndex >= 0 && this.dqIndex === index) ? null : index;
        this.cqIndex = null;
      }
      let { year, month, day } = this.getYMD(val);
      let time = year + "-" + month + "-" + day + " " + val + ":00"
      this.params.preTime = time;
      this.__init();
    },
    onCreate() {
      // this.loadingInstance = Loading.service({ fullscreen: true, background: 'rgba(0, 0, 0, 0.5)' });
      this.showDialog = true;
      this.bodyLoading = true;
      createReport(this.params).then(res => {
        if (res.code === 200 && res.data) {
          this.getFileInfo(res.data)
        }
      })
    },
    close() {
      this.showDialog = false;
    },
    // 获取文件信息
    getFileInfo(id) {
      findFiles({ ownerId: id }).then(async res => {
        if (res.code === 200 && res.data && res.data.length) {
          this.fileInfo = res.data[0]
          let { url } = this.fileInfo || {};
          if (url) {
            this.fileBlob = await this.getFileBlob(url)
          } else {
            this.bodyLoading = false;
          }
        } else {
          this.$message.error('文件/数据不存在')
          this.bodyLoading = false;
        }
      }).catch(err => {
        this.$message.error('请求出错')
      }).finally(() => {
        // this.loadingInstance.close();
      })
    },
    // 获取文件 blob
    getFileBlob(url) {
      return new Promise((resolve, reject) => {
        this.bodyLoading = true;
        fetch(url)
          .then(response => response.blob())
          .then(blob => resolve(blob))
          .catch(error => reject(error)).finally(() => {
            setTimeout(() => {
              this.bodyLoading = false;
            })
          });
      });
    },
    // 下载文件
    onDownLoad() {
      this.loading = true;
      if (this.fileInfo && this.fileInfo.url) {
        const link = document.createElement('a');
        link.href = URL.createObjectURL(this.fileBlob) || this.fileInfo.url;
        link.download = this.fileInfo.originalFilename || this.fileInfo.fileName || this.fileInfo.name || '气象快报文件';
        document.body.appendChild(link)
        link.click();
        document.body.removeChild(link)
      } else {
        this.$message.error('文件下载地址不存在');
      }
      this.loading = false;
    },
    onSetPoints() {
      // if (this.cqTime || this.dqTime) {
      //   if (this.cqIndex >= 0) {
      //     this.params.preTime = this.cqTime
      //   } else {
      //     this.params.preTime = this.dqTime
      //   }
      // }
      getWeatherList(this.params).then(res => {
        if (res && res.code === 200) {
          let arr = res.data?.map(v => {
            v.color = this.typeList.find(item => item.type === v.type).color;
            v.params = this.params
            return v;
          })
          let gifArr = res.data?.map(v => {
            v.color = this.typeList.find(item => item.type === v.type).color;
            v.name = this.typeList.find(item => item.type === v.type).name;
            v.gifUrl = this.typeList.find(item => item.type === v.type).gifUrl || '';
            v.params = this.params
            return v;
          })
          addWeatherLayer(arr)
          this.$nextTick(() => {
            addGifLayer(gifArr)
          })
        }
      })
    },
    // 雨量情况 - 暴雨预警情况
    onQcClick(row, index, type) {
      if (type === 'yl') {
        row.checked = !row.checked;
        this.params.type = this.ylList.filter(v => v.checked).map(v => v.type) || [row.type];
      } else {
        this.$message.warning("暂无数据")
      }
      this.__init(1);
    },
    // 获取雨量情况统计数据
    getRainfall() {
      getRainfallStatistics(this.params).then(res => {
        if (res && res.code === 200) {
          this.ylList = res.data.map(v => {
            v.icon = this.typeList.find(item => item.type === v.type).icon || '';
            v.name = this.typeList.find(item => item.type === v.type).name || '';
            v.deptName = '管理处';
            v.checked = false;
            v.textColor = this.typeList.find(item => item.type === v.type).textColor || '';
            return v;
          })
        }
      })
    },
    // 获取预警情况
    getBYYJ() {
      getWarningLevel(this.params).then(res => {
        if (res && res.code === 200) {
          this.byList = res.data.map(v => {
            v.icon = this.typeList2.find(item => item.name === v.alarmLevelName).icon || '';
            v.color = this.typeList2.find(item => item.name === v.alarmLevelName).color || '';
            v.deptName = '管理处';
            v.checked = false;
            return v;
          })
        }
      })
      // getWarningLevelRegion({preTime: this.params.preTime}).then(res => {
      //   if (res && res.code === 200) {
      //   }
      // })
    },
    // 移除图层数据
    removeCLayer() {
      removeOverlay()
      this.$nextTick(() => {
        removeLayer(window.mapLayer, "weatherLayer");
        removeLayer(window.mapLayer, "gifLayer");
        removeLayer(window.mapLayer, "weatherLayer", "name");
        removeLayer(window.mapLayer, "gifLayer", "name");
        setTimeout(() => {
          removeLayer(window.mapLayer, "pbfLayer");
          removeLayer(window.mapLayer, "pbfLayer", "name");
        }, 2500)
      })
    },
    // 生成默认长期时间线数据
    createCqTime() {
      // 获取当前时间
      const now = new Date();
      const currentHour = now.getHours();
      const startDate = new Date(now.getFullYear(), now.getMonth(), now.getDate()); // 记录起始日期

      // 初始化时间线数据数组
      this.cqTimeList = [];

      let len = 24;
      // 如果选择的是 24 小时
      if (this.cqIndex == 0) {
        len = 24
      } else if (this.cqIndex === 1) {
        len = 48
      } else {
        len = 72
      }

      for (let i = 0; i < len; i++) {
        const newDate = new Date(now.getTime() + i * 60 * 60 * 1000); // 计算当前循环对应的日期
        const diffDays = Math.floor((newDate - startDate) / (1000 * 60 * 60 * 24)); // 计算与起始日期的天数差
        let hour = (currentHour + i) % 24;
        hour = hour.toString().padStart(2, '0');
        let str = hour + ":00";
        if (diffDays > 0) {
          str += '+'.repeat(diffDays); // 根据天数差添加相应数量的 +
        }
        this.cqTimeList.push(str);
      }
    },
    // 生成默认短期时间线数据
    createDqTime() {
      // 初始化时间线数据数组
      this.dqTimeList = [];

      // 获取当前时间
      const now = new Date();
      const currentMinute = now.getMinutes();
      // 计算距离当前时间最近的 6 分钟间隔起始点
      const startMinute = Math.floor(currentMinute / 6) * 6;
      const startTime = new Date(now);
      startTime.setMinutes(startMinute, 0, 0);

      // 计算两小时后的时间戳
      const endTime = startTime.getTime() + 2 * 60 * 60 * 1000;

      // 循环生成数据，间隔 6 分钟
      let currentTime = startTime;
      while (currentTime.getTime() <= endTime) {
        const hours = String(currentTime.getHours()).padStart(2, '0');
        const minutes = String(currentTime.getMinutes()).padStart(2, '0');
        const timeStr = `${hours}:${minutes}`;
        this.dqTimeList.push(timeStr);
        // 增加 6 分钟
        currentTime = new Date(currentTime.getTime() + 6 * 60 * 1000);
      }
    },
    __init(st = null) {
      this.removeCLayer();
      this.params.managementOfficeIds = this.deptIds;
      this.onSetPoints()
      if (!st) {
        this.getRainfall()
        this.getBYYJ()
      }
    },
    getYMD(val) {
      let d = new Date();
      let year = d.getFullYear();
      let month = d.getMonth() + 1;
      month = month < 10 ? `0${month}` : month;
      let day = d.getDate();
      day = day < 10 ? `0${day}` : day;
      if (val && val.includes(':')) {
        let arr = val.split(':') || [];
        if (arr && arr.length) {
          if (arr[0] == '00') {
            // day = day - 0 + 1
          }
        }
      }
      return {
        year,
        month,
        day
      }
    },
  },
  computed: {
    ...mapState({
      deptId: (state) => state.user.deptId,
    })
  },
  watch: {
    checked(val) {
      this.params.summaryType = val ? 1 : 0
      let v = this.cqTimeList[0];
      let { year, month, day } = this.getYMD(v);
      let time = year + "-" + month + "-" + day + " " + v + ":00"
      this.params.preTime = time;
      this.__init();
    },
    cqTime(val) {
      // 匹配 + 号数量，1个+代表需要加1天，两个加两天
      const plusMatch = val.match(/\+/g);
      const plusCount = plusMatch ? Math.min(plusMatch.length, 2) : 0;
      // 过滤掉 + 号
      const filteredVal = val.replace(/\+/g, '');
      let { year, month, day } = this.getYMD(filteredVal);
      let time = year + "-" + month + "-" + (day + plusCount) + " " + filteredVal + ":00"
      this.params.preTime = time;
      this.params.type = []
      this.__init();
    },
    dqTime(val) {
      let { year, month, day } = this.getYMD(val);
      let time = year + "-" + month + "-" + day + " " + val + ":00"
      this.params.preTime = time;
      this.params.type = []
      this.__init();
    },
    deptId(val) {
      this.deptIds = val == 1 ? [] : [val]
    }
  },
  mounted() {
    this.createCqTime();
    this.createDqTime();
    // 页面滚动到最上方
    this.$refs.weatherRef.scrollTo(0, 0);
    // 默认
    this.params.dataType = 24;
    let v = this.cqTimeList[0];
    let { year, month, day } = this.getYMD(v);
    let time = year + "-" + month + "-" + day + " " + v + ":00"
    this.params.preTime = time;

    window.$Bus.$on('treeClick:weather', (data) => {
      let { maintenanceSectionIds, deptIds, isTop } = data;
      if (isTop) return;
      this.params.maintenanceSectionIds = maintenanceSectionIds && maintenanceSectionIds.length ? maintenanceSectionIds : [];
      this.deptIds = deptIds && deptIds.length ? deptIds : []
      this.__init();
    })
    if (this.$route.name == "oneMap") {
      this.oneMap = true;
    }
    if (this.initObj) {
      this.deptIds = this.initObj?.managementMaintenanceIds || []
      this.params.maintenanceSectionIds = this.initObj?.maintenanceSectionIds || []
    }
    this.__init();
  },
  beforeDestroy() {
    removeLayer(window.mapLayer, "weatherLayer");
    window.$Bus.$off('treeClick:weather');
  },
};
</script>

<style lang="scss" scoped>
.weather {
  width: 100%;
  height: auto;
  min-height: 60vh;
  padding: 1vh 0;
  overflow-y: auto;
  overflow-x: hidden;

  .yb-content {
    margin: 1.5vh 0;
    display: flex;
    align-items: center;

    .list {
      flex: 1;
      margin: 0 0.4vh;
      border-radius: 0.3vh;
      padding: 0.4vh 0;
      text-align: center;
      color: #fff;
      font-size: 1.5vh;
      cursor: pointer;
    }

    .cp-normal {
      background: rgba(0, 0, 0, 0.1);
      box-shadow: inset 0px 0px 10px 0px #0065FF;
      border-radius: 6px;
      border: 1px solid #20A9FF;
    }

    .cq-active {
      background: rgba(0, 0, 0, 0.1) rgba(0, 253, 253, 0.2);
      box-shadow: inset 0px 0px 10px 0px #00FDFD;
      border-radius: 6px;
      border: 1px solid #00FDFD;

      span {
        font-family: Source Han Sans, Source Han Sans;
        font-weight: 500;
        font-size: 1.5vh;
        color: #00FDFD;
        font-style: normal;
        text-transform: none;
      }
    }


  }

  .no-thing {
    width: 100%;
    height: 6vh;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #666666;
    font-size: 1.2vh;
  }

  .list {
    .list-item {
      width: 100%;
      height: 7vh;
      padding: 0.5vh 0;
      margin: 0.8vh 0;
      border-radius: 6px;
      cursor: pointer;

      display: flex;
      align-items: center;

      .item-title {
        width: 25%;
        display: flex;
        align-items: center;
        justify-content: center;
        flex-direction: column;
        flex-shrink: 0;

        img {
          width: 3vh;
          height: 3vh;
        }

        span {
          font-family: YouSheBiaoTiHei, YouSheBiaoTiHei;
          font-weight: 400;
          text-shadow: 0px 0px 20px #105ED9;
          text-align: center;
          font-style: normal;
          text-transform: none;
          font-size: 1.8vh;
          background: linear-gradient(180deg, #FFFFFF 0%, #FFFFFF 50%, #20A9FF 100%);
          background-clip: text;
          -webkit-background-clip: text;
          color: transparent;
          margin-top: 0.5vh;
        }
      }

      .column-divider {
        display: block;
        width: 0.1vh;
        height: 5vh;
        background-color: #0687FF;
      }

      .dept-count {
        margin-left: 1.2vh;

        span {
          font-family: Source Han Sans, Source Han Sans;
          font-weight: 500;
          text-shadow: 0px 0px 20px #105ED9;
          text-align: center;
          font-style: normal;
          text-transform: none;
          font-size: 1.5vh;
          background: linear-gradient(180deg, #FFFFFF 0%, #FFFFFF 50%, #20A9FF 100%);
          background-clip: text;
          -webkit-background-clip: text;
          color: transparent;
        }
      }

      .number {
        display: flex;
        align-items: center;
        line-height: 3.2vh;

        .number-item {
          flex-shrink: 0;

          span {
            font-family: Arial, Arial;
            font-weight: 700;
            font-size: 1.9vh;
            color: #F2AF4A;
            text-align: left;
            font-style: normal;
            text-transform: none;
          }

          small {
            color: #fff;
            font-size: 1.2vh;
          }
        }
      }
    }

    .list-item-normal {
      background: rgba(14, 25, 48, 0.5);
      box-shadow: inset 0px 0px 10px 0px #0687FF;
      border: 1px solid #0687FF;
    }

    .list-item-act {
      background: rgba(0, 0, 0, 0.1) rgba(0, 253, 253, 0.2);
      box-shadow: inset 0px 0px 10px 0px #00FDFD;
      border: 1px solid #00FDFD;
    }
  }

  .mx-2 {
    margin: 0 1vh;
  }


  .divider {
    border: 0.03vh solid rgba(6, 135, 255, 0.5);
  }

  .btn {
    width: 100%;
    height: 4vh;
    background: rgba(14, 25, 48, 0.4);
    box-shadow: inset 0px -4px 10px 0px #004DFF;
    border-radius: 4px;
    border: 1px solid #80C6FF;

    display: flex;
    align-items: center;
    justify-content: center;

    font-family: Source Han Sans, Source Han Sans;
    font-weight: 500;
    font-size: 1.4vh;
    color: #FFFFFF;
    text-align: center;
    font-style: normal;
    text-transform: none;
  }


}

.dialog-content {
  width: 100%;
  height: auto;
  min-height: 40vh;
  max-height: 80vh;
  overflow: hidden;
}

::v-deep .el-dialog {
  margin: 0 auto 0;
  background: rgba(4, 17, 48, 0.8);
  box-shadow: inset 0px 0px 10px 0px #3662ec;
  border-radius: 10px 10px 10px 10px;
  border: 1px solid #0687ff;
  color: #ffffff !important;

  .el-dialog__header {
    border-bottom: none;
    padding: 10px 15px !important;

    .el-dialog__title {
      color: #ffffff;
    }

    .el-dialog__headerbtn {
      color: #ffffff;
      top: 10px;
    }
  }

  .el-dialog__body {
    padding: 10px;
    color: #ffffff !important;
    max-height: unset;
  }
}

.checkbox-group {
  margin: 5px 20px 0 20px; 
  font-family: YouSheBiaoTiHei, YouSheBiaoTiHei;
  font-weight: 400;
  text-align: left;
  font-style: normal;
  text-transform: none;
  ::v-deep .el-checkbox__label {
    color: #fff
  }
}
</style>
