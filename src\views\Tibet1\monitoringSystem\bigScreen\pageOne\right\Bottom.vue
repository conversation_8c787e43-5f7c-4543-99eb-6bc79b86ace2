<template>
  <Cards :title="title" w="100%" h="28vh" :isDtl="false">
    <section class="warning-list" v-if="list.length">
      <vue-seamless-scroll :data="list" :class-option="optionHover" class="seamless-warp">
        <div class="list" v-for="(item, index) in list" :key="item.id">
          <img src="@/assets/monitoringSystem/n-warning.png" v-if="item.alertLevel === '三级'" />
          <img src="@/assets/monitoringSystem/warning.png" v-else />
          <span>{{ item.alertContent }}</span>
        </div>
      </vue-seamless-scroll>
    </section>
    <section class="no-thing" v-else>
      <img src="@/assets/monitoringSystem/n-warning.png" />
      <span>当前传感器无报警信息，持续监测中…</span>
    </section>
  </Cards>
</template>

<script>
import { isBigScreen } from '../../../utils/utils.js'
import ItemCard from '../../components/itemCard.vue'
import Echarts from '../../components/echarts.vue'
import Cards from "../../components/cards.vue"

// api
import { get } from '../../../utils/request.js'
import { fetchGet } from '../../../utils/api.js'

export default {
  name: 'Bottom',
  inject: ['iThis'],
  props: {
    title: {
      type: String,
      default: '报警信息'
    },
  },
  components: { ItemCard, Echarts, Cards },
  data() {
    return {
      isBig: isBigScreen(),
      list: []
    }
  },
  created() {
    this.__init()
  },
  methods: {
    // 获取数据
    __init() {
      this.list = [];
      // const url = 'https://jkjc.yciccloud.com:8000/xboot/alertRecord/getNotDealRecordInformation';
      // fetchGet(url, { structureNodeCode: this.iThis.params.code }).then((res) => {
      //   if (res.code === 200) {
      //     let result = res.result || {};
      //     for (let key in result) {
      //       this.list = [...this.list, ...result[key]]
      //     }
      //   }
      // })
      // 获取报警信息
      get('/bigScreen/structure/getNotDealRecordInformation', { params: { structureNodeCode: this.iThis.params.code } }).then(res => {
        if (res.code === 200 && res.data && res.data.result) {
          let result = res.data.result || {};
          for (let key in result) {
            this.list = [...this.list, ...result[key]]
          }
        }
      })
    },
  },
  computed: {
    // 滚动设置
    optionHover() {
      return {
        hoverStop: true,
        step: 0.2, // 数值越大速度滚动越快
        limitMoveNum: 2, // 开始无缝滚动的数据量 this.dataList.length
        direction: 1, // 0向下 1向上 2向左 3向右
        openWatch: true, // 开启数据实时监控刷新dom
        singleHeight: 0, // 单步运动停止的高度(默认值0是无缝不停止的滚动) direction => 0/1
        singleWidth: 0, // 单步运动停止的宽度(默认值0是无缝不停止的滚动) direction => 2/3
        waitTime: 1000, // 单步运动停止的时间(默认值1000ms)
      };
    },
  },
  watch: {},
}
</script>

<style lang="scss" scoped>
@import "@/assets/styles/utils.scss";

::-webkit-scrollbar {
  width: vwpx(14px);
  height: vwpx(14px);
}

::-webkit-scrollbar-thumb {
  background-color: rgba(1, 102, 254, 0.4);
}

::-webkit-scrollbar-track {
  background-color: rgba(0, 35, 94, .6);
}

.warning-list {
  height: 100%;
  padding: vwpx(20px);
  overflow-y: auto;

  .list {
    display: flex;
    align-items: center;
    margin-bottom: vwpx(24px);

    img {
      width: vwpx(90px);
      height: vwpx(90px);
      margin-right: vwpx(20px);
    }

    span {
      font-weight: 400;
      font-size: vwpx(28px);
      color: #FFFFFF;
      line-height: vwpx(40px);
    }
  }
}

.no-thing {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;

  span {
    font-weight: 400;
    font-size: vwpx(28px);
    color: #FFFFFF;
    line-height: vwpx(40px);
    margin-top: vwpx(20px);
  }
}

.seamless-warp {
  height: 100%;
  // overflow: hidden;
}
</style>