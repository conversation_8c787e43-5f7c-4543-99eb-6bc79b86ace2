<template>
  <el-row style="position: inherit;">
    <el-table
        :data="tableData"
        border
        height="200px"
        ref="tableRef"
        style="width: 100%">
      <el-table-column
          label="序号"
          align="center"
          type="index"
          width="50"
      />
      <el-table-column
          prop="schemeCode"
          align="center"
          label="子目号"
          width="150">
      </el-table-column>
      <el-table-column
        prop="schemeName"
        align="center"
        label="子目名称"
        width="150">
        <template slot-scope="scope">
          <el-input v-if="scope.row.rateFlag == 1" v-model="scope.row.schemeName" :disabled="readOnly">
          </el-input>
          <div v-else>{{scope.row.schemeName}}</div>
        </template>
      </el-table-column>
      <el-table-column
        prop="unit"
        align="center"
        label="单位"
        width="100">
        <template slot-scope="scope">
          <el-input v-if="scope.row.rateFlag == 1" v-model="scope.row.unit" :disabled="readOnly">
          </el-input>
          <div v-else>{{scope.row.unit}}</div>
        </template>
      </el-table-column>
      <el-table-column
        prop="price"
        align="center"
        label="单价"
        width="100">
        <template slot-scope="scope">
          <el-input v-if="scope.row.rateFlag == 1" v-model="scope.row.price" :disabled="readOnly">
          </el-input>
          <div v-else>{{scope.row.price}}</div>
        </template>
      </el-table-column>
      <el-table-column
          prop="calcDesc"
          align="center"
          label="计算式"
          width="250">
        <template slot-scope="scope">
          <el-input v-model="scope.row.calcDesc" @change="changeCalculation(scope.row)"
                    :disabled="editSafetyFee && (scope.row.schemeName == '安全生产费'||scope.row.schemeName == '安全保通费') || readOnly">
          </el-input>
        </template>
      </el-table-column>
      <el-table-column
          prop="num"
          align="center"
          label="方法数量"
          width="100">
        <template slot-scope="scope">
          <el-input v-model="scope.row.num" @change="changeSchemeNum(scope.row)"
                    :disabled="editSafetyFee && (scope.row.schemeName == '安全生产费'||scope.row.schemeName == '安全保通费') || readOnly">
          </el-input>
        </template>
      </el-table-column>
      <el-table-column
          prop="amount"
          align="center"
          label="资金"
          width="100">
      </el-table-column>
      <el-table-column
          prop="remark"
          align="center"
          label="备注"
          width="300">
        <template slot-scope="scope">
          <el-input v-model="scope.row.remark" :disabled="readOnly">
          </el-input>
        </template>
      </el-table-column>
      <el-table-column
          v-if="!readOnly"
          prop="field101"
          align="center"
          width="100"
          fixed="right"
          label="移除">
        <template slot-scope="scope">
          <el-button
              size="mini"
              type="text"
              :disabled="editSafetyFee && (scope.row.schemeName == '安全生产费'||scope.row.schemeName == '安全保通费')"
              @click="handleDelete(scope)"
          >移除
          </el-button
          >
        </template>
      </el-table-column>
    </el-table>
  </el-row>
</template>

<script>
import { Decimal } from 'decimal.js';

export default {
  components: {},
  props: {
    value: {
      type: Array,
      default: () => []
    },
    editSafetyFee: {
      type: Boolean,
      default: false
    },
    readOnly: {
      type: Boolean,
      default: false
    },
  },
  data() {
    return {
      tableData: this.value,
      total: 0
    }
  },
  watch: {
    value(val) {
      console.log('----------', val);

      this.tableData = val
    }
  },
  methods: {
    async handleDelete(e) {
      await this.tableData.splice(e.$index, 1)
      this.$nextTick(()=> {
        this.$emit('price-change')
        this.$emit('update:value', this.tableData);
      })
    },
    async changeCalculation(row) {
      let num =  eval(row.calcDesc) || 0
      this.$set(row, 'num', this.ceilToTwo(num, row.decimalPlaces || 2))
      await this.changeSchemeNum(row)
      this.$emit('update:value', this.tableData);
    },
    async changeSchemeNum(row) {
      let amount = new Decimal(row.num || 0).times(row.price || 0).toNumber()
      this.$set(row, 'amount', Math.round(amount))
      this.total = await this.tableData.reduce((acc, curr) => Number(acc) + Number(curr.amount), 0)
      if (row.schemeName != '安全生产费' && row.schemeName != '安全保通费') {
        this.$emit('price-change')
      }
      this.$emit('update:value', this.tableData);
    }
  }
}
</script>

<style scoped lang="scss">

</style>
