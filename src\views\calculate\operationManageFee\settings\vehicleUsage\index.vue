<template>
  <div class="app-container">
    <el-row :gutter="20">
      <!-- 筛选区开始 -->
      <el-col :span="24" :xs="24">
        <el-row>
          <el-col :span="24">
            <el-form ref="queryForm" :inline="true" :model="queryParams" label-width="68px" size="small">
              <el-form-item label="" prop="maintenanceRoad">
                <RoadSection ref="roadSection" v-model="queryParams.maiSecId" placeholder="路段" :readonly="maiSecId"
                             style="width: 100%"/>
              </el-form-item>
              <el-form-item label="" prop="siteName">
                <el-input v-model="queryParams.siteName" placeholder="请输入站点" />
              </el-form-item>
              <el-form-item label="" prop="carNum">
                <el-input v-model="queryParams.carNum" placeholder="请输入车牌号" />
              </el-form-item>
              <el-form-item>
                <el-button icon="el-icon-search" size="mini" type="primary" @click="handleQuery">搜索</el-button>
                <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
              </el-form-item>
            </el-form>
          </el-col>
        </el-row>
        <el-row :gutter="10" class="mb8" v-if="pageType == 'edit'">
          <el-col :span="1.5">
            <el-button
                icon="el-icon-plus"
                size="mini"
                type="primary"
                v-has-menu-permi="['operate:caruse:add']"
                @click="handleAdd"
            >新增
            </el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button
                icon="el-icon-download"
                size="mini"
                type="success"
                v-has-menu-permi="['operate:caruse:export']"
                @click="exportList"
            >导出
            </el-button>
          </el-col>
        </el-row>
        <!-- 筛选区结束 -->
        <!-- 数据表格开始 -->
        <div class="tableDiv">
          <el-table v-adjust-table v-loading="loading" :data="dataList"
                    :height="pageType == 'edit' ? 'calc(100vh - 260px)' : 'calc(70vh - 260px)'" border size="mini"
                    style="width: 100%" @selection-change="handleSelectionChange">
            <el-table-column
              type="selection"
              width="50">
            </el-table-column>
            <el-table-column align="center" fixed label="序号" type="index" width="100"></el-table-column>
            <template v-for="(column, index) in columns">
              <el-table-column v-if="column.visible" :key="index" :label="column.label" :prop="column.field" align="center" show-overflow-tooltip>
                <template slot-scope="scope">
                  <span>{{ scope.row[column.field] }}</span>
                </template>
              </el-table-column>
            </template>
            <el-table-column align="center" class-name="small-padding fixed-width" fixed="right" label="操作" v-if="pageType == 'edit'" width="300">
              <template slot-scope="scope">
                <el-button size="mini" v-has-menu-permi="['operate:caruse:edit']" type="text" icon="el-icon-edit" @click="handleEdit(scope.row)">编辑</el-button>
                <el-button size="mini" v-has-menu-permi="['operate:caruse:remove']" type="text" icon="el-icon-delete" @click="handleDelete(scope.row)">删除</el-button>
              </template>
            </el-table-column>
          </el-table>
          <pagination
              v-show="total > 0"
              :limit.sync="queryParams.pageSize"
              :page.sync="queryParams.pageNum"
              :total="total"
              @pagination="handleQuery"
          />
        </div>
        <!-- 数据表格结束 -->
      </el-col>
    </el-row>
    <!-- 新增/编辑对话框 -->
    <el-dialog :title="dialogTitle" :visible.sync="dialogVisible" append-to-body destroy-on-close width="90%">
      <el-form ref="form" :model="formData" :rules="rules" label-width="100px" size="medium">
        <el-form-item label="费用类型" prop="feeType">
          <el-input readonly value="车辆使用费"></el-input>
        </el-form-item>
        <el-form-item label="管养单位" prop="domainId">
          <select-tree
            v-model="formData.domainId"
            :deptType="100"
            :deptTypeList="[1, 3, 4]"
            clearable
            onlySelectChild
            placeholder="请选择管养单位"
          ></select-tree>
        </el-form-item>
        <el-form-item label="养护路段" prop="maiSecId">
          <RoadSection ref="roadSection" v-model="formData.maiSecId" :deptId="formData.domainId" placeholder="路段"
                       style="width: 100%"/>
        </el-form-item>
        <el-form-item label="站点" prop="siteName">
          <el-input v-model="formData.siteName" placeholder="请输入站点" />
        </el-form-item>
        <el-form-item label="车牌号" prop="carNum">
          <el-input v-model="formData.carNum" placeholder="请输入车牌号" />
        </el-form-item>
        <el-form-item label="车辆类型" prop="carType">
          <el-input v-model="formData.carType" placeholder="请输入车辆类型" />
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input :rows="3" type="textarea" v-model="formData.remark" />
        </el-form-item>
      </el-form>
      <div style="text-align: right">
        <el-button @click="closeDialog">取消</el-button>
        <el-button type="primary" @click="handleSubmit">确定</el-button>
      </div>
    </el-dialog>
    <div class="mt20" style="text-align: right">
      <el-button v-if="pageType == 'view'" type="primary" @click="handleCheckData">保存</el-button>
    </div>
  </div>
</template>

<script>
import { listCarusePage, addCaruse, editCaruse, deleteCaruse } from '@/api/calculate/operationManageFee/vehicleUsage';
import RoadSection from "@/views/baseData/components/roadSection/index.vue";
import SelectTree from "@/components/DeptTmpl/selectTree.vue";

export default {
  name: "YourComponentName",
  components: {SelectTree, RoadSection},
  props: {
    pageType: {
      type: String,
      default: 'edit'
    },
    maiSecId: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      // 遮罩层
      loading: false,
      // 显示搜索条件
      showSearch: false,
      // 总条数
      total: 0,
      dataList: [],
      selectDatas: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        maintenanceRoad: '',
        site: '',
        licensePlate: ''
      },
      // 表格列配置
      columns: [
        { label: '管养单位', field: 'domainName', visible: true },
        { label: '养护路段', field: 'maiSecName', visible: true },
        { label: '站点', field: 'siteName', visible: true },
        { label: '车牌号', field: 'carNum', visible: true },
        { label: '车辆类型', field: 'carType', visible: true }
      ],
      // 对话框标题
      dialogTitle: '',
      // 对话框是否显示
      dialogVisible: false,
      // 表单数据
      formData: {},
      // 表单验证规则
      rules: {
        domainId: [{ required: true, message: '请输入管养单位', trigger: 'blur' }],
        maiSecId: [{ required: true, message: '请输入养护路段', trigger: 'blur' }],
        siteName: [{ required: true, message: '请输入站点', trigger: 'blur' }],
        carNum: [{ required: true, message: '请输入车牌号', trigger: 'blur' }],
        carType: [{ required: true, message: '请输入车辆类型', trigger: 'blur' }]
      }
    };
  },
  created() {
    this.handleQuery();
  },
  methods: {
    handleQuery() {
      this.loading = true;
      if (this.maiSecId) this.queryParams.maiSecId = this.maiSecId
      listCarusePage(this.queryParams).then(response => {
        this.dataList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    handleAdd() {
      this.dialogTitle = '新增';
      this.formData = {};
      this.dialogVisible = true;
    },
    handleEdit(row) {
      this.dialogTitle = '编辑';
      this.formData = { ...row };
      this.dialogVisible = true;
    },
    closeDialog() {
      this.dialogVisible = false;
      this.resetForm("form");
    },
    handleSubmit() {
      this.$refs.form.validate(valid => {
        if (valid) {
          if (!this.formData.id) {
            addCaruse(this.formData).then(response => {
              this.$message.success('新增成功');
              this.closeDialog();
              this.handleQuery();
            });
          } else {
            editCaruse(this.formData).then(response => {
              this.$message.success('编辑成功');
              this.closeDialog();
              this.handleQuery();
            });
          }
        }
      });
    },
    handleDelete(row) {
      this.$confirm('是否确认删除？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        deleteCaruse(row.id).then(response => {
          this.$message.success('删除成功');
          this.handleQuery();
        });
      })
    },
    // 选中
    handleSelectionChange(e) {
      this.selectDatas = e
    },
    handleCheckData() {
      this.$emit('check', this.selectDatas)
    },
    // 导出清单按钮
    exportList() {
      this.download(
        "manager/operate/caruse/export",
        {...this.queryParams},
        `caruse_${new Date().getTime()}.xlsx`,
        {
          headers: {"Content-Type": "application/json;"},
          parameterType: "body",
        }
      );
    },
  }
};
</script>

<style scoped>
.app-container {
  padding: 20px;
}
.tableDiv {
  margin-top: 20px;
}
</style>
<style lang="scss" scoped>
@import "@/assets/styles/business.scss";
</style>
