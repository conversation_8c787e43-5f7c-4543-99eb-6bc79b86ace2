<template>
  <div v-loading="loading" :class="oneMap ? 'one-map' : 'culvert-one'">
    <el-form
      ref="form"
      :model="form"
      :rules="rules"
      label-width="150px"
      :disabled="true"
     >
      <div style="display: flex; flex-wrap: wrap; margin-bottom: 20px">
        <el-divider content-position="left">基础数据</el-divider>
        <ManageSelectTree placeholder="请选择" :formObject="form" />
        <el-col :span="12">
          <el-form-item label="养护路段" prop="maintenanceSectionId">
            <el-select
              v-model="form.maintenanceSectionId"
              style="width: 100%"
              placeholder="请选择"
              clearable
              :disabled="!form.managementMaintenanceBranchId"
            >
              <el-option
                v-for="item in routeOptions"
                :key="item.maintenanceSectionId"
                :label="item.maintenanceSectionName"
                :value="item.maintenanceSectionId"
              />
            </el-select>
          </el-form-item>
        </el-col>

        <el-col :span="12">
          <el-form-item label="路线编码" prop="routeCode">
            <el-select
              v-model="form.routeCode"
              placeholder="请选择"
              :disabled="!form.maintenanceSectionId"
              clearable
              style="width: 100%"
              @change="
                (val) => {
                  handleSelect(val);
                }
              "
            >
              <el-option
                v-for="item in routeList"
                :key="item.routeCode"
                :label="item.routeCode"
                :value="item.routeCode"
              >
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="路线名称" prop="routeName">
            <el-select
              v-model="form.routeName"
              style="width: 100%"
              placeholder="请选择"
              :disabled="true"
              clearable
            >
              <el-option
                v-for="item in []"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="调查方向" prop="direction">
            <el-select
              v-model="form.direction"
              placeholder="请选择"
              clearable
              style="width: 100%"
            >
              <el-option
                v-for="item in dict.type.sys_route_direction || []"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="具体方向" prop="specificDirection">
            <el-input v-model="form.specificDirection" style="width: 100%" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="路线技术等级" prop="routeLevel">
            <MultiDictSelect
              v-model="form.routeLevel"
              :disabled="''"
              :multiple="true"
              :options="dict.type['sys_route_grade']"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="路面类型" prop="pavementType">
            <el-select
              v-model="form.pavementType"
              placeholder="请选择"
              clearable
              style="width: 100%"
            >
              <el-option
                v-for="item in dict.type.sys_surface_type || []"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="年度" prop="years">
            <el-date-picker
              v-model="form.years"
              style="width: 100%"
              :type="'year'"
              :placeholder="'请选择年份'"
              :picker-options="pickerOptions"
              clearable
              :value-format="'yyyy'"
            />
          </el-form-item>
        </el-col>

        <el-divider content-position="left">统一里程</el-divider>
        <el-col :span="12">
          <el-form-item label="起点桩号" prop="unifiedMileageStartStake">
            <PileInput v-model="form.unifiedMileageStartStake" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="终点桩号" prop="unifiedMileageEndStake">
            <PileInput v-model="form.unifiedMileageEndStake" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="管养里程(km)" prop="maintenanceMileage">
            <el-input-number
              v-model="form.maintenanceMileage"
              :min="0"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
        <el-divider content-position="left">施工里程</el-divider>
        <el-col :span="12">
          <el-form-item label="起点桩号" prop="constructionStartStake">
            <PileInput v-model="form.constructionStartStake" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="终点桩号" prop="constructionEndStake">
            <PileInput v-model="form.constructionEndStake" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="施工里程(km)" prop="constructionMileage">
            <el-input-number
              v-model="form.constructionMileage"
              :min="0"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
        <el-divider content-position="left">其他</el-divider>
        <el-col :span="12">
          <el-form-item label="移交管理单位" prop="transferManagementUnit">
            <el-input
              v-model="form.transferManagementUnit"
              style="width: 100%"
              controls-position="right"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="备注" prop="remark">
            <el-input
              type="textarea"
              v-model="form.remark"
              placeholder="请输入备注"
              clearable
              autosize
            />
          </el-form-item>
        </el-col>
      </div>
    </el-form>
  </div>
</template>
  
  <script>
import { getIceCovered } from "@/api/baseData/road/sharpCurve/index.js";
import PileInput from "@/components/PileInput/index.vue";
import SelectTree from "@/components/DeptTmpl/selectTree";
import SectionSelect from "@/components/SectionSelect";
import ManageSelectTree from "@/components/manageSelectTree/index.vue";
import { listMaintenanceSectionAll } from "@/api/system/maintenanceSection";
import { listByMaintenanceSectionId } from "@/api/baseData/common/routeLine";
import MultiDictSelect from "@/views/baseData/components/MultiDictSelect/index.vue";

export default {
  name: "sharpCurve-form",
  components: {
    PileInput,
    SelectTree,
    SectionSelect,
    ManageSelectTree,
    MultiDictSelect,
  },
  inject: ["oneMap"],
  props: {
    formData: {
      default: {},
    },
    id: {
      type: [String, Number],
      default: "",
    },
  },
  dicts: ["sys_route_direction", "sys_route_grade", "sys_surface_type"],
  data() {
    return {
      loading: false,
      form: {
        managementMaintenanceId: "",
        managementMaintenanceBranchId: "",
      },
      pickerOptions: {
        disabledDate(v) {
          return v.getTime() > new Date().getTime();
        },
      },
      rules: {
        managementMaintenanceId: [
          { required: true, message: "请选择管理处", trigger: "change" },
        ],
        managementMaintenanceBranchId: [
          { required: true, message: "请选择管养分处", trigger: "change" },
        ],
        maintenanceSectionId: [
          { required: true, message: "请选择养护路段", trigger: "change" },
        ],
        routeCode: [
          { required: true, message: "请选择路线编码", trigger: "change" },
        ],
        routeName: [
          { required: true, message: "请输入路线名称", trigger: "blur" },
        ],
        years: [{ required: true, message: "请选择年度", trigger: "change" }],
        direction: [
          { required: true, message: "请选择调查方向", trigger: "change" },
        ],
        specificDirection: [
          { required: true, message: "请输入具体方向", trigger: "blur" },
        ],
        routeLevel: [
          { required: true, message: "请选择路线技术等级", trigger: "change" },
        ],
        pavementType: [
          { required: true, message: "请选择路面类型", trigger: "change" },
        ],
        unifiedMileageStartStake: [
          { required: true, message: "请输入起点桩号", trigger: "blur" },
        ],
        unifiedMileageEndStake: [
          { required: true, message: "请输入终点桩号", trigger: "blur" },
        ],
        maintenanceMileage: [
          { required: true, message: "请输入管养里程", trigger: "blur" },
        ],
        constructionStartStake: [
          { required: true, message: "请输入起点桩号", trigger: "blur" },
        ],
        constructionEndStake: [
          { required: true, message: "请输入终点桩号", trigger: "blur" },
        ],
        constructionMileage: [
          { required: true, message: "请输入施工里程", trigger: "blur" },
        ],
      },
      routeOptions: [],
      routeList: [],
    };
  },
  mounted() {
    this.getIceCovered();
  },
  methods: {
    init() {
      if (this.formData.id) {
        this.form = JSON.parse(JSON.stringify(this.formData));
      }
    },
    getIceCovered() {
      getIceCovered(this.id).then((res) => {
        if (res.code === 200) {
          this.form = res.data||[]
        }
      });
    },
    deptChange(e) {
      if (!e) return;
      listMaintenanceSectionAll({ departmentId: e }).then((res) => {
        if (res.code == 200) {
          this.routeOptions = res.data;
        }
      });
    },
    maintenanceSectionChange(e) {
      if (!e) return;

      const option = this.routeOptions.find(
        (i) => i.maintenanceSectionId === e
      );
      if (option && option.routeGrade) {
        this.form["routeLevel"] = option.routeGrade.split(",");
      }
      listByMaintenanceSectionId({ maintenanceSectionId: e }).then((res) => {
        if (res.code == 200) {
          this.routeList = res.data || [];
          // this.form.routeCode = ''
          this.$forceUpdate();
        }
      });
    },
    handleSelect(e) {
      const option = this.routeList.find((i) => i.routeCode === e);
      if (option) {
        this.form.routeId = option.routeId;
        this.form.routeName = option.routeName;
      }
    },
  },
};
</script>
  
<style lang="scss" scoped>
@import "@/assets/styles/common.scss";

.el-divider {
  background-color: #0166fe;
}

.el-divider__text {
  background: rgba(4, 17, 48, 0.8);
  background-image: initial;
  background-position-x: initial;
  background-position-y: initial;
  background-size: initial;
  background-repeat: initial;
  background-attachment: initial;
  background-origin: initial;
  background-clip: initial;
  color: #fff;
  border: 1.5px solid #0166fe;
  padding: 10px 20px;
}

::v-deep .el-textarea.is-disabled .el-textarea__inner {
  background-color: rgba(1, 102, 254, 0.2);
  border-color: #0166fe;
}
</style>
  