<template>
  <div class="structure-list">
    <Tables :columns="clumns" :data="tableData" v-on="$listeners" />
  </div>
</template>

<script>
import Tables from '@/views/Tibet/components/tables.vue';
import { getAllStructureInfoGroupByDomain } from '@/api/cockpit/health';

export default {
  name: 'structureList',
  components: {
    Tables
  },
  props: {
    structure: {
      type: Array,
      default: () => ([])
    },
    structureType: {
      type: String,
      default: ''
    },
    searchAll: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      clumns: [
        {
          label: '名称',
          prop: 'name',
        },
        {
          label: '类型',
          prop: 'structureType',
          width: 50
        },
        {
          label: '预案状态',
          prop: 'planStatusName',
          width: 50
        },
      ],
      tableData: [],
      structureList: [], // 存放所有结构数据
    }
  },
  watch: {
    structure: {
      handler(newVal, oldVal) {
        if (this.searchAll) {
          this.getList();
        } else {
          this.getList(this.name);
        }
      },
      deep: true,
      immediate: true
    },
  },
  methods: {
    getList(name) {
      if (name) {
        this.name = name;
        let arr = []
        this.structure.forEach(item => {
          item.planStatusName = item.planStatus == '1' ? '开启中' : '未开启'
          if (item.domainName === name && item.structureType === this.structureType) {
            arr.push(item)
          }
        });
        this.tableData = arr;
      } else {
        let arr = []
        this.structure.forEach(item => {
          item.planStatusName = item.planStatus == '1' ? '开启中' : '未开启'
          if (item.structureType === this.structureType) {
            arr.push(item)
          }
        });
        this.tableData = arr;
      }
    },
  },
}
</script>

<style lang="scss" scoped>
@import "@/assets/styles/utils.scss";

.structure-list {
  width: 100%;
  height: 100%;
  padding: vwpx(10px);
}
</style>