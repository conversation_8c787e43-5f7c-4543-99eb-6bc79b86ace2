<template>
  <div class="road-interflow-edit" style="padding: 20px" v-loading="loading">
    <el-row :gutter="20">
      <el-form
          ref="elForm"
          :model="formData"
          :rules="rules"
          :disabled="readonly"
          size="medium"
          label-width="130px"
      >
        <el-col :span="24">
          <div class="card_title">1.基本信息</div>
        </el-col>
        <el-row :gutter="20" style="flex-wrap: wrap; display: flex;">
          <el-col :span="8">
            <el-form-item label="管养单位" prop="domainId">
              <selectTree
                  :key="'domainId'"
                  v-model="formData.domainId"
                  :deptType="100"
                  :deptTypeList="[1, 3, 4]" clearable
                  filterable
                  only-select-child
                  placeholder="管养单位"
                  style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="年度" prop="year">
              <el-date-picker
                  v-model="formData.year"
                  placeholder="年度"
                  style="width: 100%"
                  type="year"
                  value-format="yyyy"
              >
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="路段名称" prop="maiSecId">
              <RoadSection ref="roadSection" v-model="formData.maiSecId" :deptId="formData.domainId" placeholder="路段"
                           style="width: 100%" @change="changeMaiSecId"/>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="选择预算" prop="budgetName">
              <el-input v-model="formData.budgetName" style="width: 100%" placeholder="选择预算" @focus="openBudget"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="专项名称" prop="name">
              <el-input style="width: 100%"  v-model="formData.name" placeholder="专项名称"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="项目编码" prop="code">
              <el-input style="width: 100%"  v-model="formData.code" placeholder="项目编码"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="缺陷责任期(月)" prop="defLiaPer">
              <el-input style="width: 100%" type="number" v-model="formData.defLiaPer" placeholder="缺陷责任期(月)"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="预计时间" prop="dates">
              <el-date-picker
                style="width: 100%"
                v-model="formData.dates"
                @change="changeDates"
                type="daterange"
                value-format="yyyy-MM-dd"
                range-separator="至"
                start-placeholder="预计开始时间"
                end-placeholder="预计结束时间">
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="预计工期(天)" prop="expectDuration">
              <el-input style="width: 100%" type="number" v-model="formData.expectDuration" placeholder="预计工期(天)"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="专项资金(元)" prop="sumFund">
              <el-input style="width: 100%" type="number" v-model="formData.sumFund" placeholder="专项资金(元)"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="工程分类" prop="projectType">
              <dict-select
                v-model="formData.projectType"
                type="project_type"
                placeholder="工程分类"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="所属工程类别" prop="mtype">
              <cost-select :type="25"
                v-model="formData.mtype"
                placeholder="所属工程类别"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="合同" prop="conId">
              <contract-section v-model="formData.conId" valueType="object" :params="contractFilterMap" placeholder="请选择合同" @change="addContract"></contract-section>
            </el-form-item>
          </el-col>
        </el-row>
        <el-col :span="24">
          <el-form-item label="已选合同" prop="conIds">
            <el-input type="textarea" rows="4" v-model="formData.conNames" :style="{width: '100%'}" placeholder="已选合同" readonly></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="立项理由" prop="reason">
            <el-input type="textarea" rows="4" v-model="formData.reason" :style="{width: '100%'}" placeholder="立项理由"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="主要工程内容" prop="enContent">
            <el-input type="textarea" rows="4" v-model="formData.enContent" :style="{width: '100%'}" placeholder="主要工程内容"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="主要工程数量" prop="enNum">
            <el-input type="textarea" rows="4" v-model="formData.enNum" :style="{width: '100%'}" placeholder="主要工程数量"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="备注" prop="remark">
            <el-input type="textarea" rows="4" v-model="formData.remark" :style="{width: '100%'}" placeholder="备注"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <div class="card_title">2.合同清单</div>
        </el-col>
        <el-col :span="24">
          <el-tabs v-model="editableTabsValue" :closable="!readonly" @tab-remove="removeTab">
            <el-tab-pane
                v-for="(item, index) in contractList"
                :key="item.conId"
                :label="item.conName"
                :name="item.conId"
            >
              <el-button v-if="!readonly" type="primary" @click="addScheme(item.conId, index)" style="margin-bottom:10px;float:right">新增</el-button>
              <methods-list :read-only="readonly" :value.sync="item.schemeList"></methods-list>
            </el-tab-pane>
          </el-tabs>
        </el-col>
        <el-col :span="24">
          <div class="card_title" style="margin-top: 15px">3.构造物</div>
        </el-col>
        <el-col :span="24">
          <el-button v-if="!readonly" type="primary" @click="addStructure" style="margin-bottom:10px;float:right">新增</el-button>
          <el-table v-adjust-table
          :data="gzList"
          border
          height="200px"
          style="width: 100%">
            <el-table-column
                prop="routeCode"
                align="center"
                label="路线编码">
            </el-table-column>
            <el-table-column
                prop="laneArray"
                align="center"
                v-if="assetType == '34'"
                label="车道">
                <template slot-scope="scope">
                  <dict-select type="lane" clearable multiple
                               v-model="scope.row.laneArray"
                               style="width: 100%"></dict-select>
                </template>
            </el-table-column>
            <el-table-column
              prop="name"
              align="center"
              v-else
              label="构造物名称">
              <template slot-scope="scope">
                {{scope.row.name}}
              </template>
            </el-table-column>
            <el-table-column
                prop="direction"
                align="center"
                label="上下行">
              <template slot-scope="scope">
                <dict-select v-if="scope.row.structureType == '1'" type="proj_bridge_direction" clearable
                             v-model="scope.row.direction"
                             style="width: 100%"></dict-select>
                <div v-else-if="scope.row.structureType == '2'"></div>
                <dict-select v-else type="route_direction" clearable
                             v-model="scope.row.direction"
                             style="width: 100%"></dict-select>
              </template>
            </el-table-column>
            <el-table-column
                prop="beginMile"
                align="center"
                label="起点桩号">
              <template slot-scope="scope">
                k
                <el-input v-model="scope.row.beginMile1" :style="{width: '40%'}" @change="changeMile(scope.row)">
                </el-input>
                <div style="width: 10%;text-align: center;display: inline-block;">+</div>
                <el-input v-model="scope.row.beginMile2" :style="{width: '40%'}" @change="changeMile(scope.row)">
                </el-input>
              </template>
            </el-table-column>
            <el-table-column
                prop="endMile"
                align="center"
                label="终点桩号">
              <template slot-scope="scope">
                k
                <el-input v-model="scope.row.endMile1" :style="{width: '40%'}" @change="changeMile(scope.row)">
                </el-input>
                <div style="width: 10%;text-align: center;display: inline-block;">+</div>
                <el-input v-model="scope.row.endMile2" :style="{width: '40%'}" @change="changeMile(scope.row)">
                </el-input>
              </template>
            </el-table-column>
            <el-table-column
                prop="roadSectionLength"
                align="center"
                label="长度">
              <template slot-scope="scope">
                {{Math.abs(((scope.row.endMile || 0) - (scope.row.beginMile || 0)).toFixed(2))}}
              </template>
            </el-table-column>
            <el-table-column
                prop="field101"
                align="center"
                v-if="!readonly"
                label="操作">
              <template slot-scope="scope">
                <el-button
                    size="mini"
                    type="text"
                    @click="handleDeleteGzw(scope.row)"
                >移除
                </el-button
                >
              </template>
            </el-table-column>
          </el-table>
        </el-col>
        <el-col v-if="!readonly" :span="24" class="mt10">
            <div style="text-align: right">
                <el-button type="primary" @click="handleSave">保存</el-button>
                <el-button @click="close">取消</el-button>
            </div>
        </el-col>
      </el-form>
    </el-row>
    <methods-tree v-if="this.contractList.length > 0" scheme-type="专项养护" :con-id="conId" ref="methodsRef" @input="checkLib" :domain-id="formData.domainId"></methods-tree>
    <el-dialog title="选择构造物" destroy-on-close :visible.sync="structureModel" width="65%" append-to-body v-if="structureModel">
      <project-asset-select @checkAsset="checkAsset" :maiSecId="formData.maiSecId" :assetType="assetType" multiple-choice></project-asset-select>
    </el-dialog>
    <el-dialog title="选择预算" destroy-on-close :visible.sync="budgetModel" width="65%" append-to-body v-if="budgetModel">
      <budget :budgetParams="budgetParams" @checkBudget="checkBudget"></budget>
    </el-dialog>
  </div>
</template>

<script>
import selectTree from "@/components/DeptTmpl/selectTree.vue";
import RoadSection from "@/views/baseData/components/roadSection/index.vue";
import ContractSection from "@/components/ContractSection/index.vue";
import ProjectAssetSelect from "./component/projectAssetSelect";
import {addProject, editProject} from "@/api/maintenanceProject/projectManage";
import MethodsTree from "@/components/MethodsTree/index.vue";
import Budget from "@/views/maintenanceProject/projectManage/component/budget.vue";
import moment from "moment";
import {v4 as uuidv4} from "uuid";
import MethodsList from "@/components/MethodsList/index.vue";
import CostSelect from "@/components/CostSelect/index.vue";
import { Decimal } from 'decimal.js';

export default {
  components: {
    CostSelect,
    Budget, ProjectAssetSelect, ContractSection, RoadSection, selectTree, MethodsTree, MethodsList},
  data() {
    return {
      loading: false,
      formData: {
        year: moment().format('YYYY')
      },
      rules: {
          domainId: [
              { required: true, message: '管养单位不能为空', trigger: 'change' }
          ],
          year: [
              { required: true, message: '年度不能为空', trigger: 'change' }
          ],
          maiSecId: [
              { required: true, message: '路段名称不能为空', trigger: 'change' }
          ],
          name: [
              { required: true, message: '专项名称不能为空', trigger: 'blur' }
          ],
          code: [
              { required: true, message: '项目编码不能为空', trigger: 'blur' }
          ],
          defLiaPer: [
              { required: true, message: '缺陷责任期不能为空', trigger: 'blur' }
          ],
          dates: [
              { required: true, message: '预计时间不能为空', trigger: 'change' }
          ],
          expectDuration: [
              { required: true, message: '预计工期不能为空', trigger: 'blur' }
          ],
          sumFund: [
              { required: true, message: '专项资金不能为空', trigger: 'blur' }
          ],
          projectType: [
              { required: true, message: '工程分类不能为空', trigger: 'change' }
          ],
          mtype: [
              { required: true, message: '所属工程类别不能为空', trigger: 'change' }
          ],
          conIds: [
              { required: true, message: '已选合同不能为空', trigger: 'blur' }
          ],
          reason: [
              { required: true, message: '立项理由不能为空', trigger: 'blur' }
          ],
          enContent: [
              { required: true, message: '主要工程内容不能为空', trigger: 'blur' }
          ],
          enNum: [
              { required: true, message: '主要工程数量不能为空', trigger: 'blur' }
          ]
      },
      editableTabsValue: '',
      treeIndex: 0,
      conId: '',
      contractList: [],
      contractFilterMap: {},
      defaultProps: {
        children: 'children',
        label: 'schemeName'
      },
      structureModel: false,
      assetType: '34',
      gzList: [],
      isFirst: false,
      budgetModel: false,
      budgetParams: {}
    }
  },
  props: {
    rowData: {
      type: Object,
      default: {}
    },
    readonly: {
      type: Boolean,
      default: false
    }
  },
  watch: {
    rowData: {
      handler(val) {
        if (val.id) {
          this.isFirst = true
          this.formData = JSON.parse(JSON.stringify(val))
          this.formData.year = String(this.formData.year)
          if (this.formData.projTDProjectStructureList) this.gzList.push(...this.formData.projTDProjectStructureList)
          this.$set(this.formData, 'dates', [this.formData.beginDate, this.formData.endDate])
          this.formData.domainId = String(this.formData.domainId)
          this.gzList.forEach(item => {
            let beginMile = item.beginMile
            this.$set(item, 'beginMile1', parseInt((beginMile || 0) / 1000))
            this.$set(item, 'beginMile2', beginMile.toString().substr(parseInt(beginMile / 1000).toString().length))
            let endMile = item.endMile
            this.$set(item, 'endMile1', parseInt((endMile || 0) / 1000))
            this.$set(item, 'endMile2', endMile.toString().substr(parseInt(endMile / 1000).toString().length))
          })
          this.formData.projTDProjectDetailList.forEach(item => {
            !this.editableTabsValue ? this.editableTabsValue = item.conId : null
            this.contractList.push({
              conId: item.conId,
              conName: item.conName,
              schemeList: [...item.children]
            })
          })
          if (this.formData.mtype == '26') {
            this.assetType = '31'
          } else if (this.formData.mtype == '29') {
            this.assetType = '32'
          } else {
            this.assetType = '34'
          }
        }
      },
      immediate: true,
      deep: true
    },
    "formData.mtype": {
      handler(val) {
        if (val == '26') {
          this.assetType = '31'
        } else if (val == '29') {
          this.assetType = '32'
        } else {
          this.assetType = '34'
        }
        if (this.isFirst) {
          this.isFirst = false
        } else {
          this.gzList = []
        }
      },
      immediate: true
    },
    contractList: {
      handler(val) {
        if (val){
          const conIds = val.map(item => item.conId)
          this.formData.conIds = conIds.join(',')
          this.formData.conNames = val.map(item => item.conName)
        }
      },
      immediate: true,
      deep: true
    }
  },
  methods: {
    addContract(e) {
      if (e) {
        this.formData.conId = ''
        this.editableTabsValue = e.value
        const exists = this.contractList.some(contract => contract.conId === e.value);
        if (!exists) {
          // 如果不存在，则添加到 contractList
          this.contractList.push({
            conId: e.value,
            conName: e.name,
            schemeList: []
          });
        }
      }
    },
    removeTab(targetName) {
      let tabs = this.contractList;
      let activeName = this.editableTabsValue;
      if (activeName === targetName) {
        tabs.forEach((tab, index) => {
          if (tab.conId === targetName) {
            let nextTab = tabs[index + 1] || tabs[index - 1];
            if (nextTab) {
              activeName = nextTab.conId;
            }
          }
        });
      }
      this.editableTabsValue = activeName;
      this.contractList = tabs.filter(tab => tab.conId !== targetName);
    },
    addScheme(e, index) {
      this.conId = e
      this.treeIndex = index
      this.$refs.methodsRef.openLibModel()
    },
    checkLib(checkDatas) {
      const schemeList = []
      checkDatas = checkDatas.filter(item => {
        return item.nodeType == 2
      })
      checkDatas = checkDatas.filter(item => item.nodeType === 2 && (item.rateFlag === 1 || !schemeList.some(filterItem => filterItem.schemeCode === item.schemeCode)));

      checkDatas.forEach(item => {
        schemeList.push({
          conId: this.contractList[this.treeIndex].conId,
          schemeId: item.id,
          id: uuidv4().replace(/-/g, '').slice(0, 20),
          schemeCode: item.schemeCode,
          schemeName: item.schemeName,
          unit: item.unit,
          price: item.price,
          priceRate: item.priceRate,
          rateFlag: item.rateFlag,
          decimalPlaces: item.decimalPlaces,
          isProduction: item.safetyFeeFlag
        })
      })
      this.contractList[this.treeIndex].schemeList.push(...schemeList)
      // 对this.contractList[this.treeIndex].schemeList根据schemeId去重
      this.contractList[this.treeIndex].schemeList = this.contractList[this.treeIndex].schemeList.reduce((acc, curr) => {
        const exists = acc.some(item => item.id === curr.id);
        return exists ? acc : [...acc, curr];
      }, []);
    },
    handleDeleteGzw(row) {
      this.gzList = this.gzList.filter(item => item.id !== row.id);
    },
    async changeCalculation(row) {
      if (!this.isValidMathFormula(row.calcDesc)) {
        this.$modal.msgError('计算式错误，请检查')
        return
      }
      let num =  this.math.evaluate(row.calcDesc) || 0
      this.$set(row, 'num', this.ceilToTwo(num, row.decimalPlaces))
      await this.changeSchemeNum(row)
    },
    async changeSchemeNum(row) {
      let money = new Decimal(row.num || 0).times(row.price || 0).toNumber()
      this.$set(row, 'amount', Math.round(money))
      this.total = await this.schemeList.reduce((acc, curr) => Number(acc) + Number(curr.amount), 0)
    },
    addStructure() {
      if (!this.formData.maiSecId || !this.formData.mtype) {
        this.$message.error('请先选择工程类型和路段')
        return
      }
      if (this.formData.mtype == '26') {
        this.assetType = '31'
      } else if (this.formData.mtype == '29') {
        this.assetType = '32'
      } else {
        this.assetType = '34'
      }
      this.structureModel = true
    },
    changeDates(e) {
      // 计算两日期相隔的天数
      const beginDate = e[0]
      const endDate = e[1]
      const date1 = new Date(beginDate)
      const date2 = new Date(endDate)
      const diffTime = Math.abs(date2 - date1)
      const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))
      this.$set(this.formData, 'expectDuration', diffDays)
    },
    checkAsset(assetList) {
      assetList.forEach(data => {
        // 如果data.id 再gzList中不存在 则插入到gzList
        const exists = this.gzList.some(item => item.id === data.id);
        data.beginMile = data.beginMile || 0
        data.endMile = data.endMile || 0
        if (!exists) {
          let beginMile = data.beginMile
          data.beginMile1 = parseInt((beginMile || 0) / 1000)
          data.beginMile2 = beginMile.toString().substr(parseInt(beginMile / 1000).toString().length)
          let endMile = data.endMile
          data.endMile1 = parseInt((endMile || 0) / 1000)
          data.endMile2 = endMile.toString().substr(parseInt(endMile / 1000).toString().length)
          this.gzList.push(data)
        }
      })
      this.structureModel = false
    },
    changeMile(row) {
        this.$set(row, 'beginMile', (row.beginMile1 || 0) * 1000 + Number(row.beginMile2 || 0))
        this.$set(row, 'endMile', (row.endMile1 || 0) * 1000 + Number(row.endMile2 || 0))
    },
    handleSave() {
      this.$refs.elForm.validate(valid => {
        if (!valid) return
        if (this.formData.projectType != '4' && !this.formData.budgetId) {
          this.$message.error('请选择预算')
          return
        }
        for (let i = 0; i < this.contractList.length; i++) {
          let contract = this.contractList[i]
          if (contract.schemeList.length == 0) {
            this.$message.error(contract.conName + '下未添加报价信息')
            return
          }
        }
        // 将this.contractList数组中的所有schemeList数组存入this.formData.projTDProjectDetailList中
        this.formData.projTDProjectDetailList = this.contractList.reduce((acc, curr) => {
          return acc.concat(curr.schemeList)
        }, [])
        this.formData.projTDProjectStructureList = this.gzList
        this.formData.beginDate = this.formData.dates[0]
        this.formData.endDate = this.formData.dates[1]
        for (let i = 0; i < this.formData.projTDProjectStructureList.length; i++) {
          let item = this.formData.projTDProjectStructureList[i]
          if (item.laneArray) item.lane = item.laneArray.join(',')
          if (!item.direction && item.structureType != '2') {
            this.$message.error('构造物中上下行不能为空')
            return
          }
        }
        this.loading = true
        if (this.formData.id) {
          editProject(this.formData).then(res => {
            this.loading = false
            this.$message.success('保存成功')
            this.close()
          }).finally(() => {
            this.loading = false
          })
        } else {
          addProject(this.formData).then(res => {
            this.loading = false
            this.$message.success('保存成功')
            this.close()
          }).finally(() => {
            this.loading = false
          })
        }
      })
    },
    changeMaiSecId() {
      const sectionName = this.$refs.roadSection.getLabel(this.formData.maiSecId)
      this.contractFilterMap = {
        sectionName
      }
    },
    openBudget() {
      if (!this.formData.year || !this.formData.maiSecId) {
        this.$message.warning('请先选择年度和路段')
        return
      }
      this.budgetParams = {
        year: this.formData.year,
        maiSecId: this.formData.maiSecId,
        domainId: this.formData.domainId
      }
      this.budgetModel = true
    },
    checkBudget(e) {
      this.$set(this.formData, 'budgetName', e.projectName);
      this.$set(this.formData, 'name', e.projectName);
      this.$set(this.formData, 'sumFund', e.sumFund);
      this.$set(this.formData, 'projectType', e.projType);
      this.$set(this.formData, 'conNames', e.conNames);
      this.$set(this.formData, 'reason', e.projectReason);
      this.$set(this.formData, 'enContent', e.enContent);
      this.$set(this.formData, 'budgetId', e.id);
      const conIdArr = e.conIds.split(',')
      this.editableTabsValue = conIdArr[0]
      this.contractList = []
      e.conNames.split(',').forEach((item,index) => {
      this.contractList.push({
        conName: item,
        conId: conIdArr[index],
        schemeList: e.conDetail[conIdArr[index]] || []
      })
    })
      this.budgetModel = false
    },
    close() {
      this.$emit('close')
    }
  }
}
</script>
<style lang="scss" scoped>
.card_title {
  width: 200px;
  text-align: left;
  margin-bottom: 15px;
  font-weight: bold;
}
::v-deep {
  .el-tabs__header {
    padding-left: 20px;
    border: 0;
  }

  .el-tabs__content {
    border: 0;
  }

  .el-form-item__label {
    font-family: Microsoft YaHei, Microsoft YaHei;
    font-weight: 400;
    font-size: 14px;
    color: #333333;
  }

  .el-input.is-disabled .el-input__inner {
    background-color: white;
    border-color: #dfe4ed;
    color: #1d2129;
  }

  .el-textarea.is-disabled .el-textarea__inner {
    background-color: white;
    border-color: #dfe4ed;
    color: #1d2129;
  }
  .el-range-editor.is-disabled {
    background-color: white;
    border-color: #dfe4ed;
    color: #1d2129;
  }
  .el-range-editor.is-disabled input {
    background-color: white;
    border-color: #dfe4ed;
    color: #1d2129;
  }
}
</style>
<style lang="scss" scoped>
@import "@/assets/styles/business.scss";
</style>
