<template>
  <el-drawer v-bind="$attrs" v-on="$listeners">
    <div class="container">
      <el-descriptions title="巡查类型信息" size="mini" border v-if="false">
        <el-descriptions-item label="类型">
          <dict-tag :options="dict.type.patrol_inspection_category" :value="data.partsType"/>
        </el-descriptions-item>
        <el-descriptions-item label="名称">
          {{ data.partsName }}
        </el-descriptions-item>
        <el-descriptions-item label="描述">
          {{ data.des }}
        </el-descriptions-item>
      </el-descriptions>
      <div style="margin: 0 10%" v-if="false">
        <el-divider>病害类型</el-divider>
      </div>
      <el-divider v-if="false"></el-divider>
      <!--开始-->
      <el-descriptions size="mini">
        <el-descriptions-item label="类型">
          {{ selectDictLabel(dict.type.patrol_inspection_category, data.partsType) }}
        </el-descriptions-item>
        <el-descriptions-item label="名称">
          {{ data.partsName }}
        </el-descriptions-item>
      </el-descriptions>
      <div style="height: 75vh;overflow: auto;margin-top: 20px">
        <el-tree :data="diseaseTree" :props="defaultProps" node-key="id" default-expand-all
                 show-checkbox
                 v-loading="loading"
                 element-loading-spinner="none"
                 element-loading-background="rgba(255, 255, 255, 0.8)"
                 @node-click="onNodeClick"
                 check-on-click-node :expand-on-click-node="false" ref="tree"
                 :default-checked-keys="diseaseIds"></el-tree>
      </div>
      <el-button  type="primary" size="small" style="position: absolute;bottom: 10px;right: 20px" @click="onSubmit" :disabled="buttonDisable">
        <i v-if="loading" class="el-icon-loading el-icon--left"/>完成
      </el-button>
    </div>
  </el-drawer>
</template>

<script>

import {listAllDiseases} from "@/api/patrol/diseases";
import {addBatchPartsDiseases, delPartsDiseasesByPartsId, listAllPartsDiseases} from "@/api/patrol/partsDiseases";

export default {
  name: "PartsDisease",
  dicts: ['patrol_inspection_category'],
  props: {
    data: {
      type: Object,
      default: {}
    }
  },
  data() {
    return {
      loading: false,
      partsType: {
        '1': '桥梁日常巡查',
        '2': '桥梁经常检查',
        '3': '涵洞定期检查',
        '4': '涵洞经常检查',
        '5': '隧道日常巡查',
        '6': '隧道经常检查',
      },
      // partsType2AssetName: {
      //   '1': '31',
      //   '2': '31',
      //   '3': '33',
      //   '4': '33',
      //   '5': '32',
      //   '6': '32',
      // },
      partsType2AssetName: {
        '1': '107',
        '2': '107',
        '3': '108',
        '4': '108',
        '5': '145',
        '6': '145',
      },
      partsType2ParentName: {
        '1': '桥梁病害',
        '2': '桥梁病害',
        '3': '涵洞病害',
        '4': '涵洞病害',
        '5': '隧道病害',
        '6': '隧道病害',
      },
      defaultProps: {
        children: 'children',
        label: 'diseaseName'
      },
      diseaseTree: [],
      diseaseIds: [],
      buttonDisable: true
    };
  },
  methods: {
    async getList() {
      if (!this.data.partsType) {
        this.$refs.tree?.setCheckedKeys(this.diseaseIds)
        return
      }
      // this.loading = true;
      let flag = listAllPartsDiseases({partsId: this.data.id}).then(res => {
        let data = res.data || []
        this.isDelete = data != 0;
        this.diseaseIds = data.map(item => item.diseasesId)
        return Promise.resolve()
      })
      listAllDiseases({assetName: this.partsType2AssetName[this.data.partsType ?? '']}).then(async res => {
        let data = res.data
        await flag
        this.diseaseTree = [{children: data, diseaseName: this.partsType2ParentName[this.data.partsType], id: 1}]
      }).finally(() => {
        // this.loading = false
      })
      this.$nextTick(()=>{
        this.onNodeClick()
      })
    },
    async onSubmit() {
      let flag = true
      if (this.isDelete)
        flag = delPartsDiseasesByPartsId(this.data.id)
      let diseaseIds = this.$refs.tree.getCheckedKeys().filter(item => item !== 1)
      if (diseaseIds.length === 0) {
        this.isDelete = false
        this.diseaseIds = []
        this.$modal.msgSuccess("修改成功");
        return
      }
      this.loading = true
      let formArray = diseaseIds.map(item => {
        return {partsId: this.data.id, diseasesId: item}
      })
      await flag && addBatchPartsDiseases(formArray).then(res => {
        this.$modal.msgSuccess("修改成功");
        this.diseaseIds = diseaseIds
        // this.getList() 能复用就先不重新渲染
      }).finally(()=>{
        this.loading = false
        this.onNodeClick()
      })

    },
    onNodeClick(){
      this.buttonDisable = JSON.stringify(this.$refs.tree?.getCheckedKeys().filter(item => item !== 1)) === JSON.stringify(this.diseaseIds)
    }
  },
  watch: {
    data() {
      this.getList()
    },
  },
};
</script>
<style scoped>
.container {
  margin-top: -20px;
  padding: 20px;
}
</style>
