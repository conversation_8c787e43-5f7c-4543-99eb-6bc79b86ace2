<template>
  <div class="app-container">
    <el-row :gutter="20">

      <!--部门数据-->
      <el-col :span="deptNav ? 5:0 " :xs="24" class="leftDiv">
        <!--折叠图标-->
        <div class="leftIcon"  @click="deptNav=false">
          <span class="el-icon-caret-left"></span>
        </div>
        <div class="head-container">
          <el-input
            v-model="deptName"
            placeholder="单位名称"
            clearable
            size="small"
            prefix-icon="el-icon-search"
            style="margin-bottom: 20px"
          />
        </div>
        <div class="head-container" style="width: 300px">
          <el-tree
            :data="deptOptions"
            :props="defaultProps"
            :expand-on-click-node="false"
            :filter-node-method="filterNode"
            ref="tree"
            :default-expanded-keys="[1]"
            node-key="id"
            highlight-current
            @node-click="handleNodeClick"
          >
            <template slot-scope="{ node, data }">
        　　　　<span class="tree-node-span" style="font-size:14px; width:280px;white-space:nowrap;overflow:hidden;text-overflow:ellipsis;"
                :title="data.label">{{ node.label }}</span>
        　　</template>
          </el-tree>
        </div>
      </el-col>



      <!--筛选区开始-->
      <el-col :span="deptNav ? 19:24" :xs="24">
        <!--展开图标-->
        <div class="rightIcon" @click="deptNav=true" v-show="!deptNav">
          <span class="el-icon-caret-right"></span>
        </div>

        <el-row>
          <el-col :span="24" >
            <el-form :model="queryParams" ref="queryForm" size="small" :inline="true"  label-width="68px">
                <el-form-item >
                  <el-button type="primary"  @click="deptNav=!deptNav"   :icon="deptNavIcon" size="mini">
                    <span v-show="deptNav">折叠</span>
                    <span v-show="!deptNav">展开</span>
                  </el-button>
                </el-form-item>

                  <el-form-item label="" prop="nickName">
                    <el-input
                        v-model="queryParams.nickName"
                        placeholder="请输入姓名"
                        clearable
                        prefix-icon="el-icon-user"
                        style="width: 240px"
                        @keyup.enter.native="handleQuery"
                    />
                  </el-form-item>
                  <el-form-item label="" prop="phonenumber">
                    <el-input
                        v-model="queryParams.phonenumber"
                        placeholder="请输入电话号码"
                        clearable
                        prefix-icon="el-icon-user"
                        style="width: 240px"
                        @keyup.enter.native="handleQuery"
                    />
                  </el-form-item>
              <el-form-item>
                <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
                <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
                <!-- <el-button v-show="!showSearch" @click="showSearch=true" icon="el-icon-arrow-down" circle></el-button>
                <el-button  v-show="showSearch" @click="showSearch=false"  icon="el-icon-arrow-up" circle></el-button> -->
              </el-form-item>
            </el-form>
            <!--默认折叠-->
          </el-col>
        </el-row>
        <!--筛选区结束-->

        <!--操作按钮区开始，后期需要就放开-->
        <el-row :gutter="10" class="mb8" v-if="false">
          <el-col :span="1.5">
            <el-button
                type="warning"
                icon="el-icon-download"
                size="mini"
                @click="handleExport"
            >导出</el-button>
          </el-col>
          <right-toolbar :showSearch.sync="showSearch" @queryTable="getList" :columns="columns"></right-toolbar>
        </el-row>
        <!--操作按钮区结束-->

        <!--数据表格开始-->
        <div class="tableDiv">
          <el-table v-adjust-table size="mini" :height="showSearch ? 'calc(100vh - 320px)' : 'calc(100vh - 230px)'"   style="width: 100%" v-loading="loading" border :data="laborpersonList">
            <el-table-column type="selection" width="50" align="center" />
            <el-table-column fixed label="序号" type="index" width="50">
              <template v-slot="scope">
                {{ (scope.$index + (queryParams.pageNum - 1) * queryParams.pageSize)+1 }}
              </template>
            </el-table-column>
            <el-table-column label="姓名" align="center" prop="nickName"/>
            <el-table-column label="性别" align="center" prop="sex" width="70" :formatter="getSexName"/>
            <el-table-column label="职称" align="center" prop="postNames" />
            <el-table-column label="员工编号" align="center" prop="userId" />
            <el-table-column label="管养单位" align="center" prop="dept.deptName" />
            <el-table-column label="电话号码" align="center" prop="phonenumber" />
          </el-table>

          <pagination
              v-show="total>0"
              :total="total"
              :page.sync="queryParams.pageNum"
              :limit.sync="queryParams.pageSize"
              @pagination="getList"
          />
        </div>
        <!--数据表格结束-->
      </el-col>
    </el-row>
  </div>
</template>

<script>
  import { getConstructionTree,getConstructUserList } from "@/api/patrol/laborqualification";
  import { listUser } from "@/api/system/user";
  import { getToken } from "@/utils/auth";
  import Treeselect from "@riophae/vue-treeselect";
  import "@riophae/vue-treeselect/dist/vue-treeselect.css";

  export default {
    name: "Laborperson",
    dicts: ['sys_user_sex'],
    components: {  },
    data() {
      return {
        // 遮罩层
        loading: false,
        // 选中数组
        ids: [],
        // 非单个禁用
        single: true,
        // 非多个禁用
        multiple: true,
        // 显示搜索条件
        showSearch: false,
        dictType:[],
        // 总条数
        total: 0,
        // 施工单位人员表格数据
        laborpersonList: null,
        // 弹出层标题
        title: "",
        // 左侧组织树
        deptNav: true,
        // 部门树选项
        deptOptions: undefined,
        // 部门名称
        deptName: undefined,
        // 是否显示弹出层
        open: false,

        // 表单参数
        form: {},
        defaultProps: {
          children: "children",
          label: "label"
        },
        // 用户导入参数
        upload: {
          // 是否显示弹出层（用户导入）
          open: false,
          // 弹出层标题（用户导入）
          title: "",
          // 是否禁用上传
          isUploading: false,
          // 是否更新已经存在的用户数据
          updateSupport: 0,
          // 设置上传的请求头部
          headers: { Authorization: "Bearer " + getToken() },
          // 上传的地址
          url: process.env.VUE_APP_BASE_API + "/system/user/importData"
        },
        // 查询参数
        queryParams: {
          pageNum: 1,
          pageSize: 50,
            deptList: [],
            nickName: null,
            phonenumber: null
        },
        // 列信息
        columns: [
          { key: 1, label: `姓名`, visible: true },
          { key: 2, label: `性别`, visible: true },
          { key: 3, label: `职称`, visible: true },
          { key: 4, label: `员工编号`, visible: true },
          { key: 5, label: `管养单位`, visible: true },
          { key: 6, label: `电话号码`, visible: true }
        ],
      };
    },
    watch: {
      // 根据名称筛选部门树
    },
    created() {
      this.getList();
      this.getDeptTree();
      console.log("fdsfsfsdfd", this.dict.type.sys_user_sex);
    },
    computed: {
      deptNavIcon() {
          return this.deptNav ? 'el-icon-arrow-left' : 'el-icon-arrow-right';
      },
    },
    methods: {
      /** 查询用户列表 */
      getList() {
        this.loading = true;
        getConstructUserList(this.queryParams).then(response => {
          this.laborpersonList = response.rows;
          this.total = response.total;
          this.loading = false;
        });
      },
       /** 查询部门下拉树结构 */
      getDeptTree() {
        getConstructionTree().then(response => {
          this.deptOptions = response.data;
        });
      },
      // 筛选节点
      filterNode(value, data) {
        if (!value) return true;
        return data.label.indexOf(value) !== -1;
      },
      // 节点单击事件
      handleNodeClick(data) {
        this.queryParams.deptList = [data.id];
        this.handleQuery();
      },
      //获取性别名称
      getSexName(row, column, cellValue) {
        if (this.dict.type.sys_user_sex) {
          let filterVals = this.dict.type.sys_user_sex.filter(item => Number.parseInt(item.value) == cellValue);
          if (filterVals && filterVals.length > 0) {
            return filterVals[0].label;
          }
        }
        return cellValue;
      },
      // 取消按钮
      cancel() {
        this.open = false;
        this.reset();
      },
      // 表单重置
      reset() {
        this.form = {
            name: null,
            sex: null,
            phone: null,
            idCard: null,
            personType: null,
            job: null,
            projectId: null,
            maintainId: null,
            status: null,
            delFlag: null
        };
        this.resetForm("form");
      },
      /** 搜索按钮操作 */
      handleQuery() {
        this.queryParams.pageNum = 1;
        this.getList();
      },
      /** 重置按钮操作 */
      resetQuery() {
        this.dateRange = [];
        this.resetForm("queryForm");
        this.handleQuery();
      },
      /** 导出按钮操作 */
      handleExport() {
        this.download('manager/laborperson/export', {
          ...this.queryParams
        }, `laborperson_${new Date().getTime()}.xlsx`)
      },
  }
  };
</script>
<style scoped>
  .hasTagsView .app-main[data-v-078753dd]{
    background: #f5f7fa;
  }

  .tableDiv{
    background-color: white;
    padding-bottom: 10px;
  }

  .leftDiv{
    border-right: 1px solid #d8dce5;
    min-height: calc(100vh - 110px);
    overflow-y: auto;
    height: calc(80vh - 110px);
    position: relative;
    top: -20px;
    padding-top: 10px;
    background-color: white;
  }

  .leftIcon{
    border: 1px solid #DCDFE6;
    border-radius: 8px;
    width: 16px;
    height: 50px;
    line-height: 50px;
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
    position: absolute;
    right: 0;
    top: 300px;
    z-index: 2;
  }
  .leftIcon:hover{
    background-color: #DCDFE6;
  }

  .rightIcon{
    border: 1px solid #DCDFE6;
    border-radius: 8px;
    width: 16px;
    height: 50px;
    line-height: 50px;
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
    position: absolute;
    left: -10px;
    top: 280px;
    z-index: 10;
    background: white;
  }
  .rightIcon:hover{
    background-color: #DCDFE6;
  }
</style>
