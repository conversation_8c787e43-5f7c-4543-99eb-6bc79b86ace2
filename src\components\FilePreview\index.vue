<template>

  <el-dialog
    :visible.sync="officePreviewVisible"
    :before-close="handelClose"
    append-to-body
    width="80%"
  >
    <div >
      <div v-if="isFileTooLarge && isDownloadSizeLimitMB" class="file-download-container">
        <button @click.stop="handleDownload" class="download-btn center-btn">
          文件大小为{{formatFileSize(this.fileInfo.fileSize)}}，超过 {{ downloadSizeLimitMB }}MB，点击下载
        </button>
      </div>
      <div v-else>
        <div v-show="isDownloadFile && fileInfo.url">
          <button
            class="download-btn right-top-btn"
            @click.stop="handleDownload"
          >
            下载文件
          </button>
        </div>
        <OfficePreview
          v-show="fileInfo.url"
          :content-type="fileInfo.contentType"
          :dialog-url="fileInfo.url"
        />
      </div>
      <!-- 加载动画 -->
      <div v-if="isLoading" class="loading-overlay">
        <div class="loading-spinner"></div>
        <span>下载中，请稍候...</span>
      </div>
    </div>
  </el-dialog>
</template>

<script>
import OfficePreview from "@/components/OfficePreview";
import { getFile } from "@/api/file";
import {formatFileSize} from "@/utils/ruoyi";

export default {
  name: "FilePreview",
  components: {
    OfficePreview,
  },
  props: {
    officePreviewVisible:{
      type:Boolean,
      default:false
    },
    isDownloadFile: {
      type: Boolean,
      default: true,
    },
    ownerId: {
      type: String,
      required: true,
    },
    downloadSizeLimitMB: {
      type: Number,
      default: 2,
    },
    isDownloadSizeLimitMB: {
      type: Boolean,
      default: true,
    },
  },
  data() {
    return {
      fileInfo: {
        url:'',
        contentType:''
      },
      isLoading: false, // 添加加载状态
    };
  },
  created() {
  },
  watch: {
    // 监听 ownerId 的变化，变化时重新加载文件信息
    ownerId(newVal, oldVal) {
      if (newVal !== oldVal) {
        this.getFileInfo();
      }
    },
  },
  methods: {
    formatFileSize,
    // 获取文件信息
    async getFileInfo() {
      try {
        const res = await getFile({ ownerId: this.ownerId });
        if (res.code === 200 && res.data) {
          this.fileInfo = res.data;
        } else {
          this.$message.error("文件预览失败");
        }
      } catch (error) {
        console.error("获取文件信息失败", error);
        this.$message.error("文件加载失败");
      }
    },

    // 文件下载方法
    async handleDownload() {
      const { url, originalFilename, contentType } = this.fileInfo;

      if (!url) {
        this.$message.error("文件无法下载，未找到文件的URL");
        return;
      }

      // 直接打开 PDF 文件
      if (contentType === "application/pdf") {
        window.open(url, "_blank");
        return;
      }

      this.isLoading = true; // 开始加载动画
      this.$modal
          .confirm("确认下载文件？")
          .then(async () => {
            try {
              const response = await fetch(url);
              if (!response.ok) throw new Error("下载文件失败！");
              const blob = await response.blob();

              const link = document.createElement("a");
              link.href = URL.createObjectURL(blob);
              link.download = originalFilename || this.getFileNameFromUrl(url);

              document.body.appendChild(link);
              link.click();
              document.body.removeChild(link);
              URL.revokeObjectURL(link.href);
            } catch (error) {
              this.$message.error("下载文件失败！");
            } finally {
              this.isLoading = false; // 结束加载动画
            }
          })
          .catch(() => {
            this.isLoading = false; // 取消确认时结束加载动画
          });
    },

    getFileNameFromUrl(url) {
      try {
        const parsedUrl = new URL(url);
        const pathname = parsedUrl.pathname;
        return pathname.substring(pathname.lastIndexOf("/") + 1);
      } catch (error) {
        console.error("解析文件名失败", error);
        return "downloaded-file";
      }
    },
    handelClose() {
      this.$emit("close");
    },
  },
  computed: {
    isFileTooLarge() {
      const fileSize = this.fileInfo?.fileSize || 0;
      return fileSize > this.downloadSizeLimitMB * 1024 * 1024;
    },
  },
};
</script>

<style scoped>
/* 防止按钮被父容器拉伸 */
.file-download-container {
  max-width: 300px; /* 设置最大宽度 */
  width: 30%;       /* 默认宽度 */
  margin: 0 auto;   /* 居中显示 */
  padding: 10px;
  text-align: center;
}
/* 下载按钮的基础样式 */
.download-btn {
  //width: auto; /* 调整为自动宽度 */
  //height: auto; /* 调整为自动高度 */
  padding: 10px 20px;
  background-color: #409eff;
  color: #fff;
  border: none;
  border-radius: 5px;
  cursor: pointer;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  font-size: 14px;
  transition: all 0.2s;
}

.download-btn:hover {
  background-color: #66b1ff !important;
}


/* 居中显示的样式 */
.center-btn {
  position: absolute;
  left: 50%;
  top: 70%;
  transform: translate(-50%, -50%);
  z-index: 999;
}

/* 右上角按钮的样式 */
.right-top-btn {
  position: absolute;
  top: 10px;
  right: 50px;
  z-index: 1000;
}


/* 加载动画样式 */
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 2000;
  color: #fff;
  font-size: 16px;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid rgba(255, 255, 255, 0.6);
  border-radius: 50%;
  border-top-color: #fff;
  animation: spin 1s infinite linear;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}
</style>
