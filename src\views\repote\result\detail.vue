<template>
	<div>
		<el-descriptions
			title=""
			size="small"
			:column="4"
			class="fixed-height-descriptions"
			:style="{ height: descriptionsHeight + 'px' }"
		>
			<el-descriptions-item
				v-if="missionData.name"
				label="任务名称"
				class="descriptions-height-item"
			>
				{{ missionData.name }}
			</el-descriptions-item>
			<el-descriptions-item
				v-if="missionData.userName"
				label="发布人"
				class="descriptions-height-item"
			>
				{{ missionData.userName }}
			</el-descriptions-item>
			<el-descriptions-item
				v-if="missionData.deptName"
				label="发布部门"
				class="descriptions-height-item"
			>
				{{ missionData.deptName }}
			</el-descriptions-item>
		</el-descriptions>
		<!--数据表格开始-->
		<div class="tableDiv">
			<el-table
				v-adjust-table
				size="mini"
				:height="tableHeight + 'px'"
				style="width: 100%"
				v-loading="loading"
				border
				:data="detailList"
				@row-click="handleRowClick"
			>
				<el-table-column label="序号" type="index"></el-table-column>
				<el-table-column
					label="填报表名称"
					:show-overflow-tooltip="true"
					align="center"
					prop="name"
				/>
				<el-table-column
					label="填报要求"
					:show-overflow-tooltip="true"
					align="center"
					prop="request"
				/>
				<el-table-column label="模板" align="center" prop="url">
					<template v-slot="scope">
						<FileUpload
							previewWidth="80%"
							for-view
							:value="scope.row.url"
							:download-name="`${scope.row.name}_模板_${scope.row.createTime}`"
						></FileUpload>
					</template>
				</el-table-column>
				<el-table-column label="备注" align="center" prop="status"></el-table-column>
				<el-table-column label="填写情况" align="center">
					<template slot-scope="scope">{{ scope.row.finishNum }}/{{ scope.row.totalNum }}</template>
				</el-table-column>
				<el-table-column label="汇总报表地址" align="center" width="200">
					<template slot-scope="scope">
						<FileUpload
							preview-width="80%"
							@click.stop.native
							for-view
							:value="scope.row.ownerId"
							:key="scope.row.ownerId"
							:download-name="scope.row.name"
							:need-time="true"
						/>
					</template>
				</el-table-column>
				<el-table-column label="操作" align="center" width="100" class-name="small-padding">
					<template slot-scope="scope">
						<el-button
							size="mini"
							type="text"
							icon="el-icon-s-goods"
							@click.stop="handleZip(scope.row)"
						>
							批量导出
						</el-button>
						<el-button
							size="mini"
							type="text"
							icon="el-icon-edit"
							@click.stop="handleMerge(scope.row)"
						>
							合并报表
						</el-button>
					</template>
				</el-table-column>
			</el-table>

			<pagination
				v-show="total > 0"
				:total="total"
				:page.sync="queryParams.pageNum"
				:limit.sync="queryParams.pageSize"
				@pagination="getList"
			/>
		</div>
		<!--数据表格结束-->
	</div>
</template>

<script>
import { EventBus } from '@/utils/eventBus'
import { getToken } from '@/utils/auth'
import { addDetail, delDetail, getDetail, listDetail, updateDetail } from '@/api/supervise/detail'
import ImageUpload from '@/views/patrol/diseases/ImageUpload.vue'
import ImagePreview from '@/views/patrol/diseases/ImagePreview.vue'
import {
	addRepoteForm,
	delRepoteForm,
	getRepoteForm,
	listRepoteForm,
	updateRepoteForm,
	mergeRepoteForm,
	downloadAndZipFiles,
} from '@/api/repote/repoteForm'
import { delRepoteMission } from '@/api/repote/repoteMission'
import { Loading } from 'element-ui'

export default {
	name: 'MissionDetail',
	components: { ImageUpload, ImagePreview },
	props: {},
	data() {
		return {
			// 遮罩层
			loading: true,
			// 选中数组
			ids: [],
			// 非单个禁用
			single: true,
			// 非多个禁用
			multiple: true,
			// 显示搜索条件
			showSearch: false,
			dictType: [],
			// 总条数
			total: 0,
			// 督查详情表格数据
			detailList: null,
			// 弹出层标题
			title: '',
			// 部门树选项
			deptOptions: undefined,
			// 是否显示弹出层
			open: false,

			// 表单参数
			form: {},
			defaultProps: {
				children: 'children',
				label: 'label',
			},
			// 用户导入参数
			upload: {
				// 是否显示弹出层（用户导入）
				open: false,
				// 弹出层标题（用户导入）
				title: '',
				// 是否禁用上传
				isUploading: false,
				// 是否更新已经存在的用户数据
				updateSupport: 0,
				// 设置上传的请求头部
				headers: { Authorization: 'Bearer ' + getToken() },
				// 上传的地址
				url: process.env.VUE_APP_BASE_API + '/system/user/importData',
			},
			// 查询参数
			queryParams: {
				pageNum: 1,
				pageSize: 50,
				missionId: null,
				name: null,
				request: null,
				url: null,
				headRow: null,
			},
			// 列信息
			columns: [
				{ key: 0, label: `任务ID`, visible: true },
				{ key: 1, label: `填报表格名称`, visible: true },
				{ key: 2, label: `填报表格要求`, visible: true },
				{ key: 3, label: `填报表格备注`, visible: true },
				{ key: 4, label: `填报模板地址`, visible: true },
				{ key: 5, label: `表头所在行数`, visible: true },
			],
			// 表单校验
			// 表单校验
			rules: {
				missionId: [{ required: true, message: '任务ID不能为空', trigger: 'blur' }],
				name: [{ required: true, message: '任务名称不能为空', trigger: 'blur' }],
				headRow: [{ required: true, message: '表头行不能为空', trigger: 'blur' }],
			},
			missionData: {},
			descriptionsHeight: 0,
			tableHeight: 0,
		}
	},
	watch: {},
	created() {
		EventBus.$on('updateMissionData', (data) => {
			if (data && data.id) {
				this.missionData = data
				this.queryParams.missionId = data.id
				this.form.missionId = data.id
				this.getList()
			} else {
				console.error('获取数据为空:', data)
			}
		})
		EventBus.$on('formHeightUpdated', this.updateDescriptionsHeight)
		EventBus.$on('tableHeightUpdated', this.updateTableHeight)
	},
	methods: {
		updateDescriptionsHeight(formHeight) {
			this.descriptionsHeight = formHeight
		},
		updateTableHeight(tableHeight) {
			this.tableHeight = tableHeight
		},
		/** 合并操作 */
		handleMerge(row) {
			// 显示 loading
			this.loadingInstance = Loading.service({
				lock: true,
				text: '正在合并，请稍候...',
				spinner: 'el-icon-loading',
				background: 'rgba(0, 0, 0, 0.7)',
				target: document.body,
			})
			mergeRepoteForm(row.id)
				.then((response) => {
					this.$modal.msgSuccess('合并成功')
				})
				.finally(() => {
					// 无论成功还是失败，都关闭 loading
					this.getList()
					this.loadingInstance.close()
				})
		},
		// async handleZip(row) {
		//   try {
		//     // 显示 loading
		//     this.loadingInstance = Loading.service({
		//       lock: true,
		//       text: "正在打包，请稍候...",
		//       spinner: "el-icon-loading",
		//       background: "rgba(0, 0, 0, 0.7)",
		//       target: document.body,
		//     });

		//     const response = await downloadAndZipFiles({
		//       formId: row.id,
		//       fileName: encodeURIComponent(row.name),
		//     });
		//     const blob = new Blob([response.data], { type: "application/zip" });
		//     const url = window.URL.createObjectURL(blob);
		//     this.saveFileToLink(url, row.name, "zip");
		//     this.loadingInstance.close();
		//   } catch (error) {
		//     console.error("下载文件时出错");
		//     // 处理错误，例如显示错误消息
		//   } finally {
		//     this.loadingInstance.close();
		//   }
		// },
		handleZip(row) {
			this.download(
				'repote/repoteForm/download',
				{
					formId: row.id,
					fileName: encodeURIComponent(row.name),
				},
				`${encodeURIComponent(row.name)}.zip`
			)
		},

		// saveFileToLink(url, fileName, fileType) {
		//   const link = document.createElement("a");
		//   link.href = url;
		//   link.setAttribute("download", `${fileName}.${fileType}`);
		//   document.body.appendChild(link);
		//   link.click();
		//   document.body.removeChild(link);
		//   window.URL.revokeObjectURL(url);
		// },
		/** 行点击事件 */
		handleRowClick(row) {
			const detailData = {
				missionName: this.missionData.name,
				...row,
			}
			EventBus.$emit('openRecordDialog', detailData)
		},
		/** 查询用户列表 */
		getList() {
			this.loading = true
			if (this.queryParams.missionId == null) {
				this.loading = false
				return
			}
			listRepoteForm(this.queryParams).then((response) => {
				this.detailList = response.rows
				this.total = response.total
				this.loading = false
			})
		},
		// 取消按钮
		cancel() {
			this.open = false
			this.reset()
		},
		// 表单重置
		reset() {
			this.form = {
				missionId: this.missionData.id,
				name: null,
				request: null,
				remark: null,
				url: null,
				headRow: null,
			}
			this.resetForm('form')
		},
		/** 搜索按钮操作 */
		handleQuery() {
			this.queryParams.pageNum = 1
			this.getList()
		},
		/** 重置按钮操作 */
		resetQuery() {
			this.dateRange = []
			this.resetForm('queryForm')
			this.handleQuery()
		},
		// 多选框选中数据
		handleSelectionChange(selection) {
			this.ids = selection.map((item) => item.id)
			this.single = selection.length != 1
			this.multiple = !selection.length
		},
		/** 新增按钮操作 */
		handleAdd() {
			this.reset()
			this.open = true
			this.title = '添加填报表格规范'
		},
		/** 修改按钮操作 */
		handleUpdate(row) {
			this.reset()
			const id = row.id || this.ids
			getRepoteForm(id).then((response) => {
				this.form = response.data
				this.open = true
				this.title = '修改添填报表格规范'
			})
		},
		/** 提交按钮 */
		submitForm: function () {
			this.$refs['form'].validate((valid) => {
				if (valid) {
					if (this.form.id != null) {
						updateRepoteForm(this.form).then((response) => {
							this.$modal.msgSuccess('修改成功')
							this.open = false
							this.getList()
						})
					} else {
						addRepoteForm(this.form).then((response) => {
							this.$modal.msgSuccess('新增成功')
							this.open = false
							this.getList()
						})
					}
				}
			})
		},
		/** 删除按钮操作 */
		handleDelete(row) {
			if (row.id) {
				const id = row.id
				this.$modal
					.confirm('是否确认删除填报格规范名称为' + row.name + '的数据项？')
					.then(function () {
						return delRepoteForm(id)
					})
					.then(() => {
						this.getList()
						this.$modal.msgSuccess('删除成功')
					})
			} else {
				if (this.ids.length > 0) {
					this.$modal.confirm('是否确认删除选中的' + this.ids.length + '条数据项？').then(() => {
						let delArray = []
						this.ids.forEach((item) => {
							delArray.push(delRepoteForm(item))
						})
						Promise.all(delArray).then(() => this.$modal.msgSuccess('删除成功') || this.getList())
					})
				}
			}
		},

		/** 导出按钮操作 */
		handleExport() {
			this.download(
				'supervise/detail/export',
				{
					...this.queryParams,
				},
				`detail_${new Date().getTime()}.zip`
			)
		},
		/** 导入按钮操作 */
		handleImport() {
			this.upload.title = '用户导入'
			this.upload.open = true
		},
		/** 下载模板操作 */
		importTemplate() {
			this.download('system/user/importTemplate', {}, `user_template.xlsx`)
		},
		// 文件上传中处理
		handleFileUploadProgress(event, file, fileList) {
			this.upload.isUploading = true
		},
		// 文件上传成功处理
		handleFileSuccess(response, file, fileList) {
			this.upload.open = false
			this.upload.isUploading = false
			this.$refs.upload.clearFiles()
			this.$alert(
				"<div style='overflow: auto;overflow-x: hidden;max-height: 70vh;padding: 10px 20px 0;'>" +
					response.msg +
					'</div>',
				'导入结果',
				{ dangerouslyUseHTMLString: true }
			)
			this.getList()
		},
		// 提交上传文件
		submitFileForm() {
			this.$refs.upload.submit()
		},
	},
}
</script>
<style scoped lang="scss">
.container {
	margin-top: -20px;
	padding: 20px;
}
::v-deep .el-list-enter-active,
::v-deep .el-list-leave-active {
	transition: all 0s;
}

::v-deep .el-list-enter,
.el-list-leave-active {
	opacity: 0;
	transform: translateY(0);
}

.tableDiv {
	::v-deep .el-upload-list__item {
		width: 42px;
		height: 42px;
		margin: 0px 5px -6px 0 !important;
	}

	::v-deep .el-upload--picture-card {
		width: 42px;
		height: 42px;
		margin: 4px 0;
	}

	::v-deep .el-icon-plus {
		font-size: 1rem;
	}

	::v-deep .el-upload-list__item-actions {
		font-size: 1rem;
	}

	::v-deep .el-upload-list__item-actions span + span {
		margin-left: 5px !important;
	}
}
</style>
