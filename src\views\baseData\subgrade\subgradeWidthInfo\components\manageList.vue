<template>
    
    <div class="route-road" v-loading="loading">
        <el-button v-if="canEdit" type="primary" @click="addList">新增</el-button>
        <el-table
            size="mini"
            height="30vh"
            style="width: 100%"
            v-loading="loading"
            border
            :data="staticList"
            >
          
            <el-table-column
                fixed
                label="序号"
                type="index"
                width="50"
                align="center"
            />
            <el-table-column
               
                label="工程性质"
                align="center"
                prop="engineeringProperties"    
                
                min-width="140"
                show-overflow-tooltip
            />
            <el-table-column
               
               label="最近一次改造时间"
               align="center"
               prop="lastReformTime"    
               
               min-width="140"
               show-overflow-tooltip
           />
           <el-table-column
               
               label="主要病害"
               align="center"
               prop="majorDisease"    
               
               min-width="140"
               show-overflow-tooltip
           />
           <el-table-column
               label="改造部位"
               align="center"
               prop="reformSite"    
               
               min-width="140"
               show-overflow-tooltip
           />
           <el-table-column
               label="改造部位"
               align="center"
               prop="reformSite"    
               
               min-width="140"
               show-overflow-tooltip
           />
           <el-table-column
                fixed="right"
                label="操作"
                align="center"
                width="100"
                v-if="canEdit"
            >
                <template slot-scope="scope">
                <el-button
                    style="font-size: 12px;font-weight: 500;"
                    type="text"
                    @click="editList(scope.row)"
                > 编辑</el-button>
                <el-button
                    style="font-size: 12px;font-weight: 500;color: red;"

                    type="text"
                    @click="deleteList(scope.row)"
                > 删除</el-button>
                </template>
                
            </el-table-column>
        </el-table>
        <el-dialog
            :title="dialogTitle"
            :visible="showAddEdit"
            width="60%"
            append-to-body
            :before-close="handleClose"
            :close-on-click-modal="false"
        >
            <el-form
              ref="form"
              :model="manageForm"
              label-width="190px"
            >
                <el-form-item
                    label="工程性质"
                    prop="engineeringProperties"
                    :rules="[{ required: true, message: '请输入工程性质', trigger: 'blur' }]"
                >
                    <el-input
                        v-model="manageForm.engineeringProperties"
                        placeholder="请输入工程性质"
                        clearable
                      />
                </el-form-item>
                <el-form-item
                    label="最近一次改造时间"
                    prop="lastReformTime"
                    :rules="[{ required: true, message: '请输入最近一次改造时间', trigger: 'blur' }]"
                >
                    <el-date-picker
                        style="width:100%"
                        v-model="manageForm.lastReformTime"
                        type="date"
                        clearable
                        value-format="yyyy-MM-dd"
                      />
                </el-form-item>
                <el-form-item
                    label="主要病害"
                    prop="majorDisease"
                    :rules="[{ required: true, message: '请输入主要病害', trigger: 'blur' }]"
                >
                    <el-input
                        v-model="manageForm.majorDisease"
                        placeholder="请输入主要病害"
                        clearable
                      />
                </el-form-item>
                <el-form-item
                    label="改造部位"
                    prop="reformSite"
                    :rules="[{ required: true, message: '请输入改造部位', trigger: 'blur' }]"
                >
                    <el-input
                        v-model="manageForm.reformSite"
                        placeholder="请输入改造部位"
                        clearable
                      />
                </el-form-item>
                
            </el-form>
            <div
                slot="footer"
                class="dialog-footer"
            >
                <el-button
                    type="primary"
                    @click="handleSubmit('edit')"
                >提 交</el-button>
                
                <el-button @click="handleClose">取 消</el-button>
            </div>
        </el-dialog>
    </div>
</template>

<script>

import { getDicts as getDicts } from '@/api/system/dict/data'
import {segmentsList} from '@/api/baseData/tunnel/baseInfo/getSections'

import {getListPage,dynamicAdd,dynamicEdit,dynamicGet,dynamicDelete} from '@/api/baseData/tunnel/baseInfo/manageList.js'

export default { // 路段选择器
    name: 'RouteRoad',
    components: {},
    props: {
        staticList: {
          
            default: []
        },
        tunnelId: {
            type: String,
            default: 0
        },
        canEdit: {
            type: Boolean,
            default: true
        }
    },
    data() {
        return {
            loading: false,
            dialogTitle: '新增',
            showAddEdit: false,
            manageForm: {
                engineeringProperties: '',
                lastReformTime: '',
                majorDisease: '',
                reformSite: '',
            },
            id: 0,  
        }
    },
    watch: {},
    computed: {
        
    },
    created() {
        
    },
    mounted() { 
        
    },
    methods: {
        deleteList(row){
            this.$confirm('是否删除该条数据', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                dynamicDelete(row).then(res => {
                    if(res.code == 200){
                        this.$message.success('删除成功')
                        this.$emit('refresh')
                    }else{
                        this.$message.error(res.msg)
                    }
                })
            }).catch(() => {})
        },
        editList(row){
            this.dialogTitle = '编辑'
            this.showAddEdit = true
            dynamicGet(row).then(res => {
                this.manageForm = res.data
            })
        },
        addList(){
            this.dialogTitle = '新增'
            this.showAddEdit = true
        },
        handleClose(done) {
            this.$confirm('确认关闭？')
                .then(_ => {
                    this.showAddEdit = false
                    this.manageForm= {
                        engineeringProperties: '',
                        lastReformTime: '',
                        majorDisease: '',
                        reformSite: '',
                    }
                })
                .catch(_ => {});
        },

        handleSubmit(type) {
            this.$refs.form.validate((valid) => {
                if (valid) {
                    if(this.dialogTitle == '新增'){
                        this.manageForm.tunnelId = this.tunnelId
                        dynamicAdd(this.manageForm).then(res => {
                            if(res.code == 200){
                                this.$message.success('新增成功')
                                
                                this.manageForm= {
                                    engineeringProperties: '',
                                    lastReformTime: '',
                                    majorDisease: '',
                                    reformSite: '',
                                }
                                
                                this.showAddEdit = false
                                this.$emit('refresh')
                            }else{
                                this.$message.error(res.msg)
                            }
                        })
                    }else{
                        this.manageForm.tunnelId = this.tunnelId
                        dynamicEdit(this.manageForm).then(res => {
                            if(res.code == 200){
                                this.$message.success('编辑成功')
                                this.showAddEdit = false
                                this.$emit('refresh')
                            }else{
                                this.$message.error(res.msg)
                            }
                        })
                    }
                } else {
                    return false;
                }
            });
        },
    },
    watch: {
        
    }
}
</script>



<style lang="scss" scoped>
.route-road {}
</style>