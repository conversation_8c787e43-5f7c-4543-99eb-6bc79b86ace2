<template>
  <div class="dept-tree" ref="deptRef" :style="{ maxHeight: isBig ? 'calc(100vh - 28px)' : 'calc(100vh - 14px)' }">
    <div class="dept-tree-title" :style="topStyle" @click="onChoose">
      <i :class="expanded ? 'el-icon-caret-bottom' : 'el-icon-caret-right'" @click.stop="onTExpand"></i>
      <span class="tree-title">集团公司</span>
    </div>
    <div class="tree-body" v-show="expanded">
      <el-tree :data="data" :props="defaultProps" accordion lazy :load="loadNode" ref="treeRef" @node-expand="onExpand"
        @node-click="handleNodeClick" node-key="id" :default-expanded-keys="expandedkeys"
        :expand-on-click-node="false"></el-tree>
    </div>
  </div>
</template>

<script>
import { deptTreeSelect } from "@/api/tmpl";
import { getMaintenanceSectionListAll } from "@/api/baseData/common/routeLine";
import {
  addMapMask,
  addWidthFeature,
  getLineConfig,
  onMatching,
  removeAllLayer,
  removeLayer,
  removeOverlay,
  toBack,
  wktToFeature,
} from "./common/mapFun";
import { getShapeList } from "@/api/oneMap/deptInfo";
import { isBigScreen } from "./common/util";
import { mapState, mapActions, mapMutations } from "vuex";
import cache from "@/plugins/cache";

export default {
  data() {
    return {
      defaultProps: {
        children: "children",
        label: "label",
        isLeaf: "leaf",
      },
      data: [],
      show: false,
      expanded: true,
      isBig: isBigScreen(),
      maintenanceSectionIds: [],
      expandedkeys: [],
      oldId: "",
      routeType: '',
    };
  },
  computed: {
    topStyle() {
      let obj = {};
      if (this.show) {
        obj = {
          background:
            "linear-gradient(90deg, rgba(0, 94, 255, 0.5) 0%, rgba(1, 102, 254, 0.05) 100%)",
        };
      } else {
        obj = {
          background:
            "linear-gradient( 90deg, rgba(0,94,255,0.1) 0%, rgba(1,102,254,0.01) 100%)",
        };
      }
      return obj;
    },
    ...mapState({
      clickBoolean: (state) => state.map.clickBoolean,
    }),
  },
  watch: {},
  mounted() {
    this.routeType = this.$route.query.type || ''

    if (this.routeType == '1') {
      this.$nextTick(() => {
        this.onChoose()
      })
    }
    this.getDeptTree();
    window.$Bus.$on("mapClick", (data) => {
      let deptId = data.dept_id || data.sys_dept_id;
      this.expandedkeys = [deptId];
      // element ui tree 回显高亮
      if (deptId) {
        let data = this.data.filter((v) => v.id === deptId);
        if (data && data.length > 0 && this.$refs.treeRef) {
          this.$refs.treeRef?.setCurrentKey(deptId);
        }
      }
      let d = this.$refs.treeRef?.getCurrentNode();
      this.handleNodeClick(d, { expanded: true });
      this.onSetTree();
    });
    window.$Bus.$on("reSet", () => {
      let obj = {
        maintenanceSectionIds: [],
        deptIds: [],
      };
      window.$Bus.$emit("treeClick", obj);
      window.$Bus.$emit("treeClick:weather", obj);
      this.oldId = "";
      this.show = true;
      this.$refs.treeRef?.setCurrentKey(null);
      const nodes = this.$refs.treeRef.store._getAllNodes();
      nodes.forEach((node) => {
        node.expanded = false;
      });
      removeLayer(window.mapLayer, "highwayLayer", "name");
      removeLayer(window.mapLayer, "treeHighwayLayer", "name");
    });

    this.$nextTick(() => {
      this.setDeptW({ deptW: this.$refs.deptRef?.clientWidth || 0 });
    });
    window.addEventListener("resize", () => {
      this.$nextTick(() => {
        this.setDeptW({ deptW: this.$refs.deptRef?.clientWidth || 0 });
      });
    });
  },
  destroyed() {
    window.$Bus.$off("mapClick", () => { });
    window.$Bus.$off("reSet", () => { });
  },
  methods: {
    ...mapActions({
      setSpread: "map/setSpread",
    }),
    ...mapMutations({
      setDeptW: "map/setDeptW",
      setCatalogW: "map/setCatalogW",
    }),
    async getDeptTree() {
      let res = await deptTreeSelect({ types: 100, deptTypeList: [1, 3, 4] });
      if (res.code === 200) {
        // 数据处理
        this.data = res.data[0].children.map((v) => {
          return v;
        });
      }
    },
    async loadNode(node, resolve) {
      if (node.level === 0) {
        resolve(this.data);
      } else if (node.level === 1) {
        let res = (await this.getDeptChild(node.data)) || [];
        let arr = [];
        if (res) {
          arr = res.map((v) => {
            v.label = v.maintenanceSectionName || "";
            v.leaf = true;
            return v;
          });
        }
        if (arr && !arr.length) {
          node.data.leaf = true;
          node.leaf = true;
          this.$forceUpdate();
        }
        resolve(arr);
      } else {
        resolve([]);
      }
    },
    getDeptChild(data) {
      return new Promise(async (resolve, reject) => {
        let arr = this.data.filter((v) => v.id === data.id) || [];
        // 数组对象扁平化
        let childs = [];
        if (arr && arr.length && arr[0].children) {
          childs = arr[0].children.map((v) => {
            return v;
          });
        }
        let allArr = [...arr, ...childs];
        let ids = allArr.map((v) => v.id);
        let params = {
          departmentIdList: ids,
        };
        await getMaintenanceSectionListAll(params)
          .then((res) => {
            if (res.code === 200) {
              this.maintenanceSectionIds = res.data.map(
                (v) => v.maintenanceSectionId
              );
              resolve(res.data);
            } else {
              resolve([]);
            }
          })
          .catch((err) => {
            reject(err);
          });
      });
    },
    onExpand(data, node, self) {

      this.$nextTick(() => {
        setTimeout(() => {
          // 隐藏子元素的图标
          if (node && data.leaf) {
            // 获取节点对应的DOM元素
            let nodeElement = self.$el;
            // 获取子元素并修改其样式
            let childElements = nodeElement.querySelector(
              ".el-tree-node__expand-icon"
            );
            // 修改子元素的样式
            if (childElements) {
              childElements.style.color = "transparent";
            }
            this.$forceUpdate();
          }
        }, 500);
      });
    },

    // 获取分出
    async handleNodeClick(data, node) {
      if (this.oldId === node.id) {
        return;
      } else {
        this.oldId = node.id;
      }
      this.show = false;
      let id
      let isHighway = false
      if (this.$route.query.type != '1') {
        isHighway = node.level == 2 ? true : false
      }
      if (node.parent && node.level == 2) {
        id = node.parent.data.id
      } else {
        id = data.id
      }

      removeAllLayer(window.mapLayer);
      removeLayer(window.mapLayer, "maskLayer", "name");
      removeLayer(window.mapLayer, "dataLayer");
      removeLayer(window.mapLayer, "dataLayer", "name");
      removeLayer(window.mapLayer, "highwayLayer", "name");
      removeLayer(window.mapLayer, "treeHighwayLayer", "name");
      // 清空天气图层数据
      removeLayer(window.mapLayer, "weatherLayer");
      removeLayer(window.mapLayer, "gifLayer");
      removeLayer(window.mapLayer, "weatherLayer", "name");
      removeLayer(window.mapLayer, "gifLayer", "name");
      let deptArr = onMatching(id);
      if (deptArr && deptArr.length) {
        removeLayer(window.mapLayer, "dataLayer");
        removeLayer(window.mapLayer, "maskLayer");
        addMapMask(window.mapLayer, deptArr);
        addWidthFeature(window.mapLayer, deptArr, "dataLayer", null, true, this.$store.state.map.deptMinZoom);
        let feature = wktToFeature(deptArr[0].mgeom);
        window.mapLayer.getView().fit(feature.getGeometry().getExtent(), {
          duration: 500,
        });
      } else {
        this.getRangeShape(id, true);
      }

      let arr = this.data.filter((v) => v.id === id);
      let childs = [];
      if (arr && arr.length > 0) {
        if (arr[0].children && arr[0].children.length > 0) {
          childs = arr[0].children;
        }
      }

      let dataObj = data;
      dataObj.id = data.id || node.parent.data.id

      let maintenanceSectionIds = [];
      if (node.parent && node.level == 2) {
        maintenanceSectionIds = [data.maintenanceSectionId];
      } else {
        let result = await this.getDeptChild(dataObj);
        maintenanceSectionIds = result.map((v) => v.maintenanceSectionId);
      }
      let obj = {
        maintenanceSectionIds,
        deptIds: arr.map((v) => v.id) || [],
      };
      this.onSetTree();
      removeLayer(window.mapLayer, "路网信息", "name");
      // 重新获取路线
      getLineConfig(arr.map((v) => v.id) || []);
      window.$Bus.$emit("treeClick", obj, isHighway);
      window.$Bus.$emit("treeClick:weather", obj, isHighway);
      // 存储deptId 到 vuex中
      this.$store.commit("map/setDeptId", { deptId: data.id });
    },
    onTExpand() {
      this.expanded = !this.expanded;
    },
    onChoose() {
      // 告诉tree组件，去关闭weather组件
      window.$Bus.$emit("closeWeather");
      this.oldId = "";
      // 重新加载路线
      removeLayer(window.mapLayer, "路网信息", "name");
      getLineConfig([]);
      removeLayer(window.mapLayer, 'heatLayer', 'name');
      removeLayer(window.mapLayer, "highwayLayer", "name");
      removeLayer(window.mapLayer, "treeHighwayLayer", "name");
      removeOverlay();
      if (this.routeType == '1') {
        this.show = true;
        this.onSetTree();
        let obj = {
          maintenanceSectionIds: [],
          deptIds: [],
          isTop: true,
        };
        window.$Bus.$emit("treeClick", obj);
        window.$Bus.$emit("treeClick:weather", obj);
        this.$refs.treeRef?.setCurrentKey(null);
        window.$Bus.$emit("JieGouWuJianCe");
        return
      }
      this.show = true;
      if (this.show) {
        this.onSetTree();
        let obj = {
          maintenanceSectionIds: [],
          deptIds: [],
          isTop: true,
        };
        window.$Bus.$emit("treeClick", obj);
        window.$Bus.$emit("treeClick:weather", obj);
      } else {
        this.setSpread(0);
        this.$emit("click", false);
        this.$nextTick(() => {
          this.setCatalogW({ catalogW: 0 });
        });
      }
      // 清除 element ui tree 所有选中状态
      this.$refs.treeRef?.setCurrentKey(null);
      // this.getRangeShape();
      toBack();
    },
    // 获取范围线
    getRangeShape(deptId = null, click = false) {
      // this.$modal.loading();
      let deptIds;
      let isAdmin = this.$store.getters.roles.includes("admin");
      if (deptId) {
        deptIds = [deptId || this.deptId];
      } else {
        deptIds = isAdmin ? [] : [deptId];
      }
      getShapeList({ sysDeptIds: deptIds })
        .then((res) => {
          if (res.code == 200) {
            if (!click) {
              cache.session.setJSON("rangeData", res.data);
            }
            removeLayer(window.mapLayer, "dataLayer");
            removeLayer(window.mapLayer, "maskLayer", "name");
            addMapMask(window.mapLayer, res.data);
            // dataLayer-管理处小图标
            addWidthFeature(window.mapLayer, res.data, "dataLayer", null, true, this.$store.state.map.deptMinZoom);
            let centerArr = res.data.filter(
              (v) => v.sys_dept_id == 46 || v.sys_dept_id == 70
            );
            if (centerArr && centerArr.length) {
              let geom = centerArr[1].mgeom || centerArr[0].mgeom;
              let feature = wktToFeature(geom);
              window.mapLayer.getView().fit(feature.getGeometry().getExtent(), {
                duration: 500,
              });
              // 设置地图缩放级别
              let view = window.mapLayer.getView();
              view.setZoom(isBigScreen() ? 8.5 : 7);
            }
            // 处理点击后移除不了图层数据问题
            if (click) {
              this.removeLayer("dataLayer");
            }
          }
        })
        .finally(() => {
          // this.$modal.closeLoading();
        });
    },
    onSetTree(data = null) {
      let width = this.isBig ? 640 : 320;
      this.setSpread(width);
      this.$emit("click", true);
    },
  },
};
</script>

<style lang="scss" scoped>
@import "@/assets/styles/utils.scss";

.dept-tree {
  z-index: 8;
  position: absolute;
  top: vwpx(10px);
  left: vwpx(10px);
  overflow: hidden;
  padding: vwpx(10px);

  width: vwpx(640px);
  min-height: vwpx(1000px);
  background: linear-gradient(90deg, #020a1e 0%, rgba(12, 42, 86, 0.2) 100%);
  box-shadow: inset 0px 0px 10px 0px rgba(35, 134, 255, 0.5);
  border-radius: 10px;
  border: 1px solid #0687ff;

  .dept-tree-title {
    display: flex;
    align-items: center;
    color: #9cbdff;
    height: vwpx(76px);
    margin-bottom: vwpx(6px);
    cursor: pointer;
    padding: 0 vwpx(10px);

    .tree-title {
      font-family: Source Han Sans, Source Han Sans;
      font-weight: 400;
      font-size: vwpx(30px);
      color: #ffffff;
      font-style: normal;
      text-transform: none;
      margin-left: vwpx(6px);
    }
  }

  .tree-body {
    max-height: calc(100vh - 140px);
    overflow-y: auto;

    &::-webkit-scrollbar {
      width: vwpx(16px);
      background: rgba(2, 10, 30, 0.8);
    }

    &::-webkit-scrollbar-thumb {
      background: rgba(35, 134, 255, 0.3);
      border-radius: vwpx(8px);
    }

    &::-webkit-scrollbar-track {
      background: rgba(2, 10, 30, 0.8);
      border-radius: vwpx(8px);
    }

    ::v-deep .el-tree {
      background: transparent;
      border: none;
      color: #ffffff;
      overflow-y: auto;

      // 节点的背景颜色
      .el-tree-node {
        background: linear-gradient(90deg,
            rgba(0, 94, 255, 0.1) 0%,
            rgba(1, 102, 254, 0.01) 100%);
        margin-bottom: vwpx(6px);

        .el-tree-node__children {
          .el-tree-node__content {
            padding-left: vwpx(30px) !important;

            .el-tree-node__expand-icon {
              font-size: vwpx(35px);
              color: rgba(255, 255, 255, 0);
            }
          }
        }
      }

      // 鼠标悬浮时节点的背景颜色
      .el-tree-node__content {
        padding-left: vwpx(20px) !important;
        height: vwpx(76px);

        .el-tree-node__expand-icon {
          font-size: vwpx(35px);
          color: #9cbdff;
        }

        .el-tree-node__label {
          font-family: Microsoft YaHei, Microsoft YaHei;
          font-weight: 400;
          font-size: vwpx(30px);
          color: #ffffff;
          text-align: left;
          font-style: normal;
          text-transform: none;
        }

        &:hover {
          background: linear-gradient(90deg,
              rgba(0, 94, 255, 0.5) 0%,
              rgba(1, 102, 254, 0.05) 100%);
        }

        .el-tree-node__expand-icon {
          padding: 2px;
        }
      }

      // 鼠标点击时节点的背景颜色
      .el-tree-node:focus>.el-tree-node__content {
        background: linear-gradient(90deg,
            rgba(0, 94, 255, 0.5) 0%,
            rgba(1, 102, 254, 0.05) 100%);
      }

      // 鼠标失去焦点时节点背景的颜色
      .is-current>.el-tree-node__content:first-child {
        background: linear-gradient(90deg,
            rgba(0, 94, 255, 0.5) 0%,
            rgba(1, 102, 254, 0.05) 100%);
      }
    }
  }
}
</style>
