<template>
	<div class="summary-container">
		<!-- 查询条件区域 -->
		<div class="query-container">
			<el-row :gutter="48" style="width: 80%">
				<!-- 部门 路线 养护路段 组件-->
				<el-col :span="9" :offset="0">
					<CascadeSelection
						style="min-width: 192px; margin-right: 10px"
						:form-data="queryForm"
						v-model="queryForm"
						types="203"
						multiple
					/>
				</el-col>
				<!-- 起止时间 选到日 -->
				<el-col :span="9" :offset="0">
					<el-date-picker
						v-model="queryForm.patrolTime"
						type="daterange"
						range-separator="至"
						start-placeholder="开始日期"
						end-placeholder="结束日期"
						:picker-options="pickerOptions"
					/>
				</el-col>
				<!-- 搜索按钮和导出清单按钮 -->
				<el-col :span="6" :offset="0">
					<el-button type="primary" @click="handleSearch">搜索</el-button>
					<el-button type="primary" @click="handleExport">导出清单</el-button>
				</el-col>
			</el-row>
		</div>
		<!-- 表格区域 -->
		<div class="table-container">
			<el-table v-adjust-table :data="tableData" style="width: 100%" height="calc(100% - 50px)">
				<el-table-column prop="number" label="序号" />
				<el-table-column prop="" label="养护路段" />
				<el-table-column prop="" label="巡查单位" />
				<el-table-column prop="" label="分处" />
				<el-table-column prop="" label="管理处" />
				<el-table-column prop="" label="巡查日期" />
				<el-table-column prop="" label="巡查次数" />
				<el-table-column prop="" label="巡查总里程(公里)" />
				<el-table-column prop="" label="桥梁巡查记录(异常/总数)" />
				<el-table-column prop="" label="隧道巡查记录(异常/总数)" />
				<el-table-column prop="" label="巡查病害数量" />
				<el-table-column prop="" label="报表">
					<template slot-scope="scope">
						<el-button type="primary" @click="handleReport(scope.row, 0)">综合</el-button>
						<el-button type="primary" @click="handleReport(scope.row, 1)">桥梁</el-button>
						<el-button type="primary" @click="handleReport(scope.row, 2)">隧道</el-button>
					</template>
				</el-table-column>
			</el-table>
			<pagination
				v-show="total > 0"
				:total="total"
				:page.sync="queryForm.pageNum"
				:limit.sync="queryForm.pageSize"
				@pagination="getList"
			/>
		</div>
	</div>
</template>

<script>
import CascadeSelection from '@/components/CascadeSelection/index.vue'
export default {
	components: {
		CascadeSelection,
	},
	data() {
		return {
			queryForm: {
				pageNum: 1,
				pageSize: 10,
				maintenanceSectionId: null,
				managementMaintenanceIds: null,
				routeCodes: null,
				patrolTime: null,
			},
			tableData: [],
			total: 0,
			pickerOptions: {
				shortcuts: [
					{
						text: '最近一周',
						onClick(picker) {
							const end = new Date()
							const start = new Date()
							start.setTime(start.getTime() - 3600 * 1000 * 24 * 7)
							picker.$emit('pick', [start, end])
						},
					},
					{
						text: '最近一个月',
						onClick(picker) {
							const end = new Date()
							const start = new Date()
							start.setTime(start.getTime() - 3600 * 1000 * 24 * 30)
							picker.$emit('pick', [start, end])
						},
					},
					{
						text: '最近三个月',
						onClick(picker) {
							const end = new Date()
							const start = new Date()
							start.setTime(start.getTime() - 3600 * 1000 * 24 * 90)
							picker.$emit('pick', [start, end])
						},
					},
				],
			},
		}
	},
	methods: {
		handleSearch() {
			this.getList()
		},
		handleExport() {
			console.log('导出清单')
		},
		handleReport(row, type) {
			console.log('报表', row, type)
		},
		getList() {
			console.log('获取列表', this.queryForm)
		},
	},
}
</script>

<style lang="scss" scoped>
.summary-container {
	height: 100%;
	display: flex;
	flex-direction: column;
}

.query-container {
	padding-bottom: 20px;
}

.table-container {
	flex: 1;
	overflow: hidden;

	.el-table {
		height: calc(100% - 50px);
	}
}
</style>
