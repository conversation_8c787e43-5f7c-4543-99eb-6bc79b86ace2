<template>
  <div class="tunnel-index">
    <!-- <div class="type">
      <el-select v-model="bridgeType" value-key="" placeholder="请选择" clearable style="width: 120px;"
        :popper-append-to-body="false">
        <el-option v-for="item in typeOptions" :key="item.value" :label="item.label" :value="item.value" />
      </el-select>
    </div> -->
    <!-- <pie-echarts name="隧道" :data="tnnelData" :color="color" height="17vh"></pie-echarts> -->
    <Echarts :option="option" v-if="option" :height="`calc(20vh - ${isBig ? '40px' : '25px'})`" key="tunnelKey" />
  </div>
</template>

<script>
import { isBigScreen } from '../../util/utils';
import PieEcharts from '../echarts/pieEcharts.vue';
import Echarts from '../echarts/echarts.vue';
export default {
  components: {
    PieEcharts,
    Echarts,
  },
  props: {
    statisticsData: { default: () => { } },
  },
  data() {
    return {
      option: null,
      bridgeType: '',
      arr: [
        {
          typeCount: 119,
          typeLabel: '特长隧道'
        },
        {
          typeCount: 428,
          typeLabel: '长隧道'
        },
        {
          typeCount: 316,
          typeLabel: '中隧道'
        },
        {
          typeCount: 538,
          typeLabel: '短隧道'
        }
      ],
      // tnnelData: [
      //   {
      //     name: "长隧道",
      //     value: 3000,
      //   },
      //   {
      //     name: "特长隧道",
      //     value: 870,
      //   },
      //   {
      //     name: "中隧道",
      //     value: 5502,
      //   },
      //   {
      //     name: "短隧道",
      //     value: 6789,
      //   },
      // ],
      // color: ["#8B34FF", "#FFE000", "#FFA800", "#FF5B00", "#ffa800"],
      isBig: isBigScreen(),
    };
  },
  created() {
    var outr = '80%';
    var inr = '77%';
    var numberdata = this.arr.map(
      (item) => item.typeCount
    );
    let colorObj = {
      特长隧道: '#8B34FF',
      长隧道: '#FFE000',
      中隧道: '#FFA800',
      短隧道: '#FF5B00',
    };
    this.option = this.initPieCharts(outr, inr, numberdata, colorObj);
  },
  methods: {
    initPieCharts(outr = 135, inr = 125, numberdata = [], colorObj = []) {
      outr = this.isBig ? "83%" : "79%";
      inr = '74%';
      let total = 0;
      let center = ['23%', '49%']; //此处调整圆环的位置
      let left = '22%';
      let size = 13;
      //计算总和
      for (let i = 0; i < numberdata.length; i++) {
        total += Number(numberdata[i]);
      }
      let placeHolderStyle = {
        normal: {
          label: {
            show: false,
          },
          labelLine: {
            show: false,
          },
          color: 'rgba(0, 0, 0, 0)',
          borderColor: 'rgba(0, 0, 0, 0)',
          borderWidth: 0,
        },
      };

      var data = [];
      for (
        var i = 0;
        i < this.arr.length;
        i++
      ) {
        let el = this.arr[i];
        data.push(
          {
            value: el.typeCount,
            name: el.typeLabel,
            itemStyle: {
              normal: {
                borderWidth: 5,
                shadowBlur: 0,
                borderColor: colorObj[el.typeLabel],
                shadowColor: colorObj[el.typeLabel],
                color: colorObj[el.typeLabel],
                borderRadius: 0,
              },
            },
          },
          {
            value: total / 60,
            name: '',
            itemStyle: placeHolderStyle,
          }
        );
      }

      var seriesObj = [
        {
          type: 'pie',
          zlevel: 0,
          silent: true,
          radius: ['90%', '94%'],
          center,
          hoverAnimation: false,
          color: 'rgba(0,62,122,1)',
          label: {
            normal: {
              show: false,
            },
          },
          labelLine: {
            normal: {
              show: false,
            },
          },
          data: [1],
        },
        {
          name: '',
          type: 'pie',
          clockWise: false,
          startAngle: '90',
          center, //此处调整圆环的位置
          radius: [outr, inr], //此处可以调整圆环的大小
          hoverAnimation: false,
          itemStyle: {
            normal: {
              label: {
                show: false,
                position: 'outside',
                color: '#fff',
                lineHeight: this.isBig ? 32 : 16,
                formatter: function (params) {
                  var percent = 0;
                  var total = 1;
                  for (var i = 0; i < data.length; i++) {
                    total += data[i].value;
                  }
                  percent = ((params.value / total) * 100).toFixed(2);
                  if (params.name !== '') {
                    return (
                      params.name +
                      `：${params.value}` +
                      '\n' +
                      '占比：' +
                      percent +
                      '%'
                    );
                  } else {
                    return '';
                  }
                },
                fontSize: this.isBig ? 30 : 12,
              },
              labelLine: {
                length: this.isBig ? 30 : 15,
                length2: this.isBig ? 60 : 30,
                show: false,
                color: '#00ffff',
              },
            },
          },
          data: data,
          animationType: 'scale',
          animationEasing: 'elasticOut',
          animationDelay: function (idx) {
            return idx * 50;
          },
        },
      ];

      let option = {
        backgroundColor: 'rgba(0,0,0,0)',
        tooltip: {
          show: true,
          formatter: (params) => {
            if (params.name == "") {
              return "";
            }
            return `${params.name}: ${params.value}`;
          },
        },
        title: {
          text: `${total}`,
          subtext: '隧道',
          top: '36%',
          textAlign: 'center',
          left: left,
          textStyle: {
            color: '#fff',
            fontSize: this.isBig ? 54 : 22,
            fontWeight: '700',
            shadowColor: 'rgba(27,126,242,0.8)',
            shadowBlur: 10,
            shadowOffsetX: 5,
            shadowOffsetY: 5,
          },
          subtextStyle: {
            fontSize: this.isBig ? 46 : 16,
            align: 'center',
            color: '#fff',
          },
        },
        toolbox: {
          show: false,
        },
        series: seriesObj,
        legend: {
          show: true,
          right: this.isBig ? '6%' : '5%',
          y: 'center' || this.isBig ? '9%' : '5%',
          icon: 'circle',
          itemWidth: this.isBig ? 30 : 10, // 设置宽度
          itemHeight: this.isBig ? 30 : 10, // 设置高度
          itemGap: this.isBig ? 35 : 16,
          formatter: (name) => {
            let d = data.filter((v) => v.name == name);
            let num = d[0].value;
            let percent = 0;
            percent = ((num / total) * 100).toFixed(2);
            return (
              ' {title|' +
              name +
              '：}' +
              '{num|' +
              num +
              '} ' +
              '{unit|座}' +
              ' {title|占比：}' +
              '{num|' +
              percent +
              '}' +
              '{unit|%}'
            );
          },
          textStyle: {
            color: '#fff',
            fontSize: this.isBig ? 30 : size,
            rich: {
              title: {
                color: '#fff',
                fontSize: this.isBig ? 30 : size - 1,
                padding: [3, 0],
              },
              num: {
                color: '#fff',
                fontSize: this.isBig ? 30 : size,
              },
              unit: {
                color: '#fff',
                fontSize: this.isBig ? 28 : size - 1,
              },
              percent: {
                color: '#fff',
                fontSize: this.isBig ? 28 : size - 1,
              },
            },
          },
        },
      };
      return option;
    },
  },
};
</script>

<style lang="scss" scoped>
@import '@/assets/styles/utils.scss';

.tunnel-index {
  padding: vwpx(20px) 0;
  color: white;
  position: relative;

  // .type {
  //   position: absolute;
  //   top: 10px;
  //   right: 10px;
  //   z-index: 99;
  // }

  // ::v-deep .el-input {
  //   .el-input__inner {
  //     background-color: rgba(1, 102, 254, 0.2);
  //     border: 1px solid #0166fe;
  //     color: #ffffff;
  //     // border-right: none;
  //   }

  //   .el-input__inner::placeholder {
  //     color: #bbbbbb;
  //   }

  //   .el-input-group__append {
  //     background-color: rgba(1, 102, 254, 0.2);
  //     border: 1px solid #0166fe;
  //     color: #ffffff;
  //     border-left: none;
  //     padding: 0 10px;
  //     cursor: pointer;
  //   }
  // }
}
</style>
