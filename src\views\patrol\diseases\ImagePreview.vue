<template>
  <div v-if="listMode">
    <el-image
      v-for="item in realSrcList"
      :src="item"
      fit="cover"
      :key="item"
      :preview-src-list="realSrcList"
      :style="`width:${realWidth};height:${realHeight};`"
    >
      <div slot="error" class="image-slot">
        <i class="el-icon-picture-outline"></i>
      </div>
    </el-image>
  </div>
  <el-image
    v-else
    :src="realSrc"
    fit="cover"
    :style="`width:${realWidth};height:${realHeight};`"
    :preview-src-list="realSrcList"
  >
    <div slot="error" class="image-slot">
      <i class="el-icon-picture-outline"></i>
    </div>
  </el-image>
</template>
<script>

import axios from 'axios'
import {getToken} from "@/utils/auth";

/**
 * 先不考虑使用缩略图,
 */
export default {
  name: "ImagePreview",
  props: {
    /**
     * 单个ownerId、ownerId数组、多个ownerId字符串逗号拼接，都行
     */
    ownerId: {
      type: [String, Array],
      default: ""
    },
    /**
     * 是否列表模式； 列表模式，每个文件都列出来；非列表模式，多个文件只会显示一个，点击详情才能切换其他文件
     */
    listMode: {
      type: Boolean,
      default: false
    },
    width: {
      type: [String, Number],
      default: ""
    },
    height: {
      type: [String, Number],
      default: ""
    },
  },
  data() {
    return {
      realSrc: '',
      realSrcList: [],
    }
  },
  methods: {
    async computeSrc() {
      if (!this.ownerId) {
        return;
      }
      let ownerList = Array.isArray(this.ownerId) ? this.ownerId : this.ownerId.split(',');
      let firstOwner = ownerList[0]
      await axios.get(process.env.VUE_APP_BASE_API + "/ruoyi-file/findFiles?ownerId=" + firstOwner, {
        headers: {
          Authorization: "Bearer " + getToken(),
        }
      }).then(res => {
        let imageList = res.data.data
        this.realSrc = imageList[0]?.url ?? ''
      })
    },
    async computeRealSrcList() {
      if (!this.ownerId) {
        return;
      }
      let ownerList = Array.isArray(this.ownerId) ? this.ownerId : this.ownerId.split(',');
      ownerList.forEach(item => {
        axios.get(process.env.VUE_APP_BASE_API + "/ruoyi-file/findFiles?ownerId=" + item, {
          headers: {
            Authorization: "Bearer " + getToken(),
          }
        }).then(res => {
          let imageList = res.data.data
          imageList.forEach(item => {
            this.realSrcList.push(item.url)
          })
        })
      });
    }
  }
  ,
  watch: {
    ownerId: {
      handler() {
        this.$nextTick(() => {
          this.computeSrc()
          this.computeRealSrcList()
        })
      },
      immediate: true
    }
  }
  ,
  computed: {
    realWidth() {
      return typeof this.width == "string" ? this.width : `${this.width}px`;
    },
    realHeight() {
      return typeof this.height == "string" ? this.height : `${this.height}px`;
    }
  }
}
</script>

<style lang="scss" scoped>
.el-image {
  border-radius: 5px;
  background-color: #ebeef5;
  box-shadow: 0 0 5px 1px #ccc;
  margin-right: 3px;

  ::v-deep .el-image__inner {
    transition: all 0.3s;
    cursor: pointer;

    &:hover {
      transform: scale(1.2);
    }
  }

  ::v-deep .image-slot {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;
    height: 100%;
    color: #909399;
    font-size: 30px;
  }
}
</style>
