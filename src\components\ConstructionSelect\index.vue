<template>
  <div>
    <el-select :id="id" v-model="selectValue" :placeholder="placeholder" clearable style="width: 100%;"
               :disabled="readOnly" @change="changeDomain">
      <el-option v-for="(item,index) in constructionUnitList"
                 :key="index"
                 :label="item.conDomainName"
                 :value="item.conDomainId">
      </el-option>
    </el-select>
  </div>
</template>
<script>
import {getList} from "@/api/contract/mgmtConstRela";
import {v4 as uuidv4} from "uuid";
import {listMaintenanceSectionAll} from "@/api/system/maintenanceSection";

export default {
  props: {
    value: {
      type: Object,
      default: ''
    },
    readOnly: {
      type: Boolean,
      default: false
    },
    maiSecId: {
      type: String,
      default: ''
    },
    maiDomainId: {
      type: String,
      default: ''
    },
    type: {
      type: Number,
      default: null
    },
    placeholder: {
      type: String,
      default: '施工单位'
    },
    addUserId: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      constructionUnitList: [],
      isFirstLoad: true, // 新增标志变量
      id: uuidv4().replace(/-/g, ''),
      routeList: []
    }
  },
  computed: {
    selectValue: {
      get: function () {
        return Number(this.value) || null
      },
      set: function (val) {
        this.$emit('input', val)
      }
    }
  },
  watch: {
    maiSecId: {
      handler: function (val) {
        if (val) {
          this.$nextTick(() => {
            const disabled = document.getElementById(this.id).disabled
            const maiSecNameLike = this.routeList.find(item => item.maintenanceSectionId == val)?.maintenanceSectionName || ''
            const params = {
              maiSecNameLike,
              maiDomainId: this.maiDomainId ? parseInt(this.maiDomainId) : undefined,
              type: this.type,
              readOnly: this.readOnly || disabled ? 1 : 0,
              pageSize: 500,
              page: 1
            }
            if (this.addUserId) {
              const deptId = this.$store.getters.deptId
              params.conDomainId = deptId
            }
            getList(params).then(res => {
              if (params.readOnly != 1) {
                res.rows = res.rows.filter((item, index, self) => {
                  return self.findIndex(t => t.conDomainName === item.conDomainName) === index;
                });
              }
              this.constructionUnitList = res.rows
              if (!this.isFirstLoad) {
                this.$emit('input', '');
              }
              this.isFirstLoad = false;
            })
          })
        }
      },
      immediate: true
    }
  },
  created() {
    this.getMaisecList()
  },
  methods: {
    changeDomain(e) {
      this.$emit('change', e)
    },
    getMaisecList() {
      const routeList = sessionStorage.getItem('routeList')
      if (routeList) {
        this.routeList = JSON.parse(routeList)
      } else {
        listMaintenanceSectionAll().then(res => {
          if (res.code == 200) {
            this.routeList = res.data
            sessionStorage.setItem('routeList', JSON.stringify(res.data))
          }
        })
      }
    }
  }
}
</script>
<style scoped lang="scss">

</style>
