<template>
  <div class="file-preview">
    <div v-if="contentType || dialogUrl">
      <img
        v-if="isImage"
        width="100%"
        :src="dialogUrl"
        :key="dialogUrl"
        alt=""
        style="height: 80vh"
      />
      <!-- Word 文件预览 -->
      <vue-office-docx
        v-else-if="isWord"
        :src="dialogUrl"
        style="height: 80vh"
        :key="dialogUrl"
      />
      <!-- Excel 文件预览 -->
      <Luckysheet
        v-else-if="isExcel"
        :file-url="dialogUrl"
        style="height: 80vh"
        :key="dialogUrl"
        :excelOptions="excelOptions"
      />
      <!-- PDF 文件预览 -->
      <vue-office-pdf
        v-else-if="isPDF"
        :src="dialogUrl"
        style="height: 80vh"
        :key="dialogUrl"
      />
      <img v-else :src="thumbnailUrl" alt="文件缩略图" />
    </div>
  </div>
</template>

<script>
import VueOfficeDocx from "@vue-office/docx";
import "@vue-office/docx/lib/index.css";
import VueOfficePdf from "@vue-office/pdf";
import Luckysheet from "@/components/Luckysheet"; // 确保路径正确

export default {
  name: "OfficePreview",
  components: {
    VueOfficeDocx,
    VueOfficePdf,
    Luckysheet,
  },
  props: {
    dialogUrl: {
      type: String,
      required: true,
    },
    contentType: {
      type: String,
      required: true,
    },
    excelOptions: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {};
  },
  methods: {
    errorHandler() {
      console.log("渲染失败");
    },
  },
  computed: {
    // 检查文件是否为图像
    isImage() {
      return this.contentType.startsWith("image/");
    },
    // 检查文件是否为Word
    isWord() {
      return (
        "application/msword" === this.contentType ||
        "application/vnd.openxmlformats-officedocument.wordprocessingml.document" ===
          this.contentType
      );
    },
    // 检查文件是否为Excel
    isExcel() {
      // this.options.xls = "application/vnd.ms-excel" === this.contentType;

      return (
        "application/vnd.ms-excel" === this.contentType ||
        "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet" ===
          this.contentType
      );
    },
    // 检查文件是否为PDF
    isPDF() {
      return "application/pdf" === this.contentType;
    },
    // 根据文件类型计算缩略图 URL
    thumbnailUrl() {
      switch (this.contentType) {
        case "application/vnd.ms-powerpoint":
        case "application/vnd.openxmlformats-officedocument.presentationml.presentation":
          return require("@/assets/images/Ippt.png");
        case "application/zip":
        case "application/x-zip-compressed":
        case "application/x-rar-compressed":
        case "application/x-7z-compressed":
        case "application/x-tar":
        case "application/gzip":
          return require("@/assets/images/Izip.png");
        default:
          return require("@/assets/images/Iother.png");
      }
    },
  },
};
</script>
