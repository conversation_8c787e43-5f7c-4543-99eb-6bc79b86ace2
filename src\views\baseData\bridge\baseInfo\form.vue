<template>
  <div>
    <el-dialog
      v-loading="loading"
      :title="title"
      :visible.sync="showForm"
      width="60%"
      append-to-body
      :before-close="handleClose"
      :close-on-click-modal="false"
      :class="forView ? 'forView' : ''"
    >
      <el-tabs v-model="activeName" type="card" @tab-click="handleClick">
        <el-tab-pane label="行政识别数据" name="administrative">
          <div style="height: 60vh; overflow-y: auto; padding: 0 10px 0 5px">
            <el-form
              ref="administrative"
              :model="form"
              label-width="190px"
              :disabled="forView"
            >
              <div style="display: flex; flex-wrap: wrap">
                <ManageSelectTree placeholder="请选择" :formObject="form" />
                <el-col
                  v-for="(item, index) in administrativeFields"
                  :key="index"
                  :span="item.span ? item.span : 12"
                >
                  <el-form-item
                    :label="item.label"
                    :prop="item.prop"
                    :rules="item.rules"
                  >
                    <span v-if="item.type === 'input'">
                      <el-input
                        v-model="form[item.prop]"
                        :placeholder="item.placeholder"
                        clearable
                      />
                    </span>
                    <span v-else-if="item.type === 'pileInput'">
                      <PileInput v-model="form[item.prop]" />
                    </span>
                    <span v-else-if="item.type === 'inputNumber'">
                      <el-input-number
                        v-model="form[item.prop]"
                        style="width: 100%"
                        :precision="item.precision"
                        clearable
                      />
                    </span>
                    <span v-else-if="item.type === 'select'">
                      <el-select
                        v-model="form[item.prop]"
                        style="width: 100%"
                        :placeholder="item.placeholder"
                        clearable
                        filterable
                        :disabled="!form[item.disabledFieds]"
                        @change="
                          (val) => {
                            handleSelect(val, item);
                          }
                        "
                      >
                        <el-option
                          v-for="v in item.options"
                          :key="v[item.optionValue]"
                          :label="v[item.optionLabel]"
                          :value="v[item.optionValue]"
                        />
                      </el-select>
                    </span>
                    <span v-else-if="item.type === 'selectTree'">
                      <SelectTree
                        v-model="form[item.prop]"
                        :dept-type="item.deptType"
                        placeholder="请选择"
                      />
                    </span>
                    <span v-else-if="item.type === 'CascaderRegion'">
                      <CascaderRegion
                        v-model="form[item.prop]"
                        :deep="1"
                        @input="
                          (value) => {
                            form[item.prop] = value;
                          }
                        "
                      />
                    </span>
                    <span v-else-if="item.type === 'dictSelect'">
                      <el-select
                        v-model="form[item.prop]"
                        style="width: 100%"
                        :multiple="item.multiple"
                        placeholder="请选择"
                        :disabled="item.disabledFieds"
                        clearable
                      >
                        <el-option
                          v-for="i in dict.type[item.dict]"
                          :key="i.value"
                          :label="i.label"
                          :value="i.value"
                        />
                      </el-select>
                    </span>

                    <span v-else-if="item.type === 'multiDictSelect'">
                      <MultiDictSelect
                        v-model="form[item.prop]"
                        :disabled="item.disabledFieds"
                        :multiple="item.multiple"
                        :options="dict.type[item.dict]"/>
                    </span>
                    <span
                      v-else-if="item.type === 'date' || item.type === 'year'"
                    >
                      <el-date-picker
                        v-model="form[item.prop]"
                        style="width: 100%"
                        :type="item.type"
                        :placeholder="item.placeholder"
                        clearable
                        :picker-options="pickerOptions"
                        :value-format="
                          item.type === 'year' ? 'yyyy' : 'yyyy-MM-dd'
                        "
                      />
                    </span>
                    <span v-else-if="item.type === 'tree'">
                      <select-tree
                        style="width: 100%"
                        v-model="form[item.prop]"
                        clearable
                      />
                    </span>
                    <!--                    <span v-else-if="item.type === 'coordinate'">-->
                    <!--                      <el-input-->
                    <!--                        v-model="form[item.prop]"-->
                    <!--                        type="number"-->
                    <!--                        :placeholder="item.placeholder"-->
                    <!--                      >-->
                    <!--                        <template slot="prepend">{{ item.prepend }}</template>-->
                    <!--                      </el-input>-->
                    <!--                    </span>-->

                    <span v-else-if="item.type === 'coordinate'">
                      <lon-lat
                        :type="item.prepend"
                        :lon.sync="form[item.propLon]"
                        :lat.sync="form[item.propLat]"
                      />
                    </span>
                    <span v-else-if="item.type === 'roadType'">
                      <SectionSelect
                        :style="forView ? 'pointer-events: none' : ''"
                        v-model="form[item.prop]"
                        :formObject="form"
                        :sectionId="form.maintenanceSectionId"
                        :disabled="!form.maintenanceSectionId"
                        clearable
                      />
                    </span>
                  </el-form-item>
                </el-col>
              </div>
            </el-form>
          </div>
        </el-tab-pane>
        <el-tab-pane label="结构技术数据" name="technology">
          <div style="height: 60vh; overflow-y: auto; padding: 0 10px 0 5px">
            <el-form
              ref="technology"
              :model="form"
              label-width="195px"
              :disabled="forView ? true : false"
            >
              <div style="display: flex; flex-wrap: wrap">
                <el-col
                  v-for="(item, index) in technologyFields"
                  :key="index"
                  :span="12"
                >
                  <el-form-item
                    :label="item.label"
                    :prop="item.prop"
                    :rules="item.rules"
                  >
                    <span v-if="item.type === 'input'">
                      <el-input
                        v-model="form[item.prop]"
                        :placeholder="item.placeholder"
                        clearable
                      />
                    </span>
                    <span v-else-if="item.type === 'inputNumber'">
                      <el-input-number
                        v-model="form[item.prop]"
                        style="width: 100%"
                        :precision="item.precision"
                      />
                    </span>
                    <span v-else-if="item.type === 'dictSelect'">
                      <el-select
                        v-model="form[item.prop]"
                        style="width: 100%"
                        placeholder="请选择"
                        clearable
                      >
                        <el-option
                          v-for="i in dict.type[item.dict]"
                          :key="i.value"
                          :label="i.label"
                          :value="i.value"
                        />
                      </el-select>
                    </span>
                  </el-form-item>
                </el-col>
              </div>
            </el-form>
          </div>
        </el-tab-pane>
        <el-tab-pane label="结构信息数据" name="information">
          <div style="height: 60vh; overflow-y: auto; padding: 0 10px 0 5px">
            <el-form
              ref="information"
              :model="form"
              label-width="180px"
              :rules="rules"
              :disabled="forView ? true : false"
            >
              <div style="display: flex; flex-wrap: wrap">
                <el-col
                  v-for="(item, index) in informationFields"
                  :key="index"
                  :span="12"
                >
                  <el-form-item
                    :label="item.label"
                    :prop="item.prop"
                    :rules="item.rules"
                  >
                    <span v-if="item.type === 'input'">
                      <el-input
                        v-model="form[item.prop]"
                        :placeholder="item.placeholder"
                        clearable
                      />
                    </span>
                    <span v-else-if="item.type === 'inputNumber'">
                      <el-input-number
                        v-model="form[item.prop]"
                        style="width: 100%"
                        :precision="item.precision"
                        clearable
                      />
                    </span>
                    <span v-else-if="item.type === 'dictSelect'">
                      <el-select
                        v-model="form[item.prop]"
                        style="width: 100%"
                        placeholder="请选择"
                        clearable
                        :multiple="item.multiple"
                      >
                        <el-option
                          v-for="i in dict.type[item.dict]"
                          :key="i.value"
                          :label="i.label"
                          :value="i.value"
                        />
                      </el-select>
                    </span>
                  </el-form-item>
                </el-col>
              </div>
            </el-form>
          </div>
        </el-tab-pane>
        <el-tab-pane label="桥梁照片" name="images">
          <div style="height: 60vh; overflow-y: auto; padding: 0 10px 0 5px">
            <el-form ref="images" :model="form" label-width="90px">
              <el-col
                v-for="(item, index) in imagesFields"
                :key="index"
                :span="8"
              >
                <el-form-item :label="item.label" :prop="item.prop">
                  <span v-if="forView && !form[item.prop]">无</span>
                  <span v-else>
                    <ImageUpload
                      v-if="!forView"
                      :key="item.ownerId"
                      v-model="form[item.prop]"
                      :limit="1"
                      :owner-id="item.ownerId"
                      storage-path="/base/bridge/baseInfo"
                    />
                    <ImagePreview
                      v-else
                      :owner-id="form[item.prop]"
                      width="146px"
                      height="146px"
                    />
                  </span>
                </el-form-item>
              </el-col>
            </el-form>
          </div>
        </el-tab-pane>
        <el-tab-pane label="档案资料" name="archives">
          <!--          <div style="height: 60vh;overflow-y: auto;padding: 0 10px 0 5px;">-->
          <!--            <el-form-->
          <!--              ref="archives"-->
          <!--              :model="form"-->
          <!--              label-width="7vw"-->
          <!--            >-->
          <!--              <div style="display: flex; flex-wrap: wrap;">-->
          <!--                <el-col-->
          <!--                  v-for="(item,index) in archivesFields"-->
          <!--                  :key="index"-->
          <!--                  :span="12"-->
          <!--                >-->
          <!--                  <el-form-item-->
          <!--                    :label="item.label"-->
          <!--                    :prop="item.prop"-->
          <!--                  >-->
          <!--                    <span v-if="forView && !form[item.prop]">无</span>-->
          <!--                    <span v-else>-->
          <!--                      <FileUpload-->
          <!--                        :key="item.ownerId"-->
          <!--                        v-model="form[item.prop]"-->
          <!--                        :limit="1"-->
          <!--                        :owner-id="item.ownerId"-->
<!--          platform="mpkj"-->
          <!--                        storage-path="/base/bridge/baseInfo"-->
          <!--                        :forView="forView"-->
          <!--                      />-->
          <!--                    </span>-->
          <!--                  </el-form-item>-->
          <!--                </el-col>-->
          <!--              </div>-->
          <!--            </el-form>-->
          <!--          </div>-->
          <div style="height: 60vh; overflow-y: auto; padding: 0 10px 0 5px">
            <el-form
              ref="archives"
              :model="form"
              label-width="190px"
              :disabled="forView ? true : false"
            >
              <div style="display: flex; flex-wrap: wrap">
                <el-col
                  v-for="(item, index) in archivesFields"
                  :key="index"
                  :span="12"
                >
                  <el-form-item
                    :label="item.label"
                    :prop="item.prop"
                    :rules="item.rules"
                  >
                    <span v-if="item.type === 'input'">
                      <el-input
                        v-model="form[item.prop]"
                        :placeholder="item.placeholder"
                        clearable
                      />
                    </span>

                    <span v-else-if="item.type === 'dictSelect'">
                      <el-select
                        v-model="form[item.prop]"
                        style="width: 100%"
                        placeholder="请选择"
                        clearable
                      >
                        <el-option
                          v-for="i in dict.type[item.dict]"
                          :key="i.value"
                          :label="i.label"
                          :value="i.value"
                        />
                      </el-select>
                    </span>
                    <span
                      v-else-if="item.type === 'date' || item.type === 'year'"
                    >
                      <el-date-picker
                        v-model="form[item.prop]"
                        style="width: 100%"
                        :type="item.type"
                        :placeholder="item.placeholder"
                        clearable
                        :value-format="
                          item.type === 'year' ? 'yyyy' : 'yyyy-MM-dd'
                        "
                      />
                    </span>
                  </el-form-item>
                </el-col>
              </div>
            </el-form>
          </div>
        </el-tab-pane>
        <el-tab-pane label="涉水信息" name="wade">
          <div style="height: 60vh; overflow-y: auto; padding: 0 10px 0 5px">
            <el-form
              ref="wade"
              :model="form"
              label-width="225px"
              :disabled="forView ? true : false"
            >
              <div style="display: flex; flex-wrap: wrap">
                <el-col
                  v-for="(item, index) in wadeFields"
                  :key="index"
                  :span="12"
                >
                  <el-form-item
                    :label="item.label"
                    :prop="item.prop"
                    :rules="item.rules"
                  >
                    <span v-if="item.type === 'input'">
                      <el-input
                        v-model="form[item.prop]"
                        :placeholder="item.placeholder"
                        clearable
                      />
                    </span>
                    <span v-else-if="item.type === 'dictSelect'">
                      <el-select
                        v-model="form[item.prop]"
                        style="width: 100%"
                        placeholder="请选择"
                        clearable
                      >
                        <el-option
                          v-for="i in dict.type[item.dict]"
                          :key="i.value"
                          :label="i.label"
                          :value="i.value"
                        />
                      </el-select>
                    </span>
                    <span v-else-if="item.type === 'date' || item.type === 'year'" >
                      <el-date-picker
                        v-model="form[item.prop]"
                        style="width: 100%"
                        :type="item.type"
                        :placeholder="item.placeholder"
                        clearable
                        :picker-options="pickerOptions"
                        :value-format="item.type === 'year' ? 'yyyy' : 'yyyy-MM-dd'"
                      />
                    </span>
                  </el-form-item>
                </el-col>
              </div>
            </el-form>
          </div>
        </el-tab-pane>
        <el-tab-pane label="其他数据" name="other">
          <div style="height: 60vh; overflow-y: auto; padding: 0 10px 0 5px">
            <el-form
              ref="other"
              :model="form"
              label-width="210px"
              :disabled="forView ? true : false"
            >
              <el-row>
                <div style="display: flex; flex-wrap: wrap">
                  <el-col
                    v-for="(item, index) in otherFields"
                    :key="index"
                    :span="item.span ? item.span : 12"
                    v-if="!item.forView || forView"
                  >
                    <el-form-item
                      :label="item.label"
                      :prop="item.prop"
                      :rules="item.rules"
                    >
                      <span v-if="item.type === 'input'">
                        <el-input
                          v-model="form[item.prop]"
                          :placeholder="item.placeholder"
                          clearable
                        />
                      </span>
                      <span v-else-if="item.type === 'inputTextarea'">
                        <el-input
                          v-model="form[item.prop]"
                          autosize
                          :placeholder="item.placeholder"
                          type="textarea"
                          clearable
                        />
                      </span>
                      <span v-else-if="item.type === 'pileInput'">
                        <PileInput v-model="form[item.prop]" />
                      </span>
                      <span v-else-if="item.type === 'inputNumber'">
                        <el-input-number
                          v-model="form[item.prop]"
                          style="width: 100%"
                          :precision="item.precision"
                          clearable
                        />
                      </span>
                      <span v-else-if="item.type === 'dictSelect'">
                        <el-select
                          v-model="form[item.prop]"
                          style="width: 100%"
                          placeholder="请选择"
                          clearable
                        >
                          <el-option
                            v-for="i in dict.type[item.dict]"
                            :key="i.value"
                            :label="i.label"
                            :value="i.value"
                          />
                        </el-select>
                      </span>
                      <span
                        v-else-if="item.type === 'date' || item.type === 'year'"
                      >
                        <el-date-picker
                          v-model="form[item.prop]"
                          style="width: 100%"
                          :type="item.type"
                          :placeholder="item.placeholder"
                          clearable
                          :value-format="
                            item.type === 'year' ? 'yyyy' : 'yyyy-MM-dd'
                          "
                        />
                      </span>
                    </el-form-item>
                  </el-col>
                </div>
              </el-row>
            </el-form>
          </div>
        </el-tab-pane>
      </el-tabs>
      <div slot="footer">
        <el-button
          v-if="!forView"
          type="primary"
          @click="handleSubmit('submit')"
          >提 交</el-button
        >
        <el-button
          v-if="(formData.id ? formData.status == 1 : true) && !forView"
          type="primary"
          plain
          @click="handleSubmit('save')"
          >暂 存</el-button
        >
        <el-button @click="handleClose">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import formFields from "./js/formFields";
import dicts from "./js/dicts";
import PileInput from "@/components/PileInput/index.vue";
import SectionSelect from "@/components/SectionSelect";
import SelectTree from "@/components/DeptTmpl/selectTree";
import ManageSelectTree from "@/components/manageSelectTree/index.vue";
import CascaderRegion from "@/views/baseData/components/CascaderRegion";
import lonLat from "@/components/mapPosition/lonLat.vue";
import { listMaintenanceSectionAll } from "@/api/system/maintenanceSection";
import { listByMaintenanceSectionId } from "@/api/baseData/common/routeLine";
import { createIdWorker } from "@/api/baseData/common";
import {
  addStatic,
  tempStatic,
  updateStatic,
} from "@/api/baseData/bridge/baseInfo/index";
import MultiDictSelect from "@/views/baseData/components/MultiDictSelect/index.vue";

export default {
  name: "bridge-form",
  props: {
    formData: { type: undefined, default: () => {} },
    title: { type: String, default: "新增桥梁静态数据" },
    showForm: { type: Boolean, default: false },
    forView: { type: Boolean, default: false },
  },
  
  components: {
    MultiDictSelect,
    PileInput,
    SectionSelect,
    SelectTree,
    CascaderRegion,
    lonLat,
    ManageSelectTree,
  },
  dicts: dicts,
  data() {
    return {
      loading: false,
      activeName: "administrative",
      rules: {
        spanGroups:[
          { required: true, message: '请输入跨径组合', trigger: 'blur' },
          //^(\d+)\*(\d+(\.\d{1,3})?)(\+(\d+)\*(\d+(\.\d{1,3})?))*$
          { pattern:/^(\d+)\*(\d+(\.\d{1,3})?)(\+(\d+)\*(\d+(\.\d{1,3})?))*$/, message: '边跨+主跨+边跨格式，例如：3*80.123+4*120+2*60', trigger: 'blur' },
        ]
      },
      form: {
        managementMaintenanceId: "",
        managementMaintenanceBranchId: "",
        pierType: [],
      },
      administrativeFields: [],
      technologyFields: [],
      informationFields: [],
      imagesFields: [],
      archivesFields: [],
      otherFields: [],
      wadeFields: [],
      pickerOptions: {
        disabledDate(v) {
          return v.getTime() > new Date().getTime() 
        }
      },
    };
  },
  created() {
    this.init();
  },
  methods: {
    init() {
      this.administrativeFields = JSON.parse(
        JSON.stringify(formFields.administrative)
      );
      this.technologyFields = JSON.parse(JSON.stringify(formFields.technology));
      this.informationFields = JSON.parse(
        JSON.stringify(formFields.information)
      );
      this.imagesFields = JSON.parse(JSON.stringify(formFields.images));
      this.archivesFields = JSON.parse(JSON.stringify(formFields.archives));
      this.wadeFields = JSON.parse(JSON.stringify(formFields.wade));
      this.otherFields = JSON.parse(JSON.stringify(formFields.other));
      // 编辑和查看时
      if (this.formData?.id) {
        const data = JSON.parse(JSON.stringify(this.formData));
        data.pierType = data.pierType ? data.pierType.split(",") : [];
        data.abutmentType = data.abutmentType
          ? data.abutmentType.split(",")
          : "";
        data.bearingType = data.bearingType ? data.bearingType.split(",") : "";
        data.expansionJointType = data.expansionJointType
          ? data.expansionJointType.split(",")
          : "";
        data.routeLevel = data.routeLevel ? data.routeLevel.split(",") : "";
        this.form = data;
      }
    },
    // tabs点击操作
    handleClick() {
      let arr = [];
      switch (this.activeName) {
        case "images":
          arr = this.imagesFields;
          break;
        case "archives":
          arr = this.archivesFields;
          break;
      }
      if (arr.length > 0) {
        arr.forEach(async (el) => {
          if (el.ownerId) return;
          try {
            let res = await createIdWorker();
            if (res.code === 200) el.ownerId = Number(res.data);
            this.$forceUpdate();
          } catch (error) {}
        });
      }
    },
    // 选择框事件
    handleSelect(e, i) {
      switch (i.prop) {
        case "routeCode":
          if (e) {
            this.administrativeFields.forEach((el) => {
              if (el.prop === "routeName") {
                const option = el.options.find((i) => i.routeCode === e);
                if (option) {
                  this.form.routeId = option.routeId;
                  this.form.routeName = option.routeName;
                }
              }
            });
          } else {
            this.form.routeId = "";
            this.form.routeName = "";
          }
          break;
        case "routeName":
          if (e) {
            this.administrativeFields.forEach((el) => {
              if (el.prop === "routeCode") {
                const option = el.options.find((i) => i.routeName === e);
                if (option) {
                  this.form.routeId = option.routeId;
                }
              }
            });
          } else {
            this.form.routeId = "";
            this.form.routeCode = "";
          }
          break;
      }
    },
    // 监听选中管理处
    deptChange(e) {
      if (!e) return;
      listMaintenanceSectionAll({departmentIdList:[this.form.managementMaintenanceBranchId]}).then((res) => {
        if (res.code == 200) {
          this.administrativeFields.forEach((el) => {
            if (el.prop === "maintenanceSectionId") {
              el.options = res.data;
            }
          });
        }
      });
    },
    // 监听选中养护路段
    maintenanceSectionChange(e) {
      if (!e) return;
      this.administrativeFields.forEach((el) => {
        if (el.prop === "maintenanceSectionId") {
          //el.options中maintenanceSectionName等于e的routeGrade

          const option = el.options.find((i) => i.maintenanceSectionId === e);
          if (option && option.routeGrade) {
            this.form.routeLevel = option.routeGrade.split(",");
          }
        }
      });

      listByMaintenanceSectionId({ maintenanceSectionId: e }).then((res) => {
        if (res.code == 200) {
          this.administrativeFields.forEach((el) => {
            if (el.prop === "routeCode" || el.prop === "routeName") {
              el.options = res.data;
            }
          });
        }
      });
    },
    // 提交和暂存操作
    async handleSubmit(type) {
      const formNames = [
        "administrative",
        "technology",
        "information",
        "images",
        "archives",
        "other",
      ];
      switch (type) {
        case "submit":
          this.form.status = 2;
          break;
        case "save":
          this.form.status = 1;
          break;
      }
      if (this.form.status === 2) {
        for (let index = 0; index < formNames.length; index++) {
          const element = formNames[index];
          const valid = await new Promise((resolve) => {
            this.$refs[element].validate((valid) => {
              resolve(valid);
            });
          });
          if (!valid) {
            // 如果表单校验不通过，定位到对应的tab
            this.activeName = element;
            return; // 中止提交操作
          }
        }
      }
      for (const key in this.form) {
        if (Array.isArray(this.form[key])) {
          this.form[key] = this.form[key].join(",");
        }
      }
      this.loading = true;
      if (this.form.id != null) {
          const api = this.form.status === 1 ? tempStatic : updateStatic;
          api(this.form)
          .then((response) => {
            this.$modal.msgSuccess(this.form.status === 1 ? "暂存成功" : "修改成功");
            this.$emit("refresh");
          })
          .catch(() => {
            this.loading = false;
          });
      } else {
        const api = this.form.status === 1 ? tempStatic : addStatic;
        api(this.form)
          .then((response) => {
            this.$modal.msgSuccess(
              this.form.status === 1 ? "暂存成功" : "新增成功"
            );
            this.$emit("refresh");
          })
          .catch(() => {
            this.loading = false;
          });
      }
    },
    // 关闭dialog
    handleClose() {
      if (this.forView) {
        this.form = {};
        this.$emit("close", false);
      } else {
        this.$modal
          .confirm("确认退出？")
          .then(() => {
            this.form = {};
            this.$emit("close", false);
          })
          .catch(() => {});
      }
    },
  },
  computed: {},
  watch: {
    "form.managementMaintenanceId"(newVal, oldVal) {
      // if (newVal) {
      //   this.deptChange(newVal);
      // }
      // if (oldVal && this.form.maintenanceSectionId) {
      //   this.form.maintenanceSectionId = "";
      //   this.form.routeCode = "";
      // }
    },
    'form.managementMaintenanceBranchId'(newVal, oldVal) {
      if (newVal) {
        this.deptChange(newVal)
      }
      if (oldVal) {
        if(this.form.maintenanceSectionId)this.form.maintenanceSectionId=''
        if(this.form.routeCode)this.form.routeCode=''
      }
    },
    "form.maintenanceSectionId"(newVal, oldVal) {
      if (newVal) {
        this.maintenanceSectionChange(newVal);
      }

      if (oldVal && this.form.routeCode) {
        this.form.routeCode = "";
      }
    },
  },
};
</script>

<style lang="scss" scoped>
@import "~@/assets/styles/el-tabs.scss";
.forView ::v-deep .el-input.is-disabled .el-input__inner {
  background-color: white;
  border-color: #dfe4ed;
  color: #1d2129;
}

::v-deep .el-textarea.is-disabled .el-textarea__inner {
  background-color: white;
  border-color: #dfe4ed;
  color: #1d2129;
}
::v-deep .el-dialog__header {
  border-bottom: 0;
  padding: 20px 30px 0 30px !important;
}

.longitude-latitude {
  ::v-deep {
    .el-input-group__prepend {
      padding: 0 12px;
    }
    .el-input__inner {
      padding: 0 5px;
    }
  }
}
</style>
