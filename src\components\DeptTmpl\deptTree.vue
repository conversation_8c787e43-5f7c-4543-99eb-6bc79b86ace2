

<template>
   <el-tree :data="deptOptions"  :props="defaultProps" @node-click="handleNodeClick"></el-tree>
</template>

<script>
import { deptTreeSelect } from "@/api/tmpl";

export default {
  data() {
    return {
      deptOptions:null,
      data: [{
        label: '一级 1',
        children: [{
          label: '二级 1-1',
          children: [{
            label: '三级 1-1-1'
          }]
        }]
      }, {
        label: '一级 2',
        children: [{
          label: '二级 2-1',
          children: [{
            label: '三级 2-1-1'
          }]
        }, {
          label: '二级 2-2',
          children: [{
            label: '三级 2-2-1'
          }]
        }]
      }, {
        label: '一级 3',
        children: [{
          label: '二级 3-1',
          children: [{
            label: '三级 3-1-1'
          }]
        }, {
          label: '二级 3-2',
          children: [{
            label: '三级 3-2-1'
          }]
        }]
      }],
      defaultProps: {
        children: 'children',
        label: 'label'
      }
    };
  },
  props: {
    deptType: Number,
  },
  created() {

    this.getDept();
  },
  methods: {
    /** 查询部门列表 */
    getDept() {
      var deptType ={types:this.deptType};
      deptTreeSelect(deptType).then(response => {
        this.deptOptions = response.data;
      });
    },
    handleNodeClick(data) {
      console.log(data);
    }
  }
};
</script>

<style scoped lang="scss">

</style>
