<template>
  <PageContainer>
    <template slot="search">
      <div style="display: flex; align-items: center; margin: 0">
        <CascadeSelection
          :form-data="queryParams"
          v-model="queryParams"
          types="201"
          :showCodes="false"
          multiple
        />
        <rangeInput
          :clearData="clearData"
          :startPlaceholder="'统一里程起始桩号'"
          :endPlaceholder="'统一里程终点桩号'"
          @startValue="
            (v) => {
              queryParams.unifiedMileageStartStake = v;
            }
          "
          @endValue="
            (v) => {
              queryParams.unifiedMileageEndStake = v;
            }
          "
        />
        <el-select
          style="margin-right: 20px"
          v-model="queryParams.status"
          placeholder="数据状态"
          clearable
        >
          <el-option
            v-for="dict in dict.type.base_data_state"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
        <div style="min-width: 220px; height: 32px">
          <el-button
            v-hasPermi="['baseData:longitudinalSlope:listPage']"
            type="primary"
            icon="el-icon-search"
            @click="handleQuery"
            >搜索</el-button
          >
          <el-button icon="el-icon-refresh" @click="resetQuery">重置</el-button>
        </div>
      </div>
    </template>

    <template slot="header">
      <div class="button-list">
        <el-button
          v-hasPermi="['baseData:longitudinalSlope:add']"
          type="primary"
          @click="handleAdd"
          >新增</el-button
        >
        <el-button
          v-hasPermi="['baseData:longitudinalSlope:edit']"
          type="primary"
          @click="handleUpdate"
          >编辑</el-button
        >
        <el-button
          v-hasPermi="['baseData:longitudinalSlope:remove']"
          type="primary"
          @click="handleDelete"
          >删除</el-button
        >
        <el-button
          v-hasPermi="['baseData:longitudinalSlope:query']"
          type="primary"
          @click="handleView"
          >查看</el-button
        >

        <el-button
          v-hasPermi="['baseData:longitudinalSlope:export']"
          type="primary"
          @click="exportList"
          >导出数据</el-button
        >
        <!-- <el-button
          type="primary"
          @click="changeStatus"
        >运营状态变更</el-button> -->
        <!-- <el-button
          type="primary"
          @click="downloadQrcode"
        >二维码下载</el-button> -->
      </div>
    </template>

    <template slot="body">
      <el-table
        v-adjust-table
        v-loading="loading"
        ref="table"
        height="100%"
        style="width: 100%"
        border
        :data="tableData"
        :row-style="rowStyle"
        @selection-change="handleSelectionChange"
        @row-click="handleRowClick"
      >
        <el-table-column type="selection" width="50" align="center" />
        <el-table-column
          fixed
          label="序号"
          type="index"
          width="50"
          align="center"
        >
          <template v-slot="scope">
            {{
              scope.$index +
              (queryParams.pageNum - 1) * queryParams.pageSize +
              1
            }}
          </template>
        </el-table-column>
        <el-table-column
          fixed
          label="管理处"
          align="center"
          prop="managementMaintenanceName"
          min-width="120"
          show-overflow-tooltip
        />
        <el-table-column
          fixed
          label="管养分处"
          align="center"
          prop="managementMaintenanceBranchName"
          min-width="120"
          show-overflow-tooltip
        />
        <el-table-column
          label="养护路段"
          align="center"
          prop="maintenanceSectionName"
          min-width="120"
          show-overflow-tooltip
        />
        <el-table-column
          label="路线编码"
          align="center"
          prop="routeCode"
          min-width="120"
          show-overflow-tooltip
        />
        <el-table-column
          label="路线名称"
          align="center"
          prop="routeName"
          min-width="120"
          show-overflow-tooltip
        />
        <el-table-column
          label="数据状态"
          align="center"
          prop="status"
          min-width="120"
        >
          <template slot-scope="scope">
            <el-link
              :type="{ 1: 'info', 2: 'success' }[scope.row.status]"
              :underline="false"
            >
              <DictTag
                :value="scope.row.status"
                :options="dict.type.base_data_state"
              />
            </el-link>
          </template>
        </el-table-column>
        <!-- <el-table-column
          label="运营状态"
          align="center"
          prop="operationState"
          min-width="130"
          show-overflow-tooltip
        >
          <template slot-scope="scope">
            <el-button
              type="text"
              @click="handleOperational($event,scope.row)"
            >
              <DictTag
                :value="scope.row.operationState"
                :options="dict.type.sys_operation_state"
              />
            </el-button>
          </template>
        </el-table-column> -->
        <el-table-column label="施工里程" align="center">
          <el-table-column
            prop="constructionStartStake"
            label="起点桩号"
            min-width="120"
            align="center"
          >
            <template slot-scope="scope">
              <span v-if="scope.row && scope.row.constructionStartStake">
                {{ formatPile(scope.row.constructionStartStake) }}
              </span>
            </template>
          </el-table-column>
          <el-table-column
            prop="constructionEndStake"
            label="终点桩号"
            min-width="120"
            align="center"
          >
            <template slot-scope="scope">
              <span v-if="scope.row && scope.row.constructionEndStake">
                {{ formatPile(scope.row.constructionEndStake) }}
              </span>
            </template>
          </el-table-column>
        </el-table-column>
        <el-table-column label="统一里程" align="center">
          <el-table-column
            prop="unifiedMileageStartStake"
            label="起点桩号"
            min-width="120"
            align="center"
          >
            <template slot-scope="scope">
              <span v-if="scope.row && scope.row.unifiedMileageStartStake">
                {{ formatPile(scope.row.unifiedMileageStartStake) }}
              </span>
            </template>
          </el-table-column>
          <el-table-column
            prop="unifiedMileageEndStake"
            label="终点桩号"
            min-width="120"
            align="center"
          >
            <template slot-scope="scope">
              <span v-if="scope.row && scope.row.unifiedMileageEndStake">
                {{ formatPile(scope.row.unifiedMileageEndStake) }}
              </span>
            </template>
          </el-table-column>
        </el-table-column>
        <el-table-column label="国高网" align="center">
          <el-table-column
            prop="nationalNetworkStartStake"
            label="起点桩号"
            min-width="120"
            align="center"
          >
            <template slot-scope="scope">
              <span v-if="scope.row && scope.row.nationalNetworkStartStake">
                {{ formatPile(scope.row.nationalNetworkStartStake) }}
              </span>
            </template>
          </el-table-column>
          <el-table-column
            prop="nationalNetworkEndStake"
            label="终点桩号"
            min-width="120"
            align="center"
          >
            <template slot-scope="scope">
              <span v-if="scope.row && scope.row.nationalNetworkEndStake">
                {{ formatPile(scope.row.nationalNetworkEndStake) }}
              </span>
            </template>
          </el-table-column>
        </el-table-column>

        <el-table-column
          label="位置"
          align="center"
          prop="location"
          min-width="120"
          show-overflow-tooltip
        />
        <el-table-column
          label="最大纵坡"
          align="center"
          prop="maximumLongitudinalSlope"
          min-width="120"
          show-overflow-tooltip
        />
        <el-table-column
          label="相对高差(m)"
          align="center"
          prop="relativeHeightDifference"
          min-width="120"
          show-overflow-tooltip
        />
        <el-table-column
          label="连续坡长(km)"
          align="center"
          prop="continuousSlopeLength"
          min-width="120"
          show-overflow-tooltip
        />
        <el-table-column
          label="坡长(m)"
          align="center"
          prop="slopeLength"
          min-width="120"
          show-overflow-tooltip
        />
      </el-table>
      <pagination
        :total="total"
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
        :pageSizes="[10, 20, 30, 50, 100, 1000]"
        @pagination="getList"
      />
    </template>
    <Form
      v-if="showAddEdit"
      :forView="forView"
      :formData="formData"
      :title="title"
      :showAddEdit="showAddEdit"
      @close="
        () => {
          showAddEdit = false;
          formData = {};
        }
      "
      @refresh="
        () => {
          showAddEdit = false;
          formData = {};
          getList();
        }
      "
    />
    <Protections
      v-if="showDetail"
      :showDetail="showDetail"
      :slopeId="slopeId"
      :formData="formData"
      @close="
        () => {
          showDetail = false;
          formData = {};
        }
      "
    />
    <ImportData
      v-if="showImport"
      :is-update="false"
      :dialog-visible="showImport"
      :import-base-type="'11'"
      :import-type="2"
      @close="closeImport"
    />
  </PageContainer>
</template>

<script>
import {
  getListPage,
  getSlope,
  delSlope,
} from "@/api/baseData/road/slope/index.js";
import Form from "./form.vue";
import CascadeSelection from "@/components/CascadeSelection/index.vue";
import rangeInput from "@/views/baseData/components/rangeInput/index.vue";
import ImportData from "@/views/baseData/components/importData/index.vue";
import { statusDialog } from "@/views/baseData/components/statusDialog/index.js";
import { statusListDialog } from "@/views/baseData/components/statusDialog/list.js";

export default {
  name: "Slope",
  components: {
    Form,
    CascadeSelection,
    rangeInput,
    ImportData,
  },
  dicts: ["sys_route_type", "sys_operation_state", "base_data_state"],
  data() {
    return {
      loading: true,
      showAddEdit: false,
      forView: false,
      clearData: false,
      title: "",
      formData: {},
      ids: [],
      total: 0,
      tableData: [],
      queryParams: {
        pageNum: 1,
        pageSize: 20,
      },
      showDetail: false,
      slopeId: "",
      showImport: false,
    };
  },
  watch: {},
  created() {
    this.getList();
  },
  methods: {
    // 获取表格数据
    getList() {
      this.loading = true;
      getListPage(this.queryParams)
        .then((response) => {
          this.tableData = response.rows;
          this.total = response.total;
          this.loading = false;
          this.clearData = false;
        })
        .catch(() => {
          this.loading = false;
        });
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id);
    },
    // 表格点击勾选
    handleRowClick(row) {
      row.isSelected = !row.isSelected;
      this.$refs.table.toggleRowSelection(row);
    },
    // 勾选高亮
    rowStyle({ row, rowIndex }) {
      if (this.ids.includes(row.id)) {
        return { "background-color": "#b7daff", color: "#333" };
      } else {
        return { "background-color": "#fff", color: "#333" };
      }
    },
    // 搜索按钮
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    // 重置按钮
    resetQuery() {
      this.clearData = true;
      this.queryParams = {
        pageNum: 1,
        pageSize: 20,
      };
      this.handleQuery();
    },
    // 新增按钮操作
    handleAdd() {
      this.forView = false;
      this.showAddEdit = true;
      this.formData = {};
      this.title = "新增纵坡数据";
    },
    // 编辑按钮
    handleUpdate() {
      if (this.ids.length != 1) {
        this.$message.warning("请选择一条数据进行编辑！");
        return;
      } else {
        getSlope(this.ids[0]).then((res) => {
          if (res.code === 200) {
            this.formData = res.data;
            this.forView = false;
            this.showAddEdit = true;
            this.title = "编辑纵坡数据";
          }
        });
      }
    },
    // 删除按钮
    handleDelete() {
      if (this.ids.length == 0) {
        this.$message.warning("请选择至少一条数据进行删除！");
        return;
      }
      this.$modal
        .confirm("确认删除？")
        .then(() => {
          delSlope(this.ids.join()).then((res) => {
            if (res && res.code == "200") {
              this.getList();
              this.$modal.msgSuccess("删除成功");
            }
          });
        })
        .catch(() => {});
    },
    // 查看按钮
    handleView() {
      if (this.ids.length != 1) {
        this.$message.warning("请选择一条数据！");
        return;
      } else {
        getSlope(this.ids[0]).then((res) => {
          if (res.code === 200) {
            this.formData = res.data;
            this.forView = true;
            this.showAddEdit = true;
            this.title = "查看纵坡数据";
          }
        });
      }
    },
    // 导出按钮
    exportList() {
      if (this.ids.length === 0) {
        this.$modal
          .confirm("即将导出所有表格数据，此过程可能花费时间较长，是否继续？")
          .then(() => {
            this.download(
              "/baseData/longitudinal/slope/export",
              this.queryParams,
              `slope_${new Date().getTime()}.xlsx`,
              {
                headers: { "Content-Type": "application/json;" },
                parameterType: "body",
              }
            );
          })
          .catch(() => {});
      } else {
        this.$modal
          .confirm(`已选择${this.ids.length}条纵坡数据，确认导出？`)
          .then(() => {
            this.download(
              "/baseData/longitudinal/slope/export",
              { ids: this.ids },
              `slope__${new Date().getTime()}.xlsx`,
              {
                headers: { "Content-Type": "application/json;" },
                parameterType: "body",
              }
            );
          })
          .catch(() => {});
      }
    },

    closeImport(v) {
      this.showImport = false;
      if (v) this.getList();
    },
    // 二维码下载
    // downloadQrcode() {
    //   if (this.ids.length === 0) {
    //     this.$modal
    //       .confirm(
    //         '即将下载所有表格的二维码数据，此过程可能花费时间较长，是否继续？'
    //       )
    //       .then(() => {
    //         this.download(
    //           '/baseData/sideslope/basic/genQrCode',
    //           this.queryParams,
    //           `QrCode_${new Date().getTime()}`,
    //           {
    //             headers: { 'Content-Type': 'application/json;' },
    //             parameterType: 'body'
    //           }
    //         )
    //       })
    //       .catch(() => {})
    //   } else {
    //     this.$modal
    //       .confirm(`已选择${this.ids.length}条纵坡数据，是否下载二维码？`)
    //       .then(() => {
    //         let data = {
    //           ...this.queryParams,
    //           ids: this.ids
    //         }
    //         this.download(
    //           '/baseData/sideslope/basic/genQrCode',
    //           data,
    //           `QrCode_${new Date().getTime()}`,
    //           {
    //             headers: { 'Content-Type': 'application/json;' },
    //             parameterType: 'body'
    //           }
    //         )
    //       })
    //       .catch(() => {})
    //   }
    // },
    // 运营状态变更按钮
    changeStatus() {
      if (this.ids.length !== 1) {
        this.$message.warning("请选择一条数据！");
        return;
      } else {
        // baseDataType 基础数据类型 ？纵坡
        statusDialog({ dataId: this.ids[0], baseDataType: 11 }).then(() => {
          this.getList();
        });
      }
    },
    // 表格操作-运营状态
    handleOperational(event, row) {
      event.stopPropagation();
      statusListDialog({ dataId: row.id, baseDataType: 11 });
    },
    // 防护形式详情
    handleDetail(row) {
      this.showDetail = true;
      this.slopeId = row.id;
      this.formData = row;
    },
  },
};
</script>

<style lang="scss" scoped>

::v-deep .el-table .el-table__fixed-body-wrapper{
    top: 72px !important;
}

.button-list {
  border-radius: 4px;
  width: 100%;
  .el-button {
    margin-bottom: 10px;
    margin-right: 10px;
    margin-left: 0;
  }
}
</style>
