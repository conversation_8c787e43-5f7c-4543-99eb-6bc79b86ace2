<template>
  <div class="road-interflow-edit" style="padding: 20px" v-loading="loading">

      <el-form
        ref="formData"
        :model="formData"
        :rules="rules"
        :disabled="readonly"
        size="medium"
        label-width="130px"
      >

        <el-col :span="24">
          <div class="card_title">涉路工程基本信息</div>
        </el-col>
        <el-row :gutter="24">
        <el-col :span="8">
          <el-form-item label="管养单位" prop="managementUnitId">
            <selectTree
              :key="'domainId'"
              v-model="formData.managementUnitId"
              :deptType="100"
              :deptTypeList="[1, 3, 4, 10]" clearable
              filterable
              only-select-child
              placeholder="管养单位"
              style="width: 100%"
              @change="deptChange(formData.managementUnitId)"
            />
          </el-form-item>
        </el-col>

        <el-col :span="8">
<!--          <el-form-item label="养护路段" prop="maintenanceSectionId" required>-->
<!--            <RoadSection ref="roadSection" v-model="formData.maintenanceSectionId" :deptId="formData.maintenanceUnitId" placeholder="路段"-->
<!--                         style="width: 100%" @change="changeMaiSecId"/>-->
<!--          </el-form-item>-->

          <el-form-item label="养护路段" prop="maintenanceSectionId">
            <el-select
              v-model="formData.maintenanceSectionId"
              filterable
              placeholder="请选养护路段"
              style="width: 100%"
            >
              <el-option
                v-for="item in maintenanceSectionList"
                :key="item.maintenanceSectionId"
                :label="item.maintenanceSectionName"
                :value="item.maintenanceSectionId"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-col>

          <el-col :span="8">
            <el-form-item label="来文名称" prop="documentReceived" required>
              <el-input style="width: 100%"  v-model="formData.documentReceived" placeholder="来文名称" clearable></el-input>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="24">

          <el-col :span="8">
            <el-form-item label="来文单位" prop="reportingUnit" >
              <el-input style="width: 100%"  v-model="formData.reportingUnit" placeholder="来文单位" clearable></el-input>
            </el-form-item>
          </el-col>

          <el-col :span="8">
            <el-form-item label="项目状态" prop="projectStatus" required>
              <el-select v-model="formData.projectStatus"  style="width: 100%"placeholder="请选择项目状态" clearable>
                <el-option
                  v-for="dict in dict.type.engineering_project_status"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </el-form-item>
          </el-col>

          <el-col :span="8">
            <el-form-item label="赔付情况" prop="compensationSituation" required>
              <el-select v-model="formData.compensationSituation" style="width: 100%" placeholder="请选择赔付情况" clearable>
                <el-option
                  v-for="dict in dict.type.engineering_compensation_situation"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </el-form-item>
          </el-col>

        </el-row>

        <el-row :gutter="24">

          <el-col :span="12">
            <el-form-item label="起点桩号" prop="startPoint">
              <el-input v-model="formData.startPoint1"
                        type="number"
                        :min="0"
                        :step="1"
                        :style="{width: '45%'}">
                <template slot="prepend">K</template>
              </el-input>
              <div style="width: 10%;text-align: center;display: inline-block;">+</div>
              <el-input v-model="formData.startPoint2"
                        type="number"
                        :min="0"
                        :style="{width: '45%'}">
              </el-input>
            </el-form-item>
          </el-col>

          <el-col :span="12">
            <el-form-item label="终点桩号" prop="endPoint">
              <el-input v-model="formData.endPoint1"
                        type="number"
                        :min="0"
                        :step="1"
                        :style="{width: '45%'}">
                <template slot="prepend">K</template>
              </el-input>
              <div style="width: 10%;text-align: center;display: inline-block;">+</div>
              <el-input v-model="formData.endPoint2"
                        type="number"
                        :min="0"
                        :style="{width: '45%'}">
              </el-input>
            </el-form-item>
          </el-col>

        </el-row>


        <el-row :gutter="24">
          <el-col :span="8">
            <el-form-item label="方向" prop="direction">
              <el-select v-model="formData.direction" placeholder="请选择方向" clearable style="width: 100%">
                <el-option
                  v-for="dict in dict.type.engineering_direction"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </el-form-item>
          </el-col>

          <el-col :span="8">
            <el-form-item label="来文时间" prop="submissionTime">
              <el-date-picker v-model="formData.submissionTime" type="datetime" format="yyyy-MM-dd" value-format="yyyy-MM-dd"
                              :style="{width: '100%'}" placeholder="请选择拟来文时间" clearable></el-date-picker>
            </el-form-item>
          </el-col>

          <el-col :span="8">
            <el-form-item label="车道" prop="laneList" >
              <el-select v-model="formData.laneList" placeholder="请选择车道" clearable multiple style="width: 100%">
                <el-option
                  v-for="dict in dict.type.engineering_lane"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>

            </el-form-item>
          </el-col>

        </el-row>

        <el-row :gutter="24">

          <el-col :span="8">
            <el-form-item label="涉路施工协议编号" prop="agreementNumber">
              <el-input style="width: 100%"  v-model="formData.agreementNumber" placeholder="请输入涉路施工协议编号"></el-input>
            </el-form-item>
          </el-col>

          <el-col :span="8">
            <el-form-item label="复函文件编码" prop="replyDocumentCode">
              <el-input style="width: 100%"  v-model="formData.replyDocumentCode" placeholder="请输入复函文件编码"></el-input>
            </el-form-item>
          </el-col>

          <el-col :span="8">
            <el-form-item label="复函时间" prop="replyTime">
              <el-date-picker v-model="formData.replyTime" type="datetime" format="yyyy-MM-dd" value-format="yyyy-MM-dd"
                              :style="{width: '100%'}" placeholder="请选择复函时间" clearable></el-date-picker>
            </el-form-item>
          </el-col>

        </el-row>

        <el-row :gutter="24">

          <el-col :span="8">
            <el-form-item label="复函意见" prop="replyOpinions">
              <el-input style="width: 100%"  v-model="formData.replyOpinions" placeholder="请输入复函文件编码"></el-input>
            </el-form-item>
          </el-col>

          <el-col :span="8">
            <el-form-item label="路产协议编号" prop="roadPropertyAgreementNumber">
              <el-input style="width: 100%"  v-model="formData.roadPropertyAgreementNumber" placeholder="请输入路产协议编号"></el-input>
            </el-form-item>
          </el-col>

          <el-col :span="8">
            <el-form-item label="是否交通管制" prop="isTrafficControl">
              <el-select v-model="formData.isTrafficControl" style="width: 100%" placeholder="是否交通管制" clearable   >
                <el-option
                  v-for="dict in dict.type.engineering_is_regulated"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="24">

          <el-col :span="8">
            <el-form-item label="协议金额" prop="agreementAmount">
              <el-input
                style="width: 100%"
                v-model="formData.agreementAmount"
                placeholder="请输入协议金额"
                :rules="[{ required: true, message: '请输入金额', trigger: 'blur' }]"
                @input="(v)=>(formData.agreementAmount=v.replace(/[^\d.]/g,''))"
              ></el-input>
            </el-form-item>
          </el-col>

          <el-col :span="8">
            <el-form-item label="建设单位" prop="constructionUnit">
              <el-input style="width: 100%"  v-model="formData.constructionUnit" placeholder="请输入建设单位"></el-input>
            </el-form-item>
          </el-col>

          <el-col :span="8">
            <el-form-item label="设计单位" prop="designUnit">
              <el-input style="width: 100%"  v-model="formData.designUnit" placeholder="请输入设计单位"></el-input>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="24">
          <el-col :span="8">
            <el-form-item label="施工单位" prop="constructionCompany">
              <el-input style="width: 100%"  v-model="formData.constructionCompany" placeholder="请输入施工单位"></el-input>
            </el-form-item>
          </el-col>

          <el-col :span="8">
            <el-form-item label="监理单位" prop="supervisionUnit">
              <el-input style="width: 100%"  v-model="formData.supervisionUnit" placeholder="请输入监理单位"></el-input>
            </el-form-item>
          </el-col>

          <el-col :span="8">
            <el-form-item label="拟开工日期" prop="plannedStartDate">
              <el-date-picker v-model="formData.plannedStartDate" type="datetime" format="yyyy-MM-dd" value-format="yyyy-MM-dd"
                              :style="{width: '100%'}" placeholder="请选择拟开工日期" clearable></el-date-picker>
            </el-form-item>
          </el-col>

        </el-row>

        <el-row :gutter="24">
          <el-col :span="8">
            <el-form-item label="拟竣工日期" prop="plannedCompletionDate">
              <el-date-picker v-model="formData.plannedCompletionDate" type="datetime" format="yyyy-MM-dd" value-format="yyyy-MM-dd"
                              :style="{width: '100%'}" placeholder="请选择拟竣工日期" clearable></el-date-picker>
            </el-form-item>
          </el-col>

          <el-col :span="8">
            <el-form-item label="联系人" prop="contactPerson">
              <el-input style="width: 100%"  v-model="formData.contactPerson" placeholder="请输入联系人"></el-input>
            </el-form-item>
          </el-col>

          <el-col :span="8">
            <el-form-item label="联系人电话" prop="phoneNumber">
              <el-input style="width: 100%"  v-model="formData.phoneNumber" placeholder="请输入联系人电话"></el-input>
            </el-form-item>
          </el-col>

        </el-row>

        <el-col :span="24">
          <el-form-item label="实施内容" prop="implementationContent">
            <el-input type="textarea" rows="2" v-model="formData.implementationContent" :style="{width: '100%'}" placeholder="实施内容"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="备注" prop="remarks">
            <el-input type="textarea" rows="2" v-model="formData.remarks" :style="{width: '100%'}" placeholder="备注"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="24" class="mt10">
          <div style="text-align: center">
            <el-button type="primary" @click="submitForm">保 存</el-button>
            <el-button @click="close">取消</el-button>
          </div>
        </el-col>
      </el-form>

  </div>
</template>

<script>
import selectTree from "@/components/DeptTmpl/selectTree.vue";
import RoadSection from "@/views/baseData/components/roadSection/index.vue";
import ContractSection from "@/components/ContractSection/index.vue";
import assetSelect from "@/components/AssetSelect/index.vue";
import {addProject, editProject} from "@/api/maintenanceProject/projectManage";
import RouteCodeSection from "@/components/RouteCodeSection/index.vue";
import type from "@geoscene/core/smartMapping/renderers/type";
import {listAllDiseases} from "@/api/patrol/diseases";
import {
  addEngineering,
  delEngineering, findDeptInfo,
  getEngineering,
  selectSectionName,
  updateEngineering
} from "@/api/engineering/engineering";
import {  addEngineeringLane,
  delEngineeringLane
} from "@/api/engineering/engineeringLane";
import Treeselect from "@riophae/vue-treeselect";
import {listMaintenanceSectionAll} from "@/api/system/maintenanceSection";
import { getTreeStruct } from '@/api/tmpl';
import {deptTreeSelect} from "@/api/system/user";
import {getDept} from "@/api/system/dept";
import {listByMaintenanceSectionId} from "@/api/baseData/common/routeLine";
import {listAllData} from "@/api/system/dict/data";
export default {
  name: "Edit",
  computed: {
    type() {
      return type
    }
  },
  dicts: ['engineering_is_regulated', 'engineering_direction', 'engineering_project_status', 'engineering_compensation_situation', 'engineering_lane', 'engineering_is_regulated'],
  components: { RouteCodeSection, assetSelect, ContractSection, RoadSection, selectTree},
  data() {
    return {
      loading: false,
      formData: {
          remarks: '',

      },
      rules: {
        managementUnitId: [
          { required: true, message: '管养单位不能为空', trigger: 'blur' }
        ],
        routeCode: [
          { required: true, message: '路线编码不能为空', trigger: 'blur' }
        ],
        maiSecId: [
          { required: true, message: '路段名称不能为空', trigger: 'blur' }
        ],
        documentReceived: [
          { required: true, message: '来文名称不能为空', trigger: 'blur' }
        ],
        projectStatus: [
          { required: true, message: '项目状态不能为空', trigger: 'blur' }
        ],
        compensationSituation: [
          { required: true, message: '赔付情况不能为空', trigger: 'blur' }
        ],
        laneList: [
          { required: true, message: '车道不能为空', trigger: 'blur' }
        ],

        startPoint: [{ required: true, validator: (rule, value, callback) => {
            if (this.formData.startPoint1 != null && this.formData.startPoint2 != null) {
              callback()
            } else {
              callback(new Error('请完整填写起点桩号'))
            }
          }, trigger: 'blur' }],
        endPoint: [{ required: true, validator: (rule, value, callback) => {
            if (this.formData.endPoint1 != null && this.formData.endPoint2 != null) {
              callback()
            } else {
              callback(new Error('请完整填写起点桩号'))
            }
          }, trigger: 'blur' }]
      },
      editableTabsValue: '',
      libModel: false,
      filterText: '',
      libData: [],
      treeIndex: 0,
      contractList: [],
      defaultProps: {
        children: 'children',
        label: 'schemeName'
      },
      structureModel: false,
      assetType: '34',
      gzList: [],
      isFirst: false,
      routeOptions: [], // 路段数据
      routeList: [], // 路线编码数据

      deptOptions: [],
      deptTreeOptions: [],// 部门数据
      maintenanceSectionId: '', //路段数据
      //车道
      //路段
      maintenanceSectionList:[],
      queryParams: {
        pageNum: 1,
        pageSize: 50,
        name: null,
        domainId: null,
        maiSecId: null,
        projectStatus: null,
        compensationSituation: null,
        agreementNumber: null,
        roadPropertyAgreementNumber: null,
        isTrafficControl:null,
        replyDocumentCode: null,
        replyOpinions: null,
        remarks: null,
        routeCode: null,
        maintenanceSectionName: null,
        direction: null,
        laneList:[],
        departmentId: null
      },
      //车道对象

    }

  },
  props: {
    //父组件传入的参数
    rowData: {
      type: Object,
      default: {}
    },
    readonly: {
      type: Boolean,
      default: false
    }
  },
  watch: {
    rowData: {
      handler(val) {
        if (val.id) {
          // 直接赋值给 formData
          this.formData = JSON.parse(JSON.stringify(val));

          // 拆分 startPoint 和 endPoint
          this.$set(this.formData, 'startPoint1', parseInt(this.formData.startPoint / 1000) || 0);
          this.$set(this.formData, 'startPoint2', this.formData.startPoint % 1000 || 0);
          this.$set(this.formData, 'endPoint1', parseInt(this.formData.endPoint / 1000) || 0);
          this.$set(this.formData, 'endPoint2', this.formData.endPoint % 1000 || 0);

          // 设置 isFirst
          this.isFirst = true;

          // 添加 projTDProjectStructureList 到 gzList
          this.gzList.push(...this.formData.projTDProjectStructureList);

          // 设置日期范围
          this.formData.dates = [this.formData.beginDate, this.formData.endDate];

          // 转换为字符串类型
          this.formData.year = String(this.formData.year);
          this.formData.domainId = String(this.formData.domainId);
        }
      },
      immediate: true,
      deep: true
    },"formData.domainId": {
      handler: function (val) {
        if (val) {
          if (!this.formData.id) {
            this.$set(this.formData, 'maiSecId', '')
            this.$set(this.formData, 'routeCode', '')
          }
          listMaintenanceSectionAll({departmentId: val}).then(res => {
            if (res.code == 200) {
              this.routeOptions = res.data
            }
          })
        }
      },
      immediate: true
    },
    "formData.assetType": {
      handler: function (val) {
        this.diseaseTypeList = []
        if (val) {
          listAllDiseases({assetName: val}).then(res => {
            this.diseaseTypeList = res.data.map(item => {
              return {
                dictLabel: item.diseaseName,
                dictValue: item.diseaseCode
              }
            })
          })
        }
      },
      immediate: true
    },
    "formData.mtype": {
      handler(val) {
        if (val == '1') {
          this.assetType = '31'
        } else if (val == '4') {
          this.assetType = '32'
        } else {
          this.assetType = '34'
        }
        if (this.isFirst) {
          this.isFirst = false
        } else {
          this.gzList = []
        }
      }
    }
  },
  created() {
    this.getDeptList()
    this.getMaintenanceSection()
    this.getDeptTreeDef()
    this.getDeptTree()
    this.getAssetType()
  },
  mounted() {
    console.log(this.formData.laneList); // 确认默认选项
  },
  methods: {
    handleLaneChange() {
      // this.selectedLane = selectedLanes; // 更新选中的车道
      //
      // // 查找字典项的标签
      // const selectedLaneLabels = selectedLanes.map(value => {
      //   const found = this.dict.type.engineering_lane.find(dict => dict.value === value);
      //   return found ? found.label : value; // 找到标签，未找到则返回原值
      // });

      // console.log("选择的车道信息:", this.laneList.map(item => item.lane)); // 打印选择的车道标签

      // 这里你可以进一步处理 selectedLaneLabels，例如显示在页面上
    },
    //数据提交
    submitForm: function() {
      this.formData.startPoint = this.formData.startPoint1 * 1000 + parseInt(this.formData.startPoint2)
      this.formData.endPoint = this.formData.endPoint1 * 1000 + parseInt(this.formData.endPoint2)

      this.formData.maintenanceSection = this.formData.maintenanceSectionId
      // this.formData.maintenanceSectionId = this.formData.maintenanceSectionId
      // this.formData.managementUnitId = this.formData.maintenanceUnitId

      findDeptInfo(this.formData.managementUnitId).then(res => {

        this.formData.managementUnit = res.data.deptName
        console.log("info"+JSON.stringify(this.formData.managementUnit))
      })

      this.$refs["formData"].validate(valid => {
        if (valid) {
          if (this.formData.id != null) {
            updateEngineering(this.formData).then(response => {
              this.$modal.msgSuccess("修改成功1");
              // 获取更新后的 涉路工程 信息
              return getEngineering(this.formData.id);
            }).then(response => {
              this.formData = response.data;
              // 并行获取车道信息
              return Promise.all([
                getEngineeringLane(this.formData.id), // 获取当前车道信息
              ]);
            }).then((laneResponse) => {
              //填充车道信息
              this.formData.lane = laneResponse.data;
              // 删除所有相关的车道信息
              return Promise.all([
                //删除当前工程的车道信息
                delEngineeringLane(this.formData.id).catch(() => {}),
              ]);
            }).then(() => {
              // 删除完成后，进行新增操作
              let addLanePromises = this.laneList.map(lane => {
                console.log("lane:" + this.laneList)
                return addEngineeringLane({
                  roadEngineeringId: this.formData.id,
                  lane: lane.lane,
                });
              });
              // 等待所有新增请求完成
              return Promise.all([addLanePromises]);
            }).then(() => {
              this.$modal.msgSuccess("修改成功2");
              this.open = false;
              this.reset();
              // this.getList();
            }).catch(error => {
              this.$modal.msgError("操作失败: " + error.message);
            });

          } else {
            console.log("formData"+this.formData.toString())
            addEngineering(this.formData).then(response => {
              // this.$modal.msgSuccess("新增成功");
              this.roadEngineeringId = response.data;

              // // 循环处理每个 LaneList 并调用 addEngineeringLane
              let LanePromises = this.formData.laneList.map(lane => {
                console.log("lane:" + this.formData.laneList)
                return addEngineeringLane({
                  roadEngineeringId: this.roadEngineeringId,
                  lane: lane
                });
              });
              // 等待所有 LanePromises 请求完成
              return Promise.all(LanePromises);
            }).then(response => {
              this.$modal.msgSuccess("新增成功");

            }).then(response => {
              this.open = false;
              this.reset();
              // this.getList();
            }).catch(error => {
              console.error("操作失败: ", error);
              this.$modal.msgError("提交失败，请重试");
            });
          }
        }
      })
    },
    handleChangeRoute(e) {
      if (!this.formData.id) {
        this.$set(this.formData, 'routeCode', '')
      }
      //获取路线数据
      listByMaintenanceSectionId({maintenanceSectionId: e}).then((res) => {
        if (res.code == 200) {
          let routeList = res.data || [];
          routeList = routeList.filter(item => item != null)
          this.routeList = routeList
        }
      });
    },

    reset() {
      this.formData = {
        domainId: null,
        maiSecId: null,
        routeCode: null,
        name: null,
        projectStatus: null,
        compensationSituation: null,
        submissionTime: null,
        reportingUnit: null,
        replyDocumentCode: null,
        replyTime: null,
        replyOpinions: null,
        isTrafficControl: null,
        startPoint1:null,
        startPoint2:null,
        endPoint1: null,
        endPoint2: null,
        Direction: null,
        lane: null,
        agreementNumber: null,
        roadPropertyAgreementNumber: null,
        agreementAmount:null,
        constructionUnit:null,
        designUnit: null,
        constructionCompany: null,
        supervisionUnit: null,
        plannedStartDate:null,
        plannedCompletionDate:null,
        contactPerson: null,
        phoneNumber: null,
        implementationContent:null,
        remark:null
      };
      this.queryParams.departmentIdList = null;
      this.resetForm("formData");
    },
    changeMaiSecId() {
      const sectionName = this.$refs.roadSection.getLabel(this.formData.maintenanceSectionId)
      console.log("sectionName"+this.sectionName)
      this.contractFilterMap = {
        sectionName
      }
    },

    getAssetType() {
      listAllData({dictType: 'sys_asset_type'}).then(res => {
        this.assetMainType = this.handleTree(res.data, "dictCode", "dictParentCode");
      }).finally(() => {
        this.loading = false
      })
    },
    getAssetType1() {
      console.log("从编辑页面收到的数据"+JSON.stringify(this.formData))
    },
    //管理处下拉选点击事件
    deptChange(departmentId) {
      console.log('departmentId: ', departmentId)
      this.formData.managementUnitId =departmentId
      getDept(departmentId).then(response => {
        this.formData.managementUnit = response.data.deptName
      })

      deptTreeSelect(departmentId).then((response) => {
        this.deptTreeOptions = response.data
        // console.log('deptTreeOptions::'+JSON.stringify(this.deptTreeOptions))

      })

      listMaintenanceSectionAll({ departmentId: departmentId }).then((res) => {
        this.maintenanceSectionList = res.data
        // console.log('managementUnit: ', JSON.stringify(this.maintenanceSectionList))
      })
    },

    rodeChange(){

        console.log('选择的路段ID：'+this.formData.maintenanceSectionId)

    },
    /** 查询管理处下拉树结构 */
    getDeptList() {
      return getTreeStruct({ types: 201 }).then((response) => {
        this.deptOptions = response.data
        // console.log('管理处:'+JSON.stringify(this.deptOptions))
      })
    },
    getMaintenanceSection() {
      listMaintenanceSectionAll().then((res) => {
        this.maintenanceSectionId = res.data
        // console.log('路段:'+JSON.stringify(this.managementUnit))
      })
    },

    /** 查询部门-用户下拉树结构 */
    getDeptTreeDef() {
      getTreeStruct({ types: 111 }).then((response) => {
        this.deptUserOptions = response.data
        // console.log('部门-用户:'+JSON.stringify(this.deptUserOptions))
      })
    },
    /** 查询部门下拉树结构 */
    getDeptTree() {
      deptTreeSelect().then((response) => {
        this.deptTreeOptions = response.data
        // console.log('deptTreeOptions；'+JSON.stringify(this.deptTreeOptions))
      })
    },
    handleDelete(row) {
      this.contractList[this.treeIndex].schemeList = this.contractList[this.treeIndex].schemeList.filter(item => item.id !== row.id);
    },
    handleDeleteGzw(row) {
      this.gzList = this.gzList.filter(item => item.id !== row.id);
    },
    changeCalculation(row) {
      this.$set(row, 'num', eval(row.calcDesc))
      this.$set(row, 'amount', row.num * row.price)
      this.total = this.methodList.reduce((acc, curr) => acc + curr.amount, 0)
    },
    changeSchemeNum(row) {
      this.$set(row, 'amount', row.num * row.price)
      this.total = this.methodList.reduce((acc, curr) => acc + curr.amount, 0)
    },

    checkAsset(data) {
      // 如果data.id 再gzList中不存在 则插入到gzList
      const exists = this.gzList.some(item => item.id === data.id);
      if (!exists) {
        data.startPoint1 = parseInt((data.startPoint1 || 0) / 1000)
        data.startPoint2 = parseInt((data.startPoint2 || 0) % 1000)
        data.endPoin1 = parseInt((data.endPoin1 || 0) / 1000)
        data.endPoin2 = parseInt((data.endPoin2 || 0) % 1000)
        this.gzList.push(data)
      }
      this.structureModel = false
    },
    changeMile(row) {
      this.$set(row, 'startPoint', (row.startPoint1 || 0) * 1000 + parseInt(row.startPoint2 || 0))
      this.$set(row, 'endPoint', (row.endMile1 || 0) * 1000 + parseInt(row.endMile2 || 0))
    },
    close() {
      this.$emit('close')
    }
  }
}
</script>
<style lang="scss" scoped>
.card_title {
  width: 200px;
  text-align: left;
  margin-bottom: 15px;
  font-weight: bold;
}
::v-deep {
  .el-tabs__header {
    padding-left: 20px;
    border: 0;
  }

  .el-tabs__content {
    border: 0;
  }

  .el-form-item__label {
    font-family: Microsoft YaHei, Microsoft YaHei;
    font-weight: 400;
    font-size: 14px;
    color: #333333;
  }

  .el-input.is-disabled .el-input__inner {
    background-color: white;
    border-color: #dfe4ed;
    color: #1d2129;
  }

  .el-textarea.is-disabled .el-textarea__inner {
    background-color: white;
    border-color: #dfe4ed;
    color: #1d2129;
  }
  .el-range-editor.is-disabled {
    background-color: white;
    border-color: #dfe4ed;
    color: #1d2129;
  }
  .el-range-editor.is-disabled input {
    background-color: white;
    border-color: #dfe4ed;
    color: #1d2129;
  }
}
</style>
