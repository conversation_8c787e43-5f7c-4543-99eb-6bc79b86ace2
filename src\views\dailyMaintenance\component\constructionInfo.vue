<template>
  <div class="app-container maindiv" v-loading="loading">
    <el-row :gutter="15">
      <el-form ref="elForm" :model="formData" :rules="rules" size="medium" label-width="100px">
        <el-row :gutter="20" style="flex-wrap: wrap; display: flex;">
          <el-col :span="12">
            <el-form-item label="管养单位" prop="domainId">
              <selectTree
                :key="'domainId'"
                :style="{width: '100%'}"
                v-model="formData.domainId"
                :disabled="readOnly || eventList.length > 0"
                only-select-child
                :deptType="100" :deptTypeList="[1, 3, 4]"
                placeholder="管养单位"
                @change="changeDomain"
                clearable
                filterable
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="路段名称" prop="maiSecId">
              <el-select v-model="formData.maiSecId" placeholder="养护路段" clearable style="width: 100%;"
                         :disabled="readOnly || eventList.length > 0" @change="getCode">
                <el-option v-for="item in routeOptions"
                           :key="item.maintenanceSectionId"
                           :label="item.maintenanceSectionName"
                           :value="item.maintenanceSectionId">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="编号" prop="code">
              <el-input v-model="formData.code" placeholder="请输入编号" clearable :style="{width: '100%'}"
                        :disabled="readOnly">
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="名称" prop="name">
              <el-input v-model="formData.name" placeholder="请输入名称" clearable :style="{width: '100%'}"
                        :disabled="readOnly">
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="施工单位" prop="conDomainId">
              <construction-select v-model="formData.conDomainId" @change="changeConDomain" :mai-sec-id="formData.maiSecId" :mai-domain-id="formData.domainId" :type="0" placeholder="施工单位" :readOnly="readOnly"></construction-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="施工合同" prop="conConId">
              <el-select v-model="formData.conConId" placeholder="施工合同" clearable style="width: 100%;"
                         :disabled="readOnly">
                <el-option v-for="item in constructionContractList"
                           :key="item.id"
                           :label="item.name"
                           :value="item.id">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="监理单位" prop="supDomainId">
              <construction-select v-model="formData.supDomainId" @change="changeSupDomain" :mai-sec-id="formData.maiSecId" :mai-domain-id="formData.domainId" :type="2" placeholder="监理单位" :readOnly="readOnly"></construction-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="监理合同" prop="supConId">
              <el-select v-model="formData.supConId" placeholder="监理合同" clearable style="width: 100%;"
                         :disabled="readOnly">
                <el-option v-for="item in supervisionContractList"
                           :key="item.id"
                           :label="item.name"
                           :value="item.id">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="缺陷责任期（月）" prop="defLiaPer">
              <el-input v-model="formData.defLiaPer" placeholder="请输入缺陷责任期（月）" clearable :disabled="readOnly"
                        :style="{width: '100%'}"></el-input>
            </el-form-item>
          </el-col>
  <!--        <el-col :span="12">-->
  <!--          <el-form-item label="验收人员" prop="acceptancePerson">-->
  <!--            <el-cascader-->
  <!--                v-model="formData.acceptancePerson"-->
  <!--                :options="deptUserOptions"-->
  <!--                :disabled="readOnly"-->
  <!--                :props="{ multiple: false, value: 'id', emitPath: false}"-->
  <!--                :show-all-levels="false"-->
  <!--                style="width: 100%"-->
  <!--                filterable-->
  <!--                clearable-->
  <!--                collapse-tags-->
  <!--            />-->
  <!--          </el-form-item>-->
  <!--        </el-col>-->
          <el-col :span="12">
            <el-form-item label="类型" prop="urgentDegree">
              <el-radio-group v-model="formData.urgentDegree" size="medium" :disabled="readOnly">
                <el-radio v-for="(item, index) in urgentDegreeOptions" :key="index" :label="item.value"
                          :disabled="item.disabled">{{ item.label }}
                </el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="费用类型" prop="costType">
              <cost-select :type="9" style="width: 100%" v-model=formData.costType></cost-select>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="实施要求" prop="exeRequire">
              <el-input v-model="formData.exeRequire" type="textarea" placeholder="请输入实施要求" :disabled="readOnly"
                        :autosize="{minRows: 4, maxRows: 4}" :style="{width: '100%'}"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="24" v-if="formData.supDomainId">
            <el-form-item label="监理实施要求" prop="supRequire">
              <el-input v-model="formData.supRequire" type="textarea" placeholder="请输入实施要求" :disabled="readOnly"
                        :autosize="{minRows: 4, maxRows: 4}" :style="{width: '100%'}"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="开始时间" prop="beginDate">
              <el-date-picker v-model="formData.beginDate" format="yyyy-MM-dd" value-format="yyyy-MM-dd"
                              :disabled="readOnly"
                              :picker-options="planStartPickerOptions"
                              :style="{width: '100%'}" placeholder="请选择开始时间" clearable></el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="结束时间" prop="endDate">
              <el-date-picker v-model="formData.endDate" format="yyyy-MM-dd" value-format="yyyy-MM-dd"
                              :disabled="readOnly"
                              :picker-options="planEndPickerOptions"
                              :style="{width: '100%'}" placeholder="请选择结束时间" clearable></el-date-picker>
            </el-form-item>
          </el-col>
        </el-row>
        <el-col :span="24" v-if="type >= 2">
          <el-form-item label="审核意见" prop="remark">
            <el-input :disabled="type != 2 && type != 6 && type != 5 && type != 3 && type != 7" v-model="formData.remark" type="textarea"
                      placeholder="请输入审核意见"
                      :autosize="{minRows: 4, maxRows: 4}" :style="{width: '100%'}"></el-input>
          </el-form-item>
        </el-col>
<!--        <el-col :span="24" v-if="type == 5">-->
<!--          <el-form-item label="拒收原因" prop="remark">-->
<!--            <el-input  v-model="formData.remark" type="textarea"-->
<!--                      placeholder="请输入拒收原因"-->
<!--                      :autosize="{minRows: 4, maxRows: 4}" :style="{width: '100%'}"></el-input>-->
<!--          </el-form-item>-->
<!--        </el-col>-->
        <el-col :span="24">
          <el-tabs v-model="activeName">
            <el-tab-pane label="明细信息" name="1">
              <div class="tableDiv" style="margin-bottom: 10px">
                <el-row>
                  <el-form :inline="true" :model="eventQueryParams" size="small" :rules="rules" ref="formData" label-width="80px">
                    <el-form-item prop="disType">
                      <el-select v-model="eventQueryParams.disType" filterable placeholder="请选择事件类型" clearable style="width: 240px;">
                        <el-option v-for="item in advicesList"
                                   :key="item.value"
                                   :label="item.label"
                                   :value="item.value">
                        </el-option>
                      </el-select>
                      <!--                  <dict-select type="sys_asset_type" onlyTailNode clearable v-model="eventQueryParams.disType" placeholder="请选择事件类型" style="width: 240px"></dict-select>-->
                    </el-form-item>
                    <el-form-item prop="dealType">
                      <dict-select type="disposal_type" clearable v-model="eventQueryParams.dealType" placeholder="请选择处置类型" style="width: 240px"></dict-select>
                    </el-form-item>
                    <el-form-item>
                      <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
                      <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
                    </el-form-item>
                  </el-form>
                </el-row>
                <el-button v-if="!readOnly && !editEvent" type="primary" icon="el-icon-plus" size="mini" @click="handleAddEvent"
                           style="margin-bottom: 10px">新增
                </el-button>
                <el-button style="margin-bottom: 10px" icon="el-icon-tickets" type="success" @click="openEventInfo()">事件信息</el-button>
                <el-table v-adjust-table stripe size="mini" height="200px"
                          style="width: 100%" border :data="eventList" highlight-current-row
                          @current-change="handleCurrentChange">
                  <el-table-column type="selection" width="50" align="center"/>
                  <el-table-column fixed label="序号" type="index" width="50"></el-table-column>
                  <template v-for="(column,index) in eventColumns">
                    <el-table-column :label="column.label"
                                     v-if="column.visible"
                                     align="center"
                                     :prop="column.field"
                                     :width="column.width">
                      <template slot-scope="scope">
                        <dict-tag v-if="column.dict" :options="dict.type[column.dict]" :value="scope.row[column.field]"/>
                        <template v-else-if="column.slots">
                          <RenderDom :row="scope.row" :index="index" :render="column.render"/>
                        </template>
                        <span v-else-if="column.isTime">{{ parseTime(scope.row[column.field], '{y}-{m}-{d}') }}</span>
                        <span v-else>{{ scope.row[column.field] }}</span>
                      </template>
                    </el-table-column>
                  </template>
                  <el-table-column
                    label="操作"
                    fixed="right"
                    align="center"
                    width="100"
                    v-if="!readOnly && !editEvent"
                    class-name="small-padding fixed-width"
                  >
                    <template slot-scope="scope" v-if="scope.row.userId !== 1">
                      <el-button
                        size="mini"
                        type="text"
                        icon="el-icon-delete"
                        @click="handleDeleteEvent(scope.row)"
                      >删除
                      </el-button>
                    </template>
                  </el-table-column>
                </el-table>
              </div>
              <el-dialog :visible.sync="eventDialog"  v-if="eventDialog" append-to-body modal-append-to-body title="新增事件"
                         width="75%">
                <event-info @saveEvent="saveEvent" :conId='formData.conConId' :maiSecId="formData.maiSecId" :filter-data="formData.detailList" :daliy-id="rowData.id"></event-info>
              </el-dialog>
            </el-tab-pane>
            <!--        <el-tab-pane label="附件信息" name="2">-->
            <!--          <file-info style="height: 400px" :in-id="formData.id"></file-info>-->
            <!--        </el-tab-pane>-->
            <!--        <el-tab-pane label="施工人员" name="3">-->
            <!--          <person-info style="height: 400px"></person-info>-->
            <!--        </el-tab-pane>-->
            <!--        <el-tab-pane label="施工物料" name="4">-->
            <!--          <material-info style="height: 400px"></material-info>-->
            <!--        </el-tab-pane>-->
            <!--        <el-tab-pane label="施工设备" name="5">-->
            <!--          <equipment-info style="height: 400px"></equipment-info>-->
            <!--        </el-tab-pane>-->
          </el-tabs>
        </el-col>
        <el-col :span="24">
          <el-form-item label="工程内容" prop="content">
            <el-input v-model="formData.content" type="textarea" placeholder="请输入工程内容" :disabled="readOnly"
                      :autosize="{minRows: 4, maxRows: 4}" :style="{width: '100%'}"></el-input>
          </el-form-item>
        </el-col>
      </el-form>
    </el-row>
    <div slot="footer" style="text-align: right">
      <template v-if="type == 1">
        <el-button @click="close">取消</el-button>
        <el-button v-if="!readOnly" type="primary" @click="handleAdd">保存</el-button>
        <el-button v-if="!readOnly && canSubmit" type="success" @click="handleSubmit">提交</el-button>
      </template>
      <template v-if="type == 2">
        <el-button type="primary" @click="handleReviewPass">通过</el-button>
        <el-button type="danger" @click="handleReviewReject">驳回</el-button>
      </template>
      <template v-if="type == 3">
        <el-button type="primary" @click="handleReviewNoticeIssue">签发</el-button>
        <el-button type="danger" @click="handleRejectNoticeReview(1)">撤回</el-button>
        <el-button type="danger" @click="handleRejectNoticeReview(2)">撤回到申请</el-button>
      </template>
      <template v-if="type == 4">
        <el-button @click="close">取消</el-button>
        <el-button type="danger" @click="handleRejectNoticeView">撤回</el-button>
      </template>
      <template v-if="type == 5">
        <el-button type="primary" @click="handleReceiveConfirm">接收</el-button>
        <el-button type="danger" @click="handleRejectConfirm">拒收</el-button>
      </template>
      <template v-if="type == 6">
        <el-button type="primary" @click="handleReviewExamine">通过</el-button>
        <el-button type="danger" @click="handleRejectExamine">撤回</el-button>
      </template>
      <template v-if="type == 7">
        <el-button type="primary" @click="handleSupReceiveConfirm">接收</el-button>
      </template>
    </div>
    <el-dialog
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :destroy-on-close="true"
      append-to-body modal-append-to-body
      :visible.sync="eventInfoVisible"
      v-if="eventInfoVisible"
      title="事件信息"
      width="80%">
      <event-detail @close="closeEventInfo" :dis-info="disData" :daliy-id="rowData.id"/>
    </el-dialog>
  </div>
</template>
<script>
import EventInfo from "../component/eventInfo.vue";
import FileInfo from "../component/fileInfo.vue";
import PersonInfo from "../component/personInfo.vue";
import MaterialInfo from "../component/materialInfo.vue";
import EquipmentInfo from "../component/equipmentInfo.vue";
import selectTree from "@/components/DeptTmpl/selectTree.vue";
import ConstructionSelect from "@/components/ConstructionSelect";

import {listMaintenanceSectionAll} from "@/api/system/maintenanceSection";
import {listDept} from "@/api/system/dept";
import {getListAll} from "@/api/contract/info/index"
import {
  addNoticeDraft,
  editNoticeDraft,
  getConstructionDetail,
  submitNoticeDraft, updateConstruction
} from "@/api/dailyMaintenance/constructionOrder/noticeDraft"
import {reviewNoticeReview, rejectNoticeReview} from "@/api/dailyMaintenance/constructionOrder/noticeReview"
import {reviewNoticeIssue, rejectNoticeIssue} from "@/api/dailyMaintenance/constructionOrder/noticeIssue";
import {rejectNoticeView} from "@/api/dailyMaintenance/constructionOrder/noticeView";
import {receiveConfirm, rejectConfirm} from "@/api/dailyMaintenance/construction/confirm";
import {reviewExamine, rejectExamine} from "@/api/dailyMaintenance/construction/examine"
import {getTreeStruct} from "@/api/tmpl";
import EventDetail from "@/views/dailyMaintenance/component/eventDetail.vue";
import {getCode} from "@/api/system/reportcode";
import {rejectSupervisionAudit, submitSupervisionAudit} from "@/api/dailyMaintenance/construction/supAcceptance";
import {listDiseaseAdvices} from "@/api/patrol/diseaseAdvices";
import {listAllDiseases} from "@/api/patrol/diseases";
import CostSelect from "@/components/CostSelect/index.vue";

export default {
  inheritAttrs: false,
  components: {
    CostSelect,
    EventDetail,
    selectTree, EquipmentInfo, MaterialInfo, PersonInfo, FileInfo, EventInfo,ConstructionSelect,
    RenderDom: {
      functional: true,
      props: {
        row: Object,
        index: Number,
        render: Function
      },
      render(createElement, ctx) {
        const {row, index} = ctx.props
        return ctx.props.render(row, index)
      }
    }
  },
  props: {
    type: {
      type: String,
      default: '1'
    },
    readOnly: {
      type: Boolean,
      default: false
    },
    editEvent: {
      type: Boolean,
      default: false
    },
    rowData: {
      type: Object,
      default: () => {
        return {}
      }
    },
    conId: {
      type: String,
      default: ''
    },
    canSubmit: {
      type: Boolean,
      default: false
    }
  },
  dicts: ['urgent_degree', 'cost_name', 'mp_daily_month_plan_status', 'route_direction', 'lane', 'disposal_type', 'sys_asset_type'],
  data() {
    return {
      loading: false,
      formData: {
        detailList: [],
        attList: [],
        personList: [],
        deviceList: [],
        materialList: [],
      },
      rules: {
        domainId: [{
          required: true,
          message: '请输入管养单位',
          trigger: 'blur'
        }],
        maiSecId: [{
          required: true,
          message: '请输入路段名称',
          trigger: 'blur'
        }],
        code: [],
        name: [{
          required: true,
          message: '请输入名称',
          trigger: 'blur'
        }],
        conDomainId: [{
          required: true,
          message: '请输入施工单位',
          trigger: 'blur'
        }],
        conConId: [{
          required: true,
          message: '请输入施工合同',
          trigger: 'blur'
        }],
        defLiaPer: [{
          required: true,
          message: '请输入缺陷责任期（月）',
          trigger: 'blur'
        }],
        urgentDegree: [{
          required: true,
          message: '类型不能为空',
          trigger: 'change'
        }],
        exeRequire: [],
        beginDate: [{
          required: true,
          message: '请选择开始时间',
          trigger: 'change'
        }],
        endDate: [{
          required: true,
          message: '请选择结束时间',
          trigger: 'change'
        }],
        checkDesc: [{
          required: true,
          message: '审核意见不能为空',
          trigger: 'change'
        }]
      },
      urgentDegreeOptions: [{
        "label": "紧急",
        "value": 1
      }, {
        "label": "普通",
        "value": 0
      }],
      routeOptions: [], // 路段数据
      constructionUnitList: [], // 施工单位列表
      supervisionUnitList: [],  // 监理单位列表
      constructionContractList: [], // 施工合同列表
      supervisionContractList: [],  // 监理合同列表
      deptUserOptions: [],  // 人员
      advicesList: [],
      activeName: '1',
      // 列信息
      eventDialog: false,
      eventQueryParams: {},
      eventList: [],
      eventColumns: [
        {key: 20, width: 100, field: 'disCode', label: `事件编码`, visible: true},
        {key: 0, width: 100, field: 'assetMainType', label: `资产类型`, visible: true, dict: 'sys_asset_type'},
        {key: 1, width: 100, field: 'maiSecId', label: `路段`, visible: true},
        {key: 2, width: 100, field: 'routeCode', label: `路线编码`, visible: true},
        {key: 3, width: 100, field: 'direction', label: `上下行`, visible: true, dict: 'route_direction'},
        {key: 4, width: 100, field: 'lane', label: `位置`, visible: true, dict: 'lane'},
        {
          key: 7,
          width: 150,
          field: 'costType',
          label: `费用类型`,
          visible: true,
          slots: true,
          render: (row, index) => {
            return (
              <cost-select type="9" read-only={this.readOnly} v-model={row.costType}></cost-select>
            )
          }
        },
        {key: 5, width: 100, field: 'beginMile', label: `起点桩号`, visible: true, slots: true, render: (row, index) => {
            return (
                <span>{this.formatPile(row.beginMile)}</span>
            )
          }},
        {key: 6, width: 100, field: 'endMile', label: `终点桩号`, visible: true, slots: true, render: (row, index) => {
            return (
                <span>{this.formatPile(row.endMile)}</span>
            )
          }},
        {key: 7, width: 100, field: 'disTypeName', label: `事件类型`, visible: true, dict: 'sys_asset_type'},
        {key: 8, width: 100, field: 'dealType', label: `处置类型`, visible: true, dict: 'disposal_type'},
        {key: 9, width: 100, field: 'disDesc', label: `描述`, visible: true,slots: true,
          render: (row) => {
            return (
              <el-tooltip className="item" effect="dark" content={row.disDesc} placement="top-start">
                <div style="overflow: hidden;white-space:nowrap;text-overflow:ellipsis">{row.disDesc}</div>
              </el-tooltip>
            )
          }},
        {key: 10, width: 100, field: 'remark', label: `备注`, visible: true},
        {key: 11, width: 100, field: 'collectTime', label: `采集时间`, visible: true},
        {key: 12, width: 100, field: 'updateByName', label: `操作人`, visible: true},
      ],
      planStartPickerOptions: this.beginDate(),
      planEndPickerOptions: this.processDate(),
      eventInfoVisible: false,
      disData: {}
    }
  },
  computed: {},
  watch: {
    'formData.domainId': function (newVal, oldVal) {
      this.getMaintenanceSectionList(newVal)
      this.getConstructionUnitList(newVal)
      this.getSupervisionUnitList(newVal)
    },
    'formData.conDomainId': function (newVal, oldVal) {
      if (newVal) {
        this.$nextTick(() => {
          this.getConstructionContractList(this.formData.conDomainId)
        })
      }
    },
    'formData.supDomainId': function (newVal, oldVal) {
      if (newVal) this.getSupervisionContractList(newVal)
    },
    "formData.costType" (newVal, oldVal) {
      if (newVal) {
        this.formData.detailList.forEach(item => {
          item.costType = newVal
        })
      }
    },
    rowData: {
      handler: function (val, oldVal) {
        if (val) {
          this.formData = JSON.parse(JSON.stringify(val))
          // if (this.formData.beginDate) this.formData.beginDate = new Date(this.formData.beginDate.replace(/-/,"/"))
          // if (this.formData.endDate) this.formData.endDate = new Date(this.formData.endDate.replace(/-/,"/"))
          this.formData.detailList = this.formData.detailList ? this.formData.detailList : []
          this.formData.attList = this.formData.attList ? this.formData.attList : []
          this.formData.personList = this.formData.personList ? this.formData.personList : []
          this.formData.deviceList = this.formData.deviceList ? this.formData.deviceList : []
          this.formData.materialList = this.formData.materialList ? this.formData.materialList : []
          this.eventList = this.formData.detailList
          if (this.formData.domainId) this.formData.domainId = String(this.formData.domainId)
          this.$set(this.formData, 'urgentDegree', 0)
          if (val.id) {
            this.loading = true
            getConstructionDetail(val.id).then(res => {
              this.$set(this.formData, 'urgentDegree', res.rows[0].urgentDegree)
              this.formData.detailList = res.rows[0].detailList || []
              this.eventList = this.formData.detailList
              this.formData.attList = res.rows[0].attList || []
              this.formData.personList = res.rows[0].personList || []
              this.formData.deviceList = res.rows[0].deviceList || []
              this.formData.materialList = res.rows[0].materialList || []
            }).finally(() => {
              this.loading = false
            })
          }
        }
      },
      immediate: true
    }
  },
  created() {
    this.getMenbers()
    this.getDisList()
  },
  mounted() {
    if (this.conId) {
      getConstructionDetail(this.conId).then(res => {
        this.formData = res.rows[0]
        if (this.formData.beginDate) this.formData.beginDate = new Date(this.formData.beginDate.replace(/-/,"/"))
        if (this.formData.endDate) this.formData.endDate = new Date(this.formData.endDate.replace(/-/,"/"))
        this.formData.detailList = res.rows[0].detailList || []
        this.eventList = this.formData.detailList
        this.formData.attList = res.rows[0].attList || []
        this.formData.personList = res.rows[0].personList || []
        this.formData.deviceList = res.rows[0].deviceList || []
        this.formData.materialList = res.rows[0].materialList || []
      })
    }

  },
  methods: {
    onOpen() {
    },
    onClose() {
      this.$refs['elForm'].resetFields()
    },
    handleConfirm() {
      this.$refs['elForm'].validate(valid => {
        if (!valid) return
        this.close()
      })
    },
    // 新增
    handleAdd() {
      this.$refs.elForm.validate(valid => {
        if (!valid) return
        // 遍历详情列表  校验是否有costType为空的
        if (this.formData.detailList.some(item => !item.costType)) {
          this.$modal.msgWarning("明细信息费用类型不能为空")
          return
        }
        if (this.editEvent) {
          updateConstruction(this.formData).then(res => {
            if (res.code == 200) {
              this.$modal.msgSuccess("修改成功");
              this.close()
            }
          })
        } else if (this.formData.id) {
          editNoticeDraft(this.formData).then(res => {
            if (res.code == 200) {
              this.$modal.msgSuccess("修改成功");
              this.close()
            }
          })
        } else {
          addNoticeDraft(this.formData).then(res => {
            if (res.code == 200) {
              this.$modal.msgSuccess("新增成功");
              this.close()
            }
          })
        }
      })
    },
    // 提交
    handleSubmit() {
      // 遍历详情列表  校验是否有costType为空的
      if (this.formData.detailList.some(item => !item.costType)) {
        this.$modal.msgWarning("明细信息费用类型不能为空")
        return
      }
      submitNoticeDraft(this.formData).then(res => {
        if (res.code == 200) {
          this.$modal.msgSuccess("提交成功");
          this.close()
        }
      })
    },
    // 获取养护路段列表
    getMaintenanceSectionList(departmentId) {
      listMaintenanceSectionAll({departmentId}).then(res => {
        if (res.code == 200) {
          this.routeOptions = res.data
          this.getConstructionContractList()
          this.getSupervisionContractList()
        }
      })
    },
    // 获取施工单位列表
    getConstructionUnitList(departmentId) {
      listDept({deptType: 6}).then(res => {
        if (res.code == 200) {
          this.constructionUnitList = res.data
        }
      })
    },
    // 获取监理单位列表
    getSupervisionUnitList(departmentId) {
      listDept({deptType: 8}).then(res => {
        if (res.code == 200) {
          this.supervisionUnitList = res.data
        }
      })
    },
    // 获取施工合同列表
    getConstructionContractList(conDomainId) {
      // 获取路段中文名
      const sectionName = this.routeOptions.length > 0 ? this.routeOptions.find(item => item.maintenanceSectionId == this.formData.maiSecId).maintenanceSectionName : this.formData.maiSecId
      getListAll({conDomainId, isEnable: '0', sectionName, pageSize: 100}).then(res => {
        if (res.code == 200) {
          this.constructionContractList = res.rows
        }
      })
    },
    // 获取监理合同列表
    getSupervisionContractList(supDomainId) {
      const sectionName = this.routeOptions.length > 0 ? this.routeOptions.find(item => item.maintenanceSectionId == this.formData.maiSecId).maintenanceSectionName : ''
      getListAll({conDomainId: supDomainId, isEnable: '0', sectionName, pageSize: 100}).then(res => {
        if (res.code == 200) {
          this.supervisionContractList = res.rows
        }
      })
    },

    // 获取人员树
    getMenbers() {
      getTreeStruct({ types: 111 }).then(res => {
        if (res.code === 200) {
          this.deptUserOptions = res.data
        }
      })
    },
    // 获取事件列表
    getDisList() {
      listAllDiseases().then(res => {
        res.data.forEach(item => {
          this.advicesList.push({
            label: item.diseaseName,
            value: item.id
          })
        })
      })
    },
    // 新增事件信息
    saveEvent(list) {
      this.formData.detailList.push(...list)

      this.formData.detailList.forEach(item => {
        if (this.formData.costType) item.costType = this.formData.costType
      })
      this.eventList = this.formData.detailList
      let content = ''
      this.eventList.forEach((item,index) => {
        content += `${index + 1}、${item.maiSecName}(${item.routeCode})${item.direction == 0 ? "上行" : item.direction == 1 ? "下行" : "双向"},${this.formatPile(item.beginMile)}-${this.formatPile(item.endMile)},${item.disDesc || ''}\n`
      })
      this.formData.content = content
      this.eventDialog = false
    },

    // 删除事件信息
    handleDeleteEvent(row) {
      this.formData.detailList = this.formData.detailList.filter(item => item.id !== row.id)
      this.eventList = this.formData.detailList
      let content = ''
      this.eventList.forEach((item,index) => {
        content += `${index + 1}、${item.maiSecName}(${item.routeCode})${item.direction == 0 ? "上行" : item.direction == 1 ? "下行" : "双向"},${this.formatPile(item.beginMile)}-${this.formatPile(item.endMile)},${item.disDesc}\n`
      })
      this.formData.content = content
    },
    // 审核通过
    handleReviewPass() {
      this.$refs.elForm.validate(valid => {
        if (!valid) return
        reviewNoticeReview(this.formData).then(res => {
          if (res.code == 200) {
            this.$modal.msgSuccess("审核通过成功");
          }
          this.close()
        }).catch((e) => {
          this.$modal.msgError(e)
          this.close()
        })
      })
    },
    // 审核不通过
    handleReviewReject() {
      this.$refs.elForm.validate(valid => {
        if (!valid) return
        rejectNoticeReview(this.formData).then(res => {
          if (res.code == 200) {
            this.$modal.msgSuccess("审核不通过成功");
          }
          this.close()
        }).catch((e) => {
          this.$modal.msgError(e)
          this.close()
        })
      })
    },
    // 签发
    handleReviewNoticeIssue() {
      reviewNoticeIssue(this.formData).then(res => {
        if (res.code == 200) {
          this.$modal.msgSuccess("签发成功");
        }
        this.close()
      }).catch((e) => {
        this.$modal.msgError(e)
        this.close()
      })
    },
    // 撤回
    handleRejectNoticeReview(rejectType) {
      this.formData.rejectType = rejectType
      rejectNoticeIssue(this.formData).then(res => {
        if (res.code == 200) {
          this.$modal.msgSuccess("撤回成功");
        }
        this.close()
      }).catch((e) => {
        this.$modal.msgError(e)
        this.close()
      })
    },
    // 查看撤回
    handleRejectNoticeView() {
      rejectNoticeView(this.formData).then(res => {
        if (res.code == 200) {
          this.$modal.msgSuccess("撤回成功");
        }
        this.close()
      }).catch((e) => {
        this.$modal.msgError(e)
        this.close()
      })
    },
    // 确认接收
    handleReceiveConfirm() {
      receiveConfirm(this.formData).then(res => {
        if (res.code == 200) {
          this.$modal.msgSuccess("接收成功");
        }
        this.close()
      }).catch((e) => {
        this.$modal.msgError(e)
        this.close()
      })
    },
    // 确认驳回
    handleRejectConfirm() {
      if (!this.formData.remark) {
        this.$modal.msgWarning("拒收需要填写拒收原因");
        return
      }
      rejectConfirm(this.formData).then(res => {
        if (res.code == 200) {
          this.$modal.msgSuccess("拒收成功");
        }
        this.close()
      }).catch((e) => {
        this.$modal.msgError(e)
        this.close()
      })
    },
    handleReviewExamine() {
      this.$refs.elForm.validate(valid => {
        if (!valid) return
        reviewExamine(this.formData).then(res => {
          if (res.code == 200) {
            this.$modal.msgSuccess("审核成功");
          }
          this.close()
        }).catch((e) => {
          this.$modal.msgError(e)
          this.close()
        })
      })
    },

    handleRejectExamine() {
      this.$refs.elForm.validate(valid => {
        if (!valid) return
        rejectExamine(this.formData).then(res => {
          if (res.code == 200) {
            this.$modal.msgSuccess("驳回成功");
          }
          this.close()
        }).catch((e) => {
          this.$modal.msgError(e)
          this.close()
        })
      })
    },

    // 监理接收
    handleSupReceiveConfirm() {
      submitSupervisionAudit(this.formData).then(res => {
        if (res.code == 200) {
          this.$modal.msgSuccess("接收成功");
        }
        this.close()
      }).catch((e) => {
        this.$modal.msgError(e)
        this.close()
      })
    },
    // 监理驳回
    // handleSupRejectConfirm() {
    //   if (!this.formData.remark) {
    //     this.$modal.msgWarning("拒收需要填写拒收原因");
    //     return
    //   }
    //   rejectSupervisionAudit(this.formData).then(res => {
    //     if (res.code == 200) {
    //       this.$modal.msgSuccess("拒收成功");
    //       this.close()
    //     }
    //   })
    // },
    changeDomain() {
      this.$set(this.formData, 'maiSecId', '')
      this.$set(this.formData, 'conDomainId', '')
      this.$set(this.formData, 'supDomainId', '')
      this.$set(this.formData, 'conConId', '')
      this.$set(this.formData, 'supConId', '')
    },
    changeConDomain(e) {
      if (e) {
        this.$set(this.formData, 'conConId', '')
        this.getConstructionContractList(e)
      }
    },
    changeSupDomain(e) {
      if (e) {
        this.$set(this.formData, 'supConId', '')
        this.getSupervisionContractList(e)
      }
    },
    handleQuery() {
      // 过滤出满足查询条件的数据
      this.eventList = this.formData.detailList.filter(item => {
        return (this.eventQueryParams.dealType ? item.dealType == this.eventQueryParams.dealType : true) && (this.eventQueryParams.disType ? item.disType == this.eventQueryParams.disType : true)
      })
    },
    resetQuery() {
      this.eventQueryParams = {}
    },
    beginDate() {
      const self = this;
      return {
        disabledDate(time) {
          if (self.formData.endDate) {
            //如果结束时间不为空，则小于结束时间
            return new Date(self.formData.endDate).getTime() < time.getTime();
          } else {
            return false;
          }
        },
      };
    },
    processDate() {
      const self = this;
      return {
        disabledDate(time) {
          if (self.formData.beginDate) {
            //如果开始时间不为空，则结束时间大于开始时间
            return new Date(self.formData.beginDate).getTime() > time.getTime();
          } else {
            return false;
          }
        },
      };
    },
    getCode() {
      this.$set(this.formData, 'code', '')
      const maiSecName = this.routeOptions.filter(item => item.maintenanceSectionId === this.formData.maiSecId)[0].maintenanceSectionName
      const params = {
        reportType: 'MP_R_TASK_CODE',
        domainId: this.formData.domainId,
        sectionName: maiSecName,
        year: new Date().getFullYear()
      }
      getCode(params).then(res => {
        if (res.code == 200) {
          this.$set(this.formData, 'code', res.msg)
        }
      })
    },

    // 关闭抽屉
    close() {
      this.$emit('close')
    },
    handleCurrentChange(row) {
      this.disData = row
    },
    openEventInfo() {
      if (!this.disData.id) {
        this.$modal.msgWarning("请先选择数据")
        return
      }
      this.eventInfoVisible = true
    },
    closeEventInfo() {
      this.eventInfoVisible = false
    },
    handleAddEvent() {
      if (!this.formData.conConId) {
        this.$modal.msgWarning("请先选择合同")
        return
      }
      this.eventDialog = true
    }
  }
}

</script>
<style scoped lang="scss">
::v-deep {
  .el-tabs__header {
    padding-left: 20px;
    border: 0;
  }
  .el-tabs__content {
    border: 0;
  }
  .el-form-item__label {
    font-family: Microsoft YaHei, Microsoft YaHei;
    font-weight: 400;
    font-size: 14px;
    color: #333333;
  }
  .el-input.is-disabled .el-input__inner {
    background-color: white;
    border-color: #dfe4ed;
    color: #1d2129;
  }
  .el-textarea.is-disabled .el-textarea__inner {
    background-color: white;
    border-color: #dfe4ed;
    color: #1d2129;
  }
}
</style>
<style lang="scss" scoped>
@import "@/assets/styles/business.scss";
</style>
