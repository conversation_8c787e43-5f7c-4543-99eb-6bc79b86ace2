<template>
  <div>
    <el-dialog :title="title" :visible.sync="showAddEdit" width="65%" append-to-body :before-close="handleClose"
      :close-on-click-modal="false" :class="forView ? 'forView' : ''">
      <div v-loading="loading" style="height: 60vh;overflow-y: auto;padding: 0 10px 0 5px;">
        <el-form ref="form" :model="form" :rules="rules" label-width="0px">
          <div style="display: flex; flex-wrap: wrap; margin-bottom: 20px;">
            <el-col :span="4">
              <el-form-item label="" prop="columnName">
                <el-input v-model="form.columnName" style="width: 100%;" clearable placeholder="请输入字段名称" />
              </el-form-item>
            </el-col>
            <el-col :span="4" :offset="1">
              <el-form-item label="" prop="alias">
                <el-input v-model="form.alias" style="width: 100%;" clearable placeholder="请输入字段别名" />
              </el-form-item>
            </el-col>
            <el-col :span="4" :offset="1">
              <el-form-item label="" prop="columnType">
                <el-select v-model="form.columnType" style="width: 100%;" placeholder="请选择数据类型" clearable>
                  <el-option v-for="i in dict.type.sys_database_type" :key="i.value" :label="i.label"
                    :value="i.value" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="4" :offset="1">
              <el-form-item label="" prop="inputBoxType">
                <!-- 输入类型 -->
                <el-select v-model="form.inputBoxType" placeholder="请选择输入框类型" clearable>
                  <el-option v-for="item in inputOptions" :key="item.value" :label="item.label" :value="item.value">
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="4" :offset="1">
              <el-form-item label="" prop="otherConfig">
                <!-- 其他配置 -->
                <el-input v-model="form.otherConfig" style="width: 100%;" clearable placeholder="请输入其他配置json" />
              </el-form-item>
            </el-col>
          </div>
        </el-form>
        <el-table v-adjust-table ref="table" height="70%" style="width: 100%" :header-cell-style="{ 'height': '36px' }"
          :row-style="rowStyle" v-loading="tableLoading" border :data="staticList" @row-click="handleRowClick"
          @selection-change="handleSelectionChange" row-key="id" @select="onSelect">
          <el-table-column type="selection" width="50" align="center" />
          <el-table-column fixed label="序号" type="index" width="50" align="center">
            <template v-slot="scope">
              {{ (scope.$index + (queryParams.pageNum - 1) * queryParams.pageSize) + 1 }}
            </template>
          </el-table-column>

          <el-table-column label="字段名称" align="center" prop="columnName" />
          <el-table-column label="字段别名" align="center" prop="alias" />

          <el-table-column label="类别名称" align="center" prop="columnType">
            <template slot-scope="scope">
              <dict-tag :options="dict.type.sys_database_type" :value="scope.row.columnType" />
            </template>
          </el-table-column>

          <el-table-column label="分类表id" align="center" prop="typeId" />
          <el-table-column label="输入框类型" align="center" prop="inputBoxType">
            <template slot-scope="{row}">
              <span>{{ row.inputBoxType }}</span>
            </template>
          </el-table-column>
          <el-table-column label="配置" align="center" prop="otherConfig" />
        </el-table>
        <pagination :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize"
          @pagination="getList" />
      </div>
      <div slot="footer">
        <el-button v-if="!forView" type="primary" :loading="loading" @click="handleSubmit('submit')"
          :disabled="ids.length ? true : false">
          提 交
        </el-button>
        <el-button v-if="!forView" type="primary" :loading="loading" @click="handleEdit">更 新</el-button>
        <!-- <el-button v-if="!forView" type="primary" :loading="loading" @click="handleDelete">删除</el-button>
        <el-button v-if="!forView && !formData.id" type="primary" :loading="loading" @click="handleSubmit('save')">
          暂存
        </el-button> -->
        <el-button @click="handleClose">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>

import SelectTree from '@/components/DeptTmpl/selectTree'

import {
  fieldsAdd,
  fieldsDelete,
  fieldsGetlistPage,
  fieldsRegister,
  fieldsUpdate,
} from '@/api/system/asset'
import { label } from '@/views/process/manage/modeler/components/factory/EntryFactory'


export default {
  name: 'wallInfo',
  props: {
    formData: {
      default: {}
    },
    showAddEdit: {
      default: false
    },
    title: {
      default: '修改资产表'
    },
    forView: {
      default: false
    },
    choseId: {
      default: ''
    },
    minId: {
      default: ''
    }
  },
  dicts: [
    'sys_database_type'
  ],
  components: {},
  data() {
    return {
      loading: false,
      tableLoading: false,
      form: {},
      selectdTables: [],
      ids: [],
      staticList: [],
      total: 0,
      queryParams: {
        pageNum: 1,
        pageSize: 50
      },
      rules: {
        columnType: [
          { required: true, message: '请选择数据类型', trigger: 'change' },

        ],
        columnName: [
          { required: true, message: '请输入字段名称', trigger: 'blur' },
          { pattern: /^[a-z_]+$/, message: '必须由小写英文和下划线组成', trigger: 'blur' }
        ],
        inputBoxType: [
          { required: true, message: '请选择输入框类型', trigger: 'change' },
        ],
        otherConfig: [
          {
            validator: (rule, value, callback) => {
              if (!value) {
                callback();
                return;
              }
              if (typeof value !== 'string') {
                callback(new Error('请输入字符串格式的JSON'));
                return;
              }
              value = value.trim();
              if (!value.startsWith('{') || !value.endsWith('}')) {
                callback(new Error('JSON必须以{开始,以}结束'));
                return;
              }
            }, trigger: 'blur'
          }
        ]
      },
      inputOptions: [
        {
          label: '默认',
          value: 0
        },
        {
          label: '地图',
          value: 1
        },
        {
          label: '文本框',
          value: 2
        },
        {
          label: '文本域',
          value: 3
        },
        {
          label: '单选框',
          value: 4
        },
        {
          label: '多选框',
          value: 5
        },
        {
          label: '地区选择',
          value: 6
        },
        {
          label: '日期',
          value: 7
        },
        {
          label: '图片',
          value: 8
        },
        {
          label: '附件',
          value: 9
        },
        {
          label: '区间',
          value: 10
        },
        {
          label: '字典',
          value: 11
        },
        {
          label: '桩号',
          value: 12
        },
        {
          label: '整数',
          value: 13
        },
        {
          label: '小数',
          value: 14
        },
        {
          label: '日期(到秒)',
          value: 15
        },
        {
          label: '布尔',
          value: 16
        },
      ],
    }
  },
  created() {
    this.init()
  },
  methods: {
    init() {
      this.getList()
      this.form.typeId = this.choseId
      this.form.mainTypeId = this.minId
    },
    rowStyle({ row, rowIndex }) {
      if (this.ids.includes(row.id)) {
        return { 'background-color': '#b7daff', color: '#333' }
      } else {
        return { 'background-color': '#fff', color: '#333' }
      }
    },
    // 删除按钮
    handleDelete() {
      if (this.ids.length == 0) {
        this.$message.warning('请选择至少一条数据进行删除！')
        return
      }
      this.$modal
        .confirm('确认删除？')
        .then(() => {
          fieldsDelete(this.ids.join()).then(res => {
            if (res && res.code == '200') {
              this.getList()
              this.$modal.msgSuccess('删除成功')
            }
          })
        })
        .catch(() => { })
    },
    getList() {
      this.tableLoading = true

      fieldsGetlistPage({ typeId: this.choseId, mainTypeId: this.minId, ...this.queryParams })
        .then(res => {
          if (res.code === 200) {
            this.staticList = res.rows
            this.total = res.total
          }
        })
        .finally(() => {
          this.tableLoading = false
        })
    },
    // 表格点击勾选
    handleRowClick(row) {
      // row.isSelected = !row.isSelected;
      this.form = row;
      this.$refs.table.toggleRowSelection(row)
    },
    handleSelectionChange(selection) {
      this.selectdTables = selection
      this.ids = selection.map(item => item.id)
      if (selection.length > 1) {
        let del_row = selection.shift();
        this.$refs.table.toggleRowSelection(del_row, false);
      }
    },
    checkBoxSelect(row) {
      return !row.isChoose;
    },
    onSelect(selection, row) {
      this.form = row;
      if (selection.length > 1) {
        let del_row = selection.shift();
        this.$refs.table.toggleRowSelection(del_row, false);
      }
      this.$forceUpdate();
    },
    handleSubmit(type) {
      let pass = true
      switch (type) {
        case 'submit':
          this.form.status = 2
          break
        case 'save':
          this.form.status = 1
          break
      }

      this.$refs.form.validate(valid => {
        if (valid) {
          pass = true
        } else {
          pass = false
          return false
        }
      })

      if (!pass) return

      if (this.form.id != null) {
        updateSlope(this.form)
          .then(response => {
            this.$modal.msgSuccess('修改成功')

          })
          .catch(() => {
            this.loading = false
            this.getList()
          })
      } else {
        fieldsAdd(this.form)
          .then(response => {
            this.$modal.msgSuccess(
              '新增成功'
            )
            this.loading = false
            this.getList()
          })
          .catch(() => {
            this.loading = false
          })
      }
    },
    handleClose() {
      if (this.forView) {
        this.form = {}
        this.$emit('close', false)
      } else {
        this.$modal
          .confirm('确认退出？')
          .then(() => {
            this.form = {}
            this.$emit('refresh')
            this.$emit('close', false)
          })
          .catch(() => { })
      }
    },
    // 编辑
    handleEdit() {
      let params = {
        id: this.form.id,
        inputBoxType: this.form.inputBoxType,
        otherConfig: this.form.otherConfig,
      }
      this.$refs.form.validate(valid => {
        if (!valid) return
        this.$modal.loading()
        fieldsUpdate(params).then(res => {
          if (res && res.code == '200') {
            this.$modal.msgSuccess('修改成功')
            this.$refs.form.clearValidate()
            // 置空form表单
            this.form = {}
            this.getList()
          }
        }).finally(() => {
          this.$modal.closeLoading()
        })
      })
    },
  },
  computed: {},
  watch: {

  }
}
</script>

<style lang="scss" scoped>
.forView ::v-deep .el-input.is-disabled .el-input__inner {
  background-color: white;
  border-color: #dfe4ed;
  color: black;
}

::v-deep .el-dialog__header {
  border-bottom: 1px #dfe4ed solid;
  padding: 20px 30px !important;
}

::v-deep .el-divider--horizontal {
  margin: 20px 0 !important;
}

::v-deep .el-table {
  thead {
    .el-checkbox__input {
      display: none !important;
    }
  }
}
</style>
