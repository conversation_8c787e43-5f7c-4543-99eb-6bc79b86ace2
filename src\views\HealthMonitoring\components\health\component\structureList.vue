<template>
  <div class="structure-list">
    <Tables :columns="clumns" :data="tableData" v-on="$listeners" />
  </div>
</template>

<script>
import Tables from '@/views/Tibet/components/tables.vue';
import { getAllStructureInfoGroupByDomain } from '@/api/cockpit/health';

export default {
  name: 'structureList',
  components: {
    Tables
  },
  props: {
    structure: {
      type: Array,
      default: () => ([])
    },
    structureType: {
      type: String,
      default: ''
    },
    searchAll: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      clumns: [
        {
          label: '名称',
          prop: 'name',
        },
        {
          label: '类型',
          prop: 'structureType',
          width: 50
        },
        {
          label: '预案状态',
          prop: 'planStatusName',
          width: 50
        },
      ],
      tableData: [],
      structureList: [], // 存放所有结构数据
      name: '' // 添加name属性
    }
  },
  watch: {
    structure: {
      handler(newVal) {
        this.getList(this.searchAll ? '' : this.name);
      },
      deep: true,
      immediate: true
    },
    searchAll: {
      handler(newVal) {
        this.getList(newVal ? '' : this.name);
      },
      immediate: true
    }
  },
  methods: {
   getList(name) {
	   console.log("name",name);
     // 更新当前name值
     if (name !== undefined) {
       this.name = name;
     }
     
     let arr = [];
     this.structure.forEach(item => {
       item.planStatusName = item.planStatus == '1' ? '开启中' : '未开启';
       
       // 根据条件筛选数据
       const typeMatch = !this.structureType || this.structureType === '全部' || item.structureType === this.structureType;
       const nameMatch = !this.name || item.domainName === this.name;
       
       if (typeMatch && (this.searchAll || nameMatch)) {
         arr.push(item);
       }
     });
     
     this.tableData = arr;
   }
  },
}
</script>

<style lang="scss" scoped>
@import "@/assets/styles/utils.scss";

.structure-list {
  width: 100%;
  height: 100%;
  padding: vwpx(10px);
}
</style>