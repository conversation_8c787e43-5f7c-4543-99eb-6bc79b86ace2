<template>
	<div>
		<el-dialog
			title="绿化详情"
			:visible.sync="showDetail"
			width="65%"
			style="width: 100%; height: 80vh"
			append-to-body
			:before-close="handleClose"
			:close-on-click-modal="true"
		>
			<el-input
				style="margin-right: 20px; width: 170px"
				v-model="queryParams.assetCode"
				placeholder="资产编码"
				clearable
			/>

			<el-button type="primary" icon="el-icon-search" @click="handleQuery">搜索</el-button>
			<el-button icon="el-icon-refresh" @click="resetQuery">重置</el-button>
			<el-button style="margin-bottom: 10px" type="primary" @click="exportList">导出列表</el-button>
			<el-table
				v-adjust-table
				v-loading="loading"
				ref="table"
				height="250px"
				border
				:data="tableData"
				:row-style="rowStyle"
				@selection-change="handleSelectionChange"
				@row-click="handleRowClick"
			>
				<!-- <el-table-column

          type="selection"
          width="50"
          align="center"
        /> -->
				<el-table-column label="序号" type="index" width="50" align="center">
					<template v-slot="scope">
						{{ scope.$index + (queryParams.pageNum - 1) * queryParams.pageSize + 1 }}
					</template>
				</el-table-column>
				<el-table-column
					label="资产子类"
					align="center"
					prop="typeName"
					min-width="140"
					show-overflow-tooltip
				></el-table-column>
				<el-table-column
					label="资产编码"
					align="center"
					prop="assetCode"
					min-width="140"
					show-overflow-tooltip
				/>
				<el-table-column
					label="管理处"
					align="center"
					prop="managementMaintenanceName"
					min-width="140"
					show-overflow-tooltip
				/>
				<el-table-column
					label="管养分处"
					align="center"
					prop="managementMaintenanceBranchName"
					min-width="140"
					show-overflow-tooltip
				/>
				<el-table-column
					label="养护路段"
					align="center"
					prop="maintenanceSectionName"
					min-width="140"
					show-overflow-tooltip
				/>
				<el-table-column
					label="路线编码"
					align="center"
					prop="routeCode"
					min-width="140"
					show-overflow-tooltip
				/>
				<el-table-column
					label="运营状态"
					align="center"
					prop="operationStateName"
					min-width="130"
					show-overflow-tooltip
				>
					<template slot-scope="{ row }">
						<el-link
							:underline="false"
							:type="{ 1: 'info', 2: 'success', 3: 'danger', 4: 'primary' }[row.operationState]"
						>
							<DictTag :value="row.operationState" :options="dict.type.sys_operation_state" />
						</el-link>
					</template>
				</el-table-column>
				<el-table-column label="桩号范围" align="center" min-width="200" show-overflow-tooltip>
					<template slot-scope="scope">
						{{ formatPile(scope.row.startStake) }} ~ {{ formatPile(scope.row.endStake) }}
					</template>
				</el-table-column>
				<el-table-column
					label="方向"
					align="center"
					prop="direction"
					min-width="140"
					show-overflow-tooltip
				>
					<template slot-scope="scope">
						<dict-tag :options="dict.type.sys_route_direction" :value="scope.row.direction" />
					</template>
				</el-table-column>
				<el-table-column
					label="位置"
					align="center"
					prop="lane"
					min-width="140"
					show-overflow-tooltip
				>
					<template slot-scope="scope">
						<dict-tag :options="dict.type.lane" :value="scope.row.lane" />
					</template>
				</el-table-column>
				<el-table-column
					label="经度"
					align="center"
					min-width="140"
					show-overflow-tooltip
					prop="longitude"
				></el-table-column>
				<el-table-column
					label="纬度"
					align="center"
					min-width="140"
					show-overflow-tooltip
					prop="latitude"
				></el-table-column>
				<el-table-column
					label="施工里程桩号"
					align="center"
					prop="constructionStake"
					min-width="140"
					show-overflow-tooltip
				>
					<template slot-scope="scope">
						{{ formatPile(scope.row.constructionStake) }}
					</template>
				</el-table-column>
				<el-table-column
					label="统一里程桩号"
					align="center"
					prop="unifiedMileageStake"
					min-width="140"
					show-overflow-tooltip
				>
					<template slot-scope="scope">
						{{ formatPile(scope.row.unifiedMileageStake) }}
					</template>
				</el-table-column>
				<el-table-column
					v-for="item in tableHead"
					:key="item.id"
					:label="item.alias"
					align="center"
					:prop="item.columnName"
					min-width="140"
					show-overflow-tooltip
				/>
				<el-table-column
					label="备注"
					align="center"
					prop="remark"
					min-width="140"
					show-overflow-tooltip
				/>
				<el-table-column label="图片" align="center">
					<template #default="{ row }">
						<el-link
							:underline="false"
							type="primary"
							:disabled="row.picPath ? false : true"
							@click.stop="previewImg(row)"
						>
							查看
						</el-link>
					</template>
				</el-table-column>
			</el-table>
			<pagination
				:total="total"
				:page.sync="queryParams.pageNum"
				:limit.sync="queryParams.pageSize"
				:pageSizes="[10, 20, 30, 50, 100, 1000]"
				@pagination="getList"
			/>
			<div slot="footer">
				<el-button @click="handleClose">取 消</el-button>
			</div>
		</el-dialog>
		<Dialog title="查看图片" width="500px" :show.sync="imgShow">
			<ImagePreview :owner-id="imageUrl" width="100%" height="100%" />
		</Dialog>
	</div>
</template>

<script>
import {
	getListPage,
	getAssetSubclass,
	getDynamicData,
} from '@/api/baseData/facility/baseInfo/index'

import CascadeSelection from '@/components/CascadeSelection/index.vue'
import rangeInput from '@/views/baseData/components/rangeInput/index.vue'

import { statusListDialog } from '@/views/baseData/components/statusDialog/list.js'
import Dialog from '@/components/Dialog/index.vue'

export default {
	name: 'ancillary-baseInfo',
	components: {
		CascadeSelection,
		rangeInput,
		Dialog,
	},
	dicts: ['sys_route_type', 'sys_operation_state', 'sys_route_direction', 'lane'],
	props: {
		showDetail: { type: Boolean, default: false },
		routeCode: { type: undefined, default: '' },
		typeId: { type: undefined, default: '' },
		maintenanceSectionId: { type: undefined, default: '' },
		managementMaintenanceBranchId: { type: undefined, default: '' },
		managementMaintenanceBranchIds: { type: undefined, default: '' },
		routeCodes: { type: undefined, default: '' },
		maintenanceSectionIds: { type: undefined, default: '' },
	},
	data() {
		return {
			loading: true,
			showAddEdit: false,
			forView: false,
			title: '',

			assetSubclassList: [],
			tableHead: [],
			ids: [],
			aseetIds: [],
			total: 0,
			tableData: [],
			queryParams: {
				pageNum: 1,
				pageSize: 20,
				mainTypeId: 7,
				operationState: '2',
			},
			imgShow: false,
			imageUrl: '',
		}
	},
	watch: {},
	created() {
		this.getList()
		this.getDynamicList()
	},
	methods: {
		// 获取表格数据
		getList() {
			this.loading = true

			this.queryParams.routeCode = this.routeCode
			this.queryParams.typeId = this.typeId
			this.queryParams.maintenanceSectionId = this.maintenanceSectionId
			this.queryParams.managementMaintenanceBranchId = this.managementMaintenanceBranchId
			this.queryParams.managementMaintenanceBranchIds = this.managementMaintenanceBranchIds
			// this.queryParams.routeCodes = this.routeCodes
			// this.queryParams.maintenanceSectionIds = this.maintenanceSectionIds
			getListPage(this.queryParams)
				.then((response) => {
					this.tableData = response.rows
					this.total = response.total
					this.loading = false
				})
				.catch(() => {
					this.loading = false
				})
		},

		handleClose() {
			this.$emit('close')
		},
		// 多选框选中数据
		handleSelectionChange(selection) {
			this.ids = selection.map((item) => item.id)
			this.aseetIds = selection.map((item) => item.aseetId)
		},
		// 表格点击勾选
		handleRowClick(row) {
			// row.isSelected = !row.isSelected
			// this.$refs.table.toggleRowSelection(row)
		},
		// 勾选高亮
		rowStyle({ row, rowIndex }) {
			if (this.ids.includes(row.id)) {
				return { 'background-color': '#b7daff', color: '#333' }
			} else {
				return { 'background-color': '#fff', color: '#333' }
			}
		},

		// 导出按钮
		exportList() {
			if (this.ids.length === 0) {
				this.$modal
					.confirm('即将导出所有表格数据，此过程可能花费时间较长，是否继续？')
					.then(() => {
						this.download(
							'/baseData/facility/export',
							this.queryParams,
							`facility_${new Date().getTime()}.xlsx`,
							{
								headers: { 'Content-Type': 'application/json;' },
								parameterType: 'body',
							}
						)
					})
					.catch(() => {})
			} else {
				this.$modal
					.confirm(`已选择${this.ids.length}条绿化数据，确认导出？`)
					.then(() => {
						this.download(
							'/baseData/facility/export',
							{ ids: this.ids, mainTypeId: 6 },
							`facility__${new Date().getTime()}.xlsx`,
							{
								headers: { 'Content-Type': 'application/json;' },
								parameterType: 'body',
							}
						)
					})
					.catch(() => {})
			}
		},

		// 搜索按钮
		handleQuery() {
			this.queryParams.pageNum = 1
			this.getList()
			this.getDynamicList()
		},
		// 查看图片
		previewImg(row) {
			this.imgShow = true
			this.imageUrl = row.picPath
		},

		getDynamicList() {
			if (this.queryParams.typeId) {
				getDynamicData({
					mainTypeId: this.queryParams.mainTypeId,
					typeId: this.queryParams.typeId,
				}).then((res) => {
					if (res.code == 200) {
						this.tableHead = res.data
						this.$nextTick(() => {
							this.$refs.table.doLayout()
						})
					}
				})
			}
		},
		// 重置按钮
		resetQuery() {
			this.queryParams = {
				pageNum: 1,
				pageSize: 20,
				mainTypeId: 7,
				operationState: '2',
			}
			this.handleQuery()
		},

		getDynamicSelect(val) {
			let data = this.assetSubclassList.find((item) => item.id == this.queryParams.typeId)
			this.queryParams.mainTypeId = data.mainType
		},

		// 表格操作-运营状态
		handleOperational(event, row) {
			event.stopPropagation()
			statusListDialog({ dataId: row.id, baseDataType: 24 })
		},
	},
}
</script>

<style lang="scss" scoped></style>
