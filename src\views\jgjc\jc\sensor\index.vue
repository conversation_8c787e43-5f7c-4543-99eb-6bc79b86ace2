<template>
  <div class="app-container">
    <el-row :gutter="20">
      <el-col :span="4" :xs="4">
        <el-tree node-key="id" :data="treeList" @current-change="tree_current_change" :expand-on-click-node="false"
                 highlight-current default-expand-all></el-tree>
      </el-col>
      <!--筛选区开始-->
      <el-col :span="20" :xs="20">
        <el-row>
          <el-col :span="24">
            <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" label-width="90px">
              <el-form-item label="监测内容：" prop="monitoringContentIdDisplay">
                <el-input
                  v-model="queryParams.monitoringContentIdDisplay"
                  placeholder="请输入监测内容"
                  :readonly="true"
                  prefix-icon="el-icon-edit"
                  style="width: 240px">
                  <el-button slot="append" icon="el-icon-search"
                             @click="onSelectMonitoringContentQueryShow"></el-button>
                </el-input>
              </el-form-item>
              <el-form-item label="设备型号：" prop="deviceModelIdDisplay">
                <el-input
                  v-model="queryParams.deviceModelIdDisplay"
                  placeholder="请输入设备型号"
                  :readonly="true"
                  prefix-icon="el-icon-edit"
                  style="width: 240px">
                  <el-button slot="append" icon="el-icon-search" @click="onSelectDeviceModelQueryShow"></el-button>
                </el-input>
              </el-form-item>
              <el-form-item label="数据编码：" prop="dataCode">
                <el-input
                  v-model="queryParams.dataCode"
                  placeholder="请输入数据编码"
                  clearable
                  prefix-icon="el-icon-edit"
                  style="width: 240px"
                  maxlength="200" show-word-limit
                  @keyup.enter.native="handleQuery"
                />
              </el-form-item>
              <el-form-item>
                <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
                <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
                <el-button v-show="!showSearch" @click="showSearch=true" icon="el-icon-arrow-down" circle></el-button>
                <el-button v-show="showSearch" @click="showSearch=false" icon="el-icon-arrow-up" circle></el-button>
              </el-form-item>
            </el-form>
            <!--默认折叠-->
          </el-col>
          <!--默认折叠 ,此处仅作为示例-->
          <el-col :span="24">
            <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch"
                     label-width="110px">
              <el-form-item label="编码：" prop="code">
                <el-input
                  v-model="queryParams.code"
                  placeholder="请输入编码"
                  clearable
                  prefix-icon="el-icon-edit"
                  style="width: 240px"
                  maxlength="200" show-word-limit
                  @keyup.enter.native="handleQuery"
                />
              </el-form-item>
              <el-form-item label="位置：" prop="position">
                <el-input
                  v-model="queryParams.position"
                  placeholder="请输入位置"
                  clearable
                  prefix-icon="el-icon-edit"
                  style="width: 240px"
                  maxlength="200" show-word-limit
                  @keyup.enter.native="handleQuery"
                />
              </el-form-item>
              <el-form-item label="是否启用" prop="enabled">
                <el-select
                  v-model="queryParams.enabled"
                  placeholder="请输入是否启用"
                  prefix-icon="el-icon-edit"
                  style="width: 240px"
                >
                  <el-option
                    v-for="d in yesNoList"
                    :key="d.id"
                    :label="d.label"
                    :value="d.id"
                  />
                </el-select>
              </el-form-item>
              <el-form-item label="隐藏" prop="hideSensor">
                <el-select
                  v-model="queryParams.hideSensor"
                  placeholder="请输入隐藏"
                  prefix-icon="el-icon-edit"
                  style="width: 240px"
                >
                  <el-option
                    v-for="d in yesNoList"
                    :key="d.id"
                    :label="d.label"
                    :value="d.id"
                  />
                </el-select>
              </el-form-item>
              <el-form-item label="检查在线状态：" prop="checkOnline">
                <el-select
                  v-model="queryParams.checkOnline"
                  placeholder="请输入检查在线状态"
                  prefix-icon="el-icon-edit"
                  style="width: 240px"
                >
                  <el-option
                    v-for="d in yesNoList"
                    :key="d.id"
                    :label="d.label"
                    :value="d.id"
                  />
                </el-select>
              </el-form-item>
              <el-form-item label="说明" prop="description">
                <el-input
                  v-model="queryParams.description"
                  placeholder="请输入说明"
                  clearable
                  prefix-icon="el-icon-edit"
                  style="width: 240px"
                  maxlength="200" show-word-limit
                  @keyup.enter.native="handleQuery"
                />
              </el-form-item>
            </el-form>
          </el-col>
        </el-row>
        <!--筛选区结束-->
        <!--操作按钮区开始-->
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button
              type="primary"
              icon="el-icon-plus"
              size="mini"
              @click="handleAdd"
              v-hasPermi="['system:sensor:add']"
            >新增
            </el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button
              type="success"
              icon="el-icon-edit"
              size="mini"
              :disabled="single"
              @click="handleUpdate"
              v-hasPermi="['system:sensor:edit']"
            >修改
            </el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button
              type="danger"
              icon="el-icon-delete"
              size="mini"
              :disabled="multiple"
              @click="handleDelete"
              v-hasPermi="['system:sensor:remove']"
            >删除
            </el-button>
          </el-col>
          <right-toolbar :showSearch.sync="showSearch" @queryTable="getList" :columns="columns"></right-toolbar>
        </el-row>
        <!--操作按钮区结束-->
        <!--数据表格开始-->
        <div class="tableDiv">
          <el-table
            ref="table"
            size="mini"
            :height="showSearch ? 'calc(100vh - 320px)' : 'calc(100vh - 260px)'"
            style="width: 100%"
            v-loading="loading"
            border
            :data="sensorList"
            @selection-change="handleSelectionChange"
            :row-style="rowStyle"
            @row-click="handleRowClick"
            @expand-change="onExpandChange"
          >
            <el-table-column type="selection" width="50" align="center" />
            <el-table-column fixed align="right" type="index" width="50" />
            <el-table-column type="expand">
              <template slot-scope="props">
                <el-row>
                  <el-button
                    type="primary"
                    icon="el-icon-plus"
                    size="mini"
                    @click="handleAddSensorValueType(props.row)"
                    v-hasPermi="['system:sensor:add']"
                  >新增
                  </el-button>
                </el-row>
                <el-row>
                  <el-table size="mini" border :data="props.row.sensorValueTypeList">
                    <el-table-column fixed align="right" type="index" width="50" />
                    <el-table-column label="编码" align="left" header-align="center" prop="code" width="120"
                                     :show-overflow-tooltip="true" />
                    <el-table-column label="名称" align="left" header-align="center" prop="name" width="120"
                                     :show-overflow-tooltip="true" />
                    <el-table-column label="单位" align="left" header-align="center" prop="unit" width="80"
                                     :show-overflow-tooltip="true" />
                    <el-table-column label="小数位数" align="right" header-align="center" prop="numericScale" width="70"
                                     :show-overflow-tooltip="true" />
                    <el-table-column label="默认显示" align="center" header-align="center" prop="defaultShow" width="80">
                      <template slot-scope="crow">
                        <el-tag v-if="crow.row.defaultShow" type="success">是</el-tag>
                        <el-tag v-if="!crow.row.defaultShow" type="danger">否</el-tag>
                      </template>
                    </el-table-column>
                    <el-table-column label="显示顺序" align="right" header-align="center" prop="showOrders" width="80"
                                     :show-overflow-tooltip="true" />
                    <el-table-column label="存储类型" align="left" header-align="center" prop="storageType" width="80"
                                     :show-overflow-tooltip="true" />
                    <el-table-column label="默认采样类型名称" align="left" header-align="center" prop="defaultSampleTypeName" width="140" :show-overflow-tooltip="true" />

                    <el-table-column label="说明" align="left" header-align="center" prop="description" width="150" :show-overflow-tooltip="true" />
                    <el-table-column label="序号" align="right" header-align="center" prop="orders" width="50"/>
                    <el-table-column
                      label="操作"
                      fixed="right"
                      align="center"
                      width="160"
                      class-name="small-padding fixed-width"
                    >
                      <template slot-scope="crow" v-if="crow.row.userId !== 1">
                        <el-button
                          size="mini"
                          type="text"
                          icon="el-icon-edit"
                          @click="handleUpdateValueType(crow.row)"
                          v-hasPermi="['system:sensor:edit']"
                        >修改
                        </el-button>
                        <el-button
                          size="mini"
                          type="text"
                          icon="el-icon-delete"
                          @click="handleDeleteValueType(crow.row)"
                          v-hasPermi="['system:sensor:remove']"
                        >删除
                        </el-button>
                      </template>
                    </el-table-column>
                  </el-table>
                </el-row>
              </template>
            </el-table-column>
            <el-table-column label="结构物" align="left" header-align="center" prop="structureName" width="150"
                             :show-overflow-tooltip="true" />
            <el-table-column label="监测内容" align="left" header-align="center" prop="monitoringContentIdDisplay"
                             width="200" :show-overflow-tooltip="true" />
            <el-table-column label="设备型号" align="left" header-align="center" prop="deviceModelIdDisplay" width="150"
                             :show-overflow-tooltip="true" />
            <el-table-column label="数据编码" align="left" header-align="center" prop="dataCode" width="200"
                             :show-overflow-tooltip="true" />
            <el-table-column label="编码" align="left" header-align="center" prop="code" width="150"
                             :show-overflow-tooltip="true" />
            <el-table-column label="位置" align="left" header-align="center" prop="position" width="200"
                             :show-overflow-tooltip="true" />
            <el-table-column label="启用" align="center" header-align="center" prop="enabled" width="50">
              <template slot-scope="scope">
                <el-tag v-if="scope.row.enabled" type="success">是</el-tag>
                <el-tag v-if="!scope.row.enabled" type="danger">否</el-tag>
              </template>
            </el-table-column>
            <el-table-column label="隐藏" align="center" header-align="center" prop="hideSensor" width="50">
              <template slot-scope="scope">
                <el-tag v-if="scope.row.hideSensor" type="success">是</el-tag>
                <el-tag v-if="!scope.row.hideSensor" type="danger">否</el-tag>
              </template>
            </el-table-column>
            <el-table-column label="检查在线状态" align="center" header-align="center" prop="checkOnline" width="80">
              <template slot-scope="scope">
                <el-tag v-if="scope.row.checkOnline" type="success">是</el-tag>
                <el-tag v-if="!scope.row.checkOnline" type="danger">否</el-tag>
              </template>
            </el-table-column>
            <el-table-column label="说明" align="left" header-align="center" prop="description" width="200"
                             :show-overflow-tooltip="true" />
            <el-table-column label="序号" align="right" header-align="center" prop="orders" width="50" />
            <el-table-column
              label="操作"
              fixed="right"
              align="center"
              width="160"
              class-name="small-padding fixed-width"
            >
              <template slot-scope="scope" v-if="scope.row.userId !== 1">
                <el-button
                  size="mini"
                  type="text"
                  icon="el-icon-edit"
                  @click="handleUpdate(scope.row)"
                  v-hasPermi="['system:sensor:edit']"
                >修改
                </el-button>
                <el-button
                  size="mini"
                  type="text"
                  icon="el-icon-delete"
                  @click="handleDelete(scope.row)"
                  v-hasPermi="['system:sensor:remove']"
                >删除
                </el-button>
              </template>
            </el-table-column>
          </el-table>

          <pagination
            v-show="total>0"
            :total="total"
            :page.sync="queryParams.pageNum"
            :limit.sync="queryParams.pageSize"
            @pagination="getList"
          />
        </div>
        <!--数据表格结束-->
      </el-col>
    </el-row>

    <!-- 添加或修改传感器对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="700px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="110px">
        <el-col :span="24">
          <el-form-item label="结构物" prop="structureName">
            <el-input v-model="form.structureName" :readonly="true" placeholder="请输入结构物">
              <el-button slot="append" icon="el-icon-search" @click="onStructureIdEditShow"></el-button>
            </el-input>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="监测内容" prop="monitoringContentIdDisplay">
            <el-input v-model="form.monitoringContentIdDisplay" :readonly="true" placeholder="请输入监测内容">
              <el-button slot="append" icon="el-icon-search" @click="onSelectMonitoringContentEditShow"></el-button>
            </el-input>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="设备型号" prop="deviceModelIdDisplay">
            <el-input v-model="form.deviceModelIdDisplay" placeholder="请输入设备型号" :readonly="true">
              <el-button slot="append" icon="el-icon-search" @click="onSelectDeviceModelEditShow"></el-button>
            </el-input>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="数据编码" prop="dataCode">
            <el-input v-model="form.dataCode" placeholder="请输入数据编码" maxlength="200" show-word-limit />
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="传感器编码" prop="code">
            <el-input v-model="form.code" placeholder="请输入编码" maxlength="200" show-word-limit />
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="位置" prop="position">
            <el-input v-model="form.position" placeholder="请输入位置" maxlength="200" show-word-limit />
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="启用" prop="enabled">
            <el-switch v-model="form.enabled">
            </el-switch>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="隐藏" prop="hideSensor">
            <el-switch v-model="form.hideSensor">
            </el-switch>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="检查在线状态" prop="checkOnline">
            <el-switch v-model="form.checkOnline">
            </el-switch>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="序号" prop="orders">
            <el-input-number v-model="form.orders" placeholder="请输入序号" style="width:100%;"
                             controls-position="right" :min="1" :step="1" :max="1000000"></el-input-number>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="说明" prop="description">
            <el-input v-model="form.description" type="textarea" :rows="5" placeholder="请输入内容" maxlength="10000"
                      show-word-limit />
          </el-form-item>
        </el-col>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
    <SelectStructure ref="selectStructureEditRef" @select="selectStructureEdit"></SelectStructure>
    <SelectMonitoringContent ref="selectMonitoringContentEditRef"
                             @select="selectMonitoringContentEdit"></SelectMonitoringContent>
    <SelectDeviceModel ref="selectDeviceModelEditRef" @select="selectDeviceModelEdit"></SelectDeviceModel>

    <SelectMonitoringContent ref="selectMonitoringContentQueryRef"
                             @select="selectMonitoringContentQuery"></SelectMonitoringContent>
    <SelectDeviceModel ref="selectDeviceModelQueryRef" @select="selectDeviceModelQuery"></SelectDeviceModel>

    <SelectValueType ref="selectValueTypeEditRef" @select="selectValueTypeEdit"></SelectValueType>

    <!-- 添加或修改传感器值类型对话框 -->
    <el-dialog :title="valueTypeTitle" :visible.sync="valueTypeOpen" width="700px" append-to-body>
      <el-form ref="valueTypeForm" :model="valueTypeForm" :rules="valueTypeRules" label-width="90px">
        <el-col :span="24">
          <el-form-item label="编码" prop="code">
            <el-input v-model="valueTypeForm.code" :readonly="true" placeholder="请输入编码">
              <el-button slot="append" icon="el-icon-search" @click="onSelectValueTypeEditShow"></el-button>
            </el-input>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="名称" prop="name">
            <el-input v-model="valueTypeForm.name" placeholder="请输入名称" />
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="单位" prop="unit">
            <el-input v-model="valueTypeForm.unit" placeholder="请输入单位" />
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="小数位数" prop="numericScale">
            <el-input v-model="valueTypeForm.numericScale" placeholder="请输入小数位数" />
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="默认显示" prop="defaultShow">
            <el-input v-model="valueTypeForm.defaultShow" placeholder="请输入默认显示" />
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="显示顺序" prop="showOrders">
            <el-input v-model="valueTypeForm.showOrders" placeholder="请输入显示顺序" />
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="存储类型" prop="storageType">
            <el-select
              v-model="valueTypeForm.storageType"
              placeholder="请输入存储类型"
              prefix-icon="el-icon-edit"
              style="width: 100%;"
            >
              <el-option
                v-for="d in storageTypeList"
                :key="d.id"
                :label="d.label"
                :value="d.id"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="默认采样类型名称" prop="defaultSampleTypeName">
            <el-select
              v-model="valueTypeForm.defaultSampleTypeName"
              placeholder="请输入默认采样类型名称"
              prefix-icon="el-icon-edit"
              style="width: 100%;"
            >
              <el-option
                v-for="d in sampleTypeList"
                :key="d.name"
                :label="d.title"
                :value="d.name"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="序号" prop="orders">
            <el-input v-model="valueTypeForm.orders" placeholder="请输入序号" />
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="说明" prop="description">
            <el-input v-model="valueTypeForm.description" type="textarea" :rows="5" placeholder="请输入内容" />
          </el-form-item>
        </el-col>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="valueTypeSubmitForm">确 定</el-button>
        <el-button @click="valueTypeCancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { addSensor, delSensor, getSensor, listSensor, updateSensor } from '@/api/jgjc/jc/sensor'
import {
  getSensorValueType,
  delSensorValueType,
  addSensorValueType,
  getSensorValueTypeFindBySensorId,
  updateSensorValueType,
} from '@/api/jgjc/jc/sensorValueType'
import { getFindTreeStructure } from '@/api/jgjc/jc/structure'
import { getToken } from '@/utils/auth'
import '@riophae/vue-treeselect/dist/vue-treeselect.css'
import SelectStructure from '@/views/jgjc/jc/structure/selectStructure.vue'
import SelectMonitoringContent from '@/views/jgjc/jc/monitoring-content/selectMonitoringContent.vue'
import SelectDeviceModel from '@/views/jgjc/jc/device-model/selectDeviceModel.vue'
import SelectValueType from '@/views/jgjc/jc/value-type/selectValueType.vue'
import { getFindAllSampleType } from '@/api/jgjc/jc/sampleType'

export default {
  name: 'Sensor',
  components: { SelectStructure, SelectMonitoringContent, SelectDeviceModel, SelectValueType },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: false,
      dictType: [],
      // 总条数
      total: 0,
      // 传感器表格数据
      sensorList: null,
      // 弹出层标题
      title: '',
      // 部门树选项
      deptOptions: undefined,
      // 是否显示弹出层
      open: false,
      treeList: [],
      // 表单参数
      form: {},
      defaultProps: {
        children: 'children',
        label: 'label',
      },
      yesNoList: [{ id: 'true', label: '是' }, { id: 'false', label: '否' }],
      // 用户导入参数
      upload: {
        // 是否显示弹出层（用户导入）
        open: false,
        // 弹出层标题（用户导入）
        title: '',
        // 是否禁用上传
        isUploading: false,
        // 是否更新已经存在的用户数据
        updateSupport: 0,
        // 设置上传的请求头部
        headers: { Authorization: 'Bearer ' + getToken() },
        // 上传的地址
        url: process.env.VUE_APP_BASE_API + '/system/user/importData',
      },
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 50,
        structureId: null,
        monitoringContentId: null,
        monitoringContentIdDisplay: null,
        deviceModelId: null,
        deviceModelIdDisplay: null,
        dataCode: null,
        code: null,
        position: null,
        enabled: null,
        hideSensor: null,
        checkOnline: null,
        description: null,
        orderDirection: 'ASC',
      },
      // 列信息
      columns: [
        { key: 0, label: `结构物`, visible: true },
        { key: 1, label: `监测内容`, visible: true },
        { key: 2, label: `设备型号`, visible: true },
        { key: 3, label: `数据编码`, visible: true },
        { key: 4, label: `编码`, visible: true },
        { key: 5, label: `位置`, visible: true },
        { key: 6, label: `启用`, visible: true },
        { key: 7, label: `隐藏`, visible: true },
        { key: 8, label: `检查在线状态`, visible: true },
        { key: 9, label: `说明`, visible: true },
        { key: 10, label: `序号`, visible: true },
      ],
      // 表单校验
      rules: {
        structureName: [
          { required: true, message: '结构物不能为空', trigger: 'blur' },
        ],
        monitoringContentIdDisplay: [
          { required: true, message: '监测内容不能为空', trigger: 'blur' },
        ],
        deviceModelIdDisplay: [
          { required: true, message: '设备型号不能为空', trigger: 'blur' },
        ],
        dataCode: [
          { required: true, message: '数据编码不能为空', trigger: 'blur' },
        ],
        code: [
          { required: true, message: '编码不能为空', trigger: 'blur' },
        ],
        position: [
          { required: true, message: '位置不能为空', trigger: 'blur' },
        ],
        orders: [
          { required: true, message: '序号不能为空', trigger: 'blur' },
        ],
      },
      storageTypeList: [{ id: 'common', label: '普通数据' }, { id: 'jsd', label: '动态大数据' }, {
        id: 'vehicle-load',
        label: '车辆荷载数据',
      }],
      sampleTypeList: [],
      valueTypeTitle: '',
      valueTypeOpen: false,
      valueTypeForm: {},
      sensorRow:null,
      valueTypeRules: {
        code: [
          { required: true, message: '编码不能为空', trigger: 'blur' },
        ],
        name: [
          { required: true, message: '名称不能为空', trigger: 'blur' },
        ],
        unit: [
          { required: true, message: '单位不能为空', trigger: 'blur' },
        ],
        numericScale: [
          { required: true, message: '小数位数不能为空', trigger: 'blur' },
        ],
        defaultShow: [
          { required: true, message: '默认显示不能为空', trigger: 'blur' },
        ],
        showOrders: [
          { required: true, message: '显示顺序不能为空', trigger: 'blur' },
        ],
        storageType: [
          { required: true, message: '存储类型不能为空', trigger: 'blur' },
        ],
        defaultSampleTypeName: [
          { required: true, message: '默认采样类型名称不能为空', trigger: 'blur' },
        ],
        orders: [
          { required: true, message: '序号不能为空', trigger: 'blur' },
        ],
      },
    }
  },
  watch: {
    // 根据名称筛选部门树
  },
  created() {
    this.getOtherData()
    this.getList()
    // this.getDeptTree();
    // this.getConfigKey("sys.user.initPassword").then(response => {
    //   this.initPassword = response.msg;
    // });
  },
  methods: {
    getOtherData() {
      getFindTreeStructure().then(response => {
        this.treeList = response.data
        this.loading = false
      })
      getFindAllSampleType().then(response => {
        this.sampleTypeList = response.data
        this.loading = false
      })
    },
    /** 查询用户列表 */
    getList() {
      this.loading = true
      listSensor(this.queryParams).then(response => {
        for(var i=0;i<response.data.length;i++){
          response.data[i].sensorValueTypeList=[]
        }
        this.sensorList = response.data
        this.total = response.total
        this.loading = false
      })
    },
    tree_current_change(data, node) {
      this.queryParams.structureId = data.id
      this.getList()
    },
    // 取消按钮
    cancel() {
      this.open = false
      this.reset()
    },
    // 表单重置
    reset() {
      this.form = {
        structureId: null,
        structureName: null,
        monitoringContentId: null,
        monitoringContentIdDisplay: null,
        deviceModelId: null,
        deviceModelIdDisplay: null,
        dataCode: '',
        code: '',
        position: '',
        enabled: true,
        hideSensor: false,
        checkOnline: true,
        orders: 1,
        description: '',
      }
      this.resetForm('form')
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dateRange = []
      this.resetForm('queryForm')
      this.queryParams.structureId = null
      this.queryParams.monitoringContentId = null
      this.queryParams.monitoringContentIdDisplay = null
      this.queryParams.deviceModelId = null
      this.queryParams.deviceModelIdDisplay = null
      this.queryParams.dataCode = null
      this.queryParams.code = null
      this.queryParams.position = null
      this.queryParams.enabled = null
      this.queryParams.hideSensor = null
      this.queryParams.checkOnline = null
      this.queryParams.description = null
      this.handleQuery()
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length != 1
      this.multiple = !selection.length
    },
    // 表格点击勾选
    handleRowClick(row) {
      row.isSelected = !row.isSelected
      this.$refs.table.toggleRowSelection(row)
    },
    // 勾选高亮
    rowStyle({ row, rowIndex }) {
      if (this.ids.includes(row.id)) {
        return { 'background-color': '#E1F0FF', color: '#333' }
      } else {
        return { 'background-color': '#fff', color: '#333' }
      }
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset()
      this.open = true
      this.title = '添加传感器'
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset()
      const id = row.id || this.ids
      getSensor(id).then(response => {
        this.form = response.data
        this.open = true
        this.title = '修改传感器'
      })
    },
    /** 提交按钮 */
    submitForm: function() {
      this.$refs['form'].validate(valid => {
        if (valid) {
          if (this.form.id != null) {
            updateSensor(this.form).then(response => {
              this.$modal.msgSuccess('修改成功')
              this.open = false
              this.getList()
            })
          } else {
            addSensor(this.form).then(response => {
              this.$modal.msgSuccess('新增成功')
              this.open = false
              this.getList()
            })
          }
        }
      })
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const id = row.id || this.ids
      this.$modal.confirm('是否确认删除传感器编号为"' + id + '"的数据项？').then(function() {
        return delSensor(id)
      }).then(() => {
        this.getList()
        this.$modal.msgSuccess('删除成功')
      }).catch(() => {
      })
    },
    onStructureIdEditShow() {
      this.$refs.selectStructureEditRef.show()
    },
    selectStructureEdit(val) {
      this.form.structureId = val.id
      this.form.structureName = val.label
    },
    onSelectMonitoringContentEditShow() {
      this.$refs.selectMonitoringContentEditRef.show()
    },
    selectMonitoringContentEdit(val) {
      this.form.monitoringContentId = val.id
      this.form.monitoringContentIdDisplay = val.label
    },
    onSelectDeviceModelEditShow() {
      this.$refs.selectDeviceModelEditRef.show()
    },
    selectDeviceModelEdit(val) {
      this.form.deviceModelId = val.id
      this.form.deviceModelIdDisplay = val.name
    },

    onSelectMonitoringContentQueryShow() {
      this.$refs.selectMonitoringContentQueryRef.show()
    },
    selectMonitoringContentQuery(val) {
      this.queryParams.monitoringContentId = val.id
      this.queryParams.monitoringContentIdDisplay = val.label
    },
    onSelectDeviceModelQueryShow() {
      this.$refs.selectDeviceModelQueryRef.show()
    },
    selectDeviceModelQuery(val) {
      this.queryParams.deviceModelId = val.id
      this.queryParams.deviceModelIdDisplay = val.name
    },
    onExpandChange(row, exp) {
      var findRow = null
      for (var i = 0; i < exp.length; i++) {
        if (row.id == exp[i].id) {
          findRow = exp[i]
          break
        }
      }
      if (findRow == null) {
        return
      }
      this.sensorRow = row
      this.getValueTypeList()
    },
    getValueTypeList() {
      getSensorValueTypeFindBySensorId(this.sensorRow.id).then(response => {
        this.sensorRow.sensorValueTypeList = response.data
      })
    },
    valueTypeReset() {
      this.valueTypeForm = {
        sensorId: null,
        valueTypeId: null,
        code: null,
        name: null,
        unit: null,
        numericScale: null,
        defaultShow: null,
        showOrders: null,
        storageType: null,
        defaultSampleTypeName: null,
        orders: 1,
        description: '',
      }
    },
    /** 修改按钮操作 */
    handleUpdateValueType(row) {
      this.reset()
      const id = row.id || this.ids
      getSensor(id).then(response => {
        this.form = response.data
        this.open = true
        this.title = '修改传感器'
      })
    },
    /** 删除按钮操作 */
    handleDeleteValueType(row) {
      const id = row.id || this.ids
      this.$modal.confirm('是否确认删除传感器值类型"' + row.name + '"的数据项？').then(function() {
        return delSensorValueType(id)
      }).then(() => {
        this.sensorRow = row
        this.getList()
        this.$modal.msgSuccess('删除成功')
      }).catch(() => {
      })
    },
    handleAddSensorValueType(row) {
      this.valueTypeReset()
      this.resetForm('valueTypeForm')
      this.valueTypeOpen = true
      this.valueTypeTitle = '添加传感器值类型'
      this.valueTypeForm.sensorId = row.id
      this.sensorRow = row
    },

    // 取消按钮
    valueTypeCancel() {
      this.valueTypeOpen = false
      this.valueTypeReset()
    },
    valueTypeSubmitForm() {
      this.$refs['valueTypeForm'].validate(valid => {
        if (valid) {
          if (this.valueTypeForm.id != null) {
            updateSensorValueType(this.valueTypeForm).then(response => {
              this.$modal.msgSuccess('修改成功')
              this.valueTypeOpen = false
              // this.$refs.table.toggleRowExpansion(this.sensorRow,false)
              // this.$refs.table.toggleRowExpansion(this.sensorRow,true)
              this.getValueTypeList()
            })
          } else {
            addSensorValueType(this.valueTypeForm).then(response => {
              this.$modal.msgSuccess('新增成功')
              this.valueTypeOpen = false
              // this.$refs.table.toggleRowExpansion(this.sensorRow,false)
              // this.$refs.table.toggleRowExpansion(this.sensorRow,true)
              this.getValueTypeList()
            })
          }
        }
      })
    },
    onSelectValueTypeEditShow() {
      this.$refs.selectValueTypeEditRef.show()
    },
    selectValueTypeEdit(val) {
      this.valueTypeForm.valueTypeId = val.id
      this.valueTypeForm.code = val.code
      this.valueTypeForm.name = val.name
      this.valueTypeForm.unit = val.unit
      this.valueTypeForm.numericScale = val.numericScale
      this.valueTypeForm.defaultShow = val.defaultShow
      this.valueTypeForm.showOrders = val.showOrders
      this.valueTypeForm.storageType = val.storageType
      this.valueTypeForm.defaultSampleTypeName = val.defaultSampleTypeName
    },
  },
}
</script>
<style>
.hasTagsView .app-main[data-v-078753dd] {
  background: #f5f7fa;
}

.tableDiv {
  background-color: white;
  padding-bottom: 10px;
}
</style>
