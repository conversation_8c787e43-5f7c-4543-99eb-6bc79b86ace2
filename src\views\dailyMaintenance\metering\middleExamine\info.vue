<template>
  <div class="road-interflow-edit">
    <el-tabs>
      <el-tab-pane label="事件信息">
        <el-row :gutter="15">
          <el-col :span="24">
            <el-form
              ref="queryForm"
              :model="queryParams"
              size="mini"
              :inline="true"
              label-width="68px"
            >
              <el-form-item prop="disType">
                <el-select v-model="queryParams.disType" filterable placeholder="请选择事件类型" clearable style="width: 190px;">
                  <el-option v-for="item in advicesList"
                             :key="item.value"
                             :label="item.label"
                             :value="item.value">
                  </el-option>
                </el-select>
              </el-form-item>
              <el-form-item prop="maiSecName">
                <RoadSection style="width: 190px" v-model="queryParams.maiSecId" placeholder="路段名称"/>
              </el-form-item>
              <el-form-item>
                <RouteCodeSection style="width: 190px;" v-model="queryParams.routeCode"
                                  :maintenanceSectionId="queryParams.maiSecId" placeholder="路线编码"/>
              </el-form-item>
              <el-form-item>
                <el-input
                  v-model="queryParams.beginMile"
                  placeholder="起止桩号"
                  clearable
                  style="width: 110px"
                >
                </el-input>
                <div style="width: 20px;display: inline-block;text-align: center">~</div>
                <el-input
                  v-model="queryParams.endMile"
                  placeholder="起止桩号"
                  clearable
                  style="width: 110px"
                >
                </el-input>
              </el-form-item>
              <el-form-item>
                <el-input style="width: 190px" placeholder="子目号" v-model="queryParams.schemeCode"></el-input>
              </el-form-item>
              <el-form-item>
                <el-input style="width: 190px" placeholder="方法名" v-model="queryParams.schemeName"></el-input>
              </el-form-item>
              <el-form-item>
                <el-input style="width: 190px" placeholder="施工单编号" v-model="queryParams.code"></el-input>
              </el-form-item>
              <el-form-item>
                <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQueryLeft">搜索</el-button>
                <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
              </el-form-item>
            </el-form>
          </el-col>
        </el-row>
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button
              type="primary"
              icon="el-icon-view"
              size="mini"
              @click="openEventInfo"
            >事件信息
            </el-button
            >
          </el-col>
          <el-col :span="1.5">
            <el-button
              type="warning"
              icon="el-icon-download"
              size="mini"
              @click="exportListLeft"
            >导出清单
            </el-button
            >
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <div class="draggable">
              <el-table v-adjust-table
                size="mini"
                style="width: 100%"
                v-loading="loading"
                border
                :data="tableDataLeft"
                row-key="detailId"
                height="600"
                ref="dataTable"
                @row-click="handleClickRow"
                @selection-change="handleSelectionChange"
                @expand-change="loadData"
                stripe
                highlight-current-row
              >
                <el-table-column type="expand">
                  <template slot-scope="props">
                    <el-table v-adjust-table :data="props.row.methodList" style="width: 100%" v-loading="methodLoading">
                      <el-table-column
                        prop=""
                        align="center"
                        label="">
                      </el-table-column>
                      <el-table-column
                        prop="schemeCode"
                        align="center"
                        label="子目号">
                      </el-table-column>
                      <el-table-column
                        prop="schemeName"
                        align="center"
                        label="养护方法">
                      </el-table-column>
                      <el-table-column
                        prop="calcDesc"
                        align="center"
                        label="计算式">
                      </el-table-column>
                      <el-table-column
                        prop="num"
                        align="center"
                        label="方法数量">
                      </el-table-column>
                      <el-table-column
                        prop="unit"
                        align="center"
                        label="方法单位">
                      </el-table-column>
                      <el-table-column
                        prop="price"
                        align="center"
                        label="单价">
                      </el-table-column>
                      <el-table-column
                        prop="amount"
                        align="center"
                        label="金额">
                      </el-table-column>
                    </el-table>
                  </template>
                </el-table-column>
                <el-table-column type="selection" width="50" align="center"/>
                <el-table-column
                  label="序号"
                  align="center"
                  type="index"
                  width="50"
                />
                <template v-for="(column,index) in columnsLeft">
                  <el-table-column :label="column.label"
                                   v-if="column.visible"
                                   align="center"
                                   :prop="column.field"
                                   :width="column.width">
                    <template slot-scope="scope">
                      <dict-tag v-if="column.dict" :options="dict.type[column.dict]" :value="scope.row[column.field]"/>
                      <template v-else-if="column.slots">
                        <RenderDom :row="scope.row" :index="index" :render="column.render"/>
                      </template>
                      <span v-else-if="column.isTime">{{ parseTime(scope.row[column.field], '{y}-{m}-{d}') }}</span>
                      <span v-else>{{ scope.row[column.field] }}</span>
                    </template>
                  </el-table-column>
                </template>
              </el-table>
              <pagination
                v-show="totalLeft>0"
                :total="totalLeft"
                :page.sync="queryParams.pageNum"
                :limit.sync="queryParams.pageSize"
                @pagination="handleQueryLeft"
              />
            </div>
          </el-col>
        </el-row>
      </el-tab-pane>
      <el-tab-pane label="中间计量表">
        <el-row :gutter="15">
          <el-col :span="24">
            <el-row :gutter="10" class="mb8">
              <el-col :span="1.5">
                <el-button
                  type="primary"
                  icon="el-icon-download"
                  size="mini"
                  @click="exportListRight"
                >导出清单
                </el-button
                >
              </el-col>
            </el-row>
          </el-col>
          <el-col :span="24">
            <div class="draggable">
              <el-table v-adjust-table
                size="mini"
                style="width: 100%"
                v-loading="loading"
                border
                :data="tableDataRight"
                row-key="id"
                height="600"
                ref="dataTable2"
                stripe
                highlight-current-row
              >
                <el-table-column
                  label="序号"
                  align="center"
                  type="index"
                  width="50"
                />
                <template v-for="(column,index) in columnsRight">
                  <el-table-column :label="column.label"
                                   v-if="column.visible"
                                   align="center"
                                   :prop="column.field">
                    <template slot-scope="scope">
                      <dict-tag v-if="column.dict" :options="dict.type[column.dict]" :value="scope.row[column.field]"/>
                      <template v-else-if="column.slots">
                        <RenderDom :row="scope.row" :index="index" :render="column.render"/>
                      </template>
                      <span v-else-if="column.isTime">{{ parseTime(scope.row[column.field], '{y}-{m}-{d}') }}</span>
                      <span v-else>{{ scope.row[column.field] }}</span>
                    </template>
                  </el-table-column>
                </template>
              </el-table>
              <pagination
                v-show="totalRight>0"
                :total="totalRight"
                :page.sync="queryParams.pageNum"
                :limit.sync="queryParams.pageSize"
                @pagination="handleQueryRight"
              />
            </div>
          </el-col>
        </el-row>
      </el-tab-pane>
    </el-tabs>
    <el-dialog title="事件信息" v-if="dialogVisible" destroy-on-close append-to-body modal-append-to-body :visible.sync="dialogVisible" width="80%">
      <event-detail :dis-id="disData.disId" :daliy-id="disData.daliyId"/>
    </el-dialog>
  </div>
</template>
<script>
import RouteCodeSection from "@/components/RouteCodeSection/index.vue";
import RoadSection from "@/views/baseData/components/roadSection/index.vue";
import {
  listEvent,
  listMethod,
  deleteEvent,
  listMethodBySettleId, listEventBySettleId
} from "@/api/dailyMaintenance/metering/middleApplication";
import {listAllDiseases} from "@/api/patrol/diseases";
import EventInfo from "@/views/dailyMaintenance/component/eventTreeInfo.vue";
import {formatDate, formatDateNoTime} from "@/utils";
import EventDetail from "@/views/dailyMaintenance/component/eventDetail.vue";

export default {
  dicts: ['route_direction', 'lane', 'sys_asset_type'],
  components: {
    EventDetail,
    EventInfo, RoadSection, RouteCodeSection,
    RenderDom: {
      functional: true,
      props: {
        row: Object,
        index: Number,
        render: Function
      },
      render(createElement, ctx) {
        const {row, index} = ctx.props
        return ctx.props.render(row, index)
      }
    }
  },
  data() {
    return {
      queryParams: {
        pageNum: 1,
        pageSize: 10,
      },
      loading: false,
      tableDataLeft: [],
      totalLeft: 0,
      columnsLeft: [
        {key: 0, width: 100, field: 'code', label: `施工单编号`, visible: true,
          slots: true,
          render: (row, index) => {
            if (row.code) {
              return (
                <el-link type="primary" style="font-size: 12px;" onClick={()=> this.routerChange(row)}>{row.code}</el-link>
              )
            } else {
              return ''
            }
          }},
        {key: 1, width: 100, field: 'assetMainTypeName', label: `资产类型`, visible: true, dict: 'sys_asset_type'},
        {key: 2, width: 100, field: 'maiSecName', label: `路段名称`, visible: true},
        {key: 3, width: 100, field: 'routeCode', label: `路线编码`, visible: true},
        {key: 4, width: 100, field: 'direction', label: `上下行`, visible: true, dict: 'route_direction'},
        {key: 5, width: 100, field: 'lane', label: `位置`, visible: true, dict: 'lane'},
        {
          key: 6,
          width: 100,
          field: 'beginMile',
          label: `起点桩号`,
          visible: true,
          slots: true,
          render: (row, index) => {
            return (
              <span>{this.formatPile(row.beginMile)}</span>
            )
          }
        },
        {
          key: 7, width: 100, field: 'endMile', label: `终点桩号`, visible: true, slots: true, render: (row, index) => {
            return (
              <span>{this.formatPile(row.endMile)}</span>
            )
          }
        },
        {key: 8, width: 100, field: 'disTypeName', label: `事件类型`, visible: true, dict: 'sys_asset_type'},
        {
          key: 9, width: 100, field: 'disDesc', label: `事件描述`, visible: true, slots: true,
          render: (row) => {
            return (
              <el-tooltip className="item" effect="dark" content={row.disDesc} placement="top-start">
                <div style="overflow: hidden;white-space:nowrap;text-overflow:ellipsis">{row.disDesc}</div>
              </el-tooltip>
            )
          }
        },
        {key: 10, width: 100, field: 'sumFund', label: `费用(元)`, visible: true, slots: true, render: (row) => {
            return (
              <span>{row.sumFund?.toFixed(0)}</span>
            );
          }},
        {
          key: 11,
          width: 100,
          field: 'productionFund',
          label: `安全生产费`,
          visible: true,
          slots: true,
          render: (row) => {
            return (
              <span>{row.productionFund?.toFixed(0)}</span>
            );
          }
        },
        {
          key: 12,
          width: 100,
          field: 'guaranteeFund',
          label: `安全保通费`,
          visible: true,
          slots: true,
          render: (row) => {
            return (
              <span>{row.guaranteeFund?.toFixed(0)}</span>
            );
          }
        },
        {
          key: 13, width: 100, field: 'supFund', label: `监理费`, visible: true, slots: true, render: (row) => {
            return (
              <span>{row.supFund?.toFixed(0)}</span>
            );
          }
        },
        {key: 14, width: 100, field: 'field14', label: `图片`, visible: true},
        {key: 15, width: 100, field: 'supDomainName', label: `监理单位`, visible: true},
        {key: 16, width: 100, field: 'updateUser', label: `操作人`, visible: true},
        {
          key: 17,
          width: 100,
          field: 'updateTime',
          label: `操作时间`,
          visible: true,
          slots: true,
          render: (row, index) => {
            return (
              <span>{formatDate(row.updateTime)}</span>
            )
          }
        }

      ],
      tableDataRight: [],
      selectIds: [],
      totalRight: 0,
      columnsRight: [
        {key: 5, field: 'schemeCode', label: `子目号`, visible: true},
        {key: 0, field: 'schemeName', label: `项目名称`, visible: true},
        {key: 1, field: 'unit', label: `单位`, visible: true},
        {key: 2, field: 'price', label: `单价`, visible: true},
        {key: 3, field: 'num', label: `数量`, visible: true},
        {key: 4, field: 'amount', label: `金额(元)`, visible: true},
      ],
      methodLoading: true,
      dialogVisible: false,
      disData: {},
      advicesList: [],
    }
  },
  props: {
    maiSecId: {
      type: String,
      default: ''
    },
    calcId: {
      type: String,
      default: ''
    }
  },
  watch: {
    calcId: {
      handler(val) {
        if (val) {
          this.handleQueryLeft()
          this.handleQueryRight()
        }
      }
    }
  },
  created() {
    this.getDisList()
    this.handleQueryLeft()
    this.handleQueryRight()
  },
  methods: {
    handleQueryLeft() {
      this.loading = true
      this.queryParams.calcId = this.calcId
      listEvent(this.queryParams).then(res => {
        this.tableDataLeft = res.rows
        this.totalLeft = res.total
        this.loading = false
      })
    },
    handleQueryRight() {
      this.loading = true
      listMethod(this.queryParams).then(res => {
        this.tableDataRight = res.rows
        this.totalRight = res.total
        this.loading = false
      })
    },
    handleDelete(row) {
      this.$modal.confirm('是否确认删除').then(() => {
        this.loading = true
        deleteEvent(row.detailId).then(() => {
          this.$modal.msgSuccess('删除成功')
          this.handleQueryLeft()
        })
      })
    },
    exportListLeft() {
      if (this.selectIds.length > 0) {
        this.queryParams.ids = this.selectIds
      }
      this.download(
        'manager/intermediate/detail/export',
        {...this.queryParams},
        `intermediate_detail_${new Date().getTime()}.xlsx`,
        {
          headers: {'Content-Type': 'application/json;'},
          parameterType: 'body'
        }
      )
    },
    exportListRight() {
      this.download(
        'manager/method/export',
        {calcId: this.calcId},
        `method_${new Date().getTime()}.xlsx`,
        {
          headers: {'Content-Type': 'application/json;'},
          parameterType: 'body'
        }
      )
    },
    handleClickRow(e) {
      this.disData = e
      e.isSelected = !e.isSelected;
      this.$refs.dataTable.toggleRowSelection(e);
    },
    // 选中
    handleSelectionChange(e) {
      this.selectIds = e.map(item => item.detailId)
    },
    loadData(row) {
      this.methodLoading = true
      listEventBySettleId({settleId: row.settleId, detailId: row.detailId}).then(res => {
        this.$set(row, 'methodList', res.data)
        this.methodLoading = false
      })
    },
    openEventInfo() {
      if (!this.disData.detailId) {
        this.$modal.msgWarning("请先选择数据")
        return
      }
      this.dialogVisible = true
    },
    // 获取事件列表
    getDisList() {
      listAllDiseases().then(res => {
        res.data.forEach(item => {
          this.advicesList.push({
            label: item.diseaseName,
            value: item.id
          })
        })
      })
    },
    resetQuery() {
      this.queryParams = {
        pageNum: 1,
        pageSize: 10,
      }
    },
    routerChange(row) {
      this.$emit('router-change', row.code)
    }
  }
}
</script>
<style scoped lang="scss">

</style>
<style lang="scss" scoped>
@import "@/assets/styles/business.scss";
</style>
