<template>
	<div class="app-container maindiv">
		<el-row :gutter="10" class="mb8">
			<el-col :span="1.5">
				<el-button icon="el-icon-plus" size="mini" type="primary" @click="openDetail">新增
				</el-button>
			</el-col>
			<el-col :span="6">
				当前结构物： {{ structureName }}
			</el-col>
			<right-toolbar :columns="columns" :showSearch.sync="showSearch" @queryTable="handleQuery"></right-toolbar>
		</el-row>
		<el-row>
			<div class="draggable">
				<el-table ref="dataTable" v-loading="loading" :data="tableData" :height="
                showSearch ? 'calc(100vh - 330px)' : 'calc(100vh - 270px)'
              " border row-key="id" size="mini" stripe style="width: 100%">
					<el-table-column align="center" label="序号" type="index" width="50" />
					<template v-for="(column,index) in columns">
						<el-table-column v-if="column.visible" :label="column.label" :prop="column.field"
							:width="column.width" align="center" show-overflow-tooltip>
							<template slot-scope="scope">
								<dict-tag v-if="column.dict" :options="dict.type[column.dict]"
									:value="scope.row[column.field]" />
								<template v-else-if="column.slots">
									<RenderDom :index="index" :render="column.render" :row="scope.row" />
								</template>
								<span
									v-else-if="column.isTime">{{ parseTime(scope.row[column.field], '{y}-{m}-{d}') }}</span>
								<span v-else>{{ scope.row[column.field] }}</span>
							</template>
						</el-table-column>
					</template>
					<el-table-column align="center" class-name="small-padding fixed-width" fixed="right" label="操作"
						width="220">
						<template slot-scope="scope">
							<el-button icon="el-icon-edit" size="mini" type="text" @click="handleUpdate(scope.row)">修改
							</el-button>
							<el-button icon="el-icon-delete" size="mini" type="text" @click="handleDelete(scope.row)">删除
							</el-button>
						</template>
					</el-table-column>
				</el-table>
				<pagination v-show="total > 0" :limit.sync="queryParams.pageSize" :page.sync="queryParams.pageNum"
					:total="total" @pagination="handleQuery" />
			</div>
		</el-row>
		<el-dialog :append-to-body="true" :destroy-on-close="true" :visible.sync="relaFlag" title="爆闪设备" width="70%"
			@close="handleClose">
			<div v-loading="loading" class="road-interflow-edit">
				<el-row :gutter="15">
					<el-form ref="elForm" :model="formData" :rules="rules" label-width="120px" size="medium">
						<!--            <el-col :span="12">-->
						<!--              <el-form-item label="结构物类型" prop="structureType">-->
						<!--                <el-input-->
						<!--                  v-model="formData.structureType"-->
						<!--                  :style="{ width: '100%' }"-->
						<!--                  clearable-->
						<!--                  placeholder="请输入结构物类型"-->
						<!--                  readonly-->
						<!--                ></el-input>-->
						<!--              </el-form-item>-->
						<!--            </el-col>-->
						<el-row :gutter="15">
							<el-col :span="12">
								<el-form-item label="爆闪类型" prop="flashType">
									<dict-select v-model="formData.flashType" :style="{ width: '100%' }" readonly=""
										type="flash_type" />
								</el-form-item>
							</el-col>
							<el-col :span="12">
								<el-form-item label="设备名称" prop="deviceName">
									<el-input v-model="formData.deviceName" :style="{ width: '100%' }" clearable
										placeholder="请输入设备名称"></el-input>
								</el-form-item>
							</el-col>
						</el-row>
						<el-row :gutter="15">
							<el-col :span="12">
								<el-form-item label="设备控制编码" prop="commonId">
									<el-input v-model="formData.commonId" :style="{ width: '100%' }" clearable
										placeholder="请输入控制设备编码"></el-input>
								</el-form-item>
							</el-col>
							<el-col :span="12">
								<el-form-item label="设备编码" prop="deviceCode">
									<el-input v-model="formData.deviceCode" :style="{ width: '100%' }" clearable
										placeholder="请输入设备编码"></el-input>
								</el-form-item>
							</el-col>
						</el-row>
						<el-row :gutter="15">
							<el-col :span="12">
								<el-form-item label="通信模式" prop="communiMode">
									<el-select v-model="formData.communiMode" :style="{ width: '100%' }"
										placeholder="请选择通信模式">
										<el-option label="http" value="http"></el-option>
										<el-option label="tcp" value="tcp"></el-option>
									</el-select>
								</el-form-item>
							</el-col>
							<el-col :span="12">
								<el-form-item label="IP地址" prop="ipAddr">
									<el-input v-model="formData.ipAddr" :style="{ width: '100%' }" clearable
										placeholder="请输入IP地址"></el-input>
								</el-form-item>
							</el-col>
						</el-row>
						<el-row :gutter="15">
							<el-col :span="12">
								<el-form-item label="设备位置" prop="position">
									<el-input v-model="formData.position" :style="{ width: '100%' }" clearable
										placeholder="请输入设备位置"></el-input>
								</el-form-item>
							</el-col>
							<el-col :span="12">
								<el-form-item label="上下行" prop="direction">
									<el-input v-model="formData.direction" :style="{ width: '100%' }" clearable
										placeholder="请输入输入上行或下行"></el-input>
								</el-form-item>
							</el-col>

							<el-col :span="12">
								<el-form-item label="经度" prop="lat">
									<el-input v-model="formData.lat" :style="{ width: '100%' }" clearable
										placeholder="请输入经度"></el-input>
								</el-form-item>
							</el-col>
						</el-row>
						<el-row :gutter="15">
							<el-col :span="12">
								<el-form-item label="纬度" prop="lon">
									<el-input v-model="formData.lon" :style="{ width: '100%' }" clearable
										placeholder="请输入纬度"></el-input>
								</el-form-item>
							</el-col>
						</el-row>
					</el-form>
				</el-row>
				<div style="text-align: right; margin-top: 20px">
					<el-button size="mini" type="primary" @click="handleSave">保存
					</el-button>
					<el-button size="mini" @click="relaFlag = false">退出</el-button>
				</div>
			</div>
		</el-dialog>
	</div>
</template>

<script>
	import selectTree from '@/components/DeptTmpl/selectTree'
	import {
		addFlashdevice,
		delFlashdevice,
		editFlashdevice,
		listFlashdevice
	} from "@/api/jgjc/structure/structure";

	export default {
		name: 'flashdevice',
		components: {
			selectTree,
			RenderDom: {
				functional: true,
				props: {
					row: Object,
					index: Number,
					render: Function
				},
				render(createElement, ctx) {
					const {
						row,
						index
					} = ctx.props
					return ctx.props.render(row, index)
				}
			}
		},
		props: [],
		dicts: ['plan_status', 'is_has_flash_dev', 'Flashdevice_status'],
		data() {
			return {
				leftTotal: 1,
				showSearch: false,
				loading: false,
				columns: [{
						key: 0,
						field: 'flashType',
						label: '爆闪设备型号',
						visible: true
					},
					// {key: 1, field: 'structType', label: '结构物类型', visible: true},
					{
						key: 2,
						field: 'deviceName',
						label: '设备名称',
						visible: true
					},
					{
						key: 3,
						field: 'deviceCode',
						label: '设备编码',
						visible: true
					},
					{
						key: 4,
						field: 'communiMode',
						label: '通信模式',
						visible: true
					},
					{
						key: 4,
						field: 'direction',
						label: '上下行',
						visible: true
					},

					{
						key: 5,
						field: 'ipAddr',
						label: 'ip地址',
						visible: true
					},
					{
						key: 6,
						field: 'position',
						label: '设备位置',
						visible: true,
						dict: 'plan_status'
					},
					{
						key: 7,
						field: 'lat',
						label: '经度',
						visible: true
					},
					{
						key: 8,
						field: 'lon',
						label: '纬度',
						visible: true
					},

				],
				tableData: [],
				relaFlag: false,
				formData: {},
				total: 0,
				queryParams: {
					pageNum: 1,
					pageSize: 10
				},
				// 养护路段
				rules: {
					structureType: [{
						required: true,
						message: '请输入结构物类型',
						trigger: 'blur'
					}],
					flashType: [{
						required: true,
						message: '请选择爆闪类型',
						trigger: 'change'
					}],
					deviceName: [{
						required: true,
						message: '请输入设备名称',
						trigger: 'blur'
					}],
					commonId: [{
						required: true,
						message: '请输入设备控制编码',
						trigger: 'blur'
					}],
					// deviceCode: [{ required: true, message: '请输入设备编码', trigger: 'blur' }],
					communiMode: [{
						required: true,
						message: '请输入通信模式',
						trigger: 'blur'
					}],
					// ipAddr: [{required: true, message: '请输入IP地址', trigger: 'blur'}]
				},
				structureName: ''
			}
		},
		computed: {},
		watch: {},
		mounted() {
			this.queryParams.structId = this.$route.query.structureId
			this.structureName = this.$route.query.structureName
			this.handleQuery()
		},
		methods: {
			// 查询
			handleQuery() {
				this.loading = true
				listFlashdevice(this.queryParams).then(res => {
					this.tableData = res.rows
					this.loading = false
					this.total = res.total
					this.$nextTick(() => {
						this.$refs.dataTable.doLayout();
					});
				})
			},
			// 修改
			handleUpdate(e) {
				this.formData = JSON.parse(JSON.stringify(e))
				this.$set(this.formData, 'structureType', this.$route.query.structureType)
				this.relaFlag = true
			},
			// 删除
			handleDelete(e) {
				this.$modal.confirm('是否确认删除').then(() => {
					this.loading = true
					const ids = [e.id]
					delFlashdevice({
						ids: ids,
						structId: this.$route.query.structureId
					}).then(res => {
						this.$modal.msgSuccess('删除成功');
						this.handleQuery()
					})
				})
			},
			// 新增
			openDetail() {
				this.formData = {}
				this.$set(this.formData, 'structureType', this.$route.query.structureType)
				this.relaFlag = true
			},
			// 保存
			handleSave() {
				this.$refs["elForm"].validate((valid) => {
					if (!valid) return;
					this.formData.structId = this.$route.query.structureId
					if (this.formData.id) {
						editFlashdevice(this.formData).then(res => {
							this.$modal.msgSuccess('保存成功')
							this.handleQuery()
						})
					} else {
						addFlashdevice(this.formData).then(res => {
							this.$modal.msgSuccess('保存成功')
							this.handleQuery()
						})
					}
					this.relaFlag = false
				});
			},
			handleClose() {
				this.formData = {}
			},
		}
	}
</script>

<style lang="scss" scoped>
	::v-deep .el-table th .gutter {
		display: table-cell !important;
	}
</style>