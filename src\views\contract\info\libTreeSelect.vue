<template>
  <div style="max-height: 500px;overflow-y: scroll">
    <el-input
        placeholder="输入关键字进行过滤"
        v-model="filterText">
    </el-input>

    <el-tree
        class="filter-tree"
        v-loading="loading"
        :data="data"
        :props="defaultProps"
        highlight-current
        node-key="id"
        :filter-node-method="filterNode"
        @node-click="checkChange"
        ref="tree">
    </el-tree>
  </div>
</template>

<script>
import {getTreeData} from "@/api/contract/quotationSystem";

export default {
  props: {
    libId: {
      type: String,
      default: () => ''
    },
    funName: {
      type: String,
      default: () => ''
    }
  },
  watch: {
    filterText(val) {
      this.$refs.tree.filter(val);
    }
  },
  created() {
    if (!this.libId) {
      this.$modal.msgError("请先选择报价体系！");
      this.$emit('checkLibPrice')
    } else {
      this.getList()
    }
  },
  methods: {
    getList() {
      this.loading = true
      const params = {
        libId: this.libId
      }
      getTreeData(params).then(res => {
        this.data = res.rows
        this.loading = false
      })
    },
    filterNode(value, data) {
      if (!value) return true;
      return data.schemeName.indexOf(value) !== -1;
    },
    checkChange(e) {
      // 选中了方法节点
      if (e.nodeType == 2) {
        const data = {
          name: this.funName,
          data: e
        }
        this.$emit('checkLibPrice', data)
      }
    }
  },
  data() {
    return {
      filterText: '',
      data: [],
      loading: false,
      defaultProps: {
        children: 'children',
        label: 'schemeName'
      }
    };
  }
};
</script>

<style lang="scss" scoped>
::v-deep .el-tree {
  margin-top: 15px;
}
</style>
<style lang="scss" scoped>
@import "@/assets/styles/business.scss";
</style>
