<template>
  <div class="container">
    <!--查询条件开始-->
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" label-width="68px">
      <div style="display: flex; align-items: center; flex-wrap: nowrap">
        <!--                <CascadeSelection-->
        <!--                    style="min-width: 192px; margin-right: 10px"-->
        <!--                    :form-data="queryParams"-->
        <!--                    v-model="queryParams"-->
        <!--                    types="201"-->
        <!--                    multiple-->
        <!--                />-->

        <el-form-item label="" prop="assetCode" style="margin-bottom: 0; margin-right: 10px">
          <el-input
            v-model="queryParams.assetCode"
            :placeholder="`请输入${partsType[type].substring(0, 2)}编码`"
            clearable
            prefix-icon="el-icon-user"
            style="width: 240px"
            @keyup.enter.native="handleQuery"
          />
        </el-form-item>

        <el-form-item label="" prop="assetName" style="margin-bottom: 0; margin-right: 10px">
          <el-input
            v-model="queryParams.assetName"
            :placeholder="`请输入${partsType[type].substring(0, 2)}${
                            ['3', '4'].includes(type) ? '类型' : '名称'
                        }`"
            clearable
            prefix-icon="el-icon-user"
            style="width: 240px"
            @keyup.enter.native="handleQuery"
          />
        </el-form-item>

        <el-form-item label="" prop="hasDisease" style="margin-bottom: 0; margin-right: 10px">
          <el-select v-model="queryParams.hasDisease" placeholder="是否有病害" clearable style="width: 240px">
            <el-option label="是" :value="true"></el-option>
            <el-option label="否" :value="false"></el-option>
          </el-select>
        </el-form-item>


        <el-form-item label="" prop="status" style="margin-bottom: 0; margin-right: 10px" >
          <DictSelect
            v-model="queryParams.status"
            type="patrol_inspection_status"
            placeholder="请选择状态"
            clearable
            style="width: 240px"
          ></DictSelect>
        </el-form-item>

        <el-form-item style="margin-bottom: 0">
          <div style="white-space: nowrap">
            <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">
              搜索
            </el-button>
            <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
            <!--                        <el-button-->
            <!--                            v-show="!showSearch"-->
            <!--                            @click="showSearch = true"-->
            <!--                            icon="el-icon-arrow-down"-->
            <!--                            circle-->
            <!--                        ></el-button>-->
            <!--                        <el-button-->
            <!--                            v-show="showSearch"-->
            <!--                            @click="showSearch = false"-->
            <!--                            icon="el-icon-arrow-up"-->
            <!--                            circle-->
            <!--                        ></el-button>-->
          </div>
        </el-form-item>
      </div>
      <!--默认折叠-->
      <br />
      <!--            <el-form-item v-show="showSearch" label="" prop="checkTime">-->
      <!--                <el-date-picker-->
      <!--                    clearable-->
      <!--                    v-model="queryParams.checkTime"-->
      <!--                    type="daterange"-->
      <!--                    value-format="yyyy-MM-dd HH:mm:ss"-->
      <!--                    start-placeholder="检查开始时间"-->
      <!--                    end-placeholder="检查结束时间"-->
      <!--                    style="width: 240px"-->
      <!--                ></el-date-picker>-->
      <!--            </el-form-item>-->

    </el-form>
    <!--查询条件结束-->

    <!--操作按钮区开始-->
            <el-row :gutter="10" style="margin-bottom: 8px" >
<!--                <el-col :span="1.5" v-if="menuType === 1">-->
<!--                    <el-button type="primary" icon="el-icon-plus" size="mini" @click="handleAdd"> 新增 </el-button>-->
<!--                </el-col>-->
<!--                <el-col :span="1.5" v-if="menuType === 1">-->
<!--                    <el-button type="primary" icon="el-icon-edit" size="mini" :disabled="single" @click="handleUpdate">-->
<!--                        修改-->
<!--                    </el-button>-->
<!--                </el-col>-->
<!--                <el-col :span="1.5" v-if="menuType === 1">-->
<!--                    <el-button type="danger" icon="el-icon-delete" size="mini" :disabled="multiple" @click="handleDelete">-->
<!--                        删除-->
<!--                    </el-button>-->
<!--                </el-col>-->

<!--                <el-col :span="1.5" v-if="menuType === 1">-->
<!--                    <el-button type="primary" size="mini" @click="handleCommit" :disabled="multiple"> 提交 </el-button>-->
<!--                </el-col>-->
<!--                <el-col :span="1.5" v-if="menuType === 2">-->
<!--                    <el-button type="primary" size="mini" @click="handleAudit" :disabled="multiple"> 审核 </el-button>-->
<!--                </el-col>-->
<!--                <el-col :span="1.5" v-if="menuType === 3">-->
<!--                    <el-button type="primary" size="mini" @click="handleUpdateAuditTime" :disabled="multiple" v-hasPermi="['patrol:assetCheck:updateAuditTime']">-->
<!--                        修改审核时间-->
<!--                    </el-button>-->
<!--                </el-col>-->
<!--                <el-col :span="1.5" v-if="menuType === 1">-->
<!--                    <el-button type="primary" icon="el-icon-upload2" size="mini" @click="handleImport"> 导入 </el-button>-->
<!--                </el-col>-->
                <el-col :span="1.5">
                    <el-button type="primary" icon="el-icon-download" size="mini" @click="handleExport"> 导出清单 </el-button>
                </el-col>
<!--                <el-col :span="1.5">-->
<!--                    <el-button type="primary" icon="el-icon-download" size="mini" @click="handleExportCard">-->
<!--                        导出卡片-->
<!--                    </el-button>-->
<!--                </el-col>-->
<!--                <el-col :span="1.5" v-if="menuType === 1 && ['1', '3', '5'].includes(type)">-->
<!--                    <el-button-->
<!--                        type="primary"-->
<!--                        icon="el-icon-plus"-->
<!--                        size="mini"-->
<!--                        v-hasPermi="['patrol:assetCheck:generation']"-->
<!--                        @click="handleGenerate"-->
<!--                    >-->
<!--                        批量生成-->
<!--                    </el-button>-->
<!--                </el-col>-->
                <right-toolbar :showSearch.sync="showSearch" @queryTable="getList" :columns="columns"></right-toolbar>
            </el-row>
    <!--操作按钮区结束-->

    <!--数据表格开始-->
    <div class="tableDiv">
      <el-table
        v-adjust-table
        ref="table"
        size="mini"
        height="22vh"
        style="width: 100%"
        v-loading="loading"
        border
        :data="assetCheckList"
        @selection-change="handleSelectionChange"
        :row-style="rowStyle"
        @row-click="handleRowClick"
      >
        <el-table-column type="selection" width="50" align="center" />
        <el-table-column fixed label="序号" type="index" width="50">
          <template v-slot="scope">
            {{ scope.$index + (queryParams.pageNum - 1) * queryParams.pageSize + 1 }}
          </template>
        </el-table-column>
        <el-table-column
          label="养护路段"
          align="center"
          prop="maintenanceSectionName"
          min-width="140"
          show-overflow-tooltip
        />
        <el-table-column
          label="路线编码"
          align="center"
          prop="routeCode"
          min-width="140"
          show-overflow-tooltip
        />
        <!--        名称-->
        <el-table-column
          :label="`${partsType[type].substring(0, 2)}${['3', '4'].includes(type) ? '类型' : '名称'}`"
          align="center"
          prop="assetName"
          min-width="140"
          show-overflow-tooltip
        />
        <el-table-column
          :label="`${partsType[type].substring(0, 2)}编码`"
          align="center"
          prop="assetCode"
          min-width="140"
          show-overflow-tooltip
        />
        <el-table-column
          label="桩号"
          align="center"
          width="120"
          prop="centerStake"
          :formatter="(...arg) => formatPile(arg[2])"
          v-if="computedColumns('桩号')"
        />
        <el-table-column
          label="管理处"
          align="center"
          prop="maintainUnitName"
          min-width="140"
          show-overflow-tooltip
        />
        <el-table-column
          label="养护单位"
          align="center"
          prop="propertyUnitName"
          min-width="140"
          show-overflow-tooltip
        />
        <el-table-column
          label="天气"
          align="center"
          prop="weather"
          v-if="['5', '6'].includes(type) && computedColumns('天气')"
        />
        <el-table-column label="负责人" align="center" prop="kahunaName" v-if="computedColumns('负责人')" />
        <el-table-column
          label="记录人"
          align="center"
          prop="oprUserName"
          width="140"
          v-if="computedColumns('记录人')"
        />
        <el-table-column
          label="巡查类别"
          align="center"
          prop="category"
          v-if="['2', '4', '6'].includes(type) && computedColumns('巡查类别')"
        >
          <template slot-scope="scope">
            <dict-tag :options="dict.type.patrol_inspection_ilk" :value="scope.row.category" />
          </template>
        </el-table-column>
        <el-table-column
          label="检查日期"
          align="center"
          prop="checkTime"
          min-width="140"
          show-overflow-tooltip
          v-if="computedColumns('检查日期')"
        >
          <template slot-scope="scope">
            <span>{{ parseTime(scope.row.checkTime, '{y}-{m}-{d}') }}</span>
          </template>
        </el-table-column>
        <el-table-column label="状态" align="center" prop="status" width="120" v-if="computedColumns('状态')">
          <template slot-scope="scope">
            <dict-tag :options="dict.type.patrol_inspection_status" :value="scope.row.status" />
          </template>
        </el-table-column>
        <el-table-column label="审核日期" align="center" prop="auditTime" min-width="140" show-overflow-tooltip>
          <template slot-scope="scope">
            <span>{{ parseTime(scope.row.auditTime, '{y}-{m}-{d}') }}</span>
          </template>
        </el-table-column>
        <el-table-column
          label="备注"
          align="center"
          prop="remark"
          show-overflow-tooltip
          v-if="computedColumns('备注')"
        />

        <el-table-column
          label="操作"
          fixed="right"
          align="center"
          width="320"
          class-name="small-padding fixed-width"
        >
          <template slot-scope="scope" v-if="scope.row.userId !== 1">
            <el-button size="mini" type="text" icon="el-icon-view" @click="handleView(scope.row)">
              查看
            </el-button>

            <el-button
              v-if="menuType === 1"
              size="mini"
              type="text"
              icon="el-icon-edit"
              @click="handleUpdate(scope.row)"
            >
              修改
            </el-button>
            <el-button
              v-if="menuType === 1"
              size="mini"
              type="text"
              icon="el-icon-view"
              @click="handleCommit(scope.row)"
            >
              提交
            </el-button>
            <el-button
              v-if="menuType === 2"
              size="mini"
              type="text"
              icon="el-icon-view"
              @click="handleAudit(scope.row)"
            >
              审核
            </el-button>
            <el-button
              v-if="menuType === 1"
              size="mini"
              type="text"
              icon="el-icon-delete"
              @click="handleDelete(scope.row)"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      <pagination
        v-show="total > 0"
        :total="total"
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
        :page-sizes="[10, 20, 30, 50, 100, 1000]"
        @pagination="getList"
      />
    </div>
    <!--数据表格结束-->

    <!-- 添加或修改资产寻检查主对话框 -->
    <el-dialog
      :title="title"
      :visible.sync="open"
      width="80%"
      append-to-body
      :close-on-click-modal="false"
      class="asset-check-edit-dialog"
    >
      <AssetCheckInsertOrUpdate
        :disabled="disabled"
        ref="assetCheckInsertOrUpdate"
        :check-entity="form"
        :title="title"
      ></AssetCheckInsertOrUpdate>
      <div slot="footer" class="dialog-footer">
        <template v-if="title.substring(0, 2) === '审核'">
          <el-button type="danger" @click="submitForm({ id: form.id, status: 4, type: form.type })">
            驳 回
          </el-button>
          <el-button
            type="primary"
            @click="submitForm({ id: form.id, status: 3, type: form.type, auditTime: form.auditTime })"
          >
            通 过
          </el-button>
        </template>
        <template v-else-if="title.substring(0, 2) === '提交'">
          <el-button type="primary" @click="submitForm({ id: form.id, status: 2, type: form.type })">
            提 交
          </el-button>
        </template>
        <template v-else-if="title.substring(0, 2) !== '查看'">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </template>
      </div>
    </el-dialog>

    <!-- 批量生成资产寻检查主对话框 -->
    <AssetCheckGenerate
      :disabled="disabled"
      ref="assetCheckGenerate"
      :visible.sync="openGenerate"
      width="80%"
      append-to-body
      :close-on-click-modal="false"
      :type="type"
      :generate="true"
      @update:visible="handleGenerateClose"
    ></AssetCheckGenerate>

    <!-- 用户导入对话框 -->
    <el-dialog :title="upload.title" :visible.sync="upload.open" width="400px" append-to-body>
      <el-upload
        ref="upload"
        :limit="1"
        accept=".xlsx, .xls"
        :headers="upload.headers"
        :action="upload.url + '?updateSupport=' + upload.updateSupport"
        :disabled="upload.isUploading"
        :on-progress="handleFileUploadProgress"
        :on-success="handleFileSuccess"
        :auto-upload="false"
        drag
      >
        <i class="el-icon-upload"></i>
        <div class="el-upload__text">
          将文件拖到此处，或
          <em>点击上传</em>
        </div>
        <div class="el-upload__tip text-center" slot="tip">
          <div class="el-upload__tip" slot="tip">
            <el-checkbox v-model="upload.updateSupport" />
            是否更新已经存在的用户数据
          </div>
          <span>仅允许导入xls、xlsx格式文件。</span>
          <el-link
            type="primary"
            :underline="false"
            style="font-size: 12px; vertical-align: baseline"
            @click="importTemplate"
          >
            下载模板
          </el-link>
        </div>
      </el-upload>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitFileForm">确 定</el-button>
        <el-button @click="upload.open = false">取 消</el-button>
      </div>
    </el-dialog>
    <!-- 批量审核对话框 -->
    <el-dialog title="批量审核" :visible.sync="auditDialogVisible" width="400px" append-to-body>
      <div style="margin-bottom: 20px">
        <div style="margin-bottom: 10px">请选择审核时间:</div>
        <el-date-picker
          v-model="auditTime"
          type="datetime"
          placeholder="选择审核时间"
          value-format="yyyy-MM-dd HH:mm:ss"
          style="width: 100%"
        ></el-date-picker>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button type="danger" @click="handleBatchAudit(4)">驳 回</el-button>
        <el-button type="primary" @click="handleBatchAudit(3)">通 过</el-button>
      </div>
    </el-dialog>

    <!-- 批量修改审核时间对话框 -->
    <el-dialog title="批量修改审核时间" :visible.sync="updateAuditTimeDialogVisible" width="400px" append-to-body>
      <div style="margin-bottom: 20px">
        <div style="margin-bottom: 10px">请选择新的审核时间:</div>
        <el-date-picker
          v-model="newAuditTime"
          type="datetime"
          placeholder="选择审核时间"
          value-format="yyyy-MM-dd HH:mm:ss"
          style="width: 100%"
        ></el-date-picker>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="updateAuditTimeDialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="submitUpdateAuditTime">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  selectAssetCheckData,
  listAssetCheck,
  getAssetCheck,
  delAssetCheck,
  addAssetCheck,
  updateAssetCheck,
  getAssetTotalCount,
} from '@/api/patrol/assetCheck';
import { getToken } from '@/utils/auth';

import TreeSelect from '@riophae/vue-treeselect';
import '@riophae/vue-treeselect/dist/vue-treeselect.css';

import AssetCheckInsertOrUpdate from '@/views/patrol/patrolManage/components/bridgeDailyCheck/insertOrUpdate.vue';
import AssetCheckGenerate from '@/views/patrol/assetCheck/generate.vue';
import { listMaintenanceSectionAll } from '@/api/system/maintenanceSection';
import { listAllRoute } from '@/api/system/route';
import { getTreeStruct } from '@/api/tmpl';
import CascadeSelection from '@/components/CascadeSelectionManagementOffice/index.vue';
/**
 * 操作按钮暂不用系统权限控制
 */

export default {
  name: 'AssetCheck',
  dicts: ['patrol_inspection_ilk', 'patrol_inspection_category', 'patrol_inspection_status'],
  components: {
    TreeSelect,
    AssetCheckInsertOrUpdate,
    CascadeSelection,
    AssetCheckGenerate,
  },
  props: {
    /**
     * 巡查类型；桥梁日常巡查、桥梁经常检查、隧道日常巡查等
     */
    type: {
      type: String,
      default: '1',
    },
    /**
     * menuType 菜单类型 ps: 1:隧道日常巡查   2:隧道日常巡查审核  3:隧道日常巡查结果
     */
    menuType: {
      type: Number,
      default: 3,
    },
  },
  data() {
    return {
      // 遮罩层
      loading: false,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: false,
      // 总条数
      total: 0,
      // 资产寻检查主表格数据
      assetCheckList: null,
      // 弹出层标题
      title: '',
      assetList: [],
      partsType: {
        1: '桥梁日常巡查',
        2: '桥梁经常检查',
        3: '涵洞定期检查',
        4: '涵洞经常检查',
        5: '隧道日常巡查',
        6: '隧道经常检查',
      },
      deptOptions: [],
      maintenanceSectionList: null,
      routeList: null,
      // 是否显示弹出层
      open: false,
      // 是否显示批量生成弹出层
      openGenerate: false,
      // 表单参数
      form: {
        type: this.type,
        partsType: this.type,
        details: null,
      },
      // 用户导入参数
      upload: {
        // 是否显示弹出层（用户导入）
        open: false,
        // 弹出层标题（用户导入）
        title: '',
        // 是否禁用上传
        isUploading: false,
        // 是否更新已经存在的用户数据
        updateSupport: 0,
        // 设置上传的请求头部
        headers: { Authorization: 'Bearer ' + getToken() },
        // 上传的地址
        url: process.env.VUE_APP_BASE_API + '/system/user/importData',
      },
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 50,
        type: this.type,
        stage: ['2', '4', '6'].includes(this.type) ? '1' : null,
        dataRule: true,
        category: null,
        kahunaId: null,
        kahunaName: null,
        kahunaSign: null,
        oprUserId: null,
        oprUserName: null,
        oprUserSign: null,
        checkTime: null,
        checkStartTime: null,
        checkEndTime: null,
        status: null,
        auditTime: null,
        image: null,
        assetId: null,
        assetName: null,
        assetCode: null,
        routeId: null,
        routeName: null,
        routeCode: null,
        propertyUnitId: null,
        propertyUnitName: null,
        departmentIdList: null,
        maintainUnitName: null,
        maintenanceSectionId: null,
        maintenanceSectionName: null,
        centerStake: null,
        weather: null,
        hasDisease: null,
      },
      // 列信息
      columns: [
        // { key: 0, label: `检查类型`, visible: true },
        { key: 1, label: `巡查类别 `, visible: true },
        { key: 2, label: `负责人`, visible: true },
        { key: 3, label: `记录人`, visible: true },
        { key: 4, label: `检查日期`, visible: true },
        { key: 5, label: `状态`, visible: true },
        // { key: 6, label: `图像`, visible: true },
        { key: 7, label: `备注`, visible: true },
        // { key: 8, label: `删除标识`, visible: true },
        // { key: 9, label: `资产id`, visible: true },
        // { key: 10, label: `资产名称`, visible: true },
        // { key: 11, label: `资产编码`, visible: true },
        // { key: 12, label: `路线名称`, visible: true },
        // { key: 13, label: `路线编码`, visible: true },
        // { key: 14, label: `权属单位`, visible: true },
        // { key: 15, label: `养护单位`, visible: true },
        { key: 16, label: `桩号`, visible: true },
        { key: 17, label: `天气`, visible: true },
        // { key: 18, label: `是否异常`, visible: true },
      ],
      // 审核时间
      auditTime: null,
      // 审核对话框可见性
      auditDialogVisible: false,
      // 修改审核时间
      newAuditTime: null,
      // 修改审核时间对话框可见性
      updateAuditTimeDialogVisible: false,
    };
  },
  watch: {
    'queryParams.checkTime': function (val) {
      if (Array.isArray(val)) {
        this.queryParams.checkStartTime = val[0] ? val[0].substring(0, 10) + ' 00:00:00' : null;
        this.queryParams.checkEndTime = val[1] ? val[1].substring(0, 10) + ' 23:59:59' : null;
      } else {
        // 当值为null或undefined时
        this.queryParams.checkStartTime = null;
        this.queryParams.checkEndTime = null;
      }
    },
    menuType: {
      handler(value) {
        // switch (value) {
        //     case 1:
        //         this.queryParams.status = '1';
        //         break;
        //     case 2:
        //         this.queryParams.status = '2';
        //         break;
        //     case 3:
        //         this.queryParams.statusList = ['3', '4'];
        // }
      },
      immediate: true,
    },
    '$attrs.current': function () {
      if (this.$attrs.current.startTime){

        // this.queryParams.maintenanceSectionName = this.$attrs.current.maintenanceSectionName
        // this.queryParams.propertyUnitName = this.$attrs.current.maintenanceSectionName
        // this.queryParams.maintainUnitName = this.$attrs.current.maintenanceSectionName
        // this.queryParams.checkTime = this.$attrs.current.maintenanceSectionName

        this.queryParams.checkStartTime = this.$attrs.current.startTime + ' 00:00:00'
        this.queryParams.checkEndTime = this.$attrs.current.startTime + ' 23:59:59'
        this.queryParams.customSqlCondition = `pac.maintenance_section_name =  '${this.$attrs.current.maintenanceSectionName}'`
        this.getList()
      }

    }
  },
  computed: {
    disabled() {
      return (
        this.title.substring(0, 2) === '查看' ||
        this.title.substring(0, 2) === '审核' ||
        this.title.substring(0, 2) === '提交'
      );
    },
    computedColumns() {
      /* return () => {
          return true;
      }; */
      return (label) => {
        let visible
        this.columns.forEach((item)=>{
          if(item.label === label){
            visible = item.visible
          }
        })
        return visible
      }
    },
  },
  created() {
    // this.getList();
    this.getDeptTree();
    this.getRouteList();
    this.getMaintenanceSection();
  },
  methods: {
    /** 查询用户列表 */
    getList() {
      this.loading = true;
      const queryParams = { ...this.queryParams };
      // 删除checkTime但保留checkStartTime和checkEndTime
      const { checkTime, departmentIdList, ...restParams } = queryParams;
      selectAssetCheckData(restParams)
        .then((response) => {
          this.assetCheckList = response.rows;
          this.total = response.total;
        })
        .catch((error) => {
          console.error('获取数据失败:', error);
        })
        .finally(() => {
          this.loading = false;
        });
    },
    /** 查询部门下拉树结构 */
    getDeptTree() {
      return getTreeStruct({ types: 201 }).then((response) => {
        this.deptOptions = response.data;
      });
    },
    //管理处下拉选点击事件
    deptChange() {
      listMaintenanceSectionAll({
        departmentIdList: this.queryParams.departmentIdList,
      }).then((res) => {
        this.maintenanceSectionList = res.data;
      });
    },
    /** 查询养护路段下拉列表 */
    getMaintenanceSection() {
      listMaintenanceSectionAll().then((res) => {
        this.maintenanceSectionList = res.data;
      });
    },
    /** 查询路线列表 */
    getRouteList() {
      listAllRoute().then((res) => {
        this.routeList = res.data;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      // this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        type: this.type,
        partsType: this.type,
        details: null,
        category: null,
        kahunaId: null,
        kahunaName: null,
        kahunaSign: null,
        oprUserId: null,
        oprUserName: null,
        oprUserSign: null,
        checkTime: null,
        status: null,
        auditTime: null,
        image: null,
        remark: null,
        assetId: null,
        assetName: null,
        assetCode: null,
        routeId: null,
        routeName: null,
        routeCode: null,
        propertyUnitId: null,
        propertyUnitName: null,
        maintainUnitId: null,
        maintainUnitName: null,
        maintenanceSectionId: null,
        maintenanceSectionName: null,
        centerStake: null,
        weather: null,
        hasDisease: null,
      };
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm('queryForm');
      this.queryParams.managementMaintenanceBranchId = undefined;
      this.queryParams.maintenanceSectionId = undefined;
      this.queryParams.routeCodes = undefined;
      this.$nextTick(() => {
        this.handleQuery();
      });
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      console.log(selection);
      this.ids = selection.map((item) => item.id);
      this.single = selection.length != 1;
      this.multiple = !selection.length;
    },
    // 表格点击勾选
    handleRowClick(row) {
      row.isSelected = !row.isSelected;
      this.$refs.table.toggleRowSelection(row);
    },
    // 勾选高亮
    rowStyle({ row, rowIndex }) {
      if (this.ids.includes(row.id)) {
        return { 'background-color': '#b7daff', color: '#333' };
      } else {
        return { 'background-color': '#fff', color: '#333' };
      }
    },
    handleCommit(row) {
      const id = row.id || this.ids;
      const type = this.type;
      if (row.id) {
        getAssetCheck({ id, type }).then((response) => {
          this.form = { ...response.data, partsType: this.type };
          this.open = true;
          this.title = `提交${this.partsType[this.type]}`;
        });
      } else {
        this.$modal
          .confirm(`是否确认提交选中的 ${id.length} 条数据项？`)
          .then(function () {
            return Promise.all(id.map((i) => updateAssetCheck({ id: i, status: 2, type })));
          })
          .then(() => {
            this.getList();
            this.$modal.msgSuccess('提交成功');
          });
      }
    },
    handleAudit(row) {
      const id = row.id || this.ids;
      const type = this.type;
      if (row.id) {
        getAssetCheck({ id, type }).then((response) => {
          this.form = { ...response.data, partsType: this.type };
          this.open = true;
          this.title = `审核${this.partsType[this.type]}`;
        });
      } else {
        // 设置默认时间为当前时间
        const now = new Date();
        const year = now.getFullYear();
        const month = String(now.getMonth() + 1).padStart(2, '0');
        const day = String(now.getDate()).padStart(2, '0');
        const hours = String(now.getHours()).padStart(2, '0');
        const minutes = String(now.getMinutes()).padStart(2, '0');
        const seconds = String(now.getSeconds()).padStart(2, '0');
        this.auditTime = `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
        this.auditDialogVisible = true;
      }
    },
    /** 批量审核确认 */
    handleBatchAudit(status) {
      if (!this.auditTime) {
        this.$message.warning('请选择审核时间');
        return;
      }
      const id = this.ids;
      const type = this.type;
      Promise.all(
        id.map((i) =>
          updateAssetCheck({
            id: i,
            status: status,
            type,
            auditTime: this.auditTime,
          })
        )
      ).then(() => {
        this.auditDialogVisible = false;
        this.auditTime = null;
        this.getList();
        this.$modal.msgSuccess('审核成功');
      });
    },
    handleView(row) {
      const id = row.id || this.ids;
      const type = this.type;
      getAssetCheck({ id, type }).then((response) => {
        this.form = { ...response.data, partsType: this.type };
        this.open = true;
        this.title = `查看${this.partsType[this.type]}`;
      });
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = `添加${this.partsType[this.type]}`;
    },
    /** 新增按钮操作 */
    handleGenerate() {
      this.openGenerate = true;
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      // this.reset();
      const id = row.id || this.ids;
      const type = this.type;
      getAssetCheck({ id, type }).then((response) => {
        this.form = { ...response.data, partsType: this.type };
        this.open = true;
        this.title = `修改${this.partsType[this.type]}`;
      });
    },
    /** 提交按钮 */
    submitForm: function (data) {
      if (!(data instanceof PointerEvent)) {
        // 处理状态描述为空的情况
        if (this.form.details) {
          this.form.details.forEach((item) => {
            if (!item.ignore && (!item.defect || item.defect.trim() === '')) {
              item.defect = '未见异常';
            }
            if (!item.ignore && (!item.advice || item.advice.trim() === '')) {
              item.advice = '正常保养';
            }
          });
        }
        updateAssetCheck(data).then((response) => {
          this.$modal.msgSuccess(`${this.title.substring(0, 2)}成功`);
          this.open = false;
          this.getList();
        });
      } else {
        this.$refs.assetCheckInsertOrUpdate.$refs['form'].validate((valid) => {
          if (valid) {
            // 处理状态描述为空的情况
            if (this.form.details) {
              this.form.details.forEach((item) => {
                if (!item.ignore && (!item.defect || item.defect.trim() === '')) {
                  item.defect = '未见异常';
                }
                if (!item.ignore && (!item.advice || item.advice.trim() === '')) {
                  item.advice = '正常保养';
                }
              });
            }

            if (this.form.id != null) {
              updateAssetCheck(this.form).then((response) => {
                this.$modal.msgSuccess('修改成功');
                this.open = false;
                this.getList();
              });
            } else {
              addAssetCheck(this.form).then((response) => {
                this.$modal.msgSuccess('新增成功');
                this.open = false;
                this.getList();
              });
            }
          }
        });
      }
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const id = row.id || this.ids;
      const type = this.type;
      if (row.id) {
        this.$modal
          .confirm(
            `是否确认删除${this.partsType[type].substring(0, 2)}名称是 ${row.assetName} 的数据项？`
          )
          .then(function () {
            return delAssetCheck({ idList: [row.id], type });
          })
          .then(() => {
            this.getList();
            this.$modal.msgSuccess('删除成功');
          });
      } else {
        // console.log(id.map(i => `idlist=${encodeURIComponent(i)}`).join('&'))
        this.$modal
          .confirm(`是否确认删除选中的 ${id.length} 条数据项？`)
          .then(function () {
            return delAssetCheck({ idList: id, type });
          })
          .then(() => {
            this.getList();
            this.$modal.msgSuccess('删除成功');
          });
      }
    },
    /** 导出按钮操作 */
    handleExportCard() {
      let queryParams = {};
      if (this.ids.length === 0) {
        queryParams = {
          ...this.queryParams,
          type: this.type,
          dataRule: true,
        };
        delete queryParams.checkTime;
        delete queryParams.departmentIdList;
      } else {
        queryParams = {
          checkIds: this.ids,
          type: this.type,
          dataRule: true,
          status: this.queryParams.status,
          statusList: this.queryParams.statusList,
        };
      }
      // 先获取数据总数，等待结果后再显示确认框
      getAssetTotalCount(queryParams).then((res) => {
        const export_count = res.data;

        const confirmMessage =
          this.ids.length === 0
            ? `根据搜索条件，本次导出共有 ${export_count} 条数据，是否确认导出？`
            : `根据选中条件，本次导出共有 ${export_count} 条数据，是否确认导出？`;

        this.$confirm(confirmMessage, '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        })
          .then(() => {
            const now = new Date(); // 添加 now 的定义
            const timeStr = `${now.getFullYear()}年${
              now.getMonth() + 1
            }月${now.getDate()}日${now.getHours()}时${now.getMinutes()}分${now.getSeconds()}秒`;
            const fileName = `${this.partsType[this.type]}_${timeStr}.xlsx`;
            this.download(
              'patrol/assetCheck/exportAssetReportCard',
              {
                ...queryParams,
              },
              fileName,
              {
                parameterType: 'body', // 设置参数类型为 body
                headers: {
                  'Content-Type': 'application/json', // 设置请求头为 JSON
                },
              }
            );
          })
          .catch((e) => {
            console.log(e);
            this.$message({
              type: 'info',
              message: '已取消导出',
            });
          });
      });
    },

    /** 导出按钮操作 */
    handleExport() {
      let queryParams = {};
      if (this.ids.length === 0) {
        queryParams = {
          ...this.queryParams,
          type: this.type,
          dataRule: true,
        };
        delete queryParams.checkTime;
        delete queryParams.departmentIdList;
      } else {
        queryParams = {
          checkIds: this.ids,
          type: this.type,
          dataRule: true,
          status: this.queryParams.status,
          statusList: this.queryParams.statusList,
        };
      }
      // 先获取数据总数，等待结果后再显示确认框
      getAssetTotalCount(queryParams).then((res) => {
        const export_count = res.data;

        const confirmMessage =
          this.ids.length === 0
            ? `根据搜索条件，本次导出共有 ${export_count} 条数据，是否确认导出？`
            : `根据选中条件，本次导出共有 ${export_count} 条数据，是否确认导出？`;

        this.$confirm(confirmMessage, '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        })
          .then(() => {
            const now = new Date(); // 添加 now 的定义
            const timeStr = `${now.getFullYear()}年${
              now.getMonth() + 1
            }月${now.getDate()}日${now.getHours()}时${now.getMinutes()}分${now.getSeconds()}秒`;
            const fileName = `${this.partsType[this.type]}_${timeStr}.xlsx`;
            this.download(
              'patrol/assetCheck/export',
              {
                ...queryParams,
              },
              fileName,
              {
                parameterType: 'body', // 设置参数类型为 body
                headers: {
                  'Content-Type': 'application/json', // 设置请求头为 JSON
                },
              }
            );
          })
          .catch((e) => {
            console.log(e);
            this.$message({
              type: 'info',
              message: '已取消导出',
            });
          });
      });
    },
    /** 导入按钮操作 */
    handleImport() {
      this.upload.title = '用户导入';
      this.upload.open = true;
    },
    /** 下载模板操作 */
    importTemplate() {
      this.download('system/user/importTemplate', {}, `user_template.xlsx`);
    },
    // 文件上传中处理
    handleFileUploadProgress(event, file, fileList) {
      this.upload.isUploading = true;
    },
    // 文件上传成功处理
    handleFileSuccess(response, file, fileList) {
      this.upload.open = false;
      this.upload.isUploading = false;
      this.$refs.upload.clearFiles();
      this.$alert(
        "<div style='overflow: auto;overflow-x: hidden;max-height: 70vh;padding: 10px 20px 0;'>" +
        response.msg +
        '</div>',
        '导入结果',
        { dangerouslyUseHTMLString: true }
      );
      this.getList();
    },
    // 提交上传文件
    submitFileForm() {
      this.$refs.upload.submit();
    },
    /** 处理生成对话框关闭 */
    handleGenerateClose(visible) {
      if (!visible) {
        this.$nextTick(() => {
          this.$refs.assetCheckGenerate.reset();
        });
      }
    },
    /** 批量修改审核时间操作 */
    handleUpdateAuditTime() {
      if (this.multiple) {
        this.$message.warning('请至少选择一条记录');
        return;
      }
      // 设置默认时间为当前时间
      const now = new Date();
      const year = now.getFullYear();
      const month = String(now.getMonth() + 1).padStart(2, '0');
      const day = String(now.getDate()).padStart(2, '0');
      const hours = String(now.getHours()).padStart(2, '0');
      const minutes = String(now.getMinutes()).padStart(2, '0');
      const seconds = String(now.getSeconds()).padStart(2, '0');
      this.newAuditTime = `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
      this.updateAuditTimeDialogVisible = true;
    },

    /** 提交修改审核时间 */
    submitUpdateAuditTime() {
      if (!this.newAuditTime) {
        this.$message.warning('请选择审核时间');
        return;
      }

      const id = this.ids;
      const type = this.type;

      Promise.all(
        id.map((i) =>
          updateAssetCheck({
            id: i,
            type,
            auditTime: this.newAuditTime,
          })
        )
      ).then(() => {
        this.updateAuditTimeDialogVisible = false;
        this.newAuditTime = null;
        this.getList();
        this.$modal.msgSuccess('修改审核时间成功');
      });
    },
  },
};
</script>
<style scoped>
.tableDiv {
  background-color: white;
  padding-bottom: 10px;
}

/*::v-deep .vue-treeselect__control {*/
/*  height: auto;*/
/*}*/

.asset-check-edit-dialog ::v-deep .el-dialog__body {
  padding: 10px 20px;
  height: 75vh;
  overflow-y: auto;
  scrollbar-width: none·;
}
.export-preview-dialog ::v-deep .el-dialog__body {
  padding: 10px;
}
</style>
