<template>
	<div class="component-container">
		<!-- 查询条件区域 -->
		<div class="query-container">
			<el-row :gutter="24" style="width: 80%">
				<!-- 下拉框选择路线-->
				<el-col :span="4" :offset="0">
					<el-select v-model="queryForm.routeCode" placeholder="请选择路线" size="small">
						<el-option label="请选择路线" value="" />
					</el-select>
				</el-col>
				<!--有无事件-->
				<el-col :span="4" :offset="0">
					<el-select v-model="queryForm.hasEvent" placeholder="事件成因" size="small">
						<el-option
							v-for="dict in dict.type.maintenance_type"
							:key="dict.value"
							:label="dict.label"
							:value="dict.value"
						/>
					</el-select>
				</el-col>
				<!--搜索按钮 导出清单 导出卡片-->
				<el-col :span="12" :offset="0">
					<el-button size="small" type="primary" @click="handleSearch">搜索</el-button>
					<el-button size="small" type="primary" @click="handleExport">导出清单</el-button>
					<el-button size="small" type="primary" @click="handleExport">导出卡片</el-button>
				</el-col>
			</el-row>
		</div>
		<!-- 表格区域 -->
		<div class="table-container">
			<el-table v-adjust-table :data="tableData" style="width: 100%" height="calc(100% - 50px)">
				<el-table-column prop="number" label="序号" width="60" />
				<el-table-column prop="sectionName" label="路段名称" />
				<el-table-column prop="direction" label="上下行" />
				<el-table-column prop="stakePosition" label="桩号位置" />
				<el-table-column prop="diseaseType" label="病害类型" />
				<el-table-column prop="eventCause" label="事件成因" />
				<el-table-column prop="disposalType" label="处置类型" />
				<el-table-column prop="reportType" label="上报类型" />
				<el-table-column prop="processStatus" label="事件处置进度" />
				<el-table-column prop="reporter" label="上报人" />
				<el-table-column prop="reportTime" label="上报时间" />
				<el-table-column prop="eventDescription" label="事件描述" />
			</el-table>
			<pagination
				v-show="total > 0"
				:total="total"
				:page.sync="queryForm.pageNum"
				:limit.sync="queryForm.pageSize"
				@pagination="getList"
			/>
		</div>
	</div>
</template>

<script>
export default {
	name: 'Diseases',
	dicts: ['maintenance_type'],
	data() {
		return {
			queryForm: {
				pageNum: 1,
				pageSize: 10,
			},
			tableData: [],
			total: 0,
		}
	},
	methods: {
		handleSearch() {
			this.getList()
		},
		handleExport() {
			console.log('导出')
		},
		handleExportCard() {
			console.log('导出卡片')
		},
		getList() {
			console.log('获取列表')
		},
	},
}
</script>

<style scoped>
.component-container {
	height: 100%;
	display: flex;
	flex-direction: column;
}

.query-container {
	padding-bottom: 20px;
}

.table-container {
	flex: 1;
	overflow: hidden;
}

.el-input,
.el-select {
	width: 100%;
}

.el-button {
	margin-right: 10px;
}

.el-button:last-child {
	margin-right: 0;
}
</style>
