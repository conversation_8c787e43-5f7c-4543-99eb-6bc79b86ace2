

var pi = 3.1415926535897932384626;
var a = 6378245.0;
var ee = 0.00669342162296594323;

// 将 GCJ-02 转换为 WGS-84 的函数
export function gcj02towgs84(lng, lat) {
  if (out_of_china(lng, lat)) {
    return [lng, lat];
  } else {
    var dlat = transformlat(lng - 105.0, lat - 35.0);
    var dlng = transformlng(lng - 105.0, lat - 35.0);
    var radlat = (lat / 180.0) * pi;
    var magic = Math.sin(radlat);
    magic = 1 - ee * magic * magic;
    var sqrtmagic = Math.sqrt(magic);
    dlat = (dlat * 180.0) / (((a * (1 - ee)) / (magic * sqrtmagic)) * pi);
    dlng = (dlng * 180.0) / ((a / sqrtmagic) * Math.cos(radlat) * pi);
    return [lng * 2 - (lng + dlng), lat * 2 - (lat + dlat)];
  }
}
/**
 * 将 WGS-84 坐标系中的经纬度坐标转换为 GCJ-02 坐标系中的经纬度坐标
 * @param {number} lng - WGS-84 坐标系中的经度
 * @param {number} lat - WGS-84 坐标系中的纬度
 * @returns {Array<number>} - 转换后的 GCJ-02 坐标系中的经纬度坐标
 */
export function wgs84togcj02(lng, lat) {
  // var x_PI = 3.14159265358979324 * 3000.0 / 180.0;
  var PI = 3.14159265358979324;
  var a = 6378245.0;
  var ee = 0.00669342162296594323;
  if (out_of_china(lng, lat)) {
    return [lng, lat]
  }else {
    var dlat = transformlat(lng - 105.0, lat - 35.0);
    var dlng = transformlng(lng - 105.0, lat - 35.0);
    var radlat = lat / 180.0 * PI;
    var magic = Math.sin(radlat);
    magic = 1 - ee * magic * magic;
    var sqrtmagic = Math.sqrt(magic);
    dlat = (dlat * 180.0) / ((a * (1 - ee)) / (magic * sqrtmagic) * PI);
    dlng = (dlng * 180.0) / (a / sqrtmagic * Math.cos(radlat) * PI);
    var mglat = lat + dlat;
    var mglng = lng + dlng;
    // return [lng * 2 - mglng, lat * 2 - mglat]
    return [mglng, mglat]
  }
}


// 判断是否在国内
function out_of_china(lng, lat) {
  return (
    lng < 72.004 || lng > 137.8347 || lat < 0.8293 || lat > 55.8271 || false
  );
}

// 转换纬度的函数
function transformlat(lng, lat) {
  var ret =
    -100.0 +
    2.0 * lng +
    3.0 * lat +
    0.2 * lat * lat +
    0.1 * lng * lat +
    0.2 * Math.sqrt(Math.abs(lng));
  ret +=
    ((20.0 * Math.sin(6.0 * lng * pi) + 20.0 * Math.sin(2.0 * lng * pi)) *
      2.0) /
    3.0;
  ret +=
    ((20.0 * Math.sin(lat * pi) + 40.0 * Math.sin((lat / 3.0) * pi)) * 2.0) /
    3.0;
  ret +=
    ((160.0 * Math.sin((lat / 12.0) * pi) + 320 * Math.sin((lat * pi) / 30.0)) *
      2.0) /
    3.0;
  return ret;
}

// 转换经度的函数
function transformlng(lng, lat) {
  var ret =
    300.0 +
    lng +
    2.0 * lat +
    0.1 * lng * lng +
    0.1 * lng * lat +
    0.1 * Math.sqrt(Math.abs(lng));
  ret +=
    ((20.0 * Math.sin(6.0 * lng * pi) + 20.0 * Math.sin(2.0 * lng * pi)) *
      2.0) /
    3.0;
  ret +=
    ((20.0 * Math.sin(lng * pi) + 40.0 * Math.sin((lng / 3.0) * pi)) * 2.0) /
    3.0;
  ret +=
    ((150.0 * Math.sin((lng / 12.0) * pi) +
      300.0 * Math.sin((lng / 30.0) * pi)) *
      2.0) /
    3.0;
  return ret;
}