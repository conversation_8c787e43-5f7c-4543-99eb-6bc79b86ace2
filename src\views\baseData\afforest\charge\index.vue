<template>
  <PageContainer>
    <template slot="search">
      <div style="display: flex; align-items: center; margin: 0">
        <CascadeSelection
          style="min-width: 192px"
          :form-data="queryParams"
          v-model="queryParams"
          types="201"
          multiple
        />
        <div style="margin: 0 20px">
          <el-date-picker
            style="width: 100%"
            v-model="queryParams.year"
            value-format="yyyy"
            type="year"
            placeholder="年份"
          />
        </div>
        <div style="min-width: 220px; height: 32px">
          <el-button
            type="primary"
            icon="el-icon-search"
            class="mb8"
            v-hasPermi="['baseData:afforestCharge:getAfforestCharge']"
            @click="handleQuery"
            >搜索</el-button
          >
          <el-button icon="el-icon-refresh" class="mb8" @click="resetQuery"
            >重置</el-button
          >
        </div>
      </div>
    </template>
    <template slot="header">
      <el-button
        v-hasPermi="['baseData:afforestCharge:export']"
        type="primary"
        style="margin-bottom:10px"
        @click="exportList"
        >数据导出</el-button
      >
    </template>
    <template slot="body">
      <el-table
        v-adjust-table
        ref="table"
        height="100%"
        style="width: 100%"
        :header-cell-style="{
          background: '#F2F3F5',
          color: '#212529',
          'font-weight': '700',
          'font-size': '14px',
        }"
        :cell-style="{ height: '36px' }"
        :row-style="rowStyle"
        border
        v-loading="loading"
        :data="staticList"
        @selection-change="handleSelectionChange"
        @row-click="handleRowClick"
      >
        <el-table-column type="selection" width="50" align="center" />
        <el-table-column
          fixed
          label="序号"
          type="index"
          width="50"
          align="center"
          >
          <template v-slot="scope">
            {{
              scope.$index +
              (queryParams.pageNum - 1) * queryParams.pageSize +
              1
            }}
          </template>
        </el-table-column>

        <el-table-column
          fixed
          label="管理处"
          align="center"
          prop="managementMaintenanceName"
          min-width="140"
          show-overflow-tooltip
        />
        <el-table-column
          label="管养分处"
          align="center"
          prop="managementMaintenanceBranchName"
          min-width="140"
          show-overflow-tooltip
        />
        <el-table-column
          label="养护路段"
          align="center"
          prop="maintenanceSectionId"
          min-width="140"
          show-overflow-tooltip
        />
        <el-table-column
          label="路线编码"
          align="center"
          prop="routeCode"
          min-width="140"
          show-overflow-tooltip
        />
        <el-table-column
          label="年度"
          align="center"
          prop="year"
          min-width="140"
          show-overflow-tooltip
        />
        <el-table-column
          label="投入费用（万元）"
          align="center"
          prop="cost"
          min-width="140"
          show-overflow-tooltip
        />
      </el-table>
      <div class="totalClass">
        <span>合计</span>
        <span>{{ totalCost }}</span>
      </div>
      <pagination
        :total="total"
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
        :pageSizes="[10, 20, 30, 50, 100, 1000]"
        @pagination="getList"
      />
    </template>
  </PageContainer>
</template>

<script>
import { listAfforestChargeRecords } from "@/api/baseData/afforest/statistic/index";

import { getAssetSubclass } from "@/api/baseData/facility/baseInfo/index";

import { getToken } from "@/utils/auth";
import CascadeSelection from "@/components/CascadeSelection/index.vue";

export default {
  name: "Charge",
  components: { CascadeSelection },
  dicts: ["sys_asset_type"],
  data() {
    return {
      loading: true,
      title: "",
      assetSubclassList: [],
      ids: [],
      single: true,
      multiple: true,
      total: 0,
      staticList: null,
      totalCost: 0,
      queryParams: {
        pageNum: 1,
        pageSize: 20,
        mainTypeId: 7,
      },
    };
  },
  watch: {},
  created() {
    this.getList();
    this.getAssetSubclassList();
    this.getOptions();
  },
  methods: {
    // 获取搜索栏相关字典数据
    getOptions() {},
    //查询字长子类
    getAssetSubclassList() {
      getAssetSubclass({ mainTypeId: 7 }).then((res) => {
        this.assetSubclassList = res;
      });
    },
    getDynamicSelect(val) {
      let data = this.assetSubclassList.find(
        (item) => item.id == this.queryParams.typeId
      );
      this.queryParams.mainTypeId = data.mainType;
    },

    // 获取表格数据
    getList() {
      this.loading = true;

      listAfforestChargeRecords(this.queryParams).then((response) => {
        this.staticList = response.rows;
        this.totalCost = response.totalCost;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id);
      this.single = selection.length != 1;
      this.multiple = !selection.length;
    },
    // 勾选高亮
    rowStyle({ row, rowIndex }) {
      if (this.ids.includes(row.id)) {
        return { "background-color": "#b7daff", color: "#333" };
      } else {
        return { "background-color": "#fff", color: "#333" };
      }
    },
    // 搜索按钮
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    // 重置按钮
    resetQuery() {
      this.dateRange = [];
      this.queryParams = {
        pageNum: 1,
        pageSize: 20,
        mainTypeId: 7,
      };
      this.handleQuery();
    },
    // 表单重置
    reset() {
      this.resetForm("form");
    },

    closeImportAdd(v) {
      this.showImportAdd = false;
      if (v) this.getList();
    },
    // 导出按钮
    exportList() {
      if (this.ids.length === 0) {
        this.$modal
          .confirm("即将导出所有表格数据，此过程可能花费时间较长，是否继续？")
          .then(() => {
            this.download(
              "/baseData/afforest/charge/export",
              this.queryParams,
              `charge_${new Date().getTime()}.xlsx`,
              {
                headers: { "Content-Type": "application/json;" },
                parameterType: "body",
              }
            );
          })
          .catch(() => {});
      } else {
        this.$modal
          .confirm(`已选择${this.ids.length}条绿化费用统计数据，确认导出？`)
          .then(() => {
            this.download(
              "/baseData/afforest/charge/export",
              { ids: this.ids },
              `charge__${new Date().getTime()}.xlsx`,
              {
                headers: { "Content-Type": "application/json;" },
                parameterType: "body",
              }
            );
          })
          .catch(() => {});
      }
    },
    // 表格点击勾选
    handleRowClick(row) {
      row.isSelected = !row.isSelected;
      this.$refs.table.toggleRowSelection(row);
    },
  },
};
</script>

<style lang="scss" scoped>
@import "~@/assets/styles/element-ui-global.scss";

.totalClass {
  display: flex;
  justify-content: space-between;
  border: 1px solid #dfe6ec;
  padding: 10px 30px;
}
</style>
