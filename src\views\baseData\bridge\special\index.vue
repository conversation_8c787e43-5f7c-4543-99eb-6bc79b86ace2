<template>
  <PageContainer>
    <template slot="search">
      <div style="display: flex; align-items: center; margin: 0">
        <CascadeSelection
          style="min-width: 192px"
          :form-data="queryParams"
          v-model="queryParams"
          types="201"
          multiple
        />
        <div style="margin:0 20px;">
          <el-input
            v-model="queryParams.bridgeName"
            placeholder="桥梁名称"
            clearable
          />
        </div>

        <div style="margin: 0 10px 0 0">
          <el-input
              v-model="queryParams.bridgeCode"
              placeholder="桥梁编码"
              clearable
          />
        </div>

        <el-select
            v-model="queryParams.status"
            placeholder="数据状态"
            clearable
            style="margin: 0 10px 0 0"
        >
          <el-option
              v-for="dict in dict.type.base_data_state"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
          />
        </el-select>
        <div style="min-width:220px;">
          <el-button
            v-hasPermi="['baseData:specialDetection:listPage']"
            type="primary"
            icon="el-icon-search"
            @click="handleQuery"
          >搜索</el-button>
          <el-button
            icon="el-icon-refresh"
            @click="resetQuery"
          >重置</el-button>
        </div>
      </div>
    </template>
    <template slot="header">
      <div class="button-list">
        <el-button
          v-hasPermi="['baseData:specialDetection:add']"
          type="primary"
          @click="handleAdd"
        >新增</el-button>
        <el-button
          v-hasPermi="['baseData:specialDetection:edit']"
          type="primary"
          @click="handleUpdate"
        >编辑</el-button>
        <el-button
          v-hasPermi="['baseData:specialDetection:remove']"
          type="primary"
          @click="handleDelete"
        >删除</el-button>
        <el-button
          v-hasPermi="['baseData:specialDetection:export']"
          type="primary"
          @click="exportList"
        >导出清单</el-button>
        <el-button
          v-hasPermi="['baseData:specialDetection:exportCard']"
          type="primary"
          @click="downloadCard"
        >导出卡片</el-button>
      </div>
    </template>
    <template slot="body">
      <el-table
        v-adjust-table
        ref="table"
        height="100%"
        style="width: 100%"
        :header-cell-style="{'height': '36px'}"
        :row-style="rowStyle"
        v-loading="loading"
        border
        :data="staticList"
        @selection-change="handleSelectionChange"
        @row-click="handleRowClick"
      >
        <el-table-column
          type="selection"
          width="50"
          align="center"
        />
        <el-table-column
          fixed
          label="序号"
          type="index"
          width="50"
          align="center"
        >
          <template v-slot="scope">
            {{ (scope.$index + (queryParams.pageNum - 1) * queryParams.pageSize)+1 }}
          </template>
        </el-table-column>
        <el-table-column
          fixed
          label="桥梁名称"
          align="center"
          prop="bridgeName"
          min-width="140"
          show-overflow-tooltip
        />
        <el-table-column
          label="桥梁编码"
          align="center"
          prop="bridgeCode"
          min-width="140"
          show-overflow-tooltip
        />
        <el-table-column
          label="中心桩号"
          align="center"
          prop="centerStake"
          min-width="140"
        >
          <template slot-scope="scope">
            {{ formatPile(scope.row.centerStake) }}
          </template>
        </el-table-column>
        <el-table-column
          label="气候及环境温度"
          align="center"
          prop="weather"
          min-width="140"
        />
        <el-table-column
          label="特殊检查完成机构"
          align="center"
          prop="organization"
          min-width="140"
          show-overflow-tooltip
        />
        <el-table-column
          label="检查类型"
          align="center"
          prop="checkType"
          min-width="140"
          show-overflow-tooltip
        />
        <el-table-column
          label="检查结论"
          align="center"
          prop="conclusion"
          min-width="140"
          show-overflow-tooltip
        />
        <el-table-column
          label="负责人"
          align="center"
          prop="principal"
          min-width="140"
          show-overflow-tooltip
        />
        <el-table-column
          label="记录人"
          align="center"
          prop="recorder"
          min-width="140"
          show-overflow-tooltip
        />
        <el-table-column
          label="特殊检查时间"
          align="center"
          prop="checkDate"
          min-width="140"
          show-overflow-tooltip
        />

        <el-table-column
            label="数据状态"
            align="center"
            prop="status"
            min-width="130"
        >
          <template slot-scope="{ row }">
            <el-link

                :underline="false"
                :type="{ 1: 'success', 2: 'primary', 3: 'info', 4: 'danger',5:'success'}[row.status]"

            >
              <DictTag
                  :value="row.status"
                  :options="dict.type.base_data_state"
              />
            </el-link>
          </template>
        </el-table-column>
        <el-table-column
          fixed="right"
          label="操作"
          align="center"
          width="100"
        >
          <template slot-scope="scope">
            <el-button
              v-hasPermi="['baseData:specialDetection:query']"
              type="text"
              @click="handleView(scope.row)"
            >查看明细</el-button>
          </template>
        </el-table-column>

      </el-table>
      <pagination
        :total="total"
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
        :pageSizes="[10, 20, 30, 50, 100, 1000]"
        @pagination="getList"
      />
    </template>
    <add-and-edit
      v-if="showAddEdit"
      :forView="forView"
      :formData="formData"
      :title="title"
      :showAddEdit="showAddEdit"
      @close="() => {showAddEdit = false;formData ={}}"
      @refresh="() => {showAddEdit = false;getList();formData ={}}"
    />
  </PageContainer>
</template>


<script>
import {
  listBridgeDetectionRecords,
  detectionExport,
  getDetectionBridge,
  delDetectionBridge
} from '@/api/baseData/bridge/specialExam/index'

import { getToken } from '@/utils/auth'
import AddAndEdit from './components/addAndEdit.vue'
import SelectTreeCheckbox from '@/components/DeptTmpl/selectTreeCheckbox'
import RouteLine from '@/components/RouteLine'
import RouteLoad from '@/components/RouteRoad'
import CascadeSelection from '@/components/CascadeSelection/index.vue'

export default {
  name: 'Special',
  components: {
    AddAndEdit,
    SelectTreeCheckbox,
    RouteLoad,
    RouteLine,
    CascadeSelection
  },
  dicts: ['base_data_state'],
  data() {
    return {
      loading: true,
      showAddEdit: false,
      forView: false,
      title: '',
      formData: {},
      ids: [],
      total: 0,
      staticList: [],
      queryParams: {
        pageNum: 1,
        pageSize: 20
      },
      selection: []
    }
  },
  watch: {},
  created() {
    this.getList()
  },
  methods: {
    getList() {
      this.loading = true
      listBridgeDetectionRecords(this.queryParams)
        .then(res => {
          if (res.code === 200) {
            this.staticList = res.rows
            this.total = res.total
          }
        })
        .finally(() => {
          this.loading = false
        })
    },
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.selection = selection
    },
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    rowStyle({ row, rowIndex }) {
      if (this.ids.includes(row.id)) {
        return { 'background-color': '#b7daff', color: '#333' }
      } else {
        return { 'background-color': '#fff', color: '#333' }
      }
    },
    resetQuery() {
      this.queryParams = {
        pageNum: 1,
        pageSize: 20
      }
      this.handleQuery()
    },
    handleAdd() {
      this.forView = false
      this.showAddEdit = true
      this.title = '新增桥梁特殊检查数据'
    },
    handleView(row) {
      getDetectionBridge(row.id).then(res => {
        if (res && res.data) {
          this.formData.baseInfoForm = res.data
          this.showAddEdit = true
          this.forView = true
          this.title = '查看桥梁特殊检查数据'
        }
      })
    },
    handleRowClick(row) {
      row.isSelected = !row.isSelected
      this.$refs.table.toggleRowSelection(row)
    },
    handleUpdate() {
      if (this.ids.length != 1) {
        this.$message.warning('请选择一条数据进行编辑！')
        return
      } else {
        this.forView = false
        let canEdit = true
        this.staticList.map(e => {
          if (this.ids[0] == e.id && e.isLocked) {
            canEdit = false
            this.$message.error(
              '桥梁特殊检查名称：' + e.specialExamName + '已锁定，不允许编辑！'
            )
          }
        })
        if (canEdit) {
          getDetectionBridge(this.ids[0]).then(res => {
            if (res && res.data) {
              this.formData.baseInfoForm = res.data
              this.formData.baseInfoForm.status=this.selection[0].status
              this.title = '编辑桥梁特殊检查数据'
              this.showAddEdit = true
            }
          })
        }
      }
    },
    handleDelete() {
      if (this.ids.length == 0) {
        this.$message.warning('请选择至少一条数据进行删除！')
        return
      }
      this.$modal
        .confirm('确认删除？')
        .then(() => {
          delDetectionBridge(this.ids).then(res => {
            if (res && res.code == '200') {
              this.getList()
              this.$modal.msgSuccess('删除成功')
            }
          })
        })
        .catch(() => {})
    },
    exportList() {
      if (this.staticList.length === 0) return
      if (this.ids.length === 0) {
        this.$modal
          .confirm('即将导出所有表格数据，此过程可能花费时间较长，是否继续？')
          .then(() => {
            this.download(
              '/baseData/bridge/special/detection/export',
              this.queryParams,
              `road_interflow_${new Date().getTime()}.xlsx`,
              {
                headers: { 'Content-Type': 'application/json;' },
                parameterType: 'body'
              }
            )
          })
          .catch(() => {})
      } else {
        this.$modal
          .confirm(`已选择${this.ids.length}条桥梁数据，确认导出清单？`)
          .then(() => {
            this.download(
              '/baseData/bridge/special/detection/export',
              { ids: this.ids },
              `static_${new Date().getTime()}.xlsx`,
              {
                headers: { 'Content-Type': 'application/json;' },
                parameterType: 'body'
              }
            )
          })
          .catch(() => {})
      }
    },
    downloadCard() {
      if (this.staticList.length === 0) return
      this.$modal
        .confirm(`即将导出卡片，点击确认继续。`)
        .then(() => {
          this.download(
            '/baseData/bridge/special/detection/exportCard',
            { ids: this.ids, ...this.queryParams },
            '',
            {
              headers: { 'Content-Type': 'application/json;' },
              parameterType: 'body'
            }
          )
        })
        .catch(() => {})
    }
  }
}
</script>

<style lang="scss" scoped>
.button-list {
  border-radius: 4px;
  width: 100%;
  .el-button {
    margin-bottom: 10px;
    margin-right: 10px;
    margin-left: 0;
  }
}
</style>
<style lang="scss">
::v-deep .el-divider--horizontal{
  margin: calc((20% - 148px) / 2) 0;
}
</style>
