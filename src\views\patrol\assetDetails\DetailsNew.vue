<template>
	<PageContainer>
		<template slot="search">
			<div class="searchBox">
				<CascadeSelection style="grid-column: 1 / 3" :form-data="queryForm" v-model="queryForm" types="201" multiple />
				<el-input v-model="queryForm.assetCode" :placeholder="`请输入${partsType[type].substring(0, 2)}编码`" clearable
					prefix-icon="el-icon-user" @keyup.enter.native="handleQuery" />
				<el-input v-model="queryForm.assetName" :placeholder="`请输入${partsType[type].substring(0, 2)}名称`" clearable
					prefix-icon="el-icon-user" @keyup.enter.native="handleQuery" />
				<el-select v-model="queryForm.inspectionType" placeholder="请选择检查类型" @change="handleInspectionTypeChange"
					:clearable="false">
					<template v-if="type !== 3">
						<el-option :label="type === 1 ? '桥梁经常检查' : '隧道经常检查'" :value="type === 1 ? 2 : 6" />
						<el-option :label="type === 1 ? '桥梁日常巡查' : '隧道日常巡查'" :value="type === 1 ? 1 : 5" />
					</template>
					<template v-else>
						<el-option label="涵洞经常检查" :value="4" />
					</template>
				</el-select>
				<el-date-picker v-model="queryForm.expiry" :type="datePickerType"
					:placeholder="`请选择${partsType[type].substring(0, 2)}${datePickerPlaceholder}`" :clearable="false"
					prefix-icon="el-icon-date" :value-format="datePickerFormat" @keyup.enter.native="handleQuery" />
				<el-select v-model="queryForm.isInspected" placeholder="请选择检查状态" clearable
					@change="handleInspectionStatusChange">
					<el-option label="已检查" :value="true" />
					<el-option label="未检查" :value="false" />
				</el-select>
				<DictSelect v-model="queryForm.status" type="patrol_inspection_status" placeholder="请选择状态" clearable
					:disabled="!queryForm.isInspected" />
				<div style="display: flex; gap: 10px">
					<el-button type="primary" icon="el-icon-search" size="mini" @click="queryhandle">
						搜索
					</el-button>
					<el-button icon="el-icon-refresh" size="mini" @click="queryReset">重置</el-button>
				</div>
			</div>
		</template>
		<!-- <template slot="header">
			<el-button v-hasPermi="['baseData:bridge:export']" type="primary" @click="handleExport">导出</el-button>
		</template> -->
		<template slot="body">
			<el-table v-adjust-table ref="table" v-loading="loading" height="100%" border :data="staticList">
				<el-table-column label="序号" type="index" width="50" align="center">
					<template v-slot="scope">
						{{ scope.$index + (pageNum - 1) * pageSize + 1 }}
					</template>
				</el-table-column>
				<el-table-column label="管理处" align="center" prop="managementMaintenanceName" min-width="130"
					show-overflow-tooltip />
				<el-table-column label="路段" align="center" prop="maintenanceSectionName" min-width="130"
					show-overflow-tooltip />
				<el-table-column label="时间" align="center" prop="dateStr" min-width="130" show-overflow-tooltip />
				<el-table-column :label="type == 1 ? '桥梁名称' : type == 2 ? '隧道名称' : type == 3 ? '涵洞名称' : ''" align="center"
					prop="assetName" min-width="130" show-overflow-tooltip />
				<el-table-column :label="type == 1 ? '桥梁编码' : type == 2 ? '隧道编码' : type == 3 ? '涵洞编码' : ''" align="center"
					prop="assetCode" min-width="130" show-overflow-tooltip />
				<el-table-column label="巡查机构" align="center" prop="managementMaintenanceBranchName" min-width="130"
					show-overflow-tooltip />
				<el-table-column label="是否已检查" align="center" prop="isCheckStr" min-width="130" show-overflow-tooltip />
			</el-table>
			<pagination :total="total" :page.sync="pageNum" :limit.sync="pageSize" @pagination="getList" />
		</template>
	</PageContainer>
</template>

<script>
import moment from 'moment'
import CascadeSelection from '@/components/CascadeSelection/index.vue'
import { assetCheckListByJm } from '@/api/patrol/assetDetails'

export default {
	name: 'AssetLedger',
	dicts: ['patrol_inspection_status'],
	components: {
		CascadeSelection,
	},
	props: {
		type: {
			type: Number,
			default: 2,
		},
	},
	data() {
		return {
			queryShow: false,
			showIframe: true,
			partsType: {
				1: '桥梁检查',
				2: '隧道检查',
				3: '涵洞检查',
			},
			queryForm: {
				type: this.type,
				assetName: null,
				assetCode: null,
				expiry: moment().format('YYYY-MM'),
				managementMaintenanceIds: null,
				maintenanceSectionId: null,
				routeCodes: null,
				status: null,
				inspectionType: null,
				isInspected: null,
			},
			// 查询参数
			queryParams: {
				type: this.type,
				assetName: null,
				assetCode: null,
				expiry: moment().format('YYYY-MM'),
				managementMaintenanceIds: null,
				maintenanceSectionId: null,
				routeCodes: null,
				status: null,
				inspectionType: null,
				isInspected: null,
			},
			datePickerType: 'year', // 默认年份选择
			datePickerFormat: 'yyyy',
			datePickerPlaceholder: '年份',
			staticList: [],
			total: 0,
			pageNum: 1,
			pageSize: 50,
			loading: false,
		}
	},
	created() {
		// 获取所有可能的 URL 查询参数
		const {
			type,
			assetName,
			assetCode,
			expiry,
			managementMaintenanceIds,
			maintenanceSectionId,
			routeCodes,
			status,
			inspectionType,
			isInspected,
		} = this.$route.query

		// 根据 inspectionType 确定 type 值
		let derivedType = this.type
		if (inspectionType) {
			const inspectionTypeNum = Number(inspectionType)
			if ([1, 2].includes(inspectionTypeNum)) {
				derivedType = 1 // 桥梁
			} else if ([3, 4].includes(inspectionTypeNum)) {
				derivedType = 3 // 涵洞
			} else if ([5, 6].includes(inspectionTypeNum)) {
				derivedType = 2 // 隧道
			}
		}

		// 创建一个包含 URL 参数的对象
		const urlParams = {
			type: derivedType,
			assetName: assetName || null,
			assetCode: assetCode || null,
			expiry: expiry || moment().format('YYYY-MM'),
			managementMaintenanceIds: managementMaintenanceIds
				? managementMaintenanceIds.split(',')
				: null,
			maintenanceSectionId: maintenanceSectionId || null,
			routeCodes: routeCodes ? routeCodes.split(',') : null,
			status: status || null,
			inspectionType: inspectionType ? Number(inspectionType) : null,
			isInspected: isInspected === undefined ? null : isInspected === 'undefined' ? false : true,
		}

		// 更新 queryForm 和 queryParams
		this.queryForm = { ...this.queryForm, ...urlParams }
		this.queryParams = { ...this.queryParams, ...urlParams }

		// 原有的初始化逻辑
		if (!this.queryForm.inspectionType) {
			this.queryForm.inspectionType = this.type === 3 ? 4 : this.type === 1 ? 2 : 6
			this.queryParams.inspectionType = this.queryForm.inspectionType
		}
		this.handleInspectionTypeChange(this.queryForm.inspectionType)
		if (Object.keys(this.$route.params).length == 0) {
			this.getList();
		}
	},
	methods: {
		queryhandle() {
			this.queryParams = { ...this.queryForm }
			this.getList()
		},
		resetQueryForm() {
			this.queryForm.type = this.type
			this.queryForm.assetName = null
			this.queryForm.assetCode = null
			this.queryForm.expiry = moment().format('YYYY-MM')
			this.queryForm.managementMaintenanceIds = null
			this.queryForm.maintenanceSectionId = null
			this.queryForm.routeCodes = null
			this.queryForm.status = null
			this.queryForm.isInspected = null
			this.queryForm.inspectionType = this.type === 3 ? 4 : this.type === 1 ? 2 : 6
		},
		// 重置查询条件
		queryReset() {
			this.resetQueryForm()
			this.queryhandle()
		},
		handleInspectionStatusChange(val) {
			this.queryForm.isInspected = val
			// 如果选择未检查或清空，将状态设为 null
			if (!val) {
				this.queryForm.status = null
			}
		},
		handleInspectionTypeChange(val) {
			// 根据检查类型设置日期选择器类型
			if (val === 1 || val === 5) {
				// 日常巡查
				this.datePickerType = 'date'
				this.datePickerFormat = 'yyyy-MM-dd'
				this.datePickerPlaceholder = '日期'
				// 设置为当天
				this.queryForm.expiry = moment().format('YYYY-MM-DD')
			} else {
				// 经常检查
				this.datePickerType = 'month'
				this.datePickerFormat = 'yyyy-MM'
				this.datePickerPlaceholder = '月份'
				// 设置为当月
				this.queryForm.expiry = moment().format('YYYY-MM')
			}
		},
		handleExport() { },
		getParams() {
			return {
				pageNum: this.pageNum,
				pageSize: this.pageSize,
				type: this.queryParams.inspectionType,
				checkTimeHas: (this.queryParams.expiry.length <= 7
					? moment(this.queryParams.expiry).endOf('month').format('YYYY-MM-DD HH:mm:ss')
					: moment(this.queryParams.expiry).endOf('day').format('YYYY-MM-DD HH:mm:ss')),
				assetCode: this.queryParams.assetCode,
				assetName: this.queryParams.assetName,
				isCheck: this.queryParams.isInspected,
				maintenanceSectionId: this.queryParams.maintenanceSectionId,
				routeCodes: this.queryParams.routeCodes,
				managementMaintenanceIds: this.queryParams.managementMaintenanceBranchIds,
				status: this.queryParams.status,
			}
		},
		async getList() {
			this.loading = true
			let res = await assetCheckListByJm(this.getParams())
			if (res.code === 200) {
				this.total = res.total
				this.staticList = res.rows
			}
			this.loading = false
		},
		async getListByParams(params) {
			this.resetQueryForm()
			this.queryForm.isInspected = params.isInspected
			this.queryForm.maintenanceSectionId = params.maintenanceSectionId
			this.queryForm.expiry = params.expiry
			this.queryhandle()
		},
	},
}
</script>

<style lang="scss" scoped>
.app-container form:first-child .el-select,
.app-container form:nth-child(2) .el-select,
.app-container form:nth-child(2) ::v-deep .el-form-item__content,
.app-container form:first-child ::v-deep .el-form-item__content {
	width: 240px;
}

.app-container form:first-child .el-form-item:last-child ::v-deep .el-form-item__content {
	width: auto;
}

.app-container {
	display: flex;
	flex-direction: column;
	padding: 10px;
	background-color: #c0c0c0;
	box-sizing: border-box;
}

.formDialog {
	::v-deep .el-dialog__body {
		height: 600px;
		overflow-y: auto;
	}

	.dialog-footer {
		width: 100%;

		.footerTip {
			color: #888888;
			font-size: 14px;
		}
	}

	.titleBox {
		height: 22px;
		position: relative;
		display: flex;
		flex-direction: row;
		align-items: center;

		.title {
			font-size: 16px;
			color: black;
			margin: 0;
		}

		.subTitle {
			margin-left: 15px;
			font-size: 12px;
			color: #888888;
		}

		.riskLevel {
			user-select: none;
			position: absolute;
			// top: 0;
			right: 5%;
			display: flex;
			align-items: center;
			flex-direction: row;

			.title {
				font-size: 16px;
				font-weight: bold;
			}

			.main {
				font-size: 16px;
				font-weight: bold;
				padding: 5px 10px 5px 10px;
				color: white;
				box-sizing: border-box;
				border-radius: 5px;
			}

			.score {
				color: #888888;
				font-size: 14px;
			}
		}
	}
}

.searchBox {
	padding: 10px;
	background: #fff;
	border-radius: 10px;
	transition: all 0.1s linear;
	display: grid;
	gap: 20px;
	grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));

	.searchMoreBox {
		min-width: 192px;
		margin-top: 10px;
		display: flex;
		align-items: center;
		flex-direction: row;
	}
}

.tableDiv {
	flex: 1;
	margin-top: 10px;
	background-color: white;
	padding-bottom: 10px;
	border-radius: 10px;
	transition: all 0.1s linear;
	display: flex;
	flex-direction: column;

	.btnBox {
		padding: 10px;
	}
}

.infoBox {
	padding: 15px 15px 0 15px;
	box-sizing: border-box;
	border-radius: 6px;
	border: 1px solid #c4c4c4;
	position: relative;

	.infoTitle {
		user-select: none;
		position: absolute;
		top: 0;
		left: 0;
		padding: 0 10px;
		font-size: 14px;
		line-height: 14px;
		font-weight: bold;
		transform: translateX(15px) translateY(-50%);
		background-color: white;
	}

	.imgBox {
		height: auto;
		width: 100%;
		display: flex;
		flex-wrap: wrap;
		align-content: flex-start;

		.imgItemBox {
			height: 240px;
			width: calc(100% / 3);
			box-sizing: border-box;
			padding: 10px;
			overflow-y: auto;
			display: flex;
			align-items: center;
			justify-content: center;
			position: relative;

			.imgDeleteBtn {
				position: absolute;
				z-index: 1;
				top: 10%;
				right: 10%;
			}
		}
	}
}

.coordinateDialog {
	.coordinateMap {
		height: 600px;
		width: 100%;
		position: relative;

		#coordinateBox {
			height: 100%;
			width: 100%;
			border-radius: 5px;
			position: relative;
			z-index: 0;
		}

		.coordinateSearch {
			position: absolute;
			z-index: 1;
			top: 10px;
			left: 10px;
			width: 50%;
			padding: 10px;
			box-sizing: border-box;
			background-color: #fff;
			border-radius: 5px;
			border: 1px solid #dcdfe6;
			box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.12), 0 0 6px 0 rgba(0, 0, 0, 0.04);
			display: flex;
			flex-direction: row;
		}

		.coordinateTip {
			position: absolute;
			z-index: 1;
			top: 10px;
			right: 10px;
			padding: 10px;
			box-sizing: border-box;
			background-color: #fff;
			border-radius: 5px;
			border: 1px solid #dcdfe6;
			box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.12), 0 0 6px 0 rgba(0, 0, 0, 0.04);
		}
	}
}

// v-if过渡动画
// 查询框
.search-enter-active {
	transition: all 0.1s linear;
}

.search-enter {
	opacity: 0;
}

.search-leave-active {
	transition: all 0.1s linear;
}

.search-leave-to {
	opacity: 0;
}

::v-deep .treeselect-main {
	line-height: 28px;
	font-size: 12px;
}

::v-deep .vue-treeselect__placeholder {
	line-height: 28px;
	color: #1d2129;
}

::v-deep .vue-treeselect__input {
	line-height: 28px;
}

::v-deep .vue-treeselect__control {
	height: 28px;
	font-size: 12px;
	font-weight: 400;
}

::v-deep .vue-treeselect__single-value {
	line-height: 28px;
}

::v-deep .vue-treeselect__menu-container {
	font-family: Arial;
	color: #1d2129;
}
</style>
