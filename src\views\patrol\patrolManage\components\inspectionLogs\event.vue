<template>

  <el-drawer v-bind="$attrs" v-on="$listeners">
<!--    <EventData  :dataId="dataId" ></EventData>-->
    <eventReview :dataId="dataId"></eventReview>

  </el-drawer>
</template>

<script>
import {getToken} from "@/utils/auth";
import {getEventListByRecordId, unbindRecord} from "@/api/dailyMaintenance/eventManage/eventReview";

import EventData from "@/views/patrol/inspectionLogs/eventData/index.vue";
import EventReview from "@/views/patrol/inspectionLogs/eventReview/index.vue";

export default {
  name: "AdviceDetail",
  components: {
    EventData,
    EventReview
  },
  dicts: ['sys_asset_type'],
  props: {
    data: {
      type: Object,
      default: {}
    }
  },
  data() {
    return {
      dataId: null,
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: false,
      dictType: [],
      // 总条数
      total: 0,
      // 事件列表
      eventList: null,
      // 弹出层标题
      title: "",
      // 部门树选项
      deptOptions: undefined,
      // 是否显示弹出层
      open: false,

      // 表单参数
      form: {},
      defaultProps: {
        children: "children",
        label: "label"
      },
      // 用户导入参数
      upload: {
        // 是否显示弹出层（用户导入）
        open: false,
        // 弹出层标题（用户导入）
        title: "",
        // 是否禁用上传
        isUploading: false,
        // 是否更新已经存在的用户数据
        updateSupport: 0,
        // 设置上传的请求头部
        headers: {Authorization: "Bearer " + getToken()},
        // 上传的地址
        url: process.env.VUE_APP_BASE_API + "/system/user/importData"
      },
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 50,
        diseaseId: null,
        code: null,
        name: null,
        sort: null
      },
      // 列信息
      columns: [
        {key: 0, label: `编码`, visible: true},
        {key: 1, label: `名称`, visible: true},
        {key: 2, label: `排序`, visible: true}
      ],
      // 表单校验
      rules: {
        code: [
          {required: true, message: "编码不能为空", trigger: "blur"}
        ],
        name: [
          {required: true, message: "名称不能为空", trigger: "blur"}
        ],


      }
    };
  },
  computed: {
    isSelect(){
      return this.ids.length >=1
    }
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询列表 */
    getList() {
      this.loading = true;
      getEventListByRecordId(this.data.id).then(response => {
        console.log("response: ",response);
        this.eventList = response.data;
        // this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        diseaseId: this.data.id,
        code: null,
        name: null,
        sort: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dateRange = [];
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id);
      this.single = selection.length != 1;
      this.multiple = !selection.length;
    },

    /** 解绑操作 */
    unbind(row) {
      const id = row.id || this.ids;
      this.$confirm('是否确认解绑该事件？', "系统提示", {confirmButtonText: '确定', cancelButtonText: '取消',
        type: "warning",
        customClass:'margin-left-50'
      }).then(function () {
        return unbindRecord(JSON.stringify(id));
      }).then(() => {
        this.getList();
        this.$message({message: '"删除成功"', customClass: 'margin-left-25'})
      }).catch(() => {
      })
    },
  },
  watch: {
    data() {


      this.queryParams.diseaseId = this.data.id
      this.form.diseaseId = this.data.id
      // this.getList()
      this.dataId = this.data.id
    }
  },
};
</script>
<style scoped>
.container {
  margin-top: -20px;
  padding: 20px;
}
/*::v-deep .el-dialog{
  position: absolute;
  margin-left: 20%;
}*/

</style>

<style >
.margin-left-50{
  margin-left: 50%;
}
.margin-left-25{
  margin-left: 25%;
}
.el-icon-unbind{

}

</style>
