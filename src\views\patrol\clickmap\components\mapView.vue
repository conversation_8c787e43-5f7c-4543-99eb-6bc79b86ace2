<template>
  <div class="map-view" ref="mapRef">
    <div id="popup" class="ol-popup custom-popup" v-show="isShowPopup">
      <div class="popup-title">{{ popupData.名称 || '-' }}</div>
      <div class="popup-content">
        <div class="popup-item">
          <i class="icon-distance"></i>
          <span>所属高速</span>
          <span class="value">{{ popupData.所属高速 || '-' }}</span>
        </div>
        <div class="popup-item">
          <i class="icon-user"></i>
          <span>负责人</span>
          <span class="value">{{ popupData.负责人 || '-' }}</span>
        </div>
        <div class="popup-item">
          <i class="icon-area"></i>
          <span>位置</span>
          <span class="value">{{ popupData.位置 || '-' }}</span>
        </div>
        <div class="popup-item">
          <i class="icon-equipment"></i>
          <span>结构</span>
          <span class="value">{{ popupData.结构 || '-' }}</span>
        </div>
        <div class="popup-item">
          <i class="icon-storage"></i>
          <span>建成时间</span>
          <span class="value">{{ formatDate(popupData.建成时间) }}</span>
        </div>
        <div class="popup-item">
          <i class="icon-distance"></i>
          <span>覆盖路段</span>
          <span class="value">{{ popupData.覆盖路段 || '-' }}</span>
        </div>
      </div>
    </div>
    <!-- 添加对话框 -->
    <el-dialog
      title="路线详情"
      :visible.sync="dialogVisible"
      width="60%"
      :destroy-on-close="true"
    >
      <road-detail v-if="dialogVisible" :line-id="selectedLineId" />
    </el-dialog>
  </div>
</template>

<script>
import { Feature, Map, View } from 'ol';
import { OSM, Vector as VectorSource, XYZ } from 'ol/source';
import VectorLayer from 'ol/layer/Vector';
import TileLayer from 'ol/layer/Tile';
import { Fill, Stroke, Text, Style, Circle } from 'ol/style';
import {
  GeometryCollection,
  LineString,
  MultiPolygon,
  Point,
  Polygon,
} from 'ol/geom';
import Mask from 'ol-ext/filter/Mask';
import Crop from 'ol-ext/filter/Crop.js';
import axios from 'axios';
import { GeoJSON, WKT } from 'ol/format';
import { defaults } from 'ol/control';
import { fromLonLat } from 'ol/proj';
import * as turf from '@turf/turf';
import Overlay from 'ol/Overlay';
import { render } from 'ol';
import { getVectorContext } from 'ol/render';

import { getShapeList } from '@/api/oneMap/deptInfo';
import { getMenuSub } from '@/api/oneMap/menuSub';
import cache from '@/plugins/cache';
import { addDataToMap, addVectorTile, removeLayer } from '../util/mapUtils';
import RoadDetail from './RoadDetail.vue';
import yjwzData from '../data/yjwz.json';

var key = 'cde0b56cf882626889981701109a7536';
const lineId = '1824259894327382017';

export default {
  props: {
    padding: {
      type: Array,
      default: () => [50, 0, 50, 50],
    },
  },
  components: {
    RoadDetail,
  },
  data() {
    return {
      vectorSource: null,
      vectorLayer: [],
      feature: null,
      map: null,
      dataList: [], // 部门数据
      isShowPopup: false,
      popupContent: '',
      overlay: null,
      animationLayer: null,
      scale: 0.02,
      animationFeatures: [],
      dialogVisible: false,
      selectedLineId: null,
      highlightedFeature: null, // 添加新的数据属性用于跟踪高亮的feature
      animating: false, // 添加动画状态控制
      animationLoop: null, // 添加动画循环控制变量
      popupData: {}, // 添加popupData用于存储当前点击的数据
    };
  },
  mounted() {
    this.__init();
  },
  unmounted() {
    this.map = null;
    removeLayer(this.map, 'dataLayer');
  },
  destroyed() {
    this.map = null;
  },
  methods: {
    async __init() {
      this.vectorSource = await this.getAreaList();
      this.vectorLayer = await this.addRegionLayer(this.vectorSource);
      this.initMap();
    },
    initMap() {
      this.map = new Map({
        target: this.$refs.mapRef,
        view: new View({
          projection: 'EPSG:3857',
          //地图初始中心点
          center: fromLonLat([102.75530900000001, 24.95423900000002]),
          //地图初始显示级别
          minZoom: 6,
          zoom: 7,
          maxZoom: 20,
          padding: this.padding,
          rotation: 0,
          enableRotation: true,
        }),
        layers: this.vectorLayer,
        controls: defaults({
          attribution: false,
          zoom: false,
          rotate: false,
          drag: false,
        }),
      });
      // this.addRegionLayer(this.vectorSource);
      this.addShadowByExt(this.vectorSource);
      this.addLine();
      this.setDataToMap();

      // 初始化动画图层
      this.initAnimationLayer();
    },
    // 获取 行政区域
    async getAreaList(deptId = null) {
      return new Promise((resolve, reject) => {
        let localData = cache.session.getJSON('rangeData');
        if (!localData) {
          this.$modal.loading();
          getShapeList({ sysDeptIds: [deptId] })
            .then((res) => {
              if (res.code == 200 && res.data) {
                cache.session.setJSON('rangeData', res.data);
                this.dataList = res.data;
                let source = this.getSource(res.data);
                resolve(source);
              } else {
                resolve(null);
              }
            })
            .catch((err) => {
              reject(err);
            })
            .finally(() => {
              this.$modal.closeLoading();
            });
        } else {
          let source = this.getSource(localData);
          this.dataList = localData;
          resolve(source);
        }
      });
    },
    // 获取source 数据
    getSource(data = []) {
      let arr = [];
      for (let index = 0; index < data.length; index++) {
        const mgeom = data[index].mgeom;
        // // 解析WKT字符串
        const geometry = new WKT().readGeometry(mgeom);
        // 获取所有点的数组
        const coordinates = geometry.getCoordinates();
        arr[index] = turf.polygon(coordinates[0], { combine: 'yes' });
        // arr[index] = turf.multiPolygon(coordinates, { combine: 'yes' });
      }
      var union = turf.union(turf.featureCollection(arr));
      let mul = new MultiPolygon([union.geometry.coordinates]).transform(
        'EPSG:4326',
        'EPSG:3857'
      );
      let mulFeature = new Feature(mul);
      this.feature = mulFeature;
      let vectorSource = new VectorSource();
      vectorSource.addFeature(mulFeature);
      return vectorSource;
    },
    // 添加数据到地图
    setDataToMap() {
      removeLayer(this.map, 'dataLayer');
      console.log('yjwzData:', yjwzData); // 添加日志检查数据

      // 确保 yjwzData 是数组
      const dataArray = Array.isArray(yjwzData)
        ? yjwzData
        : Object.values(yjwzData);

      let vectorsLayer = addDataToMap(dataArray, 'dataLayer', null, true);
      if (vectorsLayer) {
        this.map.addLayer(vectorsLayer);
      }

      // 初始化弹窗
      this.initPopup();

      // 添加点击事件监听
      this.addClickHandler();

      // 移动到行政区域
      this.map.getView().fit(this.feature.getGeometry().getExtent(), {
        duration: 500,
      });
    },

    // 初始化弹窗
    initPopup() {
      const container = document.getElementById('popup');
      this.overlay = new Overlay({
        element: container,
        autoPan: false,
        positioning: 'bottom-center',
        offset: [0, -10],
        // autoPanAnimation: {
        //   duration: 250,
        // },
      });
      this.map.addOverlay(this.overlay);
    },

    // 添加击事件处理
    addClickHandler() {
      this.map.on('singleclick', (evt) => {
        const feature = this.map.forEachFeatureAtPixel(
          evt.pixel,
          (feature) => feature
        );

        if (this.animationLoop) {
          clearInterval(this.animationLoop);
          this.animationLoop = null;
        }

        if (!feature || !feature.getProperties().data) {
          this.isShowPopup = false;
          this.overlay.setPosition(undefined);
          this.animating = false;
          this.popupData = {}; // 清空数据
          return;
        }

        // 获取点击位置的数据
        const properties = feature.getProperties().data;
        this.popupData = properties; // 保存数据用于显示

        // 添加点击动画
        const clickFeature = new Feature(new Point(evt.coordinate));
        this.animationFeatures = [clickFeature];

        // 开始循环动画
        this.startAnimationLoop();

        // 显示弹窗
        this.isShowPopup = true;
        this.overlay.setPosition(evt.coordinate);
      });
    },

    startAnimationLoop() {
      this.scale = 0.0;
      this.animating = true;
      this.map.render();

      // 设置动画循环
      this.animationLoop = setInterval(() => {
        if (!this.isShowPopup) {
          // 如果弹窗关闭,停止动画
          clearInterval(this.animationLoop);
          this.animationLoop = null;
          this.animating = false;
          return;
        }
        // 重置scale开始新的动画循环
        this.scale = 0.0;
        this.map.render();
      }, 5000); // 每秒重新开始一次动画
    },

    // 添加行政区域
    async addRegionLayer(data) {
      // return new Promise((resolve) => {
      let _this = this;
      let source = data;
      let layer = new VectorLayer({
        source: source,
        style: new Style({
          renderer(coordinate, state) {
            let arr = coordinate[0][0];
            const ctx = state.context;
            // _this.addOutlineShadow(ctx, {
            //   fillStyle: "rgba(30, 60, 95,1)",
            //   shadowOffsetY: 30,
            //   shadowOffsetX: 2,
            //   shadowColor: "rgba(30, 60, 95,1)",
            //   strokeStyle: "rgba(30, 60, 95,1)",
            //   coodArr: arr,
            // });
            // _this.addOutlineShadow(ctx, {
            //   fillStyle: "transparent",
            //   shadowOffsetY: 20,
            //   shadowOffsetX: 2,
            //   shadowColor: "rgba( 56, 113, 139,1)",
            //   strokeStyle: "rgba(30, 60, 95,1)",
            //   coodArr: arr,
            // });
            // _this.addOutlineShadow(ctx, {
            //   fillStyle: "transparent",
            //   shadowOffsetY: 15,
            //   shadowOffsetX: 2,
            //   shadowColor: "rgba(255,255,255,1)",
            //   strokeStyle: "rgba(255,255,255,1)",
            //   shadowBlur: 10,
            //   coodArr: arr,
            // });
            _this.addOutlineShadow(ctx, {
              fillStyle: 'rgba(0,36,73,0.8)',
              shadowOffsetY: 10,
              shadowOffsetX: 2,
              shadowColor: 'rgba(255,255,255,1)',
              strokeStyle: 'rgba(255,255,255,1)',
              coodArr: arr,
            });
            _this.addOutlineShadow(ctx, {
              fillStyle: 'transparent',
              shadowOffsetY: 8,
              shadowOffsetX: 2,
              shadowColor: 'rgba(255,255,255,1)',
              strokeStyle: 'rgba(255,255,255,1)',
              shadowBlur: 10,
              coodArr: arr,
            });
            _this.addOutlineShadow(ctx, {
              fillStyle: '#fff',
              shadowOffsetY: 5,
              shadowOffsetX: 2,
              shadowColor: 'rgba(70, 133, 171,1)',
              strokeStyle: 'rgba(255,255,255,1)',
              shadowBlur: 10,
              coodArr: arr,
            });
            //白色
            _this.addOutlineShadow(ctx, {
              fillStyle: 'rgba(255,255,255,0)',
              shadowOffsetY: 5,
              shadowOffsetX: 10,
              shadowColor: 'rgba(255,255,255,0)',
              strokeStyle: 'rgba(255,255,255,1)',
              shadowBlur: 15,
              coodArr: arr,
              lineWidth: 2,
            });
          },
        }),
      });
      // this.map.addLayer(layer);
      return [layer];

      //   resolve([layer]);
      // });
    },
    //添加Mask
    addMask(options) {
      return new Mask({
        feature: options.feature,
        wrapX: false,
        inner: options.inner || false,
        fill: new Fill({ color: options.fillColor }),
        shadowColor: options.shadowColor || 'rgba(0,0,0,0.5)',
        shadowWidth: options.shadowWidth || 10,
        shadowMapUnits: true,
      });
    },
    setLayerFilterCrop(layer, feature) {
      /**
       * 设置图层裁切
       */
      const crop = new Crop({
        feature: feature,
        inner: false,
        active: true,
        wrapX: true,
        shadowWidth: 2,
        shadowColor: 'rgba(255,255,255,0.7)',
      });
      layer.addFilter(crop);
    },
    //添加外发光
    addShadowByExt(source) {
      const features = source.getFeatures();
      let layer = new VectorLayer({
        source: source,
      });
      this.map.addLayer(layer);
      //内发光
      let mask = this.addMask({
        fillColor: 'rgba(1,49,93,1)',
        shadowColor: '#fff',
        feature: features[0],
        inner: true,
      });
      //设置裁切
      this.setLayerFilterCrop(layer, features[0]);
      layer.addFilter(mask);
    },
    //添加外发光
    addOutlineShadow(ctx, option) {
      // 设置属性控制图形的外观
      ctx.fillStyle = option.fillStyle || 'transparent';
      ctx.strokeStyle = option.strokeStyle || 'transparent';
      ctx.lineWidth = option.lineWidth || 1;
      //  设置Y轴偏移量
      ctx.shadowOffsetY = option.shadowOffsetY || 20;
      //  设置X轴偏移量
      ctx.shadowOffsetX = option.shadowOffsetX || 2;
      //  设置模糊度
      ctx.shadowBlur = option.shadowBlur || 2;
      //  设置阴影颜色
      ctx.shadowColor = option.shadowColor || '#000';
      ctx.beginPath();
      let arr = option.coodArr || [];
      for (let i = 0; i < arr.length; i++) {
        const data = arr[i];
        if (i === 0) {
          ctx.moveTo(data[0], data[1]);
        } else {
          ctx.lineTo(data[0], data[1]);
        }
      }
      ctx.closePath();
      ctx.fill();
      ctx.stroke();
    },

    // 添加路线
    async addLine() {
      let lineObj = cache.session.getJSON('lineObj');
      let layerName = '路网信息';
      if (!lineObj) {
        this.$modal.loading();
        await getMenuSub(lineId)
          .then((res) => {
            lineObj = res.data;
            lineObj.borderColour = 'rgba(0,191,45,0.8)';
            let layer = addVectorTile(
              this.map,
              'name',
              layerName,
              JSON.stringify({ id: lineId }),
              lineObj,
              true,
              10,
              6,
              256,
              false
            );
            this.map.addLayer(layer);

            // 添加点击事件监听
            // this.addLineClickHandler(layer);
          })
          .finally(() => {
            this.$modal.closeLoading();
          });
      } else {
        lineObj.borderColour = 'rgba(0,191,45,0.8)';
        let layer = addVectorTile(
          this.map,
          'name',
          layerName,
          JSON.stringify({ id: lineId }),
          lineObj,
          true,
          10,
          6,
          256,
          false
        );
        this.map.addLayer(layer);

        // 添加点击事件监听
        // this.addLineClickHandler(layer);
      }
    },

    // 添加路线点击处理方法
    addLineClickHandler(layer) {
      this.map.on('click', (evt) => {
        const feature = this.map.forEachFeatureAtPixel(
          evt.pixel,
          (feature, layer) => {
            if (layer && layer.get('name') === '路网信息') {
              return feature;
            }
          }
        );

        // 重置之前高亮的feature样式
        if (this.highlightedFeature) {
          //   this.highlightedFeature.setStyle(null);
        }

        if (feature) {
          const properties = feature.getProperties();
          console.log(properties);
          if (properties && properties.typename) {
            // 设置新的亮样式
            // feature.setStyle(
            //   new Style({
            //     stroke: new Stroke({
            //       color: '#00ffff',
            //       width: 4,
            //     }),
            //   })
            // );

            this.highlightedFeature = feature; // 保存当前高亮的feature
            this.selectedLineId = properties.typename;
            this.dialogVisible = true;
          }
        }
      });
    },

    setVector(name = '', obj = {}) {
      setTimeout(() => {
        let layer = addVectorTile(
          this.map,
          'name',
          name,
          JSON.stringify(obj),
          obj,
          true,
          10,
          6,
          256,
          false
        );
        this.map.addLayer(layer);
      }, 600);
    },
    removeVector(name = 'dataLayer') {
      removeLayer(this.map, name);
    },
    initAnimationLayer() {
      const source = new VectorSource();
      this.animationLayer = new VectorLayer({
        source: source,
        style: new Style({
          image: new Circle({
            fill: new Fill({
              color: 'rgba(221,0,27,0.6)',
            }),
            radius: 8,
          }),
        }),
        zIndex: 9999,
      });

      this.map.addLayer(this.animationLayer);
      this.animationLayer.on('postrender', this.animation.bind(this));
    },

    animation(event) {
      if (!this.map || !this.animating) return;

      if (this.scale >= 1) {
        this.scale = 0.02;
      }

      const vectorContext = getVectorContext(event);

      // 绘制扩散圆环
      const circleStyle = new Style({
        image: new Circle({
          radius: 100,
          scale: this.scale,
          fill: new Fill({
            color: `rgba(0,247,255,${0.6 - this.scale * 0.6})`,
          }),
          stroke: new Stroke({
            color: `rgba(0,247,255,${0.8 - this.scale * 0.8})`,
            width: 3,
          }),
        }),
      });

      vectorContext.setStyle(circleStyle);
      this.animationFeatures.forEach((feature) => {
        vectorContext.drawGeometry(feature.getGeometry());
      });

      // 添加扫描线
      const center = this.animationFeatures[0].getGeometry().getCoordinates();
      const rotation = (this.scale * Math.PI * 2) % (Math.PI * 2); // 旋转角度
      const baseRadius = 100; // 基础半径
      const currentRadius = baseRadius * this.scale; // 当前半径

      // 创建扫描线样式 - 使用扇形效果
      const scanAngle = Math.PI / 6; // 30度的扇形
      const points = [center];

      // 添加扇形的边缘点
      for (let i = -scanAngle / 2; i <= scanAngle / 2; i += scanAngle / 10) {
        const angle = rotation + i;
        points.push([
          center[0] + Math.cos(angle) * currentRadius,
          center[1] + Math.sin(angle) * currentRadius,
        ]);
      }
      points.push(center); // 闭合扇形

      const scanStyle = new Style({
        geometry: new LineString(points),
        stroke: new Stroke({
          color: 'rgba(255, 87, 51, 0.8)',
          width: 2,
        }),
        fill: new Fill({
          color: 'rgba(255, 87, 51, 0.3)',
        }),
      });

      // 绘制扫描线
      vectorContext.setStyle(scanStyle);
      vectorContext.drawGeometry(scanStyle.getGeometry());

      this.scale = this.scale + 0.02; // 降低动画速度

      if (this.map && this.animating) {
        window.requestAnimationFrame(this.map.render.bind(this.map));
      }
    },

    // 在组件销毁前清理
    beforeDestroy() {
      if (this.animationLoop) {
        clearInterval(this.animationLoop);
        this.animationLoop = null;
      }
      this.animating = false;
      if (this.animationLayer) {
        this.map.removeLayer(this.animationLayer);
      }
      this.animationFeatures = [];
      this.map = null;
    },
    // 添加时间戳转换方法
    formatDate(timestamp) {
      if (!timestamp) return '-';
      const date = new Date(timestamp);
      return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(
        2,
        '0'
      )}-${String(date.getDate()).padStart(2, '0')}`;
    },
  },
};
</script>

<style lang="scss" scoped>
.map-view {
  width: 100%;
  height: 100%;
}

// .ol-popup {
//   position: absolute;
//   background-color: white;
//   box-shadow: 0 1px 4px rgba(0, 0, 0, 0.2);
//   padding: 15px;
//   border-radius: 10px;
//   border: 1px solid #cccccc;
//   min-width: 280px;

//   .popup-content {
//     white-space: pre-wrap;
//     font-size: 14px;
//   }
// }

.custom-popup {
  background: rgba(4, 49, 128, 0.8);
  border-radius: 6px;
  color: #fff;
  padding: 0;
  min-width: 300px;
  border: 1px solid #0ff;
  box-shadow: 0 0 10px rgba(0, 255, 255, 0.3);

  .popup-title {
    font-size: 18px;
    padding: 10px 15px;
    border-bottom: 1px solid rgba(0, 255, 255, 0.2);
    color: #0ff;
  }

  .popup-content {
    padding: 15px;
  }

  .popup-item {
    display: flex;
    align-items: center;
    margin-bottom: 12px;

    i {
      margin-right: 10px;
      font-size: 16px;
      color: #0ff;
    }

    span {
      color: rgba(255, 255, 255, 0.8);
    }

    .value {
      margin-left: auto;
      color: #0ff;
      font-size: 16px;
    }

    &:last-child {
      margin-bottom: 0;
    }
  }
}

// 添加一个小三角形指向标记点
.custom-popup:after {
  content: '';
  position: absolute;
  bottom: -10px;
  left: 50%;
  margin-left: -10px;
  border-width: 10px 10px 0;
  border-style: solid;
  border-color: rgba(4, 49, 128, 0.8) transparent transparent;
}
</style>
