<template>
  <PageContainer>
    <template slot="search">
      <div style="display: flex; align-items: center; margin: 0">
        <CascadeSelection
          style="min-width: 192px"
          :form-data="queryParams"
          v-model="queryParams"
          types="201"
          multiple
        />
        <div style="margin-left: 20px">
          <el-input
            v-model="queryParams.bridgeName"
            style="width:100%;"
            placeholder="桥梁名称"
            clearable
          />
        </div>
        <div style="margin-left: 20px">
          <el-input
            v-model="queryParams.bridgeCode"
            style="width:100%"
            placeholder="桥梁编码"
            clearable
          />
        </div>
        <div style="margin-left: 20px">
          <el-date-picker
            style="width: 100%"
            v-model="queryParams.checkYear"
            type="year"
            placeholder="年份"
            value-format="yyyy"
          />
        </div>
        <div style="margin: 0 20px">
          <el-select
            v-model="queryParams.level"
            placeholder="定期检查评定等级"
            clearable
          >
            <el-option
              v-for="dict in dict.type.bridge_tec_condition_level"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </div>

        <div style="min-width: 240px; height: 32px">
          <el-button
            v-hasPermi="['baseData:periodicDetection:listPage']"
            type="primary"
            icon="el-icon-search"
            class="mb8"
            @click="handleQuery"
            >搜索</el-button
          >
          <el-button icon="el-icon-refresh" class="mb8" @click="resetQuery"
            >重置</el-button
          >
        </div>
      </div>
    </template>
    <template slot="header">
      <el-row :gutter="10" class="button-list">
        <el-col :span="1.5">
          <el-button
            v-hasPermi="['baseData:periodicDetection:add']"
            type="primary"
            @click="handleAdd"
            >新增</el-button
          >
        </el-col>
        <el-col :span="1.5">
          <el-button
            v-hasPermi="['baseData:periodicDetection:edit']"
            type="primary"
            @click="handleUpdate"
            >编辑</el-button
          >
        </el-col>
        <el-col :span="1.5">
          <el-button
            v-hasPermi="['baseData:periodicDetection:remove']"
            type="primary"
            @click="handleDelete"
            >删除</el-button
          >
        </el-col>
        <el-col :span="1.5">
          <el-button
            v-hasPermi="['baseData:periodicDetection:export']"
            type="primary"
            class="mb8"
            @click="exportList"
            >数据导出</el-button
          >
        </el-col>
        <el-button
          v-hasPermi="['baseData:import:execute']"
          class="mb8"
          type="primary"
          @click="importUpdate"
          >导入更新</el-button
        >
        <el-button
          v-hasPermi="['baseData:import:execute']"
          class="mb8"
          type="primary"
          @click="importAdd"
          >导入新增</el-button
        >
        <el-button
          v-hasPermi="['baseData:periodicDetection:import']"
          class="mb8"
          type="primary"
          @click="importZip"
          >导入附件</el-button
        >
        <el-button
          v-hasPermi="['baseData:periodicDetection:recheck']"
          class="mb8"
          type="primary"
          @click="recheckData"
          >复核</el-button
        >
        <el-button
          v-hasPermi="['baseData:periodicDetection:recheck']"
          class="mb8"
          type="primary"
          @click="deleteRecheckData"
          >取消复核</el-button
        >
        <el-button
          v-hasPermi="['baseData:periodicDetection:export']"
          class="mb8"
          type="primary"
          @click="evaluateExport"
          :disabled="this.ids.length ? true : false"
          >导出评定结果</el-button
        >
        <el-button
          v-hasPermi="['baseData:import:execute']"
          class="mb8"
          type="primary"
          @click="exportAll"
          >导入评定结果</el-button
        >
      </el-row>
    </template>
    <template slot="body">
      <el-table
        v-adjust-table
        ref="table"
        height="100%"
        style="width: 100%"
        :header-cell-style="{
          background: '#F2F3F5',
          color: '#212529',
          'font-weight': '700',
          'font-size': '14px',
        }"
        :cell-style="{ height: '36px' }"
        :row-style="rowStyle"
        border
        v-loading="loading"
        :data="staticList"
        @selection-change="handleSelectionChange"
        @row-click="handleRowClick"
      >
        <el-table-column type="selection" width="50" align="center" />
        <el-table-column
          fixed
          label="序号"
          type="index"
          width="50"
          align="center"
        >
          <template v-slot="scope">
            {{
              scope.$index +
              (queryParams.pageNum - 1) * queryParams.pageSize +
              1
            }}
          </template>
        </el-table-column>
        <!-- <el-table-column
        label="桥梁固定编码"
        align="center"
        prop="fixedCode"
        min-width="140"
        show-overflow-tooltip
      /> -->
        <el-table-column
          fixed
          label="桥梁名称"
          align="center"
          prop="bridgeName"
          min-width="140"
          show-overflow-tooltip
        />
        <el-table-column
          label="桥梁编码"
          align="center"
          prop="bridgeCode"
          min-width="140"
          show-overflow-tooltip
        />
        <el-table-column
          label="年度"
          align="center"
          prop="checkYear"
          min-width="140"
          show-overflow-tooltip
        />

        <el-table-column
          label="管养处名称"
          align="center"
          prop="managementMaintenanceName"
          min-width="140"
          show-overflow-tooltip
        />
        <el-table-column
          label="管养分处名称"
          align="center"
          prop="managementMaintenanceBranchName"
          min-width="140"
          show-overflow-tooltip
        />
        <el-table-column
          label="养护路段名称"
          align="center"
          prop="maintenanceSectionName"
          min-width="140"
          show-overflow-tooltip
        />
        <el-table-column
          label="路线编码"
          align="center"
          prop="routeCode"
          min-width="140"
          show-overflow-tooltip
        />
        <el-table-column
          label="路线名称"
          align="center"
          prop="routeName"
          min-width="140"
          show-overflow-tooltip
        />

        <el-table-column
          label="结构形式"
          align="center"
          prop="mainSuperstructureType"
          min-width="140"
          show-overflow-tooltip
        >
          <template slot-scope="scope">
            <span v-if="scope.row && scope.row.mainSuperstructureType">
              <DictTag
                :value="scope.row.mainSuperstructureType"
                :options="dict.type.bridge_main_superstructure_type"
              />
            </span>
          </template>
        </el-table-column>

        <el-table-column
          label="检查时间"
          align="center"
          prop="checkDate"
          min-width="140"
          show-overflow-tooltip
        />

        <el-table-column
          label="定期检查评定等级"
          align="center"
          prop="level"
          min-width="140"
          show-overflow-tooltip
        >
          <template slot-scope="scope">
            <span v-if="scope.row && scope.row.level">
              <DictTag
                :value="scope.row.level"
                :options="dict.type.bridge_tec_condition_level"
              />
            </span>
          </template>
        </el-table-column>
        <el-table-column
          label="评定为四五类桥的原因"
          align="center"
          prop="fourthFifthClassRatingReason"
          min-width="170"
          show-overflow-tooltip
        >
          <template slot-scope="scope">
            <span v-if="scope.row && scope.row.fourthFifthClassRatingReason">
              <DictTag
                :value="scope.row.fourthFifthClassRatingReason"
                :options="dict.type.bridge_four_five_evaluate_reasons"
              />
            </span>
          </template>
        </el-table-column>

        <el-table-column
          label="定期检查承担单位"
          align="center"
          prop="organization"
          min-width="140"
          show-overflow-tooltip
        />
        <!-- <el-table-column
        label="定期检查报告"
        align="center"
        prop="reportPath"
        min-width="140"
        show-overflow-tooltip
      /> -->
        <el-table-column
          label="检查报告"
          align="center"
          prop="originalFilename"
          min-width="300"
          show-overflow-tooltip
        >
          <template slot-scope="scope">
            <el-button
              v-if="scope.row.reportName"
              type="text"
              icon="el-icon-paperclip"

              @click.stop="handlePreview(scope.row)"
            >{{ scope.row.reportName }}
            </el-button
            >
            <!-- <span v-else>{{ scope.row.reportName }}</span> -->
            <span v-else></span>
          </template>
        </el-table-column>

        <el-table-column
          label="报告编写人"
          prop="reportWriter"
          align="center"
          min-width="140"
          show-overflow-tooltip
        />
        <el-table-column
          label="报告编制日期"
          prop="reportWriteDate"
          align="center"
          min-width="140"
          show-overflow-tooltip
        />
        <el-table-column
          label="总体评分"
          prop="overallScore"
          align="center"
          min-width="140"
          show-overflow-tooltip
        />
        <el-table-column
          label="复核单位名称"
          align="center"
          prop="recheckOrganization"
          min-width="140"
          show-overflow-tooltip
        />
        <el-table-column
          label="复核人"
          align="center"
          prop="recheckPerson"
          min-width="140"
          show-overflow-tooltip
        />
        <el-table-column
          label="复核时间"
          align="center"
          prop="recheckTime"
          min-width="140"
          show-overflow-tooltip
        />

        <!-- <el-table-column
        label="跨径组合"
        align="center"
        prop="spanGroups"
        min-width="140"
        show-overflow-tooltip
      /> -->
      </el-table>
      <pagination
        :total="total"
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
        :pageSizes="[10, 20, 30, 50, 100, 1000]"
        @pagination="getList"
      />
    </template>
    <ImportData
      v-if="showImportAdd"
      :is-update="isUpdate"
      :dialog-visible="showImportAdd"
      :import-base-type="importBaseType"
      :import-type="importType"
      @close="closeImportAdd"
      :tips-text="tipsText"
    />
    <ImportZip
      v-if="showImportZip"
      :is-update="false"
      :dialog-visible="showImportZip"
      :import-base-type="importBaseType"
      :import-type="importType"
      @close="closeImportZip"
    />
    <add-and-edit
      v-if="showAddEdit"
      :forView="forView"
      :formData="formData"
      :title="title"
      :showAddEdit="showAddEdit"
      @close="
        () => {
          showAddEdit = false;
          formData = {};
        }
      "
      @refresh="
        () => {
          showAddEdit = false;
          getList();
        }
      "
    />
      <FilePreview :owner-id="ownerId" :office-preview-visible="officePreviewVisible" @close="officePreviewVisible=false"/>
  </PageContainer>
</template>

<script>
import {
  listBridgePeriodicRecords,
  detectionRecheck,
  detectionRollback,
  delPeriodicDetectionBridge,
} from "@/api/baseData/bridge/periodic/index";
import AddAndEdit from "./components/addAndEdit.vue";
import { getToken } from "@/utils/auth";
import CascadeSelection from "@/components/CascadeSelection/index.vue";
import { getFile } from "@/api/file";

import ImportData from "@/views/baseData/components/importData/index.vue";
import ImportZip from "./components/importZip.vue";
import FilePreview from "@/components/FilePreview/index.vue";

export default {
  name: "Periodic",
  components: {FilePreview, ImportData, CascadeSelection, AddAndEdit, ImportZip},
  dicts: [
    "base_data_yes_no",
    "bridge_tec_condition_level",
    "bridge_main_superstructure_type",
    "bridge_function_type",
    "bridge_four_five_evaluate_reasons",
  ],
  data() {
    return {
      loading: true,
      showAddEdit: false,
      forView: false,
      title: "",
      formData: {},
      showUpload: false,
      importBaseType: "7",
      isUpdate: false,
      importType: 0,
      ids: [],
      single: true,
      multiple: true,
      showSearch: false,
      showImportAdd: false,
      total: 0,
      staticList: null,
      showImportZip: false,
      routeCode: [],
      queryParams: {
        pageNum: 1,
        pageSize: 20,
        checkYear:new Date().getFullYear()+''
      },
      dateRange: [],
      tipsText: null,
      routeCodeOptions: [],

      classification: [],
      operation: [],
      sysyes: [],
      assessment: [],
      lining: [],
      selectItem: [],
      officePreviewVisible:false,
      ownerId:''

    };
  },
  watch: {},
  created() {
    this.getList();
    this.getOptions();
  },
  methods: {
    async handlePreview(row) {
      this.officePreviewVisible=true;
      this.ownerId=row.reportPath;
    },
    // 获取搜索栏相关字典数据
    getOptions() {},
    recheckData() {
      //出现弹出框并且要输入复核单位名称
      if (this.ids.length == 0) {
        this.$message.error("请选择需要复核的数据");
        return;
      }
      this.$prompt("请输入复核单位名称", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        inputPattern: /\S/,
        inputErrorMessage: "复核单位名称不能为空",
      }).then(({ value }) => {
        detectionRecheck({
          periodicDetectionIds: this.ids,
          recheckOrganization: value,
        })
          .then(() => {
            this.$message({
              type: "success",
              message: "复核成功",
            });
            this.getList();
          })
          .catch(() => {});
      });
    },
    evaluateExport() {
      this.download(
        "/baseData/bridge/periodic/detection/exportAll",
        { ids: this.ids, ...this.queryParams },
        ``,
        {
          headers: { "Content-Type": "application/json;" },
          parameterType: "body",
        }
      );
    },
    deleteRecheckData() {
      if (this.ids.length == 0) {
        this.$message.error("请选择需要取消复核的数据");
        return;
      }
      this.$confirm("是否取消复核？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        detectionRollback({ periodicDetectionIds: this.ids })
          .then(() => {
            this.$message({
              type: "success",
              message: "取消复核成功",
            });
            this.getList();
          })
          .catch(() => {
            // this.$message({
            //   type: 'info',
            //   message: '取消复核'
            // });
          });
      });
    },
    // 获取表格数据
    getList() {
      this.loading = true;

      listBridgePeriodicRecords(this.queryParams).then(async (response) => {




        this.staticList = response.rows;
        this.total = response.total;
        this.loading = false;

        let dataList = JSON.parse(JSON.stringify(response.rows));
        for (let index = 0; index < dataList.length; index++) {
          const el = dataList[index];

          if(el.checkDate){

            const year = el.checkDate.substring(0, 4);
            const month = el.checkDate.substring(4, 6);
            const day = el.checkDate.substring(6, 8);
            el.checkDate=year+'-'+month+'-'+day
          }

          // if (el.reportPath == parseInt(el.reportPath)) {
          //   const r = await getFile({ownerId: el.reportPath});
          //   if (r.code === 200) {
          //     el.originalFilename = r.data?.originalFilename;
          //     el.fileUrl = r.data?.url;
          //   }
          // }
        }

        this.staticList = dataList;
      });
    },
    handleDelete() {
      if (this.ids.length == 0) {
        this.$message.warning("请选择需要删除的数据");
        return;
      }
      this.$confirm("是否删除选中数据？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        delPeriodicDetectionBridge(this.ids).then(() => {
          this.$message({
            type: "success",
            message: "删除成功",
          });
          this.getList();
        });
      });
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id);
      this.selectItem = selection;
      this.single = selection.length != 1;
      this.multiple = !selection.length;
    },
    // 勾选高亮
    rowStyle({ row, rowIndex }) {
      if (this.ids.includes(row.id)) {
        return { "background-color": "#b7daff", color: "#333" };
      } else {
        return { "background-color": "#fff", color: "#333" };
      }
    },

    exportAll() {
      this.isUpdate = false;
      this.showImportAdd = true;
      this.importType = 2;
      this.importBaseType = "33";
      this.tipsText='更新数据：请先使用数据导出功能导出需要更新的数据进行修改，然后再将修改后的文件上传回系统。(注意不要直接下载新增模板进行修改)';
    },

    handleUpdate() {
      if (this.ids.length != 1) {
        this.$message.warning("请选择一条数据进行编辑！");
        return;
      } else {
        this.formData.baseInfoForm = this.selectItem[0];
        this.title = "编辑桥梁评定数据";
        this.showAddEdit = true;
      }
    },

    // 搜索按钮
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    // 重置按钮
    resetQuery() {
      this.routeCode = [];

      this.dateRange = [];
      this.queryParams = {
        pageNum: 1,
        pageSize: 20,
        checkYear: new Date().getFullYear()+''
      };
      this.handleQuery();
    },
    // 表单重置
    reset() {
      this.resetForm("form");
    },
    // 新增按钮操作
    handleAdd() {
      this.forView = false;
      this.showAddEdit = true;
      this.title = "新增定期检查数据";
      this.formData.baseInfoForm = {};
    },
    closeImportAdd(v) {
      this.showImportAdd = false;
      if (v) this.getList();
    },
    closeImportZip(v) {
      this.showImportZip = false;
      if (v) this.getList();
    },
    handleDateChange() {
      this.queryParams.constructionStartDate = this.dateRange[0];
      this.queryParams.constructionEndDate = this.dateRange[1];
    },
    // 表格点击勾选
    handleRowClick(row) {
      row.isSelected = !row.isSelected;
      this.$refs.table.toggleRowSelection(row);
    },

    // 导入更新按钮
    importUpdate() {
      this.isUpdate = true;
      this.showImportAdd = true;
      this.importType = 1;
      this.importBaseType = "7";
      this.tipsText='';
    },
    // 导入新增按钮
    importAdd() {
      this.isUpdate = false;
      this.showImportAdd = true;
      this.importType = 2;
      this.importBaseType = "7";
      this.tipsText='';
    },
    // 导入附件按钮
    importZip() {
      this.isUpdate = false;
      this.showImportZip = true;
      this.importBaseType = "7";
      this.importType = 2;
    },
    // 导出清单按钮
    exportList() {
      this.download(
        "/baseData/bridge/periodic/detection/export",
        { ids: this.ids, ...this.queryParams },
        ``,
        {
          headers: { "Content-Type": "application/json;" },
          parameterType: "body",
        }
      );
    },
  },
};
</script>

<style lang="scss" scoped>
// @import "~@/assets/styles/element-ui-global.scss";
</style>
