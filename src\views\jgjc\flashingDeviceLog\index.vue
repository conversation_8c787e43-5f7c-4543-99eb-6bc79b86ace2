<template>
  <div class="app-container maindiv">
    <el-row>
      <div class="draggable">
        <el-table
          ref="dataTable"
          v-loading="loading"
          :data="tableData"
          :height="
                showSearch ? 'calc(100vh - 330px)' : 'calc(100vh - 270px)'
              "
          border
          row-key="id"
          size="mini"
          stripe
          style="width: 100%"
        >
          <el-table-column
            align="center"
            label="序号"
            type="index"
            width="50"
          />
          <template v-for="(column,index) in columns">
            <el-table-column v-if="column.visible"
                             :label="column.label"
                             :prop="column.field"
                             :width="column.width"
                             align="center"
                             show-overflow-tooltip>
              <template slot-scope="scope">
                <dict-tag v-if="column.dict" :options="dict.type[column.dict]" :value="scope.row[column.field]"/>
                <template v-else-if="column.slots">
                  <RenderDom :index="index" :render="column.render" :row="scope.row"/>
                </template>
                <span v-else-if="column.isTime">{{ parseTime(scope.row[column.field], '{y}-{m}-{d}') }}</span>
                <span v-else>{{ scope.row[column.field] }}</span>
              </template>
            </el-table-column>
          </template>
          <el-table-column
            align="center"
            class-name="small-padding fixed-width"
            fixed="right"
            label="操作"
            width="220"
          >
            <template slot-scope="scope">
              <el-button
                icon="el-icon-view"
                size="mini"
                type="text"
                @click="handleOpenDetail(scope.row)"
              >查看明细
              </el-button
              >
              <el-button
                icon="el-icon-delete"
                size="mini"
                type="text"
                @click="handleDelete(scope.row)"
              >删除
              </el-button
              >
            </template>
          </el-table-column>
        </el-table>
        <pagination
          v-show="total > 0"
          :limit.sync="queryParams.pageSize"
          :page.sync="queryParams.pageNum"
          :total="total"
          @pagination="handleQuery"
        />
      </div>
    </el-row>
    <el-dialog :append-to-body="true" :destroy-on-close="true" :visible.sync="relaFlag" title="明细"
               width="70%" @close="handleClose">
      <div class="draggable">
        <el-table
          v-loading="loading"
          :data="detailList"
          :height="
                showSearch ? 'calc(100vh - 330px)' : 'calc(100vh - 270px)'
              "
          border
          row-key="id"
          size="mini"
          stripe
          style="width: 100%"
        >
          <el-table-column
            align="center"
            label="序号"
            type="index"
            width="50"
          />
          <template v-for="(column,index) in detailColumns">
            <el-table-column v-if="column.visible"
                             :label="column.label"
                             :prop="column.field"
                             :width="column.width"
                             align="center"
                             show-overflow-tooltip>
              <template slot-scope="scope">
                <dict-tag v-if="column.dict" :options="dict.type[column.dict]" :value="scope.row[column.field]"/>
                <template v-else-if="column.slots">
                  <RenderDom :index="index" :render="column.render" :row="scope.row"/>
                </template>
                <span v-else-if="column.isTime">{{ parseTime(scope.row[column.field], '{y}-{m}-{d}') }}</span>
                <span v-else>{{ scope.row[column.field] }}</span>
              </template>
            </el-table-column>
          </template>
          <el-table-column
            align="center"
            class-name="small-padding fixed-width"
            fixed="right"
            label="操作"
            width="220"
          >
            <template slot-scope="scope">
              <el-button
                icon="el-icon-delete"
                size="mini"
                type="text"
                @click="handleDeleteDetail(scope.row)"
              >删除
              </el-button
              >
            </template>
          </el-table-column>
        </el-table>
        <pagination
          v-show="detailTotal > 0"
          :limit.sync="detailQueryParams.pageSize"
          :page.sync="detailQueryParams.pageNum"
          :total="detailTotal"
          @pagination="handleQueryDetail"
        />
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {listLog,listLogDetail,delLog,delLogDetail} from "@/api/jgjc/flashingDeviceLog/flashingDeviceLog";

export default {
  name: 'FlashingDeviceLog',
  components: {
    RenderDom: {
      functional: true,
      props: {
        row: Object,
        index: Number,
        render: Function
      },
      render(createElement, ctx) {
        const {row, index} = ctx.props
        return ctx.props.render(row, index)
      }
    }
  },
  props: [],
  data() {
    return {
      showSearch: false,
      loading: false,
      columns: [
        {key: 0, width: 130, field: 'flashType', label: '爆闪设备类型', visible: true},
        {key: 1, field: 'exeMode', label: '执行模式', visible: true},
        {key: 2, width: 100, field: 'successCount', label: '成功个数', visible: true},
        {key: 3, width: 100, field: 'failCount', label: '失败个数', visible: true},
        {key: 4, field: 'exeTime', label: '执行时间', visible: true},
      ],
      detailColumns: [
        {key: 0, field: 'commonCode', label: '设备控制编码', visible: true},
        {key: 1, field: 'deviceName', label: '设备名称', visible: true},
        {key: 2, field: 'deviceCode', label: '设备编码', visible: true},
        {key: 3, field: 'isSuc', label: '是否成功', visible: true},
      ],
      tableData: [],
      relaFlag: false,
      detailList: [],
      total: 0,
      detailTotal: 0,
      queryParams: {
        pageNum: 1,
        pageSize: 10
      },
      detailQueryParams: {
        pageNum: 1,
        pageSize: 10
      },
    }
  },
  computed: {},
  watch: {},
  mounted() {
    this.handleQuery()
  },
  methods: {
    // 查询
    handleQuery() {
      this.loading = true
      listLog(this.queryParams).then(res => {
        this.tableData = res.rows
        this.loading = false
        this.total = res.total
      })
    },
    handleOpenDetail(e) {
      this.relaFlag = true
      this.detailQueryParams.logId = e.id
      this.handleQueryDetail()
    },
    // 删除
    handleDelete(e) {
      this.$modal.confirm('是否确认删除').then(() => {
        this.loading = true
        const ids = [e.id]
        delLog({ids: ids }).then(res => {
          this.$modal.msgSuccess('删除成功');
          this.handleQuery()
        })
      })
    },
    // 查询
    handleQueryDetail() {
      this.loading = true
      listLogDetail(this.detailQueryParams).then(res => {
        this.detailList = res.rows
        this.loading = false
        this.detailTotal = res.total
      })
    },

    handleDeleteDetail(e) {
      this.$modal.confirm('是否确认删除').then(() => {
        this.loading = true
        const ids = [e.id]
        delLogDetail({ids: ids }).then(res => {
          this.$modal.msgSuccess('删除成功');
          this.handleQuery()
        })
      })
    },
    handleClose() {
      this.detailList = []
    },
  }
}
</script>

<style lang="scss" scoped>
::v-deep .el-table th .gutter {
  display: table-cell !important;
}
</style>
