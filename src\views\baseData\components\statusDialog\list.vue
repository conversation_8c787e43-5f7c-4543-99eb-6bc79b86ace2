<template>
  <div>
    <el-dialog
      :title="title"
      :visible.sync="visibleSync"
      :before-close="handleClose"
      width="60%"
      append-to-body
      :close-on-click-modal="false"
    >
      <el-table
        v-loading="loading"
        size="mini"
        height="300"
        style="width: 100%"
        border
        :data="tableData"
      >
        <el-table-column
          label="序号"
          type="index"
          width="50"
          align="center"
        />
        <el-table-column
          label="状态"
          align="center"
          prop="operationState"
          min-width="120"
          show-overflow-tooltip
        >
          <template #default="{ row }">
            <el-link
              :underline="false"
              :type="
                { 1: 'info', 2: 'success', 3: 'danger', 4: 'primary' }[
                  row.operationState
                ]
              "
            >
              {{
                { 1: "未投入运营", 2: "运营中", 3: "已作废", 4: "已移交" }[
                  row.operationState
                ] || "运营中"
              }}
            </el-link>
          </template>
        </el-table-column>
        <el-table-column
          label="操作人"
          align="center"
          prop="createByName"
          min-width="120"
          show-overflow-tooltip
        />
        <el-table-column
          label="操作时间"
          align="center"
          prop="createTime"
          min-width="140"
          show-overflow-tooltip
        />
        <el-table-column
          label="原因"
          align="center"
          prop="businessReason"
          min-width="160"
          show-overflow-tooltip
        />
        <el-table-column
          label="备注"
          align="center"
          prop="remark"
          min-width="160"
          show-overflow-tooltip
        />
        <el-table-column
          label="附件"
          align="center"
          prop="attachmentFilesId"
          width="220px"
          show-overflow-tooltip
        >
          <template slot-scope="scope">
            <el-button
              v-if="scope.row.attachmentFilesId"
              style="font-size: 12px; padding: 0"
              type="text"
              icon="el-icon-paperclip"
              @click="handleOfficePreview(scope.row)"
            >{{scope.row.originalFilename}}</el-button>
            <span v-else>无</span>
          </template>
        </el-table-column>
      </el-table>
      <pagination
        :total="total"
        :page.sync="pageNum"
        :limit.sync="pageSize"
        @pagination="getList"
      />
      <div slot="footer">
        <el-button @click="handleClose">关 闭</el-button>
      </div>
    </el-dialog>
    <el-dialog
      :visible.sync="officePreviewVisible"
      custom-class="fixed-dialog"
      append-to-body
      width="80%"
    >
      <OfficePreview
        :dialog-url="dialogUrl"
        :content-type="contentType"
      />
    </el-dialog>
  </div>
</template>

<script>
import { getBusinessStatusRecord } from '@/api/baseData/common/index.js'
import { formatDate } from '@/utils/index'
import { getFile } from '@/api/file'
import OfficePreview from '@/components/OfficePreview/index.vue'

export default {
  name: 'OperationalList',
  components: { OfficePreview },
  props: {
    visible: {
      default: false
    },
    dataId: {
      default: ''
    },
    baseDataType: {
      default: null
    },
    title: {
      type: String,
      default: null
    }
  },
  data() {
    return {
      loading: false,
      tableData: [],
      total: 0,
      pageNum: 1,
      pageSize: 10,
      officePreviewVisible: false,
      dialogUrl: '',
      contentType: ''
    }
  },
  computed: {
    visibleSync: {
      get() {
        return this.visible
      },
      set(v) {
        this.$emit('update:visible', v)
      }
    }
  },
  watch: {},
  created() {
    this.getList()
  },
  methods: {
    async getList() {
      this.loading = true
      getBusinessStatusRecord({
        baseDataType: this.baseDataType,
        dataId: this.dataId,
        pageNum: this.pageNum,
        pageSize: this.pageSize
      })
        .then(res => {
          if (res.code === 200) {
            res.rows.map(async el => {
              el.createTime = formatDate(el.createTime)
              if (el.attachmentFilesId) {
                const r = await getFile({ ownerId: el.attachmentFilesId })
                if (r.code === 200) {
                  el.originalFilename = r.data.originalFilename
                  el.dialogUrl = r.data.url
                  el.contentType = r.data.contentType
                }
              }
            })
            setTimeout(() => {
              this.tableData = res.rows
            }, 300)
            this.total = res.total
          }
        })
        .finally(() => {
          this.loading = false
          this.$forceUpdate()
        })
    },
    async handleOfficePreview(row) {
      this.officePreviewVisible = true
      this.dialogUrl = row.dialogUrl
      this.contentType = row.contentType
    },
    handleClose() {
      this.$emit('cancel')
    }
  }
}
</script>

<style lang="scss" scoped>
</style>
