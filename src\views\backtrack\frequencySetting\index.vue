<template>
  <div class="app-container">
    <el-row :gutter="20">

      <!--筛选区开始-->
      <el-col :span="24" :xs="24">
        <el-row>
          <el-col :span="24" >
            <el-form :model="queryParams" ref="queryForm" size="small" :inline="true"  label-width="68px">
              <el-form-item label="" prop="riskGrade">
                <el-select v-model="queryParams.riskGrade" placeholder="请选择风险等级" style="width: 100%">
                  <el-option label="一级（重大）" value="一级（重大）"></el-option>
                  <el-option label="二级（较大）" value="二级（较大）"></el-option>
                  <el-option label="三级（一般）" value="三级（一般）"></el-option>
                  <el-option label="四级（低）" value="四级（低）"></el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="" prop="patrolType">
                <el-select v-model="queryParams.patrolType" placeholder="巡查项类型" clearable style="width: 100%">
                  <el-option
                    v-for="item in patrolTypeOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  />
                </el-select>
              </el-form-item>
              <el-form-item>
                <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
                <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
              </el-form-item>
            </el-form>
          </el-col>
        </el-row>
        <!--筛选区结束-->
        <!--操作按钮区开始-->
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button
                type="primary"
                icon="el-icon-plus"
                size="mini"
                @click="handleAdd"
                v-hasPermi="['disaster:frequencySetting:add']"
            >新增</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button
                type="success"
                icon="el-icon-edit"
                size="mini"
                :disabled="single"
                @click="handleUpdate"
                v-hasPermi="['disaster:frequencySetting:edit']"
            >修改</el-button>
          </el-col>
          <right-toolbar :showSearch.sync="showSearch" @queryTable="getList" :columns="columns"></right-toolbar>
        </el-row>
        <!--操作按钮区结束-->

        <!--数据表格开始-->
        <div class="tableDiv">
          <el-table v-adjust-table ref="table" size="mini" :height="showSearch ? 'calc(100vh - 320px)' : 'calc(100vh - 260px)'" style="width: 100%"
                    v-loading="loading" border :data="frequencySettingList" @selection-change="handleSelectionChange"
                    :row-style="rowStyle" @row-click="handleRowClick">
            <el-table-column type="selection" width="50" align="center" />
            <el-table-column fixed label="序号" type="index" width="50">
              <template v-slot="scope">
                {{ (scope.$index + (queryParams.pageNum - 1) * queryParams.pageSize)+1 }}
              </template>
            </el-table-column>
            <el-table-column label="风险等级" align="center" prop="riskGrade" />
            <el-table-column label="巡查项类型" align="center" prop="patrolType">
              <template v-slot="scope">
                {{ scope.row.patrolType===1 ? '风险点' : '设计回溯' }}
              </template>
            </el-table-column>
            <el-table-column label="巡查频率(天/次)" align="center" prop="patrolFrequency" />
            <el-table-column label="操作" fixed="right" align="center" width="160" class-name="small-padding fixed-width">
              <template slot-scope="scope" v-if="scope.row.userId !== 1">
                <el-button size="mini" type="text" icon="el-icon-edit" @click="handleUpdate(scope.row)" v-hasPermi="['disaster:frequencySetting:edit']">
                  修改
                </el-button>
                <el-button size="mini" type="text" icon="el-icon-delete" @click="handleDelete(scope.row)" v-hasPermi="['disaster:frequencySetting:remove']">
                  删除
                </el-button>
              </template>
            </el-table-column>
          </el-table>

          <pagination
              v-show="total>0"
              :total="total"
              :page.sync="queryParams.pageNum"
              :limit.sync="queryParams.pageSize"
              @pagination="getList"
          />
        </div>
        <!--数据表格结束-->
      </el-col>
    </el-row>

    <!-- 添加或修改风险点巡查频率配置对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="600px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="100px">
        <el-form-item label="风险等级" prop="riskGrade">
          <el-select v-model="form.riskGrade" placeholder="请选择" style="width: 100%">
            <el-option label="一级（重大）" value="一级（重大）"></el-option>
            <el-option label="二级（较大）" value="二级（较大）"></el-option>
            <el-option label="三级（一般）" value="三级（一般）"></el-option>
            <el-option label="四级（低）" value="四级（低）"></el-option>
          </el-select>
<!--          <el-input v-model="form.riskGrade" placeholder="请输入风险等级" />-->
        </el-form-item>
        <el-form-item label="巡查项类型" prop="patrolType">
          <el-select v-model="form.patrolType" placeholder="请选择" style="width: 100%">
            <el-option
              v-for="item in patrolTypeOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="巡查频率" prop="patrolFrequency">
          <el-row :gutter="17">
            <el-col :span="11">
              <el-input v-model="form.patrolFrequency" placeholder="请输入" type="number" style="width: 100%"/>
            </el-col>
            <el-col class="line" :span="6">
              <span>天/次</span>
            </el-col>
          </el-row>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

  </div>
</template>

<script>
  import { listFrequencySetting, getFrequencySetting, delFrequencySetting, addFrequencySetting, updateFrequencySetting } from "@/api/backtrack/frequency/frequencySetting";
  import { getToken } from "@/utils/auth";
  import "@riophae/vue-treeselect/dist/vue-treeselect.css";

  export default {
    name: "FrequencySetting",
    components: {  },
    data() {
      return {
        // 遮罩层
        loading: true,
        // 选中数组
        ids: [],
        // 非单个禁用
        single: true,
        // 非多个禁用
        multiple: true,
        // 显示搜索条件
        showSearch: false,
        dictType:[],
        // 总条数
        total: 0,
        // 风险点巡查频率配置表格数据
        frequencySettingList: null,
        // 弹出层标题
        title: "",
        // 部门树选项
        deptOptions: undefined,
        // 是否显示弹出层
        open: false,

        // 表单参数
        form: {},
        defaultProps: {
          children: "children",
          label: "label"
        },
        patrolTypeOptions: [
          {value: 1,label: '风险点',},
          {value: 2,label: '设计回溯',},
        ],
        // 用户导入参数
        upload: {
          // 是否显示弹出层（用户导入）
          open: false,
          // 弹出层标题（用户导入）
          title: "",
          // 是否禁用上传
          isUploading: false,
          // 是否更新已经存在的用户数据
          updateSupport: 0,
          // 设置上传的请求头部
          headers: { Authorization: "Bearer " + getToken() },
          // 上传的地址
          url: process.env.VUE_APP_BASE_API + "/system/user/importData"
        },
        // 查询参数
        queryParams: {
          pageNum: 1,
          pageSize: 50,
            riskGrade: null,
            patrolFrequency: null
        },
        // 列信息
        columns: [
        { key: 0, label: `风险等级`, visible: true },
        { key: 1, label: `巡查频率（天/次）`, visible: true }
        ],
        // 表单校验
        rules: {


        }
      };
    },
    watch: {
      // 根据名称筛选部门树
                      },
    created() {
      this.getList();
      // this.getDeptTree();
      // this.getConfigKey("sys.user.initPassword").then(response => {
      //   this.initPassword = response.msg;
      // });
    },
    methods: {
      /** 查询用户列表 */
      getList() {
        this.loading = true;
        listFrequencySetting(this.queryParams).then(response => {
          this.frequencySettingList = response.rows;
          this.total = response.total;
          this.loading = false;
        });
      },
      // 取消按钮
      cancel() {
        this.open = false;
        this.reset();
      },
      // 表单重置
      reset() {
        this.form = {
            riskGrade: null,
            patrolFrequency: null
        };
        this.resetForm("form");
      },
      /** 搜索按钮操作 */
      handleQuery() {
        this.queryParams.pageNum = 1;
        this.getList();
      },
      /** 重置按钮操作 */
      resetQuery() {
        this.dateRange = [];
        this.resetForm("queryForm");
        this.handleQuery();
      },
      // 多选框选中数据
      handleSelectionChange(selection) {
        this.ids = selection.map(item => item.id);
        this.single = selection.length != 1;
        this.multiple = !selection.length;
      },
      // 表格点击勾选
      handleRowClick(row) {
        row.isSelected = !row.isSelected;
        this.$refs.table.toggleRowSelection(row);
      },
      // 勾选高亮
      rowStyle({ row, rowIndex }) {
        if (this.ids.includes(row.id)) {
          return { 'background-color': '#E1F0FF', color: '#333' }
        } else {
          return { 'background-color': '#fff', color: '#333' }
        }
      },
      /** 新增按钮操作 */
      handleAdd() {
        this.reset();
        this.open = true;
        this.title = "添加风险点巡查频率配置";
      },
      /** 修改按钮操作 */
      handleUpdate(row) {
        this.reset();
        const id = row.id || this.ids;
        getFrequencySetting(id).then(response => {
          this.form = response.data;
          this.open = true;
          this.title = "修改风险点巡查频率配置";
        });

      },
      /** 提交按钮 */
      submitForm: function() {
        this.$refs["form"].validate(valid => {
          if (valid) {
            if (this.form.id != null) {
              updateFrequencySetting(this.form).then(response => {
                this.$modal.msgSuccess("修改成功");
                this.open = false;
                this.getList();
              });
            } else {
              addFrequencySetting(this.form).then(response => {
                this.$modal.msgSuccess("新增成功");
                this.open = false;
                this.getList();
              });
            }
          }
        });
      },
      /** 删除按钮操作 */
      handleDelete(row) {
        const id = row.id || this.ids;
        this.$modal.confirm('是否确认删除风险点巡查频率配置编号为"' + id + '"的数据项？').then(function() {
          return delFrequencySetting(id);
        }).then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        }).catch(() => {});
      },

      /** 导出按钮操作 */
      handleExport() {

    this.download('disaster/frequencySetting/export', {
      ...this.queryParams
    }, `frequencySetting_${new Date().getTime()}.xlsx`)

      },
      /** 导入按钮操作 */
      handleImport() {
        this.upload.title = "用户导入";
        this.upload.open = true;
      },
      /** 下载模板操作 */
      importTemplate() {
        this.download('system/user/importTemplate', {
        }, `user_template.xlsx`)
      },
      // 文件上传中处理
      handleFileUploadProgress(event, file, fileList) {
        this.upload.isUploading = true;
      },
      // 文件上传成功处理
      handleFileSuccess(response, file, fileList) {
        this.upload.open = false;
        this.upload.isUploading = false;
        this.$refs.upload.clearFiles();
        this.$alert("<div style='overflow: auto;overflow-x: hidden;max-height: 70vh;padding: 10px 20px 0;'>" + response.msg + "</div>", "导入结果", { dangerouslyUseHTMLString: true });
        this.getList();
      },
      // 提交上传文件
      submitFileForm() {
        this.$refs.upload.submit();
      }
  }
  };
</script>
<style>
  .hasTagsView .app-main[data-v-078753dd]{
    background: #f5f7fa;
  }

  .tableDiv{
    background-color: white;
    padding-bottom: 10px;
  }
</style>
