<template>
  <div v-loading="loading" class="road-interflow-edit" style="padding: 20px">
    <el-row :gutter="20">
      <el-form
          ref="elForm"
          :model="formData"
          :rules="rules"
          :disabled="readOnly"
          label-width="130px"
          size="medium"
      >
        <el-col :span="12">
          <el-form-item label="督办单位" prop="domainId">
            <selectTree
                :key="'domainId'"
                onlySelectChild
                v-model="formData.domainId"
                :deptType="100"
                :deptTypeList="[1, 3, 4]" clearable
                filterable
                placeholder="督办单位"
                style="width: 100%"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="养护路段" prop="maiSecId">
            <RoadSection v-model="formData.maiSecId" :deptId="formData.domainId" placeholder="请选择养护路段"
                         style="width: 100%"/>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="督办名称" prop="name">
            <el-input v-model="formData.name" placeholder="督办名称" style="width: 100%"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="接收单位" prop="acceptDomainId">
            <construction-select v-model="formData.acceptDomainId" :mai-domain-id="formData.domainId" :mai-sec-id="formData.maiSecId" placeholder="接收单位"></construction-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="紧急程度" prop="emgGrade">
            <dict-select v-model="formData.emgGrade" :type="'urgent_degree'" placeholder="紧急程度" style="width: 100%"/>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="督办事项" prop="content">
            <el-input type="textarea" :rows="4" v-model="formData.content" placeholder="督办事项"
                      style="width: 100%"></el-input>
          </el-form-item>
        </el-col>
      </el-form>
      <el-col :span="24">
        <div class="mb10" v-if="!readOnly" style="text-align: right"><el-button type="primary" @click="addEvent">新增</el-button></div>
        <el-table v-adjust-table
            :data="eventList"
            border
            height="300px"
            ref="tableRef"
            style="width: 100%">
          <template v-for="(column,index) in columns">
            <el-table-column v-if="column.visible" show-overflow-tooltip
                             :label="column.label"
                             :prop="column.field"
                             align="center">
              <template slot-scope="scope">
                <dict-tag v-if="column.dict" :options="dict.type[column.dict]" :value="scope.row[column.field]"/>
                <template v-else-if="column.slots">
                  <RenderDom :index="index" :render="column.render" :row="scope.row"/>
                </template>
                <span v-else-if="column.isTime">{{ parseTime(scope.row[column.field], '{y}-{m}-{d}') }}</span>
                <span v-else >{{ scope.row[column.field] }}</span>
              </template>
            </el-table-column>
          </template>
          <el-table-column  v-if="type >= 2" label="回复描述" prop="acceptRemark" width="200" align="center">
            <template slot-scope="scope">
              <el-input v-model="scope.row.acceptRemark" placeholder="回复描述" style="width: 100%"/>
            </template>
          </el-table-column>
          <el-table-column
              align="center"
              v-if="!readOnly"
              class-name="small-padding fixed-width"
              fixed="right"
              label="操作"
              width="70"
          >
            <template slot-scope="scope">
              <el-button type="text" @click="handleRemove(scope.row)">移除</el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-col>
      <el-col :span="24" v-if="!readOnly">
        <div style="text-align: right" class="mt20">
          <el-button type="primary" @click="handleSave">保存</el-button>
        </div>
      </el-col>

      <el-col :span="24" v-if="type == 1 && readOnly">
        <div style="text-align: right" class="mt20">
          <el-button type="primary" @click="handleSubmit">下发</el-button>
        </div>
      </el-col>

      <el-col :span="24" v-if="type == 2">
        <div style="text-align: right" class="mt20">
          <el-button type="primary" @click="handleSubmit">接收</el-button>
          <el-button type="danger" @click="handleReject">驳回</el-button>
        </div>
      </el-col>
    </el-row>
    <el-dialog :visible.sync="addEventDialog" title="添加事件" width="80%" append-to-body modal-append-to-body>
      <add-event :maiSecId="formData.maiSecId" @select="selectEvent"></add-event>
    </el-dialog>
  </div>
</template>

<script>
import RoadSection from "@/views/baseData/components/roadSection/index.vue";
import selectTree from "@/components/DeptTmpl/selectTree.vue";
import AddEvent from "@/views/dailyMaintenance/supervise/addEvent.vue";
import ConstructionSelect from "@/components/ConstructionSelect/index.vue";
import {
  addSupervision,
  editSupervision,
  getSupervisionById,
  issuanceMethod,
  rejectSupervision
} from "@/api/dailyMaintenance/supervise";
import {formatPile} from "@/utils/ruoyi";

export default {
  components: {
    ConstructionSelect,
    AddEvent, selectTree, RoadSection,
    RenderDom: {
      functional: true,
      props: {
        row: Object,
        index: Number,
        render: Function
      },
      render(createElement, ctx) {
        const {row, index} = ctx.props
        return ctx.props.render(row, index)
      }
    }},
  dicts: ['dis_stage_name'],
  props: {
    row: {
      type: Object,
      default: {}
    },
    readOnly: {
      type: Boolean,
      default: false
    },
    type: {
      type: Number,
      default: 1
    }
  },
  watch: {
    row: {
      handler: function (val) {
        if (val.id) {
          // 查询明细
          this.loading = true
          getSupervisionById(val.id).then(res => {
            this.formData = res.data
            this.formData.taskId = val.taskId
            this.realDisData = res.data?.detailList || []
            this.eventList = res.data.detailList.map(item => ({
              ...item.disObj,
              acceptRemark: item.acceptRemark
            }));
            this.eventList.forEach(item => {
              item.beginMileShow = formatPile(item.beginMile)
              item.endMileShow = formatPile(item.endMile)
            })
            this.loading = false
          })
        }
      },
      immediate: true,
      deep: true
    }
  },
  data() {
    return {
      loading: false,
      formData: {
      },
      eventList: [],
      realDisData: [],
      rules: {
        domainId: [
          { required: true, message: '请选择督办单位', trigger: 'change' }
        ],
        maiSecId: [
          { required: true, message: '请选择养护路段', trigger: 'change' }
        ],
        name: [
          { required: true, message: '请输入督办名称', trigger: 'blur' }
        ],
        acceptDomainId: [
          { required: true, message: '请选择接收单位', trigger: 'change' }
        ],
        emgGrade: [
          { required: true, message: '请选择紧急程度', trigger: 'change' }
        ]
      },
      columns: [
        {key: 0, width: 100, field: 'routeCode', label: `路线编码`, visible: true},
        {key: 1, width: 100, field: 'beginMileShow', label: `起点桩号`, visible: true},
        {key: 2, width: 100, field: 'endMileShow', label: `终点桩号`, visible: true},
        {key: 3, width: 100, field: 'domainName', label: `管养单位`, visible: true},
        {key: 4, width: 130, field: 'disTypeName', label: `事件类型`, visible: true},
        {key: 5, width: 200, field: 'disDesc', label: `事件描述`, visible: true},
        {key: 6, width: 200, field: 'code', label: `施工单编号`, visible: true},
        {key: 7, width: 100, field: 'collectTime', label: `发现日期`, visible: true},
        {key: 8, width: 100, field: 'stage', label: `事件阶段`, visible: true, dict: 'dis_stage_name'},
        {key: 9, width: 100, field: 'timeOutMsg', label: `是否超时`, visible: true},
      ],
      addEventDialog: false
    }
  },
  methods: {
    handleSave() {
      this.$refs.elForm.validate(valid => {
          if (!valid) return
        this.formData.detailList= []
        this.loading = true
        this.eventList.forEach(item => {
          this.formData.detailList.push({
            dataId: item.id
          })
        })
        if (this.formData.id) {
          editSupervision(this.formData).then(res => {
            if (res.code == 200) {
              this.$message.success('保存成功')
              this.loading = false
              this.$emit('close')
            }
          })
        } else {
          addSupervision(this.formData).then(res => {
            if (res.code == 200) {
              this.$message.success('保存成功')
              this.loading = false
              this.$emit('close')
            }
          })
        }
      })
    },
    handleSubmit() {
      this.$refs.elForm.validate(valid => {
        if (!valid) return
        this.loading = true
        for (let i = 0; i < this.realDisData.length; i++) {
          const disData = this.realDisData[i]
          disData.acceptRemark = this.eventList.find(item => item.id == disData.disObj.id)?.acceptRemark || ''
        }
        this.formData.detailList = this.realDisData

        issuanceMethod(this.formData).then(res => {
          if (res.code == 200) {
            this.$message.success('下发成功')
            this.loading = false
            this.$emit('close')
          }
        })
      })
    },
    handleReject() {
      this.formData.detailList = this.realDisData
      rejectSupervision(this.formData).then(res => {
        if (res.code == 200) {
          this.$message.success('驳回成功')
          this.loading = false
          this.$emit('close')
        }
      })
    },
    addEvent() {
      if (this.formData.maiSecId) {
        this.addEventDialog = true
      } else {
        this.$message.warning('请先选择养护路段')
      }
    },
    selectEvent(data) {
      this.eventList.push(...data)
      this.eventList = this.eventList.filter((item, index, self) => {
        return self.findIndex(v => v.id === item.id) === index
      })
      this.addEventDialog = false
    },
    handleRemove(row) {
      this.eventList = this.eventList.filter(item => item.id !== row.id);
    }
  }
}
</script>

<style scoped lang="scss">

</style>
<style lang="scss" scoped>
@import "@/assets/styles/business.scss";
</style>
