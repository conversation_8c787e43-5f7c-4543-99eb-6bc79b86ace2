<template>
  <div>
    <el-dialog
      v-loading="loading"
      :title="title"
      :visible.sync="showForm"
      width="60%"
      append-to-body
      :before-close="handleClose"
      :close-on-click-modal="false"
      :class="forView ? 'forView' : ''"
    >
      <el-tabs v-model="activeName" type="card">
        <el-tab-pane label="基础信息" name="baseInfo">
          <div style="height: 60vh; overflow-y: auto; padding: 0 10px 0 5px">
            <el-form
              ref="baseInfo"
              :model="form"
              label-width="160px"
              :disabled="forView"
            >
              <div style="display: flex; flex-wrap: wrap">
                <ManageSelectTree placeholder="请选择" :formObject="form" />
                <el-col
                  :span="item.span ? item.span : 12"
                  v-for="(item, index) in baseInfoFields"
                  :key="index"
                  v-if="!item.forView || forView"
                >
                  <el-form-item
                    :label="item.label"
                    :prop="item.prop"
                    :rules="item.rules"
                  >
                    <span v-if="item.type === 'input'">
                      <el-input
                        v-model="form[item.prop]"
                        :placeholder="item.placeholder"
                        clearable
                      />
                    </span>
                    <span v-if="item.type === 'pileInput'">
                      <PileInput
                        v-model="form[item.prop]"
                        :placeholder="item.placeholder"
                      />
                    </span>
                    <span v-else-if="item.type === 'inputNumber'">
                      <el-input-number
                        style="width: 100%"
                        v-model="form[item.prop]"
                        :precision="item.precision"
                        clearable
                      ></el-input-number>
                    </span>
                    <span v-else-if="item.type === 'year'">
                      <el-date-picker
                        v-model="form[item.prop]"
                        style="width: 100%"
                        type="year"
                        :placeholder="item.placeholder"
                         :picker-options="pickerOptions"
                        clearable
                        value-format="yyyy"
                      />
                    </span>
                    <span v-else-if="item.type === 'select'">
                      <el-select
                        v-model="form[item.prop]"
                        style="width: 100%"
                        :placeholder="item.placeholder"
                        clearable
                        filterable
                        :disabled="!form[item.disabledFieds]"
                        @change="
                          (val) => {
                            handleSelect(val, item);
                          }
                        "
                      >
                        <el-option
                          v-for="v in item.options"
                          :key="v[item.optionValue]"
                          :label="v[item.optionLabel]"
                          :value="v[item.optionValue]"
                        />
                      </el-select>
                    </span>

                    <span v-else-if="item.type === 'CascaderRegion'">
                      <CascaderRegion
                        v-model="form[item.prop]"
                        :deep="1"
                        @input="
                          (value) => {
                            form[item.prop] = value;
                          }
                        "
                      />
                    </span>
                    <span v-else-if="item.type === 'selectTree'">
                      <SelectTree
                        v-model="form[item.prop]"
                        :dept-type="item.deptType"
                        placeholder="请选择"
                      />
                    </span>
                    <span v-else-if="item.type === 'dictSelect'">
                      <el-select
                        v-model="form[item.prop]"
                        style="width: 100%"
                        placeholder="请选择"
                        :multiple="item.multiple"
                        clearable
                        :disabled="item.disabledFieds"
                      >
                        <el-option
                          v-for="i in dict.type[item.dict]"
                          :key="i.value"
                          :label="i.label"
                          :value="i.value"
                        />
                      </el-select>
                    </span>

                    <span v-else-if="item.type === 'multiDictSelect'">
                      <MultiDictSelect
                        v-model="form[item.prop]"
                        :disabled="item.disabledFieds"
                        :multiple="item.multiple"
                        :options="dict.type[item.dict]"/>
                    </span>
                    <div v-else-if="item.type == 'uploadImg'">
                      <ImageUpload
                        :key="item.ownerId"
                        v-model="form[item.prop]"
                        :limit="1"
                        :owner-id="item.ownerId"
                        storage-path="/base/tunnel/baseInfo"
                      />
                    </div>
                    <span v-else-if="item.type === 'date'">
                      <el-date-picker
                        v-model="form[item.prop]"
                        style="width: 100%"
                        type="date"
                        :placeholder="item.placeholder"
                        :picker-options="pickerOptions"
                        clearable
                        value-format="yyyy-MM-dd"
                      />
                    </span>
                    <span v-else-if="item.type === 'tree'">
                      <select-tree
                        style="width: 100%"
                        v-model="form[item.prop]"
                        clearable
                      />
                    </span>
                    <span v-else-if="item.type === 'roadType'">
                      <SectionSelect
                        :style="forView ? 'pointer-events: none' : ''"
                        v-model="form[item.prop]"
                        :formObject="form"
                        :sectionId="form.maintenanceSectionId"
                        :disabled="!form.maintenanceSectionId"
                        clearable
                      />
                    </span>
                    <span v-else-if="item.type === 'coordinate'">
                      <lon-lat
                        :type="item.prepend"
                        :lon.sync="form[item.propLon]"
                        :lat.sync="form[item.propLat]"
                      />
                    </span>
                  </el-form-item>
                </el-col>
              </div>
            </el-form>
          </div>
        </el-tab-pane>
        <el-tab-pane label="结构技术数据" name="technology">
          <div style="height: 60vh; overflow-y: auto; padding: 0 10px 0 5px">
            <el-form
              ref="technology"
              :model="form"
              label-width="190px"
              :disabled="forView"
            >
              <div style="display: flex; flex-wrap: wrap">
                <el-col
                  :span="12"
                  v-for="(item, index) in technologyFields"
                  :key="index"
                  v-if="!item.forView || forView"
                >
                  <el-form-item
                    :label="item.label"
                    :prop="item.prop"
                    :rules="item.rules"
                  >
                    <span v-if="item.type === 'dictSelect'">
                      <el-select
                        v-model="form[item.prop]"
                        style="width: 100%"
                        placeholder="请选择"
                        clearable
                      >
                        <el-option
                          v-for="i in dict.type[item.dict]"
                          :key="i.value"
                          :label="i.label"
                          :value="i.value"
                        />
                      </el-select>
                    </span>
                    <span v-else-if="item.type === 'inputNumber'">
                      <el-input-number
                        style="width: 100%"
                        v-model="form[item.prop]"
                        :precision="item.precision"
                        :min="item.min"
                        :max="item.max"
                        clearable
                      ></el-input-number>
                    </span>
                    <span v-else-if="item.type === 'input'">
                      <el-input
                        v-model="form[item.prop]"
                        :placeholder="item.placeholder"
                      />
                    </span>
                  </el-form-item>
                </el-col>
              </div>
            </el-form>
          </div>
        </el-tab-pane>
        <el-tab-pane label="隧道管理信息" name="information">
          <div style="height: 60vh; overflow-y: auto; padding: 0 10px 0 5px">
            <el-form
              ref="information"
              :model="form"
              label-width="160px"
              :disabled="forView"
            >
              <div style="display: flex; flex-wrap: wrap">
                <el-col
                  :span="item.span ? item.span : 12"
                  v-for="(item, index) in informationFields"
                  :key="index"
                  v-if="!item.forView || forView"
                >
                  <el-form-item
                    :label="item.label"
                    :prop="item.prop"
                    :rules="item.rules"
                  >
                    <span v-if="item.type === 'input'">
                      <el-input
                        v-model="form[item.prop]"
                        :placeholder="item.placeholder"
                      />
                    </span>

                    <span v-else-if="item.type === 'inputTextarea'">
                      <el-input
                        v-model="form[item.prop]"
                        autosize
                        :placeholder="item.placeholder"
                        type="textarea"
                        clearable
                      />
                    </span>
                  </el-form-item>
                </el-col>
              </div>
            </el-form>
          </div>
        </el-tab-pane>
        <el-tab-pane label="隧道档案" name="fileInfo">
          <div style="height: 60vh; overflow-y: auto; padding: 0 10px 0 5px">
            <el-form
              ref="fileInfo"
              :model="form"
              label-width="160px"
              :disabled="forView"
            >
              <div style="display: flex; flex-wrap: wrap">
                <el-col
                  :span="12"
                  v-for="(item, index) in fileInfoFields"
                  :key="index"
                >
                  <el-form-item :label="item.label" :prop="item.prop">
                    <span v-if="item.type === 'dict'">
                      <el-select
                        v-model="form[item.prop]"
                        style="width: 100%"
                        placeholder="请选择"
                        clearable
                      >
                        <el-option
                          v-for="i in dict.type[item.dict]"
                          :key="i.value"
                          :label="i.label"
                          :value="i.value"
                        />
                      </el-select>
                    </span>
                    <span v-else-if="item.type === 'input'">
                      <el-input
                        style="width: 100%"
                        v-model="form[item.prop]"
                        :placeholder="item.placeholder"
                        clearable
                      ></el-input>
                    </span>
                    <span v-else-if="item.type === 'date'">
                      <el-date-picker
                        v-model="form[item.prop]"
                        style="width: 100%"
                        type="date"
                        :placeholder="item.placeholder"
                        clearable
                        value-format="yyyy-MM-dd"
                      />
                    </span>
                  </el-form-item>
                </el-col>
              </div>
            </el-form>
          </div>
        </el-tab-pane>
        <!--        <el-tab-pane-->
        <!--          v-if="form.id"-->
        <!--          label="改造数据"-->
        <!--          name="transform"-->
        <!--        >-->
        <!--          <div style="height: 60vh;">-->
        <!--            <ManageList-->
        <!--              v-if="activeName === 'transform'"-->
        <!--              :canEdit="forView ? false : true"-->
        <!--              :tunnelId="form.id"-->
        <!--            />-->
        <!--          </div>-->
        <!--        </el-tab-pane>-->
      </el-tabs>
      <div slot="footer">
        <el-button
          v-if="!forView"
          type="primary"
          @click="handleSubmit('submit')"
          >提 交</el-button
        >
        <el-button
          v-if="(formData.id ? formData.status == 1 : true) && !forView"
          type="primary"
          plain
          @click="handleSubmit('save')"
          >暂 存</el-button
        >
        <el-button @click="handleClose">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import formFields from "./js/formFields";
import dicts from "./js/dicts";
import PileInput from "@/components/PileInput/index.vue";
import SelectTree from "@/components/DeptTmpl/selectTree";
import SectionSelect from "@/components/SectionSelect";
import ManageSelectTree from "@/components/manageSelectTree/index.vue";
import ManageList from "./components/manageList.vue";
import lonLat from "@/components/mapPosition/lonLat.vue";
import MultiDictSelect from "@/views/baseData/components/MultiDictSelect";

import { getListPage } from "@/api/baseData/tunnel/baseInfo/manageList.js";
import { createIdWorker } from "@/api/baseData/common";
import { listMaintenanceSectionAll } from "@/api/system/maintenanceSection";
import { listByMaintenanceSectionId } from "@/api/baseData/common/routeLine";
import {
  addStatic,
  updateStatic,
  tempAddStatic,
} from "@/api/baseData/tunnel/baseInfo/index";
import CascaderRegion from "@/views/baseData/components/CascaderRegion/index.vue";
import {tempStatic} from "@/api/baseData/bridge/baseInfo";

export default {
  name: "tunnel-form",
  props: {
    formData: { type: undefined, default: () => {} },
    title: { type: String, default: "新增隧道数据" },
    showForm: { type: Boolean, default: false },
    forView: { type: Boolean, default: false },
  },
  components: {
    CascaderRegion,
    PileInput,
    SelectTree,
    SectionSelect,
    ManageList,
    lonLat,
    ManageSelectTree,
    MultiDictSelect,
  },
  dicts: dicts,
  data() {
    return {
      loading: false,
      activeName: "baseInfo",
      form: {
        managementMaintenanceId: "",
        managementMaintenanceBranchId: "",
      },
      baseInfoFields: [],
      technologyFields: [],
      informationFields: [],
      manageListData: [],
      fileInfoFields: [],
      pickerOptions: {
        disabledDate(v) {
          return v.getTime() > new Date().getTime() 
        }
      },
    };
  },
  created() {
    this.init();
  },
  methods: {
    init() {
      this.baseInfoFields = JSON.parse(JSON.stringify(formFields.baseInfo));
      this.technologyFields = JSON.parse(JSON.stringify(formFields.technology));
      this.informationFields = JSON.parse(
        JSON.stringify(formFields.information)
      );
      this.fileInfoFields = JSON.parse(JSON.stringify(formFields.fileInfo));
      // 编辑和查看时
      if (this.formData?.id) {
        const data = JSON.parse(JSON.stringify(this.formData));
        data.routeLevel = data.routeLevel ? data.routeLevel.split(",") : "";
        this.form = data;
      }
      if (!this.forView) {
        this.baseInfoFields.forEach(async (el) => {
          if (el.type === "uploadImg") {
            try {
              let res = await createIdWorker();
              if (res.code === 200) el.ownerId = Number(res.data);
              this.$forceUpdate();
            } catch (error) {}
          }
        });
      }
    },
    handleSelect(e, i) {
      switch (i.prop) {
        case "routeCode":
          if (e) {
            this.baseInfoFields.forEach((el) => {
              if (el.prop === "routeName") {
                const option = el.options.find((i) => i.routeCode === e);
                if (option) {
                  this.form.routeId = option.routeId;
                  this.form.routeName = option.routeName;
                  // this.form.routeLevel = option.routeType.toString()
                }
              }
            });
          } else {
            this.form.routeId = "";
            this.form.routeName = "";
          }
          break;
        case "routeName":
          if (e) {
            this.baseInfoFields.forEach((el) => {
              if (el.prop === "routeCode") {
                const option = el.options.find((i) => i.routeName === e);
                if (option) {
                  this.form.routeId = option.routeId;
                  // this.form.routeCode = option.routeCode
                }
              }
            });
          } else {
            this.form.routeId = "";
            this.form.routeCode = "";
          }
          break;
      }
    },

    // 监听选中管理处
    deptChange(e) {
      if (!e) return;
      listMaintenanceSectionAll({departmentIdList:[this.form.managementMaintenanceBranchId]}).then((res) => {
        if (res.code == 200) {
          this.baseInfoFields.forEach((el) => {
            if (el.prop === "maintenanceSectionId") {
              el.options = res.data;
            }
          });
        }
      });
    },
    // 监听选中养护路段
    maintenanceSectionChange(e) {
      if (!e) return;

      this.baseInfoFields.forEach((el) => {
        if (el.prop === "maintenanceSectionId") {
          //el.options中maintenanceSectionName等于e的routeGrade
          const option = el.options.find((i) => i.maintenanceSectionId === e);
          if (option && option.routeGrade) {
            this.form.routeLevel = option.routeGrade.split(",");
          }
        }
      });

      listByMaintenanceSectionId({ maintenanceSectionId: e }).then((res) => {
        if (res.code == 200) {
          this.baseInfoFields.forEach((el) => {
            if (el.prop === "routeCode" || el.prop === "routeName") {
              el.options = res.data;
            }
          });
        }
      });
    },
    async handleSubmit(type) {
      this.loading = true;
      const formNames = ["baseInfo", "technology", "information", "fileInfo"];
      switch (type) {
        case "submit":
          this.form.isStaging = false;
          break;
        case "save":
          this.form.isStaging = true;
          break;
      }
      if (!this.form.isStaging) {
        for (let index = 0; index < formNames.length; index++) {
          const element = formNames[index];
          const valid = await new Promise((resolve) => {
            this.$refs[element].validate((valid) => {
              resolve(valid);
            });
          });
          if (!valid) {
            this.loading = false;
            // 如果表单校验不通过，定位到对应的tab
            this.activeName = element;
            return; // 中止提交操作
          }
        }
      }
      for (const key in this.form) {
        if (Array.isArray(this.form[key])) {
          this.form[key] = this.form[key].join(",");
        }
      }

      this.form.rangeLength = null;
      if (this.form.isStaging) {
        tempAddStatic(this.form)
          .then((response) => {
            this.$modal.msgSuccess(
              this.form.isStaging  ? "暂存成功" : "新增成功"
            );
            this.loading = false;
            this.$emit("refresh");
          })
          .catch((err) => {
            this.loading = false;
          });
      } else {
        if (this.form.id != null) {

          const api = this.form.isStaging ? tempAddStatic : updateStatic;
          api(this.form)
            .then((response) => {
              this.loading = false;
              this.$modal.msgSuccess(
                this.form.isStaging ? "暂存成功" : "修改成功"
              );
              this.$emit("refresh");
            })
            .catch((err) => {
              this.loading = false;
            });
        } else {
          addStatic(this.form)
            .then((response) => {
              this.loading = false;
              this.$modal.msgSuccess(
                this.form.isStaging == true ? "暂存成功" : "新增成功"
              );
              this.$emit("refresh");
            })
            .catch((err) => {
              this.loading = false;
            });
        }
      }
    },
    handleClose() {
      if (this.forView) {
        this.form = {};
        this.$emit("close", false);
      } else {
        this.$modal
          .confirm("确认退出？")
          .then(() => {
            this.form = {};
            this.$emit("close", false);
          })
          .catch(() => {});
      }
    },
  },
  computed: {},
  watch: {
    "form.managementMaintenanceId"(newVal, oldVal) {
      // if (newVal) {
      //   this.deptChange(newVal);
      // }
      // if (oldVal && this.form.maintenanceSectionId) {
      //   this.form.maintenanceSectionId = "";
      //   this.form.routeCode = "";
      // }
    },
    'form.managementMaintenanceBranchId'(newVal, oldVal) {
      if (newVal) {
        this.deptChange(newVal)
      }
      if (oldVal) {
        if(this.form.maintenanceSectionId)this.form.maintenanceSectionId=''
        if(this.form.routeCode)this.form.routeCode=''
      }
    },
    "form.maintenanceSectionId"(newVal, oldVal) {
      if (newVal) {
        this.maintenanceSectionChange(newVal);
      }
      if (!newVal&&this.form.routeName) {
        this.form.routeLevel = [];
        this.form.routeName = "";
      }

      if (oldVal&&this.form.routeCode) {
        this.form.routeCode = "";
        this.form.routeName = "";
      }
    },
  },
};
</script>

<style lang="scss" scoped>
@import "~@/assets/styles/el-tabs.scss";
.forView ::v-deep .el-input.is-disabled .el-input__inner {
  background-color: white;
  border-color: #dfe4ed;
  color: #1d2129;
}
::v-deep .el-textarea.is-disabled .el-textarea__inner {
  background-color: white;
  border-color: #dfe4ed;
  color: #1d2129;
}
::v-deep .el-dialog__header {
  border-bottom: 0;
  padding: 20px 30px 0 30px !important;
}
</style>
