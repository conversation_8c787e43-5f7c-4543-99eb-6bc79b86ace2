<template>
  <div class="bim" :class="theme">
    <el-tabs tab-position="top" v-model="activeName" type="border-card">
      <el-tab-pane label="监测点图片" name="4"></el-tab-pane>
      <el-tab-pane v-if="videoInfo.length > 0" label="监测点视频" name="3"></el-tab-pane>
      <el-tab-pane label="设备在线状态" name="5"></el-tab-pane>
    </el-tabs>

    <div class="body" style="overflow: auto; display: flex; align-items: center;" v-show="activeName === '4'">
      <el-image :src="structureImages[0]" :preview-src-list="srcList" style="width: 100%;"></el-image>
    </div>

    <div class="body video" v-show="activeName === '3' && videoInfo.length > 0">
      <div class="hello-ezuikit-js">
        <el-select
          style="margin: 30px 0px"
          v-model="videoSelect"
          :teleported="false"
          popper-class="popperClass"
          placeholder="请选择"
          size="mini"
          @change="changeVideo"
        >
          <el-option v-for="(item, index) in videoInfo" :key="item.index" :label="item.name" :value="item.id"></el-option>
        </el-select>
        <div class="video-box" ref="videoContainer">
          <div id="video-container"></div>
        </div>
        <div>
          <el-button type="primary" @click="play">播放视频</el-button>
          <el-button type="primary" @click="stop">停止视频</el-button>
        </div>
      </div>
    </div>

    <div class="body" v-show="activeName === '5'">
      <equipmentonlineStatus />
    </div>

    <div class="btns">
      <div class="btn" @click="openWarningRecord" style="color: red;">超限警告记录</div>
      <div class="btn" @click="openWarningExecute">预警执行记录</div>
      <div class="btn" @click="handleEnd" v-if="checkData.planStatus == 1"
        v-has-menu-permi="['jgjc:earlyWarning:close']">结束预案</div>
      <div class="btn" @click="handleBegin" v-has-menu-permi="['jgjc:earlyWarning:start']"
        v-if="checkData.planStatus == 0">个性化启动预案
      </div>
    </div>

    <div style="position:fixed; z-index: 2000; width: 90vw;height: 90vh;left: 5%;top: 5%" class="model-box"
      v-if="recordVisible">
      <div class="title" style="z-index: 999">预警记录
        <i class="el-icon-close" @click="recordVisible = false"></i>
      </div>
      <div class="content" style="padding: 0">
        <warning-record :check-data='checkData' style="width: 100%;height: 100%" frameborder="0"
          @query='$emit("query")'></warning-record>
      </div>
    </div>

    <div style="position:fixed; z-index: 2000; width: 90vw;height: 90vh;left: 5%;top: 5%" class="model-box"
      v-if="executeVisible">
      <div class="title" style="z-index: 999">预警执行记录
        <i class="el-icon-close" @click="executeVisible = false"></i>
      </div>
      <div class="content" style="padding: 0">
        <warning-execute :check-data='checkData' style="width: 100%;height: 100%" frameborder="0"></warning-execute>
      </div>
    </div>

    <div class="model-box" v-if="modelVisible" title="启动预案" ref="dialog2" @mousedown="bringToFront('dialog2')">
      <div class="title" @mousedown="startDrag($event, 'dialog2')">启动预案
        <i class="el-icon-close" @click="modelVisible = false"></i>
      </div>
      <div class="content" style="color: white;">
        <el-form ref="elForm" :model="formData" :rules="rules" :label-width="'100px'">
          <el-row>
            <el-col :span="16">
              <el-form-item label="预案模式" prop="flashMode">
                <el-select v-model="formData.flashMode" @change="changeFlashMode" popper-class="type-popper"
                  :popper-append-to-body="false">
                  <el-option v-for="(item, index) in flashModes" :key="index" :label="item.label"
                    :value="item.value"></el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="爆闪">
                {{ flashData.strobe ? '开' : '关' }}
              </el-form-item>
              <el-form-item label="显示">
                {{ flashData.displayName }}
              </el-form-item>
              <el-form-item label="闪烁">
                {{ flashData.flicker ? '常规' : '常亮' }}
              </el-form-item>
              <el-form-item label="音频">
                {{ flashData.sound }}
              </el-form-item>
            </el-col>
            <el-col :span="8" class="tips">
              <img v-if="flashData.strobe" src="@/assets/earlyWarning/ssfwd.gif">
              <img v-else src="@/assets/earlyWarning/ssfwd.png">
              <div v-if="flashData.displayName != 'ball'" class="mode-dispaly" :style="{ 'color': flashData.display }"
                :class="flashData.flicker ? 'blink' : ''">{{ flashData.displayName }}
              </div>
              <div v-else class="display-ball" :style="{ 'background-color': flashData.display }"
                :class="flashData.flicker ? 'blink' : ''">
              </div>
            </el-col>
          </el-row>
          <div style="text-align: right; margin-top: 20px">
            <el-button class="model-btn" type="primary" @click="handleSubmit">发布</el-button>
          </div>
        </el-form>
      </div>
    </div>
  </div>
</template>

<script>
import equipmentonlineStatus from './components/equipmentonlineStatus.vue'
import warningRecord from './components/warningRecord.vue'
import warningExecute from './components/warningExecute.vue'
import { fetchGet } from './api.js'
import { getZjgtConfigPage, getSsConfigPage, getWdmConfigPage } from '@/api/jgjc/flashingModeConfig'
import { zjgtModes, wdmModes, sansiModes } from "./components/defaultModes.js"
import { getFile } from '@/api/file'
import { 
  getVideoList, //获取结构物下视频信息
  getTokenData //获取视频token
} from '@/api/Tibet/index'
import EZUIKit from 'ezuikit-js';
var player = null;

export default {
  name: 'BimAndPictures',
  props: {
    theme: {
      type: String,
      default: 'dark'
    }
  },
  components: { equipmentonlineStatus, warningRecord, warningExecute },
  data() {
    return {
      activeName: "3",
      hasBim: false,
      structureImages: ["https://jkjc.yciccloud.com:8002/ynjt-structure-file/7c28243f509445fb86fafaa6a0d0fccd-治租河.jpeg"],
      srcList: ["https://jkjc.yciccloud.com:8002/ynjt-structure-file/7c28243f509445fb86fafaa6a0d0fccd-治租河.jpeg"],
      videoInfo: [],
      TokenData: null, //萤石云TokenData
      videoSelect: 1,
      videoName: "",
      videoUrl: "",
      panoramic720view: "",
      checkData: {
        planStatus: 0, // 预案状态 0：未启动 1：已启动
      },  // 详情数据
      recordVisible: false, // 预警操作记录
      executeVisible: false, // 预警执行记录
      modelVisible: false, // 启动预案
      formData: {}, // 启动预案表单数据
      rules: { // 启动预案表单校验规则
        flashMode: [{ required: true, message: '请选择爆闪模式', trigger: 'change' }]
      },
      flashModes: zjgtModes, // 爆闪模式
      flashData: { // 爆闪数据
        strobe: false, // 爆闪
        displayName: '', // 显示
        display: '', // 显示颜色
        flicker: false, // 闪烁
        sound: '', // 音频
      },
    }
  },
  mounted() {
    window.$Bus.$on('treeToBim', (obj) => {
      this.hasBim = obj.fileId ? true : false
      if (obj.code === "1593262524063682560" || obj.code === "1593262527993745408") {
        obj.panoramic720view = "https://www.720yun.com/vr/4dbj5Oyyuf7"
      }
      this.activeName = obj.videoInfo ? '3' : '4'
      if (obj.structureImage) {
        this.structureImages = [obj.structureImage]
        this.srcList = [obj.structureImage]
      }
      if (obj.panoramic720view) {
        this.panoramic720view = obj.panoramic720view
      }
    })
    let data = JSON.parse(localStorage.getItem('mapData'));
    this.checkData = data;
    this.getTokenData();
	this.getImgList();
  },
  methods: {
    // 获取视频列表
    fetchVideoList() {
      const params = {"pageNum":1,"pageSize":100,"structureId": this.checkData.id};
      
      getVideoList(params).then(res => {
        this.videoInfo = this.transformToTargetFormat(res);
      }).catch(err => {
        this.$message.error('获取视频列表失败');
      });
    },
    getImgList(){
		  let data = JSON.parse(localStorage.getItem('mapData'));
		if ( data.imgPath) {
		  getFile({ ownerId:  data.imgPath }).then(async res => {
		    if (res.code == 200) {
				this.structureImages=[res.data?.thumbUrl];
				   this.srcList = [res.data?.thumbUrl]
		    }
		  }).finally(() => {
		  })
		}
	},
    getTokenData(){
      getTokenData().then(res => {
        this.TokenData = res.data;
        this.fetchVideoList();
      }).catch(err => {
        this.$message.error('获取海康萤石云token失败，请检查账号key是否变更');
      });
    },
    
    transformToTargetFormat(apiData) {
      return (apiData.rows || []).map((item, index) => ({
        id: index + 1, // 自动生成1, 2, 3...
        accessToken: this.TokenData,
        name: item.name,
        address: item.videoUrl
      }));
    },
    
    // 合并初始化和播放的方法
    initAndPlay() {
      const findItms = this.videoInfo.find((item) => item.id === this.videoSelect);
      const container = this.$refs.videoContainer;
      
      player = new EZUIKit.EZUIKitPlayer({
        id: 'video-container',
        accessToken: findItms.accessToken,
        url: findItms.address,
        template: 'pcLive',
        width: container.clientWidth,
        height: container.clientHeight,
        handleError: (error) => {
          console.error('handleError', error);
          this.$message.error('视频初始化失败');
        },
        env: {
          domain: 'https://open.ys7.com'
        }
      });
      
      // 自动播放
      player.play().then((data) => {
        console.log('视频播放成功', data);
      }).catch(error => {
        console.error('视频播放失败', error);
        this.$message.error('视频播放失败');
      });
      
      window.player = player;
    },
    
    // 播放方法
    play() {
      // 如果已经有播放器实例，先销毁
      if (player) {
        this.destroy();
      }
      
      // 初始化并播放视频
      this.initAndPlay();
    },
    
    // 停止方法
    stop() {
      if (player) {
        player.stop().then((data) => {
          console.log('视频停止成功', data);
        }).catch(error => {
          console.error('视频停止失败', error);
          this.$message.error('视频停止失败');
        });
      }
    },
    
    // 切换视频方法
    changeVideo(val) {
      console.log('切换视频:', val);
      // 停止当前视频
      this.stop();
      // 初始化并播放新视频
      this.initAndPlay();
    },
    
    // 销毁方法
    destroy() {
      if (player) {
        player.destroy();
        player = null;
      }
    },
    
    // BIM相关方法
    async bimToRealTime(sensorInfoList) {
      if (sensorInfoList === undefined || sensorInfoList.length === 0) {
        this.$message({ type: 'warning', message: "该构件没有对应传感器信息", duration: 2000 });
        return;
      }
      window.$Bus.$emit('bimToCharts', sensorInfoList)
    },
    
    // 其他原有方法保持不变
    tabClick() {
      // 原有实现
    },
    
    openWarningRecord() {
      this.recordVisible = true
    },
    
    openWarningExecute() {
      this.executeVisible = true
    },
    
    handleBegin() {
      // 原有实现
    },
    
    handleEnd() {
      // 原有实现
    },
    
    getColor(value) {
      // 原有实现
    },
    
    bringToFront(dialogRef) {
      // 原有实现
    },
    
    startDrag(e, dialogRef) {
      // 原有实现
    },
    
    handleDrag(e) {
      // 原有实现
    },
    
    stopDrag() {
      // 原有实现
    },
    
    handleSubmit() {
      // 原有实现
    },
    
    changeFlashMode() {
      // 原有实现
    }
  },
  beforeDestroy() {
    window.$Bus.$off('treeToBim')
    this.destroy()
  }
}
</script>

<style lang="scss" scoped>
@import "@/assets/styles/utils.scss";
@import "./components//index.scss";

::-webkit-scrollbar {
  width: vwpx(14px);
  height: vwpx(14px);
}

.bim {
  width: 100%;
  height: 100%;

  .body {
    height: calc(100% - #{vwpx(80px)}) !important;
    width: 100%;
    border: 1px solid #dcdfe6;
    border-top: none !important;
    padding: vwpx(10px);

    .video-box {
      height: 500px;
      width: 100%;
    }
  }

  ::v-deep {
    .el-tabs--border-card {
      box-shadow: none !important;
      border-bottom: none !important;
    }

    .el-tabs__item {
      width: vwpx(270px);
      height: vwpx(80px);
      padding: 0 !important;
      display: flex;
      justify-content: center;
      align-items: center;
      font-size: vwpx(30px);
      color: #303133;
      transition: 0.3s;
      flex-shrink: 0;
    }

    .el-tabs__item.is-active {
      color: #1890ff !important;
    }

    .el-tabs__header {
      background: #EFF5FF;
    }

    .el-tabs__nav {
      display: flex;
      flex-wrap: nowrap;
    }

    .el-tabs__nav-wrap::after {
      display: none;
    }

    .el-tabs__active-bar {
      display: none;
    }

    .el-tabs__content {
      height: 0 !important;
      padding: 0;
    }

    .el-carousel__container {
      background: transparent;
      height: 100%;
    }
  }

  .btns {
    display: flex;
    flex-wrap: nowrap;
    justify-content: flex-end;
    position: absolute;
    bottom: 4%;
    right: 2%;

    .btn {
      width: 11.25vmin;
      height: vwpx(68px);
      margin: 0px vwpx(20px);
      background: #005f9a;
      color: white;
      font-size: vwpx(32px);
      display: flex;
      justify-content: center;
      align-items: center;
    }
  }
}

.dark {
  ::-webkit-scrollbar-thumb {
    background-color: rgba(1, 102, 254, 0.4);
  }

  ::-webkit-scrollbar-track {
    background-color: rgba(0, 35, 94, .6);
  }

  .body {
    border: 1px solid #0166FE !important;
    border-top: none !important;
  }

  ::v-deep {
    .el-tabs__item {
      color: #fff !important;
    }

    .el-tabs__item.is-active {
      color: #0166FE !important;
      background: rgba(0, 35, 94, 0.6);
      border-right-color: transparent;
      border-left-color: transparent;
      box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.3);
    }

    .el-tabs--border-card {
      background: rgba(1, 102, 254, 0.15) !important;
      border: 1px solid #0166fe;
      border-bottom: none !important;
    }

    .el-tabs__header {
      background: rgba(1, 102, 254, 0.15) !important;
      border: none !important;
    }

    .el-tabs__nav-prev,
    .el-tabs__nav-next {
      line-height: 33px;
    }
  }
}

.model-box {
  width: 133.722222vmin;
  height: 84.944444vmin;
  background: #0b1c3a;
  border: 1px solid #1467ad;
  position: absolute;
  left: 15%;
  top: 10%;
  z-index: 9;
  cursor: default;

  .title {
    cursor: move;
    user-select: none;
    height: vwpx(70px);
    background: #003d5a;
    font-size: vwpx(40px);
    color: white;
    display: flex;
    align-items: center;
    padding-left: vwpx(40px);
    padding-right: vwpx(40px);
    justify-content: space-between;
    margin: 0px;

    i {
      cursor: pointer;
    }
  }

  .content {
    padding: 2%;
    width: 100%;
    height: 94.5%;
    position: relative;
  }
}

.tips {
  position: relative;

  img {
    width: 100%;
  }

  .mode-dispaly {
    font-size: 11vmin;
    line-height: 100%;
    position: absolute;
    width: 100%;
    height: 21%;
    position: absolute;
    margin-top: -57%;
    text-align: center
  }

  .display-ball {
    width: 11vmin;
    height: 11vmin;
    border-radius: 50%;
    position: absolute;
    margin-top: -57%;
    margin-left: calc(50% - 5.5vmin);
  }

  @keyframes blink {
    0%, 100% {
      opacity: 1;
    }
    50% {
      opacity: 0;
    }
  }

  .blink {
    animation: blink 0.5s infinite;
  }
}

.hello-ezuikit-js {
  height: 100%;
  width: 100%;
  
  .video-box {
    width: 100%;
    height: calc(100% - 100px);
  }
}
</style>