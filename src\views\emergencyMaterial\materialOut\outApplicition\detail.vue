<template>
  <el-row>
    <el-col :span="24">
      <el-row style="display: flex">
        <el-col :span="8" class="left-form">
          <el-form
            ref="elForm"
            :model="formData"
            :rules="rules"
            size="medium"
            label-width="90px"
            :disabled="readonly"
          >
            <el-col :span="24">
              <el-form-item label="编号" prop="code">
                <el-input v-model="formData.code" />
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="物资库" prop="materialId">
                <el-button
                  style="width: 100%; height: 36px; text-align: left"
                  @click="depotDialog = true"
                >
                  {{ formData.materialName }}
                </el-button>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="领用单位" prop="domainId">
                <select-tree
                  :key="'domainId'"
                  :dataRule="false"
                  v-model="formData.domainId"
                  :dept-type="100"
                  :expand-all="false"
                  clearable
                ></select-tree>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="联系人" prop="person">
                <!-- <el-cascader
                  v-model="formData.person"
                  :options="deptUserOptions"
                  :props="cascaderProps"
                  :show-all-levels="false"
                  ref="deptUser"
                  filterable
                  clearable
                ></el-cascader> -->
                <el-input v-model="formData.person"/>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="联系电话" prop="personTel">
                <el-input v-model="formData.personTel"> </el-input>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="出库时间" prop="outTime">
                <el-date-picker
                  clearable
                  v-model="formData.outTime"
                  type="datetime"
                  value-format="yyyy-MM-dd HH:mm:ss"
                  style="width: 100%"
                >
                </el-date-picker>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="备注" prop="remark">
                <el-input type="textarea" v-model="formData.remark"> </el-input>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="附件">
                <file-upload v-model="formData.fileId" :owner-id="formData.fileId" :forView="readonly"></file-upload>
              </el-form-item>
            </el-col>
          </el-form>
        </el-col>
        <el-col :span="16">
          <div
            style="
              display: flex;
              justify-content: flex-end;
              margin-bottom: 20px;
            "
          >
            <el-button type="primary" v-if="!readonly" @click="openDepotDetailDialog"
              >添加</el-button
            >
          </div>
          <el-table v-adjust-table :data="curSelectDepotInfo">
            <template v-for="(column, index) in columns">
              <el-table-column
                :label="column.label"
                v-if="column.visible"
                align="center"
                :prop="column.field"
                :width="column.width"
              >
                <template slot-scope="scope">
                  {{ scope.row[column.field] }}
                </template>
              </el-table-column>
            </template>
            <el-table-column label="出库数量">
              <template slot-scope="scope">
                <span v-if="readonly">{{ scope.row.quantity }}</span>
                <el-input-number
                  v-else
                  v-model="scope.row.quantity"
                  :controls="false"
                  :max="scope.row.kcQuantity"
                  style="width: 100%"
                ></el-input-number>
              </template>
            </el-table-column>
            <el-table-column label="用途">
              <template slot-scope="scope">
                <span v-if="readonly">{{ scope.row.useDesc }}</span>
                <el-input v-else v-model="scope.row.useDesc" />
              </template>
            </el-table-column>
            <el-table-column label="备注">
              <template slot-scope="scope">
                <span v-if="readonly">{{ scope.row.remark }}</span>
                <el-input v-else v-model="scope.row.remark" />
              </template>
            </el-table-column>
            <el-table-column label="操作" v-if="!readonly">
              <template slot-scope="scope">
                <el-button
                  type="text"
                  icon="el-icon-delete"
                  @click="removeDetailItem(scope)"
                  >移除</el-button
                >
              </template>
            </el-table-column>
          </el-table>
        </el-col>
      </el-row>
    </el-col>
    <el-col
      v-if="!readonly"
      :span="24"
      style="text-align: right; padding-right: 7.5px; margin-top: 18px"
    >
      <el-button type="primary" @click="onSave">保 存</el-button>
      <el-button @click="onClose">退 出</el-button>
    </el-col>

    <el-dialog
      title="选择物资库"
      append-to-body
      modal-append-to-body
      :visible.sync="depotDialog"
      width="70%"
      v-if="depotDialog"
    >
      <MaterialDepot
        :hasHandle="false"
        :filterColumn="filterColumn"
        tableHeight="470px"
        @depotSelect="depotSelect"
      ></MaterialDepot>
      <span slot="footer" class="dialog-footer">
        <el-button size="mini" type="primary" @click="confirmSelectDepot"
          >确 定</el-button
        >
        <el-button size="mini" @click="closeDepotDialog">退 出</el-button>
      </span>
    </el-dialog>
    <el-dialog
      title="选择物资"
      append-to-body
      modal-append-to-body
      :visible.sync="depotDetailDialog"
      width="70%"
      v-if="depotDetailDialog"
    >
      <MaterialDepotInfo
        :hasHandle="false"
        :filterColumn="filterDepotInfoColumn"
        tableHeight="470px"
        :rowData="curSelectDepot"
        @depotInfoSelect="depotInfoSelect"
      ></MaterialDepotInfo>
      <span slot="footer" class="dialog-footer">
        <el-button size="mini" type="primary" @click="confirmSelectDepotInfo"
          >确 定</el-button
        >
        <el-button size="mini" @click="closeDepotInfoDialog">退 出</el-button>
      </span>
    </el-dialog>
  </el-row>
</template>
<script>
import SelectTree from "@/components/DeptTmpl/selectTree.vue";
import {
  addMaterialOutApplicition,
  editMaterialOutApplicition,
  getDetailOutList,
} from "@/api/emergencyMaterial/materialOut";
import MaterialDepot from "@/views/emergencyMaterial/materialDepot/index.vue";
import MaterialDepotInfo from "@/views/emergencyMaterial/materialDepot/info.vue";
import { getTreeStruct } from "@/api/tmpl";
export default {
  name: "index",
  components: {
    SelectTree,
    MaterialDepot,
    MaterialDepotInfo,
  },
  data() {
    return {
      formData: {},
      loading: false,
      rules: {
        code: [{ required: true, message: "请输入编号", trigger: "blur" }],
        materialId: [
          { required: true, message: "请选择物资库", trigger: "blur" },
        ],
        domainId: [
          { required: true, message: "请选择领用单位", trigger: "blur" },
        ],
        person: [{ required: true, message: "请选择联系人", trigger: "blur" }],
        personTel: [
          { required: true, message: "请输入联系电话", trigger: "blur" },
        ],
        outTime: [
          { required: true, message: "请选择出库时间", trigger: "change" },
        ],
      },
      columns: [
        {
          key: 0,
          width: 100,
          field: "typeName",
          label: `物资类型`,
          visible: true,
        },
        {
          key: 1,
          width: 100,
          field: "assetsName",
          label: `物资名称`,
          visible: true,
        },
        {
          key: 3,
          field: "model",
          label: `规格型号`,
          visible: true,
        },
        {
          key: 4,
          width: 100,
          field: "unit",
          label: `单位`,
          visible: true,
        },
        {
          key: 5,
          field: "bpQuantity",
          label: `标配数量`,
          visible: true,
        },
        {
          key: 6,
          width: 100,
          field: "kcQuantity",
          label: `库存数量`,
          visible: true,
        },
      ],
      depotDialog: false, //选则物资库弹窗
      filterColumn: ["struct", "isNew", "describe", "area", "remark"],
      tempSelectDepot: null,
      curSelectDepot: null, //当前选中物资库
      depotDetailDialog: false, // 选择物资弹窗
      filterDepotInfoColumn: [],
      tempSelectDepotInfo: [], // 当前勾选物资
      curSelectDepotInfo: [], // 当前勾选物资
      deptUserOptions: [],
      cascaderProps: {
        multiple: false, //是否多选
        value: "id",
        emitPath: false,
      },
    };
  },
  props: {
    rowData: {
      type: Object,
      default: () => {
        return {};
      },
    },
    readonly: {
      type: Boolean,
      default: false
    }
  },
  watch: {
    rowData: {
      handler(val) {
        if (val.id) {
          getDetailOutList(val.id).then((res) => {
            this.formData = res.data;
            this.formData.person = String(this.formData.person);
            this.curSelectDepotInfo = this.formData.outboundDetailList;
            this.curSelectDepot = { id: this.formData.materialId };
          });
        }
      },
      immediate: true,
    },
  },
  created() {
    this.getDeptTreeDef();
  },
  mounted() {},
  methods: {
    getDeptTreeDef() {
      getTreeStruct({ types: 111 }).then((response) => {
        this.deptUserOptions = response.data;
      });
    },
    depotSelect(row) {
      this.tempSelectDepot = row;
    },
    closeDepotDialog() {
      this.depotDialog = false;
    },
    confirmSelectDepot() {
      this.curSelectDepot = this.tempSelectDepot;
      this.formData.materialId = this.curSelectDepot.id;
      this.formData.materialName = this.curSelectDepot.name;
      this.closeDepotDialog();
    },
    openDepotDetailDialog() {
      if (!this.formData.materialId) {
        this.$message.info("请先选择物资库");
        return;
      }
      this.depotDetailDialog = true;
    },

    depotInfoSelect(rows) {
      this.tempSelectDepotInfo = rows;
      console.log(this.tempSelectDepotInfo);
    },
    closeDepotInfoDialog() {
      this.tempSelectDepotInfo = [];
      this.depotDetailDialog = false;
    },
    confirmSelectDepotInfo() {
      this.tempSelectDepotInfo.forEach((item) => {
        item.bpQuantity = item.quantity;
        delete item.quantity;
        item.kcQuantity = Number(item.stock);
        delete item.id;
      });
      const filterSelectDepotInfo = this.tempSelectDepotInfo.filter(item=> !this.curSelectDepotInfo.some(value=> value.assetsId === item.assetsId))
      this.curSelectDepotInfo = [
        ...this.curSelectDepotInfo,
        ...filterSelectDepotInfo,
      ];
      this.closeDepotInfoDialog();
    },
    removeDetailItem(scope) {
      this.curSelectDepotInfo.splice(scope.$index, 1);
    },
    onSave() {
      this.$refs.elForm.validate((valid) => {
        if (!valid) return;
        if (
          this.formData.fileId &&
          Array.isArray(this.formData.fileId) &&
          this.formData.fileId.length > 0
        ) {
          this.formData.fileId = this.formData.fileId[0];
        }
        this.formData.outboundDetailList = this.curSelectDepotInfo;
        if (this.formData.id) {
          editMaterialOutApplicition(this.formData).then(() => {
            this.$modal.msgSuccess("保存成功");
            this.onClose();
          });
        } else {
          addMaterialOutApplicition(this.formData).then(() => {
            this.$modal.msgSuccess("保存成功");
            this.onClose();
          });
        }
      });
    },

    onClose() {
      this.$emit("close");
    },
  },
};
</script>
<style scoped lang="scss">
.card_title {
  width: 200px;
  text-align: left;
  margin-bottom: 15px;
  font-weight: bold;
}

::v-deep .el-form-item {
  margin-bottom: 12px;
}

.left-form {
  margin-right: 10px;
  padding-right: 10px;
  border-right: solid 1px #eee;
}

::v-deep .dialog-footer {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  width: 100%;
}
</style>
<style lang="scss" scoped>
@import "@/assets/styles/business.scss";
</style>
