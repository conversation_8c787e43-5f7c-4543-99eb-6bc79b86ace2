<template>
  <div class="app-container">
    <el-row :gutter="20">

      <!--筛选区开始-->
      <el-col :span="24" :xs="24">
        <el-row>
          <el-col :span="24">
            <el-form ref="queryForm" :inline="true" :model="queryParams" label-width="68px" size="small">

              <el-form-item label="" prop="libName">
                <el-input
                    v-model="queryParams.libName"
                    clearable
                    placeholder="请输入名称"
                    prefix-icon="el-icon-user"
                    style="width: 240px"
                    @keyup.enter.native="handleQuery"
                />
              </el-form-item>
              <el-form-item label="" prop="domainName">
                <selectTree v-model="queryParams.domainIdStr" :deptType="100" :deptTypeList="[1, 3, 4]" placeholder="权属单位"/>
              </el-form-item>
              <el-form-item label="" prop="beginTime">
                <el-date-picker v-model="queryParams.beginTime"
                                clearable
                                placeholder="请选择起始时间"
                                type="date"
                                value-format="yyyy-MM-dd">
                </el-date-picker>
              </el-form-item>
              <el-form-item label="" prop="endTime">
                <el-date-picker v-model="queryParams.endTime"
                                clearable
                                placeholder="请选择终止时间"
                                type="date"
                                value-format="yyyy-MM-dd">
                </el-date-picker>
              </el-form-item>
              <el-form-item>
                <el-button icon="el-icon-search" size="mini" type="primary" @click="handleQuery">搜索</el-button>
                <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
              </el-form-item>
            </el-form>
            <!--默认折叠-->
          </el-col>
        </el-row>
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button
                icon="el-icon-plus"
                size="mini"
                type="primary"
                v-has-menu-permi="['contract:lib:add']"
                @click="handleAdd"
            >新增
            </el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button
              icon="el-icon-copy-document"
              size="mini"
              type="success"
              v-has-menu-permi="['contract:lib:copy']"
              @click="handleCopy"
            >复制报价体系
            </el-button>
          </el-col>
          <right-toolbar
              :columns="columns"
              :showSearch.sync="showSearch"
              @queryTable="handleQuery"
          ></right-toolbar>
        </el-row>
        <!--筛选区结束-->
        <!--数据表格开始-->
        <div class="tableDiv">
          <el-table v-adjust-table v-loading="loading" :data="libList"
                    @row-click="handleRowClick"
                    highlight-current-row
                    :height="showSearch ? 'calc(100vh - 320px)' : 'calc(100vh - 260px)'"
                    border size="mini" style="width: 100%">
            <el-table-column align="center" label="序号" type="index" width="50">
            </el-table-column>
            <el-table-column align="center" label="名称" prop="libName"/>
            <el-table-column align="center" label="描述" prop="libDesc"/>
            <el-table-column align="center" label="起始时间" prop="beginTime" width="180">
              <template slot-scope="scope">
                <span>{{ parseTime(scope.row.beginTime, '{y}-{m}-{d}') }}</span>
              </template>
            </el-table-column>
            <el-table-column align="center" label="终止时间" prop="endTime" width="180">
              <template slot-scope="scope">
                <span>{{ parseTime(scope.row.endTime, '{y}-{m}-{d}') }}</span>
              </template>
            </el-table-column>
            <el-table-column align="center" label="备注" prop="remark"/>
            <el-table-column align="center" label="权属单位" prop="domainName"/>
            <el-table-column
                align="center"
                class-name="small-padding fixed-width"
                fixed="right"
                label="操作"
                width="160"
            >
              <template v-if="scope.row.userId !== 1" slot-scope="scope">
                <el-button
                    size="mini"
                    type="text"
                    icon="el-icon-edit"
                    v-has-menu-permi="['contract:lib:edit']"
                    @click="handleUpdate(scope.row)"
                >修改
                </el-button>
                <el-button
                    size="mini"
                    type="text"
                    icon="el-icon-delete"
                    v-has-menu-permi="['contract:lib:delete']"
                    @click="handleDelete(scope.row)"
                >删除
                </el-button>
                <el-button
                    icon="el-icon-view"
                    size="mini"
                    type="text"
                    @click="openDrawer(scope.row)"
                >报价方法
                </el-button>
              </template>
            </el-table-column>
          </el-table>

          <pagination
              v-show="total>0"
              :limit.sync="queryParams.pageSize"
              :page.sync="queryParams.pageNum"
              :total="total"
              @pagination="getList"
          />
        </div>
        <!--数据表格结束-->
      </el-col>
    </el-row>
    <!-- 添加或修改报价体系基本信息对话框 -->
    <el-dialog :title="title" :visible.sync="open" append-to-body width="70%">
      <el-row>
        <el-form ref="form" :model="form" :rules="rules" label-width="120px">

          <el-col :span="12">
            <el-form-item label="名称" prop="libName" :style="{ width: '100%' }">
              <el-input v-model="form.libName" placeholder="请输入名称"/>
            </el-form-item>
          </el-col>

          <el-col :span="12">
            <el-form-item label="权属单位" prop="domainId" :style="{ width: '100%' }">
              <selectTree v-model="form.domainId" :dept-type="100"/>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="起始时间" prop="beginTime" :style="{ width: '100%' }">
              <el-date-picker
                  v-model="form.beginTime"
                  format="yyyy-MM-dd"
                  value-format="yyyy-MM-dd HH:mm:ss"
                  :style="{ width: '100%' }"
                  placeholder="请输入起始时间"
                  :picker-options="planStartPickerOptions"
                  clearable
              ></el-date-picker>
            </el-form-item>
          </el-col>


          <el-col :span="12">
            <el-form-item label="终止时间" prop="endTime" :style="{ width: '100%' }">
              <el-date-picker
                  v-model="form.endTime"
                  format="yyyy-MM-dd"
                  value-format="yyyy-MM-dd HH:mm:ss"
                  :style="{ width: '100%' }"
                  placeholder="请输入终止时间"
                  :picker-options="planEndPickerOptions"
                  clearable
              ></el-date-picker>
            </el-form-item>
          </el-col>


          <el-col :span="24">
            <el-form-item label="备注" prop="remark" :style="{ width: '100%' }">
              <el-input v-model="form.remark" placeholder="请输入内容" type="textarea"/>
            </el-form-item>
          </el-col>

          <el-col :span="24">
            <el-form-item label="描述" prop="libDesc" :style="{ width: '100%' }">
              <el-input v-model="form.libDesc" placeholder="请输入内容" type="textarea"/>
            </el-form-item>
          </el-col>

        </el-form>
        <el-col>
          <div class="dialog-footer" style="text-align: right">
            <el-button type="primary" @click="submitForm">确 定</el-button>
            <el-button @click="cancel">取 消</el-button>
          </div>
        </el-col>
      </el-row>
    </el-dialog>
    <el-drawer :visible.sync="drawer" size="70%" title="报价方法" :destroy-on-close="true">
      <detail :lib="lib"/>
    </el-drawer>
  </div>
</template>

<script>
import {addLib, delLib, listByParam, updateLib, saveBatch, copyLib} from "@/api/contract/quotationSystem.js";
import selectTree from "@/components/DeptTmpl/selectTree.vue";

import Detail from "./detail.vue";

export default {
  name: "QuotationSystem",
  components: {Detail, selectTree},
  data() {
    return {
      // 遮罩层
      loading: true,
      // 显示搜索条件
      showSearch: false,
      dictType: [],
      lib: {},
      // 总条数
      total: 0,
      // 报价体系基本信息表格数据
      libList: null,
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 表单参数
      form: {},
      rules: {
        libName: [
          { required: true, message: '请输入名称', trigger: 'blur' }
        ],
        domainId: [
          { required: true, message: '请选择权属单位', trigger: 'change' }
        ],
        beginTime: [
          { required: true, message: '请选择起始时间', trigger: 'change' }
        ],
        endTime: [
          { required: true, message: '请选择终止时间', trigger: 'change' }
        ]
      },
      drawer: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 50,
        libName: null,
        libDesc: null,
        beginTime: null,
        endTime: null,
        producerNo: null,
        schemeType: null,
        defaultLib: null,
        isLock: null,
        domainId: null,
        status: null,
      },
      // 列信息
      columns: [
        {key: 0, label: `名称`, visible: true},
        {key: 1, label: `描述`, visible: true},
        {key: 2, label: `起始时间`, visible: true},
        {key: 3, label: `终止时间`, visible: true},
        {key: 5, label: `备注`, visible: true},
        {key: 9, label: `权属单位id`, visible: true},
        {key: 11, label: `删除标志（0代表存在，1代表删除）`, visible: true}
      ],
      planStartPickerOptions: this.beginTime(),
      planEndPickerOptions: this.processDate(),
      row: {}
    };
  },
  watch: {
    // 根据名称筛选部门树
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询用户列表 */
    getList() {
      this.loading = true;
      this.queryParams.domainId = parseInt(this.queryParams.domainIdStr)
      listByParam(this.queryParams).then(response => {
        this.libList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 表单重置
    reset() {
      this.form = {
        libName: null,
        libDesc: null,
        beginTime: null,
        endTime: null,
        producerNo: null,
        remark: null,
        schemeType: null,
        defaultLib: null,
        isLock: null,
        domainIdStr: null,
        domainName: null,
        status: null,
        delFlag: null
      };
      this.resetForm("form");
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加报价体系基本信息";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      this.form = JSON.parse(JSON.stringify(row));
      this.form.domainId = String(row.domainId)
      this.title = "修改报价体系基本信息";
      this.open = true;

    },
    /** 提交按钮 */
    submitForm: function () {
      this.$refs["form"].validate(valid => {
        if (valid) {
          this.form.domainName = undefined
          if (this.form.oldLibId != null) {
            copyLib(this.form).then(response => {
              this.$modal.msgSuccess("保存成功");
              this.open = false;
              this.getList();
            });
          } else if (this.form.id != null) {
            updateLib(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addLib(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              // 新增完后需要初始化两条根节点
              const prices = [
                {
                  libId: response.data,
                  schemeType: '专项养护',
                  nodeType: 1,
                  parentId: 0,
                  rootId: 0,
                  safetyFeeFlag: 2,
                  rateFlag: 2,
                  schemeCode: '专项养护',
                  schemeName: '专项养护'
                },
                {
                  libId: response.data,
                  schemeType: '日常养护',
                  nodeType: 1,
                  parentId: 0,
                  rootId: 0,
                  safetyFeeFlag: 2,
                  rateFlag: 2,
                  schemeCode: '日常养护',
                  schemeName: '日常养护'
                }
              ]
              saveBatch(prices).then(res => {
                this.open = false;
                this.getList();
              })
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const id = row.id || this.ids;
      this.$modal.confirm('是否确认删除？').then(function () {
        return delLib(id);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {
      });
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dateRange = [];
      this.queryParams = {
        pageNum: 1,
        pageSize: 50,
        libName: null,
        libDesc: null,
        beginTime: null,
        endTime: null,
        producerNo: null,
        schemeType: null,
        defaultLib: null,
        isLock: null,
        domainId: null,
        status: null,
      }
      this.handleQuery();
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    openDrawer(e) {
      this.lib = e
      this.drawer = true
    },
    handleRowClick(e) {
      this.row = e
    },
    handleCopy() {
      if (!this.row.id) {
        this.$modal.msgError("请选择要复制的报价体系");
        return;
      }
      this.reset();
      this.form.oldLibId = this.row.id
      this.open = true;
      this.title = "添加报价体系基本信息";
    },
    beginTime() {
      const self = this;
      return {
        disabledDate(time) {
          if (self.form.endTime) {
            //如果结束时间不为空，则小于结束时间
            return new Date(self.form.endTime).getTime() < time.getTime();
          } else {
            return false;
          }
        },
      };
    },
    processDate() {
      const self = this;
      return {
        disabledDate(time) {
          if (self.form.beginTime) {
            //如果开始时间不为空，则结束时间大于开始时间
            return new Date(self.form.beginTime).getTime() > time.getTime();
          } else {
            return false;
          }
        },
      };
    },
  }
};
</script>
<style>
.hasTagsView .app-main[data-v-078753dd] {
  background: #f5f7fa;
}

.tableDiv {
  background-color: white;
  padding-bottom: 10px;
}
</style>
<style lang="scss" scoped>
@import "@/assets/styles/business.scss";
</style>
