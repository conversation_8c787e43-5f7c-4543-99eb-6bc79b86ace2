<template>
  <div class="detail-table">
    <el-dialog title="附属设施详情" :visible.sync="showTable" width="65%" style="width: 100%;height: 80vh;" append-to-body
      :before-close="handleClose" :close-on-click-modal="true">
      <el-table v-loading="loading" ref="table" height="250px" border :data="tableData" :row-style="rowStyle"
        @selection-change="handleSelectionChange" @row-click="handleRowClick">
        <el-table-column type="selection" width="50" align="center" />
        <el-table-column fixed label="序号" type="index" width="50" align="center">
          <template v-slot="scope">
            {{
              scope.$index +
              (queryParams.pageNum - 1) * queryParams.pageSize +
              1
            }}
          </template>
        </el-table-column>
        <el-table-column label="资产子类" align="center" prop="typeName" min-width="140" show-overflow-tooltip>

        </el-table-column>
        <el-table-column label="资产编码" align="center" prop="assetCode" min-width="140" show-overflow-tooltip />
        <el-table-column label="管理处" align="center" prop="managementMaintenanceName" min-width="140"
          show-overflow-tooltip />
        <el-table-column label="管养分处" align="center" prop="managementMaintenanceBranchName" min-width="140"
          show-overflow-tooltip />
        <el-table-column label="养护路段" align="center" prop="maintenanceSectionName" min-width="140"
          show-overflow-tooltip />
        <el-table-column label="路线编码" align="center" prop="routeCode" min-width="140" show-overflow-tooltip />
        <el-table-column label="运营状态" align="center" prop="operationStateName" min-width="130" show-overflow-tooltip>
          <template slot-scope="{ row }">
            <el-link :underline="false"
              :type="{ 1: 'info', 2: 'success', 3: 'danger', 4: 'primary' }[row.operationState]">
              <DictTag :value="row.operationState" :options="dict.type.sys_operation_state" />
            </el-link>
          </template>
        </el-table-column>
        <el-table-column label="桩号范围" align="center" min-width="200" show-overflow-tooltip>
          <template slot-scope="scope">
            {{ formatPile(scope.row.startStake) }} ~ {{ formatPile(scope.row.endStake) }}
          </template>
        </el-table-column>
        <el-table-column label="方向" align="center" prop="direction" min-width="140" show-overflow-tooltip>
          <template slot-scope="scope">
            <dict-tag :options="dict.type.sys_route_direction" :value="scope.row.direction" />
          </template>
        </el-table-column>
        <el-table-column label="位置" align="center" prop="lane" min-width="140" show-overflow-tooltip>
          <template slot-scope="scope">
            <dict-tag :options="dict.type.lane" :value="scope.row.lane" />
          </template>
        </el-table-column>
        <el-table-column label="经度" align="center" min-width="140" show-overflow-tooltip prop="longitude">

        </el-table-column>
        <el-table-column label="纬度" align="center" min-width="140" show-overflow-tooltip prop="latitude">
        </el-table-column>
        <el-table-column label="施工里程桩号" align="center" prop="constructionStake" min-width="140" show-overflow-tooltip>
          <template slot-scope="scope">
            {{ formatPile(scope.row.constructionStake) }}
          </template>
        </el-table-column>
        <el-table-column label="统一里程桩号" align="center" prop="unifiedMileageStake" min-width="140" show-overflow-tooltip>
          <template slot-scope="scope">
            {{ formatPile(scope.row.unifiedMileageStake) }}
          </template>
        </el-table-column>
        <el-table-column v-for="item in tableHead" :key="item.id" :label="item.alias" align="center"
          :prop="item.columnName" min-width="140" show-overflow-tooltip />
        <el-table-column label="图片" align="center">
          <template #default="{ row }">
            <el-link :underline="false" type="primary" :disabled="row.picPath ? false : true"
              @click.stop="previewImg(row)">查看</el-link>
          </template>
        </el-table-column>
      </el-table>
      <pagination :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize"
        :pageSizes="[10, 20, 30, 50, 100, 1000]" @pagination="getList" />
      <div slot="footer">
        <el-button @click="handleClose">取 消</el-button>
      </div>
    </el-dialog>

  </div>
</template>

<script>
import {
  getListPage,

  getAssetSubclass,
  getDynamicData
} from '@/api/baseData/facility/baseInfo/index'
import { getTotalStatistics } from '@/api/baseData/tunnel/baseInfo/index'
import CascadeSelection from '@/components/CascadeSelection/index.vue'
import rangeInput from '@/views/baseData/components/rangeInput/index.vue'

import { statusListDialog } from '@/views/baseData/components/statusDialog/list.js'
import Dialog from '@/components/Dialog/index.vue'

export default {
  name: 'ancillary-baseInfo',
  components: {
    CascadeSelection,
    rangeInput,
    Dialog

  },
  dicts: ['sys_route_type', 'sys_operation_state', 'sys_route_direction', 'lane'],
  props: {
    showTable: { type: Boolean, default: false },
    row: { type: undefined, default: '' },
    params: { type: undefined, default: '' }
  },
  data() {
    return {
      loading: true,
      showAddEdit: false,
      forView: false,
      title: '',

      assetSubclassList: [],
      tableHead: [],
      ids: [],
      aseetIds: [],
      total: 0,
      tableData: [],
      queryParams: {
        pageNum: 1,
        pageSize: 20,
        mainTypeId: 8,
      },
      imgShow: false,
      imageUrl: '',


    }
  },
  watch: {},
  created() {
    this.getList()
    this.getAssetSubclassList()
  },
  methods: {
    // 获取表格数据
    getList() {
      this.loading = true
      delete this.params.pageNum
      delete this.params.pageSize
      if (this.row.maintenanceSectionName == "小计") {
        this.queryParams.statisticsType = 1

        getTotalStatistics({ ...this.queryParams, ...this.params })
          .then(response => {
            this.tableData = response.rows
            this.total = response.total
            this.loading = false
          })
          .catch(() => {
            this.loading = false
          })

      } else if (this.row.maintenanceSectionName == "总计") {
        this.queryParams.statisticsType = 2
        getTotalStatistics({ ...this.queryParams, ...this.params })
          .then(response => {
            this.tableData = response.rows
            this.total = response.total
            this.loading = false
          })
          .catch(() => {
            this.loading = false
          })

      } else {
        getListPage({ ...this.queryParams, ...this.params })
          .then(response => {
            this.tableData = response.rows
            this.total = response.total
            this.loading = false
          })
          .catch(() => {
            this.loading = false
          })
      }



    },
    //查询字长子类
    getAssetSubclassList() {
      getAssetSubclass({ mainTypeId: 8 }).then(res => {
        this.assetSubclassList = res
      })
    },
    handleClose() {
      this.$emit('close')
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.aseetIds = selection.map(item => item.aseetId)
    },
    // 表格点击勾选
    handleRowClick(row) {
      row.isSelected = !row.isSelected
      this.$refs.table.toggleRowSelection(row)
    },
    // 勾选高亮
    rowStyle({ row, rowIndex }) {
      if (this.ids.includes(row.id)) {
        return { 'background-color': '#b7daff', color: '#333' }
      } else {
        return { 'background-color': '#fff', color: '#333' }
      }
    },


    // 查看图片
    previewImg(row) {
      this.imgShow = true
      this.imageUrl = row.picPath
    },

    getDynamicList() {

      getDynamicData({ mainTypeId: this.queryParams.mainTypeId, typeId: this.params.typeId }).then(res => {
        if (res.code == 200) {
          this.tableHead = res.data
        }
      })


    },


    getDynamicSelect(val) {

      let data = this.assetSubclassList.find(item => item.id == this.queryParams.typeId)
      this.queryParams.mainTypeId = data.mainType
    },


    // 表格操作-运营状态
    handleOperational(event, row) {
      event.stopPropagation()
      statusListDialog({ dataId: row.id, baseDataType: 24 })
    },

  }
}
</script>

<style lang="scss" scoped>
.detail-table {
  ::v-deep .el-table .el-table__fixed-body-wrapper {
    top: 36px !important;
  }
}
</style>