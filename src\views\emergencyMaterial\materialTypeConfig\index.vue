<template>
  <div class="material-type-box app-container maindiv" @click="resetShowMenu">
    <div class="left-tree">
      <div class="left-title">物资类型</div>
      <el-tree
        ref="typeTree"
        :data="treeData"
        node-key="id"
        :expand-on-click-node="false"
        :props="treeProps"
        @node-click="treeNodeClick"
        highlight-current
        default-expand-all
        @node-contextmenu="openTreeMenu"
      >
        <div class="tree-label" slot-scope="{ node, data }">
          <span>{{ node.label }}</span>
          <div class="select-popover" v-if="data.isShow">
            <span @click.stop="openTreeDetail('add',data)">新增</span>
            <template v-if="data.parentId != 0">
              <span @click.stop="openTreeDetail('edit', data)">编辑</span>
              <span @click.stop="handlerDelTreeDetail(data)">删除</span>
            </template>
          </div>
        </div>
      </el-tree>
    </div>
    <div class="right-container">
      <el-form
        ref="queryForm"
        :inline="true"
        :model="queryParams"
        label-width="120px"
        size="small"
      >
        <el-form-item>
          <el-input
            v-model="queryParams.name"
            placeholder="物资名称"
            style="width: 240px"
          ></el-input>
        </el-form-item>
        <el-form-item>
          <el-input
            v-model="queryParams.code"
            placeholder="物资编码"
            style="width: 240px"
          ></el-input>
        </el-form-item>
        <el-form-item>
          <el-select
            v-model="queryParams.isStandard"
            placeholder="是否为标准物资"
            style="width: 240px"
            clearable
          >
            <el-option
              v-for="item in standardOptions"
              :label="item.label"
              :value="item.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button
            icon="el-icon-search"
            size="mini"
            type="primary"
            :loading="loading"
            @click="handleQuery"
            >搜索</el-button
          >
          <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
            >重置</el-button
          >
        </el-form-item>
      </el-form>
      <el-row :gutter="10" class="mb8">
        <el-col :span="1.5">
          <el-button
            icon="el-icon-plus"
            size="mini"
            type="primary"
            @click="openDetailDialog(null)"
            >新增
          </el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button
            icon="el-icon-view"
            size="mini"
            type="warning"
            @click="exportList"
            >导出
          </el-button>
        </el-col>
      </el-row>
      <el-table v-adjust-table stripe :data="tableData" v-loading="loading" class="mb8" :height="'calc(100vh - 270px)'" size="mini" border>
        <el-table-column align="center" label="序号" type="index" width="50" />
        <template v-for="(column, index) in columns">
          <el-table-column
            v-if="column.visible"
            :label="column.label"
            :prop="column.field"
            :width="column.width"
            align="center"
          >
            <template slot-scope="scope">
              <dict-tag
                v-if="column.dict"
                :options="dict.type[column.dict]"
                :value="scope.row[column.field]"
              />
              <template v-else-if="column.slots">
                <RenderDom
                  :row="scope.row"
                  :index="index"
                  :render="column.render"
                />
              </template>
              <span v-else-if="column.isTime">{{
                parseTime(scope.row[column.field], "{y}-{m}-{d}")
              }}</span>
              <span v-else>{{ scope.row[column.field] }}</span>
            </template>
          </el-table-column>
        </template>
        <el-table-column
          align="center"
          class-name="small-padding fixed-width"
          label="操作"
          width="250"
        >
          <template slot-scope="scope">
            <el-button
              icon="el-icon-view"
              size="mini"
              type="text"
              @click="openDetailDialog(scope.row)"
              >编辑
            </el-button>
            <el-button
              icon="el-icon-view"
              size="mini"
              type="text"
              @click="handleDelete(scope.row)"
              >删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      <pagination
        v-show="total > 0"
        :limit.sync="queryParams.pageSize"
        :page.sync="queryParams.pageNum"
        :total="total"
        @pagination="handleQuery"
      />
    </div>
    <el-dialog
      :title="detailTitle"
      :visible.sync="detailDialog"
      width="500px"
      destroy-on-close
      v-if="detailDialog"
    >
      <el-form
        ref="detailFormRef"
        :model="detailForm"
        label-width="120px"
        :rules="rules"
      >
        <el-form-item label="物资编码" prop="code">
          <el-input v-model="detailForm.code" style="width: 300px" />
        </el-form-item>
        <el-form-item label="物资名称" prop="name">
          <el-input v-model="detailForm.name" style="width: 300px" />
        </el-form-item>
        <el-form-item label="规格型号" prop="model">
          <el-input v-model="detailForm.model" style="width: 300px" />
        </el-form-item>
        <el-form-item label="单位" prop="unit">
          <el-input v-model="detailForm.unit" style="width: 300px" />
        </el-form-item>
        <el-form-item label="标配数量" prop="quantity">
          <el-input v-model="detailForm.quantity" style="width: 300px" />
        </el-form-item>
        <el-form-item label="维护周期(月)" prop="repairCycle">
          <el-input v-model="detailForm.repairCycle" style="width: 300px" />
        </el-form-item>
        <el-form-item label="是否标准物资" prop="isStandard">
          <el-select v-model="detailForm.isStandard" style="width: 300px">
            <el-option
              v-for="item in standardOptions"
              :label="item.label"
              :value="item.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="备注">
          <el-input
            type="textarea"
            v-model="detailForm.remark"
            style="width: 300px"
          />
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="saveDetail">保 存</el-button>
        <el-button @click="closeDetailDialog">取 消</el-button>
      </span>
    </el-dialog>
    <el-dialog
      :title="treeDetailDialogTree"
      :visible.sync="treeDetailDialog"
      width="500px"
      destroy-on-close
      v-if="treeDetailDialog"
    >
      <el-form ref="treeDetailRef" :model="treeDetailForm" :rules="treeDetailRules" label-width="80px">
        <el-form-item label="类型编码" prop="code">
          <el-input v-model="treeDetailForm.code" />
        </el-form-item>
        <el-form-item label="类型名称" prop="name">
          <el-input v-model="treeDetailForm.name" />
        </el-form-item>
        <el-form-item label="顺序" prop="grade">
          <el-input-number v-model="treeDetailForm.grade" :controls="false" style="width: 100%;text-align: left;"></el-input-number>
        </el-form-item>
        <el-form-item label="备注">
          <el-input type="textarea" v-model="treeDetailForm.remark" />
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="saveTreeDetail">保 存</el-button>
        <el-button @click="treeDetailDialog=false">取 消</el-button>
      </span>
    </el-dialog>
  </div>
</template>
<script>
import {
  getMaterialTypeTree,
  getMaterialList,
  addMaterial,
  editMaterial,
  delMaterial,
  addTreeDetail,
  editTreeDetail,
  delTreeDetail
} from "@/api/emergencyMaterial/materialType";
import { cloneDeep } from "lodash";
export default {
  components: {
    RenderDom: {
      functional: true,
      props: {
        row: Object,
        index: Number,
        render: Function,
      },
      render(createElement, ctx) {
        const { row, index } = ctx.props;
        return ctx.props.render(row, index);
      },
    },
  },
  data() {
    return {
      treeProps: {
        label: "name",
      },
      treeData: [],
      curSelectTree: null,
      loading: false,
      queryParams: {
        pageSize: 20,
        pageNum: 1,
      },
      standardOptions: [
        {
          label: "是",
          value: 1,
        },
        {
          label: "否",
          value: 0,
        },
      ],
      tableData: [],
      total: 0,
      columns: [
        {
          key: 1,
          field: "code",
          label: `物资编码`,
          visible: true,
        },
        {
          key: 2,
          field: "name",
          label: `物资名称`,
          visible: true,
        },
        {
          key: 3,
          field: "model",
          label: `规格型号`,
          visible: true,
        },
        {
          key: 4,
          field: "unit",
          label: `单位`,
          visible: true,
        },
        {
          key: 5,
          field: "quantity",
          label: `标配数量`,
          visible: true,
        },
        {
          key: 6,
          field: "repairCycle",
          label: `维护周期(月)`,
          visible: true,
        },
        {
          key: 7,
          field: "isStandard",
          label: `是否标准物资`,
          visible: true,
          slots: true,
          render: (row, index) => {
            return <span>{row.isStandard == 1 ? "是" : "否"}</span>;
          },
        },
        {
          key: 8,
          field: "remark",
          label: `备注`,
          visible: true,
        },
      ],
      detailTitle: "新增",
      detailDialog: false,
      detailForm: {},
      rules: {
        code: [
          { required: true, message: "请填写物资编码", trigger: "change" },
        ],
        name: [
          { required: true, message: "请填写物资名称", trigger: "change" },
        ],
        model: [
          { required: true, message: "请填写规格型号", trigger: "change" },
        ],
        unit: [{ required: true, message: "请填写单位", trigger: "change" }],
        quantity: [
          { required: true, message: "请填写标配数量", trigger: "change" },
        ],
        repairCycle: [
          { required: true, message: "请填写维护周期", trigger: "change" },
        ],
        isStandard: [
          {
            required: true,
            message: "请选择是否为标准物资",
            trigger: "change",
          },
        ],
      },
      treeDetailDialog: false,
      treeDetailDialogType: '',
      treeDetailForm: {},
      treeDetailRules: {
        code: [{required: true, message: '请输入类型编码', trigger: 'change'}],
        name: [{required: true, message: '请输入类型名称', trigger: 'change'}],
        grade: [{required: true, message: '请输入顺序', trigger: 'change'}],
      }
    };
  },
  computed: {
    treeDetailDialogTree() {
      return this.treeDetailDialogType === 'add' ? '新增物资类型' : '编辑物资类型'
    }
  },
  created() {
    this.getTypeTree();
    this.handleQuery();
  },
  methods: {
    getTypeTree() {
      getMaterialTypeTree().then((res) => {
        const cloneData = this.addIsShowField(cloneDeep(res.data))
        this.treeData = [cloneData]
      });
    },
    addIsShowField(data) {
      data.isShow = false;
      if (data.children && Array.isArray(data.children)) {
        data.children.forEach(child => this.addIsShowField(child));
      }
      return data;
    },
    openTreeMenu(event, tree) {
      this.resetShowMenu()
      this.$set(tree, 'isShow', true)
    },
    resetShowMenu() {
      this.addIsShowField(this.treeData[0])
    },
    handleQuery() {
      if (this.curSelectTree) {
        this.queryParams.type = this.curSelectTree.id;
      }
      if (this.queryParams.isStandard === "") {
        delete this.queryParams.isStandard;
      }
      this.loading = true;
      getMaterialList(this.queryParams)
        .then((res) => {
          this.tableData = res.rows || [];
          this.total = res.total || 0;
        })
        .finally(() => {
          this.loading = false;
        });
    },
    treeNodeClick(data) {
      this.curSelectTree = data;
      this.handleQuery();
    },
    openTreeDetail(type, data) {
      this.treeDetailDialogType = type
      if (type === 'edit') {
        this.treeDetailForm.code = data.code
        this.treeDetailForm.name = data.name
        this.treeDetailForm.grade = data.grade
        this.treeDetailForm.remark = data.remark
        this.treeDetailForm.id = data.id
      } else {
        this.treeDetailForm = {
          parentId: data.id
        }
      }
      this.treeDetailDialog = true
    },
    saveTreeDetail() {
      this.$refs.treeDetailRef.validate((valid)=> {
        if (valid) {
          if (this.treeDetailDialogType === 'add') {
            addTreeDetail(this.treeDetailForm).then(()=> {
              this.$message.success('操作成功')
              this.treeDetailDialog = false;
              this.resetShowMenu()
              this.getTypeTree();
            })
          } else {
            editTreeDetail(this.treeDetailForm).then(()=> {
              this.$message.success('操作成功')
              this.treeDetailDialog = false;
              this.resetShowMenu()
              this.getTypeTree();
            })
          }
        }
      })
    },
    handlerDelTreeDetail(data) {
      this.$modal.confirm("是否删除").then(() => {
        delTreeDetail(data.id).then(() => {
          this.resetShowMenu()
          this.getTypeTree();
        });
      });
    },
    exportList() {
      this.download(
        "manager/emergency/material/assets/export",
        { ...this.queryParams },
        `物资仓库资产列表_${new Date().getTime()}.xlsx`,
        {
          headers: { "Content-Type": "application/json;" },
          parameterType: "body",
        }
      );
    },
    saveDetail() {
      if (this.curSelectTree) {
        this.detailForm.type = this.curSelectTree.id;
      }
      this.$refs.detailFormRef.validate((valid) => {
        if (valid) {
          if (this.detailForm.id) {
            editMaterial(this.detailForm).then((res) => {
              this.$message.success("操作成功");
              this.closeDetailDialog();
            });
          } else {
            addMaterial(this.detailForm).then((res) => {
              this.$message.success("操作成功");
              this.closeDetailDialog();
            });
          }
        }
      });
    },
    openDetailDialog(row) {
      if (!row && (!this.curSelectTree || this.curSelectTree.parentId == 0)) {
        this.$message.warning("根节点无法添加物资");
        return;
      }
      this.detailTitle = row ? "编辑" : "新增";
      this.detailForm = row || {};
      this.detailDialog = true;
    },
    closeDetailDialog() {
      this.detailForm = {};
      this.detailDialog = false;
      this.handleQuery();
    },
    handleDelete(row) {
      this.$modal.confirm("是否删除").then(() => {
        delMaterial(row.id).then((res) => {
          this.handleQuery();
        });
      });
    },
    resetQuery() {
      this.queryParams = {
        isStandard: "",
        pageSize: 20,
        pageNum: 1,
      };
      this.handleQuery()
    },
  },
};
</script>
<style scoped lang="scss">
.material-type-box {
  display: flex;
  width: 100%;
  height: 100%;
  padding: 20px;
  background-color: #fff;

  .left-tree {
    width: 180px;
    flex-shrink: 0;
    border-right: solid 1px #eee;

    .left-title {
      padding: 0 10px 10px;
    }

    .tree-label {
      position: relative;
      width: 100%;
      font-size: 14px;
    }

    .select-popover {
      position: absolute;
      top: 0;
      right: 0;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      width: 70px;
      padding: 10px 0;
      z-index: 2;
      background-color: #fff;
      box-shadow: 0 2px 12px 0 rgba(0, 0, 0, .1);
      span {
        width: 100%;
        padding: 2px 5px;
        text-align: center;
        cursor: pointer;

        &:hover {
          background-color: #ecf5ff;
        }
      }
    }

    ::v-deep .el-tree-node__children {
      overflow: unset;
    }
  }

  .right-container {
    flex: 1;
    padding-left: 20px;
  }
}
</style>
<style lang="scss" scoped>
@import "@/assets/styles/business.scss";
</style>
