import { geOneMapMenuTree } from "@/api/oneMap/layerMenu";

const flattenTree = (tree) => {
  tree.forEach((node) => {
    // if (!node.layerMenuSubId) {
    //   node.disabled = true
    // }
    if (node.figureId) {
      if (node.child?.length > 0) {
        node.child.map((el) => {
          el.figureId = node.figureId || node.iconId;
          if (el.child?.length > 0) {
            el.child.map((e, index) => {
              e.figureId = node.figureId || node.iconId;
              // e.id = index + '123'
            });
          }
        });
      }
    }
    if (node.subClassify?.id) {
      node.id = node.subClassify.id;
      node.name = node.subClassify.name;
      node.total = node.subClassify.total;
      // node = Object.assign({}, node, node.subClassify)
      // delete node.subClassify;
    }
    if (node.child?.length > 0) {
      flattenTree(node.child); // 递归遍历子节点
    }
  });
};
const state = {
  layerList: [], // 地图图层
  treeW: 0, // 树宽度 350
  isSpread: false, // 是否收起
  menuShowType: 2, // 切换操作 - 1树形 2数据总览
  treeData1: [], // 树形 树数据
  treeData2: [], // 数据总览 树数据
  clickBoolean: false, // 点击树下钻
  legendShow: false, // 是否显示图列数据
  legendList: [], // 图列数据
  mapZoom: 7, // 地图当前层级
  tableShow: false, // 右侧表格显示
  showLevel: 12, // 地图 数据加载层级
  detlCondf: {}, // 路由或是组件配置
  tableHeader: [], // 表格表头数据
  authDeptIds: [], // 数据权限
  layerSetting: [], // 图层设置数据
  recordIndex: 0, // 地图操作记录索引
  recordList: [], // 地图操作记录
  isRecord: true, // 是否记录地图操作
  lineClick: false, // 是否点选
  deptShow: true, // 树显示
  deptW: 273, // 树宽度
  catalogW: 0, // 目录宽
  isLocal: false, // 桩号定位 定位按钮选中状态
  deptId: null, // 当前点击的部门Id
  deptMinZoom: 8.5, // 管理处地图显示最小层级
};
const getters = {
  // 获取树宽度
  getTreeWidth(state) {
    return state.treeW;
  },
};
const mutations = {
  // 设置 左侧树收起和展开
  setTreeWidth(state, { treeWidth }) {
    state.treeW = treeWidth;
    state.isSpread = treeWidth == 0 ? true : false;
  },
  // 设置树展示类型
  setMenuShowType(state, { type }) {
    state.menuShowType = type;
    if (type == 1) {
      state.clickBoolean = false;
    }
  },
  setClickTree(state, { clickStatus }) {
    state.clickBoolean = clickStatus;
  },
  // 修改图列显示隐藏
  setLegendShow(state, { legendShow }) {
    state.legendShow = legendShow;
  },
  // 设置地图图层
  setMapLayer(state, { mapLayer }) {
    state.layerList = mapLayer;
  },
  // 设置图列数据
  setLegend(state, { legendList }) {
    state.legendList = legendList;
  },
  // 设置地图层级
  setMapZoom(state, { zoom }) {
    state.mapZoom = zoom;
  },
  // 控制table列表显示隐藏
  setTableShow(state, { show }) {
    state.tableShow = show;
  },
  // 设置详情配置
  setDetlCondf(state, { config }) {
    state.detlCondf = config;
  },
  // 设置地图显示层级
  setShowLevel(state, { showLevel }) {
    state.showLevel = showLevel;
  },
  // 设置表头
  setTableHeader(state, { header }) {
    state.tableHeader = header;
  },
  // 设置数据权限Id
  setAuthDeptIds(state, { deptIds }) {
    state.authDeptIds = deptIds;
  },
  // 设置图层数据
  setLayer(state, { layer }) {
    let arr = [...state.layerSetting, ...layer];
    let obj = {};
    arr = arr.reduce((total, next) => {
      obj[next.name] ? "" : (obj[next.name] = true && total.push(next));
      return total;
    }, []);
    arr = Array.from(new Set(arr.map((a) => JSON.stringify(a)))).map((item) =>
      JSON.parse(item)
    );
    state.layerSetting = arr;
  },
  // 设置地图操作记录
  setRecordList(state, { record }) {
    state.recordList = [...state.recordList, ...record];
  },
  setRecordBool(state, { bool }) {
    state.isRecord = bool;
  },
  /**
   * 切换状态中的 lineClick 属性
   * @param {Object} state - 状态对象
   * @param {Object} { bool } - 包含布尔值的对象
   * @returns {void} - 该函数不返回任何值
   */
  lineClickBool(state, { bool }) {
    state.lineClick = bool;
  },
  // 设置地图操作记录索引
  setRecordIndex(state, { index }) {
    state.recordIndex = index;
  },
  // 设置管理树 显示隐藏
  setDeptShow(state, { bool }) {
    state.deptShow = bool;
  },
  // 设置管理树 宽度
  setDeptW(state, { deptW }) {
    state.deptW = deptW;
  },
  // 设置目录宽度
  setCatalogW(state, { catalogW }) {
    state.catalogW = catalogW;
  },
  // 设置定位选中状态
  setLoaclBol(state, { bol }) {
    state.isLocal = bol;
  },
  // 设置当前点击的部门Id
  setDeptId(state, { deptId }) {
    state.deptId = deptId;
  },
};
const actions = {
  // 设置 左侧树收起和展开
  setSpread({ commit }, treeWidth) {
    commit("setTreeWidth", { treeWidth });
  },
  // 设置树展示类型
  setShowType({ commit }, type) {
    commit("setMenuShowType", { type });
  },
  changeClickBoolean({ commit }, clickStatus) {
    commit("setClickTree", { clickStatus });
  },
  // 修改图列显示隐藏
  legendShowOrHide({ commit }, legendShow) {
    commit("setLegendShow", { legendShow });
  },
  // 赋值地图图层
  getMapLayer({ commit }, mapLayer) {
    commit("setMapLayer", { mapLayer });
  },
  // 设置图列数据
  getLegend({ commit }, legendList) {
    commit("setLegend", { legendList });
  },
  // 设置地图层级
  getMapZoom({ commit }, zoom) {
    commit("setMapZoom", { zoom });
  },
  // 修改table 显示
  getTableShow({ commit }, show) {
    commit("setTableShow", { show });
  },
  getDetlCondf({ commit }, config) {
    commit("setDetlCondf", { config });
  },
  // 获取设置地图切片显示层级
  getShowLevel({ commit }, showLevel) {
    commit("setShowLevel", { showLevel });
  },
  // 异步设置地图操作记录
  getRecordList({ commit }, record) {
    commit("setRecordList", { record });
  },
  // 异步修改路线是否可点击
  getLineClick({ commit }, bool) {
    commit("lineClickBool", { bool });
  },
  // 获取树 数据 deptIds[] 部门Id、isRefresh 四否刷新
  getTreeList(
    { state },
    { deptIds = [], maintenanceSectionIds = [], isRefresh = true }
  ) {
    if (
      !isRefresh &&
      ((state.menuShowType == 1 && state.treeData1.length) ||
        (state.menuShowType == 2 && state.treeData2.length))
    )
      return;
    return new Promise((resolve, reject) => {
      geOneMapMenuTree({
        menuShowType: state.menuShowType,
        managementMaintenanceIds: deptIds,
        maintenanceSectionIds,
      })
        .then((res) => {
          if (res) {
            if (state.menuShowType == 1) {
              state.treeData1 = res;
              flattenTree(state.treeData1);
            } else {
              state.treeData2 =
                res.map((v) => {
                  v.spread = false;
                  return v;
                }) || [];
            }
            resolve(res);
          } else {
            reject("");
          }
        })
        .catch((err) => {
          reject(err);
        });
    });
  },
};
export default {
  namespaced: true,
  state,
  getters,
  mutations,
  actions,
};
