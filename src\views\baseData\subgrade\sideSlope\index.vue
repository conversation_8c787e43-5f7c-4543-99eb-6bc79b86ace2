<template>
  <PageContainer>
    <template slot="search">
      <div style="display: flex; align-items: center; margin: 0">
        <CascadeSelection
          :form-data="queryParams"
          v-model="queryParams"
          types="201"
          multiple
        />
        <rangeInput
          :clearData="clearData"
          :startPlaceholder="'起点桩号'"
          :endPlaceholder="'终点桩号'"
          @startValue="
            (v) => {
              queryParams.startStake = v;
            }
          "
          @endValue="
            (v) => {
              queryParams.endStake = v;
            }
          "
        />

        <div style="min-width: 220px">
          <el-button
            v-hasPermi="['baseData:sideSlope:selectPage']"
            type="primary"
            icon="el-icon-search"
            @click="handleQuery"
            >搜索</el-button
          >
          <el-button icon="el-icon-refresh" @click="resetQuery">重置</el-button>
          <el-button
            v-show="!showSearch"
            icon="el-icon-arrow-down"
            circle
            @click="showSearch = true"
          />
          <el-button
            v-show="showSearch"
            icon="el-icon-arrow-up"
            style="
              color: #1890ff;
              border-color: #badeff;
              background-color: #e8f4ff;
            "
            circle
            @click="showSearch = false"
          />
        </div>
      </div>
      <div v-if="showSearch" style="margin-top: 5px">
        <el-select
          v-model="queryParams.operationState"
          placeholder="运营状态"
          clearable
          collapse-tags
          style="width: 170px"
        >
          <el-option
            v-for="dict in dict.type.sys_operation_state"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
        <el-select
          style="margin-left: 20px; width: 170px"
          v-model="queryParams.status"
          placeholder="数据状态"
          clearable
        >
          <el-option
            v-for="dict in dict.type.base_data_state"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
        <el-input
          style="margin-left: 20px; width: 170px"
          v-model="queryParams.slopeCode"
          placeholder="边坡编码"
          clearable
        />
        <el-input
          style="margin-left: 20px; width: 170px"
          v-model="queryParams.slopeName"
          placeholder="边坡名称"
          clearable
        />
          <el-select
            style="margin-left: 20px; width: 170px"
            v-model="queryParams.whetherHealthMonitorSystemType"
            placeholder="是否有健康监测系统"
            clearable
          >
            <el-option
              v-for="dict in dict.type.base_data_yes_no"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
      </div>
    </template>

    <template slot="header">
      <div class="button-list">
        <el-button
          v-hasPermi="['baseData:sideSlope:add']"
          type="primary"
          @click="handleAdd"
          >新增</el-button
        >
        <el-button
          v-hasPermi="['baseData:sideSlope:edit']"
          type="primary"
          @click="handleUpdate"
          >编辑</el-button
        >
        <el-button
          v-hasPermi="['baseData:sideSlope:delete']"
          type="primary"
          @click="handleDelete"
          >删除</el-button
        >
        <el-button
          v-hasPermi="['baseData:sideSlope:info']"
          type="primary"
          @click="handleView('')"
          >查看</el-button
        >
        <el-button
          v-hasPermi="['baseData:import:execute']"
          type="primary"
          @click="handleImport"
          >导入新增</el-button
        >
        <el-button
          v-hasPermi="['baseData:import:execute']"
          type="primary"
          @click="importUpdate"
          >导入更新</el-button
        >
        <el-button
          v-hasPermi="['baseData:sideSlope:export']"
          type="primary"
          @click="exportListAll"
          >数据导出</el-button
        >
        <el-button
          v-hasPermi="['baseData:businessStatusRecord:add']"
          type="primary"
          @click="changeStatus"
          >运营状态变更</el-button
        >
        <!-- <el-button
          type="primary"
          @click="downloadQrcode"
        >二维码下载</el-button> -->
      </div>
    </template>

    <template slot="body">
      <el-table
        v-adjust-table
        v-loading="loading"
        ref="table"
        height="100%"
        border
        :data="tableData"
        :row-style="rowStyle"
        @selection-change="handleSelectionChange"
        @row-click="handleRowClick"
      >
        <el-table-column type="selection" width="50" align="center" />
        <el-table-column
          fixed
          label="序号"
          type="index"
          width="50"
          align="center"
        >
          <template v-slot="scope">
            {{
              scope.$index +
              (queryParams.pageNum - 1) * queryParams.pageSize +
              1
            }}
          </template>
        </el-table-column>
        <el-table-column
          fixed
          label="操作"
          align="center"
          width="50"
        >
          <template slot-scope="scope">
            <el-link
              type="primary"
              :disabled="!scope.row.shape"
              @click.stop="handleLocation(scope.row)"
            >定位</el-link>
          </template>
        </el-table-column>
        <el-table-column
          label="边坡编码"
          fixed
          align="center"
          prop="slopeCode"
          min-width="120"
          show-overflow-tooltip
        />
        <el-table-column
          label="边坡名称"
          align="center"
          prop="slopeName"
          min-width="120"
          show-overflow-tooltip
        />

        <el-table-column
          label="管理处"
          align="center"
          prop="managementMaintenanceName"
          min-width="120"
          show-overflow-tooltip
        />
        <el-table-column
          label="管养分处"
          align="center"
          prop="managementMaintenanceBranchName"
          min-width="120"
          show-overflow-tooltip
        />
        <el-table-column
          label="养护路段"
          align="center"
          prop="maintenanceSectionName"
          min-width="120"
          show-overflow-tooltip
        />
        <el-table-column
          label="路线编码"
          align="center"
          prop="routeCode"
          min-width="120"
          show-overflow-tooltip
        />
        <el-table-column
          label="桩号范围"
          align="center"
          min-width="180"
          show-overflow-tooltip
        >
          <template #default="{ row }">
            <span>{{ formatPile(row.startStake) }}</span>
            <span v-if="row.startStake">~</span>
            <span>{{ formatPile(row.endStake) }}</span>
          </template>
        </el-table-column>
        <el-table-column
          align="center"
          prop="operationState"
          min-width="140"
          label="运营状态"
        >
          <template slot-scope="{ row }">
            <el-link
              :underline="false"
              :type="
                { 1: 'info', 2: 'success', 3: 'danger', 4: 'primary' }[
                  row.operationState
                ]
              "
              @click="handleOperational($event, row)"
            >
              <DictTag
                :value="row.operationState"
                :options="dict.type.sys_operation_state"
              />
            </el-link>
          </template>
        </el-table-column>
        <el-table-column label="数据状态" align="center" min-width="140">
          <template #default="{ row }">
            <el-link
              :type="{ 1: 'info', 2: 'success' }[row.status]"
              :underline="false"
            >
              <DictTag
                :value="row.status"
                :options="dict.type.base_data_state"
              />
            </el-link>
          </template>
        </el-table-column>
        <el-table-column
          align="center"
          prop="direction"
          width="140"
          label="方向"
        >
          <template slot-scope="{ row }">
            <DictTag
              :value="row.direction"
              :options="dict.type.sys_route_direction"
            />
          </template>
        </el-table-column>
        <el-table-column
          label="位置"
          align="center"
          prop="lane"
          min-width="140"
        >
          <template slot-scope="{ row }">
            <DictTag :value="row.lane" :options="dict.type.lane" />
          </template>
        </el-table-column>
        <el-table-column
          label="经度"
          align="center"
          prop="longitude"
          min-width="140"
          show-overflow-tooltip
        />
        <el-table-column
          label="纬度"
          align="center"
          prop="latitude"
          min-width="140"
          show-overflow-tooltip
        />

        <el-table-column
          label="施工里程桩号"
          align="center"
          min-width="140"
          show-overflow-tooltip
        >
          <template #default="{ row }">
            <span>{{ formatPile(row.constructionStake) }}</span>
          </template>
        </el-table-column>
        <el-table-column
          label="统一里程桩号"
          align="center"
          min-width="140"
          show-overflow-tooltip
        >
          <template #default="{ row }">
            <span>{{ formatPile(row.unifiedMileageStake) }}</span>
          </template>
        </el-table-column>
        <el-table-column
          label="路基类型"
          align="center"
          prop="roadbedType"
          min-width="140"
          show-overflow-tooltip
        />
        <el-table-column
          label="土质"
          align="center"
          prop="soil"
          min-width="140"
          show-overflow-tooltip
        />
        <el-table-column
          label="检查梯"
          align="center"
          prop="checkLadder"
          min-width="140"
          show-overflow-tooltip
        />
        <el-table-column
          label="坡脚长度(m)"
          align="center"
          prop="slopingFootLength"
          min-width="140"
          show-overflow-tooltip
        />
        <el-table-column
          label="落台宽度(m)"
          align="center"
          prop="dropWidth"
          min-width="140"
          show-overflow-tooltip
        />
        <el-table-column
          label="边坡类型"
          align="center"
          prop="slopeType"
          min-width="120"
          show-overflow-tooltip
        >
          <template slot-scope="scope">
            <DictTag
              :value="scope.row.slopeType"
              :options="dict.type.side_slope_type"
            />
          </template>
        </el-table-column>
        <el-table-column
          label="长度(m)"
          align="center"
          prop="length"
          min-width="120"
          show-overflow-tooltip
        />
        <el-table-column
          label="总高度(m)"
          align="center"
          prop="totalHeight"
          min-width="120"
          show-overflow-tooltip
        />
        <el-table-column
          label="边坡总台数"
          align="center"
          prop="slopePlatformNumber"
          min-width="120"
          show-overflow-tooltip
        />
        <el-table-column
          label="防护形式总数"
          align="center"
          prop="protectionFormNumber"
          min-width="120"
          show-overflow-tooltip
        />
        <el-table-column
          label="备注"
          align="center"
          prop="remark"
          min-width="140"
          show-overflow-tooltip
        />
        <el-table-column label="图片" align="center">
          <template #default="{ row }">
            <el-link
              :underline="false"
              type="primary"
              :disabled="row.samplePictureId ? false : true"
              @click.stop="previewImg(row)"
              >查看</el-link
            >
          </template>
        </el-table-column>
        <el-table-column
          fixed="right"
          label="防护形式"
          align="center"
          min-width="120"
        >
          <template slot-scope="scope">
            <el-button
              style="font-size: 14px; font-weight: 400"
              type="text"
              @click.stop="handleDetail(scope.row)"
              >详情</el-button
            >
          </template>
        </el-table-column>

        <el-table-column
          label="详情"
          fixed="right"
          align="center"
          prop="samplePictureId"
          width="100"
        >
          <template slot-scope="{ row }">
            <el-link
              v-hasPermi="['baseData:sideSlope:info']"
              :underline="false"
              type="primary"
              @click.stop="handleView(row)"
              >查看</el-link
            >
          </template>
        </el-table-column>

      </el-table>
      <pagination
        :total="total"
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
        :pageSizes="[10, 20, 30, 50, 100, 1000]"
        @pagination="getList"
      />
    </template>
    <Form
      v-if="showAddEdit"
      :forView="forView"
      :formData="formData"
      :title="title"
      :showAddEdit="showAddEdit"
      @close="
        () => {
          showAddEdit = false;
          formData = {};
        }
      "
      @refresh="
        () => {
          showAddEdit = false;
          formData = {};
          getList();
        }
      "
    />
    <Protections
      v-if="showDetail"
      :showDetail="showDetail"
      :slopeId="slopeId"
      :formData="formData"
      @close="
        () => {
          showDetail = false;
          formData = {};
        }
      "
    />
    <ImportData
      v-if="showImport"
      :is-update="isUpdate"
      :dialog-visible="showImport"
      :import-base-type="'8'"
      :import-type="importType"
      @close="closeImport"
    />
    <Dialog title="查看图片" width="500px" :show.sync="imgShow">
      <ImagePreview :owner-id="imageUrl" width="100%" height="100%" />
    </Dialog>
    <MapPosition
      v-if="showMapPosition"
      :dialogVisible="showMapPosition"
      :data="mapPositionData"
      @close="showMapPosition = false"
    />
  </PageContainer>
</template>

<script>
import {
  getListPage,
  getSideSlope,
  delSideSlope,
} from "@/api/baseData/subgrade/sideSlope/index.js";
import Form from "./form.vue";
import Protections from "./components/protections.vue";
import Dialog from "@/components/Dialog/index.vue";
import CascadeSelection from "@/components/CascadeSelection/index.vue";
import rangeInput from "@/views/baseData/components/rangeInput/index.vue";
import ImportData from "@/views/baseData/components/importData/index.vue";
import { statusDialog } from "@/views/baseData/components/statusDialog/index.js";
import { statusListDialog } from "@/views/baseData/components/statusDialog/list.js";
import MapPosition from '@/components/mapPosition/index.vue'

export default {
  name: "SideSlope",
  components: {
    Form,
    Protections,
    CascadeSelection,
    rangeInput,
    ImportData,
    MapPosition,
    Dialog
  },
  dicts: ["side_slope_type", "sys_operation_state", "base_data_state",'sys_route_direction','lane', 'base_data_yes_no'],
  data() {
    return {
      loading: true,
      showAddEdit: false,
      forView: false,
      title: "",
      formData: {},
      clearData: false,
      showSearch: false,
      isUpdate:false,
      ids: [],
      total: 0,
      tableData: [],
      queryParams: {
        pageNum: 1,
        pageSize: 20,
        operationState: "2",
      },
      showDetail: false,
      slopeId: "",
      imgShow: false, // 显示图片
      imageUrl: "", // 图片地址
      showImport: false,
      showMapPosition: false,
      mapPositionData: undefined,
      importType: 2,
      selectdTables: [],
    };
  },
  watch: {},
  created() {
    this.getList();
  },
  methods: {
    // 获取表格数据
    getList() {
      this.loading = true;
      getListPage(this.queryParams)
        .then((response) => {
          this.tableData = response.rows;
          this.total = response.total;
          this.loading = false;
          this.clearData = false;
        })
        .catch(() => {
          this.loading = false;
        });
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.selectdTables = selection;
      this.ids = selection.map((item) => item.id);
    },
    // 表格点击勾选
    handleRowClick(row) {
      row.isSelected = !row.isSelected;
      this.$refs.table.toggleRowSelection(row);
    },
    // 勾选高亮
    rowStyle({ row, rowIndex }) {
      if (this.ids.includes(row.id)) {
        return { "background-color": "#b7daff", color: "#333" };
      } else {
        return { "background-color": "#fff", color: "#333" };
      }
    },
     // 查看图片
     previewImg(row) {
      this.imgShow = true;
      this.imageUrl = row.samplePictureId;
    },
    // 搜索按钮
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    // 重置按钮
    resetQuery() {
      this.clearData = true;
      this.queryParams = {
        pageNum: 1,
        pageSize: 20,
        operationState: "2",
      };
      this.handleQuery();
    },
    // 新增按钮操作
    handleAdd() {
      this.forView = false;
      this.showAddEdit = true;
      this.formData = {};
      this.title = "新增边坡数据";
    },
    // 编辑按钮
    handleUpdate() {
      if (this.ids.length != 1) {
        this.$message.warning("请选择一条数据进行编辑！");
        return;
      } else {
        getSideSlope(this.ids[0]).then((res) => {
          if (res.code === 200) {
            this.formData = res.data;
            this.forView = false;
            this.showAddEdit = true;
            this.title = "编辑边坡数据";
          }
        });
      }
    },
    // 删除按钮
    handleDelete() {
      if (this.ids.length == 0) {
        this.$message.warning("请选择至少一条数据进行删除！");
        return;
      }
      this.$modal
        .confirm("确认删除？")
        .then(() => {
          delSideSlope(this.ids).then((res) => {
            if (res && res.code == "200") {
              this.getList();
              this.$modal.msgSuccess("删除成功");
            }
          });
        })
        .catch(() => {});
    },
    // 查看按钮
    handleView(row) {
      if (row) {
        this.ids = [row.id];
      }
      if (this.ids.length != 1) {
        this.$message.warning("请选择一条数据！");
        return;
      } else {
        getSideSlope(this.ids[0]).then((res) => {
          if (res.code === 200) {
            this.formData = res.data;
            this.forView = true;
            this.showAddEdit = true;
            this.title = "查看边坡数据";
          }
        });
      }
    },
    // 导出清单按钮
    exportList() {
      if (this.ids.length === 0) {
        this.$modal
          .confirm("即将导出所有表格数据，此过程可能花费时间较长，是否继续？")
          .then(() => {
            this.download(
              "/baseData/sideslope/basic/export",
              this.queryParams,
              `side_slope_${new Date().getTime()}.xlsx`,
              {
                headers: { "Content-Type": "application/json;" },
                parameterType: "body",
              }
            );
          })
          .catch(() => {});
      } else {
        this.$modal
          .confirm(`已选择${this.ids.length}条边坡数据，确认导出清单？`)
          .then(() => {
            this.download(
              "/baseData/sideslope/basic/export",
              { ids: this.ids },
              `side_slope_${new Date().getTime()}.xlsx`,
              {
                headers: { "Content-Type": "application/json;" },
                parameterType: "body",
              }
            );
          })
          .catch(() => {});
      }
    },
     // 导出清单按钮
    exportListAll() {
      if (this.ids.length === 0) {
        this.$modal
          .confirm("即将导出所有表格数据，此过程可能花费时间较长，是否继续？")
          .then(() => {
            this.download(
              // "/baseData/sideslope/basic/exportList",
              "/baseData/sideslope/basic/export",
              this.queryParams,
              `side_slope_${new Date().getTime()}.xlsx`,
              {
                headers: { "Content-Type": "application/json;" },
                parameterType: "body",
              }
            );
          })
          .catch(() => {});
      } else {
        this.$modal
          .confirm(`已选择${this.ids.length}条边坡数据，确认数据导出？`)
          .then(() => {
            this.download(
              "/baseData/sideslope/basic/export",
              { ids: this.ids },
              `side_slope_${new Date().getTime()}.xlsx`,
              {
                headers: { "Content-Type": "application/json;" },
                parameterType: "body",
              }
            );
          })
          .catch(() => {});
      }
    },
    // 导入按钮
    handleImport() {
      this.isUpdate = false;
      this.showImport = true;
      this.importType = 2;

    },
    // 导入更新
    importUpdate() {
      this.isUpdate = true;
      this.showImport = true;
      this.importType = 1;
    },
    closeImport(v) {
      this.showImport = false;
      if (v) this.getList();
    },
    // 二维码下载
    // downloadQrcode() {
    //   if (this.ids.length === 0) {
    //     this.$modal
    //       .confirm(
    //         '即将下载所有表格的二维码数据，此过程可能花费时间较长，是否继续？'
    //       )
    //       .then(() => {
    //         this.download(
    //           '/baseData/sideslope/basic/genQrCode',
    //           this.queryParams,
    //           `QrCode_${new Date().getTime()}`,
    //           {
    //             headers: { 'Content-Type': 'application/json;' },
    //             parameterType: 'body'
    //           }
    //         )
    //       })
    //       .catch(() => {})
    //   } else {
    //     this.$modal
    //       .confirm(`已选择${this.ids.length}条边坡数据，是否下载二维码？`)
    //       .then(() => {
    //         let data = {
    //           ...this.queryParams,
    //           ids: this.ids
    //         }
    //         this.download(
    //           '/baseData/sideslope/basic/genQrCode',
    //           data,
    //           `QrCode_${new Date().getTime()}`,
    //           {
    //             headers: { 'Content-Type': 'application/json;' },
    //             parameterType: 'body'
    //           }
    //         )
    //       })
    //       .catch(() => {})
    //   }
    // },
    // 运营状态变更按钮
    changeStatus() {
      if (this.ids.length !== 1) {
        this.$message.warning("请选择一条数据！");
        return;
      } else {
        // baseDataType 基础数据类型 8边坡
        let row = this.selectdTables[0];
        statusDialog({ dataId: row.assetId, baseDataType: 8 }).then(() => {
          this.getList();
        });
      }
    },
    // 表格操作-运营状态
    handleOperational(event, row) {
      event.stopPropagation();
      statusListDialog({ dataId: row.assetId, baseDataType: 8 });
    },
    // 防护形式详情
    handleDetail(row) {
      this.showDetail = true;
      this.slopeId = row.id;
      this.formData = row;
    },
      // 表格操作-定位
      handleLocation(row) {
      this.mapPositionData = row
      this.showMapPosition = true
    },
  },
};
</script>

<style lang="scss" scoped>
.first-divider {
  width: 100%;
  height: 1px;
  //border-bottom: 1px solid #dcdfe6;
  //margin: 0 0 5px 0 !important;
}

.button-list {
  border-radius: 4px;
  width: 100%;
  .el-button {
    margin-bottom: 10px;
    margin-right: 10px;
    margin-left: 0;
  }
}
</style>
