<template>
  <div class="app-container maindiv">
    <el-row>
      <el-col :span="24" :xs="24">
        <el-row>
          <el-form
            ref="queryForm"
            :model="queryParams"
            size="mini"
            :inline="true"
            label-width="68px"
          >
            <el-form-item>
              <el-input v-model="queryParams.materialName" placeholder="物资名称" />
            </el-form-item>
            <el-form-item>
              <el-input v-model="queryParams.name" placeholder="申请单名称" />
            </el-form-item>
            <el-form-item label="" prop="applyForDomainId">
              <selectTree
                :key="'domainId'"
                style="width: 100%"
                v-model="queryParams.applyForDomainId"
                :deptType="100"
                :deptTypeList="[1, 3, 4]"
                :dataRule="false"
                onlySelectChild
                placeholder="申请部门"
                clearable
              />
            </el-form-item>

            <el-form-item label="" prop="maiSecId">
              <RoadSection v-model="queryParams.maiSecId" :deptId="queryParams.applyForDomainId" placeholder="路段"
                           style="width: 100%"/>
            </el-form-item>
            <el-form-item>
              <el-button
                type="primary"
                icon="el-icon-search"
                size="mini"
                @click="handleQuery"
              >搜索</el-button
              >
              <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
              >重置</el-button
              >
            </el-form-item>
          </el-form>
        </el-row>
      </el-col>
    </el-row>
    <!--操作按钮区开始-->
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          icon="el-icon-plus"
          v-if='pageType == 1'
          size="mini"
          v-has-menu-permi="['waste:scrap:add']"
          @click="openDetailDialog(null)"
        >新增
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          icon="el-icon-view"
          size="mini"
          @click="visaPreview"
          v-has-menu-permi="['waste:scrap:preview']"
        >预览
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          icon="el-icon-download"
          size="mini"
          @click="exportList"
        >导出
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="primary"
          icon="el-icon-view"
          size="mini"
          @click="handleViewOperateInfo"
        >操作记录
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          icon="el-icon-view"
          size="mini"
          v-has-permi="['settlement:repository:regenerate']"
          @click="handleRegenerate"
        >重新生成报表
        </el-button>
      </el-col>
      <right-toolbar
        :showSearch.sync="showSearch"
        @queryTable="handleQuery"
        :columns="columns"
      ></right-toolbar>
    </el-row>
    <el-row>
      <div class="draggable">
        <el-table v-adjust-table
                  size="mini"
                  style="width: 100%"
                  v-loading="loading"
                  border
                  :data="tableData"
                  row-key="id"
                  ref="dataTable"
                  stripe
                  highlight-current-row
                  @row-click="handleClickRow"
                  :height="showSearch ? 'calc(100vh - 330px)' : 'calc(100vh - 270px)'"
        >
          <el-table-column
            label="序号"
            align="center"
            type="index"
            width="50"
          />
          <template v-for="(column, index) in columns">
            <el-table-column
              :label="column.label"
              v-if="column.visible"
              align="center"
              :prop="column.field"
              :width="column.width"
            >
              <template slot-scope="scope">
                <dict-tag
                  v-if="column.dict"
                  :options="dict.type[column.dict]"
                  :value="scope.row[column.field]"
                />
                <template v-else-if="column.slots">
                  <RenderDom
                    :row="scope.row"
                    :index="index"
                    :render="column.render"
                  />
                </template>
                <span v-else-if="column.isTime">{{
                    parseTime(scope.row[column.field], "{y}-{m}-{d}")
                  }}</span>
                <span v-else>{{ scope.row[column.field] }}</span>
              </template>
            </el-table-column>
          </template>
          <el-table-column
            label="操作"
            fixed="right"
            align="center"
            width="250"
            class-name="small-padding fixed-width"
          >
            <template slot-scope="scope">
              <el-button
                type="text"
                @click="openDetailDialog(scope.row)"
                v-if='pageType == 1'
                icon="el-icon-edit"
                v-has-menu-permi="['waste:scrap:edit']"
              >编辑</el-button
              >
              <el-button
                type="text"
                @click="handleDelete(scope.row)"
                v-if='pageType == 1'
                icon="el-icon-delete"
                v-has-menu-permi="['waste:scrap:remove']"
              >删除</el-button
              >
              <el-button
                type="text"
                @click="handleSubmit(scope.row)"
                v-if='pageType == 1'
                icon="el-icon-check"
                v-has-menu-permi="['waste:scrap:process']"
              >提交</el-button
              >
              <el-button
                type="text"
                @click="openDetailDialog(scope.row, true)"
                v-if='pageType == 2'
                icon="el-icon-check"
                v-has-menu-permi="['waste:scrap:process']"
              >审核</el-button
              >
              <el-button
                type="text"
                @click="openDetailDialog(scope.row, true)"
                v-if='pageType == 3'
                icon="el-icon-view"
              >查看</el-button
              >
            </template>
          </el-table-column>
        </el-table>
        <pagination
          v-show="total > 0"
          :total="total"
          :page.sync="queryParams.pageNum"
          :limit.sync="queryParams.pageSize"
          @pagination="handleQuery"
        />
      </div>
    </el-row>
    <!-- 详情 -->
    <el-dialog
      :title="drawerTitle"
      destroy-on-close
      :visible.sync="drawer"
      :close-on-click-modal="false"
      width="70%"
      v-if="drawer"
    >
      <el-row>
        <el-col :span='12'>
          <el-form
            ref="elForm"
            :model="formData"
            :rules="rules"
            :disabled='readOnly'
            label-width="120px"
            size="medium"
          >
            <el-form-item label="名称" prop="name">
              <el-input
                v-model="formData.name"
                :style="{ width: '100%' }"
                clearable
                placeholder="请输入物资报废申请单名称"
              ></el-input>
            </el-form-item>

            <el-form-item label="申请部门" prop="applyForDomainId">
              <selectTree
                :key="'domainId'"
                style="width: 100%"
                v-model="formData.applyForDomainId"
                :deptType="100"
                :deptTypeList="[1, 3, 4]"
                :dataRule="false"
                onlySelectChild
                placeholder="单位"
                clearable
              />
            </el-form-item>

            <el-form-item label="路段名称" prop="maiSecId">
              <RoadSection v-model="formData.maiSecId" :deptId="formData.applyForDomainId" placeholder="路段"
                           style="width: 100%"/>
            </el-form-item>

            <el-form-item label="申请人" prop="applyForPerson">
              <el-input
                v-model="formData.applyForPerson"
                :style="{ width: '100%' }"
                clearable
                placeholder="请输入申请人"
              ></el-input>
            </el-form-item>

            <el-form-item label="申请时间" prop="applyForTime">
              <el-date-picker
                v-model="formData.applyForTime"
                :style="{ width: '100%' }"
                placeholder="请选择申请时间"
                value-format='yyyy-MM-dd HH:mm:ss'
              ></el-date-picker>
            </el-form-item>

            <el-form-item label="报废原因" prop="scrapReason">
              <el-input
                v-model="formData.scrapReason"
                type="textarea"
                :style="{ width: '100%' }"
                clearable
                placeholder="请输入报废原因"
              ></el-input>
            </el-form-item>
          </el-form>
        </el-col>
        <el-col :span='12'>
          <div class="right-panel">
            <div class="panel-header">
              <span class="panel-title">选择废料物资</span>
              <el-button
                type="primary"
                icon="el-icon-plus"
                v-if='!readOnly'
                circle
                size="mini"
                @click="openMaterialDialog"
              ></el-button>
            </div>

            <el-table
              :data="selectedMaterials"
              border
              style="width: 100%"
              height="400"
            >
              <el-table-column
                prop="name"
                label="物资名称"
              ></el-table-column>
              <el-table-column
                prop="model"
                label="规格型号"
              ></el-table-column>
              <el-table-column
                prop="unit"
                label="单位"
              ></el-table-column>
              <el-table-column
                prop="quantity"
                label="数量"
              ></el-table-column>
              <el-table-column
                label="操作"
                v-if='!readOnly'
                width="80"
              >
                <template slot-scope="scope">
                  <el-button
                    size="mini"
                    type="text"
                    @click="removeMaterial(scope.$index)"
                  >
                    移除
                  </el-button>
                </template>
              </el-table-column>
            </el-table>
          </div>

          <!-- 物资选择弹窗 -->
          <el-dialog
            title="选择废料物资"
            :visible.sync="materialDialogVisible"
            width="70%"
            append-to-body
          >
            <div class="dialog-content">
              <div class="search-bar">
                <el-input
                  v-model="materialQuery.materialName"
                  placeholder="请输入物资名称"
                  style="width: 300px"
                  clearable
                ></el-input>
                <el-button
                  style='margin-left: 10px'
                  type="primary"
                  @click="queryMaterials"
                >查询</el-button>
              </div>

              <el-table
                ref="materialTable"
                :data="materialList"
                border
                height="400"
                @selection-change="handleSelectionChange"
              >
                <el-table-column
                  type="selection"
                ></el-table-column>
                <el-table-column
                  prop="name"
                  label="物资名称"
                ></el-table-column>
                <el-table-column
                  prop="model"
                  label="规格型号"
                ></el-table-column>
                <el-table-column
                  prop="unit"
                  label="单位"
                ></el-table-column>
                <el-table-column
                  prop="quantity"
                  label="数量"
                ></el-table-column>
              </el-table>
              <pagination
                v-show="materialTotal > 0"
                :total="materialTotal"
                :page.sync="materialQuery.pageNum"
                :limit.sync="materialQuery.pageSize"
                @pagination="queryMaterials"
              />
            </div>

            <div slot="footer" class="dialog-footer">
              <el-button @click="materialDialogVisible = false">取消</el-button>
              <el-button type="primary" v-if='!readOnly' @click="confirmSelection">确定</el-button>
            </div>
          </el-dialog>
        </el-col>
      </el-row>
      <div class='mt10' style="text-align: right;">
        <el-button type="primary" v-if='pageType == 1' @click="onSave">保 存</el-button>
        <el-button type="danger" v-if='pageType == 2' @click="onProcess(false)">驳 回</el-button>
        <el-button type="primary" v-if='pageType == 2' @click="onProcess(true)">通 过</el-button>
        <el-button @click="drawer = false">退 出</el-button>
      </div>
    </el-dialog>
    <el-dialog v-if="openOperateInfo" :visible.sync="openOperateInfo" destroy-on-close title="操作记录" width="80%">
      <operateInfo :business-key="rowData.id" :get-node-info="getNodeInfo"></operateInfo>
    </el-dialog>
    <IFramePreview ref="iframeRef" :down-url="preview.url" :file-name="preview.fileName"
                   :srcdoc="preview.html"></IFramePreview>
  </div>
</template>

<script>
import selectTree from "@/components/DeptTmpl/selectTree.vue";
import {
  addScrap,
  deleteScrap, getNodeInfo,
  getScrapById, processScrap, queryPendingScrapList,
  queryScrapListPage, queryScrapViewList,
  updateScrap, visaPreview,
} from '@/api/materialWaste/scrapApplication'
import RoadSection from '@/views/baseData/components/roadSection/index.vue'
import { queryWasteListPage } from '@/api/materialWaste/maintWasteMaterials'
import operateInfo from '@/views/dailyMaintenance/component/operateInfo.vue'
import IFramePreview from '@/components/IFramePreview/index.vue'
import { regenerateReport } from '@/api/dailyMaintenance/metering/addPrice'
export default {
  name: 'ScrapApplication',
  components: {
    IFramePreview,
    operateInfo,
    RoadSection,
    selectTree,
    RenderDom: {
      functional: true,
      props: {
        row: Object,
        index: Number,
        render: Function,
      },
      render(createElement, ctx) {
        const { row, index } = ctx.props;
        return ctx.props.render(row, index);
      },
    },
  },
  props: [],
  dicts: [],
  data() {
    return {
      showSearch: false,
      queryParams: {
        pageNum: 1,
        pageSize: 50,
      },
      pageType: '1',
      total: 0,
      loading: false,
      columns: [
        {
          key: 0,
          field: "name",
          label: `物资报废申请单名称`,
          visible: true,
        },
        {
          key: 1,
          field: "maiSecName",
          label: `路段名称`,
          visible: true,
        },
        {
          key: 2,
          field: "applyForDomainName",
          label: `申请部门`,
          visible: true,
        },
        {
          key: 3,
          field: "applyForPerson",
          label: `申请人`,
          visible: true,
        },
        {
          key: 4,
          field: "applyForTime",
          label: `申请时间`,
          visible: true,
        },
        {
          key: 5,
          field: "scrapReason",
          label: `报废原因`,
          visible: true,
        },
        {
          key: 6,
          field: "statusName",
          label: `状态`,
          visible: true,
        },
      ],
      tableData: [],
      rowData: {},
      drawerTitle: "新增",
      drawer: false,
      formData: {},
      rules: {
        name: [
          { required: true, message: '请输入物资报废申请单名称', trigger: 'blur' }
        ],
        applyForDomainId: [
          { required: true, message: '请选择申请部门', trigger: 'change' }
        ],
        maiSecId: [
          { required: true, message: '请选择路段名称', trigger: 'change' }
        ],
        applyForPerson: [
          { required: true, message: '请输入申请人', trigger: 'blur' },
        ],
        applyForTime: [
          { required: true, message: '请选择申请时间', trigger: 'change' }
        ]
      },
      selectedMaterials: [], // 已选物资列表
      materialDialogVisible: false, // 物资选择弹窗显示状态
      materialList: [], // 物资列表数据
      materialTotal: 0, // 物资总数
      materialQuery: {
        keyword: '',
        pageNum: 1,
        pageSize: 10,
        status: 0
      },
      multipleSelection: [], // 弹窗中选中的物资
      readOnly: false,
      openOperateInfo: false,
      preview: {
        html: '',
        url: '',
        fileName: ''
      },
    };
  },
  computed: {},
  watch: {},
  created() {
    this.pageType = this.$route.query.type
    this.handleQuery();
  },
  mounted() {},
  methods: {
    getNodeInfo,
    handleQuery() {
      this.loading = true;

      if (this.pageType == '1') {
        queryScrapListPage(this.queryParams).then(res => {
          this.loading = false;
          this.tableData = res.rows;
          this.total = res.total;
        })
      }
      if (this.pageType == '2') {
        queryPendingScrapList(this.queryParams).then(res => {
          this.loading = false;
          this.tableData = res.rows;
          this.total = res.total;
        })
      }
      if (this.pageType == '3') {
        queryScrapViewList(this.queryParams).then(res => {
          this.loading = false;
          this.tableData = res.rows;
          this.total = res.total;
        })
      }
    },
    resetQuery() {
      this.queryParams = {
        pageNum: 1,
        pageSize: 50,
      };
      this.handleQuery()
    },
    openDetailDialog(row, readOnly = false) {
      this.readOnly = readOnly
      if (row) {
        this.drawerTitle = "编辑"
        if (readOnly) {
          this.drawerTitle = row.name
        }
        this.loading = true
        getScrapById(row.id).then(res => {
          this.formData = res.data
          this.formData.taskId = row.taskId
          this.selectedMaterials = res.data.wasteList
          this.loading = false
          this.drawer = true;
        })
      } else {
        this.drawerTitle = "新增"
        this.drawer = true;
        this.formData = {}
        this.selectedMaterials = []
      }
    },
    handleDelete(row) {
      this.$modal.confirm("是否删除").then(() => {
        deleteScrap(row.id).then(res => {
          this.$message.success("删除成功");
          this.handleQuery();
        })
      });
    },
    handleClickRow(row) {
      this.rowData = row;
    },
    handleSubmit(row) {
      this.$confirm("是否提交?").then(() => {
        const params = {
          status: row.status,
          businessKey: row.id,
          taskId: row.taskId,
          approved: true
        }
        processScrap(params).then(res => {
          this.$modal.msgSuccess("提交成功");
          this.handleQuery();
        })
      });
    },
    handleViewOperateInfo() {
      if (!this.rowData.id) {
        this.$modal.msgWarning("请选择一条数据")
        return
      }
      this.openOperateInfo = true
    },
    // 导出清单按钮
    exportList() {
      let url = 'manager/scrap/export'
      if  (this.pageType == '2') {
        url = 'manager/scrap/pending/export'
      }
      if (this.pageType == '3') {
        url = 'manager/scrap/view/export'
      }
      this.download(url, {
          ...this.queryParams
        }, `物资报废申请导出_${new Date().getTime()}.xlsx`,
        {
          headers: { 'Content-Type': 'application/json;' },
          parameterType: 'body'
        })
    },
    visaPreview() {
      if (!this.rowData.id) {
        this.$message.error('请选择一条数据')
        return
      }
      this.loading = true
      visaPreview({id: this.rowData.id}).then(res => {
        if (res.code == 200) {
          this.preview.html = res.data.data.html
          this.preview.url = res.data.data.downUrl
          this.preview.fileName = res.data.data.fileName
          this.$refs.iframeRef.visible = true
        }
        this.loading = false
      })
    },
    handleRegenerate() {
      if (!this.rowData.id) {
        this.$message.error('请选择一条数据')
        return
      }
      const params = {
        idList: [this.rowData.id],
        type: 9
      }
      regenerateReport(params).then(res => {
        this.$modal.msgSuccess("操作成功")
      })
    },
    onSave() {
      this.$refs.elForm.validate(valid => {
        if (valid) {
          this.formData.wasteIdList = this.selectedMaterials.map(item => item.id);
          if (this.drawerTitle === '新增') {
            addScrap(this.formData).then(re => {
              this.$modal.msgSuccess('保存成功');
              this.drawer = false;
              this.handleQuery();
            })
          } else {
            updateScrap(this.formData).then(re => {
              this.$modal.msgSuccess('保存成功');
              this.drawer = false;
              this.handleQuery();
            })
          }
        }
      });
    },
    onProcess(approved) {
      const params = {
        status: this.formData.status,
        businessKey: this.formData.id,
        taskId: this.formData.taskId,
        approved: approved
      }
      processScrap(params).then(res => {
        this.$modal.msgSuccess("提交成功");
        this.drawer = false;
        this.handleQuery();
      })
    },
    // 打开物资选择弹窗
    openMaterialDialog() {
      this.materialDialogVisible = true;
      this.queryMaterials();
    },

    // 查询物资列表
    queryMaterials() {
      queryWasteListPage(this.materialQuery).then(res => {
        this.materialList = res.rows || [];
        this.materialTotal = res.total || 0;
      })
    },

    // 处理表格选择变化
    handleSelectionChange(val) {
      this.multipleSelection = val;
    },

    // 确认选择
    confirmSelection() {
      // 过滤掉已经选择的物资
      const newMaterials = this.multipleSelection.filter(item =>
        !this.selectedMaterials.some(selected => selected.id === item.id)
      );

      this.selectedMaterials = [...this.selectedMaterials, ...newMaterials];
      this.materialDialogVisible = false;
      console.log(this.selectedMaterials)
    },

    // 移除物资
    removeMaterial(index) {
      this.selectedMaterials.splice(index, 1);
    },
  },
};
</script>

<style lang="scss" scoped>
::v-deep .dialog-footer {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  width: 100%;
}
.right-panel {
  padding-left: 20px;
  height: 100%;
  box-sizing: border-box;
}

.panel-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 15px;
}

.panel-title {
  font-size: 16px;
  font-weight: bold;
}

.dialog-content {
  padding: 10px;
}

.search-bar {
  margin-bottom: 15px;
}
</style>
<style lang="scss" scoped>
@import "@/assets/styles/business.scss";
</style>
