<template>
  <div style="height: 100%">
    <!-- 定义DOM元素，用于在该DOM元素中显示模型或图纸 -->
    <div class="domId" ref="domId" style="height: 100%"></div>
  </div>
</template>

<script>
// import { getsensorIdbyStructidComponentid } from "@/api/home/<USER>";
// import { getviewtoken, getComponentId, getInfoByCode } from "@/api/home/<USER>";
import { fetchGet } from '../api.js';
import { isBigScreen } from '../../utils/utils.js';
const getInfoByCode = (a) => {
  return fetchGet('https://jkjc.yciccloud.com:8000/xboot/structureNormalDataManage/getByCode', a);
};
const getviewtoken = (a) => {
  return fetchGet('https://jkjc.yciccloud.com:8000/xboot/bridge/bim/getViewToken', a);
}
const getComponentId = (a) => {
  return fetchGet('https://jkjc.yciccloud.com:8000/xboot/sensorManage/getComponentIdBySensorId', a)
}
const getsensorIdbyStructidComponentid = (a) => {
  return fetchGet('https://jkjc.glyhgl.com:22586/bigScreen/structure/getByComponentIdAndStructureNode', a)
}

import {
  BimfaceSDKLoader,
  BimfaceSDKLoaderConfig,
  Glodon,
} from "bimfacesdkloader";

export default {
  name: "Bim",
  props: ["bimToRealTime"],
  data() {
    return {
      isBig: isBigScreen(),
      structureId: "",
      viewToken: "", // 这里替换成自己的
      viewer3D: "",
      app: "",
      drawableContainer: "",
      fileId: "",
      // 需要把曝光设置为0.4的桥梁 由于是甲方要求 这里只能写成静态数据
      structureIdList: [
        "1593261718891532288",
        "1593261714705616896",
        "1593261560137125888",
        "1593262505038319616",
        "1593262510889373696",
        "1593261702848319488",
        "1593261698842759168",
        "1593262445600837632",
        "1593262442039873536",
        "1593261834851454977",
        "1593261817197629440",
        "1593261822377594880",
        "1593262192235515904",
      ],
      // 是否需要拉曝光
      isExposure: false,
    };
  },
  beforeRouteEnter(to, from, next) {
    if (from.name == "home_index") {
      to.meta.refreshBim = true;
    } else {
      to.meta.refreshBim = false;
    }
    next();
  },
  activated() {
    if (this.$route.meta.refreshBim) {
      // 清除缓存
      console.log("BIM清除缓存");
      // 渲染数据
      console.log("BIM渲染数据");
    }
    // 恢复成默认的false
    this.$route.meta.refreshBim = false;
  },
  async mounted() {
    this.structureId = this.$route.query.code;

    if (!this.structureId) {
      console.warn("无code,不执行get请求");
      return;
    }
    // console.log("structureId", this.structureId);
    if (this.structureIdList.includes(this.structureId)) {
      this.isExposure = true;
    } else {
      this.isExposure = false;
    }
    await getInfoByCode({
      code: this.structureId,
    }).then((res) => {
      if (res.code == 200) {
        this.fileId = res.result.fileId;
      }
    });
    // 获取viewtoken
    if (this.fileId) {
      await getviewtoken({
        fileId: this.fileId,
      }).then((res) => {
        if (res.code == 200) {
          console.log(res);
          this.viewToken = res.result;
          console.log(this.viewToken);
        }
      });
      // 初始化显示组件
      let options = new BimfaceSDKLoaderConfig();
      options.viewToken = this.viewToken;
      BimfaceSDKLoader.load(
        options,
        this.successCallback,
        this.failureCallback
      );
      window.$Bus.$on("BimShowLable", (data) => {
        console.log(data, "bim?");
        if (this.drawable) {
          this.drawable.clear();
        }

        if (data) {
          //获取componentId
          var componentId;
          getComponentId({
            sensorId: data,
          }).then((res) => {
            if (res.code == 200) {
              componentId = res.result.componentId;
              // 添加标签
              // this.creatLable(componentId, res.result.sensorInstallCode);
              this.drawLeadLabel(componentId, res.result.sensorInstallCode);
            }
          });
        }
      });
    }
  },
  methods: {
    // 清除自定义标签
    clearLable() {
      console.log("清除构建", this.drawable);
      this.drawable.clear();

      // this.drawableContainer.clear();
    },
    // 绘制引线标签
    drawLeadLabel(componentId, data) {
      let that = this;
      this.drawableConfig =
        new Glodon.Bimface.Plugins.Drawable.DrawableContainerConfig();
      this.drawableConfig.viewer = this.viewer3D;
      this.drawableConfig.maxNum = 10;
      this.drawable = new Glodon.Bimface.Plugins.Drawable.DrawableContainer(
        this.drawableConfig
      );
      this.clearLable();
      this.viewer3D
        .getModel()
        .getComponentProperty(componentId, function (obj) {
          let o = that.viewer3D.getBoundingBoxById(componentId);
          //引线标签的配置类
          let config = new Glodon.Bimface.Plugins.Drawable.LeadLabelConfig();

          // config.height = 50;
          config.width = 160;
          let text = data;

          console.log("data:", data);

          config.text = text;
          //引线标签关联的构件
          config.objectId = componentId;

          //引线标签的世界坐标

          let position = {
            x: (o.min.x + o.max.x) / 2,
            y: (o.min.y + o.max.y) / 2,
            z: (o.min.z + o.max.z) / 2,
          };
          config.worldPosition = position;

          //引线标签是否可拖拽
          config.draggable = false;
          //引线标签的视图
          config.viewer = that.viewer3D;

          let label = new Glodon.Bimface.Plugins.Drawable.LeadLabel(config);

          that.drawable.addItem(label);
        });
    },
    // 添加自定义标签
    creatLable(componentId, data) {
      let that = this;
      let position = new Object();
      that.viewer3D
        .getModel()
        .getComponentProperty(componentId, function (objectdata) {
          console.log(objectdata);
          if (that.viewer3D.getBoundingBoxById(componentId).min)
            position = that.viewer3D.getBoundingBoxById(componentId).min;
          else position = objectdata.boundingBox.min;
          // 初始化DrawableContainer
          let drawableConfig =
            new Glodon.Bimface.Plugins.Drawable.DrawableContainerConfig();
          drawableConfig.viewer = that.viewer3D;
          that.drawableContainer =
            new Glodon.Bimface.Plugins.Drawable.DrawableContainer(
              drawableConfig
            );
          // 创建自定义元素，可以是一个dom element，也可以是个字符串
          let config = new Glodon.Bimface.Plugins.Drawable.CustomItemConfig();
          let content = document.createElement("div");
          content.innerHTML =
            '<div class="leadTips"><div><img src="https://static.bimface.com/attach/24ce9654e88a4218908f46279e5c4b04_line.png" height="35" width="49"/></div><div class="leadText" id="canvasDiv" >' +
            data +
            "</div></div>";
          config.content = content;
          config.viewer = that.viewer3D;
          config.worldPosition = position;
          //生成customItem实例
          let customItem = new Glodon.Bimface.Plugins.Drawable.CustomItem(
            config
          );
          // 添加自定义标签
          that.drawableContainer.addItem(customItem);
        });
    },
    // 加载成功回调函数
    successCallback(viewMetaData) {
      // console.log("viewMetaData", viewMetaData);
      var that = this;
      var dom4Show = this.$refs.domId;
      // 设置WebApplication3D的配置项
      var webAppConfig =
        new Glodon.Bimface.Application.WebApplication3DConfig();
      webAppConfig.domElement = dom4Show;
      webAppConfig.enableLogarithmicDepthBuffer = true;
      // 创建WebApplication3D，用以显示模型

      var app = new Glodon.Bimface.Application.WebApplication3D(webAppConfig);
      app.addView(viewMetaData.viewToken);
      var viewer3D = app.getViewer();

      // 甲方要求 有些桥需要设置默认曝光
      if (this.isExposure) {
        viewer3D.setExposureShift(0.4);
      }

      // 增加加载完成监听事件
      viewer3D.addEventListener(
        Glodon.Bimface.Viewer.Viewer3DEvent.ViewAdded,
        function () {
          that.viewAdded = true;
          //自适应屏幕大小
          window.onresize = function () {
            viewer3D.resize(
              document.documentElement.clientWidth,
              document.documentElement.clientHeight - 40
            );
          };
          // 渲染场景
          viewer3D.addEventListener(
            Glodon.Bimface.Viewer.Viewer3DEvent.MouseClicked,
            function (objectdata) {
              // console.log(objectdata);
              that.componentclick(objectdata.elementId);
            }
          );
          viewer3D.render();
          setTimeout(() => {
            viewer3D.setBackgroundColor(
              new Glodon.Web.Graphics.Color(0, 0, 0, 0)
            );
          }, 1000);
          that.viewer3D = viewer3D;
        }
      );
    },
    // 加载失败回调函数
    failureCallback(error) {
      console.log(123);
    },
    //构件点击事件
    componentclick(elementId) {
      if (!elementId) {
        // alert("请选择传感器")
        return;
      }
      getsensorIdbyStructidComponentid({
        StructureNode: this.structureId,
        ComponentId: elementId,
      }).then((res) => {
        const r = res.data
        if (r.code === 200) {
          if (r.result) {
            this.drawLeadLabel(elementId, r?.result[0]?.sensorInstallCode);
          }
          this.bimToRealTime(r.result);
        } else {
          // this.$message.error(res.message || "获取传感器失败");
        }
      });
    },
  },
  beforeDestroy() {
    window.$Bus.$off("BimShowLable");
  },
};
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style scoped>
.domId {
  width: 100%;
  height: 100%;
}
</style>

<style>
.leadTips {
  display: flex;
  justify-content: flex-start;
  width: 230px;
  transform: translateY(-40px);
}

.leadTips img {
  display: inline-block;
  width: 49px;
  height: 40px;
}

.leadText {
  width: auto;
  display: inline-block;
  background: #4a90e2;
  color: #fff;
  padding: 10px;
}
</style>
<style lang="scss">
@import "@/assets/styles/utils.scss";

.bf-drawable-lead-label {
  color: #000 !important;
  font-size: vwpx(28px) !important;
  width: vwpx(320px) !important;
}
</style>