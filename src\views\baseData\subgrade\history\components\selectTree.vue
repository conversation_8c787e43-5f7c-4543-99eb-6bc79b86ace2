<template>
  <div>
    <el-select
      class="main-select-tree"
      ref="selectTree"
      v-model="value"
      style="width: 100%;"
      :placeholder="placeholder"
      clearable
    >
      <el-option
        v-for="item in formatData(datas)"
        :key="item.value"
        :label="item.label"
        :value="item.value"
        style="display: none;"
      />
      <el-tree
        class="main-select-el-tree"
        ref="selecteltree"
        :data="datas"
        node-key="id"
        highlight-current
        :props="defaultProps"
        @node-click="handleNodeClick"
        :current-node-key="value"
        :expand-on-click-node="expandOnClickNode"
      />
    </el-select>
  </div>
</template>

<script>
import { deptTreeSelect } from '@/api/tmpl'

export default {
  name: 'bridge-baseInfo-selectTree',
  props: {
    value: {
      type: String,
      default: ''
    },
    placeholder: {
      default: '请选择'
    }
  },
  data() {
    return {
      expandOnClickNode: true,
      options: [],
      datas: [],
      defaultProps: {
        children: 'children',
        label: 'label'
      }
    }
  },
  created() {
    this.getDept()
  },
  methods: {
    getDept() {
      var deptType = { deptType: this.deptType }
      deptTreeSelect(deptType).then(response => {
        this.datas = response.data
      })
    },
    // 四级菜单
    formatData(data) {
      let options = []
      data.forEach((item, key) => {
        options.push({ label: item.label, value: item.id })
        if (item.children) {
          item.children.forEach((items, keys) => {
            options.push({ label: items.label, value: items.id })
            if (items.children) {
              items.children.forEach((itemss, keyss) => {
                options.push({ label: itemss.label, value: itemss.id })
                if (itemss.children) {
                  itemss.children.forEach((itemsss, keysss) => {
                    options.push({ label: itemsss.label, value: itemsss.id })
                  })
                }
              })
            }
          })
        }
      })
      return options
    },
    handleNodeClick(node) {
      this.$emit('input', node.id)
      this.$refs.selectTree.blur()
    }
  }
}
</script>
<style>
.main-select-el-tree .el-tree-node .is-current > .el-tree-node__content {
  font-weight: bold;
  color: #409eff;
}
.main-select-el-tree .el-tree-node.is-current > .el-tree-node__content {
  font-weight: bold;
  color: #409eff;
}
</style>
