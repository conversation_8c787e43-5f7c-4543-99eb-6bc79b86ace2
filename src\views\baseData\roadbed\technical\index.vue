<template>
  <PageContainer>
    <template slot="search">
      <div style="display: flex; align-items: center; margin: 0">
        <CascadeSelection
          :form-data="queryParams"
          v-model="queryParams"
          types="201"
          multiple
        />
        <el-select
          style="margin-left: 15px"
          v-model="queryParams.routeLevel"
          placeholder="路线技术等级"
          clearable
          collapse-tags
        >
          <el-option
            v-for="dict in dict.type.sys_route_grade"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>

        <rangeInput
          :clearData="clearData"
          :startPlaceholder="'起点桩号'"
          :endPlaceholder="'终点桩号'"
          @startValue="
            (v) => {
              queryParams.startStake = v;
            }
          "
          @endValue="
            (v) => {
              queryParams.endStake = v;
            }
          "
        />

        <div style="min-width: 240px; margin-left: 15px">
          <el-button
            v-hasPermi="['baseData:pavementTechnicalResults:listPage']"
            type="primary"
            icon="el-icon-search"
            @click="handleQuery"
            >搜索</el-button
          >
          <el-button icon="el-icon-refresh" @click="resetQuery">重置</el-button>
          <el-button
            v-show="!showSearch"
            icon="el-icon-arrow-down"
            circle
            @click="showSearch = true"
          />
          <el-button
            v-show="showSearch"
            icon="el-icon-arrow-up"
            style="
              color: #1890ff;
              border-color: #badeff;
              background-color: #e8f4ff;
            "
            circle
            @click="showSearch = false"
          />
        </div>
      </div>
      <div v-if="showSearch" style="margin-top: 5px">
        <el-date-picker
          style="width: 200px; margin-right: 15px"
          v-model="queryParams.year"
          type="year"
          placeholder="请选择年度"
          clearable
          value-format="yyyy"
          @change="getList"
        />
        <el-select
          style="margin-left: 15px"
          v-model="queryParams.projectId"
          placeholder="项目名称"
          clearable
          collapse-tags
          @change="getList"
        >
          <el-option
            v-for="dict in projectOptions"
            :key="dict.id"
            :label="dict.project"
            :value="dict.id"
          />
        </el-select>
      </div>
    </template>
    <template slot="header">
      <div class="button-list">
        <el-button
          v-hasPermi="['baseData:pavementTechnicalResults:add']"
          type="primary"
          @click="handleAdd"
          >新增</el-button
        >
        <el-button
          v-hasPermi="['baseData:pavementTechnicalResults:edit']"
          type="primary"
          @click="handleUpdate"
          >编辑</el-button
        >
        <el-button
          v-hasPermi="['baseData:pavementTechnicalResults:remove']"
          type="primary"
          @click="handleRemove"
          >删除</el-button
        >
        <el-button
          type="primary"
          @click="importUpdate"
          v-hasPermi="['baseData:import:execute']"
          >导入更新</el-button
        >
        <el-button
          v-hasPermi="['baseData:import:execute']"
          type="primary"
          @click="importAdd"
          >导入新增</el-button
        >

        <el-button
          v-hasPermi="['baseData:pavementTechnicalResults:export']"
          type="primary"
          class="mb8"
          @click="exportList"
          >数据导出</el-button
        >
      </div>
    </template>
    <template slot="body">
      <el-table
        v-adjust-table
        ref="table"
        v-loading="loading"
        height="99%"
        border
        :data="tableData"
        :header-cell-style="{ height: '36px' }"
        :row-style="rowStyle"
        @selection-change="handleSelectionChange"
        @row-click="handleRowClick"
      >
        <el-table-column fixed type="selection" width="50" align="center" />
        <el-table-column label="序号" type="index" width="50" fixed align="center">
          <template v-slot="scope">
            {{
              scope.$index +
              (queryParams.pageNum - 1) * queryParams.pageSize +
              1
            }}
          </template>
        </el-table-column>
        <el-table-column
          label="年度"
          align="center"
          prop="year"
          min-width="120"
          fixed
          show-overflow-tooltip
        />
        <el-table-column
          label="项目名称"
          align="center"
          prop="project"
          min-width="120"
          fixed
          show-overflow-tooltip
        />

        <el-table-column
          label="检测单位"
          align="center"
          prop="detectionUnit"
          min-width="120"
          show-overflow-tooltip
        />
        <el-table-column
          label="检测日期"
          align="center"
          prop="checkDate"
          min-width="120"
          show-overflow-tooltip
        />
        <el-table-column
          label="管理处"
          align="center"
          prop="managementMaintenanceName"
          min-width="120"
          show-overflow-tooltip
        />
        <el-table-column
          label="管养分处"
          align="center"
          prop="managementMaintenanceBranchName"
          min-width="120"
          show-overflow-tooltip
        />
        <el-table-column
          label="路线编码"
          align="center"
          prop="routeCode"
          min-width="120"
          show-overflow-tooltip
        ></el-table-column>
        <el-table-column
          label="路线名称"
          align="center"
          prop="routeName"
          min-width="120"
          show-overflow-tooltip
        ></el-table-column>
        <el-table-column
          label="养护路段"
          align="center"
          prop="maintenanceSectionName"
          min-width="120"
          show-overflow-tooltip
        ></el-table-column>
        <el-table-column
          label="调查方向"
          align="center"
          prop="direction"
          min-width="120"
          show-overflow-tooltip
        >
          <template slot-scope="scope">
            <dict-tag
              :options="dict.type.sys_route_direction"
              :value="scope.row.direction"
            />
          </template>
        </el-table-column>
        <el-table-column
          label="具体方向"
          align="center"
          prop="specificDirection"
          min-width="120"
          show-overflow-tooltip
        ></el-table-column>
        <el-table-column
          label="路线技术等级"
          align="center"
          prop="routeLevel"
          min-width="120"
          show-overflow-tooltip
        >
          <template slot-scope="scope">
            <dict-tag
              :options="dict.type.sys_route_grade"
              :value="scope.row.routeLevel"
            />
          </template>
        </el-table-column>
        <el-table-column
          label="路面类型"
          align="center"
          prop="pavType"
          min-width="120"
          show-overflow-tooltip
        >
          <template slot-scope="scope">
            <dict-tag
              :options="dict.type.sys_surface_type"
              :value="scope.row.pavType"
            />
          </template>
        </el-table-column>
        <el-table-column
          label="起点桩号"
          align="center"
          prop="startStake"
          min-width="120"
          show-overflow-tooltip
        >
          <template slot-scope="scope">
            <span v-if="scope.row && scope.row.startStake">
              {{ formatPile(scope.row.startStake) }}
            </span>
          </template>
        </el-table-column>
        <el-table-column
          label="终点桩号"
          align="center"
          prop="endStake"
          min-width="120"
          show-overflow-tooltip
        >
          <template slot-scope="scope">
            <span v-if="scope.row && scope.row.endStake">
              {{ formatPile(scope.row.endStake) }}
            </span>
          </template>
        </el-table-column>
        <el-table-column
          label="评定长度"
          align="center"
          prop="ratingLength"
          min-width="120"
          show-overflow-tooltip
        ></el-table-column>
        <el-table-column
          label="MQI(公路技术状况指数)"
          align="center"
          prop="mqi"
          min-width="120"
          show-overflow-tooltip
        ></el-table-column>
        <el-table-column
          label="MQI等级"
          align="center"
          prop="mqil"
          min-width="120"
          show-overflow-tooltip
        ></el-table-column>

        
        <el-table-column
          label="SCI(路基技术状况指数)"
          align="center"
          prop="sci"
          min-width="120"
          show-overflow-tooltip
        ></el-table-column>
        <el-table-column
          label="SCI等级"
          align="center"
          prop="scil"
          min-width="120"
          show-overflow-tooltip
        ></el-table-column>
        <el-table-column
          label="BCI(桥隧构造物技术状况指数)"
          align="center"
          prop="bci"
          min-width="120"
          show-overflow-tooltip
        ></el-table-column>
        <el-table-column
          label="BCI等级"
          align="center"
          prop="bcil"
          min-width="120"
          show-overflow-tooltip
        ></el-table-column>
        <el-table-column
          label="TCI(沿线设施技术状况指数)"
          align="center"
          prop="tci"
          min-width="120"
          show-overflow-tooltip
        ></el-table-column>
        
        <el-table-column
          label="TCI等级"
          align="center"
          prop="tcil"
          min-width="120"
          show-overflow-tooltip
        ></el-table-column>
        <el-table-column
          label="PQI(路面技术状况指数)"
          align="center"
          prop="pqi"
          min-width="120"
          show-overflow-tooltip
        ></el-table-column>
        <el-table-column
          label="PQI等级"
          align="center"
          prop="pqil"
          min-width="120"
          show-overflow-tooltip
        ></el-table-column>

        
        <el-table-column
          label="PCI(路面损坏状况指数)"
          align="center"
          prop="pci"
          min-width="120"
          show-overflow-tooltip
        ></el-table-column>
        <el-table-column
          label="PCI等级"
          align="center"
          prop="pcil"
          min-width="120"
          show-overflow-tooltip
        ></el-table-column>
        <el-table-column
          label="RQI(路面行驶质量指数)"
          align="center"
          prop="rqi"
          min-width="120"
          show-overflow-tooltip
        ></el-table-column>

        <el-table-column
          label="RQI等级"
          align="center"
          prop="rqil"
          min-width="120"
          show-overflow-tooltip
        ></el-table-column>
        <el-table-column
          label="RDI(路面车辙深度指数)"
          align="center"
          prop="rdi"
          min-width="120"
          show-overflow-tooltip
        ></el-table-column>
        <el-table-column
          label="RDI等级"
          align="center"
          prop="rdil"
          min-width="120"
          show-overflow-tooltip
        ></el-table-column>
        <el-table-column
          label="PBI(路面跳车指数)"
          align="center"
          prop="pbi"
          min-width="120"
          show-overflow-tooltip
        ></el-table-column>
        <el-table-column
          label="PBI等级"
          align="center"
          prop="pbil"
          min-width="120"
          show-overflow-tooltip
        ></el-table-column>

        <el-table-column
          label="SRI(路面抗滑性能指数)"
          align="center"
          prop="sri"
          min-width="120"
          show-overflow-tooltip
        ></el-table-column>
        <el-table-column
          label="SRI等级"
          align="center"
          prop="sril"
          min-width="120"
          show-overflow-tooltip
        ></el-table-column>
        <el-table-column
          label="PWI(路面磨耗指数)"
          align="center"
          prop="pwi"
          min-width="120"
          show-overflow-tooltip
        ></el-table-column>
        <el-table-column
          label="PWI等级"
          align="center"
          prop="pwil"
          min-width="120"
          show-overflow-tooltip
        ></el-table-column>
        <el-table-column
          label="PSSI(路面结构强度指数)"
          align="center"
          prop="pssi"
          min-width="120"
          show-overflow-tooltip
        ></el-table-column>
        <el-table-column
          label="PSSI等级"
          align="center"
          prop="pssil"
          min-width="120"
          show-overflow-tooltip
        ></el-table-column>
        <el-table-column
          label="操作人姓名"
          align="center"
          prop="oprUserName"
          min-width="120"
          show-overflow-tooltip
        ></el-table-column>
        <el-table-column
          label="操作时间"
          align="center"
          prop="oprDate"
          min-width="120"
          show-overflow-tooltip
        ></el-table-column>
        <el-table-column
          label="描述"
          align="center"
          prop="remark"
          min-width="120"
          show-overflow-tooltip
        ></el-table-column>
      </el-table>
      <pagination
        :total="total"
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
        :pageSizes="[10, 20, 30, 50, 100, 1000]"
        @pagination="getList"
      />
    </template>
    <ImportData
      v-if="showImport"
      :is-update="isUpdate"
      :dialog-visible="showImport"
      :import-base-type="'35'"
      :import-type="importType"
      @close="closeImport"
    />
    <add-and-edit
      v-if="showAddEdit"
      :forView="forView"
      :formData="formData"
      :title="title"
      :showAddEdit="showAddEdit"
      @close="
        () => {
          showAddEdit = false;
          formData = {};
        }
      "
      @refresh="
        () => {
          showAddEdit = false;
          getList();
        }
      "
    />
    <!-- <Detail
      :showDetail="showManageTable"
      :choseId="choseId"
      @close="() => {showManageTable = false;}"
    /> -->
  </PageContainer>
</template>

<script>
import CascadeSelection from "@/components/CascadeSelection/index.vue";
import ImportData from "@/views/baseData/components/importData/index.vue";
import PileInput from "@/components/PileInput/index.vue";
import {
  getListPage,
  deleteByIds,
  getAllProjects,
} from "@/api/baseData/roadbed/technical/index";
import { getFile } from "@/api/file";
import rangeInput from "@/views/baseData/components/rangeInput/index.vue";

import AddAndEdit from "./components/addAndEdit.vue";
// import Detail from './components/detail.vue'
export default {
  name: "Technical",
  props: {},
  components: {
    CascadeSelection,
    ImportData,
    AddAndEdit,
    PileInput,
    rangeInput,
  },
  dicts: [
    "base_data_yes_no",
    "sys_surface_type",
    "sys_route_grade",
    "sys_route_direction",
  ],
  data() {
    return {
      loading: false,
      queryParams: { pageNum: 1, pageSize: 20 },
      total: 0,
      tableData: [],
      clearData: false,
      showImport: false,
      importType: 1,
      isUpdate: false,
      ids: [],
      title: "",
      showAddEdit: false,
      forView: false,
      choseId: "",
      showSearch: false,
      formData: {
        baseInfoForm: {},
      },
      selectItem: [],
      projectOptions: [],
    };
  },
  created() {
    this.getAllProjects()
    this.getList();
  },
  methods: {
    getList() {
      this.loading = true;
      getListPage(this.queryParams)
        .then(async (res) => {
          if (res.code === 200) {
            this.tableData = res.rows;
            this.total = res.total;
            this.clearData = false;
          }
        })
        .finally(() => {
          this.loading = false;
        });
    },
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    // 新增按钮操作
    handleAdd() {
      this.forView = false;
      this.showAddEdit = true;
      this.title = "新增路面技术状况结果";
      this.formData.baseInfoForm = {};
    },
    handleUpdate() {
      if (this.ids.length != 1) {
        this.$message.warning("请选择一条数据进行编辑！");
        return;
      } else {
        this.formData.baseInfoForm = this.selectItem[0];
        this.title = "编辑路面技术状况结果";
        this.showAddEdit = true;
      }
    },
    resetQuery() {
      this.clearData = true;
      this.queryParams = {
        pageNum: 1,
        pageSize: 20,
      };
      this.getList();
    },
    handleRemove() {
      if (this.ids.length === 0) {
        this.$message.warning("请选择至少一条数据进行删除！");
        return;
      }
      this.$modal
        .confirm("确认删除？")
        .then(() => {
          deleteByIds(this.ids).then((res) => {
            if (res.code === 200) {
              this.getList();
              this.$modal.msgSuccess("删除成功");
            }
          });
        })
        .catch(() => {});
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id);
      this.selectItem = selection;
    },
    // 表格点击勾选
    handleRowClick(row) {
      row.isSelected = !row.isSelected;
      this.$refs.table.toggleRowSelection(row);

      this.choseId = row.id;
    },
    // 勾选高亮
    rowStyle({ row, rowIndex }) {
      if (this.ids.includes(row.id)) {
        return { "background-color": "#b7daff", color: "#333" };
      } else {
        return { "background-color": "#fff", color: "#333" };
      }
    },
    closeImport(v) {
      this.showImport = false;
      if (v) this.getList();
    },
    // 导入更新按钮
    importUpdate() {
      this.isUpdate = true;
      this.showImport = true;
      this.importType = 1;
    },
    // 导入新增按钮
    importAdd() {
      this.isUpdate = false;
      this.showImport = true;
      this.importType = 2;
    },
    // 导出清单按钮
    exportList() {
      if (this.tableData.length === 0) return;
      if (this.ids.length === 0) {
        this.$modal
          .confirm("即将导出所有表格数据，此过程可能花费时间较长，是否继续？")
          .then(() => {
            this.download(
              "/baseData/pavement/technical/results/export",
              this.queryParams,
              ``,
              {
                headers: { "Content-Type": "application/json;" },
                parameterType: "body",
              }
            );
          })
          .catch(() => {});
      } else {
        this.$modal
          .confirm(`已选择${this.ids.length}条路面技术状况结果，确认导出数据？`)
          .then(() => {
            this.download(
              "/baseData/pavement/technical/results/export",
              { ids: this.ids, ...this.queryParams },
              ``,
              {
                headers: { "Content-Type": "application/json;" },
                parameterType: "body",
              }
            );
          })
          .catch(() => {});
      }
    },
    getAllProjects() {
      getAllProjects({
        year: this.queryParams?.year || '',
      })
        .then((response) => {
          this.projectOptions = response;
        })
        .catch(() => {
          this.loading = false;
        });
    },
  },
  computed: {},
  watch: {
    "queryParams.year"(newVal, oldVal) {
      this.getAllProjects();

      if (this.queryParams.projectId) {
        this.queryParams.projectId = "";
      }
    },
  },
};
</script>

<style lang="scss" scoped>

::v-deep .el-table .el-table__fixed-body-wrapper{
    top: 46px !important;
}

::v-deep .el-table__fixed-header-wrapper{
    top: 0 !important;
}

.button-list {
  border-radius: 4px;
  width: 100%;
  .el-button {
    margin-bottom: 10px;
    margin-right: 10px;
    margin-left: 0;
  }
}
</style>
