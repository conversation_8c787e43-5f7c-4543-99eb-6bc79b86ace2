<template>
  <el-card style="width: 48%" shadow="hover" :key="uniqueKey">
    <div slot="header" class="header-container">
      <span>{{ parentName }}</span>
      <el-button type="primary" size="mini" @click="openDialog()"
        >添加
      </el-button>
    </div>
    <div
      class="parts-options-list"
      v-for="(item, index) in items"
      :key="item.id"
      @click="handleClick(item, index)"
      :class="{ 'parts-options-current-select': currentSelect.id === item.id }"
      :style="{
        background: currentSelect.id === item.id ? 'rgb(245, 247, 250)' : '',
      }"
    >
      <el-popover placement="left" width="400" trigger="click">
        <template #reference>
          <div>{{ getFieldValue(item, fieldName) }}</div>
        </template>
        <el-descriptions class="margin-top" :column="3" size="small" border>
          <template #title>
            <span v-html="getDescTitle(item)"></span>
          </template>
          <el-descriptions-item
            v-for="field in formStruct"
            :key="field.prop"
            :label="field.label"
          >
            <template v-if="field.prop === 'fileTypeArray'">
              <el-tag v-for="option in item[field.prop]" :key="option">{{
                option
              }}</el-tag>
            </template>
            <template v-else>
              {{ item[field.prop] || "无" }}
            </template>
          </el-descriptions-item>
        </el-descriptions>
      </el-popover>
      <div style="visibility: hidden" @click.stop>
        <span>
          <i class="el-icon-edit" title="修改" @click="openDialog(item)"></i>
          <i class="el-icon-delete" title="删除" @click="onRemove(item)"></i>
        </span>
      </div>
    </div>
    <el-dialog
      :visible.sync="dialogVisible"
      @close="cancel"
      width="600px"
      append-to-body
    >
      <template #title>
        <span v-html="dialogTitle"></span>
      </template>
      <el-form
        :model="formData"
        :rules="formRule"
        ref="form"
        label-position="right"
        label-width="100px"
      >
        <el-form-item
          v-for="(field, index) in formStruct"
          :key="index"
          :label="field.label"
          :prop="field.prop"
          :label-for="`field_${index}`"
        >
          <component
            :is="field.type"
            v-model="formData[field.prop]"
            v-bind="field.attrs"
            :id="`field_${index}`"
            class="full-width"
          >
            <template v-if="field.type === 'el-select'">
              <el-option
                v-for="option in field.attrs.options"
                :key="option.value"
                :label="option.label"
                :value="option.value"
              ></el-option>
            </template>
          </component>
        </el-form-item>
        <!-- <el-form-item label="所属名称">
          <el-input
            v-model="parentName"
            disabled
            class="full-width read-only-input"
          ></el-input>
        </el-form-item> -->
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="cancel">取消</el-button>
        <el-button type="primary" @click="save">保存</el-button>
      </span>
    </el-dialog>
  </el-card>
</template>


<script>
export default {
  name: "CardComponent",
  props: {
    items: {
      type: Array,
      default: () => [],
    },
    parentName: {
      type: String,
      default: "",
    },
    currentSelect: {
      type: Object,
      default: () => {},
    },
    defaultEntity: {
      type: Object,
      default: () => {},
    },
    dialogName: {
      type: Array,
      default: () => [],
    },
    onSave: {
      type: Function,
      default: () => {},
    },
    onDelete: {
      type: Function,
      default: () => {},
    },
    clickHandler: {
      type: Function,
      default: () => {},
    },
    fieldName: {
      type: String,
      default: "moduleName",
    },
    childFieldName: {
      type: String,
      default: "categoryName",
    },
    formStruct: {
      type: Array,
      default: () => [],
    },
    formRule: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      dialogVisible: false,
      formData: {},
      titleField: "",
      focusItem: null,
      uniqueKey: this.generateUniqueKey(), // 初始化唯一键
    };
  },
  methods: {
    generateUniqueKey() {
      return Date.now() + Math.random().toString(36).substr(2, 9);
    },
    cancel() {
      this.dialogVisible = false;
      this.focusItem = null;
      this.$refs.form.resetFields();
    },
    getFieldValue(obj, field) {
      return obj?.[field] ?? ""; // 使用可选链操作符和空值合并操作符
    },

    filterNullValues(obj) {
      return Object.fromEntries(
        Object.entries(obj ?? {}).filter(([_, v]) => v != null)
      );
    },
    openDialog(item = null) {
      this.focusItem = item;
      if (this.$refs.form) {
        this.$refs.form.resetFields();
      }
      if (item) {
        this.formData = this.filterNullValues({ ...item });
      } else {
        this.formData = { ...this.defaultEntity };
      }
      this.dialogVisible = true;
    },
    getDescTitle(item) {
      let title = this.titleField;
      if (item) {
        title = `${title} <i class="el-icon-d-arrow-right"></i> ${this.getFieldValue(
          item,
          this.fieldName
        )}`;
      }
      return title;
    },
    handleClick(item, index) {
      if (this.clickHandler) {
        this.clickHandler(item);
      }
    },
    save() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          this.onSave(this.formData);
          this.uniqueKey = this.generateUniqueKey(); // 更新唯一键
        }
        this.cancel();
      });
    },
    onRemove(item) {
      // 检查是否有下级弹窗
      let subListMessage = "";
      if (item.sysModuleFileSubList && item.sysModuleFileSubList.length > 0) {
        const subNames = item.sysModuleFileSubList.map((subItem) =>
          this.getFieldValue(subItem, this.childFieldName)
        );
        subListMessage =
          "<br>该项目包含以下子项也将被删除:<br>&nbsp;&nbsp;&nbsp;&nbsp;-&nbsp;" +
          subNames.join("<br>&nbsp;&nbsp;&nbsp;&nbsp;-&nbsp;");
      }
      // 弹出确认框，用户点击确认后删除数据
      this.$confirm(
        "此操作将永久删除" +
          this.getFieldValue(item, this.fieldName) +
          subListMessage +
          "<br>是否继续?",
        "提示",
        {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
          dangerouslyUseHTMLString: true, // 允许 HTML 渲染
        }
      )
        .then(() => {
          this.onDelete(item);
          this.uniqueKey = this.generateUniqueKey(); // 更新唯一键
        })
        .catch(() => {
          this.$message.info("已取消删除");
        });
    },
  },
  computed: {
    dialogTitle() {
      let title = (title = `${
        this.titleField
      } <i class="el-icon-d-arrow-right"></i> ${this.getFieldValue(
        this.formData,
        this.fieldName
      )}`);
      if (this.focusItem) {
        return `修改(${title})`;
      } else {
        return `添加(${title})`;
      }
    },
  },
  watch: {
    items: {
      handler(val) {
        // this.$forceUpdate();
      },
      deep: true,
      immediate: true,
    },
    dialogName: {
      handler(val) {
        this.titleField = val
          .map((name) => name)
          .join(' <i class="el-icon-d-arrow-right"></i> ');
      },
      immediate: true,
    },
  },
};
</script>

<style scope>
.parts-options-list {
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: relative;
  user-select: none;
  height: 37.33px;
  padding: 10px;
  margin-bottom: 5px;
  font-size: 14px;
  color: rgb(114, 118, 123);
  border-radius: 4px;
  background: rgb(245, 247, 250);
}

.parts-options-list:hover > div {
  visibility: unset !important;
}

.parts-options-list:hover {
  background: rgb(245, 247, 250);
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  border-radius: 4px;
  font-size: 15px;
}

.parts-options-list > :first-child {
  flex-grow: 1;
}

.parts-options-current-select {
  background: rgb(245, 247, 250) !important;
  color: #1171cd;
  /* color: black;*/
  font-weight: 500;
}

.parts-options-current-select:before {
  content: "";
  display: inline-block;
  position: absolute;
  left: -2px;
  width: 5px;
  height: 1.25rem;
  vertical-align: bottom;
  background: #3797eb;
  border-radius: 2px;
}

.full-width {
  width: 80%;
}
.header-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
</style>