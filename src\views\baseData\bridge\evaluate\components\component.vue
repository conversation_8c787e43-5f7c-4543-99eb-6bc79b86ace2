<template>
  <div>
  <el-dialog
    title="构件评定详情"
    :visible.sync="showComDetail"
    width="85%"
    append-to-body
    :before-close="handleClose"
    :close-on-click-modal="true"
  >
    <!-- <el-button
      class="mb8"
        type="primary"
        @click="importUpdate"
      >导入更新</el-button>
      <el-button
      class="mb8"
        type="primary"
        @click="importAdd"
      >导入新增</el-button> -->
    <el-table
      v-loading="loading"
      height="250px"
      border
      :data="tableData"
      :header-cell-style="{'background':'#F2F3F5','color': '#212529','height': '36px','font-weight': '700','font-size': '14px'}"
      :cell-style="{'height': '36px'}"
    >
      <el-table-column
        label="序号"
        type="index"
        width="50"
        align="center"
      />
      <el-table-column
        label="构件编码"
        align="center"
        prop="number"
        min-width="120"
        show-overflow-tooltip
      />
      <el-table-column
        label="构件得分"
        align="center"
        prop="score"
        min-width="120"
        show-overflow-tooltip
      />
     
    </el-table>
    <pagination
      :total="total"
      :page.sync="pageNum"
      :limit.sync="pageSize"
      @pagination="getList"
    />
    <div slot="footer">
      <el-button @click="handleClose">取 消</el-button>
    </div>
  </el-dialog>
  <ImportData
      v-if="showImportAdd"
      :is-update="isUpdate"
      :dialog-visible="showImportAdd"
      :import-base-type="'28'"
      :import-type="importType"
      @close="closeImportAdd"
    />
  </div>
</template>

<script>
import {
  evaluateComponent,
} from '@/api/baseData/bridge/evaluate/index'
import DictTag from "@/components/DictTag";
import ImportData from '@/views/baseData/components/importData/index.vue'
export default {
  name: 'sideSlope-protections',
  components: { ImportData},
  props: {
    showComDetail: { type: Boolean, default: false },
    componId: { type: undefined, default: '' },
    formData: { type: Object, default: () => {} }
  },
  dicts: [],
  data() {
    return {
      loading: false,
      pageNum: 1,
      pageSize: 20,
      total: 0,
      tableData: [],
      showImportAdd: false,
      importType: 1,
      isUpdate: false,


    }
  },
  created() {
    this.getList()
  },
  methods: {
    getList() {
      if (!this.componId) return
      this.loading = true
      let obj = {
        positionId: this.componId,
        pageNum: this.pageNum,
        pageSize: this.pageSize
      }
      evaluateComponent(obj)
        .then(res => {
          if (res.code === 200) {
            this.tableData = res.rows
            this.total = res.total
          }
          this.loading = false
        })
        .catch(() => {
          this.loading = false
        })
    },
    // 导入更新按钮
    importUpdate() {
      this.isUpdate = true
      this.showImportAdd = true
      this.importType = 1
    },
    // 导入新增按钮
    importAdd() {
      this.isUpdate = false
      this.showImportAdd = true
      this.importType = 2
    },
    closeImportAdd(v) {
      this.showImportAdd = false
      if (v) this.getList()
    },
    handleClose() {
      this.$emit('close')
    }
  },
  watch: {
    // slopeId(newVal, oldVal) {
    //   this.tableData = []
    //   this.getList(newVal)
    // }
  }
}
</script>

<style lang="scss" scoped>
::v-deep .el-form-item {
  margin-bottom: 10px;
  color: #1d2129;
}
</style>